/**
 *
 */
package com.stpl.tech.scm.core.service.impl;

import com.itextpdf.html2pdf.HtmlConverter;
import com.stpl.tech.master.core.PasswordImpl;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.TaxDataCache;
import com.stpl.tech.master.core.external.notification.service.NotificationService;
import com.stpl.tech.master.core.external.notification.service.SMSClientProviderService;
import com.stpl.tech.master.core.notification.sms.SMSType;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.IdIndex;
import com.stpl.tech.master.domain.model.Location;
import com.stpl.tech.master.domain.model.OtpType;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitBusinessType;
import com.stpl.tech.master.domain.model.UnitStatus;
import com.stpl.tech.master.tax.model.TaxData;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.exception.VendorRegistrationException;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.MappingCache;
import com.stpl.tech.scm.core.service.SCMMetadataService;
import com.stpl.tech.scm.core.service.SCMProductManagementService;
import com.stpl.tech.scm.core.service.SkuMappingService;
import com.stpl.tech.scm.core.templates.VendorContractTemplate;
import com.stpl.tech.scm.core.util.MultiPartFileHelper;
import com.stpl.tech.scm.core.util.SCMConstants;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.core.util.model.SkuDataAndTaxData;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.dao.SCMMetadataDao;
import com.stpl.tech.scm.data.dao.SkuMappingDao;
import com.stpl.tech.scm.data.model.BusinessDetailData;
import com.stpl.tech.scm.data.model.DocumentDetailData;
import com.stpl.tech.scm.data.model.InventoryListTypeData;
import com.stpl.tech.scm.data.model.PackagingDefinitionData;
import com.stpl.tech.scm.data.model.PageRequestDetail;
import com.stpl.tech.scm.data.model.ProductionUnitData;
import com.stpl.tech.scm.data.model.PurchaseProfile;
import com.stpl.tech.scm.data.model.SkuPackagingTaxMapping;
import com.stpl.tech.scm.data.model.SkuPriceData;
import com.stpl.tech.scm.data.model.SkuPriceHistory;
import com.stpl.tech.scm.data.model.UnitDistanceMappingData;
import com.stpl.tech.scm.data.model.UnitSkuMapping;
import com.stpl.tech.scm.data.model.UnitSkuVendorMapping;
import com.stpl.tech.scm.data.model.VendorContractInfo;
import com.stpl.tech.scm.data.model.VendorContractItem;
import com.stpl.tech.scm.data.model.VendorSkuMapping;
import com.stpl.tech.scm.data.mongo.AuditChangeLogTypes;
import com.stpl.tech.scm.data.transport.model.VendorOTPValidationDomain;
import com.stpl.tech.scm.domain.model.DocUploadType;
import com.stpl.tech.scm.domain.model.DocumentDetail;
import com.stpl.tech.scm.domain.model.FileType;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.IdCodeNameStatus;
import com.stpl.tech.scm.domain.model.MimeType;
import com.stpl.tech.scm.domain.model.PackagingDefinition;
import com.stpl.tech.scm.domain.model.PackagingPriceData;
import com.stpl.tech.scm.domain.model.PriceStatus;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.SkuData;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import com.stpl.tech.scm.domain.model.SkuPackagingMapping;
import com.stpl.tech.scm.domain.model.SkuPriceDetail;
import com.stpl.tech.scm.domain.model.SkuPriceUpdate;
import com.stpl.tech.scm.domain.model.SkuPriceUpdateDetail;
import com.stpl.tech.scm.domain.model.UnitVendorSkuMapping;
import com.stpl.tech.scm.domain.model.UpdateUnitVendorSkuMapping;
import com.stpl.tech.scm.domain.model.VendorContract;
import com.stpl.tech.scm.domain.model.VendorContractVO;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.scm.domain.model.VendorDispatchLocation;
import com.stpl.tech.scm.domain.model.VendorStatus;
import com.stpl.tech.scm.domain.model.unitSkuMappingDetail;
import com.stpl.tech.scm.notification.email.VendorContractEmailNotification;
import com.stpl.tech.scm.notification.email.VendorContractEmailOTPNotification;
import com.stpl.tech.scm.notification.email.template.VendorContractEmailNotificationTemplate;
import com.stpl.tech.scm.notification.email.template.VendorContractEmailOTPNotificationTemplate;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.TemplateRenderingException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class SkuMappingServiceImpl implements SkuMappingService {

    private static final Logger LOG = LoggerFactory.getLogger(WarehouseStockManagementServiceImpl.class);

    @Autowired
    private SkuMappingDao dao;

    @Autowired
    private SCMCache scmCache;

    @Autowired
    private TaxDataCache taxDataCache;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private MappingCache mappingCache;

    @Autowired
    private SCMMetadataService scmMetadataService;

    @Autowired
    private SCMMetadataDao scmMetadataDao;

    @Autowired
    private SCMProductManagementService scmProductManagementService;
    @Autowired
    private EnvProperties props;
    @Autowired
    private FileArchiveService fileArchiveService;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private SMSClientProviderService providerService;

    @Autowired
    private SCMConstants scmConstants;
    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.scm.data.latest.service.SkuMappingService#updatePrices(com.
     * stpl.tech.scm.domain.model.SkuPriceUpdate)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updatePrices(SkuPriceUpdate data) throws SumoException {

        if(dao.updatePrices(data)) {
            if (data.getDetail().getUpdated() != null && data.getDetail().getUpdated().getDate() != null
                    && AppUtils.isCurrentBusinessDate(data.getDetail().getUpdated().getDate()) == 0) {
                dao.updateSkuPricesFromCurrentDay(data);
                SkuPriceData newPriceData = dao.find(SkuPriceData.class,data.getDetail().getKeyId());
                try{
                    scmMetadataService.saveAuditLog(data.getDetail().getKeyId(), AuditChangeLogTypes.SKU_PRICE.value(), data.getEmployeeId(), ((Object) newPriceData),
                            AuditChangeLogTypes.UPDATE_ENTRY.value());
                }catch (Exception e){
                    LOG.info("Error While saving Audit Log Data In Mongo",e);
                }


            }
        } else {
            return false;
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean addPrice(SkuPriceUpdate data) {
        return dao.addPrice(data);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Collection<VendorDetail> searchVendorsForUnit(int unitId) {
        Map<Integer, VendorDetail> vendorDetailList = new HashMap<>();
        Map<Integer, VendorDetail> vendors = scmCache.getVendorDetails();
        List<UnitSkuVendorMapping> vendorSkuMappings = dao.searchActiveVendorMappingsForUnit(unitId);
        if (!vendorSkuMappings.isEmpty()) {
            //vendorDetailList = new HashMap<>();
            for (UnitSkuVendorMapping unitSkuVendorMapping : vendorSkuMappings) {
                Integer vendorId = unitSkuVendorMapping.getVendorId();
                if (vendors.containsKey(vendorId) && vendorDetailList.get(vendorId) == null) {
                    vendorDetailList.put(vendorId, vendors.get(vendorId).trim());
                }
            }
        }
        return vendorDetailList.values();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Collection<IdCodeName> searchVendorsForUnitTrimmed(int unitId) {
        Map<Integer, IdCodeName> vendorDetailList = new HashMap<>();
        Map<Integer, VendorDetail> vendors = scmCache.getVendorDetails();
        List<UnitSkuVendorMapping> vendorSkuMappings = dao.searchActiveVendorMappingsForUnit(unitId);
        if (!vendorSkuMappings.isEmpty()) {
            //vendorDetailList = new HashMap<>();
            for (UnitSkuVendorMapping unitSkuVendorMapping : vendorSkuMappings) {
                Integer vendorId = unitSkuVendorMapping.getVendorId();
                if (vendors.containsKey(vendorId) && vendorDetailList.get(vendorId) == null) {
                    VendorDetail vd = vendors.get(vendorId);
                    if (VendorStatus.ACTIVE.equals(vd.getStatus())) {
                        vendorDetailList.put(vendorId,
                            SCMUtil.generateIdCodeName(vd.getVendorId(), "", vd.getEntityName()));
                    }
                }
            }
        }
        return vendorDetailList.values();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<IdCodeName> getBusinessTypes() {
        List<BusinessDetailData> businessDetailData = dao.findAll(BusinessDetailData.class);
        List<IdCodeName> businessTypes = new ArrayList<>();
        if (!businessDetailData.isEmpty()) {
            for (BusinessDetailData detailData : businessDetailData) {

                businessTypes.add(SCMUtil.generateIdCodeName(detailData.getBusinessId(), "", detailData.getBusinessName()));
            }
        }
        return businessTypes;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Collection<SkuData> getSkusMappedToVendor(int unitId, int vendorId) {
        Map<Integer, SkuData> skus = new HashMap<>();
        List<UnitSkuMapping> skuMappings = dao.searchSkuMappingsForUnitAndVendor(unitId, vendorId);
        if (!skuMappings.isEmpty()) {
            for (UnitSkuMapping skuMapping : skuMappings) {
                Integer skuId = skuMapping.getSkuId();
                SkuDefinition sku = SCMUtil.clone(scmCache.getSkuDefinition(skuId), SkuDefinition.class);
                List<SkuPackagingMapping> packagings = scmCache.getSkuPackagingMappings(skuId);
                sku.getSkuPackagings().addAll(packagings);
                skus.put(skuId, convert(sku));
            }
        }
        return skus.values();
    }

    private SkuData convert(SkuDefinition sku) {
        SkuData skuData = new SkuData();
        skuData.setId(sku.getSkuId());
        skuData.setName(sku.getSkuName());
        skuData.setHsn(sku.getLinkedProduct().getCode());
        skuData.setUom(sku.getUnitOfMeasure());
        skuData.setProductId(sku.getLinkedProduct().getId());
        skuData.setLoose(sku.isSupportsLooseOrdering());
        List<PackagingPriceData> prices = sku.getSkuPackagings().stream().map(pkg -> {
            PackagingPriceData price = new PackagingPriceData();
            PackagingDefinition pkgDef = scmCache.getPackagingDefinition(pkg.getPackagingId());
            price.setId(pkg.getPackagingId());
            price.setName(pkgDef.getPackagingName());
            price.setCode(pkgDef.getPackagingCode());
            price.setRatio(pkgDef.getConversionRatio());
            price.setUom(pkgDef.getUnitOfMeasure());
            return price;
        }).collect(Collectors.toList());
        skuData.getPackagings().addAll(prices);
        Integer pid = sku.getLinkedProduct().getId();
        ProductDefinition pd = scmCache.getProductDefinition(pid);
        skuData.setCategory(pd.getCategoryDefinition().getId());
        return skuData;
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.scm.data.latest.service.SkuMappingService#searchPrices(int,
     * java.lang.String)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<SkuPriceDetail> searchPricesBySku(int skuId, String deliveryLocation) {
        return dao.searchPricesBySku(skuId, deliveryLocation);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<SkuPriceDetail> searchPricesByVendor(int vendorId, String deliveryLocation) {
        return dao.searchPricesByVendorDeliveryLocation(vendorId, deliveryLocation,true, false);
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.scm.data.latest.service.SkuMappingService#
     * searchSkuMappingsForUnit(int)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<IdCodeNameStatus> searchSkuMappingsForUnit(int unitId) {
        return dao.searchSkuMappingsForUnit(unitId);
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.scm.data.latest.service.SkuMappingService#
     * searchUnitMappingsForSku(int)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<IdCodeNameStatus> searchUnitMappingsForSku(int skuId) {
        return dao.searchUnitMappingsForSku(skuId);
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.scm.data.latest.service.SkuMappingService#
     * updateSkuMappingsForUnit(int, java.lang.String, int, java.util.List)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateSkuMappingsForUnit(int employeeId, String name, int unitId, List<Integer> skuIds) {
        boolean status = dao.updateSkuMappingsForUnit(employeeId, name, unitId, skuIds);
        if (status) {
            scmCache.refreshAvailableProductMapping();
        }
        return status;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateSkuMappingsForUnit(int employeeId, String name, int unitId, List<Integer> skuIds, Map<Integer, String> mappedInventoryWithSkuId, Map<Integer, String> productionUnitMapped,
                                            Map<Integer, String> mappedPackagingIds , Map<Integer,String> mappedTaxCodes,
                                            Map<Integer,Date> mappedVoDiscontinuedFrom, Map<Integer,Date> mappedRoDiscontinuedFrom) {

        boolean status = dao.updateSkuMappingsForUnit(employeeId, name, unitId, skuIds);
        if (mappedInventoryWithSkuId != null) {
            List<UnitSkuMapping> unitSkuMappings = dao.getUnitSkuMappingForUnitId(unitId, skuIds);
            List<InventoryListTypeData> inventoryListTypeData = dao.findAll(InventoryListTypeData.class);
            Map<String, Integer> inventoryListName = getInventoryListIdByName(inventoryListTypeData);
            List<ProductionUnitData>   productionUnitData =dao.findAll(ProductionUnitData.class);
            Map<String,Integer> productionUnitMap=getProductionUnitId(productionUnitData);
            List<PackagingDefinitionData> packagingDefinitionDataList = dao.findAll(PackagingDefinitionData.class);
            Map<String,Integer> packagingDefinitionDataMap = getPackagingListIdByName(packagingDefinitionDataList);
            mappedInventoryWithSkuId.forEach((skuID, inventoryName) -> {
                for (UnitSkuMapping unitSkuMapping : unitSkuMappings) {
                    if ((unitSkuMapping.getSkuId() == skuID) && (inventoryListName.containsKey(inventoryName) && (unitSkuMapping.getInventoryList() != inventoryListName.get(inventoryName)))) {
                        unitSkuMapping.setInventoryList(inventoryListName.get(inventoryName));
                        dao.update(unitSkuMapping, true);
                        break;
                    }
                }
            });
            productionUnitMapped.forEach((skuID, unitName) -> {
                if (unitName != null) {
                    for (UnitSkuMapping unitSkuMapping : unitSkuMappings) {
                        if ((unitSkuMapping.getSkuId() == skuID)) {
                            if (unitName != null) {
                                if(productionUnitMap.containsKey(unitName)) {
                                    unitSkuMapping.setProductionUnit(productionUnitMap.get(unitName));
                                    dao.update(unitSkuMapping, true);
                                    mappingCache.removeProductionLine(unitId,skuID);
                                    break;
                                }
                            }
                        }
                    }
                }
            });
            mappedPackagingIds.forEach((skuID, packagingName) -> {
                if (packagingName != null) {
                    for (UnitSkuMapping unitSkuMapping : unitSkuMappings) {
                        if ((unitSkuMapping.getSkuId() == skuID) && packagingDefinitionDataMap.containsKey(packagingName) &&
                                (Objects.nonNull(packagingDefinitionDataMap.get(packagingName)) &&
                                        !packagingDefinitionDataMap.get(packagingName).equals(unitSkuMapping.getPackagingId()))) {
                            unitSkuMapping.setPackagingId(packagingDefinitionDataMap.get(packagingName));
                            dao.update(unitSkuMapping, true);
                            break;
                        }
                    }
                }
            });
            mappedTaxCodes.forEach((skuID,taxCode) ->{
                if(taxCode !=null){
                    for(UnitSkuMapping unitSkuMapping : unitSkuMappings){
                        if ((unitSkuMapping.getSkuId() == skuID) && !taxCode.equals(unitSkuMapping.getTaxCode()) ) {
                            unitSkuMapping.setTaxCode(taxCode);
                            dao.update(unitSkuMapping, true);
                            break;
                        }
                    }
                }
            });
            mappedVoDiscontinuedFrom.forEach((skuID,voDate) ->{
                for(UnitSkuMapping unitSkuMapping : unitSkuMappings){
                    if ((unitSkuMapping.getSkuId() == skuID)) {
                        if (Objects.nonNull(voDate) && Objects.nonNull(unitSkuMapping.getVoDisContinuedFrom())
                                && AppUtils.getDate(voDate).compareTo(unitSkuMapping.getVoDisContinuedFrom()) == 0) {
                            break;
                        }
                        if (Objects.isNull(voDate) && Objects.isNull(unitSkuMapping.getVoDisContinuedFrom())) {
                            break;
                        }
                        if(Objects.nonNull(voDate)) {
                            unitSkuMapping.setVoDisContinuedFrom(AppUtils.getDate(voDate));
                        } else {
                            unitSkuMapping.setVoDisContinuedFrom(null);
                        }
                        dao.update(unitSkuMapping, true);
                        break;
                    }
                }
            });
            mappedRoDiscontinuedFrom.forEach((skuID,roDate) ->{
                for(UnitSkuMapping unitSkuMapping : unitSkuMappings){
                    if ((unitSkuMapping.getSkuId() == skuID)) {
                        if (Objects.nonNull(roDate) && Objects.nonNull(unitSkuMapping.getRoDisContinuedFrom())
                                && AppUtils.getDate(roDate).compareTo(unitSkuMapping.getRoDisContinuedFrom()) == 0) {
                            break;
                        }
                        if (Objects.isNull(roDate) && Objects.isNull(unitSkuMapping.getRoDisContinuedFrom())) {
                            break;
                        }
                        if(Objects.nonNull(roDate)) {
                            unitSkuMapping.setRoDisContinuedFrom(AppUtils.getDate(roDate));
                        } else {
                            unitSkuMapping.setRoDisContinuedFrom(null);
                        }
                        dao.update(unitSkuMapping, true);
                        break;
                    }
                }
            });
        }
        if (status) {
            scmCache.refreshAvailableProductMapping();
        }
        return status;
    }

    private Map<String, Integer> getInventoryListIdByName(List<InventoryListTypeData> inventoryListTypeDatas) {
        Map<String, Integer> inventoryListName = new HashMap<>();
        for (InventoryListTypeData inventoryListTypeData : inventoryListTypeDatas) {
            inventoryListName.put(inventoryListTypeData.getListName(), inventoryListTypeData.getId());
        }
        return inventoryListName;
    }

    private Map<String, Integer> getPackagingListIdByName(List<PackagingDefinitionData> packagingDefinitionDataList) {
        Map<String, Integer> packagingDefinitionDataMap = new HashMap<>();
        for (PackagingDefinitionData packagingDefinitionData : packagingDefinitionDataList) {
            packagingDefinitionDataMap.put(packagingDefinitionData.getPackagingName(), packagingDefinitionData.getPackagingId());
        }
        return packagingDefinitionDataMap;
    }

    private Map<String,Integer> getProductionUnitId(List<ProductionUnitData> productionUnitData){
        Map<String, Integer> productionUnitMap = new HashMap<>();
        for(ProductionUnitData productionData:productionUnitData){
            productionUnitMap.put(productionData.getProductionUnitName(),productionData.getId());
        }
        return productionUnitMap;
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.scm.data.latest.service.SkuMappingService#
     * updateUnitMappingsForSku(int, java.lang.String, int, java.util.List)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateUnitMappingsForSku(int employeeId, String name, int skuId, List<Integer> unitIds) {
        boolean status = dao.updateUnitMappingsForSku(employeeId, name, skuId, unitIds);
        if (status) {
            scmCache.refreshAvailableProductMapping();
        }
        return status;

    }


    private Boolean updateSiblingSkusInventoryList(Integer skuId , Map<Integer , Integer> unitInventoryListMap ){
        Integer productId = scmCache.getSkuDefinition(skuId).getLinkedProduct().getId();
        List<Integer> skuIds = scmProductManagementService.viewAllSkuByProduct(productId).get(productId).stream().map(sku -> sku.getSkuId()).
                collect(Collectors.toList());
        List<UnitSkuMapping> unitSkuMappings = dao.getActiveUnitSkuMappings(skuIds, new ArrayList<>(unitInventoryListMap.keySet()));
        unitSkuMappings.forEach(unitSkuMapping -> {
            unitSkuMapping.setInventoryList(unitInventoryListMap.get(unitSkuMapping.getUnitId()));
        });
        dao.update(unitSkuMappings,true);
        return true;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateUnitMappingsForSku(int employeeId, String name, int skuId, List<Integer> unitIds, Map<Integer, String> inventoryListWithMappedUnitId,
                                            Map<Integer, String> productionUnitMapped, Map<Integer, String> mappedPackagingIds , Map<Integer,String> mappedTaxCodes,
                                            Map<Integer,Date> mappedVoDiscontinuedFrom, Map<Integer,Date> mappedRoDiscontinuedFrom) {
        boolean status = dao.updateUnitMappingsForSku(employeeId, name, skuId, unitIds);
        if (inventoryListWithMappedUnitId != null) {
            List<UnitSkuMapping> unitSkuMappings = dao.getUnitSkuMappingForSKUS(skuId, unitIds);

            List<InventoryListTypeData> inventoryListTypeData = dao.findAll(InventoryListTypeData.class);
            Map<String, Integer> inventoryListName = getInventoryListIdByName(inventoryListTypeData);

            List<ProductionUnitData>   productionUnitData =dao.findAll(ProductionUnitData.class);
            Map<String,Integer> productionUnitMap=getProductionUnitId(productionUnitData);

            List<PackagingDefinitionData> packagingDefinitionDataList = dao.findAll(PackagingDefinitionData.class);
            Map<String,Integer> packagingDefinitionDataMap = getPackagingListIdByName(packagingDefinitionDataList);

            Map<Integer,Integer> unitInventoryListMap = new HashMap<>();

            inventoryListWithMappedUnitId.forEach((unitID, inventoryName) -> {
                for (UnitSkuMapping unitSkuMapping : unitSkuMappings) {
                    if ((unitSkuMapping.getUnitId() == unitID) && (inventoryListName.containsKey(inventoryName) && (unitSkuMapping.getInventoryList() != inventoryListName.get(inventoryName)))) {
                        unitSkuMapping.setInventoryList(inventoryListName.get(inventoryName));
                        unitInventoryListMap.put(unitID,inventoryListName.get(inventoryName));
                        dao.update(unitSkuMapping, true);
                        break;
                    }
                }
            });
            if(!unitInventoryListMap.isEmpty()){
                updateSiblingSkusInventoryList(skuId,unitInventoryListMap);
            }
            productionUnitMapped.forEach((unitID, unitName) -> {
                if (unitName != null) {
                    for (UnitSkuMapping unitSkuMapping : unitSkuMappings) {
                        if ((unitSkuMapping.getUnitId() == unitID) && productionUnitMap.containsKey(unitName) &&
                                (Objects.nonNull(productionUnitMap.get(unitName)) && !productionUnitMap.get(unitName).equals(unitSkuMapping.getProductionUnit()))) {
                            unitSkuMapping.setProductionUnit(productionUnitMap.get(unitName));
                            dao.update(unitSkuMapping, true);
                            mappingCache.removeProductionLine(unitID,unitSkuMapping.getSkuId());
                            break;
                        }
                    }
                }
            });
            mappedPackagingIds.forEach((unitID, packagingName) -> {
                if (packagingName != null) {
                    for (UnitSkuMapping unitSkuMapping : unitSkuMappings) {
                        if ((unitSkuMapping.getUnitId() == unitID) && packagingDefinitionDataMap.containsKey(packagingName) &&
                                (Objects.nonNull(packagingDefinitionDataMap.get(packagingName)) &&
                                        !packagingDefinitionDataMap.get(packagingName).equals(unitSkuMapping.getPackagingId()))) {
                            unitSkuMapping.setPackagingId(packagingDefinitionDataMap.get(packagingName));
                            dao.update(unitSkuMapping, true);
                            break;
                        }
                    }
                }
            });
            mappedTaxCodes.forEach((unitId,taxCode) ->{
                if(taxCode !=null){
                    for(UnitSkuMapping unitSkuMapping : unitSkuMappings){
                        if ((unitSkuMapping.getUnitId() == unitId && !taxCode.equals(unitSkuMapping.getTaxCode()))) {
                            unitSkuMapping.setTaxCode(taxCode);
                            dao.update(unitSkuMapping, true);
                            break;
                        }
                    }
                }
            });
            mappedVoDiscontinuedFrom.forEach((unitId,voDate) ->{
                for(UnitSkuMapping unitSkuMapping : unitSkuMappings){
                    if ((unitSkuMapping.getUnitId() == unitId)) {
                        if (Objects.isNull(voDate) && Objects.isNull(unitSkuMapping.getVoDisContinuedFrom())) {
                            break;
                        }
                        if (Objects.nonNull(voDate) && Objects.nonNull(unitSkuMapping.getVoDisContinuedFrom())
                                && AppUtils.getDate(voDate).compareTo(unitSkuMapping.getVoDisContinuedFrom()) == 0) {
                            break;
                        }
                        if (Objects.nonNull(voDate)) {
                            unitSkuMapping.setVoDisContinuedFrom(AppUtils.getDate(voDate));
                        } else {
                            unitSkuMapping.setVoDisContinuedFrom(null);
                        }
                        dao.update(unitSkuMapping, true);
                        break;
                    }
                }
            });
            mappedRoDiscontinuedFrom.forEach((unitId,roDate) ->{
                for(UnitSkuMapping unitSkuMapping : unitSkuMappings){
                    if ((unitSkuMapping.getUnitId() == unitId)) {
                        if (Objects.isNull(roDate) && Objects.isNull(unitSkuMapping.getRoDisContinuedFrom())) {
                            break;
                        }
                        if (Objects.nonNull(roDate) && Objects.nonNull(unitSkuMapping.getRoDisContinuedFrom())
                                && AppUtils.getDate(roDate).compareTo(unitSkuMapping.getRoDisContinuedFrom()) == 0) {
                            break;
                        }
                        if(Objects.nonNull(roDate)) {
                            unitSkuMapping.setRoDisContinuedFrom(AppUtils.getDate(roDate));
                        } else {
                            unitSkuMapping.setRoDisContinuedFrom(null);
                        }
                        dao.update(unitSkuMapping, true);
                        break;
                    }
                }
            });
        }
        if (status) {
            scmCache.refreshAvailableProductMapping();
        }
        return status;

    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.scm.data.latest.service.SkuMappingService#
     * searchSkuMappingsForVendor(int)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<IdCodeNameStatus> searchSkuMappingsForVendor(int vendorId) {
        return dao.searchSkuMappingsForVendor(vendorId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<IdCodeNameStatus> searchVendorMappingsForSku(int skuId) {
        return dao.searchVendorMappingsForSku(skuId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<IdCodeNameStatus> searchVendorMappingsForBusiness(int businessId) {
        return dao.searchVendorMappingsForBusiness(businessId);
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.scm.data.latest.service.SkuMappingService#
     * updateSkuMappingsForVendor(int, java.lang.String, int, java.util.List)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateVendorSkuMapping(int employeeId, String name, int vendorId, int skuId, String status) {
        return dao.updateVendorSkuMapping(employeeId, name, vendorId, skuId, status);
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.scm.data.latest.service.SkuMappingService#allActiveUnits()
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<IdCodeNameStatus> allUnits() {
        return dao.allActiveUnits();
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.scm.data.latest.service.SkuMappingService#allActiveSKU()
     */
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    @Override
    public List<IdCodeNameStatus> allSKU() {
        return dao.allActiveSKU();
    }

//    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true)
//    @Override
//    public List<unitSkuMapping>

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.scm.data.latest.service.SkuMappingService#allActiveVendors( )
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<IdCodeNameStatus> allVendors() {
        return dao.allActiveVendors();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateSkuProfiles(Map<Integer, String> skuListWithInventoryListId, int unitId, String profiles) {
        List<InventoryListTypeData> inventoryListTypeData = dao.findAll(InventoryListTypeData.class);
        Map<String, Integer> inventoryListName = getInventoryListIdByName(inventoryListTypeData);
        return dao.updateSkuProfiles(skuListWithInventoryListId, unitId, profiles, inventoryListName);
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.scm.core.service.SkuMappingService#addSkuMappingsForVendor(
     * int, java.lang.String, int, java.util.List)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean addSkuMappingsForVendor(int employeeId, String employeeName, int vendorId, List<IdCodeName> skuIds) {
        return dao.addSkuMappingsForVendor(employeeId, employeeName, vendorId, skuIds);
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean addSkuMappingsForVendorAlias(IdCodeName request) throws SumoException {
        int vendorId = Integer.parseInt(request.getName());
        int vendorSkuMappingId = dao.getVendorSkuMappingId(request.getId(), vendorId);
        VendorSkuMapping vendorSkuMapping = dao.find(VendorSkuMapping.class, vendorSkuMappingId);
        if (vendorSkuMapping != null) {
            vendorSkuMapping.setAlias(request.getCode());
            vendorSkuMapping = dao.update(vendorSkuMapping, true);
            if (vendorSkuMapping == null) {
                throw new SumoException("Error setting sku alias");
            }
        }
        return true;
    }

    /*
     * (non-Javadoc)
     *
     * @see
     * com.stpl.tech.scm.core.service.SkuMappingService#addVendorMappingsForSku(
     * int, java.lang.String, int, java.util.List)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean addVendorMappingsForSku(int employeeId, String employeeName, int skuId, List<Integer> vendorIds) {
        return dao.addVendorMappingsForSku(employeeId, employeeName, skuId, vendorIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean addVendorMappingsForBusiness(int employeeId, String employeeName, int businessId, List<Integer> vendorIds) {
        return dao.addVendorMappingsForBusiness(employeeId, employeeName, businessId, vendorIds);
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.scm.core.service.SkuMappingService#cancelPriceUpdate(com.
     * stpl.tech.scm.domain.model.SkuPriceUpdate)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean cancelPriceUpdate(SkuPriceUpdate data) {
        return dao.cancelPriceUpdate(data);
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.scm.core.service.SkuMappingService#updatePriceStatus(com.
     * stpl.tech.scm.domain.model.SkuPriceUpdate)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updatePriceStatus(SkuPriceUpdate data) {
        return dao.updatePriceStatus(data);
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.scm.core.service.SkuMappingService#
     * searchSkuMappingsForVendorAndUnit(int, int)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<UnitVendorSkuMapping> searchSkuMappingsForVendorAndUnit(int unitId, int vendorId) {
        return dao.searchSkuMappingsForVendorAndUnit(unitId, vendorId);
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.scm.core.service.SkuMappingService#
     * updateSkuMappingsForVendorAndUnit(com.stpl.tech.scm.domain.model.
     * UpdateUnitVendorSkuMapping)
     */
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<UnitVendorSkuMapping> updateSkuMappingsForVendorAndUnit(UpdateUnitVendorSkuMapping data) {
        return dao.updateSkuMappingsForVendorAndUnit(data);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<unitSkuMappingDetail> getSkusProfileForUnit(int unit, List<Integer> sku) {
        return dao.getSkusProfileForUnit(unit, sku);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<SkuDataAndTaxData> getSkuAndTaxData(int vendorDispatchId, int unitId, int vendorId , Boolean toPickFromUnitMapping) {
        List<SkuDataAndTaxData> skuDataAndTaxDataList = new ArrayList<>();
        VendorDetail vendor = scmCache.getVendorDetail(vendorId);
        UnitBasicDetail unitDetail = masterDataCache.getUnitBasicDetail(unitId);
        if (vendor != null && unitDetail != null) {
            Optional<String> deliveryLocation = Optional.ofNullable(unitDetail.getLocationCode());
            Optional<VendorDispatchLocation> dispatchLocation = vendor.getDispatchLocations().stream()
                .filter(vendorDispatchLocation -> vendorDispatchLocation.getDispatchId().equals(vendorDispatchId))
                .findFirst();
            if (dispatchLocation.isPresent() && deliveryLocation.isPresent()) {
                VendorDispatchLocation location = dispatchLocation.get();
                String locationCode = location.getLocationId();
                Location locationDetail = masterDataCache.getAllLocations().get(locationCode);
                int stateId = locationDetail.getState().getId();
                List<SkuPriceDetail> skuPriceList = dao.searchSkuPricesForVendorAndUnit(unitId, vendorId,
                    locationDetail.getCode(), deliveryLocation.get());
                List<Integer> skuIds = skuPriceList.stream().map(skuPriceDetail -> skuPriceDetail.getSku().getId()).collect(Collectors.toList());
                Map<Integer,Map<Integer,String>> skuPackagingTaxMap = new HashMap<>();
                    if(toPickFromUnitMapping.equals(Boolean.TRUE) && Objects.nonNull(skuIds) && !skuIds.isEmpty()){
                        dao.findAllUnitSkuPackagingTaxMappingByUnit(skuIds,unitId).forEach(mapping ->{
                            Integer skuId = mapping.getSkuId();
                            Integer packagingId = mapping.getPackagingId();
                            if(!skuPackagingTaxMap.containsKey(skuId)){
                                skuPackagingTaxMap.put(skuId,new HashMap<>());
                            }
                            if(!skuPackagingTaxMap.get(skuId).containsKey(packagingId)){
                                skuPackagingTaxMap.get(skuId).put(packagingId,mapping.getTaxCode());
                            }

                        });
                    }
                if (skuPriceList != null && !skuPriceList.isEmpty()) {
                    Map<Integer, String> skuAliasMap = new HashMap<>();
                    skuPriceList.forEach(skuPriceDetail -> {
                        if (!skuAliasMap.containsKey(skuPriceDetail.getSku().getId()) && skuPriceDetail.getSku().getCode() != null) {
                            skuAliasMap.put(skuPriceDetail.getSku().getId(), skuPriceDetail.getSku().getCode());
                        }
                    });
                    Map<Integer, List<PackagingPriceData>> skuPkgPrices = new HashMap<>();
                    skuPriceList.forEach(skuPriceDetail -> {
                        Integer skuId = skuPriceDetail.getSku().getId();
                        List<PackagingPriceData> priceList = skuPkgPrices.get(skuId);
                        if (priceList == null) {
                            priceList = new ArrayList<>();
                        }
                        priceList.add(convert(skuPriceDetail));
                        skuPkgPrices.put(skuId, priceList);
                    });
                    List<SkuData> skuDataList = new ArrayList<>();
                    skuPkgPrices.keySet().forEach(skuId -> {
                        SkuDefinition sku = scmCache.getActiveSkuDefinition(skuId);
                        if (sku != null) {
                            SkuData skuData = convertToSkuData(sku, skuPkgPrices.get(skuId) ,toPickFromUnitMapping , unitId);
                            if (skuAliasMap.containsKey(skuData.getId())) {
                                skuData.setAlias(skuAliasMap.get(skuData.getId()));
                            }
                            skuDataList.add(skuData);
                        }
                    });
                    skuDataList.forEach(skuData -> {
                        if (skuData.getHsn() == null) {
                            return;
                        }
                        TaxData taxData = taxDataCache.getTaxData(stateId, skuData.getHsn());
                        Map<Integer,TaxData> packagingTaxMap = new HashMap<>();
                        if(toPickFromUnitMapping.equals(Boolean.TRUE)) {
                            Map<Integer,String> tempMap = skuPackagingTaxMap.getOrDefault(skuData.getId(),new HashMap<>());
                            tempMap.keySet().forEach(packagingId ->{
                                packagingTaxMap.put(packagingId, taxDataCache.getTaxData(stateId, tempMap.get(packagingId)));
                            });
                        }
                        skuDataAndTaxDataList.add(new SkuDataAndTaxData(taxData, skuData,packagingTaxMap));
                    });
                }
            }
        }
        List<Integer> skuIds = skuDataAndTaxDataList.stream().mapToInt(SkuDataAndTaxData::getId).boxed().collect(Collectors.toList());
        List<UnitSkuMapping> unitSkuMappings = dao.getActiveUnitSkuMappings(skuIds, Collections.singletonList(unitId));
        Map<Integer, Date> voDates = getVoDatesMap(unitSkuMappings);
        LOG.info("Before checking the VO discontinued Date SKU List is : {}",skuDataAndTaxDataList.size());
        return getFinalSkuData(skuDataAndTaxDataList, voDates);
    }

    private List<SkuDataAndTaxData> getFinalSkuData(List<SkuDataAndTaxData> skuDataAndTaxDataList, Map<Integer,Date> voDates) {
        List<SkuDataAndTaxData> result = new ArrayList<>();
        skuDataAndTaxDataList.forEach(skuDataAndTaxData -> {
            if (verifyDiscontinuedSku(skuDataAndTaxData, voDates)) {
                result.add(skuDataAndTaxData);
            }
        });
        LOG.info("After checking the VO discontinued Date SKU List is : {}",result.size());
        return result;
    }

    private Map<Integer,Date> getVoDatesMap(List<UnitSkuMapping> unitSkuMappings) {
        Map<Integer, Date> result = new HashMap<>();
        unitSkuMappings.forEach(unitSkuMapping -> {
            if (Objects.nonNull(unitSkuMapping.getVoDisContinuedFrom())) {
                if (!result.containsKey(unitSkuMapping.getSkuId())) {
                    result.put(unitSkuMapping.getSkuId(), unitSkuMapping.getVoDisContinuedFrom());
                } else {
                    Date alreadyStored = result.get(unitSkuMapping.getSkuId());
                    if (alreadyStored.compareTo(unitSkuMapping.getVoDisContinuedFrom()) < 0) {
                        result.put(unitSkuMapping.getSkuId(), unitSkuMapping.getVoDisContinuedFrom());
                    }
                }
            }
        });
        return result;
    }

    private Boolean verifyDiscontinuedSku(SkuDataAndTaxData skuDataAndTaxData, Map<Integer, Date> voDates) {
        try {
            SkuDefinition skuDefinition = scmCache.getSkuDefinition(skuDataAndTaxData.getSkuData().getId());
            Date voDiscontinuedDate = skuDefinition.getVoDisContinuedFrom();
            if (Objects.nonNull(voDiscontinuedDate)) {
                return AppUtils.getCurrentTimestamp().compareTo(voDiscontinuedDate) < 0;
            } else {
                LOG.info("Trying to get the unit sku mapping :: for sku :: {}",skuDataAndTaxData.getSkuData().getName());
                voDiscontinuedDate = voDates.get(skuDataAndTaxData.getSkuData().getId());
                if (Objects.nonNull(voDiscontinuedDate)) {
                    return AppUtils.getCurrentTimestamp().compareTo(voDiscontinuedDate) < 0;
                }
                return true;
            }
        } catch (Exception e) {
            LOG.error("Exception Occurred While verifying Discontinued SKU ::: ",e);
        }
        return false;
    }

    @Override
    public String getHsnCodeFromUnitSkuMapping(SkuDefinition sku , Integer unitId){
        UnitSkuMapping skuMapping = dao.findSkuMappingBySkuAndUnit(sku.getSkuId(), unitId);
        if(Objects.nonNull(skuMapping) && Objects.nonNull(skuMapping.getTaxCode())){
            return skuMapping.getTaxCode();
        }else{
            return sku.getLinkedProduct().getCode();
        }
    }

    private SkuData convertToSkuData(SkuDefinition skuDefinition, List<PackagingPriceData> prices , Boolean toPickFromUnitMapping , Integer unitId) {
        SkuData data = new SkuData();
        data.setName(skuDefinition.getSkuName());
        data.setUom(skuDefinition.getUnitOfMeasure());
        data.setLoose(skuDefinition.isSupportsLooseOrdering());
        if(toPickFromUnitMapping.equals(Boolean.TRUE) && Objects.nonNull(unitId)){
            data.setHsn(getHsnCodeFromUnitSkuMapping(skuDefinition,unitId));
        }else{
            data.setHsn(skuDefinition.getLinkedProduct().getCode());
        }
        data.setId(skuDefinition.getSkuId());
        data.getPackagings().clear();
        data.getPackagings().addAll(prices);
        Integer pid = skuDefinition.getLinkedProduct().getId();
        ProductDefinition pd = scmCache.getProductDefinition(pid);
        data.setSubCategory(pd.getSubCategoryDefinition().getId());
        data.setProductId(pid);
        data.setCategory(pd.getCategoryDefinition().getId());
        return data;
    }

    private PackagingPriceData convert(SkuPriceDetail skuPriceDetail) {
        PackagingPriceData data = new PackagingPriceData();
        data.setId(skuPriceDetail.getPkg().getId());
        data.setCode(skuPriceDetail.getPkg().getCode());
        data.setName(skuPriceDetail.getPkg().getName());
        data.setPrice(skuPriceDetail.getCurrent().getValue()); // pkg price
        data.setRatio(skuPriceDetail.getPkg().getRatio());
        data.setUom(skuPriceDetail.getPkg().getUom());
        data.setLeadTime(skuPriceDetail.getLeadTime());
        return data;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<SkuPriceDetail> getSkuPackagingPriceForVendorsForUnit(int unitId, Set<Integer> vendorIds,
                                                                      String deliveryLocationCode) {
        return dao.getSkuPackagingPriceForVendorsForUnit(unitId, vendorIds, deliveryLocationCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<SkuPriceDetail> getSkuPackagingPriceForUnit(int unitId, String deliveryLocationCode) {
        return dao.getSkuPackagingPriceForUnit(unitId, deliveryLocationCode);
    }

    @Override
    public List<IdCodeNameStatus> unitsByBusinessType(final UnitBusinessType businessType) {
        List<IdCodeNameStatus> units = masterDataCache.getUnits().values().stream()
            .filter(unit -> unit.getUnitBusinessType().equals(businessType))
            .filter(unit -> unit.getStatus().equals(UnitStatus.ACTIVE))
            .map(unit -> {
                IdCodeNameStatus o = new IdCodeNameStatus();
                o.setId(unit.getId());
                o.setName(unit.getName() + "(" + unit.getCompany().getName() + ")");
                o.setStatus(unit.getStatus().name());
                return o;
            })
            .collect(Collectors.toList());
        return units;
    }

    @Override
    public List<Integer> getSubCategories(List<Integer> profiles) {
        List<PurchaseProfile> mappings = dao.getPurchaseMappings(profiles);
        if (mappings != null && !mappings.isEmpty()) {
            return mappings.stream()
                .mapToInt(PurchaseProfile::getSubCategory)
                .boxed().collect(Collectors.toList());
        }
        return null;
    }

    @Override
    public List<String> getUnitDistanceMapping(int firstUnitId, int secondUnitId) {
        List<String> distance = dao.getDistanceOfUnits(firstUnitId, secondUnitId);
        return distance;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateMappingsForUnit(int firstUnitId, Integer firstMappingId, BigDecimal firstDistance, int secondUnitId,
                                         Integer secondMappingId, BigDecimal secondDistance) throws SumoException {
        return dao.updateUnitDistanceMappingData(firstUnitId, firstMappingId, firstDistance, secondUnitId, secondMappingId, secondDistance,true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateSkuLeadTime(int vendorId,int leadTime) {
        if(leadTime!=0) {
            dao.updateSkuLeadTime(vendorId,leadTime);
            return true;
        } else{
            return  false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateLeadTime(IdIndex data) {
        SkuPriceData skuPriceData = dao.find(SkuPriceData.class,data.getId());
        if(skuPriceData!=null) {
            skuPriceData.setLeadTime(data.getIndex());
            dao.update(skuPriceData, false);
            return true;
        } else{
            return  false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<Integer,Integer> getSibLingSkusInventoryListId(List<Integer> unitIds , Integer skuId){
        Map<Integer,Integer> skuUnitInventoryListMap = new HashMap<>();
        Integer productId = scmCache.getSkuDefinition(skuId).getLinkedProduct().getId();
        List<Integer> siblingSkuIds = scmProductManagementService.viewAllSkuByProduct(productId).get(productId).stream().map(sku -> sku.getSkuId()).
                collect(Collectors.toList());
        List<Integer> skipUnitIds = new ArrayList<>();
        List<UnitSkuMapping> unitSkuMappings = dao.getActiveUnitSkuMappings(siblingSkuIds, unitIds);
        unitSkuMappings.forEach(unitSkuMapping -> {
            if(skipUnitIds.contains(unitSkuMapping.getUnitId())){
                //do nothing
            }else{
                if(!skuUnitInventoryListMap.containsKey(unitSkuMapping.getUnitId())){
                    skuUnitInventoryListMap.put(unitSkuMapping.getUnitId(),unitSkuMapping.getInventoryList());
                }else{
                    if(!skuUnitInventoryListMap.get(unitSkuMapping.getUnitId()).equals(unitSkuMapping.getInventoryList())){
                        skuUnitInventoryListMap.remove(unitSkuMapping.getUnitId());
                        skipUnitIds.add(unitSkuMapping.getUnitId());
                    }
                }
            }


        });
        return skuUnitInventoryListMap;


    }

    @Override
    public Map<Integer, SkuPackagingTaxMapping> getAllUnitSkuPackagingTaxMappingByStatus(Integer skuId, Integer packagingId,List<String> statuses) {
        return dao.findAllUnitSkuPackagingTaxMappingByStatus(skuId, packagingId,statuses).stream().collect(Collectors.toMap(SkuPackagingTaxMapping::getUnitId, Function.identity()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean updateUnitSkuPackagingTaxMapping(Integer skuId, Integer packagingId, Map<Integer, String> unitToTaxMap, Integer employeeId) {
        dao.updateUnitSkuPackagingTaxMapping(employeeId, skuId, packagingId, unitToTaxMap);
        if (Objects.nonNull(unitToTaxMap) && unitToTaxMap.size() > 0) {
            List<SkuPackagingTaxMapping> skuPackagingTaxMappingList = dao.findAllUnitSkuPackagingTaxMappingByStatus(skuId, packagingId, Arrays.asList(AppConstants.ACTIVE));
            List<SkuPackagingTaxMapping> updatedMappings = new ArrayList<>();

            skuPackagingTaxMappingList.forEach((mapping) -> {
                Integer unitId = mapping.getUnitId();
                if (unitToTaxMap.containsKey(unitId) && !unitToTaxMap.get(unitId).equals(mapping.getTaxCode())) {
                    mapping.setTaxCode(unitToTaxMap.get(unitId));
                    mapping.setUpdatedBy(employeeId);
                    mapping.setUpdatedAt(AppUtils.getCurrentTimestamp());
                    updatedMappings.add(mapping);
                }

            });
            dao.update(updatedMappings,true);
        }

    return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean converUnitDistanceToZipCodeDistance() throws SumoException {
       List<UnitDistanceMappingData> res =  scmMetadataDao.getUnitDistanceMapping();
       for(UnitDistanceMappingData u : res){
           dao.saveZipCodeDistance(u.getSourceUnitId(),u.getDestinationUnitId(),u.getDistance(),u.getDistance());
       }
       return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean generateSkuPriceUpdateRequest(List<SkuPriceUpdate> skuPriceUpdates) throws SumoException {
        for (SkuPriceUpdate data : skuPriceUpdates) {
            if(dao.updatePrices(data)) {
                LOG.info("Request Generated for SKU {} ",data.getDetail().getSku().getName());
                try{
                    scmMetadataService.saveAuditLog(data.getDetail().getKeyId(), AuditChangeLogTypes.SKU_PRICE.value(), data.getEmployeeId(), ((Object) data),
                            AuditChangeLogTypes.UPDATE_ENTRY.value());
                }catch (Exception e){
                    LOG.info("Error While saving Audit Log Data In Mongo",e);
                }
            }
        }
        return true;
    }

    @Override
    public List<SkuPriceDetail> getVendorPriceChange(int vendorId, String location) {
        return dao.searchPricesByVendorDeliveryLocation(vendorId, location, true, true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean processPriceRequestForVendor(SkuPriceUpdateDetail data) {
        Set<Integer> processedId =new HashSet<>();
        data.getApprovedNew().forEach(val -> {
            try{
                dao.processPriceRequestForVendor(val, PriceStatus.PENDING,PriceStatus.CREATED,processedId);
                scmMetadataService.saveAuditLog(val.getDetail().getKeyId(), AuditChangeLogTypes.SKU_PRICE.value(), val.getEmployeeId(), ((Object) val),
                        AuditChangeLogTypes.UPDATE_ENTRY.value());
            }catch (Exception e){
                LOG.info("Error While saving Audit Log Data In Mongo",e);
            }
        });
        data.getApproved().forEach(val -> {
            try{
                dao.processPriceRequestForVendor(val, PriceStatus.REACTIVATION_REQUESTED, PriceStatus.APPLIED, processedId);
                scmMetadataService.saveAuditLog(val.getDetail().getKeyId(), AuditChangeLogTypes.SKU_PRICE.value(), val.getEmployeeId(), ((Object) val),
                        AuditChangeLogTypes.UPDATE_ENTRY.value());
            }catch (Exception e){
                LOG.info("Error While saving Audit Log Data In Mongo",e);
            }
        });
        data.getRejectedNew().forEach(val -> {
            try{
                dao.processPriceRequestForVendor(val, PriceStatus.REJECTED, PriceStatus.CREATED, processedId);
                scmMetadataService.saveAuditLog(val.getDetail().getKeyId(), AuditChangeLogTypes.SKU_PRICE.value(), val.getEmployeeId(), ((Object) val),
                        AuditChangeLogTypes.UPDATE_ENTRY.value());
            }catch (Exception e){
                LOG.info("Error While saving Audit Log Data In Mongo",e);
            }
        });
        data.getRejected().forEach(val -> {
            try{
                dao.processPriceRequestForVendor(val, PriceStatus.DEACTIVATION_REQUESTED, PriceStatus.APPLIED, processedId);
                scmMetadataService.saveAuditLog(val.getDetail().getKeyId(), AuditChangeLogTypes.SKU_PRICE.value(), val.getEmployeeId(), ((Object) val),
                        AuditChangeLogTypes.UPDATE_ENTRY.value());
            }catch (Exception e){
                LOG.info("Error While saving Audit Log Data In Mongo",e);
            }
        });
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<SkuPriceDetail> previewVendorPriceChange(Integer vendorId) {
        return dao.vendorPriceChangeAsPerStatus(vendorId,List.of(PriceStatus.PENDING.name(),PriceStatus.REACTIVATION_REQUESTED.name()));
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean saveVendorPriceChange(VendorContract vendorContract) throws SumoException {
        dao.checkPendingVendorContract(vendorContract.getVendorId());
        List<SkuPriceHistory> skuPriceHistories = dao.saveVendorPriceChange(vendorContract);
        VendorContractInfo vendorContractInfo = dao.add(VendorContractInfo.builder().vendorId(vendorContract.getVendorId())
                .startDate(vendorContract.getStartDate()).endDate(vendorContract.getEndDate())
                .recordStatus(PriceStatus.CREATED.name())
                        .contractRequestedBy(vendorContract.getContractRequestedBy())
                .createdBy(vendorContract.getEmployeeName() + " [" + vendorContract.getEmployeeId() + "]")
                .creationTime(AppUtils.getCurrentTimestamp()).build(),true);
        if (Objects.isNull(vendorContractInfo.getVendorContractId())) {
            throw new SumoException("Vendor Contract Creation Failed");
        }
        skuPriceHistories.forEach(val -> {
            VendorContractItem vendorContractItem = null;
            try {
                if (!PriceStatus.DEACTIVATION_ACCEPTED.name().equals(val.getRecordStatus())) {
                    SkuPriceData skuPriceData = dao.find(SkuPriceData.class, val.getSkuPriceDataId());
                    Location dispatchLocation = masterDataCache.getAllLocations().get(skuPriceData.getDispatchLocation());
                    Location deliveryLocation = masterDataCache.getAllLocations().get(skuPriceData.getDispatchLocation());
                    String taxCode = scmCache.getProductDefinition(scmCache.getSkuDefinition(skuPriceData.getSkuId()).getLinkedProduct().getId()).getTaxCode();
                    TaxData taxData = taxDataCache.getTaxData(dispatchLocation.getState().getId(), taxCode);
                    BigDecimal taxPercentage = BigDecimal.ZERO;
                    if (dispatchLocation.getState().getCode().equalsIgnoreCase(deliveryLocation.getState().getCode())) {
                        taxPercentage = AppUtils.add(taxData.getState().getCgst(), taxData.getState().getSgst());
                    } else {
                        taxPercentage = AppUtils.add(taxData.getState().getIgst(),BigDecimal.ZERO);
                    }
                    vendorContractItem = dao.add(VendorContractItem.builder().vendorContractId(vendorContractInfo.getVendorContractId())
                            .startDate(vendorContractInfo.getStartDate())
                            .endDate(vendorContractInfo.getEndDate()).skuId(skuPriceData.getSkuId())
                            .skuPriceDataId(val.getSkuPriceDataId()).vendorId(vendorContractInfo.getVendorId())
                            .taxCode(taxCode)
                            .taxPercentage(taxPercentage)
                            .currentPrice(val.getCurrentPrice()).negotiatedPrice(val.getNegotiatedPrice())
                            .createdBy(vendorContract.getEmployeeName() + " [" + vendorContract.getEmployeeId() + "]")
                            .creationTime(AppUtils.getCurrentTimestamp()).dispatchLocation(skuPriceData.getDispatchLocation())
                            .deliveryLocation(skuPriceData.getDeliveryLocation()).skuPackagingId(skuPriceData.getPackagingId())
                            .isNew(AppUtils.setStatus(PriceStatus.APPROVED.name().equals(val.getRecordStatus())))
                            .build(), true);
                    if (Objects.isNull(vendorContractItem.getContractItemId())) {
                        throw new SumoException("Vendor Contract Creation Failed");
                    }
                }
            } catch (SumoException e) {
                throw new RuntimeException("Vendor Contract Creation Failed");
            }
        });
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<VendorContractVO> getVendorContract(Integer vendorId, PriceStatus status, Date startDate, Date endDate, Integer vendorContractId) {
        return dao.getVendorContract(vendorId,status,startDate,endDate,vendorContractId);
    }
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean cancelVendorContract(VendorContract vendorContractId) throws SumoException {
        return dao.cancelVendorContract(vendorContractId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public DocumentDetail generateContractUsingTemplate(Integer vendorContractId, Integer empCode, Integer templateId, List<VendorContractInfo> vendorContractInfos) throws SumoException, TemplateRenderingException {
        IdCodeName templateDetail = scmConstants.getVendorContractTemplate().get(templateId);
        StringBuilder fileName = new StringBuilder("VENDOR_CONTRACT"+vendorContractId+".pdf");
        VendorContractInfo vendorContractInfo = dao.find(VendorContractInfo.class,vendorContractId);
        if (Objects.isNull(vendorContractInfo)) {
            throw new SumoException("Vendor Contract Not Found");
        }
        if (Objects.isNull(vendorContractInfo.getEndDate())) {
            vendorContractInfo.setEndDate(AppUtils.addDays(vendorContractInfo.getStartDate(), Integer.parseInt(templateDetail.getName())));
        }
        String signedSignature = null;
        String signedIPAddress = null;
        if (Objects.nonNull(vendorContractInfo.getDigitalSignID())){
            DocumentDetailData documentDetailData = dao.find(DocumentDetailData.class, vendorContractInfo.getDigitalSignID());
            signedSignature = documentDetailData.getFileUrl();
            PageRequestDetail pageRequestDetail = dao.findPageRequestByEventTypeAndRecordStatus("VENDOR_CONTRACT",vendorContractInfo.getVendorContractId());
            if (Objects.nonNull(pageRequestDetail)) {
                signedIPAddress = pageRequestDetail.getAuthKey();
            }
        }
        String authSignedSignature = null;
        String authSignedIPSignature = null;
        if (Objects.nonNull(vendorContractInfo.getAuthDigitalSignID())){
            DocumentDetailData documentDetailData = dao.find(DocumentDetailData.class, vendorContractInfo.getAuthDigitalSignID());
            authSignedSignature = documentDetailData.getFileUrl();
            PageRequestDetail pageRequestDetail = dao.findPageRequestByEventTypeAndRecordStatus("VENDOR_CONTRACT_EMPLOYEE",vendorContractInfo.getVendorContractId());
            if (Objects.nonNull(pageRequestDetail)) {
                authSignedIPSignature = pageRequestDetail.getAuthKey();
            }
        }
        StringBuilder vendorContractString = new StringBuilder();
        if (Objects.nonNull(vendorContractInfos) && !vendorContractInfos.isEmpty()) {
            vendorContractString.append(vendorContractInfos.stream()
                    .map(obj -> String.valueOf(obj.getVendorContractId()))
                    .collect(Collectors.joining(", ")));
        }
        List<VendorContractItem> vendorContractItems = dao.findAllVendorContract(vendorContractInfo.getVendorContractId());
        VendorContractTemplate vendorContractTemplate = new VendorContractTemplate(vendorContractInfo,SCMDataConverter.convert(scmCache, masterDataCache, vendorContractItems),
                scmCache.getVendorDetail(vendorContractInfo.getVendorId()),
                masterDataCache.getEmployeeBasicDetail(Integer.valueOf(vendorContractInfo.getContractRequestedBy()))
                ,props.getBasePath(), templateDetail.getCode(),signedSignature,authSignedSignature,signedIPAddress,authSignedIPSignature,vendorContractString.toString());
        String invoiceHTMLTemplate = vendorContractTemplate.getContent();
        String path = props.getBasePath() + "/vendor-contract/" + fileName;
        try (OutputStream outputStream = Files.newOutputStream(Paths.get(path))) {
            HtmlConverter.convertToPdf(invoiceHTMLTemplate, outputStream);
            outputStream.flush();
            File file1 = new File(props.getBasePath() + "/vendor-contract/" + fileName);
            FileDetail fileDetail = fileArchiveService.saveFileToS3(props.getS3Bucket(), "VENDOR_CONTRACTS", fileName.toString(), file1, true);
            DocumentDetail documentDetail = new DocumentDetail();
            documentDetail.setMimeType(MimeType.PDF);
            documentDetail.setUploadType(DocUploadType.VENDOR_CONTRACT);
            documentDetail.setFileType(FileType.CONTRACT);
            documentDetail.setDocumentLink(vendorContractId.toString());
            documentDetail.setS3Key(fileDetail.getKey());
            documentDetail.setFileUrl(fileDetail.getUrl());
            documentDetail.setS3Bucket(fileDetail.getBucket());
            documentDetail.setUpdatedBy(SCMUtil.generateIdCodeName(empCode, "", masterDataCache.getEmployee(empCode)));
            documentDetail.setUpdateTime(SCMUtil.getCurrentTimestamp());
            DocumentDetailData data = dao.add(SCMDataConverter.convert(documentDetail), true);
            return SCMDataConverter.convert(data);
        } catch (IOException e) {
            LOG.error("Error when trying to create vendor contract :::::: {}", vendorContractId, e);
            throw new SumoException("Error creating vendor contract");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean mapVendorContractWithDocument(VendorContract vendorContract) throws SumoException {
        VendorContractInfo vendorContractInfo = dao.find(VendorContractInfo.class,vendorContract.getVendorContractId());
        if (Objects.isNull(vendorContractInfo)) {
            throw new SumoException("Contract Id "+vendorContract.getVendorContractId()+" Not Found");
        }
        if (PriceStatus.APPLIED.name().equals(vendorContractInfo.getRecordStatus())) {
            throw new SumoException("Contract Id"+ vendorContract.getVendorContractId()+" Already Applied");
        }
        vendorContractInfo.setUnsignedDocumentId(vendorContract.getDocumentId());
        vendorContractInfo.setTemplateId(vendorContract.getTemplateId());
        VendorContractInfo update =dao.update(vendorContractInfo,true);
        if (update.getUnsignedDocumentId().equals(vendorContract.getDocumentId())) {
            return true;
        }
        throw new SumoException("Unable to Update Contract Metadata");
     }


    private String generateAuthKey() {
        try {
            return PasswordImpl.encryptUrlCodec(AppUtils.getCurrentTimeISTString());
        } catch (Exception e) {
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public String getContractDocument(Integer documentId) throws SumoException {
        DocumentDetailData documentDetailData = dao.find(DocumentDetailData.class,documentId);
        if (Objects.isNull(documentDetailData)) {
            throw new SumoException("Document Not Found");
        }
        return documentDetailData.getFileUrl();
     }
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean triggerVendorContractMail(VendorContract vendorContract) throws SumoException, EmailGenerationException {
        VendorContractInfo vendorContractInfo = dao.find(VendorContractInfo.class,vendorContract.getVendorContractId());
        if (Objects.isNull(vendorContractInfo.getUnsignedDocumentId())) {
            throw new SumoException("Contract Document Not Found");
        }
        dao.cancelAllContractMailRequest(vendorContractInfo.getVendorContractId(),"VENDOR_CONTRACT");
        PageRequestDetail contractRequest = dao.add(PageRequestDetail.builder()
                .eventId(vendorContractInfo.getVendorContractId())
                .eventType("VENDOR_CONTRACT")
                .createdBy(vendorContract.getEmployeeName() + " [" + vendorContract.getEmployeeId() + "]")
                .recordStatus(VendorStatus.INITIATED.name())
                .registrationUrl(props.getVendorContractUrl())
                .requestDate(AppUtils.getBusinessDate())
                .authKey(generateAuthKey()).build(),true);
        if (Objects.isNull(contractRequest)) {
            throw new SumoException("Contract Event Creation Failed");
        }
        String link = SCMUtil.getRegistrationLink(contractRequest.getRegistrationUrl(), contractRequest.getAuthKey());
        DocumentDetailData documentDetailData = dao.find(DocumentDetailData.class,vendorContractInfo.getUnsignedDocumentId());
        VendorContractEmailNotificationTemplate emailTemplate = new VendorContractEmailNotificationTemplate(scmCache.getVendorDetail(vendorContractInfo.getVendorId()),
                masterDataCache.getEmployeeBasicDetail(Integer.parseInt(vendorContractInfo.getContractRequestedBy())),
                vendorContractInfo,props.getBasePath(),documentDetailData.getFileUrl(),link,"VendorContractEmailTemplate.html");
        VendorContractEmailNotification emailNotification = new VendorContractEmailNotification(emailTemplate,props.getEnvType(),
                new String[] {scmCache.getVendorDetail(vendorContractInfo.getVendorId()).getPrimaryEmail()},
                scmCache.getVendorDetail(vendorContractInfo.getVendorId()).getEntityName().toUpperCase());
        emailNotification.sendEmail();
        vendorContractInfo.setMailTime(AppUtils.getCurrentTimestamp());
        vendorContractInfo.setRecordStatus(PriceStatus.PENDING.name());
        vendorContractInfo.setIsMailTriggered(AppConstants.YES);
        vendorContractInfo = dao.update(vendorContractInfo,true);
        if (Objects.isNull(vendorContractInfo.getVendorContractId())) {
            contractRequest.setRecordStatus(VendorStatus.FAILED.name());
            dao.update(contractRequest,false);
            throw new SumoException("Mail Triggered Failed");
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean vendorAcceptance(VendorContract vendorContract) throws SumoException, TemplateRenderingException, EmailGenerationException, VendorRegistrationException {
        if (PriceStatus.APPROVED.equals(vendorContract.getStatus()) || PriceStatus.REJECTED.equals(vendorContract.getStatus())
                && AppConstants.YES.equalsIgnoreCase(vendorContract.getOtpVerified())) {
            VendorContractInfo vendorContractInfo = dao.find(VendorContractInfo.class,vendorContract.getVendorContractId());
            if (Objects.isNull(vendorContractInfo.getUnsignedDocumentId())) {
                throw new SumoException("Contract Document Not Found");
            }
            vendorContractInfo.setRecordStatus(vendorContract.getStatus().name());
            vendorContractInfo.setIpAddress(vendorContract.getIpAddress());
            vendorContractInfo.setDigitalSignID(vendorContract.getDigitalSignID());
            vendorContractInfo.setIsOtpVerified(AppConstants.YES);
            vendorContractInfo.setVendorUserName(vendorContract.getVendorUserName());
            vendorContractInfo.setVendorUserDesignation(vendorContract.getVendorUserDesignation());
            vendorContractInfo = dao.update(vendorContractInfo,true);
            if (PriceStatus.APPROVED.equals(vendorContract.getStatus())){
                DocumentDetail documentDetail = generateContractUsingTemplate(vendorContractInfo.getVendorContractId(),
                        AppConstants.SYSTEM_EMPLOYEE_ID, vendorContractInfo.getTemplateId(), null);
                vendorContractInfo.setSignedDocumentId(documentDetail.getDocumentId());
                vendorContractInfo = dao.update(vendorContractInfo, true);
                PageRequestDetail pageRequestDetail = dao.findByToken(vendorContract.getToken());
                pageRequestDetail.setRecordStatus(VendorStatus.COMPLETED.name());
                dao.update(pageRequestDetail,true);
                dao.cancelAllContractMailRequest(vendorContractInfo.getVendorContractId(),"VENDOR_CONTRACT_EMPLOYEE");
                PageRequestDetail contractRequest = dao.add(PageRequestDetail.builder()
                        .eventId(vendorContractInfo.getVendorContractId())
                        .eventType("VENDOR_CONTRACT_EMPLOYEE")
                        .createdBy("SYSTEM["+ AppConstants.SYSTEM_EMPLOYEE_ID+"]")
                        .recordStatus(VendorStatus.INITIATED.name())
                        .registrationUrl(props.getSystemContractUrl())
                        .requestDate(AppUtils.getBusinessDate())
                        .authKey(generateAuthKey()).build(),true);
                if (Objects.isNull(contractRequest)) {
                    throw new SumoException("Contract Event Creation Failed");
                }
                String link = SCMUtil.getRegistrationLink(contractRequest.getRegistrationUrl(), contractRequest.getAuthKey());
                DocumentDetailData documentDetailData = dao.find(DocumentDetailData.class,vendorContractInfo.getSignedDocumentId());
                VendorContractEmailNotificationTemplate emailTemplate = new VendorContractEmailNotificationTemplate(scmCache.getVendorDetail(vendorContractInfo.getVendorId()),
                        masterDataCache.getEmployeeBasicDetail(Integer.parseInt(vendorContractInfo.getContractRequestedBy())),
                        vendorContractInfo,props.getBasePath(),documentDetailData.getFileUrl(),link,"VendorContractEmailApprovalTemplate.html");
                VendorContractEmailNotification emailNotification = new VendorContractEmailNotification(emailTemplate,props.getEnvType(),
                        new String[]{masterDataCache.getEmployeeBasicDetail(Integer.parseInt(vendorContractInfo.getContractRequestedBy())).getEmailId()},
                        scmCache.getVendorDetail(vendorContractInfo.getVendorId()).getEntityName().toUpperCase());
                emailNotification.sendEmail();
                return true;
            } else {
                IdCodeName templateDetail = scmConstants.getVendorContractTemplate().get(vendorContractInfo.getTemplateId());
                dao.applyContractOnSKU(vendorContract,templateDetail);
            }
            if (Objects.isNull(vendorContractInfo.getVendorContractId())) {
                throw new SumoException("Error Encountered while updating");
            }
            return true;
        } else {
            throw new SumoException("Incorrect Metadata found");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean contractApplied(VendorContract vendorContract) throws SumoException, TemplateRenderingException, EmailGenerationException, VendorRegistrationException {
        if (PriceStatus.APPLIED.equals(vendorContract.getStatus())) {
            List<VendorContractInfo> vendorContractInfos = dao.closePreviousContract(vendorContract.getVendorId());
            VendorContractInfo vendorContractInfo = dao.find(VendorContractInfo.class,vendorContract.getVendorContractId());
            if (Objects.isNull(vendorContractInfo.getUnsignedDocumentId()) &&
                    Objects.isNull(vendorContractInfo.getSignedDocumentId())) {
                throw new SumoException("Contract Document Not Found");
            }
            vendorContractInfo.setRecordStatus(vendorContract.getStatus().name());
            vendorContractInfo.setAuthDigitalSignID(vendorContract.getAuthDigitalSignID());
            vendorContractInfo.setAuthIpAddress(vendorContract.getAuthIpAddress());
            vendorContractInfo = dao.update(vendorContractInfo,true);
            PageRequestDetail pageRequestDetail = dao.findByToken(vendorContract.getToken());
            pageRequestDetail.setRecordStatus(VendorStatus.COMPLETED.name());
            dao.update(pageRequestDetail,true);
            DocumentDetail documentDetail = generateContractUsingTemplate(vendorContractInfo.getVendorContractId(),
                    AppConstants.SYSTEM_EMPLOYEE_ID, vendorContractInfo.getTemplateId(),vendorContractInfos);
            vendorContractInfo.setAuthSignedDocumentId(documentDetail.getDocumentId());
            vendorContractInfo = dao.update(vendorContractInfo, true);
            VendorContractEmailNotificationTemplate emailTemplate = new VendorContractEmailNotificationTemplate(scmCache.getVendorDetail(vendorContractInfo.getVendorId()),
                    masterDataCache.getEmployeeBasicDetail(Integer.parseInt(vendorContractInfo.getContractRequestedBy())),
                    vendorContractInfo,props.getBasePath(),documentDetail.getFileUrl(),null,"VendorContractEmailAppliedTemplate.html");
            VendorContractEmailNotification emailNotification = new VendorContractEmailNotification(emailTemplate,props.getEnvType(),
                    new String[] {masterDataCache.getEmployeeBasicDetail(Integer.parseInt(vendorContractInfo.getContractRequestedBy())).getEmailId(),
                            scmCache.getVendorDetail(vendorContractInfo.getVendorId()).getPrimaryEmail()},
                    scmCache.getVendorDetail(vendorContractInfo.getVendorId()).getEntityName().toUpperCase());
            emailNotification.sendEmail();
            if (AppUtils.getBusinessDate().compareTo(vendorContractInfo.getStartDate())>=0){
                IdCodeName templateDetail = scmConstants.getVendorContractTemplate().get(vendorContractInfo.getTemplateId());
                dao.applyContractOnSKU(vendorContract,templateDetail);
            }
            if (Objects.isNull(vendorContractInfo.getVendorContractId())) {
                throw new SumoException("Error Encountered while updating");
            }
            return true;
        } else {
            return cancelVendorContract(vendorContract);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void applyContract() {
        dao.applyContract();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void expiryContract() {
        dao.expiryContract();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean triggerEmailOtpForVendor(Integer vendorId) throws SumoException {
        String contactNumber = scmCache.getVendorDetail(vendorId).getPrimaryContact();
        try {
            String otp = notificationService.getOTPMapperInstance().generateOTP(false, OtpType.VENDOR_CONTRACT, contactNumber, props.getEnvType());
            StringBuilder message = new StringBuilder(String.format("OTP for Vendor Contract Approval : %s\n" +
                    "For User Id : %s\n" +
                    "\n" +
                    "-SUNSHINE TEAHOUSE PVT LTD" , otp, AppConstants.SYSTEM_EMPLOYEE_ID));
//            StringBuilder message = new StringBuilder(String.format("OTP for Vendor Contract Approval : %s   For User Id : %s   -SUNSHINE TEAHOUSE PVT LTD" , otp, AppConstants.SYSTEM_EMPLOYEE_ID));
            VendorContractEmailOTPNotificationTemplate emailTemplate = new VendorContractEmailOTPNotificationTemplate(scmCache.getVendorDetail(vendorId), otp,props.getBasePath());
            VendorContractEmailOTPNotification emailNotification = new VendorContractEmailOTPNotification(emailTemplate,props.getEnvType(),scmCache.getVendorDetail(vendorId).getPrimaryEmail());
            emailNotification.sendEmail();
            return notificationService.sendNotification("VENDOR_CONTRACT", message.toString(), contactNumber,
                    providerService.getSMSClient(SMSType.OTP, ApplicationName.SCM_SERVICE), false, null);
        } catch (Exception e) {
            LOG.error("##### Error while sending otp ##########, {}", e.getMessage());
            throw new SumoException("Email OTP Failed");
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean triggerEmailOtpForEmployee(Integer employeeId) throws SumoException {
        String contactNumber = masterDataCache.getEmployeeBasicDetail(employeeId).getContactNumber();
        try {
            String otp = notificationService.getOTPMapperInstance().generateOTP(false,
                    OtpType.VENDOR_CONTRACT, contactNumber, props.getEnvType());
            StringBuilder message = new StringBuilder(String.format("OTP for Vendor Contract Approval : %sFor User Id : %s-SUNSHINE TEAHOUSE PVT LTD" , otp, employeeId));
            return notificationService.sendNotification("VENDOR_CONTRACT", message.toString(), contactNumber,
                    providerService.getSMSClient(SMSType.OTP, ApplicationName.SCM_SERVICE), true, null);
        } catch (Exception e) {
            LOG.error("##### Error while sending otp ##########, {}", e.getMessage());
            throw new SumoException("Email OTP Failed");
        }

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean validateVendorOtp(VendorOTPValidationDomain otp) throws SumoException {
        StringBuilder contactNumber = new StringBuilder();
        if (Objects.nonNull(otp.getVendorId())){
            VendorDetail vendorDetail = scmCache.getVendorDetail(otp.getVendorId());
            contactNumber.append(vendorDetail.getPrimaryContact());
        } else if (Objects.nonNull(otp.getEmployeeId())) {
            EmployeeBasicDetail employeeBasicDetail = masterDataCache.getEmployeeBasicDetail(otp.getEmployeeId());
            contactNumber.append(employeeBasicDetail.getContactNumber());
        }
        if (!StringUtils.isEmpty(contactNumber.toString()) && !StringUtils.isEmpty(otp.getOtpValue())) {
            boolean result = otp.getOtpValue()
                    .equals(notificationService.getOTPMapperInstance().getOTP(OtpType.VENDOR_CONTRACT, contactNumber.toString()));
            if (result) {
                notificationService.getOTPMapperInstance().removeOTP(OtpType.VENDOR_CONTRACT, contactNumber.toString());
                return true;
            }
            throw new SumoException("VENDOR_CONTRACT_EXCEPTION","Otp Verification  Failed, Please enter valid opt.");
        } else {
            throw new SumoException("VENDOR_CONTRACT_EXCEPTION","Contact number or otp is empty !");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public VendorContractVO validateRequest(String token) throws UnsupportedEncodingException, VendorRegistrationException, SumoException {
        token = URLDecoder.decode(token, "UTF-8");
        PageRequestDetail request = dao.findByToken(token);
        if (request != null) {
            return dao.getVendorContractVoByContractId(request.getEventId());
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", propagation = Propagation.REQUIRED)
    public DocumentDetail saveDigitalSignature(MultipartFile file, Integer vendorContractId) throws SumoException {
        String fileName = MultiPartFileHelper.getVendorSignatureUploadFileName(FileType.OTHERS,vendorContractId, MimeType.PNG);
        FileDetail s3File = null;

        s3File = fileArchiveService.saveFileToS3(props.getS3Bucket(), "VENDOR_CONTRACTS", fileName, file);
        DocumentDetail documentDetail = new DocumentDetail();
        documentDetail.setUploadTypeId(vendorContractId);
        documentDetail.setMimeType(MimeType.PNG);
        documentDetail.setUploadType(DocUploadType.VENDOR_CONTRACT);
        documentDetail.setFileType(FileType.CONTRACT);
        documentDetail.setDocumentLink(fileName);
        documentDetail.setS3Key(s3File.getKey());
        documentDetail.setFileUrl(s3File.getUrl());
        documentDetail.setS3Bucket(s3File.getBucket());
        documentDetail
                .setUpdatedBy(SCMUtil.generateIdCodeName(SCMServiceConstants.SYSTEM_USER, "SYSTEM", "SYSTEM"));
        documentDetail.setUpdateTime(SCMUtil.getCurrentTimestamp());
        DocumentDetailData data = dao.add(SCMDataConverter.convert(documentDetail), true);
        return SCMDataConverter.convert(data);

    }
}
