package com.stpl.tech.scm.notification.email;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.notification.email.template.CapexEmailNotificationTemplate;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

public class CapexEmailNotification extends EmailNotification {

    private CapexEmailNotificationTemplate capexEmailNotification;
    private EnvType envType;
    private String[] emails;

    public CapexEmailNotification() {
    }

    public CapexEmailNotification(CapexEmailNotificationTemplate capexEmailNotification, EnvType envType, String[] emails) {
        this.capexEmailNotification = capexEmailNotification;
        this.envType = envType;
        this.emails = emails;
    }

    @Override
    public String[] getToEmails() {
        if (SCMUtil.isDev(envType)) {
            return new String[] { "<EMAIL>" };
        } else {
            return new String[] { "<EMAIL>", "<EMAIL>" };
        }
    }

    @Override
    public String getFromEmail() {
        return "<EMAIL>";
    }

    @Override
    public String subject() {
        String subjectOfEmail = capexEmailNotification.getSubjectOfEmail();
        if (SCMUtil.isDev(envType)) {
            subjectOfEmail = " [DEV] : " + subjectOfEmail;
        }
        return subjectOfEmail;
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            return capexEmailNotification.getContent();
        } catch (TemplateRenderingException e) {
            throw new EmailGenerationException("Failed to render the template", e);
        }
    }

    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }
}
