package com.stpl.tech.scm.core.service.impl;

import com.itextpdf.html2pdf.jsoup.Jsoup;
import com.itextpdf.html2pdf.jsoup.nodes.Document;
import com.itextpdf.html2pdf.jsoup.nodes.Element;
import com.itextpdf.html2pdf.jsoup.select.Elements;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.TransportManagementService;
import com.stpl.tech.scm.core.util.EWayHelper;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.dao.TransportManagementDao;
import com.stpl.tech.scm.data.model.ConsignmentData;
import com.stpl.tech.scm.data.model.EwayBillData;
import com.stpl.tech.scm.data.model.TransferOrderData;
import com.stpl.tech.scm.data.model.VehicleData;
import com.stpl.tech.scm.data.model.VehicleDispatchData;
import com.stpl.tech.scm.data.transport.model.EWayWrapper;
import com.stpl.tech.scm.data.transport.model.TransportMode;
import com.stpl.tech.scm.domain.model.ConsignmentType;
import com.stpl.tech.scm.domain.model.DispatchStatus;
import com.stpl.tech.scm.domain.model.EWayBill;
import com.stpl.tech.scm.domain.model.EWayBillStatus;
import com.stpl.tech.scm.domain.model.EWayResponse;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.MimeType;
import com.stpl.tech.scm.domain.model.TransferOrder;
import com.stpl.tech.scm.domain.model.Vehicle;
import com.stpl.tech.scm.domain.model.VehicleDispatch;
import com.stpl.tech.scm.notification.email.DispatchEWayRequestNotification;
import com.stpl.tech.scm.notification.email.template.DispatchEWayRequestTemplate;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;
import com.stpl.tech.util.excelparser.SheetParser;
import com.stpl.tech.util.excelparser.exception.ExcelParsingException;
import com.stpl.tech.util.notification.AttachmentData;
import org.apache.commons.io.IOUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class TransportManagementServiceImpl implements TransportManagementService {

	private static final Logger LOG = LoggerFactory.getLogger(TransportManagementServiceImpl.class);

	@Autowired
	private SCMCache scmCache;

	@Autowired
	private MasterDataCache masterDataCache;

	@Autowired
	private TransportManagementDao transportManagementDao;

	@Autowired
	private FileArchiveService fileArchiveService;

	@Autowired
	private EnvProperties env;

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<IdCodeName> getAllVehicles() {
		List<IdCodeName> list = new ArrayList<>();
		List<VehicleData> vehicles = transportManagementDao.findAll(VehicleData.class);
		for (VehicleData v : vehicles) {
			if (SCMUtil.isActive(v.getStatus())) {
				list.add(SCMDataConverter.convertToIdCodeName(v));
			}
		}
		return list;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<Vehicle> getAllVehiclesByMode(TransportMode transportMode, Date date) {
		List<Vehicle> list = new ArrayList<>();
		List<VehicleData> vehicles = transportManagementDao.findAll(VehicleData.class);
		for (VehicleData v : vehicles) {
			if (SCMUtil.isActive(v.getStatus()) && transportMode.name().equals(v.getTransportMode())) {
				list.add(SCMDataConverter.convert(v));
			}
		}
		if (date != null) {
			LOG.info("Getting Suggestions for Vehicles ");
			Date beforeDate = AppUtils.getDayBeforeOrAfterDay(date, -14);
			LOG.info("Date before 14 days is : {}",beforeDate);
			List<Integer> suggestedVehicles = transportManagementDao.findVehicleDataByDates(beforeDate);
			LOG.info("Size of returned data is : {}", suggestedVehicles.size());
			if (suggestedVehicles.size() > 0) {
				for (Vehicle vehicle : list) {
					if (suggestedVehicles.contains(vehicle.getVehicleId())) {
						LOG.info("Suggested Vehicle Name is : {} and Vehicle Id is : {}", vehicle.getName(), vehicle.getVehicleId());
						vehicle.setSuggested("Suggested");
					} else {
						vehicle.setSuggested("All");
					}
				}
			}
		}
		return list;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public VehicleDispatch getVehicleDispatchForDate(Integer vehicleId, String transporter, String docketNumber,
													 Date dispatchDate, String registrationNumber) throws SumoException {
		VehicleData vehicleData = transportManagementDao.find(VehicleData.class, vehicleId);
		if (vehicleData == null) {
			throw new SumoException("Vehicle Not found with Id:" + vehicleId);
		}

		List<VehicleDispatchData> dispatchList = transportManagementDao.getVehicleDispatchHistory(vehicleId, dispatchDate, dispatchDate, registrationNumber);

		VehicleDispatchData dispatchData = null;

		if (!AppUtils.getStatus(vehicleData.getMultiDispatch())) {
			// Keep Single dispatch for non multi dispatch
			if (dispatchList != null && !dispatchList.isEmpty()) {
				dispatchData = dispatchList.get(0);
			} else {
				dispatchData = createVehicleDispatch(vehicleData, dispatchDate, transporter, docketNumber, registrationNumber);
			}
		} else {
			// check and create dispatch if not available
			if (dispatchList != null && !dispatchList.isEmpty()) {
				for (VehicleDispatchData data : dispatchList) {
					if (DispatchStatus.CREATED.name().equals(data.getStatus())
							|| DispatchStatus.PROCESSING.name().equals(data.getStatus())) {
						dispatchData = data;
						break;
					}
				}
			}

			if (dispatchData == null) {
				if (dispatchList == null || dispatchList.isEmpty() || AppUtils.getStatus(vehicleData.getMultiDispatch())) {
					dispatchData = createVehicleDispatch(vehicleData, dispatchDate, transporter, docketNumber, registrationNumber);
				}
			}
		}
		return SCMDataConverter.convert(dispatchData, scmCache, masterDataCache, false);
	}

	private VehicleDispatchData createVehicleDispatch(VehicleData vehicle, Date date, String gstin, String docNumber, String registrationNumber)
            throws SumoException {
		VehicleDispatchData dispatchData = new VehicleDispatchData();
		dispatchData.setVehicle(vehicle);
		dispatchData.setDispatchDate(date);
        dispatchData.setTransporterGstin(gstin);
        dispatchData.setDocketNumber(docNumber);
		dispatchData.setVehicleNumber(registrationNumber);
		dispatchData.setStatus(DispatchStatus.CREATED.name());
		dispatchData = transportManagementDao.add(dispatchData, true);
		return dispatchData;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<TransferOrder> getPendingTransferOrdersForEway(List<Integer> unitIds, Date startDate, Date endDate, List<Integer> receivingUnitIds) {
		List<Integer> availableTO = transportManagementDao.getTransferOrdersForEway(unitIds, startDate, endDate, receivingUnitIds);
		List<Integer> alreadyProcessdTO = transportManagementDao.getProcessedTransferOrders(availableTO);
		availableTO.removeAll(alreadyProcessdTO);
		return transportManagementDao.findAllTransferOrders(availableTO);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean createConsignment(Integer dispatchId, List<Integer> toList) throws SumoException {
		return transportManagementDao.createConsignment(dispatchId, toList);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean cancelConsignment(Integer consignmentId) throws SumoException {
		return transportManagementDao.cancelConsignment(consignmentId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public List<EWayWrapper> generateEWayJson(Integer dispatchId , HttpServletResponse response) throws SumoException, IOException {
		return generateEWayFormat(dispatchId, false,response);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public List<EWayWrapper> startDispatch(Integer dispatchId, boolean forceEway) throws SumoException, IOException {
		VehicleDispatchData dd = transportManagementDao.find(VehicleDispatchData.class, dispatchId);
		if (!DispatchStatus.CREATED.name().equals(dd.getStatus())) {
			throw new SumoException("Dispatch already in " + dd.getStatus() + " status");
		}
		checkAndUpdateEwayBills(dd);
		dd.setStatus(DispatchStatus.PROCESSING.name());
        dd.setForceEway(forceEway ? SCMServiceConstants.SCM_CONSTANT_YES : SCMServiceConstants.SCM_CONSTANT_NO);
        for (ConsignmentData cd : dd.getConsignmentList()) {
			cd.setStatus(DispatchStatus.PROCESSING.name());
		}
		transportManagementDao.flush();
		return generateEWayFormat(dd.getDispatchId(), true,null);

	}

	private List<EWayWrapper> generateEWayFormat(int dispatchId, boolean sendEmail , HttpServletResponse response) throws SumoException, IOException {

		VehicleDispatchData data = transportManagementDao.find(VehicleDispatchData.class, dispatchId);

		if (DispatchStatus.CREATED.name().equals(data.getStatus())) {
			throw new SumoException("Dispatch is in " + data.getStatus() + " State, unable to generate JSON");
		}

		Map<String, List<EwayBillData>> ewayMap = new HashMap<>();

		for (ConsignmentData cd : data.getConsignmentList()) {
			for (EwayBillData eway : cd.getEwaybills()) {
				UnitBasicDetail u = masterDataCache.getUnitBasicDetail(eway.getTransferOrder().getGenerationUnitId());
				if (ewayMap.containsKey(u.getTin())) {
					ewayMap.get(u.getTin()).add(eway); // <HaryanaGSTIN, List<ewayBill>>
				} else {
					List<EwayBillData> l = new ArrayList<>();
					l.add(eway);
					ewayMap.put(u.getTin(), l);
				}
			}
		}

		List<EWayWrapper> wrappers = new ArrayList<>();

		for (String gstin : ewayMap.keySet()) {
			wrappers.add(EWayHelper.convertToEWayData(ewayMap.get(gstin), gstin, data, masterDataCache));
		}
		if (wrappers != null) {
			boolean ewayRequired = false;
			for (EWayWrapper w : wrappers) {
				if (!w.getBillLists().isEmpty()) {
					ewayRequired = true;
				}
			}
			if(ewayRequired){
				if (sendEmail) {
					sendDispatchEwayRequestNotification(data, wrappers);
				}else{
					 String path = createEwayJsonFile(data,wrappers,response);
					 if(Objects.nonNull(path)){
						 setResponseHeaders(path,response);
					 }
				}
			}else{
				markDispatchAsCompleted(dispatchId);
			}
		}
		return wrappers;
	}

	private void  setResponseHeaders(String path , HttpServletResponse response) throws IOException {
		File tempjson = new File(path);
		response.setContentType(AppConstants.JSON_MIME_TYPE);
		response.addHeader("Content-Disposition", "attachment; filename=" + tempjson.getName());
		byte[] bytesArray =    new byte[(int) tempjson.length()];
		response.setContentLength(bytesArray.length);
		try {
			OutputStream outputStream = response.getOutputStream();
			InputStream inputStream = new FileInputStream(tempjson);
			int counter = 0;
			while ((counter = inputStream.read(bytesArray, 0, bytesArray.length)) > 0) {
				outputStream.write(bytesArray, 0, counter);
				outputStream.flush();
			}
			outputStream.close();
			inputStream.close();
		} catch (IOException e) {
			LOG.error("Encountered error while writing file to response stream",e);
			throw e;
		} finally {
			response.getOutputStream().flush();
			tempjson.delete();
		}
	}

	public void sendDispatchEwayRequestNotification(VehicleDispatchData dispatchData, List<EWayWrapper> wrappers) {
		try {
			VehicleDispatch dispatch = SCMDataConverter.convert(dispatchData, scmCache, masterDataCache, true);
			DispatchEWayRequestTemplate template = new DispatchEWayRequestTemplate(dispatch, env.getBasePath());
			DispatchEWayRequestNotification notification = new DispatchEWayRequestNotification(template,
					env.getEnvType(), dispatch.getDispatchId());

			List<AttachmentData> attachments = new ArrayList<>();
			for (EWayWrapper w : wrappers) {
				String filePath = template.getDirectory();
				String fileName = "E-WayBill_JSON_" + w.getGstin();
				File file = new File(filePath);
				if (!file.exists()) {
					file.mkdirs();
				}
				String json = JSONSerializer.toJSON(w);
				String path = SCMUtil.write(json.getBytes(StandardCharsets.UTF_8), filePath, "json", fileName, LOG);
				AttachmentData reports = new AttachmentData(IOUtils.toByteArray(new FileInputStream(path)), fileName, MimeType.JSON.value());
				attachments.add(reports);
			}
			notification.sendRawMail(attachments);
		} catch (Exception e) {
			LOG.error("Error sending asset order notification email for: " + dispatchData.getDispatchId(), e);
		}
	}


	private String createEwayJsonFile(VehicleDispatchData dispatchData, List<EWayWrapper> wrappers , HttpServletResponse response){
	    String path = null;
		try {
			VehicleDispatch dispatch = SCMDataConverter.convert(dispatchData, scmCache, masterDataCache, true);
			DispatchEWayRequestTemplate template = new DispatchEWayRequestTemplate(dispatch, env.getBasePath());
			DispatchEWayRequestNotification notification = new DispatchEWayRequestNotification(template,
					env.getEnvType(), dispatch.getDispatchId());

			List<AttachmentData> attachments = new ArrayList<>();
			for (EWayWrapper w : wrappers) {
				String filePath = template.getDirectory();
				String fileName = "E-WayBill_JSON_" + w.getGstin();
				File file = new File(filePath);
				if (!file.exists()) {
					file.mkdirs();
				}
				String json = JSONSerializer.toJSON(w);
				path = SCMUtil.write(json.getBytes(StandardCharsets.UTF_8), filePath, "json", fileName, LOG);
			}
		} catch (Exception e) {
			LOG.error("Error Creating Eway Json File for: " + dispatchData.getDispatchId(), e);
		}
		return path;
	}


	public void checkAndUpdateEwayBills(VehicleDispatchData dd) {

		List<EwayBillData> transferEvents = new ArrayList<>();
		List<EwayBillData> returnEvents = new ArrayList<>();

		for (ConsignmentData cd : dd.getConsignmentList()) {
			if (ConsignmentType.RETURN.name().equals(cd.getConsignmentType())) {
				returnEvents.addAll(cd.getEwaybills());
			} else {
				transferEvents.addAll(cd.getEwaybills());
			}
		}

		// E-Way Data for Transfers
		checkEWayRequired(transferEvents);
		// E-Way data for Returns
		checkEWayRequired(returnEvents);

		List<EwayBillData> events = new ArrayList<>();
		events.addAll(transferEvents);
		events.addAll(returnEvents);
		// transportManagementDao.updateEWayStatus(events, EWayBillStatus.PREPARED);
	}

	private void checkEWayRequired(List<EwayBillData> transferEvents) {
		// Conditions
		// InterState - check unit states
		// company does not matter
		// If states are equal || distance = 0 then set required as false
		// else
		// add to qualified TOs
		// add amount to qualified TO amount
		// End loop
		// if qualified TO amount > 50000
		// then set all required as true
		// else set all Required as false
		BigDecimal totalAmount = BigDecimal.ZERO;
		Set<Integer> qualifiedEways = new HashSet<>();
		TransferOrderData to = null;
		Unit fromUnit = null;
		Unit toUnit = null;

		for (EwayBillData eway : transferEvents) {
			to = eway.getTransferOrder();
			fromUnit = masterDataCache.getUnit(to.getGenerationUnitId());
			toUnit = masterDataCache.getUnit(to.getGeneratedForUnitId());
			// BigDecimal distance = scmCache.getDistance(fromUnit.getId(), toUnit.getId());
			if (BigDecimal.ZERO.compareTo(eway.getDistance()) != 0) {
				qualifiedEways.add(eway.getId());
				totalAmount = totalAmount.add(to.getTotalAmount());
			}
		}

		boolean ewayRequired = false;

		if (totalAmount.compareTo(new BigDecimal(50000)) > 0) {
			ewayRequired = true;
		}

		for (EwayBillData eway : transferEvents) {
			if (ewayRequired && qualifiedEways.contains(eway.getId())) {
				eway.setEwayRequired(AppConstants.YES);
				eway.setStatus(EWayBillStatus.PREPARED.name());
			} else {
				eway.setEwayRequired(AppConstants.NO);
				eway.setStatus(EWayBillStatus.NOT_REQUIRED.name());
			}
		}

	}

	@Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public List<EWayResponse> parseEwayResponseFile(MultipartFile file, Integer dispatchId) throws IOException, NoSuchFieldException, IllegalAccessException {
        Workbook workbook = null;
        if (file.getOriginalFilename().endsWith("xls")) {
			try{
				workbook = new HSSFWorkbook(file.getInputStream());
			}catch(Exception e){
				LOG.info("Uploaded xls File is Corrupted .Parsing It as Html .");
				try {
					File tempFile = fileArchiveService.convertFromMultiPart("temp_" + dispatchId +".html",file);
					return parseEwayResponseHtmlFile(tempFile,dispatchId);
				}catch (Exception exception){
					LOG.info("Error while Parsing Html FIle ");
					throw exception;
				}

			}
        } else {
            workbook = new XSSFWorkbook(file.getInputStream());
        }
        return parseEwayResponseFile(workbook,dispatchId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<EWayResponse> parseEwayResponseFile(InputStream stream, String name, Integer dispatchId) throws IOException {
        Workbook workbook;
        if (name.endsWith("xls")) {
				 workbook =  new HSSFWorkbook(stream);
		} else {
            workbook = new XSSFWorkbook(stream);
		}
        return parseEwayResponseFile(workbook,dispatchId);
    }

	private List<EWayResponse> parseEwayResponseHtmlFile(File htmlFile , Integer dispatch) throws IOException, NoSuchFieldException, IllegalAccessException {
		List<EWayResponse> entityList = new ArrayList<>();
		Document doc = Jsoup.parse(htmlFile, "UTF-8");
		Elements rows = doc.select("tr");
		Integer headerCount = doc.select("th").size();
		Field[] fields = EWayResponse.class.getDeclaredFields();
		for(Element row :rows)
		{

			EWayResponse response = new EWayResponse();
			Elements columns = row.select("td");
			if(columns.size()>0) {
				int i =0;
				for(Field field : fields){
					if(i>=headerCount){
						continue;
					}
					LOG.info("Field : {}",field.getName());
					LOG.info("Header : {}",columns.get(i).text());
					field.setAccessible(true);
					field.set(response,columns.get(i).text());
					i++;
				}
				entityList.add(response);
			}
		}
		LOG.info("Lenght : {}",entityList.size());
		Map<String, EWayResponse> updatedSet = new HashMap<>();
		for (EWayResponse r : entityList) {
			updatedSet.put(getValidDocNumberFromEwayNumber(r.getDocNo()), r);
		}
		List<EwayBillData> bills = getBillsWithEWayRequired(dispatch);
		Set<String> eways = new HashSet<>();

		// forward check
		for (EwayBillData ed : bills) {
			if (!updatedSet.containsKey(ed.getTransferOrder().getGeneratedInvoiceId())) {
				entityList.add(new EWayResponse(ed.getTransferOrder().getGeneratedInvoiceId(),
						"Eway bill number not available", true));
			}else {
				updatedSet.get(ed.getTransferOrder().getGeneratedInvoiceId()).setToId(ed.getTransferOrder().getId());
			}
			eways.add(ed.getTransferOrder().getGeneratedInvoiceId());
		}

		// reverse check
		for (EWayResponse r : entityList) {
			if (!eways.contains(getValidDocNumberFromEwayNumber(r.getDocNo()))) {
				r.setErrors(r.getErrors() + "Transfer Order Not Found under this dispatch");
			}
		}
        htmlFile.delete();
		return entityList;
	}

    private List<EWayResponse> parseEwayResponseFile(Workbook workbook, Integer dispatchId) throws IOException {
		List<EWayResponse> entityList = null;
		try {
			List<ExcelParsingException> errors = new ArrayList<>();
			SheetParser parser = new SheetParser();
			entityList = parser.createEntity(workbook.getSheetAt(0), EWayResponse.class, errors::add);
			if (!errors.isEmpty()) {
				LOG.info("Error Parsing Workbook for E-WayBill, total errors :{}", errors.size());
				StringBuilder sb = new StringBuilder();
				errors.forEach(e -> sb.append(e.getMessage() + '\n'));
				LOG.info("{}", sb.toString());
				throw new ExcelParsingException(sb.toString());
			}
			Map<String, EWayResponse> updatedSet = new HashMap<>();
			for (EWayResponse r : entityList) {
				updatedSet.put(getValidDocNumberFromEwayNumber(r.getDocNo()), r);
			}
			List<EwayBillData> bills = getBillsWithEWayRequired(dispatchId);
			Set<String> eways = new HashSet<>();

			// forward check
			for (EwayBillData ed : bills) {
				if (!updatedSet.containsKey(ed.getTransferOrder().getGeneratedInvoiceId())) {
					entityList.add(new EWayResponse(ed.getTransferOrder().getGeneratedInvoiceId(),
							"Eway bill number not available", true));
				}else {
					updatedSet.get(ed.getTransferOrder().getGeneratedInvoiceId()).setToId(ed.getTransferOrder().getId());
				}
				eways.add(ed.getTransferOrder().getGeneratedInvoiceId());
			}

			// reverse check
			for (EWayResponse r : entityList) {
				if (!eways.contains(getValidDocNumberFromEwayNumber(r.getDocNo()))) {
					r.setErrors(r.getErrors() + "Transfer Order Not Found under this dispatch");
				}
			}

		} catch (Exception e) {
			LOG.error("Error while updating Eway bill number", e);
			throw e;
		} finally {
			workbook.close();
		}
		return entityList;
	}

	private String getValidDocNumberFromEwayNumber(String docNo) {
		if (docNo.indexOf("/") == 1) {
			return "0" + docNo;
		} else {
			return docNo;
		}
	}

	public static void main(String[] args) {
/*		System.out.println(getValidDocNumberFromEwayNumber("13/OST/12345"));
		System.out.println(getValidDocNumberFromEwayNumber("07/OST/12345"));
		System.out.println(getValidDocNumberFromEwayNumber("7/OST/12345"));
*/	}

	private List<EwayBillData> getBillsWithEWayRequired(Integer dispatchId) {
		List<EwayBillData> bills = new ArrayList<>();
		VehicleDispatchData data = transportManagementDao.find(VehicleDispatchData.class, dispatchId);
		if (data != null) {
			data.getConsignmentList().stream().forEach(c -> bills.addAll(c.getEwaybills().stream()
					.filter(e -> AppUtils.getStatus(e.getEwayRequired())).collect(Collectors.toList())));
		}
		return bills;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public List<EWayBill> getEwayBills(Integer dispatchId) {
		List<EWayBill> bills = new ArrayList<>();
		VehicleDispatchData data = transportManagementDao.find(VehicleDispatchData.class, dispatchId);
		if (data != null) {
			VehicleDispatch dispatch = SCMDataConverter.convert(data, scmCache, masterDataCache, true);
			dispatch.getConsignmentList().stream().forEach(c -> bills.addAll(c.getEwaybills()));
		}
		return bills;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean updateEwayData(List<EWayResponse> list) {
		transportManagementDao.updateEwayData(list);
		transportManagementDao.checkAndMarkCompleteDispatch(list);
		return true;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public VehicleDispatch getDispatchById(Integer dispatchId) {
		return transportManagementDao.getDispatchData(dispatchId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<VehicleDispatch> getVehicleDispatchHistory(Integer vehicleId, Date startDate, Date endDate) {
		List<VehicleDispatch> dispatchList = new ArrayList<>();
		List<VehicleDispatchData> list = transportManagementDao.getVehicleDispatchHistory(vehicleId, startDate,
				endDate, null);
		if (list != null && !list.isEmpty()) {
			for (VehicleDispatchData data : list) {
				dispatchList.add(SCMDataConverter.convert(data, scmCache, masterDataCache, false));
			}
		}
		return dispatchList;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public EWayBill fetchEwayBill(Integer ewayId) {
		EWayBill bill = null;
		EwayBillData eway = transportManagementDao.find(EwayBillData.class, ewayId);
		if (eway != null) {
			bill = SCMDataConverter.convert(eway, scmCache, masterDataCache, true);
		}
		return bill;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void markDispatchAsCompleted(Integer dispatchId) {
		VehicleDispatchData data = transportManagementDao.find(VehicleDispatchData.class, dispatchId);
		if (data != null) {
			// E Ways will not be in prepared status but in not required status
			transportManagementDao.updateConsignmentStatus(dispatchId, DispatchStatus.SETTLED);
			data.setStatus(DispatchStatus.SETTLED.name());
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public List<VehicleDispatch> searchVehicleDispatchForDate(Integer vehicleId, Date d, String registrationNumber) {
		List<VehicleDispatch> dispatchs = new ArrayList<>();
		List<VehicleDispatchData> list = transportManagementDao.getVehicleDispatchHistory(vehicleId, d, d, registrationNumber);
		if (list != null && !list.isEmpty()) {
			for (VehicleDispatchData vdd : list) {
				dispatchs.add(SCMDataConverter.convert(vdd, scmCache, masterDataCache, false));
			}
		}
		return dispatchs;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean cancelEWay(Integer ewayId) throws SumoException {
		return transportManagementDao.cancelEWay(ewayId);
	}

	@Override
	public Map<String, List<Integer>> getSuggestedUnits(Integer vehicleId, Date date) {
		Map<String,List<Integer>> result = new HashMap<>();
		result.put("From",new ArrayList<>());
		result.put("To",new ArrayList<>());
		try {
			Date beforeDate = AppUtils.getDayBeforeOrAfterDay(date, -14);
			List<Integer> toIds = transportManagementDao.getToListOfVehicle(vehicleId, beforeDate);
			LOG.info("Size of To Id's is : {}",toIds.size());
			if (toIds.size() > 0) {
				List<TransferOrderData> transferOrderDataList = transportManagementDao.getTODataByIds(toIds);
				if (transferOrderDataList.size() > 0) {
					for (TransferOrderData transferOrderData : transferOrderDataList) {
						List<Integer> from = result.get("From");
						List<Integer> to = result.get("To");
						if (!from.contains(transferOrderData.getGenerationUnitId())) {
							from.add(transferOrderData.getGenerationUnitId());
							result.put("From", from);
						}
						if (!to.contains(transferOrderData.getGeneratedForUnitId())) {
							to.add(transferOrderData.getGeneratedForUnitId());
							result.put("To", to);
						}
					}
				}
			}
		}
		catch (Exception e) {
			LOG.error("Error Occurred While getting suggested units ::: ",e);
		}
		return result;
	}
}
