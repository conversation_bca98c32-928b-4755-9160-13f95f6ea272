package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "REVERSE_BOOKING_CONSUMPTION")
public class ReverseBookingConsumptionData {

	private Integer id;
	private int skuId;
	private String skuName;
	private String unitOfMeasure;
	private BigDecimal calculatedQuantity;
	private BigDecimal unitPrice;
	private BigDecimal totalCost;
	private BigDecimal wastageQuantity;
	private ReverseProductionBookingData booking;
	private List<ReverseBookingConsumptionItemDrilldown> consumption = new ArrayList<ReverseBookingConsumptionItemDrilldown>(0);

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "BOOKING_CONSUMPTION_ID", nullable = false, unique = true)
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Column(name = "SKU_ID", nullable = false)
	public int getSkuId() {
		return skuId;
	}

	public void setSkuId(int skuId) {
		this.skuId = skuId;
	}

	@Column(name = "SKU_NAME", nullable = false)
	public String getSkuName() {
		return skuName;
	}

	public void setSkuName(String skuName) {
		this.skuName = skuName;
	}

	@Column(name = "UOM", nullable = false)
	public String getUnitOfMeasure() {
		return unitOfMeasure;
	}

	public void setUnitOfMeasure(String unitOfMeasure) {
		this.unitOfMeasure = unitOfMeasure;
	}

	@Column(name = "CALCULATED_QUANTITY", nullable = false)
	public BigDecimal getCalculatedQuantity() {
		return calculatedQuantity;
	}

	public void setCalculatedQuantity(BigDecimal calculatedQuantity) {
		this.calculatedQuantity = calculatedQuantity;
	}

	@Column(name = "UNIT_PRICE", nullable = true)
	public BigDecimal getUnitPrice() {
		return unitPrice;
	}

	public void setUnitPrice(BigDecimal unitPrice) {
		this.unitPrice = unitPrice;
	}

	@Column(name = "TOTAL_COST", nullable = true)
	public BigDecimal getTotalCost() {
		return totalCost;
	}

	public void setTotalCost(BigDecimal totalCost) {
		this.totalCost = totalCost;
	}

	@Column(name = "WASTAGE_QUANTITY", nullable = true)
	public BigDecimal getWastageQuantity() {
		return wastageQuantity;
	}

	public void setWastageQuantity(BigDecimal wastageQuantity) {
		this.wastageQuantity = wastageQuantity;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "PRODUCTION_BOOK_ID", nullable = false)
	public ReverseProductionBookingData getBooking() {
		return booking;
	}

	public void setBooking(ReverseProductionBookingData booking) {
		this.booking = booking;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "consumptionData")
	public List<ReverseBookingConsumptionItemDrilldown> getConsumption() {
		return consumption;
	}

	public void setConsumption(List<ReverseBookingConsumptionItemDrilldown> consumption) {
		this.consumption = consumption;
	}
}
