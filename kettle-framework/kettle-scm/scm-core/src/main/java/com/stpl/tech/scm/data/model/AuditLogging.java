/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;
// Generated 4 May, 2016 12:06:56 AM by Hibernate Tools 3.2.2.GA

import static javax.persistence.GenerationType.IDENTITY;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * AuditLogging generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "AUDIT_LOGGING")
public class AuditLogging implements java.io.Serializable {

	private Integer auditLoggingId;
	private String objectName;
	private String fieldName;
	private String actionType;
	private Date changedAt;
	private String changedBy;
	private String oldValue;
	private String newValue;
	private String description;

	public AuditLogging() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "AUDIT_LOGGING_ID", unique = true, nullable = false)
	public Integer getAuditLoggingId() {
		return this.auditLoggingId;
	}

	public void setAuditLoggingId(Integer attributeValueId) {
		this.auditLoggingId = attributeValueId;
	}

	@Column(name = "OBJECT_NAME", nullable = false, length = 200)
	public String getObjectName() {
		return this.objectName;
	}

	public void setObjectName(String attributeValue) {
		this.objectName = attributeValue;
	}

	@Column(name = "FIELD_NAME", length = 50)
	public String getFieldName() {
		return this.fieldName;
	}

	public void setFieldName(String attributeValueShortCode) {
		this.fieldName = attributeValueShortCode;
	}

	@Column(name = "ACTION_TYPE", nullable = false, length = 1)
	public String getActionType() {
		return this.actionType;
	}

	public void setActionType(String attributeValueStatus) {
		this.actionType = attributeValueStatus;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CHANGED_AT", nullable = true, length = 19)
	public Date getChangedAt() {
		return changedAt;
	}

	public void setChangedAt(Date changedAt) {
		this.changedAt = changedAt;
	}

	@Column(name = "CHANGED_BY", nullable = false, length = 50)
	public String getChangedBy() {
		return changedBy;
	}

	public void setChangedBy(String changedBy) {
		this.changedBy = changedBy;
	}

	@Column(name = "OLD_VALUE", nullable = false, length = 1000)
	public String getOldValue() {
		return oldValue;
	}

	public void setOldValue(String oldValue) {
		this.oldValue = oldValue;
	}

	@Column(name = "NEW_VALUE", nullable = false, length = 1000)
	public String getNewValue() {
		return newValue;
	}

	public void setNewValue(String newValue) {
		this.newValue = newValue;
	}

	@Column(name = "DESCRIPTION", nullable = false, length = 5000)
	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

}
