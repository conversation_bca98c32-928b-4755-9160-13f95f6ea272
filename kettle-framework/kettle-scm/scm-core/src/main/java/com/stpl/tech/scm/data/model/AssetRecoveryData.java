package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;


@AllArgsConstructor
@NoArgsConstructor
@Entity
@Table(name = "ASSET_RECOVERY_NEW")
public class AssetRecoveryData {

    private Integer recoveryId;
    private Integer assetId;
    private Integer eventId;
    private Integer unitId;
    private BigDecimal amountToRecover;
    private String recoveryStatus;
    private BigDecimal amountRecovered;
    private String employeeRecoveryStatus;
    private BigDecimal employeeRecoveryAmount;
    private String insuranceRecoveryStatus;
    private BigDecimal insuranceRecoveryAmount;
    private BigDecimal writeOffAmount;
    private Integer createdBy;
    private Date creationDate;

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "RECOVERY_ID", unique = true, nullable = false)
    public Integer getRecoveryId() {
        return recoveryId;
    }

    public void setRecoveryId(Integer recoveryId) {
        this.recoveryId = recoveryId;
    }

    @Column(name = "ASSET_ID", unique = true, nullable = false)
    public Integer getAssetId() {
        return assetId;
    }

    public void setAssetId(Integer assetId) {
        this.assetId = assetId;
    }

    @Column(name = "EVENT_ID", unique = true, nullable = false)
    public Integer getEventId() {
        return eventId;
    }

    public void setEventId(Integer eventId) {
        this.eventId = eventId;
    }

    @Column(name = "UNIT_ID", nullable = false)
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @Column(name = "AMOUNT_TO_RECOVER", nullable = false)
    public BigDecimal getAmountToRecover() {
        return amountToRecover;
    }

    public void setAmountToRecover(BigDecimal amountToRecover) {
        this.amountToRecover = amountToRecover;
    }

    @Column(name = "RECOVERY_STATUS", nullable = false)
    public String getRecoveryStatus() {
        return recoveryStatus;
    }

    public void setRecoveryStatus(String recoveryStatus) {
        this.recoveryStatus = recoveryStatus;
    }

    @Column(name = "AMOUNT_RECOVERED")
    public BigDecimal getAmountRecovered() {
        return amountRecovered;
    }

    public void setAmountRecovered(BigDecimal amountRecovered) {
        this.amountRecovered = amountRecovered;
    }

    @Column(name = "EMPLOYEE_RECOVERY_STATUS", nullable = false)
    public String getEmployeeRecoveryStatus() {
        return employeeRecoveryStatus;
    }

    public void setEmployeeRecoveryStatus(String employeeRecoveryStatus) {
        this.employeeRecoveryStatus = employeeRecoveryStatus;
    }

    @Column(name = "EMPLOYEE_RECOVERY_AMOUNT")
    public BigDecimal getEmployeeRecoveryAmount() {
        return employeeRecoveryAmount;
    }

    public void setEmployeeRecoveryAmount(BigDecimal employeeRecoveryAmount) {
        this.employeeRecoveryAmount = employeeRecoveryAmount;
    }

    @Column(name = "INSURANCE_RECOVERY_STATUS", nullable = false)
    public String getInsuranceRecoveryStatus() {
        return insuranceRecoveryStatus;
    }

    public void setInsuranceRecoveryStatus(String insuranceRecoveryStatus) {
        this.insuranceRecoveryStatus = insuranceRecoveryStatus;
    }

    @Column(name = "INSURANCE_RECOVERY_AMOUNT")
    public BigDecimal getInsuranceRecoveryAmount() {
        return insuranceRecoveryAmount;
    }

    public void setInsuranceRecoveryAmount(BigDecimal insuranceRecoveryAmount) {
        this.insuranceRecoveryAmount = insuranceRecoveryAmount;
    }

    @Column(name = "WRITE_OFF_AMOUNT")
    public BigDecimal getWriteOffAmount() {
        return writeOffAmount;
    }

    public void setWriteOffAmount(BigDecimal writeOffAmount) {
        this.writeOffAmount = writeOffAmount;
    }

    @Column(name = "CREATED_BY", nullable = false)
    public Integer getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "CREATION_DATE", nullable = false)
    public Date getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }
}
