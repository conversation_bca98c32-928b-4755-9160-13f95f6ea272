package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "APPROVAL_DETAIL_DATA")
public class ApprovalDetailData {


    private Integer approvalRequestId;
    private Integer eventId;
    private Integer assetId;
    private Integer skuId;
    private Integer unitId;
    private String type;
    private String status;
    private String skuName;
    private BigDecimal cost;
    private Integer requestedBy;
    private Integer requestedTo;
    private Date requestDate;
    private Integer approvedBy;
    private Date approvalDate;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "APPROVAL_REQUEST_ID", nullable = false, unique = true)
    public Integer getApprovalRequestId() {
        return approvalRequestId;
    }

    public void setApprovalRequestId(Integer approvalRequestId) {
        this.approvalRequestId = approvalRequestId;
    }

    @Column(name = "EVENT_ID", nullable = false)
    public Integer getEventId() {
        return eventId;
    }

    public void setEventId(Integer eventId) {
        this.eventId = eventId;
    }

    @Column(name = "ASSET_ID", nullable = false)
    public Integer getAssetId() {
        return assetId;
    }

    public void setAssetId(Integer assetId) {
        this.assetId = assetId;
    }

    @Column(name = "SKU_ID", nullable = false)
    public Integer getSkuId() {
        return skuId;
    }

    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }

    @Column(name = "UNIT_ID", nullable = false)
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @Column(name = "TYPE", nullable = false)
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Column(name = "STATUS", nullable = false)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "SKU_NAME", nullable = false)
    public String getSkuName() {
        return skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    @Column(name = "PROCUREMENT_COST", nullable = false)
    public BigDecimal getCost() {
        return cost;
    }

    public void setCost(BigDecimal cost) {
        this.cost = cost;
    }

    @Column(name = "REQUESTED_BY", nullable = false)
    public Integer getRequestedBy() {
        return requestedBy;
    }

    public void setRequestedBy(Integer requestedBy) {
        this.requestedBy = requestedBy;
    }

    @Column(name = "REQUESTED_TO", nullable = false)
    public Integer getRequestedTo() {
        return requestedTo;
    }

    public void setRequestedTo(Integer requestedTo) {
        this.requestedTo = requestedTo;
    }

    @Column(name = "REQUEST_DATE", nullable = false)
    public Date getRequestDate() {
        return requestDate;
    }

    public void setRequestDate(Date requestDate) {
        this.requestDate = requestDate;
    }

    @Column(name = "APPROVED_BY")
    public Integer getApprovedBy() {
        return approvedBy;
    }

    public void setApprovedBy(Integer approvedBy) {
        this.approvedBy = approvedBy;
    }

    @Column(name = "APPROVAL_DATE")
    public Date getApprovalDate() {
        return approvalDate;
    }

    public void setApprovalDate(Date approvalDate) {
        this.approvalDate = approvalDate;
    }
}
