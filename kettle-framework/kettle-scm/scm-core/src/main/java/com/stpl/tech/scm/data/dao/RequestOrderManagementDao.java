package com.stpl.tech.scm.data.dao;

import java.math.BigDecimal;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.stpl.tech.scm.data.model.FulfillmentUnitMappingData;
import com.stpl.tech.scm.data.model.PlanOrderItemPrepData;
import com.stpl.tech.scm.data.model.RequestOrderData;
import com.stpl.tech.scm.data.model.RequestOrderItemData;
import com.stpl.tech.scm.domain.model.FulfillmentType;
import com.stpl.tech.scm.domain.model.NotificationType;
import com.stpl.tech.scm.domain.model.RequestOrderItem;
import com.stpl.tech.scm.domain.model.SCMOrderStatus;

/**
 * Created by <PERSON><PERSON> on 13-06-2016.
 */
public interface RequestOrderManagementDao extends SCMAbstractDao {

    public List<RequestOrderData> getRequestOrders(Integer fulfillingUnitId, Integer requestingUnitId, Date startDate, Date endDate, SCMOrderStatus status, Integer requestOrderId, String searchTag);

    public List<RequestOrderData> getRequestOrdersFromReferenceOrder(int referenceOrderId);

    public FulfillmentUnitMappingData getFulFillmentUnitByType(FulfillmentType fulfillmentType, int requestUnitId);

    public List<RequestOrderData> getPendingRequestOrders(Integer unitId);

    public List<RequestOrderData> getPendingRequestOrders(String date, int fulfillmentUnit);

    public List<RequestOrderData> getPendingAcknowledgedRequestOrders(Date date, int fulfillmentUnit);

    public List<RequestOrderData> getSpecializedROForFulfillmentDate(Date fulfillmentDate, boolean isReceiving);

    public List<RequestOrderData> getSpecialOrdersForDate(Date date);

    public List<PlanOrderItemPrepData> findPlanOrderItemPrepByPlanOrderItemId(Integer itemId);

    public List<RequestOrderData> getRequestOrdersForInvoice(Integer sendingUnit, Integer receivingUnit);

	public List<RequestOrderData> getSpecializedROForNotification(NotificationType type, Date startTime, Date endTime);

	public void markRequestOrderNotified(List<Integer> requestOrdersNotified);

    void setTaxDetail(int stateId, RequestOrderItemData detail, RequestOrderItem item);

	public boolean isDifferentCompany(Integer requestOrderId);

    public List<RequestOrderData> getRequestOrdersByIds(Integer fulfillingUnitId, Date startDate, Date endDate, SCMOrderStatus status, List<Integer> requestOrderIds);

    public List<Integer> getALlReceivingUnits(FulfillmentType fulfillmentType, int fulfillingUnit);

    public RequestOrderData getLastAdhocF9Order(RequestOrderData requestOrderData);

    public List<RequestOrderData> getLastWeekOrders(Integer unitId,Boolean isSpecial);

    public BigDecimal getLastTransferPriceBySku(Integer fulfullingUnitId, Integer skuId);
}
