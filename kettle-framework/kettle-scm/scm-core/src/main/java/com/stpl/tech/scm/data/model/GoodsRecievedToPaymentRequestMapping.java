package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
        import javax.persistence.Entity;
        import javax.persistence.FetchType;
        import javax.persistence.GeneratedValue;
        import javax.persistence.GenerationType;
        import javax.persistence.Id;
        import javax.persistence.JoinColumn;
        import javax.persistence.ManyToOne;
        import javax.persistence.OneToOne;
        import javax.persistence.Table;
        import javax.persistence.UniqueConstraint;
        import java.math.BigDecimal;
        import java.util.Date;

@Entity
@Table(name = "GOODS_RECIEVED_TO_PAYMENT_REQUEST_MAPPING")
public class GoodsRecievedToPaymentRequestMapping {

        private Integer id;
        private Integer paymentRequestId;
        private Integer goodsRecievedId;
        private String currentStatus;
        private Date lastUpdated;


        @Id
        @GeneratedValue(strategy = GenerationType.IDENTITY)
        @Column(name = "MAPPING_ID", unique = true, nullable = false)
        public Integer getId() {
                return id;
        }

        public void setId(Integer id) {
                this.id = id;
        }

        @Column(name = "PAYMENT_REQUEST_ID")
        public Integer getPaymentRequestId() {
                return paymentRequestId;
        }

        public void setPaymentRequestId(Integer paymentRequestId) {
                this.paymentRequestId = paymentRequestId;
        }

        @Column(name = "GOODS_RECIEVED_ID")
        public Integer getGoodsRecievedId() {
                return goodsRecievedId;
        }

        public void setGoodsRecievedId(Integer goodsRecievedId) {
                this.goodsRecievedId = goodsRecievedId;
        }

        @Column(name = "MAPPING_STATUS")
        public String getCurrentStatus() {
                return currentStatus;
        }

        public void setCurrentStatus(String currentStatus) {
                this.currentStatus = currentStatus;
        }

        @Column(name = "UPDATE_TIME")
        public Date getLastUpdated() {
                return lastUpdated;
        }

        public void setLastUpdated(Date lastUpdated) {
                this.lastUpdated = lastUpdated;
        }
}