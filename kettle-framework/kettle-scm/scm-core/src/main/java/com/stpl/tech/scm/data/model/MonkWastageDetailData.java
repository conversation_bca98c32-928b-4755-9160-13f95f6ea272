package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;

import static javax.persistence.GenerationType.IDENTITY;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 25-01-2019.
 */
@Entity
@Table(name = "MONK_WASTAGE_DETAIL_DATA")
public class MonkWastageDetailData {

    private Integer id;
    private Integer wastageData;
    private Integer taskId;
    private Integer orderId;
    private BigDecimal quantity;
    private String monkEvent;
    private String chaiMonk;

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "MONK_WASTAGE_DETAIL_DATA_ID", unique = true, nullable = false)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "WASTAGE_ITEM_ID")
    public Integer getWastageData() {
        return wastageData;
    }

    public void setWastageData(Integer wastageData) {
        this.wastageData = wastageData;
    }

    @Column(name = "TASK_ID")
    public Integer getTaskId() {
        return taskId;
    }

    public void setTaskId(Integer taskId) {
        this.taskId = taskId;
    }

    @Column(name = "ORDER_ID")
    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    @Column(name = "QUANTITY")
    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    @Column(name = "MONK_EVENT")
    public String getMonkEvent() {
        return monkEvent;
    }

    public void setMonkEvent(String monkEvent) {
        this.monkEvent = monkEvent;
    }

    @Column(name = "CHAI_MONK")
    public String getChaiMonk() {
        return chaiMonk;
    }

    public void setChaiMonk(String chaiMonk) {
        this.chaiMonk = chaiMonk;
    }
}
