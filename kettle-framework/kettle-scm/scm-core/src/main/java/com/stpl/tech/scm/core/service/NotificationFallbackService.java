package com.stpl.tech.scm.core.service;

import java.util.Date;
import java.util.List;

import com.stpl.tech.scm.domain.model.NotificationType;

/**
 * Created by Chaayos on 22-09-2016.
 */
public interface NotificationFallbackService {

    public void sendVendorRONotification(List<Integer> vendorIds, Date fulfillmentDate, boolean isMilk, NotificationType type);

    public void sendVendorGRNotification(List<Integer> vendorIds, Date fulfillmentDate, boolean isMilk, NotificationType type);
}
