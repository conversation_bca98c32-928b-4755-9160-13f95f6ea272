package com.stpl.tech.scm.notification.email.template;

import com.stpl.tech.util.notification.AbstractVelocityTemplate;

import java.util.HashMap;
import java.util.Map;

public class TDSEmailTemplate extends AbstractVelocityTemplate {

    private String basePath;
    private String vendorName;
    private String quarter;
    private String financialYear;

    public TDSEmailTemplate(String basePath, String vendorName, String quarter, String financialYear) {
        this.basePath = basePath;
        this.vendorName = vendorName;
        this.quarter = quarter;
        this.financialYear = financialYear;
    }

    @Override
    public String getTemplatePath() {
        return "templates/TDSEmailTemplate.html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/" + "generatedTDSMailTemplate.html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> injectToTemplateMap = new HashMap<>();
        injectToTemplateMap.put("vendorName", vendorName);
        injectToTemplateMap.put("quarter", quarter);
        injectToTemplateMap.put("financialYear", financialYear);
        return injectToTemplateMap;
    }
}
