package com.stpl.tech.scm.notification.email;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.model.ReferenceOrderData;
import com.stpl.tech.scm.data.model.RequestOrderData;
import com.stpl.tech.scm.notification.email.template.CafeRoEmailNotificationTemplate;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

import java.util.List;
import java.util.Objects;

/**
 * Created by Chaayos on 05-09-2016.
 */
public class CafeROEmailNotification extends EmailNotification {

    private CafeRoEmailNotificationTemplate cafeRoEmailNotificationTemplate;
    private EnvType envType;
    private List<String> emails;
    private ReferenceOrderData referenceOrderData;
    private RequestOrderData requestOrderData;

    public CafeROEmailNotification() {

    }

    public CafeROEmailNotification(CafeRoEmailNotificationTemplate cafeRoEmailNotificationTemplate, EnvType envType,
                                   List<String> emails, ReferenceOrderData referenceOrderData, RequestOrderData requestOrderData) {
        this.cafeRoEmailNotificationTemplate = cafeRoEmailNotificationTemplate;
        this.envType = envType;
        this.emails = emails;
        this.referenceOrderData = referenceOrderData;
        this.requestOrderData = requestOrderData;
    }

    @Override
    public String[] getToEmails() {
        emails.add("<EMAIL>");
        if ((Objects.nonNull(referenceOrderData) && referenceOrderData.getRefOrderSource().equalsIgnoreCase("FOUNTAIN9_DATA_SOURCE_ORDERING"))
                || Objects.nonNull(requestOrderData)) {
            emails.add("<EMAIL>");
            emails.add("<EMAIL>");
            emails.add("<EMAIL>");
        }
        String[] simpleArray = new String[emails.size()];
        return SCMUtil.isDev(envType) ? new String[]{"<EMAIL>"} : emails.toArray(simpleArray);
    }

    @Override
    public String getFromEmail() {
        return "<EMAIL>";
    }

    @Override
    public String subject() {
        String subject;
        if (Objects.nonNull(requestOrderData)) {
            subject = "Adhoc Request Order from " + cafeRoEmailNotificationTemplate.getUnit().getName() + " for "
                    + SCMUtil.getFormattedTime(SCMUtil.getCurrentDateIST(), "EEE dd MMM yyyy");
        }
        else if (Objects.nonNull(referenceOrderData) && referenceOrderData.getRefOrderSource().equalsIgnoreCase("FOUNTAIN9_DATA_SOURCE_ORDERING")) {
            subject = "Fountain 9 Request Orders from " + cafeRoEmailNotificationTemplate.getUnit().getName() + " for "
                    + SCMUtil.getFormattedTime(SCMUtil.getCurrentDateIST(), "EEE dd MMM yyyy");
        }
        else {
            subject = "Request Orders Raise From Warehouse/Kitchen for "
                    + SCMUtil.getFormattedTime(SCMUtil.getCurrentDateIST(), "EEE dd MMM yyyy");
        }
        if (SCMUtil.isDev(envType)) {
            subject = "Dev " + subject;
        }
        return subject;
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            return cafeRoEmailNotificationTemplate.getContent();
        } catch (TemplateRenderingException e) {
            throw new EmailGenerationException("Failed to render the template", e);
        }
    }

    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }
}

