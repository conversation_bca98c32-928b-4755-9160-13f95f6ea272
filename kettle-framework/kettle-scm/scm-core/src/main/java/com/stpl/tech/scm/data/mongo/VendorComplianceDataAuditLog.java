/*
 * Created By Shanmukh
 */

package com.stpl.tech.scm.data.mongo;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "VendorComplianceDataAuditLog")
public class VendorComplianceDataAuditLog {

    @Id
    private String id;

    @SerializedName("data")
    private VendorComplianceResponseData data;
    private Date currentTime;
    private String currentTimeIST;
    private Integer VendorComplianceDataAuditLogId;
    private Integer year;
    private String complianceType;
    private Integer month;
    private String complianceKey;
    private String financialYear;
    @SerializedName("code")
    private Integer code;
    @SerializedName("timestamp")
    private long timestamp;
    @SerializedName("transaction_id")
    private String transactionId;
    @SerializedName("message")
    private String message;
}
