package com.stpl.tech.scm.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;

@Entity
@Table(name = "COST_CENTER_DATA")
public class CostCenterData implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 2565093697355543375L;

	private Integer costCenterId;
	private String costCenterName;
	private String description;
	private String shortCode;
	private Integer owner;
	private String costCenterStatus;
	private String costCenterEmail;

	public CostCenterData() {
		// TODO Auto-generated constructor stub
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "COST_CENTER_ID")
	public Integer getCostCenterId() {
		return costCenterId;
	}

	public void setCostCenterId(Integer costCenterId) {
		this.costCenterId = costCenterId;
	}

	@Column(name = "COST_CENTER_NAME")
	public String getCostCenterName() {
		return costCenterName;
	}

	public void setCostCenterName(String costCenterName) {
		this.costCenterName = costCenterName;
	}

	@Column(name = "COST_CENTER_DESCRIPTION")
	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	@Column(name = "SHORT_CODE")
	public String getShortCode() {
		return shortCode;
	}

	public void setShortCode(String shortCode) {
		this.shortCode = shortCode;
	}

	@Column(name = "OWNER_ID")
	public Integer getOwner() {
		return owner;
	}

	public void setOwner(Integer owner) {
		this.owner = owner;
	}

	@Column(name = "COST_CENTER_STATUS")
	public String getCostCenterStatus() {
		return costCenterStatus;
	}

	public void setCostCenterStatus(String costCenterStatus) {
		this.costCenterStatus = costCenterStatus;
	}


	@Column(name="COST_CENTER_EMAIL")
	public String getCostCenterEmail(){
		return this.costCenterEmail;
	}

	public void setCostCenterEmail(String costCenterEmail) {
		this.costCenterEmail = costCenterEmail;
	}

}

