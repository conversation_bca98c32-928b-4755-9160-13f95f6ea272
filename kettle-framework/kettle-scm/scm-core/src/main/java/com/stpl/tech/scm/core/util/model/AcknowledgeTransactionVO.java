package com.stpl.tech.scm.core.util.model;
import java.util.List;
import com.stpl.tech.scm.domain.model.DayCloseTxnItem;

public class AcknowledgeTransactionVO {

    private List<DayCloseTxnItem> list;
    private List<Integer> events;

    public AcknowledgeTransactionVO() {
    }

    public AcknowledgeTransactionVO(List<DayCloseTxnItem> list, List<Integer> events) {
        super();
        this.list = list;
        this.events = events;
    }

    public List<DayCloseTxnItem> getList() {
        return list;
    }

    public void setList(List<DayCloseTxnItem> list) {
        this.list = list;
    }

    public List<Integer> getEvents() {
        return events;
    }

    public void setEvents(List<Integer> events) {
        this.events = events;
    }
}