package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "ADVANCE_PAYMENT_DATA")
public class AdvancePaymentData {

    private Integer advancePaymentId;
    private PaymentRequestData paymentRequestId;
    private String advanceType;
    private String advanceStatus;
    private Integer vendorId;
    private BigDecimal prAmount;
    private BigDecimal availableAmount;
    private BigDecimal blockedAmount;
    private PurchaseOrderData purchaseOrderData;
    private ServiceOrderData serviceOrderData;
    private String lastPoSoStatus;
    private String createdBy;
    private Date createdAt;
    private String rejectedFor;
    private List<AdvancePaymentAuditLogData> advancePaymentAuditLogDataList;
    private List<PaymentRequestData> paymentRequestDataList;
    private Integer adjustedPoSo;
    private Date refundDate;
    private Integer refundInitiatedBy;
    private Date refundReceivedDate;
    private Integer lastUpdatedBy;
    private Date lastUpdatedDate;
    private List<LinkedPaymentsForAdvance> linkedPaymentsForAdvance;
    private AdvancePaymentData childAdvance;
    private Date maxSettlementTime;
    private List<AdvancePaymentStatusLog> advancePaymentStatusLogs;
    private Integer advanceRefundDocument;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ADVANCE_PAYMENT_ID", nullable = false, unique = true)
    public Integer getAdvancePaymentId() {
        return advancePaymentId;
    }

    public void setAdvancePaymentId(Integer advancePaymentId) {
        this.advancePaymentId = advancePaymentId;
    }

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PR_ID")
    public PaymentRequestData getPaymentRequestId() {
        return paymentRequestId;
    }

    public void setPaymentRequestId(PaymentRequestData paymentRequestId) {
        this.paymentRequestId = paymentRequestId;
    }

    @Column(name = "ADVANCE_TYPE", nullable = false)
    public String getAdvanceType() {
        return advanceType;
    }

    public void setAdvanceType(String advanceType) {
        this.advanceType = advanceType;
    }

    @Column(name = "ADVANCE_STATUS", nullable = false)
    public String getAdvanceStatus() {
        return advanceStatus;
    }

    public void setAdvanceStatus(String advanceStatus) {
        this.advanceStatus = advanceStatus;
    }

    @Column(name = "VENDOR_ID", nullable = false)
    public Integer getVendorId() {
        return vendorId;
    }

    public void setVendorId(Integer vendorId) {
        this.vendorId = vendorId;
    }

    @Column(name = "PR_AMOUNT", nullable = false)
    public BigDecimal getPrAmount() {
        return prAmount;
    }

    public void setPrAmount(BigDecimal prAmount) {
        this.prAmount = prAmount;
    }

    @Column(name = "AVAILABLE_AMOUNT", nullable = false)
    public BigDecimal getAvailableAmount() {
        return availableAmount;
    }

    public void setAvailableAmount(BigDecimal availableAmount) {
        this.availableAmount = availableAmount;
    }

    @Column(name = "BLOCKED_AMOUNT", nullable = false)
    public BigDecimal getBlockedAmount() {
        return blockedAmount;
    }

    public void setBlockedAmount(BigDecimal blockedAmount) {
        this.blockedAmount = blockedAmount;
    }


    @ManyToOne(fetch = FetchType.LAZY, targetEntity = PurchaseOrderData.class)
    @JoinColumn(name = "PURCHASE_ORDER_ID")
    public PurchaseOrderData getPurchaseOrderData() {
        return purchaseOrderData;
    }

    public void setPurchaseOrderData(PurchaseOrderData purchaseOrderData) {
        this.purchaseOrderData = purchaseOrderData;
    }

    @ManyToOne(fetch = FetchType.LAZY, targetEntity = ServiceOrderData.class)
    @JoinColumn(name = "SERVICE_ORDER_ID")
    public ServiceOrderData getServiceOrderData() {
        return serviceOrderData;
    }

    public void setServiceOrderData(ServiceOrderData serviceOrderData) {
        this.serviceOrderData = serviceOrderData;
    }

    @Column(name = "LAST_PO_SO_STATUS", nullable = true)
    public String getLastPoSoStatus() {
        return lastPoSoStatus;
    }

    public void setLastPoSoStatus(String lastPoSoStatus) {
        this.lastPoSoStatus = lastPoSoStatus;
    }

    @Column(name = "CREATED_BY", nullable = false)
    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "CREATED_AT", nullable = true)
    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    @Column(name = "REJECTED_FOR", nullable = true)
    public String getRejectedFor() {
        return rejectedFor;
    }

    public void setRejectedFor(String rejectedFor) {
        this.rejectedFor = rejectedFor;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "advancePaymentId")
    public List<AdvancePaymentAuditLogData> getAdvancePaymentAuditLogDataList() {
        return advancePaymentAuditLogDataList;
    }

    public void setAdvancePaymentAuditLogDataList(List<AdvancePaymentAuditLogData> advancePaymentAuditLogDataList) {
        this.advancePaymentAuditLogDataList = advancePaymentAuditLogDataList;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "advancePaymentData")
    public List<PaymentRequestData> getPaymentRequestDataList() {
        return paymentRequestDataList;
    }

    public void setPaymentRequestDataList(List<PaymentRequestData> paymentRequestDataList) {
        this.paymentRequestDataList = paymentRequestDataList;
    }

    @Column(name = "ADJUSTED_PO_SO", nullable = true)
    public Integer getAdjustedPoSo() {
        return adjustedPoSo;
    }

    public void setAdjustedPoSo(Integer adjustedPoSo) {
        this.adjustedPoSo = adjustedPoSo;
    }

    @Column(name = "REFUND_DATE", nullable = true)
    public Date getRefundDate() {
        return refundDate;
    }

    public void setRefundDate(Date refundDate) {
        this.refundDate = refundDate;
    }

    @Column(name = "REFUND_INITIATED_BY", nullable = true)
    public Integer getRefundInitiatedBy() {
        return refundInitiatedBy;
    }

    public void setRefundInitiatedBy(Integer refundInitiatedBy) {
        this.refundInitiatedBy = refundInitiatedBy;
    }

    @Column(name = "REFUND_RECEIVED_DATE", nullable = true)
    public Date getRefundReceivedDate() {
        return refundReceivedDate;
    }

    public void setRefundReceivedDate(Date refundReceivedDate) {
        this.refundReceivedDate = refundReceivedDate;
    }

    @Column(name = "LAST_UPDATED_BY", nullable = true)
    public Integer getLastUpdatedBy() {
        return lastUpdatedBy;
    }

    public void setLastUpdatedBy(Integer lastUpdatedBy) {
        this.lastUpdatedBy = lastUpdatedBy;
    }

    @Column(name = "LAST_UPDATED_DATE", nullable = true)
    public Date getLastUpdatedDate() {
        return lastUpdatedDate;
    }

    public void setLastUpdatedDate(Date lastUpdatedDate) {
        this.lastUpdatedDate = lastUpdatedDate;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "advancePaymentData")
    public List<LinkedPaymentsForAdvance> getLinkedPaymentsForAdvance() {
        return linkedPaymentsForAdvance;
    }

    public void setLinkedPaymentsForAdvance(List<LinkedPaymentsForAdvance> linkedPaymentsForAdvance) {
        this.linkedPaymentsForAdvance = linkedPaymentsForAdvance;
    }

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CHILD_ADVANCE_ID", nullable = true)
    public AdvancePaymentData getChildAdvance() {
        return childAdvance;
    }

    public void setChildAdvance(AdvancePaymentData childAdvance) {
        this.childAdvance = childAdvance;
    }

    @Column(name = "MAX_SETTLEMENT_TIME", nullable = true)
    public Date getMaxSettlementTime() {
        return maxSettlementTime;
    }

    public void setMaxSettlementTime(Date maxSettlementTime) {
        this.maxSettlementTime = maxSettlementTime;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "advancePaymentData")
    public List<AdvancePaymentStatusLog> getAdvancePaymentStatusLogs() {
        return advancePaymentStatusLogs;
    }

    public void setAdvancePaymentStatusLogs(List<AdvancePaymentStatusLog> advancePaymentStatusLogs) {
        this.advancePaymentStatusLogs = advancePaymentStatusLogs;
    }

    @Column(name = "ADVANCE_REFUND_DOCUMENT", nullable = true)
    public Integer getAdvanceRefundDocument() {
        return advanceRefundDocument;
    }

    public void setAdvanceRefundDocument(Integer advanceRefundDocument) {
        this.advanceRefundDocument = advanceRefundDocument;
    }
}
