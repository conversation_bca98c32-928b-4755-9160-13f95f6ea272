/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;
// Generated 4 May, 2016 12:06:56 AM by Hibernate Tools 3.2.2.GA

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static javax.persistence.GenerationType.IDENTITY;

/**
 * ProductDefinitionData generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "PROFILE_DEFINITION", uniqueConstraints = @UniqueConstraint(columnNames = {"PROFILE_NAME"}))
public class ProfileDefinitionData implements java.io.Serializable {

    private Integer profileId;
    private String profileName;
    private String profileDescription;
    private Date creationDate;
    private int createdBy;
    private String profileCode;
    private String profileStatus;
    private String uniqueNumberAvailable;
    private String uniqueFieldName;

    public ProfileDefinitionData() {
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "PROFILE_ID", unique = true, nullable = false)
    public Integer getProfileId() {
        return profileId;
    }

    public void setProfileId(Integer profileId) {
        this.profileId = profileId;
    }

    @Column(name = "PROFILE_NAME", unique = true, nullable = false)
    public String getProfileName() {
        return profileName;
    }


    public void setProfileName(String profileName) {
        this.profileName = profileName;
    }

    @Column(name = "PROFILE_DESCRIPTION", length = 1000)
    public String getProfileDescription() {
        return profileDescription;
    }

    public void setProfileDescription(String profileDescription) {
        this.profileDescription = profileDescription;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATION_DATE", length = 19)
    public Date getCreationDate() {
        return this.creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    @Column(name = "CREATED_BY", nullable = false)
    public int getCreatedBy() {
        return this.createdBy;
    }

    public void setCreatedBy(int createdBy) {
        this.createdBy = createdBy;
    }

    @Column(name = "PROFILE_CODE", nullable = true, length = 30)
    public String getProfileCode() {
        return profileCode;
    }

    public void setProfileCode(String profileCode) {
        this.profileCode = profileCode;
    }

    @Column(name = "PROFILE_STATUS", nullable = true, length = 30)
    public String getProfileStatus() {
        return profileStatus;
    }

    public void setProfileStatus(String profileStatus) {
        this.profileStatus = profileStatus;
    }

    @Column(name = "UNIQUE_NUMBER_AVAILABLE", nullable = true, length = 10)
    public String getUniqueNumberAvailable() {
        return uniqueNumberAvailable;
    }

    public void setUniqueNumberAvailable(String uniqueNumberAvailable) {
        this.uniqueNumberAvailable = uniqueNumberAvailable;
    }

    @Column(name = "UNIQUE_FIELD_NAME", nullable = true, length = 50)
    public String getUniqueFieldName() {
        return uniqueFieldName;
    }

    public void setUniqueFieldName(String uniqueFieldName) {
        this.uniqueFieldName = uniqueFieldName;
    }
}