/*
 * Created By Shanmukh
 */

package com.stpl.tech.scm.data.mongo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class BusinessAddress {

    @SerializedName("addr")
    private BusinessAddressFields businessAddressFields;

    @SerializedName("ntr")
    private String natureOfPlaceOfBusiness;
}
