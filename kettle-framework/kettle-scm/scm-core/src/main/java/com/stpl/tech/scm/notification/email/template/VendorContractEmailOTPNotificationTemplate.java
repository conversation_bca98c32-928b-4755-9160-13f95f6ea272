package com.stpl.tech.scm.notification.email.template;

import com.stpl.tech.scm.data.model.VendorContractInfo;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

@NoArgsConstructor
@AllArgsConstructor
public class VendorContractEmailOTPNotificationTemplate extends AbstractVelocityTemplate {

    private VendorDetail vendor;
    private String otp;
    private String basePath;

    @Override
    public String getTemplatePath() {
        return "templates/VendorContractEmailOTPTemplate.html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/vendor-contract/" + AppUtils.getCurrentTimeISTStringWithNoColons() + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("vendor", vendor);
        stringObjectMap.put("otp", otp);
        return stringObjectMap;
    }

}
