package com.stpl.tech.scm.core.util.model;

import java.math.BigDecimal;

public class BookingConsumptionDataVO {

	private int skuId;
	private String skuName;
	private String unitOfMeasure;
	private BigDecimal calculatedQuantity;
	private BigDecimal unitPrice;
	private BigDecimal totalCost;

	public int getSkuId() {
		return skuId;
	}

	public void setSkuId(int skuId) {
		this.skuId = skuId;
	}

	public String getSkuName() {
		return skuName;
	}

	public void setSkuName(String skuName) {
		this.skuName = skuName;
	}

	public String getUnitOfMeasure() {
		return unitOfMeasure;
	}

	public void setUnitOfMeasure(String unitOfMeasure) {
		this.unitOfMeasure = unitOfMeasure;
	}

	public BigDecimal getCalculatedQuantity() {
		return calculatedQuantity;
	}

	public void setCalculatedQuantity(BigDecimal calculatedQuantity) {
		this.calculatedQuantity = calculatedQuantity;
	}

	public BigDecimal getUnitPrice() {
		return unitPrice;
	}

	public void setUnitPrice(BigDecimal unitPrice) {
		this.unitPrice = unitPrice;
	}

	public BigDecimal getTotalCost() {
		return totalCost;
	}

	public void setTotalCost(BigDecimal totalCost) {
		this.totalCost = totalCost;
	}

}
