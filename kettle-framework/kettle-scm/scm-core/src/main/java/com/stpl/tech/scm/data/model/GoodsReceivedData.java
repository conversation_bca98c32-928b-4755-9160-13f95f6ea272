package com.stpl.tech.scm.data.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;

/**
 * Created by <PERSON><PERSON> on 11-06-2016.
 */
@Entity
@Table(name = "GOODS_RECEIVED")
public class GoodsReceivedData {

    private Integer id;
    private Date generationTime;
    private Date lastUpdateTime;
    private int generationUnitId;
    private int generatedForUnitId;
    private int sourceCompanyId;
    private int receivingCompanyId;
    private int generatedBy;
    private Integer receivedBy;
    private Integer cancelledBy;
    private String status;
    private String comment;
    private BigDecimal totalAmount;
    private String autoGenerated = "N";
    private GoodsReceivedData parentGR;
    private RequestOrderData requestOrderData;
    //private PurchaseOrderData purchaseOrderData;
    private TransferOrderData transferOrderData;
    private List<GoodsReceivedItemData> goodsReceivedItemDatas = new ArrayList<GoodsReceivedItemData>();
    private Integer closureEventId;
    private String isRejectedGR="N";
    private String parentGRComment;
    private String rejectGRComment;
    private String transferOrderType;
    private Integer invoiceId;
    private String docIdsPorImages;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "GOODS_RECEIVED_ID", nullable = false, unique = true)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "GENERATION_TIME", nullable = false)
    public Date getGenerationTime() {
        return generationTime;
    }

    public void setGenerationTime(Date generationTime) {
        this.generationTime = generationTime;
    }

    @Column(name = "GENERATION_UNIT_ID", nullable = false)
    public int getGenerationUnitId() {
        return generationUnitId;
    }

    public void setGenerationUnitId(int generationUnitId) {
        this.generationUnitId = generationUnitId;
    }

    @Column(name = "GENERATED_FOR_UNIT_ID", nullable = false)
    public int getGeneratedForUnitId() {
        return generatedForUnitId;
    }

    public void setGeneratedForUnitId(int generatedForUnitId) {
        this.generatedForUnitId = generatedForUnitId;
    }

    @Column(name = "GENERATED_BY", nullable = false)
    public int getGeneratedBy() {
        return generatedBy;
    }

    public void setGeneratedBy(int generatedBy) {
        this.generatedBy = generatedBy;
    }

    @Column(name = "RECEIVED_BY", nullable = true)
    public Integer getReceivedBy() {
        return receivedBy;
    }

    public void setReceivedBy(Integer receivedBy) {
        this.receivedBy = receivedBy;
    }

    @Column(name = "GOODS_RECEIVED_STATUS", nullable = false)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "COMMENT", nullable = true, length = 1000)
    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "REQUEST_ORDER_ID", nullable = true)
    public RequestOrderData getRequestOrderData() {
        return requestOrderData;
    }

    public void setRequestOrderData(RequestOrderData requestOrderData) {
        this.requestOrderData = requestOrderData;
    }

    /*@OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "PURCHASE_ORDER_ID", nullable = true)
    public PurchaseOrderData getPurchaseOrderData() {
        return purchaseOrderData;
    }

    public void setPurchaseOrderData(PurchaseOrderData purchaseOrderData) {
        this.purchaseOrderData = purchaseOrderData;
    }*/

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "TRANSFER_ORDER_ID", nullable = true)
    public TransferOrderData getTransferOrderData() {
        return transferOrderData;
    }

    public void setTransferOrderData(TransferOrderData transferOrderData) {
        this.transferOrderData = transferOrderData;
    }

    @Column(name = "LAST_UPDATE_TIME", nullable = false)
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    public void setLastUpdateTime(Date lastUpdateTime) {
        this.lastUpdateTime = lastUpdateTime;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "goodsReceivedData", cascade = CascadeType.PERSIST)
    public List<GoodsReceivedItemData> getGoodsReceivedItemDatas() {
        return goodsReceivedItemDatas;
    }

    public void setGoodsReceivedItemDatas(List<GoodsReceivedItemData> goodsReceivedItemDatas) {
        this.goodsReceivedItemDatas = goodsReceivedItemDatas;
    }

    @Column(name = "TOTAL_AMOUNT", nullable = true)
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }


    @Column(name = "CANCELLED_BY", nullable = true)
    public Integer getCancelledBy() {
        return cancelledBy;
    }

    public void setCancelledBy(Integer cancelledBy) {
        this.cancelledBy = cancelledBy;
    }

    @Column(name = "IS_AUTO_GENERATED", nullable = true)
	public String isAutoGenerated() {
		return autoGenerated;
	}

	public void setAutoGenerated(String autoGenerated) {
		this.autoGenerated = autoGenerated;
	}

	@OneToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "PARENT_GR", nullable = true)
	public GoodsReceivedData getParentGR() {
		return parentGR;
	}

	public void setParentGR(GoodsReceivedData parentGR) {
		this.parentGR = parentGR;
	}


    @Column(name="CLOSURE_EVENT_ID",nullable = true)
    public Integer getClosureEventId() {
        return closureEventId;
    }

    public void setClosureEventId(Integer closureEventId) {
        this.closureEventId = closureEventId;
    }

    @Column(name="IS_REJECTED_GR",nullable = true)
    public String getIsRejectedGR() {
		return isRejectedGR;
	}

	public void setIsRejectedGR(String isRejectedGR) {
		this.isRejectedGR = isRejectedGR;
	}

	@Column(name="REJECT_GR_COMMENT",nullable = true,length = 1000)
	public String getRejectGRComment() {
		return rejectGRComment;
	}

	public void setRejectGRComment(String rejectGRComment) {
		this.rejectGRComment = rejectGRComment;
	}

	@Column(name="PARENT_GR_COMMENT",nullable = true,length = 1000)
	public String getParentGRComment() {
		return parentGRComment;
	}

	public void setParentGRComment(String parentGRComment) {
		this.parentGRComment = parentGRComment;
	}

	@Column(name="SOURCE_COMPANY_ID",nullable = false)
	public int getSourceCompanyId() {
		return sourceCompanyId;
	}

	public void setSourceCompanyId(int sourceCompanyId) {
		this.sourceCompanyId = sourceCompanyId;
	}

	@Column(name="RECEIVING_COMPANY_ID",nullable = false)
	public int getReceivingCompanyId() {
		return receivingCompanyId;
	}

	public void setReceivingCompanyId(int receivingCompanyId) {
		this.receivingCompanyId = receivingCompanyId;
	}

    @Column(name="TO_TYPE",nullable = true)
    public String getTransferOrderType() {
        return transferOrderType;
    }

    public void setTransferOrderType(String transferOrderType) {
        this.transferOrderType = transferOrderType;
    }

    @Column(name="INVOICE_ID",nullable = true)
    public Integer getInvoiceId() {
        return invoiceId;
    }

    public void setInvoiceId(Integer invoiceId) {
        this.invoiceId = invoiceId;
    }

    @Column(name="POR_IMAGES_DOC_IDS",nullable = true)
    public String getDocIdsPorImages() {
        return docIdsPorImages;
    }

    public void setDocIdsPorImages(String docIdsPorImages) {
        this.docIdsPorImages = docIdsPorImages;
    }
}

