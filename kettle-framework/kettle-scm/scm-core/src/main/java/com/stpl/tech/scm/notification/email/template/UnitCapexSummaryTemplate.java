package com.stpl.tech.scm.notification.email.template;

import com.stpl.tech.scm.data.transport.model.UnitCapexDataSummary;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class UnitCapexSummaryTemplate extends AbstractVelocityTemplate {

    List<UnitCapexDataSummary> unitCapexDataSummaryList;
    String basePath;
    public UnitCapexSummaryTemplate(){}

    public UnitCapexSummaryTemplate(List<UnitCapexDataSummary> unitCapexDataSummaryList, String basePath){
        this.unitCapexDataSummaryList = unitCapexDataSummaryList;
        this.basePath = basePath;
    }
    @Override
    public String getTemplatePath() {
        return "templates/UnitCapexSummaryTemplate.html";
    }

    @Override
    public String getFilepath() {
        return basePath+"/unit-capex-summary/unit-capex-temp-"+System.currentTimeMillis()+".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String,Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("detail",unitCapexDataSummaryList);
        return stringObjectMap;
    }
}
