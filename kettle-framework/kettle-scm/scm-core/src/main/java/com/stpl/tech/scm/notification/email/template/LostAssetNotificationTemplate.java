package com.stpl.tech.scm.notification.email.template;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.domain.model.LostAssetEmailObject;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;
import org.apache.velocity.tools.generic.DateTool;

import java.util.HashMap;
import java.util.Map;

@AllArgsConstructor
@NoArgsConstructor
public class LostAssetNotificationTemplate extends AbstractVelocityTemplate {
    private LostAssetEmailObject lostAsset;
    private String basePath;

    @Override
    public String getTemplatePath() {
        return "templates/LostAssetTemplate.html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/asset/lostAssets/" + lostAsset.getUnitId()
                + "_" + SCMUtil.getDateString(SCMUtil.getCurrentDateIST()) + ".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("lostAsset", lostAsset);
        stringObjectMap.put("dateTool", new DateTool());
        return stringObjectMap;
    }
}
