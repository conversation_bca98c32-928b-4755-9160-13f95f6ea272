package com.stpl.tech.scm.core.service.impl;

import java.util.Objects;

public class ForecastEstimationKey {
    private String skuId;
    private String categoryName;
    private String date;

    public ForecastEstimationKey() {
    }

    public ForecastEstimationKey(String skuId, String categoryName, String date) {
        this.skuId = skuId;
        this.categoryName = categoryName;
        this.date = date;
    }

    public String getSkuId() {
        return skuId;
    }

    public void setSkuId(String skuId) {
        this.skuId = skuId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getDate() {
        return date;
    }

    public void setDate(String date) {
        this.date = date;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ForecastEstimationKey that = (ForecastEstimationKey) o;
        return Objects.equals(skuId, that.skuId) && Objects.equals(categoryName, that.categoryName) && Objects.equals(date, that.date);
    }

    @Override
    public int hashCode() {
        return Objects.hash(skuId, categoryName, date);
    }

    @Override
    public String toString() {
        return "ForecastEstimationKey{" +
                "skuId='" + skuId + '\'' +
                ", categoryName='" + categoryName + '\'' +
                ", date='" + date + '\'' +
                '}';
    }
}
