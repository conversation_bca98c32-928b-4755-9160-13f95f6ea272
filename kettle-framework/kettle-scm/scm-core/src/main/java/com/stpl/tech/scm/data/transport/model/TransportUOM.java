package com.stpl.tech.scm.data.transport.model;

public enum TransportUOM {

	BAGS("BGS"),

	BALE("BAL"),
	
	BUNDLES("BND"),
	
	BUCKLES("BKL"),
	
	BILLION_OF_UNITS("BOU"),

	BOX("BOX"),

	BOTTLES("BTL"),

	BUNCHES("BUN"),
	
	CANS("CAN"),

	CUBIC_METERES("CBM"),
	
	CUBIC_CENTIMETERES("CCM"),

	CENTI_METERS("CMS"),

	CARTONS("CTN"),
	
	DOZENS("DZN"),
	
	DRUMS("DRM"),

	GRAMS("GMS"),

	KILO_GRAMS("KGS"),

	<PERSON><PERSON><PERSON>_LITRE("KLR"),

	KILO_METERS("KMS"),

	LITRES("LTR"),

	MILLI_LITRES("MLT"),//MLS is incorrect 

	METERS("MTR"),

	METRIC_TONNES("MTS"),

	NUMBERS_OR_UNITS("NOS"),

	OTHERS("OTH"),
	
	PACKS("PAC"),
	
	PIECES("PCS"),
	
	PAIRS("PRS"),

	QUINTAL("QTL"),

	ROLLS("ROL"),
	
	SETS("SET"),
	
	SQUARE_FEET("SQF"),
	
	SQUARE_METERS("SQM"),
	
	SQUARE_YARDS("SQY"),
	
	TONNES("TON"),
	
	UNITS("UNT"),
	
	YARDS("YDS");

	private String code;

	private TransportUOM(String code) {
		this.code = code;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}
	
	
}
