package com.stpl.tech.scm.data.model;

import javax.persistence.Entity;
import javax.persistence.Table;
import javax.persistence.GenerationType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Column;
import javax.persistence.FetchType;
import javax.persistence.ManyToOne;
import javax.persistence.JoinColumn;


@Entity
@Table(name = "VENDOR_DETAIL_CHANGE_REQUEST_DATA")
public class VendorDetailChangeRequestData implements java.io.Serializable {

    private Integer vendorDetailChangeRequestDataId;
    private VendorDetailChangeRequest vendorDetailChangeRequest;
    private Integer vendorId;
    private String tableName;
    private String fieldName;
    private String comment;
    private Integer dispatchId;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "VENDOR_DETAIL_CHANGE_REQUEST_DATA_ID", unique = true, nullable = false)
    public Integer getVendorDetailChangeRequestDataId() {
        return vendorDetailChangeRequestDataId;
    }

    public void setVendorDetailChangeRequestDataId(Integer vendorDetailChangeRequestDataId) {
        this.vendorDetailChangeRequestDataId = vendorDetailChangeRequestDataId;
    }

    @Column(name = "VENDOR_ID",nullable = false)
    public Integer getVendorId() {
        return vendorId;
    }

    public void setVendorId(Integer vendorId) {
        this.vendorId = vendorId;
    }

    @Column(name = "TABLE_NAME",nullable = false)
    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    @Column(name = "FIELD_NAME",nullable = false)
    public String getFieldName() {
        return fieldName;
    }

    public void setFieldName(String fieldName) {
        this.fieldName = fieldName;
    }

    @Column(name = "COMMENT")
    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    @Column(name = "DISPATCH_ID")
    public Integer getDispatchId() {
        return dispatchId;
    }

    public void setDispatchId(Integer dispatchId) {
        this.dispatchId = dispatchId;
    }

    @ManyToOne(fetch = FetchType.LAZY, targetEntity = VendorDetailChangeRequest.class)
    @JoinColumn(name = "VENDOR_DETAIL_CHANGE_REQUEST_ID", nullable = false)
    public VendorDetailChangeRequest getVendorDetailChangeRequest() {
        return vendorDetailChangeRequest;
    }

    public void setVendorDetailChangeRequest(VendorDetailChangeRequest vendorDetailChangeRequest) {
        this.vendorDetailChangeRequest = vendorDetailChangeRequest;
    }


}
