package com.stpl.tech.scm.data.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

@Entity
@Table(name = "GATEPASS_ITEM_DATA")
public class GatepassItemData {

	private Integer id;
	private GatepassData gatepassData;
	private Integer skuId;
	private BigDecimal price;
	private String uom;
	private String transType;
	private BigDecimal quantity;
	private BigDecimal amount;
	private BigDecimal tax;
	private BigDecimal cost;
	private Integer closureId;
	private List<GatepassTaxDetail> taxDetails = new ArrayList<GatepassTaxDetail>();
	private List<GatepassItemDrilldownDetail> drillDowns = new ArrayList<GatepassItemDrilldownDetail>();
	private Integer createdBy;
	private Date createdAt;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "ITEM_ID", nullable = false, unique = true)
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "GATEPASS_ID", nullable = false)
	public GatepassData getGatepassData() {
		return gatepassData;
	}

	public void setGatepassData(GatepassData gatepassData) {
		this.gatepassData = gatepassData;
	}

	@Column(name = "SKU_ID", nullable = false)
	public Integer getSkuId() {
		return skuId;
	}

	public void setSkuId(Integer skuId) {
		this.skuId = skuId;
	}

	@Column(name = "PRICE", nullable = true)
	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	@Column(name = "UOM", nullable = false)
	public String getUom() {
		return uom;
	}

	public void setUom(String uom) {
		this.uom = uom;
	}

	@Column(name = "AMOUNT", nullable = true)
	public BigDecimal getAmount() {
		return amount;
	}

	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}

	@Column(name = "TAX", nullable = true)
	public BigDecimal getTax() {
		return tax;
	}

	public void setTax(BigDecimal tax) {
		this.tax = tax;
	}

	@Column(name = "COST", nullable = true)
	public BigDecimal getCost() {
		return cost;
	}

	public void setCost(BigDecimal cost) {
		this.cost = cost;
	}

	@Column(name = "QUANTITY", nullable = true)
	public BigDecimal getQuantity() {
		return quantity;
	}

	public void setQuantity(BigDecimal quantity) {
		this.quantity = quantity;
	}

	@Column(name = "CLOSURE_ID", nullable = true)
	public Integer getClosureId() {
		return closureId;
	}

	public void setClosureId(Integer closureId) {
		this.closureId = closureId;
	}

	@Column(name = "TRANSACTION_TYPE", nullable = false)
	public String getTransType() {
		return transType;
	}

	public void setTransType(String transType) {
		this.transType = transType;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "itemData", cascade = CascadeType.PERSIST)
	public List<GatepassTaxDetail> getTaxDetails() {
		return taxDetails;
	}

	public void setTaxDetails(List<GatepassTaxDetail> taxDetails) {
		this.taxDetails = taxDetails;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "itemData", cascade = CascadeType.PERSIST)
	public List<GatepassItemDrilldownDetail> getDrillDowns() {
		return drillDowns;
	}

	public void setDrillDowns(List<GatepassItemDrilldownDetail> drillDowns) {
		this.drillDowns = drillDowns;
	}

	@Column(name = "CREATED_BY", nullable = false)
	public Integer getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}

	@Column(name = "CREATED_AT", nullable = false)
	public Date getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

}
