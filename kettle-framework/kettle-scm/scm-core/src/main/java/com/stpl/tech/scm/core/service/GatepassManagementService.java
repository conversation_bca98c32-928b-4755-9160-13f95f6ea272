package com.stpl.tech.scm.core.service;

import java.text.ParseException;
import java.util.List;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.scm.core.exception.GatepassException;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.domain.model.Gatepass;
import com.stpl.tech.scm.domain.model.GatepassVendorMapping;
import com.stpl.tech.scm.domain.model.SearchGatepass;

public interface GatepassManagementService {

	public List<GatepassVendorMapping> getVendorMappingList(String opsType, Integer unitId, String status);

	public Integer createGatepass(Gatepass gatepass) throws InventoryUpdateException, GatepassException, SumoException, ParseException, DataNotFoundException;

	public List<Gatepass> getGatepass(SearchGatepass gatepass);

	public Boolean updateGatepass(Gatepass gatepass) throws InventoryUpdateException, GatepassException, SumoException, ParseException, DataNotFoundException;

	public Boolean cancelGatepass(Gatepass gatepass) throws InventoryUpdateException, SumoException;

	public Boolean addVendorMapping(GatepassVendorMapping gatepassVendorDetail) throws GatepassException, SumoException;

	public Boolean updateVendorStatus(int vendorId, String status);

}
