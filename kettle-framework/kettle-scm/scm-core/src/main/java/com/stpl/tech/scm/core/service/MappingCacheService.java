package com.stpl.tech.scm.core.service;

import com.stpl.tech.scm.data.model.ProductionUnitData;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import java.util.List;

public interface MappingCacheService {

    public Integer findProductionLine(int unitId, int skuId);

	public int findSKUID(int productId);

	public ProductionUnitData findProductionLine(int productIonUnitId);

	public List<SkuDefinition> findAllSkuDefinition(int productId);

}
