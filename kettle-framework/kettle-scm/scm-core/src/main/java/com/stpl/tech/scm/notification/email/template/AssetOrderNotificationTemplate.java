package com.stpl.tech.scm.notification.email.template;

import java.util.HashMap;
import java.util.Map;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.domain.model.RequestOrder;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;

/**
 * Created by Chaayos on 05-09-2016.
 */
public class AssetOrderNotificationTemplate extends AbstractVelocityTemplate {

	private RequestOrder requestOrder;
	private String basePath;

	public AssetOrderNotificationTemplate() {

	}

	public AssetOrderNotificationTemplate(RequestOrder requestOrder, String basePath) {
		this.requestOrder = requestOrder;
		this.basePath = basePath;
	}

	@Override
	public String getTemplatePath() {
		return "templates/AssetOrderTemplate.html";
	}

	@Override
	public String getFilepath() {
		return basePath + "/vendor/requestOrders/" + requestOrder.getRequestUnit().getId() + "/" + requestOrder.getId()
				+ "_" + SCMUtil.getDateString(SCMUtil.getCurrentDateIST()) + ".html";
	}

	@Override
	public Map<String, Object> getData() {
		Map<String, Object> stringObjectMap = new HashMap<>();
		stringObjectMap.put("requestOrder", requestOrder);
		return stringObjectMap;
	}

}
