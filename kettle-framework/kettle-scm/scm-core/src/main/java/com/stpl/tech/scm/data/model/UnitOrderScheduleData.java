package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Entity
@Table(name = "UNIT_ORDER_SCHEDULE")
public class UnitOrderScheduleData {

    private Integer id;
    private RegularOrderUnitBrandData regularOrderUnitBrandData;
    private String orderingDay;
    private String isFunctional;
    private String isOrderingDay;
    private Integer orderingDays;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ORDER_SCHEDULE_ID", nullable = false, unique = true)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @ManyToOne(fetch = FetchType.LAZY, targetEntity = RegularOrderUnitBrandData.class)
    @JoinColumn(name = "UNIT_BRAND_ID", nullable = false)
    public RegularOrderUnitBrandData getRegularOrderUnitBrandData() {
        return regularOrderUnitBrandData;
    }

    public void setRegularOrderUnitBrandData(RegularOrderUnitBrandData regularOrderUnitBrandData) {
        this.regularOrderUnitBrandData = regularOrderUnitBrandData;
    }

    @Column(name = "ORDERING_DAY", nullable = false)
    public String getOrderingDay() {
        return orderingDay;
    }

    public void setOrderingDay(String orderingDay) {
        this.orderingDay = orderingDay;
    }

    @Column(name = "IS_FUNCTIONAL", nullable = false)
    public String getIsFunctional() {
        return isFunctional;
    }

    public void setIsFunctional(String isFunctional) {
        this.isFunctional = isFunctional;
    }

    @Column(name = "IS_ORDERING_DAY", nullable = false)
    public String getIsOrderingDay() {
        return isOrderingDay;
    }

    public void setIsOrderingDay(String isOrderingDay) {
        this.isOrderingDay = isOrderingDay;
    }

    @Column(name = "ORDERING_DAYS", nullable = true)
    public Integer getOrderingDays() {
        return orderingDays;
    }

    public void setOrderingDays(Integer orderingDays) {
        this.orderingDays = orderingDays;
    }
}
