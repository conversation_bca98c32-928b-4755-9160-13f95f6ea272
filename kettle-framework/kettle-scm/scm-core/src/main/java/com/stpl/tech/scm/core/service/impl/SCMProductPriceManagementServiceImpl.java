package com.stpl.tech.scm.core.service.impl;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.ProductRecipeKey;
import com.stpl.tech.master.core.external.cache.RecipeCache;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.ProductClassification;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.domain.model.UnitStatus;
import com.stpl.tech.master.recipe.model.RecipeCategoryCost;
import com.stpl.tech.master.recipe.model.RecipeCost;
import com.stpl.tech.master.recipe.model.RecipeCostData;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.master.recipe.model.RecipeIngredientCost;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.SCMProductManagementService;
import com.stpl.tech.scm.core.service.SCMProductPriceManagementService;
import com.stpl.tech.scm.core.service.StockManagementService;
import com.stpl.tech.scm.core.util.RecipeCostCalculator;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.dao.PriceManagementDao;
import com.stpl.tech.scm.data.dao.ProductPriceManagementDao;
import com.stpl.tech.scm.data.model.CostDetailData;
import com.stpl.tech.scm.domain.model.CostDetail;
import com.stpl.tech.scm.domain.model.FulfillmentType;
import com.stpl.tech.scm.domain.model.PriceUpdateDrillDown;
import com.stpl.tech.scm.domain.model.PriceUpdateEntry;
import com.stpl.tech.scm.domain.model.PriceUpdateEntryType;
import com.stpl.tech.scm.domain.model.PriceUpdateEvent;
import com.stpl.tech.scm.domain.model.PriceUpdateEventStatus;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.ProductPriceData;
import com.stpl.tech.scm.domain.model.ProductStatus;
import com.stpl.tech.scm.domain.model.ProductionBooking;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import com.stpl.tech.scm.domain.model.TrimmedCostDetail;
import com.stpl.tech.scm.domain.model.UnitDetail;
import com.stpl.tech.util.AppConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.servlet.View;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class SCMProductPriceManagementServiceImpl implements SCMProductPriceManagementService {

	// private static final Logger LOG =
	// LoggerFactory.getLogger(SCMProductPriceManagementServiceImpl.class);

	private final Logger LOG = LoggerFactory.getLogger(SCMProductPriceManagementServiceImpl.class);

	@Autowired
	private ProductPriceManagementDao managerDao;

	@Autowired
	private SCMCache cache;

	@Autowired
	private RecipeCache recipeCache;

	@Autowired
	private MasterDataCache masterCache;
	
	@Autowired
	private StockManagementService stockManagementService;
	
	@Autowired
	private SCMProductManagementService scmProductManagementService;

	@Autowired
	private PriceManagementDao priceManagementDao;

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<PriceUpdateEvent> getSkuPriceUpdateEvents() {
		return managerDao.getSkuPriceUpdateEvents();
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public PriceUpdateEvent getSkuPriceUpdateEvent(int eventId) {
		return managerDao.getSkuPriceUpdateEventData(eventId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public Integer addSkuPriceUpdateFile(PriceUpdateEvent event) throws DataUpdationException, SumoException {
		return managerDao.addSkuPriceUpdateEvent(event);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean calculateRecipeCost(Integer updatedEventId) {
		/** Gets updated Event */
		PriceUpdateEvent updatedEvent = managerDao.getPriceUpdateEvent(updatedEventId);

		/** Update price map using new prices */
		Map<Integer, ProductPriceData> priceMap = getProductPriceDataMap(updatedEvent);

		List<PriceUpdateEntry> recipeEntries = new ArrayList<>();

		/** create recipe entries */

		calculateRecipeCostEntries(recipeCache.getScmRecipes().values(), priceMap, recipeEntries);
		priceMap = getProductPriceDataMap(updatedEvent);
		calculateRecipeCostEntries(recipeCache.getRecipes().values(), priceMap, recipeEntries);
		managerDao.addPriceUpdateEntries(recipeEntries, updatedEvent);
		return true;
	}

	private void calculateRecipeCostEntries(Collection<RecipeDetail> list, Map<Integer, ProductPriceData> priceMap,
			List<PriceUpdateEntry> recipeEntries) {
		for (RecipeDetail recipe : list) {
			/** original cost **/
			RecipeCost previousCost = calculateRecipeCost(recipe);
			/** new cost **/
			RecipeCost newRecipeCost = calculateRecipeCost(recipe, priceMap);

			if (previousCost != null && newRecipeCost != null) {
				for (RecipeCategoryCost o : previousCost.getCategoryCost()) {
					for (RecipeCategoryCost n : newRecipeCost.getCategoryCost()) {
						if (o.getCostType().equals(n.getCostType()) && o.getCost().compareTo(n.getCost()) != 0) {
							recipeEntries.addAll(createRecipeEntry(recipe, newRecipeCost, n, o.getCost()));
						}
					}
				}
			}
		}

	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean calculateProductCost(Integer updatedEventId) {
		return managerDao.callProductPriceUpdate(updatedEventId);
	}

	/**
	 * Creates a Price Update Entry for RECIPE
	 *
	 * @param recipe
	 * @param categoryCost
	 * @param oldCost
	 * @return
	 */
	private List<PriceUpdateEntry> createRecipeEntry(RecipeDetail detail, RecipeCost recipe,
			RecipeCategoryCost categoryCost, BigDecimal oldCost) {

		List<PriceUpdateEntry> entries = new ArrayList<>();
		PriceUpdateEntry entry = new PriceUpdateEntry();
		entry.setEntryStatus(PriceUpdateEventStatus.INITIATED);
		entry.setKeyId(recipe.getRecipeId());
		entry.setKeyName(recipe.getRecipeName());
		if (detail.getProduct().getClassification() != null
				&& detail.getProduct().getClassification().equals(ProductClassification.SCM_PRODUCT)) {
			entry.setKeyType(PriceUpdateEntryType.RECIPE_SCM);
		} else {
			entry.setKeyType(convertUnitCategoryToPriceUpdateType(categoryCost.getCostType()));
		}
		entry.setUnitOfMeasure(recipe.getDimension());
		entry.setUnitPrice(oldCost);
		entry.setUpdatedUnitPrice(categoryCost.getCost());
		entry.setEditedUnitPrice(categoryCost.getCost());

		for (RecipeIngredientCost ingCost : recipe.getCommonIngredient()) {
			entry.getDrilldowns().add(createIngredientDrillDown(ingCost, "COMMON"));
		}
		for (RecipeIngredientCost ingCost : categoryCost.getIngredients()) {
			entry.getDrilldowns().add(createIngredientDrillDown(ingCost, categoryCost.getCostType().name()));
		}
		for (String error : recipe.getErrorCodes()) {
			entry.getErrors().add(error);
		}
		// LOG.info(JSONSerializer.toJSON(entry));
		entries.add(entry);

		if (detail.getProduct().getClassification() != null
				&& detail.getProduct().getClassification().equals(ProductClassification.SCM_PRODUCT)) {
			ProductDefinition def = cache.getProductDefinition(recipe.getProductId());

			entries.add(create(PriceUpdateEntryType.PRODUCT, def.getProductId(), def.getProductName(), entry,
					def.getUnitOfMeasure(), def.getUnitPrice()));
			for (SkuDefinition sku : viewAllSkuByProduct(recipe.getProductId())) {
				entries.add(create(PriceUpdateEntryType.SKU, sku.getSkuId(), sku.getSkuName(), entry,
						def.getUnitOfMeasure(), sku.getUnitPrice()));
			}
		}
		return entries;
	}

	public List<SkuDefinition> viewAllSkuByProduct(int productId) {
		List<SkuDefinition> skus = new ArrayList<>();
		cache.getSkuDefinitions().values().forEach((sku) -> {
			if (sku.getLinkedProduct().getId() == productId) {
				skus.add(sku);
			}
		});
		return skus;
	}

	private PriceUpdateEntry create(PriceUpdateEntryType type, int keyId, String keyName, PriceUpdateEntry entryData,
			String uom, Float unitPrice) {
		PriceUpdateEntry clone = new PriceUpdateEntry();
		clone.setEntryStatus(entryData.getEntryStatus());
		clone.setApprovedUnitPrice(entryData.getApprovedUnitPrice());
		clone.getDrilldowns().addAll(entryData.getDrilldowns());
		clone.setEditedUnitPrice(entryData.getEditedUnitPrice());
		clone.getErrors().addAll(entryData.getErrors());
		clone.setError(entryData.isError());
		clone.setKeyId(keyId);
		clone.setKeyName(keyName);
		clone.setKeyType(type);
		clone.setUnitOfMeasure(uom);
		clone.setUnitPrice(new BigDecimal(unitPrice));
		clone.setUpdatedUnitPrice(entryData.getUpdatedUnitPrice());
		// LOG.info(JSONSerializer.toJSON(clone));
		return clone;
	}

	private PriceUpdateEntryType convertUnitCategoryToPriceUpdateType(UnitCategory unitCategory) {
		switch (unitCategory) {
		case CAFE:
			return PriceUpdateEntryType.RECIPE_CAFE;
		case COD:
			return PriceUpdateEntryType.RECIPE_COD;
		case TAKE_AWAY:
			return PriceUpdateEntryType.RECIPE_TAKE_AWAY;
		case DELIVERY:
			return PriceUpdateEntryType.RECIPE_DELIVERY;
		default:
			break;
		}
		return null;
	}

	private PriceUpdateDrillDown createIngredientDrillDown(RecipeIngredientCost ingredient, String type) {
		PriceUpdateDrillDown drillDown = new PriceUpdateDrillDown();
		drillDown.setCost(ingredient.getCost());
		drillDown.setDrilldownCategory("RECIPE");
		drillDown.setDrilldownType(type);
		drillDown.setKeyId(ingredient.getProductId());
		drillDown.setKeyType(ingredient.getType().name());
		drillDown.setKeyName(ingredient.getProductName());
		drillDown.setQuantity(ingredient.getQuantity());
		drillDown.setUnitOfMeasure(ingredient.getUom());
		drillDown.setUnitPrice(ingredient.getPrice());
		return drillDown;
	}

	private Map<Integer, ProductPriceData> getProductPriceDataMap(PriceUpdateEvent updatedEvent) {
		Map<Integer, ProductPriceData> map = new HashMap<>();
		for (ProductPriceData data : cache.getProductPrices().values()) {
			map.put(data.getProductId(), SCMUtil.clone(data, ProductPriceData.class));
		}
		for (PriceUpdateEntry entry : updatedEvent.getEntries()) {
			if (PriceUpdateEntryType.PRODUCT.equals(entry.getKeyType())) {
				ProductPriceData targetProduct = map.get(entry.getKeyId());
				if (targetProduct == null) {
					targetProduct = SCMDataConverter.convert(entry);
				}
				targetProduct.setPrice(getEntryPrice(entry));
				map.put(targetProduct.getProductId(), targetProduct);
			}
		}
		return map;
	}

	private BigDecimal getEntryPrice(PriceUpdateEntry entry) {
		if (entry.getApprovedUnitPrice() != null && BigDecimal.ZERO.compareTo(entry.getApprovedUnitPrice()) != 0) {
			return entry.getApprovedUnitPrice();
		}
		if (entry.getEditedUnitPrice() != null && BigDecimal.ZERO.compareTo(entry.getEditedUnitPrice()) != 0) {
			return entry.getEditedUnitPrice();
		}
		return entry.getUpdatedUnitPrice();
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public View downloadSkuPriceUpdateFile() {
		return managerDao.downloadSkuPriceUpdateFile();
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean updateSkuPriceEvent(int eventId, int userId, String username, PriceUpdateEventStatus status)
            throws IOException, SumoException {

		if (managerDao.updateSkuPriceEvent(eventId, userId, username, status)) {
			if (PriceUpdateEventStatus.APPROVED.equals(status)) {
				managerDao.approveProductPrice(eventId);
			}
			return true;
		}
		return false;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void failEvent(Integer updatedEventId) {
		managerDao.failEvent(updatedEventId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public int updateSkuPriceEvent(PriceUpdateEvent event, Integer userId, String userName) throws SumoException {
		return managerDao.updateSkuPriceEvent(event, userId, userName);
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.core.service.SCMProductPriceManagementService#
	 * calculateRecipeCost(com.stpl.tech.master.recipe.model.RecipeDetail)
	 */
	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public RecipeCost calculateRecipeCost(RecipeDetail recipe) {
		RecipeCostCalculator calculator = new RecipeCostCalculator(recipe, cache.getProductPrices());
		return calculator.calculate();
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public RecipeCost calculateRecipeCostNew(RecipeDetail recipe, String region) {
		Pair<Integer, Integer> fulfilmentKitchenWH = getFulfilmentKitchenWHOfRegion(region);
		List<String> regions = getUniqueRegions(region, fulfilmentKitchenWH);
		List<Integer> recipeProductIds = stockManagementService.getRecipeProductIds(recipe);
		Map<String, List<ProductDefinition>> productsFulfilmentByType = getProductsFulfilmentByType(recipeProductIds);
		Map<Integer, Pair<List<SkuDefinition> , BigDecimal>> productSkuMapWithPrice = getProductSkuMapWithPrice(recipeProductIds);
		Map<String, List<Integer>> fulfilmentTypeWithSkus = getFulfilmentTypeWithSkus(productsFulfilmentByType, productSkuMapWithPrice);
		List<CostDetailData> costDetailDataKitchens = new ArrayList<>();
		List<CostDetailData> costDetailDataWhs = new ArrayList<>();
		if (Objects.nonNull(fulfilmentKitchenWH.getKey()) && fulfilmentTypeWithSkus.containsKey(FulfillmentType.KITCHEN.value()) && Objects.nonNull(fulfilmentTypeWithSkus.get(FulfillmentType.KITCHEN.value()))
					&& !fulfilmentTypeWithSkus.get(FulfillmentType.KITCHEN.value()).isEmpty()) {
			costDetailDataKitchens = priceManagementDao.getCurrentPrices(PriceUpdateEntryType.SKU, fulfilmentKitchenWH.getKey(),fulfilmentTypeWithSkus.get(FulfillmentType.KITCHEN.value()), false);
			setPricesInProductsSkuMap(productSkuMapWithPrice, costDetailDataKitchens);
		}
		if (Objects.nonNull(fulfilmentKitchenWH.getValue()) && fulfilmentTypeWithSkus.containsKey(FulfillmentType.WAREHOUSE.value()) && Objects.nonNull(fulfilmentTypeWithSkus.get(FulfillmentType.WAREHOUSE.value()))
					&& !fulfilmentTypeWithSkus.get(FulfillmentType.WAREHOUSE.value()).isEmpty()) {
			costDetailDataWhs = priceManagementDao.getCurrentPrices(PriceUpdateEntryType.SKU, fulfilmentKitchenWH.getValue(),fulfilmentTypeWithSkus.get(FulfillmentType.WAREHOUSE.value()), false);
			setPricesInProductsSkuMap(productSkuMapWithPrice, costDetailDataWhs);
		}

		List<Integer> missingProductPriceIds = getMissingPricesProductKeys(productSkuMapWithPrice);
		if (!missingProductPriceIds.isEmpty()) {
			LOG.info("Missing Prices From Fulfilment Unit Products are : {} Now Searching in Complete Region With ProductIds", missingProductPriceIds.size());
			try {
				List<CostDetailData> costDetailDataList = priceManagementDao.getAllCurrentPriceForAnyUnitInRegionByKeyId(PriceUpdateEntryType.PRODUCT, missingProductPriceIds, regions, false);
				setPricesInProductsSkuMap(productSkuMapWithPrice, costDetailDataList);
			} catch (Exception e) {
				LOG.error("Exception Occurred While getting Price From Region : {} ::: ",region,e);
			}
		}

		missingProductPriceIds.clear();
		missingProductPriceIds = getMissingPricesProductKeys(productSkuMapWithPrice);
		if (!missingProductPriceIds.isEmpty()) {
			LOG.info("Now searching for SKUs in complete region");
			if (Objects.nonNull(fulfilmentKitchenWH.getKey()) && fulfilmentTypeWithSkus.containsKey(FulfillmentType.KITCHEN.value()) && Objects.nonNull(fulfilmentTypeWithSkus.get(FulfillmentType.KITCHEN.value()))
					&& !fulfilmentTypeWithSkus.get(FulfillmentType.KITCHEN.value()).isEmpty()) {
				costDetailDataKitchens.clear();
				try {
					costDetailDataKitchens = priceManagementDao.getAllCurrentPriceForAnyUnitInRegionByKeyId(PriceUpdateEntryType.SKU, fulfilmentTypeWithSkus.get(FulfillmentType.KITCHEN.value()), regions, false);
					setPricesInProductsSkuMap(productSkuMapWithPrice, costDetailDataKitchens);
				} catch (Exception e) {
					LOG.error("Exception Occurred While getting Price From Region : {} ::: ",region,e);
				}
			}
			if (Objects.nonNull(fulfilmentKitchenWH.getValue()) && fulfilmentTypeWithSkus.containsKey(FulfillmentType.WAREHOUSE.value()) && Objects.nonNull(fulfilmentTypeWithSkus.get(FulfillmentType.WAREHOUSE.value()))
					&& !fulfilmentTypeWithSkus.get(FulfillmentType.WAREHOUSE.value()).isEmpty()) {
				costDetailDataWhs.clear();
				try {
					costDetailDataWhs = priceManagementDao.getAllCurrentPriceForAnyUnitInRegionByKeyId(PriceUpdateEntryType.SKU, fulfilmentTypeWithSkus.get(FulfillmentType.WAREHOUSE.value()), regions, false);
					setPricesInProductsSkuMap(productSkuMapWithPrice, costDetailDataWhs);
				} catch (Exception e) {
					LOG.error("Exception Occurred While getting Price From Region : {} ::: ",region,e);
				}
			}
		}
		RecipeCostCalculator calculator = new RecipeCostCalculator(recipe, cache.getProductPrices());
		calculator.setProductSkuMapWithPrice(productSkuMapWithPrice);
		return calculator.calculate();
	}

	@Override
	public List<String> getUniqueRegions(String region, Pair<Integer, Integer> fulfilmentKitchenWH) {
		List<String> regions = new ArrayList<>();
		regions.add(region);
		if (Objects.nonNull(fulfilmentKitchenWH.getKey())) {
			UnitDetail unitDetail = cache.getUnitDetail(fulfilmentKitchenWH.getKey());
			if (Objects.nonNull(unitDetail) && Objects.nonNull(unitDetail.getUnitRegion()) && !unitDetail.getUnitRegion().equalsIgnoreCase(region)) {
				regions.add(unitDetail.getUnitRegion());
			}
		}
		if (Objects.nonNull(fulfilmentKitchenWH.getValue())) {
			UnitDetail unitDetail = cache.getUnitDetail(fulfilmentKitchenWH.getValue());
			if (Objects.nonNull(unitDetail) && Objects.nonNull(unitDetail.getUnitRegion()) && !unitDetail.getUnitRegion().equalsIgnoreCase(region)) {
				regions.add(unitDetail.getUnitRegion());
			}
		}
		return regions;
	}


	@Override
	public List<Integer> getMissingPricesProductKeys(Map<Integer, Pair<List<SkuDefinition>, BigDecimal>> productSkuMapWithPrice) {
		return productSkuMapWithPrice.entrySet().stream()
				.filter(entry -> entry.getValue().getValue() == null)
				.map(Map.Entry::getKey)
				.collect(Collectors.toList());
	}

	@Override
	public void setPricesInProductsSkuMap(Map<Integer, Pair<List<SkuDefinition>, BigDecimal>> productSkuMapWithPrice, List<CostDetailData> costDetailDataWhs) {
		for (CostDetailData costDetailData : costDetailDataWhs) {
			ProductDefinition productDefinition = null;
			if (costDetailData.getKeyType().equalsIgnoreCase(PriceUpdateEntryType.SKU.value())) {
				SkuDefinition skuDefinition = cache.getSkuDefinition(costDetailData.getKeyId());
				if (Objects.nonNull(skuDefinition)) {
					productDefinition = cache.getProductDefinition(skuDefinition.getLinkedProduct().getId());
				}
			} else {
				productDefinition = cache.getProductDefinition(costDetailData.getKeyId());
			}

			if (Objects.nonNull(productDefinition)) {
				Pair<List<SkuDefinition>, BigDecimal> pair = productSkuMapWithPrice.get(productDefinition.getProductId());
				if (Objects.nonNull(pair)) {
					if (Objects.isNull(pair.getValue())) {
						pair.setValue(costDetailData.getPrice());
						productSkuMapWithPrice.put(productDefinition.getProductId(), pair);
					}
				}
			}
		}
	}

	@Override
	public Map<String, List<Integer>> getFulfilmentTypeWithSkus(Map<String, List<ProductDefinition>> productsFulfilmentByType, Map<Integer, Pair<List<SkuDefinition>, BigDecimal>> productSkuMapWithPrice) {
		Map<String, List<Integer>> result = new HashMap<>();
		for (Map.Entry<String, List<ProductDefinition>> entry : productsFulfilmentByType.entrySet()) {
			List<Integer> skuIds = new ArrayList<>();
			for (ProductDefinition productDefinition : entry.getValue()) {
				Pair<List<SkuDefinition>, BigDecimal> skusAndPrice = productSkuMapWithPrice.getOrDefault(productDefinition.getProductId(), new Pair<>(null, null));
				List<SkuDefinition> skuDefinitions = new ArrayList<>();
				if (Objects.nonNull(skusAndPrice.getKey())) {
					skuIds.addAll(skusAndPrice.getKey().stream().mapToInt(SkuDefinition::getSkuId).boxed().collect(Collectors.toList()));
				}
			}
			result.put(entry.getKey(), skuIds);
		}
		return result;
	}

	@Override
	public Map<Integer, Pair<List<SkuDefinition>, BigDecimal>> getProductSkuMapWithPrice(List<Integer> recipeProductIds) {
		Map<Integer, List<SkuDefinition>> skuByProduct = scmProductManagementService.viewAllSkuByProduct();
		Map<Integer, Pair<List<SkuDefinition>, BigDecimal>> result = new HashMap<>();
		for (Map.Entry<Integer, List<SkuDefinition>> entry : skuByProduct.entrySet()) {
			if (recipeProductIds.contains(entry.getKey())) {
				result.put(entry.getKey(),new Pair<>(entry.getValue(),null));
			}
		}
		for (Integer productId : recipeProductIds) {
			if (!result.containsKey(productId)) {
				result.put(productId, new Pair<>(null, null));
			}
		}
		return result;
	}

	@Override
	public Map<String, List<ProductDefinition>> getProductsFulfilmentByType(List<Integer> recipeProductIds) {
		Map<String, List<ProductDefinition>> result = new HashMap<>();
		for (Integer productId : recipeProductIds) {
			ProductDefinition productDefinition = cache.getProductDefinition(productId);
			if (Objects.nonNull(productDefinition)) {
				if (productDefinition.getFulfillmentType().equals(FulfillmentType.KITCHEN)) {
					List<ProductDefinition> products;
					if (result.containsKey(FulfillmentType.KITCHEN.value())) {
						products = result.get(FulfillmentType.KITCHEN.value());
					} else {
						products = new ArrayList<>();
					}
					products.add(productDefinition);
					result.put(FulfillmentType.KITCHEN.value(), products);
				} else {
					List<ProductDefinition> products;
					if (result.containsKey(FulfillmentType.WAREHOUSE.value())) {
						products = result.get(FulfillmentType.WAREHOUSE.value());
					} else {
						products = new ArrayList<>();
					}
					products.add(productDefinition);
					result.put(FulfillmentType.WAREHOUSE.value(), products);
				}
			}
		}
		return result;
	}

	@Override
	public Pair<Integer, Integer> getFulfilmentKitchenWHOfRegion(String region) {
		Pair<Integer, Integer> result = new Pair<>();
		result.setKey(cache.getRegionFulfillmentMapping(region + "-KITCHEN"));
		result.setValue(cache.getRegionFulfillmentMapping(region + "-WAREHOUSE"));
		return result;
	}

	public RecipeCost calculateRecipeCost(RecipeDetail recipe, Map<Integer, ProductPriceData> map) {
		RecipeCostCalculator calculator = new RecipeCostCalculator(recipe, map);
		return calculator.calculate();
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.core.service.SCMProductPriceManagementService#
	 * calculateRecipeCost(int)
	 */
	@Override
	public RecipeCost calculateRecipeCost(ProductRecipeKey recipe, String region) {
		RecipeDetail detail = recipeCache.getRecipe(recipe);
		if (Objects.nonNull(detail)) {
			return calculateRecipeCostNew(detail, region);
		} else {
			return null;
		}
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean recalculateRecipePrices(int unitId, Map<Integer, ProductPriceData> priceMap) throws IOException, SumoException {
		Set<Integer> productSet;
		List<RecipeCostData> recipeCostList = new ArrayList<>();
		productSet = masterCache.getUnitProductDetails(unitId).stream()
				.filter(p -> ProductStatus.ACTIVE.name().equals(p.getStatus().name())
						&& p.getType() != AppConstants.CHAAYOS_COMBO_PRODUCT_TYPE)
				.map(p -> p.getId()).collect(Collectors.toSet());

		// for cafes
		for (RecipeDetail recipe : recipeCache.getRecipes().values()) {
			if (productSet.contains(recipe.getProduct().getProductId()) && isCafe(unitId)) {
				recipeCostList.addAll(convert(unitId, calculateRecipeCost(recipe, priceMap), recipe));
			}
		}

		// for warehouse and kitchen
		// will not work as we do not have product prices at wareouse and kitchen
		// this is done while production Booking
		/*
		 * if (isWareHouseOrKitchen(unitId)) { for (RecipeDetail scmRecipe :
		 * recipeCache.getScmRecipes().values()) { recipeCostList.addAll(
		 * convert(unitId, calculateRecipeCost(scmRecipe, unitPriceMap.get(unitId)),
		 * scmRecipe)); } }
		 */
		managerDao.saveRecipeCosts(recipeCostList);

		// managerDao.sendCostDataToKettle(recipeCostList);
		return true;
	}

	/*
	 * private boolean isWareHouseOrKitchen(int unitId) { UnitBasicDetail u =
	 * masterCache.getUnitBasicDetail(unitId); return u != null &&
	 * UnitStatus.ACTIVE.equals(u.getStatus()) &&
	 * (UnitCategory.WAREHOUSE.equals(u.getCategory()) ||
	 * UnitCategory.KITCHEN.equals(u.getCategory())); }
	 */

	private boolean isCafe(int unitId) {
		UnitBasicDetail u = masterCache.getUnitBasicDetail(unitId);
		return u != null && UnitStatus.ACTIVE.equals(u.getStatus()) && UnitCategory.CAFE.equals(u.getCategory());
	}

	private List<RecipeCostData> convert(int unitId, RecipeCost recipeCost, RecipeDetail recipe) {
		List<RecipeCostData> l = new ArrayList<>();
		recipeCost.getCategoryCost().forEach(p -> {
			RecipeCostData c = new RecipeCostData();
			c.setRecipeId(recipeCost.getRecipeId());
			c.setRecipeName(recipeCost.getRecipeName());
			c.setProductId(recipe.getProduct().getProductId());
			c.setProductName(recipe.getProduct().getName());
			c.setDimension(recipe.getDimension().getName());
			c.setCategory(p.getCostType());
			c.setCost(p.getCost());
			c.setUpdatedBy("System");
			c.setUpdatedById(SCMServiceConstants.SYSTEM_USER);
			c.setUnitId(unitId);
			if (recipe.getProduct().getClassification() != null) {
				c.setProductSource(recipe.getProduct().getClassification().name());
			} else {
				c.setProductSource(ProductClassification.MENU.name());
			}
			l.add(c);
		});
		return l;
	}

	@Override
	public Map<Integer, Map<Integer, ProductPriceData>> getUnitPriceMap() {
		return managerDao.getAllCurrentProductPriceMap();
	}

	@Override
	public void saveRecipeCost(ProductionBooking booking) throws SumoException, DataNotFoundException {
		List<RecipeCostData> recipeCostList = new ArrayList<>();
		RecipeDetail recipe = recipeCache.getScmRecipe(cache.getRecipeProfile(booking.getUnitId(),booking.getProductId()));
		UnitBasicDetail u = masterCache.getUnitBasicDetail(booking.getUnitId());
		RecipeCostData c = new RecipeCostData();
		c.setRecipeId(recipe.getRecipeId());
		c.setRecipeName(recipe.getName());
		c.setProductId(recipe.getProduct().getProductId());
		c.setProductName(recipe.getProduct().getName());
		c.setDimension(recipe.getDimension().getName());
		c.setCategory(u.getCategory());
		c.setCost(booking.getUnitPrice());
		c.setUpdatedBy("System");
		c.setUpdatedById(SCMServiceConstants.SYSTEM_USER);
		c.setUnitId(booking.getUnitId());
		c.setProductSource(recipe.getProduct().getClassification().name());
		c.setEventEntryId(booking.getBookingId());
		recipeCostList.add(c);
		managerDao.saveRecipeCosts(recipeCostList);
	}

	@Override
	public List<CostDetail> fetchUnitInventory(int unitId) {
		try {
			List<CostDetailData> l = managerDao.fetchUnitInventroy(unitId);
			List<CostDetail> list = new ArrayList<>();
			if(l != null) {
				for (CostDetailData d : l) {
					CostDetail cd = SCMDataConverter.convert(d);
					String name = PriceUpdateEntryType.PRODUCT.name().equals(d.getKeyType())
							? cache.getProductDefinition(d.getKeyId()).getProductName()
							: cache.getSkuDefinition(d.getKeyId()).getSkuName();
					cd.setName(name);
					list.add(cd);
				}
			}
			return list;
		} catch (Exception e){
			LOG.error("Error while fetching unit inventory data ", e);
			return null;
		}

	}

	@Override
	public List<TrimmedCostDetail> fetchNewUnitInventory(int unitId, List<Integer> ids) {
		try {
			List<CostDetailData> l = managerDao.getCostDetailForIds(unitId, ids);
			List<TrimmedCostDetail> list = new ArrayList<>();
			for (CostDetailData d : l) {
				CostDetail cd = SCMDataConverter.convert(d);
				String name = PriceUpdateEntryType.PRODUCT.name().equals(d.getKeyType())
						? cache.getProductDefinition(d.getKeyId()).getProductName()
						: cache.getSkuDefinition(d.getKeyId()).getSkuName();

				cd.setName(name);
				list.add(new TrimmedCostDetail(unitId, name, d.getUom(),d.getKeyId(),d.getQuantity() ,d.getExpiryDate() ,PriceUpdateEntryType.valueOf(d.getKeyType()),d.getPrice()));
			}
			return list;
		} catch (Exception e){
			LOG.error("Error while fetching unit inventory data ", e);
			return new ArrayList<>();
		}
	}

}
