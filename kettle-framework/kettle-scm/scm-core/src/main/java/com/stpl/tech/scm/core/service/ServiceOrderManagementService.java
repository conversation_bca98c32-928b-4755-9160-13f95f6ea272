package com.stpl.tech.scm.core.service;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.itextpdf.text.DocumentException;
import com.stpl.tech.kettle.core.data.budget.vo.BudgetDetail;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.util.model.ServiceOrderCreateVO;
import com.stpl.tech.scm.data.model.AdvancePaymentData;
import com.stpl.tech.scm.data.model.ServiceOrderData;
import com.stpl.tech.scm.domain.model.*;
import org.springframework.web.multipart.MultipartFile;

public interface ServiceOrderManagementService {

	public List<Integer> createServiceOrder(Integer documentId, List<ServiceOrderCreateVO> requestVO, Integer approvalOfHodDocId) throws SumoException;

	public List<CostCenter> getCostCentersData();

	public List<ServiceOrder> findServiceOrders(Integer bccId,Integer vendorId, Integer serviceOrderId,
                                                Integer userId, List<ServiceOrderStatus> statusList, Date startDate, Date endDate, boolean showAll );


	public ServiceOrderSummary findServiceOrdersShort(Integer bccId, Integer vendorId, Integer serviceOrderId, Integer userId,
														  List<ServiceOrderStatus> statusList, Date startDate, Date endDate, boolean showAll , Integer costCenterId);

	public List<ServiceOrderData> findServiceOrderDataList(Integer bccId,Integer vendorId, Integer serviceOrderId, Integer userId,
														   List<ServiceOrderStatus> statusList, Date startDate, Date endDate, boolean showAll ,
														   boolean isShort, Integer costCenterId);

	public boolean rejectServiceOrder(Integer soId, Integer userId) throws SumoException;

	public boolean cancelServiceOrder(Integer soId, Integer userId) throws SumoException;

	public boolean closeServiceOrder(Integer soId, Integer userId) throws SumoException;

	public CostCenter createCentersData(CostCenter costCenter) throws SumoException;

	public CostElement createElementDataOld(Integer costCenterId, CostElement costElement) throws SumoException;

	public List<ServiceOrder> findPendingServiceOrders(Integer vendorId, Integer dispatchId, Integer userId, Integer companyId, Integer locationId, boolean showAll);

	public ServiceOrderSummary convertServiceOrdersShort(List<ServiceOrderData> serviceOrderDataList);

	public boolean createEmployeeCostCenterMap(Integer empId, Integer costCenterId, Integer createdBy) throws SumoException;


	public List<CostCenter> getCostCentersDataForEmployee(Integer empId);

	public String updateCostElementStatus(CostElement costElement);

	public Map<String, List<ListDetail>> getListDatas(String baseType);

	public boolean createElementData(CostElement vo) throws SumoException;

	public List<CostElement> getCostElementsData();

	public boolean saveListDetailData(ListDetail listDetail) throws SumoException;

	public boolean saveSubCategoryData(ListType listType) throws SumoException;

	public boolean saveSubSubCategoryData(ListData listData) throws SumoException;

	public boolean updateListDetailData(ListDetail listDetail);

	public boolean updateSubCategoryData(ListType listType);

	public boolean updateSubSubCategoryData(ListData listData);

	public List<VendorDetail> getVendorData(Integer costCenterId);

	public List<CostElement> getCostElementsData(Integer costCenterId, Integer vendorId);

	public CostElementPriceUpdate getSelectedCostElement(Integer costCenterId, Integer vendorId, Integer costElementId);

	public List<ServiceOrder> getTagList(Integer costCenterId, String tagName);

	public List<String> getTagNamesList(Integer costCenterId);

	public boolean getVendorDetailData(Integer locationId);

	public String changeLevelStatus(Integer soId, String newStatus, String currentStatus, BigDecimal amount, Integer userId) throws SumoException;

	public boolean getUnitCheckBudget(Integer unitId);

	public List<ServiceOrderItem> getDepartmentDataForUnit(List<ServiceOrderItem> requestVO);

    List<String> getBudgetCategory();

	public void calculateServiceAggregate(BudgetDetail budgetDetail, UnitDetail unitDetail, Date businessDate,
			StockTakeType type);

    public DocumentDetail uploadSoDocument(FileType type, MimeType mimeType, DocUploadType docType, Integer userId, MultipartFile file);

    public List<BulkSoDetail> readUploadedSOFile(MultipartFile file) throws IOException, SumoException;

	List<Integer> getServiceReceiveIdsForServiceOrder(Integer soId, String currentSrStatus) throws SumoException;

	public DocumentDetail uploadRequiredDocuments(FileType type,MimeType mimeType,Integer userId,DocUploadType docType,MultipartFile file,String docName);

	List<String> showRequiredDocuments(List<Integer> costElementId);

    public List<ServiceOrder> getSosForAdvance(Integer vendorId) throws SumoException;

	public Boolean submitAdvanceAdjustmentRefund(VendorAdvancePayment vendorAdvancePayment, Integer createdBy) throws SumoException;

    void validateSoPoForAdjustmentOrRefund(List<AdvancePaymentData> advancePaymentDataList) throws SumoException;

    public Boolean approveRejectAdjustmentRefund(VendorAdvancePayment vendorAdvancePayment, Integer approvedBy, String approveReject);

    DocumentDetail uploadApprovalOfHod(FileType type, MimeType mimeType, DocUploadType docType, Integer userId, MultipartFile file, String docName) throws DocumentException, IOException;

	Boolean sendCapexCloseNotification();

	public boolean removeEmployeeCostCenterMap(Integer employeeId , Integer costCenterId, Integer removedBy) throws SumoException;
}
