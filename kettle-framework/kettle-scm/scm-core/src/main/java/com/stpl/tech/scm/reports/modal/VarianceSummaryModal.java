/**
 * 
 */
package com.stpl.tech.scm.reports.modal;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 *
 */
public class VarianceSummaryModal {

	private String type;
	private String frequency;
	private String aggType;
	private String aggCode;
	private String aggDesc;
	private BigDecimal value;

	/**
	 * 
	 */
	public VarianceSummaryModal() {
		super();
	}

	/**
	 * @param type
	 * @param frequency
	 * @param aggType
	 * @param aggCode
	 * @param aggDesc
	 * @param value
	 */
	public VarianceSummaryModal(String type, String frequency, String aggType, String aggCode, String aggDesc,
			BigDecimal value) {
		super();
		this.type = type;
		this.frequency = frequency;
		this.aggType = aggType;
		this.aggCode = aggCode;
		this.aggDesc = aggDesc;
		this.value = value;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getFrequency() {
		return frequency;
	}

	public void setFrequency(String frequency) {
		this.frequency = frequency;
	}

	public String getAggType() {
		return aggType;
	}

	public void setAggType(String aggType) {
		this.aggType = aggType;
	}

	public String getAggCode() {
		return aggCode;
	}

	public void setAggCode(String aggCode) {
		this.aggCode = aggCode;
	}

	public String getAggDesc() {
		return aggDesc;
	}

	public void setAggDesc(String aggDesc) {
		this.aggDesc = aggDesc;
	}

	public BigDecimal getValue() {
		return value;
	}

	public void setValue(BigDecimal value) {
		this.value = value;
	}

}
