/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;
// Generated 4 May, 2016 12:06:56 AM by Hibernate Tools 3.2.2.GA

import static javax.persistence.GenerationType.IDENTITY;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

/**
 * UnitVendorMappingData generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "UNIT_VENDOR_MAPPING", uniqueConstraints = @UniqueConstraint(columnNames = {"UNIT_ID", "VENDOR_ID"}))
public class UnitVendorMappingData implements java.io.Serializable {

    private Integer unitVendorMappingId;
    private int unitId;
    private int vendorId;
    private String mappingStatus;
    private String fulfillmentType;
    private String emailNotification;
    private String smsNotification;
    private String notificationTime;
    private String deliveryPromiseTime;
    private Integer notifyDaysBefore;
    private Integer fulfillmentLeadDays;
    private Date creationTime;
	private Date lastUpdateTime;
	private int createdBy;
	private Integer lastUpdatedBy;
	private Integer dispatchLocationId;
    public UnitVendorMappingData() {
    }

    public UnitVendorMappingData(int unitId, int vendorId, String mappingStatus) {
        this.unitId = unitId;
        this.vendorId = vendorId;
        this.mappingStatus = mappingStatus;
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)

    @Column(name = "UNIT_VENDOR_MAPPING_ID", unique = true, nullable = false)
    public Integer getUnitVendorMappingId() {
        return this.unitVendorMappingId;
    }

    public void setUnitVendorMappingId(Integer unitVendorMappingId) {
        this.unitVendorMappingId = unitVendorMappingId;
    }

    @Column(name = "UNIT_ID", nullable = false)
    public int getUnitId() {
        return this.unitId;
    }

    public void setUnitId(int unitId) {
        this.unitId = unitId;
    }

    @Column(name = "VENDOR_ID", nullable = false)
    public int getVendorId() {
        return this.vendorId;
    }

    public void setVendorId(int vendorId) {
        this.vendorId = vendorId;
    }

    @Column(name = "MAPPING_STATUS", nullable = false, length = 15)
    public String getMappingStatus() {
        return this.mappingStatus;
    }

    public void setMappingStatus(String mappingStatus) {
        this.mappingStatus = mappingStatus;
    }
    
    @Column(name = "FULFILMENT_TYPE", nullable = false)
	public String getFulfillmentType() {
		return fulfillmentType;
	}

	public void setFulfillmentType(String fulfillmentType) {
		this.fulfillmentType = fulfillmentType;
	}
	
	@Column(name = "EMAIL_NOTIFICATION", nullable = false)
	public String getEmailNotification() {
		return emailNotification;
	}

	public void setEmailNotification(String emailNotification) {
		this.emailNotification = emailNotification;
	}
	
	@Column(name = "SMS_NOTIFICATION", nullable = false)
	public String getSmsNotification() {
		return smsNotification;
	}

	public void setSmsNotification(String smsNotification) {
		this.smsNotification = smsNotification;
	}
	
	@Column(name = "NOTIFICATION_TIME", nullable = false)
	public String getNotificationTime() {
		return notificationTime;
	}

	public void setNotificationTime(String notificationTime) {
		this.notificationTime = notificationTime;
	}

	@Column(name = "DELIVERY_PROMISE_TIME", nullable = true)
	public String getDeliveryPromiseTime() {
		return deliveryPromiseTime;
	}

	public void setDeliveryPromiseTime(String deliveryPromiseTime) {
		this.deliveryPromiseTime = deliveryPromiseTime;
	}

	@Column(name = "NOTIFY_BEFORE_DAYS", nullable = false)
	public Integer getNotifyDaysBefore() {
		return notifyDaysBefore;
	}

	public void setNotifyDaysBefore(Integer notifyDaysBefore) {
		this.notifyDaysBefore = notifyDaysBefore;
	}
	
	@Column(name = "FULFILLMENT_LEAD_DAYS", nullable = false)
	public Integer getFulfillmentLeadDays() {
		return fulfillmentLeadDays;
	}

	public void setFulfillmentLeadDays(Integer fulfillmentLeadDays) {
		this.fulfillmentLeadDays = fulfillmentLeadDays;
	}

	@Column(name = "CREATION_TIME", nullable = false)
	public Date getCreationTime() {
		return creationTime;
	}

	public void setCreationTime(Date creationTime) {
		this.creationTime = creationTime;
	}
	
	@Column(name = "LAST_UPDATED_TIME", nullable = true)
	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}
	
	@Column(name = "CREATED_BY", nullable = false)
	public int getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(int createdBy) {
		this.createdBy = createdBy;
	}
	
	@Column(name = "LAST_UPDATED_BY", nullable = true)
	public Integer getLastUpdatedBy() {
		return lastUpdatedBy;
	}

	public void setLastUpdatedBy(Integer lastUpdatedBy) {
		this.lastUpdatedBy = lastUpdatedBy;
	}

	@Column(name = "DISPATCH_LOCATION_ID", nullable = true)
	public Integer getDispatchLocationId() {
		return dispatchLocationId;
	}

	public void setDispatchLocationId(Integer dispatchLocationId) {
		this.dispatchLocationId = dispatchLocationId;
	}

	
}
