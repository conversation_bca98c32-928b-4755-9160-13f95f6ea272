package com.stpl.tech.scm.data.model;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToMany;
import javax.persistence.OneToOne;
import javax.persistence.Table;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 18-04-2017.
 */
@Entity
@Table(name = "VENDOR_DETAIL_DATA")
public class VendorDetailData {

    private Integer vendorId;
    private String entityName;
    private String firstName;
    private String lastName;
    private String primaryContact;
    private String secondaryContact;
    private String primaryEmail;
    private String secondaryEmail;
    private String status;
    private String type = "EXTERNAL";
    private Integer requestedBy;
    private Date updatedAt;
    private Integer updatedBy;
    private String disclaimerAccepted;
    private Integer leadTime;
    private String tds;
    private DocumentDetailData tdsDocument;
    private AddressDetailData vendorAddress;
    private VendorCompanyDetailData companyDetails;
    private VendorAccountDetailData accountDetails;
    private Set<VendorDispatchLocationDetailData> dispatchLocations;
    private Set<VendorCompanyDebitMapping> debitMappings ;
    private String vendorBlocked;
    private String vendorBlockedReason;
    private Date unblockedTillDate;
    private Date lastBlockedDate;
    private Integer lastBlockedBy;
    private Date lastUnBlockedDate;
    private Integer lastUnBlockedBy;
    private Integer changeRequestId;
    private String byPassContract;


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "VENDOR_ID", nullable = false, unique = true)
    public Integer getVendorId() {
        return vendorId;
    }

    public void setVendorId(Integer vendorId) {
        this.vendorId = vendorId;
    }

    @Column(name = "ENTITY_NAME", nullable = false)
    public String getEntityName() {
        return entityName;
    }

    public void setEntityName(String entityName) {
        this.entityName = entityName;
    }

    @Column(name = "VENDOR_FIRST_NAME", nullable = false)
    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    @Column(name = "VENDOR_LAST_NAME", nullable = false)
    public String getLastName() {
        return lastName;
    }

    public void setLastName(String lastName) {
        this.lastName = lastName;
    }

    @Column(name = "VENDOR_PRIMARY_CONTACT", nullable = false)
    public String getPrimaryContact() {
        return primaryContact;
    }

    public void setPrimaryContact(String primaryContact) {
        this.primaryContact = primaryContact;
    }

    @Column(name = "VENDOR_SECONDARY_CONTACT", nullable = true)
    public String getSecondaryContact() {
        return secondaryContact;
    }

    public void setSecondaryContact(String secondaryContact) {
        this.secondaryContact = secondaryContact;
    }

    @Column(name = "VENDOR_PRIMARY_EMAIL", nullable = false)
    public String getPrimaryEmail() {
        return primaryEmail;
    }

    public void setPrimaryEmail(String primaryEmail) {
        this.primaryEmail = primaryEmail;
    }

    @Column(name = "VENDOR_SECONDARY_EMAIL", nullable = true)
    public String getSecondaryEmail() {
        return secondaryEmail;
    }

    public void setSecondaryEmail(String secondaryEmail) {
        this.secondaryEmail = secondaryEmail;
    }

    @Column(name = "VENDOR_STATUS", nullable = false)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "VENDOR_TYPE", nullable = false)
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Column(name = "VENDOR_REQUESTED_BY", nullable = false)
    public Integer getRequestedBy() {
        return requestedBy;
    }

    public void setRequestedBy(Integer requestedBy) {
        this.requestedBy = requestedBy;
    }

    @Column(name = "VENDOR_UPDATED_BY", nullable = true)
    public Integer getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Integer updatedBy) {
        this.updatedBy = updatedBy;
    }

    @Column(name = "VENDOR_UPDATED_AT", nullable = false)
    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Column(name = "DISCLAIMER_ACCEPTED", nullable = false)
    public String getDisclaimerAccepted() {
		return disclaimerAccepted;
	}

	public void setDisclaimerAccepted(String disclaimerAccepted) {
		this.disclaimerAccepted = disclaimerAccepted;
	}

	@OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "VENDOR_ADDRESS_ID", nullable = false)
    public AddressDetailData getVendorAddress() {
        return vendorAddress;
    }

    public void setVendorAddress(AddressDetailData vendorAddress) {
        this.vendorAddress = vendorAddress;
    }

    @OneToOne(fetch = FetchType.LAZY, mappedBy = "vendorDetail")
    public VendorCompanyDetailData getCompanyDetails() {
        return companyDetails;
    }

    public void setCompanyDetails(VendorCompanyDetailData companyDetails) {
        this.companyDetails = companyDetails;
    }

    @OneToOne(fetch = FetchType.LAZY, mappedBy = "vendorDetail")
    public VendorAccountDetailData getAccountDetails() {
        return accountDetails;
    }

    public void setAccountDetails(VendorAccountDetailData accountDetails) {
        this.accountDetails = accountDetails;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "vendorDetail")
    public Set<VendorDispatchLocationDetailData> getDispatchLocations() {
        return dispatchLocations;
    }

    public void setDispatchLocations(Set<VendorDispatchLocationDetailData> dispatchLocations) {
        this.dispatchLocations = dispatchLocations;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "detailData")
	public Set<VendorCompanyDebitMapping> getDebitMappings() {
		return debitMappings;
	}

	public void setDebitMappings(Set<VendorCompanyDebitMapping> debitMappings) {
		this.debitMappings = debitMappings;
	}

    @Column(name = "LEAD_TIME", nullable = false)
    public Integer getLeadTime() {
        return leadTime;
    }

    public void setLeadTime(Integer leadTime) {
        this.leadTime = leadTime;
    }

    @Column(name = "TDS_RETURN_STATUS", nullable = true)
    public String getTds() {
        return tds;
    }

    public void setTds(String tds) {
        this.tds = tds;
    }

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "TDS_DOCUMENT", nullable = true)
    public DocumentDetailData getTdsDocument() {
        return tdsDocument;
    }

    public void setTdsDocument(DocumentDetailData tdsDocument) {
        this.tdsDocument = tdsDocument;
    }

    @Column(name = "VENDOR_BLOCKED", nullable = true)
    public String getVendorBlocked() {
        return vendorBlocked;
    }

    public void setVendorBlocked(String vendorBlocked) {
        this.vendorBlocked = vendorBlocked;
    }

    @Column(name = "BLOCKED_REASON", nullable = true)
    public String getVendorBlockedReason() {
        return vendorBlockedReason;
    }

    public void setVendorBlockedReason(String vendorBlockedReason) {
        this.vendorBlockedReason = vendorBlockedReason;
    }

    @Column(name = "UN_BLOCKED_TILL_DATE", nullable = true)
    public Date getUnblockedTillDate() {
        return unblockedTillDate;
    }

    public void setUnblockedTillDate(Date unblockedTillDate) {
        this.unblockedTillDate = unblockedTillDate;
    }

    @Column(name = "LAST_BLOCKED_DATE", nullable = true)
    public Date getLastBlockedDate() {
        return lastBlockedDate;
    }

    public void setLastBlockedDate(Date lastBlockedDate) {
        this.lastBlockedDate = lastBlockedDate;
    }

    @Column(name = "LAST_BLOCKED_BY", nullable = true)
    public Integer getLastBlockedBy() {
        return lastBlockedBy;
    }

    public void setLastBlockedBy(Integer lastBlockedBy) {
        this.lastBlockedBy = lastBlockedBy;
    }

    @Column(name = "LAST_UN_BLOCKED_DATE", nullable = true)
    public Date getLastUnBlockedDate() {
        return lastUnBlockedDate;
    }

    public void setLastUnBlockedDate(Date lastUnBlockedDate) {
        this.lastUnBlockedDate = lastUnBlockedDate;
    }

    @Column(name = "LAST_UN_BLOCKED_BY", nullable = true)
    public Integer getLastUnBlockedBy() {
        return lastUnBlockedBy;
    }

    public void setLastUnBlockedBy(Integer lastUnBlockedBy) {
        this.lastUnBlockedBy = lastUnBlockedBy;
    }

    @Column(name = "CHANGE_REQUEST_ID", nullable = true)
    public Integer getChangeRequestId() {
        return changeRequestId;
    }

    public void setChangeRequestId(Integer changeRequestId) {
        this.changeRequestId = changeRequestId;
    }

    @Column(name = "BY_PASS_CONTRACT")
    public String getByPassContract() {
        return byPassContract;
    }

    public void setByPassContract(String byPassContract) {
        this.byPassContract = byPassContract;
    }
}
