package com.stpl.tech.scm.core.service;

import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.data.transport.model.EWayWrapper;
import com.stpl.tech.scm.data.transport.model.TransportMode;
import com.stpl.tech.scm.domain.model.EWayBill;
import com.stpl.tech.scm.domain.model.EWayResponse;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.TransferOrder;
import com.stpl.tech.scm.domain.model.Vehicle;
import com.stpl.tech.scm.domain.model.VehicleDispatch;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.List;
import java.util.Map;

public interface TransportManagementService {

	public List<TransferOrder> getPendingTransferOrdersForEway(List<Integer> unitIds, Date startDate, Date endDate, List<Integer> receivingUnitIds);

	public List<IdCodeName> getAllVehicles();

	public List<Vehicle> getAllVehiclesByMode(TransportMode transportMode, Date date);

	public VehicleDispatch getVehicleDispatchForDate(Integer vehicleId, String transporter, String docketNumber, Date date, String regNumber) throws SumoException;

	public boolean createConsignment(Integer id, List<Integer> toList) throws SumoException;

	public boolean cancelConsignment(Integer consignmentId) throws SumoException;

	public List<EWayResponse> parseEwayResponseFile(MultipartFile file, Integer dispatchId) throws IOException, NoSuchFieldException, IllegalAccessException;

    public List<EWayResponse> parseEwayResponseFile(InputStream stream, String name, Integer dispatchId) throws IOException, InvalidFormatException, NoSuchFieldException, IllegalAccessException;

	public List<EWayBill> getEwayBills(Integer dispatchId);

	public boolean updateEwayData(List<EWayResponse> list);

	public VehicleDispatch getDispatchById(Integer dispatchId);

	public List<EWayWrapper> generateEWayJson(Integer dispatchId, HttpServletResponse response) throws SumoException , IOException;

	public List<EWayWrapper> startDispatch(Integer dispatchId, boolean forceEway) throws SumoException, IOException;

	public List<VehicleDispatch> getVehicleDispatchHistory(Integer vehicleId, Date d1, Date d2);

	public EWayBill fetchEwayBill(Integer ewayId);

	public void markDispatchAsCompleted(Integer dispatchId);

	public List<VehicleDispatch> searchVehicleDispatchForDate(Integer vehicleId, Date d, String registrationNumber);

	public boolean cancelEWay(Integer ewayId) throws SumoException;

    public Map<String, List<Integer>> getSuggestedUnits(Integer vehicleId, Date date);
}
