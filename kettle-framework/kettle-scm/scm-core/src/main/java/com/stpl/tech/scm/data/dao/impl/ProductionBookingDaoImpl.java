package com.stpl.tech.scm.data.dao.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import javax.persistence.NoResultException;
import javax.persistence.NonUniqueResultException;
import javax.persistence.Query;

import com.stpl.tech.scm.core.service.MappingCache;
import com.stpl.tech.scm.core.util.model.ReverseBookingDetail;
import com.stpl.tech.scm.data.mapper.ReverseProductionBookingMapper;
import com.stpl.tech.scm.data.model.ReverseBookingConsumptionData;
import com.stpl.tech.scm.data.model.ReverseProductionBookingData;
import com.stpl.tech.scm.data.model.ReverseProductionBookingMappingData;
import com.stpl.tech.scm.domain.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.RecipeCache;
import com.stpl.tech.master.recipe.model.IngredientProductDetail;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.core.util.model.BookingDetail;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.dao.ProductionBookingDao;
import com.stpl.tech.scm.data.dao.SkuMappingDao;
import com.stpl.tech.scm.data.model.BookingConsumptionData;
import com.stpl.tech.scm.data.model.ProductionBookingData;
import com.stpl.tech.scm.data.model.ProductionBookingMappingData;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;

@Repository
public class ProductionBookingDaoImpl extends SCMAbstractDaoImpl implements ProductionBookingDao {

	private static final Logger LOG = LoggerFactory.getLogger(ProductionBookingDaoImpl.class);

	@Autowired
	private SCMCache scmCache;

	@Autowired
	private SkuMappingDao skuMappingDao;

	@Autowired
	private RecipeCache recipeCache;

	@Autowired
	private MasterDataCache masterDataCache;

	@Autowired
	private MappingCache mappingCache;

	@Override
	public ProductionBooking calculateConsumption(int unitId, int productId, BigDecimal quantity) throws SumoException, DataNotFoundException {
		ProductDefinition product = scmCache.getProductDefinition(productId);
//		RecipeDetail recipe = recipeCache.getScmRecipe(productId);
		RecipeDetail recipe = recipeCache.getScmRecipe(scmCache.getRecipeProfile(unitId, productId));
		if (product != null && recipe != null) {
			ProductionBooking booking = createBooking(product, recipe.getProfile(), quantity, unitId);
			calculateConsumption(recipe, booking, unitId, recipe.getProfile());
			return booking;
		}else{
			String msg = Objects.isNull(product) ? "Product Cache" : "Recipe";
			throw new SumoException(msg + " missing for Product id :" + productId );
		}

	}

	private void calculateConsumption(RecipeDetail recipe, ProductionBooking booking, int unitId, String profile) throws DataNotFoundException {
		BigDecimal parentQuantity = booking.getQuantity();
		for (IngredientProductDetail pd : recipe.getIngredient().getComponents()) {
			ProductDefinition def = scmCache.getProductDefinition(pd.getProduct().getProductId());
			BigDecimal quantity = parentQuantity.multiply(pd.getQuantity());
			quantity = AppUtils.getQuantityByYield(quantity, pd.getYield());
			booking.getBookingConsumption().add(createBookingConsumption(booking, def, quantity, unitId, profile));
		}
	}

	private BookingConsumption createBookingConsumption(ProductionBooking booking, ProductDefinition def, BigDecimal quantity, int unitId,
			String profile) throws DataNotFoundException {
		BookingConsumption c = new BookingConsumption();
		c.setProductId(def.getProductId());
		c.setProductName(def.getProductName());
		c.setUnitOfMeasure(def.getUnitOfMeasure());
		c.setCalculatedQuantity(quantity);
		c.setProfile(profile);

		// pricing will be calculated while creating consumption
		c.setUnitPrice(null);
		c.setTotalCost(null);

		List<IdCodeName> availableSkus = getAvailableSkusForUnitForProduct(unitId, def.getProductId());
		c.getAvailableSkuList().addAll(availableSkus);
		c.setRecipeRequire(def.isRecipeRequired());
		int skuId = mappingCache.findSKUID(def.getProductId());
		boolean mappingFlag = false;
		try {
			ProductionBookingMappingData productionBookingMappingData = productionUnitMappingItems(booking.getProductId(), unitId, skuId, profile);
			if (productionBookingMappingData != null && productionBookingMappingData.getAutoProduction().equals(SCMServiceConstants.SCM_CONSTANT_YES)) {
				mappingFlag = true;
			}
		} catch (Exception e) {
			LOG.info("Cannot Find Mappings for {} and skudId {}", def.getProductId(), skuId);
		}
		if (def.isAutoProduction() || mappingFlag) {
//			RecipeDetail recipeDetail = recipeCache.getScmRecipe(def.getProductId());
			c.setAutoProduction(def.isAutoProduction());
			c.setMappedAutoProduction(mappingFlag);
			RecipeDetail recipeDetail = recipeCache.getScmRecipe(scmCache.getRecipeProfile(unitId, def.getProductId()));
			if (recipeDetail != null) {
				for (IngredientProductDetail pd : recipeDetail.getIngredient().getComponents()) {
					BigDecimal qty = quantity.multiply(pd.getQuantity());
					qty = AppUtils.getQuantityByYield(qty, pd.getYield());
					ProductDefinition pdef = scmCache.getProductDefinition(pd.getProduct().getProductId());
					c.getBookingConsumption().add(createBookingConsumption(booking, pdef, qty, unitId, profile));
				}
			}
		}
		return c;
	}

	private List<IdCodeName> getAvailableSkusForUnitForProduct(int unitId, Integer productId) {

		List<IdCodeName> list = new ArrayList<IdCodeName>();
		Query query = manager.createQuery(
				"select usm.skuId, sdd.skuName from UnitSkuMapping usm, SkuDefinitionData sdd where sdd.skuId = usm.skuId and usm.unitId = :unitId AND sdd.linkedProduct.productId = :productId AND usm.mappingStatus = :mappingStatus");
		query.setParameter("unitId", unitId);
		query.setParameter("productId", productId);
		query.setParameter("mappingStatus", AppConstants.ACTIVE);
		List<Object[]> results = query.getResultList();
		results.stream().forEach((record) -> {
			IdCodeName o = new IdCodeName();
			o.setId((Integer) record[0]);
			o.setName((String) record[1]);
			list.add(o);
		});
		return list;
	}

	private ProductionBooking createBooking(ProductDefinition product,String profile, BigDecimal quantity, int unitId)
			throws SumoException {
		ProductionBooking booking = new ProductionBooking();
		booking.setProductId(product.getProductId());
		booking.setProductName(product.getProductName());
		booking.setUnitOfMeasure(product.getUnitOfMeasure());
		booking.setUnitId(unitId);
		booking.setCurrentDate(AppUtils.getCurrentDate());
		booking.setExpiryDate(AppUtils.createExpiryDate(AppUtils.getCurrentTimestamp(), product.getShelfLifeInDays()));
		booking.getAvailableDates().addAll(
				AppUtils.createAvailableExpiryDate(AppUtils.getCurrentTimestamp(), product.getShelfLifeInDays()));

		// price calculation required cannot set prices here
		booking.setUnitPrice(null);
		booking.setTotalCost(null);

		booking.setBookingStatus(BookingStatus.INITITATED);
		booking.setQuantity(quantity);
		booking.setProfile(profile);

		/*
		 * List<SkuDefinition> skuList =
		 * productService.viewAllSkuByProduct(product.getProductId())
		 * .get(product.getProductId()).stream().filter(p -> { return
		 * SwitchStatus.ACTIVE.equals(p.getSkuStatus());
		 * }).collect(Collectors.toList());
		 *
		 * if (skuList == null || skuList.isEmpty()) { throw new
		 * SumoException("No ACTIVE SKU for Product: " + product.getProductName()); }
		 * else { // we assume that SEMI-FINISHED product will always have 1 SKU only
		 *  }
		 */
		int skuId = mappingCache.findSKUID(product.getProductId());
		booking.setSkuId(skuId);
		return booking;
	}

	@Override
	public BookingDetail addBooking(ProductionBooking booking) throws SumoException {
		ProductionBookingData data = new ProductionBookingData();
		data.setBookingStatus(BookingStatus.CREATED.name());
		data.setGeneratedBy(booking.getGeneratedBy().getId());
		data.setGenerationTime(AppUtils.getCurrentTimestamp());
		data.setProductId(booking.getProductId());
		data.setProductName(booking.getProductName());
		data.setSkuId(booking.getSkuId());
		data.setQuantity(booking.getQuantity());
		data.setTotalCost(booking.getTotalCost());
		data.setUnitId(booking.getUnitId());
		data.setExpiryDate(AppUtils.formatExpiryDate(booking.getExpiryDate()));
		data.setUnitOfMeasure(booking.getUnitOfMeasure());
		data.setUnitPrice(booking.getUnitPrice());
		data.setProfile(booking.getProfile());
		if(booking.getAutoBooking()==null){
			data.setAutoBooking(SCMServiceConstants.SCM_CONSTANT_NO);
		}else {
			data.setAutoBooking(booking.getAutoBooking());
		}
		data = add(data, true);

		Map<Integer, BookingConsumption> consumptionMap = new HashMap<>();
		for (BookingConsumption c : booking.getBookingConsumption()) {
			flattenConsumptionItems(consumptionMap, c);
		}

		for (Integer key : consumptionMap.keySet()) {
			addBookingConsumption(data, consumptionMap.get(key));
		}

		BookingDetail detail = new BookingDetail();

		detail.setBooking(SCMDataConverter.convert(data, scmCache, masterDataCache));
		detail.getList().addAll(data.getConsumption());
		return detail;
	}

	@Override
	public ReverseBookingDetail addReverseBooking(ReverseProductionBooking booking) throws SumoException {
		ReverseProductionBookingData data = new ReverseProductionBookingData();
		data.setBookingStatus(BookingStatus.CREATED.name());
		data.setGeneratedBy(booking.getGeneratedBy().getId());
		data.setGenerationTime(AppUtils.getCurrentTimestamp());
		data.setProductId(booking.getProductId());
		data.setProductName(booking.getProductName());
		data.setSkuId(booking.getSkuId());
		data.setQuantity(booking.getQuantity());
		data.setTotalCost(booking.getTotalCost());
		data.setUnitId(booking.getUnitId());
		data.setExpiryDate(AppUtils.formatExpiryDate(booking.getExpiryDate()));
		data.setUnitOfMeasure(booking.getUnitOfMeasure());
		data.setUnitPrice(booking.getUnitPrice());
		data.setProfile(booking.getProfile());
		if(booking.getAutoBooking()==null){
			data.setAutoBooking(SCMServiceConstants.SCM_CONSTANT_NO);
		}else {
			data.setAutoBooking(booking.getAutoBooking());
		}
		data = add(data, true);

		Map<Integer, ReverseBookingConsumption> consumptionMap = new HashMap<>();
		for (ReverseBookingConsumption c : booking.getBookingConsumption()) {
			flattenReverseConsumptionItems(consumptionMap, c);
		}
		for (Integer key : consumptionMap.keySet()) {
			addReverseBookingConsumption(data, consumptionMap.get(key));
		}

		ReverseBookingDetail detail = new ReverseBookingDetail();

		detail.setBooking(SCMDataConverter.convert(data, scmCache, masterDataCache));
		detail.getList().addAll(data.getConsumption());
		return detail;
	}

	private void flattenConsumptionItems(Map<Integer, BookingConsumption> consumptionMap, BookingConsumption c) {
		if (c.getBookingConsumption().size() == 0) {
			if (consumptionMap.get(c.getSkuId()) == null) {
				consumptionMap.put(c.getSkuId(), c);
			} else {
				BookingConsumption c1 = consumptionMap.get(c.getSkuId());
				c1.setCalculatedQuantity(SCMUtil.add(c1.getCalculatedQuantity(), c.getCalculatedQuantity()));
				// cost is not available yet
				//c1.setTotalCost(SCMUtil.add(c1.getTotalCost(), c.getTotalCost()));
				consumptionMap.put(c1.getSkuId(), c1);
			}
		} else {
			for (BookingConsumption c1 : c.getBookingConsumption()) {
				flattenConsumptionItems(consumptionMap, c1);
			}
		}
	}

	private void flattenReverseConsumptionItems(Map<Integer, ReverseBookingConsumption> consumptionMap, ReverseBookingConsumption c) {
		if (c.getBookingConsumption().size() == 0) {
			if (consumptionMap.get(c.getSkuId()) == null) {
				consumptionMap.put(c.getSkuId(), c);
			} else {
				ReverseBookingConsumption c1 = consumptionMap.get(c.getSkuId());
				c1.setCalculatedQuantity(SCMUtil.add(c1.getCalculatedQuantity(), c.getCalculatedQuantity()));
				consumptionMap.put(c1.getSkuId(), c1);
			}
		} else {
			for (ReverseBookingConsumption c1 : c.getBookingConsumption()) {
				flattenReverseConsumptionItems(consumptionMap, c1);
			}
		}
	}

	private void addBookingConsumption(ProductionBookingData booking, BookingConsumption c) throws SumoException {
		BookingConsumptionData data = new BookingConsumptionData();
		data.setBooking(booking);
		data.setCalculatedQuantity(c.getCalculatedQuantity());
		if (c.getSkuId() > 0) {
			data.setSkuId(c.getSkuId());
			data.setSkuName(c.getSkuName());
		} else if (c.getSkuId() == 0 && c.getAvailableSkuList() != null && c.getAvailableSkuList().size() > 0) {
			data.setSkuId(c.getAvailableSkuList().get(0).getId());
			data.setSkuName(c.getAvailableSkuList().get(0).getName());
		}
		data.setTotalCost(c.getTotalCost());
		data.setUnitOfMeasure(c.getUnitOfMeasure());
		data.setUnitPrice(c.getUnitPrice());
		add(data, false);
		booking.getConsumption().add(data);
	}
	private void addReverseBookingConsumption(ReverseProductionBookingData booking, ReverseBookingConsumption c) throws SumoException {
		ReverseBookingConsumptionData data = new ReverseBookingConsumptionData();
		data.setBooking(booking);
		data.setCalculatedQuantity(c.getCalculatedQuantity());
		if (c.getSkuId() > 0) {
			data.setSkuId(c.getSkuId());
			data.setSkuName(c.getSkuName());
		} else if (c.getSkuId() == 0 && c.getAvailableSkuList() != null && c.getAvailableSkuList().size() > 0) {
			data.setSkuId(c.getAvailableSkuList().get(0).getId());
			data.setSkuName(c.getAvailableSkuList().get(0).getName());
		}
		data.setTotalCost(c.getTotalCost());
		data.setUnitOfMeasure(c.getUnitOfMeasure());
		data.setUnitPrice(c.getUnitPrice());
		add(data, false);
		booking.getConsumption().add(data);
	}

	@Override
	public List<ProductionBooking> getBookings(Integer unitId, Date startDate, Date endDate, boolean isReverse) {
		if (isReverse) {
			List<ReverseProductionBooking> list = new ArrayList<>();
			Query query = manager.createQuery(
					"FROM ReverseProductionBookingData E WHERE E.unitId = :unitId AND E.generationTime >= :startDate AND E.generationTime <= :endDate");
			query.setParameter("unitId", unitId);
			query.setParameter("startDate", startDate);
			query.setParameter("endDate", SCMUtil.getNextDate(endDate));
			List<ReverseProductionBookingData> results = query.getResultList();
			results.forEach((record) -> {
				list.add(SCMDataConverter.convert(record, scmCache, masterDataCache));
			});
			return ReverseProductionBookingMapper.INSTANCE.toDomainRPBList(list);
		}
		List<ProductionBooking> list = new ArrayList<>();
		Query query = manager.createQuery(
				"FROM ProductionBookingData E WHERE E.unitId = :unitId AND E.generationTime >= :startDate AND E.generationTime <= :endDate");
		query.setParameter("unitId", unitId);
		query.setParameter("startDate", startDate);
		query.setParameter("endDate", SCMUtil.getNextDate(endDate));
		List<ProductionBookingData> results = query.getResultList();
		results.stream().forEach((record) -> {
			list.add(SCMDataConverter.convert(record, scmCache, masterDataCache));
		});
		return list;

	}

	@Override
	public ProductionBooking getLastBooking(Integer unitId, boolean isReverse) {
		if (isReverse) {
			Query query = manager.createQuery(
					"FROM ReverseProductionBookingData E WHERE E.unitId = :unitId ORDER BY E.bookingId DESC");
			query.setParameter("unitId", unitId);
			List<ReverseProductionBookingData> list =  query.getResultList();
			if (list != null && !list.isEmpty()) {
				return ReverseProductionBookingMapper.INSTANCE.toDomain(SCMDataConverter.convert(list.get(0), scmCache, masterDataCache));
			}else {
				return null;
			}
		}
		Query query = manager.createQuery(
			"FROM ProductionBookingData E WHERE E.unitId = :unitId ORDER BY E.bookingId DESC");
		query.setParameter("unitId", unitId);
		List<ProductionBookingData> list =  query.getResultList();
		if (list != null && !list.isEmpty()) {
			return SCMDataConverter.convert(list.get(0), scmCache, masterDataCache);
		}else {
			return null;
		}
	}

	@Override
	public ProductionBookingData cancelBooking(Integer bookingId, Integer empId) {
		ProductionBookingData data = find(ProductionBookingData.class, bookingId);
		if (data != null && BookingStatus.CREATED.name().equals(data.getBookingStatus())) {
			data.setBookingStatus(BookingStatus.CANCELLED.name());
			data.setCancellationTime(AppUtils.getCurrentTimestamp());
			data.setCancelledBy(empId);
			update(data, true);
			return data;
		}
		return null;
	}
	@Override
	public ReverseProductionBookingData cancelReverseBooking(Integer bookingId, Integer empId) {
		ReverseProductionBookingData data = find(ReverseProductionBookingData.class, bookingId);
		if (data != null && BookingStatus.CREATED.name().equals(data.getBookingStatus())) {
			data.setBookingStatus(BookingStatus.CANCELLED.name());
			data.setCancellationTime(AppUtils.getCurrentTimestamp());
			data.setCancelledBy(empId);
			update(data, true);
			return data;
		}
		return null;
	}

	@Override
	public void updateBookingPrice(ProductionBooking booking) {
		ProductionBookingData data = find(ProductionBookingData.class, booking.getBookingId());
		data.setTotalCost(booking.getUnitPrice().multiply(booking.getQuantity()));
		data.setUnitPrice(booking.getUnitPrice());
		update(data, true);
		for (BookingConsumption c : booking.getBookingConsumption()) {
			BookingConsumptionData d = find(BookingConsumptionData.class, c.getId());
			//LOG.info("Log for Consumption: {} {} {} {}",c.getId(), d!= null, c.getUnitPrice(), c.getCalculatedQuantity());
			//LOG.info(JSONSerializer.toJSON(c));
			d.setUnitPrice(c.getUnitPrice().setScale(6, BigDecimal.ROUND_HALF_UP));
			d.setTotalCost(c.getUnitPrice().multiply(c.getCalculatedQuantity()).setScale(6, BigDecimal.ROUND_HALF_UP));
			update(d, false);
		}
		flush();
	}

	@Override
	public void updateReverseBookingPrice(ReverseProductionBooking booking) {
		ReverseProductionBookingData data = find(ReverseProductionBookingData.class, booking.getBookingId());
		data.setTotalCost(booking.getUnitPrice().multiply(booking.getQuantity()));
		data.setUnitPrice(booking.getUnitPrice());
		update(data, true);
		for (ReverseBookingConsumption c : booking.getBookingConsumption()) {
			ReverseBookingConsumptionData d = find(ReverseBookingConsumptionData.class, c.getId());
			d.setUnitPrice(c.getUnitPrice().setScale(6, BigDecimal.ROUND_HALF_UP));
			d.setTotalCost(c.getUnitPrice().multiply(c.getCalculatedQuantity()).setScale(6, BigDecimal.ROUND_HALF_UP));
			update(d, false);
		}
		flush();
	}

	@Override
	public ProductionBookingMappingData productionUnitMappingItems(int productId, int unitId, int linkedSkuId, String profile) throws SumoException{
		List<ProductionBookingMappingData> productionBookingMappingData = new ArrayList<>();
		Query query = manager.createQuery(
			"FROM ProductionBookingMappingData E WHERE E.unitId = :unitId AND E.productId = :productId AND E.mappingStatus = :mappingStatus AND E.linkedSkuId = :linkedSkuId AND E.profile = :profile");
		query.setParameter("unitId", unitId);
		query.setParameter("productId", productId);
		query.setParameter("mappingStatus", AppConstants.ACTIVE);
		query.setParameter("linkedSkuId", linkedSkuId);
		query.setParameter("profile", profile);
		try {
			ProductionBookingMappingData mappingData = (ProductionBookingMappingData) query.getSingleResult();
			return mappingData;
		}catch (NoResultException e){
			return null;
		}catch (NonUniqueResultException e){
			return (ProductionBookingMappingData) query.getResultList().get(0);
		}
	}

	@Override
	public List<ProductionBookingMappingData> findMapping(Integer productId, Integer unitId){
		Query query= manager.createQuery("FROM ProductionBookingMappingData e where e.productId=:productId and e.unitId=:unitId");
		query.setParameter("unitId",unitId);
		query.setParameter("productId",productId);
		try {
			List<ProductionBookingMappingData> list = query.getResultList();
			return list;
		}catch (Exception e){
			LOG.info("error while finding mapping ",e);
			return null;
		}
	}

	@Override
	public List<ReverseProductionBookingMappingData> findReverseMapping(Integer productId, Integer unitId){
		Query query= manager.createQuery("FROM ReverseProductionBookingMappingData e where e.productId=:productId and e.unitId=:unitId");
		query.setParameter("unitId",unitId);
		query.setParameter("productId",productId);
		try {
			List<ReverseProductionBookingMappingData> list = query.getResultList();
			return list;
		}catch (Exception e){
			LOG.info("error while finding mapping ",e);
			return null;
		}
	}

	@Override
	public boolean inactiveProductFromProductionBooking(Integer productId, String profile) {
		Query query = manager.createQuery("UPDATE ProductionBookingMappingData e SET e.mappingStatus=:inactive ,e.cancellationTime=:date where e.productId=:productId and e.profile=:profile");
		query.setParameter("profile", profile);
		query.setParameter("inactive", AppConstants.IN_ACTIVE);
		query.setParameter("productId", productId);
		query.setParameter("date", SCMUtil.getCurrentTimestamp());
		return query.executeUpdate() != 0;
	}

	@Override
	public boolean inactiveMapping(int productId, int unitId) {
		Query query = manager.createQuery("UPDATE ProductionBookingMappingData e SET e.mappingStatus=:inactive " +
			" where e.productId=:productId and e.unitId=:unitId");
		query.setParameter("unitId", unitId);
		query.setParameter("productId", productId);
		query.setParameter("inactive", AppConstants.IN_ACTIVE);
		return query.executeUpdate() != 0;
	}

	@Override
	public  Boolean deleteCostDetailEntryForFixedAsset(Integer bookingId , Integer unitId){
		StringBuilder queryStr = new StringBuilder("DELETE FROM CostDetailDataWh");
		queryStr.append(" WHERE unitId = :unitId AND creationReason = :reason ");
		queryStr.append(" AND creationItemId = :bookingId ");
		Query query = manager.createQuery(queryStr.toString()).setParameter("unitId", unitId)
				.setParameter("bookingId", bookingId)
				.setParameter("reason", StockEventType.BOOKING_RECEIVED.value());
		query.executeUpdate();
		return  true;
	}


}
