package com.stpl.tech.scm.data.model;

import org.joda.time.DateTime;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "PRODUCT_DEFINITION_LOGS")
public class ProductDefinitionLogs {
    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "LOG_ID")
    private Integer logId;

    @Column(name = "PRODUCT_ID")
    private Integer productId;

    @Column(name = "PRODUCT_NAME")
    private String productName;

    @Column(name = "PRODUCT_DESCRIPTION")
    private String productDescription;

    @Column(name = "CATEGORY_ID")
    private Integer categoryId;

    @Column(name = "SUPPORTS_LOOSE_ORDERING")
    private String supportsLooseOrdering;

    @Column(name = "CREATION_DATE")
    private Date creationDate;

    @Column(name = "CREATED_BY")
    private Integer createdBy;

    @Column(name = "HAS_INNER")
    private String hasInner;

    @Column(name = "HAS_CASE")
    private String hasCase;

    @Column(name = "STOCK_KEEPING_FREQUENCY")
    private String stockKeepingFrequency;

    @Column(name = "PRODUCT_CODE")
    private String productCode;

    @Column(name = "SHELF_LIFE_IN_DAYS")
    private Integer shelfLifeInDays;

    @Column(name = "PRODUCT_STATUS")
    private String productStatus;

    @Column(name = "UNIT_OF_MEASURE")
    private String unitOfMeasure;

    @Column(name = "PARTICIPATES_IN_RECIPE")
    private String participatesInRecipe;

    @Column(name = "PARTICIPATES_IN_CAFE_RECIPE")
    private String participatesInCafeRecipe;

    @Column(name = "VARIANT_LEVEL_ORDERING")
    private String variantLevelOrdering;

    @Column(name = "PRODUCT_IMAGE")
    private String productImage;

    @Column(name = "SUB_CATEGORY_ID")
    private Integer subCategoryId;

    @Column(name = "SUPPORTS_SPECIALIZED_ORDERING")
    private String supportsSpecializedOrdering;

    @Column(name = "UNIT_PRICE")
    private BigDecimal unitPrice;

    @Column(name = "NEGOTIATED_UNIT_PRICE")
    private BigDecimal negotiatedUnitPrice;

    @Column(name = "TAX_CATEGORY_CODE")
    private String taxCategoryCode;

    @Column(name = "FULFILLMENT_TYPE")
    private String fulfillmentType;

    @Column(name = "DEFAULT_FULFILLMENT_TYPE")
    private String defaultFulfillmentType;

    @Column(name = "AVAILABLE_AT_CAFE")
    private String availableAtCafe;

    @Column(name = "AVAILABLE_FOR_CAFE_INVENTORY")
    private String availableForCafeInventory;

    @Column(name = "ASSET_ORDERING")
    private String assetOrdering;

    @Column(name = "VARIANCE_TYPE")
    private String varianceType;

    @Column(name = "KITCHEN_VARIANCE_TYPE")
    private String kitchenVarianceType;

    @Column(name = "AUTO_PRODUCTION")
    private String autoProduction;

    @Column(name = "PARTICIPATES_IN_PNL")
    private String participatesInPnl;

    @Column(name = "IS_BULK_GR_ALLOWED")
    private String isBulkGrAllowed;

    @Column(name = "PROFILE_ID")
    private Integer profileId;

    @Column(name = "DIVISION_ID")
    private Integer divisionId;

    @Column(name = "DEPARTMENT_ID")
    private Integer departmentId;

    @Column(name = "CLASSIFICATION_ID")
    private Integer classificationId;

    @Column(name = "SUB_CLASSIFICATION_ID")
    private Integer subClassificationId;

    @Column(name = "RECIPE_REQUIRED")
    private String recipeRequired;

    @Column(name = "INTER_CAFE_TRANSFER")
    private String interCafeTransfer;

    public Integer getLogId() {
        return this.logId;
    }

    public void setLogId(Integer logId) {
        this.logId = logId;
    }

    public Integer getProductId() {
        return this.productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public String getProductName() {
        return this.productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public String getProductDescription() {
        return this.productDescription;
    }

    public void setProductDescription(String productDescription) {
        this.productDescription = productDescription;
    }

    public Integer getCategoryId() {
        return this.categoryId;
    }

    public void setCategoryId(Integer categoryId) {
        this.categoryId = categoryId;
    }

    public String getSupportsLooseOrdering() {
        return this.supportsLooseOrdering;
    }

    public void setSupportsLooseOrdering(String supportsLooseOrdering) {
        this.supportsLooseOrdering = supportsLooseOrdering;
    }

    public Date getCreationDate() {
        return this.creationDate;
    }

    public void setCreationDate(Date creationDate) {
        this.creationDate = creationDate;
    }

    public Integer getCreatedBy() {
        return this.createdBy;
    }

    public void setCreatedBy(Integer createdBy) {
        this.createdBy = createdBy;
    }

    public String getHasInner() {
        return this.hasInner;
    }

    public void setHasInner(String hasInner) {
        this.hasInner = hasInner;
    }

    public String getHasCase() {
        return this.hasCase;
    }

    public void setHasCase(String hasCase) {
        this.hasCase = hasCase;
    }

    public String getStockKeepingFrequency() {
        return this.stockKeepingFrequency;
    }

    public void setStockKeepingFrequency(String stockKeepingFrequency) {
        this.stockKeepingFrequency = stockKeepingFrequency;
    }

    public String getProductCode() {
        return this.productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public Integer getShelfLifeInDays() {
        return this.shelfLifeInDays;
    }

    public void setShelfLifeInDays(Integer shelfLifeInDays) {
        this.shelfLifeInDays = shelfLifeInDays;
    }

    public String getProductStatus() {
        return this.productStatus;
    }

    public void setProductStatus(String productStatus) {
        this.productStatus = productStatus;
    }

    public String getUnitOfMeasure() {
        return this.unitOfMeasure;
    }

    public void setUnitOfMeasure(String unitOfMeasure) {
        this.unitOfMeasure = unitOfMeasure;
    }

    public String getParticipatesInRecipe() {
        return this.participatesInRecipe;
    }

    public void setParticipatesInRecipe(String participatesInRecipe) {
        this.participatesInRecipe = participatesInRecipe;
    }

    public String getParticipatesInCafeRecipe() {
        return this.participatesInCafeRecipe;
    }

    public void setParticipatesInCafeRecipe(String participatesInCafeRecipe) {
        this.participatesInCafeRecipe = participatesInCafeRecipe;
    }

    public String getVariantLevelOrdering() {
        return this.variantLevelOrdering;
    }

    public void setVariantLevelOrdering(String variantLevelOrdering) {
        this.variantLevelOrdering = variantLevelOrdering;
    }

    public String getProductImage() {
        return this.productImage;
    }

    public void setProductImage(String productImage) {
        this.productImage = productImage;
    }

    public Integer getSubCategoryId() {
        return this.subCategoryId;
    }

    public void setSubCategoryId(Integer subCategoryId) {
        this.subCategoryId = subCategoryId;
    }

    public String getSupportsSpecializedOrdering() {
        return this.supportsSpecializedOrdering;
    }

    public void setSupportsSpecializedOrdering(String supportsSpecializedOrdering) {
        this.supportsSpecializedOrdering = supportsSpecializedOrdering;
    }

    public BigDecimal getUnitPrice() {
        return this.unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getNegotiatedUnitPrice() {
        return this.negotiatedUnitPrice;
    }

    public void setNegotiatedUnitPrice(BigDecimal negotiatedUnitPrice) {
        this.negotiatedUnitPrice = negotiatedUnitPrice;
    }

    public String getTaxCategoryCode() {
        return this.taxCategoryCode;
    }

    public void setTaxCategoryCode(String taxCategoryCode) {
        this.taxCategoryCode = taxCategoryCode;
    }

    public String getFulfillmentType() {
        return this.fulfillmentType;
    }

    public void setFulfillmentType(String fulfillmentType) {
        this.fulfillmentType = fulfillmentType;
    }

    public String getDefaultFulfillmentType() {
        return this.defaultFulfillmentType;
    }

    public void setDefaultFulfillmentType(String defaultFulfillmentType) {
        this.defaultFulfillmentType = defaultFulfillmentType;
    }

    public String getAvailableAtCafe() {
        return this.availableAtCafe;
    }

    public void setAvailableAtCafe(String availableAtCafe) {
        this.availableAtCafe = availableAtCafe;
    }

    public String getAvailableForCafeInventory() {
        return this.availableForCafeInventory;
    }

    public void setAvailableForCafeInventory(String availableForCafeInventory) {
        this.availableForCafeInventory = availableForCafeInventory;
    }

    public String getAssetOrdering() {
        return this.assetOrdering;
    }

    public void setAssetOrdering(String assetOrdering) {
        this.assetOrdering = assetOrdering;
    }

    public String getVarianceType() {
        return this.varianceType;
    }

    public void setVarianceType(String varianceType) {
        this.varianceType = varianceType;
    }

    public String getKitchenVarianceType() {
        return this.kitchenVarianceType;
    }

    public void setKitchenVarianceType(String kitchenVarianceType) {
        this.kitchenVarianceType = kitchenVarianceType;
    }

    public String getAutoProduction() {
        return this.autoProduction;
    }

    public void setAutoProduction(String autoProduction) {
        this.autoProduction = autoProduction;
    }

    public String getParticipatesInPnl() {
        return this.participatesInPnl;
    }

    public void setParticipatesInPnl(String participatesInPnl) {
        this.participatesInPnl = participatesInPnl;
    }

    public String getIsBulkGrAllowed() {
        return this.isBulkGrAllowed;
    }

    public void setIsBulkGrAllowed(String isBulkGrAllowed) {
        this.isBulkGrAllowed = isBulkGrAllowed;
    }

    public Integer getProfileId() {
        return this.profileId;
    }

    public void setProfileId(Integer profileId) {
        this.profileId = profileId;
    }

    public Integer getDivisionId() {
        return this.divisionId;
    }

    public void setDivisionId(Integer divisionId) {
        this.divisionId = divisionId;
    }

    public Integer getDepartmentId() {
        return this.departmentId;
    }

    public void setDepartmentId(Integer departmentId) {
        this.departmentId = departmentId;
    }

    public Integer getClassificationId() {
        return this.classificationId;
    }

    public void setClassificationId(Integer classificationId) {
        this.classificationId = classificationId;
    }

    public Integer getSubClassificationId() {
        return this.subClassificationId;
    }

    public void setSubClassificationId(Integer subClassificationId) {
        this.subClassificationId = subClassificationId;
    }

    public String getRecipeRequired() {
        return this.recipeRequired;
    }

    public void setRecipeRequired(String recipeRequired) {
        this.recipeRequired = recipeRequired;
    }

    public String getInterCafeTransfer() {
        return this.interCafeTransfer;
    }

    public void setInterCafeTransfer(String interCafeTransfer) {
        this.interCafeTransfer = interCafeTransfer;
    }
}
