package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "COSTELEMENT_DOCUMENT_MAPPING")

public class CostElementDocumentMapping implements Serializable {
    private Integer costELementDocumentMappingId;
    private int costElementId;
    private int documentId;
    private String createdBy;
    private Date createdAt;

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "COSTELEMENT_DOCUMENT_MAPPING_ID", unique = true, nullable = false)
    public Integer getCostELementDocumentMappingId() {
        return costELementDocumentMappingId;
    }

    public void setCostELementDocumentMappingId(Integer costELementDocumentMappingId) {
        this.costELementDocumentMappingId = costELementDocumentMappingId;
    }

    @Column(name = "COSTELEMENT_ID", nullable = false)
    public int getCostElementId() {
        return costElementId;
    }

    public void setCostElementId(int costElementId) {
        this.costElementId = costElementId;
    }

    @Column(name = "DOCUMENT_ID", nullable = false)
    public int getDocumentId() {
        return documentId;
    }

    public void setDocumentId(int documentId) {
        this.documentId = documentId;
    }

    @Column(name = "CREATED_BY", length = 50)
    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATED_AT", nullable = true, length = 19)
    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }
}
