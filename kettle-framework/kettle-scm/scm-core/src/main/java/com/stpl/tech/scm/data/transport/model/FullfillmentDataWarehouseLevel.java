package com.stpl.tech.scm.data.transport.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class FullfillmentDataWarehouseLevel {
    String transferingUnit;
    Double avgFPer;
    Double  avgImFPer;
    Double criticalAvg;
    String isCriticalProd;
    Double bakeryFP;
    Double withoutBakeryAvgFPer;
}
