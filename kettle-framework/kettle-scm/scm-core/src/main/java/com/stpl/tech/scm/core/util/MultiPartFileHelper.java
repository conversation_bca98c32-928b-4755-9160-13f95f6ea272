/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.core.util;

import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.model.PriceUpdateEntryData;
import com.stpl.tech.scm.data.model.SkuDefinitionData;
import com.stpl.tech.scm.domain.model.FileType;
import com.stpl.tech.scm.domain.model.MimeType;
import com.stpl.tech.scm.domain.model.PriceUpdateEntry;
import com.stpl.tech.scm.domain.model.PriceUpdateEntryType;
import com.stpl.tech.scm.domain.model.PriceUpdateEvent;
import com.stpl.tech.scm.domain.model.PriceUpdateEventStatus;
import com.stpl.tech.util.AppUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.CellType;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.FormulaEvaluator;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;
import org.springframework.web.servlet.view.document.AbstractXlsView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

public class MultiPartFileHelper extends AppUtils {

    private static final Logger LOG = LoggerFactory.getLogger(MultiPartFileHelper.class);

    public static View createView(List<SkuDefinitionData> results, String fileName, String sheetName) {
        return new AbstractXlsView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> map, Workbook workbook,
                                              HttpServletRequest request, HttpServletResponse response) throws Exception {

                String fileNameWithExtention = "\"" + fileName + ".xls\"";
                response.addHeader("Content-Disposition", "attachment; filename=" + fileNameWithExtention);
                int index = workbook.getSheetIndex(sheetName);
                if (index != -1) {
                    workbook.removeSheetAt(index);
                }

                Sheet sheet = workbook.createSheet(sheetName);
                sheet.setDefaultColumnWidth(20);
                CellStyle style = generateHeaderStyle(workbook);

                Row header = sheet.createRow(0);
                header.createCell(0).setCellValue("Key Id");
                header.getCell(0).setCellStyle(style);
                header.createCell(1).setCellValue("Key Name");
                header.getCell(1).setCellStyle(style);
                header.createCell(2).setCellValue("Key Type");
                header.getCell(2).setCellStyle(style);
                header.createCell(3).setCellValue("Unit Of Measure");
                header.getCell(3).setCellStyle(style);
                header.createCell(4).setCellValue("Unit Price");
                header.getCell(4).setCellStyle(style);
                header.createCell(5).setCellValue("Updated Unit Price");
                header.getCell(5).setCellStyle(style);

                int rowCount = 1;
                for (SkuDefinitionData data : results) {
                    Row aRow = sheet.createRow(rowCount++);
                    PriceUpdateEntryData entry = new PriceUpdateEntryData();
                    SCMDataConverter.convert(data, entry);

                    setCell(aRow, 0, entry.getKeyId(), true, workbook);
                    setCell(aRow, 1, entry.getKeyName(), false, workbook);
                    setCell(aRow, 2, entry.getKeyType(), false, workbook);
                    setCell(aRow, 3, entry.getUnitOfMeasure(), false, workbook);
                    setCell(aRow, 4, entry.getUnitPrice(), false, workbook);
                    setCell(aRow, 5, entry.getUpdatedUnitPrice(), false, workbook);
                }
            }
        };
    }

    private static Cell setCell(Row aRow, int i, String value, boolean lockCell, Workbook workbook) {
        Cell cell = aRow.createCell(i);
        cell.setCellType(CellType.STRING);
        cell.setCellValue(value);
        if (lockCell) {
            CellStyle style = workbook.createCellStyle();
            style.setLocked(true);
        }
        return cell;
    }

    private static Cell setCell(Row aRow, int i, int value, boolean lockCell, Workbook workbook) {
        Cell cell = aRow.createCell(i);
        cell.setCellType(CellType.NUMERIC);
        cell.setCellValue(value);
        if (lockCell) {
            CellStyle style = workbook.createCellStyle();
            style.setLocked(true);
        }
        return cell;
    }

    private static Cell setCell(Row aRow, int i, BigDecimal value, boolean lockCell, Workbook workbook) {
        Cell cell = aRow.createCell(i);
        cell.setCellType(CellType.NUMERIC);
        if (value != null) {
            cell.setCellValue(value.doubleValue());
        }
        if (lockCell) {
            CellStyle style = workbook.createCellStyle();
            style.setLocked(true);
        }
        return cell;
    }

    private static CellStyle generateHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setFontName("Arial");
//		font.setBoldweight(HSSFFont.BOLDWEIGHT_BOLD);
        font.setBold(true);
        style.setAlignment(HorizontalAlignment.CENTER);
        style.setFont(font);
        style.setLocked(true);
        return style;
    }

    public static PriceUpdateEvent parseFile(String basePath, MultipartFile file) {
        String fileName = "PriceUpdate-" + AppUtils.getCurrentTimeISTStringWithNoColons() + ".xlsx";
        String path = writeFileToDisk(basePath, "priceUpdates", file, fileName);
        return createEvent(path);
    }

    public static PriceUpdateEvent createEvent(String filePath) {
        PriceUpdateEvent event = new PriceUpdateEvent();
        event.setDataFilePath(filePath);
        try {
            File file = new File(filePath);
            Workbook workbook = WorkbookFactory.create(file);
            FormulaEvaluator evaluator = workbook.getCreationHelper().createFormulaEvaluator();
            Sheet sheet = workbook.getSheetAt(0);
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row aRow = sheet.getRow(i);
                PriceUpdateEntry entry = new PriceUpdateEntry();
                entry.setKeyId(((Double) getCellValue(aRow.getCell(0), evaluator)).intValue());
                entry.setKeyName((String) getCellValue(aRow.getCell(1), evaluator));
                entry.setKeyType(PriceUpdateEntryType.SKU);
                entry.setUnitOfMeasure((String) getCellValue(aRow.getCell(3), evaluator));
                entry.setUnitPrice(new BigDecimal((Double) getCellValue(aRow.getCell(4), evaluator)).setScale(2,
                        BigDecimal.ROUND_HALF_UP));
                entry.setUpdatedUnitPrice(new BigDecimal((Double) getCellValue(aRow.getCell(5), evaluator)).setScale(2,
                        BigDecimal.ROUND_HALF_UP));
                entry.setEditedUnitPrice(entry.getUpdatedUnitPrice());
                entry.setEntryStatus(PriceUpdateEventStatus.INITIATED);
                event.getEntries().add(entry);
            }
        } catch (Exception e) {
            LOG.error("Error while parsing file", e);
            event = null;
        }
        return event;
    }

    private static Object getCellValue(Cell cell, FormulaEvaluator evaluator) {

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue() == null ? "" : cell.getStringCellValue();

            case BOOLEAN:
                return cell.getBooleanCellValue();

            case NUMERIC:
                return cell.getNumericCellValue();
            case FORMULA:
                return evaluator.evaluate(cell).getNumberValue();
            default:
                break;
        }
        return null;
    }

    public static String writeFileToDisk(String rootPath, String fileDir, MultipartFile file, String name) {
        if (!file.isEmpty()) {
            try {
                return AppUtils.write(file.getBytes(), rootPath, fileDir, name, LOG);
            } catch (IOException e) {
                return null;
            }
        } else {
            return null;
        }
    }

    public static String getVendorUploadFileName(FileType type, Integer vendorId, MimeType fileExtension) {
        return "VendorUpload-" + type.name() + "-" + vendorId + "." + fileExtension.name().toLowerCase();
    }
    public static  String getSwitchAssetUploadFileName(FileType type,Integer userId, MimeType ext,Boolean isNewAsset){
        if(isNewAsset){
            return "New_Switch_Asset-"+type.name()+"-"+userId+"."+ext.name().toLowerCase();
        }
        return "Old_Switch_Asset-"+type.name()+"-"+userId+"."+ext.name().toLowerCase();

    }
    public static String getVendorSignatureUploadFileName(FileType type, Integer vendorId, MimeType fileExtension) {
        return "VendorSignatureUpload-" + type.name() + "-" + vendorId + "." + fileExtension.name().toLowerCase();
    }
}
