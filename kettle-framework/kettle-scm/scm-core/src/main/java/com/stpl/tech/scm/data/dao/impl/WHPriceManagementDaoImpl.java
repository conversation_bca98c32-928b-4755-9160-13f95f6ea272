package com.stpl.tech.scm.data.dao.impl;

import com.google.common.base.Stopwatch;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.recipe.model.UnitOfMeasure;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.SCMNotificationService;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.core.util.model.Consumption;
import com.stpl.tech.scm.core.util.model.ConsumptionDrillDown;
import com.stpl.tech.scm.core.util.model.CostDataEntriesWh;
import com.stpl.tech.scm.data.dao.PriceManagementDao;
import com.stpl.tech.scm.data.model.*;
import com.stpl.tech.scm.domain.model.*;
import com.stpl.tech.scm.domain.vo.*;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Repository;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Repository("wh")
@Lazy
public class WHPriceManagementDaoImpl implements PriceManagementDao {
        private static final Logger LOG = LoggerFactory.getLogger(com.stpl.tech.scm.data.dao.impl.PriceManagementDaoImpl.class);
        private static final String CONSUMPTION_AUDIT_EXCEPTION = "INSUFFICIENT_RECEIVING";

        @PersistenceContext(unitName = "SCMDataSourcePUName")
        @Qualifier(value = "SCMDataSourceEMFactory")
        protected EntityManager manager;

        @Autowired
        private SCMNotificationService notificationService;

        @Autowired
        private MutexFactory<Integer> factory;

        @Autowired
        private MasterDataCache cache;

        @Autowired
        private SCMCache scmCache;

        @Autowired
        EnvProperties props;

        // While adding the receivings, we need to do the following
        // 1. create the entry in audit table as well as the drill down table.
        // 2. Incase prices differ from the latest then we need to add a new row and
        // mark it as latest.
        // 3. Incase the prices match then we need to topup the inventory.
        // 4. Incase the prices are not found then a new entry is made.

        public <T extends ReceivingVO> T addReceiving(T rec, boolean cancellation) throws InventoryUpdateException {
            Stopwatch watch1 = Stopwatch.createUnstarted();
            String recordType = rec.getKeyType() == null ? "UNKNOWN" : rec.getKeyType().name();
            watch1.start();
            LOG.info("{} - Step X : Add Receiving Started for ID {} and unitId {} Started", recordType, rec.getKeyId(),
                    rec.getUnitId());
            synchronized (factory.getMutex(rec.getUnitId())) {
                if (rec.isAssetOrder()) {
                    return rec;
                }
                Stopwatch watch = Stopwatch.createUnstarted();
                watch.start();
                LOG.info("{} - Step X1 : Getting current price for ID {} and unitId {} Started", recordType, rec.getKeyId(),
                        rec.getUnitId());
                PriceUpdateEntryType keyType = rec.getInventoryType();
                Date transactionTime = AppUtils.getCurrentTimestamp();
                Map<InventoryPrice, Pair<InventoryItemVO, InventoryItemDrilldown>> receiving = rec.getReceiving();
                List<Integer> keyIds = receiving.keySet().stream().map(InventoryPrice::getId).collect(Collectors.toList());
                Map<InventoryPrice, CostDetailData> current = new HashMap<>();
                List<CostDetailData> list = getCurrentPrices(keyType, rec.getUnitId(), keyIds, false);
                LOG.info("{} - Step X1 : Getting current price for ID {} and unitId {} Completed in {}", recordType,
                        rec.getKeyId(), rec.getUnitId(), watch.stop().elapsed(TimeUnit.MILLISECONDS));
                watch.start();
                LOG.info("{} - Step X2 : Sorting price values for items for ID {} and unitId {} Started", recordType,
                        rec.getKeyId(), rec.getUnitId());
                Map<Integer, CostDetailData> costDetailDataMap = new HashMap<>();
                if (list != null && list.size() > 0) {
                    list.forEach((cost) -> {
                        InventoryPrice price = new InventoryPrice(keyType, cost.getKeyId(), cost.getPrice(),
                                cost.getExpiryDate(), AppUtils.dateToString(cost.getExpiryDate()));
                        // price.setPrice(cost.getPrice());
                        current.put(price, cost);
                        costDetailDataMap.put(cost.getKeyId(), cost); // map of just the last entries for key ids
                    });
                }
                List<Integer> newKeyId = new ArrayList<>();
                for (InventoryPrice price : receiving.keySet()) {
                    if (price.getExpiryDate() == null) {
                        throw new InventoryUpdateException(
                                String.format("Inventory Price is Null for %s", JSONSerializer.toJSON(price)));
                    }
                    // if (!current.containsKey(price)) {
                    // add new price irrespective of found in the CDD list or not
                    newKeyId.add(price.getId());
                    // }
                }
                LOG.info("{} - Step X2 : Sorting price values for items for ID {} and unitId {} Completed in {}",
                        recordType, rec.getKeyId(), rec.getUnitId(), watch.stop().elapsed(TimeUnit.MILLISECONDS));
                watch.start();
                LOG.info("{} - Step X3 : Get old Cost Detail Entries for ID {} and unitId {} Started", recordType,
                        rec.getKeyId(), rec.getUnitId());
                List<Integer> ids = getOldEntries(newKeyId, keyType, rec.getUnitId());
                LOG.info("{} - Step X3 : Get old Cost Detail Entries for ID {} and unitId {} Completed in {}", recordType,
                        rec.getKeyId(), rec.getUnitId(), watch.stop().elapsed(TimeUnit.MILLISECONDS));

                watch.start();
                LOG.info("{} - Step X4 : Update old Cost Detail Entries for ID {} and unitId {} Started", recordType,
                        rec.getKeyId(), rec.getUnitId());
                updateOldEntries(ids);
                LOG.info("{} - Step X4 : Update old Cost Detail Entries for ID {} and unitId {} Completed in {}",
                        recordType, rec.getKeyId(), rec.getUnitId(), watch.stop().elapsed(TimeUnit.MILLISECONDS));
                watch.start();
                LOG.info("{} - Step X5 : Adding New Cost Detail Entries for ID {} and unitId {} Started", recordType,
                        rec.getKeyId(), rec.getUnitId());
                List<InventoryPrice> priceList = new ArrayList<>(receiving.keySet());
                Collections.sort(priceList); // sort on expiry dates with the oldest prices at the last of collection
                Set<Integer> keysProcessed = new HashSet<Integer>();
                for (InventoryPrice price : priceList) {
                    // if (!current.containsKey(price)) {

                    // add new price irrespective of found in the CDD current list or not
                    BigDecimal oldPrice = costDetailDataMap.get(price.getId()) != null
                            ? costDetailDataMap.get(price.getId()).getPrice()
                            : price.getPrice();
                    addNewCostDetail(PriceTransactionType.RECEIVING.value(), keyType, oldPrice, price, rec.getUnitId(),
                            receiving.get(price), transactionTime, cancellation, !keysProcessed.contains(price.getId()));
                    newKeyId.add(price.getId());
                    keysProcessed.add(price.getId());
                    // } else {
                    // updateExistingDetail(current.get(price),
                    // PriceTransactionType.RECEIVING.value(), keyType, price,
                    // rec.getUnitId(), receiving.get(price), transactionTime, cancellation);
                    // }
                }
                manager.flush();
                LOG.info("{} - Step X5 : Adding New Cost Detail Entries for ID {} and unitId {} Completed in {}",
                        recordType, rec.getKeyId(), rec.getUnitId(), watch.stop().elapsed(TimeUnit.MILLISECONDS));
            }
            LOG.info("{} - Step X : Add Receiving Started for ID {} and unitId {} Completed in {}", recordType,
                    rec.getKeyId(), rec.getUnitId(), watch1.stop().elapsed(TimeUnit.MILLISECONDS));
            return rec;

        }

        @Override
        public CostDetailData addNewCostDetailEntry(int unitId, InventoryItemVO item, BigDecimal oldPrice,
                                                    boolean cancellation, boolean isLatest) throws InventoryUpdateException {
            InventoryPrice price = new InventoryPrice(item.getKeyType(), item.getKeyId(), item.getPrice(),
                    item.getExpiryDate(), AppUtils.dateToString(item.getExpiryDate()));
            updateOldEntries(Arrays.asList(item.getKeyId()), item.getKeyType(), unitId);
            CostDetailData costDetailData = addNewCostDetail(PriceTransactionType.MANUAL_ADDITION.value(),
                    item.getKeyType(), oldPrice, price, unitId, item, AppUtils.getCurrentTimestamp(), cancellation,
                    isLatest);
            manager.flush();
            return costDetailData;
        }

        @Override
        public CostDetailData updateCostDetailEntry(int costDetailId, BigDecimal oldPrice, InventoryItemVO item,
                                                    boolean canellation) {
            InventoryPrice price = new InventoryPrice(item.getKeyType(), item.getKeyId(), item.getPrice());
            CostDetailDataWh data = manager.find(CostDetailDataWh.class, costDetailId);
            CostDetailDataWh costDetailData = updateCostDetail(data, PriceTransactionType.MANUAL_OVERRIDE.value(),
                    item.getKeyType(), oldPrice, price, data.getUnitId(), item, AppUtils.getCurrentTimestamp(),
                    canellation);
            return costDetailData;
        }

        /**
         * @param data
         * @param transactionType
         * @param keyType
         * @param price
         * @param unitId
         * @param inventoryItemVO
         * @param transactionTime
         * @param cancellation
         * @return
         */
        private CostDetailDataWh updateCostDetail(CostDetailDataWh data, String transactionType, PriceUpdateEntryType keyType,
                                                BigDecimal oldPrice, InventoryPrice price, int unitId, InventoryItemVO inventoryItemVO,
                                                Date transactionTime, boolean cancellation) {
            data.setLastUpdatedTime(transactionTime);
            data.setQuantity(inventoryItemVO.getQuantity());
            data.setPrice(inventoryItemVO.getPrice());
            addAuditEntry(transactionType, data, keyType, oldPrice, price, unitId, inventoryItemVO, transactionTime,
                    cancellation);
            return data;
        }

        /**
         * @param data
         * @param transactionType
         * @param keyType
         * @param price
         * @param unitId
         * @param inventoryItemVO
         * @param transactionTime
         * @param cancellation
         * @return
         */
        private CostDetailData updateExistingDetail(CostDetailData data, String transactionType,
                                                    PriceUpdateEntryType keyType, InventoryPrice price, int unitId,
                                                    Pair<InventoryItemVO, InventoryItemDrilldown> inventoryItemVO, Date transactionTime, boolean cancellation) {
            data.setLastUpdatedTime(transactionTime);
            // major change here -- changed quantity from inventory item to drill down
            data.setQuantity(AppUtils.add(data.getQuantity(),
                    inventoryItemVO.getValue().getQuantity().subtract(inventoryItemVO.getValue().getRejection())));
            addAuditEntry(transactionType, data, keyType, data.getPrice(), price, unitId, inventoryItemVO, transactionTime,
                    cancellation);
            return data;
        }

        /**
         * @param price
         * @param inventoryItemVO
         */
        private CostDetailData addNewCostDetail(String transactionType, PriceUpdateEntryType keyType, BigDecimal oldPrice,
                                                InventoryPrice price, int unitId, Pair<InventoryItemVO, InventoryItemDrilldown> inventoryItemVO,
                                                Date transactionTime, boolean cancellation, boolean isLatest) throws InventoryUpdateException {
            CostDetailDataWh data = persistCostDetailData(keyType, price, transactionTime, unitId, inventoryItemVO, isLatest);
            addAuditEntry(transactionType, data, keyType, oldPrice, price, unitId, inventoryItemVO, transactionTime,
                    cancellation);
            return data;
        }

        private CostDetailDataWh persistCostDetailData(PriceUpdateEntryType keyType, InventoryPrice price,
                                                     Date transactionTime, int unitId, Pair<InventoryItemVO, InventoryItemDrilldown> itemVO, boolean isLatest) {

            InventoryItemVO item = itemVO.getKey();
            InventoryItemDrilldown drilldown = itemVO.getValue();
            CostDetailDataWh data = new CostDetailDataWh();

            data.setKeyId(price.getId());
            data.setKeyType(keyType.name());
            data.setLastUpdatedTime(transactionTime);
            data.setLatest(isLatest ? SCMServiceConstants.SCM_CONSTANT_YES : SCMServiceConstants.SCM_CONSTANT_NO);
            data.setPrice(price.getPrice());
            data.setQuantity(drilldown.getQuantity().subtract(drilldown.getRejection()));
            data.setUnitId(unitId);
            data.setUom(item.getUom());
            data.setExpiryDate(AppUtils.formatExpiryDate(drilldown.getExpiryDate()));
            data.setCreationReason(item.getItemKeyType().value());
            data.setCreationItemId(item.getItemKeyId());
            manager.persist(data);
            return data;
        }

        private CostDetailDataWh persistCostDetailData(PriceUpdateEntryType keyType, InventoryPrice price,
                                                     Date transactionTime, int unitId, InventoryItemVO itemVO, Date expiryDate, boolean isLatest) {
            CostDetailDataWh data = new CostDetailDataWh();
            data.setKeyId(price.getId());
            data.setKeyType(keyType.name());
            data.setLastUpdatedTime(transactionTime);
            data.setLatest(isLatest ? SCMServiceConstants.SCM_CONSTANT_YES : SCMServiceConstants.SCM_CONSTANT_NO);
            data.setPrice(price.getPrice());
            data.setQuantity(itemVO.getQuantity());
            data.setUnitId(unitId);
            data.setUom(itemVO.getUom());
            data.setExpiryDate(AppUtils.formatExpiryDate(expiryDate));
            data.setCreationReason(itemVO.getItemKeyType().value());
            data.setCreationItemId(itemVO.getItemKeyId());
            manager.persist(data);
            return data;
        }

        /**
         * @param price
         * @param inventoryItemVO
         */
        private CostDetailData addNewCostDetail(String transactionType, PriceUpdateEntryType keyType, BigDecimal oldPrice,
                                                InventoryPrice price, int unitId, InventoryItemVO inventoryItemVO, Date transactionTime,
                                                boolean cancellation, boolean isLatest) throws InventoryUpdateException {
            CostDetailDataWh data = persistCostDetailData(keyType, price, transactionTime, unitId, inventoryItemVO,
                    price.getExpiryDate(), isLatest);
            manager.persist(data);
            addAuditEntry(transactionType, data, keyType, oldPrice, price, unitId, inventoryItemVO, transactionTime,
                    cancellation);
            return data;
        }

        private void updateOldEntries(List<Integer> keyIds, PriceUpdateEntryType keyType, int unitId)
                throws InventoryUpdateException {
            long start = System.currentTimeMillis();
            LOG.info("trying to update cost detail data for key type {} for unit {} starting at milliseconds {}",keyType, unitId, start);
            try {
                if (keyIds == null || keyIds.size() == 0) {
                    return;
                }
                StringBuilder queryStr = new StringBuilder(
                        "Update CostDetailDataWh p set p.latest = :noValue, p.lastUpdatedTime = :updateTime "
                                + "WHERE p.unitId = :unitId " + "AND p.keyId IN(:keyIds) and p.keyType = :keyType"
                                + " and p.latest = :isLatest");
                Query query = manager.createQuery(queryStr.toString());
                query.setParameter("keyType", keyType.name());
                query.setParameter("unitId", unitId);
                query.setParameter("keyIds", keyIds);
                query.setParameter("isLatest", SCMServiceConstants.SCM_CONSTANT_YES);
                query.setParameter("noValue", SCMServiceConstants.SCM_CONSTANT_NO);
                query.setParameter("updateTime", SCMUtil.getCurrentTimestamp());
                query.executeUpdate();
                LOG.info("Successfully updated cost detail data for key type {}", keyType);
            } catch (Exception e) {
                String message = String.format("Error while updating old pricing entry for %s unit :::: %s", keyType.name(),
                        unitId);
                LOG.error("Error updating cost detail logger {} and in {} milliseconds", e.getMessage(), System.currentTimeMillis() - start);
                LOG.info("######### key Id,{}", StringUtils.join(keyIds, ","));
                throw new InventoryUpdateException(message, e);
            }
        }

        private void updateOldEntries(List<Integer> ids) throws InventoryUpdateException {
            long start = System.currentTimeMillis();
            try {
                if (ids == null || ids.size() == 0) {
                    return;
                }
                StringBuilder queryStr = new StringBuilder(
                        "Update CostDetailDataWh p set p.latest = :noValue, p.lastUpdatedTime = :updateTime "
                                + "WHERE p.costDetailDataId IN(:ids)");
                Query query = manager.createQuery(queryStr.toString());
                query.setParameter("ids", ids);
                query.setParameter("noValue", SCMServiceConstants.SCM_CONSTANT_NO);
                query.setParameter("updateTime", SCMUtil.getCurrentTimestamp());
                query.executeUpdate();
                LOG.info("Successfully updated cost detail data for key ids {}", ids);
            } catch (Exception e) {
                String message = String.format("Error while updating old pricing entry for %s", ids);
                LOG.error("Error updating cost detail logger {} and in {} milliseconds", e.getMessage(),
                        System.currentTimeMillis() - start);
                LOG.info("######### key Id,{}", StringUtils.join(ids, ","));
                throw new InventoryUpdateException(message, e);
            }
        }

        private List<Integer> getOldEntries(List<Integer> keyIds, PriceUpdateEntryType keyType, int unitId)
                throws InventoryUpdateException {
            long start = System.currentTimeMillis();
            LOG.info("trying to update cost detail data for key type {} for unit {} starting at milliseconds {}", keyType,
                    unitId, start);
            List<Integer> ids = new ArrayList<>();

            try {
                if (keyIds == null || keyIds.size() == 0) {
                    return ids;
                }
                StringBuilder queryStr = new StringBuilder(
                        "select costDetailDataId from CostDetailDataWh p  " + "WHERE p.unitId = :unitId "
                                + "AND p.keyId IN(:keyIds) and p.keyType = :keyType" + " and p.latest = :isLatest");
                Query query = manager.createQuery(queryStr.toString());
                query.setParameter("keyType", keyType.name());
                query.setParameter("unitId", unitId);
                query.setParameter("keyIds", keyIds);
                query.setParameter("isLatest", SCMServiceConstants.SCM_CONSTANT_YES);
                List<Object> list = query.getResultList();
                if (list != null && list.size() > 0) {
                    for (Object o : list) {
                        ids.add((Integer) o);
                    }
                }
                LOG.info("Successfully updated cost detail data for key type {}", keyType);
                return ids;
            } catch (Exception e) {
                String message = String.format("Error while updating old pricing entry for %s unit :::: %s", keyType.name(),
                        unitId);
                LOG.error("Error updating cost detail logger {} and in {} milliseconds", e.getMessage(),
                        System.currentTimeMillis() - start);
                LOG.info("######### key Id,{}", StringUtils.join(keyIds, ","));
                throw new InventoryUpdateException(message, e);
            }
        }

        /**
         * @param transactionType
         * @param data
         * @param keyType
         * @param unitId
         * @param consumptions
         * @param transactionTime
         * @param cancellation
         */
        private void addAuditEntry(String transactionType, CostDetailData data, PriceUpdateEntryType keyType, int unitId,
                                   List<ConsumptionDrillDown> consumptions, Date transactionTime, boolean cancellation, Date expiryDate) throws InventoryUpdateException {
            if (consumptions == null || consumptions.size() == 0) {
                return;
            }

            InventoryItemVO inventoryItemVO = consumptions.get(0).getItem();
            BigDecimal quantity = BigDecimal.ZERO;

            for (ConsumptionDrillDown item : consumptions) {
                quantity = AppUtils.add(quantity, item.getQuantity());
            }
            CostDetailData costDetailData = null;
            try{
                costDetailData = getCurrentPrice(keyType, unitId, inventoryItemVO.getKeyId(), true).get(0);
            }catch (Exception e){
                throw  new InventoryUpdateException("No Current Price Found For " + keyType + " Id : " + inventoryItemVO.getKeyId());
            }
            CostDetailWhAuditData audit = createCostAuditObject(transactionType, data, keyType, costDetailData.getPrice(),
                    inventoryItemVO.getPrice(), unitId, inventoryItemVO.getKeyId(), inventoryItemVO.getUom(), quantity,
                    transactionTime, cancellation, expiryDate);

            for (ConsumptionDrillDown consumption : consumptions) {
                addDrillDown(convert(consumption), audit, inventoryItemVO.getUom(), transactionTime, cancellation);
                addDrillDown(convertToItem(consumption), audit, inventoryItemVO.getUom(), transactionTime, cancellation);
            }
        }

        /**
         * @param consumption
         * @return
         */
        private InventoryItemDrilldown convertToItem(ConsumptionDrillDown consumption) {
            InventoryItemDrilldown drillDown = new InventoryItemDrilldown();
            drillDown.setKeyId(consumption.getItem().getItemKeyId());
            drillDown.setKeyType(consumption.getItem().getItemKeyType().name());
            drillDown.setPrice(consumption.getPrice());
            drillDown.setQuantity(consumption.getQuantity());
            drillDown.setExpiryDate(consumption.getData().getExpiryDate());
            return drillDown;
        }

        /**
         * @param consumption
         * @return
         */
        private InventoryItemDrilldown convert(ConsumptionDrillDown consumption) {
            InventoryItemDrilldown drillDown = new InventoryItemDrilldown();
            drillDown.setKeyId(consumption.getData().getCostDetailDataId());
            drillDown.setKeyType(consumption.isVariance() ? "Variance" : "CostDetailData");
            drillDown.setPrice(consumption.getPrice());
            drillDown.setQuantity(consumption.getQuantity());
            drillDown.setExpiryDate(consumption.getData().getExpiryDate());
            return drillDown;
        }

        /**
         * @param keyType
         * @param price
         * @param data
         * @param inventoryItemVO
         * @param transactionTime
         */
        private void addAuditEntry(String transactionType, CostDetailData data, PriceUpdateEntryType keyType,
                                   BigDecimal oldPrice, InventoryPrice price, int unitId,
                                   Pair<InventoryItemVO, InventoryItemDrilldown> inventoryItemVO, Date transactionTime, boolean canellation) {

            CostDetailWhAuditData audit = createCostAuditObject(transactionType, data, keyType, oldPrice, price.getPrice(),
                    unitId, inventoryItemVO.getKey().getKeyId(), inventoryItemVO.getKey().getUom(),
                    inventoryItemVO.getValue().getQuantity().subtract(inventoryItemVO.getValue().getRejection()),
                    transactionTime, canellation, inventoryItemVO.getValue().getExpiryDate());
            for (InventoryItemDrilldown drillDown : inventoryItemVO.getKey().getDrillDowns()) {
                addDrillDown(drillDown, audit, inventoryItemVO.getKey().getUom(), transactionTime, canellation);
            }
        }

        /**
         * @param keyType
         * @param price
         * @param data
         * @param inventoryItemVO
         * @param transactionTime
         */
        private void addAuditEntry(String transactionType, CostDetailData data, PriceUpdateEntryType keyType,
                                   BigDecimal oldPrice, InventoryPrice price, int unitId, InventoryItemVO inventoryItemVO,
                                   Date transactionTime, boolean canellation) {

            CostDetailWhAuditData audit = createCostAuditObject(transactionType, data, keyType, oldPrice, data.getPrice(),
                    unitId, inventoryItemVO.getKeyId(), inventoryItemVO.getUom(), inventoryItemVO.getQuantity(),
                    transactionTime, canellation, price.getExpiryDate());

            for (InventoryItemDrilldown drillDown : inventoryItemVO.getDrillDowns()) {
                addDrillDown(drillDown, audit, inventoryItemVO.getUom(), transactionTime, canellation);
            }
        }

        private CostDetailWhAuditData createCostAuditObject(String transactionType, CostDetailData data,
                                                          PriceUpdateEntryType keyType, BigDecimal oldPrice, BigDecimal price, int unitId, int keyId, String uom,
                                                          BigDecimal quantity, Date transactionTime, boolean canellation, Date expiryDate) {
            CostDetailWhAuditData audit = new CostDetailWhAuditData();
            audit.setAddTime(transactionTime);
            audit.setCostDetailDataId(data.getCostDetailDataId());
            audit.setKeyId(keyId);
            audit.setKeyType(keyType.name());
            audit.setOldPrice(oldPrice);
            audit.setPrice(price);
            audit.setQuantity(quantity);
            audit.setTransactionType(transactionType);
            audit.setUnitId(unitId);
            audit.setUom(uom);
            audit.setExpiryDate(AppUtils.formatExpiryDate(expiryDate));
            audit.setCancellation(AppConstants.getValue(canellation));
            manager.persist(audit);
            return audit;
        }

        /**
         * @param drillDown
         * @param data
         * @param uom
         * @param transactionTime
         * @param cancellation
         */
        private void addDrillDown(InventoryItemDrilldown drillDown, CostDetailWhAuditData data, String uom,
                                  Date transactionTime, boolean cancellation) {
            CostDetailWhAuditDrilldownData audit = new CostDetailWhAuditDrilldownData();
            audit.setAddTime(transactionTime);
            audit.setCostDetailAuditDataId(data.getCostDetailAuditDataId());
            audit.setKeyId(drillDown.getKeyId());
            audit.setKeyType(drillDown.getKeyType());
            audit.setPrice(drillDown.getPrice());
            audit.setQuantity(drillDown.getQuantity());
            audit.setUom(uom);
            audit.setCancellation(AppConstants.getValue(cancellation));
            audit.setExpiryDate(AppUtils.formatExpiryDate(drillDown.getExpiryDate()));
            manager.persist(audit);

        }

        // While reducing the consumption we need to do the following
        // 1. get all the inventory data for all the listed products in the
        // consumption VO.
        // 2. Try to fulfill all the required inventory for consumable using the
        // fifo for quantity of a product or a cafe.
        // 3. Get the weighted mean of the product/sku and set against each item.
        // 4. Set the drilldowns for each entry from the inventory entries being
        // consumed at that point of time.
        public <T extends ErrorsVO> T verifyPriceData(T rec) {
            UnitBasicDetail unit = cache.getUnitBasicDetail(rec.getUnitId());
            PriceUpdateEntryType keyType = rec.getInventoryType();
            List<Integer> keyIds = new ArrayList<Integer>(rec.getConsumption().keySet());
            Map<Integer, List<Consumption>> current = new HashMap<>();
            List<CostDetailData> list = getCurrentPrices(keyType, unit.getId(), keyIds, false);
            if (list != null && list.size() > 0) {
                list.stream().forEach((cost) -> {
                    Consumption c = new Consumption(cost);
                    if (!current.containsKey(cost.getKeyId())) {
                        current.put(cost.getKeyId(), new ArrayList<>());
                    }
                    current.get(cost.getKeyId()).add(c);
                });
            }
            List<String> errors = new ArrayList<>();
            for (InventoryItemVO item : rec.getInventoryItems()) {
                if (item.getQuantity().compareTo(BigDecimal.ZERO) > 0) {
                    List<Consumption> data = current.get(item.getKeyId());
                    if (data == null || data.isEmpty()) {
                        errors.add(logError(rec, item, unit));
                    }
                }
            }
            if (errors != null && errors.size() > 0) {
                String message = "Error while creating " + rec.getKeyType().name() + "\n"
                        + Arrays.toString(errors.toArray());
                rec.setErrors(errors);
                if (!rec.getKeyType().equals(StockEventType.STOCK_TAKE)) {
                    notificationService.sendInventoryErrorNotification(message);
                }
            }
            return rec;
        }

        // While reducing the consumption we need to do the following
        // 1. get all the inventory data for all the listed products in the
        // consumption VO.
        // 2. Try to fulfill all the required inventory for consumable using the
        // fifo for quantity of a product or a cafe.
        // 3. Get the weighted mean of the product/sku and set against each item.
        // 4. Set the drilldowns for each entry from the inventory entries being
        // consumed at that point of time.

        public <T extends ConsumptionVO> T reduceConsumable(T rec, boolean cancellation) throws InventoryUpdateException, DataNotFoundException {

            return reduceConsumable(rec, cancellation, false);
        }

        public <T extends ConsumptionVO> T reduceConsumable(T rec, boolean cancellation, boolean dataCheck)
                throws InventoryUpdateException, DataNotFoundException {
            if (rec.isAssetOrder()) {
                return rec;
            }
            UnitBasicDetail unit = cache.getUnitBasicDetail(rec.getUnitId());
            PriceUpdateEntryType keyType = rec.getInventoryType();
            Date transactionTime = AppUtils.getCurrentTimestamp();

            // get available Cost detail entries
            List<Integer> keyIds = new ArrayList<Integer>(rec.getConsumption().keySet());
            boolean createMissing = rec instanceof ConsumptionData;
            LOG.info("Create Missing Entries for ConsumptionData for reduce consumables = " + createMissing);
            CostDataEntriesWh d = getOrCreateCurrentPrices(keyType, unit.getId(), keyIds, false, createMissing , false);

            // create consumption values map key Id -> List of entries
            Map<Integer, List<Consumption>> current = new HashMap<>();
            if (d.getData() != null && d.getData().size() > 0) {
                d.getData().forEach((cost) -> {
                    Consumption c = new Consumption(cost);
                    if (!current.containsKey(cost.getKeyId())) {
                        current.put(cost.getKeyId(), new ArrayList<>());
                    }
                    current.get(cost.getKeyId()).add(c);
                });
            }

            List<String> errors = new ArrayList<>();
            boolean error = false;
            for (InventoryItemVO item : rec.getInventoryItems()) {
                if (item.getQuantity().compareTo(BigDecimal.ZERO) > 0) {
                    List<Consumption> data = current.get(item.getKeyId());
                    if (data == null || data.isEmpty()) {
                        error = true;
                        errors.add(logError(rec, item, unit));
                    }
                    if (!error) {
                        // item = item to be consumed
                        // data = available cost detail data entries for consumption
                        fullFillInventory(item, data);
                    }
                }

                if (item.getQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                    List<Consumption> data = current.get(item.getKeyId());
                    if (data == null || data.isEmpty()) {
                        error = true;
                        errors.add(logError(rec, item, unit));
                    }
                    if (!error && d.getData() != null && d.getData().size() > 0) {
                        d.getData().forEach((cost) -> {
                            if (cost.getKeyId() == item.getKeyId()) {
                                item.setPrice(cost.getPrice());
                            }
                        });
                    }
                }
            }

            if (errors != null && errors.size() > 0) {
                String message = "Error while creating " + rec.getKeyType().name() + "\n"
                        + Arrays.toString(errors.toArray());
                if (!rec.getKeyType().equals(StockEventType.STOCK_TAKE)) {
                    notificationService.sendInventoryErrorNotification(message);
                }
                SlackNotificationService.getInstance().sendNotification(props.getEnvType(), "SUMO",
                        SlackNotification.SUPPLY_CHAIN, message.toString());
                throw new InventoryUpdateException(message);
            }

            if (d.getErrors() != null && d.getErrors().size() > 0) {
                String message = "Created Missing Price Entries " + rec.getKeyType().name() + "\n"
                        + printErrors(rec, d.getErrors(), unit);
                if (!rec.getKeyType().equals(StockEventType.STOCK_TAKE)) {
                    notificationService.sendInventoryErrorNotification(message);
                }
            }

            // this applies the reduced values to cost detail entries
            if (!dataCheck) {
                for (Integer keyId : current.keySet()) {
                    List<Consumption> consumptions = current.get(keyId);
                    for (Consumption c : consumptions) {
                        if (c.isConsumed()) {
                            CostDetailDataWh data = (CostDetailDataWh) c.getDetail();
                            if (c.isVariance()) {
                                data.setQuantity(BigDecimal.ZERO);
                            } else {
                                data.setQuantity(c.getCurrentQuantity());
                            }
                            data.setLastUpdatedTime(transactionTime);
                            for (Integer key : c.getDrillDowns().keySet()) {
                                List<ConsumptionDrillDown> datas = c.getDrillDowns().get(key);
                                addAuditEntry(PriceTransactionType.CONSUMPTION.value(), data, keyType, rec.getUnitId(),
                                        datas, transactionTime, cancellation, c.getDetail().getExpiryDate());
                            }
                        }
                        // Add consumption audit to back track to Cost Detail Data
                        if (c.isConsumed()) {
                            manager.persist(getConsumptionAudit(c));
                        }
                    }
                }
                manager.flush();
            }
            return rec;
        }

        private ConsumptionAuditData getConsumptionAudit(Consumption c) {
            ConsumptionAuditData consumptionAudit = new ConsumptionAuditData();
            CostDetailData data = c.getDetail();
            consumptionAudit.setCostDetailId(data.getCostDetailDataId());
            consumptionAudit.setKeyId(data.getKeyId());
            consumptionAudit.setKeyType(data.getKeyType());
            consumptionAudit.setTransactionItemId(c.getTxnItemId());
            consumptionAudit.setTransactionType(c.getTxnType().value());
            consumptionAudit.setQuantity(c.getItemQuantity());
            consumptionAudit.setCostDetailQuantity(data.getQuantity());
            consumptionAudit.setPrice(data.getPrice());
            consumptionAudit.setUom(data.getUom());
            consumptionAudit.setLastUpdatedTime(data.getLastUpdatedTime());
            consumptionAudit.setExpiryDate(AppUtils.formatExpiryDate(data.getExpiryDate()));
            consumptionAudit.setCreationReason(data.getCreationReason());
            consumptionAudit.setCreationItemId(data.getCreationItemId());
            consumptionAudit.setException(getExceptionReason(data, c.isVariance()));
            return consumptionAudit;
        }

        private String getExceptionReason(CostDetailData detail, boolean variance) {
            if (StockEventType.MANUAL_ENTRY.name().equalsIgnoreCase(detail.getCreationReason())) {
                return StockEventType.MANUAL_ENTRY.name();
            }
            if (variance) {
                return CONSUMPTION_AUDIT_EXCEPTION;
            }
            return null;
        }

        @Override
        public <T extends ConsumptionVO> T checkConsumableData(T rec) throws InventoryUpdateException, DataNotFoundException {
            return reduceConsumable(rec, false, true);
        }

        @Override
        public List<CostDetailData> getCurrentPrice(PriceUpdateEntryType keyType, int unitId, Integer keyId,
                                                    boolean current) {
            if (keyId == null) {
                return new ArrayList<>();
            }
            List<Integer> keyIds = new ArrayList<>();
            keyIds.add(keyId);
            return getCurrentPrices(keyType, unitId, keyIds, current);
        }

        @Override
        public CostDataEntriesWh getOrCreateCurrentPrices(PriceUpdateEntryType keyType, int unitId, List<Integer> keyIds,
                                                        boolean current, boolean createMissig , Boolean missingToPickFromProduct) throws DataNotFoundException {
            if (keyIds == null || keyIds.size() == 0) {
                return new CostDataEntriesWh();
            }
            StringBuilder queryStr = new StringBuilder(
                    "FROM CostDetailDataWh p WHERE p.unitId = :unitId " + "AND p.keyId IN(:keyIds) and p.keyType = :keyType");
            if (current) {
                queryStr.append(" AND p.latest = :isLatest");
            }
            queryStr.append(" ORDER BY p.expiryDate, p.costDetailDataId");
            Query query = manager.createQuery(queryStr.toString());
            query.setParameter("keyType", keyType.name());
            query.setParameter("unitId", unitId);
            query.setParameter("keyIds", keyIds);

            if (current) {
                query.setParameter("isLatest", SCMServiceConstants.SCM_CONSTANT_YES);
            }

            List<CostDetailDataWh> list = query.getResultList();
            return getOrCreateCostDetail(keyType, unitId, keyIds, createMissig, list,missingToPickFromProduct);
        }

        @Override
        public List<CostDetailData> getCurrentPrices(PriceUpdateEntryType keyType, int unitId, List<Integer> keyIds,
                                                     boolean current) {
            if (keyIds == null || keyIds.size() == 0) {
                return new ArrayList<>();
            }
            StringBuilder queryStr = new StringBuilder(
                    "FROM CostDetailDataWh p WHERE p.unitId = :unitId " + "AND p.keyId IN(:keyIds) and p.keyType = :keyType");
            if (current) {
                queryStr.append(" AND p.latest = :isLatest");
            }
            queryStr.append(" ORDER BY p.expiryDate, p.costDetailDataId");
            Query query = manager.createQuery(queryStr.toString());
            query.setParameter("keyType", keyType.name());
            query.setParameter("unitId", unitId);
            query.setParameter("keyIds", keyIds);

            if (current) {
                query.setParameter("isLatest", SCMServiceConstants.SCM_CONSTANT_YES);
            }

            return query.getResultList();
        }





        private CostDataEntriesWh getAllPrices(PriceUpdateEntryType keyType, int unitId, List<Integer> keyIds,
                                             boolean createMissing) throws DataNotFoundException {
            if (keyIds == null || keyIds.size() == 0) {
                return new CostDataEntriesWh();
            }
            StringBuilder queryStr = new StringBuilder("FROM CostDetailDataWh p WHERE p.unitId = :unitId ");
            queryStr.append("AND p.keyId IN(:keyIds) AND p.keyType = :keyType AND ");
            queryStr.append("((p.latest = :isLatest) OR (p.latest != :isLatest AND p.quantity > 0.00)) ");
            queryStr.append("ORDER BY p.keyId, p.costDetailDataId DESC");

            Query query = manager.createQuery(queryStr.toString());
            query.setParameter("keyType", keyType.name());
            query.setParameter("unitId", unitId);
            query.setParameter("keyIds", keyIds);
            query.setParameter("isLatest", SCMServiceConstants.SCM_CONSTANT_YES);
            List<CostDetailDataWh> list = query.getResultList();
            return getOrCreateCostDetail(keyType, unitId, keyIds, createMissing, list, false);
        }



        private CostDataEntriesWh getOrCreateCostDetail(PriceUpdateEntryType keyType, int unitId, List<Integer> keyIds,
                                                      boolean createMissing, List<CostDetailDataWh> list , Boolean missingToPickFromProduct) throws DataNotFoundException {
            CostDataEntriesWh d = new CostDataEntriesWh();
            LOG.info("Step 1 : In Create Cost Detail with createMissing = " + createMissing);
            if (!createMissing) {
                d.setData(list);
                return d;
            }
            List<CostDetailDataWh> costDetailDatas = new ArrayList<>();
            Set<Integer> availableKeyIds = new HashSet<>();
            Set<Integer> missingKeyIds = new HashSet<>();
            if (list != null) {
                for (CostDetailDataWh data : list) {
                    costDetailDatas.add(data);
                    availableKeyIds.add(data.getKeyId());
                }
            }
            LOG.info("Step 2 : Identified Available Key Ids = " + availableKeyIds.size() + " Cost Details Size = "
                    + costDetailDatas.size());
            for (Integer keyId : keyIds) {
                if (!availableKeyIds.contains(keyId)) {
                    missingKeyIds.add(keyId);
                }
            }
            LOG.info("Step 3 : Identified Missing Key Ids = " + missingKeyIds.size() + " Cost Details Size = "
                    + costDetailDatas.size());

            if (missingKeyIds != null && missingKeyIds.size() > 0) {
                UnitBasicDetail unit = cache.getUnitBasicDetail(unitId);
                String region = unit.getRegion();
                List<String> missingPriceErrors = new ArrayList<>();
                for (Integer key : missingKeyIds) {
                    LOG.info("Step 4.1 : Looking for Price for missing key Id = " + key + " Cost Details Size = "
                            + costDetailDatas.size());
                    CostDetailDataWh copyPrice = null;
                    try {
                        copyPrice = getCurrentPriceForAnyUnitInRegion(keyType, key, region);
                    } catch (DataNotFoundException e) {
                        if(missingToPickFromProduct.equals(Boolean.TRUE)){
                            if(PriceUpdateEntryType.PRODUCT.equals(keyType)) {
                                ProductDefinitionData productDefinitionData = manager.find(ProductDefinitionData.class, key);
                                if(BigDecimal.ZERO.compareTo(productDefinitionData.getNegotiatedUnitPrice()) == 0){
                                    String errorMsg = "Pricing entry not found product " + productDefinitionData.getProductName();
                                    missingPriceErrors.add(errorMsg);
                                }
                                copyPrice = new CostDetailDataWh();
                                copyPrice.setKeyId(key);
                                copyPrice.setExpiryDate(AppUtils.formatExpiryDate(AppUtils.addDays(AppUtils.getCurrentDate(),productDefinitionData.getShelfLifeInDays())));
                                copyPrice.setKeyType(keyType.value());
                                copyPrice.setLatest(AppUtils.setStatus(true));
                                copyPrice.setPrice(productDefinitionData.getNegotiatedUnitPrice());
                                copyPrice.setQuantity(BigDecimal.ZERO);
                                copyPrice.setUnitId(unitId);
                                copyPrice.setUom(productDefinitionData.getUnitOfMeasure());
                            }else{
                                String errorMsg = "Pricing entry not found key: " + key
                                        +" type: " + keyType;
                                missingPriceErrors.add(errorMsg);
                            }
                        }else{
                            if(PriceUpdateEntryType.PRODUCT.equals(keyType)) {
                                ProductDefinitionData productDefinitionData = manager.find(ProductDefinitionData.class, key);
                                String errorMsg = "Pricing entry not found product " + productDefinitionData.getProductName();
                                missingPriceErrors.add(errorMsg);
                            }
                            String errorMsg = "Pricing entry not found key: " + key
                                    +" type: " + keyType;
                            missingPriceErrors.add(errorMsg);
                        }


                    }
                    if (copyPrice != null) {
                        LOG.info("Step 4.2 : Found Price for missing key Id = " + key + " Cost Details Size = "
                                + costDetailDatas.size() + " from unit = " + copyPrice.getUnitId());
                        CostDetail detail = new CostDetail();
                        detail.setKeyId(key);
                        detail.setExpiryDate(AppUtils.formatExpiryDate(copyPrice.getExpiryDate()));
                        detail.setKeyType(keyType);
                        detail.setLatest(true);
                        detail.setPrice(copyPrice.getPrice());
                        detail.setQuantity(BigDecimal.ZERO);
                        detail.setUnitId(unitId);
                        detail.setUom(copyPrice.getUom());
                        try {
                            LOG.info("Step 4.3 : Adding Cost Detail Price Entry for missing key Id = " + key
                                    + " Cost Details Size = " + costDetailDatas.size() + " from unit = "
                                    + copyPrice.getUnitId());
                            CostDetailDataWh newData = (CostDetailDataWh) addNewCostDetailEntry(unitId, detail, null, false, true);
                            LOG.info("Step 4.4 : Added Cost Detail Price Entry for missing key Id = " + key
                                    + " Cost Details Size = " + costDetailDatas.size() + " from unit = "
                                    + copyPrice.getUnitId() + " with cost detail id = " + newData.getCostDetailDataId());
                            costDetailDatas.add(newData);
                            d.getErrors().add(newData);
                        } catch (InventoryUpdateException e) {
                            LOG.error(String.format(
                                    "Error while creating New Cost Detail Entry for Unit Id %s, Key Type %s and Key Id %d",
                                    unitId, keyType, key), e);
                        }
                    } else {
                        LOG.info("Step 4.2 :NOT Found Price for missing key Id = " + key + " Cost Details Size = "
                                + costDetailDatas.size());

                    }
                }
                if(!missingPriceErrors.isEmpty()){
                    String finalErrorMsg = missingPriceErrors.stream().collect(Collectors.joining(", ")) + " Check For Pending Gr's ";
                    throw new DataNotFoundException(finalErrorMsg);
                }
            }
            d.setData(costDetailDatas);
            LOG.info("Step 5 :Final Size of Cost Details Size = " + d.getData().size() + " and errors size = "
                    + d.getErrors().size());

            return d;
        }



        private CostDetailDataWh getCurrentPriceForAnyUnitInRegion(PriceUpdateEntryType keyType, Integer keyId,
                                                                 String region ) throws DataNotFoundException {
            StringBuilder queryStr = new StringBuilder(
                    "select cd.costDetailDataId FROM CostDetailDataWh cd, UnitDetailData ud WHERE cd.unitId = ud.unitId ");
            queryStr.append("AND cd.keyId = :keyId AND cd.keyType = :keyType AND ");
            queryStr.append(" cd.latest = :isLatest AND ud.unitRegion = :unitRegion AND ud.unitStatus = :unitStatus");
            queryStr.append(" ORDER BY cd.lastUpdatedTime desc");

            Query query = manager.createQuery(queryStr.toString());
            query.setParameter("keyType", keyType.name());
            query.setParameter("keyId", keyId);
            query.setParameter("unitRegion", region);
            query.setParameter("unitStatus", AppConstants.ACTIVE);
            query.setParameter("isLatest", SCMServiceConstants.SCM_CONSTANT_YES);
            query.setMaxResults(1);
            try {
                Object obj = query.getSingleResult();
                if (obj != null) {
                    return manager.find(CostDetailDataWh.class, (Integer) obj);
                }
                return null;
            } catch (NoResultException | NonUniqueResultException e) {
                throw new DataNotFoundException("Prince missing for Key type: " + keyType + " and key id: " + keyId);
            }
        }



        @Override
    public List<CostDetailData> getAllCurrentPriceForAnyUnitInRegionByKeyId(PriceUpdateEntryType keyType, List<Integer> keyIds,
                                                                            List<String> regions, Boolean isLatest) throws DataNotFoundException {
            List<CostDetailData> costDetailDataList  = new ArrayList<>();
        StringBuilder queryStr = new StringBuilder(
                "select cd FROM CostDetailDataWh cd, UnitDetailData ud WHERE cd.unitId = ud.unitId ");
        queryStr.append("AND cd.keyId in :keyIds AND cd.keyType = :keyType AND ");
            if(Boolean.TRUE.equals(isLatest)){
                queryStr.append(" cd.latest = :isLatest AND ");
            }
        queryStr.append(" ud.unitRegion in(:unitRegion) AND ud.unitStatus = :unitStatus");
        queryStr.append(" ORDER BY cd.lastUpdatedTime desc");

        Query query = manager.createQuery(queryStr.toString());
        query.setParameter("keyType", keyType.name());
        query.setParameter("keyIds", keyIds);
        query.setParameter("unitRegion", regions);
        query.setParameter("unitStatus", AppConstants.ACTIVE);
            if(Boolean.TRUE.equals(isLatest)){
                query.setParameter("isLatest", AppUtils.setStatus(isLatest));
            }
        try {
            costDetailDataList = query.getResultList();
        } catch (NoResultException | NonUniqueResultException e) {
            LOG.info("Could not Find Current prices For Region : {} ", regions);
        }
        return costDetailDataList;
    }

        /**
         * @param item
         * @param data
         */
        private void fullFillInventory(InventoryItemVO item, List<Consumption> data) throws InventoryUpdateException {
            BigDecimal value = item.getQuantity();
            List<InventoryItemDrilldown> itemDrilldowns = new ArrayList<>();
            InventoryItemDrilldown itemDrilldown = null;

            sortCollection(item, data);

            if (!item.getDrillDowns().isEmpty()) {
                // consume using keyId and EXPIRE date provided
                for (InventoryItemDrilldown i : item.getDrillDowns()) {
                    for (Consumption c : data) {
                        // update txn item id and txn type against the list of consumptions
                        c.setTxnItemId(item.getItemKeyId());
                        c.setTxnType(item.getItemKeyType());
                        c.setItemQuantity(item.getQuantity());

                        // break the loop in case the to be consumed quantity is less than equal to 0
                        if (value.compareTo(BigDecimal.ZERO) <= 0) {
                            break;
                        }
                        // skip to next consumption, when the current consumption has no quantity to
                        // consume from
                        if (c.getCurrentQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                            continue;
                        }
                        // consume from item drill downs in case the item has expiry drill downs
                        // associated with it
                        if (c.getDetail().getKeyId() == item.getKeyId()
                                && AppUtils.dateToString(c.getDetail().getExpiryDate())
                                .equals(AppUtils.dateToString(i.getExpiryDate()))) {
                            BigDecimal pending = consumeItems(c, item, i.getQuantity(), itemDrilldown, itemDrilldowns);
                            value = value.subtract(i.getQuantity().subtract(pending));
                        }
                    }
                }

            } else {
                for (Consumption c : data) {
                    // update txn item id and txn type against the list of consumptions
                    c.setTxnItemId(item.getItemKeyId());
                    c.setTxnType(item.getItemKeyType());
                    c.setItemQuantity(item.getQuantity());

                    // break the loop in case the to be consumed quantity is less than equal to 0
                    if (value.compareTo(BigDecimal.ZERO) <= 0) {
                        break;
                    }
                    // skip to next consumption, when the current consumption has no quantity to
                    // consume from
                    if (c.getCurrentQuantity().compareTo(BigDecimal.ZERO) <= 0) {
                        continue;
                    }
                    value = consumeItems(c, item, value, itemDrilldown, itemDrilldowns);
                }
            }

            // when the value is left to be consumed, but no cost detail data entry has
            // inventory > 0 to consume from
            if (value.compareTo(BigDecimal.ZERO) > 0 && data.size() > 0) {
                ConsumptionDrillDown drillDown = new ConsumptionDrillDown();
                Consumption consumption;
                try{
                     consumption = data.stream().filter(p -> p.isLatest()).findFirst().get();
                }catch (Exception e){
                    String msg = "Latest price not found in this sku ID " +item.getKeyId();
                    LOG.info(msg);
                    throw new InventoryUpdateException("PRICE_NOT_FOUND",msg);
                }

                drillDown.setItem(item);
                drillDown.setData(consumption.getDetail());
                drillDown.setPrice(consumption.getCurrentPrice());
                drillDown.setVariance(true);
                drillDown.setQuantity(value);
                BigDecimal currentCost = AppUtils.multiplyWithScale10(consumption.getCurrentPrice(), value);
                drillDown.setCost(currentCost);
                consumption.setVariance(true);
                addDrillDown(consumption, drillDown);
                itemDrilldown = createItemExpiryDrillDown(item, value, consumption.getCurrentPrice(),
                        consumption.getDetail().getExpiryDate());
                if (itemDrilldowns.contains(itemDrilldown)) {
                    for (InventoryItemDrilldown in : itemDrilldowns) {
                        if (in.getKeyId() == itemDrilldown.getKeyId()
                                && AppUtils.isEqual(in.getPrice(), itemDrilldown.getPrice())
                                && in.getExpiryDate().equals(itemDrilldown.getExpiryDate())) {
                            in.setQuantity(AppUtils.add(in.getQuantity(), itemDrilldown.getQuantity()));
                        }
                    }
                } else {
                    itemDrilldowns.add(itemDrilldown);
                }
            }
            item.setDrillDowns(itemDrilldowns);
            BigDecimal cost = BigDecimal.ZERO;
            for (InventoryItemDrilldown dd : item.getDrillDowns()) {
                cost = cost.add(dd.getPrice().multiply(dd.getQuantity()));
            }
            item.setPrice(AppUtils.divideWithScale10(cost, item.getQuantity()));
        }

        private BigDecimal consumeItems(Consumption c, InventoryItemVO item, BigDecimal value,
                                        InventoryItemDrilldown itemDrilldown, List<InventoryItemDrilldown> itemDrilldowns) {

            BigDecimal current = c.getCurrentQuantity();
            ConsumptionDrillDown drillDown = new ConsumptionDrillDown();

            drillDown.setItem(item);
            drillDown.setData(c.getDetail());
            drillDown.setPrice(c.getCurrentPrice());
            if (value.compareTo(current) >= 0) {
                drillDown.setQuantity(current);
                BigDecimal currentCost = AppUtils.multiplyWithScale10(c.getCurrentPrice(), current);
                c.setConsumed(true);
                c.setCurrentQuantity(AppUtils.subtract(c.getCurrentQuantity(), current));
                drillDown.setCost(currentCost);
                itemDrilldown = createItemExpiryDrillDown(item, drillDown.getQuantity(), c.getCurrentPrice(),
                        c.getDetail().getExpiryDate());
                value = AppUtils.subtract(value, current);
            } else {
                drillDown.setQuantity(value);
                BigDecimal currentCost = AppUtils.multiplyWithScale10(c.getCurrentPrice(), value);
                c.setConsumed(true);
                c.setCurrentQuantity(AppUtils.subtract(c.getCurrentQuantity(), value));
                drillDown.setCost(currentCost);
                itemDrilldown = createItemExpiryDrillDown(item, drillDown.getQuantity(), c.getCurrentPrice(),
                        c.getDetail().getExpiryDate());
                value = AppUtils.subtract(value, value);
            }
            addDrillDown(c, drillDown);
            if (itemDrilldowns.contains(itemDrilldown)) {
                for (InventoryItemDrilldown in : itemDrilldowns) {
                    if (in.getKeyId() == itemDrilldown.getKeyId()
                            && AppUtils.isEqual(in.getPrice(), itemDrilldown.getPrice())
                            && in.getExpiryDate().equals(itemDrilldown.getExpiryDate())) {
                        in.setQuantity(AppUtils.add(in.getQuantity(), itemDrilldown.getQuantity()));
                    }
                }
            } else {
                itemDrilldowns.add(itemDrilldown);
            }
            return value;
        }

        private void sortCollection(InventoryItemVO item, List<Consumption> data) {
            // sort here
            if (ConsumptionOrder.LIFO.equals(item.getConsumptionOrder())) {
                Collections.sort(data, (o2, o1) -> {
                    if (o1.getDetail().getExpiryDate().compareTo(o2.getDetail().getExpiryDate()) == 0) {
                        return o1.getDetail().getCostDetailDataId().compareTo(o2.getDetail().getCostDetailDataId());
                    }
                    return o1.getDetail().getExpiryDate().compareTo(o2.getDetail().getExpiryDate());
                });
            } else {
                Collections.sort(data, (o1, o2) -> {
                    if (o1.getDetail().getExpiryDate().compareTo(o2.getDetail().getExpiryDate()) == 0) {
                        return o1.getDetail().getCostDetailDataId().compareTo(o2.getDetail().getCostDetailDataId());
                    }
                    return o1.getDetail().getExpiryDate().compareTo(o2.getDetail().getExpiryDate());
                });
            }
        }

        private InventoryItemDrilldown createItemExpiryDrillDown(InventoryItemVO item, BigDecimal quantity,
                                                                 BigDecimal price, Date expiryDate) {
            InventoryItemDrilldown itemDrilldown = new InventoryItemDrilldown();
            itemDrilldown.setKeyId(item.getItemKeyId());
            itemDrilldown.setKeyType(item.getItemKeyType().value());
            itemDrilldown.setPrice(price);
            itemDrilldown.setQuantity(quantity);
            itemDrilldown.setExpiryDate(AppUtils.formatExpiryDate(expiryDate));
            return itemDrilldown;
        }

        /**
         * @param item
         * @param data
         */
        private void overrideInventory(int unitId, PriceUpdateEntryType keyType, ProductStockForUnit item,
                                       List<Consumption> data, Date transactionTime) throws InventoryUpdateException {
            // find out the total quantity of the key id and see if its different
            // from stock at hand.
            // incase its same then skip to the next key id.
            // incase its different, we need todo the following.
            // initialize the Stock AT hand to the remaining value.
            // Iterate over the list of cost detail data from index 0 to length -1
            // check if the current element is that last element. // for the last
            // element we need to see if the Remaining Value is > 0.0 then replace
            // the Cost Detail Entry with remaining value.
            // if current value < remaining then skip to next entry and current
            // value = current value - remaining value.
            // if current value > remaining then current value = remaining and rest
            // all remaining current values to 0.0 and exit the loop.

            BigDecimal remaining = item.getStockValue(); // 10
            int breakValue = -1;
            for (int i = 0; i < data.size(); i++) {
                Consumption c = data.get(i);
                if (i == data.size() - 1) { // for last entry, i=2
                    if (c.getCurrentQuantity().compareTo(remaining) != 0) {
                        c.getDetail().setQuantity(remaining);
                        addOverrideAudit(c, remaining, unitId, keyType, item, transactionTime);
                    }
                    break;
                }
                if (c.getCurrentQuantity().compareTo(remaining) <= 0) {
                    BigDecimal resetValue = AppUtils.subtract(remaining, c.getCurrentQuantity());
                    remaining = resetValue.compareTo(BigDecimal.ZERO) < 0 ? BigDecimal.ZERO : resetValue;
                    continue;
                }
                if (c.getCurrentQuantity().compareTo(remaining) > 0) { // 2>1
                    breakValue = i; // breakValue=2
                    c.getDetail().setQuantity(remaining); // costQty = 1
                    addOverrideAudit(c, remaining, unitId, keyType, item, transactionTime);
                    break;
                }
            }

            if (breakValue != -1 && breakValue != data.size() - 1) {
                for (int i = breakValue + 1; i < data.size(); i++) {
                    Consumption c = data.get(i);
                    c.getDetail().setQuantity(BigDecimal.ZERO);
                    addOverrideAudit(c, BigDecimal.ZERO, unitId, keyType, item, transactionTime);
                }
            }
            manager.flush();
        }

        private void addOverrideAudit(Consumption c, BigDecimal remaining, int unitId, PriceUpdateEntryType keyType,
                                      ProductStockForUnit item, Date transactionTime) throws InventoryUpdateException {
            ConsumptionDrillDown drillDown = new ConsumptionDrillDown();
            BigDecimal quantityOverriden = AppUtils.subtract(c.getCurrentQuantity(), remaining);
            drillDown.setCost(AppUtils.multiplyWithScale10(quantityOverriden, c.getDetail().getPrice()));
            drillDown.setQuantity(quantityOverriden);
            drillDown.setPrice(c.getDetail().getPrice());
            drillDown.setItem(item);
            drillDown.setVariance(true);
            drillDown.setData(c.getDetail());
            List<ConsumptionDrillDown> drillDowns = new ArrayList<>();
            drillDowns.add(drillDown);
            addAuditEntry(PriceTransactionType.STOCK_CORRECTION.name(), c.getDetail(), keyType, unitId, drillDowns,
                    transactionTime, false, c.getDetail().getExpiryDate());
        }

        private void addDrillDown(Consumption c, ConsumptionDrillDown d) {
            if (!c.getDrillDowns().containsKey(d.getItem().getKeyId())) {
                c.getDrillDowns().put(d.getItem().getKeyId(), new ArrayList<>());
            }
            c.getDrillDowns().get(d.getItem().getKeyId()).add(d);
        }

        public static void main(String[] args) throws InventoryUpdateException {

            InventoryItemVO vo1 = new InventoryItemVO() {
                private BigDecimal price;

                @Override
                public void setQuantity(BigDecimal price) {

                }

                @Override
                public void setPrice(BigDecimal price) {
                    this.price = price;
                }

                @Override
                public String getUom() {
                    return UnitOfMeasure.KG.name();
                }

                @Override
                public BigDecimal getQuantity() {
                    return new BigDecimal(30.0d);
                }

                @Override
                public BigDecimal getPrice() {
                    return price;
                }

                @Override
                public PriceUpdateEntryType getKeyType() {
                    return PriceUpdateEntryType.PRODUCT;
                }

                @Override
                public int getKeyId() {
                    return 10101010;
                }

                @Override
                public StockEventType getItemKeyType() {
                    return StockEventType.CONSUMPTION;
                }

                @Override
                public int getItemKeyId() {
                    return 1001;
                }

                @Override
                public List<InventoryItemDrilldown> getDrillDowns() {
                    return null;
                }

                @Override
                public void setKeyType(PriceUpdateEntryType type) {

                }

                @Override
                public Date getExpiryDate() {
                    // TODO Auto-generated method stub
                    return null;
                }

                @Override
                public void setExpiryDate(Date expiryDate) {
                    // TODO Auto-generated method stub

                }

                @Override
                public void setDrillDowns(List<InventoryItemDrilldown> drillDowns) {
                    // TODO Auto-generated method stub

                }

                @Override
                public ConsumptionOrder getConsumptionOrder() {
                    // TODO Auto-generated method stub
                    return null;
                }

            };

            InventoryItemVO vo2 = new InventoryItemVO() {
                private BigDecimal price;

                @Override
                public void setQuantity(BigDecimal price) {

                }

                @Override
                public void setPrice(BigDecimal price) {
                    this.price = price;
                }

                @Override
                public String getUom() {
                    return UnitOfMeasure.KG.name();
                }

                @Override
                public BigDecimal getQuantity() {
                    return new BigDecimal(20.0d);
                }

                @Override
                public BigDecimal getPrice() {
                    return price;
                }

                @Override
                public PriceUpdateEntryType getKeyType() {
                    return PriceUpdateEntryType.PRODUCT;
                }

                @Override
                public int getKeyId() {
                    return 10101010;
                }

                @Override
                public StockEventType getItemKeyType() {
                    return StockEventType.CONSUMPTION;
                }

                @Override
                public int getItemKeyId() {
                    return 1002;
                }

                @Override
                public List<InventoryItemDrilldown> getDrillDowns() {
                    return null;
                }

                @Override
                public void setKeyType(PriceUpdateEntryType type) {
                    // TODO Auto-generated method stub

                }

                @Override
                public Date getExpiryDate() {
                    // TODO Auto-generated method stub
                    return null;
                }

                @Override
                public void setExpiryDate(Date expiryDate) {
                    // TODO Auto-generated method stub

                }

                @Override
                public void setDrillDowns(List<InventoryItemDrilldown> drillDowns) {
                    // TODO Auto-generated method stub

                }

                @Override
                public ConsumptionOrder getConsumptionOrder() {
                    // TODO Auto-generated method stub
                    return null;
                }

            };
            List<InventoryItemVO> vos = Arrays.asList(vo1, vo2);
            Consumption c1 = new Consumption(new CostDetailData(10101010, PriceUpdateEntryType.PRODUCT.name(), 10000,
                    new BigDecimal(10.0), new BigDecimal(10.0), UnitOfMeasure.KG.name(), "N"));
            Consumption c2 = new Consumption(new CostDetailData(10101010, PriceUpdateEntryType.PRODUCT.name(), 10000,
                    new BigDecimal(10.0), new BigDecimal(20.0), UnitOfMeasure.KG.name(), "N"));
            Consumption c3 = new Consumption(new CostDetailData(10101010, PriceUpdateEntryType.PRODUCT.name(), 10000,
                    new BigDecimal(20.0), new BigDecimal(10.0), UnitOfMeasure.KG.name(), "N"));
            Consumption c4 = new Consumption(new CostDetailData(10101010, PriceUpdateEntryType.PRODUCT.name(), 10000,
                    new BigDecimal(20.0), new BigDecimal(20.0), UnitOfMeasure.KG.name(), "Y"));
            Consumption c5 = new Consumption(new CostDetailData(10101010, PriceUpdateEntryType.PRODUCT.name(), 10000,
                    new BigDecimal(0.0), new BigDecimal(20.0), UnitOfMeasure.KG.name(), "Y"));
            // Test 1
            // List<Consumption> data = Arrays.asList(c1, c2, c3, c4);
            // Test 2
            List<Consumption> data = Arrays.asList(c1, c2);
            // Test 3
            // List<Consumption> data = Arrays.asList(c5);
            com.stpl.tech.scm.data.dao.impl.WHPriceManagementDaoImpl impl = new com.stpl.tech.scm.data.dao.impl.WHPriceManagementDaoImpl();
            for (InventoryItemVO vo : vos) {
                System.out.println("Before");
                for (Consumption c : data) {
                    System.out.println(c);
                }
                impl.fullFillInventory(vo, data);
                System.out.println("After");
                for (Consumption c : data) {
                    System.out.println(c);
                }
                System.out.println("Price : " + vo.getPrice());
            }
        }

        /**
         *
         */
        private String logError(InventoryVO rec, InventoryItemVO item, UnitBasicDetail unit) {
            String itemName = "UNKNOWN";
            if (PriceUpdateEntryType.PRODUCT.equals(item.getKeyType())) {
                itemName = scmCache.getProductDefinition(item.getKeyId()).getProductName();
            } else if (PriceUpdateEntryType.SKU.equals(item.getKeyType())) {
                itemName = scmCache.getSkuDefinition(item.getKeyId()).getSkuName();
            }
            return String.format("No Price Entry For Event Type : %s\nUnit : %s(%d)\nItem Type : %s\nItem : %s(%d)\n",
                    rec.getKeyType().name(), unit.getName(), unit.getId(), item.getKeyType().name(), itemName,
                    item.getKeyId());
        }

        @Override
        public List<CostDetailData> getPriceDetailsForUnit(int unitId) {
            Query query = manager.createQuery("FROM CostDetailDataWh p WHERE p.unitId = :unitId order by p.costDetailDataId");
            query.setParameter("unitId", unitId);
            return query.getResultList();
        }

        /**
         * Collects SKU and PRODUCT ID having Latest entries and delete old entries with
         * zero quantity for the same SKU and PRODUCT
         */
        @Override
        public void deleteObsoletePrices(Integer unit, PriceUpdateEntryType entryType) throws InventoryUpdateException {

            LOG.info("DELETE obsolete prices for unit {} and entry type {}", unit, entryType);
            try {
                Query keyIdQuery = manager
                        .createQuery("SELECT E.keyId FROM CostDetailDataWh E"
                                + " WHERE E.unitId = :unitId AND E.latest = :latest AND E.keyType = :keyType")
                        .setParameter("unitId", unit).setParameter("latest", SCMServiceConstants.SCM_CONSTANT_YES)
                        .setParameter("keyType", entryType.name());
                List<Integer> keyIds = keyIdQuery.getResultList();

                if (keyIds != null && keyIds.size() > 0) {
                    StringBuilder queryStr = new StringBuilder("DELETE FROM CostDetailDataWh");
                    queryStr.append(" WHERE unitId = :unitId AND quantity <= :zeroQty");
                    queryStr.append(" AND latest = :notLatest AND keyType = :keyType AND keyId IN (:keyIds)");
                    Query query = manager.createQuery(queryStr.toString()).setParameter("unitId", unit)
                            .setParameter("zeroQty", BigDecimal.ZERO).setParameter("keyType", entryType.name())
                            .setParameter("notLatest", SCMServiceConstants.SCM_CONSTANT_NO).setParameter("keyIds", keyIds);
                    query.executeUpdate();
                }

            } catch (Exception e) {
                throw new InventoryUpdateException("Deletion of obsolete entries failed", e);
            }
        }

        @Override
        public void overrideInventory(VarianceVO all) throws DataNotFoundException, InventoryUpdateException {
            // Get all cost detail data for all the key ids for a key type for that
            // unit in the reverse order.
            // find out the total quantity of the key id and see if its different
            // from stock at hand.
            // incase its same then skip to the next key id.
            // incase its different, we need todo the following.
            // initialize the Stock AT hand to the remaining value.
            // Iterate over the list of cost detail data from index 0 to length -1
            // check if the current element is that last element. // for the last
            // element we need to see if the Remaining Value is > 0.0 then replace
            // the Cost Detail Entry with remaining value.
            // if current value < remaining then skip to next entry and current
            // value = current value - remaining value.
            // if current value > remaining then current value = remaining and rest
            // all remaining current values to 0.0 and exit the loop.
            UnitBasicDetail unit = cache.getUnitBasicDetail(all.getUnitId());
            PriceUpdateEntryType keyType = all.getInventoryType();
            Date transactionTime = AppUtils.getCurrentTimestamp();
            List<Integer> keyIds = all.getStockList().stream().map(ProductStockForUnit::getKeyId)
                    .collect(Collectors.toList());
            Map<Integer, List<Consumption>> current = new HashMap<>();
            CostDataEntriesWh d = getAllPrices(keyType, all.getUnitId(), keyIds, false);
            if (d.getData() != null && d.getData().size() > 0) {
                d.getData().forEach((cost) -> {
                    Consumption c = new Consumption(cost);
                    if (!current.containsKey(cost.getKeyId())) {
                        current.put(cost.getKeyId(), new ArrayList<>());
                    }
                    current.get(cost.getKeyId()).add(c);
                });
            }
            List<String> errors = new ArrayList<>();
            for (ProductStockForUnit item : all.getStockList()) {
                List<Consumption> data = current.get(item.getKeyId());
                if (data == null || data.isEmpty()) {
                    errors.add(logError(all, item, unit));
                } else {
                    if (item.getPrice() == null) {
                        errors.add(logError(all, item, unit));
                    } else {
                        overrideInventory(all.getUnitId(), keyType, item, data, transactionTime);
                    }
                }
            }

            if (errors != null && errors.size() > 0) {
                String message = "Error while creating " + all.getKeyType().name() + "\n"
                        + Arrays.toString(errors.toArray());
                if (!all.getKeyType().equals(StockEventType.STOCK_TAKE)) {
                    notificationService.sendInventoryErrorNotification(message);
                }
            }
        }

        private String printErrors(InventoryVO rec, List<CostDetailDataWh> errors, UnitBasicDetail unit) {
            if (errors == null || errors.size() == 0) {
                return "";
            }
            StringBuilder b = new StringBuilder();
            for (CostDetailData error : errors) {

                String itemName = "UNKNOWN";
                if (PriceUpdateEntryType.PRODUCT.name().equals(error.getKeyType())) {
                    itemName = scmCache.getProductDefinition(error.getKeyId()).getProductName();
                } else if (PriceUpdateEntryType.SKU.name().equals(error.getKeyType())) {
                    itemName = scmCache.getSkuDefinition(error.getKeyId()).getSkuName();
                }
                b.append(String.format(
                        "Added Price Entry For Event Type : %s\nUnit : %s(%d)\nItem Type : %s\nItem : %s(%d)\nPrice : %s\n",
                        rec.getKeyType().name(), unit.getName(), unit.getId(), error.getKeyType(), itemName,
                        error.getKeyId(), error.getPrice()));
                b.append("\n");

            }
            return b.toString();
        }

        @Override
        public List<CostDetailData> fixPricing(List<CostDetailData> currentPriceList) {
            List<Integer> costIds = currentPriceList.stream().mapToInt(value -> value.getCostDetailDataId()).boxed()
                    .collect(Collectors.toList());

            try {

                StringBuilder queryStr = new StringBuilder(
                        "SELECT E.keyId,COUNT(E.costDetailDataId), MIN(E.costDetailDataId)"
                                + " FROM CostDetailDataWh E WHERE E.costDetailDataId IN (:costIds) GROUP BY E.keyId");
                Query query = manager.createQuery(queryStr.toString()).setParameter("costIds", costIds);
                List<Object[]> staleIds = query.getResultList();

                List<Integer> toBeCorrectedIds = staleIds.stream().filter(obj -> (Long) obj[1] > 1l)
                        .mapToInt(obj -> (Integer) obj[2]).boxed().collect(Collectors.toList());

                // update non-latest prices
                updateNonLatestPrice(toBeCorrectedIds);

                currentPriceList = currentPriceList.stream()
                        .filter(cost -> !toBeCorrectedIds.contains(cost.getCostDetailDataId()))
                        .collect(Collectors.toList());
            } catch (Exception e) {
                LOG.error("Error while fixing pricing for inventory Ids :::: {}", costIds, e);
            }
            return currentPriceList;
        }

        private void updateNonLatestPrice(List<Integer> toBeCorrectedIds) {
            StringBuilder queryStr = new StringBuilder("UPDATE CostDetailDataWh");
            queryStr.append(" SET latest = :notLatest");
            queryStr.append(" WHERE costDetailDataId IN (:keyIds)");
            Query query = manager.createQuery(queryStr.toString())
                    .setParameter("notLatest", SCMServiceConstants.SCM_CONSTANT_NO)
                    .setParameter("keyIds", toBeCorrectedIds);
            query.executeUpdate();
        }

        @Override
        public <T extends ConsumptionVO> T updateLatestFlag(T rec) {

            // get CDD entry according to the data
            PriceUpdateEntryType keyType = rec.getInventoryType();
            List<Integer> keyIds = new ArrayList<Integer>(rec.getConsumption().keySet());
            List<CostDetailData> list = getCurrentPrices(rec.getInventoryType(), rec.getUnitId(), keyIds, false);

            // create consumption values map key Id -> List of entries
            Map<Integer, List<Consumption>> current = new HashMap<>();
            if (list != null && list.size() > 0) {
                list.stream().forEach((cost) -> {
                    Consumption c = new Consumption(cost);
                    if (!current.containsKey(cost.getKeyId())) {
                        current.put(cost.getKeyId(), new ArrayList<>());
                    }
                    current.get(cost.getKeyId()).add(c);
                });
            }

            for (InventoryItemVO item : rec.getInventoryItems()) {
                if (item.getQuantity().compareTo(BigDecimal.ZERO) > 0) {
                    List<Consumption> data = current.get(item.getKeyId());
                    checkAndDeleteEntry(item, data, rec.getUnitId());
                }
            }
            // if zero then remove it
            // update latest timestamp entry as is latest

            return null;
        }

        private void checkAndDeleteEntry(InventoryItemVO item, List<Consumption> data, int unitId) {

            boolean updateLatest = false;
            for (InventoryItemDrilldown i : item.getDrillDowns()) {
                for (Consumption c : data) {
                    // delete all those entries which have same sku/product id, same price, same
                    // expiry date and zero inventory
                    if (c.getDetail().getKeyId() == item.getKeyId()
                            && c.getDetail().getPrice().compareTo(item.getPrice()) == 0
                            && AppUtils.dateToString(c.getDetail().getExpiryDate())
                            .equals(AppUtils.dateToString(i.getExpiryDate()))
                            && BigDecimal.ZERO.compareTo(c.getDetail().getQuantity()) >= 0) {
                        // item to be deleted
                        if (c.isLatest()) {
                            updateLatest = true;
                        }
                        manager.remove(c.getDetail());
                    }
                }
            }

            // fetch the prices again from the database and set the latest flag as true on
            // the latest entry found
            if (updateLatest) {
                Query q = manager.createQuery(
                        "FROM CostDetailDataWh WHERE keyId = :keyId AND keyType = :keyType AND unitId = :unitId AND uom = :uom ORDER BY expiryDate DESC");
                q.setParameter("keyId", item.getKeyId());
                q.setParameter("keyType", item.getKeyType().name());
                q.setParameter("unitId", unitId);
                q.setParameter("uom", item.getUom());
                List<CostDetailDataWh> list = q.getResultList();
                if (list != null && !list.isEmpty()) {
                    for (CostDetailData c : list) {
                        if (SCMServiceConstants.SCM_CONSTANT_YES.equals(c.getLatest())) {
                            LOG.info("Latest entry already exists while creating new latest Entry {}",
                                    JSONSerializer.toJSON(c));
                            return;
                        }
                    }
                    CostDetailDataWh d = list.get(0);
                    d.setLatest(SCMServiceConstants.SCM_CONSTANT_YES);
                }
            }
        }
    }


