package com.stpl.tech.scm.notification.email;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.notification.email.template.VendorDebitNoteNotificationTemplate;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

import java.util.List;

public class VendorDebitNoteNotification extends EmailNotification {

    private VendorDebitNoteNotificationTemplate notificationTemplate;
    private EnvType envType;
    private List<String> emails;

    public VendorDebitNoteNotification() {
    }

    public VendorDebitNoteNotification(VendorDebitNoteNotificationTemplate notificationTemplate, EnvType envType, List<String> emails) {
        this.notificationTemplate = notificationTemplate;
        this.envType = envType;
        this.emails = emails;
    }

    @Override
    public String[] getToEmails() {
        if(SCMUtil.isDev(envType)){
            return new String[]{"<EMAIL>"};
        }else {
            return new String[]{"<EMAIL>"};
        }
    }

    @Override
    public String getFromEmail() {
        return "<EMAIL>";
    }

    @Override
    public String subject() {
        String subject = "Debit note with reference to Purchase Return Invoice " + SCMUtil.getFormattedDate(SCMUtil.getCurrentDateIST());
        if (SCMUtil.isDev(envType)) {
            subject = "Dev : " + subject;
        }
        return subject;
    }

    @Override
    public String body() throws EmailGenerationException {
        try {
            return notificationTemplate.getContent();
        } catch (TemplateRenderingException e) {
            throw new EmailGenerationException("Failed to render the template", e);
        }
    }

    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }
}
