package com.stpl.tech.scm.core.service.impl;

import com.itextpdf.text.DocumentException;
import com.stpl.tech.kettle.core.data.budget.vo.BudgetDetail;
import com.stpl.tech.kettle.core.data.budget.vo.ExpenseField;
import com.stpl.tech.kettle.core.data.budget.vo.ExpenseField.ServiceRecordCategory;
import com.stpl.tech.kettle.core.data.budget.vo.ServiceAggregate;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.domain.model.UnitStatus;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.cache.SCMStateTransitionCache;
import com.stpl.tech.scm.core.cache.SCMStateTransitionObject;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.CapexManagementService;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.PaymentRequestManagementService;
import com.stpl.tech.scm.core.service.PurchaseOrderManagementService;
import com.stpl.tech.scm.core.service.SCMNotificationService;
import com.stpl.tech.scm.core.service.ServiceOrderManagementService;
import com.stpl.tech.scm.core.service.ServiceReceiveManagementService;
import com.stpl.tech.scm.core.util.PdfHelper;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.core.util.model.ServiceOrderCreateVO;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.dao.PaymentRequestManagementDao;
import com.stpl.tech.scm.data.dao.SCMAssetManagementDao;
import com.stpl.tech.scm.data.dao.SCMMetadataDao;
import com.stpl.tech.scm.data.dao.ServiceOrderManagementDao;
import com.stpl.tech.scm.data.model.AdvancePaymentData;
import com.stpl.tech.scm.data.model.BudgetAuditDetailData;
import com.stpl.tech.scm.data.model.BusinessCostCenterData;
import com.stpl.tech.scm.data.model.CapexAuditDetailData;
import com.stpl.tech.scm.data.model.CapexBudgetDetailData;
import com.stpl.tech.scm.data.model.CostCenterData;
import com.stpl.tech.scm.data.model.CostElementData;
import com.stpl.tech.scm.data.model.DocumentDetailData;
import com.stpl.tech.scm.data.model.EmployeeCostCenterMappingData;
import com.stpl.tech.scm.data.model.ListDatas;
import com.stpl.tech.scm.data.model.ListDetailData;
import com.stpl.tech.scm.data.model.ListTypeData;
import com.stpl.tech.scm.data.model.PurchaseOrderData;
import com.stpl.tech.scm.data.model.ServiceOrderData;
import com.stpl.tech.scm.data.model.ServiceOrderItemData;
import com.stpl.tech.scm.data.model.ServiceOrderNotificationData;
import com.stpl.tech.scm.data.model.ServiceOrderStatusEventData;
import com.stpl.tech.scm.data.model.VendorCostCenterCostElementMapping;
import com.stpl.tech.scm.data.model.VendorDetailData;
import com.stpl.tech.scm.data.model.VendorDispatchLocationDetailData;
import com.stpl.tech.scm.data.transport.model.FullfillmentData;
import com.stpl.tech.scm.data.transport.model.FullfillmentDataUnitLevel;
import com.stpl.tech.scm.data.transport.model.UnitCapexDataSummary;
import com.stpl.tech.scm.domain.model.AdvancePaymentStatus;
import com.stpl.tech.scm.domain.model.BudgetAuditActions;
import com.stpl.tech.scm.domain.model.BulkSoDetail;
import com.stpl.tech.scm.domain.model.BusinessCostCenter;
import com.stpl.tech.scm.domain.model.CapexStatus;
import com.stpl.tech.scm.domain.model.CostCenter;
import com.stpl.tech.scm.domain.model.CostElement;
import com.stpl.tech.scm.domain.model.CostElementPriceUpdate;
import com.stpl.tech.scm.domain.model.DocUploadType;
import com.stpl.tech.scm.domain.model.DocumentDetail;
import com.stpl.tech.scm.domain.model.FileType;
import com.stpl.tech.scm.domain.model.ListData;
import com.stpl.tech.scm.domain.model.ListDetail;
import com.stpl.tech.scm.domain.model.ListType;
import com.stpl.tech.scm.domain.model.MimeType;
import com.stpl.tech.scm.domain.model.PaymentRequestStatus;
import com.stpl.tech.scm.domain.model.ProductStatus;
import com.stpl.tech.scm.domain.model.PurchaseOrderStatus;
import com.stpl.tech.scm.domain.model.SCMOrderStatus;
import com.stpl.tech.scm.domain.model.ServiceOrder;
import com.stpl.tech.scm.domain.model.ServiceOrderItem;
import com.stpl.tech.scm.domain.model.ServiceOrderShort;
import com.stpl.tech.scm.domain.model.ServiceOrderStatus;
import com.stpl.tech.scm.domain.model.ServiceOrderSummary;
import com.stpl.tech.scm.domain.model.StockTakeType;
import com.stpl.tech.scm.domain.model.UnitDetail;
import com.stpl.tech.scm.domain.model.VendorAdvancePayment;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.scm.domain.model.VendorDispatchLocation;
import com.stpl.tech.scm.domain.model.VendorStatus;
import com.stpl.tech.scm.domain.model.VendorType;
import com.stpl.tech.scm.domain.state.SCMTransitionData;
import com.stpl.tech.scm.domain.state.SCMTransitionStatus;
import com.stpl.tech.scm.notification.email.UnitCapexSummaryEmailNotification;
import com.stpl.tech.scm.notification.email.template.UnitCapexSummaryTemplate;
import com.stpl.tech.scm.notification.email.template.VendorSOEmailNotificationTemplate;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.AttachmentData;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.poi.ss.usermodel.RichTextString;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.util.Pair;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.ConstraintViolationException;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.HashMap;

import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.stpl.tech.scm.data.converter.SCMDataConverter.convert;

@Service
public class ServiceOrderManagementServiceImpl implements ServiceOrderManagementService {

    private static final Logger LOG = LoggerFactory.getLogger(ServiceOrderManagementServiceImpl.class);

    @Autowired
    private SCMCache scmCache;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private ServiceOrderManagementDao dao;

    @Autowired
    private SCMMetadataDao scmMetadataDao;

    @Autowired
    private PaymentRequestManagementDao paymentRequestManagementDao;

    @Autowired
    private ServiceOrderManagementService serviceOrderManagementService;

    @Autowired
    private EnvProperties props;

    @Autowired
    private FileArchiveService fileArchiveService;

    @Autowired
    private SCMNotificationService notificationService;

    @Autowired
    private CapexManagementService capexManagementService;

    @Autowired
    private PaymentRequestManagementService paymentRequestManagementService;

    @Autowired
    private ServiceReceiveManagementService serviceReceiveManagementService;

    @Autowired
    private PurchaseOrderManagementService purchaseOrderService;

    @Autowired
    private SCMAssetManagementDao scmAssetManagementDao;

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<CostCenter> getCostCentersData() {
        List<CostCenter> centers = new ArrayList<>();
        List<CostCenterData> costCenters = dao.findAll(CostCenterData.class);

        if (costCenters != null && !costCenters.isEmpty()) {
            costCenters.forEach(c -> centers.add(convert(c, masterDataCache)));
        }
        return centers;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<CostCenter> getCostCentersDataForEmployee(Integer empId) {
        List<CostCenter> centers = new ArrayList<>();
        List<Integer> costCenterIds = dao.findCostCentersForEmployee(empId);
        if (costCenterIds != null && !costCenterIds.isEmpty()) {
            List<CostCenterData> costCenters = dao.findCostCenters(costCenterIds);
            if (costCenters != null && !costCenters.isEmpty()) {
                costCenters.forEach(c -> centers.add(convert(c, masterDataCache)));
            }
        }
        return centers;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<Integer> createServiceOrder(Integer documentId, List<ServiceOrderCreateVO> createVO, Integer approvalOfHodDocId) throws SumoException {
        List<Integer> srs = new ArrayList<>();
        Set<Integer> bccIds = new HashSet<>();
        List<Integer>unitIds = new ArrayList<>() ;
        String type = createVO.get(0).getType();
        createVO.forEach(so ->{
            so.getItems().forEach(item ->{
                bccIds.add(item.getBusinessCostCenterId());

            });
        });
        /*if(Objects.nonNull(type) &&  type.equalsIgnoreCase(CapexStatus.CAPEX.value())){
            validateUnitBudgetForSOCreation(bccIds);
        }*/
        Map<Integer,String> bccCodeMap =  getBusinessCostCentersData().stream().collect((Collectors.toMap(BusinessCostCenter::getId,BusinessCostCenter::getCode)));

        for(Integer bccId : bccIds)
        {

            Date currentDate = AppUtils.getCurrentDate() ;
            Integer bccCode = null;
            try{
                  bccCode = Integer.valueOf(bccCodeMap.get(bccId));
            }catch (Exception e){
                continue;
            }
            Unit unit = masterDataCache.getUnit(bccCode);
            if(Objects.isNull(unit)){
                continue;
            }
            Date handOverDate = unit.getHandoverDate() ;
            Boolean isNSO = Objects.nonNull(handOverDate) && (SCMUtil.isBefore(currentDate,AppUtils.getDateAfterDays(handOverDate , 3)) ||
                    AppUtils.isSameDate(currentDate,AppUtils.getDateAfterDays(handOverDate , 3))) ;
            if(isNSO) {
              unitIds.add(bccCode) ;
            }
        }
        if(unitIds.size()>0) {
            validateForSO(unitIds) ;
        }
        for (ServiceOrderCreateVO vo : createVO) {
            srs.add(createServiceOrders(documentId, vo, approvalOfHodDocId));
        }
        return srs;
    }

    private  Boolean validateForSO(List<Integer> bccIds) throws  SumoException
    {
        List<Integer> nonValidUnitIds = scmMetadataDao.checkForFaReceiveing(bccIds) ;
        if(!nonValidUnitIds.isEmpty())
        {
            String title = "Fixed Assets receiving is still pending for these units";
           String msg = "";
              for(Integer unitId : nonValidUnitIds)
              {
                  msg+=masterDataCache.getUnit(unitId).getName() +"("+unitId+")"+" , " ;
              }
           throw new SumoException(title,msg);
        }
        return  true;
    }

    private List<UnitCapexDataSummary> getPendingCloseCapexUnitData(Set<Integer> unitIds){
        Date dateForCheck =  AppUtils.addDays( AppUtils.getCurrentDate(),-60);
        unitIds = unitIds.stream().filter(unitId ->{
            Unit unit = masterDataCache.getUnit(unitId);
            if(Objects.isNull(unit.getHandoverDate())){
                return true;
            }
            if(unit.getHandoverDate().before(dateForCheck)){
                return true;
            }
            return false;
        }).collect(Collectors.toSet());
        return dao.findBudgetDetails(unitIds,dateForCheck);
    }

    private Boolean validateUnitBudgetForSOCreation(Set<Integer> bccIds) throws SumoException {
        Set<Integer> unitIds = new HashSet<>();
        List<BusinessCostCenter> bccs =  getBusinessCostCentersData();
        for(BusinessCostCenter bcc : bccs){
            if(bccIds.contains(bcc.getId())){
                unitIds.add(Integer.valueOf(bcc.getCode()));
            }
        }
      List<UnitCapexDataSummary> unitCapexDataSummaryList =  getPendingCloseCapexUnitData(unitIds);
        List<Integer> nonValidUnitIds = unitCapexDataSummaryList.stream().map(UnitCapexDataSummary::getUnitId).collect(Collectors.toList());
        if(Objects.nonNull(nonValidUnitIds) && nonValidUnitIds.size() > 0){
            String title = "These Units Budget Is Not Closed For 60 Days , Please Close The Budget To Make SO For This Units !!!!!";
            String msg = "Units  : \n";
            for(Integer unitId : nonValidUnitIds){
                msg+=  + unitId + "(" + scmCache.getUnitDetail(unitId).getUnitName() + "), ";
            }
            throw new SumoException(title,msg);
        }

        return true;
    }

    private Integer createServiceOrders(Integer documentId, ServiceOrderCreateVO createVO, Integer approvalOfHodDocId) throws SumoException {
        BigDecimal billAmount = BigDecimal.ZERO;
        BigDecimal billTaxes = BigDecimal.ZERO;
        Integer serviceOrderId = null;
        ServiceOrderData serviceOrderData = new ServiceOrderData();
        List<ServiceOrderItemData> itemDataList = new ArrayList<>();
        int userId = createVO.getUserId();
        int dispatchLocationId = createVO.getDispatchLocationId();
        String comment = createVO.getComment();
        int vendorId = createVO.getVendorId();
        List<ServiceOrderItem> items = createVO.getItems();
        VendorDetail vendor = scmCache.getVendorDetail(vendorId);
        Optional<VendorDispatchLocation> dispatchLocation = SCMUtil.getDispatchLocation(vendor, dispatchLocationId);

        if (!dispatchLocation.isPresent()) {
            LOG.error("No dispatch location found in vendor with the given dispatchId {}", dispatchLocationId);
            throw new SumoException("No dispatch location found in vendor with the given dispatchId");
        }

        if (items.isEmpty()) {
            LOG.error("Didn't find items for Service Order");
            throw new SumoException("Didn't find items for Service Order");
        }

        for (ServiceOrderItem item : items) {
            if (item.getCostElementId() != 0) {
                ServiceOrderItemData itemData = createServiceOrderItemData(item, createVO.getType());
                billAmount = SCMUtil.add(billAmount, itemData.getTotalCost());
                billTaxes = SCMUtil.add(billTaxes, itemData.getTotalTax());
                itemDataList.add(itemData);
            } else {
                throw new SumoException("Cost Element Id is Not available for " + item.getCostElementName());
            }
        }

        serviceOrderData.setVendorId(vendorId);
        serviceOrderData.setDispatchLocationId(dispatchLocationId);
        serviceOrderData.setCostCenterId(createVO.getCostCenterId());
        serviceOrderData.setTotalCost(billAmount);
        serviceOrderData.setTotalAmount(SCMUtil.add(billAmount, billTaxes));
        serviceOrderData.setTotalTaxes(billTaxes);
        serviceOrderData.setType(createVO.getType());
        serviceOrderData.setStatus(ServiceOrderStatus.PENDING_APPROVAL_L1.toString());
        serviceOrderData.setGeneratedBy(userId);
        if (!createVO.getTagName().isEmpty() && !createVO.getTagName().equalsIgnoreCase("")) {
            serviceOrderData.setTagName(createVO.getTagName());
        }
        serviceOrderData.setGenerationTime(SCMUtil.getCurrentTimestamp());
        serviceOrderData.setLastUpdateTime(SCMUtil.getCurrentTimestamp());
        serviceOrderData.setLastUpdatedBy(userId);
        serviceOrderData.setComment(comment);
        // serviceOrderData.setReceiptNumber(generateSOId(vendor));
        serviceOrderData.setVendorNotified(SCMServiceConstants.SCM_CONSTANT_NO);
        if (documentId != null) {
            serviceOrderData.setUploadedDocumentId(documentId);
        }
        if (Objects.nonNull(approvalOfHodDocId)) {
            serviceOrderData.setApprovalOfHod(approvalOfHodDocId);
        }
        serviceOrderData = dao.add(serviceOrderData, true);

        // On successful persistence
        if (serviceOrderData != null && serviceOrderData.getId() != null) {
            serviceOrderId = serviceOrderData.getId();
            for (ServiceOrderItemData itemData : itemDataList) {
                itemData.setServiceOrderId(serviceOrderId);
                itemData = dao.add(itemData, false);
                if (serviceOrderData.getType().equalsIgnoreCase(BudgetAuditActions.CAPEX.value())) {
                    updateBudgetAuditDetailForCreation(itemData, serviceOrderData);
                }
            }
            dao.flush();
            serviceOrderData.setServiceOrderItemDataList(itemDataList);
            generateSOEventStatus(userId, serviceOrderData.getId());
        }
        return serviceOrderId;
    }

    private void updateBudgetAuditDetailForCreation(ServiceOrderItemData itemData, ServiceOrderData serviceOrderData) throws SumoException {
        List<BusinessCostCenter> bccs = getBusinessCostCentersData();
        CostElementData costElement = dao.find(CostElementData.class, itemData.getCostElementId());
        BusinessCostCenter bcc = bccs.stream()
                .filter(b -> b.getId().equals(itemData.getBusinessCostCenterId())).findFirst().orElse(null);
        CapexAuditDetailData capexAuditDetail = dao.findCapexAuditData(Integer.parseInt(bcc.getCode()));
        CapexBudgetDetailData capexBudgetData = dao.findBudgetUnit(Integer.parseInt(bcc.getCode()), costElement.getDepartment().getName());
        List<String> actions = Arrays.asList(BudgetAuditActions.REMAINING_AMOUNT.value(), BudgetAuditActions.RUNNING_AMOUNT.value());
        for (String action : actions) {
            BudgetAuditDetailData budgetAuditDetail = new BudgetAuditDetailData();
            budgetAuditDetail.setCapexAuditId(capexAuditDetail.getId());
            budgetAuditDetail.setCapexBudgetDetailId(capexBudgetData.getId());
            budgetAuditDetail.setActionBy(serviceOrderData.getGeneratedBy());
            budgetAuditDetail.setAction(BudgetAuditActions.CREATED.value());
            budgetAuditDetail.setKeyType(BudgetAuditActions.SO_ID.value());
            budgetAuditDetail.setKeyValue(serviceOrderData.getId());
            budgetAuditDetail.setActionTime(SCMUtil.getCurrentTimestamp());
            if (action.equalsIgnoreCase(BudgetAuditActions.REMAINING_AMOUNT.value())) {
                budgetAuditDetail.setAmountType(BudgetAuditActions.REMAINING_AMOUNT.value());
                budgetAuditDetail.setPreviousValue(capexBudgetData.getRemainingAmount());
                budgetAuditDetail.setFinalValue(capexBudgetData.getRemainingAmount().subtract(itemData.getTotalCost().add(itemData.getTotalTax())));
                budgetAuditDetail.setActionType(BudgetAuditActions.REDUCTION.value());
            } else {
                budgetAuditDetail.setAmountType(BudgetAuditActions.RUNNING_AMOUNT.value());
                budgetAuditDetail.setPreviousValue(capexBudgetData.getRunningAmount());
                budgetAuditDetail.setFinalValue(capexBudgetData.getRunningAmount().add(itemData.getTotalCost().add(itemData.getTotalTax())));
                budgetAuditDetail.setActionType(BudgetAuditActions.ADDITION.value());
            }
            dao.add(budgetAuditDetail, true);
        }
        capexBudgetData.setRemainingAmount(capexBudgetData.getRemainingAmount().subtract(itemData.getTotalCost().add(itemData.getTotalTax())));
        capexBudgetData.setRunningAmount(capexBudgetData.getRunningAmount().add(itemData.getTotalCost().add(itemData.getTotalTax())));
        dao.update(capexBudgetData, true);
    }

    private void generateSOEventStatus(int userId, Integer soId) throws SumoException {
        ServiceOrderStatusEventData eventData = new ServiceOrderStatusEventData();
        eventData.setFromStatus(ServiceOrderStatus.CREATED.toString());
        eventData.setToStatus(ServiceOrderStatus.PENDING_APPROVAL_L1.toString());
        eventData.setServiceOrderId(soId);
        eventData.setUpdatedBy(userId);
        eventData.setUpdateTime(SCMUtil.getCurrentTimestamp());
        eventData.setTransitionStatus(SCMTransitionStatus.SUCCESS.name());
        dao.add(eventData, true);
    }

    private ServiceOrderItemData createServiceOrderItemData(ServiceOrderItem item, String type) {
        ServiceOrderItemData itemData = new ServiceOrderItemData();
        if (item.getCostElementDate() != null) {
            itemData.setCostElementDate(item.getCostElementDate());
        }
        if (item.getCostElementToDate() != null) {
            itemData.setCostElementToDate(item.getCostElementToDate());
        }
        itemData.setCostElementId(item.getCostElementId());
        itemData.setUnitOfMeasure(item.getUnitOfMeasure());
        itemData.setCostElementName(item.getCostElementName());
        itemData.setServiceDescription(item.getServiceDescription());
        itemData.setReceivedQuantity(item.getReceivedQuantity());
        itemData.setAscCode(item.getAscCode());
        itemData.setUnitPrice(item.getUnitPrice());
        itemData.setRequestedQuantity(item.getRequestedQuantity());
        itemData.setTotalTax(item.getTotalTax());
        itemData.setTotalCost(item.getTotalCost());
        itemData.setTaxRate(item.getTaxRate());
        itemData.setTdsRate(item.getTdsRate());
        itemData.setBusinessCostCenterId(item.getBusinessCostCenterId());
        itemData.setBusinessCostCenterName(item.getBusinessCostCenterName());
        itemData.setType(type);
        return itemData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<ServiceOrder> findServiceOrders(Integer bccId, Integer vendorId, Integer serviceOrderId, Integer userId,
                                                List<ServiceOrderStatus> statusList, Date startDate, Date endDate, boolean showAll) {

        List<ServiceOrderData> serviceOrderDataList = findServiceOrderDataList(bccId, vendorId, serviceOrderId, userId, statusList, startDate, endDate, showAll, false,null);
        return convertServiceOrders(serviceOrderDataList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public ServiceOrderSummary findServiceOrdersShort(Integer bccId, Integer vendorId, Integer serviceOrderId, Integer userId,
                                                      List<ServiceOrderStatus> statusList, Date startDate, Date endDate, boolean showAll , Integer costCenterId) {
        List<ServiceOrderData> serviceOrderDataList = findServiceOrderDataList(bccId, vendorId, serviceOrderId, userId, statusList, startDate, endDate, showAll, true,costCenterId);
        ServiceOrderSummary orderSummary = convertServiceOrdersShort(serviceOrderDataList);
        if (vendorId != null && !orderSummary.getServiceOrderShortList().isEmpty() && orderSummary.getServiceOrderShortList().size() > 0) {
            List<Integer> costElements = null;
            Boolean findCostElements = true;
            if (userId != null && !showAll) {

                costElements = dao.findCostElementsForEmployee(userId);

                if (costElements == null || costElements.isEmpty()) {
                    findCostElements = false;
                }
            }
            if (findCostElements) {
                if (startDate == null && endDate == null) {
                    startDate = SCMUtil.getCurrentBusinessDate();
                    endDate = startDate;
                }

                List<ServiceOrderStatus> vendorOrderStatusList = Arrays.asList(ServiceOrderStatus.PENDING_APPROVAL_L1,
                        ServiceOrderStatus.PENDING_APPROVAL_L2,ServiceOrderStatus.PENDING_APPROVAL_L3, ServiceOrderStatus.APPROVED, ServiceOrderStatus.IN_PROGRESS,ServiceOrderStatus.FIN_APPROVAL_L1);

                endDate = SCMUtil.getEndOfBusinessDay(startDate.getTime() == endDate.getTime() ? startDate : endDate);
                List<ServiceOrderData> vendorServiceOrderDataList = dao.findServiceOrders(null, vendorId, null, vendorOrderStatusList,
                        startDate, endDate, costElements, showAll, true,costCenterId);


                if (!vendorServiceOrderDataList.isEmpty() && vendorServiceOrderDataList.size() > 0) {
                    BigDecimal totalApprovedAmount = BigDecimal.ZERO;
                    BigDecimal pendingApprovalL1 = BigDecimal.ZERO;
                    BigDecimal pendingApprovalL2 = BigDecimal.ZERO;
                    BigDecimal totalInprogessAmount = BigDecimal.ZERO;
                    BigDecimal pendingApprovalL3  = BigDecimal.ZERO;
                    BigDecimal finApprovalL1 = BigDecimal.ZERO;
                    for (ServiceOrderData serviceOrderData : vendorServiceOrderDataList) {
                        if (serviceOrderData.getStatus().equalsIgnoreCase(ServiceOrderStatus.APPROVED.value())) {
                            totalApprovedAmount = AppUtils.add(totalApprovedAmount, serviceOrderData.getTotalAmount());
                        } else if (serviceOrderData.getStatus().equalsIgnoreCase(ServiceOrderStatus.PENDING_APPROVAL_L1.value())) {
                            pendingApprovalL1 = AppUtils.add(pendingApprovalL1, serviceOrderData.getTotalAmount());
                        } else if (serviceOrderData.getStatus().equalsIgnoreCase(ServiceOrderStatus.PENDING_APPROVAL_L2.value())) {
                            pendingApprovalL2 = AppUtils.add(pendingApprovalL2, serviceOrderData.getTotalAmount());
                        } else if (serviceOrderData.getStatus().equalsIgnoreCase(ServiceOrderStatus.IN_PROGRESS.value())) {
                            totalInprogessAmount = AppUtils.add(totalInprogessAmount, serviceOrderData.getTotalAmount());
                        } else if (serviceOrderData.getStatus().equalsIgnoreCase(ServiceOrderStatus.PENDING_APPROVAL_L3.value())) {
                            pendingApprovalL3 = AppUtils.add(pendingApprovalL3,serviceOrderData.getTotalAmount());
                        } else if (serviceOrderData.getStatus().equalsIgnoreCase(ServiceOrderStatus.FIN_APPROVAL_L1.value())) {
                            finApprovalL1 = AppUtils.add(finApprovalL1, serviceOrderData.getTotalAmount());
                        }
                    }
                    orderSummary.setApprovedAmount(totalApprovedAmount);
                    orderSummary.setInProgessAmount(totalInprogessAmount);
                    orderSummary.setPendingApprovalL1(pendingApprovalL1);
                    orderSummary.setPendingApprovalL2(pendingApprovalL2);
                    orderSummary.setPendingApprovalL3(pendingApprovalL3);
                    orderSummary.setFinApprovalL1(finApprovalL1);
                }
            }

        }
        return orderSummary;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<ServiceOrderData> findServiceOrderDataList(Integer bccId, Integer vendorId, Integer serviceOrderId, Integer userId,
                                                           List<ServiceOrderStatus> statusList, Date startDate, Date endDate, boolean showAll, boolean isShort,Integer costCenterId) {


        List<Integer> costElements = null;
        if (userId != null && !showAll) {
            LOG.info("Trying To Find Mapped Cost Centers for user Id : {}", userId);
            costElements = dao.findCostElementsForEmployee(userId);

            if (costElements == null || costElements.isEmpty()) {
                LOG.info("No Mapped Cost Centers Found For User Id : {}", userId);
                return new ArrayList<>();
            }
            LOG.info("{} Cost elements Mapped To User Id :{} found", costElements.size(), userId);

        }
        if (startDate == null && endDate == null) {
            startDate = SCMUtil.getCurrentBusinessDate();
            endDate = startDate;
        }
        endDate = SCMUtil.getEndOfBusinessDay(startDate.getTime() == endDate.getTime() ? startDate : endDate);
        LOG.info("Cost Center : {} ", costCenterId);
        List<ServiceOrderData> serviceOrderDataList = dao.findServiceOrders(bccId, vendorId, serviceOrderId, statusList,
                startDate, endDate, costElements, showAll, isShort,costCenterId);
        if(Objects.nonNull(costCenterId) && Objects.nonNull(serviceOrderDataList)){
            serviceOrderDataList = serviceOrderDataList.stream().filter(so -> so.getCostCenterId().equals(costCenterId)).
                    collect(Collectors.toList());
        }
        if (Objects.isNull(serviceOrderDataList) || serviceOrderDataList.isEmpty()) {
            LOG.info("No Service Orders Found In Db");
        } else {
            LOG.info("Found {} Service Orders In Db", serviceOrderDataList.size());
        }

        return serviceOrderDataList;

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<ServiceOrder> getSosForAdvance(Integer vendorId) throws SumoException {
        VendorAdvancePayment vendorAdvancePayment = VendorAdvancePayment.builder().vendorId(vendorId).
                advanceType(SCMServiceConstants.STAND_ALONE_ADVANCE).advanceStatus("ALL").build();
        VendorAdvancePayment advancePayment = null;
        try {
            advancePayment = paymentRequestManagementService.getVendorAdvancePayment(vendorAdvancePayment, false, null);
        } catch (SumoException e) {
            throw new SumoException("Pending Stand Alone Advance..!",
                    "Please Settle all the Stand Alone Advance Payments related to the Vendor " + scmCache.getVendorDetail(vendorId).getEntityName());
        }
        try {
            if (Objects.isNull(advancePayment)) {
                List<ServiceOrderData> serviceOrderDataList = dao.getSosForAdvance(vendorId);
                return convertServiceOrders(serviceOrderDataList);
            } else {
                throw new SumoException("Pending Stand Alone Advance..!",
                        "Please Settle all the Stand Alone Advance Payments related to the Vendor " + scmCache.getVendorDetail(vendorId).getEntityName());
            }
        } catch (Exception e) {
            LOG.error("Exception Occurred While getting Service Order Data for Advance ::: ",e);
        }
        return new ArrayList<>();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean submitAdvanceAdjustmentRefund(VendorAdvancePayment vendorAdvancePayment, Integer createdBy) throws SumoException {
        if (Objects.nonNull(vendorAdvancePayment.getAdvanceType())) {
            List<AdvancePaymentData> advancePaymentDataList;
            Integer originalSoPoId = null;
            if (vendorAdvancePayment.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.PO_ADVANCE)) {
                PurchaseOrderData purchaseOrderData = dao.find(PurchaseOrderData.class, vendorAdvancePayment.getPoId());
                advancePaymentDataList = purchaseOrderData.getAdvancePaymentDatas();
                originalSoPoId = vendorAdvancePayment.getPoId();
            } else {
                ServiceOrderData serviceOrderData = dao.find(ServiceOrderData.class, vendorAdvancePayment.getSoId());
                advancePaymentDataList = serviceOrderData.getAdvancePaymentDatas();
                originalSoPoId = vendorAdvancePayment.getSoId();
            }

            if (Objects.nonNull(advancePaymentDataList) && !advancePaymentDataList.isEmpty()) {
                List<AdvancePaymentData> finalAdpList = new ArrayList<>();
                BigDecimal totalPrAmount = BigDecimal.ZERO;
                BigDecimal totalAvailableAmount = BigDecimal.ZERO;
                StringBuilder initiatedAdvanceError = new StringBuilder("");
                StringBuilder adjustInitiatedAdvanceError = new StringBuilder("");
                for (AdvancePaymentData adp : advancePaymentDataList) {
                    if (adp.getAdvanceStatus().equalsIgnoreCase(AdvancePaymentStatus.CREATED.value())) {
                        finalAdpList.add(adp);
                        totalPrAmount = totalPrAmount.add(adp.getPrAmount());
                        totalAvailableAmount = totalAvailableAmount.add(adp.getAvailableAmount());
                    }
                    if (adp.getAdvanceStatus().equalsIgnoreCase(AdvancePaymentStatus.INITIATED.value())) {
                        String msg = "Advance Id : " + adp.getAdvancePaymentId() + " Pending Vendor Advance of Rs : " + adp.getPrAmount() + "<br>";
                        initiatedAdvanceError.append(msg);
                    }
                    if (adp.getAdvanceStatus().equalsIgnoreCase(AdvancePaymentStatus.ADJUST_INITIATED.value())) {
                        String msg = "Advance Id : " + adp.getAdvancePaymentId() + " Pending Vendor Advance of Rs : " + adp.getPrAmount() + "<br>";
                        adjustInitiatedAdvanceError.append(msg);
                    }
                }
                if (!initiatedAdvanceError.toString().equalsIgnoreCase("")) {
                    throw new SumoException("Please Settle the Vendor Advance Related to this PO", initiatedAdvanceError.toString() + "Please Settle the vendor Advance to CANCEL the Purchase Order..!");
                }
                if (!adjustInitiatedAdvanceError.toString().equalsIgnoreCase("")) {
                    throw new SumoException("This PO has Some Adjustment of Vendor Advance", adjustInitiatedAdvanceError.toString() + "Please get the HOD Approval Of  PO..!");
                }
                if (totalPrAmount.compareTo(vendorAdvancePayment.getCompletePrAmount()) != 0 || totalAvailableAmount.compareTo(vendorAdvancePayment.getCompleteAvailableAmount()) != 0) {
                    throw new SumoException("AMOUNTS MISMATCH", "Please Refresh and Try Again Later..!");
                }

                validateSoPoForAdjustmentOrRefund(finalAdpList);

                AdvancePaymentData createdData = null;
                if (Objects.nonNull(vendorAdvancePayment.getSelectedSoPo())) {
                    if (!paymentRequestManagementService.validatePoSoStatus(vendorAdvancePayment, true)) {
                        String msg = "";
                        if (vendorAdvancePayment.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.PO_ADVANCE)) {
                            msg = "Selected Purchase Order is not eligible to create Vendor Advance ..!";
                        }
                        if (vendorAdvancePayment.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.SO_ADVANCE)) {
                            msg = "Selected Service Order is not eligible to create Vendor Advance ..!";
                        }
                        throw new SumoException("Selected Item is Not In Approved Status..! ", msg);
                    }
                    createdData = paymentRequestManagementService.createLinkedAdvancePayment(null, vendorAdvancePayment, createdBy, null, finalAdpList);
                }

                String lastStatus = null;
                for (AdvancePaymentData advancePaymentData : finalAdpList) {
                    if (vendorAdvancePayment.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.SO_ADVANCE)) {
                        ServiceOrderData serviceOrderData = dao.find(ServiceOrderData.class, originalSoPoId);
                        lastStatus = serviceOrderData.getStatus();
                        break;
                    }
                    if (vendorAdvancePayment.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.PO_ADVANCE)) {
                        PurchaseOrderData purchaseOrderData = dao.find(PurchaseOrderData.class, originalSoPoId);
                        lastStatus = purchaseOrderData.getStatus();
                        break;
                    }
                }

                for (AdvancePaymentData advancePaymentData : finalAdpList) {
                    String prevStatus = advancePaymentData.getAdvanceStatus();
                    if (Objects.nonNull(vendorAdvancePayment.getSelectedSoPo())) {
                        advancePaymentData.setAdjustedPoSo(vendorAdvancePayment.getSelectedSoPo());
                        advancePaymentData.setChildAdvance(createdData);
                    }
                    if (Objects.nonNull(vendorAdvancePayment.getRefundDate())) {
                        advancePaymentData.setAdvanceStatus(AdvancePaymentStatus.REFUND_INITIATED.value());
                        advancePaymentData.setRefundInitiatedBy(createdBy);
                        advancePaymentData.setRefundDate(AppUtils.getDate(vendorAdvancePayment.getRefundDate(), "yyyy-MM-dd"));
                        paymentRequestManagementService.logAdvancePaymentStatus(prevStatus, advancePaymentData.getAdvanceStatus(), advancePaymentData, createdBy);
                    }
                    advancePaymentData.setLastUpdatedDate(AppUtils.getCurrentTimestamp());
                    advancePaymentData.setLastUpdatedBy(createdBy);
                    if (vendorAdvancePayment.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.SO_ADVANCE)) {
                        ServiceOrderData serviceOrderData = dao.find(ServiceOrderData.class, originalSoPoId);
                        advancePaymentData.setLastPoSoStatus(lastStatus);
                        serviceOrderData.setStatus(ServiceOrderStatus.PENDING_HOD_APPROVAL.value());
                        serviceOrderData.setLastUpdateTime(AppUtils.getCurrentTimestamp());
                        serviceOrderData.setLastUpdatedBy(createdBy);
                        dao.update(serviceOrderData, true);
                    }
                    if (vendorAdvancePayment.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.PO_ADVANCE)) {
                        PurchaseOrderData purchaseOrderData = dao.find(PurchaseOrderData.class, originalSoPoId);
                        advancePaymentData.setLastPoSoStatus(lastStatus);
                        purchaseOrderData.setStatus(PurchaseOrderStatus.PENDING_HOD_APPROVAL.value());
                        purchaseOrderData.setLastUpdateTime(AppUtils.getCurrentTimestamp());
                        purchaseOrderData.setLastUpdatedBy(createdBy);
                        dao.update(purchaseOrderData, true);
                    }
                    dao.update(advancePaymentData, true);
                }

            } else {
                throw new SumoException("No Vendor Advances Found..! ", "Please Refresh and Try Again Later..!");
            }

            return true;
        }
        return false;
    }

    @Override
    public void validateSoPoForAdjustmentOrRefund(List<AdvancePaymentData> advancePaymentDataList) throws SumoException {
        for (AdvancePaymentData advancePaymentData : advancePaymentDataList) {
            if (!(Objects.isNull(advancePaymentData.getAdjustedPoSo()) && Objects.isNull(advancePaymentData.getRefundDate()))) {
                throw new SumoException("Advance Related to this is already Sent for adjustment", "Please Check the advance related to this Order is already sent for Adjustment/Refund..! [" + advancePaymentData.getAdvancePaymentId() + "]");
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean approveRejectAdjustmentRefund(VendorAdvancePayment vendorAdvancePayment, Integer approvedBy, String approveReject) {
        try {
            AdvancePaymentData advancePaymentMainData = dao.find(AdvancePaymentData.class, vendorAdvancePayment.getAdvancePaymentId());
            List<AdvancePaymentData> parentAdvancePaymentDataList = Objects.nonNull(advancePaymentMainData.getAdjustedPoSo()) ? paymentRequestManagementDao.getAllParentAdvances(advancePaymentMainData) :
                    paymentRequestManagementDao.getAllAdvancesByAdvanceStatus(advancePaymentMainData, AdvancePaymentStatus.REFUND_INITIATED.value());
            Integer childAdvanceToCancel = null;
            Integer childAdvanceToApprove = null;
            if (Objects.nonNull(parentAdvancePaymentDataList) && !parentAdvancePaymentDataList.isEmpty()) {
                for (AdvancePaymentData advancePaymentData : parentAdvancePaymentDataList) {
                    String prevStatus = advancePaymentData.getAdvanceStatus();
                    if (approveReject.equalsIgnoreCase(PaymentRequestStatus.REJECTED.value())) {
                        if (Objects.nonNull(advancePaymentData.getAdjustedPoSo())) {
                            childAdvanceToCancel = advancePaymentData.getChildAdvance().getAdvancePaymentId();
                            advancePaymentData.setAdjustedPoSo(null);
                            advancePaymentData.setChildAdvance(null);
                        }
                        if (Objects.nonNull(advancePaymentData.getRefundDate())) {
                            advancePaymentData.setAdvanceStatus(AdvancePaymentStatus.CREATED.value());
                            advancePaymentData.setRefundDate(null);
                            advancePaymentData.setRefundInitiatedBy(null);
                            paymentRequestManagementService.logAdvancePaymentStatus(prevStatus, advancePaymentData.getAdvanceStatus(), advancePaymentData, approvedBy);
                        }
                        if (advancePaymentData.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.SO_ADVANCE)) {
                            ServiceOrderData serviceOrderData = dao.find(ServiceOrderData.class, vendorAdvancePayment.getSoId());
                            serviceOrderData.setStatus(advancePaymentData.getLastPoSoStatus());
                            serviceOrderData.setLastUpdateTime(AppUtils.getCurrentTimestamp());
                            serviceOrderData.setLastUpdatedBy(approvedBy);
                            dao.update(serviceOrderData, true);
                        }
                        if (advancePaymentData.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.PO_ADVANCE)) {
                            PurchaseOrderData purchaseOrderData = dao.find(PurchaseOrderData.class, vendorAdvancePayment.getPoId());
                            purchaseOrderData.setStatus(advancePaymentData.getLastPoSoStatus());
                            purchaseOrderData.setLastUpdateTime(AppUtils.getCurrentTimestamp());
                            purchaseOrderData.setLastUpdatedBy(approvedBy);
                            dao.update(purchaseOrderData, true);
                        }
                        advancePaymentData.setLastPoSoStatus(null);
                        advancePaymentData.setLastUpdatedDate(AppUtils.getCurrentTimestamp());
                        advancePaymentData.setLastUpdatedBy(approvedBy);
                        dao.update(advancePaymentData, true);
                        paymentRequestManagementService.updateVendorBlockCache(advancePaymentData);
                    }
                    if (approveReject.equalsIgnoreCase(PaymentRequestStatus.APPROVED.value())) {
                        if (Objects.nonNull(advancePaymentData.getAdjustedPoSo()) || Objects.nonNull(advancePaymentData.getRefundDate())) {
                            if (Objects.nonNull(advancePaymentData.getAdjustedPoSo())) {
                                childAdvanceToApprove = advancePaymentData.getChildAdvance().getAdvancePaymentId();
                                advancePaymentData.setAdvanceStatus(AdvancePaymentStatus.ADJUSTED.value());
                            }
                            if (Objects.nonNull(advancePaymentData.getRefundDate())) {
                                advancePaymentData.setAdvanceStatus(AdvancePaymentStatus.REFUND_APPROVED.value());
                            }
                            if (advancePaymentData.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.PO_ADVANCE)) {
                                if (advancePaymentData.getLastPoSoStatus().equalsIgnoreCase(ServiceOrderStatus.APPROVED.value())) {
                                    purchaseOrderService.cancelPurchaseOrder(advancePaymentData.getPurchaseOrderData().getId(), approvedBy);
                                }
                                if (advancePaymentData.getLastPoSoStatus().equalsIgnoreCase(ServiceOrderStatus.IN_PROGRESS.value())) {
                                    PurchaseOrderData purchaseOrderData = advancePaymentData.getPurchaseOrderData();
                                    purchaseOrderService.createAutoLogsForPurchaseOrder(purchaseOrderData);
                                    purchaseOrderData.setStatus(PurchaseOrderStatus.CLOSED.value());
                                    purchaseOrderService.sendCLosedPoEmailNotification(purchaseOrderData, SCMServiceConstants.SYSTEM_USER);
                                    paymentRequestManagementDao.update(purchaseOrderData, true);
                                }
                            }
                            if (advancePaymentData.getAdvanceType().equalsIgnoreCase(SCMServiceConstants.SO_ADVANCE)) {
                                if (advancePaymentData.getLastPoSoStatus().equalsIgnoreCase(ServiceOrderStatus.APPROVED.value())) {
                                    serviceOrderManagementService.cancelServiceOrder(advancePaymentData.getServiceOrderData().getId(), approvedBy);
                                }
                                if (advancePaymentData.getLastPoSoStatus().equalsIgnoreCase(ServiceOrderStatus.IN_PROGRESS.value())) {
                                    serviceOrderManagementService.closeServiceOrder(advancePaymentData.getServiceOrderData().getId(), approvedBy);
                                }
                            }
                            advancePaymentData.setLastUpdatedDate(AppUtils.getCurrentTimestamp());
                            advancePaymentData.setLastUpdatedBy(approvedBy);
                            paymentRequestManagementDao.update(advancePaymentData, true);
                            paymentRequestManagementService.logAdvancePaymentStatus(prevStatus, advancePaymentData.getAdvanceStatus(), advancePaymentData, approvedBy);
                        }
                        paymentRequestManagementService.updateVendorBlockCache(advancePaymentData);
                    }
                }

                if (approveReject.equalsIgnoreCase(PaymentRequestStatus.REJECTED.value()) && Objects.nonNull(childAdvanceToCancel)) {
                    AdvancePaymentData paymentData = dao.find(AdvancePaymentData.class, childAdvanceToCancel);
                    paymentRequestManagementService.markAdjustedSoPoRejected(advancePaymentMainData, approvedBy, PaymentRequestStatus.REJECTED.value(), paymentData);
                }
                if (approveReject.equalsIgnoreCase(PaymentRequestStatus.APPROVED.value()) && Objects.nonNull(childAdvanceToApprove)) {
                    AdvancePaymentData adjustedAdvance = dao.find(AdvancePaymentData.class, childAdvanceToApprove);
                    adjustedAdvance.setAdvanceStatus(AdvancePaymentStatus.CREATED.value());
                    paymentRequestManagementDao.update(adjustedAdvance, true);
                }

                for (AdvancePaymentData advancePaymentData : parentAdvancePaymentDataList) {
                    paymentRequestManagementService.updateVendorBlockCache(advancePaymentData);
                }
                return true;
            }
            return false;
        } catch (Exception e) {
            LOG.error("Exception Occurred While Approving/Rejecting the Vendor Advance :: ", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public DocumentDetail uploadApprovalOfHod(FileType type, MimeType mimeType, DocUploadType docType, Integer userId, MultipartFile file, String docName) throws DocumentException, IOException {
        String fileName = docName + "_"+ SCMUtil.getCurrentTimeISTStringWithNoColons()  + "."
                + mimeType.name().toLowerCase();
        String baseDir = "APPROVAL_OF_HOD" + File.separator + SCMUtil.getCurrentYear() + File.separator
                + SCMUtil.getCurrentMonthName() + File.separator + SCMUtil.getCurrentDayofMonth();
        return paymentRequestManagementService.uploadDocument(type, mimeType, docType, userId, file, fileName, baseDir);
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<ServiceOrder> findPendingServiceOrders(Integer vendorId, Integer dispatchId, Integer userId, Integer companyId, Integer locationId, boolean showAll) {

        List<ServiceOrderStatus> statusList = Arrays.asList(ServiceOrderStatus.APPROVED,
                ServiceOrderStatus.IN_PROGRESS);
        List<Integer> costElements = null;
        if (userId != null) {
            costElements = dao.findCostElementsForEmployee(userId);
            if (costElements == null || costElements.isEmpty()) {
                return new ArrayList<>();
            }
        }
        List<BusinessCostCenter> bccs = getBusinessCostCentersData();
        List<ServiceOrderData> serviceOrderDataList = dao.findServiceOrders(vendorId, dispatchId, statusList,
                costElements, showAll);
        List<ServiceOrder> serviceOrder = convertServiceOrders(serviceOrderDataList);
        List<ServiceOrder> serviceOrderNew = new ArrayList<ServiceOrder>();
        for (ServiceOrder order : serviceOrder) {
            boolean check = false;
            for (ServiceOrderItem orderItem : order.getOrderItems()) {
                if (orderItem.getReceivedQuantity() != null) {
                    orderItem.setPendingQuantity(
                            orderItem.getRequestedQuantity().subtract(orderItem.getReceivedQuantity()));
                } else {
                    orderItem.setPendingQuantity(orderItem.getRequestedQuantity());
                }
                BusinessCostCenter bcc = bccs.stream()
                        .filter(b -> b.getId().equals(orderItem.getBusinessCostCenterId())).findFirst().orElse(null);
                if (bcc == null) {
                    check = true;
                    break;
                }
                orderItem.setLocationId(bcc.getLocation().getId());
                if (!bcc.getCompany().getId().equals(companyId) || !bcc.getState().getId().equals(locationId)) {
                    check = true;
                    break;
                }
            }
            if (!check) {
                serviceOrderNew.add(order);
            }
        }
        return serviceOrderNew;
    }

    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<BusinessCostCenter> getBusinessCostCentersData() {
        List<BusinessCostCenter> list = new ArrayList<>();
        for (BusinessCostCenterData d : dao.findAll(BusinessCostCenterData.class)) {
            if (AppConstants.ACTIVE.equals(d.getStatus())) {
                list.add(SCMDataConverter.convertToIdCodeName(d, masterDataCache));
            }
        }
        return list;
    }

    private boolean updateServiceOrderStatusEvent(ServiceOrderData so, ServiceOrderStatus toStatus, Integer userId) {
        ServiceOrderStatusEventData eventData = dao.findServiceOrderStatus(so.getId());
        eventData.setFromStatus(eventData.getToStatus());
        eventData.setToStatus(toStatus.name());
        eventData.setServiceOrderId(so.getId());
        eventData.setUpdatedBy(userId);
        eventData.setUpdateTime(SCMUtil.getCurrentTimestamp());
        eventData.setTransitionStatus(SCMTransitionStatus.SUCCESS.name());
        dao.update(eventData, true);
        return true;
    }

    private boolean approveServiceOrder(Integer soId, Integer userId, ServiceOrderData serviceOrderData) {
        List<BusinessCostCenter> bccs = getBusinessCostCentersData();
        try {
            if (serviceOrderData != null) {
                VendorDetail vendorDetail = scmCache.getVendorDetail(serviceOrderData.getVendorId());
                Optional<VendorDispatchLocation> vendorLocation = vendorDetail.getDispatchLocations().stream()
                        .filter(dispatchLocation -> dispatchLocation.getDispatchId()
                                .equals(serviceOrderData.getDispatchLocationId()))
                        .findFirst();
                if (vendorLocation.isPresent()) {
                    ServiceOrderStatus toStatus = ServiceOrderStatus.APPROVED;
                    if (updateServiceOrderStatusEvent(serviceOrderData, toStatus, userId)) {
                        serviceOrderData.setStatus(toStatus.name());
                        serviceOrderData.setLastUpdatedBy(userId);
                        serviceOrderData.setApprovedBy(userId);
                        serviceOrderData.setLastUpdateTime(SCMUtil.getCurrentTimestamp());
                        serviceOrderData.setVendorNotified(SCMServiceConstants.SCM_CONSTANT_YES);
                        dao.update(serviceOrderData, false);
                    }
                }
                BusinessCostCenter bcc = bccs.stream()
                        .filter(b -> b.getId().equals(serviceOrderData.getServiceOrderItemDataList().get(0).getBusinessCostCenterId())).findFirst().orElse(null);

                VendorSOEmailNotificationTemplate emailTemplate = new VendorSOEmailNotificationTemplate(
                        serviceOrderData, vendorDetail, vendorLocation.get(), props.getBasePath(),
                        masterDataCache.getEmployee(serviceOrderData.getGeneratedBy()), bcc.getCompany().getName(), props.getEnvType(), masterDataCache.getEmployee(userId));

                try {
                    Pair<File, DocumentDetailData> poInvoice = createInvoice(emailTemplate,
                            serviceOrderData.getGeneratedBy(), serviceOrderData.getId());
                    File uploadedFile = null;
                    String uploadedMimeType = null;
                    try {
                        if (serviceOrderData.getUploadedDocumentId() != null) {
                            DocumentDetailData documentData = dao.find(DocumentDetailData.class, serviceOrderData.getUploadedDocumentId());
                            uploadedMimeType = documentData.getMimeType();
                            DocumentDetail document = convert(documentData);
                            FileDetail fileDetail = new FileDetail(document.getS3Bucket(), document.getS3Key(),
                                    document.getFileUrl());
                            uploadedFile = fileArchiveService.getFileFromS3(props.getBasePath() + File.separator + "s3", fileDetail);
                        }
                    } catch (Exception e) {
                        LOG.error("Error while fetching for Uploaded SO Document");
                    }
                    Boolean sendMail = validateForMail(serviceOrderData);
                    List<ServiceOrderItemData> serviceOrderItemData = serviceOrderData.getServiceOrderItemDataList() ;
                    if(Objects.nonNull(serviceOrderItemData)){
                        List<String>departmentEmails = new ArrayList<>() ;
                        Map<Integer,String> departmentInfo = new HashMap<>();
                        for (ServiceOrderItemData item : serviceOrderItemData) {
                            Integer serviceOrderItemId = item.getId();
                            Integer costElementsId = item.getCostElementId();
                            CostElementData costElementData = dao.find(CostElementData.class, costElementsId);
                            if (Objects.nonNull(costElementData.getDepartment()) && Objects.nonNull(costElementData.getDepartment().getName())) {
                                departmentInfo.put(serviceOrderItemId, costElementData.getDepartment().getName());
                            } else {
                                departmentInfo.put(serviceOrderItemId, "");
                            }
                            if (Objects.nonNull(costElementData.getDepartment()) && Objects.nonNull(costElementData.getDepartment().getEmail())) {
                                departmentEmails.add(costElementData.getDepartment().getEmail());
                            }
                        }
                        if(Objects.nonNull(masterDataCache.getEmployeeBasicDetail(userId)) && Objects.nonNull(masterDataCache.getEmployeeBasicDetail(userId).getEmailId())){
                              departmentEmails.add(masterDataCache.getEmployeeBasicDetail(userId).getEmailId());
                        }
                        Integer emailsLength =  departmentEmails.size() ;
                        String[] emails = new String[emailsLength];
                        for (int index = 0; index < emailsLength; index++) {
                            emails[index] = departmentEmails.get(index);
                        }
                        emailTemplate.setDeptInfo(departmentInfo);
                        emailTemplate.setDepartment(true);
                        notificationService.sendSONotification(serviceOrderData, vendorDetail, emailTemplate, poInvoice.getFirst(), uploadedFile, uploadedMimeType, emails, sendMail ,false) ;
                    }

                    emailTemplate.setDeptInfo(null);
                    emailTemplate.setDepartment(false);
                    ServiceOrderNotificationData sentNotification = notificationService
                            .sendSONotification(serviceOrderData, vendorDetail, emailTemplate, poInvoice.getFirst(), uploadedFile, uploadedMimeType, getEmails(serviceOrderData), sendMail,true);
                    if (sentNotification != null) {
                        serviceOrderData.setVendorNotified(SCMServiceConstants.SCM_CONSTANT_YES);
                        serviceOrderData.setSoInvoiceDocument(poInvoice.getSecond());
                        dao.update(serviceOrderData, false);// updating service order with
                        // invoice
                    }
                } catch (Exception e) {
                    LOG.error("Encountered error while uploading file to S3 service and sending notification ::::", e);
                    throw new SumoException("Service Order creation failed since upload of S3 document failed");
                }
                dao.flush();
                return true;
            }
        } catch (Exception e) {
            LOG.error("Encountered error while approving Purchase Order {} by UserId {}", soId, userId, e);
        }
        return false;
    }

    private Boolean validateForMail(ServiceOrderData serviceOrderData) {
        // Hardcoded BCC ID's of BCC_CODE(26130,24002,26100,26185,26282,26299,26327,26422,26427,26429,26426,26495,26496)
        List<Integer> unitList = new ArrayList<>(Arrays.asList(645, 644, 580, 579, 578, 573, 485, 454, 436, 314, 203, 172, 22));
        for (ServiceOrderItemData data : serviceOrderData.getServiceOrderItemDataList()) {
            if (unitList.contains(data.getBusinessCostCenterId())) {
                return true;
            }
        }
        return false;
    }

    private String[] getEmails(ServiceOrderData serviceOrderData) {
        CostCenterData costCenterData = dao.find(CostCenterData.class, serviceOrderData.getCostCenterId());
        if (costCenterData != null) {
            List<String> emailsList = new ArrayList<>();
            if (Objects.nonNull(costCenterData.getCostCenterEmail())) {
                emailsList.add(costCenterData.getCostCenterEmail());
            }
            if (Objects.nonNull(costCenterData.getOwner())) {
                if(Objects.nonNull(masterDataCache.getEmployeeBasicDetail(costCenterData.getOwner())) && Objects.nonNull(masterDataCache.getEmployeeBasicDetail(costCenterData.getOwner()).getEmailId())){
                    emailsList.add(masterDataCache.getEmployeeBasicDetail(costCenterData.getOwner()).getEmailId());
                }
            }
            return emailsList.toArray(new String[emailsList.size()]);
        } else {
            return props.vendorPurchaseEmailCC();
        }
    }

    public Pair<File, DocumentDetailData> createInvoice(VendorSOEmailNotificationTemplate template, Integer generatedBy,
                                                        Integer poId) throws TemplateRenderingException, IOException, DocumentException, SumoException {
        String templateString = template.getContent();
        File poFile = PdfHelper.createPdf(templateString, props.getBasePath(), template.getName() + "." + MimeType.PDF.extension());
        FileDetail fileDetail = fileArchiveService.saveFileToS3(props.getS3Bucket(), "serviceOrders", poFile);
        DocumentDetailData documentDetailData = new DocumentDetailData();
        documentDetailData.setS3Bucket(fileDetail.getBucket());
        documentDetailData.setS3Key(fileDetail.getKey());
        documentDetailData.setDocumentLink(poFile.getName());
        documentDetailData.setFileUrl(fileDetail.getUrl());
        documentDetailData.setDocumentUploadType(DocUploadType.SERVICE_ORDER.name());
        documentDetailData.setMimeType(MimeType.PDF.name());
        documentDetailData.setFileType(FileType.OTHERS.name());
        documentDetailData.setUpdatedBy(generatedBy);
        documentDetailData.setDocumentUploadTypeId(poId);
        documentDetailData.setUpdateTime(SCMUtil.getCurrentTimestamp());
        documentDetailData = dao.add(documentDetailData, true);
        return Pair.of(poFile, documentDetailData);
    }

    private boolean validateSOStateTransition(ServiceOrderStatus fromStatus, ServiceOrderStatus toStatus) {
        SCMTransitionData data = new SCMTransitionData();
        data.setFromStateCode(fromStatus.name());
        data.setToStateCode(toStatus.name());
        SCMStateTransitionCache.getInstance().setTransitionState(SCMStateTransitionObject.SERVICE_ORDER, data);
        return SCMTransitionStatus.SUCCESS.equals(data.getStatus());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean rejectServiceOrder(Integer soId, Integer userId) throws SumoException {
        return changeStatus(soId, userId, ServiceOrderStatus.REJECTED);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean cancelServiceOrder(Integer soId, Integer userId) throws SumoException {
        return changeStatus(soId, userId, ServiceOrderStatus.CANCELLED);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean closeServiceOrder(Integer soId, Integer userId) throws SumoException {
        return changeStatus(soId, userId, ServiceOrderStatus.CLOSED);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Integer> getServiceReceiveIdsForServiceOrder(Integer soId, String currentSrStatus) throws SumoException {
        return dao.getServiceReceivedDataIds(soId, currentSrStatus);
    }

    private boolean changeStatus(Integer soId, Integer userId, ServiceOrderStatus status) throws SumoException {
        ServiceOrderData serviceOrder = dao.find(ServiceOrderData.class, soId);
        if (serviceOrder != null) {
            validateVendorAdvance(serviceOrder);
            if (updateStatusEvent(serviceOrder.getId(), status.toString(), userId)) {
                serviceOrder.setStatus(status.name());
                serviceOrder.setLastUpdatedBy(userId);
                serviceOrder.setApprovedBy(userId);
                serviceOrder.setLastUpdateTime(SCMUtil.getCurrentTimestamp());
                if (ServiceOrderStatus.CLOSED.equals(status)) {
                    serviceOrder.setForceClosed(SCMServiceConstants.SCM_CONSTANT_YES);
                    if (serviceOrder.getType().equalsIgnoreCase(BudgetAuditActions.CAPEX.value())) {
                        for (ServiceOrderItemData itemData : serviceOrder.getServiceOrderItemDataList()) {
                            BigDecimal receivedQuantity = dao.getReceivedQuantity(itemData.getId());
                            BigDecimal pendingQuantity = itemData.getRequestedQuantity().subtract(receivedQuantity);
                            BigDecimal totalPrice = itemData.getUnitPrice().multiply(pendingQuantity);
                            BigDecimal TotalTax = (totalPrice.multiply(itemData.getTaxRate())).divide(new BigDecimal(100));
                            updateBudgetAuditDetailForClosed(itemData, serviceOrder, totalPrice.add(TotalTax), userId);
                        }
                    }
                } else {
                    if (serviceOrder.getType().equalsIgnoreCase(BudgetAuditActions.CAPEX.value())) {
                        for (ServiceOrderItemData itemData : serviceOrder.getServiceOrderItemDataList()) {
                            if (serviceOrder.getType().equalsIgnoreCase(BudgetAuditActions.CAPEX.value())) {
                                updateBudgetAuditDetailForReject(itemData, serviceOrder,
                                        ServiceOrderStatus.CANCELLED.value(), userId);
                            }
                        }
                    }
                }

                //checking for provisional SRs)
                List<Integer> provisionalSRIds = dao.getServiceReceivedDataIds(serviceOrder.getId(), ServiceOrderStatus.PROVISIONAL.value());
                if (provisionalSRIds != null) {
                    for (Integer id : provisionalSRIds) {
                        serviceReceiveManagementService.cancelServiceReceive(id, userId);
                    }
                }
                dao.update(serviceOrder, true);
                return true;
            }
        }
        return false;
    }

    private void validateVendorAdvance(ServiceOrderData serviceOrder) throws SumoException {
        List<Integer> advancePaymentIds = new ArrayList<>();
        BigDecimal amount = BigDecimal.ZERO;
        if (Objects.nonNull(serviceOrder) && Objects.nonNull(serviceOrder.getAdvancePaymentDatas()) && !serviceOrder.getAdvancePaymentDatas().isEmpty()) {
            List<AdvancePaymentData> advancePaymentDataList = serviceOrder.getAdvancePaymentDatas();
            for (AdvancePaymentData advancePaymentData : advancePaymentDataList) {
                if (advancePaymentData.getAdvanceStatus().equalsIgnoreCase(AdvancePaymentStatus.INITIATED.value())) {
                    advancePaymentIds.add(advancePaymentData.getAdvancePaymentId());
                    amount = amount.add(advancePaymentData.getPrAmount());
                }
            }
        }
        if (!advancePaymentIds.isEmpty()) {
            throw new SumoException("Please Settle the Vendor Advance Related to this SO", "Pending Vendor Advance of Rs : " + amount + "[ " + Arrays.asList(advancePaymentIds.toArray()) + " ] <br>" +
                    "Please Settle the vendor Advance to CANCEL the Service Order..!");
        }
    }

    private void updateBudgetAuditDetailForClosed(ServiceOrderItemData itemData, ServiceOrderData serviceOrderData,
                                                  BigDecimal pendingPrice, Integer userId) throws SumoException {
        List<BusinessCostCenter> bccs = getBusinessCostCentersData();
        CostElementData costElement = dao.find(CostElementData.class, itemData.getCostElementId());
        BusinessCostCenter bcc = bccs.stream().filter(b -> b.getId().equals(itemData.getBusinessCostCenterId()))
                .findFirst().orElse(null);
        CapexAuditDetailData capexAuditDetail = dao.findCapexAuditData(Integer.parseInt(bcc.getCode()));
        CapexBudgetDetailData capexBudgetData = dao.findBudgetUnit(Integer.parseInt(bcc.getCode()),
                costElement.getDepartment().getName());
        List<String> actions = Arrays.asList(BudgetAuditActions.REMAINING_AMOUNT.value(),
                BudgetAuditActions.RUNNING_AMOUNT.value());
        for (String action : actions) {
            BudgetAuditDetailData budgetAuditDetail = new BudgetAuditDetailData();
            budgetAuditDetail.setCapexAuditId(capexAuditDetail.getId());
            budgetAuditDetail.setCapexBudgetDetailId(capexBudgetData.getId());
            budgetAuditDetail.setActionBy(userId);
            budgetAuditDetail.setAction(BudgetAuditActions.CLOSED.value());
            budgetAuditDetail.setKeyType(BudgetAuditActions.SO_ID.value());
            budgetAuditDetail.setKeyValue(serviceOrderData.getId());
            budgetAuditDetail.setActionTime(SCMUtil.getCurrentTimestamp());
            if (action.equalsIgnoreCase(BudgetAuditActions.REMAINING_AMOUNT.value())) {
                budgetAuditDetail.setAmountType(BudgetAuditActions.REMAINING_AMOUNT.value());
                budgetAuditDetail.setPreviousValue(capexBudgetData.getRemainingAmount());
                budgetAuditDetail.setFinalValue(capexBudgetData.getRemainingAmount().add(pendingPrice));
                budgetAuditDetail.setActionType(BudgetAuditActions.ADDITION.value());
            } else {
                budgetAuditDetail.setAmountType(BudgetAuditActions.RUNNING_AMOUNT.value());
                budgetAuditDetail.setPreviousValue(capexBudgetData.getRunningAmount());
                budgetAuditDetail.setFinalValue(capexBudgetData.getRunningAmount().subtract(pendingPrice));
                budgetAuditDetail.setActionType(BudgetAuditActions.REDUCTION.value());
            }
            dao.add(budgetAuditDetail, true);
        }
        capexBudgetData.setRemainingAmount(capexBudgetData.getRemainingAmount().add(pendingPrice));
        capexBudgetData.setRunningAmount(capexBudgetData.getRunningAmount().subtract(pendingPrice));
        dao.update(capexBudgetData, true);
    }

    public boolean generateSOStatusEvent(ServiceOrderData serviceOrder, ServiceOrderStatus toStatus, int userId)
            throws SumoException {
        ServiceOrderStatus fromStatus = ServiceOrderStatus.valueOf(serviceOrder.getStatus());
        ServiceOrderStatusEventData eventData = new ServiceOrderStatusEventData();
        eventData.setFromStatus(fromStatus.name());
        eventData.setToStatus(toStatus.name());
        eventData.setServiceOrderId(serviceOrder.getId());
        eventData.setUpdatedBy(userId);
        eventData.setUpdateTime(SCMUtil.getCurrentTimestamp());
        eventData.setTransitionStatus(SCMTransitionStatus.SUCCESS.name());
        eventData = dao.add(eventData, true);
        return eventData.getStatusEventId() != null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public CostCenter createCentersData(CostCenter costCenter) throws SumoException {
        CostCenterData data = new CostCenterData();
        data.setCostCenterName(costCenter.getName());
        data.setCostCenterStatus(AppConstants.ACTIVE);
        data.setShortCode(costCenter.getCode());
        data.setDescription(costCenter.getDescription());
        data.setOwner(costCenter.getOwner().getId());
        data.setCostCenterEmail(costCenter.getCostCenterEmail());
        dao.add(data, true);
        return convert(data, masterDataCache);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public CostElement createElementDataOld(Integer costCenterId, CostElement costElement) throws SumoException {
        CostCenterData center = dao.find(CostCenterData.class, costCenterId);
        if (center != null) {
            CostElementData data = new CostElementData();
            data.setAscCode(costElement.getCode());
            //data.setCostCenterId(costCenterId);
            data.setCostElementName(costElement.getName());
            data.setCostElementStatus(AppConstants.ACTIVE);
            data.setDescription(costElement.getDescription());
            data.setTaxRate(costElement.getTaxRate());
            //data.setTdsRate(costElement.getTdsRate());
            dao.add(data, true);
            /*
             * for (String uom : costElement.getUoms()) { PackagingDefinition pd =
             * scmCache.getLoosePackagingDefinition(UnitOfMeasure.valueOf(uom));
             * PackagingDefinitionData pdd = dao.find(PackagingDefinitionData.class,
             * pd.getPackagingId()); CostElementPackagingMapping map = new
             * CostElementPackagingMapping();
             * map.setCostElementDataId(data.getCostElementId());
             * map.setMappingStatus(AppConstants.ACTIVE); map.setPackaging(pdd);
             * dao.add(map, false); data.getPackagingList().add(map); }
             */
            dao.flush();
            return convert(data);
        }
        return null;
    }

    private List<ServiceOrder> convertServiceOrders(List<ServiceOrderData> serviceOrderDataList) {
        if (serviceOrderDataList != null) {
            List<ServiceOrder> soList = new ArrayList<>();
            for (ServiceOrderData data : serviceOrderDataList) {
                List<VendorAdvancePayment> vendorAdvancePayments = new ArrayList<>();
                if (Objects.nonNull(data.getAdvancePaymentDatas()) && !data.getAdvancePaymentDatas().isEmpty()) {
                    for (AdvancePaymentData advancePaymentData : data.getAdvancePaymentDatas()) {
                        VendorAdvancePayment vendorAdvancePayment = VendorAdvancePayment.builder().vendorId(data.getVendorId())
                                .advanceStatus("ALL").advanceType(SCMServiceConstants.SO_ADVANCE).soId(data.getId()).build();
                        VendorAdvancePayment advance = null;
                        try {
                            advance = paymentRequestManagementService.getVendorAdvancePayment(vendorAdvancePayment, true, advancePaymentData);
                            if (Objects.nonNull(advance)) {
                                vendorAdvancePayments.add(advance);
                            }
                        } catch (Exception e) {
                            LOG.info("Exception during Vendor Advance ..!");
                        }
                    }
                }
                ServiceOrder entry = convert(data, scmCache, masterDataCache, false, vendorAdvancePayments);
                entry.setUploadedDocumentId(data.getUploadedDocumentId());
                if (Objects.nonNull(data.getApprovalOfHod())) {
                    entry.setApprovalOfHodDocumentId(data.getApprovalOfHod());
                }
                soList.add(entry);
            }
            return soList;
        }
        return null;
    }

    @Override
    public ServiceOrderSummary convertServiceOrdersShort(List<ServiceOrderData> serviceOrderDataList) {
        if (serviceOrderDataList != null) {
            List<ServiceOrderShort> soList = new ArrayList<>();
            ServiceOrderSummary serviceOrderSummary = new ServiceOrderSummary();
            for (ServiceOrderData data : serviceOrderDataList) {
                ServiceOrderShort entry = convert(data, scmCache, false,masterDataCache );
                entry.setUploadedDocumentId(data.getUploadedDocumentId());
                if (Objects.nonNull(data.getAdvancePaymentDatas()) && !data.getAdvancePaymentDatas().isEmpty()) {
                    List<VendorAdvancePayment> vendorAdvancePayments = new ArrayList<>();
                    for (AdvancePaymentData advancePaymentData : data.getAdvancePaymentDatas()) {
                        VendorAdvancePayment vendorAdvancePayment = VendorAdvancePayment.builder().vendorId(data.getVendorId())
                                .advanceStatus("ALL").advanceType(SCMServiceConstants.SO_ADVANCE).soId(data.getId()).build();
                        VendorAdvancePayment advance = null;
                        try {
                            advance = paymentRequestManagementService.getVendorAdvancePayment(vendorAdvancePayment, true, advancePaymentData);
                            if (Objects.nonNull(advance)) {
                                vendorAdvancePayments.add(advance);
                            }
                        } catch (Exception e) {
                            LOG.info("Exception during Vendor Advance ..!");
                        }
                    }
                    if (!vendorAdvancePayments.isEmpty()) {
                        entry.setVendorAdvancePayments(vendorAdvancePayments);
                    }
                }
                if (Objects.nonNull(data.getApprovalOfHod())) {
                    entry.setApprovalOfHodDocumentId(data.getApprovalOfHod());
                }
                soList.add(entry);
            }
            serviceOrderSummary.setServiceOrderShortList(soList);
            return serviceOrderSummary;
        }
        return null;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean createEmployeeCostCenterMap(Integer empId, Integer costCenterId, Integer createdBy) throws SumoException {
        EmployeeCostCenterMappingData map = new EmployeeCostCenterMappingData();
        map.setCostCenterId(costCenterId);
        map.setEmployeeId(empId);
        map.setMappingStatus(AppConstants.ACTIVE);
        map.setAddedBy(masterDataCache.getEmployee(createdBy));
        map.setAddTime(AppUtils.getCurrentTimestamp());
        try {
            dao.add(map, true);
        } catch (ConstraintViolationException e) {
            throw new SumoException("Mapping Already Exists", e);
        }

        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean removeEmployeeCostCenterMap(Integer employeeId , Integer costCenterId , Integer removedBy) throws SumoException {
        EmployeeCostCenterMappingData mapping = dao.findCostCentersEmployeeActiveMapping(employeeId,costCenterId);
        if(Objects.isNull(mapping)){
            throw new SumoException("No Mapping Found Between EmpId : " + employeeId + " Cost Center Id : "  + costCenterId);
        }
        mapping.setMappingStatus(AppConstants.IN_ACTIVE);
        mapping.setRemovedBy(masterDataCache.getEmployee(removedBy));
        mapping.setRemovedTime(AppUtils.getCurrentTimestamp());
        dao.update(mapping,true);
        return true;
    }



    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public String updateCostElementStatus(CostElement costElement) {
        String data = new String();
        CostElementData elementData = dao.find(CostElementData.class, costElement.getId());
        if (elementData.getCostElementStatus().equalsIgnoreCase(costElement.getStatus())) {
            elementData.setCostElementName(costElement.getName());
            elementData.setCapex(costElement.getCapex());
            elementData.setDescription(costElement.getDescription());
            elementData.setIsPriceUpdate(costElement.getIsPriceUpdate());
            elementData.setAscCode(costElement.getAscCode());
            elementData.setTaxRate(costElement.getTaxRate());
            elementData.setCategory(convert(costElement.getCategory()));
            elementData.setDepartment(convert(costElement.getDepartment()));
            elementData.setDivision(convert(costElement.getDivision()));
            elementData.setSubCategory(convert(costElement.getSubCategory()));
            if (costElement.getSubSubCategory() != null) {
                elementData.setSubSubCategory(convert(costElement.getSubSubCategory()));
            }
            dao.update(elementData, true);
            return data;
        } else {
            data = dao.getCostElementStatus(costElement.getId());
            if (data.isEmpty() || data.equalsIgnoreCase("")) {
                if (!costElement.getStatus().equalsIgnoreCase(ProductStatus.ACTIVE.toString())) {
                    dao.updateStatusInMappings(costElement.getId());
                }
                elementData.setCapex(costElement.getCapex());
                elementData.setCostElementStatus(costElement.getStatus());
                elementData.setCostElementName(costElement.getName());
                elementData.setDescription(costElement.getDescription());
                elementData.setIsPriceUpdate(costElement.getIsPriceUpdate());
                elementData.setAscCode(costElement.getAscCode());
                elementData.setTaxRate(costElement.getTaxRate());
                elementData.setCategory(convert(costElement.getCategory()));
                elementData.setDepartment(convert(costElement.getDepartment()));
                elementData.setDivision(convert(costElement.getDivision()));
                elementData.setSubCategory(convert(costElement.getSubCategory()));
                elementData.setIsPriceUpdate(costElement.getIsPriceUpdate());
                if (costElement.getSubSubCategory() != null) {
                    elementData.setSubSubCategory(convert(costElement.getSubSubCategory()));
                }
                dao.update(elementData, true);
                return data;
            }
        }

        return data;
    }

    @Override
    public Map<String, List<ListDetail>> getListDatas(String baseType) {
        return scmCache.getListDetail(baseType);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean createElementData(CostElement vo) throws SumoException {
        CostElementData costElementData = new CostElementData();
        costElementData.setAscCode(vo.getAscCode());
        costElementData.setCapex(vo.getCapex());
        costElementData.setUom(vo.getUom());
        costElementData.setTaxRate(vo.getTaxRate());
        costElementData.setDescription(vo.getDescription());
        costElementData.setCostElementName(vo.getName());
        costElementData.setCostElementStatus(vo.getStatus());
        costElementData.setCategory(convert(vo.getCategory()));
        costElementData.setDepartment(convert(vo.getDepartment()));
        costElementData.setDivision(convert(vo.getDivision()));
        costElementData.setSubCategory(convert(vo.getSubCategory()));
        costElementData.setIsPriceUpdate(vo.getIsPriceUpdate());
        if (vo.getSubSubCategory() != null) {
            costElementData.setSubSubCategory(convert(vo.getSubSubCategory()));
        }
        dao.add(costElementData, true);
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<CostElement> getCostElementsData() {
        List<CostElementData> costElementData = dao.findAll(CostElementData.class);
        List<CostElement> costElement = new ArrayList<CostElement>();
        for (CostElementData p : costElementData) {
            CostElement ce = convert(p);
            costElement.add(ce);
        }
        return costElement;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean saveListDetailData(ListDetail listDetail) throws SumoException {
        ListDetailData detailData = convert(listDetail);
        dao.add(detailData, true);
        scmCache.refreshListDetails();
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean saveSubCategoryData(ListType listType) throws SumoException {
        ListTypeData typeData = SCMDataConverter.convertType(listType);
        dao.add(typeData, true);
        scmCache.refreshListDetails();
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean saveSubSubCategoryData(ListData listData) throws SumoException {
        ListDatas typeData = SCMDataConverter.convertData(listData);
        dao.add(typeData, true);
        scmCache.refreshListDetails();
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateListDetailData(ListDetail listDetail) {
        ListDetailData listDetailData = dao.find(ListDetailData.class, listDetail.getListDetailId());
        listDetailData.setName(listDetail.getName());
        listDetailData.setCode(listDetail.getCode());
        listDetailData.setAlias(listDetail.getAlias());
        listDetailData.setDescription(listDetail.getDescription());
        listDetailData.setStatus(listDetail.getStatus());
        listDetailData.setBaseType(listDetail.getBaseType());
        listDetailData.setEmail(listDetail.getEmail());
        dao.update(listDetailData, true);
        scmCache.refreshListDetails();
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateSubCategoryData(ListType listType) {
        ListTypeData listTypeData = dao.find(ListTypeData.class, listType.getListTypeId());
        listTypeData.setName(listType.getName());
        listTypeData.setCode(listType.getCode());
        listTypeData.setAlias(listType.getAlias());
        listTypeData.setDescription(listType.getDescription());
        listTypeData.setStatus(listType.getStatus());
        listTypeData.setBudgetCategory(listType.getBudgetCategory());
        dao.update(listTypeData, true);
        scmCache.refreshListDetails();
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateSubSubCategoryData(ListData listData) {
        ListDatas listDatas = dao.find(ListDatas.class, listData.getListDataId());
        listDatas.setName(listData.getName());
        listDatas.setCode(listData.getCode());
        listDatas.setAlias(listData.getAlias());
        listDatas.setDescription(listData.getDescription());
        listDatas.setStatus(listData.getStatus());
        dao.update(listDatas, true);
        scmCache.refreshListDetails();
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<VendorDetail> getVendorData(Integer costCenterId) {
        List<Integer> vendorIds = dao.findVendorIds(costCenterId);
        List<VendorDetail> vendorData = new ArrayList<VendorDetail>();
        List<VendorDetailData> vendorList = dao.findAllVendors(vendorIds);
        for (VendorDetailData vendorDetailData : vendorList) {
            VendorDetail vendorDetail = new VendorDetail();
            vendorDetail.setVendorId(vendorDetailData.getVendorId());
            vendorDetail.setEntityName(StringUtils.capitalize(vendorDetailData.getEntityName()));
            vendorDetail.setFirstName(StringUtils.capitalize(vendorDetailData.getFirstName()));
            vendorDetail.setLastName(StringUtils.capitalize(vendorDetailData.getLastName()));
            vendorDetail.setType(VendorType.valueOf(vendorDetailData.getType()));
            vendorDetail.setDisclaimerAccepted(AppConstants.getValue(vendorDetailData.getDisclaimerAccepted()));
            vendorDetail.setStatus(VendorStatus.fromValue(vendorDetailData.getStatus()));
            if (Objects.nonNull(vendorDetailData.getVendorBlocked())) {
                vendorDetail.setVendorBlocked(vendorDetailData.getVendorBlocked());
            }
            if (Objects.nonNull(vendorDetailData.getVendorBlockedReason())) {
                vendorDetail.setBlockedReason(vendorDetailData.getVendorBlockedReason());
            }
            if (Objects.nonNull(vendorDetailData.getUnblockedTillDate())) {
                vendorDetail.setUnblockedTillDate(vendorDetailData.getUnblockedTillDate());
            }
            vendorData.add(vendorDetail);
        }
        return vendorData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<CostElement> getCostElementsData(Integer costCenterId, Integer vendorId) {
        List<Integer> costElementIds = dao.findCostElementIds(costCenterId, vendorId);
        List<CostElementData> costElementDatas = dao.findCostElements(costElementIds);
        List<CostElement> costElementList = new ArrayList<CostElement>();
        for (CostElementData cost : costElementDatas) {
            CostElement costElement = new CostElement();
            costElement = convert(cost);
            costElementList.add(costElement);
        }
        return costElementList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public CostElementPriceUpdate getSelectedCostElement(Integer costCenterId, Integer vendorId, Integer costElementIds) {
        VendorCostCenterCostElementMapping costElementId = dao.findCostElement(costCenterId, vendorId, costElementIds);
        CostElementPriceUpdate costElementPrice = new CostElementPriceUpdate();
        costElementPrice.setCostElementPriceId(costElementId.getVendorCostCenterCostElementMappingId());
        costElementPrice.setCurrentPrice(costElementId.getPrice());
        return costElementPrice;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<ServiceOrder> getTagList(Integer costCenterId, String tagName) {
        List<ServiceOrderData> serviceOrderDataList = dao.getTagDataList(costCenterId, tagName);
        if (serviceOrderDataList != null) {
            List<ServiceOrder> soList = new ArrayList<>();
            for (ServiceOrderData data : serviceOrderDataList) {
                if (data.getTagName() != null) {
                    soList.add(convert(data, scmCache, masterDataCache, false, null));
                }
            }
            return soList;
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<String> getTagNamesList(Integer costCenterId) {
        List<String> tagNamesList = dao.getTagNamesList(costCenterId);
        return tagNamesList;
    }

    @Override
    public boolean getVendorDetailData(Integer locationId) {
        try {
            VendorDispatchLocationDetailData data = dao.find(VendorDispatchLocationDetailData.class, locationId);
            if (data.getGSTIN() != null) {
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            return true;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public String changeLevelStatus(Integer soId, String newStatusType, String currentStatus, BigDecimal amount,
                                     Integer userId) throws SumoException {
        ServiceOrderData serviceOrderData = dao.find(ServiceOrderData.class, soId);
        Boolean isBackDateOrder = Boolean.FALSE;
        List<ServiceOrderItemData> serviceOrderItemDataList = serviceOrderData.getServiceOrderItemDataList();
        Date lastDayPreviousMonth = SCMUtil.getLastDayOfPreviousMonth(new Date());
        for(ServiceOrderItemData serviceOrderItemData : serviceOrderItemDataList){
            Date lastServiceDate = serviceOrderItemData.getCostElementToDate();
            if(Objects.nonNull(lastServiceDate) && SCMUtil.isBefore(lastServiceDate,lastDayPreviousMonth)){
                isBackDateOrder =Boolean.TRUE;
                break;
            }
        }
        String newStatus = new String();
        if (newStatusType.equalsIgnoreCase(ServiceOrderStatus.APPROVED.toString())) {
            BigDecimal l2 = new BigDecimal(100000);
            BigDecimal l3 = new BigDecimal(200000);
            BigDecimal l4 = new BigDecimal(50000);
            BigDecimal l5 = new BigDecimal(100000);
            BigDecimal l6 = new BigDecimal(500000);
            if (currentStatus.equalsIgnoreCase(ServiceOrderStatus.PENDING_APPROVAL_L1.toString()) || currentStatus.equalsIgnoreCase(ServiceOrderStatus.CREATED.toString())) {
                if (amount.compareTo(l2) < 0) {
                    if(isBackDateOrder){
                        newStatus = ServiceOrderStatus.FIN_APPROVAL_L1.value();
                    }else {
                        newStatus = ServiceOrderStatus.APPROVED.toString();
                    }
                } else {
                        newStatus = ServiceOrderStatus.PENDING_APPROVAL_L2.toString();
                }
            } else if (currentStatus.equalsIgnoreCase(ServiceOrderStatus.PENDING_APPROVAL_L2.toString())) {
                if(amount.compareTo(l3)<0){
                    if(isBackDateOrder){
                        newStatus = ServiceOrderStatus.FIN_APPROVAL_L1.value();
                    }else {
                        newStatus = ServiceOrderStatus.APPROVED.toString();
                    }
                }else{
                    newStatus =  ServiceOrderStatus.PENDING_APPROVAL_L3.toString();
                }

                /*
                 * if (amount.compareTo(l3) < 0) { newStatus =
                 * ServiceOrderStatus.APPROVED.toString(); } else { newStatus =
                 * ServiceOrderStatus.PENDING_APPROVAL_L3.toString(); }
                 */
            }else if(currentStatus.equalsIgnoreCase(ServiceOrderStatus.PENDING_APPROVAL_L3.toString())){
                if(isBackDateOrder){
                    newStatus = ServiceOrderStatus.FIN_APPROVAL_L1.value();
                }else {
                    newStatus = ServiceOrderStatus.APPROVED.toString();
                }
            } else if(currentStatus.equalsIgnoreCase(ServiceOrderStatus.FIN_APPROVAL_L1.toString())){
                newStatus = ServiceOrderStatus.APPROVED.toString();
            }
            /*
             * else if
             * (currentStatus.equalsIgnoreCase(ServiceOrderStatus.PENDING_APPROVAL_L3.
             * toString())) { if (amount.compareTo(l4) < 0) { newStatus =
             * ServiceOrderStatus.APPROVED.toString(); } else { newStatus =
             * ServiceOrderStatus.PENDING_APPROVAL_L4.toString(); } } else if
             * (currentStatus.equalsIgnoreCase(ServiceOrderStatus.PENDING_APPROVAL_L4.
             * toString())) { if (amount.compareTo(l5) < 0) { newStatus =
             * ServiceOrderStatus.APPROVED.toString(); } else { newStatus =
             * ServiceOrderStatus.PENDING_APPROVAL_L5.toString(); } } else if
             * (currentStatus.equalsIgnoreCase(ServiceOrderStatus.PENDING_APPROVAL_L5.
             * toString())) { if (amount.compareTo(l6) < 0) { newStatus =
             * ServiceOrderStatus.APPROVED.toString(); } else { newStatus =
             * ServiceOrderStatus.PENDING_APPROVAL_L6.toString(); } } else if
             * (currentStatus.equalsIgnoreCase(ServiceOrderStatus.PENDING_APPROVAL_L6.
             * toString())) { newStatus = ServiceOrderStatus.APPROVED.toString(); }
             */
        } else {
            String type = currentStatus.substring(currentStatus.length() - 2);
            if (currentStatus.equalsIgnoreCase(ServiceOrderStatus.PENDING_APPROVAL_L1.toString()) || currentStatus.equalsIgnoreCase(ServiceOrderStatus.CREATED.toString())) {
                newStatus = ServiceOrderStatus.REJECTED_L1.toString();
            } else if (currentStatus.equalsIgnoreCase(ServiceOrderStatus.PENDING_APPROVAL_L2.toString())) {
                newStatus = ServiceOrderStatus.REJECTED_L2.toString();
            } else if (currentStatus.equalsIgnoreCase(ServiceOrderStatus.PENDING_APPROVAL_L3.toString())) {
                newStatus = ServiceOrderStatus.REJECTED_L3.toString();
            } else if(currentStatus.equalsIgnoreCase(ServiceOrderStatus.FIN_APPROVAL_L1.toString())){
                newStatus = ServiceOrderStatus.FIN_REJECTED_L1.toString();
            }
            /*
             * else if (type.equalsIgnoreCase("L3")) { newStatus =
             * ServiceOrderStatus.REJECTED_L3.toString(); } else if
             * (type.equalsIgnoreCase("L4")) { newStatus =
             * ServiceOrderStatus.REJECTED_L4.toString(); } else if
             * (type.equalsIgnoreCase("L5")) { newStatus =
             * ServiceOrderStatus.REJECTED_L5.toString(); } else { newStatus =
             * ServiceOrderStatus.REJECTED_L6.toString(); }
             */
            for (ServiceOrderItemData itemData : serviceOrderData.getServiceOrderItemDataList()) {
                if (serviceOrderData.getType().equalsIgnoreCase(BudgetAuditActions.CAPEX.value())) {
                    updateBudgetAuditDetailForReject(itemData, serviceOrderData, ServiceOrderStatus.REJECTED.value(), userId);
                }
            }
        }
        try {
            if (newStatus.equalsIgnoreCase(CapexStatus.APPROVED.value())) {
                 approveServiceOrder(soId, userId, serviceOrderData);
            } else {
                if (updateStatusEvent(soId, newStatus, userId)) {
                    serviceOrderData.setLastUpdatedBy(userId);
                    serviceOrderData.setLastUpdateTime(SCMUtil.getCurrentTimestamp());
                    serviceOrderData.setStatus(newStatus);
                    dao.update(serviceOrderData, true);
                }
            }
        } catch (Exception e) {
            LOG.error("Error in Changing Status.", e);
            return null;
        }
        return newStatus;
    }

    private void updateBudgetAuditDetailForReject(ServiceOrderItemData itemData, ServiceOrderData serviceOrderData,
                                                  String status, Integer userId) throws SumoException {
        List<BusinessCostCenter> bccs = getBusinessCostCentersData();
        CostElementData costElement = dao.find(CostElementData.class, itemData.getCostElementId());
        BusinessCostCenter bcc = bccs.stream().filter(b -> b.getId().equals(itemData.getBusinessCostCenterId()))
                .findFirst().orElse(null);
        CapexAuditDetailData capexAuditDetail = dao.findCapexAuditData(Integer.parseInt(bcc.getCode()));
        CapexBudgetDetailData capexBudgetData = dao.findBudgetUnit(Integer.parseInt(bcc.getCode()),
                costElement.getDepartment().getName());
        List<String> actions = Arrays.asList(BudgetAuditActions.REMAINING_AMOUNT.value(),
                BudgetAuditActions.RUNNING_AMOUNT.value());
        for (String action : actions) {
            BudgetAuditDetailData budgetAuditDetail = new BudgetAuditDetailData();
            budgetAuditDetail.setCapexAuditId(capexAuditDetail.getId());
            budgetAuditDetail.setCapexBudgetDetailId(capexBudgetData.getId());
            budgetAuditDetail.setActionBy(userId);
            if (status.equalsIgnoreCase(ServiceOrderStatus.REJECTED.value())) {
                budgetAuditDetail.setAction(BudgetAuditActions.REJECTED.value());
            } else {
                budgetAuditDetail.setAction(BudgetAuditActions.CANCELLED.value());
            }
            budgetAuditDetail.setKeyType(BudgetAuditActions.SO_ID.value());
            budgetAuditDetail.setKeyValue(serviceOrderData.getId());
            budgetAuditDetail.setActionTime(SCMUtil.getCurrentTimestamp());
            if (action.equalsIgnoreCase(BudgetAuditActions.REMAINING_AMOUNT.value())) {
                budgetAuditDetail.setAmountType(BudgetAuditActions.REMAINING_AMOUNT.value());
                budgetAuditDetail.setPreviousValue(capexBudgetData.getRemainingAmount());
                budgetAuditDetail.setFinalValue(
                        capexBudgetData.getRemainingAmount().add(itemData.getTotalCost().add(itemData.getTotalTax())));
                budgetAuditDetail.setActionType(BudgetAuditActions.ADDITION.value());
            } else {
                budgetAuditDetail.setAmountType(BudgetAuditActions.RUNNING_AMOUNT.value());
                budgetAuditDetail.setPreviousValue(capexBudgetData.getRunningAmount());
                budgetAuditDetail.setFinalValue(capexBudgetData.getRunningAmount()
                        .subtract(itemData.getTotalCost().add(itemData.getTotalTax())));
                budgetAuditDetail.setActionType(BudgetAuditActions.REDUCTION.value());
            }
            dao.add(budgetAuditDetail, true);
        }
        capexBudgetData.setRemainingAmount(
                capexBudgetData.getRemainingAmount().add(itemData.getTotalCost().add(itemData.getTotalTax())));
        capexBudgetData.setRunningAmount(
                capexBudgetData.getRunningAmount().subtract(itemData.getTotalCost().add(itemData.getTotalTax())));
        dao.update(capexBudgetData, true);
    }

    private boolean updateStatusEvent(Integer soId, String newStatus, Integer userId) {
        ServiceOrderStatusEventData eventData;
        try {
            eventData = dao.findServiceOrderStatus(soId);
        } catch (Exception e) {
            return true;
        }
        eventData.setFromStatus(eventData.getToStatus());
        eventData.setToStatus(newStatus);
        eventData.setServiceOrderId(soId);
        eventData.setUpdatedBy(userId);
        eventData.setUpdateTime(SCMUtil.getCurrentTimestamp());
        eventData.setTransitionStatus(SCMTransitionStatus.SUCCESS.name());
        dao.update(eventData, true);
        return true;
    }

    @Override
    public boolean getUnitCheckBudget(Integer unitId) {
        return dao.findBudgetDetail(unitId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<ServiceOrderItem> getDepartmentDataForUnit(List<ServiceOrderItem> requestVO) {
        List<BusinessCostCenter> bccs = getBusinessCostCentersData();
        for (ServiceOrderItem item : requestVO) {
            CostElementData costElement = dao.find(CostElementData.class, item.getCostElementId());
            BusinessCostCenter bcc = bccs.stream()
                    .filter(b -> b.getId().equals(item.getBusinessCostCenterId())).findFirst().orElse(null);
            CapexBudgetDetailData capexBudgetDetail = dao.findBudgetUnit(Integer.parseInt(Objects.requireNonNull(bcc).getCode()), costElement.getDepartment().getName());
            //BigDecimal remainingAmount = dao.findUnitAmount(item.getUnitId(), item.getDepartmentName());
            item.setDepartmentName(costElement.getDepartment().getName());
            item.setOriginalAmount(capexBudgetDetail.getOriginalAmount());
            item.setBudgetAmount(capexBudgetDetail.getBudgetAmount());
            item.setRemainingAmount(capexBudgetDetail.getRemainingAmount());
            item.setReceivingAmount(capexBudgetDetail.getReceivingAmount());
            item.setRunningAmount(capexBudgetDetail.getRunningAmount());
            item.setPaidAmount(capexBudgetDetail.getPaidAmount());
            List<ServiceOrderShort> soList = capexManagementService.getSOsByDepartment(Integer.valueOf(item.getUnitId()), capexBudgetDetail.getCapexRequestId(), item.getDepartmentId(),
                    bcc.getId());
            item.setServiceOrderShortList(soList);
        }
        return requestVO;
    }



    @Override
    public List<String> getBudgetCategory() {
        return Stream.of(ExpenseField.ServiceRecordCategory.values())
                .map(Enum::name)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void calculateServiceAggregate(BudgetDetail budgetDetail, UnitDetail unitDetail, Date businessDate,
                                          StockTakeType type) {

        //Calculate all service that need to be included from start of the month till end of business date
        //Aggregate them in the query on the ServiceRecordCategory
        //calculate ServiceAggregate
        //createServiceAggregate(Map<ServiceRecordCategory, BigDecimal> map)
        Map<String, BigDecimal> hashMap = dao.getServiceReceivedData(businessDate, unitDetail.getUnitId()); // TODO Abhishek allot value

        if (type.equals(StockTakeType.MONTHLY)) {
            try {
                List<Integer> serviceOrderIds = dao.getServiceOrderIdsForFinalizedUpdation(businessDate, unitDetail.getUnitId());
                if (serviceOrderIds != null && serviceOrderIds.size() > 0) {
                    dao.setAccountableForPnlServiceOrder(serviceOrderIds, true);
                }
            } catch (Exception e) {
                LOG.error("error while updating accounted for in pnl in service order {}", e.getMessage());
            }

        }
        ServiceAggregate serviceAggregate = createServiceAggregate(hashMap);
        budgetDetail.setServices(serviceAggregate);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public DocumentDetail uploadSoDocument(FileType type, MimeType mimeType, DocUploadType docType, Integer userId, MultipartFile file) {

        String fileName = "SERVICE_ORDER_" + SCMUtil.getCurrentTimeISTStringWithNoColons() + "."
                + mimeType.name().toLowerCase();
        LOG.info("File name is {}", fileName);
        String baseDir = "SO_" + File.separator + SCMUtil.getCurrentYear() + File.separator
                + SCMUtil.getCurrentMonthName() + File.separator + SCMUtil.getCurrentDayofMonth();
        File destinationFile = null;
        MultipartFile compressedFile = null;

        if(mimeType.equals(MimeType.PDF)) {
            LOG.info("#########Uploaded FIle is PDF , Trying To Compress ###### ");
            //Compression of PDF
            try{
                compressedFile = paymentRequestManagementService.compressPdf(destinationFile,file,compressedFile);
                LOG.info("###### size before compression : {}", file.getSize());
                LOG.info("###### Size After Compression : {}", compressedFile.getSize());
            }catch (SumoException e){
                LOG.info("######Error While Compressing File , Uploading Without Compression :::::",e);
                compressedFile = null;
            } catch (DocumentException e) {
                e.printStackTrace();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }else if(mimeType.equals(MimeType.PNG)){
            LOG.info("#######Uploaded File Is Of PNG Format. Uploading Without Compression .");

        }else{
            LOG.info("#######Uploaded File is Of {} Format, Trying To Compress",mimeType.extension());
            try{
                byte[] imageByte = paymentRequestManagementService.compressImage(file, mimeType.extension());
                compressedFile = new MockMultipartFile(file.getName(),
                        file.getOriginalFilename(), file.getContentType(), imageByte);
                LOG.info("###### size before compression : {}", file.getSize());
                LOG.info("###### Size After Compression : {}", compressedFile.getSize());
            }catch (Exception e){
                LOG.info("#######Error While Compressing Image , Uploading Without Compression");
                compressedFile = null;
            }
        }
        try {
            FileDetail s3File = fileArchiveService.saveFileToS3(props.getS3Bucket(), baseDir, fileName, Objects.isNull(compressedFile) ? file : compressedFile);
            DocumentDetail documentDetail=new DocumentDetail();
            documentDetail.setMimeType(mimeType);
            documentDetail.setUploadType(docType);
            documentDetail.setFileType(type);
            documentDetail.setDocumentLink(fileName);
            documentDetail.setS3Key(s3File.getKey());
            documentDetail.setFileUrl(s3File.getUrl());
            documentDetail.setS3Bucket(s3File.getBucket());
            documentDetail.setUpdatedBy(SCMUtil.generateIdCodeName(userId, "", masterDataCache.getEmployee(userId)));
            documentDetail.setUpdateTime(SCMUtil.getCurrentTimestamp());
            DocumentDetailData data = dao.add(convert(documentDetail), true);
            if(Objects.nonNull(destinationFile)){
                destinationFile.delete();
            }
            if (data.getDocumentId() != null) {
                return convert(data);
            }
        } catch (Exception e) {
            LOG.error("Encountered error while uploading document", e);
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<BulkSoDetail> readUploadedSOFile(MultipartFile file) throws IOException, SumoException {
        Set<Integer> exceptionRows = new HashSet<>();
        List<BulkSoDetail> result = new ArrayList<>();
        InputStream excelFile = file.getInputStream();
        XSSFWorkbook workbook = new XSSFWorkbook(excelFile);
        XSSFSheet sheet1 = workbook.getSheetAt(0);
        int lastRowNumber = sheet1.getLastRowNum();
        LOG.info("last row of the uploaded SO sheet is {}", lastRowNumber);
        for (int i = 1; i < lastRowNumber; i++) {
            BulkSoDetail entry = new BulkSoDetail();
            XSSFRow currentRow = sheet1.getRow(i);
            try {
                if (currentRow.getCell(0).getStringCellValue() != null) {
                    entry.setUnitId(currentRow.getCell(0).getStringCellValue());
                }
            } catch (Exception e) {
                exceptionRows.add(i + 1);
            }
            try {
                if (currentRow.getCell(1).getStringCellValue() != null) {
                    entry.setUnitName(currentRow.getCell(1).getStringCellValue());
                }
            } catch (Exception e) {
                exceptionRows.add(i + 1);
            }
            try {
                Double allocatedCost = currentRow.getCell(2).getNumericCellValue();
                if (allocatedCost != null) {
                    entry.setAllocatedCost(allocatedCost);
                } else {
                    exceptionRows.add(i + 1);
                }
            } catch (Exception e) {
                exceptionRows.add(i + 1);
            }
            result.add(entry);
        }
        if (exceptionRows.size() > 0) {
            String message = "Error Occurred While Reading Uploaded Sheet.<b>Please Fill all the values Correctly at Rows :</b>\n "
                    + Arrays.toString(exceptionRows.toArray());
            throw new SumoException("Verify Uploaded Sheet..!", message);
        }
        List<BulkSoDetail> finalResult = result.stream().filter(entry -> entry.getAllocatedCost() > 0).collect(Collectors.toList());
        return finalResult;
    }


    /**
     * @param map
     * @return
     */
    private ServiceAggregate createServiceAggregate(Map<String, BigDecimal> map) {

        ServiceAggregate a = new ServiceAggregate();
        for (String key : map.keySet()) {
            BigDecimal value = map.get(key);
            ServiceRecordCategory cat = null;
            try {
                cat = ServiceRecordCategory.valueOf(key);
            } catch (Exception e) {
                cat = ServiceRecordCategory.NONE;
            }
            switch (cat) {
                case SECURITY_GUARD_CHARGES:
                    a.setSecurityGuardCharges(value);
                    break;
                case FUEL_CHARGES:
                    a.setFuelCharges(value);
                    break;
                case LOGISTIC_CHARGES:
                    a.setLogisticCharges(value);
                    break;
                case COMMUNICATION_INTERNET:
                    a.setCommunicationInternet(value);
                    break;
                case COMMUNICATION_TELEPHONE:
                    a.setCommunicationTelephone(value);
                    break;
                case COMMUNICATION_ILL:
                    a.setCommunicationILL(value);
                    break;
                case PAYROLL_PROCESSING_FEES:
                    a.setPayrollProcessingFee(value);
                    break;
                case NEWSPAPER_CHARGES:
                    a.setNewsPaper(value);
                    break;
                case STAFF_WELFARE_EXPENSES:
                    a.setStaffWelfareExpenses(value);
                    break;
                case COURIER_CHARGES:
                    a.setCourierCharges(value);
                    break;
                case PRINTING_AND_STATIONARY:
                    a.setPrintingAndStationary(value);
                    break;
                case BUSINESS_PROMOTION:
                    a.setBusinessPromotion(value);
                    break;
                case LEGAL_CHARGES:
                    a.setLegalCharges(value);
                    break;
                case PROFESSIONAL_CHARGES:
                    a.setProfessionalCharges(value);
                    break;
                case CLEANING_CHARGES:
                    a.setCleaningCharges(value);
                    break;
                case PEST_CONTROL_CHARGES:
                    a.setPestControlCharges(value);
                    break;
                case TECHNOLOGY_TRAINING:
                    a.setTechologyTraining(value);
                    break;
                case CORPORATE_MARKETING_DIGITAL:
                    a.setCorporateMarketingDigital(value);
                    break;
                case CORPORATE_MARKETING_AD_OFFLINE:
                    a.setCorporateMarketingAdvOffline(value);
                    break;
                case CORPORATE_MARKETING_AD_ONLINE:
                    a.setCorporateMarketingAdvOnline(value);
                    break;
                case CORPORATE_MARKETING_CHANNEL_PARTNER:
                    a.setCorporateMarketingChannelPartner(value);
                    break;
                case CORPORATE_MARKETING_OUTDOOR:
                    a.setCorporateMarketingOutdoor(value);
                    break;
                case CORPORATE_MARKETING_AGENCY_FEES:
                    a.setCorporateMarketingAgencyFees(value);
                    break;
                case DELIVERY_CHARGES_VARIABLE:
                    a.setDeliveryChargesVariable(value);
                    break;
//                case CONVEYANCE_MARKETING:
//                    a.setConveyanceMarketing(value);
//                    break;
//                case CONVEYANCE_OPERATION:
//                    a.setConveyanceOperations(value);
//                    break;
//                case CONVEYANCE_OTHERS:
//                    a.setConveyanceOthers(value);
//                    break;
                case AUDIT_FEE:
                    a.setAuditFee(value);
                    break;
                case AUDIT_FEE_OUT_OF_POCKET:
                    a.setAuditFeeOutOfPocket(value);
                    break;
                case BROKERAGE:
                    a.setBrokerage(value);
                    break;
                case CHARITY_AND_DONATIONS:
                    a.setCharityAndDonations(value);
                    break;
                case DOMESTIC_TICKETS_AND_HOTELS:
                    a.setDomesticTicketsAndHotels(value);
                    break;
                case INTERNATIONAL_TICKETS_AND_HOTELS:
                    a.setInternationalTicketsAndHotels(value);
                    break;
                case HOUSEKEEPING_CHARGES:
                    a.setHouseKeepingCharges(value);
                    break;
                case LATE_FEE_CHARGES:
                    a.setLateFeeCharges(value);
                    break;
//                case MARKETING_DATA_ANALYSIS:
//                    a.setMarketingDataAnalysis(value);
//                    break;
                case MISCELLANEOUS_EXPENSES:
                    a.setMiscellaneousExpenses(value);
                    break;
                case PENALTY:
                    a.setPenalty(value);
                    break;
                case PHOTO_COPY_EXPENSES:
                    a.setPhotoCopyExpenses(value);
                    break;
                case QCR_EXPENSE:
                    a.setQcrExpense(value);
                    break;
                case RECRUITMENT_CONSULTANTS:
                    a.setRecuritmentConsultants(value);
                    break;
                case ROC_FEES:
                    a.setRocFees(value);
                    break;
                case DEBIT_CREDIT_WRITTEN_OFF:
                    a.setDebitCreditWrittenOff(value);
                    break;
                case DIFFENECE_IN_EXCHANGE:
                    a.setDifferenceInExchange(value);
                    break;
                case RND_ENGINEERING_EXPENSE:
                    a.setRnDEngineeringExpenses(value);
                    break;
                case CAPITAL_IMPROVEMENT_EXPENSES:
                    a.setCapitalImprovementExpenses(value);
                    break;
                case LEASE_HOLD_IMPROVEMENTS:
                    a.setLeaseHoldImprovements(value);
                    break;
                case MARKETING_LAUNCH:
                    a.setMarketingLaunch(value);
                    break;
                case CORPORATE_MARKETING_PHOTO:
                    a.setCorporateMarketingPhotography(value);
                    break;
                case ODC_RENTAL:
                    a.setOdcRental(value);
                    break;
//                case COGS_OTHERS:
//                    a.setCogsOthers(value);
//                    break;
                case COMMISSION_CHANGE:
                    a.setCommissionChange(value);
                    break;
                case VEHICLE_REGULAR_MAINTENANCE_HQ:
                    a.setVehicleRegularMaintenanceHq(value);
                    break;
                case BUILDING_MAINTENANCE_HQ:
                    a.setBuildingMaintenanceHq(value);
                    break;
                case COMPUTER_IT_MAINTENANCE_HQ:
                    a.setComputerItMaintenanceHq(value);
                    break;
                case EQUIPMENT_MAINTENANCE_HQ:
                    a.setEquipmentMaintenanceHq(value);
                    break;
                case MARKETING_NPI_HQ:
                    a.setMarketingNpiHq(value);
                    break;
                case LICENSE_EXPENSES:
                    a.setLicenseExpenses(value);
                    break;
                case CORPORATE_MARKETING_ATL_RADIO:
                    a.setCorporateMarketingAtlRadio(value);
                    break;
                case CORPORATE_MARKETING_ATL_TV:
                    a.setCorporateMarketingAtlTv(value);
                    break;
                case CORPORATE_MARKETING_ATL_PRINT_AD:
                    a.setCorporateMarketingAtlPrintAd(value);
                    break;
                case CORPORATE_MARKETING_ATL_CINEMA:
                    a.setCorporateMarketingAtlCinema(value);
                    break;
                case CORPORATE_MARKETING_ATL_DIGITAL:
                    a.setCorporateMarketingAtlDigital(value);
                    break;
                case LOGISTIC_INTRASTATE_COLD_VEHICLE:
                    a.setLogisticInterstateColdVehicle(value);
                    break;
                case LOGISTIC_INTRASTATE_NON_COLD_VEHICLE:
                    a.setLogisticInterstateNonColdVehicle(value);
                    break;
                case LOGISTIC_INTERSTATE_AIR:
                    a.setLogisticInterstateAir(value);
                    break;
                case LOGISTIC_INTERSTATE_ROAD:
                    a.setLogisticInterstateRoad(value);
                    break;
                case LOGISTIC_INTERSTATE_TRAIN:
                    a.setLogisticInterstateTrain(value);
                    break;
                case AIR_CONDITIONER_AMC:
                    a.setAirConditionerAmc(value);
                    break;
                case CORPORATE_MARKETING_SMS_EMAIL:
                    a.setCorporateMarketingSms(value);
                    break;

                case SECURITY_DEPOSIT_PROPERTY:
                    a.setSecurityDepositProperty(value);
                    break;
                case SECURITY_DEPOSIT_MVAT:
                    a.setSecurityDepositMVAT(value);
                    break;
                case SECURITY_DEPOSIT_ELECTRICITY:
                    a.setSecurityDepositElectricity(value);
                    break;
                case MARKETING_DISCOUNT_ECOM:
                    a.setMarketingDiscountEcom(value);
                    break;
                case SHIPPING_CHARGES:
                    a.setShippingCharges(value);
                    break;
                case OTHER_TRANSACTION_CHARGES:
                    a.setOtherTransactionCharges(value);
                    break;
                case DISCOUNT_DEALER_MARGIN:
                    a.setDiscountDealerMargin(value);
                    break;
                case PERFORMANCE_MARKETING_SERVICE:
                    a.setPerformanceMarketingService(value);
                    break;
                case INSURANCE_MARINE:
                    a.setInsuranceMarine(value);
                    break;
                case SHARE_STAMPING_CHARGES:
                    a.setShareStampingCharges(value);
                    break;
                case OTHER_CHARGES_ECOM:
                    a.setOtherChargesEcom(value);
                    break;
                case COMISSION_CHANNEL_PARTNER_FIXED:
                    a.setComissionChannelPartnerFixed(value);
                    break;
                case COGS_TRADING_GOODS:
                    a.setCogsTradingGoods(value);
                    break;
                case ROYALTY_FEES:
                    a.setRoyaltyFees(value);
                    break;
                case FREIGHT_CHARGES:
                    a.setFreightCharges(value);
                    break;
                case EMPLOYEE_FACILITATION_CHARGES:
                    a.setEmployeeFacilitationCharges(value);
                    break;
                case PRONTO_AMC:
                    a.setProntoAMC(value);
                    break;
                case OTHERS_MAINTENANCE:
                    a.setOthersMaintenance(value);
                    break;
                case TECHNOLOGY_PLATFORM_CHARGES:
                    a.setTechnologyPlatformCharges(value);
                    break;
                case INTEREST_ON_TERM_LOAN:
                    a.setInterestOnTermLoan(value);
                    break;
                case TECHNOLOGY_OTHERS:
                    a.setTechnologyOthers(value);
                    break;
                case SYSTEM_RENTAL:
                    a.setSystemRental(value);
                    break;
                case RO_RENTAL:
                    a.setRoRental(value);
                    break;
                case INSURANCE_ACCIDENTAL:
                    a.setInsuranceAccidental(value);
                    break;
                case DG_RENTAL:
                    a.setDgRental(value);
                    break;
                case OTHERS_AMC:
                    a.setOthersAMC(value);
                    break;
                case MUSIC_RENTALS:
                    a.setMusicRentals(value);
                    break;
                case INSURANCE_ASSETS:
                    a.setInsuranceAssets(value);
                    break;
                case INSURANCE_CGL:
                    a.setInsuranceCGL(value);
                    break;
                case INSURANCE_MEDICAL:
                    a.setInsuranceMedical(value);
                    break;
                case PETTY_CASH_RENTALS:
                    a.setPettyCashRentals(value);
                    break;
                case EDC_RENTAL:
                    a.setEdcRental(value);
                    break;
//                case FREIGHT_CHARGES:
//                    a.setFreightCharges(value);
//                    break;
//                case FREIGHT_CHARGES:
//                    a.setFreightCharges(value);
//                    break;
//                case FREIGHT_CHARGES:
//                    a.setFreightCharges(value);
//                    break;

                case NONE:
                default:
                    a.setOtherServiceCharges(AppUtils.add(a.getOtherServiceCharges(), value));
                    break;
            }
        }
        return a;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public DocumentDetail uploadRequiredDocuments(FileType type, MimeType mimeType,Integer userId,DocUploadType docType, MultipartFile file,String docName) {

           String fileName = docName + "-"+ "MANDATORY_UPLOADED_DOCUMENTS"+ SCMUtil.getCurrentTimeISTStringWithNoColons()  + "."
                   + mimeType.name().toLowerCase();
           LOG.info("File name is {}", fileName);
           String baseDir = "PR_" + File.separator + SCMUtil.getCurrentYear() + File.separator
                   + SCMUtil.getCurrentMonthName() + File.separator + SCMUtil.getCurrentDayofMonth();
           File destinationFile = null;
           MultipartFile compressedFile = null;

           if(mimeType.equals(MimeType.PDF)) {
               LOG.info("#########Uploaded FIle is PDF , Trying To Compress ###### ");
               //Compression of PDF
               try{
                   compressedFile = paymentRequestManagementService.compressPdf(destinationFile,file,compressedFile);
                   LOG.info("###### size before compression : {}", file.getSize());
                   LOG.info("###### Size After Compression : {}", compressedFile.getSize());
               }catch (SumoException e){
                   LOG.info("######Error While Compressing File , Uploading Without Compression :::::",e);
                   compressedFile = null;
               } catch (DocumentException e) {
                   e.printStackTrace();
               } catch (IOException e) {
                   e.printStackTrace();
               }
           }else if(mimeType.equals(MimeType.PNG)){
               LOG.info("#######Uploaded File Is Of PNG Format. Uploading Without Compression .");

           }else{
               LOG.info("#######Uploaded File is Of {} Format, Trying To Compress",mimeType.extension());
               try{
                   byte[] imageByte = paymentRequestManagementService.compressImage(file, mimeType.extension());
                   compressedFile = new MockMultipartFile(file.getName(),
                           file.getOriginalFilename(), file.getContentType(), imageByte);
                   LOG.info("###### size before compression : {}", file.getSize());
                   LOG.info("###### Size After Compression : {}", compressedFile.getSize());
               }catch (Exception e){
                   LOG.info("#######Error While Compressing Image , Uploading Without Compression");
                   compressedFile = null;
               }
           }
           try {
               FileDetail s3File = fileArchiveService.saveFileToS3(props.getS3Bucket(), baseDir, fileName, Objects.isNull(compressedFile) ? file : compressedFile);
               DocumentDetail documentDetail=new DocumentDetail();
               documentDetail.setMimeType(mimeType);
               documentDetail.setUploadType(docType);
               documentDetail.setFileType(type);
               documentDetail.setDocumentLink(fileName);
               documentDetail.setS3Key(s3File.getKey());
               documentDetail.setFileUrl(s3File.getUrl());
               documentDetail.setS3Bucket(s3File.getBucket());
               documentDetail.setUpdatedBy(SCMUtil.generateIdCodeName(userId, "", masterDataCache.getEmployee(userId)));
               documentDetail.setUpdateTime(SCMUtil.getCurrentTimestamp());
               DocumentDetailData data = dao.add(convert(documentDetail), true);
               if(Objects.nonNull(destinationFile)){
                   destinationFile.delete() ;
               }
               if (data.getDocumentId() != null) {
                   return convert(data);
               }
           } catch (Exception e) {
               LOG.error("Encountered error while uploading document", e);
           }
           return null;


    }

    @Override
    public List<String> showRequiredDocuments(List<Integer> costElementIds) {
        return dao.showRequiredDocuments(costElementIds);
    }

    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Boolean sendCapexCloseNotification() {
        Set<Integer> unitIds = masterDataCache.getAllUnits().stream().filter(u -> u.getStatus() == UnitStatus.ACTIVE && (u.getCategory() == UnitCategory.CAFE || u.getCategory() == UnitCategory.KITCHEN || u.getCategory() == UnitCategory.WAREHOUSE))
                .map(UnitBasicDetail::getId).collect(Collectors.toSet());
        List<UnitCapexDataSummary> unitCapexDataSummaryList = getPendingCloseCapexUnitData(unitIds);

        if(unitCapexDataSummaryList.isEmpty()){return true;}
        ArrayList<String> mails = new ArrayList<>();
        mails.add("<EMAIL>");
        mails.add("<EMAIL>");
        mails.add("<EMAIL>");

        for(UnitCapexDataSummary u : unitCapexDataSummaryList){
             long diffInMs = Math.abs(AppUtils.getCurrentDate().getTime() - u.getLastUpdatedTime().getTime());
             long diffInDays =   TimeUnit.DAYS.convert(diffInMs,TimeUnit.MILLISECONDS);
             mails.add(masterDataCache.getEmployeeBasicDetail(u.getGeneratedBy()).getEmailId());
             mails.add(masterDataCache.getEmployeeBasicDetail(u.getLastUpdatedBy()).getEmailId());
             u.setDiffInDays(diffInDays);
         }
        Collections.sort(unitCapexDataSummaryList, new Comparator<UnitCapexDataSummary>() {
            @Override
            public int compare(UnitCapexDataSummary c1, UnitCapexDataSummary c2) {
                return Double.compare(c2.getDiffInDays(), c1.getDiffInDays());
            }
        });
        String dateTime = new SimpleDateFormat("yyyy-MM-dd-HH-mm-ss").format(new Date());
        String fileName = "Unit_Data_Summary-" + dateTime + ".xlsx";
        try {
            XSSFWorkbook  workbook = new XSSFWorkbook();
            //Creating a Spread Sheet
            XSSFSheet spreadsheet = workbook.createSheet("Summary");
            XSSFRow row = spreadsheet.createRow(0);
            XSSFCell cell;
            cell = row.createCell(0);
            cell.setCellValue("CAPEX_ID");
            cell = row.createCell(1);
            cell.setCellValue("UNIT_ID");
            cell = row.createCell(2);
            cell.setCellValue("BUDGET_TYPE");
            cell = row.createCell(3);
            cell.setCellValue("TOTAL_BUDGET_AMOUNT");
            cell = row.createCell(4);
            cell.setCellValue("TOTAL_RUNNING_AMOUNT");
            cell = row.createCell(5);
            cell.setCellValue("TOTAL_RECEIVED_AMOUNT");
            cell = row.createCell(6);
            cell.setCellValue("TOTAL_REMAINING_AMOUNT");
            cell = row.createCell(7);
            cell.setCellValue("GENERATED_TIME");
            cell = row.createCell(8);
            cell.setCellValue("LAST_UPDATED_TIME");
            cell = row.createCell(9);
            cell.setCellValue("GENERATED_BY");
            cell = row.createCell(10);
            cell.setCellValue("LAST_UPDATED_BY");
            cell = row.createCell(11);
            cell.setCellValue("STATUS");
            cell = row.createCell(12);
            cell.setCellValue("DAYS_FROM_APPROVAL");

            int i = 1;
            for (UnitCapexDataSummary u : unitCapexDataSummaryList) {
                row = spreadsheet.createRow(i);
                cell = row.createCell(0);
                cell.setCellValue(u.getCapexId());
                cell = row.createCell(1);
                cell.setCellValue(u.getUnitId());
                cell = row.createCell(2);
                cell.setCellValue(u.getBudgetType());
                cell = row.createCell(3);
                cell.setCellValue(u.getTotalBudgetAmount().doubleValue());
                cell = row.createCell(4);
                cell.setCellValue(u.getTotalRunningAmount().doubleValue());
                cell = row.createCell(5);
                cell.setCellValue(u.getTotalReceivedAmount().doubleValue());
                cell = row.createCell(6);
                cell.setCellValue(u.getTotalRemainingAmount().doubleValue());
                cell = row.createCell(7);
                cell.setCellValue(DateFormatUtils.format(u.getGeneratedTime(), "yyyy-MM-dd HH:mm:SS"));
                cell = row.createCell(8);
                cell.setCellValue(DateFormatUtils.format(u.getLastUpdatedTime(), "yyyy-MM-dd HH:mm:SS"));
                cell = row.createCell(9);
                cell.setCellValue(u.getGeneratedBy());
                cell = row.createCell(10);
                cell.setCellValue(u.getLastUpdatedBy());
                cell = row.createCell(11);
                cell.setCellValue(u.getStatus());
                cell = row.createCell(12);
                cell.setCellValue(u.getDiffInDays());

                i++;
            }

            File fileOut = new File(props.getBasePath()+"/"+fileName);
            FileOutputStream out = new FileOutputStream(fileOut);
            workbook.write(out);
            out.close();

            byte[] barray = null;

            File file = new File(props.getBasePath() + "/" + fileName);
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            bos.write(FileUtils.readFileToByteArray(file));
            barray = bos.toByteArray();

            List<AttachmentData> attachments = new ArrayList<>();
            AttachmentData summary = null;
            String[] fileNameSplit = fileName.split("\\.");
            summary = new AttachmentData(barray, fileNameSplit[0], AppConstants.EXCEL_MIME_TYPE);
            attachments.add(summary);
            ArrayList<String> des = (ArrayList<String>) mails.stream().filter(e-> e != null && e.length()>0).collect(Collectors.toList());
            UnitCapexSummaryTemplate unitCapexSummaryTemplate = new UnitCapexSummaryTemplate(unitCapexDataSummaryList, props.getBasePath());
            UnitCapexSummaryEmailNotification unitCapexSummaryEmailNotification = new UnitCapexSummaryEmailNotification(unitCapexSummaryTemplate, props.getEnvType(), des);
            unitCapexSummaryEmailNotification.sendRawMail(attachments);
            boolean isDeleted =  file.delete();
            LOG.info("############### File Deleted : {} ###############",isDeleted);
            return true;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
    }
