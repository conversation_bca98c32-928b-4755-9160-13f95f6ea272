package com.stpl.tech.scm.core.service;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.scm.core.exception.DayCloseInitiatedException;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.exception.TransferOrderCreationException;
import com.stpl.tech.scm.data.model.*;
import com.stpl.tech.scm.domain.model.*;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;


import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.MalformedURLException;
import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by Rahul Singh on 22-06-2016.
 */
public interface TransferOrderManagementService {

    public Integer createTransferOrder(TransferOrder transferOrder, boolean skipGR, String invoiceId) throws TransferOrderCreationException, InventoryUpdateException, SumoException, DataNotFoundException;

    public List<TransferOrder> findTransferOrders(Integer generationUnitId, Integer generatedForUnitId, Date startDate, Date endDate, SCMOrderStatus status, Integer transferOrderId);

    public List<TransferOrder> incrementalTransferOrders(Integer generationUnitId, Integer generatedForUnitId, Date startDate, Date endDate, SCMOrderStatus status, Integer transferOrderId, String ids);

    public TransferOrder getTransferOrderDetail(int transferOrderId);

    public List<TransferOrder> getTorqusTransferOrderList(Integer generationUnitId, Date startDate, Date endDate);

    public Integer cancelTransferOrder(Integer transferOrderId, Integer updatedBy) throws SumoException;

    public Integer createClubbedTransferOrder(List<Integer> clubRoIds, TransferOrder transferOrder, Date fulfilmentDate, IdCodeName fulfillmentCompany, IdCodeName requestCompany, String invoiceId) throws TransferOrderCreationException, InventoryUpdateException, SumoException, DataNotFoundException;

    public Integer createExternalTransferOrder(TransferOrder transferOrder, String invoiceId) throws TransferOrderCreationException, InventoryUpdateException, SumoException, DataNotFoundException;

    public boolean approveExternalTransferOrder(Integer transferOrderId, Integer userId) throws DataNotFoundException, DataUpdationException;

    public ExpiryDataRequest getTransferOrderItemExpiries(ExpiryDataRequest transferOrder) throws InventoryUpdateException, DataNotFoundException;

    public Integer checkIfAlreadyCreated(Integer requestOrderId);

    public void updateRequestOrder(Integer requestOrderId);

    public String getEwayBillNumber(int transferOrderId);


    String getInvoiceId(int generationUnitId, int generatedForUnitId, boolean isDifferentCompanyId);

    public boolean getAlreadyTransferredRO(List<Integer> roIds);

    public SkuToProductResponse getSkuToProducts(Integer unitId, List<Integer> roIds, Boolean isPackagingRequired, Map<Integer, Integer> roItemSkuMap) throws SumoException;

    public List<TransferOrderData> createBulkTransferOrders(ClubbedTORequest clubbedTORequest, BulkTransferEvent bulkTransferEvent) throws TransferOrderCreationException,
            DayCloseInitiatedException, InventoryUpdateException, SumoException, DataNotFoundException;

    public List<ProductionBooking> bulkProductionBooking(Integer unitId, Map<Integer, GoodsReceivedItem> productToQuantityMap,
                                                         IdCodeName generatedBy, Map<Integer, ProductionBooking> skuToProductionBooking) throws DataNotFoundException, SumoException;

    public TransferOrderData createTransferOrder(TransferOrder transferOrder, boolean external, String invoiceId,
                                                 List<ProductionBooking> productionBookingList) throws SumoException, DataNotFoundException, InventoryUpdateException, TransferOrderCreationException;

    public boolean getAlreadyTransferredBulkRO(List<Integer> roIds);

    public BulkTransferEvent initiateBulkTransferEvent(ClubbedTORequest clubbedTORequest, Integer userId, Integer unitId, String type) throws SumoException;

    public void closeBulkTransferEvent(Integer TOCount, Integer bulkTransferEventId, Boolean isInvoiceSet);

    public List<ProductionBooking> calculateConsumptions(Integer generationUnitId, ProductDefinition pd, Float quantity,
                                                         List<ProductionBooking> productionBookingList, HashMap<Integer, IdCodeName> map,
                                                         List<String> errors, Date expiryDate, IdCodeName generatedBy) throws DataNotFoundException, SumoException;

    public BulkTransferEvent findBulkEvent(Integer unitId, Integer bulkEventId) throws SumoException;

    public List<BulkTransferEvent> findBulkEventsShort(Integer unitId, Date startDate, Date endDate) throws SumoException;

    public List<TransferOrder> getTOsByIds(List<Integer> toIds);

    public Boolean generateInvoiceForTO(List<TransferOrderData> transferOrderDataList, Integer eventId) throws SumoException;

    public List<TransferredAsset> findAssetTransfersByUnitId(Integer unitId) throws SumoException;

    public Boolean updateAssetStatus(TransferredAsset asset, Boolean isOriginal, Integer userId) throws SumoException;

    public Boolean bulkUpdateAssetStatus(List<TransferredAsset> assetList, Boolean isOriginal, Integer userId) throws SumoException;

    public List<TransferOrderData> bulkStandAloneTransfer(Map<Integer, Map<Integer, GoodsReceivedItem>> unitWiseDistribution, Integer unitId,
                                                          Integer userId, Integer bulkEventId) throws DataNotFoundException, SumoException, TransferOrderCreationException, InventoryUpdateException;

    public List<Integer> getAllActiveReceivingUnits(FulfillmentType fulfillmentType, Integer unitId);

    public Boolean validateStandAloneSheet(Map<Integer, Map<Integer, GoodsReceivedItem>> unitDistribution, Integer unitId) throws SumoException;

    public GoodsReceivedItem setSkuDistribution(Integer skuId, Float quantity, Integer unitId, Integer packagingId, Boolean isPkgQty);

    public List<TransferOrder> getTransferObjects(Map<Integer, Map<Integer, GoodsReceivedItem>> unitWiseDistribution,
                                                  Integer unitId, Integer userId, Boolean isStandAlone);

    public Boolean isFullfilmentWH(Integer unitId);

    public Boolean validateIfDayCloseDoneInMonth(SCMDayCloseEventData eventData, Boolean isWarning);
    public  Disclaimer validateIfDayCloseDoneInWeek(SCMDayCloseEventData eventData) ;

    public Boolean checkIfUnitIsExcludedForDayCloseCheck(Integer unitId);

    public Boolean createAutoTransferForSpecializedRO(List<RequestOrderData> requestOrderDataList, Integer userId
    ) throws SumoException;

    public GoodsReceivedData getGrByTO(Integer toId);

    public Boolean initiateFaTransfer(Map<Integer,BigDecimal> productAndQtyMap , Integer receivingUnitId , Integer fullfillingUnitId , String budgetType , Integer userId , String toType ,Integer roId) throws SumoException;

    Map<String, String> findMissingDistanceMapping(List<TransferOrder> transferOrderList, Boolean throwError) throws SumoException;

    public  void downloadEInvoiceJson(List<Integer> toIds , HttpServletResponse response) throws IOException, SumoException;

    public Boolean uploadInvoiceSheet(MultipartFile file) throws IOException, SumoException;

    public TransferOrderEInvoiceDTO findEInvoiceByTOId(Integer toId) throws MalformedURLException;

    eInvoiceResponseObject findPendingTransferOrders(Integer selectedStateCode, Integer generationUnitId, Integer generatedForUnitId, Date start, Date end);

    public  Boolean intiateChangeOnFa(Integer productId , Integer eventId , Boolean toAdd ,BigDecimal transferQty) throws SumoException;

    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void autoGrSpecializedOrder(Integer toId,TransferOrder transferOrder) throws InventoryUpdateException, ParseException, SumoException;
}

