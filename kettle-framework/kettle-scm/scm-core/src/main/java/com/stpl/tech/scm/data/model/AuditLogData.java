package com.stpl.tech.scm.data.model;


import javax.persistence.*;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "AUDIT_LOG_DATA")
public class AuditLogData {
    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "AUDIT_LOG_DATA_ID")
    private Integer auditLogDataId;

    @Column(name = "KEY_ID")
    private Integer keyId;

    @Column(name = "KEY_TYPE")
    private String keyType;

    @Column(name = "CHANGE_TYPE")
    private String changeType;

    @Column(name = "CREATED_ON")
    private Date createdOn;

    @Column(name = "UPDATED_ON")
    private Date updatedOn;

    @Column(name = "CHANGED_BY")
    private Integer changedBy;

    @Column(name = "VERSION")
    private Integer version;

    @Column(name = "OBJECT_ID")
    private Integer objectId;

    public Integer getAuditLogDataId() {
        return this.auditLogDataId;
    }

    public void setAuditLogDataId(Integer auditLogDataId) {
        this.auditLogDataId = auditLogDataId;
    }

    public Integer getKeyId() {
        return this.keyId;
    }

    public void setKeyId(Integer keyId) {
        this.keyId = keyId;
    }

    public String getKeyType() {
        return this.keyType;
    }

    public void setKeyType(String keyType) {
        this.keyType = keyType;
    }

    public String getChangeType() {
        return this.changeType;
    }

    public void setChangeType(String changeType) {
        this.changeType = changeType;
    }

    public Date getCreatedOn() {
        return this.createdOn;
    }

    public void setCreatedOn(Date createdOn) {
        this.createdOn = createdOn;
    }

    public Date getUpdatedOn() {
        return this.updatedOn;
    }

    public void setUpdatedOn(Date updatedOn) {
        this.updatedOn = updatedOn;
    }

    public Integer getChangedBy() {
        return this.changedBy;
    }

    public void setChangedBy(Integer changedBy) {
        this.changedBy = changedBy;
    }

    public Integer getVersion() {
        return this.version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Integer getObjectId() {
        return this.objectId;
    }

    public void setObjectId(Integer objectId) {
        this.objectId = objectId;
    }
}
