package com.stpl.tech.scm.data.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "SERVICE_ORDER_ITEM")
public class ServiceOrderItemData {

	private Integer id;
	private int costElementId;
	private String costElementName;
	private String serviceDescription;	
	private String ascCode;
	private BigDecimal requestedQuantity;
	private BigDecimal receivedQuantity;
	private String unitOfMeasure;
	private BigDecimal unitPrice;
	private BigDecimal totalCost;
	private Integer serviceOrderId;
	private BigDecimal taxRate;
	private BigDecimal tdsRate;
	private BigDecimal totalTax;
	private Integer businessCostCenterId;
	private String businessCostCenterName;
	private String type;
	private Date costElementDate;
	private Date costElementToDate;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "SERVICE_ORDER_ITEM_ID", nullable = false, unique = true)
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Column(name = "COST_ELEMENT_ID", nullable = false)
	public int getCostElementId() {
		return costElementId;
	}

	public void setCostElementId(int costElementId) {
		this.costElementId = costElementId;
	}

	@Column(name = "ASC_CODE", nullable = false)
	public String getAscCode() {
		return ascCode;
	}

	public void setAscCode(String hsnCode) {
		this.ascCode = hsnCode;
	}

	@Column(name = "COST_ELEMENT_NAME", nullable = false)
	public String getCostElementName() {
		return costElementName;
	}

	public void setCostElementName(String costElementName) {
		this.costElementName = costElementName;
	}

	@Column(name = "SERVICE_DESCRIPTION", nullable = false)
	public String getServiceDescription() {
		return serviceDescription;
	}

	public void setServiceDescription(String serviceDescription) {
		this.serviceDescription = serviceDescription;
	}

	@Column(name = "REQUESTED_QUANTITY", nullable = false)
	public BigDecimal getRequestedQuantity() {
		return requestedQuantity;
	}

	public void setRequestedQuantity(BigDecimal requestedQuantity) {
		this.requestedQuantity = requestedQuantity;
	}

	@Column(name = "RECEIVED_QUANTITY", nullable = true)
	public BigDecimal getReceivedQuantity() {
		return receivedQuantity;
	}

	public void setReceivedQuantity(BigDecimal receivedQuantity) {
		this.receivedQuantity = receivedQuantity;
	}

	@Column(name = "UNIT_OF_MEASURE", nullable = false)
	public String getUnitOfMeasure() {
		return unitOfMeasure;
	}

	public void setUnitOfMeasure(String unitOfMeasure) {
		this.unitOfMeasure = unitOfMeasure;
	}

	@Column(name = "UNIT_PRICE", nullable = true)
	public BigDecimal getUnitPrice() {
		return unitPrice;
	}

	public void setUnitPrice(BigDecimal unitPrice) {
		this.unitPrice = unitPrice;
	}

	@Column(name = "TOTAL_COST", nullable = true)
	public BigDecimal getTotalCost() {
		return totalCost;
	}

	public void setTotalCost(BigDecimal totalCost) {
		this.totalCost = totalCost;
	}

	@Column(name = "SERVICE_ORDER_ID", nullable = true)
	public Integer getServiceOrderId() {
		return serviceOrderId;
	}

	public void setServiceOrderId(Integer serviceOrderId) {
		this.serviceOrderId = serviceOrderId;
	}

	@Column(name = "TDS_RATE", nullable = true)
	public BigDecimal getTdsRate() {
		return tdsRate;
	}

	public void setTdsRate(BigDecimal tdsRate) {
		this.tdsRate = tdsRate;
	}

	@Column(name = "TAX_RATE", nullable = true)
	public BigDecimal getTaxRate() {
		return taxRate;
	}

	public void setTaxRate(BigDecimal taxRate) {
		this.taxRate = taxRate;
	}

	@Column(name = "TOTAL_TAX_VALUE", nullable = true)
	public BigDecimal getTotalTax() {
		return totalTax;
	}

	public void setTotalTax(BigDecimal totalTax) {
		this.totalTax = totalTax;
	}

	@Column(name = "BUSINESS_COST_CENTER_ID", nullable = true)
	public Integer getBusinessCostCenterId() {
		return businessCostCenterId;
	}

	public void setBusinessCostCenterId(Integer businessCostCenterId) {
		this.businessCostCenterId = businessCostCenterId;
	}

	@Column(name = "BUSINESS_COST_CENTER_NAME", nullable = true)
	public String getBusinessCostCenterName() {
		return businessCostCenterName;
	}

	public void setBusinessCostCenterName(String businessCostCenterName) {
		this.businessCostCenterName = businessCostCenterName;
	}

	@Column(name = "TYPE", nullable = true)
	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	@Column(name = "COST_ELEMENT_DATE")
	public Date getCostElementDate() {
		return costElementDate;
	}

	public void setCostElementDate(Date costElementDate) {
		this.costElementDate = costElementDate;
	}

	@Column(name = "COST_ELEMENT_TO_DATE")
	public Date getCostElementToDate() {
		return costElementToDate;
	}

	public void setCostElementToDate(Date costElementToDate) {
		this.costElementToDate = costElementToDate;
	}
}
