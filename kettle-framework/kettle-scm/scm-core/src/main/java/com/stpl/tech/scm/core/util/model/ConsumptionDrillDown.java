/**
 * 
 */
package com.stpl.tech.scm.core.util.model;

import java.math.BigDecimal;

import com.stpl.tech.scm.data.model.CostDetailData;
import com.stpl.tech.scm.domain.vo.InventoryItemVO;

/**
 * <AUTHOR>
 *
 */
public class ConsumptionDrillDown {

	private InventoryItemVO item;
	private CostDetailData data;
	private BigDecimal quantity;
	private BigDecimal price;
	private BigDecimal cost;
	private boolean variance;

	public InventoryItemVO getItem() {
		return item;
	}

	public void setItem(InventoryItemVO vo) {
		this.item = vo;
	}

	public BigDecimal getQuantity() {
		return quantity;
	}

	public void setQuantity(BigDecimal quantity) {
		this.quantity = quantity;
	}

	public BigDecimal getPrice() {
		return price;
	}

	public void setPrice(BigDecimal cost) {
		this.price = cost;
	}

	public boolean isVariance() {
		return variance;
	}

	public void setVariance(boolean variance) {
		this.variance = variance;
	}

	public BigDecimal getCost() {
		return cost;
	}

	public void setCost(BigDecimal cost) {
		this.cost = cost;
	}
	public CostDetailData getData() {
		return data;
	}

	public void setData(CostDetailData data) {
		this.data = data;
	}

	
	@Override
	public String toString() {

	    if(item==null || data==null){
	        return super.toString();
        }

        return "ConsumptionDrillDown [inventoryItemId=" + item==null ? "" : item.getItemKeyId() + ", costDetailId=" + data==null ? " ":data.getCostDetailDataId() + ", quantity=" + quantity + ", price=" + price + ", cost=" + cost
				+ ", variance=" + variance + "]";
	}

	
	 

}
