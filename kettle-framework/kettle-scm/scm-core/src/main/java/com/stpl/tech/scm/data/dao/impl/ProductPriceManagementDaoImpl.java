package com.stpl.tech.scm.data.dao.impl;

import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.recipe.model.RecipeCostData;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.util.MultiPartFileHelper;
import com.stpl.tech.scm.core.util.SCMKettleEndpoint;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.dao.ProductPriceManagementDao;
import com.stpl.tech.scm.data.model.CostDetailData;
import com.stpl.tech.scm.data.model.PriceUpdateEntryData;
import com.stpl.tech.scm.data.model.PriceUpdateEntryDrillDown;
import com.stpl.tech.scm.data.model.PriceUpdateEntryError;
import com.stpl.tech.scm.data.model.PriceUpdateEventData;
import com.stpl.tech.scm.data.model.ProductDefinitionData;
import com.stpl.tech.scm.data.model.ProductRecipeCostData;
import com.stpl.tech.scm.data.model.SkuDefinitionData;
import com.stpl.tech.scm.domain.model.PriceData;
import com.stpl.tech.scm.domain.model.PriceUpdateDrillDown;
import com.stpl.tech.scm.domain.model.PriceUpdateEntry;
import com.stpl.tech.scm.domain.model.PriceUpdateEntryType;
import com.stpl.tech.scm.domain.model.PriceUpdateEvent;
import com.stpl.tech.scm.domain.model.PriceUpdateEventStatus;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.ProductPriceData;
import com.stpl.tech.scm.domain.model.SwitchStatus;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.web.servlet.View;

import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
public class ProductPriceManagementDaoImpl extends SCMAbstractDaoImpl implements ProductPriceManagementDao {

	private final Logger LOG = LoggerFactory.getLogger(ProductPriceManagementDaoImpl.class);

	@Autowired
	private EnvProperties env;

	@Autowired
	private SCMCache cache;

	@Override
	public List<PriceUpdateEvent> getSkuPriceUpdateEvents() {
		List<PriceUpdateEvent> events = new ArrayList<>();
		Query query = manager.createQuery("FROM PriceUpdateEventData p order by p.creationTime desc");
		List<PriceUpdateEventData> result = query.getResultList();
		if (result != null) {
			for (PriceUpdateEventData eventData : result) {
				events.add(SCMDataConverter.convert(eventData, false));
			}
		}
		return events;
	}

	@Override
	public PriceUpdateEvent getSkuPriceUpdateEventData(int eventId) {
		PriceUpdateEventData eventData = find(PriceUpdateEventData.class, eventId);
		if (eventData != null) {
			return SCMDataConverter.convert(eventData, true);
		}
		return null;
	}

	@Override
	public Integer addSkuPriceUpdateEvent(PriceUpdateEvent event) throws DataUpdationException, SumoException {
		List<PriceUpdateEvent> initiatedEvents = getInitiatedSkuPriceUpdateEvents();
		if (initiatedEvents.isEmpty()) {
			PriceUpdateEventData data = new PriceUpdateEventData();
			setValues(event, data);
			data = add(data, true);
			for (PriceUpdateEntry entry : event.getEntries()) {
				Integer linkedProductId = cache.getSkuDefinition(entry.getKeyId()).getLinkedProduct().getId();
				if (SCMServiceConstants.CATEGORY_SEMI_FINISHED == cache.getProductDefinitions().get(linkedProductId)
						.getCategoryDefinition().getId()) {
					throw new DataUpdationException(
							"Cannot update Semi Finished Product Prices: " + entry.getKeyName());
				}
				if (BigDecimal.ZERO.compareTo(entry.getUpdatedUnitPrice()) != 0
						&& entry.getUpdatedUnitPrice().compareTo(entry.getUnitPrice()) != 0) {
					PriceUpdateEntryData entryData = new PriceUpdateEntryData();
					entryData.setEventId(data);
					setValues(entry, entryData);
					entryData = add(entryData, false);
					data.getEntries().add(entryData);
				}
			}
			flush();
			return data.getId();
		}
		return null;
	}

	@Override
	public void addPriceUpdateEntries(List<PriceUpdateEntry> recipeEntries, PriceUpdateEvent updatedEvent) {
		PriceUpdateEventData event = manager.find(PriceUpdateEventData.class, updatedEvent.getEventId());
		for (PriceUpdateEntry entry : recipeEntries) {
			PriceUpdateEntryData entryData = new PriceUpdateEntryData();
			entryData.setEventId(event);
			setValues(entry, entryData);
			manager.persist(entryData);
			manager.flush();
			if (entry.getErrors() != null && entry.getErrors().size() > 0) {
				for (String s : entry.getErrors()) {
					createErrorEntry(s, entryData, event);
				}
			}
			for (PriceUpdateDrillDown dd : entry.getDrilldowns()) {
				PriceUpdateEntryDrillDown drill = new PriceUpdateEntryDrillDown();
				drill.setEntryId(entryData);
				drill.setEventId(event);
				setValues(dd, drill);
				manager.persist(drill);
				if (dd.getErrors() != null && dd.getErrors().size() > 0) {
					for (String s : dd.getErrors()) {
						createErrorEntry(s, drill, event);
					}
				}
			}
		}

		manager.flush();
		manager.refresh(event);
		event.setNoOfRecords(event.getEntries().size());
		manager.flush();
	}

	private void createErrorEntry(String s, PriceUpdateEntryDrillDown drill, PriceUpdateEventData event) {
		PriceUpdateEntryError error = new PriceUpdateEntryError();
		error.setEntryId(drill.getId());
		error.setEventId(event.getId());
		error.setErrorMessage(s);
		error.setEntryType("DRILLDOWN");
		drill.getErrors().add(error);
		manager.persist(error);
	}

	private void createErrorEntry(String s, PriceUpdateEntryData drill, PriceUpdateEventData event) {
		PriceUpdateEntryError error = new PriceUpdateEntryError();
		error.setEntryId(drill.getId());
		error.setEventId(event.getId());
		error.setErrorMessage(s);
		error.setEntryType("ENTRY");
		drill.getErrors().add(error);
		manager.persist(error);
	}

	private void setValues(PriceUpdateDrillDown dd, PriceUpdateEntryDrillDown edd) {
		edd.setCost(dd.getCost());
		edd.setDrilldownCategory(dd.getDrilldownCategory());
		edd.setDrilldownType(dd.getDrilldownType());
		edd.setHasError(AppConstants.getValue(dd.isError()));
		edd.setKeyId(dd.getKeyId());
		edd.setKeyName(dd.getKeyName());
		edd.setKeyType(dd.getKeyType());
		edd.setQuantity(dd.getQuantity());
		edd.setUnitOfMeasure(dd.getUnitOfMeasure());
		edd.setUnitPrice(dd.getUnitPrice());
	}

	@Override
	public boolean callProductPriceUpdate(int id) {
		Query query = manager.createNativeQuery("CALL PRODUCT_PRICE_CHANGE_CALCULATE(:eventId)");
		query.setParameter("eventId", id);
		query.executeUpdate();
		manager.flush();
		return true;
	}

	@Override
	public PriceUpdateEvent getPriceUpdateEvent(int id) {
		PriceUpdateEventData event = manager.find(PriceUpdateEventData.class, id);
		return SCMDataConverter.convert(event, true);
	}

	@Override
	public void failEvent(Integer id) {
		PriceUpdateEventData event = manager.find(PriceUpdateEventData.class, id);
		if (event != null) {
			event.setEventStatus(PriceUpdateEventStatus.FAILED.name());
			update(event, true);
		}
	}

	private List<PriceUpdateEvent> getInitiatedSkuPriceUpdateEvents() {
		List<PriceUpdateEvent> events = new ArrayList<>();
		Query query = manager.createQuery("FROM PriceUpdateEventData p where p.eventStatus = :eventStatus");
		query.setParameter("eventStatus", PriceUpdateEventStatus.INITIATED.name());
		List<PriceUpdateEventData> result = query.getResultList();
		if (result != null) {
			for (PriceUpdateEventData eventData : result) {
				events.add(SCMDataConverter.convert(eventData, true));
			}
		}
		return events;
	}

	private void setValues(PriceUpdateEvent event, PriceUpdateEventData data) {
		data.setFinalizationTime(event.getFinalizationTime());
		data.setFinalizedBy(event.getFinalizedBy());
		data.setEventStatus(event.getEventStatus().name());
		data.setCreatedBy(event.getCreatedBy());
		data.setCreatedByName(event.getCreatedByName());
		data.setCreationTime(event.getCreationTime());
		data.setDataFilePath(event.getDataFilePath());
		data.setEventActionType(event.getEventActionType().name());
		data.setEventType(event.getEventType().name());
		data.setFinalizedByName(event.getFinalizedByName());
		data.setNoOfErrors(event.getNoOfErrors());
		data.setNoOfRecords(event.getNoOfRecords());
	}

	private void setValues(PriceUpdateEntry entry, PriceUpdateEntryData entryData) {
		entryData.setEditedUnitPrice(entry.getEditedUnitPrice());
		entryData.setEntryStatus(entry.getEntryStatus().name());
		entryData.setKeyId(entry.getKeyId());
		entryData.setKeyName(entry.getKeyName());
		entryData.setUnitOfMeasure(entry.getUnitOfMeasure());
		entryData.setUnitPrice(entry.getUnitPrice());
		entryData.setUpdatedUnitPrice(entry.getUpdatedUnitPrice());
		entryData.setEditedUnitPrice(entry.getEditedUnitPrice());
		entryData.setHasError(AppConstants.getValue(entry.isError()));
		entryData.setKeyType(entry.getKeyType().name());
		entryData.setApprovedUnitPrice(entry.getApprovedUnitPrice());
	}

	@Override
	public boolean updateSkuPriceEvent(int eventId, int userId, String userName, PriceUpdateEventStatus status) throws SumoException {
		PriceUpdateEventData e = find(PriceUpdateEventData.class, eventId);
		// update only if event is in initiated state
		if (PriceUpdateEventStatus.INITIATED.equals(PriceUpdateEventStatus.valueOf(e.getEventStatus()))) {

			for (PriceUpdateEntryData entryData : e.getEntries()) {

				// set price to approved if status is approved
				if (PriceUpdateEventStatus.APPROVED.equals(status)) {
					BigDecimal price = BigDecimal.ZERO;
					price = BigDecimal.ZERO;
					if (entryData.getEditedUnitPrice() != null
							&& entryData.getEditedUnitPrice().compareTo(BigDecimal.ZERO) != 0) {
						price = entryData.getEditedUnitPrice();
					} else {
						price = entryData.getUpdatedUnitPrice();
					}
					entryData.setApprovedUnitPrice(price);
				}
				entryData.setEntryStatus(status.name());
			}

			e.setEventStatus(status.name());
			e.setFinalizedBy(userId);
			e.setFinalizedByName(userName);
			e.setFinalizationTime(AppUtils.getCurrentTimestamp());
			add(e, true);
			return true;
		}
		return false;
	}

	@Override
	public View downloadSkuPriceUpdateFile() {
		Query query = manager.createQuery(
				"FROM SkuDefinitionData p WHERE p.skuStatus = :skuStatus AND p.linkedProduct.categoryDefinition.categoryId != :categoryId");
		query.setParameter("skuStatus", SwitchStatus.ACTIVE.name());
		query.setParameter("categoryId", SCMServiceConstants.CATEGORY_SEMI_FINISHED);
		List<SkuDefinitionData> data = query.getResultList();
		View view = MultiPartFileHelper.createView(data, "SCM SKU Price data", "SKU Price Data");
		return view;
	}

	@Override
	public void approveProductPrice(int eventId) throws IOException, SumoException {
		PriceUpdateEventData eventData = find(PriceUpdateEventData.class, eventId);
		List<RecipeCostData> costData = new ArrayList<>();
		for (PriceUpdateEntryData entryData : eventData.getEntries()) {
			// SKU Prices Updated
			if (PriceUpdateEntryType.SKU.name().equals(entryData.getKeyType())) {
				addCostForSCMSku(entryData);
			}
			// Product Prices Updated
			if (PriceUpdateEntryType.PRODUCT.name().equals(entryData.getKeyType())) {
				addCostForSCMProduct(entryData);
			}

			// Recipe Prices Updated
			if (PriceUpdateEntryType.RECIPE_CAFE.name().equals(entryData.getKeyType())
					|| PriceUpdateEntryType.RECIPE_COD.name().equals(entryData.getKeyType())
					|| PriceUpdateEntryType.RECIPE_DELIVERY.name().equals(entryData.getKeyType())
					|| PriceUpdateEntryType.RECIPE_TAKE_AWAY.name().equals(entryData.getKeyType())) {
				// recipes approved
				RecipeCostData c = new RecipeCostData();
				c.setRecipeId(entryData.getKeyId());
				c.setRecipeName(entryData.getKeyName());
				c.setCategory(getCategory(PriceUpdateEntryType.valueOf(entryData.getKeyType())));
				c.setCost(entryData.getApprovedUnitPrice());
				c.setUpdatedBy(eventData.getFinalizedByName());
				c.setUpdatedById(eventData.getFinalizedBy());
				c.setEventEntryId(eventData.getId());
				costData.add(c);
			}
			if (PriceUpdateEntryType.RECIPE_SCM.name().equals(entryData.getKeyType())) {
				// SCM Recipes data
				RecipeCostData c = new RecipeCostData();
				c.setRecipeId(entryData.getKeyId());
				c.setRecipeName(entryData.getKeyName());
				c.setProductId(entryData.getKeyId());
				c.setProductName(entryData.getKeyName());
				c.setDimension(entryData.getUnitOfMeasure());
				c.setCategory(getCategory(PriceUpdateEntryType.valueOf(entryData.getKeyType())));
				c.setCost(entryData.getApprovedUnitPrice());
				c.setUpdatedBy(eventData.getFinalizedByName());
				c.setUpdatedById(eventData.getFinalizedBy());
				c.setEventEntryId(eventData.getId());
				costData.add(c);
			}
		}
		if (!costData.isEmpty()) {
			sendCostDataToKettle(costData);
		}
	}

	private void addCostForSCMProduct(PriceUpdateEntryData entryData) throws SumoException {
		ProductDefinitionData product = find(ProductDefinitionData.class, entryData.getKeyId());
		product.setUnitPrice(entryData.getApprovedUnitPrice());
		product.setNegotiatedUnitPrice(entryData.getApprovedUnitPrice());
		add(product, true);
	}

	private void addCostForSCMSku(PriceUpdateEntryData entryData) throws SumoException {
		SkuDefinitionData sku = find(SkuDefinitionData.class, entryData.getKeyId());
		sku.setUnitPrice(entryData.getApprovedUnitPrice());
		sku.setNegotiatedUnitPrice(entryData.getApprovedUnitPrice());
		sku.setPriceLastUpdated(AppUtils.getCurrentTimestamp());
		add(sku, true);
	}

	private UnitCategory getCategory(PriceUpdateEntryType keyType) {
		switch (keyType) {
		case RECIPE_CAFE:
			return UnitCategory.CAFE;
		case RECIPE_COD:
			return UnitCategory.COD;
		case RECIPE_DELIVERY:
			return UnitCategory.DELIVERY;
		case RECIPE_TAKE_AWAY:
			return UnitCategory.TAKE_AWAY;
		case RECIPE_SCM:
			return UnitCategory.CAFE;
		default:
			return null;
		}
	}

	@Override
	public void sendCostDataToKettle(List<RecipeCostData> costData) throws IOException {
		String endpoint = getRecipeUpdateEndPoint();
		String authToken = env.getAuthToken();
		WebServiceHelper.postRequestWithAuthInternal(endpoint, authToken, costData);
	}

	private String getRecipeUpdateEndPoint() {
		return env.getMasterServiceBasePath() + SCMKettleEndpoint.RECIPE_COST_UPDATE;
	}

	@Override
	public Integer updateSkuPriceEvent(PriceUpdateEvent event, Integer userId, String userName) throws SumoException {
		PriceUpdateEventData eventData = find(PriceUpdateEventData.class, event.getEventId());
		if (eventData != null && PriceUpdateEventStatus.INITIATED.name().equals(eventData.getEventStatus())) {

			for (PriceUpdateEntryData entryData : eventData.getEntries()) {
				for (PriceUpdateEntry entry : event.getEntries()) {
					if (entryData.getId().equals(entry.getId()) && entry.getEditedUnitPrice() != null
							&& BigDecimal.ZERO.compareTo(entry.getEditedUnitPrice()) != 0) {
						entryData.setEditedUnitPrice(entry.getEditedUnitPrice());
					}
				}
			}
			add(eventData, true);
			return eventData.getId();
		}
		return null;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.scm.data.dao.ProductPriceManagementDao#
	 * getCurrentProductCost(int, java.util.List)
	 */
	@Override
	public Map<Integer, PriceData> getCurrentProductPrice(int unitId, List<Integer> products) {
		return getPriceData(unitId, "PRODUCT", products);
	}

	private Map<Integer, PriceData> getPriceData(int unitId, String keyType, List<Integer> keyIds) {
		Map<Integer, PriceData> data = new HashMap<>();
		Query query = manager.createQuery(
				"FROM CostDetailDataCafe p WHERE p.unitId = :unitId AND p.keyId IN(:keyIds) and p.keyType = :keyType");
		query.setParameter("keyType", keyType);
		query.setParameter("unitId", unitId);
		query.setParameter("keyIds", keyIds);
		List<CostDetailData> list = query.getResultList();
		if (list != null && list.size() > 0) {
			list.stream().forEach((cost) -> {
				PriceData price = new PriceData();
				price.setId(cost.getCostDetailDataId());
				price.setKeyId(cost.getKeyId());
				price.setPrice(cost.getPrice());
				price.setQuantity(cost.getQuantity());
				data.put(cost.getKeyId(), price);
			});
		}
		return data;

	}

	@Override
	public Map<Integer, Map<Integer, ProductPriceData>> getAllCurrentProductPriceMap() {
		Map<Integer, Map<Integer, ProductPriceData>> map = new HashMap<>();
		Query query = manager.createQuery("FROM CostDetailDataCafe p WHERE p.keyType = :keyType and p.latest = :latest");
		query.setParameter("keyType", "PRODUCT");
		query.setParameter("latest", AppConstants.YES);
		List<CostDetailData> list = query.getResultList();
		if (list != null && list.size() > 0) {
			list.stream().forEach((cost) -> {
				ProductDefinition pd = cache.getProductDefinition(cost.getKeyId());
				ProductPriceData price = new ProductPriceData();
				price.setProductId(pd.getProductId());
				price.setName(pd.getProductName());
				price.setPrice(cost.getPrice());
				price.setUom(pd.getUnitOfMeasure());
				addToPriceMap(cost.getUnitId(), price, map);
			});
		}
		return map;
	}

	private void addToPriceMap(int unitId, ProductPriceData price, Map<Integer, Map<Integer, ProductPriceData>> map) {
		Map<Integer, ProductPriceData> innerMap = map.get(unitId);
		if (innerMap == null) {
			innerMap = new HashMap<>();
			map.put(unitId, innerMap);
		}
		innerMap.put(price.getProductId(), price);
	}

	@Override
	public void saveRecipeCosts(List<RecipeCostData> recipeCostList) throws SumoException {
        for(RecipeCostData r : recipeCostList){
            add(convert(r), false);
        }
		flush();
	}

	private ProductRecipeCostData convert(RecipeCostData costData) {
		ProductRecipeCostData cost = new ProductRecipeCostData();
		cost.setRecipeId(costData.getRecipeId());
		cost.setRecipeName(costData.getRecipeName());
		cost.setCostType(costData.getCategory().name());
		cost.setCost(costData.getCost().setScale(6, RoundingMode.HALF_UP));
		cost.setLastUpdatedTime(AppUtils.getCurrentTimestamp());
		cost.setProductSource(costData.getProductSource());
		cost.setProductId(costData.getProductId());
		cost.setDimension(costData.getDimension());
		cost.setLastUpdatedTime(SCMUtil.getCurrentTimestamp());
		cost.setUnitId(costData.getUnitId());
		return cost;
	}

	@Override
	public List<CostDetailData> fetchUnitInventroy(int unitId) {
		Query query = manager.createQuery("FROM CostDetailDataCafe p WHERE p.unitId = :unitId");
		query.setParameter("unitId", unitId);
		return query.getResultList();
	}

	@Override
	public List<CostDetailData> getCostDetailForIds(int unitId, List<Integer> ids) {
		try {
			Query query = manager.createQuery("FROM CostDetailDataCafe c WHERE c.unitId = :unitId and c.keyId in (:ids)");
			query.setParameter("unitId", unitId);
			query.setParameter("ids", ids);
			return query.getResultList();
		}catch (NoResultException e){
			LOG.error("No cost detail found for unit id : {}", unitId);
		}
		return new ArrayList<>();
	}

}
