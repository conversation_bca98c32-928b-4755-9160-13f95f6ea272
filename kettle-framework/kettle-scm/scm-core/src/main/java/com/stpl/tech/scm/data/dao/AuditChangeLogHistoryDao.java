package com.stpl.tech.scm.data.dao;


import com.stpl.tech.scm.data.mongo.AuditChangeLogHistory;
import org.joda.time.DateTime;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface AuditChangeLogHistoryDao extends MongoRepository<AuditChangeLogHistory, String> {


    List<AuditChangeLogHistory> findByAuditChangeLog_UpdatedOnGreaterThan(DateTime lastEntryTime);



}

