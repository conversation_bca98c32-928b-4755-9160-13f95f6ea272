package com.stpl.tech.scm.data.model;

import javax.persistence.*;
import java.math.BigDecimal;

@Entity
@Table(name = "INVOICE_EXCESS_QUANTITY")
public class InvoiceExcessQuantity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "INVOICE_EXCESS_QUANTITY_ID")
    private Integer invoiceExcessQuantityId;

    @Column(name = "INVOICE_ID")
    private Integer invoiceId;

    @Column(name = "UNIT_ID")
    private Integer unitId;

    @Column(name = "SKU_ID")
    private Integer skuId;

    @Column(name = "SKU_NAME")
    private String skuName;

    @Column(name = "EXCESS_QTY")
    private BigDecimal excessQty;

    @Column(name = "UNIT_PRICE")
    private BigDecimal unitPrice;



    public Integer getInvoiceExcessQuantityId() {
        return this.invoiceExcessQuantityId;
    }

    public void setInvoiceExcessQuantityId(Integer invoiceExcessQuantityId) {
        this.invoiceExcessQuantityId = invoiceExcessQuantityId;
    }

    public Integer getInvoiceId() {
        return this.invoiceId;
    }

    public void setInvoiceId(Integer invoiceId) {
        this.invoiceId = invoiceId;
    }

    public Integer getUnitId() {
        return this.unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Integer getSkuId() {
        return this.skuId;
    }

    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }

    public String getSkuName() {
        return this.skuName;
    }

    public void setSkuName(String skuName) {
        this.skuName = skuName;
    }

    public BigDecimal getExcessQty() {
        return this.excessQty;
    }

    public void setExcessQty(BigDecimal excessQty) {
        this.excessQty = excessQty;
    }

    public BigDecimal getUnitPrice() {
        return this.unitPrice;
    }

    public void setUnitPrice(BigDecimal unitPrice) {
        this.unitPrice = unitPrice;
    }


}
