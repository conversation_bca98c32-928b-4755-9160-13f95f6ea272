
package com.stpl.tech.scm.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "PAYMENT_DEVIATION")
public class PaymentDeviationData {

    private Integer id;
    private String deviationCode;
    private String deviationType;
    private String deviationLevel;
    private String deviationDetail;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "PAYMENT_DEVIATION_ID", unique = true, nullable = false)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "DEVIATION_CODE", nullable = false, length = 50)
    public String getDeviationCode() {
        return deviationCode;
    }

    public void setDeviationCode(String deviationCode) {
        this.deviationCode = deviationCode;
    }

    @Column(name = "DEVIATION_TYPE", nullable = false)
    public String getDeviationType() {
        return deviationType;
    }

    public void setDeviationType(String deviationType) {
        this.deviationType = deviationType;
    }

    @Column(name = "DEVIATION_LEVEL", nullable = false)
    public String getDeviationLevel() {
        return deviationLevel;
    }

    public void setDeviationLevel(String deviationLevel) {
        this.deviationLevel = deviationLevel;
    }

    @Column(name = "DEVIATION_DETAIL", nullable = false)
    public String getDeviationDetail() {
        return deviationDetail;
    }

    public void setDeviationDetail(String deviationDetail) {
        this.deviationDetail = deviationDetail;
    }
}
