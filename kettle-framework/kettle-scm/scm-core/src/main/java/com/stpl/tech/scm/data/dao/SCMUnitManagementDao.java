package com.stpl.tech.scm.data.dao;

import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.data.model.AssetDefinitionData;
import com.stpl.tech.scm.data.model.BusinessCostCenterData;
import com.stpl.tech.scm.data.model.FulfillmentUnitMappingData;
import com.stpl.tech.scm.data.model.UnitDistanceMappingData;

import java.util.Date;
import java.util.List;

/**
 * Created by Chaayos on 07-05-2016.
 */
public interface SCMUnitManagementDao extends SCMAbstractDao {

	public List<FulfillmentUnitMappingData> viewUnitsForAdhocRequest(String fulfillmentType, int unitId);

	public List<FulfillmentUnitMappingData> getUnitToFulfillmentTypeMapping(int unitId);

	public List<UnitDistanceMappingData> getUnitDistanceMapping(int sourceUnitId, int destinationUnitId);

	public List<UnitDistanceMappingData> updateUnitDistanceMapping(List<UnitDistanceMappingData> list) throws SumoException;


	List<BusinessCostCenterData> getBusinessCostCentresByAccountTypeAndCompanyId(int companyId, String accountType, String businessCostCentreCode);

    List<BusinessCostCenterData> getBusinessCostCentresByType(String businessCostCentreType);

	List<BusinessCostCenterData> getBusinessCostCentresByUnitId(String unitId);

	public List<Pair<Pair<Integer, String>, Date>> getActiveStockTakeEvent(int unitId);

	public List<Pair<AssetDefinitionData, String>> getAssetsToHandover(int unitId , int eventId);
}
