package com.stpl.tech.scm.notification.email.template;

import com.stpl.tech.scm.domain.model.ServiceReceive;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;

import java.util.HashMap;
import java.util.Map;

public class MeasurementBookTemplate  extends AbstractVelocityTemplate {

    private ServiceReceive serviceReceived;
    private String basePath;
    private VendorDetail vendorDetail;

    public MeasurementBookTemplate() {

    }

    public MeasurementBookTemplate(ServiceReceive serviceReceived , String basePath , VendorDetail vendorDetail) {
        this.serviceReceived= serviceReceived;
        this.basePath = basePath;
        this.vendorDetail = vendorDetail;
    }

    public VendorDetail getVendorDetail() {
        return this.vendorDetail;
    }

    @Override
    public String getTemplatePath() {
        return "templates/MeasurementBookTemplate.html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/" + "measurement-book" + "/" +
                + serviceReceived.getId() + ".xlsx";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("serviceReceive", serviceReceived);
        return stringObjectMap;
    }

}
