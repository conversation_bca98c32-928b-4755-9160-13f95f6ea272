package com.stpl.tech.scm.core.service.impl;

import com.google.common.base.Stopwatch;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.exception.NegativeStockException;
import com.stpl.tech.scm.core.exception.SCMError;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.GoodsReceiveManagementService;
import com.stpl.tech.scm.core.service.MappingCache;
import com.stpl.tech.scm.core.service.SCMAssetManagementService;
import com.stpl.tech.scm.core.service.SCMNotificationService;
import com.stpl.tech.scm.core.service.SCMReportingService;
import com.stpl.tech.scm.core.service.TransferOrderManagementService;
import com.stpl.tech.scm.core.service.WarehouseStockManagementService;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.core.util.model.AcknowledgeTransactionVO;
import com.stpl.tech.scm.core.util.model.BookingConsumptionDataVO;
import com.stpl.tech.scm.core.util.model.ProductionBookingDataVO;
import com.stpl.tech.scm.data.converter.SCMDataConverter;
import com.stpl.tech.scm.data.dao.PriceManagementDao;
import com.stpl.tech.scm.data.dao.ReferenceOrderManagementDao;
import com.stpl.tech.scm.data.dao.StockManagementDao;
import com.stpl.tech.scm.data.model.BookingConsumptionData;
import com.stpl.tech.scm.data.model.CostDetailData;
import com.stpl.tech.scm.data.model.DayCloseEventLogData;
import com.stpl.tech.scm.data.model.DayCloseInventoryDrillDown;
import com.stpl.tech.scm.data.model.DayCloseTxnEventDrillDownData;
import com.stpl.tech.scm.data.model.DayCloseTxnEventMapping;
import com.stpl.tech.scm.data.model.GatepassData;
import com.stpl.tech.scm.data.model.GatepassItemData;
import com.stpl.tech.scm.data.model.GoodsReceivedData;
import com.stpl.tech.scm.data.model.ProductStockDrillDownUpdateEvent;
import com.stpl.tech.scm.data.model.ProductionBookingData;
import com.stpl.tech.scm.data.model.RegularOrderUnitBrandData;
import com.stpl.tech.scm.data.model.RegularOrderingEvent;
import com.stpl.tech.scm.data.model.ReverseBookingConsumptionData;
import com.stpl.tech.scm.data.model.ReverseProductionBookingData;
import com.stpl.tech.scm.data.model.SCMDayCloseEventData;
import com.stpl.tech.scm.data.model.SCMWastageData;
import com.stpl.tech.scm.data.model.SCMWastageEventData;
import com.stpl.tech.scm.data.model.StockEventDefinitionData;
import com.stpl.tech.scm.data.model.VarianceAcknowledgementData;
import com.stpl.tech.scm.data.model.SalesPerformaDetailData;
import com.stpl.tech.scm.data.model.StockEntryEventData;
import com.stpl.tech.scm.data.model.TransferOrderData;
import com.stpl.tech.scm.data.model.UnitDetailData;
import com.stpl.tech.scm.data.model.VarianceAcknowledgementData;
import com.stpl.tech.scm.data.model.VendorGoodsReceivedData;
import com.stpl.tech.scm.data.model.WriteOffItemData;
import com.stpl.tech.scm.domain.model.DayCloseEvent;
import com.stpl.tech.scm.domain.model.DayCloseEventLog;
import com.stpl.tech.scm.domain.model.DayCloseEventLogType;
import com.stpl.tech.scm.domain.model.DayCloseTxnItem;
import com.stpl.tech.scm.domain.model.FixedAssetCompactDefinition;
import com.stpl.tech.scm.domain.model.FixedAssetDayCloseResponseObject;
import com.stpl.tech.scm.domain.model.GatepassTransType;
import com.stpl.tech.scm.domain.model.PackagingDefinition;
import com.stpl.tech.scm.domain.model.PriceUpdateEntryType;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.ProductStockForUnit;
import com.stpl.tech.scm.domain.model.PurchaseOrderStatus;
import com.stpl.tech.scm.domain.model.RegularOrderEvent;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import com.stpl.tech.scm.domain.model.SkuPackagingMapping;
import com.stpl.tech.scm.domain.model.SkuStockForUnit;
import com.stpl.tech.scm.domain.model.StockEventStatus;
import com.stpl.tech.scm.domain.model.StockEventStatusType;
import com.stpl.tech.scm.domain.model.StockEventType;
import com.stpl.tech.scm.domain.model.StockTakeSubType;
import com.stpl.tech.scm.domain.model.StockTakeType;
import com.stpl.tech.scm.domain.model.SwitchStatus;
import com.stpl.tech.scm.domain.model.VarianceVO;
import com.stpl.tech.scm.domain.model.WastageEventType;
import com.stpl.tech.scm.domain.model.Disclaimer;
import com.stpl.tech.scm.domain.model.DayCloseEvent;
import com.stpl.tech.scm.domain.model.DayCloseEventLog;
import com.stpl.tech.scm.domain.model.DayCloseEventLogType;
import com.stpl.tech.scm.domain.model.DayCloseTxnItem;
import com.stpl.tech.scm.domain.model.Disclaimer;
import com.stpl.tech.scm.domain.model.FixedAssetCompactDefinition;
import com.stpl.tech.scm.domain.model.FixedAssetDayCloseResponseObject;
import com.stpl.tech.scm.domain.model.GatepassTransType;
import com.stpl.tech.scm.domain.model.PackagingDefinition;
import com.stpl.tech.scm.domain.model.PriceUpdateEntryType;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.ProductStockForUnit;
import com.stpl.tech.scm.domain.model.PurchaseOrderStatus;
import com.stpl.tech.scm.domain.model.RegularOrderEvent;
import com.stpl.tech.scm.domain.model.SCMOrderStatus;
import com.stpl.tech.scm.domain.model.SalesPerformaType;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import com.stpl.tech.scm.domain.model.SkuPackagingMapping;
import com.stpl.tech.scm.domain.model.SkuStockForUnit;
import com.stpl.tech.scm.domain.model.StockEventStatus;
import com.stpl.tech.scm.domain.model.StockEventType;
import com.stpl.tech.scm.domain.model.StockTakeFrequencyEnum;
import com.stpl.tech.scm.domain.model.StockTakeSubType;
import com.stpl.tech.scm.domain.model.StockTakeType;
import com.stpl.tech.scm.domain.model.SwitchStatus;
import com.stpl.tech.scm.domain.model.VarianceVO;
import com.stpl.tech.scm.domain.model.WastageEventType;
import com.stpl.tech.util.AppUtils;
import org.apache.commons.math3.util.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Copyright (C) $today.year, Sunshine Teahouse Private Limited - All Rights
 * Reserved Unauthorized copying of this file, via any medium is strictly
 * prohibited Proprietary and confidential Created by shikhar on 13-06-2017.
 */
@Service
public class WarehouseStockManagementServiceImpl implements WarehouseStockManagementService {

    private static final Logger LOG = LoggerFactory.getLogger(WarehouseStockManagementServiceImpl.class);

    @Autowired
    private StockManagementDao stockManagementDao;

    @Autowired
    private PriceManagementDao priceManagementDao;

    @Autowired
    private SCMCache scmCache;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private StockCalculator stockCalculator;

    @Autowired
    private SCMReportingService reportingService;

    @Autowired
    private GoodsReceiveManagementService goodsReceiveManagementService;

    @Autowired
    private EnvProperties props;

    @Autowired
    private SCMNotificationService notificationService;

    @Autowired
    private MappingCache mappingCacheService;

    @Autowired
    private ReferenceOrderManagementDao referenceOrderManagementDao;

    @Autowired
    private TransferOrderManagementService transferOrderManagementService;

    @Autowired
    private SCMAssetManagementService scmAssetManagementService;



    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public DayCloseEvent initiateDayClose(int unitId, int userId , Boolean isAllListType , List<String> subCategories) throws SumoException {
        Date dayCloseDate = SCMUtil.getPreviousBusinessDate(SCMUtil.getCurrentBusinessDate());
        SCMDayCloseEventData dayCloseEventData = new SCMDayCloseEventData();
        dayCloseEventData.setUnitId(unitId);
        dayCloseEventData.setBusinessDate(dayCloseDate);
        dayCloseEventData.setDayCloseEventType(StockEventType.WH_CLOSING.name());
        dayCloseEventData.setStatus(StockEventStatus.INITIATED.name());
        if(Objects.nonNull(isAllListType) && isAllListType.equals(Boolean.TRUE)){
            dayCloseEventData.setEventFrequencyType(SCMUtil.FULL_AUDIT_DAY_CLOSE);
        }else {
            dayCloseEventData.setEventFrequencyType(SCMUtil.getRequiredFrequency(dayCloseDate).name());
        }
        dayCloseEventData.setGenerationTime(SCMUtil.getCurrentTimestamp());
        dayCloseEventData.setCreatedBy(userId);
        String subCategoriesString =  String.join(",",subCategories);
        dayCloseEventData.setSubCategories(subCategoriesString);
        stockManagementDao.add(dayCloseEventData, true);
        return convert(dayCloseEventData);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<Integer> settleRejectedPendingGR(int unitId, int userId) throws InventoryUpdateException, SumoException, ParseException, DataNotFoundException {
        List<GoodsReceivedData> rejectedGRList = goodsReceiveManagementService.getRejectedPendingGrs(unitId);
        List<Integer> grIdList = new ArrayList<>();
        for (GoodsReceivedData goodsReceivedData : rejectedGRList) {
            int seconds = SCMUtil.getSecondsDiff(goodsReceivedData.getGenerationTime(), SCMUtil.getCurrentTimestamp());
            if (seconds > (72 * 3600) && goodsReceivedData != null) {
                goodsReceivedData.setReceivedBy(userId);
                goodsReceiveManagementService.settleRejectedGoodsReceived(goodsReceivedData);
                grIdList.add(goodsReceivedData.getId());
            }
        }
        return grIdList;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean cancelDayCloseEvent(int eventId, int userId) {
        boolean flag = false;
        try {
            SCMDayCloseEventData dayCloseEventData = stockManagementDao.find(SCMDayCloseEventData.class, eventId);
            if (dayCloseEventData != null) {
                dayCloseEventData.setStatus(StockEventStatus.CANCELLED.name());
                dayCloseEventData.setUpdatedAt(SCMUtil.getCurrentTimestamp());
                dayCloseEventData.setUpdatedBy(userId);
                stockManagementDao.update(dayCloseEventData, true);
                flag = true;
            }
        } catch (Exception e) {
            LOG.error("Encountered error while cancelling day close event ::::::::", e);
        }
        return flag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public DayCloseEvent getActiveWarehouseDayCloseEvent(int unitId) {
        Unit unit = masterDataCache.getUnit(unitId);
        SCMDayCloseEventData event = null;
        if (SCMUtil.isWareHouse(unit.getFamily()) || SCMUtil.isKitchen(unit.getFamily())) {
            event = stockManagementDao.getActiveDayCloseEvent(unitId);
        }
        return convert(event);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<DayCloseTxnItem> getWastagesForClosing(int unitId, List<Integer> skuList) {
        List<SCMWastageEventData> scmWastageEventDatas = stockManagementDao.getWastagesForWHUnit(unitId, skuList);
        List<DayCloseTxnItem> items = new ArrayList<>();
        if (scmWastageEventDatas != null) {
            for (SCMWastageEventData waste : scmWastageEventDatas) {
                if (waste.getType().equals(WastageEventType.SKU.name())) {
                    for (SCMWastageData wastage : waste.getItems()) {
                        DayCloseTxnItem txnItem = new DayCloseTxnItem();
                        txnItem.setSkuId(wastage.getSku().getSkuId());
                        txnItem.setName(wastage.getSku().getSkuName());
                        txnItem.setQty(wastage.getQuantity());
                        txnItem.setCost(wastage.getCost());
                        txnItem.setPrice(SCMUtil.divide(wastage.getCost(), wastage.getQuantity()));
                        txnItem.setUom(wastage.getSku().getUnitOfMeasure());
                        txnItem.setEvent(waste.getWastageId());
                        txnItem.setStatus(waste.getStatus());
                        items.add(txnItem);
                    }
                }
            }
        }
        return items;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<DayCloseTxnItem> getTransfersForClosing(int unitId, List<Integer> skuList)
            throws DataUpdationException {
        List<TransferOrderData> transfers = stockManagementDao.getTransferItemsForWHUnit(unitId, skuList);
        List<DayCloseTxnItem> txnList = new ArrayList<>();
        if (transfers != null) {
            transfers.forEach(transferOrderData -> {
                DayCloseTxnItem txn = new DayCloseTxnItem();
                txn.setStatus(transferOrderData.getStatus());
                txn.setEvent(transferOrderData.getId());
                txn.setTime(transferOrderData.getGenerationTime());
                UnitBasicDetail unit = masterDataCache.getUnitBasicDetail(transferOrderData.getGeneratedForUnitId());
                txn.setDescription(unit != null ? unit.getName() : "Unit Name not found");
                List<DayCloseTxnItem> txnItems = transferOrderData.getTransferOrderItemDatas().stream()
                        .filter(itemData -> skuList == null || skuList.contains(itemData.getSkuId()))
                        .map(itemData -> {
                            DayCloseTxnItem item = new DayCloseTxnItem();
                            item.setSkuId(itemData.getSkuId());
                            item.setName(itemData.getSkuName());
                            item.setUom(itemData.getUnitOfMeasure());
                            item.setCost(itemData.getCalculatedAmount());
                            item.setQty(itemData.getTransferredQuantity());
                            item.setPrice(SCMUtil.divide(itemData.getCalculatedAmount(), itemData.getTransferredQuantity()));
                            return item;
                        }).collect(Collectors.toList());

                txn.getTxnItems().addAll(txnItems);
                Optional<BigDecimal> totalPrice = txn.getTxnItems().stream()
                        .map(DayCloseTxnItem::getCost)
                        .reduce(SCMUtil::add);
                txn.setCost(totalPrice.orElse(BigDecimal.ZERO));
                txnList.add(txn);
            });
        }
        /*
        addGatepassTransaction(unitId, skuList, txnList, GatepassTransType.TRANSFER.name());
        addSalesPerformaInvoice(unitId, skuList, txnList);*/
        return txnList;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<DayCloseTxnItem> getGatepasses(int unitId, List<Integer> skuList) {
        List<DayCloseTxnItem> txns = new ArrayList<>();
        addGatepassTransaction(unitId, skuList, txns, GatepassTransType.TRANSFER.name());
        return txns;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<DayCloseTxnItem> getGatepassReturns(int unitId, List<Integer> skuList) {
        List<DayCloseTxnItem> txns = new ArrayList<>();
        addGatepassTransaction(unitId, skuList, txns, GatepassTransType.RETURN.name());
        return txns;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<DayCloseTxnItem> getInvoices(int unitId, List<Integer> skuList) {
        List<DayCloseTxnItem> txns = new ArrayList<>();
        addSalesPerformaInvoice(unitId, skuList, txns,false);
        return txns;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<String,List<DayCloseTxnItem>> getInvoicesMap(int unitId, List<Integer> skuList) {
        Map<String,List<DayCloseTxnItem>> map = new HashMap<>();
        map.put(SalesPerformaType.B2B_RETURN.name(),new ArrayList<>());
        map.put(SalesPerformaType.B2B_SALES.name(),new ArrayList<>());
        addSalesPerformaInvoice(unitId, skuList, map);
        return map;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean ifAlreadyUpdated(DayCloseEventLogType type, int event) {
        return stockManagementDao.findByClosureEvent(event, type) != null;
    }

    private void addSalesPerformaInvoice(int unitId, List<Integer> skuList, List<DayCloseTxnItem> txnList, boolean isReturn) {
        List<SalesPerformaDetailData> invoices = stockManagementDao.getInvoicesForWhUnit(unitId, skuList);
        if (invoices != null) {
            for (SalesPerformaDetailData salesInvoice : invoices) {
                DayCloseTxnItem txn = new DayCloseTxnItem();
                txn.setStatus(salesInvoice.getStatus());
                txn.setTime(salesInvoice.getCreatedAt());
                txn.setEvent(salesInvoice.getInvoiceId());
                UnitBasicDetail unit = masterDataCache.getUnitBasicDetail(salesInvoice.getSendingUnit());
                txn.setDescription(unit != null ? unit.getName() : "Unit Name not found");
                List<DayCloseTxnItem> txnItems = salesInvoice.getInvoiceItems().stream()
                        .filter(itemData -> skuList == null || skuList.contains(itemData.getSkuId()))
                        .map(itemData -> {
                            DayCloseTxnItem item = new DayCloseTxnItem();
                            item.setSkuId(itemData.getSkuId());
                            item.setName(scmCache.getSkuDefinition(item.getSkuId()).getSkuName());
                            item.setUom(itemData.getUnitOfMeasure());
                            item.setCost(itemData.getSellingAmount());
                            item.setQty(itemData.getQuantity());
                            item.setPrice(itemData.getSellingPrice());
                            return item;
                        }).collect(Collectors.toList());

                txn.getTxnItems().addAll(txnItems);
                Optional<BigDecimal> totalPrice = txn.getTxnItems().stream()
                        .map(DayCloseTxnItem::getCost)
                        .reduce(SCMUtil::add);
                txn.setCost(totalPrice.orElse(BigDecimal.ZERO));
                txn.setTxnType(salesInvoice.getType());
                if (!SalesPerformaType.B2B_RETURN.name().equalsIgnoreCase(salesInvoice.getType()) && !isReturn){
                    txnList.add(txn);
                }
                if (isReturn && SalesPerformaType.B2B_RETURN.name().equalsIgnoreCase(salesInvoice.getType())) {
                    txnList.add(txn);
                }
            }
        }
    }

    private void addSalesPerformaInvoice(int unitId, List<Integer> skuList, Map<String,List<DayCloseTxnItem>> map) {
        List<SalesPerformaDetailData> invoices = stockManagementDao.getInvoicesForWhUnit(unitId, skuList);
        List<DayCloseTxnItem> b2bSales = new ArrayList<>();
        List<DayCloseTxnItem> b2bReturn = new ArrayList<>();
        if (invoices != null) {
            for (SalesPerformaDetailData salesInvoice : invoices) {
                DayCloseTxnItem txn = new DayCloseTxnItem();
                txn.setStatus(salesInvoice.getStatus());
                txn.setTime(salesInvoice.getCreatedAt());
                txn.setEvent(salesInvoice.getInvoiceId());
                UnitBasicDetail unit = masterDataCache.getUnitBasicDetail(salesInvoice.getSendingUnit());
                txn.setDescription(unit != null ? unit.getName() : "Unit Name not found");
                List<DayCloseTxnItem> txnItems = salesInvoice.getInvoiceItems().stream()
                        .filter(itemData -> skuList == null || skuList.contains(itemData.getSkuId()))
                        .map(itemData -> {
                            DayCloseTxnItem item = new DayCloseTxnItem();
                            item.setSkuId(itemData.getSkuId());
                            item.setName(scmCache.getSkuDefinition(item.getSkuId()).getSkuName());
                            item.setUom(itemData.getUnitOfMeasure());
                            item.setCost(itemData.getSellingAmount());
                            item.setQty(itemData.getQuantity());
                            item.setPrice(itemData.getSellingPrice());
                            return item;
                        }).collect(Collectors.toList());

                txn.getTxnItems().addAll(txnItems);
                Optional<BigDecimal> totalPrice = txn.getTxnItems().stream()
                        .map(DayCloseTxnItem::getCost)
                        .reduce(SCMUtil::add);
                txn.setCost(totalPrice.orElse(BigDecimal.ZERO));
                if (SalesPerformaType.B2B_RETURN.name().equalsIgnoreCase(salesInvoice.getType())) {
                    b2bReturn.add(txn);
                } else {
                    b2bSales.add(txn);
                }
            }
        }
        map.put(SalesPerformaType.B2B_SALES.name(), b2bSales);
        map.put(SalesPerformaType.B2B_RETURN.name(), b2bReturn);
    }

    @Deprecated
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<DayCloseTxnItem> getBookingsForClosing(int unitId, List<Integer> skuList) {
        List<ProductionBookingData> bookings = stockManagementDao.getProductionBookingsForWHUnit(unitId, skuList);
        List<DayCloseTxnItem> txnList = new ArrayList<>();
        if (bookings != null) {
            bookings.forEach(booking -> {
                DayCloseTxnItem txn = new DayCloseTxnItem();
                txn.setStatus(booking.getBookingStatus());
                txn.setName(booking.getProductName());
                txn.setSkuId(booking.getSkuId());
                txn.setPrice(booking.getUnitPrice());
                txn.setQty(booking.getQuantity());
                txn.setEvent(booking.getBookingId());
                txn.setTime(booking.getGenerationTime());
                txn.getTxnItems().addAll(convertConsumptionForBooking(booking.getConsumption()));
                Optional<BigDecimal> totalPrice = txn.getTxnItems().stream()
                        .map(DayCloseTxnItem::getCost)
                        .reduce(SCMUtil::add);
                txn.setCost(totalPrice.orElse(BigDecimal.ZERO));
                txn.setPrice(SCMUtil.divide(txn.getCost(), txn.getQty()));
                txnList.add(txn);
            });
        }
        return txnList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public AcknowledgeTransactionVO  getBookingsSummaryForClosing(int unitId, List<Integer> skuList, boolean loadDrilldowns) {
        AcknowledgeTransactionVO acknowledgeTransactionVO = new AcknowledgeTransactionVO();
        List<Integer> bookingIds = stockManagementDao.getProductionBookingsIdsForWHUnit(unitId, skuList);
        Collection<ProductionBookingDataVO> bookings = stockManagementDao.getProductionBookingsAggregateForWHUnit(unitId, bookingIds, loadDrilldowns);
        return getAcknowledgeTransactionVO(acknowledgeTransactionVO, bookingIds, bookings);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public AcknowledgeTransactionVO  getReverseBookingsSummaryForClosing(int unitId, List<Integer> skuList, boolean loadDrilldowns) {
        AcknowledgeTransactionVO acknowledgeTransactionVO = new AcknowledgeTransactionVO();
        List<Integer> bookingIds = stockManagementDao.getReverseProductionBookingsIdsForWHUnit(unitId, skuList);
        Collection<ProductionBookingDataVO> bookings = stockManagementDao.getReserveProductionBookingsAggregateForWHUnit(unitId, bookingIds, loadDrilldowns);
        return getAcknowledgeTransactionVO(acknowledgeTransactionVO, bookingIds, bookings);
    }

    private AcknowledgeTransactionVO getAcknowledgeTransactionVO(AcknowledgeTransactionVO acknowledgeTransactionVO, List<Integer> bookingIds, Collection<ProductionBookingDataVO> bookings) {
        List<DayCloseTxnItem> txnList = new ArrayList<>();
        if (bookings != null) {
            bookings.forEach(booking -> {
                DayCloseTxnItem txn = new DayCloseTxnItem();
                txn.setName(booking.getProductName());
                txn.setSkuId(booking.getSkuId());
                txn.setPrice(booking.getUnitPrice());
                txn.setQty(booking.getQuantity());
                txn.getTxnItems().addAll(convertConsumptionForBookingVO(booking.getConsumption()));
                Optional<BigDecimal> totalPrice = txn.getTxnItems().stream()
                        .map(DayCloseTxnItem::getCost)
                        .reduce(SCMUtil::add);
                txn.setCost(totalPrice.orElse(BigDecimal.ZERO));
                txn.setPrice(SCMUtil.divide(txn.getCost(), txn.getQty()));
                txnList.add(txn);
            });
            acknowledgeTransactionVO.setList(txnList);
            acknowledgeTransactionVO.setEvents(bookingIds);
        }
        return acknowledgeTransactionVO;
    }

    @Override
	@Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public List<DayCloseTxnItem> getBookingsForClosingByBookingIds(int unitId, List<Integer> bookingIds, boolean isBooking) {
		List<DayCloseTxnItem> txnList = new ArrayList<>();
        if (isBooking) {
            Collection<ProductionBookingData> bookings = stockManagementDao.getProductionBookingsDetailsForWHUnit(unitId,
                    bookingIds);
            if (bookings != null) {
                bookings.forEach(booking -> {
                    DayCloseTxnItem txn = new DayCloseTxnItem();
                    txn.setStatus(booking.getBookingStatus());
                    txn.setName(booking.getProductName());
                    txn.setSkuId(booking.getSkuId());
                    txn.setPrice(booking.getUnitPrice());
                    txn.setQty(booking.getQuantity());
                    txn.setEvent(booking.getBookingId());
                    txn.setTime(booking.getGenerationTime());
                    txn.getTxnItems().addAll(convertConsumptionForBooking(booking.getConsumption()));
                    Optional<BigDecimal> totalPrice = txn.getTxnItems().stream().map(DayCloseTxnItem::getCost)
                            .reduce(SCMUtil::add);
                    txn.setCost(totalPrice.orElse(BigDecimal.ZERO));
                    txn.setPrice(SCMUtil.divide(txn.getCost(), txn.getQty()));
                    txnList.add(txn);
                });
            }
        } else {
            Collection<ReverseProductionBookingData> reverseBookings = stockManagementDao.getReverseProductionBookingsDetailsForWHUnit(unitId,
                    bookingIds);
            if (reverseBookings != null) {
                reverseBookings.forEach(booking -> {
                    DayCloseTxnItem txn = new DayCloseTxnItem();
                    txn.setStatus(booking.getBookingStatus());
                    txn.setName(booking.getProductName());
                    txn.setSkuId(booking.getSkuId());
                    txn.setPrice(booking.getUnitPrice());
                    txn.setQty(booking.getQuantity());
                    txn.setEvent(booking.getBookingId());
                    txn.setTime(booking.getGenerationTime());
                    txn.getTxnItems().addAll(convertReverseConsumptionForBooking(booking.getConsumption()));
                    Optional<BigDecimal> totalPrice = txn.getTxnItems().stream().map(DayCloseTxnItem::getCost)
                            .reduce(SCMUtil::add);
                    txn.setCost(totalPrice.orElse(BigDecimal.ZERO));
                    txn.setPrice(SCMUtil.divide(txn.getCost(), txn.getQty()));
                    txnList.add(txn);
                });
            }
        }
		return txnList;
	}

    @Deprecated
    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<DayCloseTxnItem> getBookingConsumptions(int unitId, List<Integer> skuList) {
        List<BookingConsumptionData> bookingConsumptions = stockManagementDao.getBookingConsumptions(unitId, skuList);
        List<DayCloseTxnItem> txnList = new ArrayList<>();
        if (bookingConsumptions != null) {
            txnList = convertConsumptionForBooking(bookingConsumptions);
        }
        return txnList;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public AcknowledgeTransactionVO getBookingConsumptionsSummary(int unitId, List<Integer> skuList) {
        AcknowledgeTransactionVO acknowledgeTransactionVO = new AcknowledgeTransactionVO();
        List<Integer> bookingIds = stockManagementDao.getProductionBookingsIdsForWHUnit(unitId, skuList);
        List<Integer> bookingConsumptionIds = stockManagementDao.getBookingConsumptionsIds(unitId, skuList);
        List<BookingConsumptionDataVO> bookingConsumptions = stockManagementDao.getBookingConsumptionsAggregate(unitId, bookingConsumptionIds);
        List<DayCloseTxnItem> txnList = new ArrayList<>();
        if (bookingConsumptions != null) {
            txnList = convertConsumptionForBookingVO(bookingConsumptions);
            acknowledgeTransactionVO.setList(txnList);
        }
        if(bookingIds != null){
            acknowledgeTransactionVO.setEvents(bookingIds);
        }
        return acknowledgeTransactionVO;
    }

    public AcknowledgeTransactionVO getReverseBookingConsumptionsSummary(int unitId, List<Integer> skuList) {
        AcknowledgeTransactionVO acknowledgeTransactionVO = new AcknowledgeTransactionVO();
        List<Integer> bookingIds = stockManagementDao.getReverseProductionBookingsIdsForWHUnit(unitId, skuList);
        List<Integer> bookingConsumptionIds = stockManagementDao.getReverseBookingConsumptionsIds(unitId, skuList);
        List<BookingConsumptionDataVO> bookingConsumptions = stockManagementDao.getReverseBookingConsumptionsAggregate(unitId, bookingConsumptionIds);
        List<DayCloseTxnItem> txnList;
        if (bookingConsumptions != null) {
            txnList = convertConsumptionForBookingVO(bookingConsumptions);
            acknowledgeTransactionVO.setList(txnList);
        }
        if(bookingIds != null){
            acknowledgeTransactionVO.setEvents(bookingIds);
        }
        return acknowledgeTransactionVO;
    }

    private List<DayCloseTxnItem> convertConsumptionForBooking(List<BookingConsumptionData> consumptionDataList) {
        return consumptionDataList.stream()
                .map(itemData -> {
                    DayCloseTxnItem item = new DayCloseTxnItem();
                    item.setSkuId(itemData.getSkuId());
                    item.setName(itemData.getSkuName());
                    item.setCost(itemData.getTotalCost());
                    item.setQty(itemData.getCalculatedQuantity());
                    item.setPrice(SCMUtil.divide(item.getCost(), item.getQty()));
                    item.setUom(itemData.getUnitOfMeasure());
                    return item;
                }).collect(Collectors.toList());
    }

    private List<DayCloseTxnItem> convertReverseConsumptionForBooking(List<ReverseBookingConsumptionData> consumptionDataList) {
        return consumptionDataList.stream()
                .map(itemData -> {
                    DayCloseTxnItem item = new DayCloseTxnItem();
                    item.setSkuId(itemData.getSkuId());
                    item.setName(itemData.getSkuName());
                    item.setCost(itemData.getTotalCost());
                    item.setQty(itemData.getCalculatedQuantity());
                    item.setPrice(SCMUtil.divide(item.getCost(), item.getQty()));
                    item.setUom(itemData.getUnitOfMeasure());
                    return item;
                }).collect(Collectors.toList());
    }

    private List<DayCloseTxnItem> convertConsumptionForBookingVO(List<BookingConsumptionDataVO> consumptionDataList) {
        return consumptionDataList.stream()
                .map(itemData -> {
                    DayCloseTxnItem item = new DayCloseTxnItem();
                    item.setSkuId(itemData.getSkuId());
                    item.setName(itemData.getSkuName());
                    item.setCost(itemData.getTotalCost());
                    item.setQty(itemData.getCalculatedQuantity());
                    item.setPrice(SCMUtil.divide(item.getCost(), item.getQty()));
                    item.setUom(itemData.getUnitOfMeasure());
                    return item;
                }).collect(Collectors.toList());
    }

    @Override
    public List<SkuDefinition> filterBySubCategory(List<SkuDefinition> taggedInventoryListSkus , List<Integer> subCategoryIds){
        if(Objects.isNull(subCategoryIds) || subCategoryIds.isEmpty()){
            return taggedInventoryListSkus;
        }
        return taggedInventoryListSkus.stream().filter(sku -> subCategoryIds.contains(scmCache.getProductDefinition(sku.getLinkedProduct().getId())
                .getSubCategoryDefinition().getId())).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<SkuStockForUnit> getInventoryForClosing(int unitId , Boolean isALlListType , List<Integer> subCategoryIds) throws DataUpdationException {

        Map<Integer,Integer> inventoryListWithMappedSku=stockManagementDao.getMappedSkuWithInventoryList(unitId);
        List<Integer> mappedSKUs = inventoryListWithMappedSku.keySet().stream().collect(Collectors.toList());
        Map<Integer, ProductStockForUnit> currentInventory = getInventorySummary(mappedSKUs, unitId).stream()
                .collect(Collectors.toMap(ProductStockForUnit::getSkuId, Function.identity()));
        List<SkuStockForUnit> inventoryList = filterBySubCategory(stockManagementDao.getTaggedInventoryListForWarehouse(unitId,inventoryListWithMappedSku,isALlListType),subCategoryIds)
                .stream().filter(skuDefinition -> currentInventory.containsKey(skuDefinition.getSkuId())
                        && skuDefinition.getSkuStatus().equals(SwitchStatus.ACTIVE)
                        && mappedSKUs.contains(skuDefinition.getSkuId()))
                .map(sku -> {
                    SkuStockForUnit item = new SkuStockForUnit(currentInventory.get(sku.getSkuId()));
                    item.setName(sku.getSkuName());
                    List<SkuPackagingMapping> skuPkgs = scmCache.getSkuPackagingMappings(item.getSkuId());
                    List<PackagingDefinition> packagings = new ArrayList<>();
                    if (skuPkgs != null && !skuPkgs.isEmpty()) {
                        packagings = skuPkgs.stream()
                                .map(mapping -> scmCache.getPackagingDefinition(mapping.getPackagingId()))
                                .collect(Collectors.toList());
                    }
                    if (packagings.isEmpty()) {
                        String message = "Packaging mappings not found in cache for given SKU " + sku.getSkuId();
                        notificationService.sendDayClosureAlarmNotification(unitId, message);
                    }
                    item.getPackagings().addAll(packagings);
                    return item;
                }).collect(Collectors.toList());
        return inventoryList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<DayCloseTxnItem> getReceivingsForClosing(int unitId, List<Integer> skuList)
            throws DataUpdationException {
        List<DayCloseTxnItem> txnList = new ArrayList<>();
        List<VendorGoodsReceivedData> vendorGRs = stockManagementDao.getVendorGRItemsForWHUnit(unitId, skuList);
        if (vendorGRs != null) {
            vendorGRs.forEach(vendorGR -> {
                DayCloseTxnItem txn = new DayCloseTxnItem();
                txn.setStatus(vendorGR.getGrStatus());
                txn.setEvent(vendorGR.getGoodsReceivedId());
                txn.setTime(vendorGR.getCreatedAt());
                String desc = scmCache.getVendorDetail(vendorGR.getGeneratedForVendor()).getEntityName();
                txn.setDescription(desc);

                txn.getTxnItems()
                        .addAll(vendorGR.getGrItemList().stream()
                                .filter(itemData -> skuList == null || skuList.contains(itemData.getSkuId()))
                                .map(itemData -> {
                                    DayCloseTxnItem item = new DayCloseTxnItem();
                                    item.setSkuId(itemData.getSkuId());
                                    item.setName(itemData.getSkuName());
                                    item.setCost(SCMUtil.multiply(itemData.getReceivedQty(), itemData.getUnitPrice()));
                                    item.setPrice(itemData.getUnitPrice());
                                    item.setQty(itemData.getReceivedQty());
                                    item.setUom(itemData.getUnitOfMeasure());
                                    return item;
                                }).collect(Collectors.toList()));
                Optional<BigDecimal> totalPrice = txn.getTxnItems().stream()
                        .map(DayCloseTxnItem::getCost)
                        .reduce(SCMUtil::add);
                txn.setCost(totalPrice.orElse(BigDecimal.ZERO));
                txn.setExternal(true);
                txnList.add(txn);
            });
        }

        List<GoodsReceivedData> internalGRs = stockManagementDao.getGRsForWHUnit(unitId, null);
        if (internalGRs != null) {
            internalGRs.forEach(internalGR -> {
                DayCloseTxnItem txn = new DayCloseTxnItem();
                txn.setStatus(internalGR.getStatus());
                txn.setEvent(internalGR.getId());
                txn.setTime(internalGR.getGenerationTime());
                String desc = masterDataCache.getUnitBasicDetail(internalGR.getGeneratedForUnitId()).getName();
                txn.setDescription(desc);
                txn.getTxnItems()
                        .addAll(internalGR.getGoodsReceivedItemDatas().stream()
                                .filter(itemData -> skuList == null || skuList.contains(itemData.getSkuId()))
                                .map(itemData -> {
                                    DayCloseTxnItem item = new DayCloseTxnItem();
                                    item.setSkuId(itemData.getSkuId());
                                    item.setName(itemData.getSkuName());
                                    item.setPrice(itemData.getUnitPrice());
                                    item.setCost(itemData.getCalculatedAmount());
                                    item.setQty(itemData.getReceivedQuantity());
                                    item.setUom(itemData.getUnitOfMeasure());
                                    return item;
                                }).collect(Collectors.toList()));
                Optional<BigDecimal> totalPrice = txn.getTxnItems().stream()
                        .map(DayCloseTxnItem::getCost)
                        .reduce(SCMUtil::add);
                txn.setCost(totalPrice.orElse(BigDecimal.ZERO));
                txnList.add(txn);
            });

            addSalesPerformaInvoice(unitId, skuList, txnList,true);
        }
        return txnList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean acknowledgeTransactions(int userId, int eventId, DayCloseEventLogType type,
                                           List<DayCloseTxnItem> events) throws DataUpdationException {
        boolean flag = false;
        SCMDayCloseEventData dayCloseEvent = stockManagementDao.find(SCMDayCloseEventData.class, eventId);
        Map<String, DayCloseTxnItem> txnList = new HashMap<>();
        Set<Integer> internalEventList = new HashSet<>();
        Set<Integer> externalEventList = new HashSet<>();
        Set<Integer> returnInvoiceList = new HashSet<>();
        List<DayCloseEventLogType> topLevelEventTypes = Arrays.asList(DayCloseEventLogType.GATEPASS,
                DayCloseEventLogType.WASTAGES,
                DayCloseEventLogType.GATEPASS_RETURN);

        if (events != null) {
            events.forEach(event -> {
                if (!topLevelEventTypes.contains(type)) {
                    event.getTxnItems().forEach(item ->
                            addToTxnList(txnList, item.getSkuId(), item.getName(), item.getPrice(), item.getQty()));
                } else {
                    addToTxnList(txnList, event.getSkuId(), event.getName(), event.getPrice(), event.getQty()); // since
                }

                if(type.equals(DayCloseEventLogType.RECEIVINGS) && SalesPerformaType.B2B_RETURN.name().equals(event.getTxnType())){
                    returnInvoiceList.add(event.getEvent());
                }else if (event.isExternal()){
                    externalEventList.add(event.getEvent());
                } else {
                    internalEventList.add(event.getEvent());
                }
            });
            saveTxnEvents(externalEventList, dayCloseEvent, type);
            saveInternalTxnEvents(internalEventList, dayCloseEvent, type);
            if(!CollectionUtils.isEmpty(returnInvoiceList)){
                saveReverseInvoiceTxnEvents(returnInvoiceList,dayCloseEvent);
            }
            saveTransactions(txnList, dayCloseEvent, type);
            saveEventLog(dayCloseEvent, type, userId);
            flag = true;
        }
        return flag;
    }

    private void saveTxnEvents(Set<Integer> externalEventList, SCMDayCloseEventData dayCloseEvent,
                               DayCloseEventLogType type) throws DataUpdationException {
        Map<Integer, String> events = externalEventList.stream()
                .collect(Collectors.toMap(txnId -> txnId, txnId -> SCMServiceConstants.SCM_CONSTANT_YES));
        saveTransactionEvents(events, dayCloseEvent, type);
    }

    private void addGatepassTransaction(int unitId, List<Integer> skuList, List<DayCloseTxnItem> txnList, String transType) {
        List<GatepassItemData> gatepasses = stockManagementDao.getGatepassItemsForWHUnit(unitId, skuList, transType);
        if (gatepasses != null) {
            for (GatepassItemData itemData : gatepasses) {
                DayCloseTxnItem txn = new DayCloseTxnItem();
                GatepassData gatepassData = itemData.getGatepassData();
                txn.setStatus(gatepassData.getStatus());
                txn.setTime(gatepassData.getCreatedAt());
                txn.setEvent(gatepassData.getId());
                UnitBasicDetail unit = masterDataCache.getUnitBasicDetail(gatepassData.getSendingUnit());
                txn.setDescription(unit != null ? unit.getName() : "Unit Name not found");
                txn.setEvent(itemData.getId());
                txn.setSkuId(itemData.getSkuId());
                txn.setName(scmCache.getSkuDefinition(txn.getSkuId()).getSkuName());
                txn.setUom(itemData.getUom());
                txn.setCost(itemData.getCost());
                txn.setQty(itemData.getQuantity());
                txn.setPrice(itemData.getPrice());
                txnList.add(txn);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean acknowledgeBookings(int userId, int eventId, DayCloseEventLogType type, List<DayCloseTxnItem> events, boolean isBooking)
            throws DataUpdationException {
        boolean flag = false;
        SCMDayCloseEventData dayCloseEvent = stockManagementDao.find(SCMDayCloseEventData.class, eventId);
        Map<String, DayCloseTxnItem> txnList = new HashMap<>();
        Map<String, DayCloseTxnItem> bookingMap = new HashMap<>();
        Set<Integer> eventList = new HashSet<>();
        if (events != null) {
            events.forEach(event -> {
                Integer skuId = event.getSkuId();
                addToTxnList(bookingMap, skuId, event.getName(), event.getPrice(), event.getQty());
                event.getTxnItems().stream().forEach(eventItem -> {
                    addToTxnList(txnList, eventItem.getSkuId(), eventItem.getName(), eventItem.getPrice(),
                            eventItem.getQty());
                });
                eventList.add(event.getEvent());
            });
            saveInternalTxnEvents(eventList, dayCloseEvent, isBooking ? DayCloseEventLogType.BOOKINGS : DayCloseEventLogType.REVERSE_BOOKING);
            saveTransactions(bookingMap, dayCloseEvent, isBooking ? DayCloseEventLogType.BOOKINGS : DayCloseEventLogType.REVERSE_BOOKING);
            saveTransactions(txnList, dayCloseEvent, isBooking ? DayCloseEventLogType.CONSUMPTION : DayCloseEventLogType.REVERSE_CONSUMPTION);
            saveEventLog(dayCloseEvent, type, userId);
            flag = true;
        }
        return flag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<ProductStockForUnit> submitInventory(int userId, int eventId, List<DayCloseTxnItem> items , Boolean isPreview)
            throws DataUpdationException, InventoryUpdateException, SumoException {
        List<ProductStockForUnit> stock = null;
        SCMDayCloseEventData eventData = stockManagementDao.find(SCMDayCloseEventData.class, eventId);
        if (eventData != null) {
            if(eventData.getStatus().equals(SCMOrderStatus.CANCELLED.name())){
                throw new InventoryUpdateException(new SCMError("Update Failed", "Day Close Already cancelled", 702));
            }
            DayCloseEventLogData inventory = stockManagementDao.findByClosureEvent(eventData.getEventId(), DayCloseEventLogType.INVENTORY);
            if (inventory != null) {
                throw new InventoryUpdateException(new SCMError("Update Failed", "Inventory Already updated", 702));
            }
            Map<Integer, DayCloseTxnItem> itemMap = items.stream()
                    .collect(Collectors.toMap(DayCloseTxnItem::getSkuId, dayCloseTxnItem -> dayCloseTxnItem));
            List<StockEntryEventData> openingStockData = getOpeningValues(eventData,isPreview); // full inventory
            Map<Integer, BigDecimal> openingValues = new HashMap<>();

            List<Integer> skuList = openingStockData.stream().mapToInt(StockEntryEventData::getSkuId)
                    .boxed().collect(Collectors.toList());
            List<CostDetailData> currentPriceList = goodsReceiveManagementService.getPriceDao()
                    .getCurrentPrices(PriceUpdateEntryType.SKU, eventData.getUnitId(), skuList, true);

            if (currentPriceList == null) {
                throw new InventoryUpdateException("No prices found for the products. Please verify if all the prices have been updated");
            } else {
                Map<Integer, CostDetailData> currentPriceMap = null;
                try {
                    currentPriceMap = currentPriceList.stream()
                            .collect(Collectors.toMap(CostDetailData::getKeyId, Function.identity()));
                } catch (Exception e) {
                    try {
                        currentPriceMap = fixPricing(currentPriceList, skuList).stream()
                                .collect(Collectors.toMap(CostDetailData::getKeyId, Function.identity()));
                    } catch (Exception e1) {
                        LOG.error("Error while fixing pricing :::: ", e);
                        String message = "Error while fixing prices for duplicate entries for pricing";
                        List<Integer> costIds = currentPriceList.stream()
                                .mapToInt(CostDetailData::getCostDetailDataId)
                                .boxed().collect(Collectors.toList());
                        message = message + "\n" + costIds;
                        notificationService.sendDayClosureAlarmNotification(eventData.getUnitId(), message);
                    }
                }

                if (currentPriceMap != null) {
                    List<StockEntryEventData> currentStock = new ArrayList<>();
                    for (StockEntryEventData stockEntry : openingStockData) {
                        Integer skuId = stockEntry.getSkuId();
                        ProductDefinition productDefinition = scmCache.getProductDefinition(scmCache.getSkuDefinition(skuId).getLinkedProduct().getId());
                        if(productDefinition.getCategoryDefinition().getId().equals(SCMServiceConstants.CATEGORY_FIXED_ASSETS)){
                            continue;
                        }
                        CostDetailData currentPrice = currentPriceMap.get(skuId);
                        DayCloseTxnItem txnItem = itemMap.get(skuId);
                        if (currentPrice == null) {
                            LOG.error(getErrorMessage(skuId, stockEntry.getUnitId()));
                        }

                        //set opening values for the unit
                        openingValues.put(skuId, SCMUtil.convertToBigDecimal(stockEntry.getCurrentStock()));
                        if (txnItem != null) {
                            stockEntry.setCurrentStock(SCMUtil.convertToBigDecimal(txnItem.getQty()));
                            stockEntry.setCurrentPrice(getPrice(skuId, currentPrice));
                            if(isPreview.equals(Boolean.FALSE)){
                                stockEntry = stockManagementDao.update(stockEntry, false);
                            }

                        }
                        currentStock.add(stockEntry);// updating current
                    }
                    if(isPreview.equals(Boolean.FALSE)){
                        stockManagementDao.flush();
                    }
                    stock = getConsumptionRecords(openingValues, currentPriceMap, currentStock, eventData, itemMap.keySet(), itemMap, isPreview);

                    if(Objects.nonNull(stock) && Objects.nonNull(masterDataCache) && Objects.nonNull(masterDataCache.getUnit(eventData.getUnitId())) && !isPreview){
                        String ackReq = null;
                        ackReq = masterDataCache.getUnit(eventData.getUnitId()).getVarianceAcknowledgementRequired();
                        if(Objects.nonNull(ackReq) && ackReq.equals(SCMUtil.YES)){
                            calculateAcknowledgementData(stock,eventData,eventData.getEventFrequencyType());
                        }
                    }
                    if (stock != null && isPreview.equals(Boolean.FALSE)) {
                        DayCloseEventLogData eventLog = saveEventLog(eventData, DayCloseEventLogType.INVENTORY, userId);
                        addInventoryWriteOff(items, eventLog);
                    }
                    if(isPreview.equals(Boolean.FALSE)){
                        stockManagementDao.flush();
                    }
                } else {
                    throw new InventoryUpdateException("Error while fetching unique entries for prices for the given SKU List");
                }
            }
        }
        return stock;
    }

    private void calculateAcknowledgementData(List<ProductStockForUnit> productStockForUnitList, SCMDayCloseEventData dayCloseEvent,String stockType) throws SumoException {

        try {
            VarianceAcknowledgementData vad = new VarianceAcknowledgementData();
            vad.setUnitId(dayCloseEvent.getUnitId());
            vad.setBusinessDate(dayCloseEvent.getBusinessDate());
            vad.setFrequency(stockType);
            vad.setGenerationTime(SCMUtil.getCurrentTimestamp());
            vad.setAcknowledged(SCMUtil.NO);
            vad.setAcknowledgementType(String.valueOf(StockTakeType.DAILY));
            vad.setAcknowledgementRequired(SCMUtil.YES);
            vad.setCurrentDayCloseEvent(dayCloseEvent);

            BigDecimal totalInventoryCost = BigDecimal.ZERO;
            BigDecimal totalVarianceCost = BigDecimal.ZERO;

            for (ProductStockForUnit productStockForUnit : productStockForUnitList) {
                BigDecimal productCost = BigDecimal.ZERO;
                BigDecimal varianceCost = BigDecimal.ZERO;
                if(Objects.nonNull(productStockForUnit.getUnitPrice())){
                    productCost = SCMUtil.multiplyWithScale(productStockForUnit.getUnitPrice(), productStockForUnit.getOpening(), 6);
                    varianceCost = SCMUtil.multiplyWithScale(productStockForUnit.getUnitPrice(), productStockForUnit.getVariance(), 6);
                    totalInventoryCost = SCMUtil.add(totalInventoryCost, productCost);
                    totalVarianceCost = SCMUtil.add(totalVarianceCost, varianceCost);
                }
            }
            BigDecimal variancePercentage = SCMUtil.multiplyWithScale(SCMUtil.divideWithScale(totalVarianceCost, totalInventoryCost, 6), BigDecimal.valueOf(100), 6);

//            if(Objects.nonNull(variancePercentage) && (variancePercentage.compareTo(BigDecimal.valueOf(1)) > 0 || variancePercentage.compareTo(BigDecimal.valueOf(-1))<0)){
//                    vad.setAcknowledgementRequired(SCMUtil.YES);
//            }
            vad.setVarianceCost(totalVarianceCost);
            vad.setInventoryCost(totalInventoryCost);
            vad.setVariancePercentage(variancePercentage);
            stockManagementDao.add(vad, false);
        }catch (Exception e){
            LOG.error("Exception Occurred While adding variance acknowledgement data ::: ",e);
        }
    }


    private List<CostDetailData> fixPricing(List<CostDetailData> currentPriceList, List<Integer> skuList) {
        currentPriceList = goodsReceiveManagementService.getPriceDao()
                .fixPricing(currentPriceList);
        return currentPriceList;
    }

    private BigDecimal getPrice(Integer skuId, CostDetailData currentPrice) {
        BigDecimal price;
        if (currentPrice == null) {
            SkuDefinition skuDefinition = scmCache.getSkuDefinition(skuId);
            price = SCMUtil.convertToBigDecimal(skuDefinition.getUnitPrice());
        } else {
            price = currentPrice.getPrice();
        }
        return price;
    }

    private String getErrorMessage(Integer skuId, Integer unitId) {
        String skuName = scmCache.getSkuDefinition(skuId).getSkuName();
        String unitName = scmCache.getUnitDetail(unitId).getUnitName();
        return String.format(String.format("No price found for SKU %s on Unit %s", skuName, unitName));
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean correctInventory(int userId, Integer eventId, List<ProductStockForUnit> items)
            throws DataUpdationException, SumoException, InventoryUpdateException {
        if (items != null) {
            SCMDayCloseEventData eventData = stockManagementDao.find(SCMDayCloseEventData.class, eventId);
            if(eventData!=null){
                if(eventData.getStatus().equals(SCMOrderStatus.CANCELLED.name())){
                    throw new InventoryUpdateException(new SCMError("Update Failed", "Day Close Already cancelled", 702));
                }
            }
            items.forEach(item -> {
                DayCloseInventoryDrillDown stockEntryEventData = stockManagementDao
                        .find(DayCloseInventoryDrillDown.class, item.getInventoryId());
                stockEntryEventData.setActual(SCMUtil.convertToBigDecimal(item.getStockValue()));
                stockEntryEventData.setVarianceReason(item.getVarianceReason());
                stockCalculator.calculateVariance(stockEntryEventData);
                BigDecimal varianceCost = SCMUtil.multiplyWithScale10(stockEntryEventData.getSkuPrice(),
                        stockEntryEventData.getVariance());
                stockEntryEventData.setVarianceCost(varianceCost);
                stockManagementDao.update(stockEntryEventData, false);
            });
            stockManagementDao.flush();
            DayCloseEventLogData eventLog = saveEventLog(eventData, DayCloseEventLogType.CORRECTION, userId);
            addCorrectionWriteOff(items, eventLog);
            return true;
        }
        return false;
    }

    private void addInventoryWriteOff(List<DayCloseTxnItem> txnItems, DayCloseEventLogData eventLog) throws SumoException {
        for (DayCloseTxnItem item : txnItems) {
            WriteOffItemData itemData = new WriteOffItemData();
            itemData.setEventLogData(eventLog);
            itemData.setSkuId(item.getSkuId());
            itemData.setSkuPrice(SCMUtil.convertToBigDecimal(item.getPrice()));
            itemData.setCorrectedValue(item.getQty());
            itemData.setExpectedValue(item.getQty());
            stockManagementDao.add(itemData, false);
        }
        stockManagementDao.flush();
    }

    private void addCorrectionWriteOff(List<ProductStockForUnit> items, DayCloseEventLogData eventLog) throws SumoException {
        for (ProductStockForUnit item : items) {
            WriteOffItemData itemData = new WriteOffItemData();
            itemData.setEventLogData(eventLog);
            itemData.setSkuId(item.getSkuId());
            itemData.setSkuPrice(SCMUtil.convertToBigDecimal(item.getPrice()));
            itemData.setCorrectedValue(SCMUtil.convertToBigDecimal(item.getStockValue()));
            itemData.setExpectedValue(SCMUtil.convertToBigDecimal(item.getExpectedValue()));
            stockManagementDao.add(itemData, false);
        }
        stockManagementDao.flush();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean submitDayClose(int userId, int eventId)
            throws DataUpdationException, InventoryUpdateException, NegativeStockException {
        SCMDayCloseEventData eventData = stockManagementDao.find(SCMDayCloseEventData.class, eventId);
        if (eventData != null) {
            List<DayCloseTxnEventMapping> txnEvents = eventData.getEventMappingList();
            if (txnEvents != null) {
                for (DayCloseTxnEventMapping txnEvent : txnEvents) {
                    switch (DayCloseEventLogType.valueOf(txnEvent.getEventType())) {
                        case BOOKINGS:
                            updateBooking(txnEvent);
                            break;
                        case REVERSE_BOOKING:
                            updateReverseBooking(txnEvent);
                            break;
                        case TRANSFERS:
                            updateTransfer(txnEvent);
                            break;
                        case INVOICE:
                        case B2B_RETURN:
                            updateInvoice(txnEvent);
                            break;
                        case RECEIVINGS:
                            updateReceiving(txnEvent);
                            break;
                        case WASTAGES:
                            updateWastage(txnEvent);
                            break;
                        case GATEPASS:
                        case GATEPASS_RETURN:
                            updateGatepass(txnEvent);
                            break;
                        default:
                            break;
                    }
                }
                stockManagementDao.flush();
                updateStockValues(eventData);
                //generateAndSendVarianceReport(eventData);
                eventData.setStatus(StockEventStatus.CLOSED.name());
                eventData.setUpdatedBy(userId);
                eventData.setUpdatedAt(SCMUtil.getCurrentTimestamp());
                stockManagementDao.update(eventData, true);
                notificationService.sendDayClosureNotification(eventData);

            }
        }
        return true;
    }

    private void checkForNegativeInventory(SCMDayCloseEventData event, List<DayCloseInventoryDrillDown> inventory)
            throws NegativeStockException {
        List<DayCloseInventoryDrillDown> foundNegative = inventory.stream()
                .filter(o -> (o.getActual() != null && BigDecimal.ZERO.compareTo(o.getActual()) > 0))
                .collect(Collectors.toList());
        if (foundNegative != null && !foundNegative.isEmpty()) {
            List<ProductStockForUnit> negatives = foundNegative.stream()
                    .map(SCMDataConverter::convertInventory)
                    .collect(Collectors.toList());
            StringBuilder message = new StringBuilder("::::::: Negative Inventory Found for Products at Day Close :::::::\n");
            negatives.forEach(stockForUnit -> message.append(stockForUnit.toString()).append("\n"));
            message.append(":::::::::::::::::::::::::::::::::");
            notificationService.sendDayClosureAlarmNotification(event.getUnitId(), message.toString());
            NegativeStockException e = new NegativeStockException("NEGATIVE STOCK FOUND",
                    "Please settle them first!", negatives);
            throw e;
        }

    }

    private boolean updateStockValues(SCMDayCloseEventData eventData)
            throws InventoryUpdateException, DataUpdationException, NegativeStockException {
        List<DayCloseInventoryDrillDown> closing = stockManagementDao.getInventoryList(eventData);
        // Check for Negatives in the current inventory that is being submitted at Day Close and notify accordingly
        checkForNegativeInventory(eventData, closing);
        if (closing != null) {
            List<StockEntryEventData> opening = getOpeningValues(eventData, false);
            Map<Integer, StockEntryEventData> openingMap = opening.stream()
                    .collect(Collectors.toMap(StockEntryEventData::getSkuId, Function.identity()));
            Map<Integer, DayCloseInventoryDrillDown> closingMap = closing.stream()
                    .collect(Collectors.toMap(DayCloseInventoryDrillDown::getSkuId, Function.identity()));
            try {
                List<ProductStockForUnit> stockList = new ArrayList<>();
                closingMap.keySet().forEach(skuId -> {
                    DayCloseInventoryDrillDown inventory = closingMap.get(skuId);
                    StockEntryEventData openingValue = openingMap.get(skuId);
                    openingValue.setCurrentStock(inventory.getActual());
                    stockManagementDao.update(openingValue, false);
                    stockList.add(SCMDataConverter.convertInventory(inventory));
                });
                settlePricing(stockList, eventData);
                goodsReceiveManagementService.getPriceDao().deleteObsoletePrices(eventData.getUnitId(), PriceUpdateEntryType.SKU);
                stockManagementDao.flush();
                raiseAlarm(eventData, closingMap);
                return true;
            } catch (Exception e) {
                String message = "Encountered error while updating active stock for the unit " + eventData.getUnitId() + "<br>"  +  e.getMessage();
                LOG.error(message, e);
                throw new InventoryUpdateException(message);
            }
        }
        return false;
    }

    private void raiseAlarm(SCMDayCloseEventData eventData, Map<Integer, DayCloseInventoryDrillDown> closingMap) {
        BigDecimal totalVarianceCost = BigDecimal.ZERO;
        BigDecimal totalInventoryCost = BigDecimal.ZERO;
        for (Integer key : closingMap.keySet()) {
            DayCloseInventoryDrillDown inventory = closingMap.get(key);
            totalVarianceCost = totalVarianceCost.add(inventory.getVarianceCost());
            BigDecimal inventoryCost = SCMUtil.multiplyWithScale(inventory.getSkuPrice(), inventory.getActual(), 5);
            totalInventoryCost = totalInventoryCost.add(inventoryCost);
        }
        BigDecimal tolerancePercentage = SCMUtil.percentage(totalVarianceCost, totalInventoryCost, 5);
        if (tolerancePercentage.compareTo(BigDecimal.valueOf(2.5d)) > 0) {
            notificationService.sendDayClosureAlarmNotification(eventData, tolerancePercentage);
        }

    }


    private void settlePricing(List<ProductStockForUnit> stockList, SCMDayCloseEventData event) throws InventoryUpdateException, DataNotFoundException {
        VarianceVO negativeVariance = new VarianceVO(event.getEventId(), event.getUnitId(), PriceUpdateEntryType.SKU);
        VarianceVO positiveVariance = new VarianceVO(event.getEventId(), event.getUnitId(), PriceUpdateEntryType.SKU);
        VarianceVO all = new VarianceVO(event.getEventId(), event.getUnitId(), PriceUpdateEntryType.SKU);
        for (ProductStockForUnit stock : stockList) {

            int typeOfVariance = stock.getVariance().compareTo(BigDecimal.ZERO);
            if (typeOfVariance > 0) {
                positiveVariance.getStockList().add(stock);
            }
            if (typeOfVariance < 0) {
                negativeVariance.getStockList().add(stock);
            }
            all.getStockList().add(stock);
        }
        positiveVariance = goodsReceiveManagementService.getPriceDao().reduceConsumable(positiveVariance, false);
        negativeVariance = goodsReceiveManagementService.getPriceDao().addReceiving(negativeVariance, false);
        List<ProductStockForUnit> list = new ArrayList<ProductStockForUnit>();
        if(positiveVariance.getStockList() != null && positiveVariance.getStockList().size() > 0) {
        	list.addAll(positiveVariance.getStockList());
        }
        if(negativeVariance.getStockList() != null && negativeVariance.getStockList().size() > 0) {
        	list.addAll(negativeVariance.getStockList());
        }
        updatePrice(event.getEventId(),list);
        goodsReceiveManagementService.getPriceDao().overrideInventory(all);
    }

    private void updatePrice(Integer eventId, List<ProductStockForUnit> stockForUnits) {
    	stockManagementDao.deleteAllProductStockDrillDownUpdateEvent(eventId);
    	if(stockForUnits != null && stockForUnits.size() > 0) {
    		List<ProductStockDrillDownUpdateEvent> list = new ArrayList<ProductStockDrillDownUpdateEvent>();
    		for(ProductStockForUnit unit : stockForUnits) {
        		list.add(convert(eventId, unit));
    		}
    		stockManagementDao.addAll(list);
    		stockManagementDao.updatePriceAndVarianceCost(eventId);
    		stockManagementDao.deleteAllProductStockDrillDownUpdateEvent(eventId);
    	}

    }

	private ProductStockDrillDownUpdateEvent convert(Integer eventId, ProductStockForUnit unit) {
		ProductStockDrillDownUpdateEvent event = new ProductStockDrillDownUpdateEvent();
		event.setCost(unit.getVariance().multiply(unit.getPrice()));
		event.setPrice(unit.getPrice());
		event.setEventId(eventId);
		event.setInventoryId(unit.getInventoryId());
		return event;
	}

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<ProductStockForUnit> getNegativeStocks(int userId, int eventId, List<DayCloseTxnItem> items ) throws DataUpdationException, InventoryUpdateException, SumoException {
        List<ProductStockForUnit> allStocks = new ArrayList<>();
        List<ProductStockForUnit> negativeStocks = new ArrayList<>();
        allStocks = submitInventory(userId,eventId,items,true);
        if(allStocks.size()>0){
            for(ProductStockForUnit stockItem : allStocks){
                ProductDefinition productDefinition = scmCache.getProductDefinition(stockItem.getProductId());
                if(productDefinition.getCategoryDefinition().getId().equals(SCMServiceConstants.CATEGORY_FIXED_ASSETS)){
                    continue;
                }
                BigDecimal actual = stockItem.getStockValue();
                BigDecimal expected = stockItem.getExpectedValue();
                boolean filter = (actual.compareTo(expected) == 0 && actual.compareTo(BigDecimal.ZERO) < 0)
                        || (stockItem.getStockValue() == null && expected.compareTo(BigDecimal.ZERO) < 0)
                        || (actual.compareTo(BigDecimal.ZERO) < 0);
                if (filter) {
                    negativeStocks.add(stockItem);
                }
            }

        }
        return  negativeStocks;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<ProductStockForUnit> getInventorySummary(int eventId) {
        SCMDayCloseEventData eventData = stockManagementDao.find(SCMDayCloseEventData.class, eventId);
        List<DayCloseInventoryDrillDown> inventoryDrillDownList = stockManagementDao.getInventoryList(eventData);
        List<ProductStockForUnit> stock = new ArrayList<>();
        if (inventoryDrillDownList != null) {
            for (DayCloseInventoryDrillDown inventoryDrillDown : inventoryDrillDownList) {
                ProductDefinition productDefinition = scmCache.getProductDefinition(inventoryDrillDown.getProductId());
                if(productDefinition.getCategoryDefinition().getId().equals(SCMServiceConstants.CATEGORY_FIXED_ASSETS)){
                    continue;
                }
                // to show just negative inventory items
                BigDecimal actual = SCMUtil.convertToBigDecimal(inventoryDrillDown.getActual());
                BigDecimal expected = SCMUtil.convertToBigDecimal(inventoryDrillDown.getExpected());
                boolean filter = (actual.compareTo(expected) == 0 && actual.compareTo(BigDecimal.ZERO) < 0)
                        || (inventoryDrillDown.getActual() == null && expected.compareTo(BigDecimal.ZERO) < 0)
                        || (actual.compareTo(BigDecimal.ZERO) < 0);
                if (filter) {
                    ProductStockForUnit stockItem = SCMDataConverter.convertInventory(inventoryDrillDown);
                    stockItem.setStockValue(SCMUtil.convertToBigDecimal(inventoryDrillDown.getExpected()));
                    stock.add(stockItem);
                }
            }
        }
        return stock;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<ProductStockForUnit> getInventorySummary(List<Integer> skuList, int unitId)
            throws DataUpdationException {
        LOG.info("{} STOCK AT HAND Step 1: STARTING CALL!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!", unitId);
        Map<Integer, DayCloseInventoryDrillDown> drillDownMap = new HashMap<>();

        for (Integer skuId : skuList) {
            DayCloseInventoryDrillDown d = new DayCloseInventoryDrillDown();
            SkuDefinition skuDefinition = scmCache.getSkuDefinition(skuId);
            d.setSkuId(skuId);
            d.setProductId(skuDefinition.getLinkedProduct().getId());
            d.setUnitOfMeasure(skuDefinition.getUnitOfMeasure());
            d.setSkuPrice(BigDecimal.valueOf(skuDefinition.getUnitPrice()));
            drillDownMap.put(skuId, d);
        }
        LOG.info("{} STOCK AT HAND Step 2: SKU LIST DONE!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!", unitId);

        List<Integer> keyIds = new ArrayList<>(drillDownMap.keySet());
        List<CostDetailData> priceData = getCurrentPrices(PriceUpdateEntryType.SKU, unitId, keyIds, true);
        Map<Integer, CostDetailData> currentPrices = priceData.stream()
                .collect(Collectors.toMap(CostDetailData::getKeyId, Function.identity()));
        for (Integer skuId : keyIds) {
            try {
                drillDownMap.get(skuId).setSkuPrice(currentPrices.get(skuId).getPrice());
            } catch (Exception e) {
                LOG.error("Error while setting current price to SKU :::::" + skuId);
            }
        }
        LOG.info("{} STOCK AT HAND Step 3: GET CURRENT PRICES DONE!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!", unitId);


        // direct openingStock
        List<StockEntryEventData> openingStock = stockManagementDao.getActiveOpeningEventForUnit(unitId, skuList);
        LOG.info("{} STOCK AT HAND Step 4: GET ACTIVE OPENING DONE!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!", unitId);

        // 7, (100,0,0,0,0)

        DayCloseInventoryDrillDown drill = null;
        if (openingStock != null && !openingStock.isEmpty()) {
            for (StockEntryEventData data : openingStock) {
                drill = drillDownMap.get(data.getSkuId());
                drill.setOpeningStock(SCMUtil.convertToBigDecimal(data.getCurrentStock()));
            }
        } else { // in case no opening event is found
            for (Integer skuID : drillDownMap.keySet()) {
                drill = drillDownMap.get(skuID);
                drill.setOpeningStock(BigDecimal.ZERO);
            }
        }

        // item - transfers
        List<DayCloseTxnItem> transfers = getTransfersForClosing(unitId, skuList);
        for (DayCloseTxnItem item : transfers) {
            for (DayCloseTxnItem data : item.getTxnItems()) {
                drill = drillDownMap.get(data.getSkuId());
                drill.setTransferred(
                        drill.getTransferred() != null ? drill.getTransferred().add(data.getQty()) : data.getQty());
            }
        }
        LOG.info("{} STOCK AT HAND Step 5: GET TRANSFERS FOR CLOSING DONE!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!", unitId);

        // item - gatepaasses
        List<DayCloseTxnItem> gatepasses = getGatepasses(unitId, skuList);
        for (DayCloseTxnItem item : gatepasses) {
            drill = drillDownMap.get(item.getSkuId());
            drill.setTransferred(drill.getTransferred() != null ? drill.getTransferred().add(item.getQty()) : item.getQty());

        }


        // item - invoices
        Map<String,List<DayCloseTxnItem>> invoices = getInvoicesMap(unitId, skuList);
        for (DayCloseTxnItem item : invoices.get(SalesPerformaType.B2B_SALES.name())) {
            for (DayCloseTxnItem data : item.getTxnItems()) {
                drill = drillDownMap.get(data.getSkuId());
                drill.setTransferred(
                        drill.getTransferred() != null ? drill.getTransferred().add(data.getQty()) : data.getQty());
            }
        }
        LOG.info("{} STOCK AT HAND Step 6: GET INVOICES DONE!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!", unitId);


        // direct wasted
        List<DayCloseTxnItem> wastage = getWastagesForClosing(unitId, skuList);
        if (wastage != null) {
            for (DayCloseTxnItem data : wastage) {
                drill = drillDownMap.get(data.getSkuId());
                if (drill != null) {
                    drill.setWasted(drill.getWasted() != null ? drill.getWasted().add(data.getQty()) : data.getQty());
                }
            }
        }
        LOG.info("{} STOCK AT HAND Step 7: GET WASTAGES DONE!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!", unitId);

        // booking sku booked , item - consumed
        AcknowledgeTransactionVO  bookings = getBookingsSummaryForClosing(unitId, skuList, false);
        for (DayCloseTxnItem item : bookings.getList()) {
            drill = drillDownMap.get(item.getSkuId());
            drill.setBooked(drill.getBooked() != null ? drill.getBooked().add(item.getQty()) : item.getQty());
        }
        LOG.info("{} STOCK AT HAND Step 8A: GET BOOKINGS DONE!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!", unitId);

        AcknowledgeTransactionVO  bookingConsumptions = getBookingConsumptionsSummary(unitId, skuList);
        for (DayCloseTxnItem data : bookingConsumptions.getList()) {
            drill = drillDownMap.get(data.getSkuId());
            if (drill != null) {
                drill.setConsumed(drill.getConsumed() != null ? drill.getConsumed().add(data.getQty()) : data.getQty());
            }
        }
        LOG.info("{} STOCK AT HAND Step 9A: GET BOOKING CONSUMPTIONS DONE!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!", unitId);

        AcknowledgeTransactionVO  reverseBookings = getReverseBookingsSummaryForClosing(unitId, skuList, false);
        for (DayCloseTxnItem item : reverseBookings.getList()) {
            drill = drillDownMap.get(item.getSkuId());
            if (Objects.nonNull(drill)) {
                drill.setReverseBooked(drill.getReverseBooked() != null ? drill.getReverseBooked().add(item.getQty()) : item.getQty());
            }
        }
        LOG.info("{} STOCK AT HAND Step 8B: GET REVERSE BOOKINGS DONE!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!", unitId);

        AcknowledgeTransactionVO  reverseBookingConsumptions = getReverseBookingConsumptionsSummary(unitId, skuList);
        for (DayCloseTxnItem data : reverseBookingConsumptions.getList()) {
            drill = drillDownMap.get(data.getSkuId());
            if (drill != null) {
                drill.setReverseConsumed(drill.getReverseConsumed() != null ? drill.getReverseConsumed().add(data.getQty()) : data.getQty());
            }
        }
        LOG.info("{} STOCK AT HAND Step 9B: GET REVERSE BOOKING CONSUMPTIONS DONE!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!", unitId);


        // items received
        List<DayCloseTxnItem> receivings = getReceivingsForClosing(unitId, skuList);
        for (DayCloseTxnItem item : receivings) {
            for (DayCloseTxnItem data : item.getTxnItems()) {
                drill = drillDownMap.get(data.getSkuId());
//                drill.setReceived(drill.getReceived() != null ? drill.getReceived().add(data.getQty()) : data.getQty());
                if(drill.getReceived() != null && !item.getStatus().equals(PurchaseOrderStatus.INITIATED.toString())){
                    drill.setReceived(SCMUtil.add(drill.getReceived(), data.getQty()));
                }
                else if(drill.getReceived() == null && !item.getStatus().equals(PurchaseOrderStatus.INITIATED.toString())){
                    drill.setReceived(data.getQty());
                }
                if(item.isExternal() && item.getStatus().equals(PurchaseOrderStatus.INITIATED.toString())) {
                    drill.setReceivedWithInitiatedGr(drill.getReceivedWithInitiatedGr() != null ? drill.getReceivedWithInitiatedGr().add(data.getQty()) : data.getQty());
                }
            }
        }
        LOG.info("{} STOCK AT HAND Step 10: GET RECEIVING DONE!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!", unitId);

        // items returned
        List<DayCloseTxnItem> returns = getGatepassReturns(unitId, skuList);
        for (DayCloseTxnItem item : returns) {
            drill = drillDownMap.get(item.getSkuId());
            drill.setReceived(drill.getReceived() != null ? drill.getReceived().add(item.getQty()) : item.getQty());
        }
        LOG.info("{} STOCK AT HAND Step 11: GET GATEPASS RETURNS DONE!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!", unitId);

        List<ProductStockForUnit> stocks = new ArrayList<>();
        for (DayCloseInventoryDrillDown d : drillDownMap.values()) {
            stockCalculator.calculateVariance(d);
            ProductDefinition definition = scmCache.getProductDefinition(d.getProductId());
            stocks.add(SCMDataConverter.convert(d, definition));
        }
        LOG.info("{} STOCK AT HAND Step 12: FINAL CALL DONE!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!", unitId);

        return stocks;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean getThresholdDayCloseTime(int unitId) {
        if (props.getDayCloseCheck() && stockManagementDao.getActiveDayCloseEvent(unitId) == null) {
            UnitDetailData unit = stockManagementDao.find(UnitDetailData.class, unitId);
            if (unit != null) {
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(SCMUtil.getCurrentTimestamp());
                int hourOfTheDay = calendar.get(Calendar.HOUR_OF_DAY);
                Integer endTime = unit.getClosingEndTime();
                return endTime != null ? endTime <= hourOfTheDay : false;
            }
        }
        return false;
    }


    @Override
    public Disclaimer checkIfDayCloseDoneInWeek(Integer unitId){
        Disclaimer res = new Disclaimer() ;
        if(isOnceInAWeekDayCloseUnit(masterDataCache.getUnit(unitId))){
            if(transferOrderManagementService.checkIfUnitIsExcludedForDayCloseCheck(unitId).equals(Boolean.TRUE)){
                return res;
            }
            //HardCoded For Training Unit
            Unit unit = masterDataCache.getUnit(unitId);
            StockEventType eventType = (unitId == 26003 || SCMUtil.isOffice(unit.getFamily()) || unit.getFamily().equals(UnitCategory.CHAI_MONK) ) ?
                    StockEventType.STOCK_TAKE : StockEventType.WH_CLOSING;
            SCMDayCloseEventData dayCloseEventData = stockManagementDao.getLastDayCloseEvent(unitId, eventType);
            if(!Objects.isNull(dayCloseEventData)){
                res = transferOrderManagementService.validateIfDayCloseDoneInWeek(dayCloseEventData) ;
            }
        }
        return res;
    }

    @Override
    public FixedAssetDayCloseResponseObject getAssetsToDayCloseToday(Integer unitId) {
        LOG.info("Getting Fixed Assets to DayClose for UNIT : {}", unitId);
        if( !scmAssetManagementService.checkFaDaycloseEnabled(unitId)){
            LOG.info("FA DayClose Not Enabled for Unit :: {}", unitId);
            FixedAssetDayCloseResponseObject response
                    = new FixedAssetDayCloseResponseObject();
            return response;
        }
        LOG.info("UNIT ID ::::::::: : ", unitId);
        Stopwatch watch = Stopwatch.createUnstarted();
        watch.start();
        List<FixedAssetCompactDefinition> availableAssets = scmAssetManagementService.viewAllFixedAssetsFromUnit(unitId);
        LOG.info("Get All Asset for Unit : {}s",  watch.stop().elapsed(TimeUnit.SECONDS));
        watch.start();
        Date lastDailyDayClose = stockManagementDao.getLastDayCloseDone(unitId,StockTakeSubType.DAILY_DAYCLOSE.value());
        LOG.info("Last Daily Day Close Date : {}", lastDailyDayClose != null ? AppUtils.getDate(lastDailyDayClose).toString():"");
        Date lastWeeklyDayClose = stockManagementDao.getLastDayCloseDone(unitId,StockTakeSubType.WEEKLY_DAYCLOSE.value());
        LOG.info("Last Weekly Day Close Date : {}", lastWeeklyDayClose != null ? AppUtils.getDate(lastWeeklyDayClose).toString():"");
        Date lastMonthlyDayClose = stockManagementDao.getLastDayCloseDone(unitId,StockTakeSubType.MONTHLY_DAYCLOSE.value());
        LOG.info("Last Monthly Day Close Date : {}", lastMonthlyDayClose != null ? AppUtils.getDate(lastMonthlyDayClose).toString():"");
        Calendar calendar = Calendar.getInstance();
//        calendar.setTime(lastWeeklyDayClose);
//        Integer lastWeeklyDone =  calendar.get(Calendar.DAY_OF_WEEK);
        calendar.setTime(SCMUtil.getCurrentBusinessDate());
        Integer dayToday = calendar.get(Calendar.DAY_OF_WEEK);
        Integer dateToday = calendar.get(Calendar.DAY_OF_MONTH);
//        Integer currentMonth = calendar.get(Calendar.MONTH);


        LOG.info("Get last day close done : {}s",  watch.stop().elapsed(TimeUnit.SECONDS));
        watch.start();
        long dailyDayDiff = -1;
        if(!Objects.isNull(lastDailyDayClose)){
            dailyDayDiff = SCMUtil.getDayDifference(AppUtils.getDate(lastDailyDayClose), SCMUtil.getCurrentBusinessDate());
            LOG.info("Daily Day Close Difference from Current Date : {}", dailyDayDiff);
        }
        long weeklyDayDiff = -1;
        if(!Objects.isNull(lastWeeklyDayClose)){
            weeklyDayDiff = SCMUtil.getDayDifference(AppUtils.getDate(lastWeeklyDayClose), SCMUtil.getCurrentBusinessDate());
        }
        long monthlyDayDiff = -1;
        if(!Objects.isNull(lastMonthlyDayClose)){
            monthlyDayDiff = SCMUtil.getDayDifference(AppUtils.getDate(lastMonthlyDayClose), SCMUtil.getCurrentBusinessDate());
        }
        Pair < Map<String ,List<FixedAssetCompactDefinition>>, Integer> result;
        Map< String ,List<FixedAssetCompactDefinition> > assetMap;
        boolean showWeekly = false;
        boolean blockweekly = false;
        result = filterAssetsForDayClose(availableAssets,unitId);
        assetMap = result.getFirst();
        if(Objects.nonNull(lastWeeklyDayClose) && Objects.nonNull(result.getSecond())){
            showWeekly = true;
            LOG.info("Selected Inventory List Id : {}", result.getSecond());
            Integer actualDayCloseDay = result.getSecond();
            actualDayCloseDay = actualDayCloseDay - 16;
            LOG.info("Actual Day Close Day : {}", actualDayCloseDay);
            LOG.info("Day Today : {}", dayToday);
            com.stpl.tech.master.domain.model.Pair<Date,Date> week = new com.stpl.tech.master.domain.model.Pair<>();
            if(Objects.equals(dayToday, actualDayCloseDay)){
                week.setKey(SCMUtil.getCurrentBusinessDate());
                week.setValue(SCMUtil.getDayBeforeOrAfterDay(week.getKey(),7));
            }else if( dayToday > actualDayCloseDay){
                week.setKey(SCMUtil.getDayBeforeOrAfterDay(SCMUtil.getCurrentBusinessDate(),-(dayToday-actualDayCloseDay)));
                week.setValue(SCMUtil.getDayBeforeOrAfterDay(week.getKey(),7));
            }else if(dayToday < actualDayCloseDay){
                week.setValue(SCMUtil.getDayBeforeOrAfterDay(SCMUtil.getCurrentBusinessDate(),actualDayCloseDay-dayToday));
                week.setKey(SCMUtil.getDayBeforeOrAfterDay(week.getValue(),-7));
            }
            LOG.info("Week start : {}", week.getKey().toString());
            LOG.info("Week end : {}", week.getValue().toString());

            if(lastWeeklyDayClose.compareTo(week.getKey()) >=0 && lastWeeklyDayClose.compareTo(week.getValue()) <=0){
                showWeekly = false;
            }else{
                if(SCMUtil.getDayDifference(AppUtils.getDate(lastWeeklyDayClose), week.getKey()) > 7){
                    blockweekly = true;
                    LOG.info(":::::::::::WEEKLY BLOCKED:::::::::: ");
                }
                Date bufferLimit = SCMUtil.getDayBeforeOrAfterDay(week.getKey(),5);
                LOG.info("Buffer Limit : {}", bufferLimit.toString());
                if(bufferLimit.compareTo(SCMUtil.getCurrentBusinessDate()) < 0){
                    blockweekly = true;
                    LOG.info(":::::::::::WEEKLY BLOCKED:::::::::: ");
                }
            }
        }
        else if(Objects.isNull(lastWeeklyDayClose)){
            Map<String, Pair<Date,Long>> events = scmAssetManagementService.getPendingDayCloseEvents(unitId);
            if(Objects.nonNull(events)  && !events.isEmpty()){
                if( Objects.nonNull(events.get(StockTakeSubType.DAILY_DAYCLOSE.value())) &&
                        Objects.nonNull(events.get(StockTakeSubType.DAILY_DAYCLOSE.value()).getValue())){
                    if(events.get(StockTakeSubType.DAILY_DAYCLOSE.value()).getValue() < 7){
                        showWeekly = true;
                    }
                }
                else if( Objects.nonNull(events.get(StockTakeSubType.MONTHLY_DAYCLOSE.value())) &&
                        Objects.nonNull(events.get(StockTakeSubType.MONTHLY_DAYCLOSE.value()).getValue())){
                    if(events.get(StockTakeSubType.MONTHLY_DAYCLOSE.value()).getValue() < 7){
                        showWeekly = true;
                    }
                }
            }
            else{
                blockweekly = true;
            }
        }
        LOG.info("asset filtering weekly : {}s",  watch.stop().elapsed(TimeUnit.SECONDS));
        watch.start();
        boolean showMonthly = false;
        boolean blockMonthly = false;
        calendar.setTime(SCMUtil.getCurrentTimestamp());
        Integer currentDay = calendar.get(Calendar.DAY_OF_MONTH);
        Integer currentMonth = calendar.get(Calendar.MONTH);
        if(Objects.nonNull(lastMonthlyDayClose)){
            calendar.setTime(lastMonthlyDayClose);
            Integer lastMonthlyDay = calendar.get(Calendar.DAY_OF_MONTH);
            Integer lastMonthlyMonth = calendar.get(Calendar.MONTH);
            if(currentMonth - lastMonthlyMonth == 1){
                if(lastMonthlyDay >= 25 && currentDay >= 25){
                    showMonthly = true;
                }else if(lastMonthlyDay < 25){
                    blockMonthly = true;
                }
            }else if(Objects.equals(currentMonth, lastMonthlyMonth)){
                if(currentDay >= 25){
                    showMonthly = true;
                }
                if(lastMonthlyDay >= 25){
                    showMonthly = false;
                }
            }else if(currentMonth - lastMonthlyMonth > 1){
                blockMonthly = true;
            }
        }else if(Objects.isNull(lastMonthlyDayClose)){
            Date unitStartDate = masterDataCache.getUnit(unitId).getStartDate();
            if(SCMUtil.getDayDifference(unitStartDate ,AppUtils.getDate(SCMUtil.getCurrentTimestamp())) > 30){
                StockEventDefinitionData auditEvent = scmAssetManagementService.getLatestNSOEventByUnit(unitId,
                        StockEventStatusType.COMPLETED.value(), StockTakeSubType.AUDIT.value());
                StockEventDefinitionData regularEvent = scmAssetManagementService.getLatestNSOEventByUnit(unitId,
                        StockEventStatusType.COMPLETED.value(), StockTakeSubType.REGULAR.value());
                if(Objects.nonNull(auditEvent) || Objects.nonNull(regularEvent)){
                    showMonthly = true;
                }else{
                    blockMonthly = true;
                }
            } else {
                showMonthly = true;
            }
        }
        if(AppUtils.getDate(SCMUtil.getCurrentTimestamp()).compareTo(AppUtils.getDate(1,9,2023)) < 0){
            if(Boolean.TRUE.equals(blockMonthly)){
                blockMonthly = false;
                showMonthly = true;
            }
        }
        LOG.info("asset filtering monthly : {}s",  watch.stop().elapsed(TimeUnit.SECONDS));

        FixedAssetDayCloseResponseObject response
         = new FixedAssetDayCloseResponseObject(
                null,
                (showWeekly || blockweekly) ? assetMap.get(StockTakeSubType.WEEKLY_DAYCLOSE.value()) : null,
                 (Objects.equals(showMonthly,true) || Objects.equals(blockMonthly,true)) ? assetMap.get(StockTakeSubType.MONTHLY_DAYCLOSE.value()) : null,
                 null, lastWeeklyDayClose, lastMonthlyDayClose, null, blockweekly, blockMonthly);
        return response;
    }

    private Pair< Map<String ,List<FixedAssetCompactDefinition>>, Integer> filterAssetsForDayClose(List<FixedAssetCompactDefinition> availableAssets, Integer unitId) {
        Map< String ,List<FixedAssetCompactDefinition> > assetList;
        UnitBasicDetail unit = masterDataCache.getUnitBasicDetail(unitId);
        String unitCategory = unit.getCategory().value();
//        String type= "";
//        if(Objects.equals(unitCategory,"WAREHOUSE")){
//             TODO Define Warehouse Types ( FA, Remot, Goods, Maintenance, IT-Maintenance) and further filter.
//        }else {
//            type= null;
//        }
//        if(Objects.equals(stockTakeEventSubType,StockTakeSubType.WEEKLY_DAYCLOSE)){
//            frequency = StockTakeFrequencyEnum.WEEKLY.value();
//        }else if(Objects.equals(stockTakeEventSubType,StockTakeSubType.MONTHLY_DAYCLOSE)){
//            frequency = StockTakeFrequencyEnum.MONTHLY.value();
//        }
        Map<String, List<Integer>> FrequencyClassMapping = new HashMap<String, List<Integer>>();
        List<Integer> classIdWeeklyList = stockManagementDao.getClassListFromUnitType(unitCategory,null,StockTakeFrequencyEnum.WEEKLY.value());
        FrequencyClassMapping.put(StockTakeFrequencyEnum.WEEKLY.value(), classIdWeeklyList);
        List<Integer> classIdMonthlyList = stockManagementDao.getClassListFromUnitType(unitCategory,null,StockTakeFrequencyEnum.MONTHLY.value());
        FrequencyClassMapping.put(StockTakeFrequencyEnum.MONTHLY.value(), classIdMonthlyList);
        Integer stockTakeDay = stockManagementDao.getInventoryDayForUnit(unitCategory, null, StockTakeFrequencyEnum.WEEKLY.value());
        assetList = getFixedAssetsForDayClose(availableAssets,FrequencyClassMapping);

        return new org.apache.commons.math3.util.Pair<>(assetList,stockTakeDay);
    }

    private Map< String ,List<FixedAssetCompactDefinition> > getFixedAssetsForDayClose(List<FixedAssetCompactDefinition> availableAssets, Map<String, List<Integer>> FrequencyClassMapping) {

        Map< String ,List<FixedAssetCompactDefinition> > assetsToDayClose = new HashMap<>();
        List<Integer> finalInventoryListsWeekly = FrequencyClassMapping.get(StockTakeFrequencyEnum.WEEKLY.value());
        assetsToDayClose.put(StockTakeSubType.WEEKLY_DAYCLOSE.value(), availableAssets.stream()
                .filter(FixedAssetCompactDefinition ->  finalInventoryListsWeekly.contains(FixedAssetCompactDefinition.getClassificationId()))
                .collect(Collectors.toList()));
        List<Integer> finalInventoryListsMonthly = FrequencyClassMapping.get(StockTakeFrequencyEnum.MONTHLY.value());
        assetsToDayClose.put(StockTakeSubType.MONTHLY_DAYCLOSE.value(), availableAssets.stream()
                .filter(FixedAssetCompactDefinition ->  finalInventoryListsMonthly.contains(FixedAssetCompactDefinition.getClassificationId()))
                .collect(Collectors.toList()));
        return assetsToDayClose;
    }

    private   Boolean isOnceInAWeekDayCloseUnit(Unit unit){
        if(SCMUtil.isKitchen(scmCache.getUnitDetail(unit.getId())) || transferOrderManagementService.isFullfilmentWH(unit.getId())){
            return false;
        }
        return SCMUtil.isOffice(unit.getFamily()) || SCMUtil.isWareHouse(unit.getFamily()) || unit.getFamily().equals(UnitCategory.CHAI_MONK);
    }

    @Override
    public List<Integer> getSkuListForUnit(Integer unitId) {
        return stockManagementDao.getSkuListForUnit(unitId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<CostDetailData> getCurrentPrices(PriceUpdateEntryType type, int unitId, List<Integer> keyIds, boolean current) {
        return goodsReceiveManagementService.getPriceDao().getCurrentPrices(type, unitId, keyIds, true);
    }

    @Override
    public Map<Integer, String> getSkuClosings(Integer unitId, List<Integer> skus) {
        return stockManagementDao.getSkuClosingDates(unitId, skus);
    }

    @Override
    public Map<Integer, BigDecimal> getInventorySummaryForProducts(Set<Integer> productIds, int unitId) {
    	//TODO SUMEET - Move this to dao
        Set<Integer> skus = new HashSet<Integer>();
        Map<Integer, List<SkuDefinition>> productToSkuMap=new HashMap<>();
        productIds.forEach(i -> {
            List<SkuDefinition> skuDefList=mappingCacheService.findAllSkuForProductId(i);
            productToSkuMap.put(i,skuDefList);
            productToSkuMap.get(i).forEach(p -> {
                skus.add(p.getSkuId());
            });
        });

        List<ProductStockForUnit> stockList = null;

        try {
            stockList = getInventorySummary(new ArrayList<Integer>(skus), unitId);
        } catch (DataUpdationException e) {
            LOG.error("Error while getting product stock for booking", e);
        }

        Map<Integer, BigDecimal> productStockMap = new HashMap<>();

        for (ProductStockForUnit stock : stockList) {
            BigDecimal quantity = productStockMap.get(stock.getProductId());
            if (quantity == null) {
                quantity = stock.getExpectedValue();
            } else {
                quantity = quantity.add(stock.getExpectedValue());
            }
            productStockMap.put(stock.getProductId(), quantity);
        }

        return productStockMap;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void generateAndSendVarianceReport(Integer eventId) {
        SCMDayCloseEventData eventData = stockManagementDao.find(SCMDayCloseEventData.class, eventId);
        reportingService.sendWhVarianceReport(eventData);
    }

    private void updateWastage(DayCloseTxnEventMapping txnEvent) {
        SCMWastageEventData wastageEventData = stockManagementDao.find(SCMWastageEventData.class, txnEvent.getTxnId());
        if (wastageEventData != null) {
            wastageEventData.setClosureEventId(txnEvent.getClosureEvent().getEventId());
            stockManagementDao.update(wastageEventData, false);
        }
    }

    private void updateReceiving(DayCloseTxnEventMapping txnEvent) {
        if (SCMServiceConstants.SCM_CONSTANT_NO.equals(txnEvent.getExternalTxn())) {
            GoodsReceivedData gr = stockManagementDao.find(GoodsReceivedData.class, txnEvent.getTxnId());
            if (gr != null) {
                gr.setClosureEventId(txnEvent.getClosureEvent().getEventId());
                stockManagementDao.update(gr, false);
            }
        } else {
            VendorGoodsReceivedData gr = stockManagementDao.find(VendorGoodsReceivedData.class, txnEvent.getTxnId());
            if (gr != null) {
                gr.setClosureEventId(txnEvent.getClosureEvent().getEventId());
                stockManagementDao.update(gr, false);
            }
        }
    }

    private void updateTransfer(DayCloseTxnEventMapping txnEvent) {
        TransferOrderData transferOrderData = stockManagementDao.find(TransferOrderData.class, txnEvent.getTxnId());
        if (transferOrderData != null) {
            transferOrderData.setClosureEventId(txnEvent.getClosureEvent().getEventId());
            stockManagementDao.update(transferOrderData, false);
        }
    }


    private void updateInvoice(DayCloseTxnEventMapping txnEvent) {
        SalesPerformaDetailData invoice = stockManagementDao.find(SalesPerformaDetailData.class, txnEvent.getTxnId());
        if (invoice != null) {
            invoice.setClosureId(txnEvent.getClosureEvent().getEventId());
            stockManagementDao.update(invoice, false);
        }
    }

    private void updateGatepass(DayCloseTxnEventMapping txnEvent) {
        GatepassItemData itemData = stockManagementDao.find(GatepassItemData.class, txnEvent.getTxnId());
        if (itemData != null) {
            itemData.setClosureId(txnEvent.getClosureEvent().getEventId());
            stockManagementDao.update(itemData, false);
        }
    }

    private void updateBooking(DayCloseTxnEventMapping txnEvent) {
        ProductionBookingData bookingData = stockManagementDao.find(ProductionBookingData.class, txnEvent.getTxnId());
        if (bookingData != null) {
            bookingData.setClosureEventId(txnEvent.getClosureEvent().getEventId());
            stockManagementDao.update(bookingData, false);
        }
    }

    private void updateReverseBooking(DayCloseTxnEventMapping txnEvent) {
        ReverseProductionBookingData bookingData = stockManagementDao.find(ReverseProductionBookingData.class, txnEvent.getTxnId());
        if (bookingData != null) {
            bookingData.setClosureEventId(txnEvent.getClosureEvent().getEventId());
            stockManagementDao.update(bookingData, false);
        }
    }

    private List<ProductStockForUnit> getConsumptionRecords(Map<Integer, BigDecimal> openingValues, Map<Integer, CostDetailData> prices,
                                                            List<StockEntryEventData> currentStock, SCMDayCloseEventData eventData,
                                                            Set<Integer> skusEntered, Map<Integer, DayCloseTxnItem> itemMap , Boolean isPreview) throws InventoryUpdateException {

        try {
            if (prices == null) {
                throw new InventoryUpdateException("Could not find any prices for unit during inventory update. Please check");
            }

            // map of current inventory on unit
            Map<Integer, DayCloseInventoryDrillDown> currentStockMap = new HashMap<>();
            List<DayCloseInventoryDrillDown> drillDowns = currentStock.stream()
                    .map(stockEntry -> {
                        DayCloseInventoryDrillDown drill = new DayCloseInventoryDrillDown();
                        BigDecimal price = getPrice(stockEntry.getSkuId(), prices.get(stockEntry.getSkuId()));
                        drill.setSkuId(stockEntry.getSkuId());
                        drill.setSkuPrice(SCMUtil.convertToBigDecimal(price));
                        drill.setOpeningStock(openingValues.get(stockEntry.getSkuId()));
                        if (skusEntered.contains(stockEntry.getSkuId())) {
                            drill.setVarianceReason(itemMap.get(stockEntry.getSkuId()).getDescription() == null ? "No Variance" : itemMap.get(stockEntry.getSkuId()).getDescription());
                        }
                        if (skusEntered.contains(stockEntry.getSkuId())
                                && !SCMUtil.convertToBigDecimal(stockEntry.getCurrentStock()).equals(BigDecimal.ZERO)) {
                            drill.setActual(stockEntry.getCurrentStock());
                        }
                        drill.setProductId(stockEntry.getProductId());
                        return drill;
                    }).collect(Collectors.toList());

            currentStockMap = drillDowns.stream()
                    .collect(
                            Collectors.toMap(
                                    DayCloseInventoryDrillDown::getSkuId,
                                    Function.identity()
                            )
                    );


            List<DayCloseTxnEventDrillDownData> drillDownData = stockManagementDao.getDrillDownData(eventData);
            if (drillDownData != null) {
                Map<Integer, List<DayCloseTxnEventDrillDownData>> skuWiseDrillDown = new HashMap<>();
                drillDownData.forEach(drillDown -> {
                    List<DayCloseTxnEventDrillDownData> txnDrillDown = skuWiseDrillDown.get(drillDown.getSkuId());
                    if (txnDrillDown == null) {
                        txnDrillDown = new ArrayList<>();
                    }
                    txnDrillDown.add(drillDown);
                    skuWiseDrillDown.put(drillDown.getSkuId(), txnDrillDown);
                });

                List<DayCloseInventoryDrillDown> stockForUnitList = stockCalculator.calculate(currentStockMap,
                        prices, skusEntered, skuWiseDrillDown);

                if (stockForUnitList != null) {
                    List<ProductStockForUnit> productStockForUnitList = new ArrayList<>();

                    for (DayCloseInventoryDrillDown drill : stockForUnitList) {
                        drill.setClosureEvent(eventData);
                        if(isPreview.equals(Boolean.FALSE)){
                            drill = stockManagementDao.add(drill, false);
                        }
                        productStockForUnitList.add(SCMDataConverter.convertInventory(drill));
                    }
                    return productStockForUnitList;
                }
            }
        } catch (Exception e) {
            LOG.error("Encountered error while calculating consumption ::::: {}", eventData.getUnitId(), e);
            throw new InventoryUpdateException("Could not update inventory for the unit " + eventData.getUnitId());
        }
        return null;
    }

    private List<StockEntryEventData> getOpeningValues(SCMDayCloseEventData eventData ,Boolean isPreview) throws DataUpdationException {
        int unitId = eventData.getUnitId();
        List<Integer> skuList = stockManagementDao.getSkuListForUnit(unitId);
        List<StockEntryEventData> opening = stockManagementDao.getActiveOpeningEventForUnit(unitId, null);
        if (opening == null || opening.size() == 0) {
            try {
                SCMDayCloseEventData openingEvent = generateOpeningEvent(unitId, StockEventStatus.INITIATED,isPreview);
                opening = scmCache.getSkuDefinitions().keySet().stream()
                        .filter(skuId -> skuList.contains(skuId))
                        .map(skuId -> {
                            try {
                                return addStockEntry(skuId, unitId, openingEvent.getEventId(),isPreview);
                            } catch (SumoException e) {
                                LOG.error("Error while adding data", e);
                            }
                            return null;
                        })
                        .collect(Collectors.toList());
            } catch (Exception e) {
                throw new DataUpdationException("Could not create opening event for unitId" + unitId);
            }
        }

        try {
            // add to opening if newly added SKU mappings found
            List<Integer> newSkus = findNew(skuList, opening);
            if (newSkus != null && !newSkus.isEmpty()) {
                newSkus = newSkus.stream().filter(skuId ->{
                    ProductDefinition productDefinition = scmCache.getProductDefinition(scmCache.getSkuDefinition(skuId).getLinkedProduct().getId());
                    if(productDefinition.getCategoryDefinition().getId().equals(SCMServiceConstants.CATEGORY_FIXED_ASSETS)){
                        return false;
                    }
                    return true;
                }).collect(Collectors.toList());
                int openingEvent = opening.get(0).getUpdateEventId();
                opening.addAll(newSkus.stream()
                        .map(skuId -> {
                            try {
                                return addStockEntry(skuId, unitId, openingEvent,isPreview);
                            } catch (SumoException e) {
                                LOG.error("Error while adding data", e);
                            }
                            return null;
                        })
                        .collect(Collectors.toList()));
            }
            if(isPreview.equals(Boolean.FALSE)){
                stockManagementDao.flush();
            }
        } catch (Exception e) {
            LOG.error("Got error while adding new opening entries for SKU Mappings :::::", e);
        }

        return opening;
    }

    private StockEntryEventData addStockEntry(Integer skuId, Integer unitId, Integer eventId , Boolean isPreview) throws SumoException {
        SkuDefinition sku = scmCache.getSkuDefinition(skuId);
        StockEntryEventData stockEntry = new StockEntryEventData();
        stockEntry.setCurrentStock(BigDecimal.ZERO);
        stockEntry.setProductId(sku.getLinkedProduct().getId());
        stockEntry.setUnitId(unitId);
        stockEntry.setGeneratedBy(SCMServiceConstants.SYSTEM_USER);
        stockEntry.setUnitOfMeasure(sku.getUnitOfMeasure());
        stockEntry.setCurrentPrice(SCMUtil.convertToBigDecimal(sku.getNegotiatedUnitPrice()));
        stockEntry.setSkuId(skuId);
        stockEntry.setUpdateEventId(eventId);
        if(isPreview.equals(Boolean.FALSE)){
            stockEntry = stockManagementDao.add(stockEntry, false);
        }
        return stockEntry;
    }

    private List<Integer> findNew(List<Integer> skuList, List<StockEntryEventData> opening) {
        Set<Integer> initialOpeningSkus = opening.stream().mapToInt(StockEntryEventData::getSkuId)
                .boxed()
                .collect(Collectors.toSet());
        return skuList.stream().filter(skuId -> !initialOpeningSkus.contains(skuId))
                .collect(Collectors.toList());
    }

    private SCMDayCloseEventData generateOpeningEvent(int unitId, StockEventStatus status , Boolean isPreview) throws SumoException {
        SCMDayCloseEventData openingEvent = new SCMDayCloseEventData();
        openingEvent.setUnitId(unitId);
        openingEvent.setDayCloseEventType(StockEventType.WH_OPENING.name());
        openingEvent.setEventFrequencyType(StockTakeType.ALL.name());
        openingEvent.setBusinessDate(SCMUtil.getCurrentBusinessDate());
        openingEvent.setGenerationTime(SCMUtil.getCurrentTimestamp());
        openingEvent.setCreatedBy(SCMServiceConstants.SYSTEM_USER);
        openingEvent.setStatus(status.name());
        if(isPreview.equals(Boolean.FALSE)){
            openingEvent = stockManagementDao.add(openingEvent, false);
        }
        return openingEvent;
    }

    private DayCloseEventLogData saveEventLog(SCMDayCloseEventData dayCloseEvent, DayCloseEventLogType type, int userId)
            throws DataUpdationException {
        try {
            DayCloseEventLogData eventLog = new DayCloseEventLogData();
            eventLog.setStatus(SwitchStatus.ACTIVE.name());
            eventLog.setCreatedAt(SCMUtil.getCurrentTimestamp());
            eventLog.setCreatedBy(userId);
            eventLog.setEventCompleted(type.name());

            if (type.equals(DayCloseEventLogType.INVENTORY)) {
                List<Integer> types = Arrays.asList(SCMUtil.getListTypeForUnit(), 16);
                eventLog.setInventoryList(types.toString());
            }

            eventLog.setDayCloseEventData(dayCloseEvent);
            return stockManagementDao.add(eventLog, true);
        } catch (Exception e) {
            LOG.error("Exception occurred while updating event log", e);
            throw new DataUpdationException("Exception occurred while updating event log");
        }
    }

    private void saveInternalTxnEvents(Set<Integer> eventList, SCMDayCloseEventData event,
                                       DayCloseEventLogType eventType) throws DataUpdationException {
        Map<Integer, String> events = eventList.stream()
                .collect(Collectors.toMap(txnId -> txnId, txnId -> SCMServiceConstants.SCM_CONSTANT_NO));
        saveTransactionEvents(events, event, eventType);
    }

    private void saveReverseInvoiceTxnEvents(Set<Integer> eventList, SCMDayCloseEventData event) throws DataUpdationException {
        Map<Integer, String> events = eventList.stream()
                .collect(Collectors.toMap(txnId -> txnId, txnId -> SCMServiceConstants.SCM_CONSTANT_NO));
        saveTransactionEvents(events, event, DayCloseEventLogType.B2B_RETURN);
    }

    private void saveTransactionEvents(Map<Integer, String> eventList, SCMDayCloseEventData event,
                                       DayCloseEventLogType eventType) throws DataUpdationException {
        try {

            for (Integer txnId : eventList.keySet()) {
                DayCloseTxnEventMapping eventMapping = new DayCloseTxnEventMapping();
                eventMapping.setEventType(eventType.name());
                eventMapping.setClosureEvent(event);
                eventMapping.setTxnId(txnId);
                eventMapping.setExternalTxn(eventList.get(txnId));
                stockManagementDao.add(eventMapping, false);
            }

        } catch (Exception e) {
            throw new DataUpdationException("Error while storing  mappings");
        }
    }

    private void addToTxnList(Map<String, DayCloseTxnItem> txnList, Integer skuId, String skuName, BigDecimal price,
                              BigDecimal qty) {
        DayCloseTxnItem item = null;
        String key = getKey(skuId, price);
        if (!txnList.keySet().contains(key)) {
            item = new DayCloseTxnItem();
            item.setSkuId(skuId);
            item.setName(skuName);
            item.setPrice(price); // TODO change it w.r.t to the pricing service
            item.setQty(SCMUtil.convertToBigDecimal(qty));
        } else {
            item = txnList.get(key);
            item.setQty(SCMUtil.add(qty, item.getQty()));
        }
        txnList.put(key, item);
    }

    private void saveTransactions(Map<String, DayCloseTxnItem> txnList, SCMDayCloseEventData eventData,
                                  DayCloseEventLogType eventLogType) throws DataUpdationException {

        try {
            for (DayCloseTxnItem txnItem : txnList.values()) {
                DayCloseTxnEventDrillDownData drillDownData = new DayCloseTxnEventDrillDownData();
                drillDownData.setEvent(eventData);
                drillDownData.setSkuId(txnItem.getSkuId());
                drillDownData.setSkuPrice(txnItem.getPrice());
                drillDownData.setSkuCost(SCMUtil.multiplyWithScale10(txnItem.getPrice(), txnItem.getQty()));
                drillDownData.setSkuQuantity(txnItem.getQty());
                drillDownData.setEventType(eventLogType.name());
                stockManagementDao.add(drillDownData, false);
            }
            stockManagementDao.flush();
        } catch (Exception e) {
            throw new DataUpdationException("Error while storing  mappings");
        }
    }

    private DayCloseEvent convert(SCMDayCloseEventData eventData) {
        DayCloseEvent event = null;
        if (eventData != null) {
            Unit unit = masterDataCache.getUnit(eventData.getUnitId());
            event = new DayCloseEvent();
            event.setId(eventData.getEventId());
            event.setBusinessDate(eventData.getBusinessDate());
            event.setUnit(SCMUtil.generateIdCodeName(unit.getId(), unit.getFamily().name(), unit.getName()));
            event.setStatus(StockEventStatus.valueOf(eventData.getStatus()));
            event.setType(StockEventType.valueOf(eventData.getDayCloseEventType()));
            event.setStockTakeType(eventData.getEventFrequencyType());
            if (eventData.getEventLogDataList() != null) {
                List<DayCloseEventLog> logs = eventData.getEventLogDataList().stream()
                        .filter(log -> log.getStatus().equals(SwitchStatus.ACTIVE.name())).map(this::convert)
                        .sorted((o1, o2) -> o2.getId() - o1.getId()) // sort in descending order of event done
                        .collect(Collectors.toList());

                event.getEventLogs().addAll(logs);
            }
            event.setSubCategories(eventData.getSubCategories());
        }
        return event;
    }

    private DayCloseEventLog convert(DayCloseEventLogData dayCloseEventLogData) {
        DayCloseEventLog eventLog = new DayCloseEventLog();
        eventLog.setId(dayCloseEventLogData.getId());
        eventLog.setCreatedAt(dayCloseEventLogData.getCreatedAt());
        Integer createdBy = dayCloseEventLogData.getCreatedBy();
        eventLog.setCreatedBy(SCMUtil.generateIdCodeName(createdBy, "", masterDataCache.getEmployee(createdBy)));
        eventLog.setStatus(SwitchStatus.valueOf(dayCloseEventLogData.getStatus()));
        eventLog.setType(DayCloseEventLogType.valueOf(dayCloseEventLogData.getEventCompleted()));
        return eventLog;
    }

    public String getKey(int skuId, BigDecimal price) {
        return skuId + "_" + price;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<RegularOrderUnitBrandData> getRoUnitBrandData(int unitId) {
        LOG.info("Getting RO unit brand data for unit is : {}",unitId);
        return referenceOrderManagementDao.getUnitOrderingSchedule(unitId,true);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<RegularOrderEvent> getRegularOrderingEvents(Integer unitId) {
        List<RegularOrderEvent> result = new ArrayList<>();
        try {
            SCMDayCloseEventData dayCloseEventData = referenceOrderManagementDao.findUnsuccessfulOrdering(unitId);
            if (Objects.nonNull(dayCloseEventData)) {
                for (RegularOrderingEvent regularOrderingEvent : dayCloseEventData.getRegularOrderingEvents()) {
                    if (checkTimeOfFulfilment(regularOrderingEvent)) {
                        if (regularOrderingEvent.getStatus().equalsIgnoreCase("CREATED")) {
                            RegularOrderEvent orderEvent = new RegularOrderEvent(regularOrderingEvent.getEventId(), regularOrderingEvent.getUnitId(), regularOrderingEvent.getBrand()
                                    , regularOrderingEvent.getFulfilmentDate(), regularOrderingEvent.getOrderingDays(), regularOrderingEvent.getStatus());
                            result.add(orderEvent);
                        }
                    }
                    else {
                        if (regularOrderingEvent.getStatus().equalsIgnoreCase("CREATED")) {
                            LOG.info("Updating the Event Status of event id : {} to expired at {}",regularOrderingEvent.getEventId(),AppUtils.getCurrentTimestamp());
                            regularOrderingEvent.setStatus("EXPIRED");
                            regularOrderingEvent.setUpdatedAt(AppUtils.getCurrentTimestamp());
                            stockManagementDao.update(regularOrderingEvent,true);
                            if (isAllEventsExpired(dayCloseEventData)) {
//                                reportingService.sendVarianceReport(dayCloseEventData.getBusinessDate(), dayCloseEventData.getUnitId(), false);
//                                reportingService.sendVarianceReport(dayCloseEventData.getBusinessDate(), dayCloseEventData.getUnitId(), true);
                                LOG.info("All Regular Ordering events got expired ..!");
                            }
                        }
                    }
                }
            }
        }
        catch (Exception e) {
            LOG.error("Error Occurred While getting Regular Ordering Events :: ",e);
        }
        return result;
    }

    private boolean isAllEventsExpired(SCMDayCloseEventData dayCloseEventData) {
        for (RegularOrderingEvent regularOrderingEvent : dayCloseEventData.getRegularOrderingEvents()) {
            LOG.info("Current event id is : {} and status is : {}",regularOrderingEvent.getEventId(),regularOrderingEvent.getStatus() );
            if (regularOrderingEvent.getStatus().equalsIgnoreCase("CREATED") || regularOrderingEvent.getStatus().equalsIgnoreCase("COMPLETED")) {
                LOG.info("Event is still in Created state :: {}",dayCloseEventData.getEventId());
                return false;
            }
        }
        return true;
    }

    private Boolean checkTimeOfFulfilment(RegularOrderingEvent regularOrderingEvent) {
        Date dateCheck = AppUtils.getUpdatedTimeInDate(9,30,0,AppUtils.getDayBeforeOrAfterDay(regularOrderingEvent.getFulfilmentDate(),-2));
        LOG.info("date check is : {} and fulfilment date is : {}",dateCheck,regularOrderingEvent.getFulfilmentDate());
        LOG.info("Current Time stamp is : {}",AppUtils.getCurrentTimestamp());
        if (AppUtils.getCurrentTimestamp().compareTo(dateCheck) <= 0) {
            LOG.info("Current Time stamp is : {} and returning true..!",AppUtils.getCurrentTimestamp());
            return true;
        }
        return false;
    }




}
