/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

/**
 * ProductRecipeCost generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "PRODUCT_RECIPE_COST_DATA", uniqueConstraints = @UniqueConstraint(columnNames = { "PRODUCT_ID", "DIMENSION", "COST_TYPE" }))
public class ProductRecipeCostData implements java.io.Serializable {

	private Integer productRecipeCostId;
	private int recipeId;
	private int productId;
	private String productSource;
	private String recipeName;
	private String dimension;
	private String costType;
	private BigDecimal cost;
	private Date lastUpdatedTime;
	private int unitId;

	/**
	 * @return the productRecipeCostId
	 */
	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "PRODUCT_RECIPE_COST_ID", unique = true, nullable = false)
	public Integer getProductRecipeCostId() {
		return productRecipeCostId;
	}

	/**
	 * @param productRecipeCostId
	 *            the productRecipeCostId to set
	 */
	public void setProductRecipeCostId(Integer productRecipeCostId) {
		this.productRecipeCostId = productRecipeCostId;
	}

	/**
	 * @return the recipeId
	 */
	@Column(name = "RECIPE_ID", nullable = false)
	public int getRecipeId() {
		return recipeId;
	}

	/**
	 * @param recipeId
	 *            the recipeId to set
	 */
	public void setRecipeId(int recipeId) {
		this.recipeId = recipeId;
	}

	/**
	 * @return the productSource
	 */
	@Column(name = "PRODUCT_SOURCE", nullable = false)
	public String getProductSource() {
		return productSource;
	}

	/**
	 * @param productSource the productSource to set
	 */
	public void setProductSource(String productSource) {
		this.productSource = productSource;
	}

	/**
	 * @return the productId
	 */
	@Column(name = "PRODUCT_ID", nullable = false)
	public int getProductId() {
		return productId;
	}

	/**
	 * @param productId
	 *            the productId to set
	 */
	public void setProductId(int productId) {
		this.productId = productId;
	}

	/**
	 * @return the recipeName
	 */
	@Column(name = "RECIPE_NAME", nullable = false)
	public String getRecipeName() {
		return recipeName;
	}

	/**
	 * @param recipeName
	 *            the recipeName to set
	 */
	public void setRecipeName(String recipeName) {
		this.recipeName = recipeName;
	}

	/**
	 * @return the dimension
	 */
	@Column(name = "DIMENSION", nullable = false)
	public String getDimension() {
		return dimension;
	}

	/**
	 * @param dimension
	 *            the dimension to set
	 */
	public void setDimension(String dimension) {
		this.dimension = dimension;
	}

	/**
	 * @return the dineInCost
	 */
	@Column(name = "COST", precision = 10)
	public BigDecimal getCost() {
		return cost;
	}

	/**
	 * @param dineInCost
	 *            the dineInCost to set
	 */
	public void setCost(BigDecimal dineInCost) {
		this.cost = dineInCost;
	}

	/**
	 * @return the lastUpdatedTime
	 */
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LAST_UPDATE_TMSTMP", nullable = false, length = 19)
	public Date getLastUpdatedTime() {
		return lastUpdatedTime;
	}

	/**
	 * @param lastUpdatedTime
	 *            the lastUpdatedTime to set
	 */
	public void setLastUpdatedTime(Date lastUpdatedTime) {
		this.lastUpdatedTime = lastUpdatedTime;
	}

	/**
	 * @return the costType
	 */
	@Column(name = "COST_TYPE", nullable = false)
	public String getCostType() {
		return costType;
	}

	/**
	 * @param costType
	 *            the costType to set
	 */
	public void setCostType(String costType) {
		this.costType = costType;
	}

	/**
	 * @return the unitId
	 */
	@Column(name = "UNIT_ID", nullable = false)
	public int getUnitId() {
		return unitId;
	}

	/**
	 * @param unitId
	 *            the unitId to set
	 */
	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}
}