package com.stpl.tech.scm.data.model;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 23-06-2017.
 */
@Entity
@Table(name = "WRITE_OFF_EVENT_LOG")
public class WriteOffItemData {

    private Integer id;
    private DayCloseEventLogData eventLogData;
    private Integer skuId;
    private BigDecimal skuPrice;
    private BigDecimal expectedValue;
    private BigDecimal correctedValue;


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "WRITE_OFF_LOG_ID", unique = true, nullable = false)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CLOSURE_EVENT_LOG_ID", nullable = false)
    public DayCloseEventLogData getEventLogData() {
        return eventLogData;
    }

    public void setEventLogData(DayCloseEventLogData eventLogData) {
        this.eventLogData = eventLogData;
    }

    @Column(name = "SKU_ID", nullable = false)
    public Integer getSkuId() {
        return skuId;
    }

    public void setSkuId(Integer skuId) {
        this.skuId = skuId;
    }

    @Column(name = "SKU_PRICE", nullable = false)
    public BigDecimal getSkuPrice() {
        return skuPrice;
    }

    public void setSkuPrice(BigDecimal skuPrice) {
        this.skuPrice = skuPrice;
    }

    @Column(name = "EXPECTED_VALUE", nullable = false)
    public BigDecimal getExpectedValue() {
        return expectedValue;
    }

    public void setExpectedValue(BigDecimal expectedValue) {
        this.expectedValue = expectedValue;
    }

    @Column(name = "CORRECTED_VALUE", nullable = false)
    public BigDecimal getCorrectedValue() {
        return correctedValue;
    }

    public void setCorrectedValue(BigDecimal correctedValue) {
        this.correctedValue = correctedValue;
    }
}

