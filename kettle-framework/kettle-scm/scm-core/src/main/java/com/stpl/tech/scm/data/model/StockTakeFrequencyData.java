package com.stpl.tech.scm.data.model;

import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "STOCKTAKE_FREQUENCY")
public class StockTakeFrequencyData {
    private Integer stockTakeFrequencyId;
    private String unitCategory;
    private String type;
    private String frequency;
    private Integer classificationId;
    private Integer inventoryListId;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "STOCKTAKE_FREQUENCY_ID", nullable = false, unique = true)
    public Integer getStockTakeFrequencyId() {
        return stockTakeFrequencyId;
    }

    public void setStockTakeFrequencyId(Integer stockTakeFrequencyId) {
        this.stockTakeFrequencyId = stockTakeFrequencyId;
    }

    @Column(name = "UNIT_CATEGORY", nullable = false)
    public String getUnitCategory() {
        return unitCategory;
    }

    public void setUnitCategory(String unitCategory) {
        this.unitCategory = unitCategory;
    }

    @Column(name = "TYPE")
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Column(name = "FREQUENCY", nullable = false)
    public String getFrequency() {
        return frequency;
    }

    public void setFrequency(String frequency) {
        this.frequency = frequency;
    }

    @Column(name = "CLASSIFICATION_ID", nullable = false)
    public Integer getClassificationId() {
        return classificationId;
    }

    public void setClassificationId(Integer classificationId) {
        this.classificationId = classificationId;
    }

    @Column(name = "INVENTORY_LIST_ID")
    public Integer getInventoryListId() {
        return inventoryListId;
    }

    public void setInventoryListId(Integer inventoryListId) {
        this.inventoryListId = inventoryListId;
    }
}
