package com.stpl.tech.scm.data.transport.model;


import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
public class UnitCapexDataSummary {

    public UnitCapexDataSummary() {}
    public UnitCapexDataSummary(Integer capexId, Integer unitId, String budgetType, BigDecimal totalBudgetAmount, BigDecimal totalRunningAmount, BigDecimal totalReceivedAmount, BigDecimal totalRemainingAmount, Date generatedTime, Date lastUpdatedTime, Integer generatedBy, Integer lastUpdatedBy, String status) {
        this.capexId = capexId;
        this.unitId = unitId;
        this.budgetType = budgetType;
        this.totalBudgetAmount = totalBudgetAmount;
        this.totalRunningAmount = totalRunningAmount;
        this.totalReceivedAmount = totalReceivedAmount;
        this.totalRemainingAmount = totalRemainingAmount;
        this.generatedTime = generatedTime;
        this.lastUpdatedTime = lastUpdatedTime;
        this.generatedBy = generatedBy;
        this.lastUpdatedBy = lastUpdatedBy;
        this.status = status;
    }

    Integer capexId;
   Integer unitId;
   String budgetType;
   BigDecimal totalBudgetAmount;
   BigDecimal totalRunningAmount;
   BigDecimal totalReceivedAmount;
   BigDecimal totalRemainingAmount;
    Date generatedTime;
    Date lastUpdatedTime;
    Integer generatedBy;
    Integer lastUpdatedBy;
    String  status;

    Long diffInDays;
}
