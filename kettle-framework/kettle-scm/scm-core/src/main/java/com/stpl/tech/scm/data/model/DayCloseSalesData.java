/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.data.model;


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.math.BigDecimal;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;


@Entity
@Table(name = "DAY_CLOSE_SALES_DATA")
public class DayCloseSalesData {

	private int id;
	private int unitId;
	private int brandId;
	private Date businessDate;
	private BigDecimal netSales;
	private String saleType;
	private BigDecimal salesPercentage;

	public DayCloseSalesData() {

	}

	public DayCloseSalesData(int unitId, int brandId, Date businessDate, BigDecimal netSales, String saleType, BigDecimal salesPercentage) {
		this.unitId = unitId;
		this.brandId = brandId;
		this.businessDate = businessDate;
		this.netSales = netSales;
		this.saleType = saleType;
		this.salesPercentage = salesPercentage;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "ID", unique = true, nullable = false)
	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	@Column(name = "UNIT_ID", nullable = false)
	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	@Column(name = "BRAND_ID")
	public int getBrandId() {
		return brandId;
	}

	public void setBrandId(int brandId) {
		this.brandId = brandId;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "BUSINESS_DATE")
	public Date getBusinessDate() {
		return businessDate;
	}

	public void setBusinessDate(Date businessDate) {
		this.businessDate = businessDate;
	}

	@Column(name = "NET_SALES")
	public BigDecimal getNetSales() {
		return netSales;
	}

	public void setNetSales(BigDecimal netSales) {
		this.netSales = netSales;
	}

	@Column(name = "SALE_TYPE")
	public String getSaleType() {
		return saleType;
	}

	public void setSaleType(String saleType) {
		this.saleType = saleType;
	}

	@Column(name = "SALES_PERCENTAGE")
	public BigDecimal getSalesPercentage() {
		return salesPercentage;
	}

	public void setSalesPercentage(BigDecimal salesPercentage) {
		this.salesPercentage = salesPercentage;
	}
}
