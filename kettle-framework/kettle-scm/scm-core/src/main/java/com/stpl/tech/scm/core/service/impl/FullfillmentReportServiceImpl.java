package com.stpl.tech.scm.core.service.impl;

import com.google.common.base.Stopwatch;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.FullfillmentReportGenerateService;
import com.stpl.tech.scm.core.service.FullfillmentReportService;
import com.stpl.tech.scm.data.dao.FullfillmentReportDao;
import com.stpl.tech.scm.data.transport.model.FullfillmentData;
import com.stpl.tech.scm.data.transport.model.FullfillmentDataUnitLevel;
import com.stpl.tech.scm.data.transport.model.FullfillmentDataWarehouseLevel;
import com.stpl.tech.scm.data.transport.model.MenuToScmData;
import com.stpl.tech.scm.notification.email.FullfillmentReportEmailNotification;
import com.stpl.tech.scm.notification.email.template.FullfillmentReportEmailNotificationTemplate;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.notification.AttachmentData;
import com.stpl.tech.util.notification.EmailNotification;
import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class FullfillmentReportServiceImpl implements FullfillmentReportService {

    private static final Logger LOG = LoggerFactory.getLogger(FullfillmentReportServiceImpl.class);
    @Autowired
    FullfillmentReportDao fullfillmentReportDao;

    @Autowired
    private EnvProperties env;

    @Autowired
    private FullfillmentReportGenerateService fullfillmentReportGenerateService;

    @Autowired
    MasterDataCache masterDataCache;

    private void buildMapFromResult(HashMap<Long, Set<Long>> transferUnitToRequestUnit,HashMap<Long, ArrayList<FullfillmentData>> requestUnitToScmProduct, List<FullfillmentData> result,Boolean removeBakeryProducts){
        List<Integer> bakeryProductIds = new ArrayList<>(Arrays.asList(100153,
                100246,
                100259,
                100776,
                100856,
                101303,
                103083,
                103188,
                103306,
                103307	));

        List<Long> LongBakeryProductIds =  bakeryProductIds.stream()
                .mapToLong(Integer::longValue)
                .boxed().collect(Collectors.toList());

        Set<Long> bakeryProductsSet = new HashSet<>(LongBakeryProductIds);

        for (FullfillmentData fd : result) {

            Long trasnferUnitToRequestUnitKey = fd.getTransferringUnitId();
            Set<Long> requestingUnitIds = transferUnitToRequestUnit.getOrDefault(trasnferUnitToRequestUnitKey, new HashSet<>());
            requestingUnitIds.add(fd.getRequestingUnitId());
            transferUnitToRequestUnit.put(trasnferUnitToRequestUnitKey, requestingUnitIds);

            Long requestUnitTOScmProductKey = fd.getRequestingUnitId();
            ArrayList<FullfillmentData> scmProdutcsData = requestUnitToScmProduct.getOrDefault(requestUnitTOScmProductKey, new ArrayList<>());

            if(Boolean.TRUE.equals(removeBakeryProducts) && !bakeryProductsSet.contains(fd.getProductId())){
                scmProdutcsData.add(fd);
            }

            if(bakeryProductsSet.contains(fd.getProductId())){
                fd.setIsBakeryProduct(true);
            }
            if (Boolean.FALSE.equals(removeBakeryProducts)){
                scmProdutcsData.add(fd);
            }

            requestUnitToScmProduct.put(requestUnitTOScmProductKey, scmProdutcsData);
        }
    }
    @Override
    public boolean startFullfillmentReportProcess() throws SumoException {

  try {
      LOG.info("################### Starting-Fullfillment-Report-Process ###################");
    List<FullfillmentData> result = fullfillmentReportDao.getFullfillmentData(false);

    Stopwatch watch = Stopwatch.createUnstarted();
    watch.start();
    // Map for (transferring_unit_id -> List(requesting_unit_id))
    HashMap<Long, Set<Long>> transferUnitToRequestUnit = new HashMap<>();

    // Map for (requesting_unit_id -> List(scm products))
    HashMap<Long, ArrayList<FullfillmentData>> requestUnitToScmProduct = new HashMap<>();

    buildMapFromResult(transferUnitToRequestUnit,requestUnitToScmProduct,result,false);

   createImpactedFFPer(transferUnitToRequestUnit,requestUnitToScmProduct);

    //-------- Fullfillment Percentage for MTD - Kitchen or Warehouse ---------
    ArrayList<FullfillmentDataWarehouseLevel> fullfillmentDataWarehouseLevelResultLastThirtyDay = fullfillmentProcessDataWarehouseLevel(transferUnitToRequestUnit,requestUnitToScmProduct);

    transferUnitToRequestUnit = new HashMap<>();
    requestUnitToScmProduct = new HashMap<>();
    buildMapFromResult(transferUnitToRequestUnit,requestUnitToScmProduct,result,true);
    createImpactedFFPer(transferUnitToRequestUnit,requestUnitToScmProduct);
    ArrayList<FullfillmentDataWarehouseLevel> fullfillmentDataWarehouseLevelResultLastThirtyDayWithoutBakery = fullfillmentProcessDataWarehouseLevel(transferUnitToRequestUnit,requestUnitToScmProduct);

    for(FullfillmentDataWarehouseLevel fd : fullfillmentDataWarehouseLevelResultLastThirtyDay){
        for(FullfillmentDataWarehouseLevel fd2 : fullfillmentDataWarehouseLevelResultLastThirtyDayWithoutBakery){
            if(Objects.equals(fd.getTransferingUnit(), fd2.getTransferingUnit())){
                fd.setWithoutBakeryAvgFPer(fd2.getAvgImFPer());
            }
        }
    }
      // ------------ get last day data -----------
    List<FullfillmentData> lastDayResult = fullfillmentReportDao.getFullfillmentData(true);


    // Map for (transferring_unit_id -> List(requesting_unit_id))
    transferUnitToRequestUnit = new HashMap<>();
    // Map for (requesting_unit_id -> List(scm products))
    requestUnitToScmProduct = new HashMap<>();

    // build map for last day results
    buildMapFromResult(transferUnitToRequestUnit,requestUnitToScmProduct,lastDayResult,false);
    //create impact for last day
    createImpactedFFPer(transferUnitToRequestUnit,requestUnitToScmProduct);

      String dateTime = new SimpleDateFormat("yyyy-MM-dd-HH-mm-ss").format(new Date());
      String fileName="Fullfillment_Reports(New)-"+dateTime+".xlsx";

      ArrayList<FullfillmentDataWarehouseLevel> fullfillmentDataWarehouseLevelResult = fullfillmentProcessDataWarehouseLevel(transferUnitToRequestUnit,requestUnitToScmProduct);
      ArrayList<FullfillmentDataUnitLevel> fullfillmentDataUnitLevelResult =   fullfillmentProcessUnitlevel(transferUnitToRequestUnit, requestUnitToScmProduct);

      List<FullfillmentDataUnitLevel> cafeResult = fullfillmentDataUnitLevelResult.stream().filter(d->{
          Unit u = masterDataCache.getUnit(Math.toIntExact(d.getRequestingUnitId()));
          return u != null && u.getFamily() == UnitCategory.CAFE;
      }).collect(Collectors.toList());
      Collections.sort(cafeResult, new Comparator<FullfillmentDataUnitLevel>() {
          @Override
          public int compare(FullfillmentDataUnitLevel c1, FullfillmentDataUnitLevel c2) {
              return Double.compare(c1.getImFPerWeightedAvg(), c2.getImFPerWeightedAvg());
          }
      });

      List<FullfillmentDataUnitLevel> mostImpacted = cafeResult.subList(0,21);

      transferUnitToRequestUnit = new HashMap<>();
      requestUnitToScmProduct = new HashMap<>();
      buildMapFromResult(transferUnitToRequestUnit,requestUnitToScmProduct,lastDayResult,true);
      createImpactedFFPer(transferUnitToRequestUnit,requestUnitToScmProduct);
      ArrayList<FullfillmentDataWarehouseLevel> fullfillmentDataWarehouseLevelResultWithoutBakery = fullfillmentProcessDataWarehouseLevel(transferUnitToRequestUnit,requestUnitToScmProduct);

      for(FullfillmentDataWarehouseLevel fd : fullfillmentDataWarehouseLevelResult){
          for(FullfillmentDataWarehouseLevel fd2 : fullfillmentDataWarehouseLevelResultWithoutBakery){
              if(Objects.equals(fd.getTransferingUnit(), fd2.getTransferingUnit())){
                  fd.setWithoutBakeryAvgFPer(fd2.getAvgImFPer());
              }
          }
      }


      fullfillmentReportGenerateService.generateWarehouseLevelSheet(fullfillmentDataWarehouseLevelResult, "Fullfillment Percentage - Kitchen and Warehouse", true,fileName);

      fullfillmentReportGenerateService.generateWarehouseLevelSheet(fullfillmentDataWarehouseLevelResultLastThirtyDay, "Fullfillment Percentage For This Month - Kitchen or Warehouse", false,fileName);

      fullfillmentReportGenerateService.generateDetailFullFillmentSheet(lastDayResult,fileName,"Fullfilment Details - Kitchen and Warehouse");
      fullfillmentReportGenerateService.generateUnitLevelDetailFullFillmentSheet(fullfillmentDataUnitLevelResult,fileName,"Fullfilment Details Unit level - Kitchen and Warehouse");



      sendEmail(fileName,fullfillmentDataWarehouseLevelResult,fullfillmentDataWarehouseLevelResultLastThirtyDay,mostImpacted);

    LOG.info("######## Fullfillment Reports Process Comppleted Successfully , Time : {}",watch.stop().elapsed(TimeUnit.MILLISECONDS));

    return true;
}catch (Exception e){
      LOG.error("######## Error in Fullfillment Report Process  : {}",e.getMessage());
      e.printStackTrace();
      throw new SumoException("Error in Fullfillment Report Process ");
}
    }

    private void createImpactedFFPer( HashMap<Long, Set<Long>> transferUnitToRequestUnit, HashMap<Long, ArrayList<FullfillmentData>> requestUnitToScmProduct) throws SumoException {
        for (Map.Entry<Long, Set<Long>> t : transferUnitToRequestUnit.entrySet()) {
            // get transfer unit id;
            Long transferUnit = t.getKey();
            Set<Long> requestingUnits = t.getValue();

            for (Long r : requestingUnits) {
                // All scm products for requesting_unit <- transfer_unit
                ArrayList<FullfillmentData> scm_products = requestUnitToScmProduct.get(r);

                List<FullfillmentData> criticalProducts = scm_products.stream().filter(p -> Objects.equals(p.getIsCritical(), "Y") && Objects.equals(p.getTransferringUnitId(), transferUnit)).collect(Collectors.toList());
                List<FullfillmentData> nonCriticalProducts = scm_products.stream().filter(p -> Objects.equals(p.getIsCritical(), "N") && Objects.equals(p.getTransferringUnitId(), transferUnit)).collect(Collectors.toList());

                // Map for scm_product_id -> Unique List(menu_product_id);
                HashMap<Long, Set<Long>> scmProductToMenuProduct = new HashMap<>();
                // Map for menu_product_id -> List(scm_products)
                HashMap<Long, ArrayList<MenuToScmData>> menuProductToScm = new HashMap<>();
                // get menu product data for requesting unit
                fullfillmentReportDao.getMenuToScmData(r, scmProductToMenuProduct, menuProductToScm);

                // get all impacted products for specific critical product
                for (FullfillmentData fdCritical : criticalProducts) {
                    Long criticalProductId = fdCritical.getProductId();
                    Set<Long> impactedScmProducts = new HashSet<>();
                    // get all menuProducts of the critical scm product
                    Set<Long> menuProducts = scmProductToMenuProduct.get(criticalProductId);

                    // for each menu product get the impacted scm product
                    for (Long mpId : menuProducts) {
                        ArrayList<MenuToScmData> products = menuProductToScm.get(mpId);
                        // get all impacted scm products of specific menu product
                        for (MenuToScmData m : products) {
                            if (!Objects.equals(m.getScmProductId(), criticalProductId)) {
                                impactedScmProducts.add(m.getScmProductId());
                            }
                        }
                    }
                    //set impacted scm products for specific critical product
                    fdCritical.setImpactedScmProducts(impactedScmProducts);
                }

                // make impact of critical product on non-critical product
                for (FullfillmentData fdCritical : criticalProducts) {
                    Double fullfillmentPerOfCriticalProduct = fdCritical.getFullfillmentPercentage() / 100;
                    Set<Long> impactedProducts = fdCritical.getImpactedScmProducts();
                    for (FullfillmentData fdNonCritical : nonCriticalProducts) {
                        if (impactedProducts.contains(fdNonCritical.getProductId())) {
                            if (fdNonCritical.getImpactedFullfillmentPercentage() == -1) {
                                Double fullfillPercentage = fdNonCritical.getFullfillmentPercentage();
                                Double impactedFfPercentage = (fullfillPercentage / 100) * fullfillmentPerOfCriticalProduct;
                                fdNonCritical.setImpactedFullfillmentPercentage(impactedFfPercentage * 100);
                            } else {
                                Double impfullfillPercentage = fdNonCritical.getImpactedFullfillmentPercentage();
                                Double impactedFfPercentage = (impfullfillPercentage / 100) * fullfillmentPerOfCriticalProduct;
                                fdNonCritical.setImpactedFullfillmentPercentage(impactedFfPercentage * 100);
                            }
                            fdNonCritical.setIsImpacted("Y");
                        } else {
                            fdNonCritical.setImpactedFullfillmentPercentage(fdNonCritical.getFullfillmentPercentage());
                            fdNonCritical.setIsImpacted("N");
                        }
                    }
                }

                // make impact of critical product on critical product
                for (FullfillmentData fdCritical : criticalProducts) {
                    Double fullfillmentPerOfCriticalProduct = fdCritical.getFullfillmentPercentage() / 100;
                    Set<Long> impactedProducts = fdCritical.getImpactedScmProducts();
                    for (FullfillmentData fdCriticalOther : criticalProducts) {
                        if (!Objects.equals(fdCritical.getProductId(), fdCriticalOther.getProductId()) && impactedProducts.contains(fdCriticalOther.getProductId())) {
                            if (fdCriticalOther.getImpactedFullfillmentPercentage() == -1) {
                                Double fullfillPercentage = fdCriticalOther.getFullfillmentPercentage();
                                Double impactedFfPercentage = (fullfillPercentage / 100) * fullfillmentPerOfCriticalProduct;
                                fdCriticalOther.setImpactedFullfillmentPercentage(impactedFfPercentage * 100);
                            } else {
                                Double impfullfillPercentage = fdCriticalOther.getImpactedFullfillmentPercentage();
                                Double impactedFfPercentage = (impfullfillPercentage / 100) * fullfillmentPerOfCriticalProduct;
                                fdCriticalOther.setImpactedFullfillmentPercentage(impactedFfPercentage * 100);
                            }
                            fdCriticalOther.setIsImpacted("Y");
                        } else {
                            fdCriticalOther.setImpactedFullfillmentPercentage(fdCriticalOther.getFullfillmentPercentage());
                            fdCriticalOther.setIsImpacted("N");
                        }
                    }
                }
            }
        }
    }

    private ArrayList<FullfillmentDataWarehouseLevel> fullfillmentProcessDataWarehouseLevel(HashMap<Long, Set<Long>> transferUnitToRequestUnit, HashMap<Long, ArrayList<FullfillmentData>> requestUnitToScmProduct){
        // ------------------ Fullfillment for last day  Details at unit level ------------------------
        ArrayList<FullfillmentDataUnitLevel> ffUnitLevelData = fullfillmentProcessUnitlevel(transferUnitToRequestUnit, requestUnitToScmProduct);
        // ---------------------------- Fullfillment for last date warehouse level -----------------------
        HashMap<Long, ArrayList<FullfillmentDataUnitLevel>> transferUnitToRequestUnitLevel = new HashMap<>();
        for (FullfillmentDataUnitLevel fd : ffUnitLevelData) {
            Long transferUnitToRequestUnitKey = fd.getTransferringUnitId();
            ArrayList<FullfillmentDataUnitLevel> requestingUnitData = transferUnitToRequestUnitLevel.getOrDefault(transferUnitToRequestUnitKey, new ArrayList<>());
            requestingUnitData.add(fd);
            transferUnitToRequestUnitLevel.put(transferUnitToRequestUnitKey, requestingUnitData);
        }
        ArrayList<FullfillmentDataWarehouseLevel> fullfillmentDataWarehouseLevelResult = new ArrayList<>();
        for (Map.Entry<Long, ArrayList<FullfillmentDataUnitLevel>> t : transferUnitToRequestUnitLevel.entrySet()) {
            Long transferUnit = t.getKey();
            ArrayList<FullfillmentDataUnitLevel> requestingUnits = t.getValue();
            FullfillmentDataWarehouseLevel fdWarehouseLevel = new FullfillmentDataWarehouseLevel();
            int count = 0;
            //Double sumF = 0.0;
            Double sumImF = 0.0;
            Double criticalSum = 0.0;
            Double bakerySum = 0.0;
            int criticalCount = 0;
            int bakeryCount = 0;
            for (FullfillmentDataUnitLevel r : requestingUnits) {
                if(count == 0){
                    fdWarehouseLevel.setTransferingUnit(r.getTransferringUnit());
                }
                ArrayList<FullfillmentData> products = requestUnitToScmProduct.get(r.getRequestingUnitId());
                List<FullfillmentData> transferUnitProducts = products.stream().filter(p-> Objects.equals(p.getTransferringUnitId(), transferUnit) && Objects.equals(p.getIsCritical(), "Y")).collect(Collectors.toList());

                for(FullfillmentData fd : transferUnitProducts){
                        criticalSum += (fd.getImpactedFullfillmentPercentage());
                        criticalCount++;
                }
                count++;
              //  sumF += r.getFPerWeightedAvg();
                sumImF += r.getImFPerWeightedAvg();
                if(r.getIsBakeryProduct()){
                    bakerySum += r.getBakeryProductFPAvg();
                    bakeryCount++;
                }
            }
            if(bakeryCount!=0){
                fdWarehouseLevel.setBakeryFP(bakerySum/bakeryCount);
            }else{
                fdWarehouseLevel.setBakeryFP((double) -1);
            }

          //  fdWarehouseLevel.setAvgFPer(sumF / count);
            if(criticalCount == 0){
                fdWarehouseLevel.setCriticalAvg(0.0);
                fdWarehouseLevel.setIsCriticalProd("N");
            }else{
                fdWarehouseLevel.setCriticalAvg((criticalSum/criticalCount));
                fdWarehouseLevel.setIsCriticalProd("Y");
            }
            if(count==0){
                fdWarehouseLevel.setAvgImFPer(0.0);
            }else{
             fdWarehouseLevel.setAvgImFPer(sumImF / count);
            }

            fullfillmentDataWarehouseLevelResult.add(fdWarehouseLevel);
        }
        return fullfillmentDataWarehouseLevelResult;
    }


    private ArrayList<FullfillmentDataUnitLevel> fullfillmentProcessUnitlevel(HashMap<Long, Set<Long>> transferUnitToRequestUnit, HashMap<Long, ArrayList<FullfillmentData>> requestUnitToScmProduct){
        ArrayList<FullfillmentDataUnitLevel> fullfillmentDataUnitLevelResult = new ArrayList<>();
        for(Map.Entry<Long,Set<Long>> t : transferUnitToRequestUnit.entrySet()){
            Long transferUnit = t.getKey();
            Set<Long> requestingUnits = t.getValue();
            for(Long r : requestingUnits){
                ArrayList<FullfillmentData> fdProductsData = requestUnitToScmProduct.get(r);
                List<FullfillmentData> fdData = fdProductsData.stream().filter(d-> Objects.equals(d.getTransferringUnitId(), transferUnit)).collect(Collectors.toList());
                if(!fdData.isEmpty()){
                    FullfillmentDataUnitLevel fdUnitLevel = new FullfillmentDataUnitLevel();
                    fdUnitLevel.setTransferringUnitId(transferUnit);
                    fdUnitLevel.setRequestingUnitId(r);
                    FullfillmentData fd = fdData.get(0);
                    fdUnitLevel.setTransferringUnit(fd.getTransferringUnit());
                    fdUnitLevel.setRequestingUnit(fd.getRequestingUnit());
                    Double qtySum =0.0;
                    Double fpSum = 0.0;
                    Double imFpSum = 0.0;
                    Double bakerySum = 0.0;
                    int bakeryCount = 0;
                    for(FullfillmentData f : fdData){
                        if(f.getIsBakeryProduct()){
                            bakerySum += f.getFullfillmentPercentage();
                            bakeryCount++;
                        }
                        qtySum += f.getRequestedAbsoluteQuantity();
                        fpSum += f.getRequestedAbsoluteQuantity() * f.getFullfillmentPercentage();
                        imFpSum += f.getRequestedAbsoluteQuantity() * f.getImpactedFullfillmentPercentage();
                    }
                    Double fullWeightedPercentage = fpSum/qtySum;
                    fdUnitLevel.setFPerWeightedAvg(fullWeightedPercentage);
                   if(bakeryCount!=0) {
                       fdUnitLevel.setBakeryProductFPAvg(bakerySum / bakeryCount);
                       fdUnitLevel.setIsBakeryProduct(true);
                   }else {
                       fdUnitLevel.setIsBakeryProduct(false);
                   }
                    Double impactedWeightedPercentage = imFpSum/qtySum;
                    if(impactedWeightedPercentage<0){
                        fdUnitLevel.setImFPerWeightedAvg(fullWeightedPercentage);
                    }else{
                        fdUnitLevel.setImFPerWeightedAvg(impactedWeightedPercentage);
                    }
                    fullfillmentDataUnitLevelResult.add(fdUnitLevel);
                }
            }
        }
            return fullfillmentDataUnitLevelResult;
    }

    private void sendEmail(String fileName,ArrayList<FullfillmentDataWarehouseLevel> lastDay, ArrayList<FullfillmentDataWarehouseLevel> lastThirtyDay, List<FullfillmentDataUnitLevel> mostImpacted) throws EmailGenerationException {

        byte[] barray = null;
        try {
            File file = new File(env.getBasePath()+"/"+fileName);
            ByteArrayOutputStream bos = new ByteArrayOutputStream();
            bos.write(FileUtils.readFileToByteArray(file));
            barray = bos.toByteArray();
        } catch (Exception e) {
          e.printStackTrace();
        }
        List<AttachmentData> attachments = new ArrayList<>();
        AttachmentData fullfillmentReport = null;
        String [] fileNameSplit = fileName.split("\\.");
        fullfillmentReport = new AttachmentData(barray,fileNameSplit[0] , AppConstants.EXCEL_MIME_TYPE);
        attachments.add(fullfillmentReport);

        FullfillmentReportEmailNotificationTemplate emailTemplate = new FullfillmentReportEmailNotificationTemplate(lastDay,lastThirtyDay,env.getBasePath(),mostImpacted);
        FullfillmentReportEmailNotification emailNotification = new FullfillmentReportEmailNotification(emailTemplate,env.getEnvType());
        emailNotification.sendRawMail(attachments);
        
        File fileToUpload = new File(env.getBasePath()+ "/"+fileName);
         boolean res = fileToUpload.delete();
        LOG.info("File deleted : {}",res);
    }
}
