package com.stpl.tech.scm.data.dao.impl;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;


import javax.persistence.Query;

import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.data.model.AssetDefinitionData;
import com.stpl.tech.scm.data.model.BusinessCostCenterData;
import com.stpl.tech.scm.domain.model.SCMOrderStatus;
import com.stpl.tech.scm.domain.model.StatusType;

import com.stpl.tech.scm.domain.model.StockEventStatusType;
import org.springframework.stereotype.Service;

import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.data.dao.SCMUnitManagementDao;
import com.stpl.tech.scm.data.model.FulfillmentUnitMappingData;
import com.stpl.tech.scm.data.model.UnitDistanceMappingData;

/**
 * Created by Rahul Singh on 07-05-2016.
 */
@Service
public class SCMUnitManagementDaoImpl extends SCMAbstractDaoImpl implements SCMUnitManagementDao {

	@Override
	public List<FulfillmentUnitMappingData> viewUnitsForAdhocRequest(String fulfillmentType, int unitId) {
		Query query = manager.createQuery(
				"FROM FulfillmentUnitMappingData f WHERE f.requestingUnitId = :requestingUnitId and f.fulfillmentType = :fulfillmentType");
		query.setParameter("requestingUnitId", unitId);
		query.setParameter("fulfillmentType", fulfillmentType);
		return query.getResultList();
	}

	@Override
	public List<FulfillmentUnitMappingData> getUnitToFulfillmentTypeMapping(int unitId) {
		Query query = manager
				.createQuery("FROM FulfillmentUnitMappingData f WHERE f.requestingUnitId = :requestingUnitId");
		query.setParameter("requestingUnitId", unitId);
		return query.getResultList();
	}

	@Override
	public List<UnitDistanceMappingData> getUnitDistanceMapping(int sourceUnitId, int destinationUnitId) {
		List<UnitDistanceMappingData> list = new ArrayList<UnitDistanceMappingData>();
		list.addAll(getMapping(sourceUnitId, destinationUnitId));
		list.addAll(getMapping(destinationUnitId, sourceUnitId));
		return list;
	}

	private List<UnitDistanceMappingData> getMapping(int sourceUnitId, int destinationUnitId) {
		Query query = manager.createQuery(
				"FROM UnitDistanceMappingData f WHERE f.sourceUnitId = :requestingUnitId and f.destinationUnitId = :fulfillmentType");
		query.setParameter("sourceUnitId", sourceUnitId);
		query.setParameter("destinationUnitId", destinationUnitId);
		return query.getResultList();
	}

	@Override
	public List<UnitDistanceMappingData> updateUnitDistanceMapping(List<UnitDistanceMappingData> list)
			throws SumoException {
		for (UnitDistanceMappingData data : list) {
			List<UnitDistanceMappingData> mapping = getMapping(data.getSourceUnitId(), data.getDestinationUnitId());
			if (mapping != null && !mapping.isEmpty()) {
				for (UnitDistanceMappingData d : mapping) {
					d.setDistance(data.getDistance());
				}
			} else {
				addMapping(data);
			}
		}
		flush();
		List<UnitDistanceMappingData> resultList = new ArrayList<UnitDistanceMappingData>();
		for (UnitDistanceMappingData data : list) {
			resultList.addAll(getMapping(data.getSourceUnitId(), data.getDestinationUnitId()));
		}
		return resultList;
	}

	@Override
	public List<BusinessCostCenterData> getBusinessCostCentresByUnitId(String unitId) {
		Query query = manager.createQuery("FROM BusinessCostCenterData b where b.code =:code");
		query.setParameter("code",unitId);
		return query.getResultList();
	}

	@Override
	public List<BusinessCostCenterData> getBusinessCostCentresByAccountTypeAndCompanyId(int companyId, String accountType, String businessCostCentreCode) {
		StringBuilder queryString = new StringBuilder(
				"FROM BusinessCostCenterData b WHERE  b.status = :status and b.companyId = :companyId ");
		if(Objects.nonNull(accountType)){
			if(SCMServiceConstants.UNIT.equalsIgnoreCase(accountType)){
				queryString.append(" and b.type=:type");
				queryString.append(" and b.code =:businessCostCentreCode");
			}else{
				queryString.append(" and b.type <>:type");
			}
		}
		Query query =manager.createQuery(queryString.toString());
		if(Objects.nonNull(accountType)){
			if(SCMServiceConstants.UNIT.equalsIgnoreCase(accountType)){
				query.setParameter("businessCostCentreCode",businessCostCentreCode);
				query.setParameter("type", UnitCategory.CAFE.value());
			}
		}else{
			query.setParameter("type", SCMServiceConstants.EMPLOYEE);
		}
		query.setParameter("status", StatusType.ACTIVE.value());
		query.setParameter("companyId",companyId);
		query.setParameter("type", UnitCategory.CAFE.value());
		return query.getResultList();
	}

	@Override
	public List<BusinessCostCenterData> getBusinessCostCentresByType(String businessCostCentreType) {
		StringBuilder data = new StringBuilder("FROM BusinessCostCenterData b where b.status =:status");
		if (Objects.nonNull(businessCostCentreType)) {
			data.append(" and  b.type =:type");
		}
		Query query = manager.createQuery(data.toString());
		if (Objects.nonNull(businessCostCentreType)) {
			query.setParameter("type", businessCostCentreType);
		}
		query.setParameter("status",StatusType.ACTIVE.value());
		return query.getResultList();
	}

	private void addMapping(UnitDistanceMappingData data) throws SumoException {
		UnitDistanceMappingData d = new UnitDistanceMappingData();
		d.setDestinationUnitId(data.getDestinationUnitId());
		d.setDistance(data.getDistance());
		d.setSourceUnitId(data.getSourceUnitId());
		d.setMappingId(null);
		add(d, false);
	}

	@Override
	public List<Pair<Pair<Integer, String>, Date>> getActiveStockTakeEvent(int unitId){
		List<Pair<Pair<Integer, String>, Date>>  activeEventList = new ArrayList<>();
		List<String> eventStatus = Arrays.asList(StockEventStatusType.IN_PROCESS.value(),StockEventStatusType.COMPLETED.value());
		Query query = manager.createQuery("SELECT eventId, eventStatus, eventCreationDate FROM StockEventDefinitionData s WHERE " +
				"s.unitType = :unitType " +
				"AND s.eventType = :eventType " +
				"AND s.subType = :subType " +
				"AND s.unitId = :unitId " +
				"AND s.eventStatus IN :eventStatus ");

		query.setParameter("unitType", "CAFE");
		query.setParameter("eventType" , "STOCK_TAKE");
		query.setParameter("subType","NSO");
		query.setParameter("unitId" , unitId);
		query.setParameter("eventStatus",eventStatus);

		List<Object[]> result = query.getResultList();
		for(Object[] o : result){
			activeEventList.add(new Pair<>(new Pair<>((Integer) o[0], (String) o[1]),(Date) o[2]));
		}

		return activeEventList;
	}

	@Override
	public List<Pair<AssetDefinitionData, String>> getAssetsToHandover(int unitId, int eventId){
		List<Pair<AssetDefinitionData,String>> assetDataList  = new ArrayList<>();

		try {
			Query query = manager.createQuery("SELECT ad , seam.assetStatus FROM  StockEventAssetMappingDefinitionData seam, AssetDefinitionData ad  " +
					"where seam.assetId = ad.assetId and " +
					"seam.unitId = :unitId AND seam.eventId = :eventId");

			query.setParameter("unitId",unitId);
			query.setParameter("eventId",eventId);
			List<Object[]> result = query.getResultList();
			for(Object[] o : result){
                 assetDataList.add(new Pair<>((AssetDefinitionData) o[0], (String) o[1]));
			}
		}catch (Exception e){

		}

		return assetDataList;

	}


}
