package com.stpl.tech.scm.core.mapper;

import com.stpl.tech.scm.data.model.AdvancePaymentAuditLogData;
import com.stpl.tech.scm.data.model.AdvancePaymentData;
import com.stpl.tech.scm.data.model.AdvancePaymentStatusLog;
import com.stpl.tech.scm.domain.model.VendorAdvancePayment;
import com.stpl.tech.scm.domain.model.VendorAdvancePaymentAuditLog;
import com.stpl.tech.scm.domain.model.VendorAdvancePaymentStatusLog;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface VendorAdvancePaymentMapper {

    VendorAdvancePaymentMapper INSTANCE = Mappers.getMapper(VendorAdvancePaymentMapper.class);

    @Mapping(source = "paymentRequestId.id", target = "paymentRequestId")
    @Mapping(source = "purchaseOrderData.id", target = "poId")
    @Mapping(source = "serviceOrderData.id", target = "soId")
    VendorAdvancePayment toVendorAdvancePayment(AdvancePaymentData advancePaymentData);

    @Mapping(source = "advancePaymentId.advancePaymentId", target = "advancePaymentId")
    VendorAdvancePaymentAuditLog toVendorAdvancePaymentLog(AdvancePaymentAuditLogData advancePaymentAuditLogData);

    @Mapping(source = "advancePaymentData.advancePaymentId", target = "advancePaymentDataId")
    VendorAdvancePaymentStatusLog toVendorAdvanceStatusLog(AdvancePaymentStatusLog advancePaymentStatusLog);
}
