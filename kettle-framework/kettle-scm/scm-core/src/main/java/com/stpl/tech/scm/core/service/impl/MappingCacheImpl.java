package com.stpl.tech.scm.core.service.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import com.stpl.tech.scm.domain.model.SkuDefinition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.stpl.tech.scm.core.service.MappingCache;
import com.stpl.tech.scm.core.service.MappingCacheService;
import com.stpl.tech.scm.data.model.ProductionUnitData;

@Service
public class MappingCacheImpl implements MappingCache {

	@Autowired
	private MappingCacheService service;
	
	private static final String MAPPING_CACHE_PRODUCTION_LINE_LOCK = "MAPPING_CACHE_PRODUCTION_LINE_LOCK";

	private static final String MAPPING_CACHE_SKU_ID_LOCK = "MAPPING_CACHE_SKU_ID_LOCK";

	private static final String MAPPING_CACHE_PRODUCTION_LINE_DATA_LOCK = "MAPPING_CACHE_PRODUCTION_LINE_DATA_LOCK";

	private static final String MAPPING_CACHE_SKU_LIST_OF_PRODUCT_ID_LOCK = "MAPPING_CACHE_SKU_LIST_OF_PRODUCT_ID_LOCK";

	private Map<Integer, Map<Integer, Integer>> productionLineMap= new HashMap<Integer,Map<Integer,Integer>>() ; // unit skuId prodLine

	private Map<Integer,Integer> skuIdMap= new HashMap<Integer,Integer>();  // productId skuId

	private Map<Integer,ProductionUnitData> productionUnitMap=new HashMap<>();

	private Map<Integer, List<SkuDefinition> > listOfSkuDefinition = new HashMap<>(); // product sku List

	@Override
	public Integer findProductionLine(int unitId, int skuId) {
		 //  using Lock
		if (!productionLineMap.containsKey(unitId)) {
			synchronized (MAPPING_CACHE_PRODUCTION_LINE_LOCK) {
				if (!productionLineMap.containsKey(unitId)) {
					productionLineMap.put(unitId, new HashMap<Integer, Integer>());
				}
			}
		}
		// then creating the Map
		if (!productionLineMap.get(unitId).containsKey(skuId)) {
			synchronized (MAPPING_CACHE_PRODUCTION_LINE_LOCK) {
				if (!productionLineMap.get(unitId).containsKey(skuId)) {
					int line = service.findProductionLine(unitId, skuId);
					productionLineMap.get(unitId).put(skuId, line);
				}
			}

		}
		return productionLineMap.get(unitId).get(skuId);
	}

	@Override
	public int findSKUID(int productId) {
		if (!skuIdMap.containsKey(productId)) {
			synchronized (MAPPING_CACHE_SKU_ID_LOCK) {
				if (!skuIdMap.containsKey(productId)) {
					int skuId = service.findSKUID(productId);
					skuIdMap.put(productId, skuId);
				}
			}
		}
		if (!skuIdMap.containsKey(productId)) {
			int skuId = service.findSKUID(productId);
			skuIdMap.put(productId, skuId);
		}
		return skuIdMap.get(productId);
	}

	@Override
	public ProductionUnitData findProductionLine(int productIonUnitId) {
		if (!productionUnitMap.containsKey(productIonUnitId)) {
			synchronized (MAPPING_CACHE_PRODUCTION_LINE_DATA_LOCK) {
				if (!productionUnitMap.containsKey(productIonUnitId)) {
					ProductionUnitData data = service.findProductionLine(productIonUnitId);
					productionUnitMap.put(productIonUnitId, data);
				}
			}
		}
		if (!productionUnitMap.containsKey(productIonUnitId)) {
			ProductionUnitData data = service.findProductionLine(productIonUnitId);
			productionUnitMap.put(productIonUnitId, data);
		}
		return productionUnitMap.get(productIonUnitId);
	}

	@Override
	public void removeProductionLine(int unitId, int skuId) {
		if (productionLineMap.containsKey(unitId) && productionLineMap.get(unitId).containsKey(skuId)) {
			productionLineMap.get(unitId).remove(skuId);
		}
	}

	@Override
	public void removeAllProductionLine() {
		productionLineMap.clear();
	}

	@Override
	public List<SkuDefinition> findAllSkuForProductId(int productId){
		if(!listOfSkuDefinition.containsKey(productId)){
			synchronized (MAPPING_CACHE_SKU_LIST_OF_PRODUCT_ID_LOCK){
				if(!listOfSkuDefinition.containsKey(productId)){
					List<SkuDefinition> data=service.findAllSkuDefinition(productId);
					listOfSkuDefinition.put(productId,data);
				}
			}
		}
		if(!listOfSkuDefinition.containsKey(productId)){
			List<SkuDefinition> data=service.findAllSkuDefinition(productId);
			listOfSkuDefinition.put(productId,data);
		}
		return listOfSkuDefinition.get(productId);
	}

	@Override
	public void removeSkuListForProduct(int productId) {
		if(listOfSkuDefinition.containsKey(productId) ){
			listOfSkuDefinition.remove(productId);
		}
		if(skuIdMap.containsKey(productId)){
			skuIdMap.remove(productId);
		}
	}

	@Override
	public void removeAllSkuDefinitionList(){
		listOfSkuDefinition.clear();
		skuIdMap.clear();
	}

}
