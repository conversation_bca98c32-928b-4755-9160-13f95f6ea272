package com.stpl.tech.scm.core.util.model;

import com.stpl.tech.scm.data.model.CostDetailDataCafe;
import com.stpl.tech.scm.data.model.CostDetailDataWh;

import java.util.ArrayList;
import java.util.List;

public class CostDataEntriesCafe extends CostDataEntries {
    private List<CostDetailDataCafe> data;
    private List<CostDetailDataCafe> errors;

    public List<CostDetailDataCafe> getData() {
        if (data == null) {
            data = new ArrayList<>();
        }
        return data;
    }

    public void setData(List<CostDetailDataCafe> data) {
        this.data = data;
    }

    public List<CostDetailDataCafe> getErrors() {
        if (errors == null) {
            errors = new ArrayList<>();
        }
        return errors;
    }

    public void setErrors(List<CostDetailDataCafe> errors) {
        this.errors = errors;
    }
}
