package com.stpl.tech.scm.data.dao;

import java.util.List;
import java.util.Set;

import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.scm.data.model.AgreementDetail;
import com.stpl.tech.scm.data.model.PurchaseOrderData;
import com.stpl.tech.scm.data.model.ServiceOrderData;
import com.stpl.tech.scm.data.model.UnitVendorMappingData;
import com.stpl.tech.scm.data.model.VendorComplianceData;
import com.stpl.tech.scm.data.model.VendorDetailData;
import com.stpl.tech.scm.data.model.VendorRegistrationRequestDetail;
import com.stpl.tech.scm.domain.model.SkuPriceDataObject;
import com.stpl.tech.scm.domain.model.SkuPriceHistoryObject;
import com.stpl.tech.scm.domain.model.VendorComplianceDataDTO;
import com.stpl.tech.scm.domain.model.VendorComplianceType;

/**
 * Created by Chaayos on 07-05-2016.
 */
public interface SCMVendorManagementDao extends SCMAbstractDao {

    public List<UnitVendorMappingData> getUnitVendorMappingByUnitId(int unitId);

	/**
	 * @param requestStatus
	 * @return
	 */
	public List<VendorDetailData> getAllVendor(String requestStatus);

	public List<VendorDetailData> getAllVendor();

	public List<VendorRegistrationRequestDetail> findCompletedRegistrationRequests(Integer vendorId);

	public Boolean checkIfVendorApprovedAtleastOnce(Integer vendorId);

	public List<AgreementDetail> getAllActiveAgreements(String type);

	public List<Pair<Integer,Integer>> getskuPackagingId(SkuPriceDataObject reqObj);

	public List<Integer> getSkuPriceDataId(SkuPriceDataObject reqObj);

	public List<SkuPriceHistoryObject> getPriceHistory(List<Integer> skuPriceDataId);

    public List<PurchaseOrderData> getPendingPos(Integer vendorId);

	List<ServiceOrderData> getPendingSos(Integer vendorId);

    List<VendorComplianceData> findVendorCompliancesByComplianceType(List<String> complianceTypes, Set<String> complianceKeys);

    List<VendorComplianceDataDTO> getLatestCompliances();
}
