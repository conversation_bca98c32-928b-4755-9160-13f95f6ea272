/*
 * Created By Shanmukh
 */

package com.stpl.tech.scm.data.mongo;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown = true)
public class BusinessAddressFields {
    @SerializedName("bnm")
    private String buildingName;

    @SerializedName("bno")
    private String doorNumber;

    @SerializedName("dst")
    private String district;

    @SerializedName("flno")
    private String floorNumber;

    @SerializedName("geocodelvl")
    private String geoCodeLevel;

    @SerializedName("landMark")
    private String landmark;

    @SerializedName("lg")
    private String longitude;

    @SerializedName("loc")
    private String location;

    @SerializedName("locality")
    private String locality;

    @SerializedName("lt")
    private String latitude;

    @SerializedName("pncd")
    private String pinCode;

    @SerializedName("st")
    private String street;

    @SerializedName("stcd")
    private String stateName;
}
