package com.stpl.tech.scm.data.dao;

import com.stpl.tech.scm.data.mongo.AuditChangeLog;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;


@Repository
public interface AuditChangeLogDao extends MongoRepository<AuditChangeLog, String> {



    @Query("{'keyId' : {'$eq' : ?0} , 'keyType' : {'$eq' : ?1 }}")
    public AuditChangeLog findByKeyId(Integer keyId , String keyType);



}
