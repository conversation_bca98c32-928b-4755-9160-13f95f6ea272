package com.stpl.tech.scm.notification.email.template;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.domain.model.Consignment;
import com.stpl.tech.scm.domain.model.EWayBill;
import com.stpl.tech.scm.domain.model.VehicleDispatch;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;

/**
 * Created by Chaayos on 31-03-2018.
 */
public class DispatchEWayRequestTemplate extends AbstractVelocityTemplate {

	private VehicleDispatch dispatchData;
	private String basePath;
	private List<EWayBill> bills;
	private String ewayRequired = "NOT REQUIRED";

	public DispatchEWayRequestTemplate() {

	}

	public DispatchEWayRequestTemplate(VehicleDispatch dispatchData, String basePath) {
		this.dispatchData = dispatchData;
		this.basePath = basePath;
		processData(dispatchData);
	}

	private void processData(VehicleDispatch data) {
		bills = new ArrayList<>();
		for (Consignment c : data.getConsignmentList()) {
			bills.addAll(c.getEwaybills());
		}

		for (EWayBill b : bills) {
			if (b.isEwayRequired()) {
				ewayRequired = "EWAY REQUIRED";
				break;
			}
		}
	}

	@Override
	public String getTemplatePath() {
		return "templates/DispatchEWayRequestTemplate.html";
	}

	@Override
	public String getFilepath() {
		return getDirectory() + dispatchData.getDispatchId() + "_" + SCMUtil.getDateString(SCMUtil.getCurrentDateIST())
				+ ".html";
	}

	public String getDirectory() {
		return basePath + "/dispatch/dispatchRequests/" + dispatchData.getDispatchId() + "/";
	}

	@Override
	public Map<String, Object> getData() {
		Map<String, Object> stringObjectMap = new HashMap<>();
		stringObjectMap.put("dispatch", dispatchData);
		stringObjectMap.put("bills", bills);
		stringObjectMap.put("ewayRequired", ewayRequired);
		return stringObjectMap;
	}

}
