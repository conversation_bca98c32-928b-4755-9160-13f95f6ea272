package com.stpl.tech.scm.notification.email.template;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.domain.model.RequestOrder;
import com.stpl.tech.scm.domain.model.RequestOrderItem;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;

/**
 * Created by Chaayos on 05-09-2016.
 */
public class VendorROEmailNotificationTemplate extends AbstractVelocityTemplate {

    private Map<Integer, List<RequestOrder>> requestOrderMap;
    private Map<Integer, Unit> unitMap;
    private VendorDetail vendorDetail;
    Map<Integer, RequestOrderItem> productQtyMap;
    private String basePath;
    private boolean isReceiving;

    public VendorROEmailNotificationTemplate(){

    }

    public VendorROEmailNotificationTemplate(Map<Integer, List<RequestOrder>> requestOrderMap, Map<Integer, Unit> unitMap,
                                             VendorDetail vendorDetail, Map<Integer, RequestOrderItem> productQtyMap, String basePath, boolean isReceiving){
        this.requestOrderMap = requestOrderMap;
        this.unitMap = unitMap;
        this.vendorDetail = vendorDetail;
        this.productQtyMap = productQtyMap;
        this.basePath = basePath;
        this.isReceiving = isReceiving;
    }

    @Override
    public String getTemplatePath() {
        return "templates/VendorROEmailTemplate.html";
    }

    @Override
    public String getFilepath() {
        return basePath + "/vendor/requestOrders/"+ vendorDetail.getVendorId()+ "/" +SCMUtil.getDateString(SCMUtil.getCurrentDateIST())+".html";
    }

    @Override
    public Map<String, Object> getData() {
        Map<String, Object> stringObjectMap = new HashMap<>();
        stringObjectMap.put("requestOrderMap", requestOrderMap);
        stringObjectMap.put("unitMap", unitMap);
        stringObjectMap.put("vendorDetail",vendorDetail);
        stringObjectMap.put("productQtyMap", productQtyMap);
        stringObjectMap.put("isReceiving", isReceiving);
        return stringObjectMap;
    }

    public VendorDetail getVendorDetail() {
        return vendorDetail;
    }

    public Map<Integer, Unit> getUnitMap() {
        return unitMap;
    }

    public Map<Integer, List<RequestOrder>> getRequestOrderMap() {
        return requestOrderMap;
    }

    public boolean isReceiving() {
        return isReceiving;
    }
}
