package com.stpl.tech.scm.data.model;

import javax.persistence.*;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "BULK_TRANSFER_EVENT")
public class BulkTransferEventData {

    private Integer bulkTransferEventId;
    private Integer roCount;
    private Integer toCount;
    private Integer generatedBy;
    private Integer sourceCompanyId;
    private Integer generationUnitId;
    private Date initiationTime;
    private Date completionTime;
    private String status;
    private String isInvoiceSet;
    private List<TransferOrderData> transferOrderDataList;
    private String type;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "BULK_TRANSFER_EVENT_ID",unique = true,nullable = false)
    public Integer getBulkTransferEventId() {
        return this.bulkTransferEventId;
    }

    public void setBulkTransferEventId(Integer bulkTransferEventId) {
        this.bulkTransferEventId = bulkTransferEventId;
    }

    @Column(name = "RO_COUNT")
    public Integer getRoCount() {
        return this.roCount;
    }

    public void setRoCount(Integer roCount) {
        this.roCount = roCount;
    }

    @Column(name = "TO_COUNT")
    public Integer getToCount() {
        return this.toCount;
    }

    public void setToCount(Integer toCount) {
        this.toCount = toCount;
    }

    @Column(name = "GENERATED_BY")
    public Integer getGeneratedBy() {
        return this.generatedBy;
    }

    public void setGeneratedBy(Integer generatedBy) {
        this.generatedBy = generatedBy;
    }

    @Column(name = "SOURCE_COMPANY_ID")
    public Integer getSourceCompanyId() {
        return this.sourceCompanyId;
    }

    public void setSourceCompanyId(Integer sourceCompanyId) {
        this.sourceCompanyId = sourceCompanyId;
    }

    @Column(name = "GENERATION_UNIT_ID")
    public Integer getGenerationUnitId() {
        return this.generationUnitId;
    }

    public void setGenerationUnitId(Integer generationUnitId) {
        this.generationUnitId = generationUnitId;
    }

    @Column(name = "INITIATION_TIME")
    public Date getInitiationTime() {
        return this.initiationTime;
    }

    public void setInitiationTime(Date initiationTime) {
        this.initiationTime = initiationTime;
    }

    @Column(name = "COMPLETION_TIME")
    public Date getCompletionTime() {
        return this.completionTime;
    }

    public void setCompletionTime(Date completionTime) {
        this.completionTime = completionTime;
    }

    @Column(name = "STATUS")
    public String getStatus() {
        return this.status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "IS_INVOICE_SET")
    public String getIsInvoiceSet() {
        return isInvoiceSet;
    }

    public void setIsInvoiceSet(String isInvoiceSet) {
        this.isInvoiceSet = isInvoiceSet;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "bulkTransferEventId")
    public List<TransferOrderData> getTransferOrderDataList() {
        return transferOrderDataList;
    }

    public void setTransferOrderDataList(List<TransferOrderData> transferOrderDataList) {
        this.transferOrderDataList = transferOrderDataList;
    }

    @Column(name = "TYPE")
    public String getType() {
        return this.type;
    }

    public void setType(String type) {
        this.type = type;
    }
}
