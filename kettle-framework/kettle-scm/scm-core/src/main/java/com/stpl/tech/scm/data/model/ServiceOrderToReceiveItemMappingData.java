package com.stpl.tech.scm.data.model;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "SERVICE_ORDER_RECEIVE_ITEM_MAPPING_DATA")
public class ServiceOrderToReceiveItemMappingData {

	private Integer id;
	private Integer serviceOrderItemDataId;
	private Integer serviceReceivedItemDataId;
	private BigDecimal quantity;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "MAPPING_ID", nullable = false, unique = true)
	public Integer getId() {
		return id;
	}

	public void setId(Integer mapping) {
		this.id = mapping;
	}

	@Column(name = "SERVICE_ORDER_ITEM_ID", nullable = false)
	public Integer getServiceOrderItemDataId() {
		return serviceOrderItemDataId;
	}

	public void setServiceOrderItemDataId(Integer serviceOrderItemData) {
		this.serviceOrderItemDataId = serviceOrderItemData;
	}

	@Column(name = "SERVICE_RECEIVED_ITEM_ID", nullable = false)
	public Integer getServiceReceivedItemDataId() {
		return serviceReceivedItemDataId;
	}

	public void setServiceReceivedItemDataId(Integer serviceReceivedItemData) {
		this.serviceReceivedItemDataId = serviceReceivedItemData;
	}

	@Column(name = "QUANTITY", nullable = false)
	public BigDecimal getQuantity() {
		return quantity;
	}

	public void setQuantity(BigDecimal quantity) {
		this.quantity = quantity;
	}

}
