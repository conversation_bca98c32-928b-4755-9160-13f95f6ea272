package com.stpl.tech.scm.notification.email.template;

import java.util.HashMap;
import java.util.Map;

import org.apache.velocity.tools.generic.DateTool;
import org.apache.velocity.tools.generic.MathTool;

import com.stpl.tech.master.domain.model.Company;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.model.ServiceOrderData;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.scm.domain.model.VendorDispatchLocation;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.NumberToWord;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;

/**
 * Created by Chaayos on 05-09-2016.
 */
public class VendorSOEmailNotificationTemplate extends AbstractVelocityTemplate {

	private ServiceOrderData serviceOrderData;
	private VendorDetail vendorDetail;
	private VendorDispatchLocation dispatchLocation;
	private String createdBy;
	private String basePath;
	private String company;
	private EnvType envType;
	private String approvedBy;
	private Boolean isDepartment ;
	private Map<Integer,String> deptInfo ;

	public VendorSOEmailNotificationTemplate() {

	}

	public VendorSOEmailNotificationTemplate(ServiceOrderData serviceOrderData,
			VendorDetail vendorDetail, VendorDispatchLocation dispatchLocation, String basePath, String createdBy,
			String company, EnvType envType, String approvedBy) {
		this.serviceOrderData = serviceOrderData;

		this.vendorDetail = vendorDetail;
		this.dispatchLocation = dispatchLocation;
		this.basePath = basePath;
		this.createdBy = createdBy;
		this.company = company;
		this.envType = envType;
		this.approvedBy = approvedBy;
	}

	@Override
	public String getTemplatePath() {
		return "templates/VendorSOEmailTemplate.html";
	}

	@Override
	public String getFilepath() {
		return basePath + "/vendor/serviceOrders/" + vendorDetail.getVendorId() + "/"
				+ serviceOrderData.getReceiptNumber() + ".html";
	}

	@Override
	public Map<String, Object> getData() {
		Map<String, Object> stringObjectMap = new HashMap<>();
		stringObjectMap.put("vendorDetail", vendorDetail);
		stringObjectMap.put("dispatchLocation", dispatchLocation);
		stringObjectMap.put("serviceOrderData", serviceOrderData);
		stringObjectMap.put("dateTool", new DateTool());
		stringObjectMap.put("mathTool", new MathTool());
		stringObjectMap.put("createdBy", this.createdBy);
		stringObjectMap.put("approvedBy", this.approvedBy);
		stringObjectMap.put("amountInWords",
				NumberToWord.getInstance().convertNumberToWords(serviceOrderData.getTotalAmount().intValue()));
		stringObjectMap.put("company", this.company);
		String link = SCMUtil.isDev(envType) ? "http://dev.kettle.chaayos.com:9595" : "https://relax.chaayos.com";
		link = link + "/scm-service/disclaimer.html";
		stringObjectMap.put("disclaimerLink", link);
		stringObjectMap.put("isDept" , this.isDepartment) ;
		stringObjectMap.put("departmentInfo",this.deptInfo) ;
		return stringObjectMap;
	}

	public VendorDetail getVendorDetail() {
		return vendorDetail;
	}

	public ServiceOrderData getServiceOrderData() {
		return serviceOrderData;
	}

	public VendorDispatchLocation getDispatchLocation() {
		return dispatchLocation;
	}

	public String getName() {
		return this.serviceOrderData.getId() + "";
	}

	public Map<Integer, String> getDeptInfo() {
		return deptInfo;
	}

	public void setDeptInfo(Map<Integer, String> deptInfo) {
		this.deptInfo = deptInfo;
	}

	public Boolean getDepartment() {
		return isDepartment;
	}

	public void setDepartment(Boolean department) {
		isDepartment = department;
	}


}
