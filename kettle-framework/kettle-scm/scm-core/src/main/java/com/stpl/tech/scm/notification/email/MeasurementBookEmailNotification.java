package com.stpl.tech.scm.notification.email;

import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.domain.model.VendorDetail;
import com.stpl.tech.scm.notification.email.template.MeasurementBookTemplate;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.EmailNotification;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class MeasurementBookEmailNotification extends EmailNotification {

    private MeasurementBookTemplate template;
    private EnvType envType;
    private String employeeName;
    private List<String> toEmails;


    public MeasurementBookEmailNotification() {

    }

    public MeasurementBookEmailNotification(MeasurementBookTemplate template, EnvType envType, String employeeName , List<String> toEmails ) {
        this.template = template;
        this.envType = envType;
        this.employeeName = employeeName;
        this.toEmails = toEmails;


    }

    @Override
    public String[] getToEmails() {

        if (SCMUtil.isDev(envType)) {
           return new String[] { "<EMAIL>" };
        } else {
            Set<String> mails = new HashSet<>();
            toEmails.forEach(email -> mails.add(email));
            VendorDetail vendorDetail = template.getVendorDetail();
            if (vendorDetail.getPrimaryEmail() != null) {
                mails.add(vendorDetail.getPrimaryEmail());
            }
            if (vendorDetail.getSecondaryEmail() != null) {
                mails.add(vendorDetail.getSecondaryEmail());
            }
            return mails.toArray(new String[0]);
        }

    }

    @Override
    public String getFromEmail() {
        return "<EMAIL>";
    }

    @Override
    public String subject() {
        String subject = "Service Receiving  Measurements are Sent By " + employeeName + " on "
                + SCMUtil.getFormattedTime(SCMUtil.getCurrentDateIST(), "EEE dd MMM yyyy");
        ;
        if (SCMUtil.isDev(envType)) {
            subject = "[Dev]" + subject;
        }
        return subject;
    }

    public String body() throws EmailGenerationException {
        try {
            return template.getContent();
        } catch (TemplateRenderingException e) {
            throw new EmailGenerationException("Failed to render the template", e);
        }
    }


    @Override
    public EnvType getEnvironmentType() {
        return envType;
    }


}
