<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<TransitionStateData xmlns="http://www.w3schools.com">
    <states>
        <stateCode>INITIATED</stateCode>
        <stateName>initiated</stateName>
        <stateType>START</stateType>
    </states>
    <states>
        <stateCode>PERFORMA_GENERATED</stateCode>
        <stateName>Generated</stateName>
        <stateType>INTERMEDIATE</stateType>
    </states>
    <states>
        <stateCode>PENDING_APPROVAL_L1</stateCode>
        <stateName>Approval level 1</stateName>
        <stateType>INTERMEDIATE</stateType>
    </states>
    <states>
        <stateCode>PENDING_APPROVAL_L2</stateCode>
        <stateName>Approval level 2</stateName>
        <stateType>INTERMEDIATE</stateType>
    </states>

    <states>
        <stateCode>APPROVED</stateCode>
        <stateName>Approved</stateName>
        <stateType>INTERMEDIATE</stateType>
    </states>
    <states>
        <stateCode>PENDING_DISPATCH</stateCode>
        <stateName>Processing</stateName>
        <stateType>INTERMEDIATE</stateType>
    </states>
    <states>
        <stateCode>PENDING_CANCEL_APPROVAL</stateCode>
        <stateName>Cancellation Approval</stateName>
        <stateType>INTERMEDIATE</stateType>
    </states>
    <states>
        <stateCode>CLOSED</stateCode>
        <stateName>Closed</stateName>
        <stateType>END</stateType>
    </states>
    <states>
        <stateCode>CANCELLED</stateCode>
        <stateName>Cancelled</stateName>
        <stateType>END</stateType>
    </states>
    <states>
        <stateCode>REJECTED</stateCode>
        <stateName>Rejected</stateName>
        <stateType>END</stateType>
    </states>

    <!--<transitions>
        <state>INITIATED</state>
        <nextStates>PERFORMA_GENERATED</nextStates>
        <nextStates>PENDING_DISPATCH</nextStates>
        <nextStates>CANCELLED</nextStates>
    </transitions>

    <transitions>
        <state>PERFORMA_GENERATED</state>
        <nextStates>PENDING_DISPATCH</nextStates>
        <nextStates>CANCELLED</nextStates>
    </transitions>

    <transitions>
        <state>PENDING_DISPATCH</state>
        <nextStates>CLOSED</nextStates>
    </transitions>-->



    <transitions>
        <state>INITIATED</state>
        <nextStates>PERFORMA_GENERATED</nextStates>
        <nextStates>APPROVED</nextStates>
        <nextStates>CANCELLED</nextStates>
        <nextStates>PENDING_APPROVAL_L1</nextStates>
    </transitions>
    <transitions>
        <state>PENDING_APPROVAL_L1</state>
        <nextStates>PENDING_APPROVAL_L2</nextStates>
        <nextStates>REJECTED</nextStates>
    </transitions>
    <transitions>
        <state>PENDING_APPROVAL_L2</state>
        <nextStates>REJECTED</nextStates>
        <nextStates>APPROVED</nextStates>
    </transitions>

    <transitions>
        <state>PERFORMA_GENERATED</state>
        <nextStates>APPROVED</nextStates>
        <nextStates>REJECTED</nextStates>
        <nextStates>CANCELLED</nextStates>
    </transitions>

    <transitions>
        <state>APPROVED</state>
        <nextStates>PENDING_DISPATCH</nextStates>
        <nextStates>CANCELLED</nextStates>

    </transitions>

    <transitions>
        <state>PENDING_DISPATCH</state>
        <nextStates>CLOSED</nextStates>
        <nextStates>CANCELLED</nextStates>
        <nextStates>PENDING_CANCEL_APPROVAL</nextStates>
    </transitions>
    <transitions>
        <state>PENDING_CANCEL_APPROVAL</state>
        <nextStates>CANCELLED</nextStates>
        <nextStates>PENDING_DISPATCH</nextStates>
        <nextStates>DELIVERED</nextStates>
        <nextStates>CLOSED</nextStates>
    </transitions>
    <transitions>
        <state>CANCELLED</state>
    </transitions>
    <transitions>
        <state>CLOSED</state>
        <nextStates>CANCELLED</nextStates>
        <nextStates>DELIVERED</nextStates>
        <nextStates>PENDING_CANCEL_APPROVAL</nextStates>
    </transitions>
    <transitions>
        <state>DELIVERED</state>
        <nextStates>CANCELLED</nextStates>
        <nextStates>PENDING_CANCEL_APPROVAL</nextStates>
        <nextStates>CORRECTION_APPROVAL</nextStates>
    </transitions>
    <transitions>
        <state>CORRECTION_APPROVAL</state>
        <nextStates>DELIVERED</nextStates>
        <nextStates>DELIVERED_WITH_CORRECTION</nextStates>
    </transitions>
</TransitionStateData>
