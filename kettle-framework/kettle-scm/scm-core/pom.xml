<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>kettle-scm</artifactId>
        <groupId>com.stpl.tech.kettle.scm</groupId>
        <version>5.0.22</version>
 		<relativePath>../pom.xml</relativePath>
    </parent>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>9</source>
                    <target>9</target>
                </configuration>
            </plugin>
        </plugins>
    </build>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>scm-core</artifactId>
    <packaging>jar</packaging>
    <properties>
        <spring-boot.repackage.skip>true</spring-boot.repackage.skip>
        <mapstruct.version>1.4.2.Final</mapstruct.version>
    </properties>
    <name>scm-core</name>
    <url>http://maven.apache.org</url>

    <dependencies>
        <dependency>
            <groupId>com.stpl.tech.kettle.scm</groupId>
            <artifactId>scm-domain</artifactId>
            <version>5.0.22</version>
        </dependency>
        <dependency>
		    <groupId>jakarta.persistence</groupId>
		    <artifactId>jakarta.persistence-api</artifactId>
		</dependency>
        <dependency>
            <groupId>com.stpl.tech.kettle.master</groupId>
            <artifactId>master-core</artifactId>
            <version>5.0.22</version>
        </dependency>
         <dependency>
            <groupId>com.stpl.tech.kettle.transaction</groupId>
            <artifactId>data-model</artifactId>
            <version>5.0.22</version>
        </dependency>
        <dependency>
		    <groupId>javax.servlet</groupId>
		    <artifactId>javax.servlet-api</artifactId>
		    <scope>provided</scope>
		</dependency>
		<dependency>
		    <groupId>org.aspectj</groupId>
		    <artifactId>aspectjrt</artifactId>
		</dependency>
        <dependency>
            <groupId>org.subtlelib</groupId>
            <artifactId>poi.builder</artifactId>
            <version>1.0.3</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-api</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
            <version>1.3.3</version>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.4</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf.tool</groupId>
            <artifactId>xmlworker</artifactId>
            <version>5.5.11</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>5.5.11</version>
        </dependency>
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>html2pdf</artifactId>
            <version>2.0.0</version>
        </dependency>
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-mongodb</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>org.apache.xmlbeans</groupId>
            <artifactId>xmlbeans</artifactId>
            <version>3.1.0</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>
    </dependencies>
</project>
