version: '1.0'
services:
  scm-service:
    image:
      scm-service:latest
    build:
      context: .
      dockerfile: Dockerfile
    environment:
      CATALINA_HOME: /usr/local/tomcat
    ports:
      - target: 8080
      - 8080:8080
      - 5701:5701
      - 5702:5702
      - 5703:5703
      - 5704:5704
    volumes:
      - /home/<USER>/logs:/usr/local/tomcat/logs

    container_name: scm-service
