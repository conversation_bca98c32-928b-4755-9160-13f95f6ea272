<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
    <title>Title</title>
    <style type="text/css">
        * {
            margin-top: -13px;
            margin-left: 0px;
            font-size: 11px;
        }

        .table {
            width: 100%;
            white-space: nowrap;
        }

        .table thead {
            border-bottom: #ccc 2px solid;
            white-space: nowrap;
        }

        .table td {
            border-top: #ccc 1px solid;
            white-space: nowrap;

            /*padding: 6px 2px;*/
        }

        .table thead td {
            font-weight: bold;
            white-space: nowrap;

        }

        .table tr:nth-child(2n) td {
            background: #f8f8f8;


        }

        .table tr:last-child td {
            font-weight: bold;


        }
        @page {
       page-break-inside: avoid;
       margin: 16mm 16mm 30mm 16mm;
}
    </style>
</head>
<body>

<div class="row" id="printSection">
    <div class="col s12">
        <p style="float: left;">
            <span style="font-size: 10.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">INVOICE ID - $data.invoice.id</span>
            <br/>
            <span style="font-size: 10.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">E-INVOICE ID - $data.invoice.uploadDocId</span>
            <br/>
            <b><em><span style="font-family: 'Cambria', serif;">Form GST INV &ndash; 1</span></em></b>
            <br/>
            <b><span style="font-size: 16.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">CHAAYOS<br/></span></b><b>
            <span style="font-size: 13.0pt; line-height: 115%; font-family: 'Cambria', serif;">$data.invoice.sendingCompany.name<br/></span></b>
            <span style="font-size: 10.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">
                $data.unitData.address.line1,
		#if ($data.unitData.address.line2),$data.unitData.address.line2, #end
                <br/> $data.unitData.address.city,
						$data.unitData.address.state,
                        $data.unitData.address.country,
						$data.unitData.address.zipCode
                <br/>
            </span>
            <b><span style="font-size: 13.0pt; line-height: 115%; font-family: 'Cambria', serif;">#if($data.invoice.type=='ECOM') DELIVERY_CHALLAN
                #else Tax Invoice #end</span></b>
        </p>
        <p style="float: right;">
            <span><img src="$data.barCodeLink" width="165px" height="165px"/></span>
        </p>
        <table style="border-collapse: collapse; border: none;"  cellpadding="0cm 5.4pt">
            <tbody>
            <tr style="height: 12.00pt; page-break-inside: avoid;">
                <td style="border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;"
                    colspan="9" width="765">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <b><span style="font-size: 10.0pt; font-family: 'Cambria', serif; text-align: left;">IRN Number&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></b>
                        <b><span style="font-size: 12.0pt; font-family: 'Cambria', serif; text-align: right;">$data.invoice.irnNo</span></b>
                    </p>
                </td>
            </tr>
            <tr style="height: 12.00pt; page-break-inside: avoid;">
                <td style="border: solid windowtext 1.0pt; border-top: 1pt solid windowtext; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    colspan="4" width="388">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <b><span style="font-size: 10.0pt; font-family: 'Cambria', serif; text-align: left;">EWAY Number&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></b>
                        <b><span style="font-size: 12.0pt; font-family: 'Cambria', serif; text-align: right;">$data.invoice.uploadedEwayNo</span></b>
                    </p>
                </td>
                <td style="border: solid windowtext 1.0pt; border-top: 1pt solid windowtext; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    colspan="4" width="388">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <b><span style="font-size: 10.0pt; font-family: 'Cambria', serif; text-align: left;">Ack Number&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></b>
                        <b><span style="font-size: 12.0pt; font-family: 'Cambria', serif; text-align: right;">$data.invoice.uploadedAckNo</span></b>
                    </p>
                </td>
            </tr>
            </tbody>
            <tbody>
            <tr style="height: 12.00pt; page-break-inside: avoid;">
                <td style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Sending Company Details</span>
                    </p>
                </td>
                <td style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <!--<span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Sending Address</span>-->
                    </p>
                </td>
                <td style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Receiving Vendor Details </span>
                    </p>
                </td>
                <td style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Shipped To</span>
                    </p>
                </td>
                <td style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Registered Address </span>
                    </p>
                </td>
                <td style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Billed To</span>
                    </p>
                </td>
            </tr>
            <tr style="height: 12.00pt; page-break-inside: avoid;">
                <td style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Unit Name</span>
                    </p>
                </td>
                <td style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.unitData.name</span>
                    </p>
                </td>
                <td style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Vendor Name </span>
                    </p>
                </td>
                <td style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.invoice.vendor.name</span>
                    </p>
                </td>
                <td style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Registered Vendor Name </span>
                    </p>
                </td>
                <td style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.invoice.vendor.name</span>
                    </p>
                </td>
            </tr>
            <tr style="height: 12.00pt; page-break-inside: avoid;">
                <td style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Company Name</span>
                    </p>
                </td>
                <td style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.company.name</span>
                    </p>
                </td>
                <td style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Company Name </span>
                    </p>
                </td>
                <td style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.invoice.vendor.name</span>
                    </p>
                </td>
                <td style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Registered Company Name </span>
                    </p>
                </td>
                <td style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.invoice.vendor.name</span>
                    </p>
                </td>
            </tr>
            <tr style="height: 12.00pt; page-break-inside: avoid;">
                <td style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Sending Address</span>
                    </p>
                </td>
                <td style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">
                                $data.invoice.dispatchAddress <br />
                            FSSAI Lic No - #if($data.invoice.sendingCompanyFssai) $data.invoice.sendingCompanyFssai
                            #else N/A #end
                        </span>
                    </p>
                </td>
                <td style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Shipping Address </span>
                    </p>
                </td>
                <td style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span
                                style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.invoice.deliveryAddress
                        </span>
                    </p>
                </td>
                <td style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Registered Address </span>
                    </p>
                </td>
                <td style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span
                                style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.invoice.billingAddress
                        </span>
                    </p>
                </td>
            </tr>
            <tr style="height: 12.00pt; page-break-inside: avoid;">
                <td style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">State/State Code</span>
                    </p>
                </td>
                <td style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.invoice.from.name/$data.invoice.from.code</span>
                    </p>
                </td>
                <td style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">State/State Code</span>
                    </p>
                </td>
                <td style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.invoice.to.name/$data.invoice.to.code</span>
                    </p>
                </td>
                <td style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">State/State Code</span>
                    </p>
                </td>
                <td style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <!--<span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.invoice.to.name/$data.invoice.to.code</span>-->
                    </p>
                </td>
            </tr>
            <tr style="height: 12.00pt; page-break-inside: avoid;">
                <td style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">GSTIN</span>
                    </p>
                </td>
                <td style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.unitData.tin</span>
                    </p>
                </td>
                <td style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">GSTIN</span>
                    </p>
                </td>
                <td style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.invoice.dispatchLocation.code</span>
                    </p>
                </td>
                <td style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Registered GSTIN</span>
                    </p>
                </td>
                <td style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <!--<span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.invoice.dispatchLocation.code</span>-->
                    </p>
                </td>
            </tr>
            <tr style="height: 12.00pt; page-break-inside: avoid;">
                <td style="width: 120.625px; border-right: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-top: none; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">CIN</span>
                    </p>
                </td>
                <td style="width: 250.625px; border-top: none; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.company.cin</span>
                    </p>
                </td>
                <td style="width: 120.625px; border-right: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-top: none; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">CIN</span>
                    </p>
                </td>
                <td style="width: 250.625px; border-top: none; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">#if($data.invoice.vendor.code) $data.invoice.vendor.code
                            #else N/A #end</span>
                    </p>
                </td>
                <td style="width: 120.625px; border-right: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-top: none; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Registered CIN</span>
                    </p>
                </td>
                <td style="width: 250.625px; border-top: none; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">#if($data.invoice.vendor.code) $data.invoice.vendor.code
                            #else N/A #end</span>
                    </p>
                </td>
            </tr>
            </tbody>
        </table>
        <!--<p>-->
            <!--<span style="font-size: 10.0pt; line-height: normal; font-family: 'Cambria', serif; color: black;">&nbsp;</span>-->
        <!--</p>-->

        <p>
            <span style="font-size: 10.0pt; line-height: normal; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
        </p>
        <table>
            <tbody>
            <tr style="height: 12.00pt; page-break-inside: avoid;">
                <td style="width: 386.625px; border-right: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-top: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;"
                    colspan="2">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <b><span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Transfer Details</span></b>
                    </p>
                </td>
                <td style="width: 120.625px; border-top: 1pt solid windowtext; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 14.15pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Invoice No.</span>
                    </p>
                </td>
                <td style="width: 250.625px; border-top: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 14.15pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.invoice.generatedId</span>
                    </p>
                </td>
            </tr>
            <tr style="height: 12.00pt; page-break-inside: avoid;">

                <td style="width: 120.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Total Items</span>
                    </p>
                </td>
                <td style="width: 250.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.invoice.items.size()</span>
                    </p>
                </td>

                <td style="width: 120.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Date of Invoice</span>
                    </p>
                </td>
                <td style="width: 250.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$date.format('medium', $data.invoice.dispatchDate)</span>
                    </p>
                </td>
            </tr>

            <tr style="height: 12.00pt; page-break-inside: avoid;">
                <td style="width: 136.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Place of Supply:</span>
                    </p>
                </td>
                <td style="width: 193.625px; border-top: none; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.invoice.dispatchLocation.name</span>
                    </p>
                </td>
                <td style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Mode of Transport: </span>
                    </p>
                </td>
                <td style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">By Road / By Train / By Air</span>
                    </p>
                </td>
            </tr>
            <tr style="height: 12.00pt; page-break-inside: avoid;">
                <td style="width: 120.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Purchase Order No: </span>
                    </p>
                </td>
                <td style="width: 193.625px; border-top: none; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                    <p style="margin: .0001pt 0; font-size: 10.0pt; line-height: normal;">$data.invoice.purchasedOrderNumber</p>
                </td>
                <td style="width: 136.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 14.15pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Vehicle No :</span>
                    </p>
                </td>
                <td style="width: 193.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 14.15pt;">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">____________________</span>
                    </p>
                </td>
            </tr>
            </tbody>
        </table>
        <!--<p>-->
            <!--<span style="font-size: 10.0pt; line-height: normal; font-family: 'Cambria', serif; color: black;">&nbsp;</span>-->
        <!--</p>-->

        <p>
            <span style="font-size: 10.0pt; line-height: normal; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
        </p>
        <table style="border-collapse: collapse; border: none;"  >
            <tbody>
            <tr style="height: 12.00pt;">
                <td style="width: 74.85pt; border: solid windowtext 1.0pt; border-left:  1pt solid windowtext; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    width="100" >
                    <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                        <b><span
                                style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">S.No</span></b>
                    </p>
                </td>
                <td style="width: 74.85pt; border: solid windowtext 1.0pt; border-left:  1pt solid windowtext; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    width="100" >
                    <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                        <b><span
                                style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Description</span></b>
                    </p>
                </td>
                <td style="width: 46.1pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    width="61" >
                    <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                        <b><span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">HSN</span></b>
                    </p>
                </td>
                <td style="width: 36.55pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    width="49" >
                    <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                        <b><span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Qty.</span></b>
                    </p>
                </td>
                <td style="width: 36.7pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    width="49" >
                    <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                        <b><span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Uom</span></b>
                    </p>
                </td>
                <td style="width: 50.5pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    >
                    <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                        <b><span
                                style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Price</span></b>
                    </p>
                </td>
                <td style="width: 63.35pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    width="84"  >
                    <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                        <b><span
                                style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Amount</span></b>
                    </p>
                </td>
                <td style=" border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    width="89"  >
                    <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                        <b><span
                                style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Packaging</span></b>
                    </p>
                </td>
                <td style="border-top: solid windowtext 1.0pt; border-left: none; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 14.15pt;"
                   width="132" >
                    <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                        <b><span
                                style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Taxes</span></b>
                    </p>
                </td>
                <td style="width: 66.8pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    width="89"   >
                    <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                        <b><span
                                style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Total</span></b>
                    </p>
                </td>
            </tr>
            <tbody style="display: block; page-break-inside: avoid;">
            #foreach( $item in $data.invoice.items )
            <tr style="height: 12.00pt;  page-break-inside: avoid;">
                <td style="width: 74.85pt; border: solid windowtext 1.0pt; border-left:  1pt solid windowtext; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    width="100" >
                    <p style="margin: .0001pt 0; line-height: normal; display: block; page-break-inside: avoid;">
                        $velocityCount
                    </p>
                </td>
                <td style="width: 74.85pt; border: solid windowtext 1.0pt; border-left:  1pt solid windowtext; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    width="100" >
                    <p style="margin: .0001pt 0; line-height: normal; display: block; page-break-inside: avoid;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">
                          #if($item.alias)  $item.alias
                            #else $item.sku.name #end
                        </span>
                    </p>
                </td>
                <td style="width: 46.1pt; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt; "
                    width="61" >
                    <p style="margin: .0001pt 0; line-height: normal; display: block; white-space: nowrap; page-break-inside: avoid;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$item.sku.code</span>
                    </p>
                </td>
                <td style="width: 36.55pt; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    width="49" >
                    <p style="margin: .0001pt 0; line-height: normal; display: block; page-break-inside: avoid;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.mathTool.roundTo(2,$item.qty)</span>
                    </p>
                </td>
                <td style="width: 36.7pt; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    width="49"  >
                    <p style="margin: .0001pt 0; line-height: normal; display: block; page-break-inside: avoid;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$item.uom</span>
                    </p>
                </td>
                <td style="width: 50.5pt; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    width="67" >
                    <p style="margin: .0001pt 0; line-height: normal; display: block; page-break-inside: avoid;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.mathTool.roundTo(2,$item.sellPrice)</span>
                    </p>
                </td>
                <td style="width: 63.35pt; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    width="84" >
                    <p style="margin: .0001pt 0; line-height: normal; display: block; page-break-inside: avoid;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.mathTool.roundTo(2,$item.sellAmount)</span>
                    </p>
                </td>

                <td style="border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    width="89"  >
                    <p style="margin: .0001pt 0; text-align: center; line-height: normal; display: block; page-break-inside: avoid;">
                        $data.mathTool.roundTo(2,$item.pkgQty) -- $item.pkg.name
                    </p>
                </td>

                <td style="border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                  width="132/$item.taxes.size()"  >
                    <p style="margin: .0001pt 0; line-height: normal; white-space: nowrap; display: block; page-break-inside: avoid;">
                        #foreach( $t in $item.taxes )
                        <span
                              style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black; page-break-inside: avoid;"> $data.mathTool.roundTo(2,$t.value) ($t.type@$data.mathTool.roundTo(2,$t.percent)%) </span><br/>
                        #end
                    </p>
                </td>
                <td style="width: 66.8pt; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    width="89" >
                    <p style="margin: .0001pt 0; line-height: normal; display: block; page-break-inside: avoid;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">
                            #set($totalAmount=$item.sellAmount + $item.totalTax)
                         $data.mathTool.roundTo(2,$totalAmount)
                        </span>
                    </p>
                </td>
            </tr>
            #end
        </tbody>
            <tr style="height: 12.00pt; page-break-inside: avoid;">
                <td style="width: 507.3pt; border: solid windowtext 1.0pt; border-top: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    colspan="8" width="676">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <b><span
                                style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">TOTAL</span></b>
                    </p>
                </td>
                <td style="width: 66.8pt; border-top: solid windowtext 1.0pt; border-left: none; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    width="89" >
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.mathTool.roundTo(2,$data.invoice.totalAmount)</span>
                    </p>
                </td>
            </tr>
            <tr style="height: 12.00pt; page-break-inside: avoid;">
                <td style="width: 507.3pt; border: solid windowtext 1.0pt; border-top: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    colspan="8" width="676">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <b><span style="font-size: 10.0pt; font-family: 'Cambria', serif;">Additional Charges (In figure)</span></b>
                    </p>
                </td>
                <td style="width: 66.8pt; border-top: none; border-left: none; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    width="89" >
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.mathTool.roundTo(2,$data.invoice.additionalCharges)</span>
                    </p>
                </td>
            </tr>
            <tr style="height: 12.00pt; page-break-inside: avoid;">
                <td style="width: 507.3pt; border: solid windowtext 1.0pt; border-top: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    colspan="8" width="676">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <b><span
                                style="font-size: 10.0pt; font-family: 'Cambria', serif;">Total Value (In figure)</span></b>
                    </p>
                </td>
                <td style="width: 66.8pt; border-top: none; border-left: none; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    width="89"   >
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">
                            #set($additionalWithAmount=$data.invoice.totalAmount + $data.invoice.additionalCharges)
                            $data.mathTool.roundTo(2,$additionalWithAmount)
                        </span>
                    </p>
                </td>
            </tr>
            <tr style="height: 12.00pt; page-break-inside: avoid;">
                <td style="border: solid windowtext 1.0pt; border-top: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                    colspan="9" width="765">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <b><span style="font-size: 10.0pt; font-family: 'Cambria', serif; text-align: left;">Total Challan Value (In Words)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></b>
                        <b><span style="font-size: 12.0pt; font-family: 'Cambria', serif; text-align: right;">$data.totalAmountInWords</span></b>
                    </p>
                </td>
            </tr>
            </tbody>
        </table>
        <p style="margin: .0001pt 0; line-height: normal;">
            <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
        </p>
        <p style="line-height: normal; margin-top: -13px">
            <b><span style="font-family: 'Cambria', serif;">Certified
                that the particulars and the amount indicated given above are
                true and correct </span></b>
        </p>
        <br/>
        <p>
            <span style="font-family: 'Cambria', serif;"><b> DISCLAIMER : I/We hereby certify that food/foods mentioned in this invoice is/are warranted to be of the nature and quality which it/these purports/purported to be </b></span>
        </p>
        <br />
        <table style="border-collapse: collapse; border: none;" >
            <tbody>
            <tr style="height: 20.00pt; page-break-inside: avoid;">
                <td style="width: 404.0pt; border: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 20.00pt;"
                    width="520"   >
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <b>
                            <span style="font-size: 14.0pt; font-family: 'Cambria', serif; color: black;">TERMS OF SALE</span>
                        </b>
                    </p>
                </td>
                <td style="width: 6.0cm; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 20.00pt;"
                    width="246"  >
                    <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                        <b><span style="font-family: 'Cambria', serif; color: black;">
                            For $data.company.name
                        </span></b>
                    </p>
                </td>
            </tr>
            <tr style="height: 73.6pt; page-break-inside: avoid;">
                <td style="width: 404.0pt; border: solid windowtext 1.0pt; border-top: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 73.6pt;"
                    width="510">
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-family: 'Cambria', serif;">1)Goods
                            once sold will not be taken back or exchanged&nbsp; </span>
                    </p>
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-family: 'Cambria', serif;">2)Seller is
                            not responsible for any loss or damaged of goods in transit</span>
                    </p>
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-family: 'Cambria', serif;">3)Buyer
                            undertakes to submit prescribed declaration to sender on
                            demand.</span>
                    </p>
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-family: 'Cambria', serif;">4)Disputes if
										any will be subject to seller court jurisdiction</span>
                    </p>
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-family: 'Cambria', serif;">5)If Payment Not received Within Due date Interest will be Charged @ 24% Per Annum</span>
                    </p>
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-family: 'Cambria', serif;">6)If any discrepancy in invoice for anything then revert within 3 days of invoice, thereafter no query will be accepted.</span>
                    </p>
                </td>
                <td style="width: 6.0cm; border-top: none; border-left: none; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 73.6pt;"
                    width="256"  >
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-family: 'Cambria', serif;">&nbsp; </span>
                    </p>
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-family: 'Cambria', serif;">&nbsp;</span>
                    </p>
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-family: 'Cambria', serif;">&nbsp;</span>
                    </p>
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-family: 'Cambria', serif;">&nbsp;</span>
                    </p>
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-family: 'Cambria', serif;">&nbsp;</span>
                    </p>
                    <p style="margin: .0001pt 0; line-height: normal;">
                        <span style="font-family: 'Cambria', serif;">&nbsp;</span>
                    </p>
                    <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                        <b><span style="font-family: 'Cambria', serif;">Authorised Signatory</span></b>
                    </p>
                </td>
            </tr>
            <!--<tr style="height: 17.45pt; page-break-inside: avoid;">-->
                <!--<td style="width: 404.0pt; border: solid windowtext 1.0pt; border-top: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 17.45pt;"-->
                    <!--width="520">-->
                    <!--<p style="margin: .0001pt 0; line-height: normal;">-->
                        <!--<span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">&nbsp;</span>-->
                    <!--</p>-->
                <!--</td>-->
                <!--<td style="width: 6.0cm; border-top: none; border-left: none; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 17.45pt;"-->
                    <!--&gt;-->
                    <!--<p style="margin: .0001pt 0; text-align: center; line-height: normal;"-->
                       <!--width="246">-->
                        <!--<b><span style="font-family: 'Cambria', serif;">Authorised Signatory</span></b>-->
                    <!--</p>-->
                <!--</td>-->
            <!--</tr>-->
            </tbody>
        </table>
        <p style="margin-top: 5px;">
            <span style="font-size: 10.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">
                Reg. Address: $data.company.registeredAddress.line1,
                        #if ($data.company.registeredAddress.line2),$data.company.registeredAddress.line2, #end
                            $data.company.registeredAddress.city,
                            $data.company.registeredAddress.state,
                            $data.company.registeredAddress.country,
                            Pin No: $data.company.registeredAddress.zipCode </span>
        </p>
    </div>
</div>

</body>
</html>
