
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
<title>Asset Lost Detected</title>
</head>
<body>
	<!--      header      -->
	<!--<div style="background-color: #213141; padding: 10px; text-align: center;">
                <img src="http://cdn.lmitassets.com/gograbon/images/merchant/chaayos.jpeg" />
            </div>-->

	<div
		style="padding: 0 30px 20px 30px; background-color: #f8f8f8; color: #000;">
		<div class="row">
			Asset Lost detected while Stock Taking at $data.AssetRecoveryDefinitionList.get(0).unitName $data.AssetRecoveryDefinitionList.get(0).unitId
		</div>
		<!--<div class="row"-->
			 <!--style="margin: 0; padding: 5px; border-bottom: #ddd 1px solid;">-->
			<!--<div class="col-xs-1"-->
				 <!--style="display: inline-block; vertical-align: top; width: 8%;">-->
				<!--Recovery Id-->
			<!--</div>-->
			<!--<div class="col-xs-1"-->
				 <!--style="display: inline-block; vertical-align: top; width: 8%;">-->
				<!--Asset Id-->
			<!--</div>-->
			<!--<div class="col-xs-2"-->
				 <!--style="display: inline-block; vertical-align: top; width: 24%;"> Asset Name</div>-->
			<!--<div class="col-xs-1"-->
				 <!--style="display: inline-block; vertical-align: top; width: 8%;">-->
				<!--Recovery Amount-->
			<!--</div>-->
			<!--<div class="col-xs-1"-->
				<!--style="display: inline-block; vertical-align: top; width: 8%;">-->
				<!--Employee Id-->
			<!--</div>-->
			<!--<div class="col-xs-1"-->
				 <!--style="display: inline-block; vertical-align: top; width: 8%;">Employee Name</div>-->
			<!--<div class="col-xs-1"-->
				 <!--style="display: inline-block; vertical-align: top; width: 8%;">Approved By Id</div>-->
			<!--<div class="col-xs-2"-->
				 <!--style="display: inline-block; vertical-align: top; width: 19%;">Approved By Name</div>-->
			<!--<div class="col-xs-1"-->
				 <!--style="display: inline-block; vertical-align: top; width: 8%;">-->
				<!--Event Id-->
			<!--</div>-->

		<!--</div>-->
		<br>
		#foreach( $item in $data.AssetRecoveryDefinitionList )
		<!--<div class="row"-->
			<!--style="margin: 0; padding: 5px; border-bottom: #ddd 1px solid;">-->
			<!--<div class="col-xs-1"-->
				<!--style="display: inline-block; vertical-align: top; width: 8%;">$item.assetRecoveryId</div>-->
			<!--<div class="col-xs-1"-->
				<!--style="display: inline-block; vertical-align: top; width: 8%;">$item.assetId</div>-->
			<!--<div class="col-xs-2"-->
				 <!--style="display: inline-block; vertical-align: top; width: 24%;">$item.assetDefinition.assetName</div>-->
			<!--<div class="col-xs-1"-->
				<!--style="display: inline-block; vertical-align: top; width: 8%;">&#8377 $item.recoveryAmount</div>-->
			<!--<div class="col-xs-1"-->
				 <!--style="display: inline-block; vertical-align: top; width: 8%;">$item.recoveryEmpId</div>-->
			<!--<div class="col-xs-1"-->
				 <!--style="display: inline-block; vertical-align: top; width: 8%;">$item.recoveryEmp.name</div>-->
			<!--<div class="col-xs-1"-->
				 <!--style="display: inline-block; vertical-align: top; width: 8%;">$item.approvedBy.id</div>-->
			<!--<div class="col-xs-2"-->
				 <!--style="display: inline-block; vertical-align: top; width: 19%;">$item.approvedBy.name</div>-->
			<!--<div class="col-xs-1"-->
				 <!--style="display: inline-block; vertical-align: top; width: 8%;">$item.eventId</div>-->
		<!--</div>-->
		<div class="row">
			<div>Recovery Id - $item.assetRecoveryId</div>
			<div>Asset Id - $item.assetId</div>
			<div>Asset Name - $item.assetDefinition.assetName</div>
			<div>Recovery Amount - $item.recoveryAmount</div>
			<div>Employee Id - $item.recoveryEmpId</div>
			<div>Employee Name - $item.recoveryEmp.name</div>
			<div>Approved By Id - $item.approvedBy.id</div>
			<div>Approved By Name - $item.approvedBy.name</div>
			<div>Event Id - $item.eventId</div>
			<div >Auditor Id -
				#if($item.auditedBy)$item.auditedBy.id#end
				#if(!$item.auditedBy)'NA'#end
			</div>
			<div>Auditor Name -
				#if($item.auditedBy)$item.auditedBy.name#end
				#if(!$item.auditedBy)'NA'#end
			</div>
		</div>
		<hr>
		#end


		<div style="margin-top: 50px;">
			<b>Note:</b> This is for reference purpose only.
		</div>
	</div>
</body>
</html>