<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
	<title>Credit Note</title>
	<style>
        .container {
            border: 20px solid black;
            padding: 20px; /* Optional: Add some padding to create space between content and border */
        }
      footer {
            background-color: #f2f2f2;
            padding: 10px;
            text-align: right;
            position: absolute;
            width: 100%;
            border-top: 1px solid #ccc;
        }
    </style>
</head>

<body>
<div id="container" style="outline: 2px solid black black;">
	<h3 style="text-align:center;font-size: 10.0pt;line-height: 115%;font-family: 'Cambria' "><b><u>Credit Note</u></b></h3>
	<p><span style="font-size: 10.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;"><b>Sunshine Teahouse Pvt Ltd</b></span></p>
	<hr style="background-color:black">
	<table >
		<tbody  style="font-size: 10.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">
		<tr >
			<td >Address:- </td>
			<td>
				$data.unitData.address.line1,
				#if ($data.unitData.address.line2),$data.unitData.address.line2, #end
				<br/> $data.unitData.address.city,
				$data.unitData.address.state,
				$data.unitData.address.country,
				$data.unitData.address.zipCode
			</td>
		</tr>
		<tr>
			<td>State Name:-</td>
			<td> $data.unitData.address.state</td>
		</tr>
		<tr>
			<td>GSTIN No :-</td>
			<td> $data.unitData.tin</td>
		</tr>
		</tbody>
	</table>
	<table style="border-collapse: collapse; border: none; width: 100%;">
		<tbody>
		<tr style="height: 12.00pt;">
			<td style="width: 74.85pt; border: solid windowtext 1.0pt; border-left:  1pt solid windowtext; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
				width="100" >
				<p style="margin: .0001pt 0; text-align: center; line-height: normal;">
					<b><span
							style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Billing Address</span></b>
				</p>
			</td>
			<td style="width: 74.85pt; border: solid windowtext 1.0pt; border-left:  1pt solid windowtext; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
				width="100" >
				<p style="margin: .0001pt 0; text-align: center; line-height: normal;">
					<b><span
							style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Shipping Address</span></b>
				</p>
			</td>
		</tr>
		<tbody style="display: block; page-break-inside: avoid;">
		<tr style="height: 12.00pt;  page-break-inside: avoid;">
			<td style="width: 46.1pt; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt; ">
				<p style="margin: .0001pt 0; line-height: normal; display: block; white-space: nowrap; page-break-inside: avoid;">
					<span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.invoice.billingAddress</span>
				</p>
			</td>
			<td style="width: 46.1pt; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt; ">
				<p style="margin: .0001pt 0; line-height: normal; display: block; white-space: nowrap; page-break-inside: avoid;">
					<span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.invoice.deliveryAddress</span>
				</p>
			</td>
		</tr>
		<tr style="height: 12.00pt;  page-break-inside: avoid;">
			<td style="width: 46.1pt; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt; ">
				<table style="border-collapse: collapse; border: none;">
					<tbody>
					<tr style="height: 12.00pt;">
						<td>
							<p style="margin: .0001pt 0; text-align: center; line-height: normal;">
								<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">State:</span></b>
							</p>
						</td>
						<td>
							<p style="margin: .0001pt 0; text-align: center; line-height: normal;">
								<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.invoice.vendorDispatchLocation.state</span></b>
							</p>
						</td>
					</tr>
					<tr style="height: 12.00pt;">
						<td>
							<p style="margin: .0001pt 0; text-align: center; line-height: normal;">
								<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">GSTIN:</span></b>
							</p>
						</td>
						<td>
							<p style="margin: .0001pt 0; text-align: center; line-height: normal;">
								<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.invoice.vendorDispatchLocation.gstin</span></b>
							</p>
						</td>
						<td></td>
						<td></td>
					</tr>
					</tbody>
				</table>
			</td>
			<td style="width: 46.1pt; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt; ">
				<table style="border-collapse: collapse; border: none;">
					<tbody>
					<tr style="height: 12.00pt;">
						<td>
							<table style="border-collapse: collapse; border: none;">
								<tbody>
								<tr style="height: 12.00pt;">
									<td>
										<p style="margin: .0001pt 0; text-align: left; line-height: normal;">
											<b><span
											style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Credit Note No.</span></b>
										</p>
									</td>
									<td>
										<p style="margin: .0001pt 0; text-align: left; line-height: normal;">
										<b><span
											style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">CN/STPL/2324/$data.creditNoteNo</span></b>
										</p>
									</td>
								</tr>
								<tr style="height: 12.00pt;">
									<td>
										<p style="margin: .0001pt 0; text-align: left; line-height: normal;">
											<b><span
													style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Purchase Order:</span></b>
										</p>
									</td>
									<td>
										<p style="margin: .0001pt 0; text-align: left; line-height: normal;">
											<b><span
													style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">-</span></b>
										</p>
									</td>
								</tr>
								<tr style="height: 12.00pt;">
									<td>
										<p style="margin: .0001pt 0; text-align: left; line-height: normal;">
											<b><span
													style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Order ID:</span></b>
										</p>
									</td>
									<td>
										<p style="margin: .0001pt 0; text-align: left; line-height: normal;">
											<b><span
													style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">-</span></b>
										</p>
									</td>
								</tr>
								<tr style="height: 12.00pt;">
									<td>
										<p style="margin: .0001pt 0; text-align: left; line-height: normal;">
											<b><span
													style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Place:</span></b>
										</p>
									</td>
									<td>
										<p style="margin: .0001pt 0; text-align: left; line-height: normal;">
											<b><span
													style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.invoice.vendorDispatchLocation.city</span></b>
										</p>
									</td>
								</tr>
								</tbody>
							</table>
						</td>
						<td>
							<table style="border-collapse: collapse; border: none;">
								<tbody>
								<tr style="height: 12.00pt;">
									<td>
										<p style="margin: .0001pt 0; text-align: left; line-height: normal;">
											<b><span
													style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Credit Note Date:</span></b>
										</p>
									</td>
									<td>
										<p style="margin: .0001pt 0; text-align: left; line-height: normal;">
											<b><span
													style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.businessDate</span></b>
										</p>
									</td>
								</tr>
								<tr style="height: 12.00pt;">
									<td>
										<p style="margin: .0001pt 0; text-align: center; line-height: normal;">
											<b><span
													style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Invoice Date:</span></b>
										</p>
									</td>
									<td>
										<p style="margin: .0001pt 0; text-align: center; line-height: normal;">
											<b><span
													style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.invoice.purchaseOrderDate</span></b>
										</p>
									</td>
								</tr>
								<tr style="height: 12.00pt;">
									<td>
										<p style="margin: .0001pt 0; text-align: center; line-height: normal;">
											<b><span
													style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Invoice No:</span></b>
										</p>
									</td>
									<td>
										<p style="margin: .0001pt 0; text-align: center; line-height: normal;">
											<b><span
													style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.barCodeLink</span></b>
										</p>
									</td>
									<td></td>
								</tr>
								</tbody>
							</table>
						</td>
					</tr>
					</tbody>
				</table>
			</td>
		</tr>
		</tbody>
	</table>

	<table style="border-collapse: collapse; border: none;"  >
		<tbody>
		<tr style="height: 12.00pt;">
			<td style="width: 74.85pt; border: solid windowtext 1.0pt; border-left:  1pt solid windowtext; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
				width="100" >
				<p style="margin: .0001pt 0; text-align: center; line-height: normal;">
					<b><span
							style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">S.No</span></b>
				</p>
			</td>
			<td style="width: 74.85pt; border: solid windowtext 1.0pt; border-left:  1pt solid windowtext; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
				width="100" >
				<p style="margin: .0001pt 0; text-align: center; line-height: normal;">
					<b><span
							style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Description</span></b>
				</p>
			</td>
			<td style="width: 46.1pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
				width="61" >
				<p style="margin: .0001pt 0; text-align: center; line-height: normal;">
					<b><span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">HSN</span></b>
				</p>
			</td>
			<td style="width: 36.55pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
				width="49" >
				<p style="margin: .0001pt 0; text-align: center; line-height: normal;">
					<b><span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Qty.</span></b>
				</p>
			</td>
			<td style="width: 36.7pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
				width="49" >
				<p style="margin: .0001pt 0; text-align: center; line-height: normal;">
					<b><span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Uom</span></b>
				</p>
			</td>
			<td style="width: 50.5pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
			>
				<p style="margin: .0001pt 0; text-align: center; line-height: normal;">
					<b><span
							style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Price</span></b>
				</p>
			</td>
			<td style="width: 63.35pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
				width="84"  >
				<p style="margin: .0001pt 0; text-align: center; line-height: normal;">
					<b><span
							style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Amount</span></b>
				</p>
			</td>
			<td style=" border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
				width="89"  >
				<p style="margin: .0001pt 0; text-align: center; line-height: normal;">
					<b><span
							style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Packaging</span></b>
				</p>
			</td>
			<td style="border-top: solid windowtext 1.0pt; border-left: none; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 14.15pt;"
				width="132" >
				<p style="margin: .0001pt 0; text-align: center; line-height: normal;">
					<b><span
							style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Taxes</span></b>
				</p>
			</td>
			<td style="width: 66.8pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
				width="89"   >
				<p style="margin: .0001pt 0; text-align: center; line-height: normal;">
					<b><span
							style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Total</span></b>
				</p>
			</td>
		</tr>
		<tbody style="display: block; page-break-inside: avoid;">
		#foreach( $item in $data.invoice.items )
		<tr style="height: 12.00pt;  page-break-inside: avoid;">
			<td style="width: 74.85pt; border: solid windowtext 1.0pt; border-left:  1pt solid windowtext; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
				width="100" >
				<p style="margin: .0001pt 0; line-height: normal; display: block; page-break-inside: avoid;">
					$velocityCount
				</p>
			</td>
			<td style="width: 74.85pt; border: solid windowtext 1.0pt; border-left:  1pt solid windowtext; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
				width="100" >
				<p style="margin: .0001pt 0; line-height: normal; display: block; page-break-inside: avoid;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">
                          #if($item.alias)  $item.alias
                            #else $item.sku.name #end
                        </span>
				</p>
			</td>
			<td style="width: 46.1pt; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt; "
				width="61" >
				<p style="margin: .0001pt 0; line-height: normal; display: block; white-space: nowrap; page-break-inside: avoid;">
					<span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$item.sku.code</span>
				</p>
			</td>
			<td style="width: 36.55pt; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
				width="49" >
				<p style="margin: .0001pt 0; line-height: normal; display: block; page-break-inside: avoid;">
					<span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.mathTool.roundTo(2,$item.qty)</span>
				</p>
			</td>
			<td style="width: 36.7pt; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
				width="49"  >
				<p style="margin: .0001pt 0; line-height: normal; display: block; page-break-inside: avoid;">
					<span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$item.uom</span>
				</p>
			</td>
			<td style="width: 50.5pt; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
				width="67" >
				<p style="margin: .0001pt 0; line-height: normal; display: block; page-break-inside: avoid;">
					<span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.mathTool.roundTo(2,$item.sellPrice)</span>
				</p>
			</td>
			<td style="width: 63.35pt; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
				width="84" >
				<p style="margin: .0001pt 0; line-height: normal; display: block; page-break-inside: avoid;">
					<span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.mathTool.roundTo(2,$item.sellAmount)</span>
				</p>
			</td>

			<td style="border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
				width="89"  >
				<p style="margin: .0001pt 0; text-align: center; line-height: normal; display: block; page-break-inside: avoid;">
					$data.mathTool.roundTo(2,$item.pkgQty) -- $item.pkg.name
				</p>
			</td>

			<td style="border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
				width="132/$item.taxes.size()"  >
				<p style="margin: .0001pt 0; line-height: normal; white-space: nowrap; display: block; page-break-inside: avoid;">
					#foreach( $t in $item.taxes )
					<span
							style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black; page-break-inside: avoid;"> $data.mathTool.roundTo(2,$t.value) ($t.type@$data.mathTool.roundTo(2,$t.percent)%) </span><br/>
					#end
				</p>
			</td>
			<td style="width: 66.8pt; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
				width="89" >
				<p style="margin: .0001pt 0; line-height: normal; display: block; page-break-inside: avoid;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">
                            #set($totalAmount=$item.sellAmount + $item.totalTax)
                         $data.mathTool.roundTo(2,$totalAmount)
                        </span>
				</p>
			</td>
		</tr>
		#end
	</tbody>
	</table>
	<table style="border-collapse: collapse; border: none; width: 100%;">
		<tbody style="display: block; page-break-inside: avoid;">
		<tr style="height: 12.00pt;  page-break-inside: avoid;">
			<td style="width: 46.1pt; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt; ">
				<table style="border-collapse: collapse; border: none;">
					<tbody>
					<tr style="height: 12.00pt;">
						<td>
							<p style="margin: .0001pt 0; text-align: left; line-height: normal;">
								<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Bank Name</span></b>
							</p>
						</td>
						<td>
							<p style="margin: .0001pt 0; text-align: left; line-height: normal;">
								<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Kotak Mahindra Bank</span></b>
							</p>
						</td>
					</tr>
					<tr style="height: 12.00pt;">
						<td>
							<p style="margin: .0001pt 0; text-align: left; line-height: normal;">
								<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Account Number</span></b>
							</p>
						</td>
						<td>
							<p style="margin: .0001pt 0; text-align: left; line-height: normal;">
								<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">**********</span></b>
							</p>
						</td>
					</tr>
					<tr style="height: 12.00pt;">
						<td>
							<p style="margin: .0001pt 0; text-align: left; line-height: normal;">
								<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">IFSC CODE</span></b>
							</p>
						</td>
						<td>
							<p style="margin: .0001pt 0; text-align: left; line-height: normal;">
								<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">KKBK0000298</span></b>
							</p>
						</td>
					</tr>
					<tr style="height: 12.00pt;">
						<td>
							<p style="margin: .0001pt 0; text-align: left; line-height: normal;">
								<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Address</span></b>
							</p>
						</td>
						<td>
							<p style="margin: .0001pt 0; text-align: left; line-height: normal;">
								<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Gurgaon Vatika Business Park Badshapur, <br/>
									Gurgaon, Haryana, India</span></b>
							</p>
						</td>
					</tr>
					</tbody>
				</table>
			</td>
			<td style="width: 46.1pt; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt; ">
				<table style="border-collapse: collapse; border: none;">
					<tbody>
					<tr style="height: 12.00pt;">
						<td>
							<p style="margin: .0001pt 0; text-align: left; line-height: normal;">
								<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">TOTAL</span></b>
							</p>
						</td>
						<td>
							<p style="margin: .0001pt 0; text-align: left; line-height: normal;">
								<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.mathTool.roundTo(2,$data.invoice.totalAmount)</span></b>
							</p>
						</td>
					<tr style="height: 12.00pt;">
						<td>
							<p style="margin: .0001pt 0; text-align: left; line-height: normal;">
								<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Additional Charges (In figure)</span></b>
							</p>
						</td>
						<td>
							<p style="margin: .0001pt 0; text-align: left; line-height: normal;">
								<b><span
										style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.mathTool.roundTo(2,$data.invoice.additionalCharges)</span></b>
							</p>
						</td>
					</tr>
					</tbody>
				</table>
			</td>
		</tr>
		</tbody>
	</table>
	</tbody>
	</table>
	<table style="width: 100%; ">
		<tr style="width: 100%;">
			<td style="width: 30%;"></td>
			<td style="width: 30%;"></td>
			<td style="width: 25%; ">
				<p>Sunshine teahouse</p>
				<p>Authorised Signature</p>
			</td>
			<td style="width: 25%;">
				<img alt="Chaayos Free Desi Chai" src="$data.basePath/b2b_invoice_signature.png" width="100px" /><br/>
			</td>
		</tr>
	</table>

</div></body></html>