<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head></head>
<body>
<div>
    <h3 style="text-align:center;margin-bottom:0;margin-top:5pt;text-decoration: underline;font-weight:700;">Vendor Advances</h3>
    <table border="1" style="width:100%;border-spacing: 0;border-color: #ccc;">
        <tbody>
        <tr>
            #foreach ($advEntry in ["PENDING_REFUND","BLOCKED_VENDORS","RUNNING_VENDOR_ADVANCES"])
            <td colspan="4" style="padding:5pt;text-align:left;">
                <p><b>$advEntry ($data.vendorAdvancePayments.get($advEntry).size()) :</b> $data.advanceData.get($advEntry)</p>
            </td>
            #end
        </tr>
        </tbody>
    </table>
    <br>

    #foreach ($advType in ["PENDING_REFUND","BLOCKED_VENDORS","RUNNING_VENDOR_ADVANCES"])
    #set( $advanceType = $advType)
    #set( $advanceList = $data.vendorAdvancePayments.get($advType))
    <h3 style="text-align:left;margin-bottom:0;margin-top:5pt;font-weight:700;">$advType</h3>
    <table border="1" style="width:100%;border-spacing: 0;border-color: #ccc;">
        <thead>
            <tr>
                <th style="text-align:center;margin-bottom:0;margin-top:5pt;text-decoration: underline;font-weight:700;">Advance Payment Id</th>
                <th style="text-align:center;margin-bottom:0;margin-top:5pt;text-decoration: underline;font-weight:700;">PR Id</th>
                <th style="text-align:center;margin-bottom:0;margin-top:5pt;text-decoration: underline;font-weight:700;">Advance Type</th>
                <th style="text-align:center;margin-bottom:0;margin-top:5pt;text-decoration: underline;font-weight:700;">Vendor Name</th>
                <th style="text-align:center;margin-bottom:0;margin-top:5pt;text-decoration: underline;font-weight:700;">Available Amount</th>
                <th style="text-align:center;margin-bottom:0;margin-top:5pt;text-decoration: underline;font-weight:700;">PO Id</th>
                <th style="text-align:center;margin-bottom:0;margin-top:5pt;text-decoration: underline;font-weight:700;">Pending GR's</th>
                <th style="text-align:center;margin-bottom:0;margin-top:5pt;text-decoration: underline;font-weight:700;">SO Id</th>
                <th style="text-align:center;margin-bottom:0;margin-top:5pt;text-decoration: underline;font-weight:700;">Pending SR's</th>
                <th style="text-align:center;margin-bottom:0;margin-top:5pt;text-decoration: underline;font-weight:700;">Pending PR's</th>
                <th style="text-align:center;margin-bottom:0;margin-top:5pt;text-decoration: underline;font-weight:700;">Created By</th>
                #if ($advanceType == 'PENDING_REFUND')
                <th style="text-align:center;margin-bottom:0;margin-top:5pt;text-decoration: underline;font-weight:700;">Refund Initiated By</th>
                <th style="text-align:center;margin-bottom:0;margin-top:5pt;text-decoration: underline;font-weight:700;">Refund Date</th>
                #end
                <th style="text-align:center;margin-bottom:0;margin-top:5pt;text-decoration: underline;font-weight:700;">Max Settlement time</th>
                <th style="text-align:center;margin-bottom:0;margin-top:5pt;text-decoration: underline;font-weight:700;">Remaining Days</th>
            </tr>
        </thead>
        <tbody>
        #foreach ($advEntry in $advanceList)
        <tr>
            <td>$advEntry.advancePaymentId</td>
            <td>$advEntry.paymentRequestId</td>
            <td>$advEntry.advanceType</td>
            <td>$data.vendorDetailMap.get($advEntry.vendorId).entityName</td>
            <td>$advEntry.availableAmount</td>
            <td>#if($advEntry.poId) $advEntry.poId #else - #end</td>
            <td>#if($advEntry.pendingGrs && $advEntry.pendingGrs.size() > 0) $advEntry.pendingGrs #else - #end</td>
            <td>#if($advEntry.soId) $advEntry.soId #else - #end</td>
            <td>#if($advEntry.pendingSrs && $advEntry.pendingSrs.size() > 0) $advEntry.pendingSrs #else - #end</td>
            <td>#if($advEntry.pendingPrs && $advEntry.pendingPrs.size() > 0) $advEntry.pendingPrs #else - #end</td>
            <td>$advEntry.createdBy</td>
            #if ($advanceType == 'PENDING_REFUND')
            <td>$advEntry.refundInitiatedByName</td>
            <td>$advEntry.refundDate</td>
            #end
            <td>$advEntry.maxSettlementTime</td>
            <td>$advEntry.diffDays</td>
        </tr>
        #end
        </tbody>
    </table>
    <br>
    #end
</div>
</body>
</html>