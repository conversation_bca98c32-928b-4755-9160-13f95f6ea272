<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
    <title>Credit Note</title>
    <style>
        .container {
            border: 20px solid black;
            padding: 20px;
        }
        footer {
            background-color: #f2f2f2;
            padding: 10px;
            text-align: right;
            position: absolute;
            width: 100%;
            border-top: 1px solid #ccc;
        }
    </style>
</head>

<body>
<div id="container" style="outline: 2px solid black;">
    <h3 style="text-align:center;font-size: 20.0pt; "><b><u>Credit Note</u></b></h3>
    <h3 style="text-align:center;font-size: 20.0pt;margin-bottom: 20px ">Sunshine Teahouse Pvt Ltd</h3>
    <hr>
    <table >
        <tbody  style="font-size: 10.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">
        <tr>
            <td><b>Address:- </b></td>
            <td>
                $data.unitData.address.line1,
                #if ($data.unitData.address.line2),$data.unitData.address.line2, #end
                <br/> $data.unitData.address.city,
                $data.unitData.address.state,
                $data.unitData.address.country,
                $data.unitData.address.zipCode
            </td>
        </tr>
        <tr>
            <td><b>State Name:- </b></td>
            <td> $data.unitData.address.state</td>
        </tr>
        <tr>
            <td><b>GSTIN No :- </b></td>
            <td> $data.unitData.tin</td>
        </tr>
        </tbody>
    </table>

    <table style="border-collapse: collapse; border: none; width: 100%;">
        <tbody>
        <tr style="height: 12.00pt;width: 100%;">
            <td style="width: 50%; border: solid windowtext 1.0pt; border-left:  1pt solid windowtext; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;">
                <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                    <b><span
                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Billing Address</span></b>
                </p>
            </td>
            <td style="width: 50%; border: solid windowtext 1.0pt; border-left:  1pt solid windowtext; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;">
                <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                    <b><span
                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Shipping Address</span></b>
                </p>
            </td>
        </tr>
        <tbody style="display: block; page-break-inside: avoid;">
        <tr style="height: 12.00pt;  page-break-inside: avoid;width: 100%;">
            <td style="width: 50%; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt; ">
                <p style="margin: .0001pt 0; line-height: normal; display: block; white-space: nowrap; page-break-inside: avoid;">
                    <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">#if($data.invoice.billingAddress) $data.invoice.billingAddress #else - #end</span>
                </p>
            </td>
            <td style="width: 50%; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt; ">
                <p style="margin: .0001pt 0; line-height: normal; display: block; white-space: nowrap; page-break-inside: avoid;">
                    <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">#if($data.invoice.deliveryAddress) $data.invoice.deliveryAddress #else - #end</span>
                </p>
            </td>
        </tr>
        <tr style="height: 12.00pt;  page-break-inside: avoid;width: 100%;">
            <td style="width: 50%; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt; ">
                <table style="border-collapse: collapse; border: none;">
                    <tbody>
                    <tr style="height: 12.00pt;">
                        <td>
                            <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                <b><span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">State:</span></b>
                            </p>
                        </td>
                        <td>
                            <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                <span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">#if($data.invoice.vendorDispatchLocation.state) $data.invoice.vendorDispatchLocation.state #else - #end</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt;">
                        <td>
                            <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                <b><span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">GSTIN:</span></b>
                            </p>
                        </td>
                        <td>
                            <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                <span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">#if($data.invoice.vendorDispatchLocation.gstin) $data.invoice.vendorDispatchLocation.gstin #else - #end</span>
                            </p>
                        </td>
                        <td></td>
                        <td></td>
                    </tr>
                    </tbody>
                </table>
            </td>
            <td style="width: 50%; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;flex: 1 ">
                <table style="border-collapse: collapse; border: none;flex: 1;width: 100%">
                    <tbody style="flex: 1;width: 100%;">
                    <tr style="height: 12.00pt;flex: 1;width: 100%;">
                        <td style="flex: 1;width: 50%;">
                            <table style="border-collapse: collapse; border: none;">
                                <tbody>
                                <tr style="height: 12.00pt;">
                                    <td>
                                        <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                            <b><span
                                                    style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Credit Note No.:</span></b>
                                        </p>
                                    </td>
                                    <td>
                                        <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                            <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">CN/STPL/2324/$data.creditNoteNo</span>
                                        </p>
                                    </td>
                                </tr>
                                <tr style="height: 12.00pt;">
                                    <td>
                                        <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                            <b><span
                                                    style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Purchase Order Id:</span></b>
                                        </p>
                                    </td>
                                    <td>
                                        <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                            <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;"></span>
                                        </p>
                                    </td>
                                </tr>
                                <tr style="height: 12.00pt;">
                                    <td>
                                        <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                            <b><span
                                                    style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Purchase Order Date:</span></b>
                                        </p>
                                    </td>
                                    <td>
                                        <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                            <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;"></span>
                                        </p>
                                    </td>
                                </tr>
                                <tr style="height: 12.00pt;">
                                    <td>
                                        <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                            <b><span
                                                    style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Place:</span></b>
                                        </p>
                                    </td>
                                    <td>
                                        <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                            <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;"></span>
                                        </p>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </td>
                        <td  style="flex: 1;width: 100%;">
                            <table style="border-collapse: collapse; border: none;">
                                <tbody>
                                <tr style="height: 12.00pt;">
                                    <td>
                                        <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                            <b><span
                                                    style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Credit Note Date:</span></b>
                                        </p>
                                    </td>
                                    <td>
                                        <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                            <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.creditNoteDate</span>
                                        </p>
                                    </td>
                                </tr>
                                <tr style="height: 12.00pt;">
                                    <td>
                                        <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                            <b><span
                                                    style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Invoice Date:</span></b>
                                        </p>
                                    </td>
                                    <td>
                                        <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                            <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.invoiceDate</span>
                                        </p>
                                    </td>
                                </tr>
                                <tr style="height: 12.00pt;">
                                    <td>
                                        <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                            <b><span
                                                    style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Invoice No:</span></b>
                                        </p>
                                    </td>
                                    <td>
                                        <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                            <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.creditNote.vendorInvoiceNumber</span>
                                        </p>
                                    </td>
                                    <td></td>
                                </tr>
                                </tbody>
                            </table>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        </tbody>
    </table>

    <table style="border-collapse: collapse; border: none;width: 100%">
        <tbody>
        <tr style="height: 12.00pt;">
            <td style="width: 74.85pt; border: solid windowtext 1.0pt; border-left:  1pt solid windowtext; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                width="100">
                <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                    <b><span
                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">S.No</span></b>
                </p>
            </td>
            <td style="width: 74.85pt; border: solid windowtext 1.0pt; border-left:  1pt solid windowtext; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                width="100">
                <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                    <b><span
                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Item Description</span></b>
                </p>
            </td>
            <td style="width: 36.55pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                width="49">
                <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                    <b><span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Qty.</span></b>
                </p>
            </td>
            <td style="width: 50.5pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
            >
                <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                    <b><span
                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Price</span></b>
                </p>
            </td>
            <td style="width: 63.35pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                width="84">
                <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                    <b><span
                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Net Amount</span></b>
                </p>
            </td>
            <td style="border-top: solid windowtext 1.0pt; border-left: none; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 14.15pt;"
                width="132">
                <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                    <b><span
                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Tax Percent</span></b>
                </p>
            </td>
            <td style="border-top: solid windowtext 1.0pt; border-left: none; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 14.15pt;"
                width="132">
                <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                    <b><span
                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Tax Amount</span></b>
                </p>
            </td>
            <td style="width: 66.8pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                width="89">
                <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                    <b><span
                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Total Amount</span></b>
                </p>
            </td>
        </tr>
        <tbody style="display: block; page-break-inside: avoid;">
        #foreach( $item in $data.creditNote.creditDebitNoteItems )
        <tr style="height: 12.00pt;  page-break-inside: avoid;">
            <td style="width: 74.85pt; border: solid windowtext 1.0pt; border-left:  1pt solid windowtext; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                width="100">
                <p style="margin: .0001pt 0; line-height: normal; display: block; page-break-inside: avoid;">
                    $velocityCount
                </p>
            </td>
            <td style="width: 74.85pt; border: solid windowtext 1.0pt; border-left:  1pt solid windowtext; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                width="100">
                <p style="margin: .0001pt 0; line-height: normal; display: block; page-break-inside: avoid;">
                        <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">
                          #if($item.alias)  $item.alias
                            #else $item.itemDesc #end
                        </span>
                </p>
            </td>
            <td style="width: 36.55pt; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                width="49">
                <p style="margin: .0001pt 0; line-height: normal; display: block; page-break-inside: avoid;">
                    <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.mathTool.roundTo(2,$item.qty)</span>
                </p>
            </td>

            <td style="width: 50.5pt; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                width="67">
                <p style="margin: .0001pt 0; line-height: normal; display: block; page-break-inside: avoid;">
                    <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.mathTool.roundTo(2,$item.price)</span>
                </p>
            </td>
            <td style="width: 63.35pt; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                width="84">
                <p style="margin: .0001pt 0; line-height: normal; display: block; page-break-inside: avoid;">
                    <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.mathTool.roundTo(2,$item.netAmount)</span>
                </p>
            </td>
            <td style="width: 63.35pt; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                width="132">
                <p style="margin: .0001pt 0; line-height: normal; display: block; page-break-inside: avoid;">
                    <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.mathTool.roundTo(2,$item.taxPercent)</span>
                </p>
            </td>
            <td style="width: 63.35pt; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                width="132">
                <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                    <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.mathTool.roundTo(2,$item.taxAmount)</span>
                </p>
            </td>
            <td style="width: 63.35pt; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                width="89">
                <p style="margin: .0001pt 0; text-align: center; line-height: normal;">
                    <span style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.mathTool.roundTo(2,$item.totalAmount)</span>
                </p>
            </td>

        </tr>
        #end
        </tbody>
    </table>

    <table style="border-collapse: collapse; border: none; width: 100%;padding-top: 10px; width: 100%;">
        <tbody style="display: block; page-break-inside: avoid;">
        <tr style="height: 12.00pt;  page-break-inside: avoid;width: 100%;">
            <td style="width: 50%; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt; ">
                <table style="border-collapse: collapse; border: none;">
                    <tbody>
                    <tr style="height: 12.00pt;">
                        <td>
                            <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                <span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Bank Name :</span>
                            </p>
                        </td>
                        <td>
                            <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                <span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Kotak Mahindra Bank</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt;">
                        <td>
                            <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                <span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Account Number :</span>
                            </p>
                        </td>
                        <td>
                            <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                <span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">**********</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt;">
                        <td>
                            <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                <span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">IFSC CODE :</span>
                            </p>
                        </td>
                        <td>
                            <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                <span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">KKBK0000298</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt;">
                        <td>
                            <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                <span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Address :</span>
                            </p>
                        </td>
                        <td>
                            <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                <span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Gurgaon Vatika Business Park Badshapur, <br/>
									Gurgaon, Haryana, India</span>
                            </p>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </td>
            <td style="width: 50%; border:  solid windowtext 1.0pt; border-right: solid windowtext 1.0pt;  border-bottom: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt; ">
                <table style="border-collapse: collapse; border: none;width: 100%;">
                    <tbody>
                    <tr style="height: 12.00pt;width: 100%;">
                        <td style="width: 50%;">
                            <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                <span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Net Amount :</span>
                            </p>
                        </td>
                        <td style="width: 50%;">
                            <p style="margin: .0001pt 0; text-align: right; line-height: normal;">
                                <span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.mathTool.roundTo(2,$data.creditNote.netAmount)</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt;width: 50%;">
                        <td style="width: 50%;">
                            <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                <span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Total Tax :</span>
                            </p>
                        </td>
                        <td style="width: 50%">
                            <p style="margin: .0001pt 0; text-align: right; line-height: normal;">
                                <span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.mathTool.roundTo(2,$data.creditNote.totalTax)</span>
                            </p>
                        </td>
                    </tr>
                    <tr style="height: 12.00pt;width: 100%;">
                        <td style="width: 50%">
                            <p style="margin: .0001pt 0; text-align: left; line-height: normal;">
                                <b><span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Grand Total :</span></b>
                            </p>
                        </td>
                        <td style="width: 50%">
                            <p style="margin: .0001pt 0; text-align: right; line-height: normal;">
                                <b><span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">$data.mathTool.roundTo(2,$data.creditNote.totalAmount)</span></b>
                            </p>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        </tbody>
    </table>
    </tbody>
    </table>
    <table style="width: 100%; ">
        <tr style="width: 100%;">
            <td style="width: 25%;"></td>
            <td style="width: 25%;"></td>
            <td style="width: 25%; ">
                <p>Sunshine Teahouse Pvt Ltd</p>
                <p>Authorised Signature</p>
            </td>
            <td style="width: 25%;">
                <img alt="Chaayos Free Desi Chai" src="$data.basePath/b2b_invoice_signature.png" width="100px"/><br/>
            </td>
        </tr>
    </table>

</div>
</body>
</html>
