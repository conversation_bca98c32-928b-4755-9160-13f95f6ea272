<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head></head>
<body>
<div>
    <h3 style="text-align:center;margin-bottom:0;margin-top:5pt;text-decoration: underline;font-weight:700;">SERVICE RECEIVED</h3>
    <h3 style="text-align:center;margin-top:5pt;">$data.company</h3>
    <table border="1" style="width:100%;border-spacing: 0;border-color: #ccc;">
        <tbody>
        <tr>
            <td colspan="3" style="padding:5pt;text-align:left;">
                <p><b>SR Id:</b> $data.serviceReceivedData.serviceReceivedId</p>
            </td>
            <td colspan="3" style="padding:5pt;text-align:left;">
                <p><b>Creation At:</b> $data.dateTool.format('d MMM, yyyy',$data.serviceReceivedData.createdAt)</p>
            </td>
            <td colspan="3" style="padding:5pt;text-align:left;">
                <p><b>Total Cost:</b>&nbsp;Rs.$data.mathTool.roundTo(2,$data.serviceReceivedData.price)</p>
            </td>
            <td colspan="3" style="padding:5pt;text-align:left;">
                <p><b>Total Taxes:</b>&nbsp;Rs.$data.mathTool.roundTo(2,$data.serviceReceivedData.totalTaxes)</p>
            </td>
            <td colspan="3" style="padding:5pt;text-align:left;">
                <p><b>Total Amount:</b>&nbsp;Rs.$data.mathTool.roundTo(2,$data.serviceReceivedData.totalAmount)</p>
            </td>
        </tr>
        </tbody>
    </table>
    <br>

    #set( $soDatas = $data.soDataMap)
    #foreach ($soEntry in $data.soSrMap.keySet())
    #set( $soData = $soDatas.get($soEntry))
    <table border="1" style="width:100%;border-spacing: 0;border-color: #ccc;">
        <tbody>
        <tr>
            <td colspan="4" style="padding:5pt;text-align:left;">
                <p><b>SO Id:</b> $soEntry </p>
                <p><b>Created By:</b> $soData.createdBy</p>
                <p><b>Generation Date:</b> $soData.generatedDate</p>
            </td>
            <td colspan="4" style="padding:5pt;text-align:left;">
                <p><b>SO Type:</b> $soData.soType </p>
                <p><b>Vendor Name:</b> $soData.vendorName</p>
                <p><b>Dispatch Location:</b>
                    $soData.dispatchLocation.address.line1
                    #if ($soData.dispatchLocation.address.line2), $soData.dispatchLocation.address.line2 #end
                    #if ($soData.dispatchLocation.address.line3), $soData.dispatchLocation.address.line3 #end
                    #if ($soData.dispatchLocation.address.city), $soData.dispatchLocation.address.city #end
                    #if ($soData.dispatchLocation.address.state), $soData.dispatchLocation.address.state #end
                    #if ($soData.dispatchLocation.address.country), $soData.dispatchLocation.address.country #end
                    #if ($soData.dispatchLocation.address.zipcode), $soData.dispatchLocation.address.zipcode #end
                </p>
            </td>
            <td style="padding:5pt;text-align:left;" colspan="4">
                <p><b>Cost Center:</b> $soData.costCenterName </p>
                <p><b>Total Amount:</b> $data.mathTool.roundTo(2,$soData.totalAmount)</p>
                <p><b>Pending Amount:</b> $data.mathTool.roundTo(2,$soData.pendingAmount)</p>
            </td>
        </tr>
        </tbody>
    </table>
    <table border="1" style="width:100%;border-spacing: 0;border-color: #ccc;">
        <thead>
            <tr>
                <th style="padding:5pt;text-align:center;">SR Item Id</th>
                <th style="padding:5pt;text-align:center;">Unit Name</th>
                <th style="padding:5pt;text-align:center;">Product (SAC Code)</th>
                <th style="padding:5pt;text-align:center;">Description</th>
                <th style="padding:5pt;text-align:center;">Received Qty</th>
                <th style="padding:5pt;text-align:center;">UOM</th>
                <th style="padding:5pt;text-align:center;">Price</th>
                <th style="padding:5pt;text-align:center;">Taxable Value</th>
                <th style="padding:5pt;text-align:center;">Total Tax</th>
            </tr>
        </thead>
        <tbody>
        #set( $items = $data.soSrMap.get($soEntry))
        #foreach( $item in $items)
        <tr>
            #if ( $item.latest == "Y" )
            <td style="padding:5pt;text-align:center;background-color: navajowhite">$item.itemId</td>
            <td style="padding:5pt;text-align:center;background-color: navajowhite">$item.businessCostCenterName</td>
            <td style="padding:5pt;text-align:center;background-color: navajowhite">$item.costElementName ($item.ascCode)</td>
            <td style="padding:5pt;text-align:center;background-color: navajowhite">$item.serviceDescription</td>
            <td style="padding:5pt;text-align:center;background-color: navajowhite">$data.mathTool.roundTo(2,$item.receivedQuantity)</td>
            <td style="padding:5pt;text-align:center;background-color: navajowhite">[$item.unitOfMeasure]</td>
            <td style="padding:5pt;text-align:center;background-color: navajowhite">$data.mathTool.roundTo(2,$item.unitPrice)</td>
            <td style="padding:5pt;text-align:center;background-color: navajowhite">$data.mathTool.roundTo(2,$item.totalPrice)</td>
            <td style="padding:5pt;text-align:center;background-color: navajowhite">
                $data.mathTool.roundTo(2,$item.totalTax)($data.mathTool.roundTo(2,$item.taxRate)%)
            </td>
            #else
            <td style="padding:5pt;text-align:center;">$item.itemId</td>
            <td style="padding:5pt;text-align:center;">$item.businessCostCenterName</td>
            <td style="padding:5pt;text-align:center;">$item.costElementName ($item.ascCode)</td>
            <td style="padding:5pt;text-align:center;">$item.serviceDescription</td>
            <td style="padding:5pt;text-align:center;">$data.mathTool.roundTo(2,$item.receivedQuantity)</td>
            <td style="padding:5pt;text-align:center;">[$item.unitOfMeasure]</td>
            <td style="padding:5pt;text-align:center;">$data.mathTool.roundTo(2,$item.unitPrice)</td>
            <td style="padding:5pt;text-align:center;">$data.mathTool.roundTo(2,$item.totalPrice)</td>
            <td style="padding:5pt;text-align:center;">
                $data.mathTool.roundTo(2,$item.totalTax)($data.mathTool.roundTo(2,$item.taxRate)%)
            </td>
            #end
        </tr>
        #end
        </tbody>
    </table>
    <br>
    #end
</div>
</body>
</html>