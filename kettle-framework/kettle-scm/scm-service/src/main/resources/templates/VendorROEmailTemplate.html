            <!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
            <html lang="en">
            <head>
                <title>Title</title>
            </head>
            <body>
            <!--      header      -->
            <!--<div style="background-color: #213141; padding: 10px; text-align: center;">
                <img src="http://cdn.lmitassets.com/gograbon/images/merchant/chaayos.jpeg" />
            </div>-->

            <div style="padding: 0 30px 20px 30px; background-color: #f8f8f8; color: #000;">
                <div style="text-align: center; padding:10px; font-size: 16px;">
                    <p>SUNSHINE TEAHOUSE PVT LTD.<br/>MEMORANDUM ORDER</p>
                </div>

                <div style="margin-bottom:20px;padding:10px; background-color: #e2e2e2; vertical-align:top;">
                    <p style="text-align: center; margin-bottom: 10px;"><b>Purchase From</b></p>
                    <p style="height: 50px; margin: 0;"><b>$data.vendorDetail.entityName</b><br>$data.vendorDetail.vendorAddress.line1</p>
                </div>

                #foreach( $unitId in $data.requestOrderMap.keySet() )
                    <div style="margin-bottom:30px;">
                        <div style=" background-color: #e2e2e2;padding: 5px;">
                            #set( $unit = $data.unitMap.get($unitId))
                            <p style="margin:0;"><b>Chaayos $unit.name</b><br> $unit.address.line1
                                #if ($unit.address.line2), $unit.address.line2 #end
                                #if ($unit.address.line3), $unit.address.line3 #end
                                #if ($unit.address.locality), $unit.address.locality #end
                                #if ($unit.address.city), $unit.address.city #end
                                #if ($unit.address.state), $unit.address.state #end
                                #if ($unit.address.country), $unit.address.country #end
                            </p>
                            <p style="margin:0;"><b>Contact:</b> $unit.address.contact1 #if ($unit.address.contact2), ${unit.address.contact2}#end, <b>Email:</b> ${unit.unitEmail}</p>
                        </div>

                        #foreach( $order in $data.requestOrderMap.get($unitId) )
                        <div style="padding:15px 5px;">
                            RO Id: $order.id - #if ($order.requestUnit.name) $order.requestUnit.name #end
                            <span style="float: right;"><b>Request time:</b>$date.format('medium', $order.generationTime)</span>
                        </div>
                        <div>
                            <div style="margin: 0; padding: 5px;background: #c90;">
                                <div style="display: inline-block; vertical-align: top; width: 45%;"><b>Product Name</b></div>
                                <div style="display: inline-block; vertical-align: top; width: 25%;"><b>Requested#if(${data.isReceiving})/Received#end  Quantity</b></div>
                                <div style="display: inline-block; vertical-align: top; width: 25%;"><b>Unit of measure</b></div>
                            </div>
                            #foreach( $item in $order.requestOrderItems )
                                <div style="margin: 0; padding: 5px; border-bottom: #ddd 1px solid;">
                                    <div style="display: inline-block; vertical-align: top; width: 45%;">$item.productName</div>
                                    <div style="display: inline-block; vertical-align: top; width: 25%;">$item.requestedAbsoluteQuantity #if(${data.isReceiving})/$item.receivedQuantity #end</div>
                                    <div style="display: inline-block; vertical-align: top; width: 25%;">$item.unitOfMeasure</div>
                                </div>
                            #end
                        </div>
                        #end
                    </div>
                #end

                <div style="background: #eee; padding: 10px;">
                    <p style="text-align: center">Consolidated Quantity</p>
                    <div style="margin: 0; padding: 5px;background: #c90;">
                        <div style="display: inline-block; vertical-align: top; width: 45%;"><b>Product Name</b></div>
                        <div style="display: inline-block; vertical-align: top; width: 25%;"><b>Requested#if(${data.isReceiving})/Received#end  Quantity</b></div>
                        <div style="display: inline-block; vertical-align: top; width: 25%;"><b>Unit of measure</b></div>
                    </div>
                    #foreach( $productId in $data.productQtyMap.keySet() )
                        <div style="margin: 0; padding: 5px; border-bottom: #ddd 1px solid;">
                            <div style="display: inline-block; vertical-align: top; width: 45%;">$data.productQtyMap[$productId].productName</div>
                            <div style="display: inline-block; vertical-align: top; width: 25%;">$data.productQtyMap[$productId].requestedAbsoluteQuantity #if(${data.isReceiving})/$data.productQtyMap[$productId].receivedQuantity #end</div>
                            <div style="display: inline-block; vertical-align: top; width: 25%;">$data.productQtyMap[$productId].unitOfMeasure</div>
                        </div>
                    #end
                </div>

                <div style="margin-top: 50px;"><b>Note:</b> This is for reference purpose only.</div>
            </div>
            </body>
            </html>