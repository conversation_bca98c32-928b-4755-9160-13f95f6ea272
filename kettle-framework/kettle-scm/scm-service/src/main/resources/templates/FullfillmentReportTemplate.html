<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
    <title>Fullfillment Report</title>
</head>

<body>
<div style="padding: 0 30px 20px 30px">
    <div style="padding: 15px 5px;">
        <h2>Fullfillment Percentage - Kitchen and Warehouse $data.prevDay</h2>
        <table style=" border: 1px solid black;
  border-collapse: collapse;">
            <tr>
                <th style=" border: 1px solid black; border-collapse: collapse;  padding: 5px;">TRANSFERRING_UNIT</th>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;" >TOTAL_FF_%</th>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;" >CRITICAL_FF_%</th>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">TOTAL_MINUS_BAKERY_FF_%</th>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">BAKERY_FF_%</th>
            </tr>
            #foreach($item in $data.ffDetailLastDay)
            <tr>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">$item.transferingUnit</td>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">$data.mathTool.roundTo(2,$item.avgImFPer)%</td>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">#if($item.isCriticalProd=="N" && $item.criticalAvg==0.0)
                    No Critical Product
                    #else
                    $data.mathTool.roundTo(2,$item.criticalAvg)%
                    #end
                </td>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">$data.mathTool.roundTo(2,$item.withoutBakeryAvgFPer)%</td>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">
                    #if($item.bakeryFP == -1.0)
                    N/A
                    #else
                    $data.mathTool.roundTo(2,$item.bakeryFP)%
                    #end
                </td>
            </tr>
            #end
        </table>

        <h2>Fullfillment Percentage For This Month - Kitchen or Warehouse $data.thisMonth </h2>
        <table style=" border: 1px solid black; border-collapse: collapse;">
            <tr>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">TRANSFERRING_UNIT</th>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;" >TOTAL_FF_%</th>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;" >CRITICAL_FF_%</th>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">TOTAL_MINUS_BAKERY_FF_%</th>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">BAKERY_FF_%</th>
            </tr>
            #foreach($item in $data.ffDetailLastThD)
            <tr>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">$item.transferingUnit</td>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">$data.mathTool.roundTo(2,$item.avgImFPer)%</td>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">#if($item.isCriticalProd=="N" && $item.criticalAvg==0.0)
                    No Critical Product
                    #else
                    $data.mathTool.roundTo(2,$item.criticalAvg)%
                    #end
                </td>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">$data.mathTool.roundTo(2,$item.withoutBakeryAvgFPer)%</td>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">
                    #if($item.bakeryFP == -1.0)
                    N/A
                    #else
                    $data.mathTool.roundTo(2,$item.bakeryFP)%
                    #end
                </td>
            </tr>
            #end
        </table>

        <h2>Top 20 Most Impacted Cafe Units</h2>
        <table style=" border: 1px solid black; border-collapse: collapse;">
            <tr>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">TRANSFERRING_UNIT</th>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">REQUESTING_UNIT</th>
                <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;" >IMPACTED_FULLFILLMENT_PERCENTAGE</th>
            </tr>
            #foreach($item in $data.mostImpacted)
            <tr>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">$item.transferringUnit</td>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">$item.requestingUnit</td>
                <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">$data.mathTool.roundTo(2,$item.imFPerWeightedAvg)%</td>
            </tr>
            #end
        </table>
    </div>


</div>
</body>
