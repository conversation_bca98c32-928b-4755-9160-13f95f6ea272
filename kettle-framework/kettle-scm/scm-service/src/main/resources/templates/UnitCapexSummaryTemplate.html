<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <h2>The budget for these units has not been closed for more than 60 days. Please close the budget.</h2>
    <title>CAPEX Budget Summary That Is Not Closed : </title>
    <table style=" border: 1px solid black;
  border-collapse: collapse;">
        <tr>
            <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;" >CAPEX_ID</th>
            <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;" >UNIT_ID</th>
            <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">BUDGET_TYPE</th>
            <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">TOTAL_BUDGET_AMT</th>
            <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">TOTAL_RUNNING_AMT</th>
            <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">TOTAL_RECEIVED_AMT</th>
            <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">TOTAL_REMAINING_AMT</th>
            <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">GENERATED_TIME</th>
            <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">LAST_UPDATED_TIME</th>
            <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">GENERATED_BY</th>
            <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">LAST_UPDATED_BY</th>
            <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">STATUS</th>
            <th style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">DAYS_FROM_APPROVAL</th>
        </tr>
        #foreach($item in $data.detail)
        <tr>
            <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">$item.capexId</td>
            <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">$item.unitId</td>
            <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">$item.budgetType</td>
            <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">$item.totalBudgetAmount</td>
            <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">$item.totalRunningAmount</td>
            <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">$item.totalReceivedAmount</td>
            <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">$item.totalRemainingAmount</td>
            <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">$item.generatedTime</td>
            <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">$item.lastUpdatedTime</td>
            <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">$item.generatedBy</td>
            <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">$item.lastUpdatedBy</td>
            <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">$item.status</td>
            <td style=" border: 1px solid black; border-collapse: collapse; padding: 5px;">$item.diffInDays</td>
        </tr>
        #end
    </table>
</head>
<body>

</body>
</html>