
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
<title>Asset Order Creation</title>
</head>
<body>
	<!--      header      -->
	<!--<div style="background-color: #213141; padding: 10px; text-align: center;">
                <img src="http://cdn.lmitassets.com/gograbon/images/merchant/chaayos.jpeg" />
            </div>-->

	<div
		style="padding: 0 30px 20px 30px; background-color: #f8f8f8; color: #000;">
		<div style="padding: 15px 5px;">
			RO Id: $data.requestOrder.id <span style="float: right;"><b>Request
					time:</b>$date.format('medium', $data.requestOrder.generationTime)</span>
		</div>
		<div style="padding: 15px 5px;">Generated By :
			$data.requestOrder.generatedBy.name</div>
		<div style="padding: 15px 5px;">Requesting Unit:
			$data.requestOrder.requestUnit.name</div>
		<div style="padding: 15px 5px;">Fulfilling Unit:
			$data.requestOrder.fulfillmentUnit.name</div>
		<div style="padding: 15px 5px;">Reason For Ordering:
			$data.requestOrder.comment</div>
		<div style="padding: 15px 5px;">Fulfillment Date:
			$date.format('medium', $data.requestOrder.fulfillmentDate)</div>

		<div class="row" style="margin: 10px; padding: 5px; background: #c90;">
			<div class="col-xs-6"
				style="display: inline-block; vertical-align: top; width: 45%;">
				<b>Product Name</b>
			</div>
			<div class="col-xs-3"
				style="display: inline-block; vertical-align: top; width: 25%;">
				<b>Requested Quantity</b>
			</div>
			<div class="col-xs-3"
				style="display: inline-block; vertical-align: top; width: 25%;">
				<b>Unit of measure</b>
			</div>
		</div>
		#foreach( $item in $data.requestOrder.requestOrderItems )
		<div class="row"
			style="margin: 0; padding: 5px; border-bottom: #ddd 1px solid;">
			<div class="col-xs-6"
				style="display: inline-block; vertical-align: top; width: 45%;">$item.productName</div>
			<div class="col-xs-3"
				style="display: inline-block; vertical-align: top; width: 25%;">$item.requestedAbsoluteQuantity</div>
			<div class="col-xs-3"
				style="display: inline-block; vertical-align: top; width: 25%;">$item.unitOfMeasure</div>
		</div>
		#end


		<div style="margin-top: 50px;">
			<b>Note:</b> This is for reference purpose only.
		</div>
	</div>
</body>
</html>