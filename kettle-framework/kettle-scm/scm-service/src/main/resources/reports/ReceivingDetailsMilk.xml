<ReportCategories xmlns="http://www.w3schools.com">
	<categories>
		<category name="SCM Report: Receiving Details - Milk" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>"
                        schedule="">
			<reports>
				<report id="1" name="Receiving Details - Milk" executionType="SQL"
                                        GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT 
    ud.UNIT_NAME,
    ro.FULFILLMENT_DATE,
    ro.GENERATION_TIME,
    ro.GENERATED_BY,
    roi.PRODUCT_NAME,
    roi.REQUESTED_ABSOLUTE_QUANTITY,
    roi.TRANSFERRED_QUANTITY,
    roi.RECEIVED_QUANTITY,
    roi.UNIT_OF_MEASURE,
    vd.VENDOR_NAME
FROM
    REQUEST_ORDER ro
        INNER JOIN
    REQUEST_ORDER_ITEM roi ON ro.REQUEST_ORDER_ID = roi.REQUEST_ORDER_ID
        AND ro.FULFILLMENT_DATE = DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00'))
        AND ro.IS_SPECIAL_ORDER = 'Y'
        INNER JOIN
    UNIT_DETAIL ud ON ud.UNIT_ID = ro.REQUEST_UNIT_ID
        INNER JOIN
    UNIT_DETAIL ud1 ON ud1.UNIT_ID = ro.FULFILLMENT_UNIT_ID
        INNER JOIN
    VENDOR_DETAIL vd ON roi.VENDOR_ID = vd.VENDOR_ID
        INNER JOIN
    PRODUCT_DEFINITION pd ON roi.Product_ID = pd.Product_ID
WHERE
    pd.Category_ID = '1'
        AND pd.SUB_CATEGORY_ID = '3';
        ]]>
					</content>
				</report>
			</reports>
		</category>
	</categories>
</ReportCategories>


