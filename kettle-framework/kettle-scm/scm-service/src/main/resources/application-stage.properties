#
# SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
# __________________
#
# [2015] - [2017] Sunshine Teahouse Private Limited
# All Rights Reserved.
#
# NOTICE:  All information contained herein is, and remains
# the property of Sunshine Teahouse Private Limited and its suppliers,
# if any.  The intellectual and technical concepts contained
# herein are proprietary to Sunshine Teahouse Private Limited
# and its suppliers, and are protected by trade secret or copyright law.
# Dissemination of this information or reproduction of this material
# is strictly forbidden unless prior written permission is obtained
# from Sunshine Teahouse Private Limited.
#
spring.application.name=scm-service
#management.server.servlet.contextPath=/${spring.application.name}-management
log.base.dir=/usr/share/tomcat8/logs/

monitoring.user-name=admin
monitoring.user-password=Chaayos12345

# Enable JavaMelody auto-configuration (optional, default: true)
javamelody.enabled=false
# Data source names to exclude from monitoring (optional, comma-separated)
#javamelody.excluded-datasources=secretSource,topSecretSource
# Enable monitoring of Spring services and controllers (optional, default: true)
javamelody.spring-monitoring-enabled=false
# Initialization parameters for JavaMelody (optional)
# See: https://github.com/javamelody/javamelody/wiki/UserGuide#6-optional-parameters
#    log http requests:
javamelody.init-parameters.log=true
#    to exclude images, css, fonts and js urls from the monitoring:
javamelody.init-parameters.url-exclude-pattern=(/webjars/.*|/css/.*|/images/.*|/fonts/.*|/js/.*)
#    to aggregate digits in http requests:
# javamelody.init-parameters.http-transform-pattern: \d+
#    to add basic auth:
javamelody.init-parameters.authorized-users=${monitoring.user-name}:${monitoring.user-password}
#    to change the default storage directory:
javamelody.init-parameters.storage-directory==${log.base.dir}/${spring.application.name}/javamelody
#    to change the default "/monitoring" path:
# javamelody.init-parameters.monitoring-path=/admin/performance

management.endpoints.web.exposure.include=*
javamelody.management-endpoint-monitoring-enabled=true
spring.main.allow-bean-definition-overriding=true

# Enable response compression
server.compression.enabled=true
# The comma-separated list of mime types that should be compressed
server.compression.mime-types=text/html,text/xml,text/plain,text/css,text/javascript,application/javascript,application/json
# Compress the response only if the response size is at least 1KB
server.compression.min-response-size=1024


log.location=${log.base.dir}/${spring.application.name}/log
server.servlet.contextPath=/${spring.application.name}
spring.mvc.servlet.path=/rest

server.port=9898


environment.type=STAGE
mail.receipt.email=<EMAIL>
mail.undelivered.email=<EMAIL>
mail.dummy.customer.id=5
mail.to.email=<EMAIL>
mail.retry.count=2
mail.thread.sleep.time=60000

server.base.dir=/data/app/kettle/stage

google.apikey = AIzaSyCawLC7jetAkAi9L4rjujnEbK41egwspb0

# jdbc.X
#SCM Data Source
scm.jdbc.driverClassName=com.mysql.cj.jdbc.Driver
scm.jdbc.url=******************************************************************************
scm.jdbc.user=root
scm.jdbc.pass=Chaayos123#@!

master.jdbc.driverClassName=com.mysql.cj.jdbc.Driver
master.jdbc.url=*********************************************************************************
master.jdbc.user=root
master.jdbc.pass=Chaayos123#@!

# hibernate.X
hibernate.dialect=org.hibernate.dialect.MySQL5Dialect
hibernate.show_sql=false
hibernate.hbm2ddl.auto=validate
hibernate.check_nullability = false

# data source for fullfillment report process
scm.data.driverClassName=com.mysql.cj.jdbc.Driver
scm.data.url=****************************************************************************
scm.data.user=rptusr
scm.data.pass=321In#@!

#mongo
#scm.mongo.schema=kettle_scm
#scm.mongo.uri=mongodb://kettleusrmongo:<EMAIL>:27017/admin?authSource=admin&connectTimeoutMS=300000


run.validate.filter=true
run.aclInterceptor=false
run.apiTokenInterceptor=true

master.cache.host.details=localhost
master.cache.host.ports=5701,5702,5703


kettle.day.close.url=http://localhost:8080/kettle-service/rest/v1/pos-metadata/unit-kettleDayClose

#automated reports
run.auto.report=false

#vendor notification setting
send.vendor.email=true
send.vendor.sms=false
vendor.email.cc=<EMAIL>
vendor.po.email.cc=<EMAIL>
vendor.edit.email.notification=<EMAIL>
sqs.message.queue.prefix=DEV

scm.client.token=eyJhbGciOiJIUzI1NiJ9.eyJwYXJ0bmVyTmFtZSI6InNjbS1zZXJ2aWNlIiwiZW52VHlwZSI6IlBST0QiLCJwYXNzQ29kZSI6IkkyWVc0IiwiaWF0IjoxNDg4ODA2NzIyfQ.GVmjU7X5vDMNFSzKhlS67wlSpTwIRw9VLry5MOOLf0c
base.path.kettle.service=http://localhost:8080
base.path.master.service=http://localhost:8080
base.path.inventory.service=http://stage.kettle.chaayos.com:8080

vendor.registration.token=eyJhbGciOiJIUzI1NiJ9.eyJwYXJ0bmVyTmFtZSI6InZlbmRvci1jbGllbnQiLCJlbnZUeXBlIjoiREVWIiwicGFzc0NvZGUiOiI3NjcwOSIsImlhdCI6MTQ5MzEwOTA0OH0.qVE5TCpygSVL4BFtPqhrRtFcuWrWYn4Mg6dQPLK_qrs
vendor.request.url=http://stage.kettle.chaayos.com:8080/scm-service/vendors.html#/vendorCreate
vendor.contract.url=http://stage.kettle.chaayos.com:8080/scm-service/vendors.html#/vendorContractCreation
system.contract.url=http://stage.kettle.chaayos.com:8080/scm-service/vendors.html#/vendorContractApproval
finance.calendar.start=2020-04-01 00:00:00
payment.request.launch=2017-11-01 00:00:00
amazon.s3.bucket=chaayosdevtest
amazon.s3.product.bucket=product.image.dev
po.cost.limit=10000
day.close.check=true

scrap.ncr.unit=26040
scrap.ncr_edu.unit=26040
scrap.chandigarh.unit=26040
scrap.mumbai.unit=26041
gatepass.limit=50000
remove.duplicate.key.error.job=true

max.applicable.depreciation.percentage=.95
vendor.gr.invoice.number.validation=true

ordering.calculationDates=3
ordering.dayDifference=-7
ordering.buffer=10
cloning.dayDifference=-21
payment.ThresholdDays=2
utrUpload.ThresholdDays=2

fountain9.username=<EMAIL>
fountain9.password=nsa_2012_nwd
fountain9.isSuperUser=false
fountain9.accessTokenUrl=https://chaayos.kronoscope.fountain9.com/atlas/auth/api/token
fountain9.forecastReportUrl=https://chaayos.kronoscope.fountain9.com/atlas/timeseries/api/get_forecast_report
fountain9.metric=quantity
fountain9.timeGranularity=day

spring.data.mongodb.uri=mongodb://stagemongo:<EMAIL>:27017/admin?authSource=admin&connectTimeoutMS=300000
spring.data.mongodb.database=kettle_master
spring.data.mongodb.auto-connect-retry=true

spring.main.allow-circular-references=true
fountain9.columns=date,brand,cafe_id,city,l1_category,l2_category,l3_category,l4_category,mother_warehouse,order_source,sku_id,sku_name,actual,predicted_lpi,predicted_baseline,predicted_upi,predicted_final,price,actual_value,predicted_baseline_value,value


dimensions.aggregated.on=cafe_id,sku_id,sku_name
fountain9.scm.columns=date,cafe_id,sku_id,sku_name,actual,predicted_lpi,predicted_baseline,predicted_upi,predicted_final,price,actual_value,predicted_baseline_value,value,safety_stock

partner.service.base.url=http://stage.kettle.chaayos.com:8080/channel-partner/
channelpartner.client.token=eyJhbGciOiJIUzI1NiJ9.eyJwYXJ0bmVyTmFtZSI6IkNIQU5ORUwtUEFSVE5FUiIsImVudlR5cGUiOiJQUk9EIiwicGFzc0NvZGUiOiJNM0EyTCIsImlhdCI6MTUyODg0OTkzOH0.RbuNjXkk7fc2xs7KwgJQTQGY6milAkKKDl8M8CSKAzU

knock.base.url = http://stage.kettle.chaayos.com:9595/knock-service/
knock.master.token=eyJhbGciOiJIUzI1NiJ9.eyJwYXJ0bmVyTmFtZSI6Imtub2NrIiwiZW52VHlwZSI6IlNQUk9EIiwicGFydG5lcklkIjoyNSwicGFzc0NvZGUiOiJXWU1VVDI2REsyVlJRQkoiLCJpYXQiOjE2NzM1Mjc2MTR9.bH2TRLXAQ7zHHQXpFV7WkWPoxEiakGmMb3BueeyPeKA
special.order.buffer.hours=2

is.client.node=true
cluster.node.ip.details=********:5701,*********:5701,*********:5701,********:5701
switch.asset.doc.dir = ${server.base.dir}/switch_asset_docs
sandbox.base.url=https://api.sandbox.co.in/
sandbox.x.api.key=key_live_mu2bxji762fMnn956mUMvtsLUHjvIGfO
sandbox.x.secret.key=secret_live_ZXzRcQAlkGKjBZtoayXRbIiwr3071R6q
sandbox.x.api.version=1.0

