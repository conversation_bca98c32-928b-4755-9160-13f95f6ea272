/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.service.core;

import com.google.common.base.Preconditions;
import com.stpl.tech.master.util.DataSourceConstants;
import com.stpl.tech.util.ExecutionEnvironment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.core.env.Environment;
import org.springframework.jdbc.datasource.DriverManagerDataSource;

import javax.sql.DataSource;

@Configuration
@PropertySource({ "classpath:props/connection.properties" })
public class SCMReportServiceConfig {

	@Autowired
	private Environment connectionProperties;

	public SCMReportServiceConfig() {
		super();
	}

	@Bean(name = DataSourceConstants.UAT_DATA_SOURCE)
	public DataSource dataUATSource() {
		return getDataSource(ExecutionEnvironment.UAT.name());
	}

	@Bean(name = DataSourceConstants.DUMP_DATA_SOURCE)
	public DataSource dataDUMPSource() {
		return getDataSource(ExecutionEnvironment.DUMP.name());
	}

	@Bean(name = DataSourceConstants.DEV_DATA_SOURCE)
	public DataSource dataDEVSource() {
		return getDataSource(ExecutionEnvironment.DEV.name());
	}

	@Bean(name = DataSourceConstants.PROD_DATA_SOURCE)
	public DataSource dataProdSource() {
		return getDataSource(ExecutionEnvironment.PROD.name());
	}

	private DataSource getDataSource(String executionEnv) {
		final DriverManagerDataSource dataSource = new DriverManagerDataSource();
		dataSource.setDriverClassName(
				Preconditions.checkNotNull(connectionProperties.getProperty(executionEnv + ".jdbc.driverClassName")));
		dataSource.setUrl(Preconditions.checkNotNull(connectionProperties.getProperty(executionEnv + ".jdbc.url")));
		dataSource
				.setUsername(Preconditions.checkNotNull(connectionProperties.getProperty(executionEnv + ".jdbc.user")));
		dataSource
				.setPassword(Preconditions.checkNotNull(connectionProperties.getProperty(executionEnv + ".jdbc.pass")));
		return dataSource;
	}
}