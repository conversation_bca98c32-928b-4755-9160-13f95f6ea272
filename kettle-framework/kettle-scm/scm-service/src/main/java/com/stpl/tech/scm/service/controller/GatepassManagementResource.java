package com.stpl.tech.scm.service.controller;

import java.text.ParseException;
import java.util.List;

import javax.ws.rs.core.MediaType;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.exception.GatepassException;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.GatepassManagementService;
import com.stpl.tech.scm.domain.model.Gatepass;
import com.stpl.tech.scm.domain.model.GatepassVendorMapping;
import com.stpl.tech.scm.domain.model.ProductStatus;
import com.stpl.tech.scm.domain.model.SearchGatepass;

@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
		+ SCMServiceConstants.GATEPASS_MANAGEMENT_ROOT_CONTEXT)
public class GatepassManagementResource extends AbstractSCMResources {
	@Autowired
	private GatepassManagementService gatepassManagementService;

	@RequestMapping(method = RequestMethod.GET, value = "vendor-list", produces = MediaType.APPLICATION_JSON)
	public List<GatepassVendorMapping> getVendorOpertaionMapping(@RequestParam(required = false) final String opsType,
			@RequestParam(required = false) final Integer unitId, @RequestParam(required = false) final String status) {
		return gatepassManagementService.getVendorMappingList(opsType, unitId, status);
	}

	@RequestMapping(method = RequestMethod.POST, value = "create-gatepass", produces = MediaType.APPLICATION_JSON)
	public Integer createGatepass(@RequestBody Gatepass gatepass) throws InventoryUpdateException, GatepassException, SumoException, ParseException, DataNotFoundException {
		return gatepassManagementService.createGatepass(gatepass);
	}

	@RequestMapping(method = RequestMethod.POST, value = "search-gatepass", produces = MediaType.APPLICATION_JSON)
	public List<Gatepass> getGatepassList(@RequestBody SearchGatepass searchGatepass) {
		return gatepassManagementService.getGatepass(searchGatepass);
	}

	@RequestMapping(method = RequestMethod.POST, value = "update-gatepass", produces = MediaType.APPLICATION_JSON)
	public Boolean updateGatepass(@RequestBody Gatepass gatepass) throws InventoryUpdateException, GatepassException, SumoException, ParseException, DataNotFoundException {
		return gatepassManagementService.updateGatepass(gatepass);
	}

	@RequestMapping(method = RequestMethod.POST, value = "cancel-gatepass", produces = MediaType.APPLICATION_JSON)
	public Boolean cancelGatepass(@RequestBody Gatepass gatepass) throws InventoryUpdateException, SumoException {
		return gatepassManagementService.cancelGatepass(gatepass);
	}
	

	@RequestMapping(method = RequestMethod.POST, value = "add-vendor-mapping", produces = MediaType.APPLICATION_JSON)
	public Boolean addVendorMapping(@RequestBody GatepassVendorMapping vendorMapping) throws GatepassException, SumoException{
		return gatepassManagementService.addVendorMapping(vendorMapping);
	}
	
	@RequestMapping(method = RequestMethod.PUT, value = "vendor-deactivate", produces = MediaType.APPLICATION_JSON)
	public Boolean deactivateVendor(@RequestBody final int mappingId) {
		return gatepassManagementService.updateVendorStatus(mappingId, ProductStatus.IN_ACTIVE.name());
	}

	@RequestMapping(method = RequestMethod.PUT, value = "vendor-activate", produces = MediaType.APPLICATION_JSON)
	public Boolean activateVendor(@RequestBody final int mappingId) {
		return gatepassManagementService.updateVendorStatus(mappingId, ProductStatus.ACTIVE.name());
	}

}
