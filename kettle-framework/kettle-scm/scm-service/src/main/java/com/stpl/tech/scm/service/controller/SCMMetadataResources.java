/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.service.controller;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.JsonDeserializationContext;
import com.google.gson.JsonDeserializer;
import com.google.gson.JsonElement;
import com.google.gson.JsonParseException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Brand;
import com.stpl.tech.master.domain.model.Company;
import com.stpl.tech.master.domain.model.Location;
import com.stpl.tech.master.domain.model.MetaInfo;
import com.stpl.tech.master.domain.model.State;
import com.stpl.tech.master.recipe.model.UnitOfMeasure;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.SCMMetadataService;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.domain.model.AttributeType;
import com.stpl.tech.scm.domain.model.DeactivateValidateResponse;
import com.stpl.tech.scm.domain.model.FulfillmentType;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.PackagingType;
import com.stpl.tech.scm.domain.model.PaymentRequestStatus;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.SCMMetadata;
import com.stpl.tech.scm.domain.model.SCMOrderStatus;
import com.stpl.tech.scm.domain.model.StockTakeType;
import com.stpl.tech.scm.domain.model.SwitchStatus;
import com.stpl.tech.scm.domain.model.UnitDetail;
import com.stpl.tech.scm.domain.model.UnitDistanceResponse;
import com.stpl.tech.scm.domain.model.VarianceType;
import com.stpl.tech.scm.service.controller.view.ExcelViewGenerator;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.View;

import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by Rahul Singh on 04-05-2016.
 */

@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
		+ SCMServiceConstants.SCM_METADATA_ROOT_CONTEXT)
public class SCMMetadataResources {


	private static final Logger LOG = LoggerFactory.getLogger(SCMMetadataResources.class);

	@Autowired
	private SCMCache scmCache;

	@Autowired
	private MasterDataCache masterCache;

	@Autowired
	private SCMMetadataService scmMetadataService;

	@Autowired
	private ExcelViewGenerator excelViewGenerator;


	@RequestMapping(method = RequestMethod.GET, value = "metadata", produces = MediaType.APPLICATION_JSON)
	public SCMMetadata getSCMMetadata() throws IOException {
		SCMMetadata scmMetadata = new SCMMetadata();
		scmMetadata.getAttributeTypes().addAll(Arrays.asList(AttributeType.values()));
		scmMetadata.getFulFillmentTypes().addAll(Arrays.asList(FulfillmentType.values()));
		scmMetadata.getVarianceTypes().addAll(Arrays.asList(VarianceType.values()));
		scmMetadata.getPackagingTypes().addAll(Arrays.asList(PackagingType.values()));
		scmMetadata.getSwitchStatus().addAll(Arrays.asList(SwitchStatus.values()));
		scmMetadata.getStockTakeType().addAll(Arrays.asList(StockTakeType.values()));
		scmMetadata.getCategoryDefinitions().addAll(scmCache.getCategoryDefinitions().values());
		scmMetadata.getSubCategoryDefinitions().addAll(scmCache.getSubCategoryDefinitions().values());
		scmMetadata.getScmOrderStatus().addAll(Arrays.asList(SCMOrderStatus.values()));
		scmMetadata.getPaymentRequestTypes().addAll(masterCache.getAllPaymentRequestTypes());
		scmMetadata.getPaymentDeviations().addAll(scmMetadataService.getAllPaymentDeviations());
		scmMetadata.getTokenizedApis().addAll(masterCache.getTokenizedApis().keySet());
		scmMetadata.getPaymentRequestStatus().addAll(Arrays.asList(PaymentRequestStatus.values()));
		return scmMetadata;
	}

	@RequestMapping(method = RequestMethod.GET, value = "uomMetadata", produces = MediaType.APPLICATION_JSON)
	public List<UnitOfMeasure> getUomMetadata() {
		return new ArrayList<>(Arrays.asList(UnitOfMeasure.values()));
	}

	@RequestMapping(method = RequestMethod.GET, value = "delivery-locations", produces = MediaType.APPLICATION_JSON)
	public Collection<Location> getAllDeliveryLocationMetadata() {
		return masterCache.getAllLocations().values();
	}

	@RequestMapping(method = RequestMethod.GET, value = "active-delivery-locations", produces = MediaType.APPLICATION_JSON)
	public List<Location> getAllActiveDeliveryLocationMetadata() {
		Collection<Location> list = masterCache.getAllLocations().values();
		return list.stream().filter(Location::isBusiness).collect(Collectors.toList());
	}

	@RequestMapping(method = RequestMethod.GET, value = "states", produces = MediaType.APPLICATION_JSON)
	public Collection<State> getAllStatesMetadata() {
		return masterCache.getAllStates().values();
	}

	@RequestMapping(method = RequestMethod.GET, value = "active-states", produces = MediaType.APPLICATION_JSON)
	public Collection<State> getAllActiveStatesMetadata() {
		Collection<State> list = masterCache.getAllStates().values();
		return list.stream().filter(State::isBusiness).collect(Collectors.toList());
	}

	@RequestMapping(method = RequestMethod.GET, value = "inventory-lists", produces = MediaType.APPLICATION_JSON)
	public Collection<IdCodeName> getAllInventoryLists() {
		return scmCache.getAllInventoryLists().values();
	}

	@RequestMapping(method = RequestMethod.GET, value = "companies", produces = MediaType.APPLICATION_JSON)
	public List<Company> getAllCompanies() {
		return masterCache.getAllCompanies();
	}

	@RequestMapping(method = RequestMethod.GET, value = "brands", produces = MediaType.APPLICATION_JSON)
	public List<Brand> getAllBrands() {
		return masterCache.getAllBrands();
	}

	@RequestMapping(method = RequestMethod.GET, value = "product-definition", produces = MediaType.APPLICATION_JSON)
	public MetaInfo getAllProductMetadata() {
		Map<Integer, ProductDefinition> defs = scmCache.getProductDefinitions();
		MetaInfo infor = new MetaInfo("SCM_PRODUCT",
				new HashMap<>());
		for (Integer key : defs.keySet()) {
			ProductDefinition p = defs.get(key);
			infor.getData().put(key,
					new com.stpl.tech.master.domain.model.IdCodeName(p.getProductId(), p.getProductName(),
							p.getProductCode(), null, p.getCategoryDefinition().getCode(),
							p.getProductStatus().name()));
		}
		return infor;
	}

	@RequestMapping(method = RequestMethod.GET, value = "region-mappings", produces = MediaType.APPLICATION_JSON)
	public Map<String, UnitDetail> getRegionFulfillmentMappings() {
		Map<String,UnitDetail> resultMap = new HashMap<>();
		Map<String,Integer> tempMap = scmMetadataService.getRegionFulfillmentMapping();
        for(String key : tempMap.keySet()){
			resultMap.put(key,scmCache.getUnitDetail(tempMap.get(key)));
		}
		return resultMap;
	}

	@RequestMapping(method = RequestMethod.POST,value = "get-Excell")
	public View getExcell(HttpServletResponse response, @RequestBody Map<String,List<String>> request , @RequestParam(required = true) String className) throws ClassNotFoundException {
		Class<?> cls = Class.forName(className);
		GsonBuilder builder = new GsonBuilder();
		builder.registerTypeAdapter(Date.class, new JsonDeserializer<Date>() {
			public Date deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
				if(SCMUtil.isValidDate(json.getAsJsonPrimitive().toString().substring(1, json.getAsJsonPrimitive().toString().length() - 1))){
					return AppUtils.parseDate(json.getAsJsonPrimitive().toString().substring(1, json.getAsJsonPrimitive().toString().length() - 1));
				}else{
					return new Date(json.getAsJsonPrimitive().getAsLong());
				}
			}
		});

		 Gson gson = builder.create();
		 List<Object> temp = new ArrayList<>();
		 request.get("data").stream().map(json -> {
			 Object object = gson.fromJson(json,cls);
			 temp.add(object);
			 return json;
		 }).collect(Collectors.toList());
		return excelViewGenerator.getExcell("download.xlsx", Arrays.asList(temp.toArray()), response, request.get("skipColumns"));
	}

	@RequestMapping(method = RequestMethod.GET, value = "get-unit-distance", produces = MediaType.APPLICATION_JSON)
	public UnitDistanceResponse getUnitDistance(@RequestParam Integer sourceId , @RequestParam Integer destinationId) throws IOException, SumoException {

		LOG.info("Request To Get Distance Between  {} and {} units from Google Map Api",sourceId,destinationId);
		UnitDistanceResponse response = new UnitDistanceResponse();
		try {
			String source =  masterCache.getUnitBasicDetail(sourceId).getLatitude() + "," + masterCache.getUnitBasicDetail(sourceId).getLongitude();
			String destination =  masterCache.getUnitBasicDetail(destinationId).getLatitude() + "," + masterCache.getUnitBasicDetail(destinationId).getLongitude();

			BigDecimal distance = SCMUtil.divide(BigDecimal.valueOf(scmMetadataService.findDistance(source,destination).getRows().
					get(0).getElements().get(0).getDistance().getValue()),BigDecimal.valueOf(1000));
			response.setDistance(distance);
			response.setSourcePinCode(masterCache.getUnit(sourceId).getAddress().getZipCode());
			response.setDestinationPinCode(masterCache.getUnit(destinationId).getAddress().getZipCode());
			response.setSourceUnitId(sourceId);
			response.setDestinationUnitId(destinationId);
		}catch (Exception e){
			String msg = "Error While Fetching Unit Distance Between Source Unit   : " +  sourceId + "and Destination Unit : " + destinationId + "From google Map Api";
			LOG.error("Error While Fetching Unit Distance Between Source Unit  : {} and Destination Unit : {} From google Map Api :::::::",sourceId,destinationId,e);
			throw new SumoException("Distance Mapping not Found !! " , msg );
		}

		return  response;

	}

	@RequestMapping(method = RequestMethod.GET, value = "validate-for-deactivation", produces = MediaType.APPLICATION_JSON)
	public DeactivateValidateResponse validateForDeactivation(@RequestParam(required = true) Integer id , @RequestParam(required = true) String type) throws URISyntaxException {
		LOG.info("Request To Validate {} with id {} for Deactivation" , type, id);
        return  scmMetadataService.validateForDeactivation(id,type);
	}

	@RequestMapping(method = RequestMethod.GET, value = "maintenance-wh-mapping", produces = MediaType.APPLICATION_JSON)
	public Map<String,List<Integer>> getMaintenanceWhMappings()  {
		LOG.info("Request To Get Maintenence WH Mapping :::::::");
		return  scmMetadataService.getMaintenanceWHMappings();
	}

	@Scheduled(cron = "0 0 5 * * *", zone = "GMT+05:30")
	@RequestMapping(method = RequestMethod.GET, value = "dump-audit-data", produces = MediaType.APPLICATION_JSON)
	public void dumpAuditData()  {
		LOG.info("Request To Dump Audit Data:::::::");
		scmMetadataService.dumpAuditLogsToMysql();
	}








}
