package com.stpl.tech.scm.service.model;

import java.util.Date;
import java.util.List;

public class EWayDataRequest {

	private List<Integer> transferringUnitList;
	private List<Integer> receivingUnitList;
	private Date startDate;
	private Date endDate;

	public EWayDataRequest() {
	}

	public List<Integer> getTransferringUnitList() {
		return transferringUnitList;
	}

	public void setTransferringUnitList(List<Integer> transferringUnitList) {
		this.transferringUnitList = transferringUnitList;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public List<Integer> getReceivingUnitList() {
		return receivingUnitList;
	}

	public void setReceivingUnitList(List<Integer> receivingUnitList) {
		this.receivingUnitList = receivingUnitList;
	}
}
