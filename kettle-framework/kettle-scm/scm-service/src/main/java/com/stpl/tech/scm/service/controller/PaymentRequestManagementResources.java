package com.stpl.tech.scm.service.controller;

import com.itextpdf.text.DocumentException;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.scm.core.PaymentBanks;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.exception.TransferOrderCreationException;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.PaymentRequestManagementService;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.dao.PaymentRequestManagementDao;
import com.stpl.tech.scm.data.model.CompanyBankMapping;
import com.stpl.tech.scm.data.model.VendorDetailData;
import com.stpl.tech.scm.domain.model.BankVO;
import com.stpl.tech.scm.domain.model.DebitNoteDetail;
import com.stpl.tech.scm.domain.model.DocUploadType;
import com.stpl.tech.scm.domain.model.DocumentDetail;
import com.stpl.tech.scm.domain.model.FileType;
import com.stpl.tech.scm.domain.model.HolidayActivateDeactivate;
import com.stpl.tech.scm.domain.model.HolidayDetails;
import com.stpl.tech.scm.domain.model.MimeType;
import com.stpl.tech.scm.domain.model.PRPaymentDetail;
import com.stpl.tech.scm.domain.model.PaymentCalendar;
import com.stpl.tech.scm.domain.model.PaymentRequest;
import com.stpl.tech.scm.domain.model.PaymentRequestStatus;
import com.stpl.tech.scm.domain.model.PaymentRequestStatusChangeVO;
import com.stpl.tech.scm.domain.model.PaymentRequestType;
import com.stpl.tech.scm.domain.model.PaymentSheetReturnVO;
import com.stpl.tech.scm.domain.model.PaymentSheetVO;
import com.stpl.tech.scm.domain.model.RejectedPayments;
import com.stpl.tech.scm.domain.model.VendorAdvancePayment;
import com.stpl.tech.scm.domain.model.VendorPRVO;
import com.stpl.tech.scm.domain.model.VendorStatus;
import com.stpl.tech.scm.service.controller.view.ExcelViewGenerator;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by Rahul Singh on 22-06-2016.
 */
@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
        + SCMServiceConstants.PAYMENT_REQUEST_MANAGEMENT_ROOT_CONTEXT)
public class PaymentRequestManagementResources extends AbstractSCMResources {

    private static final Logger LOG = LoggerFactory.getLogger(PaymentRequestManagementResources.class);
    @Autowired
    private PaymentRequestManagementService paymentRequestManagementService;

    @Autowired
    private ExcelViewGenerator excelViewGenerator;

    @Autowired
    private PaymentRequestManagementDao paymentDao;

    @Autowired
    private EnvProperties props;



    @RequestMapping(method = RequestMethod.POST, value = "payment-request", produces = MediaType.APPLICATION_JSON)
    public PaymentRequest createPaymentRequest(@RequestBody final PaymentRequest paymentRequest , @RequestParam(required = false) Integer milkInvoiceId) throws SumoException {
        return paymentRequestManagementService.createPaymentRequest(paymentRequest,milkInvoiceId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "payment-request-update-invoice", produces = MediaType.APPLICATION_JSON)
    public PaymentRequest updateInvoicePaymentRequest(@RequestBody final PaymentRequest paymentRequest) throws SumoException {
        return paymentRequestManagementService.updateInvoicePaymentRequest(paymentRequest);
    }

    @RequestMapping(method = RequestMethod.GET, value = "payment-requests", produces = MediaType.APPLICATION_JSON)
    public List<PaymentRequest> getPaymentRequests(@RequestParam(required = false) Integer vendorId,
                                                   @RequestParam(required = false) Integer unitId,
                                                   @RequestParam(required = false) Integer prId,
                                                   @RequestParam(required = false) Integer grId,
                                                   @RequestParam(required = false) Integer srId,
                                                   @RequestParam(required = false) String invoiceNumber,
                                                   @RequestParam(required = false) String startDate,
                                                   @RequestParam(required = false) String endDate,
                                                   @RequestParam(required = false) String status,
                                                   @RequestParam(required = false) String paymentDate,
                                                   @RequestParam(required = false) String requestType,
                                                   @RequestParam(required = false) Integer companyId) throws SumoException {
        if(grId !=null) {
            LOG.info("paymentDat" + paymentDate + "gr id" + grId);
        }
        if(srId !=null){
            LOG.info("paymentDate"+ paymentDate + "sr id" +srId);
        }
        Date payDate = paymentDate != null && paymentDate.trim().length() > 0 ? AppUtils.getStartOfDay(AppUtils.parseDateIST(paymentDate)) : null;
        return paymentRequestManagementService.getPaymentRequests(vendorId, unitId, prId, grId,srId, invoiceNumber,
                SCMUtil.parseDate(startDate), SCMUtil.parseDate(endDate), status, payDate, companyId,requestType);
    }

    @RequestMapping(method = RequestMethod.GET, value = "payment-requests-company", produces = MediaType.APPLICATION_JSON)
    public List<PaymentRequest> getCompanyPaymentRequests(@RequestParam(required = false) Integer vendorId,
                                                          @RequestParam(required = true) Integer companyId,
                                                          @RequestParam(required = false) Integer unitId,
                                                          @RequestParam(required = false) Integer prId,
                                                          @RequestParam(required = false) Integer grId,
                                                          @RequestParam(required = false) String invoiceNumber,
                                                          @RequestParam(required = false) String startDate,
                                                          @RequestParam(required = false) String endDate,
                                                          @RequestParam(required = false) String status,
                                                          @RequestParam(required = false) String paymentDate,
                                                          @RequestParam(required = false) String type) throws SumoException {
        return paymentRequestManagementService.getCompanyPaymentRequests(companyId, vendorId, unitId, prId, grId,
                invoiceNumber, SCMUtil.parseDate(startDate), SCMUtil.parseDate(endDate), status,
                paymentDate != null && paymentDate.trim().length() > 0 ? AppUtils.getStartOfDay(AppUtils.parseDateIST(paymentDate)) : null,type);
    }

    @RequestMapping(method = RequestMethod.GET, value = "gr-for-rejected-payment-request", produces = MediaType.APPLICATION_JSON)
    public List<RejectedPayments> getRejectedPrForGr(
            @RequestParam(required = false) Integer vendorId,
            @RequestParam(required = false) Integer unitId) throws SumoException {
        return paymentRequestManagementService.getRejectedPrForGr(unitId,vendorId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "update-filing-no", produces = MediaType.APPLICATION_JSON)
    public boolean updatePrFilingNo(@RequestParam Integer prId, @RequestParam String filingNo) throws SumoException{
        return paymentRequestManagementService.updatePrFilingNo(prId, filingNo);
    }


    @RequestMapping(method = RequestMethod.GET, value = "payment-requests-company-for-process", produces = MediaType.APPLICATION_JSON)
    public List<PaymentRequest> getCompanyPaymentRequestsForProcess(
            @RequestParam(required = false) PaymentRequestType type,
            @RequestParam(required = false) Integer vendorId,
            @RequestParam(required = true) Integer companyId,
            @RequestParam(required = false) Integer unitId,
            @RequestParam(required = false) Integer prId,
            @RequestParam(required = false) String invoiceNumber,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(required = false) String status,
            @RequestParam(required = true) String dateType) throws SumoException {

        type = type != null ? type : PaymentRequestType.GOODS_RECEIVED;
        return paymentRequestManagementService.getCompanyPaymentRequestsForProcess(type, companyId, vendorId, unitId, prId,
                invoiceNumber, SCMUtil.parseDate(startDate), SCMUtil.parseDate(endDate), status, dateType);
    }

    @RequestMapping(method = RequestMethod.GET, value = "payment-request", produces = MediaType.APPLICATION_JSON)
    public PaymentRequest viewPaymentRequest(@RequestParam final Integer paymentRequestId) throws SumoException {
        return paymentRequestManagementService.viewPaymentRequest(paymentRequestId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "payment-request-change-status", produces = MediaType.APPLICATION_JSON)
    public PaymentRequestStatusChangeVO paymentRequestStatusChange(@RequestBody final PaymentRequestStatusChangeVO changeRequest) throws SumoException, EmailGenerationException {
        return paymentRequestManagementService.changePaymentRequestStatus(changeRequest);
    }

    @RequestMapping(method = RequestMethod.POST, value = "add-debit-note", produces = MediaType.APPLICATION_JSON)
    public DebitNoteDetail addDebitNote(@RequestBody final DebitNoteDetail detail) throws SumoException {
        return paymentRequestManagementService.addDebitNote(detail);
    }

    @RequestMapping(method = RequestMethod.POST, value = "approve", produces = MediaType.APPLICATION_JSON)
    public boolean approvePaymentRequest(@RequestBody final PaymentRequest paymentRequest) throws SumoException, InventoryUpdateException, ParseException, DataNotFoundException, TransferOrderCreationException, EmailGenerationException {
        return paymentRequestManagementService.approvePaymentRequest(paymentRequest);
    }

    @RequestMapping(method = RequestMethod.POST, value = "reject", produces = MediaType.APPLICATION_JSON)
    public boolean rejectPaymentRequest(@RequestBody final PaymentRequest paymentRequest) throws SumoException, EmailGenerationException {
        return paymentRequestManagementService.rejectPaymentRequest(paymentRequest);
    }



    @RequestMapping(method = RequestMethod.POST, value = "upload-invoice", consumes = MediaType.MULTIPART_FORM_DATA )
    public DocumentDetail uploadDocument(HttpServletRequest request,
                                         @RequestParam(value = "type") FileType type,
                                         @RequestParam(value = "mimeType") MimeType mimeType,
                                         @RequestParam(value = "userId") Integer userId,
                                         @RequestParam(value = "docType") DocUploadType docType,
                                         @RequestParam(value = "file") final MultipartFile file) throws DocumentException, IOException, SumoException {
        return paymentRequestManagementService.uploadDocument(type, mimeType, docType, userId, file);
    }

    @RequestMapping(method = RequestMethod.POST, value = "upload-query-document", consumes = MediaType.MULTIPART_FORM_DATA )
    public DocumentDetail uploadQueryDocument(HttpServletRequest request,
                                         @RequestParam(value = "type") FileType type,
                                         @RequestParam(value = "mimeType") MimeType mimeType,
                                         @RequestParam(value = "userId") Integer userId,
                                         @RequestParam(value = "docType") DocUploadType docType,
                                         @RequestParam(value = "file") final MultipartFile file,
                                         @RequestParam String docName) throws DocumentException, IOException, SumoException {
        return paymentRequestManagementService.uploadQueryDocument(type, mimeType, docType, userId, file, docName);
    }

    @RequestMapping(method = RequestMethod.POST, value = "upload-debit-note", consumes = MediaType.MULTIPART_FORM_DATA )
    public DocumentDetail uploadDebitNoteDocument(HttpServletRequest request,
                                         @RequestParam(value = "type") FileType type,
                                         @RequestParam(value = "mimeType") MimeType mimeType,
                                         @RequestParam(value = "userId") Integer userId,
                                         @RequestParam(value = "docType") DocUploadType docType,
                                         @RequestParam(value = "file") final MultipartFile file) throws DocumentException, IOException, SumoException {
        return paymentRequestManagementService.uploadDebitNoteDocument(type, mimeType, docType, userId, file);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-invoice", consumes = MediaType.APPLICATION_JSON)
    public DocumentDetail getDocument(HttpServletRequest request, @RequestBody Integer docId) throws SumoException {
        return paymentRequestManagementService.getDocument(docId);
    }

    @RequestMapping(method = RequestMethod.POST , value = "get-mandatory-docs",produces = MediaType.APPLICATION_JSON)
    public List<Pair<String,String>> getMandatoryReqDoc(@RequestParam Integer prId)throws SumoException{
        return paymentRequestManagementService.getMandatoryReqDoc(prId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "debit-notes", produces = MediaType.APPLICATION_JSON)
    public List<DebitNoteDetail> getDebitNotes(@RequestParam(required = false) Integer vendorId,
                                               @RequestParam(required = false) Integer unitId,
                                               @RequestParam(required = false) Integer prId,
                                               @RequestParam(required = false) Integer dnId,
                                               @RequestParam(required = false) String invoiceNumber,
                                               @RequestParam(required = false) String startDate,
                                               @RequestParam(required = true) Integer companyId,
                                               @RequestParam(required = false) String endDate,
                                               @RequestParam(required = false) String status) throws SumoException {
        return paymentRequestManagementService.getDebitNotes(vendorId, unitId, prId, dnId, invoiceNumber,
                SCMUtil.parseDate(startDate), SCMUtil.parseDate(endDate), companyId, status);
    }

    @RequestMapping(method = RequestMethod.POST, value = "debit-note-settle", consumes = MediaType.APPLICATION_JSON)
    public DebitNoteDetail settleDebitNote(@RequestBody DebitNoteDetail detail) throws SumoException {
        return paymentRequestManagementService.settleDebitNote(detail);
    }

    @RequestMapping(method = RequestMethod.POST, value = "payment-dates", consumes = MediaType.APPLICATION_JSON)
    public List<PaymentCalendar> getPaymentDatesForPaymentRequest(@RequestBody Integer paymentRequestId) throws SumoException {
        return paymentRequestManagementService.getPaymentDatesForPaymentRequest(paymentRequestId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "payment-sheet")
    public View getPaymentSheet(@RequestBody PaymentSheetVO request) throws Exception {
        PaymentSheetReturnVO paymentSheetReturnVO = paymentRequestManagementService.getPaymentSheet(request);
        List<PaymentRequest> paymentRequests = new ArrayList<>();
        List<PaymentRequest> requestedPaymentRequests = paymentSheetReturnVO.getPaymentRequests().stream()
            .sorted(Comparator.comparing(paymentRequest -> paymentRequest.getVendorId().getId())).collect(Collectors.toList());
        String message=null;
        for (PaymentRequest pr : requestedPaymentRequests) {
            VendorDetailData vendorDetailData = paymentDao.find(VendorDetailData.class, pr.getVendorId().getId());
            if (!vendorDetailData.getStatus().equals(VendorStatus.IN_PROCESS.value())) {
                paymentRequests.add(pr);
            }
            else {

                message="vendor Id "+vendorDetailData.getVendorId() +" for payment request Id "+pr.getPaymentRequestId() +" is in IN_PROCESS state";
                break;
            }
        }
        if(message !=null)
        {
            //throw new Exception("cannot find vendor");
            throw new Exception(message);
        }
        if (paymentRequests == null || paymentRequests.isEmpty()) {
            return null;
        }


        List<PaymentRequest> paymentRequestsSheet = new ArrayList<>();
        List<PaymentRequest> paymentRequestsAutoSettle = new ArrayList<>();
        for (PaymentRequest paymentRequest : paymentRequests) {
            if (paymentRequest.getPaidAmount().compareTo(BigDecimal.ZERO) == 0 && Objects.nonNull(paymentRequest.getAdvancePaymentId())) {
                paymentRequestsAutoSettle.add(paymentRequest);
            } else {
                paymentRequestsSheet.add(paymentRequest);
            }
        }

        View view = null;
        CompanyBankMapping bankDetails = null;
        if (request.getBankName().equals(PaymentBanks.YES_BANK.name())) {
            bankDetails = paymentRequestManagementService.getBanksOfComapny(request.getCompanyId(), request.getBankName());
            view = excelViewGenerator.getYesBankPRView(paymentRequestsSheet, paymentSheetReturnVO.getVendors(), bankDetails, true);
        } else if (request.getBankName().equals(PaymentBanks.KOTAK.name())) {
            bankDetails = paymentRequestManagementService.getBanksOfComapny(request.getCompanyId(), request.getBankName());
            view = excelViewGenerator.getKotakBankPRView(paymentRequestsSheet, paymentSheetReturnVO.getVendors(), bankDetails, true);
        } else if (request.getBankName().equals(PaymentBanks.KOTAK_V2.name())) {
            bankDetails = paymentRequestManagementService.getBanksOfComapny(request.getCompanyId(), request.getBankName());
            view = excelViewGenerator.getKotakV2BankPRView(paymentRequestsSheet, paymentSheetReturnVO.getVendors(), bankDetails, true);
        } else if (request.getBankName().equals(PaymentBanks.HDFC.name())) {
            bankDetails = paymentRequestManagementService.getBanksOfComapny(request.getCompanyId(), request.getBankName());
            view = excelViewGenerator.getHdfcBankPRView(paymentRequestsSheet, paymentSheetReturnVO.getVendors(), bankDetails, true);
        } else if (request.getBankName().equals(PaymentBanks.ICICI.name())) {
            bankDetails = paymentRequestManagementService.getBanksOfComapny(request.getCompanyId(), request.getBankName());
            view = excelViewGenerator.getIciciBankPRView(paymentRequestsSheet, paymentSheetReturnVO.getVendors(), bankDetails, true);
        } else {
            throw new SumoException("Invalid bank selected.");
        }

        List<PaymentRequestStatusChangeVO> changeRequestList = new ArrayList<>();
        for (PaymentRequest paymentRequest : paymentRequests) {
            PaymentRequestStatusChangeVO paymentRequestStatusChangeVO = new PaymentRequestStatusChangeVO();
            paymentRequestStatusChangeVO.setCurrentStatus(paymentRequest.getCurrentStatus());
            paymentRequestStatusChangeVO.setNewStatus(PaymentRequestStatus.SENT_FOR_PAYMENT);
            paymentRequestStatusChangeVO.setUpdatedBy(request.getUpdatedBy());
            paymentRequestStatusChangeVO.setPaymentRequestId(paymentRequest.getPaymentRequestId());
            changeRequestList.add(paymentRequestStatusChangeVO);
            //paymentRequestManagementService.changePaymentRequestStatus(paymentRequestStatusChangeVO);
        }
        paymentRequestManagementService.changePaymentRequestStatusInBulk(changeRequestList);

        return view;
    }

    @RequestMapping(method = RequestMethod.POST, value = "payment-sheet-ad-hoc")
    public View getPaymentSheetAdHoc(@RequestBody PaymentSheetVO request) throws SumoException {
        PaymentSheetReturnVO paymentSheetReturnVO = paymentRequestManagementService.getPaymentSheet(request);
        List<PaymentRequest> paymentRequests = paymentSheetReturnVO.getPaymentRequests().stream()
                .sorted(Comparator.comparing(paymentRequest -> paymentRequest.getVendorId().getId())).collect(Collectors.toList());
        View view;
        if (request.getBankName().equals(PaymentBanks.YES_BANK.name())) {
            CompanyBankMapping bankDetails = paymentRequestManagementService.getBanksOfComapny(request.getCompanyId(), request.getBankName());
            view = excelViewGenerator.getYesBankPRView(paymentRequests, paymentSheetReturnVO.getVendors(), bankDetails,false);
        } else if (request.getBankName().equals(PaymentBanks.KOTAK.name())) {
            CompanyBankMapping bankDetails = paymentRequestManagementService.getBanksOfComapny(request.getCompanyId(), request.getBankName());
            view = excelViewGenerator.getKotakBankPRView(paymentRequests, paymentSheetReturnVO.getVendors(), bankDetails , false );
        } else if (request.getBankName().equals(PaymentBanks.KOTAK_V2.name())) {
            CompanyBankMapping bankDetails = paymentRequestManagementService.getBanksOfComapny(request.getCompanyId(), request.getBankName());
            view = excelViewGenerator.getKotakV2BankPRView(paymentRequests, paymentSheetReturnVO.getVendors(), bankDetails,false);
        } else {
            throw new SumoException("Invalid bank selected.");
        }
        return view;
    }

    @RequestMapping(method = RequestMethod.POST, value = "payment-requests-unblock")
    public boolean unblockPaymentRequests(@RequestBody PaymentSheetVO request) throws SumoException {
        return paymentRequestManagementService.unblockPaymentRequests(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "payment-requests-block")
    public boolean blockPaymentRequests(@RequestBody PaymentSheetVO request) throws SumoException {
        return paymentRequestManagementService.blockPaymentRequests(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "upload-payment-sheet", consumes = MediaType.MULTIPART_FORM_DATA)
    public List<PaymentRequest> uploadPaymentSheet(@RequestParam(value = "file") final MultipartFile file,
                                                   @RequestParam(value = "bankName") final String bankName
    ) throws IOException, SumoException {
        return paymentRequestManagementService.uploadPaymentSheet(bankName, file);
    }

    @RequestMapping(method = RequestMethod.POST, value = "payment-request-pay-adhoc")
    public boolean settlePaymentRequestSingle(@RequestBody PaymentRequestStatusChangeVO detail) throws SumoException {
        return paymentRequestManagementService.startPaymentProcessAdhoc(detail);
    }

    @RequestMapping(method = RequestMethod.POST, value = "payment-requests-settle")
    public boolean settlePaymentRequestBulk(@RequestBody Map requestObj,@RequestParam Boolean isForceUploaded) throws SumoException, EmailGenerationException {
    	List<PaymentRequest> paymentRequests = (List) requestObj.get("paymentRequests");
        String selectedCompany =(String) requestObj.get("selectedCompany");
        String utrUploadedBy = (String) requestObj.get("utrUploadedBy");
    	return paymentRequestManagementService.settlePaymentRequestBulk(paymentRequests,selectedCompany,isForceUploaded,utrUploadedBy);
    }

    @RequestMapping(method = RequestMethod.POST, value = "payment-request-settle")
    public boolean settlePaymentRequestSingle(@RequestBody PRPaymentDetail detail) throws SumoException, EmailGenerationException {
        return paymentRequestManagementService.settlePaymentRequestSingle(detail);
    }

    @RequestMapping(method = RequestMethod.POST, value = "payment-request-force-settle")
    public boolean forceSettlePaymentRequests(@RequestBody PaymentSheetVO request) throws SumoException {
        return paymentRequestManagementService.forceSettlePaymentRequests(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "banks-of-company")
    public List<BankVO> getBanksOfComapny(@RequestBody int companyId) throws SumoException {
        return paymentRequestManagementService.getBanksOfComapny(companyId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-vendor-pr-summary")
    public VendorPRVO getVendorPRVO(@RequestBody int vendorId) throws SumoException {
        return paymentRequestManagementService.getVendorPRVO(vendorId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "add-holiday")
    public boolean addHoliday(@RequestBody HolidayDetails holidayDetails) throws SumoException{
        return paymentRequestManagementService.addHoliday(holidayDetails);
    }

    @RequestMapping(method = RequestMethod.GET, value = "list-of-holidays")
    public List<HolidayDetails> listOfHolidays(@RequestParam(required = false)String holidayType,@RequestParam(required = false)Integer year) throws SumoException{
        return paymentRequestManagementService.listOfHolidays(holidayType,year);
    }

    @RequestMapping(method = RequestMethod.PUT, value = "activate-or-deactivate-holiday")
    public boolean activateOrDeactivateHoliday(@RequestBody HolidayActivateDeactivate request){
        return paymentRequestManagementService.activaeteDeActivateHoliday(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-vendor-advance-payment")
    public VendorAdvancePayment getVendorAdvancePayment(@RequestBody VendorAdvancePayment vendorAdvancePayment) throws SumoException {
        return paymentRequestManagementService.getVendorAdvancePayment(vendorAdvancePayment, Boolean.TRUE, null);
    }

    @RequestMapping(method = RequestMethod.POST, value = "create-vendor-advance-payment")
    public PaymentRequest createVendorAdvancePayment(@RequestBody VendorAdvancePayment vendorAdvancePayment) throws SumoException {
        return paymentRequestManagementService.createVendorAdvancePayment(vendorAdvancePayment);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-all-vendor-advances")
    public List<VendorAdvancePayment> getAllVendorAdvances(@RequestParam(required = false) String startDate, @RequestParam(required = false) String endDate,
                                                           @RequestParam(required = false) Integer vendorId, @RequestParam(required = false) String status, @RequestParam(required = false) Integer advancePaymentId) throws SumoException {
        return paymentRequestManagementService.getAllVendorAdvances(AppUtils.getDate(startDate, "yyyy-MM-dd"), AppUtils.getDate(endDate, "yyyy-MM-dd"), vendorId,status,advancePaymentId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "submit-refunded-date", produces = MediaType.APPLICATION_JSON)
    public Boolean submitAdvanceRefundedDate(@RequestParam Integer advancePaymentId, @RequestParam String refundedDate, @RequestParam Integer receivedBy, @RequestParam Integer refundDocId) throws SumoException {
        return paymentRequestManagementService.submitAdvanceRefundedDate(advancePaymentId, refundedDate, receivedBy, refundDocId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "refresh-vendor-advance-payments-cache", produces = MediaType.APPLICATION_JSON)
    public void refreshVendorAdvancePaymentsCache() throws SumoException {
        paymentRequestManagementService.refreshVendorAdvancePaymentsCache(null);
    }

    @Scheduled(cron = "0 0 09 * * *", zone = "GMT+05:30")
    @PostMapping(value = "send-vendor-advances-reminder", produces = MediaType.APPLICATION_JSON)
    public void sendVendorAdvancesReminder() {
        paymentRequestManagementService.sendVendorAdvancesReminder();
    }

    @Scheduled(cron = "0 0 09 * * *", zone = "GMT+05:30")
    @PostMapping(value = "reject-queried-prs")
    public void rejectQueriedPrs() {
        paymentRequestManagementService.rejectQueriedPrs();
    }

    @GetMapping(value = "validate-pr-for-query", produces = MediaType.APPLICATION_JSON)
    public Boolean validatePrForQuery(@RequestParam Integer prId) throws SumoException {
        return paymentRequestManagementService.validatePrForQuery(prId);
    }

    @PostMapping(value = "upload-advance-refund", consumes = MediaType.MULTIPART_FORM_DATA )
    public DocumentDetail uploadAdvanceRefund(HttpServletRequest request,
                                              @RequestParam(value = "type") FileType type,
                                              @RequestParam(value = "mimeType") MimeType mimeType,
                                              @RequestParam(value = "userId") Integer userId,
                                              @RequestParam(value = "docType") DocUploadType docType,
                                              @RequestParam(value = "file") final MultipartFile file,
                                              @RequestParam String docName, @RequestParam Integer advancePaymentId) throws DocumentException, IOException, SumoException {
        return paymentRequestManagementService.uploadAdvanceRefund(type, mimeType, docType, userId, file, docName, advancePaymentId);
    }

    @GetMapping(value = "employee-payment-cards", produces = MediaType.APPLICATION_JSON)
    public List<String> getEmployeePaymentCards(@RequestParam Integer userId) throws SumoException {
        return paymentRequestManagementService.getEmployeePaymentCards(userId);
    }

    @PostMapping(value = "upload-card-payment-proof", consumes = MediaType.MULTIPART_FORM_DATA )
    public DocumentDetail uploadCardPaymentProof(HttpServletRequest request,
                                              @RequestParam(value = "type") FileType type,
                                              @RequestParam(value = "mimeType") MimeType mimeType,
                                              @RequestParam(value = "userId") Integer userId,
                                              @RequestParam(value = "docType") DocUploadType docType,
                                              @RequestParam(value = "file") final MultipartFile file,
                                              @RequestParam String docName) throws DocumentException, IOException, SumoException {
        return paymentRequestManagementService.uploadCardPaymentProof(type, mimeType, docType, userId, file, docName);
    }
}
