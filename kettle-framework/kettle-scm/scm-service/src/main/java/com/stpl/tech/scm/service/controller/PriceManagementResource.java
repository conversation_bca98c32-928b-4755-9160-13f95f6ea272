package com.stpl.tech.scm.service.controller;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import javax.ws.rs.core.MediaType;

import com.stpl.tech.scm.domain.model.ScmRecipeProductCost;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.service.PriceManagementService;
import com.stpl.tech.scm.domain.model.CostDetail;

@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
        + SCMServiceConstants.PRICE_MANAGEMENT_ROOT_CONTEXT)
public class PriceManagementResource extends AbstractSCMResources {
	
	@Autowired
    private PriceManagementService priceManagementService;

    @RequestMapping(method = RequestMethod.GET, value = "price-details-for-product", produces = MediaType.APPLICATION_JSON)
    public List<CostDetail> getPriceDetailsForProduct(@RequestParam(required = true) final int unitId,
            @RequestParam(required = true) final String keyType,
            @RequestParam(required = true) final int keyId) {
        return priceManagementService.getPriceDetails(unitId,keyType,keyId);
    }
    
    @RequestMapping(method = RequestMethod.POST, value = "add-price-details", produces = MediaType.APPLICATION_JSON)
    public boolean addPriceDetails(@RequestBody CostDetail costDetail) throws InventoryUpdateException {
        return priceManagementService.addPriceDetails(costDetail);
    }
    
    @RequestMapping(method = RequestMethod.PUT, value = "update-price-details", produces = MediaType.APPLICATION_JSON)
    public boolean upatePriceDetails(@RequestBody CostDetail costDetail) {
        return priceManagementService.updatePriceDetails(costDetail);
    }
    
    @RequestMapping(method = RequestMethod.GET, value = "price-details-for-unit", produces = MediaType.APPLICATION_JSON)
    public List<CostDetail> getPriceDetailsForUnit(@RequestParam(required = true) final int unitId) {
        return priceManagementService.getPriceDetailsForUnit(unitId);
    }
    
    @RequestMapping(method = RequestMethod.GET, value = "price-details-for-product-list", produces = MediaType.APPLICATION_JSON)
    public Map<Integer, BigDecimal> getPriceDetailsForProductList(@RequestParam(required = true) final int unitId,
            @RequestParam(required = true) final String keyType,
            @RequestParam(required = true) final List<Integer> productIdList) {
        return priceManagementService.getWeightedPriceDetailsForProducts(unitId,keyType,productIdList);
    }

    @RequestMapping(method = RequestMethod.GET, value = "scm-recipe-cost", produces = MediaType.APPLICATION_JSON)
    public Map<Integer, ScmRecipeProductCost> getScmRecipeCost(@RequestParam(required = false, defaultValue = "NCR") String region ,
                                                                               @RequestParam(required = true) final List<Integer> productIdList) {
        return priceManagementService.getScmRecipeCost(region, productIdList);
    }
}
