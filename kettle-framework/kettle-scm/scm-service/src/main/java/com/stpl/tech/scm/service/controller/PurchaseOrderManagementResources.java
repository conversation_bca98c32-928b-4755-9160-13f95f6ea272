
package com.stpl.tech.scm.service.controller;

import com.itextpdf.text.DocumentException;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.exception.PurchaseOrderCreationException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.PurchaseOrderManagementService;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.core.util.model.ConsumptionView;
import com.stpl.tech.scm.core.util.model.PurchaseOrderCreateVO;
import com.stpl.tech.scm.domain.model.*;
import com.stpl.tech.scm.service.model.ExtraGrEligibilityVO;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.TemplateRenderingException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by Rahul Singh on 22-06-2016.
 */
@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
        + SCMServiceConstants.PURCHASE_ORDER_MANAGEMENT_ROOT_CONTEXT)
public class PurchaseOrderManagementResources extends AbstractSCMResources {

    private static final Logger LOG = LoggerFactory.getLogger(PurchaseOrderManagementResources.class);

    @Autowired
    private PurchaseOrderManagementService purchaseOrderService;

    @RequestMapping(method = RequestMethod.POST, value = "create-po", produces = MediaType.APPLICATION_JSON)
    public Integer createPurchaseOrder(HttpServletRequest request, @RequestBody PurchaseOrderCreateVO requestVO)
            throws PurchaseOrderCreationException, SumoException {
        return purchaseOrderService.createPurchaseOrder(requestVO);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-department-budget-data", produces = MediaType.APPLICATION_JSON)
    public CapexBudgetDetail getDepartmentBudgetData(@RequestParam Integer unitId,@RequestParam String isFixedAssetOrGoods) {
        return purchaseOrderService.getDepartmentBudgetData(unitId,isFixedAssetOrGoods);
    }



    @RequestMapping(method = RequestMethod.GET, value = "get-purchase-orders", produces = MediaType.APPLICATION_JSON)
    public List<PurchaseOrder> getPurchaseOrders(@RequestParam int vendorId,
                                                 @RequestParam int deliveryUnitId,
                                                 @RequestParam int dispatchId) {
        List<PurchaseOrder> purchaseOrderList = purchaseOrderService.getClonableOrders(vendorId, deliveryUnitId, dispatchId);
        return purchaseOrderList != null
                ? purchaseOrderList.stream()
                .filter(purchaseOrder -> !(purchaseOrder.getStatus().equals(PurchaseOrderStatus.CANCELLED)
                        || purchaseOrder.getStatus().equals(PurchaseOrderStatus.REJECTED)))
                .collect(Collectors.toList())
                : null;
    }


    @RequestMapping(method = RequestMethod.GET, value = "get-created-orders", produces = MediaType.APPLICATION_JSON)
    public List<PurchaseOrder> getCreatedOrders(@RequestParam(required = false) Integer vendorId,
                                                @RequestParam int deliveryUnitId,
                                                @RequestParam (required = false) List<Integer> skus,
                                                @RequestParam(required = false) Integer dispatchId,
                                                @RequestParam(required = false) String startDate,
                                                @RequestParam(required = false) String endDate,
                                                @RequestParam(required = false) Integer purchaseOrderId,
                                                @RequestParam(value = "status", required = false) PurchaseOrderStatus status,
                                                @RequestParam(required = false, defaultValue = "false") boolean isView) {

        List<PurchaseOrderStatus> statusList = isView
                ? Arrays.asList(PurchaseOrderStatus.values())
                : Arrays.asList(PurchaseOrderStatus.CREATED);

        return purchaseOrderService.findOrdersByStatus(vendorId, dispatchId,skus,deliveryUnitId, statusList,
                SCMUtil.parseDate(startDate), SCMUtil.parseDate(endDate), purchaseOrderId, status,null);
    }


    @RequestMapping(method = RequestMethod.POST, value = "approve-po/{poId}/{userId}", produces = MediaType.APPLICATION_JSON)
    public boolean approvePurchaseOrder(HttpServletRequest request,
                                        @PathVariable(value = "poId") Integer poId,
                                        @PathVariable(value = "userId") Integer userId)
            throws DocumentException, TemplateRenderingException, IOException {
        return purchaseOrderService.approvePurchaseOrder(poId, userId);
    }


    @RequestMapping(method = RequestMethod.POST, value = "reject-po/{poId}/{userId}", produces = MediaType.APPLICATION_JSON)
    public boolean rejectPurchaseOrder(HttpServletRequest request,
                                       @PathVariable(value = "poId") Integer poId,
                                       @PathVariable(value = "userId") Integer userId)
            throws DocumentException, TemplateRenderingException, IOException, SumoException {
        return purchaseOrderService.rejectPurchaseOrder(poId, userId);
    }


    @RequestMapping(method = RequestMethod.POST, value = "cancel-po/{poId}/{userId}", produces = MediaType.APPLICATION_JSON)
    public boolean cancelPurchaseOrder(HttpServletRequest request,
                                       @PathVariable(value = "poId") Integer poId,
                                       @PathVariable(value = "userId") Integer userId)
            throws DocumentException, TemplateRenderingException, IOException, SumoException {
        return purchaseOrderService.cancelPurchaseOrder(poId, userId);
    }


    @RequestMapping(method = RequestMethod.POST, value = "close-po/{poId}/{userId}", produces = MediaType.APPLICATION_JSON)
    public boolean closePurchaseOrder(HttpServletRequest request,
                                      @PathVariable(value = "poId") Integer poId,
                                      @PathVariable(value = "userId") Integer userId)
            throws DocumentException, TemplateRenderingException, IOException, SumoException {
        return purchaseOrderService.closePurchaseOrder(poId, userId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-pending-orders", produces = MediaType.APPLICATION_JSON)
    public List<PurchaseOrder> getPendingOrders(@RequestParam int vendorId,
                                                @RequestParam int deliveryUnitId,
                                                @RequestParam(required = false) String startDate,
                                                @RequestParam(required = false) String endDate,
                                                @RequestParam(required = false) Integer purchaseOrderId,
                                                @RequestParam(value = "status", required = false) PurchaseOrderStatus status,
                                                @RequestParam int dispatchId) {
        List<PurchaseOrderStatus> statusList = Arrays.asList(PurchaseOrderStatus.APPROVED,
                PurchaseOrderStatus.IN_PROGRESS);
        return purchaseOrderService.findOrdersByStatus(vendorId, dispatchId,null, deliveryUnitId, statusList,
                SCMUtil.parseDate(startDate), SCMUtil.parseDate(endDate), purchaseOrderId, status,PurchaseOrderExtendedStatus.EXPIRED);

    }

    @RequestMapping(method = RequestMethod.GET, value = "get-pos-for-advance", produces = MediaType.APPLICATION_JSON)
    public List<PurchaseOrder> getPosForAdvance(@RequestParam Integer vendorId) throws SumoException {
        return purchaseOrderService.getPosForAdvance(vendorId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "extra-gr-eligibility", produces = MediaType.APPLICATION_JSON)
    public List<Integer> checkExtraGrEligibility(@RequestBody ExtraGrEligibilityVO vo) {
        return purchaseOrderService.checkExtraGrEligibility(vo.getUnitId(), vo.getVendorId(), vo.getDispatchId(),vo.getPoIds(), vo.getSkus());
    }

    @RequestMapping(method = RequestMethod.GET, value = "consumption", produces = MediaType.APPLICATION_JSON)
    public List<ConsumptionView> getConsumptionForPurchase(@RequestParam int days, @RequestParam String skus, @RequestParam int unitId){
        return purchaseOrderService.getConsumptionForPurchase(days, skus, unitId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "extend-po", produces = MediaType.APPLICATION_JSON)
    public PurchaseOrderExtendedStatusLog extendPurchaseOrder(@RequestBody PurchaseOrderExtendedStatusLog purchaseOrderExtendedStatusLog) throws SumoException {
        return purchaseOrderService.extendPurchaseOrder(purchaseOrderExtendedStatusLog);

    }
    @PostMapping(value = "update-approval-po" , produces = MediaType.APPLICATION_JSON)
    public Boolean updateApprovedPurchaseOrder(@RequestBody PurchaseOrderDetails purchaseOrderDetails) throws SumoException
    {
        return purchaseOrderService.updateApprovedPurchaseOrder(purchaseOrderDetails);
    }


    @Scheduled(cron = "0 15 05 * * *", zone = "GMT+05:30")
    @RequestMapping(method = RequestMethod.GET, value = "update-po", produces = MediaType.APPLICATION_JSON)
    public void expirePurchaseOrder() throws SumoException,EmailGenerationException {
        LOG.info("updating status of po ::::::");
        purchaseOrderService.updatePoStatus();
    }

}
