package com.stpl.tech.scm.service.controller;

import java.util.List;

import com.stpl.tech.scm.domain.model.TrimmedCostDetail;
import com.stpl.tech.scm.domain.model.UnitInventoryData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.service.SCMProductPriceManagementService;
import com.stpl.tech.scm.core.service.StockManagementService;
import com.stpl.tech.scm.domain.model.CostDetail;
import com.stpl.tech.scm.domain.model.DayCloseEvent;

@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
		+ SCMServiceConstants.SCM_DATA_ROOT_CONTEXT)
public class SCMDataResource {

	private final Logger LOG = LoggerFactory.getLogger(SCMDataResource.class);

	@Autowired
	private SCMProductPriceManagementService priceManagementService;

	@Autowired
	private StockManagementService stockManagementService;

	@RequestMapping(method = RequestMethod.POST, value = "inventory")
	@ResponseBody
	public List<CostDetail> fetchUnitInventory(@RequestBody final int unitId) {
		LOG.info("Request to get cost detail for unit id {}", unitId);
		return priceManagementService.fetchUnitInventory(unitId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "new-inventory/{unitId}")
	@ResponseBody
	public List<TrimmedCostDetail> fetchNewUnitInventory(@PathVariable final int unitId, @RequestBody List<Integer> ids) {
		LOG.info("Request to get cost detail for unit id {} for {} critical product ids ",unitId, ids.size());
		return priceManagementService.fetchNewUnitInventory(unitId, ids);
	}

	@RequestMapping(method = RequestMethod.POST, value = "closure")
	@ResponseBody
	public DayCloseEvent fetchUnitDayClose(@RequestBody final int unitId) {
		return stockManagementService.fetchUnitLastDayClose(unitId);
	}

}
