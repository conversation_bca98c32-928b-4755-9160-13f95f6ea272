package com.stpl.tech.scm.service.aspect;

import com.stpl.tech.master.core.external.acl.service.ACLService;
import com.stpl.tech.master.core.external.acl.service.TokenService;
import com.stpl.tech.master.core.external.acl.service.impl.JWTToken;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.DayCloseInitiatedException;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.StockManagementService;
import com.stpl.tech.scm.core.service.WarehouseStockManagementService;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.model.SCMDayCloseEventData;
import com.stpl.tech.scm.domain.model.UnitDetail;
import com.stpl.tech.util.ACLUtil;
import com.stpl.tech.util.AppUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.Objects;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 11-06-2017.
 */

@Aspect
@Component
public class DayCloseAspect {

    private static final Logger LOG = LoggerFactory.getLogger(DayCloseAspect.class);

    @Autowired
    private WarehouseStockManagementService warehouseStockManagementService;

    @Autowired
    private StockManagementService stockManagementService;

    @Autowired
    private TokenService<JWTToken> jwtService;

    @Autowired
    private ACLService aclService;

    @Autowired
    private EnvProperties props;

    @Autowired
    private SCMCache scmCache;


    @Around("@annotation(com.stpl.tech.scm.service.annotation.DayClosureCheck)")
    public Object checkTransactionIsAvailable(ProceedingJoinPoint joinPoint) throws Throwable {
        LOG.info("::::::::::Inside check transaction available aspect:::::::");
        HttpServletRequest request = getRequestArgument(joinPoint);
        if (props.getDayCloseCheck() && request != null) {
            LOG.info("::::::::::Found request parameters in the request:::::::");
            String module = ACLUtil.getInstance().convertURIToModule(request.getRequestURI());
            if (request.getHeader("auth-internal") != null || aclService.isPreAuthenticated(module)) {
                LOG.info("::::::::::This is a pre-authenticated API for external systems hence continuing:::::::");
                return joinPoint.proceed();
            } else {
                JWTToken jwtToken = new JWTToken();
                jwtService.parseToken(jwtToken, request.getHeader("auth"));
                int unitId = jwtToken.getUnitId();
                UnitDetail unit = scmCache.getUnitDetail(unitId);
                if (validateDayClose(unit)) {
                    String[] servletPath = request.getRequestURL().toString().split("/");
                    String endPoint = servletPath[servletPath.length - 1];
                    if (endPoint.equalsIgnoreCase("wastage-event")) {
                        try {
                            String param = request.getParameter("isManual");
                            if (Objects.isNull(param)) {
                                return joinPoint.proceed();
                            }
                        } catch (Exception e) {
                            LOG.error("Exception Occurred While getting param from the Wastage event endpoint :: ",e);
                        }
                    }
                    SCMDayCloseEventData dayCloseEventData = stockManagementService.getLastSuccessfulDayCloseEventData(unitId);
                    if (Objects.nonNull(dayCloseEventData) && shouldBlockTransactions(dayCloseEventData)) {
                        String message = "After Day Close No transactions will happen till 5'o clock in the morning..!";
                        LOG.error(message + unitId + "-" + unit.getUnitName());
                        throw new DayCloseInitiatedException(message);
                    }
                    return joinPoint.proceed();
                } else {
                    String message = "";
                    if(SCMUtil.isCafe(unit)){
                       message += "Variance Report has to be generated before carrying out any transaction for :: ";
                    }else{
                       message += "Please complete the Day Close Process before proceeding for :: ";
                    }
                    message += unitId + "-" + unit.getUnitName();
                    LOG.error(message);
                    throw new DayCloseInitiatedException(message);
                }
            }
        }
        return joinPoint.proceed();
    }

    private Boolean shouldBlockTransactions(SCMDayCloseEventData dayCloseEventData) {
        return Objects.isNull(dayCloseEventData.getVarianceStatus()) && AppUtils.getBusinessDate().equals(dayCloseEventData.getBusinessDate());
    }

    private HttpServletRequest getRequestArgument(ProceedingJoinPoint pjp) {
        for (Object object : pjp.getArgs()) {
            if (object instanceof HttpServletRequest) {
                return (HttpServletRequest) object;
            }
        }
        return null;
    }

    private boolean validateDayClose(UnitDetail unitDetail) {
        Integer unitId = unitDetail.getUnitId();
        if (SCMUtil.isCafe(unitDetail)) {
            return stockManagementService.getInitiatedStockEvents(unitId) == null;
        } else {
            return warehouseStockManagementService.getActiveWarehouseDayCloseEvent(unitId) == null
                    && !warehouseStockManagementService.getThresholdDayCloseTime(unitId);
        }
    }

}
