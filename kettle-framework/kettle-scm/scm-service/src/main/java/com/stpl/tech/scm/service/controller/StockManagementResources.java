/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.service.controller;

import java.math.BigDecimal;
import java.net.URISyntaxException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.MediaType;

import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.domain.model.UnitStatus;
import com.stpl.tech.scm.data.model.ApprovalDetailData;
import com.stpl.tech.scm.data.model.RequestOrderData;
import com.stpl.tech.scm.domain.model.*;
import com.google.common.base.Stopwatch;
import com.google.gson.Gson;
import com.stpl.tech.scm.reports.modal.VarianceModal;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.View;

import com.stpl.tech.kettle.core.CalculationType;
import com.stpl.tech.kettle.core.data.budget.vo.BudgetDetail;
import com.stpl.tech.kettle.core.data.budget.vo.BudgetKey;
import com.stpl.tech.kettle.core.data.budget.vo.InventoryAggregate;
import com.stpl.tech.kettle.core.data.budget.vo.WastageAggregate;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.exception.WebServiceCallException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.domain.model.DayCloseRequestStatus;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.inventory.model.InventoryAction;
import com.stpl.tech.master.inventory.model.InventorySource;
import com.stpl.tech.master.inventory.model.ProductQuantityData;
import com.stpl.tech.master.inventory.model.QuantityRequestData;
import com.stpl.tech.master.inventory.model.QuantityResponseData;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.ThresholdType;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.DayCloseInitiatedException;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.exception.NegativeStockException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.GoodsReceiveManagementService;
import com.stpl.tech.scm.core.service.SCMMetadataService;
import com.stpl.tech.scm.core.service.ServiceOrderManagementService;
import com.stpl.tech.scm.core.service.StockManagementService;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.core.util.webservice.KettleServiceClientEndpoints;
import com.stpl.tech.scm.core.util.webservice.WebServiceHelper;
import com.stpl.tech.scm.data.model.SCMDayCloseEventData;
import com.stpl.tech.scm.data.model.StockEventCalendarData;
import com.stpl.tech.scm.domain.model.ConsumptionData;
import com.stpl.tech.scm.domain.model.DayCloseEvent;
import com.stpl.tech.scm.domain.model.DayWiseExpiryProduct;
import com.stpl.tech.scm.domain.model.PriceUpdateEntryType;
import com.stpl.tech.scm.domain.model.ProductDefinition;
import com.stpl.tech.scm.domain.model.ProductStockForUnit;
import com.stpl.tech.scm.domain.model.RequestScmItem;
import com.stpl.tech.scm.domain.model.StockCalendarEventCheckVO;
import com.stpl.tech.scm.domain.model.StockEventType;
import com.stpl.tech.scm.domain.model.StockInventoryData;
import com.stpl.tech.scm.domain.model.StockTakeType;
import com.stpl.tech.scm.domain.model.UnitDetail;
import com.stpl.tech.scm.domain.model.WastageAggregatedData;
import com.stpl.tech.scm.domain.model.WastageData;
import com.stpl.tech.scm.domain.model.WastageEvent;
import com.stpl.tech.scm.service.annotation.DayClosureCheck;
import com.stpl.tech.scm.service.controller.view.ExcelViewGenerator;
import com.stpl.tech.scm.service.model.UnitData;
import com.stpl.tech.scm.service.model.UnitProductReferenceData;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.JSONSerializer;
import com.stpl.tech.util.notification.model.ResponseData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.View;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.MediaType;
import java.math.BigDecimal;
import java.net.URISyntaxException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.TimeUnit;

@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
        + SCMServiceConstants.STOCK_MANAGEMENT_ROOT_CONTEXT)
public class StockManagementResources extends AbstractSCMResources {
    private static final Logger LOG = LoggerFactory.getLogger(StockManagementResources.class);

    @Autowired
    private StockManagementService stockManagementService;

    @Autowired
    private SCMCache scmCache;

    @Autowired
    private GoodsReceiveManagementService goodsReceiveManagementService;

    @Autowired
    private ExcelViewGenerator excelViewGenerator;

    @Autowired
    private SCMMetadataService metaService;

    @Autowired
    private EnvProperties props;

    @Autowired
    private ServiceOrderManagementService serviceOrderService;

    @Autowired
    private MasterDataCache masterDataCache;

    @RequestMapping(method = RequestMethod.POST, value = "day-close-event", consumes = MediaType.APPLICATION_JSON)
    public ResponseData postDayCloseEventForUnit(@RequestBody ConsumptionData scmProductConsumption) {
        Stopwatch watch = Stopwatch.createUnstarted();
        watch.start();
        ResponseData<ConsumptionData> response = new ResponseData<>();
        if (Boolean.FALSE.equals(stockManagementService.checkOpeningRequired(scmProductConsumption.getUnitId()))) {
            try {
                if (Boolean.TRUE.equals(SCMUtil.isSameDate(SCMUtil.getCurrentBusinessDate(), scmProductConsumption.getBusinessDate())) ) {
                    DayCloseEvent dayCloseEvent = stockManagementService.checkClosingInitiated(scmProductConsumption.getUnitId());
                    if (Objects.nonNull(dayCloseEvent) && Boolean.FALSE.equals(AppUtils.isSameDate(dayCloseEvent.getBusinessDate(),scmProductConsumption.getBusinessDate())) ) {
                        response.setSuccess(false);
                        response.setMessage("There is Pending Sumo Day Close !!");
                        return response;
                    }
                }
                response = stockManagementService.initiateDayClose(scmProductConsumption);
            } catch (Exception e) {
                response.setSuccess(false);
                response.setMessage(e.getMessage());
            }
        } else {
            response.setSuccess(false);
            response.setMessage("OPENING STOCK REQUIRED");
        }
        System.out.println(
                "########## , Time Taken To Initiate Day Close ," + watch.stop().elapsed(TimeUnit.MILLISECONDS));
        LOG.info("response for sumo day close initiate : {} ",new Gson().toJson(response));
        return response;
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-consumption", consumes = MediaType.APPLICATION_JSON)
    public ResponseData getConsumption(@RequestBody ConsumptionData scmProductConsumption) {
        LOG.error("loading consumption data  for unit: {}", scmProductConsumption.getUnitId());
        ResponseData<ConsumptionData> response = new ResponseData<>();
        try {
            response.setPayload(stockManagementService.loadConsumptionData(scmProductConsumption.getUnitId(),
                    scmProductConsumption.getBusinessDate(), scmProductConsumption.getClosureId()));
            response.setSuccess(true);
        } catch (Exception e) {
            response.setSuccess(false);
            response.setMessage(e.getMessage());
            LOG.info("Error while loading consumption data  for unit {}", scmProductConsumption.getUnitId());
        }
        return response;
    }

    @RequestMapping(method = RequestMethod.POST, value = "cancel-day-close-event", consumes = MediaType.APPLICATION_JSON)
    public ResponseData cancelDayCloseEventForUnit(@RequestBody ConsumptionData scmProductConsumption) {
        ResponseData<String> response = new ResponseData<>();

        try {
            boolean cancelled = stockManagementService.cancelDayClose(scmProductConsumption.getUnitId(), scmProductConsumption.getClosureId());
            response.setMessage("SUCCESS");
            response.setSuccess(cancelled);
        } catch (Exception e) {
            response.setMessage(e.getMessage());
            response.setSuccess(false);
        }
        return response;
    }

    @RequestMapping(method = RequestMethod.POST, value = "check-opening-for-cafe", consumes = MediaType.APPLICATION_JSON)
    public Boolean checkOpeningForCafe(@RequestBody final int unitId) {
        return stockManagementService.checkOpeningRequired(unitId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "check-pending-transfers")
    public Integer checkSpecialOrders(@RequestParam final int unitId) {
        return stockManagementService.checkSpecialOrders(unitId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get/quantity", consumes = MediaType.APPLICATION_JSON)
    public QuantityResponseData getQuantityAtHand(@RequestBody final QuantityRequestData request) {
        List<ProductQuantityData> data = new ArrayList<>();
        StockInventoryData productStock = null;
        try {
            productStock = stockManagementService.getExpectedValues(request.getUnitId(), AppConstants.ADMIN_USER_ID,
                    StockTakeType.DAILY, StockEventType.CLOSING, null);
            if (productStock != null && productStock.getInventoryResponse() != null
                    && productStock.getInventoryResponse().size() > 0) {
                Set<Integer> productIds = new HashSet<>(request.getProductIds());
                for (ProductStockForUnit stock : productStock.getInventoryResponse()) {
                    if (productIds.contains(stock.getProductId())) {
                        data.add(new ProductQuantityData(stock.getProductId(), stock.getExpectedValue(),
                                scmCache.getProductDefinitions().get(stock.getProductId()).getUnitOfMeasure()));
                    }
                }
            }
        } catch (Exception e) {
            LOG.error("Got exception while getting inventory list", e);
        }
        return new QuantityResponseData(request.getUnitId(), data, InventoryAction.REFRESH, InventorySource.SUMO, null,
                SCMUtil.getCurrentTimestamp());
    }

    @RequestMapping(method = RequestMethod.POST, value = "kettle-wastage-event", consumes = MediaType.APPLICATION_JSON)
    public List<WastageEvent> addWastageEventFromKettle(@RequestBody List<WastageEvent> wastageEvent)
            throws InventoryUpdateException, DataNotFoundException {
        return stockManagementService.addWastageEvent(wastageEvent);
    }

    @RequestMapping(method = RequestMethod.POST, value = "verify-price-data", consumes = MediaType.APPLICATION_JSON)
    public List<WastageEvent> verifyPriceData(@RequestBody List<WastageEvent> wastageEvent)
            throws InventoryUpdateException {
        return stockManagementService.verifyPriceData(wastageEvent);
    }

    @DayClosureCheck
    @RequestMapping(method = RequestMethod.POST, value = "wastage-event", consumes = MediaType.APPLICATION_JSON)
    public List<WastageEvent> addWastageEventForUnit(HttpServletRequest request,
                                                     @RequestBody List<WastageEvent> wastageEvent, @RequestParam(required = false) String isManual) throws InventoryUpdateException, DayCloseInitiatedException, DataNotFoundException, SumoException {
        if (verifyWastageEvent(wastageEvent)) {
            LOG.info("Is request From SUMO : {}",Objects.nonNull(isManual));
            return stockManagementService.addManualWastageEvent(wastageEvent, Objects.nonNull(isManual));
        } else {
            return wastageEvent;
        }
    }

    private boolean verifyWastageEvent(List<WastageEvent> wastageEvent) {
        boolean flag = true;
		/*for(WastageEvent event : wastageEvent){
			try{
				UnitCategory category = masterDataCache.getUnit(event.getUnitId()).getFamily();
				if(category.equals(UnitCategory.CAFE)){
					// fail the event if any of the verification fails
					flag = scmCache.addToUnitWiseWastageIdentity(event) != null;
				}
			}catch (Exception e){
				LOG.error("::::::::::::: Error while verifying wastage ::::::::::::",e);
			}
		}*/
        return flag;
    }

    @DayClosureCheck
    @RequestMapping(method = RequestMethod.POST, value = "cancel-wastage-event", consumes = MediaType.APPLICATION_JSON)
    public Boolean updateWastageEventForUnit(HttpServletRequest request, @RequestBody Integer wastageId)
            throws DayCloseInitiatedException {
        return stockManagementService.cancelWastageEvent(wastageId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "wastage-events")
    public List<WastageEvent> getWastageEventsForUnit(@RequestBody final UnitData unit) {
        int unitId = unit.getUnitId();
        Date businessDate = SCMUtil.getCurrentBusinessDate();
        LOG.info("Parameters being passed are ::: {} ::: {}", unitId, businessDate);
        return stockManagementService.getWastageEvents(unitId, businessDate);
    }

    @RequestMapping(method = RequestMethod.POST, value = "wastage-events-for-a-day")
    public List<WastageEvent> getWastageEventsForUnitForADay(@RequestBody final UnitData unit) {
        int unitId = unit.getUnitId();
        Date businessDate = AppUtils.getDate(unit.getBusinessDate());
        LOG.info("Parameters being passed are ::: {} ::: {}", unitId, businessDate);
        return stockManagementService.getWastageEvents(unitId, businessDate);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-wastage-events-for-Days")
    public List<WastageEvent> getWastageEventsForDates(@RequestParam String startDate, @RequestParam String endDate, @RequestParam Integer unitId) {
        LOG.info("Got Request to fetch wastage events from : {} to : {}", startDate, endDate);
        return stockManagementService.getWastageEventsForDates(startDate, endDate, unitId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "check-opening")
    public Boolean checkOpeningForUnit(@RequestParam int unitId) {
        LOG.info("checking if Opening needed for ", unitId);
        return stockManagementService.checkOpeningRequired(unitId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "closing-initiated")
    public DayCloseEvent checkStockClosing(@RequestBody UnitData unitData) {
        LOG.info("checking if closing initiated for ", unitData.getUnitId());
        return stockManagementService.checkClosingInitiated(unitData.getUnitId());
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-stocking-list")
    public View getConsumptionViewExcel(
            @RequestParam(required = false, defaultValue = "MONTHLY") StockTakeType frequency) {
        List<ProductDefinition> productList = stockManagementService.getAllProductsByFrequency(frequency);
        return excelViewGenerator.getStockView(frequency, productList);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-current-stock", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public UnitProductReferenceData getCurrentStock(@RequestBody UnitProductReferenceData data) {
        Map<Integer, BigDecimal> currentStock = stockManagementService.getCurrentStock(data.getUnitId());
        for (RequestScmItem item : data.getScmProductList()) {
            item.setStockAtHand(currentStock.containsKey(item.getId()) ? currentStock.get(item.getId()).floatValue()
                    : BigDecimal.ZERO.floatValue());
        }
        return data;
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-live-stock", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public UnitProductReferenceData getLiveStock(@RequestBody UnitProductReferenceData data) throws URISyntaxException {
        Map<Integer, BigDecimal> currentStock = stockManagementService.getScmProductInventory(data.getUnitId(), true, PriceUpdateEntryType.PRODUCT.name());
        for (RequestScmItem item : data.getScmProductList()) {
            item.setStockAtHand(currentStock.containsKey(item.getId()) ? currentStock.get(item.getId()).floatValue()
                    : BigDecimal.ZERO.floatValue());
        }
        return data;
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-current-in-transit-stock", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public UnitProductReferenceData getCurrentInTransitStock(@RequestBody UnitProductReferenceData data, @RequestParam Boolean isF9) throws URISyntaxException {
        Map<Integer, BigDecimal> currentStock = stockManagementService.getScmProductInventory(data.getUnitId(), true, PriceUpdateEntryType.PRODUCT.name());
        for (RequestScmItem item : data.getScmProductList()) {
            item.setStockAtHand(currentStock.containsKey(item.getId()) ? currentStock.get(item.getId()).floatValue() > 0
                    ? currentStock.get(item.getId()).floatValue()
                    : BigDecimal.ZERO.floatValue()
                    : BigDecimal.ZERO.floatValue());
        }
        Map<Integer, BigDecimal> currentInTransitStock = stockManagementService.getInTransitStock(data.getUnitId(), isF9);
        for (RequestScmItem item : data.getScmProductList()) {
            item.setInTransit(currentInTransitStock.containsKey(item.getId()) ? currentInTransitStock.get(item.getId()).floatValue() > 0
                    ? currentInTransitStock.get(item.getId()).floatValue()
                    : BigDecimal.ZERO.floatValue()
                    : BigDecimal.ZERO.floatValue());
        }
        return data;
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-day-wise-expiry-product")
    public DayWiseExpiryProduct getDayWiseExpiryProduct(@RequestParam Integer unitId, @RequestParam List<String> dates,
                                                        @RequestParam Boolean isAggregated, @RequestParam String firstOrderingDate) throws SumoException {
        Date firstDate = AppUtils.getDate(firstOrderingDate, "yyyy-MM-dd");
        return stockManagementService.getDayWiseExpiryProduct(unitId, dates, isAggregated, firstDate);
    }

    @RequestMapping(method = RequestMethod.POST, value = "check-inventory-loaded")
    public boolean checkInventoryUpdated(@RequestBody UnitData unit) {
        return stockManagementService.checkInventoryUpdated(unit.getUnitId(), SCMUtil.getCurrentBusinessDate());
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-expected-values")
    public StockInventoryData getPredictedValues(@RequestBody UnitData unit) {
        StockInventoryData productStock = null;
        try {
            productStock = stockManagementService.getExpectedValues(unit.getUnitId(), unit.getUserId(),
                    unit.getStockTakeType(), unit.getStockEventType(), unit.getBusinessDate());
            if (productStock != null && productStock.getInventoryResponse() != null
                    && productStock.getInventoryResponse().size() > 0) {
                return productStock;
            }
        } catch (Exception e) {
            LOG.error("Got exception while getting inventory list", e);
        }
        return productStock;
    }

    @RequestMapping(method = RequestMethod.POST, value = "update-inventory")
    public int updateInventoryForUnit(@RequestParam StockTakeType stockType, @RequestParam(required = false) String businessDate,
                                      @RequestBody StockInventoryData inventoryData)
            throws NegativeStockException, InventoryUpdateException, SumoException {
        Date currentBusinessDate = (Objects.nonNull(businessDate) && businessDate.compareTo("null") != 0) ? SCMUtil.getDate(SCMUtil.parseDate(businessDate))
                : SCMUtil.getCurrentBusinessDate();
        int result = stockManagementService.checkAndUpdateInventoryForUnit(inventoryData, stockType,
                currentBusinessDate, true);
        if (result == 404 || result == 405) {
            return result;
        }
        stockManagementService.settleDayCloseForUnit(scmCache.getUnitDetail(inventoryData.getUnit()),
                currentBusinessDate);
        return result;
    }

    @RequestMapping(method = RequestMethod.POST, value = "validate-stock-event")
    public StockCalendarEventCheckVO cheAvailableStockCalendarEvent(@RequestParam StockTakeType stockType,
                                                                    @RequestBody StockInventoryData inventoryData) {
        return stockManagementService.cheAvailableStockCalendarEvent(inventoryData, stockType, SCMUtil.getCurrentBusinessDate(), true);
    }

    @RequestMapping(method = RequestMethod.POST, value = "validate-stock-events")
    public List<StockCalendarEventCheckVO> cheAvailableStockCalendarEvent(@RequestBody StockInventoryData inventoryData,
                                                                          @RequestParam(required = false) String eventBusinessDate) {
        Date businessDate = Objects.nonNull(eventBusinessDate) ? SCMUtil.getDate(SCMUtil.parseDate(eventBusinessDate)) : SCMUtil.getCurrentBusinessDate();
        return stockManagementService.cheAvailableStockCalendarEvents(inventoryData, businessDate, true);
    }

    @RequestMapping(method = RequestMethod.POST, value = "pending-stock-event")
    public List<StockEventCalendarData> getPendingCalendarEventForUnit(@RequestBody Integer unitId) {
        return stockManagementService.getPendingCalendarEventForUnit(unitId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "planned-stock-event")
    public List<StockEventCalendarData> getPlannedCalendarEventByType(@RequestParam StockTakeType stockType,
                                                                      @RequestBody Integer unitId) {
        return stockManagementService.getPlannedCalendarEventByType(unitId, stockType);
    }

    @RequestMapping(method = RequestMethod.POST, value = "add-planned-stock-event")
    public StockEventCalendarData addPlannedCalendarEventByType(@RequestParam StockTakeType stockType,
                                                                @RequestBody Integer unitId, @RequestParam String businessDate) throws SumoException {
        List<StockEventCalendarData> events = stockManagementService.getPlannedCalendarEventByType(unitId, stockType);
        if (events != null && events.size() > 0) {
            throw new SumoException(String.format(
                    "There is already an event in pending state that exists for unit  %d amd stockType %s", unitId,
                    stockType));
        } else {
            return stockManagementService.addStockCalendarEvent(unitId, stockType,
                    AppUtils.getDate(businessDate, "yyyy-MM-dd"));
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = "update-stock-event-calendar")
    public boolean updateStockEventCalendar(@RequestBody List<StockEventCalendarData> request) throws SumoException {
        return stockManagementService.updateStockEventCalendar(request);
    }

    @RequestMapping(method = RequestMethod.GET, value = "settle-initiated-events")
    public void settleInitiatedEvents() {
        List<DayCloseEvent> dayCloseEventDataList = stockManagementService.getInitiatedStockEvents(null);
        if (dayCloseEventDataList != null && !dayCloseEventDataList.isEmpty()) {
            for (DayCloseEvent e : dayCloseEventDataList) {
                Integer unitId = e.getUnit().getId();
                String unitName = e.getUnit().getName();
                Date businessDate = e.getBusinessDate();
                try {
                    if (!e.getStockTakeType().equals(StockTakeType.FIXED_ASSETS)) {
                        stockManagementService.settleDayCloseForUnit(unitId, businessDate);
                    } else {
                        stockManagementService.settleFixedAssetStockForUnit(unitId, businessDate);
                    }
                } catch (Exception ex) {
                    stockManagementService.logVarianceError(unitId, unitName, ex);
                }
            }
        }
    }

    // Settle all the initiated events every five minutes everyday
    @Scheduled(cron = "0 0/5 * * * *", zone = "GMT+05:30")
    public void checkForSettledInventory() throws SumoException, InventoryUpdateException, NegativeStockException {
        settleInitiatedEvents();
    }

    // Settle all the remaining events at 6.15am everyday
    @Scheduled(cron = "0 00 06 * * *", zone = "GMT+05:30")
    public void settleDayClose() throws NegativeStockException, EmailGenerationException, InventoryUpdateException {
        Date businessDate = SCMUtil.getPreviousBusinessDate(SCMUtil.getCurrentBusinessDate());
        settleDayClose(businessDate);
    }

    //@Scheduled(cron = "0 0 08 * * *", zone = "GMT+05:30")
    public void updatePNLData() {
        updatePnL(null, StockTakeType.DAILY);
    }

    @RequestMapping(method = RequestMethod.POST, value = "calculate-pnl")
    public boolean updatePnLData(@RequestBody String date) {
        Date businessDate = date == null ? SCMUtil.getPreviousDate() : AppUtils.parseDate(date);
        updatePnL(businessDate, StockTakeType.DAILY);
        return true;
    }

    @RequestMapping(method = RequestMethod.POST, value = "calculate-pnl/finalized")
    public boolean updateFinalizedPnLData(@RequestBody String date) {
        updatePnL(AppUtils.parseDate(date), StockTakeType.MONTHLY);
        return true;
    }

    @RequestMapping(method = RequestMethod.POST, value = "calculate-pnl/monthly")
    public List<Integer> getUnitsWithMonthlyDone(@RequestBody String date) {
        LOG.info("Request to get Monthly Done for date {}", date);
        List<Integer> list = stockManagementService.getUnitsWithMonthlyDone(AppUtils.parseDate(date));
        LOG.info("Total units with monthly done {}", list.size());
        return list;
    }

    private void updatePnL(Date businessDate, StockTakeType type) {
        try {
            if (businessDate == null) {
                businessDate = AppUtils.getPreviousDate();
            }

            // get units with CalculationStatus.PENDING_SUMO_CALCULATION
            List<Integer> unitIds = getPnLunits(businessDate, type);
//			List<BudgetDetail> budgetList = new ArrayList<>();
            // calculate budget details
            for (Integer i : unitIds) {
                try {
                    LOG.info("Calculating Data for Unit {} and businessDate {}", i, businessDate);
                    long startTime = System.currentTimeMillis();

                    BudgetDetail budgetDetail = new BudgetDetail();
                    UnitDetail unitDetail = scmCache.getUnitDetail(i);
                    budgetDetail.setKey(createBudgetKey(unitDetail, businessDate, type));

                    // Waste Aggregate
                    stockManagementService.calculateWastageAggregate(budgetDetail, unitDetail, businessDate, type);
                    LOG.info("Wastage Aggregate calculated in {} milliseconds", System.currentTimeMillis() - startTime);
                    startTime = System.currentTimeMillis();
                    // Inventory Aggregate
                    stockManagementService.calculateInventoryAggregate(budgetDetail, unitDetail, businessDate, type);
                    LOG.info("Inventory Aggregate calculated in {} milliseconds", System.currentTimeMillis() - startTime);
                    startTime = System.currentTimeMillis();
                    // Consumables Aggregate
                    stockManagementService.calculateConsumablesAggregate(budgetDetail, unitDetail, businessDate, type);
                    LOG.info("Consumable Aggregate calculated in {} milliseconds", System.currentTimeMillis() - startTime);
                    // Service Aggregate
                    serviceOrderService.calculateServiceAggregate(budgetDetail, unitDetail, businessDate, type);
                    LOG.info("Service Aggregate calculated in {} milliseconds", System.currentTimeMillis() - startTime);

                    //Mohit - Removed this as we need to now calculate COGS tax, Fixed Asset Tax and Consumables Tax Separately
                    //applyTaxes(budgetDetail, type);
                    //budgetList.add(budgetDetail);
                    List<BudgetDetail> data = new ArrayList<>();
                    data.add(budgetDetail);
                    sendPnLDetails(data, getBudgetType(type));
                } catch (Exception e) {
                    LOG.error("Exception Caught while Calculating PnL for Sumo (unit id {})", i, e);
                }
            }
            // send to kettle
//			sendPnLDetails(budgetList, getBudgetType(type));
        } catch (Exception e) {
            LOG.error("Error while calculating PNL for businessDate {}", businessDate, e);
        }
    }

    private CalculationType getBudgetType(StockTakeType type) {
        if (StockTakeType.MONTHLY.equals(type)) {
            return CalculationType.FINALIZED;
        } else {
            return CalculationType.CURRENT;
        }
    }

    /**
     * @param b
     * @param type
     */
    @Deprecated
    private void applyTaxes(BudgetDetail b, StockTakeType type) {
        try {
            applyCOGSTax(b, type);
            applyFixedAssestTax(b, type);
            //applyConsumablesTax(b, type);
        } catch (Exception e) {
            LOG.error("Error while Applying Taxes in Budget Data for unit " + b.getKey().getUnitId(), e);
        }
    }

    /**
     * @param b
     * @param type
     */
    @Deprecated
    private void applyCOGSTax(BudgetDetail b, StockTakeType type) {
        BigDecimal totalTax = stockManagementService.getTaxAggregate(b.getKey().getUnitId(),
                b.getKey().getBusinessDate(),
                Arrays.asList(SCMServiceConstants.CATEGORY_SEMI_FINISHED, SCMServiceConstants.CATEGORY_COGS), type);
        BigDecimal aggregate = getAggregate(b);
        LOG.info("aggregate : {}", aggregate);
        applyTaxes(b, aggregate, totalTax);
    }

    /**
     * @param b
     * @param aggregate
     * @param totalTax
     */
    @Deprecated
    private void applyTaxes(BudgetDetail b, BigDecimal aggregate, BigDecimal totalTax) {
        applyTaxes(b.getInventory(), aggregate, totalTax);
        applyTaxes(b.getWastage(), aggregate, totalTax);
    }

    /**
     * @param type
     * @param
     */
    @Deprecated
    private void applyFixedAssestTax(BudgetDetail b, StockTakeType type) {

        BigDecimal faTax = stockManagementService.getFATaxAggregate(b.getKey().getUnitId(),
                b.getKey().getBusinessDate(), ThresholdType.BELOW, SCMServiceConstants.FA_PRICE_THRESHOLD, type);
        BigDecimal faCapexTax = stockManagementService.getFATaxAggregate(b.getKey().getUnitId(),
                b.getKey().getBusinessDate(), ThresholdType.ABOVE, SCMServiceConstants.FA_PRICE_THRESHOLD, type);
        b.getConsumables().setFixedAssetsTax(faTax);
        b.getConsumables().setFixedAssetsCapexTax(faCapexTax);

    }

    /**
     * @param b
     */
    @Deprecated
    private BigDecimal getAggregate(BudgetDetail b) {
        BigDecimal wastage = getWastageAggregate(b.getWastage());
        BigDecimal consumableAggregate = getInventoryAggregate(b.getInventory());
        LOG.info("wastage : {}, consumable : {}", wastage, consumableAggregate);
        return AppUtils.add(wastage, consumableAggregate);

    }

    /**
     * @param inventory
     * @return
     */
    private BigDecimal getInventoryAggregate(InventoryAggregate inventory) {
        return inventory == null ? BigDecimal.ZERO
                : AppUtils.add(inventory.getDeliveryCogs(), AppUtils.add(inventory.getDineInCogs(),
                AppUtils.add(inventory.getEmployeeMealCogs(), getValue(inventory.getStockVariance()))));
    }

    private BigDecimal getValue(BigDecimal stockVariance) {
        return stockVariance != null && stockVariance.intValue() >= 0 ? stockVariance : BigDecimal.ZERO;
    }

    /**
     * @param wastage
     * @return
     */

    public BigDecimal getWastageAggregate(WastageAggregate wastage) {
        return wastage == null ? BigDecimal.ZERO
                : AppUtils.add(wastage.getUnsatifiedCustomerCost(),
                AppUtils.add(wastage.getExpiryWastage(), AppUtils.add(wastage.getWastageOther(),
                        AppUtils.add(wastage.getMarketingAndSampling(), wastage.getTrainingCogs()))));
    }

    @Deprecated
    public void applyTaxes(WastageAggregate wastage, BigDecimal aggregate, BigDecimal tax) {
        if (wastage == null) {
            return;
        }
        wastage.setUnsatifiedCustomerCostTax(BigDecimal.ZERO.equals(aggregate) ? BigDecimal.ZERO
                : AppUtils.multiplyWithScale10(
                AppUtils.divideWithScale10(wastage.getUnsatifiedCustomerCost(), aggregate), tax));

        LOG.info("Unsatisfied {} , {}, {} - {}", aggregate, wastage.getUnsatifiedCustomerCost(), tax,
                wastage.getUnsatifiedCustomerCostTax());
        wastage.setExpiryWastageTax(BigDecimal.ZERO.equals(aggregate) ? BigDecimal.ZERO
                : AppUtils.multiplyWithScale10(AppUtils.divideWithScale10(wastage.getExpiryWastage(), aggregate), tax));
        LOG.info("Expiry Wastage tax {} , {}, {} - {}", aggregate, wastage.getExpiryWastage(), tax,
                wastage.getExpiryWastageTax());
        wastage.setWastageOtherTax(BigDecimal.ZERO.equals(aggregate) ? BigDecimal.ZERO
                : AppUtils.multiplyWithScale10(AppUtils.divideWithScale10(wastage.getWastageOther(), aggregate), tax));
        LOG.info("Wastage Other tax {} , {}, {} - {}", aggregate, wastage.getWastageOther(), tax,
                wastage.getWastageOtherTax());
        wastage.setMarketingAndSamplingTax(BigDecimal.ZERO.equals(aggregate) ? BigDecimal.ZERO
                : AppUtils.multiplyWithScale10(AppUtils.divideWithScale10(wastage.getMarketingAndSampling(), aggregate),
                tax));
        LOG.info("Marketing and Sampling tax {} , {}, {} - {}", aggregate, wastage.getMarketingAndSampling(), tax,
                wastage.getMarketingAndSamplingTax());
        wastage.setTrainingCogsTax(BigDecimal.ZERO.equals(aggregate) ? BigDecimal.ZERO
                : AppUtils.multiplyWithScale10(AppUtils.divideWithScale10(wastage.getTrainingCogs(), aggregate), tax));
        LOG.info("Training Cogs tax {} , {}, {} - {}", aggregate, wastage.getTrainingCogs(), tax,
                wastage.getTrainingCogsTax());

    }

    @Deprecated
    public void applyTaxes(InventoryAggregate inventory, BigDecimal aggregate, BigDecimal tax) {
        if (inventory == null) {
            return;
        }
        inventory.setDeliveryCogsTax(BigDecimal.ZERO.equals(aggregate) ? BigDecimal.ZERO
                : AppUtils.multiplyWithScale10(AppUtils.divideWithScale10(inventory.getDeliveryCogs(), aggregate),
                tax));
        LOG.info("DeliveryCogsTax {} , {}, {} - {}", aggregate, inventory.getDeliveryCogs(), tax,
                inventory.getDeliveryCogsTax());
        inventory.setDineInCogsTax(BigDecimal.ZERO.equals(aggregate) ? BigDecimal.ZERO
                : AppUtils.multiplyWithScale10(AppUtils.divideWithScale10(inventory.getDineInCogs(), aggregate), tax));
        LOG.info("DineInCogsTax {} , {}, {} - {}", aggregate, inventory.getDineInCogs(), tax,
                inventory.getDineInCogsTax());
        inventory.setEmployeeMealCogsTax(BigDecimal.ZERO.equals(aggregate) ? BigDecimal.ZERO
                : AppUtils.multiplyWithScale10(AppUtils.divideWithScale10(inventory.getEmployeeMealCogs(), aggregate),
                tax));
        LOG.info("EmployeeMealCogsTax {} , {}, {} - {}", aggregate, inventory.getEmployeeMealCogs(), tax,
                inventory.getEmployeeMealCogsTax());
        inventory.setStockVarianceTax(BigDecimal.ZERO.equals(aggregate) ? BigDecimal.ZERO
                : AppUtils.multiplyWithScale10(
                AppUtils.divideWithScale10(getValue(inventory.getStockVariance()), aggregate), tax));
        LOG.info("StockVarianceTax {} , {}, {} - {}", aggregate, inventory.getStockVariance(), tax,
                inventory.getStockVarianceTax());
        inventory.setZeroVarianceTax(BigDecimal.ZERO.equals(aggregate) ? BigDecimal.ZERO
                : AppUtils.multiplyWithScale10(
                AppUtils.divideWithScale10(getValue(inventory.getZeroVariance()), aggregate), tax));
        LOG.info("ZeroVarianceTax {} , {}, {} - {}", aggregate, inventory.getStockVariance(), tax,
                inventory.getStockVarianceTax());
        inventory.setVarianceYCTax(BigDecimal.ZERO.equals(aggregate) ? BigDecimal.ZERO
                : AppUtils.multiplyWithScale10(
                AppUtils.divideWithScale10(getValue(inventory.getVarianceYC()), aggregate), tax));
        LOG.info("VarianceYC {} , {}, {} - {}", aggregate, inventory.getStockVariance(), tax,
                inventory.getStockVarianceTax());
        inventory.setVariancePCCTax(BigDecimal.ZERO.equals(aggregate) ? BigDecimal.ZERO
                : AppUtils.multiplyWithScale10(
                AppUtils.divideWithScale10(getValue(inventory.getVariancePCC()), aggregate), tax));
        LOG.info("VariancePCCTax {} , {}, {} - {}", aggregate, inventory.getStockVariance(), tax,
                inventory.getStockVarianceTax());
        // need to distribute tax here

    }

    private void sendPnLDetails(List<BudgetDetail> budgetList, CalculationType type) {
        try {
            String endPoint = getPnLDataUpdateEndPoint();
            if (CalculationType.FINALIZED.equals(type)) {
                endPoint = getFinalizedPnLDataUpdateEndPoint();
            }
            WebServiceHelper.exchangeWithAuth(endPoint, props.getAuthToken(), HttpMethod.POST, Boolean.class,
                    JSONSerializer.toJSON(budgetList), null);
        } catch (URISyntaxException e) {
            LOG.error("Error in sending PnL to Kettle", e);
        }
    }

    private BudgetKey createBudgetKey(UnitDetail unit, Date businessDate, StockTakeType type) {
        BudgetKey key = new BudgetKey();
        key.setBusinessDate(businessDate);
        key.setUnitId(unit.getUnitId());
        key.setUnitName(unit.getUnitName());
        key.setDayClosureId(null);
        if (StockTakeType.MONTHLY.equals(type)) {
            key.setCalculation(CalculationType.FINALIZED.name());
        } else {
            key.setCalculation(CalculationType.CURRENT.name());
        }
        key.setYear(AppUtils.getYear(businessDate));
        key.setMonth(AppUtils.getMonth(businessDate));
        key.setDay(AppUtils.getDay(businessDate));
        return key;
    }

    @SuppressWarnings("unchecked")
    public List<Integer> getPnLunits(Date businessDate, StockTakeType type) {
        List<Integer> l = new ArrayList<>();
        try {
            String url = getPnLUnitSearchEndPoint();
            if (StockTakeType.MONTHLY.equals(type)) {
                url = getFinalizedPnLUnitSearchEndPoint();
            }
            Map<String, String> uriVariables = new HashMap<>();
            uriVariables.put("businessDate", new SimpleDateFormat("yyyy-MM-dd").format(businessDate));
            l = WebServiceHelper.exchangeWithAuth(url, props.getAuthToken(), HttpMethod.GET, List.class, null,
                    uriVariables);
        } catch (URISyntaxException e) {
            LOG.error("Error in get PnL units", e);
        }
        return new ArrayList<Integer>(l);
    }

    private String getPnLUnitSearchEndPoint() {
        return props.getKettleServiceBasePath() + KettleServiceClientEndpoints.GET_PNL_UNITS_BY_BUSINESS_DATE;
    }

    private String getFinalizedPnLUnitSearchEndPoint() {
        return props.getKettleServiceBasePath() + KettleServiceClientEndpoints.GET_PNL_UNITS_BY_BUSINESS_DATE_FINALIZED;
    }

    private String getPnLDataUpdateEndPoint() {
        return props.getKettleServiceBasePath() + KettleServiceClientEndpoints.PUT_PNL_DATA;
    }

    private String getFinalizedPnLDataUpdateEndPoint() {
        return props.getKettleServiceBasePath() + KettleServiceClientEndpoints.PUT_PNL_DATA_FINALIZED;
    }

    private void settleDayClose(Date businessDate) throws InventoryUpdateException, NegativeStockException {
        for (UnitDetail unit : metaService.getAllUnitDetails()) {
            stockManagementService.settleDayCloseForUnit(unit, businessDate);
        }
    }

    @RequestMapping(method = RequestMethod.GET, value = "settle-day-close")
    public void settleDayCloseForAll(@RequestParam(required = false) String date)
            throws NegativeStockException, InventoryUpdateException {
        settleDayClose(date != null ? SCMUtil.getDate(SCMUtil.parseDate(date)) : SCMUtil.getCurrentBusinessDate());
    }

    @RequestMapping(method = RequestMethod.GET, value = "send-variance-report-for-unit")
    public boolean settleDayCloseForUnit(@RequestParam Integer unit, @RequestParam String date,
                                         @RequestParam(required = false, defaultValue = "false") boolean override)
            throws NegativeStockException, InventoryUpdateException, SumoException, DataNotFoundException {
        return stockManagementService.settleDayCloseForUnit(unit, SCMUtil.getDate(SCMUtil.parseDate(date)));
    }

    @RequestMapping(method = RequestMethod.POST, value = "refresh-kettle-dayClose")
    public boolean refreshKettleDayClose(@RequestParam Integer unitId)
            throws DataNotFoundException, WebServiceCallException, DataUpdationException {
        return stockManagementService.refreshKettleDayClose(unitId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "wastage-entered-check")
    public Integer checkWastageEntered(@RequestParam Integer unit, @RequestParam(required = false) String date) {
        Date businessDate = date == null ? SCMUtil.getCurrentBusinessDate() : SCMUtil.parseDate(date);
        return stockManagementService.getWastageEvents(unit, businessDate).size();
    }

    @RequestMapping(method = RequestMethod.GET, value = "pending-gr-check")
    public HashMap<String, Integer> checkPendingReceiving(@RequestParam Integer unit,
                                                          @RequestParam(required = false) Integer daysInPast) {
        return goodsReceiveManagementService.getPendingGrsStatusWise(unit, daysInPast);
    }

    @RequestMapping(method = RequestMethod.GET, value = "pending-milk-bread-ros")
    public PendingMilkBread getPendingMilkBread(@RequestParam Integer unitId) {
        return stockManagementService.getPendingMilkBread(unitId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "pending-po-ro-so-check")
    public String checkPendingPoRoSo(@RequestParam String handOverData,
                                     @RequestParam(required = false) Integer unitId) {
        return goodsReceiveManagementService.getPendingPoSoRoStatus(handOverData, unitId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "stockTake")
    public StockTakeType[] getStockTakeType() {
        return StockTakeType.values();
    }

    @Scheduled(fixedRate = 300000)
    public void removeDuplicateKeyErrorWhKitchen() {
        if (props.removeDumplicateKeyError()) {
            try {
                LOG.info("Removing duplicate keys For Wh/kitchen");
                List<Integer> ids = stockManagementService.fetchDuplicateKey(false);
                if (ids != null && ids.size() > 0) {
                    stockManagementService.updateDuplicateKeyLatestFlag(ids,false);
                    SlackNotificationService.getInstance().sendNotification(props.getEnvType(), "SUMO",
                            SlackNotification.SUPPLY_CHAIN, "Number of Duplicate Keys Removed = " + ids.size());
                }
            } catch (Exception e) {
                LOG.error("Error in Removing duplicate keys For Wh/Kitchen", e);
            }
        }
    }

    @Scheduled(fixedRate = 300000)
    public void removeDuplicateKeyErrorCafe() {
        if (props.removeDumplicateKeyError()) {
            try {
                LOG.info("Removing duplicate keys For Cafe");
                List<Integer> ids = stockManagementService.fetchDuplicateKey(true);
                if (ids != null && ids.size() > 0) {
                    stockManagementService.updateDuplicateKeyLatestFlag(ids,true);
                    SlackNotificationService.getInstance().sendNotification(props.getEnvType(), "SUMO",
                            SlackNotification.SUPPLY_CHAIN, "Number of Duplicate Keys Removed = " + ids.size());
                }
            } catch (Exception e) {
                LOG.error("Error in Removing duplicate keys For Cafe", e);
            }
        }
    }

    @RequestMapping(method = RequestMethod.PUT, value = "remove-duplicate-keys")
    public Boolean removeDuplicateKey(@RequestParam("isCafe") Boolean isCafe) {
        try {
            LOG.info("Removing duplicate keys");
            List<Integer> ids = stockManagementService.fetchDuplicateKey(isCafe);
            if (ids != null && ids.size() > 0) {
                stockManagementService.updateDuplicateKeyLatestFlag(ids,isCafe);
                SlackNotificationService.getInstance().sendNotification(props.getEnvType(), "SUMO",
                        SlackNotification.SUPPLY_CHAIN, "Number of Duplicate Keys Removed = " + ids.size());
            }
            return true;
        } catch (Exception e) {
            LOG.error("Error in Removing duplicate keys", e);
            return false;
        }
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-scm-product-stock")
    public Map<Integer, BigDecimal> getScmProductInventory(@RequestParam Integer unitId, @RequestParam String keyType) throws URISyntaxException {
        return stockManagementService.getScmProductInventory(unitId, false, keyType);
    }

    @RequestMapping(method = RequestMethod.POST, value = "generate-wastage-sheet")
    public View generateWastageSheet(@RequestBody List<WastageData> wastageDataList, @RequestParam Integer unitId) {
        UnitBasicDetail unitBasicDetail = masterDataCache.getUnitBasicDetail(unitId);
        Boolean isKitchenOrWH = SCMUtil.isWareHouseOrKitchen(unitBasicDetail);
        List<WastageAggregatedData> wastageAggregatedData = stockManagementService.getWastageAggregatedData(wastageDataList, isKitchenOrWH);
        LOG.info("size of aggregated is : {}", wastageAggregatedData.size());
        return excelViewGenerator.generateWastageExcelSheet(wastageDataList, wastageAggregatedData, isKitchenOrWH);
    }

    @Scheduled(cron = "0 0 6 1 * ?", zone = "GMT+05:30")
    @RequestMapping(method = RequestMethod.POST, value = "insert-monthly-dump")
    public void insertMonthlyDumpData() {
        LOG.info("Started Inserting Monthly dump data at : {}", AppUtils.getCurrentTimestamp());
        stockManagementService.insertMonthlyDumpData();
    }

    @RequestMapping(method = RequestMethod.GET, value = "get/unit/dayClose", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public DayCloseRequestStatus getSumoDayCloseStatus(@RequestParam Integer unitId) {
        SCMDayCloseEventData scmDayCloseEventData = stockManagementService.getSumoDayCloseStatus(unitId, SCMUtil.getPreviousBusinessDate());
        return Objects.nonNull(scmDayCloseEventData) ? DayCloseRequestStatus.builder()
                .businessDate(scmDayCloseEventData.getBusinessDate())
                .unitId(scmDayCloseEventData.getUnitId())
                .dayCloseStatus(scmDayCloseEventData.getDayCloseEventType().equals(StockEventType.STOCK_TAKE.name()))
                .kettleStartOrderId(null)
                .kettleEndOrderId(null).build() : null;
    }

    @RequestMapping(method = RequestMethod.POST, value = "verify/dayCloses", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void verifyDayCloses(@RequestBody List<Integer> unitIds) {
        stockManagementService.verifyDayCloses(unitIds);
    }

    @RequestMapping(method = RequestMethod.GET, value = "fa-day-close")
    public FixedAssetDayCloseResponseObject checkFixedAssetDayClose(@RequestParam(value = "id") int unitId) throws DataUpdationException {
        return stockManagementService.getAssetsToDayCloseToday(unitId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "fetch-approvals")
    public Map<String,List<ApprovalDetail>> checkForAssetApprovals(@RequestParam(value = "id") int unitId) throws SumoException {
        return stockManagementService.checkForAssetApprovals(unitId);
    }
    @RequestMapping(method = RequestMethod.POST, value = "process-approval-request")
    public Boolean processApprovalRequest(@RequestParam(value = "requestId") Integer requestId,
                                          @RequestParam(value = "userId") Integer userId,
                                          @RequestParam(value = "res", required = false) Boolean response ) throws SumoException {
        return stockManagementService.processApprovalRequest(requestId,userId,response);
    }

    @GetMapping(value = "get-cafe-variance-edit-products", produces = MediaType.APPLICATION_JSON)
    public VarianceEdit getCafeVarianceEditProducts(@RequestParam Integer unitId) {
        return stockManagementService.getCafeVarianceEditProducts(unitId);
    }

    @PostMapping(value = "submit-cafe-variance-edit", produces = MediaType.APPLICATION_JSON)
    public VarianceEdit submitCafeVarianceEdit(@RequestBody VarianceEdit varianceEdit) throws SumoException {
        return stockManagementService.submitCafeVarianceEdit(varianceEdit);
    }


    @Scheduled(cron = "0 0 17 * * *", zone = "GMT+05:30")
    @PostMapping("validate-missing-prices")
    public void validateMissingPrices() throws DataNotFoundException, EmailGenerationException {
        stockManagementService.getMissingPrices();
    }

    @GetMapping("units-variance-acknowledge")
    public Set<Integer> getUnitsForVarianceAcknowledge(@RequestParam(value = "userId") Integer userId,@RequestParam(value = "acknowledgementType") String acknowledgementType) throws Exception{
        return stockManagementService.getUnitsForVarianceAcknowledge(userId,acknowledgementType);
    }

    @GetMapping(value = "acknowledge-variance-list")
    public List<VarianceAcknowledgementDetail> getAcknowledgeVarianceData(@RequestParam(value = "acknowledgementType") String acknowledgementType,
                                                                          @RequestParam(value = "unitId",required = false) Integer unitId,
                                                                          @RequestParam(value = "isAcknowledged",required = false) String isAcknowledged,
                                                                          @RequestParam(value = "userId") Integer userId) throws Exception {
        String isAck = null;
        if(Objects.nonNull(isAcknowledged) && isAcknowledged.equals("YES")){
            isAck = AppUtils.YES;
        }else if(Objects.nonNull(isAcknowledged) && isAcknowledged.equals("NO")){
            isAck = AppUtils.NO;
        }
        return stockManagementService.getAcknowledgeVarianceData(acknowledgementType, unitId,isAck,userId);
    }

    @PostMapping(value = "acknowledge-variance")
    public boolean approveVariance(@RequestParam(value = "id") Integer id,
                                   @RequestParam(value = "userId") Integer userId,
                                   @RequestParam(value = "comment",required = false) String comment) throws Exception {
        return stockManagementService.acknowledgeVariance(id,userId,comment);
    }

    @GetMapping(value = "get-variance-details")
    public Map<String,List<VarianceModal>> getVarianceDetails(@RequestParam(value = "unitId") Integer unitId,
                                                  @RequestParam(value = "businessDate",required = false) String businessDate,
                                                  @RequestParam(value = "varianceType",required = false) String varianceType,
                                                  @RequestParam(value = "acknowledgementType",required = false) String acknowledgementType,
                                                  @RequestParam(value = "scmDayCloseEventId",required = false) Integer scmDayCloseEventId) throws Exception {
        if(Objects.nonNull(acknowledgementType) && acknowledgementType.equals(StockTakeType.DAILY.name())){
            List<VarianceModal> varianceModalList = stockManagementService.getVarianceDetails(unitId,SCMUtil.getDate(businessDate,"yyyy-MM-dd"),varianceType,scmDayCloseEventId);
            Map<String,List<VarianceModal>> varianceDetails = new HashMap<>();
            if(Objects.nonNull(varianceModalList)){
                varianceDetails.put(businessDate,varianceModalList);
                return varianceDetails;
            }
        }else if (Objects.nonNull(acknowledgementType) && acknowledgementType.equals(StockTakeType.WEEKLY.name())){
            Map<String,List<VarianceModal>> varianceDetails = new HashMap<>();
            for (int i = 1; i < 8; i++) {
                Date date = SCMUtil.getOldDate(SCMUtil.getDate(businessDate, "yyyy-MM-dd"), i);
                List<VarianceAcknowledgementDetail> vadl = stockManagementService.getAcknowledgeVarianceData("DAILY", unitId, date);
                if (!vadl.isEmpty()) {
                    VarianceAcknowledgementDetail vad = vadl.get(0);
                    List<VarianceModal> varianceModalList = stockManagementService.getVarianceDetails(unitId, vad.getBusinessDate(), vad.getFrequency(), vad.getScmDayCloseEventId());
                    if (Objects.nonNull(varianceModalList)) {
                        varianceDetails.put(SCMUtil.formatDate(vad.getBusinessDate(),"yyyy-MM-dd"),varianceModalList);
                    }
                }
            }
            return varianceDetails;
        }
        return null;
    }

    @Scheduled(cron = "0 15 5 * * MON", zone = "GMT+05:30")
    public void generateWeeklyVarianceAcknowledgementData() throws Exception {
        stockManagementService.setWeeklyVarianceAcknowledgementData();
    }

    @GetMapping(value = "check-variance-acknowledgement")
    public VarianceAcknowledgementCheck checkVarianceAcknowledgement(@RequestParam(value = "id") Integer unitId){
        return stockManagementService.checkVarianceAcknowledgement(unitId);
    }

}
