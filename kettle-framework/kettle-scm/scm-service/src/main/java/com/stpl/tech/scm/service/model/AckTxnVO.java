package com.stpl.tech.scm.service.model;

import java.util.List;

import com.stpl.tech.scm.domain.model.DayCloseEventLogType;
import com.stpl.tech.scm.domain.model.DayCloseTxnItem;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 15-06-2017.
 */
public class AckTxnVO {

    private int id;
    private Integer unitId;
    private DayCloseEventLogType type;
    private int event;
    private List<DayCloseTxnItem> items;
    private List<Integer> eventIds;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public DayCloseEventLogType getType() {
        return type;
    }

    public void setType(DayCloseEventLogType type) {
        this.type = type;
    }

    public int getEvent() {
        return event;
    }

    public void setEvent(int event) {
        this.event = event;
    }

    public List<DayCloseTxnItem> getItems() {
        return items;
    }

    public void setItems(List<DayCloseTxnItem> items) {
        this.items = items;
    }

	public Integer getUnitId() {
		return unitId;
	}

	public void setUnitId(Integer unitId) {
		this.unitId = unitId;
	}



    public List<Integer> getEventIds() {
        return eventIds;
    }

    public void setEventIds(List<Integer> eventIds) {
        this.eventIds = eventIds;
    }
}
