package com.stpl.tech.scm.service.controller;

import com.stpl.tech.master.core.exception.InvalidRequestException;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.master.core.service.model.ErrorInfo;
import com.stpl.tech.scm.core.exception.DayCloseInitiatedException;
import com.stpl.tech.scm.core.exception.GatepassException;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.exception.NegativeStockException;
import com.stpl.tech.scm.core.exception.SCMError;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.exception.TransferOrderCreationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.servlet.http.HttpServletRequest;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 28-06-2017.
 */
public class AbstractSCMResources extends AbstractResources{
    private static final Logger LOG = LoggerFactory.getLogger(AbstractSCMResources.class);

    @ResponseStatus(HttpStatus.CONFLICT)
    @ExceptionHandler(DayCloseInitiatedException.class)
    @ResponseBody
    public ErrorInfo handleDayCloseException(DayCloseInitiatedException ex) {
        LOG.error(HttpStatus.CONFLICT.name(), ex);
        return new ErrorInfo(HttpStatus.CONFLICT.name(), ex);
    }

    @ResponseStatus(HttpStatus.NOT_ACCEPTABLE)
    @ExceptionHandler(InventoryUpdateException.class)
    @ResponseBody
    public SCMError handleInventoryException(InventoryUpdateException ex) {
        LOG.error(HttpStatus.INTERNAL_SERVER_ERROR.name(), ex);
        return ex.getCode();
    }

    @ResponseStatus(HttpStatus.NOT_ACCEPTABLE)
    @ExceptionHandler(NegativeStockException.class)
    @ResponseBody
    public ErrorInfo handleNegativeInventory(NegativeStockException ex) {
        LOG.error(HttpStatus.INTERNAL_SERVER_ERROR.name(), ex);
        ErrorInfo e = new ErrorInfo(ex.getTitle(), ex.getErrorMessage());
        e.setPayload(ex.getNegatives());
        return e;
    }


    @ResponseStatus(HttpStatus.FORBIDDEN)
    @ExceptionHandler(GatepassException.class)
    @ResponseBody
    public SCMError handleGatepassException(GatepassException ex) {
        LOG.error(HttpStatus.FORBIDDEN.name(), ex);
        return ex.getCode();
    }


    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    @ExceptionHandler(TransferOrderCreationException.class)
    @ResponseBody
    public SCMError handleTransferOrderException(HttpServletRequest req, TransferOrderCreationException ex) {
        LOG.error(HttpStatus.INTERNAL_SERVER_ERROR.name(), ex);
        return ex.getCode();
    }

    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    @ExceptionHandler(SumoException.class)
    @ResponseBody
    public SCMError handleSumoException(HttpServletRequest req, SumoException ex) {
        LOG.error(HttpStatus.INTERNAL_SERVER_ERROR.name(), ex);
        return ex.getCode();
    }

    @ResponseStatus(HttpStatus.NOT_ACCEPTABLE)
    @ExceptionHandler(InvalidRequestException.class)
    @ResponseBody
    public SCMError handleInvalidRequestException(HttpServletRequest req, InvalidRequestException ex) {
        LOG.error(HttpStatus.NOT_ACCEPTABLE.name(), ex);
        return new SCMError("Invalid Request", "Request authentication failed due to invalid token", 108);
    }
}
