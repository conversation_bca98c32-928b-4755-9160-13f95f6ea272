package com.stpl.tech.scm.service.controller;

import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.exception.VendorRegistrationException;
import com.stpl.tech.scm.core.service.SkuMappingService;
import com.stpl.tech.scm.core.util.SCMConstants;
import com.stpl.tech.scm.data.transport.model.VendorOTPValidationDomain;
import com.stpl.tech.scm.domain.model.DocumentDetail;
import com.stpl.tech.scm.domain.model.IdCodeName;
import com.stpl.tech.scm.domain.model.PriceStatus;
import com.stpl.tech.scm.domain.model.SkuPriceDetail;
import com.stpl.tech.scm.domain.model.SkuPriceUpdate;
import com.stpl.tech.scm.domain.model.SkuPriceUpdateDetail;
import com.stpl.tech.scm.domain.model.VendorContract;
import com.stpl.tech.scm.domain.model.VendorContractVO;
import com.stpl.tech.scm.service.model.VendorLookupData;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.TemplateRenderingException;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Created by Mohit
 */
@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
        + SCMServiceConstants.VENDOR_CONTRACT_MANAGEMENT_ROOT_CONTEXT)
@Log4j2
public class VendorContractManagementResource extends AbstractSCMResources{

    @Autowired
    private SkuMappingService mappingService;

    @Autowired
    private SCMCache scmCache;

    @Autowired
    private MasterDataCache masterCache;
    @Autowired
    private SCMConstants SCMConstants;


    @PostMapping("/vendor/get-request-price")
    public List<SkuPriceDetail> getVendorPriceChange(@RequestBody final VendorLookupData data) {
        return mappingService.getVendorPriceChange(data.getVendorId(), data.getLocation());
    }

    @RequestMapping(method = RequestMethod.POST, value = "generate/sku-price-request", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public boolean generateSkuPriceUpdateRequest(@RequestBody final List<SkuPriceUpdate> data) throws SumoException {
        return mappingService.generateSkuPriceUpdateRequest(data);
    }

    @RequestMapping(method = RequestMethod.POST, value = "vendor/process-price-request", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public boolean processPriceRequestForVendor(@RequestBody final SkuPriceUpdateDetail data) {
        return mappingService.processPriceRequestForVendor(data);
    }
    @GetMapping("/vendor/preview-request-price")
    public  List<SkuPriceDetail> previewVendorPriceChange(@RequestParam Integer vendorId) {
        return mappingService.previewVendorPriceChange(vendorId);
    }

    @PostMapping("/vendor/approve-request-price")
    public  boolean saveVendorPriceChange(@RequestBody VendorContract vendorContract) throws SumoException {
        return mappingService.saveVendorPriceChange(vendorContract);

    }
    @GetMapping("/vendor/get-contract")
    public List<VendorContractVO>  getVendorContract(@RequestParam(required = false) Integer vendorId,
                                                     @RequestParam PriceStatus status,
                                                     @RequestParam(required = false) String startDate,
                                                     @RequestParam(required = false) String  endDate,
                                                     @RequestParam(required = false) Integer vendorContractId) {
        return mappingService.getVendorContract(vendorId,status, AppUtils.getDate(startDate,AppUtils.DATE_FORMAT_STRING),
                AppUtils.getDate(endDate,AppUtils.DATE_FORMAT_STRING),vendorContractId);
    }

    @PostMapping("/vendor/cancel-contract")
    public boolean cancelVendorContract(@RequestBody VendorContract vendorContract) throws SumoException {
        return mappingService.cancelVendorContract(vendorContract);
    }

    @GetMapping("/vendor/get-template")
    public Collection<IdCodeName> getVendorContractTemplate() {
        return SCMConstants.getVendorContractTemplate().values();
    }

    @PostMapping("/vendor/generate-contract")
    public DocumentDetail generateContractUsingTemplate(@RequestParam Integer vendorContractId,
                                                        @RequestParam Integer empCode,
                                                        @RequestParam(required = false) Integer templateId) throws SumoException, TemplateRenderingException {
        return mappingService.generateContractUsingTemplate(vendorContractId,empCode,templateId, null);
    }

    @PostMapping("/vendor/map-contract-with-document")
    public boolean mapVendorContractWithDocument(@RequestBody VendorContract vendorContract) throws SumoException {
        return mappingService.mapVendorContractWithDocument(vendorContract);
    }

    @GetMapping("/vendor/get-contract-document")
    public String getContractDocument(@RequestParam Integer documentId) throws SumoException {
        return mappingService.getContractDocument(documentId);
    }

    @PostMapping("/vendor/trigger-mail")
    public boolean triggerVendorContractMail(@RequestBody VendorContract vendorContract) throws SumoException, EmailGenerationException {
        return mappingService.triggerVendorContractMail(vendorContract);
    }

    @PostMapping("/vendor/acceptance")
    public boolean vendorAcceptance(@RequestBody VendorContract vendorContract) throws SumoException, TemplateRenderingException, EmailGenerationException, VendorRegistrationException {
        return mappingService.vendorAcceptance(vendorContract);
    }

    @PostMapping("/vendor/apply-contract")
    public boolean contractApplied(@RequestBody VendorContract vendorContract) throws SumoException, TemplateRenderingException, EmailGenerationException, VendorRegistrationException {
        return mappingService.contractApplied(vendorContract);
    }

    @Scheduled(cron = "0 0 0 * * *", zone = "GMT+05:30")
    @PostMapping("/vendor/run-apply-contract-cron")
    public void applyContract() {
        mappingService.applyContract();
    }

    @Scheduled(cron = "0 55 23 * * *", zone = "GMT+05:30")
    @PostMapping("/vendor/run-expire-contract-cron")
    public void expiryContract() {
        mappingService.expiryContract();
    }

    @PostMapping("/vendor/trigger-otp")
    public boolean triggerEmailOtpForVendor(@RequestParam(required = false) Integer vendorId,
                                            @RequestParam(required = false) Integer employeeId) throws SumoException {
        if (Objects.nonNull(vendorId)){
            return mappingService.triggerEmailOtpForVendor(vendorId);
        } else {
            return mappingService.triggerEmailOtpForEmployee(employeeId);
        }
    }

    @PostMapping("/vendor/validate-otp")
    public boolean validateVendorOtp(@RequestBody VendorOTPValidationDomain otp) throws SumoException {
        return mappingService.validateVendorOtp(otp);
    }

    @RequestMapping(method = RequestMethod.POST, value = "validate", produces = MediaType.APPLICATION_JSON)
    public VendorContractVO  validateContractRequest(@RequestBody final Map<String,String> tokenMap)
            throws VendorRegistrationException, UnsupportedEncodingException, SumoException {
        return mappingService.validateRequest(tokenMap.get("token"));
    }

    @RequestMapping(method = RequestMethod.POST, value = "vendor/save-digital-signature", consumes = MediaType.MULTIPART_FORM_DATA)
    public DocumentDetail saveDigitalSignature(HttpServletRequest request,
                                                              @RequestParam(value = "file") final MultipartFile file,@RequestParam final Integer vendorContractId
    ) throws IOException, SumoException {
        return mappingService.saveDigitalSignature(file,vendorContractId);
    }
}
