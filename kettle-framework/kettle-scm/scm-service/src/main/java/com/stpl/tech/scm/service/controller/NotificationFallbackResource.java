package com.stpl.tech.scm.service.controller;

import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.ws.rs.core.MediaType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.service.NotificationFallbackService;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.domain.model.NotificationType;

/**
 * Created by Chaayos on 22-09-2016.
 */
@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
    + SCMServiceConstants.NOTIFICATION_FALLBACK_ROOT_CONTEXT)
public class NotificationFallbackResource extends AbstractResources{

    @Autowired
    private NotificationFallbackService notificationFallbackService;

    @RequestMapping(method = RequestMethod.POST, value = "vendor-request-notification-email-milk", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    public boolean sendVendorROEmailNotificationMilk(@RequestBody final Map requestObj) {
        List<Integer> vendorIds = (List)requestObj.get("vendorIds");
        Date fulfillmentDate = SCMUtil.parseDate((String)requestObj.get("fulfillmentDate"));
        notificationFallbackService.sendVendorRONotification(vendorIds, fulfillmentDate, true, NotificationType.EMAIL);
        return true;
    }

    @RequestMapping(method = RequestMethod.POST, value = "vendor-request-notification-email-breads", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    public boolean sendVendorROEmailNotificationBreads(@RequestBody final Map requestObj) {
        List<Integer> vendorIds = (List)requestObj.get("vendorIds");
        Date fulfillmentDate = SCMUtil.parseDate((String)requestObj.get("fulfillmentDate"));
        notificationFallbackService.sendVendorRONotification(vendorIds, fulfillmentDate, false, NotificationType.EMAIL);
        return true;
    }

    @RequestMapping(method = RequestMethod.POST, value = "vendor-request-notification-sms-milk", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    public boolean sendVendorROSMSNotificationMilk(@RequestBody final Map requestObj) {
        List<Integer> vendorIds = (List)requestObj.get("vendorIds");
        Date fulfillmentDate = SCMUtil.parseDate((String)requestObj.get("fulfillmentDate"));
        notificationFallbackService.sendVendorRONotification(vendorIds, fulfillmentDate, true, NotificationType.SMS);
        return true;
    }

    @RequestMapping(method = RequestMethod.POST, value = "vendor-request-notification-sms-breads", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    public boolean sendVendorROSMSNotificationBreads(@RequestBody final Map requestObj) {
        List<Integer> vendorIds = (List)requestObj.get("vendorIds");
        Date fulfillmentDate = SCMUtil.parseDate((String)requestObj.get("fulfillmentDate"));
        notificationFallbackService.sendVendorRONotification(vendorIds, fulfillmentDate, false, NotificationType.SMS);
        return true;
    }

    @RequestMapping(method = RequestMethod.POST, value = "vendor-receiving-notification-email-milk", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    public boolean sendVendorGREmailNotificationMilk(@RequestBody final Map requestObj) {
        List<Integer> vendorIds = (List)requestObj.get("vendorIds");
        Date fulfillmentDate = SCMUtil.parseDate((String)requestObj.get("fulfillmentDate"));
        notificationFallbackService.sendVendorGRNotification(vendorIds, fulfillmentDate, true, NotificationType.EMAIL);
        return true;
    }

    @RequestMapping(method = RequestMethod.POST, value = "vendor-receiving-notification-email-breads", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    public boolean sendVendorGREmailNotificationBreads(@RequestBody final Map requestObj) {
        List<Integer> vendorIds = (List)requestObj.get("vendorIds");
        Date fulfillmentDate = SCMUtil.parseDate((String)requestObj.get("fulfillmentDate"));
        notificationFallbackService.sendVendorGRNotification(vendorIds, fulfillmentDate, false, NotificationType.EMAIL);
        return true;
    }

    @RequestMapping(method = RequestMethod.POST, value = "vendor-receiving-notification-sms-milk", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    public boolean sendVendorGRSMSNotificationMilk(@RequestBody final Map requestObj) {
        List<Integer> vendorIds = (List)requestObj.get("vendorIds");
        Date fulfillmentDate = SCMUtil.parseDate((String)requestObj.get("fulfillmentDate"));
        notificationFallbackService.sendVendorGRNotification(vendorIds, fulfillmentDate, true, NotificationType.SMS);
        return true;
    }

    @RequestMapping(method = RequestMethod.POST, value = "vendor-receiving-notification-sms-breads", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    public boolean sendVendorGRSMSNotificationBreads(@RequestBody final Map requestObj) {
        List<Integer> vendorIds = (List)requestObj.get("vendorIds");
        Date fulfillmentDate = SCMUtil.parseDate((String)requestObj.get("fulfillmentDate"));
        notificationFallbackService.sendVendorGRNotification(vendorIds, fulfillmentDate, false, NotificationType.SMS);
        return true;
    }
}
