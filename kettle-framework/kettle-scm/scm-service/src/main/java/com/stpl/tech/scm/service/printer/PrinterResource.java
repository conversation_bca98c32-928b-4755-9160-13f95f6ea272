package com.stpl.tech.scm.service.printer;


import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import com.stpl.tech.kettle.core.service.util.MessageSigner;
import com.stpl.tech.scm.core.SCMServiceConstants;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

/**
 * Root resource (exposed at "pos-metadata" path)
 */
@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR + SCMServiceConstants.PRINTER_ROOT_CONTEXT)
public class PrinterResource {

    @RequestMapping(method = RequestMethod.POST, value = "secure/url/for")
    public String authorizePrinting(String request) {
        try {
            return MessageSigner.getInstance().sign(request);
        } catch (Exception e) {
            return null;
        }
    }

}
