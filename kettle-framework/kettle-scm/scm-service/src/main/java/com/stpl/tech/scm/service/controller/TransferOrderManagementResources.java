package com.stpl.tech.scm.service.controller;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.DayCloseInitiatedException;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.exception.TransferOrderCreationException;
import com.stpl.tech.scm.core.service.FetchTransferOrderService;
import com.stpl.tech.scm.core.service.GoodsReceiveManagementService;
import com.stpl.tech.scm.core.service.RequestOrderManagementService;
import com.stpl.tech.scm.core.service.TransferOrderManagementService;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.data.dao.RequestOrderManagementDao;
import com.stpl.tech.scm.data.dao.TransferOrderManagementDao;
import com.stpl.tech.scm.data.dao.impl.MutexFactory;
import com.stpl.tech.scm.data.model.RequestOrderData;
import com.stpl.tech.scm.data.model.TransferOrderData;
import com.stpl.tech.scm.data.model.TransferOrderEInvoice;
import com.stpl.tech.scm.domain.model.*;
import com.stpl.tech.scm.service.annotation.DayClosureCheck;
import com.stpl.tech.scm.service.controller.view.ExcelViewGenerator;
import com.stpl.tech.scm.service.model.ClubbedTODetail;
import com.stpl.tech.scm.service.model.SkuToProductsRequestData;
import com.stpl.tech.scm.service.model.UnitData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
        + SCMServiceConstants.TRANSFER_ORDER_MANAGEMENT_ROOT_CONTEXT)
public class TransferOrderManagementResources extends AbstractSCMResources {
    private static final Logger LOG = LoggerFactory.getLogger(TransferOrderManagementResources.class);

    @Autowired
    private TransferOrderManagementService transferOrderManagementService;

    @Autowired
    private FetchTransferOrderService fetchTransferOrderService;

    @Autowired
    private RequestOrderManagementService requestOrderManagementService;

    @Autowired
    private TransferOrderManagementDao transferOrderManagementDao;

    @Autowired
    private GoodsReceiveManagementService goodsReceiveManagementService;

    @Autowired
    private RequestOrderManagementDao requestOrderManagementDao;

    @Autowired
    SCMCache scmCache;

    @Autowired
    MasterDataCache masterCache;

    @Autowired
    private ExcelViewGenerator excelViewGenerator;

    @Autowired
    private MutexFactory<String> factory;



    @DayClosureCheck
    @RequestMapping(method = RequestMethod.POST, value = "transfer-order", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public Integer createTransferOrder(HttpServletRequest request, @RequestBody final TransferOrder transferOrder)
			throws TransferOrderCreationException, InventoryUpdateException, SumoException, DayCloseInitiatedException,
			DataNotFoundException {
		LOG.info("Initialize Mutex TO Creation for Generation Unit {}, For Unit {} and RO ID {}",
				transferOrder.getGeneratedForUnitId().getName(), transferOrder.getGeneratedForUnitId().getName(),
				transferOrder.getRequestOrderId());
		   long mutextWaitTime =  System.currentTimeMillis();
	       long totalTimeStart = System.currentTimeMillis();
	       Integer id = transferOrderManagementService.checkIfAlreadyCreated(transferOrder.getRequestOrderId());
            if (id == null) {
        		LOG.info("Before Mutex TO Creation for Generation Unit {}, For Unit {} and RO ID {}, TO_CREATION_TIME_CALCULATION_LOCK_START_TIME = {}",
        				transferOrder.getGeneratedForUnitId().getName(), transferOrder.getGeneratedForUnitId().getName(),
        				transferOrder.getRequestOrderId(), System.currentTimeMillis() - mutextWaitTime);
                synchronized (factory.getMutex(transferOrder.getGenerationUnitId().getId() + "_TO_ID")) {
            		LOG.info("After Mutex TO Creation for Generation Unit {}, For Unit {} and RO ID {}, TO_CREATION_TIME_CALCULATION_LOCK_WAIT_TIME = {}",
            				transferOrder.getGeneratedForUnitId().getName(), transferOrder.getGeneratedForUnitId().getName(),
            				transferOrder.getRequestOrderId(), System.currentTimeMillis() - mutextWaitTime);
                    id = transferOrderManagementService.checkIfAlreadyCreated(transferOrder.getRequestOrderId());
                    if( id == null) {
                    	 long toActualStartTime = System.currentTimeMillis();
                            boolean isDifferentCompany = false;
                            if (transferOrder.getRequestOrderId() != null) {
                                isDifferentCompany = requestOrderManagementService
                                    .isDifferentCompany(transferOrder.getRequestOrderId());
                            } else if (transferOrder.getRequestOrderId() == null) {
                                int sourceCompanyId = masterCache.getUnit(transferOrder.getGenerationUnitId().getId()).getCompany()
                                    .getId();
                                int receivingCompanyId = masterCache.getUnit(transferOrder.getGeneratedForUnitId().getId()).getCompany()
                                    .getId();
                                isDifferentCompany = sourceCompanyId != receivingCompanyId;
                            }
                            String invoiceId = transferOrderManagementService.getInvoiceId(transferOrder.getGenerationUnitId().getId(),
                                transferOrder.getGeneratedForUnitId().getId(), isDifferentCompany);
                            id = transferOrderManagementService.createTransferOrder(transferOrder, false, invoiceId);
                    		LOG.info("Completed Mutex TO Creation for Generation Unit {}, For Unit {} and RO ID {}, TO_CREATION_TIME_CALCULATION_ONLY_TO_TIME",
                    				transferOrder.getGeneratedForUnitId().getName(), transferOrder.getGeneratedForUnitId().getName(),
                    				transferOrder.getRequestOrderId(), System.currentTimeMillis() - toActualStartTime);
                            LOG.info("Transfer-order Actual Time consumed for request Order id {} is {} ms ",
									transferOrder.getRequestOrderId(), System.currentTimeMillis() - toActualStartTime);
	                    }else{
                        transferOrderManagementService.updateRequestOrder(transferOrder.getRequestOrderId());
                        LOG.info("Request# {} found in Acknowledged state while Transfer# {} Created",
                            transferOrder.getRequestOrderId(), id);
                    }
                }
            }else{
                transferOrderManagementService.updateRequestOrder(transferOrder.getRequestOrderId());
                LOG.info("Request# {} found in Acknowledged state while Transfer# {} Created",
                    transferOrder.getRequestOrderId(), id);
            }
        long totalTimeend = System.currentTimeMillis();
        LOG.info("Transfer-order API Time consumed for request Order id {} is {} ms ",transferOrder.getRequestOrderId(),totalTimeend -totalTimeStart);
        return id;
	}

    @DayClosureCheck
    @Transactional(rollbackFor = Exception.class, value = "SCMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @RequestMapping(method = RequestMethod.POST, value = "transfer-order-gr", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public Integer createTransferOrderAndGr(HttpServletRequest request, @RequestBody final TransferOrder transferOrder)
            throws TransferOrderCreationException, InventoryUpdateException, SumoException, DayCloseInitiatedException,
            DataNotFoundException, ParseException {
        Integer id =createTransferOrder(request,transferOrder);
        transferOrderManagementService.autoGrSpecializedOrder(id,transferOrder);
        return id;
    }

    @RequestMapping(method = RequestMethod.POST, value = "transfer-order-expiry-check", produces = MediaType.APPLICATION_JSON)
    public ExpiryDataRequest getTransferOrderItemExpiries(@RequestBody final ExpiryDataRequest transferOrder)
            throws TransferOrderCreationException, InventoryUpdateException, SumoException, DataNotFoundException {
        return transferOrderManagementService.getTransferOrderItemExpiries(transferOrder);
    }


    @DayClosureCheck
    @RequestMapping(method = RequestMethod.POST, value = "external-transfer-order", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public Integer createExternalTransfer(HttpServletRequest request, @RequestBody final TransferOrder transferOrder)
            throws TransferOrderCreationException, InventoryUpdateException, SumoException, DayCloseInitiatedException, DataNotFoundException {
        return transferOrderManagementService.createExternalTransferOrder(transferOrder, null);
    }

    @RequestMapping(method = RequestMethod.POST, value = "approve-external-transfer", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public boolean approveExternalTransfer(@RequestBody final Map<String, Integer> reqObj)
            throws DataNotFoundException, DataUpdationException {
        return transferOrderManagementService.approveExternalTransferOrder(reqObj.get("id"), reqObj.get("userId"));
    }

	@DayClosureCheck
	@RequestMapping(method = RequestMethod.POST, value = "clubbed-transfer-order", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public Integer createClubbedTransferOrder(HttpServletRequest request,
			@RequestBody final ClubbedTODetail clubbedTODetail) throws TransferOrderCreationException,
			DayCloseInitiatedException, InventoryUpdateException, SumoException, DataNotFoundException {
            boolean flag = transferOrderManagementService.getAlreadyTransferredRO(clubbedTODetail.getClubRoIds());
            if(!flag) {
                synchronized (factory.getMutex(clubbedTODetail.getTransferOrder().getGenerationUnitId().getId() + "_TO_ID")) {
                    flag = transferOrderManagementService.getAlreadyTransferredRO(clubbedTODetail.getClubRoIds());
                    if (!flag) {
                            String invoiceId = transferOrderManagementService.getInvoiceId(
                                clubbedTODetail.getTransferOrder().getGenerationUnitId().getId(),
                                clubbedTODetail.getTransferOrder().getGeneratedForUnitId().getId(),
                                clubbedTODetail.getTransferOrder().getSourceCompany().getId() != clubbedTODetail.getTransferOrder()
                                    .getReceivingCompany().getId());
                            return transferOrderManagementService.createClubbedTransferOrder(clubbedTODetail.getClubRoIds(),
                                clubbedTODetail.getTransferOrder(), clubbedTODetail.getFulfilmentDate(),
                                clubbedTODetail.getFulfillmentCompany(), clubbedTODetail.getRequestCompany(), invoiceId);
                    }else{
                        throw new SumoException("Error while Creating TO Found Already Created Request Order Please Club Again!");
                    }
                }
            }else{
                throw new SumoException("Error while Creating TO Found Already Created Request Order Please Club Again!");
            }
	    }

		@DayClosureCheck
		@RequestMapping(method = RequestMethod.POST, value = "bulk-transfer-order", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
		public BulkTOResponse createBulkTransferOrder(HttpServletRequest request,
                                                      @RequestBody final ClubbedTORequest clubbedTORequest) throws TransferOrderCreationException,
				DayCloseInitiatedException, InventoryUpdateException, SumoException, DataNotFoundException {
            long startTime = System.currentTimeMillis();
			LOG.info("Started Bulk Transfer Order API is Called By : {}",
					clubbedTORequest.getGenerationUnit().getName());
            List<TransferOrderData> transferOrderDataList = new ArrayList<>();
            boolean flag = transferOrderManagementService.getAlreadyTransferredBulkRO(clubbedTORequest.getClubRoIds());
            BulkTOResponse response = new BulkTOResponse();
            if(!flag) {
                synchronized (factory.getMutex(clubbedTORequest.getGenerationUnit().getId() + "_TO_ID")) {
                    flag = transferOrderManagementService.getAlreadyTransferredBulkRO(clubbedTORequest.getClubRoIds());
                    if(!flag){
                        BulkTransferEvent bulkTransferEvent = transferOrderManagementService.initiateBulkTransferEvent(clubbedTORequest,null,null,
                                SCMServiceConstants.REGULAR_BULK_TRANSFER);
                        transferOrderDataList = transferOrderManagementService.createBulkTransferOrders(clubbedTORequest,bulkTransferEvent);
                        Boolean isInvoiceSet = false;
                        try{
                            isInvoiceSet = transferOrderManagementService.generateInvoiceForTO(transferOrderDataList,null);
                        }catch (Exception e){
                            LOG.info("Error While Setting Invoices During BULK TO");
                        }
                        response.setInvoiceSet(isInvoiceSet);
                        response.setEventId(bulkTransferEvent.getBulkTransferEventId());
                        transferOrderManagementService.closeBulkTransferEvent(transferOrderDataList.size(), bulkTransferEvent.getBulkTransferEventId()
                        ,isInvoiceSet);
                        LOG.info("Completed Bulk Transfer Order API is Called By : {},{}",
                                clubbedTORequest.getGenerationUnit().getName(), System.currentTimeMillis() - startTime);
                    }else{
                        throw new SumoException("Error while Creating  TO Found Already Transferred Request Order  Please Club Again!");
                    }
                }
            }else{
                throw new SumoException("Error while Creating  TO Found Already Transferred Request Order  Please Club Again!");
            }
            response.setToIds(transferOrderDataList.stream().map(transfer -> Objects.nonNull(transfer.getId()) ? transfer.getId() : 1 ).
                    collect(Collectors.toList()));
            return response;

    }

    @RequestMapping(method = RequestMethod.POST, value = "get-torqus-transfer/{transferOrderId}")
    public View getTorqusView(@PathVariable Integer transferOrderId) {
        TransferOrder transferOrder = fetchTransferOrderService.getTransferOrderDetail(transferOrderId);
        return excelViewGenerator.getTorqusTOView(transferOrder, scmCache.getSkuDefinitions(),
                scmCache.getProductDefinitions(), scmCache.getSubCategoryDefinitions());
    }

    @RequestMapping(method = RequestMethod.GET, value = "eway-bill-number", produces = MediaType.APPLICATION_JSON)
    public String getEwayBullNum(@RequestParam final int transferOrderId) {
        return transferOrderManagementService.getEwayBillNumber(transferOrderId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-torqus-transfer")
    public View getTorqusConsolidatedView(@RequestBody UnitData unitData) {
        List<TransferOrder> transferOrders = transferOrderManagementService.getTorqusTransferOrderList(
                unitData.getUnitId(), SCMUtil.getDate(unitData.getBusinessDate()),
                SCMUtil.getNextDate(unitData.getBusinessDate()));
        if (transferOrders != null) {
            List<TransferOrderItem> transferOrderItemList = new ArrayList<>();
            transferOrders.stream().filter(transferOrder -> !transferOrder.getStatus().equals(SCMOrderStatus.CANCELLED))
                    .forEach(transferOrder -> {
                        transferOrderItemList.addAll(transferOrder.getTransferOrderItems());
                    });
            return excelViewGenerator.getConsolidatedTorqusView(transferOrderItemList, scmCache.getSkuDefinitions(),
                    scmCache.getProductDefinitions(), scmCache.getSubCategoryDefinitions());
        } else {
            return null;
        }
    }

    @RequestMapping(method = RequestMethod.GET, value = "transfer-order-find", produces = MediaType.APPLICATION_JSON)
    public List<TransferOrder> findTransferOrders(@RequestParam(required = false) final Integer generationUnitId,
                                                  @RequestParam(required = false) final Integer generatedForUnitId,
                                                  @RequestParam(required = true) final String startDate, @RequestParam(required = true) final String endDate,
                                                  @RequestParam(required = false) final SCMOrderStatus status,
                                                  @RequestParam(required = false) final Integer transferOrderId) {
        Date start = SCMUtil.getDate(SCMUtil.parseDate(startDate));
        Date end = SCMUtil.getDate(SCMUtil.getNextDate(SCMUtil.parseDate(endDate)));
        return transferOrderManagementService.findTransferOrders(generationUnitId, generatedForUnitId, start, end,
                status, transferOrderId);
    }
    @RequestMapping(method = RequestMethod.GET, value = "pending-transfer-order-find", produces = MediaType.APPLICATION_JSON)
    public eInvoiceResponseObject findPendingTransferOrders(@RequestParam(required = false) final Integer selectedStateCode,
                                                        @RequestParam(required = false) final Integer generationUnitId,
                                                  @RequestParam(required = false) final Integer generatedForUnitId,
                                                  @RequestParam(required = true) final String startDate, @RequestParam(required = true) final String endDate) {
        Date start = SCMUtil.getDate(SCMUtil.parseDate(startDate));
        Date end = SCMUtil.getDate(SCMUtil.getNextDate(SCMUtil.parseDate(endDate)));
        return transferOrderManagementService.findPendingTransferOrders(selectedStateCode,generationUnitId, generatedForUnitId, start, end);
    }


    @RequestMapping(method = RequestMethod.GET, value = "incremental-transfer-order", produces = MediaType.APPLICATION_JSON)
    public List<TransferOrder> incrementalTransferOrders(@RequestParam(required = false) final Integer generationUnitId,
                                                         @RequestParam(required = false) final Integer generatedForUnitId,
                                                         @RequestParam(required = true) final String startDate, @RequestParam(required = true) final String endDate,
                                                         @RequestParam(required = false) final SCMOrderStatus status,
                                                         @RequestParam(required = false) final Integer transferOrderId,
                                                         @RequestParam String ids) {
        Date start = SCMUtil.getDate(SCMUtil.parseDate(startDate));
        Date end = SCMUtil.getDate(SCMUtil.getNextDate(SCMUtil.parseDate(endDate)));
        return transferOrderManagementService.incrementalTransferOrders(generationUnitId, generatedForUnitId, start, end,
                status, transferOrderId, ids);
    }

    @RequestMapping(method = RequestMethod.GET, value = "transfer-order", produces = MediaType.APPLICATION_JSON)
    public TransferOrder getTransferOrderDetail(@RequestParam final int transferOrderId) {
        return fetchTransferOrderService.getTransferOrderDetail(transferOrderId);
    }

    @DayClosureCheck
    @RequestMapping(method = RequestMethod.PUT, value = "transfer-order-cancel", consumes = MediaType.APPLICATION_JSON)
    public Integer cancelTransferOrder(@RequestBody final Map requestObj) throws DayCloseInitiatedException,SumoException {
        Integer transferOrderId = (Integer) requestObj.get("transferOrderId");
        Integer updatedBy = (Integer) requestObj.get("updatedBy");
        return transferOrderManagementService.cancelTransferOrder(transferOrderId, updatedBy);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-sku-to-products", produces = MediaType.APPLICATION_JSON)
    public SkuToProductResponse getSkuToProducts(@RequestBody SkuToProductsRequestData skuToProductsRequestData, @RequestParam(required = true) Boolean isPackagingRequired) throws SumoException {
        return transferOrderManagementService.getSkuToProducts(skuToProductsRequestData.getUnitId(), skuToProductsRequestData.getRoIds(),
                isPackagingRequired, skuToProductsRequestData.getProductToSkuMap());
    }

    @RequestMapping(method = RequestMethod.GET, value = "find-bulk-event-short", produces = MediaType.APPLICATION_JSON)
    public List<BulkTransferEvent> findBulkTransferEventsShort(@RequestParam(required = true) Integer unitId ,
                                                               @RequestParam(required = true)  String startDate , @RequestParam(required = true)  String endDate) throws SumoException {
        Date start = SCMUtil.getDate(SCMUtil.parseDate(startDate));
        Date end = SCMUtil.getDate(SCMUtil.getNextDate(SCMUtil.parseDate(endDate)));
        return transferOrderManagementService.findBulkEventsShort(unitId,start,end);
    }

    @RequestMapping(method = RequestMethod.GET, value = "find-bulk-event", produces = MediaType.APPLICATION_JSON)
    public BulkTransferEvent findBulkTransferEvents(@RequestParam(required = true) Integer unitId ,
                                                          @RequestParam(required = true) Integer bulkEventId) throws SumoException {
        return transferOrderManagementService.findBulkEvent(unitId ,bulkEventId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-transfer-orders-view")
    public View getTOsView(@RequestBody List<Integer> toIds , @RequestParam(required = true) Boolean isBulkEvent) {

        List<TransferOrder> transferOrders = transferOrderManagementService.getTOsByIds(toIds);

        if (!transferOrders.isEmpty()) {
            return excelViewGenerator.getTosView(transferOrders, scmCache.getSkuDefinitions(),
                    scmCache.getProductDefinitions(), scmCache.getSubCategoryDefinitions(),isBulkEvent);
        } else {
            return null;
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = "set-transfer-invoices")
    public Boolean setInvoices(@RequestBody List<Integer> toIds , @RequestParam(required = false) Integer eventId) throws SumoException {
        List<TransferOrderData> transferOrderDataList =  transferOrderManagementDao.findByTransferIds(toIds);
        try {
            return transferOrderManagementService.generateInvoiceForTO(transferOrderDataList, eventId);
        }catch (Exception e){
            throw new SumoException("Error While Setting Invoices");
        }

    }

    @RequestMapping(method = RequestMethod.GET, value = "get-asset-transfers")
    public List<TransferredAsset> getAssetTransfers(@RequestParam Integer unitId) throws SumoException {
        return transferOrderManagementService.findAssetTransfersByUnitId(unitId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "update-asset-status")
    public Boolean updateAssetStatus(@RequestBody TransferredAsset asset,@RequestParam Boolean isOriginal,@RequestParam Integer userId) throws SumoException {
        return transferOrderManagementService.updateAssetStatus(asset,isOriginal,userId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "bulk-update-asset-status")
    public Boolean bulkUpdateAssetStatus(@RequestBody List<TransferredAsset> assetList,@RequestParam Boolean isOriginal,@RequestParam Integer userId) throws SumoException {
        return transferOrderManagementService.bulkUpdateAssetStatus(assetList,isOriginal,userId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "download-bulk-standalone-Template")
    public View getBulkStandaloneTemplate(@RequestBody List<GoodsReceivedItem> selectedProducts , @RequestParam(required = true) Integer unitId){
       FulfillmentType fulfillmentType = SCMUtil.isKitchen(scmCache.getUnitDetail(unitId)) ? FulfillmentType.KITCHEN : FulfillmentType.WAREHOUSE;
       List<Integer> unitIds = transferOrderManagementService.getAllActiveReceivingUnits(fulfillmentType,unitId);
       return excelViewGenerator.createStandAloneTOTemplate(selectedProducts,unitIds);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-products-for-standalone")
    public List<Integer> getProductsForStandAlone(@RequestParam(required = true) Integer unitId) throws SumoException {
        List<Integer> productIds = new ArrayList<>();
        Boolean isKitchen = SCMUtil.isKitchen(scmCache.getUnitDetail(unitId));
        productIds.addAll(scmCache.getProductDefinitions().values().stream()
                .filter(p -> ( isKitchen ? SCMUtil.isKitchenFullfillmentType(p.getFulfillmentType()) :
                        SCMUtil.isWarehouseFullfillmentType(p.getFulfillmentType())) ).map(ProductDefinition::getProductId).collect(Collectors.toSet()));
        productIds.retainAll(scmCache.getAvailableProductForUnit(unitId));
        return productIds;
    }

    @RequestMapping(method = RequestMethod.POST, value = "parse-bulk-standalone-sheet", consumes = MediaType.MULTIPART_FORM_DATA)
    public Map<Integer,Map<Integer,GoodsReceivedItem>> parseBulkStandAloneTOSheet(@RequestParam(value = "file") final MultipartFile file , @RequestParam(value = "unitId") Integer unitId ) throws SumoException {
       Map<Integer,Map<Integer,GoodsReceivedItem>>  unitDistribution =  excelViewGenerator.parseDistributionSheet(file,unitId);
       if(transferOrderManagementService.validateStandAloneSheet(unitDistribution,unitId)){
           return unitDistribution;
       }else{
           throw new SumoException("Invalid Excell ", "Some Additional Unit/sku is Added In Excell");
       }
    }

    @RequestMapping(method = RequestMethod.POST, value = "bulk-standalone-transfer", consumes = MediaType.MULTIPART_FORM_DATA)
    public List<Integer> bulkStandAloneTransfer(HttpServletRequest request,  @RequestParam(value = "file") final MultipartFile file , @RequestParam(value = "unitId") Integer unitId) throws SumoException, DataNotFoundException, TransferOrderCreationException, InventoryUpdateException {
        long startTime = System.currentTimeMillis();
        LOG.info("BULK Standalone Transfer API Is Called By Unit {}",scmCache.getUnitDetail(unitId).getUnitName());
        Integer userId = getLoggedInUser(request);
        Map<Integer,Map<Integer,GoodsReceivedItem>> unitWiseDistribution =  excelViewGenerator.parseDistributionSheet(file,unitId);
        BulkTransferEvent bulkTransferEvent = transferOrderManagementService.initiateBulkTransferEvent(null,userId,unitId,
                SCMServiceConstants.STANDALONE_BULK_TRANSFER);
        List<TransferOrderData> transfers = transferOrderManagementService.bulkStandAloneTransfer(unitWiseDistribution,unitId,
                userId , bulkTransferEvent.getBulkTransferEventId());
        Boolean isInvoiceSet = false;
        try{
            isInvoiceSet = transferOrderManagementService.generateInvoiceForTO(transfers,null);
        }catch (Exception e){
            LOG.info("Error While Setting Invoices During BULK StandAlone TO");
        }
        transferOrderManagementService.closeBulkTransferEvent(transfers.size(), bulkTransferEvent.getBulkTransferEventId()
                ,isInvoiceSet);
        LOG.info("Completed Bulk Transfer Order API Called By : {},{}",
               scmCache.getUnitDetail(unitId).getUnitName(), System.currentTimeMillis() - startTime);
        return  transfers.stream().map(transfer -> transfer.getId()).collect(Collectors.toList());

    }
    @RequestMapping(method = RequestMethod.POST, value = "find-missing-distance-mappings", produces = MediaType.APPLICATION_JSON)
    public void findMissingDistanceMappings(@RequestBody List<Integer> toIds) throws SumoException {
        List<TransferOrder> transferOrderList = transferOrderManagementService.getTOsByIds(toIds);
        if(!transferOrderList.isEmpty()) {
            Map<String ,String> missingMappings = transferOrderManagementService.findMissingDistanceMapping(transferOrderList, true);
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = "generate-e-invoice-json", produces = MediaType.APPLICATION_JSON)
    public void generateEInvoiceJson(HttpServletResponse response, @RequestBody List<Integer> toIds) throws IOException, SumoException {
        transferOrderManagementService.downloadEInvoiceJson(toIds,response);
    }
    @RequestMapping(method = RequestMethod.POST , value = "initiate-fa-transfer"  )
     public Boolean faTransferData(@RequestBody Map<Integer,BigDecimal> productAndQty , @RequestParam Integer receivingUnitId ,
                                   @RequestParam Integer fullfillingUnitId , @RequestParam String budgetType , @RequestParam Integer userId ,
                                   @RequestParam String toType ,@RequestParam(required = false) Integer roId) throws SumoException {
        return transferOrderManagementService.initiateFaTransfer(productAndQty ,  receivingUnitId ,  fullfillingUnitId ,  budgetType , userId ,  toType ,roId) ;
    }
    @RequestMapping(method = RequestMethod.POST , value = "change-fa-tranfer-product")
    public Boolean changFaTransferData(@RequestParam Integer productId , @RequestParam  Integer eventId ,
                                       @RequestParam Boolean toAdd ,@RequestParam(required = false) BigDecimal transferQty  ) throws SumoException {
        return transferOrderManagementService.intiateChangeOnFa(productId , eventId , toAdd , transferQty) ;
    }


    @RequestMapping(method = RequestMethod.POST, value = "upload-e-invoice-excell", consumes = MediaType.MULTIPART_FORM_DATA)
    public Boolean uploadEInvoiceExcelL(@RequestParam(value = "file") final MultipartFile file) throws IOException, SumoException {
        return transferOrderManagementService.uploadInvoiceSheet(file);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-e-invoice-data", produces = MediaType.APPLICATION_JSON)
    public TransferOrderEInvoiceDTO uploadEInvoiceExcelL(@RequestParam(value = "transferOrderId") Integer toId) throws IOException, SumoException {
        return transferOrderManagementService.findEInvoiceByTOId(toId);
    }

}
