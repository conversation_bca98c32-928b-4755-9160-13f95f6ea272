
/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.scm.service.controller;

import com.stpl.tech.kettle.report.metadata.model.ReportCategories;
import com.stpl.tech.master.core.service.AbstractAutomatedReports;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.JaxbUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.text.ParseException;


@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
		+ SCMServiceConstants.AUTOMATED_SCM_REPORTS_RESOURCE)
public class SCMReportsResource extends AutomatedSCMReportsResource {

	private static final Logger LOG = LoggerFactory.getLogger(SCMReportsResource.class);

	@Autowired
	private EnvProperties props;

	@RequestMapping(method = RequestMethod.GET, value = "generate-scm-report")
	public void generateScmReport(@RequestParam String reportName) throws ParseException, EmailGenerationException, IOException {
		LOG.info("Received Request to generate SCM Report for :: {}",reportName);
		String name = reportName.replaceAll(" ","");
		LOG.info("came inside: {}",name);
		ReportCategories reportCategories = JaxbUtil.jaxbXMLToObject(ReportCategories.class,
				props.getBasePath() + "/reports/"+name+".xml");
		executeReports(reportCategories, AppUtils.getFormattedDate(AppUtils.getCurrentBusinessDate()));
	}

}
