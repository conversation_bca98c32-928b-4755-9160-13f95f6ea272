package com.stpl.tech.scm.service.controller;

import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.scm.core.SCMServiceConstants;
import com.stpl.tech.scm.core.cache.SCMCache;
import com.stpl.tech.scm.core.exception.InventoryUpdateException;
import com.stpl.tech.scm.core.exception.NegativeStockException;
import com.stpl.tech.scm.core.exception.SumoException;
import com.stpl.tech.scm.core.service.EnvProperties;
import com.stpl.tech.scm.core.service.StockManagementService;
import com.stpl.tech.scm.core.service.WarehouseStockManagementService;
import com.stpl.tech.scm.core.util.SCMUtil;
import com.stpl.tech.scm.core.util.model.AcknowledgeTransactionVO;
import com.stpl.tech.scm.domain.model.*;
import com.stpl.tech.scm.domain.model.DayCloseEvent;
import com.stpl.tech.scm.domain.model.DayCloseEventLogType;
import com.stpl.tech.scm.domain.model.DayCloseTxnItem;
import com.stpl.tech.scm.domain.model.Disclaimer;
import com.stpl.tech.scm.domain.model.FixedAssetDayCloseResponseObject;
import com.stpl.tech.scm.domain.model.ProductStockForUnit;
import com.stpl.tech.scm.domain.model.RegularOrderEvent;
import com.stpl.tech.scm.domain.model.SkuStockForUnit;
import com.stpl.tech.scm.domain.model.UnitDetail;
import com.stpl.tech.scm.service.controller.view.ExcelViewGenerator;
import com.stpl.tech.scm.service.model.AckTxnVO;
import com.stpl.tech.scm.service.model.CurrentInventoryVO;
import com.stpl.tech.scm.service.model.DayCloseVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.View;

import java.text.ParseException;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 11-06-2017.
 */
@RestController
@RequestMapping(value = SCMServiceConstants.API_VERSION + SCMServiceConstants.SEPARATOR
        + SCMServiceConstants.WAREHOUSE_STOCK_MANAGEMENT_ROOT_CONTEXT) // wh-stock-management
public class WarehouseStockManagementResources extends AbstractSCMResources{

    private static final Logger LOG = LoggerFactory.getLogger(WarehouseStockManagementResources.class);

    @Autowired
    private WarehouseStockManagementService stockManagementService;

    @Autowired
    private ExcelViewGenerator excelViewGenerator;

    @Autowired
    SCMCache scmCache;

    @Autowired
    private StockManagementService stockService;

    @Autowired
    private EnvProperties props;

    @RequestMapping(method = RequestMethod.POST, value = "day-close/{unitId}/{userId}")
    public DayCloseEvent initiateDayCloseEventForUnit(@PathVariable(value = "unitId") int unitId,
                                                @PathVariable(value = "userId") int userId ,@RequestParam(required = false) Boolean isAllListType ,
                                                      @RequestBody(required = false) List<String> subCategories) throws SumoException {
//        try {
//            LOG.info("Removing duplicate keys Before Day Close");
//            List<Integer> ids = stockService.fetchDuplicateKey();
//            if (ids != null && ids.size() > 0) {
//                stockService.updateDuplicateKeyLatestFlag(ids);
//                SlackNotificationService.getInstance().sendNotification(props.getEnvType(), "SUMO",
//                        SlackNotification.SUPPLY_CHAIN, "Number of Duplicate Keys Removed = " + ids.size());
//            }
//        }
//        catch (Exception e){
//            LOG.error("Error in Removing duplicate keys During Day Close", e);
//        }
        DayCloseEvent event = stockManagementService.getActiveWarehouseDayCloseEvent(unitId);
        if(event == null){
            event = stockManagementService.initiateDayClose(unitId, userId , isAllListType,subCategories);
        }
        return event;
    }

    @RequestMapping(method = RequestMethod.POST, value = "settle-pending-rejectedGR/{unitId}/{userId}")
    public List<Integer> settleRejectedPendingGR(@PathVariable(value = "unitId") int unitId,
                                                @PathVariable(value = "userId") int userId) throws InventoryUpdateException, SumoException, ParseException, DataNotFoundException {
        return stockManagementService.settleRejectedPendingGR(unitId, userId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "day-close/cancel/{eventId}/{userId}")
    public boolean cancelDayCloseEventForUnit(@PathVariable(value = "eventId") int eventId,
                                              @PathVariable(value = "userId") int userId) {
        return stockManagementService.cancelDayCloseEvent(eventId, userId);
    }


    @RequestMapping(method = RequestMethod.GET, value = "day-close/{unitId}")
    public DayCloseEvent getActiveDayCloseEventForUnit(@PathVariable(value = "unitId") int unitId) {
        return stockManagementService.getActiveWarehouseDayCloseEvent(unitId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "receivings/{unitId}/{eventId}")
    public List<DayCloseTxnItem> getReceivings(@PathVariable(value = "unitId") int unitId,
                                                     @PathVariable(value = "eventId") int eventId)
                                                    throws DataUpdationException {
        return stockManagementService.getReceivingsForClosing(unitId, null);
    }

    @RequestMapping(method = RequestMethod.GET, value = "wastages/{unitId}/{eventId}")
    public List<DayCloseTxnItem> getWastages(@PathVariable(value = "unitId") int unitId,
                                             @PathVariable(value = "eventId") int eventId) {
        return stockManagementService.getWastagesForClosing(unitId, null);
    }

    @RequestMapping(method = RequestMethod.GET, value = "transfers/{unitId}/{eventId}")
    public List<DayCloseTxnItem> getTransfers(@PathVariable(value = "unitId") int unitId,
                                                    @PathVariable(value = "eventId") int eventId) throws DataUpdationException {
        return stockManagementService.getTransfersForClosing(unitId, null);
    }

    @RequestMapping(method = RequestMethod.GET, value = "gatepasses/{unitId}/{eventId}")
    public List<DayCloseTxnItem> getGatepasses(@PathVariable(value = "unitId") int unitId,
                                              @PathVariable(value = "eventId") int eventId) throws DataUpdationException {
        return stockManagementService.getGatepasses(unitId, null);
    }

    @RequestMapping(method = RequestMethod.GET, value = "returns/{unitId}/{eventId}")
    public List<DayCloseTxnItem> getReturns(@PathVariable(value = "unitId") int unitId,
                                               @PathVariable(value = "eventId") int eventId) throws DataUpdationException {
        return stockManagementService.getGatepassReturns(unitId, null);
    }

    @RequestMapping(method = RequestMethod.GET, value = "invoices/{unitId}/{eventId}")
    public List<DayCloseTxnItem> getInvoices(@PathVariable(value = "unitId") int unitId,
                                              @PathVariable(value = "eventId") int eventId) throws DataUpdationException {
        return stockManagementService.getInvoices(unitId, null);
    }

    @Deprecated
    @RequestMapping(method = RequestMethod.GET, value = "bookings/{unitId}/{eventId}")
    public List<DayCloseTxnItem> getBookings(@PathVariable(value = "unitId") int unitId,
                                             @PathVariable(value = "eventId") int eventId) {
        return stockManagementService.getBookingsForClosing(unitId, null);
    }

	@RequestMapping(method = RequestMethod.GET, value = "bookings-summary/{unitId}/{eventId}")
	public AcknowledgeTransactionVO getBookingsSummary(@PathVariable(value = "unitId") int unitId,
                                                       @PathVariable(value = "eventId") int eventId) {
		return stockManagementService.getBookingsSummaryForClosing(unitId, null, true);
	}

    @RequestMapping(method = RequestMethod.GET, value = "reverse-bookings-summary/{unitId}/{eventId}")
	public AcknowledgeTransactionVO getReverseBookingsSummary(@PathVariable(value = "unitId") int unitId,
                                                       @PathVariable(value = "eventId") int eventId) {
		return stockManagementService.getReverseBookingsSummaryForClosing(unitId, null, true);
	}

    @RequestMapping(method = RequestMethod.POST, value = "inventory/{unitId}")
    public List<SkuStockForUnit> getInventoryForClosing(@PathVariable(value = "unitId") int unitId , @RequestParam(required = false) Boolean isAllListType , @RequestBody(required = false) List<Integer> subCategoryIds)
            throws DataUpdationException {
        return stockManagementService.getInventoryForClosing(unitId,isAllListType , subCategoryIds);
    }

    @RequestMapping(method = RequestMethod.POST, value = "ack-transactions")
    public boolean acknowledgeTransactions(@RequestBody AckTxnVO ackTxnVO) throws DataUpdationException {
        boolean flag = false;
        if(stockManagementService.ifAlreadyUpdated(ackTxnVO.getType(), ackTxnVO.getEvent())){
            return flag;
        }else{
            if(DayCloseEventLogType.BOOKINGS.equals(ackTxnVO.getType())){
            	List<DayCloseTxnItem> items = stockManagementService.getBookingsForClosingByBookingIds(ackTxnVO.getUnitId(), ackTxnVO.getEventIds(), true);
                flag = stockManagementService.acknowledgeBookings(ackTxnVO.getId(), ackTxnVO.getEvent(),
                        ackTxnVO.getType(), items, true);
            } else if(DayCloseEventLogType.REVERSE_BOOKING.equals(ackTxnVO.getType())){
            	List<DayCloseTxnItem> items = stockManagementService.getBookingsForClosingByBookingIds(ackTxnVO.getUnitId(), ackTxnVO.getEventIds(), false);
                flag = stockManagementService.acknowledgeBookings(ackTxnVO.getId(), ackTxnVO.getEvent(),
                        ackTxnVO.getType(), items, false);
            } else {
                flag = stockManagementService.acknowledgeTransactions(ackTxnVO.getId(), ackTxnVO.getEvent(),
                    ackTxnVO.getType(), ackTxnVO.getItems());
            }
            return flag;
        }
    }


    @RequestMapping(method = RequestMethod.POST, value = "preview-variance")
    public List<ProductStockForUnit> previewInventory(@RequestBody AckTxnVO ackTxnVO)
            throws DataUpdationException, InventoryUpdateException, SumoException {
        return stockManagementService.getNegativeStocks(ackTxnVO.getId(),ackTxnVO.getEvent(),ackTxnVO.getItems());
    }


    @RequestMapping(method = RequestMethod.POST, value = "submit-inventory")
    public List<ProductStockForUnit> submitInventory(@RequestBody AckTxnVO ackTxnVO)
            throws DataUpdationException, InventoryUpdateException, SumoException {
        return stockManagementService.submitInventory(ackTxnVO.getId(), ackTxnVO.getEvent(), ackTxnVO.getItems(),false);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-sku-for-unit")
    public List<Integer> getSkuIdsForUnit(@RequestParam Integer unitId) throws DataUpdationException {
        return stockManagementService.getSkuListForUnit(unitId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "stock-at-hand")
    public List<ProductStockForUnit> currentInventory(@RequestBody CurrentInventoryVO reqObj) throws DataUpdationException {
        try {
            Boolean isCafe = SCMUtil.isWareHouseOrKitchen(scmCache.getUnitDetail(reqObj.getUnitId()));
            LOG.info("Removing duplicate keys While getting current inventory");
            List<Integer> ids = stockService.fetchDuplicateKey(isCafe);
            if (ids != null && ids.size() > 0) {
                stockService.updateDuplicateKeyLatestFlag(ids,isCafe);
                SlackNotificationService.getInstance().sendNotification(props.getEnvType(), "SUMO",
                        SlackNotification.SUPPLY_CHAIN, "Number of Duplicate Keys Removed = " + ids.size());
            }
        }
        catch (Exception e){
            LOG.error("Error in Removing duplicate keys While Fetching Current Inventory", e);
        }
        return stockManagementService.getInventorySummary(reqObj.getSkuIds(), reqObj.getUnitId());
    }

    @RequestMapping(method = RequestMethod.GET, value = "correct-inventory/{eventId}")
    public List<ProductStockForUnit> correctInventory(@PathVariable(value = "eventId") int eventId) throws DataUpdationException {
        return stockManagementService.getInventorySummary(eventId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "day-close-threshold")
    public Disclaimer checkClosingInitiated(@RequestParam(value = "id") int unitId) throws DataUpdationException {
        return stockManagementService.checkIfDayCloseDoneInWeek(unitId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "fa-day-close")
    public FixedAssetDayCloseResponseObject checkFixedAssetDayClose(@RequestParam(value = "id") int unitId) throws DataUpdationException {
        return stockManagementService.getAssetsToDayCloseToday(unitId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "submit-day-close")
    public boolean submitDayClose(@RequestBody DayCloseVO daycloseVO)
            throws DataUpdationException, InventoryUpdateException, SumoException, NegativeStockException {
        Integer userId = daycloseVO.getUserId();
        Integer eventId = daycloseVO.getEventId();
        boolean flag = stockManagementService.correctInventory(userId,eventId,daycloseVO.getItems());
        if(flag) {
            flag = stockManagementService.submitDayClose(userId, eventId);
            if(flag){
                stockManagementService.generateAndSendVarianceReport(eventId);
            }
        }
        return flag;
    }

    @RequestMapping(method = RequestMethod.POST, value = "download-inventory-list")
    public View downloadInventoryList(@RequestParam(required = true) Integer unitId , @RequestParam(required = false) Boolean isAllListType , @RequestBody(required = false) List<Integer> subCategoryIds ) throws DataUpdationException {
        if (unitId != null) {
            UnitDetail unitDetail= scmCache.getUnitDetail(unitId);
            List<SkuStockForUnit> inventoryList = stockManagementService.getInventoryForClosing(unitId,isAllListType,subCategoryIds);
            return excelViewGenerator.getWarehouseInventoryListView(inventoryList,unitDetail.getUnitName());
        } else {
            return null;
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = "closing-dates")
    public Map<Integer,String> getSkuClosings(@RequestBody Map<String,String> reqObj){
        String skus = reqObj.get("skus");
        Integer unitId = Integer.parseInt(reqObj.get("unitId"));
        if(skus!=null && !skus.isEmpty()){
            List<Integer> skuList = Arrays.asList(skus.split(","))
                    .stream()
                    .mapToInt(Integer::parseInt)
                    .boxed()
                    .collect(Collectors.toList());

            return stockManagementService.getSkuClosings(unitId,skuList);
        }
        return Collections.emptyMap();
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-regular-ordering-events")
    public List<RegularOrderEvent> getRegularOrderingEvents(@RequestParam Integer unitId){
        LOG.info("Got Request to find events : {}",unitId);
        return stockManagementService.getRegularOrderingEvents(unitId);
    }

}
