<style>
    .chip {
        font-size: 11px;
    }

    .btn i {
        font-size: 1rem !important;
    }

    .btn.btn-xs-small.vBtn {
        width: 70px !important;
        font-size: 10px;
        padding: 5px !important;
        height: 40px;
        line-height: 16px;
        text-align: center;
    }
    .buttonload {
		  background-color: #04AA6D; /* Green background */
		  border: none; /* Remove borders */
		  color: white; /* White text */
		  padding: 12px 16px; /* Some padding */
		  font-size: 16px /* Set a font size */
		}

</style>
<div class="row" data-ng-init="init()">
	<div class="col s12">
		<div class="searchingCard `row white z-depth-3">
			<div class="col s12">
				<h4>Vendor Contract Requests</h4>
			</div>
			<div class="row">
				<div class="col s2">
					<label>Select Start date</label>
					<input input-date type="text" ng-model="startDate" container="" format="yyyy-mm-dd"/>
				</div>
				<div class="col s2">
					<label>Select End date</label>
					<input input-date type="text" ng-model="endDate" container="" format="yyyy-mm-dd"/>
				</div>
				<div class="col s2">
					<label>Select Status</label>
					<select data-ng-model="selectedStatus"
							data-ng-options="type as type for type in contractStatus"></select>
				</div>

				<div class="col s2">
					<label>Select Vendor</label>
					<select
							ui-select2
							style="width: 100%;"
							id="vendors"
							data-ng-model="vendorSelected"
							data-ng-options="vendor as (vendor.name + ' [' + vendor.category + ']') for vendor in allVendorDataList | orderBy : 'name' track by vendor.id"
							data-ng-change="selectVendor(vendorSelected)"
							required></select>
					<p
							data-ng-show="basicDetail.selectVendorValue.$error.required"
							class="errorMessage">Please select vendor.</p>
				</div>
				<div class="col s2">
					<button class="btn margin-top-20" data-ng-click="getContracts()">Find</button>
					<!--acl-action="VOMVPOV"-->
				</div>
			</div>

			<hr>
			<div class="col s12" data-ng-if="contractRequest.length==0" style="padding:30px;color:gray;font-size: 20px;">
				<div class="row margin0 center">
					No Contracts(s) found for the selected criteria
				</div>
			</div>
			<div class="col s12" ng-if="contractRequest.length>0">
				<div class="row">
					<ul class="collapsible popout" data-collapsible="accordion" watch>
						<li class="row margin0" style="padding:5px;"
							data-ng-repeat="invR in contractRequest track by $index">
							<div class="col s10 collapsible-header waves-effect waves-light lighten-3"
								 style="border:1px  solid #ddd;padding:0;">
								<div class="col s11">
									<div class="row margin0">
										<div class="left">
											<span><b>Contract# {{invR.vendorContractId}}</b> for {{invR.vendorName}}</span>
										</div>
										<div class="right">
											<span class="chip blue-grey white-text">STATUS ::: {{invR.recordStatus}}</span>
										</div>
									</div>
									<div class="row margin0">
										<div class="right">
											<span class="chip grey white-text">By: {{invR.createdBy}}</span>
											<span class="chip grey white-text">Start Date : {{invR.startDate  | date : 'yyyy-MM-dd'}}</span>
											<span class="chip grey white-text">End Date : {{invR.endDate  | date : 'yyyy-MM-dd'}}</span>
										</div>
									</div>
								</div>
								<div class="col s1" style="padding-top: 1.5rem;">
									<i class="fa fa-caret-down right"></i>
								</div>
							</div>

							<div class="col s2" data-ng-if="invR.recordStatus!=='CANCELLED'">
								<button class="btn btn-medium vBtn"
										data-ng-if="invR.recordStatus!= 'APPLIED'"
										data-ng-click="previewTemplate(invR.vendorContractId)">
									<i class="fa fa-download"></i> Generate Contract
								</button>
								<button class="btn btn-medium vBtn"
										data-ng-if="invR.recordStatus!= 'APPLIED'"
										data-ng-click="cancelContract(invR.vendorContractId)">
									<i class="fa fa-download"></i> CANCEL
								</button>
								<button class="btn btn-medium vBtn margin-top-10"
										data-ng-if="invR.unsignedDocumentId!=null && invR.authSignedDocumentId==null && invR.signedDocumentId==null"
										data-ng-click="printContract(invR.unsignedDocumentId, $index)"><i class="fa fa-print"></i> Show Unsigned Contract
								</button>
								<button class="btn btn-medium vBtn margin-top-10"
										data-ng-if="invR.signedDocumentId!=null && invR.authSignedDocumentId==null"
										data-ng-click="printContract(invR.signedDocumentId, $index)"><i class="fa fa-print"></i> Show Signed Contract
								</button>
								<button class="btn btn-medium vBtn margin-top-10"
										data-ng-if="invR.authSignedDocumentId!=null"
										data-ng-click="printContract(invR.authSignedDocumentId, $index)"><i class="fa fa-print"></i> Show Auth Signed Contract
								</button>
								<button acl-action="VCMMV" class="btn btn-medium vBtn margin-top-10"
										data-ng-if="invR.isMailTriggered == null && invR.signedDocumentId==null"
										data-ng-click="mailVendor(invR.vendorContractId)"><i class="fa fa-print"></i> Mail Vendor
								</button>
<!--								<button class="btn btn-medium vBtn margin-top-10"-->
<!--										data-ng-if="invR.recordStatus=='APPROVED'"-->
<!--										data-ng-click="applyContract(invR.vendorContractId)"><i class="fa fa-print"></i> Apply Contract-->
<!--								</button>-->
							</div>

							<div class="collapsible-body">
								<table class="bordered striped">
									<thead>
									<tr>
										<th class="center-align">SKU</th>
										<th class="center-align">UOM</th>
										<th class="center-align">Pkg</th>
										<th class="center-align">Delivery</th>
										<th class="center-align">Dispatch</th>
<!--										<th class="center-align">Current Price</th>-->
										<th class="center-align">Negotiated Price</th>
									</tr>
									</thead>
									<tbody>
									<tr data-ng-repeat="item in invR.vendorContractItemVOS track by $index">
										<td class="center-align"><a
												data-ng-click="showPreview($event, item.skuId,'SKU')">{{item.sku.name}}[{{item.sku.code}}]</a>
										</td>
										<td class="center-align">{{item.pkg.uom}}</td>
										<td class="center-align">{{item.pkg.name}}</td>
										<td class="center-align">{{item.delivery.code}}</td>
										<td class="center-align">{{item.dispatch.code}}</td>
<!--										<td class="center-align">{{item.current.value}}</td>-->
										<td class="center-align">{{item.updated.value}}</td>
									</tr>
									</tbody>
								</table>
							</div>
						</li>
					</ul>
				</div>
			</div>
		</div>
	</div>
</div>


<script type="text/ng-template" id="vendorContractTemplatePreviewModal.html" class="modal-large" >
	<div  id="previewExcessQuantity" data-ng-init="initTemplateModal()" >
		<div class="modal-content" style="overflow-x: auto; max-height: 350px;">
			<div class="row">
				<h5><b>Vendor Contract Template</b></h5>
			</div>
			<div class="row" style="width:98%;">
				<table class="bordered striped standardView">
					<thead>
					<tr>
						<th class="center-align">Template Name</th>
						<th class="center-align">Process</th>
						<th class="center-align" data-ng-if="documentDetail.fileUrl!=null">Preview Template</th>
					</tr>
					</thead>
					<tbody>
					<tr data-ng-repeat="id in template">
						<td class="center-align">{{id.code}}</td>
						<td  class="center-align">
							<button class="btn btn-small vBtn margin-top-10"\
									data-ng-click="generateContractWithTemplate(id.id)"><i class="fa fa-print"></i> Process
							</button>
						</td>
						<td class="center-align" data-ng-if="documentDetail.fileUrl!=null">
							<a href="{{documentDetail.fileUrl}}" target="_blank">
								<div style="height:100%;width:100%">
									Preview
								</div>
							</a>
						</td>
					</tr>
					</tbody>
				</table>
				<div class="bordered striped TableMobileView">
				</div>
			</div>
		</div>
		<div class="modal-footer right">
			<button class="waves-effect waves-green btn-flat" data-ng-click="close()">Cancel</button>
			<button class="waves-effect waves-green btn" data-ng-click="submit('APPROVED')">Submit</button>
		</div>
	</div>
</script>
