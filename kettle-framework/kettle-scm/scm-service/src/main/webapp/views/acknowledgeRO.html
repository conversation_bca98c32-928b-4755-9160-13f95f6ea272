<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<div class="row white z-depth-3" data-ng-init="init()">
    <div class="col s12"><h3>Acknowledge Request Orders</h3></div>
    <div class="row searchingCard">
        <div class="col s9">
            <label for="inputCreated">Select fulfillment date</label>
            <input input-date type="text" name="created" id="inputCreated" ng-model="selectedDate"
                   data-ng-change="reset()"
                   container="" format="yyyy-mm-dd" select-years="1" min="{{minDate}}"
                   max="{{maxDate}}" />
        </div>
        <div class="col s3 margin-top-10">
            <button class="btn btn-small" ng-click="getOrders()" style="margin-top: 14px;" acl-action="TRNAOV">Get Orders</button>
        </div>
    </div>
    <hr>

    <div class="row" data-ng-if="selectedDate != null && requestOrders.length == 0
                                    && acknowledgedOrders.length == 0">
       <span class="flow-text" style="margin-left: 10px;">
           No request orders found for acknowledgement for the selected date
       </span>
    </div>

    <div class="row" data-ng-if="requestOrders.length>0">
        <div class="col s12">
            <button class="btn left" data-ng-click="acknowledgeAll()" acl-action="TRNAOA"> CHECK ALL </button>
            <button class="btn right" style="margin-right: 10px;" data-ng-if="isChecked(requestOrders)"
                    data-ng-click="submit(requestOrders)" acl-action="TRNAOA">ACKNOWLEDGE & DOWNLOAD</button>
        </div>
        <div class="col s12">
            <ul class="collapsible no-border" data-collapsible="expandable" watch>
                <li ng-repeat="ro in requestOrders">
                    <div class="collapsible-header custom-collection-header">
                        <div class="row margin0">
                            <div class="col s12">
                                <div class="col s6">
                                    <input id="RO-{{ro.id}}" data-ng-model="ro.checked" type="checkbox"/>
                                    <label for="RO-{{ro.id}}">{{ro.requestUnit.name}} (RO No. : {{ro.id}})</label>
                                </div>
                                <div class="col s6">
                                <span class="right-align">
                                    <strong>Created On: </strong>
                                    {{ro.generationTime | date:'dd/MM/yyyy hh:mm:ss a'}}
                                </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="collapsible-body">
                        <div class="row">
                            <table class="bordered striped" data-ng-if="ro.requestOrderItems.length > 0">
                                <tr>
                                    <th>Product Id</th>
                                    <th>Product Name</th>
                                    <th>Requested Quantity</th>
                                    <th>Unit Of Measure</th>
                                </tr>
                                <tr data-ng-repeat="roi in ro.requestOrderItems track by $index">
                                    <td>{{roi.productId}}</td>
                                    <td>{{roi.productName}}</td>
                                    <td>{{roi.requestedAbsoluteQuantity}}</td>
                                    <td>{{roi.unitOfMeasure}}</td>
                                </tr>
                            </table>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
    </div>

    <div class="row" data-ng-if="acknowledgedOrders.length>0">
        <div class="col s12">
            <h4>Acknowledged Orders</h4>
        </div>
        <div class="col s12">
            <ul class="collapsible no-border" data-collapsible="expandable">
                <li ng-repeat="ro in acknowledgedOrders">
                    <div class="collapsible-header custom-collection-header" style="width:75%;float:left;">
                        <div class="row margin0">
                            <div class="col s12">
                                <div class="col s6">
                                    <label>{{ro.requestUnit.name}} (RO No.: {{ro.id}})</label>
                                </div>
                                <div class="col s6 right-align">
                                    <span>
                                        <strong>Created On: </strong>
                                        {{ro.generationTime | date:'dd/MM/yyyy'}}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="right" style="line-height:55px;">
                        <button ng-print print-element-id="print-{{ro.id}}" class="btn" tooltipped data-tooltip="Print"><i style="line-height: 1rem;margin-right: 0;" class="fa fa-print"></i></button>
                        <button class="btn" data-ng-click="downloadRequestOrder(ro)">Download</button>
                    </div>
                    <div class="collapsible-body">
                        <div class="row">
                            <table class="bordered striped" data-ng-if="ro.requestOrderItems.length > 0">
                                <tr>
                                    <th>Product Id</th>
                                    <th>Product Name</th>
                                    <th>Requested Quantity</th>
                                    <th>Unit Of Measure</th>
                                    <th>Total Packaging Qty</th>
                                    <th>Packaging UOM</th>
                                </tr>
                                <tr data-ng-repeat="roi in ro.requestOrderItems track by $index">
                                    <td>{{roi.productId}}</td>
                                    <td>{{roi.productName}}</td>
                                    <td>{{roi.requestedAbsoluteQuantity}}</td>
                                    <td>{{roi.unitOfMeasure}}</td>
                                    <td>{{roi.requestedAbsoluteQuantity/productPackagingMappings[roi.productId].conversionRatio}}</td>
                                    <td>{{productPackagingMappings[roi.productId].packagingName}}</td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!--   printable RO section   -->
                    <div style="padding: 20px 50px; width:100%; display: none;" id="print-{{ro.id}}">
                        <p class="center-align" style="font-size: 24pt;font-weight: 700;">Request Order</p>
                        <p style="font-size: 20pt;">Request Order ID: {{ro.id}} </p>
                        <p style="font-size: 20pt;">Requesting Unit: {{ro.requestUnit.name}} </p>
                        <p style="font-size: 20pt;">Fulfillment Unit: {{ro.fulfillmentUnit.name}}</p>
                        <p style="font-size: 20pt;">Created On:  {{ro.generationTime | date:'dd/MM/yyyy'}}</p>
                        <table style="border:1px solid #000;width:100%" align="center">
                            <tr style="background-color: black;color:white;font-size: 12pt;font-weight: 700;">
                                <th style="border-radius: 0px;text-align:center;">Product ID</th>
                                <th style="border-radius: 0px;text-align:center;">Product Name</th>
                                <th style="border-radius: 0px;text-align:center;">Requested Qty</th>
                                <th style="border-radius: 0px;text-align:center;">UOM</th>
                                <th style="border-radius: 0px;text-align:center;">Packaging Qty</th>
                                <th style="border-radius: 0px;text-align:center;">Packaging UOM</th>
                            </tr>
                            <tr data-ng-repeat="roi in ro.requestOrderItems track by $index"
                                style="border-bottom: #000 1px solid; font-size:12pt; page-break-inside: avoid;">
                                <td style="text-align:center;border-right: 1px dashed #000;">{{roi.productId}}</td>
                                <td style="text-align:center;border-right: 1px dashed #000;">{{roi.productName}}</td>
                                <td style="text-align:center;border-right: 1px dashed #000;">{{roi.requestedAbsoluteQuantity}}</td>
                                <td style="text-align:center;border-right: 3px solid #000;">{{roi.unitOfMeasure}}</td>
                                <td style="text-align:center;">
                                    {{roi.requestedAbsoluteQuantity/productPackagingMappings[roi.productId].conversionRatio}}
                                </td>
                                <td style="text-align:left;">
                                    {{productPackagingMappings[roi.productId].packagingName}}
                                </td>
                            </tr>
                        </table>
                        <div style="width:100%;margin-top:40px;">
                            <div style="width:49%;display: inline-block;">
                                <p>Packed By:  ______________________</p>
                                <p>Security Guard:  ______________________</p>
                                <p>Date of Packing Finished:  ______________________</p>
                                <p>Time of Packing Finished:  ______________________</p>
                            </div>
                            <div style="width:49%;display: inline-block;">
                                <div style="width:100%;">
                                    <p style="text-align:center;margin:0;border: #000 1px solid;font-size:12pt;font-weight:700; padding:5px; page-break-inside: avoid;">Units Packed</p>
                                    <p style="text-align:left;margin:0;border: #000 1px solid; font-size:12pt;padding:5px; page-break-inside: avoid;">Big Crate: </p>
                                    <p style="text-align:left;margin:0;border: #000 1px solid; font-size:12pt;padding:5px; page-break-inside: avoid;">Medium Crate: </p>
                                    <p style="text-align:left;margin:0;border: #000 1px solid; font-size:12pt;padding:5px; page-break-inside: avoid;">Small Crate: </p>
                                    <p style="text-align:left;margin:0;border: #000 1px solid; font-size:12pt;padding:5px; page-break-inside: avoid;">Loose Box: </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
    </div>
</div>



