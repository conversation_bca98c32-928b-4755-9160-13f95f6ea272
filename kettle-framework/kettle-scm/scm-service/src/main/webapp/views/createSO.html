<style>
    .amount-rows{
        padding: 5px;
        font-size: 20px;
    }

    .modal-large{
       width:80% !important;
    }

    .modal-medium{
        width:50% !important;
    }
</style>
<div class="row" data-ng-init="init()">
    <div class="col s12">
        <div class="row white z-depth-3 custom-listing-li">
            <div class="col s12">
                <h4>Create Service Order</h4>
            </div>
            <div class="row">
                <div class="col s12 m6 l6">
                    <div class="row">
                        <div class="col s4">
                            <b>Select Vendor</b>
                        </div>
                        <div class="col s8">
                            <select ui-select2="selectedVendor" id="vendorList" name="vendorList" data-ng-model="selectedVendor"
                                    data-ng-options="vendor as vendor.name for vendor in vendorList track by vendor.id"
                                    data-ng-change="selectVendor(selectedVendor)"></select>
                        </div>
                    </div>
                </div>
                <div class="col s12 m6 l6" data-ng-if="locationList.length>0">
                    <div class="row">
                        <div class="col s4">
                            <b>Select Dispatch Location</b>
                        </div>
                        <div class="col s8">
                            <select id="locationList" name="locationList" data-ng-model="selectedLocation"
                                    data-ng-options="location as location.name for location in locationList track by location.id"
                                    data-ng-change="selectDispatchLocation(selectedLocation)"></select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col s12 m6 l6">
                    <div class="row">
                        <div class="col s4">
                            <b>Select Cost Center</b>
                        </div>
                        <div class="col s8">
                            <select ui-select2 id="centerList" name="centerList" data-ng-model="selectedCenter"
                                    data-ng-options="center as center.name for center in costCenters track by center.id"
                                    data-ng-change="selectCenter(selectedCenter)"></select>
                        </div>
                    </div>
                </div>
            </div>
            <hr>
            <div class="row" data-ng-if="selectedDispatchLocation.id != null">
                <div class="col s12">
                    <div class="row">
                        <div class="col s6">
                            <h5 style="margin-top:5px;">Add Items to Service Order</h5>
                        </div>
                    </div>
                </div>
                <div class="col s12">
                    <div class="row">
                        <div class="row margin10">
                            <div class="col s4">
                                <label>Select Element</label>
                                <select data-ng-model="selectedElement" data-ng-change="selectElement(selectedElement)"
                                        data-ng-options="element as element.name for element in elements track by element.id"></select>
                            </div>
                            <div class="col s8">
                                <button class="btn btn-small waves-effect waves-light right"
                                        data-ng-click="addItem(selectedElement)"> Add </button>
                            </div>
                        </div>
                        <div class="row margin10">
                            <div class="col s4">
                                <label>Description</label>
                                <input type="text" maxlength="500" data-ng-model="selectedElement.description">
                            </div>
                            <div class="col s2">
                                <label>Select UOM</label>
                                <select data-ng-model="selectedElement.pkg" data-ng-change="selectPkg(selectedElement.pkg)"
                                        data-ng-options="pkg as pkg for pkg in selectedElement.uoms track by (selectedElement.id + pkg)"></select>
                            </div>
                            <div class="col s2">
                                <label>Select Quantity</label>
                                <input type="number" min="0" step="0.001"
                                       data-ng-change="calculateCost(selectedElement)"
                                       ng-model="selectedElement.qty">
                            </div>
                            <div class="col s2">
                                <label>Base Rate</label>
                                <input type="number" min="0" step="0.001"
                                       data-ng-change="calculateCost(selectedElement)"
                                       ng-model="selectedElement.price">
                            </div>
                            <div class="col s2">
                                <label>Tax Rate (in %)</label>
                                <input type="number" disabled min="0" step="0.001" ng-model="selectedElement.taxRate">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col s4">
                                <label>Select Periodicity</label>
                                <select data-ng-model="selectedElement.periodicity"
                                        data-ng-change="changePeriodicityText(selectedElement.periodicity)"
                                        data-ng-options="period as period for period in periods"></select>
                            </div>
                            <div class="col s3"
                                 data-ng-if="selectedElement.periodicity!=undefined && selectedElement.periodicity != 'ONE TIME'">
                                <label>Select Number of {{periodicityText}}</label>
                                <input type="number" min="1" step="1" ng-model="selectedElement.iterations">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col s12" data-ng-show="serviceItems!=null && serviceItems.length>0">
                    <div class="row">
                        <h4 class="right" style="margin-bottom: 5px;">Total Amount: Rs.{{calculateAmount()}}</h4>
                        <table class="highlight">
                            <thead>
                                <tr>
                                    <th>Cost Element</th>
                                    <th>Description</th>
                                    <th>Base Rate</th>
                                    <th>Tax Rate</th>
                                    <th>Quantity</th>
                                    <th>Cost</th>
                                    <th>Total Tax</th>
                                    <th>Total Amount</th>
                                    <th>Action</th>
                                </tr>
                            </thead>

                            <tbody>
                                <tr data-ng-repeat="item in serviceItems track by $index">
                                    <td tooltipped data-tooltip="SAC Code: {{item.ascCode}}, Tax:{{item.taxRate}}">{{item.costElementName}}</td>
                                    <td>
                                        <div data-ng-if="!item.edit">
                                            {{item.serviceDescription}}
                                            <button class="btn btn-xs-small"
                                                    data-ng-click="item.edit=true"
                                                    style="width:50px;!important;">Edit</button>
                                        </div>
                                        <div data-ng-if="item.edit">
                                            <input type="text" maxlength="500" data-ng-model="item.serviceDescription">
                                            <button class="btn btn-xs-small"
                                                    style="width:50px!important;"
                                                    data-ng-click="item.edit=false">OK</button>
                                        </div>
                                    </td>
                                    <td>{{item.unitPrice}}</td>
                                    <td>{{item.taxRate}}</td>
                                    <td>{{item.requestedQuantity}}</td>
                                    <td>{{item.totalCost}} </td>
                                    <td>{{item.totalTax}}</td>
                                    <td>{{item.amountPaid}}</td>
                                    <td><button class="btn" data-ng-click="removeItem(serviceItems,$index)">Remove</button></td>
                                </tr>
                            </tbody>
                        </table>
                        <div class="col s12 right">
                            <button class="btn" data-ng-click="submit()">SUBMIT</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>