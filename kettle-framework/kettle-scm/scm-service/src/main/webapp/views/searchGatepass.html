<style>
    .popeye-modal-container
    .popeye-modal {
        position: relative;
        text-align: left;
        vertical-align: middle;
        display: inline-block;
        width: 88%;
        border-radius: 3px;
        border: none;
        z-index: 1300;
        padding: 2em 1.5em 1.5em;
        background: #fff;
        box-shadow: 0 0 10px rgba(0, 0, 0, .4);
    }
</style>
<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="col s12">
        <div class="row">
            <div class="col s12">
                <h4 class="left">Gatepass Details</h4>
                <div data-ng-show="isDetailView" style="margin-top: 35px;">
                    <input type="button" class="btn right" value="BACK"
                           data-ng-click="isDetailView = false"/>
                    <button print-btn class="btn blue right" tooltipped
                            style="margin-right: 10px;" data-tooltip="Print">
                        <i class="fa fa-print"></i>
                    </button>
                    <!-- <button class="btn btn-primary"
                    data-ng-click="getTorqusTO(transferOrderDetail.id)" tooltipped
                    data-tooltip="Download">
                    <i class="fa fa-download"></i>
                </button> -->
                </div>
            </div>
        </div>
    </div>
    <div data-ng-if="!isDetailView">
        <div class="row">
            <div class="col s12 m6 l6">
                <label>Select Operation Type:</label> <select
                    data-ng-model="searchGatepass.operationType"
                    data-ng-options="ops.name as ops.label  for ops in operationList"
                    data-ng-change="operationCheck(searchGatepass.operationType)"
            >
            </select>
            </div>
            <div class="col s12 m6 l6">
                <label>Select Status :</label> <select
                    data-ng-model="searchGatepass.status"
                    data-ng-options="status as status  for status in statusList"></select>
            </div>
            <div class="col s2 m6 l6"  data-ng-if="showVendorFilter">
                <label>Select Vendor</label>
                <select id="vendors" ui-select2="{allowClear:true, placeholder: 'Select Vendor'}"
                        ng-options="vendor as vendor.name for vendor in vendors"
                        data-ng-change="searchGatepass.vendorSelected = vendorSelected.id"
                        data-ng-model="vendorSelected"></select>
            </div>
        </div>
        <div class="row">
            <div class="col s4 m4 l4">
                <label for="startDate">Start Date:</label> <input input-date
                                                                  type="text" id="startDate"
                                                                  data-ng-model="searchGatepass.startDate"
                                                                  container="" format="yyyy-mm-dd"/>
            </div>
            <div class="col s4 m4 l4">
                <label for="endDate">End Date :</label> <input input-date
                                                               type="text" id="endDate"
                                                               data-ng-model="searchGatepass.endDate" container=""
                                                               format="yyyy-mm-dd"/>
            </div>
            <div class="col s4 m4 l4">
                <label for="gatePassId">Gate Pass Id :</label>
                <input
                       type="number" id="gatePassId" data-ng-model="searchGatepass.gatePassId"
                />
            </div>

            <div class="col s4 m4 l4">
                <label>Search :</label> <input type="button" class="btn"
                                               value="Search" data-ng-click="getGatepass()"/>
            </div>
        </div>

        <div class="col s3 right" data-ng-if="gatepass.length > 0">
            <button class="btn margin-top-20" data-ng-click="downloadExcell()" >Download Excell</button>
        </div>

        <div class="row">
            <div class="col s12 refOrderListTable"
                 data-ng-show="gatepass.length > 0">
                <ul class="collection menuItemList">
                    <li class="collection-item z-depth-1 list-head">
                        <div class="row">
                            <div class="col s2">Operation Type</div>
                            <div class="col s2">Generation Time</div>
                            <div class="col s2">Status</div>
                            <div class="col s3">Generated For</div>
                            <div class="col s3">Action</div>
                        </div>
                    </li>
                    <li class="z-depth-1"
                        data-ng-repeat="gate in gatepass track by gate.id"
                        style="margin-top: 15px;">
                        <div class="row" style="min-height: 40px;padding: 10px;">
                            <div class="col s2 clickable"
                                 data-ng-click="gatepassDetail(gate, 'View')">
                                <span style="border-bottom: 1px dashed #000">{{gate.operationType}}</span>
                            </div>
                            <div class="col s2">
								<span>{{gate.createdAt |
									date:'dd-MM-yyyyhh:mm:ss':'+0530'}}</span>
                            </div>
                            <div class="col s2">{{gate.status}}</div>
                            <div class="col s3">{{gate.vendor.name}}</div>
                            <div class="col s3"
                                 data-ng-if="gate.status !='CANCELLED' && gate.status !='CLOSED'">
                                <button class="btn btn-small green"
                                        data-ng-click="gatepassDetail(gate, 'Return')">Return
                                </button>
                                <button class="btn btn-small red" data-ng-show="!gate.assetGatePass"
                                        data-ng-click="gatepassDetail(gate, 'Lost')">Lost
                                </button>
                                <button data-ng-click="gatepassDetail(gate, 'Cancel')"
                                        tooltipped data-tooltip="Cancel Gatepass"
                                        class="btn btn-floating red"
                                        data-ng-show="getGatepassCancelStatus(gate.itemDatas) && !gate.assetGatePass">
                                    <i class="material-icons">close</i>
                                </button>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </div>
    <div class="col s12" data-ng-show="isDetailView">
        <div class="row sku-pkg-row">
            <div class="col s3">
                <label>Gatepass Id:</label>{{selectedGatepass.id}}
            </div>
            <div class="col s3">
                <label>Vendor :</label>{{selectedGatepass.vendor.name}}
            </div>
            <div class="col s3">
                <label>Operation Type:</label>{{selectedGatepass.operationType}}
            </div>
            <div class="col s3">
                <label>Status:</label>{{selectedGatepass.status}}
            </div>
        </div>
        <div class="row sku-pkg-row">
            <div class="col s3">
                <label>Generation Time:</label>{{selectedGatepass.createdAt |
                date:'dd-MM-yyyy hh:mm:ss':'+0530'}}
            </div>
            <div class="col s3">
                <label>Generated By:</label>{{selectedGatepass.createdBy.name}}
            </div>
            <div class="col s3" data-ng-show="selectedGatepass.comment != null">
                <label>Comment</label>{{selectedGatepass.comment}}
            </div>
            <div class="col s3">
                <label>Sending Unit:</label>{{selectedGatepass.sendingUnit.name}}
            </div>
            <!-- <div class="col s3"
                data-ng-show="selectedGatepass.returnable == true">
                <label>Expected Return Days</label>{{selectedGatepass.expectedReturn}}
            </div> -->
            <!-- 	<div class="col s4"
                data-ng-show="selectedGatepass.additionalCharges != null">
                <label>Additional Charges</label><br />{{selectedGatepass.additionalCharges}}
            </div> -->
        </div>
        <div class="row sku-pkg-row"
             data-ng-show="selectedGatepass.status == 'CANCELLED'">
            <div class="col s3">
                <label>Cancelled By:</label>{{selectedGatepass.cancelledBy.name}}
            </div>
            <div class="col s3">
                <label>Cancelled At :</label>{{selectedGatepass.cancelledAt |
                date:'dd-MM-yyyy hh:mm:ss':'+0530'}}
            </div>
        </div>
        <ul class="collection menuItemList z-depth-1-half"
            style="margin-bottom: 50px">
            <li class="collection-item z-depth-1 list-head">
                <div class="row">
                    <div class="col s2">Sku Name</div>
                    <div class="col s2">Transferred Qty</div>
                    <div class="col s2">Available Qty</div>
                    <div class="col s2">Unit Of Measure</div>
                    <div class="col s2">Transaction Type</div>
                    <div class="col s2"
                         data-ng-if="(actionType != 'View' && actionType != 'Cancel') && (selectedGatepass.status !='CANCELLED' && selectedGatepass.status !='CLOSED')">
                        {{actionType}}
                        Quantity
                    </div>
                </div>
            </li>
            <li
                    data-ng-repeat="item in selectedGatepass.itemDatas track by item.id" gatePassItemAssetId
                    style="margin-top: 15px;">
                <div class="row"
                     style="padding: 10px; background: #eee; border-bottom: #ddd 1px solid;">
                    <div class="col s2">
                        <a data-ng-click="showPreview($event, item.sku.id,'SKU')">{{item.sku.name}}</a>
                    </div>
                    <div class="col s2">{{item.quantity}}</div>
                    <div class="col s2">
                        <div data-ng-if="item.transType == 'TRANSFER'">
                            {{skuQuantityDetails[selectedGatepass.id][item.sku.id].avlQty}}
                        </div>
                        <div data-ng-if="item.transType != 'TRANSFER'">
                            <a href="" data-ng-click="openDetail(item)">Detail</a>
                        </div>
                    </div>
                    <div class="col s2">{{item.uom}}</div>
                    <div class="col s2">{{item.transType}}</div>
                    <div class="col s2"
                         data-ng-if="item.transType =='TRANSFER' && (actionType != 'View' && actionType != 'Cancel') && (selectedGatepass.status !='CANCELLED' && selectedGatepass.status !='CLOSED')">
                        <input
                                data-ng-if="skuQuantityDetails[selectedGatepass.id][item.sku.id].avlQty > 0"
                                type="number" data-ng-model="item.returnQuantity"
                                ng-disabled="selectedGatepass.assetGatePass"
                                data-ng-change="updateQuantity(selectedGatepass.id, item.sku.id, item)"/>
                        <span
                                data-ng-if="skuQuantityDetails[selectedGatepass.id][item.sku.id].avlQty == 0">
							- </span>
                    </div>
                </div>
                <div class="row" style="margin: 16px;" data-ng-if="selectedGatepass.assetGatePass == true">
                    <div class="col s2" style="padding: 10px; border: 1px solid black;text-align: center;"
                         data-ng-repeat="asset in item.gatepassItemAssetMappings track by $index" data-ng-show="asset.isReturned!='Y' || actionType =='VIEW'">
                            {{asset.assetTagValue}} &nbsp;&nbsp;
                            <input
                                    id="{{asset.gatePassItemAssetId}}"
                                    data-ng-model="asset.fixed"
                                    data-ng-change="updateAssetQuantity(selectedGatepass.id, item.sku.id, item)"
                                    data-ng-if="actionType != 'View'"
                                    type="checkbox"/>
                            <label
                                    class="black-text"
                                    for="{{asset.gatePassItemAssetId}}">Fixed</label>
                    </div>
                </div>
            </li>
        </ul>
        <div class="row">
            <div class="col s2 form-element">
                <input type="button" class="btn" value="BACK"
                       data-ng-click="isDetailView = false"/>
            </div>
            <div class="col s10 right-align form-element"
                 data-ng-if="actionType != 'View' && (selectedGatepass.status !='CANCELLED' && selectedGatepass.status !='CLOSED')">
                <!-- <input type="button" class="btn" value="CANCEL" data-ng-click="cancelGR()" acl-action="RECPRR" /> -->
                <a class="btn" href="#actionTypePreview" modal>{{actionType}}</a>
            </div>
        </div>
    </div>
</div>

<div class="modal" id="actionTypePreview"
     style="width: 90%; max-height: 85%;">
    <div class="modal-content">
        <div class="col s12">
            <div class="row">
                <div class="col s12">
                    <h5 class="left">{{actionType}} Preview</h5>
                </div>
            </div>
        </div>
        <div class="col s12">
            <div class="row sku-pkg-row">
                <div class="col s2">
                    <label>Vendor :</label>{{selectedGatepass.vendor.name}}
                </div>
                <div class="col s2">
                    <label>Sending Unit:</label>{{selectedGatepass.sendingUnit.name}}
                </div>
                <div class="col s2">
                    <label>Operation Type:</label>{{selectedGatepass.operationType}}
                </div>
                <div class="col s2">
                    <label>Status:</label>{{selectedGatepass.status}}
                </div>
                <div class="col s2">
                    <label>Generation Time:</label>{{selectedGatepass.createdAt |
                    date:'dd-MM-yyyy hh:mm:ss':'+0530'}}
                </div>
                <div class="col s2">
                    <label>Generated By:</label>{{selectedGatepass.createdBy.name}}
                </div>
            </div>
            <ul class="collection menuItemList z-depth-1-half"
                style="margin-bottom: 50px">
                <li class="collection-item z-depth-1 list-head">
                    <div class="row">
                        <div class="col s3">Sku Name</div>
                        <div class="col s2">Transferred Quantity</div>
                        <div class="col s2">Unit Of Measure</div>
                        <div class="col s3">Transaction Type</div>
                        <div class="col s2" data-ng-if="actionType != 'Cancel'">{{actionType}}
                            Quantity
                        </div>
                    </div>
                </li>
                <li
                        data-ng-repeat="item in selectedGatepass.itemDatas track by item.id">
                    <div class="row"
                         style="padding: 10px; background: #eee; border-bottom: #ddd 1px solid;"
                         data-ng-if="actionType == 'Cancel' || (actionType != 'Cancel' && item.returnQuantity > 0)">
                        <div class="col s3">{{item.sku.name}}</div>
                        <div class="col s2">{{item.quantity}}</div>
                        <div class="col s2">{{item.uom}}</div>
                        <div class="col s3">{{item.transType}}</div>
                        <div class="col s2" data-ng-if="actionType != 'Cancel'">
                            {{item.returnQuantity}}
                        </div>
                    </div>
                </li>
            </ul>
            <div class="row">
                <div class="col s2 form-element">
                    <!--  <input type="button" class="btn" value="BACK" data-ng-click="cancelpreviewGR()" /> -->
                    <a class="modal-action modal-close waves-effect btn"
                       href="javascript:void(0)">BACK</a>
                </div>
                <div class="col s10 right-align form-element">
                    <a class="modal-action modal-close waves-effect btn"
                       href="javascript:void(0)" data-ng-click="settleGatepass()">SUBMIT</a>
                    <!-- <input type="button" class="btn" value="SUBMIT" />  -->
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/ng-template" id="detailModal.html">
    <div class="modal-content">
        <div class="col s12">
            <div class="row">
                <div class="col s12">
                    <h5 class="left">{{selectedgatepassItem.transType}} Preview</h5>
                </div>
            </div>
            <ul class="collection menuItemList z-depth-1-half"
                style="margin-bottom: 50px">
                <li class="collection-item z-depth-1 list-head">
                    <div class="row">
                        <div class="col s3">Sku Name</div>
                        <div class="col s2">{{item.transType}} Quantity</div>
                        <div class="col s2">Unit Of Measure</div>
                        <div class="col s2">Created By</div>
                        <div class="col s3">Created On</div>
                    </div>
                </li>
                <li>
                    <div class="row"
                         style="padding: 10px; background: #eee; border-bottom: #ddd 1px solid;">
                        <div class="col s3">{{item.sku.name}}</div>
                        <div class="col s2">{{item.quantity}}</div>
                        <div class="col s2">{{item.uom}}</div>
                        <div class="col s2">{{item.createdBy.name}}</div>
                        <div class="col s3" data-ng-if="actionType != 'Cancel'">
                            {{item.createdAt | date:'dd-MM-yyyy hh:mm:ss':'+0530'}}
                        </div>
                    </div>
                </li>
            </ul>


            <div class="row">
                <button class="btn red" style="float: right;"
                        data-ng-click="cancel()">Close
                </button>
            </div>
        </div>
    </div>
</script>

<div style="width: 100%;" id="printSection">

    <div class="row">
        <!--   print-only  -->
        <div class="col s12">
            <p style="text-align: center;">
                <b><span
                        style="font-size: 16.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">CHAAYOS<br/>
					</span></b><b><span
                    style="font-size: 13.0pt; line-height: 115%; font-family: 'Cambria', serif;">{{companyMap[selectedGatepass.sendingCompany.id].name}}<br/>
					</span></b><span
                    style="font-size: 10.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">{{companyMap[selectedGatepass.sendingCompany.id].registeredAddress.line1}},
						{{companyMap[selectedGatepass.sendingCompany.id].registeredAddress.line2}}, <br/> {{companyMap[selectedGatepass.sendingCompany.id].registeredAddress.city}},
						{{companyMap[selectedGatepass.sendingCompany.id].registeredAddress.state}}, <br/> {{companyMap[selectedGatepass.sendingCompany.id].registeredAddress.country}},
						{{companyMap[selectedGatepass.sendingCompany.id].registeredAddress.zipCode}}<br/>
						</span><b><span style="font-size: 13.0pt; line-height: 115%; font-family: 'Cambria', serif;">GATEPASS</span></b>
                </span>
            </p>
            <table
                    style="border-collapse: collapse; border: none;"
                    cellpadding="0cm 5.4pt">
                <tbody>
                <tr style="height: 12.00pt; page-break-inside: avoid;">
                    <td
                            style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Sending Unit Details</span>
                        </p>
                    </td>
                    <td
                            style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                        <p style="margin: .0001pt 0; line-height: normal;">

                        </p>
                    </td>
                    <td
                            style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Receiving Vendor Details </span>
                        </p>
                    </td>
                    <td
                            style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                        <p style="margin: .0001pt 0; line-height: normal;">

                        </p>
                    </td>
                </tr>
                <tr style="height: 12.00pt; page-break-inside: avoid;">
                    <td
                            style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Unit Name</span>
                        </p>
                    </td>
                    <td
                            style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{selectedGatepass.sendingUnit.name}}</span>
                        </p>
                    </td>
                    <td
                            style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Unit Name </span>
                        </p>
                    </td>
                    <td
                            style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{vendorDetail.entityName}}</span>
                        </p>
                    </td>
                </tr>
                <tr style="height: 12.00pt; page-break-inside: avoid;">
                    <td
                            style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Company Name</span>
                        </p>
                    </td>
                    <td
                            style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{companyMap[selectedGatepass.sendingCompany.id].name}}</span>
                        </p>
                    </td>
                    <td
                            style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Company Name </span>
                        </p>
                    </td>
                    <td
                            style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{vendorDetail.companyDetails.companyName}}</span>
                        </p>
                    </td>
                </tr>
                <tr style="height: 12.00pt; page-break-inside: avoid;">
                    <td
                            style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Address</span>
                        </p>
                    </td>
                    <td
                            style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{unitData.address.line1}},
										{{unitData.address.line2}}, <br/>
										{{unitData.address.city}},
										{{unitData.address.state}}, <br/>
										{{unitData.address.country}},
										{{unitData.address.zipCode}}
									</span>
                        </p>
                    </td>
                    <td
                            style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Address </span>
                        </p>
                    </td>
                    <td
                            style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{dispatchDetail.address.line1}},
										{{dispatchDetail.address.line2}}, <br/>
										{{dispatchDetail.address.city}},
										{{dispatchDetail.address.state}}, <br/>
										{{dispatchDetail.address.country}},
										{{dispatchDetail.address.zipCode}}
									</span>
                        </p>
                    </td>
                </tr>
                <tr style="height: 12.00pt; page-break-inside: avoid;">
                    <td
                            style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: none; padding: 0cm 5.4pt; height: 12.00pt;">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">State/State Code</span>
                        </p>
                    </td>
                    <td
                            style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{unitData.location.state.name}}/{{unitData.location.state.code}}</span>
                        </p>
                    </td>
                    <td
                            style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">State/State Code</span>
                        </p>
                    </td>
                    <td
                            style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{dispatchDetail.address.state}}/{{dispatchDetail.address.stateCode}}</span>
                        </p>
                    </td>
                </tr>
                <tr style="height: 12.00pt; page-break-inside: avoid;">
                    <td
                            style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">GSTIN</span>
                        </p>
                    </td>
                    <td
                            style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{unitData.tin}}</span>
                        </p>
                    </td>
                    <td
                            style="width: 120.625px; border-top: 1pt solid windowtext; border-right: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-bottom: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">GSTIN</span>
                        </p>
                    </td>
                    <td
                            style="width: 250.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{dispatchDetail.gstin}}</span>
                        </p>
                    </td>
                </tr>
                <tr style="height: 12.00pt; page-break-inside: avoid;">
                    <td
                            style="width: 120.625px; border-right: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-top: none; padding: 0cm 5.4pt; height: 12.00pt;">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">CIN</span>
                        </p>
                    </td>
                    <td
                            style="width: 250.625px; border-top: none; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{companyMap[selectedGatepass.sendingCompany.id].cin}}</span>
                        </p>
                    </td>

                    <td
                            style="width: 120.625px; border-right: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-top: none; padding: 0cm 5.4pt; height: 12.00pt;">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Phone</span>
                        </p>
                    </td>
                    <td
                            style="width: 250.625px; border-top: none; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{vendorDetail.primaryContact}}</span>
                        </p>
                    </td>
                </tr>

                </tbody>
            </table>
            <p>
					<span
                            style="font-size: 10.0pt; line-height: normal; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
            </p>

            <p>
					<span
                            style="font-size: 10.0pt; line-height: normal; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
            </p>
            <table>
                <tbody>
                <tr style="height: 12.00pt; page-break-inside: avoid;">
                    <td
                            style="width: 386.625px; border-right: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: 1pt solid windowtext; border-image: initial; border-top: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;"
                            colspan="2">
                        <p style="margin: .0001pt 0; line-height: normal;">
                            <b><span
                                    style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Gatepass Details
											</span></b>
                        </p>
                    </td>
                    <td
                            style="width: 120.625px; border-top: 1pt solid windowtext; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 14.15pt;">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Transaction Type
										</span>
                        </p>
                    </td>
                    <td
                            style="width: 250.625px; border-top: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 14.15pt;">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{selectedGatepass.operationType}}</span>
                        </p>
                    </td>
                </tr>
                <tr style="height: 12.00pt; page-break-inside: avoid;">

                    <td
                            style="width: 120.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Gatepass ID
										</span>
                        </p>
                    </td>
                    <td
                            style="width: 250.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{selectedGatepass.id}}</span>
                        </p>
                    </td>
                    <td
                            style="width: 120.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: none; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Date</span>
                        </p>
                    </td>
                    <td
                            style="width: 250.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{selectedGatepass.createdAt
										| date:'dd-MM-yyyy':'+0530'}}</span>
                        </p>
                    </td>
                </tr>

                <tr style="height: 12.00pt; page-break-inside: avoid;">

                    <td
                            style="width: 120.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Place of supply
										</span>
                        </p>
                    </td>
                    <td
                            style="width: 250.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{dispatchDetail.address.city}}</span>
                        </p>
                    </td>
                    <td
                            style="width: 136.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;"></span>
                        </p>
                    </td>
                    <td
                            style="width: 193.625px; border-top: 1pt solid windowtext; border-left: none; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;"></span>
                        </p>
                    </td>
                </tr>
                <tr style="height: 12.00pt; page-break-inside: avoid;">

                    <td
                            style="width: 120.625px; border-top: none; border-left: 1pt solid windowtext; border-bottom: 1pt solid windowtext; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Total Items
										</span>
                        </p>
                    </td>
                    <td
                            style="width: 250.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 12.00pt;">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{selectedGatepass.itemDatas.length}}</span>
                        </p>
                    </td>
                    <td
                            style="width: 136.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 14.15pt;">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;"></span>
                        </p>
                    </td>
                    <td
                            style="width: 193.625px; border-top: none; border-bottom: 1pt solid windowtext; border-left: none; border-image: initial; border-right: 1pt solid windowtext; padding: 0cm 5.4pt; height: 14.15pt;">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;"></span>
                        </p>
                    </td>
                </tr>

                </tbody>
            </table>
            <p>
					<span
                            style="font-size: 10.0pt; line-height: normal; font-family: 'Cambria', serif; color: black;">&nbsp;</span>
            </p>
            <table
                    style="border-collapse: collapse; border: none;"
                    width="765">
                <tbody>
                <tr style="height: 12.00pt; page-break-inside: avoid;">
                    <td
                            style="width: 74.85pt; border: solid windowtext 1.0pt; border-left:  1pt solid windowtext; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                            width="100">
                        <p
                                style="margin: .0001pt 0; text-align: center; line-height: normal;">
                            <b><span
                                    style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Description</span></b>
                        </p>
                    </td>
                    <!--		<td
                                style="width: 46.1pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                                width="61">
                                <p
                                    style="margin: .0001pt 0; text-align: center; line-height: normal;">
                                    <b><span
                                        style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">HSN</span></b>
                                </p>
                            </td>-->

                    <td
                            style="width: 36.55pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                            width="49">
                        <p
                                style="margin: .0001pt 0; text-align: center; line-height: normal;">
                            <b><span
                                    style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Transferred Qty.</span></b>
                        </p>
                    </td>
                    <td
                            style="width: 36.7pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                            width="49">
                        <p
                                style="margin: .0001pt 0; text-align: center; line-height: normal;">
                            <b><span
                                    style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Unit</span></b>
                        </p>
                    </td>
                    <td
                            style="width: 50.5pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                            width="67">
                        <p
                                style="margin: .0001pt 0; text-align: center; line-height: normal;">
                            <b><span
                                    style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Price</span></b>
                        </p>
                    </td>
                    <td data-ng-repeat="t in selectedGatepass.itemDatas[0].taxes"
                        style="border-top: solid windowtext 1.0pt; border-left: none; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 14.15pt;"
                        width="{{132/selectedGatepass.itemDatas[0].taxes.length}}">
                        <p
                                style="margin: .0001pt 0; text-align: center; line-height: normal;">
                            <b><span
                                    style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{t.taxCode}}</span></b>
                        </p>
                    </td>
                    <td
                            style="width: 63.35pt; border: solid windowtext 1.0pt; border-left: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                            width="84">
                        <p
                                style="margin: .0001pt 0; text-align: center; line-height: normal;">
                            <b><span
                                    style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">Amount</span></b>
                        </p>
                    </td>

                </tr>
                <tr style="height: 12.00pt;  page-break-inside: avoid;"
                    data-ng-repeat="item in selectedGatepass.itemDatas | filter: {transType: 'TRANSFER' }">
                    <td
                            style="width: 74.85pt; border-left:  1pt solid windowtext; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                            width="100">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.sku.name}}</span>
                        </p>
                    </td>
                    <!--<td
                        style="width: 46.1pt; border: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                        width="61">
                        <p style="margin: .0001pt 0; line-height: normal;">
                            <span
                                style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.code}}</span>
                        </p>
                    </td>-->

                    <td
                            style="width: 36.55pt; border: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                            width="49">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.quantity}}</span>
                        </p>
                    </td>
                    <td
                            style="width: 36.7pt; border: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                            width="49">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.uom}}</span>
                        </p>
                    </td>
                    <td
                            style="width: 50.5pt; border: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                            width="67">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.price
										| currency :'': 2}}</span>
                        </p>
                    </td>
                    <td data-ng-repeat="t in item.taxes"
                        style="border: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                        width="{{132/item.taxes.length}}">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{t.taxAmount}} ({{t.taxPercentage}}%)</span>
                        </p>
                    </td>
                    <td
                            style="width: 63.35pt; border: none; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                            width="84">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{item.cost | currency :'': 2}}</span>
                        </p>
                    </td>
                </tr>
                <tr style="height: 12.00pt; page-break-inside: avoid;">
                    <td
                            style="width: 507.3pt; border: solid windowtext 1.0pt; border-top: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                            colspan="7" width="676">
                        <p style="margin: .0001pt 0; line-height: normal;">
                            <b><span
                                    style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">TOTAL</span></b>
                        </p>
                    </td>
                    <td
                            style="width: 66.8pt; border-top: solid windowtext 1.0pt; border-left: none; border-bottom: solid windowtext 1.0pt; border-right: solid windowtext 1.0pt; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                            width="89">
                        <p style="margin: .0001pt 0; line-height: normal;">
									<span
                                            style="font-size: 10.0pt; font-family: 'Cambria', serif; color: black;">{{totalPrice
										| currency :'': 2}}</span>
                        </p>
                    </td>
                </tr>
                <tr style="height: 12.00pt; page-break-inside: avoid;">
                    <td
                            style="border: solid windowtext 1.0pt; border-top: none; padding: 0cm 2.5pt 0cm 2.5pt; height: 12.00pt;"
                            colspan="8" width="765">
                        <p style="margin: .0001pt 0; line-height: normal;">
                            <b><span
                                    style="font-size: 10.0pt; font-family: 'Cambria', serif; text-align: left;">Total Value (In Words)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></b>
                            <b><span
                                    style="font-size: 12.0pt; font-family: 'Cambria', serif; text-align: right;">{{totalPriceInWords}}</span></b>
                        </p>
                    </td>
                </tr>
                </tbody>
            </table>
            <p>
					<span
                            style="font-size: 10.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">Reg. Address: {{companyMap[selectedGatepass.sendingCompany.id].registeredAddress.line1}},
					{{companyMap[selectedGatepass.sendingCompany.id].registeredAddress.city}},
					{{companyMap[selectedGatepass.sendingCompany.id].registeredAddress.state}},
					{{companyMap[selectedGatepass.sendingCompany.id].registeredAddress.country}}, Pin No:
					{{companyMap[selectedGatepass.sendingCompany.id].registeredAddress.zipCode}}</span>
            </p>
        </div>
    </div>
</div>
