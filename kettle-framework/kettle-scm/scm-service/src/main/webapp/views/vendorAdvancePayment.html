<div class="row white z-depth-3 " data-ng-init="init()">
  <div class="col s12">
    <div class="row white z-depth-3 teal lighten-2 center-align">
      <div class="col s12">
        <h4 class="left">Vendor Advance Payment</h4>
      </div>
    </div>
  </div>

  <div class="row">
    <div class="col s2">
      <label>Start date*</label>
      <input input-date type="text" data-ng-model="startDate" container="" format="yyyy-mm-dd" />
  </div>
  <div class="col s2">
      <label>End date*</label>
      <input input-date type="text" data-ng-model="endDate" container="" format="yyyy-mm-dd" />
  </div>
    <div class="col s2">
      <label>Advance Payment Id</label>
      <input type="number" data-ng-model="advancePaymentId"/>
    </div>
    <div class="col s2">
      <label>Select Vendor*</label>
      <select ui-select2="{placeholder: 'Select Vendor *'}" id="selectedVendor" name="selectedVendor" data-ng-model="selectedVendor"
              data-ng-change="setSelectedVendor(selectedVendor)"
              data-ng-options="vendor as vendor.name for vendor in vendorList track by vendor.id"></select>
    </div>
    <div class="col s2">
      <label>Select Status</label>
      <select ui-select2="{placeholder: 'Select Status *'}" id="selectedStatus" name="selectedStatus" data-ng-model="selectedStatus"
              data-ng-change="setSelectedVendor(selectedVendor)"
              data-ng-options="status as status for status in statusList"></select>
    </div>
    <div class="col s2">
      <button class="btn btn-medium" data-ng-click="getVendorAdvances()" style="margin-top: 15px;">Find</button>
    </div>
  </div>

  <div class="row">
    <div class="col s12" data-ng-if="(selectedVendor != null || advancePaymentId != null) && listOfAdvances.length > 0">
      <div class="col s12 grid"
           id="grid"
           ui-grid="gridOptions"
           ui-grid-save-state
           ui-grid-edit
           ui-grid-exporter
           ui-grid-resize-columns
           ui-grid-move-columns
           ui-grid-auto-resize>
      </div>
    </div>
    <div data-ng-if="(selectedVendor != null || advancePaymentId != null) && listOfAdvances.length == 0" style="padding: 10px; margin: 10px; border-radius: 4px; background: #a0e1e4;">No vendor Advances Found For the Selected Vendor..!</div>
  </div>

</div>

<script type="text/ng-template" id="viewLogs.html">
  <div class="ui-grid-cell-contents">
      <button class="btn btn-xs-small" style="overflow:hidden;" data-ng-if="row.entity.vendorAdvancePaymentAuditLogs.length > 0" data-ng-click="grid.appScope.setRoRow(row.entity)">
        View Usage
      </button>
      <span data-ng-if="row.entity.vendorAdvancePaymentAuditLogs.length == 0">-</span>
  </div>
</script>

<script type="text/ng-template" id="viewStatusLogsModalGrid.html">
  <div class="ui-grid-cell-contents">
      <button class="btn btn-xs-small" style="overflow:hidden;" data-ng-if="row.entity.vendorAdvancePaymentStatusLogs.length > 0"
              data-ng-click="grid.appScope.openStatusLogs(row.entity)" data-target='viewStatusLogsModal' modal>
        Status Logs
      </button>
      <span data-ng-if="row.entity.vendorAdvancePaymentStatusLogs.length == 0">-</span>
  </div>
</script>

<script type="text/ng-template" id="refundButton.html">
  <div class="ui-grid-cell-contents">
      <button class="btn btn-xs-small" style="overflow:hidden;" data-ng-if="row.entity.advanceStatus == 'REFUND_APPROVED'"
              data-ng-click="grid.appScope.openRefundModal(row.entity, false, false)">
        Refund Actions
      </button>
    <button class="btn btn-xs-small" style="overflow:hidden;" data-ng-if="row.entity.advanceStatus == 'REFUND_INITIATED' || row.entity.advanceStatus == 'REFUNDED'"
              data-ng-click="grid.appScope.openRefundModal(row.entity, true, false)">
        Refund Details
      </button>
    <button class="btn btn-xs-small" style="overflow:hidden;" data-ng-if="row.entity.adjustedWith != null"
              data-ng-click="grid.appScope.openRefundModal(row.entity, false, true)">
        Adjust Details
      </button>
      <span data-ng-if="row.entity.refundDate == null">-</span>
  </div>
</script>

<script type="text/ng-template" id="viewLogsModal.html" class="modal-large" >
  <div class="modal-content">
    <div class="row">
      <h5>Usage Of Advance Payment for vendor : <b>{{selectedVendor.name}}</b></h5>
    </div>
    <div class="row" style="width:98%;">
      <div class="col s12" data-ng-if="selectedRow.vendorAdvancePaymentAuditLogs.length > 0">
        <div class="col s12 grid"
             id="gridLogs"
             ui-grid="gridOptionsLogs"
             ui-grid-save-state
             ui-grid-edit
             ui-grid-exporter
             ui-grid-resize-columns
             ui-grid-move-columns
             ui-grid-auto-resize>
        </div>
      </div>

      <div data-ng-if="selectedRow.vendorAdvancePaymentAuditLogs.length == 0" style="padding: 10px; margin: 10px; border-radius: 4px; background: #a0e1e4;">No vendor Advances Logs Found For the Selected Vendor..!</div>

    </div>
    <div class="modal-footer">
      <button class="btn btn-medium red left" data-ng-click="close()">Close</button>
    </div>
  </div>
</script>

<div id="viewStatusLogsModal" class="modal">
  <div class="modal-content">
    <h4>Advance Payment Status Logs detail</h4>
    <div class="col s12">
      Status logs:
      <ul>
        <li data-ng-repeat="log in selectedRow.vendorAdvancePaymentStatusLogs"
            style="background: #78ece8;border:#06b5ab 1px solid;padding:5px;margin: 5px 0;">
          {{log.loggedAt | date:'dd-MM-yyyy hh:mm:ss a'}} : {{log.fromStatus}} to {{log.toStatus}} by {{log.loggedByName}}
        </li>
      </ul>
    </div>
    </div>
  <div class="modal-footer">
    <button class="btn btn-small modal-action modal-close">Close</button>
  </div>
</div>

<script type="text/ng-template" id="refundModal.html" class="modal-large" >
  <div class="modal-content" data-ng-if="!isAdjustView">
    <div class="row">
      <h5>Refund date of this Vendor Advance is : {{selectedRow.refundDate}} and Refund Initiated By : {{selectedRow.refundInitiatedByName}}</h5>
    </div>
    <div class="row">
      <div class="col s6">
        <div style="background: #efefef; border: #ddd 1px solid; padding: 10px;font-size: 14px;">
          <p class="center"><b><u>Advance Refund</u></b></p>
          <p><b>Refund Date :</b> {{selectedRow.refundDate}}</p>
          <p><b>Refund Amount :</b> {{selectedRow.availableAmount}}</p>
          <p data-ng-if="selectedRow.advanceStatus == 'REFUNDED'"><b>Refund Received By :</b> {{selectedRow.lastUpdatedByName}}</p>
          <p data-ng-if="selectedRow.advanceStatus == 'REFUNDED'"><b>Refund Received Date :</b> {{selectedRow.lastUpdatedDate | date:'yyyy-MM-dd HH:mm:ss'}}</p>
        </div>
      </div>
    </div>
    <div class="row" data-ng-if="selectedRow.advanceRefundDocId != null">
      <button data-tooltip="Download Document" class="btn btn-medium" data-ng-click="downloadRefundDocById(selectedRow.advanceRefundDocId)">Download Refund Doc</button>
    </div>
    <div class="row" data-ng-if="!isRefundView">
      <label for="refundedDate">Select refund received Date*</label>
      <input input-date type="text" name="refundedDate" id="refundedDate"
             ng-model="refundedDate"
             container="" format="yyyy-mm-dd"
             min="{{minRefundDate}}"
             data-ng-change="setRefundedDate(refundedDate)"/>
    </div>
    <div class="row" data-ng-if="!isRefundView">
      <div class="col s2">
        <button data-tooltip="Upload Document" class="btn btn-medium" data-ng-click="uploadRefundDoc()">Upload Document*</button>
      </div>
      <div class="col s2" data-ng-if="uploadedAdvanceRefund != null">
        <button data-tooltip="Upload Document" class="btn btn-medium" data-ng-click="downloadRefundDoc(uploadedAdvanceRefund)">Download</button>
      </div>
    </div>
    </div>

  <div class="modal-content" data-ng-if="isAdjustView">
    <div style="background: #efefef; border: #ddd 1px solid; padding: 10px;font-size: 14px;" class="row">
      <p class="center"><b><u>Adjustment Details</u></b></p>
      <p><b>Advance Amount Adjusted Id :</b> {{selectedRow.adjustedWith.advancePaymentId}}</p>
      <p><b>Adjusted Amount :</b> {{selectedRow.adjustedWith.availableAmount}}</p>
      <p><b>Adjusted with {{selectedRow.adjustedWith.advanceType == 'SO_ADVANCE' ? 'SO' : 'PO'}} ID :</b> {{selectedRow.adjustedWith.advanceType == 'SO_ADVANCE' ? selectedRow.adjustedWith.soId : selectedRow.adjustedWith.poId}}</p>
      <p data-ng-if="selectedRow.refundDate != null"><b>Refund Date :</b> {{selectedRow.refundDate}}</p>
      <p data-ng-if="selectedRow.refundDate != null"><b>Refund Amount :</b> {{selectedRow.availableAmount}}</p>
    </div>
  </div>
    <div class="modal-footer margin-top-10">
      <br>
      <button class="btn btn-medium red left" data-ng-click="close(false)">Close</button>
      <button class="btn btn-medium right" data-ng-if="refundedDate != null" data-ng-click="submitRefundedDate(refundedDate)">Mark Refunded</button>
    </div>
</script>
