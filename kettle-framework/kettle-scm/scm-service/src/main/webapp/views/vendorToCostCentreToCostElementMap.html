<style>
.grid {
	width: 900px;
	height: 550px;
	padding: 0 !important;
}

.modalgrid {
	width: 900px;
	height: 220px;
	padding: 0 !important;
}

</style>
<div
	class="row white z-depth-3 custom-listing-li"
	data-ng-init="init()">
	<div class="col s12">
		<h3>Vendor To Cost Center To Cost Element Mapping</h3>
	</div>
	<div class="col s12 m12 l6 xl6" style="width: 100%">
		<!-- <div class="row margin0">
			<label class="black-text">Category</label> <select
				ui-select2="selectedCategoryType"
				data-ng-model="mappingType"
				data-ng-options="category as category.name for category in categories track by category.listDetailId"
				data-ng-change="getSubCategory(mappingType)">
			</select>
		</div>
		<div class="row margin0">
			<label class="black-text">Sub Category</label> <select
				ui-select2="selectedSubCategoryType"
				data-ng-model="subCat"
				data-ng-options="mapping as mapping.name for mapping in subCategories track by mapping.listTypeId"
				data-ng-change="getSubSubCategory(subCat)">
			</select>
		</div>
		<div class="row margin0">
			<label class="black-text">Sub Sub-Category</label> <select
				ui-select2="selectedMappingType"
				id="mappingTypeId"
				name="mappingType"
				data-ng-model="subSubCat"
				data-ng-options="mapping as mapping.name for mapping in subSubCategories track by mapping.listDataId"
				data-ng-change="getCostElement(subSubCat)">
			</select>
		</div> -->
		<div class="row margin0" >
			<label class="black-text">Vendors</label> <select
				ui-select2="selectedMappingType"
				id="mappingTypeId"
				name="mappingType"
				data-ng-model="selectedVendor"
				data-ng-options="vendor as vendor.name for vendor in vendors | orderBy : 'name' track by vendor.id"
				data-ng-change="getCostCentreList()"
				style="width: 100%;">
			</select>
		</div>
		<table>
			<tr style="width: 100%;" >
				<td style="width: 50%">
					<label class="black-text">Cost Center</label> <select
						ui-select2="selectedMappingType"
						id="mappingTypeId"
						name="mappingType"
						data-ng-model="selectedCostCenter"
						data-ng-options="costCentre as costCentre.name for costCentre in costCentreList | orderBy : 'name' track by costCentre.id"
						data-ng-change="searchMappings()" style="width: 100%">
				</select>
				</td>
				<td  style="width: 50%" >
					<label class="black-text" >Vendor To Clone</label>
					<select
						ui-select2="selectedMappingType"
						id="mappingTypeId"
						name="mappingType"
						data-ng-model="selectVendorIdToClone"
						data-ng-options="vendor as vendor.name for vendor in vendors | orderBy : 'name' track by vendor.id"
						style="width: 100%">
				</select>
				</td>
			</tr>
		</table>

	</div>
	<div
		class="col s12">
		<div class="row">
			<div class="col s6 m6 l6 xl6">
				<button
					data-ng-disabled="selectedCostCenter == null"
					data-ng-click="searchMappings()"
					class="btn left"
					class="btn left">Search</button>
			</div>
			<div class="col s6 m6 l6 xl6" >
				<button
						data-ng-click="getClonedPriceFromVendor()"
						class="btn right"
						>Clone Pricing</button>
			</div>
			<!-- <div class="col s6 m6 l6 xl6">
				<span
				data-ng-if="selectedCostCenter != 'undefined' && selectedCostCenter.id > 0"
				data-ng-click="openVendorToCostElementMapModal()"
					> <a href="#addNewMapping"
						class="btn right"
					   modal
						style="margin-right: 10px"><b>+</b> Add New Mapping</a>
				</span>
			</div> -->
		</div>
	</div>
	</div>
	<div
		class="col s12"
		data-ng-if="gridOptions.data != null">
		<div
			id="mappingsGrid"
			ui-grid="gridOptions"
			ui-grid-edit
			ui-grid-row-edit
			ui-grid-cellNav
			ui-grid-resize-columns
			ui-grid-move-columns
			class="grid col s12"></div>
	</div>
	<div
	data-ng-if="gridOptions.data == null"
	class="text-center-disabled">
	No mappings to display.
</div>
<script
	type="text/ng-template"
	id="statusChangeButton.html">
      <div class="ui-grid-cell-contents">
		<button
				data-ng-class="{'btn btn-xs-small':row.entity.status=='IN_ACTIVE', 'btn red btn-xs-small':row.entity.status=='ACTIVE'}"
			ng-click="grid.appScope.changeStatus(row.entity)"
			ng-if="row.entity.updatedPrice == null" >
			<span ng-if="row.entity.status=='IN_ACTIVE'">Activate</span>
			<span ng-if="row.entity.status=='ACTIVE'">Deactivate</span>
		</button>
		<button 
			class="btn btn-xs-small blue" 
			ng-click="grid.appScope.updateRow(row.entity)"
			ng-if="row.entity.update &&  row.entity.status!=null">
			<span>Update</span>
		</button>
		<button 
			class="btn btn-xs-small blue" 
			ng-click="grid.appScope.addRow(row.entity)"
			ng-if="row.entity.status==null && row.entity.currentPrice==0">
			<span>ADD</span>
		</button>
      </div>
</script>
<script
	type="text/ng-template"
	id="statusBatch.html">
      <div class="ui-grid-cell-contents">
		<span  
			class="span badge active"
			ng-if="row.entity.status === 'ACTIVE'">
			ACTIVE
		</span>
		<span 
			class="span badge inactive"
			ng-if="row.entity.status === 'IN_ACTIVE'">
			IN-ACTIVE
		</span>
      </div>
</script>