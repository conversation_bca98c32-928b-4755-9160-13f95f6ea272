<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style type="text/css">
    td,th{
        padding:10px 5px !important;
    }
    .select2.select2-container{width: 100% !important;}
    #images{
        text-align: center;
    }
    img#scanned {
        height: 400px; /** Sets the display size */
        margin-right: 12px;
        margin-top: 20px;
    }
    .advanceHeader {
        line-height: 32px;
        background: #ea9e13;
        padding: 3px 5px;
        font-weight: 700;
        border-radius: 3px;
    }
</style>

<div class="searchingCard row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="col s12" data-ng-show="(selectView==true && !advanceSelectionView) || (selectView==false && !advanceSelectionView)">
        <div class="row">
            <div class="col s12">
                <h5 class="left">Create Payment Request</h5>
            </div>
        </div>
    </div>

    <div data-ng-show="selectView==true && !advanceSelectionView">
        <div class="row">
            <div class="col s4" data-ng-if="isMilkPayment == false">
                <label>Select Request Type</label>
                <select data-ng-model="prRequestType" id="prReqType" data-ng-change="setPrType(prRequestType)" data-ng-options="item as item.name for item in prRequestTypes"></select>
            </div>
            <div class="col s8" data-ng-show="prRequestType.shortCode=='GR'">
                <label>Select GR Type</label>
                <span style="margin-right: 20px">
                    <input name="group1" id="t1" type="radio" ng-model="grType" data-ng-click="handleTypeSwitch(grType)"  value="INVOICE"/>
                    <label for="t1">Invoice</label>
                </span>
                <span style="margin-right: 20px" data-ng-if="isMilkPayment == false">
                    <input name="group1" id="t2" type="radio" ng-model="grType" data-ng-click="handleTypeSwitch(grType)"  value="DELIVERY_CHALLAN"/>
                    <label for="t2">Delivery Challan</label>
                </span>
                <span>
                    <input id="milkPayment" type="checkbox" data-ng-model="isMilkPayment" />
                    <label for="milkPayment">Is Milk Payment</label>
                </span>
            </div>

            <div class="col s4" data-ng-show="prRequestType.shortCode=='ADP'">
                <label for="AdpVendorList">Select Vendor*</label>
                <select ui-select2="{allowClear:true, placeholder: 'Select Vendor for Advance Payment*'}" id="AdpVendorList" name="AdpVendorList" data-ng-model="advancePaymentVendor"
                        data-ng-change="setAdvancePaymentVendor(advancePaymentVendor)"
                        data-ng-options="item as item.name for item in advancePaymentVendors track by item.id"></select>
            </div>
            <div class="col s4" data-ng-show="prRequestType.shortCode=='ADP' && advancePaymentVendor != null">
                <label for="AdpVendorList">Select Advance Type*</label>
                <select ui-select2="{allowClear:true, placeholder: 'Select Advance Type *'}" id="advanceTypeList" name="advanceTypeList" data-ng-model="advanceType"
                        data-ng-change="setAdvanceType(advanceType)"
                        data-ng-options="adv as adv for adv in advanceTypes"></select>
            </div>
        </div>

        <div data-ng-show="prRequestType.shortCode=='GR'">

            <div class="row" data-ng-show="grType!=null">
                <div class="col s2">
                    <label>Start date*</label>
                    <input input-date type="text" data-ng-model="startDate" container="" format="yyyy-mm-dd" />
                </div>
                <div class="col s2">
                    <label>End date*</label>
                    <input input-date type="text" data-ng-model="endDate" container="" format="yyyy-mm-dd" />
                </div>
                <div class="col s3">
                    <label>Select Unit*</label>
                    <select ui-select2="{allowClear:true, placeholder: 'Select Unit*'}" id="unitListx" name="unitListx" data-ng-model="selectedUnit"
                            data-ng-change="selectUnit(selectedUnit)"
                            data-ng-options="item as item.name for item in unitList track by item.id"></select>
                </div>
                <div class="col s3">
                    <label>Select Vendor*</label>
                    <select ui-select2="{allowClear:true, placeholder: 'Select Vendor*'}" id="vendorListx" name="vendorListx" data-ng-model="selectedVendor"
                            data-ng-change="setSelectedVendor(selectedVendor)"
                            data-ng-options="item as item.name for item in vendorList track by item.id"></select>
                </div>
                <div class="col s2">
                    <input type="button" class="btn" value="Find" data-ng-click="findGrs()" style="margin-top: 25px;" />
                </div>
            </div>

            <div class="row" data-ng-show="grs.length>0 && isMilkPayment == false">
                <div class="respTable col s12">
                    <table class="row table bordered striped" style="border: #ccc 1px solid;">
                        <thead>
                        <tr>
                            <th>GR Id</th>
                            <th>Company Name</th>
                            <th>Receiving Date</th>
                            <th>Bill Amount</th>
                            <th>Action</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr data-ng-repeat="gr in grs track by gr.id">
                            <td data-ng-click="">{{gr.id}}</td>
                             <td>{{companyMap[gr.companyId].name}}</td>
                            <td>{{gr.generationTime | date:'dd-MM-yyyy hh:mm:ss a':'+0530'}}</td>
                            <td>{{gr.billAmount}}</td>
                            <td data-ng-if="grType=='INVOICE'">
                                <input type="button" class="btn btn-small" value="Create PR" data-ng-if="blockedAdvancePayments.length == 0" data-ng-click="createPR(gr, false)">
                                <input type="button" acl-action="FPRA" class="btn btn-small" value="Force Create PR" data-ng-if="blockedAdvancePayments.length == 0" data-ng-click="createPR(gr, true)">
                                <input type="button" class="btn btn-small" value="Create PR" data-ng-if="blockedAdvancePayments.length > 0 && gr.vendorAdvancePayments != null
                                && checkBlockedAdvanceForPayment(gr)" data-ng-click="createPR(gr, false)">
                                <input type="button" acl-action="FPRA" class="btn btn-small" value="Force Create PR" data-ng-if="blockedAdvancePayments.length > 0 && gr.vendorAdvancePayments != null
                                && checkBlockedAdvanceForPayment(gr)" data-ng-click="createPR(gr, true)">
                            </td>
                            <td data-ng-if="grType=='DELIVERY_CHALLAN'">
                                <span data-ng-if="blockedAdvancePayments.length == 0">
                                    <input id="gr-{{gr.id}}" type="checkbox" data-ng-model="gr.checked" data-ng-change="disableGrSelection($event, gr,grs)" data-ng-disabled="gr.disabled"/>
                                    <label for="gr-{{gr.id}}">select</label>
                                </span>
                                <span data-ng-if="blockedAdvancePayments.length > 0 && gr.vendorAdvancePayments != null
                                    && checkBlockedAdvanceForPayment(gr)">
                                    <input id="grs-{{gr.id}}" type="checkbox" data-ng-model="gr.checked" data-ng-change="disableGrSelection($event, gr,grs)" data-ng-disabled="gr.disabled"/>
                                    <label for="grs-{{gr.id}}">select</label>
                                </span>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                    <input data-ng-if="grType=='DELIVERY_CHALLAN'" type="button" class="btn" value="Initiate Payment Request" data-ng-click="initiatePR(false)" style="margin-top: 25px;" />
                    <input data-ng-if="grType=='DELIVERY_CHALLAN'" type="button" class="btn" value="Force Initiate Payment Request" data-ng-click="initiatePR(true)" style="margin-top: 25px;" />
                </div>
            </div>
            <div data-ng-if="isMilkPayment == true && grs.length>0">
                <div class="respTable col s12">
                    <table class="row table bordered striped" style="border: #ccc 1px solid;">
                        <thead>
                        <tr>
                            <th>Id</th>
                            <th>Invoice Id</th>
                            <th>Generation Date</th>
                            <th>Invoice Amount</th>
                            <th>Action</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr data-ng-repeat="gr in grs track by gr.specializedOrderInvoiceId">
                            <td data-ng-click="">{{gr.specializedOrderInvoiceId}}</td>
                            <td>{{gr.invoiceId}}</td>
                            <td>{{gr.generationTime | date:'dd-MM-yyyy hh:mm:ss a':'+0530'}}</td>
                            <td>{{gr.goodsReceived.totalAmount}}</td>
                            <td  >
                                <input type="button" class="btn btn-medium" value="Create PR" data-ng-click="openModalForQty(gr)">
                                <input type="button" acl-action="FPRA" class="btn btn-medium" value="Download Invoice" data-ng-click="openInvoice(gr)">
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div data-ng-if="showNoGR" style="padding: 10px; margin: 10px; border-radius: 4px; background: #a0e1e4;">No GR with pending payment request found.</div>
        </div>
        <div data-ng-if="advancePaymentVendor != null && currentVendorAdvancePayment != null && advanceType == 'STAND_ALONE_ADVANCE'">
            <div class="row">
                <div style="padding: 10px; margin: 10px; border-radius: 4px; background: #a0e1e4;">Advance Payment already Exists..!</div>
                <table class="bordered striped">
                    <thead class="list-head">
                    <tr>
                        <th>Advance Payment Id</th>
                        <th>PR Id</th>
                        <th>Advance Status</th>
                        <th>PR Amount</th>
                        <th>Available Amount</th>
                        <th>Blocked Amount</th>
                        <th>Created By</th>
                        <th>Created At</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td style="text-align: center">{{currentVendorAdvancePayment.advancePaymentId}}</td>
                        <td style="text-align: center">{{currentVendorAdvancePayment.paymentRequestId}}</td>
                        <td style="text-align: center">{{currentVendorAdvancePayment.advanceStatus}}</td>
                        <td style="text-align: center">{{currentVendorAdvancePayment.prAmount}}</td>
                        <td style="text-align: center">{{currentVendorAdvancePayment.availableAmount}}</td>
                        <td style="text-align: center">{{currentVendorAdvancePayment.blockedAmount}}</td>
                        <td style="text-align: center">{{currentVendorAdvancePayment.createdBy}}</td>
                        <td style="text-align: center">{{currentVendorAdvancePayment.createdAt | date: "dd-MM-yyyy"}}</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div data-ng-if="advancePaymentVendor != null && advanceType != null && advanceType == 'STAND_ALONE_ADVANCE' && currentVendorAdvancePayment == null">
            <div class="row">
                <div class="col s4">
                    <label for="advanceAmount">Enter Advance Payment Amount*:</label>
                    <input type="number" id="advanceAmount" data-ng-model="advanceAmount">
                </div>
                <div class="col s4">
                    <button data-tooltip="Upload Document" class="btn btn-medium" data-ng-click="uploadDoc(true)">Upload Document*</button>
                </div>
                <div class="col s2" data-ng-if="advanceUploadedDoc != null">
                    <button data-tooltip="Upload Document" class="btn btn-medium" data-ng-click="downloadPRInvoice(advanceUploadedDoc)">Download*</button>
                </div>
                <div class="col s2" data-ng-if="advanceUploadedDoc != null">
                    <button class="btn btn-medium" data-ng-click="createAdvancePayment(advanceAmount)">Submit</button>
                </div>
            </div>
        </div>

        <div data-ng-if="advancePaymentVendor != null && advanceType != null && advanceType != 'STAND_ALONE_ADVANCE'">
            <h5 data-ng-if="pendingPoSo.length > 0">Please Select A {{advanceType == 'PO_ADVANCE' ? "Purchase" : "Service"}} Order </h5>
            <input type="text" data-ng-model="enterredText" placeholder="Enter to Search" data-ng-if="pendingPoSo.length > 0" data-ng-change="setEnterredText(enterredText)">
            <ul class="col s12" data-collapsible="accordion" watch data-ng-if="pendingPoSo.length > 0 && advanceType == 'PO_ADVANCE'">
                <li class="row" data-ng-repeat="po in pendingPoSo | filter : enterredText">
                    <div class="collapsible-header poNumber waves-effect waves-light lighten-5 s12">
                        <div class="left col s5" data-ng-click="$event.stopPropogation()">
                            <input id="RO-{{po.id}}" data-ng-model="po.checked" type="checkbox" data-ng-click="disablePosSelection($event,po,pendingPoSo)" ng-disabled="po.disable"/>
                            <label for="RO-{{po.id}}">{{po.receiptNumber}} created on {{po.generationTime | date:'dd/MM/yyyy @ h:mma'}} (PO ID : {{po.id}}) </label>
                        </div>
                        <div class="col s2" style="display: inline-block;">
                            {{po.orderType}}
                        </div>
                        <div class="col s2" style="display: inline-block;" data-ng-if="po.type != null">
                            {{po.type}}
                        </div>
                        <div class="col s2" style="display: inline-block;">
                            Rs. {{po.paidAmount}}
                        </div>
                        <div class="right col s1">
                            <i class="fa fa-caret-down right"></i>
                            <span class="chip right" style="margin-top: 7px;">{{po.status}}</span>
                        </div>
                    </div>
                    <div class="respTable collapsible-body">
                        <table class="bordered striped row">
                            <thead>
                            <tr>
                                <th class="center-align">SKU</th>
                                <th class="center-align">UOM</th>
                                <th class="center-align">Packaging</th>
                                <th class="center-align">Packaging Qty</th>
                                <th class="center-align">Conversion Ratio</th>
                                <th class="center-align">Total Qty</th>
                                <th class="center-align">Price</th>
                                <th class="center-align" tooltipped data-tooltip="Amount = (Price * Quantity) + Taxes">Amount*</th>
                                <th class="center-align">Taxes</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr data-ng-repeat="item in po.orderItems track by $index">
                                <td class="center-align"><a data-ng-click="showPreview($event, item.skuId,'SKU')">{{item.skuName}}</a></td>
                                <td class="center-align">{{item.unitOfMeasure}}</td>
                                <td class="center-align">{{item.packagingName}}</td>
                                <td class="center-align">{{item.packagingQty}}</td>
                                <td class="center-align">{{item.conversionRatio}}</td>
                                <td class="center-align">{{item.requestedQuantity.toFixed(2)}}</td>
                                <td class="center-align">{{item.unitPrice.toFixed(2)}}</td>
                                <td class="center-align">{{item.totalCost}}</td>
                                <td class="center-align">{{item.totalTax}}</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </li>
            </ul>
            <div data-ng-if="pendingPoSo.length == 0 && errorMessage == null" style="padding: 10px; margin: 10px; border-radius: 4px; background: #a0e1e4;">
                No Pending {{advanceType == "PO_ADVANCE" ? "Purchase orders" : "Service orders"}} Found to make Advance Payment
            </div>
            <div data-ng-if="pendingPoSo.length == 0 && errorMessage != null" style="padding: 10px; margin: 10px; border-radius: 4px; background: #a0e1e4;">
                {{errorMessage}}
            </div>
            <ul class="col s12" data-collapsible="accordion" watch data-ng-if="pendingPoSo.length > 0 && advanceType == 'SO_ADVANCE'">
                <li class="row" data-ng-repeat="so in pendingPoSo | filter : enterredText">
                    <div class="collapsible-header poNumber waves-effect waves-light lighten-5 s12">
                        <div class="left" data-ng-click="$event.stopPropagation()">
                            <input id="RO-{{so.id}}" data-ng-model="so.checked" type="checkbox" data-ng-click="disablePosSelection($event,so,pendingPoSo)" ng-disabled="so.disable"/>
                            <label for="RO-{{so.id}}">SO# {{so.id}} Created: {{so.generationTime | date:'dd/MM/yyyy'}}
                                for Rs.{{so.totalAmount.toFixed(2)}} (SO ID : {{so.id}})</label>
                        </div>
                        <div class="right">
                            <span class="chip">{{so.status}}</span>
                            <i class="fa fa-caret-down right"></i>
                        </div>
                    </div>
                    <div class="collapsible-body">
                        <table class="bordered striped">
                            <thead>
                            <tr>
                                <th class="center-align">Cost Element</th>
                                <th class="center-align">UOM</th>
                                <th class="center-align">Total Qty</th>
                                <th class="center-align">Price</th>
                                <th class="center-align" tooltipped
                                    data-tooltip="Amount = (Price * Quantity) + Taxes">Amount*
                                </th>
                                <th class="center-align">Taxes</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr data-ng-repeat="item in so.orderItems track by $index">
                                <td class="center-align">{{item.costElementName}}[{{item.ascCode}}]({{item.costElementDate| date :'dd-MM-yyyy'}} To {{item.costElementToDate | date:'dd-MM-yyyy'}})</td>
                                <td class="center-align">{{item.unitOfMeasure}}</td>
                                <td class="center-align">{{item.requestedQuantity.toFixed(6)}}</td>
                                <td class="center-align">{{item.unitPrice.toFixed(2)}}</td>
                                <td class="center-align">{{(item.totalCost + item.totalTax).toFixed(2)}}</td>
                                <td class="center-align">{{item.totalTax.toFixed(2)}}</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </li>
            </ul>
            <div class="row" data-ng-if="poSoSelected !=null">
                <div class="col s4">
                    <label for="advAmount">Enter Advance Payment Amount*:</label>
                    <input type="number" id="advAmount" data-ng-model="advanceAmount" data-ng-change="setAdvanceAmount(advanceAmount)">
                </div>
                <div class="col s2" style="margin-top: 15px;">
                    <button data-tooltip="Upload Document" class="btn btn-medium" style="margin-top: 15px;" data-ng-click="uploadDoc(true)">Upload Document*</button>
                </div>
                <div class="col s2" data-ng-if="advanceUploadedDoc != null" style="margin-top: 15px;">
                    <button data-tooltip="Upload Document" class="btn btn-medium" style="margin-top: 15px;" data-ng-click="downloadPRInvoice(advanceUploadedDoc)">Download</button>
                </div>
                <div class="col s2" style="margin-top: 15px;">
                    <button class="btn btn-medium" style="margin-top: 15px;" data-ng-click="createAdvancePayment(advanceAmount)">Submit</button>
                </div>
            </div>
            </div>
        </div>
    </div>

    <div data-ng-show="selectView==false && !advanceSelectionView">
        <div class="row">
            <div class="col s12">
                <input type="button" class="btn right" value="BACK" data-ng-click="backToSelectView()" />
            </div>
        </div>
        <div class="row">
            <div class="col s12">
                <span data-ng-if="grType=='INVOICE'" style="margin-right: 20px;"><b>Gr Number:</b> {{selectedGr.id}}</span>
                <span><b>Vendor:</b> {{selectedGr.generatedForVendor.name}}</span>
            </div>
        </div>
        <div class="row">
            <div class="col s12">
                <table class="table bordered striped" style="border: #ccc 1px solid;">
                    <thead>
                        <tr>
                            <th>Sku Id</th>
                            <th>Sku Name</th>
                            <th>Packaging</th>
                            <th>Quantity</th>
                            <th>Price</th>
                            <th>Amount</th>
                            <th>Tax</th>
                            <th>Total Tax</th>
                            <th>Total Amount</th>
                            <th data-ng-if="grType=='INVOICE'">Deviations</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr data-ng-repeat="item in paymentRequest.paymentInvoice.paymentInvoiceItems">
                            <td><a data-ng-click="showPreview($event, item.skuId,'SKU')">{{item.skuId}}</a></td>
                            <td>{{item.skuName}}[{{item.hsn}}]</td>
                            <td>{{item.packagingName}}</td>
                            <td>{{item.quantity}}</td>
                            <td>{{item.unitPrice}}</td>
                            <td>{{item.totalPrice}}</td>
                            <td><span data-ng-repeat="tax in item.taxes">{{tax.taxType}}@{{tax.taxPercentage}}%,</span></td>
                            <td>{{item.totalTax}}</td>
                            <td>{{item.totalAmount}}</td>
                            <td data-ng-if="grType=='INVOICE'">
                                <span data-ng-repeat="deviation in item.deviations" style="background:#efefef;border:#ddd 1px solid;border-radius: 2px;">
                                    {{deviation.paymentDeviation.deviationDetail}}: {{deviation.deviationRemark}}
                                    <span style="cursor: pointer;" data-ng-click="removeDeviation(item.deviations, $index)">&times</span>
                                </span>
                                <button style="width:30px;" class="btn btn-small" data-ng-click="setAvailableDeviations(item,'INVOICE_ITEM')"
                                        data-target="itemDevModal" modal>&plus;</button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="row">
            <div class="col s6">
                <div style="background: #efefef; border: #ddd 1px solid; padding: 10px;font-size: 14px;">
                    <p><b>Created By:</b> {{createdByUser.name}}</p>
                    <p><b>Requesting Unit:</b> {{createdByUnit.name}}</p>
                    <p><b>Basic Amount:</b> {{paymentRequest.paymentInvoice.calculatedInvoiceAmount}}</p>
                    <p><b>Other Charges:</b> {{paymentRequest.paymentInvoice.extraCharges}}</p>
                    <p><b>Total Bill Amount:</b> {{paymentRequest.paymentInvoice.paymentAmount}}</p>
                    <p><b>Proposed Amount:</b> {{paymentRequest.proposedAmount}}</p>
                </div>
            </div>
            <div class="col s6" data-ng-if="grType=='INVOICE'">
                <p><label>Invoice Number:</label> <input type="text" data-ng-model="paymentRequest.paymentInvoice.invoiceNumber" /></p>
                <p><label>Invoice Date:</label> <input input-date type="text" ng-model="paymentRequest.paymentInvoice.invoiceDate"
                                                       container="" format="yyyy-mm-dd" max="{{maxDate}}" /></p>
                <p>
                    <span>
                    	<input id="companyInvoiceUploadId" type="checkbox" data-ng-model="companyConfirmed" data-ng-disabled ="uploadedDocData!=null" />
	                    <label for="companyInvoiceUploadId" data-ng-class="{'stpl-data': selectedGr.companyId ==1000, 'dkc-data': selectedGr.companyId == 1001}">Attach Invoice Of {{companyMap[selectedGr.companyId].name}} </label>
                	</span>
                    <span data-ng-if="companyConfirmed">
	                    <input type="button" value="Scan Doc" class="btn btn-small" data-target='scanModal' modal data-ng-click="resetScanModal()" />
	                    <input type="button" value="Snapshot" class="btn btn-small" data-target='snapModal' modal modal data-ng-click="resetSnapModal()" />
	                    <input type="button" value="Upload File" data-ng-click="uploadDoc(false)" class="btn btn-small" />
                        <input type="button" value="Preview Invoice" data-ng-if="uploadedDocData!=null"
                               data-ng-click="previewPRInvoice(uploadedDocData)" class="btn btn-small" data-target="invoicePreviewModal" modal />
                        <input type="button" value="Download Invoice" data-ng-if="uploadedDocData!=null"
                               data-ng-click="downloadPRInvoice(uploadedDocData)" class="btn btn-small" />
                    </span>
                </p>
            </div>
        </div>
        <div class="row">
            <div class="col s6" data-ng-if="grType=='INVOICE'">
                <label data-ng-if="paymentRequest.paymentInvoice.deviations.length > 0">Deviations</label>
                <div data-ng-repeat="deviation in paymentRequest.paymentInvoice.deviations"
                     style="background: #ddd; padding: 10px; position: relative; margin: 5px 0;">
                    <b>{{deviation.paymentDeviation.deviationDetail}}</b>: {{deviation.deviationRemark}}
                    <span data-ng-click="removeDeviation(paymentRequest.paymentInvoice.deviations, $index)"
                          style="position:absolute;top:0;right:0;padding:5px 10px;cursor:pointer;font-size:24px;">&times;</span>
                </div>
                <input type="button" value="Add Deviation" class="btn" data-target="invoiceDevModal"
                       data-ng-click="setAvailableDeviations(paymentRequest.paymentInvoice,'INVOICE')" modal />
            </div>
            <div class="col s6" data-ng-if="grType=='INVOICE'">
                <label>Remarks</label>
                <textarea data-ng-model="paymentRequest.remarks"></textarea>
            </div>
        </div>
        <div class="row" data-ng-if="grType=='INVOICE'">
            <div class="col s12">
                <div class="col s3">
                    <input id="amountsMatch" type="checkbox" data-ng-model="paymentRequest.amountsMatch" />
                    <label for="amountsMatch">Amounts match</label>
                </div>
            </div>
        </div>
        <div class="row">
        <div class="col s12" data-ng-if="(selectedGr.vendorAdvancePayments == null || selectedGr.vendorAdvancePayments.length == 0) && employeePaymentCards.length > 0 && eligibleForCardPayment">
            <div class="col s3">
                <input type="checkbox" id="paymentCardCheck" data-ng-model="paymentRequest.paymentCardCheck" data-ng-change="setPaymentCardCheck(paymentRequest.paymentCardCheck)"/>
                <label for="paymentCardCheck">Used Payment Card</label>
            </div>
            <div class="col s3" data-ng-if="paymentRequest.paymentCardCheck">
                <label for="paymentCard">Payment Card</label>
                <select id="paymentCard" data-ng-model="paymentRequest.paymentCard" class="form-control"
                        data-ng-change="setPaymentCard(paymentRequest.paymentCard)" data-ng-options="item for item in employeePaymentCards"></select>
            </div>
            <div class="col s3" data-ng-if="paymentRequest.paymentCard != null">
                <label for="cardPaymentTransactionNumber">Payment Transaction Number</label>
                <input type="text" id="cardPaymentTransactionNumber" data-ng-model="paymentRequest.cardPaymentTransactionNumber"
                       placeholder="Enter Payment Transaction Number"
                       data-ng-change="setCardPaymentTransactionNumber(paymentRequest.cardPaymentTransactionNumber)"/>
            </div>
        </div>
        <br>
        <div class="col s12 margin-top-10" data-ng-if="paymentRequest.paymentCardCheck && paymentRequest.paymentCard != null && paymentRequest.cardPaymentTransactionNumber != null">
            <div class="col s5">
                <label>Payment Comment</label>
                <textarea data-ng-model="paymentRequest.cardPaymentComment" style="resize: none"></textarea>
            </div>
            <div class="col s3" style="margin-top: 5px">
                <button data-tooltip="Upload Payment Proof" class="btn btn-medium" data-ng-click="uploadCardPaymentProof()" style="margin-top: 25px;">Upload Payment Proof</button>
            </div>
            <div class="col s3" data-ng-if="uploadedCardPaymentProof != null" style="margin-top: 5px">
                <button class="btn btn-small" data-ng-click="downloadPaymentProof()" style="margin-top: 25px;">Download</button>
            </div>
        </div>
        </div>
        <div class="row">
            <div class="col s12">
                <input type="button" data-ng-if="grType=='INVOICE' && (selectedGr.vendorAdvancePayments == null || selectedGr.vendorAdvancePayments.length == 0)" class="btn" value="Create Payment Request" data-ng-click="submitPaymentRequest(false)" />
                <input type="button" data-ng-if="grType=='INVOICE' && (selectedGr.vendorAdvancePayments != null && selectedGr.vendorAdvancePayments.length > 0)" class="btn" value="Next" data-ng-click="submitPaymentRequest(true)" />
                <input type="button" data-ng-if="grType=='DELIVERY_CHALLAN' && (selectedGr.vendorAdvancePayments == null || selectedGr.vendorAdvancePayments.length == 0)" class="btn" value="Initiate Payment Request" data-ng-click="submitPaymentRequest(false)" />
                <input type="button" data-ng-if="grType=='DELIVERY_CHALLAN' && (selectedGr.vendorAdvancePayments != null && selectedGr.vendorAdvancePayments.length > 0)" class="btn" value="Next" data-ng-click="submitPaymentRequest(true)" />
            </div>
        </div>
    </div>

<div class="row" data-ng-if="selectView == false && advanceSelectionView">
    <div class="row">
        <div class="col s6">
            <h4>Vendor Advance</h4>
        </div>
        <input type="button" class="btn right" value="Go Back" style="margin-top: 20px;" data-ng-click="goToPrEditScreen()">
    </div>
    <div class="row">
        <div class="advanceHeader">
            Advance : <span class="chip grey white-text">Total Advance Amount {{paymentRequest.advancePayment.totalAdvanceAmount.toFixed(2)}}</span>
            <span class="chip green white-text">Available Amount {{paymentRequest.advancePayment.availableAmount.toFixed(2)}}</span>
            <span class="chip red darken-1 white-text">Blocked {{paymentRequest.advancePayment.blocked.toFixed(2)}}</span>
            <span class="chip yellow darken-1 white-text">Remaining After Usage {{paymentRequest.advancePayment['finalAvailable'].toFixed(2)}}</span>
        </div>
        <br>
        <div class="row" data-ng-if="!showPoSoClose">
            <div style="padding: 10px; margin: 10px; border-radius: 4px; background: #a0e1e4;">Advance Amount is Used Completely Related to this PO.</div>
        </div>
        <div class="row" data-ng-if="!showPoSoClose">
            <input type="button" class="btn" value="Create Payment Request" data-ng-click="submitPaymentRequest(false,false)" />
        </div>
        <div class="row" data-ng-if="showPoSoClose">
            <div class="col s6">
                <label for="poClosed">Is PO Closed ?</label>
                <select id="poClosed" data-ng-model="paymentRequest.advancePayment.poSoClosed" class="form-control" data-ng-change="setPoClosed(paymentRequest.advancePayment.poSoClosed)">
                    <option value="Yes">Yes</option>
                    <option value="No">No</option>
                </select>
            </div>
            <div class="col s6" data-ng-if="paymentRequest.advancePayment.poSoClosed != null && paymentRequest.advancePayment.poSoClosed == 'Yes'">
                <label for="adjustOrRefund">Do You Want to get Refund Or Adjust Amount For Other Po's ?</label>
                <select id="adjustOrRefund" data-ng-model="paymentRequest.advancePayment.adjustOrRefund" class="form-control"
                        data-ng-change="setAdjustOrRefund(paymentRequest.advancePayment.adjustOrRefund)">
                    <option value="Adjust">Adjust</option>
                    <option value="Refund">Refund</option>
                </select>
            </div>
        </div>
        <div class="row" data-ng-if="paymentRequest.advancePayment.poSoClosed != null && paymentRequest.advancePayment.poSoClosed == 'Yes' && paymentRequest.advancePayment.adjustOrRefund == 'Adjust'">
            <div data-ng-if="pendingPoSo.length == 0 && errorMessage == null" style="padding: 10px; margin: 10px; border-radius: 4px; background: #a0e1e4;">
                No Pending {{paymentRequest.advancePayment.advanceType == "PO_ADVANCE" ? "Purchase orders" : "Service orders"}} Found to Adjust Advance Payment
            </div>
            <div data-ng-if="pendingPoSo.length == 0 && errorMessage != null" style="padding: 10px; margin: 10px; border-radius: 4px; background: #a0e1e4;">
                {{errorMessage}}
            </div>
            <div class="row">
                <input type="text" data-ng-model="enterredText" placeholder="Enter to Search" data-ng-if="pendingPoSo.length > 0" data-ng-change="setEnterredText(enterredText)">
            </div>
            <ul class="col s12" data-collapsible="accordion" watch data-ng-if="pendingPoSo.length > 0">
                <li class="row" data-ng-repeat="po in pendingPoSo | filter : enterredText">
                    <div class="collapsible-header poNumber waves-effect waves-light lighten-5 s12">
                        <div class="left col s5" data-ng-click="$event.stopPropogation()">
                            <input id="RO-adj-{{po.id}}" data-ng-model="po.checked" type="checkbox" data-ng-click="disablePosSelection($event,po,pendingPoSo,true)" ng-disabled="po.disable"/>
                            <label for="RO-adj-{{po.id}}">{{po.receiptNumber}} created on {{po.generationTime | date:'dd/MM/yyyy @ h:mma'}} </label>
                        </div>
                        <div class="col s2" style="display: inline-block;">
                            {{po.orderType}}
                        </div>
                        <div class="col s2" style="display: inline-block;" data-ng-if="po.type != null">
                            {{po.type}}
                        </div>
                        <div class="col s2" style="display: inline-block;">
                            Rs. {{po.paidAmount}}
                        </div>
                        <div class="right col s1">
                            <i class="fa fa-caret-down right"></i>
                            <span class="chip right" style="margin-top: 7px;">{{po.status}}</span>
                        </div>
                    </div>
                    <div class="respTable collapsible-body">
                        <table class="bordered striped row">
                            <thead>
                            <tr>
                                <th class="center-align">SKU</th>
                                <th class="center-align">UOM</th>
                                <th class="center-align">Packaging</th>
                                <th class="center-align">Packaging Qty</th>
                                <th class="center-align">Conversion Ratio</th>
                                <th class="center-align">Total Qty</th>
                                <th class="center-align">Price</th>
                                <th class="center-align" tooltipped data-tooltip="Amount = (Price * Quantity) + Taxes">Amount*</th>
                                <th class="center-align">Taxes</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr data-ng-repeat="item in po.orderItems track by $index">
                                <td class="center-align"><a data-ng-click="showPreview($event, item.skuId,'SKU')">{{item.skuName}}</a></td>
                                <td class="center-align">{{item.unitOfMeasure}}</td>
                                <td class="center-align">{{item.packagingName}}</td>
                                <td class="center-align">{{item.packagingQty}}</td>
                                <td class="center-align">{{item.conversionRatio}}</td>
                                <td class="center-align">{{item.requestedQuantity.toFixed(2)}}</td>
                                <td class="center-align">{{item.unitPrice.toFixed(2)}}</td>
                                <td class="center-align">{{item.totalCost}}</td>
                                <td class="center-align">{{item.totalTax}}</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </li>
            </ul>
            <div class="row" data-ng-if="poSoSelected != null">
                <input type="button" class="btn" value="Create Payment Request" data-ng-click="submitPaymentRequest(false)" />
            </div>
        </div>
        <div class="row" data-ng-if="paymentRequest.advancePayment.poSoClosed != null && paymentRequest.advancePayment.poSoClosed == 'Yes' && paymentRequest.advancePayment.adjustOrRefund == 'Refund'">
            <div class="row">
                <div class="col s6">
                    <label>Select Refund date</label>
                    <input input-date type="text" name="refundDate" id="refundDate"
                           ng-model="paymentRequest.advancePayment.refundDate"
                           container="" format="yyyy-mm-dd"
                           min="{{minRefundDate}}"
                           data-ng-change="setRefundDate(paymentRequest.advancePayment.refundDate)"/>
                </div>
            </div>
            <div class="row" data-ng-if="paymentRequest.advancePayment.refundDate != null">
                <input type="button" class="btn" value="Create Payment Request" data-ng-click="submitPaymentRequest(false)" />
            </div>
        </div>
        <div class="row" data-ng-if="paymentRequest.advancePayment.poSoClosed != null && paymentRequest.advancePayment.poSoClosed == 'No'">
            <input type="button" class="btn" value="Create Payment Request" data-ng-click="submitPaymentRequest(false)" />
        </div>
    </div>
</div>

<div id="snapModal" class="modal">
    <div class="modal-content">
        <h4>Take Snapshot</h4>
        <video data-ng-show="snapRunning" id="video" width="640" height="480" autoplay></video><br />
        <button data-ng-click="startSnap()" class="btn btn-small">Start</button>
        <button data-ng-click="snapPicture()" class="btn btn-small">Snap Photo</button><br />
        <canvas data-ng-hide="snapRunning" id="canvas" width="640" height="480"></canvas><br />
    </div>
    <div class="modal-footer">
        <button data-ng-click="uploadFile()" class="btn btn-small modal-action modal-close">Upload</button>
        <button class="btn btn-small modal-action modal-close red" style="margin-right: 20px;">Cancel</button>
    </div>
</div>

<div id="scanModal" class="modal">
    <div class="modal-content">
        <h3>Scan document</h3>
        <button type="button" data-ng-click="scanToPng()">Scan</button>
        <div id="images" style="margin-top: 20px;height: 480px;overflow: auto;">
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn btn-small modal-action modal-close" data-ng-click="uploadScannedFile()">Upload</button>
        <button class="btn btn-small modal-action modal-close red" style="margin-right: 20px;">Close</button>
    </div>
</div>

<div id="invoiceDevModal" class="modal">
    <div class="modal-content">
        <h5>Add deviations</h5>
        <div class="row" data-ng-repeat="dev in availableInvoiceDevs track by $index">
            <div class="col s4">{{dev.data.deviationDetail}}</div>
            <div class="col s7"><input type="text" data-ng-model="dev.remark" /></div>
            <div class="col s1">
                <span>
                    <input type="checkbox" id="{{$index}}" data-ng-model="dev.checked" />
                    <label for="{{$index}}"></label>
                </span>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn btn-small modal-action modal-close"
                data-ng-click="addDeviations(paymentRequest.paymentInvoice, 'INVOICE')">Submit</button>
    </div>
</div>

<div id="itemDevModal" class="modal">
    <div class="modal-content">
        <h5>Add deviations</h5>
        <div class="row" data-ng-repeat="dev in availableItemDevs track by $index">
            <div class="col s4">{{dev.data.deviationDetail}}</div>
            <div class="col s7"><input type="text" data-ng-model="dev.remark" /></div>
            <div class="col s1">
                <span>
                    <input type="checkbox" id="{{$index}}" data-ng-model="dev.checked" />
                    <label for="{{$index}}"></label>
                </span>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn btn-small modal-action modal-close"
                data-ng-click="addDeviations(selectedItemForDeviation, 'INVOICE_ITEM')">Submit</button>
    </div>
</div>

<div id="invoicePreviewModal" class="modal">
    <div class="modal-content">
        <div class="row" style="margin-bottom: 0px;">
            <div class="col s12">
                <div id="invoicePreview" style="max-height: 370px; overflow: auto;"></div>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn modal-action modal-close" style="margin-right: 20px;">Close</button>
    </div>
</div>

<script type="text/ng-template" id="grItemQty.html">

    <div class="modal-header red-text center-align" data-ng-init="init()">
        <h3 class="modal-title" id="modal-title" style="margin-top: 0px;">Quantity Preview</h3>
        <hr>
    </div>
    <ul class="collection" data-ng-if="invoiceGenerated == false">
        <li class="collection-item list-head">
            <div class="row">
                <div class="col s4">Sku Name</div>
                <div class="col s2">Pkg Name</div>
                <div class="col s2">Transferred Qty.</div>
                <div class="col s2">Received Qty</div>
                <div class="col s2">Final Qty</div>
            </div>
        </li>
        <li class="collection-item" >
            <div class="row" style="margin-bottom: 0;" data-ng-repeat="grItem in selectedGr.goodsReceivedItems">
                <div class="col s4">{{grItem.skuName}} ({{grItem.uom}})</div>
                <div class="col s2">{{grItem.packagingDetails[0].packagingDefinitionData.packagingName}}</div>
                <div class="col s2">{{grItem.transferredQuantity}}</div>
                <div class="col s2">{{grItem.originalReceivedQty}}</div>
                <div class="col s2"><input   data-ng-model="grItem.finalQty"  type="number" min="0"/>
                </div>
            </div>
        </li>
    </ul>
    <div class="card green white-text center-align" data-ng-if="invoiceGenerated == true">
        <div class="card-content">Invoice Generated successfully</div>
    </div>
    <!--<div class="btn btn-medium " data-ng-if="invoiceGenerated == false" data-ng-click="generateInvoice()">Generate Invoice</div>-->
    <div class="btn btn-medium right " style="margin-left: 5px;"  data-ng-click="generateInvoice()">Create PR</div>


</script>
