<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="col s12">
        <div class="row">
            <div class="col s2">
                <input type="button" data-ng-click="backToRefOrderFind()" value="Back" class="btn" style="margin-top:28px;" />
            </div>
            <div class="col s10">
                <h3>Reference Order Detail</h3>
            </div>
        </div>
        <div class="row">
            <div class="col s3"><label>Ref. Order Id</label>{{referenceOrderDetail.id}}</div>
            <div class="col s3"><label>Creation Time</label>{{referenceOrderDetail.generationTime | date:'dd-MM-yyyy hh:mm:ss a'}}</div>
            <div class="col s3"><label>Fulfillment Date</label>{{referenceOrderDetail.fulfillmentDate | date:'dd-MM-yyyy'}}</div>
            <div class="col s3"><label>Status</label>{{referenceOrderDetail.status}}</div>
        </div>
        <div class="row">
            <div class="col s3"><label>Created By</label>{{referenceOrderDetail.generatedBy.name}}</div>
            <div class="col s3"><label>Requesting unit</label>{{referenceOrderDetail.requestUnit.name}}</div>
            <div class="col s6"><label>Linked Request Orders</label>{{referenceOrderDetail.requestOrderIds.join(', ')}}</div>
        </div>

       
        <div class="row">
            <div class="col s12">
                <h3>SCM Item Details</h3>
                <ul class="collection">
                    <li class="collection-item list-head">
                        <div class="row">
                            <div class="col s4">Product Name</div>
                            <div class="col s2">Requested Quantity</div>
                            <div class="col s2">Requested Absolute Quantity</div>
                            <div class="col s2">Fulfillment Type</div>
                            <div class="col s2">Unit of Measure</div>
                        </div>
                    </li>
                    <li class="collection-item" data-ng-repeat="item in referenceOrderDetail.referenceOrderScmItems | orderBy:'productName' track by $index">
                        <div class="row" style="margin-bottom: 0px;">
                            <div class="col s4"><a data-ng-click="showPreview($event, item.productId,'PRODUCT')">{{item.productName}}</a></div>
                            <div class="col s2">{{item.requestedQuantity}}</div>
                            <div class="col s2">{{item.requestedAbsoluteQuantity}}</div>
                            <div class="col s2">{{item.fulfillmentType}}</div>
                            <div class="col s2">{{item.unitOfMeasure}}</div>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
        
         <div class="row">
            <div class="col s12">
                <h3>Menu Item Details</h3>
                <ul class="collection">
                    <li class="collection-item list-head">
                        <div class="row">
                            <div class="col s4">Product Name</div>
                            <div class="col s4">Dimension</div>
                            <div class="col s2">Requested Quantity</div>
                            <div class="col s2">Requested Absolute Quantity</div>
                        </div>
                    </li>
                    <li class="collection-item" data-ng-repeat="item in referenceOrderDetail.referenceOrderMenuItems | orderBy:'productName' track by $index">
                        <div class="row" style="margin-bottom: 0px;">
                            <div class="col s4"><a data-ng-click="showPreview($event, item.productId,'PRODUCT')">{{item.productName}}</a></div>
                            <div class="col s4">{{item.dimension}}</div>
                            <div class="col s2">{{item.requestedQuantity}}</div>
                            <div class="col s2">{{item.requestedAbsoluteQuantity}}</div>
                        </div>
                    </li>
                </ul>
            </div>
        </div>

    </div>
</div>