<div class="row" data-ng-init="init()">
    <div class="col s12">
        <div class="searchingCard row white z-depth-3 custom-listing-li">
            <div class="col s12">
                <div class="col s6">
                    <h4>Create GR for Vendor</h4>
                </div>

                <div class="col s6">
                    <div class="col s8">
                        <label>Select GR type</label>
                        <input type="radio" data-ng-model="selectedPOType" data-ng-change="grSelected()" ng-disabled="disableEnableGRType" value="REGULAR_ORDER" id="regular">
                        <label class="form-check-label" for="regular">
                            Regular
                        </label>
                        <input type="radio" data-ng-model="selectedPOType" data-ng-change="grSelected()" ng-disabled="disableEnableGRType" value="FIXED_ASSET_ORDER" id="fixed">
                        <label class="form-check-label" for="fixed">
                            Asset
                        </label>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col s6">
                    <div class="row">
                        <div class="col s4">
                            <b>Unit for Delivery :</b>
                        </div>
                        <div class="col s8">
                            <div class="left">{{currentUnit.name}}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col s5">
                    <div class="row">
                        <div class="col s4">
                            <b>Select Vendor :</b>
                        </div>
                        <div class="col s8" data-ng-show="!showExpandedView">
                            <select ui-select2="selectedVendor" id="vendorList" name="vendorList" data-ng-model="selectedVendor"
                                    data-ng-options="vendor as vendor.name for vendor in vendorList track by vendor.id"
                                    data-ng-change="selectVendor(selectedVendor)"></select>
                        </div>
                        <div class="col s8" data-ng-show="showExpandedView">
                            <span>{{selectedVendor.entityName}}</span>
                        </div>
                    </div>
                </div>
                <div class="col s7" data-ng-if="locationList.length>0">
                    <div class="row">
                        <div class="col s4">
                            <b>Select Dispatch Location:</b>
                        </div>
                        <div class="col s6" data-ng-show="!showExpandedView">
                            <select id="locationList" name="locationList" data-ng-model="selectedLocation"
                                    data-ng-options="location as location.locationName for location in locationList track by location.dispatchId"
                                    data-ng-change="selectDispatchLocation(selectedLocation)"></select>
                        </div>
                        <div class="col s6" data-ng-show="showExpandedView">
                            <span>{{selectedLocation.locationName}}[{{selectedLocation.city}}]</span>
                        </div>
                        <div class="col s2" data-ng-if="!showExpandedView">
                            <button data-ng-click="getPendingOrders()" style="width: 75px;" class="btn btn-xs-small">Search</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="searchingCard row white z-depth-3 custom-listing-li" style="padding:5px;" data-ng-show="!showExpandedView"
             data-ng-if="pendingPOs !=undefined && pendingPOs.length>0">
            <h5>Pending Purchase Orders for: {{selectedVendor.entityName}}({{selectedDispatchLocation.locationName}})
                || OPEX || REGULAR - {{pendingOpexRegularPo}} || ASSET - {{pendingOpexAssetPo}}</h5>
            <div class="col s12">
                    <button tooltipped data-tooltip="Select POs to move forward"
                            data-ng-click="selectPOs()" class="btn right">Select Orders</button>
            </div>
            <ul class="col s12" data-collapsible="accordion" watch>
                <li class="row" data-ng-repeat="po in pendingPOs track by po.id" data-ng-show="po.orderType == selectedPOType && po.type == 'OPEX'">
                    <div class="collapsible-header poNumber waves-effect waves-light lighten-5 s12" data-ng-class="{'red': po.orderType== 'FIXED_ASSET_ORDER'}">
                        <div class="left col s5" data-ng-click="$event.stopPropogation()">
                            <input id="RO-{{po.id}}-OPEX" data-ng-model="po.checked" type="checkbox" data-ng-click="disableSelection($event,po.type,po)" ng-disabled="disableOpex || po.vendorBlocked"/>
                            <label for="RO-{{po.id}}-OPEX">{{po.receiptNumber}} created on {{po.generationTime | date:'dd/MM/yyyy @ h:mma'}} </label>
                        </div>
                        <div class="col {{(po.vendorAdvancePayments != null&& po.vendorAdvancePayments.length > 0) ? 's2' : 's4'}}" style="display: inline-block;">
                            {{po.orderType}}
                        </div>
                        <div class="col s2" style="display: inline-block;" data-ng-if="po.vendorAdvancePayments != null && po.vendorAdvancePayments.length > 0">
                            <span class="chip" style="margin-top: 10px;">Advance PO</span>
                        </div>
                        <div class="col s2" style="display: inline-block;" data-ng-if="po.type != null">
                            {{po.type}}
                        </div>
                        <div class="right col s1">

                            <i class="fa fa-caret-down right"></i>
                            <span class="chip right" style="margin-top: 7px;">{{po.status}}</span>
                        </div>
                    </div>
                    <div class="respTable collapsible-body">
                        <table class="bordered striped row">
                            <thead>
                            <tr>
                                <th class="center-align">SKU</th>
                                <th class="center-align">UOM</th>
                                <th class="center-align">Packaging</th>
                                <th class="center-align">Packaging Qty</th>
                                <th class="center-align">Conversion Ratio</th>
                                <th class="center-align">Total Qty</th>
                                <th class="center-align">Price</th>
                                <th class="center-align" tooltipped data-tooltip="Amount = (Price * Quantity) + Taxes">Amount*</th>
                                <th class="center-align">Taxes</th>
                                <th class="center-align">Tax percentages</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr data-ng-repeat="item in po.orderItems track by $index">
                                <td class="center-align"><a data-ng-click="showPreview($event, item.skuId,'SKU')">{{item.skuName}}</a></td>
                                <td class="center-align">{{item.unitOfMeasure}}</td>
                                <td class="center-align">{{item.packagingName}}</td>
                                <td class="center-align">{{item.packagingQty}}</td>
                                <td class="center-align">{{item.conversionRatio}}</td>
                                <td class="center-align">{{item.requestedQuantity.toFixed(2)}}</td>
                                <td class="center-align">{{item.unitPrice.toFixed(2)}}</td>
                                <td class="center-align">{{item.totalCost}}</td>
                                <td class="center-align">{{item.totalTax}}</td>
                                <td class="center-align">{{item.taxMessage}}</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </li>
            </ul>
            <br>
            <br>
            <h5>Pending Purchase Orders for: {{selectedVendor.entityName}}({{selectedDispatchLocation.locationName}})
                || CAPEX || REGULAR - {{pendingCapexRegularPo}} || ASSET - {{pendingCapexAssetPo}} </h5>
            <div class="col s12">
                <button tooltipped data-tooltip="Select POs to move forward"
                        data-ng-click="selectPOs()" class="btn right">Select Orders</button>
            </div>
            <ul class="col s12" data-collapsible="accordion" watch>
                <li class="row" data-ng-repeat="po in pendingPOs track by po.id" data-ng-show="po.orderType == selectedPOType && po.type == 'CAPEX'">
                    <div class="collapsible-header poNumber waves-effect waves-light lighten-5 s12" data-ng-class="{'red': po.orderType== 'FIXED_ASSET_ORDER'}">
                        <div class="left col s5" data-ng-click="$event.stopPropogation()">
                            <input id="RO-{{po.id}}-CAPEX" data-ng-model="po.checked" type="checkbox" data-ng-click="disableSelection($event,po.type,po)" ng-disabled="disableCapex || po.vendorBlocked"/>
                            <label for="RO-{{po.id}}-CAPEX">{{po.receiptNumber}} created on {{po.generationTime | date:'dd/MM/yyyy @ h:mma'}} </label>
                        </div>
                        <div class="col {{(po.vendorAdvancePayments != null && po.vendorAdvancePayments.length > 0) ? 's2' : 's4'}}" style="display: inline-block;">
                            {{po.orderType}}
                        </div>
                        <div class="col s2" style="display: inline-block;" data-ng-if="po.vendorAdvancePayments != null && po.vendorAdvancePayments.length > 0">
                            <span class="chip" style="margin-top: 10px;">Advance PO</span>
                        </div>
                        <div class="col s2" style="display: inline-block;" data-ng-if="po.type != null">
                            {{po.type}}
                        </div>
                        <div class="right col s1">

                            <i class="fa fa-caret-down right"></i>
                            <span class="chip right" style="margin-top: 7px;">{{po.status}}</span>
                        </div>
                    </div>
                    <div class="respTable collapsible-body">
                        <table class="bordered striped row">
                            <thead>
                            <tr>
                                <th class="center-align">SKU</th>
                                <th class="center-align">UOM</th>
                                <th class="center-align">Packaging</th>
                                <th class="center-align">Packaging Qty</th>
                                <th class="center-align">Conversion Ratio</th>
                                <th class="center-align">Total Qty</th>
                                <th class="center-align">Price</th>
                                <th class="center-align" tooltipped data-tooltip="Amount = (Price * Quantity) + Taxes">Amount*</th>
                                <th class="center-align">Taxes</th>
                                <th class="center-align">Tax percentages</th>
                            </tr>
                            </thead>
                            <tbody>
                            <tr data-ng-repeat="item in po.orderItems track by $index">
                                <td class="center-align"><a data-ng-click="showPreview($event, item.skuId,'SKU')">{{item.skuName}}</a></td>
                                <td class="center-align">{{item.unitOfMeasure}}</td>
                                <td class="center-align">{{item.packagingName}}</td>
                                <td class="center-align">{{item.packagingQty}}</td>
                                <td class="center-align">{{item.conversionRatio}}</td>
                                <td class="center-align">{{item.requestedQuantity.toFixed(2)}}</td>
                                <td class="center-align">{{item.unitPrice.toFixed(2)}}</td>
                                <td class="center-align">{{item.totalCost}}</td>
                                <td class="center-align">{{item.totalTax}}</td>
                                <td class="center-align">{{item.taxMessage}}</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </li>
            </ul>
        </div>

        <div id="expandedPOView" class="row white z-depth-3 custom-listing-li" style="padding:5px;" data-ng-show="showExpandedView">
            <h5>Create GR for: {{selectedVendor.entityName}}({{selectedDispatchLocation.locationName}})
                <button class="right btn btn-small" data-ng-click="goBack()">Cancel GR</button>
            </h5>
            <div class="col s6">
                <div class="row">
                    <ul class="col s12">
                        <li class="row margin0" data-ng-repeat="po in selectedOrders track by po.id">
                            <div class="poNumber">
                                {{po.receiptNumber}} <span class="chip right">{{po.status}}</span>
                            </div>
                            <table class="bordered striped responsive-table" style="font-size:11px;">
                                <thead>
                                <tr>
                                    <th class="center-align">SKU</th>
                                    <th class="center-align">UOM</th>
                                    <th class="center-align">Requested</th>
                                    <th class="center-align">Received(in UOM)</th>
                                    <th class="center-align">Pending(in UOM)</th>
                                    <th class="center-align">Pending(in Pkg)</th>
                                    <th tooltipped data-tooltip="Enter Packaging Qty" class="center-align">GR Qty</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr data-ng-repeat="item in po.orderItems track by $index">
                                    <td class="center-align"><a data-ng-click="showPreview($event, item.skuId,'SKU')">{{item.skuName}} [{{item.packagingName}}]</a></td>
                                    <td class="center-align">{{item.unitOfMeasure}}</td>
                                    <td class="center-align">{{item.packagingQty}}</td>
                                    <td class="center-align">
                                        {{item.receivedQuantity!=null ? item.receivedQuantity.toFixed(2) : 0}}
                                    </td>
                                    <td>
                                        {{(item.requestedQuantity - item.receivedQuantity).toFixed(2)}}
                                    </td>
                                    <td class="center-align">
                                        {{((item.requestedQuantity - item.receivedQuantity)/item.conversionRatio).toFixed(2)}}
                                    </td>
                                    <td class="center-align">
                                        <input type="number" data-ng-if="(item.requestedQuantity - item.receivedQuantity).toFixed(2)>0"
                                               min="0" step="0.001" ng-model="item.received" data-ng-change="updateGRQty(item)">
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </li>
                    </ul>
                </div>

                <div class="row margin0" data-ng-if="!showExtraGRItems">
                    Note: Excess GR option not available on current selected PO items
                </div>

                <div class="row margin0" data-ng-if="showExtraGRItems">
                    <label>Extra GR (only upto 5%)</label>
                    <div class="col s7">
                        <label>Select SKU</label>
                        <select data-ng-model="extraGrItem"
                                data-ng-options="sku as sku.skuName +'['+sku.packagingName+']' for sku in extraGrSkus | filter : byNotFulfilled"></select>
                    </div>
                    <div class="col s3">
                        <label>Packaging Quantity</label>
                        <input type="number" min="0" step="0.001" data-ng-model="extraGrItem.received"/>
                    </div>
                    <div class="col s1" data-ng-if="grItems!=null">
                        <button class="btn btn-floating margin-top-20"
                                data-ng-click="addExtraGrItems(extraGrItem)"><i class="material-icons">add</i>
                        </button>
                    </div>
                </div>
            </div>

            <div class="col s5 right" data-ng-if="grItems!=null || extraGrItems!=null">
                <h5 style="margin-top:0px;">List of Items Received</h5>
                <div class="row margin0 itemsTable">
                    <table class="bordered striped responsive-table" style="border-top: 1px solid #d0d0d0;">
                        <thead>
                        <tr>
                            <th class="center-align">SKU</th>
                            <th class="center-align">Price</th>
                            <th class="center-align">Qty</th>
                            <th class="center-align">Total</th>
                            <th class="center-align">Taxes</th>
                            <th class="center-align">Amount</th>
                            <th class="center-align">Action</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr data-ng-repeat="(skuId,item) in grItems track by $index">
                            <td class="center-align"><a data-ng-click="showPreview($event, item.sku.skuData.id,'SKU')">{{item.sku.skuData.name}} [{{item.pkg.name}}]</a></td>
                            <td class="center-align">{{item.pkg.price.toFixed(2)}}</td>
                            <td class="center-align">{{item.received.toFixed(2)}}</td>
                            <td class="center-align">{{item.totalAmount.toFixed(2)}}</td>
                            <td class="center-align">{{item.totalTax.toFixed(2)}}</td>
                            <td class="center-align">{{item.paidAmount.toFixed(2)}}</td>
                            <td class="center-align"></td>
                        </tr>

                        <tr class="orange white-text" data-ng-repeat="(skuId,item) in extraGrItems track by $index">
                            <td class="center-align"><a data-ng-click="showPreview($event, skuId,'SKU')">{{item.sku.skuData.name}} [{{item.pkg.name}}]</a></td>
                            <td class="center-align">{{item.pkg.price.toFixed(2)}}</td>
                            <td class="center-align">{{item.received.toFixed(2)}}</td>
                            <td class="center-align">{{item.totalAmount.toFixed(2)}}</td>
                            <td class="center-align">{{item.totalTax.toFixed(2)}}</td>
                            <td class="center-align">{{item.paidAmount.toFixed(2)}}</td>
                            <td class="center-align">
                                <button data-ng-click="removeExtraGrItem(item)"
                                        class="btn btn-floating red"><i class="material-icons">close</i></button>
                            </td>
                        </tr>

                        <tr>
                            <th class="center-align"><b>Total</b></th>
                            <th class="center-align"></th>
                            <th class="center-align"></th>
                            <th class="center-align">{{gr.totalPrice.toFixed(2)}}</th>
                            <th class="center-align">{{gr.totalTax.toFixed(2)}}</th>
                            <th class="center-align"></th>
                            <th class="center-align"></th>
                        </tr>
                        </tbody>
                    </table>
                    <div class="row margin0">
                        <div class="right-align" style="line-height: 24px;">
                            <p class="no-pad-bot extraCharges">
                                <label>Extra charges(if any)</label>
                                <input style="max-width: 200px;" type="number" min="0" data-ng-change="changeTotalAmount()" data-ng-model="gr.extraCharges">
                            </p>
                        </div>
                    </div>
                </div>
                <div class="row margin0" style="border-top:1px solid;">
                    <div class="gr-form">
                        <p><b>Total Amount:</b>{{gr.totalCharged.toFixed(6)}}<br></p>
                        <p>
                            <input name="docType" type="radio" id="invoice" value="INVOICE" data-ng-model="gr.docType"/>
                            <label for="invoice">Invoice</label>
                            <input name="docType" type="radio" id="dc" value="DELIVERY_CHALLAN" data-ng-model="gr.docType"/>
                            <label for="dc">Delivery Challan</label>
                        </p>
                        <p>
                            <label>Document Number</label>
                            <input type="text" data-ng-model="gr.docNumber">
                        </p>
                        <p>
                            <label>Document Date</label>
                            <input input-date type="text" ng-model="gr.docDate" min ="{{minDate}}" max="{{maxDate}}"
                                   container="" format="yyyy-mm-dd" />
                        </p>
                        <p>
                            <label>Security Guard Comment</label>
                            <input  type="text" ng-model="gr.comment"  />
                        </p>
                        <p>
                            <input name="amountMatched" type="checkbox" id="amountMatched" data-ng-model="gr.amountMatched"/>
                            <label for="amountMatched">Please check if the amount on invoice has matched.</label>
                        </p>
                    </div>
                </div>
                <div class="row margin0 center">
                    <button class="btn" data-ng-click="submit(false)">Create GR</button>
                </div>
                <div class="row margin0 center" acl-action="VGFCR" >
                    <button class="btn" data-ng-click="submit(true)">Force Create GR</button>
                </div>
            </div>
        </div>
    </div>
</div>



