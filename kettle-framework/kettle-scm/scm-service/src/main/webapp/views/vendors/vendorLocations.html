
<div class="row" data-ng-init="init()" data-ng-class="{addMargin:!editMode}">
    <div id="locationDef" class="row scm-form">
        <div class="row">
            <div class="col s9">
                <div class="left">
                    <h5 style="margin:0px;">Vendor Dispatch/Service Locations </h5>
                    <label>Add all locations from which services/goods will be provided , it can be same as registered address or can be multiple</label>
                    <label>(Fields marked with * are mandatory. You will not be able to proceed if all mandatory fields are not filled)</label>
                    <label data-ng-show="showRequestedChangesLabel" class="yellow">Please Change the highlighted fields !!</label>
                </div>
            </div>
            <div data-ng-if="editMode" class="col s3">
                <div class="right">
                    <button class="btn" data-ng-click="goBack()">Back</button>
                </div>
            </div>
        </div>

        <div class="row white z-depth-3">
            <div class="col s12 m6 l6" style="border-right:1px solid #ddd;">
                <form id="locationDetail" name="locationDetailForm" class="scm-form" data-ng-model="location">
                    <h5>Add Location
                        <button class="btn btn-small right margin-left-5" data-ng-click="addToLocations(location)">Add</button>
                    </h5>

                    <div class="right">
                        <label class="black-text" for="addressType">Select Address Type</label>
                                        <select id="addressType" name="AddressType" data-ng-options="type as type.replace('_','+') for type in addressTypes"
                                                validate="notnull"
                                                data-ng-change = "changeAddressType(selectedAddressType)"
                                                data-ng-model="selectedAddressType" ></select>
                    </div>

                    <div class="row">
                        <div class="col s12">
                            <div class="form-element">
                                <div class="row">
                                    <div class="col l8 m8 s12">
                                        <label class=" black-text" for="locationName">Location Name*</label>
                                        <input type="text" id="locationName" name="locationName" data-ng-model="location.locationName"
                                               validate="notnull" maxlength="25" required>
                                        <p class="help-block"></p>
                                    </div>
                                    <div class="col s4" style="padding: 1.85rem 0 0 1.85rem;">
                                        <input type="checkbox" data-ng-change="changeTaxStatus(location.applyTax)"
                                               id="applyTax" name="applyTax" data-ng-model="location.applyTax">
                                        <label class="black-text" for="applyTax">Apply Taxes</label>
                                    </div>
                                </div>
                            </div>

                            <div class="form-element">
                                <div class="row">
                                    <div class="col s6">
                                        <label class="black-text">Select Notification Type*</label>
                                        <span data-ng-repeat="type in notificationTypes track by $index">
                                            <input type="checkbox" id="{{type}}" name="{{type}}" data-ng-model="checked"
                                                   data-ng-change="addToNotificationTypes(type,checked)">
                                            <label for="{{type}}">{{type}}</label>
                                        </span>
                                        <br/>
                                    </div>
                                    <div class="col s6" data-ng-if="!basicDetail.companyDetails.exemptSupplier">
                                        <label class="black-text" for="gstStatus">Select GST Status*</label>
                                        <select id="gstStatus" name="gstStatus" data-ng-options="type as type for type in gstStatusTypes"
                                                validate="notnull"
                                                data-ng-change = "changeGstStatus(location.gstStatus)"
                                                data-ng-model="location.gstStatus" required></select>
                                    </div>
                                </div>
                            </div>

                            <div class="form-element" data-ng-if="!basicDetail.companyDetails.exemptSupplier">
                                <div class="row">
                                    <!--<div class="col s6">
                                        <label class=" black-text" for="tin">Dispatch Location TIN*</label>
                                        <input type="text" id="tin" name="tin" data-ng-model="location.tin" maxlength="15" required>
                                        <p class="help-block"></p>
                                    </div>-->
                                    <div class="col s12" data-ng-if="location.gstStatus=='REGISTERED'">
                                        <label class="black-text" for="tin">Location GSTIN*</label>
                                        <input type="text" id="gstin" name="gstin" data-ng-model="location.gstin"
                                               validate="gst" maxlength="15">
                                        <p class="help-block"></p>
                                    </div>
                                </div>
                                <div class="row" data-ng-if="location.gstStatus=='REGISTERED'">
                                	<div class="col s6">
                                		<label class="black-text" >Upload GSTIN Document*</label>
                                		<button class="btn btn-medium" data-ng-click="uploadGSTIN()">Select</button>
                            		</div>
                            		<div class="col s6" ng-if="location.gstinDocument!=null">
		                                <span>{{location.gstinDocument.documentLink}}</span>
		                                <i class="material-icons pointer" data-ng-click="location.gstinDocument=null">edit</i>
		                            </div>
                                </div>
                            </div>

                            <div class="form-element">
                                <label class=" black-text" for="line1">Address Line 1*</label>
                                <input type="text" id="line1" name="line1" data-ng-model="location.address.line1"
                                       validate="notnull" maxlength="150" required>
                                <p class="help-block"></p>
                            </div>
                            <div class="form-element">
                                <label class=" black-text" for="line2">Address Line 2</label>
                                <input type="text" id="line2" name="line2" data-ng-model="location.address.line2"
                                       maxlength="150">

                                <p class="help-block"></p>
                            </div>
                            <div class="form-element">
                                <label class="black-text" for="line3">Address Line 3</label>
                                <input type="text" id="line3" name="line3" data-ng-model="location.address.line3"
                                       maxlength="150">
                                <p class="help-block"></p>
                            </div>
                            <div class="form-element">
                                <label class="black-text" for="addressContact">Address Contact Number*</label>
                                <input type="text" id="addressContact" name="addressContact" data-ng-model="location.address.addressContact"
                                       validate="notnull,phone" maxlength="10" required>
                                <p class="help-block"></p>
                            </div>
                            <div class="form-element">
                                <label class="black-text" for="addressEmail">Address Contact Email*</label>
                                <input type="text" id="addressEmail" name="addressEmail" data-ng-model="location.contactEmail"
                                       validate="notnull,email" maxlength="100" required>
                                <p class="help-block"></p>
                            </div>

                            <div class="form-element" data-ng-if="location.address.city!=null">
                                <div class="row">
                                    <label class="black-text margin-top-10 left" for="city">Select Location*</label>
                                    <button class="btn btn-small right" data-ng-click="location.address.city=null;">Edit</button>
                                </div>
                                <div class="row">
                                    <div class="col s6">
                                        <label class="black-text">City</label>
                                        <input type="text" ng-model="location.address.city"  disabled>
                                    </div>
                                    <div class="col s6">
                                        <label class="black-text">State</label>
                                        <input type="text" ng-model="location.address.state"  disabled>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col s6">
                                        <label class="black-text">Country</label>
                                        <input type="text" ng-model="location.address.country" disabled>
                                    </div>
                                    <div class="col s6">
                                        <label class="black-text">Zipcode*</label>
                                        <input type="text" validate="notnull" ng-model="location.address.zipcode" required>
                                    </div>
                                </div>
                            </div>


                            <div class="form-element" data-ng-if="location.address.city==null">
                                <div class="row">
                                    <div class="col s12">
                                        <label class=" black-text" for="city">Select City*</label>
                                        <select id="city" name="city" ui-select2="multiSelectOptions" ng-model="selectedCity"
                                                data-ng-change="changeDispatchLocation(selectedCity)" data-placeholder="Select City"
                                                validate="notnull"
                                                required>
                                            <option ng-repeat="city in cities track by city.id" value="{{city}}">{{city.name}}</option>
                                        </select>
                                        <p class="help-block"></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <button class="btn btn-small right" data-ng-click="addToLocations(location)">Add</button>
                    </div>
                </form>
            </div>
            <div class="col s12 m6 l6" >
                <h5>Added Locations</h5>
                <div class="row margin-top-10">
                    <ul class="col s12" data-collapsible="accordion" watch>
                        <li class="row" data-ng-repeat="locationDet in dispatchLocations track by $index">
                            <div class="collapsible-header waves-effect waves-light lighten-5 col s10" style="background-color: #CDDC39;">
                                {{locationDet.locationName}}
                                <i class="close material-icons" data-ng-click="removeLocation($index)">close</i>
                            </div>
                            <div class="col s2">
                                <button class="btn right margin-top-5 editGst" data-ng-click="edit($index)">Change GSTIN</button>
                            </div>

                            <div class="collapsible-body" style="padding: 5px;line-height: 22px;border: 1px solid #ddd;">
                                <span>
                                    <b>GST:</b> {{locationDet.gstStatus=="REGISTERED" ? locationDet.gstin : locationDet.gstStatus}},
                                    <b>Notification Types:</b> {{locationDet.notificationType}},
                                    <b>TIN:</b> {{locationDet.tin}},
                                    <b>Apply Taxes:</b>{{locationDet.applyTax ? "YES": "NO"}}
                                </span>
                                <br/>
                                <b>Address:</b><span create-address data-ng-model="locationDet.address"></span>
                                <br/>
                            </div>
                            <div class="col s12 ">
                            <p class="requested-changes-block" data-ng-show="detailLocation[locationDet.dispatchId]['address']"> {{detailLocation[locationDet.dispatchId]['address'].comment?detailLocation[locationDet.dispatchId]['address'].comment:"address field to be changed"}}</p>
                            <p class="requested-changes-block" data-ng-show="detailLocation[locationDet.dispatchId]['applyTax']"> {{detailLocation[locationDet.dispatchId]['applyTax'].comment?detailLocation[locationDet.dispatchId]['applyTax'].comment:"applyTax field to be changed"}}</p>
                            <p class="requested-changes-block" data-ng-show="detailLocation[locationDet.dispatchId]['city']"> {{detailLocation[locationDet.dispatchId]['city'].comment?detailLocation[locationDet.dispatchId]['city'].comment:"city field to be changed"}}</p>
                            <p class="requested-changes-block" data-ng-show="detailLocation[locationDet.dispatchId]['contactEmail']"> {{detailLocation[locationDet.dispatchId]['contactEmail'].comment?detailLocation[locationDet.dispatchId]['contactEmail'].comment:"contactEmail field to be changed"}}</p>
                            <p class="requested-changes-block" data-ng-show="detailLocation[locationDet.dispatchId]['country']"> {{detailLocation[locationDet.dispatchId]['country'].comment?detailLocation[locationDet.dispatchId]['country'].comment:"country field to be changed"}}</p>
                            <p class="requested-changes-block" data-ng-show="detailLocation[locationDet.dispatchId]['dispatchId']"> {{detailLocation[locationDet.dispatchId]['dispatchId'].comment?detailLocation[locationDet.dispatchId]['dispatchId'].comment:"dispatchId field to be changed"}}</p>
                            <p class="requested-changes-block" data-ng-show="detailLocation[locationDet.dispatchId]['gstStatus']"> {{detailLocation[locationDet.dispatchId]['gstStatus'].comment?detailLocation[locationDet.dispatchId]['gstStatus'].comment:"gstStatus field to be changed"}}</p>
                            <p class="requested-changes-block" data-ng-show="detailLocation[locationDet.dispatchId]['gstin']"> {{detailLocation[locationDet.dispatchId]['gstin'].comment?detailLocation[locationDet.dispatchId]['gstin'].comment:"gstin field to be changed"}}</p>
                            <p class="requested-changes-block" data-ng-show="detailLocation[locationDet.dispatchId]['gstinDocument']"> {{detailLocation[locationDet.dispatchId]['gstinDocument'].comment?detailLocation[locationDet.dispatchId]['gstinDocument'].comment:"gstinDocument field to be changed"}}</p>
                            <p class="requested-changes-block" data-ng-show="detailLocation[locationDet.dispatchId]['locationType']"> {{detailLocation[locationDet.dispatchId]['locationType'].comment?detailLocation[locationDet.dispatchId]['locationType'].comment:"locationType field to be changed"}}</p>
                            <p class="requested-changes-block" data-ng-show="detailLocation[locationDet.dispatchId]['notificationType']"> {{detailLocation[locationDet.dispatchId]['notificationType'].comment?detailLocation[locationDet.dispatchId]['notificationType'].comment:"notificationType field to be changed"}}</p>
                            <p class="requested-changes-block" data-ng-show="detailLocation[locationDet.dispatchId]['state']"> {{detailLocation[locationDet.dispatchId]['state'].comment?detailLocation[locationDet.dispatchId]['state'].comment:"state field to be changed"}}</p>
                            <p class="requested-changes-block" data-ng-show="detailLocation[locationDet.dispatchId]['status']"> {{detailLocation[locationDet.dispatchId]['status'].comment?detailLocation[locationDet.dispatchId]['status'].comment:"status field to be changed"}}</p>
                            <p class="requested-changes-block" data-ng-show="detailLocation[locationDet.dispatchId]['tin']"> {{detailLocation[locationDet.dispatchId]['tin'].comment?detailLocation[locationDet.dispatchId]['tin'].comment:"tin field to be changed"}}</p>
                            <p class="requested-changes-block" data-ng-show="detailLocation[locationDet.dispatchId]['updateTime']"> {{detailLocation[locationDet.dispatchId]['updateTime'].comment?detailLocation[locationDet.dispatchId]['updateTime'].comment:"updateTime field to be changed"}}</p>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div class="row" style="margin-top:10px;">
        <div class="col s12">
            <button data-ng-if="!editMode" data-ng-click="checkIfAlreadyFilled()" class="btn right"> Save & Create
                <i class="material-icons right">send</i>
            </button>
            <button data-ng-if="editMode" data-ng-click="saveLocations()" class="btn right"> Edit
                <i class="material-icons right">send</i>
            </button>
        </div>
    </div>
</div>
