<style>
    .grid {
        height: 80vh;
        padding: 10px !important;
    }

</style>

<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="col s12">
        <div class="row">
            <div class="col s12">
                <h4  class="center card-panel teal lighten-2 z-depth-1 cyan pulse" style="text-align: center;">Bulk Standalone Transfer Order</h4>
            </div>
        </div>
    </div>
    <div class="row ">
        <div data-ng-if = "!downloaded" class="col s12" > <button data-ng-click="getTemplate()" class="btn-large right teal lighten-2" tooltipped data-tooltip="Download Products Template"><i class="fa fa-download "></i> Export product Sheet</button></div>
        <div data-ng-if = "downloaded && !upload" class="col s12"> <button data-ng-click="uploadDoc()" class="btn-large right teal lighten-2" tooltipped data-tooltip="Upload Sheet"><i class="fa fa-cloud-upload"></i> Upload Sheet</button></div>
        <div data-ng-if = "downloaded && upload" class="col s12">
            <button data-ng-click="back()" class="btn-large left teal lighten-2" tooltipped data-tooltip="Go To Previous Screen"><i class="fa fa-backward"></i> Back</button>
            <button data-ng-click="createTransfers()" class="btn-large right teal lighten-2" tooltipped data-tooltip="Create Transfers"><i class="fa fa-send"></i> Submit Transfers</button>
        </div>


    </div>

    <div class="row margin0" data-ng-if="gridItems.length > 0 && upload == false">
        <div
                class="col s12 grid"
                id="grid"
                ui-grid="gridOptions"
                ui-grid-save-state
                ui-grid-edit
                ui-grid-selection
                ui-grid-resize-columns
                ui-grid-move-columns
                ui-grid-auto-resize
        ></div>
    </div>

    <div class="row margin0" data-ng-if="upload == true;">
        <div
                class="col s12 grid"
                id="unitGrid"
                ui-grid="unitGridOptions"
                ui-grid-save-state
                ui-grid-edit
                ui-grid-resize-columns
                ui-grid-move-columns
                ui-grid-auto-resize
        ></div>
    </div>

</div>
