<style type="text/css">
    td,th{
        padding:10px 5px !important;
    }
    .select2.select2-container{width: 100% !important;}
    #images{
        text-align: center;
    }
    img#scanned {
        height: 400px; /** Sets the display size */
        margin-right: 12px;
        margin-top: 20px;
    }
</style>

<div class="searchingCard row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="col s12">
        <div class="row">
            <div class="col s12">
                <h5 class="left">Create Specialized Order Invoice</h5>
            </div>
        </div>
    </div>

    <div data-ng-show="selectView==true">

        <div>
            <div class="row" >
                <div class="col s2">
                    <label>Start date*</label>
                    <input input-date type="text" data-ng-model="startDate" container="" format="yyyy-mm-dd" />
                </div>
                <div class="col s2">
                    <label>End date*</label>
                    <input input-date type="text" data-ng-model="endDate" container="" format="yyyy-mm-dd" />
                </div>
                <div class="col s3">
                    <label>Select Unit*</label>
                    <select ui-select2="{allowClear:true, placeholder: 'Select Unit*'}" id="unitListx" name="unitListx" data-ng-model="selectedUnit"
                            data-ng-change="selectUnit(selectedUnit)"
                            data-ng-options="item as item.name for item in unitList track by item.id"></select>
                </div>
                <div class="col s3">
                    <label>Select Vendor*</label>
                    <select ui-select2="{allowClear:true, placeholder: 'Select Vendor*'}" id="vendorListx" name="vendorListx" data-ng-model="selectedVendor"
                            data-ng-options="item as item.name for item in vendorList track by item.id"></select>
                </div>
                <div class="col s2">
                    <input type="button" class="btn" value="Find" data-ng-click="findGrs()" style="margin-top: 25px;" />
                </div>
            </div>

            <div class="row" data-ng-show="grs.length>0">
                <div class="left">
                    <button class="btn btn-medium" data-ng-click="selectAll()">Check/unCheck ALL</button>
                </div>
                <div class="right-aligned right">
                    <button class="btn btn-medium" data-ng-click="generateInvoice()">Generate Invoice</button>
                </div>
                <div class="respTable col s12">
                    <table class="row table bordered striped" style="border: #ccc 1px solid;">
                        <thead>
                        <tr>
                            <th>GR Id</th>
                            <th>Company Name</th>
                            <th>Receiving Date</th>
                            <th>Bill Amount</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr data-ng-repeat="gr in grs track by gr.id">
                            <td><input id="GR-{{gr.id}}"
                                       data-ng-model="gr.checked"
                                        type="checkbox"/>
                                <label for="GR-{{gr.id}}">{{gr.id}}</label></td>
                            <td>{{gr.sourceCompany.name}}</td>
                            <td>{{gr.generationTime | date:'dd-MM-yyyy hh:mm:ss a':'+0530'}}</td>
                            <td>{{gr.totalAmount}}</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div data-ng-if="showNoGR" style="padding: 10px; margin: 10px; border-radius: 4px; background: #a0e1e4;">No GR For Pending Invoice found.</div>
        </div>
    </div>
</div>
