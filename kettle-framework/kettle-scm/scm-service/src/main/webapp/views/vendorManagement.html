<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<style>
    .gridInnerDetails {
        height: 25vh;
        padding: 10px !important;
    }
    .custom-modal {
        width:90% !important;
    }
</style>

<head>
    <link rel="stylesheet" href="css/multiselect.css">
</head>

<div class="searchingCard row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="col s12">
        <div class="row">
            <div class="col s12">
                <h3>Vendor Management</h3>
            </div>
        </div>
        <div class="row">
            <div class="col s6">
                <div class="row left" style="display: inline; font-size: 16px;"> Vendor Complicance Validation :
                    <button data-ng-click="validateVendorCompliance()" style="width: 100px;" class="btn btn-medium" data-ng-if="activateVendors.length > 0 && currentUserId == 140199">Validate</button>
                </div>
            </div>

            <div class="col s6">
                <div acl-action="SVMVED" class="row right" style="display: inline; font-size: 16px;"> Send Vendor RO Notification :
                    <button data-ng-click="sendRONotification()" style="width: 100px;" class="btn btn-xs-medium">Send</button>
                </div>
            </div>
        </div>
    </div>

    <!--<div class="row">
        <label>Search Vendor</label>
        <select ui-select2  ng-options="vendor as vendor for vendor in vendorList"></select>
    </div>-->
    <div class="row">
        <div class="col s4">
            <label class="black-text" for="selectedStatus">Search By Status</label>
            <select ui-select2="selectedStatus" id="selectedStatus" name="selectedStatus" data-ng-model="selectedStatus"
                    data-ng-options="status for status in vendorStatusList"
                    data-ng-change="getVendors(selectedStatus)"></select>
        </div>
        <div class="col s4" data-ng-if="vendorList.length>0">
            <label class="black-text">Search By Name</label>
            <input type="text" id="vendorName" name="vendorName" data-ng-model="searchText.text" ng-change="changeSearchText(searchText.text)">
        </div>
        <div class="col s4">
            <label>Select Vendor</label>
            <select id="vendors" ui-select2="{allowClear:true, placeholder: 'Select Vendor'}" data-ng-model="vendorSelected"
                    ng-options="vendor as vendor.name for vendor in vendors"
                    data-ng-change="getSelectedVendor(vendorSelected)"></select>
        </div>
        <div class="row" data-ng-show="vendorList.length>0">
            <div class="col s6">
                Filter: <input
                    type="text"
                    ng-model="search"
                    ng-change="filter()"
                    placeholder="Filter"
                    class="form-control"/>
            </div>
            <div class="col s3">
                Results per page: <select
                    data-ng-model="entryLimit"
                    class="form-control">
                <option value="50">50</option>
                <option value="5">5</option>
                <option value="10">10</option>
                <option value="20">20</option>
                <option value="100">100</option>
                <option value="200">200</option>
                <option value="500">500</option>
                <option value="1000">1000</option>
                <option value="2000">2000</option>
            </select>
            </div>
            <div class="col s3 margin-top-10">
                <input type="checkbox" id="filterBlocked" data-ng-model="filterBlocked" data-ng-change="setFilterBlocked(filterBlocked)"/>
                <label for="filterBlocked">Show Blocked</label>
            </div>
        </div>
        <div class="col s12">
            <ul class="collection striped " data-ng-show="vendorList.length>0">
                <li class="collection-item list-head">
                    <div class="row">
                        <div class="col s1">Id
                            <div>
                                <span><a style="color: #05141c" ng-click="sort_by('id');">Sort By Id</a></span>
                            </div>
                        </div>
                        <div class="col s1">First Name</div>
                        <div class="col s1">Last Name</div>
                        <div class="col s2">Entity Name</div>
                        <div class="col s1">Status</div>
                        <div class="col s1">Action</div>
                        <div class="col s1">Lead Time</div>
                        <div class="col s1">Credit Cycle</div>
                        <div class="col s1">TDS</div>
                        <div class="col s2">Block/Un Block</div>
                    </div>
                </li>
                <li class="collection-item clickable"
                    data-ng-repeat="vd in filtered = (vendorList | filter:search | filter : byEntityName | filter : byBlockStatus | orderBy : 'vendorId' | orderBy : predicate :reverse) | startFrom:(currentPage-1)*entryLimit | limitTo:entryLimit track by vd.vendorId"
                    data-ng-style="vd.vendorBlocked == 'Y' ? {'background':'red'} : {}">
                    <div class="row" style="margin-bottom: 0px;">
                        <div class="col s1">{{vd.vendorId}}</div>
                        <div class="col s1">{{vd.firstName}}</div>
                        <div class="col s1">{{vd.lastName}}</div>
                        <div class="col s2">{{vd.entityName}}</div>
                        <div class="col s1" style="word-wrap: break-word;">{{vd.status}}</div>
                        <div class="col s1">
                            <a class="btn btn-small vBtn" data-ng-click="viewVendor(vd,'View')" acl-action="SVMVVI" >View</a><br>
                            <a ng-if="vd.status == 'COMPLETED' || vd.status == 'ACTIVE'" class="btn btn-small vBtn" style="margin-top:5px"
                               data-ng-click="viewVendor(vd,'Edit')" acl-action="SVMVED">Edit</a>
                            <a ng-if="vd.link != null && (vd.status == 'INITIATED' || vd.status == 'IN_PROCESS')" style="margin-top:5px"
                               class="btn btn-small vBtn" href="{{vd.link}}" target="_blank" acl-action="SVMVED">Edit
                                Page</a>
                        </div>
                        <div class="col s1" data-ng-if="vd.status != 'ACTIVE'">
                            -
                        </div>
                        <div class="col s1" data-ng-if="vd.status == 'ACTIVE'">
                        <input type="number" id="leadTime" data-ng-model="vd.leadTime" placeholder="Lead Time" style="width:56px;margin-left: -25px">
                        <a class="xs-small" style="margin-bottom: 5px; width:70px;" acl-sub-menu="VOMRPO"
                           data-ng-click="editLeadTime(vd.vendorId,vd.leadTime)" >&#x2705;</a>
                        </div>
                        <div class="col s1" data-ng-if="vd.status == 'ACTIVE'">
                                <input type="number" id="creditCyle" data-ng-model="vd.creditDays" placeholder="Credit Days" style="width:56px;margin-left: -25px">
                                  <a class="xs-small" style="margin-bottom: 5px; width:70px;" acl-action="PRACK"
                                        data-ng-click="editCreditCycle(vd.companyId,vd.creditDays)" >&#x2705;</a>
                            </div>
                        <div class="col s1" data-ng-if="vd.status != 'ACTIVE'">
                            -
                        </div>
                        <div  class="col s1">
                            <select  id="tdsApplicable"
                                    name="IsTDS" data-ng-model="vd.tds"
                                    data-ng-change="isTdsApplicable(vd.tds,$index)"
                                    validate="notnull" required>
                                <option value="true">Yes</option>
                                <option value="false">No</option>
                            </select>
                            <div>
                            <div data-ng-if="vd.tds!=null">
                                <button class="btn" data-ng-click="uploadTDS($index)">Upload</button>
                            </div>
                            <div  data-ng-if="vd.tdsDocument!=null">
                                <span>{{vd.tdsDocument.documentLink}}</span>
                                <i class="material-icons pointer" data-ng-click="vd.tdsDocument=null">edit</i>
                            </div>
                            </div>
                        </div>
                        <div class="col s2">
                            <input type="button" class="btn btn-medium vBtn margin-top-10 padding-bottom-10" data-ng-if="vd.vendorBlocked == null || (vd.vendorBlocked =='Y' && vd.blockedReason != 'MANUAL')"
                                   value="Block Manually" data-ng-click="openBlockUnblockVendorModal(vd, 'BLOCK')" acl-action="VBLK">
                            <input type="button" class="btn btn-medium vBtn margin-top-10 padding-bottom-10" value="Un Block" data-ng-if="vd.vendorBlocked =='Y' && vd.blockedReason == 'MANUAL'"
                                   data-ng-click="openBlockUnblockVendorModal(vd, 'UN_BLOCK')" acl-action="VBLK">
                            <input type="button" class="btn btn-medium vBtn margin-top-10 padding-bottom-10" value="Advance Un Block" data-ng-if="vd.vendorBlocked =='Y' && vd.blockedReason != 'MANUAL'"
                                   data-ng-click="openAdvanceUnblockVendorModal(vd)" acl-action="HODGGRP">
                            <input type="button" class="btn btn-small vBtn margin-top-10 padding-bottom-10" value="Block Details" data-ng-if="vd.unblockedTillDate != null"
                                   data-ng-click="openAdvanceUnblockVendorModal(vd)" acl-action="HODGGRP">
                        </div>
                        </div>
                </li>
            </ul>
    </div>
        <!--<div class="TableMobileView">-->
        <!--<ul class="collection striped" data-ng-show="vendorList.length>0">-->
            <!--<li class="collection-item"  data-ng-repeat="vd in vendorList | filter : byEntityName | orderBy : 'vendorId' track by vd.vendorId">-->
                <!--<div class="row">-->
                    <!--<div class="col s1">Id</div> <div class="col s1">{{vd.vendorId}}</div>-->
                <!--</div>-->
                <!--<div class="row">-->
                    <!--<div class="col s2">First Name</div>  <div class="col s2">{{vd.firstName}}</div>-->
                <!--</div>-->
                <!--<div class="row">-->
                    <!--<div class="col s2">Last Name</div>  <div class="col s1">{{vd.lastName}}</div>-->
                <!--</div>-->
                <!--<div class="row">-->
                    <!--<div class="col s2">Entity Name</div>  <div class="col s2">{{vd.entityName}}</div>-->
                <!--</div>-->
                <!--<div class="row">-->
                    <!--<div class="col s1">Status</div> <div class="col s1" style="word-wrap: break-word;">{{vd.status}}</div>-->
                <!--</div>-->
                <!--<div class="row" ng-if="vd.status == 'COMPLETED' || vd.status == 'ACTIVE' || vd.link != null && (vd.status == 'INITIATED' || vd.status == 'IN_PROCESS')">-->
                    <!--<div class="col s2">Action</div> <div class="col s2">-->
                    <!--<a class="btn btn-small vBtn" data-ng-click="viewVendor(vd)" acl-action="SVMVVI" >View</a><br>-->
                    <!--<a ng-if="vd.status == 'COMPLETED' || vd.status == 'ACTIVE'" class="btn btn-small vBtn" style="margin-top:5px"-->
                       <!--data-ng-click="generateEditRequest(vd)" acl-action="SVMVED">Edit</a>-->
                    <!--<a ng-if="vd.link != null && (vd.status == 'INITIATED' || vd.status == 'IN_PROCESS')" style="margin-top:5px"-->
                       <!--class="btn btn-small vBtn" href="{{vd.link}}" target="_blank" acl-action="SVMVED">Edit-->
                        <!--Page</a>-->
                    <!--</div>-->
                <!--</div>-->
                <!--<div class="row" data-ng-if="vd.status == 'ACTIVE'">-->
                    <!--<div class="col s1">Lead Time</div>-->
                    <!--<div class="col s1" data-ng-if="vd.status == 'ACTIVE'">-->
                        <!--<input type="number" id="leadTime" data-ng-model="vd.leadTime" placeholder="Lead Time" style="width:56px;margin-left: -25px">-->
                        <!--<a class="xs-small" style="margin-bottom: 5px; width:70px;" acl-sub-menu="VOMRPO"-->
                           <!--data-ng-click="editLeadTime(vd.vendorId,vd.leadTime)" >&#x2705;</a>-->
                    <!--</div>-->
                <!--</div>-->
                <!--<div class="row" data-ng-if="vd.status == 'ACTIVE' || selectedStatus == 'ACTIVE'">-->
                    <!--<div class="col s1" data-ng-if="selectedStatus == 'ACTIVE'">Credit Cycle</div>-->
                    <!--<div class="col s1" data-ng-if="vd.status == 'ACTIVE'">-->
                        <!--<input type="number" id="creditCyle" data-ng-model="vd.creditDays" placeholder="Credit Days" style="width:56px;margin-left: -25px">-->
                        <!--<a class="xs-small" style="margin-bottom: 5px; width:70px;" acl-action="PRACK"-->
                           <!--data-ng-click="editCreditCycle(vd.companyId,vd.creditDays)" >&#x2705;</a>-->
                    <!--</div>-->
                <!--</div>-->
                <!--<div class="row">-->
                    <!--<div  class="col s1">TDS</div>-->
                    <!--<div  class="col s1">-->
                        <!--<select  id="tdsApplicable"-->
                                 <!--name="IsTDS" data-ng-model="vd.tds"-->
                                 <!--data-ng-change="isTdsApplicable(vd.tds,$index)"-->
                                 <!--validate="notnull" required>-->
                            <!--<option value="true">Yes</option>-->
                            <!--<option value="false">No</option>-->
                        <!--</select>-->
                        <!--<div>-->
                            <!--<div data-ng-if="vd.tds!=null">-->
                                <!--<button class="btn" data-ng-click="uploadTDS($index)">Upload</button>-->
                            <!--</div>-->
                            <!--<div  data-ng-if="vd.tdsDocument!=null">-->
                                <!--<span>{{vd.tdsDocument.documentLink}}</span>-->
                                <!--<i class="material-icons pointer" data-ng-click="vd.tdsDocument=null">edit</i>-->
                            <!--</div>-->
                        <!--</div>-->
                    <!--</div>-->
                <!--</div>-->
            <!--</li>-->
        <!--</ul>-->
        <!--</div>-->

        <div class="col s12 ">
            <ul class="collection striped" data-ng-show="vendorDetail != null">
                <li class="collection-item list-head">
                    <div class="row">
                        <div class="col s1">Id</div>
                        <div class="col s1">First Name</div>
                        <div class="col s1">Last Name</div>
                        <div class="col s2">Entity Name</div>
                        <div class="col s1">Status</div>
                        <div class="col s2">Action</div>
                        <div class="col s1">Lead Time</div>
                        <div class="col s1">Credit Cycle</div>
                        <div class="col s2">Block/Un Block</div>
                    </div>
                </li>
                <li class="collection-item clickable" data-ng-style="vendorDetail.vendorBlocked == 'Y' ? {'background':'red'} : {}">
                    <div class="row" style="margin-bottom: 0px;">
                        <div class="col s1">{{vendorDetail.vendorId}}</div>
                        <div class="col s1">{{vendorDetail.firstName}}</div>
                        <div class="col s1">{{vendorDetail.lastName}}</div>
                        <div class="col s2">{{vendorDetail.entityName}}</div>
                        <div class="col s1" style="word-wrap: break-word;">{{vendorDetail.status}}</div>
                        <div class="col s2">
                            <a class="btn btn-small vBtn" data-ng-click="viewVendor(vendorDetail,'View')" acl-action="SVMVVI" >View</a>
                            <a ng-if="vendorDetail.status == 'COMPLETED' || vendorDetail.status == 'ACTIVE'" class="btn btn-small vBtn" style="margin-top:5px"
                               data-ng-click="viewVendor(vendorDetail,'Edit')" acl-action="SVMVED">Edit</a>
                            <a ng-if="vendorDetail.link != null && (vendorDetail.status == 'INITIATED' || vendorDetail.status == 'IN_PROCESS')" style="margin-top:5px"
                               class="btn btn-small vBtn" href="{{vendorDetail.link}}" target="_blank" acl-action="SVMVED">Edit
                                Page</a>
                        </div>
                        <div class="col s1" data-ng-if="vendorDetail.status == 'ACTIVE'">
                            <input type="number" id="leadTime" data-ng-model="vendorDetail.leadTime" placeholder="Lead Time" style="width:56px;margin-left: -25px">
                            <a class="xs-small" style="margin-bottom: 5px; width:70px;" acl-sub-menu="VOMRPO"
                               data-ng-click="editLeadTime(vendorDetail.vendorId,vendorDetail.leadTime)" >&#x2705;</a>
                        </div>
                        <div class="col s1" data-ng-if="vendorDetail.status != 'ACTIVE'">
                            -
                        </div>
                        <div class="col s1" data-ng-if="vendorDetail.status == 'ACTIVE'">
                            <input type="number" id="creditCyle" data-ng-model="vendorDetail.companyDetails.creditDays" placeholder="Credit Days" style="width:56px;margin-left: -25px">
                            <a class="xs-small" style="margin-bottom: 5px; width:70px;" acl-action="PRACK"
                               data-ng-click="editCreditCycle(vendorDetail.companyDetails.companyId,vendorDetail.creditDays)" >&#x2705;</a>
                        </div>
                        <div class="col s1" data-ng-if="vendorDetail.status != 'ACTIVE'">
                            -
                        </div>
                        <div class="col s2">
                            <input type="button" class="btn btn-medium vBtn margin-top-10 padding-bottom-10" data-ng-if="vendorDetail.vendorBlocked == null || (vendorDetail.vendorBlocked =='Y' && vendorDetail.blockedReason != 'MANUAL')"
                                   value="Block Manually" data-ng-click="openBlockUnblockVendorModal(vendorDetail, 'BLOCK')" acl-action="VBLK">
                            <input type="button" class="btn btn-medium vBtn margin-top-10 padding-bottom-10" value="Un Block" data-ng-if="vendorDetail.vendorBlocked =='Y' && vendorDetail.blockedReason == 'MANUAL'"
                                   data-ng-click="openBlockUnblockVendorModal(vendorDetail, 'UN_BLOCK')" acl-action="VBLK">
                            <input type="button" class="btn btn-medium vBtn margin-top-10 padding-bottom-10" value="Advance Un Block" data-ng-if="vendorDetail.vendorBlocked =='Y' && vendorDetail.blockedReason != 'MANUAL'"
                                   data-ng-click="openAdvanceUnblockVendorModal(vendorDetail)" acl-action="HODGGRP">
                            <input type="button" class="btn btn-small vBtn margin-top-10 padding-bottom-10" value="Block Details" data-ng-if="vendorDetail.unblockedTillDate != null"
                                   data-ng-click="openAdvanceUnblockVendorModal(vendorDetail)" acl-action="HODGGRP">
                        </div>
                    </div>
                </li>
            </ul>
        </div>
    <!--<div class="TableMobileView">-->
        <!--<ul class="collection" data-ng-show="vendorDetail != null">-->
            <!--<li class="collection-item clickable">-->
                <!--<div class="row">-->
                    <!--<div class="col s1">Id</div>-->
                    <!--<div class="col s1">{{vendorDetail.vendorId}}</div>-->
                <!--</div>-->
                <!--<div class="row">-->
                    <!--<div class="col s2">First Name</div>-->
                    <!--<div class="col s2">{{vendorDetail.firstName}}</div>-->
                <!--</div>-->
                <!--<div class="row">-->
                    <!--<div class="col s1">Last Name</div>-->
                    <!--<div class="col s2">{{vendorDetail.lastName}}</div>-->
                <!--</div>-->
                <!--<div class="row">-->
                    <!--<div class="col s2">Entity Name</div>-->
                    <!--<div class="col s2">{{vendorDetail.entityName}}</div>-->
                <!--</div>-->
                <!--<div class="row">-->
                    <!--<div class="col s1">Status</div>-->
                    <!--<div class="col s1" style="word-wrap: break-word;">{{vendorDetail.status}}</div>-->
                <!--</div>-->
                <!--<div class="row">-->
                    <!--<div class="col s2">Action</div>-->
                    <!--<div class="col s2">-->
                        <!--<a class="btn btn-small vBtn" data-ng-click="viewVendor(vendorDetail)" acl-action="SVMVVI">View</a>-->
                        <!--<a ng-if="vendorDetail.status == 'COMPLETED' || vendorDetail.status == 'ACTIVE'"-->
                           <!--class="btn btn-small vBtn" style="margin-top:5px"-->
                           <!--data-ng-click="generateEditRequest(vendorDetail)" acl-action="SVMVED">Edit</a>-->
                        <!--<a ng-if="vendorDetail.link != null && (vendorDetail.status == 'INITIATED' || vendorDetail.status == 'IN_PROCESS')"-->
                           <!--style="margin-top:5px" class="btn btn-small vBtn" href="{{vendorDetail.link}}" target="_blank"-->
                           <!--acl-action="SVMVED">Edit-->
                            <!--Page</a>-->
                    <!--</div>-->
                <!--</div>-->
                <!--<div class="row" data-ng-if="vendorDetail.status == 'ACTIVE'">-->
                    <!--<div class="col s1">Lead Time</div>-->
                    <!--<div class="col s1" data-ng-if="vendorDetail.status == 'ACTIVE'">-->
                        <!--<input type="number" id="leadTime" data-ng-model="vendorDetail.leadTime" placeholder="Lead Time"-->
                               <!--style="width:56px;margin-left: -25px">-->
                        <!--<a class="xs-small" style="margin-bottom: 5px; width:70px;" acl-sub-menu="VOMRPO"-->
                           <!--data-ng-click="editLeadTime(vendorDetail.vendorId,vendorDetail.leadTime)">&#x2705;</a>-->
                    <!--</div>-->
                <!--</div>-->
                <!--<div class="row" data-ng-if="selectedStatus == 'ACTIVE' || vendorDetail.status == 'ACTIVE'">-->
                    <!--<div class="col s1" data-ng-if="selectedStatus == 'ACTIVE'">Credit Cycle</div>-->
                    <!--<div class="col s1" data-ng-if="vendorDetail.status == 'ACTIVE'">-->
                        <!--<input type="number" id="creditCyle" data-ng-model="vendorDetail.companyDetails.creditDays"-->
                               <!--placeholder="Credit Days" style="width:56px;margin-left: -25px">-->
                        <!--<a class="xs-small" style="margin-bottom: 5px; width:70px;" acl-action="PRACK"-->
                           <!--data-ng-click="editCreditCycle(vendorDetail.companyDetails.companyId,vendorDetail.creditDays)">&#x2705;</a>-->
                    <!--</div>-->
                <!--</div>-->
            <!--</li>-->
        <!--</ul>-->
    <!--</div>-->
    </div>
</div>

<script type="text/ng-template" id="validateVendorCompliance.html" class="custom-modal" >
    <div class="modal-content">
        <div class="row">
            <div class="col s5">
                <label>Select Vendor : </label>
                <div>
                    <div class="col-lg-12" style="text-align: left" ng-dropdown-multiselect="" options="vendorDetails"
                         selected-model="selectedVendor"
                         extra-settings="multiSelectSettingVendor">
                    </div>
                </div>
            </div>
            <div class="col s5">
                <label>Select Compliance : </label>
                <div>
                    <div class="col-lg-12" style="text-align: left" ng-dropdown-multiselect="" options="complianceTypes"
                         selected-model="selectedComplianceType"
                         extra-settings="multiSelectSettingCompliance">
                    </div>
                </div>
            </div>
            <div class="col s2" style="margin-top: 15px;">
                <label for="forceRetry">Force Retry</label>
                <select id="forceRetry" data-ng-model="forceRetry"
                         data-ng-change="setForceRetry(forceRetry)">
                    <option value="YES">YES</option>
                    <option value="NO">NO</option>
                </select>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn btn-medium red left" data-ng-click="close()">Close</button>
        <button class="btn btn-medium right" data-ng-if="selectedVendor.length > 0 && selectedComplianceType.length > 0" data-ng-click="checkVendorCompliance()">Validate</button>
    </div>
</script>


<script type="text/ng-template" id="unBlockVendorModal.html" class="modal-large" >
    <div class="modal-content">
        <div class="row" data-ng-if="vendor.vendorBlocked == 'Y'">
            <div class="row">
                <h5 data-ng-if="vendor.vendorBlocked == 'Y'">Vendor Blocked </h5>
            </div>
            <div class="row">
                <div class="col s6" data-ng-if="vendor.vendorBlocked == 'Y'">
                    <p>Vendor Blocked Due to the Advance Payments : {{vendor.blockedReason}}</p>
                </div>
            </div>
            <div class="col s6" data-ng-if="vendor.vendorBlocked == 'Y'">
                <label for="unblockTillDate">Select Unblock Till Date*</label>
                <input input-date type="text" name="unblockTillDate" id="unblockTillDate"
                       ng-model="unblockTillDate"
                       container="" format="yyyy-mm-dd"
                       min="{{minDate}}"
                       data-ng-change="setUnblockTillDate(unblockTillDate)"/>
            </div>
        </div>
        <div class="row" data-ng-if="vendor.unblockedTillDate != null">
            <div class="row">
                <h5>Vendor Will be Unblocked Untill {{vendor.unblockedTillDate | date : "dd-MM-yyyy"}} </h5>
                <p>Please Settle all the GR/SR/PR related to the advance Payments Of : {{vendor.blockedReason}}</p>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn btn-medium red left" data-ng-click="close(false)">Close</button>
        <button class="btn btn-medium right" data-ng-if="vendor.vendorBlocked == 'Y' && unblockTillDate != null" data-ng-click="submitUnblockTillDate(unblockTillDate)">Un Block</button>
    </div>
</script>

<script type="text/ng-template" id="pendingPoGr.html">
    <div class="ui-grid-cell-contents">
        <button class="btn btn-xs-small" style="overflow:hidden;" data-ng-if="row.entity.vendorBlockGrSrs.length > 0" data-ng-click="grid.appScope.setRow(row.entity, 'PURCHASE_ORDER')">
            View GR/PR
        </button>
        <span data-ng-if="row.entity.vendorBlockGrSrs.length == 0">-</span>
    </div>
</script>

<script type="text/ng-template" id="pendingSoSr.html">
    <div class="ui-grid-cell-contents">
        <button class="btn btn-xs-small" style="overflow:hidden;" data-ng-if="row.entity.vendorBlockGrSrs.length > 0" data-ng-click="grid.appScope.setRow(row.entity, 'SERVICE_ORDER')">
            View SR/PR
        </button>
        <span data-ng-if="row.entity.vendorBlockGrSrs.length == 0">-</span>
    </div>
</script>

<script type="text/ng-template" id="blockVendorModal.html" class="modal-large" >
    <div class="modal-content">
        <div class="row">
            <div class="row">
                <h5>Pending Orders Of :  {{pendingOrdersToBlockVendor.vendorName}} ({{pendingOrdersToBlockVendor.vendorId}}) </h5>
                <div class="row" data-ng-if="vendor.lastBlockedDate != null">
                    <div style="padding: 10px; margin: 10px; border-radius: 4px; background: #a0e1e4;">Last Vendor Blocked Date is : {{vendor.lastBlockedDate | date : "yyyy-MM-dd:HH:mm:ss"}} and Blocked By : {{vendor.lastBlockedBy}}</div>
                </div>
                <div class="row" data-ng-if="vendor.lastUnBlockedDate != null">
                    <div style="padding: 10px; margin: 10px; border-radius: 4px; background: #a0e1e4;">Last Vendor Un Blocked Date is : {{vendor.lastUnBlockedDate | date : "yyyy-MM-dd:HH:mm:ss"}}
                        and Un Blocked By : {{vendor.lastUnBlockedBy}}</div>
                </div>
                <br>
            </div>
            <div class="row" data-ng-repeat="(orderType,detail) in pendingOrdersToBlockVendor.details">
                <div class="row" data-ng-click="detail.length > 0">
                    <h5>{{orderType}}({{detail.length}})</h5>
                </div>
                <div class="row" data-ng-if="detail.length > 0">
                    <div data-ng-if="orderType == 'PURCHASE_ORDER'" class="col s12 grid"
                         id="gridPo"
                         ui-grid="gridPoOptions"
                         ui-grid-save-state
                         ui-grid-edit
                         ui-grid-exporter
                         ui-grid-resize-columns
                         ui-grid-move-columns
                         ui-grid-auto-resize>
                    </div>
                    <div data-ng-if="orderType == 'PURCHASE_ORDER' && selectedRow != null && selectedRow.orderType == 'PURCHASE_ORDER'">
                        <br>
                        <h5 class="left">Pending GR/Pr for PO Id : {{selectedRow.poSoId}}</h5>
                        <button class="btn btn-primary right red margin-top-10" data-ng-click="closeDetails()">Close</button>
                        <div class="col s12 gridInnerDetails"
                             id="gridGrPr"
                             ui-grid="gridGrSrPrOptions"
                             ui-grid-save-state
                             ui-grid-edit
                             ui-grid-exporter
                             ui-grid-resize-columns
                             ui-grid-move-columns
                             ui-grid-auto-resize>
                        </div>
                    </div>
                    <div data-ng-if="orderType == 'SERVICE_ORDER'" class="col s12 grid"
                         id="gridSo"
                         ui-grid="gridSoOptions"
                         ui-grid-save-state
                         ui-grid-edit
                         ui-grid-exporter
                         ui-grid-resize-columns
                         ui-grid-move-columns
                         ui-grid-auto-resize>
                    </div>
                    <div data-ng-if="orderType == 'SERVICE_ORDER' && selectedRow != null && selectedRow.orderType == 'SERVICE_ORDER'">
                        <br>
                        <h5 class="left">Pending SR/Pr for SO Id : {{selectedRow.poSoId}}</h5>
                        <button class="btn btn-primary right red margin-top-10" data-ng-click="closeDetails()">Close</button>
                        <div class="col s12 gridInnerDetails"
                             id="gridSrPr"
                             ui-grid="gridGrSrPrOptions"
                             ui-grid-save-state
                             ui-grid-edit
                             ui-grid-exporter
                             ui-grid-resize-columns
                             ui-grid-move-columns
                             ui-grid-auto-resize>
                        </div>
                    </div>
                </div>
                <div class="row" data-ng-if="detail.length == 0">
                    <div style="padding: 10px; margin: 10px; border-radius: 4px; background: #a0e1e4;">No Peniding {{orderType}} Found For the Vendor..!</div>
                </div>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn btn-medium red left" data-ng-click="close(false)">Close</button>
        <button class="btn btn-medium right" data-ng-click="blockUnblockVendor()">{{blockType == 'BLOCK' ? 'Block' : 'Un Block'}} Vendor</button>
    </div>
</script>
