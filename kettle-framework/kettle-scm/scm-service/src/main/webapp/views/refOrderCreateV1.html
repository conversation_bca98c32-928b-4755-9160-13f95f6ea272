<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<style>
    .headerHeight{
        height: 100px;
    }
    .table-header{
        height: 100px;
        background-color: grey;
        font-size: larger;
    }
    .sales-header{
        border-right: 3px solid white;
        font-size: 20px;
        text-align: center;
        background: #26a69a;
        font-weight: 600;
        color: white;
    }
    .sales-row-side-border{
        border-right: 3px solid white;
    }
    .center-text-table{
        text-align: center;
        border: 1px solid #26a29a;
        background: #26a69a70;
    }
    .modal-large {
        width:90% !important;
    }
    .custom-modal {
        width:90% !important;
    }

</style>
<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="col s12">
        <div class="row">
            <div class="col s12">
                <h4 class="left">Create Reference Order</h4>
                <div class="right-align" style="margin-top: 20px;">
                    <input type="button" class="btn" data-ng-show="!showFulfillmentDateSelection && showMenuItemList"
                           value="BACK" data-ng-click="clearRequestOrder()"/>
                    <input type="button" class="btn" data-ng-hide="showFulfillmentDateSelection || showMenuItemList"
                           value="BACK" data-ng-click="backToMenuItemList()"/>
                </div>

            </div>
        </div>
    </div>

    <div class="row">
        <div class="col s12 menuItemList" data-ng-show="showFulfillmentDateSelection && !showMenuItemList">
            <div class="form-element">
                <div class="row">
                    <div class="col s2"><label>Fulfillment date</label></div>
                    <div class="col s4"><input input-date type="text" name="created" id="inputCreated"
                                               ng-model="fulfillmentDate"
                                               container="" format="yyyy-mm-dd" select-years="1"
                                               min="{{minRefOrderFulFillmentDate}}"
                                               max="{{maxRefOrderFulFillmentDate}}"
                                               data-ng-change="setDates(fulfillmentDate, noOfDays)"
                                               data-ng-disabled="regularOrderingEvent != null"/></div>
                    <div class="col s6"><label class="highlight-data">{{fulfillmentDay}}</label></div>
                </div>
                <div class="form-element">
                    <div class="row">
                        <div class="col s2"><label>Ordering Days</label></div>
                        <div class="col s4"><input type="number" min="1" max="7" placeholder="No Of Days"
                                                   name="noOfDays"
                                                   id="noOfDays" ng-model="noOfDays"
                                                   data-ng-change="setDates(fulfillmentDate, noOfDays)"
                                                   data-ng-disabled="regularOrderingEvent != null"/></div>
                    </div>
                </div>
                <div class="form-element">
                    <div class="row">
                        <div class="col s2"><label>Order Will Last Until</label></div>
                        <div class="col s4"><label>{{stockLastingDate | date : 'yyyy-MM-dd'}}</label></div>
                        <div class="col s6"><label class="highlight-data">{{stockLastingDay}}</label></div>
                    </div>

                </div>
                <div class="form-element">
                    <div class="row" acl-action="CEREFO">
                        <div class="col s2"><label>Order By WareHouse/ Kitchen</label></div>
                        <div class="col s4"><input type="checkbox" style="position:inherit;opacity:1; width: 20px;height: 20px;"  data-ng-model="raiseBy" data-ng-disabled="raiseBy"></div>
                    </div>
                </div>
                <div class="form-element">
                    <div class="row">
                        <div ng-show="hasCategoryBuffer">
                            <div class="row">
                                <div class="col s2"><label>Category Buffer Percentage</label></div>
                                <div class="col s4">
                                    <input type="number" placeholder="Category Buffer"
                                           id="categoryBuffer" ng-model="categoryBuffer"/>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col s2"><label>Select Category for Buffering</label></div>
                                <div class="col s8">
                                    <select style="width: 100%" data-ng-model="selectedCategories" ui-select2 multiple>
                                        <option data-ng-repeat="category in productCategory | filter: filterCategory track by $index"
                                                value="{{category.detail.id}}">{{category.detail.code}}
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-element">
                    <div class="row">
                        <div class="col-lg-3 ">
                            <label>Select Brand</label>
                        </div>
                        <div class="col-lg-4">
                            <select ng-model="selectedBrandDetails" class='form-control'
                                    data-ng-options="brand as brand.brandName for brand in brandDetails"
                                    data-ng-change="setBrand(selectedBrandDetails)"
                                    data-ng-disabled="regularOrderingEvent != null">
                            </select>
                        </div>
                    </div>
                </div>
                <div class="form-element">
                    <div class="row">
                        <label class="highlight-row" style="margin-top: 20px">Doing ordering for the below days</label>
                        <table class="table table-striped table-bordered">
                            <tbody>
                            <tr ng-repeat="entry in dataEntry"
                                data-ng-class="{'red':entry.dayType=='REMAINING_DAY','green':entry.dayType=='ORDERING_DAY'}">
                                <td>{{entry.dayType}}</td>
                                <td>{{entry.date}}</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>

                </div>

                <div class="form-element">
                    <div class="row">
                        <div class="form-group col-xs-6">
                            <input type="text" class="form-control" data-ng-model="searchText"
                                   placeholder="Search the product"/>
                        </div>
                        <table class="table table-striped table-bordered">
                            <thead style="background-color: #e7e7e7">
                            <th>Product Id</th>
                            <th>Product Name</th>
                            <th data-ng-repeat="dates in dataEntry">{{dates.date}}</th>
                            </thead>
                            <tbody>
                            <tr ng-repeat="expiryEntry in expiryProduct | toArray | filter :searchText" bgcolor="#DAF7A6">
                                <td>{{expiryEntry.productId}}</td>
                                <td>{{expiryEntry.productName}}</td>
                                <td data-ng-repeat="dates in dataEntry">
                                    <input type="number" data-ng-model="expiryEntry[dates.date]" data-ng-disabled="true"/>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="row">
                    <div class="col s12" data-ng-if="(regularOrderingEvent != null || currentUserId == 140199 || currentUserId == 126458|| currentUserId == 120063) && !errorInInstock">
                        <input type="button" class="btn" value="GUIDE ME" data-ng-click="getReferenceQuantitiesV1()"/>
                        <span class="red" data-ng-if="errorInInstock">Error Occurred While getting the day wise Expiries..!</span>
                    </div>
                    <div class="col s12" data-ng-if="regularOrderingEvent == null">
                        <h5 class="red-text">This unit is not enabled through Fountain 9.Please Contact your superior...!</h5>
                    </div>
                </div>
            </div>
        </div>
        <div class="col s12 menuItemList" data-ng-show="!showFulfillmentDateSelection && showMenuItemList">
            <h5 class="red-text" style="text-align: center;" data-ng-if="totalSale != undefined && totalSale == 0">No
                Suggestions Received From Fountain 9..!</h5>
            <h5 class="red-text" style="text-align: center;" data-ng-if="totalSale != undefined && totalSale == -1">
                Please Check your Calendar(Order Schedule)</h5>
            <table style="width: 100%; border: 3px solid white;" ng-if="dineInSale != null">
                <tr style="background: #26a69a">
                    <td colspan="2" class="sales-header">Chaayos</td>
                    <td colspan="2" class="sales-header">Ghee And Turmeric</td>
                    <td colspan="2" class="sales-header">Desi Canteen</td>
                </tr>
                <tr>
                    <td class="center-text-table">Dine In</td>
                    <td class="center-text-table" style="border-right: 3px solid white;">Delivery</td>
                    <td class="center-text-table">Dine In</td>
                    <td class="center-text-table" style="border-right: 3px solid white;">Delivery</td>
                    <td class="center-text-table">Dine In</td>
                    <td class="center-text-table">Delivery</td>
                </tr>
                <tr>
                    <td class="center-text-table">{{dineInSale["Chaayos"] || '0'}}</td>
                    <td class="center-text-table" style="border-right: 3px solid white;">
                        {{deliverySale["Chaayos"] || '0'}}
                    </td>
                    <td class="center-text-table">{{dineInSale["Ghee and Turmeric"] || '0'}}</td>
                    <td class="center-text-table" style="border-right: 3px solid white;">
                        {{deliverySale["Ghee and Turmeric"] || '0'}}
                    </td>
                    <td class="center-text-table">{{dineInSale["Desi Canteen"] || '0'}}</td>
                    <td class="center-text-table">{{deliverySale["Desi Canteen"] || '0'}}</td>
                </tr>
            </table>
            <ul class="collapsible" data-collapsible="accordion" watch>
                <li ng-repeat="category in menuCategories track by category.id">
                    <div class="collapsible-header categoryItem">
                        <div class="col s11">{{category.name}}</div>
                        <div class="col s1 center-align">&#9660;</div>
                    </div>
                    <div class="collapsible-body" style="background:#eee;">
                        <ul>
                            <ul class="collapsible" data-collapsible="accordion">
                                <table>
                                    <tr class='table-header'>
                                        <td>Menu Product</td>
                                        <td data-ng-repeat="dates in dataEntry | filter :{dayType : 'ORDERING_DAY'}">
                                            {{dates.date}}(O)
                                        </td>
                                        <td>Requesting</td>
                                        <td data-ng-repeat="dates in dataEntry | filter :{dayType : 'REMAINING_DAY'}">
                                            {{dates.date}}(R)
                                        </td>
                                        <td>Remaining Day Qty</td>
                                        <td>Ordering Day Suggested Qty</td>
                                    </tr>
                                    <tr ng-repeat="product in category.productList | orderBy:'name' track by $index">
                                        <td>
                                            <div>{{product.productName}}<span
                                                    data-ng-if="product.dimension!='None'">({{product.dimension}})</span>
                                            </div>
                                        </td>
                                        <td data-ng-repeat="dates in dataEntry | filter :{dayType : 'ORDERING_DAY'}">
                                            <div>{{product.dateOrderings[dates.date] || "0"}}</div>
                                        </td>
                                        <td>
                                            <div><input type="number" data-ng-if="!product.supportsVariantLevelOrdering"
                                                        placeholder="Quantity" ng-model="product.requestedQuantity"
                                                        data-ng-change="updateMenuProductQty(product)"
                                                        data-ng-disabled="regularOrderingEvent != null"/>
                                                <span data-ng-if="product.supportsVariantLevelOrdering">Quantity: {{product.requestedQuantity}}</span>
                                            </div>
                                        </td>
                                        <td data-ng-repeat="dates in dataEntry | filter :{dayType : 'REMAINING_DAY'}">
                                            <div>{{product.dateRemaining[dates.date] || "0"}}</div>
                                        </td>
                                        <td>
                                            <div>{{product.saleQuantity || "0"}}</div>
                                        </td>
                                        <td>
                                            <div>{{product.quantity || "0"}}</div>
                                        </td>
                                    </tr>
                                </table>
                                <div data-ng-if="product.supportsVariantLevelOrdering" class="collapsible-body"
                                     style="background:#eee;">
                                    <ul>
                                        <li data-ng-repeat="variant in product.variants track by $index"
                                            data-ng-if="variant.variantLevelOrdering" class="productListItem">
                                            <div class="row">
                                                <div class="col s6">{{variant.name}} <span
                                                        data-ng-if="variant.dimension!='None'">({{variant.dimension}})</span>
                                                </div>
                                                <div class="col s6">
                                                    <input type="number" placeholder="Quantity"
                                                           ng-model="variant.orderedQuantity"
                                                           data-ng-change="updateProductQuantity(product, variant)"/>
                                                </div>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                            </ul>
                        </ul>
                    </div>
                </li>
            </ul>
            <div class="row">
                <div class="col s12">
                    <input type="button" class="btn" value="BACK" data-ng-click="clearRequestOrder()"/>
                    <input type="button" class="btn" value="RESET" data-ng-click="resetMenuItem()"/>
                    <input type="button" class="btn" data-ng-disabled="totalSale == 0 || totalSale == -1" value="NEXT"
                           data-ng-click="createSCMProductList()"/>
                </div>
            </div>
        </div>
        <div class="col s12 menuItemList" data-ng-hide="showFulfillmentDateSelection || showMenuItemList">

            <div class="row" style="margin-bottom: 10px;">
                <div class="row">
                    <div class="col s2"><label>Ordering Days</label> {{noOfDays}}</div>
                    <div class="col s4"><label>Fulfillment Date</label> {{fulfillmentDate | date : 'dd-MM-yyyy'}}
                        ({{fulfillmentDay}})
                    </div>
                    <div class="col s4"><label>Order Will Last Until</label>{{stockLastingDate | date :
                        'dd-MM-yyyy'}}
                        ({{stockLastingDay}})
                    </div>
                    <div class="col s2" data-ng-if="currentUser == 140199 || currentUser == 127707 || currentUser == 120063">
                        <button data-ng-click="viewDetailedExpiries()" tooltipped data-tooltip="View Detailed Expiry"
                                class="btn btn-medium green">Expiries <i class="material-icons">data_thresholding</i></button>
                    </div>
                </div>
                <h4 data-ng-if="!showOnlyWarehouseItems">Kitchen Items</h4>
                <ul class="collection z-depth-1-half" style="font-size: 12px;" data-ng-if="!showOnlyWarehouseItems">
                    <li class="collection-item list-head">
                        <div class="row">
                            <!--<div class="col s1">Fulfilled By</div>-->
                            <div class="col s2">Product Name (Unit Of measure)</div>
                            <div class="col s1">Sub Category</div>
                            <div class="col s1">
                                In Stock
                            </div>
                            <div class="col s1">
                                In Transit
                            </div>
                            <div class="col s1">Probable Sale</div>
                            <div class="col s1">Predicted</div>
                            <div class="col s1">Suggested</div>
                            <div class="col s1">Ordering</div>
                            <div class="col s1">Reason</div>
                            <div class="col s1">Final Ordering Qty</div>
                            <div class="col s1">Packaging Name</div>
                        </div>
                    </li>
                    <li data-ng-repeat="scmProduct in scmProductList | filter:{selectedFulfillmentType:'KITCHEN'} | orderBy:['-critical','name'] track by $index"
                        data-ng-class="{'red lighten-3 collection-item': scmProduct.discontinuedProduct,'collection-item' : !scmProduct.discontinuedProduct}">
                        <div class="row" title="ingredient in : {{scmProduct.menuProductCount}} Menu Products : {{scmProduct.menuProducts}}">
                            <!--ng-class="{externalCategoryColor: scmProduct.selectedFulfillmentType == 'EXTERNAL',
                            warehouseCategoryColor: scmProduct.selectedFulfillmentType == 'WAREHOUSE',
                            kitchenCategoryColor: scmProduct.selectedFulfillmentType == 'KITCHEN'}"-->
                            <!--<div class="col s1">{{scmProduct.selectedFulfillmentType}}</div>-->
                            <div class="col s2">
                                <a data-ng-click="showPreview($event, scmProduct.id,'PRODUCT')">{{scmProduct.name}} ({{scmProduct.unitOfMeasure}})</a>
                                <span data-ng-if="scmProduct.discontinuedProduct">*</span>
                            </div>
                            <div class="col s1">{{scmProduct.subCategoryName}}</div>
                            <div class="col s1" title="Expiry Product : {{totalDayWiseExpiry[scmProduct.id]}}">
                                {{scmProduct.checkStockAtHand.totalStock || '0'}}
                            </div>
                            <div class="col s1" title="Expiry Product : {{totalDayWiseExpiry[scmProduct.id]}}">
                                {{scmProduct.checkInTransit.totalStock || '0'}}
                            </div>
                            <div class="col s1">{{scmProduct.saleQuantity | number : 2}}</div>
                            <div class="col s1">{{scmProduct.predictedQuantity | number : 2}}</div>
                            <div class="col s1" data-ng-if="scmProduct.f9ScmSuggestion == undefined || scmProduct.f9ScmSuggestion == null">
                                {{scmProduct.suggestedQuantity | number : 2}}
                            </div>
                            <div class="col s1" data-ng-if="scmProduct.f9ScmSuggestion != undefined && scmProduct.f9ScmSuggestion != null">
                                {{scmProduct.f9ScmSuggestion | number : 2}}
                                <s>({{scmProduct.suggestedQuantity | number : 2}})</s></div>
                            <div class="col s1 orderingColumnHighlight">
                                {{scmProduct.orderingQuantity | number : 2}}
                            </div>
                            <div class="col s1 center" data-ng-if="scmProduct.critical != undefined && !scmProduct.critical">-</div>
                            <div class="col s1" data-ng-if="scmProduct.critical != undefined && scmProduct.critical">
                                <select data-ng-disabled="true" class="input-field" ng-model="scmProduct.reason" style="margin-top: 0px; height: 2rem;" data-ng-options="reason as reason for reason in reasonsOfF9"></select>
                            </div>
                            <div class="col s1" data-ng-class="{'yellow' : scmProduct.critical}">
                                <input type="text" data-ng-model="scmProduct.packagingQuantity" data-ng-if="scmProduct.critical != undefined && !scmProduct.critical"
                                       data-ng-change="updateOrderingQty(scmProduct)"/>
                                <input type="text" data-ng-model="scmProduct.packagingQuantity" data-ng-if="scmProduct.critical != undefined && scmProduct.critical"
                                       data-ng-disabled="scmProduct.reason == undefined || scmProduct.reason == null"
                                       data-ng-change="updateOrderingQty(scmProduct)"/>
                        </div>
                        <div class="col s1">{{scmProduct.packagingName}}</div>
                        </div>
                    </li>
                </ul>
                <div class="col s12" data-ng-if="showOnlyWarehouseItems">
                    <div class="col s4">
                        <h4>Warehouse Items</h4>
                    </div>
<!--                    <div class="col s8" style="margin-top: 20px;">-->
<!--                        <input id="whOrdering" type="checkbox" data-ng-model="warehouseordering" data-ng-change="hideWareHouseItems(warehouseordering)">-->
<!--                        <label for="whOrdering">Order Items From Warehouse</label>-->
<!--                    </div>-->
                </div>
                <ul class="collection z-depth-1-half" style="font-size: 12px;" data-ng-if="showOnlyWarehouseItems">
                    <li class="collection-item list-head">
                        <div class="row">
                            <!--<div class="col s1">Fulfilled By</div>-->
                            <div class="col s2">Product Name (Unit Of measure)</div>
                            <div class="col s1">Sub Category</div>
                            <div class="col s1">
                                In Stock
                            </div>
                            <div class="col s1">
                                In Transit
                            </div>
                            <div class="col s1">Probable Sale</div>
                            <div class="col s1">Predicted</div>
                            <div class="col s1">Suggested</div>
                            <div class="col s1">Ordering</div>
                            <div class="col s1">Reason</div>
                            <div class="col s1">Final Ordering Qty</div>
                            <div class="col s1">Packaging Name</div>
                        </div>
                    </li>
                    <li class="collection-item"
                        data-ng-repeat="scmProduct in scmProductList | filter:{selectedFulfillmentType:'WAREHOUSE'} | orderBy:['-critical','name'] track by $index"
                        data-ng-class="{'red lighten-3 collection-item': scmProduct.discontinuedProduct,'collection-item' : !scmProduct.discontinuedProduct}">
                        <div class="row" title="Ingredient in : {{scmProduct.menuProductCount}} Menu Products : {{scmProduct.menuProducts}}">
                            <!--ng-class="{externalCategoryColor: scmProduct.selectedFulfillmentType == 'EXTERNAL',
                            warehouseCategoryColor: scmProduct.selectedFulfillmentType == 'WAREHOUSE',
                            kitchenCategoryColor: scmProduct.selectedFulfillmentType == 'KITCHEN'}"-->
                            <!--<div class="col s1">{{scmProduct.selectedFulfillmentType}}</div>-->
                            <div class="col s2">
                                <a data-ng-click="showPreview($event, scmProduct.id,'PRODUCT')">{{scmProduct.name}} ({{scmProduct.unitOfMeasure}})</a>
                                <span data-ng-if="scmProduct.discontinuedProduct">*</span>
                            </div>
                            <div class="col s1">{{scmProduct.subCategoryName}}</div>
                            <div class="col s1" title="Expiry Product : {{totalDayWiseExpiry[scmProduct.id]}}">
                                {{scmProduct.checkStockAtHand.totalStock || '0'}}
                            </div>
                            <div class="col s1" title="Expiry Product : {{totalDayWiseExpiry[scmProduct.id]}}">
                                {{scmProduct.checkInTransit.totalStock || '0'}}
                            </div>
                            <div class="col s1">{{scmProduct.saleQuantity | number : 2}}</div>
                            <div class="col s1">{{scmProduct.predictedQuantity | number : 2}}</div>
                            <div class="col s1" data-ng-if="scmProduct.f9ScmSuggestion == undefined || scmProduct.f9ScmSuggestion == null">
                                {{scmProduct.suggestedQuantity | number : 2}}
                            </div>
                            <div class="col s1" data-ng-if="scmProduct.f9ScmSuggestion != undefined && scmProduct.f9ScmSuggestion != null">
                                {{scmProduct.f9ScmSuggestion | number : 2}}
                                <s>({{scmProduct.suggestedQuantity | number : 2}})</s></div>
                            <div class="col s1 orderingColumnHighlight">
                                {{scmProduct.orderingQuantity | number : 2}}
                            </div>
                            <div class="col s1 center" data-ng-if="scmProduct.critical != undefined && !scmProduct.critical">-</div>
                            <div class="col s1" data-ng-if="scmProduct.critical != undefined && scmProduct.critical">
                                <select data-ng-disabled="true" class="input-field" ng-model="scmProduct.reason" style="margin-top: 0px; height: 2rem;" data-ng-options="reason as reason for reason in reasonsOfF9"></select>
                            </div>
                            <div class="col s1" data-ng-class="{'yellow' : scmProduct.critical}">
                                <input type="text" data-ng-model="scmProduct.packagingQuantity" data-ng-if="scmProduct.critical != undefined && !scmProduct.critical"
                                       data-ng-change="updateOrderingQty(scmProduct)"/>
                                <input type="text" data-ng-model="scmProduct.packagingQuantity" data-ng-if="scmProduct.critical != undefined && scmProduct.critical"
                                       data-ng-disabled="scmProduct.reason == undefined || scmProduct.reason == null"
                                       data-ng-change="updateOrderingQty(scmProduct)"/>
                            </div>
                            <div class="col s1">{{scmProduct.packagingName}}</div>
                        </div>
                    </li>
                </ul>
            </div>
            <div class="row">
                <div class="col s12">
                    <div class="form-element">
                        <label>Comment(optional)</label>
                        <textarea data-ng-model="comment"></textarea>
                    </div>
                    <div class="form-element">
                        <input type="button" class="btn" value="BACK" data-ng-click="backToMenuItemList()"/>
                        <!--<input type="button" class="btn" value="SAVE" data-ng-click="sendReferenceOrder('INITIATED')" />-->
                        <input type="button" class="btn" value="SUBMIT"
                               data-ng-click="sendReferenceOrder('CREATED')"/>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/ng-template" id="expiriesModal.html" class="modal-large">
    <div class="modal-content" style="overflow-x: auto;">
        <div class="row">
            <input type="text" data-ng-model="searchProducts" placeholder="Enter Product Name to search">
        </div>
        <div class="row">
            <table class="bordered striped">
                <thead>
                    <tr>
                        <th data-ng-repeat="column in colHeaders">
                            {{column}}
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <tr data-ng-repeat="row in displayRows | filter:searchProducts">
                        <td>{{row.productName}}</td>
                        <td data-ng-repeat="currentDate in dates">
                            {{row[currentDate.date + "_suggested"]}},
                            {{row[currentDate.date + "_ordering"]}},
                            {{row[currentDate.date + "_inStock"]}},
                            {{row[currentDate.date + "_inTransit"]}}
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn btn-medium red right" data-ng-click="close()">Close</button>
    </div>
</script>
