<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<div data-ng-init="init()">
    <div id="productDef" data-ng-model="productDefinition" class="row scm-form">
        <div class="row">
            <div class="col s6">
                <div class="left">
                    <h5 style="margin:0px;">Product Definition</h5>
                </div>
            </div>
            <div data-ng-if="editMode" class="col s6">
                <div class="right">
                    <button class="btn" data-ng-click="goBack(productDefinition.productId)">Back</button>
                </div>
            </div>
        </div>

        <form id="product" name="productForm" class="white z-depth-3 scm-form" novalidate>
            <div class="row">
                <div class="col s12 m6 l6">
                    <div class="form-element">
                        <label class=" black-text" for="productName">Product Name</label>
                        <input id="productName" name="productName" data-ng-model="productDefinition.productName" type="text" ng-maxlength="100" required>
                        <p ng-show="productForm.productName.$error.maxlength" class="errorMessage">Product name is too large.</p>
                        <p ng-show="productForm.productName.$error.required" class="errorMessage">Product name is required.</p>
                    </div>
                    <!--<div class="form-element">
                        <label class="black-text" for="productCode">Product Code</label>
                        <input id="productCode" name="productCode" type="text" data-ng-model="productDefinition.productCode" ng-maxlength="30" required/>
                        <p ng-show="productForm.productCode.$error.maxlength" class="errorMessage">Product code is too large.</p>
                        <p ng-show="productForm.productCode.$error.required" class="errorMessage">Product code is required.</p>
                    </div>-->
                    <div class="form-element">
                        <label class="black-text active" for="taxCategory">Tax Category</label>
                        <select ui-select2 id="taxCategory" name="taxCategory" data-ng-model="taxCategory"
                                data-ng-options="tax as (tax.code + '-'+ tax.desc) for tax in taxCodes track by tax.code" data-ng-change="onTaxCategoryChange()"
                                required>
                        </select>
                        <a data-ng-show="taxCategory != null" data-ng-click="showTaxDetail()">?</a>
                        <p ng-show="productForm.taxCategory.$error.required" class="errorMessage">Product Tax Category is required.</p>
                    </div>
                    <div class="form-element">
                        <label class=" black-text" for="description">Product Description</label>
                        <textarea id="description" name="description" data-ng-model="productDefinition.productDescription" data-ng-maxlength="300" required></textarea>
                        <p ng-show="productForm.description.$error.maxlength" class="errorMessage">Product description is too large.</p>
                        <p ng-show="productForm.description.$error.required" class="errorMessage">Product description is required.</p>
                    </div>
                    <div class="form-element">
                        <label class="black-text active">Fulfillment Type</label>
                        <select id="fulfillmentType" name="fulfillmentTypes"
                                ng-model="productDefinition.fulfillmentType"
                                ng-change="selectFulfilmentType(productDefinition.fulfillmentType)"
                                data-placeholder="Select a Fulfillment Type">
                            <option ng-repeat="fulfillmentType in fulfillmentTypes  | filter: removeSelf"
                                    value="{{fulfillmentType}}">{{fulfillmentType}}</option>
                        </select>
                    </div>
                    <div class="form-element" data-ng-if="showDefaultType">
                        <label class="black-text active">
                            Default Fulfillment Type
                            <button class="btn btn-xs-small right"
                                    ng-click="openDerivedMappingModal(productDefinition.defaultFulfillmentType)">Edit Mappings</button>
                        </label>
                        <select id="defaultType" name="defaultFulfillmentType"
                                ng-model="productDefinition.defaultFulfillmentType"
                                data-ng-change="mapDerivedMapping(productDefinition.defaultFulfillmentType)"
                                data-placeholder="Select Default Fulfillment Type">
                            <option ng-repeat="fulfillmentType in fulfillmentTypes | filter: byType"
                                    value="{{fulfillmentType}}">{{fulfillmentType}}</option>
                        </select>
                    </div>
                    <div class="form-element">
                        <label class="black-text active" for="stockFrequency">Stocking Frequency</label>
                        <select id="stockFrequency" name="stockFrequency"
                                data-ng-disabled="productDefinition.categoryDefinitionId == 3"
                                data-ng-model="productDefinition.stockKeepingFrequency"
                                data-ng-options="stockKeepingFrequency as stockKeepingFrequency for stockKeepingFrequency in frequencies track by stockKeepingFrequency"
                                required>
                        </select>
                        <p ng-show="productForm.stockFrequency.$error.required" class="errorMessage">Product stock frequency is required.</p>
                    </div>
                    <div class="form-element">
                        <label class="black-text active" for="productCategory">Product Category</label>
                        <select id="productCategory" name="productCategory" data-ng-model="productDefinition.categoryDefinitionId"
                                data-ng-change="selectCategory()"
                                data-ng-options="category.categoryId as category.categoryName for category in categories"
                                data-ng-disabled="disableEdit" required></select>
                        <p ng-show="productForm.productCategory.$error.required" class="errorMessage">Product category is required.</p>
                    </div>
                    <!--data-ng-options="profile.profileId as profile.profileName for profile in profileDefinitions  track by profile.profileId"-->
                    <div class="form-element" data-ng-if="productDefinition.categoryDefinitionId != null &&  productDefinition.categoryDefinitionId == 3" >
                        <label class="black-text active" for="profileName">Select Profile</label>
                        <select id="profileName" name="profileName" data-ng-model="productDefinition.profileId"
                                data-ng-options="profile.profileId as profile.profileName for profile in profileDefinitions" required>
                            <!--<option data-ng-repeat="profile in profileDefinitions" value="{{profile.profileId}}">{{profile.profileName}}</option>-->
                        </select>
                        <p ng-show="productForm.profileName.$error.required" class="errorMessage">Profile is required in case of Fixed Assets.</p>
                    </div>
                    <div class="form-element">
                        <label class="black-text active" for="subCategory">Product Sub Category</label>
                        <select id="subCategory" name="subCategoryName" data-ng-model="productDefinition.subCategoryDefinitionId"
                                data-ng-options="category.subCategoryId as category.subCategoryName for category in subCategories"
                                data-ng-change="updateShelfLifeRange(productDefinition.subCategoryDefinitionId)"
                                data-ng-disabled="disableEdit" required>
                        </select>
                        <p ng-show="productForm.subCategoryName.$error.required" class="errorMessage">Product sub category is required.</p>
                    </div>
                    <div data-ng-show="productDefinition.subCategoryDefinitionId!=null" class="form-element">
                        <div class="form-element">
                            <input id="hasShelfLife" data-ng-model="hasShelfLife" type="checkbox" data-ng-click="clickHasShelfLife()"/>
                            <label class="black-text " for="hasShelfLife">Has Shelf Life</label>
                        </div>
                        <p ng-show="hasShelfLife && productDefinition.minRange != null" class=" yellow-text text-darken-3">Range For This Sub Category is {{productDefinition.minRange}} - {{productDefinition.maxRange}}</p>
                        <p ng-show="hasShelfLife && productDefinition.minRange == null"  style="color: darkblue">Range For This Sub Category is Not Set</p>
                        <label class="black-text active" for="shelfLifeInDays">Shelf Life(in days)</label>
                        <input type="number" id="shelfLifeInDays" data-ng-disabled="!hasShelfLife" name="shelfLifeInDays"
                               data-ng-model="productDefinition.shelfLifeInDays" data-ng-maxlength="5" data-ng-min="productDefinition.minRange" data-ng-max="productDefinition.maxRange"  required>
                        <p ng-show="productForm.shelfLifeInDays.$error.maxlength" class="errorMessage">Product shelf life is too large.</p>
                        <p ng-show="productForm.shelfLifeInDays.$error.required" class="errorMessage">Product shelf life is required.</p>
                        <p ng-show="productForm.shelfLifeInDays.$error.min" class="errorMessage">Product shelf life is too small.</p>
                        <p ng-show=" hasShelfLife && productDefinition.maxRange !=null &&(productDefinition.shelfLifeInDays < productDefinition.minRange ||
                         productDefinition.shelfLifeInDays > productDefinition.maxRange)" class="errorMessage">Product shelf life is Not In Range</p>
                    </div>
                    <div class="form-element" >
                        <label class="black-text active" for="division">Select Division</label>
                        <select id="division" name="division" data-ng-model="productDefinition.divisionId"
                                data-ng-options="division.listDetailId as division.name for division in divisions  " required>
                            <!--<option data-ng-repeat="profile in profileDefinitions" value="{{profile.profileId}}">{{profile.profileName}}</option>-->
                        </select>
                        <p ng-show="productForm.division.$error.required" class="errorMessage">Division is required.</p>
                    </div>

                    <div class="form-element" >
                        <label class="black-text active" for="department">Select Department</label>
                        <select id="department" name="department" data-ng-model="productDefinition.departmentId"
                                data-ng-options="department.listDetailId as department.name for department in departments " required>
                        </select>
                        <p ng-show="productForm.department.$error.required" class="errorMessage">Department is required.</p>
                    </div>

                    <div class="form-element" >
                        <label class="black-text active" for="classification">Select Classification</label>
                        <select id="classification" name="classification" data-ng-model="productDefinition.classificationId"
                                data-ng-options="classification.listDetailId as  classification.name for classification in classifications  "
                                data-ng-change="selectedClassification(productDefinition.classificationId)" required>
                        </select>
                        <p ng-show="productForm.classification.$error.required" class="errorMessage">Classification is required.</p>
                    </div>
                    <div class="form-element"  data-ng-show="productDefinition.classificationId!=null">
                        <label class="black-text active" for="subClassification">Select Sub Classification</label>
                        <select id="subClassification" name="subClassification" data-ng-model="productDefinition.subClassificationId"
                                data-ng-options="subClassification.listTypeId as subClassification.name for subClassification in subClassifications  ">
                            <!--<option data-ng-repeat="profile in profileDefinitions" value="{{profile.profileId}}">{{profile.profileName}}</option>-->
                        </select>
                    </div>

                </div>
                <div class="col s12 m6 l6">
                    <div class="form-element">
                        <label class=" black-text active" for="uom">Unit of Measurement</label>
                        <select ui-select2="{allowClear:true,placeholder:'Unit of Measurement'}" id="uom" name="uom" data-ng-model="productDefinition.unitOfMeasure" data-ng-options="uom as uom for uom in uomMetadata" data-ng-disabled="disableEdit" required>
                        </select>
                        <p ng-show="productForm.uom.$error.required" class="errorMessage">Product unit of measure is required.</p>
                    </div>

                    <div class="form-element">
                        <label class=" black-text active" for="varianceType">Variance Type</label>
                        <select id="varianceType" name="varianceType" data-ng-model="productDefinition.varianceType"
                                data-ng-options="type as type for type in varianceTypes" required>
                        </select>
                        <p ng-show="productForm.varianceType.$error.required" class="errorMessage">Variance Type is required.</p>
                    </div>

                    <div class="form-element">
                        <label class=" black-text active" for="kitchenVarianceType">Kitchen/WH Variance Type</label>
                        <select id="kitchenVarianceType" name="kitchenVarianceType" data-ng-model="productDefinition.kitchenVarianceType"
                                data-ng-options="type as type for type in varianceTypes" required>
                        </select>
                        <p ng-show="productForm.varianceType.$error.required" class="errorMessage">Kitchen/WH Variance Type is required.</p>
                    </div>

                    <!--<div class="form-element">-->
                        <!--<label class="black-text active" for="unitPrice">Unit price</label>-->
                        <!--<input type="number" id="unitPrice" name="unitPrice" data-ng-maxlength="10" min="0" data-ng-model="productDefinition.unitPrice" required />-->
                        <!--<p ng-show="productForm.unitPrice.$error.required" class="errorMessage">Product unit price is required.</p>-->
                        <!--<p ng-show="productForm.unitPrice.$error.maxlength" class="errorMessage">Product unit price is too large.</p>-->
                        <!--<p ng-show="productForm.unitPrice.$error.min" class="errorMessage">Product unit price is too small.</p>-->
                    <!--</div>-->
                    <div class="form-element" >
                        <label class="black-text active" for="negotiatedUnitPrice">Negotiated unit price</label>
                        <input type="number" id="negotiatedUnitPrice" name="negotiatedUnitPrice" data-ng-disabled="editMode"
                               data-ng-maxlength="10" min="0" data-ng-model="productDefinition.negotiatedUnitPrice" required />
                        <p ng-show="productForm.negotiatedUnitPrice.$error.required" class="errorMessage">Product negotiated unit price is required.</p>
                        <p ng-show="productForm.negotiatedUnitPrice.$error.maxlength" class="errorMessage">Product negotiated unit price is too large.</p>
                        <p ng-show="productForm.negotiatedUnitPrice.$error.min" class="errorMessage">Product negotiated unit price is too small.</p>
                    </div>
                    <div class="form-element">
                        <input id="hasCase" data-ng-model="productDefinition.hasCase" type="checkbox" />
                        <label class="black-text" for="hasCase">Has Case</label>
                    </div>
                    <div class="form-element">
                        <input id="hasInner" data-ng-model="productDefinition.hasInner" type="checkbox" />
                        <label class="black-text " for="hasInner">Has Inner</label>
                    </div>
                    <div class="form-element">
                        <input id="recipeRequired" data-ng-model="productDefinition.recipeRequired" type="checkbox" />
                        <label class="black-text " for="recipeRequired">Recipe Required</label>
                    </div>
                    <div class="form-element">
                        <input id="looseOrdering" data-ng-model="productDefinition.supportsLooseOrdering" type="checkbox" />
                        <label class="black-text " for="looseOrdering">Supports Loose Ordering</label>
                    </div>
                    <div class="form-element">
                        <input id="variantLevelOrdering"
                               data-ng-model="productDefinition.variantLevelOrdering" type="checkbox" />
                        <label class="black-text" for="variantLevelOrdering">Variant Level Ordering</label>
                    </div>
                   <!--  <div class="form-element" data-ng-if="productDefinition.categoryDefinition.id == undefined ||
                    productDefinition.categoryDefinition.id == 1 || productDefinition.categoryDefinition.id == 2">-->
                    <div class="form-element">
                        <input id="participatesInRecipe"
                               data-ng-model="productDefinition.participatesInRecipe" type="checkbox" />
                        <label class="black-text" for="participatesInRecipe">Participates in Recipe</label>
                    </div>
                    <div class="form-element">
                        <input id="participatesInCafeRecipe"
                               data-ng-model="productDefinition.participatesInCafeRecipe" type="checkbox" />
                        <label class="black-text" for="participatesInCafeRecipe">Participates in Cafe Recipe</label>
                    </div>
                    <div class="form-element">
                        <input id="supportsSpecialOrdering"
                               data-ng-model="productDefinition.supportsSpecialOrdering" type="checkbox" />
                        <label class="black-text" for="supportsSpecialOrdering">Supports Specialized Ordering</label>
                    </div>
                    <div class="form-element">
                        <input id="availableAtCafe"
                               data-ng-model="productDefinition.availableAtCafe" type="checkbox" />
                        <label class="black-text" for="availableAtCafe">Available At Cafe</label>
                    </div>
                    <div class="form-element">
                        <input id="interCafeTransfer"
                               data-ng-model="productDefinition.interCafeTransfer" type="checkbox" />
                        <label class="black-text" for="interCafeTransfer">Inter Cafe Transfer</label>
                    </div>
                    <div class="form-element">
                        <input id="availableForCafeInventory"
                               data-ng-model="productDefinition.availableForCafeInventory" type="checkbox" />
                        <label class="black-text" for="availableForCafeInventory">Available For Cafe Inventory</label>
                    </div>
                    <div class="form-element">
                        <input id="assetOrdering"
                               data-ng-model="productDefinition.assetOrdering" type="checkbox" />
                        <label class="black-text " for="assetOrdering">Asset Ordering</label>
                    </div>
                    <div class="form-element">
                        <input id="autoProduction"
                               data-ng-model="productDefinition.autoProduction" type="checkbox" />
                        <label class="black-text " for="autoProduction">Auto Production</label>
                    </div>
                    <div class="form-element" data-ng-show="productDefinition.categoryDefinitionId == 3">

                        <input id="is_BulkGRAllowed"
                               data-ng-model="productDefinition.bulkGRAllowed" type="checkbox" />
                        <label class="black-text " for="is_BulkGRAllowed">Bulk GR allowed</label>
                    </div>

                </div>
            </div>
        </form>
        <div class="row" style="margin-top:10px;">
            <div class="col s12">
                <button data-ng-if="!editMode && productForm.$valid" data-ng-click="setProduct()" class="btn right" acl-action="SPAAPD"> Create
                    <i class="material-icons right">send</i>
                </button>
                <button data-ng-if="editMode && productForm.$valid" data-ng-click="setProduct()" class="btn right" acl-action="SPAUPP"> Edit
                    <i class="material-icons right">send</i>
                </button>
            </div>
        </div>
    </div>
</div>


<script type="text/ng-template" id="derivedMapping.html">
    <div data-ng-init="initMappings()">
        <h5>Map Derived Mappings To Units</h5>
        <div style="overflow-y:scroll; max-height:250px;">
            <h6>By Regions</h6>
            <ul class="collection">
                <li class="collection-item" data-ng-repeat="(region,units) in unitsByRegion">
                    <div class="row margin0">
                        <div class="col s6">{{region}}</div>
                        <div class="col s6">
                            <select data-ng-options="fulfillment as fulfillment for fulfillment in fulfillTypeWithNull"
                                    ng-model="mappingsByRegion[region]"
                                    data-ng-change="changeMappingsForRegion(region,units,mappingsByRegion[region])"></select>
                        </div>
                    </div>
                </li>
            </ul>

            <h6>By Units</h6>
            <ul class="collection">
                <li class="collection-item" data-ng-repeat="unit in units | orderBy : 'name'">
                    <div class="row margin0">
                        <div class="col s6">{{unit.name}}</div>
                        <div class="col s6">
                            <select data-ng-options="fulfillment as fulfillment for fulfillment in fulfillTypeWithNull"
                                    ng-model="mappings[unit.id].type"
                                    data-ng-change="changeMappings(unit.id,mappings[unit.id].type)"></select>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
    </div>
    <button class="btn right margin-top-10" data-ng-click="submitMappings()">Submit</button>
</script>

