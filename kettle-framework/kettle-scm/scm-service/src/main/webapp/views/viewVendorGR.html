<style>
    #viewGRList .collapsible-header.inverse{
        background-color: #CDDC39;
        color: white;
    }
    #viewGRList .collapsible-header{
        color: #654848;
        background-color: white;
        border: 1px solid #26a69a;
    }
    .scrollabe-table {
        display: block !important;
        overflow-x: auto !important;
        white-space: nowrap !important;
    }
    .scrollabe-table thead tr th{
        min-width: 150px;
    }
    .greyBackground {
        background-color: lightgrey;
    }
</style>
<div class="row" data-ng-init="init()">
<div style="width=100%" id="printHide">
    <!--<svg class="barcode" jsbarcode-format="CODE128" jsbarcode-value="abc123" jsbarcode-textmargin="0" jsbarcode-fontoptions="bold">-->
    <!--</svg>-->
    <div class="searchingCard col s12">
        <div class="row white z-depth-3 custom-listing-li" style="width: 100%;">
            <div class="row">
                <div class="col s12">
                    <h4>View Vendor Receivings</h4>
                </div>
            </div>
            <div class="row">
                <div class="col s2">
                    <label>Select Start date</label>
                    <input input-date type="text" ng-model="startDate" container="" format="yyyy-mm-dd" />
                </div>
                <div class="col s2">
                    <label>Select End date</label>
                    <input input-date type="text" ng-model="endDate" container="" format="yyyy-mm-dd" />
                </div>
                <div class="col s2">
                <label>Good Received Id</label>
                <input type="number" placeholder="GR ID" name="grId" id="grId" ng-model="grId"/>
                </div>
                <div class="col s2">
                    <label>Select Vendor</label>
                    <select id="vendors" ui-select2="{allowClear:true, placeholder: 'Select Vendor'}" ng-options="vendor as vendor.entityName for vendor in vendors"
                            data-ng-change="selectVendor(vendorSelected)" data-ng-model="vendorSelected"></select>
                </div>
                <div class="col s2">
                    <label>Select SKU</label>
                    <select id="skuList" ui-select2="{allowClear:true, placeholder: 'Select SKU'}" ng-options="sku.id as sku.name for sku in skus"
                            data-ng-change="selectSKU(skuSelected)" data-ng-model="skuSelected"></select>
                </div>
                <div class="col s2">
                    <button class="btn margin-top-20" data-ng-click="getGRs()">Find</button>
                </div>
            </div>
            <div class="row" data-ng-show="grsList.length > 0 || grs.length > 0">
                <div class="col s2">
                    <label>Select Gr Type</label>
                    <select ui-select2="{allowClear:true, placeholder: 'Select Type'}" ng-model="selectedType" data-ng-change="filterData()" id="selectedTypes"
                            data-ng-options="type as type for type in availableType">
                    </select>
                </div>
                <div class="col s2">
                    <label>Select Gr Status</label>
                    <select ui-select2="{allowClear:true, placeholder: 'Select Status'}" ng-model="selectedStatus" data-ng-change="filterData()" id="selectedStatus"
                            data-ng-options="status as status for status in grStatus">
                    </select>
                </div>
                <div class="col s3" style="margin-top: 15px;">
                    <label>Select Gr Created By</label>
                    <select ui-select2="{allowClear:true, placeholder: 'Select user'}" ng-model="selectedUser" data-ng-change="filterData()" id="selectedUser"
                            data-ng-options="userName as userName for userName in createdByList">
                    </select>
                </div>
                <div class="col s3" style="margin-top: 15px;">
                    <label>Select Gr By user Id</label>
                    <select ui-select2="{allowClear:true, placeholder: 'Select User Id'}" ng-model="selectedUserId" data-ng-change="filterData()" id="selectedUserId"
                            data-ng-options="userId as userId for userId in createdByIdList">
                    </select>
                </div>
            </div>
            <hr>
            <div class="row" style="padding:30px;color:gray;text-align: center;"
                 data-ng-show="grsList==null || grsList.length==0">
                No Vendor Receivings found for the selected criteria
            </div>
            <div class="col s3 right" data-ng-if="grsList!=null && grsList.length>0">
                <button class="btn margin-top-20" data-ng-click="downloadExcell()" >Download Excell</button>
            </div>
            <div class="row" data-ng-show="grsList!=null && grsList.length>0">
                <div style="padding: 0 22px;">
                    <div style="float: left; margin-right: 15px;">
                        <input id="allGrs" type="checkbox" data-ng-model="selectAllGrs" data-ng-checked="selectAllGrs==true" data-ng-change="selectAllReceiving()"/>
                        <label for="allGrs"style="margin-bottom: 0;margin-top: 6px;">Select All</label>
                    </div>
                    <input type="button" class="btn" value="No payment" acl-action="VGRDP" data-ng-click="setGRsForNoPayment()" />
                </div>
                <ul id="viewGRList" class="col s12" data-collapsible="accordion" watch>
                    <li class="row" data-ng-repeat="gr in grsList track by gr.id">
                        <div class="col s1">
                            <span data-ng-class="{'invisible':gr.toBePaid != true && gr.paymentRequestId == null}">
                                <input id="gr-{{gr.id}}" type="checkbox" data-ng-model="gr.selected"/>
                                <label for="gr-{{gr.id}}"></label>
                            </span>
                        </div>
                        <div class="col s9 collapsible-header waves-effect waves-light lighten-5" data-ng-class="{'inverse':gr.id==grId}">
                            <div class="left">
                                GR No.: <b>{{gr.id}}</b> for {{gr.generatedForVendor.name}} [{{gr.dispatchLocation.city}}]
                                <span class="chip" data-ng-if="gr.invalid">INVALID</span>
                            </div>
                            <div class="right">
                                <span class="chip grey white-text">{{gr.receiptType}}: {{gr.receiptNumber}}</span>
                                <span class="chip grey white-text" data-ng-if="gr.paymentRequestId != null">PR Generated: {{gr.paymentRequestId}}</span>
                                <span data-ng-class="{'chip green white-text':gr.toBePaid == true, 'chip red white-text':gr.toBePaid != true}">To be paid: {{gr.toBePaid?'Yes':'No'}}</span>
                                <span class="chip grey white-text">
                                    {{gr.generationTime | date:'dd/MM/yyyy @ h:mma'}}
                                </span>
                                <span class="chip grey white-text">{{gr.status}}</span>
                                <i class="fa fa-caret-down right"></i>
                            </div>
                        </div>
                        <div class="col s2">
                            <button data-ng-click="printGR(gr)" class="btn">Print</button>
                            <button id="printDiv" print-btn class="btn" data-ng-show="false">Print</button>
                            <button class="btn" data-ng-if="gr.status=='CREATED' && !gr.invalid && gr.canBeCancelled !='N' && gr.paymentRequestId == null && gr.vendorGrType != 'FIXED_ASSET_ORDER'" acl-action="VGRCL" data-ng-click="cancelGR(gr.id)">Cancel</button>
                            <button data-ng-if="isFixedAssetGR(gr) && gr.status == 'INITIATED'" class="btn btn-primary pull-left" href="#modal1" data-ng-click="selectGr(gr)"   modal>Register_Asset</button>
                            <button data-ng-if="gr.debitNote != null && gr.status == 'CREATED'" data-ng-click="downloadDebitNote(gr.debitNote)" class="btn">Debit Note</button>
                        </div>
                        <div class="right">
                            Comment : <span class="chip grey white-text">{{gr.comment}}</span>
                        </div>
                        <div class="respTable collapsible-body">
                            <table class="bordered striped">
                                <thead>
                                <tr>
                                    <th class="center-align">SKU</th>
                                    <th class="center-align">UOM</th>
                                    <th class="center-align">Pkg</th>
                                    <th class="center-align">Pkg Qty</th>
                                    <th class="center-align">Ratio</th>
                                    <th class="center-align">Total Qty</th>
                                    <th class="center-align">Price</th>
                                    <th class="center-align" tooltipped data-tooltip="Amount = (Price * Quantity) + Taxes">Amount*</th>
                                    <th class="center-align">Taxes</th>

                                </tr>
                                </thead>
                                <tbody>
                                <tr data-ng-repeat="item in gr.grItems track by $index">
                                    <td class="center-align"><a data-ng-click="showPreview($event, item.skuId,'SKU')">{{item.skuName}}[{{item.hsn}}]</a></td>
                                    <td class="center-align">{{item.unitOfMeasure}}</td>
                                    <td class="center-align">{{item.packagingName}}</td>
                                    <td class="center-align">{{item.packagingQty}}</td>
                                    <td class="center-align">{{item.conversionRatio}}</td>
                                    <td class="center-align">{{item.receivedQuantity.toFixed(2)}}</td>
                                    <td class="center-align">{{item.unitPrice.toFixed(2)}}</td>
                                    <td class="center-align">{{item.totalCost}}</td>
                                    <td class="center-align">{{item.totalTax}}</td>

                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </div>
    </div>
    <div style="width:100%" id="printSection">
    <div class="col s12">
        <div class="row" style="margin-bottom: 5px;">
            <div class="col s12">
                <p style="text-align: center;"><b><span
                        style="font-size: 16.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">CHAAYOS<br />
					</span></b><b><span
                        style="font-size: 13.0pt; line-height: 115%; font-family: 'Cambria', serif;">{{companyMap[currentPrintGR.companyId].name}}<br />
					</span></b><span
                        style="font-size: 10.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">{{companyMap[currentPrintGR.companyId].registeredAddress.line1}},
						{{companyMap[currentPrintGR.companyId].registeredAddress.line2}}, <br /> {{companyMap[currentPrintGR.companyId].registeredAddress.city}},
						{{companyMap[currentPrintGR.companyId].registeredAddress.state}}, <br /> {{companyMap[currentPrintGR.companyId].registeredAddress.country}},
						{{companyMap[currentPrintGR.companyId].registeredAddress.zipCode}}<br />
					</span><b><span style="font-size: 13.0pt; line-height: 115%; font-family: 'Cambria', serif;">Goods Receipt (GR)</span></b>
                </p>
            </div>
        </div>
        <p style="text-align:center;font-size:21px;margin:0;font-weight:bold;" data-ng-if="currentPrintGR.dispatchLocation.gstStatus!='REGISTERED'">Vendor Not Registered</p>
        <div id="expandedPOView" class="row custom-listing-li" style="padding:5px;">
            <div class="respTable col s12" data-ng-if="currentPrintGR.grItems!=null">
                <table style="margin-bottom: 20px;">
                    <thead class="itemsTable">
                        <tr>
                            <th style="width: 20%">Vendor Details</th>
                            <th style="width: 30%"></th>
                            <th style="width: 20%">Dispatch Details</th>
                            <th style="width: 30%"></th>
                        </tr>
                    </thead>
                    <tbody class="itemsTable borderLess" style="border: #000 1px solid;">
                        <tr>
                            <td>Vendor Name:</td>
                            <td>{{currentPrintGR.generatedForVendor.name}}</td>
                            <td>Dispatch Location</td>
                            <td>{{currentPrintGR.dispatchLocation.city}}</td>
                        </tr>
                        <tr>
                            <td>Vendor Id:</td>
                            <td>{{currentPrintGR.generatedForVendor.id}}</td>
                            <td>Dispatch Address</td>
                            <td>{{currentPrintGR.dispatchLocation.address.line1}}, {{currentPrintGR.dispatchLocation.address.line2}}, {{currentPrintGR.dispatchLocation.address.city}},
                                {{currentPrintGR.dispatchLocation.address.state}}, {{currentPrintGR.dispatchLocation.address.country}}, {{currentPrintGR.dispatchLocation.address.zipcode}}</td>
                        </tr>
                        <tr>
                            <td>Date of GR:</td>
                            <td>{{currentPrintGR.generationTime | date:'dd-MM-yyyy hh:mm:ss':'+0530'}}</td>
                            <td>Dispatch State</td>
                            <td>{{currentPrintGR.dispatchLocation.state}}</td>
                        </tr>
                        <tr>
                            <td>GR No.:</td>
                            <td>{{currentPrintGR.id}}</td>
                            <td>Dispatch State Code</td>
                            <td>{{currentPrintGR.dispatchLocation.address.stateCode}}</td>
                        </tr>
                    </tbody>
                </table>
                <table style="margin-bottom: 20px;">
                    <thead class="itemsTable">
                    <tr>
                        <th style="width: 20%">Receiving Unit Name</th>
                        <th style="width: 20%">{{currentPrintGR.deliveryUnitId.name}}</th>
                        <th style="width: 20%">Receiving Company Name</th>
                        <th style="width: 40%">{{companyMap[currentPrintGR.companyId].name}}</th>
                    </tr>
                    <tr>
                        <th style="width: 20%">Receiving Details</th>
                        <th style="width: 30%"></th>
                        <th style="width: 20%">Document Details</th>
                        <th style="width: 30%"></th>
                    </tr>
                    </thead>
                    <tbody class="itemsTable borderLess" style="border: #000 1px solid;">
                    <tr>
                        <td>Received By:</td>
                        <td>{{currentPrintGR.generatedBy.name}}</td>
                        <td>Document Type</td>
                        <td>{{currentPrintGR.receiptType}}</td>
                    </tr>
                    <tr>
                        <td>Receiving Time:</td>
                        <td>{{currentPrintGR.generationTime | date:'dd-MM-yyyy hh:mm:ss':'+0530'}}</td>
                        <td>Document No.</td>
                        <td>{{currentPrintGR.receiptNumber}}</td>
                    </tr>
                    <tr>
                        <td>Delivery State:</td>
                        <td>{{currentPrintGR.deliveryUnitId.state}}</td>
                        <td>Document Date</td>
                        <td>{{currentPrintGR.grDocumentDate | date:'dd-MM-yyyy hh:mm:ss':'+0530'}}</td>
                    </tr>
                    <tr>
                        <td>Delivery State Code:</td>
                        <td>{{currentPrintGR.deliveryUnitId.stateCode}}</td>
                        <td></td>
                        <td></td>
                    </tr>
                    </tbody>
                </table>
                <h5 style="margin-top:0px;font-size: 18px;">List of Items Received</h5>
                <div class="row margin0 itemsTable">
                    <table class="bordered" style="border-top: 1px solid #d0d0d0;">
                        <thead>
                        <tr>
                            <th class="center-align">SKU ID</th>
                            <th class="center-align">SKU</th>
                            <th class="center-align">Category</th>
                            <th class="center-align">Sub Category</th>
                            <th class="center-align">Price</th>
                            <th class="center-align">Pkg</th>
                            <th class="center-align">Qty</th>
                            <th class="center-align">Total</th>
                            <th class="center-align">Taxes</th>
                            <th class="center-align">Amount</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr data-ng-repeat="(skuId,item) in currentPrintGR.grItems track by $index">
                            <td>{{item.skuId}}</td>
                            <td>{{item.skuName}} [{{item.packagingName}}]</td>
                            <td class="center-align">{{item.category}}</td>
                            <td class="center-align">{{item.subCategory}}</td>
                            <td class="center-align">{{item.unitPrice}}</td>
                            <td class="center-align">{{item.packagingName}}</td>
                            <td class="center-align">{{(item.receivedQuantity/item.conversionRatio).toFixed(2)}}</td>
                            <td class="center-align">{{item.totalCost}}</td>
                            <td class="center-align">
                                <span>{{item.totalTax}}</span>
                                (<span ng-repeat="tax in item.taxes">{{tax.taxName}}@{{tax.percentage}}%</span>)
                            </td>
                            <td class="center-align">{{item.amountPaid}}</td>
                        </tr>
						<tr>
                            <th></th>
                            <th><b>Extra Charges</b></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th class="center-align">{{currentPrintGR.extraCharges.toFixed(2)}}</th>
                        </tr>
                        <tr>
                            <th></th>
                            <th><b>Total</b></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th class="center-align">{{currentPrintGR.total}}</th>
                        </tr>
                        <tr>
                            <th></th>
                            <th><b>Created By</b></th>
                            <th>{{currentPrintGR.generatedBy.name}}</th>
                            <th><b>Approved By</b></th>
                            <th>{{currentPrintGR.approvedBy.name}}</th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <p style="text-align:right;margin-right:20px;font-weight:bold;">Amounts Match: {{currentPrintGR.amountMatched?'Yes':'No'}}</p>
                <p style="text-align:right;margin-right:20px;font-weight:bold;">Comment: {{currentPrintGR.comment}}</p>
            </div>

            <div class="col s12">
                <div class="row margin0">
                    <ul class="col s12">
                    	<h5 style="margin-top:0px;font-size: 18px;">List of PO</h5>
                        <li class="row margin0" data-ng-repeat="po in currentPrintGR.purchaseOrderList track by po.id">
                            <div class="poNumber">
                                {{po.receiptNumber}}
                                <span style="margin-left: 50px;font-weight:bold;">Date: {{po.initiationTime | date:'dd-MM-yyyy hh:mm:ss':'+0530'}}</span>
                                <span class="chip right">{{po.status}}</span>
                            </div>
                            <table class="bordered itemsTable" style="margin-bottom: 20px;">
                                <thead>
                                <tr>
                                    <th class="center-align">SKU ID</th>
                                    <th class="center-align">SKU</th>
                                    <th class="center-align">UOM</th>
                                    <th class="center-align">Pending</th>
                                    <th class="center-align">Received Packaging</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr data-ng-repeat="item in po.orderItems | filter : filteredByReceivedQuantity track by $index ">
                                    <td>{{item.skuId}}</td>
                                    <td class="">{{item.skuName}} [{{item.packagingName}}]</td>
                                    <td class="center-align">{{item.unitOfMeasure}}</td>
                                    <td class="center-align">
                                        {{((item.requestedQuantity - item.receivedQuantity)/item.conversionRatio).toFixed(2)}}
                                    </td>
                                    <td class="center-align">
                                        {{item.receivedQuantity!=null ? (item.receivedQuantity/item.conversionRatio).toFixed(2) : 0}}
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </li>
                    </ul>
                </div>
            </div>
            <p style="font-size:12px;">Certified that the particulars and the amount indicated given above are true and correct.</p>
            <div style="width: 250px;border:#000 1px solid;float:right;">
                <div style="height:150px"></div>
                <div style="border-top:#000 1px solid;text-align:center;">Authorised Signatory</div>
            </div>
        </div>
    </div>
    </div>
</div>
<!-- Modal Structure -->
<div id="edit" class="modal modal-large">
    <div class="modal-content">
        <div class="row">
            <div class="col s12">
                <p style="font-size: 18px;">
                    Vendor: {{selectedGR.generatedForVendor.name}}
                </p>
                <p style="font-size: 18px;">
                    GR Number : {{selectedGR.id}}
                    GR Item Number  : {{selectedGRItem.id}}
                </p>
                <div class="row">
                    <div class="col s4">SKU Name : {{selectedGRItem.skuName}}</div>
                    <div class="col s4">UOM</div>
                    <div class="col s4">Received Qty : {{selectedGRItem.receivedQuantity}}</div>
                </div>

                <form id="product" name="productForm">

                    <ul class="col s12">
                        <h5 style="margin-top:0px;font-size: 18px;">List of Assets</h5>
                    </ul>
                    <table class="table table-bordered scrollabe-table">
                        <thead>
                            <tr style="background-color: darkseagreen;">
                                <th>
                                    <div style="    text-align: center;">Asset ID:</div>
                                    <div style="    padding-left: 54px;">
                                        <input id="selectedAll"
                                               data-ng-model="selectedAll" data-ng-change="selectAll(selectedAll)" type="checkbox" />
                                        <label class="black-text "  for="selectedAll"></label>
                                    </div>

                                </th>
                                <th>Asset Name:</th>
                                <th>
                                    <label class="black-text active">Life Time Type:</label>
                                    <!--<label class="black-text active" for="lifeTimeTypeAll">Life Time Type:</label>-->
                                    <!--<select id="lifeTimeTypeAll"-->
                                            <!--name="lifeTimeTypeAll"-->
                                            <!--data-ng-model="lifeTimeType1"-->
                                            <!--disabled-->
                                            <!--data-ng-change="setValueForAll('lifeTimeType', lifeTimeType1, false, '')"-->
                                            <!--&gt;-->
                                        <!--<option data-ng-repeat="lifeTime in lifeTimeType" value="{{lifeTime}}"> {{lifeTime}}</option>-->
                                    <!--</select>-->
                                </th>
                                <th>
                                    <label class="black-text active">Life Time Value:</label>
                                    <!--<label class="black-text active" for="lifeTimeValueAll">Life Time Value:</label>-->
                                    <!--<input type="number" id="lifeTimeValueAll" disabled-->
                                           <!--name="lifeTimeValueAll"-->
                                           <!--data-ng-maxlength="10" min="0"-->
                                           <!--data-ng-model="lifeTimeValue"-->
                                           <!--data-ng-change="setValueForAll('lifeTimeValue', lifeTimeValue, false, '')"/>-->
                                </th>
                                <th>
                                    <div style="    text-align: center;">Has Warranty:</div>
                                    <div style="    padding-left: 54px;">
                                        <input id="hasWarrantyAll"
                                               name="hasWarrantyAll"
                                               data-ng-model="hasWarranty"
                                               data-ng-change="setValueForAll('hasWarranty', hasWarranty, false, '')"
                                               type="checkbox" />
                                        <label class="black-text "  for="hasWarrantyAll">

                                        </label>
                                    </div>

                                </th>
                                <th>
                                    <label class="black-text active" for="warrantyLastDateAll">Warranty Last Date:</label>
                                    <input style="max-width: 200px;" input-date type="text"
                                           ng-disabled="hasWarranty == null || hasWarranty == false"
                                           name="warrantyLastDateAll" id="warrantyLastDateAll"
                                           data-ng-model="warrantyLastDate"
                                           data-ng-change="setValueForAll('warrantyLastDate', warrantyLastDate, false, '')"
                                           container="" format="yyyy-mm-dd" select-years="1" min="{{minDate}}"/>
                                </th>
                                <th>
                                    <div style="    text-align: center;">Has AMC:</div>
                                    <div style="    padding-left: 54px;">
                                        <input id="hasAMCAll"
                                               name="hasAMCAll"
                                               data-ng-model="hasAMC"
                                               data-ng-change="setValueForAll('hasAMC', hasAMC, false, '')"
                                               type="checkbox" />
                                        <label class="black-text "  for="hasAMCAll">

                                        </label>
                                    </div>
                                </th>
                                <th>
                                    <label class="black-text active" for="amcLastDateAll">AMC last Date:</label>
                                    <input style="max-width: 200px;" input-date type="text"
                                           ng-disabled="hasAMC == null || hasAMC == false"
                                           name="amcLastDateAll" id="amcLastDateAll"
                                           data-ng-model="amcLastDate"
                                           data-ng-change="setValueForAll('amcLastDate', amcLastDate, false, '')"
                                           container="" format="yyyy-mm-dd" select-years="1" min="{{minDate}}"
                                    />
                                </th>
                                <th>
                                    <div style="    text-align: center;"> Has Insurance:</div>
                                    <div style="    padding-left: 54px;">
                                        <input id="hasInsuranceAll"
                                               name="hasInsuranceAll"
                                               data-ng-model="hasInsurance"
                                               data-ng-change="setValueForAll('hasInsurance', hasInsurance, false, '')"
                                               type="checkbox" />
                                        <label class="black-text "  for="hasInsuranceAll">

                                        </label>
                                    </div>

                                </th>
                                <th>
                                    <label class="black-text active" for="insuranceLastDateAll">Insurance Last Date:</label>
                                    <input style="max-width: 200px;" input-date type="text"
                                           ng-disabled="hasInsurance == null || hasInsurance == false"
                                           name="insuranceLastDateAll" id="insuranceLastDateAll"
                                           data-ng-model="insuranceLastDate"
                                           data-ng-change="setValueForAll('insuranceLastDate', insuranceLastDate, false, '')"
                                           container="" format="yyyy-mm-dd" select-years="1" min="{{minDate}}"
                                    />
                                </th>
                                <th data-ng-if="selectedAssets[0].uniqueFieldName != null && selectedAssets[0].uniqueFieldName.length > 0">
                                    {{selectedAssets[0].uniqueFieldName}}
                                </th>
                                <th data-ng-repeat="entity in selectedAssets[0].profileAttributeMappingList">
                                    <label class="black-text "  for="{{entity.attributeId }}">
                                        {{getAttributeName(entity.attributeId)}} :
                                    </label>
                                    <select data-ng-if="!entity.standAlone" id="{{entity.attributeId }}"
                                            data-ng-change="setValueForAll('entityAttributeValueMappings', entity.attributeValueId, true, entity.attributeId)"
                                            name="{{entity.attributeId }}" data-ng-model="entity.attributeValueId">
                                        <option data-ng-repeat="value in entity.valueList" value="{{value.attributeValueId}}">{{value.attributeValue}}</option>
                                    </select>
                                </th>
                                <th>Action: </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr ng-class="{greyBackground : asset.selected}" data-ng-repeat="asset in selectedAssets track by asset.assetId">
                                <ng-form name="innerForm">
                                    <td style="padding-left: 60px;">
                                        <input id="{{'selected' + asset.assetId }}"
                                               data-ng-model="asset.selected" ng-disabled="!(asset.assetStatus == 'INITIATED')" type="checkbox" />
                                        <label class="black-text "  for="{{'selected' + asset.assetId }}">{{asset.assetId}}</label>
                                    </td>
                                    <td>{{asset.assetName}}</td>
                                    <td>{{asset.lifeTimeType}}
                                        <!--<select id="{{'lifeTimeType' + asset.assetId }}" name="lifeTimeType" disabled-->
                                                <!--data-ng-model="asset.lifeTimeType" data-ng-change="changeTypeAndValue(asset.assetId)"-->
                                                <!--data-ng-options="lifeTime as lifeTime for lifeTime in lifeTimeType" required></select>-->
                                        <!--<p ng-show="asset.lifeTimeType == null" class="errorMessage">Please Select Life Time type.</p>-->
                                    </td>
                                    <td>{{asset.lifeTimeValue}}
                                        <!--<input type="number" disabled id="{{'lifeTimeValue' + asset.assetId }}" name="lifeTimeValue" ng-readonly="!(asset.assetStatus == 'INITIATED')"-->
                                               <!--data-ng-maxlength="10" min="0" data-ng-model="asset.lifeTimeValue" required />-->
                                        <!--<p ng-show="asset.lifeTimeValue == null" class="errorMessage">Life Time Value is required.</p>-->
                                        <!--<p ng-show="asset.lifeTimeValue != null && asset.lifeTimeValue.length > 10" class="errorMessage">Life Time Value is too large.</p>-->
                                    </td>
                                    <td  style="padding-left: 60px;">
                                        <input id="{{'hasWarranty' + asset.assetId }}"
                                               data-ng-model="asset.hasWarranty" ng-disabled="!(asset.assetStatus == 'INITIATED')" type="checkbox" />
                                        <label class="black-text "  for="{{'hasWarranty' + asset.assetId }}"></label>
                                    </td>
                                    <td>
                                        <input style="max-width: 200px;" input-date type="text" ng-disabled="!(asset.assetStatus == 'INITIATED') || !asset.hasWarranty"
                                               name="warrantyLastDate" id="{{'warrantyLastDate' + asset.assetId }}"
                                               data-ng-model="asset.warrantyLastDate"
                                               container="" format="yyyy-mm-dd" select-years="1" min="{{minDate}}" required/>

                                    </td>
                                    <td style="padding-left: 60px;">
                                        <input id="{{'hasAMC' + asset.assetId }}"
                                               data-ng-model="asset.hasAMC" ng-disabled="!(asset.assetStatus == 'INITIATED') " type="checkbox" />
                                        <label class="black-text "  for="{{'hasAMC' + asset.assetId }}"></label>

                                    </td>
                                    <td>
                                        <input style="max-width: 200px;" input-date type="text" ng-disabled="!(asset.assetStatus == 'INITIATED') || !asset.hasAMC" name="amcLastDate" id="{{'amcLastDate' + asset.assetId }}"
                                               data-ng-model="asset.amcLastDate"
                                               container="" format="yyyy-mm-dd" select-years="1" min="{{minDate}}"
                                               required />
                                    </td>
                                    <td style="padding-left: 60px;">
                                        <input id="{{'hasInsurance' + asset.assetId }}"
                                               data-ng-model="asset.hasInsurance" ng-disabled="!(asset.assetStatus == 'INITIATED') " type="checkbox" />
                                        <label class="black-text "  for="{{'hasInsurance' + asset.assetId }}"></label>
                                    </td>
                                    <td>
                                        <input style="max-width: 200px;" input-date type="text" ng-disabled="!(asset.assetStatus == 'INITIATED') || !asset.hasInsurance" name="created" id="{{'insuranceLastDate' + asset.assetId }}"
                                               data-ng-model="asset.insuranceLastDate"
                                               container="" format="yyyy-mm-dd" select-years="1" min="{{minDate}}" required />
                                    </td>
                                    <td data-ng-if="asset.uniqueFieldName != null && asset.uniqueFieldName.length > 0">
                                        <input type="text" id="{{asset.assetId + '_' + 'unique_field_name' }}" name="uniqueFieldValue" ng-readonly="!(asset.assetStatus == 'INITIATED')"
                                                data-ng-model="asset.uniqueFieldValue" required />
                                        <p ng-show="asset.uniqueFieldValue == null" class="errorMessage">Unique Field Value is required.</p>
                                    </td>
                                    <!-- Asset Attribute Mapping START -->
                                    <td data-ng-repeat="entity in asset.entityAttributeValueMappings">
                                        <div data-ng-if="!entity.isMandatory">
                                            <div data-ng-if="!entity.standAlone ">
                                                <select id="{{asset.assetId + '_' + entity.attributeId }}" ng-disabled="!(asset.assetStatus == 'INITIATED')"
                                                        name="{{asset.assetId + '_' + entity.attributeId }}" data-ng-model="entity.attributeValueId">
                                                    <option data-ng-repeat="value in entity.valueList" value="{{value.attributeValueId}}">{{value.attributeValue}}</option>
                                                </select>
                                            </div>

                                            <div data-ng-if="entity.standAlone || (asset.assetStatus != 'INITIATED')">
                                                <input data-ng-model="entity.attributeValue" ng-disabled="!(asset.assetStatus == 'INITIATED')" type="text" required/>
                                                <p ng-show="entity.attributeValue == null && asset.assetStatus == 'INITIATED'" class="errorMessage">Please enter a value</p>
                                            </div>
                                        </div>
                                        <div data-ng-if="entity.isMandatory">

                                            <div data-ng-if="!entity.standAlone || !(asset.assetStatus == 'INITIATED')">
                                                <select id="{{asset.assetId + '_' + entity.attributeId}}" ng-disabled="!(asset.assetStatus == 'INITIATED')"
                                                        name="{{asset.assetId + '_' + entity.attributeId}}" data-ng-model="entity.attributeValueId"
                                                        required>
                                                    <option data-ng-repeat="value in entity.valueList" value="{{value.attributeValueId}}">{{value.attributeValue}}</option>
                                                </select>
                                                <p ng-show="entity.attributeValueId == null && asset.assetStatus == 'INITIATED'" class="errorMessage">Please Select a value</p>
                                            </div>

                                            <div data-ng-if="entity.standAlone && (asset.assetStatus == 'INITIATED')">
                                                <input data-ng-model="entity.attributeValue" ng-disabled="!(asset.assetStatus == 'INITIATED')" type="text" required/>
                                                <p ng-show="entity.attributeValue == null && asset.assetStatus == 'INITIATED'" class="errorMessage">Please enter a value</p>
                                            </div>
                                        </div>
                                    </td>
                                    <!-- Asset Attribute Mapping END -->
                                    <td>
                                        <button data-ng-if="asset.assetStatus == 'INITIATED'" data-ng-click="printTag(asset)"
                                                class="btn right" >
                                            Generate Barcode
                                        </button>
                                        <button data-ng-if="asset.assetStatus == 'CREATED'" ng-disabled="true"
                                                class="btn right" >
                                            Generated
                                        </button>
                                    </td>
                                </ng-form>
                            </tr>
                        </tbody>
                    </table>
                </form>
                <div class="right-align">
<!--                    <button class="btn" data-ng-show="isBulkGRAllowed" data-ng-click="bulkGenerate()">Bulk Generate</button>-->
                    <!--<button class="btn" data-ng-click="printCode('abc123')">PrintCode</button>-->
                </div>
                <div class="right-align">
                    <button class="modal-action modal-close waves-effect waves-green btn" data-ng-click="reset();">Close</button>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="modal1" class="modal modal-large">
    <div class="modal-content">
        <div class="row">
            <div class="col s12">
                <p style="font-size: 18px;">
                    Vendor: {{selectedGR.generatedForVendor.name}}
                </p>
                <p style="font-size: 18px;">
                    GR Number : {{selectedGR.id}}
                    GR Item Number  : {{selectedGRItem.id}}
                </p>
                <button data-ng-if="isRefresh == true && selectedGR.status == 'INITIATED'" class="btn btn-primary" style="margin-bottom : 5px;" data-ng-click="refreshApproveVendorGR()" acl-action="SVRB" >Refresh</button>
                <div>  
                <div class="row">
                    <div class="col s3">SKU Name : </div>
                    <div class="col s3">UOM</div>
                    <div class="col s3">Received Qty : </div>
                    <div class="col s3">Status</div>
                </div>
                <div class="row" data-ng-repeat="grItem in selectedGR.grItems">
                    <div class="col s3"> {{grItem.skuName}}</div>
                    <div class="col s3"> {{grItem.packagingName}}</div>
                    <div class="col s3"> {{grItem.receivedQuantity}}</div>
                    <div class="col s3">
                        <button data-ng-if="grItem.status == 'Continue'" class="btn btn-primary pull-left"
                                data-ng-click="selectGRItem(grItem)" href="#edit" modal>Continue Generation</button>
                        <button data-ng-if="grItem.status == 'Generated'" class="btn btn-primary pull-left"
                                data-ng-click="selectGRItem(grItem)" href="#edit" modal>Generated</button>
                        <button data-ng-if="grItem.status == 'Create Asset'" class="btn btn-primary pull-left" href="#edit"
                                ng-click="createAsset(grItem,selectedGR)" modal>{{grItem.status}}</button>
                    </div>
                    </div>
                </div>


                <div class="right-align">
                    <button id="modal1closebtn" class="modal-action modal-close waves-effect waves-green btn" >Close</button>
                </div>
            </div>
        </div>
    </div>
</div>

