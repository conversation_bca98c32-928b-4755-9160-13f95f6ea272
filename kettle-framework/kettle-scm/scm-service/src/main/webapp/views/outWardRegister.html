<!--
   ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
   ~ __________________
   ~
   ~ [2015] - [2023] Sunshine Teahouse Private Limited
   ~ All Rights Reserved.
   ~
   ~ NOTICE:  All information contained herein is, and remains
   ~ the property of Sunshine Teahouse Private Limited and its suppliers,
   ~ if any.  The intellectual and technical concepts contained
   ~ herein are proprietary to Sunshine Teahouse Private Limited
   ~ and its suppliers, and are protected by trade secret or copyright law.
   ~ Dissemination of this information or reproduction of this material
   ~ is strictly forbidden unless prior written permission is obtained
   ~ from Sunshine Teahouse Private Limited.
   -->
<style>
    .hovertext {
        position: relative;
        border-bottom: 1px dotted black;
    }

    .hovertext:before {
        content: attr(data-hover);
        visibility: hidden;
        opacity: 0;
        width: 140px;
        background-color: black;
        color: #fff;
        text-align: center;
        border-radius: 5px;
        padding: 5px 0;
        transition: opacity 1s ease-in-out;
        position: absolute;
        z-index: 1;
        left: 0;
        top: 110%;
    }

    .hovertext:hover:before {
        opacity: 1;
        visibility: visible;
    }


    .btn:hover {
        background-color: #26a69a;
        color: white;
    }

    .stickyaddbtn {
        position: fixed;
        top: 90%;
        left: 80%;
        z-index: 9999 !important;
    }

    .stickyaddbtn a {
        display: block;
        text-align: center;
        padding: 16px;
        transition: all 0.3s ease;
        color: white;
        font-size: 20px;
    }

    .stickyaddbtn a:hover {
        background-color: #26a69a;
    }

    {
        box-sizing: border-box ;
    }
    .mainLoginInput {
        height: 40px;
        padding: 0px;
        font-size: 30px;
        margin: 5px 0;
    }

    .mainLoginInput::-webkit-input-placeholder {
        font-family: FontAwesome;
        font-weight: normal;
        overflow: visible;
        vertical-align: top;
        display: inline-block !important;
        padding-left: 5px;
        padding-top: 2px;
    }

    .mainLoginInput::-moz-placeholder {
        font-family: FontAwesome;
        font-weight: normal;
        overflow: visible;
        vertical-align: top;
        display: inline-block !important;
        padding-left: 5px;
        padding-top: 2px;
    }

    .mainLoginInput:-ms-input-placeholder {
        font-family: FontAwesome;
        font-weight: normal;
        overflow: visible;
        vertical-align: top;
        display: inline-block !important;
        padding-left: 5px;
        padding-top: 2px;
    }
</style>

<div>
    <div class="searchingCard row white z-depth-3" data-ng-init="init()">

        <div data-ng-show="viewRegister==true">
            <div class="col s12">
                <h4>{{headingMsg}}</h4>
            </div>
            <div class="row">
                <div class="col s4">
                    <label for="date" style="margin-top: 3px;">Start Date</label>
                    <input input-date type="text" name="startdate" id="date" ng-model="startDate" container=""
                           format="yyyy-mm-dd" required/>
                </div>
                <div class="col s4">
                    <label for="enddate" style="margin-top: 3px;">End Date</label>
                    <input input-date type="text" name="endDate" id="enddate" ng-model="endDate" container=""
                           format="yyyy-mm-dd" required/>
                </div>
                <div class="col s4">
                    <button class="btn btn-primary" style="margin-top: 3px;" data-ng-click="getEntriesOfInvoices()">
                        Find
                    </button>
                </div>
            </div>
            <hr>
            <div class="stickyaddbtn">
                <button style="" class="btn-floating btn-large waves-effect waves-light"
                        data-ng-click="getInvoices('VIEW')"><i class="material-icons">add</i></button>
            </div>
            <div class="col s12" data-ng-if="formEntry.length==0" style="padding:30px;color:gray;font-size: 20px;">
                <div class="row margin0 center">
                    No Invoices found for the selected criteria
                </div>
            </div>
        </div>
        <div data-ng-show="selectView==true">
            <div class="col s12" ng-if="formEntry.length>0">
                <div class="row">
                    <ul>
                        <li class="row margin0" style="padding:5px;"
                            data-ng-repeat="formE in formEntry | filter:{id:selectedEntry} track by $index">
                            <div class="col s12 waves-effect waves-light lighten-3"
                                 style="border:1px  solid #ddd;padding:0;">
                                <div class="col s11">
                                    <div class="row margin0">
                                        <div class="left"><span><b>#{{formE.id}}</b></span></div>
                                        <div class="right"><span><b>Vehicle-</b> {{formE.vehicleNoType}}</span></div>
                                    </div>
                                    <div class="row margin0">
                                        <div class="left">
                                            <span>Created At:<b> {{formE.dateTime |
                                            date:'dd/MM/yyyy hh:mm:ss a'}}</b></span>
                                            <br>
                                            <span>Registered At:<b> {{formE.submissionDateTime |
                                            date:'dd/MM/yyyy hh:mm:ss a'}}</b></span>

                                        </div>
                                        <div class="right">
                                            <span><b>Deliverer-</b> {{formE.nameOfDeliverer}}</span></div>

                                    </div>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>

        </div>

        <div class="col s12" data-ng-show="invoiceView==true">
            <div class="col s12" data-ng-show="formView==false">
                <h4>Pending Invoices</h4>
            </div>


            <div data-ng-show="formView==false">
                <div>

                    <div data-ng-if="invoiceRequest.length>0">
                        <div>
                            <input class="mainLoginInput" type="text" data-ng-model="searchInvoice"
                                   placeholder="&#61442; Search..."
                                   style="text-align: center;border-radius: 50px 50px  50px 50px !important;"
                               data-ng-change="setSearchInput(searchInvoice)"/>
                        </div>
                        <div style="display:flex; flex-direction: row; justify-content: center; align-items: center;margin-top: 10px;">
                            <label>Filter On Invoice Id</label>
                            <input style="text-align: center;" type="text" data-ng-model="searchInvoiceId"
                                   data-ng-change="setSearchInvoiceId(searchInvoiceId)" placeholder="Invoice Id"/></div>
                        </div>

                    </div>

                    <div class="col s12" data-ng-if="invoiceRequest.length>0"
                         data-ng-repeat="invReq in invoiceRequest  | filter:{id:searchInvoiceId} | filter:searchInvoice track by $index">
                        <div class="row">

                            <ul>
                                <li class="row margin0" style="padding: 7px;">
                                    <div class="col s12 waves-effect waves-light lighten-3"
                                         data-ng-class="{'red white-text': invReq.id === createdInvoice.id}"
                                         style="border:1px solid #ddd;padding:0;line-height: normal;">
                                        <div class="row margin0">
                                            <div class="left">
                                                <span><b>InvID# {{invReq.id}}</b></span>
                                                <br><span
                                                    class=""><b>Dispatch:</b> {{invReq.dispatchDate | date:'dd-MM-yyyy'}}</span>
                                                <br><span
                                                    class=""><b>Amount:</b>(Rs) {{invReq.totalAmount.toFixed(2)}}</span>
                                            </div>
                                            <div class="right">
                                                <br><span class=""><b>Mode:</b>{{invReq.vehicle.transportMode}}</span>
                                                <br><span class="hovertext"
                                                          data-hover="{{invReq.vehicle.make}} {{invReq.vehicle.model}} {{invReq.vehicle.name}}">
                                    <b>Reg No.</b>{{invReq.vehicle.registrationNumber}}</span>
                                            </div>
                                        </div>
                                        <div class="row margin0">
                                            <br><span><b
                                                style="margin-top: 20px;line-height: 1.4;">Del Location:</b>{{invReq.deliveryAddress}}</span>
                                        </div>
                                        <div class="row margin0">
                                            <br><span><b
                                                style="margin-top: 5px; margin-bottom: 10px;line-height: 1.4;">Vendor:</b>{{invReq.vendor.name}}[{{invReq.dispatchLocation.name}}]</span>
                                        </div>
                                        <div class="row" style="margin-top: 5px;">
                                            <button class="btn btn-primary waves-effect"
                                                    style="border-radius: 30%;margin-left: 40%;margin-right: 40%;"
                                                    data-ng-click="activateForm('VIEW',invReq)">
                                                Register Entry
                                            </button>
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                <hr>
                <div class="col s12" data-ng-if="invoiceRequest.length==0" style="padding:30px;color:gray;font-size: 20px;">
                    <div class="row margin0 center" ng-if="showViewActions">
                        No Pending Invoices.
                    </div>
                </div>
                    <div class="row ">
                        <div class="stickyaddbtn">
                            <button style="background-color: red"
                                    class="btn-floating btn-large waves-effect waves-light"
                                    data-ng-click="backToRegisterView()" value="Back"><i class="material-icons">keyboard_return</i>
                            </button>
                        </div>
                    </div>
                </div>

            </div>
            <div data-ng-show="formView==true">
                <div class="row">
                    <div>
                        <form name="outWardForm" class="col s12 scm-form" id="myform" novalidate>
                            <div class="row">
                                <h4 style="margin: 5px 0 10px;">New Registry
                                </h4>
                                <div class="col s12 m6 l6">
                                    <div class="form-group">
                                        <label class="black-text" for="serialNo">Serial Number</label>
                                        <input id="serialNo" placeholder="Serial Number" type="Number" name="serialNo"
                                               ng-model="formResponse.serialNumber" required disabled>
                                        <p ng-show="outWardForm.serialNo.$error.required" class="errorMessage">Serial
                                            Number is required.</p>
                                    </div>
                                    <div class="form-group">
                                        <label class="black-text active" for="startdate">Select Date</label>
                                        <input input-date type="text" name="startdate" id="startdate"
                                               ng-model="formResponse.date"
                                               container="" format="yyyy-mm-dd" select-years="1"
                                               required disabled>
                                        <p ng-show="outWardForm.startdate.$error.required" class="errorMessage">Date is
                                            required.</p>
                                    </div>
                                    <div class="form-group">
                                        <label class="black-text" for="challanId">Challan/Bill Number</label>
                                        <input placeholder="Challan ID" id="challanId" name="challanId"
                                               ng-model="formResponse.challanNo"
                                               type="text" data-ng-maxlength="50" required disabled>
                                        <p ng-show="outWardForm.challanId.$error.required" class="errorMessage">Challan
                                            Number is Required</p>
                                    </div>
                                    <div class="form-group">
                                        <label class="black-text" for="time">Input Time</label>
                                        <input id="time" type="time" name="time" ng-model="formResponse.time" required
                                               disabled/>
                                        <p ng-show="outWardForm.time.$error.required" class="errorMessage">Time is
                                            Required</p>
                                    </div>
                                    <div class="form-control">
                                        <label class="black-text" for="senderName">Name & Address of Buyer/
                                            Consignee/ Receiver/ Party</label>
                                        <input placeholder="Sender Name" name="senderName" id="senderName"
                                               ng-model="formResponse.addressOfBuyer"
                                               type="text" data-ng-maxlength="500" required disabled>
                                        <p ng-show="outWardForm.senderName.$error.required" class="errorMessage">Sender
                                            Name is Required</p>
                                    </div>
                                    <div class="form-group">
                                        <label class="black-text" for="articleName">Name / Details of Article</label>
                                        <input placeholder="Enter Name of Article" name="articleName"
                                               ng-model="formResponse.detailsOfArticle"
                                               id="articleName" type="text" data-ng-maxlength="50" required disabled>
                                        <p ng-show="outWardForm.articleName.$error.required" class="errorMessage">
                                            Article Name is Required</p>
                                    </div>
                                </div>
                                <div class="col s12 m6 l6">
                                    <div class="form-group">
                                        <label class="black-text" for="quantity">Quantity</label>
                                        <input placeholder="Enter Quantity" name="quantity" id="quantity"
                                               ng-model="formResponse.quantity"
                                               type="number" data-ng-maxlength="20" required disabled>
                                        <p ng-show="outWardForm.quantity.$error.required" class="errorMessage">Quantity
                                            is Required</p>
                                    </div>
                                    <div class="black-text">
                                        <label class="black-text" for="amount">Amount</label>
                                        <input placeholder="Enter price" name="amount" id="amount" type="number"
                                               ng-model="formResponse.amount"
                                               data-ng-maxlength="20" required disabled>
                                        <p ng-show="outWardForm.amount.$error.required" class="errorMessage">Amount is
                                            Required</p>
                                    </div>
                                    <div class="form-group">
                                        <label class="black-text" for="deliveryBoyName">Name of Deliverer</label>
                                        <input placeholder="DeliveryBoy Name" name="deliveryBoyName"
                                               ng-model="formResponse.nameOfDeliverer"
                                               id="deliveryBoyName" data-ng-maxlength="500" type="text" required
                                               disabled>
                                        <p ng-show="outWardForm.deliveryBoyName.$error.required" class="errorMessage">
                                            Name of Deliverer is Required</p>
                                    </div>

                                    <div class="form-group">
                                        <label class="black-text" for="noPlate">Vehicle No. & Type of
                                            vehicle</label>
                                        <input placeholder="Number Plate Name" name="noPlate" id="noPlate"
                                               ng-model="formResponse.vehicleNoType"
                                               type="text" data-ng-maxlength="50" required disabled>
                                        <p ng-show="outWardForm.noPlate.$error.required" class="errorMessage">Vehicle
                                            Number and Type isRequired</p>
                                    </div>
                                    <div class="form-group">
                                        <label class="black-text" for="securitySign">Signature of Security
                                            /Officer /Sender</label>
                                        <input placeholder="Signature of Security" name="securitySign"
                                               ng-model="formResponse.signatureOfSecurity"
                                               id="securitySign" type="text" data-ng-maxlength="50" required>
                                        <p ng-show="outWardForm.securitySign.$error.required" class="errorMessage">
                                            Security Signature is Required</p>
                                    </div>
                                    <div class="form-group">
                                        <label class="black-text" for="remarks">Remarks</label>
                                        <textarea id="remarks" style="max-width: 100%;height:70% !important;"
                                                  placeholder="Enter Comment on Invoice" name="remarks"
                                                  ng-model="formResponse.remarks" rows="10" cols="33"></textarea>
                                    </div>
                                </div>
                            </div>
                        </form>
                        <div class="row"
                             style="position: fixed;top:94%;width:95%;background-color: #888484;display:flex;">
                            <div class="row left">
                                <button class="waves-effect waves-red btn left"
                                        style="background-color:#FF0000;margin-top: 7px;"
                                        data-ng-click="backtoFormAndInvoiceView()">CLOSE
                                </button>
                            </div>
                            <div class="row right" style="text-align: center;">
                                <button data-ng-disabled="outWardForm.$valid == false"
                                        style="margin-right: 15px;margin-top: 7px;"
                                        class=" waves-effect waves-green btn right"
                                        data-ng-click="setNewEntry(invoiceRequest)">SUBMIT
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

