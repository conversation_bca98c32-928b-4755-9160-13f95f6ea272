<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style>
    .select2.select2-container{
        width:100% !important;
    }
</style>

<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="col s12">
		<div class="row">
			<div class="col s12">
				<h3 class="left">Manage Vendor Ledger Balance</h3>
			</div>
		</div>
		<div class="row">
			<div class="col s6">
				<label class="black-text" for="selectedCompany">Select
					Company</label> <select ui-select2="selectedCompany" id="selectedCompany"
					name="companyList" data-ng-model="selectedCompany"
					data-ng-change="changeCompany()"
					data-ng-options="company as company.name for company in companyList track by company.id"></select>
			</div>
		</div>
		<div class="row">
            <div class="col s12">
                <div style="text-align: center">
                    <input type="button" class="btn" acl-action="VNDDB" value="Download Vendor Ledger Balance Sheet" data-ng-click="downloadBalanceSheet()" />
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col s12">
                <div style="text-align: center">
                    <input type="button" class="btn" acl-action="VNUDB" value="Upload Balance Sheet" data-ng-click="uploadDoc()" />
                    <p style="border:#f7a4a4 1px solid; padding: 10px;background:#f3c9c9;border-radius: 3px;color: #8a0a0a;"
                       data-ng-if="showNoPR">No valid balances found.</p>
                </div>
            </div>
        </div>
        <div class="row" data-ng-if="dbs.length>0">
            <div class="col s12">
                <table class="table striped" style="border:#ddd 1px solid;margin-bottom: 10px;">
                    <thead>
                        <tr>
                            <th>Vendor Id</th>
                            <th>Name.</th>
                            <th>Balance</th>
                            <th>Comapny Name </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr data-ng-repeat="db in dbs track by db.vendorId">
                            <td>{{db.vendorId}}</td>
                            <td>{{db.entityName}}</td>
                            <td>{{db.debitBalance}}</td>
                            <td>{{db.companyName}}</td>
                        </tr>
                    </tbody>
                </table>
                <input type="button" data-ng-click="submitBalances()" value="Submit Balances" class="btn green" />
            </div>
        </div>
    </div>

</div>
