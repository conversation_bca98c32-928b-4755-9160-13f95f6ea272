<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<style>
    .optionTab {
        margin: 10px 0px !important;;
        width: 400px;
        position: relative;
        display: block;
        left: 50%;
        transform: translateX(-50%);
    }
    @media screen and (max-width: 400px) {
        .optionTab {
            width: 80%;
        }
    }

</style>

<div class="container loginContainer" data-ng-init="init()">
    <div class="card-panel teal lighten-2 white-text" style="margin-top:0;" data-ng-if="showMessage">{{message}}</div>
    <div style="text-align: center; margin-top: 20px;"><img src="img/slogo.png" style="margin-bottom: 5px; width: 350px;" /></div>
    <div class="row optionTab">
        <div class="col s6 login-tab pointer" data-ng-class="{'active':loginType == 'sumo'}"
             data-ng-click="changeLoginType('sumo')">
            GOODS
        </div>
        <div class="col s6 login-tab pointer" data-ng-class="{'active':loginType == 'service'}"
             data-ng-click="changeLoginType('service')">
            SERVICE
        </div>
    </div>
    <div class="card #f5f5f5 grey lighten-4 loginCard">
        <div class="card-content">
            <!--<h4 style="margin-top: 0px;">{{tagline}}</h4>-->
            <div class="row" style="margin-bottom: 0px;">
                <div class="col s12">
                    <form data-ng-submit="login(email,password)" style="border:none !important;">
                        <div class="form-element">
                            <label for="userId" class="active">User Id</label>
                            <input id="userId" tabindex="1" type="number" ng-minlength="6" ng-maxlength="6"  ng-change="fetchUnit(userId)" autocomplete="off" class="validate" data-ng-model="userId">
                        </div>
                        <div class="form-element">
                            <label for="passcode" class="active">Passcode</label>
                            <input id="passcode" tabindex="2" type="password" autocomplete="off" class="validate" data-ng-model="passcode">
                        </div>
                        <div class="form-element" data-ng-if="loginType=='sumo'">
                            <label for="unit">Unit</label>
                            <select id="unit" tabindex="3" ui-select2="unitSelectOptions"
                                    data-ng-change="selectUnit(selectedUnit)"
                                    ng-model="selectedUnit" data-placeholder="Enter name of a unit">    <!-- data-ng-change="selectUnit(selectedUnit)"-->
                                <option ng-repeat="unit in unitList track by unit.id" value="{{unit}}">{{unit.name}}</option>
                            </select>
                        </div>
                        <div class="form-element right-align" style="margin-bottom: 0px;">
                            <input type="submit" value="Login" class="btn" />
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <h5 class="center-align green-text-5" data-ng-if="appVersion!=null && appVersion!=''">V.{{appVersion}}</h5>
</div>
