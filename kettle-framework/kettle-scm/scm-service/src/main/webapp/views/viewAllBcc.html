<style>
 .popeye-modal-container .popeye-modal{
    width: 900px;
	}
</style>

<div class="row" data-ng-init="init()">
	<div class="col s12">
		<div class="row white z-depth-3 custom-listing-li">
			<div class="col s12">
				<h4>Service Order BCC Items</h4>
			</div>
			<br>
			 
			<div class="row">
				<div class="col s12">
					<div class="row" data-ng-repeat="(key,rcv) in receivings">
						<label>SO for {{rcv.company.name}} {{rcv.state.name}}</label>
						<table class="bordered striped">
							<thead>
								<tr>
									<th>Cost Center</th>
									<th>Cost Element</th>
									<th>Unit Price</th>
									<th>Quantity</th>
									<th>Tax Rate</th>
									<th>Cost</th>
									<th>Tax</th>
									<th></th>
								</tr>
							</thead>
							<tbody>
								<tr data-ng-repeat="item in rcv.items track by $index">
									<td>{{item.businessCostCenterName}}</td>
									<td class="pointer" data-tooltip="{{item.serviceDescription}}"
										tooltipped>{{item.costElementName}}[{{item.ascCode}}]</td>
									<td>{{item.unitPrice}}</td>
									<td>{{item.requestedQuantity}}</td>
									<td>{{item.taxRate}}</td>
									<td>{{item.totalCost}}</td>
									<td>{{item.totalTax}}</td>
									<td class="pointer"
										data-ng-click="removeItemFromRcv($index, rcv, key)"><i
										class="material-icons">clear</i></td>
								</tr>
							</tbody>
						</table>
					</div>
					<div class="row margin0 center"
						data-ng-if="receivings != undefined && receivings != {}">
						<button class="btn" data-ng-click="submit(receivings)">Preview
							& Submit</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<script type="text/ng-template" id="departmentSO1Modal.html">
<div class="modal-header" data-ng-init="init()">
	<h3 class="modal-title" id="modal-title" style="margin-top: 0px;">Summary</h3>
    <hr>
</div>
<div class="modal-body" id="modal-body">
<div class="row">
                <div class="col s12">
                    <table class="table bordered striped" style="border: #ccc 1px solid;">
                        <thead>
                        <tr>
                            <th>Department Name</th>
							<th>Cost Element Name</th>
                            <th>Total Cost</th>
                            <th>Total Tax</th>
                            <th>Total Amount</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr data-ng-repeat="items in summaryDepartmentList">
                            <td>{{items.departmentName}}</td>
							<td>{{items.costElementName}}</td>
 							<td>{{items.totalCostDup}}</td>
 							<td>{{items.totalTaxDup}}</td>
 							<td>{{items.totalAmountDup}}</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
				</div>
				<div class="row">
					<button class="right btn" data-ng-click="submit()">Submit</button>
            </div>
	</div>
		</script>