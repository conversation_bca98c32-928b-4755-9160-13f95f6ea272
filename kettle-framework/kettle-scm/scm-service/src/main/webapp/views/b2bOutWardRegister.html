<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style>
</style>

<div class="">
    <div class="row scm-form" data-ng-model="outRegister" id="outRegister">
        <div class="row">
            <div class="col s6">
                <div class="left">
                    <h3 style="margin: 1px 0 0;">Outward Form</h3>
                </div>
            </div>
        </div>
    </div>
    <div class="row" data-ng-init="init()">
        <div class="col s2">
            <label for="startdate">From</label>
            <input input-date type="text" name="startdate" id="startdate" value={{initDate}} ng-model="startDate" container="" format="yyyy-mm-dd" required/>
        </div>
        <div class="col s2">
            <label for="enddate">To</label>
                <input input-date type="text"  name="endDate" id="enddate" value={{initDate}} ng-model="endDate" container="" format="yyyy-mm-dd" required/>
        </div>
        <div class="col s2">
            <button class="btn btn-primary" data-ng-click="getInvoices()">Find</button>
            <!--acl-action="VOMVPOV"-->
        </div>
    </div>
    <div class="col s12" data-ng-if="invoiceRequest.length>0"
         data-ng-repeat="invReq in invoiceRequest | filter:{type:txnType} track by $index">
        <div class="row">
            <span data-ng-click="openForm(invoiceRequest)">
                <ul class="collapsible popout" data-collapsible="accordion" watch>
                    <li class="row margin0" style="padding: 7px;">
                        <div class="col s10 collapsible-header waves-effect waves-light lighten-3"
                             data-ng-class="{'red white-text': invReq.id === createdInvoice.id}"
                             style="border:1px solid #ddd;padding:0;line-height: normal;">
                            <div class="row margin0">
                                <div class="left">
                                    <span><b>InvID# {{invReq.id}}</b></span>
                                    <span class="chip grey white-text">Dispatch: {{invReq.dispatchDate |
                                    date:'dd-MM-yyyy'}}</span>
                                    <span class="chip grey white-text">Amount: {{invReq.totalAmount.toFixed(2)}}</span>
                                </div>
                                <div class="right">
                                    <span class="chip red darken-1 white-text">{{invReq.vehicle.transportMode}}</span>
                                    <span class="chip red darken-1 white-text">{{invReq.vehicle.name}}</span>
                                </div>
                            </div>
                            <div class="row margin0">
                                <br><span><b style="line-height: normal;">Delivery
                                        location:</b>{{invReq.deliveryAddress}}</span>
                                <br><span><b
                                    style="line-height: normal;">For:</b>{{invReq.vendor.name}}[{{invReq.dispatchLocation.name}}]</span>
                            </div>
                        </div>
                        <a class="waves-effect waves-light btn modal-trigger" data-ng-click="selectInvoice(invReq)"  modal href="#modal" >Open Form for Invoice
                            {{invReq.id}}</a>
                        <div class="modal" id="modal">
                            <div class="modal-content">
                                <form name="outWardForm" class="col s12 scm-form"  novalidate>
                                    <div class="row">
                                        <h3 style="margin: 5px 0 0; ">New Entry Form</h3>
                                        <div class="col s12 m6 l6">
                                            <div class="form-element">
                                                <label class="black-text" for="serialNo">Serial Number</label>
                                                <input id="serialNo" type="Number" name="serialNo" ng-model="formResponse.serialNumber" required>
<!--                                                <p ng-show="outWardForm.serialNo.$error.required" class="errorMessage">Serial Number is required.</p>-->
                                            </div>
                                            <div class="form-element">
                                                <label class="black-text active" for="startdate">Select Date</label>
                                                <input input-date type="date" name="startdate" id="startdate" ng-model="formResponse.date"
                                                       value={{initDate}} container="" format="yyyy-mm-dd" select-years="1"
                                                       required>
<!--                                                <p ng-show="outWardForm.startdate.$error.required" class="errorMessage">Date is required.</p>-->
                                            </div>
                                            <div class="form-element">
                                                <label class="black-text" for="challanId">Challan/Bill Number</label>
                                                <input placeholder="Challan ID" id="challanId" name="challanId" ng-model="formResponse.challanNo"
                                                       type="number" data-ng-maxlength="50" required>
<!--                                                <p ng-show="outWardForm.challanId.$error.required" class="errorMessage">Challan Number is Required-->
                                                </p>
                                            </div>
                                            <div class="form-element">
                                                <label class="black-text" for="time">Input Time</label>
                                                <input id="time" type="time" name="time" ng-model="formResponse.time"  required />
<!--                                                <p ng-show="outWardForm.time.$error.required" class="errorMessage">Time is Required</p>-->
                                            </div>
                                            <div class="form-control">
                                                <label class="black-text" for="senderName">Name & Address of Buyer/
                                                    Consignee/ Receiver/ Party</label>
                                                <input placeholder="Sender Name" name="senderName" id="senderName" ng-model="formResponse.addressOfBuyer"
                                                       type="text" data-ng-maxlength="500" required>
<!--                                                <p ng-show="outWardForm.senderName.$error.required" class="errorMessage">Sender Name is Required</p>-->
                                            </div>
                                            <div class="form-element">
                                                <label class="black-text" for="articleName">Name / Details of Article</label>
                                                <input placeholder="Enter Name of Article" name="articleName" ng-model="formResponse.detailsOfArticle"
                                                       id="articleName" type="text" data-ng-maxlength="50" required>
<!--                                                <p ng-show="outWardForm.articleName.$error.required" class="errorMessage">Article Name is Required</p>-->
                                            </div>
                                        </div>
                                        <div class="col s12 m6 l6">
                                            <div class="form-element">
                                                <label class="black-text" for="quantity">Quantity</label>
                                                <input placeholder="Enter Quantity" name="quantity" id="quantity" ng-model="formResponse.quantity"
                                                       type="number" data-ng-maxlength="20" required>
<!--                                                <p ng-show="outWardForm.quantity.$error.required" class="errorMessage">Quantity is Required</p>-->
                                            </div>
                                            <div class="black-text">
                                                <label class="black-text" for="amount">Amount</label>
                                                <input placeholder="Enter price" name="amount" id="amount" type="number" ng-model="formResponse.amount"
                                                       data-ng-maxlength="20" required>
<!--                                                <p ng-show="outWardForm.amount.$error.required" class="errorMessage">Amount is Required</p>-->
                                            </div>
                                            <div class="form-element">
                                                <label class="black-text" for="deliveryBoyName">Name of Deliverer</label>
                                                <input placeholder="DeliveryBoy Name" name="deliveryBoyName" ng-model="formResponse.nameOfDeliverer"
                                                       id="deliveryBoyName" data-ng-maxlength="500" type="text" required>
<!--                                                <p ng-show="outWardForm.deliveryBoyName.$error.required" class="errorMessage">Name of Deliverer is Required</p>-->
                                            </div>
                                            <div class="form-element">
                                                <label class="black-text" for="designation">Sign Of Deliverer</label>
                                                <input placeholder="Designation" name="designation" id="designation" ng-model="formResponse.signOfDeliverer"
                                                       type="text" data-ng-maxlength="500" required>
<!--                                                <p ng-show="outWardForm.designation.$error.required" class="errorMessage">Signature is Required</p>-->
                                            </div>
                                            <div class="form-element">
                                                <label class="black-text" for="noPlate">Vehicle No. & Type of
                                                    vehicle</label>
                                                <input placeholder="Number Plate Name" name="noPlate" id="noPlate" ng-model="formResponse.vehicleNoType"
                                                       type="text" data-ng-maxlength="50" required>
<!--                                                <p ng-show="outWardForm.noPlate.$error.required" class="errorMessage">Vehicle Number and Type isRequired</p>-->
                                            </div>
                                            <div class="form-element">
                                                <label class="black-text" for="securitySign">Signature of Security
                                                    /Officer /Sender</label>
                                                <input placeholder="Signature of Security" name="securitySign" ng-model="formResponse.signatureOfSecurity"
                                                       id="securitySign" type="text" data-ng-maxlength="50" required>
<!--                                                <p ng-show="outWardForm.securitySign.$error.required" class="errorMessage">Security Signature is Required</p>-->
                                            </div>
                                            <div class="form-element">
                                                <label class="black-text" for="remarks">Remarks</label>
                                                <textarea id="remarks" placeholder="Enter Comment on Invoice" name="remarks" ng-model="formResponse.remarks" type="text" rows="4" cols="50"></textarea>
<!--                                                <p ng-show="outWardForm.remarks.$error.required" class="errorMessage">Remarks Required</p>-->
                                            </div>
                                        </div>

                                    </div>
                                    <button  class="btn right" data-ng-click="setNewEntry(invoiceRequest)">SUBMIT</button>
                                    <button class="btn btn-danger left" data-ng-click="closeModal(modal)"> CANCEL </button>
                                </form>
<!--                                <div class="modal-footer">-->
<!--                                    <div>-->
<!--                                        <button  class="btn right" data-ng-click="setNewEntry()">SUBMIT</button>-->
<!--                                    </div>-->

<!--                                </div>-->
                            </div>
<!--                            <div class="modal-footer">-->
<!--&lt;!&ndash;                                <a modal href="#submitmodal" class="modal-close waves-effect waves-green btn-flat"> </a>&ndash;&gt;-->
<!--                                    <div>-->
<!--                                        <button acl-action="" class="btn right" data-ng-click=""  >SUBMIT<i-->
<!--                                                class="material-icons right">send</i></button>-->
<!--                                    </div>-->

<!--                                                data-ng-if="outWardForm.$valid"-->


<!--                            </div>-->
                        </div>
                    </li>
                </ul>
            </span>
        </div>
    </div>
</div>