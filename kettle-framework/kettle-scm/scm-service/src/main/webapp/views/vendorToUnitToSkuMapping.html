<style>
.grid {
	width: 900px;
	height: 550px;
	padding: 0 !important;
}

.modalgrid {
	width: 900px;
	height: 220px;
	padding: 0 !important;
}
</style>
<div
	class="row white z-depth-3 custom-listing-li"
	data-ng-init="init()">
	<div class="col s12">
		<h3>Vendor To Unit To SKU Mapping</h3>
	</div>
	<div class="col s12 m12 l6 xl6">
		<div class="row margin0">
			<label class="black-text">Value</label> <select
				ui-select2="selectedValue"
				id="vendorDataId"
				name="vendorData"
				data-ng-model="vendorDataType"
				data-ng-change="searchMappings()"
				data-ng-options="vendorData as (vendorData.name + ' [' + vendorData.category + ']') for vendorData in vendorList | orderBy : 'name' track by vendorData.id"></select>
			<!--data-ng-change="searchMappings()"-->
		</div>
		<div class="row margin0">
			<label class="black-text">Units</label> <select
				ui-select2="selectedValue"
				id="unitDataId"
				name="unitData"
				data-ng-model="unitDataType"
				data-ng-change="searchMappings()"
				data-ng-options="unitData as unitData.name for unitData in unitList | orderBy : 'name' track by unitData.id"></select>
			<!--data-ng-change="searchMappings()"-->
		</div>
	</div>
	<div
		class="col s12"
		style="">
		<div class="row">
			<div class="col s6 m6 l6 xl6">
				<button
					data-ng-click="searchMappings()"
					class="btn left"
					acl-action="SMVUSVI">Search</button>
			</div>
			<div class="col s6 m6 l6 xl6">
				<button
						data-ng-click="redirectToSkuPriceUpdate()"
						class="btn right"
						acl-action="SMVUSVI">Sku To Price</button>
			</div>
		</div>
	</div>
</div>
<div
	class="row"
	id="gridView"
	data-ng-if="gridOptions.data != null">
	<div class="col s12">
		<div
			id="mappingsGrid"
			ui-grid="gridOptions"
			class="grid col s12"
			ui-grid-resize-columns
			ui-grid-move-columns></div>
	</div>
</div>
<script
	type="text/ng-template"
	id="statusChangeButton.html">
      <div class="ui-grid-cell-contents">
		<button
				data-ng-class="{'btn btn-xs-small':row.entity.status=='IN_ACTIVE', 'btn red btn-xs-small':row.entity.status=='ACTIVE'}"
			ng-click="grid.appScope.changeStatus(row.entity)"
			ng-if="row.entity.status!='NA'" acl-action="SMVUSUP">
			<span ng-if="row.entity.status=='IN_ACTIVE'">Activate</span>
			<span ng-if="row.entity.status=='ACTIVE'">Deactivate</span>
		</button>
      </div>
</script>
<script
		type="text/ng-template"
		id="skuIdTemplate.html">
	<div class="ui-grid-cell-contents">
		<a style="cursor: pointer" data-ng-click="grid.appScope.showPreview($event, row.entity.sku.id,'SKU')">{{row.entity.sku.id}}</a>
	</div>
</script>