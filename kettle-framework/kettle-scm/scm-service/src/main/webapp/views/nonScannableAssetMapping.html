<div
        class="row white z-depth-3"
        data-ng-init="init()">
    <div class="col s12">
        <div class="row">
            <div class="col s12">
                <h4 class="center card-panel teal lighten-2 z-depth-1 cyan pulse" style="text-align: center;">Non
                    Scannable Products Mapping</h4>
            </div>
        </div>
    </div>
    <div>
		<div class="btn btn-small right" data-ng-click="submitData()">Submit</div>
        <div class="row margin0">
            <div
                    class="col s12 grid"
                    id="grid"
                    ui-grid="gridOptions"
                    ui-grid-save-state
                    ui-grid-edit
                    ui-grid-selection
                    ui-grid-resize-columns
                    ui-grid-move-columns></div>
        </div>
    </div>
</div>

<script
        type="text/ng-template"
        id="rowView.html">
    <div ng-repeat="(colRenderIndex, col) in colContainer.renderedColumns track by col.uid" ui-grid-one-bind-id-grid="rowRenderIndex + '-' + col.uid + '-cell' " class="ui-grid-cell"
         ng-class="{ green : (row.isSelected) }" role="{{col.isRowHeader ?'rowheader' : 'gridcell' }}" ui-grid-cell></div>

</script>