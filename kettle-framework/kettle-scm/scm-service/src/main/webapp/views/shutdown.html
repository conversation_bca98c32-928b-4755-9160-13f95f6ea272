<nav style="position: fixed; top:0;z-index: 9;">
    <div class="row" style="margin-bottom:0px;font-size:16px;">
        <div class="col s10">
            <a href="#" data-activates="nav-mobile" class="button-collapse top-nav full hide-on-large-only">
                <i class="material-icons">menu</i>
            </a> &nbsp;
            <img src="img/logo.png" style="height: 50px; float: left;" /><span class="page-title white-text">SuMo </span>
            <p style="margin:0; margin-left: 30px; display: inline;text-transform: uppercase;">{{unitName}}</p>
        </div>
        <div class="col s2">
            <a href="#" data-ng-click="logout()" class="white-text right btn" style="margin-top: 12px; color:#fff;">
                Logout
            </a>
        </div>
    </div>
</nav>
<div class="row" style="margin-top: 30px;" data-ng-init="init()">
    <div class="row">
        <div class="container center white-text" data-ng-class="{'red lighten-1' : isBlocked  ,'orange darken-2' : !isBlocked }"
             style="margin-top:5%;padding:1%;width:80%;margin-left:10%;">
            <h3 class="header">Attention !!!!</h3>
        </div>
        <div class="col s12 container center white-text" data-ng-class="{'red lighten-1' : isBlocked  ,'orange darken-2' : !isBlocked }"
             style="margin-top:1%;padding:2%;width:80%;margin-left:10%;" data-ng-if="regularDayCloseWarning">

            <div class="row">
                <div class="col s4" style="border-right:5px solid white;padding-top:2%;padding-bottom:2%;">
                    <h5>Weekly Day Close Pending</h5>
                </div>
                <div class="col s8" style="margin-top:10px;">
                    <h6 data-ng-if="!isBlocked"  class="header">
                        <p>
                            You have not done Day close for {{unit.name}}  For this week yet,<br>
                            Transfers To Your Unit Will Be Blocked in :
                        <div class="grey lighten-1 z-depth-3 black-text">
                            <span id="days"> {{CountDown.days}} </span>days
                            <span id="hours">{{CountDown.hours}} </span>hours
                            <span id="minutes">{{CountDown.minutes}} </span>minutes
                            <span id="seconds">{{CountDown.seconds}} </span>seconds
                        </div>
                        </p>
                    </h6>
                    <h6 data-ng-if="isBlocked" class="header">
                        <p>
                            You have not done Day close for  {{unit.name}}  For this Week yet,<br>
                            Transfers To Your Unit Is Blocked <br>
                            Please Do Day CLose To Unblock Transfers !!
                        </p>
                    </h6>
                </div>
            </div>
        </div>


        <div class="col s12 container center white-text" data-ng-class="{'red lighten-1' : varianceBlocking  ,'orange darken-2' : varianceWarning }"
             style="margin-top:1%;padding:2%;width:80%;margin-left:10%;" data-ng-if="showVarianceDisclaimer">
            <div class="row">
                <div class="col s4" style="border-right:5px solid white;padding-top:2%;padding-bottom:2%;">
                    <h5>Daily Variance Acknowledgement Pending</h5>
                </div>
                <div class="col s8" style="margin-top:10px;">
                    <h6  class="header" data-ng-if="varianceWarning">
                        <p>
                            You have not acknowledged variance for {{unit.name}} yet,<br>
                            Day Close Will be Blocked in :
                        <div class="grey lighten-1 z-depth-3 black-text">
                            <span id="ds"> {{CountDownVariance.days}} </span>days
                            <span id="hs">{{CountDownVariance.hours}} </span>hours
                            <span id="ms">{{CountDownVariance.minutes}} </span>minutes
                            <span id="ss">{{CountDownVariance.seconds}} </span>seconds
                        </div>
                        </p>
                    </h6>
                    <h6 data-ng-if="varianceBlocking" class="header">
                        <p>
                            You have not acknowledged variance for  {{unit.name}}  of previous week yet,<br>
                            Day Close Of Your Unit Is  Blocked <br>
                            Please Acknowledge Variance To Day Close !!
                        </p>
                    </h6>
                </div>
            </div>
        </div>
        <div class="col s12 container center white-text" data-ng-class="{'red lighten-1' : varianceWeeklyBlocking  ,'orange darken-2' : varianceWeeklyWarning }"
             style="margin-top:1%;padding:2%;width:80%;margin-left:10%;" data-ng-if="showVarianceWeeklyDisclaimer">
            <div class="row">
                <div class="col s4" style="border-right:5px solid white;padding-top:2%;padding-bottom:2%;">
                    <h5>Weekly Variance Acknowledgement Pending</h5>
                </div>
                <div class="col s8" style="margin-top:10px;">
                    <h6  class="header" data-ng-if="varianceWeeklyWarning">
                        <p>
                            You have not acknowledged variance for {{unit.name}} yet,<br>
                            Day Close Will Be Blocked soon
                        </p>
                    </h6>
                    <h6 data-ng-if="varianceWeeklyBlocking" class="header">
                        <p>
                            You have not acknowledged variance for  {{unit.name}}  of previous 2 weeks yet,<br>
                            Day Close Of Your Unit Is Blocked <br>
                            Please Acknowledge Weekly Variance To Day Close !!
                        </p>
                    </h6>
                </div>
            </div>
        </div>
        <div class="col s12 container center white-text"
             data-ng-show="showFAWarning"
             data-ng-class="{'red lighten-1' : pendingDayClose  ,'orange darken-2' : !pendingDayClose }"
             style="margin-top:1%;padding:2%;width:80%;margin-left:10%;">

            <div class="row">
                <div class="col s4" style="border-right:5px solid white;padding-top:2%;padding-bottom:2%;margin-top:1%;margin-bottom:-2%;">
                    <h5>Fixed Asset Day Close</h5>
                </div>
                <div class="col s8" style="margin-top:10px;">
                    <h6 data-ng-show="!pendingDayClose"  class="header">
                        <p>
                            <span data-ng-if="assetsDaily != null || assetsWeekly != null || assetsMonthly != null">
                            You have not done Fixed Assets Day close for  {{unit.name}}  For <span data-ng-if="assetsDaily != null">Today, </span>
                                <span data-ng-if="assetsWeekly != null ">This Week, </span>
                                <span data-ng-if="assetsMonthly != null ">This Month, </span><br>
                            Fixed Asset Receiving And Transfers From Your Unit Will Be Blocked If Day Close remains Pending <br>
                            Please Do Fixed Assets Day Close !!<br>
                            <span data-ng-if="assetsDaily != null">Daily DayClose Assets  : {{assetsDaily != null ? assetsDaily.length : 0}}<br></span>
                            <span data-ng-if="assetsWeekly != null">Weekly DayClose Assets : {{assetsWeekly != null ? assetsWeekly.length : 0}}<br></span>
                            <span data-ng-if="assetsMonthly != null">Monthly DayClose Assets : {{assetsMonthly != null ? assetsMonthly.length : 0}}<br></span>
                                </span>
                            <span data-ng-if="assetsDaily == null && assetsWeekly == null && assetsMonthly == null">
                                Fixed Assets Day Close Up-to-Date. Please Continue.
                                </span>

                        </p>
                    </h6>
                    <h6 data-ng-show="pendingDayClose" class="header">
                        <p>
                            <span data-ng-if="blockMonthly">Monthly (Last Done On : {{lastMonthlyDayCloseDate}}), </span>
                            <span data-ng-if="blockWeekly">Weekly (Last Done On : {{lastWeeklyDayCloseDate}}), </span>
                            <span data-ng-if="blockDaily">Daily (Last Done On : {{lastDailyDayCloseDate}}), </span>
                            <br>Fixed Assets Day Close not done for  {{unit.name}} yet,
                            Fixed Asset Receiving And Transfers From Your Unit Are Blocked.
                            Please Do Fixed Assets Day Close !!<br>
                            <span data-ng-if="assetsDaily != null">Daily DayClose Assets  : {{assetsDaily != null ? assetsDaily.length : 0}}<br></span>
                            <span data-ng-if="assetsWeekly != null">Weekly DayClose Assets : {{assetsWeekly != null ? assetsWeekly.length : 0}}<br></span>
                            <span data-ng-if="assetsMonthly != null">Monthly DayClose Assets : {{assetsMonthly != null ? assetsMonthly.length : 0}}<br></span>
                        </p>
                    </h6>
                </div>
            </div>
        </div>


        </div>


    </div>
    <div class="container right-align white-text" data-ng-class="{'red lighten-1' : isBlocked  ,'orange darken-2' : !isBlocked }"
         style="margin-top:0.5%;padding:1%;width:80%;margin-left:10%;">
        <button class="btn btn-large teal lighten-5 black-text " data-ng-click="goToHome()">Continue</button>
    </div>
</div>

