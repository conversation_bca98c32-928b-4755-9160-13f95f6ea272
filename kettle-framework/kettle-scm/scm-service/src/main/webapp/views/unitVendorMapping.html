<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="col s12">
        <div class="row">
            <div class="col s12">
                <h3>Unit Vendor Mapping (for Cafes' Specialized Ordering only)</h3>
            </div>
        </div>
        <div class="row">
            <div class="col s12">
                <label>Select Unit</label> <select data-ng-model="selectedUnit"
					data-ng-options="unit as unit.name for unit in unitList track by unit.id"
					data-ng-disabled="editType=='Update'" data-ng-change="changeUnit()"></select>
				<br /><br />
                <button class="btn" data-ng-click="getMappedVendors()" acl-sub-menu="SMUVMCVI">Get Vendors</button>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col s12">
            <ul class="collection striped" data-ng-show="mappedVendors.length>0">
                <li class="collection-item list-head">
                    <div class="row">
                        <div class="col s2">Name</div>
                        <div class="col s1">Fulfillment Type</div>
                         <div class="col s1">Notify Days</div>
                         <div class="col s1">Fulfillment Lead</div>
                         <div class="col s1">Notify Time</div>
                         <div class="col s1">Delivery Time</div>
                         <div class="col s2">Notify By(SMS,EMAIL)</div>
                        <div class="col s1">Status</div>
                        <div class="col s2">Action</div>
                    </div>
                </li>
                <li class="collection-item clickable" data-ng-repeat="vd in mappedVendors | orderBy : 'vendorId' track by vd.unitVendorMappingId">
                    <div class="row" style="margin-bottom: 0px;">
                        <div class="col s2">{{vd.vendor.name}}</div>
                        <div class="col s1">{{vd.fulFillmentType == null ? "-" : vd.fulFillmentType}}</div>
                        <div class="col s1">{{vd.noOfDays== null ? "-" : vd.noOfDays}}</div>
                        <div class="col s1">{{vd.fulfillmentLeadDays== null ? "-" : vd.fulfillmentLeadDays}}</div>
                        <div class="col s1">{{vd.notificationTime== null ? "-" : vd.notificationTime}}</div>
                        <div class="col s1">{{vd.deliveryPromiseTime == null ? "-" : vd.deliveryPromiseTime}}</div>
                         <div class="col s2">{{vd.smsNotification== true ? "SMS" : "-"}},{{vd.emailNotification== true ? "EMAIL" : "-"}}</div>
                        <div class="col s1">{{vd.mappingStatus}}</div>
                        <div class="col s2">
                            <button class="btn" data-ng-if="vd.mappingStatus=='ACTIVE'"
                                    data-ng-click="deactivateMapping(vd.unitVendorMappingId)" acl-action="SMUVMCDA">Deactivate</button>
                            <button class="btn" data-ng-if="vd.mappingStatus=='IN_ACTIVE'"
                                    data-ng-click="activateMapping(vd.unitVendorMappingId)" acl-action="SMUVMCAC">Activate</button>
                            <button class="btn" data-ng-if="vd.mappingStatus=='ACTIVE'"
                                    data-ng-click="editMapping(vd)" >Edit</button>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
    </div>

	<div class="row" data-ng-show="selectedUnit!=null">
		<div class="col s12">
			<button class="btn" data-ng-click="addNewForm(true)"
				acl-action="SMUVMCAD">Add new vendor</button>
			<div data-ng-if="showVendorAddForm" style="margin-top: 30px;">
				<div class="row">
					<div class="col s12">
						<label>Select Vendor</label> <select
							data-ng-model="selectedVendor"
							data-ng-change="selectVendor(selectedVendor)"
							data-ng-options="vendor as (vendor.name + ' [' + vendor.type + ']') for vendor in vendorList | orderBy:'name' track by vendor.id" data-ng-disabled="editType=='Update'"></select>
					</div>
				</div>
				<div class="row">
					<div class="col s12">
						<label>Fulfillment Type</label> <select
							data-ng-model="vendorMapping.fulFillmentType"
							data-ng-change="selectType(vendorMapping.fulFillmentType)"
							data-ng-options="type as type for type in fullFilmentType"></select>
					</div>
				</div>
				<div class="row" >
					<div class="col s12">
						<label>Select Location</label> <select
							data-ng-model="selectedLocation"
							data-ng-change="selectLocation(selectedLocation)"
							data-ng-options="location as (location.name + ' [' + location.status + ']') for location in locationList track by location.id" ></select>
					</div>
				</div>
				
				<div class="row">
					<div class="col s4">
						<label>RO Notification Type</label>
					</div>
					<div class="col s4" data-ng-click="htest()">
						<input type="checkbox" value="45" id="smsNotification" data-ng-model="vendorMapping.smsNotification" /><label
							class="black-text" for="smsNotification">SMS</label>
					</div>
					<div class="col s4">
						<input type="checkbox" value="54" id="emailNotification" data-ng-model="vendorMapping.emailNotification"/><label
							class="black-text" for="emailNotification">Email</label>
					</div>
				</div>
				<div class="row">
					<div class="col s2">
						<label>Notify Before </label>
					</div>
					<div class="col s3">
						<input type="number" class="form-element" data-ng-model="vendorMapping.noOfDays"/>
					</div>
					<div class="col s1">
						<label>day(s) </label>
					</div>
					
					<div class="col s2">
						<label>Notification Time </label>
					</div>
					<div class="col s3">
						<select data-ng-model="vendorMapping.notificationTime"
							data-ng-change="selectTime(vendorMapping.notificationTime)"
							data-ng-options="slots as slots for slots in timeSlots"></select>
					</div>
					<div class="col s1">
						<label>hr</label>
					</div>
				</div>
				<div class="row">
					<div class="col s2">
						<label>Fulfillment Lead Days</label>
					</div>
					<div class="col s3">
						<input type="number" class="form-element" data-ng-model="vendorMapping.fulfillmentLeadDays" data-ng-disabled="vendorMapping.fulFillmentType == 'SELF'"/>
					</div>
					<div class="col s1">
						<label>day(s) </label>
					</div>
					<div class="col s2">
						<label>Delivery Promise Time</label>
					</div>
					<div class="col s3">
						<select data-ng-model="vendorMapping.deliveryPromiseTime"
								data-ng-change="selectDeliveryPromiseTime(vendorMapping.deliveryPromiseTime)"
								data-ng-options="slots as slots for slots in timeSlots"></select>
					</div>
					<div class="col s1">
						<label>hr</label>
					</div>
				</div>
				<button class="btn" data-ng-click="addMapping()"
					acl-action="SMUVMCAD" data-ng-if="editType=='Add'">Add Vendor Details</button>
				<button class="btn" data-ng-click="updateMapping()"
					acl-action="SMUVMCAD"  data-ng-if="editType=='Update'">Update Vendor Details</button>
				<button class="btn red right" data-ng-click="cancelMapping()"
					acl-action="SMUVMCAD" >Cancel</button>
			</div>
		</div>
	</div>
</div>
