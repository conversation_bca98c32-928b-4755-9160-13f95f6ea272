<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->


<head>
    <link rel="stylesheet" href="css/multiselect.css">
</head>
<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()" style="padding: 0 10px;">
    <div class="col s12">
        <div id="searchDispatchScreen" data-ng-show="showSearchDispatchScreen">
            <h5>Create Dispatch</h5>
            <div class="row" style="background: #eee; padding: 10px; margin: 5px; border: #ddd 1px solid;">
                <div class="searchingCard row">
                    <div class="col s3">
                        <label>Dispatch Date*</label>
                        <input input-date type="text" ng-model="dispatchDate" container="" format="yyyy-mm-dd" min="{{minDate}}" max="{{maxDate}}"
                                data-ng-change="setDispatchDate(dispatchDate)"/>
                    </div>
                    <div class="col s3">
                        <label>Select Transport Mode*</label>
                        <select data-ng-options="mode as mode.name for mode in transportModes track by mode.id"
                                data-ng-model="selectedTransportMode" data-ng-change="getVehicles()"></select>
                    </div>
                    <div class="col s4">
                        <label>Select Vehicle*</label>
                        <select ui-select2 data-ng-options="vehicle as vehicle.name group by vehicle.suggested for vehicle in vehicles | orderBy: ['-suggested','name'] track by vehicle.vehicleId"
                                data-ng-change="selectVehicle(selectedVehicle)" data-ng-model="selectedVehicle"></select>
                    </div>
                    <div class="col s2">
                        <input type="button" class="btn" value="Search" data-ng-click="getDispatchList()"
                               style="margin-top: 20px;" />
                    </div>
                </div>
                <div class="row margin0" data-ng-if="selectedVehicle.name.indexOf('EMERGENCY') >= 0">
                    <label>Change Vehicle Registration Number</label>
                    <input type="text" placeholder="Registration Number" style="text-transform: uppercase;width:40%;"
                           data-ng-model="selectedVehicle.registrationNumber">
                    <span data-ng-if="inValidRegNumber">Please check if Registration Number is correct</span>
                </div>
                <div id="transporterFields" class="row margin0" data-ng-if="showTransporterFields">
                    <div class="col s8">
                        <label>Transporter Details</label>
                        <input type="text" placeholder="Transporter GSTIN" style="text-transform: uppercase;width:40%;"
                               data-ng-change="validateAndSaveId(transporterId)"
                               data-ng-model="transporterId">
                        <span data-ng-if="showValidGstText" style="cursor: pointer;"
                              tooltipped data-tooltip="GSTIN should have just 15 characters.
                            First 2 should be the state code, next 10 should be the PAN number, last three should be alphanumeric with 'Z' in between">
                            <i class="fa fa-times red-text"></i>
                        </span>
                        <input type="text" placeholder="Docket Number" style="text-transform: uppercase;width:40%;"
                               data-ng-change = "validateAndSaveName(docketNumber)"
                               data-ng-model="docketNumber">
                    </div>
                </div>
            </div>
        </div>

        <div id="createDispatchScreen" data-ng-if="showCreateDispatchScreen">
            <div class="row">
                <div class="col s12 right-align">
                    <h5 class="left">Search Dispatch</h5>
                    <input type="button" class="btn" value="Back" data-ng-click="goToSearchDispatchScreen()" style="margin-top: 10px;" />
                </div>
            </div>
            <div class="row" style="background: #f9bd7d; padding: 10px; margin:0 5px;">
                <div class="col s2"><label>Mode</label>{{selectedTransportMode.name}}</div>
                <div class="col s2"><label>Vehicle Name</label>{{selectedVehicle.name}}</div>
                <div class="col s2"><label>Vehicle Make</label>{{selectedVehicle.make}}</div>
                <div class="col s2"><label>Vehicle Model</label>{{selectedVehicle.model}}</div>
                <div class="col s2"><label>Vehicle Number</label>{{selectedVehicle.registrationNumber}}</div>
                <div class="col s2"><label>Dispatch Date</label>{{dispatchDate}}</div>
            </div>
            <div data-ng-if="dispatchList != null">
                <div class="row" style="background: #f9bd7d; border-top:#eee 1px solid; padding: 10px; margin: 0px 5px 10px 5px; font-weight: bold;">
                    <div class="col s6">Dispatch Id</div>
                    <div class="col s6">Dispatch Status</div>
                </div>
                <div data-ng-repeat="dispatchItem in dispatchList" style="border:#ccc 1px solid; margin: 0 5px 5px 5px;">
                    <div class="row" data-ng-click="dispatchItem.showDetail!=true?dispatchItem.showDetail=true:dispatchItem.showDetail=false"
                         style="background: #cec2f1; padding: 10px; margin: 0; cursor: pointer;">
                        <div class="col s6">{{dispatchItem.dispatchId}}</div>
                        <div class="col s6">
                            {{dispatchItem.status}}
                            <span style="float:right; font-weight: bold;font-size: 18px;" data-ng-if="dispatchItem.showDetail!=true">&plus;</span>
                            <span style="float:right; font-weight: bold;font-size: 18px;" data-ng-if="dispatchItem.showDetail==true">&minus;</span>
                        </div>
                    </div>
                    <div data-ng-show="dispatchItem.showDetail==true">
                        <div class="row" data-ng-if="dispatchItem.consignmentList.length > 0" style="border: #ddd 1px solid; margin: 20px 5px 5px;">
                            <div class="col s12" style="padding:0;">
                                <table class="table bordered striped">
                                    <tr>
                                        <th>Consignment Id</th>
                                        <th>Consignment Type</th>
                                        <th>From Unit</th>
                                        <th>Status</th>
                                        <th>Action</th>
                                    </tr>
                                    <tr data-ng-repeat="item in dispatchItem.consignmentList | orderBy : 'consignmentUnit.name' track by item.consigmentId">
                                        <td>{{item.consigmentId}}</td>
                                        <td>{{item.consignmentType}}</td>
                                        <td>{{item.consignmentUnit.name}}</td>
                                        <td>{{item.status}}</td>
                                        <td>
                                            <input type="button" class="btn" value="View" data-ng-click="viewConsignmentData(item)"/>
                                            <input type="button" class="btn" value="Cancel" data-ng-if="item.status!='SETTLED'" data-ng-click="cancelConsignment(item.consigmentId)"/>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                        <div class="row" data-ng-if="dispatchItem.status == 'CREATED'" style="margin:10px;">
                            <div class="col s12">
                                <input type="button" class="btn" value="Add Transfers" data-ng-click="addConsignment('TRANSFER', dispatchItem)" />
                                <input type="button" class="btn" value="Add Receivings" data-ng-click="addConsignment('RECEIVING', dispatchItem)" />
                                <input type="button" class="btn" value="Start Dispatch" style="float:right;" data-ng-click="startDispatch(dispatchItem)" />
                                <span style="margin-top: .45rem;float: right;display: inline-block;margin-right: 0.45rem;background-color: #ddd;padding: .2rem;border: 1px solid #8e6a6a;border-radius: 3px;align-items: center;">
                                    <input style="position: relative; opacity: 1; left: auto;" type="checkbox" value="forceEway" data-ng-model="forceEway" data-ng-change="changeForceEway(forceEway)"/> Check to send notification
                                </span>
                                <input type="button" class="btn" value="Download E-Way File" style="display: none;" data-ng-click="downloadEwayFile(dispatchItem)" />
                            </div>
                        </div>
                        <div class="row" style="margin: 10px;" data-ng-if="dispatchItem.status=='SETTLED'">
                            <div class="col s12">
                                <input type="button" class="btn" value="Print Transfer Orders" data-ng-if="dispatchItem.status=='SETTLED'"
                                       data-ng-click="getDispatchDetailForPrint(dispatchItem)"/>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col s12 right-align">
                    <input type="button" class="btn" value="Create Dispatch" data-ng-click="createDispatch()" data-ng-if="showCreateDispatch==true" />
                </div>
            </div>
        </div>

        <div id="createConsignmentScreen" data-ng-show="showCreateConsignmentScreen">
            <div class="row">
                <div class="col s12">
                    <h5>Create <span style="text-transform: capitalize;">{{consignmentType}}</span> Consignment</h5>
                    <input type="button" class="btn" value="Back" data-ng-click="goToCreateDispatchScreen()" style="float: right;"/>
                </div>
            </div>
            <div class="row" style="background: #eee; padding: 10px; margin: 5px; border: #ddd 1px solid;">
                <div class="col s4">
                    <label>From Unit ({{consignmentUnits.length}})</label>
                    <div>
                        <div class="col-lg-12" style="text-align: left" ng-dropdown-multiselect="" options="consignmentUnits"
                             selected-model="selectedConsignmentUnits"
                             extra-settings="multiSelectSetting" data-ng-if="consignmentUnitsSuggested.length == 0">
                        </div>
                        <div class="col-lg-12" style="text-align: left" ng-dropdown-multiselect="" options="consignmentUnitsSuggested"
                             selected-model="selectedConsignmentUnits"
                             extra-settings="multiSelectSetting" data-ng-if="consignmentUnitsSuggested.length > 0">
                        </div>
                    </div>
                </div>
                <div class="col s6">
                    <label>Selected units ({{selectedConsignmentUnits.length}})</label>
                    <span style="background: #ccc; border-radius: 3px; margin: 3px; padding: 5px; cursor: pointer; display: inline-block;"
                          data-ng-repeat="item in selectedConsignmentUnits track by $index"
                          data-ng-click="removeConsignmentUnit($index)">{{item.name}} &times;</span>
                </div>
            </div>
            <div class="row" style="background: #eee; padding: 10px; margin: 5px; border: #ddd 1px solid;">
                <div class="col s4">
                    <label>To Unit ({{consignmentReceivingUnits.length}})</label>
                    <div>
                        <div class="col-lg-12" style="text-align: left" ng-dropdown-multiselect="" options="consignmentReceivingUnits"
                             selected-model="selectedConsignmentReceivingUnits"
                             extra-settings="multiSelectSetting" data-ng-if="consignmentReceivingUnitsSuggested.length == 0">
                        </div>
                        <div class="col-lg-12" style="text-align: left" ng-dropdown-multiselect="" options="consignmentReceivingUnitsSuggested"
                             selected-model="selectedConsignmentReceivingUnits"
                             extra-settings="multiSelectSetting" data-ng-if="consignmentReceivingUnitsSuggested.length > 0">
                        </div>
                    </div>
                </div>
                <div class="col s6">
                    <label>Selected units ({{selectedConsignmentReceivingUnits.length}})</label>
                    <span style="background: #ccc; border-radius: 3px; margin: 3px; padding: 5px; cursor: pointer; display: inline-block;"
                          data-ng-repeat="item in selectedConsignmentReceivingUnits track by $index"
                          data-ng-click="removeConsignmentReceivingUnit($index)">{{item.name}} &times;</span>
                </div>
            </div>
            <div class="row" style="background: #eee; padding: 10px; margin: 5px; border: #ddd 1px solid;">
                <div class="col s4">
                    <label>Start date</label>
                    <input input-date type="text" ng-model="startDate" container="" format="yyyy-mm-dd" />
                </div>
                <div class="col s4">
                    <label>End date</label>
                    <input input-date type="text" ng-model="endDate" container="" format="yyyy-mm-dd"/>
                </div>
                <div class="col s4">
                    <input type="button" class="btn" value="Search" data-ng-click="getConsignmentItems()"
                           style="margin-top: 20px;"/>
                </div>
            </div>
            <div class="row" style="border: #ddd 1px solid; margin: 20px 5px 5px;" data-ng-if="consignmentItems.length > 0">
                <div class="col s12" style="padding:0;">
                    <table class="table bordered striped">
                        <tr>
                            <th>
                            <span>
                                <input id="allConsignments" type="checkbox"
                                       data-ng-change="selectAllConsignmentItems()"
                                       data-ng-model="selectAllConsignments"
                                       data-ng-checked="selectAllConsignments==true"/>
                                <label for="allConsignments">All</label>
                            </span>
                            </th>
                            <th>TO Number</th>
                            <th>From Unit</th>
                            <th>To Unit</th>
                            <th>Distance</th>
                            <th>Generation Date</th>
                            <th>Status</th>
                            <th>Action</th>
                        </tr>
                        <tr data-ng-repeat="item in consignmentItems | orderBy : ['generationUnitId.name', 'generatedForUnitId.name'] track by item.id">
                            <td>
                                <span>
                                    <input id="cg-{{item.id}}" type="checkbox" data-ng-model="item.selected" data-ng-checked="item.checked==true" />
                                    <label for="cg-{{item.id}}"></label>
                                </span>
                            </td>
                            <td>{{item.id}}</td>
                            <td>{{item.generationUnitId.name}}</td>
                            <td>{{item.generatedForUnitId.name}}</td>
                            <td>{{item.distance}}</td>
                            <td>{{item.generationTime | date:'dd-MM-yyyy hh:mm:ss a'}}</td>
                            <td>{{item.status}}</td>
                            <td><input type="button" class="btn" value="View" data-ng-click="getTODetails(item.id)"/></td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="row" data-ng-if="consignmentItems.length > 0">
                <div class="col s12 right-align">
                    <input type="button" class="btn" data-ng-click="createConsignment()" value="Submit"/>
                </div>
            </div>
        </div>

        <div id="consignmentDetailScreen" data-ng-if="showConsignmentDetailScreen">
            <div class="row">
                <div class="col s12 right-align">
                    <h5 style="float: left;">Consignment Detail</h5>
                    <input type="button" class="btn" value="Back" data-ng-click="goToCreateDispatchScreen()">
                </div>
            </div>
            <div class="row">
                <div class="col s12">
                    <table class="table" style="background: #efefef; border:#ddd 1px solid;">
                        <tr>
                            <th>Consignment Id</th>
                            <th>Consignment Type</th>
                            <th>From Unit</th>
                            <th>Status</th>
                        </tr>
                        <tr>
                            <td>{{viewConsignmentItem.consigmentId}}</td>
                            <td>{{viewConsignmentItem.consignmentType}}</td>
                            <td>{{viewConsignmentItem.consignmentUnit.name}}</td>
                            <td>{{viewConsignmentItem.status}}</td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="row">
                <div class="col s12">
                    <table class="table bordered striped" style="border:#ddd 1px solid;">
                        <tr>
                            <th>EWB Id</th>
                            <th>EWB No.</th>
                            <th>Generation Unit</th>
                            <th>Generated For Unit</th>
                            <th>Distance</th>
                            <th>EWB Status</th>
                            <th>Action</th>
                        </tr>
                        <tr data-ng-repeat="item in viewConsignmentItem.ewaybills | orderBy : ['transferOrder.generationUnitId.name', 'transferOrder.generatedForUnitId.name'] track by item.id">
                            <td>{{item.id}}</td>
                            <td>{{item.ewayBillNumber}}</td>
                            <td>{{item.transferOrder.generationUnitId.name}}</td>
                            <td>{{item.transferOrder.generatedForUnitId.name}}</td>
                            <td>{{item.distance}}</td>
                            <td>{{item.status}}</td>
                            <td>
                                <input type="button" value="View" class="btn" data-ng-click="getEwayBillDetails(item.id)"/>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>


    </div>


</div>


<script type="text/ng-template" id="ewaySheetPreviewModal.html">
    <div>
        <div class="row" style="margin-bottom: 0;">
            <div class="col s12">
                <table class="table bordered striped">
                    <tr>
                        <th>Serial No.</th>
                        <th>Supply Type</th>
                        <th>Doc No.</th>
                        <th>Doc Date</th>
                        <th>Other Party GSTN</th>
                        <th>Supply State</th>
                        <th>Vehicle No.</th>
                        <th>No. of Items</th>
                        <th>EWB No.</th>
                        <th>EWB Date</th>
                        <th>EWB Status</th>
                        <th>Errors</th>
                    </tr>
                    <tr data-ng-repeat="item in uploadedDocData track by $index">
                        <td>{{item.slNo}}</td>
                        <td>{{item.supplyType}}</td>
                        <td>{{item.docNo}}</td>
                        <td>{{item.docDate | date:'dd-MM-yyyy hh:mm:ss a'}}</td>
                        <td>{{item.otherPartyGstin}}</td>
                        <td>{{item.supplyState}}</td>
                        <td>{{item.vehicleNo}}</td>
                        <td>{{item.noOfItems}}</td>
                        <td>{{item.ewbNo}}</td>
                        <td>{{item.ewdDate}}</td>
                        <td>{{item.ewbStatus}}</td>
                        <td>{{item.errors}}</td>
                    </tr>
                </table>
            </div>
        </div>
        <div class="row right-align">
            <div class="co s12">
                <button class="btn" data-ng-click="closeModal('CANCEL')" style="margin-right: 20px;">Close</button>
                <button class="btn" data-ng-click="closeModal('SUBMIT')" style="margin-right: 20px;">Submit</button>
            </div>
        </div>
    </div>
</script>


<script type="text/ng-template" id="ewayBillPreviewModal.html">
    <div>
        <div class="row" style="margin-bottom: 0px;">
            <div class="col s12">
                <div class="row" data-ng-if="viewEwayBill.type=='EWAY'"
                     style="background: #eee; border: #ddd 1px solid; margin: 5px; padding: 10px;">
                    <div class="col s2">
                        <label>EWB Id</label>{{viewEwayBill.id}}
                    </div>
                    <div class="col s3">
                        <label>EWB Number</label>{{viewEwayBill.ewayBillNumber}}
                    </div>
                    <div class="col s2">
                        <label>Distance</label>{{viewEwayBill.distance}}
                    </div>
                    <div class="col s2">
                        <label>Status</label>{{viewEwayBill.status}}
                    </div>
                    <div class="col s3">
                        <label>Vehicle</label>{{viewEwayBill.vehicle.name}}[{{viewEwayBill.vehicle.vehicleNumber}}]
                    </div>
                </div>
                <div class="row">
                    <div class="col s2">
                        <label>Transfer order Id</label>{{viewEwayBill.transferOrder.id}}
                    </div>
                    <div class="col s2">
                        <label>Request order Id</label>{{viewEwayBill.transferOrder.requestOrderId}}
                    </div>
                    <div class="col s2">
                        <label>GR Id</label>{{viewEwayBill.transferOrder.goodsReceivedId}}
                    </div>
                    <div class="col s4">
                        <label>Generated for unit</label>{{viewEwayBill.transferOrder.generatedForUnitId.name}}
                    </div>
                    <div class="col s2">
                        <label>Status</label>{{viewEwayBill.transferOrder.status}}
                    </div>
                </div>
                <div class="row">
                    <div class="col s4">
                        <label>Generation unit</label>{{viewEwayBill.transferOrder.generationUnitId.name}}
                    </div>
                    <div class="col s8">
                        <label>Comment</label>{{viewEwayBill.transferOrder.comment==null?'No comments':viewEwayBill.transferOrder.comment}}
                    </div>
                </div>
                <div class="row">
                    <div class="col s12">
                        <ul class="collection">
                            <li class="collection-item list-head">
                                <div class="row">
                                    <div class="col s2">Product Name</div>
                                    <div class="col s2">Requested Qty.</div>
                                    <div class="col s2">Absolute Qty.</div>
                                    <div class="col s2">Transferred Qty.</div>
                                    <div class="col s2">Received Qty.</div>
                                    <div class="col s2">Unit of Measure</div>
                                </div>
                            </li>
                            <li class="collection-item"
                                data-ng-repeat="item in viewEwayBill.transferOrder.transferOrderItems track by $index">
                                <div class="row sku-title-strip">
                                    <div class="col s2">{{item.skuName}} [{{item.skuId}}]</div>
                                    <div class="col s2">{{item.requestedQuantity}}</div>
                                    <div class="col s2">{{item.requestedAbsoluteQuantity}}</div>
                                    <div class="col s2">{{item.transferredQuantity}}</div>
                                    <div class="col s2">{{item.receivedQuantity}}</div>
                                    <div class="col s2">{{item.unitOfMeasure}}</div>
                                </div>
                                <div class="row" style="margin-bottom: 0;">
                                    <div class="col s2">
                                        <label>Packaging</label>
                                    </div>
                                    <div class="col s2">
                                        <label>Units packed</label>
                                    </div>
                                    <div class="col s2">
                                        <label>Transferred Qty.</label>
                                    </div>
                                    <div class="col s2">
                                        <label>Units Received</label>
                                    </div>
                                    <div class="col s2">
                                        <label>Received Qty.</label>
                                    </div>
                                    <div class="col s2">
                                        <label>Unit of Measure</label>
                                    </div>
                                </div>
                                <div class="row" data-ng-repeat="pkg in item.packagingDetails"
                                     style="margin-bottom: 0px;">
                                    <div class="col s2">{{pkg.packagingDefinitionData.packagingName}}</div>
                                    <div class="col s2">{{pkg.numberOfUnitsPacked==null?0:pkg.numberOfUnitsPacked}}
                                    </div>
                                    <div class="col s2">{{pkg.transferredQuantity==null?0:pkg.transferredQuantity}}
                                    </div>
                                    <div class="col s2">
                                        {{pkg.numberOfUnitsReceived==null?0:pkg.numberOfUnitsReceived}}
                                    </div>
                                    <div class="col s2">{{pkg.receivedQuantity==null?0:pkg.receivedQuantity}}</div>
                                    <div class="col s2">{{pkg.packagingDefinitionData.unitOfMeasure}}</div>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        <div class="row right-align">
            <div class="co s12">
                <button class="btn" data-ng-click="closeModal()" style="margin-right: 20px;">Close</button>
            </div>
        </div>
    </div>
</script>


<!--   printable TO section   -->
<div style="width: 100%;" id="printSection">
    <div data-ng-repeat="consignment in dispatchData.consignmentList">
        <div class="page" data-ng-repeat="ewb in consignment.ewaybills">
            <div class="print-border-table" style="font-size: 12px;">
                <div style="text-align: center;">
                    <p style="font-size: 21px;">
                    <div style="font-weight: bold; font-style: italic;">Form GST INV &ndash; 1</div>
                    <div style="font-weight: bold;">CHAAYOS</div>
                    </p>
                    <span style="font-size: 13.0pt;">{{companyMap[ewb.transferOrder.sourceCompany.id].name}}<br/></span>
                    <span>
                        {{companyMap[ewb.transferOrder.sourceCompany.id].registeredAddress.line1}},
                        {{companyMap[ewb.transferOrder.sourceCompany.id].registeredAddress.line2}}, <br/> {{companyMap[ewb.transferOrder.sourceCompany.id].registeredAddress.city}},
                        {{companyMap[ewb.transferOrder.sourceCompany.id].registeredAddress.state}}, <br/> {{companyMap[ewb.transferOrder.sourceCompany.id].registeredAddress.country}},
                        {{companyMap[ewb.transferOrder.sourceCompany.id].registeredAddress.zipCode}}<br/>
                    </span>
                    <div style="font-weight: bold;font-size: 13.0pt;">Invoice</div>
                    <div style="font-weight: bold;font-size: 13.0pt;">E-Way Bill Number:
                        {{ewb.ewayBillNumber!=null?ewb.ewayBillNumber:'NA'}}
                    </div>
                </div>

                <table style="margin-top: 30px;">
                    <tr>
                        <td>Sending Unit Details</td>
                        <td></td>
                        <td>Receiving Unit Details</td>
                        <td>Billed To/Shipped To</td>
                    </tr>
                    <tr>
                        <td>Unit Name</td>
                        <td>{{ewb.transferOrder.generationUnitId.name}}</td>
                        <td>Unit Name</td>
                        <td>{{ewb.transferOrder.generatedForUnitId.name}}</td>
                    </tr>
                    <tr>
                        <td>Company Name</td>
                        <td>{{companyMap[ewb.transferOrder.sourceCompany.id].name}}</td>
                        <td>Company Name</td>
                        <td>{{companyMap[ewb.transferOrder.receivingCompany.id].name}}</td>
                    </tr>
                    <tr>
                        <td>Address</td>
                        <td>{{ewb.transferOrder.generationUnitId.address}}</td>
                        <td>Address</td>
                        <td>{{ewb.transferOrder.generatedForUnitId.address}}</td>
                    </tr>
                    <tr>
                        <td>State/State Code</td>
                        <td>{{ewb.transferOrder.fromState.name}}/{{ewb.transferOrder.fromState.code}}</td>
                        <td>State/State Code</td>
                        <td>{{ewb.transferOrder.toState.name}}/{{ewb.transferOrder.toState.code}}</td>
                    </tr>
                    <tr>
                        <td>GSTIN</td>
                        <td>{{ewb.transferOrder.generationUnitId.tin}}</td>
                        <td>GSTIN</td>
                        <td>{{ewb.transferOrder.generatedForUnitId.tin}}</td>
                    </tr>
                    <tr>
                        <td>CIN</td>
                        <td>{{companyMap[ewb.transferOrder.sourceCompany.id].cin}}</td>
                        <td>CIN</td>
                        <td>{{companyMap[ewb.transferOrder.receivingCompany.id].cin}}</td>
                    </tr>
                </table>
                <table style="margin-top: 100px;">
                    <tr>
                        <td colspan="2">Transfer Detail</td>
                        <td>Invoice No.</td>
                        <td>{{ewb.transferOrder.invoiceId}}</td>
                    </tr>
                    <tr>
                        <td>Transfer Order Id</td>
                        <td>{{ewb.transferOrder.id}}</td>
                        <td>Date of Invoice</td>
                        <td>{{ewb.transferOrder.generationTime | date:'dd-MM-yyyy':'+0530'}}</td>
                    </tr>
                    <tr>
                        <td>Request Order Id</td>
                        <td>{{ewb.transferOrder.requestOrderId}}</td>
                        <td>Mode of Transport</td>
                        <td>By Road / By Train / By Air</td>
                    </tr>
                    <tr>
                        <td>Total Items</td>
                        <td>{{ewb.transferOrder.transferOrderItems.length}}</td>
                        <td>Vehicle No.</td>
                        <td>____________________________</td>
                    </tr>
                    <tr>
                        <td>Place of Supply:</td>
                        <td>{{ewb.transferOrder.generationUnitId.city}}</td>
                    </tr>
                </table>
                <table style="margin-top: 100px;">
                    <tr>
                        <th>Description</th>
                        <th>HSN</th>
                        <th>RO QTY.</th>
                        <th>Invoice QTY.</th>
                        <th>UOM</th>
                        <th>Price</th>
                        <th>Amount</th>
                        <th>Packaging</th>
                        <td data-ng-repeat="t in ewb.transferOrder.availableTaxes">
                            {{t}}
                        </td>
                        <th>Total</th>
                    </tr>
                    <tr data-ng-repeat="item in ewb.transferOrder.transferOrderItems track by $index">
                        <td>{{item.skuName}} [{{item.skuCode}}]</td>
                        <td>{{item.code}}</td>
                        <td>{{item.requestedQuantity}}</td>
                        <td>{{item.transferredQuantity}}</td>
                        <td>{{item.unitOfMeasure}}</td>
                        <td>{{item.negotiatedUnitPrice | currency :'': 2}}</td>
                        <td>{{item.total | currency :'': 2}}</td>
                        <td>
                            <span ng-repeat="packaging in item.packagingDetails">
                                {{packaging.numberOfUnitsPacked}} -- {{packaging.packagingDefinitionData.packagingName}} <br/>
                            </span>
                        </td>
                        <td data-ng-repeat="t in item.taxes">{{t.value}} ({{t.percentage}}%)</td>
                        <td>{{(item.total + item.tax) | currency :'': 2}}</td>
                    </tr>
                    <tr>
                        <td colspan="{{8 + ewb.transferOrder.availableTaxes.length}}" style="font-weight: bold;">TOTAL
                        </td>
                        <td>{{ewb.transferOrder.totalPrice | currency :'': 2}}</td>
                    </tr>
                    <tr>
                        <td colspan="{{8 + ewb.transferOrder.availableTaxes.length}}" style="font-weight: bold;">Total
                            Invoice Value (In
                            figure)
                        </td>
                        <td>{{ewb.transferOrder.totalPrice | currency :'': 2}}</td>
                    </tr>
                    <tr>
                        <td colspan="{{9 + ewb.transferOrder.availableTaxes.length}}" style="font-weight: bold;">Total
                            Invoice Value (In
                            Words) <span style="margin-left: 50px;">{{ewb.transferOrder.totalPriceInWords}}</span>
                        </td>
                    </tr>
                </table>
                <p style="font-weight: bold;">
                    Certified that the particulars and the amount indicated given above are true and correct
                </p>
                <table>
                    <tr>
                        <th>TERMS OF SALE</th>
                        <th>For {{companyMap[ewb.transferOrder.sourceCompany.id].name}}</th>
                    </tr>
                    <tr>
                        <td>
                            1)Goods once sold will not be taken back or exchanged<br/>
                            2)Seller isnot responsible for any loss or damaged of goods in transit<br/>
                            3)Buyer undertakes to submit prescribed declaration to sender on demand.<br/>
                            4)Disputes if any will be subject to seller court jurisdiction<br/><br/>
                        </td>
                        <td></td>
                    </tr>
                    <tr>
                        <td></td>
                        <td>Authorised Signatory</td>
                    </tr>
                </table>
                <p>Reg. Address: {{companyMap[ewb.transferOrder.sourceCompany.id].registeredAddress.line1}},
                    {{companyMap[ewb.transferOrder.sourceCompany.id].registeredAddress.line2}},
                    {{companyMap[ewb.transferOrder.sourceCompany.id].registeredAddress.city}},
                    {{companyMap[ewb.transferOrder.sourceCompany.id].registeredAddress.state}},
                    {{companyMap[ewb.transferOrder.sourceCompany.id].registeredAddress.country}},
                    Pin No:
                    {{companyMap[ewb.transferOrder.sourceCompany.id].registeredAddress.zipCode}}
                </p>
            </div>
        </div>
    </div>

</div>

<script type="text/ng-template" id="unitDistanceView.html">

    <div class="modal-header" data-ng-init="init()">
        <h3 class="modal-title" id="modal-title" style="margin-top: 0px;">Distance Mapping Not Found between Units {{distanceMapResponse.sourceUnitId}} and {{distanceMapResponse.destinationUnitId}}</h3>
        <hr>
    </div>
    <div data-ng-if="isValid==null">
        <div class="form-group form-inline">
            <label for="govtUrl">Govt Website Url</label>
            <input class="form-control" data-ng-model="govtEwayUrl" id ="govtUrl"
                   placeholder="Eway Distance Url" required type="text" data-ng-class="{'green white-text':isCopied(govtEwayUrl)}" style="width: 76%" disabled/>
            <button ng-click="goToLink(govtEwayUrl)" title="copy url" style="border: whitesmoke;  color: #0f9d58;"
                    >Go To Link <i class="material-icons" style="margin-bottom: 0px;">link</i>
            </button>
        </div>
        <label for="sourceId">Source pin Code</label>
        <input class="form-control" id = "sourceId" data-ng-model="sourcePinCode" data-ng-class="{'green white-text':isCopied(sourcePinCode)}"
               placeholder="Source Pin Code" required type="text" style="width: 76%" disabled/>
        <button ng-click="copyToClipboard(sourcePinCode)" title="copy Source Pin Code"
                style="border: none; background: none;"><em class="fa fa-copy"
                                                            style="font-size: 20px; "></em>
        </button>
        <label for="destinationId">Destination pin Code</label>
        <input class="form-control" id = "destinationId" data-ng-model="destinationPinCode" data-ng-class="{'green white-text':isCopied(destinationPinCode)}"
               placeholder="Destination Pin Code" required type="text" style="width: 76%" disabled/>
        <button ng-click="copyToClipboard(destinationPinCode)" title="copy Destination Pin Code"
                style="border: none;  background: none;"><em class="fa fa-copy"
                                                             style="font-size: 20px; "></em>
        </button>
        <label> copy Above url and Enter Above Both Pincodes there And Then Enter the Response Below </label>
        <input class="form-control" id = "userDistance" type="number" style="width: 76%" data-ng-model="userInput" placeholder="Enter Response From Govt Website">
        <button class="btn-toolbar  btn-medium center " ng-click="setDistance(userInput)" title="send"
                style="border: whitesmoke;  color: #0f9d58; text-align: center">Send <i  class="material-icons " style="padding-bottom: 0px;">send</i>
        </button>
    </div>

    <div data-ng-if="isValid!=null">
        <div class="card  darken-1" data-ng-class="{'green' : isValid == true ,
        'red ' : isValid == false}">
            <div class="card-content white-text">
                <span data-ng-if="isValid == true" class="card-title">Mapping Successfully Updated</span>
                <span data-ng-if="isValid == false" class="card-title">Could not update Distance Mapping</span>
                <p data-ng-if="isValid == true">System Found Distance is Within Govt Allowed Range . Now You Can Create Dispatch</p>
                <p data-ng-if="isValid == false">System Found Distance is Not Within Govt Allowed Range . PLease Contact SCM Team For Updation Of Mapping For Source Unit :  {{distanceMapResponse.sourceUnitId}}
                    and Destiantion unit Id: {{distanceMapResponse.destinationUnitId}}</p>
            </div>
            <div class="card-action">
                <button  class="btn-medium" data-ng-click="closeModal()">Close Modal</button>
            </div>
        </div>
    </div>


</script>
