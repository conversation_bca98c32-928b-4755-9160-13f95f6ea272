<style>
.grid {
	height: 550px;
	padding: 0 !important;
}
.button-33 {
background-color: #c2fbd7;
border-radius: 100px;
box-shadow: rgba(44, 187, 99, .2) 0 -25px 18px -14px inset,rgba(44, 187, 99, .15) 0 1px 2px,rgba(44, 187, 99, .15) 0 2px 4px,rgba(44, 187, 99, .15) 0 4px 8px,rgba(44, 187, 99, .15) 0 8px 16px,rgba(44, 187, 99, .15) 0 16px 32px;
color: green;
cursor: pointer;
display: inline-block;
font-family: CerebriSans-Regular,-apple-system,system-ui,Roboto,sans-serif;
padding: 7px 20px;
text-align: center;
text-decoration: none;
transition: all 250ms;
border: 0;
font-size: 16px;
user-select: none;
-webkit-user-select: none;
touch-action: manipulation;
}

.button-33:hover {
box-shadow: rgba(44,187,99,.35) 0 -25px 18px -14px inset,rgba(44,187,99,.25) 0 1px 2px,rgba(44,187,99,.25) 0 2px 4px,rgba(44,187,99,.25) 0 4px 8px,rgba(44,187,99,.25) 0 8px 16px,rgba(44,187,99,.25) 0 16px 32px;
transform: scale(1.05) rotate(-1deg);
}
</style>
<div
	class="row white z-depth-3 custom-listing-li"
	data-ng-init="init()">
	<div class="col s12">
		<h4>Vendor to SKU Price Update</h4>
	</div>
	<div class="col s12 m12 l6 xl6">
		<div class="row margin0">
				<label class="black-text">Search Type</label> <select
				ui-select2="selectSearchType"
				data-ng-model="searchType"
				data-ng-options="t as t.name for t in searchTypeList"
				data-ng-change="showValues()">
			</select>
		</div>
		<div class="row margin0">
			<label class="black-text">{{searchType.name}} Value</label> <select
				id="selectSkuForPriceUpdateId"
				ui-select2="selectSkuForPriceUpdate"
				data-ng-model="sourceValue"
				data-placeholder="Select value"
				data-ng-options="s as (s.name + ' [' + s.category + ']') for s in searchValues| orderBy : 'name'  track by s.id">
			</select>
		</div>
		<div class="row margin0">
			<label class="black-text">Delivery Location</label> <select
				ui-select2="selectDeliveryLocation"
				data-ng-model="deliveryLocation"
				data-placeholder="Select Delivery Location"
				data-ng-options="location as location.name+ ', ' +location.state.name for location in allDeliveyLocationList | orderBy : 'name' track by location.id"></select>
		</div>
	</div>
	<div
		class="col s12"
		style="">
		<div class="row">
			<div class="col s6 m6 l6 xl6">
				<button
					data-ng-click="showPricing()"
					class="btn left"
					acl-action="SMVUSPVI">Search</button>
			</div>
			<div class="col s6 m6 l6 xl6">
				<span
					data-ng-click="getDataForAddModal()"
					data-ng-if="sourceValue != null && deliveryLocation != null"
					acl-action="SMVUSPAD"> <a
						href="#addSkuPriceMappingModal"
						class="btn right"
						style="margin-right: 10px"
						modal> <b>+</b> Add
					</a>
				</span>
			</div>
		</div>
	</div>
</div>

<div class="col s12 center" style="justify-content: center">
	<button
			class="button-33" role="button"
			ng-click="previewUpdationModel()"
			data-ng-if="isEntryFoundForUpdation()">
		<span>Preview</span>
	</button>
</div>
</br>
<div
	class="row"
	id="gridView"
	data-ng-if="showGrid()">
	<div class="col s12">
		<div
			id="mappingsGrid"
			ui-grid="priceGridOptions"
			ui-grid-edit
			ui-grid-row-edit
			ui-grid-cellNav
			ui-grid-resize-columns
			ui-grid-move-columns
			class="grid col s12"></div>
	</div>
</div>
<div
	data-ng-if="showNoDataMsg()"
	class="text-center-disabled">
	No mappings to display. <br>Click Add to add new mappings.
</div>

<script
	type="text/ng-template"
	id="statusChangeButton.html">
      <div class="ui-grid-cell-contents">
<!--		<button-->
<!--				data-ng-class="{'btn btn-xs-small':row.entity.status=='IN_ACTIVE', 'btn red btn-xs-small':row.entity.status=='ACTIVE'}"-->
<!--			ng-click="grid.appScope.changeStatus(row.entity)"-->
<!--			ng-if="row.entity.update == null && row.entity.updated.value == null" acl-action="SMVUSPUP">-->
<!--			<span ng-if="row.entity.status=='IN_ACTIVE'">Deactivate</span>-->
<!--			<span ng-if="row.entity.status=='ACTIVE'">Activate</span>-->
<!--		</button>-->
		<button 
			class="btn btn-xs-small blue" 
			ng-click="grid.appScope.updateRow(row.entity)"
			data-ng-if="!row.entity.isUpdated">
			<span>Update</span>
		</button>
		<button 
			class="btn btn-xs-small orange" 
			ng-click="grid.appScope.cancelRow(row.entity)"
			data-ng-if="row.entity.isUpdated">
			<span>Cancel Update</span>
		</button>
      </div>
</script>
<script
	type="text/ng-template"
	id="statusBatch.html">
      <div class="ui-grid-cell-contents">
		<span  
			class="span badge active"
			ng-if="row.entity.status === 'ACTIVE'">
			ACTIVE
		</span>
		<span 
			class="span badge inactive"
			ng-if="row.entity.status === 'IN_ACTIVE'">
			IN-ACTIVE
		</span>
      </div>
</script>
<div
	id="addSkuPriceMappingModal"
	class="modal modal-mx">
	<form
		id="basicDetail"
		name="basicDetailsForm"
		class="white z-depth-3 scm-form"
		novalidate>
		<div class="modal-content">
			<div class="row">
				<div class="col s12 card-panel teal lighten-2">
					<h5 class="white-text center">Add Vendor to SKU Price Mapping</h5>
				</div>
			</div>
		</div>
		<div class="row">
			<div class="col s12 m12 l3 xl3">
				<label class="black-text">{{searchType.name}} Name</label>
			</div>
			<div class="col s12 m12 l9 xl9">
				<input
					type="text"
					data-ng-model="sourceValue.name"
					disabled></input>
			</div>
		</div>
		<div class="row">
			<div class="col s12 m12 l3 xl3">
				<label class="black-text">Delivery Location</label>
			</div>
			<div class="col s12 m12 l9 xl9">
				<input
					type="text"
					data-ng-model="deliveryLocation.name"
					disabled></input>
			</div>
		</div>
		<div
			class="row"
			data-ng-if="!isSKUValue()">
			<div class="col s12 m12 l3 xl3">
				<label class="black-text">SKU</label>
			</div>
			<div class="col s12 m12 l9 xl9">
				</input> <select
					ui-select2
					style="width: 100%;"
					id="skuValueSelectId"
					data-ng-model="selectSKUValue"
					data-ng-options="sku as sku.name for sku in skuDataList | orderBy : 'name' track by sku.id"
					data-ng-change="getSkuPackaging(selectSKUValue)"
					required></select>
				<p
					data-ng-show="basicDetail.selectSKUValue.$error.required"
					class="errorMessage">Please select SKU.</p>
			</div>
		</div>
		<div
			class="row"
			data-ng-if="isSKUValue()">
			<div class="col s12 m12 l3 xl3">
				<label class="black-text">Vendor</label>
			</div>
			<div class="col s12 m12 l9 xl9">
				<select
					ui-select2
					style="width: 100%;"
					id="vendorValueSelectId"
					data-ng-model="selectVendorValue"
					data-ng-options="vendor as (vendor.name + ' [' + vendor.category + ']') for vendor in vendorDataList | orderBy : 'name' track by vendor.id"
					data-ng-change="getVendorDispatchLocations(selectVendorValue)"
					required></select>
				<p
					data-ng-show="basicDetail.selectVendorValue.$error.required"
					class="errorMessage">Please select vendor.</p>
			</div>
		</div>
		<div class="row">
			<div class="col s12 m12 l3 xl3">
				<label class="black-text">Dispatch Location</label>
			</div>
			<div class="col s12 m12 l9 xl9">
				<select
					ui-select2
					style="width: 100%;"
					id="vendorDispatchLocationSelectId"
					name="selectVendorDispatchLocationValue"
					data-ng-model="selectDispatchLocation"
					data-ng-options="dloc as dloc.name + ', ' + dloc.state.name for dloc in vendorDispatchLocations | orderBy : 'name' track by dloc.id"
					required></select>
			</div>
		</div>
		<div class="row">
			<div class="col s12 m12 l3 xl3">
				<label class="black-text">Packaging</label>
			</div>
			<div class="col s12 m12 l9 xl9">
				<select
					ui-select2
					style="width: 100%;"
					id="skuPackSelector"
					data-ng-model="selectSkuPackaging"
					data-ng-options="pkg as pkg.packagingName for pkg in skuPkgs track by pkg.packagingId"
					required></select>
			</div>
		</div>
		<div class="row">
			<div class="col s12 m12 l3 xl3">
				<label class="black-text">Packaging Negotiated Price</label>
			</div>
			<div class="col s12 m12 l9 xl9">
				<input
					type="number"
					min="0.000001"
					data-ng-model="negotiatedPriceValue"
					required />
			</div>
		</div>
		<div class="row">
			<div class="col s12 m12 l3 xl3">
				<label class="black-text">Lead Time</label>
			</div>
			<div class="col s12 m12 l9 xl9">
				<input
						type="number"
						data-ng-model="leadTime"
						required />
			</div>
		</div>
		<div class="modal-footer">
			<div class="row">
				<div class="col s12">
					<button
						class="modal-action waves-effect waves-green btn right"
						data-ng-class="{'disabled':!basicDetailsForm.$valid}"
						style="margin-right: 10px"
						data-ng-click="submitPriceData()"
						data-ng-disabled="!basicDetailsForm.$valid">Submit</button>
					<button
						class="modal-action modal-close waves-effect waves-green btn right"
						style="margin-right: 10px"
						data-ng-click="clearAddPriceData()">Cancel</button>
				</div>
			</div>
		</div>
	</form>
</div>

<script
		type="text/ng-template"
		id="skuIdTemplate.html">
	<div class="ui-grid-cell-contents">
		<a style="cursor: pointer" data-ng-click="grid.appScope.showPreview($event, row.entity.sku.id,'SKU')">{{row.entity.sku.name}}</a>
	</div>
</script>

<script type="text/ng-template" id="vendorToSkuPriceUpdateModal.html" class="modal-large" >
	<div  id="previewExcessQuantity" data-ng-init="initSKUPriceUpdationModal()" >
		<div class="modal-content" style="overflow-x: auto; max-height: 350px;">
			<div class="row">
				<h5><b>Vendor Price Update</b></h5>
			</div>
			<div class="row" style="width:98%;">
				<table class="bordered striped standardView">
					<thead>
					<tr>
						<th class="center-align">Vendor</th>
						<th class="center-align">SKU</th>
						<th class="center-align">Dispatch Location</th>
						<th class="center-align">Delivery Location</th>
						<th class="center-align">Packaging</th>
						<th class="center-align">Unit Of Measure</th>
						<th class="center-align">Current Price</th>
						<th class="center-align">Updated Price</th>
					</tr>
					</thead>
					<tbody>
					<tr data-ng-repeat="id in data">
						<td class="center-align">{{id.detail.vendor.name}}</td>
						<td class="center-align">{{id.detail.sku.name}}</td>
						<td class="center-align">{{id.detail.dispatch.code}}</td>
						<td class="center-align">{{id.detail.delivery.code}}</td>
						<td class="center-align">{{id.detail.pkg.name}}</td>
						<td class="center-align">{{id.detail.pkg.uom}}</td>
						<td class="center-align">{{id.detail.current.value}}</td>
						<td class="center-align">
							<div>
								<input type="number" data-ng-model="id.detail.updated.value" min="0.000001"
									   data-ng-change="updateData(id,id.detail.updated.value)">
							</div>
						</td>
					</tr>
					</tbody>
				</table>
				<div class="bordered striped TableMobileView">
				</div>
			</div>
		</div>
		<div class="modal-footer right">
			<button class="waves-effect waves-green btn-flat" data-ng-click="close()">Cancel</button>
			<button class="waves-effect waves-green btn" data-ng-click="submit()">Submit</button>
		</div>
	</div>
</script>