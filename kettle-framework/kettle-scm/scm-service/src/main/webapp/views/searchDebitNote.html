<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="col s12">
        <div class="row">
            <div class="col s12">
                <h5 class="left">Search Debit Note</h5>
            </div>
        </div>
    </div>

    <div data-ng-show="selectView==true">

        <div class="row">
            <div class="col s2">
                <label>Start date</label>
                <input input-date type="text" data-ng-model="startDate" container="" format="yyyy-mm-dd"/>
            </div>
            <div class="col s2">
                <label>End date</label>
                <input input-date type="text" data-ng-model="endDate" container="" format="yyyy-mm-dd"/>
            </div>
            <div class="col s2">
                <label>Select Debit Note Status</label>
                <select ui-select2 id="selectedStatus"
                          name="selectedStatus" data-ng-model="selectedStatus"
                          data-ng-change="setSelectedStatus(selectedStatus)"
                          data-ng-options="status as status for status in statusList"></select>
            </div>
            <div class="col s3">
					<label>Select Company</label> <select ui-select2 id="companyList"
						name="companyList" data-ng-model="selectedCompany"
						data-ng-change="getCompanyMappedUnits()"
						data-ng-options="company as company.name for company in companyList track by company.id"></select>
				</div>
            <div class="col s3">
                <label>Select Unit</label>
                <select ui-select2 id="unitListSearchDebitNote" name="unitList" data-ng-model="selectedUnit"
                        data-ng-change="selectUnit(selectedUnit)"
                        data-ng-options="item as item.name for item in unitList track by item.id"></select>
            </div>
           
        </div>
        <div class="row">
        	 <div class="col s3">
                <label>Select Vendor</label>
                <select ui-select2 id="vendorList" name="vendorList" data-ng-model="selectedVendor"
                        data-ng-options="item as item.name for item in vendorList track by item.id"></select>
            </div>
            <div class="col s3">
                <label>PR number</label>
                <input type="text" data-ng-model="prId"/>
            </div>
            <div class="col s3">
                <label>Invoice number</label>
                <input type="text" data-ng-model="invoiceNumber"/>
            </div>
            <div class="col s3">
                <label>Debit Note Number</label>
                <input type="text" data-ng-model="dnId"/>
            </div>
        </div>
	<div class="row">
		<div class="col s3">
                <input type="button" class="btn" value="Find" data-ng-click="findDebitNotes()"/>
        </div>
	</div>

        <div class="row">
            <div class="col s12">
                <table class="table bordered striped" style="border: #ccc 1px solid;" data-ng-if="dns.length>0">
                    <thead>
                    <tr>
                        <th>DN Id</th>
                        <th>PR Id</th>
                        <th>Debit Note Status</th>
                        <th>Invoice Number</th>
                        <th>Busy Reference</th>
                        <th>Amount</th>
                        <th>Tax</th>
                        <th>Total</th>
                        <th>Advance Amount</th>
                        <th>Advance Payment Id</th>
                        <th>Creation time</th>
                        <th>Created By</th>
                        <th>Credit received</th>
                        <th>Receive time</th>
                        <th>Received by</th>
                        <th>Action</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr data-ng-repeat="dn in dns track by dn.debitNoteId">
                        <td>{{dn.debitNoteId}}</td>
                        <td>{{dn.paymentRequestId}}</td>
                        <td>{{dn.debitNoteStatus}}</td>
                        <td>{{dn.invoiceNumber}}</td>
                        <td>{{dn.busyReferenceNumber}}</td>
                        <td>{{dn.amount}}</td>
                        <td>{{dn.totalTaxes}}</td>
                        <td>{{dn.totalAmount}}</td>
                        <td data-ng-if="dn.advanceAmount != null">{{dn.advanceAmount}}</td>
                        <td data-ng-if="dn.advanceAmount == null">-</td>
                        <td data-ng-if="dn.advancePaymentId != null">{{dn.advancePaymentId}}</td>
                        <td data-ng-if="dn.advancePaymentId == null">-</td>
                        <td>{{dn.generationTime | date:'dd-MM-yyyy hh:mm:ss a'}}</td>
                        <td>{{dn.generatedBy.name}}</td>
                        <td>{{dn.creditNoteReceived==true?"YES":"NO"}}</td>
                        <td>{{dn.creditNoteReceivingTime | date:'dd-MM-yyyy hh:mm:ss a'}}</td>
                        <td>{{dn.lastUpdatedBy.name}}</td>
                        <td>
                            <input type="button" value="Settle" data-ng-show="dn.creditNoteReceived==false && dn.debitNoteStatus == 'CREATED'"
                                   acl-action="DNSTL" class="btn btn-small" data-ng-click="settleDebitNote(dn)" />
                        </td>
                    </tr>
                    </tbody>
                </table>
                <div data-ng-if="showNoDN" style="padding: 10px; margin: 10px; border-radius: 4px; background: #a0e1e4;">No debit notes found.</div>
            </div>
        </div>
    </div>

</div>
