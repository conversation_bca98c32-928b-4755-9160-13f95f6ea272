<div class="row white z-depth-3 " data-ng-init="init()">
    <div class="col s12">
            <div class="col s6 pull-left">
                <h3 class="information">Holiday Calendar</h3>
            </div>
            <div class="col s6 pull-right">
                <button class="btn btn-primary pull-right" data-toggle="modal" id="addCompanyIdDiv" style="margin-top: 20px"
                        ng-click="addHoliday()"><i class="fa fa-plus fw"></i> Add Holiday</button>
            </div>
    </div>

    <div class="row">
        <div class="col s3">
            <label>Select Holiday Type:</label>
            <select data-ng-model="selectedAction" data-ng-change="setSelectedAction(selectedAction)"
                    data-ng-options="holiday for holiday in holidayTypes">
                <option value=""></option>
            </select>
        </div>
        <div class="col s3">
            <label>Select Year:</label>
            <input type="text" input-date format="yyyy" data-ng-model="selectedYear" data-ng-change="setSelectedYear(selectedYear)" placeholder="Select year"/>
        </div>
        <div class="col s1">
            <button class="btn btn-primary center" style="background-color: red;margin-top: 20px;" data-ng-click="clearSearch()">Reset</button>
        </div>
        <div class="col s4">
            <button class="btn btn-primary center" style="margin-top: 20px;margin-left: 70px;" data-ng-click="listOfHolidays()">Show List Of Holidays</button>
        </div>
    </div>

</div>

<div class="row white z-depth-3" id="holidayModal">
    <div class="col s12" style="margin-top: 20px">	<button data-ng-click="mainScreen()" class="btn right">BACK</button></div>
    <form id="Holiday" name="HolidayAddForm" class="white z-depth-3 scm-form" >
    <div class="row">
        <div class="col s6">
            <label>Select Holiday Type:</label>
            <select data-ng-model="selectedHolidayType" data-ng-change="setSelectedHolidayType(selectedHolidayType)" data-ng-options="holiday for holiday in holidayTypes" data-placeholder="Select type of holiday">
                <option value=""></option>
            </select>
        </div>
        <div class="col s12 m6 l6">
            <label>Select Date:</label>
            <input type="text" input-date format="mmm dd yyyy" data-ng-model="selectedDate" data-ng-change="setSelectedDate(selectedDate)"/>
        </div>
        <div class="col s12">&nbsp;</div>
        <div class="form-group">
            <button class="btn btn-primary right"  ng-click="submitAddHoliday()">Add Holiday</button>
        </div>
    </div>
    </form>
</div>

<div class="row white z-depth-3" data-ng-if="holidaysList.length > 0">
    <div class="col s12">&nbsp;</div>
    <div class="form-group">
        <label>Search :</label>
        <input type="text" data-ng-model="searchFilter" placeholder="Enter date to search from list ">
    </div>
    <div class="col s12">&nbsp;</div>
    <div class="col s12">
        <table class="table striped" style="border:#ddd 1px solid;margin-bottom: 10px;">
            <thead style="border-bottom: #ccc 2px solid;background-color: #1de9b6;">
                <th align="center">S.NO</th>
                <th align="center">Holiday Type</th>
                <th align="center">Date </th>
                <th align="center">Year</th>
                <th align="center">Status</th>
                <th align="center">Update Status</th>
            </thead>
            <tbody>
                <tr data-ng-repeat="holiday in holidaysList | filter:searchFilter | orderBy:'holidayDate' track by $index">
                    <td>{{$index+1}}</td>
                    <td>{{holiday.holidayType}}</td>
                    <td>{{holiday.holidayDate | date:'dd-MM-yyyy'}}</td>
                    <td>{{holiday.holidayYear}}</td>
                    <td style="font-weight: bold">{{holiday.status}}</td>
                    <td>
                    <button
                            data-ng-class="{'btn btn-small':holiday.status=='IN_ACTIVE', 'btn red btn-small':holiday.status=='ACTIVE'}"
                            data-ng-click="changeHolidayStatus(holiday.holidayDate,holiday.status)">
                        <span ng-if="holiday.status=='IN_ACTIVE'">Activate</span>
                        <span ng-if="holiday.status=='ACTIVE'">Deactivate</span>
                    </button>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>
