<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
  <style>
  .budget-exceeded-card {
      border: 2px red solid;
    padding-top: 15px;
  }
 .popeye-modal-container .popeye-modal {
    position: relative;
    text-align: left;
    vertical-align: middle;
    display: inline-block;
    width: 80%;
    border-radius: 3px;
    border: none;
    z-index: 1300;
    padding: 2em 1.5em 1.5em;
    background: #fff;
    box-shadow: 0 0 10px rgba(0,0,0,.4);
}

.budget-exceeded-label {
    color: #EFE8E8;
    background-color: #D82F2F;
    border-color: #ebccd1;
    border-radius: 8px;
    font-size: 20px;
    padding: 10px  !IMPORTANT;
    margin-bottom:0px;
}
  </style>

<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="col s12">
        <div class="row">
            <div data-ng-if="!assetOrder" class="col s12 m6 l6">
                <h3 data-ng-if="!bulkOrder">Ad-hoc Order</h3>
                <h3 data-ng-if="bulkOrder">Bulk Order</h3>
            </div>
            <div data-ng-if="assetOrder" class="col s12 m6 l6">
                <h3>Asset Order</h3>
            </div>
            <div class="col s12 m6 l6 menuItemList">

            </div>
        </div>
    </div>

    <div class="row">
        <div class="col s12 m6 l6">
            <label>Select Fulfillment Unit:</label>
            <select ui-select2 ng-model="selectedUnit" id="selectedUnit" ng-change="selectCategory(selectedUnit)" data-placeholder="Select Unit">
            	<option value=""></option>
                <option ng-repeat="unit in scmUnitList track by unit.id" value="{{unit}}">{{unit.name}}</option>
            </select>
        </div>
        <div class="col s12 m6 l6">
            <div class="form-element">
                <label for="inputCreated">Fulfillment date</label>
                <input input-date type="text" name="created" id="inputCreated" ng-model="fulfillmentDate"
                       container="" format="yyyy-mm-dd" select-years="1" min="{{minRefOrderFulFillmentDate}}"
                       max="{{maxRefOrderFulFillmentDate}}" data-ng-change="checkForF9Orders()"/>
            </div>
        </div>
    </div>

    <div class="row" >
        <div class="col s12 m6 l6">
            <label>Select Product:</label>
            <select ui-select2 ng-model="selectedProduct" id="selectedProduct" data-placeholder="Enter name of product" data-ng-change="setSelectedProduct(selectedProduct)">
                <option value=""></option>
                <option ng-repeat="product in scmProductDetails" value="{{product}}">{{product.productName}}</option>
            </select>
            <input type="button" class="btn" value="ADD PRODUCT" data-ng-click="addNewRoItem()" />
        </div>
        <div class="col s12 m6 l6">
            <div class="form-element">
                <label>Comment {{assetOrder ? "*" : "(optional)"}}</label>
                <textarea data-ng-model="comment"></textarea>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="row">
            <div class="col s12">
                <ul class="collection" data-ng-if="addedRoItems.length>0">
                    <li class="collection-item list-head">
                        <div class="row" style="margin-bottom: 0px;">
                            <div class="col s1">#</div>
                            <div data-ng-class="{'col s2' : !assetOrder,'col s3':((!isFountain9Unit && !assetOrder) || assetOrder)}">Product Name</div>
                            <div  data-ng-if="isCapex && assetOrder" class="col s2">SKU Name</div>
                            <div data-ng-class="{'col s1' : !assetOrder,'col s2':((!isFountain9Unit && !assetOrder) || assetOrder)}">Requested Qty.</div>
                            <!--<div class="col s2">Requested Absolute Qty.</div>-->
                            <div data-ng-class="{'col s1' : !assetOrder,'col s2':((!isFountain9Unit && !assetOrder) || assetOrder)}">Packaging Qty</div>
                            <div class="col s2">Packaging Name</div>
                            <div class="col s2" data-ng-if="isFountain9Unit && !assetOrder">
                                Reason
                                <br>
                                <select style="color: black;width: 170px;" ng-model="selecetedReason" data-ng-options="reason as reason for reason in reasons"
                                        data-ng-change="changeAllReasons(selecetedReason)"></select>
                            </div>
                            <div class="col s2" data-ng-if="isFountain9Unit && !assetOrder">
                                Comment <span data-ng-if="selecetedReason == 'OTHER'"><b>Min : 3 letters</b></span>
                                <br>
                                <textarea data-ng-model="enterredComment"  data-ng-change="changeAllComments(enterredComment)" style="resize: none;color: black;" data-ng-if="selecetedReason != null"></textarea>
                                <span data-ng-if="enterredComment.length > 0">{{100-enterredComment.length}} Characters Remaining</span>
                                <span style="color: red" data-ng-if="enterredComment.length > 100">{{enterredComment.length - 100}} Characters Exceeded</span>
                            </div>
                            <div data-ng-class="{'col s1' : !assetOrder,'col s2':((!isFountain9Unit && !assetOrder) || assetOrder)}">Remove</div>
                        </div>
                    </li>
                    <li class="collection-item" data-ng-repeat="roi in addedRoItems track by roi.productId">
                        <div class="row" style="margin-bottom: 0;">
                            <div class="col s1">{{$index + 1}}</div>
                            <div data-ng-class="{'col s2' : !assetOrder,'col s3':((!isFountain9Unit && !assetOrder) || assetOrder)}">
                                <a data-ng-click="showPreview($event, roi.productId,'PRODUCT')">{{roi.productName}} ({{roi.unitOfMeasure}})</a>
                            </div>
                            <div class="col s2" data-ng-if="isCapex && assetOrder">
                                <select  id="" ui-select2="{allowClear:true, placeholder: 'Select SKU'}"
                                        ng-options="sku.skuId as sku.skuName for sku in roi.skus"
                                        data-ng-init="currentSku.sku[sku.skuId] = -1"
                                         data-ng-model="currentSku.sku[sku.skuId]" data-ng-change="onSelectChange(currentSku.sku[sku.skuId],roi.productId)"></select>
                            </div>

                            <div data-ng-class="{'col s1' : !assetOrder,'col s2':((!isFountain9Unit && !assetOrder) || assetOrder)}">{{(!isNaN(roi.requestedQuantity)) ?  (roi.requestedQuantity) : undefined}}</div>
                            <div data-ng-class="{'col s1' : !assetOrder,'col s2':((!isFountain9Unit && !assetOrder) || assetOrder)}">
                                <div data-ng-if="!isManualBook[roi.productId]"><input data-ng-model="roi.packagingQuantity" type="number" min="0" data-ng-change="calculateRequestQty(roi)" /></div>
                                <div data-ng-if="isManualBook[roi.productId]"><span data-ng-init="calculateRequestQty(roi)">{{roi.packagingQuantity}}</span></div>
                            </div>
                            <!--<div class="col s2">{{roi.requestedAbsoluteQuantity==null?0:roi.requestedAbsoluteQuantity}}</div>-->
                            <div class="col s2">{{roi.packagingName}}</div>
                            <div class="col s2" data-ng-if="isFountain9Unit && !assetOrder">
                                <select class="input-field" ng-model="roi.reason" style="margin-top: 0px; height: 2rem;" data-ng-options="reason as reason for reason in reasons">
                                </select>
                            </div>
                            <div class="col s2" data-ng-if="isFountain9Unit && !assetOrder">
                                <textarea data-ng-model="roi.comment" style="resize: none"></textarea>
                                <span data-ng-if="roi.comment.length >0 && roi.comment.length <=100">{{100-roi.comment.length}} Characters Remaining</span>
                                <span style="color: red" data-ng-if="roi.comment.length > 100">{{roi.comment.length - 100}} Characters Exceeded</span>
                            </div>
                            <div data-ng-class="{'col s1' : !assetOrder ,'col s2':((!isFountain9Unit && !assetOrder) || assetOrder)}"><button class="btn btn-small" data-ng-click="removeItem($index)">Remove</button></div>
                        </div>
                    </li>
                </ul>
            </div>
        </div>

    <div class="col s12 form-element">
        <input type="button" class="btn" value="SUBMIT" data-ng-click="createRoObject(true)" />
        <input type="button" class="btn btn-warning" value="FORCE SUBMIT" data-ng-click="createRoObject(false)"  acl-action="SOROIBFS"
        style="float: right;background-color: #d83333;" />
    </div>
    </div>
</div>

<script type="text/ng-template" id="budgetExceededModal.html">
<div>
	<div class="row ">
		<div class="col s12 alert budget-exceeded-label  text-center">Request
			Order can not be created because you have excceded budget
			allocated.Details of budget and products are given below :</div>
	</div>
	<div class="row budget-exceeded-card"
		data-ng-repeat="resp in  orderResponse">
		<div class="col s4">
			<label>Budget Amount</label>{{resp.budgetAmount}}
		</div>
		<div class="col s4">
			<label>Current Amount</label>{{resp.currentAmount}}
		</div>
		<div class="col s4">
			<label>Requested Amount</label>{{resp.requestedAmount}}
		</div>
		<div class="row" style ="margin-bottom: 0px !important;">
			<div class="col s12" style="margin-top: 7px;">
				<label>Product name of this category are : </label>
			</div>
			<div class="row" >
				<div class="col s12" >
					<span data-ng-repeat="products in  resp.products">
							<span  style="color:red;"	> {{$index+1}}. {{products}}</span><span data-ng-if="$index < (resp.products.length - 1)">,</span>
					</span>
				</div>
			</div>
		</div>
	</div>
	<div class="row">
		<!--  <button class="btn green"  data-ng-click="rejectedGoodsReceivedDecline()">Confirm</button> -->
		<button class="btn red" style="float: right;" data-ng-click="cancel()">Cancel</button>
	</div>
</div>
</script>

<script type="text/ng-template" id="departmentROModal.html">
    <div class="modal-header">
        <h3 class="modal-title" id="modal-title" style="margin-top: 0px;">Summary</h3>
        <hr>
    </div>
    <div class="modal-body" id="modal-body">
        <div class="row">
            <div class="col s12">
                <div data-ng-if="emptyCheck">
                    <h5>No Budget Found </h5>
                    <br>
                    <div class="row">
                        <button class="btn red pull-right"  data-ng-click="cancel()">Close</button>
                    </div>
                </div>
                <br>
            </div>
            <div class="col s12" data-ng-if="!emptyCheck">
                <table class="table bordered striped" style="border: #ccc 1px solid;">
                    <thead>
                    <tr>
                        <th>Department Name</th>
                        <th>Total Cost</th>
                        <th>Total Tax</th>
                        <th>Total Amount</th>
                        <th>Original Amount</th>
                        <th>Budget Amount</th>
                        <th>Remaining Amount</th>
                        <th>Running Amount</th>
                        <th>Receiving Amount</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr ng-class="{'red': totalAmount > budgetDetails.remainingAmount}">
                        <td style="color: black;">{{budgetDetails.departmentName}}</td>
                        <td style="color: black;">{{totalCost.toFixed(2)}}</td>
                        <td style="color: black;">{{totalTax.toFixed(2)}}</td>
                        <td style="color: black;">{{totalAmount.toFixed(2)}}</td>
                        <td style="color: black;">{{budgetDetails.originalAmount.toFixed(2)}}</td>
                        <td style="color: black;">{{budgetDetails.budgetAmount.toFixed(2)}}</td>
                        <td style="color: black;">{{budgetDetails.remainingAmount.toFixed(2)}}</td>
                        <td style="color: black;">{{budgetDetails.runningAmount.toFixed(2)}}</td>
                        <td style="color: black;">{{budgetDetails.receivingAmount.toFixed(2)}}</td>
                    </tr>
                    </tbody>
                </table>
                <br>
                <div class="row">
                    <button class="btn red"  data-ng-click="cancel()">Cancel</button>
                    <button data-ng-if="totalAmount <= budgetDetails.remainingAmount" class="btn green" style="float: right;" data-ng-click="submit(false)">Settle</button>
                    <button data-ng-if="totalAmount > budgetDetails.remainingAmount && ro.assetOrder == false" class="btn green" style="float: right;" data-ng-click="submit(true)">Settle With Opex</button>

                </div>
            </div>
        </div>
    </div>
</script>

<script type="text/ng-template" id="qtyValidation.html">

    <div class="modal-header red-text" data-ng-init="init()">
        <h3 class="modal-title" id="modal-title" style="margin-top: 0px;">Please Re-Enter Qty For Product : {{roItem.productName}}</h3>
        <hr>
    </div>
    <ul class="collection">
        <li class="collection-item list-head">
            <div class="row">
                <div class="col s4">Product Name</div>
                <div class="col s2">Requested Qty.</div>
                <div class="col s2">Packaging Qty</div>
                <div class="col s2">Packaging Name</div>
            </div>
        </li>

        <li class="collection-item" >
            <div class="row" style="margin-bottom: 0;">
                <div class="col s4">{{roItem.productName}} ({{roItem.unitOfMeasure}})</div>
                <div class="col s2">{{roItem.reValidatedPkgQty*roItem.conversionRatio}}</div>
                <div class="col s2"><input   data-ng-model="roItem.reValidatedPkgQty" data-ng-change="calculateRequestQty(roItem)" type="number" min="0"/>
                </div>
                <div class="col s2">{{roItem.packagingName}}</div>
            </div>
        </li>
    </ul>
    <hr>
    <div class="modal-header red-text" data-ng-init="init()">
        <h5 class="modal-title" id="modal-title" style="margin-top: 10px;">
            * You Are Ordering {{roItem.reValidatedPkgQty*roItem.conversionRatio}}  {{roItem.unitOfMeasure}} Of {{roItem.productName}}
        </h5>
    </div>

    <button class="btn-toolbar  btn-medium right " ng-click="submit()" title="send"
            style="border: whitesmoke;  color: #0f9d58; text-align: center">Send <i  class="material-icons " style="padding-bottom: 0px;">send</i>
    </button>

</script>
