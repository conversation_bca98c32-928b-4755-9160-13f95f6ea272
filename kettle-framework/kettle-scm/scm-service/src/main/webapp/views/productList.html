<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="row" data-ng-init="init()">
    <div class="col s12">
        <div class="row white z-depth-3 custom-listing-li">
            <div class="col s12">
                <h4>Search Product</h4>
                <!-- <button class="btn btn-primary" type="button" ng-click="refreshCache()" style="float: right;margin-top: -52px;">Refresh SCM Cache</button> -->
                <div class="input-field">
                    <select ui-select2="productSelectOptions" ng-model="productId" data-ng-change="selectProduct(productId)" data-placeholder="Enter name of a product">
                        <option ng-repeat="product in products track by product.productId" value="{{product.productId}}">{{product.productName}}</option>
                    </select>
                </div>
            </div>
        </div>
        <div data-ng-if="productDetail.productId != null" class="row product-detail white z-depth-3">
            <div class="col s12">
                <div class="row" style="margin-bottom:0px;">
                    <div class="col s7" style="padding-left:0px;">
                        <h3>{{productDetail.productName}}</h3>
                    </div>
                    <div class="col s5">
                        <a class="waves-effect waves-light btn right"
                           data-ng-click="editProduct(productDetail)" style="margin: 1rem;" acl-action="SPPUPP">EDIT</a>
                        <a class="waves-effect waves-light btn right"
                           data-ng-click="changeStatus(productDetail.productId, 'ARCHIVED')" style="margin: 1rem;" acl-action="SPPUPP">ARCHIVE</a>
                        <a class="waves-effect waves-light btn right"
                           data-ng-click="setImageViaUploadDoc(productDetail.productId)" style="margin: 1rem;" acl-action="SPPUPP">SET IMAGE</a>
                        <div class="input-field right" style="margin-right: 10px;"
                             data-ng-if="productDetail.productStatus == 'IN_ACTIVE' ">
                            <a id="productStatus" data-ng-click="changeStatus(productDetail.productId, 'ACTIVE')"
                               class="waves-effect waves-light btn validate" acl-action="SPPAPC">Activate</a>
                        </div>
                        <div class="input-field right"
                             data-ng-if="productDetail.productStatus == 'ACTIVE' ">
                            <a id="productActiveStatus"
                               class="waves-effect waves-light btn validate"
                               data-ng-click="validateForDeactivation(productDetail.productId, 'IN_ACTIVE')"
                               acl-action="SPPDPE">Deactivate</a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col s12">
                <blockquote class="flow-text" style="word-break: break-all;">{{productDetail.productDescription}}</blockquote>
            </div>
            <div class="col s12">
                <div class="row">
                    <p class="col s4">
                        <strong>Created On: </strong>
                        {{productDetail.creationDate | date:'dd/MM/yyyy'}}
                    </p>

                    <p class="col s4">
                        <strong>Created By: </strong> {{productDetail.createdBy.name}}
                    </p>

                    <p class="col s4"><strong>Product Code: </strong>{{productDetail.productCode}}</p>

                    <p class="col s4"><strong>Tax Category: </strong><a data-ng-click="showTaxDetail(productDetail.taxCode)">{{productDetail.taxCode}}</a></p>

                    <p class="col s4"><strong>Product Category: </strong>{{productDetail.categoryDefinition.name}}</p>

                    <p class="col s4" data-ng-if="productDetail.categoryDefinition.id == 3 "><strong>Profile Id : </strong>{{productDetail.profileId}}</p>

                    <p class="col s4"
                       data-ng-show="productDetail.categoryDefinition.name == 'Fixed Assets'">
                        <strong>Associated Profile: </strong>{{getProfileName(productDetail.profileId)}}
                    </p>

                    <p class="col s4"><strong>Product Sub Category: </strong>{{productDetail.subCategoryDefinition.name}}</p>

                    <p class="col s4"><strong>Stocking Frequency: </strong>{{productDetail.stockKeepingFrequency}}</p>

                    <p class="col s4"><strong>Division : </strong>{{productDetail.divisionName}}</p>
                    <p class="col s4"><strong>Department: </strong>{{productDetail.departmentName}}</p>
                    <p class="col s4"><strong>Classification: </strong>{{productDetail.classificationName}}</p>
                    <p class="col s4"><strong>Sub Classification: </strong>{{productDetail.subClassificationName}}</p>

                    <p class="col s4"><strong>Shelf Life (in days): </strong>{{productDetail.shelfLifeInDays}}</p>

                    <p class="col s4"><strong>Fulfillment Type: </strong>{{productDetail.fulfillmentType}}</p>

                    <p class="col s4"><strong>Variance Type: </strong>{{productDetail.varianceType}}</p>

                    <p class="col s4"><strong>Kitchen/WH Variance Type: </strong>{{productDetail.kitchenVarianceType}}</p>

                    <p class="col s4" data-ng-if="productDetail.fulfillmentType=='DERIVED'">
                        <strong>Default Type: </strong>{{productDetail.defaultFulfillmentType}}
                        <button class="btn btn-xs-small" data-ng-if="productDetail.derivedMappings.length>0"
                                ng-click="viewDerivedMappings(productDetail)">View Mappings</button>
                    </p>

                    <p class="col s4">
                        Status: {{productDetail.productStatus}}
                    </p>

                    <p class="col s4">
                        Unit of Measure: {{productDetail.unitOfMeasure}}
                    </p>

                    <!--<p class="col s4">-->
                        <!--Unit Price: {{productDetail.unitPrice}}-->
                    <!--</p>-->

                    <!--<p class="col s4">-->
                        <!--Negotiated Unit Price: {{productDetail.negotiatedUnitPrice}}-->
                    <!--</p>-->

                    <p class="col s4">
                        Product Description: {{productDetail.productDescription}}
                    </p>

                    <p class="col s4">
                        <i data-ng-if="productDetail.supportsLooseOrdering" class="material-icons left">done</i>
                        <i data-ng-if="!productDetail.supportsLooseOrdering" class="material-icons left">error</i>
                        Supports Loose Ordering
                    </p>

                    <p class="col s4">
                        <i data-ng-if="productDetail.hasCase" class="material-icons left">done</i>
                        <i data-ng-if="!productDetail.hasCase" class="material-icons left">error</i>
                        Contains Case Profile
                    </p>

                    <p class="col s4">
                        <i data-ng-if="productDetail.hasInner" class="material-icons left">done</i>
                        <i data-ng-if="!productDetail.hasInner" class="material-icons left">error</i>
                        Contains Inner Profile
                    </p>

                    <p class="col s4">
                        <i data-ng-if="productDetail.participatesInRecipe" class="material-icons left">done</i>
                        <i data-ng-if="!productDetail.participatesInRecipe" class="material-icons left">error</i>
                        Participates in Recipe
                    </p>

                    <p class="col s4">
                        <i data-ng-if="productDetail.participatesInCafeRecipe" class="material-icons left">done</i>
                        <i data-ng-if="!productDetail.participatesInCafeRecipe" class="material-icons left">error</i>
                        Participates in Cafe Recipe
                    </p>

                    <p class="col s4">
                        <i data-ng-if="productDetail.variantLevelOrdering" class="material-icons left">done</i>
                        <i data-ng-if="!productDetail.variantLevelOrdering" class="material-icons left">error</i>
                        Variant Level Ordering
                    </p>

                    <p class="col s4">
                        <i data-ng-if="productDetail.supportsSpecialOrdering" class="material-icons left">done</i>
                        <i data-ng-if="!productDetail.supportsSpecialOrdering" class="material-icons left">error</i>
                        Specialized Ordering
                    </p>

                    <p class="col s4">
                        <i data-ng-if="productDetail.availableAtCafe" class="material-icons left">done</i>
                        <i data-ng-if="!productDetail.availableAtCafe" class="material-icons left">error</i>
                        Available at Cafe
                    </p>

                    <p class="col s4">
                        <i data-ng-if="productDetail.interCafeTransfer" class="material-icons left">done</i>
                        <i data-ng-if="!productDetail.interCafeTransfer" class="material-icons left">error</i>
                        Inter Cafe Transfer
                    </p>

                    <p class="col s4">
                        <i data-ng-if="productDetail.availableForCafeInventory" class="material-icons left">done</i>
                        <i data-ng-if="!productDetail.availableForCafeInventory" class="material-icons left">error</i>
                        Available for Cafe Inventory
                    </p>

                    <p class="col s4">
                        <i data-ng-if="productDetail.assetOrdering" class="material-icons left">done</i>
                        <i data-ng-if="!productDetail.assetOrdering" class="material-icons left">error</i>
                        Asset Ordering
                    </p>
                    <p class="col s12">
                        <i data-ng-if="productDetail.autoProduction" class="material-icons left">done</i>
                        <i data-ng-if="!productDetail.autoProduction" class="material-icons left">error</i>
                        Auto Production
                    </p>
                    <p class="col s4"><image data-ng-show="{{productDetail.productImage}}" data-ng-src="{{productBaseUrl}}{{productDetail.productImage}}" style="width:100%;" /> </p>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/ng-template" id="viewDerivedMapping.html">
    <div data-ng-init="initMappings()">
        <h5>Derived Mappings To Units</h5>
        <ul class="collection" style="overflow-y:scroll; max-height:250px;">
            <li class="collapsible-header row">
                <div class="col s6 center">Unit</div>
                <div class="col s6 center">Fulfillment Type</div>
            </li>
            <li class="collection-item" data-ng-repeat="mapping in mappings">
                <div class="row margin0">
                    <div class="col s6 center">{{units[mapping.unit].name}}</div>
                    <div class="col s6 center">{{mapping.type}}</div>
                </div>
            </li>
        </ul>
    </div>
</script>

<script type="text/ng-template" id="productImageModal.html">
<div id="productImageModal">
    <div class="modal-content">
        <h4>Set Product Image</h4>
        <p>{{productDetail.productName}}</p>
        <button data-ng-click="takeSnapShot()" class="btn modal-action modal-close">Take Snapshot</button>
        <button data-ng-click="uploadFile()" class="btn modal-action modal-close">Upload File</button>
    </div>
</div>
</script>
