<style>
    #acknowledge-variance li.tab {
        color: #fff !important;
        text-align: center;
        padding: 0 !important;
    }

    #acknowledge-variance li.tab a {
        background-color: #e84a17;
        color: #fff;
        display: block;
        padding: 0.75rem;
        text-transform: uppercase;
        border: 1px solid #e84a17;
        border-left: 1px solid #fff;
        border-right: 1px solid #fff;
        margin-top: 5px;
    }

    #acknowledge-variance li.tab a.active {
        color: #e84a17;
        font-weight: 700;
        background-color: #fff;
        border: 1px solid #e84a17;
    }

    .bold {
        font-weight: 700;
    }

    #acknowledge-variance #basic .col {
        padding: 0 0.75rem;
    }

    #acknowledge-variance .locationDiv {
        background-color: #f6f6f6;
        padding: 1rem;
    }

    .uppercase-text {
        font-size: 12px;
        word-break: break-all;
        text-transform: uppercase;
    }
</style>
<div class="row" data-ng-init="init()">
    <div class="col s12">
        <div class="row white z-depth-3 custom-listing-li">
            <div class="col s12" style="padding-bottom: 10px">
                <h4>Acknowledge Variance </h4>
            </div>
            <div class="col s12" style="padding-bottom: 10px">
                <div class="col s3">
                    <label>Type</label>
                    <select ui-select2 id="selectedVarianceType" ng-model="selectedVarianceType"
                            ng-change="selectType(selectedVarianceType)" data-placeholder="Select Type">
                        <option value=""></option>
                        <option data-ng-repeat="varianceType in acknowledgeVarianceTypes" value="{{varianceType}}">
                            {{varianceType}}
                        </option>
                    </select>
                </div>
                <div class="col s3">
                    <label>Units</label>
                    <select ui-select2 ng-model="selectedUnit" id="selectedUnit" ng-change="selectUnit(selectedUnit)"
                            data-placeholder="Select Unit">
                        <option value=""></option>
                        <option ng-repeat="unit in scmUnitList track by unit.id" value="{{unit}}">{{unit.name}}</option>
                    </select>
                </div>
                <div class="col s3">
                    <label>Is Acknowledged</label>
                    <select ui-select2 id="isAcknowledged" ng-model="isAcknowledged"
                            ng-change="selectIsAcknowledged(isAcknowledged)" data-placeholder="">
                        <option value=""></option>
                        <option data-ng-repeat="isAcknowledged in isAcknowledgedTypes" value="{{isAcknowledged}}">
                            {{isAcknowledged}}
                        </option>
                    </select>
                </div>
            </div>
            <div class="col s12" style="padding-bottom: 10px">
                <div class="col s4">
                    <input type="button" class="btn" value="FIND" data-ng-click="getAcknowledgeVarianceList()"
                           style="margin-top: 20px;"/>
                </div>
            </div>
        </div>

    </div>
    <div class="col s12">
        <ul class="collection striped " data-ng-show="acknowledgeVarianceList.length>0">
            <li class="collection-item list-head">
                <div class="row">
                    <div class="col s1">Id</div>
                    <div class="col s1">Business Date</div>
                    <div class="col s2">Unit Id</div>
                    <div class="col s2">Is Acknowledged</div>
                    <div class="col s2">Variance Cost</div>
                    <div class="col s2">Variance Percentage</div>
                    <div class="col s2">Actions</div>
                </div>
            </li>
            <li class="collection-item clickable" data-ng-repeat="vl in acknowledgeVarianceList">
                <div class="row" style="margin-bottom: 0px;">
                    <div class="col s1">{{vl.id}}</div>
                    <div class="col s1">{{vl.businessDate | date:'yyyy-MM-dd'}}</div>
                    <div class="col s2">{{vl.unitId}}</div>
                    <div class="col s2">{{vl.acknowledged}}</div>
                    <div class="col s2">{{vl.varianceCost}}</div>
                    <div class="col s2">{{vl.variancePercentage}}</div>
                    <div class="col s2">
                        <input type="button" class="btn btn-medium vBtn margin-top-10 padding-bottom-10"
                               data-ng-if="vl.acknowledged=='N'"
                               value="ACKNOWLEDGE" data-ng-click="acknowledgeVariance(vl.id,vl.unitId,vl.businessDate,vl.frequency,vl.scmDayCloseEventId,vl.acknowledgementType,'ACKNOWLEDGE',vl.comment)">
                        <input type="button" class="btn btn-medium vBtn margin-top-10 padding-bottom-10"
                               data-ng-if="vl.acknowledged=='Y'"
                               value="VIEW" data-ng-click="acknowledgeVariance(vl.id,vl.unitId,vl.businessDate,vl.frequency,vl.scmDayCloseEventId,vl.acknowledgementType,'VIEW',vl.comment)">
                    </div>
                </div>
            </li>
        </ul>
    </div>

</div>

<script type="text/ng-template" id="varianceDetailsModal.html">

    <div class="modal-header " data-ng-init="init()">
        <h4 class="modal-title"  style="margin-top: 0px;">{{unitName}} && BusinessDate : {{selectedDay | date:'yyyy-MM-dd'}}</h4>
        <hr>
    </div>
    <div id="acknowledge-variance" class="row">
        <div class="col s12">
            <ul tabs reload="allTabContentLoaded">
                <div class="col s3" data-ng-repeat="day in days ">
                    <li class="tab col s12" data-ng-click="setSelectedDay(day)"><a>{{day | date:'yyyy-MM-dd' }}</a></li>
                </div>
            </ul>
        </div>
    </div>
    <ul class="collection">
        <li class="collection-item list-head">
            <div class="row">
                <div class="col s3">Name</div>
                <div class="col s1">UOM</div>
                <div class="col s1">Opening Stock</div>
                <div class="col s1">Closing Stock</div>
                <div class="col s1">Wastage</div>
                <div class="col s1">Consumption</div>
                <div class="col s1">Transferred</div>
                <div class="col s1">Received</div>
                <div class="col s1">Variance </div>
                <div class="col s1">Variance Cost</div>
            </div>
        </li>

        <li class="collection-item" data-ng-repeat="item in selectedVarianceDetails " >
            <div class="row" style="margin-bottom: 0;">
                <div class="col s3">{{item.name}}</div>
                <div class="col s1">{{item.uom == null ? "NA":item.uom}}</div>
                <div class="col s1">{{item.openingStock == null ? "NA":item.openingStock}}</div>
                <div class="col s1">{{item.closingStock == null ? "NA":item.closingStock}}</div>
                <div class="col s1">{{item.wasted == null ? "NA":item.wasted}}</div>
                <div class="col s1">{{item.consumption == null ? "NA":item.consumption}}</div>
                <div class="col s1">{{item.transferred == null ? "NA" :item.transferred }}</div>
                <div class="col s1">{{item.received ==null ? "NA" : item.received}}</div>
                <div class="col s1">{{item.variance == null ? "NA" : item.variance}}</div>
                <div class="col s1">{{item.varianceCost == null ?"NA" : item.varianceCost}}</div>
            </div>
        </li>
    </ul>
    <div class="col s12" style="margin-top:20px;">
        <label>Comment :</label>
        <textarea data-ng-if="type=='ACKNOWLEDGE'" data-ng-change="setComment(comment)" data-ng-model="comment" ></textarea>
        <text  data-ng-if="type=='VIEW'">{{comment==null?'':comment}}</text>
    </div>
    <hr>

    <div class="modal-footer">
        <button class="btn right "  data-ng-if="(selectedDay==lastDay || lastDay==null) && type=='ACKNOWLEDGE'" data-ng-click="acknowledgeVarianceDetails()">ACKNOWLEDGE</button>
        <button class="btn right "  data-ng-if="(selectedDay!=lastDay)" data-ng-click="nextDate()">NEXT</button>
        <button class="btn red left" style="margin-right: 20px;" data-ng-click="closeModal()">Close</button>
    </div>

</script>
