<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style>
    .my-modal-popup{
        width:600px !important;
    }
    td,th{
        padding:10px 5px !important;
    }
    .select2.select2-container{
        width:100% !important;
    }
    #images{
        text-align: center;
    }
    img#scanned {
        height: 400px; /** Sets the display size */
        margin-right: 12px;
        margin-top: 20px;
    }
    .prBlocked{
        background: red;
        padding: 5px;
    }/*
    .debitExceeded{
        background: orange;
        padding: 5px;
    }*/
    .modal1 {
    display: none; /* Hidden by default */
    position: fixed; /* Stay in place */
    z-index: 999; /* Sit on top */
    left: 0;
    top: 0;
    width: 100%; /* Full width */
    height: 100vh; /* Full height */
    overflow: auto; /* Enable scroll if needed */
    background-color: rgb(0,0,0); /* Fallback color */
    background-color: rgba(0,0,0,0.4); /* Black w/ opacity */
}

/* modal1 Content/Box */
.modal1-content {
    background-color: #fefefe;
    margin: 15% auto; /* 15% from the top and centered */
    padding: 20px;
    border: 1px solid #888;
    width: 50%; /* Could be more or less, depending on screen size */
}
.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
}

.close:hover,
.close:focus {
    color: black;
    text-decoration: none;
    cursor: pointer;
}

    .popeye-modal-container .popeye-modal {
        width: 900px;
    }
</style>

<div class="searchingCard row white z-depth-3 custom-listing-li" data-ng-init="init()">
    <div class="col s12">
        <div class="row">
            <div class="col s12">
                <h5 class="left">Search Payment Request</h5>
            </div>
        </div>
    </div>

    <div data-ng-show="selectView==true">
        <div class="row">
            <div class="col s12">
                <label>Select Request Type</label>
                <select data-ng-model="prRequestType"
                        data-ng-change="setRequestType(prRequestType)"
                        data-ng-options="item as item.name for item in prRequestTypes"></select>
            </div>
        </div>

        <div data-ng-show="prRequestType.shortCode=='GR' || prRequestType.shortCode=='MILK' || prRequestType.shortCode=='ADP'">
            <div class="row">
                <div class="col s2" style="margin-top:0px">
                    <label>Start date</label>
                    <input input-date type="text" data-ng-model="startDate" container="" format="yyyy-mm-dd"/>
                </div>
                <div class="col s2" style="margin-top:0px">
                    <label>End date</label>
                    <input input-date type="text" data-ng-model="endDate" container="" format="yyyy-mm-dd"/>
                </div>
                <div class="col s3">
                    <label>Select Vendor</label>
                    <select id="vendorList" ui-select2="{allowClear:true, placeholder: 'Select Vendor'}" name="vendorList" data-ng-model="selectedVendor"
                            data-ng-options="item as item.name for item in vendorList track by item.id"></select>
                </div>
                <div class="col s3" data-ng-if="prRequestType.shortCode != 'ADP'">
                    <label>Select Company</label>
                    <select ui-select2 id="companies" name="companies" data-ng-model="selectedCompany"
                            data-ng-options="item as item.name for item in companyMap track by item.id"></select>
                </div>
                <div class="col s2" style="margin-top:0px" data-ng-if="prRequestType.shortCode != 'ADP'">
                    <label>Select Unit</label>
                    <select ui-select2 id="unitList" name="unitList" data-ng-model="selectedUnit"
                            data-ng-change="selectUnit(selectedUnit)"
                            data-ng-options="item as item.name for item in unitList track by item.id"></select>
                </div>
            </div>
            <div class="row" style="margin-bottom: 0px;">

                <div class="col s2">
                    <label>Select Status</label>
                    <select ui-select2="{allowClear:true, placeholder: 'Select Status'}" id="prStatusList" name="prStatusList" data-ng-model="selectedPRStatus"
                            data-ng-options="status as status.name for status in prStatusList track by status.id"></select>
                </div>
                <div class="col s2">
                    <label>PR number</label>
                    <input type="text" data-ng-model="prId"/>
                </div>
                <div class="col s2" data-ng-if="prRequestType.shortCode != 'ADP'">
                    <label>Invoice number</label>
                    <input type="text" data-ng-model="invoiceNumber"/>
                </div>
                <div class="col s2" data-ng-if="prRequestType.shortCode != 'ADP'">
                    <label>Payment date</label>
                    <input input-date type="text" data-ng-model="paymentDate" container="" format="yyyy-mm-dd"/>
                </div>
                <div class="col s2" data-ng-if="prRequestType.shortCode != 'ADP'">
                    <label>GR Id</label>
                    <input  type="number" data-ng-model="grId" />
                </div>
            </div>

            <div class="row">
                <div class="col s2">
                    <input type="button" class="btn" value="Find" data-ng-click="findPrs()"/>
                </div>
            </div>
        </div>


        <div data-ng-show="prRequestType.shortCode=='SR'">
            <div class="row">
                <div class="col s2" style="margin-top:0px">
                    <label>Start date</label>
                    <input input-date type="text" data-ng-model="startDate" container="" format="yyyy-mm-dd"/>
                </div>
                <div class="col s2" style="margin-top:0px">
                    <label>End date</label>
                    <input input-date type="text" data-ng-model="endDate" container="" format="yyyy-mm-dd"/>
                </div>
                <div class="col s3">
                    <label>Select Vendor</label>
                    <select id="serviceVendors"  ui-select2="{allowClear:true, placeholder: 'Select Vendor'}" name="serviceVendorList" data-ng-model="selectedServiceVendor"
                            data-ng-options="item as item.name for item in serviceVendors track by item.id"></select>
                </div>
                <div class="col s3">
                    <label>Select Company</label>
                    <select ui-select2 id="companies" name="companies" data-ng-model="selectedCompany"
                            data-ng-options="item as item.name for item in companyMap track by item.id"></select>
                </div>
                <div class="col s2" style="margin-top:0px">
                    <label>Select Unit</label>
                    <select ui-select2 id="unitList" name="unitList" data-ng-model="selectedUnit"
                            data-ng-change="selectUnit(selectedUnit)"
                            data-ng-options="item as item.name for item in unitList track by item.id"></select>
                </div>

            </div>
            <div class="row" style="margin-bottom: 0px;">
                <div class="col s2">
                    <label>Select Status</label>
                    <select ui-select2="{allowClear:true, placeholder: 'Select Status'}" id="prForSrStatusList" name="prForSrStatusList" data-ng-model="selectedPRStatus"
                            data-ng-options="status as status.name for status in prStatusList track by status.id"></select>
                </div>
                <div class="col s2">
                    <label>PR number</label>
                    <input type="text" data-ng-model="prId"/>
                </div>
                <div class="col s2">
                    <label>Invoice number</label>
                    <input type="text" data-ng-model="invoiceNumber"/>
                </div>
                <div class="col s2">
                    <label>Payment date</label>
                    <input input-date type="text" data-ng-model="paymentDate" container="" format="yyyy-mm-dd"/>
                </div>
                <div class="col s2">
                    <label>SR Id</label>
                    <input  type="number" data-ng-model="srId" />
                </div>
            </div>
            <div class="row">
                <div class="col s2">
                    <input type="button" class="btn" value="Find" data-ng-click="findPrs()"/>
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col s12">
                <p data-ng-if="prs.length>0">
                    <span style="padding: 5px; background: red; display: inline-block;"></span> Blocked &emsp;
                    <span style="padding: 5px; background: orange; display: inline-block;"></span> Debit limit exceeded
                </p>
                <div class="row">
                    <div class="col s3" style="margin-bottom: 20px">
                        <label>Search</label>
                        <input type="text" id="search" data-ng-model="search" data-ng-change="filter()" />
                    </div>
                    <!--<div class="col s3" style="text-align: right;">
                        <input type="checkbox" id="showDebitExceeded" data-ng-model="debitFilter" data-ng-checked="debitFilter==true" data-ng-change="applySpecialFilters()" />
                        <label for="showDebitExceeded">Show debit exceeded</label>
                    </div>-->
                    <div class="checkboxRow">
                    <div class="col s3" style="text-align: right;">
                        <input type="checkbox" id="showBlocked" data-ng-model="blockFilter" data-ng-checked="blockFilter==true" data-ng-change="applySpecialFilters()" />
                        <label for="showBlocked">Show blocked</label>
                    </div>
                    <div class="col s3" style="text-align: right;">
                        <input type="checkbox" id="showValid" data-ng-model="validFilter" data-ng-checked="validFilter==true" data-ng-change="applySpecialFilters()" />
                        <label for="showValid">Show valid</label>
                    </div>
                    </div>
                </div>
                <div class="respTable standardView">
                <table class="row table bordered striped" style="border:#ccc 1px solid;margin-bottom: 10px;" data-ng-if="prs.length>0">
                    <thead>
                    <tr>
                        <th>
                            <span>
                                <input id="allPrs" type="checkbox" data-ng-change="selectAllPaymentRequests()"
                                       data-ng-model="selectAllPrs" data-ng-checked="selectAllPrs==true" />
                                <label for="allPrs">Select All</label>
                            </span>
                        </th>
                        <th>PR Id</th>
                        <th>Company Name</th>
                        <th>Invoice #</th>
                        <th>Creation Date</th>
                        <th>Vendor</th>
                        <th>Credit Cycle</th>
                        <th>Ledger Balance</th>
                        <th>Unit</th>
                        <th>Proposed Amount</th>
                        <th>Paid Amount</th>
                        <th title="Payment Date calculated after removing holidays,non-working saturdays and sundays">Payment Date</th>
                        <th title="Actual Vendor Payment Date">Vendor Payment Date</th>
                        <th>Status</th>
                        <th>Actions</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr data-ng-repeat="pr in filtered = (filteredPrs | filter:search | orderBy : 'vendorId.name') track by $index">
                        <td>
                            <span>
                                <input id="pr-{{pr.paymentRequestId}}" type="checkbox" data-ng-model="pr.selected"/>
                                <label for="pr-{{pr.paymentRequestId}}"></label>
                            </span>
                        </td>
                        <td>
                            <span data-ng-class="{'prBlocked':pr.blocked==true}">{{pr.paymentRequestId}}</span>
                        </td>
                        <td>{{companyMap[pr.companyId].name}}</td>
                        <td>{{pr.invoiceNumber}}</td>
                        <td>{{pr.creationTime | date:'dd-MM-yyyy hh:mm:ss a'}}</td>
                        <td>{{pr.vendorId.name}}</td>
                        <td>{{pr.vendorCreditPeriod}}</td>
                        <td>{{pr.vendorDebitBalance}}</td>
                        <td data-ng-if = "pr.businessCostDetailData == null">-</td>
                        <td ng-if="pr.businessCostDetailData !=null && !isMulitpleUnit(pr.businessCostDetailData)">{{pr.businessCostDetailData}}</td>
                        <td ng-if="pr.businessCostDetailData !=null && isMulitpleUnit(pr.businessCostDetailData)">
                            <div class="col-xs-1" style="align:right" ng-click="showBccData(pr.businessCostDetailData)">
                                <p style="color: #00b0ff; text-decoration: underline">MULTIPLE[{{pr.businessCostDetailData.split(",").length}}]</p>
                            </div>
                        </td>
<!--                        <td data-ng-if="pr.businessCostDetailData.length>1" <div ng-click="viewMultipleBcc()" data-target="#showBccModal" data-toggle="modal"><a>MULTIPLE[{{pr.businessCostDetailData.length}}]</a></td>-->
                        <td>{{pr.proposedAmount}}</td>
                        <td>{{pr.paidAmount}}</td>
                        <!--<td>{{(pr.paymentCycle != null? pr.paymentCycle.paymentDate:pr.paymentDate) | date:'dd-MM-yyyy'}}</td>-->
                        <td>{{pr.paymentDate | date:'dd-MM-yyyy'}}</td>
                        <td>{{(pr.paymentCycle != null? pr.paymentCycle.paymentDate:pr.vendorPaymentDate) | date:'dd-MM-yyyy'}}</td>
                        <td>{{pr.currentStatus}}</td>
                        <td>
                            <input class="btn btn-xs-small" style="margin-bottom: 5px;" type="button" value="View"
                                   data-ng-click="viewPaymentRequest(pr,'VIEW')">
                           <input class="btn btn-xs-small" style="margin-bottom: 5px;" type="button" value="Update Invoice"
                                   data-ng-if="pr.currentStatus=='INITIATED'"
                                   acl-action="PRUPI" data-ng-click="viewPaymentRequest(pr,'UPDATE_INVOICE')">
                            <input class="btn btn-xs-small" style="margin-bottom: 5px;" type="button" value="HOD Approval"
                                   data-ng-if="pr.currentStatus=='PENDING_HOD_APPROVAL'"
                                   acl-action="HODGGRP" data-ng-click="viewPaymentRequest(pr,'PENDING_HOD_APPROVAL')">
                            <input class="btn btn-xs-small" style="margin-bottom: 5px;" type="button" value="Cancel"
                                   data-ng-if="pr.currentStatus=='CREATED' || pr.currentStatus=='INITIATED'"
                                   acl-action="PRCNL" data-ng-click="changeStatus(pr,'CANCELLED')">
                            <input class="btn btn-xs-small" style="margin-bottom: 5px;" type="button" value="Resolve Query"
                                   data-ng-if="pr.currentStatus=='QUERIED'"
                                   acl-action="PRCNL" data-ng-click="viewPaymentRequest(pr,'QUERIED')">
                       <!--       <input class="btn btn-xs-small" style="margin-bottom: 5px;" type="button"
                                   value="Acknowledge" data-ng-if="pr.currentStatus=='CREATED'"
                                   acl-action="PRACK" data-ng-click="changeStatus(pr,'ACKNOWLEDGED')">
                            <input class="btn btn-xs-small" style="margin-bottom: 5px;" type="button"
                                   value="Approve" data-ng-if="pr.currentStatus=='ACKNOWLEDGED'"
                                   acl-action="PRAPR" data-ng-click="viewPaymentRequest(pr,'APPROVE')">
                            <input type="button" class="btn btn-xs-small" value="Settle" data-ng-if="pr.currentStatus=='SENT_FOR_PAYMENT' && pr.blocked==false && pr.debitExceeded==false"
                                   acl-action="PRSTL" data-target="paymentSettleModal" modal data-ng-click="resetPaymentDetail(pr)" />
                            <input type="button" class="btn btn-xs-small" value="Pay Adhoc" data-ng-if="pr.currentStatus=='APPROVED' && pr.blocked==false && pr.debitExceeded==false"
                                   acl-action="PRADP" data-target="reasonModal" modal data-ng-click="setAction(pr,'ADHOC')" /> -->
                        </td>
                    </tr>
                    </tbody>
                </table>
                </div>
                <!-- Business Cost Centre Modal -->
                <div
                        class = "modal1"
                        id = "showBccModal">
                    <div class="modal1-content">
                        <div class="modal-header">
                            <button type="button" class="close"  ng-click="hideBccData()" data-dismiss="modal">&times;</button>
                            <h4 class="modal-title">Requesting Units {{selectedpartnerID.name}}</h4>
                        </div>
                        <div class="modal-body" style="text-align: center">
                            <div data-ng-repeat="data in bccList track by $index" >
                                <p style="text-align: start">{{data}}</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="TableMobileView" data-ng-if="prs.length>0">
                    <div>
        <span>
            <input id="allPrs" type="checkbox" data-ng-change="selectAllPaymentRequests()" data-ng-model="selectAllPrs"
                   data-ng-checked="selectAllPrs==true" />
            <label for="allPrs">Select All</label>
        </span>
                    </div>
                    <ul class="collection striped center">
                        <li class="collection-item"
                            data-ng-repeat="pr in filtered = (filteredPrs | filter:search | orderBy : 'vendorId.name') track by $index">
                            <div class="row">
                                <div class="col">
                                    Select
                                </div>
                                <div class="col">
                    <span>
                        <input id="pr-{{pr.paymentRequestId}}" type="checkbox" data-ng-model="pr.selected" />
                        <label for="pr-{{pr.paymentRequestId}}"></label>
                    </span>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col">PR Id</div>
                                <div class="col">
                                    <span data-ng-class="{'prBlocked':pr.blocked==true}">{{pr.paymentRequestId}}</span>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col">Company Name</div>
                                <div class="col">{{companyMap[pr.companyId].name}}</div>
                            </div>
                            <div class="row">
                                <div class="col">Invoice #</div>
                                <div class="col">{{pr.invoiceNumber}}</div>
                            </div>
                            <div class="row">
                                <div class="col">Creation Date</div>
                                <div class="col">{{pr.creationTime | date:'dd-MM-yyyy hh:mm:ss a'}}</div>
                            </div>
                            <div class="row">
                                <div class="col">Vendor</div>
                                <div class="col">{{pr.vendorId.name}}</div>
                            </div>
                            <div class="row">
                                <div class="col">Credit Cycle</div>
                                <div class="col">{{pr.vendorCreditPeriod}}</div>
                            </div>
                            <div class="row">
                                <div class="col">Ledger Balance</div>
                                <div class="col">{{pr.vendorDebitBalance}}</div>
                            </div>
                            <div class="row">
                                <div class="col">Unit</div>
                                <div class="col">{{pr.requestingUnit.name}}</div>
                            </div>
                            <div class="row">
                                <div class="col">Proposed Amount</div>
                                <div class="col">{{pr.proposedAmount}}</div>
                            </div>
                            <div class="row">
                                <div class="col">Paid Amount</div>
                                <div class="col">{{pr.paidAmount}}</div>
                            </div>
                            <div class="row">
                                <div class="col">Payment Date</div>
                                <div class="col">{{pr.paymentDate | date:'dd-MM-yyyy'}}</div>
                            </div>
                            <div class="row">
                                <div class="col">Vendor Payment Date</div>
                                <div class="col">{{(pr.paymentCycle != null? pr.paymentCycle.paymentDate:pr.vendorPaymentDate) |
                                    date:'dd-MM-yyyy'}}</div>
                            </div>
                            <div class="row">
                                <div class="col">Status</div>
                                <div class="col">{{pr.currentStatus}}</div>
                            </div>
                            <div class="row">
                                <div class="col">Actions</div>
                                <div class="col">
                                    <input class="btn btn-xs-small" style="margin-bottom: 5px;" type="button" value="View"
                                           data-ng-click="viewPaymentRequest(pr,'VIEW')">
                                    <input class="btn btn-xs-small" style="margin-bottom: 5px;" type="button" value="Update Invoice"
                                           data-ng-if="pr.currentStatus=='INITIATED'" acl-action="PRUPI"
                                           data-ng-click="viewPaymentRequest(pr,'UPDATE_INVOICE')">
                                    <input class="btn btn-xs-small" style="margin-bottom: 5px;" type="button" value="Cancel"
                                           data-ng-if="pr.currentStatus=='CREATED' || pr.currentStatus=='INITIATED'" acl-action="PRCNL"
                                           data-ng-click="changeStatus(pr,'CANCELLED')">
                                    <!--       <input class="btn btn-xs-small" style="margin-bottom: 5px;" type="button"
                                       value="Acknowledge" data-ng-if="pr.currentStatus=='CREATED'"
                                       acl-action="PRACK" data-ng-click="changeStatus(pr,'ACKNOWLEDGED')">
                                <input class="btn btn-xs-small" style="margin-bottom: 5px;" type="button"
                                       value="Approve" data-ng-if="pr.currentStatus=='ACKNOWLEDGED'"
                                       acl-action="PRAPR" data-ng-click="viewPaymentRequest(pr,'APPROVE')">
                                <input type="button" class="btn btn-xs-small" value="Settle" data-ng-if="pr.currentStatus=='SENT_FOR_PAYMENT' && pr.blocked==false && pr.debitExceeded==false"
                                       acl-action="PRSTL" data-target="paymentSettleModal" modal data-ng-click="resetPaymendivetail(pr)" />
                                <input type="button" class="btn btn-xs-small" value="Pay Adhoc" data-ng-if="pr.currentStatus=='`APPROVED`' && pr.blocked==false && pr.debitExceeded==false"
                                       acl-action="PRADP" data-target="reasonModal" modal data-ng-click="setAction(pr,'ADHOC')" /> -->
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
                <div data-ng-if="showNoPR" style="padding: 10px; margin: 10px; border-radius: 4px; background: #a0e1e4;">No payment requests found.</div>
                <!-- <input type="button" class="btn" data-ng-if="prs.length>0" value="Start Payment Process"
                       acl-action="PRSPP" data-target="paymentSheetModal" modal />
                <input type="button" class="btn" data-ng-if="prs.length>0" value="Un-block requests"
                       acl-action="PRUBL" data-ng-click="setAction(null,'UNBLOCK')" data-target="reasonModal" modal />
                <input type="button" class="btn" data-ng-if="prs.length>0" value="Block requests"
                       acl-action="PRBLK" data-ng-click="setAction(null,'BLOCK')" data-target="reasonModal" modal /> -->
            </div>
        </div>
    </div>

    <div data-ng-show="selectView==false">
        <div class="row">
            <div class="col s12">
                <input type="button" class="btn btn-xs-small" data-ng-click="backToSelectView()" value="Back">
            </div>
        </div>
        <div>
			<div class="row">
				<div class="col s12"><b>Company Name:</b> {{companyMap[viewPr.companyId].name}}</div>
			</div>
			<div class="row">
                <div class="col s6">
                    <p><b>PR Id:</b> {{viewPr.paymentRequestId}}</p>
                    <p><b>Vendor:</b> {{viewPr.vendorId.name}}</p>
                    <p><b>Invoice Number:</b> {{viewPr.paymentInvoice.invoiceNumber}}</p>
                    <p><b>Status:</b> {{viewPr.currentStatus}}</p>
                    <p><b>Deviation Count:</b> {{viewPr.deviationCount}}</p>
                    <p><b>Blocked:</b> {{viewPr.blocked==true?'Yes':'No'}}</p>
                </div>
                <div class="col s6">
                    <p><b>Creation time:</b> {{viewPr.creationTime | date:'dd-MM-yyyy hh:mm:ss a'}}</p>
                    <p><b>Invoice Date:</b> {{viewPr.paymentInvoice.invoiceDate | date: 'dd-MM-yyyy hh:mm:ss a'}}</p>
                    <p><b>Amount match:</b> {{viewPr.amountsMatch==true?'Yes':'No'}}</p>
                    <p><b>Doc Type:</b> {{viewPr.grDocType}}</p>
                    <p><b>Last update:</b> {{viewPr.lastUpdated | date:'dd-MM-yyyy hh:mm:ss a'}}</p>
                    <p data-ng-if="viewPr.blocked"><b>Blocked by:</b> {{viewPr.blockedBy.name}}</p>
                    <p><b>Vendor Credit Days:</b> {{viewPr.vendorCreditPeriod}}</p>
                </div>
            </div>

            <div class="row" data-ng-if="viewPr.type != 'ADVANCE_PAYMENT'">
                <div class="respTable col s12">
                    <table class="row table bordered striped" style="border: #ccc 1px solid;">
                        <thead>
                        <tr>
                            <th>Sku Id</th>
                            <th>Sku Name</th>
                            <th data-ng-if="viewPr.type == 'GOODS_RECEIVED'">Asset Tag</th>
                            <th data-ng-if="prRequestType.shortCode!='SR'">Category</th>
                            <th data-ng-if="prRequestType.shortCode!='SR'">Sub Category</th>
                            <th data-ng-if="prRequestType.shortCode=='SR'">Description</th>
                            <th>Packaging</th>
                            <th>Quantity</th>
                            <th>Price</th>
                            <th>Tax</th>
                            <th>Total Tax</th>
                            <th>Total Amount</th>
                            <th>Deviations</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr data-ng-repeat="item in viewPr.paymentInvoice.paymentInvoiceItems">
                            <td><a data-ng-click="showPreview($event, item.skuId,'SKU')">{{item.skuId}}</a></td>
                            <td>{{item.skuName}}[{{item.hsn}}]</td>
                            <td data-ng-if="viewPr.type == 'GOODS_RECEIVED'">
                                <a data-ng-click="showAssetTags(item,item.assetTags)" data-ng-if="item.assetTags.length==1">{{item.assetTags[0]}}</a>
                                <a data-ng-click="showAssetTags(item,item.assetTags)" data-ng-if="item.assetTags.length>1">{{item.assetTags[0]}} +{{item.assetTags.length-1}}More</a>
                                <a data-ng-if="item.assetTags==null || item.assetTags==undefined || item.assetTags.length<1"> - </a>
                            </td>
                            <td data-ng-if="prRequestType.shortCode!='SR'">{{item.category}}</td>
                            <td data-ng-if="prRequestType.shortCode!='SR'">{{item.subCategory}}</td>
                            <td data-ng-if="prRequestType.shortCode=='SR'">{{item.costDescription}}</td>
                            <td>{{item.packagingName}}</td>
                            <td>{{item.quantity}}</td>
                            <td>{{item.unitPrice}}</td>
                            <td><span data-ng-repeat="tax in item.taxes">{{tax.taxType}}@{{tax.taxPercentage}}%,</span></td>
                            <td>{{item.totalTax}}</td>
                            <td>{{item.totalAmount}}</td>
                            <td>
                                <span data-ng-repeat="d in item.deviations" class="deviationTag {{d.currentStatus}}" tooltipped
                                      data-position="bottom" data-delay="10" data-tooltip="{{d.deviationRemark}} ## {{d.actionRemark}}">
                                    {{d.paymentDeviation.deviationDetail}}
                                    <span data-ng-if="actionType=='UPDATE_INVOICE'" style="cursor: pointer;" data-ng-click="deleteDeviation(item.deviations, $index)">&times</span>
                                </span>
                                <!--<input type="button" value="Manage" class="btn btn-small" data-ng-show="actionType=='APPROVE'"
                                       data-ng-click="setAvailableDeviations(item, 'INVOICE_ITEM')" data-target="manageDevModal" modal />-->
                                <input type="button" value="Add" class="btn btn-small" data-ng-show="actionType=='UPDATE_INVOICE'"
                                       data-ng-click="setAvailableDeviations(item, 'INVOICE_ITEM')" data-target="addDevModal" modal />
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="row">
                <div class="col s6">
                    <div style="background: #efefef; border: #ddd 1px solid; padding: 10px;font-size: 14px;">
                        <p><b>Created By:</b> {{viewPr.createdBy.name}}</p>
                        <p data-ng-if="prRequestType.shortCode=='GR'"><b>Requesting Unit:</b> {{viewPr.requestingUnit.name}}</p>
                        <p data-ng-if="prRequestType.shortCode=='SR'"><b>Requesting Unit:</b>{{requestingUnitsForView}}</p>
                        <p><b>Basic Amount:</b> {{viewPr.paymentInvoice.calculatedInvoiceAmount}}</p>
                        <p><b>Other Charges:</b> {{viewPr.paymentInvoice.extraCharges}}</p>
                        <p><b>Total Bill Amount:</b> {{viewPr.paymentInvoice.paymentAmount}}</p>
                        <p><b>Proposed Amount:</b> {{viewPr.proposedAmount}}</p>
                    </div>
                </div>
                <div class="col s6" data-ng-show="actionType!='UPDATE_INVOICE'">
                    <label data-ng-show=" prRequestType.code != 'ADVANCE_PAYMENT'">Category Wise Amount:</label>
                    <input type="button" value="Summary" class="btn btn-small" data-ng-show=" prRequestType.code != 'ADVANCE_PAYMENT'"
                           data-ng-click="clubSummaryAmountModal(viewPr)"/>
                    <p data-ng-show=" prRequestType.code != 'ADVANCE_PAYMENT'"><label>Invoice Number:</label> {{viewPr.paymentInvoice.invoiceNumber}}</p>
                    <p><label>Invoice Date:</label> {{viewPr.paymentInvoice.invoiceDate | date: 'dd-MM-yyyy hh:mm:ss a'}}</p>
                    <p>
                        <label>Load Invoice:</label>
                        <input data-ng-if="uploadedDocData==null" type="button" value="Load" class="btn btn-small" data-ng-click="loadInvoiceDoc()" />
                        <input type="button" value="Preview Invoice" data-ng-if="uploadedDocData!=null"
                               data-ng-click="previewPRInvoice(uploadedDocData)" class="btn btn-small" data-target="invoicePreviewModal" modal />
                        <input type="button" value="Download Invoice" data-ng-if="uploadedDocData!=null"
                               data-ng-click="downloadPRInvoice(uploadedDocData)" class="btn btn-small" />
                    </p>
                    <p>
                        <label data-ng-if="viewPr.type=='SERVICE_RECEIVED'"> Load Documents:</label>
                        <input data-ng-if="viewPr.type=='SERVICE_RECEIVED'" type="button" value="Load Mandatory Documents" class="btn btn-large-medium" data-ng-click="loadMandatoryDoc()" data-target="openMandatoryReqDocModal" modal />
                    </p>
                </div>
                <div class="col s6" data-ng-if="viewPr.paymentRequestQueries!= null && viewPr.paymentRequestQueries.length > 0">
                    <div class="col s3 center">
                        <input type="button" value="Show Queries" class="btn btn-medium" data-ng-click="showQueries(false)"/>
                    </div>
                    <div class="col s3 center" style="margin-left: 15px;" data-ng-if="actionType =='QUERIED'">
                        <input type="button" value="Resolve Queries" class="btn btn-medium" data-ng-click="showQueries(true)"/>
                    </div>
                </div>
                <div class="col s6" data-ng-show="actionType=='UPDATE_INVOICE'">
                    <p><label>Invoice Number:</label> <input type="text" data-ng-model="viewPr.paymentInvoice.invoiceNumber" /></p>
                    <p><label>Invoice Date:</label> <input data-ng-if="actionType=='UPDATE_INVOICE'" input-date type="text" ng-model="viewPr.paymentInvoice.invoiceDate" format="yyyy-mm-dd" /></p>
                    <p>
                        <label>Attach Invoice:</label>
                        <input type="button" value="Scan Doc" class="btn btn-small" data-target='scanModal' modal data-ng-click="resetScanModal()" />
                        <input type="button" value="Snapshot" class="btn btn-small" data-target='snapModal' modal data-ng-click="resetSnapModal()" />
                        <input type="button" value="Upload File" data-ng-click="uploadDoc()" class="btn btn-small" />
                        <input type="button" value="Preview Invoice" data-ng-if="uploadedDocData!=null"
                               data-ng-click="previewPRInvoice(uploadedDocData)" class="btn btn-small" data-target="invoicePreviewModal" modal />
                        <input type="button" value="Download Invoice" data-ng-if="uploadedDocData!=null"
                               data-ng-click="downloadPRInvoice(uploadedDocData)" class="btn btn-small" />
                    </p>
                </div>
            </div>
            <div class="row" data-ng-if="viewPr.paymentCard != null">
                <div class="col s3">
                    <u><label for="paymentCard">Payment Card</label></u>
                    <p id="paymentCard">{{viewPr.paymentCard}}</p>
                </div>
                <div class="col s3">
                    <u><label for="cardPaymentTransactionNumber">Transaction Number</label></u>
                    <p id="cardPaymentTransactionNumber">{{viewPr.cardPaymentTransactionNumber}}</p>
                </div>
                <div class="col s3">
                    <u><label>Card Payment Comment</label></u>
                    <textarea data-ng-model="viewPr.cardPaymentComment" style="resize: none" data-ng-disabled="true" data-ng-if="viewPr.cardPaymentComment != null"></textarea>
                    <p data-ng-if="viewPr.cardPaymentComment == null">-</p>
                </div>
                <div class="col s3" style="margin-top: 5px">
                    <button class="btn btn-xs-small vBtn margin-right-5" style="margin-top: 15px"
                            data-ng-click="downloadDocumentById(viewPr.cardPaymentProof)">Download Proof</button>
                </div>
            </div>
            <div class="row">
                <div class="col s6">
                    <p data-ng-if="viewPr.paymentInvoice.deviations.length>0 || actionType!='VIEW'">Deviations:</p>
                    <span data-ng-repeat="d in viewPr.paymentInvoice.deviations" class="deviationTag {{d.currentStatus}}" tooltipped
                          data-position="bottom" data-delay="10" data-tooltip="{{d.deviationRemark}} ## {{d.actionRemark}}">
                        {{d.paymentDeviation.deviationDetail}}
                        <span data-ng-if="actionType=='UPDATE_INVOICE'" style="cursor: pointer;"
                              data-ng-click="deleteDeviation(viewPr.paymentInvoice.deviations, $index)">&times</span>
                    </span>
                    <!--<input type="button" data-ng-show="actionType=='APPROVE'" value="Manage deviation" class="btn" data-target='manageDevModal'
                           data-ng-click="setAvailableDeviations(viewPr.paymentInvoice, 'INVOICE')" modal />-->
                    <input type="button" data-ng-show="actionType=='UPDATE_INVOICE'" value="Add deviation" class="btn" data-target='addDevModal'
                           data-ng-click="setAvailableDeviations(viewPr.paymentInvoice, 'INVOICE')" modal />
                </div>
                <div class="col s6">
                    <p data-ng-if="viewPr.paymentInvoice.rejections.length>0 || actionType!='VIEW'">Rejections:</p>
                    <span class="deviationTag"
                          data-ng-repeat="r in viewPr.paymentInvoice.rejections">
                            {{r.paymentDeviation.deviationDetail}} : {{r.deviationRemark}}
                            <!--<span style="cursor: pointer;font-size: 24px;" data-ng-show="actionType=='APPROVE'"
                                  data-ng-click="deleteRejection(viewPr.paymentInvoice.rejections, $index)">&times;</span>-->
                        </span>
                    <!--<input type="button" value="Manage rejection" class="btn" data-ng-show="actionType=='APPROVE'"
                           data-target='addRejectionModal' data-ng-click="setAvailableRejections()" modal />-->
                </div>
            </div>
            <!--<div class="row">
                <div class="col s6" data-ng-show="actionType=='APPROVE'">
                    <input type="button" class="btn" value="Get Payment Dates" data-ng-click="getProposedPaymentDates()" data-target="calendarModal" modal />
                    <label data-ng-if="selectedPaymentDate!=null">Selected Payment Cycle</label>
                    <table class="table striped" style="border:#ddd 1px solid;" data-ng-if="selectedPaymentDate!=null">
                        <thead><tr><th>Cycle</th><th>Payment Date</th></tr></thead>
                        <tbody><tr><td>{{selectedPaymentDate.name}} - {{selectedPaymentDate.cycleTag}}</td><td>{{selectedPaymentDate.paymentDate | date:'dd-MM-yyyy hh:mm:ss a'}}</td></tr></tbody>
                    </table>
                </div>
            </div>-->
            <div class="row">
                <div class="col s6">
                    <label>Paid Amount</label>
                    <!--<input type="number" data-ng-model="viewPr.paidAmount" data-ng-show="actionType=='APPROVE' && viewPr.debitNote==null" />-->
                    <span data-ng-show="viewPr.debitNote!=null || actionType=='VIEW'">{{viewPr.paidAmount}}</span>
                    <!--<button class="btn btn-xs-small" data-ng-show="actionType=='APPROVE' && viewPr.paidAmount!=viewPr.proposedAmount && viewPr.debitNote==null"
                            data-ng-click="resetPaidAmount()">Reset</button>-->
                </div>
                <!--<div class="col s6" data-ng-show="actionType=='APPROVE'">
                    <input type="button" class="btn" value="Add debit note" data-target='addDebitNoteModal' style="margin-top: 20px;"
                           data-ng-if="viewPr.debitNote==null && viewPr.paidAmount<viewPr.proposedAmount" data-ng-click="createDebitNote()" modal />
                </div>-->
            </div>
            <div class="row">
                <div class="col s6" data-ng-show="actionType=='VIEW'">
                    <label>Remarks:</label>
                    {{viewPr.remarks}}
                </div>
                <div class="col s6" data-ng-if="viewPr.debitNote!=null">
                    <label style="margin-top: 5px;">Debit Note Detail:</label>
                    <table class="table striped" style="border: #ccc 1px solid;">
                        <thead>
                        <tr>
                            <th>Amount</th>
                            <th>Tax</th>
                            <th>Total</th>
                            <th>Advance Amount</th>
                            <th>Extra Debit Note Amount</th>
                            <th>Created By</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td>{{viewPr.debitNote.amount}}</td>
                            <td>{{viewPr.debitNote.totalTaxes}}</td>
                            <td>{{viewPr.debitNote.totalAmount}}</td>
                            <td data-ng-if="viewPr.debitNote.advanceAmount != null">{{viewPr.debitNote.advanceAmount}}</td>
                            <td data-ng-if="viewPr.debitNote.advanceAmount == null">-</td>
                            <td data-ng-if="viewPr.debitNote.advanceAmount != null">{{viewPr.debitNote.totalAmount - viewPr.debitNote.advanceAmount}}</td>
                            <td data-ng-if="viewPr.debitNote.advanceAmount == null">-</td>
                            <td>{{viewPr.debitNote.generatedBy.name}}</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="row">
                <div class="col s6">
                    <input type="button" class="btn" value="View logs" data-target='viewLogsModal' modal />
                    <input type="button" class="btn" value="Payment Detail" data-ng-if="actionType=='VIEW' && viewPr.currentStatus=='PAID'"
                           data-target='paymentViewModal' modal />
                </div>
                <div class="col s6" data-ng-if="actionType=='UPDATE_INVOICE'">
                    <span>
                        <input id="amountsMatch" type="checkbox" data-ng-model="viewPr.amountsMatch" />
                        <label for="amountsMatch">Amounts match</label>
                    </span>
                </div>
                <div class="col s6" data-ng-if="actionType=='VIEW'">
                    <div data-ng-repeat="item in viewPr.requestItemMappings track by item.id" class="prItemMapping {{item.status}}">
                        {{item.paymentRequestType}} #{{item.paymentRequestItemId}} - {{item.status}}
                    </div>
                </div>
            </div>
            <div class="row" data-ng-if="actionType=='PENDING_HOD_APPROVAL'">
                <div class="col s6">
                    <div style="background: #efefef; border: #ddd 1px solid; padding: 10px;font-size: 14px;">
                        <p class="center"><b><u>Advance Details</u></b></p>
                        <p><b>Advance Amount Used :</b> {{viewPr.advanceAmount}}</p>
                        <p><b>{{viewPr.vendorAdvancePayments[0].advanceType == 'SO_ADVANCE' ? 'SO' : 'PO'}} Closed :</b> {{(isAdjusted || refundSelectedDate != null) ? 'Yes' : 'No'}}</p>
                        <p data-ng-if="isAdjusted"><b>Adjusted With {{viewPr.vendorAdvancePayments[0].advanceType == 'SO_ADVANCE' ? 'SO' : 'PO'}} :</b> {{selectedSoPo}}</p>
                        <p data-ng-if="isAdjusted"><b>Adjusted Amount :</b> {{amount}}</p>
                        <p data-ng-if="refundSelectedDate != null"><b>Refund Date :</b> {{refundSelectedDate}}</p>
                        <p data-ng-if="refundSelectedDate != null"><b>Refund Amount :</b> {{amount}}</p>
                    </div>
                </div>
            </div>
            <div class="row" data-ng-if="actionType=='HOD_REJECTED'">
                <div class="col s6">
                    <div style="background: #efefef; border: #ddd 1px solid; padding: 10px;font-size: 14px;">
                        <p class="center"><b><u>Advance Details</u></b></p>
                        <p><b>Advance Amount Used :</b> {{viewPr.advanceAmount}}</p>
                        <p><b>Advance Payment Id :</b> {{involvedAdvanceIds.join(",")}}</p>
                    </div>
                </div>
            </div>
            <div class="row">
                <!--<div class="col s12" data-ng-show="actionType=='APPROVE'">
                    <input type="button" style="float: right;" class="btn green" value="Approve Request" data-ng-click="approvePaymentRequest()" />
                    <input type="button" class="btn red" value="Reject Request" data-ng-click="rejectPaymentRequest()" />
                </div>-->
                <div class="col s12" data-ng-show="actionType=='UPDATE_INVOICE'">
                    <input type="button" class="btn" value="Update Request" data-ng-click="updatePaymentRequest()" />
                    <input type="button" class="btn" acl-action="SUMO_FUPRI" value="Force Update Request" data-ng-click="updatePaymentRequest(true)" />
                </div>
            </div>
            <div class="row" data-ng-if="poSrDetails.length > 0">
                <div class="col s12">
                    <table class="bordered striped">
                        <thead>
                        <tr data-ng-if="viewPr.type=='GOODS_RECEIVED'">
                            <th>S.No</th>
                            <th>GR Id</th>
                            <th>GR Creation Date</th>
                            <th>Status</th>
                            <th>PO Id</th>
                            <th>PO Creation Date</th>
                        </tr>
                        <tr data-ng-if="viewPr.type=='SERVICE_RECEIVED'">
                            <th>S.No</th>
                            <th>SR Id</th>
                            <th>SR Creation Date</th>
                            <th>Status</th>
                            <th>SO Id</th>
                            <th>SO Creation Date</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr data-ng-if="viewPr.type=='GOODS_RECEIVED'" data-ng-repeat="item in poSrDetails track by $index">
                            <td>{{$index+1}}</td>
                            <td>{{item.grId}}</td>
                            <td>{{item.grGenerationTime| date:'dd/MM/yyyyhh:mm:ss'}}</td>
                            <td>{{item.status}}</td>
                            <td data-ng-if="item.multiplePo.length == 0">{{item.poId}}</td>
                            <td data-ng-if="item.multiplePo.length == 0">{{item.poGenerationTime| date:'dd/MM/yyyy hh:mm:ss'}}</td>
                            <td colspan="2" data-ng-if="item.multiplePo.length > 0">
                                <button class="btn btn-medium" data-ng-click="viewMultipleSoPos(item.multiplePo)">View Po's({{item.multiplePo.length}})</button>
                            </td>
                        </tr>
                        <tr data-ng-if="viewPr.type=='SERVICE_RECEIVED'" data-ng-repeat="item in poSrDetails track by $index">
                            <td>{{$index+1}}</td>
                            <td>{{item.srId}}</td>
                            <td>{{item.srGenerationTime| date:'dd/MM/yyyyhh:mm:ss'}}</td>
                            <td>{{item.status}}</td>
                            <td data-ng-if="item.multipleSo.length == 0">{{item.soId}}</td>
                            <td data-ng-if="item.multipleSo.length == 0">{{item.soGenerationTime| date:'dd/MM/yyyy hh:mm:ss'}}</td>
                            <td colspan="2" data-ng-if="item.multipleSo.length > 0">
                                <button class="btn btn-medium" data-ng-click="viewMultipleSoPos(item.multipleSo)">View So's({{item.multipleSo.length}})</button>
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>

            </div>
            <div class="row" data-ng-if="actionType == 'PENDING_HOD_APPROVAL'">
                <input type="button" class="btn btn-danger left red" value="HOD REJECT" data-ng-click="changeStatus(viewPr,'HOD_REJECTED')" />
                <input type="button" class="btn btn-medium right" value="HOD APPROVE" data-ng-click="changeStatus(viewPr,'CREATED')" />
            </div>
        </div>
    </div>

</div>
<!--
<div id="manageDevModal" class="modal">
    <div class="modal-content">
        <h5>Item deviations</h5>
        <table data-ng-if="selectedItemForDev.deviations.length>0">
            <thead><tr><th>Deviation</th><th>Action Remarks</th><th>Action</th></tr></thead>
            <tbody>
                <tr data-ng-repeat="d in selectedItemForDev.deviations track by $index">
                    <td>{{d.paymentDeviation.deviationDetail}} - {{d.deviationRemark}} [{{d.currentStatus}}]</td>
                    <td><input type="text" data-ng-model="d.actionRemark" /></td>
                    <td>
                        <input type="button" class="btn btn-xs-small" value="Accept" data-ng-if="d.currentStatus!='ACCEPTED'" data-ng-click="deviationAction(d,'APPROVE')" />
                        <input type="button" class="btn btn-xs-small" value="Reject" data-ng-if="d.currentStatus!='REJECTED'" data-ng-click="deviationAction(d,'REJECT')" />
                        <input type="button" class="btn btn-xs-small" value="Remove" data-ng-if="d.currentStatus!='REMOVED' && d.addType!='NEW'" data-ng-click="deviationAction(d,'REMOVE')" />
                        <input type="button" class="btn btn-xs-small" value="Delete" data-ng-if="d.addType=='NEW'"
                               data-ng-click="deleteDeviation(selectedItemForDev.deviations,$index)" />
                    </td>
                </tr>
            </tbody>
        </table>
        <p>Available deviations:</p>
        <div class="row" data-ng-repeat="d in availableDevs track by $index">
            <div class="col s4">{{d.data.deviationDetail}}</div>
            <div class="col s7"><input type="text" data-ng-model="d.remark" /></div>
            <div class="col s1">
                <span>
                    <input type="checkbox" id="id-{{$index}}" data-ng-model="d.checked" />
                    <label for="id-{{$index}}"></label>
                </span>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn btn-small modal-action modal-close" data-ng-click="addDeviations(selectedItemForDev, selectedItemForDevType)">Add</button>
        <button class="btn btn-small modal-action modal-close red" style="margin-right: 20px;">Close</button>
    </div>
</div>-->

<div id="addDevModal" class="modal">
    <div class="modal-content">
        <h5>Item deviations</h5>
        <div class="row" data-ng-repeat="d in availableDevs track by $index">
            <div class="col s4">{{d.data.deviationDetail}}</div>
            <div class="col s7"><input type="text" data-ng-model="d.remark" /></div>
            <div class="col s1">
                <span>
                    <input type="checkbox" id="ad-{{$index}}" data-ng-model="d.checked" />
                    <label for="ad-{{$index}}"></label>
                </span>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn btn-small modal-action modal-close"
                data-ng-click="addDeviations(selectedItemForDev)">Submit</button>
        <button class="btn btn-small modal-action modal-close red" style="margin-right: 20px;">Close</button>
    </div>
</div>

<!--<div id="addRejectionModal" class="modal">
    <div class="modal-content">
        <h4>Add deviation</h4>
        <div class="row" data-ng-repeat="d in availableRejections">
            <div class="col s6">{{d.data.deviationDetail}}: </div>
            <div class="col s3">
                <span>
                    <input id="ir-{{$index}}" type="checkbox" data-ng-model="d.checked" />
                    <label for="ir-{{$index}}"></label>
                </span>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn btn-small modal-action modal-close" data-ng-click="addRejections()">Submit</button>
        <button class="btn btn-small modal-action modal-close red" data-ng-click="addRejections()" style="margin-right: 20px;">Close</button>
    </div>
</div>-->
<!--
<div id="addDebitNoteModal" class="modal">
    <div class="modal-content">
        <h4>Add debit note</h4>
        <div class="row">
            <div class="col s12">
                <label>Amount:</label>
                <input type="number" data-ng-model="debitNote.amount" data-ng-change="calculateTotalAmount()" />
            </div>
            <div class="col s12">
                <label>Tax:</label>
                <input type="number" data-ng-model="debitNote.totalTaxes" data-ng-change="calculateTotalAmount()" />
            </div>
            <div class="col s12">
                <label>Busy Reference Number*:</label>
                <input type="text" data-ng-model="debitNote.busyReferenceNumber" />
            </div>
            <div class="col s12">
                <label>Total Amount:</label> {{debitNote.totalAmount}}
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn btn-small modal-action modal-close red">Cancel</button>
        <button class="btn btn-small modal-action modal-close" data-ng-click="submitDebitNote()" style="margin-right: 20px;">Submit</button>
    </div>
</div>-->

<div id="viewLogsModal" class="modal">
    <div class="modal-content">
        <h4>Log detail</h4>
        <div class="row">
            <div class="col s12">
                Logs:
                <ul>
                    <li data-ng-repeat="log in viewPr.requestLogs"
                        style="background: #a5e9fd;border:1px solid #8fd5f7;padding:5px;margin: 5px 0;">
                        {{log.updateTime | date:'dd-MM-yyyy hh:mm:ss a'}} : {{log.logData}}
                    </li>
                </ul>
            </div>
            <div class="col s12">
                Status logs:
                <ul>
                    <li data-ng-repeat="log in viewPr.statusLogs"
                        style="background: #78ece8;border:#06b5ab 1px solid;padding:5px;margin: 5px 0;">
                        {{log.updateTime | date:'dd-MM-yyyy hh:mm:ss a'}} : {{log.fromStatus}} to {{log.toStatus}} by {{log.updatedBy.name}}
                    </li>
                </ul>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn btn-small modal-action modal-close">Close</button>
    </div>
</div>

<div id="snapModal" class="modal">
    <div class="modal-content">
        <h4>Take Snapshot</h4>
        <video data-ng-show="snapRunning" id="video" width="640" height="480" autoplay></video><br />
        <button data-ng-click="startSnap()" class="btn btn-small">Start</button>
        <button data-ng-click="snapPicture()" class="btn btn-small">Snap Photo</button><br />
        <canvas data-ng-hide="snapRunning" id="canvas" width="640" height="480"></canvas><br />
    </div>
    <div class="modal-footer">
        <button class="btn btn-small modal-action modal-close" data-ng-click="uploadFile()">Upload</button>
        <button class="btn btn-small modal-action modal-close red" style="margin-right: 20px;">Cancel</button>
    </div>
</div>

<div id="scanModal" class="modal">
    <div class="modal-content">
        <h3>Scan document</h3>
        <button type="button" data-ng-click="scanToPng()">Scan</button>
        <div id="images" style="margin-top: 20px;"></div>
    </div>
    <div class="modal-footer">
        <button class="btn btn-small modal-action modal-close" data-ng-click="uploadScannedFile()">Upload</button>
        <button class="btn btn-small modal-action modal-close red" style="margin-right: 20px;">Cancel</button>
    </div>
</div>

<div id="openMandatoryReqDocModal" class="modal">
    <div class="modal-content">
        <h5>Required Documents</h5>
        <div class="row" data-ng-repeat="doc in mandatoryDocuments">
            <div class="col s3">{{doc.name}}</div>  <a href={{doc.code}} target="_blank" download><button class="btn btn-small">Download</button></a>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn btn-small modal-action modal-close red" style="margin-right: 20px;">Close</button>
    </div>

</div>
<!--
<div id="calendarModal" class="modal">
    <div class="modal-content">
        <label>Select Payment Cycle</label>
        <select ui-select2 id="getProposedPaymentDates" name="getProposedPaymentDates" data-ng-model="selectedPaymentDate"
                data-ng-options="item as item.name for item in proposedPaymentDates track by item.id"></select>
        <table data-ng-if="selectedPaymentDate!=null">
            <thead><tr><th>Cycle</th><th>Payment Date</th></tr></thead>
            <tbody><tr><td>{{selectedPaymentDate.name}} - {{selectedPaymentDate.cycleTag}}</td><td>{{selectedPaymentDate.paymentDate | date:'dd-MM-yyyy hh:mm:ss a'}}</td></tr></tbody>
        </table>
    </div>
    <div class="modal-footer">
        <button class="btn btn-small modal-action modal-close">Close</button>
    </div>
</div>-->
<!--
<div id="paymentSheetModal" class="modal">
    <div class="modal-content">
        <div class="row">
            <div class="col s12">
                <label>Select Payment Bank</label>
                <span>
                    <input name="group1" type="radio" id="yes" data-ng-model="bankName" value="YES_BANK" />
                    <label for="yes">Yes Bank</label>
                </span>
                        <span>
                    <input name="group1" type="radio" id="kotak" data-ng-model="bankName" value="KOTAK" />
                    <label for="kotak">Kotak Bank</label>
                </span>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn modal-action modal-close" data-ng-click="downloadPaymentSheet()">Download Payment Sheet</button>
        <button class="btn modal-action modal-close red" style="margin-right: 20px;">Close</button>
    </div>
</div>-->
<!--

<div id="paymentSettleModal" class="modal">
    <div class="modal-content">
        <div class="row">
            <div class="col s12">
                <p>
                    <label>Select Payment Bank*</label>
                    <span>
                        <input name="group2" type="radio" id="yes1" data-ng-model="paymentDetail.debitBank" value="YES_BANK" />
                        <label for="yes1">Yes Bank</label>
                    </span>
                    <span>
                        <input name="group2" type="radio" id="kotak1" data-ng-model="paymentDetail.debitBank" value="KOTAK" />
                        <label for="kotak1">Kotak Bank</label>
                    </span>
                </p>
                <p>
                    <label>Beneficiary Account Number*</label>
                    {{paymentDetail.beneficiaryAccountNumber}}
                </p>
                <p>
                    <label>Beneficiary IFSC Code*</label>
                    {{paymentDetail.beneficiaryIfscCode}}
                </p>
                <p>
                    <label>Debit Account Number*</label>
                    <input type="text" data-ng-model="paymentDetail.debitAccount" />
                </p>
                <p>
                    <label>Payment Type*</label>
                    <select ui-select2 id="ptList" name="ptList" data-ng-model="selectedPaymentType"
                            data-ng-options="item as item.name for item in paymentTypeList track by item.id"></select>
                </p>
                <p>
                    <label>Paid Amount*</label>
                    <input type="text" data-ng-model="paymentDetail.paidAmount" />
                </p>
                <p>
                    <label>Payment Date*</label>
                    <input input-date type="text" data-ng-model="paymentDetail.paymentDate" container="" format="yyyy-mm-dd"/>
                </p>
                <p>
                    <label>Remarks</label>
                    <input type="text" data-ng-model="paymentDetail.remarks" />
                </p>
                <p>
                    <label>UTR Number*</label>
                    <input type="text" data-ng-model="paymentDetail.utrNumber" />
                </p>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn modal-action modal-close" data-ng-click="settlePaymentRequests()">Settle Payment Requests</button>
        <button class="btn modal-action modal-close red" style="margin-right: 20px;">Close</button>
    </div>
</div>
-->

<div id="paymentViewModal" class="modal">
    <div class="modal-content">
        <div class="row">
            <div class="col s12">
                <p>
                    <label>Payment Bank</label>
                    {{viewPr.paymentDetail.debitBank}}
                </p>
                <p>
                    <label>Debit Account Number</label>
                    {{viewPr.paymentDetail.debitAccount}}
                </p>
                <p>
                    <label>Beneficiary Name</label>
                    {{viewPr.paymentDetail.vendorName}}
                </p>
                <p>
                    <label>Beneficiary Account Number</label>
                    {{viewPr.paymentDetail.beneficiaryAccountNumber}}
                </p>
                <p>
                    <label>Beneficiary IFSC Code</label>
                    {{viewPr.paymentDetail.beneficiaryIfscCode}}
                </p>
                <p>
                    <label>Payment Type</label>
                    {{viewPr.paymentDetail.paymentType}}
                </p>
                <p>
                    <label>Paid Amount</label>
                    {{viewPr.paymentDetail.paidAmount}}
                </p>
                <p>
                    <label>Payment Date</label>
                    {{viewPr.paymentDetail.paymentDate | date:'dd-MM-yyyy hh:mm:ss a'}}
                </p>
                <p>
                    <label>Remarks</label>
                    {{viewPr.paymentDetail.remarks}}
                </p>
                <p>
                    <label>UTR Number</label>
                    {{viewPr.paymentDetail.utrNumber}}
                </p>
                <p>
                    <label>TDS</label>
                    {{viewPr.paymentDetail.tdsAmount}}
                </p>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn modal-action modal-close red">Close</button>
    </div>
</div>
<!--
<div id="reasonModal" class="modal">
    <div class="modal-content">
        <div class="row">
            <div class="col s12">
                <label>Reason*</label>
                <input type="text" data-ng-model="actionReason" />
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn modal-action modal-close" data-ng-click="submitAction()">Submit</button>
        <button class="btn modal-action modal-close red" style="margin-right: 20px;">Close</button>
    </div>
</div>-->

<div id="invoicePreviewModal" class="modal">
    <div class="modal-content" style="margin-bottom: 0px;">
        <div class="row">
            <div class="col s12">
                <div id="invoicePreview" style="max-height: 370px; overflow: auto;"></div>
            </div>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn modal-action modal-close" style="margin-right: 20px;">Close</button>
    </div>
</div>

<script type="text/ng-template" id="clubSummaryAmountModal.html">
    <div class="modal-header" data-ng-init="init()">
        <h3 class="modal-title" id="modal-title" style="margin-top: 0px;">Summary</h3>
        <hr>
    </div>
    <div class="modal-body" id="modal-body">
        <div class="row">
            <div class="respTable col s12">
                <table class="row table bordered striped" style="border: #ccc 1px solid;">
                    <thead>
                    <tr>

                        <th>Category</th>
                        <th data-ng-if="summaryInvoicesList.type=='GOODS_RECEIVED'">Sub Category</th>
                        <th data-ng-if="summaryInvoicesList.type!='GOODS_RECEIVED'">Budget Category</th>
                        <th>Total Tax</th>
                        <th>Amount</th>
                        <th>Total Amount</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr data-ng-repeat="items in summaryInvoicesList">
                        <td>{{items.category}}</td>
                        <td data-ng-if="summaryInvoicesList.type=='GOODS_RECEIVED'">{{items.subCategory}}</td>
                        <td data-ng-if="summaryInvoicesList.type!='GOODS_RECEIVED'">{{items.budgetCategory}}</td>
                        <td >{{items.tdsRate}}</td>
                        <td >{{items.totalPrice}}</td>
                        <td>{{items.packagingPrice}}</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</script>

<script type="text/ng-template" id="viewPoSo.html">
    <div class="modal-header" data-ng-init="init()">
        <h3 class="modal-title" id="modal-title" style="margin-top: 0px;" data-ng-if="typeReceived.type == 'GOODS_RECEIVED'">List Of PO's</h3>
        <h3 class="modal-title" id="modal-title" style="margin-top: 0px;" data-ng-if="typeReceived.type == 'SERVICE_RECEIVED'">List Of SO's</h3>
        <hr>
    </div>
    <div class="modal-body" id="modal-body">
        <div class="row">
            <div class="col s12">
                <table class="table bordered striped" style="border: #ccc 1px solid;">
                    <thead>
                    <tr data-ng-if="typeReceived.type == 'SERVICE_RECEIVED'">
                        <th>S.No</th>
                        <th>SO Id</th>
                        <th>So Generation Time</th>
                    </tr>
                    <tr data-ng-if="typeReceived.type == 'GOODS_RECEIVED'">
                        <th>S.No</th>
                        <th>PO Id</th>
                        <th>PO Generation Time</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr data-ng-if="typeReceived.type == 'SERVICE_RECEIVED'" data-ng-repeat="item in poSo track by $index">
                        <td>{{$index+1}}</td>
                        <td>{{item.soId}}</td>
                        <td>{{item.soGenerationTime | date:'dd/MM/yyyy hh:mm:ss'}}</td>
                    </tr>
                    <tr data-ng-if="typeReceived.type == 'GOODS_RECEIVED'" data-ng-repeat="item in poSo track by $index">
                        <td>{{$index+1}}</td>
                        <td>{{item.poId}}</td>
                        <td>{{item.poGenerationTime | date:'dd/MM/yyyy hh:mm:ss'}}</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="row">
            <button class="right btn red" data-ng-click="closeModal()">Close</button>
        </div>
    </div>
</script>

<script type="text/ng-template" id="prQueriesModal.html">
    <div class="modal-header" data-ng-init="init()">
        <h3 class="modal-title" id="pr-query-title" style="margin-top: 0px;">Payment request Query ..!</h3>
        <hr>
    </div>
    <div class="modal-body" id="pr-query-body">
        <div class="row">
            <p style="font-weight: bold">Raised By : {{pr.paymentRequestQueries[0].queryRaisedByName}} on {{pr.lastQueriedDate | date: 'yyyy-MM-dd hh:mm:ss'}}</p>
            <p style="font-weight: bold" data-ng-if="pr.paymentRequestQueries[0].queryResolvedByName != null">Resolved By : {{pr.paymentRequestQueries[0].queryResolvedByName}}
                on {{pr.lastQueryResolvedDate | date: 'yyyy-MM-dd hh:mm:ss'}}</p>
        </div>
        <div class="row" data-ng-repeat="query in pr.paymentRequestQueries track by $index">

            <div class="row">
                <p style="font-weight: bold">{{query.paymentDeviationDetail}}</p>
            </div>
            <div class="col s12">
                <div class="col s6">
                    <label>Raised By comment : </label>
                    <textarea data-ng-model="query.raisedByComment" style="resize: none" data-ng-disabled="true"></textarea>
                </div>
                <div class="col s6 center margin-top-20" data-ng-if="isEdit">
                    <button  class="btn btn-small" data-ng-click="uploadQueryDoc(query)">Upload</button>
                </div>
            </div>
            <div class="col s12">
                <div class="col s6">
                    <label>Resolved By Comment : </label>
                    <textarea data-ng-model="query.resolvedByComment" style="resize: none" data-ng-disabled="!isEdit"></textarea>
                </div>
                <div class="col s6 center margin-top-20" data-ng-if="query.documentDetail !=null">
                    <button class="btn btn-small" data-ng-click="downloadQueryDoc(query.documentDetail)">Download</button>
                </div>
            </div>
            <hr>
        </div>
    </div>
    <div class="modal-footer">
        <button class="btn btn-medium red left" data-ng-click="close()">Close</button>
        <button class="btn btn-medium right" data-ng-if="isEdit" data-ng-click="resolveQuery()">Resolve Query</button>
    </div>
    </div>
</script>

<script type="text/ng-template" id="showAssetTagsModal.html">
    <div class="modal-header" data-ng-init="init()">
        <h3 class="modal-title" style="margin-top: 0px;">Asset Tags</h3>
        <hr>
    </div>
    <div class="modal-body">
        <div class="row" data-ng-if="tagIds.length>0" data-ng-repeat="tag in tagIds track by $index">
            <h6>{{tag.toString()}}</h6>
        </div>
        <div class="row" data-ng-if="tagIds.length==0" >
            <h5>No Tags Available</h5>
        </div>
    </div>
    <div class="modal-footer">
        <div class="row">
            <button class="right btn red" data-ng-click="closeAssetModal()">Close</button>
        </div>
    </div>
</script>
