<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<style>
    .popeye-modal{
        width: 80% !important;
    }

</style>
<div class="row white z-depth-3 custom-listing-li" data-ng-init="init()" >
    <div class="col s12">
        <div class="row">
            <div class="col s12 m6 l6">
          <h4>Standalone Asset Transfer Order</h4>
        </div>
        </div>
        
   </div>

    <div class="row">
        <div class="col s12 m6 l6 ">
            <label>Select Unit Type:</label>
            <select  ui-select2 ng-model="selectedCategory"  data-placeholder="Select Category"
                    data-ng-change="changeCategory(selectedCategory)">
                <option value=""></option>
                <option ng-repeat="category in avilableCategories" value="{{category}}" >{{category}}</option>
            </select>
        </div>
        <div class="col s12 m6 l6">
            <label>Select Type:</label>
            <select data-ng-model="selectedType" data-ng-change="validateHandOverDate(selectedType)">
                    <option value="OPEX">OPEX</option>
                    <option value="CAPEX">CAPEX</option>
            </select>
            </div>
        </div>

    <div class="row">
        <div class="col s12 m6 l6">
            <label>Select Receiving Unit:</label>
            <select  ui-select2 ng-model="selectedUnit" data-placeholder="Select Unit"
                    data-ng-change="setReceivingUnit(selectedUnit)">
                <option value=""></option>
                <option ng-repeat="unit in selectedUnitList" value="{{unit}}">{{unit.name}}</option>
            </select>
        </div>
        <div class="col s12 m6 l6">
            <div class="col s6">
                <label>Select Product:</label>
                <select ui-select2 ng-model="selectedProduct" data-placeholder="Enter name of product">
                    <option value=""></option>
                    <option ng-repeat="product in scmProductDetails | filter : byProducts | filter : categoryFilter"
                            value="{{product}}">{{product.productName}}
                    </option>
                </select>
            </div>

            <div class="col s6">
                <label>Select Asset Count:</label>
                <input type="number" name="assetCount" data-ng-model="assetCount"/>
            </div>


            <input type="button" class="btn" value="ADD PRODUCT" data-ng-click="addNewTOItem()" acl-action="TRNSTA"/>
            </div>
        </div>
    <div class="row" data-ng-show="true">
        <dev draggable1 class="col s3">
            <label style="color: whitesmoke;">Focus to scan </label>
            <input id="_draggableInputId" type="text" max-length="6" name="scan" data-ng-model="scannedAssetTagValue"
                   data-ng-change="onScan()" stopEvent>
        </dev>
    </div>

    <div class="row" data-ng-show="TOProducts.length>0">
        <form name="trForm" novalidate>
            <ul class="collection z-depth-1-half" style="margin-bottom: 50px;">
                <li class="collection-item list-head">
                    <div class="row" style="margin-bottom: 0;">
                        <div class="col s3">#</div>
                        <div class="col s3">Product Name</div>
                        <div class="col s3">Transferred Quantity</div>
                        <div class="col s3">Unit Of Measure</div>
                    </div>
                </li>
                <li style="margin-bottom: 10px; border:#ddd 1px solid;" data-ng-repeat="item in TOProducts track by $index">
                    <div class="row" style="padding: 10px; background: #eee;border-bottom: #ddd 1px solid;">
                        <div class="col s3">{{$index+1}}</div>
                        <div class="col s3"><a data-ng-click="showPreview($event, item.productId,'PRODUCT')">{{item.productName}}</a>
                                                </div>
                        <div class="col s3">{{item.transferredQuantity==null?0:item.transferredQuantity}}</div>
                        <div class="col s3">{{item.unitOfMeasure}}</div>
                        </div>
                    <div class="row">
                        <div class="col s5">
                            <label>Select SKU</label>
                            <select data-ng-model="item.selectedSku"
                                    data-ng-change="onSkuChanged(item)"
                                    data-ng-options="sku as sku.skuName for sku in item.skuList track by sku.skuId"></select>
                    </div>
                        <div class="col s5">
                            <label>Select Packaging:</label>
                            <select data-ng-model="item.selectedPackaging"
                                    data-ng-options="packaging as packaging.packagingDefinition.packagingName group by packaging.packagingDefinition.packagingType for packaging
                                     in item.selectedSku.skuPackagings | orderBy: 'packagingDefinition.packagingType' track by packaging.packagingId"
                                    data-ng-init="item.selectedPackaging=item.selectedSku.skuPackagings[0]"></select>
                                  </div>
                        <div class="col s2">
                            <input type="button" value="Update" class="btn" data-ng-click="addPackaging(item)"
                                   style="margin-top:20px;"/>
                            <input type="button" value="Remove" class="btn red"
                                   data-ng-click="TOProducts.splice($index, 1)" style="margin-top:20px;"/>
                                          </div>
                                              </div>
                    <div class="row" data-ng-repeat="trItem in item.trPackaging track by $index">
                        <div class="col s10">
                            <div class="row">
                                <div class="col s6"><a data-ng-click="showPreview($event, trItem.skuId,'SKU')">{{trItem.skuName}}</a>
                                          </div>
                                <div class="col s6">Transferred Qty: {{trItem.transferredQuantity}} &nbsp;
                                    {{trItem.unitOfMeasure}}
                                </div>
                            </div>
                            <div class="row" data-ng-repeat="pgd in trItem.packagingDetails track by $index">
                                <div data-ng-if="!addBillBookDetails[item.productId]">
                                    <div class="col s2">{{pgd.packagingDefinitionData.packagingName}}</div>
                                    <div class="col s2">
                                        <label>Units Packed:</label>
                                        <div data-ng-if="!isManualBook[item.productId]">
                                            <input type="number" name="pkgQty[$index]" data-ng-disabled="true"
                                                   data-ng-model="pgd.numberOfUnitsPacked"
                                                   ng-change="updatePackagingQty(pgd,trItem,item)" required/>
                                        </div>
                                        <div data-ng-if="isManualBook[item.productId]">
                                            <span name="pkgQty[$index]"
                                                  data-ng-init="updatePackagingQty(pgd,trItem,item)">{{pgd.numberOfUnitsPacked}}</span>
                                        </div>
                                        <p ng-show="trForm.pkgQty[$index].$error.required" class="errorMessage">Please
                                            enter valid quantity.</p>
                                    </div>
                                    <div class="col s2"><label>Transferred Qty:</label> {{pgd.transferredQuantity}}
                                    </div>
                                    <div class="col s2"><label>Unit Of Measure:</label>
                                        {{pgd.packagingDefinitionData.unitOfMeasure}}
                                    </div>
                                    <div class="col s2">
                                        <button class="btn btn-small"
                                                data-ng-click="removePackaging(trItem,$index, item)">Remove
                                        </button>
                                    </div>
                                    <div class="col s2">
                                        <button class="btn btn-small" data-ng-if="item.productId==100217 && isWarehouse"
                                                data-ng-init="manualDetailsRequired()"
                                                data-ng-click="fillManualBBDetails(trItem,$index, item)"
                                                data-ng-class="{true:'red', false:'green'}[isBillBooksDetailFilled]">Add
                                            Details
                                        </button>
                                    </div>
                                </div>
                                <div class="row data-division" data-ng-if="hasDrillDown(trItem)">
                                    <div class="row data-expiry">
                                        Expiry Info
                                      </div>
                                    <div class="row" style="font-weight: bold;">
                                        <div class="col s3">
                                            Quantity
                                  </div>
                                        <div class="col s3">
                                            Expiry Date
                              </div>

                          </div>
                                    <div class="row" data-ng-repeat="drilldown in trItem.drillDowns track by $index">
                                        <div class="col s3">
                                            {{drilldown.quantity}}
                    </div>
                                        <div class="col s3 data-date">
                                            {{drilldown.expiryDate| date: 'yyyy-MM-dd HH:mm:ss'}}
            </div>
                </div>
                </div>
                                <div data-ng-if="addBillBookDetails[item.productId]"
                                     data-ng-include="'views/ManualBillBookDetail.html'"></div>
            </div>
    </div>
                        <div class="col s2">
                            <ng-form name="innerForm">
                                <label>Asset Tag Value:</label>
                                <div>
                                    <input type="text"

                                           name="assetTag"
                                           data-ng-model="item.associatedAssetTagValue"
                                           data-ng-minlength="0"
                                           data-ng-maxlength="6"
                                           ng-change="validateAssetTagValue(item.associatedAssetTagValue,item)"
                                           required/>
                                    <p ng-show="innerForm.assetTag.$error.required" class="errorMessage">Asset Tag Value
                                        is required.</p>
                                    <p ng-show="innerForm.assetTag.$error.maxlength" class="errorMessage">Asset Tag
                                        Value is too large.</p>
                                    <p ng-show="innerForm.assetTag.$error.min" class="errorMessage">Asset Tag Value is
                                        too small.</p>
                                    <div data-ng-if="item.assetValidated == null || !item.assetValidated">
                                        <p class="errorMessage">Enter Valid Asset Tag Value.</p>
        </div>
    </div>
                            </ng-form>
</div>
    </div>

                </li>
            </ul>
            <div class="row">
                <div class="col s9 form-element">
                    <label>Comment(optional):</label>
                    <textarea data-ng-model="transferOrderDetail.comment"></textarea>
                </div>
                <div class="col s3 form-element">

                    <label for="toType">Select Transfer Order type:</label>
                    <select id="toType" class="form-control" name="toType" ng-model="transferOrderDetail.toType"
                            ng-options="toType as toType for toType in toTypeList " required></select>
                    <p ng-show="trForm.toType.$error.required" class="errorMessage">Asset Tag Value is required.</p>

                </div>
                <div class="col s12 form-element">
                    <input type="button" data-ng-click="showPendingRequest=true" class="btn" value="Back"/>
                    <input type="button" data-ng-if="trForm.$valid" data-ng-click="startTransferProcess()"
                           class="btn right" value="Submit" acl-action="TRNSTA"/>
                </div>
        </div>
        </form>
    </div>
    </div>

<script type="text/ng-template" id="budgetViewModal.html">
    <div class="modal-header">
        <h3 class="modal-title" id="modal-title" style="margin-top: 0px;">Summary</h3>
        <hr>
    </div>
    <div class="modal-body" id="modal-body">
        <div class="row">
            <div class="col s12">
                <div data-ng-if="emptyCheck">
                    <h5>No Budget Found For Fixed Assets(FA_Equipment) Department.Please Upload the Budget and Try Again.</h5>
                    <br>
                    <div class="row">
                        <button class="btn red pull-right"  data-ng-click="cancel()">Close</button>
                    </div>
                </div>
                <br>
            </div>
            <div class="col s12" data-ng-if="!emptyCheck">
                <table class="table bordered striped" style="border: #ccc 1px solid;">
                    <thead>
                    <tr>
                        <th>Department Name</th>
                        <th>Total Amount</th>
                        <th>Original Amount</th>
                        <th>Budget Amount</th>
                        <th>Remaining Amount</th>
                        <th>Running Amount</th>
                        <th>Receiving Amount</th>
                        <th>Paid Amount</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr data-ng-class="{'red': transferOrderDetail.totalCost > budgetDetails.remainingAmount}">
                        <td style="color: black;">{{budgetDetails.departmentName}}</td>
                        <td style="color: black;">{{transferOrderDetail.totalCost.toFixed(2)}}</td>
                        <td style="color: black;">{{budgetDetails.originalAmount.toFixed(2)}}</td>
                        <td style="color: black;">{{budgetDetails.budgetAmount.toFixed(2)}}</td>
                        <td style="color: black;">{{budgetDetails.remainingAmount.toFixed(2)}}</td>
                        <td style="color: black;">{{budgetDetails.runningAmount.toFixed(2)}}</td>
                        <td style="color: black;">{{budgetDetails.receivingAmount.toFixed(2)}}</td>
                        <td style="color: black;">{{budgetDetails.paidAmount.toFixed(2)}}</td>
                    </tr>
                    </tbody>
                </table>
                <br>
                <div class="row">
                    <button class="btn red"  data-ng-click="cancel()">Cancel</button>
                    <button class="btn green" style="float: right;" data-ng-click="submit()">Submit</button>
                </div>
            </div>
        </div>

    </div>
</script>
