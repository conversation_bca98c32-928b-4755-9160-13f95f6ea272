<style>
    .custom-modal{
        width: 70% !important;
    }
    @media screen and (max-width: 991px) {
        .searchingCard {
            display: flex;
            flex-direction: column;
            margin:0px;
            width:100% !important;
        }
        .searchingCard .col {
            margin-left:0px !important;
            margin-right: 0px !important;
            width:100% !important;
        }

        .searchingCard .btn {
            width:100% !important;
        }

        .custom-modal{
            width: 90% !important;
        }

    }
</style>
<div class="row" data-ng-init="init()">
    <div class="col s12">
        <div class="row white z-depth-3">
            <div class="col s12">
                <h4>View Service Receiving</h4>
            </div>
            <div class="searchingCard col s12 margin-bottom-15">
                <div class="col s2">
                    <label>Select Start date</label>
                    <input input-date type="text" ng-model="startDate" container="" format="yyyy-mm-dd" />
                </div>
                <div class="col s2">
                    <label>Select End date</label>
                    <input input-date type="text" ng-model="endDate" container="" format="yyyy-mm-dd" />
                </div>
                <div class="col s2">
                    <label>Select Vendor</label>
                    <select id="vendors" ui-select2="{allowClear:true, placeholder: 'Select Vendor'}"
                            ng-options="vendor as vendor.name for vendor in vendors"
                            data-ng-change="selectVendor(vendorSelected)" data-ng-model="vendorSelected"></select>
                </div>
                <div class="col s2">
                    <label>Business Cost Center</label>
                    <select id="bcc" ui-select2="{allowClear:true, placeholder: 'Select Business Cost Center'}"
                            ng-options="bcc as bcc.name for bcc in bcc"
                            data-ng-change="selectBcc(bccSelected)" data-ng-model="bccSelected"></select>
                </div>
                <div class="col s2">
                    <label>Dispatch Location</label>
                    <select id="locations" ui-select2="{allowClear:true, placeholder: 'Select Dispatch Location'}"
                            ng-options="location as location.name for location in locationList"
                            data-ng-change="selectDispatchLocation(locationSelected)" data-ng-model="locationSelected"></select>
                </div>
                <div class="col s1">
                    <label>Service Received Id</label>
                    <input type="number" placeholder="SR ID" name="srId" id="srId" ng-model="srId"/>
                </div>
                <div class="col s1">
                    <button class="btn btn-small margin-top-20" data-ng-click="getSRsShort()">Find</button>
                </div>
            </div>
            <hr>
            <div class="col s12" data-ng-if="srRequestShort.length==0" style="padding:30px;color:gray;font-size: 20px;">
                <div class="row margin0 center">
                    No Service Receiving found for the selected criteria
                </div>
            </div>
            <div class="col s12 respTable standardView" ng-if="srRequestShort.length>0">
                <div class="row">
                    <ul class="collection striped center" >
                        <li class="collection-item list-head">
                            <div class="row" style="font-size:12px">
                                <div class="col s1">ID</div>
                                <div class="col s1">Generation Time</div>
                                <div class="col s1">SO ID(s)</div>
                                <div class="col s1">BCC</div>
                                <div class="col s1">Vendor Name</div>
                                <div class="col s1">Dispatch From</div>
                                <div class="col s2">Deliver To</div>
                                <div class="col s1">Total Amount</div>
                                <div class="col s1">Total Tax</div>
                                <!--<div class="col s1">Total Tax</div>-->
                                <div class="col s1">Status</div>
                                <div class="col s1">Type</div>
                                <div class="col s1" align="center">Actions</div>
                            </div>
                        </li>
                        <li class="collection-item " style="padding:5px;" data-ng-repeat="soR in srRequestShort track by $index">
                            <div class="row margin0" data-ng-class="{'red white-text': soR.id==createdPO}" style="font-size:12px;">
                                <div class="col s1 underline pointer" data-ng-click = "viewDetailSR(soR)">{{soR.id}}</div>
                                <div class="col s1">{{soR.creationTime | date :'dd-MM-yyyy @ h:mma'}}</div>
                                <div class="col s1">[<span data-ng-repeat="so in soR.serviceOrderList">{{so.id}}</span>]</div>
                                <div class="col s1" data-ng-if="soR.serviceOrderList.length > 1"><span>Multiple [{{soR.serviceOrderList.length}}</span>]</div>
                                <div class="col s1" data-ng-if="soR.serviceOrderList.length === 1">
                                    <span data-ng-if="soR.serviceOrderList[0].orderItems.length > 1">Multiple [{{soR.serviceOrderList[0].orderItems.length}}]</span>
                                    <span data-ng-if="soR.serviceOrderList[0].orderItems.length === 1">{{soR.serviceOrderList[0].orderItems[0].businessCostCenterName}}</span>
                                </div>
                                <div class="col s1">{{soR.vendor.name}}</div>
                                <div class="col s1">{{soR.location.name}}</div>
                                <div class="col s2">{{soR.company.name}} [{{soR.deliveryState.name}}]</div>
                                <div class="col s1">{{soR.totalAmount.toFixed(2)}}</div>
                                <div class="col s1">{{soR.totalTaxes.toFixed(2)}}</div>
                                <div class="col s1">{{soR.status}}</div>
                                <div class="col s1">{{soR.type!=null ? soR.type : "regular"}}</div>
                                <div class="col s1">
                                    <button data-ng-if="(soR.status=='CREATED' || soR.status=='PROVISIONAL' ) && soR.paymentRequestId==null"
                                            class="btn btn-xs-small margin-right-5 " style="width: 70px; !important;"
                                            acl-action="SRCNCL" data-ng-click="cancel(soR.id,$index)">Cancel</button>

                                    <button class="btn btn-xs-small  margin-right-5 margin-top-5"
                                            style="width: 70px; !important;"
                                            data-ng-click="printSRWithCallBack(soR)">Print</button>
                                    <button id="printDiv" print-btn class="btn" data-ng-show="false">Print</button>

                                    <button data-ng-if="soR.status=='PROVISIONAL' && soR.paymentRequestId==null"
                                            class="btn btn-xs-small  margin-top-5 " style="width: 70px; !important;" acl-action="SRAPPRV"
                                            data-ng-click="viewDetailSR(soR)">APPROVE</button>
                                    <button data-ng-if="soR.type=='PROVISIONAL' && soR.paymentRequestId==null"
                                            class="btn btn-xs-small  margin-top-5"  acl-action="SRAPPRV"
                                            data-ng-click="emailPopup(soR.id)">Send Email</button>

                                </div>
                            </div>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="TableMobileView" ng-if="srRequestShort.length>0">
                <ul class="collection striped center">
                    <li class="collection-item" data-ng-repeat="soR in srRequestShort track by $index"
                        data-ng-class="{'red white-text': soR.id==createdPO}">
                        <div class="row">
                            <div class="col">ID</div>
                            <div class="col underline pointer" data-ng-click="viewDetailSR(soR)">{{soR.id}}</div>
                        </div>
                        <div class="row">
                            <div class="col">Generation Time</div>
                            <div class="col">{{soR.creationTime | date :'dd-MM-yyyy @ h:mma'}}</div>
                        </div>
                        <div class="row">
                            <div class="col">SO ID(s)</div>
                            <div class="col">[<span data-ng-repeat="soId in soR.serviceOrderList">{{soId}}</span>]</div>
                        </div>
                        <div class="row">
                            <div class="col">Vendor Name</div>
                            <div class="col">{{soR.vendor.name}}</div>
                        </div>
                        <div class="row">
                            <div class="col">Dispatch From</div>
                            <div class="col">{{soR.location.name}}</div>
                        </div>
                        <div class="row">
                            <div class="col">Deliver To</div>
                            <div class="col">{{soR.company.name}} [{{soR.deliveryState.name}}]</div>
                        </div>
                        <div class="row">
                            <div class="col">Total Amount</div>
                            <div class="col">{{soR.totalAmount.toFixed(2)}}</div>
                        </div>
                        <div class="row">
                            <div class="col">Total Tax</div>
                            <div class="col">{{soR.totalTaxes.toFixed(2)}}</div>
                        </div>
                        <div class="row">
                            <div class="col">Status</div>
                            <div class="col">{{soR.status}}</div>
                        </div>
                        <div class="row">
                            <div class="col" align="center">Actions</div>
                            <div class="col">
                                <button data-ng-if="(soR.status=='CREATED' || soR.status=='PROVISIONAL' ) && soR.paymentRequestId==null"
                                        class="btn btn-xs-small margin-right-5" style="width: 70px; !important;" acl-action="SRCNCL"
                                        data-ng-click="cancel(soR.id,$index)">Cancel</button>
                                <button data-ng-if="soR.status=='PROVISIONAL' && soR.paymentRequestId==null"
                                        class="btn btn-xs-small margin-right-5" style="width: 70px; !important;" acl-action="SRAPPRV"
                                        data-ng-click="approveSR(soR.id)">APPROVE</button>

                                <button class="btn btn-xs-small margin-right-5 margin-top-10" style="width: 70px; !important;"
                                        data-ng-click="printSRWithCallBack(soR)">Print</button>
                                <button id="printDiv" print-btn class="btn" data-ng-show="false">Print</button>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</div>


<div style="width:100%" id="printSection">
    <div class="col s12">
        <div class="row" style="margin-bottom: 5px;">
            <div class="col s12">
                <p style="text-align: center;"><b><span
                        style="font-size: 16.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">CHAAYOS<br />
					</span></b><b><span
                        style="font-size: 13.0pt; line-height: 115%; font-family: 'Cambria', serif;">{{currentPrintSR.company.name}}<br />
					</span></b><span
                        style="font-size: 10.0pt; line-height: 115%; font-family: 'Cambria', serif; color: black;">
                        {{currentPrintSR.companyAddress.line1}},
                        {{currentPrintSR.companyAddress.line2}}, <br /> {{currentPrintSR.companyAddress.city}},
						{{currentPrintSR.companyAddress.state}}, <br /> {{currentPrintSR.companyAddress.country}},
						{{currentPrintSR.companyAddress.zipCode}}<br />
					</span><b><span style="font-size: 13.0pt; line-height: 115%; font-family: 'Cambria', serif;">Service Receipt (SR)</span></b>
                </p>
            </div>
        </div>
        <div id="expandedPOView" class="row custom-listing-li" style="padding:5px;">
            <div class="col s12" data-ng-if="currentPrintSR.serviceReceiveItems!=null">
                <table style="margin-bottom: 20px;">
                    <thead class="itemsTable">
                    <tr>
                        <th style="width: 20%">Vendor Details</th>
                        <th style="width: 30%"></th>
                        <th style="width: 20%">Dispatch Details</th>
                        <th style="width: 30%"></th>
                    </tr>
                    </thead>
                    <tbody class="itemsTable borderLess" style="border: #000 1px solid;">
                    <tr>
                        <td>Vendor Name:</td>
                        <td>{{currentPrintSR.vendor.name}}</td>
                        <td>Dispatch Location</td>
                        <td>{{currentPrintSR.location.name}}</td>
                    </tr>
                    <tr>
                        <td>Vendor Id:</td>
                        <td>{{currentPrintSR.vendor.id}}</td>
                        <td>Dispatch Address</td>
                        <td>{{currentPrintSR.dispatchAddress.line1}}, {{currentPrintSR.dispatchAddress.line2}}, {{currentPrintSR.dispatchAddress.city}},
                            {{currentPrintSR.dispatchAddress.state}}, {{currentPrintSR.dispatchAddress.country}}, {{currentPrintSR.dispatchAddress.zipcode}}</td>
                    </tr>
                    <tr>
                        <td>Date of SR:</td>
                        <td>{{currentPrintSR.creationTime | date:'dd-MM-yyyy hh:mm:ss':'+0530'}}</td>
                        <td>Dispatch State</td>
                        <td>{{currentPrintSR.dispatchAddress.state}}</td>
                    </tr>
                    <tr>
                        <td>SR No.:</td>
                        <td>{{currentPrintSR.id}}</td>
                        <td>Dispatch State Code</td>
                        <td>{{currentPrintSR.dispatchAddress.stateCode}}</td>
                    </tr>
                    </tbody>
                </table>
                <table style="margin-bottom: 20px;">
                    <thead class="itemsTable">
                    <tr>
                        <th style="width: 20%">Receiving Company Name</th>
                        <th style="width: 80%">{{currentPrintSR.company.name}}</th>
                    </tr>
                    </thead>
                    <tbody class="itemsTable borderLess" style="border: #000 1px solid;">
                    <tr>
                        <td>Received By:</td>
                        <td>{{currentPrintSR.createdBy.name}}</td>
                    </tr>
                    <tr>
                        <td>Receiving Time:</td>
                        <td>{{currentPrintSR.creationTime | date:'dd-MM-yyyy hh:mm:ss':'+0530'}}</td>
                    </tr>
                    <tr>
                        <td>Delivery State:</td>
                        <td>{{currentPrintSR.deliveryState.name}}</td>
                    </tr>
                    <tr>
                        <td>Delivery State Code:</td>
                        <td>{{currentPrintSR.deliveryState.code}}</td>
                    </tr>
                    </tbody>
                </table>
                <h5 style="margin-top:0px;font-size: 18px;">List of Elements Received</h5>
                <div class="row margin0 itemsTable">
                    <table class="bordered" style="border-top: 1px solid #d0d0d0;">
                        <thead>
                        <tr>
                            <th class="center-align">Element ID</th>
                            <th class="center-align">Element Name</th>
                            <th class="center-align">Cost Center</th>
                            <th class="center-align">Description</th>
                            <th class="center-align">Price</th>
                            <th class="center-align">Qty</th>
                            <th class="center-align">Total</th>
                            <th class="center-align">Taxes</th>
                            <th class="center-align">Amount</th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr data-ng-repeat="item in currentPrintSR.serviceReceiveItems track by $index">
                            <td class="center-align">{{item.costElementId}}</td>
                            <td class="center-align">{{item.costElementName}} [{{item.ascCode}}]</td>
                            <td class="center-align">{{item.businessCostCenterName}}</td>
                            <td class="center-align">{{item.serviceDescription}}</td>
                            <td class="center-align">{{item.unitPrice}}</td>
                            <td class="center-align">{{item.receivedQuantity}}</td>
                            <td class="center-align">{{item.totalCost}}</td>
                            <td class="center-align">
                                <span>{{item.totalTax}}</span>
                                (<span ng-repeat="tax in item.taxes">{{tax.taxName}}@{{tax.percentage}}%</span>)
                            </td>
                            <td class="center-align">{{item.totalAmount}}</td>
                        </tr>
                        <tr>
                            <th></th>
                            <th><b>Total</b></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th class="center-align">{{currentPrintSR.totalAmount}}</th>
                        </tr>
                        <tr>
                            <th>Created By</th>
                            <th>{{currentPrintSR.createdBy.name}}</th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <div class="col s12">
                <div class="row margin0">
                    <ul class="col s12">
                        <h5 style="margin-top:0px;font-size: 18px;">List of Attached Service Orders</h5>
                        <li class="row margin0" data-ng-repeat="so in currentPrintSR.serviceOrderList track by so.id">
                            <div class="poNumber">
                                {{so.id}}
                                <span style="margin-left: 50px;font-weight:bold;">Date: {{so.generationTime | date:'dd-MM-yyyy hh:mm:ss':'+0530'}}</span>
                                <span class="chip right">{{so.status}}</span>
                            </div>
                            <table class="bordered itemsTable" style="margin-bottom: 20px;">
                                <thead>
                                <tr>
                                    <th class="center-align">Cost Element ID</th>
                                    <th class="center-align">Cost Element Name</th>
                                    <th class="center-align">UOM</th>
                                    <th class="center-align">Pending</th>
                                    <th class="center-align">Received</th>
                                </tr>
                                </thead>
                                <tbody>
                                <tr data-ng-repeat="item in so.orderItems | filter : filteredByReceivedQuantity track by $index ">
                                    <td>{{item.costElementId}}</td>
                                    <td class="">{{item.costElementName}} [{{item.ascCode}}]</td>
                                    <td class="center-align">{{item.unitOfMeasure}}</td>
                                    <td class="center-align">
                                        {{(item.requestedQuantity - item.receivedQuantity).toFixed(6)}}
                                    </td>
                                    <td class="center-align">
                                        {{item.receivedQuantity!=null ? item.receivedQuantity.toFixed(6) : 0}}
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </li>
                    </ul>
                </div>
            </div>
            <p style="font-size:12px;">Certified that the particulars and the amount indicated given above are true and correct.</p>
            <div style="width: 250px;border:#000 1px solid;float:right;">
                <div style="height:150px"></div>
                <div style="border-top:#000 1px solid;text-align:center;">Authorised Signatory</div>
            </div>
        </div>
    </div>
</div>

<script type="text/ng-template" id="vendorMbEmail.html">
<div class="modal-header" data-ng-init="init()">
    <h3 class="modal-title" id="modal-title" style="margin-top: 0px;">Vendor Measurement Book Email</h3>
    <div>Email will be sent to <b>Vendor Email</b> and Your Email (<b>{{userEmail}}</b>) by Default</div>
    <hr>
</div>
<div class="modal-body" id="modal-body">
    <div class="row">
        <div class="row">
            <h5 style="text-align-last: center;"></h5>
            <div class="row standardView" style="max-height: 500px; overflow: auto;">
                <table class="row white z-depth-3">
                    <!--<b class="col s-12 align-center" data-ng-show="srItem!=null">Measurement Book Details :</b>-->
                    <thead>
                    <tr>
                        <th>Add To Emails</th>
                        <th></th>
                        <th>Add CC Emails</th>
                        <th></th>

                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td><input data-ng-model="enteredToEmail"  type="email"></td>
                        <td><button data-ng-click="addToEmail()">Add</button></td>
                        <td ><input data-ng-model="enteredCcEmail"  type="email"></td>
                        <td><button data-ng-click="addCcEmail()" >Add</button></td>
                    </tr>
                    </tbody>


                </table>
                <div data-ng-show="toEmails.length>0 || ccEmails.length >0">
                   <div class="row" >
                       <div class="col"><b>To Emails : </b></div>
                       <div class="col blue-grey z-depth-3" style="margin: 5px;" data-ng-repeat="email in toEmails" >{{email}} <a class="btn-floating btn-xs-small waves-effect  red" data-ng-click="removeToEmail(email)"><i class="material-icons">cancel</i></a>  </div>
                  </div>
                  <div class="row">
                      <div class="col "><b>CC Emails :</b></div>
                      <div class="col blue-grey z-depth-3" style="margin: 5px;" data-ng-repeat="email in ccEmails">{{email}} <a class="btn-floating btn-xs-small waves-effect  red" data-ng-click="removeCcEmail(email)"><i class="material-icons">cancel</i></a> </div>
                  </div>
                </div>
                <div>
                    <button class=" btn btn-medium btn-right" data-ng-click="sendMbEmail()">Send Email</button>
                </div>

            </div>
        </div>
    </div>
</div>
</script>

