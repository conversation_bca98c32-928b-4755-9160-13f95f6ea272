<style>
    .custom-modal{
        width: 50% !important;
    }
    .popeye-modal-container .popeye-modal{
    width: 50%
    }
</style>
<div
	class="row white z-depth-3 custom-listing-li"
	data-ng-init="init()">
	<div class="col s12">
		<h4>Cost Element</h4>
	</div>
	<div Class="col s12">
		<div Class="row" style="float: right;margin-top: -35px;">
			<div class="col s12">
	                        <input id="archived" data-ng-model="archived" type="checkbox" data-ng-click="archiveData()"/>
	                        <label class="black-text " for="archived">Archived Data</label>
			</div>
		</div>
	</div>
</div>
<div class="row">
    <div class="col-lg-4">
    	Search :
    	<input type="text" ng-model="search" ng-change="filter()" placeholder="Filter" class="form-control" />
    </div>
</div>
<ul class="collection striped" data-ng-show="allCostElements.length>0">
	<li class="collection-item list-head">
	    <div class="row">
	        <div class="col s1">Id</div>
	        <div class="col s1">Code(Tax)</div>
	        <div class="col s2">Name</div>
	        <div class="col s2">Category</div>
	        <div class="col s1">Sub Category</div>
	        <div class="col s2">Department</div>
	        <div class="col s1">Division</div>
	        <div class="col s1">Is Editable</div>
	        <div class="col s1">Status</div>
	    </div>
	</li>
      <li class="collection-item clickable"
                    data-ng-repeat="ce in allCostElements  | filter:search | orderBy : 'id'">
         <div class="row" style="margin-bottom: 0px;">
			<div class="col s1 underline pointer" data-ng-click="viewDetailCost(ce)"> {{ce.id}}</div>
			<div class="col s1" style="word-break: break-all;"> {{ce.code == null || ce.code == "" ? "NA" : ce.code }}({{ce.taxRate == null || ce.taxRate == "" ? "NA" : ce.taxRate+"%"}})</div>
			<div class="col s2" style="word-break: break-word;"> {{ce.name == null || ce.name == "" ? "NA" : ce.name}}</div>
			<div class="col s2" style="word-break: break-word;"> {{ce.category.name == null || ce.category.name == "" ? "NA" : ce.category.name}}</div>
			<div class="col s1" style="word-break: break-word;"> {{ce.subCategory.name == null || ce.subCategory.name == "" ? "NA" : ce.subCategory.name}}</div>
			<div class="col s2" style="word-break: break-word;"> {{ce.department.name == null || ce.department.name  == "" ? "NA" : ce.department.name }}</div>
			<div class="col s1" style="word-break: break-word;"> {{ce.division.name == null || ce.division.name == "" ? "NA" : ce.division.name}}</div>
			<div class="col s1"> {{ce.isPriceUpdate == null || ce.isPriceUpdate == "" ? "NA" : ce.isPriceUpdate}}</div>
			<div class="col s1"> {{ce.status == null || ce.status == "" ? "NA" : ce.status}}</div>
          </div>
     </li>
</ul>

<div
	id="addNewCostElement"
	class="modal modal-mx">
	<form
		id="basicDetail"
		name="basicDetailsForm"
		class="white z-depth-3 scm-form"
		novalidate>
		<div class="modal-content">
			<div class="row">
				<div class="col s12 card-panel teal lighten-2">
					<h5 class="white-text center">Add Cost Element</h5>
				</div>
			</div>
		</div>
		<div class="row">
			<div class="col s12 m12 l3 xl3">
				<label class="black-text">Cost Center Name</label>
			</div>
			<div class="col s12 m12 l9 xl9">
				<select
					ui-select2
					style="width: 100%;"
					id="costCenterSelector"
					data-ng-model="costCenterCreateVO.costCenterId"
					data-ng-options="o.id as o.name for o in allCostCenters"
					required></select>
			</div>
		</div>
		<div class="row">
			<div class="col s12 m12 l3 xl3">
				<label class="black-text">Cost Element Name</label>
			</div>
			<div class="col s12 m12 l9 xl9">
				<input
					type="text"
					data-ng-model="costCenterCreateVO.costElement.name"
					required></input>
			</div>
		</div>
		<div class="row">
			<div class="col s12 m12 l3 xl3">
				<label class="black-text">SAC Code</label>
			</div>
			<div class="col s12 m12 l9 xl9">
				<input
					type="text"
					onkeyup="this.value = this.value.toUpperCase()"
					style='text-transform:uppercase'
					data-ng-model="costCenterCreateVO.costElement.code"
					required></input>
			</div>
		</div>
		
		<div class="row">
			<div class="col s12 m12 l3 xl3">
				<label class="black-text">Description</label>
			</div>
			<div class="col s12 m12 l9 xl9">
				<input
					type="text"
					data-ng-model="costCenterCreateVO.costElement.description"
					required></input>
			</div>
		</div>
		<div class="row">
			<div class="col s12 m12 l3 xl3">
				<label class="black-text">Tax Rate</label>
			</div>
			<div class="col s12 m12 l9 xl9">
				<input
					type="number"
					min="0" 
					data-ng-model="costCenterCreateVO.costElement.taxRate"
					required></input>
			</div>
		</div>
		<div class="row">
			<div class="col s12 m12 l3 xl3">
				<label class="black-text">Unit Of Measure</label>
			</div>
			<div class="col s12 m12 l9 xl9">
				<select data-ng-model="costCenterCreateVO.costElement.uoms" ui-select2 style="width:100%"
				  data-ng-options="uom as uom for uom in uomMetadata" multiple="multiple" required></select>
			</div>
		</div>
		<div class="modal-footer">
			<div class="row">
				<div class="col s12">
					<button
						class="modal-action waves-effect waves-green btn right"
						data-ng-class="{'disabled':!basicDetailsForm.$valid}"
						style="margin-right: 10px"
						data-ng-click="createCostElement()"
						data-ng-disabled="!basicDetailsForm.$valid">Submit</button>
					<button
						class="modal-action modal-close waves-effect waves-green btn right"
						style="margin-right: 10px"
						data-ng-click="clearCostElement()">Cancel</button>
				</div>
			</div>
		</div>
	</form>
</div>
<script type="text/ng-template" id="viewDetailCostElmnt.html">
<div class="modal-body" id="modal-body" data-ng-init="init()">
	<div class="row">
		
		<div class="col s12" style="margin-top: 10px;">
			<div class="row">
				<div class="col s12 m12 l12">
					<div class="row">
						<div class="col s12">
							<label>Name</label> <input type="text" 
								ng-model="name">
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="col s12">
			<div class="row">
				<div class="col s12 m12 l12">
					<div class="row">
						<div class="col s12">
							<label class="black-text active" for="taxCategory">Tax Category</label>
                        <select id="taxCategory" name="taxCategory" data-ng-model="taxCategory"
                                data-ng-options="tax as (tax.code + '-'+ tax.desc) for tax in taxCodes track by tax.code">
                        </select>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="col s12">
			<div class="row">
				<div class="col s12 m12 l12">
					<div class="row">
						<div class="col s12">
							<label>Description</label> <textarea data-ng-model="description" name="description" data-ng-model="cc" data-ng-maxlength="500" ></textarea>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="col s12">
			<div class="row">
				<div class="col s12 m12 l12">
					<div class="row">
						<div class="col s12">
							<label>IsPrice Editable</label> <select
								data-ng-model="selectedIsPrice"
								data-ng-options="capex as capex for capex in capexs"></select>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="col s12">
			<div class="row">
				<div class="col s12 m12 l12">
					<div class="row">
						<div class="col s12">
							<label>Capex</label> <select
								data-ng-model="selectedCapex"
								data-ng-options="capex as capex for capex in capexs"></select>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="col s12">
			<div class="row">
				<div class="col s12 m12 l12">
					<div class="row">
						<div class="col s12">
							<label>Status</label> <select ng-model="status"
								data-ng-options="period as period for period in periods"></select>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="col s12">
			<div class="row">
				<div class="col s12 m12 l12">
					<div class="row">
						<div class="col s12">
							<label class="black-text active" >Division</label>
							<select id="selectedDivision" name="selectedDivision" data-ng-model="selectedDivision"
									data-ng-options="division as division.name for division in divisions track by division.listDetailId">
							</select>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="col s12">
			<div class="row">
				<div class="col s12 m12 l12">
					<div class="row">
						<div class="col s12">
							<label class="black-text active">Department</label>
							<select id="selectedDepartment" name="selectedDepartment" data-ng-model="selectedDepartment" data-ng-disabled="true"
									data-ng-options="department as department.name for department in departments track by department.listDetailId">
							</select>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="col s12">
			<div class="row">
				<div class="col s12 m12 l12">
					<div class="row">
						<div class="col s12">
							<label class="black-text active">Classification</label>
							<select id="selectedCategory" name="selectCategory" data-ng-model="selectedCategory"
									data-ng-change="selectCategory(selectedCategory)"
									data-ng-options="category as category.name for category in categories track by category.listDetailId">
							</select>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="col s12">
			<div class="row">
				<div class="col s12 m12 l12">
					<div class="row">
						<div class="col s12">
							<label class="black-text active">Sub Classification</label>
							<select id="selectedSubCategory" name="selectedSubCategory" data-ng-model="selectedSubCategory"
									data-ng-change="selectSubCategory(selectedSubCategory)"
									data-ng-options="subCategory as subCategory.name for subCategory in subCategories track by subCategory.listTypeId">
							</select>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="col s12">
			<div class="row">
				<div class="col s12 m12 l12">
					<div class="row">
						<div class="col s12">
							<label class="black-text active">Sub Sub Classification</label>
							<select id="selectedSubSubCategory" name="selectedSubSubCategory" data-ng-model="selectedSubSubCategory"
									data-ng-options="subSubCategory as subSubCategory.name for subSubCategory in subSubCategories track by subSubCategory.listDataId">
							</select>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>
<div class="modal-footer">
	<button class="btn btn-primary" type="button"
		ng-click="saveStatus()" data-ng-if="showErrors != true">Submit</button>
	<button class="btn btn-warning" type="button" ng-click="cancel()">Cancel</button>
</div>
</script>