<style>
    .grid {
        height: 80vh;
        padding: 10px !important;
    }

    .select2.select2-container {
        width: auto !important;
    }

</style>

<div class="row white z-depth-3"
     data-ng-init="init()">
    <div class="col s12  white z-depth-3  blue-grey lighten-5 center-align">
    <h4>SKU Packaging Tax Mapping</h4>
    </div>
</div>
<div>
    <div
            class="row"
            id="mappingDivDisplay">
        <div class="col s4">
            <label
                    class="black-text"
                    for="skuList">Sku</label>
            <select
                    ui-select2="{allowClear:true, placeholder: 'Select Sku'}"
                    id="skuList"
                    name="requestForListData"
                    data-placeholder="Select Sku"
                    data-ng-model="selectedSku"
                    data-ng-options="sku as sku.name for sku in skuList track by sku.id"
                    data-ng-change="resetSelection()"></select>
        </div>
        <div class="col s4">
            <label
                    class="black-text"
                    for="skuPackagings">Select Packaging</label>
            <select
                    id="skuPackagings"
                    ui-select2="{allowClear:true, placeholder: 'Select Packaging'}"
                    data-placeholder="Select Packaging"
                    data-ng-model="selectedPackaging"
                    data-ng-options="packaging as packaging.packagingName  for packaging in  skuMapping[selectedSku.id]  track by packaging.packagingId"
                    ></select>
        </div>
        <div class="col s2">
            <button
                    data-ng-click="getUnitSkuPackagingTaxMappings(selectedSku,selectedPackaging)"
                    data-tooltip="Search Mappings"
                    class="btn left" tooltipped acl-action="SMMUSVI">Search</button>
        </div>
    </div>
    <div data-ng-show="showGrid">
        <div class="row">
            <div class="col s4">
                <a
                        href="#bulkAddTax"
                        modal
                        style="text-decoration: none;
                     color:white">
                    <button
                            class="btn btn-medium " style="overflow:hidden;">Bulk Edit
                    </button>
                </a>
            </div>
            <div class="col s4 white z-depth-3 blue-grey lighten-5 center-align">
                <div>
                    <span class="bold">Selected Packaging : </span> <span
                        class="yellow black-text bold">{{currentPackaging.packagingName}}</span>
                </div>
            </div>
            <div class="col s4">
                <button
                        data-ng-click="submit()"
                        data-tooltip="Submit Mappings"
                        class="btn right" tooltipped acl-action="SMMUSVI">Submit
                </button>
            </div>
        </div>

    </div>
    <div class="row margin0" data-ng-if="showGrid">
        <div
                class="col s12 grid"
                id="grid"
                ui-grid="gridOptions"
                ui-grid-save-state
                ui-grid-edit
                ui-grid-selection
                ui-grid-resize-columns
                ui-grid-move-columns
                ui-grid-auto-resize
        ></div>
    </div>
</div>
</div>
<div
            class="row margin0"
            data-ng-show="submitted">
    <button
    data-ng-click="confirmMappings()"
    data-tooltip="confirm  Mappings"
    class="btn right" tooltipped acl-action="SMMUSVI">Submit</button>
        <div class="col s12">
            <ul class="collection striped">
                <li class="collection-item list-head">
                    <div class="row">
                        <div class="col s2">Id</div>
                        <div class="col s2">Name</div>
                        <div class="col s2">Category</div>
                        <div class="col s2">Region</div>
                        <div class="col s2">Status</div>
                        <div class="col s2">Tax Category</div>
                    </div>
                </li>
            </ul>
            <ul
                    class="collection striped"
                    style="max-height: 350px; overflow: auto;">
                <li
                        class="collection-item clickable"
                        data-ng-repeat="allGVS in allGridViewShow | orderBy : 'id' track by $index">
                    <div
                            class="row"
                            style="margin-bottom: 0px;">
                        <div class="col s2">{{allGVS.id}}</div>
                        <div class="col s2">{{allGVS.name }}</div>
                        <div class="col s2">{{allGVS.category}}</div>
                        <div class="col s2">{{allGVS.region}}</div>
                        <div class="col s2">{{allGVS.mappingStatus}}</div>
                        <div class="col s2">{{allGVS.taxCategory.code}}</div>
                    </div>
                </li>
            </ul>
        </div>
    </div>

</div>


<div id="bulkAddTax"
     class="modal modal-mx">
    <div class="modal-content">
    </div>
    <div class="row">
        <div class="data-error">* All Units Of Selected Region Will Be Selected After Submission</div>
        <div class="row">
            <label class="black-text active" for="region">region</label>
            <select ui-select2 = "{allowClear:true, placeholder: 'Select Region'}"  class="select2" id="region" name="region" data-ng-model="bulkEdit.region"
                    data-ng-options="region for region in regions | orderBy:  region "
            >
            </select>

            <label class="black-text active" for="bulkTaxCategory">Tax Category</label>
            <select ui-select2 = "{allowClear:true, placeholder: 'Select Tax Category'}" class="select2" id="bulkTaxCategory" name="bulkTaxCategory" data-ng-model="bulkEdit.taxCategory"
                    data-ng-options="tax as (tax.code + '-'+ tax.desc) for tax in taxCodes track by tax.code"
            >
            </select>
        </div>


    </div>
    <div class="row">
        <button
                class="modal-action modal-close waves-effect waves-green btn right"
                style="margin-left: 10px"
                ng-click="closeBulkEditModal()">close
        </button>
        <button
                data-ng-if="bulkEdit.taxCategory !=null"
                class="modal-action modal-close waves-effect waves-green btn right"
                style="margin-left: 10px"
                ng-click="confirmBulkEdit()">confirm
        </button>
    </div>

</div>




<div id="addTax"
class="modal modal-mx">
<div class="modal-content">
</div>
<div class="row">
    <div class="row">
        <label class="black-text active" for="taxCategory">Tax Category</label>
        <select ui-select2 class="select2" id="taxCategory" name="taxCategory" data-ng-model="row.taxCategory"
                data-ng-options="tax as (tax.code + '-'+ tax.desc) for tax in row.taxCategories track by tax.code"
        >
        </select>
    </div>


</div>
<div class="row">
    <button
            class="modal-action modal-close waves-effect waves-green btn right"
            style="margin-right: 10px"
            ng-click="cancelTaxModal()">ok
    </button>
</div>

</div>






<script
        type="text/ng-template"
        id="taxCode.html">
    <div class="ui-grid-cell-contents">
        <a
                href="#addTax"
                modal
                style="text-decoration: none;
                     color:white">
            <button
                    class="btn btn-xs-small " style="overflow:hidden;"
                    data-ng-click="grid.appScope.onTaxCategoryChange(row.entity)"><b>+</b> {{row.entity.taxCategory.code}}
            </button>
        </a>
    </div>
</script>
