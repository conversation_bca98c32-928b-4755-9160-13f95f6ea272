<style>

    .select2.select2-container {
        width: auto !important;
    }
</style>
<div
        class="row white z-depth-3"
        data-ng-init="init()">
    <div class="col s12">
        <h4>Unit to SKU Profile Mapping</h4>
    </div>
    <div
            class="row"
            id="mappingDivDisplay">
            <div class="scm-form" style="padding: 10px 0;">
                    <div class="col s6">
                        <label class="black-text" >Select Unit</label>
                        <select ui-select2="selectedUnit1"
                                id="requestForListData"
                                name="requestForUnitData"
                                data-placeholder="Select Unit"
                                data-ng-model="selectedUnit"
                                data-ng-options="unit as unit.name for unit in allUnitsList track by unit.id"
                                data-ng-change="showProductForMapping(selectedUnit)"></select>
                    </div>

                    <div class="col s6" >
                        <label class="black-text" >Sku To Profile</label>
                        <button class="btn" ng-click="showAllSkus()" style="margin-top:-5px;">
                            <span>Allocate Profile</span>
                        </button>
                    </div>

                    <div class="col s12">
                            <label>Profile Name</label>
                            <select style="margin-top: -30px" ng-model="selectedProfile" class="form-control" data-placeholder="Select Profile" ng-change="changeProfile(selectedProfile)">
                                <option ng-repeat="profile  in recipeProfiles">{{profile.name}}</option>
                            </select>
                    </div>
                    <div class="col s2">
                        <Button class="btn" data-ng-click="showAllSelectedSkus()" style="margin-top:20px;">
                            <span>Preview</span>
                        </Button>
                    </div>
                </div>
            </div>
        </div>

<!--Profile Preview Modal-->

<script type="text/ng-template"  id="ProfileDetails.html">
    <div class="row" data-ng-init="initProfileShowModal()">
    <div class="modal-content">
        <div class="row">
            <h5>
                Selected Sku Profile <b>{{selectUnit.name}}</b>
            </h5>
        </div>
        <div class="row margin0">
            <div class="modal-body">
            <table class="bordered striped">
                <thead>
                <tr>
                    <th class="center-align">SKU ID</th>
                    <th class="center-align">NAME</th>
                    <th class="center-align">MAPPING STATUS</th>
                    <th class="center-align">PREVIOUS PROFILE</th>
                    <th class="center-align">UPDATED PROFILE</th>
                </tr>
                </thead>
                <tbody>
                <tr data-ng-repeat="sku in updatedList track by $index">
                    <td class="center-align">{{sku.id}}</td>
                    <td class="center-align">{{sku.name}}</td>
                    <td class="center-align">{{sku.mappingStatus}}</td>
                    <td class="center-align">{{sku.profile}}</td>
                    <td class="center-align">{{updatedProfile}}</td>
                </tr>
                </tbody>
            </table>
        </div>
        </div>
    </div>
        <div class="row" style="margin-top: 10px;">
            <button class="btn right margin-right-5" data-ng-click="submit()">Submit</button>
            <button class="btn right margin-right-5" data-ng-click="cancel()">Cancel</button>
        </div>
    </div>
</script>

 <!--Products to Map-->
<script type="text/ng-template" id="skuProfileMapping.html">
    <div class="row" data-ng-init="initProfileModal()">
        <h5 style="color:#6a5353;">Unit Sku To Profile Mapping</h5>
        <hr>
        <div class="row">
            <div class="col-lg-4">
                Filter:
                <input type="text" ng-model="search" ng-change="filter()" placeholder="Filter" class="form-control" />
            </div>
        </div>
        <ul class="collection-with-header unit-collection collection striped">
            <li class="collection-header">
                <div class="row">
                    <div class="col s2">
                        <span class="custom-col-header">Select</span>
                    </div>
                    <div class="col s5">
                        <span class="custom-col-header">Sku Name</span>
                    </div>
                    <div class="col s2">
                        <span class="custom-col-header">Profile Name</span>
                    </div>
                </div>
            </li>
            <li class="collection-item collection-item clickable" data-ng-repeat="sku in skuList | filter:search | orderBy : 'name'">

                <div >
                    <div class="row">

                        <div class="col s2">
                            <input type="checkbox"
                                   ng-model="sku.dataCheck" ng-disabled="sku.checkBoxcheck" data-ng-click="isCheckTrue($event, sku.code)" style="position:inherit;opacity:1">
                        </div>
                        <div class="col s5">
                            {{sku.name}}
                        </div>
                        <div class="col s2">
                            {{sku.profile}}
                        </div>
                    </div>
                </div>
            </li>
        </ul>
    </div>
    <hr>
    <div class="row">
        <button class="btn right margin-right-5" data-ng-click="submit()">Submit</button>
        <button class="btn right margin-right-5" data-ng-click="cancel()">Cancel</button>
    </div>
</script>