<!doctype html>
<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CHAAYOS - VENDORS</title>
    <link rel="icon" type="image/png" sizes="32x32" href="favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="96x96" href="favicon-96x96.png">
    <link rel="icon" type="image/png" sizes="16x16" href="favicon-16x16.png">
    <!-- CSS -->
    <link rel="stylesheet" href="libs/materialize/dist/css/materialize.min.css">
    <link rel="stylesheet" href="libs/select2/select2.min.css">
    <link rel="stylesheet" href="libs/alert/alert.css">
    <link rel="stylesheet" href="css/dnd.css">
    <link rel="stylesheet" href="libs/angular-print/angularPrint.css">
    <!-- custom styles -->
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
    <link href="libs/font-awesome/css/font-awesome.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/style.css">
    <script src="https://cdn.jsdelivr.net/npm/signature_pad@4.0.0/dist/signature_pad.umd.min.js"></script>

    <!-- custom method to disable back button -->
    <script type = "text/javascript" >
        history.pushState(null, null, '');
        window.addEventListener('popstate', function(event) {
            history.pushState(null, null, '');
        });
    </script>
    <!-- custom method to disable back button -->

    <style>
        form input[type="text"]{
            text-transform: uppercase;
        }

        .custom-nav{
            position: fixed;
            top:0;
            z-index: 9;height: 3.5rem;line-height: 3.5rem;
        }
        .editGst{
            border: 1px solid #cddc39;
            background-color: #fff;
            color: #836363;
            border-radius: 3px;
            font-size: 12px;
            padding: 5px 10px;
            line-height: 12px;
        }
    </style>

</head>
<body data-ng-app="vendorApp">
<!-- Spinner code starts here-->
<div class="overlay" data-ng-show="showSpinner">
    <div id="scm-spinner-wrapper">
    <div id="scm-spinner" class="preloader-wrapper big active">
        <div class="spinner-layer spinner-blue-only">
            <div class="circle-clipper left">
                <div class="circle"></div>
            </div>
            <div class="gap-patch">
                <div class="circle"></div>
            </div>
            <div class="circle-clipper right">
                <div class="circle"></div>
            </div>
        </div>
        </div>
    </div>
</div>
<!-- Spinner code ends here-->
<div class="overlay" data-ng-show="showOverlay"></div>

<nav class="custom-nav">
    <div class="row" style="margin-bottom:0px;font-size:16px;">
        <div class="col s12 center-align">
            <h5 class="page-title white-text">Chaayos Registration</h5>
        </div>
    </div>
</nav>

<div class="row" style="margin-left:5%;margin-right:5%;" ui-view></div>


<!-- Alert Service Template  -->
<div id="materialModal" onclick="closeMaterialAlert(event, false)" class="hide">
    <div id="materialModalCentered">
        <div id="materialModalContent" onclick="event.stopPropagation()">
            <div id="materialModalTitle">&nbsp;</div>
            <div id="materialModalText">&nbsp;</div>
            <div id="materialModalButtons">
                <div id="materialModalButtonDismiss" class="materialModalButton" onclick="closeMaterialAlert(event, true)">
                    <a>OK</a>
                </div>
                <div id="materialModalButtonOK" class="materialModalButton" onclick="closeMaterialAlert(event, true)">
                    <a>Yes</a>
                </div>
                <div id="materialModalButtonCANCEL" class="materialModalButton"
                     onclick="closeMaterialAlert(event, false)">
                    <a>NO</a>
                </div>
            </div>
        </div>
    </div>
</div>

<!--Upload file template-->
<div id="fileUploadServiceModal" class="modal hide">
    <div id="fileUploadModalCentered">
        <div id="fileUploadModalContent" class="row" onclick="event.stopPropagation()">
            <div class="col s12">
                <div id="fileModalTitle" style="font-size: 18px;">File Upload</div>
                <div class="file-field input-field">
                    <div class="btn btn-small">
                         <input id="fileToUpload" class="pull-left" type="file"> <span id="fileModalText">Choose File</span>
                    </div>
                    <div class="file-path-wrapper">
                        <input id="uploadFilePath" class="file-path validate" type="text" placeholder="Select File">
                    </div>
                    <span id="fileUploadError"></span>
                </div>
                <div id="fileUploadButtons">
                    <button class="waves-effect waves-green btn"
                            onclick="uploadFile(event,true)">UPLOAD
                    </button>
                    <button class="red waves-effect waves-red btn"
                            onclick="closeFileModal(event)">CANCEL
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JS -->
<!--<script src="libs/jquery/dist/jquery.min.js"></script>-->
<script src="libs/jquery/dist/jquery.min.js"></script>
<script src="libs/materialize/dist/js/materialize.min.js"></script>
<script src="libs/angular/angular.min.js"></script>
<!--<script src="libs/signature_pad/signature.js"></script>-->
<!--<script src="libs/signature_pad.min.js"></script>-->
<script src="libs/angular/angular-cookies.min.js"></script>

<script src="libs/angular-ui-router/release/angular-ui-router.min.js"></script>
<script src="libs/angular-materialize/angular-materialize.js"></script>
<script src="libs/select2/select2.min.js"></script>
<script src="libs/select2/ui-select2.js"></script>
<script src="libs/drag&drop/angular-drag-and-drop-lists.min.js"></script>
<script src="libs/FileSaver/FileSaver.min.js"></script>
<script src="libs/angular-print/angularPrint.js"></script>
<script src="libs/fixed-header/sticky-header.js"></script>
<script src="libs/alert/alert.js"></script>
<script src="libs/validation/form.js?v=6.0"></script>

<!-- ANGULAR CUSTOM SERVICES-->
<script src="js/vendor/vendor-app.js?v=0.1.9"></script>
<script src="js/vendor/services/AuthService.js?v=0.1.9"></script>
<script src="js/vendor/services/AppUtil.js?v=0.1.9"></script>


<!-- ANGULAR CUSTOM CONTROLLERS-->
<script src="js/vendor/controllers/VendorController.js?v=0.1.9"></script>
<!--<script>-->
<!--    var canvas = document.getElementById("signature");-->
<!--                var ratio = Math.max(window.devicePixelRatio || 1, 1);-->
<!--                canvas.width = canvas.offsetWidth * ratio;-->
<!--                canvas.height = canvas.offsetHeight * ratio;-->
<!--                canvas.getContext("2d").scale(ratio, ratio);-->
<!--</script>-->

</body>
</html>
