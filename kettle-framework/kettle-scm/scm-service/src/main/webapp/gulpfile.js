// Include gulp
var gulp = require('gulp'),
    {src, dest, series, parallel, watch} = require('gulp'),
    fs = require('fs'),
    handlebars = require('gulp-compile-handlebars'),
    rename = require('gulp-rename'),
    jshint = require('gulp-jshint'),
    stylish = require('jshint-stylish'),
    concat = require('gulp-concat'),
    uglify = require('gulp-uglify'),
    cleanCSS = require('gulp-clean-css'),
    hash = require('gulp-hash'),
    clean = require('gulp-clean'),
    angularFilesort = require('gulp-angular-filesort'),
    noop = require("gulp-noop"),
    argv = require('minimist')(process.argv.slice(2)),
    htmlhint = require('gulp-htmlhint'),
    htmlmin = require('gulp-htmlmin'),
    ngHtml2Js = require('gulp-ng-html2js'),
    browserSync = require('browser-sync');
    gulpif = require('gulp-if');


var s3 = require('gulp-s3-upload')({
    accessKeyId: "********************",
    secretAccessKey: "waqQ/XK541NHcKM7xIWi5WflDunCSqfmTAA9XoSY"
});

var properties = {
    externalScripts: [
        "./libs/jquery/dist/jquery.min.js",
        "./libs/materialize/dist/js/materialize.min.js",
        "./libs/angular/angular.min.js",
        "./libs/angular/angular-cookies.min.js",
        "./libs/angular-ui-router/release/angular-ui-router.min.js",
        "./libs/angular-materialize/angular-materialize.js",
        "./libs/angular-sanitize.min.js",
        "./libs/select2/select2.min.js",
        "./libs/select2/ui-select2.js",
        "./libs/alert/alert.js",
        "./libs/drag&drop/angular-drag-and-drop-lists.min.js",
        "./libs/FileSaver/FileSaver.min.js",
        "./libs/angular-print/angularPrint.js",
        "./libs/fixed-header/sticky-header.js",
        "./libs/angular-grid/angular-touch.min.js",
        "./libs/angular-grid/angular-animate.min.js",
        "./libs/angular-grid/ui-grid.min.js",
        "./libs/angular-grid/ui-grid.exporter.min.js",
        "./libs/fonts/vfs_fonts.js",
        "./libs/angular-grid/lodash.min.js",
        "./libs/angular-grid/jszip.min.js",
        "./libs/angular-grid/excel-builder.dist.js",
        "./libs/popeye/popeye.min.js",
        "./libs/scanner.js",
        "./libs/angularjs-dropdown-multiselect.min.js",
        "libs/validation/form.js",
        "libs/highcharts/highcharts.min.js",
        "libs/highcharts/highcharts-ng.min.js",
        "libs/html2canvas.min.js",
        "libs/xlsx.full.min.js",
        "libs/qrcode.min.js",
        "./js/qz/*.js"],
    internalScripts: ["./js/app.js",
        './js/controllers/**/*.js',
        './js/vendor/controllers/**/*.js',
        './js/services/**/*.js',
        './js/vendor/services/**/*.js',
        './js/vendor/vendor-app.js',
        './scripts/directives/**/*.js',
        './scripts/filters/**/*.js'],
    appName: "scmApp",
    version: '0.0.1',
    cdn: {
        prod: {
            content: "https://dvkqc6sjvl6ta.cloudfront.net",
            static: "https://d33m6da7d295ei.cloudfront.net",
            images: "https://d24a96amsw87c2.cloudfront.net"
        },
        uat: {
            content: "http://d9aufd82838fn.cloudfront.net",
            static: "http://d33m6da7d295ei.cloudfront.net",
            images: "http://d144va1t1nw5i9.cloudfront.net"
        },
        stage: {
            content: "http://d3i7vuw55bx2t1.cloudfront.net",
            static: "http://d3hy1vte1uigzt.cloudfront.net",
            images: "http://d1ebmh8bcp2aeo.cloudfront.net"
        },
        dev: {
            content: "http://d9aufd82838fn.cloudfront.net",
            static: "http://dtpzpaa2tyt97.cloudfront.net",
            images: "http://d144va1t1nw5i9.cloudfront.net"
        },
        local: {
            content: "http://dev.kettle.chaayos.com:9595/scm-service",
            static: "http://dev.kettle.chaayos.com:9595/scm-service",
            images: "http://dev.kettle.chaayos.com:9595/scm-service"
        }
    },
    buckets: {
        dev: {
            content: "dev.cdn.content.distribution",
            static: "dev.cdn.static.distribution",
            images: "dev.cdn.images.distribution"
        },
        uat: {
            content: "uat.cdn.content.distribution",
            static: "uat.cdn.static.distribution",
            images: "uat.cdn.images.distribution"
        },
        stage: {
            content: "stage.cdn.content.distribution",
            static: "stage.cdn.static.distribution",
            images: "stage.cdn.images.distribution"
        },
        prod: {
            content: "prod.cdn.content.distribution",
            static: "prod.cdn.static.distribution",
            images: "prod.cdn.images.distribution"
        }
    },
    baseUrls: {
        prod: {
            master: "https://internal.chaayos.com/",
            analytics: "https://internal.chaayos.com/",
            kettle: "https://internal.chaayos.com/",
            scm: "https://internal.chaayos.com/",
            crm: "https://internal.chaayos.com/",
            image: "https://d8xnaajozedwc.cloudfront.net/"
        },
        uat: {
            master: "http://uat.kettle.chaayos.com:9191/",
            analytics: "http://uat.kettle.chaayos.com:9191/",
            kettle: "http://uat.kettle.chaayos.com:9191/",
            scm: "http://uat.kettle.chaayos.com:9191/",
            crm: "http://uat.kettle.chaayos.com:9191/",
            image: "http://d3mikkh4u78xq4.cloudfront.net/"
        },
        stage: {
            master: "http://stage.kettle.chaayos.com:9191/",
            analytics: "http://stage.kettle.chaayos.com:9191/",
            kettle: "http://stage.kettle.chaayos.com:9191/",
            scm: "http://stage.kettle.chaayos.com:9191/",
            crm: "http://stage.kettle.chaayos.com:9191/",
            image: "http://d3mikkh4u78xq4.cloudfront.net/"
        },
        dev: {
            master: "http://dev.kettle.chaayos.com:9595/",
            analytics: "http://dev.kettle.chaayos.com:9595/",
            kettle: "http://dev.kettle.chaayos.com:9595/",
            scm: "http://dev.kettle.chaayos.com:9595/",
            crm: "http://dev.kettle.chaayos.com:9595/",
            image: "http://d2h0i672lsq59f.cloudfront.net/"
        },
        local: {
            master: "http://localhost:8080/",
            analytics: "http://localhost:8080/",
            kettle: "http://localhost:8080/",
            scm: "http://localhost:8080/",
            crm: "http://localhost:8080/",
            image: "http://d2h0i672lsq59f.cloudfront.net/"
        }
    }

};

// Lint Task
function lint() {
    return src(['js/**/*.js'])
        .pipe(jshint())
        .pipe(jshint.reporter(stylish));
}

// Concatenate & Minify JS third party js scripts
function staticJs() {
    return src(properties.externalScripts)
           .pipe(gulpif('!*.min.js', uglify())).on('error', console.error)
           .pipe(concat('static.js'))
       // .pipe(concat('static.js'))
        //.pipe(uglify())
        .pipe(hash({
                version: properties.version,
                template: '<%= name %>.<%= hash %><%= ext %>'
            }))
        .pipe(dest('./dist/static/js/'))
        .pipe(hash.manifest('staticJS.json', {
            deleteOld: false,
            sourceDir: './dist/static/js/'
        }))
        .pipe(dest('./dist/assets'));
}

// Concatenate & Minify JS internal js scripts
function contentJs() {
    return src(properties.internalScripts)
        .pipe(angularFilesort())
        .pipe(concat('content.js'))
        .pipe(uglify())
        .pipe(hash({
                version: properties.version,
                template: '<%= name %>.<%= hash %><%= ext %>'
            }))
        .pipe(dest('./dist/content/js/'))
        .pipe(hash.manifest('contentJS.json', {
            deleteOld: true,
            sourceDir: './dist/content/js/'
        }))
        .pipe(dest('./dist/assets'));
}

// Concatenate & Minify static CSS
function staticCss() {
    return src(['libs/materialize/dist/css/materialize.min.css', 'libs/select2/select2.min.css', 'libs/alert/alert.css',
        'css/dnd.css', 'libs/angular-print/angularPrint.css', 'libs/angular-grid/ui-grid.min.css', 'libs/popeye/popeye.min.css',
        'libs/font-awesome/css/font-awesome.min.css'])
        .pipe(concat('static.css'))
        .pipe(cleanCSS({compatibility: 'ie8'}))
        .pipe(hash({
                version: properties.version,
                template: '<%= name %>.<%= hash %><%= ext %>'
            }))
        .pipe(dest('./dist/static/css'))
        .pipe(hash.manifest('staticCSS.json', {
            deleteOld: true,
            sourceDir: './dist/static/css/'
        }))
        .pipe(dest('./dist/assets'));
}

// Concatenate & Minify content CSS
function contentCss() {
    return src(['css/style.css'])
        .pipe(concat('content.css'))
        .pipe(cleanCSS({compatibility: 'ie8'}))
        .pipe(hash({
                version: properties.version,
                template: '<%= name %>.<%= hash %><%= ext %>'
            }))
        .pipe(dest('./dist/content/css/'))
        .pipe(hash.manifest('contentCSS.json', {
            deleteOld: true,
            sourceDir: './dist/content/css/'
        }))
        .pipe(dest('./dist/assets'));
}

// Concatenate & Minify CSS
function images() {
    return src(['img/**'])
        .pipe(dest('./dist/img'))
        .pipe(argv.deployment === "local" ? noop() : s3({
            Bucket: properties.buckets[argv.deployment].images,
            ACL: 'public-read',
            keyTransform: function (relative_filename) {
                return "scm-service/" + argv.version + "/img/" + relative_filename;
            }
        }, {
            maxRetries: 5
        }));
}

// Concatenate & Minify CSS
function copyFonts() {
    return src(['fonts/**'])
        .pipe(dest('./dist/static/fonts'));
}

function buildHtml() {
    return src(['./**/*.html', '!index.html', '!indexnew.html', '!./node_modules/**', '!./WEB-INF/**'])
        .pipe(htmlhint({'doctype-first': false}))
        .pipe(htmlmin({
            collapseWhitespace: true,
            removeComments: true,
            removeEmptyAttributes: false,
            removeEmptyElements: false
        }))
        .pipe(ngHtml2Js({
            moduleName: "scmApp",
            prefix: (typeof argv.version != "undefined" ? argv.version + "/" : "")
        }))
        .pipe(concat("partials.js"))
        .pipe(uglify())
        .pipe(hash({
                version: properties.version,
                template: '<%= name %>.<%= hash %><%= ext %>'
            }))
        .pipe(dest('dist/content/js'))
        .pipe(hash.manifest('contentJS.json', {
            deleteOld: true,
            sourceDir: './dist/content/js/'
        }))
        .pipe(dest('./dist/assets'));
}

function cleanBuild() {
    return src('./dist', {read: false, allowEmpty:true}).pipe(clean());
}

function compileIndex() {
    var data = {};
    data.staticJS = JSON.parse(fs.readFileSync('dist/assets/staticJS.json', 'utf8'));
    data.contentJS = JSON.parse(fs.readFileSync('dist/assets/contentJS.json', 'utf8'));
    data.staticCSS = JSON.parse(fs.readFileSync('dist/assets/staticCSS.json', 'utf8'));
    data.contentCSS = JSON.parse(fs.readFileSync('dist/assets/contentCSS.json', 'utf8'));
    data.baseUrl = properties.cdn[argv.deployment]; //base url for content files
    data.isLocal = argv.deployment == "local";
    data.version = (typeof argv.version != "undefined" ? argv.version + "/" : "");
    data.baseUrls = properties.baseUrls[argv.deployment]; //base urls for rest APIs
    var handlebarOpts = {
        ignorePartials: true,
        helpers: {
            eachInMapCss: function (map, block) {
                var out = '';
                Object.keys(map).map(function (prop) {
                    if (prop.indexOf(".css") >= 0) {
                        out += block.fn({key: prop, value: map[prop], data: data});
                    }
                });
                return out;
            },
            eachInMapJs: function (map, block) {
                var out = '';
                Object.keys(map).map(function (prop) {
                    if (prop.indexOf(".js") >= 0) {
                        out += block.fn({key: prop, value: map[prop], data: data});
                    }
                });
                return out;
            }
        }
    };
    return src('build/templates/index.hbs')
        .pipe(handlebars(data, handlebarOpts))
        .pipe(rename('index.html'))
        .pipe(dest('./'));
}

function s3UploadStatic() {
    return src('./dist/static/**')
        .pipe(argv.deployment === "local" ? noop() : s3({
            Bucket: properties.buckets[argv.deployment].static,
            ACL: 'public-read',
            keyTransform: function (relative_filename) {
                return "scm-service/" + argv.version + "/" + relative_filename;
            }
        }, {
            maxRetries: 5
        }));
}

function s3UploadContent() {
    return src('./dist/content/**')
        .pipe(argv.deployment === "local" ? noop() : s3({
            Bucket: properties.buckets[argv.deployment].content,
            ACL: 'public-read',
            keyTransform: function (relative_filename) {
                return "scm-service/" + argv.version + "/" + relative_filename;
            }
        }, {
            maxRetries: 5
        }));
}

// Watch Files For Changes
function watchChanges() {
    browserSync.init({
        server: {
            baseDir: "./"
        }
    });
    watch(properties.internalScripts, { delay: 500 }, series(lint, contentJs, compileIndex));
    watch('styles/main.css', { delay: 500 }, series(contentCss, compileIndex));
    watch(['./**/*.html', '!index.html', '!indexTest.html', '!./bower_components/**', '!./node_modules/**'], series(buildHtml, compileIndex));
    watch(['./dist/**/*', 'index.html']).on('change', browserSync.reload);
}

function build(done) {
    console.log("NODE_ENV:"+process.env.NODE_ENV);
    if(process.env.NODE_ENV != undefined) {
        argv.deployment = process.env.NODE_ENV;
    }
    if(argv.deployment != 'local' && (typeof argv.version == 'undefined' || argv.version == null || argv.version.length == 0)){
        console.log("Please provide valid version parameter i.e --version=0.0.1 please check previous version");
        done();
    } else {
        if (argv.deployment != 'local') {
            series(cleanBuild, lint, parallel(staticJs, staticCss, contentJs, contentCss, copyFonts), buildHtml, compileIndex, s3UploadStatic, s3UploadContent)();
            done();
        } else {
            series(cleanBuild, lint, parallel(staticJs, staticCss, contentJs, contentCss, copyFonts), buildHtml, compileIndex, watchChanges)();
            done();
        }
    }

}
exports.cleanBuild = cleanBuild;
exports.lint = lint;
exports.staticJs = staticJs;
exports.staticCss = staticCss;
exports.contentJs = contentJs;
exports.contentCss = contentCss;
exports.images = images;
exports.copyFonts = copyFonts;
exports.buildHtml = buildHtml;
exports.compileIndex = compileIndex;
exports.s3UploadStatic = s3UploadStatic;
exports.s3UploadContent = s3UploadContent;
exports.watchChanges = watchChanges;
gulp.task('default', build);


//for prod:   gulp --deployment=prod --version=0.0.1
//for local:   gulp --deployment=local --version=0.0.1
//for dev:   gulp --deployment=dev --version=0.0.1
