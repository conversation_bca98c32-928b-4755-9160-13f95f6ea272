'use strict';
angular.module('scmApp').controller('vendorContractCtrl', ['$rootScope', '$stateParams', '$scope', 'apiJson', '$http', '$state',
    'appUtil', '$toastService', '$alertService', 'metaDataService', '$fileUploadService', '$window', 'previewModalService', '$timeout','Popeye',
    function ($rootScope, $stateParams, $scope, apiJson, $http, $state, appUtil,
              $toastService, $alertService, metaDataService, $fileUploadService, $window, previewModalService, $timeout,Popeye) {

        $scope.getAllVendors = function (skuId) {
            $http({
                method: "GET",
                dataType: 'json',
                data: '',
                headers: {
                    "Content-Type": "application/json"
                },
                url: apiJson.urls.skuMapping.getAllVendors,
            }).then(function success(response) {
                $scope.allVendorDataList = response.data.filter(function(item){ return item.category === 'VENDOR'});
            }, function error(response) {
                console.log("error:" + response);
            });
        };

        function getCreatedInvoices(view, startDate, endDate) {
            if (appUtil.isEmptyObject(startDate)) {
                $toastService.create("Please select a start date first");
                return;
            }
            if (appUtil.isEmptyObject(endDate)) {
                $toastService.create("Please select a end date first");
                return;
            }
            if (appUtil.isEmptyObject($scope.selectedStatus)) {
                $toastService.create("Please select a contract status");
                return;
            }

            var params = {
                status: $scope.selectedStatus,
                startDate: startDate,
                endDate: endDate
            };

            if (!appUtil.isEmptyObject($scope.vendorSelected)) {
                params["vendorId"] = $scope.vendorSelected.id;
            }

            $http({
                method: "GET",
                url: apiJson.urls.skuMapping.getVendorContract,
                params: params
            }).then(function (response) {
                if (appUtil.isEmptyObject(response)) {
                    $toastService.create("No Contract found!");
                } else {
                    $scope.contractRequest = response.data;
                    $scope.contractRequest = $scope.contractRequest.sort(function (a, b) {
                        return b.id - a.id;
                    });
                }
            }, function (err) {
                console.log("Encountered error at backend", err);
            });
        }

        $scope.cancelContract = function(val) {
            $alertService.confirm("Are you sure?", null, function (result) {
                 if (result) {
                     var currentUser = appUtil.getCurrentUser();
                                 var payload = {
                                     vendorContractId: val,
                                     employeeId: currentUser.userId,
                                     employeeName: currentUser.user.name
                                 };
                                 $http({
                                     method: 'POST',
                                     url: apiJson.urls.skuMapping.cancelVendorContract,
                                     data: payload
                                 }).then(function (response) {
                                     if (response.data) {
                                         $toastService.create("Contract Cancelled");
                                     }
                                 }, function (err) {
                                      $toastService.create("Encountered error at backend");
                                 });
                     Popeye.closeCurrentModal(true);
                 }
            });
            $scope.getContracts();
        }

        $scope.previewTemplate = function (val) {
            var mappingModal = Popeye.openModal({
                templateUrl : "vendorContractTemplatePreviewModal.html",
                controller : "vendorContractTemplatePreviewModalCtrl",
                resolve : {
                    payload : function() {
                        return val;
                    }
                },
                click : false,
                keyboard : false
            });
            mappingModal.closed.then(function (result) {
                if (result) {
                    $scope.getContracts();
                }
            });
        }

        $scope.downloadExcell = function (){
            var params = {
                className : "com.stpl.tech.scm.domain.model.SalesPerformaInvoice"
            }
            var skipColumns = ['invoice','signedQrCode','ewayBill','deliveredDocumentUrl'];
            var jsonStrings = [];
            for(var i = 0;i<$scope.invRequest.length;i++){
                jsonStrings.push(JSON.stringify($scope.invRequest[i]));
            }
            metaDataService.downloadExcell(jsonStrings,params,skipColumns);
        }

        function createEmptyMultipartFile(fileName) {
          var emptyBuffer = new ArrayBuffer(0);
          var emptyFile = new Blob([emptyBuffer], { type: 'multipart/form-data' });
          emptyFile.name = fileName;
          return emptyFile;
        }


        function uploadEmptyFile(url, type, invoiceId, callback) {
            var fileExt = metaDataService.getFileExtension(invoiceId+".pdf");
            if (fileExt.toLowerCase() == 'pdf' || metaDataService.isImage(fileExt.toLowerCase())) {
                var mimeType = fileExt.toUpperCase();
                var fd = new FormData();
                fd.append('fileType', type);
                fd.append('mimeType', mimeType);
                fd.append('userId', $scope.currentUser.userId);
                fd.append('invoiceId', invoiceId);
                fd.append('file', createEmptyMultipartFile(invoiceId+".pdf"));
                $http({
                        url: url,
                        method: 'POST',
                        data: fd,
                        headers: {
                            'Content-Type': undefined
                        },
                        transformRequest: angular.identity
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (!appUtil.isEmptyObject(response)) {
                    $toastService.create("Upload successful");
                    callback(response);
                    } else {
                        $toastService.create("Upload failed");
                    }
                }).error(function (response) {
                    if (response.errorMsg != null) {
                        toastService.create(response.errorMsg);
                    }
                    $rootScope.showFullScreenLoader = false;
                    $toastService.create("Upload failed");
                    });
            }
        }
        
        function uploadFile(url, type, invoiceId, callback) {
            $fileUploadService.openFileModal("Upload Invoice Document", "Find", function (file) {
                if (file == null) {
                    $toastService.create('File cannot be empty');
                    return;
                }
                if (file.size > 2048000) {
                    $toastService.create('File size should not be greater than 2MB.');
                    return;
                }
                var fileExt = metaDataService.getFileExtension(file.name);
                if (fileExt.toLowerCase() == 'pdf' || metaDataService.isImage(fileExt.toLowerCase())) {
                    var mimeType = fileExt.toUpperCase();
                    var fd = new FormData();
                    fd.append('fileType', type);
                    fd.append('mimeType', mimeType);
                    fd.append('userId', $scope.currentUser.userId);
                    fd.append('invoiceId', invoiceId);
                    fd.append('file', file);
                    $http({
                        url: url,
                        method: 'POST',
                        data: fd,
                        headers: {
                            'Content-Type': undefined
                        },
                        transformRequest: angular.identity
                    }).success(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        if (!appUtil.isEmptyObject(response)) {
                            $toastService.create("Upload successful");
                            callback(response);
                        } else {
                            $toastService.create("Upload failed");
                        }
                    }).error(function (response) {
                        if (response.errorMsg != null) {
                            $toastService.create(response.errorMsg);
                        }
                        $rootScope.showFullScreenLoader = false;
                        $toastService.create("Upload failed");

                    });
                } else {
                    $toastService.create('Upload Failed , File Format not Supported');
                }
            });
        }


        $scope.selectJsonInvoice = function(jsonInvoice){
            $scope.calculatedDistance = null;
            $scope.selectedJsonInvoice = jsonInvoice;
        }

        $scope.resetDistance =  function (){
            $scope.calculatedDistance = null;
            $scope.selectedJsonInvoice = null;
        }

        $scope.selectInv = function(jsonInvoice,index){
            $scope.indexSelectedInv = index;
            $scope.dateOfDelAtCust = null;
            $scope.selectedInv = jsonInvoice;
            $scope.minDate = appUtil.convertToDateWithoutTime(jsonInvoice.createdAt);
            $scope.maxDate = appUtil.getCurrentBusinessDate();
        }

        $scope.resetDateOfDelivery =  function (){
            $scope.indexSelectedInv = null;
            $scope.dateOfDelAtCust = null;
            $scope.selectedInv = null;
            $scope.minDate = null;
            $scope.maxDate = null;
        }


        $scope.printChallan = function (invoice) {
            $scope.selectedInvoice = invoice;
            $scope.totalAmountInWords = appUtil.inWords(Math.round(invoice.totalAmount + invoice.additionalCharges));
            $scope.availableTaxes = 1;
            $timeout(function () {
                angular.element('#' + invoice.id + '_printDiv').trigger('click');
            });
        };

        $scope.downloadExcel = function (invoiceId) {
            $http({
                url: apiJson.urls.invoiceManagement.downloadExcel,
                method: 'GET',
                params: {invoiceId: invoiceId},
                responseType: 'arraybuffer',
                headers: {
                    'Content-Type': undefined,
                    'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                },
                transformRequest: angular.identity
            }).success(function (response) {
                $rootScope.showFullScreenLoader = false;
                var fileName = "Performa_Invoice_Request_" + invoiceId + ".xlsx";
                var blob = new Blob([response],
                    {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;'}, fileName);
                saveAs(blob, fileName);
            }).error(function (response) {
                $rootScope.showFullScreenLoader = false;
                $toastService.create("Download failed");
            });
        };

        function sanitizeJson(buffer) {
            var json = JSON.parse(buffer);
            for (var key in Object.keys(json)) {
                if (typeof json[key] == "object") {
                    sanitizeJson(json[key]);
                } else {
                    if (json[key] == null) {
                        delete json[key];
                    }
                }
            }
            return json;
        }

        $scope.downloadJson = function (invoiceId) {
            $http({
                url: apiJson.urls.invoiceManagement.downloadJson,
                method: 'GET',
                params: {invoiceId: invoiceId},
                responseType: 'arraybuffer',
                headers: {
                    'Content-Type': undefined,
                    'Accept': 'application/json'
                },
                transformRequest: angular.identity
            }).success(function (response) {
                $rootScope.showFullScreenLoader = false;
                var fileName = "EWAY_JSON_INVOICE_" + invoiceId + ".json";
                var blob = new Blob([response], {type: 'application/json;'}, fileName);
                saveAs(blob, fileName);
                $scope.selectedJsonInvoice = null;
            }).error(function (response) {
                $rootScope.showFullScreenLoader = false;
                $toastService.create("Download failed");
                $scope.selectedJsonInvoice=null;
            });
        };

        $scope.downloadEportalJson = function (invoiceId) {
            if($scope.calculatedDistance==null || $scope.calculatedDistance==0){
                $toastService.create("Please Enter Calculated Distance From seller to Buyer!!");
                return;
            }
            $http({
                url: apiJson.urls.invoiceManagement.downloadEportalJson,
                method: 'GET',
                params: {invoiceId: invoiceId, calculatedDistance: $scope.calculatedDistance},
                responseType: 'arraybuffer',
                headers: {
                    'Content-Type': undefined,
                    'Accept': 'application/json'
                },
                transformRequest: angular.identity
            }).success(function (response) {
                $rootScope.showFullScreenLoader = false;
                var fileName = "EPORTAL_JSON_INVOICE_" + invoiceId + ".json";
                var blob = new Blob([response], {type: 'application/json;'}, fileName);
                saveAs(blob, fileName);
                $scope.calculatedDistance = null;
            }).error(function (response) {
                $rootScope.showFullScreenLoader = false;
                $toastService.create("Download failed");
                $scope.calculatedDistance = null;
            });
        };

        $scope.uploadEway = function (id, index) {
            uploadFile(apiJson.urls.invoiceManagement.uploadInvoice, "EWAY", id, function (response) {
                $scope.invRequest[index].ewayBill = response["invoice"];
                checkDispatchReady(JSON.parse(response['notification']));
            });
        };

        $scope.uploadDoc = function (invoiceId) {
                $fileUploadService.openFileModal("Upload Invoice Sheet", "Find", function (file) {
                    $scope.uploadInvoiceSheet(file, invoiceId);
                });
        };

        $scope.uploadInvoiceSheet = function (file, invoiceId) {
            var fd = new FormData(document.forms[0]);
            fd.append("file", file);
            fd.append("InvoiceId", invoiceId);
            $http({
                url: apiJson.urls.invoiceManagement.uploadInvoicesSheet,
                method: 'POST',
                data: fd,
                headers: {'Content-Type': undefined},
                transformRequest: angular.identity
            }).success(function (response) {
                $rootScope.showFullScreenLoader = false;
                if (response == true) {
                    $scope.uploadFlag = response;
                    $toastService.create("Excel Sheet is uploaded successfully!!.");
                    $scope.getInvoices();
                }
            }).error(function (response) {
                $rootScope.showFullScreenLoader = false;
                    $toastService.create("Error in uploaded sheet.");
            });
        };

        $scope.uploadInvoice = function (id, index) {
            uploadFile(apiJson.urls.invoiceManagement.uploadInvoice, "INVOICE", id, function (response) {
                $scope.invRequest[index].invoice = response["invoice"];
                checkDispatchReady(JSON.parse(response['notification']));
            });
        };

        $scope.uploadDeliveredDocument = function (invR, index) {
            if (invR.type == "B2B_RETURN") {
                uploadEmptyFile(apiJson.urls.invoiceManagement.uploadInvoice, "DELIVER", invR.id, function (response) {
                    $scope.invRequest[index].deliverdDocumentUrl = response["invoice"];
                    getCreatedInvoices($scope.showViewActions, $scope.startDate, $scope.endDate);
                });    
            } else {
                uploadFile(apiJson.urls.invoiceManagement.uploadInvoice, "DELIVER", invR.id, function (response) {
                    $scope.invRequest[index].deliverdDocumentUrl = response["invoice"];
                    getCreatedInvoices($scope.showViewActions, $scope.startDate, $scope.endDate);
                });
            }
        };

        function checkDispatchReady(check) {
            if (check) {
                $alertService.alert("Notification Sent for Dispatch!!",
                    "Thanks! Performa Invoice is now available for Dispatch with E-way and Invoice",
                    function () {
                        getCreatedInvoices($scope.showViewActions, $scope.startDate, $scope.endDate);
                    });
            }
            else {
                console.log("not true")
            }
        }


        $scope.init = function () {
            $scope.unitData = appUtil.getUnitData();
            $scope.companyMap = appUtil.getCompanyMap();
            var currentDate = appUtil.getCurrentBusinessDate();
            if (!appUtil.isEmptyObject(currentDate)) {
                $scope.startDate = appUtil.formatDate(currentDate, "yyyy-MM-dd");
                $scope.endDate = $stateParams.createdInvoice != null
                    ? appUtil.formatDate($stateParams.createdInvoice.dispatchDate, "yyyy-MM-dd")
                    : $scope.startDate;
                $scope.showViewActions = $stateParams.viewInvoice;
                $scope.createdInvoice = $stateParams.createdInvoice;
                $scope.invRequest = [];
                $scope.currentUser = appUtil.getCurrentUser();
                $scope.selectedStatus = null;
                $scope.vendorSelected = null;
                $scope.selectedUnit = null;
                $scope.locationSelected = null;
                $scope.contractStatus = ["CREATED","PENDING","APPROVED","APPLIED","CANCELLED","REJECTED"];
                $scope.showPreview = previewModalService.showPreview;

                $scope.getAllVendors();

                metaDataService.getSkuListForUnit($scope.currentUser.unitId, function (skuForUnitList) {
                    $scope.skus = skuForUnitList;
                });

                getUnitList(function (units) {
                    $scope.units = units;
                });

//                getCreatedInvoices($scope.showViewActions, $scope.startDate, $scope.endDate);
            }
        };

        $scope.reset = function () {
            $scope.selectedSKU = null;
            $scope.vendorSelected = null;
        };

        $scope.selectVendor = function (vendor) {
            $scope.vendorSelected = vendor;

        };

        $scope.getContracts = function () {
            getCreatedInvoices($scope.showViewActions, $scope.startDate, $scope.endDate);
        };

        function getUnitList() {
            $http({
                method: 'GET',
                url: apiJson.urls.unitMetadata.allUnitsList
            }).then(function success(response) {
                if (response.data != null) {
                    var units = response.data;
                    $scope.units = units.filter(function (unit) {
                        return (unit.category == "WAREHOUSE" || unit.category == "KITCHEN") && unit.status == "ACTIVE";
                    });
                }
            }, function error(response) {
                console.log("error:" + response);
            });
        }

        function openUrl(documentId) {
            $http({
                method: "GET",
                url: apiJson.urls.skuMapping.getUrl,
                params: {
                    documentId: documentId
                }
            }).then(function (response) {
                if (!appUtil.isEmptyObject(response.data)) {
                    $window.open(response.data, '_blank');
                } else {
                    $toastService.create("Please check if the selected contract is correct!!");
                }
            }, function (err) {
                console.log("Encountered error at backend", err);
            });
        }

        $scope.printContract = function (invR, index) {
            openUrl(invR);
        };

        $scope.dispatch = function (invR, index) {
            if($scope.dateOfDelAtCust == null || $scope.dateOfDelAtCust == undefined){
                $toastService.create("Please Enter date of delivery at customer");
                return;
            }
            var dodDate = appUtil.formatDate($scope.dateOfDelAtCust, "yyyy-MM-dd");
            $http({
                method: "GET",
                url: apiJson.urls.invoiceManagement.dispatch,
                params: {
                    invoiceId: invR.id,
                    userId: $scope.currentUser.userId,
                    dateOfDelivery : dodDate
                }
            }).then(function (response) {
                if (!appUtil.isEmptyObject(response.data)) {
                    $scope.invRequest[index] = response.data;
                    invR = $scope.invRequest[index];
                    $scope.printInvoice(invR, index);
                    setTimeout(function () {
                        // $scope.printEway(invR, index);
                    }, 5000);
                } else {
                    $toastService.create("Error while trying to change the status of dispatch");
                }
            }, function (err) {
                if (err.data.errorMessage != null) {
                    $alertService.alert(err.data.errorType, err.data.errorMessage, null, true);
                }
                console.log("Encountered error at backend", err);
            });
        };

        $scope.approve = function (invR, index) {
            invR.createdBy = appUtil.createGeneratedBy();
            $http({
                method: "POST",
                url: apiJson.urls.invoiceManagement.approve,
                data: invR
            }).then(function (response) {
                if (!appUtil.isEmptyObject(response.data)) {
                    $scope.invRequest[index] = response.data;
                    invR = $scope.invRequest[index];
                } else {
                    $toastService.create("Error while trying to change the status of invoice request");
                }
            }, function (err) {
                console.log("Encountered error at backend", err);
            });
        };

        $scope.reject = function (invR, index) {
            invR.createdBy = appUtil.createGeneratedBy();
            $http({
                method: "POST",
                url: apiJson.urls.invoiceManagement.reject,
                params: {
                    invoiceId: invR.id,
                    userId: $scope.currentUser.userId
                }
            }).then(function (response) {
                if (!appUtil.isEmptyObject(response.data)) {
                    $scope.invRequest[index] = response.data;
                    invR = $scope.invRequest[index];
                } else {
                    $toastService.create("Error while trying to change the status of invoice request");
                }
            }, function (err) {
                console.log("Encountered error at backend", err);
            });
        };

        $scope.setReadyToDispatch = function (invR, index) {
            // if (invR.ewayBill == null) {
            //     if ((invR.totalAmount + invR.additionalCharges) >= 50000) {
            //         $toastService.create("Upload E-Invoiceway bill");
            //         return;
            //     }
            // }
            invR.createdBy = appUtil.createGeneratedBy();
            $http({
                method: "GET",
                url: apiJson.urls.invoiceManagement.readyToDispatch,
                params: {
                    invoiceId: invR.id,
                    userId: $scope.currentUser.userId
                }
            }).then(function (response) {
                if (!appUtil.isEmptyObject(response.data)) {
                    $scope.invRequest[index] = response.data;
                    invR = $scope.invRequest[index];
                } else {
                    $toastService.create("Error while trying to change the status of invoice request");
                }
            }, function (err) {
                if (err.data.errorMsg != null) {
                    $toastService.create(err.data.errorMsg);
                }
                console.log("Encountered error at backend", err);
            });
        };

        $scope.uploadDocInvoice = function (invoice, index) {
            $scope.updatedDocInvoice = invoice;
        };

        $scope.updateDocumentNumber = function(UploadedDocumentNumber){
            if (appUtil.isEmptyObject(UploadedDocumentNumber) || (UploadedDocumentNumber == null || UploadedDocumentNumber == undefined)) {
                $toastService.create("Please... Add Uploaded Document Number!");
                return;
            }
            else {
                var obj = {
                    invoiceId: $scope.updatedDocInvoice.id,
                    uploadDocId: UploadedDocumentNumber
                }
                $http({
                    method: "POST",
                    url: apiJson.urls.invoiceManagement.updateUploadDocumentID,
                    params: obj,
                }).then(function (response) {
                    $toastService.create("Uploaded Document Number updated successfully.");
                    $scope.getInvoices();
                }, function (error) {
                    $toastService.create("Could not save Document number");
                });
            }
            $scope.UploadedDocumentNumber = null;
        }
        $scope.generateB2BInvoice = function (invoice, index) {
            if(invoice.irnNo == null){
                $toastService("Please Upload Excel First");
                return;
            }
            //TODO generate invoice file and then send it to backend
            $scope.selectedInvoice = invoice;
            // if (invoice.ewayBill == null) {
            //     if ((invoice.totalAmount + invoice.additionalCharges) >= 50000) {
            //         $toastService.create("Upload Eway bill");
            //         return;
            //     }
            // }
            $scope.totalAmountInWords = appUtil.inWords(Math.round(invoice.totalAmount + invoice.additionalCharges));
            $scope.availableTaxes = 1;


            $http({
                method: "POST",
                url: apiJson.urls.invoiceManagement.generateAndSaveB2bInvoice,
                params: {
                    invoiceId: invoice.id,
                    userId: $scope.currentUser.userId
                }
            }).then(function (response) {
                if (!appUtil.isEmptyObject(response.data)) {
                    console.log("b2b response")
                    $toastService.create("Invoice uploaded");
                    // $scope.invRequest[index].invoice = response["invoice"];
                    // $scope.notification=response['notification'];
                    // console.log($scope.notification);
                    // checkDispatchReady(JSON.parse(response["notification"]));
                    getCreatedInvoices($scope.showViewActions, $scope.startDate, $scope.endDate);
                }
            }, function (err) {
                if (err.data.errorMsg != null) {
                    $toastService.create(err.data.errorMsg);
                }
                console.log("Encountered error at backend", err);
            });
        };

        $scope.uploadCancelInvoiceDoc = function () {
            $fileUploadService.openFileModal("Upload Cancel Invoice Document", "Find", function (file) {
                if (file == null) {
                    $toastService.create('File cannot be empty');
                    return;
                }
                if(file.size > 307200){
                    $toastService.create('File size should not be greater than 300 kb.');
                    return;
                }
                var fileExt = metaDataService.getFileExtension(file.name);
                if (fileExt.toLowerCase() == 'pdf' || metaDataService.isImage(fileExt.toLowerCase())) {
                    var mimeType = fileExt.toUpperCase();
                    var fd = new FormData();
                    fd.append('type', "OTHERS");
                    fd.append('docType', "CANCEL_INVOICE");
                    fd.append('mimeType', fileExt.toUpperCase());
                    fd.append('userId', appUtil.getCurrentUser().userId);
                    fd.append('file', file);
                    $http({
                        url: apiJson.urls.invoiceManagement.uploadCancelInvoice,
                        method: 'POST',
                        data: fd,
                        headers: {'Content-Type': undefined},
                        transformRequest: angular.identity
                    }).success(function (response) {
                        if (!appUtil.isEmptyObject(response)) {
                            $toastService.create("Upload successful");
                            $scope.uploadedDocData = response;
                        } else {
                            $toastService.create("Upload failed");
                        }
                    }).error(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        $toastService.create("Upload failed");
                    });
                } else {
                    $toastService.create('Upload Failed , File Format not Supported');
                }
                /*metaDataService.uploadFile("OTHERS","PAYMENT_REQUEST_INVOICE",file, function(doc){
                    $scope.uploadedDocData = doc;
                });*/
            });
        };

        $scope.downloadCancelInvoice = function (prInvoice) {
            metaDataService.downloadDocument(prInvoice);
        };

        $scope.cancelUploadDocument = function (invId, Index) {
                $scope.cancelInvoiceId = invId;
                $scope.cancelInvoiceIndex = Index;
        }
        $scope.cancelPreview = function (invId, Index) {
            $alertService.confirm("Again Are you sure?", "You are going to cancel this Invoice", function (result) {
                if (result) {
                    if($scope.uploadedDocData == null){
                        $toastService.create("Please Upload Cancel Document First!");
                        return;
                    }
                    $scope.cancelInvoice(invId, Index);
                } else {
                    $scope.uploadedDocData = null;
                    return;
                }
            });
        }

        $scope.closeModal = function(){
            $scope.uploadedDocData = null;
        }

        $scope.mailVendor = function (val) {
            $alertService.confirm("Are you sure, the details are correct?", null, function (result) {
                 if (result) {
                      var payload = {
                          vendorContractId : val,
                          employeeId : appUtil.getCurrentUser().userId,
                          employeeName : appUtil.getCurrentUser().user.name
                      }
                      $http({
                          url: apiJson.urls.skuMapping.triggerMail,
                          method: 'POST',
                          data : payload
                      }).then(function success(response) {
                          if (response.data != null && response.status == 200) {
                              $toastService.create("Mail Triggered To Vendor Successfully");
                          }
                      }, function error(response) {
                          $toastService.create("Issue With Template Preview");
                      });
                       $scope.getContracts();
                     Popeye.closeCurrentModal(true);
                 }
            });
        }

        $scope.applyContract = function (val) {
            $alertService.confirm("Are you sure?", null, function (result) {
                if (result) {
                    $rootScope.showFullScreenLoader = true;
                    var payload = {
                        vendorContractId : val,
                        status :'APPROVED'
                    }
                    $http({
                        url: apiJson.urls.skuMapping.applyContract,
                        method: 'POST',
                        data : payload
                    }).then(function success(response) {
                        if (response.data != null && response.status == 200) {
                            $toastService.create("Contract Applied Successfully");
                        }
                    }, function error(response) {
                        $toastService.create("Issue With Template Preview");
                    });
                    $scope.getContracts();
                    $rootScope.showFullScreenLoader = false;
                }
            });

        }

            $scope.cancelInvoice = function (invId, index) {
            $http({
                method: "GET",
                url: apiJson.urls.invoiceManagement.cancel,
                params: {
                    invoiceId: invId,
                    userId: $scope.currentUser.userId,
                    docId: $scope.uploadedDocData != null ? $scope.uploadedDocData.documentId : null
                }
            }).then(function (response) {
                if (!appUtil.isEmptyObject(response.data)) {
                    $scope.invRequest[index] = response.data;
                    $scope.uploadedDocData = null;
                } else {
                    $scope.uploadedDocData = null;
                    $toastService.create("Error while trying to change the status of dispatch");
                }
            }, function (err) {
                console.log("Encountered error at backend", err);
            });
        };

    }
]).controller('vendorContractTemplatePreviewModalCtrl', ['$scope', 'payload','Popeye','$http','apiJson', 'appUtil', '$toastService',
         function ($scope, payload, Popeye,$http,apiJson, appUtil, $toastService) {
             $scope.initTemplateModal =function () {
                  $scope.payload = payload;
                  $scope.template = [];
                  $scope.templateId= null;
                  getTemplate();
             }

             function getTemplate() {
                $http({
                    url: apiJson.urls.skuMapping.contractTemplate,
                    method: 'GET'
                }).then(function success(response) {
                    if (response.data != null && response.status == 200) {
                        $scope.template = response.data;
                    }
                }, function error(response) {
                    $toastService.create("Request Send Failed");
                    Popeye.closeCurrentModal(false);
                });
             }

             $scope.generateContractWithTemplate = function (val) {
                $scope.templateId = val;
                $http({
                    url: apiJson.urls.skuMapping.generateContractTemplate+"?vendorContractId="+$scope.payload+"&empCode="+appUtil.getCurrentUser().userId+"&templateId="+val,
                    method: 'POST'
                }).then(function success(response) {
                    if (response.data != null && response.status == 200) {
                        $scope.documentDetail = response.data;
                    }
                }, function error(response) {
                    $toastService.create("Issue With Template Preview");
                });
             }

             $scope.submit = function () {
                var payload = {
                    vendorContractId : $scope.payload,
                    documentId : $scope.documentDetail.documentId,
                    templateId : $scope.templateId,
                    employeeId : appUtil.getCurrentUser().userId,
                    employeeName : appUtil.getCurrentUser().user.name
                }
                $http({
                    url: apiJson.urls.skuMapping.mapContractWithDocument,
                    method: 'POST',
                    data: payload
                }).then(function success(response) {
                    if (response.data != null && response.status == 200) {
                        $toastService.create("Template Ready to Preview");
                        Popeye.closeCurrentModal(true);
                    }
                }, function error(response) {
                    $toastService.create("Request For Template Failed");
                    Popeye.closeCurrentModal(false);
                });
                 Popeye.closeCurrentModal(true);
             };

             $scope.close = function() {
                Popeye.closeCurrentModal(false);
             }


         }
         ]);;
