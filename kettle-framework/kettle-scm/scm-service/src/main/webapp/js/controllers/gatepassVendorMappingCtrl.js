/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

angular.module('scmApp')
.controller('gatepassVendorMappingCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil','$location','$toastService','$alertService',
	function ($rootScope, $scope, apiJson, $http, appUtil,$location, $toastService,$alertService) {

	$scope.init = function () {
		$scope.getVendors();
		$scope.unitList = [];
		$scope.operationList = [
			{name : "QUALITY_CHECK", label : "QUALITY CHECK"},{name : "REPAIR", label : "REPAIR"},
			{name : "NPD", label : "NPD"},{name : "SAMPLE", label : "SAMPLE"},
			{name : "INTERNAL_TXN", label : "INTERNAL TXN"}];
		$scope.unitList = appUtil.filterUnitList(appUtil.getUnitList());
		$scope.showVendorAddForm=false;
		$scope.selectedVendor = null;
		$scope.editType="Add";
		$scope.mappedVendors = [];
		$scope.vendorMapping = {};
	};

	$scope.addNewForm=function(value){
		$scope.editType="Add";
		$scope.showVendorAddForm=value;
		if(false){
			createVendorMapping();
		}
	};

	$scope.cancelMapping=function(){
		$scope.addNewForm(false);
	};

	$scope.changeUnit  = function(selectedUnit){
		$scope.selectedUnit = JSON.parse(selectedUnit);
		if($scope.mappedVendors.length>0){
			$alertService.confirm("Are you sure to change unit, Your all data will be resetted ?","",function(result){
				if(result){
					$scope.vendorMapping = {};
					$scope.selectedVendor = null;
					$scope.addNewForm(false);
					$scope.mappedVendors = [];
					$scope.$apply();
				}
			});
		}
	};

	$scope.getVendors = function () {
		$http({
			method: "GET",
			url: apiJson.urls.vendorManagement.vendors
		}).then(function success(response) {
			$scope.vendorList = response.data;
			$scope.vendorList = $scope.vendorList.filter(function(vendor){
				return vendor.status=="ACTIVE";
			});
		}, function error(response) {
			console.log("error:" , response);
		});
	};

	$scope.getMappedVendors = function () {
		var url = apiJson.urls.gatepassManagement.vendorList+"?unitId="+$scope.selectedUnit.id;
		if(!appUtil.isEmptyObject($scope.vendorMapping.operationType)){
			url += "&opsType="+$scope.vendorMapping.operationType
		}
		$http({
			method: "GET",
			url: url
		}).then(function success(response) {
			$scope.mappedVendors = response.data;
		}, function error(response) {
			console.log("error:" + response);
		});
	};

	$scope.addMapping = function () {
		
		if(appUtil.isEmptyObject($scope.vendorMapping.vendorId)){
			$toastService.create("Please Select Vendor!");
			return;
		}
		if(appUtil.isEmptyObject($scope.vendorMapping.operationType)){
			$toastService.create("Please Select Operation Type!");
			return;
		}

		$scope.vendorMapping.unit = appUtil.getIdCodeName($scope.selectedUnit.id);
		$scope.vendorMapping.createdBy = appUtil.createGeneratedBy();
//		console.log($scope.vendorMapping);
		$http({
			method: "POST",
			url: apiJson.urls.gatepassManagement.addVendorMapping,
			data: $scope.vendorMapping
		}).then(function success(response) {
			if(response.data!=null){
				$toastService.create("Vendor "+$scope.selectedVendor.entityName+" added successfully for unit  "+$scope.selectedUnit.name+"!");
				$scope.addNewForm(false);
				$scope.getMappedVendors();
			}else{
				$toastService.create("Something went wrong. Please try again.");
			}
		}, function error(response) {
			//$alertService.alert(response.data.errorTitle,response.data.errorMsg,true);
			$toastService.create(response.data.errorMsg);
			//console.log("error:" , response);
		});
	};

	$scope.activateMapping = function (mappingId) {
		$http({
			method: "PUT",
			url: apiJson.urls.gatepassManagement.activateVendor,
			data: mappingId
		}).then(function success(response) {
			if(response.data==true){
				$scope.getMappedVendors();
			}else{
				$toastService.create("Something went wrong. Please try again.");
			}
		}, function error(response) {
			console.log("error:" , response);
		});
	};

	$scope.deactivateMapping = function (mappingId) {
		$http({
			method: "PUT",
			url: apiJson.urls.gatepassManagement.deactivateVendor,
			data: mappingId
		}).then(function success(response) {
			if (response.data == true) {
				$scope.getMappedVendors();
			} else {
				$toastService.create("Something went wrong. Please try again.");
			}
		}, function error(response) {
			console.log("error:", response);
		});
	};

	$scope.selectVendor = function(selectedVendor){
		$scope.selectedVendor = JSON.parse(selectedVendor);
		$scope.vendorMapping.vendorId = $scope.selectedVendor.vendorId;
	};

	function createVendorMapping(){
		$scope.vendorMapping={};
		$scope.vendorMapping.createdBy = appUtil.createGeneratedBy();
		$scope.vendorMapping.unit = null;
		$scope.vendorMapping.vendorId = null;
		$scope.vendorMapping.vendor = null;
	}

}]);
