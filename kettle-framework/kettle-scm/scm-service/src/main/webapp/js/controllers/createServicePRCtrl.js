/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 16-06-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('createServicePRCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$location', '$toastService', 'metaDataService',
        '$fileUploadService', '$alertService','Popeye','$timeout',
        function ($rootScope, $scope, apiJson, $http, appUtil, $location,
                  $toastService, metaDataService, $fileUploadService, $alertService, Popeye, $timeout) {

            $scope.init = function () {
                $scope.employeePaymentCards = [];
                $scope.eligibleForCardPayment = false;
                $scope.uploadedCardPaymentProof = null;
                $scope.maxDate = appUtil.formatDate(appUtil.getDate(0), "yyyy-MM-dd");
            	$scope.srs = [];
                $scope.startDate = appUtil.formatDate(new Date(), "yyyy-MM-dd");
                $scope.endDate = appUtil.formatDate(appUtil.getDate(1), "yyyy-MM-dd");
                $scope.selectView = true;
                $scope.itemDeviations = [];
                $scope.invoiceDeviations = [];
                $scope.getSelectedSrs = [];
                $scope.currentUser = appUtil.getCurrentUser();
                $scope.minRefundDate = appUtil.formatDate(appUtil.getDate(0), "yyyy-MM-dd");
                $scope.advanceSelectionView =false;
                $scope.showSelectedDocuments = [];
                $scope.addReqDocuments = [];
                $scope.addReqDocumentsName=[];
                $scope.requiredDocument = [];
                $scope.mandatoryDocs =[];
                $scope.isUploaded = [];
                metaDataService.getSCMMetaData(function (metadata) {
                    $scope.prRequestTypes = metadata.paymentRequestTypes.filter(function (prType) {
                        return prType.shortCode == "SR";
                    });
                    $scope.prRequestType = $scope.prRequestTypes == null ? null : $scope.prRequestTypes[0];
                    metadata.paymentDeviations.map(function (deviation) {
                        if (deviation.deviationType == "DEVIATION") {
                            if (deviation.deviationLevel == "INVOICE_ITEM") {
                                $scope.itemDeviations.push(deviation);
                            } else if (deviation.deviationLevel == "INVOICE") {
                                $scope.invoiceDeviations.push(deviation);
                            }
                        }
                    });
                }, true);

                metaDataService.getServiceVendors(function (vendors) {
                    $scope.vendorList = vendors;
                });

                metaDataService.getCompanyList(function (companies) {
                    $scope.companies = companies;
                });

                metaDataService.getStates("active",function(states){
                    $scope.locations = states;
                });
                $scope.filledPr = false;
                $scope.getEmployeePaymentCards();
            };

            $scope.getEmployeePaymentCards = function () {
                $http({
                    method: 'GET',
                    url: apiJson.urls.paymentRequestManagement.employeePaymentCards,
                    params: {
                        userId: $scope.currentUser.userId
                    }
                }).then(function (response) {
                    $scope.employeePaymentCards = response.data;
                }, function (error) {
                    console.log(error);
                    $toastService.create("Error Occurred While getting Employee Payment Cards");
                    $scope.employeePaymentCards = [];
                });
            };

            $scope.setPaymentCardCheck = function (check) {
                $scope.paymentRequest.paymentCardCheck = check;
                $scope.setPaymentCard(null);
                $scope.setCardPaymentTransactionNumber(null);
                $scope.paymentRequest.cardPaymentComment = null;
                $scope.uploadedCardPaymentProof = null;
                $scope.paymentRequest.cardPaymentProof = null;
            };

            $scope.setCardPaymentTransactionNumber = function (transactionNumber) {
                $scope.paymentRequest.cardPaymentTransactionNumber = transactionNumber;
                $scope.uploadedCardPaymentProof = null;
                $scope.paymentRequest.cardPaymentProof = null;
            };

            $scope.setPaymentCard = function (card) {
                $scope.paymentRequest.paymentCard = card;
                $scope.setCardPaymentTransactionNumber(null);
                $scope.paymentRequest.cardPaymentComment = null;
                $scope.uploadedCardPaymentProof = null;
                $scope.paymentRequest.cardPaymentProof = null;
            };

            $scope.selectCompany = function(selectedCompany){
              $scope.selectedCompany = selectedCompany;
              $scope.resetListOfSrs();
            };

            $scope.selectLocation = function(selectedLocation){
                $scope.selectedLocation = selectedLocation;
                $scope.resetListOfSrs();
            };

            $scope.blockPrForBlockedVendors = function (srs) {
                if ($scope.blockedAdvancePayments.length > 0) {
                    angular.forEach(srs, function (sr) {
                        if (sr.vendorAdvancePayments!= null) {
                            var check = true;
                            for (var i = 0; i < sr.vendorAdvancePayments.length;i++) {
                                if ($scope.blockedAdvancePayments.indexOf(sr.vendorAdvancePayments[i].advancePaymentId) != -1) {
                                    check = false;
                                }
                            }
                            sr.vendorBlocked = check;
                        } else {
                            sr.vendorBlocked = true;
                        }
                    });
                }
            };

            $scope.findSrs = function () {
                $scope.showNoGR = false;
                var params = {};
                var flag = true;
                if (appUtil.isEmptyObject($scope.startDate)) {
                    $toastService.create("Please select start date");
                    flag = false;
                }
                if (appUtil.isEmptyObject($scope.endDate)) {
                    $toastService.create("Please select end date");
                    flag = false;
                }
                if (appUtil.isEmptyObject($scope.selectedVendor)) {
                    $toastService.create("Please select vendor");
                    flag = false;
                }
                // if(appUtil.isEmptyObject($scope.selectedLocation)){
                //     $toastService.create("Please select delivery location");
                //     flag = false;
                // }
                if(appUtil.isEmptyObject($scope.selectedCompany)){
                    $toastService.create("Please select company");
                    flag = false;
                }
                if (!appUtil.isEmptyObject($scope.selectedLocation) && $scope.selectedLocation !== undefined) {
                    params["stateId"] = $scope.selectedLocation.id;
                }
                else{
                    params["stateId"] = null;
                }

                if(flag) {
                    $scope.srs = [];
                    $http({
                        method: 'GET',
                        url: apiJson.urls.serviceReceivedManagement.findReceivingsForPayment,
                        params: {
                            startDate: $scope.startDate,
                            endDate: $scope.endDate,
                            vendorId: $scope.selectedVendor.id,
                            companyId: $scope.selectedCompany.id,
                            stateId: params.stateId,
                            userId: $scope.currentUser.userId
                        }
                    }).then(function (response) {
                        if (!appUtil.isEmptyObject(response.data)) {
                            $scope.srs = response.data;
                            $scope.blockPrForBlockedVendors($scope.srs);
                        } else {
                            $scope.showNoGR = true;
                        }
                    }, function (error) {
                        console.log(error);
                    });
                }
            };

            $scope.resetListOfSrs = function () {
                $scope.srs = [];
            };

            $scope.setSelectedVendor = function (selectedVendor) {
                $scope.blockedAdvancePayments = [];
                $scope.resetListOfSrs();
                metaDataService.getVendorDetail(selectedVendor.id, function (vendor) {
                    if (vendor.vendorBlocked != undefined && vendor.vendorBlocked != null && vendor.vendorBlocked == 'Y') {
                        if (vendor.blockedReason == 'MANUAL') {
                            $alertService.alert("Vendor is Blocked...!", "This vendor is blocked for making further PO/GR/SO/SR/PR...!" +
                                "<br><b>Please Contact Finance Team..!</b>", function () {
                            }, true);
                            $scope.selectedVendor = null;
                            $timeout(function () {
                                $('#vendorListx').val('').trigger('change');
                            });
                            return;
                        } else {
                            $alertService.alert("Vendor is Blocked...!", "This vendor is blocked for making further PO/GR/SO/SR/PR...!" +
                                "<br><b>Only SR's Related to these Advance are allowed to process further " + vendor.blockedReason + "</b>", function () {
                            }, false);
                            $scope.blockedAdvancePayments = JSON.parse(vendor.blockedReason);
                        }
                    }
                });
            };

            $scope.selectAllAdvanceSrs = function ($event, sr, srs) {
                if (sr.advanceSrs.length > 0) {
                    if (sr.checked) {
                        var notFoundSrs = [];
                        angular.forEach(sr.advanceSrs, function (advSr) {
                            var found = false;
                            for (var i=0;i<srs.length;i++) {
                                if (advSr == srs[i].id) {
                                    found = true;
                                    break;
                                }
                            }
                            if (!found) {
                                notFoundSrs.push(advSr);
                            }
                        });
                        if (notFoundSrs.length > 0 ) {
                            $http({
                                method: 'GET',
                                url: apiJson.urls.serviceReceivedManagement.getMinMaxDateForSrs,
                                params: {
                                    srIds : sr.advanceSrs
                                }
                            }).then(function (response) {
                                if (!appUtil.isEmptyObject(response.data)) {
                                    $alertService.alert("All Sr's are Not available in this Search Range..!", "Please Set the Start Date as : " + appUtil.formatDate(response.data["MIN_DATE"], "yyyy-MM-dd") + " <br>" +
                                        "Please Set the End Date as : " + appUtil.formatDate(response.data["MAX_DATE"], "yyyy-MM-dd") + " <br>" +
                                        "Missing SR Id's are : " + notFoundSrs.join(","), null, true);

                                    $alertService.confirm("Do You Want to Modify The Search..?", "<b>All Sr's are Not available in this Search Range..! </b>" +
                                        "<br>Set the Start Date as : " + appUtil.formatDate(response.data["MIN_DATE"], "yyyy-MM-dd") + " <br>" +
                                        "Set the End Date as : " + appUtil.formatDate(response.data["MAX_DATE"], "yyyy-MM-dd") + " <br>" +
                                        "Missing SR Id's are : " + notFoundSrs.join(","), function (result) {
                                        if (result) {
                                            $scope.startDate = appUtil.formatDate(response.data["MIN_DATE"], "yyyy-MM-dd");
                                            $scope.endDate = appUtil.formatDate(response.data["MAX_DATE"], "yyyy-MM-dd");
                                            $scope.findSrs();
                                        }
                                    });
                                    angular.forEach(srs, function (item) {
                                        item.checked = false;
                                        item.disabled = false;
                                    });
                                } else {
                                    $alertService.alert("All Sr's are Not available in this Search Range..!", "Please Increase the range Of Date as all the Srs is not available over here..!<br>" +
                                        "Missing SR Id's are : " + notFoundSrs.join(","), null, true);
                                    angular.forEach(srs, function (item) {
                                        item.checked = false;
                                        item.disabled = false;
                                    });
                                }
                            }, function (error) {
                                console.log(error);
                                $alertService.alert("All Sr's are Not available in this Search Range..!", "Please Increase the range Of Date as all the Srs is not available over here..!<br>" +
                                    "Missing SR Id's are : " + notFoundSrs.join(","), null, true);
                                angular.forEach(srs, function (item) {
                                    item.checked = false;
                                    item.disabled = false;
                                });
                            });
                            return;
                        }
                        $toastService.create("Selecting all Srs related to this SO, as the SO has advance Payment..!");
                        angular.forEach(srs, function (item) {
                            item.checked = false;
                            item.disabled = true;
                            if (sr.advanceSrs.indexOf(item.id) != -1) {
                                item.checked = true;
                                item.disabled = false;
                            }
                        });
                    } else {
                        $toastService.create("Un Selecting all Srs related to this SO, as the SO has advance Payment..!");
                        angular.forEach(srs, function (item) {
                            item.checked = false;
                            item.disabled = false;
                        });
                    }
                } else {
                    if (sr.checked) {
                        angular.forEach(srs, function (item) {
                            if (item.advanceSrs.length > 0) {
                                item.checked = false;
                                item.disabled = true;
                            }
                        });
                    } else {
                        var nonAdvanceSelected = false;
                        angular.forEach(srs, function (item) {
                            if (item.advanceSrs.length == 0) {
                                if (item.checked) {
                                    nonAdvanceSelected = true;
                                }
                            }
                        });
                        if (nonAdvanceSelected) {
                            angular.forEach(srs, function (item) {
                                if (item.advanceSrs.length > 0) {
                                    item.checked = false;
                                    item.disabled = true;
                                }
                            });
                        } else {
                            angular.forEach(srs, function (item) {
                                if (item.advanceSrs.length > 0) {
                                    item.checked = false;
                                    item.disabled = false;
                                }
                            });
                        }
                    }
                }
            };

            $scope.backToSelectView = function () {
                $scope.selectView = true;
                $scope.paymentRequest = null;
                $scope.uploadedDocData = null;
                $scope.filledPr = false;
                $timeout(function () {
                    $('#selectPrLocation').val('').trigger('change');
                });
                $scope.uploadedCardPaymentProof = null;
            };

            $scope.createPR = function () {
                $scope.uploadedDocData = null;
                $scope.selectView = false;
            };

            $scope.prLocation = function(selectedPrLocation){
                $scope.selectPrLocation = selectedPrLocation;
            }

            $scope.setAvailableDeviations = function (item, type) {
                $scope.availableInvoiceDevs = [];
                $scope.availableItemDevs = [];
                var devList = [], availableList = [];
                if (type === "INVOICE") {
                    devList = angular.copy($scope.invoiceDeviations);
                } else {
                    devList = angular.copy($scope.itemDeviations);
                }
                devList.map(function (iDev) {
                    var found = false;
                    if(!appUtil.isEmptyObject(item.deviations)){
                        item.deviations.map(function (dev) {
                            if (iDev.paymentDeviationId === dev.paymentDeviation.paymentDeviationId) {
                                found = true;
                            }
                        });
                    }

                    if (!found) {
                        availableList.push({data: iDev});
                    }
                });
                if (type === "INVOICE") {
                    $scope.availableInvoiceDevs = availableList;
                } else {
                    $scope.selectedItemForDeviation = item;
                    $scope.availableItemDevs = availableList;
                }
            };

            $scope.addDeviations = function (item, type) {
                var devList;
                if(appUtil.isEmptyObject(item.deviations)){
                    item.deviations = [];
                }

                if (type === "INVOICE") {
                    devList = angular.copy($scope.availableInvoiceDevs);
                } else {
                    devList = angular.copy($scope.availableItemDevs);
                }
                devList.map(function (dev) {
                    if (dev.checked) {
                        item.deviations.push({
                            mappingId: null,
                            paymentDeviation: dev.data,
                            deviationRemark: dev.remark,
                            currentStatus: "CREATED",
                            createdBy: appUtil.createGeneratedBy()
                        });
                    }
                });
            };

            $scope.removeDeviation = function (list, index) {
                list.splice(index, 1);
            };

            $scope.showDeviationInput = function (item) {
                item.showDeviationInput = true;
            };

            $scope.updatePRQty = function(item){
            	if(item.receivedQuantity < item.invoiceQuantity){
                    $toastService.create("Invoice Quantity cannot be greater than received quantity");
                    item.invoiceQuantity = parseInt(item.invoiceQuantity.toString().slice(0, -1));
                    return;
                }
            	var taxPercent = 0;
                for(var x = 0; x < item.taxes.length ; x++ ){
                        taxPercent = taxPercent + item.taxes[x].percentage;
                }
            	var totalCost = getTotalPrice(item);
            	var taxPrice = getTaxPrice(totalCost,taxPercent);
            	var totalAmount = getTotalAmount(totalCost,taxPrice);
            	item.totalCost = totalCost;
            	item.totalTax = taxPrice;
                item.totalAmount = totalAmount;
                $scope.calculateTotal();
            }

            $scope.fillPrAmount = function () {
                if (!$scope.filledPr) {
                    $scope.paymentRequest.paymentInvoice.paymentInvoiceItems.forEach(function (item) {
                        item.invoiceAmount = parseFloat((item.receivedQuantity*item.unitPrice).toFixed(6));
                        $scope.updatePRAmount(item);
                    });
                    $scope.filledPr = true;
                }
                else {
                    $scope.paymentRequest.paymentInvoice.paymentInvoiceItems.forEach(function (item) {
                        item.invoiceAmount = 0;
                        $scope.updatePRAmount(item);
                    });
                    $scope.filledPr = false;
                }
            };


            $scope.updatePRAmount = function(item){
                item.invoiceQuantity = roundToSixDecimal(item.invoiceAmount / item.unitPrice);
                if(item.receivedQuantity < item.invoiceQuantity){
                    $toastService.create("Invoice Quantity cannot be greater than received quantity");
                    item.invoiceAmount = parseInt(item.invoiceAmount.toString().slice(0, -1));
                    item.invoiceQuantity = item.invoiceAmount / parseInt(item.unitPrice);
                    return;
                }
                var taxPercent = 0;
                for(var x = 0; x < item.taxes.length ; x++ ){
                    taxPercent = taxPercent + item.taxes[x].percentage;
                }
                var remainingQuantity=item.receivedQuantity-item.invoiceQuantity;
                var totalCost = getTotalPrice(item);
                var taxPrice = getTaxPrice(totalCost,taxPercent);
                var remainingTotalAmount=remainingQuantity*item.unitPrice;
                var remainingTaxAmount=getTaxPrice(remainingTotalAmount,taxPercent);
                var totalAmount = getTotalAmount(totalCost,taxPrice);
                item.totalCost = totalCost;
                item.totalTax = taxPrice;
                item.totalAmount = totalAmount;
                item.remainingTotalAmount=roundToSixDecimal(remainingTotalAmount);
                item.remainingTaxAmount=remainingTaxAmount;
                $scope.calculateTotal();
            }

            $scope.calculateTotal = function(){
                $scope.paidAmount = 0;
				for (var i = 0; i < $scope.paymentRequest.paymentInvoice.paymentInvoiceItems.length; i++) {
					if ($scope.paymentRequest.paymentInvoice.paymentInvoiceItems[i].invoiceQuantity != undefined
						&& $scope.paymentRequest.paymentInvoice.paymentInvoiceItems[i].invoiceQuantity != null) {
                		$scope.paidAmount = parseFloat($scope.paidAmount) + parseFloat($scope.paymentRequest.paymentInvoice.paymentInvoiceItems[i].totalAmount);
                		$scope.paymentRequest.paymentInvoice.paymentAmount = $scope.paidAmount;
                		$scope.paymentRequest.paymentInvoice.invoiceAmount = $scope.paidAmount;
                		$scope.paymentRequest.proposedAmount = $scope.paidAmount;
                		$scope.paymentRequest.paymentInvoice.calculatedInvoiceAmount = $scope.paidAmount;
                	}
                }
            }

            function getTotalPrice(item) {
                var cost = parseFloat(item.invoiceQuantity * item.unitPrice);
                return cost.toFixed(2);
            }

            function getTaxPrice(receivedCost,taxRate) {
                return ((receivedCost*taxRate)/100).toFixed(2);
            }

            function getTotalAmount(totalCost,totalTax){
				return (parseFloat(totalCost) + parseFloat(totalTax)).toFixed(2);
			}

            $scope.submitPaymentRequest = function (forceCreated, isValidate) {
                for(var i in $scope.addReqDocumentsName) {
                    $scope.addReqDocuments.push($scope.addReqDocumentsName[i]);
                }
            	var check = true;
                $scope.duplicatePr = angular.copy($scope.paymentRequest);
            	$scope.paymentRequest.paymentInvoice.paymentInvoiceItems.map(function (item) {
            		if(item.invoiceQuantity == null || item.invoiceQuantity == 0){
                        check = false;
                     }
            	});
//            	if($scope.isAllDocumentsUploaded == false){
//            	$toastService.create("Upload all mandatory documents");
//            	return;
//            	}
            	if(!check){
                    $toastService.create("Invoice Quantity cannot be empty.");
                    return;
                 }
                if(appUtil.isEmptyObject($scope.selectPrLocation)){
                    $toastService.create("Please select a state before creating Payment Request!");
                    return;
                }
                if(appUtil.isEmptyObject($scope.paymentRequest.paymentInvoice.invoiceDate)){
                    $toastService.create("Please select a valid invoice date before creating Payment Request!");
                    return;
                }
                if(appUtil.isEmptyObject($scope.paymentRequest.paymentInvoice.invoiceNumber)){
                    $toastService.create("Please enter a valid invoice number before creating Payment Request!");
                    return;
                }
                if(appUtil.isEmptyObject($scope.uploadedDocData)){
                    $toastService.create("Please upload invoice first before creating Payment Request!");
                    return;
                }
                if ($scope.selectedSRs[0].advanceSrs.length > 0) {
                    var advSrs = $scope.selectedSRs[0].advanceSrs;
                    console.log("Selected Sr Id's ",advSrs);
                    $http({
                        method: 'GET',
                        url: apiJson.urls.serviceReceivedManagement.reCheckAllSrsForAdvance,
                        params: {
                            srId: advSrs[0]
                        }
                    }).then(function (response) {
                        console.log("Rechecked Sr Id's ",response.data);
                        var check = true;
                        var selectedIds = [];
                        var cancelledSrs = [];
                        var missingSrs = [];
                        angular.forEach($scope.selectedSRs, function (sr) {
                            selectedIds.push(sr.id);
                            if (response.data.indexOf(sr.id) == -1) {
                                cancelledSrs.push(sr.id);
                            }
                        });
                        angular.forEach(response.data, function (srId) {
                            if (selectedIds.indexOf(srId) == -1) {
                                missingSrs.push(srId);
                            }
                        });
                        if (cancelledSrs.length > 0) {
                            check = false;
                            $alertService.alert("Looks Like Some SR's are Cancelled..!", "Please Check..! Some Of the Sr's are Cancelled..!<br>" +
                                "Cancelled Sr Id's are : " + cancelledSrs.join(","), null, true);
                        }
                        if (missingSrs.length > 0) {
                            check = false;
                            $alertService.alert("Missing SR's", "Please Check..! There are Some Missing SR's related to the Same SO of Advance Payment..!<br>" +
                                "Missing Srs in this PR are : " + missingSrs.join(","), null, true);
                        }
                        if (check) {
                            $scope.submitRequest(forceCreated, isValidate);
                        }
                    }, function (error) {
                        console.log(error);
                        $toastService.create("Error Occurred While re checking all Srs for Advance..!");
                    });
                } else {
                    if ($scope.paymentRequest.paymentCardCheck != undefined && $scope.paymentRequest.paymentCardCheck != null && $scope.paymentRequest.paymentCardCheck) {
                        if (appUtil.isEmptyObject($scope.paymentRequest.paymentCard)) {
                            $toastService.create("Please Select the Payment Card ..!");
                            return;
                        }
                        if (appUtil.isEmptyObject($scope.paymentRequest.cardPaymentTransactionNumber)) {
                            $toastService.create("Please Enter the Payment Transaction Number ..!");
                            return;
                        }
                        if (appUtil.isEmptyObject($scope.uploadedCardPaymentProof)) {
                            $toastService.create("Please Upload the Card Payment Proof ..!");
                            return;
                        } else {
                            $scope.paymentRequest.cardPaymentProof = $scope.uploadedCardPaymentProof.documentId;
                        }
                    }
                    $scope.submitRequest(forceCreated, isValidate);
                }
            };

            $scope.submitRequest = function (forceCreated, isValidate) {
                var totalAmount = 0;
                $scope.paymentRequest.deviationCount = $scope.paymentRequest.paymentInvoice.deviations.length;
                $scope.paymentRequest.paymentInvoice.invoiceDocumentHandle = $scope.uploadedDocData.documentId;
                $scope.paymentRequest.paymentInvoice.paymentInvoiceItems.forEach(function (item) {
                    if (!appUtil.isEmptyObject(item.deviations)) {
                        $scope.paymentRequest.deviationCount += item.deviations.length;
                    }
                    item.skuId = item.costElementId;
                    item.skuName = item.costElementName;
                    item.hsn = item.ascCode;
                    item.skuDate = item.costElementDate;
                    item.toSkuDate = item.costElementToDate;
                    item.uom = item.unitOfMeasure;
                    item.quantity = item.invoiceQuantity;
                    item.conversionRatio = 1;
                    item.totalPrice = item.totalCost;
                    item.packagingName = item.unitOfMeasure;
                    item.serviceReceivedId = item.serviceReceivedId;
                    item.serviceReceivedItemId = item.id;
                    totalAmount = (parseFloat(totalAmount) + parseFloat(item.totalAmount)).toFixed(2);
                    item.taxes.forEach(function (tax) {
                        tax.taxType = tax.taxName;
                        tax.taxPercentage = tax.percentage;
                        tax.taxValue = tax.value
                    });
                    item.taxes = angular.copy(item.taxes);
                });
                $scope.paymentRequest.invoiceNumber = $scope.paymentRequest.paymentInvoice.invoiceNumber;
                $scope.paymentRequest.grDocType = 'INVOICE';
                $scope.paymentRequest.paymentInvoice.paymentAmount = totalAmount;
                $scope.paymentRequest.paymentInvoice.invoiceAmount = totalAmount;
                $scope.paymentRequest.paidAmount = totalAmount;
                $scope.paymentRequest.proposedAmount = totalAmount;
                $scope.paymentRequest.paymentInvoice.calculatedInvoiceAmount = totalAmount;
                $scope.paymentRequest.state = $scope.selectPrLocation;
                $scope.paymentRequest.forceCreate = forceCreated;
                $scope.paymentRequest.mandatoryRequiredDocument = $scope.addReqDocuments.toString();
                if ($scope.selectedSRs[0].advanceSrs.length > 0) {
                    $scope.paymentRequest.advanceSrs = $scope.selectedSRs[0].advanceSrs;
                    if ($scope.poSoSelected != null) {
                        $scope.paymentRequest.advancePayment.selectedSoPo = $scope.poSoSelected.id;
                    }
                }
                if (!isValidate) {
                    $scope.paymentRequest.duplicatePaidAmount = angular.copy($scope.paymentRequest.proposedAmount);
                    if ($scope.selectedSRs[0].advanceSrs.length > 0) {
                        $scope.paymentRequest.advancePaymentIds = $scope.advPaymentIds;
                        $scope.paymentRequest.advanceSrs = $scope.selectedSRs[0].advanceSrs;
                        $scope.paymentRequest.proposedAmount = $scope.paymentRequest.finalProposedAmount;
                        $scope.paymentRequest.paidAmount = $scope.paymentRequest.finalProposedAmount;
                        $scope.paymentRequest.availableAmount =  $scope.totalAdvanceAvailableAmount;
                        $scope.paymentRequest.advancePayment.availableAmount =  $scope.totalAdvanceAvailableAmount;
                        if ($scope.poSoSelected != null) {
                            $scope.paymentRequest.advancePayment.selectedSoPo = $scope.poSoSelected.id;
                        }
                    }
                    $http({
                        url: apiJson.urls.serviceReceivedManagement.createPaymentRequest,
                        method: 'POST',
                        data: $scope.paymentRequest,
                    }).success(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        if (response != null) {
                            $alertService.alert("Payment Request creation successful",
                                "Payment request created successfully with request id " + response.paymentRequestId, function () {
                                    $scope.init();
                                });
                        } else {
                            $toastService.create("Payment Request creation failed.");
                        }
                        $scope.selectPrLocation = null;
                    }).error(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        $alertService.alert("Payment Request creation failed", response.errorMsg, function () {
                        }, true)
                    });
                } else {
                    $scope.advanceSelectionView = true;
                    $scope.selectView = false;
                    var uniqueAdvances = {};
                    angular.forEach($scope.selectedSRs, function (sr) {
                        if (sr.vendorAdvancePayments != null && sr.vendorAdvancePayments.length > 0) {
                            angular.forEach(sr.vendorAdvancePayments, function (advance) {
                                uniqueAdvances[advance.advancePaymentId] = advance;
                            });
                        }
                    });
                    $scope.adjustAdvanceAmount(Object.values(uniqueAdvances));
                }
            };

            $scope.setRefundDate = function (refundDate) {
                $scope.paymentRequest.advancePayment.refundDate = refundDate;
            };

            $scope.setSoClosed = function (flag) {
                $scope.paymentRequest.advancePayment.soClosed = flag;
                $scope.paymentRequest.advancePayment.adjustOrRefund = null;
                $scope.paymentRequest.advancePayment.refundDate = null;
                $scope.pendingPoSo = [];
                $scope.poSoSelected = null;
            };

            $scope.setEnterredText = function (text) {
                $scope.enterredText = text;
            };

            $scope.setAdjustOrRefund = function (flag) {
                $scope.paymentRequest.advancePayment.adjustOrRefund = flag;
                $scope.paymentRequest.advancePayment.refundDate = null;
                $scope.enterredText = null;
                if ($scope.paymentRequest.advancePayment.adjustOrRefund == 'Adjust') {
                    $scope.getSosForAdvance();
                }
            };

            $scope.getSosForAdvance = function () {
                $scope.pendingPoSo = [];
                $scope.errorMessage = null;
                $http({
                    method: 'GET',
                    url: apiJson.urls.paymentRequestManagement.getSosForAdvance,
                    params: {
                        vendorId: $scope.selectedVendor.id
                    }
                }).then(function success(response) {
                    $scope.pendingPoSo = response.data;
                    var finalPoSo = [];
                    for (var i = 0; i < $scope.pendingPoSo.length; i++) {
                        if ($scope.pendingPoSo[i].id != $scope.cancelObject.soId) {
                            finalPoSo.push($scope.pendingPoSo[i]);
                        }
                    }
                    $scope.pendingPoSo = finalPoSo;
                    angular.forEach($scope.pendingPoSo, function (poSo) {
                        poSo.maxLimit = parseFloat((poSo.totalAmount + appUtil.getAdvanceBufferAmount(poSo.totalAmount)).toFixed(0));
                    });
                }, function error(response) {
                    $scope.pendingPoSo = [];
                    if(response.data.errorMsg != null) {
                        $scope.errorMessage = response.data.errorMsg;
                        $alertService.alert(response.data.errorTitle, response.data.errorMsg, null, true);
                    } else{
                        $toastService.create("Error Occurred While Creating Advance Payment...!");
                        console.log("error:" + response);
                    }
                });
            };

            $scope.goToPrEditScreen = function () {
                $scope.paymentRequest.proposedAmount = angular.copy($scope.paymentRequest.paidAmount);
                $scope.advanceSelectionView = false;
                $scope.selectView = false;
            };

            $scope.disableSosSelection = function ($event, po, poSoList) {
                $scope.poSoSelected = null;
                if ($event.target.checked) {
                    if (po.maxLimit < $scope.paymentRequest.advancePayment['finalAvailable']) {
                        $toastService.create("Can not adjust to the SO which has less amount than the remaining advance..!");
                        po.checked = false;
                        return;
                    }
                    if (po.maxLimit > $scope.paymentRequest.advancePayment['finalAvailable']) {
                        $toastService.create("Can not adjust to the SO which has More amount than the Maximum Limit amount of SO Selected : " + po.maxLimit);
                        po.checked = false;
                        return;
                    }
                    po.disable = false;
                    angular.forEach(poSoList, function (poSo) {
                        if (po.id == poSo.id) {
                            poSo.disable =  false;
                            $scope.poSoSelected = po;
                        } else {
                            poSo.disable = true;
                        }
                    });
                } else {
                    $scope.poSoSelected = null;
                    angular.forEach(poSoList, function (poSo) {
                        poSo.disable = false;
                    });
                }
            };

            $scope.adjustAdvanceAmount = function (advances) {
                $scope.showPoSoClose = false;
                $scope.totalAdvancePrAmount = 0;
                $scope.totalAdvanceAvailableAmount = 0;
                $scope.totalAdvanceBlockedAmount = 0;
                $scope.advPaymentIds = [];
                var notCompletedPayment = null;
                for (var i = 0; i < advances.length; i++) {
                    if (notCompletedPayment == null && advances[i].advanceStatus == 'CREATED') {
                        notCompletedPayment = advances[i];
                    }
                    $scope.totalAdvanceAvailableAmount = $scope.totalAdvanceAvailableAmount + advances[i].availableAmount;
                    $scope.totalAdvancePrAmount = $scope.totalAdvancePrAmount + advances[i].prAmount;
                    $scope.totalAdvanceBlockedAmount = $scope.totalAdvanceBlockedAmount + advances[i].blockedAmount;
                    $scope.advPaymentIds.push(advances[i].advancePaymentId);
                }
                $scope.paymentRequest.advancePayment = {};
                 $scope.paymentRequest.advancePayment = angular.copy(notCompletedPayment);
                 $scope.paymentRequest.advancePayment.available = angular.copy($scope.totalAdvanceAvailableAmount);
                 $scope.paymentRequest.advancePayment.blocked = angular.copy($scope.totalAdvanceBlockedAmount);
                 $scope.paymentRequest.advancePayment.usedAmount = 0;
                 $scope.paymentRequest.advancePayment.finalAvailable = 0;
                 if ($scope.totalAdvanceAvailableAmount >= $scope.paymentRequest.proposedAmount) {
                     $scope.paymentRequest.advancePayment.finalAvailable = parseFloat(($scope.totalAdvanceAvailableAmount - $scope.paymentRequest.proposedAmount).toFixed(6));
                     $scope.paymentRequest.advancePayment.usedAmount = $scope.paymentRequest.proposedAmount;
                     $scope.paymentRequest.finalProposedAmount = 0;
                 } else {
                     $scope.paymentRequest.advancePayment.finalAvailable = 0;
                     $scope.paymentRequest.advancePayment.usedAmount = $scope.totalAdvanceAvailableAmount;
                     $scope.paymentRequest.finalProposedAmount = parseFloat(($scope.paymentRequest.proposedAmount - $scope.totalAdvanceAvailableAmount).toFixed(6));
                 }

                 if ($scope.paymentRequest.advancePayment.finalAvailable > 0) {
                     $scope.showPoSoClose = true;
                 }
            };

            $scope.initiatePR = function () {
                $scope.poSoSelected = null;
                $scope.eligibleForCardPayment = true;
                $scope.selectedSRs = angular.copy($scope.srs).filter(function (sr) {
                    return sr.checked;
                });
                $scope.getSelectedSrs = angular.copy($scope.selectedSRs);
                if($scope.selectedSRs.length==0){
                    $toastService.create("Please select at least 1 Receiving to go raise payment request!!");
                    return;
                }
                for (var i = 0; i < $scope.selectedSRs.length; i++) {
                    for (var j = 0; j < $scope.selectedSRs[i].serviceOrderList.length; j++) {
                        var so = $scope.selectedSRs[i].serviceOrderList[j];
                        if (so.type != undefined && so.type != null && so.type == "CAPEX") {
                            $scope.eligibleForCardPayment = false;
                            break;
                        }
                    }
                    if (!$scope.eligibleForCardPayment) {
                        break;
                    }
                }
                createPaymentRequestObject($scope.selectedSRs);
                $scope.selectView = false;
            };



            $scope.showRequiredDocuments = function(){
                $http({
                    method: 'POST',
                    url: apiJson.urls.serviceOrderManagement.showRequiredDocuments,
                    data: getCostElementIdList($scope.getSelectedSrs),

                }).then(function success(response) {
                    $scope.showSelectedDocuments = response.data;
                    $rootScope.rootLoading = false;
                }, function error(response) {
                    $toastService.create("Error in getting mapped documents")
                    $rootScope.rootLoading = false;
                });

            };

            $scope.downloadPRInvoice = function (prInvoice) {
                metaDataService.downloadDocument(prInvoice);
            };

            $scope.previewPRInvoice = function(prInvoice){
                if(!appUtil.isEmptyObject(prInvoice.documentLink)){
                    $http({
                        method:"POST",
                        url:apiJson.urls.vendorManagement.downloadDocument,
                        data: prInvoice,
                        responseType: 'arraybuffer'
                    }).then(function(response){
                        var arrayBufferView = new Uint8Array( response.data );
                        var blob = new Blob( [ arrayBufferView ], { type: appUtil.mimeTypes[prInvoice.mimeType] } );
                        var urlCreator = window.URL || window.webkitURL;
                        var imageUrl = urlCreator.createObjectURL( blob );
                        var preview = document.getElementById("invoicePreview");
                        preview.innerHTML = "";
                        var img = new Image();
                        img.src = imageUrl;
                        preview.appendChild(img);
                    },function(error){
                        $toastService.create("Could not download the document... Please try again");
                    });
                }else{
                    $toastService.create("Not a valid document... Please check");
                }
            };

  $scope.previewMandDocs = function(mandatoryDocs){
                if(!appUtil.isEmptyObject(mandatoryDocs)){
                    $http({
                        method:"POST",
                        url:apiJson.urls.vendorManagement.downloadDocument,
                        data: mandatoryDocs,
                        responseType: 'arraybuffer'
                    }).then(function(response){
                        var arrayBufferView = new Uint8Array( response.data );
                        var blob = new Blob( [ arrayBufferView ], { type: appUtil.mimeTypes[mandatoryDocs.mimeType] } );
                        var urlCreator = window.URL || window.webkitURL;
                        var imageUrl = urlCreator.createObjectURL( blob );
                        var preview = document.getElementById("mandatoryDocsPreview");
                        preview.innerHTML = "";
                        var img = new Image();
                        img.src = imageUrl;
                        preview.appendChild(img);
                    },function(error){
                        $toastService.create("Could not download the document... Please try again");
                    });
                }else{
                    $toastService.create("Not a valid document... Please check");
                }
            };
            /////////////////////document upload methods/////////////////////////////////

            $scope.resetScanModal = function () {
                $scope.imagesScanned = [];
                document.getElementById('images').innerHTML = "";
                var canvas = document.createElement('canvas');
                canvas.id = "scaleCanvas";
                document.getElementById('images').appendChild(canvas);
                $scope.uploadedDocData = null;
            };

            $scope.resetSnapModal = function () {
                $scope.snapRunning = false;
                if ($scope.localstream != null) {
                    $scope.localstream.getTracks()[0].stop();
                }
                $scope.uploadedDocData = null;
                var canvas = document.getElementById('canvas');
                var context = canvas.getContext('2d');
                context.clearRect(0, 0, 640, 480);
            };

            $scope.startSnap = function () {
                var video = document.getElementById('video');
                // Get access to the camera!
                if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                    // Not adding `{ audio: true }` since we only want video now
                    navigator.mediaDevices.getUserMedia({video: true}).then(function (stream) {
                        video.src = window.URL.createObjectURL(stream);
                        $scope.localstream = stream;
                        video.play();
                    });
                }
                $scope.snapRunning = true;
            };

            $scope.snapPicture = function () {
                var canvas = document.getElementById('canvas');
                var context = canvas.getContext('2d');
                var video = document.getElementById('video');
                context.drawImage(video, 0, 0, 640, 480);
                video.pause();
                video.src = "";
                $scope.localstream.getTracks()[0].stop();
                $scope.snapRunning = false;
            };

            function dataURItoBlob(dataURI) {
                var byteString = atob(dataURI.split(',')[1]);
                var ab = new ArrayBuffer(byteString.length);
                var ia = new Uint8Array(ab);
                for (var i = 0; i < byteString.length; i++) {
                    ia[i] = byteString.charCodeAt(i);
                }
                return new Blob([ab], {type: 'image/png'});
            }

            $scope.uploadFile = function () {
                var canvas = document.getElementById('canvas');
                var blob = dataURItoBlob(canvas.toDataURL("image/png"));
                var fd = new FormData(document.forms[0]);
                fd.append("file", blob);
                fd.append('type', "OTHERS");
                fd.append('docType', "PAYMENT_REQUEST_INVOICE");
                fd.append('mimeType', "PNG");
                fd.append('userId', $scope.currentUser.userId);
                $http({
                    url: apiJson.urls.paymentRequestManagement.uploadInvoice,
                    method: 'POST',
                    data: fd,
                    headers: {'Content-Type': undefined},
                    transformRequest: angular.identity
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (!appUtil.isEmptyObject(response)) {
                        $toastService.create("Upload successful");
                        $scope.uploadedDocData = response;
                    } else {
                        $toastService.create("Upload failed");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    $toastService.create("Upload failed");
                });
            };

            $scope.uploadReqDoc = function (docName) {
                $fileUploadService.openFileModal("Upload Document", "Find", function (file) {
                    if (file == null) {
                        $toastService.create('File cannot be empty');
                        return;
                    }
                    var fileExt = metaDataService.getFileExtension(file.name);
                    var fileLimit = fileExt.toLowerCase() == 'png' ?1024000  : 5120000;
                    if(file.size > fileLimit){
                        var msg = ""
                        if(fileExt.toLowerCase() == 'png'){
                            msg = "PNG Images are Not Compressed . For Larger Images Upload JPG/JPEG Format."
                        }
                        $toastService.create('File size should not be greater than ' + fileLimit/1024000 +  ' MB.' + msg);
                        return;
                    }

                    if (fileExt.toLowerCase() == 'pdf' || metaDataService.isImage(fileExt.toLowerCase())) {
                        var mimeType = fileExt.toUpperCase();
                        var fd = new FormData();
                        fd.append('type', "OTHERS");
                        fd.append('mimeType', fileExt.toUpperCase());
                        fd.append('userId', $scope.currentUser.userId);
                        fd.append('docType', "MANDATORY_UPLOADED_DOCUMENTS");
                        fd.append('file', file);
                        fd.append('docName', docName);
                        $http({
                            url: apiJson.urls.serviceOrderManagement.uploadRequiredDocuments,
                            method: 'POST',
                            data: fd,
                            headers: {'Content-Type': undefined},
                            transformRequest: angular.identity
                        }).success(function (response) {
                            $rootScope.showFullScreenLoader = false;
                            if (!appUtil.isEmptyObject(response)) {
                                $toastService.create("Upload successful");
                                $scope.requiredDocument[docName] = true;
                                $scope.mandatoryDocs[docName]=response;
                                $scope.addReqDocuments.push(response.documentId);
//                                if($scope.addReqDocuments.length>0) {
//                                    for(var i in $scope.addReqDocumentsName){
//                                        if($scope.addReqDocumentsName[i].code==response.documentLink.split("-")[0]){
//                                            for(var j in $scope.addReqDocuments){
//                                                if($scope.addReqDocuments[j] == $scope.addReqDocumentsName[i].name){
//                                                    $scope.addReqDocuments.splice(j,1);
//                                                    $scope.addReqDocumentsName.splice(i,1);
//                                                }
//                                            }
//                                        }
//                                    }
//                                }
                                $scope.addReqDocumentsName[response.documentLink.split("-")[0]] = response.documentId;
//                                $scope.addReqDocumentsName = [
//                                                                    {
//                                                                    name : response.documentId,
//                                                                    code: response.documentLink.split("-")[0],
//                                                                    }
//                                                             ]
//                                $scope.addReqDocuments.push(response.documentId);
                                $scope.isUploaded[docName] = true;
                            } else {
                                $toastService.create("Upload failed");
                            }
                        }).error(function (response) {
                            $rootScope.showFullScreenLoader = false;
                            if(response.errorCode == 702){
                                $alertService.alert(response.errorTitle, response.errorMsg,true)
                            }else{
                                $toastService.create("Upload failed");
                            }
                        });
                    } else {
                        $toastService.create('Upload Failed , File Format not Supported');
                    }
                });
            };

            $scope.uploadCardPaymentProof = function () {
                $fileUploadService.openFileModal("Upload Card Payment Proof", "Find", function (file) {
                    if (file == null) {
                        $toastService.create('File cannot be empty');
                        return;
                    }
                    var fileExt = metaDataService.getFileExtension(file.name);
                    var fileLimit = fileExt.toLowerCase() == 'png' ? 1024000 : 5120000;
                    if (file.size > fileLimit) {
                        var msg = ""
                        if (fileExt.toLowerCase() == 'png') {
                            msg = "PNG Images are Not Compressed . For Larger Images Upload JPG/JPEG Format."
                        }
                        $toastService.create('File size should not be greater than ' + fileLimit / 1024000 + ' MB.' + msg);
                        return;
                    }

                    if (fileExt.toLowerCase() == 'pdf' || metaDataService.isImage(fileExt.toLowerCase())) {
                        var fd = new FormData();
                        fd.append('type', "OTHERS");
                        fd.append('mimeType', fileExt.toUpperCase());
                        fd.append('userId', appUtil.getCurrentUser().userId);
                        fd.append('docType', "CARD_PAYMENT_PROOF");
                        fd.append('file', file);
                        fd.append('docName', "CARD_PAYMENT_PROOF");
                        $http({
                            url: apiJson.urls.paymentRequestManagement.uploadCardPaymentProof,
                            method: 'POST',
                            data: fd,
                            headers: {'Content-Type': undefined},
                            transformRequest: angular.identity
                        }).success(function (response) {
                            $rootScope.showFullScreenLoader = false;
                            if (!appUtil.isEmptyObject(response)) {
                                $scope.uploadedCardPaymentProof = response;
                            } else {
                                $toastService.create("Upload failed");
                                $scope.uploadedCardPaymentProof = null;
                            }
                        }).error(function (response) {
                            $rootScope.showFullScreenLoader = false;
                            if (response.errorMsg != null) {
                                $alertService.alert(response.errorTitle, response.errorMsg, true)
                            } else {
                                $toastService.create("Upload failed");
                            }
                            $scope.uploadedCardPaymentProof = null;
                        });
                    } else {
                        $toastService.create('Upload Failed , File Format not Supported');
                        $scope.uploadedCardPaymentProof = null;
                    }
                });
            };

            $scope.downloadPaymentProof = function () {
                metaDataService.downloadDocument($scope.uploadedCardPaymentProof);
            };

            $scope.validateRequiredDocuments = function() {
//            $scope.isAllDocumentsUploaded = true;
//                for(var x in $scope.showSelectedDocuments) {
//                    if($scope.requiredDocument[$scope.showSelectedDocuments[x]]===null
//                        || $scope.requiredDocument[$scope.showSelectedDocuments[x]]===undefined ) {
//                      $toastService.create('Upload All Required Document');
//                      $scope.isAllDocumentsUploaded = false;
//                      return;
//                    }
//                }
                Popeye.closeCurrentModal();
            }

             $scope.uploadDoc = function () {
                            $fileUploadService.openFileModal("Upload Invoice Document", "Find", function (file) {
                                if (file == null) {
                                    $toastService.create('File cannot be empty');
                                    return;
                                }
                                var fileExt = metaDataService.getFileExtension(file.name);
                                var fileLimit = fileExt.toLowerCase() == 'png' ?1024000  : 5120000;
                                if(file.size > fileLimit){
                                    var msg = ""
                                    if(fileExt.toLowerCase() == 'png'){
                                        msg = "PNG Images are Not Compressed . For Larger Images Upload JPG/JPEG Format."
                                    }
                                    $toastService.create('File size should not be greater than ' + fileLimit/1024000 +  ' MB.' + msg);
                                    return;
                                }

                                if (fileExt.toLowerCase() == 'pdf' || metaDataService.isImage(fileExt.toLowerCase())) {
                                    var mimeType = fileExt.toUpperCase();
                                    var fd = new FormData();
                                    fd.append('type', "OTHERS");
                                    fd.append('docType', "PAYMENT_REQUEST_INVOICE");
                                    fd.append('mimeType', fileExt.toUpperCase());
                                    fd.append('userId', $scope.currentUser.userId);
                                    fd.append('file', file);
                                    $http({
                                        url: apiJson.urls.paymentRequestManagement.uploadInvoice,
                                        method: 'POST',
                                        data: fd,
                                        headers: {'Content-Type': undefined},
                                        transformRequest: angular.identity
                                    }).success(function (response) {
                                        $rootScope.showFullScreenLoader = false;
                                        if (!appUtil.isEmptyObject(response)) {
                                            $toastService.create("Upload successful");
                                            $scope.uploadedDocData = response;

                                        } else {
                                            $toastService.create("Upload failed");
                                        }
                                    }).error(function (response) {
                                        $rootScope.showFullScreenLoader = false;
                                        if(response.errorCode == 702){
                                            $alertService.alert(response.errorTitle, response.errorMsg,true)
                                        }else{
                                            $toastService.create("Upload failed");
                                        }
                                    });
                                } else {
                                    $toastService.create('Upload Failed , File Format not Supported');
                                }
                            });
                        };

            $scope.scanToPng = function () {
                scanner.scan($scope.displayImagesOnPage,
                    {
                        "output_settings": [
                            {
                                "type": "return-base64",
                                "format": "png"
                            }
                        ]
                    }
                );
            };

            $scope.displayImagesOnPage = function (successful, mesg, response) {
                if (!successful) { // On error
                    console.error('Failed: ' + mesg);
                    return;
                }
                if (successful && mesg != null && mesg.toLowerCase().indexOf('user cancel') >= 0) { // User cancelled.
                    console.info('User cancelled');
                    return;
                }
                var scannedImages = scanner.getScannedImages(response, true, false); // returns an array of ScannedImage
                $scope.imagesScanned = [];
                $scope.processScannedImage(scannedImages[0]);
            };

            $scope.processScannedImage = function (scannedImage) {
                $scope.imagesScanned.push(scannedImage);
                scaleImage(scannedImage.src);
            };

            function scaleImage(src) {
                var MAX_WIDTH = 1000;
                var image = new Image();
                var canvas = document.getElementById("scaleCanvas");
                image.onload = function () {
                    //var canvas = document.getElementById("scaleCanvas");
                    if (image.width > MAX_WIDTH) {
                        image.height *= MAX_WIDTH / image.width;
                        image.width = MAX_WIDTH;
                    }
                    var ctx = canvas.getContext("2d");
                    ctx.clearRect(0, 0, canvas.width, canvas.height);
                    canvas.width = image.width;
                    canvas.height = image.height;
                    ctx.drawImage(image, 0, 0, image.width, image.height);
                };
                image.src = src;
            }

            $scope.uploadScannedFile = function () {
                var canvas = document.getElementById('scaleCanvas');
                var blob = dataURItoBlob(canvas.toDataURL("image/png"));
                var fd = new FormData(document.forms[0]);
                fd.append("file", blob);
                fd.append('type', "OTHERS");
                fd.append('docType', "PAYMENT_REQUEST_INVOICE");
                fd.append('mimeType', "PNG");
                fd.append('userId', $scope.currentUser.userId);
                $http({
                    url: apiJson.urls.paymentRequestManagement.uploadInvoice,
                    method: 'POST',
                    data: fd,
                    headers: {'Content-Type': undefined},
                    transformRequest: angular.identity
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (!appUtil.isEmptyObject(response)) {
                        $toastService.create("Upload successful");
                        $scope.uploadedDocData = response;
                    } else {
                        $toastService.create("Upload failed");
                    }
                }).error(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    $toastService.create("Upload failed");
                });
            };

            ///////////////////utility functions/////////////////////////////

            function addNewInvoiceItem(invoiceItems, item) {
                var invoiceItem = {
                    skuId: item.costElementId,
                    skuName: item.costElementName,
                    hsn: item.ascCode,
                    uom: item.unitOfMeasure,
                    conversionRatio: 1,
                    quantity: item.receivedQuantity,
                    totalAmount: item.totalAmount,
                    totalTax: item.totalTaxes,
                    totalPrice: item.totalCost,
                    unitPrice: item.unitPrice,
                    taxes: [],
                    deviations: [],
                    showDeviationInput: true
                };
                item.taxes.map(function (tax) {
                    invoiceItem.taxes.push({
                        taxDetailId: null,
                        taxType: tax.taxCategory,
                        taxPercentage: tax.percentage,
                        taxValue: tax.value
                    });
                });
                invoiceItems.push(invoiceItem);
            }

            function addToExistingItem(item, grItem) {
                item.quantity += grItem.receivedQuantity;
                item.totalAmount += grItem.amountPaid;
                item.totalTax += grItem.totalTax;
                item.totalPrice += grItem.totalCost;
            }

            function getCostElementIdList(getSelectedSrs){
            var items = [];
            for(var i in getSelectedSrs){
                    var serviceItems = [];
                     serviceItems = getSelectedSrs[i].serviceReceiveItems;
                     for(var j in serviceItems){
                     var ceIds = serviceItems[j].costElementId;
                     items.push(ceIds);
                     }
            }
            return items;
            }

            function getInvoiceItems(selectedSRs) {
                var items = [];
                for(var i in selectedSRs){
                    var rcvItems = selectedSRs[i].serviceReceiveItems;
                    Array.prototype.push.apply(items, rcvItems);
                }
                return items;
            }

            function getInvoiceAmount(selectedSRs) {
                var amount = 0;
                for(var i in selectedSRs){
                    amount += parseFloat(selectedSRs[i].totalAmount);
                }
                return amount;
            }

            function getTotalCost(selectedSRs){
                var totalCost = 0;
                for(var i in selectedSRs){
                    totalCost += (selectedSRs[i].totalAmount - selectedSRs[i].totalTaxes);
                }
                return totalCost;
            }

            function createPaymentRequestObject(selectedSRs) {
                var invoiceItems = getInvoiceItems(selectedSRs);
                console.log(invoiceItems)
                invoiceItems.forEach(function (item){
                    item.totalAmount = 0;
                    item.totalCost = 0;
                    item.totalTax = 0;
                    item.invoiceAmount = 0;
                    item.remainingTotalAmount=roundToSixDecimal(item.receivedQuantity*item.unitPrice);
                    item.remainingTaxAmount=getTaxPrice(item.remainingTotalAmount,item.taxRate);
                });
               var amount = calculateProposedAmount(invoiceItems);
                var invoice = {
                    invoiceNumber: null,
                    invoiceDate: null,
                    invoiceDocumentHandle: "",
                    paymentInvoiceItems: invoiceItems,
                    paymentAmount: 0,
                    invoiceAmount: 0,
                    calculatedInvoiceAmount: 0,
                    deviations: []
                };

                $scope.paymentRequest = {
                    type: $scope.prRequestType.code,
                    vendorId: {id: $scope.selectedVendor.id},
                    createdBy: appUtil.createGeneratedBy(),
                    paymentInvoice: invoice,
                    invoiceNumber : invoice.invoiceNumber,
                    proposedAmount: 0,
                    paidAmount: 0,
                    amountsMatch: false,
                    deviationCount: 0,
                    companyId :$scope.selectedCompany.id,
                    company: $scope.selectedCompany,
                    deliveryLocation: $scope.selectedLocation,
                    requestItemMappings: addGrItemMapping(selectedSRs),
                    grDocType : 'INVOICE'
                };
            }

            $scope.changeProposedAmount = function (tdsRate) {
                if (!appUtil.isEmptyObject(tdsRate)) {
                    var items = $scope.paymentRequest.paymentInvoice.paymentInvoiceItems;
                    $scope.paymentRequest.proposedAmount = calculateProposedAmount(items);
                }
            };
            function roundToSixDecimal(price) {
                return Number(price.toFixed(6));
            }
            function calculateProposedAmount(items) {
                var amount = 0;
                for(var i in items){
                    var item = items[i];
                    amount = parseFloat(amount) + parseFloat(parseFloat(item.totalCost)) + parseFloat(item.totalTax);
                }
                return amount.toFixed(2);
            }

            function getTds(totalCost, tdsRate) {
                var tds = appUtil.isEmptyObject(tdsRate) ? 0 : parseFloat(tdsRate);
                return parseFloat(parseFloat(totalCost) * (tds/100)).toFixed(2);
            }

            function addGrDataToInvoice(gr) {
                $scope.paymentRequest.proposedAmount += (gr.billAmount + gr.extraCharges);
                $scope.paymentRequest.paidAmount += (gr.billAmount + gr.extraCharges);
                $scope.paymentRequest.paymentInvoice.calculatedInvoiceAmount += gr.billAmount;
                $scope.paymentRequest.paymentInvoice.extraCharges += gr.extraCharges;
                $scope.paymentRequest.paymentInvoice.invoiceAmount += (gr.billAmount + gr.extraCharges);
                $scope.paymentRequest.paymentInvoice.paymentAmount += (gr.billAmount + gr.extraCharges);
            }

            function addGrItemMapping(selectedSRs) {
                var mappings = [];
                for(var i in selectedSRs){
                    mappings.push({
                        paymentRequestType: $scope.prRequestType.code,
                        paymentRequestItemId: selectedSRs[i].id
                    });
                }
                return mappings;
            }

            $scope.viewDetail = function(sr){
                var viewDetailModal = Popeye.openModal({
                    templateUrl: "viewSRDetail.html",
                    controller: "viewSRDetailCtrl",
                    resolve: {
                        sr: function(){
                            return sr;
                        }
                    },
                    modalClass:'custom-modal',
                    click: false,
                    keyboard: false
                });
            };
        }
    ]
);
