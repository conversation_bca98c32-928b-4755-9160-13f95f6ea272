/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2022] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */


angular.module('scmApp').controller('skuPackagingTaxMappingCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil',
     '$alertService','$toastService', 'metaDataService', '$timeout',
    function ($rootScope, $scope, apiJson, $http, appUtil,  $alertService,$toastService , metaDataService,$timeout) {
        $scope.init = function () {
            $scope.skuTaxMap = {};
            $scope.getAllSkus();
            getAllMappings();
            $scope.packagingMap = appUtil.getPackagingMap();
            $scope.getAllUnitList();
            $scope.getRegions();
            $scope.taxCodes = appUtil.getTaxProfiles();
            $scope.gridOptions = appUtil.getGridOptions($scope);
            $scope.gridOptions['enableSelectAll'] = false;
            $scope.gridOptions['onRegisterApi'] = function (gridApi) {
                $scope.gridApi = gridApi;
                $scope.gridApi.core.on.filterChanged($scope, function () {
                    $timeout(function () {
                        angular.forEach($scope.gridApi.selection.getSelectedGridRows(), function (row) {
                            if (!row.visible) $scope.gridApi.selection.selectRow(row.entity);
                        });
                    });
                });

                $scope.gridApi.selection.on.rowSelectionChanged($scope, function (row) {
                    if (row.isSelected) {
                        row.entity.status = 'ACTIVE';
                    } else {
                        row.entity.status = 'IN_ACTIVE'
                    }
                });
            }
            $scope.showGrid = false;
            $scope.currentSku = null;
            $scope.currentPackaging = null;
            $scope.submitted = false;
            $scope.bulkEdit = {
                region: null,
                taxCategory: null
            }
        };

        $scope.submit = function (){
            $scope.unitTaxMap = {};
            $scope.allGridViewShow = [];
            $scope.submitted = true;
            $scope.showGrid = false;
            $scope.gridApi.selection.getSelectedRows().forEach(function (row) {
                    $scope.unitTaxMap[row.id] = row.taxCategory.code;
                    var tempObj = {
                        id : row.id,
                        name : row.name,
                        category : row.category,
                        region : row.subCategory,
                        mappingStatus : row.status,
                        taxCategory : row.taxCategory
                    };
                    $scope.allGridViewShow.push(tempObj);
            });
        }

        $scope.confirmMappings = function (){
            updateMappings($scope.unitTaxMap);
            $scope.submitted = false;
            $scope.showGrid = true;
            $scope.init();
        }

        function updateMappings(unitToTaxMap){
            $http({
                method: "POST",
                url: apiJson.urls.skuMapping.updateUnitSkuPackagingTaxMappings,
                params : {
                    skuId : $scope.currentSku.id,
                    packagingId : $scope.currentPackaging.packagingId
                },
                data : unitToTaxMap
            }).then(function (response) {
                if(response.data){
                    $toastService.create("Successfully Updated Mappings !!");
                }
            }, function (response) {
                console.log("Error while updating mappings", response);
                $toastService.create("Error while updating mappings");
            });
        }



       /* $scope.getAllUnitList = function () {
            metaDataService.getUnitList(function (response) {
                $scope.allUnitsList = response;/!*.filter(function (value) {
                        return value.code == 'WAREHOUSE' || value.code == 'KITCHEN' || value.code == 'OFFICE';
                    });*!/
            });
        };*/

        $scope.showGridData = function (){
            transformGridData();
            $scope.gridOptions.columnDefs = $scope.gridColumns();
            $scope.gridOptions.data = $scope.allUnitsList;
            $timeout(function () {
                $scope.preSelectData();
            });
            $scope.showGrid = true;
        }


        $scope.gridColumns = function () {
            return [{
                field: 'id',
                name: 'id',
                enableCellEdit: false,
                displayName: 'unit Id',
            }, {
                field: 'name',
                name: 'name',
                enableCellEdit: false,
                displayName: 'Unit name'
            }, {
                field: 'category',
                name: 'category',
                enableCellEdit: false,
                displayName: 'Category'
            }, {
                field: 'subCategory',
                name: 'region',
                enableCellEdit: false,
                displayName: 'region'
            }, {
                field: 'status',
                name: 'status',
                enableCellEdit: false,
                displayName: 'Mapping Status'
            },
                {
                    name: 'Tax Category',
                    cellTemplate: 'taxCode.html',
                    displayName: 'Tax Category',
                    enableCellEdit: false
                }


            ];
        };

        $scope.confirmBulkEdit = function () {
            if (!appUtil.isEmptyObject($scope.bulkEdit.region) && !appUtil.isEmptyObject($scope.bulkEdit.taxCategory)) {
                updateBulkRegionTax();
                $scope.cancelTaxModal();
                $timeout(function (){
                    $('#region').val('').trigger('change');
                    $('#bulkTaxCategory').val('').trigger('change');

                });
                $scope.bulkEdit.region = null;
                $scope.bulkEdit.taxCategory = null;
            } else {
                $toastService.create("Please Select Both Region And Tax Category For Bulk Edit !!");
            }
        }

        function updateBulkRegionTax() {
            $scope.gridOptions.data.forEach(function (row, index) {
                if (row.subCategory === $scope.bulkEdit.region || $scope.bulkEdit.region === "ALL" ) {
                    $scope.gridOptions.data[index].taxCategory = $scope.bulkEdit.taxCategory;
                    $scope.gridApi.selection.selectRow($scope.gridOptions.data[index]);
                }
            });
            $timeout(function (){
                $scope.$apply();
            })

        }


        $scope.cancelTaxModal = function () {
            $timeout(function () {
                $('#bulkAddTax').val('').trigger('change');
            });

        };

        $scope.closeBulkEditModal = function () {
            $timeout(function () {
                $('#addTax').val('').trigger('change');
            });
        }

        $scope.onTaxCategoryChange = function (row) {
            $scope.row = row;
            $timeout(function () {
                $('#taxCategory').trigger('change');
            })

        }


        function setTaxCode(sku){
            for(var i in $scope.taxCodes){
                if($scope.taxCodes[i].code == sku.taxCode){
                    sku.taxCategory = angular.copy($scope.taxCodes[i]);
                    break;
                }
            }
            sku.taxCategories = $scope.taxCodes;
        }

        $scope.preSelectData = function () {
            $scope.gridApi.grid.modifyRows($scope.gridOptions.data);
            $scope.gridOptions.data.forEach(function (row, index) {
                if (row.check) {
                    $scope.gridApi.selection.selectRow($scope.gridOptions.data[index]);
                } else {
                    $scope.gridApi.selection.unSelectRow($scope.gridOptions.data[index]);
                }
            });
        };


        function transformGridData(){
            for(var i in $scope.allUnitsList){
                if(!appUtil.isEmptyObject($scope.unitSkuPackaingTaxMapping) && !appUtil.isEmptyObject($scope.unitSkuPackaingTaxMapping[$scope.allUnitsList[i].id])){
                    $scope.allUnitsList[i]["taxCode"] = $scope.unitSkuPackaingTaxMapping[$scope.allUnitsList[i].id].taxCode;
                    $scope.allUnitsList[i]["check"] = $scope.unitSkuPackaingTaxMapping[$scope.allUnitsList[i].id].mappingStatus === "ACTIVE" ? true : false;
                }else{
                    $scope.allUnitsList[i]["taxCode"] = $scope.skuTaxMap[$scope.currentSku.id];
                    $scope.allUnitsList[i]["check"] = false;
                }
                $scope.allUnitsList[i]["status"] =  $scope.allUnitsList[i]["check"] ? "ACTIVE" : "IN_ACTIVE";
                setTaxCode($scope.allUnitsList[i]);
            }

        }

        $scope.getAllUnitList = function () {
            metaDataService.getUnitList(function (response) {
                $scope.allUnitsList = response;
            });
        };

        $scope.resetSelection = function (){
            $scope.selectedPackaging = null;
            $timeout(function (){
                $('#skuPackagings').val('').trigger('change');
            })

        }


        $scope.getAllSkus = function () {
            $http({
                method: "GET",
                dataType: 'json',
                data: '',
                headers: {
                    "Content-Type": "application/json"
                },
                url: apiJson.urls.skuMapping.getAllSku
            }).then(function success(response) {
                $scope.skuList = response.data;
                createSkuTaxMap();

            }, function (response) {
                console.log("got error", response);
            });
        };

        function createSkuTaxMap(){

            for(var i in $scope.skuList){
                $scope.skuTaxMap[$scope.skuList[i].id] = $scope.skuList[i].taxCategoryCode;
            }
        }

        function transformPackaingMap(tempSkuMappings){
            $scope.skuMapping = {};
            Object.keys(tempSkuMappings).forEach(function(skuId){
                $scope.skuMapping[skuId] = [];
                for(var packaging in tempSkuMappings[skuId]){
                    if(tempSkuMappings[skuId][packaging].mappingStatus === "ACTIVE") {
                        $scope.skuMapping[skuId].push($scope.packagingMap[tempSkuMappings[skuId][packaging].packagingId]);
                    }
                }
            });
        }

        function getAllMappings() {
            $http({
                method: "GET",
                url: apiJson.urls.productManagement.skuPackagingMappings
            }).then(function (response) {
                var tempSkuMappings = response.data;
                transformPackaingMap(tempSkuMappings);
            }, function (response) {
                console.log("Error while getting mappings", response);
            });
        }

        $scope.getRegions = function (){
            $http({
                method: "GET",
                url: apiJson.urls.unitMetadata.regions
            }).then(function (response) {
                $scope.regions = response.data;
                $scope.regions.push("ALL");
            }, function (response) {
                console.log("Error while getting Regions", response);
            });
        }



        $scope.getUnitSkuPackagingTaxMappings = function (sku, packaging) {
            $scope.currentSku = sku;
            $scope.currentPackaging = packaging;
            $scope.unitSkuPackaingTaxMapping = {};
            $http({
                method: "GET",
                url: apiJson.urls.skuMapping.getUnitSkuPackagingTaxMappings,
                params: {
                    skuId: $scope.currentSku.id,
                    packagingId : $scope.currentPackaging.packagingId
                }
            }).then(function (response) {
                if(response.data!=null){
                    $scope.unitSkuPackaingTaxMapping = response.data;
                    $scope.showGridData();
                }
            }, function (response) {
                console.log("Error while getting Unit Sku Packaging Tax mappings", response);
            });
        }

    }]);
