angular.module('scmApp').controller(
    'costElementToDocumentMapCtrl',
    ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$location', '$toastService', 'metaDataService',
        '$fileUploadService', '$alertService', 'previewModalService', 'Popeye', '$timeout', '$window',
        function ($rootScope, $scope, apiJson, $http, appUtil, $location, $toastService, metaDataService, $fileUploadService, $alertService,
                  previewModalService, Popeye, $timeout, $window) {

            $scope.init = function () {
            	$scope.getCostElement();
            	$scope.mappingTable = [];
            	$scope.hideModalGrid = true;
                $scope.getDocument();

            }


            $scope.getCostElement = function(){
				$http({
					method : 'GET',
					url : apiJson.urls.serviceMappingManagement.getCostElementData,
				}).then(function success(response) {
					if(response.data != null){
						$scope.costElements = response.data;
					}
				}, function error(response) {
		               console.log("error:" + response);
		          })
            }

            $scope.getDocument=function(){
                $http({
                    method : 'GET',
                    url : apiJson.urls.serviceMappingManagement.getDocumentList,
                }).then(function success(response){
                    if(response.data!=null){
                        $scope.documents = response.data;
                    }
                }, function error(response) {
                        console.log("error" + response);
                    })
            }

            $scope.searchMappings = function () {
                if ($scope.selectedCostElement == undefined || $scope.selectedCostElement == null
                    || $scope.selectedCostElement.id == 0) {
                    return;
                }
                $scope.getCostElementDocumentMappingsGrid();
            }

            $scope.getCostElementDocumentMappingsGrid = function () {
                $scope.gridOptions = $scope.DocumentGridOptions();
                $scope.getAllCostDocumentMappings();
            }

            $scope.getAllCostDocumentMappings = function(){
				$http({
					method : 'GET',
					url : apiJson.urls.serviceMappingManagement.getCostElementToDocumentMapping,
					params : {
						costElementId : $scope.selectedCostElement.id
					}
				}).then(function success(response) {
					if(response.data != null){
						$scope.gridOptions.data = response.data;
					}
				}, function error(response) {
		               console.log("error:" + response);
		          })
            }

            $scope.openCostElementToDocumentMapModal = function () {
            	 $scope.newMappingList = [];
                if ($scope.selectedCostElement != undefined && $scope.selectedCostElement.id > 0) {
                	 $scope.getModalSkuMappGrid();
                }
            }

            $scope.getModalSkuMappGrid = function () {
                $scope.modalGridOptions = $scope.DocumentGridOptions();
                $scope.addModalGridOptions = $scope.AddGridOptions();
                $scope.modalGridOptions.data = [];
                $scope.addModalGridOptions.data = [];
            }

            $scope.DocumentGridOptions = function () {
                return {
                    enableFiltering: true,
                    enableColumnResizing: true,
                    enableColumnMenus: false,
                    columnDefs: [{
                        field: 'id',
                        displayName: 'Document Id'
                    }, {
                        field: 'name',
                        displayName: 'Document Name'
                    }, {
                        field: 'code',
                        displayName: 'Document Code',
                    }, {
                        field: 'status',
                        displayName: 'Action',
                        cellTemplate: 'statusChangeRemoveButton.html'
                    }]
                };
            }

            $scope.AddGridOptions = function () {
                            return {
                                enableFiltering: true,
                                enableColumnResizing: true,
                                enableColumnMenus: false,
                                columnDefs: [{
                                    field: 'id',
                                    displayName: 'Document Id'
                                }, {
                                    field: 'documentName',
                                    displayName: 'Document Name'
                                }, {
                                    field: 'documentCode',
                                    displayName: 'Document Code',
                                }, {
                                    field: 'status',
                                    displayName: 'Action',
                                    cellTemplate: 'removeButton.html'
                                }]
                            };
                        }

            $scope.removeRow = function (value) {
                var index = $scope.gridOptions.data.indexOf(value);
                $scope.gridOptions.data.splice(index, 1);
            }

            $scope.removeRowInModal = function (value) {
                            var index = $scope.modalGridOptions.data.indexOf(value);
                            $scope.modalGridOptions.data.splice(index, 1);
                        }

            $scope.cancelModal = function () {
                $scope.hideModalGrid = true;
                $timeout(function () {
                    $('#modalValueDataId').val('').trigger('change');
                });
            }

            $scope.addToModalGridData = function () {
            var duplicateMapping = false;
                $scope.hideModalGrid = false;
                data = $scope.selectedDocument;
                if (data == undefined || data == null) {
                    return;
                }
                for(var i in $scope.newMappingList){
                if(data.id == ($scope.newMappingList[i]).id){
                 duplicateMapping = true;
                }
                }
                if (duplicateMapping) {
                    $toastService.create('Duplicate Mapping');
                } else {
                    $scope.newMappingList.push(data);
                    $scope.modalGridOptions.data = $scope.newMappingList;
                    $scope.addModalGridOptions.data = $scope.newMappingList;
                }
            }

            $scope.submitModalGridData = function () {
                var list = [];
                var x;
                for (x in $scope.newMappingList) {
                    list.push($scope.newMappingList[x].id);
                }
                var currentUser = appUtil.getCurrentUser();
                payload = {
                    id: $scope.selectedCostElement.id,
                    mappingIds: list,
                    employeeId: currentUser.userId,
                    employeeName: currentUser.user.name
                }
                $http({
                    url: apiJson.urls.serviceMappingManagement.addCostElementToDocumentMapping,
                    method: 'POST',
                    data: payload
                }).then(function (response) {
                    if (response) {
                        $toastService.create('Mappings Added Successfully');
                        $scope.cancelModal();
                        $scope.searchMappings();
                    }
                }, function (response) {
                    console.log("error", response);
                });
            }

            $scope.changeStatus = function (value) {
                var documentId = null;
                    documentId = value.id;
                    costElementId = $scope.selectedCostElement.id;
                var payload = {
                    documentId: documentId,
                    costElementId: costElementId
                }

                $http({
                    url: apiJson.urls.serviceMappingManagement.updateCostElementDocumentMapping,
                    method: 'POST',
                    data: payload
                }).then(function (response) {
                    if (response.data == true) {

                        $toastService.create("Mapping removed successfully");
                        $scope.removeRow(value);
                    }
                }, function (response) {
                    console.log("error", response);
                });
            };





    }
]
);
