/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 16-06-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('grOrderMgtCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil','$location','$toastService','metaDataService',
        function ($rootScope, $scope, apiJson, $http, appUtil,$location, $toastService , metaDataService) {

            $scope.init = function () {
                $scope.selectedSku = null;
                metaDataService.getSkuListForUnit(appUtil.getCurrentUser().unitId,function(skuForUnitList){
                    $scope.skuList = skuForUnitList;
                });
                var grOrderListObj = appUtil.getGrOrderListObj();
                $scope.scmOrderStatusList = [];
                $scope.scmOrderStatusList.push("");
                $scope.scmOrderStatusList = $scope.scmOrderStatusList.concat(appUtil.getMetadata().scmOrderStatus);
                $scope.status = grOrderListObj.status==null?$scope.scmOrderStatusList[0]:grOrderListObj.status;
                $scope.goodsReceiveOrderList = grOrderListObj.goodsReceiveOrderList;
                $scope.goodsReceiveOrderId = grOrderListObj.goodsReceiveOrderId;
                $scope.startDate = grOrderListObj.startDate==null?appUtil.formatDate(new Date(), "yyyy-MM-dd"):grOrderListObj.startDate;
                $scope.endDate = grOrderListObj.endDate==null?appUtil.formatDate(new Date(), "yyyy-MM-dd"):grOrderListObj.endDate;
                $scope.unitList = [];
                $scope.unitList.push({
                    id: null,
                    name: ""
                });
                $scope.unitList = $scope.unitList.concat(appUtil.getUnitList());
                $scope.generationUnit = grOrderListObj.generationUnit==null?$scope.unitList[0]:grOrderListObj.generationUnit;
                $scope.goodsReceiveOrderList = grOrderListObj.goodsReceiveOrderList;
                $scope.findGoodsReceiveOrders();
                $scope.gridOptions = appUtil.getGridOptions($scope);
                $scope.multiSelectSettings = {showEnableSearchButton: true, template: '<b>{{option}}</b>'};
            }

            $scope.findGoodsReceiveOrders = function(){
            	 if($scope.startDate==null || $scope.startDate.trim() == ''|| $scope.endDate==null || $scope.endDate.trim() == ''){
            		 $toastService.create("Please fill start date and end date properly!");
            		 return false;
                }else if($scope.startDate != null && $scope.endDate != null && $scope.startDate > $scope.endDate) {
	           		 $toastService.create("Please fill end date greater than or equal to start date properly!");
	                 return false;
                }
                 else{
                    var url = apiJson.urls.goodsReceivedManagement.goodReceivedFind+"?generatedForUnitId="+
                        appUtil.getCurrentUser().unitId+"&startDate="+$scope.startDate+"&endDate="+$scope.endDate;
                    if($scope.goodsReceiveOrderId!=null){
                        url+="&goodsReceiveOrderId="+$scope.goodsReceiveOrderId;
                    }
                    if($scope.generationUnit.id!=null){
                        url+="&generationUnitId="+$scope.generationUnit.id;
                    }
                    if($scope.status!=null && $scope.status!=""){
                        url+="&status="+$scope.status;
                    }
                    if($scope.selectedSku!=null){
                        url+="&skuId="+$scope.selectedSku.id;
                    }
                    $http({
                        method: "GET",
                        url: url
                    }).then(function success(response) {
                        $scope.goodsReceiveOrderList = response.data;
                        $scope.gridOptions.columnDefs = $scope.gridColumns();
                        $scope.gridOptions.data = $scope.goodsReceiveOrderList;
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                }
            }

            $scope.gridColumns  = function () {
                return [{
                    field: 'id',
                    name: 'GR Id',
                    enableCellEdit: false,
                    displayName: 'GR Id'
                }, {
                    field: 'transferOrderId',
                    name: 'TO ID',
                    enableCellEdit: false,
                    displayName: 'TO ID'
                }, {
                    field: 'generationUnitId.name',
                    name: 'Transferring Unit',
                    enableCellEdit: false,
                    displayName: 'Transferring Unit'
                }, {
                    field: 'requestOrderId',
                    name: 'Request Order id',
                    enableCellEdit: false,
                    displayName: 'Request Order id'
                },
                    {
                        field: 'receivedBy.name',
                        name: 'Generated By',
                        enableCellEdit: false,
                        displayName: 'Generated By'
                    },{
                        field: 'cancelledBy.name',
                        name: 'Cancelled By',
                        enableCellEdit: false,
                        displayName: 'Cancelled By'
                    },{
                    field: 'lastUpdateTime',
                    name: 'Last Update Time',
                    enableCellEdit: false,
                    displayName: 'Last Update Time',
                    cellFilter: 'date:\'yyyy-MM-dd hh:mm:ss a\':\'+0530\'', width: 200,
                    sortingAlgorithm: function (aDate, bDate) {
                        var a=new Date(aDate);
                        var b=new Date(bDate);
                        if (a < b) {
                            return -1;
                        }
                        else if (a > b) {
                            return 1;
                        }
                        else {
                            return 0;
                        }
                    }
                },
                    {field: 'status',
                        name: 'status',
                        enableCellEdit: false,
                        displayName: 'Status'
                    },
                    {
                        name : 'Gr View',
                        cellTemplate :' <div  class="ui-grid-cell-contents"> <button type="button" class = "btn btn-xs-small"  ng-click="grid.appScope.openGrOrderAction(row.entity.id)" >GR View </button> </div>  '
                    }
                ]
            }


            $scope.openGrOrderAction = function(grId){
                $rootScope.selecteGrOrderId = grId;
                appUtil.setGrOrderListObj({
                    startDate: $scope.startDate,
                    endDate: $scope.endDate,
                    goodsReceiveOrderId: $scope.goodsReceiveOrderId,
                    generationUnit: $scope.generationUnit,
                    status: $scope.status,
                    goodsReceiveOrderList: $scope.goodsReceiveOrderList
                });
                $location.path("/menu/grOrderAction");
            }

        }]);
