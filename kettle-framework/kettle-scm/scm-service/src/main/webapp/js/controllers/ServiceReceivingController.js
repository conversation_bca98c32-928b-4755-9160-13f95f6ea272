'use strict';

angular.module('scmApp')
    .controller('createServiceRcvCtrl', ['$rootScope', '$scope','$state', 'apiJson', '$http', 'appUtil','metaDataService','$fileUploadService',
        '$toastService','$alertService','Popeye','$timeout', function ($rootScope, $scope, $state, apiJson, $http, appUtil, metaDataService,
                                $fileUploadService,$toastService, $alertService, Popeye, $timeout) {

            function getMinDate(soList){
                if(!appUtil.isEmptyObject(soList) && soList.length>0){
                    return soList[0].generationTime;
                }else{
                    return appUtil.getDate(-365);
                }
            }

            $scope.init = function (){
                $scope.maxDate = appUtil.formatDate(appUtil.getCurrentBusinessDate(), "yyyy-MM-dd");
                $scope.minDate = appUtil.formatDate(getMinDate(), "yyyy-MM-dd");
                $scope.selectedVendor = null;
                $scope.locationList = [];
                $scope.selectedOrders = [];
                $scope.showExpandedView = false;
                $scope.srItems = null;
                $scope.receivings = {};
                $scope.currentUser = appUtil.getCurrentUser();
                $scope.filled = false;

                metaDataService.getCompanyList(function(companies){
                    $scope.companyList = companies;
                });
                metaDataService.getServiceVendors(function(serviceVendors){
                    $scope.vendorList = serviceVendors;
                });
                metaDataService.getAllBusinessCostCenters(function (bcc) {
                    $scope.units = bcc;
                });
                
            };

            $scope.selectVendor = function(vendor){
                $scope.receivings = undefined;
                $scope.srItems = null;
                $scope.selectedDispatchLocation = null;
                $scope.pendingSOs = null;
                $scope.selectedVendor = vendor;
                $scope.blockedAdvancePayments = [];
                metaDataService.getVendorLocations($scope.selectedVendor.id, function (locations) {
                    $scope.locationList = locations;
                });
                metaDataService.getVendorDetail($scope.selectedVendor.id, function (vendor) {
                    if (vendor.vendorBlocked != undefined && vendor.vendorBlocked != null && vendor.vendorBlocked == 'Y') {
                        if (vendor.blockedReason == 'MANUAL') {
                            $alertService.alert("Vendor is Blocked...!", "This vendor is blocked for making further PO/GR/SO/SR/PR...!" +
                                "<br><b>Please Contact Finance Team..!</b>", function () {
                            }, true);
                            $scope.selectedVendor = null;
                            $timeout(function () {
                                $('#vendorList').val('').trigger('change');
                            });
                            return;
                        } else {
                            $alertService.alert("Vendor is Blocked...!", "This vendor is blocked for making further PO/GR/SO/SR/PR...!" +
                                "<br><b>Only SO's Related to these Advance are allowed to process further " + vendor.blockedReason + "</b>", function () {
                            }, false);
                            $scope.blockedAdvancePayments = JSON.parse(vendor.blockedReason);
                        }
                    }
                });
            };

            $scope.selectDispatchLocation = function(location){
                $scope.selectedDispatchLocation = location;
                metaDataService.getCompanyList(function (companies) {
                    $scope.companies = companies;
                });
               // $scope.getPendingOrders();
            };
            
            $scope.selectCompany = function(selectedCompany){
                $scope.selectedCompany = selectedCompany;
                metaDataService.getStates("active",function(states){
                    $scope.locations = states;
                });
              };

              $scope.selectDelLocation = function(selectedDelLocation){
                  $scope.selectedDelLocation = selectedDelLocation;
                  $scope.getPendingOrders();
              };

              $scope.blockSrForBlockedVendors = function (sos) {
                  if ($scope.blockedAdvancePayments.length > 0) {
                      angular.forEach(sos, function (so) {
                          if (so.vendorAdvancePayments!= null && so.vendorAdvancePayments.length > 0) {
                              var check = true;
                              for (var i = 0; i < so.vendorAdvancePayments.length;i++) {
                                  if ($scope.blockedAdvancePayments.indexOf(so.vendorAdvancePayments[i].advancePaymentId) != -1) {
                                      check = false;
                                  }
                              }
                              so.vendorBlocked = check;
                          } else {
                              so.vendorBlocked = true;
                          }
                      });
                  }
              };

            $scope.getPendingOrders = function(){
                $scope.sosAdvance = [];
                $http({
                    method:"GET",
                    url:apiJson.urls.serviceOrderManagement.pendingOrders,
                    params:{
                        vendorId:$scope.selectedVendor.id,
                        dispatchId:$scope.selectedDispatchLocation.id,
                        userId: appUtil.getCurrentUser().userId,
                        companyId: $scope.selectedCompany.id,
                        locationId: $scope.selectedDelLocation.id
                    }
                }).then(function(response){
                    $scope.pendingSOs = response.data;
                    if(appUtil.isEmptyObject($scope.pendingSOs)){
                        $toastService.create("Could not find pending Service Orders!");
                    }
                    $scope.blockSrForBlockedVendors($scope.pendingSOs);
                },function(error){
                    console.log(error);
                });
            };

            $scope.changeTotalAmount = function(){};

            $scope.goBack = function () {
                $scope.showExpandedView = false;
                $scope.filled = false;
                $scope.selectedOrders = [];
                $scope.pendingSOs.forEach(function(so){
                   so.checked = false;
                   so.disabled = false;
                });
                $scope.srItems = undefined;
                $scope.receivings = {};
                $scope.showSrItems = false;
            };

            $scope.selectSOs=function(){
            	$scope.billAmount = 0;
                $scope.totalTaxes = 0;
                $scope.paidAmount = 0;
                var selected = $scope.pendingSOs.filter(function(so){
                    return so.checked!=undefined && so.checked;
                });
                if (selected.length == 0) {
                    $toastService.create("Please Select atleast 1 Service Order ..!");
                    return false;
                }
                var costElementId = selected[0].orderItems[0].costElementId;
                var price = selected[0].orderItems[0].unitPrice;
                var id = selected[0].id;
                var check = true;
                var map = new Map();
                for(var i = 0; i < selected.length ; i++){
                	for(var x = 0; x < selected[i].orderItems.length ; x++){
                          if(map.has(selected[i].orderItems[x].costElementId)){
                             var listData = map.get(selected[i].orderItems[x].costElementId);
                                       if(listData != selected[i].orderItems[x].unitPrice){
                                           check = false;
                                       }
                          }else{
                              map.set(selected[i].orderItems[x].costElementId,selected[i].orderItems[x].unitPrice);
                           }
                	}
                    var thresholdWithPending = 0;
                	var pendingAmount = 0;
                    var totalPendingTax = 0;
                    var totalAmount = selected[i].totalAmount;
                	var threshold = (5*totalAmount)/100;
                	selected[i].threshold = threshold;
                    for (var x = 0; x < selected[i].orderItems.length; x++) {
                        var itemPendingAmount = (selected[i].orderItems[x].pendingQuantity)* selected[i].orderItems[x].unitPrice;
                        var itempendingTax = (itemPendingAmount * selected[i].orderItems[x].taxRate)/100;
                        pendingAmount = pendingAmount + itemPendingAmount + itempendingTax;
                        totalPendingTax = totalPendingTax + itempendingTax;
                    }
                    thresholdWithPending = threshold + pendingAmount;
                    selected[i].pendingAmount = pendingAmount;
                    selected[i].thresholdWithPending = thresholdWithPending;
                    selected[i].consumed = 0;
                    selected[i].remainingWithoutTax = thresholdWithPending - totalPendingTax - (5*selected[i].totalTaxes)/100;
                    selected[i].pendingWithoutTax = selected[i].remainingWithoutTax;
                    selected[i].remaining = thresholdWithPending;
                }
               if(!check){
                     $toastService.create("Prices of cost elements are different on selected SO's");
               		return;
               }
                if(selected.length>0){
                    $scope.showExpandedView = true;
                    $scope.selectedOrders = angular.copy(selected);
                }else{
                    $toastService.create("Select at least one Order before moving forward.");
                }
            };

            $scope.fillAmount = function () {
                if (!$scope.filled) {
                    $scope.selectedOrders.forEach(function (so) {
                        so.orderItems.forEach(function (item) {
                            item.receivedCost = parseFloat((item.pendingQuantity*item.unitPrice).toFixed(2));
                            $scope.updateSRQty(item,so,false);
                        })
                    });
                    $scope.filled = true;
                }
                else {
                    $scope.selectedOrders.forEach(function (so) {
                        so.orderItems.forEach(function (item) {
                            item.receivedCost = 0;
                            $scope.updateSRQty(item,so,true);
                        })
                    });
                    $scope.filled = false;
                }
            };

            $scope.disableSoSelection = function ($event,so, pendingSOs) {
                if (so.vendorAdvancePayments != null) {
                    var initiatedMsg = "";
                    var advanceAdjustInitiatedMsg = "";
                    var pendingPrs = [];
                    for (var i = 0; i < so.vendorAdvancePayments.length; i++) {
                        if (so.vendorAdvancePayments[i].advanceStatus == 'INITIATED') {
                            initiatedMsg += "Advacne Id : " + so.vendorAdvancePayments[i].advancePaymentId + " Pending Vendor Advance of Rs : " + so.vendorAdvancePayments[i].prAmount + "<br>";
                            so.checked = false;
                        }
                        if (so.vendorAdvancePayments[i].advanceStatus == 'ADJUST_INITIATED') {
                            advanceAdjustInitiatedMsg += "Advacne Id : " + so.vendorAdvancePayments[i].advancePaymentId + " Pending Vendor Advance of Rs : " + so.vendorAdvancePayments[i].prAmount + "<br>";
                            so.checked = false;
                        }
                        if (so.vendorAdvancePayments[i].pendingPrs.length > 0) {
                            for (var j=0;j<so.vendorAdvancePayments[i].pendingPrs.length;j++) {
                                if (pendingPrs.indexOf(so.vendorAdvancePayments[i].pendingPrs[j]) === -1) {
                                    pendingPrs.push(so.vendorAdvancePayments[i].pendingPrs[j]);
                                }
                            }
                            so.checked = false;
                        }
                    }
                    if (pendingPrs.length > 0) {
                        $alertService.alert("Please Settle all the pending PR's related to this SO..!","Pending P's related to this SO are : " +
                            pendingPrs.join(",") + "<br><b>Settle Pending PR's/Complete the Payment Of above SR's</b>",function () {}, true);
                        return false;
                    }
                    if (advanceAdjustInitiatedMsg != "") {
                        $alertService.alert("This PO has Some Adjustment of Vendor Advance", advanceAdjustInitiatedMsg + "<br>" +
                            "Please Settle the Vendor Advances to do the Receiving..!", function () {
                        }, false);
                        return false;
                    }
                    if (initiatedMsg != "") {
                        $alertService.alert("Please Settle the Vendor Advance Related to this SO", initiatedMsg + "<br>" +
                            "Please Settle the Vendor Advances to do the Receiving..!", function () {
                        }, false);
                        return false;
                    }
                }
                if (so.checked) {
                    if (so.vendorAdvancePayments != null && so.vendorAdvancePayments.length > 0) {
                        angular.forEach(pendingSOs, function (item) {
                            item.checked = false;
                            item.disabled = true;
                            if (item.id == so.id) {
                                item.checked = true;
                                item.disabled = false;
                            }
                        });
                    } else {
                        angular.forEach(pendingSOs, function (item) {
                            if (item.id == so.id) {
                                item.checked = true;
                                item.disabled = false;
                            }
                            if (item.vendorAdvancePayments != null && item.vendorAdvancePayments.length > 0) {
                                item.checked = false;
                                item.disabled = true;
                            }
                        });
                    }
                } else {
                    var nonAdvanceSelected = false;
                    angular.forEach(pendingSOs, function (item) {
                        if (item.vendorAdvancePayment == null) {
                            if (item.checked) {
                                nonAdvanceSelected = true;
                            }
                        }
                    });
                    if (nonAdvanceSelected) {
                        angular.forEach(pendingSOs, function (item) {
                            if (item.vendorAdvancePayment != null) {
                                item.checked = false;
                                item.disabled = true;
                            }
                        });
                    } else {
                        angular.forEach(pendingSOs, function (item) {
                            item.checked = false;
                            item.disabled = false;
                        });
                    }
                }
            };

            $scope.updateThreshold = function(item, selected){
                var totalSoAmount = 0;
                var totalPendingCost = 0;
                for (var x = 0; x < selected.orderItems.length; x++) {
                    console.log(selected.orderItems[x].totalAmount);
                    if(selected.orderItems[x].totalAmount!=null && selected.orderItems[x].totalAmount!=undefined) {
                        totalSoAmount = parseFloat(totalSoAmount) + parseFloat(selected.orderItems[x].totalAmount);
                        if(selected.orderItems[x].receivedCost==undefined || selected.orderItems[x].receivedCost==null) {
                            totalPendingCost = parseFloat(totalPendingCost) + parseFloat(0);
                        }
                        else {
                            totalPendingCost = parseFloat(totalPendingCost) + parseFloat(selected.orderItems[x].receivedCost);
                        }
                    }

                    if(selected.thresholdWithPending < totalSoAmount){
                        $toastService.create("cannot enter value greater then the threshold value for service order");
                        item.receivedQuantity = 0;
                        item.received = null;
                        item.totalAmount = 0;
                        item.receivedCost = 0;
                        item.taxPrice = 0;
                        $scope.calculateTotal();
                        totalPendingCost = 0;
                        totalSoAmount = 0;
                        for (var x = 0; x < selected.orderItems.length; x++) {
                            if (selected.orderItems[x].totalAmount == undefined || selected.orderItems[x].totalAmount == null) {
                                totalSoAmount = parseFloat(totalSoAmount) + parseFloat(0);
                            } else {
                                totalSoAmount = parseFloat(totalSoAmount) + parseFloat(selected.orderItems[x].totalAmount);
                            }
                            if (selected.orderItems[x].receivedCost == undefined || selected.orderItems[x].receivedCost == null) {
                                totalPendingCost = parseFloat(totalPendingCost) + parseFloat(0);
                            }
                            else {
                                totalPendingCost = parseFloat(totalPendingCost) + parseFloat(selected.orderItems[x].receivedCost);
                            }
                        }
                        selected.remainingWithoutTax = parseFloat(selected.pendingWithoutTax) - parseFloat(totalPendingCost);
                        selected.remaining = parseFloat(selected.thresholdWithPending) - parseFloat(totalSoAmount);
                        selected.consumed = parseFloat(totalSoAmount);
                        return;
                    }
                }
                selected.remaining = parseFloat(selected.thresholdWithPending) - parseFloat(totalSoAmount);
                selected.remainingWithoutTax = parseFloat(selected.pendingWithoutTax) - parseFloat(totalPendingCost);
                selected.consumed = parseFloat(totalSoAmount);
            }

            $scope.updateSRQty = function(item,serviceOrder,isCleared){
            	/*if(item.receivedQuantity == null){
            		item.receivedQuantity = 0;
            	}*/
               // var pending = parseFloat(item.requestedQuantity) - parseFloat(item.receivedQuantity);
                if (isCleared) {
                    item.receivedQuantity = 0;
                    item.received = null;
                    item.totalAmount = 0;
                    item.receivedCost = 0;
                    item.taxPrice = 0;
                    $scope.updateThreshold(item,serviceOrder);
                    $scope.calculateTotal();
                    item.receivedCost = null;
                    return;
                }
                if(serviceOrder.remaining<=0 || item.receivedCost<=0){
                    $toastService.create("cannot enter value greater then the threshold value for this item or less than zero!");
                    item.receivedQuantity = 0;
                    item.received = null;
                    item.totalAmount = 0;
                    item.receivedCost = 0;
                    item.taxPrice = 0;
                    $scope.updateThreshold(item,serviceOrder);
                    $scope.calculateTotal();
                    item.receivedCost = null;
                    return;
                }
                if(!appUtil.isEmptyObject(item.receivedCost)){
                    item.serviceOrderId= item.serviceOrderId;
                    item.deliveryLocation = item.locationId;
                    item.serviceOrderItemId= item.id;
                    item.ascCode = item.ascCode;
                    var receivedCost = item.receivedCost;
                    var taxPrice = getTaxPrice(receivedCost,item.taxRate);
                    var totalAmount = getTotalAmount(receivedCost,taxPrice)
                    /*if(getVarPrice(totalAmount,item.amountPaid)){
                         $toastService.create("Quantity received cannot be greater than requested quantity");
                         item.received = parseInt(item.received.toString().slice(0, -1));
                         return;
                    }*/
                    	item.received = (item.receivedCost/item.unitPrice).toFixed(12);
                        item.receivedQuantity = item.received;
                    	item.totalAmount = totalAmount.toFixed(2);
                    	item.taxPrice = taxPrice;
                    	item.receivedCost = receivedCost;
                        $scope.updateThreshold(item,serviceOrder);
                    	$scope.calculateTotal();
                        /*$scope.billAmount = parseFloat($scope.billAmount) + parseFloat(receivedCost);
                        $scope.totalTaxes = parseFloat($scope.totalTaxes) + parseFloat(taxPrice);
                        $scope.paidAmount = parseFloat($scope.paidAmount) + parseFloat(totalAmount);*/
                }
                else{
                	item.receivedQuantity = 0;
                	item.received = null;
                	item.totalAmount = 0;
                	item.receivedCost = 0;
                	item.taxPrice = 0;
                	$scope.calculateTotal();
                }
            };
            
            $scope.calculateTotal = function(){
            	$scope.billAmount = 0;
                $scope.totalTaxes = 0;
                $scope.paidAmount = 0;
                for(var x = 0 ; x < $scope.selectedOrders.length ; x++){
                	for(var i = 0; i < $scope.selectedOrders[x].orderItems.length; i++){
                	    if($scope.selectedOrders[x].orderItems[i].receivedCost != undefined && $scope.selectedOrders[x].orderItems[i].receivedCost != 0){
                		$scope.billAmount = parseFloat($scope.billAmount) + parseFloat($scope.selectedOrders[x].orderItems[i].receivedCost);
                		$scope.totalTaxes = parseFloat($scope.totalTaxes) + parseFloat($scope.selectedOrders[x].orderItems[i].taxPrice);
                		$scope.paidAmount = parseFloat($scope.paidAmount) + parseFloat($scope.selectedOrders[x].orderItems[i].totalAmount);
                	}}
                }
            }
            
            function getVarPrice(newTotal, oldTotal){
            	var TaxPriceVary = ((oldTotal*5)/100).toFixed(2);
            	oldTotal = parseFloat(TaxPriceVary) + parseFloat(oldTotal);
				if(newTotal > oldTotal){
					return true;
				}
				else{
					return false;
				}
            }

            function getCost(item) {
                var cost = parseFloat(item.received * item.unitPrice);
                return cost.toFixed(2);
            }
            
            function getTaxPrice(receivedCost,taxRate) {
                return ((receivedCost*taxRate)/100).toFixed(2);
            }
            
            function getTotalAmount(totalCost,totalTax){
				return parseFloat(totalCost) + parseFloat(totalTax);
			}

            $scope.allocateCost = function (item, soId) {
                var allocateCostModal = Popeye.openModal({
                    templateUrl: "allocateCost.html",
                    controller: "allocateCostCtrl",
                    resolve: {
                        item: function(){
                            var toBeSent = angular.copy(item);
                            toBeSent.soId = soId;
                            return toBeSent;
                        },
                        srItems: function () {
                            return angular.copy($scope.srItems);
                        },
                        units: function () {
                            return angular.copy($scope.units);
                        },
                        companies: function () {
                            return angular.copy($scope.companyList);
                        }
                    },
                    modalClass:'custom-modal',
                    click: false,
                    keyboard: false
                });

                allocateCostModal.closed.then(function(allItems) {
                    if(!appUtil.isEmptyObject(allItems)){
                        $scope.srItems = angular.copy(allItems);
                        $scope.receivings = calculateReceivings($scope.srItems);
                    }
                });
            };

            $scope.removeItemFromRcv = function (index,rcv, key){
                rcv.items.splice(index,1);
                if(rcv.items.length==0){
                    delete $scope.receivings[key];
                }
                if(Object.keys($scope.receivings).length==0){
                    $scope.receivings = undefined;
                }
            };

            function calculateReceivings(srItems) {
                var receivings = {};
                for(var key in srItems){
                    addToMap(receivings,srItems[key]);
                }
                return receivings;
            }

            function addToMap(map, obj) {
                var key = obj.company.id + "_" + obj.state.id;
                if(appUtil.isEmptyObject(map[key])){
                    map[key] = {
                        company: obj.company,
                        location: obj.location,
                        state: obj.state,
                        items: []
                    };
                }
                map[key].items.push(obj);
            }
            
            $scope.submit = function(){
                $alertService.confirm("Are you sure?","",function(result){
                    if(result){
                        sendRequestForRcvng();
                    }
                });
            };

            function sendRequestForRcvng(){
                var reqObjitems = prepareRcvngs($scope.selectedOrders);
                if (reqObjitems.length === 0) {
                    $toastService.create("Please enter the Received Amount and try again..!");
                    return;
                }
                var reqObj = {
                		 userId: $scope.currentUser.userId,
                         dispatchLocationId: $scope.selectedDispatchLocation.id,
                         vendorId: $scope.selectedVendor.id,
                         companyId: $scope.selectedCompany.id,
                         deliveryLocationId:reqObjitems[0].locationId,
                         deliveryStateId: $scope.selectedDelLocation.id,
                         items: reqObjitems
                };
                if(reqObj.items.length>0){
                    $http({
                        method:"POST",
                        url:apiJson.urls.serviceReceivedManagement.createReceiving,
                        data:reqObj
                    }).then(function(response){
                        var srIds = response.data;
                        if(srIds!=null){
                            $scope.init();
                            $toastService.create("Receiving(s) with IDs: " + srIds + " created", function(){
                                $state.go('menu.viewSR',{
                                        vendor:$scope.selectedVendor,
                                        dispatchLocation:$scope.selectedDispatchLocation
                                });
                            });
                        }else {
                            $toastService.create("Receiving could not be created !! Please select ");
                        }
                    },function(error){
                        console.log(error);
                        if (error.data.errorMsg != null) {
                            $alertService.alert(error.data.errorMsg);
                        } else {
                            $toastService.create("Receiving could not be created due to some error!!");
                        }
                    });
                }
            }

            function prepareRcvngs(selectedOrders){
                var allRcvngsItems = [];
                if(!appUtil.isEmptyObject(selectedOrders)){
                	for(var x = 0 ; x < selectedOrders.length ;x++){
                		for(var i = 0 ; i < selectedOrders[x].orderItems.length ;i++){
               			 if(selectedOrders[x].orderItems[i].received != null && selectedOrders[x].orderItems[i].received != 0 && !Number.isNaN(selectedOrders[x].orderItems[i].received)){
                   			 allRcvngsItems.push(selectedOrders[x].orderItems[i]);
                   		  }
                   	}
                }
                	
                  /*  for(var key in selectedOrders){
                        var rcv = selectedOrders[key];
                        for(var key in rcv.orderItems){
                            var rcv = selectedOrders[key];
                        allRcvngs.push({
                            items: rcv.orderItems
                        });
                       }
                    }*/
                }
                return allRcvngsItems;
            }
        }
    ]
).controller("allocateCostCtrl",
            ['$rootScope','$scope','appUtil','$toastService','Popeye','item','srItems','units','companies',
    function($rootScope, $scope, appUtil,$toastService, Popeye, item, srItems, units, companies){

        function getTotalCost() {
            var cost = 0;
            $scope.units.forEach(function (unit) {
                cost += (appUtil.isEmptyObject(unit.allocatedCost) || isNaN(unit.allocatedCost))
                            ? 0 : parseFloat(unit.allocatedCost);
            });
            return cost;
        }

        function prepareObj(bcc, item, qty, tax) {
            return {
                serviceOrderId: item.soId,
                serviceOrderItemId: item.id,
                businessCostCenterId: bcc.id,
                businessCostCenterName: bcc.name,
                costElementId: item.costElementId,
                costElementName: item.costElementName,
                company: bcc.company,
                location: bcc.location,
                state: bcc.state,
                receivedQuantity: parseFloat(qty),
                ascCode: item.ascCode,
                serviceDescription: item.serviceDescription,
                unitOfMeasure: item.unitOfMeasure,
                unitPrice: item.unitPrice,
                totalCost: parseFloat(bcc.allocatedCost),
                totalAmount: parseFloat(parseFloat(bcc.allocatedCost)+parseFloat(tax)),
                taxRate: parseFloat(item.taxRate),
                tdsRate: item.tdsRate,
                totalTax: parseFloat(tax)
            };
        }

        $scope.initCostModal = function(){
            $scope.item = item;
            $scope.allItems = srItems;
            $scope.units = units;
            $scope.companies = companies;
        };

        $scope.updateQty = function (unit) {
            var item = angular.copy($scope.item);
            var allocatedTillNow = getTotalCost(unit.id);
            if(!appUtil.isEmptyObject(item.receivedCost) && !isNaN(item.receivedCost)
                    && parseFloat(item.receivedCost) >= allocatedTillNow){
                if(appUtil.isEmptyObject($scope.allItems)){
                    $scope.allItems = {};
                }
                var qty = parseFloat(parseFloat(unit.allocatedCost)/parseFloat(item.unitPrice)).toFixed(2);
                var tax = parseFloat(parseFloat(unit.allocatedCost) * (parseFloat(item.taxRate)/100)).toFixed(2);
                var key = item.id + "_" + unit.id;
                var obj = prepareObj(unit, item, qty, tax);
                if(appUtil.isEmptyObject($scope.allItems[key])){
                    $scope.allItems[key] = {};
                }
                // add item specific to SO item and to that specific unit
                $scope.allItems[key] = obj;
                unit.qty = isNaN(qty) ? 0 : qty;
                $scope.totalAllocated = allocatedTillNow;
            }else {
                if(appUtil.isEmptyObject(unit.allocatedCost)){
                    $toastService.create("Select a valid cost to be allocated");
                }
                if(parseFloat(item.receivedCost) < allocatedTillNow){
                    $toastService.create("Total Allocated Cost cannot be less than cost across units");
                }
            }
        };


        $scope.cancel=function(){
            Popeye.closeCurrentModal();
        };

        $scope.submit = function () {
            var returnResult = {};
            for(var key in $scope.allItems){
               if(!appUtil.isEmptyObject($scope.allItems[key].totalCost)
                   && !isNaN($scope.allItems[key].totalCost)){
                  returnResult[key] = $scope.allItems[key];
               }
            }
            Popeye.closeCurrentModal(returnResult);
        };
    }
]);