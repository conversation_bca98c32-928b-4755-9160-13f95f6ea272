scmApp.controller(
    'unitToProductProfileMappingCtrl',
    [
        '$rootScope',
        '$scope',
        '$interval',
        'apiJson',
        '$http',
        'appUtil',
        '$toastService',
        'uiGridConstants',
        '$alertService',
        '$timeout',
        'metaDataService',
        'previewModalService',
        'Popeye',
        function ($rootScope, $scope, $interval, apiJson, $http, appUtil, $toastService, $uiGridConstants,
                  $alertService, $timeout, metaDataService,previewModalService,Popeye) {

            $scope.init = function () {
                $scope.allSkuList = [];
                $scope.allUnitsList = [];
                $scope.updatedList = [];
                $scope.getAllUnitList();
                $scope.getAllSkus();
                $scope.getRecipeProfiles();
                $scope.showPreview = previewModalService.showPreview;
            };

            $scope.getAllSkus = function () {
                $http({
                    method: "GET",
                    dataType: 'json',
                    data: '',
                    headers: {
                        "Content-Type": "application/json"
                    },
                    url: apiJson.urls.skuMapping.getAllSku
                }).then(function success(response) {
                    $scope.allSkuList = response.data
                }, function (response) {
                    console.log("got error", response);
                });
            };

            $scope.getAllUnitList = function () {
                metaDataService.getUnitList(function (response) {
                    $scope.allUnitsList = response;/*.filter(function (value) {
                        return value.code == 'WAREHOUSE' || value.code == 'KITCHEN' || value.code == 'OFFICE';
                    });*/
                });
            };

            $scope.showProductForMapping = function (selectedUnit) {
                $scope.selectedUnit = selectedUnit;
                    $http({
                        method: "POST",
                        data:  selectedUnit.id,
                        url: apiJson.urls.skuMapping.getSkuForUnit
                    }).then(function success(response) {
                        $scope.UnitListDetails=[];
                        response.data.filter(function (sku) {
                            if(sku.mappingStatus=="ACTIVE" && sku.recipeRequired== true){
                                $scope.UnitListDetails.push(sku)
                            }
                        });
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                };
            $scope.showAllSelectedSkus = function(){
                if (appUtil.isEmptyObject($scope.selectedProfile)) {
                    $toastService.create("Please select updated Profile for sku");
                    return;
                }

                if(appUtil.isEmptyObject($scope.updatedList)){
                    $toastService.create("Please Select Sku First!");
                    return;
                }
                else
                {
                    var profileShowModal = Popeye.openModal({
                        templateUrl: "ProfileDetails.html",
                        controller: "profileDetailsCtrl",
                        resolve: {

                            list: function () {
                                return angular.copy($scope.updatedList);
                            },
                            updatedProfile: function() {
                                return angular.copy($scope.selectedProfile);
                            },
                            selectedUnit: function () {
                                return angular.copy($scope.selectedUnit);
                            }

                        },
                        modalClass: 'custom-modal',
                        click: false,
                        keyboard: false
                    });

                    profileShowModal.closed.then(function (list) {
                        $scope.selectedProfile = null;
                        $("#select2-requestForListData-container").html('');
                        $scope.selectedUnit = null;
                        $scope.UnitListDetails=null;
                    });
                }
            }

            $scope.changeProfile = function(selectProfile){
                $scope.selectedProfile = selectProfile;
            };

            $scope.getRecipeProfiles = function (){
                $http({
                    method: 'GET',
                    url: apiJson.urls.recipeManagement.recipeProfiles,
                    params: {type: "SCM_R"}
                }).then(function success(response) {
                    $scope.recipeProfiles = response.data;
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            $scope.showAllSkus = function () {
                console.log($scope.selectedUnit);
                if(!$scope.UnitListDetails){
                    $toastService.create("No sku found for the Unit Please select Unit First");
                    return;
                }
                if (appUtil.isEmptyObject($scope.selectedUnit)) {
                    $toastService.create("Please select Unit");
                    return;
                }
                console.log("inside view")
                var allocateCostModal = Popeye.openModal({
                    templateUrl: "skuProfileMapping.html",
                    controller: "skuProfileMappingCtrl",
                    resolve: {

                        list: function () {
                            return angular.copy($scope.UnitListDetails);
                        }

                    },
                    modalClass: 'custom-modal',
                    click: false,
                    keyboard: false
                });

                allocateCostModal.closed.then(function (list) {
                    $scope.updatedList = list;
                    console.log($scope.updatedList);
                });
            }
        }
    ]
).controller("skuProfileMappingCtrl",
    ['$rootScope', '$window', '$scope', 'appUtil', 'apiJson', '$http', '$toastService', 'Popeye', 'list',
        function ($rootScope, $window, $scope, appUtil, apiJson, $http, $toastService, Popeye, list) {


            $scope.initProfileModal = function () {
                $scope.skuList = list
                for (var i = 0; i < $scope.skuList.length; i++) {
                    $scope.skuList[i].dataCheck = false;
                    $scope.skuList[i].checkBoxcheck = false;
                }
            };
            $scope.isCheckTrue = function ($e, code) {
                    if ($e.target.checked) {
                        for (var i = 0; i < $scope.skuList.length; i++) {
                            if ($scope.skuList[i].code == code) {
                                $scope.skuList[i].checkBoxcheck = false;
                            }
                            else {
                                $scope.skuList[i].checkBoxcheck = true;
                            }
                        }
                    } else {
                        for (var i = 0; i < $scope.unitList.length; i++) {
                            $scope.skuList[i].checkBoxcheck = false;
                        }
                    }
            }

            $scope.submit = function () {
                $scope.submitData();
            }

            $scope.submitData = function () {
                var returnResult = {};
                for (var key in $scope.skuList) {
                    if ($scope.skuList[key].dataCheck && $scope.skuList[key].profile!=null) {
                        returnResult[key] = $scope.skuList[key];
                    }
                }
                Popeye.closeCurrentModal(returnResult);
            }
            $scope.cancel = function () {
                Popeye.closeCurrentModal();
            };

        }]).controller("profileDetailsCtrl",['$rootScope', '$window', '$scope', 'appUtil', 'apiJson', '$http', '$alertService', '$toastService', 'Popeye', 'list', 'updatedProfile', 'selectedUnit',
        function ($rootScope, $window, $scope, appUtil, apiJson, $http, $alertService, $toastService, Popeye, list, updatedProfile, selectedUnit){
        $scope.initProfileShowModal = function () {
            $scope.updatedList = list;
            $scope.updatedProfile = updatedProfile;
            $scope.selectUnit = selectedUnit;
        };

        $scope.submit = function () {
            $scope.submitData();
        }
        $scope.submitData = function () {
            var returnResult = {profile:$scope.selectedProfile,
                                Unit:$scope.selectUnit};
            var list = {};
                for (var key in $scope.updatedList) {
                        list[$scope.updatedList[key].id]=$scope.updatedList[key].inventoryList;
                }
            console.log(list);
            $alertService.confirm("Are you sure you are going to update profile for sku?","",function(result) {
                if (result) {
                    $http({
                        method: 'POST',
                        data: list,
                        headers: {
                            "Content-Type": "application/json"
                        },
                        params: {
                            unitId: $scope.selectUnit.id,
                            profile: $scope.updatedProfile,
                        },
                        url: apiJson.urls.skuMapping.updateSkuProfileForUnit,
                    }).then(function success(response) {
                        $toastService.create("Successfully update the profiles For Selected Sku!");
                        $scope.listofProfiles = response.data;
                        // $scope.selectedProfile = null;
                        // $scope.selectedUnit = null;
                    }, function error(response) {
                        console.log("error:" + response);
                    });

                    Popeye.closeCurrentModal(returnResult);
                }
            })
        }
        $scope.cancel = function () {
            Popeye.closeCurrentModal();
        };
    }]);
