/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

angular
    .module('scmApp')
    .controller(
        'prodPlanningCtrl',
        [
            '$rootScope',
            '$scope',
            'apiJson',
            '$http',
            '$state',
            '$timeout',
            'appUtil',
            '$toastService',
            '$alertService',
            'metaDataService',
            'previewModalService',
            function ($rootScope, $scope, apiJson, $http, $state, $timeout, appUtil, $toastService, $alertService,metaDataService, previewModalService) {

                $scope.requestOrders = [];
                $scope.aggregatedItems = [];
                $scope.sentRequest = false;
                $scope.allCheck = false;
                $scope.allCheckGnt = false;
                $scope.isShow = false;
                $scope.productionPlanningSummaryData = [];
                $scope.summaryUnitData = [];
                $scope.updatedPlanItems = [];
                $scope.cityData = [];
                $scope.updateFlag = false;
               // $scope.cityName=[];
                $scope.allUnitWithRos=false;
                $scope.allActiveUnit =[];
                $scope.selectAllToggle=false;
                $scope.init = function () {
                    $scope.noOfDays = 2;
                    $scope.brandList = appUtil.getBrandList();
                    $scope.brandList.forEach(function (value) {
                        if (value.brandCode == 'CH') {
                            $scope.chaayosId = value.brandId;
                        } else if (value.brandCode == 'GNT') {
                            $scope.gntId = value.brandId;
                        } else if (value.brandCode == 'DC') {
                            $scope.dcId = value.brandId;
                        }
                    })
                    $scope.setDates(appUtil.getDate(0), $scope.noOfDays);
                    $scope.selectedDate = appUtil.formatDate(appUtil.getDate(0), "yyyy-MM-dd");
                    $scope.minDate = appUtil.formatDate(appUtil.getDate(-30), "yyyy-MM-dd");
                    $scope.maxDate = appUtil.formatDate(appUtil.getDate(30), "yyyy-MM-dd");
                    $scope.orderTypes = ["TRANSFER", "INVOICE"];
                    $scope.selectedTab = $scope.orderTypes[0];
                    $scope.isAcknowledged = false;
                    $scope.getOrders();
                    $scope.companyMap = appUtil.getCompanyMap();
                    $scope.showPreview = previewModalService.showPreview;
                    $scope.allRos = [];
                };

                $scope.printRos = function () {
                    var roIds = [];
                    $scope.acknowledgedOrders.forEach(function (order) {
                        roIds.push(order.id);
                    });

                    $http({
                        method: 'POST',
                        url: apiJson.urls.requestOrderManagement.getRequestOrdersByIds,
                        data :  roIds
                    }).then(function (response) {
                        if (!appUtil.isEmptyObject(response.data)) {
                            $scope.allRos = response.data;
                            $timeout(function() {
                                angular.element('#printDiv').trigger('click');
                            },0);
                        } else {
                            $toastService.create("Could Not FInd RO's for this ids..");
                        }
                    }, function (error) {
                        console.log(error);
                    });

                }


                $scope.setDates = function (date, days) {
                    $scope.dataEntry = [];
                    $scope.fulfillmentDate = date;
                    $scope.noOfDays = days;
                        $scope.dataEntry.push({
                            dayType: 'REMAINING_DAY',
                            date: appUtil.formatDate(appUtil.calculatedDate(-1, $scope.fulfillmentDate), "yyyy-MM-dd"),
                            brands: [{
                                id: $scope.chaayosId,
                                saleAmount: 0,
                                deliverySalePercentage: 0
                            },
                                {
                                    id: $scope.gntId,
                                    saleAmount: 0,
                                    deliverySalePercentage: 0
                                },
                                {
                                    id: $scope.dcId,
                                    saleAmount: 0,
                                    deliverySalePercentage: 0
                                }],
                        })

                    for (var i = 0; i <= $scope.noOfDays - 1; i++) {
                        $scope.dataEntry.push({
                            dayType: 'ORDERING_DAY',
                            date: appUtil.formatDate(appUtil.calculatedDate(i, $scope.fulfillmentDate), "yyyy-MM-dd"),
                            brands: [{
                                id: $scope.chaayosId,
                                saleAmount: 0,
                                deliverySalePercentage: 0
                            },
                                {
                                    id: $scope.gntId,
                                    saleAmount: 0,
                                    deliverySalePercentage: 0
                                },
                                {
                                    id: $scope.dcId,
                                    saleAmount: 0,
                                    deliverySalePercentage: 0
                                }]
                        })
                    }
                    console.log($scope.dataEntry);
                };

                $scope.getSalesPercentage = function () {
                    console.log("list",$scope.guideMeList);
                    $http({
                        method: "POST",
                        url: apiJson.urls.referenceOrderManagement.salesPercentageForUnits,
                        data: $scope.guideMeList,
                        responseType: 'arraybuffer',
                        headers: {
                            'Content-type': 'application/json',
                            'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                        }
                    }).then(
                            function (response) {
                                $toastService.create("Sheed Download Successfully!");
                                var fileName = ("Regular Ordering Sheet-")
                                    + appUtil.formatDate(new Date($scope.selectedDate),
                                        "dd-MM-yyyy") + "-"
                                    + appUtil.formatDate(Date.now(), "dd-MM-yyyy-hh-mm-ss")
                                    + ".xls";
                                var blob = new Blob(
                                    [response.data],
                                    {
                                        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                                    }, fileName);
                                saveAs(blob, fileName);
                            }, function (response) {
                                console.log(response);
                                $toastService.create("Sheed Download Failed");
                            });
                }

                $scope.selectedRO = {
                    roType: null,
                    count: 0
                }

                $scope.reset = function () {
                    $scope.setDates($scope.selectedDate, $scope.noOfDays);
                    $scope.requestOrders = [];
                    $scope.aggregatedItems = [];
                    $scope.sentRequest = false;
                };

                function setPackagingByProducts() {
                    metaDataService
                        .getAllPackagingMappings(function (mappings) {
                            $scope.packagingMap = appUtil.getPackagingMap();
                            $scope.productPackagingMappings = [];
                            for (var product in mappings) {
                                var defaultProductMapping = mappings[product].filter(function (mapping) {
                                    return mapping.isDefault;
                                })[0];
                                try {
                                    $scope.productPackagingMappings[product] = $scope.packagingMap[defaultProductMapping.packagingId];
                                } catch (e) {
                                    console.log(product, mappings[product]);
                                }
                            }
                        });
                }

                $scope.changeStatus = function (changedStatus) {
                    $scope.isAcknowledged = changedStatus;
                }

                $scope.selectOpenRo = function (ro) {
                    $http({
                        method: 'GET',
                        url: apiJson.urls.requestOrderManagement.requestOrder,
                        params: {requestOrderId: ro.id}
                    }).then(function (response) {
                        if (!appUtil.isEmptyObject(response.data)) {
                            $scope.selectedRo = response.data;
                        } else {
                            $toastService.create("Could Not Find Items for the Selected Ro!!");
                        }
                    }, function (error) {
                        console.log(error);
                    });
                }

                $scope.getOrders = function () {
                    if (appUtil.isEmptyObject($scope.selectedTab)) {
                        $scope.selectedTab = "TRANSFER";
                    }
                    if ($scope.selectedDate != null) {
                        $http({
                            url: apiJson.urls.requestOrderManagement.pendingOrdersByFulfillmentDateShort,
                            method: "GET",
                            params: {
                                date: $scope.selectedDate,
                                fulfillmentUnit: appUtil.getCurrentUser().unitId,
                                transferType: $scope.selectedTab,
                                fetchAcknowledged: $scope.isAcknowledged,
                                region: appUtil.unitData.region,
                            }
                        }).then(function (response) {
                            if (!appUtil.checkEmpty(response)) {
                                var allOrders = response.data.orderDetailShort;
                                $scope.productionPlanningSummaryData = response.data.productionPlanningSummary;
                                $scope.setSummaryData(response,$scope.productionPlanningSummaryData);
                                $scope.requestOrders = allOrders.filter(function (order) {
                                    return order.status == "CREATED";
                                }).sort(sortByUnit);

                                $scope.acknowledgedOrders = allOrders.filter(function (order) {
                                    return order.status == "ACKNOWLEDGED";
                                }).sort(sortByUnit);
                                $scope.addFamilyToRequestOrder($scope.requestOrders);
                                if ($scope.acknowledgedOrders.length > 0) {
                                    setPackagingByProducts();
                                }
                            }
                            $scope.sentRequest = true;
                        }, function (response) {
                            console.log(response);
                        });
                    } else {
                        $toastService.create("Select a date first");
                    }
                    handleEdgeCase();
                };

                $scope.setSummaryData=function (response,planningSummary){
                    $scope.cityData = planningSummary.cityRegionWise;
                    $scope.allActiveUnit = planningSummary.allActiveUnit;
                    $scope.untiWiseGntAndChaayos = response.data.unitWiseSummary;
                    //console.log($scope.allActiveUnit);
                    $scope.unitMap = {};
                    $scope.allActiveUnit.map(function (unit) {
                        unit.selected = false;
                        $scope.unitMap[unit.id] = unit;
                    });
                    //console.log($scope.allActiveUnit);
                    //console.log($scope.unitMap);
                    $scope.regionCity=[];
                    for( var  index in $scope.cityData){
                        var test={};
                        test.name=$scope.cityData[index];
                        test.selected=false;
                        $scope.regionCity.push(test);
                    }
                    //console.log($scope.regionCity);
                };

                $scope.addFamilyToRequestOrder= function (data){
                   var unit =appUtil.getUnitData();
                   var name=unit.family;
                    for(var index in data){
                        data[index].orderRaisedBy=name;
                    }
                    $scope.requestOrders=data;
                    console.log($scope.requestOrders);
                }

                function sortByUnit(a, b) {
                    if (a.requestUnit.name < b.requestUnit.name)
                        return -1;
                    if (a.requestUnit.name > b.requestUnit.name)
                        return 1;
                    return 0;
                }

                $scope.acknowledgeAll = function () {
                    if ($scope.allCheck == false) {
                        $scope.requestOrders.forEach(function (order) {
                            order.checked = true;
                        });
                        $scope.allCheck = true;
                    } else {
                        $scope.requestOrders.forEach(function (order) {
                            order.checked = false;
                        });
                        $scope.allCheck = false;
                    }
                };

                // $scope.selectAllGnt = function () {
                //     if ($scope.allCheckGnt == false) {
                //         $scope.requestOrders.forEach(function (order) {
                //             if (order.gntflag == true) {
                //                 order.checked = true;
                //             }
                //         });
                //         $scope.allCheckGnt = true;
                //     } else {
                //         $scope.requestOrders.forEach(function (order) {
                //             order.checked = false;
                //         });
                //         $scope.allCheckGnt = false;
                //     }
                // };

                $scope.isChecked = function (requestOrders) {
                    return requestOrders.filter(function (order) {
                        return order.checked;
                    }).length > 0;
                };

                $scope.previewOrders = function (requestOrders) {
                    if ($scope.selectedDate != null) {
                        var selectedRequestOrders = requestOrders.filter(function (order) {
                            return order.checked;
                        });
                        var selectOrderIds = [];
                        selectedRequestOrders.forEach(function (order) {
                            selectOrderIds.push(order.id);
                        });
                        var unitData = appUtil.getUnitData();
                        if (selectedRequestOrders.length > 0) {
                            $http({
                                url: apiJson.urls.requestOrderManagement.previewPlanOrders,
                                method: "POST",
                                data: {
                                    fulfillmentDate: $scope.selectedDate,
                                    lastUpdatedBy: appUtil.createGeneratedBy(),
                                    list: selectOrderIds,
                                    unitId: unitData.id
                                },
                                responseType: 'arraybuffer',
                                headers: {
                                    'Content-type': 'application/json',
                                    'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                                }
                            })
                                .then(
                                    function (response) {
                                        var fileName = ($scope.selectedTab == "TRANSFER" ? "Preview Request Order Sheet-" : "Preview Request Invoice Sheet-")
                                            + appUtil.formatDate(new Date($scope.selectedDate),
                                                "dd-MM-yyyy") + "-"
                                            + appUtil.formatDate(Date.now(), "dd-MM-yyyy-hh-mm-ss")
                                            + ".xls";
                                        var blob = new Blob(
                                            [response.data],
                                            {
                                                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                                            }, fileName);
                                        saveAs(blob, fileName);
                                    }, function (response) {
                                        console.log(response);
                                        $toastService.create("Previewing request order failed");
                                    });
                        } else {
                            $toastService
                                .create("Please select at least one request order before previewing");
                        }
                    } else {
                        $toastService.create("Please select a date first");
                    }
                };

                $scope.updatePlanItems = function (requestOrders) {
                    $scope.updateItems();
                    if($scope.updateFlag){
                        $http({
                            url: apiJson.urls.requestOrderManagement.updatePlanning,
                            method: 'POST',
                            data: $scope.updatedPlanItems,
                            params: {eventId: $scope.planningEvent.id}
                        }).success(function (data) {
                            $scope.acknowledgePlan();
                        }).error(function (err) {
                            console.log("Error during getting data", err);
                        });
                    }else{
                        $toastService
                            .create("Please acknowledge at least one request order before submitting");
                    }
                };

                $scope.submit = function (requestOrders) {
                    if ($scope.selectedDate != null) {
                        var selectedRequestOrders = requestOrders.filter(function (order) {
                            return order.checked;
                        });
                        var selectOrderIds = [];
                        selectedRequestOrders.forEach(function (order) {
                            selectOrderIds.push(order.id);
                        });
                        var unitData = appUtil.getUnitData();
                        if (selectedRequestOrders.length > 0) {
                            $http({
                                url: apiJson.urls.requestOrderManagement.startPlanning,
                                method: 'POST',
                                data: {
                                    fulfillmentDate: $scope.selectedDate,
                                    lastUpdatedBy: appUtil.createGeneratedBy(),
                                    list: selectOrderIds,
                                    unitId: unitData.id
                                },
                                headers: {
                                    'Content-type': 'application/json',
                                }
                            }).success(function (data) {

                                $scope.planningEvent = data;
                                $scope.itemsMap = new Object();
                                $scope.itemsMap["REQUESTED"] = {
                                    items: [],
                                    head: 'Requested Semi-Finished Products',
                                    tab: 1,
                                    key: 'REQUESTED'
                                };
                                $scope.itemsMap["ADDITIONAL"] = {
                                    items: [],
                                    head: 'Additional Semi-Finished Products',
                                    tab: 2,
                                    key: 'ADDITIONAL'
                                };
                                $scope.itemsMap["SKU"] = {
                                    items: [],
                                    head: 'Required SKU Items',
                                    tab: 3,
                                    key: 'SKU'
                                };

                                var index = null;
                                initCheckBoxModal();
                                $scope.avaiableDates = false;
                                for (index in $scope.planningEvent.requestItems) {
                                    $scope.addToItemsMap($scope.planningEvent.requestItems[index]);
                                }

                                $scope.openItemType = 'REQUESTED';
                                $scope.currentTab = 1;
                                $scope.isSemiFinisedItems = true;

                            }).error(function (err) {
                                $alertService.alert("error during planning",err.errorMsg,function (){},true);
                                console.log("Error during getting data", err);
                            });
                        } else {
                            $toastService
                                .create("Please acknowledge at least one request order before submitting");
                        }
                    } else {
                        $toastService.create("Please select a date first");
                    }
                };

                $scope.addToItemsMap = function (data) {
                    if (data.itemType == "SKU") {
                        $scope.itemsMap["SKU"].items.push(data);
                    } else if (data.itemType == "ADDITIONAL") {
                        $scope.itemsMap["ADDITIONAL"].items.push(data);
                    } else if (data.itemType == "REQUESTED") {
                        $scope.itemsMap["REQUESTED"].items.push(data);
                        if(data.recipeRequire && !$scope.avaiableDates){
                            $scope.availableDatesForAll = data.availableDates;
                            $scope.checkBoxModal.updatedExpiryDate = data.expiryDate;
                            $scope.checkBoxModal.checkAllUpdatedDates = true;
                            $scope.avaiableDates = true;
                        }
                    } else {
                        $scope.itemsMap["SKU"].items.push(data);
                    }
                };

                $scope.switchtab = function (val) {
                    $scope.currentTab = $scope.currentTab + val;
                    $scope.openItemType = $scope.getTabKey();
                    initCheckBoxModal();
                };

                $scope.getTabKey = function () {
                    switch ($scope.currentTab) {
                        case 1:
                            return "REQUESTED";
                        case 2:
                            return "ADDITIONAL";
                        case 3:
                            return "SKU";
                        default:
                            return "REQUESTED";
                    }
                };

                $scope.acknowledgePlan = function () {
                    $http({
                        url: apiJson.urls.requestOrderManagement.acknowledgePlanorders,
                        method: "POST",
                        data: $scope.planningEvent.id,
                        responseType: 'arraybuffer',
                        headers: {
                            'Content-type': 'application/json',
                            'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                        }
                    })
                        .then(
                            function (response) {
                                $toastService.create("Orders Acknowledged Successfully!");
                                var fileName = ($scope.selectedTab == "TRANSFER" ? "Request Order Sheet-" : "Request Invoice Sheet-")
                                    + appUtil.formatDate(new Date($scope.selectedDate),
                                        "dd-MM-yyyy") + "-"
                                    + appUtil.formatDate(Date.now(), "dd-MM-yyyy-hh-mm-ss")
                                    + ".xls";
                                var blob = new Blob(
                                    [response.data],
                                    {
                                        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                                    }, fileName);
                                saveAs(blob, fileName);
                                $("#planningItemsModal").closeModal();
                                $scope.reset();
                                $scope.init();
                                $state.go('menu.prodHistory');
                            }, function (response) {
                                console.log(response);
                                $toastService.create("Planning Event Failed");
                            });
                };

                $scope.setROType = function (ro) {
                    if ($scope.selectedRO.roType == null || $scope.selectedRO.count == 0) {
                        $scope.selectedRO.roType = ro.assetOrder ? 'FIXED_ASSET' : 'REGULAR';
                        $scope.selectedRO.count = 1;
                    } else if ($scope.selectedRO.roType == 'FIXED_ASSET' && ro.assetOrder && ro.checked) {
                        $scope.selectedRO.count = $scope.selectedRO.count + 1;
                    } else if ($scope.selectedRO.roType == 'REGULAR' && !ro.assetOrder && ro.checked) {
                        $scope.selectedRO.count = $scope.selectedRO.count + 1;
                    } else {
                        $scope.selectedRO.count = $scope.selectedRO.count - 1;
                    }
                }

                $scope.downloadPlanOrdersBulk = function (){
                    var roIds = [];
                    $scope.acknowledgedOrders.forEach(function (order) {
                        roIds.push(order.id);
                    });

                    $http({
                        url: apiJson.urls.requestOrderManagement.downloadPlanOrdersBulk,
                        method: 'POST',
                        responseType: 'arraybuffer',
                        data: roIds,
                        headers: {
                            'Content-type': 'application/json',
                            'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                        }
                    })
                        .success(
                            function (data) {
                                var fulfillmentDate = appUtil
                                    .getFormattedDate($scope.selectedDate);
                                var fileName = ($scope.selectedTab == "TRANSFER" ? "Request Order Sheet-" : "Request Invoice Sheet-")
                                     + "_" + fulfillmentDate
                                    + "_"
                                    + appUtil.formatDate(Date.now(), "dd-MM-yyyy-hh-mm-ss")
                                    + ".xls";
                                var blob = new Blob(
                                    [data],
                                    {
                                        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                                    }, fileName);
                                saveAs(blob, fileName);
                            }).error(function (err) {
                        console.log("Error during getting data", err);
                    });

                }

                $scope.downloadRequestOrder = function (requestOrder) {
                    console.log("download a request order ::::", requestOrder);
                    $http({
                        url: apiJson.urls.requestOrderManagement.downloadPlanOrder,
                        method: 'POST',
                        responseType: 'arraybuffer',
                        data: requestOrder.id,
                        headers: {
                            'Content-type': 'application/json',
                            'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                        }
                    })
                        .success(
                            function (data) {
                                var fulfillmentDate = appUtil
                                    .getFormattedDate(requestOrder.fulfillmentDate);
                                var fileName = ($scope.selectedTab == "TRANSFER" ? "Request Order Sheet-" : "Request Invoice Sheet-")
                                    + requestOrder.requestUnit.name + "_" + fulfillmentDate
                                    + "_"
                                    + appUtil.formatDate(Date.now(), "dd-MM-yyyy-hh-mm-ss")
                                    + ".xls";
                                var blob = new Blob(
                                    [data],
                                    {
                                        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                                    }, fileName);
                                saveAs(blob, fileName);
                            }).error(function (err) {
                        console.log("Error during getting data", err);
                    });
                };

                $scope.productionPlanningSummary = function () {
                    $scope.isShow = !$scope.isShow;
                    if ($scope.isShow == false) {
                        handleEdgeCase();
                    }
                    if (appUtil.isEmptyObject($scope.selectedTab)) {
                        $scope.selectedTab = "TRANSFER";
                    }
                    if ($scope.isShow) {
                        if ($scope.selectedDate != null) {
                            $http({
                                url: apiJson.urls.requestOrderManagement.getSummaryForProductionPlanning,
                                method: "GET",
                                params: {
                                    date: $scope.selectedDate,
                                    fulfillmentUnit: appUtil.getCurrentUser().unitId,
                                    region: appUtil.unitData.region
                                }
                            }).then(function (response) {
                                $scope.productionPlanningSummaryData = response.data.productionPlanningSummary;
                                $scope.setSummaryData(response, $scope.productionPlanningSummaryData);
                            }, function (response) {
                                console.log(response);
                            });
                        } else {
                            alert("select date");
                        }
                    }
                };

                $scope.checkCity = function (city) {
                    console.log(city);
                    //console.log( $scope.regionCity);
                    for (var i = 0; i < $scope.regionCity.length; i++) {
                        if ($scope.regionCity[i].name == city) {
                            $scope.regionCity[i].selected = !$scope.regionCity[i].selected;
                        }
                    }
                    $scope.tempList=[];
                    for (var i = 0; i < $scope.regionCity.length; i++) {
                        if($scope.regionCity[i].selected ==true){
                            for(var j=0; j<$scope.allActiveUnit.length; j++){
                                if($scope.regionCity[i].name == $scope.allActiveUnit[j].city){
                                    $scope.tempList.push( $scope.allActiveUnit[j]);
                                }
                            }
                        }
                    }
                    console.log($scope.tempList);
                };

                $scope.guideMeList=[];
                $scope.allSelect = function (){
                    console.log("we have selected all");
                    $scope.selectAllToggle=!$scope.selectAllToggle
                    for (var i=0; i< $scope.tempList.length ;i++){
                        $scope.tempList[i].selected=$scope.selectAllToggle;
                    }

                };

                $scope.singleSelect = function (id){
                    for (var i=0; i< $scope.tempList.length ;i++){
                        if( $scope.tempList[i].id==id){
                        $scope.tempList[i].selected=!$scope.tempList[i].selected;
                        }
                    }
                };

                function initCheckBoxModal() {
                    $scope.checkBoxModal = {};
                    $scope.checkBoxModal.checkAllBuffered = false;
                    $scope.checkBoxModal.updatedBuffered = null;
                    $scope.checkBoxModal.checkAllUpdatedDates = false;
                    $scope.checkBoxModal.updatedExpiryDate = null;
                    // $scope.changeAllBuffered();
                }

                $scope.changeAllBuffered = function () {
                    if(($scope.checkBoxModal.checkAllBuffered === true && $scope.checkBoxModal.updatedBuffered > 10) || ($scope.checkBoxModal.updatedBuffered <0) ){
                        alert("Please Enter Buffered Percentage Less Or Equals To 10!");
                        $scope.checkBoxModal.checkAllBuffered = false;
                        $scope.checkBoxModal.updatedBuffered=null;
                        return;
                    }
                    if ($scope.checkBoxModal.checkAllBuffered === true) {
                        if ($scope.checkBoxModal.updatedBuffered == (null || undefined)) {
                            alert("Please Enter Buffered Rate");
                            $scope.checkBoxModal.checkAllBuffered = false;
                            return false;
                        } else {
                            for (var i = 0; i < $scope.itemsMap[$scope.openItemType].items.length; i++) {
                                if($scope.itemsMap[$scope.openItemType].items[i].recipeRequire == true && $scope.itemsMap[$scope.openItemType].items[i].itemType  == "REQUESTED") {
                                    $scope.itemsMap[$scope.openItemType].items[i].bufferedPercentage = $scope.checkBoxModal.updatedBuffered;
                                    $scope.itemsMap[$scope.openItemType].items[i].updatedBuffered = $scope.checkBoxModal.updatedBuffered;
                                    $scope.itemsMap[$scope.openItemType].items[i].bufferedQuantity = parseFloat($scope.checkBoxModal.updatedBuffered * $scope.itemsMap[$scope.openItemType].items[i].requestedQuantity / 100).toFixed(0);
                                    $scope.itemsMap[$scope.openItemType].items[i].totalQuantity = $scope.itemsMap[$scope.openItemType].items[i].requestedQuantity + parseInt($scope.itemsMap[$scope.openItemType].items[i].bufferedQuantity);
                                }
                                // $scope.changeBufferedQuantity( $scope.itemsMap[$scope.openItemType].items[i]);
                            }
                        }
                    }
                    else if ($scope.checkBoxModal.checkAllBuffered === false) {
                        for (var i = 0; i < $scope.itemsMap[$scope.openItemType].items.length; i++) {
                            $scope.checkBoxModal.updatedBuffered=null;
                            $scope.itemsMap[$scope.openItemType].items[i].updatedBuffered = null;
                            $scope.itemsMap[$scope.openItemType].items[i].bufferedPercentage = 0;
                            $scope.itemsMap[$scope.openItemType].items[i].bufferedQuantity = 0;
                            $scope.itemsMap[$scope.openItemType].items[i].totalQuantity = $scope.itemsMap[$scope.openItemType].items[i].requestedQuantity;
                        }
                    }
                    // $scope.updateItems();
                };
                $scope.changeAllExpiryDates = function () {
                    if ($scope.checkBoxModal.checkAllUpdatedDates === true) {
                        if ($scope.checkBoxModal.updatedExpiryDate == (null || undefined)) {
                            alert("Please Enter Expiry Date");
                            $scope.checkBoxModal.checkAllUpdatedDates = false;
                            return false;
                        } else {
                            for (var i = 0; i < $scope.itemsMap[$scope.openItemType].items.length; i++) {
                                if($scope.itemsMap[$scope.openItemType].items[i].recipeRequire == true && $scope.itemsMap[$scope.openItemType].items[i].itemType  == "REQUESTED") {
                                    $scope.itemsMap[$scope.openItemType].items[i].expiryDate = $scope.checkBoxModal.updatedExpiryDate;
                                }
                            }
                        }
                    }
                    else if ($scope.checkBoxModal.checkAllBuffered === false) {
                        $scope.checkBoxModal.checkAllUpdatedDates=false;
                        $scope.checkBoxModal.updatedExpiryDate = null;
                        for (var i = 0; i < $scope.itemsMap[$scope.openItemType].items.length; i++) {
                            if($scope.itemsMap[$scope.openItemType].items[i].recipeRequire == true && $scope.itemsMap[$scope.openItemType].items[i].itemType  == "REQUESTED") {
                                $scope.itemsMap[$scope.openItemType].items[i].expiryDate = $scope.checkBoxModal.updatedExpiryDate;
                            }
                        }
                    }
                    // $scope.updateItems();
                };

                $scope.updateItems = function () {
                    $scope.updateFlag = true;
                    $scope.updatedPlanItems = [];
                    angular.forEach($scope.itemsMap, function (value, key) {
                        for(var i=0;i<value.items.length; i++)
                        $scope.updatedPlanItems.push(value.items[i]);
                    });
                    $scope.updateFlag = true;
                }

                $scope.changeBufferedQuantity = function (bufferedItem) {
                    var item = bufferedItem;
                    if ((item.bufferedPercentage != null && item.bufferedPercentage > 10) ||(item.bufferedPercentage <0)){
                        alert("Please Enter Buffered Percentage Less Or Equals To 10 For The Product!");
                        item.updatedBuffered = null;
                        item.bufferedPercentage = 0;
                        item.bufferedQuantity = 0;
                        item.totalQuantity = item.requestedQuantity;
                        return false;
                    }
                    if(item.bufferedPercentage != null && item.bufferedPercentage!=undefined) {
                        item.bufferedQuantity = parseFloat(item.bufferedPercentage * item.requestedQuantity / 100).toFixed(0);
                        item.totalQuantity = item.requestedQuantity + parseInt(item.bufferedQuantity);
                    }else{
                        item.updatedBuffered = null;
                        item.bufferedQuantity = 0;
                        item.totalQuantity = item.requestedQuantity;
                    }
                    // $scope.updateItems();
                };

                $scope.guideMe = function (){
                    $scope.guideMeList=[];
                    console.log($scope.dataEntry);
                    for (var i=0; i< $scope.tempList.length ;i++) {
                        if( $scope.tempList[i].selected==true) {
                            $scope.guideMeList.push({
                                unitId: $scope.tempList[i].id,
                                salesData: $scope.dataEntry
                            });
                        }
                    }
                    $scope.getSalesPercentage();
                    console.log($scope.guideMeList);
                }

                function handleEdgeCase() {
                    $scope.isShow = false;
                    $scope.allUnitWithRos=false;
                    if ($scope.regionCity != null) {
                        for (var i = 0; i < $scope.regionCity.length; i++) {
                            $scope.regionCity[i].selected = false;
                        }
                    }
                    $scope.tempList = [];

                }

                $scope.unitWithAllRos=function (){
                    handleEdgeCase();
                    $scope.allUnitWithRos=true;
                    $scope.tempList = [];
                    var j=0;
                    for(var i in $scope.untiWiseGntAndChaayos){
                            $scope.tempList[j]=$scope.untiWiseGntAndChaayos[i].unitBasicDetails;
                            $scope.tempList[j].gntFlag=$scope.untiWiseGntAndChaayos[i].gntFlag;
                            $scope.tempList[j].chaayosFlag=$scope.untiWiseGntAndChaayos[i].chaayosFlag;
                            $scope.tempList[j].selected=false;
                            j++;
                    }
                }
                $scope.restToSummary=function (){
                    $scope.tempList = [];
                    $scope.allUnitWithRos=false;
                    handleEdgeCase();
                    $scope.productionPlanningSummary();
                }

                $scope.changeBufferPercentage=function (bufferedItem){
                    var item = bufferedItem;
                    if(item.bufferedQuantity<0 || item.bufferedQuantity==undefined || item.bufferedQuantity==""  || item.bufferedQuantity==''){
                        item.updatedBuffered = null;
                        item.bufferedPercentage = 0;
                        item.bufferedQuantity = 0;
                        item.totalQuantity = item.requestedQuantity;
                        return false;
                    }
                    if(item.bufferedQuantity != null && item.bufferedQuantity!=undefined) {
                        item.bufferedPercentage = parseFloat((item.bufferedQuantity * 100) / item.requestedQuantity).toFixed(0);
                        item.totalQuantity = item.requestedQuantity + parseInt(item.bufferedQuantity);
                    }
                    if (item.bufferedPercentage != null && item.bufferedPercentage > 10) {
                        alert("Please Enter Buffered Percentage Less Or Equals To 10 For The Product!");
                        item.updatedBuffered = null;
                        item.bufferedPercentage = 0;
                        item.bufferedQuantity = 0;
                        item.totalQuantity = item.requestedQuantity;
                        return false;
                    }
                }
            }]);
