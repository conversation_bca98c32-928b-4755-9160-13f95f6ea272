/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by shikhar on 11-05-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('attrValueMappingCtrl', ['$http', '$rootScope', '$scope', 'authService', '$location', '$state',
        'appUtil', '$stateParams', 'metaDataService', '$toastService', 'apiJson',
        function ($http, $rootScope, $scope, authService, $location, $state, appUtil, $stateParams, metaDataService, $toastService, apiJson) {

            $scope.attributes = [];
            $scope.values = {};
            $scope.selectedValues = [];
            $scope.countValue = 0;
            $scope.addedValues = [];
            $scope.attributeId = null;
            $scope.mappingToEdit = {};

            $scope.selectOptions = {width: '100%'};

            function getAllValues(callback) {
                metaDataService.getAttributeValues(function(values){
                    $scope.values = values;
                    if(callback != undefined){
                        callback();
                    }
                });
            }

            function reinit(){
                getAllValues(function(){
                    resetForm();
                    $scope.reset();
                });
            }

            function resetForm() {
                $scope.getValues($scope.attributeId);
                $scope.addedValues = [];
                $scope.countValue = 0;
            }

            $scope.getCount = function (count) {
                return new Array(count);
            };

            $scope.init = function () {
                metaDataService.getAttrDefinitions(function (attributes) {
                    var data = attributes;
                    for (var key in data) {
                        var subArray = data[key];
                        $scope.attributes = $scope.attributes.concat(subArray);
                    }
                });
                getAllValues();
            };

            $scope.getValues = function (attributeId) {
                $scope.selectedValues = $scope.values[attributeId];
            };

            $scope.addToCount = function () {
                if($scope.countValue > 0){
                    return;
                }
                $scope.countValue += 1;
            };

            $scope.getAttribute = function (id) {
                var attribute = appUtil.getAttribute(id);
                return !appUtil.checkEmpty(attribute) ? attribute.attributeName : "Not found";
            };

            $scope.editValue = function (mapping) {
                $scope.mappingToEdit = mapping;
            };


            $scope.reset = function () {
                $scope.mappingToEdit = {};
            };

            $scope.submitValue = function (mappingToEdit) {
                if (!appUtil.isEmptyObject(mappingToEdit)) {
                    $http({
                        method: 'PUT',
                        url: apiJson.urls.attributeManagement.value,
                        data: mappingToEdit
                    }).then(function (response) {
                        $toastService.create("Updated value to attribute successfully", function () {
                            reinit();

                        });
                    }, function (response) {
                        console.log("Encoutered an error :::", response);
                    });
                }
            };


            $scope.addAttrValues = function () {

                if (!appUtil.checkEmpty($scope.attributeId)) {
                    for (var index=0; index< $scope.addedValues.length; index++) {
                        $scope.addedValues[index].attributeDefinitionId = $scope.attributeId;
                        $scope.addedValues[index].attributeValueStatus = "ACTIVE";
                    }

                    $http({
                        method: 'POST',
                        url: apiJson.urls.attributeManagement.value,
                        data: $scope.addedValues
                    }).then(function (response) {
                        $toastService.create("Added value to attribute successfully", function () {
                            reinit();
                        });
                    }, function (response) {
                        console.log("Encountered an error :::", response);
                    });
                } else {
                    $toastService.create("Please select a attribute first");
                }
            };

            $scope.cancel = function () {
                resetForm();
            };
        }
    ]
);
