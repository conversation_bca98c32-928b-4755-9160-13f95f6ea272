'use strict';

angular.module('scmApp')
    .controller('assetConvertorCtrl', ['$http', '$scope', 'appUtil', 'metaDataService', '$toastService',
        'apiJson', '$alertService', 'Popeye',
        function ($http, $scope, appUtil, metaDataService,
                  $toastService, apiJson, $alertService, Popeye) {
            $scope.init = function () {
                $scope.productList = [];
                $scope.productList = appUtil.getScmProductDetails();
                $scope.selectedProduct = null;
                $scope.productQuantityMap = {};
                $scope.currentUnit = appUtil.getUnitData();
                $scope.selectedAssets = {};
                $scope.productMap = {};
                createProductMap();
                $scope.getNonAssetProductStock();
                $scope.assetList = [];



            }

            function  createProductMap (){
                      for(var i =0;i<$scope.productList.length;i++){
                          $scope.productMap[$scope.productList[i].productId] = $scope.productList[i];
                      }
            }


            $scope.getNonAssetProductStock = function (){
                $http({
                    url: apiJson.urls.assetManagement.getNonAssetStockInAssetInventory,
                    method: "GET"
                }).then(function (response) {
                    if (response.data != undefined && response.data != null) {
                        $scope.productQuantityMap = response.data;
                        for(var productId in $scope.productQuantityMap){
                            var productInfo = $scope.productMap[productId];
                            productInfo.qty  = $scope.productQuantityMap[productId];
                             $scope.assetList.push(productInfo);
                        }

                    }
                }, function (response) {
                    console.log(response);
                    $toastService.create("Server Error");
                });
            }

            $scope.validateQty = function(product,input,availableQty){
                if(input < 0 || input > availableQty || appUtil.isFloat(input)){
                    $toastService.create("Please Enter Valid Qty !!");
                    product.transferQty = 0;
                }
            }

            $scope.convertAssets = function (){
                var selectedProductList = [];
                var selectedProductMap = {};
                for(var i =0;i<$scope.assetList.length;i++){
                    if($scope.assetList[i].checked == true){
                        if($scope.assetList[i].transferQty == null || $scope.assetList[i].transferQty == 0){
                            $toastService.create("Please Enter Transfer Qty !!!");
                            return;
                        }
                        selectedProductMap[$scope.assetList[i].productId] = $scope.assetList[i].transferQty;
                        //selectedProductList.push($scope.assetList[i].productId);
                    }
                }

                $http({
                    url: apiJson.urls.assetManagement.convertNonAssetStockcInAssetInentory,
                    method: "POST",
                    data : selectedProductMap
                }).then(function (response) {
                    if (response.data != undefined && response.data != null) {
                       $toastService.create("SuccessFully Converted Assets !!");
                       $scope.init();

                    }
                }, function (response) {
                    console.log(response);
                    $toastService.create("Server Error");
                });

            }

        }]
);


