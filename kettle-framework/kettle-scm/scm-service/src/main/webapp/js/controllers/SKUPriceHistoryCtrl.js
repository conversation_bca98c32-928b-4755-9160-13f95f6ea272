angular.module('scmApp').controller('SKUPriceHistoryCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil',
    '$location', 'fileService', '$alertService','$toastService','metaDataService','$timeout','$filter',
    function ($rootScope, $scope, apiJson, $http, appUtil, $location, fileService, $alertService,$toastService, metaDataService,$timeout, $filter) {

        $scope.init = function () {

            $scope.getAllVendors();
            getAllMappings();
            getDeliveryLocations();
            $scope.selectedVendor = null;
            $scope.selectedSku = null;
            $scope.selectedDispatchLocation = null;
            $scope.selectedDeliveryLocation = null;
            $scope.selectedPackaging = null;
                

            //for ui-grid
            $scope.showGrid = false;
            $scope.disableFindButton = true;
            $scope.showChart = false;

            //for graph
            $scope.seriesData = {};

            $scope.series = [];
            $scope.packagingMap = appUtil.getPackagingMap();


        };
        $scope.getAllVendors = function () {
            $http({
                url: apiJson.urls.vendorManagement.vendorsTrimmed,
                method: 'GET'
            }).success(function (response) {
                $rootScope.showFullScreenLoader = false;
                $scope.vendorList = [];
                response.map(function (item) {
                    $scope.vendorList.push(item);
                });
            }).error(function (response) {
                $rootScope.showFullScreenLoader = false;
                if (response.errorMsg != null) {
                    $alertService.alert(response.errorTitle, response.errorMsg, null, true);
                } else {
                    $toastService.create("Could not get vendors list.");
                }
            });
        };

        $scope.setDispatchLocationList = function(vendorId){
            metaDataService.getVendorDetail(vendorId,function(vendor){
                $scope.locationList = vendor.dispatchLocations.filter(function (loc) {
                    return loc.status == "ACTIVE";

                })
                if($scope.locationList.length == 1){
                    $scope.selectDispatchLocation = $scope.locationList[0];
                    $timeout(function(){
                        $('#locationList').trigger('change');
                    });
                }
            });

        };

        $scope.setSelectedPackaging = function (packaging) {
            $scope.selectedPackaging = packaging;  
        };


        $scope.getData = function (selectedVendor) {
            $timeout(function(){
                $('#skuList').val(null).trigger('change');
            });
            $scope.selectedVendor = selectedVendor;
            if($scope.selectedVendor != null ){
                $http({
                    url: apiJson.urls.skuMapping.getSkuForVendor,
                    method: 'POST',
                    data: $scope.selectedVendor.id
                }).then(function (response) {
                    $scope.skuList = response.data;

                }, function (response) {
                    $scope.skuList = [];
                    console.log("error", response);
                });
                $scope.selectedSku = null;
                $scope.selectedPackaging = null;
                $scope.disableFindButton = true;


                $scope.setDispatchLocationList($scope.selectedVendor.id);
                $scope.series = [];
                $scope.seriesData = [];
                $scope.chartOptions  = {};

            }
        };

        $scope.getSkusData = function (selectedSku) {
            $scope.series = [];
            $scope.seriesData = [];
            $scope.chartOptions  = {};
            $scope.showChart = false;
            $scope.selectedSku = selectedSku;
            $scope.packagingList = $scope.skuMapping[$scope.selectedSku.id];
            $scope.selectedPackaging = null;
            if($scope.packagingList.length == 1){
                $scope.selectedPackaging = $scope.packagingList[0];
            }
            $scope.disableFindButton = false;
            getPriceHistoryRecord();
        }

        function getDeliveryLocations() {
            $http({
                method: "GET",
                url: apiJson.urls.scmMetadata.deliveryLocations,
            }).then(function success(response) {
                if(!appUtil.isEmptyObject(response)){
                    $scope.deliveryLocationList = response.data;
                }else{
                    $toastService.create("Error while fetching delivery locations!");
                }
            }, function error(response) {
                console.log("error:" + response);
            });
        }

        function transformPackagingMap(tempSkuMappings){
            $scope.skuMapping = {};
            Object.keys(tempSkuMappings).forEach(function(skuId){
                $scope.skuMapping[skuId] = [];
                for(var packaging in tempSkuMappings[skuId]){
                    if(tempSkuMappings[skuId][packaging].mappingStatus === "ACTIVE") {
                        $scope.skuMapping[skuId].push($scope.packagingMap[tempSkuMappings[skuId][packaging].packagingId]);
                    }
                }
            });
        }

        function getAllMappings() {
            $http({
                method: "GET",
                url: apiJson.urls.productManagement.skuPackagingMappings
            }).then(function (response) {
                var tempSkuMappings = response.data;
                transformPackagingMap(tempSkuMappings);
            }, function (response) {
                console.log("Error while getting mappings", response);
            });
        }

        $scope.gridOptions = {
             enableGridMenu: true,
             exporterExcelFilename: 'download.xlsx',
             exporterExcelSheetName: 'Sheet1',
             enableColumnMenus: true,
             saveFocus: false,
             enableRowSelection: true,
             enableFiltering: true,
             saveScroll: true,
             enableSelectAll: true,
             multiSelect: true,
             enableColumnResizing: true,
             exporterMenuPdf : false,
             exporterMenuExcel : true,
             fastWatch: true,
             onRegisterApi: function(gridApi){
                $scope.gridApi = gridApi;
             },
         };

        $scope.gridOptions.columnDefs = [
            { name: 'skuPriceDataId', displayName: 'Record Id'},
            { name: 'negotiatedPrice', displayName: 'Price'},
            { name: 'createdAt', displayName: 'Update Date'},
            { name: 'createdBy', displayName: 'Updated By'}
        ]
        $scope.showSkuGrid = function() {
            getPriceHistoryRecord();
            $scope.showGrid = true;
        };
        $scope.expandAllRows = function() {
          $scope.gridApi.expandable.expandAllRows();
        };
        $scope.collapseAllRows = function() {
          $scope.gridApi.expandable.collapseAllRows();
        };


        function getPriceHistoryRecord(){

            $http({
                method: "POST",
                url: apiJson.urls.vendorManagement.getPriceHistory,
                data :  {
                           vendorId : $scope.selectedVendor.id,
                           skuId : $scope.selectedSku.id,
                           packagingId : $scope.selectedPackaging != null ? $scope.selectedPackaging.packagingId : -1,
                           dispatchLocation : $scope.selectedDispatchLocation != null ? $scope.selectedDispatchLocation.locationName : "",
                           deliveryLocation : $scope.selectedDeliveryLocation != null ? $scope.selectedDeliveryLocation.name : ""
                         }
            }).then(function (response) {
                $scope.data = response.data;
                $scope.gridOptions.data = response.data;
                getPriceHistoryGraph();

            }, function (response) {
                 console.log("Error while getting mappings", response);
            });

        };

        function getPriceHistoryGraph() {
            $scope.dates = [];
            $scope.outerMap = {};
            $scope.firstValue = {};
            $scope.seriesData = {};
            $scope.series = [];
            for(var i = 0; i < $scope.data.length; i++) {
                if($scope.dates.indexOf($scope.data[i].createdAt.substring(0,11)) == -1){
                    $scope.dates.push($scope.data[i].createdAt.substring(0,11));
                };
                if($scope.outerMap[$scope.data[i].packagingId] == null){
                    $scope.outerMap[$scope.data[i].packagingId] = {};
                }
                if($scope.seriesData[$scope.data[i].packagingId] == null){
                    $scope.seriesData[$scope.data[i].packagingId] = [];
                        $scope.firstValue[$scope.data[i].packagingId] = $scope.data[i].negotiatedPrice;
                        $scope.outerMap[$scope.data[i].packagingId][$scope.data[i].createdAt.substring(0,11)] = $scope.data[i].negotiatedPrice;
                };
                $scope.outerMap[$scope.data[i].packagingId][$scope.data[i].createdAt.substring(0,11)] = $scope.data[i].negotiatedPrice;
            };

            $scope.dates.sort(function(a, b){
               var dateA = new Date(a);
               var dateB = new Date(b);
               return dateA - dateB;
             });
             console.log($scope.dates);

            for(var keys in $scope.outerMap){
               for(var date in $scope.dates){
                   if($scope.outerMap[keys][$scope.dates[date]] == null){
                        if(date == 0){
                            $scope.outerMap[keys][$scope.dates[date]] = $scope.firstValue[keys];
                        }
                        else {
                            $scope.outerMap[keys][$scope.dates[date]] = $scope.outerMap[keys][$scope.dates[date-1]];
                        }
                   }
               }

               $scope.myArray = [];
               angular.forEach($scope.outerMap[keys], function(value, key) {
                 $scope.myArray.push({ key: key, value: value });
               });

                $scope.sortedArray = $filter('orderBy')($scope.myArray, function(item) {
                  return new Date(item.key);
                });
                for(var date in $scope.dates){
                    $scope.seriesData[keys].push($scope.outerMap[keys][$scope.dates[date]]);
                };

            }
                console.log($scope.outerMap);
                console.log($scope.seriesData);


                for (var i = 0; i <$scope.packagingList.length; i++){

                    $scope.series.push({
                        name: $scope.packagingList[i].packagingName,
                        data : $scope.seriesData[$scope.packagingList[i].packagingId]
                    });
                };
                

            $scope.chartOptions  = {
                   chart: {
                       type: 'line',
                       height: 550,
                       width: 1350
                   },
                   title:{
                       text: $scope.selectedSku.name
                   },
                   subtitle: {
                       text: $scope.selectedVendor.name
                   },
                   xAxis: {
                       categories: $scope.dates,
                       title:{
                           text:'Update Date'
                       },
                   },
                   yAxis: {
                       title:{
                           text:'Price'
                       },
                   },
                   plotOptions: {
                       line: {
                           dataLabels: {
                               enabled: true
                           }
                       }
                   },
                   series: $scope.series
               }
               $scope.showChart = true;
        };

        $scope.createCharts = function(){

                    var element = document.getElementById("myChart");
                    $scope.charts = Highcharts.chart(element,$scope.chartOptions);
        }

    }]);