/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

angular
    .module('scmApp')
    .controller(
        'prodHistoryCtrl',
        [
            '$rootScope',
            '$scope',
            'apiJson',
            '$http',
            'appUtil',
            '$toastService',
            'metaDataService',
            '$window',
            '$timeout',
            'previewModalService',
            '$fileUploadService',
            'fileService',
            'Popeye',
            function ($rootScope, $scope, apiJson, $http, appUtil, $toastService, metaDataService, $window, $timeout, previewModalService,
                      $fileUploadService,fileService,Popeye) {

                $scope.isShow=false;
                $scope.planId=0;
                $scope.init = function () {
                    $scope.reset();
                    $scope.startDate = appUtil.formatDate(appUtil.getDate(0), "yyyy-MM-dd");
                    $scope.endDate = appUtil.formatDate(appUtil.getDate(0), "yyyy-MM-dd");
                    $scope.getPlans();
                    $scope.showPreview = previewModalService.showPreview;
                    $scope.warehouseOrKitchen = appUtil.isWarehouse();
                    $scope.isForAdjustments = false;
                    $scope.isMultiPackaging = false;
                    $scope.editPackagings = false;
                    $scope.isAutoMaticDistribution = false;
                    $scope.selectedPackaging = null;
                    initCheckBoxModal();
                    $scope.listOfRos = [];
                    $scope.selectedProduct = null;
                    $scope.updatedList = [];
                    $scope.getUnitProductPackagingMappings();
                    $scope.productPackaging = [];
                    metaDataService.getAllPackagingMappings(function (packagingMap) {
                        $scope.productPackaging = packagingMap;
                    },true);
                    $scope.displayConversions = {};
                    $scope.duplicateConversionRatio = null;
                    $scope.planIdArray=[];
                    $scope.showBtn=false;
                    $scope.checkAll = false;
                    setPackagingByProducts();
                };

                $scope.reset = function () {
                    $scope.allCheck=false;
                    $scope.plans = null;
                };

                $scope.toggleAllCheckboxes = function(){
                    $scope.checkAll = !$scope.checkAll;
                }

                $scope.validateBtn=function(){
                    var check = false;
                    angular.forEach($scope.plans,function(plan){
                        if(plan.checked){
                            check = true;
                        }
                    });
                    if (check) {
                        $scope.showBtn=true;
                    } else {
                        $scope.showBtn=false;
                    }

                }

                $scope.selectAll = function () {
                    $scope.allCheck = !$scope.allCheck;
                    if ($scope.allCheck == false) {
                        $scope.plans.forEach(function (plan) {
                            plan.checked = false;
                        });
                    } else {
                        $scope.plans.forEach(function (plan) {
                            plan.checked = true;
                        });
                        $scope.showBtn=true;
                    }
                };

                $scope.checkDates = function () {
                    return $scope.startDate != null && $scope.endDate != null
                        && new Date($scope.startDate).getTime() <= new Date($scope.endDate).getTime();
                };

                $scope.getPlans = function () {
                    $scope.listOfRos = [];
                    $scope.selectedProduct = null;
                    $scope.updatedList = [];
                    $scope.displayConversions = {};
                    initCheckBoxModal();
                    $scope.finalAdjustmentsRequestedList = [];
                    $scope.roItemsData = [];
                    if ($scope.checkDates()) {
                        var payload = {
                            unitId: appUtil.getCurrentUser().unitId,
                            startDate: $scope.startDate,
                            endDate: $scope.endDate
                        };
                        $http({
                            url: apiJson.urls.requestOrderManagement.plansByFulfillmentDate,
                            method: "POST",
                            data: payload
                        }).then(function (response) {
                            $scope.plans = response.data;
                        }, function (response) {
                            console.log(response);
                        });
                    } else {
                        $toastService.create("Select a date first");
                    }
                };

                function sortByUnit(a, b) {
                    if (a.requestUnit.name < b.requestUnit.name)
                        return -1;
                    if (a.requestUnit.name > b.requestUnit.name)
                        return 1;
                    return 0;
                }

                $scope.acknowledgeAll = function () {
                    $scope.requestOrders.forEach(function (order) {
                        order.checked = true;
                    });
                };

                $scope.downloadSelectedPlanOrders=function(){

                    var ids = [];
                    angular.forEach($scope.plans,function (plan) {
                        if (plan.checked) {
                            ids.push(plan.id);
                        }
                    });
                    if(ids.length>0){
                        $http({
                            url: apiJson.urls.requestOrderManagement.downloadPlanOrdersZip,
                            method: "POST",
                            data: ids,
                            responseType: 'arraybuffer',
                            headers: {
                                'Content-type': 'application/json',
                                'Accept': 'application/zip'
                            }
                        }).then(function (response) {
                            var now = new Date().toISOString().slice(0, 10);
                            var fileName = "Updated Request Order Sheet-" +now+ ".zip";
                            var blob = new Blob(
                                [response.data],
                                {
                                    type: 'application/zip'
                                }, fileName);
                            saveAs(blob, fileName);
                        }, function (response) {
                            console.log(response);
                        });
                    }
                    else{
                        $toastService.create("Please select Atleast one Plan to Download");
                        }
                }

                $scope.downloadPlanorders = function (eventId) {
                    $http({
                        url: apiJson.urls.requestOrderManagement.downloadPlanOrders,
                        method: "POST",
                        data: eventId,
                        responseType: 'arraybuffer',
                        headers: {
                            'Content-type': 'application/json',
                            'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                        }
                    }).then(function (response) {
                        var fileName = "Updated Request Order Sheet-" + eventId + ".xlsx";
                        var blob = new Blob(
                            [response.data],
                            {
                                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                            }, fileName);
                        saveAs(blob, fileName);
                    }, function (response) {
                        console.log(response);
                    });
                };


                $scope.downloadRequestOrder = function (requestOrder) {
                    console.log("download a request order ::::", requestOrder);
                    $http({
                        url: apiJson.urls.requestOrderManagement.downloadOrder,
                        method: 'POST',
                        responseType: 'arraybuffer',
                        data: requestOrder,
                        headers: {
                            'Content-type': 'application/json',
                            'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                        }
                    }).success(function (data) {
                        var fulfillmentDate = appUtil.getFormattedDate(requestOrder.fulfillmentDate);
                        var fileName = "Request Order Sheet_"
                            + requestOrder.requestUnit.name + "_" + fulfillmentDate + "_"
                            + appUtil.formatDate(Date.now(), "dd-MM-yyyy-hh-mm-ss") + ".xls";
                        var blob = new Blob(
                            [data],
                            {
                                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                            }, fileName);
                        saveAs(blob, fileName);
                    }).error(function (err) {
                        console.log("Error during getting data", err);
                    });
                };

                $scope.isChecked = function (requestOrders) {
                    return requestOrders.filter(function (order) {
                        return order.checked;
                    }).length > 0;
                };

                $scope.submit = function (requestOrders) {
                    if ($scope.selectedDate != null) {
                        var aggregatedItems = [];
                        var requestOrdersItems = [];
                        $scope.aggregatedItems = [];
                        var selectedRequestOrders = requestOrders.filter(function (order) {
                            return order.checked;
                        });
                        selectedRequestOrders.forEach(function (order) {
                            requestOrdersItems = requestOrdersItems.concat(order.requestOrderItems);
                        });
                        var productMap = [];
                        requestOrdersItems
                            .forEach(function (orderItem) {
                                var item = productMap[orderItem.productId];
                                if (appUtil.checkEmpty(item)) {
                                    productMap[orderItem.productId] = {
                                        id: orderItem.productId,
                                        name: orderItem.productName,
                                        quantity: 0,
                                        uom: orderItem.unitOfMeasure
                                    };
                                }
                                productMap[orderItem.productId].quantity += orderItem.requestedAbsoluteQuantity;
                            });

                        productMap.forEach(function (product) {
                            $scope.aggregatedItems.push(product);
                        });

                        //console.log($scope.aggregatedItems);

                        if (selectedRequestOrders.length > 0) {
                            $http({
                                url: apiJson.urls.requestOrderManagement.acknowledgeOrders,
                                method: 'POST',
                                responseType: 'arraybuffer',
                                data: {
                                    fulfillmentDate: $scope.selectedDate,
                                    lastUpdatedBy: appUtil.createGeneratedBy(),
                                    requestOrders: selectedRequestOrders
                                },
                                headers: {
                                    'Content-type': 'application/json',
                                    'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                                }
                            }).success(
                                function (data) {
                                    var fileName = "Request Order Sheet-"
                                        + appUtil.formatDate(new Date($scope.selectedDate), "dd-MM-yyyy") + "-"
                                        + appUtil.formatDate(Date.now(), "dd-MM-yyyy-hh-mm-ss") + ".xls";
                                    var blob = new Blob(
                                        [data], {
                                            type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                                        }, fileName);
                                    saveAs(blob, fileName);
                                    $scope.init();
                                }).error(function (err) {
                                console.log("Error during getting data", err);
                            });
                        } else {
                            $toastService.create("Please acknowledge at least one request order before submitting");
                        }
                    } else {
                        $toastService.create("Please select a date first");
                    }
                };

                $scope.setPlanProductId = function (item) {
                    //$scope.planProductId = item.productId;
                    //$scope.planItemId = item.id;
                    //$scope.prepQuantity = item.requestedQuantity - item.availableQuantity > 0 ? item.requestedQuantity - item.availableQuantity : 0;
                    $scope.prepQuantity = item.totalQuantity;
                    $scope.selectedPlanProduct = item;
                    $scope.showPrepPlan = false;
                    $scope.prepPlanData = null;
                    $scope.printPlanData = null;
                };

                $scope.getPlanItemsForSemiFinishedProduct = function () {
                    $http({
                        url: apiJson.urls.requestOrderManagement.getPlanItemsForSemiFinishedProduct,
                        method: 'POST',
                        data: {

                            id: $scope.selectedPlanProduct.id,
                            productId: $scope.selectedPlanProduct.productId,
                            productName: $scope.selectedPlanProduct.productName,
                            requestedQuantity: $scope.prepQuantity,
                            requestedAbsoluteQuantity: $scope.prepQuantity,
                            unitId: appUtil.getCurrentUser().unitId
                        },
                        headers: {
                            'Content-type': 'application/json'
                        }
                    }).success(function (data) {
                        if (data !== null) {
                            $scope.prepPlanData = data;
                            $scope.showPrepPlan = true;
                            $scope.selectedPlanProduct.printCount = $scope.selectedPlanProduct.printCount === null ? 1 : $scope.selectedPlanProduct.printCount + 1;
                            $scope.formatPlan($scope.prepPlanData);
                        } else {
                            $toastService.create("Error while getting plan data.");
                        }
                    }).error(function (err) {
                        console.log("Error while getting data", err);
                    });
                };

                $scope.savePlanForSemiFinished = function () {
                    $scope.prepPlanData.requestedBy = {id: appUtil.getCurrentUser().userId, code: "", name: ""};
                    $http({
                        url: apiJson.urls.requestOrderManagement.submitPrepPlan + '/' + appUtil.getCurrentUser().unitId,
                        method: 'POST',
                        data: $scope.prepPlanData,
                        headers: {
                            'Content-type': 'application/json'
                        }
                    }).success(function (data) {
                        if (data !== null) {
                            //$scope.prepPlanData = data;
                            $scope.showPrepPlan = false;
                            $timeout(function () {
                                $scope.$apply(function () {
                                    $scope.printPlanData = angular.copy($scope.prepPlanData);
                                    $scope.printPlanData.planOrderItem.unitOfMeasure = $scope.selectedPlanProduct.unitOfMeasure;
                                    $scope.formatPlan($scope.printPlanData);
                                });
                                document.querySelector("#noPrint").style.display = "none";
                                $window.print();
                                document.querySelector("#noPrint").style.display = "block";
                            }, 0);
                        } else {
                            $toastService.create("Error while saving plan data.");
                        }
                    }).error(function (err) {
                        console.log("Error while saving data", err);
                    });
                };

                $scope.getProductPlans = function (roi) {
                    $scope.printPlanData = null;
                    $http({
                        url: apiJson.urls.requestOrderManagement.getPrepPlans + '/' + appUtil.getCurrentUser().unitId,
                        method: 'POST',
                        data: roi.id,
                        headers: {
                            'Content-type': 'application/json'
                        }
                    }).success(function (data) {
                        if (data !== null) {
                            $scope.plansForView = data;
                            $scope.plansForView.map(function (plan) {
                                $scope.formatPlan(plan);
                            });
                        } else {
                            $toastService.create("Error while saving plan data.");
                        }
                    }).error(function (err) {
                        console.log("Error while saving data", err);
                    });
                };

                $scope.expandPlan = function (plan) {
                    plan.expand = plan.expand !== true;
                };

                $scope.rePrintPlan = function (plan) {
                    $timeout(function () {
                        $scope.$apply(function () {
                            $scope.printPlanData = angular.copy(plan);
                            $scope.formatPlan($scope.printPlanData);
                        });
                        $window.print();
                    }, 0);
                };

                $scope.formatPlan = function (plan) {
                    var index = 0;
                    var set = true;
                    plan.recipeNotes = plan.recipeNotes != null ? plan.recipeNotes.replace(/\n/g, "<br />") : plan.recipeNotes;
                    plan.planOrderItemPrepItems.map(function (item) {
                        item.recipeNotes = item.recipeNotes != null ? item.recipeNotes.replace(/\n/g, "<br />") : item.recipeNotes;
                        if (item.planOrderItemPrepItems != null && item.planOrderItemPrepItems.length > 0) {
                            set = true;
                        }
                        if (set) {
                            index++;
                            set = false;
                        }
                        item.stepIndex = index;
                        if (item.planOrderItemPrepItems != null && item.planOrderItemPrepItems.length > 0) {
                            index++;
                        }
                    });
                };

                $scope.formatUom = function (amount, uom) {

                };

                $scope.filterDataByProduct = function (product) {
                    $scope.getDefaultPackaging(product.productId);
                    initCheckBoxModal();
                    $scope.listOfRos = [];
                    $scope.selectedProduct = product;
                    $scope.setDuplicates();
                    // $scope.listOfRos = $scope.filterRoByProduct(product);
                    $scope.listOfRos = $scope.filterRoByProduct(angular.copy($scope.copyOfListOfRos[product.productId]));
                };

                $scope.filterRoByProduct = function (productList) {
                    var result =  [];
                    for (var i in productList) {
                        productList[i].displayQuantity = parseFloat((productList[i].requestedQuantity/$scope.displayConversions.conversionRatio).toFixed(1));
                        result.push(productList[i]);
                    }
                    return result;
                };

                function initCheckBoxModal() {
                    $scope.checkBoxModal = {};

                    $scope.checkBoxModal.checkAll = false;
                    $scope.checkBoxModal.checkAllFinalQuantity = false;
                    $scope.checkBoxModal.checkAllAdjustmentReason = false;

                    $scope.checkBoxModal.updatedFinalQuantity = null;
                    $scope.checkBoxModal.updatedAdjustmentReason = null;
                }

                $scope.updateAll = function () {
                    if ($scope.checkBoxModal.checkAll === true) {
                        for (var j = 0; j < $scope.listOfRos.length; j++) {
                            if ($scope.listOfRos[j].transferred != true) {
                                $scope.listOfRos[j].checked = true;
                            }
                        }
                    } else if ($scope.checkBoxModal.checkAll === false) {
                        for (var j = 0; j < $scope.listOfRos.length; j++) {
                            $scope.listOfRos[j].checked = false;
                        }
                    }
                };

                $scope.setDuplicates = function() {
                    $scope.duplicateConversionRatio = null;
                    var stringOfRatio = $scope.displayConversions.conversionRatio.toString();
                    if (stringOfRatio.indexOf(".") != -1) {
                        $scope.duplicateConversionRatio = parseFloat(stringOfRatio) * 1000000;
                    }
                };

                $scope.changeQuantity = function(currentQuantity,selectedProduct, mapping, value, isItem) {
                    if (isItem) {
                        if (!mapping.packaging.itemCheckPack) {
                            mapping.packaging.itemQuantity = null;
                            calculateFinalQuantity(currentQuantity,selectedProduct,isItem);
                        }
                        else {
                            calculateQuantities(currentQuantity,selectedProduct,mapping,value,isItem);
                        }
                    }
                    else {
                        if (!mapping.packaging.checkPack) {
                            mapping.packaging.quantity = null;
                            calculateFinalQuantity(currentQuantity,selectedProduct,isItem);
                        }
                        else {
                            calculateQuantities(currentQuantity,selectedProduct,mapping,value,isItem);
                        }
                    }
                };

                function calculateQuantities(currentQuantity,selectedProduct,mapping,value,isItem) {
                    var finValue = value;
                    if (finValue != null) {
                        var finValueString = value.toString();
                        if (finValueString.indexOf(".") != -1 && mapping.packaging.packagingType != "LOOSE") {
                            $toastService.create("Please enter a whole number..! decimal is allowed only for LOOSE Packaging..!");
                            if (isItem) {
                                mapping.packaging.itemQuantity = null;
                                mapping.packaging.itemCheckPack = false;
                            }
                            else {
                                mapping.packaging.quantity = null;
                                mapping.packaging.checkPack = false;
                            }
                            return false;
                        }
                    }
                    if (isItem) {
                        mapping.packaging.itemQuantity = finValue;
                    }
                    else {
                        mapping.packaging.quantity = finValue;
                    }
                    calculateFinalQuantity(currentQuantity, selectedProduct,isItem);
                }

                function calculateFinalQuantity(currentQuantity,item,isItem) {
                    console.log("item is : ",item);
                    var currentQuantityDup = currentQuantity;
                    if (isItem) {
                        item.finalQuantity = 0;
                    }
                    else {
                        $scope.checkBoxModal.updatedFinalQuantity = 0;
                    }
                    for (var index in item.productPackagings) {
                        var mapping = item.productPackagings[index];
                        if (!appUtil.isEmptyObject(mapping)) {
                            if (isItem && mapping.packaging.itemCheckPack && !appUtil.isEmptyObject(mapping.packaging.itemQuantity)) {
                                item.finalQuantity += parseFloat((mapping.packaging.itemQuantity * mapping.packaging.conversionRatio).toFixed(6));
                            }
                            if (!isItem && mapping.packaging.checkPack && !appUtil.isEmptyObject(mapping.packaging.quantity)) {
                                $scope.checkBoxModal.updatedFinalQuantity += parseFloat((mapping.packaging.quantity * mapping.packaging.conversionRatio).toFixed(6));
                            }
                        }
                    }

                    if (isItem) {
                        if (item.finalQuantity < 0) {
                            $toastService.create("Quantity cannot be less than zero");
                            item.finalQuantity = currentQuantityDup;
                        }
                        else {
                            item.finalQuantity = parseFloat(item.finalQuantity.toFixed(6));
                            $scope.updateAdjustedQuantity(item,true);
                        }
                    }
                    else {
                        if ($scope.checkBoxModal.updatedFinalQuantity < 0) {
                            $toastService.create("Quantity cannot be less than zero");
                            $scope.checkBoxModal.updatedFinalQuantity = currentQuantityDup;
                        } else {
                            $scope.checkBoxModal.updatedFinalQuantity = parseFloat($scope.checkBoxModal.updatedFinalQuantity.toFixed(6));
                        }
                    }
                }

                $scope.preFillPacakging = function (product,packaging) {
                    $scope.selectedPackaging = packaging;
                    $scope.highLightPackaging(product.productPackagings,packaging);
                    $scope.changeQuantities(product,true);
                };

                $scope.highLightPackaging = function (packs, item) {
                    for (var pack in packs) {
                        if (packs[pack].packaging.packagingId == item.packaging.packagingId) {
                            packs[pack].packaging.highLight = true;
                        } else {
                            packs[pack].packaging.highLight = false;
                        }
                    }
                };

                $scope.disableHighLightPackaging = function (packs) {
                    for (var pack in packs) {
                        packs[pack].packaging.highLight = false;
                    }
                };

                $scope.applyQuantities = function (product,isPrefill) {
                    $scope.checkBoxModal.checkAllFinalQuantity = !$scope.checkBoxModal.checkAllFinalQuantity;
                    $scope.disableHighLightPackaging($scope.selectedProduct.productPackagings);
                    $scope.changeQuantities(product,isPrefill);
                };

                $scope.changeQuantities = function (product,isPrefill) {
                    console.log($scope.checkBoxModal.checkAllFinalQuantity);
                    if (isPrefill) {
                        for (var i = 0; i < $scope.listOfRos.length; i++) {
                            if ($scope.listOfRos[i].checked) {
                                $scope.listOfRos[i].finalQuantity = $scope.listOfRos[i].requestedAbsoluteQuantity;
                                var packagings = getPackaginsPrefillCopy(angular.copy($scope.listOfRos[i].productPackagings), $scope.selectedPackaging , $scope.listOfRos[i]);
                                $scope.listOfRos[i].productPackagings = packagings;
                                if ($scope.listOfRos[i].finalQuantity != null) {
                                    $scope.listOfRos[i].adjustedQuantity = parseFloat(($scope.listOfRos[i].finalQuantity - $scope.listOfRos[i].requestedAbsoluteQuantity).toFixed(6));
                                    if ($scope.listOfRos[i].adjustedQuantity > 0) {
                                        $scope.listOfRos[i].adjustedReason = "Adjustment - Excess";
                                    } else if ($scope.listOfRos[i].adjustedQuantity == 0) {
                                        $scope.listOfRos[i].adjustedReason = "No - Adjustment";
                                    } else {
                                        $scope.listOfRos[i].adjustedReason = "Adjustment - Short";
                                    }
                                }
                            }
                        }
                    }
                    else {
                        $scope.selectedPackaging = null;
                        if ($scope.checkBoxModal.checkAllFinalQuantity === true) {
                            if ($scope.checkBoxModal.updatedFinalQuantity == null || $scope.checkBoxModal.updatedFinalQuantity <= 0) {
                                $toastService.create("Please enter Quantity greater than 0 ...!");
                                $scope.checkBoxModal.checkAllFinalQuantity = false;
                                $scope.checkBoxModal.updatedFinalQuantity = null;
                                return false;
                            }
                            if ($scope.isAutoMaticDistribution) {
                                if ($scope.checkBoxModal.updatedFinalQuantity == 0) {
                                    $toastService.create("Please enter Quantity greater than 0 when auto distribution is on else off it...!");
                                    $scope.checkBoxModal.checkAllFinalQuantity = false;
                                    $scope.checkBoxModal.updatedFinalQuantity = null;
                                    return false;
                                }
                                autoDistribute($scope.checkBoxModal.updatedFinalQuantity, product, function (packs) {
                                    console.log("Final packs are : ", packs);
                                    getFinalPackagings(packs, product.productPackagings, function (packss) {
                                        if (packss == null) {
                                            $toastService.create("Can not auto distribute quantity .. please Update manually..!");
                                            angular.forEach(product.productPackagings, function (pack) {
                                                pack.packaging.checkPack = null;
                                                pack.packaging.quantity = null;
                                            });
                                            $scope.checkBoxModal.updatedFinalQuantity = null;
                                            $scope.checkBoxModal.checkAllFinalQuantity = false;
                                        } else {
                                            for (var i = 0; i < $scope.listOfRos.length; i++) {
                                                if ($scope.listOfRos[i].checked) {
                                                    $scope.listOfRos[i].updateManually = false;
                                                    console.log("Updated final quantity is ", $scope.checkBoxModal.updatedFinalQuantity, Math.round($scope.checkBoxModal.updatedFinalQuantity));
                                                    $scope.listOfRos[i].finalQuantity = $scope.checkBoxModal.updatedFinalQuantity;
                                                    $scope.listOfRos[i].productPackagings = getPackaginsCopy(angular.copy($scope.listOfRos[i].productPackagings), packss);
                                                    $scope.listOfRos[i].adjustedQuantity = parseFloat(($scope.listOfRos[i].finalQuantity - $scope.listOfRos[i].requestedAbsoluteQuantity).toFixed(6));
                                                    if ($scope.listOfRos[i].adjustedQuantity > 0) {
                                                        $scope.listOfRos[i].adjustedReason = "Adjustment - Excess";
                                                    } else if ($scope.listOfRos[i].adjustedQuantity == 0) {
                                                        $scope.listOfRos[i].adjustedReason = "No - Adjustment";
                                                    } else {
                                                        $scope.listOfRos[i].adjustedReason = "Adjustment - Short";
                                                    }
                                                }
                                            }
                                        }
                                    });
                                });
                            } else {
                                for (var i = 0; i < $scope.listOfRos.length; i++) {
                                    if ($scope.listOfRos[i].checked) {
                                        $scope.listOfRos[i].updateManually = false;
                                        console.log("Updated final quantity is ", $scope.checkBoxModal.updatedFinalQuantity, Math.round($scope.checkBoxModal.updatedFinalQuantity));
                                        $scope.listOfRos[i].finalQuantity = $scope.checkBoxModal.updatedFinalQuantity;
                                        var packagings = getPackaginsCopy(angular.copy($scope.listOfRos[i].productPackagings),angular.copy(product.productPackagings));
                                        $scope.listOfRos[i].productPackagings = packagings;
                                        $scope.listOfRos[i].adjustedQuantity = parseFloat(($scope.listOfRos[i].finalQuantity - $scope.listOfRos[i].requestedAbsoluteQuantity).toFixed(6));
                                        if ($scope.listOfRos[i].adjustedQuantity > 0) {
                                            $scope.listOfRos[i].adjustedReason = "Adjustment - Excess";
                                        } else if ($scope.listOfRos[i].adjustedQuantity == 0) {
                                            $scope.listOfRos[i].adjustedReason = "No - Adjustment";
                                        } else {
                                            $scope.listOfRos[i].adjustedReason = "Adjustment - Short";
                                        }
                                    }
                                }
                            }
                        } else if ($scope.checkBoxModal.checkAllFinalQuantity === false) {
                            for (var i = 0; i < $scope.listOfRos.length; i++) {
                                if ($scope.listOfRos[i].checked) {
                                    $scope.listOfRos[i].adjustedQuantity = null;
                                    $scope.listOfRos[i].finalQuantity = null;
                                    $scope.listOfRos[i].adjustedReason = null;
                                    $scope.listOfRos[i].updateManually = false;
                                    var item = $scope.listOfRos[i];
                                    for (var pack in item.productPackagings) {
                                        item.productPackagings[pack].packaging.quantity = 0;
                                        item.productPackagings[pack].packaging.itemCheckPack = false;
                                        item.productPackagings[pack].packaging.itemQuantity = null;
                                    }
                                }
                            }
                        }
                    }
                };

                function getFinalPackagings(packs,originalPacks,callBack) {
                    if (packs == null) {
                        callBack(packs);
                        return;
                    }
                    var result = [];
                    originalPacks.forEach(function (pack) {
                        result.push(pack);
                    })
                    for (var i in originalPacks) {
                        for (var j in packs) {
                            if (originalPacks[i].packagingId == packs[j].packagingId) {
                                result[i] = packs[j];
                                break;
                            }
                        }
                    }
                    callBack(result);
                }

                $scope.setAutoDistribute = function (flag,product) {
                    $scope.isAutoMaticDistribution = flag;
                    if (!flag) {
                        angular.forEach(product.productPackagings,function (pack) {
                            pack.packaging.checkPack = null;
                            pack.packaging.quantity = null;
                        });
                        $scope.checkBoxModal.updatedFinalQuantity = null;
                        $scope.checkBoxModal.checkAllFinalQuantity = false;
                    }
                };

                function autoDistribute(finalQuantity, product, callBack) {
                    var pgkDetails = [];
                    var qty = finalQuantity;
                    angular.forEach(product.productPackagings,function (mapping) {
                        mapping.packaging.quantity = null;
                        mapping.packaging.checkPack = false;
                    });

                    getCalculatedNumbers(product, "CASE", qty, pgkDetails , function (qty) {
                        if(qty > 0){
                            getCalculatedNumbers(product, "INNER", qty, pgkDetails, function (qty) {
                                if (qty > 0) {
                                    getCalculatedNumbers(product, "LOOSE", qty, pgkDetails, function (qty) {
                                        if (qty > 0) {
                                            pgkDetails = null;
                                        }
                                        callBack(pgkDetails);
                                    });
                                } else {
                                    callBack(pgkDetails)
                                }
                            });
                        } else {
                            callBack(pgkDetails)
                        }
                    });

                }

                function getCalculatedNumbers(product, type, qty, pgkDetails, callBack){
                    var ret = qty;
                    var packagingList = sortPackaging(product.productPackagings, type);
                    packagingList.forEach(function (item) {
                        var unitsPacked = 0;
                        if(type == item.packaging.packagingType){
                            if(type == "LOOSE"){
                                if((ret/item.packaging.conversionRatio)>0){
                                    unitsPacked = (ret/item.packaging.conversionRatio);
                                    ret = ret - (unitsPacked*item.packaging.conversionRatio);
                                }
                            }else{
                                if(parseInt(ret/item.packaging.conversionRatio)>0){
                                    unitsPacked = parseInt(ret/item.packaging.conversionRatio);
                                    ret = Math.round((ret - (unitsPacked*item.packaging.conversionRatio))*1000000)/1000000;
                                }
                            }
                            if(unitsPacked > 0){
                                item.packaging.checkPack = true;
                                item.packaging.quantity = unitsPacked;
                                pgkDetails.push(item);
                            }
                        }
                    });
                    callBack(ret);
                }

                function sortPackaging(packagings, type){
                    var pkgList = [];
                    packagings.forEach(function (pkg) {
                        if(pkg.packaging.packagingType == type){
                            pkgList.push(pkg);
                        }
                    });
                    pkgList.sort(function (a,b) {
                        return b.packaging.conversionRatio - a.packaging.conversionRatio;
                    });
                    return pkgList;
                }

                function getPackaginsPrefillCopy(packings,item,roItem) {
                    var unitsPacked = 0;
                    var ret = roItem.finalQuantity;
                    if (item.packaging.packagingType == "LOOSE") {
                        if ((ret / item.packaging.conversionRatio) > 0) {
                            unitsPacked = (ret / item.packaging.conversionRatio);
                            ret = ret - (unitsPacked * item.packaging.conversionRatio);
                        }
                    } else {
                        if (parseInt(parseFloat(ret / item.packaging.conversionRatio).toFixed(2)) > 0) {
                            unitsPacked = parseInt(parseFloat(ret / item.packaging.conversionRatio).toFixed(2));
                            ret = Math.round((ret - (unitsPacked * item.packaging.conversionRatio)) * 1000000) / 1000000;
                        }
                    }

                    if (ret > 0) {
                        $toastService.create("Can not Prefill the packaging for RO item : " + roItem.roItemId + " Please Update Manually..!");
                        console.log("for Ro item id : ",roItem.roItemId,"remaining quantity is : ",ret);
                        for (var pack in packings) {
                            packings[pack].packaging.itemQuantity = null;
                            packings[pack].packaging.itemCheckPack = false;
                        }
                        roItem.finalQuantity = null;
                        roItem.adjustedQuantity = null;
                        roItem.adjustedReason = null;
                        roItem.updateManually = true;
                    } else {
                        for (var pack in packings) {
                            if (item.packaging.packagingId == packings[pack].packaging.packagingId) {
                                packings[pack].packaging.itemQuantity = angular.copy(unitsPacked);
                                packings[pack].packaging.itemCheckPack = true;
                            } else {
                                packings[pack].packaging.itemQuantity = null;
                                packings[pack].packaging.itemCheckPack = false;
                            }
                        }
                        roItem.updateManually = false;
                    }
                    return packings;
                }

                function getPackaginsCopy(packings,productPacks) {
                    for (var pack in packings) {
                        for (var originalPack in productPacks) {
                            if (productPacks[originalPack].packaging.packagingId == packings[pack].packaging.packagingId) {
                                if (productPacks[originalPack].packaging.checkPack != undefined && productPacks[originalPack].packaging.checkPack != null
                                        && productPacks[originalPack].packaging.checkPack) {
                                    packings[pack].packaging.itemQuantity = angular.copy(productPacks[originalPack].packaging.quantity);
                                    packings[pack].packaging.itemCheckPack = angular.copy(productPacks[originalPack].packaging.checkPack);
                                } else {
                                    if (packings[pack].packaging.itemCheckPack != undefined && packings[pack].packaging.itemCheckPack != null) {
                                        packings[pack].packaging.itemQuantity = null;
                                        packings[pack].packaging.itemCheckPack = false;
                                    }
                                }
                                break;
                            }
                        }
                    }
                    console.log("Packs are : ",packings);
                    return packings;
                }

                $scope.onOffMultiPackagings = function (value,product) {
                    $scope.isMultiPackaging = value;
                    initCheckBoxModal();
                    $scope.isAutoMaticDistribution = false;
                    if (!$scope.isMultiPackaging) {
                        angular.forEach(product.productPackagings,function (mapping) {
                            mapping.packaging.quantity = null;
                            mapping.packaging.checkPack = false;
                        });
                    }
                    $scope.listOfRos = $scope.filterRoByProduct(angular.copy($scope.copyOfListOfRos[product.productId]));
                    $scope.selectedPackaging = null;
                    $scope.disableHighLightPackaging($scope.selectedProduct.productPackagings);
                };

                $scope.changeAllQuantities = function () {
                    console.log($scope.checkBoxModal.checkAllFinalQuantity);
                    if ($scope.checkBoxModal.checkAllFinalQuantity === true) {
                        if ($scope.checkBoxModal.updatedFinalQuantity == null || $scope.checkBoxModal.updatedFinalQuantity <0 ) {
                            $toastService.create("Please enter Quantity greater than 0 ...!");
                            $scope.checkBoxModal.checkAllFinalQuantity = false;
                            $scope.checkBoxModal.updatedFinalQuantity = null;
                            return false;
                        }
                        else if ($scope.duplicateConversionRatio != null) {
                            console.log("Had a decimal conversion ratio ",$scope.duplicateConversionRatio);
                            var stringOfFinalQuantity = $scope.checkBoxModal.updatedFinalQuantity.toString();
                            if (stringOfFinalQuantity.indexOf(".") == -1) {
                                stringOfFinalQuantity = stringOfFinalQuantity + ".000000";
                            }
                            var duplicateFinalQuantity = null;
                            console.log("String Of Final Quantity is ",stringOfFinalQuantity);
                            $scope.checkBoxModal.updatedFinalQuantity = parseFloat(parseFloat(stringOfFinalQuantity).toFixed(6));
                            duplicateFinalQuantity =  Math.round($scope.checkBoxModal.updatedFinalQuantity * 1000000);
                            console.log("Duplicates are ratio and quantity: ",$scope.duplicateConversionRatio,duplicateFinalQuantity);
                            if (duplicateFinalQuantity % $scope.duplicateConversionRatio > 0) {
                                $toastService.create("Please enter Quantity in the multiple of " + $scope.displayConversions.conversionRatio );
                                $scope.checkBoxModal.updatedFinalQuantity = null;
                                if ($scope.checkBoxModal.checkAllFinalQuantity != null && $scope.checkBoxModal.checkAllFinalQuantity) {
                                    $scope.checkBoxModal.checkAllFinalQuantity = false;
                                }
                                return false;
                            }
                        }
                        else if ($scope.duplicateConversionRatio == null && $scope.checkBoxModal.updatedFinalQuantity % $scope.displayConversions.conversionRatio > 0) {
                            $toastService.create("Please enter Quantity in the multiple of " + $scope.displayConversions.conversionRatio );
                            $scope.checkBoxModal.checkAllFinalQuantity = false;
                            $scope.checkBoxModal.updatedFinalQuantity = null;
                            return false;
                        }

                        for (var i = 0; i < $scope.listOfRos.length; i++) {
                            if($scope.listOfRos[i].checked){
                                $scope.listOfRos[i].updateManually = false;
                                console.log("Updated final quantity is ",$scope.checkBoxModal.updatedFinalQuantity,Math.round($scope.checkBoxModal.updatedFinalQuantity));
                                $scope.listOfRos[i].finalQuantity =  $scope.checkBoxModal.updatedFinalQuantity;
                                $scope.listOfRos[i].adjustedQuantity = parseFloat(($scope.listOfRos[i].finalQuantity - $scope.listOfRos[i].requestedAbsoluteQuantity).toFixed(6));
                                if ($scope.listOfRos[i].adjustedQuantity > 0) {
                                    $scope.listOfRos[i].adjustedReason = "Adjustment - Excess";
                                }
                                else if ($scope.listOfRos[i].adjustedQuantity == 0)  {
                                    $scope.listOfRos[i].adjustedReason = "No - Adjustment";
                                }
                                else {
                                    $scope.listOfRos[i].adjustedReason = "Adjustment - Short";
                                }

                                if (!$scope.isMultiPackaging) {
                                    for (var j in $scope.listOfRos[i].productPackagings) {
                                        if ($scope.listOfRos[i].productPackagings[j].packaging.packagingId == $scope.displayConversions.packagingId) {
                                            $scope.listOfRos[i].productPackagings[j].packaging.itemCheckPack = true;
                                            $scope.listOfRos[i].productPackagings[j].packaging.itemQuantity = getFinalQuantity($scope.listOfRos[i].finalQuantity);
                                        }
                                        else {
                                            $scope.listOfRos[i].productPackagings[j].packaging.itemCheckPack = false;
                                            $scope.listOfRos[i].productPackagings[j].packaging.itemQuantity = null;
                                        }
                                    }
                                    $scope.listOfRos[i].displayQuantity = getFinalQuantity($scope.listOfRos[i].finalQuantity);
                                }
                            }
                        }
                    } else if ($scope.checkBoxModal.checkAllFinalQuantity === false) {
                        for (var i = 0; i < $scope.listOfRos.length; i++) {
                            if($scope.listOfRos[i].checked) {
                                $scope.listOfRos[i].adjustedQuantity = null;
                                $scope.listOfRos[i].finalQuantity = null;
                                $scope.listOfRos[i].adjustedReason = null;
                                $scope.listOfRos[i].updateManually = false;
                                if (!$scope.isMultiPackaging) {
                                    for (var j in $scope.listOfRos[i].productPackagings) {
                                        if ($scope.listOfRos[i].productPackagings[j].packaging.packagingId == $scope.displayConversions.packagingId) {
                                            $scope.listOfRos[i].productPackagings[j].packaging.itemCheckPack = true;
                                            $scope.listOfRos[i].productPackagings[j].packaging.itemQuantity = getFinalQuantity($scope.listOfRos[i].requestedQuantity);
                                        }
                                    }
                                    $scope.listOfRos[i].displayQuantity = getFinalQuantity($scope.listOfRos[i].requestedQuantity);
                                }
                            }
                        }
                    }
                };

                $scope.changeRow = function (item) {
                    if (item.checked == false) {
                        for (var i in $scope.listOfRos){
                            if ($scope.listOfRos[i].roItemId == item.roItemId) {
                                $scope.listOfRos[i].finalQuantity = null;
                                $scope.listOfRos[i].adjustedQuantity = null;
                                $scope.listOfRos[i].adjustedReason = null;
                                $scope.listOfRos[i].updateManually = false;
                                for (var j in $scope.listOfRos[i].productPackagings) {
                                        $scope.listOfRos[i].productPackagings[j].packaging.itemCheckPack = false;
                                        $scope.listOfRos[i].productPackagings[j].packaging.itemQuantity = null;
                                }
                            }
                        }
                    }
                };

                $scope.getUnitProductPackagingMappings = function () {
                    $http({
                        method: "GET",
                        url: apiJson.urls.productManagement.defaultPackagingMappings,
                        params: {
                            unitId : appUtil.getCurrentUser().unitId
                        }
                    }).then(function success(response) {
                        if (response.data != null) {
                            $scope.unitProductPackagingMap = response.data;
                        } else {
                            $toastService.create("Something went wrong. Please try again!");
                        }
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                };

                $scope.getDefaultPackaging = function(productId){
                    console.log("Updating the conversion Ratio and Default Packaging");
                    $scope.displayConversions = {};
                    var packagingDef = appUtil.getPackagingMap();
                    $scope.productPackaging[productId].forEach(function (pack) {
                        if (pack.mappingStatus == 'ACTIVE' && pack.isDefault) {
                            $scope.displayConversions.packagingName = packagingDef[pack.packagingId].packagingName;
                            $scope.displayConversions.conversionRatio = packagingDef[pack.packagingId].conversionRatio;
                            $scope.displayConversions.packagingId = pack.packagingId;
                        }
                    });
                    if($scope.unitProductPackagingMap[productId] !=null && $scope.unitProductPackagingMap[productId]!= undefined) {
                        $scope.unitProductPackagingMap[productId].forEach(function (pack) {
                            if(appUtil.getCurrentUser().unitId == pack.fulfillmentUnitId) {
                                $scope.displayConversions.packagingName = packagingDef[pack.packagingId].packagingName;
                                $scope.displayConversions.conversionRatio = packagingDef[pack.packagingId].conversionRatio;
                                $scope.displayConversions.packagingId = pack.packagingId;
                            }
                        });
                    }
                };

                $scope.updateAdjustedPackagingQuantity = function (item,quantity) {
                    if (quantity < 0) {
                        $toastService.create("Please Enter Quantity Greater than 0...!");
                        quantity = 0;
                        return;
                    }
                    item.finalQuantity = 0;
                    for (var index in item.productPackagings) {
                        var mapping = item.productPackagings[index];
                        if (!appUtil.isEmptyObject(mapping) && !appUtil.isEmptyObject(mapping.packaging.quantity)) {
                            item.finalQuantity += (mapping.packaging.quantity * mapping.packaging.conversionRatio);
                        }
                    }
                    item.finalQuantity = parseFloat(item.finalQuantity.toFixed(6));
                };

                $scope.updateAdjustedQuantity = function(item,isMulti) {
                    if (item.finalQuantity != null && (item.finalQuantity != "" || item.finalQuantity == 0)) {
                        if (item.finalQuantity < 0) {
                            $toastService.create("Enter Quantity Greater than 0..!");
                            item.finalQuantity = null;
                            if (!isMulti) {
                                for (var i in item.productPackagings) {
                                    if (item.productPackagings[i].packaging.packagingId == $scope.displayConversions.packagingId) {
                                        item.productPackagings[i].packaging.itemCheckPack = false;
                                        item.productPackagings[i].packaging.itemQuantity = null;
                                    }
                                }
                                item.displayQuantity = getFinalQuantity(item.requestedQuantity);
                            }
                            return false;
                        }
                        else {
                            if ($scope.duplicateConversionRatio != null && !isMulti) {
                                item.adjustedQuantity = parseFloat((item.finalQuantity - item.requestedAbsoluteQuantity).toFixed(6));
                            }
                            else {
                                item.adjustedQuantity = parseFloat((item.finalQuantity - item.requestedAbsoluteQuantity).toFixed(6));
                            }

                            if (item.adjustedQuantity > 0) {
                                item.adjustedReason = "Adjustment - Excess";
                            }
                            else if (item.adjustedQuantity == 0)  {
                                item.adjustedReason = "No - Adjustment";
                            }
                            else {
                                item.adjustedReason = "Adjustment - Short";
                            }

                            if (!isMulti) {
                                for (var i in item.productPackagings) {
                                    if (item.productPackagings[i].packaging.packagingId == $scope.displayConversions.packagingId) {
                                        item.productPackagings[i].packaging.itemCheckPack = true;
                                        item.productPackagings[i].packaging.itemQuantity = getFinalQuantity(item.finalQuantity);
                                    }
                                }
                                item.displayQuantity = getFinalQuantity(item.finalQuantity);
                            }
                        }
                    }
                    else {
                        item.finalQuantity = null;
                        item.adjustedQuantity = null;
                        item.adjustedReason = null;
                        if (!isMulti) {
                            for (var i in item.productPackagings) {
                                if (item.productPackagings[i].packaging.packagingId == $scope.displayConversions.packagingId) {
                                    item.productPackagings[i].packaging.itemCheckPack = false;
                                    item.productPackagings[i].packaging.itemQuantity = getFinalQuantity(item.requestedQuantity);
                                }
                            }
                            item.displayQuantity = getFinalQuantity(item.requestedQuantity);
                        }
                    }
                };

                function getFinalQuantity(qty) {
                    return parseFloat((qty/$scope.displayConversions.conversionRatio).toFixed(6));
                }

                function createObj(item) {
                    var result = {
                        roId:item.roId,
                        roItemId:item.roItemId,
                        roProductId:item.roProductId,
                        adjustedQuantity:item.adjustedQuantity,
                        adjustedReason:item.adjustedReason,
                        unitId:item.unitId,
                        unitName:item.unitName,
                        requestedQuantity:item.requestedQuantity,
                        finalQuantity:item.finalQuantity,
                        multiPackagingAdjustments : createPackagingObj(item,item.productPackagings)
                    };
                    return result;
                }

                function createPackagingObj(item,packagings) {
                    var result = [];
                    for (var i in packagings) {
                        if ((packagings[i].packaging.itemCheckPack && packagings[i].packaging.itemQuantity != null) || (packagings[i].packaging.itemPackagingId != null)) {
                            var obj = {};
                            obj.packagingId = packagings[i].packagingId;
                            obj.itemCheckPack = packagings[i].packaging.itemCheckPack;
                            obj.itemQuantity = getItemQuantity(packagings[i]);
                            obj.itemPackagingId = packagings[i].packaging.itemPackagingId != null ? packagings[i].packaging.itemPackagingId : null
                            obj.packagingName = packagings[i].packaging.packagingName;
                            obj.packagingType = packagings[i].packaging.packagingType;
                            result.push(obj);
                        }
                    }
                    return result;
                }

                function getItemQuantity(pack) {
                    if (pack.packaging.itemPackagingId != null) {
                        if (pack.packaging.itemCheckPack) {
                            return pack.packaging.itemQuantity != null ? pack.packaging.itemQuantity : 0;
                        } else {
                            return 0;
                        }
                    }
                    else {
                        return pack.packaging.itemQuantity != null ? pack.packaging.itemQuantity : 0;
                    }
                }

                $scope.submitAdjustments = function (planId) {
                    $scope.updatedList = [];
                    var checkForItemsQuantity = true;
                    var checkForConversion = true;
                    var stringOfRatio = $scope.displayConversions.conversionRatio.toString();
                    $scope.listOfRos.forEach(function (item) {
                        if (item.checked == true) {
                            if (item.adjustedQuantity != null && item.finalQuantity != null) {
                                var obj = createObj(item);
                                $scope.updatedList.push(obj);
                            } else {
                                $toastService.create("Enter Final Quantity for Ro item Id : " + item.roItemId);
                                item.updateManually = true;
                                checkForItemsQuantity = false;
                            }
                            if (!$scope.isMultiPackaging) {
                                if ($scope.duplicateConversionRatio != null) {
                                    var stringOfFinalQuantityItem = item.finalQuantity.toString();
                                    if (stringOfFinalQuantityItem.indexOf(".") == -1) {
                                        stringOfFinalQuantityItem = stringOfFinalQuantityItem + ".000000";
                                    }
                                    var duplicateQuantityOfItem = null;
                                    console.log("conversion ratio is ", stringOfRatio, stringOfFinalQuantityItem);
                                    item.finalQuantity = parseFloat(parseFloat(stringOfFinalQuantityItem).toFixed(6));
                                    duplicateQuantityOfItem = Math.round(item.finalQuantity * 1000000);
                                    console.log("Duplicates are ratio and quantity: ", $scope.duplicateConversionRatio, duplicateQuantityOfItem);
                                    if (duplicateQuantityOfItem % $scope.duplicateConversionRatio > 0) {
                                        $toastService.create("Please enter Quantity in the multiple of " + $scope.displayConversions.conversionRatio + "for Ro item Id : " + item.roItemId);
                                        item.finalQuantity = null;
                                        item.adjustedQuantity = null;
                                        item.adjustedReason = null;
                                        checkForConversion = false;
                                        item.updateManually = false;
                                        for (var j in item.productPackagings) {
                                            if (item.productPackagings[j].packaging.packagingId == $scope.displayConversions.packagingId) {
                                                item.productPackagings[j].packaging.itemCheckPack = false;
                                                item.productPackagings[j].packaging.itemQuantity = null;
                                            }
                                        }
                                        item.displayQuantity = getFinalQuantity(item.requestedQuantity);
                                    }
                                } else {
                                    if (item.finalQuantity != null && (item.finalQuantity % $scope.displayConversions.conversionRatio > 0)) {
                                        $toastService.create("Please enter Quantity in the multiple of " + $scope.displayConversions.conversionRatio + "for Ro item Id : " + item.roItemId);
                                        checkForConversion = false;
                                        item.finalQuantity = null;
                                        item.adjustedQuantity = null;
                                        item.adjustedReason = null;
                                        item.updateManually = false;
                                        for (var j in item.productPackagings) {
                                            if (item.productPackagings[j].packaging.packagingId == $scope.displayConversions.packagingId) {
                                                item.productPackagings[j].packaging.itemCheckPack = false;
                                                item.productPackagings[j].packaging.itemQuantity = null;
                                            }
                                        }
                                        item.displayQuantity = getFinalQuantity(item.requestedQuantity);
                                    }
                                }
                            }
                        }
                    });
                    if (!checkForItemsQuantity || !checkForConversion) {
                        if (!$scope.isMultiPackaging) {
                            $toastService.create("Please enter Final Quantity to adjust Quantity for the selected items in the multiple Of " + $scope.displayConversions.conversionRatio);
                        }
                        else {
                            $toastService.create("Please enter Final Quantity to adjust Quantity for the selected items ..!");
                        }
                        return false;
                    }
                    console.log("List of final Checked ", $scope.updatedList);

                    if ($scope.updatedList.length > 0) {
                        var finalAdjustmentModal = Popeye.openModal({
                            templateUrl: "previewFinalAdjustments.html",
                            controller: "previewFinalAdjustmentsCtrl",
                            modalClass: 'custom-modal',
                            resolve: {
                                updatedList: function () {
                                    return $scope.updatedList;
                                },
                                planId: function () {
                                    return planId;
                                },
                                selectedProduct: function () {
                                    return $scope.selectedProduct;
                                }
                            },
                            click: false,
                            keyboard: false
                        });

                        finalAdjustmentModal.closed.then(function (result) {
                            if (result) {
                                $scope.previewAdjustments(angular.copy($scope.adjustmentPlanId));
                            }
                        });
                    }
                    else {
                        $toastService.create("No Items Selected to adjust Quantity");
                    }
                };

                $scope.showInstructions = function () {
                    Popeye.openModal({
                        templateUrl: "instructionsModal.html",
                        controller: "instructionsModalCtrl",
                        modalClass: 'modal-large',
                        resolve: {
                            isMulti : function () {
                                return $scope.isMultiPackaging;
                            }
                        },
                        click: false,
                        keyboard: false
                    });
                };

                $scope.findByRo = function () {
                    $scope.roItemsData = [];
                    console.log("ro items data is : ",$scope.roItemsData);
                    if ($scope.finalAdjustmentsRequestedList.length > 0) {
                        $http({
                            url: apiJson.urls.requestOrderManagement.getRoItemsByPlanId,
                            method: 'GET',
                            params: {planId: $scope.adjustmentPlanId}
                        }).then(function success(response) {
                            console.log(response);
                            if (response.data != null && response.status == 200) {
                                $scope.roItemsData = getCopyOfROItemsData(response.data);
                            }
                        }, function error(response) {
                            $toastService.create("Error in Fetching products by Ro Items.");
                            console.log("Error in Fetching products by Ro Items :: ",response);
                            $scope.roItemsData = [];
                        });
                    }
                    else {
                        $toastService.create("No Products Found to get RO Items");
                    }
                };

                function getCopyOfROItemsData(roItems) {
                   $scope.copyOfListOfRos = {};
                   $scope.productWithMultiPacks = {};
                    for (var item in roItems) {
                        var packagings = $scope.productPackagings[roItems[item].roProductId];
                        var multiPackagings = roItems[item].multiPackagingAdjustments;
                        var finalPackagings = [];
                        var check = false;
                        for (var pack in packagings) {
                            var pck = angular.copy(packagings[pack]);
                            for (var multiPack in multiPackagings) {
                                if (pck.packagingId == multiPackagings[multiPack].packagingId) {
                                    check = true;
                                    pck.packaging.itemCheckPack = multiPackagings[multiPack].itemCheckPack;
                                    pck.packaging.itemQuantity = multiPackagings[multiPack].itemQuantity;
                                    pck.packaging.itemPackagingId = multiPackagings[multiPack].itemPackagingId;
                                    break;
                                }
                            }
                            finalPackagings.push(pck);
                        }
                        $scope.productWithMultiPacks[roItems[item].roItemId] = check;
                        roItems[item].productPackagings = finalPackagings;
                        if ($scope.copyOfListOfRos[roItems[item].roProductId] != undefined) {
                            var list = $scope.copyOfListOfRos[roItems[item].roProductId];
                            list.push(roItems[item]);
                            $scope.copyOfListOfRos[roItems[item].roProductId] = list;
                        }
                        else {
                            var list = [];
                            list.push(roItems[item]);
                            $scope.copyOfListOfRos[roItems[item].roProductId] = list;
                        }
                    }
                    console.log("copy by product is : ",$scope.copyOfListOfRos);
                    console.log("Products with multiPacks are is  : ",$scope.productWithMultiPacks);
                    return roItems;
                }

                $scope.closeAdjustments = function(){
                    console.log("clicked on close");
                    $scope.isForAdjustments = false;
                    $scope.selectedProduct = null;
                    $scope.displayConversions = {};
                    $scope.adjustmentPlanId=null;
                    initCheckBoxModal();
                    $scope.listOfRos = [];
                    $scope.duplicateConversionRatio = null;
                };

                function setPackagingByProduct(productId) {
                    return angular.copy($scope.productPackagings[productId]);
                }

                $scope.previewAdjustments = function (id) {
                    $scope.selectedProduct = null;
                    $scope.displayConversions = {};
                    $scope.duplicateConversionRatio = null;
                    $scope.adjustmentPlanId=null;
                    $scope.isForAdjustments = true;
                    $scope.adjustmentPlanId=id;
                    $scope.isMultiPackaging = false;
                    $scope.copyOfListOfRos = {};
                    $scope.productWithMultiPacks = {};
                    $scope.isAutoMaticDistribution = false;
                    initCheckBoxModal();
                    var listOfItems;
                    for(var index in $scope.plans){
                        if($scope.plans[index].id==id ){
                            listOfItems=$scope.plans[index].requestItems;
                            break;
                        }
                    }
                    console.log(listOfItems);
                    $scope.finalAdjustmentsRequestedList=[];
                    for (var index in listOfItems){
                        if(listOfItems[index].itemType == "REQUESTED"){
                            listOfItems[index].productPackagings = setPackagingByProduct(listOfItems[index].productId);
                            $scope.finalAdjustmentsRequestedList.push(listOfItems[index]);
                        }
                    }
                    console.log("Final Adjustments Requested list is :",$scope.finalAdjustmentsRequestedList);
                    $scope.findByRo();
                };

                function setPackagingByProducts() {
                    metaDataService.getAllPackagingMappings(function (mappings) {
                        $scope.packagingMap = appUtil.getPackagingMap();
                        $scope.productPackagings = [];
                        for(var product in mappings){
                            try{
                                $scope.productPackagings[product] = getPackagings(mappings[product],$scope.packagingMap);
                            }catch(e) {
                                console.log(product, mappings[product]);
                            }
                        }
                    });
                }

                function getPackagings(mappingList,packagingMap) {
                    for(var key in mappingList){
                        mappingList[key].packaging = angular.copy(packagingMap[mappingList[key].packagingId]);
                    }
                    return mappingList;
                }

                $scope.previewExcessQuantity = function (id){
                    console.log($scope.plans);
                    $scope.planId=id;
                    var list;
                    for(var index in $scope.plans){
                        if($scope.plans[index].id==id ){
                            list=$scope.plans[index].requestItems;
                            break;
                        }
                    }
                    console.log(list);
                    $scope.finalRequestedList=[];
                    for (var index in list){
                        if(list[index].recipeRequire==true && list[index].itemType=="REQUESTED"){
                            $scope.finalRequestedList.push(list[index]);
                        }
                    }
                    console.log($scope.finalRequestedList);
                    if ($scope.finalRequestedList.length != 0) {
                        var previewModal = Popeye.openModal({
                            templateUrl: "previewExcessQuantity.html",
                            controller: "previewExcessQuantityCtrl",
                            modalClass: 'modal-large',
                            resolve: {
                                items: function () {
                                    return $scope.finalRequestedList;
                                },
                                planId: function () {
                                    return id;
                                }
                            },
                            click: false,
                            keyboard: false
                        });

                        previewModal.closed.then(function (result) {
                            if (result) {
                                $scope.downloadPlanorders($scope.planId);
                                $scope.getPlans();
                                console.log(result);
                            }
                        });
                    }else {
                        $toastService.create("Can't update No Semi-Finished Items");
                    }

                }
            }
        ]
    ).controller('previewExcessQuantityCtrl', ['$scope', 'items', 'planId','Popeye','$http','apiJson', 'appUtil', '$toastService',
    function ($scope, item, planId,Popeye,$http,apiJson, appUtil, $toastService) {
        $scope.initExcessQuantity = function () {
            $scope.items = item;
            $scope.planId = planId;
            $scope.skuProductMap = appUtil.getSkuProductMap();
            $scope.activeProducts = appUtil.getActiveScmProducts();
            $scope.packagingMap = appUtil.getPackagingMap();
        };

        $scope.close = function(){
            Popeye.closeCurrentModal(false);
        };

        $scope.submit = function () {
            var flag = false;
            for(var i=0; i<$scope.items.length; i++) {
                $scope.packagingDefinition = [];
                $scope.skuList = $scope.skuProductMap[$scope.items[i].productId].filter(function (sku) {
                    return sku.skuStatus == 'ACTIVE';
                });
                $scope.skuList.forEach(function (sku) {
                    sku.skuPackagings = sku.skuPackagings.filter(function (mapping) {
                        return mapping.mappingStatus == "ACTIVE";
                    });
                    sku.skuPackagings.forEach(function (packaging) {
                        if (packaging.isDefault) {
                            $scope.packagingDefinition = $scope.packagingMap[packaging.packagingId];
                        }
                    });
                    var difference = $scope.items[i].excessQuantity % $scope.packagingDefinition.conversionRatio;
                    if (difference > 0) {
                        $toastService.create("Please Enter the value having multiple of " + $scope.packagingDefinition.conversionRatio + " for packaging quantity of "+ $scope.items[i].productName);
                        flag = true;
                    }
                });
                if(flag){
                    break;
                }
            }
            if(flag){
                return;
            }else {
                $scope.excessPlanning();
            }
        };

        $scope.excessPlanning = function (){
                $http({
                    url: apiJson.urls.requestOrderManagement.uploadExcessPlanning,
                    method: 'POST',
                    data: $scope.items,
                    params: {planId:  $scope.planId}
                }).then(function success(response) {
                    //TODO
                    console.log(response);
                    if (response.data != null && response.status == 200) {
                        $toastService.create("Excess Quantities Updated Successfully");
                        Popeye.closeCurrentModal(true);
                    }
                }, function error(response) {
                    $toastService.create("Error in Updating Excess Quantities.");
                    Popeye.closeCurrentModal(true);
                });
        }

        $scope.updateData = function (id, excessQuantity) {
            if (excessQuantity != null) {
                for (var index in $scope.items) {
                    if ($scope.items[index].productId == id) {
                        $scope.items[index].excessQuantity = excessQuantity;
                    }
                }
            }
        }

    }
    ]).controller('previewFinalAdjustmentsCtrl', ['$scope', 'updatedList', 'planId','Popeye','$http','apiJson', 'appUtil', '$toastService','selectedProduct',
    function ($scope, updatedList, planId,Popeye,$http,apiJson, appUtil, $toastService,selectedProduct) {
        console.log("plan id is ",planId);
        $scope.updatedList = updatedList;
        $scope.planId = planId;
        $scope.selectedProduct = selectedProduct;

        $scope.close = function () {
            Popeye.closeCurrentModal(false);
        };

        $scope.submit = function () {
            $http({
                url: apiJson.urls.requestOrderManagement.updateAdjustedQuantities,
                method: 'POST',
                params: {
                    "updatedBy" : appUtil.getCurrentUser().user.name + " [" + appUtil.getCurrentUser().user.id + " ]"
                },
                data: $scope.updatedList
            }).then(function success(response) {
                console.log(response);
                if (response.data != null && response.status == 200) {
                    if (response.data == true ){
                        $toastService.create("Adjustments Updated Successfully..!");
                        Popeye.closeCurrentModal(true);
                    }
                    else {
                        $toastService.create("Can not update the Adjustments..Please try again...!");
                    }
                }
            }, function error(response) {
                $toastService.create("Error in updating Adjustments...!");
            });
        };
    }]).controller('instructionsModalCtrl', ['$scope', 'Popeye','isMulti',
    function ($scope, Popeye, isMulti) {
        $scope.isMulti = isMulti;
        $scope.close = function () {
            Popeye.closeCurrentModal();
        };
    }])
    .filter('formatuom', [function () {
    return function (amount, uom) {
        if (isNaN(amount)) {
            return "0 " + uom;
        } else {
            var ret;
            if (amount < 1) {
                if (uom == "L") {
                    ret = amount * 1000;
                    ret = parseFloat(ret).toFixed(3) + " ML";
                } else if (uom == "KG") {
                    ret = amount * 1000;
                    ret = parseFloat(ret).toFixed(3) + " GM";
                } else {
                	ret = parseFloat(amount).toFixed(3) + " " + uom;
                }
            } else {
                ret = parseFloat(amount).toFixed(3) + " " + uom;
            }
            return ret;
        }
    }
}]);