/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by shikhar on 25-04-2016.
 */
'use strict';

angular.module('scmApp')
    .controller('assetListCtrl', ['$rootScope', '$scope', 'authService', '$location', '$state', 'apiJson',
            'appUtil', '$http', '$stateParams', 'productService', '$alertService', 'Popeye', '$fileUploadService', '$toastService', 'metaDataService', 'PrintService',
            '$timeout',
            function ($rootScope, $scope, authService, $location, $state, apiJson, appUtil, $http, $stateParams,
                      productService, $alertService, Popeye, $fileUploadService, $toastService, metaDataService, PrintService, $timeout) {

                $scope.init = function () {
                    $rootScope.assetPrintStatus = {};
                    productService.getAllProducts(function (products) {
                        $scope.products = products;
                    });
                    $scope.currentUserId = appUtil.getCurrentUser().userId;
                    $scope.isWarehouse = appUtil.isWarehouse();
                    $scope.assetId = null;
                    $scope.assets = [];
                    $scope.assetDetail = {};
                    $scope.currentUnit = appUtil.getUnitData();
                    $scope.bulkPrint = false;
                    $scope.filteredAssets = $scope.assets;
                    $scope.subCategories = ['ALL','Kitchen equipment',
                        'Office Equipment ',
                        'IT',
                        'Furniture',
                        'Vehicles',
                    'Air Conditioner',
                    'Equipment',
                    'Cutlery'];
                    $scope.model = {selectedSubCategory : 'ALL',
                        selectedAssetStatuses : [],
                        selectedUnitId : null
                    };
                    $scope.assetStatuses = [
                        'INITIATED',
                        'CREATED',
                        'IN_USE',
                        'DISCARDED',
                        'SCRAPPED',
                        'LOST_ADJUSTED',
                        'IN_REPAIR',
                        'READY_FOR_USE',
                        'IN_RENOVATION',
                        'BROKEN',
                        'LOST_IDENTIFIED',
                        'PENDING_RETURN',
                        'SETTLED',
                        'PENDING_LOST',
                        'DUPLICATE'
                    ];
                    $scope.allUnitsList =  appUtil.getUnitList();
                    $scope.batchSize = 70;
                    $rootScope.startBatchIndex = 0;
                    $rootScope.endBatchIndex = 69;
                    // getAllAssets();
                };

                $scope.findAssets = function () {
                    $scope.assetName = null;
                    getAllAssets();
                }

                $scope.searchAssetByName = function (name) {
                    $http({
                        method: 'GET',
                        url: apiJson.urls.assetManagement.getAssetNameSlimWithUnit,
                        params: {
                            unitId: $scope.currentUnit.id,
                            assetName: name
                        }
                    }).then(function success(response) {
                        $scope.assets = response.data;
                    }, function error(response) {
                        console.log("Encountered an error", response);
                    });
                }

                function getUserUnits() {
                    $http({
                        method: 'POST',
                        url: apiJson.urls.unitManagement.userUnits,
                        data: {
                            employeeId: appUtil.createGeneratedBy().id,
                            onlyActive: true
                        }
                    }).then(function success(response) {
                        $scope.userUnitArray = response.data;

                    }, function error(response) {
                        console.log("Encountered an error", response);
                    });

                }

                $scope.updateAssetName = function (assetId, newAssetName) {
                    $http({
                        method: 'POST',
                        url: apiJson.urls.assetManagement.updateAssetName,
                        params: {
                            assetId: assetId,
                            newName: newAssetName
                        }
                    }).then(function success(response) {
                        if (response.data) {
                            $toastService.create("Asset Name Updated Successfully!");
                        } else {
                            $toastService.create("Error Occurred While Updating Asset Name! Please try Again!");
                        }
                    }, function error(response) {
                        console.log("Encountered an error", response);
                        $toastService.create("Error Occurred While Updating Asset Name");
                    });
                }

                $scope.markNotFoundInAudit = function (asset) {
                    $alertService.confirm("Are you sure?","Do you want to mark this asset as NOT FOUND",function(result) {
                        if (result) {
                            $http({
                                method: 'POST',
                                url: apiJson.urls.assetManagement.markNotFoundInAudit,
                                params: {
                                    assetTag: asset.tagValue,
                                    updatedBy: $scope.currentUserId
                                }
                            }).then(function success(response) {
                                if (response.data) {
                                    $toastService.create("Asset Status Updated Successfully ..!");
                                    asset.assetStatus = 'PENDING_LOST';
                                } else {
                                    $toastService.create("Error Occurred While Updating Asset Status ..! Please try Again..!");
                                }
                            }, function error(response) {
                                console.log("Encountered an error", response);
                                $toastService.create("Error Occurred While Marking asset as NOT FOUND IN AUDIT ");
                            });
                        }
                    });
                };

                function getAllAssets(unitId) {
                    var id = unitId == null ? $scope.currentUnit.id : unitId;
                    $http({
                        method: 'GET',
                        url: apiJson.urls.assetManagement.getAssetSlimWithUnit,
                        params: {
                            unitId : id,
                        }
                    }).then(function success(response) {
                        $scope.assets = response.data;
                        $scope.filteredAssets = $scope.assets
                        getUserUnits();
                    }, function error(response) {
                        console.log("Encountered an error",response);
                    });

                }

                $scope.employeeAssetFilter = function(asset){

                    for(var i in $scope.userUnitArray){
                        if(asset.ownerId == $scope.userUnitArray[i].id){
                            return true;
                        }
                    }
                    return false;
                }

                function getAssetDetail(assetId, assets) {

                    for (var index in assets) {
                        if (assets[index].assetId == assetId) {
                            return assets[index];
                        }
                    }
                    //return null;
                }

                $scope.selectAsset = function (assetId) {

                    if (assetId != null && assetId != "") {
                        $scope.assetId = assetId;
                        $scope.assetDetail = getAssetDetail(assetId, $scope.assets);
                    }
                };

                $scope.printAllAssets = function () {
                    $scope.bulkPrint = true;
                    $scope.filteredAssets = $scope.assets;
                    $scope.model.selectedUnitId = $scope.currentUnit.id;

                }

                $scope.changeUnit = function (selectedUnit){
                    $scope.model.selectedUnitId = selectedUnit;
                    getAllAssets(selectedUnit);
                }


                $scope.openBulkTagPrintModal = function () {
                    var bulkTagPrintModal = Popeye.openModal({
                        templateUrl: "bulkTagPrintModal.html",
                        controller: "bulkTagPrintCtrl",
                        resolve: {
                            assets: function () {
                                return angular.copy($scope.assets);
                            }
                        },
                        modalClass: 'custom-modal',
                        click: false,
                        keyboard: false
                    });

                    bulkTagPrintModal.closed.then(function (data) {

                    });
                }

                $scope.printTags = function (assets) {
                    $scope.printSuccessMap = {};
                    var assetIds = [];
                    for (var i = 0; i < assets.length; i++) {
                        assetIds.push(assets[i].assetId);
                    }
                    console.log(appUtil.createGeneratedBy());
                    $http({
                        method: 'POST',
                        url: apiJson.urls.assetManagement.bulkPrintAssetTag,
                        params: {
                            printedBy: appUtil.createGeneratedBy().id
                        },
                        data: assetIds
                    }).then(function success(response) {
                        print(assets);
                    }, function error(response) {
                        console.log("Encountered an error", response);
                        alert('Error Printing asset tag , contact support');
                    }).catch(function (e) {
                        console.log("e :::::", e);
                    });

                }

                function print(assets) {
                    $rootScope.tagsBulkList = [];
                    $rootScope.assetPrintStatus = {};
                    console.log("Starting To Print " + assets.length + " Asset Tags ");

                    for (var i = 0; i < assets.length; i++) {
                        try {
                            var asset = assets[i];
                            asset.lastTagPrintedBy = appUtil.createGeneratedBy();
                            asset.tagPrintCount = asset.tagPrintCount + 1;
                            var code = {}
                            code.assetName = asset.assetName;
                            for (var j in $scope.products) {
                                if ($scope.products[j].productId == asset.productId) {
                                    code.subCategoryCode = $scope.products[j].subCategoryDefinition.code;
                                }
                            }
                            code.assetTagValue = asset.tagValue
                            code.batchIndex = i;
                            var tempCode = angular.copy(code);
                            $rootScope.assetPrintStatus[code.assetTagValue] = "PENDING";
                            $rootScope.tagsBulkList.push(tempCode);
                            //PrintService.printBarCode(tempCode);

                        } catch (e1) {
                            console.log("error e1 ::", e1);
                        }

                    }
                    PrintService.printTagsInbatch();

                }


                $scope.printAgain = function () {
                    $alertService.confirm("Are you sure?", "", function (result) {
                        console.log(appUtil.createGeneratedBy());
                        if (result) {
                            $http({
                                method: 'POST',
                                url: apiJson.urls.assetManagement.assetPrintTag,
                                params: {
                                    assetId: $scope.assetDetail.assetId,
                                    printedBy: appUtil.createGeneratedBy().id
                                }
                            }).then(function success(response) {
                                $scope.assetDetail.lastTagPrintedBy = appUtil.createGeneratedBy();
                                $scope.assetDetail.tagPrintCount = $scope.assetDetail.tagPrintCount + 1;
                                var code = {}
                                code.assetName = $scope.assetDetail.assetName;
                                for (var i in $scope.products) {
                                    if ($scope.products[i].productId == $scope.assetDetail.productId) {
                                        code.subCategoryCode = $scope.products[i].subCategoryDefinition.code;
                                    }
                                }
                                code.assetTagValue = $scope.assetDetail.tagValue
//                                code.assetTagApprovedBy =
                                code.validTill = appUtil.formatDate(appUtil.getDate(6), "dd-MMM-yyyy");
                                code.printedByName = appUtil.getCurrentUser().user.name;
                                code.printedById = appUtil.getCurrentUser().user.id;
                                if($scope.isWarehouse){
                                    PrintService.printBarCode(code);
                                }else{
                                    PrintService.printBarCodeBill(code);
                                }


                            },  function error(response) {
                                console.log("Encountered an error", response);
                                if (response.data.errorMessage != null) {
                                    $alertService.alert("Cannot Print Tag", response.data.errorMessage, null, true);
                                } else {
                                    alert('Error Printing asset tag , contact support');
                                }
                            });
                        }
                    });
                }

                $scope.changeStatus = function (status) {
                    $alertService.confirm("Are you sure?", "", function (result) {
                        if (result) {
                            $http({
                                method: 'GET',
                                url: apiJson.urls.assetManagement.alterAssetStatus,
                                params: {
                                    assetId: $scope.assetDetail.assetId,
                                    assetStatus: status
                                }
                            }).then(function success(response) {
                                $scope.assetDetail.assetStatus = status;
                            }, function error(response) {
                                console.log("Encountered an error", response);
                                alert('Error changing asset status , contact support')
                            });
                        }
                    });

                }





                $scope.printFilteredAssetTags = function (){
                    var selectedAssets = [];
                    for(var l = 0;l<$scope.filteredAssets.length;l++){
                        if($scope.filteredAssets[l].checked == true){
                            selectedAssets.push($scope.filteredAssets[l]);
                        }
                    }
                    $alertService.confirm("Are you sure?", selectedAssets.length + " Asset Tags Will Be Printed  , Please Make Sure You Have Enough Paper In Printer ",
                        function (result) {
                            if (result) {
                                $scope.printTags(selectedAssets);
                            }
                        });
                }

                $scope.back = function (){
                    getAllAssets();
                    $scope.bulkPrint = false;
                }

                $scope.applyFilter = function (){
                    if($scope.model.selectedSubCategory == null ||   $scope.model.selectedSubCategory == "ALL"){
                        $scope.filteredAssets = $scope.assets;
                    }else{
                        $scope.filteredAssets = $scope.assets.filter(function (asset){
                            return asset.subCategoryName == $scope.model.selectedSubCategory;
                        });
                    }

                    if($scope.model.selectedAssetStatuses.length > 0){
                        $scope.filteredAssets = $scope.filteredAssets.filter(function (asset){
                            return  $scope.model.selectedAssetStatuses.indexOf(asset.assetStatus) != -1;
                        });
                    }



                    $scope.selectAll(true);
                }

                $scope.selectAll = function (status){
                    var currentStatus = false;
                    if(status == null){
                        currentStatus = $scope.filteredAssets[0].checked == null ? false : $scope.filteredAssets[0].checked;
                    }else{
                        currentStatus = status
                    }

                    for(var i = 0;i<$scope.filteredAssets.length;i++){
                        $scope.filteredAssets[i].checked = !currentStatus;
                    }

                }

            }


        ]
    );

