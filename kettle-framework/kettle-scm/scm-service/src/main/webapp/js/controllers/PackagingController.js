/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by shikhar on 21-05-2016.
 *
 * "packagingType",
 * "packagingCode",
 * "packagingName",
 * "packagingStatus",
 * "conversionRatio",
 * "unitOfMeasure"
 */

'use strict';

angular.module('scmApp')
    .controller('packagingCtrl', ['$rootScope', 'authService', '$state', '$scope', '$http', 'apiJson', 'appUtil',
        '$toastService','packagingService',
        function ($rootScope, authService, $state, $scope, $http, apiJson, appUtil, $toastService,packagingService) {

            $scope.packagingTypes = ['CASE', 'INNER', 'LOOSE'];
            $scope.selectOptions = {width:'100%'};
            $scope.packagingId = 0;
            $scope.definitions = {};
            $scope.listing = [];
            $scope.countValue = 0;
            $scope.profiles = [];
            $scope.selectedProfile = {};
            $scope.checkEmpty = appUtil.isEmptyObject;


            function getAllProfiles(){
                 packagingService.getAllProfiles(function(definitions){
                    $scope.definitions = definitions;
                    $scope.selectedProfile = {};
                    $scope.changeType($scope.packagingType);
                });
            }

            $scope.init = function () {
                $scope.packagingType = $scope.packagingTypes[0];
                getAllProfiles();
            };

            $scope.changeType = function(type){
                $scope.packagingType = type;
                $scope.listing = $scope.definitions[type];
                $scope.selectedProfile = {};
            };

            $scope.changeStatus = function (packagingType) {
                $scope.packagingType = packagingType;
                $scope.getProfile($scope.packagingType, $scope.packagingId);
            };

            $scope.getProfile = function (packagingType, packagingId) {
                $scope.selectedProfile = {};
                var profiles = $scope.definitions[packagingType];
                if(!appUtil.isEmptyObject(profiles)){
                    profiles.forEach(function(profile){
                        if(profile.packagingId == packagingId){
                            $scope.selectedProfile = profile;
                        }
                    });
                }
            };

            $scope.getCount = function (count) {
                return new Array(count);
            };

            $scope.addToCount = function () {
                $scope.countValue += 1;
            };

            $scope.reset = function () {
                $scope.countValue = 0;
            };

            $scope.submit = function () {

                if (!appUtil.checkEmpty($scope.profiles) && !appUtil.checkEmpty($scope.packagingType)) {

                    $scope.profiles.forEach(function (profile) {
                        profile.packagingStatus = "ACTIVE";
                        profile.packagingType = $scope.packagingType;
                    });
                    $http({
                        method: 'POST',
                        url: apiJson.urls.productManagement.packaging,
                        data: $scope.profiles
                    }).then(function (response) {
                        console.log(response.data);
                        if(response.data){
                            $toastService.create("Added Profiles successfully",function(){
                                getAllProfiles();
                                $scope.countValue = 0;
                                $scope.profiles = {};
                            });
                        }
                    }, function (response) {
                        console.log(response);
                    });
                }else{
                    $toastService.create("Please select a mapping type first!");
                }
            };
        }
    ]
);