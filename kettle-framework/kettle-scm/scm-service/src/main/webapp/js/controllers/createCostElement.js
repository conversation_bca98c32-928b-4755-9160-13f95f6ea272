angular.module('scmApp').controller(
		'createCostElement', ['$rootScope', '$scope', 'apiJson', '$http','$state', 'appUtil', '$location', '$toastService', 'metaDataService',
	        '$fileUploadService', '$alertService', 'previewModalService', 'Popeye', '$timeout', '$window',
	        function ($rootScope, $scope, apiJson, $http, $state, appUtil, $location, $toastService, metaDataService, $fileUploadService, $alertService,
	                  previewModalService, Popeye, $timeout, $window) {
					
					$scope.init = function () {
						$scope.capexs = ["Yes","No"];
						$scope.periods = ["ACTIVE","IN_ACTIVE"];
                        $scope.loginType =  $rootScope.loginType=="sumo"?"GOODS":"SERVICE";
						//$scope.taxCodes = appUtil.getTaxProfiles();
						$scope.getListData();
						var map = null;
						$scope.categories = [];
						$scope.departments = [];
						$scope.divisions = [];
						$scope.subCategories = [];
						$scope.subSubCategories = [];
						$scope.getTaxProfiles();
						$scope.getCategoryTaxProfiles();
						metaDataService.getUOMDefinitions(function (uoms) {
							$scope.uomMetadata = uoms;
						});
					};
					
					$scope.getListData = function (){
						$http({
							method : 'GET',
							url : apiJson.urls.serviceOrderManagement.getListData+"?baseType="+$scope.loginType,
						}).then(function success(response) {
							if(response.data != null){
								map = response.data;
								$scope.departments = map["Department"];
								$scope.divisions = map["Division"];
								$scope.categories = map["Classification"];
							}
						}, function error(response) {
				               console.log("error:" + response);
				          })
					}
					
						$scope.getCategoryTaxProfiles = function (){
				            $http({
				                method: 'GET',
				                url: apiJson.urls.unitMetadata.getAllCategoriesTax
				            }).then(function success(response) {
				            	$scope.taxPercent  = response.data;
				            }, function error(response) {
				                console.log("error:" + response);
				            });
						}
						
						 $scope.getTaxProfiles = function (){
						        $http({
						            method: "GET",
						            url: apiJson.urls.unitMetadata.getAllTaxCategories
						        }).then(function success(response) {
						            var taxes = response.data;
						            var filteredTaxes = [];
								for(var i in taxes){
								    if(taxes[i].status == 'ACTIVE'){
									    filteredTaxes.push(taxes[i]);
								    }
								}
								$scope.taxCodes = filteredTaxes;
						        }, function error(response) {
						            console.log("error:" + response);
						        });
						    }
					
					$scope.selectCategory = function(selectedCategory){
						for(var i = 0 ; i < $scope.categories.length ; i++){
							if($scope.categories[i].listDetailId == selectedCategory.listDetailId){
								$scope.subCategories = $scope.categories[i].listType;
							}
						}
					}
					
					$scope.selectSubCategory = function(selectedSubCategory){
						for(var i = 0 ; i < $scope.subCategories.length ; i++){
							if($scope.subCategories[i].listTypeId == selectedSubCategory.listTypeId){
								$scope.subSubCategories = $scope.subCategories[i].listData;
							}
						}
					}
					
					$scope.getTaxPercent = function(){
						for(var x = 0 ; x < $scope.taxPercent.length ; x++){
							if($scope.taxPercent[x].category.code == $scope.taxCategory.code){
								return $scope.taxPercent[x].taxes[0].igst;
							}
						}
					}
					
					$scope.createCostElement = function() {
						if($scope.elementName=="" || $scope.elementName==null){
				  			alert("Please input Cost Element Name.");
				  			return;
				  		}
				      	if($scope.taxCategory.code=="" || $scope.taxCategory.code==null){
				  			alert("Please input SAC Code.");
				  			return;
				  		}
				    	if($scope.selectedStatus=="" || $scope.selectedStatus==null){
				 			alert("Please input Status.");
				 			return;
				 		}
				    	if($scope.selectedCategory=="" || $scope.selectedCategory==null){
				 			alert("Please input Category.");
				 			return;
				 		}
				    	if($scope.selectedDepartment=="" || $scope.selectedDepartment==null){
				 			alert("Please input Department.");
				 			return;
				 		}
				    	if($scope.selectedCapex=="" || $scope.selectedCapex==null){
				 			alert("Please input Capex.");
				 			return;
				 		}
				    	if($scope.uom=="" || $scope.uom==null){
				 			alert("Please input Unit Of Measure.");
				 			return;
				 		}
				    	if($scope.description=="" || $scope.description==null){
				 			alert("Please input Description.");
				 			return;
				 		}
				    	if($scope.selectedIsPrice=="" || $scope.selectedIsPrice==null){
				 			alert("Please input IsPrice Editable.");
				 			return;
				 		}
				    	var taxPercent = $scope.getTaxPercent();
						var reqObj = {
								name: $scope.elementName,
								 status: $scope.selectedStatus,
								 ascCode: $scope.taxCategory.code,
								 taxRate: taxPercent,
								  category: $scope.selectedCategory,
								  department: $scope.selectedDepartment,
								  division: $scope.selectedDivision,
								 subCategory: $scope.selectedSubCategory,
								 subSubCategory: $scope.selectedSubSubCategory,
								 capex: $scope.selectedCapex,
								 uom: $scope.uom,
								 description: $scope.description,
								 isPriceUpdate: $scope.selectedIsPrice
								
						}
	                	 $http({
	                         url: apiJson.urls.serviceOrderManagement.createCostElement,
	                         method: 'POST',
	                         data: reqObj
	                     }).then(function (response) {
	                         $toastService.create("Cost Element Added!");
	                         $state.go('menu.searchCostElement');
	                        // $state.go('menu.searchCostElement');
	                     }, function (response) {
	                         console.log("got error", response);
	                     });
	                };
					
	                
				} ]);