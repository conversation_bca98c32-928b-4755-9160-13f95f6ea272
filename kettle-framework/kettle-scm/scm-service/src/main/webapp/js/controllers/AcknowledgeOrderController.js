/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by shikhar on 29-06-2016.
 */
angular.module('scmApp')
    .controller('acknowledgeOrderCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil','$toastService','metaDataService',
        function ($rootScope, $scope, apiJson, $http, appUtil, $toastService,metaDataService) {

            $scope.requestOrders = [];
            $scope.aggregatedItems = [];
            $scope.sentRequest = false;
            $scope.init = function () {
                $scope.selectedDate = appUtil.formatDate(appUtil.getDate(0), "yyyy-MM-dd");
                $scope.minDate = appUtil.formatDate(appUtil.getDate(-30), "yyyy-MM-dd");
                $scope.maxDate = appUtil.formatDate(appUtil.getDate(7), "yyyy-MM-dd");
                $scope.getOrders();
            };

            $scope.reset = function(){
                $scope.requestOrders = [];
                $scope.aggregatedItems = [];
                $scope.sentRequest = false;
            };

            function setPackagingByProducts() {
                metaDataService.getAllPackagingMappings(function (mappings) {
                    $scope.packagingMap = appUtil.getPackagingMap();
                    $scope.productPackagingMappings = [];
                    for(var product in mappings){
                        var defaultProductMapping = mappings[product].filter(function(mapping){
                            return mapping.isDefault;
                        })[0];
                        try{
                            $scope.productPackagingMappings[product] = $scope.packagingMap[defaultProductMapping.packagingId];
                        }catch(e) {
                            console.log(product, mappings[product]);
                        }
                    }
                });
            }

            $scope.getOrders = function(){
                if($scope.selectedDate!=null
                    && appUtil.getCurrentUser()!=null
                    && appUtil.getCurrentUser().unitId!=null){
                    $http({
                        url: apiJson.urls.requestOrderManagement.pendingOrdersByFulfillmentDate,
                        method: "GET",
                        params: {date:$scope.selectedDate, fulfillmentUnit: appUtil.getCurrentUser().unitId}
                    }).then(function(response){
                        //console.log(response);
                        if(!appUtil.checkEmpty(response)){
                            var allOrders = response.data;
                            $scope.requestOrders = allOrders.filter(function(order){
                                return order.status == "CREATED";
                            }).sort(sortByUnit);

                            $scope.acknowledgedOrders = allOrders.filter(function(order){
                                return order.status == "ACKNOWLEDGED";
                            }).sort(sortByUnit);

                            if($scope.acknowledgedOrders.length>0){
                                setPackagingByProducts();
                            }
                        }
                        $scope.sentRequest = true;
                    },function(response){
                        console.log(response);
                    });
                }else{
                    if($scope.selectedDate==null){
                        $toastService.create("Select a date  first");
                    }
                    if(appUtil.getCurrentUser()==null || appUtil.getCurrentUser().unitId==null){
                        console.log("Logged In user does not have a valid unitId to get orders");
                    }
                }
            };

            function sortByUnit(a,b){
            	if(a.requestUnit.name < b.requestUnit.name) return -1;
                if(a.requestUnit.name > b.requestUnit.name) return 1;
                return 0;
            }


            $scope.acknowledgeAll = function(){
              $scope.requestOrders.forEach(function(order){
                  order.checked = true;
              });
            };

            $scope.downloadRequestOrder = function(requestOrder){
                console.log("download a request order ::::",requestOrder);
                $http({
                    url: apiJson.urls.requestOrderManagement.downloadOrder,
                    method: 'POST',
                    responseType: 'arraybuffer',
                    data: requestOrder,
                    headers: {
                        'Content-type': 'application/json',
                        'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    }
                }).success(function(data){
                    var fulfillmentDate = appUtil.getFormattedDate(requestOrder.fulfillmentDate);
                    var fileName = "Request Order Sheet_" + requestOrder.requestUnit.name
                                    + "_" + fulfillmentDate + "_" + appUtil.formatDate(Date.now(),"dd-MM-yyyy-hh-mm-ss") + ".xls";
                    var blob = new Blob([data], {
                        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                    },fileName);
                    saveAs(blob, fileName);
                }).error(function(err){
                    console.log("Error during getting data",err);
                });
            };

            $scope.isChecked = function(requestOrders){
                return requestOrders.filter(function(order){
                    return order.checked;
                }).length > 0;
            };

            $scope.submit = function(requestOrders){
                if($scope.selectedDate!=null){
                    var aggregatedItems = [];
                    var requestOrdersItems = [];
                    $scope.aggregatedItems = [];
                    var selectedRequestOrders = requestOrders.filter(function(order){
                        return order.checked;
                    });
                    selectedRequestOrders.forEach(function(order){
                        requestOrdersItems = requestOrdersItems.concat(order.requestOrderItems);
                    });
                    var productMap = [];
                    requestOrdersItems.forEach(function(orderItem){
                        var item = productMap[orderItem.productId];
                        if(appUtil.checkEmpty(item)){
                            productMap[orderItem.productId] = {
                                id:orderItem.productId,
                                name: orderItem.productName,
                                quantity: 0,
                                uom:orderItem.unitOfMeasure
                            };
                        }
                        productMap[orderItem.productId].quantity += orderItem.requestedAbsoluteQuantity;
                    });

                    productMap.forEach(function(product){
                        $scope.aggregatedItems.push(product);
                    });

                    console.log($scope.aggregatedItems);

                    if(selectedRequestOrders.length>0){
                        $http({
                            url: apiJson.urls.requestOrderManagement.acknowledgeOrders,
                            method: 'POST',
                            responseType: 'arraybuffer',
                            data: {
                                fulfillmentDate:$scope.selectedDate,
                                lastUpdatedBy: appUtil.createGeneratedBy(),
                                requestOrders: selectedRequestOrders
                            },
                            headers: {
                                'Content-type': 'application/json',
                                'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                            }
                        }).success(function(data){
                            var fileName = "Request Order Sheet-" + appUtil.formatDate(new Date($scope.selectedDate),"dd-MM-yyyy") + "-" + appUtil.formatDate(Date.now(),"dd-MM-yyyy-hh-mm-ss") + ".xls";
                            var blob = new Blob([data], {
                                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                            },fileName);
                            saveAs(blob, fileName);
                            $scope.init();
                        }).error(function(err){
                            console.log("Error during getting data",err);
                        });
                    }else{
                        $toastService.create("Please acknowledge at least one request order before submitting");
                    }
                }else{
                    $toastService.create("Please select a date first");
                }
            };
        }
    ]
);