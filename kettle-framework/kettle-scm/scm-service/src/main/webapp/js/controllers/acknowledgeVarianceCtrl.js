'use strict';

angular.module('scmApp')
    .controller('acknowledgeVarianceCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$toastService', 'metaDataService', 'Popeye', '$timeout',
        function ($rootScope, $scope, apiJson, $http, appUtil, $toastService, metaDataService, Popeye, $timeout) {


            $scope.init = function () {
                $scope.acknowledgeVarianceTypes = ["DAILY", "WEEKLY"];
                $scope.isAcknowledgedTypes = ["YES", "NO"];
                $scope.acknowledgeVarianceList = [];
                $scope.varianceDetails = {};
                $scope.varianceDetailsFilter = {};
                $scope.selectedVarianceType = "DAILY";
                $scope.selectedUnit = null;
                $scope.selectedUnitId = null;
                $scope.isAcknowledged = "NO";
                $scope.getAccessibleUnits();

            };

            $scope.getAccessibleUnits = function () {
                if ($scope.selectedVarianceType == null || $scope.selectedVarianceType == undefined) {
                    $toastService.create("Please Select acknowledgement type !!");
                    return;
                }
                $http({
                    method: "GET",
                    url: apiJson.urls.stockManagement.getUnitsForVarianceAcknowledge,
                    params: {
                        userId: appUtil.getCurrentUser().userId,
                        acknowledgementType: $scope.selectedVarianceType
                    }
                }).then(function success(response) {
                    if (response.data != null ) {
                        $scope.scmUnitList = [];
                        appUtil.getUnitList().map(function (unit) {
                            var i = 0;
                            for (i = 0; i < response.data.length; i++) {
                                if (unit.id == response.data[i]) {
                                    $scope.scmUnitList.push(unit);
                                }
                            }
                        });
                    } else {
                        $toastService.create("No units accessibility for variance acknowledgement ");
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });

            };

            $scope.selectType = function (varianceType) {
                $scope.selectedUnitId = null;
                $scope.selectedUnit = null;
                $scope.scmUnitList = [];
                $timeout(function () {
                    $('#selectedUnit').val(null).trigger('change');
                    $scope.getAccessibleUnits();
                });
                $scope.selectedVarianceType = varianceType;
            };

            $scope.selectUnit = function (selectedUnit) {
                $scope.selectedUnit = selectedUnit;
                $scope.selectedUnitId = JSON.parse($scope.selectedUnit).id;
            };

            $scope.selectIsAcknowledged = function (val) {
                $scope.isAcknowledged = val;
            };

            $scope.getAcknowledgeVarianceList = function () {
                if (appUtil.isEmptyObject($scope.selectedVarianceType)) {
                    $toastService.create("Please Select Acknowledgement type !");
                    return;
                }
                var data = {
                    "unitId": $scope.selectedUnitId,
                    "acknowledgementType": $scope.selectedVarianceType,
                    "isAcknowledged": $scope.isAcknowledged,
                    "userId": appUtil.getCurrentUser().userId
                };
                $http({
                    method: "GET",
                    url: apiJson.urls.stockManagement.getVarianceAcknowledgeList,
                    params: data
                }).then(function success(response) {
                    if (response.data != null || response.data != undefined) {
                        $scope.acknowledgeVarianceList = response.data;
                    }
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            $scope.acknowledgeVariance = function (id, unitId, businessDate, varianceType, dayCloseEventId, acknowledgementType, type,comment) {
                $http({
                    method: "GET",
                    url: apiJson.urls.stockManagement.getVarianceDetails,
                    params: {
                        "unitId": unitId,
                        "businessDate": appUtil.formatDate(businessDate, "yyyy-MM-dd"),
                        "varianceType": varianceType,
                        "scmDayCloseEventId": dayCloseEventId,
                        "acknowledgementType": acknowledgementType
                    }
                }).then(function success(response) {
                    if (response.data) {
                        $scope.varianceDetails = response.data;
                        if (!appUtil.isEmptyObject($scope.varianceDetails)) {
                            Object.keys($scope.varianceDetails);
                            $scope.varianceDetailsFilter = {};
                            Object.keys($scope.varianceDetails).map(function (val) {
                                $scope.varianceDetailsFilter[val] = [];
                                $scope.varianceDetails[val].map(function (val1) {
                                    if (val1.variance != 0) {
                                        $scope.varianceDetailsFilter[val].push(val1);
                                    }
                                })
                            })
                            $scope.openVarianceModal(id, unitId, businessDate, type , comment);
                        }

                    }
                }, function error(response) {
                    console.log("error:" + response);
                });

            };

            $scope.openVarianceModal = function (id, unitId, businessDate, type , comment) {
                var varianceDetailsModal = Popeye.openModal({
                    templateUrl: "varianceDetailsModal.html",
                    controller: "varianceDetailsModalCtrl",
                    resolve: {
                        id: function () {
                            return id;
                        },
                        unitId: function () {
                            return unitId
                        },
                        businessDate: function () {
                            return businessDate
                        },
                        varianceDetails: function () {
                            return $scope.varianceDetailsFilter
                        },
                        type: function () {
                            return type
                        },
                        comment: function () {
                            return comment
                        }
                    },
                    modalClass: 'custom-modal',
                    click: false,
                    keyboard: false
                });
                varianceDetailsModal.closed.then(function (result) {
                    if (result) {
                        $scope.getAcknowledgeVarianceList();
                    }
                });
            }
        }

    ]).controller('varianceDetailsModalCtrl', ['$scope', 'appUtil', '$toastService', 'apiJson', '$http', 'Popeye', '$window', 'id', 'unitId', 'businessDate', 'varianceDetails', 'type','comment','$alertService',
        function ($scope, appUtil, $toastService, apiJson, $http, Popeye, $window, id, unitId, businessDate, varianceDetails, type , comment,$alertService) {

            $scope.init = function () {
                $scope.id = JSON.parse(id);
                $scope.unitId = JSON.parse(unitId);
                $scope.unitName = null;
                appUtil.getUnitList().map(function (unit) {
                    if (unit.id == $scope.unitId) {
                        $scope.unitName = unit.name;
                    }
                });
                $scope.selectedDay = null;
                $scope.selectedVarianceDetails = null;
                $scope.businessDate = JSON.parse(businessDate);
                $scope.varianceDetails = varianceDetails;
                $scope.days = Object.keys($scope.varianceDetails);
                $scope.lastDay = null;
                $scope.selectedDay = null;
                if (!appUtil.isEmptyObject($scope.days)) {
                    $scope.selectedDay = $scope.days[0];
                    $scope.selectedVarianceDetails = $scope.varianceDetails[$scope.selectedDay];
                    $scope.lastDay = $scope.days[$scope.days.length - 1];
                }
                $scope.comment = comment;
                $scope.type = type;
            }

            $scope.setSelectedDay = function (day) {
                $scope.selectedDay = day;
                $scope.selectedVarianceDetails = $scope.varianceDetails[day];
            }

            $scope.nextDate = function () {
                var nextIndex = null;
                $scope.days.map(function (day,index){
                    if(day == $scope.selectedDay && nextIndex==null){
                        $scope.selectedDay = $scope.days[index+1];
                        $scope.selectedVarianceDetails = $scope.varianceDetails[$scope.selectedDay];
                        nextIndex = index +1;
                    }
                })
            }

            $scope.setComment = function (comment) {
                $scope.comment = comment;
            }

            $scope.acknowledgeVarianceDetails = function () {
                $http({
                    method: "POST",
                    url: apiJson.urls.stockManagement.acknowledgeVariance,
                    params: {
                        "id": $scope.id,
                        "userId": appUtil.getCurrentUser().userId,
                        "comment": $scope.comment==null ? " " : $scope.comment
                    }
                }).then(function success(response) {
                    if (response.data) {
                        $scope.closeModal(true);
                        $toastService.create("Variance acknowledged for unit " + unitId + " businessDate " + appUtil.convertToDateWithoutTime(businessDate));
                    }
                }, function error(response) {
                    console.log(response.data)
                        if (response.data.errorCode != null) {
                            $alertService.alert(response.data.errorTitle, response.data.errorMsg, function () { }, true);
                        } else {
                            $toastService.create("Could not create transfer. Please try again later");
                            console.log("error:" + response);
                        }
                    // console.log("error:" + response);
                });
            }


            $scope.closeModal = function (res) {
                Popeye.closeCurrentModal(res);
            };

        }
    ]
);
