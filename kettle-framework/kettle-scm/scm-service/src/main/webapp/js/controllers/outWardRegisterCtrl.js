angular.module('scmApp').controller('outWardRegisterCtrl', ['$rootScope', '$stateParams', '$scope', 'apiJson', '$http', '$state', 'appUtil',
    '$toastService', '$alertService', 'metaDataService', '$fileUploadService', '$window', 'previewModalService', '$timeout',
    function ($rootScope, $stateParams, $scope, apiJson, $http, $state, appUtil, $toastService, $alertService, metaDataService, $fileUploadService,
              $window, previewModalService, $timeout) {

        $scope.init = function () {
            $scope.businessType = $stateParams.type;
            if ($scope.businessType == "ECOM") {
                $scope.headingMsg = "ECOM Register Entries";
            }
            if ($scope.businessType == "B2B_SALES") {
                $scope.headingMsg = "B2B Register Entries";
            }
            $scope.unitData = appUtil.getUnitData();
            $scope.companyMap = appUtil.getCompanyMap();
            var currentDate = appUtil.getCurrentBusinessDate();
            if (!appUtil.isEmptyObject(currentDate)) {
                $scope.selectedUnit = null;
                $scope.txnTypes = ["B2B_SALES", "ECOM"];
                $scope.startDate = new Date().toJSON().slice(0, 10);
                $scope.endDate = new Date().toJSON().slice(0, 10);
                $scope.selectView = true;
                $scope.invoiceView = false;
                $scope.viewRegister = true;
                $scope.formView = false;
                $scope.showViewActions = true;
                $scope.showFormActions = true;
                $scope.invoiceRequest = [];
                $scope.selectedUnit = null;
                $scope.vendorSelected = null;
                $scope.locationSelected = null;
                $scope.currentUser = appUtil.getCurrentUser();
                $scope.selectedStatusForEntry = ["PENDING_DISPATCH",  "CLOSED"];
                $scope.showPreview = previewModalService.showPreview;
                $scope.initTime = new Date().toLocaleTimeString().slice(0, 8);
                $scope.initDate = appUtil.formatDate(new Date(), 'dd-MM-yyyy-hh-mm-ss');
                $scope.fetchPending = false;
                getUnits();
                $scope.getInvoiceEntriesForSelectedCriteria($scope.showViewActions, $scope.startDate, $scope.endDate, $scope.fetchPending, $scope.selectedStatusForEntry);
                $scope.selectView = true;
                $scope.formResponse = getFormResponseInitialObject();
                $scope.userFilterInput = undefined;
                $scope.searchInvoiceId = "";
                $scope.searchInvoice = "";

            }
        };

        $scope.setSearchInvoiceId = function (invoiceId){
            $scope.searchInvoiceId = invoiceId;
        }

        $scope.setSearchInput = function (searchInput){
            $scope.searchInvoice = searchInput;
        }

        $scope.filterInvoices = function (userInput){
            $scope.userFilterInput = userInput;
        }

        $scope.filterByInvoiceId  = function (invoice){
            if($scope.searchInvoiceId == null || $scope.searchInvoiceId == ""){
                return true;
            }
            return invoice.id == $scope.searchInvoiceId;
        }

        function getFormResponseInitialObject() {
            return {
                serialNumber: null,
                date: null,
                challanNo: null,
                time: null,
                addressOfBuyer: null,
                detailsOfArticle: null,
                quantity: null,
                amount: null,
                nameOfDeliverer: null,
                vehicleNoType: null,
                signatureOfSecurity: null,
                remarks: null,
                invoiceId: null,
                businessType: null,
                unitId: appUtil.isEmptyObject($scope.selectedUnit) ? $scope.currentUser.unitId : $scope.selectedUnit
            };
        }


        $scope.viewPendingInvoices = function (actionType) {
            $rootScope.showFullScreenLoader = true;
            $scope.selectView = false;
            $scope.actionType = actionType;

        }
        $scope.getInvoices = function (actionType) {
            $scope.showFormActions = actionType;
            $scope.invoiceView = true;
            $scope.viewRegister = false;
            $scope.selectView = false;
            $scope.formView = false;
            getCreatedInvoiceForSelectedUnitAndToday($scope.showFormActions, $scope.startDate, $scope.endDate, $scope.fetchPending);
        };

        function getCreatedInvoiceForSelectedUnitAndToday(view) {
            $scope.selectView = false;
            $scope.formView = false;
            $scope.actionType = view;
            var statusValidForOutWardEntry = ["PENDING_DISPATCH", "CLOSED"];
            var unitId = appUtil.isEmptyObject($scope.selectedUnit) ? $scope.currentUser.unitId : $scope.selectedUnit;
            var params = {
                sendingUnit: unitId,
                isView: view,
                fetchPending: true,
                status: statusValidForOutWardEntry,
                businessType: $scope.businessType
            };
            $http({
                method: "GET",
                url: apiJson.urls.invoiceManagement.getInvoices,
                params: params
            }).then(function (response) {
                if (appUtil.isEmptyObject(response)) {
                    $toastService.create("No Invoices Found for Today");
                } else {
                    $scope.invoiceRequest = response.data;
                    $scope.invoiceRequest = $scope.invoiceRequest.sort(function (a, b) {
                        return b.id - a.id;
                    });
                }
            }, function (e) {
                console.log("Cannot get Invoice for Today and Selected Unit", e);
            });
        }

        $scope.backtoFormAndInvoiceView = function () {
            $scope.formResponse = getFormResponseInitialObject();
            $scope.formView = false;
            $scope.invoiceView = true;
            $scope.viewRegister=false;
            $scope.selectView = false;
        }

        $scope.backToRegisterView = function () {
            $scope.init();
            $scope.selectView = true;
            $scope.viewRegister = true;
            $scope.formView = false;
            $scope.invoiceView = false;
            $scope.getInvoiceEntriesForSelectedCriteria($scope.showViewActions, $scope.startDate, $scope.endDate);

        }

        function getUnits() {
            $http({
                method: 'GET', url: apiJson.urls.unitMetadata.allUnitsList
            }).then(function (response) {
                if (response.data != null) {
                    var units = response.data;
                    $scope.units = units.filter(function (unit) {
                        return (unit.category == "WAREHOUSE" || unit.category == "KITCHEN") && unit.status == "ACTIVE";
                    });
                }
            }, function (err) {
                console.log("Error in getting Unit List", err);
            });
        }

        $scope.selectInvoice = function (invoice) {
            $scope.selectedInvoice = invoice;
        }

        $scope.activateForm = function (actionType, invoice) {
            $scope.selectedInvoice = invoice;
            $scope.formAction = actionType;
            $scope.formView = true;
            $scope.selectedItem = invoice;
            $scope.formResponse.amount = invoice.totalAmount + invoice.additionalCharges;
            $scope.formResponse.nameOfDeliverer = invoice.sendingUnit.name;
            $scope.formResponse.vehicleNoType = invoice.vehicle.registrationNumber;
            $scope.formResponse.serialNumber = invoice.id;
            $scope.formResponse.challanNo = invoice.generatedId;
            $scope.formResponse.time = new Date(invoice.createdAt);
            $scope.formResponse.addressOfBuyer = invoice.billingAddress;
            $scope.formResponse.date = new Date(invoice.dispatchDate);

            if (invoice.items.length > 1) {
                $scope.formResponse.detailsOfArticle = "Total Items";
                $scope.formResponse.quantity = invoice.items.length;
            }
            if (invoice.items.length === 1) {
                $scope.formResponse.detailsOfArticle = invoice.items[0].sku.name;
                $scope.formResponse.quantity = invoice.items[0].qty;
            }
        }

        $scope.setNewEntry = function (invoiceRequest) {
            try {
                var date = new Date($scope.formResponse.date);
                var time = new Date($scope.formResponse.time);
                date.setHours(time.getHours());
                date.setMinutes(time.getMinutes());
                date.setSeconds(time.getSeconds());
                $scope.formResponse.dateTime = date;
                $scope.formResponse.businessType = $scope.selectedInvoice.type;
                $scope.formResponse.invoiceId = $scope.selectedInvoice.id;

                $http({
                    method: 'POST',
                    url: apiJson.urls.invoiceManagement.saveOutwardRegisterEntry,
                    data: $scope.formResponse,
                }).then(function success(response) {
                    if (response.data) {
                        var entry = response.data;
                        $toastService.create("Form was Successfully Submitted");
                        var message = "New Entry created <b>successfully! </b> <br>";
                        $alertService.alert("Entry Created!!", message, $scope.backToRegisterView());
                        $scope.backToRegisterView();
                        var i = 0;
                        $('#myform')[i++].reset();
                        $scope.init();
                    }
                }, function failure(err) {
                    var message = "New Entry<b> failed to Create!</b>  <br>";
                    $alertService.alert("Entry Not Created!!", message, true);
                    console.log("Error in saving forms data in Database", err);
                })

            } catch (err) {
                alert("Form was not Submitted");
            }
        }

        $scope.openClicked = function (invId) {
            $scope.selectedItem = invId;
        }

        $scope.getInvoices = function () {
            $scope.invoiceView = true;
            $scope.viewRegister = false;
            $scope.selectView = false;
            getCreatedInvoiceForSelectedUnitAndToday($scope.showViewActions, $scope.startDate, $scope.endDate, $scope.fetchPending);
        };

        $scope.getInvoiceEntriesForSelectedCriteria = function (showViewActions, startDate, endDate) {
            var unitId = appUtil.isEmptyObject($scope.selectedUnit) ? $scope.currentUser.unitId : $scope.selectedUnit;
            var params = {
                startDate: startDate,
                endDate: endDate,
                showViewActions: showViewActions,
                sendingUnit: unitId,
                businessType: $scope.businessType
            };
            $http({
                method: 'GET',
                url: apiJson.urls.invoiceManagement.getOutWardFormEntries,
                params: params
            }).then(function (response) {
                if (appUtil.isEmptyObject(response)) {
                    $toastService.create("No Entries found!");
                } else {
                    $scope.formEntry = response.data;
                    $scope.formEntry = $scope.formEntry.sort(function (a, b) {
                        return b.id - a.id;
                    });
                }
            }, function (err) {
                console.log("Encountered error at backend", err);
            });
        }

        $scope.getEntriesOfInvoices = function () {
            $scope.selectView = true;
            $scope.getInvoiceEntriesForSelectedCriteria($scope.showViewActions, $scope.startDate, $scope.endDate);
        };

        $scope.changeStatus = function (changedStatus) {
            $scope.fetchPending = changedStatus;
            $scope.getInvoices();
            console.log($scope.fetchPending);
        }

        $scope.openForm = function (invoice) {
            $scope.selectedItem = invoice.id;
            if (invoice.expanded != null) {
                invoice.expanded = !invoice.expanded;
            } else {
                invoice.expanded = true;
            }
            if (invoice.expanded === false) {
                $scope.selectedItem = null;
            }
        }

        $scope.closeForm = function (invoice) {
            invoice.expanded = false;
        }

        $scope.closeModal = function (modal) {
            $timeout(function (modal) {
                $('#modal').val('').trigger('change');
            });
            var i = 0;
            $('#myform')[i++].reset();
        }
    }]);