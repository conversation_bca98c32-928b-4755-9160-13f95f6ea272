'use strict';

angular.module('scmApp')
    .controller('masterDocumentCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$location', '$toastService', 'metaDataService',
        '$fileUploadService', '$alertService', 'previewModalService', 'Popeye','$timeout','$window',
        function ($rootScope, $scope, apiJson, $http, appUtil, $location, $toastService, metaDataService,
                  $fileUploadService, $alertService, previewModalService, Popeye, $timeout, $window) {

            $scope.init = function () {
                $scope.documentName = null;
                $scope.additionalDocuments = [];
                $scope.createdBy = appUtil.getCurrentUser().userId;
                $scope.getAdditionalDocument();
            };

            $scope.getAdditionalDocument = function() {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'GET',
                    url: apiJson.urls.serviceMappingManagement.addAdditionalDocuments,
                }).then(function success(response) {
                    $rootScope.showFullScreenLoader = false;
                    if (response.status === 200 && response.data) {
                        $scope.additionalDocuments = response.data;
                    }
                }, function error(response) {
                    $rootScope.showFullScreenLoader = false;
                    $toastService.create("Unable to get Additional Document Name due to "+ response.data.errorMessage);
                });
            }

            $scope.addMasterDocuments = function(){
            if ($scope.documentName == null || $scope.documentName.trim().length === 0) {
                        $toastService.create("Invalid Document Name");
                        return;
                    }
                        $rootScope.showFullScreenLoader = true;
                        $http({
                            method: 'POST',
                            url: apiJson.urls.serviceMappingManagement.addMasterDocuments+"?documentName="+$scope.documentName+
                            "&createdBy="+ $scope.createdBy,

                        }).then(function success(response) {
                            $rootScope.showFullScreenLoader = false;
                            if (response.status === 200 && response.data) {
                                $toastService.create("Document Name Added Successfully");
                                $scope.documentName=null;
                                $scope.additionalDocuments=[];
                                $scope.getAdditionalDocument();
                            }
                        }, function error(response) {
                            $rootScope.showFullScreenLoader = false;
                            console.log("error:" + response);
                            $toastService.create("Unable to add Document Name due to "+ response.data.errorMessage);
                        });


            }

    }]);