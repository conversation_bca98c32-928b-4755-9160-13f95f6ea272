/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

angular
    .module('scmApp')
    .controller(
        'prodBookingCtrl',
        [
            '$rootScope',
            '$scope',
            'apiJson',
            '$http',
            'appUtil',
            '$toastService',
            'metaDataService',
            '$timeout',
            '$alertService',
            'previewModalService',
            function ($rootScope, $scope, apiJson, $http, appUtil, $toastService, metaDataService, $timeout,$alertService, previewModalService) {

                $scope.init = function () {
                    $scope.currentUnitId = appUtil.getCurrentUser().unitId;
                    $scope.fixedAsset = false;
                    $scope.hasProductionBooking = false;
                    $scope.showReverseBooking = false;
                    $scope.bookingData = null;
                    $scope.selectedVendor = null;
                    $scope.selectedLocation = null;
                  //  $scope.scmProductDetails = appUtil.getActiveScmProducts();
                    $scope.getProdUnitToSku(appUtil.getActiveScmProducts());
                    // $scope.getbookingHistory();
                    $scope.showPreview = previewModalService.showPreview;
                    $scope.updateMapping = null;
                };

                $scope.reset = function () {
                    $scope.fixedAsset = false;
                    $scope.selectedLocation = null;
                    $scope.bookingData = null;
                    $scope.hasProductionBooking = false;
                    $scope.showReverseBooking = false;
                    $scope.selectedProductId = null;
                    $scope.bookingQuantity = null;
                    // $scope.getbookingHistory();
                    $timeout(function () {
                        $('#selectedProductIdselect').val('').trigger('change');
                    });
                };

                $scope.bySemiFinshed = function (value) {
                    return value.recipeRequired == true && value.autoProduction == false;
                };

                $scope.calculateBookingConsumption = function (flag) {
                    $scope.updateMapping=flag;
                    if($scope.updateMapping==true){
                        $scope.bookingQuantity=1;
                    }
                    if ($scope.selectedProductId != null && $scope.bookingQuantity != null) {
                        $http({
                            url: apiJson.urls.productionBooking.calculateConsumption,
                            method: "GET",
                            params: {
                                productId: $scope.selectedProductId,
                                unitId: appUtil.getCurrentUser().unitId,
                                quantity: $scope.bookingQuantity
                            }
                        }).then(function (response) {
                            if (response.data != undefined && response.data != null) {
                                $scope.bookingData = response.data;
                                for(var i=0; i<$scope.scmProductDetails.length; i++){
                                    if($scope.scmProductDetails[i].productId == response.data.productId && $scope.scmProductDetails[i].categoryDefinition.id == 3){
                                        $scope.fixedAsset = true;
                                        $scope.getVendorForSku(response.data.skuId);
                                        break;
                                    }
                                }
                                $scope.initializeSkus();
                            }else {
                                var msg = "Unable to calculate consumption! Please check product recipe."
                                $toastService.create(msg);
                            }
                        }, function (response) {
                            console.log(response);
                            $toastService.create("Server Error");
                        });
                    } else {
                        $toastService.create("Please select all values!");
                    }
                };

                $scope.initializeSkus = function (){
                        for(var j = 0 ;j<$scope.bookingData.bookingConsumption.length;j++){
                            $scope.bookingData.bookingConsumption[j].selectedSku=$scope.bookingData.bookingConsumption[j].availableSkuList[0];
                        }
                }

                $scope.getVendorForSku = function (skuId) {
                    $scope.vendorDataList = [];
                    $http({
                        method: "POST",
                        dataType: 'json',
                        data: skuId,
                        headers: {
                            "Content-Type": "application/json"
                        },
                        url: apiJson.urls.skuMapping.getVendorForSku
                    }).then(function success(response) {
                        $scope.vendorDataList = response.data;
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                };

                $scope.selectVendor = function (vendor) {
                    $scope.selectedVendor = vendor;
                    metaDataService.getVendorLocations(vendor.id, function (locations) {
                        $scope.locationList = locations;
                    });
                };

                $scope.selectDispatchLocation = function (location) {
                    $scope.selectedLocation = location;
                };

                $scope.setWastageQuantity = function (c,quantity,index) {
                    if(c.calculatedQuantity<quantity && quantity >0) {
                        $toastService.create("Please Enter Wastage Quantity Less Than Actual Calculated Quantity");
                        c.wastageQuantity = null;
                        $('wastage-'+index).val(null);
                        return;
                    }
                    c.wastageQuantity = quantity;
                }

                $scope.setAutoFillWastageQuantity = function (c,val,index) {
                    if(val) {
                        c.wastageQuantity = c.calculatedQuantity;
                    } else {
                        $('wastage-'+index).val(null);
                        c.wastageQuantity = null;
                    }
                }


                $scope.submitBookingConsumption = function () {
                    var errorMessage = "";
                    var index = null;

                    $scope.callRecursionTOSetData($scope.bookingData,errorMessage);

                    // for (index in $scope.bookingData.bookingConsumption) {
                    //     if ($scope.bookingData.bookingConsumption[index].selectedSku != undefined
                    //         && $scope.bookingData.bookingConsumption[index].selectedSku != null
                    //         && $scope.bookingData.bookingConsumption[index].selectedSku.id != null) {
                    //         $scope.bookingData.bookingConsumption[index].skuId = $scope.bookingData.bookingConsumption[index].selectedSku.id;
                    //         $scope.bookingData.bookingConsumption[index].skuName = $scope.bookingData.bookingConsumption[index].selectedSku.name;
                    //         // $scope.bookingData.bookingConsumption[index].bookingConsumption.map(function (consumption) {
                    //         //
                    //         //
                    //         //     if(consumption.selectedSku != undefined && consumption.selectedSku != null && consumption.selectedSku.id != null){
                    //         //         consumption.skuId = consumption.selectedSku.id;
                    //         //         consumption.skuName = consumption.selectedSku.name;
                    //         //     }else{
                    //         //         errorMessage = errorMessage + "<b>"
                    //         //             + consumption.productName + "</b><br>";
                    //         //     }
                    //         // })
                    //         if($scope.bookingData.bookingConsumption[index].bookingConsumption.length>0){
                    //             for( var i in $scope.bookingData.bookingConsumption[index].bookingConsumption){
                    //                 if ( $scope.bookingData.bookingConsumption[index].bookingConsumption[i].selectedSku != undefined
                    //                     && $scope.bookingData.bookingConsumption[index].bookingConsumption[i].selectedSku != null
                    //                     && $scope.bookingData.bookingConsumption[index].bookingConsumption[i].selectedSku.id != null) {
                    //                     $scope.bookingData.bookingConsumption[index].bookingConsumption[i].skuId = $scope.bookingData.bookingConsumption[index].bookingConsumption[i].selectedSku.id;
                    //                     $scope.bookingData.bookingConsumption[index].bookingConsumption[i].skuName = $scope.bookingData.bookingConsumption[index].bookingConsumption[i].selectedSku.name;
                    //                 }
                    //             }
                    //         }
                    //
                    //
                    //     } else {
                    //         errorMessage = errorMessage + "<b>"
                    //             + $scope.bookingData.bookingConsumption[index].productName + "</b><br>";
                    //     }
                    // }
                    var currentUser = appUtil.getCurrentUser();
                    $scope.bookingData.generatedBy = {
                        id: currentUser.user.id,
                        name: currentUser.user.name
                    };
                    if (errorMessage.length == 0) {
                        $alertService.confirm(
                            "Are you sure, the booking and consumption details are correct?", null,
                            function (result) {
                                if (result) {
                                    $scope.submitBookingData();
                                }
                            });
                    } else {
                        errorMessage = "Please update Unit to SKU mapping. <br/> SKU Missing for the following products.<br/><br/>"
                            + errorMessage;
                        $alertService.alert("Missing SKU", errorMessage);
                    }
                };

                $scope.callRecursionTOSetData = function (data,errorMessage) {
                    for (var index in data.bookingConsumption) {
                        if (data.bookingConsumption[index].selectedSku != undefined
                            && data.bookingConsumption[index].selectedSku != null
                            && data.bookingConsumption[index].selectedSku.id != null) {
                            data.bookingConsumption[index].skuId = data.bookingConsumption[index].selectedSku.id;
                            data.bookingConsumption[index].skuName = data.bookingConsumption[index].selectedSku.name;
                            if (data.bookingConsumption[index].bookingConsumption.length > 0) {
                                $scope.callRecursionTOSetData(data.bookingConsumption[index],errorMessage);
                            }
                        } else {
                            errorMessage = errorMessage + "<b>"
                                + $scope.bookingData.bookingConsumption[index].productName + "</b><br>";
                            return false;
                        }
                    }
                    return true;
                };

                $scope.submitBookingData = function () {
                    console.log($scope.updateMapping);
                    if($scope.fixedAsset == true){
                        if(appUtil.isEmptyObject($scope.selectedVendor && $scope.selectedLocation)){
                            $toastService.create("Please Select Vendor and Dispatch Location");
                            return;
                        }
                        $scope.bookingData.vendorId = $scope.selectedVendor.id;
                        $scope.bookingData.dispatchId = $scope.selectedLocation.id;

                    }
                    $scope.bookingData.reverseBooking = $scope.hasProductionBooking;
                    $http({
                        url: apiJson.urls.productionBooking.add,
                        method: "POST",
                        data: $scope.bookingData,
                        params: {
                            updateMapping:$scope.updateMapping
                        }
                    }).then(function (response) {
                        if($scope.updateMapping==true){
                            $toastService.create("Mapping Updated Successfully!");
                        }else {
                            $toastService.create("Booking Added Successfully!");
                        }
                        var isReverse = $scope.bookingData.reverseBooking;
                        $scope.reset();
                        $scope.getLastBooking(isReverse);
                    }, function (response) {
                        console.log(response);
                        if (response.data.errorMsg != null) {
                            $alertService.alert(response.dataerrorTitle, response.data.errorMsg, null, true);
                        } else {
                            $toastService.create("Server Error");
                        }
                        if(response.data.e)
                        $toastService.create("Server Error");
                    });
                };

                $scope.changeHasProductionBooking = function (val) {
                    $scope.hasProductionBooking = val;
                }

                $scope.getbookingHistory = function () {
                    $scope.showReverseBooking = false;
                    var payload = {
                        unitId: appUtil.getCurrentUser().unitId,
                        startDate: appUtil.getDateWithoutTime(),
                        endDate: appUtil.getDateWithoutTime()
                    };
                    $http({
                        url: apiJson.urls.productionBooking.bookings,
                        method: "POST",
                        data: payload
                    }).then(function (response) {
                        $scope.bookingHistory = response.data;
                    }, function (response) {
                        console.log(response);
                    });
                };

                $scope.getReversebookingHistory = function () {
                    $scope.showReverseBooking = true;
                    var payload = {
                        unitId: appUtil.getCurrentUser().unitId,
                        startDate: appUtil.getDateWithoutTime(),
                        endDate: appUtil.getDateWithoutTime(),
                    };
                    $http({
                        url: apiJson.urls.productionBooking.bookings+ "?isReverse=true",
                        method: "POST",
                        data: payload
                    }).then(function (response) {
                        $scope.bookingHistory = response.data;
                    }, function (response) {
                        console.log(response);
                    });
                };

                $scope.getLastBooking = function (isReverse) {
                    if (isReverse) {
                        $scope.showReverseBooking = true;
                    }
                    if($scope.updateMapping!=true) {
                        var list = [];
                        var param = {
                            unitId: appUtil.getCurrentUser().unitId,
                            isReverse: isReverse
                        };
                        $http({
                            url: apiJson.urls.productionBooking.getLastBooking,
                            method: "POST",
                            params: param
                        }).then(function (response) {
                            list.push(response.data);
                            $scope.bookingHistory = list;
                        }, function (response) {
                            console.log(response);
                        });
                    }
                };


                $scope.cancelBooking = function (event, bookingId) {
                    var msg = "This action cannot be reverted back. All the SKU inventory consumption will be reverted.";
                    $alertService.confirm("Are you sure?", msg, function (result) {
                        if (result) {
                            var payload = {
                                empId: appUtil.getCurrentUser().user.id,
                                id: bookingId,
                                reverseBooking : $scope.showReverseBooking
                            };

                            $http({
                                url: apiJson.urls.productionBooking.cancel,
                                method: "POST",
                                data: payload
                            }).then(function (response) {
                                if (response.data) {
                                    $toastService.create("Booking Cancelled!");
                                    if ($scope.showReverseBooking) {
                                        $scope.getReversebookingHistory();
                                    } else {
                                        $scope.getbookingHistory();
                                    }
                                } else {
                                    $toastService.create("Booking Cancellation Failed!");
                                }
                            }, function (response) {
                                console.log(response);
                            });
                        }
                    });

                    event.stopPropagation();
                };

                $scope.getProdUnitToSku = function(activeScmProducts) {
                	 $http({
    					method : "GET",
    					url : apiJson.urls.filter.availableProdfromUnitToSku,
    					params : {
    						unitId : appUtil.getCurrentUser().unitId
    					}
    				}).then(function success(response) {
    					if (response.data != null && response.data.length > 0) {
    						console.log(response.data);
    						$scope.scmProductDetails = $scope.getFilteredProds(activeScmProducts,response.data);
    					} else {
    						$toastService.create("Something went wrong. Please try again!");
    					}
    				}, function error(response) {
    					console.log("error:" + response);
    				});
    			};

                $scope.getFilteredProds = function (activeScmProducts, ProdIds) {
                    var result = [];
                    angular.forEach(ProdIds, function (prod) {
                        angular.forEach(activeScmProducts, function (scmProd) {
                            if (scmProd.productId == prod) {
                                if (scmProd.recipeRequired == true) {//filter of recipe require=Y
                                    result.push(scmProd);
                                }
                            }
                        });
                    });
                    console.log(result);
                    return result;
                };


            }]);
