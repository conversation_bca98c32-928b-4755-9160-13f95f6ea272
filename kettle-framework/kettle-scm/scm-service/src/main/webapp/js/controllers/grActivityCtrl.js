/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by <PERSON><PERSON> on 16-06-2016.
 */

'use strict';

angular.module('scmApp')
    .controller('grActivityCtrl', ['$rootScope', '$scope', 'apiJson', '$http', 'appUtil', '$location', '$toastService', 'Popeye', 'previewModalService','$alertService','metaDataService','$fileUploadService',
        function ($rootScope, $scope, apiJson, $http, appUtil, $location, $toastService, Popeye, previewModalService, $alertService,metaDataService,$fileUploadService) {

                $scope.isWarehouse = appUtil.isWarehouseOrKitchen();
                $scope.isOnlyWarehouse = appUtil.isWarehouse();
                $scope.init = function () {
                    $scope.showPendingGr = true;
                    $scope.grEvent = {};
                    $scope.amountWithoutRejected = 0;
                    $scope.getPendingGRs();
                    $scope.selectedSkus = [];
                    $scope.showPreview = previewModalService.showPreview;
                    $scope.allheaders = ['Pending Disputes' , 'Pending Receivings', 'Raised Disputes' , 'All'];
                  /*  $scope.allheaders = [
                        { name : 'Pending Disputes',checked : false} ,
                        {name : 'Pending Receivings' , checked: false},
                        {name :'Raised Disputes',checked: false} ,{ name :'All' , checked: false}];*/
                    $scope.selectedHeader = 'All';
                    $scope.toId = null;
                    $scope.currentUser = appUtil.getCurrentUser();
                    $scope.userList = [];
                    $scope.selectedUser = null;
                    $scope.multiSelectSettings = {showEnableSearchButton: false, template: '<b>{{option}}</b>'};
                    $scope.dayClosePending = false;
                    $scope.checkPendingDayClose($scope.currentUser.unitId, function() {
                        metaDataService.getSkuListForUnit($scope.currentUser.unitId,function(skuForUnitList){
                            $scope.skuList = skuForUnitList;
                            $scope.selectedSku = null;
                            console.log($scope.selectedSku);
                        });
                    });
                    $scope.selectedUnit = null;
                    $scope.unitList = [];
                };
                    $scope.uploadedPODDoc = null;
                    $scope.porImagesUrl=[];
                    $scope.getSkuFilterData = function (){
                    $scope.getPendingGRs();
                }

                $scope.checkPendingDayClose = function(unitId, callback){
                    var URL="";
                    if($scope.isOnlyWarehouse == true){
                        URL  = apiJson.urls.warehouseClosing.checkFixedAssetDayClose
                    }else if($scope.isOnlyWarehouse == false){
                        URL  = apiJson.urls.stockManagement.checkFixedAssetDayClose
                    }
                    $http({
                        method: 'GET',
                        url: URL,
                        params: {
                            id:unitId
                        }
                    }).then(function success(response) {
                        if(response.data != null){
                            $scope.blockDaily = response.data.blockDaily;
                            $scope.blockWeekly = response.data.blockWeekly;
                            $scope.blockMonthly = response.data.blockMonthly;
                            if($scope.blockDaily || $scope.blockWeekly || $scope.blockMonthly){
                                $scope.dayClosePending = true;
                                if($scope.blockMonthly){
                                    $scope.blockDaily = false;
                                    $scope.blockWeekly = false;
                                }
                                else if($scope.blockWeekly){
                                   $scope.blockDaily = false;
                                }
                            }
                            var today = new Date();
                            if(today.getDay() == 0 || today.getDay() == 6){
                                $scope.dayClosePending = false;
                            }
                            callback();
                        }
                        else{
                            $scope.dayClosePending = false;
                            callback();
                        }
                    });
                }

                $scope.showAssetGrid= function(){
                    $scope.showGrid = !$scope.showGrid;
                }

                $scope.initiateGRRequest = function(){

                    console.log($scope.grDetail);
                    if(appUtil.getCurrentUser()!=null && appUtil.getCurrentUser().user!=null && appUtil.getCurrentUser().unitId!=null){
                         $http({
                        url: apiJson.urls.goodsReceivedManagement.initiateGREvent,
                        method: 'POST',
                        params:{
                            userId : appUtil.getCurrentUser().user.id,
                            unitId : appUtil.getCurrentUser().unitId
                        },
                        data: $scope.grDetail,
                    }).then(function success(data){
                        console.log(data);
                        $scope.grDetail.faGrInitiated = true;
                    },function error(response){
                        if(response.data.errorTitle && response.data.errorMsg){
                        $alertService.alert(response.data.errorTitle, response.data.errorMsg);
                          return;   
                    }
                    $alertService.alert("ERROR", "Something went wrong !");
                    console.log(response.data);
                    })
                    }


                }


            $scope.getImageS3Links = function (callback) {

                var filesArr=[];
                $scope.grDetail.goodsReceivedItems.map(function (grItem){
                    var name ="multi-por-images_"+grItem.id;
                    var htmlEle = document.getElementById(name);
                  if(htmlEle!=null){
                    var currentFileArr=[];
                    currentFileArr=htmlEle.files;
                    for (var index = 0; index < currentFileArr.length; index++) {
                        filesArr.push(currentFileArr[index]);
                    }
                  }
                    
                })
                if (filesArr.length <= 0) {
                    callback();
                }
                if (filesArr.length > $scope.grDetail.goodsReceivedItems.length*3) {
                    $toastService.create("Please Upload less than "+$scope.grDetail.goodsReceivedItems.length*3 +" Images");
                    return;
                }
                var count = 0;
                var fd = [];
                $scope.porImagesUrl = [];

                for (var index = 0; index < filesArr.length; index++) {

                    $scope.uploadImage = function (fd, i, filesArr) {

                       if(filesArr[i].size > 5120000){
                           $toastService.create('Image size should not be greater than 5 MB.');
                           return;
                       }
                       else {
                            var fileExt = metaDataService.getFileExtension(filesArr[i].name);
                            fd[i] = new FormData();
                            fd[i].append('type', "OTHERS");
                            fd[i].append('docType', "PROOF_OF_REJECTION");
                            fd[i].append('mimeType', fileExt.toUpperCase());
                            fd[i].append('userId', appUtil.getCurrentUser().userId);
                            fd[i].append('file', filesArr[i]);
                            fd[i].append('grId', $scope.grDetail.id);
                            fd[i].append('fileName', filesArr[i].name);
                            if (metaDataService.isImage(fileExt.toLowerCase())) {
                                $http({
                                    url: apiJson.urls.goodsReceivedManagement.uploadProofOfRejection,
                                    method: 'POST',
                                    data: fd[i],
                                    headers: {'Content-Type': undefined},
                                    transformRequest: angular.identity
                                }).success(function (response) {
                                    $rootScope.showFullScreenLoader = false;
                                    if (response.fileUrl !== null) {
                                        $scope.previewDocument = true;
                                        $scope.porImagesUrl.push(response.documentId);
                                        if(filesArr.length-1 ===count){
                                            callback();
                                        }
                                        count +=1;
                                    } else {
                                        $toastService.create("Image Upload failed");
                                    }
                                }).error(function (response) {
                                    $rootScope.showFullScreenLoader = false;
                                    $toastService.create("Image Upload failed");
                                    return;
                                });
                            } else {
                                $toastService.create('Upload Failed , File Format not Supported, Please upload images only');
                                return;
                            }
                       }

                    }
                    $scope.uploadImage(fd, index, filesArr);
                }
            }

                $scope.getGrView = function (header){
                    $http({
                        url: apiJson.urls.goodsReceivedManagement.getGrsView,
                        method: 'POST',
                        responseType: 'arraybuffer',
                        params : {
                            unitId : $scope.currentUser.unitId,
                            type : header
                        },
                        headers: {
                            'Content-type': 'application/json',
                            'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                        }
                    })
                        .success(
                            function (data) {
                                console.log("excell generated");
                                var fileName =  "Goods_Received" ;
                                    + ".xlsx";
                                var blob = new Blob(
                                    [data],
                                    {
                                        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                                    }, fileName);
                                saveAs(blob, fileName);
                            }).error(function (err) {
                        console.log("Error during getting data", err);
                    });

                }



                $scope.isFiltered = function (item , type){
                    if($scope.toId !=null){
                        if(item.transferOrderId === $scope.toId){
                            return true;
                        }
                        return false;
                    }else if($scope.selectedUser !=null){
                        if(item.generatedBy.name === $scope.selectedUser){
                            if(!appUtil.isEmptyObject($scope.selectedUnit)){
                                if(type != 'RaisedDisputedGR'){
                                    return item.generationUnitId.name === $scope.selectedUnit;
                                }else{
                                    return item.generatedForUnitId.name === $scope.selectedUnit;
                                }


                            }else{
                                return true;
                            }
                        }
                        return  false;
                    }else if (!appUtil.isEmptyObject($scope.selectedUnit)){
                        if(type != 'RaisedDisputedGR'){
                            return item.generationUnitId.name === $scope.selectedUnit;
                        }else{
                            return item.generatedForUnitId.name === $scope.selectedUnit;
                        }
                    }
                    return  true;
                }



                $scope.getPendingGRs = function () {
                    $scope.allGrs = [];
                    $scope.addToList('PendingDisputedGR', 'No pending disputed goods receiving found.', 'Pending Disputes', true)
                    $scope.addToList('PendingGR', 'No pending goods receiving found.', 'Pending Receivings', false)
                    $scope.addToList('RaisedDisputedGR', 'No raised disputes goods receiving found.', 'Raised Disputes', false)
                    $scope.getPendingGRData();
                    $scope.getPendingDisputedGrs();
                    $scope.getRaisedDisputedGrs();



                };

                $scope.getPendingGRData = function () {
                    $scope.pendingGRList = null;
                    $http({
                        method: "GET",
                        url: apiJson.urls.goodsReceivedManagement.pendingGrs + "?unitId=" + appUtil.getCurrentUser().unitId,
                        params: {
                            skuId : $scope.selectedSku != null ? $scope.selectedSku.id : null
                        }
                    }).then(function success(response) {
                        $scope.updateList('PendingGR', response.data);
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                };

                $scope.addToList = function (type, message, header, actionForRejected) {
                    var data = {
                        type: type,
                        message: message,
                        header: header,
                        list: null,
                        actionForRejected: actionForRejected
                    };
                    $scope.allGrs.push(data);

                };

                $scope.updateList = function (type, list){
                   for(var gr in list){
                       if($scope.userList.indexOf(list[gr].generatedBy.name) == -1){
                           $scope.userList.push(list[gr].generatedBy.name);
                       }
                       if($scope.unitList.indexOf(list[gr].generationUnitId.name) == -1){
                           $scope.unitList.push(list[gr].generationUnitId.name);
                       }

                   }
                    for (var i in $scope.allGrs) {
                        if ($scope.allGrs[i].type == type) {
                            $scope.allGrs[i].list = list;
                            return;
                        }
                    }

                };

                $scope.getPendingDisputedGrs = function () {
                    $scope.pendingDisputedGRList = null;
                    $http({
                        method: "GET",
                        url: apiJson.urls.goodsReceivedManagement.pendingDisputedGrs + "?unitId=" + appUtil.getCurrentUser().unitId,
                        params: {
                            skuId : $scope.selectedSku != null ? $scope.selectedSku.id : null
                        }
                    }).then(function success(response) {
                        $scope.updateList('PendingDisputedGR', response.data);
                        //console.log($scope.pendingDisputedGRList);
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                };

                $scope.getColorCode = function (grDate){
                    var grdate =  appUtil.formatDate(grdate, "yyyy-MM-dd hh:mm:ss");
                    var grDate = new Date(grDate);
                    var currentDate = new Date();
                    grDate.setHours(0,0,0,0);
                    currentDate.setHours(0,0,0,0);
                    if(currentDate.getTime()>grDate.getTime()){
                        return "#cdffcd";
                    }else if(currentDate.getTime()<grDate.getTime()){
                        return 'red';
                    }else{
                        return "#ffffbf";
                    }
                }


                $scope.getRaisedDisputedGrs = function () {
                    $scope.raisedDisputedGRList = null;


                    $http({
                        method: "GET",
                        url: apiJson.urls.goodsReceivedManagement.raisedDisputedGrs + "?unitId=" + appUtil.getCurrentUser().unitId,
                        params: {
                            skuId : $scope.selectedSku != null ? $scope.selectedSku.id : null
                        }
                    }).then(function success(response) {
                        $scope.updateList('RaisedDisputedGR', response.data);
                        //console.log($scope.raisedDisputedGRList);
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                };
                $scope.refreshGrEvent = function(grId){
                    $scope.showPendingGr = true;
                    $scope.fillReceiving(grId);
                }

                $scope.cancelGrEvent = function(grId){
                    $alertService.confirm("Are you sure?","Cancel Gr Event !",function(result){
                            if(result){
                                $http({
                                    method:"GET",
                                    url : apiJson.urls.goodsReceivedManagement.cancelGrEvent,
                                    params: {grId : grId}
                                  }).then(function success(response){
                                      if(response.data == true){
                                          $scope.fillReceiving(grId);
                                          $alertService.alert("Successfully Cancelled", "Fixed Assets GR is cancelled !");
                                      }
                                  }, function error(response){
                                      console.log("Error in gr event cancel : "+ response);
                                      $alertService.alert(response.data.errorTitle, response.data.errorMsg, function () { }, true);
                                  })
                            }
                    });
                 
                }

                $scope.fillReceiving = function (grId) {
                    $http({
                        method: "GET",
                        url: apiJson.urls.goodsReceivedManagement.goodReceived + "?grId=" + grId
                    }).then(function success(response) {
                        $scope.grDetail = response.data;
                       
                        if($scope.grDetail.transferOrderType === 'FIXED_ASSET_TRANSFER' || $scope.grDetail.transferOrderType === 'BROKEN_ASSET_TRANSFER' || $scope.grDetail.transferOrderType === 'RENOVATION_ASSET_TRANSFER')
                        {
                        for(var i in $scope.grDetail.goodsReceivedItems){
                            $scope.grDetail.goodsReceivedItems[i].isScanned = false;
                        }
                        $scope.grDetail.faGrInitiated = false;
                        $http({
                            method:"GET",
                            url : apiJson.urls.goodsReceivedManagement.getGrEvent,
                            params : {grId: $scope.grDetail.id }
                        }).then(function success(response){
                            if(response.data !=null && response.data.faGrDataList!=null){
                                $scope.grEvent = response.data;
                                for(var i in $scope.grDetail.goodsReceivedItems){ 
                                    for(var j in response.data.faGrDataList){
                                        if($scope.grDetail.goodsReceivedItems[i].associatedAssetId === response.data.faGrDataList[j].assetId){
                                            if(response.data.faGrDataList[j].scannedQty>0){
                                            $scope.grDetail.goodsReceivedItems[i].isScanned = true;
                                            $scope.grDetail.goodsReceivedItems[i].packagingDetails[0].hasRejection = false;
                                            $scope.grDetail.goodsReceivedItems[i].packagingDetails[0].numberOfUnitsRejected = 0;
                                            $scope.grDetail.goodsReceivedItems[i].packagingDetails[0].rejectionReason = null;
                                            $scope.updateRejection($scope.grDetail.goodsReceivedItems[i].drillDowns,$scope.grDetail.goodsReceivedItems[i].packagingDetails[0],$scope.grDetail.goodsReceivedItems[i],false);
                                        }
                                        }     
                                    }
                                }
                                $scope.grDetail.faGrInitiated = true;
                            }else{
                                $scope.grEvent={};
                            }
                            $scope.setInitialUnitReceived();
                            $scope.showPendingGr = false;
                          
                        }, function error(response){
                            console.log("error:" + response);
                            $alertService.alert("Error", "Something not found !");
                        });

                        }else{
                            $scope.setInitialUnitReceived();
                            $scope.showPendingGr = false;
                        }
                    }, function error(response) {
                        console.log("error:" + response);
                    });

                   
                };

                $scope.findReceivingAmount = function(){
                    if (!appUtil.isEmptyObject($scope.grDetail)) {
                        $scope.amountWithoutRejectedDup = 0;
                        $scope.grDetail.goodsReceivedItems.forEach(function (grItem) {
                            grItem.packagingDetails.forEach(function (pkg) {
                                if (pkg.numberOfUnitsRejected == 0 || pkg.numberOfUnitsRejected == null || pkg.numberOfUnitsRejected == undefined) {
                                    console.log("negotiated Price and  tax amount is : ", grItem.unitPrice, grItem.taxAmount);
                                    var unitPrice = grItem.unitPrice != null ? parseFloat(grItem.unitPrice) : 0;
                                    var calAmount = $scope.getTotalCost(grItem.transferredQuantity,unitPrice);
                                    console.log("cal amount is : ",calAmount);
                                    var taxAmount = 0;
                                    if (grItem.taxAmount != null) {
                                        taxAmount = parseFloat(grItem.taxAmount);
                                    }
                                    console.log("tax amount is : ",taxAmount);
                                    // var totalAmount = parseFloat(calAmount) + parseFloat(taxAmount);
                                    var totalAmount = $scope.getAmountAndTax(parseFloat(calAmount),parseFloat(taxAmount));
                                    console.log("total amount is : ",totalAmount);
                                    $scope.amountWithoutRejectedDup = parseFloat(parseFloat($scope.amountWithoutRejectedDup) + parseFloat(totalAmount)).toFixed(6);
                                    console.log("amnt with out rejected new is : ",$scope.amountWithoutRejectedDup);
                                }
                            });
                        });
                        console.log("amount without rejected after adding is : ", $scope.amountWithoutRejected);
                        $scope.grDetail.amountWithoutRejected = parseFloat($scope.amountWithoutRejectedDup) > 0 ? parseFloat($scope.amountWithoutRejectedDup) : 0;

                        if ($scope.grDetail.originalGrForUnitId != null) {
                            if ($scope.grDetail.originalGrForUnitId == appUtil.getCurrentUser().unitId) {
                                $scope.grDetail.updateRecievings = true;
                            }
                            else {
                                $scope.grDetail.updateRecievings = false;
                            }
                        }
                        $scope.settleGR();
                    }
                };

            $scope.validateChecks = function () {
                if ($scope.grDetail.transferOrderType == 'FIXED_ASSET_TRANSFER' && $scope.dayClosePending ){
                    var msg = "Fixed Asset Day Close Pending. Fixed Asset Transfers and Receiving are Disabled Until Fixed Asset Day Close is Completed.";
                    if($scope.blockMonthly){
                        msg = "Monthly " + msg;
                    }else if($scope.blockWeekly){
                        msg = "Weekly " + msg;
                    }else if($scope.blockDaily){
                        msg = "Daily " + msg;
                    }
                    $alertService.alert("Can't make Transfer !!", msg,
                     function () { }, true);
                    return;
                }
                $scope.getImageS3Links(function (){
                   
                    var isCommentRequired = false;
                    var isImageUploaded = false;
                    var isRejected = false;

                    $scope.grDetail.goodsReceivedItems.forEach(function (grItem) {
                       if($scope.grDetail.transferOrderType === 'FIXED_ASSET_TRANSFER' || $scope.grDetail.transferOrderType === 'BROKEN_ASSET_TRANSFER' || $scope.grDetail.transferOrderType === 'RENOVATION_ASSET_TRANSFER')
                      {  if(grItem.isScanned==false && (grItem.packagingDetails[0].hasRejection===undefined || grItem.packagingDetails[0].hasRejection===null ||grItem.packagingDetails[0].hasRejection===false )){
                           if(!isRejected){
                            $toastService.create("Please reject non-scanned assests !");
                            isRejected = true;
                            return;
                           }
                        }
                    }
                        grItem.packagingDetails.forEach(function (pkg) {
                            if (pkg.numberOfUnitsRejected > 0 && appUtil.isEmptyObject($scope.grDetail.comment)) {
                                if (!isCommentRequired) {
                                    $toastService.create("Please provide comment for rejected unit!");
                                    isCommentRequired = true;
                                }
                            }

                        if($scope.grDetail.transferOrderType === 'FIXED_ASSET_TRANSFER' || $scope.grDetail.transferOrderType === 'BROKEN_ASSET_TRANSFER' || $scope.grDetail.transferOrderType === 'RENOVATION_ASSET_TRANSFER'){
                            if(pkg.numberOfUnitsRejected > 0 && (pkg.rejectionReason === undefined || pkg.rejectionReason === null || pkg.rejectionReason.length === 0)){
                               if(!isRejected){
                                $toastService.create("Please provide reason for rejected units!");
                                isRejected = true;
                                return;
                               }
                                
                            }
                        }
                            

                            if ($scope.grDetail.specialOrder != undefined && $scope.grDetail.specialOrder != null && $scope.grDetail.specialOrder &&  pkg.numberOfUnitsRejected > 0) {
                                var name = "multi-por-images_" + grItem.id;
                                var htmlEle = document.getElementById(name);
                                var imagesArr = [];
                                imagesArr = htmlEle.files;
                                console.log(imagesArr);
                                console.log(imagesArr.length);
                                if (imagesArr.length <= 0) {
                                    $toastService.create("Please provide images for rejected unit!");
                                    isImageUploaded = true;
                                }
                            }
                        });
                    });
                    if (isCommentRequired) {
                        return;
                    }
                    if (isImageUploaded) {
                        return;
                    }
                    if(isRejected){
                        return;
                    }

                    var grPreviewModal = Popeye.openModal({
                        templateUrl: "grPreview.html",
                        controller: "grPreviewCtrl",
                        resolve: {
                            grDetail: function () {
                                return $scope.grDetail;
                            }
                        },
                        scope:$scope,
                        click: false,
                        keyboard: false
                    });
                    grPreviewModal.closed
                        .then(function (isSuccessful) {
                            if (isSuccessful) {
                                $scope.checkAssetOrGoods();
                            }
                        });
                });

                };

                $scope.validateAssetTagValue = function (assetTagValue, grItem) {
                    if (assetTagValue == null || assetTagValue.length != 6) {
                        grItem.assetVerified = false;
                        // grItem.associatedAssetId = null;
                        return;
                    }
                    if (assetTagValue == grItem.associatedAssetTagValue) {
                        grItem.assetVerified = true;
                    } else {
                        grItem.assetVerified = false;
                    }
                }

            $scope.setInitialUnitReceived = function () {
                $scope.grDetail.goodsReceivedItems.forEach(function (grItem) {
                    grItem.receivedQuantity = grItem.transferredQuantity;
                    grItem.packagingDetails.forEach(function (pkg) {
                        pkg.numberOfUnitsReceived = pkg.numberOfUnitsPacked;
                        pkg.receivedQuantity = pkg.transferredQuantity
                    });
                })
            };




                $scope.updateRejection = function (drilldowns, pkg, grItem ,isDrillDownRequired) {
                    pkg.unitsRejected = 0;
                    pkg.expiryDrillDown = [];
                    var i = 0;
                    for (i = 0; i < drilldowns.length; i++) {
                        var d = drilldowns[i];
                        if(!isDrillDownRequired){
                            d.rejection = 0;
                        }
                        pkg.unitsRejected = pkg.unitsRejected + d.rejection;
                        pkg.expiryDrillDown.push({
                            expiryDate: d.expiryDate,
                            keyId: d.keyId,
                            keyType: d.keyType,
                            price: d.price,
                            quantity: d.rejection
                        });
                    }
                    $scope.calculateReceivedQty(pkg, grItem);
                };

                $scope.isSemiFinished = function (grItem) {
                    return grItem.category == 'Semi Finished';
                };


                $scope.backbuttonClick = function () {
                    $scope.showPendingGr = true;
                    $scope.tIdCheck = null;
                    $scope.grEvent={};
                };

                $scope.calculateReceivedQty = function (pkg, grItem) {

                    if (pkg.unitsRejected < 0) {
                        $toastService.create("Negative values are not allowed here");
                        return;
                    }

                    if (appUtil.isFloat(pkg.unitsRejected) && pkg.packagingDefinitionData.conversionRatio > 1) {
                        $toastService.create("Please enter a correct value for rejecting the item.");
                        return;
                    }

                    if (appUtil.isFloat(pkg.unitsRejected) && (pkg.packagingDefinitionData.packagingType === "LOOSE" &&
                        (pkg.packagingDefinitionData.unitOfMeasure === "PC" || pkg.packagingDefinitionData.unitOfMeasure === "SACHET"))) {
                        $toastService.create("Please enter a correct value for rejecting the item.");
                        return;
                    }

                    var received = angular.copy(pkg.numberOfUnitsReceived);
                    pkg.numberOfUnitsReceived = pkg.numberOfUnitsPacked - pkg.unitsRejected;
                    if (pkg.numberOfUnitsReceived >= 0) {
                        pkg.receivedQuantity = pkg.numberOfUnitsReceived * pkg.packagingDefinitionData.conversionRatio;
                        pkg.numberOfUnitsRejected = angular.copy(pkg.unitsRejected);
                        $scope.updateItemPkg(grItem);
                    } else {
                        $toastService.create("You cannot reject more than you have received.");
                        pkg.numberOfUnitsReceived = received;
                    }
                };

                $scope.updateItemPkg = function (grItem) {
                    var qty = 0;
                    grItem.packagingDetails.forEach(function (item) {
                        qty += item.receivedQuantity;
                    });
                    grItem.receivedQuantity = qty;
                };

                function getGRDetail(grId) {
                    var promise = $http({
                        method: "GET",
                        url: apiJson.urls.goodsReceivedManagement.goodReceived + "?grId=" + grId
                    }).then(function success(response) {
                        $scope.grDetail = response.data;
                        $scope.setInitialUnitReceived();
                        console.log("response.data", response.data);
                        if($scope.grDetail.transferOrderType === 'FIXED_ASSET_TRANSFER' || $scope.grDetail.transferOrderType === 'BROKEN_ASSET_TRANSFER' || $scope.grDetail.transferOrderType === 'RENOVATION_ASSET_TRANSFER')
                         {
                          for(var i in $scope.grDetail.goodsReceivedItems){
                            $scope.grDetail.goodsReceivedItems[i].isScanned = false;
                            }
                        $scope.grDetail.faGrInitiated = false;
                        $http({
                            method:"GET",
                            url : apiJson.urls.goodsReceivedManagement.getGrEvent,
                            params : {grId: $scope.grDetail.id }
                        }).then(function success(res){
                            if(res.data !=null && res.data.faGrDataList!=null){
                               $scope.grEvent = res.data;
                                for(var i in $scope.grDetail.goodsReceivedItems){ 
                                    for(var j in res.data.faGrDataList){
                                        if($scope.grDetail.goodsReceivedItems[i].associatedAssetId === res.data.faGrDataList[j].assetId){
                                            res.data.faGrDataList[j].scannedQty>0 ? $scope.grDetail.goodsReceivedItems[i].isScanned = true : null;
                                        }     
                                    }
                                }
                                $scope.grDetail.faGrInitiated = true;
                                return response.data;   
                            }
                        }, function error(response){
                            console.log("error:" + response);
                        });
                    }
                       
                    }, function error(response) {
                        console.log("error:" + response);
                    });
            
                return promise;
             
                }

                $scope.getTotalCost = function (selectedQuantity, price) {
                    return (selectedQuantity * price).toFixed(6);
                };

                $scope.getAmountAndTax = function(total_amount,total_tax) {
                    return (total_amount + total_tax).toFixed(6);
                };

                $scope.openAcceptOptions = function ($event, grId) {
                    $scope.budgetDetails = {};
                    getGRDetail(grId).then(function (data) {
                        $scope.amountWithoutRejectedDup = 0;
                        if ($scope.grDetail.transferOrderType == 'FIXED_ASSET_TRANSFER'
                            || $scope.grDetail.transferOrderType == 'BROKEN_ASSET_TRANSFER'
                            || $scope.grDetail.transferOrderType == 'RENOVATION_ASSET_TRANSFER') {
                            $scope.grDetail.goodsReceivedItems.forEach(function (grItem) {
                                grItem.packagingDetails.forEach(function (pkg) {

                                    if (pkg.numberOfUnitsRejected == 0 || pkg.numberOfUnitsRejected == null || pkg.numberOfUnitsRejected == undefined) {
                                        console.log("Changes Logic");
                                        var unitPrice = grItem.negotiatedUnitPrice != null ? parseFloat(grItem.negotiatedUnitPrice) : 0;
                                        var calAmount = $scope.getTotalCost(grItem.transferredQuantity,unitPrice);
                                        console.log("cal amount is : ", calAmount);
                                        var taxAmount = 0;
                                        if (grItem.taxAmount != null) {
                                            taxAmount = parseFloat(grItem.taxAmount);
                                        }
                                        console.log("tax amount is : ", taxAmount);
                                        // var totalAmount = parseFloat(calAmount) + parseFloat(taxAmount);
                                        var totalAmount = $scope.getAmountAndTax(parseFloat(calAmount),parseFloat(taxAmount));
                                        console.log("total amount is : ", totalAmount);
                                        $scope.amountWithoutRejectedDup = parseFloat(parseFloat($scope.amountWithoutRejectedDup) + parseFloat(totalAmount)).toFixed(6);
                                        console.log("amnt with out rejected new is : ", $scope.amountWithoutRejectedDup);
                                    }
                                });
                            });
                        }
                        $scope.grDetail.amountWithoutRejected = parseFloat($scope.amountWithoutRejectedDup) > 0 ? parseFloat($scope.amountWithoutRejectedDup) : 0;
                        var mappingModal = Popeye.openModal({
                            templateUrl: "acceptOption.html",
                            controller: "acceptOptionCtrl",
                            resolve: {
                                grDetail: function () {
                                    return $scope.grDetail;
                                }
                            },
                            click: false,
                            keyboard: false
                        });

                        mappingModal.closed
                            .then(function (isSuccessful) {
                                if (isSuccessful) {
                                    $scope.showPendingGr = true;
                                    $scope.getPendingGRs();
                                }
                            });
                    });
                    $event.stopPropagation();
                };


                $scope.acceptDeclinedGoodsReceive = function ($event, grId) {
                    getGRDetail(grId).then(function (data) {
                        var currentUser = appUtil.getCurrentUser();
                        $scope.grDetail.receivedBy = appUtil.createGeneratedBy();
                        $http({
                            method: "PUT",
                            url: apiJson.urls.goodsReceivedManagement.acceptDeclinedGR,
                            data: $scope.grDetail
                        }).then(function success(response) {
                            if (response.data != null && response.data == true) {
                                $toastService.create("Declined Goods Receiving Accepted Successfully!");
                            } else {
                                $toastService.create("Something went wrong. Please try again!");
                            }
                            $scope.showPendingGr = true;
                            $scope.getPendingGRs();
                        }, function error(response) {
                            console.log("error:" + response);
                        });
                    });
                    $event.stopPropagation();
                };


                $scope.openDeclineOptions = function ($event, grId) {
                    getGRDetail(grId).then(function (data) {
                        var mappingModal = Popeye.openModal({
                            templateUrl: "declineOption.html",
                            controller: "declineOptionCtrl",
                            resolve: {
                                grDetail: function () {
                                    return $scope.grDetail;
                                }
                            },
                            click: false,
                            keyboard: false
                        });

                        mappingModal.closed
                            .then(function (isSuccessful) {
                                if (isSuccessful) {
                                    $scope.showPendingGr = true;
                                    $scope.getPendingGRs();
                                }
                            });

                    });
                    $event.stopPropagation();
                };

                // function verifyAssetId() {
                //     for (var i in $scope.grDetail.goodsReceivedItems) {
                //         var grItem = $scope.grDetail.goodsReceivedItems[i];
                //         console.log(grItem);
                //         if (grItem.packagingDetails[0].hasRejection != null
                //             && grItem.packagingDetails[0].hasRejection == true) {

                //         } else {
                //             if (grItem.assetVerified != true) {
                //                 var position = parseInt(i)+1;
                //                 $toastService.create("Please enter appropriate asset id for " + position + " GR Item");
                //                 return true;
                //             }
                //         }
                //     }
                //     return false;
                // }

                $scope.checkAssetOrGoods = function(){
                    $scope.grDetail.docIdsPorImages = $scope.porImagesUrl.join(",");
                    if ($scope.grDetail.transferOrderType == 'FIXED_ASSET_TRANSFER'
                        || $scope.grDetail.transferOrderType == 'BROKEN_ASSET_TRANSFER'
                        || $scope.grDetail.transferOrderType == 'RENOVATION_ASSET_TRANSFER') {
                        $scope.findReceivingAmount();
                    }
                    else{
                        $scope.settleGR();
                    }
                };

                $scope.settleGR = function () {
                    console.log($scope.grDetail);
                    $scope.grDetail.receivedBy = appUtil.createGeneratedBy();
                    if($scope.grEvent && $scope.grEvent.stockEventDefinitionData){
                        $scope.grDetail.eventId  = $scope.grEvent.stockEventDefinitionData.eventId;
                    }
                    $http({
                        method: "PUT",
                        url: apiJson.urls.goodsReceivedManagement.settleGR,
                        data: $scope.grDetail
                    }).then(function success(response) {
                        if (response.data != null && response.data == true) {
                            $toastService.create("Goods Receiving Settled Successfully!");
                            $scope.showPendingGr = true;
                            $scope.grEvent={};
                            $scope.getPendingGRs();
                        } else {
                            $http({
                                method: "GET",
                                url: apiJson.urls.goodsReceivedManagement.isGrSettled + "?grId=" + $scope.grDetail.id.toString()
                            }).then(function success(response) {
                                if (response.data == true) {
                                    $scope.grEvent={};
                                    $scope.showPendingGr = true;
                                    $scope.getPendingGRs();
                                    $toastService.create("GR for GR Id " + $scope.grDetail.id + " is already settled!");
                                } else {
                                    $toastService.create("Something went wrong. Please try again!");
                                }
                            })
                        }

                    }, function error(response) {
                        console.log("error:" + response);
                        if (response.data.errorMsg != null) {
                            $alertService.alert(response.data.errorTitle, response.data.errorMsg, null, true);
                        } else {
                            $toastService.create("Something went wrong. Please try again!");
                        }
                    });
                };

                $scope.cancelGR = function () {
                    $http({
                        method: "PUT",
                        url: apiJson.urls.goodsReceivedManagement.cancelGR,
                        data: $scope.grDetail.id
                    }).then(function success(response) {
                        if (response.data != null && response.data == true) {
                            $toastService.create("Goods Receiving Cancelled Successfully!");
                        } else {
                            $toastService.create("Something went wrong. Please try again!");
                        }
                        $scope.showPendingGr = true;
                        $scope.getPendingGRs();
                    }, function error(response) {
                        console.log("error:" + response);
                    });
                };

                $scope.enableRejectionInput = function (button, pkg ,grItem) {
            
                    if(($scope.grDetail.transferOrderType === 'FIXED_ASSET_TRANSFER' || $scope.grDetail.transferOrderType === 'BROKEN_ASSET_TRANSFER' || $scope.grDetail.transferOrderType === 'RENOVATION_ASSET_TRANSFER') || $scope.grDetail.faGrInitiated==true){
                        for(var i in grItem.drillDowns){
                            grItem.drillDowns[i].rejection = 1;
                            $scope.updateRejection(grItem.drillDowns,pkg,grItem,true);    
                        }

                        pkg.unitsRejected = 1;
                        $scope.calculateReceivedQty(pkg, grItem);
                        pkg.hasRejection = true;
                        return;
                    }

                    if (pkg.hasRejection != null && pkg.hasRejection == true) {
                        pkg.hasRejection = false
                        pkg.numberOfUnitsRejected = 0;
                        pkg.rejectionReason = null;
                        button.value = "Reject";
                        $scope.updateRejection(grItem.drillDowns,pkg,grItem,false);
                    } else {
                        pkg.hasRejection = true
                        button.value = "Cancel Reject"
                    }
                }

            }
        ]
    ).controller('grPreviewCtrl', ['$scope', 'grDetail','Popeye',
        function ($scope, grDetail,Popeye) {
          
                $scope.grDetail = grDetail;
                $scope.scannedItems = 0;
                $scope.rejectedItems = 0;

                if($scope.grDetail.transferOrderType === 'FIXED_ASSET_TRANSFER'
                || $scope.grDetail.transferOrderType === 'BROKEN_ASSET_TRANSFER' || $scope.grDetail.transferOrderType === 'RENOVATION_ASSET_TRANSFER'){
                    $scope.grDetail.goodsReceivedItems.forEach(function(grItem){
                        if(grItem.isScanned==true){
                            $scope.scannedItems++;
                            return;
                        }
                            $scope.rejectedItems++;
                    });
                }
                
            
            $scope.back = function () {
                Popeye.closeCurrentModal(false);
            };

            $scope.submit = function () {
                Popeye.closeCurrentModal(true);
            };


    }]).
controller('acceptOptionCtrl', ['$scope','$rootScope', 'grDetail', 'appUtil', '$toastService', 'apiJson', '$http', 'Popeye','$alertService','$fileUploadService','metaDataService','$window',
    function ($scope,$rootScope, grDetail, appUtil, $toastService, apiJson, $http, Popeye, $alertService, $fileUploadService,metaDataService,$window) {

            $scope.previewDocument = false;
            $scope.grDetail = grDetail;
            $scope.currentUnit = appUtil.getCurrentUser();
            $scope.isSuccessful = true;

            $scope.initiateGRRequest = function(){

                console.log($scope.grDetail);
                if(appUtil.getCurrentUser()!=null && appUtil.getCurrentUser().user!=null && appUtil.getCurrentUser().unitId!=null){
                     $http({
                    url: apiJson.urls.goodsReceivedManagement.initiateGREvent,
                    method: 'POST',
                    params:{
                        userId : appUtil.getCurrentUser().user.id,
                        unitId : appUtil.getCurrentUser().unitId
                    },
                    data: $scope.grDetail,
                }).then(function success(data){
                    console.log(data);
                    $scope.grDetail.faGrInitiated = true;
                },function error(response){
                    if(response.data.errorTitle && response.data.errorMsg){
                    $alertService.alert(response.data.errorTitle, response.data.errorMsg);
                      return;   
                }
                $alertService.alert("ERROR", "Something went wrong !");
                console.log(response.data);
                })
                }


            }

            $scope.refreshGrEvent = function(){
                $http({
                    method:"GET",
                    url : apiJson.urls.goodsReceivedManagement.getGrEvent,
                    params : {grId: $scope.grDetail.id }
                }).then(function success(response){
                    if(response.data!=null && response.data.faGrDataList!=null){
                        $scope.grEvent = response.data;
                        for(var i in $scope.grDetail.goodsReceivedItems){ 
                            for(var j in response.data.faGrDataList){
                                if($scope.grDetail.goodsReceivedItems[i].associatedAssetId === response.data.faGrDataList[j].assetId){
                                    if(response.data.faGrDataList[j].scannedQty>0){
                                        $scope.grDetail.goodsReceivedItems[i].isScanned = true;
                                    }
                                }     
                            }
                        }
                    }else{
                        $scope.grDetail.faGrInitiated = false;
                        for(var i in $scope.grDetail.goodsReceivedItems){
                            $scope.grDetail.goodsReceivedItems[i].isScanned = false;
                        }
                    }                  
                }, function error(response){
                    console.log("error:" + response);
                   $alertService.alert("ERROR","Something went wrong !");
                });
            }

            $scope.cancelGrEvent = function(grId){
                $alertService.confirm("Are you sure?","Cancel Gr Event !",function(result){
                    if(result){
                        $http({
                            method:"GET",
                            url : apiJson.urls.goodsReceivedManagement.cancelGrEvent,
                            params: {grId : grId}
                          }).then(function success(response){
                              if(response.data == true){
                                  $alertService.alert("Successfully Cancelled", "Fixed Assets GR Event is cancelled !");
                                  closeModal();
                              }
                          }, function error(response){
                              console.log("Error in gr event cancel : "+ response);
                              $alertService.alert(response.data.errorTitle, response.data.errorMsg, function () { }, true);
                          })
                    }
                });
                
            }

            $scope.rejectedGoodsReceivedFound = function () {
                if (appUtil.isEmptyObject($scope.grDetail.rejectGRComment)) {
                    $toastService.create("Please Provide reason!");
                    return;
                }

                if ($scope.grDetail.transferOrderType == 'FIXED_ASSET_TRANSFER'
                    || $scope.grDetail.transferOrderType == 'BROKEN_ASSET_TRANSFER'
                    || $scope.grDetail.transferOrderType == 'RENOVATION_ASSET_TRANSFER') {
                    if ($scope.grDetail.originalGrForUnitId != null) {
                        if ($scope.grDetail.originalGrForUnitId == $scope.currentUnit.unitId) {
                            $scope.grDetail.updateRecievings = true;
                        }
                        else {
                            $scope.grDetail.updateRecievings = false;
                        }
                        if($scope.grEvent && $scope.grEvent.stockEventDefinitionData){
                            $scope.grDetail.eventId  = $scope.grEvent.stockEventDefinitionData.eventId;
                        }
                    }

                }
                $scope.grDetail.receivedBy = appUtil.createGeneratedBy();
                $http({
                    method: "PUT",
                    url: apiJson.urls.goodsReceivedManagement.rejectedFoundGR,
                    data: $scope.grDetail
                }).then(function success(response) {
                    if (response.data != null && response.data == true) {
                        $toastService.create("Goods Receiving Settled Successfully!");
                    } else {
                        $toastService.create("Something went wrong. Please try again!");
                    }
                    closeModal();
                }, function error(response) {
                    $scope.isSuccessful = false;
                    closeModal();
                    if (response.data.errorMsg != null) {
                        $alertService.alert(response.data.errorTitle, response.data.errorMsg, null, true);
                    } else {
                        $toastService.create("Something went wrong. Please try again!");
                    }
                    console.log("error:" + response);
                });
            };

            $scope.uploadProofOfDelivery = function (grId) {
                            $fileUploadService.openFileModal("Upload Proof Of Delivery", "Find", function (file) {
                                if (file == null) {
                                    $toastService.create('File cannot be empty');
                                    return;
                                }
                                if(file.size > 5120000){
                                    $toastService.create('File size should not be greater than 5 MB.');
                                    return;
                                }
                                var fileExt = metaDataService.getFileExtension(file.name);
                                if (fileExt.toLowerCase() == 'pdf' || metaDataService.isImage(fileExt.toLowerCase())) {
                                    var mimeType = fileExt.toUpperCase();
                                    var fd = new FormData();
                                    fd.append('type', "OTHERS");
                                    fd.append('docType', "PROOF_OF_DELIVERY");
                                    fd.append('mimeType', fileExt.toUpperCase());
                                    fd.append('userId', appUtil.getCurrentUser().userId);
                                    fd.append('file', file);
                                    fd.append('grId',grId);
                                    $http({
                                        url: apiJson.urls.goodsReceivedManagement.uploadPOD,
                                        method: 'POST',
                                        data: fd,
                                        headers: {'Content-Type': undefined},
                                        transformRequest: angular.identity
                                    }).success(function (response) {
                                        $rootScope.showFullScreenLoader = false;
                                        if (response.fileUrl !== null) {
                                                    $scope.previewDocument = true;
                                                    $scope.fileUrl = response.fileUrl;
                                            $toastService.create("Upload successful");
                                        } else {
                                            $toastService.create("Upload failed");
                                        }
                                    }).error(function (response) {
                                        $rootScope.showFullScreenLoader = false;
                                        $toastService.create("Upload failed");
                                    });
                                } else {
                                    $toastService.create('Upload Failed , File Format not Supported');
                                }
                            });
                        };

            $scope.docPreview = function(){

            $window.open($scope.fileUrl,'_blank');
            }
            $scope.rejectedGoodsReceivedLost = function () {
                if (appUtil.isEmptyObject($scope.grDetail.rejectGRComment)) {
                    $toastService.create("Please Provide reason!");
                    return;
                }
                $scope.grDetail.receivedBy = appUtil.createGeneratedBy();
                $http({
                    method: "PUT",
                    url: apiJson.urls.goodsReceivedManagement.rejectedLostGR,
                    data: $scope.grDetail
                }).then(function success(response) {
                    if (response.data != null && response.data == true) {
                        $toastService.create("Goods Receiving Loss Registered Successfully!");
                    } else {
                        $toastService.create("Something went wrong. Please try again!");
                    }
                    closeModal();
                }, function error(response) {
                    $scope.isSuccessful = false;
                    console.log("error:" + response);
                    closeModal();
                });
            };

            function closeModal() {
                Popeye.closeCurrentModal($scope.isSuccessful);
            }

        }
    ]
).controller('declineOptionCtrl', ['$scope', 'grDetail', 'appUtil', '$toastService', 'apiJson', '$http', 'Popeye',
        function ($scope, grDetail, appUtil, $toastService, apiJson, $http, Popeye) {
            $scope.grDetail = grDetail;
            //  console.log("$scope.grDetail",$scope.grDetail);
            $scope.isSuccessful = true;

            $scope.rejectedGoodsReceivedDecline = function () {
                if (appUtil.isEmptyObject($scope.grDetail.rejectGRComment)) {
                    $toastService.create("Please Provide reason!");
                    return;
                }
                $scope.grDetail.cancelledBy = appUtil.createGeneratedBy();
                $http({
                    method: "PUT",
                    url: apiJson.urls.goodsReceivedManagement.declineGR,
                    data: $scope.grDetail
                }).then(function success(response) {
                    if (response.data != null && response.data == true) {
                        $toastService.create("Goods Receiving Declined Successfully!");
                    } else {
                        $toastService.create("Something went wrong. Please try again!");
                    }
                    closeModal();
                }, function error(response) {
                    $scope.isSuccessful = false;
                    closeModal();
                    console.log("error:" + response);
                });
            };

            $scope.cancel = function () {
                $scope.isSuccessful = false;
                closeModal();
            };

            function closeModal(value) {
                Popeye.closeCurrentModal($scope.isSuccessful);
            }

        }
    ]
);

