'use strict';
angular.module('scmApp').controller('approveInvoiceCtrl', ['$rootScope', '$stateParams', '$scope', 'apiJson', '$http', '$state',
    'appUtil', '$toastService', '$alertService', 'metaDataService', '$fileUploadService', '$window', 'previewModalService', '$timeout','Popeye',
    function ($rootScope, $stateParams, $scope, apiJson, $http, $state, appUtil,
              $toastService, $alertService, metaDataService, $fileUploadService, $window, previewModalService, $timeout,Popeye) {

        function getCreatedInvoices(view, startDate, endDate) {
            if (appUtil.isEmptyObject(startDate)) {
                $toastService.create("Please select a start date first");
                return;
            }
            if (appUtil.isEmptyObject(endDate)) {
                $toastService.create("Please select a end date first");
                return;
            }
            if($scope.raiseCreditNote){
                if (appUtil.isEmptyObject($scope.vendorSelected)) {
                    $toastService.create("Please select vendor");
                    return;
                }
            }
            var unitId = appUtil.isEmptyObject($scope.selectedUnit) ? $scope.currentUser.unitId : $scope.selectedUnit;
            var params = {
                sendingUnit: unitId,
                isView: view,
                startDate: startDate,
                endDate: endDate,
                raiseCreditNote : $scope.raiseCreditNote,
            };

            if (!appUtil.isEmptyObject($scope.vendorSelected)) {
                params["vendorId"] = $scope.vendorSelected.vendorId;
            }


            if (!appUtil.isEmptyObject($scope.locationSelected)) {
                params["dispatchId"] = $scope.locationSelected.dispatchId;
            }

            if (!appUtil.isEmptyObject($scope.selectedStatus)) {
                params["status"] = $scope.selectedStatus;
            }

            $http({
                method: "GET",
                url: apiJson.urls.invoiceManagement.getInvoices,
                params: params
            }).then(function (response) {
                if (appUtil.isEmptyObject(response)) {
                    $toastService.create("No Orders found!");
                } else {
                    $scope.invRequest = response.data;
                    $scope.invRequest = $scope.invRequest.sort(function (a, b) {
                        return b.id - a.id;
                    });
                    if($scope.raiseCreditNote){
                        $scope.invRequest.map(function(invr){
                            invr.checked = false;
                        })
                    }
                }
            }, function (err) {
                console.log("Encountered error at backend", err);
            });
        }

        $scope.selectAllInvoices = function (){
            $scope.allSelected = !$scope.allSelected;
            $scope.invRequest.map(function(invr){
                invr.checked = $scope.allSelected;
            })
        }

        $scope.raiseCN = function (){
            var invoiceList = [];
            $scope.invRequest.map(function (invr){
                if(invr.checked){
                    invoiceList.push(invr.id);
                }
            })
            if(invoiceList.length==0){
                $toastService.create("Please select some invoices");
                return;
            }
            $scope.openCNModal(invoiceList);

        }

        $scope.openCNModal = function (invoiceIds) {
            var CNModal = Popeye.openModal({
                templateUrl: "creditNoteModal.html",
                controller: "creditNoteModalCtrl",
                resolve: {
                    invoiceIds: function () {
                        return invoiceIds;
                    },
                    vendor: function (){
                        return $scope.vendorSelected;
                    }

                },
                modalClass: 'custom-modal',
                click: false,
                keyboard: false
            });
            CNModal.closed.then(function (result) {
                if (result) {
                    getCreatedInvoices($scope.showViewActions, $scope.startDate, $scope.endDate);
                }
            });
        }

        $scope.downloadExcell = function (){
            var params = {
                className : "com.stpl.tech.scm.domain.model.SalesPerformaInvoice"
            }
            var skipColumns = ['invoice','signedQrCode','ewayBill','deliveredDocumentUrl'];
            var jsonStrings = [];
            for(var i = 0;i<$scope.invRequest.length;i++){
                jsonStrings.push(JSON.stringify($scope.invRequest[i]));
            }
            metaDataService.downloadExcell(jsonStrings,params,skipColumns);
        }

        function createEmptyMultipartFile(fileName) {
          var emptyBuffer = new ArrayBuffer(0);
          var emptyFile = new Blob([emptyBuffer], { type: 'multipart/form-data' });
          emptyFile.name = fileName;
          return emptyFile;
        }


        function uploadEmptyFile(url, type, invoiceId, callback) {
            var fileExt = metaDataService.getFileExtension(invoiceId+".pdf");
            if (fileExt.toLowerCase() == 'pdf' || metaDataService.isImage(fileExt.toLowerCase())) {
                var mimeType = fileExt.toUpperCase();
                var fd = new FormData();
                fd.append('fileType', type);
                fd.append('mimeType', mimeType);
                fd.append('userId', $scope.currentUser.userId);
                fd.append('invoiceId', invoiceId);
                fd.append('file', createEmptyMultipartFile(invoiceId+".pdf"));
                $http({
                        url: url,
                        method: 'POST',
                        data: fd,
                        headers: {
                            'Content-Type': undefined
                        },
                        transformRequest: angular.identity
                }).success(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if (!appUtil.isEmptyObject(response)) {
                    $toastService.create("Upload successful");
                    callback(response);
                    } else {
                        $toastService.create("Upload failed");
                    }
                }).error(function (response) {
                    if (response.errorMsg != null) {
                        toastService.create(response.errorMsg);
                    }
                    $rootScope.showFullScreenLoader = false;
                    $toastService.create("Upload failed");
                    });
            }
        }

        function uploadFile(url, type, invoiceId, callback) {
            $fileUploadService.openFileModal("Upload Invoice Document", "Find", function (file) {
                if (file == null) {
                    $toastService.create('File cannot be empty');
                    return;
                }
                if (file.size > 2048000) {
                    $toastService.create('File size should not be greater than 2MB.');
                    return;
                }
                var fileExt = metaDataService.getFileExtension(file.name);
                if (fileExt.toLowerCase() == 'pdf' || metaDataService.isImage(fileExt.toLowerCase())) {
                    var mimeType = fileExt.toUpperCase();
                    var fd = new FormData();
                    fd.append('fileType', type);
                    fd.append('mimeType', mimeType);
                    fd.append('userId', $scope.currentUser.userId);
                    fd.append('invoiceId', invoiceId);
                    fd.append('file', file);
                    $http({
                        url: url,
                        method: 'POST',
                        data: fd,
                        headers: {
                            'Content-Type': undefined
                        },
                        transformRequest: angular.identity
                    }).success(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        if (!appUtil.isEmptyObject(response)) {
                            $toastService.create("Upload successful");
                            callback(response);
                        } else {
                            $toastService.create("Upload failed");
                        }
                    }).error(function (response) {
                        if (response.errorMsg != null) {
                            $toastService.create(response.errorMsg);
                        }
                        $rootScope.showFullScreenLoader = false;
                        $toastService.create("Upload failed");

                    });
                } else {
                    $toastService.create('Upload Failed , File Format not Supported');
                }
            });
        }


        $scope.selectJsonInvoice = function(jsonInvoice){
            $scope.calculatedDistance = null;
            $scope.selectedJsonInvoice = jsonInvoice;
        }

        $scope.resetDistance =  function (){
            $scope.calculatedDistance = null;
            $scope.selectedJsonInvoice = null;
        }

        $scope.selectInv = function(jsonInvoice,index){
            $scope.indexSelectedInv = index;
            $scope.dateOfDelAtCust = null;
            $scope.selectedInv = jsonInvoice;
            $scope.minDate = appUtil.convertToDateWithoutTime(jsonInvoice.createdAt);
            $scope.maxDate = appUtil.getCurrentBusinessDate();
        }

        $scope.resetDateOfDelivery =  function (){
            $scope.indexSelectedInv = null;
            $scope.dateOfDelAtCust = null;
            $scope.selectedInv = null;
            $scope.minDate = null;
            $scope.maxDate = null;
        }


        $scope.printChallan = function (invoice) {
            $scope.selectedInvoice = invoice;
            $scope.totalAmountInWords = appUtil.inWords(Math.round(invoice.totalAmount + invoice.additionalCharges));
            $scope.availableTaxes = 1;
            $timeout(function () {
                angular.element('#' + invoice.id + '_printDiv').trigger('click');
            });
        };

        $scope.updateCorrectedData = function (invoice,index) {
            $scope.openCorrectionDetailsModal(invoice);

        };

        $scope.openCorrectionDetailsModal = function (invoice) {
            var correctionDetailsModal = Popeye.openModal({
                templateUrl: "correctionDetailsModal.html",
                controller: "correctionDetailsModalCtrl",
                resolve: {
                    invoice: function () {
                        return invoice;
                    },
                },
                modalClass: 'custom-modal',
                click: false,
                keyboard: false
            });
            correctionDetailsModal.closed.then(function (res) {
                if(res){
                    getCreatedInvoices($scope.showViewActions, $scope.startDate, $scope.endDate);
                }
            });
        }

        $scope.viewCorrectionDetailsModal = function (invoice,action) {
            var viewCorrectionDetailsModal = Popeye.openModal({
                templateUrl: "viewCorrectionDetailsModal.html",
                controller: "viewCorrectionDetailsModalCtrl",
                resolve: {
                    invoice: function () {
                        return invoice;
                    },
                    action: function (){
                        return action;
                    }
                },
                modalClass: 'custom-modal',
                click: false,
                keyboard: false
            });
            viewCorrectionDetailsModal.closed.then(function (res) {
                if(res){
                    getCreatedInvoices($scope.showViewActions, $scope.startDate, $scope.endDate);
                }
            });
        }


        $scope.downloadExcel = function (invoiceId) {
            $http({
                url: apiJson.urls.invoiceManagement.downloadExcel,
                method: 'GET',
                params: {invoiceId: invoiceId},
                responseType: 'arraybuffer',
                headers: {
                    'Content-Type': undefined,
                    'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                },
                transformRequest: angular.identity
            }).success(function (response) {
                $rootScope.showFullScreenLoader = false;
                var fileName = "Performa_Invoice_Request_" + invoiceId + ".xlsx";
                var blob = new Blob([response],
                    {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;'}, fileName);
                saveAs(blob, fileName);
            }).error(function (response) {
                $rootScope.showFullScreenLoader = false;
                $toastService.create("Download failed");
            });
        };

        function sanitizeJson(buffer) {
            var json = JSON.parse(buffer);
            for (var key in Object.keys(json)) {
                if (typeof json[key] == "object") {
                    sanitizeJson(json[key]);
                } else {
                    if (json[key] == null) {
                        delete json[key];
                    }
                }
            }
            return json;
        }

        $scope.downloadJson = function (invoiceId) {
            $http({
                url: apiJson.urls.invoiceManagement.downloadJson,
                method: 'GET',
                params: {invoiceId: invoiceId},
                responseType: 'arraybuffer',
                headers: {
                    'Content-Type': undefined,
                    'Accept': 'application/json'
                },
                transformRequest: angular.identity
            }).success(function (response) {
                $rootScope.showFullScreenLoader = false;
                var fileName = "EWAY_JSON_INVOICE_" + invoiceId + ".json";
                var blob = new Blob([response], {type: 'application/json;'}, fileName);
                saveAs(blob, fileName);
                $scope.selectedJsonInvoice = null;
            }).error(function (response) {
                $rootScope.showFullScreenLoader = false;
                $toastService.create("Download failed");
                $scope.selectedJsonInvoice=null;
            });
        };

        $scope.downloadEportalJson = function (invoiceId) {
            if($scope.calculatedDistance==null || $scope.calculatedDistance==0){
                $toastService.create("Please Enter Calculated Distance From seller to Buyer!!");
                return;
            }
            $http({
                url: apiJson.urls.invoiceManagement.downloadEportalJson,
                method: 'GET',
                params: {invoiceId: invoiceId, calculatedDistance: $scope.calculatedDistance},
                responseType: 'arraybuffer',
                headers: {
                    'Content-Type': undefined,
                    'Accept': 'application/json'
                },
                transformRequest: angular.identity
            }).success(function (response) {
                $rootScope.showFullScreenLoader = false;
                var fileName = "EPORTAL_JSON_INVOICE_" + invoiceId + ".json";
                var blob = new Blob([response], {type: 'application/json;'}, fileName);
                saveAs(blob, fileName);
                $scope.calculatedDistance = null;
            }).error(function (response) {
                $rootScope.showFullScreenLoader = false;
                $toastService.create("Download failed");
                $scope.calculatedDistance = null;
            });
        };

        $scope.uploadEway = function (id, index) {
            uploadFile(apiJson.urls.invoiceManagement.uploadInvoice, "EWAY", id, function (response) {
                $scope.invRequest[index].ewayBill = response["invoice"];
                checkDispatchReady(JSON.parse(response['notification']));
            });
        };

        $scope.uploadDoc = function (invoiceId) {
                $fileUploadService.openFileModal("Upload Invoice Sheet", "Find", function (file) {
                    $scope.uploadInvoiceSheet(file, invoiceId);
                });
        };

        $scope.uploadInvoiceSheet = function (file, invoiceId) {
            var fd = new FormData(document.forms[0]);
            fd.append("file", file);
            fd.append("InvoiceId", invoiceId);
            $http({
                url: apiJson.urls.invoiceManagement.uploadInvoicesSheet,
                method: 'POST',
                data: fd,
                headers: {'Content-Type': undefined},
                transformRequest: angular.identity
            }).success(function (response) {
                $rootScope.showFullScreenLoader = false;
                if (response == true) {
                    $scope.uploadFlag = response;
                    $toastService.create("Excel Sheet is uploaded successfully!!.");
                    $scope.getInvoices();
                }
            }).error(function (response) {
                $rootScope.showFullScreenLoader = false;
                    $toastService.create("Error in uploaded sheet.");
            });
        };

        $scope.uploadInvoice = function (id, index) {
            uploadFile(apiJson.urls.invoiceManagement.uploadInvoice, "INVOICE", id, function (response) {
                $scope.invRequest[index].invoice = response["invoice"];
                checkDispatchReady(JSON.parse(response['notification']));
            });
        };

        $scope.uploadDeliveredDocument = function (invR, index) {
            if (invR.type == "B2B_RETURN") {
                uploadEmptyFile(apiJson.urls.invoiceManagement.uploadInvoice, "DELIVER", invR.id, function (response) {
                    $scope.invRequest[index].deliverdDocumentUrl = response["invoice"];
                    getCreatedInvoices($scope.showViewActions, $scope.startDate, $scope.endDate);
                });
            } else {
                uploadFile(apiJson.urls.invoiceManagement.uploadInvoice, "DELIVER", invR.id, function (response) {
                    $scope.invRequest[index].deliverdDocumentUrl = response["invoice"];
                    getCreatedInvoices($scope.showViewActions, $scope.startDate, $scope.endDate);
                });
            }
        };

        function checkDispatchReady(check) {
            if (check) {
                $alertService.alert("Notification Sent for Dispatch!!",
                    "Thanks! Performa Invoice is now available for Dispatch with E-way and Invoice",
                    function () {
                        getCreatedInvoices($scope.showViewActions, $scope.startDate, $scope.endDate);
                    });
            }
            else {
                console.log("not true")
            }
        }


        $scope.init = function () {
            $scope.unitData = appUtil.getUnitData();
            $scope.companyMap = appUtil.getCompanyMap();
            var currentDate = appUtil.getCurrentBusinessDate();
            if (!appUtil.isEmptyObject(currentDate)) {
                $scope.startDate = appUtil.formatDate(currentDate, "yyyy-MM-dd");
                $scope.endDate = $stateParams.createdInvoice != null
                    ? appUtil.formatDate($stateParams.createdInvoice.dispatchDate, "yyyy-MM-dd")
                    : $scope.startDate;
                $scope.showViewActions = $stateParams.viewInvoice;
                $scope.createdInvoice = $stateParams.createdInvoice;
                $scope.raiseCreditNote = $stateParams.raiseCreditNote;
                console.log("$scope.raiseCreditNote   ",$scope.raiseCreditNote);
                console.log("$scope.showViewActions   ",$scope.showViewActions);
                console.log("$scope.createdInvoice   ",$scope.createdInvoice);

                $scope.invRequest = [];
                $scope.currentUser = appUtil.getCurrentUser();
                $scope.selectedStatus = null;
                $scope.vendorSelected = null;
                $scope.selectedUnit = null;
                $scope.locationSelected = null;
                $scope.txnStatus = ["", "INITIATED", "PERFORMA_GENERATED", "PENDING_DISPATCH", "DELIVERED", "CLOSED", "CANCELLED"];
                $scope.txnTypes = ["", "SCRAP", "RETURN_TO_VENDOR", "B2B_SALES", "ECOM"];
                $scope.showPreview = previewModalService.showPreview;
                if($scope.raiseCreditNote){
                    $scope.allSelected = false;
                }

                metaDataService.getVendorsForUnit($scope.currentUser.unitId, function (vendorsForUnit) {
                    $scope.vendors = vendorsForUnit;
                });

                metaDataService.getSkuListForUnit($scope.currentUser.unitId, function (skuForUnitList) {
                    $scope.skus = skuForUnitList;
                });

                getUnitList(function (units) {
                    $scope.units = units;
                });

                getCreatedInvoices($scope.showViewActions, $scope.startDate, $scope.endDate);
            }
        };

        $scope.reset = function () {
            $scope.selectedSKU = null;
            $scope.vendorSelected = null;
        };

        $scope.selectVendor = function (vendor) {
            $scope.vendorSelected = vendor;

        };

        $scope.getInvoices = function () {
            getCreatedInvoices($scope.showViewActions, $scope.startDate, $scope.endDate);
        };

        function getUnitList() {
            $http({
                method: 'GET',
                url: apiJson.urls.unitMetadata.allUnitsList
            }).then(function success(response) {
                if (response.data != null) {
                    var units = response.data;
                    $scope.units = units.filter(function (unit) {
                        return (unit.category == "WAREHOUSE" || unit.category == "KITCHEN") && unit.status == "ACTIVE";
                    });
                }
            }, function error(response) {
                console.log("error:" + response);
            });
        }

        function openUrl(invoiceId, fileType) {
            $http({
                method: "GET",
                url: apiJson.urls.invoiceManagement.getUrl,
                params: {
                    invoiceId: invoiceId,
                    fileType: fileType
                }
            }).then(function (response) {
                if (!appUtil.isEmptyObject(response.data)) {
                    $window.open(response.data, '_blank');
                } else {
                    $toastService.create("Please check if the selected invoice is correct!!");
                }
            }, function (err) {
                console.log("Encountered error at backend", err);
            });
        }

        $scope.printEway = function (invR, index) {
            openUrl(invR.id, "EWAY");
        };

        $scope.printInvoice = function (invR) {
            openUrl(invR.id, "INVOICE");
        };

        $scope.printDeliverDoc = function (invR, index) {
            openUrl(invR.id, "DELIVER");
        }

        $scope.printPO = function (invR, index) {
            openUrl(invR.id, "PURCHASE_ORDER");
        }

        $scope.printInvCancelDoc = function (invR, index) {
            openUrl(invR.id, "CANCEL_INVOICE");
        }

        $scope.dispatch = function (invR, index) {
            if($scope.dateOfDelAtCust == null || $scope.dateOfDelAtCust == undefined){
                $toastService.create("Please Enter date of delivery at customer");
                return;
            }
            var dodDate = appUtil.formatDate($scope.dateOfDelAtCust, "yyyy-MM-dd");
            $http({
                method: "GET",
                url: apiJson.urls.invoiceManagement.dispatch,
                params: {
                    invoiceId: invR.id,
                    userId: $scope.currentUser.userId,
                    dateOfDelivery : dodDate
                }
            }).then(function (response) {
                if (!appUtil.isEmptyObject(response.data)) {
                    $scope.invRequest[index] = response.data;
                    invR = $scope.invRequest[index];
                    $scope.printInvoice(invR, index);
                    setTimeout(function () {
                        // $scope.printEway(invR, index);
                    }, 5000);
                } else {
                    $toastService.create("Error while trying to change the status of dispatch");
                }
            }, function (err) {
                if (err.data.errorMessage != null) {
                    $alertService.alert(err.data.errorType, err.data.errorMessage, null, true);
                }
                console.log("Encountered error at backend", err);
            });
        };



        $scope.approve = function (invR, index) {
            invR.createdBy = appUtil.createGeneratedBy();
            $http({
                method: "POST",
                url: apiJson.urls.invoiceManagement.approve,
                data: invR
            }).then(function (response) {
                if (!appUtil.isEmptyObject(response.data)) {
                    $scope.invRequest[index] = response.data;
                    invR = $scope.invRequest[index];
                } else {
                    $toastService.create("Error while trying to change the status of invoice request");
                }
            }, function (err) {
                console.log("Encountered error at backend", err);
            });
        };

        $scope.reject = function (invR, index) {
            invR.createdBy = appUtil.createGeneratedBy();
            $http({
                method: "POST",
                url: apiJson.urls.invoiceManagement.reject,
                params: {
                    invoiceId: invR.id,
                    userId: $scope.currentUser.userId
                }
            }).then(function (response) {
                if (!appUtil.isEmptyObject(response.data)) {
                    $scope.invRequest[index] = response.data;
                    invR = $scope.invRequest[index];
                } else {
                    $toastService.create("Error while trying to change the status of invoice request");
                }
            }, function (err) {
                console.log("Encountered error at backend", err);
            });
        };

        $scope.setReadyToDispatch = function (invR, index) {
            // if (invR.ewayBill == null) {
            //     if ((invR.totalAmount + invR.additionalCharges) >= 50000) {
            //         $toastService.create("Upload E-Invoiceway bill");
            //         return;
            //     }
            // }
            invR.createdBy = appUtil.createGeneratedBy();
            $http({
                method: "GET",
                url: apiJson.urls.invoiceManagement.readyToDispatch,
                params: {
                    invoiceId: invR.id,
                    userId: $scope.currentUser.userId
                }
            }).then(function (response) {
                if (!appUtil.isEmptyObject(response.data)) {
                    $scope.invRequest[index] = response.data;
                    invR = $scope.invRequest[index];
                } else {
                    $toastService.create("Error while trying to change the status of invoice request");
                }
            }, function (err) {
                if (err.data.errorMsg != null) {
                    $toastService.create(err.data.errorMsg);
                }
                console.log("Encountered error at backend", err);
            });
        };

        $scope.uploadDocInvoice = function (invoice, index) {
            $scope.updatedDocInvoice = invoice;
        };

        $scope.updateDocumentNumber = function(UploadedDocumentNumber){
            if (appUtil.isEmptyObject(UploadedDocumentNumber) || (UploadedDocumentNumber == null || UploadedDocumentNumber == undefined)) {
                $toastService.create("Please... Add Uploaded Document Number!");
                return;
            }
            else {
                var obj = {
                    invoiceId: $scope.updatedDocInvoice.id,
                    uploadDocId: UploadedDocumentNumber
                }
                $http({
                    method: "POST",
                    url: apiJson.urls.invoiceManagement.updateUploadDocumentID,
                    params: obj,
                }).then(function (response) {
                    $toastService.create("Uploaded Document Number updated successfully.");
                    $scope.getInvoices();
                }, function (error) {
                    $toastService.create("Could not save Document number");
                });
            }
            $scope.UploadedDocumentNumber = null;
        }
        $scope.generateB2BInvoice = function (invoice, index) {
            if(invoice.irnNo == null){
                $toastService("Please Upload Excel First");
                return;
            }
            //TODO generate invoice file and then send it to backend
            $scope.selectedInvoice = invoice;
            // if (invoice.ewayBill == null) {
            //     if ((invoice.totalAmount + invoice.additionalCharges) >= 50000) {
            //         $toastService.create("Upload Eway bill");
            //         return;
            //     }
            // }
            $scope.totalAmountInWords = appUtil.inWords(Math.round(invoice.totalAmount + invoice.additionalCharges));
            $scope.availableTaxes = 1;
            // $timeout(function() {
            //     angular.element('#'+invoice.id+'_printB2BInvoice').trigger('click');
            //     /*html2canvas(document.getElementById('printSection'), {
            //         onrendered: function (canvas) {
            //             var data = canvas.toDataURL();
            //             var docDefinition = {
            //                 content: [{
            //                     image: data,
            //                     width: 500,
            //                 }]
            //             };
            //             var data = pdfMake.createPdf(docDefinition);
            //             pdfMake.createPdf(docDefinition).download("test.pdf");
            //         }
            //     });*/
            // });

            $http({
                method: "POST",
                url: apiJson.urls.invoiceManagement.generateAndSaveB2bInvoice,
                params: {
                    invoiceId: invoice.id,
                    userId: $scope.currentUser.userId
                }
            }).then(function (response) {
                if (!appUtil.isEmptyObject(response.data)) {
                    console.log("b2b response")
                    $toastService.create("Invoice uploaded");
                    // $scope.invRequest[index].invoice = response["invoice"];
                    // $scope.notification=response['notification'];
                    // console.log($scope.notification);
                    // checkDispatchReady(JSON.parse(response["notification"]));
                    getCreatedInvoices($scope.showViewActions, $scope.startDate, $scope.endDate);
                }
            }, function (err) {
                if (err.data.errorMsg != null) {
                    $toastService.create(err.data.errorMsg);
                }
                console.log("Encountered error at backend", err);
            });
        };

        $scope.uploadCancelInvoiceDoc = function () {
            $fileUploadService.openFileModal("Upload Cancel Invoice Document", "Find", function (file) {
                if (file == null) {
                    $toastService.create('File cannot be empty');
                    return;
                }
                if(file.size > 307200){
                    $toastService.create('File size should not be greater than 300 kb.');
                    return;
                }
                var fileExt = metaDataService.getFileExtension(file.name);
                if (fileExt.toLowerCase() == 'pdf' || metaDataService.isImage(fileExt.toLowerCase())) {
                    var mimeType = fileExt.toUpperCase();
                    var fd = new FormData();
                    fd.append('type', "OTHERS");
                    fd.append('docType', "CANCEL_INVOICE");
                    fd.append('mimeType', fileExt.toUpperCase());
                    fd.append('userId', appUtil.getCurrentUser().userId);
                    fd.append('file', file);
                    $http({
                        url: apiJson.urls.invoiceManagement.uploadCancelInvoice,
                        method: 'POST',
                        data: fd,
                        headers: {'Content-Type': undefined},
                        transformRequest: angular.identity
                    }).success(function (response) {
                        if (!appUtil.isEmptyObject(response)) {
                            $toastService.create("Upload successful");
                            $scope.uploadedDocData = response;
                        } else {
                            $toastService.create("Upload failed");
                        }
                    }).error(function (response) {
                        $rootScope.showFullScreenLoader = false;
                        $toastService.create("Upload failed");
                    });
                } else {
                    $toastService.create('Upload Failed , File Format not Supported');
                }
                /*metaDataService.uploadFile("OTHERS","PAYMENT_REQUEST_INVOICE",file, function(doc){
                    $scope.uploadedDocData = doc;
                });*/
            });
        };

        $scope.downloadCancelInvoice = function (prInvoice) {
            metaDataService.downloadDocument(prInvoice);
        };

        $scope.cancelUploadDocument = function (invId, Index) {
                $scope.cancelInvoiceId = invId;
                $scope.cancelInvoiceIndex = Index;
        }
        $scope.cancelPreview = function (invId, Index) {
            $alertService.confirm("Again Are you sure?", "You are going to cancel this Invoice", function (result) {
                if (result) {
                    if($scope.uploadedDocData == null){
                        $toastService.create("Please Upload Cancel Document First!");
                        return;
                    }
                    $scope.cancelInvoice(invId, Index);
                } else {
                    $scope.uploadedDocData = null;
                    return;
                }
            });
        }

        $scope.closeModal = function(){
            $scope.uploadedDocData = null;
        }

        $scope.approveCancellation = function (invId, index , status) {
            $http({
                method: "GET",
                url: apiJson.urls.invoiceManagement.approveCancellation,
                params: {
                    invoiceId: invId,
                    userId: $scope.currentUser.userId,
                    docId: $scope.uploadedDocData != null ? $scope.uploadedDocData.documentId : null,
                    status : status
                }
            }).then(function (response) {
                if (!appUtil.isEmptyObject(response.data)) {
                    $scope.invRequest[index] = response.data;
                    $scope.uploadedDocData = null;
                    $toastService.create("Your cancellation Request for invoice ",invId," went for approval");
                } else {
                    $scope.uploadedDocData = null;
                    $toastService.create("Error while trying to approve cancellation request");
                }
            }, function (err) {
                console.log("Encountered error at backend", err);
            });
        };

            $scope.cancelInvoice = function (invId, index) {
            $http({
                method: "GET",
                url: apiJson.urls.invoiceManagement.cancel,
                params: {
                    invoiceId: invId,
                    userId: $scope.currentUser.userId,
                    docId: $scope.uploadedDocData != null ? $scope.uploadedDocData.documentId : null
                }
            }).then(function (response) {
                if (!appUtil.isEmptyObject(response.data)) {
                    $scope.invRequest[index] = response.data;
                    $scope.uploadedDocData = null;
                } else {
                    $scope.uploadedDocData = null;
                    $toastService.create("Error while trying to change the status of dispatch");
                }
            }, function (err) {
                console.log("Encountered error at backend", err);
            });
        };

    }
]).controller('correctionDetailsModalCtrl', ['$scope', 'appUtil', '$toastService', 'apiJson', '$http', 'Popeye', '$window', '$alertService','invoice',
        function ($scope, appUtil, $toastService, apiJson, $http, Popeye, $window,$alertService,invoice) {

            $scope.init = function () {
                $scope.invoiceDetails = invoice;
                $scope.invoiceDetails.totalCorrectedSellingCost = $scope.invoiceDetails.totalSellingCost;
                $scope.invoiceDetails.totalCorrectedTax = $scope.invoiceDetails.totalTax;
                $scope.invoiceDetails.totalAmount = parseFloat(($scope.invoiceDetails.totalSellingCost + $scope.invoiceDetails.totalTax).toFixed(6));
                $scope.invoiceDetails.totalCorrectedAmount = parseFloat(($scope.invoiceDetails.totalCorrectedSellingCost + $scope.invoiceDetails.totalCorrectedTax).toFixed(6));
                $scope.invoiceDetails.items.map(function (item){
                    item.correctedTax = item.totalTax;
                    item.correctedSellPrice = item.sellPrice;
                })
                $scope.priceEdited = false;
                console.log("invoice items ",$scope.invoiceDetails);
            }

            $scope.setPriceEdited = function (val){
                $scope.priceEdited = val;
            }

            $scope.updateDetails = function () {

                var totalCorrectedAmount = 0;
                var totalCorrectedSellingCost = 0;
                var totalCorrectedTax = 0;

                $scope.invoiceDetails.items.map(function (item) {
                    var taxTotalPercentForItem = 0;

                    if (item.taxes != undefined && item.taxes != null && item.taxes.length > 0) {
                        item.taxes.map(function (tax) {
                            taxTotalPercentForItem += tax.percent;
                        })
                    }
                    item.correctedTax = parseFloat((item.pkgQty * item.correctedSellPrice * taxTotalPercentForItem * 0.01).toFixed(6));
                    item.correctedSellAmount = parseFloat((item.pkgQty * item.correctedSellPrice).toFixed(6));
                    totalCorrectedSellingCost += item.correctedSellAmount;
                    totalCorrectedTax += item.correctedTax;
                    totalCorrectedAmount += item.correctedTax + item.correctedSellAmount;
                });
                $scope.invoiceDetails.totalCorrectedSellingCost = parseFloat(totalCorrectedSellingCost.toFixed(6));
                $scope.invoiceDetails.totalCorrectedTax = parseFloat(totalCorrectedTax.toFixed(6));
                $scope.invoiceDetails.totalCorrectedAmount = parseFloat((totalCorrectedAmount).toFixed(6));
                $scope.setPriceEdited(false);

            };

            $scope.saveInvoiceDetails = function () {

                var currentUser = appUtil.getCurrentUser();
                var params = {
                    userId : currentUser.userId,
                };
                $http({
                    method: "POST",
                    url: apiJson.urls.invoiceManagement.saveCorrectedInvoiceDetails,
                    data : $scope.invoiceDetails,
                    params : params
                }).then(function (response) {
                    if (appUtil.isEmptyObject(response)) {
                        $toastService.create("Error while saving corrected details");
                    } else {
                        $toastService.create("Corrected details saved");
                        $scope.closeModal(true);
                    }
                }, function (err) {
                    console.log("Encountered error at backend", err);
                });
            }

            $scope.closeModal = function (res) {
                Popeye.closeCurrentModal(res);
            };

        }
    ]).controller('viewCorrectionDetailsModalCtrl', ['$scope', 'appUtil', '$toastService', 'apiJson', '$http', 'Popeye', '$window', '$alertService','invoice','action',
        function ($scope, appUtil, $toastService, apiJson, $http, Popeye, $window,$alertService,invoice,action) {

            $scope.init = function () {
                $scope.invoice = invoice;
                $scope.action = action;
                $scope.currentUser = appUtil.getCurrentUser();
                $scope.getCorrectedDetails();
                $scope.invoiceDetails = null;

            }

            $scope.approve = function () {
                $scope.invoice.createdBy = appUtil.createGeneratedBy();
                $http({
                    method: "POST",
                    url: apiJson.urls.invoiceManagement.approve,
                    data: $scope.invoice
                }).then(function (response) {
                    if (!appUtil.isEmptyObject(response.data)) {
                        $scope.closeModal(true);
                    } else {
                        $toastService.create("Error while trying to change the status of invoice request");
                    }
                }, function (err) {
                    console.log("Encountered error at backend", err);
                });
            };

            $scope.reject = function () {
                $scope.invoice.createdBy = appUtil.createGeneratedBy();
                $http({
                    method: "POST",
                    url: apiJson.urls.invoiceManagement.reject,
                    params: {
                        invoiceId: $scope.invoice.id,
                        userId: $scope.currentUser.userId
                    }
                }).then(function (response) {
                    if (!appUtil.isEmptyObject(response.data)) {
                        $scope.closeModal(true);
                    } else {
                        $toastService.create("Error while trying to change the status of invoice request");
                    }
                }, function (err) {
                    console.log("Encountered error at backend", err);
                });
            };

            $scope.getCorrectedDetails = function () {
                var reqObj={
                    invoiceId : $scope.invoice.id,
                };
                $http({
                    method: "GET",
                    url: apiJson.urls.invoiceManagement.getCorrectedInvoiceDetails,
                    params : reqObj
                }).then(function (response) {
                    if (!appUtil.isEmptyObject(response)) {
                        $scope.invoiceDetails = response.data;
                        console.log("invoice",response.data);

                    }
                }, function (err) {
                    console.log("Encountered error at backend", err);
                });

            };

            $scope.closeModal = function (res) {
                Popeye.closeCurrentModal(res);
            };

        }
    ]).controller('creditNoteModalCtrl', ['$scope', 'appUtil', '$toastService', 'apiJson', '$http', 'Popeye', '$window', '$alertService','invoiceIds','vendor','metaDataService','$fileUploadService',
        function ($scope, appUtil, $toastService, apiJson, $http, Popeye, $window,$alertService,invoiceIds,vendor,metaDataService,$fileUploadService) {

            $scope.init = function () {
                $scope.invoiceIds = invoiceIds;
                $scope.vendor = vendor;
                $scope.creditNoteDetails = [];
                $scope.lastItemId = 1;
                $scope.vendorInvoiceNumber = null;
                $scope.invoiceDate = null;
                $scope.uploadedDocData = null;
                $scope.docId = null;

            }

            $scope.addCreditNoteDetail = function () {

                if ($scope.creditNoteDetails.length > 0) {
                    $scope.lastItemId = $scope.creditNoteDetails[$scope.creditNoteDetails.length-1].itemId + 1;
                } else {
                    $scope.lastItemId = 1;
                }
                var newField = {
                    itemId: $scope.lastItemId,
                    itemDesc: "",
                    qty: 0,
                    price: 0,
                    taxAmount: 0,

                }
                $scope.creditNoteDetails.push(newField);

            }

            $scope.saveDebitCreditNoteDetails = function () {

                if ($scope.creditNoteDetails.length < 1) {
                    $toastService.create("Field should be more than 0");
                    return;
                }
                if (appUtil.isEmptyObject($scope.vendorInvoiceNumber)) {
                    $toastService.create("Please Enter vendor invoice number");
                    return;
                }
                if (appUtil.isEmptyObject($scope.docId)) {
                    $toastService.create("Please upload vendor Id");
                    return;
                }
                if (appUtil.isEmptyObject($scope.invoiceDate)) {
                    $toastService.create("Please enter invoice date");
                    return;
                }
                var currentUser = appUtil.getCurrentUser();

                var reqObj={
                    invoiceId : $scope.invoiceIds.toString(),
                    vendorId : $scope.vendor.vendorId,
                    createdBy : currentUser.userId,
                    itemDetails : $scope.creditNoteDetails,
                    invoiceDocUrl :$scope.uploadedDocData.fileUrl ,
                    invoiceDate : $scope.invoiceDate,
                    vendorInvoiceNumber : $scope.vendorInvoiceNumber,
                };

                $http({
                    method: "POST",
                    url: apiJson.urls.invoiceManagement.saveCreditDebitNoteDetails,
                    data : reqObj,
                }).then(function (response) {
                    if (appUtil.isEmptyObject(response)) {
                        $toastService.create("Error while saving credit debit note details");
                    } else {
                        $toastService.create("Credit Debit Note created");
                        $scope.closeModal(true);
                    }
                }, function (err) {
                    console.log("Encountered error at backend", err);
                });
            }

            $scope.removeField = function (id){
                $scope.creditNoteDetailsNew = [];
                $scope.creditNoteDetails.map(function (detail){
                    if(detail.itemId != id && detail.itemId<id){
                        $scope.creditNoteDetailsNew.push(detail);
                    }else if(detail.itemId != id && detail.itemId>id){
                        detail.itemId = detail.itemId -1;
                        $scope.creditNoteDetailsNew.push(detail);
                    }
                })
                $scope.creditNoteDetails = $scope.creditNoteDetailsNew;
            }

            $scope.downloadInvoice = function () {
                metaDataService.downloadDocument($scope.uploadedDocData);
            };

            $scope.uploadInv = function () {

                $fileUploadService.openFileModal("Upload Invoice Document", "Find", function (file) {
                    if (file == null) {
                        $toastService.create('File cannot be empty');
                        return;
                    }

                    var fileExt = metaDataService.getFileExtension(file.name);
                    if (fileExt.toLowerCase() == 'pdf' || metaDataService.isImage(fileExt.toLowerCase())) {
                        var mimeType = fileExt.toUpperCase();
                        var fd = new FormData();
                        fd.append('userId', appUtil.getCurrentUser().userId);
                        fd.append('mimeType', fileExt.toUpperCase());
                        fd.append('file', file);
                        $http({
                            url: apiJson.urls.invoiceManagement.uploadVendorInvoice,
                            method: 'POST',
                            data: fd,
                            headers: {'Content-Type': undefined},
                            transformRequest: angular.identity
                        }).success(function (response) {
                            if (!appUtil.isEmptyObject(response)) {
                                $toastService.create("Upload successful");
                                $scope.uploadedDocData = response;
                                $scope.docId= response.documentId;
                            } else {
                                $toastService.create("Upload failed");
                            }

                        }).error(function (response) {
                            $toastService.create("Upload failed");
                        });
                    } else {
                        $toastService.create('Upload Failed , File Format not Supported');
                    }

                });
            };


            $scope.closeModal = function (res) {
                Popeye.closeCurrentModal(res);
            };

        }
    ]
)
