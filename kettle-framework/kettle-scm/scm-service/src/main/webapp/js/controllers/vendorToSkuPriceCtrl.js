angular.module('scmApp').controller(
    'vendorToSkuPriceCtrl',
    [
        '$rootScope',
        '$scope',
        '$interval',
        'apiJson',
        '$http',
        'appUtil',
        '$toastService',
        '$timeout',
        'previewModalService',
        'Popeye',
        function ($rootScope, $scope, $interval, apiJson, $http, appUtil, $toastService, $timeout, previewModalService,Popeye) {

            $scope.init = function () {
                $scope.createSearchTypeList();
                $scope.getAllSkus();
                $scope.getAllVendors();
                $scope.getAllDeliveryLocations();
                $scope.priceGridOptions = $scope.locationGridOptions();
                $scope.showPreview = previewModalService.showPreview;
                $scope.updatedPriceRequest = new Map();
                $scope.showPricing();
                $scope.updatedPriceRequest=[];
            };

            $scope.createSearchTypeList = function () {
//            {
//                                type: 'SKU',
//                                name: '<PERSON>U'
//                            },
                $scope.searchTypeList = [{
                    type: 'VENDOR',
                    name: 'Vendor'
                }];
            };

            $scope.showValues = function () {
                if ($scope.searchType == undefined || $scope.searchType == null) {
                    return;
                }
                $timeout(function () {
                    $('#selectSkuForPriceUpdateId').val(null).trigger('change');
                });
                $scope.priceGridOptions.data = null;
                if ($scope.isSKUValue()) {
                    $scope.searchValues = $scope.allSkuDataList;
                } else {
                    $scope.searchValues = $scope.allVendorDataList
                }
            };

            $scope.getAllSkus = function () {
                $http({
                    method: "GET",
                    dataType: 'json',
                    data: '',
                    headers: {
                        "Content-Type": "application/json"
                    },
                    url: apiJson.urls.skuMapping.getAllSku
                }).then(function success(response) {
                    $scope.allSkuDataList = response.data;
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            $scope.getVendorForSku = function (skuId) {
                $scope.vendorDataList = [];
                $http({
                    method: "POST",
                    dataType: 'json',
                    data: skuId,
                    headers: {
                        "Content-Type": "application/json"
                    },
                    url: apiJson.urls.skuMapping.getVendorForSku
                }).then(function success(response) {
                    $scope.vendorDataList = response.data;
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            $scope.getSkuForVendor = function (vendorId) {
                $scope.skuDataList = [];
                $http({
                    method: "POST",
                    dataType: 'json',
                    data: vendorId,
                    headers: {
                        "Content-Type": "application/json"
                    },
                    url: apiJson.urls.skuMapping.getSkuForVendor
                }).then(function success(response) {
                    $scope.skuDataList = response.data;
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            $scope.getAllVendors = function (skuId) {
                $http({
                    method: "GET",
                    dataType: 'json',
                    data: '',
                    headers: {
                        "Content-Type": "application/json"
                    },
                    url: apiJson.urls.skuMapping.getAllVendors,
                }).then(function success(response) {
                    $scope.allVendorDataList = response.data.filter(function(item){ return item.category === 'VENDOR'});
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            $scope.getAllDeliveryLocations = function () {
                $http({
                    method: "GET",
                    dataType: 'json',
                    data: '',
                    headers: {
                        "Content-Type": "application/json"
                    },
                    url: apiJson.urls.scmMetadata.activeDeliveryLocations,
                }).then(function success(response) {
                    $scope.allDeliveyLocationList = response.data;
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            $scope.showPricing = function () {
                $scope.updatedPriceRequest=new Map();
                if ($scope.sourceValue == undefined || $scope.sourceValue == null
                    || $scope.deliveryLocation == undefined || $scope.deliveryLocation == null) {
                    return;
                }
                if ($scope.isSKUValue()) {
                    $scope.getPricingBySku();
                } else {
                    $scope.getPricingByVendor();
                }
            };

            $scope.isSKUValue = function () {
                return $scope.searchType != null && $scope.searchType.type === 'SKU';
            };

            $scope.getPricingBySku = function () {
                var payload = {
                    skuId: $scope.sourceValue.id,
                    location: $scope.deliveryLocation.code
                };
                $http({
                    url: apiJson.urls.skuMapping.searchSkuPrice,
                    method: 'POST',
                    data: payload
                }).then(function (response) {
                    $scope.priceGridOptions.data = response.data;
                }, function (response) {
                    console.log("error", response);
                });
            };

            $scope.getPricingByVendor = function () {
                var payload = {
                    vendorId: $scope.sourceValue.id,
                    location: $scope.deliveryLocation.code
                };
                $http({
                    url: apiJson.urls.skuMapping.searchVendorToSkuPrice,
                    method: 'POST',
                    data: payload
                }).then(function (response) {
                    $scope.priceGridOptions.data = response.data;
                }, function (response) {
                    console.log("error", response);
                });
            };

            $scope.locationGridOptions = function () {
                return {
                    enableColumnMenus: false,
                    enableFiltering: true,
                    enableCellEditOnFocus: true,
                    enableColumnResizing: true,
                    cellEditableCondition: function ($scope) {
                        return $scope.row.entity.status === 'ACTIVE';
                    },
                    columnDefs: [{
                        field: 'vendor.name',
                        displayName: 'Vendor',
                        enableCellEdit: false
                    }, {
                        field: 'sku.name',
                        displayName: 'SKU',
                        cellTemplate: 'skuIdTemplate.html',
                        enableCellEdit: false
                    }, {
                        field: 'dispatch.name',
                        displayName: 'Dispatch Location',
                        enableCellEdit: false
                    }, {
                        field: 'pkg.name',
                        displayName: 'Packaging',
                        enableCellEdit: false
                    }, {
                        field: 'pkg.uom',
                        displayName: 'UOM',
                        enableCellEdit: false
                    }, {
                        field: 'current.date',
                        displayName: 'Start Date',
                        type: 'date',
                        cellFilter: 'date: \'yyyy-MM-dd\'',
                        enableCellEdit: false
                    }, {
                        field: 'status',
                        displayName: 'Status',
                        enableCellEdit: false,
                        cellTemplate: 'statusBatch.html'
                    }, {
                        field: 'current.value',
                        displayName: 'Negotiated Price',
                        enableCellEdit: false
                    }, {
                        field: 'updated.value',
                        displayName: 'Updated Price',
                        type: 'number',
                        enableCellEdit: true
                    }, {
                        field: 'action',
                        displayName: 'Action',
                        cellTemplate: 'statusChangeButton.html',
                        enableCellEdit: false
                    }],
                    onRegisterApi: function (gridApi) {
                        $scope.gridApi = gridApi;
                        gridApi.edit.on.afterCellEdit($scope, function (rowEntity, colDef, newValue, oldValue) {
                            console.log(newValue);
                            // to display update
                            if (colDef.field=='leadTime'.toString()) {
                                rowEntity.update = false;
                            }
                            else
                            {
                                rowEntity.update = true;
                            }
                            console.log(JSON.stringify(rowEntity));
                            $scope.$apply();
                        });
                    }
                };
            };

            $scope.changeStatus = function (value) {
                return;
            };

            $scope.updateLeadTime = function(value)
            {
                if (value.leadTime == null || value.LeadTime == 0) {
                    var msg = "Please fill update lead time"
                    $toastService.create(msg);
                    return;
                }
                var payload={
                    id:value.keyId,
                    index:value.leadTime
                }
                $http({
                    url: apiJson.urls.skuMapping.updateSkuLeadTime,
                    method: 'POST',
                    data: payload
                }).then(function (response) {
                    if (response.data) {
                        $toastService.create("Successfully updated lead time");
                    }
                }, function (response) {
                    console.log("error", response);
                });
                console.log(value);
            }

            $scope.updateRow = function (value) {
                if (value.updated.value  <= 0 || value.updated == null || value.updated.value == null) {
                    var msg = "Error in Metadata";
                    $toastService.create(msg);
                    value.updated =null;
                    return;
                }
                value.isUpdated=true;
                $scope.updatedPriceRequest.set(value.keyId,value);
                return;
            };

            $scope.cancelRow = function (value) {
                $scope.updatedPriceRequest.delete(value.keyId);
                value.updated=null;
                value.isUpdated=false;
                return;
            };

            $scope.previewUpdationModel = function () {
                var mappingModal = Popeye.openModal({
                    templateUrl : "vendorToSkuPriceUpdateModal.html",
                    controller : "vendorToSkuPriceUpdateModalCtrl",
                    resolve : {
                        data : function() {
                            return $scope.updatedPriceRequest;
                        },
                        vendor : function() {
                            return $scope.sourceValue.name;
                        }
                    },
                    click : false,
                    keyboard : false
                });
                mappingModal.closed.then(function (result) {
                    if (result) {
                        $scope.showPricing();
                        $scope.updatedPriceRequest=new Map();
                    }
                });
            }



            $scope.isEntryFoundForUpdation = function () {
                return $scope.updatedPriceRequest.size>0;
            }

            $scope.updatePriceGridRow = function (value) {
                var x = null;
                var id = null;
                for (x in $scope.priceGridOptions.data) {
                    if ($scope.priceGridOptions.data[x].keyId == value.keyId) {
                        id = x;
                    }
                }
                if (id != null) {
                    $scope.priceGridOptions.data[id] = value;
                }
            };

            // Modal Methods
            $scope.getDataForAddModal = function () {
                $scope.selectSkuPackaging = null;
                if ($scope.isSKUValue()) {
                    $scope.selectSKUValue = $scope.sourceValue;
                    $scope.getVendorForSku($scope.sourceValue.id);
                    $scope.getSkuPackaging($scope.sourceValue);
                } else {
                    $scope.selectVendorValue = $scope.sourceValue;
                    $scope.getSkuForVendor($scope.sourceValue.id);
                    $scope.getVendorDispatchLocations($scope.sourceValue);
                }
            };

            $scope.getSkuPackaging = function (sku) {
                if (sku == undefined || sku == null) {
                    return;
                }

                $scope.selectSKUValue = sku;
                $http({
                    method: "POST",
                    dataType: 'json',
                    data: sku.id,
                    headers: {
                        "Content-Type": "application/json"
                    },
                    url: apiJson.urls.skuMapping.skuPackaging,
                }).then(function success(response) {
                    $scope.skuPkgs = response.data;
                }, function error(response) {
                    console.log("error:" + response);
                });
            };

            $scope.getVendorDispatchLocations = function (vendor) {
                if (vendor == undefined || vendor == null) {
                    return;
                }
                $scope.selectVendorValue = vendor;
                $http({
                    method: "POST",
                    dataType: 'json',
                    data: vendor.id,
                    headers: {
                        "Content-Type": "application/json"
                    },
                    url: apiJson.urls.skuMapping.vendorSites,
                }).then(function success(response) {
                    $scope.vendorDispatchLocations = response.data;
                }, function error(response) {
                    console.log("error:" + response);
                });
                $timeout(function () {
                    $('#vendorDispatchLocationSelectId').val('').trigger('change');
                });
            };

            $scope.submitPriceData = function () {
                if ($scope.negotiatedPriceValue == null || $scope.selectSkuPackaging == null
                    || $scope.selectDispatchLocation == null || $scope.selectVendorValue == null
                    || $scope.selectSKUValue == null || $scope.leadTime == null) {
                    var msg = "Please fill all values.";
                    $toastService.create(msg);
                    return false;
                }
                var currentUser = appUtil.getCurrentUser();
                var payload = {
                    detail: {},
                    employeeId: currentUser.userId,
                    employeeName: currentUser.user.name
                };
                payload.detail.sku = $scope.selectSKUValue;
                payload.detail.pkg = {
                    id: $scope.selectSkuPackaging.packagingId,
                    name: $scope.selectSkuPackaging.packagingName,
                };
                payload.detail.vendor = $scope.selectVendorValue;
                payload.detail.dispatch = $scope.selectDispatchLocation;
                payload.detail.delivery = $scope.deliveryLocation;
                payload.detail.leadTime= $scope.leadTime;
                payload.detail.current = {
                    value: $scope.negotiatedPriceValue
                };

                if ($scope.alreadyExists(payload)) {
                    var msg = "SKU price Mapping already exists! Please verify."
                    $toastService.create(msg);
                    return false;
                }

                $http({
                    url: apiJson.urls.skuMapping.addSkuPrice,
                    method: 'POST',
                    data: payload
                }).then(function (response) {
                    if (response.data) {
                        var msg = 'Update Submitted Successfully';
                        $toastService.create(msg);
                        $('#addSkuPriceMappingModal').closeModal();
                        $scope.showPricing();
                        $scope.updatedPriceRequest=new Map();
                    }
                }, function (response) {
                    console.log("error", response);
                    var msg = 'Update Failed';
                    $toastService.create(msg);
                    $scope.showPricing();
                });
                $scope.clearAddPriceData();
            };

            $scope.alreadyExists = function (payload) {
                var payloadKey = payload.detail.sku.id + '#' + payload.detail.pkg.id + '#'
                    + payload.detail.vendor.id + '#' + payload.detail.dispatch.id + '#'
                    + payload.detail.delivery.id;

                if ($scope.priceGridOptions != null && $scope.priceGridOptions.data != null
                    && $scope.priceGridOptions.data.length > 0) {
                    var index = 0;

                    for (index = 0; index < $scope.priceGridOptions.data.length; index++) {
                        var value = $scope.priceGridOptions.data[index];
                        var innerKey = value.sku.id + '#' + value.pkg.id + '#' + value.vendor.id + '#'
                            + value.dispatch.id + '#' + value.delivery.id;
                        if (payloadKey === innerKey) {
                            return true;
                        }
                    }
                }
                return false;
            };

            $scope.clearAddPriceData = function () {
                $scope.negotiatedPriceValue = null;
                $scope.selectSkuPackaging = null;
                $scope.selectDispatchLocation = null;
                $scope.selectVendorValue = null;
                $scope.vendorDispatchLocations = null;
                $scope.skuPkgs = null;
                $timeout(function () {
                    $('#vendorValueSelectId').val('').trigger('change');
                    $('#vendorDispatchLocationSelectId').val('').trigger('change');
                    $('#skuPackSelector').val('').trigger('change');
                    $('#skuValueSelectId').val('').trigger('change');
                });
            };

            $scope.showGrid = function () {
                return $scope.sourceValue != null && $scope.deliveryLocation != null
                    && $scope.priceGridOptions.data != null && $scope.priceGridOptions.data.length > 0
            };

            $scope.showNoDataMsg = function () {
                return $scope.sourceValue != null && $scope.deliveryLocation != null
                    && $scope.priceGridOptions.data != null && $scope.priceGridOptions.data.length == 0;
            };

        }]).controller('vendorToSkuPriceUpdateModalCtrl', ['$scope', 'data','vendor','Popeye','$http','apiJson', 'appUtil', '$toastService',
               function ($scope, data,vendor, Popeye,$http,apiJson, appUtil, $toastService) {
                   $scope.initSKUPriceUpdationModal =function () {
                        var currentUser = appUtil.getCurrentUser();
                        $scope.data = [];
                        $scope.vendor = vendor;
                        data.forEach(function (headerkey, headervalue) {
                            var payload = {
                                detail: headerkey,
                                employeeId: currentUser.userId,
                                employeeName: currentUser.user.name
                            };
                            $scope.data.push(payload);
                        });
                   }

                   $scope.close = function(){
                       Popeye.closeCurrentModal(false);
                   };

                   $scope.submit = function () {
                       $http({
                           url: apiJson.urls.skuMapping.generateSkuPrice,
                           method: 'POST',
                           data: $scope.data
                       }).then(function success(response) {
                           if (response.data != null && response.status == 200) {
                               $toastService.create("Request Send Successfully");
                               Popeye.closeCurrentModal(true);
                           }
                       }, function error(response) {
                           $toastService.create(response.data.errorMsg);
                           Popeye.closeCurrentModal(false);
                       });
                   };

                   $scope.updateData = function (id, excessQuantity) {
                   }

               }
               ]);