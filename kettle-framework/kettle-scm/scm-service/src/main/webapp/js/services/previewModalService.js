/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by Rahul on 04-01-2017.
 */
angular.module('scmApp').service('previewModalService', ['appUtil', 'Popeye','apiJson', function (appUtil, Popeye, apiJson) {

    var service = this;

    service.showPreview = function ($event, id, type) {
        $event.stopPropagation();
        $event.preventDefault();
        var previewObj = {id:id, type:type, found:false};
        if(type=="PRODUCT"){
            appUtil.getScmProductDetails().map(function (item) {
                if(item.productId == id){
                    previewObj.image = apiJson.productBaseUrl+item.productImage;
                    previewObj.name = item.productName;
                    previewObj.description = item.productDescription;
                    previewObj.uom = item.unitOfMeasure;
                    previewObj.found = true;
                    previewObj.imageFound = !appUtil.checkEmpty(item.productImage);
                }
            })
        }
        if(type=="SKU"){
            Object.values(appUtil.getSkuProductMap()).map(function (val) {
                val.map(function (item) {
                    if(item.skuId == id){
                        previewObj.image = apiJson.skuBaseUrl+item.skuImage;
                        previewObj.name = item.skuName;
                        previewObj.description = item.skuDescription;
                        previewObj.uom = item.unitOfMeasure;
                        previewObj.found = true;
                        previewObj.imageFound = !appUtil.checkEmpty(item.skuImage);
                    }
                })
            })
        }
        var previewModal = Popeye.openModal({
            templateUrl: "views/previewModal.html",
            controller: "previewModalCtrl",
            resolve: {
                previewObj: function () {
                    return previewObj;
                }
            },
            click: true,
            keyboard: false
        });
    };

    return service;
}]);