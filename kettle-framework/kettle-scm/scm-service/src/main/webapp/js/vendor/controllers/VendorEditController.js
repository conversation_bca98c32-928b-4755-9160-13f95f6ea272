scmApp.controller('vendorEditCtrl', ['$rootScope', '$scope', '$http', 'appUtil', '$toastService', 'apiJson',
        '$alertService', '$location', 'authService', '$state', '$stateParams', 'metaDataService','Popeye',
        function ($rootScope, $scope, $http, appUtil, $toastService, apiJson,
                  $alertService, $location, authService, $state, $stateParams, metaDataService,Popeye) {

            $scope.download = function (value) {
                metaDataService.downloadDocument(value);
            };

            $scope.init = function () {
                $scope.editMode = false;
                $scope.requestId = $stateParams.reqId;
                $scope.currentUser = appUtil.getCurrentUser();
                getVendorDetail($stateParams.vendor.vendorId);
                $scope.approvalData = $stateParams.data;
                $scope.requestId = $stateParams.reqId;
                $scope.showApproveBtn = isValidVendorRequest();
                $scope.mode =$stateParams.mode;
            };

            function getVendorDetail(vendorId) {
                $http({
                    url: apiJson.urls.vendorManagement.vendor,
                    method: 'GET',
                    params: {
                        vendorId: vendorId
                    }
                }).then(function (response) {
                    $scope.basicDetail = response.data;
                    $scope.dispatchLocationTest= $scope.basicDetail.dispatchLocations;
                    $scope.basicDetailCheckBoxes = JSON.parse(JSON.stringify(response.data));

                    var checkList =["accountDetails","companyDetails","vendorEditedData"];
                    var checkListLocations =["dispatchLocations"];

                    Object.keys($scope.basicDetailCheckBoxes).map(function (key){
                        if(checkList.includes(key)){
                            Object.keys($scope.basicDetailCheckBoxes[key]).map(function (keyInside){
                                $scope.basicDetailCheckBoxes[key][keyInside]={};
                                $scope.basicDetailCheckBoxes[key][keyInside]["changesRequired"]=false;
                                $scope.basicDetailCheckBoxes[key][keyInside]["comment"]="";
                            })
                        } else if (checkListLocations.includes(key)) {
                            $scope.basicDetailCheckBoxes[key].map(function (key1, index) {
                                    Object.keys($scope.basicDetailCheckBoxes[key][index]).map(function (keyInside) {
                                        $scope.basicDetailCheckBoxes[key][index][keyInside] = {};
                                        $scope.basicDetailCheckBoxes[key][index][keyInside]["changesRequired"] = false;
                                        $scope.basicDetailCheckBoxes[key][index][keyInside]["comment"] = "";
                                        $scope.basicDetailCheckBoxes[key][index][keyInside]["dispatchId"] = $scope.basicDetail[key][index].dispatchId.toString();
                                    })
                                }
                            )
                        } else {
                            $scope.basicDetailCheckBoxes[key]={};
                            $scope.basicDetailCheckBoxes[key]["changesRequired"]=false;
                            $scope.basicDetailCheckBoxes[key]["comment"]="";
                        }

                    })
                    $scope.showApproveBtn = isValidVendorRequest();

                });
            }

            $scope.approveDetails = function () {
                $http({
                    url: apiJson.urls.vendorManagement.approveVendor,
                    method: 'POST',
                    data: $stateParams.data
                }).then(function (response) {
                    if (!appUtil.isEmptyObject(response) && response.data) {
                        $toastService.create("Vendor Approved successfully!", function () {
                            $scope.goBack();
                        });
                    }
                }, function (response) {
                    console.log("got error", response);
                });
            }

            function isValidVendorRequest() {
                return $scope.basicDetail != null && $scope.requestId != null
                    && getPreviousState() != 'menu.vendorManagement' && $scope.basicDetail.status == "COMPLETED"
                    && ($scope.currentUser.user.department.id == 105 || $scope.currentUser.user.id == 120063);
            }

            $scope.onCheckboxClicked = function (key) {
                if(!$scope.basicDetailCheckBoxes[key].changesRequired){
                    $scope.basicDetailCheckBoxes[key].comment="";
                }

            };

            $scope.onDispatchLocationsCheckboxClicked = function (key,value,index) {
                if(!$scope.basicDetailCheckBoxes.dispatchLocations[index][key].changesRequired){
                    $scope.basicDetailCheckBoxes.dispatchLocations[index][key].comment="";
                }
            };

            $scope.onCompanyDetailsCheckboxClicked = function (key) {
                if(!$scope.basicDetailCheckBoxes.companyDetails[key].changesRequired){
                    $scope.basicDetailCheckBoxes.companyDetails[key].comment="";
                }
            };

            $scope.onAccountDetailsCheckboxClicked = function (key) {
                if(!$scope.basicDetailCheckBoxes.accountDetails[key].changesRequired){
                    $scope.basicDetailCheckBoxes.accountDetails[key].comment="";
                }
            };

            $scope.changeCreditDays = function (creditDays) {
                $scope.creditDays = creditDays;
            };

            $scope.isEdited=function (key) {
                  var editedData=$scope.basicDetail.vendorEditedData;
                if(key=='vendorId')
                    return false;
                if(editedData==null)
                    return false;
                for (var key1 in editedData) {

                    if(key==key1)
                    {
                        return editedData[key1];
                    }
                }
                return false;
            };

            function getPreviousState() {
                return appUtil.isEmptyObject($rootScope.previousState.name)
                    ? 'menu.vendorManagement' : $rootScope.previousState.name;
            }

            $scope.goBack = function () {
                $state.go(getPreviousState());
            };

            $scope.onSubmitEditChanges = function(){
                $scope.timeout = setTimeout(function () {
                    openRequestChangesModal();
                }, 500);

            }

            function openRequestChangesModal(){
                $scope.modalOpened = true;

                var distanceViewModal = Popeye.openModal({
                    templateUrl: "requestedChanges.html",
                    controller: "requestedChangesCtrl",
                    resolve: {
                        basicDetail : function (){
                            return $scope.basicDetail;
                        },
                        basicDetailCheckBoxes : function (){
                            return $scope.basicDetailCheckBoxes;
                        },
                    },
                    modalClass:'custom-modal',
                    click: false,
                    keyboard: false
                });

                distanceViewModal.closed.then(function(data){
                    $scope.modalOpened = false;
                });
            }

            $scope.requestForChange = function (key) {
                var checkHideDetails = ["vendorId", "status", "requestedBy", "updateTime", "paymentBlocked", "disclaimerAccepted", "creditDays", "leadTime", "blockedReason",
                    "lastUnBlockedDate", "lastBlockedDate", "type", "updatedBy", "creditPeriodDays", "tds", "vendorBlocked", "unblockedTillDate", "lastBlockedBy", "lastUnBlockedBy",
                    "creditDays", "exemptSupplier", "paymentBlocked", "micrCode", "updateTime", "dispatchId", "tin", "status"];
                if (checkHideDetails.includes(key)) {
                    return false;
                } else {
                    return true;
                }
            }

            $scope.partialdeactivateVendor = function(vendorId){
                $alertService.confirm("Are you sure?","",function(result){
                    if(result){
                    	$http({
                            url: apiJson.urls.vendorManagement.partialDeactivateVendor,
                            method: 'GET',
                            params: {
                                vendorId: vendorId
                            }
                        }).then(function (response) {
                        	if(response.data == ""){
                                $toastService.create("Vendor Partially Deactivated");
                          	$state.go(getPreviousState());  
                          }
                          else{
                          	$toastService.create(response.data);
                          }
                        }, function (err) {
                            console.log("Encountered error at backend", err);
                        });
                    }
                });
            }
            
            $scope.fullydeactivateVendor = function(vendorId){
                $alertService.confirm("Are you sure?","",function(result){
                    if(result){
                    	$http({
                            url: apiJson.urls.vendorManagement.vendorDeactivate,
                            method: 'PUT',
                            params: {
                                vendorId: vendorId
                            }
                        }).then(function (response) {
                            if(response.data){
                            	$toastService.create("Vendor Deactivated successfully");
                               $state.go(getPreviousState());
                            }
                        }, function (err) {
                            console.log("Encountered error at backend", err);
                        });
                    }
                });
            }
            
            $scope.reactivateVendor = function(vendorId){
                $alertService.confirm("Are you sure?","",function(result){
                    if(result){
                    	$http({
                            url: apiJson.urls.vendorManagement.reactivateVendor,
                            method: 'GET',
                            params: {
                                vendorId: vendorId
                            }
                        }).then(function (response) {
                            if(response.data){
                            	$toastService.create("Vendor Reactivated successfully");
                            	 $state.go(getPreviousState());
                            }
                        }, function (err) {
                            console.log("Encountered error at backend", err);
                        });
                    }
                });
            }

            $scope.approve = function (creditCycle) {
                if (!appUtil.isEmptyObject(creditCycle)) {
                    $http({
                        url: apiJson.urls.vendorManagement.approveVendor,
                        method: 'POST',
                        data: {
                            requestId: $scope.requestId,
                            creditCycle: creditCycle,
                            userId: $scope.currentUser.userId
                        }
                    }).then(function (response) {
                        if (!appUtil.isEmptyObject(response) && response.data) {
                            $toastService.create("Vendor Approved successfully!");
                            $scope.showApproveBtn = false;
                        } else {
                            $toastService.create("Failed to approve Vendor request! Please try again later...");
                        }
                    }, function (response) {
                        console.log("got error", response);
                    });
                } else {
                    $toastService.create("Please enter credit cycle before approving vendor!");
                }
            };

            $scope.blockPayments = function (vendor) {
                $http({
                    url: apiJson.urls.vendorManagement.blockPayments,
                    method: 'POST',
                    data: {
                        vendorId: vendor.vendorId,
                        userId: $scope.currentUser.userId
                    }
                }).then(function (response) {
                    if (!appUtil.isEmptyObject(response) && response.data == true) {
                        $toastService.create("Vendor payments blocked successfully!");
                        vendor.accountDetails.paymentBlocked = true;
                    } else {
                        $toastService.create("Failed to block payments");
                    }
                }, function (response) {
                    console.log("got error", response);
                });
            };

            $scope.unBlockPayments = function (vendor) {
                $http({
                    url: apiJson.urls.vendorManagement.unBlockPayments,
                    method: 'POST',
                    data: {
                        vendorId: vendor.vendorId,
                        userId: $scope.currentUser.userId
                    }
                }).then(function (response) {
                    if (!appUtil.isEmptyObject(response) && response.data == true) {
                        $toastService.create("Vendor payments un-blocked successfully!");
                        vendor.accountDetails.paymentBlocked = false;
                    } else {
                        $toastService.create("Failed to un-block payments");
                    }
                }, function (response) {
                    console.log("got error", response);
                });
            };
        }
    ]
).filter('hideFilter', [function () {
    return function (items, hideProps) {
        hideProps = hideProps.split(",");
        var result = {};
        angular.forEach(items, function (value, key) {
            if (hideProps.indexOf(key) == -1) {
                result[key] = value;
            }
        });
        return result;
    }
}]).directive('changeForm', [function () {
        return {
            restrict: 'A',
            require: 'ngModel',
            replace: true,
            scope: {
                ngModel: '=ngModel'
            },
            link: function compile(scope, element) {
                scope.$watch('ngModel', function (value) {
                    var html = value.replace(/([A-Z])/g, " $1"); // inserting space into
                    var finalResult = html.charAt(0).toUpperCase() + html.slice(1);
                    element.html(finalResult);
                });
            }
        };
    }]
).directive('createAddress', [function () {
        return {
            restrict: 'A',
            require: 'ngModel',
            replace: true,
            scope: {
                ngModel: '=ngModel'
            },
            link: function compile(scope, element) {
                scope.$watch('ngModel', function (address) {
                    var addressForStr = angular.copy(address);
                    var values = Object.keys(address).filter(function (key) {
                        return key != "addressId" && key != "addressType" && key != "locationId" && key != "stateCode";
                    }).map(function (key) {
                        return address[key];
                    });

                    var addressStr = values.filter(function (val) {
                        return val;
                    }).join(", ");
                    element.html(addressStr.toUpperCase());
                });
            }
        };
    }]
).controller('requestedChangesCtrl', ['$rootScope','$scope','appUtil','apiJson','$state', '$toastService', '$http', 'Popeye','basicDetail','basicDetailCheckBoxes',
        function ($rootScope,$scope,appUtil,apiJson,$state, $toastService, $http, Popeye,basicDetail,basicDetailCheckBoxes) {
            $scope.init = function (){
                $scope.basicDetail = basicDetail;
                $scope.basicDetailCheckBoxes = basicDetailCheckBoxes;
                $scope.vendorId = basicDetail.vendorId
            }

            $scope.submit = function (){
                $scope.vendorEditRequest($scope.vendorId);
            }

            $scope.vendorEditRequest = function (vendorId){


                $http({
                    url: apiJson.urls.vendorManagement.vendorDetailEditRequest,
                    method: 'POST',
                    params: {
                        vendorId: vendorId,
                        userId: appUtil.getCurrentUser().userId
                    },
                    data: $scope.basicDetailCheckBoxes,

                }).then(function (response) {
                    if(response.data){
                        $scope.closeModal();
                        $toastService.create("Vendor Edit Request Generated");
                        $state.go('menu.vendorManagement');
                    }
                }, function (err) {
                    $scope.closeModal();
                    $state.go('menu.vendorManagement');
                    $toastService.create("Encountered error while generating Edit Request for vendor ",vendorId, err);
                });

            }

            $scope.closeModal = function () {
                Popeye.closeCurrentModal({success : true});
            };

        }
    ]
);
