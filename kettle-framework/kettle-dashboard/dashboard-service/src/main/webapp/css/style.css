@charset "utf-8";
/* CSS Document */
.header {
    background: #5E7E47;
    min-height: 50px;
    color: #fff;
    padding: 10px 0;
}

.ggreen {
    color: #5E7E47;
}

.ggrey {
    color: #525252;
}

.head1 {
    font-size: 23px;
}

.head2 {
    font-size: 18px;
}

.center{
    text-align: center;
}

.right{
    text-align: right;
}

/***************** login page style *********************/
.loginBack {
    /*background-color:#50773e;*/
    background-size: cover;
    position: fixed;
    width: 100%;
    height: 100%;
}

.responseBox {
    width: 400px;
    margin: auto;
    margin-top: 100px;
    height: 50px;
}

.alert-success {
    color: #FFFFFF;
    background-color: #237900;
    border-color: #1E4200;
}

.loginBox {
    width: 400px;
    margin: auto;
    border-radius: 2px;
    border: #DDD 1px solid;
    box-shadow: #CCC 0 0 8px 0;
    padding: 20px;
    text-align: center;
    background: rgba(246, 246, 246, 0.78);
}

.loginDisabled {
    cursor: no-drop;
    pointer-events: none;
}

.responseBox .alert {
    padding: 10px;
}



/*******************dashboard styles**************************/
.tile {
    background: #fff;
    border: #ddd 1px solid;
    border-radius: 6px;
    margin: 10px;
    padding: 10px;
}

.tileWidth1 {
    width: 100%;
    max-width: 415px;
}

.tileWidth2 {
    min-width: 925px;
}

.tileNumber {
    font-size: 50px;
    text-align: center;
    min-height: 100px;
    padding: 20px 0;
    position: relative;
}

.subNumber {
    font-size: 22px;
    text-align: center;
    padding: 10px 0;
}

.tileNumberDesc {
    min-height: 25px;
    margin: -20px 0 20px 0
}

.slide {
    position: absolute;
    opacity: 0;
}

.slide.show {
    position: absolute;
    opacity: 1;
}

.stockTick {
    position: absolute;
    bottom: 0;
    left: 0;
    font-size: 16px;
}

.upArrow{
    color: green;
    font-size: 40px;
    font-weight: bolder;
}

.downArrow{
    color:red;
    font-size: 40px;
    font-weight: bolder;
}

.actionBtnPanel{
    position:absolute;
    bottom: 0;
    right: 0;
    opacity: 0;
}

.directionBtnPanel{
    position:absolute;
    top: 50px;
    right: 5px;
}

.actionBtnPanel:hover{
    position:fixed;
    bottom: 0;
    right: 0;
    opacity: 1;
    padding:20px;
}

.apcTicket{
    border: #ccc 3px solid;
    padding: 9px 10px;
    text-align: center;
    border-radius: 50%;
    display: inline-block;
    width: 88px;
    height: 80px;
}

.quantityDisplay {
	border-radius : 50%;
	font-size: 50px;
	font-weight : bold;
	background : #ddd;
	text-align: center;
	line-height : 150px;
	margin : 10px;
	display : inline-block;
	border : #29dac3 8px solid;
	width : 180px;
	height :180px;
}

.textDisplay {
	font-size: 28px;
	font-weight : bold;
	text-align: center;
	margin : 10px;
}

.numberDisplay {
	font-size: 16px;
	font-weight : bold;
	text-align: center;
	margin : 5px;
}

.productTile {
    background: #fff;
    border: #ddd 1px solid;
    border-radius: 6px;
    margin: 10px;
    padding-top: 10px;
    width:210px;
}


.productRow {
    background: #fff;
    width:100%;
}

.productHead {
   font-size: 23px;
   	width: 100%;
    height: 60px;
   text-align: center;
}

.productNumber {
    font-size: 50px;
    text-align: center;
    height: 120px;
    padding: 36px 0;
    position: relative;
    width: 100%;
}

.productDataHead {
   font-size: 16px;
   width:100%;
   height:50px;
   text-align: center;
   padding-top: 15px;
}

.productLabelTile {
	
	width:71px;
	text-align: center;
	
}
.productLabelTile {
	
	width:71px;
	text-align: center;
	
}
