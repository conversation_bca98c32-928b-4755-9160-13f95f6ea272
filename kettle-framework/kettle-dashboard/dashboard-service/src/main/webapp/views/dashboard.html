<div ng-init="init()"></div>
<button type="button"
	style="float: right; position: relative; z-index: 999; margin: 5px 5px 0 0;"
	class="btn btn-success btn-sm" data-ng-click="changeSlide()">Next</button>

<!--<div class="container-fluid slide show">
	<p
		style="text-align: center; margin: 0; padding: 9px 0;; font-weight: bold; font-size: 30px;">Current
		Sales Dashboard</p>

	<div class="row">
		<div class="col" style="background: #eaeaea;">
			<div class="row" id="celebrationGif" style="display: none; position:fixed;width:100%; height:100%;z-index:9999999;">
				<div class="col-sm-7">
					<img id="myImage" src="img/celebration.gif" style="float: left; width:100%;">
				</div>
				<div class="col-sm-5">
					<marquee class="ggreen" data-ng-if="currentSaleValue > 0"
						direction="up" style="font-size: 140px; height:100%; text-align: center">{{currentSaleValue/10 |
									number:1}}
						L</marquee>
				</div>
			</div>
			<div class="row">
				<div class="col-sm-4">
					<div class="tile">
						<div class="row center">
							<div class="col">
								<span class="apcTicket">{{cumulativeSales.mtd.total.apc |
									number:0}}<br />APC
								</span>
							</div>
							<div class="col head1 ggreen">MTD</div>
							<div class="col">
								<span class="apcTicket">{{cumulativeSales.mtd.total.ticket
									| numFormatThousand}} K<br />Tkt
								</span>
							</div>
						</div>
						<div class="tileNumber">
							{{cumulativeSales.mtd.total.sales |
							numFormatCrore}}/{{cumulativeSales.mtdTarget.total.sales |
							numFormatCrore}} Cr
							<div class="stockTick">
								<span class="upArrow"
									data-ng-if="cumulativeSales.mtd.total.sales>cumulativeSales.mtdTarget.total.sales">&#8593;</span>
								<span class="downArrow"
									data-ng-if="cumulativeSales.mtd.total.sales<cumulativeSales.mtdTarget.total.sales">&#8595;</span>
								{{cumulativeSales.mtd.total.sales |
								getPercentage:cumulativeSales.mtdTarget.total.sales}}
							</div>
						</div>
						<div class="tileNumberDesc text-center ggrey">Sale/Target</div>
						<div class="row" style="margin-bottom: -10px">
							<div class="col border border-left-0">
								<div class="head2">Dine In</div>
								<div class="subNumber">{{cumulativeSales.mtd.dineIn.sales
									| numFormatCrore}}/{{cumulativeSales.mtdTarget.dineIn.sales |
									numFormatCrore}} Cr</div>
							</div>
							<div class="col border border-right-0">
								<div class="head2">Delivery</div>
								<div class="subNumber">
									{{cumulativeSales.mtd.delivery.sales
									|numFormatCrore}}/{{cumulativeSales.mtdTarget.delivery.sales |
									numFormatCrore}} Cr</div>
							</div>
						</div>
					</div>
				</div>
				<div class="col-sm-4">
					<div class="tile">
						<div class="row center">
							<div class="col-sm-3">
								<span class="apcTicket">{{aggregateSales.data.apc.current
									| number:0}}<br />APC
								</span>
							</div>
							<div class="col head1 ggreen">Current Day Sale</div>
							<div class="col-sm-3">
								<span class="apcTicket">{{aggregateSales.data.tickets.current
									| numFormatThousand}} K<br />Tkt
								</span>
							</div>
						</div>
						<div class="tileNumber">
							{{aggregateSales.data.netSale.current |
							numFormatLac}}/{{cumulativeSales.todayTarget.total.sales |
							numFormatLac}} L
							  <div class="stockTick">
                                    <span class="upArrow" data-ng-if="aggregateSales.data.netSale.current>cumulativeSales.todayTarget.total.sales">&#8593;</span>
                                    <span class="downArrow" data-ng-if="aggregateSales.data.netSale.current<cumulativeSales.todayTarget.total.sales">&#8595;</span>
                                    {{aggregateSales.data.netSale.current | getPercentage:cumulativeSales.todayTarget.total.sales}}
                                </div>
						</div>
						<div class="tileNumberDesc text-center ggrey">Current/Target</div>
						<div class="row" style="margin-bottom: -10px">
							<div class="col border border-left-0">
								<div class="head2">Dine In</div>
								<div class="subNumber">
									{{aggregateSales.data.netSale.current -
									aggregateSales.data.deliveryNetSale.current |
									numFormatLac}}/{{cumulativeSales.todayTarget.dineIn.sales |
									numFormatLac}} L</div>
							</div>
							<div class="col border border-right-0">
								<div class="head2">Delivery</div>
								<div class="subNumber">
									{{aggregateSales.data.deliveryNetSale.current |
									numFormatLac}}/{{cumulativeSales.todayTarget.delivery.sales |
									numFormatLac}} L</div>
							</div>
						</div>
					</div>
				</div>
				<div class="col-sm-4">
					<div class="tile">
						<div class="row center">
							<div class="col">
								<span class="apcTicket">{{cumulativeSales.monthlyProjected.total.apc
									| number:0}}<br />APC
								</span>
							</div>
							<div class="col head1 ggreen">Monthly</div>
							<div class="col">
								<span class="apcTicket">{{cumulativeSales.monthlyProjected.total.ticket
									| numFormatThousand}} K<br />Tkt
								</span>
							</div>
						</div>
						<div class="tileNumber">
							{{cumulativeSales.monthlyProjected.total.sales |
							numFormatCrore}}/ {{cumulativeSales.monthlyTarget.total.sales |
							numFormatCrore}} Cr
							<div class="stockTick">
								 <span class="upArrow"
									data-ng-if="cumulativeSales.monthlyProjected.total.sales>cumulativeSales.monthlyTarget.total.sales">&#8593;</span>
								<span class="downArrow"
									data-ng-if="cumulativeSales.monthlyProjected.total.sales<cumulativeSales.monthlyTarget.total.sales">&#8595;</span>
								{{cumulativeSales.monthlyProjected.total.sales |
								getPercentage:cumulativeSales.monthlyTarget.total.sales}}
							</div>
						</div>
						<div class="tileNumberDesc text-center ggrey">Projected/Target</div>
						<div class="row" style="margin-bottom: -10px">
							<div class="col border border-left-0">
								<div class="head2">Dine In</div>
								<div class="subNumber">
									{{cumulativeSales.monthlyProjected.dineIn.sales |
									numFormatCrore}}/{{cumulativeSales.monthlyTarget.dineIn.sales |
									numFormatCrore}} Cr</div>
							</div>
							<div class="col border border-right-0">
								<div class="head2">Delivery</div>
								<div class="subNumber">
									{{cumulativeSales.monthlyProjected.delivery.sales |
									numFormatCrore}}/{{cumulativeSales.monthlyTarget.delivery.sales
									| numFormatCrore}} Cr</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div class="row">
				<div class="col-sm-4"></div>
				<div class="col-sm-4">
					<div class="tile">
						<div class="row center">
							<div class="col"></div>
							<div class="col head1 ggreen">Cafe</div>
							<div class="col"></div>
						</div>
						<table class="table table-striped table-bordered"
							style="margin-top: 30px;">
							<thead class="thead-dark">
								<tr>
									<th>Percentage</th>
									<th>&lt;{{cumulativeSales.cafeCountMTD.percentage*100}}%</th>
									<th>{{cumulativeSales.cafeCountMTD.percentage*100}}-100%</th>
									<th>&gt;100%</th>
								</tr>
							</thead>
							<tbody style="font-size: 21px;">
								<tr>
									<td>Current</td>
									<td>{{cumulativeSales.cafeCountMTD.below}}</td>
									<td>{{cumulativeSales.cafeCountMTD.above}}</td>
									<td>{{cumulativeSales.cafeCountMTD.beyond}}</td>
								</tr>
								<tr>
									<td>Projected</td>
									<td>{{cumulativeSales.cafeCountProjected.below}}</td>
									<td>{{cumulativeSales.cafeCountProjected.above}}</td>
									<td>{{cumulativeSales.cafeCountProjected.beyond}}</td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>
				<div class="col-sm-4"></div>
			</div>
		</div>
	</div>
</div>
 -->
<div class="container-fluid">
	<p
		style="text-align: center; margin: 0; padding: 9px 0; font-weight: bold; font-size: 30px;">New
		Products Dashboard</p>
	<div class="row">

		<div data-ng-repeat="data in productSales track by $index"
			class="productTile">
			<div class="productRow center">
				<div class="productHead ggreen">{{data.detail.detail.name}}</div>
			</div>
			<div class="productRow center">
				<div class="productNumber">{{data.data.total.current}}</div>
			</div>
			<div class="row" style="padding-left: 10px;">
				<div class="productLabelTile border border-left-0">
					<div class="productDataHead">Dine In</div>
					<div class="subNumber">{{data.data.dineIn.current}}</div>
				</div>
				<div class="productLabelTile border border-left-0">
					<div class="productDataHead">Delivery</div>
					<div class="subNumber">{{data.data.delivery.current}}</div>
				</div>
				<div class="productLabelTile border border-left-0">
					<div class="productDataHead">Take Away</div>
					<div class="subNumber">{{data.data.takeAway.current}}</div>
				</div>
			</div>
		</div>



		<!-- <div
				style="width: 20%; display: inline-block; text-align: center; margin-top: 20px;"
				data-ng-repeat="data in productSales track by $index">
				<span highlighter="data.data.total.current" class="quantityDisplay">{{data.data.total.current}}</span><br>
				<span class="textDisplay">{{data.detail.detail.name}}</span> <br>
				<span class="numberDisplay">Dine In :
					{{data.data.dineIn.current}} Delivery :
					{{data.data.delivery.current}} Take Away :
					{{data.data.takeAway.current}}</span>
			</div> -->
	</div>
</div>
<div class="container-fluid slide"></div>
<div class="actionBtnPanel">
	<button type="button" class="btn btn-success btn-sm"
		data-ng-click="refreshData()">Refresh</button>
	<button type="button" class="btn btn-success btn-sm"
		data-ng-click="goFullScreen()">Full screen</button>
</div>
