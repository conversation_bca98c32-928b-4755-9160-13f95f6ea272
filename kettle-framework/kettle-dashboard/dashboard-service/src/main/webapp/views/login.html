<div class="loginBack" ng-init="init()">
	<div class="responseBox">
    	<div class="alert" ng-class="{'alert-danger':!authSuccess, 'alert-success':authSuccess}" ng-show="showMessage">
    		<button type="button" class="close"><span aria-hidden="true" ng-click="removeAlert()">&times;</span></button>
        	{{authMessage}}
        </div>
    </div>
    <div class="loginBox">
    	<img src="img/samriddhi_green.png" style="height:50px" /><br /><br />
        <form>
        	<div class="input-group">
            	<span class="input-group-addon">User Id</span>
                <input type="text" class="form-control" ng-model="userId" data-ng-disabled="true" />
            </div><br />
        	<div class="input-group">
            	<span class="input-group-addon">Passcode&nbsp;</span>
                <input type="password" class="form-control" ng-model="passcode" />
            </div><br />
            <button ng-class="{loginDisabled:loginDisabled}" ng-click="login()" style="background-color:#509ef7;border-color:#509ef7" class="btn btn-primary btn-lg btn-block">{{loginText}}</button>
            <div class="clearfix"></div>
        </form>
    </div>
</div>