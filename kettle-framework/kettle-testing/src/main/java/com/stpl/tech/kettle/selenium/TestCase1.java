package com.stpl.tech.kettle.selenium;

import org.openqa.selenium.*;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.testng.annotations.AfterClass;
import org.testng.annotations.BeforeClass;
import org.testng.annotations.Test;

import java.util.concurrent.TimeUnit;

import static org.testng.Assert.fail;

public class TestCase1 {
    private WebDriver driver;
    private String baseUrl;
    String alert = "Order Placed Successfully";
    private boolean acceptNextAlert = true;
    private StringBuffer verificationErrors = new StringBuffer();

    @BeforeClass(alwaysRun = true)
    public void setUp() throws Exception {
        ChromeOptions options = new ChromeOptions();
        options.addArguments("--start-maximized");
        driver = new ChromeDriver(options);
        baseUrl = "http://dev.kettle.chaayos.com:9595/kettle-service/#/";
        driver.manage().timeouts().implicitlyWait(30, TimeUnit.SECONDS);
    }

    @Test
    public void test1() throws Exception {
        driver.get(baseUrl + "login");
        Thread.sleep(1000);
        driver.findElement(By.xpath(".//*[@id='userId']")).sendKeys("120057");
        driver.findElement(By.xpath(".//*[@id='password']")).sendKeys("123123");
        driver.findElement(By.xpath(".//*[@id='unit']")).sendKeys("Juhu");
        driver.findElement(By.xpath(".//*[@id='screen']")).sendKeys("Transaction");
        driver.findElement(By.xpath("html/body/div[1]/div[1]/div[2]/form/div[9]/button[1]")).click();
        Thread.sleep(1000);
        driver.findElement(By.xpath("html/body/div[1]/div[1]/div[3]/div[2]/button[1]")).click();
        Thread.sleep(1000);
        driver.findElement(By.xpath("(//*[@id='menu-header']/li[1]/a)")).click();
        driver.findElement(By.xpath("(//li[@id='categoryRow']/a/button)")).click();
        driver.findElement(By.xpath("(//button[@type='button'])[2]")).click();
        driver.findElement(By.xpath("(html/body/div[1]/div[1]/div[2]/div[2]/div[2]/div/div/div/div[1]/div[1]/button)")).click();
        driver.findElement(By.xpath("(html/body/div[3][contains(@class, 'modal')]/div[contains(@class, 'modal-dialog')]/div[contains(@class, 'modal-content')]/div[2][contains(@class, 'modal-body')]/form/div[1]/div[2]/div/div[2]/button[2][text()='No Sugar'])")).click();
        driver.findElement(By.xpath("(html/body/div[3][contains(@class, 'modal')]/div[contains(@class, 'modal-dialog')]/div/div[2][contains(@class, 'modal-body')]/form/div[2]/div/div[2]/div[2]/button[1][contains(text(), 'Tulsi')])")).click();
        driver.findElement(By.xpath("(html/body/div[3][contains(@class, 'modal')]/div[contains(@class, 'modal-dialog')]/div[contains(@class, 'modal-content')]/div[3][contains(@class, 'modal-footer')]/div/button[2][text()='Submit'])")).click();
        driver.findElement(By.xpath("html/body/div[1]/div[1]/div[2]/div[2]/div[3]/div/a[3]")).click();
        Thread.sleep(1000);
        driver.findElement(By.xpath("html/body/div[3][contains(@class, 'modal')]/div[contains(@class, 'modal-dialog')]/div[contains(@class, 'modal-content')]/div[2][contains(@class, 'modal-body')]/form/div[1]/div[2]/div/input")).sendKeys("Mayank");

        driver.findElement(By.xpath("html/body/div[3][contains(@class, 'modal')]/div[contains(@class, 'modal-dialog')]/div[contains(@class, 'modal-content')]/div[2][contains(@class, 'modal-body')]/form/div[2]/div[1]/div/a[1]")).click();

        driver.findElement(By.xpath("html/body/div[3][contains(@class, 'modal')]/div[contains(@class, 'modal-dialog')]/div[contains(@class, 'modal-content')]/div[3][contains(@class, 'modal-footer')]/div/div[2]/button[2]")).click();
        Thread.sleep(10000);
    }

    @Test
    public void test2() throws Exception {
        driver.findElement(By.xpath("html/body/div[1]/div[1]/div[3]/div[2]/button[1]")).click();
        Thread.sleep(1000);
        driver.findElement(By.xpath("(//*[@id='menu-header']/li[1]/a)")).click();
        driver.findElement(By.xpath("(//li[@id='categoryRow']/a/button)")).click();
        Thread.sleep(1000);
        driver.findElement(By.xpath("(//button[@type='button'])[2]")).click();
        Thread.sleep(1000);
        driver.findElement(By.xpath("(//button[@type='button'])[2]")).click();
        Thread.sleep(1000);
        driver.findElement(By.xpath("(//button[@type='button'])[2]")).click();
        Thread.sleep(1000);
        driver.findElement(By.xpath("(html/body/div[1]/div[1]/div[2]/div[2]/div[2]/div/div/div/div[1]/div[1]/button)")).click();
        driver.findElement(By.xpath("(html/body/div[3][contains(@class, 'modal')]/div[contains(@class, 'modal-dialog')]/div[contains(@class, 'modal-content')]/div[2][contains(@class, 'modal-body')]/form/div[1]/div[2]/div/div[2]/button[2][text()='No Sugar'])")).click();
        driver.findElement(By.xpath("(html/body/div[3][contains(@class, 'modal')]/div[contains(@class, 'modal-dialog')]/div/div[2][contains(@class, 'modal-body')]/form/div[2]/div/div[2]/div[2]/button[1][contains(text(), 'Tulsi')])")).click();
        driver.findElement(By.xpath("(html/body/div[3][contains(@class, 'modal')]/div[contains(@class, 'modal-dialog')]/div/div[2][contains(@class, 'modal-body')]/form/div[2]/div/div[2]/div[2]/button[2][contains(text(), 'Adrak')])")).click();
        driver.findElement(By.xpath("(html/body/div[3][contains(@class, 'modal')]/div[contains(@class, 'modal-dialog')]/div/div[2][contains(@class, 'modal-body')]/form/div[2]/div/div[2]/div[2]/button[3][contains(text(), 'Eliachi')])")).click();
        driver.findElement(By.xpath("(html/body/div[3][contains(@class, 'modal')]/div[contains(@class, 'modal-dialog')]/div[contains(@class, 'modal-content')]/div[3][contains(@class, 'modal-footer')]/div/button[2][text()='Submit'])")).click();
        driver.findElement(By.xpath("html/body/div[1]/div[1]/div[2]/div[2]/div[3]/div/a[3]")).click();
        Thread.sleep(1000);
        driver.findElement(By.xpath("html/body/div[3][contains(@class, 'modal')]/div[contains(@class, 'modal-dialog')]/div[contains(@class, 'modal-content')]/div[2][contains(@class, 'modal-body')]/form/div[1]/div[2]/div/input")).sendKeys("Mayank");

        driver.findElement(By.xpath("html/body/div[3][contains(@class, 'modal')]/div[contains(@class, 'modal-dialog')]/div[contains(@class, 'modal-content')]/div[2][contains(@class, 'modal-body')]/form/div[2]/div[1]/div/a[1]")).click();

        driver.findElement(By.xpath("html/body/div[3][contains(@class, 'modal')]/div[contains(@class, 'modal-dialog')]/div[contains(@class, 'modal-content')]/div[3][contains(@class, 'modal-footer')]/div/div[2]/button[2]")).click();
        Thread.sleep(10000);
    }

    @AfterClass(alwaysRun = true)
    public void tearDown() throws Exception {
        driver.quit();
        String verificationErrorString = verificationErrors.toString();
        if (!"".equals(verificationErrorString)) {
            fail(verificationErrorString);
        }
    }

    private boolean isElementPresent(By by) {
        try {
            driver.findElement(by);
            return true;
        } catch (NoSuchElementException e) {
            return false;
        }
    }

    private boolean isAlertPresent() {
        try {
            driver.switchTo().alert();
            return true;
        } catch (NoAlertPresentException e) {
            return false;
        }
    }

    private String closeAlertAndGetItsText() {
        try {
            Alert alert = driver.switchTo().alert();
            String alertText = alert.getText();
            if (acceptNextAlert) {
                alert.accept();
            } else {
                alert.dismiss();
            }
            return alertText;
        } finally {
            acceptNextAlert = true;
        }
    }
}
