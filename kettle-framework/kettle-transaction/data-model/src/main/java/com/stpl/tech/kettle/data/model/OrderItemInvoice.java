/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;

/**
 * OrderItem generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "ORDER_ITEM_INVOICE")
public class OrderItemInvoice implements java.io.Serializable {

	private Integer orderItemInvoiceId;
	private int orderId;
	private int stateId;
	private int stateInvoiceId;
	private BigDecimal totalAmount;
	private BigDecimal taxableAmount;
	private String taxCategory;
	private BigDecimal taxAmount;
	private List<OrderItemInvoiceTaxDetail> orderItemInvoiceTaxes = new ArrayList<OrderItemInvoiceTaxDetail>(0);

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "ORDER_ITEM_INVOICE_ID", unique = true, nullable = false)
	public Integer getOrderItemInvoiceId() {
		return this.orderItemInvoiceId;
	}

	public void setOrderItemInvoiceId(Integer orderItemId) {
		this.orderItemInvoiceId = orderItemId;
	}

	@Column(name = "ORDER_ID", nullable = false)
	public int getOrderId() {
		return orderId;
	}

	public void setOrderId(int orderId) {
		this.orderId = orderId;
	}

	@Column(name = "STATE_ID", nullable = false)
	public int getStateId() {
		return stateId;
	}

	public void setStateId(int stateId) {
		this.stateId = stateId;
	}

	@Column(name = "STATE_INVOICE_ID", nullable = false)
	public int getStateInvoiceId() {
		return stateInvoiceId;
	}

	public void setStateInvoiceId(int invoiceId) {
		this.stateInvoiceId = invoiceId;
	}

	@Column(name = "TOTAL_AMOUNT", nullable = false, precision = 10)
	public BigDecimal getTotalAmount() {
		return this.totalAmount;
	}

	public void setTotalAmount(BigDecimal totalAmount) {
		this.totalAmount = totalAmount;
	}

	
	@Column(name = "TAXABLE_AMOUNT", nullable = false, precision = 10)
	public BigDecimal getTaxableAmount() {
		return this.taxableAmount;
	}

	public void setTaxableAmount(BigDecimal paidAmount) {
		this.taxableAmount = paidAmount;
	}

	@Column(name = "TAX_CATEGORY", nullable = true, length = 40)
	public String getTaxCategory() {
		return taxCategory;
	}

	public void setTaxCategory(String taxCode) {
		this.taxCategory = taxCode;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "orderItemInvoice")
	public List<OrderItemInvoiceTaxDetail> getOrderItemInvoiceTaxes() {
		return orderItemInvoiceTaxes;
	}

	public void setOrderItemInvoiceTaxes(List<OrderItemInvoiceTaxDetail> orderItemInvoiceTaxes) {
		this.orderItemInvoiceTaxes = orderItemInvoiceTaxes;
	}

	@Column(name = "TOTAL_TAX", precision = 10)
	public BigDecimal getTaxAmount() {
		return taxAmount;
	}

	public void setTaxAmount(BigDecimal taxAmount) {
		this.taxAmount = taxAmount;
	}

}
