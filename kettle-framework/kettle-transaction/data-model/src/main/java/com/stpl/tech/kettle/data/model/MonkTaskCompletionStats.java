/*
 * Created By Shanmukh
 */

package com.stpl.tech.kettle.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "MONK_TASK_COMPLETION_STATS")
public class MonkTaskCompletionStats implements Cloneable{

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "MONK_TASK_COMPLETION_STATS_ID", unique = true)
    private Integer monkTaskCompletionStatsId;

    @Column(name="ORDER_ID")
    private Integer orderId;

    @Column(name ="TASK_ID")
    private Integer taskId;

    @Column(name="UNIT_ID")
    private Integer unitId;

    @Column(name = "ACTUAL_BOIL_COUNT")
    private Integer actualBoilCount;

    @Column(name = "EXPECTED_BOIL_COUNT")
    private Integer expectedBoilCount;

    @Column(name = "TOTAL_TIME_FOR_POURING")
    private Integer totalTimeForPouring;

    @Column(name = "TOTAL_TIME_FOR_ADD_PATTI")
    private Integer totalTimeForAddPatti;

    @Column(name = "TOTAL_TIME_FOR_BREWING")
    private Integer totalTimeForBrewing;

    @Column(name = "TOTAL_TIME_FOR_TASK_COMPLETION")
    private Integer totalTimeForTaskCompletion;

    @Column(name = "ACTUAL_WATER_DISPENSED")
    private Integer actualWaterDispensed;

    @Column(name = "EXPECTED_WATER_TO_DISPENSE")
    private Integer expectedWaterToDispense;

    @Column(name = "ACTUAL_MILK_DISPENSED")
    private Integer actualMilkDispensed;

    @Column(name = "EXPECTED_MILK_TO_DISPENSE")
    private Integer expectedMilkToDispense;

    @Column(name = "FINAL_QUANTITY_WITH_INGREDIENTS")
    private Integer finalQuantityWithIngredients;

    @Column(name = "BOILS_DETECTED_BEFORE_ADD_PATTI")
    private Integer boilsDetectedBeforeAddPatti;

    @Column(name = "MINIMUM_TARGET_WEIGHT")
    private Integer minimumTargetWeight;

    @Column(name = "FIRMWARE_VERSION")
    private BigDecimal firmwareVersion;

    @Column(name = "IS_ACTUAL_COMPLETION_LOG")
    private String isActualCompletionLog;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "LOG_ADD_TIME")
    private Date logAddTime;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "LOG_ADD_TIME_AT_SERVER")
    private Date logAddTimeAtServer;

    @Column(name = "RECIPE")
    private String recipe;

    @Column(name = "IS_MANUAL_TASK")
    private String isManualTask;

    @Column(name = "IS_CLUBBED")
    private String isClubbed;

    @Column(name = "CLUBBED_WITH_TASK")
    private Integer clubbedWithTask;

    @Column(name = "RECIPE_QUANTITY")
    private BigDecimal recipeQuantity;

    @Column(name = "TASK_QUANTITY")
    private BigDecimal taskQuantity;

    @Column(name = "RECIPE_VERSION_AT_MONK")
    private String recipeVersionAtMonk;

    @Column(name = "RECIPE_REGION_AT_MONK")
    private String recipeRegionAtMonk;

    @Column(name = "ACTUAL_RECIPE_VERSION_OF_UNIT")
    private String actualRecipeVersionOfUnit;

    @Column(name = "ACTUAL_RECIPE_REGION_OF_UNIT")
    private String actualRecipeRegionOfUnit;

    @Column(name = "IS_SPLIT")
    private String isSplit;

    @Column(name = "LINKED_TASK_ID")
    private Integer linkedTaskId;

    @Column(name = "MONK_NAME")
    private String monkName;

    @Column(name = "HOT_STATION_VERSION")
    private String hotStationVersion;

    @Column(name = "ASSEMBLY_VERSION")
    private String assemblyVersion;

    @Column(name = "WEIGHT_CALIBRATION_POINT")
    private BigDecimal weightCalibrationPoint;

    @Column(name = "FORCEFULLY_REMOVED_REASON")
    private String forcefullyRemoved;

    @Column(name = "TOTAL_MONKS")
    private Integer totalMonks;

    @Column(name = "ACTIVE_MONKS")
    private Integer activeMonks;

    @Column(name = "DE_ACTIVATED_MONKS")
    private Integer deActivatedMonks;

    @Column(name = "CONNECTED_MONKS")
    private Integer connectedMonks;

    @Column(name = "NOT_CONNECTED_MONKS")
    private Integer notConnectedMonks;

    @Column(name = "ACTIVE_BUT_NOT_CONNECTED_MONKS")
    private Integer activeButNotConnectedMonks;

    @Column(name = "UNDER_CLEANING_MONKS")
    private Integer underCleaningMonks;

    @Column(name = "UNDER_RESET_MONKS")
    private Integer underResetMonks;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "MONK_X_TWO_LOG_DATA_ID", nullable = false)
    private MonkXTwoLogData monkXTwoLogDataId;

    @Column(name = "EXPECTED_WATER_TO_DISPENSE_AFTER_ADDONS")
    private Integer expectedWaterToDispenseAfterAddons;

    @Column(name = "EXPECTED_MILK_TO_DISPENSE_AFTER_ADDONS")
    private Integer expectedMilkToDispenseAfterAddons;

    @Column(name = "RECIPE_AFTER_ADDONS")
    private String recipeAfterAddons;

    public MonkTaskCompletionStats(Integer orderId, Integer taskId, Integer unitId, Integer actualBoilCount,
                                   Integer expectedBoilCount, Integer totalTimeForPouring, Integer totalTimeForAddPatti,
                                   Integer totalTimeForBrewing, Integer totalTimeForTaskCompletion, Integer actualWaterDispensed,
                                   Integer expectedWaterToDispense, Integer actualMilkDispensed, Integer expectedMilkToDispense,
                                   Integer finalQuantityWithIngredients, Integer boilsDetectedBeforeAddPatti, Integer minimumTargetWeight,
                                   BigDecimal firmwareVersion, Date logAddTime,Integer expectedWaterToDispenseAfterAddons, Integer expectedMilkToDispenseAfterAddons,String recipeAfterAddons ) {
        this.orderId = orderId;
        this.taskId = taskId;
        this.unitId = unitId;
        this.actualBoilCount = actualBoilCount;
        this.expectedBoilCount = expectedBoilCount;
        this.totalTimeForPouring = totalTimeForPouring;
        this.totalTimeForAddPatti = totalTimeForAddPatti;
        this.totalTimeForBrewing = totalTimeForBrewing;
        this.totalTimeForTaskCompletion = totalTimeForTaskCompletion;
        this.actualWaterDispensed = actualWaterDispensed;
        this.expectedWaterToDispense = expectedWaterToDispense;
        this.actualMilkDispensed = actualMilkDispensed;
        this.expectedMilkToDispense = expectedMilkToDispense;
        this.finalQuantityWithIngredients = finalQuantityWithIngredients;
        this.boilsDetectedBeforeAddPatti = boilsDetectedBeforeAddPatti;
        this.minimumTargetWeight = minimumTargetWeight;
        this.firmwareVersion = firmwareVersion;
        this.logAddTime = logAddTime;
        this.expectedMilkToDispenseAfterAddons = expectedMilkToDispenseAfterAddons;
        this.expectedWaterToDispenseAfterAddons = expectedWaterToDispenseAfterAddons;
        this.recipeAfterAddons = recipeAfterAddons;
    }

    @Override
    public MonkTaskCompletionStats clone() throws CloneNotSupportedException {
        try {
            return (MonkTaskCompletionStats) super.clone();
        } catch (CloneNotSupportedException e) {
            throw e;
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof MonkTaskCompletionStats)) return false;
        MonkTaskCompletionStats that = (MonkTaskCompletionStats) o;
        return Objects.equals(getMonkTaskCompletionStatsId(), that.getMonkTaskCompletionStatsId()) && Objects.equals(getOrderId(), that.getOrderId()) && Objects.equals(getTaskId(), that.getTaskId()) && Objects.equals(getUnitId(), that.getUnitId()) && Objects.equals(getActualBoilCount(), that.getActualBoilCount()) && Objects.equals(getExpectedBoilCount(), that.getExpectedBoilCount()) && Objects.equals(getTotalTimeForPouring(), that.getTotalTimeForPouring()) && Objects.equals(getTotalTimeForAddPatti(), that.getTotalTimeForAddPatti()) && Objects.equals(getTotalTimeForBrewing(), that.getTotalTimeForBrewing()) && Objects.equals(getTotalTimeForTaskCompletion(), that.getTotalTimeForTaskCompletion()) && Objects.equals(getActualWaterDispensed(), that.getActualWaterDispensed()) && Objects.equals(getExpectedWaterToDispense(), that.getExpectedWaterToDispense()) && Objects.equals(getActualMilkDispensed(), that.getActualMilkDispensed()) && Objects.equals(getExpectedMilkToDispense(), that.getExpectedMilkToDispense()) && Objects.equals(getFinalQuantityWithIngredients(), that.getFinalQuantityWithIngredients()) && Objects.equals(getBoilsDetectedBeforeAddPatti(), that.getBoilsDetectedBeforeAddPatti()) && Objects.equals(getMinimumTargetWeight(), that.getMinimumTargetWeight()) && Objects.equals(getFirmwareVersion(), that.getFirmwareVersion()) && Objects.equals(getIsActualCompletionLog(), that.getIsActualCompletionLog()) && Objects.equals(getLogAddTime(), that.getLogAddTime()) && Objects.equals(getLogAddTimeAtServer(), that.getLogAddTimeAtServer()) && Objects.equals(getRecipe(), that.getRecipe()) && Objects.equals(getIsManualTask(), that.getIsManualTask()) && Objects.equals(getIsClubbed(), that.getIsClubbed()) && Objects.equals(getClubbedWithTask(), that.getClubbedWithTask()) && Objects.equals(getRecipeQuantity(), that.getRecipeQuantity()) && Objects.equals(getTaskQuantity(), that.getTaskQuantity()) && Objects.equals(getRecipeVersionAtMonk(), that.getRecipeVersionAtMonk()) && Objects.equals(getRecipeRegionAtMonk(), that.getRecipeRegionAtMonk()) && Objects.equals(getActualRecipeVersionOfUnit(), that.getActualRecipeVersionOfUnit()) && Objects.equals(getActualRecipeRegionOfUnit(), that.getActualRecipeRegionOfUnit()) && Objects.equals(getIsSplit(), that.getIsSplit()) && Objects.equals(getLinkedTaskId(), that.getLinkedTaskId()) && Objects.equals(getMonkName(), that.getMonkName()) && Objects.equals(getHotStationVersion(), that.getHotStationVersion()) && Objects.equals(getAssemblyVersion(), that.getAssemblyVersion()) && Objects.equals(getWeightCalibrationPoint(), that.getWeightCalibrationPoint()) && Objects.equals(getForcefullyRemoved(), that.getForcefullyRemoved()) && Objects.equals(getTotalMonks(), that.getTotalMonks()) && Objects.equals(getActiveMonks(), that.getActiveMonks()) && Objects.equals(getDeActivatedMonks(), that.getDeActivatedMonks()) && Objects.equals(getConnectedMonks(), that.getConnectedMonks()) && Objects.equals(getNotConnectedMonks(), that.getNotConnectedMonks()) && Objects.equals(getActiveButNotConnectedMonks(), that.getActiveButNotConnectedMonks()) && Objects.equals(getUnderCleaningMonks(), that.getUnderCleaningMonks()) && Objects.equals(getUnderResetMonks(), that.getUnderResetMonks());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getMonkTaskCompletionStatsId(), getOrderId(), getTaskId(), getUnitId(), getActualBoilCount(), getExpectedBoilCount(), getTotalTimeForPouring(), getTotalTimeForAddPatti(), getTotalTimeForBrewing(), getTotalTimeForTaskCompletion(), getActualWaterDispensed(), getExpectedWaterToDispense(), getActualMilkDispensed(), getExpectedMilkToDispense(), getFinalQuantityWithIngredients(), getBoilsDetectedBeforeAddPatti(), getMinimumTargetWeight(), getFirmwareVersion(), getIsActualCompletionLog(), getLogAddTime(), getLogAddTimeAtServer(), getRecipe(), getIsManualTask(), getIsClubbed(), getClubbedWithTask(), getRecipeQuantity(), getTaskQuantity(), getRecipeVersionAtMonk(), getRecipeRegionAtMonk(), getActualRecipeVersionOfUnit(), getActualRecipeRegionOfUnit(), getIsSplit(), getLinkedTaskId(), getMonkName(), getHotStationVersion(), getAssemblyVersion(), getWeightCalibrationPoint(), getForcefullyRemoved(), getTotalMonks(), getActiveMonks(), getDeActivatedMonks(), getConnectedMonks(), getNotConnectedMonks(), getActiveButNotConnectedMonks(), getUnderCleaningMonks(), getUnderResetMonks());
    }
}
