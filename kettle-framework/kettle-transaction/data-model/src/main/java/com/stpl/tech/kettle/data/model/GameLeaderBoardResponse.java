package com.stpl.tech.kettle.data.model;

import com.stpl.tech.kettle.clevertap.domain.model.GameLeaderBoardDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GameLeaderBoardResponse {
    private GameLeaderBoardDTO yourScore;
    private List<GameLeaderBoardDTO> top10Score;
    private boolean isInTop10;
    private Integer rank;
}
