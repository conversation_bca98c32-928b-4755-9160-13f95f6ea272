package com.stpl.tech.kettle.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "WALLET_SUGGESTION_EVENTS")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class WalletSuggestionEvents {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "EVENT_ID",unique=true,nullable = false)
    private int eventId;

    @Column(name ="ORDER_ID",nullable = false)
    private Integer orderId;

    @Column(name ="CUSTOMER_ID",nullable = false)
    private Integer customerId;

    @Column(name ="CUSTOMER_NAME",nullable = false)
    private String customerName;

    @Column(name ="CONTACT_NUMBER",nullable = false)
    private String contactNumber;

    @Column(name ="BILLING_TIME" , nullable = false)
    private Date billingTime;

    @Column(name="GENERATION_TIME",nullable = false)
    private Date generationTime;

    @Column(name ="TOTAL_AMOUNT_IN_WALLET",nullable = false)
    private BigDecimal totalAmountInWallet;

    @Column(name = "PAID_EXTRA",nullable = false)
    private BigDecimal paidExtra;

    @Column(name = "EXTRA_AMOUNT_GAINED",nullable = false)
    private BigDecimal extraAmountGained;

    @Column(name="PARTNER_ID",nullable = false)
    private Integer partnerId;

    @Column(name="BRAND_ID" ,nullable = false)
    private Integer brandId;

    @Column(name = "STATUS",nullable = false)
    private String status;

    @Column(name = "EVENT_TOKEN",nullable = false)
    private String eventToken;

    @Column(name = "WALLET_OFFER_ID")
    private String walletOfferId;
}
