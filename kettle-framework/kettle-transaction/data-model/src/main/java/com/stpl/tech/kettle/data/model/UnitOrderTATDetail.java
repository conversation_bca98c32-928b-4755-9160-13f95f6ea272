package com.stpl.tech.kettle.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "ORDER_PREP_TIME_AGGREGATE")
public class UnitOrderTATDetail implements Serializable {

    private static final long serialVersionUID = 497590201230422818L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private int id;

    @Column(name = "UNIT_ID")
    private int unitId;

    @Column(name = "BRAND_ID")
    private int brandId;

    @Column(name = "RECORD_TYPE", length = 30)
    private String type;

    @Column(name = "TOTAL_ORDERS")
    private int totalOrders;

    @Column(name = "DELAY_ORDERS")
    private int delayedOrders;

    @Column(name = "AVG_TIME_IN_MINUTES")
    private BigDecimal averageTAT;

    @Column(name = "BUSINESS_DATE")
    @Temporal(TemporalType.DATE)
    private Date businessDate;
}
