/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

import static javax.persistence.GenerationType.IDENTITY;

// Generated 19 Aug, 2015 4:03:09 PM by Hibernate Tools 4.0.0

import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

/**
 * ReportParams generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "REPORT_PARAMS")
public class ReportParams implements java.io.Serializable {

	private Integer reportParamsId;
	private ReportDefinition reportDefinition;
	private AttributeDefinition attributeDefinition;
	private Set<ReportExecutionParams> reportExecutionParamses = new HashSet<ReportExecutionParams>(0);

	public ReportParams() {
	}

	public ReportParams(ReportDefinition reportDefinition, AttributeDefinition attributeDefinition) {
		this.reportDefinition = reportDefinition;
		this.attributeDefinition = attributeDefinition;
	}

	public ReportParams(ReportDefinition reportDefinition, AttributeDefinition attributeDefinition,
			Set<ReportExecutionParams> reportExecutionParamses) {
		this.reportDefinition = reportDefinition;
		this.attributeDefinition = attributeDefinition;
		this.reportExecutionParamses = reportExecutionParamses;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "REPORT_PARAMS_ID", unique = true, nullable = false)
	public Integer getReportParamsId() {
		return this.reportParamsId;
	}

	public void setReportParamsId(Integer reportParamsId) {
		this.reportParamsId = reportParamsId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "REPORT_DEF_ID", nullable = false)
	public ReportDefinition getReportDefinition() {
		return this.reportDefinition;
	}

	public void setReportDefinition(ReportDefinition reportDefinition) {
		this.reportDefinition = reportDefinition;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ATTRIBUTE_DEF_ID", nullable = false)
	public AttributeDefinition getAttributeDefinition() {
		return this.attributeDefinition;
	}

	public void setAttributeDefinition(AttributeDefinition attributeDefinition) {
		this.attributeDefinition = attributeDefinition;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "reportParams")
	public Set<ReportExecutionParams> getReportExecutionParamses() {
		return this.reportExecutionParamses;
	}

	public void setReportExecutionParamses(Set<ReportExecutionParams> reportExecutionParamses) {
		this.reportExecutionParamses = reportExecutionParamses;
	}

}
