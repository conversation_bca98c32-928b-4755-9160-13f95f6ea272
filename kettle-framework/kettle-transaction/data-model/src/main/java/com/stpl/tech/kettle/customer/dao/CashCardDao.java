/*
] * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.customer.dao;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import com.stpl.tech.kettle.core.data.vo.CustomerCardInfo;
import com.stpl.tech.kettle.core.data.vo.GiftOffer;
import com.stpl.tech.kettle.core.exception.CardValidationException;
import com.stpl.tech.kettle.data.model.CashCardDetail;
import com.stpl.tech.kettle.data.model.CashCardOffer;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.domain.model.CashCardEventStatus;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.data.dao.AbstractDao;
import com.stpl.tech.master.domain.model.GiftCardActivationRequest;

public interface CashCardDao extends AbstractDao {

    public CashCardDetail getCardDetail(int customerId, String cardNumber, boolean runValidations) throws CardValidationException;

    public List<CashCardDetail> getCardDetails(int customerId) throws CardValidationException;

    public void createCardEvent(int cashCardId, int customerId, int orderId, int settlementId, BigDecimal amount) throws CardValidationException;

    public void cancelCardEvent(int orderId, int settlementId, int cashCardId) throws CardValidationException;

    public CashCardDetail getCardDetail(String serial) throws CardValidationException;

    public void deactivateCashCards();

    public CashCardDetail getCardDetail(int cardId);

    public CashCardDetail getCardDetailForActivation(GiftCardActivationRequest giftCardActivationRequest) throws CardValidationException, DataUpdationException;

    public void logCashCardEvent(CashCardDetail cashCardDetail, CashCardEventStatus cashCardEventStatus, GiftCardActivationRequest giftCardActivationRequest) throws DataUpdationException;

	/**
	 * @param customerId
	 * @return
	 */
    public CustomerCardInfo getCashCardAmount(int customerId);

	/**
	 * @param unitId
	 * @return
	 */
	public List<GiftOffer> getCurrentCardOffer(int unitId, Integer partnerId);

	/**
	 * @param cardAmount
	 * @param unitId
	 * @return
	 */
	public CashCardOffer getCashCardOffer(BigDecimal cardAmount, int unitId,int channelId);

	public void activateCashCard(CashCardDetail cardDetail,OrderDetail orderDetail) throws DataUpdationException;

	/**
	 * @param orderId
	 * @return
	 */
	public List<CashCardDetail> getCardsByPurchaseOrderId(Integer orderId);

	/**
	 * @param orderId
	 */
	public void blockCards(Integer orderId);

	/**
	 * @return
	 */
	public List<Integer> getUnitsWithCardOffersForToday();

	public List<CashCardDetail> getActiveCashCards(int customerId);

	public List<CashCardOffer> getAllCashCardOffers(Date businessDate);

    List<CashCardOffer> getCashCardOffersForDate(Date startDate, Date endDate, Integer partnerId);

	List<CashCardOffer> getCashCardOffersForDateWithState(Date startDate, Date endDate, Integer partnerId, String status);

	public List<CashCardOffer> addCashCardOffers(List<CashCardOffer> list) throws CardValidationException;

	public List<CashCardOffer> changeStatusAllCashCardOffers(List<CashCardOffer> list);

    String getUniqueCashCardNumber();

	public BigDecimal getGiftCardOffer(Integer purchaseOrderId) ;

}
