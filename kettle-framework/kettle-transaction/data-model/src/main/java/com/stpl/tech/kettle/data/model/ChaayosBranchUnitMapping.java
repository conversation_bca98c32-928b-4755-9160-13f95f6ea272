package com.stpl.tech.kettle.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Getter
@Setter
@Table(name = "CHAAYOS_BRANCH_UNIT_MAPPING")
@Entity
@AllArgsConstructor
@NoArgsConstructor
public class ChaayosBranchUnitMapping {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID",unique=true,nullable = false)
    private int mappingId;
    @Column(name = "BRANCH_ID")
    private Integer branchId;
    @Column(name = "ALIAS",columnDefinition = "TEXT")
    private String alias;
    @Column(name = "UNIT_ID")
    private Integer unitId;
    @Column(name = "BRAND_ID")
    private Integer brandId;
}
