/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

import static javax.persistence.GenerationType.IDENTITY;

// Generated 19 Aug, 2015 4:37:51 PM by Hibernate Tools 4.0.0

import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

/**
 * ReportExecutionDetail generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "REPORT_EXECUTION_DETAIL")
public class ReportExecutionDetail implements java.io.Serializable {

	private Integer executionDetailId;
	private ReportDefinition reportDefinition;
	private String currentStatus;
	private Set<ReportExecutionParams> reportExecutionParams = new HashSet<ReportExecutionParams>(0);
	private Set<ReportOutputDetails> reportOutputDetails = new HashSet<ReportOutputDetails>(0);

	public ReportExecutionDetail() {
	}

	public ReportExecutionDetail(ReportDefinition reportDefinition, String currentStatus) {
		this.reportDefinition = reportDefinition;
		this.currentStatus = currentStatus;
	}

	public ReportExecutionDetail(ReportDefinition reportDefinition, String currentStatus,
			Set<ReportExecutionParams> reportExecutionParamses, Set<ReportOutputDetails> reportOutputDetailses) {
		this.reportDefinition = reportDefinition;
		this.currentStatus = currentStatus;
		this.reportExecutionParams = reportExecutionParamses;
		this.reportOutputDetails = reportOutputDetailses;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "EXECUTION_DETAIL_ID", unique = true, nullable = false)
	public Integer getExecutionDetailId() {
		return this.executionDetailId;
	}

	public void setExecutionDetailId(Integer executionDetailId) {
		this.executionDetailId = executionDetailId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "REPORT_DEF_ID", nullable = false)
	public ReportDefinition getReportDefinition() {
		return this.reportDefinition;
	}

	public void setReportDefinition(ReportDefinition reportDefinition) {
		this.reportDefinition = reportDefinition;
	}

	@Column(name = "CURRENT_STATUS", nullable = false, length = 30)
	public String getCurrentStatus() {
		return this.currentStatus;
	}

	public void setCurrentStatus(String currentStatus) {
		this.currentStatus = currentStatus;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "reportExecutionDetail")
	public Set<ReportExecutionParams> getReportExecutionParams() {
		return this.reportExecutionParams;
	}

	public void setReportExecutionParams(Set<ReportExecutionParams> reportExecutionParamses) {
		this.reportExecutionParams = reportExecutionParamses;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "reportExecutionDetail")
	public Set<ReportOutputDetails> getReportOutputDetails() {
		return this.reportOutputDetails;
	}

	public void setReportOutputDetails(Set<ReportOutputDetails> reportOutputDetailses) {
		this.reportOutputDetails = reportOutputDetailses;
	}

}
