/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;
// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 * OrderExternalSettlementData generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "ORDER_EXTERNAL_SETTLEMENT_DATA")
public class OrderExternalSettlementData implements java.io.Serializable {

	private Integer externalSettlementId;
	private OrderSettlement orderSettlement;
	private BigDecimal amountPaid;
	private String externalTransactionId;

	public OrderExternalSettlementData() {
	}

	public OrderExternalSettlementData(OrderSettlement orderSettlement, BigDecimal amountPaid, String externalTransactionId) {
		this.orderSettlement = orderSettlement;
		this.amountPaid = amountPaid;
		this.externalTransactionId = externalTransactionId;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "ORDER_EXTERNAL_SETTLEMENT_ID", unique = true, nullable = false)
	public Integer getExternalSettlementId() {
		return this.externalSettlementId;
	}

	public void setExternalSettlementId(Integer settlementId) {
		this.externalSettlementId = settlementId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SETTLEMENT_ID", nullable = false)
	public OrderSettlement getOrderSettlement() {
		return orderSettlement;
	}

	public void setOrderSettlement(OrderSettlement orderSettlement) {
		this.orderSettlement = orderSettlement;
	}

	@Column(name = "AMOUNT_PAID", precision = 10)
	public BigDecimal getAmountPaid() {
		return this.amountPaid;
	}

	public void setAmountPaid(BigDecimal amountPaid) {
		this.amountPaid = amountPaid;
	}

	@Column(name = "EXTERNAL_TRANSACTION_ID")
	public String getExternalTransactionId() {
		return externalTransactionId;
	}

	public void setExternalTransactionId(String externalTransactionId) {
		this.externalTransactionId = externalTransactionId;
	}

}
