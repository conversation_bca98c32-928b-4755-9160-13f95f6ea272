package com.stpl.tech.kettle.data.model;

import com.stpl.tech.master.data.model.OfferDescriptionMetadata;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class GamifiedOfferResponse implements Serializable {

    private static final long serialVersionUID = 5430988093961359585L;
    private String offerType;//PERCENTAGE_BILL_STATREGY
    private String offerCode;
    private String validityFrom;
    private String validityTill;
    private String text;
    private Boolean isExistingOffer;
    private BigDecimal chaayosCash;
    private String offerTnCString;
    private Integer maxUsage;
    private String refCode;

    private Integer offerCampaignId;
    private boolean isOfferApplied;
    private String crmAppBannerUrl ;
    private String couponCode ;
    private Integer campaignId ;
    private String campaignStrategy;
    private BigDecimal remainingChaayosCash;

    private int minBillValue;
    private int offerValue;
    private String offerValueType;
    private List<Integer> productList;

    private List<OfferDescriptionMetadata> offerTextToShow;

    public GamifiedOfferResponse(String offerType){
        this.offerType = offerType;
    }
}
