package com.stpl.tech.kettle.data.model;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "PAYTM_PAYMENT_DETAILS")
public class PaytmPaymentDetails {

    private Integer paytmPaymentDetailId;
    private String transactionId;
    private String bankTransactionId;
    private String orderId;
    private BigDecimal transactionAmount;
    private String status;
    private String transactionType;
    private String responseCode;
    private String responseMessage;
    private String mId;
    private BigDecimal refundAmount;
    private String refundId;
    private String isAmountRefunded;
    private Date transactionDate;
    private Date updatedTime;

    private String gatewayName;
    private String cardIssuer;
    private String paymentMode;
    private String refundDate;
    private String refundType;
    private String refundIdByPaytm;
    private BigDecimal totalRefundedAmount;

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "PAYTM_PAYMENT_DETAIL_ID", unique = true, nullable = false)
    public Integer getPaytmPaymentDetailId() {
        return paytmPaymentDetailId;
    }

    public void setPaytmPaymentDetailId(Integer paytmPaymentDetailId) {
        this.paytmPaymentDetailId = paytmPaymentDetailId;
    }

    @Column(name = "TRANSACTION_ID", nullable = false)
    public String getTransactionId() {
        return transactionId;
    }

    public void setTransactionId(String transactionId) {
        this.transactionId = transactionId;
    }

    @Column(name = "BANK_TRANSACTION_ID", nullable = true)
    public String getBankTransactionId() {
        return bankTransactionId;
    }

    public void setBankTransactionId(String bankTransactionId) {
        this.bankTransactionId = bankTransactionId;
    }

    @Column(name = "ORDER_ID", nullable = false)
    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }

    @Column(name = "TRANSACTION_AMOUNT", precision = 10, nullable = false)
    public BigDecimal getTransactionAmount() {
        return transactionAmount;
    }

    public void setTransactionAmount(BigDecimal transactionAmount) {
        this.transactionAmount = transactionAmount;
    }

    @Column(name = "STATUS", nullable = false)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "TRANSACTION_TYPE", nullable = false)
    public String getTransactionType() {
        return transactionType;
    }

    public void setTransactionType(String transactionType) {
        this.transactionType = transactionType;
    }

    @Column(name = "RESPONSE_CODE", nullable = false)
    public String getResponseCode() {
        return responseCode;
    }

    public void setResponseCode(String responseCode) {
        this.responseCode = responseCode;
    }

    @Column(name = "RESPONSE_MESSAGE", nullable = true)
    public String getResponseMessage() {
        return responseMessage;
    }

    public void setResponseMessage(String responseMessage) {
        this.responseMessage = responseMessage;
    }

    @Column(name = "MID", nullable = false)
    public String getmId() {
        return mId;
    }

    public void setmId(String mId) {
        this.mId = mId;
    }

    @Column(name = "REFUND_AMOUNT", precision = 10, nullable = false)
    public BigDecimal getRefundAmount() {
        return refundAmount;
    }

    public void setRefundAmount(BigDecimal refundAmount) {
        this.refundAmount = refundAmount;
    }

    @Column(name = "REFUND_ID", nullable = false)
    public String getRefundId() {
        return refundId;
    }

    public void setRefundId(String refundId) {
        this.refundId = refundId;
    }

    @Column(name = "IS_AMOUNT_REFUNDED", nullable = false)
    public String getIsAmountRefunded() {
        return isAmountRefunded;
    }

    public void setIsAmountRefunded(String isAmountRefunded) {
        this.isAmountRefunded = isAmountRefunded;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "TRANSACTION_DATE", nullable = false, length = 19)
    public Date getTransactionDate() {
        return transactionDate;
    }

    public void setTransactionDate(Date transactionDate) {
        this.transactionDate = transactionDate;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "UPDATED_TIME", nullable = false, length = 19)
    public Date getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    @Column(name = "GATEWAY_NAME", nullable = true)
    public String getGatewayName() {
        return gatewayName;
    }

    public void setGatewayName(String gatewayName) {
        this.gatewayName = gatewayName;
    }

    @Column(name = "CARD_ISSUER", nullable = true)
    public String getCardIssuer() {
        return cardIssuer;
    }

    public void setCardIssuer(String cardIssuer) {
        this.cardIssuer = cardIssuer;
    }

    @Column(name = "PAYMENT_MODE", nullable = true)
    public String getPaymentMode() {
        return paymentMode;
    }

    public void setPaymentMode(String paymentMode) {
        this.paymentMode = paymentMode;
    }

    @Column(name = "REFUND_DATE", nullable = true)
    public String getRefundDate() {
        return refundDate;
    }

    public void setRefundDate(String refundDate) {
        this.refundDate = refundDate;
    }

    @Column(name = "REFUND_TYPE", nullable = true)
    public String getRefundType() {
        return refundType;
    }

    public void setRefundType(String refundType) {
        this.refundType = refundType;
    }

    @Column(name = "REFUND_ID_BY_PAYTM", nullable = true)
    public String getRefundIdByPaytm() {
        return refundIdByPaytm;
    }

    public void setRefundIdByPaytm(String refundIdByPaytm) {
        this.refundIdByPaytm = refundIdByPaytm;
    }

    @Column(name = "TOTAL_REFUNDED_AMOUNT", precision = 10, nullable = true)
    public BigDecimal getTotalRefundedAmount() {
        return totalRefundedAmount;
    }

    public void setTotalRefundedAmount(BigDecimal totalRefundedAmount) {
        this.totalRefundedAmount = totalRefundedAmount;
    }
}
