package com.stpl.tech.kettle.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import java.io.Serializable;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name="ORDER_DELAY_REASONS")
public class OrderDelayReason implements Serializable {
    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "DELAY_REASON_ID", unique = true, nullable = false)
    private Integer id;

    @Column(name = "KETTLE_ORDER_ID", nullable = false)
        private Integer orderId;

    @Column(name = "PARTNER_ORDER_ID", nullable = false)
    private String partnerOrderId;

    @Column(name = "PARTNER_NAME", nullable = false)
    private String partnerOrderName;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "BILLING_SERVER_TIME",nullable = false)
    private Date billingServerTime;

    @Column(name = "RIDER_NAME", nullable = true)
    private String riderName;

    @Column(name = "RIDER_CONTACT", nullable = true)
    private String riderContact;

    @Column(name = "RIDER_DELAY_REASON",nullable = true)
    private String riderDelayReason;

    @Column(name = "OPTIONAL_RIDER_DELAY",nullable = true)
    private String optionalRiderDelay;

    @Column(name = "RIDER_DELAY_UPDATED_BY",nullable = true)
    private Integer riderDelayUpdatedBy;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "RIDER_DELAY_UPDATED_AT",nullable = true)
    private Date riderDelayUpdatedAt;

    @Column(name = "UNIT_ID",nullable = true)
    private Integer unitId;

    @Column(name = "UNIT_NAME",nullable = true)
    private String unitName;

    @Column(name = "CAFE_DELAY_REASON",nullable = true)
    private String cafeDelayReason;

    @Column(name = "OPTIONAL_CAFE_DELAY",nullable = true)
    private String optionalCafeDelay;

    @Column(name = "CAFE_DELAY_UPDATED_BY",nullable = true)
    private Integer cafeDelayUpdatedBy;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CAFE_DELAY_UPDATED_AT",nullable = true)
    private Date cafeDelayUpdatedAt;
    @Column(name = "RIDER_DELAY_SOURCE",nullable = false)
    private String riderDelaySource;
    @Column(name = "CAFE_DELAY_SOURCE",nullable = false)
    private String cafeDelaySource;
}
