/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;
// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import com.stpl.tech.kettle.domain.model.CustomerEmailData;
import com.stpl.tech.kettle.domain.model.SubscriptionViewData;

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.ColumnResult;
import javax.persistence.ConstructorResult;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.SqlResultSetMapping;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * OrderSettlement generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "SUBSCRIPTION_EVENT_DETAIL")
public class SubscriptionEventDetail implements java.io.Serializable {

	private Integer subscriptionEventDetailId;
	private SubscriptionDetail subscriptionDetail;
	private Date eventDate;
	private int eventId;
	private String eventStatus;
	private String eventSource;
	private String reasonText;
	private OrderDetail orderDetail;
	private Date addTime;
	private Date eventTime;
	private Date lastUpdateTime;
	private String regularScheduleChanged;
	private String remark;
	private int retryCount;

	public SubscriptionEventDetail() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)

	@Column(name = "SUBSCRIPTION_EVENT_DETAIL_ID", unique = true, nullable = false)
	public Integer getSubscriptionEventDetailId() {
		return this.subscriptionEventDetailId;
	}

	public void setSubscriptionEventDetailId(Integer subscriptionEventDetailId) {
		this.subscriptionEventDetailId = subscriptionEventDetailId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SUBSCRIPTION_ID", nullable = false)
	public SubscriptionDetail getSubscriptionDetail() {
		return this.subscriptionDetail;
	}

	public void setSubscriptionDetail(SubscriptionDetail subscriptionDetail) {
		this.subscriptionDetail = subscriptionDetail;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ORDER_ID", nullable = true)
	public OrderDetail getOrderDetail() {
		return orderDetail;
	}

	public void setOrderDetail(OrderDetail orderId) {
		this.orderDetail = orderId;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LAST_UPDATE_TIME", nullable = true, length = 19)
	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "ADD_TIME", nullable = false, length = 19)
	public Date getAddTime() {
		return addTime;
	}

	public void setAddTime(Date addTime) {
		this.addTime = addTime;
	}

	@Column(name = "EVENT_STATUS", nullable = false, length = 30)
	public String getEventStatus() {
		return eventStatus;
	}

	public void setEventStatus(String eventStatus) {
		this.eventStatus = eventStatus;
	}

	@Column(name = "EVENT_SOURCE", nullable = false, length = 30)
	public String getEventSource() {
		return eventSource;
	}

	public void setEventSource(String toStatus) {
		this.eventSource = toStatus;
	}

	@Column(name = "REASON_TEXT", nullable = true, length = 100)
	public String getReasonText() {
		return reasonText;
	}

	public void setReasonText(String reasonText) {
		this.reasonText = reasonText;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "EVENT_TIME", nullable = false, length = 19)
	public Date getEventTime() {
		return eventTime;
	}

	public void setEventTime(Date eventStartDate) {
		this.eventTime = eventStartDate;
	}

	@Column(name = "REGULAR_SCHEDULE_CHANGED", nullable = false, length = 1)
	public String getRegularScheduleChanged() {
		return regularScheduleChanged;
	}

	public void setRegularScheduleChanged(String regularScheduleChanged) {
		this.regularScheduleChanged = regularScheduleChanged;
	}

	@Column(name = "REMARK", nullable = true, length = 1000)
	public String getRemark() {
		return remark;
	}

	public void setRemark(String remark) {
		this.remark = remark;
	}

	@Column(name = "EVENT_ID", nullable = false)
	public int getEventId() {
		return eventId;
	}

	public void setEventId(int eventId) {
		this.eventId = eventId;
	}

	@Column(name = "RETRY_COUNT", nullable = false)
	public int getRetryCount() {
		return retryCount;
	}

	public void setRetryCount(int retryCount) {
		this.retryCount = retryCount;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "EVENT_DATE", nullable = false, length = 19)
	public Date getEventDate() {
		return eventDate;
	}

	public void setEventDate(Date eventDate) {
		this.eventDate = eventDate;
	}

}
