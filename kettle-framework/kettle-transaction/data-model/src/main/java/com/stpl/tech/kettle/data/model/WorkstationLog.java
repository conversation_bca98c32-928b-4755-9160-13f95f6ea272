/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

import com.hazelcast.transaction.impl.operations.PurgeTxBackupLogOperation;
import com.stpl.tech.util.AppConstants;

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "WORKSTATION_LOG")
public class WorkstationLog {

	private int workstationLogId;
	private int orderId;// not generatedOrderId
	private long itemId;
	private long billCreationTime;
	private int unitId;
	private String type;
	private int employeeId;
	private int productId;
	private int itemQuantity;
	private String dimension;
	private String orderSource;
	private int timeToAcknowledge; // currentTime - orderCreationTime
	private int timeToStart; // currentTime - orderCreationTime -
								// timeToAcknowledge
	private int timeToProcess; // currentTime - orderCreationTime - timeToStart
								// - timeToAcknowledge
	private int timeToCancel; // currentTIme - orderCreationTime - timeToStart
								// - timeToAcknowledge - timeToProcess
	private int timeToProcessByMachine;
	private int cooktopStation; // no of cooktopStation
	private int stationTasksForOrder; // no of total tasks of this type in the
										// parent order
	private boolean dispatched;
	private boolean cancelled;
	private String monkName;
	private String panPlaced = AppConstants.NO;
	private String sufficientWater = AppConstants.NO;
	private String sufficientMilk = AppConstants.NO;
	private String cleanSensor = AppConstants.NO;
	private String others = AppConstants.NO;
	private Long panNotPlacedTime = 0L;
	private Long inSufficientWater = 0L;
	private Long inSufficientMilk = 0L;
	private Long senorNotCleanedTime = 0L;
	private Long othersTimeTaken = 0L;

	private Long quequingTime = 0L;
	private String valid;

    @Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "WORKSTATION_LOG_ID", unique = true, nullable = false)
	public int getWorkstationLogId() {
		return workstationLogId;
	}

	public void setWorkstationLogId(int workstationLogId) {
		this.workstationLogId = workstationLogId;
	}

	@Column(name = "ORDER_ID", nullable = false)
	public int getOrderId() {
		return orderId;
	}

	public void setOrderId(int orderId) {
		this.orderId = orderId;
	}

	@Column(name = "ORDER_ITEM_ID", nullable = false)
	public long getItemId() {
		return itemId;
	}

	public void setItemId(long itemId) {
		this.itemId = itemId;
	}

	@Column(name = "BILL_CREATION_TIME", nullable = true)
	public long getBillCreationTime() {
		return billCreationTime;
	}

	public void setBillCreationTime(long billCreationTime) {
		this.billCreationTime = billCreationTime;
	}

	@Column(name = "UNIT_ID", nullable = false)
	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	@Column(name = "TYPE", nullable = false)
	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	@Column(name = "EMPLOYEE_ID", nullable = false)
	public int getEmployeeId() {
		return employeeId;
	}

	public void setEmployeeId(int employeeId) {
		this.employeeId = employeeId;
	}

	@Column(name = "PRODUCT_ID", nullable = false)
	public int getProductId() {
		return productId;
	}

	public void setProductId(int productId) {
		this.productId = productId;
	}

	@Column(name = "ITEM_QUANTITY", nullable = false)
	public int getItemQuantity() {
		return itemQuantity;
	}

	public void setItemQuantity(int itemQuantity) {
		this.itemQuantity = itemQuantity;
	}

	@Column(name = "DIMENSION", nullable = false)
	public String getDimension() {
		return dimension;
	}

	public void setDimension(String dimension) {
		this.dimension = dimension;
	}

	@Column(name = "ORDER_SOURCE")
	public String getOrderSource() {
		return orderSource;
	}

	public void setOrderSource(String orderSource) {
		this.orderSource = orderSource;
	}

	@Column(name = "TIME_TO_ACKNOWLEDGE", nullable = true)
	public int getTimeToAcknowledge() {
		return timeToAcknowledge;
	}

	public void setTimeToAcknowledge(int timeToAcknowledge) {
		this.timeToAcknowledge = timeToAcknowledge;
	}

	@Column(name = "TIME_TO_START", nullable = true)
	public int getTimeToStart() {
		return timeToStart;
	}

	public void setTimeToStart(int timeToStart) {
		this.timeToStart = timeToStart;
	}

	@Column(name = "TIME_TO_PROCESS", nullable = true)
	public int getTimeToProcess() {
		return timeToProcess;
	}

	public void setTimeToProcess(int timeToProcess) {
		this.timeToProcess = timeToProcess;
	}

	@Column(name = "TIME_TO_CANCEL", nullable = true)
	public int getTimeToCancel() {
		return timeToCancel;
	}

	public void setTimeToCancel(int timeToCancel) {
		this.timeToCancel = timeToCancel;
	}

	@Column(name = "TIME_TO_PROCESS_BY_MACHINE", nullable = true)
	public int getTimeToProcessByMachine() {
		return timeToProcessByMachine;
	}

	public void setTimeToProcessByMachine(int timeToProcessByMachine) {
		this.timeToProcessByMachine = timeToProcessByMachine;
	}

	@Column(name = "COOKTOP_STATION", nullable = true)
	public int getCooktopStation() {
		return cooktopStation;
	}

	public void setCooktopStation(int cooktopStation) {
		this.cooktopStation = cooktopStation;
	}

	@Column(name = "STATION_TASK_FOR_ORDER", nullable = true)
	public int getStationTasksForOrder() {
		return stationTasksForOrder;
	}

	public void setStationTasksForOrder(int stationTasksForOrder) {
		this.stationTasksForOrder = stationTasksForOrder;
	}

	@Column(name = "IS_DISPATCHED", nullable = true)
	public boolean isDispatched() {
		return dispatched;
	}

	public void setDispatched(boolean dispatched) {
		this.dispatched = dispatched;
	}

	@Column(name = "CANCELLED", nullable = true)
	public boolean isCancelled() {
		return cancelled;
	}

	public void setCancelled(boolean cancelled) {
		this.cancelled = cancelled;
	}

	@Column(name = "MONK_NAME", nullable = true)
	public String getMonkName() {
		return monkName;
	}

	public void setMonkName(String monkName) {
		this.monkName = monkName;
	}

	@Column(name = "IS_PAN_NOT_PLACED",nullable = true)

	public String isPanPlaced() {
		return panPlaced;
	}

	public void setPanPlaced(String panPlaced) {
		this.panPlaced = panPlaced;
	}

	@Column(name = "IS_INSUFFICIENT_WATER",nullable = true)
	public String isSufficientWater() {
		return sufficientWater;
	}

	public void setSufficientWater(String sufficientWater) {
		this.sufficientWater = sufficientWater;
	}

	@Column(name = "IS_INSUFFICIENT_MILK",nullable = true)
	public String isSufficientMilk() {
		return sufficientMilk;
	}

	public void setSufficientMilk(String sufficientMilk) {
		this.sufficientMilk = sufficientMilk;
	}

	@Column(name = "IS_SENSOR_NOT_CLEAN",nullable = true)
	public String isCleanSensor() {
		return cleanSensor;
	}

	public void setCleanSensor(String cleanSensor) {
		this.cleanSensor = cleanSensor;
	}

	@Column(name = "IS_OTHERS",nullable = true)
	public String isOthers() {
		return others;
	}

	public void setOthers(String others) {
		this.others = others;
	}

	@Column(name = "PAN_NOT_PLACED_TIME",nullable = true)
	public Long getPanNotPlacedTime() {
		return panNotPlacedTime;
	}

	public void setPanNotPlacedTime(Long panNotPlacedTime) {
		this.panNotPlacedTime = panNotPlacedTime;
	}

	@Column(name = "INSUFFICIENT_WATER_TIME",nullable = true)
	public Long getInSufficientWater() {
		return inSufficientWater;
	}

	public void setInSufficientWater(Long inSufficientWater) {
		this.inSufficientWater = inSufficientWater;
	}

	@Column(name = "INSUFFICIENT_MILK_TIME",nullable = true)
	public Long getInSufficientMilk() {
		return inSufficientMilk;
	}

	public void setInSufficientMilk(Long inSufficientMilk) {
		this.inSufficientMilk = inSufficientMilk;
	}

	@Column(name = "SENSOR_NOT_CLEANED_TIME",nullable = true)
	public Long getSenorNotCleanedTime() {
		return senorNotCleanedTime;
	}

	public void setSenorNotCleanedTime(Long senorNotCleanedTime) {
		this.senorNotCleanedTime = senorNotCleanedTime;
	}

	@Column(name = "OTHERS_TIME_TAKEN",nullable = true)
	public Long getOthersTimeTaken() {
		return othersTimeTaken;
	}

	public void setOthersTimeTaken(Long othersTimeTaken) {
		this.othersTimeTaken = othersTimeTaken;
	}

	@Column(name = "QUEQUING_TIME",nullable = true)
	public Long getQuequingTime() {return quequingTime;}

	public void setQuequingTime(Long quequingTime) {
		this.quequingTime = quequingTime;
	}

	@Column(name ="IS_VALID",nullable = true)
	public String isValid() {
		return valid;
	}

	public void setValid(String valid) {
		this.valid= valid;
	}
}
