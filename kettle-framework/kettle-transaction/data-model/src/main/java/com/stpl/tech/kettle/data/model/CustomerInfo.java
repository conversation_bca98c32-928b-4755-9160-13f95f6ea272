/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.ColumnResult;
import javax.persistence.ConstructorResult;
import javax.persistence.Entity;
import javax.persistence.EntityListeners;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.SqlResultSetMapping;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

import com.stpl.tech.kettle.data.event.listner.CommonJPAEventListner;
import com.stpl.tech.kettle.domain.model.CustomerOneViewData;

/**
 * CustomerInfo generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "CUSTOMER_INFO", uniqueConstraints = @UniqueConstraint(columnNames = { "CONTACT_NUMBER",
		"COUNTRY_CODE" }))
	@SqlResultSetMapping(name = "CustomerOneViewData", classes = @ConstructorResult(targetClass = CustomerOneViewData.class, columns = {
			@ColumnResult(name = "customerId", type = Integer.class),
			@ColumnResult(name = "firstName", type = String.class),
			@ColumnResult(name = "contactNumber", type = String.class),
			@ColumnResult(name = "countryCode", type = String.class), 
			@ColumnResult(name = "emailId", type = String.class),
			@ColumnResult(name = "refCode", type = String.class),
			@ColumnResult(name = "isNumberVerified", type = String.class),
			@ColumnResult(name = "isEmailVerified", type = String.class),
			@ColumnResult(name = "isDnd", type = String.class),
			@ColumnResult(name = "isBlacklisted", type = String.class),
			@ColumnResult(name = "activeDeliveryOrders", type = Integer.class),
			@ColumnResult(name = "activeDineInOrders", type = Integer.class),
			@ColumnResult(name = "activeWalletOrders", type = Integer.class),
			@ColumnResult(name = "activeOverallOrders", type = Integer.class),
			@ColumnResult(name = "activeDeliverySales", type = BigDecimal.class),
			@ColumnResult(name = "activeDineInSales", type = BigDecimal.class),
			@ColumnResult(name = "activeOverallSales", type = BigDecimal.class),
			@ColumnResult(name = "activeWalletSales", type = BigDecimal.class),
			@ColumnResult(name = "activeDeliveryGmv", type = BigDecimal.class),
			@ColumnResult(name = "activeDineInGmv", type = BigDecimal.class),
			@ColumnResult(name = "activeOverallGmv", type = BigDecimal.class),
			@ColumnResult(name = "deliveryOrders", type = Integer.class),
			@ColumnResult(name = "dineInOrders", type = Integer.class),
			@ColumnResult(name = "walletOrders", type = Integer.class),
			@ColumnResult(name = "overallOrders", type = Integer.class),
			@ColumnResult(name = "deliverySales", type = BigDecimal.class),
			@ColumnResult(name = "dineInSales", type = BigDecimal.class),
			@ColumnResult(name = "overallSales", type = BigDecimal.class),
			@ColumnResult(name = "walletSales", type = BigDecimal.class),
			@ColumnResult(name = "deliveryGmv", type = BigDecimal.class),
			@ColumnResult(name = "dineInGmv", type = BigDecimal.class),
			@ColumnResult(name = "overallGmv", type = BigDecimal.class),
			@ColumnResult(name = "lastOrderId", type = Integer.class),
			@ColumnResult(name = "lastDineOrderId", type = Integer.class),
			@ColumnResult(name = "lastDeliveryOrderId", type = Integer.class),
			@ColumnResult(name = "lastWalletOrderId", type = Integer.class),
			@ColumnResult(name = "lastOrderTime", type = Integer.class),
			@ColumnResult(name = "lastDineInOrderTime", type = Date.class),
			@ColumnResult(name = "lastDeliveryOrderTime", type = Date.class),
			@ColumnResult(name = "lastWalletOrderTime", type = Date.class),
			@ColumnResult(name = "availedSignupOffer", type = String.class),
			@ColumnResult(name = "signupOfferExpiryTime", type = Date.class),
			@ColumnResult(name = "acquiredLoyaltyPoints", type = Integer.class),
			@ColumnResult(name = "cumulativeLoyaltyPoints", type = Integer.class),
			@ColumnResult(name="cardcode",type=String.class)}))
@EntityListeners(CommonJPAEventListner.class)
public class CustomerInfo implements java.io.Serializable {

	private int customerId;
	private String firstName;
	private String middleName;
	private String lastName;
	private String countryCode;
	private String contactNumber;
	private String emailId;
	private String isNumberVerified;
	private String isEmailVerified;
	private Date addTime;
	private Integer registrationUnitId;
	private String acquisitionSource;
	private String acquisitionToken;
	private Date numberVerificationTime;
	private Date emailVerificationTime;
	private String smsSubscriber;
	private String emailSubscriber;
	private String loyaltySubscriber;
	private String isBlacklisted = "N";
	private String isInternal = "N";
	private List<CustomerAddressInfo> customerAddressInfos = new ArrayList<CustomerAddressInfo>(0);
	private Integer trueCallerProfileId;
	private String isDND = "N";
	private String optOutOfFaceIt = "N";
	private Date optOutTime;
	private String refCode;
	private String isRefSubscriber;
	private String refAcquisitionSource;
	private Date referredOn;
	private Integer referralDataId;
	private Integer referrerId;
	private String referrerAwarded;
	private String faceId;
	private Integer acquisitionBrandId;
	private String isChaayosCustomer;
	private String gender;
	private Date dateOfBirth;
	private Date anniversary;
	private String customerAppId;
	private String optWhatsapp;
	private String isDeleted;
	private String isRenewed;

	private String appAction;

	private Date appActionTime;

	public CustomerInfo() {
	}

	public CustomerInfo(int customerId, String countryCode, String contactNumber, Date addTime) {
		this.customerId = customerId;
		this.countryCode = countryCode;
		this.contactNumber = contactNumber;
		this.addTime = addTime;
	}

	public CustomerInfo(int customerId, String firstName, String middleName, String lastName, String countryCode,
			String contactNumber, String emailId, String isNumberVerified, String isEmailVerified, Date addTime,
			List<CustomerAddressInfo> customerAddressInfos) {
		this.customerId = customerId;
		this.firstName = firstName;
		this.middleName = middleName;
		this.lastName = lastName;
		this.countryCode = countryCode;
		this.contactNumber = contactNumber;
		this.emailId = emailId;
		this.isNumberVerified = isNumberVerified;
		this.isEmailVerified = isEmailVerified;
		this.addTime = addTime;
		this.customerAddressInfos = customerAddressInfos;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "CUSTOMER_ID", unique = true, nullable = false)
	public int getCustomerId() {
		return this.customerId;
	}

	public void setCustomerId(int customerId) {
		this.customerId = customerId;
	}

	@Column(name = "FIRST_NAME", length = 50)
	public String getFirstName() {
		return this.firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	@Column(name = "MIDDLE_NAME", length = 50)
	public String getMiddleName() {
		return this.middleName;
	}

	public void setMiddleName(String middleName) {
		this.middleName = middleName;
	}

	@Column(name = "LAST_NAME", length = 50)
	public String getLastName() {
		return this.lastName;
	}

	public void setLastName(String lastName) {
		this.lastName = lastName;
	}

	@Column(name = "COUNTRY_CODE", nullable = false, length = 5)
	public String getCountryCode() {
		return this.countryCode;
	}

	public void setCountryCode(String countryCode) {
		this.countryCode = countryCode;
	}

	@Column(name = "CONTACT_NUMBER", nullable = false, length = 12)
	public String getContactNumber() {
		return this.contactNumber;
	}

	public void setContactNumber(String contactNumber) {
		this.contactNumber = contactNumber;
	}

	@Column(name = "EMAIL_ID", length = 50)
	public String getEmailId() {
		return this.emailId;
	}

	public void setEmailId(String emailId) {
		this.emailId = emailId;
	}

	@Column(name = "IS_NUMBER_VERIFIED", length = 1)
	public String getIsNumberVerified() {
		return this.isNumberVerified;
	}

	public void setIsNumberVerified(String isNumberVerified) {
		this.isNumberVerified = isNumberVerified;
	}

	@Column(name = "IS_EMAIL_VERIFIED", length = 1)
	public String getIsEmailVerified() {
		return this.isEmailVerified;
	}

	public void setIsEmailVerified(String isEmailVerified) {
		this.isEmailVerified = isEmailVerified;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "ADD_TIME", nullable = false, length = 19)
	public Date getAddTime() {
		return this.addTime;
	}

	public void setAddTime(Date addTime) {
		this.addTime = addTime;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "NUMBER_VERIFICATION_TIME", nullable = true, length = 19)
	public Date getNumberVerificationTime() {
		return this.numberVerificationTime;
	}

	public void setNumberVerificationTime(Date numberVerificationTime) {
		this.numberVerificationTime = numberVerificationTime;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "EMAIL_VERIFICATION_TIME", nullable = true, length = 19)
	public Date getEmailVerificationTime() {
		return this.emailVerificationTime;
	}

	public void setEmailVerificationTime(Date emailVerificationTime) {
		this.emailVerificationTime = emailVerificationTime;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "customerInfo")
	public List<CustomerAddressInfo> getCustomerAddressInfos() {
		return this.customerAddressInfos;
	}

	public void setCustomerAddressInfos(List<CustomerAddressInfo> customerAddressInfos) {
		this.customerAddressInfos = customerAddressInfos;
	}

	@Column(name = "REGISTRATION_UNIT_ID", nullable = true)
	public Integer getRegistrationUnitId() {
		return registrationUnitId;
	}

	public void setRegistrationUnitId(Integer registrationUnitId) {
		this.registrationUnitId = registrationUnitId;
	}

	@Column(name = "ACQUISITION_SOURCE", length = 100)
	public String getAcquisitionSource() {
		return acquisitionSource;
	}

	public void setAcquisitionSource(String acquisitionSource) {
		this.acquisitionSource = acquisitionSource;
	}

	@Column(name = "ACQUISITION_TOKEN", length = 50)
	public String getAcquisitionToken() {
		return acquisitionToken;
	}

	public void setAcquisitionToken(String acquisitionToken) {
		this.acquisitionToken = acquisitionToken;
	}

	@Column(name = "IS_SMS_SUBSCRIBER", length = 1)
	public String getSmsSubscriber() {
		return smsSubscriber;
	}

	public void setSmsSubscriber(String smsSubscriber) {
		this.smsSubscriber = smsSubscriber;
	}

	@Column(name = "IS_EMAIL_SUBSCRIBER", length = 1)
	public String getEmailSubscriber() {
		return emailSubscriber;
	}

	public void setEmailSubscriber(String emailSubscriber) {
		this.emailSubscriber = emailSubscriber;
	}

	@Column(name = "IS_LOYALTY_SUBSCRIBER", length = 1)
	public String getLoyaltySubscriber() {
		return loyaltySubscriber;
	}

	public void setLoyaltySubscriber(String loyaltySubscriber) {
		this.loyaltySubscriber = loyaltySubscriber;
	}

	@Column(name = "IS_BLACKLISTED", length = 1, nullable = false)
	public String getIsBlacklisted() {
		return isBlacklisted;
	}

	public void setIsBlacklisted(String isBlacklisted) {
		this.isBlacklisted = isBlacklisted;
	}

	@Column(name = "TRUE_CALLER_PROFILE_ID")
	public Integer getTrueCallerProfileId() {
		return trueCallerProfileId;
	}

	public void setTrueCallerProfileId(Integer trueCallerProfileId) {
		this.trueCallerProfileId = trueCallerProfileId;
	}

	@Column(name = "IS_DND")
	public String getIsDND() {
		return isDND;
	}

	public void setIsDND(String isDND) {
		this.isDND = isDND;
	}

	@Column(name = "IS_INTERNAL")
	public String getIsInternal() {
		return isInternal;
	}

	public void setIsInternal(String isInternal) {
		this.isInternal = isInternal;
	}

	@Column(name = "REF_CODE")
	public String getRefCode() {
		return refCode;
	}

	public void setRefCode(String refCode) {
		this.refCode = refCode;
	}

	@Column(name = "IS_REF_SUBSCRIBER")
	public String getIsRefSubscriber() {
		return isRefSubscriber;
	}

	public void setIsRefSubscriber(String isRefSubscriber) {
		this.isRefSubscriber = isRefSubscriber;
	}

	@Column(name = "REF_ACQUISITION_SOURCE")
	public String getRefAcquisitionSource() {
		return refAcquisitionSource;
	}

	public void setRefAcquisitionSource(String refAcquistionSource) {
		this.refAcquisitionSource = refAcquistionSource;
	}

	@Column(name = "REFERRED_ON")
	public Date getReferredOn() {
		return referredOn;
	}

	public void setReferredOn(Date referredOn) {
		this.referredOn = referredOn;
	}

	@Column(name = "REFERRAL_DATA_ID")
	public Integer getReferralDataId() {
		return referralDataId;
	}

	public void setReferralDataId(Integer referradDataId) {
		this.referralDataId = referradDataId;
	}

	@Column(name = "REFERRER_ID")
	public Integer getReferrerId() {
		return referrerId;
	}

	public void setReferrerId(Integer referralId) {
		this.referrerId = referralId;
	}

	@Column(name = "IS_REFERRER_AWARDED")
	public String getReferrerAwarded() {
		return referrerAwarded;
	}

	public void setReferrerAwarded(String referrerAwarded) {
		this.referrerAwarded = referrerAwarded;
	}

	@Column(name = "FACE_ID")
	public String getFaceId() {
		return faceId;
	}

	public void setFaceId(String faceId) {
		this.faceId = faceId;
	}

	@Column(name = "OPT_OUT_FACE_IT")
	public String getOptOutOfFaceIt() {
		return optOutOfFaceIt;
	}

	public void setOptOutOfFaceIt(String optOutOfFaceIt) {
		this.optOutOfFaceIt = optOutOfFaceIt;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "OPT_OUT_TIME", nullable = true, length = 19)
	public Date getOptOutTime() {
		return optOutTime;
	}

	public void setOptOutTime(Date optOutTime) {
		this.optOutTime = optOutTime;
	}

	@Column(name = "ACQUISITION_BRAND_ID", nullable = false)
	public Integer getAcquisitionBrandId() {
		return acquisitionBrandId;
	}

	public void setAcquisitionBrandId(Integer acquisitionBrandId) {
		this.acquisitionBrandId = acquisitionBrandId;
	}

	@Column(name = "IS_CHAAYOS_CUSTOMER", nullable = false)
	public String getIsChaayosCustomer() {
		return isChaayosCustomer;
	}

	public void setIsChaayosCustomer(String isChaayosCustomer) {
		this.isChaayosCustomer = isChaayosCustomer;
	}

	@Column(name = "GENDER")
	public String getGender() {
		return gender;
	}

	public void setGender(String gender) {
		this.gender = gender;
	}

	@Column(name = "DATE_OF_BIRTH")
	public Date getDateOfBirth() {
		return dateOfBirth;
	}

	public void setDateOfBirth(Date dateOfBirth) {
		this.dateOfBirth = dateOfBirth;
	}

	@Column(name = "ANNIVERSARY")
	public Date getAnniversary() {
		return anniversary;
	}

	public void setAnniversary(Date anniversary) {
		this.anniversary = anniversary;
	}

	@Column(name = "CUSTOMER_APP_ID")
	public String getCustomerAppId() {
		return customerAppId;
	}

	public void setCustomerAppId(String customerAppId) {
		this.customerAppId = customerAppId;
	}

	@Column(name = "OPT_IN_WHATSAPP")
	public String getOptWhatsapp() {
		return optWhatsapp;
	}

	public void setOptWhatsapp(String optWhatsapp) {
		this.optWhatsapp = optWhatsapp;
	}

	@Column(name = "IS_DELETED")
	public String getIsDeleted() {
		return isDeleted;
	}

	public void setIsDeleted(String isDeleted) {
		this.isDeleted = isDeleted;
	}

	@Column(name = "IS_RENEWED")
	public String getIsRenewed() {
		return isRenewed;
	}

	public void setIsRenewed(String isRenewed) {
		this.isRenewed = isRenewed;
	}

	@Column(name = "APP_ACTION",length = 100)
	public String getAppAction() {
		return appAction;
	}

	public void setAppAction(String appAction) {
		this.appAction = appAction;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "APP_ACTION_TIME", length = 19)
	public Date getAppActionTime() {
		return appActionTime;
	}

	public void setAppActionTime(Date appActionTime) {
		this.appActionTime = appActionTime;
	}
}
