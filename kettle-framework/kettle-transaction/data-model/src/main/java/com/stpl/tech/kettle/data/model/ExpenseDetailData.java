/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.math.BigDecimal;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "UNIT_EXPENSE_RECORD")
public class ExpenseDetailData implements java.io.Serializable {

	private static final long serialVersionUID = 1L;

	private int id;
	private int unitId;
	private String expenseCategory;
	private String expenseType;
	private Integer expenseTypeId;
	private String budgetCategory;
	private BigDecimal amount;
	private String comment;
	private Integer createdBy;
	private Date createdOn;
	private String status;
	private Integer cancelledBy;
	private Date cancelledOn;
	private String cancellationReason;
	private String accountableInPnL;
	private Integer pnlDetailId;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "RECORD_ID", unique = true, nullable = false)
	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	@Column(name = "UNIT_ID", nullable = false)
	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	@Column(name = "EXPENSE_CATEGORY", nullable = false)
	public String getExpenseCategory() {
		return expenseCategory;
	}

	public void setExpenseCategory(String expenseCategory) {
		this.expenseCategory = expenseCategory;
	}

	@Column(name = "EXPENSE_TYPE", nullable = false)
	public String getExpenseType() {
		return expenseType;
	}

	public void setExpenseType(String expenseType) {
		this.expenseType = expenseType;
	}

	@Column(name = "AMOUNT", nullable = false)
	public BigDecimal getAmount() {
		return amount;
	}

	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}

	@Column(name = "COMMENT", nullable = true)
	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}

	@Column(name = "CREATED_BY", nullable = false)
	public Integer getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATED_ON", nullable = true, length = 19)
	public Date getCreatedOn() {
		return createdOn;
	}

	public void setCreatedOn(Date createdOn) {
		this.createdOn = createdOn;
	}

	@Column(name = "STATUS", nullable = false)
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@Column(name = "CANCELLED_BY", nullable = true)
	public Integer getCancelledBy() {
		return cancelledBy;
	}

	public void setCancelledBy(Integer cancelledBy) {
		this.cancelledBy = cancelledBy;
	}
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CANCELLED_ON", nullable = true, length = 19)
	public Date getCancelledOn() {
		return cancelledOn;
	}

	public void setCancelledOn(Date cancelledOn) {
		this.cancelledOn = cancelledOn;
	}

	@Column(name = "EXPENSE_TYPE_ID", nullable = false)
	public Integer getExpenseTypeId() {
		return expenseTypeId;
	}

	public void setExpenseTypeId(Integer expenseTypeId) {
		this.expenseTypeId = expenseTypeId;
	}

	@Column(name = "BUDGET_CATEGORY", nullable = true)
	public String getBudgetCategory() {
		return budgetCategory;
	}

	public void setBudgetCategory(String budgetCategory) {
		this.budgetCategory = budgetCategory;
	}

	@Column(name = "CANCELLATION_REASON", nullable = true)
	public String getCancellationReason() {
		return cancellationReason;
	}

	public void setCancellationReason(String cancellationReason) {
		this.cancellationReason = cancellationReason;
	}

	@Column(name = "IS_ACCOUNTABLE_IN_PNL", nullable = false)
	public String getAccountableInPnL() {
		return accountableInPnL;
	}

	public void setAccountableInPnL(String accountableInPnL) {
		this.accountableInPnL = accountableInPnL;
	}

	@Column(name = "PNL_DETAIL_ID", nullable = true)
	public Integer getPnlDetailId() {
		return pnlDetailId;
	}

	public void setPnlDetailId(Integer pnlDetailId) {
		this.pnlDetailId = pnlDetailId;
	}

}