package com.stpl.tech.kettle.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "WORKSTATION_MANUAL_TASK_DETAIL")
public class WorkStationManualTaskDetail {

    private Integer manualTaskId;
    private String taskType;
    private Date orderTime;
    private Integer unitId;
    private Integer employeeId;
    private String generatedOrderId;
    private Integer productItemId;
    private String dimension;
    private Integer quantity;
    private Integer monkNumber;
    private String errorType;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "MANUAL_TASK_ID")
    public Integer getManualTaskId() {
        return manualTaskId;
    }

    public void setManualTaskId(Integer manualTaskId) {
        this.manualTaskId = manualTaskId;
    }


    @Column(name = "TASK_TYPE")
    public String getTaskType() {
        return taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    @Column(name = "ORDER_TIME")
    public Date getOrderTime() {
        return orderTime;
    }

    public void setOrderTime(Date orderTime) {
        this.orderTime = orderTime;
    }

    @Column(name = "UNIT_ID")
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @Column(name = "EMPLOYEE_ID")
    public Integer getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(Integer employeeId) {
        this.employeeId = employeeId;
    }

    @Column(name = "GENERATED_ORDER_ID")
    public String getGeneratedOrderId() {
        return generatedOrderId;
    }

    public void setGeneratedOrderId(String generatedOrderId) {
        this.generatedOrderId = generatedOrderId;
    }

    @Column(name = "PRODUCT_ITEM_ID")
    public Integer getProductItemId() {
        return productItemId;
    }

    public void setProductItemId(Integer productItemId) {
        this.productItemId = productItemId;
    }

    @Column(name = "DIMENSION")
    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    @Column(name = "QUANTITY")
    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    @Column(name = "MONK_NUMBER")
    public Integer getMonkNumber() {
        return monkNumber;
    }

    public void setMonkNumber(Integer monkNumber) {
        this.monkNumber = monkNumber;
    }

    @Column(name = "ERROR_TYPE")
    public String getErrorType() {
        return errorType;
    }

    public void setErrorType(String errorType) {
        this.errorType = errorType;
    }
}