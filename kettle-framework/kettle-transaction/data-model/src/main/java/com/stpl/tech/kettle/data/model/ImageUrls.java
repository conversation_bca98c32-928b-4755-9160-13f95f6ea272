package com.stpl.tech.kettle.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.util.ArrayList;
import java.util.List;

@SuppressWarnings("serial")
@Entity
@Table(name = "IMAGE_URLS")
public class ImageUrls implements java.io.Serializable{

    private Integer id;

    private String url;

    private OrderComplaint orderComplaint;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name="ID", unique = true, nullable= false)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name="URL", unique = true, nullable = false)
    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name="ORDER_ID",nullable = false)
    public OrderComplaint getOrderComplaint() {
        return orderComplaint;
    }

    public void setOrderComplaint(OrderComplaint orderComplaint) {
        this.orderComplaint = orderComplaint;
    }

}
