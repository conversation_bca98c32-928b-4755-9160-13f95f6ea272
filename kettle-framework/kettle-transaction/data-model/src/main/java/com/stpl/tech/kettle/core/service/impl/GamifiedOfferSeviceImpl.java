package com.stpl.tech.kettle.core.service.impl;

import com.stpl.tech.kettle.clevertap.domain.model.GameLeaderBoardDTO;
import com.stpl.tech.kettle.clevertap.service.CleverTapDataPushService;
import com.stpl.tech.kettle.clevertap.util.CleverTapConstants;
import com.stpl.tech.kettle.clevertap.util.CleverTapEvents;
import com.stpl.tech.kettle.core.OfferSource;
import com.stpl.tech.kettle.core.cache.CampaignCache;
import com.stpl.tech.kettle.core.data.vo.SubscriptionProduct;
import com.stpl.tech.kettle.core.service.CustomerOfferManagementService;
import com.stpl.tech.kettle.core.service.DroolsDecisionService;
import com.stpl.tech.kettle.core.service.GamifiedOfferService;
import com.stpl.tech.kettle.core.service.OrderManagementService;
import com.stpl.tech.kettle.customer.service.CashBackService;
import com.stpl.tech.kettle.customer.service.CustomerService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.dao.GamifiedOfferDao;
import com.stpl.tech.kettle.data.dao.OrderManagementDao;
import com.stpl.tech.kettle.data.model.CashPacketData;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.DroolsCustomerProperties;
import com.stpl.tech.kettle.data.model.GameLeaderBoard;
import com.stpl.tech.kettle.data.model.GameLeaderBoardResponse;
import com.stpl.tech.kettle.data.model.GamifiedOfferDetail;
import com.stpl.tech.kettle.data.model.GamifiedOfferRequest;
import com.stpl.tech.kettle.data.model.GamifiedOfferResponse;
import com.stpl.tech.kettle.data.model.GamifiedOfferType;
import com.stpl.tech.kettle.data.model.SpecialOfferType;
import com.stpl.tech.kettle.data.model.SubscriptionPlan;
import com.stpl.tech.kettle.domain.model.CreateNextOfferRequest;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.NextOffer;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.offer.dao.impl.OfferDescriptionMetadataDao;
import com.stpl.tech.master.data.model.CouponDetailData;
import com.stpl.tech.master.data.model.OfferDescriptionMetadata;
import com.stpl.tech.master.domain.model.CampaignDetail;
import com.stpl.tech.master.domain.model.CampaignDetailResponse;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.DroolVersionDomain;
import com.stpl.tech.master.domain.model.OfferType;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.jms.JMSException;
import java.io.IOException;
import java.math.BigDecimal;
import java.security.SecureRandom;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;

@Service
@Slf4j
public class GamifiedOfferSeviceImpl implements GamifiedOfferService {

    @Autowired
    private CustomerService customerService;

    @Autowired
    private DroolsDecisionService droolsOfferDecisionService;

    @Autowired
    private CampaignCache campaignCache;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private GamifiedOfferDao gamifiedOfferDao;

    @Autowired
    private OrderManagementService orderManagementService;

    @Autowired
    private OrderManagementDao orderManagementDao;

    @Autowired
    private CashBackService cashBackService;

    @Autowired
    private EnvironmentProperties props;

    @Autowired
    private CleverTapDataPushService cleverTapDataPushService;

    @Autowired
    private OfferDescriptionMetadataDao offerDescriptionMetadataDao;

    @Autowired
    private CustomerOfferManagementService offerService;

    private static final String LEADS = "LEADS";


    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<GamifiedOfferResponse> getGamifiedOffer(GamifiedOfferRequest request,String source) throws DataUpdationException, JMSException, IOException {
        setGamifiedOfferInitialData(request);
        if(Objects.isNull(request.getCampaignDetailResponse())
                || Objects.isNull(request.getCampaignDetailResponse().getCampaignId())){
            log.info("SPECIAL_OFFER ::: No campaign found for token ::: {}",request.getCampaignToken());
            return getNoOfferResponse();
        }
        updateCustomerInfo(request);
        setActiveOfferData(request);
        List<GamifiedOfferResponse> gamifiedOfferResponseList = getOfferListToShow(request);
        String eventName = CleverTapEvents.UNLOCK_DEAL_POS;
        if(!StringUtils.isEmpty(source) && AppConstants.SCRATCH_CARD.equalsIgnoreCase(source)){
            eventName = CleverTapEvents.SCRATCH_CARD;
        }
        if("REFERRAL".equals(request.getUtmSource()) && request.getActiveGameOffer().isEmpty() && !gamifiedOfferResponseList.isEmpty()){
            addScoreForReferral(request.getUtmMedium(), request.getCampaignDetailResponse().getCampaignId());
        }
        if(!CollectionUtils.isEmpty(gamifiedOfferResponseList)) {
            Map<String, Object> eventData = new HashMap<>();
            Map<String, Object> userProfiles = new HashMap<>();
            GamifiedOfferResponse data = gamifiedOfferResponseList.get(0);
            if(Objects.nonNull(data) && !StringUtils.isEmpty(data.getOfferCode()) && !GamifiedOfferType.NO_OFFER.name().equalsIgnoreCase(data.getOfferCode())){
                eventData.put("unlockDealCouponCode",data.getOfferCode());
                eventData.put("unlockDealCouponText",data.getText());
                eventData.put("unlockDealCouponEndDate",data.getValidityTill());
                if (OfferSource.DIGITAL.equals(request.getOfferSource()) && request.getUtmSource().equals("wa")) {
                    try {
                        userProfiles.put("unlockDealCouponCode",data.getOfferCode());
                        userProfiles.put("unlockDealCouponText",data.getText());
                        userProfiles.put("unlockDealCouponEndDate",data.getValidityTill());
                        cleverTapDataPushService.uploadProfileAttributes(request.getCustomer().getId(),AppUtils.getCurrentTimestamp().toInstant().getEpochSecond()
                                ,CleverTapConstants.REGULAR,userProfiles);
                        cleverTapDataPushService.publishCustomEvent(request.getCustomer().getId(), CleverTapEvents.UNLOCK_DEAL,
                                AppUtils.getCurrentTimestamp().toInstant().getEpochSecond(), CleverTapConstants.REGULAR, eventData);
                    } catch (Exception e) {
                        log.info("Error in Pushing Event to clever tap for CustomerId : {}", request.getCustomer().getId(), e.getMessage());
                    }
                } else {
                    userProfiles.put("unlockDealPosCouponCode",data.getOfferCode());
                    userProfiles.put("unlockDealPosCouponText",data.getText());
                    userProfiles.put("unlockDealPosCouponEndDate",data.getValidityTill());
                    try {
                        cleverTapDataPushService.uploadProfileAttributes(request.getCustomer().getId(),AppUtils.getCurrentTimestamp().toInstant().getEpochSecond()
                                ,CleverTapConstants.REGULAR,userProfiles);
                        cleverTapDataPushService.publishCustomEvent(request.getCustomer().getId(), eventName,
                                AppUtils.getCurrentTimestamp().toInstant().getEpochSecond(), CleverTapConstants.REGULAR, eventData);
                    } catch (Exception e) {
                        log.info("Error in Pushing Event to clever tap for CustomerId : {}", request.getCustomer().getId(), e.getMessage());
                    }
                }
            }
        }

        return gamifiedOfferResponseList;
    }

    private void setActiveOfferData(GamifiedOfferRequest request) {
        request.setActiveGameOffer(orderManagementDao.getActiveGameOffer(request.getCustomer().getContactNumber(),
                request.getCampaignDetailResponse().getCampaignId(), request.getOfferSource().name(),campaignCache));
    }

    private void setGamifiedOfferInitialData(GamifiedOfferRequest request){
        Customer customer = customerService.getCustomer(request.getContactNumber());
        CampaignDetailResponse campaignResponse = campaignCache.getCampaignByToken(request.getCampaignToken(), AppConstants.ACTIVE);
        request.setCustomer(customer);
        request.setCampaignDetailResponse(campaignResponse);
        if(Objects.isNull(request.getOfferSource())){
            request.setOfferSource("CRM_APP".equals(request.getUtmMedium())
                    ? OfferSource.POS
                    : OfferSource.DIGITAL);
        }
    }

    private void updateCustomerInfo(GamifiedOfferRequest request){
        if((Objects.isNull(request.getCustomer().getEmailId()) && Objects.nonNull(request.getEmail())
                && !masterDataCache.getAllUnitsEmailId().contains(request.getEmail()))
                || (Objects.isNull(request.getCustomer().getOptWhatsapp()) && Objects.nonNull(request.getWhatsappOpt()))){
            CustomerInfo info = customerService.getCustomerInfoObject(request.getCustomer().getContactNumber());
            info.setEmailId(request.getEmail());
            log.info("SPECIAL_OFFER  ::: adding customer email ::: {}",request.getEmail());
            if(Objects.isNull(request.getCustomer().getOptWhatsapp()) && Objects.nonNull(request.getWhatsappOpt())){
                info.setOptWhatsapp(AppUtils.setStatus(request.getWhatsappOpt()));
                log.info("SPECIAL_OFFER ::: setting whatsapp status ::: {}",AppUtils.setStatus((request.getWhatsappOpt())));
            }
            orderManagementDao.update(info);
        }
    }

    private boolean shouldUseOldOffer(String offerType, GamifiedOfferRequest request){
        if(!request.getActiveGameOffer().containsKey(offerType)){
            return false;
        }
        int noOfOffers = request.getActiveGameOffer().get(offerType).size();
        if(GamifiedOfferType.CHAAYOS_CASH.name().equals(offerType)
                && request.getActiveGameOffer().containsKey(offerType)
                && BigDecimal.ZERO.compareTo(request.getActiveGameOffer().get(offerType).get(0).getRemainingChaayosCash()) < 0){
            return true;
        } else if(request.getActiveGameOffer().containsKey(offerType)
                && !request.getActiveGameOffer().get(offerType).get(noOfOffers-1).isOfferApplied()){
            return true;
        }
        return false;
    }

    private boolean isAlreadyAllotedOffer(String offerType,GamifiedOfferRequest request, int campaignId){
        if(!request.getActiveGameOffer().containsKey(offerType)){
            return false;
        }
        if((GamifiedOfferType.CHAAYOS_CASH.name().equals(offerType) || GamifiedOfferType.MEMBERSHIP.name().equals(offerType))
                && request.getActiveGameOffer().containsKey(offerType)){
            return true;
        }else if((GamifiedOfferType.NBO.name().equals(offerType) || GamifiedOfferType.DNBO.name().equals(offerType))
                && isOfferWithCampaignIdPresent(offerType, request.getActiveGameOffer().get(offerType), campaignId)){
            return true;
        }
        return false;
    }

    private boolean isOfferWithCampaignIdPresent(String offerType, List<GamifiedOfferResponse> activeOffers,Integer campaignId){
        for(GamifiedOfferResponse offer : activeOffers){
            if(Objects.nonNull(campaignId) && campaignId.equals(offer.getOfferCampaignId())) {
                return true;
            }
        }
        return false;
    }


    private List<GamifiedOfferResponse> getOfferListToShow(GamifiedOfferRequest request) throws DataUpdationException, JMSException, IOException {
        List<GamifiedOfferResponse> gamifiedOfferResponseList = new ArrayList<>();
        log.info("Existing offer found for campaignId : {} with size:{}",
                request.getCampaignDetailResponse().getCampaignId(),request.getActiveGameOffer().size());
        DroolsCustomerProperties properties = gamifiedOfferDao.getCustomerProperties(request.getCustomer().getId(),AppConstants.CHAAYOS_BRAND_ID);
        String[] offerStrings = droolsOfferDecisionService.getOfferString(properties, OfferSource.POS.equals(request.getOfferSource()),
                    request.getOfferType(), Objects.nonNull(request.getUnitId()) ? request.getUnitId() : null).getKey().split(",");
        if(!StringUtils.isEmpty(properties.getRuleName()) && LEADS.equalsIgnoreCase(properties.getRuleName())){
            List<String> list = Arrays.asList(offerStrings);
            Collections.shuffle(list);
            offerStrings = list.toArray(new String[0]);
        }
        for(String offerString : offerStrings){
            String offerType = offerString.split("#")[0];
            Integer campaignId = null;
            if(GamifiedOfferType.NBO.name().equals(offerType) || GamifiedOfferType.DNBO.name().equals(offerType)) {
                campaignId = Integer.parseInt(offerString.split("#")[1]);
            }
            if(shouldUseOldOffer(offerType, request)) {
                int noOfOffers = request.getActiveGameOffer().get(offerType).size();
                GamifiedOfferResponse response = request.getActiveGameOffer().get(offerType).get(noOfOffers - 1);
                if(GamifiedOfferType.NBO.name().equals(offerType)){
                    orderManagementService.getAdditionalOfferData(response);
                }
                try {
                    HashSet<Integer> offerIds = new HashSet<>();
                    CouponDetailData couponDetailData = offerService.getCouponDetailData(response.getOfferCode());
                    if(Objects.nonNull(couponDetailData)){
                        offerIds.add(couponDetailData.getOfferDetail().getOfferDetailId());
                    }
                    List<OfferDescriptionMetadata> offerDescriptionMetadataList = offerDescriptionMetadataDao.findByOfferIdInAndStatus(offerIds,AppConstants.ACTIVE);
                    if(!CollectionUtils.isEmpty(offerDescriptionMetadataList)){
                        offerDescriptionMetadataList.sort(Comparator.comparing(item -> Integer.parseInt(item.getSequenceNumber())));
                        response.setOfferTextToShow(offerDescriptionMetadataList);
                    }
                }catch (Exception e){
                    log.info("Error in setting offerText for coupon code : {}",response.getCouponCode());
                }
                gamifiedOfferResponseList.add(response);
                break;
            } else if(!isAlreadyAllotedOffer(offerType,request,campaignId)) {
                GamifiedOfferResponse gamifiedOfferResponse = getGamifiedOffer(request.getCustomer(),request.getCampaignDetailResponse().getCampaignId(),
                        offerString,request,OfferSource.POS.equals(request.getOfferSource()));
                if(Objects.nonNull(gamifiedOfferResponse) && !GamifiedOfferType.NO_OFFER.name().equals(gamifiedOfferResponse.getOfferType())) {
                    gamifiedOfferResponseList.add(gamifiedOfferResponse);
                    break;
                }
            }
        }
        if(gamifiedOfferResponseList.isEmpty()){
            gamifiedOfferResponseList.add(new GamifiedOfferResponse(GamifiedOfferType.NO_OFFER.name()));
        }
        return gamifiedOfferResponseList;
    }

    private List<GamifiedOfferResponse> getOfferResponseList(GamifiedOfferRequest request) throws DataUpdationException, JMSException, IOException {
        List<GamifiedOfferResponse> gamifiedOfferResponseList = new ArrayList<>();
        log.info("Existing offer found for campaignId : {} with size:{}",
                request.getCampaignDetailResponse().getCampaignId(),request.getActiveGameOffer().size());
        if(request.getActiveGameOffer().containsKey(GamifiedOfferType.MEMBERSHIP.name())){
            GamifiedOfferResponse response = request.getActiveGameOffer().get(GamifiedOfferType.MEMBERSHIP.name()).get(0);
            updateGameLeaderBoard(request, response);
            gamifiedOfferResponseList.add(response);
            return gamifiedOfferResponseList;
        } else if (request.getActiveGameOffer().containsKey(GamifiedOfferType.NBO.name())) {
            DroolsCustomerProperties properties = gamifiedOfferDao.getCustomerProperties(request.getCustomer().getId(),AppConstants.CHAAYOS_BRAND_ID);
            String[] offerStrings = droolsOfferDecisionService.getOfferString(properties, OfferSource.POS.equals(request.getOfferSource()),
                    request.getOfferType(),Objects.nonNull(request.getUnitId()) ? request.getUnitId() : null).getKey().split(",");
            GamifiedOfferResponse existingOffer = null;
            List<String> validOfferString = new ArrayList<>();
            for(String offerString : offerStrings){
                Integer campaignId = Integer.parseInt(String.valueOf(offerString.split("#")[1]));
                boolean isOfferStringUsed = false;
                for(GamifiedOfferResponse response : request.getActiveGameOffer().get(GamifiedOfferType.NBO.name())){
                    if(!response.isOfferApplied()){
                        existingOffer = response;
                    }
                    if(campaignId.equals(response.getOfferCampaignId())){
                        isOfferStringUsed = true;
                    }
                }
                if(!isOfferStringUsed){
                    validOfferString.add(offerString);
                }
            }
            return offerDecisionForNBO(request, existingOffer, validOfferString);
        }else{
            log.info("Getting offer from cutsomer drool properties for customerId :::::{}", request.getCustomer().getId());
            DroolsCustomerProperties properties = gamifiedOfferDao.getCustomerProperties(request.getCustomer().getId(),AppConstants.CHAAYOS_BRAND_ID);
            String[] offerStrings = droolsOfferDecisionService.getOfferString(properties, OfferSource.POS.equals(request.getOfferSource()),
                    request.getOfferType(),Objects.nonNull(request.getUnitId()) ? request.getUnitId() : null).getKey().split(",");
            gamifiedOfferResponseList.add(getGamifiedOffer(request.getCustomer(), request.getCampaignDetailResponse().getCampaignId(), offerStrings[0], request, OfferSource.POS.equals(request.getOfferSource())));
        }
        return gamifiedOfferResponseList;
    }

    private void updateGameLeaderBoard(GamifiedOfferRequest request, GamifiedOfferResponse existingOffer){
        GameLeaderBoard gameLeaderBoard = updateLeaderBoard(request.getCustomer(), request.getCampaignDetailResponse().getCampaignId(), request);
        if(Objects.isNull(existingOffer.getRefCode()) && Objects.nonNull(gameLeaderBoard)){
            existingOffer.setRefCode(gameLeaderBoard.getRefCode());
        }
    }

    private List<GamifiedOfferResponse> offerDecisionForNBO(GamifiedOfferRequest request, GamifiedOfferResponse existingOffer, List<String> valifOfferString) throws DataUpdationException, JMSException, IOException {
        List<GamifiedOfferResponse> gamifiedOfferResponseList = new ArrayList<>();
        if(Objects.nonNull(existingOffer) && !existingOffer.isOfferApplied()){
            if(Objects.nonNull(existingOffer.getOfferCampaignId())){
                existingOffer.setCrmAppBannerUrl(campaignCache.getCampaign(existingOffer.getOfferCampaignId()).getCrmAppBannerUrl());
            }
            updateGameLeaderBoard(request, existingOffer);
            gamifiedOfferResponseList.add(existingOffer);
        }
        else if(Objects.isNull(existingOffer) && !valifOfferString.isEmpty()){
            gamifiedOfferResponseList.add(getGamifiedOffer(request.getCustomer(), request.getCampaignDetailResponse().getCampaignId(), valifOfferString.get(0), request, OfferSource.POS.equals(request.getOfferSource())));
        }else{
            log.info("Returning no offer as all offers were already given");
            if(OfferSource.DIGITAL.equals(request.getOfferSource())){
                gamifiedOfferResponseList.add(new GamifiedOfferResponse(GamifiedOfferType.NO_OFFER.name()));
            }
        }
        return gamifiedOfferResponseList;
    }

    private String createRandomCode(int codeLength){
        char[] chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890".toCharArray();
        StringBuilder sb = new StringBuilder();
        Random random = new SecureRandom();
        for (int i = 0; i < codeLength; i++) {
            char c = chars[random.nextInt(chars.length)];
            sb.append(c);
        }
        return sb.toString();
    }

    private GameLeaderBoard updateLeaderBoard(Customer customer, int campaignId, GamifiedOfferRequest request){
        log.info("GAME_SCORE: {}",JSONSerializer.toJSON(request));
        if(Objects.nonNull(request.getGameScore())){
            GameLeaderBoard gameLeaderBoard = orderManagementDao.getActiveLeaderboardEntry(customer.getId(), campaignId);
            if(Objects.isNull(gameLeaderBoard)){
                int maxIteration = 3;
                String validRefCode=null;
                while(Objects.isNull(validRefCode) && maxIteration > 0){
                    List<String> refCodeList = new ArrayList<>();
                    refCodeList.add(createRandomCode(6));
                    refCodeList.add(createRandomCode(6));
                    refCodeList.add(createRandomCode(6));
                    refCodeList.add(createRandomCode(6));
                    validRefCode = orderManagementDao.getValidRefCode(refCodeList, campaignId);
                    maxIteration = maxIteration - 1;
                    if(maxIteration == 0 && Objects.isNull(validRefCode)){
                        log.error("Maximum iteration reached to generated valid ref code");
                        return null;
                    }
                }
                GameLeaderBoard newLeaderBoard = GameLeaderBoard.builder()
                        .campaignId(campaignId)
                        .customerId(customer.getId())
                        .gamePlayFrequency(1)
                        .gameScore(request.getGameScore())
                        .refCode(validRefCode)
                        .refScore(0)
                        .userName(customer.getContactNumber().substring(0,4)+"XXXXXX")
                        .totalScore(request.getGameScore()).build();
                log.info("Leader Board : {}",JSONSerializer.toJSON(newLeaderBoard));
                orderManagementDao.add(newLeaderBoard);
                return newLeaderBoard;
            }else{
                gameLeaderBoard.setGameScore(Math.max(gameLeaderBoard.getGameScore(), request.getGameScore()));
                gameLeaderBoard.setTotalScore(gameLeaderBoard.getGameScore()+ gameLeaderBoard.getRefScore());
                gameLeaderBoard.setGamePlayFrequency(gameLeaderBoard.getGamePlayFrequency()+1);
                orderManagementDao.update(gameLeaderBoard);
                return gameLeaderBoard;
            }
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public GameLeaderBoardResponse getLeaderBoard(String contactNumber, String token, boolean getRank) {
        GameLeaderBoardResponse response = new GameLeaderBoardResponse();
        CampaignDetailResponse detailResponse = campaignCache.getCampaignByToken(token, AppConstants.ACTIVE);
        List<GameLeaderBoardDTO> leaderBoardList = orderManagementDao.getTop10Score(detailResponse.getCampaignId());
        response.setTop10Score(leaderBoardList);
        if(!getRank){
            return response;
        }
        Customer customer = customerService.getCustomer(contactNumber);
        if(Objects.isNull(customer)){
            return GameLeaderBoardResponse.builder().isInTop10(false).yourScore(null).top10Score(new ArrayList<>()).build();
        }
        GameLeaderBoard gameLeaderBoard = orderManagementDao.getActiveLeaderboardEntry(customer.getId(), detailResponse.getCampaignId());
        boolean isInTop10 =false;
        for(GameLeaderBoardDTO dto : leaderBoardList){
            if(Objects.nonNull(gameLeaderBoard.getRefCode()) && gameLeaderBoard.getRefCode().equals(dto.getRefCode())){
                isInTop10=true;
                break;
            }
        }
        response.setInTop10(isInTop10);
        Integer rank = orderManagementDao.getRankForContact(contactNumber, detailResponse.getCampaignId(), gameLeaderBoard.getTotalScore());
        response.setYourScore(GameLeaderBoardDTO.builder()
                .refCode(gameLeaderBoard.getRefCode())
                .gameScore(gameLeaderBoard.getGameScore())
                .gameScore(gameLeaderBoard.getGameScore())
                .name(gameLeaderBoard.getUserName())
                .totalScore(gameLeaderBoard.getTotalScore()).build());
        response.setRank(rank);
        return response;
    }

    @Override
    public Pair<Boolean, String> isGamifiedOfferExist(GamifiedOfferRequest request) {
        setGamifiedOfferInitialData(request);
        if(Objects.isNull(request.getCampaignDetailResponse())
                || Objects.isNull(request.getCampaignDetailResponse().getCampaignId())){
            log.info("SPECIAL_OFFER ::: No campaign found for token ::: {}",request.getCampaignToken());
            return new Pair<>(false,"PRE");
        }
        setActiveOfferData(request);
        DroolsCustomerProperties properties = gamifiedOfferDao.getCustomerProperties(request.getCustomer().getId(),AppConstants.CHAAYOS_BRAND_ID);
        Pair<String, String> droolData = droolsOfferDecisionService.getOfferString(properties, OfferSource.POS.equals(request.getOfferSource()),request.getOfferType(),
                Objects.nonNull(request.getUnitId()) ? request.getUnitId() : null);
        String[] offerStrings = droolData.getKey().split(",");
        properties.setOfferOccurrenceType(properties.getOfferOccurrenceType());
        if(request.getActiveGameOffer().containsKey(GamifiedOfferType.MEMBERSHIP.name())){
            return new Pair<>(true,properties.getOfferOccurrenceType());
        } else if (request.getActiveGameOffer().containsKey(GamifiedOfferType.NBO.name())) {
            GamifiedOfferResponse existingOffer = null;
            List<String> validOfferString = new ArrayList<>();
            for(String offerString : offerStrings){
                Integer campaignId = Integer.parseInt(String.valueOf(offerString.split("#")[1]));
                boolean isOfferStringUsed = false;
                for(GamifiedOfferResponse response : request.getActiveGameOffer().get(GamifiedOfferType.NBO.name())){
                    if(!response.isOfferApplied()){
                        existingOffer = response;
                    }
                    if(campaignId.equals(response.getOfferCampaignId())){
                        isOfferStringUsed = true;
                    }
                }
                if(!isOfferStringUsed){
                    validOfferString.add(offerString);
                }
            }
            if(Objects.nonNull(existingOffer) && !existingOffer.isOfferApplied()){
                return new Pair<>(true, properties.getOfferOccurrenceType());
            }
            else return new Pair<>(Objects.isNull(existingOffer) && !validOfferString.isEmpty(), properties.getOfferOccurrenceType());
        }else{
            return new Pair<>(true,properties.getOfferOccurrenceType());
        }
    }

    @Override
    public List<GamifiedOfferResponse> getOfferViaDrools(GamifiedOfferRequest request) throws DataUpdationException, JMSException, IOException {
        setGamifiedOfferInitialData(request);
        if(Objects.isNull(request.getCampaignDetailResponse())
                || Objects.isNull(request.getCampaignDetailResponse().getCampaignId())){
            log.info("SPECIAL_OFFER ::: No campaign found for token ::: {}",request.getCampaignToken());
            return getNoOfferResponse();
        }
        updateCustomerInfo(request);
//        setActiveOfferData(request);
        List<GamifiedOfferResponse> gamifiedOfferResponseList = getOfferResponseList(request);

        /*if("REFERRAL".equals(request.getUtmSource()) && request.getActiveGameOffer().isEmpty() && !gamifiedOfferResponseList.isEmpty()){
            addScoreForReferral(request.getUtmMedium(), request.getCampaignDetailResponse().getCampaignId());
        }*/
        return gamifiedOfferResponseList;
    }

    private void addScoreForReferral(String refCode, Integer campaignId){
        orderManagementDao.addReferralScore(refCode, campaignId);
    }

    private GamifiedOfferResponse getGamifiedOffer(Customer customer,  Integer campaignId, String offerString, GamifiedOfferRequest gameRequest, boolean isPosOffer) throws DataUpdationException, JMSException, IOException {
        String offerType = offerString.split("#")[0];
        if(GamifiedOfferType.NBO.name().equals(offerType)){
            CreateNextOfferRequest request = new CreateNextOfferRequest(customer.getId(), customer.getContactNumber(),
                    AppConstants.CHAAYOS_BRAND_ID, Integer.parseInt(offerString.split("#")[1]), gameRequest.getUtmSource(), gameRequest.getUtmMedium());
            return createGeneralNBOOffer(gameRequest ,request, customer, Integer.parseInt(offerString.split("#")[2]), isPosOffer, campaignId);
        }else if(GamifiedOfferType.DNBO.name().equals(offerType)){
            CreateNextOfferRequest request = new CreateNextOfferRequest(customer.getId(), customer.getContactNumber(),
                    AppConstants.CHAAYOS_BRAND_ID, Integer.parseInt(offerString.split("#")[1]), gameRequest.getUtmSource(), gameRequest.getUtmMedium());
            return createGeneralDNBOOffer(gameRequest , request, customer, Integer.parseInt(offerString.split("#")[2]), isPosOffer, campaignId);
        }else if(GamifiedOfferType.CHAAYOS_CASH.name().equals(offerType)){
            return allotFreeCash(customer, offerString, campaignId, gameRequest, isPosOffer);
        }else if(GamifiedOfferType.MEMBERSHIP.name().equals(offerType)){
            SubscriptionProduct product = getSubscriptionProduct(offerString.split("#")[1],new BigDecimal(offerString.split("#")[2]),
                    Integer.valueOf(offerString.split("#")[3]));
            return allotFreeMembership(gameRequest, customer, product, campaignId, Integer.parseInt(offerString.split("#")[4]), isPosOffer);
        }else{
            return new GamifiedOfferResponse(GamifiedOfferType.NO_OFFER.name());
        }
   }

    private GamifiedOfferResponse allotFreeCash(Customer customer,  String offerString, Integer campaignId, GamifiedOfferRequest request, boolean isPosOffer) throws JMSException, IOException {
        String[] offerDetail = offerString.split("#");
        CashPacketData cashPacketData = cashBackService.allotCashAsFreeGift(new BigDecimal(offerDetail[1]),customer.getContactNumber(),Integer.valueOf(offerDetail[2]),Integer.valueOf(offerDetail[3]),customer);
        if(Objects.isNull(cashPacketData)){
            return new GamifiedOfferResponse(GamifiedOfferType.CHAAYOS_CASH.name());
        }
        GamifiedOfferDetail gamifiedOfferDetail  = addGamifiedOfferDetail(GamifiedOfferType.CHAAYOS_CASH, cashPacketData, campaignCache.getCampaign(campaignId), customer, request, isPosOffer);
        return getOfferResponse(gamifiedOfferDetail, false, null, null);
    }

    private GamifiedOfferResponse createGeneralNBOOffer(GamifiedOfferRequest gameRequest ,CreateNextOfferRequest request, Customer customer, int lagDays, boolean isPosOffer, Integer gameCampaignId){
       NextOffer offer = orderManagementService.createGeneralOffer(request, customer, campaignCache.getCampaign(request.getCampaignId()), lagDays).getValue();
       if(Objects.isNull(offer)){
           return new GamifiedOfferResponse(GamifiedOfferType.NO_OFFER.name());
       }
       GamifiedOfferDetail offerDetail = addGamifiedOfferDetail(GamifiedOfferType.NBO, offer, campaignCache.getCampaign(gameCampaignId), customer, gameRequest,isPosOffer);
       return getOfferResponse(offerDetail, false, campaignCache.getCampaign(request.getCampaignId()), offer);
   }

   private GamifiedOfferResponse createGeneralDNBOOffer(GamifiedOfferRequest gameRequest ,CreateNextOfferRequest request,  Customer customer, int lagDays, boolean isPosOffer, Integer gameCampaignId){
        NextOffer offer = orderManagementService.createDeliveryGeneralOffer(request, customer, campaignCache.getCampaign(request.getCampaignId()), lagDays).getValue();
       if(Objects.isNull(offer)){
           return new GamifiedOfferResponse(GamifiedOfferType.NO_OFFER.name());
       }
       GamifiedOfferDetail offerDetail = addGamifiedOfferDetail(GamifiedOfferType.DNBO, offer, campaignCache.getCampaign(gameCampaignId), customer, gameRequest,isPosOffer);
       return getOfferResponse(offerDetail, false,campaignCache.getCampaign(request.getCampaignId()), offer);
   }

   private GamifiedOfferResponse allotFreeMembership(GamifiedOfferRequest request, Customer  customer,
                                                     SubscriptionProduct product, int campaignId, int lagDays, boolean isPosOffer) throws DataUpdationException {
       SubscriptionPlan plan = orderManagementService.createSubscription(product, customer, campaignId, request.getUtmSource(), lagDays);
       if(Objects.isNull(plan)){
           return new GamifiedOfferResponse(GamifiedOfferType.NO_OFFER.name());
       }
       GamifiedOfferDetail gamifiedOfferDetail = addGamifiedOfferDetail(GamifiedOfferType.MEMBERSHIP, plan,
               campaignCache.getCampaign(campaignId), customer, request,isPosOffer);
       return getOfferResponse(gamifiedOfferDetail, false, campaignCache.getCampaign(campaignId), null);
   }

    private SubscriptionProduct getSubscriptionProduct(String planCode,BigDecimal price,Integer validityInDays){
        Product product = masterDataCache.getSubscriptionSkuCodeDetail(planCode).getValue();
        SubscriptionProduct subscriptionProduct = new SubscriptionProduct();
        subscriptionProduct.setProductId(product.getId());
        subscriptionProduct.setProductName(product.getName());
        subscriptionProduct.setSubscriptionCode(planCode);
        subscriptionProduct.setDimensionCode("NONE");
        subscriptionProduct.setPrice(price);
        subscriptionProduct.setValidityInDays(validityInDays);
        return subscriptionProduct;
    }

    private GamifiedOfferDetail addGamifiedOfferDetail(GamifiedOfferType type, Object data, CampaignDetail campaignDetail, Customer customer, GamifiedOfferRequest request, boolean isPosOffer){
        try {
            GameLeaderBoard leaderBoard = updateLeaderBoard(customer, campaignDetail.getCampaignId(), request);
            GamifiedOfferDetail detail = new GamifiedOfferDetail();
            if(GamifiedOfferType.MEMBERSHIP.equals(type)){
                SubscriptionPlan plan = (SubscriptionPlan) data;
                CouponDetail couponDetail = masterDataCache.getSubscriptionSkuCodeDetail(plan.getSubscriptionPlanCode()).getKey();
                detail.setCouponCode(plan.getSubscriptionPlanCode());
                detail.setOfferText(couponDetail.getOffer().getText());
                detail.setStartDate(AppUtils.getDateString(plan.getPlanStartDate(), AppUtils.DATE_FORMAT_STRING));
                detail.setEndDate(AppUtils.getDateString(plan.getPlanEndDate(), AppUtils.DATE_FORMAT_STRING));
                detail.setSubscriptionPlanId(plan.getSubscriptionPlanId());
                detail.setTnc(couponDetail.getOffer().getTermsAndConditions());
            }else if(GamifiedOfferType.NBO.equals(type) || GamifiedOfferType.DNBO.equals(type)){
                NextOffer nextOffer = (NextOffer) data;
                detail.setCustomerCampaignOfferDetailId(nextOffer.getCustomerCampaignOfferDetailId());
                detail.setMaxUsage(nextOffer.getMaxUsage());
                detail.setStartDate(nextOffer.getValidityFrom());
                detail.setEndDate(nextOffer.getValidityTill());
                detail.setCouponCode(nextOffer.getOfferCode());
                detail.setOfferText(nextOffer.getText());
                detail.setTnc(nextOffer.getTnc());
            }else if(GamifiedOfferType.CHAAYOS_CASH.equals(type)){
                CashPacketData packetData = (CashPacketData) data;
                detail.setCashAmount(packetData.getInitialAmount());
                detail.setCouponCode("CHAAYOS_CASH");
                detail.setStartDate(AppUtils.getDateString(packetData.getActivationTime(), AppUtils.DATE_FORMAT_STRING));
                detail.setEndDate(AppUtils.getDateString(packetData.getExpirationDate(), AppUtils.DATE_FORMAT_STRING));
                detail.setCashPacketId(packetData.getCashPacketId());
                detail.setTnc(campaignCache.getCouponDetail("CHAAYOS_CASH").getOffer().getTermsAndConditions());
            }
            detail.setOfferType(type.name());
            detail.setRecordStatus(AppConstants.ACTIVE);
            detail.setCampaignStrategy(campaignDetail.getCampaignStrategy());
            detail.setCampaignId(campaignDetail.getCampaignId());
            detail.setContactNumber(customer.getContactNumber());
            detail.setCustomerId(customer.getId());
            detail.setUtmMedium(request.getUtmMedium());
            detail.setUtmSource(request.getUtmSource());
            detail.setRecordTime(AppUtils.getCurrentTimestamp());
            detail.setFlow(request.getFlow());
            detail.setOfferSource(isPosOffer ? OfferSource.POS.name() : OfferSource.DIGITAL.name() );
            detail.setRefCode(Objects.nonNull(leaderBoard) ? leaderBoard.getRefCode() : null);
            orderManagementDao.add(detail);
            return detail;
        }catch (Exception e){
            log.error("Error while adding offer ",e);
        }
        return null;
    }

    private GamifiedOfferResponse getOfferResponse(GamifiedOfferDetail offerDetail, Boolean isExistingOffer, CampaignDetail campaign, NextOffer nextOffer){
        if(Objects.isNull(offerDetail)){
            return new GamifiedOfferResponse(GamifiedOfferType.NO_OFFER.name());
        }
        GamifiedOfferResponse response = new GamifiedOfferResponse();
        if(GamifiedOfferType.NBO.name().equals(offerDetail.getOfferType()) || SpecialOfferType.DNBO.name().equals(offerDetail.getOfferType())){
            response.setMaxUsage(null);
        }else if(SpecialOfferType.CHAAYOS_CASH.name().equals(offerDetail.getOfferType())){
            response.setChaayosCash(offerDetail.getCashAmount());
        }
        response.setOfferCode(offerDetail.getCouponCode());
        response.setText(offerDetail.getOfferText());
        response.setValidityFrom(offerDetail.getStartDate());
        response.setValidityTill(offerDetail.getEndDate());
        response.setIsExistingOffer(isExistingOffer);
        response.setOfferType(offerDetail.getOfferType());
        response.setOfferTnCString(offerDetail.getTnc());
        response.setRefCode(offerDetail.getRefCode());
        response.setCouponCode(offerDetail.getCouponCode());
        response.setMinBillValue(nextOffer.getMinBillValue());
        response.setOfferValueType(nextOffer.getOfferValueType());
        response.setOfferValue(nextOffer.getOfferValue());
        response.setProductList(nextOffer.getProductList());
        if(Objects.nonNull(campaign)){
            response.setCampaignId(campaign.getCampaignId());
            response.setCampaignStrategy(campaign.getCampaignStrategy());
        }
        if(Objects.nonNull(nextOffer)){
            response.setCrmAppBannerUrl(nextOffer.getCrmAppBannerUrl());
        }
        try {
            HashSet<Integer> offerIds = new HashSet<>();
            CouponDetailData couponDetailData = offerService.getCouponDetailData(offerDetail.getCouponCode());
            if(Objects.nonNull(couponDetailData)){
                offerIds.add(couponDetailData.getOfferDetail().getOfferDetailId());
            }
            List<OfferDescriptionMetadata> offerDescriptionMetadataList = offerDescriptionMetadataDao.findByOfferIdInAndStatus(offerIds,AppConstants.ACTIVE);
            if(!CollectionUtils.isEmpty(offerDescriptionMetadataList)){
                offerDescriptionMetadataList.sort(Comparator.comparing(item -> Integer.parseInt(item.getSequenceNumber())));
                response.setOfferTextToShow(offerDescriptionMetadataList);
            }
        }catch (Exception e){
            log.info("Error in setting offerText for coupon code : {}",offerDetail.getCouponCode());
        }
        return response;
    }

    private List<GamifiedOfferResponse> getNoOfferResponse(){
        List<GamifiedOfferResponse> responseList = new ArrayList<>();
        responseList.add(new GamifiedOfferResponse(GamifiedOfferType.NO_OFFER.name()));
        return responseList;
    }
}
