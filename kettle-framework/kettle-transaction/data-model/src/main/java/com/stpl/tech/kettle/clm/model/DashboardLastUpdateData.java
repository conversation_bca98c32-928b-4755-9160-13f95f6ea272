package com.stpl.tech.kettle.clm.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "DASHBOARD_DATA_LAST_UPDATE")
public class DashboardLastUpdateData {
    private Integer id;
    private Date zfr;
    private Date zgv;
    private Date scr;
    private Date zcr;
    private Date zors;
    private Date zilr;
    private Date zomatoOrs;
    private Date zomatoRejections;

    private Date swiggyRatings;
    private Date swiggyOrsRejection;
    private Date zomatoPromoRedemption;

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "ID", unique = true, nullable = false)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "ZOMATO_FOOD_RATING", nullable = true)
    public Date getZfr() {
        return zfr;
    }

    public void setZfr(Date zfr) {
        this.zfr = zfr;
    }

    @Column(name = "GRID_VISIBILITY", nullable = true)
    public Date getZgv() {
        return zgv;
    }

    public void setZgv(Date zgv) {
        this.zgv = zgv;
    }

    @Column(name = "SWIGGY_COUPON_REDEMPTION", nullable = true)
    public Date getScr() {
        return scr;
    }

    public void setScr(Date scr) {
        this.scr = scr;
    }


    @Column(name = "FOOD_RATING_ZOMATO_ORS", nullable = true)
    public Date getZors() {
        return zors;
    }

    public void setZors(Date zors) {
        this.zors = zors;
    }

    @Column(name = "ZOMATO_ITEM_LEVEL_RATING", nullable = true)
    public Date getZilr() {
        return zilr;
    }

    public void setZilr(Date zilr) {
        this.zilr = zilr;
    }

    @Column(name = "ZOMATO_COUPON_REDEMPTION", nullable = true)
    public Date getZcr() {
        return zcr;
    }

    public void setZcr(Date zcr) {
        this.zcr = zcr;
    }

    @Column(name = "ZOMATO_ORS")
    public Date getZomatoOrs() {
        return zomatoOrs;
    }
    public void setZomatoOrs(Date zomatoOrs) {
        this.zomatoOrs = zomatoOrs;
    }

    @Column(name = "ZOMATO_REJECTIONS")
    public Date getZomatoRejections() {
        return zomatoRejections;
    }
    public void setZomatoRejections(Date zomatoRejections) {
        this.zomatoRejections = zomatoRejections;
    }
    @Column(name = "SWIGGY_RATINGS")
    public Date getSwiggyRatings() {
        return swiggyRatings;
    }
    public void setSwiggyRatings(Date swiggyRatings) {
        this.swiggyRatings = swiggyRatings;
    }

    @Column(name = "SWIGGY_ORS_REJECTION")
    public Date getSwiggyOrsRejection() {
        return swiggyOrsRejection;
    }
    public void setSwiggyOrsRejection(Date swiggyOrsRejection) {
        this.swiggyOrsRejection = swiggyOrsRejection;
    }

    @Column(name = "ZOMATO_PROMO_REDEMPTION", nullable = true)
    public Date getZomatoPromoRedemption() {
        return zomatoPromoRedemption;
    }
    public void setZomatoPromoRedemption(Date zomatoPromoRedemption) {
        this.zomatoPromoRedemption = zomatoPromoRedemption;
    }
}

