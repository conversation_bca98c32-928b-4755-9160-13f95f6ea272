package com.stpl.tech.kettle.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

@Entity
@Table(name = "EXTERNAL_PARTNER_CARD_DETAIL")
public class ExternalPartnerCardDetail implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 3483071903358155160L;

	private Integer cardDetailId;
	private String externalOrderId;
	private String partnerCardNumber;
	private String partnerCode;
	private String requestSource;
	private String requestStatus;
	private Date requestTime;
	private Date responseTime;
	private String partnerTransactionId;
	private String redirectUrl;
	private String cancelledBy;
	private String cancellationReason;
	private Date cancellationTime;
	private String customerName;
	private Integer customerId;
	private BigDecimal transactionAmount;
	private String cardNumber;
	private byte[] response;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "CARD_DETAIL_ID", unique = true, nullable = false)
	public Integer getCardDetailId() {
		return cardDetailId;
	}

	public void setCardDetailId(Integer cardDetailId) {
		this.cardDetailId = cardDetailId;
	}

	@Column(name = "EXTERNAL_ORDER_ID", nullable = false)
	public String getExternalOrderId() {
		return externalOrderId;
	}

	public void setExternalOrderId(String generatedOrderId) {
		this.externalOrderId = generatedOrderId;
	}

	@Column(name = "PARTNER_CARD_NUMBER", nullable = false)
	public String getPartnerCardNumber() {
		return partnerCardNumber;
	}

	public void setPartnerCardNumber(String partnerCardNumber) {
		this.partnerCardNumber = partnerCardNumber;
	}

	@Column(name = "PARTNER_CODE", nullable = false)
	public String getPartnerCode() {
		return partnerCode;
	}

	public void setPartnerCode(String partnerCode) {
		this.partnerCode = partnerCode;
	}

	@Column(name = "REQUEST_SOURCE", nullable = true)
	public String getRequestSource() {
		return requestSource;
	}

	public void setRequestSource(String requestSource) {
		this.requestSource = requestSource;
	}

	@Column(name = "REQUEST_STATUS", nullable = true)
	public String getRequestStatus() {
		return requestStatus;
	}

	public void setRequestStatus(String requestedStaus) {
		this.requestStatus = requestedStaus;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "REQUEST_TIME", nullable = true, length = 19)
	public Date getRequestTime() {
		return requestTime;
	}

	public void setRequestTime(Date requestTime) {
		this.requestTime = requestTime;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "RESPONSE_TIME", nullable = true, length = 19)
	public Date getResponseTime() {
		return responseTime;
	}

	public void setResponseTime(Date responseTime) {
		this.responseTime = responseTime;
	}

	@Column(name = "PARTNER_TRANSACTION_ID", nullable = true)
	public String getPartnerTransactionId() {
		return partnerTransactionId;
	}

	public void setPartnerTransactionId(String partnerTransactionId) {
		this.partnerTransactionId = partnerTransactionId;
	}

	@Column(name = "REDIRECT_URL", nullable = true)
	public String getRedirectUrl() {
		return redirectUrl;
	}

	public void setRedirectUrl(String value) {
		this.redirectUrl = value;
	}

	@Column(name = "CANCELLED_BY", nullable = true)
	public String getCancelledBy() {
		return cancelledBy;
	}

	public void setCancelledBy(String canceledBy) {
		this.cancelledBy = canceledBy;
	}

	@Column(name = "CANCELLATION_REASON", nullable = true)
	public String getCancellationReason() {
		return cancellationReason;
	}

	public void setCancellationReason(String cancelationReason) {
		this.cancellationReason = cancelationReason;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CANCELLATION_TIME", nullable = true, length = 19)
	public Date getCancellationTime() {
		return cancellationTime;
	}

	public void setCancellationTime(Date cancelationTime) {
		this.cancellationTime = cancelationTime;
	}

	/**
	 * @return the customerName
	 */
	@Column(name = "CUSTOMER_NAME", nullable = false)
	public String getCustomerName() {
		return customerName;
	}

	/**
	 * @param customerName the customerName to set
	 */
	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	/**
	 * @return the customerId
	 */
	@Column(name = "CUSTOMER_ID", nullable = false)
	public Integer getCustomerId() {
		return customerId;
	}

	/**
	 * @param customerId the customerId to set
	 */
	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}

	/**
	 * @return the transactionAmount
	 */
	@Column(name = "TRANSACTION_AMOUNT", precision = 10, nullable = true)
	public BigDecimal getTransactionAmount() {
		return transactionAmount;
	}

	/**
	 * @param transactionAmount the transactionAmount to set
	 */
	public void setTransactionAmount(BigDecimal transactionAmount) {
		this.transactionAmount = transactionAmount;
	}

	@Column(name = "CARD_NUMBER", nullable = true)
	public String getCardNumber() {
		return cardNumber;
	}

	public void setCardNumber(String cardNumber) {
		this.cardNumber = cardNumber;
	}

	@Column(name = "RESPONSE_RESULT", nullable = true, columnDefinition="blob")
	public byte[] getResponse() {
		return response;
	}

	public void setResponse(byte[] response) {
		this.response = response;
	}

}
