package com.stpl.tech.kettle.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Entity
@Table (name = "SUPERU_ORDER_LEVEL_DATA")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class SuperUOrderLevelData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ORDER_LEVEL_ID")
    Integer orderLevelId;

    @Column(name = "ORDER_ID")
    Integer orderId;
    @Column(name = "UNIT_ID")
    Integer unitId;

    @Column(name = "BILLING_SERVER_TIME")
    Date billingServerTime;

    @Column(name = "FEEDBACK")
    String feedback;
    @Column(name = "ORDER_RATING")
    BigDecimal orderRating;
    @Column(name = "IS_APPLICABLE")
    Boolean isApplicable = true;
    @Column(name = "IS_FEEDBACK_READ")
    Boolean isFeedbackRead = false;
    @Column(name = "ORDER_DETAILS")
    String orderDetails;

    @Column(name = "CUSTOMER_NAME")
    String customerName;
    @OneToMany(mappedBy = "superUOrderLevelData",cascade = CascadeType.ALL,fetch = FetchType.EAGER)
    List<SuperUOrderRatingData> orderRatingData;

}
