package com.stpl.tech.kettle.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;

@Entity
@Table(name = "UNIT_TABLE_MAPPING")
public class UnitTableMappingDetail {

	private int tableRequestId;
	private int unitId;
	private int tableNumber;
	private Integer customerId;
	private String contact;
	private String customerName;
	private int totalOrders;
	private int totalAmount;
	private String tableStatus;
	private List<TableOrderMappingDetail> orders;
	private Integer settledOrderId;


	@Column(name = "SETTLEMENT_ORDER_ID",nullable = true)
	public Integer getSettledOrderId() {
		return settledOrderId;
	}

	public void setSettledOrderId(Integer settledOrderId) {
		this.settledOrderId = settledOrderId;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "TABLE_REQUEST_ID", unique = true, nullable = false)
	public int getTableRequestId() {
		return tableRequestId;
	}

	public void setTableRequestId(int tableRequestId) {
		this.tableRequestId = tableRequestId;
	}

	@Column(name = "UNIT_ID", nullable = false)
	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	@Column(name = "TABLE_NUMBER", nullable = false)
	public int getTableNumber() {
		return tableNumber;
	}

	public void setTableNumber(int tableNumber) {
		this.tableNumber = tableNumber;
	}

	@Column(name = "CUSTOMER_ID", nullable = true)
	public Integer getCustomerId() {
		return customerId;
	}

	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}
	
	@Column(name = "CONTACT", nullable = true)
	public String getContact() {
		return contact;
	}

	public void setContact(String contact) {
		this.contact = contact;
	}

	@Column(name = "CUSTOMER_NAME", nullable = true)
	public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	@Column(name = "TOTAL_ORDERS", nullable = false)
	public int getTotalOrders() {
		return totalOrders;
	}

	public void setTotalOrders(int totalOrders) {
		this.totalOrders = totalOrders;
	}

	@Column(name = "TOTAL_AMOUNT", nullable = false)
	public int getTotalAmount() {
		return totalAmount;
	}

	public void setTotalAmount(int totalAmount) {
		this.totalAmount = totalAmount;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "tableRequestId")
	public List<TableOrderMappingDetail> getOrders() {
		return orders;
	}

	public void setOrders(List<TableOrderMappingDetail> orders) {
		this.orders = orders;
	}

	@Column(name = "TABLE_STATUS", nullable = false)
	public String getTableStatus() {
		return tableStatus;
	}

	public void setTableStatus(String tableStatus) {
		this.tableStatus = tableStatus;
	}

}
