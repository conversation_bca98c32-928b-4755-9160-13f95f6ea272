package com.stpl.tech.kettle.stock.dao;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.stpl.tech.kettle.core.data.vo.UnitTimeStockData;
import com.stpl.tech.kettle.data.model.PartnerUnitProductStockData;
import com.stpl.tech.kettle.data.model.UnitProductStockEventDataDetail;
import com.stpl.tech.master.data.dao.AbstractDao;
import com.stpl.tech.master.domain.model.Pair;
import org.springframework.security.core.parameters.P;

public interface UnitProductStockDataDao extends AbstractDao {

	public void deleteData(Date calculationDate, Integer unitId);

	List<UnitProductStockEventDataDetail> findAllByUnitIdAndEventTimeStampOrderByProductIdAsc(Integer unitId, Date previousDate);

	Map<String, PartnerUnitProductStockData> getPreviousDayStockData(int unit, Date businessDate);

	public Map<Integer, UnitTimeStockData> getUnitClosingTimeMap(Date previousDate);

	public Map<Integer, Map<Integer,Map<String,String>>> getLatestPreviousStockEvent(Date eventTimestamp);

	public Map<Integer,Map<Date,Boolean>> getStockInSCMData(int unitId, Date businessDate,Date lastDayCloseTime , Date currentDayCloseTime);

	public Map<Integer,Boolean> getScmInitialStatusMap(Integer unitId , Date date);

	public Pair<Date,Date> getCurrentAndLastDayCLoseTime(Integer unitId,Date businessDate);


}

