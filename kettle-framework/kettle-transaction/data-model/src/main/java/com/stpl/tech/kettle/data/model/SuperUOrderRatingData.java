package com.stpl.tech.kettle.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.math.BigDecimal;

@Entity
@Table(name = "SUPERU_ORDER_RATING_DATA")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class SuperUOrderRatingData {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ORDER_RATING_ID")
    Integer orderRatingId;

    @Column(name = "KEY_ID")
    Integer keyId;

    @Column(name = "DATA_KEY")
    String key;
    @Column(name = "VALUE")
    BigDecimal value;

    @Column(name = "TYPE")
    @Enumerated(EnumType.STRING)
    SuperUOrderRatingType type;
    @Column(name = "WEIGHT")
    BigDecimal weight;
    @Column(name = "RATING")
    BigDecimal rating;
    @Column(name = "IS_APPLICABLE")
    Boolean isApplicable;

    @ManyToOne
    @JoinColumn(name="ORDER_LEVEL_DATA_ID", nullable = false)
    SuperUOrderLevelData superUOrderLevelData;
}
