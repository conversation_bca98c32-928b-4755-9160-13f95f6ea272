/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 * OrderInfo generated by hbm2java
 */
@Entity
@Table(name = "TEMP_ACCESS_CODE_USAGE_DATA")
public class TempAccessCodeUsageData implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -7618335422998116937L;

	private Integer tempAccessCodeUsageId;
	private TempAccessCode accessCode;
	private String contactNumber;
	private String reasonForDenial;
	private String accessGranted;
	private Integer usageTime;

	public TempAccessCodeUsageData() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "TEMP_ACCESS_CODE_USAGE_ID", unique = true, nullable = false)
	public Integer getTempAccessCodeUsageId() {
		return this.tempAccessCodeUsageId;
	}

	public void setTempAccessCodeUsageId(Integer tempAccessCodeUsageId) {
		this.tempAccessCodeUsageId = tempAccessCodeUsageId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "TEMP_ACCESS_CODE_ID", nullable = true)
	public TempAccessCode getAccessCode() {
		return accessCode;
	}

	public void setAccessCode(TempAccessCode accessCode) {
		this.accessCode = accessCode;
	}

	@Column(name = "CONTACT_NUMBER", nullable = true, length = 15)
	public String getContactNumber() {
		return contactNumber;
	}

	public void setContactNumber(String contactNumber) {
		this.contactNumber = contactNumber;
	}

	@Column(name = "REASON_FOR_DENIAL", nullable = true, length = 50)
	public String getReasonForDenial() {
		return reasonForDenial;
	}

	public void setReasonForDenial(String reasonForDenial) {
		this.reasonForDenial = reasonForDenial;
	}

	@Column(name = "ACCESS_GRANTED", nullable = true, length = 1)
	public String getAccessGranted() {
		return accessGranted;
	}

	public void setAccessGranted(String accessGranted) {
		this.accessGranted = accessGranted;
	}

	@Column(name = "USAGE_TIME", nullable = true)
	public Integer getUsageTime() {
		return usageTime;
	}

	public void setUsageTime(Integer usageTime) {
		this.usageTime = usageTime;
	}

	@Override
	public String toString() {
		return "TempAccessCodeUsageData [tempAccessCodeUsageId=" + tempAccessCodeUsageId + ", accessCode=" + accessCode
				+ ", contactNumber=" + contactNumber + ", reasonForDenial=" + reasonForDenial + ", accessGranted="
				+ accessGranted + ", usageTime=" + usageTime + "]";
	}

	
}