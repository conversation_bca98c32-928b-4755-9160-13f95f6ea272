package com.stpl.tech.kettle.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Map;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WalletSuggestionCustomerInfo {

    private String customerId;
    private String brandId;
    private String  amountPayable;
    private Map<String,String> activeDenominationMap;
    private String lastVisitTime;
    private  String newCustomer;
    private Integer unitId;
}
