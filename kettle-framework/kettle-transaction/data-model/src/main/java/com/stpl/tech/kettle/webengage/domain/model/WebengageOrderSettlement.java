package com.stpl.tech.kettle.webengage.domain.model;

import com.stpl.tech.kettle.domain.model.OrderPaymentDenomination;
import com.stpl.tech.master.domain.model.PaymentMode;

import java.util.List;

/**
 * Created by Chaayos on 10-05-2017.
 */
public class WebengageOrderSettlement {

    private String mode;
    private Float amount;
    private List<OrderPaymentDenomination> denominations;
    private Float extraVouchers;
    private PaymentMode modeDetail;

    public String getMode() {
        return mode;
    }

    public void setMode(String mode) {
        this.mode = mode;
    }

    public Float getAmount() {
        return amount;
    }

    public void setAmount(Float amount) {
        this.amount = amount;
    }

    public List<OrderPaymentDenomination> getDenominations() {
        return denominations;
    }

    public void setDenominations(List<OrderPaymentDenomination> denominations) {
        this.denominations = denominations;
    }

    public Float getExtraVouchers() {
        return extraVouchers;
    }

    public void setExtraVouchers(Float extraVouchers) {
        this.extraVouchers = extraVouchers;
    }

    public PaymentMode getModeDetail() {
        return modeDetail;
    }

    public void setModeDetail(PaymentMode modeDetail) {
        this.modeDetail = modeDetail;
    }
}
