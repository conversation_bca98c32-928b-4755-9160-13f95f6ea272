/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

import static javax.persistence.GenerationType.IDENTITY;

// Generated 19 Aug, 2015 4:37:51 PM by Hibernate Tools 4.0.0

import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;

/**
 * AttributeDefinition generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "ATTRIBUTE_DEFINITION")
public class AttributeDefinition implements java.io.Serializable {

	private Integer attributeDefId;
	private String attributeName;
	private String attributeCode;
	private String attributeDesc;
	private String attributeType;
	private Set<ReportParams> reportParamses = new HashSet<ReportParams>(0);
	private Set<ReportAttributes> reportAttributeses = new HashSet<ReportAttributes>(0);

	public AttributeDefinition() {
	}

	public AttributeDefinition(String attributeName, String attributeCode, String attributeDesc, String attributeType) {
		this.attributeName = attributeName;
		this.attributeCode = attributeCode;
		this.attributeDesc = attributeDesc;
		this.attributeType = attributeType;
	}

	public AttributeDefinition(String attributeName, String attributeCode, String attributeDesc, String attributeType,
			Set<ReportParams> reportParamses, Set<ReportAttributes> reportAttributeses) {
		this.attributeName = attributeName;
		this.attributeCode = attributeCode;
		this.attributeDesc = attributeDesc;
		this.attributeType = attributeType;
		this.reportParamses = reportParamses;
		this.reportAttributeses = reportAttributeses;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "ATTRIBUTE_DEF_ID", unique = true, nullable = false)
	public Integer getAttributeDefId() {
		return this.attributeDefId;
	}

	public void setAttributeDefId(Integer attributeDefId) {
		this.attributeDefId = attributeDefId;
	}

	@Column(name = "ATTRIBUTE_NAME", nullable = false, length = 30)
	public String getAttributeName() {
		return this.attributeName;
	}

	public void setAttributeName(String attributeName) {
		this.attributeName = attributeName;
	}

	@Column(name = "ATTRIBUTE_CODE", nullable = false, length = 30)
	public String getAttributeCode() {
		return this.attributeCode;
	}

	public void setAttributeCode(String attributeCode) {
		this.attributeCode = attributeCode;
	}

	@Column(name = "ATTRIBUTE_DESC", nullable = false, length = 100)
	public String getAttributeDesc() {
		return this.attributeDesc;
	}

	public void setAttributeDesc(String attributeDesc) {
		this.attributeDesc = attributeDesc;
	}

	@Column(name = "ATTRIBUTE_TYPE", nullable = false, length = 20)
	public String getAttributeType() {
		return this.attributeType;
	}

	public void setAttributeType(String attributeType) {
		this.attributeType = attributeType;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "attributeDefinition")
	public Set<ReportParams> getReportParamses() {
		return this.reportParamses;
	}

	public void setReportParamses(Set<ReportParams> reportParamses) {
		this.reportParamses = reportParamses;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "attributeDefinition")
	public Set<ReportAttributes> getReportAttributeses() {
		return this.reportAttributeses;
	}

	public void setReportAttributeses(Set<ReportAttributes> reportAttributeses) {
		this.reportAttributeses = reportAttributeses;
	}

}
