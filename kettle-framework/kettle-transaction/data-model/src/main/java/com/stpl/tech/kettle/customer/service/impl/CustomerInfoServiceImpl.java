package com.stpl.tech.kettle.customer.service.impl;

import com.stpl.tech.kettle.clevertap.service.CleverTapDataPushService;
import com.stpl.tech.kettle.clevertap.util.CleverTapConstants;
import com.stpl.tech.kettle.clevertap.util.CleverTapEvents;
import com.stpl.tech.kettle.core.LoyaltyEventStatus;
import com.stpl.tech.kettle.core.OrderEmailEntryType;
import com.stpl.tech.kettle.core.data.vo.FeedbackCount;
import com.stpl.tech.kettle.customer.dao.CustomerBirthdayDetailDao;
import com.stpl.tech.kettle.customer.dao.CustomerDao;
import com.stpl.tech.kettle.customer.dao.CustomerInfoDao;
import com.stpl.tech.kettle.customer.service.CustomerEventService;
import com.stpl.tech.kettle.customer.service.CustomerInfoService;
import com.stpl.tech.kettle.data.model.CustomerAddressInfo;
import com.stpl.tech.kettle.data.model.CustomerContactInfoMapping;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.OrderEmailNotification;
import com.stpl.tech.kettle.data.model.ProfileUpdateResponse;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.CustomerCashPacketLog;
import com.stpl.tech.kettle.domain.model.CustomerCashPacketLogList;
import com.stpl.tech.kettle.domain.model.CustomerInfoDineIn;
import com.stpl.tech.kettle.domain.model.CustomerLoyaltyEntry;
import com.stpl.tech.kettle.domain.model.CustomerLoyaltyEntryList;
import com.stpl.tech.kettle.domain.model.CustomerWalletEntry;
import com.stpl.tech.kettle.domain.model.CustomerWalletEntryList;
import com.stpl.tech.kettle.domain.model.EmailResponse;
import com.stpl.tech.kettle.domain.model.WalletEntryType;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.offer.dao.OfferManagementDao;
import com.stpl.tech.master.core.external.offer.service.OfferManagementService;
import com.stpl.tech.master.data.model.CouponDetailData;
import com.stpl.tech.master.domain.model.CloneCouponData;
import com.stpl.tech.master.domain.model.CouponCloneResponse;
import com.stpl.tech.master.domain.model.CouponData;
import com.stpl.tech.master.util.CustomerEventType;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.transaction.TransactionType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.parameters.P;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Random;
import java.util.regex.Pattern;

import static com.stpl.tech.kettle.data.util.KettleUtils.getBrandIdForVerificationEmail;
import static com.stpl.tech.kettle.data.util.KettleUtils.isProfileCompleted;

@Slf4j
@Service
public class CustomerInfoServiceImpl implements CustomerInfoService {

	private static final Logger LOG = LoggerFactory.getLogger(CustomerInfoServiceImpl.class);

	@Autowired
	private CustomerInfoDao customerInfoDao;

	@Autowired
	private CustomerDao customerDao;


	@Autowired
	private CustomerBirthdayDetailDao customerBirthdayDetailDao;

	@Autowired
	private CustomerEventService customerEventService;

	@Autowired
	MasterDataCache cache;

	@Autowired
	private EnvironmentProperties environmentProperties;
	@Autowired
	private CleverTapDataPushService cleverTapDataPushService;

	@Autowired
	private OfferManagementService offerManagementService;
	@Autowired
	private OfferManagementDao offerManagementDao;

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public CustomerInfoDineIn getCustomerInfoForDineIn(int customerId) {
		return customerInfoDao.getCustomerInfoForDineIn(customerId);
	}
	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public CustomerInfo getCustomerInfoById(int customerId) throws DataNotFoundException {
		return customerInfoDao.getCustomerInfoById(customerId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Customer getCustomerInfo(int customerId) throws DataNotFoundException {
		return customerInfoDao.getCustomerById(customerId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public Boolean updateCustomerInfo(int customerId, String name, String acquisitionSource, String acquisitionToken) throws DataUpdationException {
		return customerInfoDao.updateCustomerInfo(customerId, name, acquisitionSource, acquisitionToken);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public String updateCustomerBasicDetail(int customerId, CustomerInfo customerInfo) throws DataUpdationException, DataNotFoundException {
		CustomerInfo oldCustomerObj = customerDao.find(CustomerInfo.class, customerId);
		customerInfoDao.updateCustomerBasicDetail(customerId, customerInfo,oldCustomerObj);
		String contactNumber = customerInfo.getContactNumber();
		if(StringUtils.isEmpty(contactNumber) || StringUtils.isEmpty(AppUtils.getValidContactNUmber(contactNumber))){
			CustomerInfo tempCustomerInfo = customerInfoDao.getCustomerInfoById(customerId);
			contactNumber = tempCustomerInfo.getContactNumber();
		}
		try {
			if (Objects.nonNull(customerInfo.getDateOfBirth())) {
				customerEventService.addCustomerEventMonth(contactNumber,
					AppUtils.getMonth(customerInfo.getDateOfBirth()), AppUtils.getDay(customerInfo.getDateOfBirth()),
					AppConstants.APP, CustomerEventType.Birthday, null);
			}
			if (Objects.nonNull(customerInfo.getAnniversary())) {
				customerEventService.addCustomerEventMonth(contactNumber,
						AppUtils.getMonth(customerInfo.getAnniversary()), AppUtils.getDay(customerInfo.getAnniversary()),
						AppConstants.APP, CustomerEventType.Anniversary, null);
			}
		}
		catch(Exception e){
			LOG.info("Unable to add Event data in Customer Event Detail table for customer Id {}",customerId);
		}
		EmailResponse emailResponse = EmailResponse.DataUpdated;
		if(customerInfo.getEmailId() != null && !AppConstants.EXCLUDE_CUSTOMER_IDS.contains(customerId)) {
			emailResponse = verifyCustomerEmailById(customerId, customerInfo.getEmailId());
		}
		return emailResponse.value();
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public CustomerWalletEntryList getWalletLedger(int customerId, int pageNo, int pageSize) {
		List<CustomerWalletEntry> list = new ArrayList<>();
		list.addAll(customerInfoDao.getWalletPurchaseEntry(customerId));
		list.addAll(customerInfoDao.getWalletPaidEntry(customerId));
		Collections.sort(list, (a, b) -> (a.getTime().compareTo(b.getTime())));
		BigDecimal availableBalance = BigDecimal.ZERO;
		for (CustomerWalletEntry data : list) {
			if (data.getType().equals(WalletEntryType.PURCHASE.value())) {
				availableBalance = availableBalance.add(data.getAmount());
				if(Objects.nonNull(data.getOfferAmount())){
					availableBalance = availableBalance.add(data.getOfferAmount());
				}
			} else if (data.getType().equals(WalletEntryType.PAID.value())) {
				availableBalance = availableBalance.subtract(data.getAmount());
			}
			data.setBalance(availableBalance);
		}
		Collections.reverse(list);
		return new CustomerWalletEntryList(getPagenatedList(pageNo, pageSize, list));
	}

	private <E> List<E> getPagenatedList(int pageNo, int pageSize, List<E> list) {
		if (pageNo == 0) {
			return list;
		} else {
			List<E> pagenatedList = new ArrayList<>();
			if (list.size() <= (pageNo - 1) * pageSize) {
				return pagenatedList;
			} else {
				int start = (pageNo - 1) * pageSize;
				int end = list.size() < (pageNo) * pageSize ? list.size() - 1 : (pageNo) * pageSize - 1;
				for (int i = start; i <= end; i++) {
					pagenatedList.add(list.get(i));
				}
				return pagenatedList;
			}
		}
	}
	public List<CustomerLoyaltyEntry> mergeListAccToTime(List<CustomerLoyaltyEntry> list1, List<CustomerLoyaltyEntry> list2){
		int i=0,j=0;
		List<CustomerLoyaltyEntry> loyaltyEntries=new ArrayList<>();
		Integer totalSum=0;
		while(i<list1.size() && j<list2.size()){
			if(list1.get(i).getTime().after(list2.get(j).getTime())){

				if(AppConstants.SUCCESS.equals(list2.get(j).getStatus())){
					totalSum += list2.get(j).getPoints();
				}
				list2.get(j).setTotal(totalSum);
				loyaltyEntries.add(list2.get(j));
				j++;
			}else{
				if(AppConstants.SUCCESS.equals(list1.get(i).getStatus())){
					totalSum += list1.get(i).getPoints();
				}
				list1.get(i).setTotal(totalSum);
				loyaltyEntries.add(list1.get(i));
				i++;
			}
		}
		while(i<list1.size()) {
			if(AppConstants.SUCCESS.equals(list1.get(i).getStatus())){
				totalSum += list1.get(i).getPoints();
			}
			list1.get(i).setTotal(totalSum);
		    loyaltyEntries.add(list1.get(i));
		    i++;
        }
		while(j<list2.size()){
			if(AppConstants.SUCCESS.equals(list2.get(j).getStatus())){
				totalSum += list2.get(j).getPoints();
			}
			list2.get(j).setTotal(totalSum);
		    loyaltyEntries.add(list2.get(j));
		    j++;
        }
		return loyaltyEntries;
	}
	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public CustomerLoyaltyEntryList getLoyaltyLedger(int customerId, int pageNo, int pageSize) {
		List<CustomerLoyaltyEntry> loyaltyEntries = customerInfoDao.getLoyaltyLedger(customerId);
		List<CustomerLoyaltyEntry> giftingLoyaltyEntries=customerInfoDao.getLoyaltyGiftingLedger(customerId);
		Integer totalGiftPoints = 0;
		for (CustomerLoyaltyEntry obj : giftingLoyaltyEntries) {
			if (AppConstants.SUCCESS.equals(obj.getStatus()) &&
					TransactionType.DEBIT.name().equals(obj.getType()) &&
					LoyaltyEventStatus.ACTIVE.name().equals(obj.getLoyaltyEventStatus())) {

				totalGiftPoints += Math.max(0,(Math.max(0,obj.getPoints())-Math.max(obj.getRedeemedPoints(),0)));
			}
		}

		List<CustomerLoyaltyEntry> mergedLoyaltyEntries=mergeListAccToTime(loyaltyEntries,giftingLoyaltyEntries);
		Collections.reverse(mergedLoyaltyEntries);
		CustomerLoyaltyEntryList result = new CustomerLoyaltyEntryList(getPagenatedList(pageNo, pageSize, mergedLoyaltyEntries));
		result.setTotalGiftedPoints(totalGiftPoints);

		return result;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public CustomerCashPacketLogList getCashLedger(int customerId, int pageNo, int pageSize) {
		List<CustomerCashPacketLog> cashPacketData = customerInfoDao.getCashReferalLedger(customerId);
		BigDecimal availableAmount = BigDecimal.ZERO;
		for (CustomerCashPacketLog data : cashPacketData) {
			availableAmount = data.getType().equals(TransactionType.CREDIT.name())
					? availableAmount.add(data.getAmount())
					: availableAmount.subtract(data.getAmount());
			data.setBalance(availableAmount);
			if(availableAmount.compareTo(BigDecimal.ZERO) < 0) {
				data.setBalance(BigDecimal.ZERO);
			}
		}
		Collections.reverse(cashPacketData);
		return new CustomerCashPacketLogList(getPagenatedList(pageNo, pageSize, cashPacketData));
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public CustomerCashPacketLogList getCashExpLedger(int customerId, Date date) {
		List<CustomerCashPacketLog> list = customerInfoDao.getCashExpLedger(customerId, date);
		if (list.size() == 0) {
			list = customerInfoDao.getLatestCashExpLedger(customerId);
		}
		return new CustomerCashPacketLogList(null, list);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public EmailResponse verifyCustomerEmailById(int customerId, String email)
			throws DataNotFoundException, DataUpdationException {
		CustomerInfo info = getCustomerInfoById(customerId);
		String oldEmailId = info.getEmailId();
		String newEmail = AppUtils.checkBlank(email);
		if (!isValidEmail(newEmail)) {
			return EmailResponse.InvalidEmail;
		}
		if(oldEmailId != null && oldEmailId.equalsIgnoreCase(newEmail)) {
			return EmailResponse.EmailExists;
		}
		//Not needed as customer should be able to change even verified email address
//		 else if (AppConstants.getValue(info.getIsEmailVerified())) {
//			return EmailResponse.EmailVerified;
//		}
		else {
			boolean emailSent = sendVerificationEmail(newEmail, oldEmailId, info.getContactNumber(),
					info.getFirstName(),getBrandIdForVerificationEmail(info.getAcquisitionBrandId()));
			boolean emailUpdated = customerInfoDao.updateCustomerEmail(customerId, newEmail);
			if (emailSent) {
				return EmailResponse.VerificationSent;
			} else {
				return emailUpdated ? EmailResponse.EmailUpdated : EmailResponse.EmailNotUpdated;
			}
		}
	}


	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean sendVerificationEmail(String email, String existingEmail, String contact, String customerName,Integer brandId) {
		try {
			if (AppUtils.checkBlank(email) != null && !email.equals(existingEmail) && !cache.getAllUnitsEmailId().contains(email) ) {
				generateOrderEmailEventForVerification(contact, email, customerName,brandId);
				return true;
			} else {
				LOG.info("Incorrect email entered for the contact {}", contact);
				return false;
			}
		} catch (Exception e) {
			LOG.error(e.getMessage());
		}
		return false;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public EmailResponse verifyCustomerEmail(int customerId,int brandId) throws DataNotFoundException, DataUpdationException {
		CustomerInfo info = getCustomerInfoById(customerId);
		String emailId = info.getEmailId();
		if (!isValidEmail(emailId)) {
			return EmailResponse.InvalidEmail;
		}else if(brandId != info.getAcquisitionBrandId() && AppConstants.DOHFUL_BRAND_ID.equals(info.getAcquisitionBrandId())){
			return EmailResponse.InvalidEmail;
		} else if (AppConstants.getValue(info.getIsEmailVerified())) {
			return EmailResponse.EmailVerified;
		} else if(!AppConstants.EXCLUDE_CUSTOMER_IDS.contains(customerId)){
			boolean emailSent = sendVerificationEmail(emailId, null, info.getContactNumber(), info.getFirstName(),brandId);
			if (emailSent) {
				return EmailResponse.VerificationSent;
			} else {
				return EmailResponse.EmailNotVerified;
			}
		} else{
			return null;
		}
	}

	private void generateOrderEmailEventForVerification(String contactNumber, String email, String customerName,Integer brandId) {
		OrderEmailNotification emailNotification = new OrderEmailNotification(OrderEmailEntryType.VERIFICATION.name(),
				-1, 1, AppConstants.NO, AppUtils.getCurrentTimestamp());
		emailNotification.setEmailAddress(email);
		emailNotification.setContact(contactNumber);
		emailNotification.setCustomerName(customerName);
		emailNotification.setIsEmailDelivered(AppConstants.NO);
		emailNotification.setIsEmailVerified(AppConstants.NO);
		emailNotification.setBrandId(brandId);
		customerInfoDao.add(emailNotification);
	}

	public static boolean isValidEmail(String email) {
		String emailRegex = "^[a-zA-Z0-9_+&*-]+(?:\\." + "[a-zA-Z0-9_+&*-]+)*@" + "(?:[a-zA-Z0-9-]+\\.)+[a-z"
				+ "A-Z]{2,7}$";
		Pattern pat = Pattern.compile(emailRegex);
		if (email == null)
			return false;
		return pat.matcher(email).matches();
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean addCustomerProductFeedback(int customerId, int productId, int rating, Integer sourceId,
			String source) {
		return customerInfoDao.addCustomerProductFeedback(customerId, productId, rating, sourceId, source);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean removeCustomerProductFeedback(int customerId, int productId, String source) {
		return customerInfoDao.removeCustomerProductFeedback(customerId, productId, source);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<FeedbackCount> getProductSpecificFeedbackCount() {
		return customerInfoDao.getProductSpecificFeedbackCount();
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<FeedbackCount> getCustomerSpecificFeedbackCount(int customerId, String source) {
		return customerInfoDao.getCustomerSpecificFeedbackCount(customerId, source);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public String deleteCustomerData(Integer customerId) throws DataNotFoundException {
		String newContactNumber = "";
		try {
			CustomerInfo customerInfo = customerDao.getCustomerInfoById(customerId);
			List<CustomerAddressInfo> customerAddressInfoList = customerInfoDao.getCustomerAddressInfoById(customerId);
			if (customerAddressInfoList.size() != 0) {
				for (CustomerAddressInfo customerAddressInfo: customerAddressInfoList) {
					customerAddressInfo.setStatus(AppConstants.IN_ACTIVE);
					customerInfoDao.delete(customerAddressInfo);
				}
			}
			if (customerInfo != null) {
				newContactNumber = randomAlphanumericString(10);
				List<String> oldMappedContactNumbersList = customerInfoDao.getOldMappedContactNumbersList();
				while (oldMappedContactNumbersList != null && oldMappedContactNumbersList.contains(newContactNumber)) {
					newContactNumber = randomAlphanumericString(10);
				}

				String customerOldContactNumber = customerInfo.getContactNumber();
				customerInfo.setContactNumber(newContactNumber);
				customerInfo.setIsDeleted("Y");
				customerInfo.setIsRenewed(null);

				CustomerContactInfoMapping oldCustomerContactInfoMapping = customerInfoDao.getCustomerContactInfoMapping(customerOldContactNumber);
				CustomerContactInfoMapping customerContactInfoMapping = new CustomerContactInfoMapping();

				if (oldCustomerContactInfoMapping == null) {
					customerContactInfoMapping.setCustomerId(customerId);
					customerContactInfoMapping.setOldContactNumber(customerOldContactNumber);
					customerContactInfoMapping.setNewContactNumber(newContactNumber);
					customerDao.add(customerContactInfoMapping);
				} else {
					oldCustomerContactInfoMapping.setNewContactNumber(newContactNumber);
					oldCustomerContactInfoMapping.setCustomerId(customerId);
					customerDao.update(oldCustomerContactInfoMapping);
				}

				customerDao.update(customerInfo);
				LOG.info("User Profile Data Deleted");
			}
		} catch (Exception e) {
			LOG.info("Data not found for CustomerID", customerId);
			throw new DataNotFoundException(e);
		}
		return newContactNumber;
	}

	private String randomAlphanumericString(int n) {
		byte[] array = new byte[256];
		new Random().nextBytes(array);
		String randomString = new String(array, Charset.forName("UTF-8"));
		StringBuffer r = new StringBuffer();
		String AlphaNumericString = randomString.replaceAll("[^A-Za-z0-9]", "");
		for (int k = 0; k < AlphaNumericString.length(); k++) {
			if (Character.isLetter(AlphaNumericString.charAt(k)) && (n > 0) || Character.isDigit(AlphaNumericString.charAt(k)) && (n > 0)) {
				r.append(AlphaNumericString.charAt(k));
				n--;
			}
		}
		return r.toString();
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public ProfileUpdateResponse updateCustomerBasicDetailAndGenerateCoupon(int customerId, CustomerInfo customerInfo) throws DataUpdationException, DataNotFoundException {
		CustomerInfo oldCustomerObj = customerDao.find(CustomerInfo.class, customerId);
		LOG.info("Customer name : {} , contact number : {} , DOB : {} , email : {}, gender : {} before Updating",
				oldCustomerObj.getFirstName(),oldCustomerObj.getContactNumber(),oldCustomerObj.getDateOfBirth(),
				oldCustomerObj.getEmailId(),oldCustomerObj.getGender());
		boolean isAllEntryExistForCustomerProfile = isProfileCompleted(oldCustomerObj);
		ProfileUpdateResponse profileUpdateResponse = new ProfileUpdateResponse();
		String contactNumber = customerInfo.getContactNumber();
		String response = updateCustomerBasicDetail(customerId,customerInfo);
		customerInfo = customerInfoDao.getCustomerInfoById(customerId);
		if(StringUtils.isEmpty(contactNumber) || StringUtils.isEmpty(AppUtils.getValidContactNUmber(contactNumber))){
			contactNumber = customerInfo.getContactNumber();
		}
		profileUpdateResponse.setResponse(response);
		LOG.info("Customer name : {} , contact number : {} , DOB : {} , email : {}, gender : {} after Updating",
				customerInfo.getFirstName(),customerInfo.getContactNumber(),customerInfo.getDateOfBirth(),
				customerInfo.getEmailId(),customerInfo.getGender());
		boolean isProfileCompleted = isProfileCompleted(customerInfo);
		if(isProfileCompleted && !isAllEntryExistForCustomerProfile){
			CouponCloneResponse couponResponse = generateCoupon(contactNumber,customerInfo.getCustomerId());
			if(Objects.nonNull(couponResponse) && CollectionUtils.isEmpty(couponResponse.getErrors())) {
				if(Objects.nonNull(couponResponse.getMappings()) &&
						couponResponse.getMappings().containsKey(contactNumber)) {
					CouponData couponData = couponResponse.getMappings().get(contactNumber);
					profileUpdateResponse.setCouponCode(couponData.getCoupon());
					profileUpdateResponse.setCouponDescription(couponResponse.getDescription());
					profileUpdateResponse.setCouponEndDate(couponData.getEndDate());
				}
			}
		}
		return profileUpdateResponse;
	}

	private CloneCouponData getCloneCouponData(int usageCount, int validityInDays, String prefix,
											   CouponDetailData clone, Integer applicableUnitId, String applicableRegion) {
		CloneCouponData data = new CloneCouponData();
		data.setCloneCouponCode(clone.getCouponCode());
		data.setOfferDetailId(clone.getOfferDetail().getOfferDetailId());
		data.setPrefix(prefix);
		data.setUsageCount(usageCount);
		data.setMaxCustomerUsage(clone.getMaxCustomerUsage());
		data.setValidityInDays(validityInDays);
		data.setApplicableRegion(applicableRegion);
		data.setApplicableUnitId(applicableUnitId);
		return data;
	}

	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	private CouponCloneResponse generateCoupon(String contactNumber,Integer customerId){
		String startDay = AppUtils.getDateString(AppUtils.getBusinessDate(), AppConstants.DATE_FORMAT);
		try {
			if(Objects.nonNull(contactNumber)) {
				String masterCoupon = environmentProperties.getProfileCompletionCouponCode();
				String couponPrefix = environmentProperties.getProfileCompletionCouponCodePrefix();
				CouponDetailData clone = offerManagementDao.getCoupon(masterCoupon);
				CloneCouponData cloneCoupon = getCloneCouponData(1,30, couponPrefix, clone, null, null);
				CouponCloneResponse response =  offerManagementService.
						createCoupon(cloneCoupon, Arrays.asList(contactNumber), startDay, null, null, clone);
				if(Objects.nonNull(response) && CollectionUtils.isEmpty(response.getErrors())) {
					CouponData couponData = response.getMappings().get(contactNumber);
					try {
						Map<String, Object> eventData = new HashMap<>();
						eventData.put("ProfileCompletionCouponCode", couponData.getCoupon());
						eventData.put("ProfileCompletionCouponText", response.getDescription());
						eventData.put("ProfileCompletionCouponEndDate", couponData.getEndDate());
						cleverTapDataPushService.publishCustomEvent(customerId, CleverTapEvents.PROFILE_COMPLETION,
								AppUtils.getCurrentTimestamp().toInstant().getEpochSecond(), CleverTapConstants.REGULAR, eventData);
					} catch (Exception e) {
						LOG.info("Error in Pushing Profile completion Event to clever tap for CustomerId : {}", customerId, e.getMessage());
					}
					return response;
				}
			}
		}catch (Exception e){
			LOG.info("Error in generating profile completion coupon for customer with contact number : {} and error is : {}"
					,contactNumber,e.getMessage());
		}
		return null;

	}



}
