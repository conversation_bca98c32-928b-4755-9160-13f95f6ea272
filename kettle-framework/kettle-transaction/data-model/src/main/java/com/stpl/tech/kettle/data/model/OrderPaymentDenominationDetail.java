/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Entity
@Table(name = "ORDER_PAYMENT_DENOMINATION")
public class OrderPaymentDenominationDetail implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 2738478113815417116L;

	private int id;
	private int orderId;
	private OrderSettlement orderSettlement;
	private int denominationId;
	private int count;
	private int totalAmount;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "ID", unique = true, nullable = false)
	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	@Column(name = "ORDER_ID", nullable = false)
	public int getOrderId() {
		return orderId;
	}

	public void setOrderId(int orderId) {
		this.orderId = orderId;
	}

	@Column(name = "DENOMINATION_ID", nullable = false)
	public int getDenominationId() {
		return denominationId;
	}

	public void setDenominationId(int denomination) {
		this.denominationId = denomination;
	}

	@Column(name = "DENOMINATION_COUNT", nullable = false)
	public int getCount() {
		return count;
	}

	public void setCount(int count) {
		this.count = count;
	}

	@Column(name = "TOTAL_AMOUNT", nullable = false)
	public int getTotalAmount() {
		return totalAmount;
	}

	public void setTotalAmount(int totalAmount) {
		this.totalAmount = totalAmount;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SETTLEMENT_ID", nullable = false)
	public OrderSettlement getOrderSettlement() {
		return orderSettlement;
	}

	public void setOrderSettlement(OrderSettlement orderSettlement) {
		this.orderSettlement = orderSettlement;
	}
}
