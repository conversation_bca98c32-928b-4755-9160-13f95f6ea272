package com.stpl.tech.kettle.offer.strategy;

import com.stpl.tech.kettle.core.TransactionConstants;
import com.stpl.tech.kettle.core.notification.OfferOrder;
import com.stpl.tech.kettle.domain.model.DiscountDetail;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.PercentageDetail;
import com.stpl.tech.master.core.WebErrorCode;
import com.stpl.tech.master.core.exception.OfferValidationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.OfferDetail;
import com.stpl.tech.master.domain.model.OfferMetaDataType;
import com.stpl.tech.master.domain.model.ProductBasicDetail;
import com.stpl.tech.master.util.BudgetCategoryConstants;
import com.stpl.tech.util.AppUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.TreeMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

public class PriceDescendingPercentageItemStrategy extends PercentageItemStrategy {

    private static final Logger LOG = LoggerFactory.getLogger(PriceDescendingPercentageItemStrategy.class);

    StringBuffer offerMessage = new StringBuffer();
    String messageTemplate = "Applied discount of %.2f on %s <br/>";

    private static final Integer MULTIPLIER_FACTOR=3;


    @Override
    public OfferOrder applyStrategy(OfferOrder order, CouponDetail coupon, MasterDataCache cache, Map<String, OrderItem> foundItems) throws OfferValidationException {
        LOG.info("Applying percentage discount to the offer order");
        return applyDiscountStrategy(coupon, order, cache);
    }

    @Override
    public OrderItem addDiscountDetails(OrderItem orderItem, BigDecimal discountInOffer, OfferDetail offer, int counter) {
        LOG.info("add DiscountDetails of percent item BOGO strategy");
        BigDecimal totalAmount = getTotalAmountOfItem(orderItem.getPrice(), orderItem.getQuantity());
        if (counter >= orderItem.getQuantity()) {
            return getDiscountItem(orderItem, discountInOffer, totalAmount, orderItem.getQuantity());
        } else {
            return getDiscountItem(orderItem, discountInOffer, totalAmount, counter);
        }
    }

    private OrderItem getDiscountItem(OrderItem orderItem, BigDecimal discountInOffer, BigDecimal totalAmount, int quantity) {
        BigDecimal discountOnPrice = orderItem.getPrice().multiply(BigDecimal.valueOf(quantity))
                .setScale(2, RoundingMode.HALF_UP);
        BigDecimal valueDiscount = AppUtils.percentOf(discountInOffer, discountOnPrice);
        BigDecimal effectivePercentage = AppUtils.percentage(valueDiscount, totalAmount);
        offerMessage.append(String.format(messageTemplate, valueDiscount.floatValue(), orderItem.getProductName()));
        OrderItem modifiedItem =  getModifiedItem(orderItem, effectivePercentage, valueDiscount);
        modifiedItem.getDiscountDetail().setQtyDiscountApplied(quantity);

        return modifiedItem;
    }

    protected void updateOrderDiscount(OfferOrder offerOrder, String code, BigDecimal discountPercentage) {

        LOG.info("Updating order with coupon code:{}", code);
        DiscountDetail discountDetail = getDiscountDetail(code, discountPercentage);
        BigDecimal offerValue = AppUtils.percentOf(discountDetail.getDiscount().getPercentage(),
                offerOrder.getOrder().getTransactionDetail().getTotalAmount());
        discountDetail.setTotalDiscount(offerValue);
        if (offerOrder.getOrder().getTransactionDetail().getDiscountDetail().getPromotionalOffer() != null) {
            discountDetail.setTotalDiscount(discountDetail.getTotalDiscount()
                    .add(offerOrder.getOrder().getTransactionDetail().getDiscountDetail().getPromotionalOffer()));
        }
        discountDetail.getDiscount().setValue(offerValue);
        offerOrder.getOrder().getTransactionDetail().setDiscountDetail(discountDetail);
    }

    private DiscountDetail getDiscountDetail(String code, BigDecimal discountPercentage) {
        PercentageDetail percentageDetail = new PercentageDetail();
        percentageDetail.setPercentage(discountPercentage);
        DiscountDetail discountDetail = new DiscountDetail();
        discountDetail.setDiscountCode(TransactionConstants.MARKETING_VOUCHER_ID);
        discountDetail.setDiscountReason(code);
        discountDetail.setDiscount(percentageDetail);
        discountDetail.setPromotionalOffer(BigDecimal.ZERO);
        return discountDetail;
    }

    @Override
    public OfferOrder applyDiscountStrategy(CouponDetail coupon, OfferOrder order, MasterDataCache cache) throws OfferValidationException {

        OfferDetail offer = coupon.getOffer();

        Map<OfferMetaDataType, Set<String>> mappings = makeMapOfMappings(offer.getMetaDataMappings());
        Map<Integer, OrderItem> productDiscountMap = new HashMap<Integer, OrderItem>();

        //Set<OrderItem> orderItems = new HashSet<OrderItem>(order.getOrder().getOrders());
        Set<ProductBasicDetail> productDetails = new HashSet<ProductBasicDetail>();

        for (OrderItem item : order.getOrder().getOrders()) {
            int productId = item.getProductId();
            productDiscountMap.put(productId, item);
            productDetails.add(cache.getProductBasicDetail(productId));
        }
        AtomicBoolean isItemFounded = new AtomicBoolean(false);
        for (OfferMetaDataType mappingKey : mappings.keySet()) {
            Optional<List<Integer>> commonList = Optional
                    .ofNullable(mappingKey.getCommonElements(mappings.get(mappingKey), productDetails));
            if (commonList.isPresent()) {
                List<Integer> commonElementList = commonList.get();
                commonElementList = sortCommonList(productDiscountMap, commonElementList);

                AtomicInteger counter = new AtomicInteger(offer.getMinQuantity());

                if(!StringUtils.isEmpty(order.getApplyOnItemKey())){
                    modifyAndAddDiscountDetails(commonElementList,order,counter,offer,coupon,true,isItemFounded);
                }
                else{
                    modifyAndAddDiscountDetails(commonElementList,order,counter,offer,coupon,false,isItemFounded);
                }
                if(!isItemFounded.get()){
                    modifyAndAddDiscountDetails(commonElementList,order,counter,offer,coupon,false,isItemFounded);
                }

                if(counter.get() > 0){
                    throw new OfferValidationException("Eligible Item Quantity less than Quantity Redeemed", WebErrorCode.INSUFFICIENT_DATA);
                }
            }


        }


        if(!isItemFounded.get() && BudgetCategoryConstants.LOYALTY_OFFER.equals(coupon.getOffer().getBudgetCategory())){
            throw new OfferValidationException("Eligible Item not Present in Cart", WebErrorCode.OFFER_PRODUCT_NOT_FOUND);
        }
        order.setAppliedOfferMessage(getOfferMessage());
        return order;
    }

    @Override
    List<Integer> sortCommonList(Map<Integer, OrderItem> productDiscountMap, List<Integer> commonElementList) {

        commonElementList.sort(new Comparator<Integer>() {
            @Override
            public int compare(Integer o1, Integer o2) {
                return productDiscountMap.get(o2).getPrice().subtract(productDiscountMap.get(o1).getPrice()).intValue();
            }
        });

        return commonElementList;
    }


}
