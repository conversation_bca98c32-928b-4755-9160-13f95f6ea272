/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.dao;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.stpl.tech.kettle.core.data.vo.OrderFetchStrategy;
import com.stpl.tech.kettle.core.data.vo.OrderStatusData;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.data.model.*;
import com.stpl.tech.kettle.data.model.OrderItem;
import com.stpl.tech.kettle.domain.model.*;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.data.dao.AbstractDao;
import com.stpl.tech.util.TemplateRenderingException;

public interface OrderSearchDao extends AbstractDao {

	/**
	 * Return Order details for an order productId
	 *
	 * @param orderId
	 * @return
	 * @throws DataNotFoundException
	 */
	public Order getOrderDetail(int orderId) throws DataNotFoundException;

	/**
	 * Return Order details for an generated Order Id
	 *
	 * @param generatedOrderId
	 * @return
	 * @throws DataNotFoundException
	 */
	public Order getOrderDetail(String generatedOrderId) throws DataNotFoundException;

	/**
	 * Get last order details for the business dte and the unit
	 *
	 * @param unitId
	 * @return
	 * @throws DataNotFoundException
	 */
	public int getLastOrderDetail(int unitId) throws DataNotFoundException;

	public int getLastDayCloseOrderId(int unitId);

	/**
	 * Get all orders for a duration
	 *
	 * @param unitId
	 * @param startTime
	 * @param endTime
	 * @return
	 * @throws DataNotFoundException
	 */
	public List<Order> getOrderDetails(int unitId, Date startTime, Date endTime, OrderFetchStrategy strategy) throws DataNotFoundException;

	/**
	 * Get all settlements for a duration
	 *
	 * @param unitId
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public List<Settlement> getSettlementDetails(int unitId, Date startTime, Date endTime);

	public List<OrderEmailNotification> getEmailEvents();

	public OrderInfo getOrderReceipt(int orderId, boolean includeReceipts, String customerName)
			throws DataNotFoundException, TemplateRenderingException;

	public OrderInfo getOrderReceipt(String orderId, boolean includeReceipts)
			throws DataNotFoundException, TemplateRenderingException;

	public List<OrderStatusData> getOrderStatusForDay(int unitId, List<OrderStatus> orderStatus,
			List<String> categories,List<String> currentOrderStatus,String generatedOrderId) throws DataNotFoundException;

	public Date getLastBusinessDate(int unitId);

	public List<UnitToDeliveryPartnerMappings> getUnitToDeliveryPartnerMappings(int unitId);

	public int getLastOrderOfLastBusinessDate();

	public List<EmployeeMealData> getEmployeeMealData(int employeeId);

	public List<Order> getOrderDetails(int unitId, int startOrderId, int endOrderId, OrderFetchStrategy strategy)
			throws DataNotFoundException;

	public CashCardDetail findCashCardByCardNumber(String cashCardNumber, Integer empId, Integer unitId);

	public int getCostEvent(int orderId);

	public List<Order> getEmployeeMealOrders(int userId);

	public boolean publishFinalOrderStatus(List<Integer> orderIdList);

	public Order getPartnerOrderDetail(String externalOrderId, int channerPartnerId) throws DataNotFoundException;

	public OrderStatus getPartnerOrderStatus(String externalOrderId, int channerPartnerId) throws DataNotFoundException;

	public List<OrderInfo> getOrderToPublish(int id, Date businessDate) throws DataNotFoundException, TemplateRenderingException;

	public List<Order> getOrderDetails(Date businessDate, OrderFetchStrategy strategy);

	public List<Order> getOrderDetails(Date businessDate, OrderFetchStrategy strategy, List<Integer> unitIds);

	public List<Order> getOrderDetails(Date businessDate, OrderFetchStrategy strategy, int unitId, String source);

	public UnitClosure getLastDayClose(int unitId);

	public UnitClosure getUnitClosure(int closureId);

	public List<OrderInfo> getOrderDetails(int unitId, int lastOrderId, OrderFetchStrategy strategy,
			List<Integer> skipOrders);

	public List<Integer> getOrderIdsInBatch(Integer startOrderId, int batchSize);

    CustomerAddressInfo getOrderAddressInfo(Integer addressId);

	List<Order> getCustomerOrders(int customerId, Date currentDate, int maxSize, List<Integer> filteredOrderIds);

	public List<OrderNPS> getCustomerFeedbacks(int customerId, Date fromDate, int maxSize, List<Integer> filteredIds);

	public OrderNPS getFeedbackDetail(int surveyResponseId) throws DataNotFoundException;

	public OrderNPS getCustomerFeedback(int orderId);

	public Integer getCustomerId(Integer orderId);

	public Integer getChannelPartnerId(Integer orderId);
	
	public Integer getTableRequestIdByOrderId(Integer orderId);

	OrderStatusEvent getLatestOrderTransitionDetail(int orderId);

	PartnerOrderRiderStatesDetail getRiderDelayReason(int orderId);
	public Map<String,Object> getPartnerOrderDetail(String partnerSourceId, String issueType);

	List<Order> getAllOrdersOfTheDays(DayWiseOrderConsumptionRequest dayWiseOrderConsumptionRequest, OrderFetchStrategy strategy);

	List<OrderItem> getComboOrderItems(int comboItemId);

	public Boolean isPrioritizedOrder(Integer kettleOrderId);
	List<String> getOrdersForUnitFromStatus(Integer unitId, List<String> statusList);

	OrderRefundDetail getOrderRefundByOrderId(int orderId);

}
