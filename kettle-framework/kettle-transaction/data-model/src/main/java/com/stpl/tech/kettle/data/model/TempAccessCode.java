/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * OrderInfo generated by hbm2java
 */
@Entity
@Table(name = "TEMP_ACCESS_CODE")
public class TempAccessCode implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 3371357830577619581L;
	private Integer tempAccessCodeId;
	private String tempCode;
	private String codeStatus;
	private Integer orderId;
	private Date creationTime;
	private Date expirationTime;
	private List<TempAccessCodeUsageData> usageData = new ArrayList<TempAccessCodeUsageData>(0);

	public TempAccessCode() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "TEMP_ACCESS_CODE_ID", unique = true, nullable = false)
	public Integer getTempAccessCodeId() {
		return this.tempAccessCodeId;
	}

	public void setTempAccessCodeId(Integer feedbackFieldId) {
		this.tempAccessCodeId = feedbackFieldId;
	}

	@Column(name = "TEMP_CODE", nullable = false, length = 10)
	public String getTempCode() {
		return tempCode;
	}

	public void setTempCode(String tempCode) {
		this.tempCode = tempCode;
	}

	@Column(name = "CODE_STATUS", nullable = false, length = 15)
	public String getCodeStatus() {
		return codeStatus;
	}

	public void setCodeStatus(String codeStatus) {
		this.codeStatus = codeStatus;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "accessCode")
	public List<TempAccessCodeUsageData> getUsageData() {
		return usageData;
	}

	public void setUsageData(List<TempAccessCodeUsageData> formFields) {
		this.usageData = formFields;
	}

	@Column(name = "ORDER_ID", nullable = true)
	public Integer getOrderId() {
		return orderId;
	}

	public void setOrderId(Integer orderId) {
		this.orderId = orderId;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATION_TIME", nullable = false, length = 19)
	public Date getCreationTime() {
		return creationTime;
	}

	public void setCreationTime(Date creationTime) {
		this.creationTime = creationTime;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "EXPIRATION_TIME", nullable = false, length = 19)
	public Date getExpirationTime() {
		return expirationTime;
	}

	public void setExpirationTime(Date expirationTime) {
		this.expirationTime = expirationTime;
	}

}