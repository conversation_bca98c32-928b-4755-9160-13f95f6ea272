/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.service.impl;

import com.stpl.tech.kettle.clevertap.service.CleverTapDataPushService;
import com.stpl.tech.kettle.clevertap.util.CleverTapConstants;
import com.stpl.tech.kettle.clevertap.util.CleverTapEvents;
import com.stpl.tech.kettle.core.CouponType;
import com.stpl.tech.kettle.core.CustomerRepeatType;
import com.stpl.tech.kettle.core.SignUpTimeSlotCount;
import com.stpl.tech.kettle.core.SubscriptionEventType;
import com.stpl.tech.kettle.core.exception.SignUpOfferException;
import com.stpl.tech.kettle.core.file.management.GoogleSheetLoader;
import com.stpl.tech.kettle.core.notification.OfferOrder;
import com.stpl.tech.kettle.core.service.CustomerOfferManagementService;
import com.stpl.tech.kettle.customer.dao.CustomerInfoDao;
import com.stpl.tech.kettle.customer.dao.SubscriptionPlanDao;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.dao.CustomerOfferDetailDao;
import com.stpl.tech.kettle.data.dao.CustomerOfferManagementDao;
import com.stpl.tech.kettle.data.model.CustomerAddressInfo;
import com.stpl.tech.kettle.data.model.CustomerCampaignJourney;
import com.stpl.tech.kettle.data.model.CustomerCampaignOfferDetail;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.CustomerOfferDetail;
import com.stpl.tech.kettle.data.model.SubscriptionPlan;
import com.stpl.tech.kettle.data.model.SubscriptionPlanEvent;
import com.stpl.tech.kettle.data.model.WebOfferCouponRedemptionDetail;
import com.stpl.tech.kettle.data.model.WebOfferDetail;
import com.stpl.tech.kettle.domain.model.CreateNextOfferRequest;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.CustomerDineInView;
import com.stpl.tech.kettle.domain.model.CustomerEmailData;
import com.stpl.tech.kettle.domain.model.CustomerOneViewData;
import com.stpl.tech.kettle.domain.model.CustomerTransactionViewData;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.SubscriptionInfoDetail;
import com.stpl.tech.master.core.CouponMappingType;
import com.stpl.tech.master.core.WebErrorCode;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.exception.OfferValidationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.core.external.offer.dao.OfferManagementDao;
import com.stpl.tech.master.core.external.offer.service.OfferManagementService;
import com.stpl.tech.master.data.model.CouponDetailData;
import com.stpl.tech.master.data.model.CouponDetailMappingData;
import com.stpl.tech.master.data.model.CustomerWinbackOfferInfo;
import com.stpl.tech.master.data.model.DeliveryCouponDetailData;
import com.stpl.tech.master.data.model.DeliveryOfferDetailData;
import com.stpl.tech.master.domain.model.CampaignDetail;
import com.stpl.tech.master.domain.model.CampaignMapping;
import com.stpl.tech.master.domain.model.CloneCouponData;
import com.stpl.tech.master.domain.model.CouponCloneResponse;
import com.stpl.tech.master.domain.model.CouponData;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.CustomerWinbackOfferInfoDomain;
import com.stpl.tech.master.domain.model.DeliveryCouponStatus;
import com.stpl.tech.master.domain.model.IdentifierType;
import com.stpl.tech.master.domain.model.OfferResponse;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.servlet.View;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.StringJoiner;

@Service
public class CustomerOfferManagementServiceImpl implements CustomerOfferManagementService {

    @Autowired
    private CustomerOfferManagementDao customerOfferDao;

    @Autowired
    private OfferManagementDao offerDao;

    @Autowired
    private OfferManagementService offerService;

    @Autowired
    private SubscriptionPlanDao subscriptionDao;

    @Autowired
    private CustomerInfoDao customerDao;

    @Autowired
    private EnvironmentProperties env;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private CleverTapDataPushService cleverTapDataPushService;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private CustomerOfferDetailDao customerOfferDetailDao;

    private static final Logger LOG = LoggerFactory.getLogger(CustomerOfferManagementServiceImpl.class);

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    @Override
    public OfferOrder applyCoupoun(OfferOrder offerOrder, BigDecimal offerValue)
            throws OfferValidationException, DataNotFoundException {
        if (offerOrder.getOrder() != null) {
            return customerOfferDao.applyCoupon(offerOrder, offerValue);
        } else {
            throw new OfferValidationException("Could not find order in this request", WebErrorCode.NOT_AVAILABLE);
        }
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public boolean addCouponMappingTypeCustomer(String coupon, Customer customer) {
        return offerDao.addCouponMapping(coupon, CouponMappingType.CONTACT_NUMBER, String.valueOf(customer.getId()));
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.kettle.core.service.CustomerOfferManagementService#
     * getFreeItemOffer(java.util.Date)
     */
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public CouponDetail getFreeItemOffer(Date businessDate) {
        return offerDao.getFreeItemOffer(businessDate);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean availedCashOffer(Integer customerId, Date startOfBusinessDay, Collection<String> offerCodes, int maxUsageAllowed) {
        return customerOfferDao.availedCashOffer(customerId, startOfBusinessDay, offerCodes, maxUsageAllowed);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Boolean hasSignUpOfferAvailed(Integer customerId) throws Exception {
        if (customerId != null) {
            List<WebOfferDetail> webOfferDetails = customerOfferDao.getWebOfferDetails(customerId, AppConstants.SIGNUP_OFFER_DELIVERY);
            return AppUtils.isNonEmptyList(webOfferDetails);
        }
        return false;
    }

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Boolean hasSubscription(Integer customerId, String offerCode){
		SubscriptionPlan plan = subscriptionDao.getActiveSubscription(customerId, offerCode);
		return plan != null && !plan.getPlanEndDate().before(AppUtils.getBusinessDate());
	}

    @Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public SubscriptionInfoDetail getSubscriptionInfoDetail(int customerId){
		SubscriptionPlan plan = subscriptionDao.getSubscription(customerId);
        if(Objects.nonNull(plan) && !plan.getEventType().equals(SubscriptionEventType.SUBSCRIPTION_CANCELLED.name())){
            return new SubscriptionInfoDetail(plan.getCustomerId(),plan.getSubscriptionPlanCode(),
                    !plan.getPlanEndDate().before(AppUtils.getBusinessDate())
                    && AppConstants.ACTIVE.equals(plan.getStatus())
                    ,plan.getPlanStartDate(),plan.getPlanEndDate()
                    ,Objects.nonNull(plan.getOverAllSaving()) ? plan.getOverAllSaving() : BigDecimal.ZERO, (int) AppUtils.getActualDayDifference(AppUtils.getBusinessDate(),plan.getPlanEndDate()));
        }
        return null;
	}


    @Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public void addSubscriptionSaving(int customerId, Order order, Pair<CouponDetail, Product> subscriptionObj) {
		LOG.info("Adding Over All Saving For Customer Id ::{}",customerId);
        subscriptionDao.addSubscriptionSaving(customerId, order,subscriptionObj);
        subscriptionDao.addSubscriptionEventSaving(customerId,order.getTransactionDetail().getSavings());
    }


    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM",readOnly = false,propagation = Propagation.REQUIRED)
    public void addSubscriptionEventSaving(int customerId,BigDecimal subscriptionSavings){
        LOG.info("Adding subscription savings for CustomerId ::{}",customerId);
        subscriptionDao.addSubscriptionEventSaving(customerId,subscriptionSavings);
    }



    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean availSignUpOffer(String customerId, String customerName, String timeOfDelivery, String dateOfDelivery,
                                    String completeAddress, String city, String pinCode, com.stpl.tech.kettle.domain.model.OrderItem product,
                                    Integer unitId, Integer brandId) throws Exception {
        CustomerInfo customer = customerDao.getCustomerInfoById(Integer.parseInt(customerId));
        if (customer != null) {
            if (customerDao.updateCustomerInfo(customer.getCustomerId(), customerName, "NEO SERVICE", AppConstants.SIGNUP_OFFER_DELIVERY)) {
                CustomerAddressInfo address = new CustomerAddressInfo(customer, completeAddress, city,
                        getStateByCityName(city.toLowerCase()), "India", pinCode, "HOME", customerName,null,null);
                if (customerDao.updateCustomerAddress(customer.getCustomerId(), address)) {
                    WebOfferDetail webOfferDetail = new WebOfferDetail(AppConstants.SIGNUP_OFFER_DELIVERY,
                            Integer.parseInt(customerId), customer.getFirstName(), customer.getContactNumber(),
                            timeOfDelivery, dateOfDelivery, completeAddress, city, pinCode, brandId, unitId);
                    updateProductInfoWebOffer(product, webOfferDetail);
                    webOfferDetail = customerOfferDao.saveWebOfferDetail(webOfferDetail);
                    LOG.info("After Saving webOfferDetail " + JSONSerializer.toJSON(webOfferDetail));
                    if (webOfferDetail.getWebOfferDetailId() != null) {
                        slackToFreeChaiDeliveryChannel(webOfferDetail);
                        updateSignUpOfferSheet(webOfferDetail);
                        return Boolean.TRUE;
                    }
                    throw new SignUpOfferException("Failed to avail sign up offer");
                }
                throw new SignUpOfferException("Failed to update customer address to avail sign up offer");
            }
            throw new SignUpOfferException("Failed to update customer name to avail sign up offer");
        }
        throw new SignUpOfferException("Customer not found to avail sign up offer");
    }

    private String getStateByCityName(String city) {
        switch (city) {
            case "gurgaon":
                return "Haryana";
            case "delhi":
                return "New Delhi";
            case "noida":
                return "Uttar Pradesh";
            case "chandigarh":
                return "Punjab";
            case "mumbai":
                return "Maharashtra";
            case "bangalore":
                return "Karnataka";
            default:
                return null;
        }

    }

    private void updateProductInfoWebOffer(com.stpl.tech.kettle.domain.model.OrderItem product,
                                           WebOfferDetail webOfferDetail) {
        webOfferDetail.setProductId(product.getProductId());
        webOfferDetail.setProductName(product.getProductName());
        webOfferDetail.setQuantity(product.getQuantity());
        webOfferDetail.setPrice(product.getPrice());
        webOfferDetail.setDimension(product.getDimension());
        webOfferDetail.setVariants(null);
        webOfferDetail.setAddons(null);
        webOfferDetail.setRecipeId(product.getRecipeId());
        webOfferDetail.setCreationTime(AppUtils.getCurrentTimestamp());


        if (product.getComposition() != null && AppUtils.isNonEmptyList(product.getComposition().getVariants())) {
            StringJoiner joiner = new StringJoiner(",");
            product.getComposition().getVariants().forEach(item -> joiner.add(item.getAlias()));
            webOfferDetail.setVariants(joiner.toString());
        }
        if (product.getComposition() != null && AppUtils.isNonEmptyList(product.getComposition().getAddons())) {
            StringJoiner joiner = new StringJoiner(",");
            product.getComposition().getAddons().forEach(item -> joiner.add(item.getProduct().getName()));
            webOfferDetail.setAddons(joiner.toString());
        }
    }

    private void slackToFreeChaiDeliveryChannel(WebOfferDetail wod) {

        StringBuilder message = new StringBuilder();
        message.append("Free Chai Delivery").append(System.lineSeparator())
                .append("Customer Name : ").append(wod.getCustomerName()).append(System.lineSeparator())
                .append("Customer Contact : ").append(wod.getCustomerContact()).append(System.lineSeparator())
                .append("Address : ").append(wod.getCompleteAddress()).append(System.lineSeparator())
                .append("Unique ID : ").append(wod.getWebOfferDetailId()).append(System.lineSeparator())
                .append("Offer Type : ").append(wod.getOfferType()).append(System.lineSeparator())
                .append("Request Raised : ").append(AppUtils.getCurrentTimeISTString()).append(System.lineSeparator())
                .append("Order Time and Date : ").append(wod.getTimeOfDelivery()).append(" ").append(wod.getDateOfDelivery()).append(System.lineSeparator())
                .append(wod.getProductName()).append(" : ").append(wod.getDimension()).append(System.lineSeparator())
                .append("Variants : ").append(wod.getVariants()).append(System.lineSeparator())
                .append("Addons : ").append(wod.getAddons()).append(System.lineSeparator())
                .append("Brand Id : ").append(wod.getBrandId()).append(System.lineSeparator());

        SlackNotificationService.getInstance().sendNotification(env.getEnvironmentType(), "Sign Up Offer", null,
                SlackNotification.FREE_CHAI_DELIVERY.getChannel(env.getEnvironmentType()), message.toString());
    }

    private void updateSignUpOfferSheet(WebOfferDetail wod) throws IOException {
        try {
            List<List<Object>> list = new ArrayList<>();
            List<Object> offerDetails = Arrays.asList(
                    wod.getWebOfferDetailId(),
                    wod.getOfferType(),
                    wod.getCustomerId(),
                    wod.getCustomerName(),
                    wod.getCustomerContact(),
                    wod.getCreationTime() != null ? AppUtils.getFormattedTime(wod.getCreationTime()) : null,
                    wod.getTimeOfDelivery(),
                    wod.getDateOfDelivery(),
                    wod.getCompleteAddress(),
                    wod.getCity(),
                    wod.getPinCode(),
                    wod.getProductId(),
                    wod.getProductName(),
                    wod.getQuantity(),
                    wod.getPrice(),
                    wod.getDimension(),
                    wod.getVariants(),
                    wod.getAddons(),
                    wod.getRecipeId(),
                    wod.getBrandId(),
                    wod.getUnitId()
            );
            list.add(offerDetails);
            GoogleSheetLoader loader = new GoogleSheetLoader();
            loader.writeRowsSheet("<EMAIL>",
                    AppUtils.isDev(env.getEnvironmentType()) ? "14gkvJQU_jLuEwdefLyYiFvio7EmmiNTHQIULwE1wQ-Q" : "1mRdKfZ8mM6gd8mv7tc1hruyn3ugHCSenZtD8eIu4nRc",
                    AppConstants.SIGNUP_OFFER_DELIVERY, list);
        } catch (IOException ex) {
            throw new IOException(" Failed to update the google sheet for " + wod.getWebOfferDetailId() + " ex:: " + ex);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public WebOfferCouponRedemptionDetail addCouponRedemptionDetail(WebOfferCouponRedemptionDetail redemptionDetail) {
        return customerDao.add(redemptionDetail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public SignUpTimeSlotCount fetchSignUpOfferTimeSlots(String dateOfDelivery) throws Exception {
        return customerOfferDao.fetchSignUpOfferTimeSlots(dateOfDelivery);
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    @Override
	public boolean hasCustomerReceivedPostOrderOffer(int customerId, Integer lastNDays, Integer campaignId) {
    	return customerOfferDao.hasCustomerReceivedPostOrderOffer(customerId, lastNDays, campaignId);
	}

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    @Override
	public CustomerCampaignOfferDetail getActiveCustomerOffer(int customerId, String strategy) {
    	return customerOfferDao.getActiveCustomerOffer(customerId, strategy);
	}

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean setCustomerJourneyState(CustomerCampaignJourney journeyDetail) {
        if(Objects.isNull(journeyDetail.getCampaignJourneyId())){
            journeyDetail = customerOfferDao.add(journeyDetail);
        }else{
            journeyDetail = customerOfferDao.update(journeyDetail);
        }
        if(Objects.nonNull(journeyDetail)){
            return true;
        }
        return false;
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
	public boolean updateOfferApplicationDetails(String couponCode, Integer customerId, Integer orderId, BigDecimal savings) {
		return customerOfferDao.updateOfferApplicationDetails(couponCode, customerId, orderId, savings);
	}

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	@Override
	public CustomerOneViewData getCustomerOneViewData(int customerId, Integer brandId, List<Integer> excludeOrderIds) {
		return customerOfferDao.getCustomerOneViewData(customerId, brandId, excludeOrderIds);
	}

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	@Override
	public CustomerDineInView getCustomerDineInView(int customerId, Integer brandId, List<Integer> excludeOrderIds) {
		return customerOfferDao.getCustomerDineInView(customerId, brandId, excludeOrderIds);
	}

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    @Override
    public CustomerTransactionViewData getCustomerTransactionViewData(int customerId, Integer brandId, List<Integer> excludeOrderIds) {
        return customerOfferDao.getCustomerTransactionViewData(customerId, brandId, excludeOrderIds);
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	@Override
	public CustomerEmailData getCustomerEmailData(int customerId, Integer brandId) {
		return customerOfferDao.getCustomerEmailData(customerId, brandId);
	}


	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public List<CustomerCampaignOfferDetail> createPostOrderOffer(Integer brandId, Integer unitId, Integer campaignId,
                                                                  String countryCode, CouponCloneResponse response, Map<String, Pair<Integer, String>> customerNumberToIdMapping, CampaignDetail campaignDetail, CustomerRepeatType type, Integer journey) {
		List<CustomerCampaignOfferDetail> list = new ArrayList<>();
		for (String contactNumber : response.getMappings().keySet()) {
			list.add(get(brandId, unitId, campaignId, response.getMappings().get(contactNumber),
					response.getDescription(), response.getCode(),
					customerNumberToIdMapping.get(contactNumber).getKey(), null, contactNumber, countryCode,
					customerNumberToIdMapping.get(contactNumber).getValue(),campaignDetail, type,journey));
		}
		customerOfferDao.addAll(list);
		return list;
	}


	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public CustomerCampaignOfferDetail createPostOrderOffer(Integer brandId, Integer unitId, Integer campaignId, int customerId, Integer orderId,
                                                            String contactNumber, String countryCode, CouponData response, String firstName,
                                                            String description, String cloneCopunCode, CampaignDetail campaignDetail, CustomerRepeatType type, Integer journeyNumber, CreateNextOfferRequest request) {
		CustomerCampaignOfferDetail detail = get(brandId, unitId, campaignId, response, description, cloneCopunCode,
				customerId, orderId, contactNumber, countryCode, firstName,campaignDetail,type,journeyNumber);
        detail.setCouponType(CouponType.INTERNAL.name());
        if(Objects.nonNull(request)){
            detail.setUtmMedium(request.getUtmMedium());
            detail.setUtmSource(request.getUtmSource());
        }
		customerOfferDao.add(detail);
		return detail;
	}

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public CustomerCampaignOfferDetail createDeliveryPostOrderOffer(Integer brandId, Integer unitId, Integer campaignId,
                                                                    Customer customer, Integer orderId, String contactNumber, String countryCode, CouponData response,
                                                                    String firstName, String description, String cloneCouponCode, DeliveryCouponDetailData deliveryCoupon,
                                                                    Boolean isCloneCoupon, CampaignDetail campaignDetail, CustomerRepeatType type, Integer journeyNumber, CreateNextOfferRequest request) {
        CustomerCampaignOfferDetail detail = get(brandId, unitId, campaignId, response, description, cloneCouponCode,
                customer.getId(), orderId, contactNumber, countryCode, firstName,campaignDetail,type,journeyNumber);
        detail.setCouponType(CouponType.EXTERNAL.name());
        detail.setChannelPartner(deliveryCoupon.getChannelPartnerId());
        if(Objects.nonNull(request)){
            detail.setUtmMedium(request.getUtmMedium());
            detail.setUtmSource(request.getUtmSource());
        }
        offerService.updateAllocationOfCoupon(deliveryCoupon, customer.getId(), customer.getContactNumber(), campaignId, orderId);
        customerOfferDao.add(detail);
        return detail;
    }

    private CustomerCampaignOfferDetail get(Integer brandId, Integer unitId, Integer campaignId, CouponData data, String offerText,
                                            String cloneCode, Integer customerId, Integer orderId, String contactNumber, String countryCode,
                                            String firstName, CampaignDetail campaignDetail, CustomerRepeatType type, Integer journey) {
        CustomerCampaignOfferDetail detail = new CustomerCampaignOfferDetail();
        CampaignMapping mapping = isNextJourney(campaignDetail, journey, type);
		detail.setCampaignCloneCode(cloneCode);
		detail.setCampaignId(campaignId);
		setCustomerData(detail, customerId, contactNumber, countryCode, firstName);
		detail.setIsNotificationRequired(AppConstants.YES);
		detail.setOfferCreateOrderId(orderId);
		detail.setStatus(AppConstants.ACTIVE);
		detail.setBrandId(brandId);
		detail.setUnitId(unitId);
        detail.setCurrentJourneyNumber(journey);
        detail.setCustomerType(type.name());
        if (Objects.nonNull(data)) {
            detail.setCouponCode(data.getCoupon());
            detail.setCouponDetailId(data.getCouponDetailId());
            detail.setOfferDetailId(data.getOfferDetailId());
            detail.setCouponStartDate(AppUtils.getDate(data.getStartDate(), AppUtils.DATE_FORMAT_STRING));
            detail.setCouponEndDate(AppUtils.getDate(data.getEndDate(), AppUtils.DATE_FORMAT_STRING));
            detail.setReminderDays(campaignDetail.getMappings().get(type.name()).get(journey).getReminderDays());
            if(Objects.nonNull(mapping)){
                detail.setNextJourneyNumber(journey+1);
                detail.setNextOfferDate(AppUtils.addDays(detail.getCouponEndDate(),1));
            }
        }
		detail.setCouponGenerationTime(AppUtils.getCurrentTimestamp());
		detail.setOfferText(offerText);
		return detail;
	}
    private CampaignMapping isNextJourney(CampaignDetail campaignDetail , Integer journey, CustomerRepeatType type){
        CampaignMapping mapping = campaignDetail.getMappings().get(type.name()).get(journey+1);
        if(Objects.nonNull(mapping)){
            LOG.info("Customer next journey found customer type : {}, journey : {}", type.name(),mapping.getJourney());
        }else{
            LOG.info("No next journey found for customer type : {}, journey : {}", type.name(), journey+1);
        }
        return mapping;
    }

	private void setCustomerData(CustomerCampaignOfferDetail detail, int customerId, String contactNumber,
			String countryCode, String firstName) {
		detail.setContactNumber(contactNumber);
		detail.setCountryCode(countryCode);
		detail.setCustomerId(customerId);
		detail.setFirstName(firstName);
	}

	@Override
	public CouponCloneResponse getCloneCode(String startDay, List<String> contactNumbers, CustomerRepeatType type,
                                            String cloneCode,  int usageCount, int validityInDays, String prefix,
			Integer applicableUnitId, String applicableRegion)
			throws DataUpdationException {
		if (type.equals(CustomerRepeatType.NEW) && AppConstants.LOYAL_TEA_COUPON_CODE.equals(cloneCode)) {
			CouponCloneResponse response = new CouponCloneResponse();
            String endDay = AppUtils.getDateString(AppUtils.getDayBeforeOrAfterDay(AppUtils.getDate(startDay,AppUtils.DATE_FORMAT_STRING),
                    validityInDays-1));
			response.setCode(cloneCode);
			response.setDescription("A Free Chai");
			response.setIdentifier(IdentifierType.CONTACT_NUMBER);
            Map<String, CouponData> dataMap = new HashMap<>();
			for(String contactNumber : contactNumbers) {
				dataMap.put(contactNumber, new CouponData(cloneCode, startDay, endDay, 1, null, null));
			}
            response.setMappings(dataMap);
			return response;
		} else {
			CouponDetailData clone = offerDao.getCoupon(cloneCode);
			CloneCouponData cloneCoupon = getCloneCouponData(usageCount,validityInDays, prefix, clone, applicableUnitId, applicableRegion);
			return offerService.createCoupon(cloneCoupon, contactNumbers, startDay, applicableUnitId, applicableRegion, clone);
		}

	}

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public DeliveryCouponDetailData getDeliveryCloneCode(String code, Integer brandId, Boolean getClonedCoupon) {
        DeliveryCouponDetailData data;
        if(getClonedCoupon){
            data = offerDao.getDeliveryCoupon(code, brandId);
            if(Objects.nonNull(data)){
                data.setDeliveryCouponStatus(DeliveryCouponStatus.PROCESSING.name());
                offerDao.update(data);
            }
        }else{
            data = offerDao.getMasterCoupon(code, brandId);
        }
        return data;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public CouponDetailData getCouponDetailData(String code) {
        return offerDao.getCoupon(code);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void addCustomerMappingCouponData(CouponDetailData couponDetail, String contactNumber) {
        CouponDetailMappingData couponMapping = new CouponDetailMappingData();
        couponMapping.setCouponDetail(couponDetail.getCouponDetailId());
        couponMapping.setDataType(String.class.getCanonicalName());
        couponMapping.setMappingGroup(1);
        couponMapping.setMappingType(CouponMappingType.CONTACT_NUMBER.name());
        couponMapping.setMappingValue(contactNumber);
        couponMapping.setMinValue("1");
        couponMapping.setStatus(AppConstants.ACTIVE);
        offerDao.add(couponMapping);
    }

    @Override
    public CouponCloneResponse getCloneCodeForDefault(List<String> contactNumbers, CustomerRepeatType type, String code, Integer couponDelay,
                                                      Integer usageCount, Integer validityInDays, String prefix, Integer applicableUnitId, String applicableRegion, CouponDetailData clone) throws DataUpdationException {
        couponDelay = (Objects.nonNull(couponDelay)) ? couponDelay :0;
        Date today = AppUtils.getNextDate(AppUtils.addDays(AppUtils.getBusinessDate(),couponDelay-1));
        Date startDate = null;
        if(AppUtils.isBefore(today,clone.getStartDate())){
            startDate = clone.getStartDate();
        }else{
            startDate = today;
        }
        Date endDate = AppUtils.addDays(startDate,validityInDays);
        if(!AppUtils.isBefore(endDate,clone.getEndDate())){
            endDate = AppUtils.addDays(clone.getEndDate(),1);
        }
        validityInDays = AppUtils.getDaysDiff(endDate, startDate);
        CloneCouponData cloneCoupon = getCloneCouponData(usageCount,validityInDays, prefix, clone, applicableUnitId, applicableRegion);
        return offerService.createCoupon(cloneCoupon, contactNumbers, AppUtils.getDateString(startDate,AppUtils.DATE_FORMAT_STRING), applicableUnitId, applicableRegion, clone);
    }

    @Override
    public CustomerCampaignOfferDetail getActiveOfferCampaign(int customerId, Integer campaignId) {
        return customerOfferDao.getActiveOfferCampaign(customerId,campaignId);
    }

    @Override
    public boolean hasCustomerReceivedDNBO(int customerId, Integer dinePostOrderOfferCheckLastNDaysValue, Integer campaignId) {
        return customerOfferDao.hasCustomerReceivedDNBO(customerId, dinePostOrderOfferCheckLastNDaysValue, campaignId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean cancelSubscription(Integer customerId) throws DataUpdationException {
        SubscriptionPlan subscriptionPlan = subscriptionDao.getActiveSubscription(customerId);
        if(AppConstants.CHAI_PREPAID.equals(subscriptionPlan.getSubscriptionPlanCode())){
            LOG.info("Can't cancel chaayos subscription of type {}", AppConstants.CHAI_PREPAID);
            throw new DataUpdationException("Can't cancel chaayos subscription of type "+AppConstants.CHAI_PREPAID);
        }
        if (Objects.nonNull(subscriptionPlan) && subscriptionPlan.getStatus().equals(AppConstants.ACTIVE)) {
            SubscriptionPlanEvent planEvent = subscriptionDao.getSubscriptionPlanEvent(subscriptionPlan.getLastRenewalEventId());
            if (Objects.nonNull(subscriptionPlan.getEventType())
                    && SubscriptionEventType.NEW_SUBSCRIPTION.name().equals(subscriptionPlan.getEventType())
                    && (Objects.isNull(subscriptionPlan.getOverAllSaving())
                    || BigDecimal.ZERO.compareTo(subscriptionPlan.getOverAllSaving()) == 0)) {
                subscriptionPlan.setStatus(AppConstants.IN_ACTIVE);
                subscriptionPlan.setEventType(SubscriptionEventType.SUBSCRIPTION_CANCELLED.name());
                subscriptionDao.update(subscriptionPlan);
                planEvent.setStatus(AppConstants.IN_ACTIVE);
                planEvent.setEventType(SubscriptionEventType.SUBSCRIPTION_CANCELLED.name());
                subscriptionDao.update(subscriptionPlan);
                return true;
            } else if (Objects.nonNull(subscriptionPlan.getEventType()) &&
                    SubscriptionEventType.RENEW_BEFORE_EXPIRY.name().equals(subscriptionPlan.getEventType())
                    && (Objects.isNull(planEvent.getSubscriptionSavings())
                    || BigDecimal.ZERO.compareTo(planEvent.getSubscriptionSavings()) == 0)) {
                planEvent.setStatus(AppConstants.IN_ACTIVE);
                planEvent.setEventType(SubscriptionEventType.SUBSCRIPTION_CANCELLED.name());
                subscriptionDao.update(planEvent);
                SubscriptionPlanEvent subscriptionPlanEvent = subscriptionDao.getActiveSubscriptionEvent(subscriptionPlan);
                subscriptionPlan.setEventType(subscriptionPlanEvent.getEventType());
                subscriptionPlan.setPlanEndDate(subscriptionPlanEvent.getPlanEndDate());
                subscriptionDao.update(subscriptionPlan);
                return true;
            }
        }
        return false;
    }


    private CloneCouponData getCloneCouponData(int usageCount, int validityInDays, String prefix,
			CouponDetailData clone, Integer applicableUnitId, String applicableRegion) {
		CloneCouponData data = new CloneCouponData();
		data.setCloneCouponCode(clone.getCouponCode());
		data.setOfferDetailId(clone.getOfferDetail().getOfferDetailId());
		data.setPrefix(prefix);
		data.setUsageCount(usageCount);
        data.setMaxCustomerUsage(clone.getMaxCustomerUsage());
		data.setValidityInDays(validityInDays);
		data.setApplicableRegion(applicableRegion);
		data.setApplicableUnitId(applicableUnitId);
		return data;
	}

    private CloneCouponData getDeliveryCloneCouponData(int usageCount, int validityInDays, String prefix,
                                               DeliveryCouponDetailData clone, Integer applicableUnitId, String applicableRegion) {
        CloneCouponData data = new CloneCouponData();
        data.setCloneCouponCode(clone.getCouponCode());
        data.setPrefix(prefix);
        data.setUsageCount(usageCount);
        data.setValidityInDays(validityInDays);
        data.setApplicableRegion(applicableRegion);
        data.setApplicableUnitId(applicableUnitId);
        return data;
    }

    @Override
    public SubscriptionInfoDetail getSubscriptionInfoDetailForCustomer(int customerId) {
        BigDecimal overallSavings = BigDecimal.ZERO;
        int remainingChai = 0;
        List<SubscriptionPlan> subscriptionPlanList = subscriptionDao.getAllSubscriptionPlanForCustomer(customerId);
        List<SubscriptionPlan> planListByCode = new ArrayList<>();
        SubscriptionPlan plan = null;
        String lastPlanCode = "";
        if(Objects.nonNull(subscriptionPlanList) && !subscriptionPlanList.isEmpty()){
            plan = subscriptionPlanList.get(0);
            lastPlanCode=plan.getSubscriptionPlanCode();
            for(SubscriptionPlan subscriptionPlan : subscriptionPlanList){
                if (lastPlanCode.length() > 0 && subscriptionPlan.getSubscriptionPlanCode().equalsIgnoreCase(lastPlanCode)) {
                    planListByCode.add(subscriptionPlan);
                }
            }
        }
        if(!planListByCode.isEmpty()){
            for(SubscriptionPlan plan1 : planListByCode){
                overallSavings = AppUtils.add(overallSavings,plan1.getOverAllSaving());
            }
        }
        if(Objects.nonNull(plan) && Objects.nonNull(plan.getSubscriptionPlanCode()) && AppConstants.CHAI_PREPAID.equalsIgnoreCase(plan.getSubscriptionPlanCode()) && Objects.nonNull(plan.getFrequencyLimit()) && Objects.nonNull(plan.getOverAllFrequency())){
            if(BigDecimal.ZERO.compareTo(AppUtils.subtract(plan.getFrequencyLimit(), plan.getOverAllFrequency()))<0){
                remainingChai = plan.getFrequencyLimit().subtract(plan.getOverAllFrequency()).intValue();
            }
        }
        if(Objects.nonNull(plan)) {
            try {
                return new SubscriptionInfoDetail(plan.getCustomerId(), plan.getSubscriptionPlanCode(),
                        !plan.getPlanEndDate().before(AppUtils.getCurrentDate())
                                && AppConstants.ACTIVE.equals(plan.getStatus())
                        , plan.getPlanStartDate(), plan.getPlanEndDate()
                        , overallSavings, AppUtils.getActualDayDifference(AppUtils.getCurrentDate(), plan.getPlanEndDate()),remainingChai);
            } catch (Exception e) {
                LOG.error("Exception while getting subscription info detail for customer with id :{}", customerId, e);
            }
        }
        return null;
    }

    @Override
    public List<OfferResponse> getWinbackOffers(){
        return offerService.getWinbackOffers();
    }

    @Override
    public List<DeliveryOfferDetailData> getWinbackOfferForDelivery(){
        return offerService.getWinbackOfferForDelivery();
    }

    @Override
    public CustomerWinbackOfferInfo generateWinbackCoupon(CustomerWinbackOfferInfoDomain domain, Integer customerId){
        return offerService.generateWinbackCoupon(domain,customerId);
    }

    @Override
    public CustomerWinbackOfferInfo markNotified(Integer id){
        CustomerWinbackOfferInfo data =  offerService.markNotified(id);
        if(Objects.nonNull(data) && data.getIsNotified().equals(AppConstants.YES)){
            Map<String, Object> eventData = new HashMap<>();
            Map<String,Object> userProfiles = new HashMap<>();
            eventData.put("customerId",data.getCustomerId());
            eventData.put("contactNumber",data.getContactNumber());
            eventData.put("winbackOfferCode",data.getCouponCode());
            eventData.put("winbackCodeStartDate",data.getStartDate());
            eventData.put("winbackCodeEndDate",data.getEndDate());
            eventData.put("winbackCodeDescription",data.getOfferDescription());
            userProfiles.put("winbackOfferCode",data.getCouponCode());
            userProfiles.put("winbackCodeEndDate",data.getEndDate());
            userProfiles.put("winbackCodeStartDate",data.getStartDate());
            userProfiles.put("winbackCodeDescription",data.getOfferDescription());
            if(!StringUtils.isEmpty(data.getOrderSource())){
                if(data.getOrderSource().equals(AppConstants.DINE_IN)){
                    eventData.put("winbackOrderSource",data.getOrderSource());
                    userProfiles.put("winbackOrderSource",data.getOrderSource());
                } else if (data.getOrderSource().equals(AppConstants.DELIVERY)) {
                    eventData.put("winbackOrderSource",data.getOrderSource());
                    userProfiles.put("winbackOrderSource",data.getOrderSource());
                    if(!StringUtils.isEmpty(data.getChannelPartner())){
                        eventData.put("winbackPartner",data.getChannelPartner());
                        userProfiles.put("winbackPartner",data.getChannelPartner());
                    }
                }
            }
            ThreadPoolTaskExecutor executor = (ThreadPoolTaskExecutor) applicationContext.getBean("taskExecutor");
            executor.execute(() -> {
                try {
                    cleverTapDataPushService.uploadProfileAttributes(data.getCustomerId(),AppUtils.getCurrentTimestamp().toInstant().getEpochSecond()
                            , CleverTapConstants.REGULAR,userProfiles);
                    cleverTapDataPushService.publishCustomEvent(data.getCustomerId(), CleverTapEvents.WINBACK,
                            AppUtils.getCurrentTimestamp().toInstant().getEpochSecond(), CleverTapConstants.REGULAR, eventData);
                } catch (Exception e) {
                    LOG.info("Error in pushing data to cleverTap with exception : {}", e);
                }
            });
        }
        return data;
    }

    @Override
    public List<CustomerWinbackOfferInfo> getWinbackInfo(){
        return offerService.getWinbackInfo();
    }

    @Override
    public View getWinbackSheet(){
        List<CustomerWinbackOfferInfo> data = offerService.getWinbackInfo();
        return offerService.getWinbackSheet(data);
    }

    @Override
    public View getWinbackSheet(Date startDate,Date endDate){
        List<CustomerWinbackOfferInfo> data = offerService.getWinbackInfo(startDate,endDate);
        return offerService.getWinbackSheet(data);
    }

    @Override
    public CouponDetailData getCouponCustomerMapping(String couponCode, String contactNumber){
        return offerDao.getCouponCustomerMapping(couponCode, contactNumber);
    }

    @Override
    public void updateCustomerCouponMapping(CouponDetailData couponDetailData){
        offerDao.updateCustomerCouponMapping(couponDetailData);
    }

    @Override
    public void updateCustomerCouponOfferDetail(CouponDetailData couponDetailData, Integer customerId, Integer orderId) {
        try {
            CustomerOfferDetail customerOfferDetail = customerOfferDetailDao.findByOfferCodeAndCustomerIdAndOrderId(couponDetailData.getCouponCode(), customerId, orderId);
            if (Objects.nonNull(customerOfferDetail)) {
                customerOfferDetail.setAvailed(AppConstants.NO);
                customerOfferDetailDao.save(customerOfferDetail);
            }
        } catch (Exception e) {
            LOG.info("Error while updating customer coupon offer detail for customer id ::: {} , {}", customerId, e.getMessage());
        }
    }
}
