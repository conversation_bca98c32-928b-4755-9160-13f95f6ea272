/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import com.stpl.tech.kettle.google.domain.model.GoogleCustomerOrderData;
import com.stpl.tech.master.OfferLastRedemptionView;

import javax.persistence.Column;
import javax.persistence.ColumnResult;
import javax.persistence.ConstructorResult;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.SqlResultSetMapping;
import javax.persistence.SqlResultSetMappings;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import javax.persistence.UniqueConstraint;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static javax.persistence.GenerationType.IDENTITY;

/**
 * OrderInfo generated by hbm2java
 */
@Entity
@Table(name = "ORDER_DETAIL", uniqueConstraints = @UniqueConstraint(columnNames = "GENERATED_ORDER_ID"))
@SqlResultSetMappings(
		value = {
				@SqlResultSetMapping(
						name = "GoogleCustomerOrderData",
						classes = @ConstructorResult(targetClass = GoogleCustomerOrderData.class,
								columns = {
										@ColumnResult(name = "contactNumber", type = String.class),
										@ColumnResult(name = "transactionTime", type = Date.class),
										@ColumnResult(name = "orderTaxableAmount", type = BigDecimal.class),
										@ColumnResult(name = "generatedOrderId", type = String.class),
										@ColumnResult(name = "gclid", type = String.class)
								})
				),
				@SqlResultSetMapping(name = "OfferLastRedemptionView",
						classes = @ConstructorResult(targetClass = OfferLastRedemptionView.class,
								columns = {
										@ColumnResult(name = "customerId", type = Integer.class),
										@ColumnResult(name = "lastOrderTime", type = Date.class),
										@ColumnResult(name = "orderToday", type = Integer.class),
										@ColumnResult(name = "orderInLastHour", type = Integer.class),})
				)

		}
)
public class OrderDetail implements java.io.Serializable {

	private static final long serialVersionUID = 5441681397850434848L;

	private Integer orderId;
	private String generatedOrderId;
	private Integer unitOrderId;
	private Integer terminalId;
	private Integer tableNumber;
	private Integer customerId;
	private String campaignId;
	private int empId;
	private String orderStatus;
	private String cancelationReason;
	private Integer cancellationReasonId;
	private String wastageType;
	private Integer wastageKettleId;
	private String settlementType;
	private int unitId;
	private String hasParcel;
	private Date businessDate;
	private Date billStartTime;
	private Date billGenerationTime;
	private int billCreationSeconds;
	private Date billingServerTime;
	private Date billCancellationTime;
	private int channelPartnerId;
	private int deliveryPartnerId;
	private Integer deliveryAddress;
	private String orderRemark;
	private BigDecimal totalAmount;
	private BigDecimal saleAmount;
	private BigDecimal promotionalDiscount;
	private BigDecimal totalDiscount;
	private BigDecimal taxableAmount;
	private BigDecimal discountPercent;
	private BigDecimal discountAmount;
	private Integer discountReasonId;
	private String discountReason;
	private String orderSource;
	private String orderType;
	private String orderSourceId;
	private BigDecimal taxAmount;
	private BigDecimal roundOffAmount;
	private BigDecimal settledAmount;
	private Integer printCount;
	private Integer cancelledBy;
	private Integer cancelApprovedBy;
	private Integer pointsRedeemed;
	private String offerCode;
	private String tempCode;
	private BigDecimal savingAmount;
	private String customerName;
	private Integer tokenNumber;
	private Integer linkedOrderId;
	private Integer manualBillBookNo;
	private String outOfDelivery = "N";
	private Integer tableRequestId;
	private String giftCardOrder;
	private String qrLink;
	private SubscriptionDetail subscriptionDetail;
	private OrderInvoiceDetail invoiceDetail;
	private List<OrderItem> orderItems = new ArrayList<OrderItem>(0);
	private List<EmployeeMealData> employeeMealData = new ArrayList<EmployeeMealData>(0);
	private List<OrderSettlement> orderSettlements = new ArrayList<OrderSettlement>(0);
	private List<OrderTaxDetail> orderTaxes = new ArrayList<OrderTaxDetail>(0);
	private List<OrderRePrintDetail> orderReprints = new ArrayList<OrderRePrintDetail>(0);
	private String orderAttribute;
	private Integer brandId;
	private List<PartnerOrderDiscountMapping> partnerOrderDiscountMapping = new ArrayList<PartnerOrderDiscountMapping>(0);
	private String partnerCustomerId;
	private String invoiceId;
	private String isInvoice;
	private BigDecimal collectionAmount;
	private Integer refOrderId;

	private String sourceVersion;
	private BigDecimal serviceTaxAmount;
	private BigDecimal serviceChargePercent;
	private BigDecimal serviceCharge;

	public OrderDetail() {
	}

	public OrderDetail(int unitOrderId, String generatedOrderId, int empId, String orderStatus, int unitId,
			String hasParcel, Date billStartTime, int channelPartnerId, String orderSource) {
		this.unitOrderId = unitOrderId;
		this.generatedOrderId = generatedOrderId;
		this.empId = empId;
		this.orderStatus = orderStatus;
		this.unitId = unitId;
		this.hasParcel = hasParcel;
		this.billStartTime = billStartTime;
		this.channelPartnerId = channelPartnerId;
		this.orderSource = orderSource;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "ORDER_ID", unique = true, nullable = false)
	public Integer getOrderId() {
		return this.orderId;
	}

	public void setOrderId(Integer orderId) {
		this.orderId = orderId;
	}

	@Column(name = "UNIT_ORDER_ID", nullable = false)
	public Integer getUnitOrderId() {
		return this.unitOrderId;
	}

	public void setUnitOrderId(Integer unitOrderId) {
		this.unitOrderId = unitOrderId;
	}

	@Column(name = "GENERATED_ORDER_ID", unique = true, nullable = false, length = 30)
	public String getGeneratedOrderId() {
		return this.generatedOrderId;
	}

	public void setGeneratedOrderId(String generatedOrderId) {
		this.generatedOrderId = generatedOrderId;
	}

	@Column(name = "CUSTOMER_ID")
	public Integer getCustomerId() {
		return this.customerId;
	}

	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}

	@Column(name = "CAMPAIGN_ID")
	public String getCampaignId() {
		return campaignId;
	}

	public void setCampaignId(String campaignId) {
		this.campaignId = campaignId;
	}

	@Column(name = "EMP_ID", nullable = false)
	public int getEmpId() {
		return this.empId;
	}

	public void setEmpId(int empId) {
		this.empId = empId;
	}

	@Column(name = "ORDER_STATUS", nullable = false, length = 30)
	public String getOrderStatus() {
		return this.orderStatus;
	}

	public void setOrderStatus(String orderStatus) {
		this.orderStatus = orderStatus;
	}

	@Column(name = "CANCELATION_REASON", length = 100)
	public String getCancelationReason() {
		return this.cancelationReason;
	}

	public void setCancelationReason(String cancelationReason) {
		this.cancelationReason = cancelationReason;
	}

	@Column(name = "CANCELATION_REASON_ID")
	public Integer getCancellationReasonId() {
		return cancellationReasonId;
	}

	public void setCancellationReasonId(Integer cancelationReasonId) {
		this.cancellationReasonId = cancelationReasonId;
	}

	@Column(name = "WASTAGE_TYPE", length = 20)
	public String getWastageType() {
		return wastageType;
	}

	public void setWastageType(String bookedWastage) {
		this.wastageType = bookedWastage;
	}

	@Column(name = "SETTLEMENT_TYPE", length = 10)
	public String getSettlementType() {
		return this.settlementType;
	}

	public void setSettlementType(String settlementType) {
		this.settlementType = settlementType;
	}

	@Column(name = "ORDER_SOURCE", nullable = false, length = 10)
	public String getOrderSource() {
		return this.orderSource;
	}

	public void setOrderSource(String orderSource) {
		this.orderSource = orderSource;
	}

	@Column(name = "UNIT_ID", nullable = false)
	public int getUnitId() {
		return this.unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	@Column(name = "HAS_PARCEL", nullable = false, length = 1)
	public String getHasParcel() {
		return this.hasParcel;
	}

	public void setHasParcel(String hasParcel) {
		this.hasParcel = hasParcel;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "BILL_START_TIME", nullable = false, length = 19)
	public Date getBillStartTime() {
		return this.billStartTime;
	}

	public void setBillStartTime(Date billStartTime) {
		this.billStartTime = billStartTime;
	}

	@Column(name = "BILL_CREATION_SECONDS", nullable = false, length = 19)
	public int getBillCreationSeconds() {
		return billCreationSeconds;
	}

	public void setBillCreationSeconds(int billCreationSeconds) {
		this.billCreationSeconds = billCreationSeconds;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "BILL_GENERATION_TIME", length = 19)
	public Date getBillGenerationTime() {
		return this.billGenerationTime;
	}

	public void setBillGenerationTime(Date billGenerationTime) {
		this.billGenerationTime = billGenerationTime;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "BILLING_SERVER_TIME", nullable = false, length = 19)
	public Date getBillingServerTime() {
		return billingServerTime;
	}

	public void setBillingServerTime(Date billingServerTime) {
		this.billingServerTime = billingServerTime;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "BILL_CANCELLATION_TIME", nullable = true, length = 19)
	public Date getBillCancellationTime() {
		return billCancellationTime;
	}

	public void setBillCancellationTime(Date billCancellationTime) {
		this.billCancellationTime = billCancellationTime;
	}

	@Column(name = "CHANNEL_PARTNER_ID", nullable = false)
	public int getChannelPartnerId() {
		return this.channelPartnerId;
	}

	public void setChannelPartnerId(int channelPartnerId) {
		this.channelPartnerId = channelPartnerId;
	}

	@Column(name = "TOTAL_AMOUNT", precision = 10)
	public BigDecimal getTotalAmount() {
		return this.totalAmount;
	}

	public void setTotalAmount(BigDecimal totalAmount) {
		this.totalAmount = totalAmount;
	}

	@Column(name = "TAXABLE_AMOUNT", precision = 10)
	public BigDecimal getTaxableAmount() {
		return this.taxableAmount;
	}

	public void setTaxableAmount(BigDecimal taxableAmount) {
		this.taxableAmount = taxableAmount;
	}

	@Column(name = "DISCOUNT_PERCENT", precision = 10)
	public BigDecimal getDiscountPercent() {
		return this.discountPercent;
	}

	public void setDiscountPercent(BigDecimal discountPercent) {
		this.discountPercent = discountPercent;
	}

	@Column(name = "DISCOUNT_AMOUNT", precision = 10)
	public BigDecimal getDiscountAmount() {
		return this.discountAmount;
	}

	public void setDiscountAmount(BigDecimal discountAmount) {
		this.discountAmount = discountAmount;
	}

	@Column(name = "DISCOUNT_REASON_ID")
	public Integer getDiscountReasonId() {
		return this.discountReasonId;
	}

	public void setDiscountReasonId(Integer discountReasonId) {
		this.discountReasonId = discountReasonId;
	}

	@Column(name = "DISCOUNT_REASON")
	public String getDiscountReason() {
		return this.discountReason;
	}

	public void setDiscountReason(String discountReason) {
		this.discountReason = discountReason;
	}

	@Column(name = "TOTAL_TAX", precision = 10)
	public BigDecimal getTaxAmount() {
		return taxAmount;
	}

	public void setTaxAmount(BigDecimal taxAmount) {
		this.taxAmount = taxAmount;
	}

	@Column(name = "ROUND_OFF_AMOUNT", precision = 10)
	public BigDecimal getRoundOffAmount() {
		return this.roundOffAmount;
	}

	public void setRoundOffAmount(BigDecimal roundOffAmount) {
		this.roundOffAmount = roundOffAmount;
	}

	@Column(name = "SETTLED_AMOUNT", precision = 10)
	public BigDecimal getSettledAmount() {
		return this.settledAmount;
	}

	public void setSettledAmount(BigDecimal settledAmount) {
		this.settledAmount = settledAmount;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "orderDetail")
	public List<OrderItem> getOrderItems() {
		return this.orderItems;
	}

	public void setOrderItems(List<OrderItem> orderItems) {
		this.orderItems = orderItems;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "orderDetail")
	public List<EmployeeMealData> getEmployeeMealData() {
		return this.employeeMealData;
	}

	public void setEmployeeMealData(List<EmployeeMealData> employeeMealData) {
		this.employeeMealData = employeeMealData;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "orderDetail")
	public List<OrderSettlement> getOrderSettlements() {
		return this.orderSettlements;
	}

	public void setOrderSettlements(List<OrderSettlement> orderSettlements) {
		this.orderSettlements = orderSettlements;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "orderDetail")
	public List<OrderTaxDetail> getOrderTaxes() {
		return orderTaxes;
	}

	public void setOrderTaxes(List<OrderTaxDetail> orderTaxes) {
		this.orderTaxes = orderTaxes;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "orderDetail")
	public List<OrderRePrintDetail> getOrderReprints() {
		return orderReprints;
	}

	public void setOrderReprints(List<OrderRePrintDetail> orderReprints) {
		this.orderReprints = orderReprints;
	}

	@Column(name = "PRINT_COUNT", nullable = false)
	public Integer getPrintCount() {
		return printCount;
	}

	public void setPrintCount(Integer printCount) {
		this.printCount = printCount;
	}

	@Column(name = "CANCELLED_BY", nullable = true)
	public Integer getCancelledBy() {
		return cancelledBy;
	}

	public void setCancelledBy(Integer cancelledBy) {
		this.cancelledBy = cancelledBy;
	}

	@Column(name = "CANCEL_APPROVED_BY", nullable = true)
	public Integer getCancelApprovedBy() {
		return cancelApprovedBy;
	}

	public void setCancelApprovedBy(Integer cancelApprovedBy) {
		this.cancelApprovedBy = cancelApprovedBy;
	}

	@Column(name = "POINTS_REDEEMED")
	public Integer getPointsRedeemed() {
		return pointsRedeemed;
	}

	public void setPointsRedeemed(Integer pointsRedeemed) {
		this.pointsRedeemed = pointsRedeemed;
	}

	@Column(name = "TERMINAL_ID", nullable = true)
	public Integer getTerminalId() {
		return terminalId;
	}

	public void setTerminalId(Integer terminalId) {
		this.terminalId = terminalId;
	}

	@Column(name = "TABLE_NUMBER", nullable = true)
	public Integer getTableNumber() {
		return tableNumber;
	}

	public void setTableNumber(Integer tableNumber) {
		this.tableNumber = tableNumber;
	}

	@Column(name = "DELIVERY_PARTNER_ID", nullable = true)
	public int getDeliveryPartnerId() {
		return deliveryPartnerId;
	}

	public void setDeliveryPartnerId(int deliveryPartnerId) {
		this.deliveryPartnerId = deliveryPartnerId;
	}

	@Column(name = "ORDER_REMARK", nullable = true)
	public String getOrderRemark() {
		return orderRemark;
	}

	public void setOrderRemark(String orderRemark) {
		this.orderRemark = orderRemark;
	}

	@Column(name = "DELIVERY_ADDRESS", nullable = true)
	public Integer getDeliveryAddress() {
		return deliveryAddress;
	}

	public void setDeliveryAddress(Integer deliveryAddress) {
		this.deliveryAddress = deliveryAddress;
	}

	@Column(name = "ORDER_SOURCE_ID", nullable = true)
	public String getOrderSourceId() {
		return orderSourceId;
	}

	public void setOrderSourceId(String orderSourceId) {
		this.orderSourceId = orderSourceId;
	}

	@Column(name = "SALE_AMOUNT", precision = 10)
	public BigDecimal getSaleAmount() {
		return saleAmount;
	}

	public void setSaleAmount(BigDecimal saleAmount) {
		this.saleAmount = saleAmount;
	}

	@Column(name = "PROMOTIONAL_DISCOUNT", precision = 10)
	public BigDecimal getPromotionalDiscount() {
		return promotionalDiscount;
	}

	public void setPromotionalDiscount(BigDecimal promotionalDiscount) {
		this.promotionalDiscount = promotionalDiscount;
	}

	@Column(name = "TOTAL_DISCOUNT", precision = 10)
	public BigDecimal getTotalDiscount() {
		return totalDiscount;
	}

	public void setTotalDiscount(BigDecimal totalDiscount) {
		this.totalDiscount = totalDiscount;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SUBSCRIPTION_ID", nullable = true)
	public SubscriptionDetail getSubscriptionDetail() {
		return subscriptionDetail;
	}

	public void setSubscriptionDetail(SubscriptionDetail subscriptionDetail) {
		this.subscriptionDetail = subscriptionDetail;
	}

	@Transient
	public OrderInvoiceDetail getInvoiceDetail() {
		return invoiceDetail;
	}

	public void setInvoiceDetail(OrderInvoiceDetail invoiceDetail) {
		this.invoiceDetail = invoiceDetail;
	}

	@Column(name = "OFFER_CODE", nullable = true, length = 30)
	public String getOfferCode() {
		return offerCode;
	}

	public void setOfferCode(String offerCode) {
		this.offerCode = offerCode;
	}

	@Column(name = "TEMP_CODE", nullable = true, length = 10)
	public String getTempCode() {
		return tempCode;
	}

	public void setTempCode(String tempCode) {
		this.tempCode = tempCode;
	}

	@Column(name = "SAVING_AMOUNT", precision = 10)
	public BigDecimal getSavingAmount() {
		return savingAmount;
	}

	public void setSavingAmount(BigDecimal savingAmount) {
		this.savingAmount = savingAmount;
	}

	@Column(name = "CUSTOMER_NAME", nullable = true)
	public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	@Column(name = "WASTAGE_KETTLE_ID", nullable = true)
	public Integer getWastageKettleId() {
		return wastageKettleId;
	}

	public void setWastageKettleId(Integer wastageKettleId) {
		this.wastageKettleId = wastageKettleId;
	}

	@Column(name = "TOKEN_NUMBER", nullable = true)
	public Integer getTokenNumber() {
		return tokenNumber;
	}

	public void setTokenNumber(Integer tokenNumber) {
		this.tokenNumber = tokenNumber;
	}

	@Column(name = "ORDER_TYPE", nullable = true)
	public String getOrderType() {
		return orderType;
	}

	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}

	@Column(name = "LINKED_ORDER_ID", nullable = true)
	public Integer getLinkedOrderId() {
		return linkedOrderId;
	}

	public void setLinkedOrderId(Integer linkedOrderId) {
		this.linkedOrderId = linkedOrderId;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "BUSINESS_DATE", nullable = true, length = 10)
	public Date getBusinessDate() {
		return businessDate;
	}

	public void setBusinessDate(Date businessDate) {
		this.businessDate = businessDate;
	}

	@Column(name = "MANUAL_BILL_BOOK_NO", nullable = true)
	public Integer getManualBillBookNo() {
		return manualBillBookNo;
	}

	public void setManualBillBookNo(Integer manualBillBookNo) {
		this.manualBillBookNo = manualBillBookNo;
	}

	@Column(name = "OUT_OF_DELIVERY", nullable = true)
	public String getOutOfDelivery() {
		return outOfDelivery;
	}

	public void setOutOfDelivery(String outOfDelivery) {
		this.outOfDelivery = outOfDelivery;
	}

	@Column(name = "TABLE_REQUEST_ID", nullable = true)
	public Integer getTableRequestId() {
		return tableRequestId;
	}

	public void setTableRequestId(Integer tableRequestId) {
		this.tableRequestId = tableRequestId;
	}

	@Column(name = "IS_GIFT_CARD_ORDER", nullable = true)
	public String getGiftCardOrder() {
		return giftCardOrder;
	}

	public void setGiftCardOrder(String giftCardOrder) {
		this.giftCardOrder = giftCardOrder;
	}

	@Column(name = "ORDER_ATTRIBUTE", nullable = true)
	public String getOrderAttribute() {
		return orderAttribute;
	}

	public void setOrderAttribute(String orderAttribute) {
		this.orderAttribute = orderAttribute;
	}

	@Column(name = "QR_LINK", nullable = true)
	public String getQrLink() {
		return qrLink;
	}

	public void setQrLink(String qrLink) {
		this.qrLink = qrLink;
	}

	@Column(name = "BRAND_ID")
	public Integer getBrandId() {
		return brandId;
	}

	public void setBrandId(Integer brandId) {
		this.brandId = brandId;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "orderDetail")
	public List<PartnerOrderDiscountMapping> getPartnerOrderDiscountMapping() {
		return partnerOrderDiscountMapping;
	}

	public void setPartnerOrderDiscountMapping(List<PartnerOrderDiscountMapping> partnerOrderDiscountMapping) {
		this.partnerOrderDiscountMapping = partnerOrderDiscountMapping;
	}

	@Column(name = "PARTNER_CUSTOMER_ID")
	public String getPartnerCustomerId() {
		return partnerCustomerId;
	}

	public void setPartnerCustomerId(String partnerCustomerId) {
		this.partnerCustomerId = partnerCustomerId;
	}


	@Column(name = "INVOICE_ID")
	public String getInvoiceId() {
		return invoiceId;
	}

	public void setInvoiceId(String invoiceId) {
		this.invoiceId = invoiceId;
	}


	@Column(name = "IS_INVOICE")
	public String getIsInvoice() {
		return isInvoice;
	}

	public void setIsInvoice(String  isInvoice) {
		this.isInvoice = isInvoice;
	}


	@Column(name = "COLLECTION_AMOUNT", precision = 10)
	public BigDecimal getCollectionAmount() {
		return collectionAmount;
	}

	public void setCollectionAmount(BigDecimal collectionAmount) {
		this.collectionAmount = collectionAmount;
	}

	@Column(name = "REF_ORDER_ID")
	public Integer getRefOrderId() {
		return refOrderId;
	}

	public void setRefOrderId(Integer refOrderId) {
		this.refOrderId = refOrderId;
	}

	@Column(name = "SOURCE_VERSION" , length = 10)
	public String getSourceVersion() {
		return sourceVersion;
	}

	public void setSourceVersion(String sourceVersion) {
		this.sourceVersion = sourceVersion;
	}

	@Column(name = "SERVICE_CHARGE_AMOUNT", precision = 10)
	public BigDecimal getServiceCharge() {
		return serviceCharge;
	}

	public void setServiceCharge(BigDecimal serviceCharge) {
		this.serviceCharge = serviceCharge;
	}

	@Column(name = "SERVICE_TAX_AMOUNT", precision = 10)
	public BigDecimal getServiceTaxAmount() {
		return serviceTaxAmount;
	}

	public void setServiceTaxAmount(BigDecimal serviceTaxAmount) {
		this.serviceTaxAmount = serviceTaxAmount;
	}

	@Column(name = "SERVICE_CHARGE_PERCENT", precision = 10)
	public BigDecimal getServiceChargePercent() {
		return serviceChargePercent;
	}

	public void setServiceChargePercent(BigDecimal serviceChargePercent) {
		this.serviceChargePercent = serviceChargePercent;
	}
}
