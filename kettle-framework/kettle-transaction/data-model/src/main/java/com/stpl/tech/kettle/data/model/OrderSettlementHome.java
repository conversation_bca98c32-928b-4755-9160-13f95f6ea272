/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

// Generated 23 Jul, 2015 10:12:38 AM by Hibernate Tools 4.0.0

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

/**
 * Home object for domain model class OrderSettlement.
 * 
 * @see com.stpl.tech.kettle.data.model.OrderSettlement
 * <AUTHOR> Tools
 */
@Stateless
public class OrderSettlementHome {

	private static final Log log = LogFactory.getLog(OrderSettlementHome.class);

	@PersistenceContext
	private EntityManager entityManager;

	public void persist(OrderSettlement transientInstance) {
		log.debug("persisting OrderSettlement instance");
		try {
			entityManager.persist(transientInstance);
			log.debug("persist successful");
		} catch (RuntimeException re) {
			log.error("persist failed", re);
			throw re;
		}
	}

	public void remove(OrderSettlement persistentInstance) {
		log.debug("removing OrderSettlement instance");
		try {
			entityManager.remove(persistentInstance);
			log.debug("remove successful");
		} catch (RuntimeException re) {
			log.error("remove failed", re);
			throw re;
		}
	}

	public OrderSettlement merge(OrderSettlement detachedInstance) {
		log.debug("merging OrderSettlement instance");
		try {
			OrderSettlement result = entityManager.merge(detachedInstance);
			log.debug("merge successful");
			return result;
		} catch (RuntimeException re) {
			log.error("merge failed", re);
			throw re;
		}
	}

	public OrderSettlement findById(Integer id) {
		log.debug("getting OrderSettlement instance with productId: " + id);
		try {
			OrderSettlement instance = entityManager.find(OrderSettlement.class, id);
			log.debug("get successful");
			return instance;
		} catch (RuntimeException re) {
			log.error("get failed", re);
			throw re;
		}
	}
}
