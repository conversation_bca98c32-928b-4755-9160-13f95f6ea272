/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core;

public enum KOTType {

	HOT(5, "Hot Beverages"),
	COLD(6, "Cold Beverages"),
	FOOD(7, "Food Items"),
	COMBOS(8, "Combos"),
	BAKERY(10, "Bakery"),
	MERCHANDISE(9, "Merchandise"),
	CLUBBED(-1, "Charity,Book-Wastage");

	private final int id;

	private final String description;

	private KOTType(int id, String description) {
		this.id = id;
		this.description = description;
	}

	public int getId() {
		return id;
	}

	public String getDescription() {
		return description;
	}

}
