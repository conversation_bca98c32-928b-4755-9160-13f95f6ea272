/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core;

public enum ReportDefinitionEnum {

	UNIT_ADHOC_REPORT(110), UNIT_DAILY_MANAGER_REPORT(100), UNIT_MONTHLY_MANAGER_REPORT(103), DAILY_SALES_REPORT(
			102), MONTHLY_SALES_REPORT(104);

	private final int reportDefId;

	private ReportDefinitionEnum(int reportDefId) {
		this.reportDefId = reportDefId;
	}

	public int getReportDefId() {
		return reportDefId;
	}

}
