/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.dao;

import com.stpl.tech.kettle.clevertap.domain.model.GameLeaderBoardDTO;
import com.stpl.tech.kettle.commission.MonthlyAOVDetail;
import com.stpl.tech.kettle.commission.PartnerAOVRequest;
import com.stpl.tech.kettle.core.EmailStatus;
import com.stpl.tech.kettle.core.OrderEmailEntryType;
import com.stpl.tech.kettle.core.cache.CampaignCache;
import com.stpl.tech.kettle.core.cache.OrderInfoCache;
import com.stpl.tech.kettle.core.data.vo.CreateOrderResult;
import com.stpl.tech.kettle.core.data.vo.DelayReason;
import com.stpl.tech.kettle.core.data.vo.PartnerDataConsiderRequest;
import com.stpl.tech.kettle.core.data.vo.SubscriptionProduct;
import com.stpl.tech.kettle.core.exception.CardValidationException;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.data.model.CustomerBrandMapping;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.FeedbackDetail;
import com.stpl.tech.kettle.data.model.GameLeaderBoard;
import com.stpl.tech.kettle.data.model.GamifiedOfferResponse;
import com.stpl.tech.kettle.data.model.LoyaltyScore;
import com.stpl.tech.kettle.data.model.MenuProductCostData;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.data.model.OrderInvoiceDetail;
import com.stpl.tech.kettle.data.model.OrderItem;
import com.stpl.tech.kettle.data.model.OrderItemStatus;
import com.stpl.tech.kettle.data.model.OrderPaymentDetail;
import com.stpl.tech.kettle.data.model.OrderRefundDetail;
import com.stpl.tech.kettle.data.model.OrderSettlement;
import com.stpl.tech.kettle.data.model.OrderStatusEvent;
import com.stpl.tech.kettle.data.model.SpecialOfferDetail;
import com.stpl.tech.kettle.data.model.SubscriptionPlan;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItemConsumable;
import com.stpl.tech.kettle.domain.model.OrderNotification;
import com.stpl.tech.kettle.domain.model.OrderPaymentDetailData;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.domain.model.Settlement;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.data.dao.AbstractDao;
import com.stpl.tech.master.domain.model.Consumable;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.util.TemplateRenderingException;

import java.util.*;

public interface OrderManagementDao extends AbstractDao {

    /**
     * Create oder with the given order details
     *
     * @param order
     * @return
     * @throws DataUpdationException
     */
    public CreateOrderResult createOrder(Order order) throws DataUpdationException;

    /**
     * Update the given order. A settled order cannot be deleted
     *
     * @param order
     * @return
     * @throws DataUpdationException
     */
    public boolean updateOrder(Order order) throws DataUpdationException;

    /**
     * Delete an order. This changes the state of the order to be deleted
     *
     * @param
     * @return
     * @throws DataUpdationException
     * @throws DataNotFoundException
     */
    public OrderStatusEvent deleteOrder(int unitId, String generatedOrderId, int cancelledBy, int cancelApprovedBy,
                                        String reason, Integer reasonId, String bookWastage) throws DataUpdationException, DataNotFoundException, CardValidationException;

    /**
     * @param orderId
     * @param emailId
     * @param isSystemGenerated
     * @param contact
     */
    public void generateOrderEmailEvent(OrderEmailEntryType type, int orderId, int retryCount, String emailId,
                                        boolean isSystemGenerated, boolean isEmailVerified, Date currentTimestamp, String contact);

    public void generateOrderEmailEvent(OrderEmailEntryType type, int orderId, int retryCount, Customer customer,
                                        boolean isSystemGenerated, Date currentTimestamp);

    public boolean updateStatus(int orderEmailId, EmailStatus status, String errorMessage);

    public void addReprintRequest(int orderId, int generatedBy, int approvedBy, String reason);

    public OrderStatusEvent updateOrderStatus(Integer orderId, OrderStatus toStatus, int approvedBy, int generatedBy,
                                              int unitId, String reason, Boolean refund, Integer reasonId, String bookWastage);

    public OrderStatusEvent generateOrderStatusEvent(boolean checkExistingState, int orderId, OrderStatus fromStatus,
                                                     OrderStatus toStatus, int approvedBy, int generatedBy, String reason);

    public OrderStatusEvent generateFailureOrderStatusEvent(boolean checkExistingState, int orderId,
                                                            OrderStatus fromStatus, OrderStatus toStatus, int approvedBy, int generatedBy, String errorStatckTrace,
                                                            String reason);

    void updateCustomerTransaction(int customerId, int brandId);

    public boolean addOrderEnquiryItems(Order order, Integer orderId);

    public boolean changeSettlementMode(int unitId, int orderId, int editedBy, List<Pair<Integer, Integer>> settlements)
            throws DataUpdationException;

    public void setWastageSumoId(int orderId, int sumoId);

    /**
     * @param o
     * @param values
     */
    public int addCost(Order o, Collection<Consumable> values);

    public int remainingBillCount(int unitId, int startNo, int endNo);

    public int validateBillBookNo(int unitId, int billBookNo);

    public boolean outOfDeliveryOrder(int orderId);

    public List<OrderStatusEvent> getOrderTransitionDetail(int orderId);

    public OrderStatusEvent getLastOrderStatusEvent(int orderId);

    public Date getOrderStatusEventTime(int orderId, OrderStatus fromStatus);

    public void saveMonthlyConsumptionData(UnitBasicDetail ubd, int month, int year,
                                           Collection<OrderItemConsumable> values, String source);

    public void markNPSfeedbackCancelled(Integer orderId);

    public void updateNewCustomerFlagForOrders(int unitId, Date businessDate);

    public OrderSettlement addSettlement(OrderDetail order, Settlement item, OrderSettlement oldSettlement)
            throws DataUpdationException;

    public boolean getPaymentStatus(String cartId);

    OrderDetail getOrderByExternalOrderId(String externalOrderId);

    public void updateFeedbackUrl(int orderId, String feedbackUrl);

    Date getCustomerOfferLastAppliedTime(int customerId, String offerCode);

    FeedbackDetail getInAppFeedback(Order order) throws DataNotFoundException;

    public List<PartnerDataConsiderRequest> getListOfChannelPartnerOrderDetail(Date businessDate, List<Integer> brandIds, List<Integer> channelPartnerIds);

    public PartnerDataConsiderRequest getChannelPartnerOrderDetail(Integer orderId);

    Long checkDeliveryOrderForCustomer( Integer customerId);

    public Long checkDineInOrderForCustomer(Integer customerId,Date date);

    void setDiscount(OrderItem info, com.stpl.tech.kettle.domain.model.OrderItem item);

    void setComplimentaryDetails(OrderItem info, com.stpl.tech.kettle.domain.model.OrderItem item, boolean isComboConsituent);

    Integer getMappedVariant(com.stpl.tech.kettle.domain.model.OrderItem item);

    CustomerBrandMapping checkIsNewCustomerBrandWise(Integer customerId, Integer brandId);

    List<MenuProductCostData> getCogsData(Integer unitId, Integer closureId);

    void deleteCogsDrillDown(Integer menuProductCostId) throws DataUpdationException;

    void deleteMenuCogsData(List<Integer> menuProductCostId) throws DataUpdationException;

    List<Integer> unsettledKettleOrdersDetailList(List<Integer> ids);

//    OrderInvoiceDetail saveOrderInvoiceDetail(RequestInvoiceDetail requestInvoiceDetail);
    OrderDetail getOrderDetailByGeneratedOrderId(String generatedOrderId);

    public int getNextStateInvoiceId(String stateCode, String financialYear);
    public OrderInvoiceDetail getInvoiceDetail(String  generatedOrderId);

    boolean alreadyAvailedOffer(Integer customerId, Integer campaignId);

    LoyaltyScore getCustomerLoyaltyScore(Integer customerId);

    List<Object[]> getDayCloseEstimatesData(List<Integer> fountain9UnitIds, Date startDate, Date endDate);

	SubscriptionPlan createSubscription(SubscriptionProduct subscriptionProduct, Customer customer, Integer campaignId, String source, Integer lagDays)
			throws DataUpdationException;

    CustomerInfo getCustomerFromContact(String contactNumber);

    List<String> getOfferStrings(Integer customerId, Integer brandId);

    Map<Integer,SpecialOfferDetail> getActiveOffer(String contactNumber, Integer campaignId, String utmMedium);

    Map<String, List<GamifiedOfferResponse>> getActiveGameOffer(String contactNumber, Integer campaignId, String source, CampaignCache campaignCache);

    Boolean isSpecialOfferExist(String contact, Integer campaignId);

    Boolean isGamifiedOfferExist(String contact, Integer campaignId, String source);

    void createOrderNotificationData(OrderNotification orderNotification, OrderInfo info);

    void resetOverallFrequency(Order orderDetail);


    Boolean updateOrderStatusByKettleAdmin(String generatedOrderId);
    GameLeaderBoard getActiveLeaderboardEntry(int id, Integer campaignId);

    void addReferralScore(String refCode, Integer campaignId);

    List<GameLeaderBoardDTO> getTop10Score( Integer campaignId);

    Integer getRankForContact(String contactNumber, Integer campaignId, Integer score);

    String getValidRefCode(List<String> refCodeList, Integer campaignId);

    public List<OrderItem> getOrderItems(Set<Integer> orderItemsIds);

    public List<MonthlyAOVDetail> getPartnerBrandAndMonthWiseAOVData(PartnerAOVRequest partnerAOVRequest);

    boolean addRidersDelayReason(DelayReason delayReason, OrderInfoCache ordersCache) throws DataNotFoundException, TemplateRenderingException;

    List<OrderDetail> getOrdersCommissionNotCalculated(String startDate, String endDate);

    public List<OrderPaymentDetailData> getOrderPaymentDetailByBatch(Integer customerId, Integer orderId, String paymentSource, String contactNumber, Integer startPosition);

    public List<OrderPaymentDetailData> getOrderPaymentDetailBySourceAndStatusByBatch(String paymentSource, String paymentStatus, Date startDate);

    public OrderItemStatus getOrderItemStatusByOrderItemId(Integer orderItemId);

    List<OrderStatusEvent> getLastOrderTransactionEvent(int orderId);

    List<OrderRefundDetail> getOrderRefundByUnitId(Integer unitId,Date startDate);
    OrderDetail getOrderDetailByLinkedOrderId(Integer linkedOrderId);


/*    List<Object[]> getDataForPointsAllotment(Date startDate, Date endDate);

    List<GameLeaderBoard> getLeaderBoardData(List<Integer> customerIds, List<Integer> campaignIds);

    void updatePointForRedemption(int campaignId, int points, List<Integer> customerIds);

    void updateAllotmentStatus(List<Integer> gamifiedOfferDetailIds);*/
}
