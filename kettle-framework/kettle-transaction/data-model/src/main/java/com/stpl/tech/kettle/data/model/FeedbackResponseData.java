/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * OrderInfo generated by hbm2java
 */
@Entity
@Table(name = "FEEDBACK_RESPONSE_DATA")
public class FeedbackResponseData implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -7787680362360513831L;

	private Integer feedbackResponseDataId;
	private int feedbackResponseId;
	private String responseData;
	private Integer feedbackRating;

	public FeedbackResponseData() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "FEEDBACK_RESPONSE_DATA_ID", unique = true, nullable = false)
	public Integer getFeedbackResponseDataId() {
		return this.feedbackResponseDataId;
	}

	public void setFeedbackResponseDataId(Integer feedbackResponseDataId) {
		this.feedbackResponseDataId = feedbackResponseDataId;
	}

	@Column(name = "FEEDBACK_RESPONSE_ID", nullable = false)
	public int getFeedbackResponseId() {
		return feedbackResponseId;
	}

	public void setFeedbackResponseId(int feedbackResponseId) {
		this.feedbackResponseId = feedbackResponseId;
	}

	@Column(name = "RESPONSE_DATA", nullable = false, length = 500)
	public String getResponseData() {
		return responseData;
	}

	public void setResponseData(String responseData) {
		this.responseData = responseData;
	}

	@Column(name = "FEEDBACK_RATING", nullable = true)
	public Integer getFeedbackRating() {
		return feedbackRating;
	}

	public void setFeedbackRating(Integer feedbackRating) {
		this.feedbackRating = feedbackRating;
	}
}