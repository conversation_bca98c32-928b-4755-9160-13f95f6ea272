package com.stpl.tech.kettle.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Entity
@Table(name = "ORDER_REFUND_DETAIL")
public class OrderRefundDetail implements Serializable {

    private static final long serialVersionUID = 119311297473022695L;

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "ORDER_REFUND_DETAIL_ID", unique = true, nullable = false)
    private Integer orderRefundDetailId;
    @Column(name = "ORDER_ID",nullable = false)
    private Integer orderId;
    @Column(name = "UNIT_ID", nullable = false)
    private Integer unitId;
    @Column(name = "REFERENCE_REFUND_ID")
    private Integer referenceRefundId;
    @Column(name = "REFUND_AMOUNT", nullable = false)
    private BigDecimal refundAmount;
    @Column(name = "REFUND_STATUS", nullable = false)
    private String refundStatus;
    @Column(name = "REFUND_TYPE", nullable = false)
    private String refundType;
    @Column(name = "REFUND_REASON")
    private String refundReason;
    @Column(name = "CREATED_BY", nullable = false)
    private Integer createdBy;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATION_TIME", nullable = false)
    private Date creationTime;
    @Column(name = "OTP_VERIFIED_BY", nullable = false)
    private String otpVerifiedBy;
    @Column(name = "OTP_VERIFIED_CONTACT", nullable = false, length = 12)
    private String otpVerifiedContact;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "UPDATION_TIME", nullable = false)
    private Date updatedTime;
    @Column(name = "ROUND_OFF_AMOUNT", precision = 10)
    private BigDecimal roundOffAmount;
}
