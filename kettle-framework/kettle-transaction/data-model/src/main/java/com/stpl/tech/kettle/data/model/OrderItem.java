/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Transient;

/**
 * OrderItem generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "ORDER_ITEM")
public class OrderItem implements java.io.Serializable {

	private Integer orderItemId;
	private OrderDetail orderDetail;
	private OrderItemInvoice orderItemInvoice;
	private int productId;
	private String productName;
	private String productAliasName;
	private int quantity;
	private BigDecimal price;
	private BigDecimal originalPrice;
	private String hasAddon;
	private BigDecimal totalAmount;
	private BigDecimal paidAmount;
	private BigDecimal discountPercent;
	private BigDecimal discountAmount;
	private Integer discountReasonId;
	private Integer parentItemId;
	private String discountReason;
	private String isComplimentary;
	private String comboConstituent;
	private Integer complimentaryTypeId;
	private String complimentaryReason;
	private String dimension;
	private String recipeProfile;
	private String billType;
	private String taxCode;
	private BigDecimal promotionalDiscount;
	private BigDecimal taxAmount;
	private Integer recipeId;
	private List<OrderItemAddon> orderItemAddons = new ArrayList<OrderItemAddon>(0);
	private List<OrderItemTaxDetail> orderItemTaxes = new ArrayList<OrderItemTaxDetail>(0);
	private Integer cancellationReasonId;
	private String wastageBooked;
	private String takeAway;
	private String recomCategory;
	private String taxDeductedByPartner;
	private String sourceCategory;
	private String sourceSubCategory;

	private String orderItemRemark;

	@Transient
	private String isHoldOn = "N";

	public OrderItem() {
	}

	public OrderItem(OrderDetail orderDetail, int productId, String productName, int quantity, BigDecimal price,
			String hasAddon, BigDecimal totalAmount, BigDecimal paidAmount, String billType, String taxCode) {
		this.orderDetail = orderDetail;
		this.productId = productId;
		this.productName = productName;
		this.quantity = quantity;
		this.price = price;
		this.hasAddon = hasAddon;
		this.totalAmount = totalAmount;
		this.paidAmount = paidAmount;
		this.billType = billType;
		this.taxCode = taxCode;
	}

	public OrderItem(OrderDetail orderDetail, int productId, String productName, int quantity, BigDecimal price,
			String hasAddon, BigDecimal totalAmount, BigDecimal paidAmount, BigDecimal discountPercent,
			BigDecimal discountAmount, Integer discountReasonId, String discountReason, String isComplimentary,
			Integer complimentaryTypeId, String complimentaryReason, String dimension, String billType, String taxCode,
			List<OrderItemAddon> orderItemAddons) {
		this.orderDetail = orderDetail;
		this.productId = productId;
		this.productName = productName;
		this.quantity = quantity;
		this.price = price;
		this.hasAddon = hasAddon;
		this.totalAmount = totalAmount;
		this.paidAmount = paidAmount;
		this.discountPercent = discountPercent;
		this.discountAmount = discountAmount;
		this.discountReasonId = discountReasonId;
		this.discountReason = discountReason;
		this.isComplimentary = isComplimentary;
		this.complimentaryTypeId = complimentaryTypeId;
		this.complimentaryReason = complimentaryReason;
		this.dimension = dimension;
		this.billType = billType;
		this.taxCode = taxCode;
		this.orderItemAddons = orderItemAddons;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "ORDER_ITEM_ID", unique = true, nullable = false)
	public Integer getOrderItemId() {
		return this.orderItemId;
	}

	public void setOrderItemId(Integer orderItemId) {
		this.orderItemId = orderItemId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ORDER_ID", nullable = false)
	public OrderDetail getOrderDetail() {
		return this.orderDetail;
	}

	public void setOrderDetail(OrderDetail orderDetail) {
		this.orderDetail = orderDetail;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ORDER_ITEM_INVOICE_ID", nullable = true)
	public OrderItemInvoice getOrderItemInvoice() {
		return orderItemInvoice;
	}

	public void setOrderItemInvoice(OrderItemInvoice orderItemInvoice) {
		this.orderItemInvoice = orderItemInvoice;
	}

	@Column(name = "PRODUCT_ID", nullable = false)
	public int getProductId() {
		return this.productId;
	}

	public void setProductId(int productId) {
		this.productId = productId;
	}

	@Column(name = "PRODUCT_NAME", nullable = false)
	public String getProductName() {
		return this.productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	@Column(name = "PRODUCT_ALIAS_NAME")
	public String getProductAliasName() {
		return productAliasName;
	}

	public void setProductAliasName(String productAliasName) {
		this.productAliasName = productAliasName;
	}

	@Column(name = "QUANTITY", nullable = false)
	public int getQuantity() {
		return this.quantity;
	}

	public void setQuantity(int quantity) {
		this.quantity = quantity;
	}

	@Column(name = "PRICE", nullable = false, precision = 10)
	public BigDecimal getPrice() {
		return this.price;
	}

	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	@Column(name = "HAS_ADDON", nullable = false, length = 1)
	public String getHasAddon() {
		return this.hasAddon;
	}

	public void setHasAddon(String hasAddon) {
		this.hasAddon = hasAddon;
	}

	@Column(name = "TOTAL_AMOUNT", nullable = false, precision = 10)
	public BigDecimal getTotalAmount() {
		return this.totalAmount;
	}

	public void setTotalAmount(BigDecimal totalAmount) {
		this.totalAmount = totalAmount;
	}

	@Column(name = "AMOUNT_PAID", nullable = false, precision = 10)
	public BigDecimal getPaidAmount() {
		return this.paidAmount;
	}

	public void setPaidAmount(BigDecimal paidAmount) {
		this.paidAmount = paidAmount;
	}

	@Column(name = "DISCOUNT_PERCENT", precision = 10)
	public BigDecimal getDiscountPercent() {
		return this.discountPercent;
	}

	public void setDiscountPercent(BigDecimal discountPercent) {
		this.discountPercent = discountPercent;
	}

	@Column(name = "DISCOUNT_AMOUNT", precision = 10)
	public BigDecimal getDiscountAmount() {
		return this.discountAmount;
	}

	public void setDiscountAmount(BigDecimal discountAmount) {
		this.discountAmount = discountAmount;
	}

	@Column(name = "DISCOUNT_REASON_ID")
	public Integer getDiscountReasonId() {
		return this.discountReasonId;
	}

	public void setDiscountReasonId(Integer discountReasonId) {
		this.discountReasonId = discountReasonId;
	}

	@Column(name = "PARENT_ITEM_ID")
	public Integer getParentItemId() {
		return parentItemId;
	}

	public void setParentItemId(Integer parentItemId) {
		this.parentItemId = parentItemId;
	}

	@Column(name = "DISCOUNT_REASON")
	public String getDiscountReason() {
		return this.discountReason;
	}

	public void setDiscountReason(String discountReason) {
		this.discountReason = discountReason;
	}

	@Column(name = "IS_COMPLIMENTARY", length = 1)
	public String getIsComplimentary() {
		return this.isComplimentary;
	}

	public void setIsComplimentary(String isComplimentary) {
		this.isComplimentary = isComplimentary;
	}

	@Column(name = "COMBO_CONSTITUENT", length = 1)
	public String getComboConstituent() {
		return comboConstituent;
	}

	public void setComboConstituent(String comboConstituent) {
		this.comboConstituent = comboConstituent;
	}

	@Column(name = "COMPLIMENTARY_TYPE_ID")
	public Integer getComplimentaryTypeId() {
		return this.complimentaryTypeId;
	}

	public void setComplimentaryTypeId(Integer complimentaryTypeId) {
		this.complimentaryTypeId = complimentaryTypeId;
	}

	@Column(name = "COMPLIMENTARY_REASON", length = 150)
	public String getComplimentaryReason() {
		return this.complimentaryReason;
	}

	public void setComplimentaryReason(String complimentaryReason) {
		this.complimentaryReason = complimentaryReason;
	}

	@Column(name = "DIMENSION", length = 10)
	public String getDimension() {
		return this.dimension;
	}

	public void setDimension(String dimension) {
		this.dimension = dimension;
	}

	@Column(name = "BILL_TYPE", nullable = false, length = 10)
	public String getBillType() {
		return this.billType;
	}

	public void setBillType(String billType) {
		this.billType = billType;
	}

	@Column(name = "TAX_CODE", nullable = true, length = 40)
	public String getTaxCode() {
		return taxCode;
	}

	public void setTaxCode(String taxCode) {
		this.taxCode = taxCode;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "orderItem")
	public List<OrderItemAddon> getOrderItemAddons() {
		return this.orderItemAddons;
	}

	public void setOrderItemAddons(List<OrderItemAddon> orderItemAddons) {
		this.orderItemAddons = orderItemAddons;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "orderItem")
	public List<OrderItemTaxDetail> getOrderItemTaxes() {
		return orderItemTaxes;
	}

	public void setOrderItemTaxes(List<OrderItemTaxDetail> orderItemTaxes) {
		this.orderItemTaxes = orderItemTaxes;
	}

	@Column(name = "PROMOTIONAL_DISCOUNT", nullable = true, length = 10)
	public BigDecimal getPromotionalDiscount() {
		return promotionalDiscount;
	}

	public void setPromotionalDiscount(BigDecimal promotionalDiscount) {
		this.promotionalDiscount = promotionalDiscount;
	}

	@Column(name = "RECIPE_ID", nullable = true)
	public Integer getRecipeId() {
		return recipeId;
	}

	public void setRecipeId(Integer recipeId) {
		this.recipeId = recipeId;
	}

	@Column(name = "TOTAL_TAX", precision = 10)
	public BigDecimal getTaxAmount() {
		return taxAmount;
	}

	public void setTaxAmount(BigDecimal taxAmount) {
		this.taxAmount = taxAmount;
	}

	@Column(name = "CANCELATION_REASON_ID")
	public Integer getCancellationReasonId() {
		return cancellationReasonId;
	}

	public void setCancellationReasonId(Integer cancelationReasonId) {
		this.cancellationReasonId = cancelationReasonId;
	}
	@Column(name = "WASTAGE_BOOKED", length = 1)
	public String getWastageBooked() {
		return wastageBooked;
	}

	public void setWastageBooked(String bookedWastage) {
		this.wastageBooked = bookedWastage;
	}


	@Column(name = "TAKE_AWAY", length = 1)
	public String getTakeAway() {
		return takeAway;
	}

	public void setTakeAway(String takeAway) {
		this.takeAway = takeAway;
	}

	@Column(name = "RECIPE_PROFILE", nullable = true)
	public String getRecipeProfile() {
		return recipeProfile;
	}

	public void setRecipeProfile(String recipeProfile) {
		this.recipeProfile = recipeProfile;
	}

	@Column(name = "RECOM_CATEGORY", nullable = true)
	public String getRecomCategory() {
		return recomCategory;
	}

	public void setRecomCategory(String recomCategory) {
		this.recomCategory = recomCategory;
	}

	@Column(name = "ORIGINAL_PRICE")
	public BigDecimal getOriginalPrice() {
		return originalPrice;
	}

	public void setOriginalPrice(BigDecimal originalPrice) {
		this.originalPrice = originalPrice;
	}

	@Column(name = "TAX_DEDUCTED_BY_PARTNER")
	public String getTaxDeductedByPartner() {
		return taxDeductedByPartner;
	}

	public void setTaxDeductedByPartner(String taxDeductedByPartner) {
		this.taxDeductedByPartner = taxDeductedByPartner;
	}

	@Column(name = "SOURCE_CATEGORY" ,nullable=true)
	public String getSourceCategory() {
		return sourceCategory;
	}

	public void setSourceCategory(String sourceCategory) {
		this.sourceCategory = sourceCategory;
	}

	@Column(name = "SOURCE_SUB_CATEGORY" ,nullable=true)
	public String getSourceSubCategory() {
		return sourceSubCategory;
	}

	public void setSourceSubCategory(String sourceSubCategory) {
		this.sourceSubCategory = sourceSubCategory;
	}

	@Column(name = "ORDER_ITEM_REMARK", nullable = true)
	public String getOrderItemRemark() {
		return orderItemRemark;
	}

	public void setOrderItemRemark(String orderItemRemark) {
		this.orderItemRemark = orderItemRemark;
	}

	@Transient
	public String getIsHoldOn() {
		return isHoldOn;
	}

	@Transient
	public void setIsHoldOn(String isHoldOn) {
		this.isHoldOn = isHoldOn;
	}
}
