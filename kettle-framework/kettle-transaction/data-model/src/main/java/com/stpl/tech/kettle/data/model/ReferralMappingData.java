package com.stpl.tech.kettle.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import java.io.Serializable;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "REFERRAL_MAPPING_DATA")
public class ReferralMappingData implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 2141741675632154611L;

	private Integer referralDataId;
	private Integer referrerId; // (Customer Id - Foreign key)
	private String referralCode;
	private String contactNumber;
	private Integer referentId; // (Customer Id - Foreign key)
	private String referentName; // (Added on SignUp)
	private String referralSource; // (Parameter Acquired From URL)
	private String referralSourceCategory;
	private String referralStatus;
	private String referralSourceURL; /// - Used on Sign Up by Referent Customer, through which signup is done
	private String campaignId; // (Campaign Id in URL)
	private Date creationTime;
	private Date lastUpdateTime;
	/*
	 * NOTE: each URL will have following Param campaignId - ID of the campaign
	 * under which this referral is done Ref Source - Source from which customer is
	 * performing sign up (facebook / Whatsapp / ChaayosApp etc.)
	 */

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "REFERRAL_DATA_ID", unique = true, nullable = false)
	public Integer getReferralDataId() {
		return referralDataId;
	}

	public void setReferralDataId(Integer referralDataId) {
		this.referralDataId = referralDataId;
	}

	@Column(name = "REFERRER_ID")
	public Integer getReferrerId() {
		return referrerId;
	}

	public void setReferrerId(Integer referrerId) {
		this.referrerId = referrerId;
	}

	@Column(name = "REFERREL_CODE")
	public String getReferralCode() {
		return referralCode;
	}

	public void setReferralCode(String referralCode) {
		this.referralCode = referralCode;
	}

	@Column(name = "CONTACT_NUMBER")
	public String getContactNumber() {
		return contactNumber;
	}

	public void setContactNumber(String contactNumber) {
		this.contactNumber = contactNumber;
	}

	@Column(name = "REFERENT_ID")
	public Integer getReferentId() {
		return referentId;
	}

	public void setReferentId(Integer referentId) {
		this.referentId = referentId;
	}

	@Column(name = "REFERENT_NAME")
	public String getReferentName() {
		return referentName;
	}

	public void setReferentName(String referentName) {
		this.referentName = referentName;
	}

	@Column(name = "REFERRAL_SOURCE")
	public String getReferralSource() {
		return referralSource;
	}

	public void setReferralSource(String referralSource) {
		this.referralSource = referralSource;
	}

	@Column(name = "REFERRAL_SOURCE_CATEGORY")
	public String getReferralSourceCategory() {
		return referralSourceCategory;
	}

	public void setReferralSourceCategory(String referralSourceCategory) {
		this.referralSourceCategory = referralSourceCategory;
	}

	@Column(name = "REFERRAL_STATUS")
	public String getReferralStatus() {
		return referralStatus;
	}

	public void setReferralStatus(String referralStatus) {
		this.referralStatus = referralStatus;
	}

	@Column(name = "REFERRAL_SOURCE_URL")
	public String getReferralSourceURL() {
		return referralSourceURL;
	}

	public void setReferralSourceURL(String referralSourceURL) {
		this.referralSourceURL = referralSourceURL;
	}

	@Column(name = "CAMPAIGN_ID")
	public String getCampaignId() {
		return campaignId;
	}

	public void setCampaignId(String campaignId) {
		this.campaignId = campaignId;
	}

	@Column(name = "CREATION_TIME")
	public Date getCreationTime() {
		return creationTime;
	}

	public void setCreationTime(Date creationTime) {
		this.creationTime = creationTime;
	}

	@Column(name = "LAST_UPDATE_TIME")
	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

}
