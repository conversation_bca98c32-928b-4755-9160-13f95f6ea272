package com.stpl.tech.kettle.data.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class UnitPullDetail implements Serializable {

    private static final long serialVersionUID = -2163590154321514485L;
    private Integer id;

    private Date creationTime;

    private int createdBy;

    private String witnessedBy;

    private int paymentModeId;

    private BigDecimal pullAmount;

    private String comment;

    private String status;

    private Date pullDate;

    private String pendingReason;

    private String source;

    private int unitId;

    private Integer settlementDetail;

    private Integer closureId;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    public int getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(int createdBy) {
        this.createdBy = createdBy;
    }

    public String getWitnessedBy() {
        return witnessedBy;
    }

    public void setWitnessedBy(String witnessedBy) {
        this.witnessedBy = witnessedBy;
    }

    public int getPaymentModeId() {
        return paymentModeId;
    }

    public void setPaymentModeId(int paymentModeId) {
        this.paymentModeId = paymentModeId;
    }

    public BigDecimal getPullAmount() {
        return pullAmount;
    }

    public void setPullAmount(BigDecimal pullAmount) {
        this.pullAmount = pullAmount;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getPullDate() {
        return pullDate;
    }

    public void setPullDate(Date pullDate) {
        this.pullDate = pullDate;
    }

    public String getPendingReason() {
        return pendingReason;
    }

    public void setPendingReason(String pendingReason) {
        this.pendingReason = pendingReason;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public int getUnitId() {
        return unitId;
    }

    public void setUnitId(int unitId) {
        this.unitId = unitId;
    }

    public Integer getSettlementDetail() {
        return settlementDetail;
    }

    public void setSettlementDetail(Integer settlementDetail) {
        this.settlementDetail = settlementDetail;
    }

    public Integer getClosureId() {
        return closureId;
    }

    public void setClosureId(Integer closureId) {
        this.closureId = closureId;
    }
}
