package com.stpl.tech.kettle.data.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;

@Entity
@Table(name = "RULES_DATA")
public class RulesData implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -3522693408510832432L;

	private Integer ruleId;

	private String ruleName;

	private String ruleDescription;

	private String daySlot;
	
	private String status;

	private List<RulesOptionData> options = new ArrayList<RulesOptionData>(0);

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "RULE_ID", unique = true, nullable = false)
	public Integer getRuleId() {
		return ruleId;
	}

	public void setRuleId(Integer ruleId) {
		this.ruleId = ruleId;
	}

	@Column(name = "RULE_NAME", nullable = false, length = 30)
	public String getRuleName() {
		return ruleName;
	}

	public void setRuleName(String ruleName) {
		this.ruleName = ruleName;
	}

	@Column(name = "RULE_DESCRIPTION", nullable = false, length = 255)
	public String getRuleDescription() {
		return ruleDescription;
	}

	public void setRuleDescription(String ruleDescription) {
		this.ruleDescription = ruleDescription;
	}

	@Column(name = "DAY_SLOT", nullable = false, length = 15)
	public String getDaySlot() {
		return daySlot;
	}

	public void setDaySlot(String daySlot) {
		this.daySlot = daySlot;
	}
	
	@Column(name = "RULE_STATUS", nullable = false, length = 15)
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@OneToMany(fetch = FetchType.EAGER, mappedBy = "rule")
	public List<RulesOptionData> getOptions() {
		return options;
	}

	public void setOptions(List<RulesOptionData> options) {
		this.options = options;
	}

	
	
}
