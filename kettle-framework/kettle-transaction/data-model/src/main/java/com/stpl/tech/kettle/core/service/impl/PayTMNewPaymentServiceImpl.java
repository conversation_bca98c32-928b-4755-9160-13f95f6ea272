package com.stpl.tech.kettle.core.service.impl;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.paytm.pg.merchant.PaytmChecksum;
import com.stpl.tech.kettle.core.exception.PaymentFailureException;
import com.stpl.tech.kettle.core.payment.PaymentPartnerType;
import com.stpl.tech.kettle.core.service.PayTMNewPaymentService;
import com.stpl.tech.kettle.core.service.util.PaytmEndpoints;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.dao.PaymentGatewayDao;
import com.stpl.tech.kettle.data.model.OrderPaymentDetail;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.payment.config.PaytmConfig;
import com.stpl.tech.master.data.model.UnitToPartnerDqrMapping;
import com.stpl.tech.master.data.repository.UnitToPartnerDqrMappingDao;
import com.stpl.tech.master.data.repository.UnitToPartnerEdcMappingDao;
import com.stpl.tech.master.payment.model.MerchantConfig;
import com.stpl.tech.master.payment.model.PaymentRequest;
import com.stpl.tech.master.payment.model.patymNew.PaytmEDCStatusRequest;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.master.data.model.UnitToPartnerEdcMapping;
import com.stpl.tech.master.payment.model.OrderPayment;
import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.master.payment.model.PaymentStatus;
import com.stpl.tech.master.payment.model.patymNew.PaytmFetchPaymentOptionsRequest;
import com.stpl.tech.master.payment.model.patymNew.PaytmOauth;
import com.stpl.tech.master.payment.model.patymNew.PaytmOauthRequest;
import com.stpl.tech.master.payment.model.patymNew.PaytmOauthResponse;
import com.stpl.tech.master.payment.model.patymNew.PaytmParamBody;
import com.stpl.tech.master.payment.model.patymNew.PaytmParamHead;
import com.stpl.tech.master.payment.model.patymNew.PaytmParamResponse;
import com.stpl.tech.master.payment.model.patymNew.PaytmParams;
import com.stpl.tech.master.payment.model.patymNew.PaytmPaymentStatusType;
import com.stpl.tech.master.payment.model.patymNew.PaytmRefundResponse;
import com.stpl.tech.master.payment.model.patymNew.PaytmDQRStatusRequest;
import com.stpl.tech.master.payment.model.patymNew.PaytmStatusResponse;
import com.stpl.tech.master.payment.model.patymNew.PaytmTransactionAmount;
import com.stpl.tech.master.payment.model.patymNew.PaytmUserInfo;
import com.stpl.tech.master.payment.model.patymNew.PaytmValidateSSOTokenResponse;
import com.stpl.tech.master.payment.model.paytm.PaytmCreateRequest;
import com.stpl.tech.master.payment.model.paytm.PaytmCreateRequestResponseWrapper;
import com.stpl.tech.master.payment.model.paytm.PaytmCreateResponse;
import com.stpl.tech.util.JSONSerializer;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.codehaus.jettison.json.JSONException;
import org.codehaus.jettison.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.BufferedReader;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.Base64;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;

@Service
public class PayTMNewPaymentServiceImpl implements PayTMNewPaymentService {

    private static final Logger LOG = LoggerFactory.getLogger(PayTMNewPaymentServiceImpl.class);

    @Autowired
    private PaymentGatewayDao paymentGatewayDao;

    @Autowired
    private PaytmConfig env;

    @Autowired
    private PaytmConfig paytmConfig;

    @Autowired
    private EnvironmentProperties environmentProperties;

    @Autowired
    private UnitToPartnerDqrMappingDao unitToPartnerDqrMappingDao;

    @Autowired
    private UnitToPartnerEdcMappingDao unitToPartnerEdcMappingDao;

    @Override
    public String getSignature(PaytmParams paytmParams) throws Exception {
        JSONObject paytm = new JSONObject();
        JSONObject body = new JSONObject();
        try {
            body.put("requestType", paytmParams.getBody().getRequestType());
            body.put("mid", paytmParams.getBody().getMid());
            body.put("websiteName", paytmParams.getBody().getWebsiteName());
            body.put("orderId", paytmParams.getBody().getOrderId());
            body.put("callbackUrl", paytmParams.getBody().getCallbackUrl());
            body.put("paytmSsoToken", paytmParams.getBody().getPaytmSsoToken());

            JSONObject txnAmount = new JSONObject();
            txnAmount.put("value", paytmParams.getBody().getTxnAmount().getValue());
            txnAmount.put("currency", paytmParams.getBody().getTxnAmount().getCurrency());

            JSONObject userInfo = new JSONObject();
            userInfo.put("custId", paytmParams.getBody().getUserInfo().getCustId());
            body.put("txnAmount", txnAmount);
            body.put("userInfo", userInfo);
        } catch (JSONException e) {
            LOG.error("Error creating signature body ,", e);
            throw e;
        }

        String paytmChecksum ;
        try {
            paytmChecksum = generateChecksum(body.toString());
        } catch (Exception e) {
            LOG.error("Error generating signature", e);
            throw e;
        }
        LOG.info("checksum generated::::::::::"+paytmChecksum);
        JSONObject head = new JSONObject();
        head.put("signature", paytmChecksum);
        paytm.put("head", head);
        paytm.put("body", body);
        return paytm.toString();
    }

    @Override
    public PaytmParamResponse initiatePaytmPayment(PaytmParams paytmParams) throws Exception {
        String post_data = getSignature(paytmParams);
        LOG.info("post_data:::"+post_data);
        String endPoint = env.getDineInPaytmBaseUrl() + "/theia/api/v1/initiateTransaction?mid=" + paytmParams.getBody().getMid()
                + "&orderId=" + paytmParams.getBody().getOrderId();
        return sendRequest(endPoint, post_data, PaytmParamResponse.class);
    }

    private <T> T sendRequest(String endPoint, String post_data, Class<T> output) throws MalformedURLException {
        URL url = new URL(endPoint);
        T paytmParamResponse = null;
        try {
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setDoOutput(true);

            DataOutputStream requestWriter = new DataOutputStream(connection.getOutputStream());
            requestWriter.writeBytes(post_data);
            requestWriter.close();
            StringBuilder response = getResponseAsStringBuilder(connection);
            paytmParamResponse = WebServiceHelper.convert(response.toString(), output);
        } catch (Exception exception) {
            exception.printStackTrace();
        }

        return paytmParamResponse;
    }

    private StringBuilder getResponseAsStringBuilder(HttpURLConnection connection) throws IOException {
        InputStream is = connection.getInputStream();
        BufferedReader rd = new BufferedReader(new InputStreamReader(is));
        String line;
        StringBuilder response = new StringBuilder();
        while ((line = rd.readLine()) != null) {
            response.append(line);
            response.append('\n');
        }
        rd.close();
        return response;
    }


    @Override
    public PaytmCreateRequest createRequestDineIn(OrderPaymentRequest order)
        throws PaymentFailureException, DataNotFoundException {
        PaytmCreateRequest request = createRequestObjectDineIn(order.getGenerateOrderId(), order.getPaidAmount(),
            order.getContactNumber(), order.getRedirectUrl());
        String checkSum = null;
        try {
            TreeMap<String, String> parameters = request.getParameters();
            JSONObject data = new JSONObject();
            for (String key:parameters.keySet()) {
                data.put(key, parameters.get(key));
            }
            checkSum = PaytmChecksum.generateSignature(data.toString(), env.getDineInPaytmMerchantKey());
            LOG.info("Generated checksum ::::::::: {}", checkSum);
        } catch (JSONException e) {
            e.printStackTrace();
        } catch (Exception e) {
            LOG.error("Exception while generating checksum for the request :::::: {}", order.getCartId(), e);
            throw new PaymentFailureException("Error in generating checksum for order " + order.getGenerateOrderId(), e);
        }
        request.setChecksumHash(checkSum);
        return request;
    }

    private PaytmCreateRequest createRequestObjectDineIn(String generatedOrderId, BigDecimal paidAmount,
                                                         String contactNumber, String callbackUrl) {
        PaytmCreateRequest object = new PaytmCreateRequest(generatedOrderId, paidAmount, contactNumber);
        object.setChannelId(env.getDineInPaytmChannelIdApp());
        object.setCallbackUrl(callbackUrl);
        object.setIndustryTypeId(env.getDineInPaytmInductryTypeId());
        object.setMid(env.getDineInPaytmMid());
        object.setWebsite(env.getDineInPaytmWebsiteApp());
        return object;
    }

    @Override
    public PaytmOauth getPaytmOauth(PaytmOauthRequest request) throws MalformedURLException {
        PaytmOauth paytmOauth = null;
        String auth = "Basic " + Base64.getEncoder().encodeToString(
                (env.getDineInPaytmClientId() + ":" + env.getDineInPaytmClientSecret()).getBytes());
        String endpoint = env.getPaytmOauthBaseUrl() + PaytmEndpoints.OAUTH_V2_ENDPOINT;
        try {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append("grant_type=").append(request.getGrantType())
                .append("&scope=").append(request.getScope())
                .append("&code=").append(request.getCode())
                .append("&client_id=").append(env.getDineInPaytmClientId());
            String post_data = stringBuilder.toString();
            OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
            MediaType mediaType = MediaType.parse("application/x-www-form-urlencoded");
            RequestBody body = RequestBody.create(mediaType, post_data);
            Request requestData = new Request.Builder()
                .url(endpoint)
                .method("POST", body)
                .addHeader("Authorization", auth)
                .addHeader("Content-Type", "application/x-www-form-urlencoded")
                .build();
            Response response = client.newCall(requestData).execute();
            String result = response.body().string();
            LOG.info(result);
            paytmOauth = new Gson().fromJson(result, PaytmOauth.class);
        } catch (Exception exception) {
           LOG.error("Error processing paytm Oauth ",exception);
        }
        return paytmOauth;
    }

    @Override
    public PaytmOauthResponse getPaytmOauth2V3(PaytmOauthRequest request) {
        PaytmOauthResponse paytmOauth = null;
        String auth = "Basic " + Base64.getEncoder().encodeToString(
            (env.getDineInPaytmClientId() + ":" + env.getDineInPaytmClientSecret()).getBytes());
        String endpoint = env.getPaytmOauthBaseUrl() + PaytmEndpoints.OAUTH_V3_ENDPOINT;
        LOG.info(auth);
        LOG.info(endpoint);
        try {
            JSONObject requestData = new JSONObject();
            requestData.put("grantType", request.getGrantType());
            requestData.put("code", request.getCode());
            requestData.put("deviceId", request.getDeviceId());
            requestData.put("refreshToken", request.getRefreshToken());
            OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
            MediaType mediaType = MediaType.parse("application/json");
            RequestBody body = RequestBody.create(mediaType, requestData.toString());
            LOG.info(requestData.toString());
            Request requestCall = new Request.Builder()
                .url(endpoint)
                .method("POST", body)
                .addHeader("Authorization", auth)
                .addHeader("Content-Type", "application/json")
                .build();
            Response response = client.newCall(requestCall).execute();
            String result = response.body().string();
            LOG.info(result);
            paytmOauth = new Gson().fromJson(result, PaytmOauthResponse.class);
        } catch (Exception exception) {
            LOG.error("Error processing paytm Oauth2 V3 ",exception);
        }
        return paytmOauth;
    }

    @Override
    public boolean revokePaytmToken(PaytmOauthRequest request) {
        String auth = "Basic " + Base64.getEncoder().encodeToString(
                (env.getDineInPaytmClientId() + ":" + env.getDineInPaytmClientSecret()).getBytes());
        String endpoint = env.getPaytmOauthBaseUrl() + PaytmEndpoints.REVOKE_TOKEN_ENDPOINT;
        LOG.info(auth);
        LOG.info(endpoint);
        try {
            OkHttpClient client = new OkHttpClient().newBuilder()
                    .build();
            Request requestCall = new Request.Builder()
                    .url(endpoint)
                    .method("DELETE", null)
                    .addHeader("Authorization", auth)
                    .addHeader("session_token", request.getCode())
                    .addHeader("x-device-identifier", request.getDeviceId())
                    .addHeader("Content-Type", "application/json")
                    .build();
            Response response = client.newCall(requestCall).execute();
            return response.isSuccessful();
        } catch (Exception exception) {
            LOG.error("Error revoking paytm token ",exception);
        }
        return false;
    }

    @Override
    public PaytmValidateSSOTokenResponse validatePaytmSSOToken(String token) {
        String endpoint = env.getPaytmOauthBaseUrl() + PaytmEndpoints.VALIDATE_TOKEN_ENDPOINT;
        LOG.info(endpoint);
        LOG.info(token);
        try {
            OkHttpClient client = new OkHttpClient().newBuilder().build();
            Request requestCall = new Request.Builder()
                    .url(endpoint).method("GET", null)
                    .addHeader("session_token", token).build();
            Response response = client.newCall(requestCall).execute();
            String result = response.body().string();
            LOG.info(result);
            return new Gson().fromJson(result, PaytmValidateSSOTokenResponse.class);
        } catch (Exception exception) {
            LOG.error("Error validating paytm token ", exception);
        }
        return null;
    }

    @Override
    public String getPaytmAccessToken(PaytmFetchPaymentOptionsRequest optionsRequest) {
        String endpoint = env.getDineInPaytmBaseUrl() + PaytmEndpoints.ACCESS_TOKEN_ENDPOINT;
        try {
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append("mid=").append(env.getDineInPaytmMid())
                .append("&referenceId=").append(optionsRequest.getReferenceId());
            endpoint = endpoint+"?"+stringBuilder.toString();
            JSONObject requestData = new JSONObject();
            JSONObject requestBody = new JSONObject();
            requestBody.put("mid", env.getDineInPaytmMid());
            requestBody.put("referenceId", optionsRequest.getReferenceId());
            requestBody.put("paytmSsoToken", optionsRequest.getSsoToken());
            requestData.put("body", requestBody);
            JSONObject head = new JSONObject();
            head.put("tokenType", "CHECKSUM");
            head.put("token", generateChecksum(requestBody.toString()));
            requestData.put("head", head);
            OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
            MediaType mediaType = MediaType.parse("application/json");
            RequestBody body = RequestBody.create(mediaType, requestData.toString());
            LOG.info(endpoint);
            LOG.info(requestData.toString());
            Request request = new Request.Builder()
                .url(endpoint)
                .method("POST", body)
                .addHeader("Content-Type", "application/json")
                .build();
            Response response = client.newCall(request).execute();
            String result = response.body().string();
            LOG.info(result);
            return result;
        } catch (Exception exception) {
            LOG.error("Error processing paytm Oauth ",exception);
            return null;
        }
    }

    @Override
    public String fetchPaytmPaymentOptions(PaytmFetchPaymentOptionsRequest request) {
        String result = null;
        try {
            JSONObject token = new JSONObject();
            token.put("txnToken", request.getTxnToken());
            JSONObject head = new JSONObject();
            head.put("head", token);
            OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
            MediaType mediaType = MediaType.parse("application/json");
            RequestBody body = RequestBody.create(mediaType, head.toString());
            String endpoint = env.getDineInPaytmBaseUrl() + PaytmEndpoints.FETCH_PAYMENT_OPTIONS_ENDPOINT + "?mid="+env.getDineInPaytmMid();
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append("mid=").append(env.getDineInPaytmMid())
                .append("&orderId=").append(request.getOrderId());
            endpoint = endpoint+"?"+stringBuilder.toString();
            Request requestData = new Request.Builder()
                .url(endpoint)
                .method("POST", body)
                .addHeader("Content-Type", "application/json")
                .build();
            Response response = client.newCall(requestData).execute();
            result = response.body().string();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    @Override
    public String fetchPaytmPaymentOptionsV2(PaytmFetchPaymentOptionsRequest request) {
        String result = null;
        try {
            String resultData = getPaytmAccessToken(request);
            JsonElement jsonElement = new Gson().fromJson(resultData, JsonObject.class).get("body");
            String orderString = new Gson().toJson(jsonElement);
            String accessToken = new Gson().fromJson(orderString, JsonObject.class).get("accessToken").getAsString();
            JSONObject body = new JSONObject();
            body.put("mid", env.getDineInPaytmMid());
            JSONObject head = new JSONObject();
            head.put("tokenType", "ACCESS");
            head.put("token", accessToken);
            JSONObject requestData = new JSONObject();
            requestData.put("body", body);
            requestData.put("head", head);
            OkHttpClient client = new OkHttpClient().newBuilder()
                .build();
            MediaType mediaType = MediaType.parse("application/json");
            RequestBody requestBody = RequestBody.create(mediaType, requestData.toString());
            String endpoint = env.getDineInPaytmBaseUrl() + PaytmEndpoints.FETCH_PAYMENT_OPTIONS_ENDPOINT;
            StringBuilder stringBuilder = new StringBuilder();
            stringBuilder.append("mid=").append(env.getDineInPaytmMid())
                .append("&referenceId=").append(request.getReferenceId());
            endpoint = endpoint+"?"+stringBuilder.toString();
            LOG.info(endpoint);
            LOG.info(requestData.toString());
            Request requestCall = new Request.Builder()
                .url(endpoint)
                .method("POST", requestBody)
                .addHeader("Content-Type", "application/json")
                .build();
            Response response = client.newCall(requestCall).execute();
            result = response.body().string();
            LOG.info("paytm");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return result;
    }

    public String generateChecksum(String body) throws Exception {
        String paytmChecksum;
        try {
            paytmChecksum = PaytmChecksum.generateSignature(body, env.getDineInPaytmMerchantKey());
        } catch (Exception e) {
            LOG.error("Error generating signature", e);
            throw e;
        }
        LOG.info("checksum generated::::::::::"+paytmChecksum);
        return paytmChecksum;
    }


    @Override
    public PaytmStatusResponse getPayTmPaymentStatus(String orderId) throws Exception {

        org.json.JSONObject paytmParams = new org.json.JSONObject();
        org.json.JSONObject body = new org.json.JSONObject();
        body.put("mid", env.getDineInPaytmMid());
        body.put("orderId", orderId);

        String checksum = PaytmChecksum.generateSignature(body.toString(), env.getDineInPaytmMerchantKey());

        org.json.JSONObject head = new org.json.JSONObject();
        head.put("signature", checksum);

        paytmParams.put("body", body);
        paytmParams.put("head", head);
        String post_data = paytmParams.toString();

        String endPoint = env.getDineInPaytmBaseUrl() + "/v3/order/status";

        PaytmStatusResponse paytmStatusResponse = getPaytmRespose(post_data, endPoint);


        return paytmStatusResponse;
    }

    @Override
    public PaytmCreateRequestResponseWrapper createPaymentRequest(OrderPaymentRequest order, Map<String,String> map) throws Exception {
        String ssoToken = map.get("ssoToken");
        PaytmCreateRequest request = createRequestDineIn(order);
        LOG.info("::::::::::::::::: Paytm Request generated ::::::::::::::::");
        LOG.info(JSONSerializer.toJSON(request));
        paymentGatewayDao.createRequest(request, order);
        /*
        Adding signature in header
         */
        PaytmParams paytmParams = createPaytmParams(request, ssoToken);
        PaytmParamResponse paytmParamResponse = initiatePaytmPayment(paytmParams);
        if(paytmParamResponse == null
                || paytmParamResponse.getBody() == null
                || paytmParamResponse.getBody().getResultInfo() == null
                || paytmParamResponse.getBody().getResultInfo().getResultStatus().equals("F")){
            LOG.error("Error in initiating payment request ", paytmParamResponse);
            throw new PaymentFailureException();
        }
        PaytmCreateRequestResponseWrapper paytmCreateRequestResponseWrapper =
                new PaytmCreateRequestResponseWrapper(request, paytmParamResponse);
        return paytmCreateRequestResponseWrapper;
    }

    @Override
    public boolean isPaymentSuccessFul(PaytmCreateResponse response) {
        return response.getStatus().equals(PaytmPaymentStatusType.TXN_SUCCESS.name());
    }

    @Override
    public OrderPaymentDetail handleDisassociatedPayment(OrderPaymentDetail paymentDetail) throws PaymentFailureException {
        try {
            PaytmStatusResponse paytmStatusResponse = getPayTmPaymentStatus(paymentDetail.getExternalOrderId());
            LOG.info("Payment response from paytm ", paytmStatusResponse);
            PaymentStatus status = null;
            if (paytmStatusResponse.getBody().getResultInfo().getResultStatus().equals(PaytmPaymentStatusType.TXN_SUCCESS.name())) {
                status = PaymentStatus.SUCCESSFUL;
            } else {
                status = PaymentStatus.FAILED;
            }
            paymentDetail.setPartnerTransactionId(paytmStatusResponse.getBody().getTxnId());
            paymentDetail.setPaymentStatus(status.toString());

            return paymentGatewayDao.save(paymentDetail);
        } catch (Exception e) {
            LOG.error("Error getting payment status from paytm ", e);
            throw new PaymentFailureException();
        }
    }

    private PaytmParams createPaytmParams(PaytmCreateRequest request ,String ssoToken ){
        PaytmParams paytmParams = new PaytmParams();
        PaytmParamHead head = new PaytmParamHead();
        paytmParams.setHead(head);
        PaytmParamBody body = new PaytmParamBody();
        paytmParams.setBody(body);
        PaytmTransactionAmount txnAmount = new PaytmTransactionAmount();
        body.setTxnAmount(txnAmount);
        txnAmount.setCurrency("INR");
        txnAmount.setValue(request.getTransactionAmount());
        PaytmUserInfo userInfo = new PaytmUserInfo();
        body.setUserInfo(userInfo);
        userInfo.setCustId(request.getCustomerId());
        body.setCallbackUrl(paytmConfig.getDineInCallBackUrl());
        body.setMid(paytmConfig.getDineInPaytmMid());
        body.setOrderId(request.getOrderId());
        body.setPaytmSsoToken(ssoToken);
        body.setRequestType("Payment");
        body.setWebsiteName(paytmConfig.getDineInPaytmWebsiteApp());

        return paytmParams;
    }


    private PaytmStatusResponse getPaytmRespose(String post_data, String endPoint) {
        try {
            PaytmStatusResponse paytmStatusResponse = sendRequest(endPoint, post_data, PaytmStatusResponse.class);
            LOG.info("Paytm transaction status check response::" + new Gson().toJson(paytmStatusResponse));
            return paytmStatusResponse;
        } catch (Exception e){
            LOG.error("Error in getting status from paytm " ,e);
            return null;
        }
    }

    @Override
    public boolean validateResponse(PaytmCreateResponse response, boolean verifyPaytmStatus) {
        boolean flag = false;
        try {
            String paytmChecksum = response.getChecksumHash();
            TreeMap<String, String> params = getChecksumValidationMap(response);
            String body = JSONSerializer.toJSON(response);
            LOG.info("Paytm payment response body " + body );
            LOG.info("Paytm payment checksum " + paytmChecksum);
            boolean isVerifySignature = PaytmChecksum.verifySignature(params, env.getDineInPaytmMerchantKey(), paytmChecksum);
            LOG.info("Paytm signature verification response " + isVerifySignature);
            if (isVerifySignature) {

                flag = response.getStatus().equalsIgnoreCase("TXN_SUCCESS");
                if(verifyPaytmStatus){
                    if(flag){
                        // verify once more from paytm status api
                        PaytmStatusResponse paytmStatusResponse = getPayTmPaymentStatus(response.getOrderId());
                        boolean paymentStatus = false;
                        int count = 0;
                        int maxAttempt = Integer.parseInt(env.getMaxPaytmStatusCheck());
                        while(count < maxAttempt && !paymentStatus){

                            String status = paytmStatusResponse.getBody().getResultInfo().getResultStatus();
                            if(status == null){
                                paymentStatus = false;
                            } else if(status.equals("TXN_SUCCESS")){
                                paymentStatus = true;
                            } else if(status.equals("TXN_FAILURE")){
                                paymentStatus = false;
                            } else {
                                // PENDING
                                paymentStatus = false;
                            }
                            count++;
                        }
                    }
                }
            } else {
                LOG.info("Failed while validating checksum hash from PAYTM for external orderId :::", response.getOrderId());
            }
        } catch (Exception e) {
            LOG.error("Encountered error while validating response", e);
        }
        return flag;
    }

    @Override
    public Map updatePaytmResponse(PaytmCreateResponse response, boolean verifyPaytmStatus) {
        boolean validation = validateResponse(response, verifyPaytmStatus);
        return paymentGatewayDao.updateAndRedirect(response, validation);
    }

    @Override
    public Map updatePaytmResponseWithoutVerification(PaytmCreateResponse response) {
        boolean isSuccessful = isPaymentSuccessFul(response);
        return paymentGatewayDao.updateAndRedirect(response, isSuccessful);
    }

    @Override
    public OrderPayment refundRequest(OrderPayment request)  {
        try {
            return processRefund(request);
        } catch (Exception e){
            request.setPaymentStatus(PaymentStatus.REFUND_FAILED);
            request.setRefundStatus(PaymentStatus.FAILED);
            LOG.error("Error processing refund " , e);
            return request;
        }
    }

    private OrderPayment processRefund(OrderPayment request) throws Exception {
        JSONObject paytmParams = createRefundObject(request);
        String post_data = paytmParams.toString();
        String endPoint = env.getDineInPaytmBaseUrl() + "/refund/apply";
        LOG.info("Request " , JSONSerializer.toJSON(paytmParams));
        PaytmRefundResponse paytmRefundResponse = sendRequest(endPoint, post_data, PaytmRefundResponse.class);
        if(paytmRefundResponse != null && paytmRefundResponse.getBody() != null
                && paytmRefundResponse.getBody().getResultInfo().getResultStatus().equalsIgnoreCase("PENDING")
                && paytmRefundResponse.getBody().getResultInfo().getResultCode().equals(601)){
            request.setPaymentStatus(PaymentStatus.REFUND_PROCESSED);
            request.setRefundStatus(PaymentStatus.CREATED);
            request.setRefundId(paytmRefundResponse.getBody().getRefundId());
        } else {
            request.setPaymentStatus(PaymentStatus.REFUND_FAILED);
            request.setRefundStatus(PaymentStatus.FAILED);
            request.setRefundId(paytmRefundResponse.getBody().getRefundId());
            String error = "Error while creating refund request. Response from Paytm :::: " +
                    JSONSerializer.toJSON(paytmRefundResponse) + " " + post_data;
            LOG.error(error);
        }
        return request;
    }

    private JSONObject createRefundObject(OrderPayment request) throws Exception {
        JSONObject paytmParams = new JSONObject();
        JSONObject body = new JSONObject();
        body.put("mid", env.getDineInPaytmMid());
        body.put("txnType", "REFUND");
        body.put("orderId", request.getExternalOrderId());
        body.put("txnId", request.getPartnerTransactionId());
        body.put("refId", getReundId(request));
        body.put("refundAmount", request.getTransactionAmount().toString());

        String checksum = PaytmChecksum.generateSignature(body.toString(), env.getDineInPaytmMerchantKey());

        JSONObject head = new JSONObject();
        head.put("signature", checksum);

        paytmParams.put("body", body);
        paytmParams.put("head", head);
        return paytmParams;
    }

    private String getReundId(OrderPayment request) {
        return request.getExternalOrderId() + "_REFUND";
    }

    /**
     * Get merchant configuration based on payment mode and merchantId
     */
    private MerchantConfig getMerchantConfiguration(Integer paymentModeId, String merchantId, Integer orderId) {
        try {
            // Check if payment mode is DQR
            if(paymentModeId == AppConstants.PAYMENT_MODE_PAYTM_DQR_UPI) {
                List<UnitToPartnerDqrMapping> mapping = unitToPartnerDqrMappingDao.findByMerchantIdAndStatus(merchantId, AppConstants.ACTIVE);
                UnitToPartnerDqrMapping unitToPartnerDqrMapping = mapping.stream().findFirst().orElse(null);
                if(Objects.nonNull(unitToPartnerDqrMapping)){
                    return MerchantConfig.builder()
                            .merchantId(unitToPartnerDqrMapping.getMerchantId())
                            .merchantKey(unitToPartnerDqrMapping.getMerchantKey())
                            .paymentPartnerType(PaymentPartnerType.PAYTM_DQR.name())
                            .build();
                } else {
                    LOG.warn("No UnitToPartnerDqrMapping found for merchantId: {}", merchantId);
                }
            }
            else {
                List<UnitToPartnerEdcMapping> mapping = unitToPartnerEdcMappingDao.findByMerchantIdAndStatus(merchantId, AppConstants.ACTIVE);
                UnitToPartnerEdcMapping unitToPartnerEdcMapping = mapping.stream().findFirst().orElse(null);
                if(Objects.nonNull(unitToPartnerEdcMapping)){
                    return  MerchantConfig.builder()
                            .merchantId(unitToPartnerEdcMapping.getMerchantId())
                            .merchantKey(unitToPartnerEdcMapping.getMerchantKey())
                            .tId(unitToPartnerEdcMapping.getTId())
                            .paymentPartnerType(PaymentPartnerType.PATYM_EDC.name())
                            .build();
                }
                else {
                    LOG.warn("No UnitToPartnerEdcMapping found for merchantId: {}", merchantId);
                }
            }
        } catch (Exception e) {
            LOG.error("Error getting merchant configuration for order: {}, using default DQR config",
                      orderId, e);
            throw e;
        }
        return null;
    }

    private TreeMap<String, String> getChecksumValidationMap(PaytmCreateResponse paytmCreateResponse) {
        TreeMap<String, String> params = new TreeMap<>();
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        try {
            if(paytmCreateResponse != null) {
                String paytmResponse = mapper.writeValueAsString(paytmCreateResponse);
                System.out.println(paytmResponse);
                JsonElement data = new Gson().fromJson(paytmResponse, JsonObject.class);
                data.getAsJsonObject().entrySet().forEach(stringJsonElementEntry -> {
                    if (!stringJsonElementEntry.getKey().equals("persistentAttributes") &&
                            !stringJsonElementEntry.getKey().equals("reason") &&
                            !stringJsonElementEntry.getKey().equals("partnerOrderId") &&
                            !stringJsonElementEntry.getKey().equals("CHECKSUMHASH") &&
                            !stringJsonElementEntry.getValue().isJsonNull()) {
                        params.put(stringJsonElementEntry.getKey(), stringJsonElementEntry.getValue().getAsString());
                    }
                });
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return params;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRES_NEW)
    public OrderPaymentDetail fetchKettlePaymentStatus(OrderPaymentDetail opd) throws PaymentFailureException {
        String endPoint;
        PaymentRequest paymentRequest;
        String auth=environmentProperties.getKettleClientToken();
        if(opd.getPaymentModeId() == AppConstants.PAYMENT_MODE_PAYTM_DQR_UPI) {
            paymentRequest = createDQRStatusRequest(opd);
            endPoint = env.getPivotServiceBaseUrl() + env.getKettlePaytmDQRStatusUrl();
        } else {
            paymentRequest = createEDCStatusRequest(opd);
            endPoint = env.getPivotServiceBaseUrl() + env.getKettlePaytmEDCStatusUrl();
        }
        LOG.info("Request " , JSONSerializer.toJSON(paymentRequest));
        try {
            PaytmStatusResponse paytmStatusResponse = WebServiceHelper.postRequestWithAuthInternalWithTimeout(endPoint, paymentRequest, PaytmStatusResponse.class, auth);
            if(paytmStatusResponse != null && paytmStatusResponse.getBody() != null) {
                String paymentStatus = null;
                if (PaytmPaymentStatusType.TXN_SUCCESS.name().equals(paytmStatusResponse.getBody().getResultInfo().getResultStatus())) {
                    paymentStatus = PaymentStatus.SUCCESSFUL.name();
                } else if(PaytmPaymentStatusType.TXN_FAILURE.name().equals(paytmStatusResponse.getBody().getResultInfo().getResultStatus())) {
                    paymentStatus = PaymentStatus.FAILED.name();
                } else {
                    paymentStatus = paytmStatusResponse.getBody().getResultInfo().getResultStatus();
                }
                opd.setPaymentStatus(paymentStatus);
                opd.setPartnerPaymentStatus(paytmStatusResponse.getBody().getResultInfo().getResultStatus());
                return paymentGatewayDao.save(opd);
            }
            LOG.info("Response: " + paytmStatusResponse);
        } catch (Exception exception){
            LOG.error("Error making payment status request", exception);
        }
        return opd;
    }

    private PaytmDQRStatusRequest createDQRStatusRequest(OrderPaymentDetail payment) {
        PaytmDQRStatusRequest paytmStatusRequest = new PaytmDQRStatusRequest();
        MerchantConfig merchantConfig = getMerchantConfiguration(payment.getPaymentModeId(), payment.getMerchantId(), payment.getOrderId());
        paytmStatusRequest.setOrderId(payment.getExternalOrderId());
        paytmStatusRequest.setMerchantId(merchantConfig != null ? merchantConfig.getMerchantId() : payment.getMerchantId());
        paytmStatusRequest.setMerchantKey(merchantConfig != null ? merchantConfig.getMerchantKey() : null);
        return paytmStatusRequest;
    }

    private PaytmEDCStatusRequest createEDCStatusRequest(OrderPaymentDetail payment) {
        MerchantConfig merchantConfig = getMerchantConfiguration(payment.getPaymentModeId(), payment.getMerchantId(), payment.getOrderId());
        PaytmEDCStatusRequest paytmStatusRequest = new PaytmEDCStatusRequest();
        paytmStatusRequest.setMerchantTransactionId(payment.getExternalOrderId());
        paytmStatusRequest.setPaytmMid(merchantConfig != null ? merchantConfig.getMerchantId() : payment.getMerchantId());
        paytmStatusRequest.setPaytmTid(merchantConfig != null ? merchantConfig.getTId() : null);
        paytmStatusRequest.setMerchantKey(merchantConfig != null ? merchantConfig.getMerchantKey() : null);
        return paytmStatusRequest;
    }
}
