/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.customer.service.impl;

import com.google.gson.Gson;
import com.stpl.tech.kettle.clm.dao.CustomerFavChaiIncrementalDataDao;
import com.stpl.tech.kettle.clm.model.FavChai.CustomerFavChaiCustomizationDetail;
import com.stpl.tech.kettle.clm.service.SelectPotentialSavingService;
import com.stpl.tech.kettle.core.CampaignOfferDetail;
import com.stpl.tech.kettle.core.LoyaltyEventType;
import com.stpl.tech.kettle.core.OrderEmailEntryType;
import com.stpl.tech.kettle.core.converter.SaveChaiConverter;
import com.stpl.tech.kettle.core.data.vo.CustomerLastOrderInfo;
import com.stpl.tech.kettle.core.data.vo.CustomerResponse;
import com.stpl.tech.kettle.core.data.vo.CustomerTransactionData;
import com.stpl.tech.kettle.core.data.vo.CustomerVisitInfo;
import com.stpl.tech.kettle.core.exception.CardValidationException;
import com.stpl.tech.kettle.core.notification.OTPEmailNotification;
import com.stpl.tech.kettle.core.notification.OTPEmailTemplate;
import com.stpl.tech.kettle.core.notification.OfferOrder;
import com.stpl.tech.kettle.core.notification.sms.CustomerSMSNotificationType;
import com.stpl.tech.kettle.customer.dao.CustomerDao;
import com.stpl.tech.kettle.customer.dao.CustomerFavChaiMappingDao;
import com.stpl.tech.kettle.customer.dao.CustomerInfoDao;
import com.stpl.tech.kettle.customer.dao.LoyaltyDao;
import com.stpl.tech.kettle.customer.dao.SubscriptionPlanDao;
import com.stpl.tech.kettle.customer.service.CardService;
import com.stpl.tech.kettle.customer.service.CustomerService;
import com.stpl.tech.kettle.data.converter.DataConverter;
import com.stpl.tech.kettle.data.dao.FavChaiCustomizationDetailDao;
import com.stpl.tech.kettle.data.model.CashCardDetail;
import com.stpl.tech.kettle.data.model.CrmAppScreenDetail;
import com.stpl.tech.kettle.data.model.CustomerAdditionalDetail;
import com.stpl.tech.kettle.data.model.CustomerCampaignOfferDetail;
import com.stpl.tech.kettle.data.model.CustomerContactInfoMapping;
import com.stpl.tech.kettle.data.model.CustomerDetailChangelog;
import com.stpl.tech.kettle.data.model.CustomerFavChaiMapping;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.CustomerLeadData;
import com.stpl.tech.kettle.data.model.FavChaiCustomizationDetail;
import com.stpl.tech.kettle.data.model.FeedbackDetail;
import com.stpl.tech.kettle.data.model.FeedbackEvent;
import com.stpl.tech.kettle.data.model.LoyaltyScore;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.data.model.OrderEmailNotification;
import com.stpl.tech.kettle.data.model.OrderItemAddon;
import com.stpl.tech.kettle.data.model.SubscriptionPlan;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.CustomerBasicInfo;
import com.stpl.tech.kettle.domain.model.CustomerDineInView;
import com.stpl.tech.kettle.domain.model.CustomerEmailData;
import com.stpl.tech.kettle.domain.model.CustomerOffer;
import com.stpl.tech.kettle.domain.model.CustomerOneViewData;
import com.stpl.tech.kettle.domain.model.CustomerTransactionViewData;
import com.stpl.tech.kettle.domain.model.PotentialSavingData;
import com.stpl.tech.kettle.domain.model.SavedChai;
import com.stpl.tech.kettle.domain.model.SignupOfferStatus;
import com.stpl.tech.kettle.domain.model.SubscriptionInfoDetail;
import com.stpl.tech.kettle.domain.model.truecaller.TrueCallerPostRequest;
import com.stpl.tech.kettle.domain.model.truecaller.TrueCallerVerifiedProfile;
import com.stpl.tech.master.OfferLastRedemptionView;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.master.core.data.vo.NotificationPayload;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.exception.DuplicateRequestException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.ProductRecipeKey;
import com.stpl.tech.master.core.external.cache.RecipeCache;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.core.external.notification.publisher.CustomerCommunicationEventPublisher;
import com.stpl.tech.master.core.external.notification.service.NotificationService;
import com.stpl.tech.master.core.external.notification.service.SMSClientProviderService;
import com.stpl.tech.master.domain.model.Address;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.Brand;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.OtpType;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.JSONSerializer;
import com.stpl.tech.util.excelparser.SheetParser;
import com.stpl.tech.util.excelparser.exception.ExcelParsingException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.util.EntityUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.http.MediaType;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.jms.JMSException;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.stpl.tech.kettle.data.util.KettleUtils.getBrandIdForVerificationEmail;

enum CustomerType {
    OLD, NEW;
}

enum CustomerUpdatedBy {
    TRUECALLER, SELF;
}

@Service
public class CustomerServiceImpl implements CustomerService {

    private static final Logger LOG = LoggerFactory.getLogger(CustomerServiceImpl.class);

    @Autowired
    private CustomerDao dao;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private LoyaltyDao loyaltyDao;

    @Autowired
    private EnvironmentProperties props;

    @Autowired
    private NotificationService notificationService;
    @Autowired
    private SMSClientProviderService providerService;
    @Autowired
    private SubscriptionPlanDao subscriptionPlanDao;

    @Autowired
    private CardService cardService;

    @Autowired
    private SelectPotentialSavingService selectPotentialSavingService;

    @Autowired
    private CustomerInfoDao customerInfoDao;

    @Autowired
    private CustomerCommunicationEventPublisher customerCommunicationEventPublisher;

    @Autowired
    private EnvironmentProperties environment;

    @Autowired
    private CustomerFavChaiIncrementalDataDao customerFavChaiIncrementalDataDao;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private RecipeCache recipeCache;

    @Autowired
    private CustomerFavChaiMappingDao customerFavChaiMappingDao;

    @Autowired
    private FavChaiCustomizationDetailDao favChaiCustomizationDetailDao;



    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Customer getCustomer(int customerId) throws DataNotFoundException {
        return dao.getCustomer(customerId);
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean isCustomerOld(String customerId, int beforeYear) {
        return dao.isCustomerOld(customerId, beforeYear);
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Customer getCustomer(String contactNumber) {
        Customer customer= dao.getCustomer(contactNumber);
        if(Objects.nonNull(customer)){
            SubscriptionPlan plan = subscriptionPlanDao.getSubscription(customer.getId());
            if (Objects.nonNull(plan)) {
                customer.setSubscriptionInfoDetail(new SubscriptionInfoDetail(plan.getCustomerId(), plan.getSubscriptionPlanCode(),
                        !plan.getPlanEndDate().before(AppUtils.getBusinessDate())
                        && AppConstants.ACTIVE.equals(plan.getStatus()), plan.getPlanStartDate(), plan.getPlanEndDate()
                        , Objects.nonNull(plan.getOverAllSaving()) ? plan.getOverAllSaving() : BigDecimal.ZERO,
                        (int) AppUtils.getDayDifference(AppUtils.getBusinessDate(), plan.getPlanEndDate())));

                if(AppUtils.getActualDayDifference(AppUtils.getBusinessDate(),plan.getPlanEndDate()) <= 30){
                    customer.getSubscriptionInfoDetail().setEligible(true);
                }else {
                    customer.getSubscriptionInfoDetail().setEligible(false);
                }
            }
        }
        return customer;
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Customer getCustomer(String code, String contactNumber) throws DataNotFoundException {
        return dao.getCustomer(code, contactNumber);
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Customer addCustomer(Customer customer) throws DataUpdationException {
        return dao.addCustomer(customer);
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateCustomer(Customer customer) throws DataUpdationException {
        return dao.updateCustomer(customer);
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateCustomerAppAction(Customer customer) throws DataUpdationException {
        return dao.updateCustomerAppAction(customer);
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateShopifyCustomer(Customer customer) throws DataUpdationException {
        return dao.updateShopifyCustomer(customer);
    }
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateCustomerEmail(Customer customer,String flag) throws DataUpdationException {
        return dao.updateCustomerEmail(customer,flag);
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateBasicCustomerInfo(Customer customer) throws DataUpdationException {
        try {
            CustomerInfo oldCustomerObj = dao.find(CustomerInfo.class, customer.getId());
            String oldContact = oldCustomerObj.getContactNumber();
            String oldName = oldCustomerObj.getFirstName();
            Integer customerId = customer.getId();
            String oldEmailId = oldCustomerObj.getEmailId();
            String newEmail = AppUtils.checkBlank(customer.getEmailId());
            if(Objects.nonNull(newEmail)){
                if(!masterDataCache.getAllUnitsEmailId().contains(customer.getEmailId())){
                    oldCustomerObj.setEmailId(newEmail);
                }
                else{
                    throw new DataUpdationException("EmailId - "+customer.getEmailId()+" is not valid for sign up");
                }
            }else{
                oldCustomerObj.setEmailId(AppUtils.checkBlank(newEmail));
            }
            oldCustomerObj.setFirstName(customer.getFirstName());
            if (customer.isContactNumberVerified()) {
                oldCustomerObj.setIsNumberVerified("Y");
            }
            if (AppUtils.isBlank(oldCustomerObj.getRefCode()) && !AppUtils.isBlank(customer.getFirstName())) {
                oldCustomerObj.setRefCode(dao.getUniqueRefCode(customer.getFirstName(), oldContact));
                oldCustomerObj.setIsRefSubscriber(AppUtils.NO);
                dao.updateRefCodeInCashData(oldCustomerObj.getCustomerId(), oldCustomerObj.getRefCode());
            }
            if (oldEmailId == null) {
                dao.updateMigrationPoints(oldCustomerObj, customer.getEmailId());
            }
            if (checkIfEmailVerified(oldEmailId, newEmail, customer.isEmailVerified())
                    && !AppConstants.EXCLUDE_CUSTOMER_IDS.contains(oldCustomerObj.getCustomerId())) {
                sendVerificationEmail(newEmail, oldEmailId, oldCustomerObj.getContactNumber(),
                        oldCustomerObj.getFirstName(),oldCustomerObj.getAcquisitionBrandId());
            }
            if (!StringUtils.isEmpty(customer.getGender())) {
                oldCustomerObj.setGender(customer.getGender());
            }
            if (customer.getDateOfBirth() != null) {
                oldCustomerObj.setDateOfBirth(customer.getDateOfBirth());
            }
            if (customer.getAnniversary() != null) {
                oldCustomerObj.setAnniversary(customer.getAnniversary());
            }
            if (Objects.nonNull(customer.getOptWhatsapp())){
                oldCustomerObj.setOptWhatsapp(customer.getOptWhatsapp());
            }
            createChangeLog(oldEmailId, oldContact, oldName, customerId, oldCustomerObj.getIsNumberVerified(),
                    CustomerUpdatedBy.SELF);
            dao.update(oldCustomerObj);
            return true;
        } catch (Exception e) {
            LOG.error("Error while updating customer with number " + customer.getContactNumber(), e);
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void syncCustomerProfileDineInApp(Customer customer) {
        try {
            Customer oldCustomer = getCustomer(customer.getContactNumber());
            if (oldCustomer != null) {
               LOG.debug("found customer profile data: " + new Gson().toJson(oldCustomer));
                CustomerInfo customerInfo = dao.find(CustomerInfo.class, oldCustomer.getId());
                boolean customerUpdated = false;
                boolean addChangelog = false;
                if ((customerInfo.getEmailId() == null || !AppUtils.getStatus(customerInfo.getIsEmailVerified())) && customer.getEmailId() != null && !masterDataCache.getAllUnitsEmailId().contains(customerInfo.getEmailId())) {
                    customerInfo.setEmailId(customer.getEmailId());
                    customerInfo.setIsEmailVerified(AppConstants.NO);
                    customerUpdated = true;
                    addChangelog = true;
                }
                if (!AppConstants.YES.equalsIgnoreCase(customerInfo.getIsNumberVerified())) {
                    customerInfo.setIsNumberVerified(customer.isContactNumberVerified() ? AppConstants.YES : AppConstants.NO);
                    customerUpdated = true;
                }
                if (customer.getFirstName() != null) {
                    if (customer.getLastName() != null && !customerInfo.getLastName().trim().isEmpty()) {
                        customer.setFirstName(customer.getFirstName().trim() + " " + customer.getLastName().trim());
                    }
                    if (!customer.getFirstName().equalsIgnoreCase(customer.getFirstName())) {
                        customerInfo.setFirstName(customer.getFirstName());
                        customerUpdated = true;
                        addChangelog = true;
                    }
                }
                if (!StringUtils.isEmpty(customer.getGender())) {
                    customerInfo.setGender(customer.getGender());
                    customerUpdated = true;
                }
                if (customer.getDateOfBirth() != null) {
                    customerInfo.setDateOfBirth(customer.getDateOfBirth());
                    customerUpdated = true;
                }
                if (customer.getAnniversary() != null) {
                    customerInfo.setAnniversary(customer.getAnniversary());
                    customerUpdated = true;
                }
                if (addChangelog) {
                    createChangeLog(customer.getEmailId(), customer.getContactNumber(), customerInfo.getFirstName(), customer.getId(), customerInfo.getIsNumberVerified(),
                            CustomerUpdatedBy.TRUECALLER);
                }
                if (customerUpdated) {
                    LOG.info("updating customer profile data in KETTLE: " + customerInfo.getEmailId() + customerInfo.getIsEmailVerified());
                    dao.update(customerInfo);
                }
            }
        } catch (Exception e) {
            LOG.error("Error while updating customer with number " + customer.getContactNumber(), e);
        }
    }

    private boolean checkIfEmailVerified(String oldEmailId, String newEmail, boolean isVerified) {
        return AppUtils.checkBlank(oldEmailId) == null && AppUtils.checkBlank(newEmail) != null && !isVerified;
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public int addAddress(int customerId, Address address) throws DataUpdationException {
        return dao.addAddress(customerId, address);
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Address addAddress(String contact, Address address) throws DataUpdationException {
        return dao.addAddress(contact, address);
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void verifyContactNumber(String contactNumber) {
        dao.verifyContactNumber(contactNumber);

    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void verifyEmailAddress(String contactNumber) {
        dao.verifyEmailAddress(contactNumber);

    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public CustomerTransactionData getCustomerTransactionInfo(String contactNumber) throws DataNotFoundException, CardValidationException {
        Customer customer = getCustomer(contactNumber);
        if(Objects.nonNull(customer)){
            try{
                BigDecimal walletBalance = getWalletBalance(customer.getId());
                customer.setWalletBalance(walletBalance);
            }catch (Exception e ){
                LOG.info("Error While Setting Wallet Balance : {} ", e);
            }
        }
        CustomerTransactionData customerTransactionData = dao.getCustomerTransactionInfo(contactNumber,customer);
        return customerTransactionData;
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Customer viewCustomer(String contactNumber) throws DataNotFoundException {
        return dao.viewCustomer(contactNumber);
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean blacklistCustomer(int customerId) throws DataNotFoundException {
        CustomerInfo customer = dao.find(CustomerInfo.class, customerId);
        customer.setIsBlacklisted(AppUtils.setStatus(true));
        customer = (CustomerInfo) dao.update(customer);
        if (customer != null) {
            return true;
        }
        return false;
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean whitelistCustomer(int customerId) throws DataNotFoundException {
        CustomerInfo customer = dao.find(CustomerInfo.class, customerId);
        customer.setIsBlacklisted(AppUtils.setStatus(false));
        customer = (CustomerInfo) dao.update(customer);
        if (customer != null) {
            return true;
        }
        return false;
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Customer getCustomerByDeliveryAddress(int deliveryAddressId) {
        return DataConverter.convert(dao.getCustomerByDeliveryAddress(deliveryAddressId), null, BigDecimal.ZERO);
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<CustomerOffer> getOfferDetail(int customerId, String offerCode) {
        return dao.getOfferDetail(customerId, offerCode);
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void addOfferDetail(int customerId, String offerCode, int orderId) {
        dao.addOfferDetail(customerId, offerCode, orderId);
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public Customer addCustomerUnchecked(Customer customer) throws DataUpdationException {
        return dao.addCustomerUnchecked(customer);
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public void overrideContactVerificationStatus(String contactNumber, boolean contactVerified) {
        dao.overrideContactVerificationStatus(contactNumber, contactVerified);
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    @Override
    public boolean eligibleForSignupOffer(int customerId) {
        return dao.eligibleForSignupOffer(customerId);
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    @Override
    public List<CustomerInfo> getCustomersWithPendingLoyalty(Date startDate, Date endDate) {
        return dao.getCustomersWithPendingLoyalty(startDate, endDate);
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    @Override
    public List<CustomerInfo> getNewCustomers(String firstOrderSource, Date startDate, Date endDate) {
        return dao.getNewCustomers(firstOrderSource, startDate, endDate);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Address> getNewAddress(int id, List<Integer> addressIds) {
        return dao.getNewAddress(id, addressIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void removeInvalidEmails(List<String> invalidEmails) throws DataUpdationException {
        if (!dao.removeInvalidEmails(invalidEmails)) {
            throw new DataUpdationException("Could not update invalid email Ids on Email Bounces");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean sendVerificationEmail(String email, String existingEmail, String contact, String customerName, Integer brandId) {
        boolean flag = false;
        if (AppUtils.checkBlank(email) != null && !email.equals(existingEmail)) {
            generateOrderEmailEventForVerification(contact, email, customerName,getBrandIdForVerificationEmail(brandId));
            flag = true;
        } else {
            LOG.info("Incorrect email entered for the contact {}", contact);
            flag = false;
        }
        return flag;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", propagation = Propagation.REQUIRED)
    public void sendOTPEmail(Customer customer, String token) {
        if (customer.isEmailVerified()) {
            try {
                if(Objects.isNull(token)) {
                    token = notificationService.getOTPMapperInstance().generateOTP(false,
                            OtpType.KETTLE_CRM, customer.getContactNumber(), props.getEnvironmentType());
                }
                OTPEmailTemplate template = new OTPEmailTemplate(props.getBasePath(), token, props.getOTPEmailTemplatePath());
                OTPEmailNotification notification = new OTPEmailNotification(template,
                        props.getEnvironmentType(), customer.getEmailId(), customer.getFirstName());
                notification.sendEmail();
            } catch (EmailGenerationException | DuplicateRequestException e) {
                LOG.error("Encountered error while generating OTP email for :::: {}", customer.getEmailId(), e);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", propagation = Propagation.REQUIRED)
    public void sendOTPEmailBrandWise(Customer customer, String token, Integer brandId) {
        //TODO - Add and change this code when going live with sending email for other brands
//        if (customer.isEmailVerified()) {
//            try {
//                if(Objects.isNull(token)) {
//                    token = notificationService.getOTPMapperInstance().generateOTP(false,
//                            OtpType.KETTLE_CRM, customer.getContactNumber(), props.getEnvironmentType());
//                }
//                OTPEmailTemplate template = new OTPEmailTemplate(props.getBasePath(), token, props.getOTPEmailTemplatePath());
//                OTPEmailNotification notification = new OTPEmailNotification(template,
//                        props.getEnvironmentType(), customer.getEmailId(), customer.getFirstName());
//                notification.sendEmail();
//            } catch (EmailGenerationException | DuplicateRequestException e) {
//                LOG.error("Encountered error while generating OTP email for :::: {}", customer.getEmailId(), e);
//            }
//        }
    }

    private void generateOrderEmailEventForVerification(String contactNumber, String email, String customerName , Integer brandId) {
        OrderEmailNotification emailNotification = new OrderEmailNotification(OrderEmailEntryType.VERIFICATION.name(),
                -1, 1, AppConstants.NO, AppUtils.getCurrentTimestamp());
        emailNotification.setEmailAddress(email);
        emailNotification.setContact(contactNumber);
        emailNotification.setCustomerName(customerName);
        emailNotification.setIsEmailDelivered(AppConstants.NO);
        emailNotification.setIsEmailVerified(AppConstants.NO);
        emailNotification.setBrandId(brandId);
        dao.add(emailNotification);
    }

    @Override
    public TrueCallerVerifiedProfile getTrueCallerVerifiedProfile(String requestId) {
        String requestUrl = props.getTrueCallerServiceUrl() + "/get-profile?requestId=" + requestId;
        HttpGet reqObj = new HttpGet(requestUrl);
        try {
            HttpResponse response = WebServiceHelper.getRequest(reqObj);
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                return WebServiceHelper.convertResponse(response, TrueCallerVerifiedProfile.class);
            }
        } catch (Exception e) {
            LOG.error(" ::::::: No True caller profile found for request id {} ::::::", requestId);
        } finally {
            reqObj.releaseConnection();
            reqObj.abort();
        }
        return null;
    }

    @Override
    public String sendTrueCallerRequest(TrueCallerPostRequest request) throws IOException {
        String requestUrl = props.getTrueCallerServiceUrl() + "/verify";
        HttpPost reqObj = new HttpPost(requestUrl);
        reqObj.setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON.toString());
        String body = JSONSerializer.toJSON(request);
        org.apache.http.HttpEntity httpEntity = new StringEntity(body, AppConstants.CHARSET);
        reqObj.setEntity(httpEntity);
        try {
            org.apache.http.HttpResponse response = WebServiceHelper.postRequest(reqObj, 25, 25);
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                String requestId = EntityUtils.toString(response.getEntity());
                EntityUtils.consume(response.getEntity());
                return requestId;
            }
        } catch (Exception e) {
            LOG.error("Error while saving true caller request", e);

            StringBuilder message = new StringBuilder(":::: TRUE CALLER VERIFICATION FAILURE NOTIFICATION :::: \n");
            message.append("CONTACT NUMBER :: ").append(request.getContact()).append("\n");
            message.append("ERROR TRACE ::").append(e.getMessage()).append("\n");
            message.append(":::::::::::::::::::::::::::::::::\n");
            SlackNotificationService.getInstance().sendNotification(props.getEnvironmentType(), "KETTLE_CRM",
                    SlackNotification.SYSTEM_ERRORS, message.toString());
            throw e;
        } finally {
            reqObj.releaseConnection();
            reqObj.abort();
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Customer updateTrueCallerInfo(TrueCallerVerifiedProfile trueCallerVerifiedProfile) {
        Customer customer = getCustomer(trueCallerVerifiedProfile.getContact());
        if (customer != null && !AppConstants.EXCLUDE_CUSTOMER_IDS.contains(customer.getId())) {
            try {
                Integer customerId = customer.getId();
                CustomerInfo oldCustomerObj = dao.find(CustomerInfo.class, customerId);
                String oldEmailId = oldCustomerObj.getEmailId();
                String oldName = oldCustomerObj.getFirstName();
                String oldContact = oldCustomerObj.getContactNumber();

                boolean sendMail = false;
                // update for all customers except for EMP35 employees
                if ((oldEmailId == null || !oldEmailId.contains("chaayos.com"))
                        && trueCallerVerifiedProfile.getEmail() != null) {
                    // update verification status for true caller verified users
                    if ((oldCustomerObj.getIsEmailVerified() == null || oldCustomerObj.getIsEmailVerified().equalsIgnoreCase(AppConstants.NO))) {
                        oldCustomerObj.setEmailId(trueCallerVerifiedProfile.getEmail());
                        sendMail = true;
                    }
                }
                oldCustomerObj.setFirstName(trueCallerVerifiedProfile.getName());
                oldCustomerObj.setContactNumber(trueCallerVerifiedProfile.getContact());
                // update verification status for true caller verified users
                String isVerifiedOldValue = oldCustomerObj.getIsNumberVerified();
                if (isVerifiedOldValue == null || isVerifiedOldValue.equalsIgnoreCase(AppConstants.NO)) {
                    oldCustomerObj.setNumberVerificationTime(AppUtils.getCurrentTimestamp());
                    oldCustomerObj.setIsNumberVerified(AppConstants.YES);
                }
                oldCustomerObj.setTrueCallerProfileId(trueCallerVerifiedProfile.getTcId());
                oldCustomerObj = (CustomerInfo) dao.update(oldCustomerObj);
                createChangeLog(oldEmailId, oldContact, oldName, customerId, isVerifiedOldValue,
                        CustomerUpdatedBy.TRUECALLER);
                LoyaltyScore score = null;
                if (oldCustomerObj != null) {
                    score = dao.getLoyaltyScore(customerId);
                }

                if (sendMail) {
                    // send verification email after new email is updated
                    sendVerificationEmail(trueCallerVerifiedProfile.getEmail(), oldEmailId,
                            trueCallerVerifiedProfile.getContact(), trueCallerVerifiedProfile.getName(),oldCustomerObj.getAcquisitionBrandId());
                }
                return DataConverter.convert(oldCustomerObj, score, dao.getAvailableCash(customerId));
            } catch (Exception e) {
                LOG.error("Error while updating customer with number " + customer.getContactNumber(), e);
            }
        } else {
            customer = new Customer();
            customer.setTrueCallerProfile(trueCallerVerifiedProfile);
            customer.setFirstName(trueCallerVerifiedProfile.getName());
            customer.setEmailId(trueCallerVerifiedProfile.getEmail());
            customer.setContactNumber(trueCallerVerifiedProfile.getContact());
            customer.setContactNumberVerified(true);
        }
        return customer;
    }

    private void createChangeLog(String oldEmailId, String oldContact, String oldName, Integer customerId,
                                 String isVerified, CustomerUpdatedBy updatedBy) {
        CustomerDetailChangelog changelog = new CustomerDetailChangelog();
        changelog.setChangedBy(updatedBy.name());
        changelog
                .setCustomerType(AppConstants.getValue(isVerified) ? CustomerType.OLD.name() : CustomerType.NEW.name());
        changelog.setCustomerId(customerId);
        changelog.setOldContact(oldContact);
        changelog.setOldName(oldName);
        changelog.setOldEmail(oldEmailId);
        changelog.setUpdatedAt(AppUtils.getCurrentTimestamp());
        dao.add(changelog);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void markAsInternalCustomers(List<String> contactNumber) {
        dao.markAsInternalCustomers(contactNumber);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean existsContactNumber(String number) {
        return dao.existsContactNumber(number);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public BigDecimal getAvailableCash(Integer customerId) {
        return dao.getAvailableCash(customerId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public BigDecimal getAvailableCashback(Integer customerId) {
        return dao.getAvailableCashback(customerId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Customer getCustomerByRefCode(String refCode) {
        return dao.getCustomerByRefCode(refCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void gererateRefCodes() {
        boolean hasCustomers = true;
        int i = 0;
        while (hasCustomers) {
            hasCustomers = gererateRefCodeForCustomers();
            i = i + 1;
            if (i > 50) {
                break;
            }
        }
    }

    private boolean gererateRefCodeForCustomers() {
        List<Integer> list = dao.getCustomersWithoutRefCode();
        if (list != null && !list.isEmpty()) {
            LOG.debug("Generating refcodes for {} customers", list.size());
            for (Integer customerId : list) {
                CustomerInfo c = dao.find(CustomerInfo.class, customerId);
                c.setRefCode(dao.getUniqueRefCode(c.getFirstName(), c.getContactNumber()));
            }
            return true;
        } else {
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public CustomerInfo getCustomerInfoObject(String contact) {
        return dao.getCustomerInfo(contact);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<CustomerInfo> getCustomerWithReferralCode(List<String> contacts) {
        return dao.getCustomerWithReferralCode(contacts);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Customer getCustomerByFaceId(String faceId) {
        return dao.getCustomerByFaceId(faceId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean mapCustomerByFaceId(Pair<String, String> data) {
        return dao.mapCustomerByFaceId(data.getKey(), data.getValue());
    }


    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Customer createCustomerAndAwardLoyalty(String contactNumber, String name, Integer orderId,
                                                  String acquisitionSource, String acquisitionToken) throws DataUpdationException, DataNotFoundException {
        CustomerInfo info = getCustomerInfoObject(contactNumber);
        boolean signuOffer = false;
        int customerId = -1;
        if (info == null) {
            Customer customer = new Customer();
            customer.setAcquisitionSource(acquisitionSource);
            customer.setAcquisitionToken(acquisitionToken);
            customer.setAddTime(AppUtils.getCurrentTimestamp());
            customer.setContactNumber(contactNumber);
            customer.setFirstName(name);
            customer.setCountryCode(AppConstants.DEFAULT_COUNTRY_CODE);
            customer = addCustomer(customer);
            signuOffer = true;
            customerId = customer.getId();
        } else {
            customerId = info.getCustomerId();
        }
        if (!loyaltyDao.isLoyaltyAwarded(customerId, orderId)) {
            loyaltyDao.updateScore(customerId, LoyaltyEventType.OUTLET_VISIT, AppConstants.CAFE_VISIT_LOYALTY_POINT,
                    orderId, signuOffer, true);
            loyaltyDao.updateCustomerId(customerId, orderId);
        }
        return getCustomer(customerId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public String optOutOfFaceIt(String contactNumber, boolean flag) {
        return dao.optOutOfFaceIt(contactNumber,flag);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public String optOutOfFaceIt(int customerId) {
        return dao.optOutOfFaceIt(customerId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public OrderDetail getOrderDetail(int orderId) {
        return dao.getOrderDetail(orderId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Integer> getCustomerIds(int customerId, int batchSize) {
        return dao.getCustomerIds(customerId, batchSize);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<Integer> getCustomerIdsWithPendingSignupOffer(int noOfDays) {
        return dao.getCustomerIdsWithPendingSignupOffer(noOfDays);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void expireSignupOffer(List<Integer> customerIds) {
        dao.expireSignupOffer(customerIds);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void expireSignupOffer(int noOfDays) {
        dao.expireSignupOffer(noOfDays);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean isBlackListed(String contactNumber) {
        Customer customer = getCustomer(contactNumber);
        if (customer == null) {
            return false;
        }
        return customer.isBlacklisted();
    }

    @Override
    public List<LoyaltyScore> getCustomersLoyaltyScore(List<Integer> customerIds) {
        LOG.debug("Enter getCustomersLoyaltyScore customerIds: " + JSONSerializer.toJSON(customerIds));
        List<LoyaltyScore> loyaltyScores = dao.getCustomersLoyaltyScore(customerIds);
        LOG.debug("Loyalty scores fetched:::: " + JSONSerializer.toJSON(loyaltyScores));
        return loyaltyScores;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public CustomerAdditionalDetail saveCustomerAdditionalDetail(IdCodeName request) {
        CustomerAdditionalDetail customerAdditionalDetail = new CustomerAdditionalDetail();
        customerAdditionalDetail.setCustomerId(request.getId());
        customerAdditionalDetail.setCampaignName(request.getName());
        customerAdditionalDetail.setValue(request.getCode());
        customerAdditionalDetail.setBusinessDate(AppUtils.getBusinessDate());
        return dao.add(customerAdditionalDetail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Long checkCustomerAdditionalDetail(Integer customerId, String type) {
        return dao.checkCustomerAdditionalDetail(customerId, type);
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean hasOrdersForOrderSource(String contactNumber, String orderSource) {
        return dao.hasOrdersForOrderSource(contactNumber, orderSource);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean sendDownloadAppLinkToCustomer(String contactNumber, Customer customerInfo, String mode) {
        try {
            String message = CustomerSMSNotificationType.valueOf(mode).getMessage(customerInfo);
            boolean status = notificationService
                    .sendNotification(CustomerSMSNotificationType.valueOf(mode).name(), message,
                            contactNumber,
                            providerService.getSMSClient(CustomerSMSNotificationType.valueOf(mode)
                                    .getTemplate().getSMSType(), ApplicationName.KETTLE_SERVICE),
                            props.sendAppDownloadLinkToCustomer(),null);
            return status;
        } catch (IOException | JMSException e) {
            LOG.error(
                    "Error while sending app download link to customer " + contactNumber,
                    e);
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public CustomerAdditionalDetail saveAppDownloadLinkData(int customerId, String campaignName, Boolean status) {
        CustomerAdditionalDetail customerAdditionalDetail = new CustomerAdditionalDetail(customerId, campaignName, AppConstants.getValue(status), AppUtils.getBusinessDate());
        customerAdditionalDetail = dao.add(customerAdditionalDetail);
        return customerAdditionalDetail;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateCrmScreenUrl(CrmAppScreenDetail detail) {
        return dao.updateCrmScreenUrl(detail);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateCrmScreenStatus(List<CrmAppScreenDetail> details) {
        return dao.updateCrmScreenStatus(details);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public CustomerVisitInfo customerVisit(Integer customerId, Integer unitId) {
        CustomerVisitInfo customerVisitInfo = new CustomerVisitInfo();
        List<OrderDetail> result = dao.customerVisit(customerId);
        LOG.debug("updating the last order for the customer");
        if (!result.isEmpty()) {
            for (OrderDetail orderDetail : result) {
                List<com.stpl.tech.kettle.data.model.OrderItem> orderItems = orderDetail.getOrderItems();
                Set<Integer> productId = new HashSet<>();
                List<CustomerLastOrderInfo> customerLastOrderInfo = new ArrayList<>();
                for (com.stpl.tech.kettle.data.model.OrderItem item : orderItems) {
                    if (!AppConstants.HIDDEN_PRODUCTS_SET.contains(item.getProductId())) { //check //appCont
                        CustomerLastOrderInfo lastInfoDetail = new CustomerLastOrderInfo();
                        lastInfoDetail.setDimension(item.getDimension());
                        lastInfoDetail.setProductID(item.getProductId());
                        lastInfoDetail.setProductName(item.getProductName());
                        lastInfoDetail.setQuantity(item.getQuantity());
                        Map<Integer, List<List<String>>> addonsMap = new HashMap<>();
                        List<OrderItemAddon> orderItemAddons = item.getOrderItemAddons();
                        List<List<String>> listOfString = new ArrayList<>();
                        for (OrderItemAddon addon : orderItemAddons) {
                            List<String> str = new ArrayList<>();
                            str.add(addon.getDimension());
                            str.add(addon.getName());
                            str.add(addon.getType());
                            listOfString.add(str);
                        }
                        addonsMap.put(item.getProductId(), listOfString);
                        lastInfoDetail.setProductAddons(addonsMap);
                        customerLastOrderInfo.add(lastInfoDetail);
                        if (AppConstants.DESI_CHAI_ID.contains(item.getProductId())) {
                            productId.add(AppConstants.DESI_CHAI_ID.get(0));
                        } else if (AppConstants.BAARISH_CHAI_ID.contains(item.getProductId())) {
                            productId.add(AppConstants.BAARISH_CHAI_ID.get(0));
                        } else {
                            productId.add(item.getProductId());
                        }
                    }
                }
                if (!customerLastOrderInfo.isEmpty()) {
                    customerVisitInfo.setOrderId(orderDetail.getOrderId());
                    customerVisitInfo.setInfoList(customerLastOrderInfo);
                    customerVisitInfo.setProductId(productId);
                    customerVisitInfo.setCustomerId(customerId);
                    customerVisitInfo.setUnitId(orderDetail.getUnitId());
                    customerVisitInfo.setOrderSource(orderDetail.getOrderSource());
                    customerVisitInfo.setChannelPartner(orderDetail.getChannelPartnerId());
                    Unit currentUnit = masterDataCache.getUnit(unitId);
                    Unit previousUnit = masterDataCache.getUnit(orderDetail.getUnitId());
                    LOG.debug("Current region  customer visiting  " + currentUnit.getRegion());
                    LOG.debug("Previous  region customer visited  " + previousUnit.getRegion());
                    customerVisitInfo.setPreviousRegion(previousUnit.getRegion());
                    customerVisitInfo.setCurrentRegion(currentUnit.getRegion());
                    customerVisitInfo.setUnitName(previousUnit.getName());
                    if (currentUnit.getRegion().equals(previousUnit.getRegion())) {
                        customerVisitInfo.setRegularCustomer(true);
                    } else {
                        customerVisitInfo.setOutStationCustomer(true);
                    }
                    //feedbackDetail(orderDetail.getOrderId(), customerId);
                    OrderDetail firstZomatoOrderDetail = dao.getFirstZomatoOrder(customerId);
                    if(firstZomatoOrderDetail != null) {
                        if(firstZomatoOrderDetail.getOrderId() == customerVisitInfo.getOrderId()) {
                            customerVisitInfo.setFirstZomatoOrder(true);
                        } else {
                            customerVisitInfo.setFirstZomatoOrder(false);
                        }
                    } else {
                        customerVisitInfo.setFirstZomatoOrder(false);
                    }
                    return customerVisitInfo;
                }
            }
        } else {
            customerVisitInfo.setFirstTimeCustomer(true);
            customerVisitInfo.setFirstZomatoOrder(false);
        }
        return customerVisitInfo;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateCustomerAppId(String appId, int customerId) {
        CustomerInfo info = dao.find(CustomerInfo.class, customerId);
        if (info != null && info.getCustomerAppId() == null) {
            info.setCustomerAppId(appId);
            if(Objects.isNull(info.getOptWhatsapp())){
                info.setOptWhatsapp(AppConstants.YES);
            }
            dao.update(info);
            return true;
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public CustomerVisitInfo feedbackDetail(CustomerVisitInfo customerVisitInfo, Integer customerId) {
        List<FeedbackDetail> result = dao.feedbackDetails(customerVisitInfo.getOrderId(), customerVisitInfo.getCustomerId());
        LOG.debug("size is " + result.size());
        if (result.isEmpty()) {
            // dont show anything;
            customerVisitInfo.setLastFeedback(false);
        } else {
            customerVisitInfo.setLastFeedback(true);
            for (FeedbackDetail feedback : result) {
                List<FeedbackEvent> event = feedback.getFeedbackEvents();
                FeedbackEvent data=event.get(event.size()-1);
               // for (FeedbackEvent data : event) {
                    switch (data.getEventStatus()) {
                        case AppConstants.STATUS_CREATED:
                            //status is created
                            customerVisitInfo.setLastFeedback(false);
                            break;
                        case AppConstants.STATUS_NOTIFIED:
                            // not filled
                            String s = data.getEventSource() + ",  NPS  Not filled ";
                            LOG.debug("notification " + s);
                            customerVisitInfo.setLastFeedbackEvent(false);
                            customerVisitInfo.setLastFeedbackDetail(s);
                            break;
                        case AppConstants.STATUS_SUCCESSFUL:
                            //String.valueOf( d.getRating());
                            String str = data.getEventSource() +", "+ data.getRating();
                            LOG.debug("notification " + str);
                            customerVisitInfo.setLastFeedbackEvent(true);
                            customerVisitInfo.setLastFeedbackDetail(str);
                            return customerVisitInfo;
                        default:
                            // doubt -> case here is CANCELLED | failed
                            customerVisitInfo.setLastFeedback(false);
                            break;
                    }
               // }
            }
        }
//        customerVisitInfo.setLastFeedback(true);
//        //customerVisitInfo.setLastFeedbackDetail("9");
//        customerVisitInfo.setLastFeedbackDetail("POS ,NPS  Not filled ");
        Object[] savingData = selectPotentialSavingService.getPotentialSavingData(customerId, AppConstants.CHAAYOS_BRAND_ID);
        if(Objects.nonNull(savingData)){
            customerVisitInfo.setPotentialSavingData(PotentialSavingData.builder()
                    .totalOrderCount((Integer) savingData[0])
                    .currentOrderCount((Integer) savingData[1])
                    .totalNetSales(BigDecimal.valueOf((Integer) savingData[2]))
                    .currentNetSales(BigDecimal.valueOf((Integer) savingData[3]))
                    .totalGmvSales(BigDecimal.valueOf((Integer) savingData[4]))
                    .currentGmvSale(BigDecimal.valueOf((Integer) savingData[5])).build());
        }else{
            customerVisitInfo.setPotentialSavingData(PotentialSavingData.builder()
                    .totalOrderCount(0).currentOrderCount(0).totalNetSales(BigDecimal.ZERO)
                    .currentNetSales(BigDecimal.ZERO).totalGmvSales(BigDecimal.ZERO).currentGmvSale(BigDecimal.ZERO).build());
        }
        return customerVisitInfo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void removeIncorrectEmailInfo(int customerId) {
     CustomerInfo customerInfo= dao.find(CustomerInfo.class,customerId);
     if(customerInfo!=null && !AppUtils.isValidEmail(customerInfo.getEmailId())){
         LOG.info("Setting Email Id null for Customer::{}",customerId);
         customerInfo.setEmailId(null);
         customerInfo.setIsEmailVerified(AppConstants.NO);
         customerInfo.setEmailVerificationTime(null);
         customerInfo.setEmailSubscriber(AppConstants.NO);
         dao.update(customerInfo);
     }

    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<Integer> removeAllFaceIdsWithGivenFaceId(String faceId) {
        return dao.removeAllFaceIdsWithGivenFaceId(faceId);
    }

	@Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public CustomerOneViewData getCustomerOneViewData(int customerId, Integer brandId, List<Integer> excludeOrderIds) {
		return dao.getCustomerOneViewData(customerId,brandId, excludeOrderIds);
	}

	@Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public CustomerDineInView getCustomerDineInView(int customerId, Integer brandId, List<Integer> excludeOrderIds) {
		return dao.getCustomerDineInView(customerId,brandId, excludeOrderIds);
	}

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public CustomerTransactionViewData getCustomerTransactionViewData(int customerId, Integer brandId, List<Integer> excludeOrderIds) {
        return dao.getCustomerTransactionViewData(customerId, brandId, excludeOrderIds);
    }


	@Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public CustomerEmailData getCustomerEmailData(int customerId, Integer brandId) {
		return dao.getCustomerEmailData(customerId,brandId);
	}

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<String,List<CustomerCampaignOfferDetail>> getNextBestOfferDetails(Integer brandId){
        Map<String,List<CustomerCampaignOfferDetail>> map = new HashMap<>();
        map.put("CLM_REPEAT_1",dao.getNextBestOfferDetail("CLM_REPEAT_1", brandId));
        map.put("CLM_DORMANT_1",dao.getNextBestOfferDetail("CLM_DORMANT_1", brandId));
        map.put("CLM_DORMANT_2",dao.getNextBestOfferDetail("CLM_DORMANT_2", brandId));
        map.put("CLM_DORMANT_REM",dao.getNextBestOfferDetail("CLM_DORMANT_REM", brandId));
        return map;
    }

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<CustomerCampaignOfferDetail> getAllNextBestOfferDetails(Integer brandId, String cloneCouponCode,
			Date couponStartDate) {
		return dao.getAllNextBestOfferDetails(brandId, cloneCouponCode, couponStartDate);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<CustomerCampaignOfferDetail> getUsedNextBestOfferDetails(Integer brandId, String cloneCouponCode,
			Date couponStartDate) {
		return dao.getUsedNextBestOfferDetails(brandId, cloneCouponCode, couponStartDate);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Map<String, List<CustomerCampaignOfferDetail>> getUsedNextBestOfferDetails(Integer brandId,
			Date couponStartDate) {
		return dao.getUsedNextBestOfferDetails(brandId, couponStartDate);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public List<CustomerCampaignOfferDetail> getNotUsedNextBestOfferDetails(Integer brandId, String cloneCouponCode,
			Date couponStartDate) {
		return dao.getNotUsedNextBestOfferDetails(brandId, cloneCouponCode, couponStartDate);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Map<String, List<CustomerCampaignOfferDetail>> getNotUsedNextBestOfferDetails(Integer brandId,
			Date couponStartDate) {
		return dao.getNotUsedNextBestOfferDetails(brandId, couponStartDate);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Map<String, Pair<Boolean, Boolean>> getNotificationFlags(List<String> customerContact) {
		return dao.getNotificationFlags(customerContact);
	}

    @Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateWhatsappOptInOut(CustomerResponse customerResponse) {
		return dao.updateWhatsappOptInOut(customerResponse);
	}

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Integer checkCouponUsage(int customerId, String code) {
		return dao.checkCouponUsage(customerId, code);
	}

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public boolean getValidOfferFlag(OfferOrder offerOrder, CouponDetail couponDetail) {
        SubscriptionPlan subscriptionPlan = subscriptionPlanDao.getActiveSubscription(offerOrder.getOrder().getCustomerId(), couponDetail.getCode());
        if(Objects.nonNull(subscriptionPlan)) {
            return doOfferValidation(dao.getOrderDetailViaOffer(offerOrder.getOrder().getCustomerId(), offerOrder.getCouponCode(),
                            subscriptionPlan.getPlanStartDate(), Objects.nonNull(couponDetail.getOffer().getApplicableHour()) ? couponDetail.getOffer().getApplicableHour() : 0),
                    couponDetail);
        } else {
            return doOfferValidation(dao.getOrderDetailViaOffer(offerOrder.getOrder().getCustomerId(), offerOrder.getCouponCode(),
                            couponDetail.getStartDate(),Objects.nonNull(couponDetail.getOffer().getApplicableHour()) ? couponDetail.getOffer().getApplicableHour() : 0),
                    couponDetail);
        }
	}

    @Override
    public boolean doOfferValidation(OfferLastRedemptionView offerLastRedemptionView, CouponDetail couponDetail) {
        if(Objects.isNull(offerLastRedemptionView) || Objects.isNull(offerLastRedemptionView.getLastOrderTime())){
            return false;
        } else if (Objects.nonNull(couponDetail.getOffer().getDailyFrequencyCount()) && couponDetail.getOffer().getDailyFrequencyCount()>0){
            if (offerLastRedemptionView.getOrderToday() < couponDetail.getOffer().getDailyFrequencyCount() && offerLastRedemptionView.getOrderInLastHour() == 0) {
                return false;
            } else if (offerLastRedemptionView.getOrderToday() >= couponDetail.getOffer().getDailyFrequencyCount() || offerLastRedemptionView.getOrderInLastHour() > 0) {
                return true;
            }
        } else if (Objects.isNull(couponDetail.getOffer().getDailyFrequencyCount()) || couponDetail.getOffer().getDailyFrequencyCount() == 0){
            if (offerLastRedemptionView.getOrderToday() == 0) {
                return false;
            }else{
                return true;
            }
        }
        return false;
    }

    @Override
    public CustomerContactInfoMapping getCustomerContactInfoMapping(Integer customerId) {
        return dao.getCustomerContactInfoMapping(customerId);
    }

    @Override
    public List<CampaignOfferDetail> getNotUsedNextOfferDetails(Integer brandId) {
        return dao.getNotUsedNextOfferDetails(brandId);
    }

    @Override
    public List<CustomerCampaignOfferDetail> getPendingNextJourneyEligibleOffer(Integer brandId, Date nextOfferDate) {
        return dao.getPendingNextJourneyEligibleOffer(brandId, nextOfferDate);
    }

    public BigDecimal getWalletBalance(Integer customerId) throws CardValidationException {
        List<CashCardDetail> cashCardDetailList=cardService.getCardDetails(customerId);
        BigDecimal walletBalance=BigDecimal.ZERO;
        for(CashCardDetail cashCardDetail: cashCardDetailList){
            if(cashCardDetail.getCashPendingAmount().compareTo(BigDecimal.ZERO)==1){
                walletBalance= walletBalance.add(cashCardDetail.getCashPendingAmount());
            }
        }
        return walletBalance;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<CustomerBasicInfo> getEmpDiscount() {
        try {
            List<CustomerInfo> isEmpApplicable = dao.getEmpDiscount();
            List<CustomerBasicInfo> applicableEmp = new ArrayList<>();
            for(CustomerInfo info : isEmpApplicable){
                applicableEmp.add(DataConverter.convert(info));
            }
            if(Objects.nonNull(applicableEmp)){
                return applicableEmp;
            }
            return new ArrayList<>();
        }catch (Exception e){
            LOG.error("Error found in employees who are applicable for employee discounts");
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean deactivateEmpDiscount(List<Integer> customerIds){
        try {
            Integer deactivateEmpBenefits = dao.deactivateEmpDiscount(customerIds);
            if(Objects.nonNull(deactivateEmpBenefits)){
                return true;
            }
            return false;
        }
        catch (Exception e){
            LOG.error("Error in deactivating employee discount");
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void deactivateOldSubscriptionPlans() {
        dao.deactivateOldSubscriptionPlans();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void deactivateOldSubscriptionPlanEvents() {
        dao.deactivateOldSubscriptionPlanEvents();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Map<String,List<String>> uploadCustomerSheet(MultipartFile file, String acquisitionSource) throws Exception {
        Map<String,List<String>> finalObject = new HashMap<>();
        List<Integer> customerIds = new ArrayList<>();
        List<ExcelParsingException> errors = new ArrayList<>();
        Workbook workbook;
        if (file.getOriginalFilename().endsWith("xls")) {
            workbook = new HSSFWorkbook(file.getInputStream());
        } else {
            workbook = new XSSFWorkbook(file.getInputStream());
        }
        SheetParser parser = new SheetParser();
        List<CustomerLeadData> customerLeadData = parser.createEntity(workbook.getSheetAt(0),
                CustomerLeadData.class, errors::add);
        if (CollectionUtils.isNotEmpty(customerLeadData) && CollectionUtils.isEmpty(errors)) {
            List<String> validCustomerContact = new ArrayList<>();
            List<String> customerContacts = new ArrayList<>();
            List<CustomerInfo> customerList = new ArrayList<>();
            List<String> customersToOptIn = new ArrayList<>();
            for (CustomerLeadData customerData : customerLeadData) {
                if (Objects.nonNull(customerData) && Objects.nonNull(customerData.getCustomerContact())) {
                    Pattern pattern = Pattern.compile("(0/91)?[6-9][0-9]{9}");
                    Matcher match = pattern.matcher(customerData.getCustomerContact());
                    Boolean isValidNumber = match.find() && match.group().equals(customerData.getCustomerContact());
                    if (isValidNumber) {
                        validCustomerContact.add(customerData.getCustomerContact());
                    }
                }
            }
            List<Object[]> customerAvailable = customerInfoDao.getCustomerInfoByContactList(validCustomerContact);
            for (Object[] o : customerAvailable) {
                if (Objects.isNull(o[1]) || AppConstants.NO.equalsIgnoreCase((String) o[1])) {
                    customersToOptIn.add((String) o[0]);
                }
                customerContacts.add((String) o[0]);
            }
            List<String> customerToAdd = getCustomerToAdd(validCustomerContact, customerContacts);
            if(customersToOptIn.size() !=0) {
                customerInfoDao.updateCustomerOptIn(customersToOptIn);
            }
            finalObject.put("ValidCustomers",validCustomerContact);
            finalObject.put("CustomersUpdated",customersToOptIn);
            customerIds = addCustomerDetails(customerLeadData, customerToAdd, customerList, acquisitionSource, customersToOptIn);
            workbook.close();
        } else {
            LOG.info("Error Parsing Workbook for Manpower Expenses, total errors :{}", errors.size());
            StringBuilder sb = new StringBuilder();
            errors.forEach(e -> sb.append(e.getMessage() + '\n'));
            LOG.info("{}", sb.toString());
            throw new ExcelParsingException(sb.toString());
        }
        List<String> custIds = new ArrayList<>();
        for(Integer ids : customerIds){
            custIds.add(String.valueOf(ids));
        }
        finalObject.put("NewCustomerAdded",custIds);
        return finalObject;
    }

    public void makeCustomerObject(CustomerLeadData customerLeadData, List<CustomerInfo> customerInfoList, String acquisitionSource) {
        CustomerInfo customerInfo = new CustomerInfo();
        String name;
        if (Objects.nonNull(customerLeadData.getCustomerName())) {
            name = customerLeadData.getCustomerName().replaceAll("[^a-zA-Z]+", "");
        } else {
            name = null;
        }
        customerInfo.setFirstName(name);
        if (Objects.nonNull(customerLeadData.getEmail())) {
            if (AppUtils.isValidEmail(customerLeadData.getEmail())) {
                customerInfo.setEmailId(customerLeadData.getEmail());
            }
        }
        if (Objects.nonNull(customerLeadData.getAddTime())) {
            customerInfo.setAddTime(customerLeadData.getAddTime());
        }
        customerInfo.setAcquisitionSource(acquisitionSource);
        if (Objects.nonNull(customerLeadData.getCustomerContact())) {
            customerInfo.setContactNumber(customerLeadData.getCustomerContact());
        }
        customerInfo.setCountryCode(AppConstants.DEFAULT_COUNTRY_CODE);
        customerInfo.setAcquisitionToken(customerLeadData.getAcquisitionToken());
        customerInfo.setIsBlacklisted(AppConstants.NO);
        customerInfo.setAcquisitionBrandId(1);
        customerInfo.setSmsSubscriber(AppConstants.YES);
        customerInfo.setIsChaayosCustomer(AppConstants.YES);
        customerInfo.setOptWhatsapp(AppConstants.YES);
        customerInfoList.add(customerInfo);
    }

    public List<String> getCustomerToAdd(List<String> validCustomerContact, List<String> customerAvailable) {
        List<String> customerToAdd = new ArrayList<>();
        if(Objects.nonNull(validCustomerContact)) {
            for (String contact : validCustomerContact) {
                if (CollectionUtils.isNotEmpty(customerAvailable)) {
                    if (!customerAvailable.contains(contact)) {
                        customerToAdd.add(contact);
                    }
                } else {
                    customerToAdd.add(contact);
                }
            }
        }
        return customerToAdd;
    }

    public List<Integer> addCustomerDetails(List<CustomerLeadData> customerLeadData, List<String> customerToAdd,
                                            List<CustomerInfo> customerList, String acquisitionSource, List<String> customersToOptIn) throws DataUpdationException {
        List<Integer> customerIds = new ArrayList<>();
        if (Objects.nonNull(customersToOptIn)) {
            customerToAdd.addAll(customersToOptIn);
        }
        for (CustomerLeadData data : customerLeadData) {
            if (Objects.nonNull(data)) {
                if (customerToAdd.contains(data.getCustomerContact())) {
                    makeCustomerObject(data, customerList, acquisitionSource);
                }
            }
        }
        List<CustomerInfo> customerInfos = new ArrayList<>();
        for(CustomerInfo info : customerList){
            if(!customersToOptIn.contains(info.getContactNumber())){
                customerInfos.add(info);
            }
        }
        List<CustomerInfo> customersAdded = customerInfoDao.addAll(customerInfos);
        if(Objects.nonNull(customersAdded)) {
            if (customersAdded.size() != 0) {
                List<LoyaltyScore> loyaltyScores = makeLoyaltyScoreObject(customersAdded, customerIds);
                List<LoyaltyScore> loyaltyScoreAdded = loyaltyDao.addAll(loyaltyScores);
                if (Objects.nonNull(customersAdded) && Objects.nonNull(loyaltyScoreAdded)) {
                    ThreadPoolTaskExecutor executor = (ThreadPoolTaskExecutor) applicationContext
                            .getBean("taskExecutor");
                    executor.execute(() -> {
                        LOG.info("Starting bulk push to whatsApp sqs for uploaded customer lead in separate thread");
                        try {
                            for (CustomerInfo info : customerList) {
                                customerCommunicationEventPublisher.publishCustomerCommunicationEvent(environment.getEnvironmentType().name(), getNotificationPayload(info));
                            }
                        } catch (JMSException e) {
                            LOG.info("Unable to publish customer to queue for gupshup opt in");
                        }
                    });
                }
            }
        }
        return customerIds;
    }

    private NotificationPayload getNotificationPayload(CustomerInfo customerResponse) {
        NotificationPayload payload = new NotificationPayload();
        payload.setCustomerId(customerResponse.getCustomerId());
        payload.setOrderId(0);
        payload.setMessageType(AppConstants.WA_OPT_IN);
        payload.setContactNumber(customerResponse.getContactNumber());
        payload.setRequestTime(AppUtils.getCurrentTimestamp());
        payload.setSendWhatsapp(true);
        payload.setWhatsappOptIn(true);
        Map<String, String> map = new HashMap<>();
        map.put("firstName", customerResponse.getFirstName());
        payload.setPayload(map);
        return payload;
    }

    List<LoyaltyScore> makeLoyaltyScoreObject(List<CustomerInfo> customersAdded, List<Integer> customerIds) {
        List<LoyaltyScore> loyaltyScoreList = new ArrayList<>();
        for (CustomerInfo info : customersAdded) {
            LoyaltyScore score = new LoyaltyScore(info.getCustomerId(), 0);
            if (AppConstants.YES.equals(info.getIsRenewed())) {
                score.setAvailedSignupOffer(AppConstants.YES);
                score.setSignupOfferStatus(SignupOfferStatus.RENEW_CUSTOMER_FORCE_EXPIRED.name());
                score.setSignupOfferExpired(AppConstants.YES);
                score.setSignupOfferExpiryTime(AppUtils.getCurrentTimestamp());
            }
            loyaltyScoreList.add(score);
            customerIds.add(info.getCustomerId());
        }
        return loyaltyScoreList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean getCustomerOrders(int customerId){
        return customerInfoDao.getCustomerOrderDetails(customerId);
    }

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public CustomerInfo subscribeCustomer(int customerId, String channel) throws DataNotFoundException {
		CustomerInfo customer = dao.find(CustomerInfo.class, customerId);
		if (channel.equalsIgnoreCase(AppConstants.WHATSAPP)) {
			customer.setOptWhatsapp(AppConstants.YES);
            try {
				customerCommunicationEventPublisher.publishCustomerCommunicationEvent(environment.getEnvironmentType().name(), getNotificationPayload(customer));
			} catch (Exception e) {
				LOG.info("Exception during subscribing customer to whatsapp");
			}
		} else if (channel.equalsIgnoreCase(AppConstants.SMS)) {
			customer.setSmsSubscriber(AppConstants.YES);
		} else {
			LOG.info("Unknown channel to subscribe customer {} ", channel);
		}
		return (CustomerInfo) dao.update(customer);
	}

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public CustomerInfo unsubscribeCustomer(int customerId, String channel) throws DataNotFoundException {
		CustomerInfo customer = dao.find(CustomerInfo.class, customerId);
		if (channel.equalsIgnoreCase(AppConstants.WHATSAPP)) {
			customer.setOptWhatsapp(AppConstants.NO);

		} else if (channel.equalsIgnoreCase(AppConstants.SMS)) {
			customer.setSmsSubscriber(AppConstants.NO);
		} else {
			LOG.info("Unknown channel to unsubscribe customer {} ", channel);
		}
		return (CustomerInfo) dao.update(customer);
	}

    @Override
    @Transactional(rollbackFor = Exception.class, value = "ClmDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Pair<Integer, Integer> saveIncrementalCustomerSaveChaiData(Integer batchSize, Integer page) {
        List<CustomerFavChaiCustomizationDetail> customerFavChaiCustomizationDetail = customerFavChaiIncrementalDataDao.findByAddedToProdAndRecipeIdIsNotNull(AppConstants.NO,batchSize);
        int i = 0 ;
        int success=0;
        int failed=0;
        //while(i<customerFavChaiCustomizationDetail.size()){
//            List<CustomerFavChaiCustomizationDetail> customerFavChaiBatchedData = customerFavChaiCustomizationDetail;
            if(processBatchedData(customerFavChaiCustomizationDetail,batchSize)){
                success+=1;
            }else{
                failed+=1;
                LOG.info("unable to save Fav Chai for customerId {}",customerFavChaiCustomizationDetail.get(i));
            }
            i+=page;
       // }
        return new Pair<>(success,failed) ;
    }

    private boolean processBatchedData(List<CustomerFavChaiCustomizationDetail> customerFavChaiBatchedData, Integer batchSize) {
        List<SavedChai> savedChais = new ArrayList<>();
        HashMap<CustomerFavChaiMapping, List<FavChaiCustomizationDetail>> customerChaiCustomizationMap = new HashMap<>();
        List<CustomerFavChaiCustomizationDetail> successFullyConvertedChaiList = new ArrayList<>();
        ExecutorService taskExecutor = Executors.newFixedThreadPool(5);
        customerFavChaiBatchedData.forEach(customerFavChaiCustomizationDetail -> taskExecutor.execute(() -> {
            Product product = masterDataCache.getProduct(customerFavChaiCustomizationDetail.getProductId());
            ProductRecipeKey productRecipeKey = new ProductRecipeKey(
                        customerFavChaiCustomizationDetail.getProductId(),
                        customerFavChaiCustomizationDetail.getDimension(),
                        customerFavChaiCustomizationDetail.getRecipeProfile()
                );
            RecipeDetail recipe = recipeCache.getRecipe(productRecipeKey);

            if (Objects.nonNull(product) && Objects.nonNull(recipe)) {
                SavedChai savedChai = SaveChaiConverter.convertToSavedChai(
                        customerFavChaiCustomizationDetail,
                        product,
                        recipe,
                        customerChaiCustomizationMap,
                        successFullyConvertedChaiList
                );
                if(Objects.nonNull(savedChai) && !savedChai.getChaiCustomizationList().isEmpty()){
                    savedChais.add(savedChai);
                }
            }
        }));
        taskExecutor.shutdown();
        try {
            taskExecutor.awaitTermination(Long.MAX_VALUE, TimeUnit.NANOSECONDS);
        } catch (InterruptedException e) {
            LOG.error("--------------------INTERUPTION !!!!-------------Error in completion of save chai threads", e);
            return false;
        }
        return callToBulkSaveFavChai(savedChais, customerChaiCustomizationMap, successFullyConvertedChaiList, customerFavChaiBatchedData);
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    private boolean callToBulkSaveFavChai(List<SavedChai> savedChais, HashMap<CustomerFavChaiMapping, List<FavChaiCustomizationDetail>> customerChaiCustomizationMap, List<CustomerFavChaiCustomizationDetail> successFullyConvertedChaiList, List<CustomerFavChaiCustomizationDetail> customerFavChaiBatchedData) {
        List<Integer> successForCustomers = successFullyConvertedChaiList.stream().map(customerFavChaiCustomizationDetail -> customerFavChaiCustomizationDetail.getKey().getCustomerId()).collect(Collectors.toList());
        List<CustomerFavChaiCustomizationDetail> failedChaiCustomizationList = customerFavChaiBatchedData.stream().filter(customerFavChaiCustomizationDetail ->!successForCustomers.contains(customerFavChaiCustomizationDetail.getKey().getCustomerId())).collect(Collectors.toList());

    //        List<CustomerFavChaiMapping> customerFavChaiMappings = new ArrayList<>(customerChaiCustomizationMap.keySet());
        List<CustomerFavChaiMapping> customerFavChaiMappings = new ArrayList<>();
        for (CustomerFavChaiMapping mapping : customerChaiCustomizationMap.keySet()) {
            customerFavChaiMappings.add(mapping);
        }
        List<FavChaiCustomizationDetail> favChaiCustomizationDetails = new ArrayList<>();
        List<CustomerFavChaiMapping> savedCustomerFavChaiMappings;
        try{
            savedCustomerFavChaiMappings = new ArrayList<>(customerFavChaiMappingDao.saveAll(customerFavChaiMappings));
        }catch(Exception e ){
            LOG.error("Error while updating  customer fav chai mappings :::::::: ", e);
            return false;
        }

        LOG.info("Printing the list of customization ids for saved customer fav Chai ::::{}", savedCustomerFavChaiMappings.stream().map(CustomerFavChaiMapping::getCustomizationId).collect(Collectors.toList()));

        //Override  Customer Fav Chai Mapping
        for (CustomerFavChaiMapping customerFavChaiMapping : savedCustomerFavChaiMappings){
            if (!customerChaiCustomizationMap.isEmpty() && customerChaiCustomizationMap.containsKey(customerFavChaiMapping)){
                List<FavChaiCustomizationDetail> favChaiCustomizationDetailList = customerChaiCustomizationMap.get(customerFavChaiMapping);
                favChaiCustomizationDetailList.forEach(favChaiCustomizationDetail -> favChaiCustomizationDetail.setCustomerFavChaiMapping(customerFavChaiMapping));
                favChaiCustomizationDetails.addAll(favChaiCustomizationDetailList);
                customerChaiCustomizationMap.put(customerFavChaiMapping, favChaiCustomizationDetailList);
            }
        }

        try{
            favChaiCustomizationDetailDao.saveAll(favChaiCustomizationDetails);
        }catch(Exception e){
            LOG.error("Error while updating customer fav chai customizations :::::::: ", e);
            return false ;
        }

        // Updating Added To Prod Flag in table - Chai Customization Detail
        try{
            LOG.info("Call to update Added To Prod flag for Customers whose data is created Successfully :::::::::{} !  " , new Gson().toJson(successForCustomers));
            successFullyConvertedChaiList.forEach(customerFavChaiCustomizationDetail -> customerFavChaiCustomizationDetail.setAddedToProd(AppConstants.YES));
            customerFavChaiIncrementalDataDao.saveAll(successFullyConvertedChaiList);
        }catch(Exception e ){
            LOG.error("Error while updating fav chai customizations :::::::: ", e);
        }

        try{
            LOG.info("Call to update Added To Prod flag for Failed Cases :::::::::{}! ", new Gson().toJson(failedChaiCustomizationList.stream().map(failedChaiCustomization->failedChaiCustomization.getKey().getCustomerId()).collect(Collectors.toList())) );
            failedChaiCustomizationList.forEach(customerFavChaiCustomizationDetail -> customerFavChaiCustomizationDetail.setAddedToProd(AppConstants.FAIL));
            customerFavChaiIncrementalDataDao.saveAll(successFullyConvertedChaiList);
        }catch(Exception e ){
            LOG.error("Error while updating fav chai customizations :::::::: ", e);
        }

        return true ;
    }

    @Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
	public Integer getCustomerId(String contactNumber) {
		return dao.getCustomerId(contactNumber);
	}

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Integer addNewCustomer(String contactNumber,String customerName,String acquisitionSource,String acquisitionToken) throws DataUpdationException {
        try {
            Customer customer = new Customer();
            customer.setCountryCode(AppConstants.DEFAULT_COUNTRY_CODE);
            customer.setAddTime(AppUtils.getCurrentTimestamp());
            customer.setAcquisitionBrandId(AppConstants.CHAAYOS_BRAND_ID);
            customer.setChaayosCustomer(true);
            customer.setBlacklisted(false);
            customer.setAcquisitionSource(acquisitionSource);
            customer.setAcquisitionToken(acquisitionToken);
            customer.setOptWhatsapp(AppConstants.YES);
            customer.setSmsSubscriber(true);
            customer.setContactNumber(contactNumber);
            customer.setFirstName(customerName);
            customer = addCustomer(customer);
            return customer.getId();
        }catch (Exception e){
            LOG.info("Error in Creating Customer for contact number : {} and Error is : {}",contactNumber,e);
        }
        return null;
    }
    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<CustomerInfo> getCustomersWithDOBorAnniversary(){
        return customerInfoDao.getCustomersWithDOBorAnniversary();
    }

    @Override
    public String getCustomerContactBrandWise(String contactNumber, Integer brandId) {
        String contact = contactNumber;
        if (Objects.nonNull(brandId)) {
            Brand brandData = masterDataCache.getBrandMetaData().get(brandId);
            if (Objects.nonNull(brandData) && Objects.nonNull(brandData.getBrandContactCode()) && !contactNumber.contains(brandData.getBrandContactCode())) {
                contact = brandData.getBrandContactCode().concat(contactNumber);
            }
        }
        return contact;
    }

    @Override
    public String getBrandContactCode(Integer brandId){
        if (Objects.nonNull(brandId)) {
            Brand brandData = masterDataCache.getBrandMetaData().get(brandId);
            if (Objects.nonNull(brandData) && Objects.nonNull(brandData.getBrandContactCode())) {
                return brandData.getBrandContactCode();
            }
        }
        return "";
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public CustomerInfo addCustomerFromCampaign(Customer customer) throws DataUpdationException{
        return dao.addCustomerFromCampaign(customer);
    }

}
