package com.stpl.tech.kettle.data.model;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 11-09-2017.
 */
@Entity
@Table(name = "MONK_LOG")
public class MonkLogData {
    private Integer logId;
    private int orderId;
    private int orderItemId;
    private String monkName;
    private Date taskCreationTime;
    private String reassigned = "N";
    private String event;
    private int timeElapsedAtEvent;


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "MONK_LOG_ID", unique = true, nullable = false)
    public Integer getLogId() {
        return logId;
    }

    public void setLogId(Integer logId) {
        this.logId = logId;
    }

    @Column(name = "ORDER_ID", nullable = false)
    public int getOrderId() {
        return orderId;
    }

    public void setOrderId(int orderId) {
        this.orderId = orderId;
    }

    @Column(name = "ORDER_ITEM_ID", nullable = false)
    public int getOrderItemId() {
        return orderItemId;
    }

    public void setOrderItemId(int orderItemId) {
        this.orderItemId = orderItemId;
    }

    @Column(name = "TASK_CREATION_TIME", nullable = false)
    public Date getTaskCreationTime() {
        return taskCreationTime;
    }

    public void setTaskCreationTime(Date taskCreationTime) {
        this.taskCreationTime = taskCreationTime;
    }

    @Column(name = "MONK_NAME", nullable = false)
    public String getMonkName() {

        return monkName;
    }

    public void setMonkName(String monkName) {
        this.monkName = monkName;
    }

    @Column(name = "MONK_EVENT", nullable = false)
    public String getEvent() {
        return event;
    }

    public void setEvent(String event) {
        this.event = event;
    }

    @Column(name = "TIME_AT_EVENT", nullable = false)
    public int getTimeElapsedAtEvent() {
        return timeElapsedAtEvent;
    }

    public void setTimeElapsedAtEvent(int timeElapsedAtEvent) {
        this.timeElapsedAtEvent = timeElapsedAtEvent;
    }

    @Column(name = "REASSIGNED", nullable = false)
    public String getReassigned() {
        return reassigned;
    }

    public void setReassigned(String reassigned) {
        this.reassigned = reassigned;
    }
}
