/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

// Generated 19 Aug, 2015 4:03:38 PM by Hibernate Tools 4.0.0

import javax.ejb.Stateless;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;

import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

/**
 * Home object for domain model class ReportExecutionParams.
 * 
 * @see com.stpl.tech.kettle.data.model.ReportExecutionParams
 * <AUTHOR> Tools
 */
@Stateless
public class ReportExecutionParamsHome {

	private static final Log log = LogFactory.getLog(ReportExecutionParamsHome.class);

	@PersistenceContext
	private EntityManager entityManager;

	public void persist(ReportExecutionParams transientInstance) {
		log.debug("persisting ReportExecutionParams instance");
		try {
			entityManager.persist(transientInstance);
			log.debug("persist successful");
		} catch (RuntimeException re) {
			log.error("persist failed", re);
			throw re;
		}
	}

	public void remove(ReportExecutionParams persistentInstance) {
		log.debug("removing ReportExecutionParams instance");
		try {
			entityManager.remove(persistentInstance);
			log.debug("remove successful");
		} catch (RuntimeException re) {
			log.error("remove failed", re);
			throw re;
		}
	}

	public ReportExecutionParams merge(ReportExecutionParams detachedInstance) {
		log.debug("merging ReportExecutionParams instance");
		try {
			ReportExecutionParams result = entityManager.merge(detachedInstance);
			log.debug("merge successful");
			return result;
		} catch (RuntimeException re) {
			log.error("merge failed", re);
			throw re;
		}
	}

	public ReportExecutionParams findById(Integer id) {
		log.debug("getting ReportExecutionParams instance with productId: " + id);
		try {
			ReportExecutionParams instance = entityManager.find(ReportExecutionParams.class, id);
			log.debug("get successful");
			return instance;
		} catch (RuntimeException re) {
			log.error("get failed", re);
			throw re;
		}
	}
}
