/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

// Generated 14 Jul, 2015 1:35:13 AM by Hibernate Tools 4.0.0

import javax.persistence.Column;
import javax.persistence.Embeddable;

/**
 * TrainingDesignationMappingId generated by hbm2java
 */
@SuppressWarnings("serial")
@Embeddable
public class TrainingDesignationMappingId implements java.io.Serializable {

	private int trainingId;
	private int departmentId;
	private int designationId;

	public TrainingDesignationMappingId() {
	}

	public TrainingDesignationMappingId(int trainingId, int departmentId, int designationId) {
		this.trainingId = trainingId;
		this.departmentId = departmentId;
		this.designationId = designationId;
	}

	@Column(name = "TRAINING_ID", nullable = false)
	public int getTrainingId() {
		return this.trainingId;
	}

	public void setTrainingId(int trainingId) {
		this.trainingId = trainingId;
	}

	@Column(name = "DEPARTMENT_ID", nullable = false)
	public int getDepartmentId() {
		return this.departmentId;
	}

	public void setDepartmentId(int departmentId) {
		this.departmentId = departmentId;
	}

	@Column(name = "DESIGNATION_ID", nullable = false)
	public int getDesignationId() {
		return this.designationId;
	}

	public void setDesignationId(int designationId) {
		this.designationId = designationId;
	}

	public boolean equals(Object other) {
		if ((this == other)) {
			return true;
		}
		if ((other == null)) {
			return false;
		}
		if (!(other instanceof TrainingDesignationMappingId)) {
			return false;
		}
		TrainingDesignationMappingId castOther = (TrainingDesignationMappingId) other;

		return (this.getTrainingId() == castOther.getTrainingId())
				&& (this.getDepartmentId() == castOther.getDepartmentId())
				&& (this.getDesignationId() == castOther.getDesignationId());
	}

	public int hashCode() {
		int result = 17;

		result = 37 * result + this.getTrainingId();
		result = 37 * result + this.getDepartmentId();
		result = 37 * result + this.getDesignationId();
		return result;
	}

}
