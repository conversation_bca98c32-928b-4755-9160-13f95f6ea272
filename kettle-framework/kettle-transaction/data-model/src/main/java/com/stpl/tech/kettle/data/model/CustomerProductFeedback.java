/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.ColumnResult;
import javax.persistence.ConstructorResult;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.SqlResultSetMapping;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.stpl.tech.kettle.domain.model.CustomerDineInView;

/**
 * CustomerInfo generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "CUSTOMER_PRODUCT_FEEDBACK")
@SqlResultSetMapping(name = "CustomerDineInViewData", classes = @ConstructorResult(targetClass = CustomerDineInView.class, columns = {
		@ColumnResult(name = "customerId", type = Integer.class),
		@ColumnResult(name = "activeDineInOrders", type = Integer.class),
		@ColumnResult(name = "dineInOrders", type = Integer.class),
		@ColumnResult(name = "availedSignupOffer", type = String.class),
		@ColumnResult(name = "signupOfferExpiryTime", type = Date.class),
		@ColumnResult(name = "lastOrderTime", type = Date.class)}))
public class CustomerProductFeedback implements java.io.Serializable {

	private int id;
	private Integer customerId;
	private Integer productId;
	private Integer rating;
	private String source;
	private Integer sourceId;
	private Date addTime;
	private Date updateTime;
	private String status;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "CUSTOMER_PRODUCT_FEEDBACK_ID", unique = true, nullable = false)
	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}


	@Column(name = "CUSTOMER_ID", nullable = false)
	public Integer getCustomerId() {
		return this.customerId;
	}

	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}

	@Column(name = "PRODUCT_ID", nullable = false)
	public Integer getProductId() {
		return productId;
	}

	public void setProductId(Integer productId) {
		this.productId = productId;
	}

	@Column(name = "RATING", nullable = false)
	public Integer getRating() {
		return rating;
	}

	public void setRating(Integer rating) {
		this.rating = rating;
	}

	@Column(name = "RATING_SOURCE", nullable = false)
	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "UPDATE_TIME", nullable = true, length = 19)
	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	@Column(name = "RATING_STATUS", nullable = false)
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "ADD_TIME", nullable = false, length = 19)
	public Date getAddTime() {
		return this.addTime;
	}

	public void setAddTime(Date addTime) {
		this.addTime = addTime;
	}

	@Column(name = "RATING_SOURCE_ID", nullable = true)
	public Integer getSourceId() {
		return sourceId;
	}

	public void setSourceId(Integer sourceId) {
		this.sourceId = sourceId;
	}

	@Override
	public String toString() {
		return "CustomerProductFeedback [id=" + id + ", customerId=" + customerId + ", productId=" + productId
				+ ", rating=" + rating + ", source=" + source + ", sourceId=" + sourceId + ", addTime=" + addTime
				+ ", updateTime=" + updateTime + ", status=" + status + "]";
	}

}
