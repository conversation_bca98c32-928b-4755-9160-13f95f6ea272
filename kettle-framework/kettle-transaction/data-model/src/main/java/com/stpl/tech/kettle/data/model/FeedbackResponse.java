/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * OrderInfo generated by hbm2java
 */
@Entity
@Table(name = "FEEDBACK_RESPONSE")
public class FeedbackResponse implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -6804765738988056945L;

	private Integer feedbackResponseId;
	private int feedbackFieldId;
	private Integer feedbackInfoId;
	private String responseType;
	private Integer feedbackRating;

	public FeedbackResponse() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "FEEDBACK_RESPONSE_ID", unique = true, nullable = false)
	public Integer getFeedbackResponseId() {
		return this.feedbackResponseId;
	}

	public void setFeedbackResponseId(Integer feedbackResponseId) {
		this.feedbackResponseId = feedbackResponseId;
	}

	@Column(name = "FEEDBACK_FIELD_ID", nullable = false)
	public int getFeedbackFieldId() {
		return feedbackFieldId;
	}

	public void setFeedbackFieldId(int feedbackFieldId) {
		this.feedbackFieldId = feedbackFieldId;
	}

	@Column(name = "RESPONSE_TYPE", nullable = false, length = 20)
	public String getResponseType() {
		return responseType;
	}

	public void setResponseType(String responseType) {
		this.responseType = responseType;
	}

	@Column(name = "FEEDBACK_INFO_ID", nullable = true)
	public Integer getFeedbackInfoId() {
		return feedbackInfoId;
	}

	public void setFeedbackInfoId(Integer feedbackInfoId) {
		this.feedbackInfoId = feedbackInfoId;
	}

	@Column(name = "FEEDBACK_RATING", nullable = true)
	public Integer getFeedbackRating() {
		return feedbackRating;
	}

	public void setFeedbackRating(Integer feedbackRating) {
		this.feedbackRating = feedbackRating;
	}

}