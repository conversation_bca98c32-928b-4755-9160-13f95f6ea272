package com.stpl.tech.kettle.core.service.impl;

import com.google.gson.Gson;
import com.stpl.tech.kettle.data.dao.WalletRecommendationDao;
import com.stpl.tech.kettle.data.model.DroolsDecisionTableData;
import com.stpl.tech.kettle.core.DroolFileType;
import com.stpl.tech.kettle.core.config.DroolsConfig;
import com.stpl.tech.kettle.core.service.DroolsDecisionService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.dao.DroolsDecisionDao;
import com.stpl.tech.kettle.data.model.DroolsCustomerProperties;
import com.stpl.tech.kettle.data.model.GamifiedOfferType;
import com.stpl.tech.kettle.data.model.RecomOfferData;
import com.stpl.tech.kettle.data.model.SuperUDomain;
import com.stpl.tech.kettle.data.model.WalletRecommendationDetail;
import com.stpl.tech.kettle.data.model.WalletSuggestionCustomerInfo;
import com.stpl.tech.kettle.data.model.WalletSuggestionData;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.data.model.RedeemedCouponDetailData;
import com.stpl.tech.master.domain.model.DroolVersionDomain;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;
import com.stpl.tech.util.excelparser.SheetParser;
import com.stpl.tech.util.excelparser.exception.ExcelParsingException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.kie.api.runtime.KieContainer;
import org.kie.api.runtime.KieSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ClassPathResource;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@Slf4j
public class DroolsDecisionServiceImpl implements DroolsDecisionService {

    @Autowired
    private DroolsConfig droolsConfig;

    @Autowired
    private FileArchiveService fileArchiveService;

    @Autowired
    private EnvironmentProperties properties;

    @Autowired
    private DroolsDecisionDao droolsDecisionDao;
    @Autowired
    private WalletRecommendationDao walletRecommendationDao;

    @Autowired
    private MasterDataCache masterDataCache;

    private static final String DROOLS_FOR_OFFER_DECISION = "drools/offer_decision";
    private static final String DROOLS_FOR_RECOM_OFFER_DECISION = "drools/recom_offer_decision";
    private static final String DROOLS_FOR_WALLET_RECOMMENDATION = "drools/wallet_recommendation";
    private static final String SUPERU_PATH= "/drools/superu_parameters/";




    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Pair<String, String> getOfferString(DroolsCustomerProperties properties, boolean isPosOffer,String offerType, Integer unitId) {
        Map<String, DroolVersionDomain> unitDroolVersionMapping = null;
        if(Objects.nonNull(masterDataCache.getUnitDroolVersionMapping()) && Objects.nonNull(unitId) && masterDataCache.getUnitDroolVersionMapping().containsKey(unitId)){
            unitDroolVersionMapping = masterDataCache.getUnitDroolVersionMapping().get(unitId);
        }
        String offerSheetVersion = Objects.nonNull(unitDroolVersionMapping) && unitDroolVersionMapping.containsKey(DroolFileType.OFFER_DECISION.name()) ?
                unitDroolVersionMapping.get(DroolFileType.OFFER_DECISION.name()).getVersion() : null;
        if(!isDroolContainerInitializeForOfferDecision(offerSheetVersion)){
            initailizeDroolContainer(DroolFileType.OFFER_DECISION.getFileName(), offerSheetVersion);
        }
        KieContainer container = droolsConfig.getKieContainerForOfferDecision(offerSheetVersion);
        KieSession kieSession = container.newKieSession();
        kieSession.insert(properties);
        kieSession.fireAllRules();
        kieSession.destroy();
        log.info("Offer Drools result : {}", JSONSerializer.toJSON(properties));
        Pair<String, String> offerPair = new Pair<>();
        if(isPosOffer){
            if (Objects.nonNull(properties.getDayPartOffer()) && AppConstants.YES.equalsIgnoreCase(properties.getDayPartOffer())) {
                String dayPart = AppUtils.getDayPart(AppUtils.getHour(AppUtils.getCurrentTimestamp()));
                if (Objects.nonNull(properties.getDayPartOfferString()) && properties.getDayPartOfferString().containsKey(dayPart)) {
                    offerPair.setKey(properties.getDayPartOfferString().get(dayPart));
                } else {
                    offerPair.setKey(properties.getPosOfferString());
                }
            }
            else if(Objects.nonNull(offerType) &&
                    (GamifiedOfferType.RECOM.name().equalsIgnoreCase(offerType) ||
                            GamifiedOfferType.FREEBIE.name().equalsIgnoreCase(offerType))){
                String offerString = GamifiedOfferType.RECOM.name().equalsIgnoreCase(offerType) ? properties.getRecomOfferString()
                        : properties.getFreebieOfferString();
                offerPair.setKey(offerString);
            }else {
                offerPair.setKey(properties.getPosOfferString());
            }
        }else{
            offerPair.setKey(properties.getDigitalOfferString());
        }
        offerPair.setValue(properties.getOfferOccurrenceType());
        return  offerPair;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public RecomOfferData getRecomOfferData(DroolsCustomerProperties properties, Integer unitId) {
        Map<String, DroolVersionDomain> unitDroolVersionMapping = null;
        if(Objects.nonNull(masterDataCache.getUnitDroolVersionMapping()) && Objects.nonNull(unitId) && masterDataCache.getUnitDroolVersionMapping().containsKey(unitId)){
            unitDroolVersionMapping = masterDataCache.getUnitDroolVersionMapping().get(unitId);
        }
        String recommOfferSheetVersion = Objects.nonNull(unitDroolVersionMapping) && unitDroolVersionMapping.containsKey(DroolFileType.RECOM_OFFER_DECISION.name()) ?
                unitDroolVersionMapping.get(DroolFileType.RECOM_OFFER_DECISION.name()).getVersion() : null;
        if(!isDroolContainerInitializeForRecomOfferDecision(recommOfferSheetVersion)){
            initailizeDroolContainer(DroolFileType.RECOM_OFFER_DECISION.getFileName(), recommOfferSheetVersion);
        }
        KieContainer container = droolsConfig.getKieContainerForRecomOfferDecision(recommOfferSheetVersion);
        KieSession kieSession = container.newKieSession();
        kieSession.insert(properties);
        kieSession.fireAllRules();
        kieSession.destroy();
        log.info("Recom Drools result : {}", JSONSerializer.toJSON(properties));
        List<Integer> productIds = new ArrayList<>();
        for(String id : properties.getProductIds().split(",")){
            if(!id.isEmpty()){
                productIds.add(Integer.valueOf(id));
            }
        }
        return RecomOfferData.builder()
                .resultAPC(properties.getResultAPC())
                .discountType(properties.getDiscountType())
                .discountValue(properties.getDiscountValue())
                .offerTag(properties.getOfferTag())
                .productIds(productIds)
                .build();
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean addNewDroolFile(MultipartFile file,String droolFileType, boolean persist){
        try {
            if(Objects.isNull(droolFileType)){
                log.info("Error while updating drool sheet as drool file type in null");
                return false;
            }
            String fileTypeName = DroolFileType.valueOf(droolFileType).getFileName();
            DroolsDecisionTableData currentDroolData = droolsDecisionDao.getDecisionTableData(droolFileType);
            if(Objects.isNull(currentDroolData)){
                currentDroolData = DroolsDecisionTableData.builder().fileName(fileTypeName + "_v0.0.xls").build();
            }
            String fileName = getNextVersion(currentDroolData.getFileName().split(".xls")[0]) + ".xls";
            String version = "v" + fileName.split("_v")[1].split(".xls")[0];
            if(persist){
                String baseDir = properties.getEnvironmentType().name().toLowerCase() + "/" + fileTypeName  + "/" + version;
                log.info(":::::: Request to upload New Drool decision table ::::::");
                FileDetail s3File = fileArchiveService.saveFileToS3(properties.getS3BucketForDrools(),
                        baseDir, fileName, file, true);
                if (s3File != null) {
                    log.info("URL ::::: {}", s3File.getUrl());
                }
                saveCurrentAndDeactivateOther(DroolsDecisionTableData.builder()
                        .type(DroolFileType.valueOf(droolFileType).name())
                        .creationTime(AppUtils.getCurrentTimestamp())
                        .fileName(fileName)
                        .status(AppConstants.PROCESSING)
                        .version(version)
                        .isDefault(AppConstants.NO)
                        .build());
            }
        }catch (Exception e){
            return false;
        }
        return true;
    }


    private void resetDroolContainer(String droolFileType, String version){
        if(DroolFileType.OFFER_DECISION.equals(DroolFileType.valueOf(droolFileType))){
            droolsConfig.initDroolConfigForOfferDecision(version);
        } else if (DroolFileType.RECOM_OFFER_DECISION.equals(DroolFileType.valueOf(droolFileType))) {
            droolsConfig.initDroolConfigForRecomOfferDecision(version);
        } else if (DroolFileType.WALLET_RECOMMENDATION.equals(DroolFileType.valueOf(droolFileType))) {
            droolsConfig.initWalletRecommendationDroolConfig(version);
        } else if (DroolFileType.SUPERU_PARAMETER.equals(DroolFileType.valueOf(droolFileType))) {
            droolsConfig.initSuperUDroolConfig(version);
        }
    }

    @Override
    public void downloadRecipeMedia(HttpServletResponse response, String fileName, String droolFileType,String version) throws IOException {
        String s3RecipeMediaBucket = properties.getS3BucketForDrools();
        String envType = properties.getEnvironmentType().name().toLowerCase();
        FileDetail fileDetail = new FileDetail(s3RecipeMediaBucket, envType+"/"+DroolFileType.valueOf(droolFileType).getFileName()+"/"+ version + "/" + fileName, null);
        File file = fileArchiveService.getFileFromS3(properties.getDroolBasePath() + File.separator + "s3", fileDetail);
        setFileToResponse(response,fileName, file);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean activateVersion(String fileName, String droolFileType, String version) throws IOException {
        try {
            if(Objects.isNull(droolFileType)){
                log.info("Error while updating drool sheet as drool file type in null");
                return false;
            }
            getFileFromS3andDownload(DroolFileType.valueOf(droolFileType).getFileName(),fileName,version);
            resetDroolContainer(droolFileType, version);
//            droolsDecisionDao.deactivateOther(droolFileType);
            droolsDecisionDao.activateCurrentFile(droolFileType, fileName);
        }catch (Exception e ){
            log.error("Error in activating drool File : {}",e);
            return false;
        }
        return true;
    }

    @Override
    public List<DroolsDecisionTableData> fetchAllFileByType(String fileType) {
        return droolsDecisionDao.fetchAllFileByType(fileType);
    }

    private void setFileToResponse(HttpServletResponse response, String fileName, File file) throws IOException {
        if (Objects.nonNull(file)) {
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.addHeader("Content-Disposition", "attachment; filename=" + file.getName());
            byte[] bytesArray = new byte[(int) file.length()];
            response.setContentLength(bytesArray.length);
            try {
                OutputStream outputStream = response.getOutputStream();
                InputStream inputStream = new FileInputStream(file);
                int counter = 0;
                while ((counter = inputStream.read(bytesArray, 0, bytesArray.length)) > 0) {
                    outputStream.write(bytesArray, 0, counter);
                    outputStream.flush();
                }
                outputStream.close();
                inputStream.close();
            } catch (IOException e) {
                log.error("Encountered error while writing file to response stream", e);
                throw e;
            } finally {
                response.getOutputStream().flush();
                file.delete(); // delete the temporary file created after completing request
            }
        }
    }

    private static String getNextVersion(String fileName){
        Double version = Double.valueOf(fileName.split("_v")[1]);
        version = version +.1;
        version = BigDecimal.valueOf(version).setScale(1, RoundingMode.HALF_UP).doubleValue();
        return (fileName.split("_v")[0]+"_v"+version);
    }

    private void saveCurrentAndDeactivateOther(DroolsDecisionTableData data){
        //droolsDecisionDao.deactivateOther(data.getType());
        droolsDecisionDao.declineExistingProcessingFile(data.getType(), data.getFileName());
        droolsDecisionDao.add(data);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "ClmDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public WalletSuggestionData getCustomerWalletSuggestionData(String customerId, String brandId){

        try {
            if(Objects.nonNull(customerId) && Objects.nonNull(brandId)) {
                List<Object[]> customerData =  walletRecommendationDao.getCustomerWalletInfo(Integer.valueOf(customerId), Integer.valueOf(brandId));
                if(Objects.isNull(customerData) || customerData.isEmpty() ){
                    return setCustomerWalletInfo(null);
                }
                return  setCustomerWalletInfo(customerData.get(0));
            }
        }catch (Exception e){
            log.error("Error getting Customer Info from CLM One View for customer::"+customerId,e);
        }
        return  setCustomerWalletInfo(null);
    }

    private WalletSuggestionData setCustomerWalletInfo(Object[] customerData){
        if(Objects.isNull(customerData)){
            return  WalletSuggestionData.builder().isNewCustomer("Y").build();
        }else{
            return WalletSuggestionData.builder()
                    .isNewCustomer("N")
                    .latestOrderDayDiff(Objects.nonNull(customerData[7]) ? AppUtils.getActualDayDifference(
                            AppUtils.getDate(String.valueOf(customerData[7]),AppUtils.DATE_FORMAT_STRING),
                            AppUtils.getCurrentDate()): -1000000)
                    .visitsCount(Integer.valueOf(String.valueOf(customerData[2])))
                    .build();
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public WalletRecommendationDetail getCustomerWalletRecommendation(WalletSuggestionCustomerInfo customerData){
        WalletSuggestionData customerWalletData =null;
        if(Objects.nonNull(customerData)){
            Map<String, DroolVersionDomain> unitDroolVersionMapping = null;
            if(Objects.nonNull(masterDataCache.getUnitDroolVersionMapping())) {
                unitDroolVersionMapping = masterDataCache.getUnitDroolVersionMapping().get(customerData.getUnitId());
            }
            customerWalletData = WalletSuggestionData.builder().amountPayable(Integer.valueOf(customerData.getAmountPayable())).isNewCustomer(customerData.getNewCustomer()).latestOrderDayDiff(Integer.parseInt(customerData.getLastVisitTime())).build();
            log.info("Printing customer WalletData::{}", new Gson().toJson(customerWalletData));
            String walletRecomSheetVersion = Objects.nonNull(unitDroolVersionMapping) && unitDroolVersionMapping.containsKey(DroolFileType.WALLET_RECOMMENDATION.name()) ?
                    unitDroolVersionMapping.get(DroolFileType.WALLET_RECOMMENDATION.name()).getVersion() : null;
            if(!isDroolContainerInitializeForWalletRecommendation(walletRecomSheetVersion)){
                initailizeDroolContainer(DroolFileType.WALLET_RECOMMENDATION.getFileName(), walletRecomSheetVersion);
            }
            KieContainer container = droolsConfig.getKieContainerForWalletRecommendation(walletRecomSheetVersion);
            KieSession kieSession = container.newKieSession();
            kieSession.insert(customerWalletData);
            kieSession.fireAllRules();
            kieSession.destroy();

            log.info("Wallet Suggestion Data after drools fired::{}",new Gson().toJson(customerWalletData));
            List<Integer> denominationArray = customerData.getActiveDenominationMap().keySet().stream().map(Integer::parseInt).sorted(Collections.reverseOrder()).collect(Collectors.toList());
            Integer suggestWalletAmount = 0;
            Integer extraWalletAmount = 0;
            Integer totalPayableAmount = Integer.valueOf(customerData.getAmountPayable());
            for(int i=0; i<denominationArray.size();i++){
                if(totalPayableAmount >= denominationArray.get(i) && denominationArray.get(i)>=customerWalletData.getMinimumDenomination()){
                    Integer quantity = totalPayableAmount/denominationArray.get(i);
                    if(quantity > 0){
                        totalPayableAmount = totalPayableAmount - (quantity*denominationArray.get(i));
                    }
                    suggestWalletAmount += denominationArray.get(i) * quantity;
                    extraWalletAmount += Integer.parseInt(customerData.getActiveDenominationMap().get(String.valueOf(denominationArray.get(i)))) * quantity;
                }
            }
            if (totalPayableAmount <200 && totalPayableAmount >= 0) {
                if ( ((customerWalletData.getMinimumDenomination()*customerWalletData.getMinimumDenominationCount()) - totalPayableAmount) > customerWalletData.getWalletRecommendationBaseValue()) {
                    suggestWalletAmount += (customerWalletData.getMinimumDenomination()*customerWalletData.getMinimumDenominationCount());
                    extraWalletAmount +=  (customerWalletData.getMinimumDenominationCount() * (Integer.parseInt(customerData.getActiveDenominationMap().get(String.valueOf(customerWalletData.getMinimumDenomination())))) );
                }
                else {
                    suggestWalletAmount += (customerWalletData.getMinimumDenomination()*(customerWalletData.getMinimumDenominationCount() +1)) ;
                    extraWalletAmount +=  ((customerWalletData.getMinimumDenominationCount() +1) * (Integer.parseInt(customerData.getActiveDenominationMap().get(String.valueOf(customerWalletData.getMinimumDenomination())))));
                }
                totalPayableAmount = 0;
            }
            //Double extraAmount = (extraWalletAmount*customerWalletData.getExtraValueMultiplier())/10;
            Double extraAmount = (extraWalletAmount*customerWalletData.getExtraValueMultiplier());
            return WalletRecommendationDetail.builder().walletSuggestion(suggestWalletAmount).extraAmount(extraAmount.intValue()).build();


        }
        return null;
    }

    @Override
    public boolean isDroolContainerInitializeForOfferDecision(String version){
        return Objects.nonNull(droolsConfig.getKieContainerForOfferDecision(version));
    }

    @Override
    public boolean isDroolContainerInitializeForRecomOfferDecision(String version){
        return Objects.nonNull(droolsConfig.getKieContainerForRecomOfferDecision(version));
    }
    @Override
    public boolean isDroolContainerInitializeForWalletRecommendation(String version){
        return Objects.nonNull(droolsConfig.getKieContainerForWalletRecommendation(version));
    }

    @Override
    public boolean isDroolContainerInitializeForSuperU(String version){
        return Objects.nonNull(droolsConfig.getKieContainerForSuperU(version));
    }
    public void processAndDownloadDroolFile(String droolFileType,String version) throws Exception {
        String fileTypeName = droolFileType;
        try {
            List<DroolsDecisionTableData> currentDroolData = droolsDecisionDao.getDecisionTableDataByStatus(droolFileType.toUpperCase());
            boolean isFileUpdated = false;
            if (Objects.nonNull(currentDroolData) && !currentDroolData.isEmpty()) {
                for (DroolsDecisionTableData data : currentDroolData) {
                    if (AppConstants.PROCESSING.equals(data.getStatus()) && !isFileUpdated) {
                        getFileFromS3andDownload(fileTypeName, data.getFileName(),version);
                        droolsDecisionDao.declineExistingProcessingFile(droolFileType.toUpperCase(),data.getFileName());
//                        droolsDecisionDao.deactivateOther(droolFileType.toUpperCase());
                        droolsDecisionDao.activateCurrentFile(droolFileType.toUpperCase(), data.getFileName());
                        isFileUpdated = true;
                    }
                }
                if(!isFileUpdated){
                    DroolsDecisionTableData data = currentDroolData.get(0);
                    File file = null;
                    if(AppConstants.ACTIVE.equals(data.getStatus())) {
                        String filePath = "";
                        if(Objects.nonNull(version)) {
                            filePath = properties.getDroolBasePath() + "/drools/" + fileTypeName + "/" + version + "/" + fileTypeName + ".xls";
                        }
                        else{
                            filePath = properties.getDroolBasePath() + "/drools/" + fileTypeName + "/default/" + fileTypeName + ".xls";
                        }
                        file = Paths.get(filePath).toFile();
                    }
                    if(Objects.isNull(file) || !file.exists()){
                        getFileFromS3andDownload(fileTypeName, data.getFileName(),version);
                    }
                }
            }
            else {
                copyFromResource(droolFileType);
            }
        }catch (Exception e){
            log.info("Error in Processing Drool file : {}",e.getMessage());
            log.info("Drool File not available on S3");
        }
    }

    @Override
    public void initailizeDroolContainer(String droolFileType, String version){
        try{
            processAndDownloadDroolFile(droolFileType,version);
            if(DroolFileType.OFFER_DECISION.getFileName().equals(droolFileType)) {
                droolsConfig.initDroolConfigForOfferDecision(version);
            }
            if(DroolFileType.RECOM_OFFER_DECISION.getFileName().equals(droolFileType)) {
                droolsConfig.initDroolConfigForRecomOfferDecision(version);
            }
            if(DroolFileType.WALLET_RECOMMENDATION.getFileName().equals(droolFileType)){
                droolsConfig.initWalletRecommendationDroolConfig(version);
            }
            if(DroolFileType.SUPERU_PARAMETER.getFileName().equals(droolFileType)){
                droolsConfig.initSuperUDroolConfig(version);
            }
        }catch (Exception e){
            log.info("Error in initialize drool container for offerDecision : {}",e.getMessage());
            e.printStackTrace();
        }
    }

    private void getFileFromS3andDownload(String fileTypeName,String fileName, String version) throws Exception {
        try {
            OutputStream os;
            String s3RecipeMediaBucket = properties.getS3BucketForDrools();
            String envType = properties.getEnvironmentType().name().toLowerCase();
            String path = properties.getDroolBasePath() + "/drools/" + fileTypeName + "/";
            String filePath = Objects.nonNull(version) ? envType + "/" + fileTypeName + "/" + version + "/" + fileName :
                    envType + "/" + fileTypeName + "/default/" + DroolFileType.valueOf(fileTypeName).getFileName();
            FileDetail fileDetail = new FileDetail(s3RecipeMediaBucket, filePath.replaceAll(" ",""), null);
            File file = fileArchiveService.getFileFromS3(properties.getDroolBasePath() + File.separator + "s3", fileDetail);
            FileInputStream input = new FileInputStream(file);
            MultipartFile multipartFile = new MockMultipartFile("file",
                    file.getName(), "text/plain", IOUtils.toByteArray(input));
            String newFilePath = Objects.nonNull(version) ? path + version + "/" + fileTypeName + ".xls" : path + "default/" + fileTypeName + ".xls";
            File newFile = new File(newFilePath);
            if (!newFile.exists()) {
                Path dirPath = Paths.get(path + (Objects.nonNull(version) ? version : "default"));
                if (!Files.exists(dirPath)) {
                    Files.createDirectories(dirPath);
                }
                boolean isFileCreated = newFile.createNewFile();
                if (!isFileCreated) {
                    log.info("Unable to create file {} skipping reset process", newFile.getAbsolutePath());
                    throw new Exception("Unable to Create file");
                }
            }
            os = new FileOutputStream(newFile);
            os.write(multipartFile.getBytes());
            os.close();
            os.flush();
        }catch (Exception e){
            log.info("Error in saving file to data folder from s3 : {}",e.getMessage());
        }
    }


    public void copyFromResource(String droolFileType){
        String destinationDirectory = "";
        String sourceFileDirectory = "";
        if(DroolFileType.OFFER_DECISION.getFileName().equals(droolFileType)) {
            destinationDirectory = properties.getBasePath() + "/" + DROOLS_FOR_OFFER_DECISION;
            sourceFileDirectory = DROOLS_FOR_OFFER_DECISION + "/" + droolFileType + ".xls";
        }
        if(DroolFileType.RECOM_OFFER_DECISION.getFileName().equals(droolFileType)){
            destinationDirectory = properties.getBasePath() + "/" + DROOLS_FOR_RECOM_OFFER_DECISION;
            sourceFileDirectory = DROOLS_FOR_RECOM_OFFER_DECISION + "/" + droolFileType + ".xls";
        }
        if(DroolFileType.WALLET_RECOMMENDATION.getFileName().equals(droolFileType)){
            destinationDirectory = properties.getBasePath() + "/" + DROOLS_FOR_WALLET_RECOMMENDATION;
            sourceFileDirectory = DROOLS_FOR_WALLET_RECOMMENDATION + "/" + droolFileType + ".xls";
        }
        if(DroolFileType.SUPERU_PARAMETER.getFileName().equals(droolFileType)){
            destinationDirectory = properties.getBasePath() + "/" + SUPERU_PATH +"/default";
            sourceFileDirectory = SUPERU_PATH + "/" + droolFileType + ".xls";
        }
        try {
            fileArchiveService.saveFileToDestinationPath(sourceFileDirectory,destinationDirectory,droolFileType);
        }catch (Exception e){
            log.info("Error in saving commission matrix drool file into data folder : {}",e.getMessage());
        }
    }

    /**
     *
     * @param file
     * @param clazz
     * @return
     * @throws FileNotFoundException
     *
     * This Function parse the sheet and validate if it is correct or not
     */
    private boolean parseAndValidateSheet(File file,Class<?> clazz) throws FileNotFoundException {
        try {
            InputStream inputStream = new FileInputStream(file);
            Workbook workbook;
            if (file.getName().endsWith("xls")) {
                workbook = new HSSFWorkbook(inputStream);
            } else {
                workbook = new XSSFWorkbook(inputStream);
            }
            List<ExcelParsingException> errors = new ArrayList<>();
            SheetParser parser = new SheetParser();
            parser.createEntity(workbook.getSheetAt(0),clazz, errors::add);
            if(errors.isEmpty()){
                return true;
            }
        }catch (Exception e){
            log.info("Error in parsing the sheet : {}",e.getMessage());
        }
        return false;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean setDefaultDroolSheet(String droolFileType, String fileName, String version) {
        try {
            String s3RecipeMediaBucket = properties.getS3BucketForDrools();
            String envType = properties.getEnvironmentType().name().toLowerCase();
            FileDetail fileDetail = new FileDetail(s3RecipeMediaBucket, envType + "/" + DroolFileType.valueOf(droolFileType).getFileName() + "/" + version + "/" + fileName, null);
            File file = fileArchiveService.getFileFromS3(properties.getDroolBasePath() + File.separator + "s3", fileDetail);
            try (FileInputStream input = new FileInputStream(file)) {
                MultipartFile multipartFile = new MockMultipartFile("file", file.getName(), "text/plain", IOUtils.toByteArray(input));
                String filePath = properties.getDroolBasePath() + "/drools/" + DroolFileType.valueOf(droolFileType).getFileName() + "/default";
                Path dirPath = Paths.get(filePath);
                Path newFilePath = dirPath.resolve(DroolFileType.valueOf(droolFileType).getFileName() + ".xls");
                if (!Files.exists(dirPath)) {
                    Files.createDirectories(dirPath);
                }
                if (Files.deleteIfExists(newFilePath)) {
                    log.info("Existing file {} deleted successfully", newFilePath);
                }
                try (OutputStream os = new FileOutputStream(newFilePath.toFile())) {
                    os.write(multipartFile.getBytes());
                    log.info("New file created and written: {}", newFilePath);
                }
                String fileTypeName = DroolFileType.valueOf(droolFileType).getFileName();
                String baseDir = properties.getEnvironmentType().name().toLowerCase() + "/" + fileTypeName + "/default";
                log.info(":::::: Request to upload default file to S3 ::::::");
                FileDetail s3File = fileArchiveService.saveFileToS3(properties.getS3BucketForDrools(),
                        baseDir, DroolFileType.valueOf(droolFileType).getFileName() + ".xls", multipartFile, true);
                if (s3File != null) {
                    log.info("URL ::::: {}", s3File.getUrl());
                }
                droolsDecisionDao.findByTypeAndVersionAndSetIsDefault(droolFileType.toUpperCase(), version, AppConstants.YES);
                resetDroolContainer(droolFileType,null);
            } catch (IOException e) {
                log.error("Error processing the file: {}", e.getMessage());
                throw new RuntimeException("File processing failed", e);
            }
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean inactivateVersion(String droolFileType, String fileName, String version) {
        try {
            String folderPath = properties.getEnvironmentType().name().toLowerCase() + "/" + DroolFileType.valueOf(droolFileType).getFileName() + "/" + version;
            Path filePath = Paths.get(folderPath);
            if (Files.exists(filePath)) {
                Files.delete(filePath);
                log.info("File deleted from server: " + folderPath);
            } else {
                log.info("File does not exist on server: " + folderPath);
            }
            droolsDecisionDao.findByTypeAndVersionAndSetStatusAndIsDefault(droolFileType.toUpperCase(), version, AppConstants.IN_ACTIVE,AppConstants.NO);
        } catch (Exception e) {
            return false;
        }
        return true;
    }

    @Override
    public SuperUDomain getSuperUParam(SuperUDomain superUDomain){
        superUDomain.setIsSuperULive("Y");
        String version = null;
        log.info("inside getSuperUParam");
        initailizeDroolContainer(DroolFileType.SUPERU_PARAMETER.getFileName(), version);
        KieContainer container = droolsConfig.getKieContainerForSuperU(version);
        KieSession kieSession = container.newKieSession();
        kieSession.insert(superUDomain);
        kieSession.fireAllRules();
        kieSession.destroy();
        log.info("Offer Drools result : {}", JSONSerializer.toJSON(superUDomain));
        return superUDomain;
    }



}
