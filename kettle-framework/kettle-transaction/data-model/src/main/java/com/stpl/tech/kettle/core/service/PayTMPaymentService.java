package com.stpl.tech.kettle.core.service;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import com.stpl.tech.kettle.core.exception.PaymentFailureException;
import com.stpl.tech.kettle.data.model.OrderPaymentDetail;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.payment.model.OrderPayment;
import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.master.payment.model.PaymentRefundRequest;
import com.stpl.tech.master.payment.model.patymNew.PaytmStatusResponse;
import com.stpl.tech.master.payment.model.paytm.PaytmCreateRequest;
import com.stpl.tech.master.payment.model.paytm.PaytmCreateResponse;
import com.stpl.tech.master.payment.model.paytm.PaytmPaymentStatus;
import com.stpl.tech.master.payment.model.paytmUpi.PaytmUpiQrRequest;
import com.stpl.tech.master.payment.model.paytmUpi.PaytmUpiQrResponse;
import com.stpl.tech.master.payment.model.paytmUpi.PaytmUpiS2SResponse;
import org.json.JSONException;

public interface PayTMPaymentService {

	public PaytmCreateRequest createRequest(OrderPaymentRequest order) throws PaymentFailureException, DataNotFoundException;


    PaytmStatusResponse getPaytmStatusResponse(String orderId) throws Exception;

    boolean validateResponse(PaytmCreateResponse response);

 //   boolean validateResponse(PaytmCreateResponse response, boolean verifyPaytmStatus);

    public PaytmCreateResponse getPaymentStatus(String externalOrderId, BigDecimal transactionAmount) throws PaymentFailureException, IOException, JSONException;

	public OrderPayment refundRequest(OrderPayment request) throws IOException, PaymentFailureException;

	public PaytmCreateRequest createPayTMQRForKIOSK(OrderPaymentRequest order, PaytmCreateRequest request) throws Exception;

	public PaytmPaymentStatus checkKIOSKPaytmQRPaymentStatus(String orderID) throws Exception;

	public boolean refundKIOSKPaytmQRPaymentAmount(String orderId, BigDecimal amountToRefund, String refundReason) throws Exception;

	public PaytmUpiQrRequest createPaytmUpiRequest(OrderPaymentRequest order) throws PaymentFailureException, DataNotFoundException;

	public PaytmUpiQrResponse createPayTMUPIQR(OrderPaymentRequest order, PaytmUpiQrRequest request) throws Exception;

	public OrderPayment refundPaytmUpiQR(OrderPayment paymentRefundRequest) throws PaymentFailureException;

    PaytmUpiQrResponse getPayTmUpiQRCodeId(OrderPaymentRequest order) throws PaymentFailureException;

	PaytmCreateRequest getPayTMQRCodeIdForKIOSK(OrderPaymentRequest order) throws PaymentFailureException;

	OrderPayment checkPaytmPayment(OrderPaymentDetail paymentDetail);

	Boolean checkPaytmPaymentStatus(OrderPaymentDetail paymentDetail);

    PaytmCreateRequest createPaytmRequest(OrderPaymentRequest orderPaymentRequest, Map<String,String> map) throws DataNotFoundException, PaymentFailureException;

    Map updatePaytmResponse(PaytmCreateResponse response);

	OrderPayment refundPaytmUpiQRWrapper(OrderPayment paymentRefundRequest) throws PaymentFailureException;

	Map updatePaytmUpiStatus(PaytmUpiS2SResponse response);

	Boolean syncOrderAndInitiatePaytmRefundInBulk(List<OrderPaymentDetail> opdList);
}
