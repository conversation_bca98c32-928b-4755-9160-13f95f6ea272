package com.stpl.tech.kettle.customer.dao.impl;

import java.util.ArrayList;
import java.util.List;

import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.customer.dao.CustomerDataLookupDao;
import com.stpl.tech.kettle.data.dao.impl.AbstractDaoImpl;
import com.stpl.tech.master.data.dao.AbstractDao;

import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

@Repository
@Slf4j
public class CustomerDataLookupDaoImpl extends AbstractDaoImpl implements AbstractDao, CustomerDataLookupDao {

	@Override
	public <T> List<T> updateAll(List<T> list) {
		if (list == null) {
			return null;
		}
		List<T> l = new ArrayList<T>();
		if (list.isEmpty()) {
			return l;
		}
		try {
			for (T data : list) {
				manager.merge(data);
				l.add(data);
			}
			manager.flush();
			return l;
		} catch (Exception e) {
			log.error("Error adding " + list.getClass().getName() + " {}", e.getMessage());
		}
		return null;
	}

}
