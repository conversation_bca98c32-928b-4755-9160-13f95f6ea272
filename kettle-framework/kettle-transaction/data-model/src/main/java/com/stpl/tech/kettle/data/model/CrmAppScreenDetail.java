package com.stpl.tech.kettle.data.model;

import com.stpl.tech.util.AppUtils;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;


import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "CRM_APP_SCREEN_DETAIL")
public class CrmAppScreenDetail {


    private Integer key;
    private String screenType;
    private String city;
    private String contentType;
    private String imagePath;
    private String cityType;
    private String status;
    private Integer updatedBy;
    private Integer unitId;
    private String unitName;
    private Date updatedOn;

    public CrmAppScreenDetail() {
    }

    public CrmAppScreenDetail(CrmAppScreenDetail detail) {
        this.screenType = detail.getScreenType();
        this.city = detail.getCity();
        this.contentType = detail.getContentType();
        this.imagePath = detail.getImagePath();
        this.cityType = detail.getCityType();
        this.status = detail.getStatus();
        this.updatedBy = detail.getUpdatedBy();
        this.updatedOn = AppUtils.getCurrentTimestamp();
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name="KEY_ID")
    public Integer getKey() {
        return key;
    }

    public void setKey(Integer key) {
        this.key = key;
    }

    @Column(name="SCREEN_TYPE")
    public String getScreenType() {
        return screenType;
    }

    public void setScreenType(String screenType) {
        this.screenType = screenType;
    }

    @Column(name="CITY")
    public String getCity() {
        return city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    @Column(name="CONTENT_TYPE")
    public String getContentType() {
        return contentType;
    }

    public void setContentType(String contentType) {
        this.contentType = contentType;
    }

    @Column(name="PATH")
    public String getImagePath() {
        return imagePath;
    }

    public void setImagePath(String imagePath) {
        this.imagePath = imagePath;
    }

    @Column(name="CITY_TYPE")
    public String getCityType() {
        return cityType;
    }

    public void setCityType(String cityType) {
        this.cityType = cityType;
    }

    @Column(name="STATUS")
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name="UPDATED_BY")
    public Integer getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Integer updatedBy) {
        this.updatedBy = updatedBy;
    }

    @Temporal(TemporalType.DATE)
    @Column(name="UPDATED_ON")
    public Date getUpdatedOn() {
        return updatedOn;
    }

    public void setUpdatedOn(Date updatedOn) {
        this.updatedOn = updatedOn;
    }

    @Column(name = "UNIT_ID")
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @Column(name = "UNIT_NAME")
    public String getUnitName() { return unitName; }

    public void setUnitName(String unitName) { this.unitName = unitName; }
}
