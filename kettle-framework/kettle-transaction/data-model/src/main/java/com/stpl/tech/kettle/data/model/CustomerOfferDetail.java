/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;
// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.ColumnResult;
import javax.persistence.ConstructorResult;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.SqlResultSetMapping;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.stpl.tech.kettle.domain.model.CustomerEmailData;

/**
 * ChannelPartner generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "CUSTOMER_OFFER_DETAIL")
@SqlResultSetMapping(name = "CustomerEmailData", classes = @ConstructorResult(targetClass = CustomerEmailData.class, columns = {
		@ColumnResult(name = "customerId", type = Integer.class),
		@ColumnResult(name = "overallOrders", type = Integer.class),
		@ColumnResult(name = "overallVisits", type = Integer.class),
		@ColumnResult(name = "overallSavings", type = BigDecimal.class),
		@ColumnResult(name = "availedSignupOffer", type = String.class),
		@ColumnResult(name = "signupOfferExpiryTime", type = Date.class),
		@ColumnResult(name = "acquiredLoyaltyPoints", type = Integer.class),
		@ColumnResult(name = "walletBalance", type = BigDecimal.class),
		@ColumnResult(name = "chaayosCashBalance", type = BigDecimal.class),
		@ColumnResult(name = "membershipAvailable", type = String.class),
		@ColumnResult(name = "membershipPlan", type = String.class),
		@ColumnResult(name = "membershipEndDate", type = Date.class)}))
public class CustomerOfferDetail implements java.io.Serializable {

	private Integer offerDetailId;
	private Integer customerId;
	private String offerCode;
	private Date availTime;
	private Integer orderId;
	private String availed;

	public CustomerOfferDetail() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)

	@Column(name = "OFFER_DETAIL_ID", unique = true, nullable = false)
	public Integer getOfferDetailId() {
		return this.offerDetailId;
	}

	public void setOfferDetailId(Integer offerDetailId) {
		this.offerDetailId = offerDetailId;
	}

	@Column(name = "CUSTOMER_ID", nullable = false)
	public Integer getCustomerId() {
		return this.customerId;
	}

	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}

	@Column(name = "OFFER_CODE", nullable = false, length = 100)
	public String getOfferCode() {
		return offerCode;
	}

	public void setOfferCode(String offerCode) {
		this.offerCode = offerCode;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "AVAIL_TIME", length = 19)
	public Date getAvailTime() {
		return availTime;
	}

	public void setAvailTime(Date availTime) {
		this.availTime = availTime;
	}

	@Column(name = "ORDER_ID", nullable = false)
	public Integer getOrderId() {
		return orderId;
	}

	public void setOrderId(Integer orderId) {
		this.orderId = orderId;
	}

	@Column(name = "IS_AVAILED", nullable = false, length = 1)
	public String getAvailed() {
		return availed;
	}

	public void setAvailed(String availed) {
		this.availed = availed;
	}

}
