/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

// Generated 27 Jul, 2015 8:18:00 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.stpl.tech.util.AppConstants;

/**
 * UnitClosureDetails generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "UNIT_CLOSURE_DETAILS")
public class UnitClosureDetails implements java.io.Serializable {

	private Integer closureId;
	private int unitId;
	private Date businessDate;
	private Date closureStartTime;
	private Date closureEndTime;
	private Integer startOrderId;
	private Integer lastOrderId;
	private int employeeId;
	private String reportStatus;
	private String reconciliationStatus;
	private String closureComment;
	private String currentStatus;
	private String pnlGenerated;
	private Integer pnlInstanceId;
	private List<ClosureStatus> closureStatuses = new ArrayList<ClosureStatus>(0);
	private List<ClosurePaymentDetails> closurePaymentDetailses = new ArrayList<ClosurePaymentDetails>(0);

	public UnitClosureDetails() {
	}

	public UnitClosureDetails(int unitId, Date businessDate, Date closureStartTime, int employeeId,
			String currentStatus) {
		this.unitId = unitId;
		this.businessDate = businessDate;
		this.closureStartTime = closureStartTime;
		this.employeeId = employeeId;
		this.currentStatus = currentStatus;
		this.pnlGenerated = AppConstants.NO;
	}

	public UnitClosureDetails(int unitId, Date businessDate, Date closureStartTime, Date closureEndTime,
			Integer startOrderId, Integer lastOrderId, int employeeId, String reconciliationStatus,
			String closureComment, List<ClosureStatus> closureStatuses,
			List<ClosurePaymentDetails> closurePaymentDetailses, String currentStatus) {
		this.unitId = unitId;
		this.businessDate = businessDate;
		this.closureStartTime = closureStartTime;
		this.closureEndTime = closureEndTime;
		this.startOrderId = startOrderId;
		this.lastOrderId = lastOrderId;
		this.employeeId = employeeId;
		this.reconciliationStatus = reconciliationStatus;
		this.closureComment = closureComment;
		this.closureStatuses = closureStatuses;
		this.closurePaymentDetailses = closurePaymentDetailses;
		this.currentStatus = currentStatus;
		this.pnlGenerated = AppConstants.NO;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "CLOSURE_ID", unique = true, nullable = false)
	public Integer getClosureId() {
		return this.closureId;
	}

	public void setClosureId(Integer closureId) {
		this.closureId = closureId;
	}

	@Column(name = "UNIT_ID", nullable = false)
	public int getUnitId() {
		return this.unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "BUSINESS_DATE", nullable = false, length = 10)
	public Date getBusinessDate() {
		return this.businessDate;
	}

	public void setBusinessDate(Date businessDate) {
		this.businessDate = businessDate;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CLOSURE_START_TIME", nullable = false, length = 19)
	public Date getClosureStartTime() {
		return this.closureStartTime;
	}

	public void setClosureStartTime(Date closureStartTime) {
		this.closureStartTime = closureStartTime;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CLOSURE_END_TIME", length = 19)
	public Date getClosureEndTime() {
		return this.closureEndTime;
	}

	public void setClosureEndTime(Date closureEndTime) {
		this.closureEndTime = closureEndTime;
	}

	@Column(name = "LAST_ORDER_ID")
	public Integer getLastOrderId() {
		return this.lastOrderId;
	}

	public void setLastOrderId(Integer lastOrderId) {
		this.lastOrderId = lastOrderId;
	}

	@Column(name = "START_ORDER_ID")
	public Integer getStartOrderId() {
		return this.startOrderId;
	}

	public void setStartOrderId(Integer startOrderId) {
		this.startOrderId = startOrderId;
	}

	@Column(name = "EMPLOYEE_ID", nullable = false)
	public int getEmployeeId() {
		return this.employeeId;
	}

	public void setEmployeeId(int employeeId) {
		this.employeeId = employeeId;
	}

	@Column(name = "RECONCILIATION_STATUS", length = 30)
	public String getReconciliationStatus() {
		return this.reconciliationStatus;
	}

	public void setReconciliationStatus(String reconciliationStatus) {
		this.reconciliationStatus = reconciliationStatus;
	}

	@Column(name = "CLOSURE_COMMENT", length = 500)
	public String getClosureComment() {
		return this.closureComment;
	}

	public void setClosureComment(String closureComment) {
		this.closureComment = closureComment;
	}

	@Column(name = "CURRENT_STATUS", length = 30)
	public String getCurrentStatus() {
		return this.currentStatus;
	}

	public void setCurrentStatus(String currentStatus) {
		this.currentStatus = currentStatus;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "unitClosureDetails")
	public List<ClosureStatus> getClosureStatuses() {
		return this.closureStatuses;
	}

	public void setClosureStatuses(List<ClosureStatus> closureStatuses) {
		this.closureStatuses = closureStatuses;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "unitClosureDetails")
	public List<ClosurePaymentDetails> getClosurePaymentDetailses() {
		return this.closurePaymentDetailses;
	}

	public void setClosurePaymentDetailses(List<ClosurePaymentDetails> closurePaymentDetailses) {
		this.closurePaymentDetailses = closurePaymentDetailses;
	}

	@Column(name = "REPORT_STATUS")
	public String getReportStatus() {
		return reportStatus;
	}

	public void setReportStatus(String reportStatus) {
		this.reportStatus = reportStatus;
	}

	@Column(name = "IS_PNL_GENERATED" , nullable = true)
	public String getPnlGenerated() {
		return pnlGenerated;
	}

	public void setPnlGenerated(String pnlGenerated) {
		this.pnlGenerated = pnlGenerated;
	}

	@Column(name = "PNL_INSTANCE_ID" , nullable = true)
	public Integer getPnlInstanceId() {
		return pnlInstanceId;
	}

	public void setPnlInstanceId(Integer pnlInstanceId) {
		this.pnlInstanceId = pnlInstanceId;
	}

	
}
