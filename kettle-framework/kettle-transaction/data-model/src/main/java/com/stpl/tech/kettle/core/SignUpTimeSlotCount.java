package com.stpl.tech.kettle.core;

public class SignUpTimeSlotCount {

    /**8:00 AM - 10:00 AM*/
    protected String slotOneName;
    protected Long slotOneCount;
    /**12:00 PM - 2:00 PM*/
    protected String slotTwoName;
    protected Long slotTwoCount;
    /**2:00 PM - 4:00 PM*/
    protected String slotThreeName;
    protected Long slotThreeCount;
    /**4:00 PM - 6:00 PM*/
    protected String slotFourName;
    protected Long slotFourCount;
    /**6:00 PM - 8:00 PM*/
    protected String slotFiveName;
    protected Long slotFiveCount;
    /**8:00 PM - 11:00 PM*/
    protected String slotSixName;
    protected Long slotSixCount;

    public String getSlotOneName() {
        return slotOneName;
    }

    public void setSlotOneName(String slotOneName) {
        this.slotOneName = slotOneName;
    }

    public Long getSlotOneCount() {
        return slotOneCount;
    }

    public void setSlotOneCount(Long slotOneCount) {
        this.slotOneCount = slotOneCount;
    }

    public String getSlotTwoName() {
        return slotTwoName;
    }

    public void setSlotTwoName(String slotTwoName) {
        this.slotTwoName = slotTwoName;
    }

    public Long getSlotTwoCount() {
        return slotTwoCount;
    }

    public void setSlotTwoCount(Long slotTwoCount) {
        this.slotTwoCount = slotTwoCount;
    }

    public String getSlotThreeName() {
        return slotThreeName;
    }

    public void setSlotThreeName(String slotThreeName) {
        this.slotThreeName = slotThreeName;
    }

    public Long getSlotThreeCount() {
        return slotThreeCount;
    }

    public void setSlotThreeCount(Long slotThreeCount) {
        this.slotThreeCount = slotThreeCount;
    }

    public String getSlotFourName() {
        return slotFourName;
    }

    public void setSlotFourName(String slotFourName) {
        this.slotFourName = slotFourName;
    }

    public Long getSlotFourCount() {
        return slotFourCount;
    }

    public void setSlotFourCount(Long slotFourCount) {
        this.slotFourCount = slotFourCount;
    }

    public String getSlotFiveName() {
        return slotFiveName;
    }

    public void setSlotFiveName(String slotFiveName) {
        this.slotFiveName = slotFiveName;
    }

    public Long getSlotFiveCount() {
        return slotFiveCount;
    }

    public void setSlotFiveCount(Long slotFiveCount) {
        this.slotFiveCount = slotFiveCount;
    }

    public String getSlotSixName() {
        return slotSixName;
    }

    public void setSlotSixName(String slotSixName) {
        this.slotSixName = slotSixName;
    }

    public Long getSlotSixCount() {
        return slotSixCount;
    }

    public void setSlotSixCount(Long slotSixCount) {
        this.slotSixCount = slotSixCount;
    }
}