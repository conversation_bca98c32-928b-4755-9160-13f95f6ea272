/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are private by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */
package com.stpl.tech.kettle.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "UNIT_EXPENSE_DETAIL")
public class UnitExpenseDetail {

	private int unitExpenseDetailId;
	private int unitId;
	private String unitName;
	private String type;
	private int year;
	private int iterationNumber;
	private int expenseUpdateEventId;
	private String status;
	private String updatedBy;
	private Date lastUpdateTime;
	private BigDecimal manpower;
	private BigDecimal netSalesAmount;
	private BigDecimal gmvAmount;
	private Integer netTickets;
	private Integer totalTickets;
	private BigDecimal cogs;
	private BigDecimal consumablesAndUtilities;
	private BigDecimal marketingAndSampling;
	private BigDecimal unsatifiedCustomerCost;
	private BigDecimal ppeCost;
	private BigDecimal employeeMeal;
	private BigDecimal wastageAndExpired;
	private BigDecimal deliveryCost;
	private BigDecimal electricity;
	private BigDecimal water;
	private BigDecimal rentDG;
	private BigDecimal chargesDG;
	private BigDecimal creditCardCharges;
	private BigDecimal amexCardCharges;
	private BigDecimal sodexoCharges;
	private BigDecimal tktRestaurantCharges;
	private BigDecimal channelPartnerCharges;
	private BigDecimal scmRental;
	private BigDecimal edcMachine;
	private BigDecimal freightOutward;
	private BigDecimal convenyance;
	private BigDecimal staffWelfare;
	private BigDecimal changeCommission;
	private BigDecimal courier;
	private BigDecimal printingAndStationery;
	private BigDecimal miscExp;
	private BigDecimal parkingCharges;
	private BigDecimal cleaningCharges;
	private BigDecimal newspaper;
	private BigDecimal rent;
	private BigDecimal fixedRent;
	private BigDecimal rentPercentage;
	private BigDecimal camCharges;
	private BigDecimal internet;
	private BigDecimal telephone;
	private BigDecimal opsCostTotal;
	private BigDecimal opsCostPrecentage;
	private BigDecimal kitchenCostTotal;
	private BigDecimal kitchenCostPercentage;
	private BigDecimal repairAndMaintenanceMinor;
	private BigDecimal repairAndMaintenanceMajor;
	private BigDecimal manualAdjustments;
	private BigDecimal totalCost;
	private BigDecimal ebitdaPercentage;
	private String comments;
	private BigDecimal customerCareCost;
	private BigDecimal maintenanceTeamCost;
	private BigDecimal trainingTeamCost;
	private BigDecimal itTeamCost;
	private BigDecimal consumablesStationary;
	private BigDecimal consumablesUniform;
	private BigDecimal consumablesEquipment;
	private BigDecimal consumablesCutlery;
	private BigDecimal msp;
	private BigDecimal systemRent;
	private BigDecimal insurance;
    private BigDecimal paytmCharges;
    private BigDecimal mobikwikCharges;
    private BigDecimal freeChargeCharges;
    private BigDecimal deliveryTotalTickets;
    private BigDecimal deliveryNetTickets;
    private BigDecimal deliveryGMV;
    private BigDecimal deliverySales;
    private BigDecimal deliveryCOGS;
    private BigDecimal deliveryUnsatisfiedCustomerCost;
	private BigDecimal deliveryPPECost;
    private BigDecimal deliverySampleingAndMarketingCost;
    private BigDecimal deliveryPaytmCahrges;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "UNIT_EXPENSE_DETAIL_ID", unique = true, nullable = false)
	public int getUnitExpenseDetailId() {
		return unitExpenseDetailId;
	}

	public void setUnitExpenseDetailId(int unitExpenseDetailId) {
		this.unitExpenseDetailId = unitExpenseDetailId;
	}

	@Column(name = "UNIT_ID", nullable = false)
	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	@Column(name = "ENTRY_TYPE", nullable = false)
	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	@Column(name = "ENTRY_YEAR", nullable = false)
	public int getYear() {
		return year;
	}

	public void setYear(int year) {
		this.year = year;
	}

	@Column(name = "ITERATION_NUMBER", nullable = false)
	public int getIterationNumber() {
		return iterationNumber;
	}

	public void setIterationNumber(int iterationNumber) {
		this.iterationNumber = iterationNumber;
	}

	@Column(name = "EXPENSE_UPDATE_EVENT_ID", nullable = true)
	public int getExpenseUpdateEventId() {
		return expenseUpdateEventId;
	}

	public void setExpenseUpdateEventId(int expenseUpdateEventId) {
		this.expenseUpdateEventId = expenseUpdateEventId;
	}

	@Column(name = "EXPENSE_STATUS", nullable = false)
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@Column(name = "UPDATED_BY", nullable = false)
	public String getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(String updatedBy) {
		this.updatedBy = updatedBy;
	}

	@Column(name = "LAST_UPDATE_TIME", nullable = false)
	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	@Column(name = "MANPOWER", precision = 10)
	public BigDecimal getManpower() {
		return manpower;
	}

	public void setManpower(BigDecimal manpower) {
		this.manpower = manpower;
	}

	@Column(name = "NET_SALES_AMOUNT", precision = 10)
	public BigDecimal getNetSalesAmount() {
		return netSalesAmount;
	}

	public void setNetSalesAmount(BigDecimal revenue) {
		this.netSalesAmount = revenue;
	}

	@Column(name = "COGS", precision = 10)
	public BigDecimal getCogs() {
		return cogs;
	}

	public void setCogs(BigDecimal cogs) {
		this.cogs = cogs;
	}

	@Column(name = "CONSUMABLES_AND_UTILITIES", precision = 10)
	public BigDecimal getConsumablesAndUtilities() {
		return consumablesAndUtilities;
	}

	public void setConsumablesAndUtilities(BigDecimal consumablesAndUtilities) {
		this.consumablesAndUtilities = consumablesAndUtilities;
	}

	@Column(name = "EMPLOYEE_MEAL", precision = 10)
	public BigDecimal getEmployeeMeal() {
		return employeeMeal;
	}

	public void setEmployeeMeal(BigDecimal employeeMeal) {
		this.employeeMeal = employeeMeal;
	}

	@Column(name = "WASTAGE_AND_EXPIRED", precision = 10)
	public BigDecimal getWastageAndExpired() {
		return wastageAndExpired;
	}

	public void setWastageAndExpired(BigDecimal wastageAndExpired) {
		this.wastageAndExpired = wastageAndExpired;
	}

	@Column(name = "DELIVERY_COST", precision = 10)
	public BigDecimal getDeliveryCost() {
		return deliveryCost;
	}

	public void setDeliveryCost(BigDecimal deliveryCost) {
		this.deliveryCost = deliveryCost;
	}

	@Column(name = "ELECTRICITY", precision = 10)
	public BigDecimal getElectricity() {
		return electricity;
	}

	public void setElectricity(BigDecimal electricity) {
		this.electricity = electricity;
	}

	@Column(name = "WATER", precision = 10)
	public BigDecimal getWater() {
		return water;
	}

	public void setWater(BigDecimal water) {
		this.water = water;
	}

	@Column(name = "DG_RENT", precision = 10)
	public BigDecimal getRentDG() {
		return rentDG;
	}

	public void setRentDG(BigDecimal rentDG) {
		this.rentDG = rentDG;
	}

	@Column(name = "DG_CHARGES", precision = 10)
	public BigDecimal getChargesDG() {
		return chargesDG;
	}

	public void setChargesDG(BigDecimal chargesDG) {
		this.chargesDG = chargesDG;
	}

	@Column(name = "CREDIT_CARD_CHARGES", precision = 10)
	public BigDecimal getCreditCardCharges() {
		return creditCardCharges;
	}

	public void setCreditCardCharges(BigDecimal creditCardCharges) {
		this.creditCardCharges = creditCardCharges;
	}

	@Column(name = "CHANNEL_PARTNER", precision = 10)
	public BigDecimal getChannelPartnerCharges() {
		return channelPartnerCharges;
	}

	public void setChannelPartnerCharges(BigDecimal channelPartner) {
		this.channelPartnerCharges = channelPartner;
	}

	@Column(name = "SCM_RENTEL", precision = 10)
	public BigDecimal getScmRental() {
		return scmRental;
	}

	public void setScmRental(BigDecimal scmRental) {
		this.scmRental = scmRental;
	}

	@Column(name = "EDC_MACHINE", precision = 10)
	public BigDecimal getEdcMachine() {
		return edcMachine;
	}

	public void setEdcMachine(BigDecimal edcMachine) {
		this.edcMachine = edcMachine;
	}

	@Column(name = "FREIGHT_OUTWARD", precision = 10)
	public BigDecimal getFreightOutward() {
		return freightOutward;
	}

	public void setFreightOutward(BigDecimal freightOutward) {
		this.freightOutward = freightOutward;
	}

	@Column(name = "CONVENYANCE", precision = 10)
	public BigDecimal getConvenyance() {
		return convenyance;
	}

	public void setConvenyance(BigDecimal convenyance) {
		this.convenyance = convenyance;
	}

	@Column(name = "STAFF_WELFARE", precision = 10)
	public BigDecimal getStaffWelfare() {
		return staffWelfare;
	}

	public void setStaffWelfare(BigDecimal staffWelfare) {
		this.staffWelfare = staffWelfare;
	}

	@Column(name = "CHANGE_COMMISSION", precision = 10)
	public BigDecimal getChangeCommission() {
		return changeCommission;
	}

	public void setChangeCommission(BigDecimal changeCommission) {
		this.changeCommission = changeCommission;
	}

	@Column(name = "COURIER", precision = 10)
	public BigDecimal getCourier() {
		return courier;
	}

	public void setCourier(BigDecimal courier) {
		this.courier = courier;
	}

	@Column(name = "PRINTING_AND_STATIONARY", precision = 10)
	public BigDecimal getPrintingAndStationery() {
		return printingAndStationery;
	}

	public void setPrintingAndStationery(BigDecimal printingAndStationery) {
		this.printingAndStationery = printingAndStationery;
	}

	@Column(name = "MISC_EXP", precision = 10)
	public BigDecimal getMiscExp() {
		return miscExp;
	}

	public void setMiscExp(BigDecimal miscExp) {
		this.miscExp = miscExp;
	}

	@Column(name = "PARKING_CHARGES", precision = 10)
	public BigDecimal getParkingCharges() {
		return parkingCharges;
	}

	public void setParkingCharges(BigDecimal parkingCharges) {
		this.parkingCharges = parkingCharges;
	}

	@Column(name = "CLEANING_CHARGES", precision = 10)
	public BigDecimal getCleaningCharges() {
		return cleaningCharges;
	}

	public void setCleaningCharges(BigDecimal cleaningCharges) {
		this.cleaningCharges = cleaningCharges;
	}

	@Column(name = "NEWSPAPER", precision = 10)
	public BigDecimal getNewspaper() {
		return newspaper;
	}

	public void setNewspaper(BigDecimal newspaper) {
		this.newspaper = newspaper;
	}

	@Column(name = "RENT", precision = 10)
	public BigDecimal getRent() {
		return rent;
	}

	public void setRent(BigDecimal rent) {
		this.rent = rent;
	}

	@Column(name = "FIXED_RENT", precision = 10)
	public BigDecimal getFixedRent() {
		return fixedRent;
	}

	public void setFixedRent(BigDecimal fixedRent) {
		this.fixedRent = fixedRent;
	}

	@Column(name = "RENT_PERCENTAGE", precision = 10)
	public BigDecimal getRentPercentage() {
		return rentPercentage;
	}

	public void setRentPercentage(BigDecimal rentPercentage) {
		this.rentPercentage = rentPercentage;
	}

	@Column(name = "CAM_CAHRGES", precision = 10)
	public BigDecimal getCamCharges() {
		return camCharges;
	}

	public void setCamCharges(BigDecimal camCharges) {
		this.camCharges = camCharges;
	}

	@Column(name = "INTERNET", precision = 10)
	public BigDecimal getInternet() {
		return internet;
	}

	public void setInternet(BigDecimal internet) {
		this.internet = internet;
	}

	@Column(name = "TELEPHONE", precision = 10)
	public BigDecimal getTelephone() {
		return telephone;
	}

	public void setTelephone(BigDecimal telephone) {
		this.telephone = telephone;
	}

	@Column(name = "OPS_COST_TOTAL", precision = 10)
	public BigDecimal getOpsCostTotal() {
		return opsCostTotal;
	}

	public void setOpsCostTotal(BigDecimal opsCost) {
		this.opsCostTotal = opsCost;
	}

	@Column(name = "OPS_COST_PERCENTAGE", precision = 10)
	public BigDecimal getOpsCostPrecentage() {
		return opsCostPrecentage;
	}

	public void setOpsCostPrecentage(BigDecimal opsCostPrecentage) {
		this.opsCostPrecentage = opsCostPrecentage;
	}

	@Column(name = "KITCHEN_COST_PERCENTAGE", precision = 10)
	public BigDecimal getKitchenCostPercentage() {
		return kitchenCostPercentage;
	}

	public void setKitchenCostPercentage(BigDecimal kitchenCostPercentage) {
		this.kitchenCostPercentage = kitchenCostPercentage;
	}

	@Column(name = "KITCHEN_COST_TOTAL", precision = 10)
	public BigDecimal getKitchenCostTotal() {
		return kitchenCostTotal;
	}

	public void setKitchenCostTotal(BigDecimal kitchenCost) {
		this.kitchenCostTotal = kitchenCost;
	}

	@Column(name = "REPAIR_AND_MAINTENANCE_MINOR", precision = 10)
	public BigDecimal getRepairAndMaintenanceMinor() {
		return repairAndMaintenanceMinor;
	}

	public void setRepairAndMaintenanceMinor(BigDecimal repairAndMaintenanceMinor) {
		this.repairAndMaintenanceMinor = repairAndMaintenanceMinor;
	}

	@Column(name = "REPAIR_AND_MAINTENANCE_MAJOR", precision = 10)
	public BigDecimal getRepairAndMaintenanceMajor() {
		return repairAndMaintenanceMajor;
	}

	public void setRepairAndMaintenanceMajor(BigDecimal repairAndMaintenanceMajor) {
		this.repairAndMaintenanceMajor = repairAndMaintenanceMajor;
	}

	@Column(name = "GMV_AMOUNT", precision = 10)
	public BigDecimal getGmvAmount() {
		return gmvAmount;
	}

	public void setGmvAmount(BigDecimal gmvAmount) {
		this.gmvAmount = gmvAmount;
	}

	@Column(name = "NET_TICKETS")
	public Integer getNetTickets() {
		return netTickets;
	}

	public void setNetTickets(Integer netTickets) {
		this.netTickets = netTickets;
	}

	@Column(name = "TOTAL_TICKETS")
	public Integer getTotalTickets() {
		return totalTickets;
	}

	public void setTotalTickets(Integer totalTickets) {
		this.totalTickets = totalTickets;
	}

	@Column(name = "MARKETING_AND_SAMPLING", precision = 10)
	public BigDecimal getMarketingAndSampling() {
		return marketingAndSampling;
	}

	public void setMarketingAndSampling(BigDecimal marketingAndSampling) {
		this.marketingAndSampling = marketingAndSampling;
	}

	@Column(name = "UNSATISFIED_CUSTOMER_COST", precision = 10)
	public BigDecimal getUnsatifiedCustomerCost() {
		return unsatifiedCustomerCost;
	}

	public void setUnsatifiedCustomerCost(BigDecimal unsatifiedCustomerCost) {
		this.unsatifiedCustomerCost = unsatifiedCustomerCost;
	}

	@Column(name = "PPE_COST", precision = 10)
	public BigDecimal getPPECost() {
		return this.ppeCost;
	}

	public void setPPECost(BigDecimal ppeCost) {
		this.ppeCost = ppeCost;
	}

	@Column(name = "AMEX_CARD_CHARGES", precision = 10)
	public BigDecimal getAmexCardCharges() {
		return amexCardCharges;
	}

	public void setAmexCardCharges(BigDecimal amexCardCharges) {
		this.amexCardCharges = amexCardCharges;
	}

	@Column(name = "SODEXO_CHARGES", precision = 10)
	public BigDecimal getSodexoCharges() {
		return sodexoCharges;
	}

	public void setSodexoCharges(BigDecimal sodexoCharges) {
		this.sodexoCharges = sodexoCharges;
	}

	@Column(name = "TKT_RESTAURANT_CHARGES", precision = 10)
	public BigDecimal getTktRestaurantCharges() {
		return tktRestaurantCharges;
	}

	public void setTktRestaurantCharges(BigDecimal tktRestaurantCharges) {
		this.tktRestaurantCharges = tktRestaurantCharges;
	}

	@Column(name = "MANUAL_ADJUSTMENT", precision = 10)
	public BigDecimal getManualAdjustments() {
		return manualAdjustments;
	}

	public void setManualAdjustments(BigDecimal manualAdjustments) {
		this.manualAdjustments = manualAdjustments;
	}

	@Column(name = "COMMENTS", length = 500)
	public String getComments() {
		return comments;
	}

	public void setComments(String comments) {
		this.comments = comments;
	}

	@Column(name = "UNIT_NAME", length = 100)
	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}

	@Column(name = "TOTAL_COST", precision = 10)
	public BigDecimal getTotalCost() {
		return totalCost;
	}

	public void setTotalCost(BigDecimal totalCost) {
		this.totalCost = totalCost;
	}

	@Column(name = "EBIDTA_PERCENTAGE", precision = 10)
	public BigDecimal getEbitdaPercentage() {
		return ebitdaPercentage;
	}

	public void setEbitdaPercentage(BigDecimal ebitdaPercentage) {
		this.ebitdaPercentage = ebitdaPercentage;
	}

	@Column(name = "CUSTOMER_CARE_COST", precision = 10)
	public BigDecimal getCustomerCareCost() {
		return customerCareCost;
	}

	public void setCustomerCareCost(BigDecimal customerCareCost) {
		this.customerCareCost = customerCareCost;
	}

	@Column(name = "MAINTENANCE_TEAM_COST", precision = 10)
	public BigDecimal getMaintenanceTeamCost() {
		return maintenanceTeamCost;
	}

	public void setMaintenanceTeamCost(BigDecimal maintenanceTeamCost) {
		this.maintenanceTeamCost = maintenanceTeamCost;
	}

	@Column(name = "TRAINING_TEAM_COST", precision = 10)
	public BigDecimal getTrainingTeamCost() {
		return trainingTeamCost;
	}

	public void setTrainingTeamCost(BigDecimal trainingTeamCost) {
		this.trainingTeamCost = trainingTeamCost;
	}

	@Column(name = "IT_TEAM_COST", precision = 10)
	public BigDecimal getItTeamCost() {
		return itTeamCost;
	}

	public void setItTeamCost(BigDecimal itTeamCost) {
		this.itTeamCost = itTeamCost;
	}

	@Column(name = "CONSUMABLES_STATIONARY", precision = 10)
	public BigDecimal getConsumablesStationary() {
		return consumablesStationary;
	}

	public void setConsumablesStationary(BigDecimal consumablesStationary) {
		this.consumablesStationary = consumablesStationary;
	}

	@Column(name = "CONSUMABLES_UNIFORM", precision = 10)
	public BigDecimal getConsumablesUniform() {
		return consumablesUniform;
	}

	public void setConsumablesUniform(BigDecimal consumablesUniform) {
		this.consumablesUniform = consumablesUniform;
	}

	@Column(name = "CONSUMABLES_EQUIPMENT", precision = 10)
	public BigDecimal getConsumablesEquipment() {
		return consumablesEquipment;
	}

	public void setConsumablesEquipment(BigDecimal consumablesEquipment) {
		this.consumablesEquipment = consumablesEquipment;
	}

	@Column(name = "CONSUMABLES_CUTLERY", precision = 10)
	public BigDecimal getConsumablesCutlery() {
		return consumablesCutlery;
	}

	public void setConsumablesCutlery(BigDecimal consumablesCutlery) {
		this.consumablesCutlery = consumablesCutlery;
	}

	@Column(name = "MSP", precision = 10)
	public BigDecimal getMsp() {
		return msp;
	}

	public void setMsp(BigDecimal msp) {
		this.msp = msp;
	}

	@Column(name = "SYSTEM_RENT", precision = 10)
	public BigDecimal getSystemRent() {
		return systemRent;
	}

	public void setSystemRent(BigDecimal systemRent) {
		this.systemRent = systemRent;
	}

	@Column(name = "INSURANCE", precision = 10)
	public BigDecimal getInsurance() {
		return insurance;
	}

	public void setInsurance(BigDecimal insurance) {
		this.insurance = insurance;
	}

	@Column(name = "PAYTM_CHARGES", precision = 10)
	public BigDecimal getPaytmCharges() {
		return paytmCharges;
	}

	public void setPaytmCharges(BigDecimal paytmCharges) {
		this.paytmCharges = paytmCharges;
	}

	@Column(name = "MOBIKWIK_CHARGES", precision = 10)
	public BigDecimal getMobikwikCharges() {
		return mobikwikCharges;
	}

	public void setMobikwikCharges(BigDecimal mobikwikCharges) {
		this.mobikwikCharges = mobikwikCharges;
	}

	@Column(name = "FREE_CHARGE_CHARGES", precision = 10)
	public BigDecimal getFreeChargeCharges() {
		return freeChargeCharges;
	}

	public void setFreeChargeCharges(BigDecimal freeChargeCharges) {
		this.freeChargeCharges = freeChargeCharges;
	}

	@Column(name = "DEL_TOTAL_TICKETS", precision = 10)
	public BigDecimal getDeliveryTotalTickets() {
		return deliveryTotalTickets;
	}

	public void setDeliveryTotalTickets(BigDecimal deliveryTotalTickets) {
		this.deliveryTotalTickets = deliveryTotalTickets;
	}

	@Column(name = "DEL_NET_TICKETS", precision = 10)
	public BigDecimal getDeliveryNetTickets() {
		return deliveryNetTickets;
	}

	public void setDeliveryNetTickets(BigDecimal deliveryNetTickets) {
		this.deliveryNetTickets = deliveryNetTickets;
	}

	@Column(name = "DEL_GMV", precision = 10)
	public BigDecimal getDeliveryGMV() {
		return deliveryGMV;
	}

	public void setDeliveryGMV(BigDecimal deliveryGMV) {
		this.deliveryGMV = deliveryGMV;
	}

	@Column(name = "DEL_SALES", precision = 10)
	public BigDecimal getDeliverySales() {
		return deliverySales;
	}

	public void setDeliverySales(BigDecimal deliverySales) {
		this.deliverySales = deliverySales;
	}

	@Column(name = "DEL_COGS", precision = 10)
	public BigDecimal getDeliveryCOGS() {
		return deliveryCOGS;
	}

	public void setDeliveryCOGS(BigDecimal deliveryCOGS) {
		this.deliveryCOGS = deliveryCOGS;
	}

	@Column(name = "DEL_UNSATISFIED_CUSTOMER_COST", precision = 10)
	public BigDecimal getDeliveryUnsatisfiedCustomerCost() {
		return deliveryUnsatisfiedCustomerCost;
	}

	public void setDeliveryUnsatisfiedCustomerCost(BigDecimal deliveryUnsatisfiedCustomerCost) {
		this.deliveryUnsatisfiedCustomerCost = deliveryUnsatisfiedCustomerCost;
	}

	@Column(name = "DEL_PPE_COST", precision = 10)
	public BigDecimal getDeliveryPPECost() {
		return this.deliveryPPECost;
	}

	public void setDeliveryPPECost(BigDecimal deliveryPPECost) {
		this.deliveryPPECost = deliveryPPECost;
	}

	@Column(name = "DEL_MARKETING_AND_SAMPLING", precision = 10)
	public BigDecimal getDeliverySampleingAndMarketingCost() {
		return deliverySampleingAndMarketingCost;
	}

	public void setDeliverySampleingAndMarketingCost(BigDecimal deliverySampleingAndMarketingCost) {
		this.deliverySampleingAndMarketingCost = deliverySampleingAndMarketingCost;
	}

	@Column(name = "DEL_PAYTM_CHARGES", precision = 10)
	public BigDecimal getDeliveryPaytmCahrges() {
		return deliveryPaytmCahrges;
	}

	public void setDeliveryPaytmCahrges(BigDecimal deliveryPaytmCahrges) {
		this.deliveryPaytmCahrges = deliveryPaytmCahrges;
	}

	
}
