package com.stpl.tech.kettle.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

@Entity
@Table(name = "STANDALONE_TRANSACTION_DETAIL")
public class StandaloneTransactionDetail implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 4876301954521731219L;

	private Integer transactionDetailId;
	private String accounId;
	private String eventType;
	private String paymentId;
	private BigDecimal paymentAmount;
	private String paymentCurrency;
	private String paymentStatus;
	private String paymentInvoiceId;
	private String internationalPayment;
	private String paymentMethod;
	private BigDecimal paymentRefundAmount;
	private String paymentRefundStatus;
	private String paymentCaptured;
	private String paymentDescription;
	private String paymentCardId;
	private String paymentBankName;
	private String paymentWallet;
	private String paymentVpaHandle;
	private String paymentEmailId;
	private String paymentContactNumber;
	private String paymentNotesEmailId;
	private String paymentNotesContactNumber;
	private String paymentCustomerName;
	private BigDecimal paymentTransactionFee;
	private BigDecimal paymentTransactionFeeTax;
	private String paymentErrorCode;
	private String paymentErrorDescription;
	private Date paymentTime;
	private String orderId;
	private BigDecimal orderAmount;
	private BigDecimal orderAmountPaid;
	private BigDecimal orderAmountDue;
	private String orderCurrency;
	private String orderReceipt;
	private String orderOfferId;
	private String orderStatus;
	private Integer orderAttempts;
	private Date orderCreationTime;
	private Date requestTime;
	
	private Date lastUpdateTime;
	private String campaignId;
	private String campaignDescription;
	private String currentStatus;
	private String smsType;
	private String emailType;
	private String notified;
	
	
	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "TRANSACTION_DETAIL_ID", unique = true, nullable = false)
	public Integer getTransactionDetailId() {
		return transactionDetailId;
	}

	public void setTransactionDetailId(Integer orderPaymentDetailId) {
		this.transactionDetailId = orderPaymentDetailId;
	}

	@Column(name = "ACCOUNT_ID")
	public String getAccounId() {
		return accounId;
	}

	public void setAccounId(String accounId) {
		this.accounId = accounId;
	}

	@Column(name = "EVENT_TYPE")
	public String getEventType() {
		return eventType;
	}

	public void setEventType(String eventType) {
		this.eventType = eventType;
	}

	@Column(name = "PAYMENT_ID")
	public String getPaymentId() {
		return paymentId;
	}

	public void setPaymentId(String paymentId) {
		this.paymentId = paymentId;
	}

	@Column(name = "PAYMENT_AMOUNT", precision = 10)
	public BigDecimal getPaymentAmount() {
		return paymentAmount;
	}

	public void setPaymentAmount(BigDecimal paymentAmount) {
		this.paymentAmount = paymentAmount;
	}

	@Column(name = "PAYMENT_CURRENCY")
	public String getPaymentCurrency() {
		return paymentCurrency;
	}

	public void setPaymentCurrency(String paymentCurrency) {
		this.paymentCurrency = paymentCurrency;
	}

	@Column(name = "PAYMENT_STATUS")
	public String getPaymentStatus() {
		return paymentStatus;
	}

	public void setPaymentStatus(String paymentStatus) {
		this.paymentStatus = paymentStatus;
	}

	@Column(name = "PAYMENT_INVOICE_ID")
	public String getPaymentInvoiceId() {
		return paymentInvoiceId;
	}

	public void setPaymentInvoiceId(String paymentInvoiceId) {
		this.paymentInvoiceId = paymentInvoiceId;
	}

	@Column(name = "PAYMENT_INTERNATIONAL")
	public String getInternationalPayment() {
		return internationalPayment;
	}

	public void setInternationalPayment(String internationalPayment) {
		this.internationalPayment = internationalPayment;
	}

	@Column(name = "PAYMENT_METHOD")
	public String getPaymentMethod() {
		return paymentMethod;
	}

	public void setPaymentMethod(String paymentMethod) {
		this.paymentMethod = paymentMethod;
	}

	@Column(name = "PAYMENT_REFUND_AMOUNT", precision = 10)
	public BigDecimal getPaymentRefundAmount() {
		return paymentRefundAmount;
	}

	public void setPaymentRefundAmount(BigDecimal paymentRefundAmount) {
		this.paymentRefundAmount = paymentRefundAmount;
	}

	@Column(name = "PAYMENT_REFUND_STATUS")
	public String getPaymentRefundStatus() {
		return paymentRefundStatus;
	}

	public void setPaymentRefundStatus(String paymentRefundStatus) {
		this.paymentRefundStatus = paymentRefundStatus;
	}

	@Column(name = "PAYMENT_CAPTURED")
	public String getPaymentCaptured() {
		return paymentCaptured;
	}

	public void setPaymentCaptured(String paymentCaptured) {
		this.paymentCaptured = paymentCaptured;
	}

	@Column(name = "PAYMENT_DESCRIPTION")
	public String getPaymentDescription() {
		return paymentDescription;
	}

	public void setPaymentDescription(String paymentDescription) {
		this.paymentDescription = paymentDescription;
	}

	@Column(name = "PAYMENT_CARD_ID")
	public String getPaymentCardId() {
		return paymentCardId;
	}

	public void setPaymentCardId(String paymentCardId) {
		this.paymentCardId = paymentCardId;
	}

	@Column(name = "PAYMENT_BANK_NAME")
	public String getPaymentBankName() {
		return paymentBankName;
	}

	public void setPaymentBankName(String paymentBankName) {
		this.paymentBankName = paymentBankName;
	}

	@Column(name = "PAYMENT_WALLET")
	public String getPaymentWallet() {
		return paymentWallet;
	}

	public void setPaymentWallet(String paymentWallet) {
		this.paymentWallet = paymentWallet;
	}

	@Column(name = "PAYMENT_VPA_HANDLE")
	public String getPaymentVpaHandle() {
		return paymentVpaHandle;
	}

	public void setPaymentVpaHandle(String paymentVpaHandle) {
		this.paymentVpaHandle = paymentVpaHandle;
	}

	@Column(name = "PAYMENT_EMAIL_ID")
	public String getPaymentEmailId() {
		return paymentEmailId;
	}

	public void setPaymentEmailId(String paymentEmailId) {
		this.paymentEmailId = paymentEmailId;
	}

	@Column(name = "PAYMENT_CONTACT_NUMBER")
	public String getPaymentContactNumber() {
		return paymentContactNumber;
	}

	public void setPaymentContactNumber(String paymentContactNumber) {
		this.paymentContactNumber = paymentContactNumber;
	}

	@Column(name = "PAYMENT_NOTES_EMAIL_ID")
	public String getPaymentNotesEmailId() {
		return paymentNotesEmailId;
	}

	public void setPaymentNotesEmailId(String paymentNotesEmailId) {
		this.paymentNotesEmailId = paymentNotesEmailId;
	}

	@Column(name = "PAYMENT_NOTES_CONTACT_NUMBER")
	public String getPaymentNotesContactNumber() {
		return paymentNotesContactNumber;
	}

	public void setPaymentNotesContactNumber(String paymentNotesContactNumber) {
		this.paymentNotesContactNumber = paymentNotesContactNumber;
	}

	@Column(name = "PAYMENT_CUSTOMER_NAME")
	public String getPaymentCustomerName() {
		return paymentCustomerName;
	}

	public void setPaymentCustomerName(String paymentCustomerName) {
		this.paymentCustomerName = paymentCustomerName;
	}

	@Column(name = "PAYMENT_TRANSACTION_FEES", precision = 10)
	public BigDecimal getPaymentTransactionFee() {
		return paymentTransactionFee;
	}

	public void setPaymentTransactionFee(BigDecimal paymentTransactionFee) {
		this.paymentTransactionFee = paymentTransactionFee;
	}

	@Column(name = "PAYMENT_TRANSACTION_FEES_TAX", precision = 10)
	public BigDecimal getPaymentTransactionFeeTax() {
		return paymentTransactionFeeTax;
	}

	public void setPaymentTransactionFeeTax(BigDecimal paymentTransactionFeeTax) {
		this.paymentTransactionFeeTax = paymentTransactionFeeTax;
	}

	@Column(name = "PAYMENT_ERROR_CODE")
	public String getPaymentErrorCode() {
		return paymentErrorCode;
	}

	public void setPaymentErrorCode(String paymentErrorCode) {
		this.paymentErrorCode = paymentErrorCode;
	}

	@Column(name = "PAYMENT_ERROR_DESCRIPTION")
	public String getPaymentErrorDescription() {
		return paymentErrorDescription;
	}

	public void setPaymentErrorDescription(String paymentErrorDescription) {
		this.paymentErrorDescription = paymentErrorDescription;
	}
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "PAYMENT_TIME", nullable = true, length = 19)
	public Date getPaymentTime() {
		return paymentTime;
	}

	public void setPaymentTime(Date paymentTime) {
		this.paymentTime = paymentTime;
	}

	@Column(name = "ORDER_ID")
	public String getOrderId() {
		return orderId;
	}

	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}

	@Column(name = "ORDER_AMOUNT", precision = 10)
	public BigDecimal getOrderAmount() {
		return orderAmount;
	}

	public void setOrderAmount(BigDecimal orderAmount) {
		this.orderAmount = orderAmount;
	}

	@Column(name = "ORDER_AMOUNT_PAID", precision = 10)
	public BigDecimal getOrderAmountPaid() {
		return orderAmountPaid;
	}

	public void setOrderAmountPaid(BigDecimal orderAmountPaid) {
		this.orderAmountPaid = orderAmountPaid;
	}

	@Column(name = "ORDER_AMOUNT_DUE", precision = 10)
	public BigDecimal getOrderAmountDue() {
		return orderAmountDue;
	}

	public void setOrderAmountDue(BigDecimal orderAmountDue) {
		this.orderAmountDue = orderAmountDue;
	}

	@Column(name = "ORDER_CURRENCY")
	public String getOrderCurrency() {
		return orderCurrency;
	}

	public void setOrderCurrency(String orderCurrency) {
		this.orderCurrency = orderCurrency;
	}

	@Column(name = "ORDER_RECEIPT")
	public String getOrderReceipt() {
		return orderReceipt;
	}

	public void setOrderReceipt(String orderReceipt) {
		this.orderReceipt = orderReceipt;
	}

	@Column(name = "ORDER_OFFER_ID")
	public String getOrderOfferId() {
		return orderOfferId;
	}

	public void setOrderOfferId(String orderOfferId) {
		this.orderOfferId = orderOfferId;
	}

	@Column(name = "ORDER_STATUS")
	public String getOrderStatus() {
		return orderStatus;
	}

	public void setOrderStatus(String orderStatus) {
		this.orderStatus = orderStatus;
	}

	@Column(name = "ORDER_ATTEMPTS")
	public Integer getOrderAttempts() {
		return orderAttempts;
	}

	public void setOrderAttempts(Integer orderAttempts) {
		this.orderAttempts = orderAttempts;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "ORDER_CREATION_TIME", nullable = true, length = 19)
	public Date getOrderCreationTime() {
		return orderCreationTime;
	}

	public void setOrderCreationTime(Date orderCreationTime) {
		this.orderCreationTime = orderCreationTime;
	}
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "REQUEST_TIME", nullable = true, length = 19)
	public Date getRequestTime() {
		return requestTime;
	}

	public void setRequestTime(Date requestTime) {
		this.requestTime = requestTime;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LAST_UPDATE_TIME", nullable = true, length = 19)
	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	@Column(name = "CAMPAIGN_ID")
	public String getCampaignId() {
		return campaignId;
	}

	public void setCampaignId(String campaignId) {
		this.campaignId = campaignId;
	}

	@Column(name = "CAMPAIGN_DESCRIPTION")
	public String getCampaignDescription() {
		return campaignDescription;
	}

	public void setCampaignDescription(String campaignDescription) {
		this.campaignDescription = campaignDescription;
	}

	@Column(name = "CURRENT_STATUS")
	public String getCurrentStatus() {
		return currentStatus;
	}

	public void setCurrentStatus(String currentStatus) {
		this.currentStatus = currentStatus;
	}

	@Column(name = "SMS_TYPE")
	public String getSmsType() {
		return smsType;
	}

	public void setSmsType(String smsType) {
		this.smsType = smsType;
	}

	@Column(name = "EMAIL_TYPE")
	public String getEmailType() {
		return emailType;
	}

	public void setEmailType(String emailType) {
		this.emailType = emailType;
	}

	@Column(name = "IS_NOTIFIED")
	public String getNotified() {
		return notified;
	}

	public void setNotified(String notified) {
		this.notified = notified;
	}

}
