package com.stpl.tech.kettle.data.dao.impl;

import com.google.common.base.Stopwatch;
import com.google.gson.Gson;
import com.stpl.tech.kettle.data.dao.FavChaiCustomizationDao;
import com.stpl.tech.kettle.data.model.CustomerFavChaiMapping;
import com.stpl.tech.kettle.data.model.FavChaiCustomizationDetail;
import com.stpl.tech.kettle.domain.model.OrderItemComposition;
import com.stpl.tech.kettle.domain.model.ProductSource;
import com.stpl.tech.kettle.domain.model.SaveCustomerFavChaiRequest;
import com.stpl.tech.kettle.domain.model.SelectedOrderItem;
import com.stpl.tech.master.core.external.cache.RecipeCache;
import com.stpl.tech.master.domain.model.ProductClassification;
import com.stpl.tech.master.recipe.model.IngredientProductDetail;
import com.stpl.tech.master.recipe.model.IngredientVariantDetail;
import com.stpl.tech.master.recipe.read.model.OptionDataVO;
import com.stpl.tech.util.AppConstants;
import org.codehaus.jackson.map.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Repository
public class FavChaiCustomizationDaoImpl extends AbstractDaoImpl implements FavChaiCustomizationDao {

    private static final Logger LOG = LoggerFactory.getLogger(FavChaiCustomizationDaoImpl.class);


    @Autowired
    private RecipeCache recipeCache;

    @Override
    public void saveFavChaiCustomizationDetail(SelectedOrderItem orderItemDetails, CustomerFavChaiMapping latestCustomerFavChaiMapping, SaveCustomerFavChaiRequest customerFavChaiRequest) {
        addAddons(latestCustomerFavChaiMapping, orderItemDetails.getComposition(),orderItemDetails,customerFavChaiRequest);
    }

    private void addAddons(CustomerFavChaiMapping customerFavChai, OrderItemComposition composition, SelectedOrderItem orderItemDetails, SaveCustomerFavChaiRequest customerFavChaiRequest) {
        Stopwatch watch = Stopwatch.createUnstarted();
        watch.start();
        customerFavChai.getFavChaiCustomizationDetailList().addAll(addAddon(customerFavChai,composition,orderItemDetails,customerFavChaiRequest));
        customerFavChai.getFavChaiCustomizationDetailList().addAll(addMandatoryAddon(customerFavChai,orderItemDetails));
        System.out.println("&&&&&&&&&&  - , Added Order Item Addons----------,"
                + watch.stop().elapsed(TimeUnit.MILLISECONDS));
        ObjectMapper mapper = new ObjectMapper();
        String value=null;
        try {
            value=mapper.writeValueAsString(customerFavChai);
        } catch (IOException e) {
//            e.printStackTrace();
        }
    }

    private List<FavChaiCustomizationDetail> addMandatoryAddon(CustomerFavChaiMapping customerFavChai, SelectedOrderItem orderItemDetails) {
        List<FavChaiCustomizationDetail> list = new ArrayList<>();
//        get Recipe Profile
        getRecipeProfile(customerFavChai,orderItemDetails);
        Collection<IngredientProductDetail> mandatoryAddons = recipeCache.getMandatoryAddons(customerFavChai.getProductId(),
                customerFavChai.getDimension(), customerFavChai.getRecipeProfile());
        LOG.info("**********Mandatory AddOns from recipeCache************* {}",new Gson().toJson(mandatoryAddons));
        if (mandatoryAddons != null && mandatoryAddons.size() > 0) {
            for (IngredientProductDetail detail : mandatoryAddons) {
                list.add(addAddon(customerFavChai, ProductSource.MENU, detail.getProduct().getClassification(), detail));
            }
        }
        LOG.info("******Mandatory AddOns List*********** {}", new Gson().toJson(list));
        return list;
    }

    private void getRecipeProfile(CustomerFavChaiMapping customerFavChai, SelectedOrderItem orderItem) {
        int productId = orderItem.getProductId();
        if (AppConstants.CHAAYOS_BRAND_ID == orderItem.getBrandId()) {
            Integer recipeId = null;
            try {
                recipeId = recipeCache.getUnitProductRecipeId(orderItem.getUnitId(), productId, orderItem.getDimension());
            } catch (Exception e) {
                // Do Nothing
            }
            if (recipeId != null && recipeId != orderItem.getRecipeId()) {
                customerFavChai.setRecipeId(recipeId);
                orderItem.setRecipeId(recipeId);
            } else {
                customerFavChai.setRecipeId(orderItem.getRecipeId());
            }
        } else {
            customerFavChai.setRecipeId(orderItem.getRecipeId());
        }
        LOG.info("Recipe id for favChai :{}",customerFavChai.getRecipeId());
        String profile = null;
        try {
            profile = recipeCache.getRecipe(customerFavChai.getRecipeId()).getProfile();
        } catch (Exception e) {
            profile = AppConstants.DEFAULT_RECIPE_PROFILE;
        }
        LOG.info("Recipe profile for favChai :{}",customerFavChai.getRecipeProfile());
        customerFavChai.setRecipeProfile(profile);
    }

    private List<FavChaiCustomizationDetail> addAddon(CustomerFavChaiMapping customerFavChai, OrderItemComposition composition, SelectedOrderItem orderItemDetails, SaveCustomerFavChaiRequest customerFavChaiRequest) {
        List<FavChaiCustomizationDetail> list = new ArrayList<>();

        if (composition != null) {
            if (composition.getVariants() != null && composition.getVariants().size() > 0) {
                for (IngredientVariantDetail detail : composition.getVariants()) {
                    list.add(addAddon(customerFavChai, ProductSource.SCM, ProductClassification.VARIANT, detail));
                }
            }
            if (composition.getProducts() != null && composition.getProducts().size() > 0) {
                for (IngredientProductDetail detail : composition.getProducts()) {
                    list.add(addAddon(customerFavChai, ProductSource.SCM, ProductClassification.PRODUCT_VARIANT, detail));
                }
            }
            if (composition.getAddons() != null && composition.getAddons().size() > 0) {
                for (IngredientProductDetail detail : composition.getAddons()) {
                    list.add(addAddon(customerFavChai, ProductSource.MENU, detail.getProduct().getClassification(), detail));
                }
            }

            if(composition.getOptions()!=null && composition.getOptions().size()>0){
                if(customerFavChaiRequest.getRecipeDetails().getOptions()!=null && customerFavChaiRequest.getRecipeDetails().getOptions().size()>0){
                    for(OptionDataVO option : customerFavChaiRequest.getRecipeDetails().getOptions()){
                        List<String> paidAddOn = composition.getOptions().stream().filter(optionName->option.getName().equalsIgnoreCase(optionName)).collect(Collectors.toList());
                        if( paidAddOn.size()>0){
                            list.add(addAddon(customerFavChai,ProductSource.MENU,ProductClassification.PAID_ADDON,option));
                        }
                    }
                }
            }
        }
        LOG.info("****** AddOns List*********** {}", new Gson().toJson(list));
        return list;
    }

    private FavChaiCustomizationDetail addAddon(CustomerFavChaiMapping customerFavChai, ProductSource menu, ProductClassification paidAddon, OptionDataVO option) {
        FavChaiCustomizationDetail info = new FavChaiCustomizationDetail(customerFavChai,option.getId(),"None",option.getName(),ProductClassification.PAID_ADDON.name(),"N",new BigDecimal(1),ProductSource.ADDON.name(),option.getShortCode()==null ? null : option.getShortCode(),"PC");
        LOG.info("FAV CHAI CUSTOMIZATION DETAIL for customizationId ::::{} {}",customerFavChai.getCustomizationId(),new Gson().toJson(info));
        try{
            manager.persist(info);
        }catch(Exception e){
            LOG.error("Unable to persist favChaiCustomizationDetail for customizationId :::{} and favCustomizationDetail is :::{} with error ::{}",customerFavChai.getCustomizationId(),info,e);
        }
        return info;
    }

    private FavChaiCustomizationDetail addAddon(CustomerFavChaiMapping customerFavChai, ProductSource source, ProductClassification type,
                                                IngredientProductDetail detail) {
        FavChaiCustomizationDetail info = new FavChaiCustomizationDetail(customerFavChai, detail.getProduct().getProductId(),
                detail.getDimension() == null ? null : detail.getDimension().getName(), detail.getProduct().getName(), type == null ? null : type.name(), AppConstants.getValue(detail.isDefaultSetting()), detail.getQuantity(), source.name(), getShortCode(detail.getProduct().getName()),detail.getUom() == null ? null : detail.getUom().name());
        LOG.info("FAV CHAI CUSTOMIZATION DETAIL for customizationId ::::{} {}",customerFavChai.getCustomizationId(),new Gson().toJson(info));
        try{
            manager.persist(info);
        }catch(Exception e){
            LOG.error("Unable to persist favChaiCustomizationDetail for customizationId :::{} and favCustomizationDetail is :::{} with error:::{}",customerFavChai.getCustomizationId(),info,e);
        }
        return info;
    }

    private FavChaiCustomizationDetail addAddon(CustomerFavChaiMapping customerFavChai, String name) {
        FavChaiCustomizationDetail info = new FavChaiCustomizationDetail(customerFavChai, -1, "None",
                name, ProductClassification.FREE_OPTION.name(), "N", new BigDecimal((customerFavChai.getQuantity())), ProductSource.OPTION.name(),getShortCode(name),"PC");
        LOG.info("FAV CHAI CUSTOMIZATION DETAIL for customizationId ::::{} {}",customerFavChai.getCustomizationId(),new Gson().toJson(info));
        try{
            manager.persist(info);
        }catch(Exception e){
            LOG.error("Unable to persist favChaiCustomizationDetail for customizationId :::{} and favCustomizationDetail is :::{} with error :::{}",customerFavChai.getCustomizationId(),info,e);
        }
        return info;
    }

    private FavChaiCustomizationDetail addAddon(CustomerFavChaiMapping customerFavChai, ProductSource source, ProductClassification type,
                                                IngredientVariantDetail detail) {
        FavChaiCustomizationDetail info = new FavChaiCustomizationDetail(customerFavChai, detail.getProductId(), null,
                detail.getAlias(), type.name(), AppConstants.getValue(detail.isDefaultSetting()), detail.getQuantity(), source.name(),getShortCode(detail.getAlias()),detail.getUom() == null ? null : detail.getUom().name());
        LOG.info("FAV CHAI CUSTOMIZATION DETAIL for customizationId ::::{}{}",customerFavChai.getCustomizationId(),new Gson().toJson(info));
        try{
            manager.persist(info);
        }catch(Exception e){
            LOG.error("Unable to persist favChaiCustomizationDetail for customizationId :::{} and favCustomizationDetail is :::{} with error ::{}",customerFavChai.getCustomizationId(),info,e);
        }
        return info;
    }

    private String getShortCode(String name){
        String shortCode ="";
        if(Objects.nonNull(name) && name.length()>0){
            String [] arr = name.split(" ");
            if(arr.length>1){
                for(String ele : arr){
                    shortCode = shortCode + ele.toUpperCase().charAt(0);
                }
                return shortCode;
            }else {
                return shortCode + arr[arr.length-1].toUpperCase().charAt(0);
            }
        }
        return null;
    }
}
