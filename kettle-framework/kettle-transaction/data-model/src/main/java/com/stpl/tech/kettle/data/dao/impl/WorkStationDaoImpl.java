/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.dao.impl;

import com.stpl.tech.kettle.data.converter.DataConverter;
import com.stpl.tech.kettle.data.dao.WorkStationDao;
import com.stpl.tech.kettle.data.model.AssemblyLogData;
import com.stpl.tech.kettle.data.model.AssemblyTATData;
import com.stpl.tech.kettle.data.model.MonkCalibrationEventDetail;
import com.stpl.tech.kettle.data.model.MonkLogData;
import com.stpl.tech.kettle.data.model.MonkTroubleShootData;
import com.stpl.tech.kettle.data.model.WorkStationManualTaskDetail;
import com.stpl.tech.kettle.data.model.WorkstationLog;
import com.stpl.tech.kettle.domain.model.MonkCalibrationEvent;
import com.stpl.tech.kettle.domain.model.TroubleShoot;
import com.stpl.tech.kettle.domain.model.WorkstationManualTask;
import com.stpl.tech.master.domain.model.SwitchStatus;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 05-Apr-2016 10:52:40 pm
 * 
 */
@Repository
public class WorkStationDaoImpl extends AbstractDaoImpl implements WorkStationDao {

	private static final Logger LOG = LoggerFactory.getLogger(WorkStationDaoImpl.class);

	@Override
	public int addWorkStationLog(List<WorkstationLog> logList) {
		int count = 0;
		try {
			for (WorkstationLog log : logList) {
				Query query = manager.createQuery("FROM WorkstationLog W where W.itemId =:itemId");
				query.setParameter("itemId",log.getItemId());
				query.setMaxResults(1);
				List<WorkstationLog> wl =  query.getResultList();
				if (wl.isEmpty()) {
					manager.persist(log);
					manager.flush();
					count++;
				}
				else {
					LOG.info("Entry already exists in workstationLog for itemId : {}",log.getItemId());
				}
			}
		} catch (Exception e) {
			LOG.error(e.getMessage());
		}
		return count;
	}

	@Override
	public int addAssemblyLog(List<AssemblyLogData> logList) {
		int count = 0;
		try {
			for (AssemblyLogData log : logList) {
				//AssemblyLogData ald = manager.find(AssemblyLogData.class, log.getAssemblyLogDataId());
				Query query = manager.createQuery("FROM AssemblyLogData A where A.orderId =:orderId");
				query.setParameter("orderId",log.getOrderId());
				query.setMaxResults(1);
				 List<AssemblyLogData> ald = query.getResultList();
				if (ald.isEmpty()) {
					manager.persist(log);
					manager.flush();
					count++;
				}
				else {
					LOG.info("Entry already exists in assemblyLog for orderId : {}",log.getOrderId());
				}
			}
		} catch (Exception e) {
			LOG.error(e.getMessage());
		}
		return count;
	}

    @Override
    public int addMonkLog(List<MonkLogData> monkLogData) {
        int count = 0;
        try {
            for (MonkLogData log : monkLogData) {
                AssemblyLogData ald = manager.find(AssemblyLogData.class, log.getLogId());
                if (ald == null) {
                    manager.persist(log);
                } else {
                    manager.merge(log);
                }
                manager.flush();
                count++;
            }
        } catch (Exception e) {
            LOG.info(e.getMessage());
        }
        return count;

    }

	@Override
	public List<AssemblyTATData> getAssemblyTAT(Integer unitId, Date businessDate) {
		Query query = manager.createQuery("FROM AssemblyTATData WHERE " +
				"unitId=:unitId AND businessDate=:businessDate AND status=:status");
		query.setParameter("unitId", unitId)
				.setParameter("businessDate", businessDate)
				.setParameter("status", SwitchStatus.ACTIVE.name());
		return query.getResultList();
	}

	@Override
	public void processTATForAllUnits() {
		Query query = manager.createNativeQuery("CALL HOURLY_TAT_DUMP()");
		query.executeUpdate();
	}

	@Override
	public List<WorkStationManualTaskDetail> addWorkStationManualTasks(List<WorkstationManualTask> workstationTasks) {
		List<WorkStationManualTaskDetail> taskDetails = new ArrayList<>();
		for (WorkstationManualTask task : workstationTasks) {
			WorkStationManualTaskDetail taskDetail = DataConverter.convert(task);
			taskDetails.add(taskDetail);
		}
		return addAll(taskDetails);
	}

	@Override
	public List<WorkstationManualTask> getWorkStationManualTasks(String generatedOrderId) {
		Query query = manager.createQuery("FROM WorkStationManualTaskDetail WHERE generatedOrderId=:generatedOrderId");
		query.setParameter("generatedOrderId", generatedOrderId);
		List<WorkstationManualTask> savedTasks = new ArrayList<>();
		try {
			List<WorkStationManualTaskDetail> tasks = query.getResultList();
			if (Objects.nonNull(tasks) && !tasks.isEmpty()) {
				for (WorkStationManualTaskDetail task : tasks) {
					WorkstationManualTask manualTask = DataConverter.convert(task);
					savedTasks.add(manualTask);
				}
			}
		} catch (Exception e) {
			LOG.info("Error while fetching manual tasks for generatedOrderId: {}", generatedOrderId, e);
		}

		return savedTasks;
	}

	@Override
	public MonkCalibrationEvent addMonkCalibrationEvent(MonkCalibrationEvent monkCalibrationEvent) {
		try {
			MonkCalibrationEventDetail calibrationDetail = DataConverter.convert(monkCalibrationEvent);
			calibrationDetail = add(calibrationDetail);
			if (!CollectionUtils.isEmpty(monkCalibrationEvent.getTroubleShoots())) {
				for (TroubleShoot troubleShoot : monkCalibrationEvent.getTroubleShoots()) {
					MonkTroubleShootData monkTroubleShootData = new MonkTroubleShootData();
					monkTroubleShootData.setMonkCalibrationEventId(calibrationDetail.getMonkCalibrationEventId());
					monkTroubleShootData.setTroubleShootCode(troubleShoot.getCode());
					monkTroubleShootData.setTroubleShootCodeMeaning(troubleShoot.getCodeMeaning());
					add(monkTroubleShootData);
				}
			}
			return DataConverter.convert(calibrationDetail);
		} catch (Exception e) {
			LOG.error("Error while saving Monk Calibration Event: {}", monkCalibrationEvent, e);
		}
		return new MonkCalibrationEvent();
	}

	@Override
	public MonkCalibrationEvent getLastMonkCalibrationStatus(Integer unitId, Integer monkNo) {

		try {
			Query query = manager.createQuery("FROM MonkCalibrationEventDetail CE WHERE CE.unitId = :unitId and CE.monkNo = :monkNo " +
					"ORDER BY CE.serverTime DESC");
			query.setParameter("unitId", unitId);
			query.setParameter("monkNo", monkNo);
			query.setMaxResults(1);
			List<MonkCalibrationEventDetail> calibrationEventDetails = query.getResultList();
			if (Objects.nonNull(calibrationEventDetails) && !calibrationEventDetails.isEmpty()) {
				return DataConverter.convert(calibrationEventDetails.get(calibrationEventDetails.size() - 1));
			}
		} catch (Exception e) {
			LOG.error("Error while fetching Last Monk Calibration Event for unit: {}, monk no.: {}", unitId, monkNo, e);
		}
		return null;
	}

	@Override
	public List<MonkCalibrationEvent> getMonkCalibrationStatus(Integer unitId) {
		List<MonkCalibrationEvent> calibrationEvents = new ArrayList<>();
		try {
			Query query = manager.createQuery("SELECT CE1 FROM MonkCalibrationEventDetail CE1 LEFT JOIN MonkCalibrationEventDetail CE2 " +
					"ON (CE1.monkNo = CE2.monkNo AND CE1.serverTime < CE2.serverTime) " +
					"WHERE CE1.unitId = :unitId and " +
					"CE1.monkNo IS NOT NULL and CE2.serverTime IS NULL");
			query.setParameter("unitId", unitId);
			List<MonkCalibrationEventDetail> calibrationEventDetails = query.getResultList();
			if (Objects.nonNull(calibrationEventDetails) && !calibrationEventDetails.isEmpty()) {
				for (MonkCalibrationEventDetail calibrationEvent : calibrationEventDetails) {
					calibrationEvents.add(DataConverter.convert(calibrationEvent));
				}
			}
		} catch (Exception e) {
			LOG.error("Error while fetching Last Monk Calibration Event for unit: {}", unitId, e);
		}
		return calibrationEvents;
	}


	@Override
	public WorkstationLog getWorkStationLog(int orderItemId) {
		try {
			Query query = manager.createQuery("SELECT W FROM WorkStationLog W WHERE W.orderItemId = :orderItemId");
			query.setParameter("orderItemId",orderItemId);
			WorkstationLog log = (WorkstationLog) query.getResultList();
			if(Objects.nonNull(log)){
				return log;
			}
			else {
				return null;
			}
		} catch (Exception e) {
			LOG.error("Error in fetching workstation log for orderItemId {}",orderItemId);
		}
		return null;
	}

}
