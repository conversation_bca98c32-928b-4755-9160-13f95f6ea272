package com.stpl.tech.kettle.data.model;

import java.io.Serializable;

public class FeedbackOrderItem implements Serializable {
    /**
	 * 
	 */
	private static final long serialVersionUID = -5186324326914179951L;
	Integer pid; //productId
    Integer iid; //itemId
    String in; //itemName
    Integer qt; //quantity
    String it; //issueTags
    String d; //dimension
    String c; //customisation
    String q; //question
    Integer ir; //itemRating
    String ic; //itemComment
    String pi; //productImage
    String iri; //itemResponseImage
    String irv; //itemResponseVideo

    public Integer getPid() {
        return pid;
    }

    public void setPid(Integer pid) {
        this.pid = pid;
    }

    public Integer getIid() {
        return iid;
    }

    public void setIid(Integer iid) {
        this.iid = iid;
    }

    public String getIn() {
        return in;
    }

    public void setIn(String in) {
        this.in = in;
    }

    public Integer getQt() {
        return qt;
    }

    public void setQt(Integer qt) {
        this.qt = qt;
    }

    public String getIt() {
        return it;
    }

    public void setIt(String it) {
        this.it = it;
    }

    public String getD() {
        return d;
    }

    public void setD(String d) {
        this.d = d;
    }

    public String getC() {
        return c;
    }

    public void setC(String c) {
        this.c = c;
    }

    public String getQ() {
        return q;
    }

    public void setQ(String q) {
        this.q = q;
    }

    public Integer getIr() {
        return ir;
    }

    public void setIr(Integer ir) {
        this.ir = ir;
    }

    public String getIc() {
        return ic;
    }

    public void setIc(String ic) {
        this.ic = ic;
    }

    public String getPi() {
        return pi;
    }

    public void setPi(String pi) {
        this.pi = pi;
    }

    public String getIri() {
        return iri;
    }

    public void setIri(String iri) {
        this.iri = iri;
    }

    public String getIrv() {
        return irv;
    }

    public void setIrv(String irv) {
        this.irv = irv;
    }
    
}
