/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;
// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.Transient;
import java.math.BigDecimal;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

/**
 * ChannelPartner generated by hbm2java
 */
@Entity
@Table(name = "CASH_CARD_OFFER")
public class CashCardOffer implements java.io.Serializable {

	/**
	 *
	 */
	private static final long serialVersionUID = 4250700462291454429L;
	private Integer cashCardOfferId;
	private String description;
	private String suggestWalletDescription;
	private String offerStatus;
	private Date startDate;
	private Date endDate;
	private String createdBy;
	private Date creationTime;
	private String cancelledBy;
	private Date cancellationTime;
	private Integer unitId;
	private BigDecimal denomination;
	private BigDecimal percentage;
	private BigDecimal suggestWalletPercentage;
	private  String walletType;
	private Integer partnerId;

	public CashCardOffer() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)

	@Column(name = "CASH_CARD_OFFER_ID", unique = true, nullable = false)
	public Integer getCashCardOfferId() {
		return this.cashCardOfferId;
	}

	public void setCashCardOfferId(Integer loyaltyPointsId) {
		this.cashCardOfferId = loyaltyPointsId;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "START_DATE", nullable = false, length = 10)
	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	@Column(name = "CARD_DESCRIPTION", nullable = true)
	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	@Column(name = "SUGGEST_WALLET_CARD_DESCRIPTION", nullable = true)
	public String getSuggestWalletDescription() {
		return suggestWalletDescription;
	}

	public void setSuggestWalletDescription(String suggestWalletDescription) {
		this.suggestWalletDescription = suggestWalletDescription;
	}


	@Column(name = "OFFER_STATUS", nullable = false, length = 15)
	public String getOfferStatus() {
		return offerStatus;
	}

	public void setOfferStatus(String offerStatus) {
		this.offerStatus = offerStatus;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "END_DATE", length = 10)
	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATION_TIME", nullable = false, length = 19)
	public Date getCreationTime() {
		return creationTime;
	}

	public void setCreationTime(Date creationTime) {
		this.creationTime = creationTime;
	}

	@Column(name = "CARD_DENOMINATION", nullable = false, precision = 10)
	public BigDecimal getDenomination() {
		return denomination;
	}

	public void setDenomination(BigDecimal denomination) {
		this.denomination = denomination;
	}

	@Column(name = "OFFER_PERCENTAGE", nullable = true, precision = 10)
	public BigDecimal getPercentage() {
		return percentage;
	}

	public void setPercentage(BigDecimal percentage) {
		this.percentage = percentage;
	}

	@Column(name = "SUGGEST_WALLET_OFFER_PERCENTAGE", nullable = true, precision = 10)
	public BigDecimal getSuggestWalletPercentage() {
		return suggestWalletPercentage;
	}

	public void setSuggestWalletPercentage(BigDecimal suggestWalletPercentage) {
		this.suggestWalletPercentage = suggestWalletPercentage;
	}

	@Transient
	public String getWalletType() {
		return walletType;
	}

	public void setWalletType(String walletType) {
		this.walletType = walletType;
	}

	@Column(name = "UNIT_ID")
	public Integer getUnitId() {
		return unitId;
	}

	public void setUnitId(Integer unitId) {
		this.unitId = unitId;
	}

	@Column(name = "CREATED_BY")
	public String getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(String createdBy) {
		this.createdBy = createdBy;
	}

	@Column(name = "CANCELLED_BY")
	public String getCancelledBy() {
		return cancelledBy;
	}

	public void setCancelledBy(String cancelledBy) {
		this.cancelledBy = cancelledBy;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CANCELLATION_TIME", nullable = true, length = 19)
	public Date getCancellationTime() {
		return cancellationTime;
	}

	public void setCancellationTime(Date cancellationTime) {
		this.cancellationTime = cancellationTime;
	}

	@Column(name = "PARTNER_ID", nullable = false)
	public Integer getPartnerId() {
		return partnerId;
	}

	public void setPartnerId(Integer partnerId) {
		this.partnerId = partnerId;
	}
}
