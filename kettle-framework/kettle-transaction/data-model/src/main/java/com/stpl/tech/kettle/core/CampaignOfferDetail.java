package com.stpl.tech.kettle.core;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

public class CampaignOfferDetail implements Serializable {

    private static final long serialVersionUID = -2449711155159960978L;
    private Integer capmpaignOfferDetailId;
    private Integer customerId;
    private String countryCode;
    private String contactNumber;
    private String firstName;
    private Integer brandId;
    private Integer unitId;
    private String campaignCloneCode;
    private String couponCode;
    private Integer couponDetailId;
    private Integer offerDetailId;
    private String offerText;
    private Date couponStartDate;
    private Date couponEndDate;
    private Date couponGenerationTime;
    private String isNotificationRequired;
    private Integer offerCreateOrderId;
    private String isOfferApplied;
    private Integer offerApplyLastOrderId;
    private Date offerApplyLastTime;
    private Integer offerApplyCount;
    private String offerApplyType;
    private String status;
    private Integer gapInDays;
    private BigDecimal overallSavings;
    private Integer campaignId;
    private String couponType;
    private Integer channelPartner;
    private Integer reminderDays;
    private String customerType;
    private Integer currentJourneyNumber;
    private Integer nextJourneyNumber;
    private Date nextOfferDate;

    public CampaignOfferDetail() {
    }

    public CampaignOfferDetail(Integer capmpaignOfferDetailId,String contactNumber, Integer campaignId, String campaignCloneCode, Integer channelPartner,
                                       Integer customerId, String firstName, String couponCode, Date couponStartDate, Date couponEndDate, String offerText,
                                       String couponType, Integer reminderDays){
        this.capmpaignOfferDetailId = capmpaignOfferDetailId;
        this.contactNumber = contactNumber;
        this.campaignId = campaignId;
        this.campaignCloneCode = campaignCloneCode;
        this.channelPartner = channelPartner;
        this.customerId = customerId;
        this.firstName = firstName;
        this.couponCode = couponCode;
        this.couponStartDate = couponStartDate;
        this.couponEndDate = couponEndDate;
        this.offerText = offerText;
        this.couponType = couponType;
        this.reminderDays = reminderDays;
    }

    public Integer getCapmpaignOfferDetailId() {
        return capmpaignOfferDetailId;
    }

    public void setCapmpaignOfferDetailId(Integer capmpaignOfferDetailId) {
        this.capmpaignOfferDetailId = capmpaignOfferDetailId;
    }

    public Integer getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getContactNumber() {
        return contactNumber;
    }

    public void setContactNumber(String contactNumber) {
        this.contactNumber = contactNumber;
    }

    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public String getCampaignCloneCode() {
        return campaignCloneCode;
    }

    public void setCampaignCloneCode(String campaignCloneCode) {
        this.campaignCloneCode = campaignCloneCode;
    }

    public String getCouponCode() {
        return couponCode;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }

    public Integer getCouponDetailId() {
        return couponDetailId;
    }

    public void setCouponDetailId(Integer couponDetailId) {
        this.couponDetailId = couponDetailId;
    }

    public Integer getOfferDetailId() {
        return offerDetailId;
    }

    public void setOfferDetailId(Integer offerDetailId) {
        this.offerDetailId = offerDetailId;
    }

    public String getOfferText() {
        return offerText;
    }

    public void setOfferText(String offerText) {
        this.offerText = offerText;
    }

    public Date getCouponStartDate() {
        return couponStartDate;
    }

    public void setCouponStartDate(Date couponStartDate) {
        this.couponStartDate = couponStartDate;
    }

    public Date getCouponEndDate() {
        return couponEndDate;
    }

    public void setCouponEndDate(Date couponEndDate) {
        this.couponEndDate = couponEndDate;
    }

    public Date getCouponGenerationTime() {
        return couponGenerationTime;
    }

    public void setCouponGenerationTime(Date couponGenerationTime) {
        this.couponGenerationTime = couponGenerationTime;
    }

    public String getIsNotificationRequired() {
        return isNotificationRequired;
    }

    public void setIsNotificationRequired(String isNotificationRequired) {
        this.isNotificationRequired = isNotificationRequired;
    }

    public Integer getOfferCreateOrderId() {
        return offerCreateOrderId;
    }

    public void setOfferCreateOrderId(Integer offerCreateOrderId) {
        this.offerCreateOrderId = offerCreateOrderId;
    }

    public String getIsOfferApplied() {
        return isOfferApplied;
    }

    public void setIsOfferApplied(String isOfferApplied) {
        this.isOfferApplied = isOfferApplied;
    }

    public Integer getOfferApplyLastOrderId() {
        return offerApplyLastOrderId;
    }

    public void setOfferApplyLastOrderId(Integer offerApplyLastOrderId) {
        this.offerApplyLastOrderId = offerApplyLastOrderId;
    }

    public Date getOfferApplyLastTime() {
        return offerApplyLastTime;
    }

    public void setOfferApplyLastTime(Date offerApplyLastTime) {
        this.offerApplyLastTime = offerApplyLastTime;
    }

    public Integer getOfferApplyCount() {
        return offerApplyCount;
    }

    public void setOfferApplyCount(Integer offerApplyCount) {
        this.offerApplyCount = offerApplyCount;
    }

    public String getOfferApplyType() {
        return offerApplyType;
    }

    public void setOfferApplyType(String offerApplyType) {
        this.offerApplyType = offerApplyType;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Integer getGapInDays() {
        return gapInDays;
    }

    public void setGapInDays(Integer gapInDays) {
        this.gapInDays = gapInDays;
    }

    public BigDecimal getOverallSavings() {
        return overallSavings;
    }

    public void setOverallSavings(BigDecimal overallSavings) {
        this.overallSavings = overallSavings;
    }

    public Integer getCampaignId() {
        return campaignId;
    }

    public void setCampaignId(Integer campaignId) {
        this.campaignId = campaignId;
    }

    public String getCouponType() {
        return couponType;
    }

    public void setCouponType(String couponType) {
        this.couponType = couponType;
    }

    public Integer getChannelPartner() {
        return channelPartner;
    }

    public void setChannelPartner(Integer channelPartner) {
        this.channelPartner = channelPartner;
    }

    public Integer getReminderDays() {
        return reminderDays;
    }

    public void setReminderDays(Integer reminderDays) {
        this.reminderDays = reminderDays;
    }

    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    public Integer getCurrentJourneyNumber() {
        return currentJourneyNumber;
    }

    public void setCurrentJourneyNumber(Integer currentJourneyNumber) {
        this.currentJourneyNumber = currentJourneyNumber;
    }

    public Integer getNextJourneyNumber() {
        return nextJourneyNumber;
    }

    public void setNextJourneyNumber(Integer nextJourneyNumber) {
        this.nextJourneyNumber = nextJourneyNumber;
    }

    public Date getNextOfferDate() {
        return nextOfferDate;
    }

    public void setNextOfferDate(Date nextOfferDate) {
        this.nextOfferDate = nextOfferDate;
    }
}
