package com.stpl.tech.kettle.stock.dao.impl;

import java.math.BigDecimal;
import java.sql.Time;
import java.sql.Timestamp;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

import javax.persistence.Query;

import com.stpl.tech.kettle.core.data.vo.UnitTimeStockData;
import com.stpl.tech.kettle.data.model.PartnerUnitProductStockData;
import com.stpl.tech.kettle.data.model.UnitProductStockEventDataDetail;
import com.stpl.tech.kettle.stock.service.impl.AutomatedStockEventReportImpl;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.UnitHours;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.data.dao.impl.AbstractDaoImpl;
import com.stpl.tech.kettle.stock.dao.UnitProductStockDataDao;

@Repository
public class UnitProductStockDataDaoImpl extends AbstractDaoImpl implements UnitProductStockDataDao {

    private static final Logger LOG = LoggerFactory.getLogger(AutomatedStockEventReportImpl.class);
    @Autowired
    MasterDataCache masterDataCache;

    @Override
    public void deleteData(Date calculationDate, Integer unitId) {
        Query query = manager.createQuery("DELETE FROM UnitProductStockData a WHERE a.calculationDate = :calculationDate AND a.unitId = :unitId");
        query.setParameter("unitId",unitId);
        query.setParameter("calculationDate", calculationDate);
        query.executeUpdate();
//        manager.flush();
    }

    @Override
    public List<UnitProductStockEventDataDetail> findAllByUnitIdAndEventTimeStampOrderByProductIdAsc(Integer unitId, Date previousDate) {
        Query query = manager.createQuery("FROM UnitProductStockEventDataDetail U WHERE U.unitId = :unitId AND U.eventTimeStamp BETWEEN :startDate AND :endDate " +
                "ORDER BY U.productId ASC,U.dimension ASC");
        query.setParameter("unitId",unitId);
        query.setParameter("startDate", AppUtils.getStartOfBusinessDay(previousDate));
        query.setParameter("endDate",AppUtils.getStartOfBusinessDay(AppUtils.getDayBeforeOrAfterDay(previousDate,1)));
        List<UnitProductStockEventDataDetail> result = query.getResultList();
        return result;
    }

    @Override
    public Map<String, PartnerUnitProductStockData> getPreviousDayStockData(int unitId, Date businessDate) {
        Query query = manager.createQuery("FROM PartnerUnitProductStockData P where P.unitId =:unitId and P.businessDate = :businessDate order by P.key DESC");
        query.setParameter("unitId",unitId);
        query.setParameter("businessDate",businessDate);
        List<PartnerUnitProductStockData> partnerUnitProductStockDataList = query.getResultList();
        Map<String, PartnerUnitProductStockData> stockDataMap = new HashMap<>();
        for(PartnerUnitProductStockData pupsd : partnerUnitProductStockDataList){
            if(!stockDataMap.containsKey(pupsd.getProductId()+pupsd.getDimension())){
                stockDataMap.put(pupsd.getProductId()+pupsd.getDimension(), pupsd);
            }
        }
        return stockDataMap;
    }

    @Override
    public Map<Integer, UnitTimeStockData> getUnitClosingTimeMap(Date previousDate) {
        try{
            LOG.info("Getting Unit Timing");
            Query query = manager.createNativeQuery("SELECT OD.UNIT_ID, MIN(OD.BILLING_SERVER_TIME), " +
                    "MAX(OD.BILLING_SERVER_TIME) FROM ORDER_DETAIL OD WHERE OD.BUSINESS_DATE = :businessDate GROUP BY OD.UNIT_ID");
            query.setParameter("businessDate",AppUtils.getSQLFormattedDate(previousDate));
            Map<Integer, UnitTimeStockData> map = new HashMap<>();
            List<Object[]> result = query.getResultList();
            for (Object[] obj : result) {
                try {
                    UnitHours unitHourDetail = masterDataCache.getUnit((Integer) obj[0]).getOperationalHours()
                            .get(AppUtils.getWeekDayNumber((Date) obj[1])-1);
                    map.put((Integer) obj[0], getUnitTimeStockData(unitHourDetail, obj, previousDate));
                }catch (Exception e){
                    LOG.error("Exception Caught :::{}",(Integer) obj[0],e);
                }
            }
            return map;
        } catch (Exception e){
            LOG.error("Exception Caught!!:::",e);
            return null;
        }
    }

    @Override
    public Map<Integer, Map<Integer, Map<String, String>>> getLatestPreviousStockEvent(Date eventTimestamp) {
        try{
            LOG.info("Getting Unit Timing");
            Query query = manager.createNativeQuery("SELECT U.UNIT_ID, U.PRODUCT_ID, U.DIMENSION,U.STATUS FROM UNIT_PRODUCTS_STOCK_EVENT_DATA U " +
                    "WHERE EVENT_TIMESTAMP BETWEEN :start AND :end AND KEY_ID IN (SELECT MAX(UG.KEY_ID) FROM UNIT_PRODUCTS_STOCK_EVENT_DATA UG WHERE EVENT_TIMESTAMP BETWEEN :start AND :end  GROUP BY UG.PRODUCT_ID, UG.DIMENSION, UG.UNIT_ID ) ORDER BY U.UNIT_ID, U.PRODUCT_ID");
            query.setParameter("start",AppUtils.getSQLFormattedDate(AppUtils.addDays(eventTimestamp, -30)));
            query.setParameter("end",AppUtils.getSQLFormattedDate(eventTimestamp));
            Map<Integer, Map<Integer, Map<String, String>>> lpse = new HashMap<>();
            List<Object[]> resultList = query.getResultList();
            // result[0] unitId
            // result[1] productId
            // result[2] dimension
            // result[3] status
            for(Object[] result : resultList){
                int unitId = (int) result[0];
                int productId = (int) result[1];
                String dimension = result[2].toString();
                String status = result[3].toString();
                if(!lpse.containsKey(unitId)){
                    Map<Integer, Map<String, String>> pds = new HashMap<>();
                    lpse.put(unitId, pds);
                }
                if(!lpse.get(unitId).containsKey(productId)){
                    Map<String, String> ds = new HashMap<>();
                    lpse.get(unitId).put(productId, ds);
                }
                lpse.get(unitId).get(productId).put(dimension, status);
            }
            return lpse;
        } catch (Exception e){
            LOG.error("Exception Caught while fetching Latest Previous Stock Event!!:::",e);
            return null;
        }
    }


    @Override
    public Pair<Date,Date> getCurrentAndLastDayCLoseTime(Integer unitId,Date businessDate){
        Query query = manager.createNativeQuery("SELECT GENERATION_TIME\n" +
                "FROM KETTLE_SCM.DAY_CLOSE_EVENT\n" +
                "WHERE UNIT_ID = :unitId\n" +
                "  AND CLOSURE_EVENT_TYPE = 'STOCK_TAKE'\n" +
                "  AND STATUS = 'CLOSED'\n" +
                "  AND BUSINESS_DATE <= :businessDate\n" +
                "ORDER BY GENERATION_TIME DESC\n" +
                "LIMIT 2;");
        query.setParameter("unitId",unitId);
        query.setParameter("businessDate",AppUtils.getDate(businessDate));
        List<Timestamp> scmStockInData = query.getResultList();
       return new Pair<>( scmStockInData.get(0), scmStockInData.get(1));

    }

    @Override
    public Map<Integer,Map<Date,Boolean>> getStockInSCMData(int unitId, Date businessDate,Date lastDayCloseTime , Date currentDayCloseTime ) {
        Query query = manager.createNativeQuery(
                "select s.LINKED_PRODUCT_ID , g.LAST_UPDATE_TIME from KETTLE_SCM.GOODS_RECEIVED g inner join " +
                        "KETTLE_SCM.GOODS_RECEIVED_ITEM i \n" +
                        "on g.GOODS_RECEIVED_ID = i.GOODS_RECEIVED_ID \n" +
                        "inner join KETTLE_SCM.SKU_DEFINITION s on s.SKU_ID = i.SKU_ID\n" +
                        "where g.GENERATED_FOR_UNIT_ID  = :unitId and g.GOODS_RECEIVED_STATUS = \"SETTLED\"   and  i.RECEIVED_QUANTITY > 0 and\n" +
                        "g.LAST_UPDATE_TIME >= :startDate  and g.LAST_UPDATE_TIME <= :endDate and\n" +
                        " s.LINKED_PRODUCT_ID in\n" +
                        " (SELECT PRODUCT_ID FROM KETTLE_SCM.PRODUCT_DEFINITION where CATEGORY_LEVEL = \"LEVEL_1\" and PRODUCT_STATUS = \"ACTIVE\" ) order by g.LAST_UPDATE_TIME ;");
        query.setParameter("unitId",unitId);
        query.setParameter("startDate",lastDayCloseTime);
        query.setParameter("endDate",currentDayCloseTime);
        List<Object[]> scmStockInData = query.getResultList();
        Map<Integer,Map<Date,Boolean>> scmStockInMap = new HashMap<>();
        for(Object[] objects : scmStockInData){
            if(!scmStockInMap.containsKey((Integer) objects[0])){
                scmStockInMap.put((Integer) objects[0],new TreeMap<Date,Boolean>((o1, o2) -> o1.compareTo(o2)));
            }
            scmStockInMap.get((Integer) objects[0]).put((Date) objects[1],Boolean.TRUE);
        }
        return scmStockInMap;
    }

    @Override
    public Map<Integer,Boolean> getScmInitialStatusMap(Integer unitId , Date date){
        Query query = manager.createNativeQuery(
                "SELECT " +
                        "    si.PRODUCT_ID, " +
                        "    si.CLOSING_STOCK AS ACTUAL_CLOSING_VALUE, " +
                        "    si.GENERATION_TIME " +
                        "FROM " +
                        "    KETTLE_SCM.STOCK_INVENTORY si " +
                        "WHERE " +
                        "    si.UNIT_ID = :unitId " +
                        "    AND si.PRODUCT_ID IN ( " +
                        "        SELECT pd.PRODUCT_ID " +
                        "        FROM KETTLE_SCM.PRODUCT_DEFINITION pd " +
                        "        WHERE pd.CATEGORY_LEVEL = 'LEVEL_1' " +
                        "          AND pd.PRODUCT_STATUS = 'ACTIVE' " +
                        "    ) " +
                        "    AND si.BUSINESS_DATE = :businessDate " +
                        "    AND si.STOCK_TYPE = 'DAILY' " +
                        "GROUP BY si.PRODUCT_ID"
        );

        query.setParameter("unitId",unitId);
        query.setParameter("businessDate",AppUtils.getDateAfterDays(AppUtils.getDate(date),-1));
        List<Object[]> scmStockData = query.getResultList();
        Map<Integer,Boolean> scmStockMap = new HashMap<>();
        for(Object[] objects : scmStockData){
            scmStockMap.put((Integer) objects[0] , ((BigDecimal)objects[1]).compareTo(BigDecimal.ZERO) > 0);
        }
        return  scmStockMap;
    }

    public void insertScmStockOutEntries(Integer unitId , BigDecimal downtime , BigDecimal cafeOperationalHours
    ,Integer brandId , Integer productId , Date businessDate , Date openingTime , Date closingTime){

    }

    private UnitTimeStockData getUnitTimeStockData(UnitHours unitHourDetail, Object[] dayCloseData, Date previousDate){
        long minDiff = AppUtils.getMinutesDifference(unitHourDetail.getDineInClosingTime(), unitHourDetail.getDineInOpeningTime());
        boolean is24HourCafe = minDiff <= 20;
        Date deliveryOpeningTime = AppUtils.setTimeToDate(previousDate, unitHourDetail.getDeliveryOpeningTime());
        Date deliveryClosingTime = AppUtils.setTimeToDate(previousDate, unitHourDetail.getDeliveryClosingTime());
        if(deliveryClosingTime.before(deliveryOpeningTime)){
            deliveryClosingTime=AppUtils.addDays(deliveryClosingTime,1);
        }
        return UnitTimeStockData.builder()
                .dineInTime(is24HourCafe ? Arrays.asList((Date) dayCloseData[1], (Date) dayCloseData[2])
                        : Arrays.asList(AppUtils.setTimeToDate(previousDate, unitHourDetail.getDineInOpeningTime()), (Date) dayCloseData[2]))
                .deliveryTime(Arrays.asList(deliveryOpeningTime, deliveryClosingTime))
                .dineInOpeningTime(AppUtils.setTimeToDate(previousDate, unitHourDetail.getDineInOpeningTime()))
                .dineInClosingTime(AppUtils.setTimeToDate(previousDate, unitHourDetail.getDineInClosingTime()))
                .deliveryOpeningTime(AppUtils.setTimeToDate(previousDate, unitHourDetail.getDeliveryOpeningTime()))
                .deliveryClosingTime(AppUtils.setTimeToDate(previousDate, unitHourDetail.getDeliveryClosingTime()))
                .is24HoursCafe(is24HourCafe)
                .isOperational(unitHourDetail.isIsOperational()).build();
    }



    public static Time leastOpeningTime(Time a, Time b) {
        if(a == null && b == null){
            return null;
        }
        else if(a == null){
            return b;
        }
        else if(b == null){
            return a;
        }
        else {
            return (a.before(b) ? a : b);
        }
    }
}