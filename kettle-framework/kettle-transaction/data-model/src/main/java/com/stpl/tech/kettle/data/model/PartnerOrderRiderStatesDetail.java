package com.stpl.tech.kettle.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Entity
@Table(name="PARTNER_ORDER_RIDER_STATES_DETAIL")
public class PartnerOrderRiderStatesDetail {
    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "PARTNER_ORDER_RIDER_DETAIL_ID", unique = true, nullable = false)
    private Integer id;

    @Column(name = "PARTNER_ORDER_ID",nullable = false )
    private String partnerOrderId;
    @Column(name = "KETTLE_ORDER_ID", nullable = false)
    private Integer kettleOrderId;
    @Column(name = "PARTNER_NAME", nullable = false)
    private String partnerName;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "BILLING_SERVER_TIME",nullable = false)
    private Date billingServerTime;

    @Column(name="RIDER_NAME", length=100)
    private String riderName ;
    @Column(name="RIDER_CONTACT")
    private String riderContact;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name= "RIDER_ASSIGNED_AT")
    private Date riderAssignedAt;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name= "RIDER_ARRIVED_AT")
    private Date riderArrivedAt;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name= "ORDER_PICKUP_TIME")
    private Date orderPickupTime;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name= "ORDER_DELIVERY_TIME")
    private Date orderDeliveryTime;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name ="RIDER_REACHED_CUSTOMER_AT")
    private Date riderReachedCustomerAt;

    @Column(name = "REASONS_FOR_DELAY")
    private String reasonsForDelay;

}
