/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;
// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * OrderSettlement generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "SUBSCRIPTION_STATUS_EVENT"

)
public class SubscriptionStatusEvent implements java.io.Serializable {

	private Integer subscriptionStatusEventId;
	private SubscriptionDetail subscriptionDetail;
	private String eventStatus;
	private String eventType;
	private String reasonText;
	private int generatedBy;
	private Date addTime;
	private Date eventStartDate;
	private Date eventEndDate;
	private Date lastUpdateTime;

	public SubscriptionStatusEvent() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)

	@Column(name = "SUBSCRIPTION_STATUS_EVENT_ID", unique = true, nullable = false)
	public Integer getSubscriptionStatusEventId() {
		return this.subscriptionStatusEventId;
	}

	public void setSubscriptionStatusEventId(Integer subscriptionStatusEventId) {
		this.subscriptionStatusEventId = subscriptionStatusEventId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SUBSCRIPTION_ID", nullable = false)
	public SubscriptionDetail getSubscriptionDetail() {
		return this.subscriptionDetail;
	}

	public void setSubscriptionDetail(SubscriptionDetail subscription) {
		this.subscriptionDetail = subscription;
	}

	@Column(name = "GENERATED_BY", nullable = false)
	public int getGeneratedBy() {
		return generatedBy;
	}

	public void setGeneratedBy(int employeeId) {
		this.generatedBy = employeeId;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LAST_UPDATE_TIME", nullable = false, length = 19)
	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "ADD_TIME", nullable = false, length = 19)
	public Date getAddTime() {
		return addTime;
	}

	public void setAddTime(Date addTime) {
		this.addTime = addTime;
	}

	@Column(name = "EVENT_STATUS", nullable = false, length = 15)
	public String getEventStatus() {
		return eventStatus;
	}

	public void setEventStatus(String eventStatus) {
		this.eventStatus = eventStatus;
	}

	@Column(name = "EVENT_TYPE", nullable = false, length = 30)
	public String getEventType() {
		return eventType;
	}

	public void setEventType(String toStatus) {
		this.eventType = toStatus;
	}

	@Column(name = "REASON_TEXT", nullable = false, length = 100)
	public String getReasonText() {
		return reasonText;
	}

	public void setReasonText(String reasonText) {
		this.reasonText = reasonText;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "EVENT_START_DATE", nullable = false, length = 19)
	public Date getEventStartDate() {
		return eventStartDate;
	}

	public void setEventStartDate(Date eventStartDate) {
		this.eventStartDate = eventStartDate;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "EVENT_END_DATE", nullable = false, length = 19)
	public Date getEventEndDate() {
		return eventEndDate;
	}

	public void setEventEndDate(Date eventEndDate) {
		this.eventEndDate = eventEndDate;
	}

}
