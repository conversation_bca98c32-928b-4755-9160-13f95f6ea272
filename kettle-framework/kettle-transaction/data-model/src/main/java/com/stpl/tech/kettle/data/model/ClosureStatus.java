/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

import static javax.persistence.GenerationType.IDENTITY;

// Generated 27 Jul, 2015 8:18:00 PM by Hibernate Tools 4.0.0

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * ClosureStatus generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "CLOSURE_STATUS")
public class ClosureStatus implements java.io.Serializable {

	private Integer closureStatusId;
	private UnitClosureDetails unitClosureDetails;
	private String closureStatus;
	private Date updateTmstmp;

	public ClosureStatus() {
	}

	public ClosureStatus(UnitClosureDetails unitClosureDetails, String closureStatus, Date updateTmstmp) {
		this.unitClosureDetails = unitClosureDetails;
		this.closureStatus = closureStatus;
		this.updateTmstmp = updateTmstmp;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "CLOSURE_STATUS_ID", unique = true, nullable = false)
	public Integer getClosureStatusId() {
		return this.closureStatusId;
	}

	public void setClosureStatusId(Integer closureStatusId) {
		this.closureStatusId = closureStatusId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "CLOSURE_ID", nullable = false)
	public UnitClosureDetails getUnitClosureDetails() {
		return this.unitClosureDetails;
	}

	public void setUnitClosureDetails(UnitClosureDetails unitClosureDetails) {
		this.unitClosureDetails = unitClosureDetails;
	}

	@Column(name = "CLOSURE_STATUS", nullable = false, length = 20)
	public String getClosureStatus() {
		return this.closureStatus;
	}

	public void setClosureStatus(String closureStatus) {
		this.closureStatus = closureStatus;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "UPDATE_TMSTMP", nullable = false, length = 19)
	public Date getUpdateTmstmp() {
		return this.updateTmstmp;
	}

	public void setUpdateTmstmp(Date updateTmstmp) {
		this.updateTmstmp = updateTmstmp;
	}

}
