package com.stpl.tech.kettle.core;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.Column;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Workbook;

import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.excelparser.ExcelWriter;
import com.stpl.tech.util.excelparser.annotations.ExcelField;

public class HibernateExcelWriter extends ExcelWriter {
	
	public HibernateExcelWriter() {
		super();
	}

	public HibernateExcelWriter(Workbook workbook) {
		super(workbook);
	}

	public int writeHeaders(int startCol, List<Field> excelColumns, String parent, Row headerRow, Class<?> clazz) {
		CellStyle headerStyle = getHeaderStyle();
		Map<String, String> map = getGettersMap(clazz);
		for (Field header : excelColumns) {

			ExcelField cellAnnotation = header.getAnnotation(ExcelField.class);
			String headerLabel = parent;
			String columnAnnotation = map.get(header.getName());
			if (columnAnnotation == null && cellAnnotation.headerName() != null
					&& !AppConstants.BLANK.equals(cellAnnotation.headerName())) {
				headerLabel = parent + cellAnnotation.headerName();
			}
			if (columnAnnotation != null) {
				headerLabel = parent + columnAnnotation;
			} else {
				headerLabel = header.getName();
			}
			// for flat structure only
			Cell cell = headerRow.createCell(startCol++);
			cell.setCellValue(capitalize(headerLabel));
			cell.setCellStyle(headerStyle);
		}
		return startCol;
	}

	private Map<String, String> getGettersMap(Class<?> clazz) {
		Map<String, String> map = new HashMap<String, String>();
		Method[] methods = clazz.getMethods();
		for (Method m : methods) {

			if (m.isAnnotationPresent(Column.class) && m.getName().startsWith("get")) {
				Column annotationNameAtt = m.getAnnotation(Column.class);
				String n = m.getName().substring(3);
				n = n.substring(0, 1).toLowerCase() + n.substring(1, n.length());
				map.put(n, annotationNameAtt.name());
			}
		}
		return map;
	}
}
