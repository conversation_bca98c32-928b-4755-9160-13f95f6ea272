package com.stpl.tech.kettle.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.util.Date;

@SuppressWarnings("serial")
@Entity
@Table(name = "LOYALTY_TRANSFER")
public class LoyaltyTransfer implements java.io.Serializable {

    private Integer eventId;
    private Integer senderId;
    private String senderName;
    private String senderContactNumber;
    private Integer receiverId;
    private String receiverName;
    private String receiverContactNumber;
    private Integer senderInitialPoints;
    private Integer receiverInitialPoints;
    private Integer senderTransferPoints;
    private Date transferInitiatedTime;
    private Date transferRequestTime;
    private String transferStatus;
    private Date expiryTime;
    private String transferType;
    private String transferMessage;
    private Integer bonusPoint;
    private Integer totalTransferred;
    private Integer senderBalance;
    private Integer receiverBalance;

    public LoyaltyTransfer() {

    }


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "EVENT_ID", unique = true, nullable = false)
    public Integer getEventId() {
        return eventId;
    }

    public void setEventId(Integer eventId) {
        this.eventId = eventId;
    }

    @Column(name = "SENDER_ID", nullable = false)
    public Integer getSenderId() {
        return senderId;
    }

    public void setSenderId(Integer senderId) {
        this.senderId = senderId;
    }

    @Column(name = "SENDER_NAME", length = 50)
    public String getSenderName() {
        return senderName;
    }

    public void setSenderName(String senderName) {
        this.senderName = senderName;
    }

    @Column(name = "SENDER_CONTACT_NUMBER", nullable = false, length = 15)
    public String getSenderContactNumber() {
        return senderContactNumber;
    }

    public void setSenderContactNumber(String senderContactNumber) {
        this.senderContactNumber = senderContactNumber;
    }

    @Column(name = "RECEIVER_ID")
    public Integer getReceiverId() {
        return receiverId;
    }

    public void setReceiverId(Integer receiverId) {
        this.receiverId = receiverId;
    }

    @Column(name = "RECEIVER_NAME", length = 50)
    public String getReceiverName() {
        return receiverName;
    }

    public void setReceiverName(String receiverName) {
        this.receiverName = receiverName;
    }

    @Column(name = "RECEIVER_CONTACT_NUMBER", nullable = false, length = 15)
    public String getReceiverContactNumber() {
        return receiverContactNumber;
    }

    public void setReceiverContactNumber(String receiverContactNumber) {
        this.receiverContactNumber = receiverContactNumber;
    }

    @Column(name = "SENDER_INITIAL_POINTS")
    public Integer getSenderInitialPoints() {
        return senderInitialPoints;
    }

    public void setSenderInitialPoints(Integer senderInitialPoints) {
        this.senderInitialPoints = senderInitialPoints;
    }

    @Column(name = "RECEIVER_INITIAL_POINTS")
    public Integer getReceiverInitialPoints() {
        return receiverInitialPoints;
    }

    public void setReceiverInitialPoints(Integer receiverInitialPoints) {
        this.receiverInitialPoints = receiverInitialPoints;
    }

    @Column(name = "SENDER_TRANSFER_POINTS")
    public Integer getSenderTransferPoints() {
        return senderTransferPoints;
    }

    public void setSenderTransferPoints(Integer senderTransferPoints) {
        this.senderTransferPoints = senderTransferPoints;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "TRANSFER_INITIATED_TIME")
    public Date getTransferInitiatedTime() {
        return transferInitiatedTime;
    }

    public void setTransferInitiatedTime(Date transferInitiatedTime) {
        this.transferInitiatedTime = transferInitiatedTime;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "TRANSFER_REQUEST_TIME")
    public Date getTransferRequestTime() {
        return transferRequestTime;
    }

    public void setTransferRequestTime(Date transferRequestTime) {
        this.transferRequestTime = transferRequestTime;
    }

    @Column(name = "TRANSFER_STATUS")
    public String getTransferStatus() {
        return transferStatus;
    }

    public void setTransferStatus(String transferStatus) {
        this.transferStatus = transferStatus;
    }

    @Column(name = "EXPIRY_TIME")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getExpiryTime() {
        return expiryTime;
    }

    public void setExpiryTime(Date expiryTime) {
        this.expiryTime = expiryTime;
    }

    @Column(name = "TRANSFER_TYPE", nullable = false)
    public String getTransferType() {
        return transferType;
    }

    public void setTransferType(String transferType) {
        this.transferType = transferType;
    }

    @Column(name = "TRANSFER_MESSAGE")
    public String getTransferMessage() {
        return transferMessage;
    }

    public void setTransferMessage(String transferMessage) {
        this.transferMessage = transferMessage;
    }

    @Column(name = "BONUS_POINT")
    public Integer getBonusPoint() {
        return bonusPoint;
    }

    public void setBonusPoint(Integer bonusPoint) {
        this.bonusPoint = bonusPoint;
    }

    @Column(name = "TOTAL_TRANSFERRED")
    public Integer getTotalTransferred() {
        return totalTransferred;
    }

    public void setTotalTransferred(Integer totalTransferred) {
        this.totalTransferred = totalTransferred;
    }

    @Column(name = "SENDER_BALANCE")
    public Integer getSenderBalance() {
        return senderBalance;
    }

    public void setSenderBalance(Integer senderBalance) {
        this.senderBalance = senderBalance;
    }

    @Column(name = "RECEIVER_BALANCE")
    public Integer getReceiverBalance() {
        return receiverBalance;
    }

    public void setReceiverBalance(Integer receiverBalance) {
        this.receiverBalance = receiverBalance;
    }
}
