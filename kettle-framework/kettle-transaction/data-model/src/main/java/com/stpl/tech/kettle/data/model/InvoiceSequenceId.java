package com.stpl.tech.kettle.data.model;


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import static javax.persistence.GenerationType.IDENTITY;

@SuppressWarnings("serial")
@Entity
@Table(name = "INVOICE_SEQUENCE_ID")
public class InvoiceSequenceId implements java.io.Serializable {
    private Integer sequenceId;
    private String stateCode;
    private String financialYear;
    private int nextValue;


    public InvoiceSequenceId() {
    }

    public InvoiceSequenceId(String  stateCode, String financialYear, int nextValue) {
        this.stateCode = stateCode;
        this.financialYear = financialYear;
        this.nextValue = nextValue;
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "SEQUENCE_ID", unique = true, nullable = false)
    public Integer getSequenceId() {
        return this.sequenceId;
    }

    public void setSequenceId(Integer sequenceId) {
        this.sequenceId = sequenceId;
    }

    @Column(name="FINANCIAL_YEAR", nullable = false)
    public String getFinancialYear() {
        return financialYear;
    }

    public void setFinancialYear(String financialYear) {
        this.financialYear = financialYear;
    }


    @Column(name = "STATE_CODE", nullable = false)
    public String getStateCode() {
        return this.stateCode;
    }

    public void setStateCode(String stateCode) {
        this.stateCode = stateCode;
    }



    @Column(name = "NEXT_VALUE", nullable = false)
    public int getNextValue() {
        return this.nextValue;
    }

    public void setNextValue(int nextValue) {
        this.nextValue = nextValue;
    }

}