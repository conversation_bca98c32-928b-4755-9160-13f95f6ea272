/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;
// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 * OrderItemAddon generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "SUBSCRIPTION_ITEM_ADDON"

)
public class SubscriptionItemAddon implements java.io.Serializable, AddonData {

	private Integer subscriptionItemAddonId;
	private SubscriptionItem subscriptionItem;
	private int productId;
	private String source;
	private String dimension;
	private String uom;
	private BigDecimal quantity;
	private String name;
	private String defaultSetting;
	private String type;

	public SubscriptionItemAddon() {
	}
	
	public SubscriptionItemAddon(SubscriptionItem subscriptionItem, int productId, String name, String type,
			String source, String dimension, String uom, BigDecimal quantity, String defaultSetting) {
		this.subscriptionItem = subscriptionItem;
		this.productId = productId;
		this.source = source;
		this.dimension = dimension;
		this.quantity = quantity;
		this.name = name;
		this.type = type;
		this.uom = uom;
		this.defaultSetting = defaultSetting;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)

	@Column(name = "SUBSCRIPTION_ITEM_ADDON_ID", unique = true, nullable = false)
	public Integer getSubscriptionItemAddonId() {
		return this.subscriptionItemAddonId;
	}

	public void setSubscriptionItemAddonId(Integer orderItemAddonId) {
		this.subscriptionItemAddonId = orderItemAddonId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SUBSCRIPTION_ITEM_ID", nullable = false)
	public SubscriptionItem getSubscriptionItem() {
		return this.subscriptionItem;
	}

	public void setSubscriptionItem(SubscriptionItem orderItem) {
		this.subscriptionItem = orderItem;
	}

	@Column(name = "ADDON_ID", nullable = false)
	public int getProductId() {
		return this.productId;
	}

	public void setProductId(int addonId) {
		this.productId = addonId;
	}

	@Column(name = "PRODUCT_SOURCE_SYSTEM", nullable = true)
	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	@Column(name = "DIMENSION", nullable = true)
	public String getDimension() {
		return dimension;
	}

	public void setDimension(String dimension) {
		this.dimension = dimension;
	}

	@Column(name = "QUANTITY", nullable = true)
	public BigDecimal getQuantity() {
		return quantity;
	}

	public void setQuantity(BigDecimal quantity) {
		this.quantity = quantity;
	}

	@Column(name = "ADDON_NAME", nullable = true)
	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	@Column(name = "ADDON_TYPE", nullable = true)
	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	@Column(name = "UNIT_OF_MEASURE", nullable = true)
	public String getUom() {
		return uom;
	}

	public void setUom(String uom) {
		this.uom = uom;
	}

	@Column(name = "DEFAULT_SETTING", nullable = true)
	public String getDefaultSetting() {
		return defaultSetting;
	}

	public void setDefaultSetting(String defaultSetting) {
		this.defaultSetting = defaultSetting;
	}
}
