package com.stpl.tech.kettle.stock.service.impl;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.stpl.tech.kettle.core.data.vo.StockOutReportData;
import com.stpl.tech.kettle.core.data.vo.UnitTimeStockData;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.dao.UnitInventoryDao;
import com.stpl.tech.kettle.data.model.PartnerUnitProductStockData;
import com.stpl.tech.kettle.data.model.ScmStockData;
import com.stpl.tech.kettle.data.model.StockDataUnitWise;
import com.stpl.tech.kettle.data.model.UnitProductStockData;
import com.stpl.tech.kettle.data.model.UnitProductStockEventAggregate;
import com.stpl.tech.kettle.data.model.UnitProductStockEventDataDetail;
import com.stpl.tech.kettle.data.mongo.stock.dao.SCMStockOutProductsDao;
import com.stpl.tech.kettle.data.mongo.stock.dao.UnitProductStockEventDataDao;
import com.stpl.tech.kettle.domain.model.InventoryInfo;
import com.stpl.tech.kettle.domain.model.StockOutScmProductData;
import com.stpl.tech.kettle.domain.model.UnitProductsStockEventData;
import com.stpl.tech.kettle.stock.dao.UnitProductStockDataDao;
import com.stpl.tech.kettle.stock.service.AutomatedStockEventReport;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductPrice;
import com.stpl.tech.master.domain.model.ProductStatus;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.inventory.StockStatus;
import com.stpl.tech.master.inventory.UnitProductsStockEvent;
import com.stpl.tech.master.inventory.model.InventoryData;
import com.stpl.tech.master.inventory.model.InventorySource;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.domain.adapter.DateDeserializer2;
import com.stpl.tech.util.endpoint.Endpoints;
import com.stpl.tech.util.excelparser.ExcelWriter;
import org.apache.poi.ss.usermodel.Workbook;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.servlet.View;
import org.springframework.web.servlet.view.document.AbstractXlsxView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.TreeMap;

@Service
public class AutomatedStockEventReportImpl implements AutomatedStockEventReport {

    private static final Logger LOG = LoggerFactory.getLogger(AutomatedStockEventReportImpl.class);

    @Autowired
    UnitProductStockDataDao dao;

    @Autowired
    UnitProductStockDataDao partnerStockDao;

    @Autowired
    UnitProductStockEventDataDao unitProductStockDataDao;

    @Autowired
    SCMStockOutProductsDao scmStockOutProductsDao;


    @Autowired
    MasterDataCache cache;

    @Autowired
    EnvironmentProperties props;
    

    UnitProductStockData addUnitProductStockData(UnitProductsStockEventData stockData, Date calculationDate, Date startTime, Date endTime, Integer downTime, Date fromStamp) {
        return new UnitProductStockData(AppUtils.getDate(stockData.getEventTimeStamp()), calculationDate, startTime,
                endTime, stockData.getUnitId(), stockData.getUnitName(),
                stockData.getProductId(), stockData.getProductName(), downTime, fromStamp,
                stockData.getEventTimeStamp(), AppUtils.getHour(fromStamp), ((AppUtils.getHour(stockData.getEventTimeStamp()) + 1) % 24),
                (int) AppUtils.getMinutesDifference(endTime, startTime), stockData.getDimension(),
                AppUtils.divideWithScale10(new BigDecimal(downTime), new BigDecimal(hoursDifference(AppUtils.getHour(fromStamp),
                        ((AppUtils.getHour(endTime) + 1) % 24)))), stockData.getBrandId());
    }

    UnitProductStockData addUnitProductStockDataAfterDayClose(UnitProductsStockEventData stockData, Date calculationDate, Date startTime, Date endTime, Integer downTime, Date fromStamp) {
        return new UnitProductStockData(AppUtils.getDate(stockData.getEventTimeStamp()), calculationDate, startTime,
                endTime, stockData.getUnitId(), stockData.getUnitName(), stockData.getProductId(), stockData.getProductName(), downTime, fromStamp,
                endTime, AppUtils.getHour(fromStamp), ((AppUtils.getHour(endTime) + 1) % 24),
                (int) AppUtils.getMinutesDifference(endTime, startTime), stockData.getDimension(),
                AppUtils.divideWithScale10(new BigDecimal(downTime), new BigDecimal(hoursDifference(AppUtils.getHour(fromStamp),
                        ((AppUtils.getHour(endTime) + 1) % 24)))), stockData.getBrandId());
    }

    PartnerUnitProductStockData addPartnerUnitProductStockData(UnitProductsStockEventData stockData, Date calculationDate, Date startTime, Date endTime, Integer downTime, Date fromStamp) {
        return new PartnerUnitProductStockData(AppUtils.getDate(stockData.getEventTimeStamp()), calculationDate, startTime,
                endTime, stockData.getUnitId(), stockData.getUnitName(),
                stockData.getProductId(), stockData.getProductName(), downTime, fromStamp,
                stockData.getEventTimeStamp(), AppUtils.getHour(fromStamp), ((AppUtils.getHour(stockData.getEventTimeStamp()) + 1) % 24),
                (int) AppUtils.getMinutesDifference(endTime, startTime), stockData.getDimension(),
                AppUtils.divideWithScale10(new BigDecimal(downTime), new BigDecimal(hoursDifference(AppUtils.getHour(fromStamp),
                        ((AppUtils.getHour(endTime) + 1) % 24)))), stockData.getBrandId());
    }

    PartnerUnitProductStockData addPartnerUnitProductStockDataAfterDayClose(UnitProductsStockEventData stockData, Date calculationDate, Date startTime, Date endTime, Integer downTime, Date fromStamp) {
        return new PartnerUnitProductStockData(AppUtils.getDate(stockData.getEventTimeStamp()), calculationDate, startTime,
                endTime, stockData.getUnitId(), stockData.getUnitName(), stockData.getProductId(), stockData.getProductName(), downTime, fromStamp,
                endTime, AppUtils.getHour(fromStamp), ((AppUtils.getHour(endTime) + 1) % 24),
                (int) AppUtils.getMinutesDifference(endTime, startTime), stockData.getDimension(),
                AppUtils.divideWithScale10(new BigDecimal(downTime), new BigDecimal(hoursDifference(AppUtils.getHour(fromStamp),
                        ((AppUtils.getHour(endTime) + 1) % 24)))), stockData.getBrandId());
    }

    private Integer hoursDifference(int startHour, int endHour) {
        if (endHour < startHour) {
            return ((24 - Math.abs(endHour - startHour)) * 60);
        } else {
            return (Math.abs(endHour - startHour) * 60);
        }
    }

    //    boolean checkDayCloseTime(UnitProductStockEventDataDetail unitProductsStockEventData, List<Date> unitTime) {
    boolean checkDayCloseTime(UnitProductsStockEventData unitProductsStockEventData, List<Date> unitTime) {
        return unitProductsStockEventData.getEventTimeStamp().after(unitTime.get(0))
                && unitProductsStockEventData.getEventTimeStamp().before(unitTime.get(1));
    }

    private List<InventoryInfo> getLiveInventory(Integer unitId) {
        long startTime = System.currentTimeMillis();
        String unitZone = cache.getUnitBasicDetail(unitId).getUnitZone();
        String endPoint = props.getInventoryServiceBasePath() + Endpoints.INVENTORY_SERVICE_ENTRY_POINT
                + (Objects.nonNull(unitZone) ? unitZone.toLowerCase() : AppConstants.DEFAULT_UNIT_ZONE) +
                Endpoints.INVENTORY_SERVICE_VERSION + Endpoints.GET_CAFE_INVENTORY_INFO;
        Map<String, String> params = new HashMap<>();
        params.put("unitId", String.valueOf(unitId));
        List<InventoryInfo> data = new ArrayList<>();
        try {
            List<?> list = WebServiceHelper.postRequestWithAuthInternalWithTimeout(endPoint, unitId, List.class, null);
            GsonBuilder gSonBuilder = new GsonBuilder().registerTypeAdapter(Date.class, new DateDeserializer2());
            list.forEach(p -> {
                Gson gson = gSonBuilder.create();
                String str = gson.toJson(p);
                InventoryInfo cat = gson.fromJson(str, InventoryInfo.class);
                if (cat != null) {
                    data.add(cat);
                }
            });
        } catch (Exception e) {
            LOG.error("Error while creating request for inventory for unit Id {}", unitId, e);
        }
        LOG.info("Inventory Data collected from inventory Service in {} miliseconds",
                System.currentTimeMillis() - startTime);
        return data;
    }

    private void getCurrentStock(Integer unitId, UnitProductsStockEvent stockIn , UnitProductsStockEvent stockOut)
            throws DataNotFoundException {

        Map<Integer, InventoryInfo> inventory = new HashMap<>();
        for (InventoryInfo info : getLiveInventory(unitId)) {
            inventory.put(info.getId(), info);
        }
        if (inventory != null && !inventory.keySet().isEmpty()) {
            for (Integer productId : inventory.keySet()) {
                InventoryInfo inventoryInfo = new Gson().fromJson(new Gson().toJson(inventory.get(productId)), InventoryInfo.class);
                int stock = inventoryInfo.getQuantity();
                if (stock > 0) {
                    stockIn.getProductIds().add(String.valueOf(productId));
                } else {
                    stockOut.getProductIds().add(String.valueOf(productId));
                }
                if(inventoryInfo.getDim() != null) {
                    inventoryInfo.getDim().forEach((dimension, stockCount) -> {
                        if (stockCount > 0) {
                            stockIn.getProductIds().add(productId + dimension);
                            //stockIn.getProductDimensions().add(new IdName(productId, dimension));
                        } else {
                            stockOut.getProductIds().add(productId + dimension);
                            //stockOut.getProductDimensions().add(new IdName(productId, dimension));
                        }
                    });
                }
            }
        }


    }

    private UnitProductStockEventDataDetail getMissingStockUpdateEvent(Integer productId , String dimension , Integer unitId , Integer brandId,
                                                                       String stockEventStatus,String unitName , Date time , String productName){
        UnitProductStockEventDataDetail unitProductStockEventDataDetail =  new UnitProductStockEventDataDetail();
        unitProductStockEventDataDetail.setBrandId(brandId);
        unitProductStockEventDataDetail.setUnitId(unitId);
        unitProductStockEventDataDetail.setUnitName(unitName);
        unitProductStockEventDataDetail.setStatus(stockEventStatus);
        unitProductStockEventDataDetail.setProductId(productId);
        unitProductStockEventDataDetail.setDimension(dimension);
        unitProductStockEventDataDetail.setEventType("MISSING_EVENT_AMENDMENT");
        unitProductStockEventDataDetail.setEventTimeStamp(time);
        unitProductStockEventDataDetail.setProductName(productName);
        return unitProductStockEventDataDetail;

    }


    private Map<Integer,Map<Date,Boolean>> getScmStockOutMapByTime(List<StockOutScmProductData> stockOutScmProductDataList){
        Map<Integer,Map<Date,Boolean>> scmStockOutMap = new HashMap<>();
        Map<Boolean,Integer> lastStockOutMap = new HashMap<>();
        lastStockOutMap.put(Boolean.FALSE,-1);
        for(StockOutScmProductData stockOutScmProductData : stockOutScmProductDataList){
            if(Boolean.FALSE.equals(stockOutScmProductData.isStockOut())){
                continue;
            }
            for(InventoryData inventoryData : stockOutScmProductData.getInventoryData()){
                if(inventoryData.getQty().compareTo(BigDecimal.ZERO)<=0){
                    if(!scmStockOutMap.containsKey(inventoryData.getId())){
                        scmStockOutMap.put(inventoryData.getId(),new TreeMap<Date,Boolean>((o1, o2) -> o1.compareTo(o2)));
                    }
                    if(!lastStockOutMap.get(Boolean.FALSE).equals(inventoryData.getId())){
                        scmStockOutMap.get(inventoryData.getId()).put(stockOutScmProductData.getId().getDate(),Boolean.FALSE);
                        lastStockOutMap.put(Boolean.FALSE,inventoryData.getId());
                    }
                }
            }
        }
        return scmStockOutMap;
    }

    private Map<Integer,Map<Date,Boolean>> getScmStockInMapByTime(Integer unitId,Date date, Date lastDayCloseTime , Date currentDayCloseTime){
        Map<Integer,Map<Date,Boolean>> scmStockInMap = new HashMap<>();
        LOG.info("unit id : {} , last day close time : {} , current day close time : {} ", unitId,lastDayCloseTime,currentDayCloseTime);
        scmStockInMap = dao.getStockInSCMData(unitId,date,lastDayCloseTime,currentDayCloseTime);
        return scmStockInMap;
    }

    private Map<Integer,Boolean> getScmInitialStatusMap(Integer unitId, Date date){
        return dao.getScmInitialStatusMap(unitId,date);
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void saveScmStockOutData(Integer unitId , Date businessDate , Integer brandId ,
                                     Date cafeStartTime , Date cafeCloseTime){
        try {
            List<StockOutScmProductData> stockOutScmProductDataList = scmStockOutProductsDao.findByAndUnitIdAndIdBetween(unitId,
                    new ObjectId(AppUtils.getStartOfBusinessDay(businessDate)), new ObjectId(AppUtils.getEndOfBusinessDay(businessDate)));
            Map<Integer,Map<Date,Boolean>> scmStockOutMap = getScmStockOutMapByTime(stockOutScmProductDataList);
            Map<Integer,Boolean> scmInitialMap = getScmInitialStatusMap(unitId,businessDate);
            Pair<Date,Date> currentAndLastDayCLoseTime =   dao.getCurrentAndLastDayCLoseTime(unitId,businessDate);
            Map<Integer,Map<Date,Boolean>> scmStockInMap = getScmStockInMapByTime(unitId,businessDate,currentAndLastDayCLoseTime.getValue(),
                    currentAndLastDayCLoseTime.getKey());
            Map<Integer,BigDecimal> scmProductDownTimeMap = new HashMap<>();
            Map<Integer, Pair<Date,Boolean>> currentStockMap = new HashMap<>();
            LOG.info("scmStockOutMap : {} " , new Gson().toJson(scmStockOutMap));
            LOG.info("scmStockInMap : {} " , new Gson().toJson(scmStockInMap));
            LOG.info("scmInitialMap : {} " , new Gson().toJson(scmInitialMap));
            List<ScmStockData> scmStockDataList = new ArrayList<>();
            for(Integer scmProductId : scmInitialMap.keySet()){
                try{
                    currentStockMap.put(scmProductId,new Pair<>(cafeStartTime,scmInitialMap.get(scmProductId)));
                    scmProductDownTimeMap.put(scmProductId, BigDecimal.ZERO);
                    if(scmStockInMap.containsKey(scmProductId)){
                        Map<Date,Boolean> productStockInMap  = scmStockInMap.get(scmProductId);
                        for(Date date1 : productStockInMap.keySet()){
                            if(date1.compareTo(cafeStartTime) >=0){
                                break;
                            }
                            currentStockMap.put(scmProductId,new Pair<>(date1,productStockInMap.get(date1)));
                        }
                    }
                    if(scmStockOutMap.containsKey(scmProductId)){
                        Map<Date,Boolean> productStockOutMap  = scmStockOutMap.get(scmProductId);
                        for(Date date1 : productStockOutMap.keySet()){
                            if(date1.compareTo(cafeStartTime) >=0){
                                break;
                            }
                            if(currentStockMap.containsKey(scmProductId) && date1.compareTo(currentStockMap.get(scmProductId).getKey()) > 0){
                                currentStockMap.put(scmProductId,new Pair<>(date1,productStockOutMap.get(date1)));
                            }
                        }
                    }
                    if(scmStockInMap.containsKey(scmProductId)){
                        LOG.info("product {] Stock in map : {} ", scmProductId,new Gson().toJson(scmStockInMap.get(scmProductId)));
                    }
                    if(scmStockOutMap.containsKey(scmProductId)){
                        LOG.info("product : {}  Stock out map : {} ",scmProductId ,new Gson().toJson(scmStockOutMap.get(scmProductId)));
                    }

                    Map<Date,Boolean> mergedScmStockMap = mergeStockInStockOutMaps(scmStockInMap.get(scmProductId),scmStockOutMap.get(scmProductId),
                            cafeStartTime,cafeCloseTime);

                    LOG.info("Merged Map for Product : {} : {}",scmProductId, new Gson().toJson(mergedScmStockMap));

                    for(Date date1 : mergedScmStockMap.keySet()){
                        if(!mergedScmStockMap.get(date1).equals(currentStockMap.get(scmProductId).getValue())){
                            if(Boolean.TRUE.equals(mergedScmStockMap.get(date1))){
                                BigDecimal currentDownTime =  scmProductDownTimeMap.get(scmProductId);
                                BigDecimal newDownTime = AppUtils.add(currentDownTime  ,
                                        AppUtils.divide(BigDecimal.valueOf(AppUtils.getDateDifferenceInMinutes(
                                                currentStockMap.get(scmProductId).getKey(),date1)),BigDecimal.valueOf(60))) ;
                                scmProductDownTimeMap.put(scmProductId,newDownTime);
                            }else if(Boolean.FALSE.equals(mergedScmStockMap.get(date1))){
                                //Do nothing
                            }
                            currentStockMap.put(scmProductId,new Pair<>(date1,mergedScmStockMap.get(date1)));
                        }
                    }
                    if(Boolean.FALSE.equals(currentStockMap.get(scmProductId).getValue())){
                        BigDecimal currentDownTime =  scmProductDownTimeMap.get(scmProductId);
                        BigDecimal newDownTime = AppUtils.add(currentDownTime  ,
                                AppUtils.divide(BigDecimal.valueOf(AppUtils.getDateDifferenceInMinutes(
                                        currentStockMap.get(scmProductId).getKey(),cafeCloseTime)),BigDecimal.valueOf(60))) ;
                        scmProductDownTimeMap.put(scmProductId,newDownTime);
                    }
                    LOG.info("Downtime Map for Product : {} : {}",scmProductId, new Gson().toJson(scmProductDownTimeMap.get(scmProductId)));
                    if(scmProductDownTimeMap.get(scmProductId).compareTo(BigDecimal.ZERO)>=0){
                        scmStockDataList.add(ScmStockData.builder().productId(scmProductId).brandId(brandId).businessDate(businessDate)
                                .totalDownTime(scmProductDownTimeMap.get(scmProductId)).cafeOpeningTime(cafeStartTime)
                                .cafeClosingTime(cafeCloseTime).cafeOperationHours(AppUtils.divide(BigDecimal.valueOf(AppUtils.getDateDifferenceInMinutes(
                                        cafeStartTime,cafeCloseTime)),BigDecimal.valueOf(60))).unitId(unitId).build());
                    }
                }catch (Exception e){
                    LOG.error("Error While Calculating Stock Data For SCM Product Id : {} , Date : {}  : {} ", scmProductId, businessDate,e);
                }

            }
            if(!scmStockDataList.isEmpty()){
                dao.addAll(scmStockDataList);
            }
        }catch (Exception e){
            LOG.error("Error While Processing SCM Stock out data : {}" , e);
        }

    }

    public Map<Date, Boolean> mergeStockInStockOutMaps(Map<Date, Boolean> stockInMap, Map<Date, Boolean> stockOutMap, Date cafeStartTime
    , Date cafeCloseTime) {
        if (Objects.isNull(stockInMap) || stockInMap.isEmpty()) {
            return filterAndSortMap(stockOutMap, cafeStartTime,cafeCloseTime);
        }
        if (Objects.isNull(stockOutMap) || stockOutMap.isEmpty()) {
            return filterAndSortMap(stockInMap,cafeStartTime,cafeCloseTime);
        }

        Map<Date, Boolean> mergedMap = new TreeMap<>();
        mergedMap.putAll(stockInMap);
        mergedMap.putAll(stockOutMap);

        return filterAndSortMap(mergedMap, cafeStartTime,cafeCloseTime);
    }

    private Map<Date, Boolean> filterAndSortMap(Map<Date, Boolean> map, Date cafeStartTime , Date cafeCloseTime) {
        Map<Date, Boolean> filteredMap = new TreeMap<>();
        if(CollectionUtils.isEmpty(map)){
            return filteredMap;
        }
        for (Map.Entry<Date, Boolean> entry : map.entrySet()) {
            if (entry.getKey().after(cafeStartTime) && entry.getKey().before(cafeCloseTime)) {
                filteredMap.put(entry.getKey(), entry.getValue());
            }
        }

        return filteredMap;
    }





    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public StockOutReportData execute(Date previousDate, Integer unitId,
                                      boolean saveResults, List<Date> unitTime, boolean partner, Integer brand, UnitTimeStockData unitTimeStockData,
                                      Map<Integer, Map<String, String>> lpse) {
        try {
            if (previousDate != null && unitId != null && unitTime != null) {
                Unit unitData = cache.getUnit(unitId);
                UnitProductsStockEvent stockIn = new UnitProductsStockEvent(unitId, StockStatus.STOCK_IN);
                UnitProductsStockEvent stockOut = new UnitProductsStockEvent(unitId, StockStatus.STOCK_OUT);
                getCurrentStock(unitId,stockIn,stockOut);

//                List<UnitProductStockEventDataDetail> stockData = unitProductStockDataDao
//                        .findAllByUnitIdAndEventTimeStampOrderByProductIdAsc(unitId, previousDate);
                List<UnitProductsStockEventData> stockData = unitProductStockDataDao
                        .findAllByUnitIdAndBrandIdAndEventTimeStampBetweenOrderByProductIdAscDimensionAscEventTimeStampAsc(unitId, brand,
                                AppUtils.getStartOfBusinessDay(previousDate), AppUtils.getStartOfBusinessDay(AppUtils.getDayBeforeOrAfterDay(previousDate, 1)));
//                Map<String, PartnerUnitProductStockData> previousDayStockData =
//                        partnerStockDao.getPreviousDayStockData(unitId, AppUtils.getDayBeforeOrAfterDay(AppUtils.getCurrentDate(), -2));
                Map<String, List<UnitProductsStockEventData>> productIdsInStockData = new HashMap<>();
                for(UnitProductsStockEventData sd : stockData){
                    String key = sd.getProductId()+sd.getDimension();
                    if(!productIdsInStockData.containsKey(key)){
                        productIdsInStockData.put(key, new ArrayList<>());
                    }
                    productIdsInStockData.get(key).add(sd);
                }
                List<UnitProductStockEventDataDetail> missingStockInEvents = new ArrayList<>();
                int totalSkus = 0;
                for(Product product : cache.getUnitProductDetails(unitId)){
                    if(!brand.equals(product.getBrandId()) || !product.isInventoryTracked() || !ProductStatus.ACTIVE.equals(product.getStatus())){
                        continue;
                    }
                    for(ProductPrice price : product.getPrices()){
                        totalSkus+=1;
                        String key = product.getId()+price.getDimension();
                        if(!productIdsInStockData.containsKey(key) &&
                                AppConstants.STOCK_OUT_CONSTANT.equals(statusForProduct(product.getId(), price.getDimension(), lpse))){
                            if(stockIn.getProductIds().contains(key) || (price.getDimension().equals(AppConstants.DIMENSION_NONE) &&
                                    stockIn.getProductIds().contains(String.valueOf(product.getId())))){
                                productIdsInStockData.put(key, new ArrayList<>());
                                productIdsInStockData.get(key).add(new UnitProductsStockEventData(unitId,unitData.getName(), product.getId(),
                                        product.getName(), AppConstants.STOCK_IN_CONSTANT,AppUtils.addSeconds(unitTime.get(0), 60),
                                        price.getDimension(),brand, InventorySource.CAFE_ORDER.name()));
                                missingStockInEvents.add(getMissingStockUpdateEvent(product.getId(),price.getDimension(),unitId,brand,
                                        StockStatus.STOCK_IN.toString(),unitData.getName(),AppUtils.addSeconds(unitTime.get(0), 60),product.getName()));
                            }else{
                                productIdsInStockData.put(key, new ArrayList<>());
                                productIdsInStockData.get(key).add(new UnitProductsStockEventData(unitId,unitData.getName(), product.getId(),
                                        product.getName(), AppConstants.STOCK_OUT_CONSTANT,AppUtils.addSeconds(unitTime.get(0), 60),
                                        price.getDimension(),brand, InventorySource.CAFE_ORDER.name()));
                            }
                        }
                    }

                }
                if(!CollectionUtils.isEmpty(missingStockInEvents)){
                    LOG.info("Adding {} missing Stock In Events For Unit Id :: {} ", missingStockInEvents.size(),unitId);
                    dao.addAll(missingStockInEvents);
                }
                stockData = new ArrayList<>();
                for(Map.Entry<String, List<UnitProductsStockEventData>> map : productIdsInStockData.entrySet()){
                    stockData.addAll(map.getValue());
                }
                List<UnitProductStockData> stockOutEventData = new ArrayList<>();
                List<PartnerUnitProductStockData> partnerStockOutEventData = new ArrayList<>();
                if (stockData != null) {
                    LOG.info("Success for GET request for Unit {}", unitId);
                } else {
                    return null;
                }
                int downtime;
                Date fromStamp = new Date();
                Date fromStampCheck = new Date();
                for (int i = 0; i < stockData.size(); i++) {
                    LOG.info("opening : {} and closing is :{}",unitTime.get(0),unitTime.get(1));
                    LOG.info("status is : {} and unit :{} product id :{} dimesion :{} and eventTime is : {}",stockData.get(i).getStatus(),stockData.get(i).getUnitId(),stockData.get(i).getProductId(),stockData.get(i).getDimension(),stockData.get(i).getEventTimeStamp());
                    try {
                        if (stockData.get(i).getBrandId() == null) {
                            int brandId = getBrandId(stockData, i);
                            stockData.get(i).setBrandId(brandId);
                        }
                        if (stockData.get(i).getDimension() == null) {
                            String dimensionName = getDimension(stockData, i);
                            stockData.get(i).setDimension(dimensionName);
                        }
                        if(stockData.size() > 1 && i!=0){
                            if(!stockData.get(i).getProductId().equals(stockData.get(i - 1).getProductId())
                                    || !stockData.get(i).getDimension().equals(stockData.get(i - 1).getDimension())
                                    || !stockData.get(i).getBrandId().equals(stockData.get(i - 1).getBrandId())){
                                fromStamp = unitTime.get(0);
                                LOG.info("Changed from stamp for new product or dimension or brand");
                            }
                        }
                        if (stockData.get(i).getStatus().equals(AppConstants.STOCK_IN_CONSTANT)
                                && checkDayCloseTime(stockData.get(i), unitTime)) {
                            if (i == 0) {
                                continue;
                            }
                            if (stockData.get(i - 1).getStatus().equals(AppConstants.STOCK_IN_CONSTANT)
                                    && stockData.get(i).getProductId().equals(stockData.get(i - 1).getProductId())
                                    && stockData.get(i).getDimension().equals(stockData.get(i - 1).getDimension())
                                    && stockData.get(i).getBrandId().equals(stockData.get(i - 1).getBrandId())) {
                                continue;
                            }
                            if (stockData.get(i - 1).getStatus().equals(AppConstants.STOCK_OUT_CONSTANT)
                                    && stockData.get(i).getProductId().equals(stockData.get(i - 1).getProductId())
                                    && stockData.get(i).getDimension().equals(stockData.get(i - 1).getDimension())
                                    && stockData.get(i).getBrandId().equals(stockData.get(i - 1).getBrandId())) {
                                if (fromStampCheck.equals(fromStamp)) {
                                    fromStamp = unitTime.get(0);
                                }
                                downtime = (int) (AppUtils.getMinutesDifference(fromStamp, stockData.get(i).getEventTimeStamp()));
                                if(!partner){
                                    stockOutEventData.add(addUnitProductStockData(stockData.get(i), AppUtils.getCurrentTimestamp(), unitTime.get(0),
                                            unitTime.get(1), downtime, fromStamp));
                                }else {
                                    partnerStockOutEventData.add(addPartnerUnitProductStockData(stockData.get(i), AppUtils.getCurrentTimestamp(), unitTime.get(0),
                                            unitTime.get(1), downtime, fromStamp));
                                }
                                fromStamp = unitTime.get(0);
                            }
                        } else if (stockData.get(i).getStatus().equals(AppConstants.STOCK_OUT_CONSTANT)
                                && checkDayCloseTime(stockData.get(i), unitTime)) {
                            if (i == 0) {
                                fromStamp = stockData.get(i).getEventTimeStamp();
                            }
                            if (i > 0 && stockData.get(i - 1).getStatus().equals(AppConstants.STOCK_OUT_CONSTANT)
                                    && (stockData.get(i).getProductId().equals(stockData.get(i - 1).getProductId())
                                    && stockData.get(i).getDimension().equals(stockData.get(i - 1).getDimension()))
                                    && stockData.get(i).getBrandId().equals(stockData.get(i - 1).getBrandId())) {
                                continue;
                            }
                            if (i > 0 && (!stockData.get(i).getProductId().equals(stockData.get(i - 1).getProductId())
                                    || !stockData.get(i).getDimension().equals(stockData.get(i - 1).getDimension())
                                    || !stockData.get(i).getBrandId().equals(stockData.get(i - 1).getBrandId()))) {
                                fromStamp = stockData.get(i).getEventTimeStamp();
                            }
                            if (i > 0 && stockData.get(i).getProductId().equals(stockData.get(i - 1).getProductId())
                                    && stockData.get(i).getDimension().equals(stockData.get(i - 1).getDimension())
                                    && stockData.get(i).getBrandId().equals(stockData.get(i - 1).getBrandId())) {
                                fromStamp = stockData.get(i).getEventTimeStamp();
                            }

//                            if ((i == stockData.size()) || (i < stockData.size() - 1 && (!stockData.get(i).getProductId().equals(stockData.get(i + 1).getProductId())
//                                    || !stockData.get(i).getDimension().equals(getDimension(stockData, i + 1))
//                                    || !stockData.get(i).getBrandId().equals(getBrandId(stockData, i + 1))))) {
//
//
//                                fromStamp = createStockDataAfterDayClose(unitTime, stockData, stockOutEventData, i);
//                            }

                            if((i == stockData.size())){
                                fromStamp = createStockDataAfterDayClose(unitTime, stockData, stockOutEventData,partnerStockOutEventData, i,partner);
                            }
                            else if((i < stockData.size() - 1)){
                                if(!stockData.get(i).getProductId().equals(stockData.get(i + 1).getProductId()) || !checkDayCloseTime(stockData.get(i+1), unitTime)){
                                    fromStamp = createStockDataAfterDayClose(unitTime, stockData, stockOutEventData,partnerStockOutEventData, i, partner);
                                }
                                else{
                                    if(!stockData.get(i).getDimension().equals(getDimension(stockData, i + 1)) || !checkDayCloseTime(stockData.get(i+1), unitTime)){
                                        fromStamp = createStockDataAfterDayClose(unitTime, stockData, stockOutEventData,partnerStockOutEventData, i, partner);
                                    }
                                    else{
                                        if(!stockData.get(i).getBrandId().equals(getBrandId(stockData, i + 1)) || !checkDayCloseTime(stockData.get(i+1), unitTime)){
                                            fromStamp = createStockDataAfterDayClose(unitTime, stockData, stockOutEventData,partnerStockOutEventData, i, partner);
                                        }
                                    }
                                }

                            }
                        }else{
                            LOG.info("Not in dayclose threshold for status is : {} and unit :{} product id :{} dimesion :{} and eventTime is : {}",stockData.get(i).getStatus(),stockData.get(i).getUnitId(),stockData.get(i).getProductId(),
                                    stockData.get(i).getDimension(),stockData.get(i).getEventTimeStamp());
                        }
                    } catch (Exception e) {
                        LOG.error("Exception Caught While Generating Data for product Id ::{}", stockData.get(i).getProductId(), e);
                    }
                }

                StockOutReportData data = new StockOutReportData();
                if(AppConstants.GNT_BRAND_ID ==  brand && !partner){
                    saveScmStockOutData(unitId,previousDate,brand,AppUtils.getGntOpeningTime(previousDate),AppUtils.getGntClosingTime(previousDate));
                }
                if (saveResults) {
                    LOG.info("Loading Data into Database");
                    List<UnitProductStockData> stockReport = dao.addAll(stockOutEventData);
                    partnerStockDao.addAll(partnerStockOutEventData);
                    if(!partner && Objects.nonNull(unitTimeStockData)){
                        Integer totalDownTimeUnit = stockOutEventData.stream().mapToInt(UnitProductStockData::getDownTime).reduce(0, Integer::sum);
                        Integer dineInOperationalTime = (int) AppUtils.getMinutesDifference(
                                unitTimeStockData.getDineInTime().get(0),unitTimeStockData.getDineInTime().get(1));
                        Integer deliveryOperationalTime = (int) AppUtils.getMinutesDifference(
                                unitTimeStockData.getDeliveryTime().get(0),unitTimeStockData.getDeliveryTime().get(1));
                        partnerStockDao.add(StockDataUnitWise.builder()
                                .brandId(brand)
                                .businessDate(previousDate)
                                .unitId(unitId)
                                .cafeOpeningTime(unitTimeStockData.getDineInTime().get(0))
                                .cafeClosingTime(unitTimeStockData.getDineInTime().get(1))
                                .deliveryOpeningTime(unitTimeStockData.getDeliveryTime().get(0))
                                .deliveryClosingTime(unitTimeStockData.getDeliveryTime().get(1))
                                .noOfSku(totalSkus)
                                .dineInOperationalTime(dineInOperationalTime)
                                .deliveryOperationalTime(deliveryOperationalTime)
                                .totalDineInOperationalTime(totalSkus * dineInOperationalTime)
                                .totalDeliveryOperationalTime(totalSkus * deliveryOperationalTime)
                                .totalDineInDownTime(totalDownTimeUnit)
                                .isTwentyFourCafe(AppUtils.setStatus(unitTimeStockData.is24HoursCafe())).build());
                    }
                    data.setEvents(stockReport);
                    return data;
                } else {
                    LOG.info("Requesting Data for Excel Sheet Creation");
                    data.setEvents(stockOutEventData);
                    return data;
                }
            }
        } catch (Exception e) {
            LOG.error("Exception Faced : ",e);
        }
        return null;
    }

    private String statusForProduct(int productId, String dimension, Map<Integer, Map<String, String>> pds){
        if(Objects.nonNull(pds) && !pds.isEmpty() && pds.containsKey(productId) && pds.get(productId).containsKey(dimension)){
            return pds.get(productId).get(dimension);
        }
        return AppConstants.STOCK_IN_CONSTANT;
    }

    private Date createStockDataAfterDayClose(List<Date> unitTime, List<UnitProductsStockEventData> stockData, List<UnitProductStockData> stockOutEventData, List<PartnerUnitProductStockData> partnerStockOutEventData, int i, boolean partner) {
        int downtime;
        Date fromStamp;
        downtime = (int) AppUtils.getMinutesDifference(stockData.get(i).getEventTimeStamp(), unitTime.get(1));
        if(!partner) {
            stockOutEventData.add(addUnitProductStockDataAfterDayClose(stockData.get(i), AppUtils.getCurrentTimestamp(), unitTime.get(0),
                    unitTime.get(1), downtime, stockData.get(i).getEventTimeStamp()));
        }
        else {
            partnerStockOutEventData.add(addPartnerUnitProductStockDataAfterDayClose(stockData.get(i), AppUtils.getCurrentTimestamp(), unitTime.get(0),
                    unitTime.get(1), downtime, stockData.get(i).getEventTimeStamp()));
        }
        fromStamp = unitTime.get(0);
        return fromStamp;
    }

    private int getBrandId(List<UnitProductsStockEventData> stockData, int i) {
        if(stockData.get(i).getBrandId() !=null){
            return stockData.get(i).getBrandId();
        }
        int brandId = AppConstants.CHAAYOS_BRAND_ID;
        try {
            brandId = cache.getProduct(stockData.get(i).getProductId()).getBrandId();
        } catch (Exception e) {
            LOG.error("Brand Not Found :: {}", stockData.get(i).getProductId());
        }
        return brandId;
    }

    private String getDimension(List<UnitProductsStockEventData> stockData, int i) {
        if(stockData.get(i).getDimension() !=null){
            return stockData.get(i).getDimension();
        }
        String dimensionName = "None";
        try {
            dimensionName = (cache.getDimensionProfile(cache.getProduct(stockData.get(i).getProductId()).getDimensionProfileId()).getContent() != null &&
                    !cache.getDimensionProfile(cache.getProduct(stockData.get(i).getProductId()).getDimensionProfileId()).getContent().isEmpty()) ?
                    cache.getDimensionProfile(cache.getProduct(stockData.get(i).getProductId()).getDimensionProfileId()).getContent().get(0).getCode() : "None";
        } catch (Exception e) {
            LOG.error("Dimension Not Found {}", stockData.get(i).getProductId());
        }
        stockData.get(i).setDimension(dimensionName);
        return dimensionName;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public View executeForDownload(Date startDate, Date endDate, List<Integer> unitId, Date calculationDate,
                                   boolean saveResults) {
        try {
            List<UnitProductStockData> unitProductsStockData = new ArrayList<>();

            List<UnitProductStockEventAggregate> unitProductStockEventAggregates = new ArrayList<>();
            Map<Integer, UnitTimeStockData> unitTime = partnerStockDao.getUnitClosingTimeMap(startDate);
            for (Integer unitID : unitId) {
                LOG.info("Code Execution for " + unitID);

                StockOutReportData stockOutReportData = execute(startDate, unitID, saveResults, unitTime.get(unitID).getDineInTime(), false,0, null, null);
                if (stockOutReportData.getEvents() != null) {
                    unitProductsStockData.addAll(stockOutReportData.getEvents());
                }
                if (stockOutReportData.getAggregate() != null) {
                    unitProductStockEventAggregates.addAll(stockOutReportData.getAggregate());
                }
            }
            StockOutReportData data = new StockOutReportData();
            data.setEvents(unitProductsStockData);
            data.setAggregate(unitProductStockEventAggregates);

            LOG.info("Creating Excel for downloading");
            LOG.info("unitProductsStockData List Size - " + unitProductsStockData.size());
            LOG.info("unitProductStockEventAggregates List Size - " + unitProductStockEventAggregates.size());
            return new RenderExcelView(unitProductsStockData, unitProductStockEventAggregates);
        } catch (Exception e) {
            LOG.error("Exception Faced :", e);
        }
        return null;
    }

    @Override
    public Map<Integer, UnitTimeStockData> getUnitClosingTimeMap(Date previousDate) {
        return partnerStockDao.getUnitClosingTimeMap(previousDate);
    }

    @Override
    public Map<Integer, Map<Integer, Map<String, String>>> getLatestPreviousStockEvent(Date eventTimestamp) {
        return partnerStockDao.getLatestPreviousStockEvent(AppUtils.getStartOfBusinessDay(eventTimestamp));
    }


    private static class RenderExcelView extends AbstractXlsxView {

        List<UnitProductStockData> unitProductsStockData;
        List<UnitProductStockEventAggregate> unitProductStockEventAggregates;

        public RenderExcelView(List<UnitProductStockData> unitProductsStockData,
                               List<UnitProductStockEventAggregate> unitProductStockEventAggregates) {
            super();
            this.unitProductsStockData = unitProductsStockData;
            this.unitProductStockEventAggregates = unitProductStockEventAggregates;
        }

        @Override
        protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest request,
                                          HttpServletResponse response) throws Exception {
            try {
                LOG.info("Inside Building The excel");
                response.addHeader("Content-Disposition", "attachment; filename=\"StockReportSheet.xlsx\"");
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                ExcelWriter writer = new ExcelWriter(workbook);
                List<UnitProductStockEventAggregate> l = new ArrayList<>();
                Integer downTimeAggr = 0;
                Integer oprationTimeAggr = 0;
                List<UnitProductStockData> s = new ArrayList<>();
                for (UnitProductStockData stock : unitProductsStockData) {
                    s.add(stock);
                }
                writer.writeSheet(s, UnitProductStockData.class);
                for (UnitProductStockEventAggregate aggregate : unitProductStockEventAggregates) {
                    l.add(aggregate);
                    downTimeAggr = downTimeAggr + aggregate.getDownTime();
                    oprationTimeAggr = oprationTimeAggr + aggregate.getOperationTime();
                }
                l.add(new UnitProductStockEventAggregate(new Date(), downTimeAggr, oprationTimeAggr));
                writer.writeSheet(l, UnitProductStockEventAggregate.class);
                LOG.info("Completed Building The excel");
            } catch (Exception e) {
                LOG.error("Error in building excel", e);
            }
        }
    }
}
