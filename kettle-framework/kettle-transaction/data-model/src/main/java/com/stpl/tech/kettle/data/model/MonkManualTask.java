package com.stpl.tech.kettle.data.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import java.util.HashSet;
import java.util.Set;

/**
 * Model for logging manual tasks performed by monks
 */
@Entity
@Table(name = "MONK_MANUAL_TASK")
@Getter
@Setter
public class MonkManualTask {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "MONK_MANUAL_TASK_ID")
    private Integer monkManualTaskId;

    @Column(name = "ORDER_ID")
    private String orderId;

    @Column(name = "UNIT_ID")
    private Integer unitId;

    @Column(name = "EMPLOYEE_ID")
    private Integer employeeId;

    @Column(name = "PRODUCT_ID")
    private Integer productId;

    @Column(name = "DIMENSION")
    private String dimension;

    @Column(name = "QUANTITY")
    private Integer quantity;

    @OneToMany(mappedBy = "monkManualTask", cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<MonkManualTaskAddon> addons = new HashSet<>();

    // Helper method to add an addon and set its reference to this task
    public void addAddon(MonkManualTaskAddon addon) {
        if (addon != null) {
            addon.setMonkManualTask(this);
            this.addons.add(addon);
        }
    }

    // Helper method to add multiple addons
    public void addAddons(Set<MonkManualTaskAddon> addons) {
        if (addons != null) {
            for (MonkManualTaskAddon addon : addons) {
                addAddon(addon);
            }
        }
    }
} 