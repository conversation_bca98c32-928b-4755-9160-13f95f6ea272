/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

@Entity
@Table(name = "EXPENSE_UPDATE_EVENT_DATA")
public class ExpenseUpdateEventData {

	private int eventId;
	private int addedByUserId;
	private String addedByUserName;
	private int updatedByUserId;
	private String updatedByUserName;
	private Date eventTimestamp;
	private String eventDescription;
	private String type;
	private int year;
	private int iterationNumber;
	private String status;
	private int noOfRows;
	private int startOrderId;
	private int endOrderId;
	private String inputFileName;
	private String storedFileName;
	private String errorMessage;
	private Date startDate;
	private Date endDate;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "EVENT_ID", unique = true, nullable = false)
	public int getEventId() {
		return eventId;
	}

	public void setEventId(int eventId) {
		this.eventId = eventId;
	}

	@Column(name = "ADDED_BY_USER_ID", nullable = false)
	public int getAddedByUserId() {
		return addedByUserId;
	}

	public void setAddedByUserId(int userId) {
		this.addedByUserId = userId;
	}

	@Column(name = "ADDED_BY_USER_NAME", nullable = true)
	public String getAddedByUserName() {
		return addedByUserName;
	}

	public void setAddedByUserName(String addedByUserName) {
		this.addedByUserName = addedByUserName;
	}

	@Column(name = "UPDATED_BY_USER_ID", nullable = false)
	public int getUpdatedByUserId() {
		return updatedByUserId;
	}

	public void setUpdatedByUserId(int updatedByUserId) {
		this.updatedByUserId = updatedByUserId;
	}

	@Column(name = "UPDATED_BY_USER_NAME", nullable = true)
	public String getUpdatedByUserName() {
		return updatedByUserName;
	}

	public void setUpdatedByUserName(String updatedByUserName) {
		this.updatedByUserName = updatedByUserName;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "EVENT_TIMESTAMP", nullable = false, length = 19)
	public Date getEventTimestamp() {
		return eventTimestamp;
	}

	public void setEventTimestamp(Date eventTimestamp) {
		this.eventTimestamp = eventTimestamp;
	}

	@Column(name = "EVENT_DESCRIPTION", nullable = false, length = 1000)
	public String getEventDescription() {
		return eventDescription;
	}

	public void setEventDescription(String eventDescription) {
		this.eventDescription = eventDescription;
	}

	@Column(name = "ENTRY_TYPE", nullable = false)
	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	@Column(name = "ENTRY_YEAR", nullable = false)
	public int getYear() {
		return year;
	}

	public void setYear(int year) {
		this.year = year;
	}

	@Column(name = "ITERATION_NUMBER", nullable = false)
	public int getIterationNumber() {
		return iterationNumber;
	}

	public void setIterationNumber(int iterationNumber) {
		this.iterationNumber = iterationNumber;
	}

	@Column(name = "EVENT_STATUS", nullable = false)
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@Column(name = "NO_OF_ROWS", nullable = false)
	public int getNoOfRows() {
		return noOfRows;
	}

	public void setNoOfRows(int noOfRows) {
		this.noOfRows = noOfRows;
	}

	@Column(name = "INPUT_FILE_NAME", length = 250, nullable = false)
	public String getInputFileName() {
		return inputFileName;
	}

	public void setInputFileName(String inputFilePath) {
		this.inputFileName = inputFilePath;
	}

	@Column(name = "STORED_FILE_NAME", length = 250, nullable = false)
	public String getStoredFileName() {
		return storedFileName;
	}

	public void setStoredFileName(String storedFileName) {
		this.storedFileName = storedFileName;
	}

	@Column(name = "ERROR_MESSAGE", length = 10000, nullable = true)
	public String getErrorMessage() {
		return errorMessage;
	}

	public void setErrorMessage(String errorMessage) {
		this.errorMessage = errorMessage;
	}

	@Column(name = "START_ORDER_ID", nullable = false)
	public int getStartOrderId() {
		return startOrderId;
	}

	public void setStartOrderId(int startOrderId) {
		this.startOrderId = startOrderId;
	}

	@Column(name = "END_ORDER_ID", nullable = false)
	public int getEndOrderId() {
		return endOrderId;
	}

	public void setEndOrderId(int endOrderId) {
		this.endOrderId = endOrderId;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "START_DATE", nullable = false, length = 19)
	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "END_DATE", nullable = false, length = 19)
	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	
}
