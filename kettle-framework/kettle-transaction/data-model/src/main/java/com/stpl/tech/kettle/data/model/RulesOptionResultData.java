package com.stpl.tech.kettle.data.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;

@Entity
@Table(name = "RULES_OPTION_RESULT_DATA")
public class RulesOptionResultData implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -1108276493766838210L;

	private Integer optionResultDataId;

	private RulesOptionData optionData;

	private String resultCategory;

	private Integer countWithDiscount;

	private Integer countWithoutDiscount;

	private Integer availedWithDiscount;

	private Integer availedWithoutDiscount;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "OPTION_RESULT_DATA_ID", unique = true, nullable = false)
	public Integer getOptionResultDataId() {
		return optionResultDataId;
	}

	public void setOptionResultDataId(Integer optionResultDataId) {
		this.optionResultDataId = optionResultDataId;
	}

	@OneToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "OPTION_DATA_ID", nullable = false)
	public RulesOptionData getOptionData() {
		return optionData;
	}

	public void setOptionData(RulesOptionData optionData) {
		this.optionData = optionData;
	}
	

	@Column(name = "RESULT_CATEGORY", nullable = false, length = 10)
	public String getResultCategory() {
		return resultCategory;
	}

	

	public void setResultCategory(String resultCategory) {
		this.resultCategory = resultCategory;
	}

	@Column(name = "COUNT_WITH_DISCOUNT", nullable = true)
	public Integer getCountWithDiscount() {
		return countWithDiscount;
	}

	public void setCountWithDiscount(Integer countWithDiscount) {
		this.countWithDiscount = countWithDiscount;
	}

	@Column(name = "COUNT_WITHOUT_DISCOUNT", nullable = true)
	public Integer getCountWithoutDiscount() {
		return countWithoutDiscount;
	}

	public void setCountWithoutDiscount(Integer countWithoutDiscount) {
		this.countWithoutDiscount = countWithoutDiscount;
	}

	@Column(name = "AVAILED_WITH_DISCOUNT", nullable = true)
	public Integer getAvailedWithDiscount() {
		return availedWithDiscount;
	}

	public void setAvailedWithDiscount(Integer availedWithDiscount) {
		this.availedWithDiscount = availedWithDiscount;
	}

	@Column(name = "AVAILED_WITHOUT_DISCOUNT", nullable = true)
	public Integer getAvailedWithoutDiscount() {
		return availedWithoutDiscount;
	}

	public void setAvailedWithoutDiscount(Integer availedWithoutDiscount) {
		this.availedWithoutDiscount = availedWithoutDiscount;
	}

}
