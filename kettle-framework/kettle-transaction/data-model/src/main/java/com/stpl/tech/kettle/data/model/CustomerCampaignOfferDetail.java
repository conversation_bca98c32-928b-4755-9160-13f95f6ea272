/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;
// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import com.stpl.tech.kettle.core.CampaignOfferDetail;

import javax.persistence.Column;
import javax.persistence.ColumnResult;
import javax.persistence.ConstructorResult;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.SqlResultSetMapping;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.math.BigDecimal;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

/**
 * ChannelPartner generated by hbm2java
 */
@Entity
@Table(name = "CUSTOMER_CAMPIAGN_OFFER_DETAIL")
@SqlResultSetMapping(name = "CampaignOfferDetail", classes = @ConstructorResult(targetClass = CampaignOfferDetail.class, columns = {
        @ColumnResult(name = "capmpaignOfferDetailId", type = Integer.class),
        @ColumnResult(name = "contactNumber", type = String.class),
        @ColumnResult(name = "campaignId", type = Integer.class),
        @ColumnResult(name = "campaignCloneCode", type = String.class),
        @ColumnResult(name = "channelPartner", type = Integer.class),
        @ColumnResult(name = "customerId", type = Integer.class),
        @ColumnResult(name = "firstName", type = String.class),
        @ColumnResult(name = "couponCode", type = String.class),
        @ColumnResult(name = "couponStartDate", type = Date.class),
        @ColumnResult(name = "couponEndDate", type = Date.class),
        @ColumnResult(name = "offerText", type = String.class),
        @ColumnResult(name = "couponType", type = String.class),
        @ColumnResult(name = "reminderDays", type = Integer.class),
}))
public class CustomerCampaignOfferDetail implements java.io.Serializable {

    /**
     *
     */
    private static final long serialVersionUID = 3232848262580649158L;
    private Integer capmpaignOfferDetailId;
    private Integer customerId;
    private String countryCode;
    private String contactNumber;
    private String firstName;
    private Integer brandId;
    private Integer unitId;
    private String campaignCloneCode;
    private String couponCode;
    private Integer couponDetailId;
    private Integer offerDetailId;
    private String offerText;
    private Date couponStartDate;
    private Date couponEndDate;
    private Date couponGenerationTime;
    private String isNotificationRequired;
    private Integer offerCreateOrderId;
    private String isOfferApplied;
    private Integer offerApplyLastOrderId;
    private Date offerApplyLastTime;
    private Integer offerApplyCount;
    private String offerApplyType;
    private String status;
    private Integer gapInDays;
    private BigDecimal overallSavings;
    private Integer campaignId;
    private String couponType;
    private Integer channelPartner;
    private Integer reminderDays;
    private String customerType;
    private Integer currentJourneyNumber;
    private Integer nextJourneyNumber;
    private Date nextOfferDate;
    private String utmMedium;
    private String utmSource;

    public CustomerCampaignOfferDetail() {
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "CUSTOMER_CAMPIAGN_OFFER_DETAIL_ID", unique = true, nullable = false)
    public Integer getCapmpaignOfferDetailId() {
        return capmpaignOfferDetailId;
    }

    public void setCapmpaignOfferDetailId(Integer capmpaignOfferDetailId) {
        this.capmpaignOfferDetailId = capmpaignOfferDetailId;
    }

    @Column(name = "OFFER_TEXT", nullable = true)
    public String getOfferText() {
        return offerText;
    }

    public void setOfferText(String offerText) {
        this.offerText = offerText;
    }

    @Column(name = "CUSTOMER_ID", nullable = true)
    public Integer getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    @Column(name = "COUNTRY_CODE", nullable = true)
    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    @Column(name = "CONTACT_NUMBER", nullable = true)
    public String getContactNumber() {
        return contactNumber;
    }

    public void setContactNumber(String contactNumber) {
        this.contactNumber = contactNumber;
    }


    @Column(name = "CAMPAIGN_CLONE_CODE", nullable = true)
    public String getCampaignCloneCode() {
        return campaignCloneCode;
    }

    public void setCampaignCloneCode(String campaignCloneCode) {
        this.campaignCloneCode = campaignCloneCode;
    }

    @Column(name = "CAMPAIGN_ID", nullable = true)
    public Integer getCampaignId() {
        return campaignId;
    }

    public void setCampaignId(Integer campaignId) {
        this.campaignId = campaignId;
    }


    @Column(name = "COUPON_CODE", nullable = true)
    public String getCouponCode() {
        return couponCode;
    }


    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }

    @Column(name = "COUPON_DETAIL_ID", nullable = true)
    public Integer getCouponDetailId() {
        return couponDetailId;
    }

    public void setCouponDetailId(Integer couponDetailId) {
        this.couponDetailId = couponDetailId;
    }

    @Column(name = "OFFER_DETAIL_ID", nullable = true)
    public Integer getOfferDetailId() {
        return offerDetailId;
    }

    public void setOfferDetailId(Integer offerDetailId) {
        this.offerDetailId = offerDetailId;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "COUPON_START_DATE", nullable = true, length = 10)
    public Date getCouponStartDate() {
        return couponStartDate;
    }

    public void setCouponStartDate(Date couponStartDate) {
        this.couponStartDate = couponStartDate;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "COUPON_END_DATE", nullable = true, length = 10)
    public Date getCouponEndDate() {
        return couponEndDate;
    }

    public void setCouponEndDate(Date couponEndDate) {
        this.couponEndDate = couponEndDate;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "COUPON_GENERATION_TIME", nullable = true, length = 19)
    public Date getCouponGenerationTime() {
        return couponGenerationTime;
    }

    public void setCouponGenerationTime(Date couponGenerationTime) {
        this.couponGenerationTime = couponGenerationTime;
    }

    @Column(name = "IS_NOTIFICATION_REQUIRED", nullable = true)
    public String getIsNotificationRequired() {
        return isNotificationRequired;
    }

    public void setIsNotificationRequired(String isNotificationRequired) {
        this.isNotificationRequired = isNotificationRequired;
    }

    @Column(name = "OFFER_CREATE_ORDER_ID", nullable = true)
    public Integer getOfferCreateOrderId() {
        return offerCreateOrderId;
    }

    public void setOfferCreateOrderId(Integer offerCreateOrderId) {
        this.offerCreateOrderId = offerCreateOrderId;
    }

    @Column(name = "OFFER_APPLIED", nullable = true)
    public String getIsOfferApplied() {
        return isOfferApplied;
    }

    public void setIsOfferApplied(String isOfferApplied) {
        this.isOfferApplied = isOfferApplied;
    }

    @Column(name = "OFFER_APPLY_LAST_ORDER_ID", nullable = true)
    public Integer getOfferApplyLastOrderId() {
        return offerApplyLastOrderId;
    }

    public void setOfferApplyLastOrderId(Integer offerApplyLastOrderId) {
        this.offerApplyLastOrderId = offerApplyLastOrderId;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "OFFER_APPLY_LAST_TIME", nullable = true, length = 19)
    public Date getOfferApplyLastTime() {
        return offerApplyLastTime;
    }

    public void setOfferApplyLastTime(Date offerApplyLastTime) {
        this.offerApplyLastTime = offerApplyLastTime;
    }

    @Column(name = "OFFER_APPLY_COUNT", nullable = true)
    public Integer getOfferApplyCount() {
        return offerApplyCount;
    }

    public void setOfferApplyCount(Integer offerApplyCount) {
        this.offerApplyCount = offerApplyCount;
    }

    @Column(name = "OFFER_APPLY_TYPE", nullable = true)
    public String getOfferApplyType() {
        return offerApplyType;
    }

    public void setOfferApplyType(String offerApplyType) {
        this.offerApplyType = offerApplyType;
    }

    @Column(name = "OFFER_STATUS", nullable = true)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "GAP_IN_DAYS", nullable = true)
    public Integer getGapInDays() {
        return gapInDays;
    }

    public void setGapInDays(Integer gapInDays) {
        this.gapInDays = gapInDays;
    }

    @Column(name = "OVERALL_SAVINGS", precision = 10)
    public BigDecimal getOverallSavings() {
        return overallSavings;
    }

    public void setOverallSavings(BigDecimal overallSavings) {
        this.overallSavings = overallSavings;
    }

    @Column(name = "FIRST_NAME", nullable = true)
    public String getFirstName() {
        return firstName;
    }

    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }

    @Column(name = "BRAND_ID", nullable = true)
    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    @Column(name = "UNIT_ID", nullable = true)
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @Column(name = "COUPON_TYPE", nullable = true)
    public String getCouponType() {
        return couponType;
    }

    public void setCouponType(String couponType) {
        this.couponType = couponType;
    }

    @Column(name = "CHANNEL_PARTNER", nullable = true)
    public Integer getChannelPartner() {
        return channelPartner;
    }

    public void setChannelPartner(Integer channelPartner) {
        this.channelPartner = channelPartner;
    }

    @Column(name = "REMINDER_DAYS")
    public Integer getReminderDays() {
        return reminderDays;
    }

    public void setReminderDays(Integer reminderDays) {
        this.reminderDays = reminderDays;
    }

    @Column(name = "CUSTOMER_TYPE")
    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    @Column(name = "CURRENT_JOURNEY_NUMBER")
    public Integer getCurrentJourneyNumber() {
        return currentJourneyNumber;
    }

    public void setCurrentJourneyNumber(Integer currentJourneyNumber) {
        this.currentJourneyNumber = currentJourneyNumber;
    }

    @Column(name = "NEXT_JOURNEY_NUMBER")
    public Integer getNextJourneyNumber() {
        return nextJourneyNumber;
    }

    public void setNextJourneyNumber(Integer nextJourneyNumber) {
        this.nextJourneyNumber = nextJourneyNumber;
    }

    @Column(name = "NEXT_JOURNEY_OFFER_DATE")
    public Date getNextOfferDate() {
        return nextOfferDate;
    }

    public void setNextOfferDate(Date nextOfferDate) {
        this.nextOfferDate = nextOfferDate;
    }

    @Column(name = "UTM_MEDIUM")
    public String getUtmMedium() {
        return utmMedium;
    }

    public void setUtmMedium(String utmMedium) {
        this.utmMedium = utmMedium;
    }

    @Column(name = "UTM_SOURCE")
    public String getUtmSource() {
        return utmSource;
    }

    public void setUtmSource(String utmSource) {
        this.utmSource = utmSource;
    }
}
