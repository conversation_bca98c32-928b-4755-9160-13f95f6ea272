package com.stpl.tech.kettle.core.data.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.util.List;

@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UnitTimeStockData {
    private List<Date> dineInTime;
    private List<Date> deliveryTime;
    private boolean isOperational;
    private boolean is24HoursCafe;

    private Date dineInOpeningTime;
    private Date dineInClosingTime;
    private Date deliveryClosingTime;
    private Date deliveryOpeningTime;

}
