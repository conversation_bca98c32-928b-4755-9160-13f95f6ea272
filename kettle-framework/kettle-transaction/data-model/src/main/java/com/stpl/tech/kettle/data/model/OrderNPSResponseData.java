package com.stpl.tech.kettle.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "ORDER_NPS_RESPONSE_DATA")
public class OrderNPSResponseData implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private Integer responseId;
	private Integer surveryResponseId;
	private String question;
	private String response;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "RESPONSE_ID", unique = true, nullable = false)
	public Integer getResponseId() {
		return responseId;
	}

	public void setResponseId(Integer responseId) {
		this.responseId = responseId;
	}

	@Column(name = "SURVEY_RESPONSE_ID", nullable = true)
	public Integer getSurveryResponseId() {
		return surveryResponseId;
	}

	public void setSurveryResponseId(Integer surveryResponseId) {
		this.surveryResponseId = surveryResponseId;
	}

	@Column(name = "NPS_QUESTION", nullable = true)
	public String getQuestion() {
		return question;
	}

	public void setQuestion(String question) {
		this.question = question;
	}

	@Column(name = "NPS_RESPONSE", nullable = true)
	public String getResponse() {
		return response;
	}

	public void setResponse(String response) {
		this.response = response;
	}

}
