/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.dao.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.persistence.NoResultException;
import javax.persistence.Query;

import com.google.common.base.Stopwatch;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.master.payment.model.PaymentRequestStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.core.exception.PaymentFailureException;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.converter.DataConverter;
import com.stpl.tech.kettle.data.dao.PaymentGatewayDao;
import com.stpl.tech.kettle.data.model.OrderPaymentAttributeData;
import com.stpl.tech.kettle.data.model.OrderPaymentDetail;
import com.stpl.tech.kettle.data.model.OrderPaymentEvent;
import com.stpl.tech.kettle.data.model.PaytmPaymentDetails;
import com.stpl.tech.kettle.data.model.StandaloneTransactionDetail;
import com.stpl.tech.kettle.delivery.model.PaymentPartner;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.payment.model.OrderPayment;
import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.master.payment.model.PaymentRequest;
import com.stpl.tech.master.payment.model.PaymentResponse;
import com.stpl.tech.master.payment.model.PaymentStatus;
import com.stpl.tech.master.payment.model.PaymentStatusChangeRequest;
import com.stpl.tech.master.payment.model.razorpay.BasicTransactionInfo;
import com.stpl.tech.master.payment.model.razorpay.OrderEntity;
import com.stpl.tech.master.payment.model.razorpay.PaymentEntity;
import com.stpl.tech.master.payment.model.razorpay.RazorPayEventData;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;

@Repository
public class PaymentGatewayDaoImpl extends AbstractDaoImpl implements PaymentGatewayDao {

	private static final Logger LOG = LoggerFactory.getLogger(PaymentGatewayDaoImpl.class);
	
	private static final List<String> statusList;

	static {
		statusList = new ArrayList<>();
		statusList.add(PaymentStatus.DISASSOCIATED.name());
		statusList.add(PaymentStatus.SUCCESSFUL.name());

	}
	@Autowired
	private MasterDataCache masterDataCache;

	@Autowired
	private EnvironmentProperties props;

	@Override
	public void createRequest(PaymentRequest request, OrderPaymentRequest order) {
		Stopwatch watch = Stopwatch.createUnstarted();
		watch.start();
		OrderPaymentDetail detail = new OrderPaymentDetail();
		if (order.getPaymentModeId() != PaymentPartner.INGENICO.getSystemId(null)) {
			cancelIfExist(order.getGenerateOrderId());
		}
		detail.setExternalOrderId(order.getGenerateOrderId());
		detail.setPaymentModeId(order.getPaymentModeId());
		detail.setPaymentSource(order.getPaymentSource() == null ? ApplicationName.KETTLE_SERVICE.name()
				: order.getPaymentSource().name());
		detail.setPaymentModeName(order.getPaymentModeName());
		detail.setPaymentStatus(request.getStatus());
		detail.setRequestStatus(PaymentRequestStatus.INITIATED.name());
		detail.setRequestTime(AppUtils.getCurrentTimestamp());
		detail.setRedirectUrl(order.getRedirectUrl());
		detail.setPartnerOrderId(request.getPartnerOrderId());
		detail.setCartId(order.getCartId());
		detail.setCustomerId(order.getCustomerId());
		detail.setCustomerName(order.getCustomerName());
		detail.setTransactionAmount(order.getPaidAmount());
		detail.setContactNumber(order.getContactNumber());
		detail.setMerchantId(order.getMerchantId());
		detail.setBrandId(order.getBrandId());
		manager.persist(detail);
		manager.flush();
		createParameters(detail.getOrderPaymentDetailId(), request.getPersistentAttributes());
		LOG.info("Time Taken to create request {}", watch.stop().elapsed(TimeUnit.MILLISECONDS));
	}

	private void createParameters(int paymentDetailId, Map<String, String> attributes) {
		if (attributes != null && attributes.size() > 0) {
			for (String key : attributes.keySet()) {
				OrderPaymentAttributeData data = new OrderPaymentAttributeData();
				data.setAttributeKey(key);
				data.setAttributeType("STRING");
				data.setAttributeValue(attributes.get(key));
				data.setOrderPaymentDetailId(paymentDetailId);
				manager.persist(data);
			}
			manager.flush();
		}

	}

	private void cancelIfExist(String orderId) {

		Query query = manager.createQuery("from OrderPaymentDetail E where E.externalOrderId = :orderId " +
				"and E.requestStatus <> :requestStatus");
		query.setParameter("requestStatus", "CANCELLED");
		query.setParameter("orderId", orderId);
		List<OrderPaymentDetail> orderPaymentDetails = query.getResultList();
		if(orderPaymentDetails != null && orderPaymentDetails.size() > 0){
			for(OrderPaymentDetail orderPaymentDetail : orderPaymentDetails){
				orderPaymentDetail.setRequestStatus(PaymentRequestStatus.CANCELLED.name());
				orderPaymentDetail.setUpdateTime(AppUtils.getCurrentTimestamp());
				manager.persist(orderPaymentDetail);
				manager.flush();
			}
		}
//		Query query = manager.createQuery("update OrderPaymentDetail E "
//				+ "set E.requestStatus = :requestStatus, E.updateTime = :updateTime where E.externalOrderId = :orderId"
//				+ " and E.requestStatus <> :requestStatus");
//		query.setParameter("requestStatus", "CANCELLED");
//		query.setParameter("orderId", orderId);
//		query.setParameter("updateTime", AppUtils.getCurrentTimestamp());
//		query.executeUpdate();
//		manager.flush();
	}

	private void cancelIfPaytmExist(String orderId) {
		Query query = manager.createQuery("update OrderPaymentDetail E "
				+ "set E.requestStatus = :requestStatus, E.updateTime = :updateTime where E.externalOrderId = :orderId"
				+ " and E.requestStatus = :requestStatus1 and E.paymentModeId = :modeId");
		query.setParameter("requestStatus", "CANCELLED");
		query.setParameter("requestStatus1", "INITIATED");
		query.setParameter("orderId", orderId);
		query.setParameter("modeId", 13);
		query.setParameter("updateTime", AppUtils.getCurrentTimestamp());
		query.executeUpdate();
		manager.flush();
	}

	@Override
	public void updateIngenicoPaymentMode(OrderPaymentRequest order) {
		Query query = manager.createQuery("update OrderPaymentDetail E "
				+ "set E.paymentModeName = :paymentModeName, E.updateTime = :updateTime where E.externalOrderId = :orderId and E.paymentModeId = :paymentModeId");
		query.setParameter("paymentModeName", order.getPaymentModeName());
		query.setParameter("orderId", order.getGenerateOrderId());
		query.setParameter("updateTime", AppUtils.getCurrentTimestamp());
		query.setParameter("paymentModeId", PaymentPartner.INGENICO.getSystemId(null));
		query.executeUpdate();
		manager.flush();
	}

	@Override
	public void markTransactionFail(OrderPaymentRequest order) {
		Query query = manager.createQuery("update OrderPaymentDetail E "
				+ "set E.updateTime = :updateTime, E.paymentStatus = :paymentStatus where E.externalOrderId = :orderId and E.paymentStatus = :paymentStatus1");
		query.setParameter("orderId", order.getGenerateOrderId());
		query.setParameter("updateTime", AppUtils.getCurrentTimestamp());
		query.setParameter("paymentStatus", order.getPaymentModeStatus());
		query.setParameter("paymentStatus1", "INITIATED");
		query.executeUpdate();
		manager.flush();
	}

	@Override
	public Boolean checkIfIngenicoExist(String orderId) {
		try {
			Query query = manager.createQuery(
					"from OrderPaymentDetail E where E.externalOrderId = :orderId" + " and E.paymentModeId = :modeId");
			query.setParameter("orderId", orderId);
			query.setParameter("modeId", 25);
			Object object = query.getSingleResult();
			cancelIfPaytmExist(orderId);
			return object == null ? false : true;
		} catch (Exception e) {
			LOG.error("Error in finding Payment Detail for orderId " + orderId);
			return false;
		}
	}

	@Override
	public OrderPaymentDetail getSuccessfulOrderPaymentDetail(String orderId) {
		try {
			Query query = manager.createQuery("from OrderPaymentDetail E " + " where E.externalOrderId = :orderId"
					+ " and E.requestStatus = :successStatus");
			query.setParameter("successStatus", "SUCCESSFUL");
			query.setParameter("orderId", orderId);
			Object object = query.getSingleResult();
			return object == null ? null : (OrderPaymentDetail) object;

		} catch (Exception e) {
			LOG.error("Error in finding Payment Detail for orderId " + orderId);
			return null;
		}
	}

	@Override
	public OrderPaymentDetail updateResponse(PaymentStatus status, PaymentResponse response) {
		OrderPaymentDetail paymentDetail = getActivePaymentDetail(response.getOrderId());
		if (paymentDetail != null) {
			paymentDetail.setPartnerPaymentStatus(response.getStatus());
			paymentDetail.setPartnerTransactionId(response.getTransactionId());
			paymentDetail.setPaymentStatus(status.name());
			paymentDetail.setResponseTime(AppUtils.getCurrentTimestamp());
			manager.merge(paymentDetail);
			manager.flush();
			createParameters(paymentDetail.getOrderPaymentDetailId(), response.getPersistentAttributes());
		}
		return paymentDetail;
	}

	@Override
	public OrderPaymentDetail markPaymentRefunded(PaymentResponse response) {
		try {
			LOG.info("Marking payment {} as REFUNDED " ,response.getPartnerOrderId());
			Query query = manager.createQuery("from OrderPaymentDetail E "
					+ "where E.partnerOrderId = :partnerOrderId");
			query.setParameter("partnerOrderId", response.getPartnerOrderId());
			OrderPaymentDetail paymentDetail = (OrderPaymentDetail) query.getSingleResult();
			paymentDetail.setOrderId(Integer.parseInt(response.getOrderId()));
			paymentDetail.setPartnerPaymentStatus(response.getStatus());
			paymentDetail.setPartnerTransactionId(response.getTransactionId());
			paymentDetail.setPaymentStatus(PaymentStatus.REFUND_INITIATED.name());
			paymentDetail.setResponseTime(AppUtils.getCurrentTimestamp());
			manager.merge(paymentDetail);
			manager.flush();
			createParameters(paymentDetail.getOrderPaymentDetailId(), response.getPersistentAttributes());
			return paymentDetail;
		} catch (Exception e) {
			LOG.error("Could not find active payment detail for external order id: {}", response.getOrderId(), e);
			return null;
		}
	}
	@Override
	public OrderPaymentDetail markPaymentRefundInitiated(PaymentResponse response) {
		try {
			LOG.info("Marking payment {} as REFUND_INITIATED " ,response.getOrderId());
			Query query = manager.createQuery("from OrderPaymentDetail E "
					+ "where E.externalOrderId = :externalOrderId and E.paymentStatus = :paymentStatus and E.orderId is null");
			query.setParameter("externalOrderId", response.getOrderId());
			query.setParameter("paymentStatus", PaymentStatus.SUCCESSFUL.name());
			OrderPaymentDetail paymentDetail = (OrderPaymentDetail) query.getSingleResult();
//			paymentDetail.setPartnerPaymentStatus(response.getStatus());
//			paymentDetail.setExternalOrderId(response.getOrderId());
			paymentDetail.setPaymentStatus(PaymentStatus.REFUND_INITIATED.name());
			paymentDetail.setResponseTime(AppUtils.getCurrentTimestamp());
			manager.merge(paymentDetail);
			manager.flush();
			createParameters(paymentDetail.getOrderPaymentDetailId(), response.getPersistentAttributes());
			return paymentDetail;
		} catch (Exception e) {
			LOG.error("Could not find active payment detail for external order id: {}", response.getOrderId(), e);
			return null;
		}
	}
	
	@Override
	public OrderPaymentDetail updateResponseUsingPartnerOrderId(PaymentStatus status, PaymentResponse response) {
		OrderPaymentDetail paymentDetail = getActivePaymentDetailUsingPartnerOrderId(response.getPartnerOrderId());
		if (paymentDetail != null) {
			paymentDetail.setPartnerPaymentStatus(response.getStatus());
			paymentDetail.setPartnerTransactionId(response.getTransactionId());
			paymentDetail.setPaymentStatus(status.name());
			paymentDetail.setResponseTime(AppUtils.getCurrentTimestamp());
			manager.merge(paymentDetail);
			manager.flush();
			createParameters(paymentDetail.getOrderPaymentDetailId(), response.getPersistentAttributes());
		}
		return paymentDetail;
	}

	@Override
	public OrderPaymentDetail getActivePaymentDetail(String externalOrderId) {
		try {
			Query query = manager.createQuery("from OrderPaymentDetail E "
					+ "where E.externalOrderId = :externalOrderId" + " and E.requestStatus = :requestStatus");
			query.setParameter("requestStatus", PaymentStatus.INITIATED.name());
			query.setParameter("externalOrderId", externalOrderId);
			OrderPaymentDetail paymentDetail = (OrderPaymentDetail) query.getSingleResult();
			return paymentDetail;
		} catch (Exception e) {
			LOG.error("Could not find active payment detail for external order id: {}", externalOrderId, e);
		}
		return null;
	}

	public OrderPaymentDetail getOrderPaymentDetailByExternalOrderId(String externalOrderId){
		try {
			Query query = manager.createQuery("from OrderPaymentDetail E "
					+ "where E.externalOrderId = :externalOrderId");
			query.setParameter("externalOrderId", externalOrderId);
			OrderPaymentDetail paymentDetail = (OrderPaymentDetail) query.getSingleResult();
			return paymentDetail;
		} catch (Exception e) {
			LOG.error("Could not find active payment detail for external order id: {}", externalOrderId, e);
		}
		return null;
	}

	@Override
	public OrderPaymentDetail getActivePaymentDetailUsingPartnerOrderId(String partnerOrderId) {
		try {
			Query query = manager.createQuery("from OrderPaymentDetail E "
					+ "where E.partnerOrderId = :partnerOrderId" + " and E.requestStatus = :requestStatus");
			query.setParameter("requestStatus", PaymentStatus.INITIATED.name());
			query.setParameter("partnerOrderId", partnerOrderId);
			OrderPaymentDetail paymentDetail = (OrderPaymentDetail) query.getSingleResult();
			return paymentDetail;
		} catch (Exception e) {
			LOG.error("Could not find active payment detail for partner order id: {}", partnerOrderId, e);
		}
		return null;
	}
	
	@Override
	public void createPaymentEvent(RazorPayEventData event) {
		OrderPaymentEvent d = new OrderPaymentEvent();
		String orderId = null;
		List<OrderPaymentDetail> paymentDetails = null;
		if (event.getEvent().equals("payment.authorized") || event.getEvent().equals("payment.failed")) {
			orderId = event.getPayload().getPayment().getEntity().getOrderId();
			PaymentEntity load = event.getPayload().getPayment().getEntity();
			setPaymentEntityData(event.getEvent(), null, load, d);
			Query query = manager.createQuery("from OrderPaymentDetail E "
					+ "where E.partnerOrderId = :partnerOrderId order by orderPaymentDetailId desc");
			query.setParameter("partnerOrderId", orderId);
			paymentDetails = query.getResultList();
		} else if (event.getEvent().equals("order.paid")) {
			orderId = event.getPayload().getOrder().getEntity().getReceipt();
			OrderEntity orderLoad = event.getPayload().getOrder().getEntity();
			setOrderEntityData(event.getEvent(), orderLoad, d);
			Query query = manager.createQuery("from OrderPaymentDetail E "
					+ "where E.externalOrderId = :externalOrderId order by orderPaymentDetailId desc");
			query.setParameter("externalOrderId", orderId);
			paymentDetails = query.getResultList();
		}
		if (paymentDetails != null && paymentDetails.size() > 0) {
			d.setOrderPaymentDetailId(paymentDetails.get(0).getOrderPaymentDetailId());
		}
		manager.persist(d);
		manager.flush();

	}

	@Override
	public StandaloneTransactionDetail createStandalonePaymentEvent(RazorPayEventData event) {

		LOG.error("Got a Standalone payment request\n" + JSONSerializer.toJSON(event));
		String paymentId = null;
		if (event.getPayload() != null && event.getPayload().getPayment() != null
				&& event.getPayload().getPayment().getEntity() != null) {
			paymentId = event.getPayload().getPayment().getEntity().getId();
		}
		if (paymentId == null) {
			LOG.error("Got a payment Request with no payment Id \n" + JSONSerializer.toJSON(event));
			return null;
		}
		StandaloneTransactionDetail transaction = getStandAloneTransaction(paymentId);
		if (transaction == null) {
			StandaloneTransactionDetail detail = createTransaction(event);
			manager.persist(detail);
			manager.flush();
			return detail;
		} else {
			StandaloneTransactionDetail detail = updateTransaction(transaction, event);
			manager.flush();
			return detail;
		}
	}

	private StandaloneTransactionDetail updateTransaction(StandaloneTransactionDetail transaction,
			RazorPayEventData event) {
		if (event != null) {
			setEvent(transaction, event);
		}
		if (event.getInfo() != null) {
			setBasicInfo(transaction, event.getInfo());
		}
		if (event.getPayload() != null && event.getPayload().getPayment() != null
				&& event.getPayload().getPayment().getEntity() != null) {
			setPaymentEntity(transaction, event.getPayload().getPayment().getEntity());
		}
		if (event.getPayload() != null && event.getPayload().getOrder() != null
				&& event.getPayload().getOrder().getEntity() != null) {
			setOrderEntity(transaction, event.getPayload().getOrder().getEntity());
		}

		return transaction;
	}

	private void setEvent(StandaloneTransactionDetail transaction, RazorPayEventData event) {
		transaction.setRequestTime(new Date(event.getCreatedAt()));
		transaction.setAccounId(event.getAccountId());
		transaction.setEventType(event.getEvent());
		transaction.setLastUpdateTime(AppUtils.getCurrentTimestamp());
	}
	
	public void setNotificationDetail(String paymentId, String smsType, String emailType) {
		StandaloneTransactionDetail transaction = getStandAloneTransaction(paymentId);
		transaction.setSmsType(smsType);
		transaction.setEmailType(emailType);
		transaction.setNotified(AppConstants.YES);
		manager.flush();
	}
	


	private void setOrderEntity(StandaloneTransactionDetail t, OrderEntity e) {

		t.setOrderId(e.getId());
		t.setOrderAmount(getValue(e.getAmount()));
		t.setOrderAmountPaid(getValue(e.getPaidAmount()));
		t.setOrderAmountDue(getValue(e.getDueAmount()));
		t.setOrderCurrency(e.getCurrency());
		t.setOrderReceipt(e.getReceipt());
		t.setOrderOfferId(e.getOfferId());
		t.setOrderStatus(e.getStatus());
		t.setOrderAttempts(e.getAttempts());
		t.setOrderCreationTime(new Date(e.getCreatedAt()));

	}

	private void setPaymentEntity(StandaloneTransactionDetail t, PaymentEntity e) {
		t.setPaymentId(e.getId());
		t.setPaymentAmount(getValue(e.getAmount()));
		t.setPaymentCurrency(e.getCurrency());
		t.setPaymentStatus(e.getStatus());
		t.setPaymentInvoiceId(e.getInvoiceId());
		t.setInternationalPayment(AppConstants.getValue(e.getInternational()));
		t.setPaymentMethod(e.getMethod());
		t.setPaymentRefundAmount(getValue(e.getAmountRefunded()));
		t.setPaymentRefundStatus(e.getRefundStatus());
		t.setPaymentCaptured(AppConstants.getValue(e.getCaptured()));
		t.setPaymentDescription(e.getDescription());
		t.setPaymentCardId(e.getCardId());
		t.setPaymentBankName(e.getBank());
		t.setPaymentWallet(e.getWallet());
		t.setPaymentVpaHandle(e.getVpa());
		t.setPaymentEmailId(e.getEmail());
		t.setPaymentContactNumber(e.getContact());
		if (e.getNotes() != null) {
			t.setPaymentNotesEmailId(e.getNotes().getEmail());
			t.setPaymentNotesContactNumber(e.getNotes().getContact());
			t.setPaymentCustomerName(e.getNotes().getName());
		}
		t.setPaymentTransactionFee(getValue(e.getFee()));
		t.setPaymentTransactionFeeTax(getValue(e.getTax()));
		t.setPaymentErrorCode(e.getErrorCode());
		t.setPaymentErrorDescription(e.getErrorDescription());
		t.setPaymentTime(new Date(e.getCreatedAt()));
		t.setOrderId(e.getOrderId());

	}

	private BigDecimal getValue(Integer i) {
		return i == null ? null : new BigDecimal(i);
	}

	private void setBasicInfo(StandaloneTransactionDetail t, BasicTransactionInfo e) {
		t.setCampaignDescription(e.getCampaignDescription());
		t.setCampaignId(e.getCampaignId());
		t.setCurrentStatus(e.getCurrentStatus());
	}

	private StandaloneTransactionDetail createTransaction(RazorPayEventData event) {
		StandaloneTransactionDetail detail = new StandaloneTransactionDetail();
		return updateTransaction(detail, event);
	}

	public StandaloneTransactionDetail getStandAloneTransaction(String paymentId) {
		try {

			Query query = manager.createQuery("from StandaloneTransactionDetail E " + "where E.paymentId = :paymentId");
			query.setParameter("paymentId", paymentId);
			return (StandaloneTransactionDetail) query.getSingleResult();
		} catch (Exception e) {
			return null;
		}
	}

	private void setPaymentEntityData(String event, String receipt, PaymentEntity load, OrderPaymentEvent d) {
		d.setBank(load.getBank());
		d.setCardId(load.getCardId());
		d.setCreateTime(new Date(load.getCreatedAt()));
		d.setEntity(load.getEntity());
		d.setErrorCode(load.getErrorCode());
		d.setErrorDescription(load.getErrorDescription());
		d.setEventType(event);
		d.setPaymentId(load.getId());
		d.setPaymentStatus(load.getStatus());
		d.setReceipt(receipt);
		d.setServiceFee(load.getFee());
		d.setServiceTax(load.getServiceTax());
		d.setVpa(load.getVpa());
		d.setWallet(load.getWallet());
	}

	private void setOrderEntityData(String event, OrderEntity load, OrderPaymentEvent d) {
		d.setCreateTime(new Date(load.getCreatedAt()));
		d.setEntity(load.getEntity());
		d.setEventType(event);
		d.setPaymentId(load.getId());
		d.setPaymentStatus(load.getStatus());
		d.setReceipt(load.getReceipt());
		d.setAttempts(load.getAttempts());
	}

	@Override
	public OrderPaymentDetail getSuccessfulPaymentDetailFromOrderId(String partnerOrderId) {
		Query query = manager.createQuery("from OrderPaymentDetail E " + "where E.partnerOrderId = :partnerOrderId"
				+ " and E.requestStatus = :requestStatus");
		query.setParameter("requestStatus", PaymentStatus.INITIATED.name());
		query.setParameter("partnerOrderId", partnerOrderId);
		OrderPaymentDetail paymentDetail = (OrderPaymentDetail) query.getSingleResult();
		return paymentDetail;
	}

	@Override
	public OrderPaymentDetail getSuccessfulPaymentDetailFromPartnerTransactionId(String partnerTransactionId) {
		Query query = manager.createQuery("from OrderPaymentDetail E " + "where E.partnerTransactionId = :partnerTransactionId"
				+ " and E.paymentStatus = :paymentStatus");
		query.setParameter("paymentStatus", PaymentStatus.REFUND_INITIATED.name());
		query.setParameter("partnerTransactionId", partnerTransactionId);
		OrderPaymentDetail paymentDetail = (OrderPaymentDetail) query.getSingleResult();
		return paymentDetail;
	}

	@Override
	public Boolean cancelPayment(PaymentStatusChangeRequest cancel) {
		OrderPaymentDetail detail = getActivePaymentDetail(cancel.getReceiptId());
		detail.setPaymentStatus(PaymentStatus.CANCELLED.name());
		detail.setCancelledBy(cancel.getCancelledBy());
		detail.setCancellationReason(cancel.getCancellationReason());
		detail.setCancellationTime(AppUtils.getCurrentTimestamp());
		manager.flush();
		return true;
	}

	@Override
	public Boolean paymentFailure(PaymentStatusChangeRequest cancel) {
		OrderPaymentDetail detail = getActivePaymentDetail(cancel.getReceiptId());
		detail.setPaymentStatus(PaymentStatus.FAILED.name());
		detail.setFailureReason(cancel.getFailureReason());
		detail.setFailureTime(AppUtils.getCurrentTimestamp());
		manager.flush();
		return true;
	}

	@Override
	public OrderPaymentDetail getSuccessfulPaymentDetail(Integer settlementId, Integer orderId) {
		Query query = manager.createQuery("from OrderPaymentDetail E where E.orderSettlementId = :orderSettlementId"
				+ " and E.orderId= :orderId and E.requestStatus = :requestStatus and E.paymentStatus = :paymentStatus");
		query.setParameter("orderSettlementId", settlementId);
		query.setParameter("orderId", orderId);
		query.setParameter("requestStatus", PaymentStatus.INITIATED.name());
		query.setParameter("paymentStatus", PaymentStatus.SUCCESSFUL.name());
		OrderPaymentDetail paymentDetail = (OrderPaymentDetail) query.getSingleResult();
		return paymentDetail;
	}

	@Override
	public List<OrderPaymentDetail> getRecentDisassociatedPaymentDetail(String contact, Date timeBeforeRefundIsEligible) {
		try {
			Query query = manager.createQuery("from OrderPaymentDetail E where E.contactNumber = :contact"
					+ " and E.requestStatus = :requestStatus" + " and E.orderId is null and E.orderSettlementId is null and " +
					" ( E.paymentStatus is null or E.paymentStatus = :paymentStatus ) " +
					" and E.requestTime <= :timeBeforeRefundIsEligible"
					+ " and E.requestTime >= :date " + " order by E.orderPaymentDetailId desc");
			query.setParameter("contact", contact);
			query.setParameter("requestStatus", PaymentStatus.INITIATED.name());
			query.setParameter("paymentStatus", PaymentStatus.INITIATED.name());
			query.setParameter("date", props.getRefundCutOffDate());
			query.setParameter("timeBeforeRefundIsEligible", timeBeforeRefundIsEligible);

			return query.getResultList();
		} catch (Exception e) {
			LOG.error("Could not find payment detail due to :::", e);
			return null;
		}
	}

	@Override
	public List<OrderPaymentDetail> getRecentDisassociatedPaymentDetail(Date lowerDate, Date upperDate) {
		try {
			Query query = manager.createQuery("from OrderPaymentDetail where orderId is null and requestTime >= :lowerDate and requestTime < :upperDate and paymentSource in :paymentSourceList order by 1 desc");
			query.setParameter("lowerDate", lowerDate);
			query.setParameter("upperDate", upperDate);
			List<String> paymentSources = new ArrayList<>();
			paymentSources.add(AppConstants.DINE_IN);
			paymentSources.add(AppConstants.PAYMENT_SRC_NEO_SERVICE);
			query.setParameter("paymentSourceList", paymentSources);
			return query.getResultList();
		} catch (Exception e) {
			LOG.error("Could not find payment detail due to :::", e);
			return null;
		}
	}

	@Override
	public List<OrderPaymentDetail> getRecentDisassociatedPaymentDetailsForCurrentDay() {
		try {
			Query query = manager.createQuery("from OrderPaymentDetail where orderId is null and requestTime >= :date and paymentSource = :paymentSource order by 1 desc");
			query.setParameter("date", AppUtils.addHoursToDate(AppUtils.getCurrentTimestamp(), -24));
			query.setParameter("paymentSource", AppConstants.DINE_IN);
			return query.getResultList();
		} catch (Exception e) {
			LOG.error("Could not find payment detail due to :::", e);
			return null;
		}
	}

	@Override
	public List<OrderPaymentDetail> getRecentDisassociatedPartnerPaymentDetail(Date lowerDate, Date upperDate) {
		try {
			Query query = manager.createQuery("from OrderPaymentDetail " +
					"where orderId is null " +
					"and requestTime >= :lowerDate and requestTime < :upperDate " +
					"and paymentSource in :paymentSourceList " +
					"and paymentModeId in :paymentModeList ");
			query.setParameter("lowerDate", lowerDate);
			query.setParameter("upperDate", upperDate);
			List<String> paymentSources = new ArrayList<>();
			paymentSources.add(AppConstants.KETTLE_SERVICE);
			List<Integer> paymentModeList = AppConstants.PAYTM_AVAILABLE_PAYMENT_MODES; // payment mode id 35, 36, 37, 39
			query.setParameter("paymentSourceList", paymentSources);
			query.setParameter("paymentModeList", paymentModeList);
			return query.getResultList();
		} catch (Exception e) {
			LOG.error("Could not find payment detail due to :::", e);
			return null;
		}
	}

	@Override
	public List<OrderPaymentDetail> getOrderPaymentDetailsForCurrentDayNeo(Date startTime, Date endTime) {
		try {
			LOG.info("Getting order payment details from {}", startTime);
			Query query = manager.createQuery("from OrderPaymentDetail " +
					"where requestTime >= :date " +
					"and requestTime <= :endDate " +
					"and paymentSource = :paymentSource order by 1 desc");
			query.setParameter("date", startTime);
			query.setParameter("endDate", endTime);
			query.setParameter("paymentSource", AppConstants.PAYMENT_SRC_NEO_SERVICE);
			return query.getResultList();
		} catch (Exception e) {
			LOG.error("Could not find payment detail due to :::", e);
			return null;
		}
	}

	@Override
	public List<OrderPaymentDetail> getRecentDisassociatedPaymentDetailAfterTime(String contact, Date time) {
		try {
			Query query = manager.createQuery("from OrderPaymentDetail E where E.contactNumber = :contact"
					+ " and E.requestStatus = :requestStatus" + " and E.orderId is null and E.orderSettlementId is null and " +
					"( E.paymentStatus is null or E.paymentStatus = :paymentStatus ) " +
					" and E.requestTime >= :timeAfterRefundIsEligible"
					+ " and E.requestTime >= :date " + " order by E.orderPaymentDetailId desc");
			query.setParameter("contact", contact);
			query.setParameter("requestStatus", PaymentStatus.INITIATED.name());
			query.setParameter("paymentStatus", PaymentStatus.INITIATED.name());
			query.setParameter("date", props.getRefundCutOffDate());
			query.setParameter("timeAfterRefundIsEligible", time);

			return query.getResultList();
		} catch (Exception e) {
			LOG.error("Could not find payment detail due to :::", e);
			return null;
		}
	}
	
	@Override
	public OrderPaymentDetail getDisassociatedPaymentDetail(String contact, Date timeBeforeRefundIsEligible) {


		try {
			Query query = manager.createQuery("from OrderPaymentDetail E where (E.contactNumber = :contact"
					+ " and E.paymentStatus= :paymentStatus and E.requestStatus = :requestStatus)"
					+ " or (E.contactNumber = :contact and E.orderId is null and E.orderSettlementId is null"
					+ " and E.requestStatus = :requestStatus and E.paymentStatus IN :statusList and E.requestTime >= :date)" +
					" and (E.requestTime <= :timeBeforeRefundIsEligible)"
					+ " order by E.orderPaymentDetailId desc");
			query.setParameter("contact", contact);
			query.setParameter("requestStatus", PaymentStatus.INITIATED.name());
			query.setParameter("paymentStatus", PaymentStatus.DISASSOCIATED.name());
			query.setParameter("statusList", statusList);
			query.setParameter("date", props.getRefundCutOffDate());
			query.setParameter("timeBeforeRefundIsEligible", timeBeforeRefundIsEligible);
			query.setMaxResults(1);
			OrderPaymentDetail paymentDetail = (OrderPaymentDetail) query.getSingleResult();
			return paymentDetail;
		} catch (Exception e) {
			LOG.error("Could not find payment detail due to :::", e);
			return null;
		}
	}

	@Override
	public List<OrderPaymentDetail> getDisassociatedPaymentDetail(Date timeBeforeRefundIsEligible) {
		try {
			Query query = manager.createQuery("from OrderPaymentDetail E where "
					+ " E.paymentStatus= :paymentStatus"
					+ " or (E.orderId is null and E.orderSettlementId is null"
					+ " and E.paymentStatus = :paymentStatus and E.requestTime >= :timeBeforeRefundIsEligible)"
					+ " order by 1 desc");
			query.setParameter("paymentStatus", PaymentStatus.REFUND_INITIATED.name());
			query.setParameter("timeBeforeRefundIsEligible", timeBeforeRefundIsEligible);
			List<OrderPaymentDetail> paymentDetail = (List<OrderPaymentDetail>) query.getResultList();
			return paymentDetail;
		} catch (Exception e) {
			LOG.error("Could not find payment detail due to :::", e);
			return null;
		}
	}

	@Override
	public OrderPayment refundPayment(OrderPayment request) {
		OrderPaymentDetail paymentDetail = manager.find(OrderPaymentDetail.class, request.getOrderPaymentDetailId());
		paymentDetail.setPaymentStatus(request.getPaymentStatus().name());
		paymentDetail.setRefundStatus(request.getRefundStatus().name());
		paymentDetail.setRefundProcessTime(AppUtils.getCurrentTimestamp());
		paymentDetail.setRefundId(request.getRefundId());
		paymentDetail = manager.merge(paymentDetail);
		return DataConverter.convert(paymentDetail, masterDataCache.getPaymentMode(paymentDetail.getPaymentModeId()));
	}

	@Override
	public List<OrderPaymentDetail>  getPendingRefunds() {
		Date currentDateMinusThree = AppUtils.getDayBeforeOrAfterCurrentDay(-3);
		List<String> refundStatuses = Arrays.asList(PaymentStatus.REFUND_INITIATED.name(),
				PaymentStatus.DISASSOCIATED.name());
		Query query = manager.createQuery("SELECT E from OrderPaymentDetail E where"
				+ " (E.paymentStatus in (:refundStatus) and E.refundRequestTime >= :requestTime )"
				+ " OR (E.paymentStatus = :successStatus and E.orderId IS NULL AND E.refundStatus IS NULL)"
				+ " order by E.orderPaymentDetailId desc");
		query.setParameter("refundStatus", refundStatuses);
		query.setParameter("requestTime", currentDateMinusThree);
		query.setParameter("successStatus", PaymentStatus.SUCCESSFUL.name());
		List<OrderPaymentDetail> paymentDetailList = query.getResultList();
		return paymentDetailList;
	}

	@Override
	public void updateRedirectUrl(int id, String url) {
		OrderPaymentDetail detail = manager.find(OrderPaymentDetail.class, id);
		detail.setRedirectUrl(url);
		manager.flush();
	}

	@Override
	public OrderPaymentDetail save(OrderPaymentDetail paymentDetail) {
		paymentDetail = manager.merge(paymentDetail);
		manager.flush();
		return paymentDetail;
	}

	@Override
	public OrderPaymentDetail getSuccessfulPaymentDetailFromOrderId(Integer orderId) throws PaymentFailureException {
		try {
			Query query = manager.createQuery("from OrderPaymentDetail E where"
					+ " E.orderId= :orderId and E.requestStatus = :requestStatus and E.paymentStatus = :paymentStatus");
			query.setParameter("orderId", orderId);
			query.setParameter("requestStatus", PaymentStatus.INITIATED.name());
			query.setParameter("paymentStatus", PaymentStatus.SUCCESSFUL.name());
			OrderPaymentDetail paymentDetail = (OrderPaymentDetail) query.getSingleResult();
			return paymentDetail;
		} catch (Exception e) {
			LOG.error("Encountered error while finding payment detail for orderId :: {}", orderId, e);
			throw new PaymentFailureException("Encountered error while fetching payment detail");
		}
	}

	@Override
	public OrderPaymentDetail getPaymentDetailFromOrderId(Integer orderId) throws PaymentFailureException {
		try {
			Query query = manager.createQuery("from OrderPaymentDetail E where E.orderId= :orderId");
			query.setParameter("orderId", orderId);
			OrderPaymentDetail paymentDetail = (OrderPaymentDetail) query.getSingleResult();
			return paymentDetail;
		} catch (Exception e) {
			LOG.error("Encountered error while finding payment detail for orderId :: {}", orderId, e);
			throw new PaymentFailureException("Encountered error while fetching payment detail");
		}
	}

	@Override
	public OrderPaymentDetail getPaymentDetail(String externalOrderId) {
		try {
			Query query = manager.createQuery("from OrderPaymentDetail E where E.externalOrderId= :externalOrderId");
			query.setParameter("externalOrderId", externalOrderId);
			return (OrderPaymentDetail) query.getSingleResult();
		} catch (Exception e) {
			LOG.error("Encountered error while finding payment detail for externalOrderId:: {}", externalOrderId, e);
			return null;
		}
	}

	@Override
	public OrderPaymentDetail getPaymentDetail(Integer paymentDetailId) {
		try {
			return manager.find(OrderPaymentDetail.class, paymentDetailId);
		} catch (Exception e) {
			LOG.error("Encountered error while finding payment detail for :: {}", paymentDetailId, e);
			return null;
		}
	}
	
	@Override
	public PaytmPaymentDetails getPaytmPaymentStatus(String orderId) {
		try {
			Query query = manager.createQuery("from PaytmPaymentDetails E where E.orderId= :orderId");
			query.setParameter("orderId", orderId);
			PaytmPaymentDetails paymentDetail = (PaytmPaymentDetails) query.getSingleResult();
			return paymentDetail;
		} catch (NoResultException ne) {
			LOG.info("PayTm payment details not found, new order " + orderId);
			return null;
		} catch (Exception e) {
			LOG.error("Exception, ... ", e);
			LOG.info("PayTm payment details not found, new order " + orderId);
			return null;
		}
	}

	@Override
	public PaytmPaymentDetails savePaytmPaymentDetails(PaytmPaymentDetails paytmPaymentDetails) {
		try {
			paytmPaymentDetails = add(paytmPaymentDetails);
			manager.flush();
			return paytmPaymentDetails;
		} catch (Exception e) {
			LOG.error("Encountered error while saving payment detail!! ", e);
			return null;
		}
	}

	@Override
	public boolean updatePaytmPaymentDetails(PaytmPaymentDetails paytmPaymentDetails) {
		try {
			Query query = manager.createQuery("update PaytmPaymentDetails E "
					+ "set E.status = :status, E.updatedTime = :updateTime where E.orderId = :orderId"
					+ " and E.paytmPaymentDetailId = :paytmPaymentDetailId");
			query.setParameter("status", paytmPaymentDetails.getStatus());
			query.setParameter("updateTime", AppUtils.getCurrentTimestamp());
			query.setParameter("orderId", paytmPaymentDetails.getOrderId());
			query.setParameter("paytmPaymentDetailId", paytmPaymentDetails.getPaytmPaymentDetailId());
			query.executeUpdate();
			manager.flush();
			return true;
		} catch (Exception e) {
			LOG.error("Encountered error while saving payment detail!! ", e);
			return false;
		}
	}

	@Override
	public OrderPaymentDetail updatePayTMResponse(PaymentStatus status, String orderId, String transactionId,
			String partnerPaymentStatus, Map<String, String> persistentAttributes) {
		OrderPaymentDetail paymentDetail = getActivePaymentDetail(orderId);
		if (paymentDetail != null) {
			paymentDetail.setPartnerPaymentStatus(partnerPaymentStatus);
			paymentDetail.setPartnerTransactionId(transactionId);
			paymentDetail.setPaymentStatus(status.name());
			paymentDetail.setResponseTime(AppUtils.getCurrentTimestamp());
			manager.merge(paymentDetail);
			manager.flush();
			createParameters(paymentDetail.getOrderPaymentDetailId(), persistentAttributes);
		}
		return paymentDetail;
	}

	@Override
	public List<OrderPaymentDetail> getInitiatedPaymentDetails(PaymentStatus requestStatus) {
		int tenMinutesBefore = -600000;
		Date tenMinutesBeforeTimeStamp = AppUtils.getDateBeforeOrAfterInSeconds(AppUtils.getCurrentTimestamp(),
				tenMinutesBefore);
		List<OrderPaymentDetail> orderPaymentDetailList = null;
		Query query = manager.createQuery("FROM OrderPaymentDetail where orderSettlementId is null and"
				+ " orderId is null and requestStatus = :requestStatus" + " and requestTime >= :tenMinutesBefore");
		query.setParameter("requestStatus", requestStatus.name()).setParameter("tenMinutesBefore",
				tenMinutesBeforeTimeStamp);
		orderPaymentDetailList = (List<OrderPaymentDetail>) query.getResultList();
		return orderPaymentDetailList;
	}

	@Override
	public void createPaymentEvent(OrderPaymentEvent paymentEvent) {
		manager.persist(paymentEvent);
		manager.flush();
	}

	@Override
	public Map updateAndRedirect(PaymentResponse response, boolean validation) {
		PaymentStatus status = validation ? PaymentStatus.SUCCESSFUL : PaymentStatus.FAILED;
		OrderPaymentDetail paymentDetail = updateResponse(status, response);
		if(paymentDetail == null){
			validation = false;
		}
		Map returnResult = new HashMap<String,String>();
		if (validation) {
			returnResult.put("orderId", response.getOrderId());
			returnResult.put("txnId", response.getTransactionId());
		} else {
			returnResult.put("error", response.getOrderId());
			returnResult.put("reason", response.getReason());
		}
		return returnResult;
	}

	@Override
	public Map update(PaymentResponse response, boolean validation) {
		PaymentStatus status = validation ? PaymentStatus.SUCCESSFUL : PaymentStatus.FAILED;
		updateResponseUsingPartnerOrderId(status, response);
		Map returnResult = new HashMap<String, String>();
		if (validation) {
			returnResult.put("orderId", response.getOrderId());
			returnResult.put("txnId", response.getTransactionId());
		} else {
			returnResult.put("error", response.getOrderId());
			returnResult.put("reason", response.getReason());
		}
		return returnResult;
	}

	@Override
	public Map updateForDineIn(PaymentResponse response, boolean validation) {
		PaymentStatus status = validation ? PaymentStatus.REFUND_INITIATED : PaymentStatus.FAILED;
		updateResponseUsingPartnerOrderId(status, response);
		Map returnResult = new HashMap<String, String>();
		if (validation) {
			returnResult.put("orderId", response.getOrderId());
			returnResult.put("txnId", response.getTransactionId());
		} else {
			returnResult.put("error", response.getOrderId());
			returnResult.put("reason", response.getReason());
		}
		return returnResult;
	}

	@Override
	public List<OrderPaymentDetail> getTransactionsApplicableForRefund(String contact) {
		try {
			Query query = manager.createQuery("from OrderPaymentDetail E where (E.contactNumber = :contact"
					+ " and E.paymentStatus= :paymentStatus and E.requestStatus = :requestStatus)"
					+ " or (E.contactNumber = :contact and E.orderId is null and E.orderSettlementId is null"
					+ " and E.requestStatus = :requestStatus and E.paymentStatus IN :statusList and E.requestTime >= :date)"
					+ " order by E.orderPaymentDetailId desc");
			query.setParameter("contact", contact);
			query.setParameter("requestStatus", PaymentStatus.INITIATED.name());
			query.setParameter("paymentStatus", PaymentStatus.DISASSOCIATED.name());
			query.setParameter("statusList", statusList);
			query.setParameter("date", props.getRefundFlowDate());
			return query.getResultList();
		} catch (Exception e) {
			LOG.error("Could not find payment detail due to :::", e);
			return null;
		}
	}

//	private Map updateAndRedirectToIngenico(PaymentResponse response, boolean validation) {
//		PaymentStatus status = validation ? PaymentStatus.SUCCESSFUL : PaymentStatus.FAILED;
//		OrderPaymentDetail paymentDetail = updateResponse(status, response);
//		if(paymentDetail == null){
//			validation = false;
//		}
//		Map returnResult = new HashMap<String, String>();
//		if (validation) {
//			returnResult.put("orderId", response.getOrderId());
//			returnResult.put("txnId", response.getTransactionId());
//
//		} else {
//			returnResult.put("error", response.getOrderId());
//			returnResult.put("reason", response.getReason());
//		}
//		return returnResult;
//	}

//	private Map updateAndPushForPaytmUpi( PaymentResponse response, boolean validation) {
//		PaymentStatus status = validation ? PaymentStatus.SUCCESSFUL : PaymentStatus.FAILED;
//		paymentGatewayDao.updateResponse(status, response);
//		Map returnResult = new HashMap<String, String>();
//		if (validation) {
//			returnResult.put("orderId", response.getOrderId());
//			returnResult.put("txnId", response.getTransactionId());
//		} else {
//			returnResult.put("error", response.getOrderId());
//			returnResult.put("reason", response.getReason());
//		}
//		return returnResult;
//	}

	@Override
	public Boolean syncOrderAndInitiatePaytmRefundInBulk(List<OrderPaymentDetail> opdList) {
		try {
			String sql = "SELECT " +
					"opd.ORDER_PAYMENT_DETAIL_ID, " +
					"os.SETTLEMENT_ID, " +
					"os.ORDER_ID, " +
					"od.ORDER_STATUS " +
					"FROM ORDER_PAYMENT_DETAIL opd " +
					"LEFT JOIN ORDER_EXTERNAL_SETTLEMENT_DATA oes " +
					"  ON oes.EXTERNAL_TRANSACTION_ID = opd.EXTERNAL_ORDER_ID " +
					"LEFT JOIN ORDER_SETTLEMENT os " +
					"  ON os.SETTLEMENT_ID = oes.SETTLEMENT_ID " +
					"LEFT JOIN ORDER_DETAIL od " +
					"  ON od.ORDER_ID = os.ORDER_ID " +
					" AND od.ORDER_TYPE = 'ORDER' " +
					"WHERE opd.ORDER_PAYMENT_DETAIL_ID IN (:orderPaymentDetailList) " +
					" AND opd.PAYMENT_STATUS IN ('SUCCESS', 'SUCCESSFUL', 'ACCEPTED_SUCCESS')";

			List<Integer> opdIds = opdList.stream().map(OrderPaymentDetail::getOrderPaymentDetailId).collect(Collectors.toList());
			List<Object[]> resultList = manager.createNativeQuery(sql)
					.setParameter("orderPaymentDetailList", opdIds)
					.getResultList();

			// Map <opdId, [settlementId, orderId, orderStatus]>
			Map<Integer, Object[]> resultMap = new HashMap<>();
			for (Object[] row : resultList) {
				Integer opdId = ((Number) row[0]).intValue();
				resultMap.put(opdId, row);
			}

			for (OrderPaymentDetail opd : opdList) {
				Object[] result = resultMap.get(opd.getOrderPaymentDetailId());

				Integer settlementId = null;
				Integer orderId = null;
				String orderStatus = null;

				if (Objects.nonNull(result)) {
					settlementId = result[1] != null ? ((Number) result[1]).intValue() : null;
					orderId = result[2] != null ? ((Number) result[2]).intValue() : null;
					orderStatus = result[3] != null ? (String) result[3] : null;
				}

				// settlement & orderId always set
				opd.setOrderSettlementId(settlementId);
				opd.setOrderId(orderId);

				// refund condition: orderStatus null OR CANCELLED
				if ((Objects.isNull(orderStatus) || OrderStatus.CANCELLED.name().equals(orderStatus))) {
					opd.setRequestStatus(PaymentStatus.REFUND_INITIATED.name());
					opd.setPaymentStatus(PaymentStatus.REFUND_INITIATED.name());
				}

				manager.merge(opd);
			}
			manager.flush();
			return true;
		} catch (Exception e) {
			LOG.error("Exception while fetching payment details for bulk refund initiation", e);
		}
		return false;
	}
}
