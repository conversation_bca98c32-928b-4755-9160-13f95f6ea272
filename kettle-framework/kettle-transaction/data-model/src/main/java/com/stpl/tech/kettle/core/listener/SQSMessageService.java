package com.stpl.tech.kettle.core.listener;

import com.amazon.sqs.javamessaging.SQSSession;
import com.amazonaws.regions.Regions;
import com.stpl.tech.kettle.core.service.OrderManagementService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.master.notification.SQSNotification;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.jms.JMSException;
import javax.jms.MessageConsumer;
import javax.jms.Session;

@Service
public class SQSMessageService {

	private static final Logger LOG = LoggerFactory.getLogger(SQSMessageService.class);

	@Autowired
	private EnvironmentProperties props;

	@Autowired
	private OrderManagementService service;

	@PostConstruct
	public void init() throws JMSException {
		Regions region = AppUtils.getRegion(props.getEnvironmentType());
		SQSSession session = SQSNotification.getInstance().getSession(region, Session.CLIENT_ACKNOWLEDGE);
		CustomerTransactionViewListener listener = new CustomerTransactionViewListener(service);
		MessageConsumer consumer = SQSNotification.getInstance().getConsumer(session, props.getEnvironmentType().name(),
				"_CUSTOMER_TRANSACTION_VIEW");
		consumer.setMessageListener(listener);
		try{
			OrderInfoListener orderInfoListener = new OrderInfoListener(service);
			MessageConsumer orderInfoConsumer = SQSNotification.getInstance().getConsumer(session,props.getEnvironmentType().name(),"_ORDER_INFO_CACHE");
			orderInfoConsumer.setMessageListener(orderInfoListener);
		}catch (Exception e){
			LOG.error("Error While Initatting Order Info Cache Queue Consumer : ",e);
		}
		SQSNotification.getInstance().getSqsConnection(region).start();
	}

	public void stopQueueProcessing(Regions region) throws JMSException {
		LOG.info("Stopping Queueing Service");
		SQSNotification.getInstance().getSqsConnection(region).stop();
		LOG.info("Stopped Queueing Service");
	}

	public void startQueueProcessing(Regions region) throws JMSException {
		LOG.info("Starting Queueing Service");
		SQSNotification.getInstance().getSqsConnection(region).start();
		LOG.info("Started Queueing Service");
	}
}
