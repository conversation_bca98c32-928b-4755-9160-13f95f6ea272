/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

import static javax.persistence.GenerationType.IDENTITY;

// Generated 16 Aug, 2015 4:57:46 PM by Hibernate Tools 4.0.0

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * OrderEmailNotification generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "ORDER_EMAIL_NOTIFICATION")
public class OrderEmailNotification implements java.io.Serializable {

	private Integer orderEmailId;
	private String entryType;
	private int orderId;
	private int retryCount;
	private String contact;
	private String emailAddress;
	private String userRequested;
	private String isEmailDelivered;
	private String isEmailVerified;
	private Date requestTime;
	private Date executionTime;
	private String errorMessage;
    private String customerName;
	private Integer brandId;

	public OrderEmailNotification() {
	}

	public OrderEmailNotification(String entryType, int orderId, int retryCount, String userRequested,
			Date requestTime) {
		this.entryType = entryType;
		this.orderId = orderId;
		this.retryCount = retryCount;
		this.userRequested = userRequested;
		this.requestTime = requestTime;
	}

	public OrderEmailNotification(String entryType, int orderId, int retryCount, String emailAddress,
			String userRequested, String isEmailDeivered, String isEmailVerified, Date requestTime, Date executionTime,
			String errorMessage) {
		this.entryType = entryType;
		this.orderId = orderId;
		this.retryCount = retryCount;
		this.emailAddress = emailAddress;
		this.userRequested = userRequested;
		this.isEmailDelivered = isEmailDeivered;
		this.isEmailVerified = isEmailVerified;
		this.requestTime = requestTime;
		this.executionTime = executionTime;
		this.errorMessage = errorMessage;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "ORDER_EMAIL_ID", unique = true, nullable = false)
	public Integer getOrderEmailId() {
		return this.orderEmailId;
	}

	public void setOrderEmailId(Integer orderEmailId) {
		this.orderEmailId = orderEmailId;
	}

	@Column(name = "ORDER_ID", nullable = false)
	public int getOrderId() {
		return this.orderId;
	}

	public void setOrderId(int orderId) {
		this.orderId = orderId;
	}

	@Column(name = "RETRY_COUNT", nullable = false)
	public int getRetryCount() {
		return this.retryCount;
	}

	public void setRetryCount(int retryCount) {
		this.retryCount = retryCount;
	}

	@Column(name = "EMAIL_ADDRESS", length = 200)
	public String getEmailAddress() {
		return this.emailAddress;
	}

	public void setEmailAddress(String emailAddress) {
		this.emailAddress = emailAddress;
	}

	@Column(name = "USER_REQUESTED", nullable = false, length = 1)
	public String getUserRequested() {
		return this.userRequested;
	}

	public void setUserRequested(String userRequested) {
		this.userRequested = userRequested;
	}

	@Column(name = "IS_EMAIL_DELIVERED", length = 1)
	public String getIsEmailDelivered() {
		return this.isEmailDelivered;
	}

	public void setIsEmailDelivered(String isEmailDeivered) {
		this.isEmailDelivered = isEmailDeivered;
	}

	@Column(name = "IS_EMAIL_VERIFIED", length = 1)
	public String getIsEmailVerified() {
		return this.isEmailVerified;
	}

	public void setIsEmailVerified(String isEmailVerified) {
		this.isEmailVerified = isEmailVerified;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "REQUEST_TIME", nullable = false, length = 19)
	public Date getRequestTime() {
		return this.requestTime;
	}

	public void setRequestTime(Date requestTime) {
		this.requestTime = requestTime;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "EXECUTION_TIME", length = 19)
	public Date getExecutionTime() {
		return this.executionTime;
	}

	public void setExecutionTime(Date executionTime) {
		this.executionTime = executionTime;
	}

	@Column(name = "ERROR_MESSAGE", length = 5000)
	public String getErrorMessage() {
		return this.errorMessage;
	}

	public void setErrorMessage(String errorMessage) {
		this.errorMessage = errorMessage;
	}

	@Column(name = "ENTRY_TYPE", nullable = false, length = 30)
	public String getEntryType() {
		return entryType;
	}

	public void setEntryType(String entryType) {
		this.entryType = entryType;
	}

    @Column(name = "CONTACT", nullable = true, length = 15)
    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    @Column(name = "CUSTOMER_NAME", nullable = true, length = 45)
    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

	@Column(name = "BRAND_ID", nullable = true)
	public Integer getBrandId() {
		return brandId;
	}

	public void setBrandId(Integer brandId) {
		this.brandId = brandId;
	}
}
