/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */
package com.stpl.tech.kettle.customer.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;

@Service
@RefreshScope
public class EnvironmentProperties {

    private static final Logger LOG = LoggerFactory.getLogger(EnvironmentProperties.class);
    @Autowired
    private Environment env;

    private List<String> sdpConfirmationsNumbers;

    public Environment getEnv() {
        return env;
    }

    public EnvType getEnvironmentType() {
        return EnvType.valueOf(env.getProperty("environment.type"));
    }

    public String getRecieptEmail() {
        return env.getProperty("mail.receipt.email");
    }

    public String getRecieptEmailDohful() {
        return env.getProperty("mail.receipt.email.dohful");
    }

    public String getUndeliveredEmail() {
        return env.getProperty("mail.undelivered.email");
    }

    public int getDummyCustomerId() {
        return env.getProperty("mail.dummy.customer.id", Integer.class);
    }

    public String getToEmail() {
        return env.getProperty("mail.to.email");
    }

    public int getRetryCount() {
        return env.getProperty("mail.retry.count", Integer.class);
    }

    public int getSleepTime() {
        return env.getProperty("mail.thread.sleep.time", Integer.class);
    }

    public String getBasePath() {
        return env.getProperty("server.base.dir");
    }

    public String getDroolBasePath() {
        return env.getProperty("server.drool.base.dir");
    }

    public String getVerifyEmailPath() {
        return env.getProperty("account.verify.email.path");
    }

    public String getVerifyEmailDohfulPath() {
        return env.getProperty("account.verify.dohful.email.path","https://cafes.chaayos.com/verifyemail");
    }

    public Long getPingDelayTime() {
        return Long.valueOf(env.getProperty("unit.ping.delay.time", "180000"));
    }

    public boolean getRunValidateFilter() {
        return Boolean.valueOf(env.getProperty("run.validate.filter", "false"));
    }

    public boolean getTrackInventory() {
        return Boolean.valueOf(env.getProperty("inventory.track"));
    }

    public boolean getRunAutoDayClose() {
        return Boolean.valueOf(env.getProperty("run.auto.dayclose", "false"));
    }

    public boolean getRunIntraDayExternalReport() {
        return Boolean.valueOf(env.getProperty("run.intraday.external.reports", "false"));
    }

    public boolean getSendStockOutNotifications() {
        return Boolean.valueOf(env.getProperty("send.stockout.notification", "false"));
    }

    public boolean getRunAutoReports() {
        return Boolean.valueOf(env.getProperty("run.auto.report", "false"));
    }

    public boolean getAutomatedLoyaltySMS() {
        return Boolean.valueOf(env.getProperty("send.automated.loyalty.sms", "false"));
    }

    public boolean getExpireSignupOffer() {
        return Boolean.valueOf(env.getProperty("expire.signup.offer", "false"));
    }

    public int getExpireSignupOfferDays() {
        return Integer.valueOf(env.getProperty("expire.signup.offer.days", "30"));
    }

    public boolean getHourlyOffer() {
        return Boolean.valueOf(env.getProperty("send.hourly.offer.notifications", "false"));
    }

    public boolean getAutomatedFeedbackSMS() {
        return Boolean.valueOf(env.getProperty("send.automated.feedback.sms", "false"));
    }

    public boolean getAutomatedNPSSMS() {
        return Boolean.valueOf(env.getProperty("send.automated.nps.sms", "false"));
    }

    public boolean getNPSConsiderationForDelivery() {
        return Boolean.valueOf(env.getProperty("send.automated.delivery.nps.sms", "false"));
    }

    public boolean getAutomatedLowRatingFeedbackSMS() {
        return Boolean.valueOf(env.getProperty("send.automated.low.rating.feedback.sms", "false"));
    }

    public boolean getAutomatedReminderSMS() {
        return Boolean.valueOf(env.getProperty("send.automated.reminder.sms", "false"));
    }

    public boolean getRunAutoIntradayReports() {
        return Boolean.valueOf(env.getProperty("run.auto.intraday.report", "false"));
    }

    public boolean getAllotedNoToSDPSMS() {
        return Boolean.valueOf(env.getProperty("delivery.allotment.sdp.sms", "false"));
    }

    public boolean getReallotmentSDPSMS() {
        return Boolean.valueOf(env.getProperty("delivery.reallotment.sdp.sms", "false"));
    }

    public boolean getAllotedNoToCustomerSMS() {
        return Boolean.valueOf(env.getProperty("delivery.alloted.sdp.customer.sms", "false"));
    }

    public boolean getDeliveryConfirmationOfCustomerSMS() {
        return Boolean.valueOf(env.getProperty("delivery.confirmation.customer.sms", "false"));
    }

    public boolean getFreeChaiDeliveryConfirmationOfCustomerSMS() {
        return Boolean.valueOf(env.getProperty("free.chai.delivery.sms", "false"));
    }

    public boolean enableFreeChaiDeliveryConfirmationOfCustomerSMS() {
        return Boolean.valueOf(env.getProperty("free.chai.delivery.enable", "false"));
    }

    public Integer getNoOfMonthsForFreeChaiDeliveryCondition() {
        return Integer.valueOf(env.getProperty("free.chai.delivery.months"));
    }

    public Integer getTranactionLimitForFreeChaiDeliveryCondition() {
        return Integer.valueOf(env.getProperty("free.chai.delivery.transaction"));
    }

    public List<String> getDeliveryConfirmationBySDPNosList() {
        if (sdpConfirmationsNumbers == null || sdpConfirmationsNumbers.size() == 0) {
            sdpConfirmationsNumbers = new ArrayList<>();
            String prop = env.getProperty("delivery.confirmation.sdp.no.list", "");
            if (prop != null && prop.length() > 0) {
                sdpConfirmationsNumbers = Arrays.asList(prop.split(","));
            }

        }
        return sdpConfirmationsNumbers;
    }

    public Set<Integer> getStockoutEmailExclusions() {
        Set<Integer> set = new HashSet<>();
        if (env.getProperty("unit.stockout.exclusions") != null) {
            String[] units = env.getProperty("unit.stockout.exclusions").split(",");
            for (String unit : units) {
                set.add(Integer.valueOf(unit));
            }
        }
        return set;
    }

    public boolean getSendAutomatedOTPSMS() {
        return Boolean.valueOf(env.getProperty("send.automated.otp.sms", "false"));
    }

    public boolean getSendAutomzatedOTPViaIVR() {
        return Boolean.valueOf(env.getProperty("send.automated.otp.via.ivr", "false"));
    }

    public Integer getIVRId() {
        return Integer.valueOf(env.getProperty("ivr.id"));
    }

    public boolean getSendOTPLastFourDigits() {
        return Boolean.valueOf(env.getProperty("send.otp.last.four.digit", "false"));
    }


    public boolean getSendAutomatedWelcomeSMS() {
        return Boolean.valueOf(env.getProperty("send.automated.welcome.sms", "false"));
    }

    public boolean getRunExpenseReport() {
        return Boolean.valueOf(env.getProperty("run.auto.expense.report", "false"));
    }

    public boolean publishOrders() {
        return Boolean.valueOf(env.getProperty("sqs.publish.queue.order", "false"));
    }

    public boolean publishInventory() {
        return Boolean.valueOf(env.getProperty("sqs.publish.queue.inventory", "false"));
    }

    public String addConsumptionURL() {
        return env.getProperty("scm.consumption.url");
    }

    public String getConsumptionURL() {
        return env.getProperty("scm.get.consumption.url");
    }

    public String getDayCloseURLinSCM() {
        return env.getProperty("scm.day.close.url");
    }

    public String calculatePnLURL() {
        return env.getProperty("scm.pnl.url");
    }

    public String checkOpeningURL() {
        return env.getProperty("scm.opening.check.url");
    }

    public String verifyPriceDataURL() {
        return env.getProperty("verify.price.data");
    }

    public String addWastageURL() {
        return env.getProperty("add.wastage.url");
    }

//	public String getURLforFeedback(int brandId) {
//		return env.getProperty("feedback.url." + brandId);
//	}

//	public String getURLforDineinFeedback(int brandId) {
//		return getURLforFeedback(brandId) + env.getProperty("feedback.endpoint.dinein." + brandId);
//	}

//	public String getURLforCafeNPS(int brandId) {
//		return getURLforFeedback(brandId) + env.getProperty("feedback.endpoint.nps.cafe." + brandId);
//	}


    //--
//	public String getURLforDeliveryNPS(int brandId) {
//		return getURLforFeedback(brandId) + env.getProperty("feedback.endpoint.nps.delivery." + brandId);
//	}
    // --
//	public String getURLforDeliveryNPSWithOnlyOrder(int brandId) {
//		return getURLforFeedback(brandId) + env.getProperty("feedback.endpoint.nps.delivery.only.order." + brandId);
//	}

    public boolean getSendFeedbackMessageForSwiggyDelivery() {
        return Boolean.parseBoolean(env.getProperty("send.feedback.message.delivery.swiggy", "false"));
    }
    // --
//	public String getURLforDeliveryFeedback(int brandId) {
//		return getURLforFeedback(brandId) + env.getProperty("feedback.endpoint.delivery." + brandId);
//	}
// --
//	public String getURLforLowRatingDineinFeedback(int brandId) {
//		return getURLforFeedback(brandId) + env.getProperty("feedback.endpoint.low.rating.dinein." + brandId);
//	}

//	public String getURLforLowRatingDeliveryFeedback(int brandId) {
//		return getURLforFeedback(brandId) + env.getProperty("feedback.endpoint.low.rating.delivery." + brandId);
//	}

    public int getDineInFeedbackMessageDelay() {
        return Integer.valueOf(env.getProperty("automated.feedback.sms.dinein.time.gap", "45"));
    }

    public int getDineInNPSMessageDelay() {
        return Integer.valueOf(env.getProperty("automated.nps.sms.dinein.time.gap", "45"));
    }

    public int getNextDayFeedbackMessageDelay() {
        return Integer.valueOf(env.getProperty("automated.feedback.sms.next.day.time.gap", "540"));
    }

    public int getThresholdFeedbackMessageDelay() {
        return Integer.valueOf(env.getProperty("automated.feedback.sms.threshold.time.gap", "1260"));
    }

    public int getNPSThresholdMessageDelay() {
        return Integer.valueOf(env.getProperty("automated.nps.sms.threshold.time.gap", "180"));
    }

    public int getDeliveryFeedbackMessageDelay() {
        return Integer.valueOf(env.getProperty("automated.feedback.sms.delivery.time.gap", "45"));
    }

    public int getDeliveryNPSMessageDelay() {
        return Integer.valueOf(env.getProperty("automated.nps.sms.delivery.time.gap", "80"));
    }

//	public String getFeedbackRedirectURL(int brandId) {
//		return getURLforFeedback(brandId) + env.getProperty("feedback.endpoint.redirect.url." + brandId);
//	}

    public String getMassOfferURL() {
        return env.getProperty("mass.offer.url");
    }

    public Boolean getFeedbackNotoficationSMS() {
        return Boolean.valueOf(env.getProperty("feedback.notification.sms", "false"));
    }

    public Boolean getFeedbackNotoficationSlack() {
        return Boolean.valueOf(env.getProperty("feedback.notification.slack", "false"));
    }

    public Boolean getAutomatedFeedbackSMSForAll() {
        return Boolean.valueOf(env.getProperty("automated.feedback.sms.trigger.for.all", "false"));
    }

    public int getAccessCodeExpirationTime() {
        return Integer.valueOf(env.getProperty("access.code.expiration.time.gap", "45"));
    }

    public Boolean getServiceTaxExemptRule() {
        return Boolean.valueOf(env.getProperty("exempt.service.tax", "false"));
    }

    public Boolean getDispatchDelayEmailTrigger() {
        return Boolean.valueOf(env.getProperty("delivery.dispatch.delay.trigger.email", "false"));
    }

    public Boolean getDeliveryDelayEmailTrigger() {
        return Boolean.valueOf(env.getProperty("delivery.delivered.delay.trigger.email", "false"));
    }

    public Boolean getDeliveryDelaySlackTrigger() {
        return Boolean.valueOf(env.getProperty("delivery.delivered.delay.trigger.slack", "false"));
    }

    public int getDispactDelayEmailTriggerTimeGap() {
        return Integer.valueOf(env.getProperty("delivery.dispatch.delay.time.gap", "840"));
    }

    public int getDeliveryDelayEmailTriggerTimeGap() {
        return Integer.valueOf(env.getProperty("delivery.delivered.delay.time.gap", "3000"));
    }

    public int getBulkDeliveryDelayEmailTriggerTimeGap() {
        return Integer.valueOf(env.getProperty("delivery.bulk.delivered.delay.time.gap", "4500"));
    }

    public int getDeliveryBuffer() {
        return Integer.valueOf(env.getProperty("delivery.buffer.time", "60"));
    }

    public int getRiderOrderLimit() {
        return Integer.valueOf(env.getProperty("rider.order.limit", "10"));
    }

    public String getDeliverySupportEmailId() {
        return env.getProperty("delivery.support.email.id", "<EMAIL>");
    }

    public String getDeliverySupportSecondaryEmailId() {
        return env.getProperty("delivery.support.secondary.email.id", "<EMAIL>");
    }

    public String getInventoryQueuePrefix() {
        return env.getProperty("sqs.message.queue.prefix", "DEV");
    }

    public Date getRefundCutOffDate() {
        return AppUtils.parseDate(env.getProperty("refund.cutoff.date", AppUtils.getCurrentDateISTFormatted()));
    }

    public Date getRefundFlowDate() {
        return AppUtils.parseDate(env.getProperty("refund.flow.date", AppUtils.getCurrentDateISTFormatted()));
    }

    public String getWebengageLicence() {
        return env.getProperty("webengage.licence");
    }

    public String getWebengageKey() {
        return env.getProperty("webengage.auth");
    }

    public String getWebengageBaseUrl() {
        return env.getProperty("webengage.base.url");
    }

    public String getSCMClientToken() {
        return env.getProperty("scm.client.token");
    }

    public String getKettleClientToken() {
        return env.getProperty("kettle.client.token");
    }

    public String getChannelPartnerClientToken() {
        return env.getProperty("channelpartner.client.token");
    }

    public BigDecimal getEmployeeMealPerDayAmountLimit() {
        return new BigDecimal(env.getProperty("employee.meal.amount.limit"));
    }

    public Integer getEmployeeMealDayLimit() {
        return Integer.valueOf(env.getProperty("employee.meal.day.limit"));
    }

    public Integer getEmployeeMealMonthlyStartDate() {
        return Integer.valueOf(env.getProperty("employee.meal.monthly.start.date"));
    }

    public boolean getRawPrintingSatus() {
        return Boolean.valueOf(env.getProperty("raw.print.enabled", "false"));
    }

    public String getTrueCallerServiceUrl() {
        return env.getProperty("truecaller.service.url", "");
    }

    public String getInventoryServiceBasePath() {
        return env.getProperty("inventory.base.url");
    }

    public String getSCMServiceBasePath() {
        return env.getProperty("scm.base.url");
    }

    public String getS3Bucket() {
        return env.getProperty("amazon.s3.bucket", "chaayosdevtest");
    }

    public String getReceiptDownloadBaseUrl() {
        return env.getProperty("order.receipt.download.base.url");
    }

    public boolean isKioskOrderPDFReceiptSMS() {
        return Boolean.valueOf(env.getProperty("send.kiosk.order.pdf.receipt.sms", "false"));
    }

    public String getBillPromotion() {
        try {
            if (Boolean.valueOf(env.getProperty("promotional.offer.active", "false"))) {
                String startDateStr = env.getProperty("promotional.offer.start.date", "2011-01-01");
                String endDateStr = env.getProperty("promotional.offer.end.date", "2011-01-01");
                String offerHtml = env.getProperty("promotional.offer.html.text");
                Date startDate = AppUtils.getDate(startDateStr, "yyyy-MM-dd");
                Date endDate = AppUtils.getDate(endDateStr, "yyyy-MM-dd");
                Date currentDate = AppUtils.getCurrentDate();
                if (currentDate.compareTo(startDate) >= 0 && currentDate.compareTo(endDate) <= 0) {
                    return offerHtml;
                }
            }
        } catch (Exception e) {
            LOG.error("Error while fetching the promotional offer", e);
        }
        return null;
    }

    public String getAGSChecksumKey() {
        return env.getProperty("ags.checksum.key", "CHAAYOS@123");
    }

    public String getAppOrderStatusQueueRegion() {
        return env.getProperty("aws.sqs.dine.in.app.status.queue.region", "ap-south-1");
    }

    public String getOTPHashKey() {
        return env.getProperty("automated.otp.sms.hash", "CHAAYOS@123");
    }

    public Date getCashBackStartDate() {
        String cashBackStartDate = env.getProperty("cash.back.start.date", "2020-11-04");
        return AppUtils.getDate(cashBackStartDate, "yyyy-MM-dd");
    }

    public Date getCashBackEndDate() {
        String cashBackEndDate = env.getProperty("cash.back.end.date", "2020-11-08");
        return AppUtils.getDate(cashBackEndDate, "yyyy-MM-dd");
    }

    public Date getCashBackCardEndDate() {
        String cashBackCardEndDate = env.getProperty("cash.back.card.end.date", "2020-12-08");
        return AppUtils.getDate(cashBackCardEndDate, "yyyy-MM-dd");
    }

    public Date getCashBackCardStartDate() {
        String cashBackCardStartDate = env.getProperty("cash.back.card.start.date", "2020-11-09");
        return AppUtils.getDate(cashBackCardStartDate, "yyyy-MM-dd");
    }

    public BigDecimal getCashBackPercentage() {
        String cashBackPercentage = env.getProperty("cash.back.percentage", "50");
        return new BigDecimal(cashBackPercentage);
    }

    public int getNumberOfMinuteForRefundBlock() {
        String numberOfMinutes = env.getProperty("number.minutes.refund.block", "8");
        return Integer.parseInt(numberOfMinutes);
    }

    public String getDineInCRMBaseUrl() {
        return env.getProperty("dinein.crm.base.url", "http://15.206.45.59:8989/app-crm/");
    }

    public String getKettleServiceBaseUrl(){
        return env.getProperty("kettle.service.base.url");
    }

    public String getPartnerServiceBaseUrl(){
        return env.getProperty("partner.service.base.url");
    }

    public String getDineInToken() {
        return env.getProperty("dinein.token", "eyJhbGciOiJIUzI1NiJ9.eyJzZXNzaW9uS2V5IjoiUHVaZWdpRzNMazRuNllkdWl6NUxOaWVrVTRmSXlNTUZpclBsVHBFMnV6TjZuTWV0SXJZNmh4WXdVZ3c0SGtBWk5MaHgzc2d5MUxFWlxuUkhUYXY3RkcxVW1XZEpmK0oraE9SRnJ6UTZMakN0UT0iLCJ1bml0SWQiOjEwMDAwLCJ0ZXJtaW5hbElkIjoxLCJ1c2VySWQiOjEyMDA1NywiaWF0IjoxNjA2NDgwNTc1LCJpc3N1ZXIiOiJLRVRUTEVfU0VSVklDRSJ9.vKyPcdsdd-FjzCHVwScqTBCHc6rMl79J2a-faxWdNi4");
    }

    public Integer getMaxTaxableAmount() {
        return Integer.parseInt(env.getProperty("ordering.maxTaxAbleAmount"));
    }

    public Integer getMissionGaramChaiMaximumOrderValue() {
        return Integer.parseInt(env.getProperty("mission.garamChai.maxOrderValue"));
    }

    public String getS3CrmBucket() {
        return env.getProperty("amazon.s3.product.bucket", "product.image.dev");
    }

    public String getS3ReportBucket() {
        return env.getProperty("amazon.s3.report.bucket", "dev.chaayos.report");
    }

    public Integer getMissionGaramChaiThresholdMPR() {
        return Integer.parseInt(env.getProperty("mission.garamChai.thresholdMPR"));
    }

    public String getCrmHostUrl() {
        return env.getProperty("crm.screen.host.url", "http://d1nqp92n3q8zl7.cloudfront.net/crmapp/");
    }

    public List<Integer> getMissionGaramChaiBrandIds() {
        List<String> list = Arrays.asList(env.getProperty("mission.garamChai.brandIds").split(","));
        List<Integer> newList = list.stream()
                .map(s -> Integer.parseInt(s))
                .collect(Collectors.toList());
        return newList;
    }

    public List<Integer> getMissionGaramChaiChannelpartnerIds() {
        List<String> list = Arrays.asList(env.getProperty("mission.garamChai.channelPartnerIds").split(","));
        List<Integer> newList = list.stream()
                .map(s -> Integer.parseInt(s))
                .collect(Collectors.toList());
        return newList;
    }

    public Boolean isMissionGaramChaiActive() {
        return Boolean.parseBoolean(env.getProperty("mission.garamChai.enable"));
    }

    public boolean sendAppDownloadLinkToCustomer() {
        return Boolean.valueOf(env.getProperty("app.download.link", "false"));
    }

    public boolean getLoyaltyGiftingReminderSMS() {
        return Boolean.valueOf(env.getProperty("loyalty.gifting.reminder", "false"));
    }

    public String getCleverTapAccountId() {
        return env.getProperty("clevertap.accountId");
    }

    public String getCleverTapPasscode() {
        return env.getProperty("clevertap.passcode");
    }

    public String getBonusPoints() {
        return env.getProperty("loyalty.gifting.bonus.point");
    }

    public String getLoyaltyPointsPerChai() {
        return env.getProperty("loyalty.gifting.per_chai.point");
    }

    public Boolean getRecommendationProduct(){  return Boolean.valueOf(env.getProperty("customer.recomendation"));}

    public String getS3InventoryBucket() {
        return env.getProperty("inventory.prediction.s3.path");
    }

    public String getMasterBasePath() {
        return env.getProperty("base.path.kettle.master");
    }

    public String getKettleJobsToken() {
        return env.getProperty("kettle.jobs.token","eyJhbGciOiJIUzI1NiJ9.***********************************************************************************************************************************************.dHGj5k2FJz6ctXIqBEYp9B385iS6__Ec-Vm8Msa0dU0");
    }

    public boolean getCleverTapEnabled(){
        return Boolean.valueOf(env.getProperty("clevertap.push.enable","false"));
    }

    public String getOrderFeedbackType() {return env.getProperty("order.feedback.type");}

    public String getDineInTokenForKettle(){return env.getProperty("dinein.token","eyJhbGciOiJIUzI1NiJ9.eyJwYXJ0bmVyTmFtZSI6IkRJTkVfSU4iLCJlbnZUeXBlIjoiREVWIiwicGFydG5lcklkIjoxNSwicGFzc0NvZGUiOiJSQzdKODIySDdGREdORTYiLCJpYXQiOjE1ODczODI3NjF9.S5xx5c4chqH36auT8QmOlTwlVJwx-JlXDjCNBs6eR8Q");}

    public String getIconImageHostUrl() { return env.getProperty("icon.image.host.url","");
    }

    public String getMasterProductMetadataAuth() {
        return env.getProperty("master.product.metadata.auth");
    }

    public String getKettleServiceOrderManagementAuth() {
        return env.getProperty("kettle.service.order.management.auth");
    }

    public String getThankYouPageBasePath() {
        return env.getProperty("thankyou.page.base.path");
    }

	public int getSubscriptionProductId() {
		return Integer.valueOf(env.getProperty("subscription.product.type", "3810"));
	}

	public boolean isDinePostOrderOfferEnabled() {
		return Boolean.valueOf(env.getProperty("dinein.post.order.offer.enabled", "false"));
	}

    public boolean isDeliveryPostOrderOfferEnabled() {
        return Boolean.valueOf(env.getProperty("delivery.post.order.offer.enabled", "false"));
    }

	public Integer getDinePostOrderOfferCheckLastNDaysValue() {
		return Integer.valueOf(env.getProperty("dine.post.order.offer.check.last.n.days.value", "90"));
	}

    public Integer getDeliveryPostOrderOfferCheckLastNDaysValue() {
        return Integer.valueOf(env.getProperty("delivery.post.order.offer.check.last.n.days.value", "90"));
    }

	public String getChaayosBaseUrl() {
		return env.getProperty("chaayos.base.url", "https://cafes.chaayos.com");
	}

    public Boolean getIsShowNpsRating(){ return  Boolean.valueOf(env.getProperty("is.show.nps.rating"));}

    public Boolean getIsShowOrderFeedbackRating(){ return  Boolean.valueOf(env.getProperty("is.show.order.feedback.rating"));}

    public String getOrderFeedbackQuestion(){ return env.getProperty("order.feedback.question");}

    public String getNpsQuestion(){ return env.getProperty("nps.question");}

    public String getOrderReceipt() {
        return env.getProperty("order.receipt.cloudfront");
    }

    public boolean getFacebookPushEnabled() {
        return Boolean.valueOf(env.getProperty("facebook.push.enable", "false"));
    }

    public String getAccessTokenForFacebookPush() {
        return env.getProperty("facebook.accessToken", "");
    }

    public String getEventSetIdForChaayosDineIn() {
        return env.getProperty("facebook.push.chaayos.dinein.eventSetId");
    }

    public String getEventSetIdForChaayosDelivery() {
        return env.getProperty("facebook.push.chaayos.delivery.eventSetId");
    }

    public String getEventSetIdForBrandGNT() {
        return env.getProperty("facebook.push.gnt.eventSetId");
    }

    public Integer getSubscriptionValidBuyingDay() {
        return Integer.valueOf(env.getProperty("subscription.valid.buy.n.days.value", "0"));
    }

    public int getOfferMultiplierFactor() {
        return Integer.valueOf(env.getProperty("offer.multiplier.factor", "1"));
    }

    public String getOTPEmailTemplatePath() {
        return env.getProperty("otp.email.template", "template/OTPEmailTemplate.html");
    }

    public boolean getPrintDeliveryTempFlag(){ return Boolean.valueOf(env.getProperty("print.delivery.temp","false")); }

    public String getMembershipBuyLinkBasePath(){ return env.getProperty("membership.buy.link.base.path","https://cafes.chaayos.com/membership");}

    public boolean getisDineWhatsappNotificationFlag(){ return Boolean.valueOf(env.getProperty("dinein.order.notification.whatsapp","false")); }

    public boolean getSystemGeneratedNotifications(){ return Boolean.valueOf(env.getProperty("system.generated.notifications","false")); }

    public String getFacebookLeadsVerificationToken() {
        return env.getProperty("facebook.leads.verification.token", "");
    }

    public boolean getGooglePushEnabledForOfflineConversion() {
        return Boolean.valueOf(env.getProperty("google.push.enable.offlineconversion", "false"));
    }

    public boolean getGooglePushEnabledForStoreSalesConversion() {
        return Boolean.valueOf(env.getProperty("google.push.enable.storesales", "false"));
    }

    public String getRefreshTokenForGoogleAdsPush() {
        return env.getProperty("api.googleads.refreshToken");
    }

    public String getClientSecretForGoogleAdsPush() {
        return env.getProperty("api.googleads.clientSecret");
    }

    public String getClientIdForGoogleAdsPush() {
        return env.getProperty("api.googleads.clientId");
    }

    public String getDeveloperTokenForGoogleAdsPush() {
        return env.getProperty("api.googleads.developerToken");
    }

    public Long getLoginCustomerIdForGoogleAdsPush() {
        return Long.parseLong(env.getProperty("api.googleads.loginCustomerId"));
    }

    public String getKnockBaseUrl(){
        return env.getProperty("knock.base.url");
    }

    public String getKnockMasterToken(){
        return env.getProperty("knock.master.token");
    }

    public boolean getIsSendSmsForCampaignBySystem(){ return Boolean.valueOf(env.getProperty("send.campaign.sms.by.system", "false"));}
    public boolean isCafeOperationDisable(){ return Boolean.valueOf(env.getProperty("disable.cafe.operations", "false"));}

    public boolean getSendFeedbackMessageForCODOrders() {
        return Boolean.parseBoolean(env.getProperty("send.cod.order.feedback.message", "false"));
    }

    public String getS3BucketForDrools(){
        return env.getProperty("offer.drools.s3.bucket","com.chaayos.drool.dev");
    }

	public List<String> getClevertapLeadSources() {
		return Arrays.asList(env.getProperty("clevertap.lead.sources", AppConstants.CUSTOMER_LEAD_SOURCES).split(","));
	}

	 public boolean sendFeedBackSMSFromClevertap(){
        return  Boolean.valueOf(env.getProperty("send.feedback.sms.from.clevertap","false"));
    }

    public String getChaayosCashbackCouponCode(){
        return env.getProperty("chaayos.cashback.coupon.code","CHAAYOS_CASH");
    }

    public String getCashBackCouponCodePrefix(){
        return env.getProperty("cashback.coupon.code.prefix","CCFLAT");
    }

    public String getCashBackCouponCodeForNewCustomer(){
        return env.getProperty("cashback.coupon.code.new.customer","");
    }

    public int getValidityForCashBackCoupon(){
        return Integer.valueOf(env.getProperty("cashback.coupon.code.validity","45"));
    }

    public String getCashBackCouponCodeForNewCustomerPrefix(){
        return env.getProperty("cashback.coupon.code.new.customer.prefix","CCNEW");
    }

    public String getDeliveryOrderCancellationTime(){
        return env.getProperty("delivery.order.cancellation.time","7200");
    }

    public String getDineInOrderCancellationTime(){
        return env.getProperty("delivery.order.cancellation.time","2700");
    }

    public String getFamePilotUrlForFeedbackEvent(){
        return env.getProperty("famePilot.feedback.event.url","https://api.famepilot.com/reviews/partner-feedback/");
    }

    public String getFamePilotBussinessUUID(){
        return env.getProperty("business_uuid","558ddc60-2151-4f5e-8184-2c9910996656");
    }

    public List<String> getValidUnitList(){
        List<String> list = Arrays.asList(env.getProperty("valid.city.list.cashback","").split(","));
        return list;
    }

    public boolean getLoyaltyFlagForCashBack(){
        return Boolean.valueOf(env.getProperty("loyalty.flag.for.cashback", "true"));
    }

    public String getChannelPartnerBaseUrl(){
        return env.getProperty("partner.base.url","http://stage.kettle.chaayos.com:8080/channel-partner/rest/");
    }

    public String getPartnerToken(){
        return env.getProperty("partner.auth.token","eyJhbGciOiJIUzI1NiJ9.eyJwYXJ0bmVyTmFtZSI6Im5lby1yZWRpcy1jbGllbnQiLCJlbnZUeXBlIjoiUFJPRCIsInBhc3NDb2RlIjoiOTIwMjYiLCJpYXQiOjE0ODE4MDY1Njh9.yIFAX0uqgY7MO5LaFbwVS6703F9wtauMonOeeI_z4Bw");
    }

    public Boolean isSugarVariantToUpdate(){
        return Boolean.valueOf(env.getProperty("to.update.sugar.variant","true"));
    }

    public String getSweetnerProductIds(){
        return env.getProperty("sweetner.product.ids" , "1000206,1000205,1000204,1000207");
    }

    public List<Integer> getBirthdayMonthOfferIds(){
        String ids = env.getProperty("birthday.month.offer.ids","");
        if(StringUtils.isEmpty(ids))
        {
            return AppConstants.BIRTHDAY_MONTH_OFFER_IDS;
        }
        return Arrays.asList(ids.split(",")).stream()
                .map(s -> Integer.parseInt(s))
                .collect(Collectors.toList());

    }

    public List<Integer> getAnniversaryMonthOfferIds(){
        String ids = env.getProperty("anniversary.month.offer.ids","");
        if(StringUtils.isEmpty(ids))
        {
            return new ArrayList<>();
        }
        return Arrays.asList(ids.split(",")).stream()
                .map(s -> Integer.parseInt(s))
                .collect(Collectors.toList());

    }

    public List<Integer> getotherEventMonthOfferIds(){
        String ids = env.getProperty("other.month.offer.ids","");
        if(StringUtils.isEmpty(ids))
        {
            return new ArrayList<>();
        }
        return Arrays.asList(ids.split(",")).stream()
                .map(s -> Integer.parseInt(s))
                .collect(Collectors.toList());

    }

    public  String dumpHostLink (){
     return   env.getProperty("dump.hostname");
    }

    public  String dumpUserName (){
        return   env.getProperty("dump.username");
    }

    public  String dumpPassword (){
        return   env.getProperty("dump.password");
    }
    public Boolean isRevalidationOrderActive(){
        return Boolean.valueOf(env.getProperty("revalidation.order.active"));
    }

    public List<Integer> revalidationOrderPartnerIds(){
        String ids = env.getProperty("revalidation.order.partner.ids","");
        if(StringUtils.isEmpty(ids))
        {
            return new ArrayList<>();
        }
        return Arrays.asList(ids.split(",")).stream()
                .map(s -> Integer.parseInt(s))
                .collect(Collectors.toList());
    }
    public String getVortexBaseUrl(){
        return env.getProperty("vortex.base.url");
    }

    public String getVortexMasterToken(){
        return env.getProperty("vortex.master.token");
    }


    public String getFamePilotTicketDataApiUsername(){
        return env.getProperty("fame.pilot.ticket.data.api.username");
    }

    public String getFamePilotTicketDataApiPassword(){
        return env.getProperty("fame.pilot.ticket.data.api.password");
    }

    public String getFamePilotTicketDataUrl(){
        return  env.getProperty("fame.pilot.ticket.data.url");
    }

    public String getOfferBannerUrl() {
        return env.getProperty("offer.banner.url");
    }

    public Boolean getShowOfferFlag() {
        return Boolean.valueOf(env.getProperty("show.offer.flag"));
    }

    public String getProfileCompletionCouponCode() {
        return env.getProperty("profile.completion.coupon.code","CPCO24");
    }
    public String getProfileCompletionCouponCodePrefix() {
        return env.getProperty("profile.completion.coupon.code.prefix","CPC");
    }


    public Boolean isPriortizationOfOrdersEnabled(){
        return Boolean.valueOf(env.getProperty("is.priortization.mode.enabled","true"));
    }

    public Integer getDaysAfterLoyaltyExpire(){
        return Integer.valueOf(env.getProperty("days.after.loyalty.expire","365"));
    }

    public String getDateToCheckNewCustomer(){
        return (env.getProperty("date.to.check.new.customer","2024-10-17"));
    }


    public Boolean isAssemblyFirestoreEnabledForAll() {
        return Boolean.valueOf(env.getProperty("assembly.firestore.enabled.all", "false"));
    }
    public String getAssemblyFirestoreUnits() {
        return env.getProperty("units.for.order.delivery.through.fire.store", "");
    }

    public Integer dayCloseExcludeCompany() {
        return Integer.valueOf(env.getProperty("day.close.exclude.company.id", "1005"));
    }

    public Integer getMinPercentageAllowForUnsatisfiedOrder(){
        return Integer.valueOf(env.getProperty("percentage.value.unsatisfied.order", "15"));
    }

    public Set<Integer> getSpecialMilkVariantConsumptionExcludeProudctIds(){
        String ids = env.getProperty("special.milk.variant.consumption.exclude.product.ids","102316,101381");
        try{
            if(StringUtils.isEmpty(ids)) {
                return new HashSet<>();
            }
            return Arrays.stream(ids.split(",")).map(String::trim)
                    .map(Integer::parseInt)
                    .collect(Collectors.toSet());
        }catch (Exception e){
            LOG.error("Error while Fetching special milk variant consumption exclude product ids :: ");
            return new HashSet<>();
        }
    }

    public Boolean allowDohfulCash(){
        return Boolean.valueOf(env.getProperty("allow.dohful.cash","false"));
    }

    public Boolean enableMinCountApplicableProductsFilter(){
        return Boolean.valueOf(env.getProperty("enable.min.count.applicable.products.filter","true"));
    }

    public String dreamFolksGetVendorTransactionEndpoint() {
        return env.getProperty("dreamFolks.getVendorTransaction.endpoint", "");
    }

    public String dreamFolksApiKey() {
        return env.getProperty("dreamFolks.apikey", "");
    }

    public String dreamFolksCheckSumKey() {
        return env.getProperty("dreamFolks.checkSumKey", "");
    }

    public String getPartnerPaymentOfferCode(){
        return env.getProperty("partner.payment.offerCode", "");
    }

    public Boolean getKettlePaytmRefundCronStatus() {
        return Boolean.valueOf(env.getProperty("kettle.paytm.sync.refund.cron.enabled","true"));
    }

}
