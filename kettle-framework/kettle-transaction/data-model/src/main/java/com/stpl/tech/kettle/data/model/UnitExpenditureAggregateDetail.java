package com.stpl.tech.kettle.data.model;

import com.stpl.tech.kettle.core.data.vo.PnLRepresentation;
import com.stpl.tech.kettle.core.data.vo.PnLRepresentation.PnLRepresentationBuilder;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.util.AppUtils;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.math.BigDecimal;
import java.time.YearMonth;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "UNIT_EXPENDITURE_AGGREGATE_DETAIL")
public class UnitExpenditureAggregateDetail {
    @Id
    private int detailId;
    private int unitId;
    private String unitName;
    private Integer unitExpenditureDetailId;
    private String status;
    private int year;
    private int month;
    private int day;
    private Date businessDate;
    private String calculation;
    private Integer sumoClosureId;
    private Integer totalTickets;
    private BigDecimal totalSales;
    private BigDecimal totalApc;
    private BigDecimal totalGmv;
    private BigDecimal totalDiscount;
    private BigDecimal totalDiscountLoyalTea;
    private BigDecimal totalDiscountMarketing;
    private BigDecimal totalDiscountOps;
    private BigDecimal totalDiscountBd;
    private BigDecimal totalDiscountEmployeeFico;
    private BigDecimal dineInSales;
    private BigDecimal dineInApc;
    private BigDecimal dineInDiscount;
    private BigDecimal deliverySales;
    private BigDecimal deliveryApc;
    private BigDecimal deliveryDiscount;
    private BigDecimal directVariableCogs;
    private BigDecimal cogs;
    private BigDecimal wastageExpiry;
    private BigDecimal stockVariance;
    private BigDecimal directVariableOthers;
    private BigDecimal inDirectVariableCost;
    private BigDecimal directFixedCost;
    private BigDecimal employeeMeal;
    private BigDecimal manpowerFixed;
    private BigDecimal manpowerIncentive;
    private BigDecimal facilitiesProperty;
    private BigDecimal employeeMealSales;
    private BigDecimal inDirectFixedCost;
    private BigDecimal consumable;
    private BigDecimal facilitiesFixed;
    private BigDecimal supportCommWh;
    private BigDecimal supportOpsManagement;
    private BigDecimal logistics;
    private BigDecimal facilitiesVariable;
    private BigDecimal commissionCardsWallets;
    private BigDecimal commissionCp;
    private BigDecimal deliveryCharges;
    private BigDecimal anyOtherVariable;
    private BigDecimal maintenance;
    private BigDecimal marketingLs;
    private BigDecimal growthPnl;
    private BigDecimal allocation;
    private BigDecimal marketingCorp;
    private BigDecimal supportHq;
    private BigDecimal ficoPayouts;
    private BigDecimal technology;
    private BigDecimal growthCapex;
    private BigDecimal capexFixedAssets;
    private BigDecimal indirectIncome;
    private BigDecimal amortisationDepreciation;
    private BigDecimal growthSd;
    private BigDecimal employeeDiscount;
    private BigDecimal empMealApc;
    private BigDecimal grossProfit;
    private BigDecimal grossProfitPercentage;
    private BigDecimal contributionPercentage;
    private BigDecimal contribution;
    private BigDecimal operatingProfit;
    private BigDecimal operatingProfitPercentage;
    private BigDecimal netProfit;
    private BigDecimal netProfitPercentage;
    private BigDecimal cashBurn;
    private BigDecimal revenue;

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "UNIT_AGGREGATE_DETAIL_ID", unique = true, nullable = false)
    public int getDetailId() {
        return detailId;
    }

    public void setDetailId(int detailId) {
        this.detailId = detailId;
    }

    @Column(name = "UNIT_ID", nullable = true)
    public int getUnitId() {
        return unitId;
    }

    public void setUnitId(int unitId) {
        this.unitId = unitId;
    }

    @Column(name = "UNIT_NAME", nullable = true)
    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    @Column(name = "UNIT_EXPENDITURE_ID", nullable = false)
    public Integer getUnitExpenditureDetailId() {
        return unitExpenditureDetailId;
    }

    public void setUnitExpenditureDetailId(Integer unitExpenditureDetailId) {
        this.unitExpenditureDetailId = unitExpenditureDetailId;
    }

    @Column(name = "AGGREGATE_STATUS", nullable = true)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "YEAR", nullable = true)
    public int getYear() {
        return year;
    }

    public void setYear(int year) {
        this.year = year;
    }

    @Column(name = "MONTH", nullable = true)
    public int getMonth() {
        return month;
    }

    public void setMonth(int month) {
        this.month = month;
    }

    @Column(name = "DAY", nullable = true)
    public int getDay() {
        return day;
    }

    public void setDay(int day) {
        this.day = day;
    }

    @Column(name = "CALCULATION_TYPE", nullable = true)
    public String getCalculation() {
        return calculation;
    }

    public void setCalculation(String calculation) {
        this.calculation = calculation;
    }

    @Column(name = "BUSINESS_DATE", nullable = true)
    @Temporal(TemporalType.DATE)
    public Date getBusinessDate() {
        return businessDate;
    }

    public void setBusinessDate(Date businessDate) {
        this.businessDate = businessDate;
    }

    @Column(name = "SUMO_DAY_CLOSE_EVENT_ID", nullable = true)
    public Integer getSumoClosureId() {
        return sumoClosureId;
    }

    public void setSumoClosureId(Integer sumoClosureId) {
        this.sumoClosureId = sumoClosureId;
    }

    @Column(name = "TOTAL_TICKETS", nullable = true)
    public Integer getTotalTickets() {
        return totalTickets;
    }

    public void setTotalTickets(Integer totalTickets) {
        this.totalTickets = totalTickets;
    }

    @Column(name = "TOTAL_SALES", nullable = true)
    public BigDecimal getTotalSales() {
        return totalSales;
    }

    public void setTotalSales(BigDecimal totalSales) {
        this.totalSales = totalSales;
    }

    @Column(name = "TOTAL_APC", nullable = true)
    public BigDecimal getTotalApc() {
        return totalApc;
    }

    public void setTotalApc(BigDecimal totalApc) {
        this.totalApc = totalApc;
    }

    @Column(name = "TOTAL_GMV", nullable = true)
    public BigDecimal getTotalGmv() {
        return totalGmv;
    }

    public void setTotalGmv(BigDecimal totalGmv) {
        this.totalGmv = totalGmv;
    }

    @Column(name = "TOTAL_DISCOUNT", nullable = true)
    public BigDecimal getTotalDiscount() {
        return totalDiscount;
    }

    public void setTotalDiscount(BigDecimal totalDiscount) {
        this.totalDiscount = totalDiscount;
    }

    @Column(name = "TOTAL_DISCOUNT_LOYAL_TEA", nullable = true)
    public BigDecimal getTotalDiscountLoyalTea() {
        return totalDiscountLoyalTea;
    }

    public void setTotalDiscountLoyalTea(BigDecimal totalDiscountLoyalTea) {
        this.totalDiscountLoyalTea = totalDiscountLoyalTea;
    }

    @Column(name = "TOTAL_DISCOUNT_MARKETING", nullable = true)
    public BigDecimal getTotalDiscountMarketing() {
        return totalDiscountMarketing;
    }

    public void setTotalDiscountMarketing(BigDecimal totalDiscountMarketing) {
        this.totalDiscountMarketing = totalDiscountMarketing;
    }

    @Column(name = "TOTAL_DISCOUNT_OPS", nullable = true)
    public BigDecimal getTotalDiscountOps() {
        return totalDiscountOps;
    }

    public void setTotalDiscountOps(BigDecimal totalDiscountOps) {
        this.totalDiscountOps = totalDiscountOps;
    }

    @Column(name = "TOTAL_DISCOUNT_BD", nullable = true)
    public BigDecimal getTotalDiscountBd() {
        return totalDiscountBd;
    }

    public void setTotalDiscountBd(BigDecimal totalDiscountBd) {
        this.totalDiscountBd = totalDiscountBd;
    }

    @Column(name = "TOTAL_DISCOUNT_EMPLOYEE_FICO", nullable = true)
    public BigDecimal getTotalDiscountEmployeeFico() {
        return totalDiscountEmployeeFico;
    }

    public void setTotalDiscountEmployeeFico(BigDecimal totalDiscountEmployeeFico) {
        this.totalDiscountEmployeeFico = totalDiscountEmployeeFico;
    }

    @Column(name = "DINE_IN_SALES", nullable = true)
    public BigDecimal getDineInSales() {
        return dineInSales;
    }

    public void setDineInSales(BigDecimal dineInSales) {
        this.dineInSales = dineInSales;
    }

    @Column(name = "DINE_IN_APC", nullable = true)
    public BigDecimal getDineInApc() {
        return dineInApc;
    }

    public void setDineInApc(BigDecimal dineInApc) {
        this.dineInApc = dineInApc;
    }

    @Column(name = "DINE_IN_DISCOUNT", nullable = true)
    public BigDecimal getDineInDiscount() {
        return dineInDiscount;
    }

    public void setDineInDiscount(BigDecimal dineInDiscount) {
        this.dineInDiscount = dineInDiscount;
    }

    @Column(name = "DELIVERY_SALES", nullable = true)
    public BigDecimal getDeliverySales() {
        return deliverySales;
    }

    public void setDeliverySales(BigDecimal deliverySales) {
        this.deliverySales = deliverySales;
    }

    @Column(name = "DELIVERY_APC", nullable = true)
    public BigDecimal getDeliveryApc() {
        return deliveryApc;
    }

    public void setDeliveryApc(BigDecimal deliveryApc) {
        this.deliveryApc = deliveryApc;
    }

    @Column(name = "DELIVERY_DISCOUNT", nullable = true)
    public BigDecimal getDeliveryDiscount() {
        return deliveryDiscount;
    }

    public void setDeliveryDiscount(BigDecimal deliveryDiscount) {
        this.deliveryDiscount = deliveryDiscount;
    }

    @Column(name = "DIRECT_VARIABLE_COGS", nullable = true)
    public BigDecimal getDirectVariableCogs() {
        return directVariableCogs;
    }

    public void setDirectVariableCogs(BigDecimal directVariableCogs) {
        this.directVariableCogs = directVariableCogs;
    }

    @Column(name = "COGS", nullable = true)
    public BigDecimal getCogs() {
        return cogs;
    }

    public void setCogs(BigDecimal cogs) {
        this.cogs = cogs;
    }

    @Column(name = "WASTAGE_EXPIRY", nullable = true)
    public BigDecimal getWastageExpiry() {
        return wastageExpiry;
    }

    public void setWastageExpiry(BigDecimal wastageExpiry) {
        this.wastageExpiry = wastageExpiry;
    }

    @Column(name = "STOCK_VARIANCE", nullable = true)
    public BigDecimal getStockVariance() {
        return stockVariance;
    }

    public void setStockVariance(BigDecimal stockVariance) {
        this.stockVariance = stockVariance;
    }

    @Column(name = "DIRECT_VARIABLE_OTHERS", nullable = true)
    public BigDecimal getDirectVariableOthers() {
        return directVariableOthers;
    }

    public void setDirectVariableOthers(BigDecimal directVariableOthers) {
        this.directVariableOthers = directVariableOthers;
    }

    @Column(name = "INDIRECT_VARIABLE_OTHERS", nullable = true)
    public BigDecimal getInDirectVariableCost() {
        return inDirectVariableCost;
    }

    public void setInDirectVariableCost(BigDecimal inDirectVariableOthers) {
        this.inDirectVariableCost = inDirectVariableOthers;
    }

    @Column(name = "DIRECT_FIXED_COST", nullable = true)
    public BigDecimal getDirectFixedCost() {
        return directFixedCost;
    }

    public void setDirectFixedCost(BigDecimal directFixedCost) {
        this.directFixedCost = directFixedCost;
    }

    @Column(name = "EMPLOYEE_MEAL", nullable = true)
    public BigDecimal getEmployeeMeal() {
        return employeeMeal;
    }

    public void setEmployeeMeal(BigDecimal employeeMeal) {
        this.employeeMeal = employeeMeal;
    }

    @Column(name = "MANPOWER_FIXED", nullable = true)
    public BigDecimal getManpowerFixed() {
        return manpowerFixed;
    }

    public void setManpowerFixed(BigDecimal manpowerFixed) {
        this.manpowerFixed = manpowerFixed;
    }

    @Column(name = "MANPOWER_INCENTIVE", nullable = true)
    public BigDecimal getManpowerIncentive() {
        return manpowerIncentive;
    }

    public void setManpowerIncentive(BigDecimal manpowerIncentive) {
        this.manpowerIncentive = manpowerIncentive;
    }

    @Column(name = "FACILITIES_PROPERTY", nullable = true)
    public BigDecimal getFacilitiesProperty() {
        return facilitiesProperty;
    }

    public void setFacilitiesProperty(BigDecimal facilitiesProperty) {
        this.facilitiesProperty = facilitiesProperty;
    }

    @Column(name = "EMPLOYEE_MEAL_SALES", nullable = true)
    public BigDecimal getEmployeeMealSales() {
        return employeeMealSales;
    }

    public void setEmployeeMealSales(BigDecimal employeeMealSales) {
        this.employeeMealSales = employeeMealSales;
    }

    @Column(name = "INDIRECT_FIXED_COST", nullable = true)
    public BigDecimal getInDirectFixedCost() {
        return inDirectFixedCost;
    }

    public void setInDirectFixedCost(BigDecimal inDirectFixedCost) {
        this.inDirectFixedCost = inDirectFixedCost;
    }

    @Column(name = "CONSUMABLE", nullable = true)
    public BigDecimal getConsumable() {
        return consumable;
    }

    public void setConsumable(BigDecimal consumable) {
        this.consumable = consumable;
    }

    @Column(name = "FACILITIES_FIXED", nullable = true)
    public BigDecimal getFacilitiesFixed() {
        return facilitiesFixed;
    }

    public void setFacilitiesFixed(BigDecimal facilitiesFixed) {
        this.facilitiesFixed = facilitiesFixed;
    }

    @Column(name = "SUPPORT_COMM_WH", nullable = true)
    public BigDecimal getSupportCommWh() {
        return supportCommWh;
    }

    public void setSupportCommWh(BigDecimal supportCommWh) {
        this.supportCommWh = supportCommWh;
    }

    @Column(name = "SUPPORT_OPS_MANAGEMENT", nullable = true)
    public BigDecimal getSupportOpsManagement() {
        return supportOpsManagement;
    }

    public void setSupportOpsManagement(BigDecimal supportOpsManagement) {
        this.supportOpsManagement = supportOpsManagement;
    }

    @Column(name = "LOGISTICS", nullable = true)
    public BigDecimal getLogistics() {
        return logistics;
    }

    public void setLogistics(BigDecimal logistics) {
        this.logistics = logistics;
    }

    @Column(name = "FACILITIES_VARIABLE", nullable = true)
    public BigDecimal getFacilitiesVariable() {
        return facilitiesVariable;
    }

    public void setFacilitiesVariable(BigDecimal facilitiesVariable) {
        this.facilitiesVariable = facilitiesVariable;
    }

    @Column(name = "COMMISSION_CARD_WALLETS", nullable = true)
    public BigDecimal getCommissionCardsWallets() {
        return commissionCardsWallets;
    }

    public void setCommissionCardsWallets(BigDecimal commissionCardsWallets) {
        this.commissionCardsWallets = commissionCardsWallets;
    }

    @Column(name = "COMMISSION_CP", nullable = true)
    public BigDecimal getCommissionCp() {
        return commissionCp;
    }

    public void setCommissionCp(BigDecimal commissionCp) {
        this.commissionCp = commissionCp;
    }

    @Column(name = "DELIVERY_CHARGES", nullable = true)
    public BigDecimal getDeliveryCharges() {
        return deliveryCharges;
    }

    public void setDeliveryCharges(BigDecimal deliveryCharges) {
        this.deliveryCharges = deliveryCharges;
    }

    @Column(name = "ANY_OTHER_VARIABLE", nullable = true)
    public BigDecimal getAnyOtherVariable() {
        return anyOtherVariable;
    }

    public void setAnyOtherVariable(BigDecimal anyOtherVariable) {
        this.anyOtherVariable = anyOtherVariable;
    }

    @Column(name = "MAINTENANCE", nullable = true)
    public BigDecimal getMaintenance() {
        return maintenance;
    }

    public void setMaintenance(BigDecimal maintenance) {
        this.maintenance = maintenance;
    }

    @Column(name = "MARKETING_LS", nullable = true)
    public BigDecimal getMarketingLs() {
        return marketingLs;
    }

    public void setMarketingLs(BigDecimal marketingLs) {
        this.marketingLs = marketingLs;
    }

    @Column(name = "GROWTH_PNL", nullable = true)
    public BigDecimal getGrowthPnl() {
        return growthPnl;
    }

    public void setGrowthPnl(BigDecimal growthPnl) {
        this.growthPnl = growthPnl;
    }

    @Column(name = "ALLOCATION", nullable = true)
    public BigDecimal getAllocation() {
        return allocation;
    }

    public void setAllocation(BigDecimal allocation) {
        this.allocation = allocation;
    }

    @Column(name = "MARKETING_CORP", nullable = true)
    public BigDecimal getMarketingCorp() {
        return marketingCorp;
    }

    public void setMarketingCorp(BigDecimal marketingCorp) {
        this.marketingCorp = marketingCorp;
    }

    @Column(name = "SUPPORT_HQ", nullable = true)
    public BigDecimal getSupportHq() {
        return supportHq;
    }

    public void setSupportHq(BigDecimal supportHq) {
        this.supportHq = supportHq;
    }

    @Column(name = "FICO_PAYOUTS", nullable = true)
    public BigDecimal getFicoPayouts() {
        return ficoPayouts;
    }

    public void setFicoPayouts(BigDecimal ficoPayouts) {
        this.ficoPayouts = ficoPayouts;
    }

    @Column(name = "TECHNOLOGY", nullable = true)
    public BigDecimal getTechnology() {
        return technology;
    }

    public void setTechnology(BigDecimal technology) {
        this.technology = technology;
    }

    @Column(name = "GROWTH_CAPEX", nullable = true)
    public BigDecimal getGrowthCapex() {
        return growthCapex;
    }

    public void setGrowthCapex(BigDecimal growthCapex) {
        this.growthCapex = growthCapex;
    }

    @Column(name = "CAPEX_FIXED_ASSETS", nullable = true)
    public BigDecimal getCapexFixedAssets() {
        return capexFixedAssets;
    }

    public void setCapexFixedAssets(BigDecimal capexFixedAssets) {
        this.capexFixedAssets = capexFixedAssets;
    }

    @Column(name = "INDIRECT_INCOME", nullable = true)
    public BigDecimal getIndirectIncome() {
        return indirectIncome;
    }

    public void setIndirectIncome(BigDecimal indirectIncome) {
        this.indirectIncome = indirectIncome;
    }

    @Column(name = "AMORTISATION_DEPRECIATION", nullable = true)
    public BigDecimal getAmortisationDepreciation() {
        return amortisationDepreciation;
    }

    public void setAmortisationDepreciation(BigDecimal amortisationDepreciation) {
        this.amortisationDepreciation = amortisationDepreciation;
    }

    @Column(name = "GROWTH_SD", nullable = true)
    public BigDecimal getGrowthSd() {
        return growthSd;
    }

    public void setGrowthSd(BigDecimal growthSd) {
        this.growthSd = growthSd;
    }

    @Column(name = "EMPLOYEE_DISCOUNT", nullable = true)
    public BigDecimal getEmployeeDiscount() {
        return employeeDiscount;
    }

    public void setEmployeeDiscount(BigDecimal employeeDiscount) {
        this.employeeDiscount = employeeDiscount;
    }

    @Column(name = "EMP_MEAL_APC", nullable = true)
    public BigDecimal getEmpMealApc() {
        return empMealApc;
    }

    public void setEmpMealApc(BigDecimal empMealApc) {
        this.empMealApc = empMealApc;
    }

    @Column(name = "GROSS_PROFIT", nullable = true)
    public BigDecimal getGrossProfit() {
        return grossProfit;
    }

    public void setGrossProfit(BigDecimal grossProfit) {
        this.grossProfit = grossProfit;
    }

    @Column(name = "GROSS_PROFIT_PERCENTAGE", nullable = true)
    public BigDecimal getGrossProfitPercentage() {
        return grossProfitPercentage;
    }

    public void setGrossProfitPercentage(BigDecimal grossProfitPercentage) {
        this.grossProfitPercentage = grossProfitPercentage;
    }

    @Column(name = "CONTRIBUTION_PERCENTAGE", nullable = true)
    public BigDecimal getContributionPercentage() {
        return contributionPercentage;
    }

    public void setContributionPercentage(BigDecimal contributionPercentage) {
        this.contributionPercentage = contributionPercentage;
    }

    @Column(name = "CONTRIBUTION", nullable = true)
    public BigDecimal getContribution() {
        return contribution;
    }

    public void setContribution(BigDecimal contribution) {
        this.contribution = contribution;
    }

    @Column(name = "OPERATING_PROFIT", nullable = true)
    public BigDecimal getOperatingProfit() {
        return operatingProfit;
    }

    public void setOperatingProfit(BigDecimal operatingProfit) {
        this.operatingProfit = operatingProfit;
    }

    @Column(name = "OPERATING_PROFIT_PERCENTAGE", nullable = true)
    public BigDecimal getOperatingProfitPercentage() {
        return operatingProfitPercentage;
    }

    public void setOperatingProfitPercentage(BigDecimal operatingProfitPercentage) {
        this.operatingProfitPercentage = operatingProfitPercentage;
    }

    @Column(name = "NET_PROFIT", nullable = true)
    public BigDecimal getNetProfit() {
        return netProfit;
    }

    public void setNetProfit(BigDecimal netProfit) {
        this.netProfit = netProfit;
    }

    @Column(name = "NET_PROFIT_PERCENTAGE", nullable = true)
    public BigDecimal getNetProfitPercentage() {
        return netProfitPercentage;
    }

    public void setNetProfitPercentage(BigDecimal netProfitPercentage) {
        this.netProfitPercentage = netProfitPercentage;
    }

    @Column(name = "CASH_BURN", nullable = true)
    public BigDecimal getCashBurn() {
        return cashBurn;
    }

    public void setCashBurn(BigDecimal cashBurn) {
        this.cashBurn = cashBurn;
    }

    @Column(name = "REVENUE", nullable = true)
    public BigDecimal getRevenue() {
        return revenue;
    }

    public void setRevenue(BigDecimal revenue) {
        this.revenue = revenue;
    }

    private BigDecimal get(BigDecimal d) {
        return d == null ? BigDecimal.ZERO : d;
    }

    public Integer totalTickets(UnitExpenditureDetail detail) {
        Integer value = detail.getDineInTicket() + detail.getDeliveryTicket() + detail.getEmployeeMealTicket();
        return value;
    }

    public BigDecimal totalSales(UnitExpenditureDetail detail) {
        BigDecimal value = get(detail.getDineInSales()).add(get(detail.getDeliverySales())).add(get(employeeMealSales(detail)));
        return value;
    }

    public BigDecimal totalSales(UnitBudgetoryDetail detail) {
        BigDecimal value = get(detail.getDineInSales()).add(get(detail.getDeliverySales()));//.add(get(employeeMealSales(detail)));
        return value;
    }

    public BigDecimal totalApc(UnitExpenditureDetail detail) {
        BigDecimal value = AppUtils.divideWithScale((get(totalGmv(detail)).subtract(get(totalDiscount(detail)))), new BigDecimal((totalTickets(detail))), 4);
        return value;
    }

    public BigDecimal totalGmv(UnitExpenditureDetail detail) {
        BigDecimal value = get(detail.getDineInGmv()).add(get(detail.getDeliveryGmv())).add(get(detail.getEmployeeMealGmv()));
        return value;
    }

    public BigDecimal totalDiscount(UnitExpenditureDetail detail) {
        BigDecimal value = get(totalDiscountLoyalTea(detail)).add(get(totalDiscountMarketing(detail))).add(get(totalDiscountOps(detail)))
                .add(get(totalDiscountBd(detail))).add(get(totalDiscountEmployeeFico(detail)));
        return value;
    }

    public BigDecimal totalDiscountLoyalTea(UnitExpenditureDetail detail) {
        BigDecimal value = get(detail.getDineInDiscountLoyalty()).add(get(detail.getDeliveryDiscountLoyalty())).add(get(detail.getEmpDiscountLoyalty()));
        return value;
    }

    public BigDecimal totalDiscountMarketing(UnitExpenditureDetail detail) {
        BigDecimal value = get(detail.getDineInDiscountMarketing()).add(get(detail.getDeliveryDiscountMarketing())).add(get(detail.getEmpDiscountMarketing()));
        return value;
    }

    public BigDecimal totalDiscountBd(UnitExpenditureDetail detail) {
        BigDecimal value = get(detail.getDineInDiscountBd()).add(get(detail.getDeliveryDiscountBd())).add(get(detail.getEmpDiscountBd()));
        return value;
    }

    public BigDecimal totalDiscountOps(UnitExpenditureDetail detail) {
        BigDecimal value = get(detail.getDineInDiscountOps()).add(get(detail.getDeliveryDiscountOps())).add(get(detail.getEmpDiscountOps()));
        return value;
    }

    public BigDecimal totalDiscountEmployeeFico(UnitExpenditureDetail detail) {
        BigDecimal value = get(detail.getDineInDiscountEmployeeFico()).add(get(detail.getDeliveryDiscountEmployeeFico())).add(get(detail.getEmpDiscountEmployeeFico()));
        return value;
    }

    public BigDecimal dineInSale(UnitExpenditureDetail detail) {
        BigDecimal value = get(detail.getDineInGmv()).subtract(get(dineInDiscount(detail)));
        return value;
    }

    public BigDecimal dineInDiscount(UnitExpenditureDetail detail) {
        BigDecimal value = get(detail.getDineInDiscountLoyalty()).add(get(detail.getDineInDiscountMarketing()))
                .add(get(detail.getDineInDiscountBd())).add(get(detail.getDineInDiscountOps())).add(get(detail.getDineInDiscountEmployeeFico()));
        return value;
    }

    public BigDecimal dineInApc(UnitExpenditureDetail detail) {
        BigDecimal value = AppUtils.divideWithScale(get(dineInDiscount(detail)), (new BigDecimal(detail.getDineInTicket())), 4);
        return value;
    }

    public BigDecimal deliveryDiscount(UnitExpenditureDetail detail) {
        BigDecimal value = get(detail.getDeliveryDiscountLoyalty()).add(get(detail.getDeliveryDiscountMarketing()))
                .add(get(detail.getDeliveryDiscountBd())).add(get(detail.getDeliveryDiscountOps())).add(get(detail.getDeliveryDiscountEmployeeFico()));
        return value;
    }

    public BigDecimal deliverySales(UnitExpenditureDetail detail) {
        BigDecimal value = get(detail.getDeliveryGmv()).subtract(get(deliveryDiscount(detail)));
        return value;
    }

    public BigDecimal deliveryApc(UnitExpenditureDetail detail) {
        BigDecimal value = AppUtils.divideWithScale(get(deliverySales(detail)), (new BigDecimal(detail.getDeliveryTicket())), 4);
        return value;
    }

    public BigDecimal employeeMealSales(UnitExpenditureDetail detail) {
        BigDecimal value = get(detail.getEmployeeMealGmv()).subtract(employeeDiscount(detail));
        return value;
    }

//    public BigDecimal employeeMealSales(UnitBudgetoryDetail detail) {
//        BigDecimal value = get(detail.get()).subtract(employeeDiscount(detail));
//        return value;
//    }

    public BigDecimal directVariableCogs(UnitExpenditureDetail detail) {
        BigDecimal value = get(cogs(detail)).add(get(wastageExpiry(detail))).add(get(stockVariance(detail)));
        return value;
    }

    public BigDecimal directVariableCogs(UnitBudgetoryDetail detail) {
        BigDecimal value = get(cogs(detail)).add(get(wastageExpiry(detail))).add(get(stockVariance(detail)));
        return value;
    }

    public void calculateDirectVariableCogs() {
        this.directVariableCogs = get(this.cogs).add(get(this.wastageExpiry)).add(get(this.stockVariance));
    }

    public void calculateRevenue() {
        this.revenue = get(this.totalGmv).subtract(get(this.totalDiscount));
    }

    public void calculateGrossProfit() {
        this.grossProfit = get(this.totalSales).subtract(get(this.directVariableCogs));
    }

    public BigDecimal calculateGrossProfit(UnitBudgetoryDetail unitBudgetoryDetail) {
        return get(totalSales(unitBudgetoryDetail)).subtract(get(directVariableCogs(unitBudgetoryDetail)));
    }

    public void calculateGrossProfitPercentage() {
        this.grossProfitPercentage = AppUtils.percentage(this.grossProfit, this.totalSales);
    }

    public void calculateDirectVariableOthers() {
        this.directVariableOthers = get(this.facilitiesVariable).add(get(this.commissionCardsWallets)).add(get(this.commissionCp))
                .add(get(this.deliveryCharges));
    }

    public void calculateContribution() {
        this.contribution = get(this.grossProfit).subtract((get(this.directVariableOthers)).add(get(this.inDirectVariableCost)));
    }

    public BigDecimal calculateContribution(UnitBudgetoryDetail budget) {
        return get(calculateGrossProfit(budget)).subtract((get(directVariableOthers(budget))).add(get(inDirectVariableCost(budget))));
    }

    public void calculateContributionPercentage() {
        this.contributionPercentage = AppUtils.percentage(this.contribution, this.totalSales);
    }

    public BigDecimal cogs(UnitExpenditureDetail detail) {
        BigDecimal value = get(detail.getDineInCogs()).add(get(detail.getDeliveryCogs())).add(get(detail.getUnsatifiedCustomerCost()))
                .add(get(detail.getPPECost())).add(get(detail.getTrainingCogs())).add(get(detail.getDineInCogsTax()))
                .add(get(detail.getDeliveryCogsTax())).add(get(detail.getTrainingCogsTax())).add(get(detail.getUnsatifiedCustomerCostTax()))
                .add(get(detail.getPPECostTax())).add(get(detail.getVariancePCC())).add(get(detail.getVariancePCCTax()))
                .add((get(detail.getVarianceYC()))).add(get(detail.getVarianceYCTax())).add(get(detail.getCogsOthers()))
                .add(get(detail.getCogsLogistics())).add(get(detail.getConsumableDisposable())).add(get(detail.getConsumableDisposableTax()))
                .add(get(detail.getCogsTradingGoods()));
        return value;
    }

    public BigDecimal cogs(UnitBudgetoryDetail detail) {
        BigDecimal value = get(detail.getDineInCogs()).add(get(detail.getDeliveryCogs())).add(get(detail.getUnsatisfiedCustomerCost()))
                .add(get(detail.getPPECost())).add(get(detail.getTrainingCogs())).add(get(detail.getDineInCogsTax()))
                .add(get(detail.getDeliveryCogsTax())).add(get(detail.getTrainingCogsTax())).add(get(detail.getUnsatisfiedCustomerCostTax()))
                .add(get(detail.getPPECostTax())).add(get(detail.getVariancePCC())).add(get(detail.getVariancePccTax()))
                .add((get(detail.getVarianceYC()))).add(get(detail.getVarianceYcTax())).add(get(detail.getCogsOthers()))
                .add(get(detail.getCogsLogistics())).add(get(detail.getConsumableDisposable())).add(get(detail.getConsumableDisposableTax()));
        //.add(get(detail.getCogsTradingGoods()));
        return value;
    }

    public BigDecimal wastageExpiry(UnitExpenditureDetail detail) {
        BigDecimal value = get(detail.getExpiryWastage()).add(get(detail.getWastageOther()))
                .add(get(detail.getExpiryWastageTax())).add(get(detail.getWastageOtherTax()));
        return value;
    }

    public BigDecimal wastageExpiry(UnitBudgetoryDetail detail) {
        BigDecimal value = get(detail.getExpiryWastage()).add(get(detail.getWastageOther()))
                .add(get(detail.getExpiryWastageTax())).add(get(detail.getWastageOtherTax()));
        return value;
    }

    public BigDecimal stockVariance(UnitExpenditureDetail detail) {
        BigDecimal value = get(detail.getVarianceZero()).add(get(detail.getVarianceZeroTax()));
        return value;
    }

    public BigDecimal stockVariance(UnitBudgetoryDetail detail) {
        BigDecimal value = get(detail.getVarianceZero()).add(get(detail.getVarianceZeroTax()));
        return value;
    }

    public void calculateInDirectVariableCost() {
        this.inDirectVariableCost = get(this.anyOtherVariable).add(get(this.maintenance)).add(get(this.marketingLs));
    }

    public void calculateDirectFixedCost() {
        this.directFixedCost = get(this.manpowerFixed).add(get(this.manpowerIncentive)).add(get(this.facilitiesProperty)).add(get(this.employeeMeal));
    }

    public void calculateInDirectFixedCost() {
        this.inDirectFixedCost = get(this.consumable).add(get(this.facilitiesFixed)).add(get(this.supportCommWh)).add(get(this.supportOpsManagement))
                .add(get(this.logistics));
    }

    public void calculateOperationProfit() {
        this.operatingProfit = get(this.contribution).subtract(get(this.directFixedCost)).add(get(this.inDirectFixedCost));
    }

    public BigDecimal calculateOperationProfit(UnitBudgetoryDetail detail) {
        return get(calculateContribution(detail)).subtract(get(directFixedCost(detail))).add(get(inDirectFixedCost(detail)));
    }

    public void calculateOperationProfitPercentage() {
        this.operatingProfitPercentage = AppUtils.percentage(this.operatingProfit, this.totalSales);
    }

    public void calculateTotalAllocationCost() {
        this.allocation = get(this.supportHq).add(get(this.technology)).add(get(this.marketingCorp));
    }

    public BigDecimal calculateTotalAllocationCost(UnitBudgetoryDetail detail) {
        return get(supportHq(detail)).add(get(technology(detail))).add(get(marketingCorp(detail)));
    }

    public void calculateNetProfit() {
        this.netProfit = get(this.operatingProfit).subtract(get(this.allocation));
    }

    public BigDecimal calculateNetProfit(UnitBudgetoryDetail detail) {
        return get(calculateOperationProfit(detail)).subtract(get(calculateTotalAllocationCost(detail)));
    }

    public void calculateNetProfitPercentage() {
        this.netProfitPercentage = AppUtils.percentage(this.netProfit, this.totalSales);
    }

    public void calculateCashBurn() {
        this.cashBurn = get(this.netProfit).subtract(get(this.amortisationDepreciation).add(this.ficoPayouts).add(get(this.growthPnl)).add(get(this.growthCapex))
                .add(get(this.capexFixedAssets)).add(get(this.growthSd)));
    }

    public void calculateAggregates() {
        calculateDirectVariableCogs();
        calculateGrossProfit();
        calculateGrossProfitPercentage();
        calculateDirectVariableOthers();
        calculateInDirectVariableCost();
        calculateContribution();
        calculateContributionPercentage();
        calculateOperationProfit();
        calculateOperationProfitPercentage();
        calculateTotalAllocationCost();
        calculateNetProfit();
        calculateNetProfitPercentage();
        calculateRevenue();
        calculateCashBurn();
    }


    public BigDecimal directVariableOthers(UnitExpenditureDetail detail) {
        BigDecimal value = facilitiesVariable(detail).add(commissionCardWallets(detail)).add(commissionCp(detail)).add(deliveryCharges(detail));
        return value;
    }

    public BigDecimal directVariableOthers(UnitBudgetoryDetail detail) {
        BigDecimal value = facilitiesVariable(detail).add(commissionCardWallets(detail)).add(commissionCp(detail)).add(deliveryCharges(detail));
        return value;
    }

    public BigDecimal inDirectVariableCost(UnitExpenditureDetail detail) {
        BigDecimal value =
                get(detail.getSecurityGuardCharges()).add(get(detail.getCommissionChangeCafe())).add(get(detail.getCommissionChange())).add(get(detail.getStaffWelfareExpenses()))
                        .add(get(detail.getNewsPaper())).add(get(detail.getTravellingExpense())).add(get(detail.getStaffWelfareExpensesCafe())).add(get(detail.getCourierChargesCafe()))
                        .add(get(detail.getCourierCharges())).add(get(detail.getPrintingAndStationary())).add(get(detail.getPrintingAndStationaryCafe())).add(get(detail.getBusinessPromotion()))
                        .add(get(detail.getLegalCharges())).add(get(detail.getProfessionalCharges())).add(get(detail.getConveyanceMarketing())).add(get(detail.getBusinessPromotionCafe()))
                        .add(get(detail.getConveyanceOperations())).add(get(detail.getConveyanceOthers())).add(get(detail.getAuditFee()))
                        .add(get(detail.getAuditFeeOutOfPocket())).add(get(detail.getCharityAndDonations())).add(get(detail.getHouseKeepingCharges())).add(get(detail.getLateFeeCharges()))
                        .add(get(detail.getMarketingDataAnalysis())).add(get(detail.getMiscellaneousExpenses())).add(get(detail.getPenalty()))
                        .add(get(detail.getPhotoCopyExpensesCafe())).add(get(detail.getPhotoCopyExpenses())).add(get(detail.getQcrExpense())).add(get(detail.getRecuritmentConsultants()))
                        .add(get(detail.getRocFees())).add(get(detail.getConveyanceOdc())).add(get(detail.getDebitCreditWrittenOff()))
                        .add(get(detail.getDifferenceInExchange())).add(get(detail.getRoundedOff()))
                        .add(get(detail.getBuildingMaintenance())).add(get(detail.getComputerMaintenance())).add(get(detail.getCleaningChargesCafe()))
                        .add(get(detail.getCleaningCharges())).add(get(detail.getPestControlCharges())).add(get(detail.getEquipmentMaintenance()))
                        .add(get(detail.getProntoAMC())).add(get(detail.getOthersAMC())).add(get(detail.getOthersMaintenance()))
                        .add(get(detail.getRnDEngineeringExpenses())).add(get(detail.getRoAMC())).add(get(detail.getBuildingMaintenanceHq())).add(get(detail.getEquipmentMaintenanceHq()))
                        .add(get(detail.getComputerItMaintenanceHq())).add(get(detail.getAirConditionerAmc())).add(get(detail.getMarketingAndSampling()))
                        .add(get(detail.getConsumableMarketing())).add(get(detail.getMarketingAndSamplingTax()))
                        .add(get(detail.getConsumableMarketingTax())).add(get(detail.getMarketingNPI())).add(get(detail.getNewsPaperCafe()))
                        .add(get(detail.getMarketingNpiHq())).add(get(detail.getConsumableLhi())).add(get(detail.getConsumableLhiTax()))
                        .add(get(detail.getConsumableMaintenance())).add(get(detail.getConsumableMaintenanceTax()))
                        .add(get(detail.getMaintenancePestControlCafe())).add(get(detail.getDomesticTicketsAndHotels())).add(get(detail.getInternationalTicketsAndHotels()))
                        .add(get(detail.getShippingCharges())).add(get(detail.getInsuranceMarine())).add(get(detail.getShareStampingCharges())).add(get(detail.getMarketingDiscountEcom()))
                        .add(get(detail.getPerformanceMarketingService())).add(get(detail.getGiftCardOffer()));
        //.add(get(detail.getLossOnDisposalFixedAssets())
//        .add(get(detail.getShortAndExcess()))
        return value;
    }

    public BigDecimal inDirectVariableCost(UnitBudgetoryDetail detail) {
        BigDecimal value =
                get(detail.getSecurityGuardCharges()).add(get(detail.getCommissionChangeCafe())).add(get(detail.getCommissionChange())).add(get(detail.getStaffWelfareExpenses()))
                        .add(get(detail.getNewsPaper())).add(get(detail.getTravellingExpense())).add(get(detail.getStaffWelfareExpensesCafe())).add(get(detail.getCourierChargesCafe()))
                        .add(get(detail.getCourierCharges())).add(get(detail.getPrintingAndStationary())).add(get(detail.getPrintingAndStationaryCafe())).add(get(detail.getBusinessPromotion()))
                        .add(get(detail.getLegalCharges())).add(get(detail.getProfessionalCharges())).add(get(detail.getConveyanceMarketing())).add(get(detail.getBusinessPromotionCafe()))
                        .add(get(detail.getConveyanceOperations())).add(get(detail.getConveyanceOthers())).add(get(detail.getAuditFee()))
                        .add(get(detail.getAuditFeeOutOfPocket())).add(get(detail.getCharityAndDonations())).add(get(detail.getHouseKeepingCharges())).add(get(detail.getLateFeeCharges()))
                        .add(get(detail.getMarketingDataAnalysis())).add(get(detail.getMiscellaneousExpenses())).add(get(detail.getPenalty()))
                        .add(get(detail.getPhotoCopyExpensesCafe())).add(get(detail.getPhotoCopyExpenses())).add(get(detail.getQcrExpense())).add(get(detail.getRecuritmentConsultants()))
                        .add(get(detail.getRocFees())).add(get(detail.getDebitCreditWrittenOff()))
                        .add(get(detail.getDifferenceInExchange())).add(get(detail.getRoundedOff()))
                        .add(get(detail.getBuildingMaintenance())).add(get(detail.getComputerMaintenance())).add(get(detail.getCleaningChargesCafe()))
                        .add(get(detail.getCleaningCharges())).add(get(detail.getPestControlCharges())).add(get(detail.getEquipmentMaintenance()))
                        .add(get(detail.getProntoAMC())).add(get(detail.getOthersAMC())).add(get(detail.getOthersMaintenance()))
                        .add(get(detail.getRnDEngineeringExpenses())).add(get(detail.getRoAMC())).add(get(detail.getBuildingMaintenanceHq())).add(get(detail.getEquipmentMaintenanceHq()))
                        .add(get(detail.getComputerItMaintenanceHq())).add(get(detail.getAirConditionerAmc())).add(get(detail.getMarketingAndSampling()))
                        .add(get(detail.getConsumableMarketing())).add(get(detail.getMarketingAndSamplingTax()))
                        .add(get(detail.getConsumableMarketingTax())).add(get(detail.getMarketingNPI())).add(get(detail.getNewsPaperCafe()))
                        .add(get(detail.getMarketingNpiHq())).add(get(detail.getConsumableLhi())).add(get(detail.getConsumableLhiTax()))
                        .add(get(detail.getConsumableMaintenance())).add(get(detail.getConsumableMaintenanceTax()))
                        .add(get(detail.getMaintenancePestControlCafe())).add(get(detail.getDomesticTicketsAndHotels())).add(get(detail.getInternationalTicketsAndHotels()))
                        .add(get(detail.getInsuranceMarine()));
//                        .add(get(detail.getPerformanceMarketingService())).add(get(detail.getGiftCardOffer()));
        //.add(get(detail.getLossOnDisposalFixedAssets()).add(get(detail.getConveyanceOdc())).add(get(detail.getShippingCharges()))
//        .add(get(detail.getShortAndExcess())).add(get(detail.getShareStampingCharges())).add(get(detail.getMarketingDiscountEcom()))
        return value;
    }

    public BigDecimal directFixedCost(UnitExpenditureDetail detail) {
        BigDecimal value = get(manpowerFixed(detail)).add(get(manpowerIncentive(detail))).add(get(facilitiesProperty(detail))).add(get(employeeMeal(detail)));
        return value;
    }
    public BigDecimal directFixedCost(UnitBudgetoryDetail detail) {
        BigDecimal value = get(manpowerFixed(detail)).add(get(manpowerIncentive(detail))).add(get(facilitiesProperty(detail))).add(get(employeeMeal(detail)));
        return value;
    }


    public BigDecimal employeeMeal(UnitExpenditureDetail detail) {
        BigDecimal value = get(detail.getEmployeeMealCogs()).add(get(detail.getEmployeeMealCogsTax()));

        return value;
    }

    public BigDecimal employeeMeal(UnitBudgetoryDetail detail) {
        BigDecimal value = get(detail.getEmployeeMealCogs()).add(get(detail.getEmployeeMealCogsTax()));

        return value;
    }

    public BigDecimal manpowerFixed(UnitExpenditureDetail detail) {
        BigDecimal value = get(detail.getSalary()).add(get(detail.getByodCharges())).add(get(detail.getCarLease())).add(get(detail.getDriverSalary()))
                .add(get(detail.getGratuity())).add(get(detail.getInsurnaceMedical())).add(get(detail.getTelephoneSR())).add(get(detail.getVehicleRunningAndMaintSR()))
                .add(get(detail.getEmployeeStockOptionExpense())).add(get(detail.getEmployerContributionLWF())).add(get(detail.getEsicEmployerCont()))
                .add(get(detail.getLeaveTravelReimbursement())).add(get(detail.getPfAdministrationCharges())).add(get(detail.getPfEmployerCont()))
                .add(get(detail.getQuarterlyIncentive())).add(get(detail.getBusinessPromotionSR())).add(get(detail.getNoticePeriodBuyout()))
                .add(get(detail.getNoticePeriodDeduction())).add(get(detail.getRelocationExpenses())).add(get(detail.getStipendExpenses()))
                .add(get(detail.getTrainingCostRecovery())).add(get(detail.getSeverancePay())).add(get(detail.getLabourCharges()));

        return value;
    }

    public BigDecimal manpowerFixed(UnitBudgetoryDetail detail) {
        BigDecimal value = get(detail.getSalary()).add(get(detail.getByodCharges())).add(get(detail.getCarLease())).add(get(detail.getDriverSalary()))
                .add(get(detail.getGratuity())).add(get(detail.getInsurnaceMedical())).add(get(detail.getTelephoneSR())).add(get(detail.getVehicleRunningAndMaintSR()))
                .add(get(detail.getEmployeeStockOptionExpense())).add(get(detail.getEmployerContributionLWF())).add(get(detail.getEsicEmployerCont()))
                .add(get(detail.getLeaveTravelReimbursement())).add(get(detail.getPfAdministrationCharges())).add(get(detail.getPfEmployerCont()))
                .add(get(detail.getQuarterlyIncentive())).add(get(detail.getBusinessPromotionSR())).add(get(detail.getNoticePeriodBuyout()))
                .add(get(detail.getNoticePeriodDeduction())).add(get(detail.getRelocationExpenses())).add(get(detail.getStipendExpenses()))
                .add(get(detail.getTrainingCostRecovery())).add(get(detail.getSeverancePay())).add(get(detail.getLabourCharges()));

        return value;
    }

    public BigDecimal manpowerIncentive(UnitExpenditureDetail detail) {
        BigDecimal value = get(detail.getSalesIncentive()).add(get(detail.getBonusAttendance())).add(get(detail.getBonusJoining()))
                .add(get(detail.getBonusReferral())).add(get(detail.getAllowanceRemoteLocation())).add(get(detail.getAllowanceCityCompensatory()))
                .add(get(detail.getBonusHoliday())).add(get(detail.getAllowanceMonk())).add(get(detail.getAllowanceEmployeeBenefit()))
                .add(get(detail.getBonusOthers())).add(get(detail.getAllowanceOthers()));

        return value;
    }

    public BigDecimal manpowerIncentive(UnitBudgetoryDetail detail) {
        BigDecimal value = get(detail.getSalesIncentive()).add(get(detail.getBonusAttendance())).add(get(detail.getBonusJoining()))
                .add(get(detail.getBonusReferral())).add(get(detail.getAllowanceRemoteLocation())).add(get(detail.getAllowanceCityCompensatory()))
                .add(get(detail.getBonusHoliday())).add(get(detail.getAllowanceMonk())).add(get(detail.getAllowanceEmployeeBenefit()))
                .add(get(detail.getBonusOthers())).add(get(detail.getAllowanceOthers()));

        return value;
    }

    public BigDecimal facilitiesProperty(UnitExpenditureDetail detail) {
        BigDecimal value = get(detail.getPropertyTax()).add(get(detail.getDgRental())).add(get(detail.getPropertyFixRent()))
                .add(get(detail.getRevenueShare())).add(get(detail.getFixCAM())).add(get(detail.getChillingCharges()))
                .add(get(detail.getMarketingCharges())).add(get(detail.getLicenseExpenses()));

        return value;
    }

    public BigDecimal facilitiesProperty(UnitBudgetoryDetail detail) {
        BigDecimal value = get(detail.getPropertyTax()).add(get(detail.getDgRental())).add(get(detail.getPropertyFixRent()))
                .add(get(detail.getRevenueShare())).add(get(detail.getFixCAM())).add(get(detail.getChillingCharges()))
                .add(get(detail.getMarketingCharges())).add(get(detail.getLicenseExpenses()));

        return value;
    }

    public BigDecimal inDirectFixedCost(UnitExpenditureDetail detail) {
        BigDecimal value = consumable(detail).add(facilitiesFixed(detail).add(logistics(detail)).add(supportCommWh(detail))).add(supportOpsManagement(detail));
        return value;
    }

    public BigDecimal inDirectFixedCost(UnitBudgetoryDetail detail) {
        BigDecimal value = consumable(detail).add(facilitiesFixed(detail).add(logistics(detail)).add(supportCommWh(detail))).add(supportOpsManagement(detail));
        return value;
    }

    public BigDecimal consumable(UnitExpenditureDetail detail) {
        BigDecimal value = get(detail.getConsumableUtility()).add(get(detail.getConsumableStationary())).add(get(detail.getConsumableUniform()))
                .add(get(detail.getConsumableCutlery())).add(get(detail.getConsumableEquipment())).add(get(detail.getConsumableOthers()))
                .add(get(detail.getConsumableOthersTax())).add(get(detail.getConsumableUtilityTax())).add(get(detail.getConsumableStationaryTax()))
                .add(get(detail.getConsumableUniformTax())).add(get(detail.getConsumableEquipmentTax()))
                .add(get(detail.getConsumableIt())).add(get(detail.getConsumableItTax())).add(get(detail.getConsumableOfficeEquipment()))
                .add(get(detail.getConsumableOfficeEquipmentTax())).add(get(detail.getConsumableChaiMonk())).add(get(detail.getConsumableChaiMonkTax()))
                .add(get(detail.getConsumableKitchenEquipment())).add(get(detail.getConsumableKitchenEquipmentTax())).add(get(detail.getConsumableCutleryTax()));

        return value;
    }
    public BigDecimal consumable(UnitBudgetoryDetail detail) {
        BigDecimal value = get(detail.getConsumableUtility()).add(get(detail.getConsumableStationary())).add(get(detail.getConsumableUniform()))
                .add(get(detail.getConsumableCutlery())).add(get(detail.getConsumableEquipment())).add(get(detail.getConsumableOthers()))
                .add(get(detail.getConsumableOthersTax())).add(get(detail.getConsumableUtilityTax())).add(get(detail.getConsumableStationaryTax()))
                .add(get(detail.getConsumableUniformTax())).add(get(detail.getConsumableEquipmentTax()))
                .add(get(detail.getConsumableIt())).add(get(detail.getConsumableItTax())).add(get(detail.getConsumableOfficeEquipment()))
                .add(get(detail.getConsumableOfficeEquipmentTax())).add(get(detail.getConsumableChaiMonk())).add(get(detail.getConsumableChaiMonkTax()))
                .add(get(detail.getConsumableKitchenEquipment())).add(get(detail.getConsumableKitchenEquipmentTax())).add(get(detail.getConsumableCutleryTax()));

        return value;
    }

    public BigDecimal facilitiesFixed(UnitExpenditureDetail detail) {
        BigDecimal value = get(detail.getCommunicationInternet()).add(get(detail.getCommunicationTelephone())).add(get(detail.getCommunicationILL())).
                add(get(detail.getPayrollProcessingFee())).add(get(detail.getEdcRental())).add(get(detail.getSystemRental())).add(get(detail.getRoRental()))
                .add(get(detail.getPettyCashRentals())).add(get(detail.getMusicRentals())).add(get(detail.getInternetPartnerRental())).add(get(detail.getInsuranceCGL()))
                .add(get(detail.getInsuranceDnO())).add(get(detail.getOdcRental())).add(get(detail.getInsuranceAssets()));

        return value;
    }

    public BigDecimal facilitiesFixed(UnitBudgetoryDetail detail) {
        BigDecimal value = get(detail.getCommunicationInternet()).add(get(detail.getCommunicationTelephone())).add(get(detail.getCommunicationILL())).
                add(get(detail.getPayrollProcessingFee())).add(get(detail.getEdcRental())).add(get(detail.getSystemRental())).add(get(detail.getRoRental()))
                .add(get(detail.getPettyCashRentals())).add(get(detail.getMusicRentals())).add(get(detail.getInternetPartnerRental())).add(get(detail.getInsuranceCGL()))
                .add(get(detail.getInsuranceDnO())).add(get(detail.getOdcRental())).add(get(detail.getInsuranceAssets()));

        return value;
    }

    public BigDecimal supportCommWh(UnitExpenditureDetail detail) {
        return get(detail.getSupportCommWH());
    }

    public BigDecimal supportCommWh(UnitBudgetoryDetail detail) {
        return get(detail.getSupportCommWH());
    }

    public BigDecimal supportOpsManagement(UnitExpenditureDetail detail) {
        return get(detail.getSupportOpsManagement());
    }

    public BigDecimal supportOpsManagement(UnitBudgetoryDetail detail) {
        return get(detail.getSupportOpsManagement());
    }

    public BigDecimal logistics(UnitExpenditureDetail detail) {
        BigDecimal value = get(detail.getLogisticInterstateColdVehicle()).add(get(detail.getLogisticInterstateNonColdVehicle()))
                .add(get(detail.getLogisticInterstateAir())).add(get(detail.getLogisticInterstateRoad())).add(get(detail.getLogisticInterstateTrain()));

        return value;
    }

    public BigDecimal logistics(UnitBudgetoryDetail detail) {
        BigDecimal value = get(detail.getLogisticInterstateColdVehicle()).add(get(detail.getLogisticInterstateNonColdVehicle()))
                .add(get(detail.getLogisticInterstateAir())).add(get(detail.getLogisticInterstateRoad())).add(get(detail.getLogisticInterstateTrain()));

        return value;
    }

    public BigDecimal facilitiesVariable(UnitExpenditureDetail detail) {
        BigDecimal value = get(detail.getEnergyElectricity()).add(get(detail.getWaterCharges())).add(get(detail.getEnergyDGRunningCafe()))
                .add(get(detail.getWaterChargesCafe()));

        return value;
    }
    public BigDecimal facilitiesVariable(UnitBudgetoryDetail detail) {
        BigDecimal value = get(detail.getEnergyElectricity()).add(get(detail.getWaterCharges())).add(get(detail.getEnergyDGRunningCafe()))
                .add(get(detail.getWaterCharges()));

        return value;
    }

    public BigDecimal commissionCardWallets(UnitExpenditureDetail detail) {
        BigDecimal value = get(detail.getCreditCardTransactionCharges()).add(get(detail.getVoucherTransactionCharges()))
                .add(get(detail.getWalletsTransactionCharges()));

        return value;
    }

    public BigDecimal commissionCardWallets(UnitBudgetoryDetail detail) {
        BigDecimal value = get(detail.getCreditCardTransactionCharges()).add(get(detail.getVoucherTransactionCharges()))
                .add(get(detail.getWalletsTransactionCharges()));

        return value;
    }

    public BigDecimal commissionCp(UnitExpenditureDetail detail) {
        BigDecimal value = get(detail.getFixedParkingCharges()).add(get(detail.getVehicleRegularMaintenanceHq())).add(get(detail.getCommissionChannelPartners()))
                .add(get(detail.getCancellationChargesChannelPartners())).add(get(detail.getOtherTransactionCharges())).add(get(detail.getOtherChargesEcom()))
                .add(get(detail.getComissionChannelPartnerFixed()));

        return value;
    }

    public BigDecimal commissionCp(UnitBudgetoryDetail detail) {
        BigDecimal value = get(detail.getFixedParkingCharges()).add(get(detail.getVehicleRegularMaintenanceHq())).add(get(detail.getCommissionChannelPartners()))
                .add(get(detail.getCancellationChargesChannelPartners()));
//        .add(get(detail.getOtherTransactionCharges())).add(get(detail.getOtherChargesEcom()))
//                .add(get(detail.getComissionChannelPartnerFixed()));

        return value;
    }

    public BigDecimal deliveryCharges(UnitExpenditureDetail detail) {
        BigDecimal value = get(detail.getDepreciationOfBike()).add(get(detail.getFuelCharges())).add(get(detail.getVehicleRegularMaintenance()))
                .add(get(detail.getParkingCharges())).add(get(detail.getTravellingExpense())).add(get(detail.getFuelChargesCafe()));

        return value;
    }

    public BigDecimal deliveryCharges(UnitBudgetoryDetail detail) {
        BigDecimal value = get(detail.getDepreciationOfBike()).add(get(detail.getFuelCharges())).add(get(detail.getVehicleRegularMaintenance()))
                .add(get(detail.getParkingCharges())).add(get(detail.getTravellingExpense())).add(get(detail.getFuelChargesCafe()));

        return value;
    }

    public BigDecimal anyOtherVariable(UnitExpenditureDetail detail) {
        BigDecimal value = get(detail.getSecurityGuardCharges()).add(get(detail.getCommissionChange())).add(get(detail.getCommissionChangeCafe())).add(get(detail.getNewsPaper())).add(get(detail.getStaffWelfareExpensesCafe()))
                .add(get(detail.getStaffWelfareExpenses())).add(get(detail.getCourierCharges())).add(get(detail.getPrintingAndStationary())).add(get(detail.getPrintingAndStationaryCafe()))
                .add(get(detail.getBusinessPromotion())).add(get(detail.getLegalCharges())).add(get(detail.getProfessionalCharges()))
                .add(get(detail.getConveyanceMarketing())).add(get(detail.getConveyanceOperations())).add(get(detail.getConveyanceOthers()))
                .add(get(detail.getAuditFee())).add(get(detail.getAuditFeeOutOfPocket())).add(get(detail.getCharityAndDonations()))
                .add(get(detail.getDomesticTicketsAndHotels())).add(get(detail.getInternationalTicketsAndHotels())).add(get(detail.getNewsPaperCafe()))
                .add(get(detail.getHouseKeepingCharges())).add(get(detail.getLateFeeCharges())).add(get(detail.getMarketingDataAnalysis()))
                .add(get(detail.getMiscellaneousExpenses())).add(get(detail.getPenalty())).add(get(detail.getPhotoCopyExpenses())).add(get(detail.getPhotoCopyExpensesCafe()))
                .add(get(detail.getQcrExpense())).add(get(detail.getRecuritmentConsultants())).add(get(detail.getRocFees()))
                .add(get(detail.getConveyanceOdc())).add(get(detail.getDebitCreditWrittenOff())).add(get(detail.getDifferenceInExchange()))
                .add(get(detail.getRoundedOff())).add(get(detail.getTravellingExpense())).add(get(detail.getCourierChargesCafe()))
                .add(get(detail.getBankCharges())).add(get(detail.getShippingCharges())).add(get(detail.getBusinessPromotionCafe()))
                .add(get(detail.getInsuranceMarine())).add(get(detail.getShareStampingCharges())).add(get(detail.getIntrestOnTDSorGST()));
        // lossOnDisposableOfFixedAssets,
        return value;
    }
    public BigDecimal anyOtherVariable(UnitBudgetoryDetail detail) {
        BigDecimal value = get(detail.getSecurityGuardCharges()).add(get(detail.getCommissionChange())).add(get(detail.getCommissionChangeCafe())).add(get(detail.getNewsPaper())).add(get(detail.getStaffWelfareExpensesCafe()))
                .add(get(detail.getStaffWelfareExpenses())).add(get(detail.getCourierCharges())).add(get(detail.getPrintingAndStationary())).add(get(detail.getPrintingAndStationaryCafe()))
                .add(get(detail.getBusinessPromotion())).add(get(detail.getLegalCharges())).add(get(detail.getProfessionalCharges()))
                .add(get(detail.getConveyanceMarketing())).add(get(detail.getConveyanceOperations())).add(get(detail.getConveyanceOthers()))
                .add(get(detail.getAuditFee())).add(get(detail.getAuditFeeOutOfPocket())).add(get(detail.getCharityAndDonations()))
                .add(get(detail.getDomesticTicketsAndHotels())).add(get(detail.getInternationalTicketsAndHotels())).add(get(detail.getNewsPaperCafe()))
                .add(get(detail.getHouseKeepingCharges())).add(get(detail.getLateFeeCharges())).add(get(detail.getMarketingDataAnalysis()))
                .add(get(detail.getMiscellaneousExpenses())).add(get(detail.getPenalty())).add(get(detail.getPhotoCopyExpenses())).add(get(detail.getPhotoCopyExpensesCafe()))
                .add(get(detail.getQcrExpense())).add(get(detail.getRecuritmentConsultants())).add(get(detail.getRocFees()))
                .add(get(detail.getDebitCreditWrittenOff())).add(get(detail.getDifferenceInExchange()))
                .add(get(detail.getRoundedOff())).add(get(detail.getTravellingExpense())).add(get(detail.getCourierChargesCafe()))
                .add(get(detail.getBankCharges())).add(get(detail.getBusinessPromotionCafe()))
                .add(get(detail.getInsuranceMarine())).add(get(detail.getIntrestOnTDSorGST()));
        // lossOnDisposableOfFixedAssets,.add(get(detail.getConveyanceOdc())).add(get(detail.getShippingCharges())).add(get(detail.getShareStampingCharges()))
        return value;
    }

    public BigDecimal maintenance(UnitExpenditureDetail detail) {
        BigDecimal value = get(detail.getBuildingMaintenance()).add(get(detail.getComputerMaintenance())).add(get(detail.getPestControlCharges()))
                .add(get(detail.getEquipmentMaintenance())).add(get(detail.getProntoAMC())).add(get(detail.getOthersAMC()))
                .add(get(detail.getOthersMaintenance())).add(get(detail.getRnDEngineeringExpenses())).add(get(detail.getRoAMC()))
                .add(get(detail.getBuildingMaintenanceHq())).add(get(detail.getComputerItMaintenanceHq())).add(get(detail.getEquipmentMaintenanceHq()))
                .add(get(detail.getMaintenancePestControlCafe()))
                .add(get(detail.getAirConditionerAmc())).add(get(detail.getConsumableLhi())).add(get(detail.getConsumableLhiTax()))
                .add(get(detail.getConsumableMaintenance())).add(get(detail.getConsumableMaintenanceTax())).add(get(detail.getCleaningCharges()))
                .add(get(detail.getCleaningChargesCafe()));

        return value;
    }

    public BigDecimal maintenance(UnitBudgetoryDetail detail) {
        BigDecimal value = get(detail.getBuildingMaintenance()).add(get(detail.getComputerMaintenance())).add(get(detail.getPestControlCharges()))
                .add(get(detail.getEquipmentMaintenance())).add(get(detail.getProntoAMC())).add(get(detail.getOthersAMC()))
                .add(get(detail.getOthersMaintenance())).add(get(detail.getRnDEngineeringExpenses())).add(get(detail.getRoAMC()))
                .add(get(detail.getBuildingMaintenanceHq())).add(get(detail.getComputerItMaintenanceHq())).add(get(detail.getEquipmentMaintenanceHq()))
                .add(get(detail.getMaintenancePestControlCafe()))
                .add(get(detail.getAirConditionerAmc())).add(get(detail.getConsumableLhi())).add(get(detail.getConsumableLhiTax()))
                .add(get(detail.getConsumableMaintenance())).add(get(detail.getConsumableMaintenanceTax())).add(get(detail.getCleaningCharges()))
                .add(get(detail.getCleaningChargesCafe()));

        return value;
    }

    public BigDecimal marketingLs(UnitExpenditureDetail detail) {
        BigDecimal value = get(detail.getMarketingAndSampling()).add(get(detail.getConsumableMarketing())).add(get(detail.getMarketingAndSamplingTax()))
                .add(get(detail.getConsumableMarketingTax())).add(get(detail.getMarketingNPI())).add(get(detail.getMarketingNpiHq()))
                .add(get(detail.getPerformanceMarketingService())).add(get(detail.getMarketingDiscountEcom())).add(get(detail.getGiftCardOffer()));
        return value;
    }

    public BigDecimal marketingLs(UnitBudgetoryDetail detail) {
        BigDecimal value = get(detail.getMarketingAndSampling()).add(get(detail.getConsumableMarketing())).add(get(detail.getMarketingAndSamplingTax()))
                .add(get(detail.getConsumableMarketingTax())).add(get(detail.getMarketingNPI())).add(get(detail.getMarketingNpiHq())).add(get(detail.getDiscountGiftCard()));
//                .add(get(detail.getPerformanceMarketingService())).add(get(detail.getMarketingDiscountEcom())));
        return value;
    }

    public BigDecimal growthPnl(UnitExpenditureDetail detail) {
        BigDecimal value = get(detail.getOpeningLicencesFees()).add(get(detail.getPreOpeningRegistrationCharges())).add(get(detail.getPreOpeningStampDutyCharges()))
                .add(get(detail.getBrokerage())).add(get(detail.getMarketingLaunch())).add(get(detail.getPreOpeningConsumable()))
                .add(get(detail.getPreOpeningOthers())).add(get(detail.getPreOpeningSalary())).add(get(detail.getPreOpeningConsumableTax()));

        return value;
    }

    public BigDecimal growthPnl(UnitBudgetoryDetail detail) {
        BigDecimal value = get(detail.getOpeningLicencesFees()).add(get(detail.getPreOpeningRegistrationCharges())).add(get(detail.getPreOpeningStampDutyCharges()))
                .add(get(detail.getBrokerage())).add(get(detail.getMarketingLaunch())).add(get(detail.getPreOpeningConsumable()))
                .add(get(detail.getPreOpeningOthers())).add(get(detail.getPreOpeningSalary())).add(get(detail.getPreOpeningConsumableTax()));

        return value;
    }

    public BigDecimal marketingCorp(UnitExpenditureDetail detail) {
        BigDecimal value = get(detail.getCorporateMarketingOutdoor()).add(get(detail.getCorporateMarketingAgencyFees()))
                .add(get(detail.getCorporateMarketingSms())).add(get(detail.getCorporateMarketingPhotography())).add(get(detail.getCorporateMarketingChannelPartner()))
                .add(get(detail.getCorporateMarketingAtlRadio())).add(get(detail.getCorporateMarketingAtlTv()))
                .add(get(detail.getCorporateMarketingAtlPrintAd())).add(get(detail.getCorporateMarketingAtlCinema())).add(get(detail.getCorporateMarketingAtlDigital()));

        return value;
    }

    public BigDecimal marketingCorp(UnitBudgetoryDetail detail) {
        BigDecimal value = get(detail.getCorporateMarketingOutdoor()).add(get(detail.getCorporateMarketingAgencyFees()))
                .add(get(detail.getCorporateMarketingSms())).add(get(detail.getCorporateMarketingPhotography())).add(get(detail.getCorporateMarketingChannelPartner()))
                .add(get(detail.getCorporateMarketingAtlRadio())).add(get(detail.getCorporateMarketingAtlTv()))
                .add(get(detail.getCorporateMarketingAtlPrintAd())).add(get(detail.getCorporateMarketingAtlCinema())).add(get(detail.getCorporateMarketingAtlDigital()));

        return value;
    }

    public BigDecimal allocation(UnitExpenditureDetail detail) {
        BigDecimal value = marketingCorp(detail).add(supportHq(detail)).add(ficoPayouts(detail).add(technology(detail)));
        return value;
    }

    public BigDecimal allocation(UnitBudgetoryDetail detail) {
        BigDecimal value = marketingCorp(detail).add(supportHq(detail)).add(ficoPayouts(detail).add(technology(detail)));
        return value;
    }

    public BigDecimal supportHq(UnitExpenditureDetail detail) {
        BigDecimal value = get(detail.getSupportCCC()).add(get(detail.getSupport()));

        return value;
    }

    public BigDecimal supportHq(UnitBudgetoryDetail detail) {
        BigDecimal value = get(detail.getSupportCCC()).add(get(detail.getSupport()));

        return value;
    }

    public BigDecimal ficoPayouts(UnitExpenditureDetail detail) {
        BigDecimal value = get(detail.getServiceChargesFICO()).add(get(detail.getInterestOnFixedDepositFICO())).add(get(detail.getInterestOnTermLoan()));

        return value;
    }

    public BigDecimal ficoPayouts(UnitBudgetoryDetail detail) {
        BigDecimal value = get(detail.getServiceChargesFICO()).add(get(detail.getInterestOnFixedDepositFICO())).add(get(detail.getInterestOnTermLoan()));

        return value;
    }

    public BigDecimal technology(UnitExpenditureDetail detail) {
        BigDecimal value = get(detail.getTechnologyPlatformCharges()).add(get(detail.getTechnologyTraining())).add(get(detail.getTechnologyOthers()))
                .add(get(detail.getTechnologyVariable()));

        return value;
    }

    public BigDecimal technology(UnitBudgetoryDetail detail) {
        BigDecimal value = get(detail.getTechologyPlatformCharges()).add(get(detail.getTechologyTraining())).add(get(detail.getTechologyOthers()))
                .add(get(detail.getTechnologyVariable()));

        return value;
    }

    public BigDecimal growthCapex(UnitExpenditureDetail detail) {
        BigDecimal value = get(detail.getCapitalImprovementExpenses()).add(get(detail.getLeaseHoldImprovements())).add(get(detail.getPreOpeningRent()))
                .add(get(detail.getPreOpeningEleWater())).add(get(detail.getFixedAssetsEquipmentHq())).add(get(detail.getFixedAssetFurnitureHq()))
                .add(get(detail.getFixedAssetsItHq())).add(get(detail.getFixedAssetsKitchenEquipmentHq())).add(get(detail.getFixedAssetsOfficeEquipmentHq()))
                .add(get(detail.getFixedAssetsEquipmentHqTax())).add(get(detail.getFixedAssetFurnitureHqTax())).add(get(detail.getFixedAssetsItHqTax()))
                .add(get(detail.getFixedAssetsKitchenEquipmentHqTax())).add(get(detail.getFixedAssetsOfficeEquipmentHqTax()))
                .add(get(detail.getPreOpeningCam())).add(get(detail.getFixedAssetsVehicle())).add(get(detail.getFixedAssetsVehicleTax()))
                .add(get(detail.getFixedAssetsVehicleHq())).add(get(detail.getFixedAssetsVehicleHqTax()));

        return value;
    }

//    public BigDecimal growthCapex(UnitBudgetoryDetail detail) {
//        BigDecimal value = get(detail.getCapitalImprovementExpenses()).add(get(detail.getLeaseHoldImprovements())).add(get(detail.getPreOpeningRent()))
//                .add(get(detail.getPreOpeningEleWater())).add(get(detail.getFixedAssetsEquipmentHq())).add(get(detail.getFixedAssetFurnitureHq()))
//                .add(get(detail.getFixedAssetsItHq())).add(get(detail.getFixedAssetsKitchenEquipmentHq())).add(get(detail.getFixedAssetsOfficeEquipmentHq()))
//                .add(get(detail.getFixedAssetsEquipmentHqTax())).add(get(detail.getFixedAssetFurnitureHqTax())).add(get(detail.getFixedAssetsItHqTax()))
//                .add(get(detail.getFixedAssetsKitchenEquipmentHqTax())).add(get(detail.getFixedAssetsOfficeEquipmentHqTax()))
//                .add(get(detail.getPreOpeningCam())).add(get(detail.getFixedAssetsVehicle())).add(get(detail.getFixedAssetsVehicleTax()))
//                .add(get(detail.getFixedAssetsVehicleHq())).add(get(detail.getFixedAssetsVehicleHqTax()));
//
//        return value;
//    }

    public BigDecimal capexFixedAssets(UnitExpenditureDetail detail) {
        BigDecimal value = get(detail.getFixedAssetsEquipment()).add(get(detail.getFixedAssetFurniture())).add(get(detail.getFixedAssetsIT()))
                .add(get(detail.getFixedAssetsKitchenEquipment())).add(get(detail.getFixedAssetsOfficeEquipmentTax()))
                .add(get(detail.getFixedAssetsEquipmentTax())).add(get(detail.getFixedAssetsItTax())).add(get(detail.getFixedAssetsKitchenEquipmentTax()))
                .add(get(detail.getFixedAssetFurnitureTax()));

        return value;
    }
//    public BigDecimal capexFixedAssets(UnitBudgetoryDetail detail) {
//        BigDecimal value = get(detail.getFixedAssetsEquipment()).add(get(detail.getFixedAssetFurniture())).add(get(detail.getFixedAssetsIT()))
//                .add(get(detail.getFixedAssetsKitchenEquipment())).add(get(detail.getFixedAssetsOfficeEquipmentTax()))
//                .add(get(detail.getFixedAssetsEquipmentTax())).add(get(detail.getFixedAssetsItTax())).add(get(detail.getFixedAssetsKitchenEquipmentTax()))
//                .add(get(detail.getFixedAssetFurnitureTax()));
//
//        return value;
//    }

    public BigDecimal inDirectIncome(UnitExpenditureDetail detail) {
        BigDecimal value = get(detail.getInterestOnLoan()).add(get(detail.getInterestOnFDR())).add(get(detail.getProfitSaleMutualFunds()))
                .add(get(detail.getInterestIncomeTaxRefund())).add(get(detail.getMiscIncome())).add(get(detail.getDiscountReceived()))
                .add(get(detail.getScrape())).add(get(detail.getServiceCharges())).add(get(detail.getLiabilityNoLongerRequiredWrittenBack()))
                .add(get(detail.getRoyaltyFees())).add(get(detail.getFreightCharges()));
        return value;
    }

    public BigDecimal amortisationAndDepreciation(UnitExpenditureDetail detail) {
        BigDecimal value = get(detail.getDepreciationOfFurnitureFixture()).add(get(detail.getDepreciationOfOfficeEquipment())).add(get(detail.getDepreciationOfEquipment()))
                .add(get(detail.getDepreciationOfIt())).add(get(detail.getDepreciationOfVehicle())).add(get(detail.getDepreciationOfOthers()))
                .add(get(detail.getDepreciationOfKitchenEquipment()));
        return value;
    }

//    public BigDecimal amortisationAndDepreciation(UnitBudgetoryDetail detail) {
//        BigDecimal value = get(detail.getDepreciationOfFurnitureFixture()).add(get(detail.getDepreciationOfOfficeEquipment())).add(get(detail.getDepreciationOfEquipment()))
//                .add(get(detail.getDepreciationOfIt())).add(get(detail.getDepreciationOfVehicle())).add(get(detail.getDepreciationOfOthers()))
//                .add(get(detail.getDepreciationOfKitchenEquipment()));
//        return value;
//    }

    public BigDecimal growthSd(UnitExpenditureDetail detail) {
        BigDecimal value = get(detail.getSecurityDepositProperty()).add(get(detail.getSecurityDepositMVAT())).add(get(detail.getSecurityDepositElectricity()));
        return value;
    }
//    public BigDecimal growthSd(UnitBudgetoryDetail detail) {
//        BigDecimal value = get(detail.getSecurityDepositProperty()).add(get(detail.getSecurityDepositMVAT())).add(get(detail.getSecurityDepositElectricity()));
//        return value;
//    }

    public BigDecimal employeeDiscount(UnitExpenditureDetail detail) {
        BigDecimal value = get(detail.getEmpDiscountLoyalty()).add(get(detail.getEmpDiscountMarketing())).add(get(detail.getEmpDiscountOps())).add(get(detail.getEmpDiscountBd()))
                .add(get(detail.getEmpDiscountEmployeeFico()));
        return value;
    }

    public BigDecimal employeeDiscount(UnitBudgetoryDetail detail) {
        BigDecimal value = get(detail.getEmpDiscountLoyalty()).add(get(detail.getEmpDiscountMarketing())).add(get(detail.getEmpDiscountOps())).add(get(detail.getEmpDiscountBd()))
                .add(get(detail.getEmpDiscountEmployeeFico()));
        return value;
    }

    public BigDecimal empMealApc(UnitExpenditureDetail detail) {
        BigDecimal value = AppUtils.divideWithScale(get(employeeMealSales(detail)), (get(new BigDecimal(detail.getEmployeeMealTicket()))), 4);
        return value;
    }

    public PnLRepresentation render(UnitExpenditureAggregateDetail currentAggregate, List<UnitExpenditureAggregateDetail> mtdAggregates, UnitBudgetoryDetail budget) throws DataUpdationException {
        // calculate prorata budget
        YearMonth yearMonthObject = YearMonth.of(budget.getYear(), budget.getMonth());
        int daysInMonth = yearMonthObject.lengthOfMonth();
        BigDecimal proRata = AppUtils.divideWithScale10(new BigDecimal(currentAggregate.getDay()), new BigDecimal(daysInMonth));

        PnLRepresentationBuilder b = new PnLRepresentationBuilder();
        b.key("Business Date")
                .currentValue(currentAggregate.getBusinessDate().toString())
                .mtdValue(mtdAggregates.stream().map(u -> u.getBusinessDate().toString()).collect(Collectors.toList())).add()
            .key(" Unit Id")
                .currentValue(currentAggregate.getUnitId())
                .mtdValue(mtdAggregates.stream().map(u -> u.getUnitId() + "").collect(Collectors.toList())).add()
            .key(" Unit Name")
                .currentValue(currentAggregate.getUnitName())
                .mtdValue(mtdAggregates.stream().map(u -> u.getUnitName()).collect(Collectors.toList())).add()
            .key("1. Total Transaction")
                .currentValue(currentAggregate.totalTickets)
                .mtdValue(mtdAggregates.stream().map(u -> u.getTotalTickets() + "").collect(Collectors.toList())).budget(budget.getTicket())
                .budgetMtd(AppUtils.multiply(new BigDecimal(budget.getTicket()), proRata) + "").add()
            .key("2. Gross sales value")
                .currentValue(currentAggregate.totalGmv)
                .mtdValue(mtdAggregates.stream().map(u -> u.getTotalGmv() + "").collect(Collectors.toList())).budget(budget.getSales())
                .budgetMtd(AppUtils.multiply(budget.getSales(), proRata) + "").add()
            .key("3. Discount")
                .currentValue(currentAggregate.totalDiscount)
                .mtdValue(mtdAggregates.stream().map(u -> u.getTotalDiscount() + "").collect(Collectors.toList())).budget(budget.getDiscount())
                .budgetMtd(AppUtils.multiply(budget.getDiscount(), proRata) + "")
                    .drilldown("3.1 Discount - Loyal Tea",
                        currentAggregate.totalDiscountLoyalTea + "",
                        mtdAggregates.stream().map(u -> u.getTotalDiscountLoyalTea() + "").collect(Collectors.toList()), budget.getDiscountLoyalty() + "",
                        AppUtils.multiply(budget.getDiscountLoyalty(), proRata) + "")
                    .drilldown("3.2. Discount - Marketing", currentAggregate.totalDiscountMarketing + "",
                        mtdAggregates.stream().map(u -> u.getTotalDiscountMarketing() + "").collect(Collectors.toList()), budget.getDiscountMarketing() + "",
                        AppUtils.multiply(budget.getDiscountMarketing(), proRata) + "")
                    .drilldown("3.3. Discount - Ops", currentAggregate.totalDiscountOps + "",
                        mtdAggregates.stream().map(u -> u.getTotalDiscountOps() + "").collect(Collectors.toList()), budget.getDiscountOps() + "",
                        AppUtils.multiply(budget.getDiscountOps(), proRata) + "")
                    .drilldown("3.4. Discount - BD", currentAggregate.totalDiscountBd + "",
                        mtdAggregates.stream().map(u -> u.getTotalDiscountBd() + "").collect(Collectors.toList()), budget.getDiscountBd() + "",
                        AppUtils.multiply(budget.getDiscountBd(), proRata) + "")
                    .drilldown("3.5. Discount - Employee /FICO", currentAggregate.totalDiscountEmployeeFico + "",
                        mtdAggregates.stream().map(u -> u.getTotalDiscountEmployeeFico() + "").collect(Collectors.toList()), budget.getDiscountEmployeeFico() + "",
                        AppUtils.multiply(budget.getDiscountEmployeeFico(), proRata) + "").add()
            .key("4. Net Revenue")
                .currentValue(currentAggregate.totalSales)
                .mtdValue(mtdAggregates.stream().map(u -> u.getTotalSales() + "").collect(Collectors.toList()))
                .budget(budget.getSales()).budgetMtd(AppUtils.multiply(budget.getSales(), proRata)).add()
            .key("5. Total Direct Variable  COGS")
                .currentValue(currentAggregate.directVariableCogs)
                .mtdValue(mtdAggregates.stream().map(u -> u.getDirectVariableCogs() + "").collect(Collectors.toList())).budget(directVariableCogs(budget))
                .budgetMtd(AppUtils.multiply(directVariableCogs(budget), proRata))
                    .drilldown("5.1. COGS", currentAggregate.cogs + "",
                        mtdAggregates.stream().map(u -> u.getCogs() + "").collect(Collectors.toList()), cogs(budget) + "",
                        AppUtils.multiply(cogs(budget), proRata)+"")
                    .drilldown("5.2. Wastage + Expiry", currentAggregate.wastageExpiry + "",
                        mtdAggregates.stream().map(u -> u.getWastageExpiry() + "").collect(Collectors.toList()),
                        wastageExpiry(budget) + "", AppUtils.multiply(wastageExpiry(budget), proRata)+"")
                    .drilldown("5.3. Stock Variance", currentAggregate.stockVariance + "",
                        mtdAggregates.stream().map(u -> u.getStockVariance() + "").collect(Collectors.toList()),
                        stockVariance(budget) + "", AppUtils.multiply(stockVariance(budget), proRata)+"").add()
            .key("6. Gross Profit")
                .currentValue(currentAggregate.grossProfit)
                .mtdValue(mtdAggregates.stream().map(u -> u.getGrossProfit() + "").collect(Collectors.toList()))
                .budget(calculateGrossProfit(budget)+"").budgetMtd(AppUtils.multiply(calculateGrossProfit(budget),proRata)).add()
            .key("7. Gross Profit %")
                .currentValue(currentAggregate.grossProfitPercentage)
                .mtdValue(mtdAggregates.stream().map(u -> u.getGrossProfitPercentage() + "").collect(Collectors.toList()))
                .budget(AppUtils.percentage(calculateGrossProfit(budget), totalSales(budget))+"")
                .budgetMtd(AppUtils.multiply(AppUtils.percentage(calculateGrossProfit(budget), totalSales(budget)),proRata)+"").add()
            .key("8. Total Direct Variable Others")
                .currentValue(currentAggregate.directVariableOthers)
                .mtdValue(mtdAggregates.stream().map(u -> u.getDirectVariableOthers() + "").collect(Collectors.toList()))
                .budget(directVariableOthers(budget)+"").budgetMtd(AppUtils.multiply(directVariableOthers(budget), proRata)+"")
                    .drilldown("9.1. Facilities Variable", currentAggregate.facilitiesVariable + "",
                        mtdAggregates.stream().map(u -> u.getFacilitiesVariable() + "").collect(Collectors.toList()), facilitiesVariable(budget)+"",
                        AppUtils.multiply(facilitiesVariable(budget), proRata)+"")
                    .drilldown("9.2. Commission - Card/Wallets", currentAggregate.commissionCardsWallets + "",
                        mtdAggregates.stream().map(u -> u.getCommissionCardsWallets() + "").collect(Collectors.toList()), commissionCardWallets(budget)+"",
                        AppUtils.multiply(commissionCardWallets(budget), proRata)+"")
                    .drilldown("9.3. Commissions - CP", currentAggregate.commissionCp + "",
                        mtdAggregates.stream().map(u -> u.getCommissionCp() + "").collect(Collectors.toList()), commissionCp(budget)+"",
                        AppUtils.multiply(commissionCp(budget), proRata)+"")
                    .drilldown("9.4. Delivery Charges", currentAggregate.deliveryCharges + "",
                        mtdAggregates.stream().map(u -> u.getDeliveryCharges() + "").collect(Collectors.toList()), deliveryCharges(budget)+"",
                        AppUtils.multiply(deliveryCharges(budget), proRata)+"").add()
            .key("9. Total Indirect Variable Cost")
                .currentValue(currentAggregate.inDirectVariableCost)
                .mtdValue(mtdAggregates.stream().map(u -> u.getInDirectVariableCost() + "").collect(Collectors.toList()))
                .budget(inDirectVariableCost(budget)+"").budgetMtd(AppUtils.multiply(inDirectVariableCost(budget),proRata)+"")
                    .drilldown("9.1. Any Other Variable", currentAggregate.anyOtherVariable + "",
                        mtdAggregates.stream().map(u -> u.getAnyOtherVariable() + "").collect(Collectors.toList()), anyOtherVariable(budget)+"",
                        AppUtils.multiply(anyOtherVariable(budget),proRata)+"")
                    .drilldown("9.2. Maintenance", currentAggregate.maintenance + "",
                        mtdAggregates.stream().map(u -> u.getMaintenance() + "").collect(Collectors.toList()), maintenance(budget)+"",
                        AppUtils.multiply(maintenance(budget),proRata)+"")
                    .drilldown("9.3. Marketing-LS", currentAggregate.marketingLs + "",
                        mtdAggregates.stream().map(u -> u.getMarketingLs() + "").collect(Collectors.toList()), marketingLs(budget)+"",
                        AppUtils.multiply(marketingLs(budget),proRata)+"").add()
            .key("10. Contribution")
                .currentValue(currentAggregate.contribution)
                .mtdValue(mtdAggregates.stream().map(u -> u.getContribution() + "").collect(Collectors.toList()))
                .budget(calculateContribution(budget)+"").budgetMtd(AppUtils.multiply(calculateContribution(budget),proRata)+"").add()
            .key("11. Contribution %").currentValue(currentAggregate.contributionPercentage)
                .mtdValue(mtdAggregates.stream().map(u -> u.getContributionPercentage() + "").collect(Collectors.toList()))
                .budget(AppUtils.percentage(calculateContribution(budget), totalSales(budget)))
                .budgetMtd(AppUtils.percentage(AppUtils.percentage(calculateContribution(budget), totalSales(budget)),proRata)+"").add()
            .key("12. Total Direct Fixed cost")
                .currentValue(currentAggregate.directFixedCost)
                .mtdValue(mtdAggregates.stream().map(u -> u.getDirectFixedCost() + "").collect(Collectors.toList()))
                .budget(directFixedCost(budget)+"")
                .budgetMtd(AppUtils.multiply(directFixedCost(budget),proRata)+"")
                    .drilldown("12.1. Manpower - Fixed", currentAggregate.manpowerFixed + "",
                        mtdAggregates.stream().map(u -> u.getManpowerFixed() + "").collect(Collectors.toList()),
                        manpowerFixed(budget)+"", AppUtils.multiply(manpowerFixed(budget),proRata)+"")
                    .drilldown("12.2. Employee Meal", currentAggregate.employeeMeal + "",
                        mtdAggregates.stream().map(u -> u.getEmployeeMeal() + "").collect(Collectors.toList()),
                        employeeMeal(budget)+"",AppUtils.multiply(employeeMeal(budget),proRata)+ "")
                    .drilldown("12.3. Manpower - Incentive", currentAggregate.manpowerIncentive + "",
                        mtdAggregates.stream().map(u -> u.getManpowerIncentive() + "").collect(Collectors.toList()),
                        manpowerIncentive(budget)+"",AppUtils.multiply(manpowerIncentive(budget),proRata)+ "")
                    .drilldown("12.4. Facilities - Property", currentAggregate.facilitiesProperty + "",
                        mtdAggregates.stream().map(u -> u.getFacilitiesProperty() + "").collect(Collectors.toList()),
                        facilitiesFixed(budget)+"", AppUtils.multiply(facilitiesFixed(budget),proRata)+"").add()
            .key("13. Total Indirect Fixed Cost")
                .currentValue(currentAggregate.inDirectFixedCost)
                .mtdValue(mtdAggregates.stream().map(u -> u.getInDirectFixedCost() + "").collect(Collectors.toList()))
                .budget(inDirectFixedCost(budget)+"").budgetMtd(AppUtils.multiply(inDirectFixedCost(budget),proRata)+"")
                    .drilldown("13.1. Consumable", currentAggregate.consumable + "",
                        mtdAggregates.stream().map(u -> u.getConsumable() + "").collect(Collectors.toList()), consumable(budget) + "",
                        AppUtils.multiply(consumable(budget), proRata) + "")
                    .drilldown("13.2. Logistics", currentAggregate.logistics + "",
                        mtdAggregates.stream().map(u -> u.getLogistics() + "").collect(Collectors.toList()), logistics(budget)+"",
                        AppUtils.multiply(logistics(budget),proRata)+"")
                    .drilldown("13.3. Facilities Fixed", currentAggregate.facilitiesFixed + "",
                        mtdAggregates.stream().map(u -> u.getFacilitiesFixed() + "").collect(Collectors.toList()), facilitiesFixed(budget)+"",
                        AppUtils.multiply(facilitiesFixed(budget),proRata)+"")
                    .drilldown("13.4. Support - Comm/WH", currentAggregate.supportCommWh + "",
                        mtdAggregates.stream().map(u -> u.getSupportCommWh() + "").collect(Collectors.toList()), supportCommWh(budget) + "",
                        AppUtils.multiply(supportCommWh(budget), proRata) + "")
                    .drilldown("13.4. Support - Ops Management", currentAggregate.supportOpsManagement + "",
                        mtdAggregates.stream().map(u -> u.getSupportOpsManagement() + "").collect(Collectors.toList()), supportOpsManagement(budget) + "",
                        AppUtils.multiply(supportOpsManagement(budget), proRata) + "").add()
            .key("14. Operating Profit")
                .currentValue(currentAggregate.operatingProfit)
                .mtdValue(mtdAggregates.stream().map(u -> u.getOperatingProfit() + "").collect(Collectors.toList()))
                .budget(calculateOperationProfit(budget)+"")
                .budgetMtd(AppUtils.multiply(calculateOperationProfit(budget),proRata)+"").add()
            .key("15. Operating Profit%")
                .currentValue(currentAggregate.operatingProfitPercentage)
                .mtdValue(mtdAggregates.stream().map(u -> u.getOperatingProfitPercentage() + "").collect(Collectors.toList()))
                .budget(AppUtils.percentage(calculateOperationProfit(budget),totalSales(budget))+"")
                .budgetMtd(AppUtils.multiply(AppUtils.percentage(calculateOperationProfit(budget),totalSales(budget)),proRata)+"").add()
            .key("16. Total Allocation Cost")
                .currentValue(currentAggregate.allocation)
                .mtdValue(mtdAggregates.stream().map(u -> u.getAllocation() + "").collect(Collectors.toList()))
                .budget(allocation(budget)+"").budgetMtd(AppUtils.multiply(allocation(budget),proRata)+"")
                    .drilldown("16.1. Support - HQ", currentAggregate.supportHq + "",
                        mtdAggregates.stream().map(u -> u.getSupportHq() + "").collect(Collectors.toList()), supportHq(budget)+"",
                        AppUtils.multiply(supportHq(budget),proRata)+"")
                    .drilldown("16.2. Technology", currentAggregate.technology + "",
                        mtdAggregates.stream().map(u -> u.getTechnology() + "").collect(Collectors.toList()), technology(budget)+"",
                        AppUtils.multiply(technology(budget),proRata)+"")
                    .drilldown("16.3. Marketing-Corp", currentAggregate.marketingCorp + "",
                        mtdAggregates.stream().map(u -> u.getMarketingCorp() + "").collect(Collectors.toList()), marketingCorp(budget)+"",
                        AppUtils.multiply(marketingCorp(budget),proRata)+"").add()
            .key("17. Net Profit")
                .currentValue(currentAggregate.netProfit)
                .mtdValue(mtdAggregates.stream().map(u -> u.getNetProfit() + "").collect(Collectors.toList()))
                .budget(calculateNetProfit(budget)+"")
                .budgetMtd(AppUtils.multiply(calculateNetProfit(budget),proRata)+"").add()
            .key("18. Net Profit%")
                .currentValue(currentAggregate.netProfitPercentage)
            .mtdValue(mtdAggregates.stream().map(u -> u.getNetProfitPercentage() + "").collect(Collectors.toList()))
            .budget(AppUtils.percentage(calculateNetProfit(budget),totalSales(budget))+"")
            .budgetMtd(AppUtils.multiply(AppUtils.percentage(calculateNetProfit(budget),totalSales(budget)),proRata)+"").add()
            .key("19. Amortisation & Depreciation")
                .currentValue(currentAggregate.amortisationDepreciation)
                .mtdValue(mtdAggregates.stream().map(u -> u.getAmortisationDepreciation() + "").collect(Collectors.toList()))
                    .budget("").add()
            .key("20. FICO Payouts")
                .currentValue(currentAggregate.ficoPayouts)
                .mtdValue(mtdAggregates.stream().map(u -> u.getFicoPayouts() + "").collect(Collectors.toList()))
                .budget(ficoPayouts(budget)+"")
                .budgetMtd(AppUtils.percentage(ficoPayouts(budget),proRata)+"").add()
            .key("21. Growth PNL")
                .currentValue(currentAggregate.growthPnl)
                .mtdValue(mtdAggregates.stream().map(u -> u.getGrowthPnl() + "").collect(Collectors.toList()))
                .budget(growthPnl(budget)+"")
                .budgetMtd(AppUtils.multiply(growthPnl(budget),proRata)+"").add()
            .key("22. Growth Capex")
                .currentValue(currentAggregate.growthCapex)
                .mtdValue(mtdAggregates.stream().map(u -> u.getGrowthCapex() + "").collect(Collectors.toList()))
                .budget("").add()
            .key("23. Capex Fixed Assets")
                .currentValue(currentAggregate.capexFixedAssets)
                .mtdValue(mtdAggregates.stream().map(u -> u.getCapexFixedAssets() + "").collect(Collectors.toList()))
                .budget("").add()
            .key("24. Growth SD")
                .currentValue(currentAggregate.growthSd)
                .mtdValue(mtdAggregates.stream().map(u -> u.getGrowthSd() + "").collect(Collectors.toList()))
                .budget("").add()
            .key("25. Cash Burn")
                .currentValue(currentAggregate.cashBurn)
                .mtdValue(mtdAggregates.stream().map(u -> u.getCashBurn() + "").collect(Collectors.toList()))
                .budget("").add();

        return b.build();
    }
}