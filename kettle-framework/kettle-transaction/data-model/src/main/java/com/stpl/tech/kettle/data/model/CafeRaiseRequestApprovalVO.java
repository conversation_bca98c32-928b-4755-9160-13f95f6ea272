package com.stpl.tech.kettle.data.model;

import com.stpl.tech.util.excelparser.annotations.ExcelField;
import com.stpl.tech.util.excelparser.annotations.ExcelObject;
import com.stpl.tech.util.excelparser.annotations.ExcelSheet;
import com.stpl.tech.util.excelparser.annotations.ParseType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ExcelSheet(value = "Cafe Raise Request Approval Data")
@ExcelObject(parseType = ParseType.ROW, start = 1, end = 0)
public class CafeRaiseRequestApprovalVO implements Serializable {

    private static final long serialVersionUID = 5116841269273980431L;
    @ExcelField
    private Integer unitId;
    @ExcelField
    private String cafeName;
    @ExcelField
    private String partnerName;
    @ExcelField
    private String reason;
    @ExcelField
    private Date businessDate;
    @ExcelField
    private Date actionTimestamp;
    @ExcelField
    private String actionTaken;
    @ExcelField
    private Integer brandId;
    @ExcelField
    private String brandName;
    @ExcelField
    private String commentReason;
    @ExcelField
    private String autoSwitch;
    @ExcelField
    private Integer requestedByEmpId;
    @ExcelField
    private String requestedByEmpName;
    @ExcelField
    private Integer acceptedByEmpId;
    @ExcelField
    private String  acceptedByEmpName;

}

