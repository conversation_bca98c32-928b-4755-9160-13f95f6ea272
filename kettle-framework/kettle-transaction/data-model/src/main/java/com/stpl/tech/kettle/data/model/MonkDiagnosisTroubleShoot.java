/*
 * Created By Shanmukh
 */

package com.stpl.tech.kettle.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "MONK_DIAGNOSIS_TROUBLE_SHOOT")
public class MonkDiagnosisTroubleShoot {

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "MONK_DIAGNOSIS_TROUBLE_SHOOT_ID", unique = true, nullable = false)
    private Integer monkDiagnosisTroubleShootId;

    @Column(name = "MONK_DIAGNOSIS_EVENT_ID",nullable = false)
    private Integer monkDiagnosisEventId;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "DIAGNOSIS_AT",nullable = true)
    private Date diagnosisAt;

    @Column(name = "CODE",nullable = false)
    private Integer code;

    @Column(name = "CODE_MEANING",nullable = false)
    private String codeMeaning;

    @Column(name = "ACKNOWLEDGED",nullable = false)
    private String acknowledged;
}
