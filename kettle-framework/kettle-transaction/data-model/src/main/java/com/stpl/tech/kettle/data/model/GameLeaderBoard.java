package com.stpl.tech.kettle.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.kie.api.definition.rule.All;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(name = "GAME_LEADER_BOARD")
public class GameLeaderBoard {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "GAME_LEADER_BOARD_ID")
    private Integer gameLeaderBoardId;

    @Column(name = "CAMPAIGN_ID")
    private Integer campaignId;

    @Column(name = "CUSTOMER_ID")
    private Integer customerId;

    @Column(name = "USER_NAME")
    private String userName;

    @Column(name = "REF_CODE")
    private String refCode;

    @Column(name = "GAME_SCORE")
    private Integer gameScore;

    @Column(name = "REF_SCORE")
    private Integer refScore;

    @Column(name = "TOTAL_SCORE")
    private Integer totalScore;

    @Column(name = "GAME_PLAY_FREQUENCY")
    private Integer gamePlayFrequency;
}
