/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.offer.strategy;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

import com.stpl.tech.kettle.core.TransactionConstants;
import com.stpl.tech.kettle.core.notification.OfferOrder;
import com.stpl.tech.kettle.domain.model.DiscountDetail;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.PercentageDetail;
import com.stpl.tech.master.core.CouponMappingType;
import com.stpl.tech.master.core.WebErrorCode;
import com.stpl.tech.master.core.exception.OfferValidationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.OfferDetail;
import com.stpl.tech.master.domain.model.OfferMetaDataType;
import com.stpl.tech.master.domain.model.ProductBasicDetail;
import com.stpl.tech.master.util.BudgetCategoryConstants;
import org.apache.commons.lang3.StringUtils;

public abstract class AbstractItemStrategy {

	public Map<OfferMetaDataType, Set<String>> makeMapOfMappings(List<IdCodeName> metaDataMappings) {

		Map<OfferMetaDataType, Set<String>> mappingMap = new HashMap<OfferMetaDataType, Set<String>>();
		for (IdCodeName mapping : metaDataMappings) {
			if(!mapping.getName().equalsIgnoreCase(CouponMappingType.ACQUISITION_SOURCE.name())) {
				OfferMetaDataType mapKey = OfferMetaDataType.valueOf(mapping.getName());
					Set<String> listOfMappings = mappingMap.get(mapKey);
					if (listOfMappings == null) {
						listOfMappings = new HashSet<String>();
					}
					listOfMappings.add(mapping.getCode());
					mappingMap.put(mapKey, listOfMappings);
				}
		}
		return mappingMap;

	}

	public List<Integer> getApplicableProducts(CouponDetail coupon, OfferOrder order, MasterDataCache cache){
		OfferDetail offer = coupon.getOffer();

		Map<OfferMetaDataType, Set<String>> mappings = makeMapOfMappings(offer.getMetaDataMappings());

		//Set<OrderItem> orderItems = new HashSet<OrderItem>(order.getOrder().getOrders());
		Set<ProductBasicDetail> productDetails = new HashSet<ProductBasicDetail>();
        List<Integer> applicableProducts = new ArrayList<>();
		for (OrderItem item : order.getOrder().getOrders()) {
			int productId = item.getProductId();
			productDetails.add(cache.getProductBasicDetail(productId));
		}
		for (OfferMetaDataType mappingKey : mappings.keySet()) {
			Optional<List<Integer>> commonList = Optional
					.ofNullable(mappingKey.getCommonElements(mappings.get(mappingKey), productDetails));
            commonList.ifPresent(applicableProducts::addAll);
		}
		return applicableProducts;
	}

	public OfferOrder applyDiscountStrategy(CouponDetail coupon, OfferOrder order, MasterDataCache cache) throws OfferValidationException {

		OfferDetail offer = coupon.getOffer();

		Map<OfferMetaDataType, Set<String>> mappings = makeMapOfMappings(offer.getMetaDataMappings());
		Map<Integer, OrderItem> productDiscountMap = new HashMap<Integer, OrderItem>();

		//Set<OrderItem> orderItems = new HashSet<OrderItem>(order.getOrder().getOrders());
		Set<ProductBasicDetail> productDetails = new HashSet<ProductBasicDetail>();

		for (OrderItem item : order.getOrder().getOrders()) {
			int productId = item.getProductId();
			productDiscountMap.put(productId, item);
			productDetails.add(cache.getProductBasicDetail(productId));
		}
		AtomicBoolean isItemFounded = new AtomicBoolean(false);
		for (OfferMetaDataType mappingKey : mappings.keySet()) {
			Optional<List<Integer>> commonList = Optional
					.ofNullable(mappingKey.getCommonElements(mappings.get(mappingKey), productDetails));
			if (commonList.isPresent()) {
				List<Integer> commonElementList = commonList.get();
				commonElementList = sortCommonList(productDiscountMap, commonElementList);
				/*if (commonElementList.size() > offer.getMinQuantity()) {
					commonElementList = commonElementList.subList(0, offer.getMinQuantity());
				}*/

				AtomicInteger counter = new AtomicInteger(offer.getMinQuantity());

				if(!StringUtils.isEmpty(order.getApplyOnItemKey())){
					modifyAndAddDiscountDetails(commonElementList,order,counter,offer,coupon,true,isItemFounded);
				}
				else{
					modifyAndAddDiscountDetails(commonElementList,order,counter,offer,coupon,false,isItemFounded);
				}
				if(!isItemFounded.get()){
					modifyAndAddDiscountDetails(commonElementList,order,counter,offer,coupon,false,isItemFounded);
				}
			}

		}
		if(!isItemFounded.get() && BudgetCategoryConstants.LOYALTY_OFFER.equals(coupon.getOffer().getBudgetCategory())){
			throw new OfferValidationException("Eligible Item not Present in Cart", WebErrorCode.OFFER_PRODUCT_NOT_FOUND);
		}
		order.setAppliedOfferMessage(getOfferMessage());
		return order;
	}

	void addDiscountReason(OrderItem modifiedItem, CouponDetail coupon){
        modifiedItem.getDiscountDetail().setDiscountCode(TransactionConstants.MARKETING_VOUCHER_ID);
        modifiedItem.getDiscountDetail().setDiscountReason(coupon.getCode());
    }

	List<Integer> sortCommonList(Map<Integer, OrderItem> productDiscountMap, List<Integer> commonElementList) {

		commonElementList.sort(new Comparator<Integer>() {
			@Override
			public int compare(Integer o1, Integer o2) {
				return productDiscountMap.get(o1).getPrice().subtract(productDiscountMap.get(o2).getPrice()).intValue();
			}
		});

		return commonElementList;
	}

	public BigDecimal getTotalAmountOfItem(BigDecimal itemPrice, int count) {
		BigDecimal itemCount = new BigDecimal(count);
		BigDecimal totalAmountofItem = itemPrice.multiply(itemCount).setScale(2, BigDecimal.ROUND_HALF_UP);
		return totalAmountofItem;
	}

	public OrderItem getModifiedItem(OrderItem orderItem, BigDecimal percentageDiscount, BigDecimal valueDiscount) {
		DiscountDetail discountDetail = new DiscountDetail();
		PercentageDetail percentageDetail = new PercentageDetail();

		percentageDetail.setPercentage(BigDecimal.ZERO);
		percentageDetail.setValue(BigDecimal.ZERO);

		discountDetail.setPromotionalOffer(valueDiscount);
		discountDetail.setDiscount(percentageDetail);
		orderItem.setDiscountDetail(discountDetail);

		return orderItem;
	}

	public void modifyAndAddDiscountDetails(List<Integer> commonElementList,OfferOrder order, AtomicInteger counter,OfferDetail offer,
											CouponDetail coupon,boolean isSpecifiedItemKeyPresent,AtomicBoolean isItemFounded){
		for(Integer product : commonElementList){
			for (OrderItem item : order.getOrder().getOrders()) {
				if (counter.get() <= 0) {
					break;
				}
				if(isSpecifiedItemKeyPresent && (item.getProductId() == product) && !StringUtils.isEmpty(item.getItemKey())
						&& order.getApplyOnItemKey().equals(item.getItemKey())){
					getModifiedOrderItem(item,offer,counter,coupon,isItemFounded,order);
				}
				else if(!isSpecifiedItemKeyPresent && (item.getProductId() == product)) {
					getModifiedOrderItem(item,offer,counter,coupon,isItemFounded,order);
				}
			}
			if(counter.get() <= 0) {
				break;
			}
		}
	}

	public void getModifiedOrderItem(OrderItem item,OfferDetail offer,AtomicInteger counter,CouponDetail coupon,
									 AtomicBoolean isItemFounded,OfferOrder order){
		if (counter.get() > 0) {
			OrderItem modifiedItem = addDiscountDetails(item,
					BigDecimal.valueOf(offer.getOfferValue()), offer, counter.get());
			counter.set(counter.get() - modifiedItem.getQuantity());
			addDiscountReason(modifiedItem, coupon);
			isItemFounded.set(true);
			if(order.isSignupOffer()){
				modifiedItem.setLoyaltyBurnPoints(0);
			}
		}
	}

	public abstract OrderItem addDiscountDetails(OrderItem orderItem, BigDecimal discountValue, OfferDetail offer,int counter);

	public abstract String getOfferMessage();

}
