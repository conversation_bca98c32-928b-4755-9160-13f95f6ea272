/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.OrderBy;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * OrderInfo generated by hbm2java
 */
@Entity
@Table(name = "SUBSCRIPTION_DETAIL")
public class SubscriptionDetail implements java.io.Serializable {

	private static final long serialVersionUID = 5441681397850434848L;

	private Integer subscriptionId;
	private String generatedSubscriptionId;
	private CustomerInfo customerInfo;
	private int empId;
	private String subscriptionStatus;
	private String cancellationReason;
	private String settlementType;
	private int unitId;
	private Date subscriptionCreationTime;
	private Date cancellationTime;
	private int channelPartnerId;
	private Integer deliveryAddress;
	private String orderRemark;
	private BigDecimal totalAmount;
	private BigDecimal saleAmount;
	private BigDecimal promotionalDiscount;
	private BigDecimal totalDiscount;
	private BigDecimal taxableAmount;
	private BigDecimal discountPercent;
	private BigDecimal discountAmount;
	private Integer discountReasonId;
	private String discountReason;
	private String orderSourceId;
	private String offerCode;
	private String emailNotification;
	private String smsNotification;
	private BigDecimal taxAmount;
	private BigDecimal roundOffAmount;
	private BigDecimal settledAmount;
	private String automatedDelivery;
	private Integer cancelledBy;
	private List<SubscriptionItem> subscriptionItems = new ArrayList<SubscriptionItem>(0);
	private List<SubscriptionSettlement> subscriptionSettlements = new ArrayList<SubscriptionSettlement>(0);
	private Date startDate;
	private Date endDate;
	private Date lastUpdateTime;
	private String subscriptionType;// MONTHLY, WEEKLY
	private List<SubscriptionEventItem> eventItems = new ArrayList<SubscriptionEventItem>(0);
	private List<SubscriptionTaxDetail> subscriptionTaxes = new ArrayList<SubscriptionTaxDetail>(0);

	public SubscriptionDetail() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "SUBSCRIPTION_ID", unique = true, nullable = false)
	public Integer getSubscriptionId() {
		return this.subscriptionId;
	}

	public void setSubscriptionId(Integer subscriptionId) {
		this.subscriptionId = subscriptionId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "CUSTOMER_ID", nullable = false)
	public CustomerInfo getCustomerInfo() {
		return this.customerInfo;
	}

	public void setCustomerInfo(CustomerInfo customerId) {
		this.customerInfo = customerId;
	}

	@Column(name = "EMP_ID", nullable = false)
	public int getEmpId() {
		return this.empId;
	}

	public void setEmpId(int empId) {
		this.empId = empId;
	}

	@Column(name = "SUBSCRIPTION_STATUS", nullable = false, length = 30)
	public String getSubscriptionStatus() {
		return this.subscriptionStatus;
	}

	public void setSubscriptionStatus(String orderStatus) {
		this.subscriptionStatus = orderStatus;
	}

	@Column(name = "CANCELLATION_REASON", length = 100)
	public String getCancellationReason() {
		return this.cancellationReason;
	}

	public void setCancellationReason(String cancelationReason) {
		this.cancellationReason = cancelationReason;
	}

	@Column(name = "SETTLEMENT_TYPE", length = 10)
	public String getSettlementType() {
		return this.settlementType;
	}

	public void setSettlementType(String settlementType) {
		this.settlementType = settlementType;
	}

	@Column(name = "UNIT_ID", nullable = false)
	public int getUnitId() {
		return this.unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "SUBSCRIPTION_CREATION_TIME", nullable = false, length = 19)
	public Date getSubscriptionCreationTime() {
		return subscriptionCreationTime;
	}

	public void setSubscriptionCreationTime(Date subscriptionCreationTime) {
		this.subscriptionCreationTime = subscriptionCreationTime;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CANCELLATION_TIME", nullable = true, length = 19)
	public Date getCancellationTime() {
		return cancellationTime;
	}

	public void setCancellationTime(Date cancellationTime) {
		this.cancellationTime = cancellationTime;
	}

	@Column(name = "CHANNEL_PARTNER_ID", nullable = false)
	public int getChannelPartnerId() {
		return this.channelPartnerId;
	}

	public void setChannelPartnerId(int channelPartnerId) {
		this.channelPartnerId = channelPartnerId;
	}

	@Column(name = "TOTAL_AMOUNT", precision = 10)
	public BigDecimal getTotalAmount() {
		return this.totalAmount;
	}

	public void setTotalAmount(BigDecimal totalAmount) {
		this.totalAmount = totalAmount;
	}

	@Column(name = "TAXABLE_AMOUNT", precision = 10)
	public BigDecimal getTaxableAmount() {
		return this.taxableAmount;
	}

	public void setTaxableAmount(BigDecimal taxableAmount) {
		this.taxableAmount = taxableAmount;
	}

	@Column(name = "DISCOUNT_PERCENT", precision = 10)
	public BigDecimal getDiscountPercent() {
		return this.discountPercent;
	}

	public void setDiscountPercent(BigDecimal discountPercent) {
		this.discountPercent = discountPercent;
	}

	@Column(name = "DISCOUNT_AMOUNT", precision = 10)
	public BigDecimal getDiscountAmount() {
		return this.discountAmount;
	}

	public void setDiscountAmount(BigDecimal discountAmount) {
		this.discountAmount = discountAmount;
	}

	@Column(name = "DISCOUNT_REASON_ID")
	public Integer getDiscountReasonId() {
		return this.discountReasonId;
	}

	public void setDiscountReasonId(Integer discountReasonId) {
		this.discountReasonId = discountReasonId;
	}

	@Column(name = "DISCOUNT_REASON")
	public String getDiscountReason() {
		return this.discountReason;
	}

	public void setDiscountReason(String discountReason) {
		this.discountReason = discountReason;
	}

	@Column(name = "TOTAL_TAX", precision = 10)
	public BigDecimal getTaxAmount() {
		return taxAmount;
	}

	
	public void setTaxAmount(BigDecimal taxAmount) {
		this.taxAmount = taxAmount;
	}

	@Column(name = "ROUND_OFF_AMOUNT", precision = 10)
	public BigDecimal getRoundOffAmount() {
		return this.roundOffAmount;
	}

	public void setRoundOffAmount(BigDecimal roundOffAmount) {
		this.roundOffAmount = roundOffAmount;
	}

	@Column(name = "SETTLED_AMOUNT", precision = 10)
	public BigDecimal getSettledAmount() {
		return this.settledAmount;
	}

	public void setSettledAmount(BigDecimal settledAmount) {
		this.settledAmount = settledAmount;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "subscriptionDetail")
	public List<SubscriptionItem> getSubscriptionItems() {
		return this.subscriptionItems;
	}

	public void setSubscriptionItems(List<SubscriptionItem> orderItems) {
		this.subscriptionItems = orderItems;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "subscriptionDetail")
	public List<SubscriptionSettlement> getSubscriptionSettlements() {
		return this.subscriptionSettlements;
	}

	public void setSubscriptionSettlements(List<SubscriptionSettlement> orderSettlements) {
		this.subscriptionSettlements = orderSettlements;
	}

	@Column(name = "CANCELLED_BY", nullable = true)
	public Integer getCancelledBy() {
		return cancelledBy;
	}

	public void setCancelledBy(Integer cancelledBy) {
		this.cancelledBy = cancelledBy;
	}

	@Column(name = "ORDER_REMARK", nullable = true)
	public String getOrderRemark() {
		return orderRemark;
	}

	public void setOrderRemark(String orderRemark) {
		this.orderRemark = orderRemark;
	}

	@Column(name = "DELIVERY_ADDRESS", nullable = true)
	public Integer getDeliveryAddress() {
		return deliveryAddress;
	}

	public void setDeliveryAddress(Integer deliveryAddress) {
		this.deliveryAddress = deliveryAddress;
	}

	@Column(name = "ORDER_SOURCE_ID", nullable = true)
	public String getOrderSourceId() {
		return orderSourceId;
	}

	public void setOrderSourceId(String orderSourceId) {
		this.orderSourceId = orderSourceId;
	}

	@Column(name = "SALE_AMOUNT", precision = 10)
	public BigDecimal getSaleAmount() {
		return saleAmount;
	}

	public void setSaleAmount(BigDecimal saleAmount) {
		this.saleAmount = saleAmount;
	}

	@Column(name = "PROMOTIONAL_DISCOUNT", precision = 10)
	public BigDecimal getPromotionalDiscount() {
		return promotionalDiscount;
	}

	public void setPromotionalDiscount(BigDecimal promotionalDiscount) {
		this.promotionalDiscount = promotionalDiscount;
	}

	@Column(name = "TOTAL_DISCOUNT", precision = 10)
	public BigDecimal getTotalDiscount() {
		return totalDiscount;
	}

	public void setTotalDiscount(BigDecimal totalDiscount) {
		this.totalDiscount = totalDiscount;
	}

	@Column(name = "OFFER_CODE", nullable = true, length = 15)
	public String getOfferCode() {
		return offerCode;
	}

	public void setOfferCode(String offerCode) {
		this.offerCode = offerCode;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "START_DATE", nullable = false, length = 10)
	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "END_DATE", nullable = false, length = 10)
	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	@Column(name = "SUBSCRIPTION_TYPE", nullable = false, length = 10)
	public String getSubscriptionType() {
		return subscriptionType;
	}

	public void setSubscriptionType(String subscriptionType) {
		this.subscriptionType = subscriptionType;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "subscriptionDetail")
	@OrderBy("eventItemValue asc")
	public List<SubscriptionEventItem> getEventItems() {
		return eventItems;
	}

	public void setEventItems(List<SubscriptionEventItem> eventItems) {
		this.eventItems = eventItems;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LAST_UPDATE_TIME", nullable = true, length = 19)
	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

	@Column(name = "GENERATED_SUBSCRIPTION_ID", nullable = false, length = 30)
	public String getGeneratedSubscriptionId() {
		return generatedSubscriptionId;
	}

	public void setGeneratedSubscriptionId(String generatedSubscriptionId) {
		this.generatedSubscriptionId = generatedSubscriptionId;
	}

	@Column(name = "EMAIL_NOTIFICATION", nullable = false, length = 1)
	public String getEmailNotification() {
		return emailNotification;
	}

	public void setEmailNotification(String emailNotification) {
		this.emailNotification = emailNotification;
	}

	@Column(name = "SMS_NOTIFICATION", nullable = false, length = 1)
	public String getSmsNotification() {
		return smsNotification;
	}

	public void setSmsNotification(String smsNotification) {
		this.smsNotification = smsNotification;
	}

	@Column(name = "AUTOMATED_DELIVERY", nullable = false, length = 1)
	public String getAutomatedDelivery() {
		return automatedDelivery;
	}

	public void setAutomatedDelivery(String automatedDelivery) {
		this.automatedDelivery = automatedDelivery;
	}

	
	@OneToMany(fetch = FetchType.LAZY, mappedBy = "subscriptionDetail")
	public List<SubscriptionTaxDetail> getSubscriptionTaxes() {
		return subscriptionTaxes;
	}

	public void setSubscriptionTaxes(List<SubscriptionTaxDetail> subscriptionTaxes) {
		this.subscriptionTaxes = subscriptionTaxes;
	}

	
}
