package com.stpl.tech.kettle.core;

public enum NamedQueryDefinition {


	CUSTOMER_ONE_VIEW("SELECT \r\n"
			+ "    ci.CUSTOMER_ID customerId,\r\n"
			+ "    ci.FIRST_NAME firstName,\r\n"
			+ "    ci.CONTACT_NUMBER contactNumber,\r\n"
			+ "    ci.COUNTRY_CODE countryCode,\r\n"
			+ "    ci.EMAIL_ID emailId,\r\n"
			+ "    ci.REF_CODE refCode,\r\n"
			+ "    ci.IS_NUMBER_VERIFIED isNumberVerified,\r\n"
			+ "    ci.IS_EMAIL_VERIFIED isEmailVerified,\r\n"
			+ "    ci.IS_DND isDnd,\r\n"
			+ "    ci.IS_BLACKLISTED isBlacklisted,\r\n"
			+ "    COALESCE(SUM(CASE\r\n"
			+ "        WHEN\r\n"
			+ "           od.ORDER_ID IS NOT NULL AND   (IS_GIFT_CARD_ORDER IS NULL\r\n"
			+ "                OR IS_GIFT_CARD_ORDER = 'N')\r\n"
			+ "                AND BILLING_SERVER_TIME >= DATE_ADD(CURRENT_DATE(),\r\n"
			+ "                INTERVAL - 90 DAY)\r\n"
			+ "                AND od.ORDER_SOURCE = 'COD'\r\n"
			+ "        THEN\r\n"
			+ "            1\r\n"
			+ "        ELSE 0\r\n"
			+ "    END), 0) activeDeliveryOrders,\r\n"
			+ "    COALESCE(SUM(CASE\r\n"
			+ "        WHEN\r\n"
			+ "           od.ORDER_ID IS NOT NULL AND   (IS_GIFT_CARD_ORDER IS NULL\r\n"
			+ "                OR IS_GIFT_CARD_ORDER = 'N')\r\n"
			+ "                AND BILLING_SERVER_TIME >= DATE_ADD(CURRENT_DATE(),\r\n"
			+ "                INTERVAL - 90 DAY)\r\n"
			+ "        THEN\r\n"
			+ "            1\r\n"
			+ "        ELSE 0\r\n"
			+ "    END), 0) activeOverallOrders,\r\n"
			+ "    COALESCE(SUM(CASE\r\n"
			+ "        WHEN\r\n"
			+ "           od.ORDER_ID IS NOT NULL AND   (IS_GIFT_CARD_ORDER IS NULL\r\n"
			+ "                OR IS_GIFT_CARD_ORDER = 'N')\r\n"
			+ "                AND BILLING_SERVER_TIME >= DATE_ADD(CURRENT_DATE(),\r\n"
			+ "                INTERVAL - 90 DAY)\r\n"
			+ "                AND od.ORDER_SOURCE <> 'COD'\r\n"
			+ "        THEN\r\n"
			+ "            1\r\n"
			+ "        ELSE 0\r\n"
			+ "    END), 0) activeDineInOrders,\r\n"
			+ "    COALESCE(SUM(CASE\r\n"
			+ "        WHEN\r\n"
			+ "           od.ORDER_ID IS NOT NULL AND   (IS_GIFT_CARD_ORDER = 'Y')\r\n"
			+ "                AND BILLING_SERVER_TIME >= DATE_ADD(CURRENT_DATE(),\r\n"
			+ "                INTERVAL - 90 DAY)\r\n"
			+ "        THEN\r\n"
			+ "            1\r\n"
			+ "        ELSE 0\r\n"
			+ "    END), 0) activeWalletOrders,\r\n"
			+ "    COALESCE(SUM(CASE\r\n"
			+ "        WHEN\r\n"
			+ "           od.ORDER_ID IS NOT NULL AND   (IS_GIFT_CARD_ORDER IS NULL\r\n"
			+ "                OR IS_GIFT_CARD_ORDER = 'N')\r\n"
			+ "                AND BILLING_SERVER_TIME >= DATE_ADD(CURRENT_DATE(),\r\n"
			+ "                INTERVAL - 90 DAY)\r\n"
			+ "                AND od.ORDER_SOURCE = 'COD'\r\n"
			+ "        THEN\r\n"
			+ "            od.TAXABLE_AMOUNT\r\n"
			+ "        ELSE 0\r\n"
			+ "    END), 0) activeDeliverySales,\r\n"
			+ "    COALESCE(SUM(CASE\r\n"
			+ "        WHEN\r\n"
			+ "           od.ORDER_ID IS NOT NULL AND   (IS_GIFT_CARD_ORDER IS NULL\r\n"
			+ "                OR IS_GIFT_CARD_ORDER = 'N')\r\n"
			+ "                AND BILLING_SERVER_TIME >= DATE_ADD(CURRENT_DATE(),\r\n"
			+ "                INTERVAL - 90 DAY)\r\n"
			+ "                AND od.ORDER_SOURCE <> 'COD'\r\n"
			+ "        THEN\r\n"
			+ "            od.TAXABLE_AMOUNT\r\n"
			+ "        ELSE 0\r\n"
			+ "    END), 0) activeDineInSales,\r\n"
			+ "    COALESCE(SUM(CASE\r\n"
			+ "        WHEN\r\n"
			+ "           od.ORDER_ID IS NOT NULL AND   (IS_GIFT_CARD_ORDER IS NULL\r\n"
			+ "                OR IS_GIFT_CARD_ORDER = 'N')\r\n"
			+ "                AND BILLING_SERVER_TIME >= DATE_ADD(CURRENT_DATE(),\r\n"
			+ "                INTERVAL - 90 DAY)\r\n"
			+ "        THEN\r\n"
			+ "            od.TAXABLE_AMOUNT\r\n"
			+ "        ELSE 0\r\n"
			+ "    END), 0) activeOverallSales,\r\n"
			+ "    COALESCE(SUM(CASE\r\n"
			+ "        WHEN\r\n"
			+ "           od.ORDER_ID IS NOT NULL AND   (IS_GIFT_CARD_ORDER = 'Y')\r\n"
			+ "                AND BILLING_SERVER_TIME >= DATE_ADD(CURRENT_DATE(),\r\n"
			+ "                INTERVAL - 90 DAY)\r\n"
			+ "        THEN\r\n"
			+ "            od.TAXABLE_AMOUNT\r\n"
			+ "        ELSE 0\r\n"
			+ "    END), 0) activeWalletSales,\r\n"
			+ "    COALESCE(SUM(CASE\r\n"
			+ "        WHEN\r\n"
			+ "           od.ORDER_ID IS NOT NULL AND   (IS_GIFT_CARD_ORDER IS NULL\r\n"
			+ "                OR IS_GIFT_CARD_ORDER = 'N')\r\n"
			+ "                AND BILLING_SERVER_TIME >= DATE_ADD(CURRENT_DATE(),\r\n"
			+ "                INTERVAL - 90 DAY)\r\n"
			+ "                AND od.ORDER_SOURCE = 'COD'\r\n"
			+ "        THEN\r\n"
			+ "            od.TOTAL_AMOUNT\r\n"
			+ "        ELSE 0\r\n"
			+ "    END), 0) activeDeliveryGmv,\r\n"
			+ "    COALESCE(SUM(CASE\r\n"
			+ "        WHEN\r\n"
			+ "           od.ORDER_ID IS NOT NULL AND   (IS_GIFT_CARD_ORDER IS NULL\r\n"
			+ "                OR IS_GIFT_CARD_ORDER = 'N')\r\n"
			+ "                AND BILLING_SERVER_TIME >= DATE_ADD(CURRENT_DATE(),\r\n"
			+ "                INTERVAL - 90 DAY)\r\n"
			+ "                AND od.ORDER_SOURCE <> 'COD'\r\n"
			+ "        THEN\r\n"
			+ "            od.TOTAL_AMOUNT\r\n"
			+ "        ELSE 0\r\n"
			+ "    END), 0) activeDineInGmv,\r\n"
			+ "    COALESCE(SUM(CASE\r\n"
			+ "        WHEN\r\n"
			+ "           od.ORDER_ID IS NOT NULL AND   (IS_GIFT_CARD_ORDER IS NULL\r\n"
			+ "                OR IS_GIFT_CARD_ORDER = 'N')\r\n"
			+ "                AND BILLING_SERVER_TIME >= DATE_ADD(CURRENT_DATE(),\r\n"
			+ "                INTERVAL - 90 DAY)\r\n"
			+ "        THEN\r\n"
			+ "            od.TOTAL_AMOUNT\r\n"
			+ "        ELSE 0\r\n"
			+ "    END), 0) activeOverallGmv,\r\n"
			+ "    COALESCE(SUM(CASE\r\n"
			+ "        WHEN\r\n"
			+ "           od.ORDER_ID IS NOT NULL AND   (IS_GIFT_CARD_ORDER IS NULL\r\n"
			+ "                OR IS_GIFT_CARD_ORDER = 'N')\r\n"
			+ "                AND od.ORDER_SOURCE = 'COD'\r\n"
			+ "        THEN\r\n"
			+ "            1\r\n"
			+ "        ELSE 0\r\n"
			+ "    END) , 0)deliveryOrders,\r\n"
			+ "    COALESCE(SUM(CASE\r\n"
			+ "        WHEN\r\n"
			+ "           od.ORDER_ID IS NOT NULL AND   (IS_GIFT_CARD_ORDER IS NULL\r\n"
			+ "                OR IS_GIFT_CARD_ORDER = 'N')\r\n"
			+ "                AND od.ORDER_SOURCE <> 'COD'\r\n"
			+ "        THEN\r\n"
			+ "            1\r\n"
			+ "        ELSE 0\r\n"
			+ "    END), 0) dineInOrders,\r\n"
			+ "    COALESCE(SUM(CASE\r\n"
			+ "        WHEN od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER = 'Y') THEN 1\r\n"
			+ "        ELSE 0\r\n"
			+ "    END), 0) walletOrders,\r\n"
			+ "    COALESCE(SUM(CASE\r\n"
			+ "        WHEN\r\n"
			+ "            od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER IS NULL\r\n"
			+ "                OR IS_GIFT_CARD_ORDER = 'N')\r\n"
			+ "        THEN\r\n"
			+ "            1\r\n"
			+ "        ELSE 0\r\n"
			+ "    END), 0) overallOrders,\r\n"
			+ "    COALESCE(SUM(CASE\r\n"
			+ "        WHEN\r\n"
			+ "            od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER IS NULL\r\n"
			+ "                OR IS_GIFT_CARD_ORDER = 'N')\r\n"
			+ "                AND od.ORDER_SOURCE = 'COD'\r\n"
			+ "        THEN\r\n"
			+ "            od.TAXABLE_AMOUNT\r\n"
			+ "        ELSE 0\r\n"
			+ "    END), 0) deliverySales,\r\n"
			+ "    COALESCE(SUM(CASE\r\n"
			+ "        WHEN\r\n"
			+ "            od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER IS NULL\r\n"
			+ "                OR IS_GIFT_CARD_ORDER = 'N')\r\n"
			+ "                AND od.ORDER_SOURCE <> 'COD'\r\n"
			+ "        THEN\r\n"
			+ "            od.TAXABLE_AMOUNT\r\n"
			+ "        ELSE 0\r\n"
			+ "    END), 0) dineInSales,\r\n"
			+ "    COALESCE(SUM(CASE\r\n"
			+ "        WHEN\r\n"
			+ "            od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER IS NULL\r\n"
			+ "                OR IS_GIFT_CARD_ORDER = 'N')\r\n"
			+ "        THEN\r\n"
			+ "            od.TAXABLE_AMOUNT\r\n"
			+ "        ELSE 0\r\n"
			+ "    END), 0) overallSales,\r\n"
			+ "    COALESCE(SUM(CASE\r\n"
			+ "        WHEN od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER = 'Y') THEN od.TAXABLE_AMOUNT\r\n"
			+ "        ELSE 0\r\n"
			+ "    END), 0) walletSales,\r\n"
			+ "    COALESCE(SUM(CASE\r\n"
			+ "        WHEN\r\n"
			+ "            od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER IS NULL\r\n"
			+ "                OR IS_GIFT_CARD_ORDER = 'N')\r\n"
			+ "                AND od.ORDER_SOURCE = 'COD'\r\n"
			+ "        THEN\r\n"
			+ "            od.TOTAL_AMOUNT\r\n"
			+ "        ELSE 0\r\n"
			+ "    END), 0) deliveryGmv,\r\n"
			+ "    COALESCE(SUM(CASE\r\n"
			+ "        WHEN\r\n"
			+ "            od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER IS NULL\r\n"
			+ "                OR IS_GIFT_CARD_ORDER = 'N')\r\n"
			+ "                AND od.ORDER_SOURCE <> 'COD'\r\n"
			+ "        THEN\r\n"
			+ "            od.TOTAL_AMOUNT\r\n"
			+ "        ELSE 0\r\n"
			+ "    END), 0) dineInGmv,\r\n"
			+ "    COALESCE(SUM(CASE\r\n"
			+ "        WHEN\r\n"
			+ "            od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER IS NULL\r\n"
			+ "                OR IS_GIFT_CARD_ORDER = 'N')\r\n"
			+ "        THEN\r\n"
			+ "            od.TOTAL_AMOUNT\r\n"
			+ "        ELSE 0\r\n"
			+ "    END), 0) overallGmv,\r\n"
			+ "    COALESCE(MAX(CASE\r\n"
			+ "        WHEN\r\n"
			+ "            od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER IS NULL\r\n"
			+ "                OR IS_GIFT_CARD_ORDER = 'N')\r\n"
			+ "        THEN\r\n"
			+ "            od.ORDER_ID\r\n"
			+ "        ELSE 0\r\n"
			+ "    END), 0) lastOrderId,\r\n"
			+ "    COALESCE(MAX(CASE\r\n"
			+ "        WHEN\r\n"
			+ "            od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER IS NULL\r\n"
			+ "                OR IS_GIFT_CARD_ORDER = 'N')\r\n"
			+ "                AND od.ORDER_SOURCE <> 'COD'\r\n"
			+ "        THEN\r\n"
			+ "            od.ORDER_ID\r\n"
			+ "        ELSE 0\r\n"
			+ "    END), 0) lastDineOrderId,\r\n"
			+ "    COALESCE(MAX(CASE\r\n"
			+ "        WHEN\r\n"
			+ "            od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER IS NULL\r\n"
			+ "                OR IS_GIFT_CARD_ORDER = 'N')\r\n"
			+ "                AND od.ORDER_SOURCE = 'COD'\r\n"
			+ "        THEN\r\n"
			+ "            od.ORDER_ID\r\n"
			+ "        ELSE 0\r\n"
			+ "    END), 0) lastDeliveryOrderId,\r\n"
			+ "    COALESCE(MAX(CASE\r\n"
			+ "        WHEN od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER = 'Y') THEN od.ORDER_ID\r\n"
			+ "        ELSE 0\r\n"
			+ "    END), 0) lastWalletOrderId,\r\n"
			+ "    MAX(CASE\r\n"
			+ "        WHEN\r\n"
			+ "            od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER IS NULL\r\n"
			+ "                OR IS_GIFT_CARD_ORDER = 'N')\r\n"
			+ "        THEN\r\n"
			+ "            od.BILLING_SERVER_TIME\r\n"
			+ "        ELSE NULL\r\n"
			+ "    END) lastOrderTime,\r\n"
			+ "    MAX(CASE\r\n"
			+ "        WHEN\r\n"
			+ "            od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER IS NULL\r\n"
			+ "                OR IS_GIFT_CARD_ORDER = 'N')\r\n"
			+ "                AND od.ORDER_SOURCE <> 'COD'\r\n"
			+ "        THEN\r\n"
			+ "            od.BILLING_SERVER_TIME\r\n"
			+ "        ELSE NULL\r\n"
			+ "    END) lastDineInOrderTime,\r\n"
			+ "    MAX(CASE\r\n"
			+ "        WHEN\r\n"
			+ "            od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER IS NULL\r\n"
			+ "                OR IS_GIFT_CARD_ORDER = 'N')\r\n"
			+ "                AND od.ORDER_SOURCE = 'COD'\r\n"
			+ "        THEN\r\n"
			+ "            od.BILLING_SERVER_TIME\r\n"
			+ "        ELSE NULL\r\n"
			+ "    END) lastDeliveryOrderTime,\r\n"
			+ "    MAX(CASE\r\n"
			+ "        WHEN od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER = 'Y') THEN od.BILLING_SERVER_TIME\r\n"
			+ "        ELSE NULL \r\n"
			+ "    END) lastWalletOrderTime,\r\n"
			+ "    COALESCE(MAX(ls.AVAILED_SIGNUP_OFFER), 'Y') availedSignupOffer,\r\n"
			+ "    MAX(ls.SIGNUP_OFFER_EXPIRY_TIME) signupOfferExpiryTime, \r\n"
			+ "    COALESCE(MAX(ls.ACQUIRED_POINTS), 0) acquiredLoyaltyPoints,\r\n"
			+ "    COALESCE(MAX(ls.CUMULATIVE_POINTS), 0) cumulativeLoyaltyPoints\r\n"
			+ "    \r\n"
			+ " FROM\r\n"
			+ "    CUSTOMER_INFO ci\r\n"
			+ "        INNER JOIN\r\n"
			+ "    LOYALTY_SCORE ls ON ls.CUSTOMER_ID = ci.CUSTOMER_ID\r\n"
			+ "        LEFT OUTER JOIN\r\n"
			+ "    ORDER_DETAIL od ON od.CUSTOMER_ID = ci.CUSTOMER_ID\r\n"
			+ "        AND od.ORDER_STATUS <> 'CANCELLED'\r\n"
			+ "        AND od.BRAND_ID = :brandId "
			+ "        AND od.ORDER_TYPE = 'order' AND od.ORDER_ID NOT IN (:excludeOrderIds) \r\n"
			+ " WHERE\r\n"
			+ "    ci.CUSTOMER_ID = :customerId "
			+ "    group by ci.CUSTOMER_ID\r\n"
			+ ""),
	CUSTOMER_DINE_IN_VIEW("SELECT \r\n"
			+ "    ci.CUSTOMER_ID customerId,\r\n"
			+ "    COALESCE(SUM(CASE\r\n"
			+ "        WHEN\r\n"
			+ "           od.ORDER_ID IS NOT NULL AND   (IS_GIFT_CARD_ORDER IS NULL\r\n"
			+ "                OR IS_GIFT_CARD_ORDER = 'N')\r\n"
			+ "                AND BILLING_SERVER_TIME >= DATE_ADD(CURRENT_DATE(),\r\n"
			+ "                INTERVAL - 90 DAY)\r\n"
			+ "                AND od.ORDER_SOURCE <> 'COD'\r\n"
			+ "        THEN\r\n"
			+ "            1\r\n"
			+ "        ELSE 0\r\n"
			+ "    END), 0) activeDineInOrders,\r\n"
			+ "    COALESCE(SUM(CASE\r\n"
			+ "        WHEN\r\n"
			+ "           od.ORDER_ID IS NOT NULL AND   (IS_GIFT_CARD_ORDER IS NULL\r\n"
			+ "                OR IS_GIFT_CARD_ORDER = 'N')\r\n"
			+ "                AND od.ORDER_SOURCE <> 'COD'\r\n"
			+ "        THEN\r\n"
			+ "            1\r\n"
			+ "        ELSE 0\r\n"
			+ "    END), 0) dineInOrders,\r\n"
			+ "    COALESCE(MAX(ls.AVAILED_SIGNUP_OFFER), 'Y') availedSignupOffer,\r\n"
			+ "    MAX(ls.SIGNUP_OFFER_EXPIRY_TIME) signupOfferExpiryTime, \r\n"
			+ "     (ls.LAST_ORDER_TIME) lastOrderTime \r\n"
			+ "    \r\n"
			+ " FROM\r\n"
			+ "    CUSTOMER_INFO ci\r\n"
			+ "        INNER JOIN\r\n"
			+ "    LOYALTY_SCORE ls ON ls.CUSTOMER_ID = ci.CUSTOMER_ID\r\n"
			+ "        LEFT OUTER JOIN\r\n"
			+ "    ORDER_DETAIL od ON od.CUSTOMER_ID = ci.CUSTOMER_ID\r\n"
			+ "        AND od.ORDER_STATUS <> 'CANCELLED'\r\n"
			+ "        AND od.BRAND_ID = :brandId "
			+ "        AND od.ORDER_TYPE = 'order' AND od.ORDER_ID NOT IN (:excludeOrderIds) \r\n"
			+ " WHERE\r\n"
			+ "    ci.CUSTOMER_ID = :customerId "
			+ "    group by ci.CUSTOMER_ID\r\n"
			+ ""),
	CUSTOMER_TRANSACTION_VIEW("SELECT \r\n"
			+ "    ci.CUSTOMER_ID customerId,\r\n"
			+ "    COALESCE(MIN(CASE\r\n"
			+ "        WHEN\r\n"
			+ "            od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER IS NULL\r\n"
			+ "                OR IS_GIFT_CARD_ORDER = 'N')\r\n"
			+ "        THEN\r\n"
			+ "            od.ORDER_ID\r\n"
			+ "        ELSE 0\r\n"
			+ "    END), 0) firstOrderId,\r\n"
			+ "    MIN(CASE\r\n"
			+ "        WHEN\r\n"
			+ "            od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER IS NULL\r\n"
			+ "                OR IS_GIFT_CARD_ORDER = 'N')\r\n"
			+ "        THEN\r\n"
			+ "            od.ORDER_SOURCE\r\n"
			+ "        ELSE NULL\r\n"
			+ "    END) firstOrderSource,\r\n"
			+ "    MIN(CASE\r\n"
			+ "        WHEN\r\n"
			+ "            od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER IS NULL\r\n"
			+ "                OR IS_GIFT_CARD_ORDER = 'N')\r\n"
			+ "        THEN\r\n"
			+ "            od.CHANNEL_PARTNER_ID\r\n"
			+ "        ELSE NULL\r\n"
			+ "    END) firstOrderChannelPartnerId,\r\n"
			+ "    MIN(CASE\r\n"
			+ "        WHEN\r\n"
			+ "            od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER IS NULL\r\n"
			+ "                OR IS_GIFT_CARD_ORDER = 'N')\r\n"
			+ "        THEN\r\n"
			+ "            od.BUSINESS_DATE\r\n"
			+ "        ELSE NULL\r\n"
			+ "    END) firstOrderBusinessDate,\r\n"
			+ "    MIN(CASE\r\n"
			+ "        WHEN\r\n"
			+ "            od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER IS NULL\r\n"
			+ "                OR IS_GIFT_CARD_ORDER = 'N')\r\n"
			+ "        THEN\r\n"
			+ "            od.UNIT_ID\r\n"
			+ "        ELSE NULL\r\n"
			+ "    END) firstOrderUnitId,\r\n"
			+ "    COALESCE(MAX(CASE\r\n"
			+ "        WHEN\r\n"
			+ "            od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER IS NULL\r\n"
			+ "                OR IS_GIFT_CARD_ORDER = 'N')\r\n"
			+ "        THEN\r\n"
			+ "            od.ORDER_ID\r\n"
			+ "        ELSE 0\r\n"
			+ "    END), 0) lastOrderId,\r\n"
			+ "    MAX(CASE\r\n"
			+ "        WHEN\r\n"
			+ "            od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER IS NULL\r\n"
			+ "                OR IS_GIFT_CARD_ORDER = 'N')\r\n"
			+ "        THEN\r\n"
			+ "            od.ORDER_SOURCE\r\n"
			+ "        ELSE NULL\r\n"
			+ "    END) lastOrderSource,\r\n"
			+ "    MAX(CASE\r\n"
			+ "        WHEN\r\n"
			+ "            od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER IS NULL\r\n"
			+ "                OR IS_GIFT_CARD_ORDER = 'N')\r\n"
			+ "        THEN\r\n"
			+ "            od.CHANNEL_PARTNER_ID\r\n"
			+ "        ELSE NULL\r\n"
			+ "    END) lastOrderChannelPartnerId,\r\n"
			+ "    MAX(CASE\r\n"
			+ "        WHEN\r\n"
			+ "            od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER IS NULL\r\n"
			+ "                OR IS_GIFT_CARD_ORDER = 'N')\r\n"
			+ "        THEN\r\n"
			+ "            od.BUSINESS_DATE\r\n"
			+ "        ELSE NULL\r\n"
			+ "    END) lastOrderBusinessDate,\r\n"
			+ "    MAX(CASE\r\n"
			+ "        WHEN\r\n"
			+ "            od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER IS NULL\r\n"
			+ "                OR IS_GIFT_CARD_ORDER = 'N')\r\n"
			+ "        THEN\r\n"
			+ "            od.UNIT_ID\r\n"
			+ "        ELSE NULL\r\n"
			+ "    END) lastOrderUnitId,\r\n"
			+ "    COALESCE(SUM(CASE\r\n"
			+ "        WHEN\r\n"
			+ "           od.ORDER_ID IS NOT NULL AND   (IS_GIFT_CARD_ORDER IS NULL\r\n"
			+ "                OR IS_GIFT_CARD_ORDER = 'N')\r\n"
			+ "                AND od.ORDER_SOURCE <> 'COD'\r\n"
			+ "        THEN\r\n"
			+ "            1\r\n"
			+ "        ELSE 0\r\n"
			+ "    END), 0) totalDineInOrders,\r\n"
			+ "    COALESCE(SUM(CASE\r\n"
			+ "        WHEN\r\n"
			+ "           od.ORDER_ID IS NOT NULL AND   (IS_GIFT_CARD_ORDER IS NULL\r\n"
			+ "                OR IS_GIFT_CARD_ORDER = 'N')\r\n"
			+ "                AND od.ORDER_SOURCE = 'COD'\r\n"
			+ "        THEN\r\n"
			+ "            1\r\n"
			+ "        ELSE 0\r\n"
			+ "    END) , 0) totalDeliveryOrders,\r\n"
			+ "    COALESCE(SUM(CASE\r\n"
			+ "        WHEN\r\n"
			+ "            od.ORDER_ID IS NOT NULL AND  (IS_GIFT_CARD_ORDER IS NULL\r\n"
			+ "                OR IS_GIFT_CARD_ORDER = 'N')\r\n"
			+ "        THEN\r\n"
			+ "            1\r\n"
			+ "        ELSE 0\r\n"
			+ "    END), 0) totalOrders\r\n"
			+ "    \r\n"
			+ " FROM\r\n"
			+ "    CUSTOMER_INFO ci\r\n"
			+ "        LEFT OUTER JOIN\r\n"
			+ "    ORDER_DETAIL od ON od.CUSTOMER_ID = ci.CUSTOMER_ID\r\n"
			+ "        AND od.ORDER_STATUS <> 'CANCELLED'\r\n"
			+ "        AND od.BRAND_ID = :brandId "
			+ "        AND od.IS_GIFT_CARD_ORDER <> 'Y'\r\n"
			+ "        AND od.ORDER_TYPE = 'order' AND od.ORDER_ID NOT IN (:excludeOrderIds) \r\n"
			+ " WHERE\r\n"
			+ "    ci.CUSTOMER_ID = :customerId "
			+ "    group by ci.CUSTOMER_ID\r\n"
			+ ""),
	CUSTOMER_EMAIL_DATA("SELECT \r\n"
			+ "    a.CUSTOMER_ID customerId,\r\n"
			+ "    a.overallOrders overallOrders,\r\n"
			+ "    a.overallVisits,\r\n"
			+ "    a.overallSavings,\r\n"
			+ "    a.availedSignupOffer,\r\n"
			+ "    a.signupOfferExpiryTime,\r\n"
			+ "    a.acquiredLoyaltyPoints,\r\n"
			+ "    COALESCE(b.CARD_PENDING_AMOUNT, 0) walletBalance,\r\n"
			+ "    COALESCE(c.CURRENT_AMOUNT, 0) chaayosCashBalance,\r\n"
			+ "    a.membershipAvailable,\r\n"
			+ "    a.membershipPlan,\r\n"
			+ "    a.membershipEndDate\r\n"
			+ "FROM\r\n"
			+ "    (SELECT \r\n"
			+ "        ci.CUSTOMER_ID CUSTOMER_ID,\r\n"
			+ "            COALESCE(SUM(CASE\r\n"
			+ "                WHEN\r\n"
			+ "                    od.ORDER_ID IS NOT NULL\r\n"
			+ "                        AND (IS_GIFT_CARD_ORDER IS NULL\r\n"
			+ "                        OR IS_GIFT_CARD_ORDER = 'N')\r\n"
			+ "                THEN\r\n"
			+ "                    1\r\n"
			+ "                ELSE 0\r\n"
			+ "            END), 0) overallOrders,\r\n"
			+ "            COALESCE(SUM(CASE\r\n"
			+ "                WHEN\r\n"
			+ "                    od.ORDER_ID IS NOT NULL\r\n"
			+ "                        AND (IS_GIFT_CARD_ORDER IS NULL\r\n"
			+ "                        OR IS_GIFT_CARD_ORDER = 'N')\r\n"
			+ "                THEN\r\n"
			+ "                    od.TOTAL_AMOUNT - od.TAXABLE_AMOUNT\r\n"
			+ "                ELSE 0\r\n"
			+ "            END), 0) overallSavings,\r\n"
			+ "            COUNT(DISTINCT CASE\r\n"
			+ "                WHEN\r\n"
			+ "                    od.ORDER_ID IS NOT NULL\r\n"
			+ "                        AND (IS_GIFT_CARD_ORDER IS NULL\r\n"
			+ "                        OR IS_GIFT_CARD_ORDER = 'N')\r\n"
			+ "                THEN\r\n"
			+ "                    od.BUSINESS_DATE\r\n"
			+ "                ELSE 0\r\n"
			+ "            END) overallVisits,\r\n"
			+ "            COALESCE(MAX(ls.AVAILED_SIGNUP_OFFER), 'Y') availedSignupOffer,\r\n"
			+ "            MAX(ls.SIGNUP_OFFER_EXPIRY_TIME) signupOfferExpiryTime,\r\n"
			+ "            COALESCE(MAX(ls.ACQUIRED_POINTS), 0) acquiredLoyaltyPoints,\r\n"
			+ "            COALESCE(MAX(ls.CUMULATIVE_POINTS), 0) cumulativeLoyaltyPoints,\r\n"
			+ "            (case when sp.SUBSCRIPTION_PLAN_ID IS NULL THEN 'N' ELSE 'Y' END) membershipAvailable,\r\n"
			+ "            (case when sp.SUBSCRIPTION_PLAN_ID IS NULL THEN NULL ELSE sp.SUBSCRIPTION_PLAN_CODE END) membershipPlan,\r\n"
			+ "            (case when sp.SUBSCRIPTION_PLAN_ID IS NULL THEN NULL ELSE sp.PLAN_END_DATE END) membershipEndDate\r\n"
			+ "    FROM\r\n"
			+ "        CUSTOMER_INFO ci\r\n"
			+ "    INNER JOIN LOYALTY_SCORE ls ON ls.CUSTOMER_ID = ci.CUSTOMER_ID\r\n"
			+ "    LEFT OUTER JOIN ORDER_DETAIL od ON od.CUSTOMER_ID = ci.CUSTOMER_ID\r\n"
			+ "        AND od.ORDER_STATUS <> 'CANCELLED'\r\n"
			+ "        AND od.BRAND_ID = :brandId "
			+ "        AND od.ORDER_TYPE = 'order' \r\n"
			+ "   LEFT OUTER JOIN SUBSCRIPTION_PLAN sp ON ci.CUSTOMER_ID = sp.CUSTOMER_ID \r\n"
			+ "   	   AND sp.PLAN_STATUS  = 'ACTIVE'  \r\n"
			+ "        AND sp.PLAN_START_DATE <= :currentDate "
			+ "        AND sp.PLAN_END_DATE >= :currentDate  "
			+ "    WHERE\r\n"
			+ "        ci.CUSTOMER_ID = :customerId "
			+ "    GROUP BY ci.CUSTOMER_ID) a\r\n"
			+ "        LEFT OUTER JOIN\r\n"
			+ "    (SELECT \r\n"
			+ "        ls.CUSTOMER_ID,\r\n"
			+ "            SUM(ls.CARD_PENDING_AMOUNT) CARD_PENDING_AMOUNT\r\n"
			+ "    FROM\r\n"
			+ "        CASH_CARD_DETAIL ls\r\n"
			+ "    WHERE\r\n"
			+ "        ls.CUSTOMER_ID = :customerId "
			+ "            AND ls.CARD_STATUS = 'ACTIVE'\r\n"
			+ "    GROUP BY ls.CUSTOMER_ID) b ON a.CUSTOMER_ID = b.CUSTOMER_ID\r\n"
			+ "        LEFT OUTER JOIN\r\n"
			+ "    (SELECT \r\n"
			+ "        ls.CUSTOMER_ID, ls.CURRENT_AMOUNT\r\n"
			+ "    FROM\r\n"
			+ "        CASH_DATA ls\r\n"
			+ "    WHERE\r\n"
			+ "        ls.CUSTOMER_ID = :customerId) c ON a.CUSTOMER_ID = c.CUSTOMER_ID"),
	SUBSCRIPTION_NOT_USED_NTH_DAY("SELECT \n" +
			"    a.SUBSCRIPTION_PLAN_ID subscriptionId,\n" +
			"    a.CUSTOMER_ID customerId,\n" +
			"    a.PLAN_START_DATE planStartDate,\n" +
			"    a.PLAN_END_DATE planEndDate,\n" +
			"    a.SUBSCRIPTION_PLAN_CODE subscriptionPlanCode,\n" +
			"    a.OVERALL_SAVING totalSaving \n" +
			"FROM\n" +
			"    (SELECT \n" +
			"        sp.SUBSCRIPTION_PLAN_ID,\n" +
			"            sp.CUSTOMER_ID,\n" +
			"            sp.PLAN_START_DATE,\n" +
			"            sp.PLAN_END_DATE,\n" +
			"            sp.SUBSCRIPTION_PLAN_CODE,\n" +
			"            sp.OVERALL_SAVING,\n" +
			"            MAX(sp.RENEWAL_TIME) RENEWAL_TIME,\n" +
			"            DATE_ADD(DATE(sp.RENEWAL_TIME), INTERVAL 1770 MINUTE) ORDERED_AFTER,\n" +
			"            MAX(od.BILLING_SERVER_TIME) LAST_ORDER_TIME\n" +
			"    FROM\n" +
			"        SUBSCRIPTION_PLAN sp\n" +
			"    LEFT OUTER JOIN ORDER_DETAIL od ON sp.SUBSCRIPTION_PLAN_CODE = od.OFFER_CODE\n" +
			"        AND sp.CUSTOMER_ID = od.CUSTOMER_ID\n" +
			"        AND od.BILLING_SERVER_TIME > DATE_ADD(DATE(sp.RENEWAL_TIME), INTERVAL 1770 MINUTE)\n" +
			"    WHERE\n" +
			"        sp.RENEWAL_TIME BETWEEN DATE_ADD(:businessDate, INTERVAL - :nthDay DAY) AND DATE_ADD(:businessDate, INTERVAL - :mthDay DAY)\n" +
			"            AND sp.PLAN_END_DATE >= :businessDate\n" +
			"    GROUP BY sp.CUSTOMER_ID) a\n" +
			"WHERE\n" +
			"    a.LAST_ORDER_TIME IS NULL"),
		ORDER_FREQUENCY_FOR_OFFER("SELECT\n" +
				"    od.CUSTOMER_ID customerId,\n" +
				"    MAX(BILLING_SERVER_TIME) lastOrderTime,\n" +
				"    SUM(CASE\n" +
				"        WHEN\n" +
				"            od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE\n" +
				"                    WHEN\n" +
				"                        HOUR(ADDTIME(UTC_TIMESTAMP, '05:30:00')) <= 5\n" +
				"                    THEN\n" +
				"                        SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')),\n" +
				"                            1)\n" +
				"                    ELSE CURRENT_DATE\n" +
				"                END),\n" +
				"                INTERVAL 5 HOUR) AND ADDTIME(UTC_TIMESTAMP, '05:30:00')\n" +
				"        THEN\n" +
				"            1\n" +
				"        ELSE 0\n" +
				"    END) orderToday,\n" +
				"    SUM(CASE\n" +
				"        WHEN\n" +
				"            od.BILLING_SERVER_TIME > :lastTimestamp \n" +
				"        THEN\n" +
				"            1\n" +
				"        ELSE 0\n" +
				"    END) orderInLastHour\n" +
				"FROM\n" +
				"    ORDER_DETAIL od\n" +
				"WHERE\n" +
				"    od.CUSTOMER_ID = :customer\n" +
				"    and od.OFFER_CODE = :code\n" +
				"    and (od.BUSINESS_DATE IS NULL OR od.BUSINESS_DATE > :businessDate)\n" +
				"    and od.ORDER_STATUS <> 'CANCELLED'\n" +
				"GROUP BY od.CUSTOMER_ID"),
	CUSTOMER_OFFER_FOR_REMINDER("SELECT \n" +
			"    c.CUSTOMER_CAMPIAGN_OFFER_DETAIL_ID capmpaignOfferDetailId,\n" +
			"    c.CONTACT_NUMBER contactNumber,\n" +
			"    c.CAMPAIGN_ID campaignId,\n" +
			"    c.CAMPAIGN_CLONE_CODE campaignCloneCode,\n" +
			"    c.CHANNEL_PARTNER channelPartner,\n" +
			"    c.CUSTOMER_ID customerId,\n" +
			"    c.FIRST_NAME firstName,\n" +
			"    c.COUPON_CODE couponCode,\n" +
			"    c.COUPON_START_DATE couponStartDate,\n" +
			"    c.COUPON_END_DATE couponEndDate,\n" +
			"    c.OFFER_TEXT offerText,\n" +
			"    c.COUPON_TYPE couponType,\n" +
			"    c.REMINDER_DAYS reminderDays\n" +
			"FROM\n" +
			"    CUSTOMER_CAMPIAGN_OFFER_DETAIL c\n" +
			"WHERE\n" +
			"    COUPON_END_DATE = DATE(DATE_ADD( :currentDate ,\n" +
			"            INTERVAL REMINDER_DAYS - 1 DAY))\n" +
			"        AND BRAND_ID = :brandId\n" +
			"        AND OFFER_STATUS = :active\n" +
			"        AND (OFFER_APPLIED IS NULL\n" +
			"        OR OFFER_APPLIED = :no)"),
	CLEVERTAP_USER_ATTRIBUTE_QUERY("CALL CLEVERTAP_PROFILE_ATTRIBUTES(:customerId)"),
	CLEVERTAP_EVENT_ATTRIBUTE_QUERY("CALL CLEVERTAP_CHARGED_EVENT_ATTRIBUTES( :offerCode ,:customerId , :orderId,:orderSource,:brandId)"),
	CLEVERTAP_SUBSCRIPTION_EVENT_ATTRIBUTE_QUERY("CALL CLEVERTAP_SUBSCRIPTION_EVENT_ATTRIBUTES(:customerId, :orderId)"),
	CLM_CUSTOMER_ONE_VIEW_UPDATE_COMMUNICATIONS("UPDATE CLM_CUSTOMER_ONE_VIEW SET IS_SMS_SUBSCRIBER = :optInSms , OPT_IN_WHATSAPP = :optInWhatsapp WHERE CUSTOMER_ID = :customerId ");
	;

	private final String query;


	private NamedQueryDefinition(String query) {
		this.query = query;
	}


	public String getQuery() {
		return query;
	}

}
