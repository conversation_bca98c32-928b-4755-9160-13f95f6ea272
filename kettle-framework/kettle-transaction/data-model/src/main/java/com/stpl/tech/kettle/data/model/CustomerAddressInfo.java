/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

// Generated 14 Jul, 2015 1:35:13 AM by Hibernate Tools 4.0.0

import javax.persistence.*;

import static javax.persistence.GenerationType.IDENTITY;

/**
 * CustomerAddressInfo generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "CUSTOMER_ADDRESS_INFO")
public class CustomerAddressInfo implements java.io.Serializable {

    private Integer addressId;
    private CustomerInfo customerInfo;
    private String landmark;
    private String addressLine1;
    private String addressLine2;
    private String addressLine3;
    private String locality;
    private String subLocality;
    private String city;
    private String state;
    private String country;
    private String zipcode;
    private String addressType;
    private String company;
    private Boolean preferredAddress;
    private String name;
    private String contact;
    private String email;
    private String status;
    private String latitude;
    private String longitude;

    private String source;

    private String sourceId;

    @Column(name = "SOURCE_ID", unique = true)
    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    @Column(name = "SOURCE")
    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }




    public CustomerAddressInfo() {
    }

    public CustomerAddressInfo(CustomerInfo customerInfo, String addressLine1, String city, String state,
                               String country, String zipcode, String addressType, String name,String source,String sourceId) {
        this.customerInfo = customerInfo;
        this.addressLine1 = addressLine1;
        this.city = city;
        this.state = state;
        this.country = country;
        this.zipcode = zipcode;
        this.addressType = addressType;
        this.name = name;
        this.source=source;
        this.sourceId=sourceId;
    }

    public CustomerAddressInfo(CustomerInfo customerInfo, String addressLine1, String addressLine2, String addressLine3,
                               String city, String state, String country, String zipcode, String addressType,String source,String sourceId) {
        this.customerInfo = customerInfo;
        this.addressLine1 = addressLine1;
        this.addressLine2 = addressLine2;
        this.addressLine3 = addressLine3;
        this.city = city;
        this.state = state;
        this.country = country;
        this.zipcode = zipcode;
        this.addressType = addressType;
        this.source=source;
        this.sourceId=sourceId;

    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "ADDRESS_ID", unique = true, nullable = false)
    public Integer getAddressId() {
        return this.addressId;
    }

    public void setAddressId(Integer addressId) {
        this.addressId = addressId;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CUSTOMER_ID", nullable = false)
    public CustomerInfo getCustomerInfo() {
        return this.customerInfo;
    }

    public void setCustomerInfo(CustomerInfo customerInfo) {
        this.customerInfo = customerInfo;
    }

    @Column(name = "LANDMARK", nullable = true)
    public String getLandmark() {
        return this.landmark;
    }

    public void setLandmark(String value) {
        this.landmark = value;
    }

    @Column(name = "ADDRESS_LINE_1", nullable = false)
    public String getAddressLine1() {
        return this.addressLine1;
    }

    public void setAddressLine1(String addressLine1) {
        this.addressLine1 = addressLine1;
    }

    @Column(name = "ADDRESS_LINE_2")
    public String getAddressLine2() {
        return this.addressLine2;
    }

    public void setAddressLine2(String addressLine2) {
        this.addressLine2 = addressLine2;
    }

    @Column(name = "ADDRESS_LINE_3")
    public String getAddressLine3() {
        return this.addressLine3;
    }

    public void setAddressLine3(String addressLine3) {
        this.addressLine3 = addressLine3;
    }

    @Column(name = "SUB_LOCALITY")
    public String getSubLocality() {
        return subLocality;
    }

    public void setSubLocality(String subLocality) {
        this.subLocality = subLocality;
    }

    @Column(name = "LOCALITY")
    public String getLocality() {
        return locality;
    }

    public void setLocality(String locality) {
        this.locality = locality;
    }

    @Column(name = "CITY", nullable = false, length = 128)
    public String getCity() {
        return this.city;
    }

    public void setCity(String city) {
        this.city = city;
    }

    @Column(name = "STATE", nullable = false, length = 128)
    public String getState() {
        return this.state;
    }

    public void setState(String state) {
        this.state = state;
    }

    @Column(name = "COUNTRY", nullable = false, length = 128)
    public String getCountry() {
        return this.country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    @Column(name = "ZIPCODE", nullable = true, length = 40)
    public String getZipcode() {
        return this.zipcode;
    }

    public void setZipcode(String zipcode) {
        this.zipcode = zipcode;
    }

    @Column(name = "ADDRESS_TYPE", nullable = false, length = 50)
    public String getAddressType() {
        return this.addressType;
    }

    public void setAddressType(String addressType) {
        this.addressType = addressType;
    }

    @Column(name = "PREFERRED_ADDRESS")
    public Boolean getPreferredAddress() {
        return preferredAddress;
    }

    public void setPreferredAddress(Boolean preferredAddress) {
        this.preferredAddress = preferredAddress;
    }

    @Column(name = "COMPANY")
    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    @Column(name = "NAME")
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Column(name = "CONTACT")
    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    @Column(name = "EMAIL")
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    @Column(name = "STATUS")
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "LATITUDE")
    public String getLatitude() {
        return latitude;
    }

    public void setLatitude(String latitude) {
        this.latitude = latitude;
    }

    @Column(name = "LONGITUDE")
    public String getLongitude() {
        return longitude;
    }

    public void setLongitude(String longitude) {
        this.longitude = longitude;
    }
}
