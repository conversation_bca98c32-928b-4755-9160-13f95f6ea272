/**
 * 
 */
package com.stpl.tech.kettle.data.util;

import com.stpl.tech.master.domain.model.Consumable;
import com.stpl.tech.master.domain.model.ExtendedConsumable;
import com.stpl.tech.master.domain.model.Pair;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 *
 */
public class OatMilkConsumptionHelperP0 extends DesiChaiConsumptionHelper {

	public static final String CONSUMABLE_DATA_CREATOR = "CONSUMABLE_DATA_CREATOR";
	public static final String CONSUMABLE_DATA_CREATOR_DOUBLE_CHECK = "CONSUMABLE_DATA_CREATOR_DOUBLE_CHECK";
	public static final String SCM_PRODUCT_DATA_CREATOR = "SCM_PRODUCT_DATA_CREATOR";
	public static final String SCM_PRODUCT_DATA_CREATOR_DOUBLE_CHECK = "SCM_PRODUCT_DATA_CREATOR_DOUBLE_CHECK";

	private Map<Integer, ExtendedConsumable> scmProducts;
	private Map<Integer, Map<String, Map<String, List<Pair<Integer, BigDecimal>>>>> consumables;


	public Map<Integer, Map<String, Map<String, List<Pair<Integer, BigDecimal>>>>> consumables() {
		if (consumables == null) {
			synchronized (CONSUMABLE_DATA_CREATOR) {
				if (consumables == null) {
					synchronized (CONSUMABLE_DATA_CREATOR_DOUBLE_CHECK) {
						consumables = new HashMap<>();
						for (Integer id : products) {
							consumables.put(id, new HashMap<>());
							if (id == DESI_CHAI || id == DESI_CHAI_DASH_MILK || id == DESI_CHAI_DOUBLE_DASH_MILK
									|| id == FULL_DOODH || id == DESI_DOODH_KAM || id == DESI_PAANI_KAM
									|| id == LEMON_GRASS_GINGER || id == LEMON_GRASS_GINGER_DOODH_KUM
									|| id == LEMON_GRASS_GINGER_FULL_DOODH || id == LEMON_GRASS_GINGER_PAANI_KUM) {
								consumables.get(id).put(REGULAR, new HashMap<>());
								consumables.get(id).put(FULL, new HashMap<>());
								consumables.get(id).put(MINI_KETLI, new HashMap<>());
								consumables.get(id).put(TEA_FOR_2, new HashMap<>());
								consumables.get(id).put(TEA_FOR_4, new HashMap<>());
								consumables.get(id).put(CHOTI_KETLI, new HashMap<>());
								consumables.get(id).put(BADI_KETLI, new HashMap<>());
							} else if (id == CUTTING_CHAI) {
								consumables.get(id).put(NONE, new HashMap<>());
							} else if (id == KULHAD_CHAI) {
								consumables.get(id).put(NONE, new HashMap<>());
								consumables.get(id).put(MINI_KETLI, new HashMap<>());
								consumables.get(id).put(CHOTI_KETLI, new HashMap<>());
								consumables.get(id).put(BADI_KETLI, new HashMap<>());
							}
							if (id == CUTTING_CHAI || id == KULHAD_CHAI) {
								for (String key : consumables.get(id).keySet()) {
									consumables.get(id).get(key).put(REGULAR_PATTI_REGULAR_SUGAR, new ArrayList<>());
									consumables.get(id).get(key).put(REGULAR_PATTI_NO_SUGAR, new ArrayList<>());
								}
							} else if (id == DESI_CHAI || id == DESI_CHAI_DASH_MILK || id == DESI_CHAI_DOUBLE_DASH_MILK
									|| id == FULL_DOODH || id == DESI_DOODH_KAM || id == DESI_PAANI_KAM
									|| id == LEMON_GRASS_GINGER || id == LEMON_GRASS_GINGER_DOODH_KUM
									|| id == LEMON_GRASS_GINGER_FULL_DOODH || id == LEMON_GRASS_GINGER_PAANI_KUM) {
								for (String key : consumables.get(id).keySet()) {
									consumables.get(id).get(key).put(REGULAR_PATTI_REGULAR_SUGAR, new ArrayList<>());
									consumables.get(id).get(key).put(REGULAR_PATTI_NO_SUGAR, new ArrayList<>());
									consumables.get(id).get(key).put(KADAK_REGULAR_SUGAR, new ArrayList<>());
									consumables.get(id).get(key).put(KADAK_NO_SUGAR, new ArrayList<>());
								}
							}

							if (id == DESI_CHAI || id == LEMON_GRASS_GINGER) {
								consumables.get(id).get(REGULAR).get(REGULAR_PATTI_REGULAR_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(102792, new BigDecimal("1.00"))));
								consumables.get(id).get(REGULAR).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(102792, new BigDecimal("0.0028"))));

								consumables.get(id).get(FULL).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(100123, new BigDecimal("0.0038")),
												new Pair<Integer, BigDecimal>(100330, new BigDecimal("0.0205"))));
								consumables.get(id).get(FULL).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(100123, new BigDecimal("0.0038"))));

								consumables.get(id).get(MINI_KETLI).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(104297, new BigDecimal("0.0038")),
												new Pair<Integer, BigDecimal>(100330, new BigDecimal("0.0205"))));
								consumables.get(id).get(MINI_KETLI).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(104297, new BigDecimal("0.0038"))));

								consumables.get(id).get(TEA_FOR_2).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(104297, new BigDecimal("0.0038")),
												new Pair<Integer, BigDecimal>(100330, new BigDecimal("0.0205"))));
								consumables.get(id).get(TEA_FOR_2).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(104297, new BigDecimal("0.0038"))));

								consumables.get(id).get(TEA_FOR_4).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(102792, new BigDecimal("0.0076")),
												new Pair<Integer, BigDecimal>(100330, new BigDecimal("0.0410"))));
								consumables.get(id).get(TEA_FOR_4).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(102792, new BigDecimal("0.0076"))));

								consumables.get(id).get(CHOTI_KETLI).get(REGULAR_PATTI_REGULAR_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(102792, new BigDecimal("2.00"))));
								consumables.get(id).get(CHOTI_KETLI).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(102792, new BigDecimal("0.0056"))));

								consumables.get(id).get(BADI_KETLI).get(REGULAR_PATTI_REGULAR_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(102792, new BigDecimal("5.00"))));
								consumables.get(id).get(BADI_KETLI).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(102792, new BigDecimal("0.0140"))));

								consumables.get(id).get(REGULAR).get(KADAK_REGULAR_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101715, new BigDecimal("1.00"))));
								consumables.get(id).get(REGULAR).get(KADAK_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101715, new BigDecimal("0.0031"))));

								consumables.get(id).get(FULL).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(100123, new BigDecimal("0.00852")),
												new Pair<Integer, BigDecimal>(100330, new BigDecimal("0.0205"))));
								consumables.get(id).get(FULL).get(KADAK_NO_SUGAR).addAll(Arrays
										.asList(new Pair<Integer, BigDecimal>(100123, new BigDecimal("0.00852"))));

								consumables.get(id).get(MINI_KETLI).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(101716, new BigDecimal("0.00852")),
												new Pair<Integer, BigDecimal>(100330, new BigDecimal("0.0205"))));
								consumables.get(id).get(MINI_KETLI).get(KADAK_NO_SUGAR).addAll(Arrays
										.asList(new Pair<Integer, BigDecimal>(101716, new BigDecimal("0.00852"))));

								consumables.get(id).get(TEA_FOR_2).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(101716, new BigDecimal("0.00852")),
												new Pair<Integer, BigDecimal>(100330, new BigDecimal("0.0205"))));
								consumables.get(id).get(TEA_FOR_2).get(KADAK_NO_SUGAR).addAll(Arrays
										.asList(new Pair<Integer, BigDecimal>(101716, new BigDecimal("0.00852"))));

								consumables.get(id).get(TEA_FOR_4).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(101715, new BigDecimal("0.017")),
												new Pair<Integer, BigDecimal>(100330, new BigDecimal("0.041"))));
								consumables.get(id).get(TEA_FOR_4).get(KADAK_NO_SUGAR).addAll(Arrays
										.asList(new Pair<Integer, BigDecimal>(101715, new BigDecimal("0.017"))));

								consumables.get(id).get(CHOTI_KETLI).get(KADAK_REGULAR_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101715, new BigDecimal("2.00"))));
								consumables.get(id).get(CHOTI_KETLI).get(KADAK_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101715, new BigDecimal("0.0062"))));

								consumables.get(id).get(BADI_KETLI).get(KADAK_REGULAR_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101715, new BigDecimal("5.00"))));
								consumables.get(id).get(BADI_KETLI).get(KADAK_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101715, new BigDecimal("0.0310"))));
							}

							if (id == CUTTING_CHAI) {
								consumables.get(id).get(NONE).get(REGULAR_PATTI_REGULAR_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(100699, new BigDecimal("1.00"))));
								consumables.get(id).get(NONE).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(100123, new BigDecimal("0.0016"))));

							}

							if (id == KULHAD_CHAI) {

								consumables.get(id).get(NONE).get(REGULAR_PATTI_REGULAR_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(103467, new BigDecimal("1.00"))));
								consumables.get(id).get(NONE).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(103467, new BigDecimal("0.005"))));
								consumables.get(id).get(CHOTI_KETLI).get(REGULAR_PATTI_REGULAR_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(103467, new BigDecimal("2.00"))));
								consumables.get(id).get(CHOTI_KETLI).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(103467, new BigDecimal("0.010"))));

								consumables.get(id).get(BADI_KETLI).get(REGULAR_PATTI_REGULAR_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(103467, new BigDecimal("5.00"))));
								consumables.get(id).get(BADI_KETLI).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(103467, new BigDecimal("0.025"))));

								consumables.get(id).get(MINI_KETLI).get(REGULAR_PATTI_REGULAR_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101720, new BigDecimal("1.00")),
												new Pair<Integer, BigDecimal>(101726, new BigDecimal("1.00"))));
								consumables.get(id).get(MINI_KETLI).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101720, new BigDecimal("1.00"))));

							}

							if (id == FULL_DOODH || id == DESI_PAANI_KAM || id == LEMON_GRASS_GINGER_FULL_DOODH
									|| id == LEMON_GRASS_GINGER_PAANI_KUM) {

								consumables.get(id).get(REGULAR).get(REGULAR_PATTI_REGULAR_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101715, new BigDecimal("1.00"))));
								consumables.get(id).get(REGULAR).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101715, new BigDecimal("0.0031"))));

								consumables.get(id).get(FULL).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(100123, new BigDecimal("0.00852")),
												new Pair<Integer, BigDecimal>(100330, new BigDecimal("0.0205"))));
								consumables.get(id).get(FULL).get(REGULAR_PATTI_NO_SUGAR).addAll(Arrays
										.asList(new Pair<Integer, BigDecimal>(100123, new BigDecimal("0.00852"))));

								consumables.get(id).get(MINI_KETLI).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(101716, new BigDecimal("0.00852")),
												new Pair<Integer, BigDecimal>(100330, new BigDecimal("0.0205"))));
								consumables.get(id).get(MINI_KETLI).get(REGULAR_PATTI_NO_SUGAR).addAll(Arrays
										.asList(new Pair<Integer, BigDecimal>(101716, new BigDecimal("0.00852"))));

								consumables.get(id).get(TEA_FOR_2).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(101716, new BigDecimal("0.00852")),
												new Pair<Integer, BigDecimal>(100330, new BigDecimal("0.0205"))));
								consumables.get(id).get(TEA_FOR_2).get(REGULAR_PATTI_NO_SUGAR).addAll(Arrays
										.asList(new Pair<Integer, BigDecimal>(101716, new BigDecimal("0.00852"))));

								consumables.get(id).get(TEA_FOR_4).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(101715, new BigDecimal("0.0170")),
												new Pair<Integer, BigDecimal>(100330, new BigDecimal("0.0410"))));
								consumables.get(id).get(TEA_FOR_4).get(REGULAR_PATTI_NO_SUGAR).addAll(Arrays
										.asList(new Pair<Integer, BigDecimal>(101715, new BigDecimal("0.0170"))));

								consumables.get(id).get(CHOTI_KETLI).get(REGULAR_PATTI_REGULAR_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101715, new BigDecimal("2.00"))));
								consumables.get(id).get(CHOTI_KETLI).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101715, new BigDecimal("0.0062"))));

								consumables.get(id).get(BADI_KETLI).get(REGULAR_PATTI_REGULAR_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101715, new BigDecimal("5.00"))));
								consumables.get(id).get(BADI_KETLI).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101715, new BigDecimal("0.0310"))));

								consumables.get(id).get(REGULAR).get(KADAK_REGULAR_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(103467, new BigDecimal("1.00"))));
								consumables.get(id).get(REGULAR).get(KADAK_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(103467, new BigDecimal("0.005"))));

								consumables.get(id).get(FULL).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(100123, new BigDecimal("0.01305")),
												new Pair<Integer, BigDecimal>(100330, new BigDecimal("0.0205"))));
								consumables.get(id).get(FULL).get(KADAK_NO_SUGAR).addAll(Arrays
										.asList(new Pair<Integer, BigDecimal>(100123, new BigDecimal("0.01305"))));

								consumables.get(id).get(MINI_KETLI).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(101720, new BigDecimal("0.01305")),
												new Pair<Integer, BigDecimal>(100330, new BigDecimal("0.0205"))));
								consumables.get(id).get(MINI_KETLI).get(KADAK_NO_SUGAR).addAll(Arrays
										.asList(new Pair<Integer, BigDecimal>(101720, new BigDecimal("0.01305"))));

								consumables.get(id).get(TEA_FOR_2).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(101720, new BigDecimal("0.01305")),
												new Pair<Integer, BigDecimal>(100330, new BigDecimal("0.0205"))));
								consumables.get(id).get(TEA_FOR_2).get(KADAK_NO_SUGAR).addAll(Arrays
										.asList(new Pair<Integer, BigDecimal>(101720, new BigDecimal("0.01305"))));

								consumables.get(id).get(TEA_FOR_4).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(103467, new BigDecimal("0.0261")),
												new Pair<Integer, BigDecimal>(100330, new BigDecimal("0.041"))));
								consumables.get(id).get(TEA_FOR_4).get(KADAK_NO_SUGAR).addAll(Arrays
										.asList(new Pair<Integer, BigDecimal>(103467, new BigDecimal("0.0261"))));

								consumables.get(id).get(CHOTI_KETLI).get(KADAK_REGULAR_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(103467, new BigDecimal("2.00"))));
								consumables.get(id).get(CHOTI_KETLI).get(KADAK_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(103467, new BigDecimal("0.010"))));

								consumables.get(id).get(BADI_KETLI).get(KADAK_REGULAR_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(103467, new BigDecimal("5.00"))));
								consumables.get(id).get(BADI_KETLI).get(KADAK_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(103467, new BigDecimal("0.025"))));

							}

							if (id == DESI_DOODH_KAM || id == DESI_CHAI_DASH_MILK || id == DESI_CHAI_DOUBLE_DASH_MILK
									|| id == LEMON_GRASS_GINGER_DOODH_KUM) {

								consumables.get(id).get(REGULAR).get(REGULAR_PATTI_REGULAR_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(102792, new BigDecimal("1.00"))));
								consumables.get(id).get(REGULAR).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(102792, new BigDecimal("0.0028"))));

								consumables.get(id).get(FULL).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(100123, new BigDecimal("0.0038")),
												new Pair<Integer, BigDecimal>(100330, new BigDecimal("0.0205"))));
								consumables.get(id).get(FULL).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(100123, new BigDecimal("0.0038"))));

								consumables.get(id).get(MINI_KETLI).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(104297, new BigDecimal("0.0038")),
												new Pair<Integer, BigDecimal>(100330, new BigDecimal("0.0205"))));
								consumables.get(id).get(MINI_KETLI).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(104297, new BigDecimal("0.0038"))));

								consumables.get(id).get(TEA_FOR_2).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(104297, new BigDecimal("0.0038")),
												new Pair<Integer, BigDecimal>(100330, new BigDecimal("0.0205"))));
								consumables.get(id).get(TEA_FOR_2).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(104297, new BigDecimal("0.0038"))));

								consumables.get(id).get(TEA_FOR_4).get(REGULAR_PATTI_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(102792, new BigDecimal("0.0076")),
												new Pair<Integer, BigDecimal>(100330, new BigDecimal("0.0410"))));
								consumables.get(id).get(TEA_FOR_4).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(102792, new BigDecimal("0.0076"))));

								consumables.get(id).get(CHOTI_KETLI).get(REGULAR_PATTI_REGULAR_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(102792, new BigDecimal("2.00"))));
								consumables.get(id).get(CHOTI_KETLI).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(102792, new BigDecimal("0.0056"))));

								consumables.get(id).get(BADI_KETLI).get(REGULAR_PATTI_REGULAR_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(102792, new BigDecimal("5.00"))));
								consumables.get(id).get(BADI_KETLI).get(REGULAR_PATTI_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(102792, new BigDecimal("0.0140"))));

								consumables.get(id).get(REGULAR).get(KADAK_REGULAR_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101715, new BigDecimal("1.00"))));
								consumables.get(id).get(REGULAR).get(KADAK_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101715, new BigDecimal("0.0031"))));

								consumables.get(id).get(FULL).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(100123, new BigDecimal("0.00852")),
												new Pair<Integer, BigDecimal>(100330, new BigDecimal("0.0205"))));
								consumables.get(id).get(FULL).get(KADAK_NO_SUGAR).addAll(Arrays
										.asList(new Pair<Integer, BigDecimal>(100123, new BigDecimal("0.00852"))));

								consumables.get(id).get(MINI_KETLI).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(101716, new BigDecimal("0.00852")),
												new Pair<Integer, BigDecimal>(100330, new BigDecimal("0.0205"))));
								consumables.get(id).get(MINI_KETLI).get(KADAK_NO_SUGAR).addAll(Arrays
										.asList(new Pair<Integer, BigDecimal>(101716, new BigDecimal("0.00852"))));

								consumables.get(id).get(TEA_FOR_2).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(101716, new BigDecimal("0.00852")),
												new Pair<Integer, BigDecimal>(100330, new BigDecimal("0.0205"))));
								consumables.get(id).get(TEA_FOR_2).get(KADAK_NO_SUGAR).addAll(Arrays
										.asList(new Pair<Integer, BigDecimal>(101716, new BigDecimal("0.00852"))));

								consumables.get(id).get(TEA_FOR_4).get(KADAK_REGULAR_SUGAR)
										.addAll(Arrays.asList(
												new Pair<Integer, BigDecimal>(101715, new BigDecimal("0.034")),
												new Pair<Integer, BigDecimal>(100330, new BigDecimal("0.082"))));
								consumables.get(id).get(TEA_FOR_4).get(KADAK_NO_SUGAR).addAll(Arrays
										.asList(new Pair<Integer, BigDecimal>(101715, new BigDecimal("0.034"))));

								consumables.get(id).get(CHOTI_KETLI).get(KADAK_REGULAR_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101715, new BigDecimal("2.00"))));
								consumables.get(id).get(CHOTI_KETLI).get(KADAK_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101715, new BigDecimal("0.0062"))));

								consumables.get(id).get(BADI_KETLI).get(KADAK_REGULAR_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101715, new BigDecimal("5.00"))));
								consumables.get(id).get(BADI_KETLI).get(KADAK_NO_SUGAR).addAll(
										Arrays.asList(new Pair<Integer, BigDecimal>(101715, new BigDecimal("0.0310"))));

							}
						}
					}
				}
			}
		}
		return consumables;
	}

	private Consumable getConsumable(int productId, String name, String uom) {
		Consumable c = new Consumable();
		c.setName(name);
		c.setProductId(productId);
		c.setUom(uom);
		return c;
	}

	public Map<Integer, ExtendedConsumable> scmProducts() {
		if (scmProducts == null) {
			synchronized (SCM_PRODUCT_DATA_CREATOR) {
				if (scmProducts == null) {
					synchronized (SCM_PRODUCT_DATA_CREATOR_DOUBLE_CHECK) {
						scmProducts = new HashMap<>();
						scmProducts.put(100123, getConsumable(100123, "Desi Chai Patti", "KG", DesiChaiConsumptionHelper.PATTI));
						scmProducts.put(100298, getConsumable(100298, "Sachet DRC - Desi Regular", "SACHET", DesiChaiConsumptionHelper.PATTI));
						scmProducts.put(101723, getConsumable(101723, "Chai Sachet PKK-Sky Blue-MK", "SACHET", DesiChaiConsumptionHelper.PATTI));
						scmProducts.put(101726, getConsumable(101726, "Sugar Sachet Pink-MK", "SACHET", DesiChaiConsumptionHelper.SUGAR));
						scmProducts.put(100330, getConsumable(100330, SUGAR, "KG", DesiChaiConsumptionHelper.SUGAR));
						scmProducts.put(100699, getConsumable(100699, "Chai Sachet - MSC - Yellow", "PC", DesiChaiConsumptionHelper.PATTI));
						scmProducts.put(100834,
								getConsumable(100834, "Chai Sachet - Desi Kadak Regular - DKR - Green", "PC", DesiChaiConsumptionHelper.PATTI));
						scmProducts.put(100895, getConsumable(100895, "Chai Sachet - PKK - Sky blue", "PC", DesiChaiConsumptionHelper.PATTI));
						scmProducts.put(103483, getConsumable(103483, "Sugar Sachet Orange - PN", "PC", DesiChaiConsumptionHelper.SUGAR));
						scmProducts.put(100333, getConsumable(100333, "Sachet-Sugar White", "PC", DesiChaiConsumptionHelper.SUGAR));
						scmProducts.put(100171, getConsumable(100171, "Green Tea Patti-Premium", "PC", DesiChaiConsumptionHelper.PATTI));
						scmProducts.put(100144, getConsumable(100144, "English Breakfast Patti", "PC", DesiChaiConsumptionHelper.PATTI));
						scmProducts.put(100120, getConsumable(100120, "Darjeeling First Flush Patti", "PC", DesiChaiConsumptionHelper.PATTI));
						scmProducts.put(100166, getConsumable(100166, "Gods Chai Patti", "PC", DesiChaiConsumptionHelper.PATTI));
						scmProducts.put(100237, getConsumable(100237, "Mint Green Patti", "PC", DesiChaiConsumptionHelper.PATTI));
						scmProducts.put(100194, getConsumable(100194, "Kashmiri Kahwa Patti", "PC", DesiChaiConsumptionHelper.PATTI));
						scmProducts.put(100332, getConsumable(100332, "Sachet-Sugar Brown", "PC", DesiChaiConsumptionHelper.SUGAR));
						scmProducts.put(102792, getConsumable(102792, "Chai Sachet- Brown - ORC", "SACHET", DesiChaiConsumptionHelper.PATTI));
						scmProducts.put(101715, getConsumable(101715, "Chai Sachet DRC-White-Regular-BLR", "PC", DesiChaiConsumptionHelper.PATTI));
						scmProducts.put(103467, getConsumable(103467, "Chai Sachet Green Reg - HYD", "PC", DesiChaiConsumptionHelper.PATTI));
						scmProducts.put(103468, getConsumable(103468, "Chai Sachet Green MK - HYD", "PC", DesiChaiConsumptionHelper.PATTI));

						scmProducts.put(101715, getConsumable(101715, "Chai Sachet DRC-White-Regular-BLR", "PC", DesiChaiConsumptionHelper.PATTI));
						scmProducts.put(101716, getConsumable(101716, "Chai Sachet DRC-White-MK-BLR", "PC", DesiChaiConsumptionHelper.PATTI));
						scmProducts.put(101717, getConsumable(101717, "Chai Sachet DRC-White-Full-BLR", "PC", DesiChaiConsumptionHelper.PATTI));
						scmProducts.put(101718, getConsumable(101718, "Chai Sachet MSC-Yellow-BLR", "PC", DesiChaiConsumptionHelper.PATTI));
						scmProducts.put(101721, getConsumable(101721, "Chai Sachet DRK-Green-Full-BLR", "PC", DesiChaiConsumptionHelper.PATTI));
						scmProducts.put(101722, getConsumable(101722, "Chai Sachet PKK-Sky Blue-Regular-BLR", "PC", DesiChaiConsumptionHelper.PATTI));
						scmProducts.put(101724, getConsumable(101724, "Chai Sachet PKK-Sky Blue-Full-BLR", "PC", DesiChaiConsumptionHelper.PATTI));
						scmProducts.put(101725, getConsumable(101725, "Sugar Sachet-Regular", "PC", DesiChaiConsumptionHelper.SUGAR));

						scmProducts.put(101727, getConsumable(101727, "Sugar Sachet-Full", "PC", DesiChaiConsumptionHelper.SUGAR));
						scmProducts.put(101728, getConsumable(101728, "Sugar Sachet-None", "PC", DesiChaiConsumptionHelper.SUGAR));
						scmProducts.put(104297, getConsumable(104297, "Chai Sachet- Brown - ORC-MK","SACHET", DesiChaiConsumptionHelper.PATTI));
						scmProducts.put(101720, getConsumable(101720, "Chai Sachet DKR-Green-MK", "SACHET", DesiChaiConsumptionHelper.PATTI));
					}
				}
			}
		}
		return scmProducts;
	}
}
