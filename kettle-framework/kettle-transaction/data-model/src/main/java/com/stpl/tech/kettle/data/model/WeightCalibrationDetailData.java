package com.stpl.tech.kettle.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
@Entity
@Table(name = "WEIGHT_CALIBRATION_DETAIL")
public class WeightCalibrationDetailData {
    public static final String UNIT_ID = "UNIT_ID";
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "WT_CALIBRATION_ID")
    private int id;
    @Column(name = "UNIT_ID")
    private int unitId;

    @Column(name = "USER_ID")
    int userId;

    @Column(name = "USER_NAME")
    String userName;
    @Column(name = "MONK_NAME")
    String monkName;

    @Column(name = "KNOWN_WEIGHT")
    String knownWeight;
    @Column(name = "MEASURED_WEIGHT")
    String measuredWeight;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "MEASUREMENT_TIME")
    Date measurementTime;

}
