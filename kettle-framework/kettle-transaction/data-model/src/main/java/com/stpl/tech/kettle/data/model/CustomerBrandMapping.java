/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;



import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;


@Entity
@Table(name = "CUSTOMER_BRAND_MAPPING")
public class CustomerBrandMapping implements java.io.Serializable {

    private int id;
    private int customerId;
    private int brandId;
    private int lastOrderId;
    private int totalOrder;
    private Date lastOrderTime;
    private Integer lastSpecialOrderId;
    private Integer totalSpecialOrder;
    private Date lastSpecialOrderTime;

    public CustomerBrandMapping() {
    }

    public CustomerBrandMapping(int customerId, int brandId, int totalOrder) {
        this.customerId = customerId;
        this.brandId = brandId;
        this.totalOrder = totalOrder;
    }

    public CustomerBrandMapping(int customerId, int brandId, int totalOrder,Integer totalSpecialOrder) {
        this.customerId = customerId;
        this.brandId = brandId;
        this.totalOrder = totalOrder;
        this.totalSpecialOrder = totalSpecialOrder;
    }

    public CustomerBrandMapping(int customerId, int brandId, int lastOrderId, int totalOrder, Date lastOrderTime) {
        this.customerId = customerId;
        this.brandId = brandId;
        this.lastOrderId = lastOrderId;
        this.totalOrder = totalOrder;
        this.lastOrderTime = lastOrderTime;
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "ID", unique = true, nullable = false)
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Column(name = "CUSTOMER_ID")
    public int getCustomerId() {
        return customerId;
    }

    public void setCustomerId(int customerId) {
        this.customerId = customerId;
    }

    @Column(name = "BRAND_ID")
    public int getBrandId() {
        return brandId;
    }

    public void setBrandId(int brandId) {
        this.brandId = brandId;
    }


    @Column(name = "TOTAL_ORDER")
    public int getTotalOrder() {
        return totalOrder;
    }

    public void setTotalOrder(int totalOrder) {
        this.totalOrder = totalOrder;
    }


    @Column(name = "LAST_ORDER_ID")
    public int getLastOrderId() {
        return lastOrderId;
    }

    public void setLastOrderId(int lastOrderId) {
        this.lastOrderId = lastOrderId;
    }


    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "LAST_ORDER_TIME")
    public Date getLastOrderTime() {
        return lastOrderTime;
    }

    public void setLastOrderTime(Date lastOrderTime) {
        this.lastOrderTime = lastOrderTime;
    }

    @Column(name = "LAST_SPECIAL_ORDER_ID")
    public Integer getLastSpecialOrderId() {
        return lastSpecialOrderId;
    }

    public void setLastSpecialOrderId(Integer lastSpecialOrderId) {
        this.lastSpecialOrderId = lastSpecialOrderId;
    }

    @Column(name = "TOTAL_SPECIAL_ORDER")
    public Integer getTotalSpecialOrder() {
        return totalSpecialOrder;
    }

    public void setTotalSpecialOrder(Integer totalSpecialOrder) {
        this.totalSpecialOrder = totalSpecialOrder;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "LAST_SPECIAL_ORDER_TIME")
    public Date getLastSpecialOrderTime() {
        return lastSpecialOrderTime;
    }

    public void setLastSpecialOrderTime(Date lastSpecialOrderTime) {
        this.lastSpecialOrderTime = lastSpecialOrderTime;
    }
}
