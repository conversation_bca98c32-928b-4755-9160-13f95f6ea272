package com.stpl.tech.kettle.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.math.BigDecimal;

@Getter
@Setter
@ToString
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class WalletSuggestionData {
    private String customerId;
    private Integer amountPayable;
    private String isNewCustomer;
    private Integer latestOrderDayDiff;
    private Integer visitsCount;

    private String storeFormat;
    private Integer dineInFlag;
    private Integer deliveryFlag;
    private Integer daysSinceCustomerAcquired;
    private Integer dineInVisitsCount;
    private Integer visitsCount30Days;
    private Integer visitsCount90Days;
    private Integer netSales;
    private Integer loyaltyPointsBalance;
    private Integer activeSelectMembership;
    private Integer subscriptionExpiryDayDiff;
    private Integer giftCardLastOrderDate;
    private BigDecimal giftCardAmountPurchased;
    private String profile;

    private Integer minimumDenomination;
    private Integer minimumDenominationCount;
    private Integer walletRecommendationBaseValue;
    private Double extraValueMultiplier;
}
