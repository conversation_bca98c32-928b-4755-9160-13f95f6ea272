/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core;

import com.stpl.tech.kettle.core.cache.MetadataCache;
import com.stpl.tech.kettle.core.notification.OfferOrder;
import com.stpl.tech.master.core.CouponMappingType;
import com.stpl.tech.master.core.WebErrorCode;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.CouponMapping;
import com.stpl.tech.util.AppUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

public class CouponMappingHelper {

	public static List<CouponMapping> getValueOf(CouponMappingType type, OfferOrder o, MasterDataCache cache)
			throws DataNotFoundException {
		List<CouponMapping> values = new ArrayList<>();
		switch (type) {

		case CUSTOMER:
			values.add(new CouponMapping(
					Integer.toString(o.getOrder().getCustomerId() == null ? 0 : o.getOrder().getCustomerId())));
			return values;
		case CONTACT_NUMBER:
			values.add(new CouponMapping(o.getContact()));
			return values;
		case NEW_CUSTOMER:
			values.add(new CouponMapping(AppUtils.setStatus(o.isNewCustomer())));
			return values;
		case UNIT:
			values.add(new CouponMapping(Integer.toString(o.getOrder().getUnitId())));
			return values;
		case UNIT_REGION:
			values.add(new CouponMapping(cache.getUnitBasicDetail(o.getOrder().getUnitId()).getRegion()));
			return values;
		case CITY:
			values.add(new CouponMapping(cache.getUnitBasicDetail(o.getOrder().getUnitId()).getLocationCode()));
			return values;
		case ORDER_SOURCE:
			values.add(new CouponMapping(o.getOrder().getSource()));
			return values;
		case CHANNEL_PARTNER:
			values.add(new CouponMapping(Integer.toString(o.getOrder().getChannelPartner())));
			return values;
		case PAYMENT_MODE:
			o.getOrder().getSettlements()
					.forEach(modes -> values.add(new CouponMapping(Integer.toString(modes.getMode()))));
			return values;
		case PRODUCT:
			o.getOrder().getOrders().forEach(
					item -> values.add(new CouponMapping(Integer.toString(item.getProductId()), item.getDimension())));
			return values;
		case PRODUCT_CATEGORY:
			o.getOrder().getOrders()
					.forEach(item -> values.add(new CouponMapping(
							Integer.toString(cache.getProductBasicDetail(item.getProductId()).getType()),
							item.getDimension())));
			return values;
		case PRODUCT_SUB_CATEGORY:
			o.getOrder().getOrders()
					.forEach(item -> values.add(new CouponMapping(
							Integer.toString(cache.getProductBasicDetail(item.getProductId()).getSubType()),
							item.getDimension())));
			return values;
		case UNIT_SUB_CATEGORY:
			values.add(new CouponMapping(cache.getUnitBasicDetail(o.getOrder().getUnitId()).getSubCategory().value()));
			return values;
		default:
			return values;
		}

	}

	public static String getErrorStatement(CouponMappingType type, Set<CouponMapping> mappingList,
			MasterDataCache cache, MetadataCache metadataCache) throws NumberFormatException, DataNotFoundException {
		StringBuilder sb = new StringBuilder();
		switch (type) {
		case CUSTOMER:
			return "Customer is not eligible for the offer. This offer is for selective customers only.";
		case CONTACT_NUMBER:
			return "Contact Number is not eligible for the offer. This offer is for selective customers only.";
		case NEW_CUSTOMER:
			return "This offer is for new customers only.";
		case UNIT:
			for (CouponMapping mapping : mappingList) {
				sb.append(cache.getUnit(Integer.parseInt(mapping.getValue())).getName() + ", ");
			}
			return "This offer cannot be availed at this unit. Offer can be availed at "
					+ sb.substring(0, sb.length() - 2) + " only";
		case UNIT_REGION:
			for (CouponMapping mapping : mappingList) {
				sb.append(mapping.getValue() + ", ");
			}
			return "This offer cannot be availed in this region. Offer can be availed in "
					+ sb.substring(0, sb.length() - 2) + " only";
		case CITY:
			for (CouponMapping mapping : mappingList) {
				sb.append(mapping.getValue() + ", ");
			}
			return "This offer cannot be availed in this city. Offer can be availed in "
					+ sb.substring(0, sb.length() - 2) + " only";
		case ORDER_SOURCE:
			return "Incorrect order Source. This offer cannot be availed on this unit.";
		case CHANNEL_PARTNER:
			Set<String> partnerNameSet =  new HashSet<>();
			for(CouponMapping mapping : mappingList){
				String partnerName = cache.getChannelPartnerDetail(Integer.parseInt(mapping.getValue())).getPartnerName();
				if(StringUtils.isNotBlank(partnerName)){
					partnerNameSet.add(partnerName);
				}else{
					partnerNameSet.add(cache.getChannelPartner(Integer.parseInt(mapping.getValue())).getName());
				}
			}
			partnerNameSet.forEach(name -> {
				sb.append(name + ", ");
			});
			return "This coupon can be applied at " + sb.substring(0,sb.length()-2)+" only";
		case PAYMENT_MODE:
			mappingList.forEach(p -> {
				sb.append(cache.getPaymentMode(Integer.parseInt(p.getValue())).getName() + ", ");
			});
			return "Incorrect Payment Mode. Valid for " + sb.substring(0, sb.length() - 2) + " only";
		case PRODUCT:
			mappingList.forEach(p -> {
				sb.append(cache.getProductBasicDetail(Integer.parseInt(p.getValue())).getDetail().getName() + " ("
						+ p.getDimension() + "), ");
			});
			return "Order does not contain required Product, Eligible Products: " + sb.substring(0, sb.length() - 2);
		case PRODUCT_CATEGORY:
			mappingList.forEach(p -> {
				sb.append(cache.getProductCategory(Integer.parseInt(p.getValue())).getDetail().getName() + " ("
						+ p.getDimension() + "), ");
			});
			return "Order does not contain Product of required Category, Eligible Product Category: "
					+ sb.substring(0, sb.length() - 2);
		case PRODUCT_SUB_CATEGORY:
			mappingList.forEach(p -> {
				sb.append(cache.getProductSubCategory(Integer.parseInt(p.getValue())).getName() + " ("
						+ p.getDimension() + "), ");
			});
			return "Order does not contain Product of required Sub-Category, Eligible Product Sub-Category: "
					+ sb.substring(0, sb.length() - 2);
			case UNIT_SUB_CATEGORY:
				for (CouponMapping mapping : mappingList) {
					sb.append(mapping.getValue() + ", ");
				}
			    return "This offer cannot be availed in this unit sub category. Offer can be availed in units with sub-category "
				    + sb.substring(0, sb.length() - 2) + " only";
		default:
			return null;
		}
	}

	public static WebErrorCode getErrorCode(CouponMappingType type) {
		switch (type) {
		case CUSTOMER:
			return WebErrorCode.CUSTOMER_NOT_FOUND;
		case CONTACT_NUMBER:
			return WebErrorCode.CUSTOMER_NOT_FOUND;
		case UNIT:
			return WebErrorCode.INVALID_UNIT;
		case UNIT_REGION:
			return WebErrorCode.INVALID_REGION;
		case CITY:
			return WebErrorCode.INVALID_CITY;
		case ORDER_SOURCE:
			return WebErrorCode.INVALID_SOURCE;
		case PRODUCT:
			return WebErrorCode.PRODUCT_NOT_FOUND;
		case PRODUCT_CATEGORY:
			return WebErrorCode.PRODUCT_NOT_FOUND;
		case PRODUCT_SUB_CATEGORY:
			return WebErrorCode.PRODUCT_NOT_FOUND;
		default:
			return WebErrorCode.NOT_AVAILABLE;
		}
	}
}
