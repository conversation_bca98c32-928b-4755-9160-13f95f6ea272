package com.stpl.tech.kettle.data.model;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Getter
@Setter
public class OrderDetailForFeedback implements Serializable{
    /**
	 * 
	 */
	private static final long serialVersionUID = 5595533142130228003L;
	private Integer bid;    //brandId
    private String bn;   // brandName
    private String bl;   // brandLogo
    private String un;    //unitName
    private Integer uid;  // unitId
    private String uc; // unitCity
    private String goid; // generatedOrderId
    private Integer oid; //orderId
    private Integer fid; //feedbackId
    private Integer cid; //customerId
    private String cn; //customerName
    private String fet; //feedbackEventType
    private Date ft;  //feedbackTime
    private String ua; //userAgent
    private String od; //orderDate
    private Integer or; //orderRating
    private Integer onr; //orderNPSRating
    private boolean sor; //showOrderRating
    private boolean sonr; //showOrderNPSRating
    private String oc; //orderComment
    private String os; //orderSource
    private String ru; //redirectUrl
    private String rud; //redirectUrlDefault
    private String cpc; //channelPartnerCode
    private String cpn; //channelPartnerName
    private List<FeedbackOrderItem> fiol; //feedbackOrderItemList
    private String t; //token
    private String bpiu; //baseProductImageUrl
    private String cc; //customerCallback;
    private String ofq; //orderFeedbackQuestion;
    private String nq; //npsQuestion
    private List<OrderFeedbackQuestionResponse> ynq; // yes no questions

    private List<OrderFeedbackQuestionResponse> trq; // text response question

    private List<OrderFeedbackQuestionResponse> mcq; // Multiple Choice question

    private List<OrderFeedbackQuestionResponse> erq; // Emoji Rating question

    @Override
    public String toString() {
        return "OrderDetailForFeedback{" +
                "bid=" + bid +
                ", bn='" + bn + '\'' +
                ", bl='" + bl + '\'' +
                ", un='" + un + '\'' +
                ", uid=" + uid +
                ", uc='" + uc + '\'' +
                ", goid='" + goid + '\'' +
                ", oid=" + oid +
                ", fid=" + fid +
                ", cid=" + cid +
                ", cn='" + cn + '\'' +
                ", fet='" + fet + '\'' +
                ", ft=" + ft +
                ", ua='" + ua + '\'' +
                ", od='" + od + '\'' +
                ", or=" + or +
                ", onr=" + onr +
                ", sor=" + sor +
                ", sonr=" + sonr +
                ", oc='" + oc + '\'' +
                ", os='" + os + '\'' +
                ", ru='" + ru + '\'' +
                ", rud='" + rud + '\'' +
                ", cpc='" + cpc + '\'' +
                ", cpn='" + cpn + '\'' +
                ", fiol=" + fiol +
                ", t='" + t + '\'' +
                ", bpiu='" + bpiu + '\'' +
                ", cc='" + cc + '\'' +
                ", ofq='" + ofq + '\'' +
                ", nq='" + nq + '\'' +
                ", ynq=" + ynq +
                ", trq=" + trq +
                '}';
    }
}
