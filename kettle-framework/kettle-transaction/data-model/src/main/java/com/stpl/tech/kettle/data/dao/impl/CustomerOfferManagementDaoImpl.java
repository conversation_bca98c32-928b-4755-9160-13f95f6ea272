/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 *
 */
package com.stpl.tech.kettle.data.dao.impl;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.Stack;
import java.util.stream.Collectors;

import javax.persistence.NoResultException;
import javax.persistence.Query;

import com.stpl.tech.kettle.core.CampaignStrategy;
import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.core.cache.CampaignCache;
import com.stpl.tech.kettle.customer.dao.CustomerDao;
import com.stpl.tech.kettle.customer.dao.LoyaltyDao;
import com.stpl.tech.kettle.customer.dao.SubscriptionPlanDao;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.model.LoyaltyScore;
import com.stpl.tech.kettle.data.model.SubscriptionPlan;
import com.stpl.tech.kettle.domain.model.RevalidationReason;
import com.stpl.tech.kettle.report.metadata.model.DayType;
import com.stpl.tech.kettle.offer.strategy.PercentageItemStrategy;
import com.stpl.tech.master.core.external.offer.dao.OfferMetaDataManagementDao;
import com.stpl.tech.master.data.converter.MasterDataConverter;
import com.stpl.tech.master.data.model.CouponDetailData;
import com.stpl.tech.master.data.model.CouponDetailMappingData;
import com.stpl.tech.master.data.model.OfferDetailData;
import com.stpl.tech.master.data.model.OfferMetadata;
import com.stpl.tech.master.domain.model.CampaignDetail;
import com.stpl.tech.master.domain.model.CustomerAppliedCouponDetail;
import com.stpl.tech.master.util.BudgetCategoryConstants;
import com.stpl.tech.master.util.MasterUtil;
import lombok.SneakyThrows;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.LocalTime;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.core.CouponMappingHelper;
import com.stpl.tech.kettle.core.SignUpTimeSlotCount;
import com.stpl.tech.kettle.core.cache.MetadataCache;
import com.stpl.tech.kettle.core.notification.OfferOrder;
import com.stpl.tech.kettle.customer.service.CustomerService;
import com.stpl.tech.kettle.data.dao.CustomerOfferManagementDao;
import com.stpl.tech.kettle.data.model.CustomerCampaignJourney;
import com.stpl.tech.kettle.data.model.CustomerCampaignOfferDetail;
import com.stpl.tech.kettle.data.model.WebOfferDetail;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.CustomerDineInView;
import com.stpl.tech.kettle.domain.model.CustomerEmailData;
import com.stpl.tech.kettle.domain.model.CustomerOneViewData;
import com.stpl.tech.kettle.domain.model.CustomerTransactionViewData;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.offer.strategy.OfferActionStrategy;
import com.stpl.tech.kettle.offer.strategy.OfferStrategyHelper;
import com.stpl.tech.master.core.CouponMappingType;
import com.stpl.tech.master.core.FrequencyOfferType;
import com.stpl.tech.master.core.OfferCategoryType;
import com.stpl.tech.master.core.WebErrorCode;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.OfferValidationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.offer.dao.OfferManagementDao;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.CouponMapping;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.OfferComboItem;
import com.stpl.tech.master.domain.model.OfferComboItemGroup;
import com.stpl.tech.master.domain.model.OfferDetail;
import com.stpl.tech.master.domain.model.OfferMetaDataType;
import com.stpl.tech.master.domain.model.SwitchStatus;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 *
 */
@Repository
public class CustomerOfferManagementDaoImpl extends AbstractDaoImpl implements CustomerOfferManagementDao {

	@Autowired
	private MetadataCache cache;

	@Autowired
	private MasterDataCache masterCache;

	@Autowired
	private CustomerService customerService;

	@Autowired
	private CampaignCache campaignCache;

	@Autowired
	private OfferManagementDao offerManagementDao;

	@Autowired
	private SubscriptionPlanDao subscriptionPlanDao;
	@Autowired
	private CustomerDao customerDao;
	@Autowired
	private LoyaltyDao loyaltyDao;

	@Autowired
	private EnvironmentProperties props;

	@Autowired
	private OfferMetaDataManagementDao offerMetaDataManagementDao;

	private static final Logger LOG = LoggerFactory.getLogger(CustomerOfferManagementDaoImpl.class);

	@Override
	public OfferOrder applyCoupon(OfferOrder offerOrder, BigDecimal offerValue)
            throws DataNotFoundException, OfferValidationException {
		CouponDetail coupon = offerManagementDao.getCouponDetail(offerOrder.getCouponCode(), false, true, false);
		if(props.getPartnerPaymentOfferCode().equalsIgnoreCase(offerOrder.getCouponCode()) &&
				Objects.nonNull(offerOrder.getDreamFolksVoucherDetails()) &&
				offerOrder.getDreamFolksVoucherDetails().getRedeemedQty() > 0){
			coupon.getOffer().setMinQuantity(offerOrder.getDreamFolksVoucherDetails().getRedeemedQty());
			coupon.getOffer().setMaxQuantity(offerOrder.getDreamFolksVoucherDetails().getRedeemedQty());
		}
		if (isApplicable(offerOrder, coupon)) {

			offerOrder = modifyOrder(offerOrder, coupon, offerValue);
		}
		return offerOrder;
	}

	private Map<String, OrderItem> findComboItems(OfferOrder offerOrder, CouponDetail coupon)
			throws ClassNotFoundException, OfferValidationException {
		Map<String, OrderItem> foundItems = new HashMap<>();
		OfferDetail offerDetail = coupon.getOffer();
		Stack<OfferComboItemGroup> groups = new Stack<>();
		Stack<OfferComboItemGroup> groupsNotFound = new Stack<>();
		for (IdCodeName mapping : offerDetail.getMetaDataMappings()) {
			if (OfferMetaDataType.valueOf(mapping.getName()).equals(OfferMetaDataType.COMBO_ITEM_GROUP)
					&& mapping.getStatus().equalsIgnoreCase(SwitchStatus.ACTIVE.name())) {
				OfferComboItemGroup comboGroup = (OfferComboItemGroup) JSONSerializer.toJSON(mapping.getCode(),
						Class.forName(mapping.getType()));
				groups.push(comboGroup);
			}
		}
		while (!groups.empty()) {
			OfferComboItemGroup group = groups.pop();
			boolean flag = false;
			for (OrderItem item : offerOrder.getOrder().getOrders()) {
				OfferComboItem comboItem = adapt(item);
				if (group.getItems().contains(comboItem)) {
					foundItems.put(comboItem.toString(), item);
					flag = true;
					break;
				}
			}
			if (!flag) {
				groupsNotFound.push(group);
			}
		}

		if (groupsNotFound.empty()) {
			return foundItems;
		} else {
			StringBuilder message = new StringBuilder(
					"Please add one item from the following group(s) with the suggested quantity: <br/>");
			int count = 0;
			while (!groupsNotFound.empty()) {
				message.append("GROUP_").append(++count).append(": ").append(groupsNotFound.pop().toString())
						.append("<br/>");
			}
			throw new OfferValidationException(message.toString(), WebErrorCode.INSUFFICIENT_DATA);
		}
	}

	private OfferComboItem adapt(OrderItem item) {
		return new OfferComboItem(item.getProductId(), item.getQuantity(), item.getDimension(), item.getProductName());
	}

	private OfferOrder modifyOrder(OfferOrder offerOrder, CouponDetail coupon, BigDecimal offerValue) throws OfferValidationException {
		long startTime = System.currentTimeMillis();
		try {
			String offerType = coupon.getOffer().getType();
			int discountValue = coupon.getOffer().getOfferValue();
			//Handling for Chaayos Cash Offer
			offerValue = handlingChaayosCashOffer(offerType,discountValue,offerOrder,offerValue);

			Map<String, OrderItem> foundItems = null;

			if (OfferCategoryType.COMBO_STRATEGY.name().equalsIgnoreCase(offerType)) {
				foundItems = findComboItems(offerOrder, coupon);
			}
			//Handling Loyalty Burn Offers
			if(BudgetCategoryConstants.LOYALTY_OFFER.equals(coupon.getOffer().getBudgetCategory())){
				checkForLoyaltyBurnOffers(offerOrder,coupon);
			}
			Class<? extends OfferActionStrategy> strategyClass = OfferStrategyHelper
					.getStrategy(OfferCategoryType.valueOf(offerType));
			if(offerValue != null){
				strategyClass.newInstance().applyStrategy(offerOrder, coupon, masterCache, foundItems, offerValue);
			} else {
				strategyClass.newInstance().applyStrategy(offerOrder, coupon, masterCache, foundItems);
			}
			if(AppUtils.CHAAYOS_CASH_OFFER_CODE.contains(offerOrder.getCouponCode())){
				offerOrder.getOrder().setCashRedeemed(offerOrder.getOrder().getTransactionDetail().getDiscountDetail().getDiscount().getValue());
			}
			offerOrder.setAwardLoyalty(
					coupon.getOffer().getRemoveLoyalty() != null && coupon.getOffer().getRemoveLoyalty() ? false
							: true);
			offerOrder.getOrder().setBypassLoyateaAward(!offerOrder.getAwardLoyalty());
			offerOrder.setOtpRequired(coupon.getOffer().getOtpRequired());
			offerOrder.getOrder().setOfferAccountType(
					coupon.getOffer().getBudgetCategory());
			if(Objects.nonNull(coupon.getOffer().getLoyaltyBurnPoints()) && coupon.getOffer().getLoyaltyBurnPoints() > 0 &&
			    offerOrder.getOrder().getOfferAccountType().equals(BudgetCategoryConstants.LOYALTY_OFFER)){
				offerOrder.getOrder().setPointsRedeemed((coupon.getOffer().getLoyaltyBurnPoints())*(-1));
			}
			addToCouponAppliedCacheAndCheckForRevalidation(offerOrder,coupon);
		} catch (OfferValidationException e) {
			throw e;
		} catch (Exception e) {
			LOG.error("ERROR while modifying order", e);
			offerOrder.setError(true);
			offerOrder.setErrorMessage("ERROR while modifying order");
			offerOrder.setOrder(null);
		}
		LOG.info("COUPON_APPLY modifyOrder ends in {}ms", System.currentTimeMillis()-startTime);
		return offerOrder;
	}

	@Override
	public void checkCouponMapping(Set<CouponMapping> mappingList, List<CouponMapping> values, CouponMappingType type)
			throws OfferValidationException, DataNotFoundException {
		if (mappingList != null && !mappingList.isEmpty()) {
			for (CouponMapping value : values) {
				if (mappingList.contains(value)) {
					return;
				}
			}
			throw new OfferValidationException(
					CouponMappingHelper.getErrorStatement(type, mappingList, masterCache, cache),
					CouponMappingHelper.getErrorCode(type));
		}
	}

	public List<Integer> getPaymentModes(Set<CouponMapping> values) {
		if (values == null || values.size() == 0) {
			return null;
		}
		List<Integer> ids = new ArrayList<>();
		for (CouponMapping value : values) {
			try {
				ids.add(Integer.valueOf(value.getValue()));
			} catch (Exception e) {
				LOG.error("Error in checking coupon payment mode mappings:", e);
			}
		}
		return ids != null && ids.size() > 0 ? ids : null;
	}

	private boolean isApplicable(OfferOrder offerOrder, CouponDetail coupon)
            throws OfferValidationException, DataNotFoundException {
        long startTime = System.currentTimeMillis();
		checkPreConditions(offerOrder, coupon);
		checkCouponMappings(offerOrder, coupon);
		checkOfferSubscriptionValidity(offerOrder, coupon);
		checkOfferDayAndTiming(offerOrder, coupon);
		LOG.info("Validating is coupon eligible ends in {}ms", System.currentTimeMillis()-startTime);
		return true;
	}

	public void checkCouponForBrandId(OfferOrder offerOrder) throws OfferValidationException {
		if (Objects.nonNull(offerOrder) && !StringUtils.isEmpty(offerOrder.getCouponCode())) {
			CouponDetailData coupon = offerManagementDao.getCoupon(offerOrder.getCouponCode());
			if (Objects.nonNull(coupon)) {
				OfferDetailData offer = coupon.getOfferDetail();
				if (Objects.nonNull(offer)) {
					Integer brandId = offer.getBrandId();
					if (Objects.isNull(brandId) || !brandId.equals(offerOrder.getOrder().getBrandId())) {
						String message = String.format("Coupon Code %s is Not Valid for this Brand!", offerOrder.getCouponCode());
						LOG.error(message);
						offerOrder.setError(true);
						offerOrder.setErrorMessage(message);
						offerOrder.setErrorCode(WebErrorCode.INVALID_COUPON.getCode());
						throw new OfferValidationException(message, WebErrorCode.INVALID_COUPON);
					}
				}
			}
		}
	}

	private void checkOfferSubscriptionValidity(OfferOrder offerOrder, CouponDetail coupon)
			throws OfferValidationException {
		long startTime = System.currentTimeMillis();
		if(Objects.nonNull(offerOrder.isSkipSubscriptionValidation()) && offerOrder.isSkipSubscriptionValidation()){
			return;
		}
		if (Boolean.TRUE.equals(Objects.nonNull(coupon.getOffer().isFrequencyApplicable())
				&& coupon.getOffer().isFrequencyApplicable()
				&& Objects.nonNull(coupon.getOffer().getFrequencyCount()))
				&& coupon.getOffer().getFrequencyCount() > 0) {
			if (FrequencyOfferType.TIME_BASED.name().equals(coupon.getOffer().getFrequencyStrategy())) {
				if (customerService.getValidOfferFlag(offerOrder, coupon)) {
					throw new OfferValidationException("Maximum Usage Count Reached for the Customer",
							WebErrorCode.MAX_LIMIT_REACHED);
				}
			} else if (FrequencyOfferType.QUANTITY_BASED.name().equals(coupon.getOffer().getFrequencyStrategy())) {
				SubscriptionPlan subscriptionPlan = subscriptionPlanDao.getActiveSubscription(offerOrder.getOrder().getCustomerId(),
						offerOrder.getCouponCode());
				if (subscriptionPlan.getOverAllFrequency().compareTo(subscriptionPlan.getFrequencyLimit()) >= 0) {
					throw new OfferValidationException("Maximum Available Quantity Already Availed",
							WebErrorCode.MAX_LIMIT_REACHED);
				} else if (subscriptionPlan.getOverAllFrequency().compareTo(subscriptionPlan.getFrequencyLimit()) < 0) {
					coupon.getOffer().setMinQuantity(
							Math.abs(AppUtils.subtract(subscriptionPlan.getOverAllFrequency(),subscriptionPlan.getFrequencyLimit()).intValue()));
					return;
				}
				throw new OfferValidationException("Maximum Available Quantity Already Availed",
						WebErrorCode.MAX_LIMIT_REACHED);
			}
			else if(FrequencyOfferType.TIME_QUANTITY_BASED.name().equals(coupon.getOffer().getFrequencyStrategy())){
				SubscriptionPlan subscriptionPlan = subscriptionPlanDao.getActiveSubscription(offerOrder.getOrder().getCustomerId(),
						offerOrder.getCouponCode());
				checkUsageCount(subscriptionPlan,coupon,offerOrder);
				checkQuantityLimit(subscriptionPlan,coupon);
			}
		}
		LOG.info("COUPON_APPLY checkOfferSubscriptionValidity ends in {}ms", System.currentTimeMillis()-startTime);
	}

	private void checkQuantityLimit(SubscriptionPlan subscriptionPlan,CouponDetail coupon)throws OfferValidationException{
		if (subscriptionPlan.getOverAllFrequency().compareTo(subscriptionPlan.getFrequencyLimit()) >= 0) {
			throw new OfferValidationException("Maximum Available Quantity Already Availed",
					WebErrorCode.MAX_LIMIT_REACHED);
		}
	}

	private void checkUsageCount(SubscriptionPlan subscriptionPlan,CouponDetail coupon,OfferOrder offerOrder) throws OfferValidationException{
		boolean maximumUsageReached ;
		if(Objects.nonNull(subscriptionPlan)) {
			maximumUsageReached = customerService.doOfferValidation(customerDao.getOrderDetailViaOffer(offerOrder.getOrder().getCustomerId(), offerOrder.getCouponCode(),
							subscriptionPlan.getPlanStartDate(), Objects.nonNull(coupon.getOffer().getApplicableHour()) ? coupon.getOffer().getApplicableHour() : 0),
					coupon);
		} else {
			maximumUsageReached = customerService.doOfferValidation(customerDao.getOrderDetailViaOffer(offerOrder.getOrder().getCustomerId(), offerOrder.getCouponCode(),
							coupon.getStartDate(),Objects.nonNull(coupon.getOffer().getApplicableHour()) ? coupon.getOffer().getApplicableHour() : 0),
					coupon);
		}
		if (maximumUsageReached) {
			throw new OfferValidationException("Maximum Usage Count Reached for the Customer",
					WebErrorCode.MAX_LIMIT_REACHED);
		}
	}

	private boolean checkPreConditions(OfferOrder offerOrder, CouponDetail coupon)
            throws OfferValidationException, DataNotFoundException {
		if (Objects.isNull(coupon)) {
			throw new OfferValidationException("Coupon Not Found", WebErrorCode.INVALID_COUPON);
		} else if (!AppConstants.ACTIVE.equals(coupon.getStatus().toString())) {
			throw new OfferValidationException("Coupon " + coupon.getCode() + " has expired", WebErrorCode.EXPIRED);
		} else if (!AppConstants.ACTIVE.equals(coupon.getOffer().getStatus().toString())) {
			throw new OfferValidationException("Offer for " + coupon.getCode() + " has expired", WebErrorCode.EXPIRED);
		}
		checkCouponForBrandId(offerOrder);
		offerOrder.setOfferDescription(coupon.getOffer().getDescription());
		checkZeroTaxProducts(offerOrder, coupon);
		checkCustomer(offerOrder, coupon);
		checkReUsablity(offerOrder, coupon);
		checkUsageLimit(coupon);
		if(coupon.getOffer().isValidateCustomer()) {
			checkCustomerUsageLimit(offerOrder.getOrder().getCustomerId(), coupon);
		}
		checkCouponExpiry(coupon);
		checkOrderAmount(offerOrder, coupon);
		checkMinimumItems(offerOrder, coupon);
		checkManualOverride(offerOrder, coupon);
		return true;
	}

	private void checkZeroTaxProducts(OfferOrder offerOrder, CouponDetail coupon) throws OfferValidationException {
		long startTime = System.currentTimeMillis();
		for (OrderItem item : offerOrder.getOrder().getOrders()) {
			if(!AppConstants.ZERO_TAX_PRODUCTS_SET.contains(item.getProductId())) {
				if (AppUtils.isGiftCard(item.getCode()) || AppUtils.isZeroTaxProduct(item.getCode())
						|| (Objects.nonNull(masterCache.getSubscriptionProductDetail(item.getProductId()))
						&& !coupon.getOffer().getType().equalsIgnoreCase(OfferCategoryType.PERCENTAGE_ITEM_STRATEGY.name()) && !coupon.getOffer().getType().equalsIgnoreCase(OfferCategoryType.FLAT_ITEM_STRATEGY.name())) ) {
					throw new OfferValidationException("Offer is not applicable on " + item.getProductName(),
							WebErrorCode.INSUFFICIENT_DATA);
				}
			}
		}
		LOG.info("checkZeroTaxProducts ends in {}ms", System.currentTimeMillis()-startTime);
	}

	public void checkCouponExpiry(CouponDetail coupon) throws OfferValidationException {
		long startTime = System.currentTimeMillis();
		Date today = AppUtils.getCurrentDate();
		if (today.compareTo(coupon.getStartDate()) < 0) {
			throw new OfferValidationException(
					"Coupon can be availed only after "
							+ new SimpleDateFormat("dd MMM yyyy").format(coupon.getStartDate()),
					WebErrorCode.NOT_AVAILABLE);
		}
		if (today.compareTo(coupon.getEndDate()) > 0) {
			throw new OfferValidationException(
					"Coupon has expired on " + new SimpleDateFormat("dd MMM yyyy").format(coupon.getEndDate()),
					WebErrorCode.EXPIRED);
		}
		if (today.compareTo(coupon.getOffer().getEndDate()) > 0) {
			throw new OfferValidationException(
					"Offer has expired on " + new SimpleDateFormat("dd MMM yyyy").format(coupon.getEndDate()),
					WebErrorCode.EXPIRED);
		}
		LOG.info("COUPON_APPLY checkCouponExpiry ends in {}ms", System.currentTimeMillis()-startTime);
	}

	public void checkUsageLimit(CouponDetail coupon) throws OfferValidationException {
		long startTime = System.currentTimeMillis();
		if (coupon.getMaxUsage() != null && coupon.getMaxUsage().compareTo(coupon.getUsage()) < 1) {
			throw new OfferValidationException("Coupon has crossed maximum usage Limit", WebErrorCode.EXPIRED);
		}
		LOG.info("COUPON_APPLY checkUsageLimit ends in {}ms", System.currentTimeMillis()-startTime);
	}

	public void checkCustomerUsageLimit(int customerId, CouponDetail coupon) throws OfferValidationException {
		long startTime = System.currentTimeMillis();
		if (coupon.getMaxCustomerUsage() != null) {
			Integer customerUsageCount = customerService.checkCouponUsage(customerId, coupon.getCode());
			if (customerUsageCount != null && coupon.getMaxCustomerUsage().compareTo(customerUsageCount) < 1)
				throw new OfferValidationException("Coupon has crossed maximum usage Limit for the customer",
						WebErrorCode.EXPIRED);
		}
	}

	@SneakyThrows
    private void checkMinimumItems(OfferOrder offerOrder, CouponDetail coupon) throws OfferValidationException {
		long startTime = System.currentTimeMillis();
		int totalItems = 0;
		if(props.enableMinCountApplicableProductsFilter() &&coupon.getOffer().getType().equalsIgnoreCase(OfferCategoryType.PERCENTAGE_ITEM_STRATEGY.name())){
			Class<? extends OfferActionStrategy> strategyClass = OfferStrategyHelper
					.getStrategy(OfferCategoryType.valueOf(coupon.getOffer().getType()));
			PercentageItemStrategy percentageItemStrategy = (PercentageItemStrategy) strategyClass.newInstance();
			List<Integer> applicableProducts =  percentageItemStrategy.getApplicableProducts(coupon,offerOrder,masterCache);
			for (OrderItem item : offerOrder.getOrder().getOrders()) {
				if(applicableProducts.isEmpty() || applicableProducts.contains(item.getProductId())){
					totalItems += item.getQuantity();
				}
			}
		}else{
			for (OrderItem item : offerOrder.getOrder().getOrders()) {
				totalItems += item.getQuantity();
			}
		}

		if (totalItems < coupon.getOffer().getMinItemCount()) {
			throw new OfferValidationException(
					"This Coupon requires minimum " + coupon.getOffer().getMinItemCount() + " items",
					WebErrorCode.INSUFFICIENT_DATA);
		}
		LOG.info("COUPON_APPLY checkMinimumItems ends in {}ms", System.currentTimeMillis()-startTime);
	}

	private void checkReUsablity(OfferOrder offerOrder, CouponDetail coupon) throws OfferValidationException {
		long startTime = System.currentTimeMillis();
		if (!coupon.isReusable() && coupon.getUsage() > 1) {
			throw new OfferValidationException("This Coupon cannot be used again.", WebErrorCode.EXPIRED);
		}

		if (!coupon.isReusableByCustomer()) {
			if (offerOrder.getOrder().getCustomerId() != null && !customerService
					.getOfferDetail(offerOrder.getOrder().getCustomerId(), offerOrder.getCouponCode()).isEmpty()) {
				throw new OfferValidationException(
						"Customer has already availed the Coupon, This Coupon cannot be used again.",
						WebErrorCode.EXPIRED);
			}
		}
		LOG.info("COUPON_APPLY checkReUsablity ends in {}ms", System.currentTimeMillis()-startTime);
	}

	private void checkCustomer(OfferOrder offerOrder, CouponDetail coupon)
			throws OfferValidationException, DataNotFoundException {
		long startTime = System.currentTimeMillis();
		OfferDetail offer = coupon.getOffer();
		if (!offer.isValidateCustomer()) {
			return;
		}
		if (offerOrder.getOrder().getCustomerId() == null || offerOrder.getOrder().getCustomerId() <= 5) {
			throw new OfferValidationException("Coupon can be availed by registered customers only",
					WebErrorCode.CUSTOMER_NOT_FOUND);
		}
		if (offerOrder.getOrder().getCustomerId() != null) {
			offerOrder
					.setContact(customerService.getCustomer(offerOrder.getOrder().getCustomerId()).getContactNumber());
		}
		if (offer.getOfferScope().equals(AppConstants.OFFER_SCOPE_CORPORATE) && offer.getEmailDomain() != null) {
			Customer customer = null;
			try {
				customer = customerService.getCustomer(offerOrder.getOrder().getCustomerId());
			} catch (DataNotFoundException e) {
				LOG.error("Customer Not Found", e);
			}
			if (customer != null && customer.isEmailVerified() && customer.getEmailId() != null
					&& customer.getEmailId().toLowerCase().endsWith(offer.getEmailDomain())) {
				return;
			} else {
				throw new OfferValidationException(
						"Corporate Coupon cannot be availed as the registered customer email Id is incorrect or not verified",
						WebErrorCode.INSUFFICIENT_DATA);
			}
		}
		LOG.info("COUPON_APPLY checkCustomer ends in {}ms", System.currentTimeMillis()-startTime);
	}

	private void checkManualOverride(OfferOrder offerOrder, CouponDetail coupon) throws OfferValidationException {
		long startTime = System.currentTimeMillis();
		if (coupon.isManualOverride()) {
			offerOrder.setManualOverride(true);
			throw new OfferValidationException(
					"Coupon is to be punched manually, Offer Description: " + coupon.getOffer().getDescription(),
					WebErrorCode.NOT_AVAILABLE);
		}
		LOG.info("COUPON_APPLY checkManualOverride ends in {}ms", System.currentTimeMillis()-startTime);
	}

	private void checkOfferDayAndTiming(OfferOrder offerOrder, CouponDetail coupon) {
		Map<String, Map<String, OfferMetadata>> offerMetaDataMap = groupOfferMetadataByType(coupon.getOffer().getId());
		if (CollectionUtils.isEmpty(offerMetaDataMap)) {
			return;
		}
		validateCurrentDay(offerMetaDataMap.get(OfferMetaDataType.DAYS.name()));
		validateCurrentTime(offerMetaDataMap.get(OfferMetaDataType.TIME_RANGE.name()));
	}

	private Map<String, Map<String, OfferMetadata>> groupOfferMetadataByType(Integer offerId) {
		List<String> mappingTypes = List.of(OfferMetaDataType.DAYS.name(), OfferMetaDataType.TIME_RANGE.name());
		List<OfferMetadata> offerMetadataList = offerMetaDataManagementDao.findAllByOfferIdAndMappingTypes(
				offerId, mappingTypes, AppConstants.ACTIVE
		);
		if (CollectionUtils.isEmpty(offerMetadataList)) {
			return new HashMap<>();
		}
		Map<String, Map<String, OfferMetadata>> result = new HashMap<>();
		for (OfferMetadata metadata : offerMetadataList) {
			result.computeIfAbsent(metadata.getMappingType(), k -> new HashMap<>())
					.put(metadata.getMappingValue(), metadata);
		}
		return result;
	}

	@SneakyThrows
	private void validateCurrentDay(Map<String, OfferMetadata> dayMetadataMap) {
		if ( CollectionUtils.isEmpty(dayMetadataMap) ) {
			LOG.info("Day Metadata Map is empty");
			return;
		}

		Set<DayType> validDays = dayMetadataMap.keySet().stream()
				.map(DayType::fromString)
				.sorted(Comparator.comparingInt(DayType::getCalendarDay))
				.collect(Collectors.toCollection(LinkedHashSet::new));

		DayType today = DayType.today();
		LOG.info("Valid Days for this Offer : {}", validDays);
		LOG.info("Today is : {}", today);
		boolean isTodayValid = validDays.contains(today);
		if (!isTodayValid) {
			throw new OfferValidationException(getOfferNotValidDayMessage(validDays), WebErrorCode.COUPON_NOT_APPLICABLE_CURRENT_DAY);
		}
	}

	private String getOfferNotValidDayMessage(Set<DayType> validDays) {
		StringBuilder message = new StringBuilder();
		if ( !validDays.isEmpty() ) {
			message.append("Offer only valid on ");
			validDays.forEach(day -> message.append(day.getDisplayName()).append(", "));
			message.delete(message.length() - 2, message.length());
		}
		return message.toString();
	}

	@SneakyThrows
    private void validateCurrentTime(Map<String, OfferMetadata> timeMetadataMap) {
		if ( CollectionUtils.isEmpty(timeMetadataMap) ) {
			LOG.info("Time Metadata Map is empty");
			return;
		}
		for (String range : timeMetadataMap.keySet()) {
			if (range == null || !range.contains(AppConstants.OFFER_TIMING_SEPARATOR)) {
				throw new OfferValidationException("current time range is not valid", WebErrorCode.COUPON_NOT_APPLICABLE_CURRENT_TIME);
			}
			String[] startEndTime = range.split(AppConstants.OFFER_TIMING_SEPARATOR);
			if (startEndTime.length != 2) {
				throw new OfferValidationException("current time range is not valid", WebErrorCode.COUPON_NOT_APPLICABLE_CURRENT_TIME);
			}
			String startTime = startEndTime[0].trim();
			String endTime = startEndTime[1].trim();
			LocalTime currentLocalTime = MasterUtil.getCurrentLocalTime(AppConstants.DEFAULT_TIME_ZONE);
			LocalTime startLocalTime = LocalTime.parse(startTime);
			LocalTime endLocalTime = LocalTime.parse(endTime);
			LOG.info("Current Time is : {}", currentLocalTime);
			LOG.info("Offer Valid from : {} to {}", startLocalTime, endLocalTime);
			if (currentLocalTime.isBefore(startLocalTime) || currentLocalTime.isAfter(endLocalTime)) {
				throw new OfferValidationException(getOfferNotValidTimeMessage(startTime, endTime), WebErrorCode.COUPON_NOT_APPLICABLE_CURRENT_TIME);
			}
		}
	}

	private String getOfferNotValidTimeMessage(String start, String end) {
		return "Offer only valid from " + start + " to " + end;
	}


	private void checkOrderAmount(OfferOrder offerOrder, CouponDetail coupon) throws OfferValidationException {
		long startTime = System.currentTimeMillis();
		// minimum order amount check
		if (offerOrder.getOrder().getTransactionDetail().getPaidAmount()
				.compareTo(new BigDecimal(coupon.getOffer().getMinValue())) == -1) {
			throw new OfferValidationException(
					"Minimum Paid Amount to apply this coupon is Rs." + coupon.getOffer().getMinValue(),
					WebErrorCode.INSUFFICIENT_DATA);
		}
		// maximum order amount check
		if (coupon.getOffer().getMaxBillValue() != null
				&& BigDecimal.ZERO.compareTo(coupon.getOffer().getMaxBillValue()) != 0 && offerOrder.getOrder()
						.getTransactionDetail().getPaidAmount().compareTo(coupon.getOffer().getMaxBillValue()) == 1) {
			throw new OfferValidationException(
					"Maximum Paid Amount to apply this coupon is Rs." + coupon.getOffer().getMinValue(),
					WebErrorCode.INSUFFICIENT_DATA);
		}
		LOG.info("COUPON_APPLY checkOrderAmount ends in {}ms", System.currentTimeMillis()-startTime);
	}

	private void checkCouponMappings(OfferOrder offerOrder, CouponDetail coupon)
			throws OfferValidationException, DataNotFoundException {
		long startTime = System.currentTimeMillis();
		Customer customer = null;
		if (Objects.nonNull(offerOrder.getContact())) {
			customer = customerService.getCustomer(offerOrder.getContact());
		} else if(Objects.nonNull(offerOrder.getOrder().getCustomerId())) {
			customer = customerService.getCustomer(offerOrder.getOrder().getCustomerId());
		}
		if (Objects.nonNull(coupon.getCouponApplicability())) {
			DateTime limitDate = AppUtils.getCurrentTimeIST().minusDays(coupon.getCouponApplicability());
			if (Objects.nonNull(customer)) {
				Integer offerId =  offerManagementDao.getCouponOfferId(coupon.getCode());
				if(Objects.nonNull(offerId))
				{
					List<String> couponCodes = getCustomerPreviousAppliedCoupon(customer.getId(), limitDate.toDate(), coupon.getCode());
					if(Objects.nonNull(couponCodes)){
						boolean haveApplied = offerManagementDao.checkOfferIdForCoupon(couponCodes,offerId);
						if(!haveApplied){
							throw new OfferValidationException("Applicability reached, coupon can be applied " + coupon.getCouponApplicability() +
									" days after it is used. ", WebErrorCode.COUPON_APPLICABILITY_LIMIT);
						}
					}
				}
			}
		}
		boolean isMappingExistForCoupon = offerManagementDao.isMappingExistForThisCoupon(coupon.getId());
		LOG.info("Is Mapping Exist For Coupon : {}",isMappingExistForCoupon);
		if(Objects.isNull(coupon.getMappings()) || coupon.getMappings().isEmpty()) {
			if(!isMappingExistForCoupon){
				return;
			}
		}
		for (CouponMappingType type : CouponMappingType.values()) {
			if (!type.equals(CouponMappingType.FIRST_ORDER) && !type.equals(CouponMappingType.PAYMENT_MODE) && !type.equals(CouponMappingType.FREEBIE_PRODUCT)) {
				if(type.equals(CouponMappingType.NEW_CUSTOMER) && coupon.getMappings().containsKey(CouponMappingType.NEW_CUSTOMER.name())) {
					boolean isNew = customerService.getCustomerOrders(customer.getId());
					if(isNew){
						offerOrder.setNewCustomer(true);
						if (!verifyForAcqSource(coupon, customer))
							throw new OfferValidationException("Coupon is not valid for the customer with acquisition source : "+ customer.getAcquisitionSource(),
									WebErrorCode.COUPON_APPLICABLE_NEW_CUSTOMER_ACQUISITION);
					}
				}
				if(CouponMappingType.CUSTOMER.equals(type) && Objects.nonNull(offerOrder.getOrder().getCustomerId())) {
					//TODO get coupon mapping for customerid and add to mappings list
					List<CouponDetailMappingData> mappings = offerManagementDao.getCouponMappingsForCustomerId(coupon.getId(), offerOrder.getOrder().getCustomerId());
					if(!mappings.isEmpty()){
						List<CouponMapping> mappingList = MasterDataConverter.convert(mappings);
						coupon.getCouponMappingList().addAll(mappingList);
						if(!coupon.getMappings().containsKey(CouponMappingType.CUSTOMER.name())){
							coupon.getMappings().put(CouponMappingType.CUSTOMER.name(), new HashSet<>());
						}
						coupon.getMappings().get(CouponMappingType.CUSTOMER.name()).addAll(mappingList);
					}
					else{
						long count = offerManagementDao.getCouponMappingsCountForCustomerId(coupon.getId());
						if(count>0){
							throw new OfferValidationException("Customer is not eligible for the offer. This offer is for selective customers only.",WebErrorCode.CUSTOMER_NOT_FOUND);
						}
						else{
							if(!coupon.getMappings().containsKey(CouponMappingType.CUSTOMER.name())){
								coupon.getMappings().put(CouponMappingType.CUSTOMER.name(), new HashSet<>());
							}
						}
					}
				}
				if(CouponMappingType.CONTACT_NUMBER.equals(type) && Objects.nonNull(offerOrder.getContact())) {
					//TODO get coupon mapping for contact number and add to mappings list
					List<CouponDetailMappingData> mappings = offerManagementDao.getCouponMappingsForContactNumber(coupon.getId(), offerOrder.getContact());
					if(!mappings.isEmpty()){
						List<CouponMapping> mappingList = MasterDataConverter.convert(mappings);
						coupon.getCouponMappingList().addAll(mappingList);
						if(!coupon.getMappings().containsKey(CouponMappingType.CONTACT_NUMBER.name())){
							coupon.getMappings().put(CouponMappingType.CONTACT_NUMBER.name(), new HashSet<>());
						}
						coupon.getMappings().get(CouponMappingType.CONTACT_NUMBER.name()).addAll(mappingList);
					}
					else{
						long count = offerManagementDao.getCouponMappingsCountForContactNumber(coupon.getId());
						if(count>0){
							throw new OfferValidationException("Contact Number is not eligible for the offer. This offer is for selective customers only.",WebErrorCode.CUSTOMER_NOT_FOUND);
						}
						else{
							if(!coupon.getMappings().containsKey(CouponMappingType.CONTACT_NUMBER.name())){
								coupon.getMappings().put(CouponMappingType.CONTACT_NUMBER.name(), new HashSet<>());
							}
						}
					}
				}
				checkCouponMapping(coupon.getMappings().get(type.name()),
						CouponMappingHelper.getValueOf(type, offerOrder, masterCache), type);
			} else if (type.equals(CouponMappingType.PAYMENT_MODE) && coupon.getMappings().containsKey(CouponMappingType.PAYMENT_MODE.name())) {
				offerOrder.getOrder().setAllowedPaymentIds(getPaymentModes(coupon.getMappings().get(type.name())));
			} else if (type.equals(CouponMappingType.FIRST_ORDER) && coupon.getMappings().containsKey(CouponMappingType.FIRST_ORDER.name())) {
				// check order count in customer object
				Set<CouponMapping> mappingList = coupon.getMappings().get(type.name());
				if (mappingList != null && !mappingList.isEmpty()) {
					if (customer.getOrderCount() != null && customer.getOrderCount() > 0) {
						throw new OfferValidationException("Coupon is applicable on first order only.",
								WebErrorCode.COUPON_APPLICABLE_FIRST_ORDER);
					}
				}
			}
		}
		LOG.info("COUPON_APPLY checkCouponMappings ends in {}ms", System.currentTimeMillis()-startTime);
	}

	private boolean verifyForAcqSource(CouponDetail coupon,Customer customer){
		int count = 0;
		for(IdCodeName source : coupon.getOffer().getMetaDataMappings()){
			if(CouponMappingType.ACQUISITION_SOURCE.name().equalsIgnoreCase(source.getName())){
				count++;
				if(Objects.nonNull(customer.getAcquisitionSource()) && Objects.nonNull(source.getCode())){
					if (customer.getAcquisitionSource().equalsIgnoreCase(source.getCode())) {
						return true;
					}
				}
			}
		}
		if(count==0)
			return true;
		return false;
	}

	@Override
	public boolean availedCashOffer(Integer customerId, Date startOfBusinessDay, Collection<String> offerCodes, int maxUsageAllowed) {
		try {
			Query query = manager.createQuery(
					"select orderId From OrderDetail data where data.billingServerTime >= :startOfDay and data.customerId = :customerId and data.offerCode IN (:offerCodes)");
			query.setParameter("startOfDay", startOfBusinessDay);
			query.setParameter("customerId", customerId);
			query.setParameter("offerCodes", offerCodes);
			List<Object> o = query.getResultList();
			return o != null && o.size() >= maxUsageAllowed;
		} catch (Exception e) {
			LOG.info("Error while checking if customer {} has availed {} since {}", customerId, offerCodes,
					startOfBusinessDay, e);
		}
		return false;
	}

	@Override
	public WebOfferDetail saveWebOfferDetail(WebOfferDetail webOfferDetail) {
		try{
			return add(webOfferDetail);
		}catch (Exception e){
			LOG.info("Error while saving web offer detail in db {}", JSONSerializer.toJSON(webOfferDetail), e);
		}
		return null;
	}

	@Override
	public List<WebOfferDetail> getWebOfferDetails(Integer customerId, String offerType) throws NoResultException{
		try {
			LOG.info("Enter getWebOfferDetails DAO for customer: "+customerId);
			List<WebOfferDetail> list = new ArrayList<>();
			Query query = manager.createQuery("FROM WebOfferDetail W WHERE W.customerId = :customerId AND W.offerType = :offerType");
			query.setParameter("customerId", customerId);
			query.setParameter("offerType", offerType);
			List<WebOfferDetail> results = query.getResultList();
			LOG.info("WebOfferDetails found: "+JSONSerializer.toJSON(results));
			return results;
		} catch (NoResultException e) {
			LOG.error(String.format("Did not find any web offer details for customer %s and offerType %s", customerId, offerType));
		}
		return null;
	}

	@Override
	public SignUpTimeSlotCount fetchSignUpOfferTimeSlots(String dateOfDelivery) {
		SignUpTimeSlotCount response = null;
		try{
			LOG.info("Enter fetchSignUpOfferTimeSlots for dateOfDelivery: " + dateOfDelivery);
			String queryString = "SELECT SUM(CASE WHEN TIME_OF_DELIVERY = :slotOne THEN 1 ELSE 0 END), "
					+ " SUM(CASE WHEN W.timeOfDelivery = :slotTwo THEN 1 ELSE 0 END), "
					+ " SUM(CASE WHEN W.timeOfDelivery = :slotThree THEN 1 ELSE 0 END), "
					+ " SUM(CASE WHEN W.timeOfDelivery = :slotFour THEN 1 ELSE 0 END), "
					+ " SUM(CASE WHEN W.timeOfDelivery = :slotFive THEN 1 ELSE 0 END), "
					+ " SUM(CASE WHEN W.timeOfDelivery = :slotSix THEN 1 ELSE 0 END) "
					+ " FROM WebOfferDetail W WHERE W.dateOfDelivery = :deliveryDate ";
			Query query = manager.createQuery(queryString);
			query.setParameter("slotOne", "8:00 AM - 10:00 AM");
			query.setParameter("slotTwo", "12:00 PM - 2:00 PM");
			query.setParameter("slotThree", "2:00 PM - 4:00 PM");
			query.setParameter("slotFour", "4:00 PM - 6:00 PM");
			query.setParameter("slotFive", "6:00 PM - 8:00 PM");
			query.setParameter("slotSix", "8:00 PM - 11:00 PM");
			query.setParameter("deliveryDate", dateOfDelivery);
            LOG.info("fetchSignUpOfferTimeSlots  response:: " + JSONSerializer.toJSON(query.getSingleResult()));
            Object[] resultSet = (Object[]) query.getSingleResult();
			if (resultSet != null) {
				response = new SignUpTimeSlotCount();

				Long slotOneCount = (Long) resultSet[0];
                response.setSlotOneName("8:00 AM - 10:00 AM");
                response.setSlotOneCount(slotOneCount!=null ? slotOneCount:0);

                Long slotTwoCount = (Long) resultSet[1];
                response.setSlotTwoName("12:00 PM - 2:00 PM");
                response.setSlotTwoCount(slotTwoCount!=null ? slotTwoCount:0);

                Long slotThreeCount = (Long) resultSet[2];
                response.setSlotThreeName("2:00 PM - 4:00 PM");
                response.setSlotThreeCount(slotThreeCount!=null ? slotThreeCount:0);

                Long slotFourCount = (Long) resultSet[3];
                response.setSlotFourName("4:00 PM - 6:00 PM");
                response.setSlotFourCount(slotFourCount!=null ? slotFourCount:0);

                Long slotFiveCount = (Long) resultSet[4];
                response.setSlotFiveName("6:00 PM - 8:00 PM");
                response.setSlotFiveCount(slotFiveCount!=null ? slotFiveCount:0);

                Long slotSixCount = (Long) resultSet[5];
                response.setSlotSixName("8:00 PM - 11:00 PM");
                response.setSlotSixCount(slotSixCount!=null ? slotSixCount:0);
			}
		}catch (Exception e){
			LOG.info("Error while fetchSignUpOfferTimeSlots for dateOfDelivery {} in db", dateOfDelivery, e);
		}
		return response;
	}

	private List<CustomerCampaignOfferDetail> getAlreadyReceivedOffer(int customerId, Integer lastNDays, Integer campaignId){
		try {
			LOG.info("Getting DELIVERY_NBO CUSTOMER_CAMPIAGN_OFFER_DETAIL for customer: {} in last {} Days ",customerId, lastNDays);
			Query query = manager.createQuery("FROM CustomerCampaignOfferDetail W WHERE W.customerId = :customerId AND W.status = :active "
					+ "and W.couponEndDate >= :dayBeforeNthDay");
			query.setParameter("customerId", customerId);
			query.setParameter("active", AppConstants.ACTIVE);
			query.setParameter("dayBeforeNthDay", AppUtils.addDays(AppUtils.getBusinessDate(), lastNDays * -1));
			List<CustomerCampaignOfferDetail> results = query.getResultList();
			return results;
		} catch (NoResultException e) {
			LOG.error("Did not find any CustomerCampaignOfferDetailfor customer{} in last {} Days", customerId, lastNDays);
		}
		return null;
	}

	@Override
	public boolean hasCustomerReceivedPostOrderOffer(int customerId, Integer lastNDays, Integer campaignId) {
			List<CustomerCampaignOfferDetail> results = getAlreadyReceivedOffer(customerId, lastNDays, campaignId);
			return isCampaignNBOType(results);
	}

	private boolean isCampaignNBOType(List<CustomerCampaignOfferDetail> results){
		if (results != null && results.size() != 0) {
			LOG.info("Number of active customer offers are :: {}", results.size());
			for (CustomerCampaignOfferDetail detail : results) {
				CampaignDetail campaignDetail = campaignCache.getCampaign(detail.getCampaignId());
				if (CampaignStrategy.NBO.name().equals(campaignDetail.getCampaignStrategy())) {
					LOG.info("Active customer offers found with customer campaign offer detail id :: {}", detail.getCapmpaignOfferDetailId());
					return true;
				}
			}
		}
		LOG.info("No active NBO offer found for customer");
		return false;
	}

	private boolean isCampaignDNBOType(List<CustomerCampaignOfferDetail> results){
		if (results != null && results.size() != 0) {
			LOG.info("Number of active customer offers are :: {}", results.size());
			for (CustomerCampaignOfferDetail detail : results) {
				CampaignDetail campaignDetail = campaignCache.getCampaign(detail.getCampaignId());
				if (CampaignStrategy.DELIVERY_NBO.name().equals(campaignDetail.getCampaignStrategy())) {
					LOG.info("Active customer offers found with customer campaign offer detail id :: {}", detail.getCapmpaignOfferDetailId());
					return true;
				}
			}
		}
		LOG.info("No active NBO offer found for customer");
		return false;
	}


	@Override
	public CustomerCampaignOfferDetail getActiveCustomerOffer(int customerId, String strategy) {
		try {
			LOG.info("Getting CUSTOMER_CAMPIAGN_OFFER_DETAIL for customer: {} ", customerId);
			Query query = manager.createQuery(
					"FROM CustomerCampaignOfferDetail W WHERE W.customerId = :customerId AND W.status = :active "
							+ "and W.couponEndDate >= :currentDate");
			query.setParameter("customerId", customerId);
			query.setParameter("active", AppConstants.ACTIVE);
			query.setParameter("currentDate", AppUtils.getBusinessDate());
			CustomerCampaignOfferDetail result = offerWithStrategy(query.getResultList(),strategy);
			if(Objects.nonNull(result)){
				LOG.info("NBO type CustomerCampaignOfferDetail found: for customer Id {}", customerId);
				return result;
			}
			LOG.info("No NBO type CustomerCampaignOfferDetail found: for customer Id {}", customerId);
		} catch (NoResultException e) {
			LOG.error("Did not find any CustomerCampaignOfferDetail for customer{}", customerId);
		}
		return null;
	}

	private CustomerCampaignOfferDetail offerWithStrategy(List<CustomerCampaignOfferDetail> detailList, String strategy){
		for(CustomerCampaignOfferDetail detail : detailList){
			CampaignDetail campaignDetail = campaignCache.getCampaign(detail.getCampaignId());
			if(campaignDetail.getCampaignStrategy().equals(strategy)){
				return detail;
			}
		}
		return null;
	}

	@Override
	public CustomerCampaignJourney getJourneyForSession(CustomerCampaignJourney journeyDetail) {
		try {
			LOG.info("Getting journey detail for session : {} and customerNumber",
					journeyDetail.getSessionStartTime(), journeyDetail.getContactNumber());
			Query query = manager.createQuery("FROM CustomerCampaignJourney ccj " +
					"WHERE ccj.sessionStartTime = :time and ccj.contactNumber = :contactNumber");
			query.setParameter("time",journeyDetail.getSessionStartTime());
			query.setParameter("contactNumber",journeyDetail.getContactNumber());
			CustomerCampaignJourney journey = (CustomerCampaignJourney) query.getSingleResult();
 			return journey;
		}catch (NoResultException e){
			LOG.error("No journey detail found for session : {} and customerNumber : {}",
					journeyDetail.getSessionStartTime(), journeyDetail.getContactNumber());
		}catch (Exception e){
			LOG.error("No journey detail found for session : {} and customerNumber : {}",
					journeyDetail.getSessionStartTime(), journeyDetail.getContactNumber());
		}
		return null;
	}

	public CustomerCampaignOfferDetail get(Integer customerId, String couponCode) {
		try {
			LOG.error("Getting CustomerCampaignOfferDetail for customer{} and coupon {}", customerId, couponCode);
			Query query = manager.createQuery(
					"FROM CustomerCampaignOfferDetail W WHERE W.couponCode = :couponCode and W.customerId = :customerId");
			query.setParameter("couponCode", couponCode);
			query.setParameter("customerId", customerId);
			Object o = query.getSingleResult();
			return o == null ? null : (CustomerCampaignOfferDetail) o;
		} catch (NoResultException e) {
			LOG.error("Did not find any CustomerCampaignOfferDetail for customer{} and coupon {}", customerId,
					couponCode);
		}
		return null;
	}

	@Override
	public boolean updateOfferApplicationDetails(String couponCode,Integer customerId,  Integer orderId, BigDecimal savings) {
		try {
			LOG.info("Updating CustomerCampaignOfferDetail for for customerOid {} , coupon code: {} for savings of {} in order id ",customerId, couponCode, savings, orderId);
			CustomerCampaignOfferDetail offer = get(customerId, couponCode);
			if(offer == null) {
				return false;
			}
			offer.setOfferApplyCount(offer.getOfferApplyCount() == null ? 1 : offer.getOfferApplyCount() + 1);
			offer.setOverallSavings(offer.getOverallSavings() == null ? savings : AppUtils.add(offer.getOverallSavings(), savings));
			offer.setOfferApplyLastOrderId(orderId);
			offer.setOfferApplyLastTime(AppUtils.getCurrentTimestamp());
			offer.setIsOfferApplied(AppConstants.YES);
			Date today =AppUtils.getBusinessDate();
			Date creationDate = AppUtils.getBusinessDate(offer.getCouponGenerationTime());
			int gapInDay = AppUtils.getAbsDaysDiff(today, creationDate);
			offer.setGapInDays(gapInDay);
			manager.merge(offer);
			manager.flush();
			return true;
		} catch (Exception e) {
			LOG.error("Error updating CustomerCampaignOfferDetail for customer{} and coupon {}", customerId,
					couponCode, e);
		}
		return false;
	}
	@Override
	public CustomerOneViewData getCustomerOneViewData(int customerId, Integer brandId, List<Integer> excludeOrderIds) {
		return customerService.getCustomerOneViewData(customerId, brandId, excludeOrderIds);
	}

	@Override
	public CustomerDineInView getCustomerDineInView(int customerId, Integer brandId, List<Integer> excludeOrderIds) {
		return customerService.getCustomerDineInView(customerId, brandId, excludeOrderIds);
	}

	@Override
	public CustomerCampaignOfferDetail getActiveOfferCampaign(int customerId, Integer campaignId) {
		try {
			LOG.info("Getting CUSTOMER_CAMPIAGN_OFFER_DETAIL for customer: {} and Campaign Id : {}", customerId,campaignId);
			Query query = manager.createQuery(
					"FROM CustomerCampaignOfferDetail W WHERE W.customerId = :customerId AND W.status = :active "
							+ "AND W.couponEndDate >= :currentDate AND W.campaignId = :campaignId");
			query.setParameter("customerId", customerId);
			query.setParameter("active", AppConstants.ACTIVE);
			query.setParameter("currentDate", AppUtils.getBusinessDate());
			query.setParameter("campaignId", campaignId);
			List<CustomerCampaignOfferDetail> results = query.getResultList();
			LOG.info("CustomerCampaignOfferDetail found: for customer Id {} and result :: {}", customerId,JSONSerializer.toJSON(results));
			return results != null && results.size() > 0 ? results.get(0) : null;
		} catch (NoResultException e) {
			LOG.error("Did not find any CustomerCampaignOfferDetailfor customer{}", customerId);
		}
		return null;
	}

	@Override
	public boolean hasCustomerReceivedDNBO(int customerId, Integer lastNDays, Integer campaignId) {
		List<CustomerCampaignOfferDetail> results = getAlreadyReceivedOffer(customerId, lastNDays, campaignId);
		return isCampaignDNBOType(results);
	}

	@Override
	public CustomerTransactionViewData getCustomerTransactionViewData(int customerId, Integer brandId, List<Integer> excludeOrderIds) {
		return customerService.getCustomerTransactionViewData(customerId, brandId, excludeOrderIds);
	}

	@Override
	public CustomerEmailData getCustomerEmailData(int customerId, Integer brandId) {
		return customerService.getCustomerEmailData(customerId, brandId);
	}


	public List<String> getCustomerPreviousAppliedCoupon(Integer customerId,Date startDate,String offerCode){
		Query query = manager.createQuery("Select od.offerCode from OrderDetail od where od.customerId = :customerId and od.billingServerTime >= :startDate  order by od.billingServerTime");
		query.setParameter("customerId",customerId);
		query.setParameter("startDate",startDate);
		try {
			List<String> val = query.getResultList();
			if(!CollectionUtils.isEmpty(val))
				return val;
		}catch (NoResultException e){
			LOG.info("Error while getting coupon applied by customer.");
			return null;
		}
		return null;
	}

	public BigDecimal handlingChaayosCashOffer(String offerType,int discountValue,OfferOrder offerOrder,BigDecimal offerValue){
		BigDecimal customerCash = Objects.nonNull(offerValue) ? offerValue : BigDecimal.ZERO;
		String usedCode = AppConstants.CHAAYOS_CASH_BACK_OFFER_CODE;

		if(AppUtils.CHAAYOS_CASH_OFFER_CODE.contains(offerOrder.getCouponCode())){
			if(OfferCategoryType.FLAT_BILL_STRATEGY.name().equals(offerType)){
				offerValue = AppUtils.getMinimum(customerCash,new BigDecimal(discountValue));
			}else if(OfferCategoryType.PERCENTAGE_BILL_STRATEGY.name().equals(offerType)){
				BigDecimal discountAmount = AppUtils.multiply(offerOrder.getOrder().getTransactionDetail().getTotalAmount(),
						new BigDecimal(discountValue)).divide(new BigDecimal(100));
				if(discountAmount.compareTo(customerCash) > 0){
					offerValue = AppUtils
							.multiply(customerCash, new BigDecimal(100))
							.divide(offerOrder.getOrder().getTransactionDetail().getTotalAmount(),10, BigDecimal.ROUND_HALF_UP);
				}else {
					offerValue = new BigDecimal(discountValue);
				}
			}
			offerOrder.getOrder().setCashRedeemed(offerValue);
			offerOrder.setCouponCode(offerOrder.getOrder().getOfferCode() == null ? usedCode
					: usedCode.equals(offerOrder.getOrder().getOfferCode()) ? offerOrder.getOrder().getOfferCode()
					: usedCode);
		}
		return offerValue;
	}
	public void checkForLoyaltyBurnOffers(OfferOrder offerOrder,CouponDetail coupon) throws OfferValidationException, DataNotFoundException {
		Customer customer = customerService.getCustomer(offerOrder.getOrder().getCustomerId());
		LoyaltyScore loyaltyScore = loyaltyDao.getScore(offerOrder.getOrder().getCustomerId());
		if(Objects.nonNull(customer) && Objects.nonNull(loyaltyScore)) {
			boolean eligibleForSignupOffer = coupon.getOffer().isSignupOfferApplicable() && offerOrder.isSignupOffer()
					&& TransactionUtils.eligibleForSignupOffer(customerService, customer, masterCache);
			if (!eligibleForSignupOffer && Objects.nonNull(loyaltyScore.getAcquiredPoints()) && Objects.nonNull(coupon.getOffer().getLoyaltyBurnPoints())
					&& (loyaltyScore.getAcquiredPoints() < coupon.getOffer().getLoyaltyBurnPoints())) {
				if (offerOrder.isSignupOffer()) {
					throw new OfferValidationException("Customer is not eligible for Signup Offer", WebErrorCode.COUPON_APPLICABLE_NEW_CUSTOMER_ACQUISITION);
				} else {
					throw new OfferValidationException("Customer doesn't have sufficient loyalty points", WebErrorCode.COUPON_APPLICABLE_NEW_CUSTOMER_ACQUISITION);
				}
			}
		}
	}

	private void addToCouponAppliedCacheAndCheckForRevalidation(OfferOrder offerOrder, CouponDetail couponDetail) {
		LOG.info("Adding to Customer Applied Coupon Cache");
		if (Objects.nonNull(offerOrder.getOrder().getCustomerId()) && AppConstants.CUSTOMER.equals(couponDetail.getOffer().getOfferScope())) {
			masterCache.getCustomerAppliedCouponDetailMap().put(couponDetail.getCode() + "_" + offerOrder.getOrder().getCustomerId(),
					new CustomerAppliedCouponDetail(couponDetail.isReusable(), couponDetail.isReusableByCustomer(),
							couponDetail.getMaxUsage(), couponDetail.getUsage()));
			masterCache.getCustomerAppliedCouponDetailMap().put(couponDetail.getCode(),
					new CustomerAppliedCouponDetail(couponDetail.isReusable(), couponDetail.isReusableByCustomer(),
							couponDetail.getMaxUsage(), couponDetail.getUsage()));


			//Checking for re-validation & finding the reason for re-validation
			if (!couponDetail.isReusable() || !couponDetail.isReusableByCustomer() || couponDetail.getMaxUsage() == 1) {
				LOG.info("Setting Attributes for revalidation");
				offerOrder.getOrder().setRevalidate(true);
				offerOrder.getOrder().setRevalidationReason(RevalidationReason.COUPON_MAX_USAGE_AND_BY_CUSTOMER.name());
			}
		}
	}

}
