package com.stpl.tech.kettle.data.model;

import com.stpl.tech.kettle.domain.model.SubscriptionViewData;

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.ColumnResult;
import javax.persistence.ConstructorResult;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.SqlResultSetMapping;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

@Entity
@Table(name = "SUBSCRIPTION_PLAN")
@SqlResultSetMapping(name = "SubscriptionViewData", classes = @ConstructorResult(targetClass = SubscriptionViewData.class, columns = {
		@ColumnResult(name = "customerId", type = Integer.class),
		@ColumnResult(name = "subscriptionId", type = Integer.class),
		@ColumnResult(name = "planStartDate", type = Date.class),
		@ColumnResult(name = "planEndDate", type = Date.class),
		@ColumnResult(name = "subscriptionPlanCode", type = String.class),
		@ColumnResult(name="offerDescription",type = String.class)
}))
public class SubscriptionPlan {

	private Integer subscriptionPlanId;
	private String subscriptionPlanCode;
	private Integer customerId;
	private String status;
	private Date planStartDate;
	private Date planEndDate;
	private Date renewalTime;
	private String eventType;
	private Integer lastRenewalEventId;
	private BigDecimal overAllSaving;
	private String offerDescription;
	private String frequencyStrategy;//TIME_BASED OR QAUNTITY_BASED
	private BigDecimal overAllFrequency;
	private BigDecimal frequencyLimit;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "SUBSCRIPTION_PLAN_ID", unique = true, nullable = false)
	public Integer getSubscriptionPlanId() {
		return subscriptionPlanId;
	}

	public void setSubscriptionPlanId(Integer subscriptionPlanId) {
		this.subscriptionPlanId = subscriptionPlanId;
	}

	@Column(name = "SUBSCRIPTION_PLAN_CODE", nullable = false, length = 200)
	public String getSubscriptionPlanCode() {
		return subscriptionPlanCode;
	}

	public void setSubscriptionPlanCode(String subscriptionPlanCode) {
		this.subscriptionPlanCode = subscriptionPlanCode;
	}

	@Column(name = "CUSTOMER_ID", nullable = false)
	public Integer getCustomerId() {
		return customerId;
	}

	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}

	@Column(name = "PLAN_STATUS", nullable = false, length = 15)
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "PLAN_START_DATE", nullable = true, length = 10)
	public Date getPlanStartDate() {
		return planStartDate;
	}

	public void setPlanStartDate(Date planStartDate) {
		this.planStartDate = planStartDate;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "PLAN_END_DATE", nullable = true, length = 10)
	public Date getPlanEndDate() {
		return planEndDate;
	}

	public void setPlanEndDate(Date planEndDate) {
		this.planEndDate = planEndDate;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "RENEWAL_TIME", nullable = false, length = 19)
	public Date getRenewalTime() {
		return renewalTime;
	}

	public void setRenewalTime(Date renewalTime) {
		this.renewalTime = renewalTime;
	}

	@Column(name = "EVENT_TYPE", nullable = false, length = 50)
	public String getEventType() {
		return eventType;
	}

	public void setEventType(String eventType) {
		this.eventType = eventType;
	}

	@Column(name = "LAST_RENEWAL_EVENT_ID", nullable = false)
	public Integer getLastRenewalEventId() {
		return lastRenewalEventId;
	}

	public void setLastRenewalEventId(Integer lastRenewalEventId) {
		this.lastRenewalEventId = lastRenewalEventId;
	}

	@Column(name = "OVERALL_SAVING", nullable = true)
	public BigDecimal getOverAllSaving() {
		return overAllSaving;
	}

	public void setOverAllSaving(BigDecimal overAllSaving) {
		this.overAllSaving = overAllSaving;
	}

	@Column(name = "OFFER_DESCRIPTION",nullable = true)
	public String getOfferDescription() {
		return offerDescription;
	}

	public void setOfferDescription(String offerDescription) {
		this.offerDescription = offerDescription;
	}

	@Column(name = "FREQUENCY_STRATEGY",nullable = true)
	public String getFrequencyStrategy() {
		return frequencyStrategy;
	}

	public void setFrequencyStrategy(String offerStrategy) {
		this.frequencyStrategy = offerStrategy;
	}

	@Column(name = "OVERALL_FREQUENCY")
	public BigDecimal getOverAllFrequency() {
		return overAllFrequency;
	}

	public void setOverAllFrequency(BigDecimal overAllFrquency) {
		this.overAllFrequency = overAllFrquency;
	}

	@Column(name = "FREQUENCY_LIMIT",nullable = true)
	public BigDecimal getFrequencyLimit() {
		return frequencyLimit;
	}

	public void setFrequencyLimit(BigDecimal frequencyLimit) {
		this.frequencyLimit = frequencyLimit;
	}
}
