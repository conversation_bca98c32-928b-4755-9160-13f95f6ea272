package com.stpl.tech.kettle.data.mongo.stock.dao;

import com.stpl.tech.kettle.domain.model.StockOutScmProductData;
import org.bson.types.ObjectId;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;


@Repository
public interface SCMStockOutProductsDao extends MongoRepository<StockOutScmProductData, ObjectId> {

    /*@Query("{ 'productId': { $in: ?0 }, 'unitId': ?1, '_id': { $gte: ?2, $lte: ?3 } }")
    List<StockOutScmProductData> findByProductIdAndUnitIdAndTimestampBetween(
            List<Integer> productIds,
            int unitId,
            ObjectId startId,
            ObjectId endId
    );*/

    @Query("{ 'unitId': ?1, '_id': { $gte: ?2, $lte: ?3 } }")
    List<StockOutScmProductData> findByAndUnitIdAndIdBetween(
            int unitId,
            ObjectId startId,
            ObjectId endId
    );


}
