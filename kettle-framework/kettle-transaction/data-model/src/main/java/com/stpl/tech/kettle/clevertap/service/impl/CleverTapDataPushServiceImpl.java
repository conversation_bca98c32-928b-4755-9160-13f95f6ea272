package com.stpl.tech.kettle.clevertap.service.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.paytm.pg.App;
import com.stpl.tech.kettle.core.data.vo.CustomerResponse;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.customer.service.LoyaltyService;
import com.stpl.tech.kettle.data.model.LoyaltyScore;
import com.stpl.tech.kettle.data.model.OrderItem;
import com.stpl.tech.kettle.domain.model.SignupOfferStatus;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.google.gson.Gson;
import com.stpl.tech.kettle.clevertap.converter.CleverTapConverter;
import com.stpl.tech.kettle.clevertap.data.dao.CleverTapDataPushDao;
import com.stpl.tech.kettle.clevertap.data.model.CleverTapProfilePushTrack;
import com.stpl.tech.kettle.clevertap.data.model.EventPushTrack;
import com.stpl.tech.kettle.clevertap.domain.model.CleverTapEndpoints;
import com.stpl.tech.kettle.clevertap.domain.model.CleverTapPushResponse;
import com.stpl.tech.kettle.clevertap.domain.model.CleverTapRequest;
import com.stpl.tech.kettle.clevertap.domain.model.ClevertapChargedEventData;
import com.stpl.tech.kettle.clevertap.domain.model.ClevertapReceivedChaayosCashData;
import com.stpl.tech.kettle.clevertap.domain.model.CustomerProfileCleverTap;
import com.stpl.tech.kettle.clevertap.domain.model.EventUploadRequest;
import com.stpl.tech.kettle.clevertap.domain.model.GetProfileResponse;
import com.stpl.tech.kettle.clevertap.domain.model.NextBestOfferEventData;
import com.stpl.tech.kettle.clevertap.domain.model.ProfileUploadRequest;
import com.stpl.tech.kettle.clevertap.publisher.CleverTapEventPublisher;
import com.stpl.tech.kettle.clevertap.service.CleverTapDataPushService;
import com.stpl.tech.kettle.clevertap.util.AbstractRestTemplate;
import com.stpl.tech.kettle.clevertap.util.CleverTapConstants;
import com.stpl.tech.kettle.clevertap.util.CleverTapEvents;
import com.stpl.tech.kettle.core.data.vo.CustomerCardInfo;
import com.stpl.tech.kettle.core.notification.sms.CustomerSMSNotificationType;
import com.stpl.tech.kettle.customer.dao.CustomerDao;
import com.stpl.tech.kettle.customer.service.CardService;
import com.stpl.tech.kettle.customer.service.CustomerService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.OrderNotification;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.util.AppConstants;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * Created by Chaayos on 02-05-2017.
 */
@Service
public class CleverTapDataPushServiceImpl implements CleverTapDataPushService {

    private static final Logger LOG = LoggerFactory.getLogger(CleverTapDataPushServiceImpl.class);

    @Autowired
    CustomerService customerService;

    @Autowired
    private EnvironmentProperties env;

    @Autowired
    private CleverTapDataPushDao dao;

    @Autowired
    private CardService cardService;

    @Autowired
    private EnvironmentProperties props;

    @Autowired
    private CleverTapConverter cleverTapConverter;

    @Autowired
    private CustomerDao customerDao;

    @Autowired
    private CleverTapEventPublisher cleverTapEventPublisher;

    @Autowired
    private LoyaltyService loyaltyService;

    @Autowired
    private MasterDataCache masterDataCache;

    private static Gson gson = new Gson();

    private static final String CLEVERTAP_DATE_PREFIX = "$D_";

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void persistEventTrack(EventPushTrack eventPushTrack) {
        try {
            LOG.info("trying to save clevertap events");
            dao.add(eventPushTrack);
        } catch (Exception e) {
            LOG.error("Error while saving data for clevertap events push {}", e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void persistEventTracks(List<EventPushTrack> eventPushTrack) {
        try {
            LOG.info("trying to save clevertap events");
            dao.addAll(eventPushTrack);
        } catch (Exception e) {
            LOG.error("Error while saving data for clevertap events push {}", e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void persistProfileTrack(CleverTapProfilePushTrack profilePushTrack) {
        try {
            LOG.info("trying to save clevertap users");
            dao.add(profilePushTrack);
        } catch (Exception e) {
            LOG.error("Error while saving data for clevertap profile push {}", e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void persistProfileTrack(List<CleverTapProfilePushTrack> profilePushTracks) {
        try {
            LOG.info("trying to save clevertap users");
            dao.addAll(profilePushTracks);
        } catch (Exception e) {
            LOG.error("Error while saving data for clevertap profile push {}", e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public CleverTapPushResponse pushUserToCleverTap(Integer customerIds, Boolean... lead) {
        CleverTapPushResponse pushUserResponse = new CleverTapPushResponse();
        try {
            pushUserResponse = pushUsersToCleverTap(Arrays.asList(customerIds), CleverTapConstants.REGULAR, lead);
            persistProfileTrack(pushUserResponse.getProfiles());
        } catch (DataNotFoundException e) {
            LOG.info("Error while pushing single user to clevertap {}", e.getMessage());
            persistProfileTrack(new CleverTapProfilePushTrack(customerIds, "PENDING", CleverTapConstants.REGULAR));
        }catch (Exception e) {
            LOG.info("Error while pushing single user to clevertap {}", e.getMessage());
        }
        return pushUserResponse;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public CleverTapPushResponse pushUsersToCleverTap(List<Integer> customerIds, String updateType, Boolean... lead)
            throws DataNotFoundException {
        CleverTapRequest request = new CleverTapRequest();
        CleverTapRequest evtrequest = new CleverTapRequest();
        List<ProfileUploadRequest> uploadRequests = new ArrayList<>();
        List<EventUploadRequest> eventRequest = new ArrayList<>();
        CleverTapPushResponse cleverTapPushResponse = new CleverTapPushResponse();
        List<CleverTapProfilePushTrack> customers = new ArrayList<>();
        if (CollectionUtils.isEmpty(customerIds)) {
            return cleverTapPushResponse;
        }
        for (Integer id : customerIds) {
            Customer customer = customerService.getCustomer(id);
            LoyaltyScore loyaltyScore = loyaltyService.getScore(id);
            LOG.info("converting data for user {} and customer id {}", customer.getContactNumber(), customer.getId());
            CustomerProfileCleverTap profileData = cleverTapConverter.convert(customer);
            if (Objects.nonNull(loyaltyScore)) {
                profileData.setSignupOfferStatus(loyaltyScore.getSignupOfferStatus());
                if ( !AppUtils.getStatus(loyaltyScore.getAvailedSignupOffer()) && SignupOfferStatus.AVAILABLE.name().equalsIgnoreCase(loyaltyScore.getSignupOfferStatus()) && Objects.isNull(loyaltyScore.getSignupOfferExpiryTime())){
                    profileData.setSignupOfferExpiryDate(AppUtils.getClevertapFormattedDate(AppUtils.getDateAfterDays(AppUtils.getCurrentDate() ,env.getExpireSignupOfferDays())));
                }
            }
            uploadRequests.add(cleverTapConverter.convert(id, customer.getAddTime().toInstant().toEpochMilli() / 1000, profileData));
            if (Objects.nonNull(lead) && lead.length > 0 && Objects.nonNull(lead[0]) && lead[0]) {
                EventUploadRequest req = new EventUploadRequest();
                req.setTs(customer.getAddTime().toInstant().getEpochSecond());
                req.setEvtName(CleverTapEvents.LEAD_GENERATED);
                req.setType(CleverTapConstants.EVENT);
                req.setIdentity(id);
                Map<String, Object> data = new HashMap<>();
                data.put("Name", customer.getFirstName());
                data.put("CustomerId", id);
                data.put("AcquisitionSource", customer.getAcquisitionSource());
                req.setEvtData(data);
                eventRequest.add(req);
            }
        }
        if (!uploadRequests.isEmpty()) {
            request.setD(uploadRequests);
            LOG.info("Uploading {} customers data to clevertap with startId and endId as {},{}", uploadRequests.size(),
                    customerIds.get(0), customerIds.get(customerIds.size() - 1));
            //LOG.info("Profile json formed {}", new Gson().toJson(request));
            try {
//                cleverTapPushResponse = AbstractRestTemplate.postWithHeaders(CleverTapEndpoints.UPLOAD_PROFILE_OR_EVENT,
//                    request, CleverTapPushResponse.class, env.getCleverTapAccountId(), env.getCleverTapPasscode(),
//                    env);
                cleverTapEventPublisher.publishCleverTapEvent(props.getEnvironmentType().name(), gson.toJson(request), true);
                cleverTapPushResponse.setStatus("PUSHED_TO_QUEUE");
                for (Integer customerId : customerIds) {
                    CleverTapProfilePushTrack pushTrack = new CleverTapProfilePushTrack(customerId, cleverTapPushResponse.getStatus(), updateType);
                    pushTrack.setPublishTime(AppUtils.getCurrentTimestamp());
                    customers.add(pushTrack);
                }
            } catch (Exception e) {
                LOG.info("Failed IN Sending {} customers data to clevertap with startId and endId as {},{}",
                        uploadRequests.size(), customerIds.get(0), customerIds.get(customerIds.size() - 1));
                LOG.info("#########,{}", StringUtils.join(customerIds, ","));
                LOG.error("Error in pushing data to clevertap  with startId and endId as {},{}", customerIds.get(0),
                        customerIds.get(customerIds.size() - 1), e);
                for (Integer customerId : customerIds) {
                    customers.add(new CleverTapProfilePushTrack(customerId, AppConstants.ERROR, updateType));
                }
            }
            cleverTapPushResponse.setProfiles(customers);
        }
        if (!eventRequest.isEmpty()) {
            evtrequest.setD(eventRequest);
            LOG.info("Uploading {} customers lead data to clevertap with startId and endId as {},{}", eventRequest.size(),
                    customerIds.get(0), customerIds.get(customerIds.size() - 1));
            //LOG.info("Profile json formed {}", new Gson().toJson(request));
            try {
//                 cleverTapPushResponse = AbstractRestTemplate.postWithHeaders(CleverTapEndpoints.UPLOAD_PROFILE_OR_EVENT,
//                     request, CleverTapPushResponse.class, env.getCleverTapAccountId(), env.getCleverTapPasscode(),
//                     env);
                cleverTapEventPublisher.publishCleverTapEvent(props.getEnvironmentType().name(), gson.toJson(evtrequest), true);
                cleverTapPushResponse.setStatus("PUSHED_TO_QUEUE");
                for (Integer customerId : customerIds) {
                    CleverTapProfilePushTrack pushTrack = new CleverTapProfilePushTrack(customerId, cleverTapPushResponse.getStatus(), updateType);
                    pushTrack.setPublishTime(AppUtils.getCurrentTimestamp());
                    customers.add(pushTrack);
                }
            } catch (Exception e) {
                LOG.info("Failed IN Sending {} customers lead data to clevertap with startId and endId as {},{}",
                        uploadRequests.size(), customerIds.get(0), customerIds.get(customerIds.size() - 1));
                LOG.info("#########,{}", StringUtils.join(customerIds, ","));
                LOG.error("Error in pushing data to clevertap  with startId and endId as {},{}", customerIds.get(0),
                        customerIds.get(customerIds.size() - 1), e);
                for (Integer customerId : customerIds) {
                    customers.add(new CleverTapProfilePushTrack(customerId, AppConstants.ERROR, updateType));
                }
            }
        }
        return cleverTapPushResponse;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public CleverTapPushResponse pushUsersToCleverTapWithCheckCustomer(List<Integer> customerIds, String updateType) throws DataNotFoundException {
        CleverTapRequest request = new CleverTapRequest();
        List<ProfileUploadRequest> uploadRequests = new ArrayList<>();
        List<Integer> alreadyExistedCustomer = new ArrayList<>();
        CleverTapPushResponse cleverTapPushResponse = new CleverTapPushResponse();
        for (Integer id : customerIds) {
            Customer customer = customerService.getCustomer(id);
            GetProfileResponse isUserExist = getUserProfile(customer.getId());
            if ((isUserExist.getStatus().equals(CleverTapConstants.SUCCESS) && isUserExist.getRecord() == null) || CleverTapConstants.REGULAR.equals(updateType)) {
                LOG.info("converting data for user {}", customer.getContactNumber());
                CustomerCardInfo cardInfo = cardService.getCashCardAmount(customer.getId());
                uploadRequests.add(cleverTapConverter.convert(customer, cardInfo));
            } else {
                alreadyExistedCustomer.add(customer.getId());
                LOG.info("skipping customer as it exists for customer Id {} and contact number{}", customer.getId(), customer.getContactNumber());
            }
        }
        if (!uploadRequests.isEmpty()) {
            request.setD(uploadRequests);
            LOG.info("total request to upload for a batch is {}  out of which new customer are {} and skipped customers are {}", customerIds.size(), uploadRequests.size(), alreadyExistedCustomer.size());
            try {
//                cleverTapPushResponse = AbstractRestTemplate.postWithHeaders(CleverTapEndpoints.UPLOAD_PROFILE_OR_EVENT, request, CleverTapPushResponse.class, env.getCleverTapAccountId(), env.getCleverTapPasscode(), env);
                cleverTapEventPublisher.publishCleverTapEvent(props.getEnvironmentType().name(), gson.toJson(request), true);
                cleverTapPushResponse.setStatus("PUSHED_TO_QUEUE");
                for (Integer customerId : customerIds) {
                    CleverTapProfilePushTrack pushTrack = new CleverTapProfilePushTrack(customerId, cleverTapPushResponse.getStatus(), updateType);
                    pushTrack.setPublishTime(AppUtils.getCurrentTimestamp());
                    persistProfileTrack(pushTrack);
                }
            } catch (Exception e) {
                LOG.error("error while pushing customer data {}", e.getMessage());
                for (Integer customerId : customerIds) {
                    persistProfileTrack(new CleverTapProfilePushTrack(customerId, AppConstants.ERROR, updateType));
                }
            }
        }
        return cleverTapPushResponse;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<Integer> getCustomerIdsBatch(int batchSize, Integer lastCustomerId) {
        return dao.getCustomerIdsBatch(batchSize, lastCustomerId, AppConstants.EXCLUDE_CUSTOMER_IDS);
    }


    @Override
    public GetProfileResponse getUserProfile(Integer customerIds) {
        HashMap<String, String> map = new HashMap<>();
        map.put("identity", String.valueOf(customerIds));
        GetProfileResponse response = null;
        try {
            response = AbstractRestTemplate.getWithHeaders(CleverTapEndpoints.GET_USER_PROFILE, map, GetProfileResponse.class, env.getCleverTapAccountId(), env.getCleverTapPasscode(), env);
        } catch (Exception e) {
            LOG.error("error while getting user profile for user {}", customerIds);
        }
        return response;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<Integer> getOrdersBatch(Integer startOrderId, Integer batchSize) {
        return dao.getOrderBatch(startOrderId, batchSize, Arrays.asList(AppConstants.CHANNEL_PARTNER_DINE_IN_APP, AppConstants.BAZAAR_PARTNER_ID), AppConstants.EXCLUDE_CUSTOMER_IDS);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public CleverTapPushResponse uploadEvent(List<Integer> orderList, String evtName, String updateType, Map<Integer, OrderNotification> orderNotificationMap) {
        CleverTapPushResponse cleverTapPushResponse = new CleverTapPushResponse();
        ArrayList<EventUploadRequest> requestList = new ArrayList<>();
        List<EventPushTrack> orders = new ArrayList<>();
        for (Integer orderId : orderList) {
            OrderDetail order = dao.find(OrderDetail.class, orderId);
            ClevertapChargedEventData evt = new ClevertapChargedEventData();
            CustomerInfo customerInfo = dao.find(CustomerInfo.class, order.getCustomerId());
            OrderNotification notificationData = null;
            if (Objects.nonNull(orderNotificationMap) && !orderNotificationMap.isEmpty()) {
                if (orderNotificationMap.containsKey(orderId) && Objects.nonNull(orderNotificationMap.get(orderId)) && Objects.nonNull(customerInfo)) {
                    notificationData = orderNotificationMap.get(orderId);
                }
            }
            LOG.info("Printing notification data :{}", new Gson().toJson(notificationData));

            if (customerInfo != null) {
                if (evtName.equals(CleverTapEvents.CHARGED)) {
                    evt = cleverTapConverter.convertChargedEventOrderData(order, customerInfo, orderNotificationMap.get(orderId));
                } else if (evtName.equalsIgnoreCase(CleverTapEvents.SUBSCRIPTION_PURCHASED_EVENT)) {
                    evt = cleverTapConverter.convertSubscriptionEventOrderData(order, customerInfo, orderNotificationMap.get(orderId));
                } else if (evtName.equalsIgnoreCase(CleverTapEvents.WALLET_PURCHASED_EVENT)) {
                    evt = cleverTapConverter.convertWalletEventOrderData(order, customerInfo, orderNotificationMap.get(orderId));
                }
                EventUploadRequest request = cleverTapConverter.convert(order, evtName, evt);
                requestList.add(request);
            } else {
                LOG.info("customer not exist for customerId: {} tagged with order", order.getCustomerId());
            }
        }
        if (!requestList.isEmpty()) {
            CleverTapRequest uploadRequest = new CleverTapRequest();
            uploadRequest.setD(requestList);
            try {
                LOG.info("Uploading {} orders data to clevertap with startId and endId as {},{}", requestList.size(),
                        orderList.get(0), orderList.get(orderList.size() - 1));
                //LOG.info("Event json formed {}", new Gson().toJson(requestList));
                cleverTapEventPublisher.publishCleverTapEvent(props.getEnvironmentType().name(), gson.toJson(uploadRequest), true);
//                cleverTapPushResponse = AbstractRestTemplate.postWithHeaders(CleverTapEndpoints.UPLOAD_PROFILE_OR_EVENT, uploadRequest, CleverTapPushResponse.class, env.getCleverTapAccountId(), env.getCleverTapPasscode(), env);
                cleverTapPushResponse.setStatus("PUSHED_TO_QUEUE");
                for (Integer orderId : orderList) {
                    EventPushTrack eventPushTrack = new EventPushTrack(evtName, orderId, cleverTapPushResponse.getStatus(), updateType, AppConstants.CLEVERTAP);
                    eventPushTrack.setPublishTime(AppUtils.getCurrentTimestamp());
                    orders.add(eventPushTrack);
                }
            } catch (Exception e) {
                LOG.error("error while uploading events {}", e.getMessage());
                LOG.info("Failed IN Sending {} customers data to clevertap with startId and endId as {},{}",
                        requestList.size(), orderList.get(0), orderList.get(orderList.size() - 1));
                LOG.info("######### order events,{}", StringUtils.join(orderList, ","));
                LOG.error("Error in pushing data to clevertap  with startId and endId as {},{}", orderList.get(0),
                        orderList.get(orderList.size() - 1), e);
                for (Integer orderId : orderList) {
                    orders.add(new EventPushTrack(evtName, orderId, AppConstants.ERROR, updateType, AppConstants.CLEVERTAP));
                }
            }

        }
        cleverTapPushResponse.setEvents(orders);
        return cleverTapPushResponse;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public CleverTapPushResponse uploadNextBestOfferEvents(List<Integer> orderList, String evtName, String updateType, String nextOfferEventName) {
        CleverTapPushResponse cleverTapPushResponse = new CleverTapPushResponse();
        ArrayList<EventUploadRequest> requestList = new ArrayList<>();
        List<EventPushTrack> orders = new ArrayList<>();
        for (Integer orderId : orderList) {
            OrderDetail order = dao.find(OrderDetail.class, orderId);
            NextBestOfferEventData evt = new NextBestOfferEventData();
            CustomerInfo customerInfo = dao.find(CustomerInfo.class, order.getCustomerId());
            if (customerInfo != null) {
                if (evtName.equals(CleverTapEvents.NEXT_BEST_OFFER)) {
                    evt = cleverTapConverter.convertNextBestOfferEventData(order, customerInfo, nextOfferEventName);
                }
                EventUploadRequest request = cleverTapConverter.convert(order, evtName, evt);
                requestList.add(request);
            } else {
                LOG.info("customer not exist for customerId: {} tagged with order", order.getCustomerId());
            }
        }
        if (!requestList.isEmpty()) {
            CleverTapRequest uploadRequest = new CleverTapRequest();
            uploadRequest.setD(requestList);
            try {
                LOG.info("Uploading {} orders data to clevertap with startId and endId as {},{}", requestList.size(),
                        orderList.get(0), orderList.get(orderList.size() - 1));
//                cleverTapPushResponse = AbstractRestTemplate.postWithHeaders(CleverTapEndpoints.UPLOAD_PROFILE_OR_EVENT, new Gson().toJson(uploadRequest), CleverTapPushResponse.class, env.getCleverTapAccountId(), env.getCleverTapPasscode(), env);
                cleverTapEventPublisher.publishCleverTapEvent(props.getEnvironmentType().name(), gson.toJson(uploadRequest), true);
                cleverTapPushResponse.setStatus("PUSHED_TO_QUEUE");
                for (Integer orderId : orderList) {
                    EventPushTrack eventPushTrack = new EventPushTrack(evtName, orderId, cleverTapPushResponse.getStatus(), updateType, AppConstants.CLEVERTAP);
                    eventPushTrack.setPublishTime(AppUtils.getCurrentTimestamp());
                    orders.add(eventPushTrack);
                }
            } catch (Exception e) {
                LOG.error("error while uploading events {}", e.getMessage());
                LOG.info("Failed IN Sending {} customers data to clevertap with startId and endId as {},{}",
                        requestList.size(), orderList.get(0), orderList.get(orderList.size() - 1));
                LOG.info("######### order events,{}", StringUtils.join(orderList, ","));
                LOG.error("Error in pushing data to clevertap  with startId and endId as {},{}", orderList.get(0),
                        orderList.get(orderList.size() - 1), e);
                for (Integer orderId : orderList) {
                    orders.add(new EventPushTrack(evtName, orderId, "ERROR", updateType, AppConstants.CLEVERTAP));
                }
            }

        }
        cleverTapPushResponse.setEvents(orders);
        return cleverTapPushResponse;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public CleverTapPushResponse publishGenericEvent(Integer customerId, String evtName, String cashMetadataType,
                                                     String cashTransactionCode, Date creationDate, Date expirationDate, BigDecimal amount,
                                                     Boolean sendNotification, CustomerSMSNotificationType customerSMSNotificationType, BigDecimal walletBalance,
                                                     Integer loyaltyBalance, BigDecimal chaayosCash, String comment) {

        List<EventPushTrack> eventPushTrackList = new ArrayList<>();
        CustomerInfo customerInfo = customerDao.getCustomerInfoById(customerId);
        CleverTapPushResponse cleverTapPushResponse = new CleverTapPushResponse();
        EventUploadRequest request = new EventUploadRequest();
        ClevertapReceivedChaayosCashData evt = new ClevertapReceivedChaayosCashData();
        if (Objects.nonNull(customerInfo)) {
            if (evtName.equals(CleverTapEvents.RECEIVED_CHAAYOS_CASH)) {
                evt = cleverTapConverter.convertReceivedChaayosCashData(cashMetadataType, cashTransactionCode,
                        creationDate, expirationDate, amount, sendNotification, customerSMSNotificationType,
                        walletBalance, loyaltyBalance, chaayosCash, comment);
            }

            request = cleverTapConverter.convertReceivedChaayosCashData(customerId, evtName, evt);
        } else {
            LOG.info("customer not exist for customerId: {} tagged with order", customerId);
        }

        if (Objects.nonNull(request)) {
            CleverTapRequest uploadRequest = new CleverTapRequest();

            List<EventUploadRequest> req = new ArrayList<>();
            req.add(request);
            uploadRequest.setD(req);
            try {
//				cleverTapPushResponse = AbstractRestTemplate.postWithHeaders(CleverTapEndpoints.UPLOAD_PROFILE_OR_EVENT,
//						uploadRequest, CleverTapPushResponse.class, env.getCleverTapAccountId(),
//						env.getCleverTapPasscode(), env);
                cleverTapEventPublisher.publishCleverTapEvent(props.getEnvironmentType().name(), gson.toJson(uploadRequest), true);
                cleverTapPushResponse.setStatus("PUSHED_TO_QUEUE");
                LOG.info("response for uploaded event{}", new Gson().toJson(cleverTapPushResponse));
                EventPushTrack eventPushTrack = new EventPushTrack(evtName, -1, cleverTapPushResponse.getStatus(),
                        "event", AppConstants.CLEVERTAP);
                eventPushTrack.setPublishTime(AppUtils.getCurrentTimestamp());
                eventPushTrackList.add(eventPushTrack);
            } catch (Exception e) {
                LOG.error("error while uploading events {}", e.getMessage());
                EventPushTrack eventPushTrack = new EventPushTrack(evtName, -1, "ERROR", "event",
                        AppConstants.CLEVERTAP);
                eventPushTrackList.add(eventPushTrack);
            }
        }

        cleverTapPushResponse.setEvents(eventPushTrackList);

        return cleverTapPushResponse;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public CleverTapPushResponse uploadProfileAttributes(Integer customerId, long epochSeconds, String updateType, Object data) {
        CleverTapPushResponse cleverTapPushResponse = new CleverTapPushResponse();
        CleverTapProfilePushTrack profilePushStatus = new CleverTapProfilePushTrack();
        ProfileUploadRequest request = cleverTapConverter.convert(customerId, epochSeconds, data);
        if (Objects.nonNull(request)) {
            CleverTapRequest uploadRequest = new CleverTapRequest();
            uploadRequest.setD(Arrays.asList(request));
            try {
                LOG.info("Uploading offer attributes  to clevertap for customer {}", customerId);
                // LOG.info("Event json formed {}", new Gson().toJson(requestList));
//				cleverTapPushResponse = AbstractRestTemplate.postWithHeaders(CleverTapEndpoints.UPLOAD_PROFILE_OR_EVENT,
//						uploadRequest, CleverTapPushResponse.class, env.getCleverTapAccountId(),
//						env.getCleverTapPasscode(), env);
                cleverTapEventPublisher.publishCleverTapEvent(props.getEnvironmentType().name(), gson.toJson(uploadRequest), true);
                cleverTapPushResponse.setStatus("PUSHED_TO_QUEUE");
                profilePushStatus = new CleverTapProfilePushTrack(customerId, cleverTapPushResponse.getStatus(), updateType);
                profilePushStatus.setPublishTime(AppUtils.getCurrentTimestamp());
            } catch (Exception e) {
                LOG.error("error while uploading offer attributes  to clevertap for customner {} to clevertap {}", customerId,
                        e.getMessage());
                profilePushStatus = new CleverTapProfilePushTrack(customerId, AppConstants.ERROR, updateType);

            }

        }
        cleverTapPushResponse.setProfiles(Arrays.asList(profilePushStatus));
        persistProfileTrack(Arrays.asList(profilePushStatus));
        return cleverTapPushResponse;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public CleverTapPushResponse publishCustomEvent(Integer customerId, String evtName, long epochSeconds,
                                                    String updateType, Object data) {
        CleverTapPushResponse cleverTapPushResponse = new CleverTapPushResponse();
        List<EventPushTrack> eventPushTrackList = new ArrayList<>();
        EventUploadRequest eventRequest = new EventUploadRequest();
        eventRequest.setIdentity(customerId);
        eventRequest.setEvtName(evtName);
        eventRequest.setTs(epochSeconds);
        eventRequest.setType(CleverTapConstants.EVENT);
        eventRequest.setEvtData(data);

        if (Objects.nonNull(eventRequest)) {
            CleverTapRequest uploadRequest = new CleverTapRequest();
            List<EventUploadRequest> req = new ArrayList<>();
            req.add(eventRequest);
            uploadRequest.setD(req);
            try {
//				cleverTapPushResponse = AbstractRestTemplate.postWithHeaders(CleverTapEndpoints.UPLOAD_PROFILE_OR_EVENT,
//						uploadRequest, CleverTapPushResponse.class, env.getCleverTapAccountId(),
//						env.getCleverTapPasscode(), env);
                long time = System.currentTimeMillis();
                cleverTapEventPublisher.publishCleverTapEvent(props.getEnvironmentType().name(),
                        gson.toJson(uploadRequest), true);
                LOG.info("################# time taken by publisher {} ################", System.currentTimeMillis() - time);

                cleverTapPushResponse.setStatus("PUSHED_TO_QUEUE");
                LOG.info("response for uploaded event{}", new Gson().toJson(cleverTapPushResponse));
                EventPushTrack eventPushTrack = new EventPushTrack(evtName, customerId, cleverTapPushResponse.getStatus(),
                        updateType, AppConstants.CLEVERTAP);
                eventPushTrack.setPublishTime(AppUtils.getCurrentTimestamp());
                eventPushTrackList.add(eventPushTrack);
            } catch (Exception e) {
                LOG.error("error while uploading events {}", e.getMessage());
                EventPushTrack eventPushTrack = new EventPushTrack(evtName, -1, "ERROR", "event",
                        AppConstants.CLEVERTAP);
                eventPushTrackList.add(eventPushTrack);
            }
        }
        cleverTapPushResponse.setEvents(eventPushTrackList);
        persistEventTracks(eventPushTrackList);
        return cleverTapPushResponse;
    }@Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void updateCustomerAppAction(int customerId, String appAction) throws DataNotFoundException, DataUpdationException {
        Customer customer = customerService.getCustomer(customerId);
        if(Objects.nonNull(customer)){
            customer.setAppAction(appAction);
            customer.setAppActionTime(new Date());
            customerService.updateCustomerAppAction(customer);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public CleverTapPushResponse uploadEventForPartner(Map<Integer,Integer> orderList,String evtName,String updateType,Map<Integer, OrderNotification> orderNotificationMap){
        CleverTapPushResponse cleverTapPushResponse = new CleverTapPushResponse();
        ArrayList<EventUploadRequest> requestList = new ArrayList<>();
        List<EventPushTrack> orders = new ArrayList<>();
        for(Map.Entry<Integer,Integer> entry : orderList.entrySet()){
            Integer orderId = entry.getKey();
            Integer customerId = entry.getValue();
            OrderDetail order = dao.find(OrderDetail.class,orderId);
            ClevertapChargedEventData evt = new ClevertapChargedEventData();
            CustomerInfo customerInfo = dao.find(CustomerInfo.class, customerId);
            OrderNotification notificationData = null;
            if (Objects.nonNull(orderNotificationMap) && !orderNotificationMap.isEmpty()) {
                if (orderNotificationMap.containsKey(orderId) && Objects.nonNull(orderNotificationMap.get(orderId)) && Objects.nonNull(customerInfo)) {
                    notificationData = orderNotificationMap.get(orderId);
                }
            }
            LOG.info("Printing notification data :{}", new Gson().toJson(notificationData));
            order.setCustomerId(customerId);
            if (customerInfo != null) {
                if (evtName.equals(CleverTapEvents.CHARGED)) {
                    evt = cleverTapConverter.convertChargedEventOrderData(order, customerInfo, orderNotificationMap.get(orderId));
                }
                EventUploadRequest request = cleverTapConverter.convert(order, evtName, evt);
                requestList.add(request);
            }
            else {
                LOG.info("customer not exist for customerId: {} tagged with order", order.getCustomerId());
            }
        }
        if (!requestList.isEmpty()){
            CleverTapRequest uploadRequest = new CleverTapRequest();
            uploadRequest.setD(requestList);
            try {
                LOG.info("Uploading {} orders data to clevertap with startId and endId as {},{}", requestList.size(),
                        orderList.get(0), orderList.get(orderList.size() - 1));
                //LOG.info("Event json formed {}", new Gson().toJson(requestList));
                cleverTapEventPublisher.publishCleverTapEvent(props.getEnvironmentType().name(), gson.toJson(uploadRequest), true);
//                cleverTapPushResponse = AbstractRestTemplate.postWithHeaders(CleverTapEndpoints.UPLOAD_PROFILE_OR_EVENT, uploadRequest, CleverTapPushResponse.class, env.getCleverTapAccountId(), env.getCleverTapPasscode(), env);
                cleverTapPushResponse.setStatus("PUSHED_TO_QUEUE");
                for (Integer orderId : orderList.keySet()) {
                    EventPushTrack eventPushTrack = new EventPushTrack(evtName, orderId, cleverTapPushResponse.getStatus(), updateType, AppConstants.CLEVERTAP);
                    eventPushTrack.setPublishTime(AppUtils.getCurrentTimestamp());
                    orders.add(eventPushTrack);
                }
            } catch (Exception e) {
                LOG.error("error while uploading events {}", e.getMessage());
                LOG.info("Failed IN Sending {} customers data to clevertap with startId and endId as {},{}",
                        requestList.size(), orderList.get(0), orderList.get(orderList.size() - 1));
                LOG.info("######### order events,{}", StringUtils.join(orderList.keySet(), ","));
                LOG.error("Error in pushing data to clevertap  with startId and endId as {},{}", orderList.get(0),
                        orderList.get(orderList.size() - 1), e);
                for (Integer orderId : orderList.keySet()) {
                    orders.add(new EventPushTrack(evtName, orderId, AppConstants.ERROR, updateType, AppConstants.CLEVERTAP));
                }
            }
        }
        cleverTapPushResponse.setEvents(orders);
        return cleverTapPushResponse;
    }

    @Override
    public List<ProfileUploadRequest> getUserProfileForCleverTap(List<Integer> customerIds, String updateType)
            throws DataNotFoundException{
        List<ProfileUploadRequest> uploadRequests = new ArrayList<>();
        if (CollectionUtils.isEmpty(customerIds)) {
            return uploadRequests;
        }
        for (Integer id : customerIds) {
            Customer customer = customerService.getCustomer(id);
            LoyaltyScore loyaltyScore = loyaltyService.getScore(id);
            LOG.info("converting data for user {} and customer id {}", customer.getContactNumber(), customer.getId());
            CustomerProfileCleverTap profileData = cleverTapConverter.convert(customer);
            if (Objects.nonNull(loyaltyScore)) {
                profileData.setSignupOfferStatus(loyaltyScore.getSignupOfferStatus());
                if ( !AppUtils.getStatus(loyaltyScore.getAvailedSignupOffer()) && SignupOfferStatus.AVAILABLE.name().equalsIgnoreCase(loyaltyScore.getSignupOfferStatus()) && Objects.isNull(loyaltyScore.getSignupOfferExpiryTime())){
                    profileData.setSignupOfferExpiryDate(AppUtils.getClevertapFormattedDate(AppUtils.getDateAfterDays(AppUtils.getCurrentDate() ,env.getExpireSignupOfferDays())));
                }
            }
            uploadRequests.add(cleverTapConverter.convert(id, customer.getAddTime().toInstant().getEpochSecond(),
                    profileData));
        }
        return uploadRequests;
    }

    @Override
    public List<EventUploadRequest> getLeadEventRequestForCleverTap(List<Integer> customerIds, String updateType, Boolean... lead)
            throws DataNotFoundException {
        List<EventUploadRequest> eventRequest = new ArrayList<>();
        if (CollectionUtils.isEmpty(customerIds)) {
            return eventRequest;
        }
        for (Integer id : customerIds) {
            Customer customer = customerService.getCustomer(id);
            LOG.info("converting data for Lead Event for user {} and customer id {}", customer.getContactNumber(), customer.getId());
            if (Objects.nonNull(lead) && lead.length > 0 && Objects.nonNull(lead[0]) && lead[0]) {
                EventUploadRequest req = new EventUploadRequest();
                req.setTs(customer.getAddTime().toInstant().getEpochSecond());
                req.setEvtName(CleverTapEvents.LEAD_GENERATED);
                req.setType(CleverTapConstants.EVENT);
                req.setIdentity(id);
                Map<String, Object> data = new HashMap<>();
                data.put("Name", customer.getFirstName());
                data.put("CustomerId", id);
                data.put("AcquisitionSource", customer.getAcquisitionSource());
                req.setEvtData(data);
                eventRequest.add(req);
            }
        }
        return eventRequest;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<EventUploadRequest> getEventDataList(List<Integer> orderList, String updateType,
                                                     Map<Integer, OrderNotification> orderNotificationMap){
        List<EventUploadRequest> requestList = new ArrayList<>();
        for (Integer orderId : orderList) {
            OrderDetail data = dao.find(OrderDetail.class, orderId);
            if (Objects.nonNull(data)) {
                OrderDetail order = data;
                String evtName = getOrderType(order);
                ClevertapChargedEventData evt = new ClevertapChargedEventData();
                CustomerInfo customerData = dao.find(CustomerInfo.class, order.getCustomerId());
                OrderNotification notificationData = null;
                if (Objects.nonNull(orderNotificationMap) && !orderNotificationMap.isEmpty()) {
                    if (orderNotificationMap.containsKey(orderId) && Objects.nonNull(orderNotificationMap.get(orderId))
                            && Objects.nonNull(customerData)) {
                        notificationData = orderNotificationMap.get(orderId);
                    }
                }
                if (Objects.nonNull(customerData)) {
                    try {
                        if (evtName.equals(CleverTapEvents.CHARGED)) {
                            evt = cleverTapConverter.convertChargedEventOrderData(order, customerData,
                                    orderNotificationMap.get(orderId));
                        } else if (evtName.equalsIgnoreCase(CleverTapEvents.SUBSCRIPTION_PURCHASED_EVENT)) {
                            evt = cleverTapConverter.convertSubscriptionEventOrderData(order, customerData,
                                    orderNotificationMap.get(orderId));
                        } else if (evtName.equalsIgnoreCase(CleverTapEvents.WALLET_PURCHASED_EVENT)) {
                            evt = cleverTapConverter.convertWalletEventOrderData(order, customerData,
                                    orderNotificationMap.get(orderId));
                        }
                        EventUploadRequest request = cleverTapConverter.convert(order, evtName, evt);
                        requestList.add(request);
                    } catch (Exception e) {
                        LOG.info("Exception occured while converting clevertap event request ", e);
                    }
                } else {
                    LOG.info("customer not exist for customerId: {} tagged with order", order.getCustomerId());
                }
            }
        }
        return requestList;
    }

    @Override
    public CleverTapPushResponse publishToCleverTapQueueNew(Object payload,Integer id,String updateType){
        CleverTapPushResponse cleverTapPushResponse = new CleverTapPushResponse();
        if (Objects.nonNull(payload)) {
            Map<String, Object> uploadRequest = new HashMap<>();
            if (payload instanceof List) {
                uploadRequest.put("d", payload);
            } else {
                uploadRequest.put("d", Arrays.asList(payload));
            }
            try {
                cleverTapEventPublisher.publishCleverTapEvent(props.getEnvironmentType().name(),
                        gson.toJson(uploadRequest), true);
                cleverTapPushResponse.setStatus(CleverTapConstants.PUSHED_TO_QUEUE);
            }catch (Exception e){
                LOG.error("error while uploading offer attributes  to clevertap for customner {} to clevertap {}", id,
                        e.getMessage());
                cleverTapPushResponse.setStatus("FAILED_TO_PUSHED");
            }
        }
        return cleverTapPushResponse;
    }

    private String getOrderType(OrderDetail order) {
        for (OrderItem item : order.getOrderItems()) {
            if (Objects.nonNull(masterDataCache.getSubscriptionProductDetail(item.getProductId()))) {
                return CleverTapEvents.SUBSCRIPTION_PURCHASED_EVENT;
            } else if (AppUtils.getStatus(order.getGiftCardOrder())) {
                return CleverTapEvents.WALLET_PURCHASED_EVENT;
            }
        }
        return CleverTapEvents.CHARGED;
    }

    private void pushOtpEventToCleverTap(Object payload) {
        CleverTapPushResponse cleverTapPushResponse = new CleverTapPushResponse();
        if (Objects.nonNull(payload)) {
            Map<String, Object> uploadRequest = new HashMap<>();
            if (payload instanceof List) {
                uploadRequest.put("d", payload);
            } else {
                uploadRequest.put("d", Arrays.asList(payload));
            }
            Pair<String, String> cleverTapAcc = getCleverTapAccByBrand(1);
            try {
                cleverTapPushResponse = AbstractRestTemplate.postWithHeaders(CleverTapEndpoints.UPLOAD_PROFILE_OR_EVENT,
                        uploadRequest, CleverTapPushResponse.class, cleverTapAcc.getKey(), cleverTapAcc.getValue(), env);
                LOG.info("Response Status from cleverTap is : {}", cleverTapPushResponse.getStatus());
                LOG.info("Response from cleverTap is : {}", new Gson().toJson(cleverTapPushResponse).toString());
            } catch (Exception e) {
                LOG.info("Error in pushing data to cleverTap using API :{}", e);
            }
        }
    }

    private Pair<String, String> getCleverTapAccByBrand(Integer brandId) {
        if (brandId.equals(com.stpl.tech.util.AppConstants.CHAAYOS_BRAND_ID)) {
            return new Pair<>(env.getCleverTapAccountId(), env.getCleverTapPasscode());
        } else {
            LOG.info("CLEVER_TAP-EVENT-PUSH :: invalid brand id found Brand id : {}", brandId);
            return null;
        }
    }

    @Override
    public void generateOtpEventPayloadForCleverTap(CustomerResponse customer, String otp){
        List<EventUploadRequest> eventRequestList = new ArrayList<>();
        Map<String,String> data = new HashMap<>();
        data.put("OTP",otp);
        EventUploadRequest eventRequest = new EventUploadRequest();
        eventRequest.setIdentity(customer.getId());
        eventRequest.setEvtName(CleverTapEvents.CLEVERTAP_EVENT_TO_RESEND);
        eventRequest.setTs(AppUtils.getCurrentTimestamp().toInstant().getEpochSecond());
        eventRequest.setType(CleverTapConstants.EVENT);
        eventRequest.setEvtData(data);
        eventRequestList.add(eventRequest);
        pushOtpEventToCleverTap(eventRequest);
    }





}
