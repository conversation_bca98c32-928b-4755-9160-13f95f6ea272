/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.dao;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.stpl.tech.kettle.core.exception.PaymentFailureException;
import com.stpl.tech.kettle.data.model.OrderPaymentDetail;
import com.stpl.tech.kettle.data.model.OrderPaymentEvent;
import com.stpl.tech.kettle.data.model.PaytmPaymentDetails;
import com.stpl.tech.kettle.data.model.StandaloneTransactionDetail;
import com.stpl.tech.master.payment.model.OrderPayment;
import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.master.payment.model.PaymentRequest;
import com.stpl.tech.master.payment.model.PaymentResponse;
import com.stpl.tech.master.payment.model.PaymentStatus;
import com.stpl.tech.master.payment.model.PaymentStatusChangeRequest;
import com.stpl.tech.master.payment.model.razorpay.RazorPayEventData;

public interface PaymentGatewayDao {

	public void createRequest(PaymentRequest request, OrderPaymentRequest order);

	public OrderPaymentDetail updateResponse(PaymentStatus status, PaymentResponse response);

	public void updateRedirectUrl(int id, String url);

	public OrderPaymentDetail getActivePaymentDetail(String externalOrderId);

	public OrderPaymentDetail getOrderPaymentDetailByExternalOrderId(String externalOrderId);

	public OrderPaymentDetail getSuccessfulPaymentDetailFromOrderId(String externalOrderId);

	public OrderPaymentDetail getSuccessfulPaymentDetailFromPartnerTransactionId(String partnerTransactionId);

	public void createPaymentEvent(RazorPayEventData event);

	public Boolean cancelPayment(PaymentStatusChangeRequest cancel);

	public Boolean paymentFailure(PaymentStatusChangeRequest cancel);

	public OrderPaymentDetail getSuccessfulPaymentDetail(Integer settlementId, Integer orderId);

	List<OrderPaymentDetail> getRecentDisassociatedPaymentDetailAfterTime(String contact, Date time);

	public OrderPaymentDetail getDisassociatedPaymentDetail(String contact, Date date);

	public List<OrderPaymentDetail> getDisassociatedPaymentDetail(Date date);

	public OrderPayment refundPayment(OrderPayment request);

	public List<OrderPaymentDetail> getPendingRefunds();

	public OrderPaymentDetail save(OrderPaymentDetail paymentDetail);

	public OrderPaymentDetail getSuccessfulPaymentDetailFromOrderId(Integer orderId) throws PaymentFailureException;

	public OrderPaymentDetail getPaymentDetailFromOrderId(Integer orderId) throws PaymentFailureException;

	public OrderPaymentDetail getPaymentDetail(Integer paymentDetailId);

	public PaytmPaymentDetails getPaytmPaymentStatus(String orderId);

	public PaytmPaymentDetails savePaytmPaymentDetails(PaytmPaymentDetails paytmPaymentDetails);

	public boolean updatePaytmPaymentDetails(PaytmPaymentDetails paytmPaymentDetails);


	public OrderPaymentDetail updatePayTMResponse(PaymentStatus status,
										   String orderId, String transactionId,
										   String partnerPaymentStatus,
										   Map<String, String> persistentAttributes);

	public List<OrderPaymentDetail> getInitiatedPaymentDetails(PaymentStatus initiated);

	public void createPaymentEvent(OrderPaymentEvent paymentEvent);

	OrderPaymentDetail getSuccessfulOrderPaymentDetail(String orderId);

	public Boolean checkIfIngenicoExist(String orderId);

	public void updateIngenicoPaymentMode(OrderPaymentRequest order);

	public void markTransactionFail(OrderPaymentRequest order);

	public StandaloneTransactionDetail createStandalonePaymentEvent(RazorPayEventData event);

	public void setNotificationDetail(String paymentId, String smsType, String emailType);

	public StandaloneTransactionDetail getStandAloneTransaction(String paymentId);

	List<OrderPaymentDetail> getRecentDisassociatedPaymentDetail(String contact, Date timeBeforeRefundIsEligible);

	List<OrderPaymentDetail> getRecentDisassociatedPaymentDetail(Date lowerDate, Date upperDate);

	List<OrderPaymentDetail> getRecentDisassociatedPaymentDetailsForCurrentDay();

	List<OrderPaymentDetail> getRecentDisassociatedPartnerPaymentDetail(Date lowerDate, Date upperDate);

	OrderPaymentDetail getActivePaymentDetailUsingPartnerOrderId(String partnerOrderId);

	OrderPaymentDetail markPaymentRefunded(PaymentResponse response);

	OrderPaymentDetail markPaymentRefundInitiated(PaymentResponse response);

	OrderPaymentDetail updateResponseUsingPartnerOrderId(PaymentStatus status, PaymentResponse response);

	OrderPaymentDetail getPaymentDetail(String partnerTransactionId);

    Map updateAndRedirect(PaymentResponse response, boolean validation);

    Map update(PaymentResponse response, boolean validation);

    Map updateForDineIn(PaymentResponse response, boolean validation);

    List<OrderPaymentDetail> getTransactionsApplicableForRefund(String contact);

	List<OrderPaymentDetail> getOrderPaymentDetailsForCurrentDayNeo(Date startTime, Date endTime);

	Boolean syncOrderAndInitiatePaytmRefundInBulk(List<OrderPaymentDetail> opdList);

}
