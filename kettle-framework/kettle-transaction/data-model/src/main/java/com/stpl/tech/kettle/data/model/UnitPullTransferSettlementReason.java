package com.stpl.tech.kettle.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.io.Serializable;
import java.util.Date;

@Entity
@Table(name = "UNIT_PULL_TRANSFER_SETTLEMENT_REASON")
public class UnitPullTransferSettlementReason implements Serializable {
    private static final long serialVersionUID = 119311297473022695L;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "UNIT_PULL_TRANSFER_SETTLEMENT_REASON_ID", unique = true, nullable = false)
    private int id;

    @Column(name = "UNIT_ID")
    private  int unitId;

    @Column(name = "DAY_CLOSURE_ID")
    private  int dayClosureId;

    @Column(name = "PULL_ID")
    private int pullId;

    @Column(name = "REASON")
    private String reason;

    @Column(name = "DAY_CLOSE_STATUS")
    private String status;

    @Temporal(TemporalType.DATE)
    @Column(name = "BUISSNES_DATE")
    private Date businessDate;

    @Column(name = "CREATED_BY")
    private int createdBy;

    @Column(name = "WITNESSED_BY")
    private  String witnessBy;

    @Column(name = "PAYMENT_MODE")
    private int paymentId;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public int getUnitId() {
        return unitId;
    }

    public void setUnitId(int unitId) {
        this.unitId = unitId;
    }

    public int getDayClosureId() {
        return dayClosureId;
    }

    public void setDayClosureId(int dayClosureId) {
        this.dayClosureId = dayClosureId;
    }

    public int getPullId() {
        return pullId;
    }

    public void setPullId(int pullId) {
        this.pullId = pullId;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public Date getBusinessDate() {
        return businessDate;
    }

    public void setBusinessDate(Date businessDate) {
        this.businessDate = businessDate;
    }

    public int getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(int createdBy) {
        this.createdBy = createdBy;
    }

    public String getWitnessBy() {
        return witnessBy;
    }

    public void setWitnessBy(String witnessBy) {
        this.witnessBy = witnessBy;
    }

    public int getPaymentId() {
        return paymentId;
    }

    public void setPaymentId(int paymentId) {
        this.paymentId = paymentId;
    }
}
