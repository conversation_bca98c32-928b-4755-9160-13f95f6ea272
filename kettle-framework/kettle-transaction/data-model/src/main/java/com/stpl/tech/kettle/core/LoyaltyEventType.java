/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core;

public enum LoyaltyEventType {

	MIGRATION("Addition"),
	CONTACT_NUMBER_VERIFICATION("Verification"),
	EMAIL_VERIFICATION("Verification"),
	REGULAR_REDEMPTION_VERIFICATION("Redemption"),
	REGULAR_REDEMPTION("Redemption"),
	OFFER_REDEMPTION("Redemption"),
	OUTLET_VISIT("Addition"),
	OUTLET_VISIT_NO_ADDITION("No Addition"),
	ONLINE_REDEMTION("Redemption"),
	ONLINE_ORDERING("Addition"),
	EXPIRATION("Expiration"),
	TABLE_ORDER("No Addition"),
	LOYALTY_GIFTING(""),
	OFFER_PURCHASE("Subtraction"),
	ORDER_CANCELLED("Addition"),
	CLM_TOPUP("Addition");
;

	private final String type;

	private LoyaltyEventType(String type) {
		this.type = type;
	}

	public String getType() {
		return type;
	}
}
