package com.stpl.tech.kettle.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "CUSTOMER_CAMPAIGN_JOURNEY")
public class CustomerCampaignJourney implements java.io.Serializable {

    private static final long serialVersionUID = 8477739259088089136L;

    private Integer campaignJourneyId;
    private Date sessionStartTime;
    private String utmSource;
    private String utmMedium;
    private Integer customerId;
    private String contactNumber;
    private String finalState;
    private Integer orderId;
    private Integer campaignId;
    private String deviceType;
    private String browserName;
    private String deviceOS;
    private String gclid;

    public CustomerCampaignJourney() {
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "CAMPAIGN_JOURNEY_ID")
    public Integer getCampaignJourneyId() {
        return campaignJourneyId;
    }

    public void setCampaignJourneyId(Integer campaignJourneyId) {
        this.campaignJourneyId = campaignJourneyId;
    }

    @Column(name = "SESSION_START_TIME")
    public Date getSessionStartTime() {
        return sessionStartTime;
    }

    public void setSessionStartTime(Date sessionStartTime) {
        this.sessionStartTime = sessionStartTime;
    }

    @Column(name = "UTM_SOURCE")
    public String getUtmSource() {
        return utmSource;
    }

    public void setUtmSource(String utmSource) {
        this.utmSource = utmSource;
    }

    @Column(name = "UTM_MEDIUM")
    public String getUtmMedium() {
        return utmMedium;
    }

    public void setUtmMedium(String utmMedium) {
        this.utmMedium = utmMedium;
    }


    @Column(name = "CUSTOMER_ID")
    public Integer getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    @Column(name = "CONTACT_NUMBER")
    public String getContactNumber() {
        return contactNumber;
    }

    public void setContactNumber(String contactNumber) {
        this.contactNumber = contactNumber;
    }

    @Column(name = "FINAL_STATE")
    public String getFinalState() {
        return finalState;
    }

    public void setFinalState(String finalState) {
        this.finalState = finalState;
    }

    @Column(name = "ORDER_ID")
    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    @Column(name = "CAMPAIGN_ID")
    public Integer getCampaignId() {
        return campaignId;
    }

    public void setCampaignId(Integer campaignId) {
        this.campaignId = campaignId;
    }

    @Column(name = "DEVICE_TYPE")
    public String getDeviceType() {
        return deviceType;
    }

    public void setDeviceType(String deviceType) {
        this.deviceType = deviceType;
    }

    @Column(name = "BROWSER_NAME")
    public String getBrowserName() {
        return browserName;
    }

    public void setBrowserName(String browserName) {
        this.browserName = browserName;
    }

    @Column(name = "DEVICE_OS")
    public String getDeviceOS() {
        return deviceOS;
    }

    public void setDeviceOS(String deviceOS) {
        this.deviceOS = deviceOS;
    }

    @Column(name = "GCLID")
    public String getGclid() {
        return gclid;
    }

    public void setGclid(String gclid) {
        this.gclid = gclid;
    }
}
