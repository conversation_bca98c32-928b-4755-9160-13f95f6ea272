package com.stpl.tech.kettle.data.model;

import com.stpl.tech.util.excelparser.annotations.ExcelField;
import com.stpl.tech.util.excelparser.annotations.ExcelObject;
import com.stpl.tech.util.excelparser.annotations.ExcelSheet;
import com.stpl.tech.util.excelparser.annotations.ParseType;

import javax.persistence.*;
import java.sql.Time;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@ExcelSheet(value = "Aggregate")
@ExcelObject(parseType = ParseType.ROW, start = 1, end = 0)
@Entity
@Table(name = "UNIT_PRODUCT_STOCK_EVENT_AGGREGATE")
public class UnitProductStockEventAggregate {
    private Integer key;
    private Date businessDate;
    @ExcelField
    private Date calculationDate;
    private Date cafeOpening;
    private Date cafeClosing;
    @ExcelField
    private Integer unitId;
    @ExcelField
    private Integer productId;
    @ExcelField
    private String productName;
    @ExcelField
    private Integer downTime;
    @ExcelField
    private Integer operationTime;
    @ExcelField
    private Float percentage;
    @ExcelField
    private Integer instance;

    public UnitProductStockEventAggregate() {
    }


    public UnitProductStockEventAggregate(Date businessDate, Date calculationDate, Date cafeOpening,
                                          Date cafeClosing, Integer unitId, Integer productId, String productName, Integer downTime, Integer operationTime, Integer instance) {
        this.businessDate = businessDate;
        this.calculationDate = calculationDate;
        this.cafeOpening = cafeOpening;
        this.cafeClosing = cafeClosing;
        this.unitId = unitId;
        this.productId = productId;
        this.productName = productName;
        this.downTime = downTime;
        this.operationTime = operationTime;
        this.percentage=((downTime * 100.0f) / operationTime);
        this.instance = instance;
    }

    public UnitProductStockEventAggregate(Date calculationDate, Integer downTime, Integer operationTime) {
        this.calculationDate = calculationDate;
        this.downTime = downTime;
        this.operationTime = operationTime;
    }

    @Id
    @Column(name = "KEY_ID")
    @GeneratedValue(strategy = IDENTITY)
    public Integer getKey() {
        return key;
    }

    public void setKey(Integer key) {
        this.key = key;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "BUSINESS_DATE", nullable = true, length = 10)
    public Date getBusinessDate() {
        return businessDate;
    }

    public void setBusinessDate(Date businessDate) {
        this.businessDate = businessDate;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "CALCULATION_DATE", nullable = true, length = 10)
    public Date getCalculationDate() {
        return calculationDate;
    }

    public void setCalculationDate(Date calculationDate) {
        this.calculationDate = calculationDate;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CAFE_OPENING")
    public Date getCafeOpening() {
        return cafeOpening;
    }

    public void setCafeOpening(Date cafeOpening) {
        this.cafeOpening = cafeOpening;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CAFE_CLOSING")
    public Date getCafeClosing() {
        return cafeClosing;
    }

    public void setCafeClosing(Date cafeClosing) {
        this.cafeClosing = cafeClosing;
    }

    @Column(name = "UNIT_ID")
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @Column(name = "PRODUCT_ID")
    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    @Column(name = "PRODUCT_NAME")
    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    @Column(name = "DOWNTIME")
    public Integer getDownTime() {
        return downTime;
    }

    public void setDownTime(Integer downTime) {
        this.downTime = downTime;
    }

    @Column(name = "OPERATION_TIME")
    public Integer getOperationTime() {
        return operationTime;
    }

    public void setOperationTime(Integer operationTime) {
        this.operationTime = operationTime;
    }

    @Column(name = "PERCENTAGE")
    public Float getPercentage() {
        return percentage;
    }

    public void setPercentage(Float percentage) {
        this.percentage = percentage;
    }

    @Column(name = "INSTANCE")
    public Integer getInstance() {
        return instance;
    }

    public void setInstance(Integer instance) {
        this.instance = instance;
    }
}
