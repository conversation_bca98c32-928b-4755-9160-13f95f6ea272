/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import javax.persistence.UniqueConstraint;

@Entity
@Table(name = "UNIT_BUDGET_EXCEEDED_DATA", uniqueConstraints = { @UniqueConstraint(columnNames = { "UNIT_ID", "DAY",
		"MONTH", "YEAR", "BUDGET_CATEGORY", "NOTIFICATION_TYPE" }) })
public class UnitBudgetExceededData implements java.io.Serializable {

	private static final long serialVersionUID = 1L;

	private int id;
	private int unitId;
	private String budgetCategory;
	private String expenseType;
	private String expenseLabel;
	private Integer month;
	private Integer day;
	private Integer year;
	private String notificationType;
	private BigDecimal budgetAmount;
	private BigDecimal currentAmount;
	private BigDecimal requestedAmount;
	private String expenseSource;
	private Integer createdBy;
	private Date createdOn;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "EXCEEDED_DATA_ID", unique = true, nullable = false)
	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	@Column(name = "UNIT_ID", nullable = false)
	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	@Column(name = "EXPENSE_TYPE", nullable = false)
	public String getExpenseType() {
		return expenseType;
	}

	public void setExpenseType(String expenseType) {
		this.expenseType = expenseType;
	}

	@Column(name = "MONTH", nullable = false)
	public Integer getMonth() {
		return month;
	}

	public void setMonth(Integer month) {
		this.month = month;
	}

	@Column(name = "DAY", nullable = false)
	public Integer getDay() {
		return day;
	}

	public void setDay(Integer day) {
		this.day = day;
	}

	@Column(name = "YEAR", nullable = false)
	public Integer getYear() {
		return year;
	}

	public void setYear(Integer year) {
		this.year = year;
	}

	@Column(name = "NOTIFICATION_TYPE", nullable = false)
	public String getNotificationType() {
		return notificationType;
	}

	public void setNotificationType(String notificationType) {
		this.notificationType = notificationType;
	}

	@Column(name = "BUDGET_AMOUNT", nullable = false)
	public BigDecimal getBudgetAmount() {
		return budgetAmount;
	}

	public void setBudgetAmount(BigDecimal budgetAmount) {
		this.budgetAmount = budgetAmount;
	}

	@Column(name = "CURRENT_AMOUNT", nullable = false)
	public BigDecimal getCurrentAmount() {
		return currentAmount;
	}

	public void setCurrentAmount(BigDecimal currentAmount) {
		this.currentAmount = currentAmount;
	}

	@Column(name = "REQUESTED_AMOUNT", nullable = false)
	public BigDecimal getRequestedAmount() {
		return requestedAmount;
	}

	public void setRequestedAmount(BigDecimal requestedAmount) {
		this.requestedAmount = requestedAmount;
	}
	
	
	@Column(name = "EXPENSE_LABEL", nullable = false)
	public String getExpenseLabel() {
		return expenseLabel;
	}

	public void setExpenseLabel(String expenseLabel) {
		this.expenseLabel = expenseLabel;
	}

	@Column(name = "EXPENSE_SOURCE", nullable = false)
	public String getExpenseSource() {
		return expenseSource;
	}

	public void setExpenseSource(String expenseSource) {
		this.expenseSource = expenseSource;
	}

	@Column(name = "BUDGET_CATEGORY", nullable = true)
	public String getBudgetCategory() {
		return budgetCategory;
	}

	public void setBudgetCategory(String budgetCategory) {
		this.budgetCategory = budgetCategory;
	}

	@Column(name = "CREATED_BY", nullable = false)
	public Integer getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(Integer createdBy) {
		this.createdBy = createdBy;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATED_ON", nullable = true, length = 19)
	public Date getCreatedOn() {
		return createdOn;
	}

	public void setCreatedOn(Date createdOn) {
		this.createdOn = createdOn;
	}

}