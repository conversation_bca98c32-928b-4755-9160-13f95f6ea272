package com.stpl.tech.kettle.core.service.impl;

import com.stpl.tech.kettle.core.exception.PaymentFailureException;
import com.stpl.tech.kettle.core.notification.ErrorNotification;
import com.stpl.tech.kettle.core.notification.StandaloneTransactionNotification;
import com.stpl.tech.kettle.core.notification.StandaloneTransactionReceipt;
import com.stpl.tech.kettle.core.notification.sms.CustomerSMSNotificationType;
import com.stpl.tech.kettle.core.payment.PaymentApplicableResponse;
import com.stpl.tech.kettle.core.payment.PaymentPartnerType;
import com.stpl.tech.kettle.core.payment.factory.PaymentFactory;
import com.stpl.tech.kettle.core.service.AGSPaymentService;
import com.stpl.tech.kettle.core.service.EzetapPaymentService;
import com.stpl.tech.kettle.core.service.GooglePaymentService;
import com.stpl.tech.kettle.core.service.IngenicoPaymentService;
import com.stpl.tech.kettle.core.service.PayTMNewPaymentService;
import com.stpl.tech.kettle.core.service.PayTMPaymentService;
import com.stpl.tech.kettle.core.service.PaymentServiceNew;
import com.stpl.tech.kettle.core.service.RazorPayPaymentService;
import com.stpl.tech.kettle.core.service.RefundDataSmsToken;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.converter.DataConverter;
import com.stpl.tech.kettle.data.dao.PaymentGatewayDao;
import com.stpl.tech.kettle.data.model.OrderPaymentDetail;
import com.stpl.tech.kettle.data.model.OrderPaymentEvent;
import com.stpl.tech.kettle.data.model.StandaloneTransactionDetail;
import com.stpl.tech.kettle.delivery.model.PaymentPartner;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.core.external.notification.service.NotificationService;
import com.stpl.tech.master.core.external.notification.service.SMSClientProviderService;
import com.stpl.tech.master.core.payment.config.PaytmConfig;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.payment.model.AGS.AGSPaymentCMResponse;
import com.stpl.tech.master.payment.model.AGS.AGSPaymentS2SResponse;
import com.stpl.tech.master.payment.model.AGS.AGSPaymentS2SStatus;
import com.stpl.tech.master.payment.model.OrderPayment;
import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.master.payment.model.PaymentRequest;
import com.stpl.tech.master.payment.model.PaymentStatus;
import com.stpl.tech.master.payment.model.PaymentStatusChangeRequest;
import com.stpl.tech.master.payment.model.PaymentVO;
import com.stpl.tech.master.payment.model.gpay.GPayPaymentStatus;
import com.stpl.tech.master.payment.model.patymNew.PaytmFetchPaymentOptionsRequest;
import com.stpl.tech.master.payment.model.patymNew.PaytmOauth;
import com.stpl.tech.master.payment.model.patymNew.PaytmOauthRequest;
import com.stpl.tech.master.payment.model.patymNew.PaytmOauthResponse;
import com.stpl.tech.master.payment.model.patymNew.PaytmParamResponse;
import com.stpl.tech.master.payment.model.patymNew.PaytmParams;
import com.stpl.tech.master.payment.model.patymNew.PaytmStatusResponse;
import com.stpl.tech.master.payment.model.patymNew.PaytmValidateSSOTokenResponse;
import com.stpl.tech.master.payment.model.paytm.PaytmCreateRequest;
import com.stpl.tech.master.payment.model.paytm.PaytmCreateResponse;
import com.stpl.tech.master.payment.model.paytm.PaytmPaymentStatus;
import com.stpl.tech.master.payment.model.razorpay.BasicTransactionInfo;
import com.stpl.tech.master.payment.model.razorpay.RazorPayEventData;
import com.stpl.tech.master.payment.model.razorpay.RazorPayPaymentResponse;
import com.stpl.tech.master.payment.model.razorpay.RazorPayPaymentStatus;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.jms.JMSException;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
public class PaymentServiceNewImpl implements PaymentServiceNew {

    private static final Logger LOG = LoggerFactory.getLogger(PaymentServiceNewImpl.class);

    @Autowired
    private PaymentGatewayDao paymentGatewayDao;
    @Autowired
    private PayTMPaymentService payTMService;
    @Autowired
    private GooglePaymentService gPayService;
    @Autowired
    private RazorPayPaymentService razorPayService;
    @Autowired
    private IngenicoPaymentService ingenicoService;
    @Autowired
    private MasterDataCache masterCache;
    @Autowired
    private EnvironmentProperties props;
    @Autowired
    private AGSPaymentService agsPaymentService;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private SMSClientProviderService providerService;
    @Autowired
    private PayTMNewPaymentService payTMNewPaymentService;
    @Autowired
    private PaymentFactory paymentFactory;
    @Autowired
    private RazorPayPaymentService razorPayPaymentService;

    @Autowired
    private EnvironmentProperties env;


    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public PaymentRequest createPaymentRequest(OrderPaymentRequest order,
                                               PaymentPartnerType paymentPartnerType,
                                               Map<String, String> map)
            throws Exception {
        return paymentFactory.getCreatePaymentAdapter(paymentPartnerType)
                .createPaymentRequest(order, map);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Object updatePayment(Object payment, PaymentPartnerType paymentPartnerType,
                                boolean skipSignatureVerification,Integer brandId) throws PaymentFailureException {
        return paymentFactory.getCreatePaymentAdapter(paymentPartnerType)
                .updatePayment(payment, skipSignatureVerification,brandId);
    }

    @Deprecated
    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public PaymentRequest createRequest(OrderPaymentRequest order)
            throws PaymentFailureException, DataNotFoundException {
        PaymentRequest request = null;
        if (order.getPaymentModeId() == PaymentPartner.PAYTM.getSystemId(props.getEnvironmentType())) {
            request = payTMService.createRequest(order);
        } else if (order.getPaymentModeId() == PaymentPartner.RAZORPAY.getSystemId(props.getEnvironmentType())) {
            request = razorPayService.createRequest(props.getEnvironmentType(), order);
        }
// else if(order.getPaymentModeId() == PaymentPartner.INGENICO.getSystemId(props.getEnvironmentType())){
//			request = ingenicoService.createRequest(props.getEnvironmentType(), order);
//		}

        if (request == null) {
            throw new PaymentFailureException("Online Payment for "
                    + masterCache.getPaymentMode(order.getPaymentModeId()).getName() + " is not supported");
        } else {
            paymentGatewayDao.createRequest(request, order);
        }
        return request;
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Map updateRazorPayResponseForHangingPayment(RazorPayPaymentResponse response)
            throws PaymentFailureException {
        return razorPayService.updateRazorPayResponseForHangingPayment(response);
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Map updateRazorPayResponseForHangingPaymentDineIn(RazorPayPaymentResponse response)
            throws PaymentFailureException {
        return razorPayService.updateRazorPayResponseForHangingPaymentDineIn(response);
    }

    @Override
    public PaytmStatusResponse getPaytmStatusResponse(String orderId) throws Exception {
        return payTMService.getPaytmStatusResponse(orderId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void createPaymentEvent(RazorPayEventData event) {
        paymentGatewayDao.createPaymentEvent(event);
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public StandaloneTransactionDetail createStandalonePaymentEvent(RazorPayEventData event) {
        if (event.getInfo() == null) {
            event.setInfo(new BasicTransactionInfo());
        }
        event.getInfo().setCurrentStatus(getStatus(event.getEvent()));
        return paymentGatewayDao.createStandalonePaymentEvent(event);
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean setNotificationDetail(String paymentId, String smsType, String emailType) {
        paymentGatewayDao.setNotificationDetail(paymentId, smsType, emailType);
        return true;
    }


    private String getStatus(String event) {
        switch (event) {
            case "payment.authorized":
                return "AUTHORIZED";
            case "payment.captured":
                return "CAPTURED";
            case "payment.failed":
                return "FAILED";
            case "order.paid":
                return "SUCCESSFUL";
            default:
                return "UNKNOWN";
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public RazorPayPaymentResponse fetchRazorPayPayment(String paymentId) throws PaymentFailureException {
        return razorPayService.fetchRazorPayPayment(paymentId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public RazorPayPaymentResponse fetchResponseByRazorPayByPartnerOrderId(String partnerOrderId,Integer brandId) throws PaymentFailureException {
        return razorPayService.fetchOrder(props.getEnvironmentType(), partnerOrderId,brandId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean cancelPayment(PaymentStatusChangeRequest cancel) {
        return paymentGatewayDao.cancelPayment(cancel);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean failurePayment(PaymentStatusChangeRequest failure) {
        return paymentGatewayDao.paymentFailure(failure);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean checkPaymentStatus(PaymentVO payment) {
        OrderPaymentDetail paymentDetail = paymentGatewayDao.getActivePaymentDetail(payment.getOrderId());
        if (paymentDetail != null) {
            int paymentMode = paymentDetail.getPaymentModeId();
            if (PaymentPartner.RAZORPAY.getSystemId(props.getEnvironmentType()) == paymentMode) {
                return razorPayService.checkRazorpayPayment(paymentDetail);
            } else if (PaymentPartner.PAYTM.getSystemId(props.getEnvironmentType()) == paymentMode) {
                return payTMService.checkPaytmPaymentStatus(paymentDetail);
            } else if (PaymentPartner.INGENICO.getSystemId(props.getEnvironmentType()) == paymentMode) {
                return ingenicoService.checkIngenicoPaymentStatus(paymentDetail);
            }
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public OrderPayment refundByPaymentId(Integer paymentDetailId) throws IOException, PaymentFailureException {
        OrderPaymentDetail paymentDetail = paymentGatewayDao.getPaymentDetail(paymentDetailId);
        if (paymentDetail != null) {
            OrderPayment payment = DataConverter.convert(paymentDetail, masterCache.getPaymentMode(paymentDetail.getPaymentModeId()));
            return refundPayment(payment);
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void updateStatusOfDisassociatedPayments(String contact, Date timeBeforeRefundIsEligible) {
        LOG.info("updating status of disassociated payments");

        List<OrderPaymentDetail> payments = paymentGatewayDao.getRecentDisassociatedPaymentDetail(contact, timeBeforeRefundIsEligible);
        if (payments != null && payments.size() > 0) {
            for (OrderPaymentDetail opd : payments) {
                if (PaymentPartner.RAZORPAY.getSystemId(props.getEnvironmentType()) == opd.getPaymentModeId()) {
                    LOG.info("Verifying status of RazorPay payment with id " + opd.getPartnerOrderId());
                    try {
                        RazorPayPaymentResponse payment = razorPayService.fetchOrder(props.getEnvironmentType(),
                                opd.getPartnerOrderId(),opd.getBrandId());
                        if (payment != null) {
                            updateRazorPayResponseForHangingPayment(payment);
                        }
                    } catch (PaymentFailureException e) {
                        LOG.error("Error in updaing payment response for razor pay payment id "
                                + opd.getPartnerOrderId());
                    }
                } else if (PaymentPartner.PAYTM.getSystemId(props.getEnvironmentType()) == opd.getPaymentModeId()) {
                    LOG.info("Verifying status of Paytm payment with id " + opd.getPartnerOrderId());
                    try {
                        if (opd.getPaymentSource().equals(ApplicationName.DINE_IN.value())) {
                            PaytmStatusResponse paytmStatusResponse = payTMNewPaymentService.getPayTmPaymentStatus(opd.getExternalOrderId());
                            if (paytmStatusResponse != null) {
                                LOG.info("Payment response ", paytmStatusResponse);

                                PaytmCreateResponse sudoPaymentResponse = new PaytmCreateResponse();
                                sudoPaymentResponse.setOrderId(opd.getExternalOrderId());
                                sudoPaymentResponse.setStatus(paytmStatusResponse.getBody().getResultInfo().getResultStatus());
                                sudoPaymentResponse.setTransactionId(paytmStatusResponse.getBody().getTxnId());
                                PaymentPartnerType paymentPartnerType = PaymentPartnerType.PAYTM2;
                                boolean skipSignatureVerification = true;
                                updatePayment(sudoPaymentResponse, paymentPartnerType, skipSignatureVerification,opd.getBrandId());
                                //updatePaytmResponseWithoutVerification(sudoPaymentResponse);
                            }
                        } else {
                            LOG.info("Payment source is not dine in");
                        }

                    } catch (Exception e) {
                        LOG.error("Error in updating payment response for Paytm payment id "
                                + opd.getPartnerOrderId());
                    }
                } else {

                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void updateStatusOfDisassociatedPayments(Date lowerDate, Date upperDate) {
        LOG.info("updating status of disassociated payments");
        List<OrderPaymentDetail> payments = paymentGatewayDao.getRecentDisassociatedPaymentDetail(lowerDate, upperDate);
        if (payments != null && !payments.isEmpty()) {
            for (OrderPaymentDetail opd : payments) {
                if (PaymentPartner.RAZORPAY.getSystemId(props.getEnvironmentType()) == opd.getPaymentModeId()) { // PaymentModeId = 12
                    if (!PaymentStatus.REFUND_PROCESSED.name().equalsIgnoreCase(opd.getPaymentStatus())) {
                        LOG.info("Verifying status of RazorPay payment with id " + opd.getPartnerOrderId());
                        try {
                            RazorPayPaymentResponse payment = razorPayService.fetchOrder(props.getEnvironmentType(),
                                    opd.getPartnerOrderId(), opd.getBrandId());
                            if (payment != null) {
                                updateRazorPayResponseForHangingPaymentDineIn(payment); // update order_payment_detail to setstatus as REFUND_INITIATED
                            }
                        } catch (PaymentFailureException e) {
                            LOG.error("Error in updaing payment response for razor pay payment id "
                                    + opd.getPartnerOrderId());
                        }
                    }
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void updateStatusOfDisassociatedPaymentsDaily() {
        LOG.info("updating status of disassociated payments");
        List<OrderPaymentDetail> payments = paymentGatewayDao.getRecentDisassociatedPaymentDetailsForCurrentDay();
        if (payments != null && !payments.isEmpty()) {
            for (OrderPaymentDetail opd : payments) {
                if (PaymentPartner.RAZORPAY.getSystemId(props.getEnvironmentType()) == opd.getPaymentModeId()) { // PaymentModeId = 12
                    if (Objects.isNull(opd.getPaymentStatus()) || !opd.getPaymentStatus().equalsIgnoreCase(PaymentStatus.REFUND_PROCESSED.name())) {
                        LOG.info("Verifying status of RazorPay payment with id " + opd.getPartnerOrderId());
                        try {
                            RazorPayPaymentResponse payment = razorPayService.fetchOrder(props.getEnvironmentType(),
                                    opd.getPartnerOrderId(), opd.getBrandId());
                            if (payment != null) {
                                updateRazorPayResponseForHangingPaymentDineIn(payment); // update order_payment_detail to setstatus as REFUND_INITIATED
                            }
                        } catch (PaymentFailureException e) {
                            LOG.error("Error in updaing payment response for razor pay payment id "
                                    + opd.getPartnerOrderId());
                        }
                    }
                }
            }
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public void updateStatusOfDisassociatedPartnerPayments(Date lowerDate, Date upperDate) {
        if(!env.getKettlePaytmRefundCronStatus()) return;
        LOG.info("updating status of disassociated payments");
        List<OrderPaymentDetail> payments = paymentGatewayDao.getRecentDisassociatedPartnerPaymentDetail(lowerDate, upperDate);
        List<OrderPaymentDetail> paymentsToBeSynced = new ArrayList<>();
        if (payments != null && payments.size() > 0) {
            for (OrderPaymentDetail opd : payments) {
                if (opd.getPartnerTransactionId() != null) {
                    try{
                        OrderPaymentDetail paymentDetail = payTMNewPaymentService.fetchKettlePaymentStatus(opd);
                        if(paymentDetail != null){
                            paymentsToBeSynced.add(paymentDetail);
                        }
                    }
                    catch(Exception e){
                        LOG.error("[PaytmSyncAndRefund]: Exception while order syncing/marking refund | OrderPaymentDetailId={} | OrderId={} | PartnerTxnId={} | Error={}",
                                opd.getOrderPaymentDetailId(), opd.getOrderId(), opd.getPartnerTransactionId(), e.getMessage());
                    }
                }
            }

            try{
                Boolean isSyncedOrMarked = payTMService.syncOrderAndInitiatePaytmRefundInBulk(paymentsToBeSynced);
                if (isSyncedOrMarked) {
                    LOG.info("[PaytmSyncAndRefund] Success: Synced and marked refund for {} payments", paymentsToBeSynced.size());
                }
                else {
                    LOG.info("[PaytmSyncAndRefund] Failed: Could not sync/mark refund for {} payments", paymentsToBeSynced.size());
                }
            }
            catch (Exception e){
                LOG.error("[PaytmSyncAndRefund]: Exception while syncing/marking refund for {} payments | Error={}", paymentsToBeSynced.size(), e.getMessage());
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void updateStatusOfDisassociatedPaymentsNeo(Date startTime, Date endTime) {
        LOG.info(":: :: :: :: updating status of disassociated payments for NEO :: :: :: ::");

        // getting all order payment detail data with payment status null
        List<OrderPaymentDetail> payments = paymentGatewayDao.getOrderPaymentDetailsForCurrentDayNeo(startTime, endTime);
        if (Objects.nonNull(payments) && !payments.isEmpty()) {
            for (OrderPaymentDetail opd : payments) {
                if (PaymentPartner.RAZORPAY.getSystemId(props.getEnvironmentType()) == opd.getPaymentModeId()) {

                    if (Objects.nonNull(opd.getOrderId()) && Objects.nonNull(opd.getPaymentStatus()) && opd.getPaymentStatus().equals(PaymentStatus.SUCCESSFUL.name())) {
                        LOG.info("Exclude opd id ::  {} orderid :: {} opd status :: {} ", opd.getOrderPaymentDetailId(), opd.getOrderId(), opd.getPaymentStatus());
                        continue;
                    }

                    LOG.info("Verifying status of RazorPay payment with OPD id {} PO id {}", opd.getOrderPaymentDetailId(), opd.getPartnerOrderId());
                    try {
                        RazorPayPaymentResponse paymentResponse = razorPayService.fetchOrder(props.getEnvironmentType(),
                                opd.getPartnerOrderId(), opd.getBrandId());
                        if (Objects.nonNull(paymentResponse) && Objects.nonNull(paymentResponse.getStatus())) {
                            PaymentStatus status = null;
                            if (isRazorPayPaymentSuccess(paymentResponse)) {
                                if (Objects.isNull(opd.getOrderId())) {
                                    paymentResponse.setStatus("successful");
                                    status = PaymentStatus.REFUND_INITIATED;
                                } else {
                                    paymentResponse.setStatus("successful");
                                    status = PaymentStatus.SUCCESSFUL;
                                }
                            } else if (paymentResponse.getStatus().equalsIgnoreCase("refunded")) {
                                paymentResponse.setStatus("successful");
                                status = PaymentStatus.REFUND_PROCESSED;
                            } else if (paymentResponse.getStatus().equalsIgnoreCase("failed")) {
                                paymentResponse.setStatus("failed");
                                status = PaymentStatus.FAILED;
                            }
                            if (Objects.nonNull(status)) {
                                paymentGatewayDao.updateResponseUsingPartnerOrderId(status, paymentResponse);
                            }
                        }
                    } catch (PaymentFailureException e) {
                        LOG.error("Error in updating payment response for razor pay payment id {}", opd.getPartnerOrderId());
                    }
                }
            }
        }
    }

    private boolean isRazorPayPaymentSuccess(RazorPayPaymentResponse response) {
        return response.getStatus().equalsIgnoreCase(RazorPayPaymentStatus.SUCCESSFUL.value())
                || response.getStatus().equalsIgnoreCase(RazorPayPaymentStatus.CAPTURED.value())
                || response.getStatus().equalsIgnoreCase(RazorPayPaymentStatus.AUTHORIZED.value());
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public OrderPayment getDisassociatedPayment(String contact) throws PaymentFailureException {
        Date timeBeforeRefundIsEligible = AppUtils.addMinutesToDate(AppUtils.getCurrentTimestamp(), -1 * props.getNumberOfMinuteForRefundBlock());
        OrderPaymentDetail paymentDetail = paymentGatewayDao.getDisassociatedPaymentDetail(contact, timeBeforeRefundIsEligible);
        if (paymentDetail != null) {
            LOG.info("Disassociated payment detail ", paymentDetail);
            if (paymentDetail.getPartnerTransactionId() == null) {
                int paymentModeId = paymentDetail.getPaymentModeId();
                EnvType env = props.getEnvironmentType();
                if (paymentModeId == PaymentPartner.PAYTM.getSystemId(env)) {
                    LOG.info("Disassociated payment is of Paytm ");
                    if (paymentDetail.getPaymentSource().equals(ApplicationName.DINE_IN.value())) {
                        paymentDetail = payTMNewPaymentService.handleDisassociatedPayment(paymentDetail);
                    } else {
                        return payTMService.checkPaytmPayment(paymentDetail);
                    }
                } else if (paymentModeId == PaymentPartner.RAZORPAY.getSystemId(env)) {
                    paymentDetail = razorPayService.handleDisassociatedPayment(paymentDetail);
                }
            }
            return DataConverter.convert(paymentDetail, masterCache.getPaymentMode(paymentDetail.getPaymentModeId()));
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public OrderPayment getDisassociatedPayment() throws PaymentFailureException {
        Date timeBeforeRefundIsEligible = AppUtils.addMinutesToDate(AppUtils.getCurrentTimestamp(), -10);
        List<OrderPaymentDetail> orderPaymentDetailList = paymentGatewayDao.getDisassociatedPaymentDetail(timeBeforeRefundIsEligible);
        for (OrderPaymentDetail paymentDetail: orderPaymentDetailList) {
            if (paymentDetail != null) {
                LOG.info("Disassociated payment detail ", paymentDetail);
                if (paymentDetail.getPartnerTransactionId() == null) {
                    int paymentModeId = paymentDetail.getPaymentModeId();
                    EnvType env = props.getEnvironmentType();
                    if (paymentModeId == PaymentPartner.RAZORPAY.getSystemId(env)) {
                        paymentDetail = razorPayService.handleDisassociatedPaymentForDineIn(paymentDetail);
                    }
                }
                return DataConverter.convert(paymentDetail, masterCache.getPaymentMode(paymentDetail.getPaymentModeId()));
            }
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<OrderPayment> getPendingRefunds() {
        List<OrderPaymentDetail> paymentDetailList = paymentGatewayDao.getPendingRefunds();
        if (paymentDetailList != null) {
            return paymentDetailList.stream()
                    .map(orderPaymentDetail -> DataConverter.convert(orderPaymentDetail,
                            masterCache.getPaymentMode(orderPaymentDetail.getPaymentModeId())))
                    .collect(Collectors.toList());
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public OrderPayment refundPayment(Integer orderId) throws IOException, PaymentFailureException {
        OrderPaymentDetail paymentDetail = paymentGatewayDao.getSuccessfulPaymentDetailFromOrderId(orderId);
        if (paymentDetail != null) {
            OrderPayment payment = DataConverter.convert(paymentDetail, masterCache.getPaymentMode(paymentDetail.getPaymentModeId()));
            return refundPayment(payment);
        }
        LOG.error("Did not find any external payment detail for orderID::::: {}", orderId);
        throw new PaymentFailureException("Could not invoke refund for Payment Detail");
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public OrderPayment refundDoublePayments(String partnerTransactionId) throws IOException, PaymentFailureException {
        OrderPaymentDetail paymentDetail = paymentGatewayDao.getSuccessfulPaymentDetailFromPartnerTransactionId(partnerTransactionId);
        if (paymentDetail != null) {
            OrderPayment payment = DataConverter.convert(paymentDetail, masterCache.getPaymentMode(paymentDetail.getPaymentModeId()));
            if (Objects.isNull(payment.getRefundId())) {
                return refundPayment(payment);
            } else {
                return payment;
            }
        }
        LOG.error("Did not find any external payment detail for partnerTransactionId::::: {}", partnerTransactionId);
        throw new PaymentFailureException("Could not invoke refund for Payment Detail");
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public OrderPayment getPaymentStatus(Integer orderId) throws PaymentFailureException {
        OrderPaymentDetail paymentDetail = paymentGatewayDao.getPaymentDetailFromOrderId(orderId);
        if (paymentDetail != null) {
            return DataConverter.convert(paymentDetail, masterCache.getPaymentMode(paymentDetail.getPaymentModeId()));
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public PaytmCreateRequest getPayTMQRCodeIdForKIOSK(OrderPaymentRequest order) throws PaymentFailureException {
        return payTMService.getPayTMQRCodeIdForKIOSK(order);
    }

    @Override
    public PaytmPaymentStatus checkKIOSKPaytmQRPaymentStatus(String orderId) throws PaymentFailureException {
        try {
            LOG.info("Checking paytm payment status for OrderID " + orderId);
            return payTMService.checkKIOSKPaytmQRPaymentStatus(orderId);
        } catch (Exception ex) {
            LOG.error("Exception Occurred ", ex);
            throw new PaymentFailureException("Could not create payTM QR code.");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public GPayPaymentStatus checkGPayQRPaymentStatus(OrderPaymentRequest request) throws PaymentFailureException {
        return gPayService.checkGPayQRPaymentStatus(request);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public PaymentApplicableResponse getTransactionsApplicableForRefund(String contact) {
        Date timeBeforeRefundIsEligible = AppUtils.addMinutesToDate(AppUtils.getCurrentTimestamp(), -1 * props.getNumberOfMinuteForRefundBlock());
        updateStatusOfDisassociatedPayments(contact, timeBeforeRefundIsEligible);
        List<OrderPaymentDetail> orderPaymentDetailList = paymentGatewayDao.getTransactionsApplicableForRefund(contact);
        List<OrderPaymentDetail> yetToBeProcessedPayments = paymentGatewayDao.getRecentDisassociatedPaymentDetailAfterTime(contact, timeBeforeRefundIsEligible);
        PaymentApplicableResponse response = new PaymentApplicableResponse(orderPaymentDetailList, yetToBeProcessedPayments);
        return response;
    }

    @Override
    public boolean refundKIOSKPaytmQRPaymentAmount(String orderId, BigDecimal amountToRefund, String refundReason) throws PaymentFailureException {
        try {
            LOG.info("Refunding paytm payment amount for orderId " + orderId);
            return payTMService.refundKIOSKPaytmQRPaymentAmount(orderId, amountToRefund, refundReason);
        } catch (Exception ex) {
            LOG.error("Exception Occurred ", ex);
            throw new PaymentFailureException("Could not refund payTM amount.");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void markTransactionCancel(OrderPaymentRequest order) throws Exception {
        try {
            paymentGatewayDao.markTransactionFail(order);
        } catch (Exception e) {
            LOG.error("Error while updating transaction status :::::::: {}", e.getMessage());
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void initiateAutoRefunds() {
        int tenMinutes = 600000;
        List<OrderPaymentDetail> payments = paymentGatewayDao.getInitiatedPaymentDetails(PaymentStatus.INITIATED);
        if (payments != null && payments.size() > 0) {
            for (OrderPaymentDetail payment : payments) {
                //initiate refunds for payments with no order id and order settlement id for more than
                if (AppUtils.getSecondsDiff(AppUtils.getCurrentTimestamp(), payment.getRequestTime()) > tenMinutes) {
                    OrderPayment paymentRequest = DataConverter.convert(payment, masterCache.getPaymentMode(payment.getPaymentModeId()));
                    try {
                        paymentRequest = refundByPaymentId(payment.getOrderPaymentDetailId());
                        logRefundAttempt(paymentRequest, PaymentStatus.SUCCESSFUL, null, null);
                    } catch (IOException | PaymentFailureException e) {
                        logRefundAttempt(paymentRequest, PaymentStatus.FAILED, "refund_failed",
                                paymentRequest.getFailureReason());
                    }
                }
            }
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void initiateAutoRefundsForDineIn() {
        int tenMinutes = 600000;
        List<OrderPaymentDetail> payments = paymentGatewayDao.getInitiatedPaymentDetails(PaymentStatus.REFUND_INITIATED);
        if(payments!=null && payments.size()>0){
            for(OrderPaymentDetail payment : payments){
                //initiate refunds for payments with no order id and order settlement id for more than
                if(AppUtils.getSecondsDiff(AppUtils.getCurrentTimestamp(),payment.getRequestTime()) > tenMinutes){
                    OrderPayment paymentRequest = DataConverter.convert(payment,masterCache.getPaymentMode(payment.getPaymentModeId()));
                    try {
                        paymentRequest = refundByPaymentId(payment.getOrderPaymentDetailId());
                        logRefundAttempt(paymentRequest, PaymentStatus.SUCCESSFUL, null, null);
                    } catch (IOException | PaymentFailureException e) {
                        logRefundAttempt(paymentRequest, PaymentStatus.FAILED,"refund_failed",
                                paymentRequest.getFailureReason());
                    }
                }
            }
        }
    }


    private void logRefundAttempt(OrderPayment paymentRequest, PaymentStatus paymentStatus,
                                  String errorCode, String errorDescription) {
        OrderPaymentEvent paymentEvent = new OrderPaymentEvent();
        paymentEvent.setOrderPaymentDetailId(paymentRequest.getOrderPaymentDetailId());
        paymentEvent.setEventType(PaymentStatus.REFUND_INITIATED.name());
        paymentEvent.setPaymentStatus(paymentStatus.name());
        paymentEvent.setCreateTime(AppUtils.getCurrentTimestamp());
        paymentEvent.setPaymentId(paymentRequest.getPartnerTransactionId());
        if (paymentStatus.equals(PaymentStatus.FAILED)) {
            paymentEvent.setErrorCode(errorCode);
            paymentEvent.setErrorDescription(errorDescription);
        }
        paymentGatewayDao.createPaymentEvent(paymentEvent);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public OrderPayment refundPayment(OrderPayment request) throws IOException, PaymentFailureException {
        EnvType envType = props.getEnvironmentType();

        // TODO: Add condition for PAYTM UPI

        if (request.getPaymentModeId() == PaymentPartner.PAYTM.getSystemId(props.getEnvironmentType())) {
            if (request.getPaymentSource().equalsIgnoreCase("DINE_IN")) {
                request = payTMNewPaymentService.refundRequest(request);
            } else {
                request = payTMService.refundRequest(request);
                //request = payTMService.refundPaytmUpiQR(request);
            }
        } else if (request.getPaymentModeId() == PaymentPartner.RAZORPAY.getSystemId(envType)) {
            request = razorPayService.refundRequest(envType, request);
        } else if (request.getPaymentModeId() == PaymentPartner.INGENICO.getSystemId(envType)) {
            request = ingenicoService.refundRequest(envType, request);
        } else if (request.getPaymentModeId() == PaymentPartner.GPAY.getSystemId(envType)) {
            request = gPayService.refundRequest(envType, request);
        }

        if (request.getPaymentStatus().equals(PaymentStatus.REFUND_PROCESSED)) {
            LOG.info("Refund processed");
            request = paymentGatewayDao.refundPayment(request);
            String message = String.format("Refund successfully processed for: \n" +
                            "Application: %s\n" +
                            "Contact: %s\n" +
                            "Payment Id: %s \n" +
                            "Order Id: %s",
                    request.getPaymentSource(), request.getContactNumber(), request.getPartnerTransactionId(), request.getExternalOrderId());

            SlackNotificationService.getInstance()
                    .sendNotification(envType, "Kettle", null,
                            SlackNotification.REFUNDS.getChannel(envType), message);
            // refund sms
            if(Objects.nonNull(request.getBrandId()) && request.getBrandId() == AppConstants.CHAAYOS_BRAND_ID) {
                refundNotification(request.getContactNumber(), request);
            }
        } else if (request.getPaymentStatus().equals(PaymentStatus.REFUND_INITIATED)) {
            LOG.info("Refund Initiated");
            request = paymentGatewayDao.refundPayment(request);
            String message = String.format("Refund Initiated for: \n" +
                            "Application: %s\n" +
                            "Contact: %s\n" +
                            "Payment Id: %s \n" +
                            "Order Id: %s",
                    request.getContactNumber(), request.getPaymentSource(), request.getPartnerTransactionId(), request.getExternalOrderId());

            SlackNotificationService.getInstance()
                    .sendNotification(envType, "Kettle", null,
                            SlackNotification.REFUNDS.getChannel(envType), message);
        } else {
            LOG.info("Refund failed");
            String message = String.format("Refund failed for: \n" +
                            "Application: %s\n" +
                            "Contact: %s\n" +
                            "Payment Id: %s \n" +
                            "Order Id: %s" +
                            "Failure Reason: %s",
                    request.getContactNumber(), request.getPaymentSource(), request.getPartnerTransactionId(),
                    request.getExternalOrderId(), request.getFailureReason());

            SlackNotificationService.getInstance()
                    .sendNotification(envType, "Kettle", null,
                            SlackNotification.REFUNDS.getChannel(envType), message);
            throw new PaymentFailureException(message);
        }
        return request;
    }

    @Override
    public OrderPayment refundPaytmUpiQR(OrderPayment paymentRefundRequest) throws PaymentFailureException {
        return payTMService.refundPaytmUpiQRWrapper(paymentRefundRequest);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public AGSPaymentS2SResponse updateAGSPaymentS2SStatus(AGSPaymentS2SStatus status)
            throws PaymentFailureException {
        return agsPaymentService.updateAGSPaymentS2SStatus(status);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public AGSPaymentCMResponse checkAGSPaymentS2SStatus(String externalOrderId)
            throws PaymentFailureException {
        return agsPaymentService.checkAGSPaymentS2SStatus(externalOrderId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void sendStandaloneNotification(String paymentId, String event, BasicTransactionInfo info) {
        StandaloneTransactionDetail transaction = paymentGatewayDao.getStandAloneTransaction(paymentId);
        String smsType = null;
        String emailType = null;
        if (transaction != null) {
            paymentId = transaction.getPaymentId();
            if (event.equals("payment.failed")) {
                String failureMessage = String.format(
                        "COVID19_FAILURE : Call Back Request\n" + "Name : %s\n" + "Contact Number : %s\n"
                                + "Email Id : %s\n" + "Amount : %s\n" + "Error Code : %s\n"
                                + "Error Description : %s\n",
                        transaction.getPaymentCustomerName(), transaction.getPaymentContactNumber(),
                        transaction.getPaymentEmailId(), transaction.getPaymentAmount().intValue() / 100,
                        transaction.getPaymentErrorCode(), transaction.getPaymentErrorDescription());
                SlackNotificationService.getInstance().sendNotification(props.getEnvironmentType(), AppConstants.KETTLE,
                        SlackNotification.STANDALONE_TRANSACTION_FAILURE, failureMessage);
                LOG.info("Checking for info.isSmsFailureNotification() " + info.isSmsFailureNotification());

                if (info.isSmsFailureNotification()) {
                    smsType = StandaloneTransactionReceipt.COVID19_FAILURE;
                    LOG.info("Sending SMS  " + smsType + " to user " + transaction.getPaymentCustomerName() + " and contact " + transaction.getPaymentContactNumber());
                    sendStandaloneFailureNotification(true, transaction.getPaymentCustomerName(),
                            transaction.getPaymentContactNumber());
                }
                if (info.isEmailFailureNotification()) {
                    emailType = StandaloneTransactionReceipt.COVID19_FAILURE;
                    StandaloneTransactionReceipt receipt = new StandaloneTransactionReceipt(transaction,
                            props.getEnvironmentType(), props.getBasePath(),
                            StandaloneTransactionReceipt.COVID19_FAILURE, "<EMAIL>");
                    StandaloneTransactionNotification notification = new StandaloneTransactionNotification(receipt);
                    try {
                        notification.sendEmail();
                    } catch (EmailGenerationException e) {
                        LOG.error("Error In Sending Standalone Success Transaction Email", e);
                        new ErrorNotification("Error In Sending Standalone Success Transaction Email", e.getMessage(),
                                e, props.getEnvironmentType()).sendEmail();
                    }

                }

            } else if (event.equals("order.paid")) {
                if (info.isSmsSuccessNotification()) {
                    LOG.info("Skipping SMS Notification of successul transaction");
                }
                if (info.isEmailSuccessNotification()) {
                    emailType = StandaloneTransactionReceipt.COVID19_SUCCESS;
                    StandaloneTransactionReceipt receipt = new StandaloneTransactionReceipt(transaction,
                            props.getEnvironmentType(), props.getBasePath(),
                            StandaloneTransactionReceipt.COVID19_SUCCESS, "<EMAIL>");
                    StandaloneTransactionNotification notification = new StandaloneTransactionNotification(receipt);
                    try {
                        notification.sendEmail();
                    } catch (EmailGenerationException e) {
                        new ErrorNotification("Error In Sending Standalone Success Transaction Email", e.getMessage(),
                                e, props.getEnvironmentType()).sendEmail();
                    }
                }

            }

        }
        if (smsType != null || emailType != null) {
            paymentGatewayDao.setNotificationDetail(paymentId, smsType, emailType);
        }

    }

    private boolean sendStandaloneFailureNotification(boolean sendSms, String customerName, String contactNumber) {
        if (contactNumber.startsWith("+91") && contactNumber.substring(3).length() == 10) {
            contactNumber = contactNumber.substring(3);
        } else {
            LOG.error("Could not send COVID_19_FAILURE_NOTIFICATION to customer " + contactNumber + " and name "
                    + customerName);
            return false;
        }
        try {
            String message = CustomerSMSNotificationType.COVID_19_FAILURE_NOTIFICATION.getMessage(customerName);
            boolean status = notificationService.sendNotification(
                    CustomerSMSNotificationType.COVID_19_FAILURE_NOTIFICATION.name(), message, contactNumber,
                    providerService.getSMSClient(
                            CustomerSMSNotificationType.COVID_19_FAILURE_NOTIFICATION.getTemplate().getSMSType(),
                            ApplicationName.KETTLE_SERVICE),
                    sendSms,null);
            return status;
        } catch (IOException | JMSException e) {
            LOG.error("Error while sending message to the  COVID_19_FAILURE_NOTIFICATION campaign contact number" + contactNumber, e);
        }
        return false;
    }

    public void refundNotification(String contactNumber, OrderPayment refundEmailInfo ) {
        try {
            // Create RefundDataSmsToken object and set transactionId
            RefundDataSmsToken smsToken = new RefundDataSmsToken();
            smsToken.setCustomerName(refundEmailInfo.getCustomerName());
            smsToken.setRefundAmount(refundEmailInfo.getTransactionAmount().toString());
            smsToken.setRefundId(refundEmailInfo.getRefundId());
            
            String message = CustomerSMSNotificationType.REFUND_NOTIFICATION.getMessage(smsToken);
            notificationService.sendNotification(CustomerSMSNotificationType.REFUND_NOTIFICATION.name(),
                    message, contactNumber,
                    providerService.getSMSClient(
                            CustomerSMSNotificationType.REFUND_NOTIFICATION.getTemplate().getSMSType(),
                            ApplicationName.KETTLE_SERVICE),
                    contactNumber != null, null);
            LOG.info("Refund notification sent successfully for orderId {} and refundId {}",refundEmailInfo.getOrderId(),refundEmailInfo.getRefundId());
        } catch (IOException | JMSException e) {
            LOG.error("Error while sending the Refund message to {} for orderId {} ", contactNumber,
                    refundEmailInfo.getOrderId(), e);
        }
    }

    @Override
    public PaytmParamResponse initiatePaytmPayment(PaytmParams paytmParams) throws Exception {
        return payTMNewPaymentService.initiatePaytmPayment(paytmParams);
    }

    @Override
    public PaytmOauth getPaytmOauth(PaytmOauthRequest paytmOauthRequest) throws Exception {
        return payTMNewPaymentService.getPaytmOauth(paytmOauthRequest);
    }

    @Override
    public PaytmOauthResponse getPaytmOauth2V3(PaytmOauthRequest paytmOauthRequest) {
        return payTMNewPaymentService.getPaytmOauth2V3(paytmOauthRequest);
    }

    @Override
    public boolean revokePaytmToken(PaytmOauthRequest paytmOauthRequest) {
        return payTMNewPaymentService.revokePaytmToken(paytmOauthRequest);
    }

    @Override
    public PaytmValidateSSOTokenResponse validatePaytmSSOToken(String ssoToken) {
        return payTMNewPaymentService.validatePaytmSSOToken(ssoToken);
    }

    @Override
    public String getPaytmAccessToken(PaytmFetchPaymentOptionsRequest request) throws Exception {
        return payTMNewPaymentService.getPaytmAccessToken(request);
    }

    @Override
    public String fetchPaytmPaymentOptions(PaytmFetchPaymentOptionsRequest request) {
        return payTMNewPaymentService.fetchPaytmPaymentOptions(request);
    }

    @Override
    public String fetchPaytmPaymentOptionsV2(PaytmFetchPaymentOptionsRequest request) {
        return payTMNewPaymentService.fetchPaytmPaymentOptionsV2(request);
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void updateRefundInitiatedPayment(Object payment) throws PaymentFailureException {
        razorPayPaymentService.updateRefundInitiatedRazorPayPayment((RazorPayPaymentResponse) payment);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public OrderPaymentDetail getOrderPaymentDetailStatus(String externalOrderId){
        if (StringUtils.isBlank(externalOrderId)) {
            LOG.info("external order di is null");
        }
        OrderPaymentDetail orderPaymentDetail = paymentGatewayDao.getOrderPaymentDetailByExternalOrderId(externalOrderId);
        return orderPaymentDetail;
    }
}