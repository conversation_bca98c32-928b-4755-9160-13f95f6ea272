/*
` * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 *
 */
package com.stpl.tech.kettle.customer.dao.impl;

import com.stpl.tech.kettle.core.CampaignOfferDetail;
import com.stpl.tech.kettle.core.LoyaltyEventType;
import com.stpl.tech.kettle.core.NamedQueryDefinition;
import com.stpl.tech.kettle.core.TransactionConstants;
import com.stpl.tech.kettle.core.data.vo.CustomerResponse;
import com.stpl.tech.kettle.core.data.vo.CustomerTransactionData;
import com.stpl.tech.kettle.core.data.vo.OrderFetchStrategy;
import com.stpl.tech.kettle.customer.dao.CustomerDao;
import com.stpl.tech.kettle.customer.dao.LoyaltyDao;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.converter.DataConverter;
import com.stpl.tech.kettle.data.dao.impl.AbstractDaoImpl;
import com.stpl.tech.kettle.data.model.CashData;
import com.stpl.tech.kettle.data.model.CrmAppScreenDetail;
import com.stpl.tech.kettle.data.model.CustomerAddressInfo;
import com.stpl.tech.kettle.data.model.CustomerCampaignOfferDetail;
import com.stpl.tech.kettle.data.model.CustomerContactInfoMapping;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.CustomerOfferDetail;
import com.stpl.tech.kettle.data.model.CustomerTransactionDetail;
import com.stpl.tech.kettle.data.model.FeedbackDetail;
import com.stpl.tech.kettle.data.model.LoyaltyEvents;
import com.stpl.tech.kettle.data.model.LoyaltyScore;
import com.stpl.tech.kettle.data.model.MigrationLookupData;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.data.model.OrderEmailNotification;
import com.stpl.tech.kettle.data.model.ReferralMappingData;
import com.stpl.tech.kettle.data.model.ReferralStatus;
import com.stpl.tech.kettle.data.model.UnitTableMappingDetail;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.CustomerDineInView;
import com.stpl.tech.kettle.domain.model.CustomerEmailData;
import com.stpl.tech.kettle.domain.model.CustomerOffer;
import com.stpl.tech.kettle.domain.model.CustomerOneViewData;
import com.stpl.tech.kettle.domain.model.CustomerTransactionViewData;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.domain.model.SignupOfferStatus;
import com.stpl.tech.kettle.domain.model.truecaller.TrueCallerVerifiedProfile;
import com.stpl.tech.kettle.referral.dao.CashManagerDao;
import com.stpl.tech.kettle.referral.dao.ReferralDao;
import com.stpl.tech.kettle.referral.model.ReferralRequest;
import com.stpl.tech.master.OfferLastRedemptionView;
import com.stpl.tech.master.core.data.vo.NotificationPayload;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.RecipeCache;
import com.stpl.tech.master.core.external.notification.publisher.CustomerCommunicationEventPublisher;
import com.stpl.tech.master.data.model.BrandAttributes;
import com.stpl.tech.master.domain.model.Address;
import com.stpl.tech.master.domain.model.Brand;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;
import org.slf4j.Logger;
import org.apache.commons.lang.StringUtils;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.persistence.FlushModeType;
import javax.persistence.NoResultException;
import javax.persistence.NonUniqueResultException;
import javax.persistence.Query;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Repository
public class CustomerDaoImpl extends AbstractDaoImpl implements CustomerDao {

    private static final Logger LOG = LoggerFactory.getLogger(CustomerDaoImpl.class);

    @Autowired
    private LoyaltyDao loyaltyDao;

    @Autowired
    private MasterDataCache cache;

    @Autowired
    private RecipeCache recipeCache;

    @Autowired
    private EnvironmentProperties props;

    @Autowired
    private ReferralDao referralDao;

    @Autowired
    private CashManagerDao cashManager;

    @Autowired
    private CustomerCommunicationEventPublisher customerCommunicationEventPublisher;

    @Autowired
    private EnvironmentProperties environment;


    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.kettle.data.dao.CustomerDao#getCustomer(int)
     */
    public Customer getCustomer(int customerId) throws DataNotFoundException {

        try {
            CustomerInfo customer = manager.find(CustomerInfo.class, customerId);
            return DataConverter.convert(customer, getLoyaltyScore(customerId), getAvailableCash(customerId));
        } catch (NoResultException e) {
            throw new DataNotFoundException(String.format("Did not find Customer with ID : %d", customerId), e);
        }
    }

    @Override
    public CustomerTransactionDetail getCustomerTransactionDetail(int customerId, int brandId) {
        CustomerTransactionDetail customerTransactionDetail;
        try {
            Query query = manager.createQuery("FROM CustomerTransactionDetail c WHERE c.customerId = :customerId AND c.brandId = :brandId");
            query.setParameter("customerId", customerId);
            query.setParameter("brandId", brandId);
            customerTransactionDetail = (CustomerTransactionDetail) query.getSingleResult();
        } catch (NoResultException | NonUniqueResultException e) {
            customerTransactionDetail = null;
        }
        return customerTransactionDetail;
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.kettle.data.dao.CustomerDao#getCustomer(java.lang.String)
     */
    public Customer getCustomer(String contactNumber) {
        return getCustomer(AppConstants.DEFAULT_COUNTRY_CODE, contactNumber);
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.kettle.data.dao.CustomerDao#getCustomer(java.lang.String,
     * java.lang.String)
     */
    public Customer getCustomer(String code, String contactNumber) {
        CustomerInfo customer = getCustomerInfo(code, contactNumber);
        LoyaltyScore score = null;
        BigDecimal cash = null;
        if (customer != null) {
            score = getLoyaltyScore(customer.getCustomerId());
            cash = getAvailableCash(customer.getCustomerId());
        }

        return DataConverter.convert(customer, score, cash);

    }

    public boolean isCustomerOld(String contactNumber, int beforeYear) {
        LOG.info("Enter isCustomerOld:: contactNumber" + contactNumber);
        Query query = manager.createNativeQuery("SELECT od.BILLING_SERVER_TIME FROM ORDER_DETAIL od " +
                "INNER JOIN CUSTOMER_INFO ci ON od.CUSTOMER_ID=ci.CUSTOMER_ID " +
                "WHERE ci.CONTACT_NUMBER= :contactNumber AND od.ORDER_SOURCE = :orderSource " +
                "ORDER BY od.ORDER_ID DESC LIMIT 1");
        query.setParameter("contactNumber", contactNumber);
        query.setParameter("orderSource", "CAFE");
        LOG.info("isCustomerOld:: " + AppUtils.isNonEmptyList(query.getResultList()));
        if (AppUtils.isNonEmptyList(query.getResultList())) {
            Date lastOrderDate = (Date) query.getSingleResult();
            return (AppUtils.getYear(lastOrderDate) < beforeYear) ? true : false;
        }
        return false;
    }

    public String getCountryCode(String code) {
        return (code == null ? AppConstants.DEFAULT_COUNTRY_CODE : code);
    }

    private CustomerInfo getCustomerInfo(String code, String contactNumber) {
        String countryCode = getCountryCode(code);
        try {
            LOG.info(String.format("Looking up for customer with contact number %s", contactNumber));
            Query query = manager.createQuery(
                    "FROM CustomerInfo E where E.contactNumber = :contactNumber");
            query.setParameter("contactNumber", contactNumber);
//            query.setParameter("countryCode", countryCode);
            Object result = query.getSingleResult();
            if (result != null) {
                return (CustomerInfo)result ;
            } else {
                LOG.error(String.format("Did not find Customer with Contact Number : %s",
                        contactNumber));
                return null;
            }
        } catch (NoResultException e) {
            LOG.error(String.format("Did not find Customer with Contact Number : %s",
                    contactNumber));
            return null;
        }
    }

    @Override
    public Integer getCustomerId(String contactNumber) {
        try {
            LOG.info(String.format("Looking up for customer Id with contact number %s", contactNumber));
            Query query = manager.createQuery(
                    "select customerId FROM CustomerInfo E where E.contactNumber = :contactNumber");
            query.setParameter("contactNumber", contactNumber);
//            query.setParameter("countryCode", countryCode);
            Object result = query.getSingleResult();
            if (result != null) {
                return (Integer)result ;
            } else {
                LOG.error(String.format("Did not find Customer Id with Contact Number : %s",
                        contactNumber));
                return null;
            }
        } catch (NoResultException e) {
            LOG.error(String.format("Did not find Customer Id with Contact Number : %s",
                    contactNumber));
            return null;
        }
    }
    
    @Override
    public LoyaltyScore getLoyaltyScore(int customerId) {
        if (customerId > 5) {
            try {
                //LOG.info(String.format("Looking up for customer loyalty score with with customer id %d", customerId));
                Query query = manager.createQuery("FROM LoyaltyScore E where E.customerId = :customerId");
                query.setParameter("customerId", customerId);
                return (LoyaltyScore) query.getSingleResult();
            } catch (NoResultException e) {
                LOG.info(String.format("Did not find Customer Loyalty Score with customer ID : %d", customerId));
                return null;
            }
        }
        return null;
    }

    @Override
    public Customer addCustomer(Customer customer) throws DataUpdationException {
        return addCustomer(customer, true);
    }

    @Override
    public Customer addCustomerUnchecked(Customer customer) throws DataUpdationException {
        LOG.info("CUSTOMER BRANCH CHECK INTERNAL : {} {}" , customer.getAcquisitionBrandId() , customer.isChaayosCustomer());
        return addCustomer(customer, false);
    }

    public Customer addCustomer(Customer customer, boolean check) throws DataUpdationException {

        String countryCode = customer.getCountryCode();
        String contactNumber = customer.getContactNumber();
        Customer cust = null;
        if (check) {
            cust = getCustomer(countryCode, contactNumber);
        }

        if (cust == null) {
            CustomerInfo info = createCustomer(customer);
			try {
				if (StringUtils.isNotBlank(info.getOptWhatsapp())
						&& info.getOptWhatsapp().equalsIgnoreCase(AppConstants.YES)) {

					customerCommunicationEventPublisher.publishCustomerCommunicationEvent(
							environment.getEnvironmentType().name(), getNotificationPayload(info));
				}
			} catch (Exception e) {
				LOG.info("Exception during subscribing customer to whatsapp");
			}
            LoyaltyScore score = new LoyaltyScore(info.getCustomerId(), 0);
            if(AppConstants.YES.equals(info.getIsRenewed())){
                score.setAvailedSignupOffer(AppConstants.YES);
                score.setSignupOfferStatus(SignupOfferStatus.RENEW_CUSTOMER_FORCE_EXPIRED.name());
                score.setSignupOfferExpired(AppConstants.YES);
                score.setSignupOfferExpiryTime(AppUtils.getCurrentTimestamp());
            }
            manager.persist(score);
            manager.flush();

            updateReferralData(customer, info);

            cust = DataConverter.convert(info, score, BigDecimal.ZERO);
        }
        return cust;
    }

    private void updateReferralData(Customer customer, CustomerInfo info) {
        // handle reference data
        // this is done in the end as customer id is required to be updated
        // referral code check
        if (!AppUtils.isBlank(customer.getSignUpRefCode())) {
            ReferralMappingData refData = referralDao.searchByCode(customer.getSignUpRefCode(),
                    info.getContactNumber());
            if (refData == null) {

                // create referral
                Customer referrer = getCustomerByRefCode(customer.getSignUpRefCode());
                ReferralRequest request = new ReferralRequest();
                request.setSource(info.getAcquisitionSource());
                request.setCampaign(info.getAcquisitionToken());
                request.setContact(info.getContactNumber());
                request.setName(info.getFirstName());
                request.setReferrerId(referrer.getId());
                request.setSignUpRefCode(customer.getSignUpRefCode());
                refData = createReferralMappingData(request, referrer, null);

            }
            info.setReferralDataId(refData.getReferralDataId());
            info.setReferrerId(refData.getReferrerId());
            info.setReferredOn(refData.getCreationTime());
            info.setReferrerAwarded(AppUtils.NO);
            manager.merge(info);
        }
        // do this irrespective of refCode to update referral status if referral code is
        // not used
        referralDao.updateReferralStatus(info, customer.getSignUpRefCode());

    }


    private ReferralMappingData createReferralMappingData(ReferralRequest request, Customer referrer,
                                                          Customer newCustomer) {

        // if referral already exists
        ReferralMappingData data = referralDao.getReffralMapping(request.getContact(), referrer.getId());

        if (data != null) {
            LOG.info("Referral Already Exists , {}", JSONSerializer.toJSON(data));
            return data;
        }

        // in case we have new referral
        data = new ReferralMappingData();
        data.setCreationTime(AppUtils.getCurrentTimestamp());
        data.setContactNumber(request.getContact());
        data.setReferentName(request.getName());
        data.setCampaignId(request.getCampaign());
        data.setReferralSource(request.getSource());
        // TODO add these if required
        // data.setReferralSourceCategory(null);
        // data.setReferralSourceURL(null);

        if (referrer != null) {
            data.setReferrerId(referrer.getId());
            data.setReferralCode(referrer.getRefCode());
        }

        // other status are updated while adding customer
        if (newCustomer != null) {
            data.setReferentId(newCustomer.getId());
            data.setReferralStatus(ReferralStatus.EXISTING_CUSTOMER.name());
        } else {
            data.setReferralStatus(ReferralStatus.INITIATED.name());
        }

        // TODO URL Parameters and other metadata to be added
        data.setLastUpdateTime(AppUtils.getCurrentTimestamp());
        return (ReferralMappingData) referralDao.update(data);
    }

    private CustomerInfo createCustomer(Customer customer) throws DataUpdationException{
        CustomerInfo info = new CustomerInfo();
        CustomerContactInfoMapping customerContactInfoMapping = getCustomerContactInfoMappingFromOldContact(customer.getContactNumber());
        info.setContactNumber(customer.getContactNumber());
        info.setCountryCode(getCountryCode(customer.getCountryCode()));
        if(AppUtils.checkBlank(customer.getEmailId())!=null){
            if(!cache.getAllUnitsEmailId().contains(customer.getEmailId())){
                info.setEmailId(AppUtils.checkBlank(customer.getEmailId()));
            }
            else{
                throw new DataUpdationException("EmailId - "+customer.getEmailId()+" is not valid for sign up");
            }
        }else{
            info.setEmailId(AppUtils.checkBlank(customer.getEmailId()));
        }
        info.setFirstName(customer.getFirstName());
        info.setMiddleName(customer.getMiddleName());
        info.setLastName(customer.getLastName());
        if(Objects.isNull(customer.getAddTime())) {
            info.setAddTime(AppUtils.getCurrentTimestamp());
        }
        else {
            info.setAddTime(customer.getAddTime());
        }
        info.setRegistrationUnitId(customer.getRegistrationUnitId());
        info.setAcquisitionSource(customer.getAcquisitionSource());
        info.setAcquisitionToken(customer.getAcquisitionToken());
        info.setIsNumberVerified(customer.isContactNumberVerified() ? AppConstants.YES : AppConstants.NO);
        info.setIsEmailVerified(customer.isEmailVerified() ? AppConstants.YES : AppConstants.NO);
        info.setSmsSubscriber(AppUtils.YES);
        info.setEmailSubscriber(AppUtils.YES);
        info.setLoyaltySubscriber(AppUtils.YES);
        info.setOptOutOfFaceIt(AppUtils.NO);
        if (customer.isContactNumberVerified()) {
            info.setNumberVerificationTime(AppUtils.getCurrentTimestamp());
        }
        if (customer.getTrueCallerProfile() != null) {
            TrueCallerVerifiedProfile verifiedProfile = customer.getTrueCallerProfile();
            info.setTrueCallerProfileId(verifiedProfile.getTcId());
        }
        // generate RefCode for customer
        info.setRefCode(getUniqueRefCode(info.getFirstName(), info.getContactNumber()));
        info.setIsRefSubscriber(customer.getIsRefSubscriber() == null ? AppConstants.YES : customer.getIsRefSubscriber());
        info.setReferrerAwarded(AppUtils.NO);
        // changes to accomodate new brand
		info.setAcquisitionBrandId(customer.getAcquisitionBrandId() == null ? AppConstants.CHAAYOS_BRAND_ID
				: customer.getAcquisitionBrandId());
        info.setIsChaayosCustomer(customer.isChaayosCustomer() ? AppConstants.YES : AppConstants.NO);
        if (StringUtils.isNotEmpty(customer.getGender())) {
            info.setGender(customer.getGender());
        }
        if (customer.getDateOfBirth() != null) {
            info.setDateOfBirth(customer.getDateOfBirth());
        }
        if (customer.getAnniversary() != null) {
            info.setAnniversary(customer.getAnniversary());
        }
        if (customer.getOptWhatsapp() != null) {
            info.setOptWhatsapp(customer.getOptWhatsapp());
        }
        if(Objects.nonNull(customerContactInfoMapping)){
            info.setIsRenewed(AppConstants.YES);
        }
        manager.persist(info);
        manager.flush();

        info.getCustomerAddressInfos().addAll(addAddress(info, customer.getAddresses()));
        return info;
    }

    @Override
    public String getUniqueRefCode(String name, String contact) {
        return getUniqueRefCode(name, contact, 10, 7);
    }

    @Override
    public void updateRefCodeInCashData(int customerId, String refCode) {
        cashManager.updateRefCodeInCashData(customerId, refCode);
    }

    private String getUniqueRefCode(String name, String contact, int iterations, int codeLength) {

        if (name == null || AppUtils.isBlank(AppUtils.cleanUp(name))) {
            // no name no game
            return null;
        }

        List<String> codeList = AppUtils.getCodeList(name, contact, iterations, codeLength);
        String code = collectUnusedCode(codeList);

        if (code == null) {
            // recurse on failure
            // congratulation you have a very generic name and contact number
            // *Bell ring* Shame! ... Shame! ... Shame!
            if (iterations == 1000) {
                codeLength = codeLength + 1;
                iterations = 10;
            } else {
                iterations = iterations * 10;
            }
            code = getUniqueRefCode(name, contact, iterations, codeLength);
        }

        return code;
    }

    private String collectUnusedCode(List<String> codeList) {
        String code = null;
        Query query = manager.createQuery("SELECT E.refCode FROM CustomerInfo E where E.refCode IN (:refCodeList)");
        query.setParameter("refCodeList", codeList);

        @SuppressWarnings("unchecked")
        List<String> l = query.getResultList();

        if (l == null || l.isEmpty()) {
            return codeList.get(0);
        } else {
            for (String s : codeList) {
                if (!l.contains(s)) {
                    code = s;
                    break;
                }
            }
        }
        return code;
    }

    private List<CustomerAddressInfo> addAddress(CustomerInfo customer, List<Address> addresses) {
        List<CustomerAddressInfo> objects = new ArrayList<CustomerAddressInfo>();
        if (addresses != null && addresses.size() > 0) {
            for (Address address : addresses) {

                objects.add(addAddress(customer, address));
            }
        }
        return objects;
    }

    private CustomerAddressInfo addAddress(CustomerInfo customer, Address address) {
        CustomerAddressInfo info = new CustomerAddressInfo();

        if(address.getId()!=0)
        {
            info = manager.find(CustomerAddressInfo.class, address.getId());

        }
        setData(customer, info, address);
        manager.persist(info);
        manager.flush();
        return info;
    }

    private void setData(CustomerInfo customer, CustomerAddressInfo info, Address address) {
        info.setLandmark(address.getLandmark());
        info.setAddressLine1(address.getLine1());
        info.setAddressLine2(address.getLine2());
        info.setAddressLine3(address.getLine3());
        info.setAddressType(address.getAddressType());
        info.setSubLocality(address.getSubLocality());
        info.setLocality(address.getLocality());
        info.setCompany(address.getCompany());
        info.setCity(address.getCity());
        info.setCountry(address.getCountry());
        info.setState(address.getState());
        info.setZipcode(address.getZipCode());

        info.setSource(address.getSource());
        info.setSourceId(address.getSourceId());
        info.setStatus(address.getStatus());

        if(address.getLatitude() != null) {
            info.setLatitude(address.getLatitude());
        }
        if(address.getLongitude() != null) {
            info.setLongitude(address.getLongitude());
        }
        info.setCustomerInfo(customer);
        info.setPreferredAddress(address.isPreferredAddress());
        if (address.getName() != null) {
            info.setName(address.getName());
        }
        if (address.getEmail() != null) {
            info.setEmail(address.getEmail());
        }
    }

    /**
     * This would update customer information. Incase the customer has address
     * array, it will update the ones that have addressId set in them. for others it
     * will just add those addresses
     */
    @Override
    public boolean updateCustomer(Customer customer) {
        try {
            if (Objects.nonNull(customer.getId())) {
                CustomerInfo customerInfo = manager.find(CustomerInfo.class, customer.getId());
                customerInfo.setOptWhatsapp(customer.getOptWhatsapp());
                manager.persist(customerInfo);
                manager.flush();
                return true;
            }
        } catch (Exception e){
            LOG.error("Exception Faced While Updateing Whatsapp OptIn Info for Cutomer Id :: {}",customer.getId());
            return false;
        }
        return false;
    }

    @Override
    public boolean updateCustomerAppAction(Customer customer) {
        try {
            if (Objects.nonNull(customer.getId())) {
                CustomerInfo customerInfo = manager.find(CustomerInfo.class, customer.getId());
                customerInfo.setAppAction(customer.getAppAction());
                customerInfo.setAppActionTime(customer.getAppActionTime());
                manager.persist(customerInfo);
                manager.flush();
                return true;
            }
        } catch (Exception e){
            LOG.error("Exception Faced While Updating App Action for Cutomer Id :: {}",customer.getId());
            return false;
        }
        return false;
    }


    @Override
    public boolean updateShopifyCustomer(Customer customer) {
        try {
            if (Objects.nonNull(customer.getId())) {
                CustomerInfo customerInfo = manager.find(CustomerInfo.class, customer.getId());
                List<Address> addresses=customer.getAddresses();
                int totalAddresses=addresses.size();
                for(int addressIndex=0;addressIndex<totalAddresses;addressIndex++)
                {
                    Address address=addresses.get(addressIndex);
                    if(address.getLine1()==null && address.getCity()==null && address.getState()==null)
                    {
                        addresses.remove(address);
                    }
                }
                addAddress(customerInfo,addresses);
                return true;
            }
        } catch (Exception e){
            LOG.error("Exception Faced While Updateing Shopify Customer Info for Cutomer Id :: {}",customer.getId());
            LOG.info(e.toString());
            return false;
        }

        return false;
    }

    @Override
    public boolean updateCustomerEmail(Customer customer,String flag) {
        try {
            if (Objects.nonNull(customer.getId())) {
                CustomerInfo customerInfo = manager.find(CustomerInfo.class, customer.getId());
                customerInfo.setIsEmailVerified(flag);
                manager.persist(customerInfo);
                manager.flush();
                return true;
            }
        } catch (Exception e){
            LOG.error("Exception Faced While Updating Email Verified Flag Info for Cutomer Id :: {}",customer.getId());
            return false;
        }
        return false;
    }

    /**
     * This would update customer information. Incase the customer has address
     * array, it will update the ones that have addressId set in them. for others it
     * will just add those addresses
     */

    public void updateMigrationPoints(CustomerInfo customer, String emailId) {
        if (emailId == null || "".equals(emailId.trim())) {
            return;
        }
        MigrationLookupData data = getMigrationDataByEmail(emailId);
        if (data == null) {
            return;
        }
        loyaltyDao.updateScore(customer.getCustomerId(), LoyaltyEventType.MIGRATION, data.getAcquiredPoints(),
                data.getCumulativePoints(), false, false);

        data.setIsActive("N");
        customer.setRegistrationUnitId(data.getRegistrationUnitId());
        manager.flush();
    }

    private MigrationLookupData getMigrationDataByEmail(String emailId) {
        try {
            LOG.info(String.format("Looking up for migration data for emailId %s", emailId));
            Query query = manager.createQuery(
                    "FROM MigrationLookupData E where E.lookupType = :lookupType and E.lookupText = :lookupText and E.isActive = :isActive");
            query.setParameter("lookupType", "EMAIL");
            query.setParameter("lookupText", emailId);
            query.setParameter("isActive", "Y");
            return (MigrationLookupData) query.getSingleResult();
        } catch (NoResultException e) {
            // LOG.error(String.format("Did not find Customer with Existing
            // email Id : %s", emailId), e);
            return null;
        }
    }

    public int addAddress(int customerId, Address address) throws DataUpdationException {
        try {
            CustomerInfo customer = manager.find(CustomerInfo.class, customerId);
            CustomerAddressInfo info = addAddress(customer, address);
            customer.getCustomerAddressInfos().add(info);
            return info.getAddressId();
        } catch (NoResultException e) {
            throw new DataUpdationException(
                    "Cannot add address for customer with customerId:" + customerId + " as customer does not exist", e);
        }
    }

    public Address addAddress(String contact, Address address) throws DataUpdationException {
        try {
            CustomerInfo customer = getCustomerInfo(null, contact);
            CustomerAddressInfo info = addAddress(customer, address);
            customer.getCustomerAddressInfos().add(info);
            return DataConverter.convert(info);
        } catch (NoResultException e) {
            throw new DataUpdationException(
                    "Cannot add address for customer with contact " + contact + " as customer does not exist", e);
        }
    }

    public void verifyContactNumber(String contactNumber) {
        String countryCode = getCountryCode(null);
        CustomerInfo info = getCustomerInfo(countryCode, contactNumber);
        info.setIsNumberVerified(AppConstants.YES);
        info.setNumberVerificationTime(AppUtils.getCurrentTimestamp());
        /*
         * loyaltyDao.updateScore(info.getCustomerId(),
         * LoyaltyEventType.CONTACT_NUMBER_VERIFICATION,
         * AppConstants.VERIFICATION_LOYALTY_POINT, null);
         */
        manager.flush();
    }

    public void verifyEmailAddress(String contactNumber) {
        String countryCode = getCountryCode(null);
        CustomerInfo info = getCustomerInfo(countryCode, contactNumber);
        info.setIsEmailVerified(AppConstants.YES);
        info.setEmailVerificationTime(AppUtils.getCurrentTimestamp());
        Brand brand = cache.getBrandMetaData().get(info.getAcquisitionBrandId());
        CustomerContactInfoMapping customerContactInfoMapping = getCustomerContactInfoMappingFromOldContact(contactNumber);
        if (customerContactInfoMapping == null && Boolean.TRUE.equals(brand.getAwardLoyalty())) {
            if (!loyaltyDao.isLoyaltyAwarded(info.getCustomerId(), LoyaltyEventType.EMAIL_VERIFICATION)) {
                loyaltyDao.updateScore(info.getCustomerId(), LoyaltyEventType.EMAIL_VERIFICATION,
                        AppConstants.VERIFICATION_LOYALTY_POINT, null, false, false);
            }
        } else {
            info.setIsRenewed(AppConstants.YES);
        }
        manager.flush();
    }

    /**
     * Method to Collect Customer and Customer Transaction Details using contact
     * number
     *
     * @param contactNumber
     * @return CustomerTransactionData
     */
    public CustomerTransactionData getCustomerTransactionInfo(String contactNumber , Customer customer) throws DataNotFoundException {
        CustomerTransactionData data = new CustomerTransactionData();
        LOG.info(String.format("Looking up Order data for customer having Contact Number %s", contactNumber));
        data.setOrders(getOrderListforCustomer(customer));
        data.setCustomer(customer);
        return data;
    }

    /**
     * Method to Collect Customer and Customer Transaction Details using contact
     * number
     *
     * @param contactNumber
     * @return CustomerTransactionData
     */
    @Override
    public Customer viewCustomer(String contactNumber) throws DataNotFoundException {
        return getCustomer(contactNumber);
    }

    @SuppressWarnings("unchecked")
    private List<Order> getOrderListforCustomer(Customer customer) throws DataNotFoundException {
        List<Order> orderList = null;
        if (customer != null) {
            try {
                orderList = new ArrayList<Order>();
                Query query = manager.createQuery("FROM OrderDetail OD WHERE OD.customerId = :customerId"
                        + " ORDER BY OD.billGenerationTime DESC", OrderDetail.class);
                query.setParameter("customerId", customer.getId());
                query.setMaxResults(10);
                OrderFetchStrategy strategy = new OrderFetchStrategy(false, false, false, false, false, null, -1, false);
                for (OrderDetail orderDetail : (List<OrderDetail>) query.getResultList()) {
                    orderList.add(DataConverter.convert(cache, orderDetail, strategy,recipeCache,props));
                }
            } catch (NoResultException e) {
                LOG.error(String.format("Did not find any Order Details for Customer with mobile number : %s",
                        customer.getContactNumber()));
            }
        }
        return orderList;
    }

    public CustomerInfo getCustomerByDeliveryAddress(int deliveryAddressId) {
        Query query = manager.createQuery("FROM CustomerAddressInfo CAI WHERE CAI.addressId = :deliveryAddressId",
                CustomerAddressInfo.class);
        query.setParameter("deliveryAddressId", deliveryAddressId);
        CustomerAddressInfo cai = (CustomerAddressInfo) query.getSingleResult();
        return cai.getCustomerInfo();
    }

    public List<CustomerOffer> getOfferDetail(int customerId, String offerCode) {
        List<CustomerOffer> customerOfferList = new ArrayList<>();
        try {
            LOG.info(String.format("Looking up for Customer Offer customerId %d data for offerCode %s", customerId,
                    offerCode));
            Query query = manager.createQuery(
                    "FROM CustomerOfferDetail E where E.customerId = :customerId and E.offerCode = :offerCode and E.availed = :availed");
            query.setParameter("customerId", customerId);
            query.setParameter("offerCode", offerCode);
            query.setParameter("availed", "Y");
            @SuppressWarnings("unchecked")
            List<CustomerOfferDetail> list = query.getResultList();
            if (list != null) {
                for (CustomerOfferDetail detail : list) {
                    customerOfferList.add(DataConverter.convert(detail));
                }
            }
        } catch (NoResultException e) {
            LOG.error(String.format("Did not find any offer with code %s for customer with productId %d", offerCode,
                    customerId));
        }
        return customerOfferList;
    }

    public void addOfferDetail(int customerId, String offerCode, int orderId) {
        CustomerOfferDetail detail = new CustomerOfferDetail();
        detail.setAvailed(AppConstants.YES);
        detail.setAvailTime(AppUtils.getCurrentTimestamp());
        detail.setCustomerId(customerId);
        detail.setOfferCode(offerCode);
        detail.setOrderId(orderId);
        manager.persist(detail);
        manager.flush();
    }

    @Override
    public void overrideContactVerificationStatus(String contactNumber, boolean contactVerified) {
        Query query = manager.createQuery(
                "UPDATE CustomerInfo SET isNumberVerified = :verificationStatus WHERE contactNumber = :contactNumber");
        query.setParameter("contactNumber", contactNumber);
        query.setParameter("verificationStatus", AppConstants.getValue(contactVerified));
        query.executeUpdate();
    }

    @Override
    public List<CustomerInfo> getCustomersWithPendingLoyalty(Date startDate, Date endDate) {
        LOG.info("Finding Loyalty Reminder Customers between {} and {}", startDate, endDate);
        List<CustomerInfo> customers = new ArrayList<>();
        Query query = manager.createQuery(
                "select customerId from LoyaltyScore where orderCount = :orderCount and availedSignupOffer = :availedSignupOffer and signupOfferExpired is null and lastOrderTime > :startDate and lastOrderTime <= :endDate");
        query.setParameter("orderCount", 1);
        query.setParameter("availedSignupOffer", AppConstants.NO);
        query.setParameter("startDate", startDate);
        query.setParameter("endDate", endDate);
        List<Integer> customerIds = query.getResultList();
        if (customerIds != null && customerIds.size() > 0) {
            customers = getCustomers(customerIds);
        }
        return customers;
    }

    @Override
    public boolean eligibleForSignupOffer(int customerId) {
        List<CustomerOffer> list = getOfferDetail(customerId, TransactionConstants.SIGNUP_OFFER_CODE);
        return list == null || list.size() == 0;
    }

    @Override
    public List<CustomerInfo> getNewCustomers(String firstOrderSource, Date startDate, Date endDate) {
        LOG.info("Finding New Customers between {} and {} for order source {}", startDate, endDate, firstOrderSource);
        List<CustomerInfo> customers = new ArrayList<>();
        Query query = manager.createQuery(
                "select ls.customerId from LoyaltyScore ls, OrderDetail od where ls.orderCount = :orderCount and ls.lastOrderTime > :startDate and ls.lastOrderTime <= :endDate and ls.lastOrderId = od.orderId and od.orderSource = :orderSource");
        query.setParameter("orderCount", 1);
        query.setParameter("startDate", startDate);
        query.setParameter("endDate", endDate);
        query.setParameter("orderSource", firstOrderSource);
        List<Integer> customerIds = query.getResultList();
        if (customerIds != null && customerIds.size() > 0) {
            customers = getCustomers(customerIds);
        }
        return customers;
    }

    private List<CustomerInfo> getCustomers(List<Integer> customerIds) {
        Query customerQuery = manager.createQuery("from CustomerInfo where customerId in (:customerIds)");
        customerQuery.setParameter("customerIds", customerIds);
        return customerQuery.getResultList();
    }

    @Override
    public List<Address> getNewAddress(int id, List<Integer> addressIds) {
        List<Address> newAddresses = new ArrayList<>();
        Query addressQuery = null;
        if (addressIds != null && !addressIds.isEmpty()) {
            addressQuery = manager.createQuery(
                    "from CustomerAddressInfo where addressId not in (:addressIds) and customerInfo.customerId = :customerId");
            addressQuery.setParameter("addressIds", addressIds);
            addressQuery.setParameter("customerId", id);
        } else {
            addressQuery = manager.createQuery("from CustomerAddressInfo where customerInfo.customerId = :customerId");
            addressQuery.setParameter("customerId", id);
        }
        if (addressQuery != null) {
            List<CustomerAddressInfo> addresses = addressQuery.getResultList();
            if (addresses != null && addresses.size() > 0) {
                for (CustomerAddressInfo address : addresses) {
                    newAddresses.add(DataConverter.convert(address));
                }

            }
        }
        return newAddresses;
    }

    @Override
    public boolean removeInvalidEmails(List<String> invalidEmails) throws DataUpdationException {
        boolean flag = false;
        try {
            Query query = manager.createQuery("UPDATE CustomerInfo SET emailId = :nullId, isEmailVerified = :nullId"
                    + " WHERE emailId IN (:emailId)");
            query.setParameter("nullId", null);
            query.setParameter("emailId", invalidEmails);
            query.executeUpdate();
            flag = true;
        } catch (Exception e) {
            flag = false;
        }

        if (flag) {
            expireBouncedEmailEvents(invalidEmails);
        }
        return flag;
    }

    private List<OrderEmailNotification> getEmailEvents(List<String> invalidEmails) {
        Query query = manager
                .createNativeQuery("select * from ORDER_EMAIL_NOTIFICATION where IS_EMAIL_DELIVERED = :isEmailDeivered "
                        + "AND RETRY_COUNT <= :retryCount AND EXECUTION_TIME IS NULL "
                        + "AND EMAIL_ADDRESS IN (:emails)", OrderEmailNotification.class)
                .setFlushMode(FlushModeType.COMMIT);
        query.setParameter("isEmailDeivered", "N");
        query.setParameter("retryCount", props.getRetryCount());
        query.setParameter("emails", invalidEmails);
        return query.getResultList();
    }

    private void expireBouncedEmailEvents(List<String> invalidEmails) {
        List<OrderEmailNotification> emailEvents = getEmailEvents(invalidEmails);
        for (OrderEmailNotification event : emailEvents) {
            expireEvent(event);
        }
    }

    private void expireEvent(OrderEmailNotification event) {
        event.setIsEmailDelivered(AppConstants.getValue(false));
        event.setIsEmailVerified(AppConstants.getValue(false));
        event.setExecutionTime(AppUtils.getCurrentTimestamp());
        event.setErrorMessage("Expired due to EMAIL_BOUNCE");
        manager.flush();
    }

    @Override
    public void updateLastNPSTime(Date updateTime, int customerId) {
        try{
        Query query = manager
                .createQuery("UPDATE LoyaltyScore E SET E.lastNPSTime = :updateTime where E.customerId = :customerId");
        query.setParameter("customerId", customerId);
        query.setParameter("updateTime", updateTime);
        query.executeUpdate();
        }catch (Exception e){
            LOG.error("Error while updating last nps time for customer with id : {}", customerId,e );
        }
    }

    @Override
    public void markAsInternalCustomers(List<String> contactNumbers) {
        Query query = manager.createQuery(
                "UPDATE CustomerInfo E SET E.isInternal = :internal where E.contactNumber IN (:contactNumbers) AND E.isInternal = :notInternal");
        query.setParameter("internal", AppConstants.YES);
        query.setParameter("notInternal", AppConstants.NO);
        query.setParameter("contactNumbers", contactNumbers);
        query.executeUpdate();

        Query query1 = manager.createQuery(
                "UPDATE CustomerInfo E SET E.isInternal = :notInternal where E.contactNumber NOT IN (:contactNumbers) AND E.isInternal = :internal");
        query1.setParameter("internal", AppConstants.YES);
        query1.setParameter("notInternal", AppConstants.NO);
        query1.setParameter("contactNumbers", contactNumbers);
        query1.executeUpdate();

    }

    @Override
    public boolean existsContactNumber(String number) {
        return getCustomerInfo(getCountryCode(null), number) != null;
    }

    @Override
    public BigDecimal getAvailableCash(Integer customerId) {
        CashData cash = cashManager.getCustomerCashData(customerId);
        if (cash == null) {
            return BigDecimal.ZERO;
            /*
             * try { cash = cashManager.createCashData(getCustomer(customerId)); } catch
             * (DataNotFoundException | DataUpdationException e) {
             * LOG.error("Error while getting available Cash", e); return null; }
             */
        }
        return cash.getCurrentAmount();
    }

    @Override
    public BigDecimal getAvailableCashback(Integer customerId) {
        return cashManager.getAvailableCashback(customerId);
    }

    @Override
    public Customer getCustomerByRefCode(String refCode) {
        CustomerInfo c = null;
        try {
            Query query = manager.createQuery("FROM CustomerInfo E where E.refCode = :refCode");
            query.setParameter("refCode", refCode);
            c = (CustomerInfo) query.getSingleResult();
        } catch (NoResultException nre) {
            LOG.info("No Customer with ref code {}", refCode);
        }
        return DataConverter.convert(c, null, BigDecimal.ZERO);

    }

    @SuppressWarnings("unchecked")
    @Override
    public List<Integer> getCustomersWithoutRefCode() {
        Query query = manager.createQuery(
                "SELECT customerId FROM CustomerInfo E where E.refCode IS NULL AND E.firstName IS NOT NULL AND LENGTH(TRIM(E.firstName)) > 0");
        query.setMaxResults(1000);
        return query.getResultList();
    }

    @Override
    public CustomerInfo getCustomerInfo(String contact) {
        try {
            Query query = manager.createQuery("FROM CustomerInfo E where E.contactNumber = :contactNumber");
            query.setParameter("contactNumber", contact);
            return (CustomerInfo) query.getSingleResult();
        } catch (NoResultException nre) {
            LOG.info("No Customer with contact number {}", contact);
        }
        return null;
    }

    @Override
    public List<CustomerInfo> getCustomerWithReferralCode(List<String> contacts) {
        try {
            Query query = manager.createQuery(
                    "FROM CustomerInfo E where E.contactNumber IN (:contacts) and refCode is not null and isNumberVerified = :verified");
            query.setParameter("contacts", contacts);
            query.setParameter("verified", AppConstants.YES);
            return query.getResultList();
        } catch (NoResultException nre) {
            LOG.info("Error in fetching customers with contact list", nre);
        }
        return null;

    }

    @Override
    public Customer getCustomerByFaceId(String faceId) {
        try {
            Query query = manager.createQuery("FROM CustomerInfo E where E.faceId = :faceId");
            query.setParameter("faceId", faceId);
            CustomerInfo customer = (CustomerInfo) query.getSingleResult();
            return DataConverter.convert(customer, getLoyaltyScore(customer.getCustomerId()),
                    getAvailableCash(customer.getCustomerId()));
        } catch (NoResultException e) {
            LOG.info("ERROR: no customer with faceId", faceId);
            return null;
        }
    }

    @Override
    public String optOutOfFaceIt(String contactNumber,boolean flag) {
        CustomerInfo customerInfo= getCustomerInfo(contactNumber);
        if(flag){
            return removeFaceMetaData(customerInfo);
        }else {
            return optOutOfFaceIt(customerInfo);
        }
    }

    private String removeFaceMetaData(CustomerInfo info) {
        String faceId = null;
        if (info != null && !AppUtils.getStatus(info.getOptOutOfFaceIt())) {
            info.setOptOutOfFaceIt(AppConstants.NO);
            //info.setOptOutTime(AppUtils.getCurrentTimestamp());
            faceId = info.getFaceId();
            info.setFaceId(null);
            manager.flush();
        }
        return faceId;
    }

    @Override
    public String optOutOfFaceIt(int customerId) {
        return optOutOfFaceIt(manager.find(CustomerInfo.class, customerId));
    }

    @Override
    public void updateIsChaayosCustomer(int customerId, String chaayosCustomer) {

        Query query = manager.createQuery("UPDATE CustomerInfo c SET c.isChaayosCustomer = :chaayosCustomer "
                + " WHERE c.customerId = :customerId");
        query.setParameter("chaayosCustomer", chaayosCustomer);
        query.setParameter("customerId", customerId);
        query.executeUpdate();

        return;
    }

    @Override
    public OrderDetail getOrderDetail(int orderId) {
        return manager.find(OrderDetail.class, orderId);
    }

    @Override
    public String getCustomerRefCode(int customerId) {
        Query query = manager.createQuery("SELECT c.refCode FROM CustomerInfo c where c.customerId = :customerId ");
        query.setParameter("customerId", customerId);
        try {
            return (String) query.getSingleResult();
        } catch (NoResultException e) {
            LOG.info("Error in fetching customers refCode with customerId", customerId, e);
        }
        return null;
    }

    private String optOutOfFaceIt(CustomerInfo info) {
        String faceId = null;
        if (info != null && !AppUtils.getStatus(info.getOptOutOfFaceIt())) {
            info.setOptOutOfFaceIt(AppConstants.YES);
            info.setOptOutTime(AppUtils.getCurrentTimestamp());
            faceId = info.getFaceId();
            info.setFaceId(null);
            manager.flush();
        }
        return faceId;
    }

    @Override
    public boolean mapCustomerByFaceId(String contact, String faceId) {
        CustomerInfo customer = null;
        try {
            Query query = manager.createQuery("FROM CustomerInfo E where E.contactNumber = :contact");
            query.setParameter("contact", contact);
            customer = (CustomerInfo) query.getSingleResult();
        } catch (NoResultException nre) {
            LOG.info("Error in fetching customers with contact list", nre);
            return false;
        }
        if (customer != null) {
            customer.setFaceId(faceId);
            manager.merge(customer);
            return true;
        }
        return false;
    }

    @Override
    public List<Integer> getCustomerIds(int customerId, int batchSize) {
        Query query = manager.createQuery("SELECT E.customerId FROM CustomerInfo E where E.customerId>:customerId");
        query.setParameter("customerId", customerId);
        query.setMaxResults(batchSize);
        return query.getResultList();
    }

    @Override
    public List<Integer> getCustomerIdsWithPendingSignupOffer(int noOfDays) {
        Query query = manager.createQuery("SELECT E.customerId FROM CustomerInfo E, LoyaltyScore L where E.customerId = L.customerId "
                + "and E.isNumberVerified = :yes and (L.availedSignupOffer = :no or L.availedSignupOffer is null) "
                + "and L.lastOrderTime <= :checkPoint");
        query.setParameter("yes", AppConstants.YES);
        query.setParameter("no", AppConstants.NO);
        query.setParameter("checkPoint", AppUtils.getDayBeforeOrAfterCurrentDay(-1 * noOfDays));
        return query.getResultList();
    }

    @Override
    public void expireSignupOffer(List<Integer> customerIds) {
        Query query = manager.createQuery("update LoyaltyScore L "
                + " set L.availedSignupOffer = :yes , L.signupOfferExpired = :yes, L.signupOfferExpiryTime = :currentTime, " +
                "L.signupOfferStatus = :status where L.customerId IN (:customerIds)"
                + " and L.signupOfferExpired is null and ( L.availedSignupOffer is null or L.availedSignupOffer = :no)");
        query.setParameter("yes", AppConstants.YES);
        query.setParameter("no", AppConstants.NO);
        query.setParameter("customerIds", customerIds);
        query.setParameter("currentTime", AppUtils.getCurrentTimestamp());
        query.setParameter("status", SignupOfferStatus.EXPIRED.name());
        query.executeUpdate();

    }

    @Override
    public void expireSignupOffer(int noOfDays) {
        List<Integer> customerIds = getCustomerIdsWithPendingSignupOffer(noOfDays);
        if (customerIds == null || customerIds.size() == 0) {
            LOG.info("No customer found for sign up offer expiration");
            return;
        }
        LOG.info("Expired {} customers signup offer", customerIds.size());
        LOG.info("Expired {} customers signup offer", customerIds);
        expireSignupOffer(customerIds);
    }

    @Override
    public List<LoyaltyScore> getCustomersLoyaltyScore(List<Integer> customerIds) {
        if (AppUtils.isNonEmptyList(customerIds)) {
            try {
                LOG.info("Looking up for customers loyalty score");
                Query query = manager.createQuery("FROM LoyaltyScore E where E.customerId IN (:customerIds)");
                query.setParameter("customerIds", customerIds);
                return query.getResultList();
            } catch (NoResultException e) {
                LOG.info("In getCustomersLoyaltyScore() Did not find Customers Loyalty Score");
                return null;
            }
        }
        return null;
    }


    @Override
    public Long checkCustomerAdditionalDetail(Integer customerId, String type) {
        Query query = manager.createQuery(" select count(*) FROM CustomerAdditionalDetail o where"
                + " o.customerId = :customerId and campaignName=:campaignName  group by o.customerId ");
        query.setParameter("customerId", customerId);
        query.setParameter("campaignName", type);
        Long response = 0L;
        try {
            response = (Long) query.getSingleResult();
        } catch (Exception e) {
            LOG.info("No record found");
        }
        return response;
    }

    @Override
    public boolean hasOrdersForOrderSource(String contactNumber, String orderSource) {
        LOG.info("Enter hasOrdersForOrderSource:: contactNumber" + contactNumber
                + " for orderSource:: " + orderSource);
        Query query = manager.createNativeQuery("SELECT od.ORDER_ID FROM ORDER_DETAIL od " +
                "INNER JOIN CUSTOMER_INFO ci ON od.CUSTOMER_ID=ci.CUSTOMER_ID " +
                "WHERE ci.CONTACT_NUMBER= :contactNumber AND od.ORDER_SOURCE = :orderSource ");
        query.setParameter("contactNumber", contactNumber);
        query.setParameter("orderSource", orderSource);
        LOG.info("hasOrdersForOrderSource:: " + AppUtils.isNonEmptyList(query.getResultList()));
        return AppUtils.isNonEmptyList(query.getResultList());
    }

    @Override
    public boolean updateCrmScreenUrl(CrmAppScreenDetail detail) {
        try {
            CrmAppScreenDetail screenDetail = manager.find(CrmAppScreenDetail.class, detail.getKey());
            screenDetail.setImagePath(detail.getImagePath());
            manager.flush();
            return true;
        } catch (Exception e) {
            LOG.error("Error Faced ::", e);
            return false;
        }
    }

    @Override
    public boolean updateCrmScreenStatus(List<CrmAppScreenDetail> details) {
        try {
            for (CrmAppScreenDetail data : details) {
                CrmAppScreenDetail screenDetail = manager.find(CrmAppScreenDetail.class, data.getKey());
                screenDetail.setStatus(data.getStatus());
            }
            manager.flush();
            return true;
        } catch (Exception e) {
            LOG.error("Error Faced ::", e);
            return false;
        }
    }

    @Override
    public List<OrderDetail> customerVisit(Integer customerId) {
        Query query = manager.createQuery("FROM OrderDetail e WHERE e.customerId =:customerId "
                + "AND e.orderStatus NOT IN (:orderStatus) AND e.channelPartnerId <>:channelPartnerId order by orderId desc  ");
        query.setParameter("customerId", customerId);
        query.setParameter("channelPartnerId",AppConstants.BAZAAR_PARTNER_ID);
        query.setParameter("orderStatus",
                Arrays.asList(OrderStatus.CANCELLED.name(), OrderStatus.CANCELLED_REQUESTED.name()));
        query.setMaxResults(10);
        List<OrderDetail> result = query.getResultList();
        return result;
    }

    @Override
    public OrderDetail getFirstZomatoOrder(Integer customerId) {
        try {
            Query query = manager.createQuery("FROM OrderDetail od WHERE od.customerId = :customerId AND od.channelPartnerId = :channelPartnerId ORDER BY od.orderId");
            query.setParameter("customerId", customerId);
            query.setParameter("channelPartnerId", AppConstants.CHANNEL_PARTNER_ZOMATO);
            query.setMaxResults(1);
            OrderDetail firstZomatoOrder = (OrderDetail) query.getSingleResult();
            return firstZomatoOrder;
        } catch(NoResultException e) {
            LOG.error("No Zomato Order found for customer id {}", customerId);
            return null;
        }
    }

    @Override
    public List<FeedbackDetail> feedbackDetails(Integer orderId, Integer customerId) {
        Query query = manager.createQuery("FROM FeedbackDetail e WHERE e.customerId =:customerId AND e.orderId =:orderId");
        query.setParameter("customerId",customerId);
        query.setParameter("orderId",orderId);
        return query.getResultList();
    }

    @Override
    public List<Integer> removeAllFaceIdsWithGivenFaceId(String faceId) {
        LOG.info("removing all customer-face-ID having face id {} ",faceId);
        List<Integer> customerIds = new ArrayList<>();
        Query query = manager.createQuery(" FROM CustomerInfo c where c.faceId =:faceId ");
        query.setParameter("faceId", faceId);
        List<CustomerInfo> customerInfoList = query.getResultList();
        if (customerInfoList != null && customerInfoList.size() > 0) {
            // remove face Ids
            for (CustomerInfo info : customerInfoList) {
                removeFaceMetaData(info);
                customerIds.add(info.getCustomerId());
            }
        }
        return customerIds;
    }

	@Override
	public CustomerOneViewData getCustomerOneViewData(int customerId, Integer brandId, List<Integer> excludeOrderIds) {
		LOG.info("Looking for customer one view for brandId {} and customerId {}", brandId, customerId);
		Query nq = this.manager.createNativeQuery(NamedQueryDefinition.CUSTOMER_ONE_VIEW.getQuery(), "CustomerOneViewData");
		nq.setParameter("brandId", brandId);
		nq.setParameter("customerId", customerId);
		nq.setParameter("excludeOrderIds", excludeOrderIds);
		List<CustomerOneViewData> customers = nq.getResultList();
		if (customers == null || customers.size() == 0) {
			LOG.info("Did Not Find customer one view for brandId {} and customerId {}", brandId, customerId);
			return null;
		} else {
			LOG.info("Found customer one view for brandId {} and customerId {}", brandId, customerId);
			CustomerOneViewData view = customers.get(0);
			updateCustomerOneView(brandId, view);
			return view;
		}
	}

	@Override
	public CustomerDineInView getCustomerDineInView(int customerId, Integer brandId, List<Integer> excludeOrderIds) {
		LOG.info("Looking for customer one view for brandId {} and customerId {}", brandId, customerId);
		Query nq = this.manager.createNativeQuery(NamedQueryDefinition.CUSTOMER_DINE_IN_VIEW.getQuery(), "CustomerDineInViewData");
		nq.setParameter("brandId", brandId);
		nq.setParameter("customerId", customerId);
		nq.setParameter("excludeOrderIds", excludeOrderIds);
		List<CustomerDineInView> customers = nq.getResultList();
		if (customers == null || customers.size() == 0) {
			LOG.info("Did Not Find customer one view for brandId {} and customerId {}", brandId, customerId);
			return null;
		} else {
			LOG.info("Found customer one view for brandId {} and customerId {}", brandId, customerId);
			CustomerDineInView view = customers.get(0);
			return view;
		}
	}

    @Override
    public CustomerTransactionViewData getCustomerTransactionViewData(int customerId, Integer brandId, List<Integer> excludeOrderIds) {
        LOG.info("Looking for customer one view for brandId {} and customerId {}", brandId, customerId);
        Query nq = this.manager.createNativeQuery(NamedQueryDefinition.CUSTOMER_TRANSACTION_VIEW.getQuery(), "CustomerTransactionViewData");
        nq.setParameter("brandId", brandId);
        nq.setParameter("customerId", customerId);
        nq.setParameter("excludeOrderIds", excludeOrderIds);
        List<CustomerTransactionViewData> customers = nq.getResultList();
        if (customers == null || customers.size() == 0) {
            LOG.info("Did Not Find customer one view for brandId {} and customerId {}", brandId, customerId);
            return null;
        } else {
            LOG.info("Found customer one view for brandId {} and customerId {}", brandId, customerId);
            CustomerTransactionViewData view = customers.get(0);
            return view;
        }
    }

	@Override
	public CustomerEmailData getCustomerEmailData(int customerId, Integer brandId) {
		LOG.info("Looking for customer email data for brandId {} and customerId {}", brandId, customerId);
		Query nq = this.manager.createNativeQuery(NamedQueryDefinition.CUSTOMER_EMAIL_DATA.getQuery(),
				"CustomerEmailData");
		nq.setParameter("brandId", brandId);
		nq.setParameter("customerId", customerId);
		nq.setParameter("currentDate", AppUtils.getBusinessDate());
		List<CustomerEmailData> customers = nq.getResultList();
		if (customers == null || customers.size() == 0) {
			LOG.info("Did Not Find customer email data for brandId {} and customerId {}", brandId, customerId);
			return null;
		} else {
			LOG.info("Found customer email data for brandId {} and customerId {}", brandId, customerId);
			return customers.get(0);
		}
	}

    @Override
    public List<CustomerCampaignOfferDetail> getNextBestOfferDetail(String type, Integer brandId){
        LOG.info("Looking For Unavailed Next Best Offer Detail");
        try {
            if(type.contains("CLM_DORMANT")) {
                return getNextBestForDormant(type, brandId);
            }else if(type.equals("CLM_REPEAT_1")){
                return getNextBestForRepeat(type, brandId);
            }
        }catch (Exception e){
            LOG.error("Exception Faced While Fetching Customer Info For Next Best Offer",e);
            return null;
        }
        return null;
    }




    @Override
    public CustomerInfo getCustomerInfoById(Integer cid) {
        return manager.find(CustomerInfo.class,cid);
    }

    private List<CustomerCampaignOfferDetail> getNextBestForRepeat(String type, Integer brandId) {
        Query query = manager.createQuery(" FROM CustomerCampaignOfferDetail where (isOfferApplied is null or isOfferApplied <> :isOfferApplied) and " +
                "couponStartDate >=:t10 and couponStartDate <=:t9  and campaignCloneCode = :campaignCloneCode and brandId = :brandId");
        query.setParameter("isOfferApplied", AppConstants.YES);
        query.setParameter("t10", AppUtils.getDayBeforeOrAfterCurrentDay(-10));
        query.setParameter("brandId", brandId);
        query.setParameter("t9", AppUtils.getDayBeforeOrAfterCurrentDay(-9));
        query.setParameter("campaignCloneCode", type);
        return query.getResultList();
    }

    private List<CustomerCampaignOfferDetail> getNextBestForDormant(String type, Integer brandId) {
        if (type.contains("REM")){
            Query query = manager.createQuery(" FROM CustomerCampaignOfferDetail c where ( (couponStartDate >=:t10 and couponStartDate <=:t9) " +
                    "or (couponStartDate >=:t25 and couponStartDate <=:t24) or (couponStartDate >=:t40 and couponStartDate <=:t39) " +
                    ") and campaignCloneCode like :campaignCloneCode  and brandId = :brandId");
            query.setParameter("t10",AppUtils.getDayBeforeOrAfterCurrentDay(-10));
            query.setParameter("t9", AppUtils.getDayBeforeOrAfterCurrentDay(-9));
            query.setParameter("t25", AppUtils.getDayBeforeOrAfterCurrentDay(-25));
            query.setParameter("t24", AppUtils.getDayBeforeOrAfterCurrentDay(-24));
            query.setParameter("t40", AppUtils.getDayBeforeOrAfterCurrentDay(-40));
            query.setParameter("t39", AppUtils.getDayBeforeOrAfterCurrentDay(-39));
            query.setParameter("brandId", brandId);
            query.setParameter("campaignCloneCode", type.replace("_REM","") + "%");
            return query.getResultList();
        } else if (type.contains("DORMANT_1")) {
            Query query = manager.createQuery(" FROM CustomerCampaignOfferDetail c where couponStartDate >=:t16 and couponStartDate <=:t15 " +
                    "and campaignCloneCode = :campaignCloneCode");
            query.setParameter("t16",AppUtils.getDayBeforeOrAfterCurrentDay(-16));
            query.setParameter("t15",AppUtils.getDayBeforeOrAfterCurrentDay(-15));
            query.setParameter("campaignCloneCode", type);
            return query.getResultList();
        } else if(type.contains("DORMANT_2")){
            Query query = manager.createQuery(" FROM CustomerCampaignOfferDetail c where couponStartDate >=:t31 and couponStartDate <=:t30  and campaignCloneCode = :campaignCloneCode");
            query.setParameter("t31", AppUtils.getDayBeforeOrAfterCurrentDay(-31));
            query.setParameter("t30", AppUtils.getDayBeforeOrAfterCurrentDay(-30));
            query.setParameter("campaignCloneCode", type);
            return query.getResultList();
        }
        return new ArrayList<>();
    }

    private void updateCustomerOneView(int brandId, CustomerOneViewData view) {
		view.setBrandId(brandId);
		view.setIsNew(view.getOverallOrders() == null || view.getOverallOrders() == 0
				|| !AppConstants.getValue(view.getAvailedSignupOffer()) ? AppConstants.YES : AppConstants.NO);
		view.setDeliveryApc(AppUtils.divide(view.getDeliverySales(), new BigDecimal(view.getDeliveryOrders())));
		view.setDineInApc(AppUtils.divide(view.getDineInSales(), new BigDecimal(view.getDineInOrders())));
		view.setOverallApc(AppUtils.divide(view.getOverallSales(), new BigDecimal(view.getOverallOrders())));
		view.setWalletApc(AppUtils.divide(view.getWalletSales(), new BigDecimal(view.getWalletOrders())));

		view.setActiveDeliveryApc(AppUtils.divide(view.getActiveDeliverySales(), new BigDecimal(view.getActiveDeliveryOrders())));
		view.setActiveDineInApc(AppUtils.divide(view.getActiveDineInSales(), new BigDecimal(view.getActiveDineInOrders())));
		view.setActiveOverallApc(AppUtils.divide(view.getActiveOverallSales(), new BigDecimal(view.getActiveOverallOrders())));
		view.setActiveWalletApc(AppUtils.divide(view.getActiveWalletSales(), new BigDecimal(view.getActiveWalletOrders())));

		view.setDeliveryDiscount(AppUtils.subtract(view.getDeliveryGmv(), view.getDeliverySales()));
		view.setDineInDiscount(AppUtils.subtract(view.getDineInGmv(), view.getDineInSales()));
		view.setOverallDiscount(AppUtils.subtract(view.getOverallGmv(), view.getOverallSales()));


		view.setActiveDeliveryDiscount(AppUtils.subtract(view.getActiveDeliveryGmv(), view.getActiveDeliverySales()));
		view.setActiveDineInDiscount(AppUtils.subtract(view.getActiveDineInGmv(), view.getActiveDineInSales()));
		view.setActiveOverallDiscount(AppUtils.subtract(view.getActiveOverallGmv(), view.getActiveOverallSales()));

	}

	@Override
	public List<CustomerCampaignOfferDetail> getAllNextBestOfferDetails(Integer brandId, String cloneCouponCode,
			Date couponStartDate) {
		Query query = null;
			query = manager.createQuery(" FROM CustomerCampaignOfferDetail c where couponStartDate = :couponStartDate "
					+ "and campaignCloneCode = :campaignCloneCode and c.brandId = :brandId and c.status = :active");
		query.setParameter("active", AppConstants.ACTIVE);
		query.setParameter("couponStartDate", couponStartDate);
		query.setParameter("brandId", brandId);
		query.setParameter("campaignCloneCode", cloneCouponCode);
		return query.getResultList();
	}

	@Override
	public List<CustomerCampaignOfferDetail> getUsedNextBestOfferDetails(Integer brandId, String cloneCouponCode,
			Date couponStartDate) {
		Query query =  manager.createQuery(" FROM CustomerCampaignOfferDetail c where couponStartDate =:couponStartDate "
					+ "and campaignCloneCode = :campaignCloneCode and c.brandId = :brandId and c.status = :active and c.isOfferApplied is not null and c.isOfferApplied = :yes");
		query.setParameter("yes", AppConstants.YES);
		query.setParameter("active", AppConstants.ACTIVE);
		query.setParameter("couponStartDate", couponStartDate);
		query.setParameter("brandId", brandId);
		query.setParameter("campaignCloneCode", cloneCouponCode);
		return query.getResultList();
	}

	@Override
	public Map<String, List<CustomerCampaignOfferDetail>> getUsedNextBestOfferDetails(Integer brandId,
			Date couponStartDate) {
		Map<String, List<CustomerCampaignOfferDetail>> map = new HashMap<>();
		Query query =  manager.createQuery(" FROM CustomerCampaignOfferDetail c where couponStartDate =:couponStartDate and c.brandId = :brandId and c.status = :active and c.isOfferApplied is not null and c.isOfferApplied = :yes");
		query.setParameter("yes", AppConstants.YES);
		query.setParameter("active", AppConstants.ACTIVE);
		query.setParameter("couponStartDate", couponStartDate);
		query.setParameter("brandId", brandId);
		List<CustomerCampaignOfferDetail> list = query.getResultList();
		if (list != null && list.size() > 0) {
			for (CustomerCampaignOfferDetail detail : list) {
				if (!map.containsKey(detail.getCampaignCloneCode())) {
					map.put(detail.getCampaignCloneCode(), new ArrayList<>());
				}
				map.get(detail.getCampaignCloneCode()).add(detail);
			}
		}
		return map;
	}

	@Override
	public List<CustomerCampaignOfferDetail> getNotUsedNextBestOfferDetails(Integer brandId, String cloneCouponCode,
			Date couponStartDate) {
		Query query =  manager.createQuery(" FROM CustomerCampaignOfferDetail c where couponStartDate =:couponStartDate "
					+ "and campaignCloneCode = :campaignCloneCode and c.brandId = :brandId and c.status = :active and (c.isOfferApplied is null or c.isOfferApplied = :no)");
		query.setParameter("no", AppConstants.NO);
		query.setParameter("active", AppConstants.ACTIVE);
		query.setParameter("couponStartDate", couponStartDate);
		query.setParameter("brandId", brandId);
		query.setParameter("campaignCloneCode", cloneCouponCode);
		return query.getResultList();
	}

	@Override
	public Map<String, List<CustomerCampaignOfferDetail>> getNotUsedNextBestOfferDetails(Integer brandId,
			Date couponStartDate) {
		Map<String, List<CustomerCampaignOfferDetail>> map = new HashMap<>();
		Query query = manager.createQuery(
				" FROM CustomerCampaignOfferDetail c where couponStartDate =:couponStartDate and c.brandId = :brandId and c.status = :active and (c.isOfferApplied is null or c.isOfferApplied = :no)");
		query.setParameter("no", AppConstants.NO);
		query.setParameter("active", AppConstants.ACTIVE);
		query.setParameter("couponStartDate", couponStartDate);
		query.setParameter("brandId", brandId);
		List<CustomerCampaignOfferDetail> list = query.getResultList();
		if (list != null && list.size() > 0) {
			for (CustomerCampaignOfferDetail detail : list) {
				if (!map.containsKey(detail.getCampaignCloneCode())) {
					map.put(detail.getCampaignCloneCode(), new ArrayList<>());
				}
				map.get(detail.getCampaignCloneCode()).add(detail);
			}
		}
		return map;
	}

    @Override
    public List<CampaignOfferDetail> getNotUsedNextOfferDetails(Integer brandId) {
        try {
            Query query = manager.createNativeQuery(NamedQueryDefinition.CUSTOMER_OFFER_FOR_REMINDER.getQuery(),"CampaignOfferDetail");
            query.setParameter("brandId", brandId);
            query.setParameter("active", AppConstants.ACTIVE);
            query.setParameter("no", AppConstants.NO);
            query.setParameter("currentDate", AppUtils.getNextDate(AppUtils.addDays(AppUtils.getCurrentTimestamp(), -1)));
            List<CampaignOfferDetail> list = query.getResultList();
            List<Integer> offerIds = new ArrayList<>();
            for(CampaignOfferDetail detail : list){
                offerIds.add(detail.getCapmpaignOfferDetailId());
            }
            LOG.info("Found offers to send reminder for offerIds : {}",JSONSerializer.toJSON(offerIds));
            return list;
        } catch (NoResultException e) {
            LOG.error("No offer found for reminder for brand id : {}",brandId);
        }
        return null;
    }

    @Override
    public List<CustomerCampaignOfferDetail> getPendingNextJourneyEligibleOffer(Integer brandId, Date nextOfferDate) {
        try {
            Query query = manager.createQuery("FROM CustomerCampaignOfferDetail c WHERE c.brandId = :brandId AND c.status = :active " +
                    "AND c.nextJourneyNumber IS NOT NULL and c.nextOfferDate = :nextOfferDate");
            query.setParameter("nextOfferDate",nextOfferDate);
            query.setParameter("brandId",brandId);
            query.setParameter("active",AppConstants.ACTIVE);
            List<CustomerCampaignOfferDetail> details = query.getResultList();
            if(details.isEmpty()){
                LOG.info("No offer found eligible for next journey offer");
            }else{
                LOG.info("{} Offers found for next journey offer",details.size());
            }
            return  details;
        }catch (Exception e){
            LOG.error("Error while fetching offers eligible for next journey");
        }
        return  null;
    }


    @Override
    public Map<String, Pair<Boolean, Boolean>> getNotificationFlags(List<String> customerContact){
        Query query =  manager.createQuery("SELECT c.contactNumber,c.isBlacklisted,c.isNumberVerified,c.isDND,c.optWhatsapp  FROM CustomerInfo c where c.contactNumber IN (:customerContact)");
        query.setParameter("customerContact",customerContact);
        List<Object[]> objects = query.getResultList();
        Map<String, Pair<Boolean, Boolean>> resultant = new HashMap<>();
        boolean sms = false;
        boolean whatsapp = false;
        if (!objects.isEmpty()){
            for (Object[] obj : objects) {
                try {
                    sms = !AppConstants.YES.equals(obj[1]) && AppConstants.YES.equals(obj[2]) && !AppConstants.YES.equals(obj[3]);
                    whatsapp = sms && AppConstants.YES.equals(obj[4]);
                    resultant.put((String) obj[0], new Pair<Boolean, Boolean>(sms, whatsapp));
                } catch (Exception e){
                    LOG.error("Exception Faced While Generating Notification Flags For Customer {}",(String)obj[1]);
                }
            }
        }
        return resultant;
    }

    @Override
    public boolean updateWhatsappOptInOut(CustomerResponse customerResponse){
        try {
            CustomerInfo customerInfo = manager.find(CustomerInfo.class, customerResponse.getId());
            customerInfo.setOptWhatsapp(customerResponse.isOptWhatsapp() ? AppConstants.YES : AppConstants.NO);
            manager.persist(customerInfo);
            manager.flush();
            return true;
        } catch (Exception e){
            LOG.info("Exception Faced While Updating Customer Info For Whatsapp ::: {}",customerResponse.getId());
            return false;
        }
    }
	public Integer checkCouponUsage(int customerId, String code) {
		try {
			Query query = manager.createQuery(
					" select count(*) FROM OrderDetail OD WHERE OD.customerId = :customerId and OD.offerCode = :code and OD.orderStatus <> :cancelled");
			query.setParameter("customerId", customerId);
			query.setParameter("code", code);
			query.setParameter("cancelled", OrderStatus.CANCELLED.name());
			Object obj = query.getSingleResult();
            // Todo change from long return type to integer
            Long longObj = (Long) obj;
			return obj == null ? null : longObj.intValue();
		} catch (NoResultException e) {
			LOG.error("Did not find any order with customer {} and code {}", customerId, code);
		}
		return null;
	}

	@Override
	public OfferLastRedemptionView getOrderDetailViaOffer(Integer customerId, String couponCode, Date businessDate, Integer dailyFreqCount) {
		try {
//			String queryString = "select max(OD.billingServerTime) FROM OrderDetail OD WHERE OD.customerId = :customerId" +
//                    " and OD.offerCode = :code AND (OD.businessDate is null OR OD.businessDate >= :businessDate) ";
            Query queryString = this.manager.createNativeQuery(NamedQueryDefinition.ORDER_FREQUENCY_FOR_OFFER.getQuery(), "OfferLastRedemptionView");
//			Query query = manager.createQuery(queryString);
            queryString.setParameter("customer", customerId);
            queryString.setParameter("code", couponCode);
            queryString.setParameter("businessDate", businessDate);
            queryString.setParameter("lastTimestamp", AppUtils.getDateBeforeOrAfterInSeconds(AppUtils.getCurrentTimestamp(),-dailyFreqCount*3600));
            OfferLastRedemptionView o = (OfferLastRedemptionView) queryString.getSingleResult();
            return o;
		} catch (NoResultException e) {
			LOG.error("Did not find any order with customer {} and code {}", customerId, couponCode);
		}
		return null;
	}

    @Override
    public CustomerContactInfoMapping getCustomerContactInfoMapping(Integer customerId) {
        try {
            Query query = manager.createQuery("FROM CustomerContactInfoMapping where customerId = :customerId");
            query.setParameter("customerId", customerId);
            return (CustomerContactInfoMapping) query.getSingleResult();
        } catch (NoResultException e) {
            LOG.error("Did not find any customer contact info mapping with customerId {}", customerId);
        }
        return null;
    }

    @Override
    public CustomerContactInfoMapping getCustomerContactInfoMappingFromOldContact(String oldContact) {
        try {
            Query query = manager.createQuery("FROM CustomerContactInfoMapping where oldContactNumber = :oldContact");
            query.setParameter("oldContact", oldContact);
            return (CustomerContactInfoMapping) query.getSingleResult();
        } catch (NoResultException e) {
            LOG.error("Did not find any customer contact info mapping with contact number {}", oldContact);
        }
        return null;
    }

    @Override
    public List<CustomerInfo> getEmpDiscount() throws Exception {
        try {
            Query query = manager.createQuery("FROM CustomerInfo C WHERE C.isEmailVerified = :isEmailVerified AND C.emailId LIKE :emailId AND (C.isDeleted = :no" +
                    " OR C.isDeleted is NULL)");
            query.setParameter("isEmailVerified", AppConstants.YES);
            query.setParameter("emailId", "%chaayos.com");
            query.setParameter("no",AppConstants.NO);
            List<CustomerInfo> result = query.getResultList();
            return result;
        } catch (Exception e){
            throw new Exception("Sorry Data not found");
        }
    }

    public Integer deactivateEmpDiscount(List<Integer> customerIds) throws Exception {
        try{
            Query query = manager.createQuery("update CustomerInfo C set C.isEmailVerified =:no,C.emailId=:null where C.customerId IN (:customerIds)");
            query.setParameter("no", AppConstants.NO);
            query.setParameter("null",null);
            query.setParameter("customerIds",customerIds);
            int rows = query.executeUpdate();
            return new Integer(rows);
        }catch (Exception e){
            throw new Exception("Sorry data for deactivating employee not found",e);
        }
    }

    @Override
    public void deactivateOldSubscriptionPlans() {
        Query query = manager.createQuery("update SubscriptionPlan S set S.status = :inActive where S.planEndDate < :currentDate and S.status = :active");
        query.setParameter("inActive", AppConstants.IN_ACTIVE);
        query.setParameter("currentDate", AppUtils.getCurrentTimestamp());
        query.setParameter("active", AppConstants.ACTIVE);
        int subscriptionPlanRes = query.executeUpdate();
        LOG.info("Old Subscription plans have been made IN_ACTIVE: {}", subscriptionPlanRes);
    }

    @Override
    public void deactivateOldSubscriptionPlanEvents() {
        Query query = manager.createQuery("update SubscriptionPlanEvent SE set SE.status = :inActive where SE.planEndDate < :currentDate and SE.status = :active");
        query.setParameter("inActive", AppConstants.IN_ACTIVE);
        query.setParameter("currentDate", AppUtils.getCurrentTimestamp());
        query.setParameter("active", AppConstants.ACTIVE);
        int subscriptionPlanEventRes = query.executeUpdate();
        LOG.info("Old Subscription Plan Events have been made IN_ACTIVE: {}", subscriptionPlanEventRes);
    }

    private NotificationPayload getNotificationPayload(CustomerInfo customerResponse) {
        NotificationPayload payload = new NotificationPayload();
        payload.setCustomerId(customerResponse.getCustomerId());
        payload.setOrderId(0);
        payload.setMessageType(AppConstants.WA_OPT_IN);
        payload.setContactNumber(customerResponse.getContactNumber());
        payload.setRequestTime(AppUtils.getCurrentTimestamp());
        payload.setSendWhatsapp(true);
        payload.setWhatsappOptIn(true);
        Map<String, String> map = new HashMap<>();
        map.put("firstName", customerResponse.getFirstName());
        payload.setPayload(map);
        return payload;
    }

    @Override
    public List<LoyaltyScore>  getLoyaltyScoreData(List<Integer> customerIds)  throws Exception {
        try {
            LOG.info("Query to fetch Loyalty Score");
            Query query = manager.createQuery("FROM LoyaltyScore WHERE customerId in (:customerIds)");
            query.setParameter("customerIds", customerIds);
            List<LoyaltyScore> result = query.getResultList();
            return result;
        } catch (Exception e){
            throw new Exception("No Loyalty Score Data  found for customers");
        }
    }

    @Override
    public List<LoyaltyEvents> getLoyaltyEventsForCustomer(List<Integer> customerIds) throws Exception {
        try{
           Query query = manager.createQuery("FROM LoyaltyEvents WHERE customerId in (:customerIds)  and transactionStatus = 'SUCCESS' and transactionType in ('DEBIT','CREDIT')");
           query.setParameter("customerIds",customerIds);
           List<LoyaltyEvents> res = query.getResultList();
           return  res;
        }catch (Exception e){
            throw new Exception("No Loyalty Event  found for customer id : "+customerIds.size());
        }

    }

    @Override
    public List<LoyaltyEvents> getLoyaltyDebitEventsForCustomer(List<Integer> customerIds) throws Exception {
        try{
            Query query = manager.createQuery("FROM LoyaltyEvents WHERE customerId in (:customerIds)  and transactionStatus = 'SUCCESS' and transactionType in ('DEBIT') order by loyaltyEventsId desc");
            query.setParameter("customerIds",customerIds);
            List<LoyaltyEvents> res = query.getResultList();
            return  res;
        }catch (Exception e){
            throw new Exception("No Loyalty Event  found for customer id : "+customerIds.size());
        }

    }

    @Override
    public List<Integer> getCustomersBeforeAddTime(Date startDate, Date endDate) {
        Query query = manager.createQuery("SELECT E.customerId FROM CustomerInfo E where E.addTime >= :startDate and E.addTime < :endDate and E.customerId not in (67456,142315,3527255,24035)");
        query.setParameter("startDate", startDate);
        query.setParameter("endDate", endDate);
        return query.getResultList();
    }

    @Override
    public List<Object[]> getCustomersWhoseLoyaltyPointsExpiringThisMonth(){
        Date date = AppUtils.getLastDayOfMonth(AppUtils.getCurrentDate());
        try {
            Query query = manager.createQuery("SELECT E.customerId,E.transactionPoints,E.redeemedPoints,S.acquiredPoints FROM " +
                    "LoyaltyEvents E inner join LoyaltyScore S " +
                    "on E.customerId = S.customerId where E.loyaltyEventStatus = 'ACTIVE' and E.expirationTime = :expireTime");
            query.setParameter("expireTime", date);
            List<Object[]> objects = query.getResultList();
            return objects;
        }catch (Exception e){
            LOG.info("Error in fetching Loyalty events for expiration time : {} and Error is ",date,e);
        }
        return null;
    }

    @Override
    public List<LoyaltyEvents> getLoyaltyEventsExpiryOn30thNovForCustomers(List<Integer> customerIds) throws Exception {
        try{
            LOG.info("Query To fetch LoyaltyEvents for customer whose points expiring this Nov.");
            Query query = manager.createQuery("FROM LoyaltyEvents WHERE customerId in (:customerIds)  and transactionStatus = 'SUCCESS' " +
                    "and loyaltyEventStatus = 'ACTIVE' order by 1 desc");
            query.setParameter("customerIds",customerIds);
            List<LoyaltyEvents> res = query.getResultList();
            return  res;
        }catch (Exception e){
            throw new Exception("No Loyalty Event  found for customer id : "+customerIds.size());
        }

    }

    @Override
    public UnitTableMappingDetail findOpenTableByCustomerId(Integer customerId){
        try {
            Query query = manager
                    .createQuery("From UnitTableMappingDetail where customerId = :customerId and tableStatus <> 'CLOSED' ");
            query.setParameter("customerId", customerId);
            UnitTableMappingDetail unitTableMappingDetail = (UnitTableMappingDetail) query.getSingleResult();
            return unitTableMappingDetail;
        }catch (NoResultException e){
            return null;
        }
    }

    @Override
    public CustomerInfo addCustomerFromCampaign(Customer customer) throws DataUpdationException{
        CustomerInfo customerInfo = new CustomerInfo();
        customerInfo.setContactNumber(customer.getContactNumber());
        customerInfo.setAcquisitionSource(customer.getAcquisitionSource());
        customerInfo.setAcquisitionToken(customer.getAcquisitionToken());
        customerInfo.setCountryCode(AppConstants.DEFAULT_COUNTRY_CODE);
        customerInfo.setAddTime(AppUtils.getCurrentTimestamp());
        customerInfo.setAcquisitionBrandId(AppConstants.CHAAYOS_BRAND_ID);
        customerInfo.setIsChaayosCustomer(AppConstants.YES);
        customerInfo.setIsBlacklisted(AppConstants.NO);
        customerInfo.setOptWhatsapp(AppConstants.YES);
        customerInfo.setSmsSubscriber(AppConstants.YES);
        manager.persist(customerInfo);
        manager.flush();
        if(Objects.nonNull(customerInfo.getCustomerId())){
            LoyaltyScore score = new LoyaltyScore(customerInfo.getCustomerId(), 0);
            manager.persist(score);
            manager.flush();
        }
        return customerInfo;
    }
}
