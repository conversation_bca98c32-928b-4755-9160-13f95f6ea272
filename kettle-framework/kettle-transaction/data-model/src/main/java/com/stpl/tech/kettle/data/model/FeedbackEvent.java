/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * OrderInfo generated by hbm2java
 */
@Entity
@Table(name = "ORDER_FEEDBACK_EVENT")
public class FeedbackEvent implements java.io.Serializable {

	/**
	 *
	 */
	private static final long serialVersionUID = 2413753119350530780L;

	private Integer feedbackEventId;
	private FeedbackDetail feedbackDetail;
	private String eventStatus;
	private String eventSource;
	private String eventType;
	private Integer rating;
	private String eventLongUrl;
	private String eventShortUrl;
	private String eventShortUrlId;
	private Date eventGenerationTime;
	private Date eventNotificationTime;
	private Date eventTriggerTime;
	private Date eventCompletionTime;
	private Integer latestFeedbackInfoId;
	private Integer brandId;
	private String ratingType;
	private Integer maxRating;

	public FeedbackEvent() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "FEEDBACK_EVENT_ID", unique = true, nullable = false)
	public Integer getFeedbackEventId() {
		return this.feedbackEventId;
	}

	public void setFeedbackEventId(Integer feedbackEventId) {
		this.feedbackEventId = feedbackEventId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "FEEDBACK_ID", nullable = false)
	public FeedbackDetail getFeedbackDetail() {
		return feedbackDetail;
	}

	public void setFeedbackDetail(FeedbackDetail feedbackDetail) {
		this.feedbackDetail = feedbackDetail;
	}

	@Column(name = "EVENT_STATUS", nullable = true, length = 30)
	public String getEventStatus() {
		return this.eventStatus;
	}

	public void setEventStatus(String orderStatus) {
		this.eventStatus = orderStatus;
	}

	@Column(name = "EVENT_SOURCE", nullable = false, length = 15)
	public String getEventSource() {
		return this.eventSource;
	}

	public void setEventSource(String hasParcel) {
		this.eventSource = hasParcel;
	}

	@Column(name = "EVENT_LONG_URL", nullable = true, length = 500)
	public String getEventLongUrl() {
		return eventLongUrl;
	}

	public void setEventLongUrl(String eventUrl) {
		this.eventLongUrl = eventUrl;
	}

	@Column(name = "EVENT_SHORT_URL", nullable = true, length = 150)
	public String getEventShortUrl() {
		return eventShortUrl;
	}

	public void setEventShortUrl(String eventShortUrl) {
		this.eventShortUrl = eventShortUrl;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "EVENT_GENERATION_TIME", nullable = false, length = 19)
	public Date getEventGenerationTime() {
		return this.eventGenerationTime;
	}

	public void setEventGenerationTime(Date feedbackTime) {
		this.eventGenerationTime = feedbackTime;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "EVENT_TRIGGER_TIME", nullable = false, length = 19)
	public Date getEventTriggerTime() {
		return eventTriggerTime;
	}

	public void setEventTriggerTime(Date eventTriggerTime) {
		this.eventTriggerTime = eventTriggerTime;
	}
	
	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "EVENT_NOTIFICATION_TIME", nullable = true, length = 19)
	public Date getEventNotificationTime() {
		return eventNotificationTime;
	}

	public void setEventNotificationTime(Date eventNotificationTime) {
		this.eventNotificationTime = eventNotificationTime;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "EVENT_COMPLETION_TIME", nullable = true, length = 19)
	public Date getEventCompletionTime() {
		return eventCompletionTime;
	}

	public void setEventCompletionTime(Date eventCompletionTime) {
		this.eventCompletionTime = eventCompletionTime;
	}
	
	@Column(name = "LATEST_FEEDBACK_INFO_ID", nullable = true)
	public Integer getLatestFeedbackInfoId() {
		return latestFeedbackInfoId;
	}

	public void setLatestFeedbackInfoId(Integer latestFeedbackInfoId) {
		this.latestFeedbackInfoId = latestFeedbackInfoId;
	}
	@Column(name = "EVENT_SHORT_URL_ID", nullable = true, length = 50)
	public String getEventShortUrlId() {
		return eventShortUrlId;
	}

	public void setEventShortUrlId(String eventShortUrlId) {
		this.eventShortUrlId = eventShortUrlId;
	}
	@Column(name = "EVENT_TYPE", nullable = true, length = 15)
	public String getEventType() {
		return eventType;
	}

	public void setEventType(String eventType) {
		this.eventType = eventType;
	}
	@Column(name = "FEEDBACK_RATING", nullable = true)
	public Integer getRating() {
		return rating;
	}

	public void setRating(Integer rating) {
		this.rating = rating;
	}

	@Column(name = "BRAND_ID")
	public Integer getBrandId() {
		return brandId;
	}

	public void setBrandId(Integer brandId) {
		this.brandId = brandId;
	}

	@Column(name = "RATING_TYPE")
	public String getRatingType() {
		return ratingType;
	}

	public void setRatingType(String ratingType) {
		this.ratingType = ratingType;
	}

	@Column(name = "MAX_RATING")
	public Integer getMaxRating() {
		return maxRating;
	}

	public void setMaxRating(Integer maxRating) {
		this.maxRating = maxRating;
	}

}
