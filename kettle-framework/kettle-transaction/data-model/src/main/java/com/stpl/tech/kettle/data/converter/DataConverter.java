/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.converter;

import com.stpl.tech.kettle.core.FeedbackEventType;
import com.stpl.tech.kettle.core.SubscriptionEventItemType;
import com.stpl.tech.kettle.core.TransactionConstants;
import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.core.data.vo.CustomerCardInfo;
import com.stpl.tech.kettle.core.data.vo.FeedbackEventInfo;
import com.stpl.tech.kettle.core.data.vo.OrderFetchStrategy;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.core.service.impl.CustomerFavChaiManagementServiceImpl;
import com.stpl.tech.kettle.data.model.AddonData;
import com.stpl.tech.kettle.data.model.AssemblyTATData;
import com.stpl.tech.kettle.data.model.CashPacketLogData;
import com.stpl.tech.kettle.data.model.ClosurePaymentDetails;
import com.stpl.tech.kettle.data.model.ClosurePaymentTaxDetails;
import com.stpl.tech.kettle.data.model.ComplimentaryCode;
import com.stpl.tech.kettle.data.model.CreditAccountDetail;
import com.stpl.tech.kettle.data.model.CustomerAddressInfo;
import com.stpl.tech.kettle.data.model.CustomerFavChaiMapping;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.CustomerOfferDetail;
import com.stpl.tech.kettle.data.model.CustomerTransactionDetail;
import com.stpl.tech.kettle.data.model.DeliveryDetail;
import com.stpl.tech.kettle.data.model.DeliveryPartner;
import com.stpl.tech.kettle.data.model.EmployeeMealData;
import com.stpl.tech.kettle.data.model.ExpenseDetailData;
import com.stpl.tech.kettle.data.model.ExpenseUpdateEventData;
import com.stpl.tech.kettle.data.model.FavChaiCustomizationDetail;
import com.stpl.tech.kettle.data.model.FeedbackEvent;
import com.stpl.tech.kettle.data.model.LoyaltyScore;
import com.stpl.tech.kettle.data.model.ManualBillBookData;
import com.stpl.tech.kettle.data.model.MeterDetailsData;
import com.stpl.tech.kettle.data.model.MonkCalibrationEventDetail;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.data.model.OrderExternalSettlementData;
import com.stpl.tech.kettle.data.model.OrderInvoiceDetail;
import com.stpl.tech.kettle.data.model.OrderItemAddon;
import com.stpl.tech.kettle.data.model.OrderItemTaxDetail;
import com.stpl.tech.kettle.data.model.OrderNPSDetail;
import com.stpl.tech.kettle.data.model.OrderNPSResponseData;
import com.stpl.tech.kettle.data.model.OrderPaymentDenominationDetail;
import com.stpl.tech.kettle.data.model.OrderPaymentDetail;
import com.stpl.tech.kettle.data.model.OrderRePrintDetail;
import com.stpl.tech.kettle.data.model.OrderRefundDetail;
import com.stpl.tech.kettle.data.model.OrderSettlement;
import com.stpl.tech.kettle.data.model.OrderStatusEvent;
import com.stpl.tech.kettle.data.model.OrderTaxDetail;
import com.stpl.tech.kettle.data.model.PartnerOrderRiderStatesDetail;
import com.stpl.tech.kettle.data.model.PnlAdjustmentDetail;
import com.stpl.tech.kettle.data.model.PullDenomination;
import com.stpl.tech.kettle.data.model.PullDetail;
import com.stpl.tech.kettle.data.model.ReferralMappingData;
import com.stpl.tech.kettle.data.model.ReportAttributes;
import com.stpl.tech.kettle.data.model.ReportDefinition;
import com.stpl.tech.kettle.data.model.ReportStatusEventData;
import com.stpl.tech.kettle.data.model.SettlementDenomination;
import com.stpl.tech.kettle.data.model.SettlementDetail;
import com.stpl.tech.kettle.data.model.SubscriptionDetail;
import com.stpl.tech.kettle.data.model.SubscriptionEventDetail;
import com.stpl.tech.kettle.data.model.SubscriptionEventItem;
import com.stpl.tech.kettle.data.model.SubscriptionItem;
import com.stpl.tech.kettle.data.model.SubscriptionItemAddon;
import com.stpl.tech.kettle.data.model.SubscriptionItemTaxDetail;
import com.stpl.tech.kettle.data.model.SubscriptionSettlement;
import com.stpl.tech.kettle.data.model.SubscriptionTaxDetail;
import com.stpl.tech.kettle.data.model.TableOrderMappingDetail;
import com.stpl.tech.kettle.data.model.UnitBudgetExceededData;
import com.stpl.tech.kettle.data.model.UnitClosureDetails;
import com.stpl.tech.kettle.data.model.UnitExpenseDetail;
import com.stpl.tech.kettle.data.model.UnitProductInventory;
import com.stpl.tech.kettle.data.model.UnitTableMappingDetail;
import com.stpl.tech.kettle.data.model.UnitToDeliveryPartnerMappings;
import com.stpl.tech.kettle.data.model.WorkStationManualTaskDetail;
import com.stpl.tech.kettle.delivery.model.DeliveryResponse;
import com.stpl.tech.kettle.delivery.model.DeliveryStatus;
import com.stpl.tech.kettle.domain.model.ActionDetail;
import com.stpl.tech.kettle.domain.model.AttributeType;
import com.stpl.tech.kettle.domain.model.CalculationIndexStatus;
import com.stpl.tech.kettle.domain.model.CartOrderItem;
import com.stpl.tech.kettle.domain.model.ClosurePaymentDetail;
import com.stpl.tech.kettle.domain.model.ComplimentaryDetail;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.CustomerBasicInfo;
import com.stpl.tech.kettle.domain.model.CustomerCashPacketLog;
import com.stpl.tech.kettle.domain.model.CustomerFavChaiMappingVO;
import com.stpl.tech.kettle.domain.model.CustomerInfoDineIn;
import com.stpl.tech.kettle.domain.model.CustomerOffer;
import com.stpl.tech.kettle.domain.model.CustomerTransaction;
import com.stpl.tech.kettle.domain.model.Dimension;
import com.stpl.tech.kettle.domain.model.DiscountDetail;
import com.stpl.tech.kettle.domain.model.ExpenseDetail;
import com.stpl.tech.kettle.domain.model.ExpenseStatus;
import com.stpl.tech.kettle.domain.model.ExpenseUpdateEvent;
import com.stpl.tech.kettle.domain.model.ExternalPartnerDetail;
import com.stpl.tech.kettle.domain.model.ExternalSettlement;
import com.stpl.tech.kettle.domain.model.FavouriteChai;
import com.stpl.tech.kettle.domain.model.IterationType;
import com.stpl.tech.kettle.domain.model.MeterDetail;
import com.stpl.tech.kettle.domain.model.MeterDetailEntryType;
import com.stpl.tech.kettle.domain.model.MilkVariant;
import com.stpl.tech.kettle.domain.model.MonkCalibrationEvent;
import com.stpl.tech.kettle.domain.model.ObjectFactory;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderDetailTrim;
import com.stpl.tech.kettle.domain.model.OrderInvoice;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.OrderItemComposition;
import com.stpl.tech.kettle.domain.model.OrderNPS;
import com.stpl.tech.kettle.domain.model.OrderNPSResponse;
import com.stpl.tech.kettle.domain.model.OrderPaymentDenomination;
import com.stpl.tech.kettle.domain.model.OrderRefund;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.domain.model.OrderStatusDomain;
import com.stpl.tech.kettle.domain.model.PartnerOrderRiderStatesDetailData;
import com.stpl.tech.kettle.domain.model.PercentageDetail;
import com.stpl.tech.kettle.domain.model.PnlAdjustment;
import com.stpl.tech.kettle.domain.model.ProductInventory;
import com.stpl.tech.kettle.domain.model.ProductSource;
import com.stpl.tech.kettle.domain.model.PullPacket;
import com.stpl.tech.kettle.domain.model.PullPacketDenomination;
import com.stpl.tech.kettle.domain.model.PullPacketStatus;
import com.stpl.tech.kettle.domain.model.PullSettlementDenomination;
import com.stpl.tech.kettle.domain.model.PullSettlementDetail;
import com.stpl.tech.kettle.domain.model.RefundType;
import com.stpl.tech.kettle.domain.model.ReportAttribute;
import com.stpl.tech.kettle.domain.model.ReportDef;
import com.stpl.tech.kettle.domain.model.ReportStatusEvent;
import com.stpl.tech.kettle.domain.model.SaveCustomerFavChaiRequest;
import com.stpl.tech.kettle.domain.model.Settlement;
import com.stpl.tech.kettle.domain.model.SettlementStatus;
import com.stpl.tech.kettle.domain.model.SettlementType;
import com.stpl.tech.kettle.domain.model.Subscription;
import com.stpl.tech.kettle.domain.model.SubscriptionStatus;
import com.stpl.tech.kettle.domain.model.SubscriptionType;
import com.stpl.tech.kettle.domain.model.TATSummary;
import com.stpl.tech.kettle.domain.model.TableOrder;
import com.stpl.tech.kettle.domain.model.TableOrderItem;
import com.stpl.tech.kettle.domain.model.TableStatus;
import com.stpl.tech.kettle.domain.model.TaxDetail;
import com.stpl.tech.kettle.domain.model.TransactionDetail;
import com.stpl.tech.kettle.domain.model.UnitBudgetExceeded;
import com.stpl.tech.kettle.domain.model.UnitClosure;
import com.stpl.tech.kettle.domain.model.UnitExpense;
import com.stpl.tech.kettle.domain.model.UnitTableMapping;
import com.stpl.tech.kettle.domain.model.Variant;
import com.stpl.tech.kettle.domain.model.WorkstationManualTask;
import com.stpl.tech.kettle.referral.model.ReferentInfo;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.RecipeCache;
import com.stpl.tech.master.data.converter.MasterDataConverter;
import com.stpl.tech.master.domain.model.Address;
import com.stpl.tech.master.domain.model.BillBookOrder;
import com.stpl.tech.master.domain.model.BillType;
import com.stpl.tech.master.domain.model.ComplimentaryReason;
import com.stpl.tech.master.domain.model.DenominationDetail;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.ListData;
import com.stpl.tech.master.domain.model.ManualBillBook;
import com.stpl.tech.master.domain.model.MasterObjectFactory;
import com.stpl.tech.master.domain.model.PartnerDetail;
import com.stpl.tech.master.domain.model.PaymentMode;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductBasicDetail;
import com.stpl.tech.master.domain.model.ProductClassification;
import com.stpl.tech.master.domain.model.ProductPrice;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.payment.model.OrderPayment;
import com.stpl.tech.master.payment.model.PaymentStatus;
import com.stpl.tech.master.recipe.model.BasicInfo;
import com.stpl.tech.master.recipe.model.IngredientProductDetail;
import com.stpl.tech.master.recipe.model.IngredientVariantDetail;
import com.stpl.tech.master.recipe.model.ProductData;
import com.stpl.tech.master.recipe.model.RecipeDetail;
import com.stpl.tech.master.recipe.model.UnitOfMeasure;
import com.stpl.tech.master.recipe.read.model.OptionDataVO;
import com.stpl.tech.master.recipe.read.model.RecipeDetailVO;
import com.stpl.tech.master.tax.model.TaxationDetailDao;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.WsType;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.xml.datatype.DatatypeConfigurationException;
import javax.xml.datatype.DatatypeFactory;
import javax.xml.datatype.XMLGregorianCalendar;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

public class DataConverter {

    private static final ObjectFactory factory = new ObjectFactory();
    private static final MasterObjectFactory masterFactory = new MasterObjectFactory();

    private static final Logger LOG = LoggerFactory.getLogger(DataConverter.class);



    public static PartnerDetail convert(UnitToDeliveryPartnerMappings mapping) {
        PartnerDetail detail = masterFactory.createPartnerDetail();
        detail.setMappingId(mapping.getId());
        detail.setDetail(convert(mapping.getDeliveryPartner()));
        detail.setPriority(mapping.getPriority());
        return detail;
    }

    public static List<ProductInventory> getInventoryDetails(MasterDataCache cache, UnitBasicDetail unit,
                                                             Map<Integer, UnitProductInventory> currentInventory, Set<Integer> productsToBeTracked) {
        List<ProductInventory> list = new ArrayList<ProductInventory>();
        for (ProductBasicDetail product : cache.getAllProductsBasicDetail()) {
            if (product.isInventoryTracked() && productsToBeTracked != null
                    && productsToBeTracked.contains(product.getDetail().getId())) {
                list.add(convertToInventory(unit, product, currentInventory));
            }
        }

        return list;
    }

    public static List<ProductInventory> getInventoryDetailsForProducts(MasterDataCache cache, UnitBasicDetail unit,
                                                                        Map<Integer, UnitProductInventory> currentInventory, Set<Integer> productsToBeTracked) {
        List<ProductInventory> list = new ArrayList<ProductInventory>();
        Set<Integer> productIds = currentInventory.keySet();
        for (ProductBasicDetail product : cache.getAllProductsBasicDetail()) {
            if (product.isInventoryTracked() && productsToBeTracked != null
                    && productsToBeTracked.contains(product.getDetail().getId())
                    && productIds.contains(product.getDetail().getId())) {
                list.add(convertToInventory(unit, product, currentInventory));
            }
        }
        return list;
    }

    public static Customer convert(CustomerInfo obj, LoyaltyScore score, BigDecimal chaayosCash) {
        return convert(obj, score, chaayosCash, null);
    }

    public static CustomerBasicInfo convert(CustomerInfo info){
        CustomerBasicInfo customer = new CustomerBasicInfo();
        customer.setCustomerId(info.getCustomerId());
        customer.setName(info.getFirstName());
        customer.setEmailId(info.getEmailId());
        customer.setContactNumber(info.getContactNumber());
        return customer;
    }

    public static Customer convert(CustomerInfo obj, LoyaltyScore score, BigDecimal chaayosCash, CustomerTransactionDetail transactionDetail) {
        if (obj == null) {
            return null;
        }
        Customer data = factory.createCustomer();

        data.setContactNumberVerified(
                convert(AppConstants.getValue(obj.getIsNumberVerified(), AppConstants.DEFAULT_IS_NUMBER_VERIFIED)));
        data.setContactNumber(obj.getContactNumber());
        data.setCountryCode(obj.getCountryCode());
        data.setEmailId(obj.getEmailId());
        data.setFirstName(obj.getFirstName());
        data.setMiddleName(obj.getMiddleName());
        data.setLastName(obj.getLastName());
        data.setId(obj.getCustomerId());
        data.setInternal(AppUtils.getStatus(obj.getIsInternal()));
        data.setRegistrationUnitId(obj.getRegistrationUnitId());
        data.setAcquisitionSource(obj.getAcquisitionSource());
        data.setAcquisitionToken(obj.getAcquisitionToken());
        data.setSmsSubscriber(AppUtils.getStatus(obj.getSmsSubscriber()));
        data.setEmailSubscriber(AppUtils.getStatus(obj.getEmailSubscriber()));
        data.setLoyaltySubscriber(AppUtils.getStatus(obj.getLoyaltySubscriber()));
        data.setBlacklisted(AppUtils.getStatus(obj.getIsBlacklisted()));
        data.setDND(AppUtils.getStatus(obj.getIsDND()));
        data.setOptOutOfFaceIt(AppUtils.getStatus(obj.getOptOutOfFaceIt()));
        data.setOptOutTime(obj.getOptOutTime());
        data.setAppAction(obj.getAppAction());
        data.setAppActionTime(obj.getAppActionTime());
        if (obj.getFaceId() != null) {
            data.setFaceId(obj.getFaceId());
        }
        data.setEmailVerified(
                convert(AppConstants.getValue(obj.getIsEmailVerified(), AppConstants.DEFAULT_IS_EMAIL_VERIFIED)));
        for (CustomerAddressInfo address : obj.getCustomerAddressInfos()) {
            data.getAddresses().add(convert(address));
        }
        if (score != null) {
            data.setLoyaltyPoints(score.getAcquiredPoints());
            data.setLastOrderId(score.getLastOrderId());
            data.setLastOrderTime(score.getLastOrderTime());
            data.setOrderCount(score.getOrderCount());
            data.setAvailedSignupOffer(AppConstants.getValue(score.getAvailedSignupOffer()));
            data.setLastNPSTime(score.getLastNPSTime());
            data.setSignUpOfferExpired(AppConstants.getValue(score.getSignupOfferExpired()));
            data.setSignupOfferExpiryTime(score.getSignupOfferExpiryTime());
        }
        if (obj.getAddTime() != null) {
            data.setAddTime(obj.getAddTime());
        }
        if (obj.getNumberVerificationTime() != null) {
            data.setNumberVerificationTime(obj.getNumberVerificationTime());
        }
        if (AppConstants.getValue(obj.getIsRefSubscriber())) {
            data.setRefCode(obj.getRefCode());
        }
        data.setIsRefSubscriber(obj.getIsRefSubscriber());
        data.setRefAcquisitionSource(obj.getRefAcquisitionSource());
        data.setReferredOn(obj.getReferredOn());
        data.setReferralDataId(obj.getReferralDataId());
        data.setReferrerId(obj.getReferrerId());
        data.setReferrerAwarded(AppUtils.getStatus(obj.getReferrerAwarded()));
        data.setChaayosCash(chaayosCash);
        data.setAcquisitionBrandId(obj.getAcquisitionBrandId());
        data.setChaayosCustomer(AppUtils.getStatus(obj.getIsChaayosCustomer()));
        if (obj.getGender() != null) {
            data.setGender(obj.getGender());
        }
        if (obj.getDateOfBirth() != null) {
            data.setDateOfBirth(obj.getDateOfBirth());
        }
        if (obj.getAnniversary() != null) {
            data.setAnniversary(obj.getAnniversary());
        }
        data.setCustomerAppId(obj.getCustomerAppId());
        if (Objects.nonNull(obj.getOptWhatsapp())) {
            data.setOptWhatsapp(obj.getOptWhatsapp());
        }
        if (transactionDetail != null) {
            data.setCustomerTransaction(convert(transactionDetail));
        }
        return data;
    }

    public static CustomerTransaction convert(CustomerTransactionDetail transactionDetail) {
        CustomerTransaction customerTransaction = new CustomerTransaction();
        customerTransaction.setCustomerId(transactionDetail.getCustomerId());
        customerTransaction.setBrandId(transactionDetail.getBrandId());
        customerTransaction.setFirstOrderBusinessDate(transactionDetail.getFirstOrderBusinessDate());
        customerTransaction.setId(transactionDetail.getId());
        customerTransaction.setFirstOrderId(transactionDetail.getFirstOrderId());
        customerTransaction.setFirstOrderChannelPartnerId(transactionDetail.getFirstOrderChannelPartnerId());
        customerTransaction.setFirstOrderSource(transactionDetail.getFirstOrderSource());
        customerTransaction.setFirstOrderUnitId(transactionDetail.getFirstOrderUnitId());
        if (transactionDetail.getLastOrderBusinessDate() != null) {
            customerTransaction.setLastOrderBusinessDate(transactionDetail.getLastOrderBusinessDate());
        }
        if (transactionDetail.getLastOrderChannelPartnerId() != null) {
            customerTransaction.setLastOrderChannelPartnerId(transactionDetail.getLastOrderChannelPartnerId());
        }
        if (transactionDetail.getLastOrderSource() != null) {
            customerTransaction.setLastOrderSource(transactionDetail.getLastOrderSource());
        }
        if (transactionDetail.getLastOrderId() != null) {
            customerTransaction.setLastOrderId(transactionDetail.getLastOrderId());
        }
        if (transactionDetail.getLastOrderUnitId() != null) {
            customerTransaction.setLastOrderUnitId(transactionDetail.getLastOrderUnitId());
        }
        customerTransaction.setTotalDeliveryOrders(transactionDetail.getTotalDeliveryOrders());
        customerTransaction.setTotalDineInOrders(transactionDetail.getTotalDineInOrders());
        customerTransaction.setTotalOrders(transactionDetail.getTotalOrders());
        return customerTransaction;
    }

    public static CustomerTransactionDetail convert(CustomerTransaction customerTransaction) {
        CustomerTransactionDetail transactionDetail = new CustomerTransactionDetail();
        transactionDetail.setCustomerId(customerTransaction.getCustomerId());
        transactionDetail.setBrandId(customerTransaction.getBrandId());
        transactionDetail.setFirstOrderBusinessDate(customerTransaction.getFirstOrderBusinessDate());
        transactionDetail.setId(customerTransaction.getId());
        transactionDetail.setFirstOrderId(customerTransaction.getFirstOrderId());
        transactionDetail.setFirstOrderChannelPartnerId(customerTransaction.getFirstOrderChannelPartnerId());
        transactionDetail.setFirstOrderSource(customerTransaction.getFirstOrderSource());
        transactionDetail.setFirstOrderUnitId(customerTransaction.getFirstOrderUnitId());
        if (customerTransaction.getLastOrderBusinessDate() != null) {
            transactionDetail.setLastOrderBusinessDate(transactionDetail.getLastOrderBusinessDate());
        }
        if (customerTransaction.getLastOrderChannelPartnerId() != null) {
            transactionDetail.setLastOrderChannelPartnerId(transactionDetail.getLastOrderChannelPartnerId());
        }
        if (customerTransaction.getLastOrderSource() != null) {
            transactionDetail.setLastOrderSource(transactionDetail.getLastOrderSource());
        }
        if (customerTransaction.getLastOrderId() != null) {
            transactionDetail.setLastOrderId(transactionDetail.getLastOrderId());
        }
        if (customerTransaction.getLastOrderUnitId() != null) {
            transactionDetail.setLastOrderUnitId(transactionDetail.getLastOrderUnitId());
        }
        transactionDetail.setTotalDeliveryOrders(customerTransaction.getTotalDeliveryOrders());
        transactionDetail.setTotalDineInOrders(customerTransaction.getTotalDineInOrders());
        transactionDetail.setTotalOrders(customerTransaction.getTotalOrders());
        return transactionDetail;
    }

    public static CustomerInfoDineIn convert(int customerId, LoyaltyScore loyaltyScore,
                                             BigDecimal cash,
                                             CustomerCardInfo customerCardInfo,
                                             String refCode, OrderDetail lastOrder, String lastItemName, Date lastUpdatedTime, BigDecimal totalSpent,
                                             int orderCount,CustomerInfo customerInfo,MasterDataCache masterDataCache) {

        CustomerInfoDineIn customerInfoDineIn = factory.createCustomerInfoDineIn();
        customerInfoDineIn.setRefCode(refCode);
        customerInfoDineIn.setCustomerId(customerId);
        if (Objects.nonNull(lastUpdatedTime)) {
            customerInfoDineIn.setLastUpdatedTime(lastUpdatedTime);
        }
        if (Objects.nonNull(lastItemName)) {
            customerInfoDineIn.setLastOrderItemName(lastItemName);
        }
        if (Objects.nonNull(totalSpent)) {
            customerInfoDineIn.setTotalSpent(totalSpent);
        }
        if (customerCardInfo != null) {
            customerInfoDineIn.setCardAmount(customerCardInfo.getCardAmount());
            customerInfoDineIn.setHasCard(customerCardInfo.isHasCard());

        }
        if (Objects.nonNull(lastOrder)) {
            customerInfoDineIn.setLastOrderId(lastOrder.getOrderId());
            customerInfoDineIn.setLastOrderTime(lastOrder.getBillGenerationTime());
        }
        customerInfoDineIn.setChaayosCash(cash);
        customerInfoDineIn.setOrderCount(orderCount);

        if (loyaltyScore != null) {
            customerInfoDineIn.setCustomerId(loyaltyScore.getCustomerId());
//            customerInfoDineIn.setLastOrderId(loyaltyScore.getLastOrderId());
//            customerInfoDineIn.setLastOrderTime(loyaltyScore.getLastOrderTime());
            customerInfoDineIn.setLoyaltyPoints(loyaltyScore.getAcquiredPoints());
//            customerInfoDineIn.setOrderCount(loyaltyScore.getOrderCount());
            if (loyaltyScore.getOrderCount() == null || (loyaltyScore.getOrderCount() == 1
                    && !TransactionUtils.checkWithinTripThresholdTime(loyaltyScore.getLastOrderTime(),masterDataCache)
                    && !AppUtils.getStatus(loyaltyScore.getAvailedSignupOffer()))) {
                customerInfoDineIn.setOfferAvailed(false);
            } else {
                customerInfoDineIn.setOfferAvailed(true);
            }
            if (loyaltyScore.getAvailedSignupOffer() != null) {
                customerInfoDineIn.setSignupOfferAvailed(AppUtils.getStatus(loyaltyScore.getAvailedSignupOffer()));
            }
            if (loyaltyScore.getLastOrderId() != null) {
             /*
                 First order has been created
              */
                customerInfoDineIn.setSignupOfferExpiryTime(loyaltyScore.getSignupOfferExpiryTime());
                if (loyaltyScore.getSignupOfferExpired() != null) {
                    customerInfoDineIn.setSignupOfferExpired(AppUtils.getStatus(loyaltyScore.getSignupOfferExpired()));
                } else {
                    /*
                        If sign up expiry is set as null then offer has not been availed
                     */
                    customerInfoDineIn.setSignupOfferExpired(false);
                }
            } else {
			    /*
			        Customer did not order anything till now hence setting value to null
			     */
                customerInfoDineIn.setSignupOfferExpired(null);
                customerInfoDineIn.setSignupOfferExpiryTime(null);
            }


        }
        if (Objects.nonNull(customerInfo)) {
            customerInfoDineIn.setOptWhatsapp(Objects.nonNull(customerInfo.getOptWhatsapp()) ? customerInfo.getOptWhatsapp() : AppConstants.NO);
            customerInfoDineIn.setSmsSubscriber(Objects.nonNull(customerInfo.getSmsSubscriber()) ? customerInfo.getSmsSubscriber() : AppConstants.NO);
            customerInfoDineIn.setLoyalteaAvailable(Objects.nonNull(loyaltyScore.getAcquiredPoints()) ? Math.floorDiv(loyaltyScore.getAcquiredPoints(), 60) : 0);
        }
        return customerInfoDineIn;
    }
//    public static CustomerInfoDineIn convert(int customerId, LoyaltyScore loyaltyScore,
//                                             BigDecimal cash,
//                                             CustomerCardInfo customerCardInfo,
//                                             String refCode,String lastItemName,Date lastUpdatedTime,BigDecimal totalSpent) {
//
//        CustomerInfoDineIn customerInfoDineIn = factory.createCustomerInfoDineIn();
//        customerInfoDineIn.setRefCode(refCode);
//        customerInfoDineIn.setCustomerId(customerId);
//        if(Objects.nonNull(lastUpdatedTime)) {
//            customerInfoDineIn.setLastUpdatedTime(lastUpdatedTime);
//        }
//        if(Objects.nonNull(lastItemName)){
//            customerInfoDineIn.setLastOrderItemName(lastItemName);
//        }
//        if(Objects.nonNull(totalSpent)) {
//            customerInfoDineIn.setTotalSpent(totalSpent);
//        }
//        if (customerCardInfo != null) {
//            customerInfoDineIn.setCardAmount(customerCardInfo.getCardAmount());
//            customerInfoDineIn.setHasCard(customerCardInfo.isHasCard());
//
//        }
//        customerInfoDineIn.setChaayosCash(cash);
//        if (loyaltyScore != null) {
//            customerInfoDineIn.setCustomerId(loyaltyScore.getCustomerId());
//            customerInfoDineIn.setLastOrderId(loyaltyScore.getLastOrderId());
//            customerInfoDineIn.setLastOrderTime(loyaltyScore.getLastOrderTime());
//            customerInfoDineIn.setLoyaltyPoints(loyaltyScore.getAcquiredPoints());
//            customerInfoDineIn.setOrderCount(loyaltyScore.getOrderCount());
//			if (loyaltyScore.getOrderCount() == null || (loyaltyScore.getOrderCount() == 1
//					&& !TransactionUtils.checkWithinTripThresholdTime(loyaltyScore.getLastOrderTime())
//					&& !AppUtils.getStatus(loyaltyScore.getAvailedSignupOffer()))) {
//				customerInfoDineIn.setOfferAvailed(false);
//			} else {
//				customerInfoDineIn.setOfferAvailed(true);
//			}
//			if(loyaltyScore.getAvailedSignupOffer() != null){
//                customerInfoDineIn.setSignupOfferAvailed(AppUtils.getStatus(loyaltyScore.getAvailedSignupOffer()));
//            }
//			if(loyaltyScore.getLastOrderId() != null){
//			    /*
//			        First order has been created
//			     */
//                customerInfoDineIn.setSignupOfferExpiryTime(loyaltyScore.getSignupOfferExpiryTime());
//                if(loyaltyScore.getSignupOfferExpired() != null){
//                    customerInfoDineIn.setSignupOfferExpired(AppUtils.getStatus(loyaltyScore.getSignupOfferExpired()));
//                } else {
//                    /*
//                        If sign up expiry is set as null then offer has not been availed
//                     */
//                    customerInfoDineIn.setSignupOfferExpired(false);
//                }
//            } else {
//			    /*
//			        Customer did not order anything till now hence setting value to null
//			     */
//                customerInfoDineIn.setSignupOfferExpired(null);
//                customerInfoDineIn.setSignupOfferExpiryTime(null);
//            }
//
//
//        }
//        return customerInfoDineIn;
//    }

//	public static CustomerLoyaltyEntry convert(LoyaltyEvents event, Integer availablePoints){
//		CustomerLoyaltyEntry customerLoyaltyEntry = factory.createCustomerLoyaltyEntry();
//		customerLoyaltyEntry.setCode(event.getTransactionCode());
//		customerLoyaltyEntry.setCodeType(event.getTransactionCodeType());
//		customerLoyaltyEntry.setPoints(event.getTransactionPoints());
//		customerLoyaltyEntry.setStatus(event.getTransactionStatus());
//		customerLoyaltyEntry.setTime(event.getTransactionTime());
//		customerLoyaltyEntry.setType(event.getTransactionType());
//		customerLoyaltyEntry.setAdded();
//		customerLoyaltyEntry.setTotal(availablePoints);
//		customerLoyaltyEntry.setUnit();
//		return customerLoyaltyEntry;
//	}

    public static CustomerCashPacketLog convert(CashPacketLogData cashPacketLogData, BigDecimal balance, String name) {
        CustomerCashPacketLog data = factory.createCustomerCashPacketLog();
        data.setBalance(balance);
        data.setAmount(cashPacketLogData.getTransactionAmount());
        data.setCodeType(cashPacketLogData.getTransactionCodeType());
        data.setTime(cashPacketLogData.getTransactionTime());
        data.setType(cashPacketLogData.getTransactionType());
        data.setName(name);
        return data;
    }

    public static Address convert(CustomerAddressInfo obj) {
        Address add = masterFactory.createAddress();
        add.setId(obj.getAddressId());
        add.setName(obj.getName());
        add.setEmail(obj.getEmail());
        add.setContact1(obj.getContact());
        add.setLandmark(obj.getLandmark());
        add.setLine1(obj.getAddressLine1());
        add.setLine2(obj.getAddressLine2());
        add.setLine3(obj.getAddressLine3());
        add.setSubLocality(obj.getSubLocality());
        add.setLocality(obj.getLocality());
        add.setCity(obj.getCity());
        add.setState(obj.getState());
        add.setCountry(obj.getCountry());
        add.setZipCode(obj.getZipcode());
        add.setAddressType(obj.getAddressType());
        add.setCompany(obj.getCompany());
        add.setPreferredAddress(obj.getPreferredAddress());


        add.setSource(obj.getSource());
        add.setSourceId(obj.getSourceId());

        return add;
    }

    public static ProductInventory convertToInventory(UnitBasicDetail unit, ProductBasicDetail product,
                                                      Map<Integer, UnitProductInventory> currentNumberOfUnits) {
        return convertToInventory(unit, product, currentNumberOfUnits.get(product.getDetail().getId()));
    }

    public static ProductInventory convertToInventory(UnitBasicDetail unit, ProductBasicDetail product,
                                                      UnitProductInventory data) {
        ProductInventory inventory = factory.createProductInventory();
        inventory.setProduct(product);
        if (data != null) {
            inventory.setQuantity(data.getNoOfUnits());
            inventory.setLastStockOutTime(data.getLastStockOutTime());
            inventory.setLastUpdatedTime(data.getLastUpdateTmstmp());
            inventory.setExpireQuantity(data.getExpireQuantity());
        }
        inventory.setUnit(unit);
        return inventory;
    }

    public static ProductInventory convert(MasterDataCache cache, UnitProductInventory obj)
            throws DataNotFoundException {
        ProductInventory inventory = factory.createProductInventory();
        ProductBasicDetail product = cache.getProductBasicDetail(obj.getProductId());
        inventory.setProduct(product);
        inventory.setUnit(cache.getUnitBasicDetail(obj.getUnitId()));
        inventory.setLastStockOutTime(obj.getLastStockOutTime());
        inventory.setLastUpdatedTime(obj.getLastUpdateTmstmp());
        inventory.setQuantity(obj.getNoOfUnits());
        return inventory;
    }

    public static com.stpl.tech.kettle.domain.model.InventoryThresholdData convert(com.stpl.tech.kettle.data.model.InventoryThresholdData obj) {
        com.stpl.tech.kettle.domain.model.InventoryThresholdData inventory = factory.createInventoryThresholdData();
        inventory.setId(obj.getInventoryThresholdDataId());
        inventory.setUnitId(obj.getUnitId());
        inventory.setProductId(obj.getProductId());
        inventory.setDayOfTheWeek(obj.getDayOfTheWeek());
        inventory.setAvgQuantity(obj.getAverageQuantity());
        inventory.setMaxQuantity(obj.getMaxQuantity());
        inventory.setMinQuantity(obj.getMinQuantity());
        inventory.setTotalDays(obj.getTotalDays());
        inventory.setTotalQuantity(obj.getTotalQuantity());
        inventory.setLastUpdateTime(obj.getLastUpdateTime());
        inventory.setStatus(obj.getThresholdDataStatus());
        return inventory;
    }

    public static IdCodeName convert(DeliveryPartner type) {

        IdCodeName detail = masterFactory.createIdCodeName();
        detail.setId(type.getPartnerId());
        detail.setName(type.getPartnerDisplayName());
        detail.setCode(type.getPartnerCode());
        detail.setStatus(type.getPartnerStatus());
        detail.setType(type.getPartnerType());
        return detail;
    }

    public static Order convert(MasterDataCache cache, OrderDetail obj, OrderFetchStrategy strategy,RecipeCache recipeCache,
                                EnvironmentProperties properties) {
        Order order = factory.createOrder();
        order.setOrderId(obj.getOrderId());
        order.setUnitOrderId(obj.getUnitOrderId());
        // Cannot be null
        order.setChannelPartner(obj.getChannelPartnerId());
        order.setBusinessDate(obj.getBusinessDate());
        order.setDeliveryPartner(obj.getDeliveryPartnerId());
        order.setCustomerId(obj.getCustomerId());
        // Cannot be null
        order.setEmployeeId(obj.getEmpId());
        order.setSource(obj.getOrderSource());
        order.setSourceId(obj.getOrderSourceId());
        order.setPartnerCustomerId(obj.getPartnerCustomerId());
        order.setGenerateOrderId(obj.getGeneratedOrderId());
        order.setHasParcel(convert(AppConstants.getValue(obj.getHasParcel(), AppConstants.DEFAULT_HAS_PARCEL)));
        order.setSettlementType(obj.getSettlementType() == null ? TransactionConstants.DEFAULT_SETTLEMENT_TYPE
                : SettlementType.valueOf(obj.getSettlementType()));
        order.setStatus(obj.getOrderStatus() == null ? TransactionConstants.DEFAULT_ORDER_STATUS
                : OrderStatus.valueOf(obj.getOrderStatus()));
        order.setCancellationDetails(getCancellationDetail(obj));
        // /Cannot be null
        order.setUnitId(obj.getUnitId());
        order.setUnitName(cache.getUnitBasicDetail(obj.getUnitId()).getName());
        order.setTerminalId(obj.getTerminalId());
        order.setTableNumber(obj.getTableNumber());
        order.setTransactionDetail(
                toTransactionDetail(obj, (strategy == null || (strategy != null && strategy.isFetchTaxes()))));
        order.setBillCreationTime(obj.getBillGenerationTime());
        order.setBillStartTime(obj.getBillStartTime());
        order.setPrintCount(obj.getPrintCount());
        order.setOrderRemark(obj.getOrderRemark());
        order.setSourceVersion(obj.getSourceVersion());
        order.setDeliveryAddress(obj.getDeliveryAddress());
        order.setSubscriptionDetail(convert(obj.getSubscriptionDetail()));
        order.setOfferCode(obj.getOfferCode());
        order.setTempCode(obj.getTempCode());
        order.setBillingServerTime(obj.getBillingServerTime());
        order.setCustomerName(obj.getCustomerName() != null ? obj.getCustomerName() : "");
        order.setCampaignId(obj.getCampaignId());
        order.setTokenNumber(obj.getTokenNumber());
        order.setOrderType(obj.getOrderType());
        order.setLinkedOrderId(obj.getLinkedOrderId());
        order.setPointsRedeemed(obj.getPointsRedeemed() != null ? obj.getPointsRedeemed() : 0);
        order.setTableRequestId(obj.getTableRequestId());
        order.setGiftCardOrder(AppConstants.getValue(obj.getGiftCardOrder()));
        order.setOrderAttribute(obj.getOrderAttribute());
        order.setQrLink(obj.getQrLink());
        order.setQrHeader(TransactionConstants.QR_CODE_HEARDER);
        order.setBrandId(obj.getBrandId());
        order.setWastageType(obj.getWastageType());
		if (AppConstants.ORDER_TYPE_PAID_EMPLOYEE_MEAL.equals(obj.getOrderType())
				|| AppConstants.ORDER_TYPE_EMPLOYEE_MEAL.equals(obj.getOrderType())) {
			List<EmployeeMealData> employeeMealData = obj.getEmployeeMealData();
			if (Objects.nonNull(employeeMealData) && employeeMealData.size() > 0) {
				for (EmployeeMealData employee : employeeMealData) {
					if (order.getUnitId() == employee.getUnitId()) {
						order.setEmployeeMeal(true);
						order.setEmployeeIdForMeal(employee.getEmployeeId());
						break;
					}
				}
			}
		}
        if (obj.getManualBillBookNo() != null) {
            order.setBillBookNo(obj.getManualBillBookNo());
        }

        if ((strategy == null || (strategy != null && strategy.isFetchPrintDetails())) && obj.getPrintCount() > 1) {
            for (OrderRePrintDetail detail : obj.getOrderReprints()) {
                order.getReprints().add(convert(detail));
            }
        }
        if ((strategy == null || (strategy != null && strategy.isFetchItems()))) {
            for (com.stpl.tech.kettle.data.model.OrderItem item : obj.getOrderItems()) {
                if (item.getParentItemId() == null) {
                    OrderItem oi = convert(cache, item, recipeCache, order.getSource(), order.getBrandId());
                    if (AppConstants.SERVICE_CHARGE_SUBTYPE.equals(oi.getProductSubType())) {
                        LOG.info("Found service charge item :: {}", item.getProductId());
                        order.setServiceChargeItem(oi);
                        continue;
                    }
                    order.getOrders().add(oi);
                }
            }

            // combo
            for (com.stpl.tech.kettle.data.model.OrderItem item : obj.getOrderItems()) {
                if (item.getParentItemId() != null) {
                    OrderItem comboItem = convert(cache, item,recipeCache,order.getSource(),order.getBrandId());
                    for (OrderItem comboParent : order.getOrders()) {
                        if (item.getParentItemId().equals(comboParent.getItemId())) {
                            comboParent.getComposition().getMenuProducts().add(comboItem);
                            break;
                        }
                    }
                }

            }

        }
        if ((strategy == null || (strategy != null && strategy.isFetchSettlement()))) {
            for (OrderSettlement item : obj.getOrderSettlements()) {
                order.getSettlements().add(convert(cache, item));
                if (item.getPaymentModeId() == AppConstants.PAYMENT_MODE_CASH) {
                    order.setPendingCash(true);
                }
            }
        }
        if (obj.getOutOfDelivery() != null && obj.getOutOfDelivery().equalsIgnoreCase("Y")) {
            order.setOod(true);
        } else {
            order.setOod(false);
        }

		if (obj.getInvoiceDetail() != null) {
			OrderInvoiceDetail invoice = obj.getInvoiceDetail();
			OrderInvoice i = new OrderInvoice();
			i.setCompanyAddress(invoice.getCompanyAddress());
			i.setCompanyName(invoice.getCompanyName());
			i.setInvoiceId(obj.getInvoiceId());
			i.setGstIn(invoice.getGstIn());
			i.setTaxType(invoice.getTaxType());
			order.setInvoice(i);
		}
		TransactionUtils.calculateOrderPrepTime(order, cache);
		return order;
    }

    public static Order convert(MasterDataCache cache, SubscriptionDetail obj, SubscriptionEventDetail event)
            throws DataNotFoundException {
        Order order = factory.createOrder();
        order.setGenerateOrderId(obj.getGeneratedSubscriptionId());
        order.setSource(UnitCategory.COD.name());
        // Cannot be null
        order.setChannelPartner(obj.getChannelPartnerId());
        order.setCustomerId(obj.getCustomerInfo().getCustomerId());
        // Cannot be null
        order.setEmployeeId(obj.getEmpId());
        order.setSourceId(obj.getOrderSourceId());
        order.setHasParcel(true);
        order.setSettlementType(obj.getSettlementType() == null ? TransactionConstants.DEFAULT_SETTLEMENT_TYPE
                : SettlementType.valueOf(obj.getSettlementType()));
        order.setStatus(TransactionConstants.DEFAULT_ORDER_STATUS);
        order.setCancellationDetails(getCancellationDetail(obj));
        // /Cannot be null
        order.setUnitId(obj.getUnitId());
        order.setUnitName(cache.getUnitBasicDetail(obj.getUnitId()) == null ? obj.getUnitId() + ""
                : cache.getUnitBasicDetail(obj.getUnitId()).getName());
        order.setTransactionDetail(toTransactionDetail(obj));
        order.setBillCreationTime(obj.getSubscriptionCreationTime());
        order.setBillStartTime(obj.getSubscriptionCreationTime());
        order.setPrintCount(0);
        order.setOrderRemark(event != null && event.getRemark() != null ? event.getRemark() : obj.getOrderRemark());
        order.setDeliveryAddress(obj.getDeliveryAddress());
        order.setDeliveryPartner(
                AppConstants.getValue(obj.getAutomatedDelivery()) ? AppConstants.DELIVERY_PARTNER_AUTOMATED
                        : AppConstants.DELIVERY_PARTNER_NONE);
        for (SubscriptionItem item : obj.getSubscriptionItems()) {
            order.getOrders().add(convert(cache, item));
        }

        for (SubscriptionSettlement item : obj.getSubscriptionSettlements()) {
            order.getSettlements().add(convert(cache, item));
            if (item.getPaymentModeId() == AppConstants.PAYMENT_MODE_CASH) {
                order.setPendingCash(true);
            }
        }
        order.setSubscriptionDetail(convert(obj));
        return order;
    }

    public static Subscription convert(SubscriptionDetail detail) {
        if (detail == null) {
            return null;
        }

        Subscription subscription = factory.createSubscription();
        subscription.setType(SubscriptionType.valueOf(detail.getSubscriptionType()));
        List<SubscriptionEventItem> eventList = detail.getEventItems();
        subscription.getDaysOfTheWeek().addAll(convert(SubscriptionEventItemType.DAY_OF_WEEK, eventList));
        subscription.getTimeOfTheDay().addAll(convert(SubscriptionEventItemType.TIME_OF_DAY, eventList));
        subscription.getDaysOfTheMonth().addAll(convert(SubscriptionEventItemType.DAY_OF_MONTH, eventList));
        subscription.setEndDate(AppUtils.getDate(detail.getEndDate()));
        subscription.setStartDate(AppUtils.getDate(detail.getStartDate()));
        subscription.setSubscriptionId(detail.getSubscriptionId());
        subscription.setAutomatedDelivery(AppConstants.getValue(detail.getAutomatedDelivery()));
        subscription.setEmailNotification(AppConstants.getValue(detail.getEmailNotification()));
        subscription.setSmsNotification(AppConstants.getValue(detail.getSmsNotification()));
        subscription.setSubscriptionStatus(SubscriptionStatus.valueOf(detail.getSubscriptionStatus()));

        return subscription;
    }

    private static List<Integer> convert(SubscriptionEventItemType type, List<SubscriptionEventItem> events) {
        List<Integer> list = new ArrayList<>();
        if (events != null) {
            for (SubscriptionEventItem event : events) {
                if (AppConstants.ACTIVE.equals(event.getEventItemStatus())
                        && type.name().equals(event.getEventItemType())) {
                    list.add(event.getEventItemValue());
                }
            }
        }
        return list;

    }

    private static ActionDetail convert(OrderRePrintDetail detail) {
        ActionDetail obj = factory.createActionDetail();
        obj.setReason(detail.getPrintReason());
        obj.setApprovedBy(detail.getApprovedBy());
        obj.setGeneratedBy(detail.getGeneratedBy());
        obj.setActionTime(detail.getReprintTime());
        return obj;
    }

    private static ActionDetail getCancellationDetail(OrderDetail order) {
        ActionDetail obj = factory.createActionDetail();
        obj.setActionTime(order.getBillCancellationTime());
        obj.setApprovedBy(order.getCancelApprovedBy());
        obj.setGeneratedBy(order.getCancelledBy());
        obj.setReason(order.getCancelationReason());
        obj.setBookedWastage(order.getWastageType());
        obj.setReasonId(order.getCancellationReasonId());
        return obj;
    }

    private static ActionDetail getCancellationDetail(SubscriptionDetail order) {
        ActionDetail obj = factory.createActionDetail();
        obj.setActionTime(order.getCancellationTime());
        obj.setGeneratedBy(order.getCancelledBy());
        obj.setReason(order.getCancellationReason());
        return obj;
    }

    public static OrderItem convert(MasterDataCache cache, com.stpl.tech.kettle.data.model.OrderItem item,RecipeCache recipeCache,
                                    String source , Integer brandId) {
        OrderItem data = factory.createOrderItem();
        data.setItemId(item.getOrderItemId());
        data.setProductId(item.getProductId());
        Product product = cache.getProduct(item.getProductId());
        if(Objects.nonNull(product))
        {
            data.setProductAttr(product.getAttribute());
            data.setStationCategory(product.getStationCategoryName());
            data.setProductSubType(product.getSubType());
        }
        data.setProductCategory(cache.getProductCategory(cache.getProduct(item.getProductId()).getType()).getDetail());
        data.setProductSubCategory(cache.getProductSubCategory(cache.getProduct(item.getProductId()).getSubType()));
        data.setProductName(item.getProductName());
        data.setProductAliasName(Objects.nonNull(item.getProductAliasName())? item.getProductAliasName() : item.getProductName());
        data.setAmount(item.getPaidAmount());
        data.setTotalAmount(item.getTotalAmount());
        data.setBillType(BillType.valueOf(item.getBillType()));
        data.setCode(item.getTaxCode());
        data.setTakeAway(AppConstants.getValue(item.getTakeAway()));
        data.setComplimentaryDetail(toComplimentaryDetail(item));
        data.setDimension(item.getDimension());
        data.setProductDescription(product.getDescription());
        DiscountDetail discount = factory.createDiscountDetail();
        discount.setDiscount(convert(item.getDiscountPercent(), item.getDiscountAmount()));
        discount.setDiscountCode(item.getDiscountReasonId());
        discount.setDiscountReason(item.getDiscountReason());
        discount.setPromotionalOffer(item.getPromotionalDiscount());
        data.setDiscountDetail(discount);
        data.setPrice(item.getPrice());
        data.setQuantity(item.getQuantity());
        data.setComposition(new OrderItemComposition());
        data.setRecipeId(item.getRecipeId() != null ? item.getRecipeId() : 0);
        data.setRecipeProfile(item.getRecipeProfile());
        data.setTaxDeductedByPartner(AppUtils.getStatus(item.getTaxDeductedByPartner()));
        data.setOrderItemRemark(item.getOrderItemRemark());
        //add Others to Composition
        addOthersToComposition(item, data,recipeCache,source);
        // TODO add required variable
        // ProductSource.SCM, ProductClassification.VARIANT
        if (true) {
            for (OrderItemAddon addon : item.getOrderItemAddons()) {
                addToComposition(cache, addon, data,brandId);
            }
        }
        data.setTax(item.getTaxAmount());
        if (item.getOrderItemTaxes() != null) {
            for (OrderItemTaxDetail tax : item.getOrderItemTaxes()) {

                data.getTaxes().add(convert(tax));
            }
        }
        return data;
    }

    private static void addOthersToComposition(com.stpl.tech.kettle.data.model.OrderItem item, OrderItem data, RecipeCache recipeCache, String source) {
        RecipeDetail recipeDetail = recipeCache.getRecipe(item.getRecipeId());
        if(Objects.nonNull(recipeDetail)){
            if(Objects.nonNull(source)){
                switch (UnitCategory.valueOf(source)){
                    case COD:
                        if(Objects.nonNull(recipeDetail.getDeliveryConsumables())  && recipeDetail.getDeliveryConsumables().size()>0){
                            recipeDetail.getDeliveryConsumables().stream().forEach(deliveryConsumable ->{
                                if(Objects.nonNull(deliveryConsumable.getDisplay()) && deliveryConsumable.getDisplay() && Objects.nonNull(deliveryConsumable.getDisplayCode())){
                                    data.getComposition().getOthers().add(deliveryConsumable);
                                }
                            });
                        }
                        break;
                    case CAFE:
                        if(Objects.nonNull(recipeDetail.getDineInConsumables())  && recipeDetail.getDineInConsumables().size()>0){
                            recipeDetail.getDineInConsumables().stream().forEach(dineInConsumable ->{
                                if(Objects.nonNull(dineInConsumable.getDisplay()) && dineInConsumable.getDisplay() && Objects.nonNull(dineInConsumable.getDisplayCode())){
                                    data.getComposition().getOthers().add(dineInConsumable);
                                }
                            });
                        }
                        break;
                    case TAKE_AWAY:
                        if(Objects.nonNull(recipeDetail.getTakeawayConsumables())  && recipeDetail.getTakeawayConsumables().size()>0){
                            recipeDetail.getTakeawayConsumables().stream().forEach(takewayConsumable ->{
                                if(Objects.nonNull(takewayConsumable.getDisplay()) && takewayConsumable.getDisplay() && Objects.nonNull(takewayConsumable.getDisplayCode())){
                                    data.getComposition().getOthers().add(takewayConsumable);
                                }
                            });
                        }
                        break;
                    default:
                        break;
                }
            }
            if(Objects.nonNull(recipeDetail.getIngredient()) && Objects.nonNull(recipeDetail.getIngredient().getComponents()) && recipeDetail.getIngredient().getComponents().size()>0){
                recipeDetail.getIngredient().getComponents().stream().forEach(otherProduct ->{
                    if(Objects.nonNull(otherProduct.getDisplay()) && otherProduct.getDisplay() && Objects.nonNull(otherProduct.getDisplayCode())){
                        data.getComposition().getOthers().add(otherProduct);
                    }
                });
            }
        }
    }

    private static void addToComposition(MasterDataCache cache, AddonData addon, OrderItem data,Integer brandId) {

        if (addon.getType() != null) {
            if (!addon.getType().equalsIgnoreCase(AppConstants.PREFERENCE)) {
                switch (ProductClassification.valueOf(addon.getType())) {
                    case VARIANT:
                        data.getComposition().getVariants().add(convertToVarient(addon)); // IngredientVarientDetail
                        break;
                    case PRODUCT_VARIANT:
                        data.getComposition().getProducts().add(convert(cache, addon)); // IngredientProductDetail
                        break;
                    case FREE_ADDON:
                        data.getComposition().getAddons().add(convert(cache, addon)); // IngredientProductDetail
                        break;
                    case PAID_ADDON:
                        data.getComposition().getAddons().add(convert(cache, addon));// IngredientProductDetail
                        break;
                    case FREE_OPTION:
                        data.getComposition().getOptions().add(addon.getName());// String
                        if(Objects.nonNull(cache.getSpecialMilkVariant(addon.getName()))){
                            data.setMilkVariant(MilkVariant.builder().productId(cache.getSpecialMilkVariant(addon.getName())).
                                    productName(addon.getName()).profile(AppUtils.getMilkVariantPaidAddonPrefix(addon.getName()) + data.getRecipeProfile())
                                            .scmProductId(AppUtils.getMilkVariantPaidAddonSCMProduct(addon.getName(),brandId)).
                                    scmProductName(AppUtils.getMilkVariantPaidAddonSCMProductName(addon.getName(),brandId))
                                    .build());
                        }
                        break;
                    /*case OTHERS:
                        data.getComposition().getOthers().add(convert(cache,addon));*/
                    default:
                        break;
                }
            }
        } else {
            data.getComposition().getAddons().add(convert(cache, addon));
        }
    }

    private static IngredientProductDetail convert(MasterDataCache cache, AddonData addon) {
        IngredientProductDetail i = new IngredientProductDetail();
        i.setQuantity(addon.getQuantity());
        i.setDefaultSetting(AppConstants.getValue(addon.getDefaultSetting()));
        i.setUom(addon.getUom() == null ? null : UnitOfMeasure.valueOf(addon.getUom()));
        ProductData p = new ProductData();
        if (ProductSource.MENU.name().equals(addon.getSource())) {
            ProductBasicDetail detail = cache.getProductBasicDetail(addon.getProductId());
            if (detail != null) {
                p.setClassification(detail.getClassification());
                p.setCode(detail.getDetail().getCode());
                p.setName(detail.getDetail().getName());
                p.setProductId(detail.getDetail().getId());
                p.setShortCode(detail.getDetail().getShortCode());
            }

        } else {
            p.setProductId(addon.getProductId());
            p.setName(addon.getName());
        }
        i.setProduct(p);
        BasicInfo info = new BasicInfo();
        info.setCode(addon.getDimension() != null ? addon.getDimension() : AppConstants.DIMENSION_NONE);
        info.setName(addon.getDimension() != null ? addon.getDimension() : AppConstants.DIMENSION_NONE);
        i.setDimension(info);
        return i;
    }

    private static IngredientVariantDetail convertToVarient(AddonData addon) {
        IngredientVariantDetail i = new IngredientVariantDetail();
        i.setDefaultSetting(AppConstants.getValue(addon.getDefaultSetting()));
        i.setQuantity(addon.getQuantity());
        i.setUom(addon.getUom() == null ? null : UnitOfMeasure.valueOf(addon.getUom()));
        i.setProductId(addon.getProductId());
        i.setAlias(addon.getName());
        return i;
    }


    public static OrderItem convert(MasterDataCache cache, SubscriptionItem item) {
        OrderItem data = factory.createOrderItem();
        data.setItemId(item.getSubscriptionItemId());
        data.setProductId(item.getProductId());
        data.setProductCategory(cache.getProductCategory(cache.getProduct(item.getProductId()).getType()).getDetail());
        data.setProductSubCategory(cache.getProductSubCategory(cache.getProduct(item.getProductId()).getSubType()));
        data.setProductName(item.getProductName());
        data.setAmount(item.getPaidAmount());
        data.setTotalAmount(item.getTotalAmount());
        data.setBillType(BillType.valueOf(item.getBillType()));
        data.setCode(item.getTaxCode());
        data.setTakeAway(AppConstants.getValue(item.getTakeAway()));
        data.setDimension(item.getDimension());
        DiscountDetail discount = factory.createDiscountDetail();
        discount.setDiscount(convert(item.getDiscountPercent(), item.getDiscountAmount()));
        discount.setDiscountCode(item.getDiscountReasonId());
        discount.setDiscountReason(item.getDiscountReason());
        data.setDiscountDetail(discount);
        data.setPrice(item.getPrice());
        data.setQuantity(item.getQuantity());
        data.setComposition(new OrderItemComposition());
        data.setTax(item.getTaxAmount());
        for (SubscriptionItemAddon addon : item.getSubscriptionItemAddons()) {
            addToComposition(cache, addon, data,null);
        }
        if (item.getSubscriptionItemTaxes() != null) {
            for (SubscriptionItemTaxDetail tax : item.getSubscriptionItemTaxes()) {
                data.getTaxes().add(convert(tax));
            }
        }
        return data;
    }

    public static Settlement convert(MasterDataCache cache, OrderSettlement settlement) {

        Settlement data = factory.createSettlement();
        data.setAmount(settlement.getAmountPaid());
        data.setMode(settlement.getPaymentModeId());
        data.setSettlementId(settlement.getSettlementId());
        data.setModeDetail(cache.getPaymentMode(settlement.getPaymentModeId()));
        if (settlement.getExternalTransactions() != null && settlement.getExternalTransactions().size() > 0) {
            for (OrderExternalSettlementData d : settlement.getExternalTransactions()) {
                ExternalSettlement s = new ExternalSettlement();
                s.setExternalSettlementId(d.getExternalSettlementId());
                s.setExternalTransactionId(d.getExternalTransactionId());
                s.setAmount(d.getAmountPaid());
                data.getExternalSettlements().add(s);
            }
        }
        if (settlement.getPreviousPaymentMode() != null) {
            data.setEdited(AppConstants.getValue(settlement.getEdited()));
            data.setOldMode(cache.getPaymentMode(settlement.getPreviousPaymentMode()));
            data.setEditedBy(cache.getEmployee(settlement.getEditedBy()));
        }
        if (settlement.getExtraVouchers().compareTo(BigDecimal.ZERO) == 0) {
            data.setExtraVouchers(BigDecimal.ZERO);
        } else {
            data.setExtraVouchers(settlement.getExtraVouchers());
        }
        for (OrderPaymentDenominationDetail denomination : settlement.getDenominations()) {
            data.getDenominations().add(convert(denomination));
        }
        return data;

    }

    private static OrderPaymentDenomination convert(OrderPaymentDenominationDetail denomination) {
        OrderPaymentDenomination data = factory.createOrderPaymentDenomination();
        data.setId(denomination.getId());
        data.setDenominationDetailId(denomination.getId());
        data.setOrderId(denomination.getOrderId());
        data.setTotalAmount(data.getTotalAmount());
        data.setSettlementId(denomination.getOrderSettlement().getSettlementId());
        data.setCount(data.getCount());
        return data;
    }

    public static Settlement convert(MasterDataCache cache, SubscriptionSettlement settlement) {

        Settlement data = factory.createSettlement();
        data.setAmount(settlement.getAmountPaid());
        data.setMode(settlement.getPaymentModeId());
        data.setSettlementId(settlement.getSubscriptionSettlementId());
        data.setModeDetail(cache.getPaymentMode(settlement.getPaymentModeId()));
        return data;

    }

    private static ComplimentaryDetail toComplimentaryDetail(com.stpl.tech.kettle.data.model.OrderItem item) {
        ComplimentaryDetail data = factory.createComplimentaryDetail();
        data.setIsComplimentary(
                convert(AppConstants.getValue(item.getIsComplimentary(), AppConstants.DEFAULT_IS_COMPLIMENTARY)));
        data.setReason(item.getComplimentaryReason());
        data.setReasonCode(item.getComplimentaryTypeId());
        return data;
    }

    private static TransactionDetail toTransactionDetail(OrderDetail obj, boolean fetchTaxes) {
        TransactionDetail detail = factory.createTransactionDetail();

        DiscountDetail discount = factory.createDiscountDetail();
        discount.setDiscount(convert(obj.getDiscountPercent(), obj.getDiscountAmount()));
        discount.setDiscountCode(obj.getDiscountReasonId());
        discount.setPromotionalOffer(obj.getPromotionalDiscount());
        discount.setDiscountReason(obj.getDiscountReason());
        discount.setTotalDiscount(obj.getTotalDiscount());
        detail.setDiscountDetail(discount);
        detail.setTotalAmount(obj.getTotalAmount());
        detail.setSaleAmount(obj.getSaleAmount());
        detail.setTaxableAmount(obj.getTaxableAmount());
        detail.setPaidAmount(obj.getSettledAmount());
        detail.setRoundOffValue(obj.getRoundOffAmount());
        detail.setSavings(obj.getSavingAmount());
        detail.setTax(obj.getTaxAmount());
        if (obj.getOrderTaxes() != null && fetchTaxes) {
            for (OrderTaxDetail tax : obj.getOrderTaxes()) {
                detail.getTaxes().add(convert(tax));
            }
        }
        detail.setServiceTaxAmount(obj.getServiceTaxAmount());
        detail.setServiceCharge(obj.getServiceCharge());
        detail.setServiceChargePercent(obj.getServiceChargePercent());
        detail.setServiceChargeDescription(AppConstants.SERVICE_CHARGE_DESCRIPTION);
        return detail;
    }

    private static TransactionDetail toTransactionDetail(SubscriptionDetail obj) {
        TransactionDetail detail = factory.createTransactionDetail();
        DiscountDetail discount = factory.createDiscountDetail();
        discount.setDiscount(convert(obj.getDiscountPercent(), obj.getDiscountAmount()));
        discount.setDiscountCode(obj.getDiscountReasonId());
        discount.setPromotionalOffer(obj.getPromotionalDiscount());
        discount.setDiscountReason(obj.getDiscountReason());
        detail.setDiscountDetail(discount);
        detail.setTotalAmount(obj.getTotalAmount());
        detail.setSaleAmount(obj.getSaleAmount());
        detail.setTaxableAmount(obj.getTaxableAmount());
        detail.setPaidAmount(obj.getSettledAmount());
        detail.setRoundOffValue(obj.getRoundOffAmount());
        detail.setTax(obj.getTaxAmount());
        if (obj.getSubscriptionTaxes() != null) {
            for (SubscriptionTaxDetail tax : obj.getSubscriptionTaxes()) {
                detail.getTaxes().add(convert(tax));
            }
        }
        return detail;
    }

    private static <T extends TaxationDetailDao> TaxDetail convert(T tax) {
        TaxDetail taxDetail = new TaxDetail();
        taxDetail.setCode(tax.getTaxCode());
        taxDetail.setType(tax.getTaxType());
        taxDetail.setValue(tax.getTotalTax());
        taxDetail.setPercentage(tax.getTaxPercentage());
        taxDetail.setTotal(tax.getTotalAmount());
        taxDetail.setTaxable(tax.getTaxableAmount());
        return taxDetail;
    }

    private static PercentageDetail convert(BigDecimal percentage, BigDecimal value) {
        PercentageDetail detail = factory.createPercentageDetail();
        detail.setPercentage(percentage == null ? BigDecimal.ZERO : percentage);
        detail.setValue(value == null ? BigDecimal.ZERO : value);
        return detail;
    }

    private static boolean convert(String yesOrNo) {
        return yesOrNo.toUpperCase().equals(AppConstants.YES);
    }

    public static ReportDef convert(ReportDefinition report) {
        ReportDef def = factory.createReportDef();
        def.setId(report.getReportDefId());
        def.setName(report.getReportName());
        def.setType(report.getReportType());
        def.setMultipleSection(convert(
                AppConstants.getValue(report.getHasMultipleSections(), AppConstants.DEFAULT_HAS_MULTIPLE_SECTION)));
        def.setFrequency(report.getReportFrequency());
        def.setDescription(report.getReportDescription());
        def.setAutoTriggered(
                convert(AppConstants.getValue(report.getAutoTriggered(), AppConstants.DEFAULT_HAS_MULTIPLE_SECTION)));
        for (ReportAttributes attribute : report.getReportAttributeses()) {
            def.getAttributes().add(convert(attribute));
        }
        return def;
    }

    private static ReportAttribute convert(ReportAttributes attribute) {

        ReportAttribute attr = factory.createReportAttribute();
        attr.setId(attribute.getReportAttributesId());
        attr.setName(attribute.getAttributeDefinition().getAttributeName());
        attr.setType(AttributeType.valueOf(attribute.getAttributeDefinition().getAttributeType()));
        attr.setBooleanValue(AppConstants.getValue(attribute.getBooleanValue()));
        attr.setDoubleValue(attribute.getDoubleValue());
        attr.setIntegerValue(attribute.getIntegerValue());
        attr.setCode(attribute.getAttributeDefinition().getAttributeCode());
        attr.setDescription(attribute.getAttributeDefinition().getAttributeDesc());
        return attr;
    }

    public static CustomerOffer convert(CustomerOfferDetail detail) {
        CustomerOffer offer = factory.createCustomerOffer();
        offer.setOfferDetailId(detail.getOfferDetailId());
        offer.setAvailed(AppConstants.getValue(detail.getAvailed()));
        offer.setAvailTime(detail.getAvailTime());
        offer.setCustomerId(detail.getCustomerId());
        offer.setOfferCode(detail.getOfferCode());
        offer.setOrderId(detail.getOrderId());
        return offer;
    }

    public static ListData convertToListData(List<ComplimentaryCode> codes) {
        ListData listData = masterFactory.createListData();
        listData.setDetail(MasterDataConverter.dummyIdCodeName(AppConstants.RTL_CODE_COMPLIMENTARY_CODE));
        if (codes != null) {
            for (ComplimentaryCode code : codes) {
                listData.getContent().add(convert(code));
            }
        }
        return listData;
    }

    public static ComplimentaryReason convert(ComplimentaryCode compCode) {
        ComplimentaryReason idCodeName = new ComplimentaryReason();
        if (compCode != null) {
            idCodeName.setId(compCode.getId());
            idCodeName.setName(compCode.getName());
            idCodeName.setCode(compCode.getCode());
            idCodeName.setType(AppConstants.YES.equals(compCode.getIsAccountable()) ? AppConstants.ACCOUNTABLE
                    : AppConstants.NOT_ACCOUNTABLE);
            idCodeName.setCategory(compCode.getCategory());
        }
        return idCodeName;
    }

    public static DeliveryDetail convert(DeliveryResponse deliveryReponse) {
        DeliveryDetail detail = new DeliveryDetail();
        if (deliveryReponse != null) {
            detail.setDeliveryBoyName(deliveryReponse.getDeliveryBoyName());
            detail.setOrderId(deliveryReponse.getOrderId());
            detail.setGeneratedOrderId(deliveryReponse.getGeneratedOrderId());
            detail.setDeliveryBoyPhoneNum(deliveryReponse.getDeliveryBoyPhoneNum());
            detail.setDeliveryBoyId(deliveryReponse.getDeliveryBoyId());
            detail.setAllotedNo(deliveryReponse.getAllotedNo());
            if (deliveryReponse.getDeliveryDetailId() != null) {
                detail.setId(deliveryReponse.getDeliveryDetailId());
            }
            detail.setStatusUpdateTime(deliveryReponse.getStatusUpdateTime());
            detail.setDeliveryPartnerId(deliveryReponse.getDeliveryPartnerId());
            detail.setDeliveryStatus(DeliveryStatus.get(deliveryReponse.getDeliveryStatus()).toString());
            detail.setDeliveryTaskId(deliveryReponse.getDeliveryTaskId());
        }
        return detail;
    }

    public static DeliveryResponse convert(int unitId, DeliveryDetail deliveryDetail) {
        DeliveryResponse detail = new DeliveryResponse();

        if (deliveryDetail != null) {
            detail.setUnitId(unitId);
            detail.setDeliveryBoyName(deliveryDetail.getDeliveryBoyName());
            detail.setDeliveryBoyPhoneNum(deliveryDetail.getDeliveryBoyPhoneNum());
            detail.setDeliveryBoyId(deliveryDetail.getDeliveryBoyId());
            detail.setDeliveryDetailId(deliveryDetail.getId());
            detail.setOrderId(deliveryDetail.getOrderId());
            detail.setGeneratedOrderId(deliveryDetail.getGeneratedOrderId());
            detail.setDeliveryTaskId(deliveryDetail.getDeliveryTaskId());
            detail.setStatusUpdateTime(deliveryDetail.getStatusUpdateTime());
            detail.setDeliveryStatus(DeliveryStatus.valueOf(deliveryDetail.getDeliveryStatus()).getDeliveryStatus());
            detail.setDeliveryPartnerId(deliveryDetail.getDeliveryPartnerId());
            detail.setAllotedNo(deliveryDetail.getAllotedNo());
        }

        return detail;
    }

    public static OrderItem createDummyOrderItem(Product product, OrderItem parentItem) {
        OrderItem data = factory.createOrderItem();
        data.setProductId(product.getId());
        data.setProductName(product.getName());
        data.setProductAliasName(Objects.nonNull(product.getProductAliasName())?product.getProductAliasName(): product.getName());
        data.setComplimentaryDetail(parentItem.getComplimentaryDetail());
        data.setQuantity(parentItem.getQuantity());
        // TODO This needs proper fix in composite products
        data.setDimension(
                product.getType() == 4 || product.getType() == 5 || product.getType() == 6 || product.getType() == 25
                        ? "R"
                        : "N");
        return data;
    }

    public static ReportStatusEventData convert(ReportStatusEvent source) {
        ReportStatusEventData target = new ReportStatusEventData();
        target.setEventId(source.getId());
        target.setUnitId(source.getUnitId());
        target.setUserId(source.getUserId());
        target.setReportStartTime(source.getStartTime());
        target.setStartOrderId(source.getStartOrderId());
        target.setEndOrderId(source.getEndOrderId());
        target.setTenantId(source.getTenantId());
        target.setTerminalId(source.getTerminalId());
        target.setFileNum(source.getFileNum());
        target.setBusinessDate(source.getBusinessDate());
        target.setEventStatus(source.getEventStatus());
        target.setDescription(source.getDescription());
        return target;
    }

    public static ReportStatusEvent convert(ReportStatusEventData source) {
        ReportStatusEvent target = new ReportStatusEvent();
        target.setId(source.getEventId());
        target.setUnitId(source.getUnitId());
        target.setUserId(source.getUserId());
        target.setStartTime(source.getReportStartTime());
        target.setStartOrderId(source.getStartOrderId());
        target.setEndOrderId(source.getEndOrderId());
        target.setTenantId(source.getTenantId());
        target.setTerminalId(source.getTerminalId());
        target.setFileNum(source.getFileNum());
        target.setBusinessDate(source.getBusinessDate());
        target.setEventStatus(source.getEventStatus());
        target.setDescription(source.getDescription());
        return target;
    }

    public static PullPacket convert(PullDetail pullDetail, int createdBy, String wittness, MasterDataCache cache)
            throws DataNotFoundException {
        PullPacket pullPacket = new PullPacket();
        pullPacket.setPullPacketId(pullDetail.getId());
        pullPacket.setComment(pullDetail.getComment());
        pullPacket.setClosurePaymentDetail(convert(pullDetail.getClosurePaymentDetails()));
        pullPacket.setCreatedBy(createdBy);
        pullPacket.setCreatedByName(cache.getEmployee(createdBy));
        pullPacket.setWitnessedBy(wittness);
        pullPacket.setPaymentMode(cache.getPaymentMode(pullDetail.getPaymentModeId()));
        pullPacket.setPullAmount(pullDetail.getPullAmount());
        pullPacket.setPullDate(pullDetail.getPullDate());
        pullPacket.setPullPacketStatus(PullPacketStatus.fromValue(pullDetail.getStatus()));
        pullPacket.setPullPacketUnit(cache.getUnitBasicDetail(pullDetail.getUnitId()));
        pullPacket.setPullPendingReason(pullDetail.getPendingReason());
        /*
         * PullSettlementDetail psd = pullDetail.getSettlementDetail() == null ? null :
         * convert(pullDetail.getSettlementDetail(), cache);
         */
        pullPacket.setPullSettlementDetail(null);
        pullPacket.setPullSource(pullDetail.getSource());
        if (pullPacket.getPullPacketStatus() != PullPacketStatus.INITIATED) {
            pullPacket.getPullDenominations().addAll(convert(cache, pullDetail.getPullDenominations()));
        } else {
            int paymentModeId = pullDetail.getPaymentModeId();
            pullPacket.getPullDenominations().addAll(convert(cache.getPaymentMode(paymentModeId).getDenominations(), paymentModeId));
            }
        return pullPacket;
    }

    public static List<PullPacketDenomination> convert(List<DenominationDetail> denoms, Integer paymentModeId) {
        List<PullPacketDenomination> pullPacketDenominations = new ArrayList<>();
        if (denoms != null) {
            for (DenominationDetail dd : denoms) {
                PullPacketDenomination pullPacketDenomination = new PullPacketDenomination();
                pullPacketDenomination.setDenominationDetail(dd);
                pullPacketDenominations.add(pullPacketDenomination);
            }
        }
        return pullPacketDenominations;
    }

    public static List<PullSettlementDenomination> convertSettlementDenomination(List<DenominationDetail> denoms) {
        List<PullSettlementDenomination> pullSettlementDenominations = new ArrayList<>();
        if (denoms != null && denoms.size() > 0) {
            for (DenominationDetail dd : denoms) {
                PullSettlementDenomination pullSettlementDenomination = new PullSettlementDenomination();
                pullSettlementDenomination.setDenominationDetail(dd);
                pullSettlementDenominations.add(pullSettlementDenomination);
            }
        }
        return pullSettlementDenominations;
    }

    public static PullPacket convert(PullDetail pullDetail, int createdBy, String createdByName, String wittness,
                                     MasterDataCache cache, List<PullDenomination> denoms) throws DataNotFoundException {
        PullPacket pullPacket = new PullPacket();
        pullPacket.setPullPacketId(pullDetail.getId());
        pullPacket.setComment(pullDetail.getComment());
        pullPacket.setClosurePaymentDetail(convert(pullDetail.getClosurePaymentDetails()));
        pullPacket.setCreatedBy(createdBy);
        pullPacket.setCreatedByName(createdByName);
        pullPacket.setWitnessedBy(wittness);
        pullPacket.setPaymentMode(cache.getPaymentMode(pullDetail.getPaymentModeId()));
        pullPacket.setPullAmount(pullDetail.getPullAmount());
        pullPacket.setPullDate(pullDetail.getPullDate());
        pullPacket.setPullPacketStatus(PullPacketStatus.fromValue(pullDetail.getStatus()));
        pullPacket.setPullPacketUnit(cache.getUnitBasicDetail(pullDetail.getUnitId()));
        pullPacket.setPullPendingReason(pullDetail.getPendingReason());
        /*
         * PullSettlementDetail psd = pullDetail.getSettlementDetail() == null ? null :
         * convert(pullDetail.getSettlementDetail(), cache);
         */
        pullPacket.setPullSettlementDetail(null);
        pullPacket.setPullSource(pullDetail.getSource());
        pullPacket.getPullDenominations().addAll(convert(cache, denoms));
        return pullPacket;
    }

    public static List<PullPacketDenomination> convert(MasterDataCache cache, List<PullDenomination> pdlist) {
        List<PullPacketDenomination> ppdmList = new ArrayList<>();
        for (PullDenomination pd : pdlist) {
            PullPacketDenomination ppdm = new PullPacketDenomination();
            ppdm.setDenominationDetail(cache.getDenominationDetail(pd.getDenominationId()));
            ppdm.setLooseCurrencyCount(pd.getLooseCurrencyCount());
            ppdm.setPacketCount(pd.getPacketCount());
            ppdm.setPullDenominationId(pd.getId());
            ppdm.setTotalAmount(pd.getTotalAmount());
            ppdmList.add(ppdm);
        }

        return ppdmList;
    }
    public static void convertPullDenominations(MasterDataCache cache, List<PullDenomination> pdlist,List<PullPacketDenomination> pullPacketDenominations){
        for (PullDenomination pd : pdlist) {
            PullPacketDenomination ppdm = new PullPacketDenomination();
            ppdm.setDenominationDetail(cache.getDenominationDetail(pd.getDenominationId()));
            ppdm.setLooseCurrencyCount(pd.getLooseCurrencyCount());
            ppdm.setPacketCount(pd.getPacketCount());
            ppdm.setPullDenominationId(pd.getId());
            ppdm.setTotalAmount(pd.getTotalAmount());
            pullPacketDenominations.add(ppdm);
        }
        return;
    }

    public static ClosurePaymentDetail convert(ClosurePaymentDetails cpds) {
        ClosurePaymentDetail cpd = new ClosurePaymentDetail();
        cpd.setActualAmount(cpds.getActualAmount());
        cpd.setBillCount(cpds.getBillCount());
        cpd.setClosureId(cpds.getUnitClosureDetails().getClosureId());
        cpd.setDiscount(cpds.getDiscount());
        cpd.setExpectedAmount(cpds.getExpectedAmount());
        cpd.setGmv(cpds.getGmv());
        cpd.setId(cpds.getPaymentClosureId());
        cpd.setNetSalesAmount(cpds.getNetSalesAmount());
        cpd.setPaymentModeId(cpds.getPaymentModeId());
        cpd.setRoundOff(cpds.getRoundOff());
        cpd.setTotalAmount(cpds.getTotalAmount());
        cpd.setTax(cpds.getTotalTax());
        for (ClosurePaymentTaxDetails tax : cpds.getClosureTaxes()) {
            cpd.getTaxes().add(convert(tax));
        }
        return cpd;
    }

    public static PullSettlementDetail convert(SettlementDetail settlementDetail, MasterDataCache cache,
                                               boolean fetchPulls, boolean fetchDenominations)
            throws DataNotFoundException, DatatypeConfigurationException {
        PullSettlementDetail psd = new PullSettlementDetail();
        psd.setClosingAmount(settlementDetail.getClosingAmount());
        psd.setId(settlementDetail.getId());
        psd.setSettlementAmount(settlementDetail.getSettlementAmount());
        psd.setOriginalAmount(settlementDetail.getOriginalAmount());
        psd.setExtraAmount(settlementDetail.getExtraAmount());
        psd.setSettlementClosingReceipt(settlementDetail.getSettlementClosingReceipt());
        psd.setSettlementReceiptPath(settlementDetail.getSettlementReceiptPath());
        psd.setSettlementServiceProvider(settlementDetail.getSettlementServiceProvider());
        psd.setSettlementServiceProviderReceipt(settlementDetail.getSettlementServiceProviderReceipt());
        psd.setSettlementStatus(SettlementStatus.fromValue(settlementDetail.getSettlementStatus()));
        GregorianCalendar cal = new GregorianCalendar();
        cal.setTime(settlementDetail.getTime());
        XMLGregorianCalendar date = DatatypeFactory.newInstance().newXMLGregorianCalendar(cal);
        psd.setSettlementTime(date);
        psd.setSettlementType(cache.getPaymentMode(settlementDetail.getSettlementTypeId()));
        UnitBasicDetail ubd = cache.getUnitBasicDetail(settlementDetail.getUnitId());
        psd.setSettlementUnit(ubd);
        psd.setTotalAmount(settlementDetail.getTotalAmount());
        psd.setUnsettledAmount(settlementDetail.getUnsettledAmount());
        psd.setPaymentMode(cache.getPaymentMode(settlementDetail.getSettlementTypeId()));
        List<PullPacket> pps = new ArrayList<>();
        if (fetchPulls) {
            for (PullDetail pd : settlementDetail.getPullDetails()) {
                PullPacket pp = convert(pd, pd.getCreatedBy(), cache.getEmployee(pd.getCreatedBy()),
                        pd.getWitnessedBy(), cache, pd.getPullDenominations());
                pps.add(pp);
            }
        }
        psd.getPullDetails().addAll(pps);
        if (fetchDenominations) {
            psd.getSettlementDenominations()
                    .addAll(convertSettlementDenoms(cache, settlementDetail.getSettlementDenominations()));
        }
        return psd;
    }

    public static List<PullSettlementDenomination> convertSettlementDenoms(MasterDataCache cache,
                                                                           List<SettlementDenomination> sdlist) {
        List<PullSettlementDenomination> psdmList = new ArrayList<>();
        for (SettlementDenomination sd : sdlist) {
            PullSettlementDenomination ppdm = new PullSettlementDenomination();
            ppdm.setDenominationDetail(cache.getDenominationDetail(sd.getDenominationId()));
            ppdm.setLooseCurrencyCount(sd.getLooseCurrencyCount());
            ppdm.setPacketCount(sd.getPacketCount());
            ppdm.setSettlementDenominationId(sd.getId());
            ppdm.setTotalAmount(sd.getTotalAmount());
            psdmList.add(ppdm);
        }
        return psdmList;
    }

    public static UnitExpense convert(UnitExpenseDetail expenseDetail) {
        UnitExpense expense = factory.createUnitExpense();
        // Event Fields
        expense.setType(expenseDetail.getType());
        expense.setYear(expenseDetail.getYear());
        expense.setUpdatedBy(expenseDetail.getUpdatedBy());
        expense.setIterationNumber(expenseDetail.getIterationNumber());
        expense.setExpenseUpdateEventId(expenseDetail.getExpenseUpdateEventId());

        // Expense Fields
        expense.setAmexCardCharges(expenseDetail.getAmexCardCharges());
        expense.setCamCharges(expenseDetail.getCamCharges());
        expense.setCleaningCharges(expenseDetail.getCleaningCharges());
        expense.setChangeCommission(expenseDetail.getChangeCommission());
        expense.setChannelPartnerCharges(expenseDetail.getChannelPartnerCharges());
        expense.setChargesDG(expenseDetail.getChargesDG());
        expense.setCogs(expenseDetail.getCogs());
        expense.setComments(expenseDetail.getComments());
        expense.setConsumablesAndUtilities(expenseDetail.getConsumablesAndUtilities());
        expense.setConsumablesCutlery(expenseDetail.getConsumablesCutlery());
        expense.setConsumablesEquipment(expenseDetail.getConsumablesEquipment());
        expense.setConsumablesStationary(expenseDetail.getConsumablesStationary());
        expense.setConsumablesUniform(expenseDetail.getConsumablesUniform());
        expense.setConvenyance(expenseDetail.getConvenyance());
        expense.setCourier(expenseDetail.getCourier());
        expense.setCreditCardCharges(expenseDetail.getCreditCardCharges());
        expense.setCustomerCareCost(expenseDetail.getCustomerCareCost());
        expense.setDeliveryCost(expenseDetail.getDeliveryCost());
        expense.setEdcMachine(expenseDetail.getEdcMachine());
        expense.setElectricity(expenseDetail.getElectricity());
        expense.setEmployeeMeal(expenseDetail.getEmployeeMeal());
        expense.setFixedRent(expenseDetail.getFixedRent());
        expense.setFreightOutward(expenseDetail.getFreightOutward());
        expense.setGmvAmount(expenseDetail.getGmvAmount());
        expense.setInsurance(expenseDetail.getInsurance());
        expense.setInternet(expenseDetail.getInternet());
        expense.setItTeamCost(expenseDetail.getItTeamCost());
        expense.setKitchenCostTotal(expenseDetail.getKitchenCostTotal());
        expense.setKitchenCostPercentage(expenseDetail.getKitchenCostPercentage());
        expense.setManpower(expenseDetail.getManpower());
        expense.setMarketingAndSampling(expenseDetail.getMarketingAndSampling());
        expense.setUnsatifiedCustomerCost(expenseDetail.getUnsatifiedCustomerCost());
        expense.setPPECost(expenseDetail.getPPECost());
        expense.setManualAdjustments(expenseDetail.getManualAdjustments());
        expense.setMiscExp(expenseDetail.getMiscExp());
        expense.setMsp(expenseDetail.getMsp());
        expense.setNetSalesAmount(expenseDetail.getNetSalesAmount());
        expense.setNetTickets(expenseDetail.getNetTickets());
        expense.setNewspaper(expenseDetail.getNewspaper());
        expense.setOpsCostTotal(expenseDetail.getOpsCostTotal());
        expense.setOpsCostPercentage(expenseDetail.getOpsCostPrecentage());
        expense.setParkingCharges(expenseDetail.getParkingCharges());
        expense.setPrintingAndStationery(expenseDetail.getPrintingAndStationery());
        expense.setRent(expenseDetail.getRent());
        expense.setRentDG(expenseDetail.getRentDG());
        expense.setRentPercentage(expenseDetail.getRentPercentage());
        expense.setRepairAndMaintenanceMajor(expenseDetail.getRepairAndMaintenanceMajor());
        expense.setRepairAndMaintenanceMinor(expenseDetail.getRepairAndMaintenanceMinor());
        expense.setMaintenanceTeamCost(expenseDetail.getMaintenanceTeamCost());
        expense.setNetSalesAmount(expenseDetail.getNetSalesAmount());
        expense.setScmRental(expenseDetail.getScmRental());
        expense.setSodexoCharges(expenseDetail.getSodexoCharges());
        expense.setStaffWelfare(expenseDetail.getStaffWelfare());
        expense.setSystemRent(expenseDetail.getSystemRent());
        expense.setTelephone(expenseDetail.getTelephone());
        expense.setTotalTickets(expenseDetail.getTotalTickets());
        expense.setTrainingTeamCost(expenseDetail.getTrainingTeamCost());
        expense.setTktRestaurantCharges(expenseDetail.getTktRestaurantCharges());
        expense.setUnitId(expenseDetail.getUnitId());
        expense.setUnitName(expenseDetail.getUnitName());
        expense.setWastageAndExpired(expenseDetail.getWastageAndExpired());
        expense.setWater(expenseDetail.getWater());
        expense.setLastUpdateTime(expenseDetail.getLastUpdateTime());
        expense.setTotalCost(expenseDetail.getTotalCost());
        expense.setEbitdaPercentage(expenseDetail.getEbitdaPercentage());

        expense.setPaytmCharges(expenseDetail.getPaytmCharges());
        expense.setMobikwikCharges(expenseDetail.getMobikwikCharges());
        expense.setFreeChargeCharges(expenseDetail.getFreeChargeCharges());
        expense.setDeliveryTotalTickets(expenseDetail.getDeliveryTotalTickets());
        expense.setDeliveryNetTickets(expenseDetail.getDeliveryNetTickets());
        expense.setDeliveryGMV(expenseDetail.getDeliveryGMV());
        expense.setDeliverySales(expenseDetail.getDeliverySales());
        expense.setDeliveryCOGS(expenseDetail.getDeliveryCOGS());
        expense.setDeliveryUnsatisfiedCustomerCost(expenseDetail.getDeliveryUnsatisfiedCustomerCost());
        expense.setDeliveryPPECost(expenseDetail.getDeliveryPPECost());
        expense.setDeliverySampleingAndMarketingCost(expenseDetail.getDeliverySampleingAndMarketingCost());
        expense.setDeliveryPaytmCharges(expenseDetail.getDeliveryPaytmCahrges());

        return expense;
    }

    public static ExpenseUpdateEvent convert(ExpenseUpdateEventData data) {
        ExpenseUpdateEvent event = factory.createExpenseUpdateEvent();
        event.setEventId(data.getEventId());
        event.setUpdatedByUserId(data.getUpdatedByUserId());
        event.setAddedByUserId(data.getAddedByUserId());
        event.setEventTimestamp(data.getEventTimestamp());
        event.setDescription(data.getEventDescription());
        event.setErrorMessage(data.getErrorMessage());
        event.setYear(data.getYear());
        event.setInputFileName(data.getInputFileName());
        event.setIterationNumber(data.getIterationNumber());
        event.setNoOfRows(data.getNoOfRows());
        event.setStatus(data.getStatus());
        event.setStoredFileName(data.getStoredFileName());
        event.setType(IterationType.valueOf(data.getType()));
        return event;
    }

    public static UnitClosure convert(UnitClosureDetails ucd) {
        UnitClosure uc = factory.createUnitClosure();
        uc.setBusinessDate(ucd.getBusinessDate());
        uc.setComment(ucd.getClosureComment());
        uc.setUnitId(ucd.getUnitId());
        uc.setStartOrderId(ucd.getStartOrderId());
        uc.setLastOrderId(ucd.getLastOrderId());
        uc.setEmployeeId(ucd.getEmployeeId());
        uc.setId(ucd.getClosureId());
        uc.setPnlGenerated(AppConstants.getValue(ucd.getPnlGenerated()));
        uc.setPnlInstanceId(ucd.getPnlInstanceId());
        uc.setStartTime(ucd.getClosureStartTime());
        return uc;
    }

    public static FeedbackEventInfo convert(MasterDataCache cache, FeedbackEvent event) {
        FeedbackEventInfo info = new FeedbackEventInfo();
        info.setContactNumber(event.getFeedbackDetail().getContactNumber());
        info.setCustomerId(event.getFeedbackDetail().getCustomerId());
        info.setEmailId(event.getFeedbackDetail().getEmailId());
        info.setEventCompletionTime(event.getEventCompletionTime());
        info.setEventGenerationTime(event.getEventGenerationTime());
        info.setEventLongUrl(event.getEventLongUrl());
        info.setEventNotificationTime(event.getEventNotificationTime());
        info.setEventShortUrl(event.getEventShortUrl());
        info.setEventShortUrlId(event.getEventShortUrlId());
        info.setEventSource(event.getEventSource());
        info.setEventStatus(event.getEventStatus());
        info.setEventTriggerTime(event.getEventTriggerTime());
        info.setFeedbackEventId(event.getFeedbackEventId());
        info.setFeedbackId(event.getFeedbackDetail().getFeedbackId());
        info.setOrderSource(event.getFeedbackDetail().getOrderSource());
        info.setUnitId(event.getFeedbackDetail().getUnitId());
        info.setCustomerName(event.getFeedbackDetail().getCustomerName());
        info.setUnitName(cache.getUnitBasicDetail(event.getFeedbackDetail().getUnitId()).getName());
        info.setType(FeedbackEventType.valueOf(event.getEventType()));
        info.setRating(event.getFeedbackDetail().getRating());
        info.setBrand(cache.getBrandMetaData().get(event.getBrandId()));
        return info;
    }

    public static IdCodeName convert(CreditAccountDetail detail) {
        IdCodeName creditAccount = new IdCodeName();
        creditAccount.setId(detail.getCreditAccountDetailId());
        creditAccount.setCode(detail.getLegalName());
        creditAccount.setName(detail.getDisplayName());
        return creditAccount;
    }

    public static OrderPayment convert(OrderPaymentDetail paymentDetail, PaymentMode paymentMode) {
        OrderPayment orderPayment = new OrderPayment();
        if (paymentDetail.getPaymentStatus() != null) {
            orderPayment.setPaymentStatus(PaymentStatus.valueOf(paymentDetail.getPaymentStatus()));
        }
        if (paymentDetail.getRefundStatus() != null) {
            orderPayment.setRefundStatus(PaymentStatus.valueOf(paymentDetail.getRefundStatus()));
        }
        orderPayment.setPaymentPartner(paymentMode.getName());
        orderPayment.setRefundRequested(paymentDetail.getRefundRequested());
        orderPayment.setTransactionAmount(paymentDetail.getTransactionAmount());
        orderPayment.setContactNumber(paymentDetail.getContactNumber());
        orderPayment.setCustomerId(paymentDetail.getCustomerId());
        orderPayment.setCustomerName(paymentDetail.getCustomerName());
        orderPayment.setCartId(paymentDetail.getCartId());
        orderPayment.setExternalOrderId(paymentDetail.getExternalOrderId());
        orderPayment.setOrderId(paymentDetail.getOrderId());
        orderPayment.setOrderPaymentDetailId(paymentDetail.getOrderPaymentDetailId());
        orderPayment.setFailureReason(paymentDetail.getFailureReason());
        orderPayment.setOrderSettlementId(paymentDetail.getOrderSettlementId());
        orderPayment.setPartnerTransactionId(paymentDetail.getPartnerTransactionId());
        orderPayment.setPaymentModeId(paymentDetail.getPaymentModeId());
        orderPayment.setPaymentSource(paymentDetail.getPaymentSource());
        orderPayment.setRefundId(paymentDetail.getRefundId());
        orderPayment.setPaymentProcessTime(paymentDetail.getRequestTime());
        orderPayment.setPartnerOrderId(paymentDetail.getPartnerOrderId());
        orderPayment.setPaymentModeName(paymentDetail.getPaymentModeName());
        orderPayment.setMerchantId(paymentDetail.getMerchantId());
        if(paymentDetail.getBrandId() != null) {
            orderPayment.setBrandId(paymentDetail.getBrandId());
        }
        return orderPayment;
    }

    public static ManualBillBook convert(ManualBillBookData mbd, int usedBillsCount) {
        ManualBillBook mb = new ManualBillBook();
        mb.setId(mbd.getBillBookId());
        mb.setStartNo(mbd.getStartNo());
        mb.setEndNo(mbd.getEndNo());
        mb.setStatus(mbd.getStatus());
        IdCodeName idCodeName = new IdCodeName();
        idCodeName.setId(mbd.getUnitId());
        mb.setGeneratedForUnitId(idCodeName);
        mb.setActivationTime(mbd.getActivationTime());
        mb.setCreationTime(mbd.getCreationTime());
        mb.setUsedBillCount(usedBillsCount);
        return mb;
    }

    /**
     * @param order
     * @param masterCache
     * @return
     */
    public static BillBookOrder convert(OrderDetail order, MasterDataCache masterCache) {
        BillBookOrder bill = new BillBookOrder();
        bill.setBookNo(order.getManualBillBookNo());
        bill.setEmployee(masterCache.getEmployee(order.getEmpId()));
        bill.setId(order.getGeneratedOrderId());
        bill.setSettlement(convertToString(order.getOrderSettlements(), masterCache));
        bill.setStatus(order.getOrderStatus());
        bill.setTaxable(order.getTaxableAmount());
        bill.setTaxes(order.getTaxAmount());
        bill.setTime(AppUtils.getFormattedTime(order.getBillingServerTime()));
        bill.setTotal(order.getTotalAmount());
        bill.setPaid(order.getSettledAmount());
        return bill;
    }

    /**
     * @param orderSettlements
     * @return
     */
    private static String convertToString(List<OrderSettlement> orderSettlements, MasterDataCache masterCache) {
        StringBuffer buffer = new StringBuffer();
        for (OrderSettlement settlement : orderSettlements) {
            buffer.append("[");
            buffer.append(masterCache.getPaymentMode(settlement.getPaymentModeId()).getName());
            buffer.append(":");
            buffer.append(settlement.getAmountPaid());
            buffer.append("]|");
        }
        return buffer.toString();
    }

    public static OrderStatusDomain convert(OrderStatusEvent statusEvent) {
        OrderStatusDomain statusDomain = new OrderStatusDomain();
        statusDomain.setOrderStatusId(statusEvent.getOrderStatusId());
        statusDomain.setOrderId(statusEvent.getOrderId());
        statusDomain.setFromStatus(statusEvent.getFromStatus());
        statusDomain.setToStatus(statusEvent.getToStatus());
        if (statusEvent.getReasonText() != null) {
            statusDomain.setReasonText(statusEvent.getReasonText());
        }
        statusDomain.setStartTime(statusEvent.getStartTime());
        if (statusEvent.getUpdateTime() != null) {
            statusDomain.setUpdateTime(statusEvent.getUpdateTime());
            int seconds = AppUtils.getSecondsDiff(statusEvent.getStartTime(), statusEvent.getUpdateTime());
            statusDomain.setElapsedTime(seconds / 60);
        }
        statusDomain.setTransitionStatus(statusEvent.getTransitionStatus());
        if (statusEvent.getErrorTrace() != null) {
            statusDomain.setErrorTrace(statusEvent.getErrorTrace());
        }
        return statusDomain;
    }

    public static ExpenseDetail convert(ExpenseDetailData data, MasterDataCache cache) {
        ExpenseDetail detail = new ExpenseDetail();
        detail.setId(data.getId());
        detail.setUnitId(new IdCodeName(data.getUnitId(), cache.getUnit(data.getUnitId()).getName(), ""));
        detail.setExpenseCategory(data.getExpenseCategory());
        detail.setExpenseType(data.getExpenseType());
        detail.setExpenseTypeId(data.getExpenseTypeId());
        detail.setBudgetCategory(data.getBudgetCategory());
        detail.setAccountableInPnL(AppUtils.getStatus(data.getAccountableInPnL()));
        detail.setAmount(data.getAmount());
        if (data.getComment() != null) {
            detail.setComment(data.getComment());
        }
        detail.setStatus(ExpenseStatus.valueOf(data.getStatus()));

        IdCodeName codeName = new IdCodeName();
        codeName.setId(data.getCreatedBy());
        codeName.setName(cache.getEmployee(data.getCreatedBy()));
        detail.setCreatedBy(codeName);

        detail.setCreatedOn(data.getCreatedOn());
        detail.setStatus(ExpenseStatus.valueOf(data.getStatus()));
        if (AppUtils.getDaysDiff(detail.getCreatedOn(), AppUtils.getCurrentTimestamp()) < 4) {
            detail.setCancel(true);
        }
        if (data.getCancelledBy() != null) {
            codeName.setId(data.getCancelledBy());
            codeName.setName(cache.getEmployee(data.getCancelledBy()));
            detail.setCancelledBy(codeName);
        }

        if (data.getCancelledOn() != null) {
            detail.setCancelledOn(data.getCancelledOn());
        }

        if (data.getCancellationReason() != null) {
            detail.setCancellationReason(data.getCancellationReason());
        }
        return detail;
    }

    public static MeterDetailsData convert(MeterDetail detail, Date createdOn) {
        MeterDetailsData data = new MeterDetailsData();
        data.setUnitId(detail.getUnitId());
        data.setBillType(detail.getBillType());
        data.setMeterNo(detail.getMeterNo());
        data.setCurrentUnit(detail.getCurrentReading());
        data.setEntryType(detail.getEntryType().name());
        data.setStatus(ExpenseStatus.ACTIVE.name());
        data.setCreatedOn(createdOn);
        data.setCreatedBy(detail.getCreatedBy().getId());
        data.setBusinessDate(AppUtils.getCurrentBusinessDate());
        return data;
    }

    public static MeterDetail convert(MeterDetailsData data, MasterDataCache masterCache) {
        MeterDetail detail = new MeterDetail();
        detail.setId(data.getId());
        detail.setUnitId(data.getUnitId());
        detail.setBillType(data.getBillType());
        detail.setMeterNo(data.getMeterNo());
        detail.setCurrentReading(data.getCurrentUnit());
        detail.setEntryType(MeterDetailEntryType.valueOf(data.getEntryType()));
        detail.setCalculationIndex(CalculationIndexStatus.valueOf(data.getCalculationIndex()));
        detail.setStatus(ExpenseStatus.valueOf(data.getStatus()));
        detail.setCreatedOn(data.getCreatedOn());
        IdCodeName codeName = new IdCodeName();
        codeName.setId(data.getCreatedBy());
        codeName.setName(masterCache.getEmployee(data.getCreatedBy()));
        detail.setCreatedBy(codeName);
        if (data.getCancelledBy() != null) {
            codeName.setId(data.getCancelledBy());
            codeName.setName(masterCache.getEmployee(data.getCancelledBy()));
            detail.setCancelledBy(codeName);
        }

        if (data.getCancelledOn() != null) {
            detail.setCancelledOn(data.getCancelledOn());
        }

        if (data.getUpdatedBy() != null) {
            detail.setUpdatedBy(new IdCodeName(data.getUpdatedBy(), masterCache.getEmployee(data.getUpdatedBy()), ""));
        }
        if (data.getUpdatedOn() != null) {
            detail.setUpdatedOn(data.getUpdatedOn());
        }
        return detail;
    }

    public static UnitBudgetExceeded convert(UnitBudgetExceededData data, MasterDataCache cache) {
        UnitBudgetExceeded detail = new UnitBudgetExceeded();
        detail.setId(data.getId());
        detail.setUnitId(new IdCodeName(data.getUnitId(), cache.getUnit(data.getUnitId()).getName(), ""));
        detail.setExpenseLabel(data.getExpenseLabel());
        detail.setExpenseType(data.getExpenseType());
        detail.setBudgetCategory(data.getBudgetCategory());
        detail.setExpenseSource(data.getExpenseSource());
        detail.setNotificationType(data.getNotificationType());
        detail.setBudgetAmount(data.getBudgetAmount());
        detail.setRequestedAmount(data.getRequestedAmount());
        detail.setCurrentAmount(data.getCurrentAmount());
        detail.setCreatedOn(data.getCreatedOn());
        detail.setCreatedBy(new IdCodeName(data.getCreatedBy(), cache.getEmployee(data.getCreatedBy()), ""));
        return detail;
    }

    public static ExternalPartnerDetail convert(
            com.stpl.tech.master.data.model.ExternalPartnerDetail externalPartnerDetail,
            MasterDataCache masterDataCache, RecipeCache recipeCache) {
        ExternalPartnerDetail detail = new ExternalPartnerDetail();
        detail.setId(externalPartnerDetail.getId());
        detail.setPartnerName(externalPartnerDetail.getPartnerName());
        detail.setPartnerCode(externalPartnerDetail.getPartnerCode());
        detail.setLinkedProduct(masterDataCache.getProduct(externalPartnerDetail.getLinkedProductId()));
        detail.setLinkedPaymentMode(masterDataCache.getPaymentMode(externalPartnerDetail.getLinkedPaymentModeId()));
        RecipeDetail recipeDetail = recipeCache.getRecipe(externalPartnerDetail.getLinkedProductId(),
                AppConstants.NO_DIMENSION_STRING, AppConstants.DEFAULT_RECIPE_PROFILE);

        detail.setRecipeId(recipeDetail == null ? 0 : recipeDetail.getRecipeId());
        return detail;
    }

    public static UnitTableMapping convert(UnitTableMappingDetail data) {
        UnitTableMapping table = new UnitTableMapping(data.getTableRequestId(), data.getUnitId(), data.getTableNumber(),
                data.getCustomerId(), data.getCustomerName(), data.getTotalOrders(), data.getTotalAmount(),
                TableStatus.OCCUPIED, data.getContact());
        if (data.getOrders() != null) {
            for (TableOrderMappingDetail map : data.getOrders()) {
                table.getOrders().add(convert(map));
            }
        }
        return table;
    }


    public static TableOrder convert(TableOrderMappingDetail map) {
        TableOrder order = new TableOrder();
        OrderDetail od = map.getOrder();
        order.setBillingServerTime(od.getBillingServerTime());
        order.setCustomerName(od.getCustomerName());
        order.setGenerateOrderId(od.getGeneratedOrderId());
        order.setOfferCode(od.getOfferCode());
        order.setOrderId(od.getOrderId());
        order.setSource(od.getOrderSource());
        order.setStatus(OrderStatus.valueOf(od.getOrderStatus()));
        order.setTransactionDetail(toTransactionDetail(od, false));
        for (com.stpl.tech.kettle.data.model.OrderItem item : od.getOrderItems()) {
            if (AppUtils.isGiftCard(item.getTaxCode())) {
                order.getItems().add(convert(item));
            }
        }
        return order;
    }

    private static TableOrderItem convert(com.stpl.tech.kettle.data.model.OrderItem item) {
        TableOrderItem toi = new TableOrderItem();
        toi.setDimension(item.getDimension());
        toi.setItemCode(item.getTaxCode());
        toi.setPrice(item.getPrice());
        toi.setProductId(item.getProductId());
        toi.setProductName(item.getProductName());
        toi.setQuantity(item.getQuantity());
        toi.setTaxAmount(item.getTaxAmount());
        toi.setTotalAmount(item.getTotalAmount());
        return toi;
    }

    public static TATSummary convert(AssemblyTATData tatData) {
        TATSummary tat = new TATSummary();
        tat.setHot(tatData.getHotPrepTime());
        tat.setCold(tatData.getColdPrepTime());
        tat.setFood(tatData.getFoodPrepTime());
        tat.setDch(tatData.getDispatchTime());
        tat.setOrderTime(tatData.getOrderPrepTime());
        return tat;
    }


    public static OrderNPS convertOrderNPS(OrderNPSDetail orderNPSDetail) {

        OrderNPS orderNPS = new OrderNPS();
        orderNPS.setCustomerId(orderNPSDetail.getCustomerId());
        orderNPS.setFeedbackTime(orderNPSDetail.getSurveyCreationTime());
        orderNPS.setFeedbackType("NPS");
        orderNPS.setOrderId(orderNPSDetail.getOrderId());
        orderNPS.setQuestion(orderNPSDetail.getQuestion());
        orderNPS.setResponse(orderNPSDetail.getResponse());
        orderNPS.setRating(orderNPSDetail.getRating());
        orderNPS.setUnitId(orderNPSDetail.getUnitId());
        orderNPS.setUnitName(orderNPSDetail.getUnitName());
        List<OrderNPSResponse> questions = new ArrayList<>();
        if (orderNPSDetail.getQuestions() != null) {
            for (OrderNPSResponseData data : orderNPSDetail.getQuestions()) {
                questions.add(convert(data));
            }
        }
        orderNPS.setQuestions(questions);
        return orderNPS;
    }

    private static OrderNPSResponse convert(OrderNPSResponseData data) {
        OrderNPSResponse orderNPSResponse = new OrderNPSResponse();
        orderNPSResponse.setId(data.getResponseId());
        orderNPSResponse.setQuestion(data.getQuestion());
        orderNPSResponse.setResponse(data.getResponse());
        return orderNPSResponse;
    }

    public static List<ReferentInfo> convert(List<ReferralMappingData> referralMappings,
                                             List<LoyaltyScore> referentLoyaltyScores) {
        List<ReferentInfo> referents = new ArrayList<>();
        if (AppUtils.isNonEmptyList(referralMappings)) {
            referralMappings.forEach(referralMappingData -> {
                ReferentInfo referentInfo = factory.createReferentInfo();
                referentInfo.setName(referralMappingData.getReferentName());
                referentInfo.setStatus(referralMappingData.getReferralStatus());
                referentInfo.setContact(referralMappingData.getContactNumber());
                referentInfo.setCreationTime(referralMappingData.getCreationTime());
                //The default bonus received if referent has signed up and placed first order.
                referentInfo.setDefaultBonus(300);
                referentLoyaltyScores.forEach(loyaltyScore -> {
                    //LOG.info("loyaltyScore :::" + JSONSerializer.toJSON(loyaltyScore));
                    if (loyaltyScore != null
                            && referralMappingData.getReferentId() != null
                            && referralMappingData.getReferentId().equals(loyaltyScore.getCustomerId())
                            && loyaltyScore.getLastOrderId() != null) {
                        referentInfo.setFirstOrderPlaced(Boolean.TRUE);
                        referentInfo.setLastOrderTime(loyaltyScore.getLastOrderTime());
                        referentInfo.setAcquiredPoints(loyaltyScore.getAcquiredPoints());
                    }
                });
                //LOG.info("referentInfo :::" + JSONSerializer.toJSON(referentInfo));
                referents.add(referentInfo);
            });
        }
        return referents;
    }

    public static PnlAdjustment convert(PnlAdjustmentDetail pnlAdjustmentDetail) {
        PnlAdjustment p = new PnlAdjustment();
        p.setAdjustmentId(pnlAdjustmentDetail.getAdjustmentId());
        p.setAdjustmentType(pnlAdjustmentDetail.getAdjustmentType());
        p.setAdjustmentValue(pnlAdjustmentDetail.getAdjustmentValue());
        p.setMonth(pnlAdjustmentDetail.getMonth());
        p.setYear(pnlAdjustmentDetail.getYear());
        p.setStatus(pnlAdjustmentDetail.getStatus());
        p.setUnitId(pnlAdjustmentDetail.getUnitId());
        p.setPnlHeaderName(pnlAdjustmentDetail.getPnlHeaderName());
        p.setPnlHeaderColumnName(pnlAdjustmentDetail.getPnlHeaderColumnName());
        p.setPnlHeaderDetail(pnlAdjustmentDetail.getPnlHeaderDetail());
        p.setPnlHeaderType(pnlAdjustmentDetail.getPnlHeaderType());
        p.setCreatedBy(pnlAdjustmentDetail.getCreatedBy());
        p.setCreationTime(pnlAdjustmentDetail.getCreationTime());
        p.setCreateComment(pnlAdjustmentDetail.getCreateComment());
        p.setCreateCommentText(pnlAdjustmentDetail.getCreateCommentText());
        p.setRejectedBy(pnlAdjustmentDetail.getRejectedBy());
        p.setRejectionTime(pnlAdjustmentDetail.getRejectionTime());
        p.setRejectComment(pnlAdjustmentDetail.getRejectComment());
        p.setRejectCommentText(pnlAdjustmentDetail.getRejectCommentText());
        p.setApprovedBy(pnlAdjustmentDetail.getApprovedBy());
        p.setApprovalTime(pnlAdjustmentDetail.getApprovalTime());
        p.setApprovedComment(pnlAdjustmentDetail.getApprovedComment());
        p.setApprovedCommentText(pnlAdjustmentDetail.getApprovedCommentText());
        p.setCancelledBy(pnlAdjustmentDetail.getCancelledBy());
        p.setCancellationTime(pnlAdjustmentDetail.getCancellationTime());
        p.setCancellationComment(pnlAdjustmentDetail.getCancellationComment());
        p.setCancellationCommentText(pnlAdjustmentDetail.getCancellationCommentText());
        p.setApplied(AppConstants.getValue(pnlAdjustmentDetail.getIsApplied()));
        return p;
    }

    public static CustomerFavChaiMappingVO convert(CustomerFavChaiMapping customerFavChaiMapping) {
        CustomerFavChaiMappingVO customerFavChaiMappingVO = factory.createCustomerFavChaiMappingVO();
        customerFavChaiMappingVO.setCustomerId(customerFavChaiMapping.getCustomerId());
        customerFavChaiMappingVO.setProductId(customerFavChaiMapping.getProductId());
        customerFavChaiMappingVO.setProductName(customerFavChaiMapping.getProductName());
        customerFavChaiMappingVO.setDimension(customerFavChaiMapping.getDimension());
        customerFavChaiMappingVO.setCreatedAt(customerFavChaiMapping.getCreatedAt());
        customerFavChaiMappingVO.setCreationTime(customerFavChaiMapping.getCreationTime());
        customerFavChaiMappingVO.setLastUpdatedTime(customerFavChaiMapping.getLastUpdatedTime());
        customerFavChaiMappingVO.setConsumeType(customerFavChaiMapping.getConsumeType());
        customerFavChaiMappingVO.setIsUpdated(customerFavChaiMapping.getIsUpdated());
        customerFavChaiMappingVO.setTagType(customerFavChaiMapping.getTagType());
        customerFavChaiMappingVO.setSourceId(customerFavChaiMapping.getSourceId());
        customerFavChaiMappingVO.setSourceName(customerFavChaiMapping.getSourceName());
        customerFavChaiMappingVO.setStatus(customerFavChaiMapping.getStatus());
        customerFavChaiMappingVO.setCustomizationId(customerFavChaiMapping.getCustomizationId());
        customerFavChaiMappingVO.setRecipeId(customerFavChaiMapping.getRecipeId());
        customerFavChaiMappingVO.setRecipeProfile(customerFavChaiMapping.getRecipeProfile());
        customerFavChaiMappingVO.setQuantity(customerFavChaiMapping.getQuantity());
        customerFavChaiMappingVO.setShortCode(customerFavChaiMapping.getShortCode());
        return customerFavChaiMappingVO;
    }


    public static com.stpl.tech.kettle.domain.model.FavChaiCustomizationDetail convert(FavChaiCustomizationDetail favChaiCustomizationDetail) {
        com.stpl.tech.kettle.domain.model.FavChaiCustomizationDetail favChaiCustomizationDetailObj = factory.createFavChaiCustomizationDetail();
        favChaiCustomizationDetailObj.setName(favChaiCustomizationDetail.getName());
        favChaiCustomizationDetailObj.setProductId(favChaiCustomizationDetail.getProductId());
        favChaiCustomizationDetailObj.setDimension(favChaiCustomizationDetail.getDimension());
        favChaiCustomizationDetailObj.setSource(favChaiCustomizationDetail.getSource());
        favChaiCustomizationDetailObj.setType(favChaiCustomizationDetail.getType());
        favChaiCustomizationDetailObj.setQuantity(favChaiCustomizationDetail.getQuantity());
        favChaiCustomizationDetailObj.setDefaultSetting(favChaiCustomizationDetail.getDefaultSetting());
        favChaiCustomizationDetailObj.setFavChaiCustomizationDetailId(favChaiCustomizationDetail.getFavChaiCustomizationDetailId());
        favChaiCustomizationDetailObj.setCustomizationId(favChaiCustomizationDetail.getCustomerFavChaiMapping().getCustomizationId());
        favChaiCustomizationDetailObj.setShortCode(favChaiCustomizationDetail.getShortCode());
        favChaiCustomizationDetailObj.setUom(favChaiCustomizationDetail.getUom());
        return favChaiCustomizationDetailObj;
    }

    public static FavouriteChai convert(CustomerFavChaiMappingVO customerFavChaiMappingVO, MasterDataCache masterDataCache, int unitId) {
        FavouriteChai favouriteChaiObj = factory.createFavChaiDineIn();
        favouriteChaiObj.setName(customerFavChaiMappingVO.getTagType());
//       set Product Details for Fav Chai
        com.stpl.tech.kettle.domain.model.FavChaiProductDetails favChaiProductDetails = factory.createFavChaiProductDetails();
        favChaiProductDetails.setProductName(customerFavChaiMappingVO.getProductName());
        favChaiProductDetails.setProductId(customerFavChaiMappingVO.getProductId());
        favChaiProductDetails.setDimension(customerFavChaiMappingVO.getDimension());
        favouriteChaiObj.setProductDetailsForFavChai(favChaiProductDetails);
        favouriteChaiObj.setCartOrderItem(setCartOrderItemDetails(customerFavChaiMappingVO, favouriteChaiObj, masterDataCache, unitId));
        favouriteChaiObj.setName(customerFavChaiMappingVO.getTagType());
        favouriteChaiObj.setCustomerId(customerFavChaiMappingVO.getCustomerId());
        favouriteChaiObj.setStatus(AppConstants.ACTIVE);
        favouriteChaiObj.setIsUpdated(customerFavChaiMappingVO.getIsUpdated());
        favouriteChaiObj.setSourceId(customerFavChaiMappingVO.getSourceId());
        favouriteChaiObj.setSourceName(customerFavChaiMappingVO.getSourceName());
        return favouriteChaiObj;
    }

    private static com.stpl.tech.kettle.domain.model.CartOrderItem setCartOrderItemDetails(CustomerFavChaiMappingVO customerFavChaiMappingVO, FavouriteChai favouriteChaiObj, MasterDataCache masterDataCache, int unitId) {
        com.stpl.tech.kettle.domain.model.CartOrderItem cartOrderItemObj = factory.createCartOrderItem();
        cartOrderItemObj.setDim(new Dimension());
        cartOrderItemObj.setQty(customerFavChaiMappingVO.getQuantity());
        List<com.stpl.tech.kettle.domain.model.Variant> variants = new ArrayList<>();
        List<com.stpl.tech.kettle.domain.model.Addon> addonList = new ArrayList<>();
        List<com.stpl.tech.kettle.domain.model.Options> optionsList = new ArrayList<>();
        List<com.stpl.tech.kettle.domain.model.CartOrderItem> paidAddonsList = new ArrayList<>();
        if (Objects.nonNull(customerFavChaiMappingVO) && Objects.nonNull(customerFavChaiMappingVO.getFavChaiCustomizationDetailList()) && !customerFavChaiMappingVO.getFavChaiCustomizationDetailList().isEmpty()) {
            com.stpl.tech.kettle.domain.model.Addon addon = factory.createAddon();
            for (com.stpl.tech.kettle.domain.model.FavChaiCustomizationDetail favChaiCustomizationDetail : customerFavChaiMappingVO.getFavChaiCustomizationDetailList()) {
                if (ProductClassification.VARIANT.value().equalsIgnoreCase(favChaiCustomizationDetail.getType())) {
                    com.stpl.tech.kettle.domain.model.Variant variant = new Variant();
                    variant.setId(favChaiCustomizationDetail.getProductId());
                    if(ProductClassification.VARIANT.value().equalsIgnoreCase(favChaiCustomizationDetail.getType()) && favChaiCustomizationDetail.getName().equalsIgnoreCase("Regular Sugar")){
                        variant.setName("Sugar");
                    }else{
                        variant.setName("Chai Patti");
                    }
                    com.stpl.tech.kettle.domain.model.Options option = factory.createOptionsData();
                    option.setId(favChaiCustomizationDetail.getProductId());
                    option.setName(favChaiCustomizationDetail.getName());
                    option.setType(favChaiCustomizationDetail.getType());
                    option.setSize(favChaiCustomizationDetail.getUom());
                    option.setQty(favChaiCustomizationDetail.getQuantity());
                    variant.setOption(option);
                    variants.add(variant);
                } else if (ProductClassification.FREE_ADDON.value().equalsIgnoreCase(favChaiCustomizationDetail.getType())) {
                    addon.setName(AppConstants.addons);
                    com.stpl.tech.kettle.domain.model.Options option = factory.createOptionsData();
                    option.setQty(favChaiCustomizationDetail.getQuantity());
                    option.setSize(favChaiCustomizationDetail.getUom());
                    option.setName(favChaiCustomizationDetail.getName());
                    option.setType(ProductClassification.FREE_ADDON.value());
                    option.setId(favChaiCustomizationDetail.getProductId());
                    optionsList.add(option);
                    addon.setOptions(optionsList);
                } else {
//                    Paid Addons ---->dimension is None always
                    com.stpl.tech.kettle.domain.model.CartOrderItem cartOrderItem = factory.createCartOrderItem();
                    cartOrderItem.setQty(customerFavChaiMappingVO.getQuantity());
                    cartOrderItem.setPid(favChaiCustomizationDetail.getProductId());
                    cartOrderItem.setName(favChaiCustomizationDetail.getName());
                    cartOrderItem.setDim(new Dimension());
                    cartOrderItem.getDim().setCode(favChaiCustomizationDetail.getDimension());
                    cartOrderItem.getDim().setName(favChaiCustomizationDetail.getDimension());
                    Collection<Product> unitProductList = masterDataCache.getUnitProductDetails(unitId);//TODO here set unitId
                    if (Objects.nonNull(unitProductList) && !unitProductList.isEmpty()) {
                        for (Product product : unitProductList) {
                            if (product.getId() == favChaiCustomizationDetail.getProductId()) {
                                ProductPrice pPrice = product.getPrices().get(0);
                                cartOrderItem.setPrice(pPrice.getPrice());
                            }
                        }
                    }
                    paidAddonsList.add(cartOrderItem);
                }
            }
            cartOrderItemObj.setVariant(variants);//Variants
            cartOrderItemObj.setPaidAddons(paidAddonsList);//paid addons
            addonList.add(addon);
            cartOrderItemObj.setAddon(addonList);//free addons
        }
        cartOrderItemObj.getDim().setName(getName(customerFavChaiMappingVO.getDimension()));
        cartOrderItemObj.getDim().setCode(customerFavChaiMappingVO.getDimension());
        cartOrderItemObj.setPid(customerFavChaiMappingVO.getProductId());
        cartOrderItemObj.setName(customerFavChaiMappingVO.getProductName());
        return cartOrderItemObj;
    }

    private static String getName (String dimensionCode){
        String res = "";
        if(Objects.nonNull(dimensionCode)){
            String[] r = dimensionCode.split ("(?<=.)(?=\\p{Lu})");
            for (int i = 0; i < r.length; i++) {
                String str = r[i];
                if (i == r.length - 1) {
                    res += str;
                } else {
                    res += str + " ";
                }
            }
        }
        return res ;
    }

    public static com.stpl.tech.kettle.domain.model.SaveCustomerFavChaiRequest convert(FavouriteChai favouriteChai) {
        com.stpl.tech.kettle.domain.model.SaveCustomerFavChaiRequest customerFavChaiRequest = factory.createSaveCustomerFavChaiRequest();
        //set customer basic info
        CustomerBasicInfo customerBasicInfo =new CustomerBasicInfo(favouriteChai.getCustomerId(), favouriteChai.getCustomerName());
        customerFavChaiRequest.setCustomerBasicInfo(customerBasicInfo);
        customerFavChaiRequest.setFavChaiMarked(AppConstants.ACTIVE.equalsIgnoreCase(favouriteChai.getStatus()));
        //Step -- set selectedOrderItemDetails
        customerFavChaiRequest.setOrderItemDetails(setSelectedOrderItemDetails(favouriteChai, customerFavChaiRequest));
        customerFavChaiRequest.getOrderItemDetails().setBrandId(AppConstants.CHAAYOS_BRAND_ID);
        customerFavChaiRequest.getOrderItemDetails().setSourceId(favouriteChai.getSourceId());
        customerFavChaiRequest.getOrderItemDetails().setSourceName(favouriteChai.getSourceName());
        customerFavChaiRequest.getOrderItemDetails().setTagType(favouriteChai.getName());
        customerFavChaiRequest.getOrderItemDetails().setConsumeType(null);
        customerFavChaiRequest.getOrderItemDetails().setQuantity(favouriteChai.getCartOrderItem().getQty());
        return customerFavChaiRequest;
    }

    private static com.stpl.tech.kettle.domain.model.SelectedOrderItem setSelectedOrderItemDetails(FavouriteChai favouriteChai, SaveCustomerFavChaiRequest customerFavChaiRequest) {
        com.stpl.tech.kettle.domain.model.SelectedOrderItem selectedOrderItemObj = factory.createdSelectedOrderItem();
        if (Objects.nonNull(favouriteChai.getProductDetailsForFavChai())) {
            selectedOrderItemObj.setProductId(favouriteChai.getProductDetailsForFavChai().getProductId());
            selectedOrderItemObj.setProductName(favouriteChai.getProductDetailsForFavChai().getProductName());
            selectedOrderItemObj.setDimension(favouriteChai.getProductDetailsForFavChai().getDimension());//make sure dimension is code with blank space between eg ChotiKetli and not Choti Ketli
            selectedOrderItemObj.setPrice(favouriteChai.getProductDetailsForFavChai().getPrice());
        }
        selectedOrderItemObj.setDimension(favouriteChai.getCartOrderItem().getDim().getCode());//dimension on kettle side is stored as ChotiKetlu
        selectedOrderItemObj.setTagType(favouriteChai.getName());
        selectedOrderItemObj.setConsumeType(null);//FOR DINE IN
        com.stpl.tech.kettle.domain.model.OrderItemComposition orderItemComposition = factory.createOrderItemComposition();
        //Set variants data
        List<IngredientVariantDetail> ingredientVariantDetailList = new ArrayList<>();
        if (favouriteChai.getCartOrderItem().getVariant() != null) {
            for (Variant variant : favouriteChai.getCartOrderItem().getVariant()) {
                if (!AppConstants.DINE_IN_MILK_SELECTION_VARIANT_NAME.equalsIgnoreCase(variant.getName())) {
                    IngredientVariantDetail variantDetail = new IngredientVariantDetail();
                    variantDetail.setAlias(variant.getOption().getName());
                    variantDetail.setProductId(variant.getOption().getId());
                    variantDetail.setQuantity(variant.getOption().getQty());
                    variantDetail.setDefaultSetting(false);
                    variantDetail.setUom(variant.getOption().getSize() == null ? null : UnitOfMeasure.valueOf(variant.getOption().getSize()));
                    ingredientVariantDetailList.add(variantDetail);
                }
            }
        }
        //Set addOns Data
        List<IngredientProductDetail> ingredientProductDetailList = new ArrayList<>();
        if (Objects.nonNull(favouriteChai.getCartOrderItem().getAddon()) && !favouriteChai.getCartOrderItem().getAddon().isEmpty()) {
            for (com.stpl.tech.kettle.domain.model.Addon addon : favouriteChai.getCartOrderItem().getAddon()) {
                if (addon.getOptions() != null) {
                    for (com.stpl.tech.kettle.domain.model.Options option : addon.getOptions()) {
                        IngredientProductDetail i = new IngredientProductDetail();
                        i.setQuantity(option.getQty());
                        i.setDefaultSetting(false);
                        i.setUom(null);
                        ProductData p = new ProductData();
                        p.setProductId(option.getId());
                        p.setName(option.getName());
                        p.setClassification(ProductClassification.valueOf(option.getType()));
                        i.setProduct(p);
                        BasicInfo info = new BasicInfo();
                        info.setCode(option.getSize() != null ? option.getSize() : AppConstants.DIMENSION_NONE);
                        info.setName(option.getSize() != null ? option.getSize() : AppConstants.DIMENSION_NONE);
                        i.setDimension(info);
                        ingredientProductDetailList.add(i);
                    }
                }
            }
        }
//      Set Paid Addons Data
        setRecipeDetailVO(favouriteChai,orderItemComposition,customerFavChaiRequest);
        orderItemComposition.setVariants(ingredientVariantDetailList);
        orderItemComposition.setAddons(ingredientProductDetailList);
//        orderItemComposition.setOptions(optionNames);
        selectedOrderItemObj.setComposition(orderItemComposition);
        return selectedOrderItemObj;
    }

    private static RecipeDetailVO setRecipeDetailVO(FavouriteChai favouriteChai, OrderItemComposition orderItemComposition, SaveCustomerFavChaiRequest customerFavChaiRequest){
        List<OptionDataVO> options = new ArrayList<>();
        List<String> optionNames = new ArrayList<>();
        if (Objects.nonNull(favouriteChai.getCartOrderItem().getPaidAddons()) && !favouriteChai.getCartOrderItem().getPaidAddons().isEmpty()) {
            for (CartOrderItem paidAddOn : favouriteChai.getCartOrderItem().getPaidAddons()) {
                OptionDataVO optionDataVO = new OptionDataVO();
                optionDataVO.setProductId(paidAddOn.getPid());
                optionDataVO.setId(paidAddOn.getPid());
                optionDataVO.setName(paidAddOn.getName());
                optionNames.add(paidAddOn.getName());
                options.add(optionDataVO);
            }
        }
        RecipeDetailVO recipeDetailVO = factory.createRecipeDetails();
        recipeDetailVO.getOptions().clear();
        recipeDetailVO.getOptions().addAll(options);
        customerFavChaiRequest.setRecipeDetails(recipeDetailVO);
        if(Objects.nonNull(orderItemComposition)){
            orderItemComposition.setOptions(optionNames);
        }
        return recipeDetailVO;
    }

    public static List<OrderDetailTrim> convert(List<Order> objs){
        List<OrderDetailTrim> orderDetailTrimList = new ArrayList<>();
        for(Order obj : objs){
            OrderDetailTrim detailTrim = new OrderDetailTrim();
            detailTrim.setGeneratedOrderId(obj.getGenerateOrderId());
            detailTrim.setChannelPartnerId(obj.getChannelPartner());
            detailTrim.setOrderType(obj.getOrderType());
            detailTrim.setBrandId(obj.getBrandId());
            detailTrim.setManualBillBookNo(obj.getBillBookNo());
            detailTrim.setTableNumber(obj.getTableNumber());
            detailTrim.setOrderSource(obj.getSource());
            detailTrim.setBillGenerationTime(obj.getBillCreationTime());
            detailTrim.setTaxableAmount(obj.getTransactionDetail().getTaxableAmount());
            detailTrim.setSettledAmount(obj.getTransactionDetail().getPaidAmount());
            detailTrim.setSettlementType(obj.getSettlementType().value());
            detailTrim.setSettlements(obj.getSettlements());
            detailTrim.setPointsRedeemed(obj.getPointsRedeemed());
            detailTrim.setPointsAcquired(obj.getPointsAcquired());
            orderDetailTrimList.add(detailTrim);
        }
        return orderDetailTrimList;
    }

    public static WorkStationManualTaskDetail convert(WorkstationManualTask task) {
        WorkStationManualTaskDetail taskDetail = new WorkStationManualTaskDetail();
        taskDetail.setTaskType(task.getTaskType());
        taskDetail.setOrderTime(task.getOrderTime());
        taskDetail.setUnitId(task.getUnitId());
        taskDetail.setEmployeeId(task.getEmployeeId());
        taskDetail.setGeneratedOrderId(task.getGeneratedOrderId());
        taskDetail.setProductItemId(task.getProductItemId());
        taskDetail.setDimension(task.getDimension());
        taskDetail.setQuantity(task.getQuantity());
        taskDetail.setMonkNumber(task.getMonkNumber());
        taskDetail.setErrorType(task.getErrorType());
        return taskDetail;
    }

    public static WorkstationManualTask convert(WorkStationManualTaskDetail taskDetail) {
        WorkstationManualTask task = new WorkstationManualTask();
        task.setManualTaskId(taskDetail.getManualTaskId());
        task.setTaskType(taskDetail.getTaskType());
        task.setOrderTime(taskDetail.getOrderTime());
        task.setUnitId(taskDetail.getUnitId());
        task.setEmployeeId(taskDetail.getEmployeeId());
        task.setGeneratedOrderId(taskDetail.getGeneratedOrderId());
        task.setProductItemId(taskDetail.getProductItemId());
        task.setDimension(taskDetail.getDimension());
        task.setQuantity(taskDetail.getQuantity());
        task.setMonkNumber(taskDetail.getMonkNumber());
        task.setErrorType(taskDetail.getErrorType());
        return task;
    }

    public static MonkCalibrationEventDetail convert(MonkCalibrationEvent monkCalibrationEvent) {
        MonkCalibrationEventDetail calibrationDetail = new MonkCalibrationEventDetail();
        calibrationDetail.setUnitId(monkCalibrationEvent.getUnitId());
        calibrationDetail.setOrderId(monkCalibrationEvent.getOrderId());
        calibrationDetail.setOrderItemId(monkCalibrationEvent.getOrderItemId());
        calibrationDetail.setMonkNo(monkCalibrationEvent.getMonkNo());
        calibrationDetail.setProcessStatus(monkCalibrationEvent.getProcessStatus());
        calibrationDetail.setCalibrationStatus(monkCalibrationEvent.getCalibrationStatus());
        calibrationDetail.setOrderTime(monkCalibrationEvent.getOrderTime());
        calibrationDetail.setStartTime(monkCalibrationEvent.getStartTime());
        calibrationDetail.setEndTime(monkCalibrationEvent.getEndTime());
        calibrationDetail.setServerTime(AppUtils.getCurrentTimestamp());
        calibrationDetail.setExpectedQuantity(monkCalibrationEvent.getExpectedQuantity());
        calibrationDetail.setMachineQuantity(monkCalibrationEvent.getMachineQuantity());
        calibrationDetail.setEnteredQuantity(monkCalibrationEvent.getEnteredQuantity());
        calibrationDetail.setMilkQuantity(monkCalibrationEvent.getMilkQuantity());
        calibrationDetail.setTotalMonks(monkCalibrationEvent.getTotalMonks());
        calibrationDetail.setActiveMonks(monkCalibrationEvent.getActiveMonks());
        calibrationDetail.setInActiveMonks(monkCalibrationEvent.getInActiveMonks());
        calibrationDetail.setCalibratedMonks(monkCalibrationEvent.getCalibratedMonks());
        calibrationDetail.setActiveNonCalibratedMonks(monkCalibrationEvent.getActiveNonCalibratedMonks());
        return calibrationDetail;
    }

    public static MonkCalibrationEvent convert(MonkCalibrationEventDetail calibrationDetail) {
        MonkCalibrationEvent monkCalibrationEvent = new MonkCalibrationEvent();
        monkCalibrationEvent.setMonkCalibrationEventId(calibrationDetail.getMonkCalibrationEventId());
        monkCalibrationEvent.setUnitId(calibrationDetail.getUnitId());
        monkCalibrationEvent.setOrderId(calibrationDetail.getOrderId());
        monkCalibrationEvent.setOrderItemId(calibrationDetail.getOrderItemId());
        monkCalibrationEvent.setMonkNo(calibrationDetail.getMonkNo());
        monkCalibrationEvent.setProcessStatus(calibrationDetail.getProcessStatus());
        monkCalibrationEvent.setCalibrationStatus(calibrationDetail.getCalibrationStatus());
        monkCalibrationEvent.setOrderTime(calibrationDetail.getOrderTime());
        monkCalibrationEvent.setStartTime(calibrationDetail.getStartTime());
        monkCalibrationEvent.setEndTime(calibrationDetail.getEndTime());
        monkCalibrationEvent.setServerTime(calibrationDetail.getServerTime());
        monkCalibrationEvent.setExpectedQuantity(calibrationDetail.getExpectedQuantity());
        monkCalibrationEvent.setMachineQuantity(calibrationDetail.getMachineQuantity());
        monkCalibrationEvent.setEnteredQuantity(calibrationDetail.getEnteredQuantity());
        monkCalibrationEvent.setMilkQuantity(calibrationDetail.getMilkQuantity());
        monkCalibrationEvent.setTotalMonks(calibrationDetail.getTotalMonks());
        monkCalibrationEvent.setActiveMonks(calibrationDetail.getActiveMonks());
        monkCalibrationEvent.setInActiveMonks(calibrationDetail.getInActiveMonks());
        monkCalibrationEvent.setCalibratedMonks(calibrationDetail.getCalibratedMonks());
        monkCalibrationEvent.setActiveNonCalibratedMonks(calibrationDetail.getActiveNonCalibratedMonks());
        return monkCalibrationEvent;
    }

    public static PartnerOrderRiderStatesDetailData convert(PartnerOrderRiderStatesDetail partnerOrderRiderStatesDetail){
        return PartnerOrderRiderStatesDetailData.builder().partnerOrderId(partnerOrderRiderStatesDetail.getPartnerOrderId()).kettleOrderId(partnerOrderRiderStatesDetail.getKettleOrderId())
                .billingServerTime(partnerOrderRiderStatesDetail.getBillingServerTime()).partnerName(partnerOrderRiderStatesDetail.getPartnerName()).riderName(partnerOrderRiderStatesDetail.getRiderName())
                .riderContact(partnerOrderRiderStatesDetail.getRiderContact()).riderAssignedAt(partnerOrderRiderStatesDetail.getRiderAssignedAt()).riderArrivedAt(partnerOrderRiderStatesDetail.getRiderArrivedAt())
                .delayReason(partnerOrderRiderStatesDetail.getReasonsForDelay()).orderPickupTime(partnerOrderRiderStatesDetail.getOrderPickupTime()).orderDeliveryTime(partnerOrderRiderStatesDetail.getOrderDeliveryTime()).build();
    }

    public static OrderRefund convert(OrderRefundDetail orderRefundDetail) {
        return OrderRefund.builder().orderRefundDetailId(orderRefundDetail.getOrderRefundDetailId()).orderId(orderRefundDetail.getOrderId()).refundAmount(orderRefundDetail.getRefundAmount())
                .refundType(orderRefundDetail.getRefundType()).refundStatus(orderRefundDetail.getRefundStatus()).updationTime(orderRefundDetail.getUpdatedTime()).referenceRefundId(orderRefundDetail.getReferenceRefundId())
                .refundReason(orderRefundDetail.getRefundReason()).createdBy(orderRefundDetail.getCreatedBy()).creationTime(orderRefundDetail.getCreationTime()).build();

    }

}
