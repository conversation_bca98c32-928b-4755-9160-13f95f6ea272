package com.stpl.tech.kettle.data.model;

import javax.persistence.*;
import java.util.Date;

/**
 * Created by Chaayos on 19-05-2017.
 */
@Entity
@Table(name = "CASH_CARD_EVENT_LOG")
public class CashCardEventsLogData {

    private Integer id;
    private String cardNumber;
    private String cardSerial;
    private String event;
    private String eventDetail;
    private Date eventTime;
    private Integer empId;
    private Integer unitId;


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "CASH_CARD_EVENT_LOG_ID", nullable = false, unique = true)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "CARD_NUMBER")
    public String getCardNumber() {
        return cardNumber;
    }

    public void setCardNumber(String cardNumber) {
        this.cardNumber = cardNumber;
    }

    @Column(name = "CARD_SERIAL")
    public String getCardSerial() {
        return cardSerial;
    }

    public void setCardSerial(String cardSerial) {
        this.cardSerial = cardSerial;
    }

    @Column(name = "EVENT_NAME", nullable = false, length = 100)
    public String getEvent() {
        return event;
    }

    public void setEvent(String event) {
        this.event = event;
    }

    @Column(name = "EVENT_DETAIL", length = 200, nullable = false)
    public String getEventDetail() {
        return eventDetail;
    }

    public void setEventDetail(String eventDetail) {
        this.eventDetail = eventDetail;
    }

    @Column(name = "EVENT_TIME", nullable = false)
    public Date getEventTime() {
        return eventTime;
    }

    public void setEventTime(Date eventTime) {
        this.eventTime = eventTime;
    }

    @Column(name = "EMP_ID", nullable = false)
    public Integer getEmpId() {
        return empId;
    }

    public void setEmpId(Integer empId) {
        this.empId = empId;
    }

    @Column(name = "UNIT_ID")
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }
}
