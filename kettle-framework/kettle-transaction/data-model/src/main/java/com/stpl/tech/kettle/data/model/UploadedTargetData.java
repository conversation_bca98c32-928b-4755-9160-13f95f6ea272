/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are private by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */
package com.stpl.tech.kettle.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.stpl.tech.util.excelparser.annotations.ExcelField;
import com.stpl.tech.util.excelparser.annotations.ExcelObject;
import com.stpl.tech.util.excelparser.annotations.ExcelSheet;
import com.stpl.tech.util.excelparser.annotations.ParseType;

@ExcelSheet(value = "Monthly Target Data")
@ExcelObject(parseType = ParseType.ROW, start = 1, end = 0)
@Entity
@Table(name = "MONTHLY_TARGETS_DATA")
public class UploadedTargetData {

	private int targetDataId;
	@ExcelField(validationType = ExcelField.ValidationType.HARD, regex = "[0-2][0-9][0-9][0-9]")
	private int unitId;
	@ExcelField
	private String targetType;
	@ExcelField
	private Date businessDate;
	@ExcelField(validationType = ExcelField.ValidationType.HARD, regex = "[0-2][0-9][0-9][0-9]")
	private int month;
	@ExcelField(validationType = ExcelField.ValidationType.HARD, regex = "[0-2][0-9][0-9][0-9]")
	private int year;
	private String status;

	// SALES
	@ExcelField
	protected BigDecimal netSale;
	@ExcelField
	protected BigDecimal deliveryNetSale;
	@ExcelField
	protected BigDecimal dineInNetSale;
	@ExcelField
	protected int tickets;
	@ExcelField
	protected int deliveryTickets;
	@ExcelField
	protected int dineInTickets;
	@ExcelField
	protected BigDecimal gmv;
	@ExcelField
	protected BigDecimal deliveryGmv;
	@ExcelField
	protected BigDecimal dineInGmv;
	@ExcelField
	protected BigDecimal apc;
	@ExcelField
	protected BigDecimal deliveryApc;
	@ExcelField
	protected BigDecimal dineInApc;

	// PENITRATION
	@ExcelField
	protected int food;
	@ExcelField
	protected int nonVeg;
	@ExcelField
	protected int cold;
	@ExcelField
	protected int hot;
	@ExcelField
	protected int meals;
	@ExcelField
	protected int cakes;
	@ExcelField
	protected int giftCards;
	@ExcelField
	protected int merchandise;
	@ExcelField
	protected int beverage;
	@ExcelField
	protected int newCustWithNoPriPrd;
	@ExcelField
	protected int seasonal;
	@ExcelField
	protected int customer;
	@ExcelField
	protected int newCustomer;
	@ExcelField
	protected int regular;
	@ExcelField
	protected int full;
	@ExcelField
	protected BigDecimal merchandiseSales;
	@ExcelField
	protected int seasonalProductOne;
	@ExcelField
	protected int seasonalProductTwo;

	@ExcelField
	protected int seasonalProductThree;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "TARGETS_DATA_ID", unique = true, nullable = false)
	public int getTargetDataId() {
		return targetDataId;
	}

	public void setTargetDataId(int targetDataId) {
		this.targetDataId = targetDataId;
	}

	@Column(name = "UNIT_ID", nullable = false)
	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	
	@Column(name = "TARGET_TYPE", nullable = false)
	public String getTargetType() {
		return targetType;
	}

	public void setTargetType(String targetType) {
		this.targetType = targetType;
	}

 	@Temporal(TemporalType.DATE)
	@Column(name = "BUSINESS_DATE", nullable = true, length = 10)
	public Date getBusinessDate() {
		return businessDate;
	}

	public void setBusinessDate(Date businessDate) {
		this.businessDate = businessDate;
	}

	@Column(name = "TARGET_MONTH", nullable = false)
	public int getMonth() {
		return month;
	}

	public void setMonth(int month) {
		this.month = month;
	}

	@Column(name = "TARGET_YEAR", nullable = false)
	public int getYear() {
		return year;
	}

	public void setYear(int year) {
		this.year = year;
	}

	@Column(name = "RECORD_STATUS", nullable = false)
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@Column(name = "NET_SALE", nullable = false)
	public BigDecimal getNetSale() {
		return netSale;
	}

	public void setNetSale(BigDecimal netSale) {
		this.netSale = netSale;
	}

	@Column(name = "NET_SALE_DELIVERY", nullable = false)
	public BigDecimal getDeliveryNetSale() {
		return deliveryNetSale;
	}

	public void setDeliveryNetSale(BigDecimal deliveryNetSale) {
		this.deliveryNetSale = deliveryNetSale;
	}

	@Column(name = "NET_SALE_DINE_IN", nullable = false)
	public BigDecimal getDineInNetSale() {
		return dineInNetSale;
	}

	public void setDineInNetSale(BigDecimal dineInNetSale) {
		this.dineInNetSale = dineInNetSale;
	}

	@Column(name = "TICKETS", nullable = false)
	public int getTickets() {
		return tickets;
	}

	public void setTickets(int tickets) {
		this.tickets = tickets;
	}

	@Column(name = "TICKETS_DELIVERY", nullable = false)
	public int getDeliveryTickets() {
		return deliveryTickets;
	}

	public void setDeliveryTickets(int deliveryTickets) {
		this.deliveryTickets = deliveryTickets;
	}

	@Column(name = "TICKETS_DINE_IN", nullable = false)
	public int getDineInTickets() {
		return dineInTickets;
	}

	public void setDineInTickets(int dineInTickets) {
		this.dineInTickets = dineInTickets;
	}

	@Column(name = "GMV", nullable = false)
	public BigDecimal getGmv() {
		return gmv;
	}

	public void setGmv(BigDecimal gmv) {
		this.gmv = gmv;
	}

	@Column(name = "GMV_DELIVERY", nullable = false)
	public BigDecimal getDeliveryGmv() {
		return deliveryGmv;
	}

	public void setDeliveryGmv(BigDecimal deliveryGmv) {
		this.deliveryGmv = deliveryGmv;
	}

	@Column(name = "GMV_DINE_IN", nullable = false)
	public BigDecimal getDineInGmv() {
		return dineInGmv;
	}

	public void setDineInGmv(BigDecimal dineInGmv) {
		this.dineInGmv = dineInGmv;
	}

	@Column(name = "APC", nullable = false)
	public BigDecimal getApc() {
		return apc;
	}

	public void setApc(BigDecimal apc) {
		this.apc = apc;
	}

	@Column(name = "APC_DELIVERY", nullable = false)
	public BigDecimal getDeliveryApc() {
		return deliveryApc;
	}

	public void setDeliveryApc(BigDecimal deliveryApc) {
		this.deliveryApc = deliveryApc;
	}

	@Column(name = "APC_DINE_IN", nullable = false)
	public BigDecimal getDineInApc() {
		return dineInApc;
	}

	public void setDineInApc(BigDecimal dineInApc) {
		this.dineInApc = dineInApc;
	}

	@Column(name = "FOOD", nullable = false)
	public int getFood() {
		return food;
	}

	public void setFood(int food) {
		this.food = food;
	}

	@Column(name = "NON_VEG", nullable = false)
	public int getNonVeg() {
		return nonVeg;
	}

	public void setNonVeg(int nonVeg) {
		this.nonVeg = nonVeg;
	}

	@Column(name = "COLD", nullable = false)
	public int getCold() {
		return cold;
	}

	public void setCold(int cold) {
		this.cold = cold;
	}

	@Column(name = "HOT", nullable = false)
	public int getHot() {
		return hot;
	}

	public void setHot(int hot) {
		this.hot = hot;
	}

	@Column(name = "MEAL", nullable = false)
	public int getMeals() {
		return meals;
	}

	public void setMeals(int meals) {
		this.meals = meals;
	}

	@Column(name = "CAKE", nullable = false)
	public int getCakes() {
		return cakes;
	}

	public void setCakes(int cakes) {
		this.cakes = cakes;
	}

	@Column(name = "GIFT_CARD", nullable = false)
	public int getGiftCards() {
		return giftCards;
	}

	public void setGiftCards(int giftCards) {
		this.giftCards = giftCards;
	}

	@Column(name = "MERCHANDISE", nullable = false)
	public int getMerchandise() {
		return merchandise;
	}

	public void setMerchandise(int merchandise) {
		this.merchandise = merchandise;
	}

	@Column(name = "BEVERAGE", nullable = false)
	public int getBeverage() {
		return beverage;
	}

	public void setBeverage(int beverage) {
		this.beverage = beverage;
	}

	@Column(name = "NEW_CUST_WITH_NO_PRIMARY_PRODUCT", nullable = false)
	public int getNewCustWithNoPriPrd() {
		return newCustWithNoPriPrd;
	}

	public void setNewCustWithNoPriPrd(int newCustWithNoPriPrd) {
		this.newCustWithNoPriPrd = newCustWithNoPriPrd;
	}

	@Column(name = "SEASONAL", nullable = false)
	public int getSeasonal() {
		return seasonal;
	}

	public void setSeasonal(int seasonal) {
		this.seasonal = seasonal;
	}

	@Column(name = "CUSTOMER", nullable = false)
	public int getCustomer() {
		return customer;
	}

	public void setCustomer(int customer) {
		this.customer = customer;
	}

	@Column(name = "NEW_CUSTOMER", nullable = false)
	public int getNewCustomer() {
		return newCustomer;
	}

	public void setNewCustomer(int newCustomer) {
		this.newCustomer = newCustomer;
	}

	@Column(name = "DIMENSION_REGULAR", nullable = false)
	public int getRegular() {
		return regular;
	}

	public void setRegular(int regular) {
		this.regular = regular;
	}

	@Column(name = "DIMENSION_FULL", nullable = false)
	public int getFull() {
		return full;
	}

	public void setFull(int full) {
		this.full = full;
	}

	@Column(name = "MERCHANDISE_SALES", precision = 10)
	public BigDecimal getMerchandiseSales() {
		return merchandiseSales;
	}

	public void setMerchandiseSales(BigDecimal merchandiseSales) {
		this.merchandiseSales = merchandiseSales;
	}

	@Column(name = "SEASONAL_PRODUCT_1", nullable = false)
	public int getSeasonalProductOne() {
		return seasonalProductOne;
	}

	public void setSeasonalProductOne(int seasonalProductOne) {
		this.seasonalProductOne = seasonalProductOne;
	}
	@Column(name = "SEASONAL_PRODUCT_2", nullable = false)
	public int getSeasonalProductTwo() {
		return seasonalProductTwo;
	}

	public void setSeasonalProductTwo(int seasonalProductTwo) {
		this.seasonalProductTwo = seasonalProductTwo;
	}

	@Column(name = "SEASONAL_PRODUCT_3", nullable = false)
	public int getSeasonalProductThree() {
		return seasonalProductThree;
	}

	public void setSeasonalProductThree(int seasonalProductThree) {
		this.seasonalProductThree = seasonalProductThree;
	}
}