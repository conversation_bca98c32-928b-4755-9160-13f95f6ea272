/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core.service;

import com.stpl.tech.kettle.core.data.vo.VerificationResponse;
import com.stpl.tech.kettle.data.model.DeliveryPartner;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.domain.model.BillBookOrder;
import com.stpl.tech.master.domain.model.ConsumptionMetadata;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.ListData;
import com.stpl.tech.master.domain.model.ListDataTrim;
import com.stpl.tech.master.domain.model.ManualBillBook;
import com.stpl.tech.master.domain.model.PartnerDetail;
import com.stpl.tech.master.domain.model.Unit;

import java.util.List;
import java.util.Map;

public interface MetadataMangementService {

	public List<IdCodeName> getAllDeliveryPartners();

	public IdCodeName updateDeliveryPartner(DeliveryPartner deliveryPartner) throws DataNotFoundException;

	public boolean changeDeliveryPartnerStatus(int partnerId, String active) throws DataNotFoundException;

	public List<PartnerDetail> getAllDeliveyPartnersForUnit(int unitId);

	public boolean updateDeliveryPartnerForUnit(Unit unit);

	public boolean createManualBillBookEntry(ManualBillBook manualBillBook);
	
	public boolean cancelManualBillBookEntry(int transferOrderId);
	
	public boolean updateManualBillBookEntry(ManualBillBook manualBillBook);
	
	public List<ManualBillBook> getManualBillBookDetail(int unitId, boolean getAll);
	
	public List<BillBookOrder> getAllOrdersForBillBook(int billBookId);
	
	public boolean validateManualBillBookEntry(ManualBillBook manualBillBook);
	
	public VerificationResponse validateManualBillBookNo(int billBookNo,int unitId);

	Map<String, Map<Integer, Map<String, ConsumptionMetadata>>> getConsumableCodes(String allSachetsFlag);

    List<ListData> getListDataByGroupName(String gName);

	List<ListDataTrim> getListDataByGroupNameAndCat(String gName, String cat);

	boolean refreshConsumableCodes();
}
