package com.stpl.tech.kettle.data.model;

import java.math.BigDecimal;

public class SpecialOfferResponse {
    private String offerType;
    private String offerCode;
    private String validityFrom;
    private String validityTill;
    private String text;
    private Boolean isExistingOffer;
    private BigDecimal chaayosCash;
    private String offerTnCString;
    private Integer maxUsage;

    public SpecialOfferResponse() {
    }

    public SpecialOfferResponse(String offerType) {
        this.offerType = offerType;
    }

    public String getOfferType() {
        return offerType;
    }

    public void setOfferType(String offerType) {
        this.offerType = offerType;
    }

    public String getOfferCode() {
        return offerCode;
    }

    public void setOfferCode(String offerCode) {
        this.offerCode = offerCode;
    }

    public String getValidityFrom() {
        return validityFrom;
    }

    public void setValidityFrom(String validityFrom) {
        this.validityFrom = validityFrom;
    }

    public String getValidityTill() {
        return validityTill;
    }

    public void setValidityTill(String validityTill) {
        this.validityTill = validityTill;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public Boolean getExistingOffer() {
        return isExistingOffer;
    }

    public void setExistingOffer(Boolean existingOffer) {
        isExistingOffer = existingOffer;
    }

    public BigDecimal getChaayosCash() {
        return chaayosCash;
    }

    public void setChaayosCash(BigDecimal chaayosCash) {
        this.chaayosCash = chaayosCash;
    }

    public String getOfferTnCString() {
        return offerTnCString;
    }

    public void setOfferTnCString(String offerTnCString) {
        this.offerTnCString = offerTnCString;
    }

    public Integer getMaxUsage() {
        return maxUsage;
    }

    public void setMaxUsage(Integer maxUsage) {
        this.maxUsage = maxUsage;
    }
}
