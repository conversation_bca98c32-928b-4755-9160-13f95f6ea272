package com.stpl.tech.kettle.data.model;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Entity
@Table(name = "SCM_STOCK_DATA")
public class ScmStockData {


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "SCM_STOCK_DATA_ID")
    Integer scmStockDataId;

    @Column(name = "UNIT_ID")
    Integer unitId;

    @Column(name = "TOTAL_DOWNTIME")
    BigDecimal totalDownTime;

    @Column(name = "CAFE_TOTAL_HOURS")
    BigDecimal cafeOperationHours;

    @Column(name = "BRAND_ID")
    Integer brandId;

    @Column(name = "BUSINESS_DATE")
    Date businessDate;


    @Column(name = "OPENING_TIME")
    Date cafeOpeningTime;

    @Column(name = "CLOSING_TIME")
    Date cafeClosingTime;

    @Column(name = "PRODUCT_ID")
    Integer productId;
}
