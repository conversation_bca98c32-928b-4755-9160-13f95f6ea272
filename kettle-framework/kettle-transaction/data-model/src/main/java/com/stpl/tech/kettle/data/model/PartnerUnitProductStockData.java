package com.stpl.tech.kettle.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.math.BigDecimal;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "PARTNER_UNIT_PRODUCT_STOCK_DATA")
public class PartnerUnitProductStockData {
    private Integer key;
    private Date calculationDate;
    private Date cafeOpening;
    private Date cafeClosing;
    private Integer unitId;
    private String unitName;
    private Integer productId;
    private String productName;
    private Integer downTime;
    private Date businessDate;
    private Date startTime;
    private Date closeTime;
    private String dimension;
    private Integer startHour;
    private Integer endHour;
    private Integer totalTime;
    private BigDecimal weight;
    private Integer brandId;

    public PartnerUnitProductStockData(Date businessDate, Date calculationDate, Date cafeOpening,
                                       Date cafeClosing, Integer unitId, String unitName, Integer productId, String productName, Integer downTime, Date startTime, Date closeTime,
                                       Integer startHour, Integer endHour, Integer totalTime, String dimension, BigDecimal weight, Integer brandId) {
        this.businessDate = businessDate;
        this.calculationDate = calculationDate;
        this.cafeOpening = cafeOpening;
        this.cafeClosing = cafeClosing;
        this.unitId = unitId;
        this.unitName = unitName;
        this.productId = productId;
        this.productName = productName;
        this.downTime = downTime;
        this.startTime = startTime;
        this.closeTime = closeTime;
        this.dimension = dimension;
        this.startHour = startHour;
        this.endHour = endHour;
        this.totalTime = totalTime;
        this.weight = weight;
        this.brandId = brandId;
    }

    public PartnerUnitProductStockData() {

    }

    @Id
    @Column(name = "KEY_ID", nullable = false, unique = true)
    @GeneratedValue(strategy = IDENTITY)
    public Integer getKey() {
        return key;
    }

    public void setKey(Integer key) {
        this.key = key;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "BUSINESS_DATE", nullable = true, length = 10)
    public Date getBusinessDate() {
        return businessDate;
    }

    public void setBusinessDate(Date businessDate) {
        this.businessDate = businessDate;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "CALCULATION_DATE", nullable = true, length = 10)
    public Date getCalculationDate() {
        return calculationDate;
    }

    public void setCalculationDate(Date calculationDate) {
        this.calculationDate = calculationDate;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CAFE_OPENING")
    public Date getCafeOpening() {
        return cafeOpening;
    }

    public void setCafeOpening(Date cafeOpening) {
        this.cafeOpening = cafeOpening;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CAFE_CLOSING")
    public Date getCafeClosing() {
        return cafeClosing;
    }

    public void setCafeClosing(Date cafeClosing) {
        this.cafeClosing = cafeClosing;
    }

    @Column(name = "UNIT_ID")
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @Column(name = "UNIT_NAME")
    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    @Column(name = "PRODUCT_ID")
    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    @Column(name = "PRODUCT_NAME")
    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    @Column(name = "DOWNTIME")
    public Integer getDownTime() {
        return downTime;
    }

    public void setDownTime(Integer downTime) {
        this.downTime = downTime;
    }

    @Column(name = "START_TIME")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    @Column(name = "CLOSE_TIME")
    @Temporal(TemporalType.TIMESTAMP)
    public Date getCloseTime() {
        return closeTime;
    }

    public void setCloseTime(Date closeTime) {
        this.closeTime = closeTime;
    }

    @Column(name = "START_HOUR")
    public Integer getStartHour() {
        return startHour;
    }

    public void setStartHour(Integer startHour) {
        this.startHour = startHour;
    }

    @Column(name = "END_HOUR")
    public Integer getEndHour() {
        return endHour;
    }

    public void setEndHour(Integer endHour) {
        this.endHour = endHour;
    }

    @Column(name = "TOTAL_TIME")
    public Integer getTotalTime() {
        return totalTime;
    }

    public void setTotalTime(Integer totalTime) {
        this.totalTime = totalTime;
    }

    @Column(name = "WEIGHT")
    public BigDecimal getWeight() {
        return weight;
    }

    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }

    @Column(name = "DIMENSION")
    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    @Column(name = "BRAND_ID")
    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }
}
