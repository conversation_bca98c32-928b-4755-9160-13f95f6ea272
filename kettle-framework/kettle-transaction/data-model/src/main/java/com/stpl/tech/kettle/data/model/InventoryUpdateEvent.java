/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;
// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * OrderSettlement generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "INVENTORY_UPDATE_EVENT"

)
public class InventoryUpdateEvent implements java.io.Serializable {

	private Integer inventoryUpdateEventId;
	private int unitId;
	private int recordsCount;
	private String eventType;
	private String updateComment;
	private int updatedBy;
	private Date updateTime;
	private Date businessDate;
	private String updateStatus;

	public InventoryUpdateEvent() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)

	@Column(name = "INVENTORY_UPDATE_EVENT_ID", unique = true, nullable = false)
	public Integer getInventoryUpdateEventId() {
		return this.inventoryUpdateEventId;
	}

	public void setInventoryUpdateEventId(Integer inventoryUpdateEventId) {
		this.inventoryUpdateEventId = inventoryUpdateEventId;
	}

	@Column(name = "UNIT_ID", nullable = false)
	public int getUnitId() {
		return this.unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	@Column(name = "UPDATED_BY", nullable = true)
	public int getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(int updatedBy) {
		this.updatedBy = updatedBy;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "UPDATE_TIME", nullable = false, length = 19)
	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	@Column(name = "EVENT_TYPE", nullable = false, length = 15)
	public String getEventType() {
		return eventType;
	}

	public void setEventType(String eventType) {
		this.eventType = eventType;
	}

	@Column(name = "UPDATE_COMMENT", nullable = true, length = 300)
	public String getUpdateComment() {
		return updateComment;
	}

	public void setUpdateComment(String reasonText) {
		this.updateComment = reasonText;
	}

	@Column(name = "UPDATE_STATUS", nullable = false, length = 15)
	public String getUpdateStatus() {
		return updateStatus;
	}

	public void setUpdateStatus(String updateStatus) {
		this.updateStatus = updateStatus;
	}

	@Column(name = "RECORDS_COUNT", nullable = false)
	public int getRecordsCount() {
		return recordsCount;
	}

	public void setRecordsCount(int recordCount) {
		this.recordsCount = recordCount;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "BUSINESS_DATE", nullable = false, length = 19)
	public Date getBusinessDate() {
		return businessDate;
	}

	public void setBusinessDate(Date businessDate) {
		this.businessDate = businessDate;
	}

}
