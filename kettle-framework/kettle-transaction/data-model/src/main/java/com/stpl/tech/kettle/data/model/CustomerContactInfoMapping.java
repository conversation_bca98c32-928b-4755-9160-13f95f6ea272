package com.stpl.tech.kettle.data.model;

import javax.persistence.*;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "CUSTOMER_CONTACT_INFO_MAPPING")
public class CustomerContactInfoMapping implements java.io.Serializable {
    private Integer mappingId;
    private Integer customerId;
    private String oldContactNumber;
    private String newContactNumber;

    public CustomerContactInfoMapping() {
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "ID", unique = true, nullable = false)
    public Integer getMappingId() {
        return mappingId;
    }

    public void setMappingId(Integer mappingId) {
        this.mappingId = mappingId;
    }

    @Column(name = "CUSTOMER_ID")
    public Integer getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    @Column(name = "OLD_CONTACT_NUMBER")
    public String getOldContactNumber() {
        return oldContactNumber;
    }

    public void setOldContactNumber(String oldContactNumber) {
        this.oldContactNumber = oldContactNumber;
    }

    @Column(name = "NEW_CONTACT_NUMBER")
    public String getNewContactNumber() {
        return newContactNumber;
    }

    public void setNewContactNumber(String newContactNumber) {
        this.newContactNumber = newContactNumber;
    }
}
