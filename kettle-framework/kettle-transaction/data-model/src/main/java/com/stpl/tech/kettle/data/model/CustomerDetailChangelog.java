package com.stpl.tech.kettle.data.model;

import com.stpl.tech.kettle.domain.model.CustomerOneViewData;
import com.stpl.tech.kettle.domain.model.CustomerTransactionViewData;

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.ColumnResult;
import javax.persistence.ConstructorResult;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.SqlResultSetMapping;
import javax.persistence.Table;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 14-03-2018.
 */

@Entity
@Table(name = "CUSTOMER_INFO_CHANGE_LOG")
@SqlResultSetMapping(name = "CustomerTransactionViewData", classes = @ConstructorResult(targetClass = CustomerTransactionViewData.class, columns = {
        @ColumnResult(name = "customerId", type = Integer.class),
        @ColumnResult(name = "firstOrderId", type = Integer.class),
        @ColumnResult(name = "firstOrderSource", type = String.class),
        @ColumnResult(name = "firstOrderChannelPartnerId", type = Integer.class),
        @ColumnResult(name = "firstOrderBusinessDate", type = Date.class),
        @ColumnResult(name = "firstOrderUnitId", type = Integer.class),
        @ColumnResult(name = "lastOrderId", type = Integer.class),
        @ColumnResult(name = "lastOrderSource", type = String.class),
        @ColumnResult(name = "lastOrderChannelPartnerId", type = Integer.class),
        @ColumnResult(name = "lastOrderBusinessDate", type = Date.class),
        @ColumnResult(name = "lastOrderUnitId", type = Integer.class),
        @ColumnResult(name = "totalDineInOrders", type = Integer.class),
        @ColumnResult(name = "totalDeliveryOrders", type = Integer.class),
        @ColumnResult(name = "totalOrders", type = Integer.class)}))
public class CustomerDetailChangelog {

    private Integer id;
    private Integer customerId;
    private String oldContact;
    private String oldName;
    private String oldEmail;
    private Date updatedAt;
    private String customerType; // OLD, NEW
    private String changedBy; //  TRUECALLER, SELF

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "LOG_ID", unique = true, nullable = false)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "CUSTOMER_ID")
    public Integer getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    @Column(name = "OLD_CONTACT")
    public String getOldContact() {
        return oldContact;
    }

    public void setOldContact(String oldContact) {
        this.oldContact = oldContact;
    }

    @Column(name = "OLD_NAME")
    public String getOldName() {
        return oldName;
    }

    public void setOldName(String oldName) {
        this.oldName = oldName;
    }

    @Column(name = "OLD_EMAIL")
    public String getOldEmail() {
        return oldEmail;
    }

    public void setOldEmail(String oldEmail) {
        this.oldEmail = oldEmail;
    }

    @Column(name = "UPDATED_AT")
    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    @Column(name = "CUSTOMER_TYPE")
    public String getCustomerType() {
        return customerType;
    }

    public void setCustomerType(String customerType) {
        this.customerType = customerType;
    }

    @Column(name = "CHANGED_BY")
    public String getChangedBy() {
        return changedBy;
    }

    public void setChangedBy(String changedBy) {
        this.changedBy = changedBy;
    }
}
