package com.stpl.tech.kettle.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "CASHBACK_OFFER_DATA")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class CashBackOfferData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Integer id;

    @Column(name = "UNIT_ID")
    private Integer unitId;
    @Column(name = "LAG_DAYS")
    private int lagDays;

    @Column(name = "VALIDITY_IN_DAYS")
    private int validityInDays;

    @Column(name = "OFFER_START_DATE")
    private Date offerStartDate;

    @Column(name = "OFFER_END_DATE")
    private Date offerEndDate;

    @Column(name = "MAX_NUMBER_OF_ORDER")
    private Integer maxNumberOfOrder;

    @Column(name = "CASHBACK_PERCENTAGE")
    private BigDecimal cashbackPercentage;

    @Column(name = "OFFER_STATUS")
    private String offerStatus;
}
