package com.stpl.tech.kettle.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * Created by shikhar on 31/5/19.
 */

@Entity
@Table(name = "ASSEMBLY_TAT_DATA")
public class AssemblyTATData {

    private Integer tatId;
    private int unitId;
    private Date businessDate;
    private String status;
    private String type;
    private Integer hotPrepTime;
    private Integer coldPrepTime;
    private Integer foodPrepTime;
    private Integer dispatchTime;
    private Integer orderPrepTime;
    private Date generationTime;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ASSEMBLY_TAT_DATA_ID", unique = true, nullable = false)
    public Integer getTatId() {
        return tatId;
    }

    public void setTatId(Integer tatId) {
        this.tatId = tatId;
    }

    @Column(name = "UNIT_ID", nullable = false)
    public int getUnitId() {
        return unitId;
    }

    public void setUnitId(int unitId) {
        this.unitId = unitId;
    }

    @Column(name = "BUSINESS_DATE", nullable = false)
    public Date getBusinessDate() {
        return businessDate;
    }

    public void setBusinessDate(Date businessDate) {
        this.businessDate = businessDate;
    }

    @Column(name = "TAT_STATUS", nullable = false)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "TAT_TYPE", nullable = false)
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Column(name = "HOT_PREP_TIME")
    public Integer getHotPrepTime() {
        return hotPrepTime;
    }

    public void setHotPrepTime(Integer hotPrepTime) {
        this.hotPrepTime = hotPrepTime;
    }

    @Column(name = "COLD_PREP_TIME")
    public Integer getColdPrepTime() {
        return coldPrepTime;
    }

    public void setColdPrepTime(Integer coldPrepTime) {
        this.coldPrepTime = coldPrepTime;
    }

    @Column(name = "FOOD_PREP_TIME")
    public Integer getFoodPrepTime() {
        return foodPrepTime;
    }

    public void setFoodPrepTime(Integer foodPrepTime) {
        this.foodPrepTime = foodPrepTime;
    }

    @Column(name = "DISPATCH_TIME")
    public Integer getDispatchTime() {
        return dispatchTime;
    }

    public void setDispatchTime(Integer dispatchTime) {
        this.dispatchTime = dispatchTime;
    }

    @Column(name = "ORDER_PREP_TIME")
    public Integer getOrderPrepTime() {
        return orderPrepTime;
    }

    public void setOrderPrepTime(Integer orderPrepTime) {
        this.orderPrepTime = orderPrepTime;
    }

    @Column(name = "GENERATION_TIME")
    public Date getGenerationTime() {
        return generationTime;
    }

    public void setGenerationTime(Date generationTime) {
        this.generationTime = generationTime;
    }
}
