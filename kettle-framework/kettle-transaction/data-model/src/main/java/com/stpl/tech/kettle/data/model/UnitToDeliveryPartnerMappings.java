/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.io.Serializable;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "UNIT_TO_DELIVERY_MAPPINGS")
public class UnitToDeliveryPartnerMappings implements Serializable {

	private static final long serialVersionUID = 6526856856092968419L;

	private int id;
	private int unitId;
	private DeliveryPartner deliveryPartner;
	private int priority;

	public UnitToDeliveryPartnerMappings() {
	}

	public UnitToDeliveryPartnerMappings(int unitDetail, DeliveryPartner deliveryPartner, int priority) {
		super();
		this.unitId = unitDetail;
		this.deliveryPartner = deliveryPartner;
		this.priority = priority;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "ID", unique = true, nullable = false)
	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	@Column(name = "UNIT_ID", nullable = false)
	public int getUnitId() {
		return this.unitId;
	}

	public void setUnitId(int unitDetail) {
		this.unitId = unitDetail;
	}

	@ManyToOne(fetch = FetchType.EAGER)
	@JoinColumn(name = "DELIVERY_PARTNER_ID", nullable = false)
	public DeliveryPartner getDeliveryPartner() {
		return deliveryPartner;
	}

	public void setDeliveryPartner(DeliveryPartner deliveryPartner) {
		this.deliveryPartner = deliveryPartner;
	}

	@Column(name = "PRIORITY")
	public int getPriority() {
		return priority;
	}

	public void setPriority(int priority) {
		this.priority = priority;
	}

	@Override
	public String toString() {
		return "UnitToDeliveryPartnerMappings [productId=" + id + ", unitId=" + unitId + ", deliveryPartnerId="
				+ deliveryPartner.getPartnerId() + ", priority=" + priority + "]";
	}
}
