package com.stpl.tech.kettle.customer.dao.impl;

import com.stpl.tech.kettle.core.CashCardStatus;
import com.stpl.tech.kettle.core.LoyaltyEventTransactionType;
import com.stpl.tech.kettle.core.LoyaltyEventType;
import com.stpl.tech.kettle.core.data.vo.CustomerCardInfo;
import com.stpl.tech.kettle.core.data.vo.FeedbackCount;
import com.stpl.tech.kettle.customer.dao.CustomerDao;
import com.stpl.tech.kettle.customer.dao.CustomerInfoDao;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.dao.impl.AbstractDaoImpl;
import com.stpl.tech.kettle.data.model.CashPacketEventStatus;
import com.stpl.tech.kettle.data.model.CustomerAddressInfo;
import com.stpl.tech.kettle.data.model.CustomerContactInfoMapping;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.CustomerProductFeedback;
import com.stpl.tech.kettle.data.model.LoyaltyScore;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.data.model.UnitTableMappingDetail;
import com.stpl.tech.kettle.data.util.DesiChaiConsumptionHelper;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.CustomerCashPacketLog;
import com.stpl.tech.kettle.domain.model.CustomerInfoDineIn;
import com.stpl.tech.kettle.domain.model.CustomerLoyaltyEntry;
import com.stpl.tech.kettle.domain.model.CustomerWalletEntry;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.master.core.data.vo.NotificationPayload;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.notification.publisher.CustomerCommunicationEventPublisher;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.hql.internal.ast.QuerySyntaxException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.jms.JMSException;
import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import com.stpl.tech.kettle.data.converter.DataConverter;

@Slf4j
@Repository
public class CustomerInfoDaoImpl extends AbstractDaoImpl implements CustomerInfoDao {


    @Autowired
    private CustomerDao customerDao;

    @Autowired
    MasterDataCache cache;

    @Autowired
    private CustomerCommunicationEventPublisher customerCommunicationEventPublisher;

    @Autowired
    protected EnvironmentProperties enviournment;

    private static final Logger LOG = LoggerFactory.getLogger(CustomerInfoDaoImpl.class);


    @Override
    public CustomerInfoDineIn getCustomerInfoForDineIn(int customerId) {
        String refCode = customerDao.getCustomerRefCode(customerId);
        LoyaltyScore score = customerDao.getLoyaltyScore(customerId);
        BigDecimal cash = customerDao.getAvailableCash(customerId);
        CustomerInfo   customerInfo =  customerDao.getCustomerInfoById(customerId);
        CustomerCardInfo customerCardInfo = getCashCardAmount(customerId);
//        int lastOrderId = getLastOrderDetails(customerId);
        OrderDetail lastOrder = getLastOrderDetails(customerId);
        int orderCount = getLastOrderCount(customerId);
        String lastOrderItemName = Objects.nonNull(lastOrder) ? getLastOrderItemName(customerId,lastOrder.getOrderId()) : null;
        BigDecimal totalSpent = getTotalSpent(customerId);
        Date lastUpdatedTime = getLastUpdateTime(customerId);
        return DataConverter.convert(customerId, score, cash, customerCardInfo, refCode,lastOrder ,lastOrderItemName,lastUpdatedTime,totalSpent,
                orderCount,customerInfo,cache);
    }

    private OrderDetail getLastOrderDetails(int customerId) {
        Query query = manager.createQuery("SELECT c  FROM OrderDetail c where c.customerId = :customerId order by c.billingServerTime desc").setMaxResults(1);
        query.setParameter("customerId", customerId);
        try {
            return (OrderDetail)(query.getSingleResult());
        } catch (NoResultException e) {
            LOG.info("Error in fetching last order details with customerId", customerId, e);
        }
        return null;
    }
    private int getLastOrderCount(int customerId) {
        Query query = manager.createQuery("SELECT count(c.orderId)  FROM OrderDetail c where c.customerId = :customerId").setMaxResults(1);
        query.setParameter("customerId", customerId);

        try {
            return ((Long)query.getSingleResult()).intValue();
        } catch (NoResultException e) {
            LOG.info("Error in fetching order count with customerId", customerId, e);
        }
        return 0;
    }

    public Date getLastUpdateTime(int customerId){
        Query query = manager.createQuery(
                "select cd.updatedAt from CustomerDetailChangelog cd where cd.customerId = :customerId order by cd.updatedAt desc").setMaxResults(1);
        query.setParameter("customerId" , customerId);
        try {
            if(query.getResultList().size() >0) {
                return (Date) query.getSingleResult();
            }
        } catch (Exception e){
            LOG.info("Error in fetching last Update Time with customerId", customerId, e);
        }
        return  null;
    }

    public String getLastOrderItemName(int customerId,int orderId){
        Query query = manager.createQuery("select od.productName from OrderItem od , LoyaltyScore ls where od.orderDetail.orderId = :orderId  and ls.customerId = :customerId").setMaxResults(1);
        query.setParameter("customerId",customerId);
        query.setParameter("orderId",orderId);
        try {
            return (String) query.getSingleResult();
        } catch (Exception e){
            LOG.info("Error in fetching last Order Item with customerId", customerId, e);
        }
        return null;
    }

    public BigDecimal getTotalSpent(int customerId){
        Query query = manager.createQuery(
                "select sum(settledAmount) from OrderDetail where customerId = :customerId ");
        query.setParameter("customerId",customerId);
        try {
            return (BigDecimal)query.getSingleResult();
        } catch (Exception e){
            LOG.info("Error in fetching total spent with customerId", customerId, e);
        }
        return null;
    }

    @Override
    public CustomerCardInfo getCashCardAmount(int customerId) {
        Query query = manager.createQuery(
                "select sum(cashPendingAmount) FROM CashCardDetail E where E.customerId = :customerId AND E.cashPendingAmount > :pendingAmount AND E.cardStatus = :cardStatus");
        query.setParameter("customerId", customerId);
        query.setParameter("pendingAmount", BigDecimal.ZERO);
        query.setParameter("cardStatus", CashCardStatus.ACTIVE.name());
        Object data = query.getSingleResult();
        return new CustomerCardInfo(data);
    }

    @Override
    public CustomerInfo getCustomerInfoById(int customerId) throws DataNotFoundException {
        CustomerInfo customerInfo;
        try {
            customerInfo = manager.find(CustomerInfo.class, customerId);
            customerInfo.setCustomerAddressInfos(null);
        } catch (NoResultException e) {
            throw new DataNotFoundException(String.format("Did not find Customer with ID : %d", customerId), e);
        }
        return customerInfo;
    }

    @Override
    public Customer getCustomerById(int customerId) throws DataNotFoundException {
        return customerDao.getCustomer(customerId);
    }

    @Override
    public List<CustomerWalletEntry> getWalletPurchaseEntry(int customerId) {
        Query query = manager.createQuery(
                "select 'PURCHASE', cd.cashInitialAmount , cd.activationTime, cd.initialOffer, 0.0 , cd.purchaseOrderId from CashCardDetail cd WHERE " +
                        "cd.cardStatus = 'ACTIVE' " +
                        "and cd.customerId = :customerId");
        query.setParameter("customerId", customerId);
        List<Object[]> list = query.getResultList();
        List<CustomerWalletEntry> customerWalletEntries = new ArrayList<>();
        if (list != null) {
            for (Object[] array : list) {
                insertWalletEntry(customerWalletEntries, array, false);

            }
        }
        return customerWalletEntries;
    }

    @Override
    public List<CustomerWalletEntry> getWalletPaidEntry(int customerId) {
        Query query = manager.createQuery(
                "    select 'PAID', ce.settlementAmount, ce.settlementTime , 0.0, 0.0, ce.orderId FROM CashCardDetail cd, " +
                        "    CashCardEvent ce " +
                        "    where cd.cashCardId =  ce.cashCardId " +
                        "    and ce.settlementStatus = 'ACTIVE' " +
                        "    and cd.cardStatus = 'ACTIVE' " +
                        "    and cd.customerId = :customerId");
        query.setParameter("customerId", customerId);
        List<Object[]> list = query.getResultList();
        List<CustomerWalletEntry> customerWalletEntries = new ArrayList<>();
        if (list != null) {
            for (Object[] array : list) {
                insertWalletEntry(customerWalletEntries, array, true);
            }
        }
        return customerWalletEntries;
    }

    private void insertWalletEntry(List<CustomerWalletEntry> customerWalletEntries, Object[] array, boolean paid) {
        Integer purchaseOrderId = (Integer) array[5];
        if (purchaseOrderId == null || customerWalletEntries.size() == 0) {
            addToList(customerWalletEntries, array, purchaseOrderId, paid);
        } else {
            CustomerWalletEntry previousEntry = customerWalletEntries.get(customerWalletEntries.size() - 1);
            /*
                Compare with previous entry if purchaseOrderId is same add both else add independently
             */
            if (previousEntry.getOrderId() == null || !previousEntry.getOrderId().equals( purchaseOrderId)) {
                addToList(customerWalletEntries, array, purchaseOrderId, paid);
            } else {
                BigDecimal amount = array[1] == null ? BigDecimal.ZERO : (BigDecimal) array[1];
                BigDecimal offer = array[3] == null ? BigDecimal.ZERO : paid ? new BigDecimal((Double) array[3]) :  (BigDecimal) array[3];
                previousEntry.setAmount(previousEntry.getAmount().add(amount));
                previousEntry.setOfferAmount(previousEntry.getOfferAmount().add(offer));
            }
        }
    }

    private void addToList(List<CustomerWalletEntry> customerWalletEntries, Object[] array, Integer purchaseOrderId, boolean paid) {
        BigDecimal offer = paid ? new BigDecimal((Double) array[3]) : (BigDecimal)  array[3];
        customerWalletEntries.add(new CustomerWalletEntry((String) array[0], (BigDecimal) array[1], (Date) array[2]
                , offer , new BigDecimal((Double) array[4]), purchaseOrderId));
    }


//    SELECT le.CUSTOMER_ID, le.TRANSACTION_TYPE, le.TRANSACTION_POINTS, le.TRANSACTION_CODE_TYPE , le.TRANSACTION_STATUS, od.ORDER_ID, le.TRANSACTION_TIME
//    FROM KETTLE_DUMP.LOYALTY_EVENTS le LEFT JOIN  ORDER_DETAIL od ON le.ORDER_ID = od.ORDER_ID
//    where le.CUSTOMER_ID = '9276' and le.TRANSACTION_STATUS = 'SUCCESS'  ORDER BY le.TRANSACTION_TIME ASC;

    @Override
    public List<CustomerLoyaltyEntry> getLoyaltyLedger(int customerId) {
        Query query = manager.createQuery(
                "select le.transactionType, (select od.unitId from OrderDetail od  where od.orderId = le.orderId ), " +
                        "(select od.channelPartnerId from OrderDetail od  where od.orderId = le.orderId), " +
                        "le.transactionCodeType, le.transactionCode, le.transactionStatus, le.transactionPoints " +
                        ", le.transactionTime " +
                        " from LoyaltyEvents le  " +
                        " WHERE le.customerId = :customerId and le.transactionCode <> :transactionCode " +
                        "  ORDER BY le.transactionTime  ASC");
        query.setParameter("customerId", customerId);
        query.setParameter("transactionCode", LoyaltyEventType.LOYALTY_GIFTING.name());
        List<Object[]> list = query.getResultList();
        List<CustomerLoyaltyEntry> customerLoyaltyEntries = new ArrayList<>();
        Integer total = 0;
        if (list != null) {
            for (Object[] array : list) {
                String unitName = null;
                try {
                    unitName = array[1] == null ? null : cache.getUnit((Integer) array[1]).getName();
                }catch (Exception e){
                    LOG.info("Error in getting unit detail");
                }
                String status = (String) array[5];
                if ("SUCCESS".equals(status)) {
                    total = ((String) array[0]).equals(LoyaltyEventTransactionType.DEBIT.name()) ? total + (Integer) array[6] : total + (Integer) array[6];
                }
                boolean added = ((String) array[0]).equals(LoyaltyEventTransactionType.DEBIT.name()) && total % 60 == 0 && "SUCCESS".equals(status);
                String partner = "";
                if(array[2] != null && cache.getChannelPartner((int) array[2]) != null) {
                    partner = cache.getChannelPartner((int) array[2]).getName();
                }
                customerLoyaltyEntries.add(new CustomerLoyaltyEntry((String) array[0], unitName, added, (String) array[3],
                        (String) array[4], status, (Integer) array[6], (Date) array[7], total, partner,null,null,null,null,null,null,null,null,null));
            }
        }
        return customerLoyaltyEntries;
    }

    @Override
    public List<CustomerLoyaltyEntry> getLoyaltyGiftingLedger(int customerId) {
        Query query=manager.createQuery(
                "select le.transactionType, (select lt.senderName from LoyaltyTransfer lt where lt.eventId=le.eventId)," +
                        "(select lt.senderId from LoyaltyTransfer lt where lt.eventId=le.eventId),"+
                        "(select lt.receiverName from LoyaltyTransfer lt where lt.eventId=le.eventId)," +
                        "(select lt.transferStatus from LoyaltyTransfer lt where lt.eventId=le.eventId)," +
                        "le.transactionCodeType, le.transactionCode, le.transactionStatus, le.transactionPoints ," +
                        "le.transactionTime, " +
                        " le.eventId ," +
                        "(select lt.senderBalance from LoyaltyTransfer lt where lt.eventId=le.eventId), " +
                        "(select lt.receiverBalance from LoyaltyTransfer lt where lt.eventId=le.eventId)," +
                        "le.loyaltyEventStatus, " +
                        "le.redeemedPoints " +
                        " from LoyaltyEvents le  " +
                        " WHERE le.customerId = :customerId and le.transactionCode = :transactionCode " +
                        "ORDER BY le.transactionTime  ASC");
        query.setParameter("customerId", customerId);
        query.setParameter("transactionCode", LoyaltyEventType.LOYALTY_GIFTING.name());
        List<Object[]> list = query.getResultList();
        List<CustomerLoyaltyEntry> customerLoyaltyEntries = new ArrayList<>();
        if(list!=null){
            Integer total=0;
            for(Object[] array:list){
                Integer balance=0;

                if ("SUCCESS".equals(array[7])) {
                    balance=((String) array[0]).equals(LoyaltyEventTransactionType.DEBIT.name())? (Integer)array[11] : (Integer)array[12];
                    total = total + (Integer) array[8];
                }
                log.info("Total->"+total);
                CustomerLoyaltyEntry customerLoyaltyEntry=new CustomerLoyaltyEntry((String) array[0], null, false, (String) array[5],
                        (String) array[6], (String) array[7], (Integer) array[8], (Date) array[9], total, null,
                        (String) array[1],(Integer) array[2],(String) array[3],(String) array[4],
                        (Integer) array[10],(Integer) array[11],(Integer) array[12] , (String) array[13],(Integer) array[14]);
                customerLoyaltyEntries.add(customerLoyaltyEntry);
        }
    }
        return customerLoyaltyEntries;
    }

    @Override
    public List<CustomerCashPacketLog> getCashExpLedger(int customerId, Date expDate) {
        String queryString = "SELECT  cd.TRANSACTION_CODE_TYPE, ci.FIRST_NAME, cd.EXPIRATION_DATE , cd.CURRENT_AMOUNT " +
                "FROM CASH_PACKET_DATA cd  LEFT OUTER JOIN " +
                "    CUSTOMER_INFO ci ON ci.CUSTOMER_ID = cd.REFERENT_ID " +
                "    where cd.CUSTOMER_ID = :customerId and cd.EXPIRATION_DATE > NOW() and cd.EXPIRATION_DATE < DATE(:expDate) " +
                " and cd.CURRENT_AMOUNT > 0 " +
                " and cd.EVENT_STATUS = :eventStatus " +
                " order by cd.EXPIRATION_DATE DESC ";
        Query query = manager.createNativeQuery(queryString);
        query.setParameter("customerId", customerId);
        query.setParameter("expDate", expDate);
        query.setParameter("eventStatus", CashPacketEventStatus.ACTIVE);
        List<Object[]> list = query.getResultList();
        List<CustomerCashPacketLog> customerCashPackets = new ArrayList<>();
        if (list != null) {
            for (Object[] array : list) {
                addToList(customerCashPackets, array);
            }
        }
        return customerCashPackets;
    }

    @Override
    public List<CustomerCashPacketLog> getLatestCashExpLedger(int customerId) {
        String queryString = "SELECT  cd.TRANSACTION_CODE_TYPE, ci.FIRST_NAME, cd.INITIAL_EXPIRATION_DATE , cd.CURRENT_AMOUNT " +
                "FROM CASH_PACKET_DATA cd  LEFT OUTER JOIN " +
                "    CUSTOMER_INFO ci ON ci.CUSTOMER_ID = cd.REFERENT_ID " +
                "    where cd.CUSTOMER_ID = :customerId and cd.INITIAL_EXPIRATION_DATE > NOW() " +
                " and cd.CURRENT_AMOUNT > 0 " +
                " and cd.EVENT_STATUS = :eventStatus " +
                " order by cd.INITIAL_EXPIRATION_DATE DESC limit 1 ";
        Query query = manager.createNativeQuery(queryString);
        query.setParameter("customerId", customerId);
        query.setParameter("eventStatus", CashPacketEventStatus.ACTIVE);
        List<CustomerCashPacketLog> customerCashPacketLogs = new ArrayList<>();
        try {
            List<Object[]> list = query.getResultList();
            if (list != null) {
                for (Object[] array : list) {
                    addToList(customerCashPacketLogs, array);
                }
            }
        } catch (NoResultException ex) {
            return customerCashPacketLogs;
        }
        return customerCashPacketLogs;
    }

    private void addToList(List<CustomerCashPacketLog> customerCashPacketLogs, Object[] array) {
        CustomerCashPacketLog customerCashPacketLog = new CustomerCashPacketLog();
        customerCashPacketLog.setBalance(null);
        customerCashPacketLog.setName((String) array[1]);
        customerCashPacketLog.setAmount((BigDecimal) array[3]);
        customerCashPacketLog.setCodeType((String) array[0]);
        customerCashPacketLog.setTime((Date) array[2]);
        customerCashPacketLog.setType(null);
        customerCashPacketLogs.add(customerCashPacketLog);
    }

    @Override
    public Boolean updateCustomerInfo(int customerId, String name, String acquisitionSource, String acquisitionToken) throws DataUpdationException {
        try {
            if (name != null && !name.equals("")) {
                CustomerInfo oldCustomerObj = customerDao.find(CustomerInfo.class, customerId);
                oldCustomerObj.setFirstName(name);
                if(!AppUtils.isBlank(acquisitionSource)){
                    oldCustomerObj.setAcquisitionSource(acquisitionSource);
                }
                if(!AppUtils.isBlank(acquisitionToken)){
                    oldCustomerObj.setAcquisitionToken(acquisitionToken);
                }
                oldCustomerObj = customerDao.update(oldCustomerObj);
                return oldCustomerObj != null;
            } else {
                return false;
            }
        } catch (Exception e) {
            throw new DataUpdationException(e.getMessage());
        }
    }

    @Override
    public Boolean updateCustomerBasicDetail(int customerId, CustomerInfo customerInfo,CustomerInfo oldCustomerObj) throws DataUpdationException {
        try {
            boolean isCustomerNameChange = false;
            if (oldCustomerObj != null) {
                if(!StringUtils.isEmpty(customerInfo.getFirstName())) {
                    if(customerInfo.getLastName() != null && !customerInfo.getLastName().trim().isEmpty()) {
                        customerInfo.setFirstName(customerInfo.getFirstName().trim() + " " + customerInfo.getLastName());
                    }
                    oldCustomerObj.setFirstName(customerInfo.getFirstName());
                    isCustomerNameChange = true;
                }
                if(!StringUtils.isEmpty(customerInfo.getGender())) {
                    oldCustomerObj.setGender(customerInfo.getGender());
                }
                if(Objects.isNull(oldCustomerObj.getDateOfBirth()) && customerInfo.getDateOfBirth() != null) {
                    oldCustomerObj.setDateOfBirth(customerInfo.getDateOfBirth());
                }
                if(Objects.isNull(oldCustomerObj.getAnniversary()) && customerInfo.getAnniversary() != null) {
                    oldCustomerObj.setAnniversary(customerInfo.getAnniversary());
                }
                if (Objects.nonNull(customerInfo.getOptWhatsapp())){
                    oldCustomerObj.setOptWhatsapp(customerInfo.getOptWhatsapp());
                    if(oldCustomerObj.getOptWhatsapp().equals(AppConstants.YES)) {
                        sendWhatsappOptInRequest(oldCustomerObj);
                    }
                }
                oldCustomerObj = customerDao.update(oldCustomerObj);
                if(isCustomerNameChange) {
                    try {
                        UnitTableMappingDetail utm = customerDao.findOpenTableByCustomerId(customerId);
                        if(Objects.nonNull(utm)){
                            utm.setCustomerName(customerInfo.getFirstName());
                            customerDao.update(utm);
                        }
                    } catch (Exception e) {
                        LOG.info("Error in updating name in table : {}", e);
                    }
                }
                return oldCustomerObj != null;
            } else {
                return false;
            }
        } catch (Exception e) {
            throw new DataUpdationException(e.getMessage());
        }
    }

    private void sendWhatsappOptInRequest(CustomerInfo customerResponse) throws JMSException {
        NotificationPayload payload = new NotificationPayload();
        payload.setCustomerId(customerResponse.getCustomerId());
        payload.setMessageType(AppConstants.WA_OPT_IN);
        payload.setContactNumber(customerResponse.getContactNumber());
        payload.setRequestTime(AppUtils.getCurrentTimestamp());
        Map<String,String> map = new HashMap<>();
        map.put("firstName",customerResponse.getFirstName());
        map.put("sourceDynamicText",customerResponse.getAcquisitionSource());
        payload.setPayload(map);
        customerCommunicationEventPublisher.publishCustomerCommunicationEvent(enviournment.getEnvironmentType().name(),payload);
    }

    @Override
    public Boolean updateCustomerEmail(int customerId, String email) throws DataUpdationException {
        try {
            if (email != null && !email.equals("") && !cache.getAllUnitsEmailId().contains(email)) {
                CustomerInfo oldCustomerObj = customerDao.find(CustomerInfo.class, customerId);
                oldCustomerObj.setEmailId(email);
                oldCustomerObj.setIsEmailVerified(AppConstants.NO);
                oldCustomerObj = customerDao.update(oldCustomerObj);
                return oldCustomerObj != null;
            } else {
                return false;
            }
        } catch (Exception e) {
            throw new DataUpdationException(e.getMessage());
        }
    }

    @Override
    public boolean addCustomerProductFeedback(int customerId, int productId, int rating, Integer sourceId,
                                              String source) {
        Integer mappedId = DesiChaiConsumptionHelper.getMappedProduct(productId);
        int pId = mappedId == null ? productId : mappedId;
        if (!hasCustomerProductFeedback(customerId, pId, source, AppConstants.ACTIVE)) {
            CustomerProductFeedback feedback = new CustomerProductFeedback();
            feedback.setAddTime(AppUtils.getCurrentTimestamp());
            feedback.setCustomerId(customerId);
            feedback.setProductId(pId);
            feedback.setRating(rating);
            feedback.setSource(source);
            feedback.setStatus(AppConstants.ACTIVE);
            feedback.setSourceId(sourceId);
            manager.persist(feedback);
            manager.flush();
            return true;
        } else {
            return false;
        }
    }


    @Override
    public boolean removeCustomerProductFeedback(int customerId, int productId, String source) {
        Query query = manager.createQuery("update CustomerProductFeedback E "
                + " set E.status = :inActive, E.updateTime = :currentTime " + "where E.customerId = :customerId "
                + "and E.productId = :productId and E.source = :source and E.status = :active");
        query.setParameter("inActive", AppConstants.IN_ACTIVE);
        query.setParameter("currentTime", AppUtils.getCurrentTimestamp());
        query.setParameter("customerId", customerId);
        query.setParameter("productId", productId);
        query.setParameter("source", source);
        query.setParameter("active", AppConstants.ACTIVE);
        query.executeUpdate();
        return true;
    }

    private boolean hasCustomerProductFeedback(int customerId, int productId, String source, String status) {
        Query query = manager.createQuery("from CustomerProductFeedback E "
                + " where E.customerId = :customerId "
                + "and E.productId = :productId and E.source = :source and E.status = :active");
        query.setParameter("customerId", customerId);
        query.setParameter("productId", productId);
        query.setParameter("source", source);
        query.setParameter("active", AppConstants.ACTIVE);
        List<CustomerProductFeedback> feedbacks = query.getResultList();

        return feedbacks != null && feedbacks.size() > 0;
    }

    @Override
    public List<FeedbackCount> getProductSpecificFeedbackCount() {
        List<FeedbackCount> list = new ArrayList<>();
        Query query = manager.createQuery("select E.productId, count(*) as cnt from CustomerProductFeedback E "
                + " where E.status = :active and E.rating IN (:promoters) group by E.productId");
        query.setParameter("active", AppConstants.ACTIVE);
        query.setParameter("promoters", AppConstants.PROMOTER_RATINGS);
        List<Object[]> data = query.getResultList();
        if (data != null && data.size() > 0) {
            for (Object[] objects : data) {
                FeedbackCount count = new FeedbackCount();
                count.setProductId((Integer) (objects[0]));
                count.setCount((Long) (objects[1]));
                list.add(count);
            }
        }
        return list;
    }

    @Override
    public List<FeedbackCount> getCustomerSpecificFeedbackCount(int customerId, String source) {
        List<FeedbackCount> list = new ArrayList<>();
        Query query = manager.createQuery("select E.productId, count(*) as cnt from CustomerProductFeedback E "
                + " where E.status = :active and E.customerId = :customerId and E.rating IN (:promoters) and E.source = :source group by E.productId");
        query.setParameter("active", AppConstants.ACTIVE);
        query.setParameter("promoters", AppConstants.PROMOTER_RATINGS);
        query.setParameter("customerId", customerId);
        query.setParameter("source", source);
        List<Object[]> data = query.getResultList();
        if (data != null && data.size() > 0) {
            for (Object[] objects : data) {
                FeedbackCount count = new FeedbackCount();
                count.setProductId((Integer) (objects[0]));
                count.setCount((Long) (objects[1]));
                list.add(count);
            }
        }
        return list;
    }


    @Override
    public List<CustomerCashPacketLog> getCashReferalLedger(int customerId) {
        String queryString = "SELECT " +
                "    cp.TRANSACTION_TYPE, " +
                "    cp.TRANSACTION_CODE, " +
                "    cp.TRANSACTION_CODE_TYPE, " +
                "    cp.TRANSACTION_AMOUNT, " +
                "    cp.TRANSACTION_TIME, " +
                "    ci.FIRST_NAME " +
                "FROM " +
                "    CASH_PACKET_DATA cd " +
                "        INNER JOIN " +
                "    CASH_PACKET_LOG_DATA cp ON cd.CASH_PACKET_ID = cp.CASH_PACKET_ID " +
                "        LEFT OUTER JOIN " +
                "    CUSTOMER_INFO ci ON ci.CUSTOMER_ID = cd.REFERENT_ID " +
                "WHERE " +
                "    cd.CUSTOMER_ID = :customerId " +
                "        AND cd.EVENT_STATUS <> 'INITIATED' " +
                "        AND cp.TRANSACTION_CODE <> 'SIGNUP_REFERRER' ORDER BY cp.TRANSACTION_TIME ASC";
        Query query = manager.createNativeQuery(queryString);

        query.setParameter("customerId", customerId);
        List<Object[]> list = query.getResultList();
        List<CustomerCashPacketLog> customerCashPacketLogs = new ArrayList<>();
        if (list != null) {
            for (Object[] array : list) {
                String name = (String) array[5];
                CustomerCashPacketLog customerCashPacketLog = new CustomerCashPacketLog();
                customerCashPacketLog.setType((String) array[0]);
                customerCashPacketLog.setTime((Date) array[4]);
                customerCashPacketLog.setCodeType((String) array[2]);
                customerCashPacketLog.setAmount((BigDecimal) array[3]);
                customerCashPacketLog.setBalance(null);
                if (array.length == 6) {
                    customerCashPacketLog.setName((String) array[5]);
                }
                customerCashPacketLogs.add(customerCashPacketLog);
            }
        }
        return customerCashPacketLogs;
    }

    @Override
    public Boolean updateCustomerAddress(int customerId, CustomerAddressInfo customerAddressInfo) throws DataUpdationException {
        try {
            CustomerInfo oldCustomerObj = customerDao.find(CustomerInfo.class, customerId);
            if (customerAddressInfo != null && oldCustomerObj != null) {
                manager.persist(customerAddressInfo);
                manager.flush();
                return true;
            }
        } catch (Exception e) {
            throw new DataUpdationException(e.getMessage());
        }
        return false;
    }

    @Override
    public List<String> getOldMappedContactNumbersList() {
        try {
            Query query = manager.createQuery("select E.newContactNumber from CustomerContactInfoMapping E");
            List<String> oldNosList = (List<String>)query.getResultList();
//            return (List<String>)query.getResultList();
            return oldNosList;
        } catch (NoResultException e) {
            LOG.error("No Result found for Mapped Contact Numbers List");
        }
        return null;
    }

    @Override
    public CustomerContactInfoMapping getCustomerContactInfoMapping(String oldContactNumber) {
        try {
            Query query = manager.createQuery("FROM CustomerContactInfoMapping E where E.oldContactNumber =:oldContactNumber");
            query.setParameter("oldContactNumber", oldContactNumber);
            CustomerContactInfoMapping customerContactInfoMapping = (CustomerContactInfoMapping)query.getSingleResult();
            return customerContactInfoMapping;
        } catch (NoResultException e) {
            LOG.error("No Result found for Mapped Contact Numbers List");
        }
        return null;
    }

    @Override
    public List<CustomerAddressInfo> getCustomerAddressInfoById(Integer customerId) {
        try {
//            Query query = manager.createQuery("FROM CustomerAddressInfo where contact =: contact");
            Query query = manager.createQuery("FROM CustomerAddressInfo where customerInfo.customerId = :customerId");
            query.setParameter("customerId", customerId);
            return query.getResultList();
        } catch (NoResultException e) {
            LOG.error("No CustomerAddressInfo Result found for Contact", customerId);
        }
        return null;
    }

    @Override
    public List<Object[]> getCustomerInfoByContactList(List<String> contactNumbers){
        try {
            Query query = manager.createQuery("select ci.contactNumber,ci.optWhatsapp FROM CustomerInfo ci where ci.contactNumber IN ( :contacts )");
            query.setParameter("contacts", contactNumbers);
            return query.getResultList();
        }
        catch (NoResultException e){
            LOG.info("No CustomerInfo Result found for the contacts");
        }
        catch (Exception e){
            LOG.error("Error while finding customer through contact numbers",e);
        }
        return null;
    }

    @Override
    public void updateCustomerOptIn(List<String> customerContacts){
        try{
            Query query = manager.createQuery("UPDATE CustomerInfo ci SET ci.optWhatsapp = :status " +
                    "WHERE ci.contactNumber IN ( :contacts )");
            query.setParameter("status",AppConstants.YES);
            query.setParameter("contacts",customerContacts);
            query.executeUpdate();
        } catch (NoResultException exception){
            LOG.error("No result found for updating whatsApp opt for existing customer leads");
        } catch (Exception e){
            LOG.error("Error while updating whatsApp opt for existing customer leads");
        }
    }

    @Override
    public boolean getCustomerOrderDetails(int customerId){
        try {
            Query query = manager.createQuery("Select od.orderId from OrderDetail od where od.customerId = :customerId AND od.brandId = :brandId");
            query.setParameter("customerId", customerId);
            query.setParameter("brandId",1);
            return CollectionUtils.isEmpty(query.getResultList()) ?true : false;
        }catch (NoResultException exception){
            LOG.error("No orders found for customer");
        } catch (Exception e){
            LOG.error("Error while getting orders of customers");
        }
        return false;
    }

    @Override
    public List<CustomerInfo> getCustomersWithDOBorAnniversary(){
        try {
            Query query = manager.createQuery("Select ci from CustomerInfo ci where ci.dateOfBirth is not NULL or ci.anniversary is not NULL");
            return query.getResultList();
        }catch (NoResultException exception){
            LOG.error("Customers not found");
        } catch (Exception e){
            LOG.error("Error while getting customers");
        }
        return null;
    }

    @Override
    public Integer getKettleCustomerId(String partnerCustomerId){
        try {
            Query query = manager.createNativeQuery("SELECT pm.CUSTOMER_ID FROM PARTNER_ORDER_CUSTOMER_MAPPING pm " +
                    "WHERE pm.PARTNER_CUSTOMER_ID = :partnerCustomerId");
            query.setParameter("partnerCustomerId", partnerCustomerId);
            Integer customerId = (Integer) query.getSingleResult();
            return customerId;
        } catch (Exception e) {
            LOG.error("Error in fetching Kettle customer for partner customer id : {} and error is : {}",
                    partnerCustomerId,e.getMessage());
        }
        return null;
    }




}
