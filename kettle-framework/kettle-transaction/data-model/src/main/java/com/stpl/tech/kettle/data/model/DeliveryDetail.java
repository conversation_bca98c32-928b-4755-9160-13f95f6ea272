/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "DELIVERY_DETAIL", uniqueConstraints = @UniqueConstraint(columnNames = "DELIVERY_TASK_ID"))
public class DeliveryDetail implements java.io.Serializable {

    private static final long serialVersionUID = -4944903530617249101L;

    private int id;
    private int deliveryPartnerId;
    private int orderId;
    private String deliveryTaskId;
    private String deliveryStatus;
    private String generatedOrderId;
    private Date statusUpdateTime;
    private Integer deliveryBoyId;
    private String deliveryBoyName;
    private String deliveryBoyPhoneNum;
    private String feedbackStatus = "N";
    private String feedbackCodeReceived;
    private String positiveFeedbackCode;
    private String negativeFeedbackCode;
    private String allotedNo;
    private String deliverySource;

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "ID", unique = true, nullable = false)
    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    @Column(name = "DELIVERY_PARTNER_ID", nullable = false)
    public int getDeliveryPartnerId() {
        return deliveryPartnerId;
    }

    public void setDeliveryPartnerId(int deliveryPartnerId) {
        this.deliveryPartnerId = deliveryPartnerId;
    }

    @Column(name = "ORDER_ID", nullable = false)
    public int getOrderId() {
        return orderId;
    }

    public void setOrderId(int orderId) {
        this.orderId = orderId;
    }

    @Column(name = "DELIVERY_TASK_ID", nullable = false)
    public String getDeliveryTaskId() {
        return deliveryTaskId;
    }

    public void setDeliveryTaskId(String deliveryTaskId) {
        this.deliveryTaskId = deliveryTaskId;
    }

    @Column(name = "DELIVERY_STATUS", nullable = false)
    public String getDeliveryStatus() {
        return deliveryStatus;
    }

    public void setDeliveryStatus(String deliveryStatus) {
        this.deliveryStatus = deliveryStatus;
    }

    @Column(name = "STATUS_UPDATE_TMSTMP", nullable = false)
    public Date getStatusUpdateTime() {
        return statusUpdateTime;
    }

    public void setStatusUpdateTime(Date statusUpdateTime) {
        this.statusUpdateTime = statusUpdateTime;
    }

    @Column(name = "DELIVERY_BOY_NAME", nullable = true)
    public String getDeliveryBoyName() {
        return deliveryBoyName;
    }

    public void setDeliveryBoyName(String deliveryBoyName) {
        this.deliveryBoyName = deliveryBoyName;
    }

    @Column(name = "DELIVERY_BOY_PHONE_NUM", nullable = true)
    public String getDeliveryBoyPhoneNum() {
        return deliveryBoyPhoneNum;
    }

    public void setDeliveryBoyPhoneNum(String deliveryBoyPhoneNum) {
        this.deliveryBoyPhoneNum = deliveryBoyPhoneNum;
    }

    @Column(name = "GENERATED_ORDER_ID", nullable = true)
    public String getGeneratedOrderId() {
        return generatedOrderId;
    }

    public void setGeneratedOrderId(String generatedOrderId) {
        this.generatedOrderId = generatedOrderId;
    }

    @Column(name = "FEEDBACK_STATUS", nullable = true)
    public String getFeedbackStatus() {
        return feedbackStatus;
    }

    public void setFeedbackStatus(String feedbackStatus) {
        if (feedbackStatus != null) {
            this.feedbackStatus = feedbackStatus;
        }
    }

    @Column(name = "FEEDBACK_CODE_RECEIVED", nullable = true)
    public String getFeedbackCodeReceived() {
        return feedbackCodeReceived;
    }

    public void setFeedbackCodeReceived(String feedbackCodeReceived) {
        this.feedbackCodeReceived = feedbackCodeReceived;
    }

    @Column(name = "POSITIVE_FEEDBACK_CODE", nullable = true)
    public String getPositiveFeedbackCode() {
        return positiveFeedbackCode;
    }

    public void setPositiveFeedbackCode(String positiveFeedbackCode) {
        this.positiveFeedbackCode = positiveFeedbackCode;
    }

    @Column(name = "NEGATIVE_FEEDBACK_CODE", nullable = true)
    public String getNegativeFeedbackCode() {
        return negativeFeedbackCode;
    }

    public void setNegativeFeedbackCode(String negativeFeedbackCode) {
        this.negativeFeedbackCode = negativeFeedbackCode;
    }
    @Column(name = "DELIVERY_BOY_ID", nullable = true)
	public Integer getDeliveryBoyId() {
		return deliveryBoyId;
	}

	public void setDeliveryBoyId(Integer deliveryBoyId) {
		this.deliveryBoyId = deliveryBoyId;
	}
	
	@Column(name = "ALLOTED_NO", nullable = true)
	public String getAllotedNo() {
		return allotedNo;
	}

	public void setAllotedNo(String allotedNo) {
		this.allotedNo = allotedNo;
	}
	
	@Column(name = "DELIVERY_SOURCE", nullable = true)
	public String getDeliverySource() {
		return deliverySource;
	}

	public void setDeliverySource(String deliverySource) {
		this.deliverySource = deliverySource;
	}
    
}
