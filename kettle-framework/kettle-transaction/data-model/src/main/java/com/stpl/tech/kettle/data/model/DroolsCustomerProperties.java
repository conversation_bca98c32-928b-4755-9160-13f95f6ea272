package com.stpl.tech.kettle.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

@Setter
@Getter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DroolsCustomerProperties {
    private Integer dineInFlag;
    private Integer  deliveryFlag;
    private Integer visitsCount;
    private Integer dineInVisitsCount;
    private Integer visitsCount30Days;
    private Integer visitsCount90Days;
    private Integer netSales;
    private Integer latestOrderDayDiff;
    private BigDecimal giftCardBalance;
    private Integer subscriptionExpiryDayDiff;
    private Integer loyaltyPointsBalance;
    private BigDecimal numberOfPax;
    private BigDecimal apc;
    private String posOfferString;
    private String digitalOfferString;
    private String isNewCustomer;
    private String ruleName;
    private String offerOccurrenceType;
    private Integer resultAPC;
    private String discountType;
    private Integer discountValue;
    private String offerTag;
    private String productIds;
    private String recomOfferString;
    private String freebieOfferString;
    private String dayPartOffer;
    private Map<String,String> dayPartOfferString = new HashMap<>();
}
