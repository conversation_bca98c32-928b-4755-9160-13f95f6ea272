package com.stpl.tech.kettle.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "FAV_CHAI_CUSTOMIZATION_DETAIL")
public class FavChaiCustomizationDetail implements Serializable {

    private static final long serialVersionUID = 5929432523260300592L;

    private Integer favChaiCustomizationDetailId;
    private CustomerFavChaiMapping customerFavChaiMapping;
    private int productId;
    private String dimension;
    private String name;
    private String type;
    private String defaultSetting;
    private BigDecimal quantity;
    private String source;
    private String shortCode ;
    private String uom;

    public FavChaiCustomizationDetail() {

    }

    public FavChaiCustomizationDetail(CustomerFavChaiMapping customerFavChaiMapping, int productId, String dimension, String name, String type, String defaultSetting, BigDecimal quantity, String source,String shortCode,String uom) {
        this.customerFavChaiMapping = customerFavChaiMapping;
        this.productId = productId;
        this.dimension = dimension;
        this.name = name;
        this.type = type;
        this.defaultSetting = defaultSetting;
        this.quantity = quantity;
        this.source=source;
        this.shortCode= shortCode;
        this.uom =uom;
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "FAV_CHAI_CUSTOMIZATION_DETAIL_ID", unique = true, nullable = false)
    public Integer getFavChaiCustomizationDetailId() {
        return favChaiCustomizationDetailId;
    }

    public void setFavChaiCustomizationDetailId(Integer favChaiCustomizationDetailId) {
        this.favChaiCustomizationDetailId = favChaiCustomizationDetailId;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "CUSTOMIZATION_ID", nullable = false)
    public CustomerFavChaiMapping getCustomerFavChaiMapping() {
        return customerFavChaiMapping;
    }

    public void setCustomerFavChaiMapping(CustomerFavChaiMapping customerFavChaiMapping) {
        this.customerFavChaiMapping = customerFavChaiMapping;
    }
    @Column(name = "ADDON_ID", nullable = false)
    public int getProductId() {
        return productId;
    }

    public void setProductId(int productId) {
        this.productId = productId;
    }

    @Column(name = "DIMENSION", nullable = true)
    public String getDimension() {
        return dimension;
    }
    public void setDimension(String dimension) {
        this.dimension = dimension;
    }
    @Column(name = "ADDON_NAME", nullable = true)
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Column(name = "ADDON_TYPE", nullable = true)
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Column(name = "DEFAULT_SETTING", nullable = true)
    public String getDefaultSetting() {
        return defaultSetting;
    }

    public void setDefaultSetting(String defaultSetting) {
        this.defaultSetting = defaultSetting;
    }

    @Column(name = "QUANTITY", nullable = true)
    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    @Column(name = "PRODUCT_SOURCE_SYSTEM", nullable = true)
    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    @Column(name = "SHORT_CODE")
    public String getShortCode() {
        return shortCode;
    }

    public void setShortCode(String shortCode) {
        this.shortCode = shortCode;
    }

    @Column(name = "UNIT_OF_MEASURE")
    public String getUom() {
        return uom;
    }

    public void setUom(String uom) {
        this.uom = uom;
    }
}
