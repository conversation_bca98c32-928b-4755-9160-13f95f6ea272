package com.stpl.tech.kettle.data.model;


import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "ASSEMBLY_NOTIFICATION_DATA")
public class AssemblyNotificationData {

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "KEY_ID", unique = true, nullable = false)
    private Integer keyId;

    @Column(name = "ASSEMBLY_NOTIFICATION_UNIT_WISE_DATA_ID",nullable = true)
    private Integer assemblyNotificationUnitWiseDataId;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "NOTIFICATION_START_TIME",nullable = true)
    private Date notificationStartTime;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "NOTIFICATION_END_TIME",nullable = true)
    private Date notificationEndTime;

    @Column(name = "NOTIFICATION_STATUS",nullable = true)
    private String notificationStatus;

    @Column(name = "RESPONSE_MESSAGE",nullable = true)
    private String responseMessage;

    @Column(name = "NOTIFICATION_TRIGGERED_BY",nullable = true)
    private Integer notificationTriggeredBy;
}
