/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "ORDER_ENQUIRY_ITEM")
public class OrderEnquiryItem {

	private int id;

	private int productId;

	private int orderderedQuantity;

	private int availableQuantity;

	private boolean isReplacementServed;

	private Integer orderId;
	
	private String enquiryOrderId;

	private Integer customerId;

	private int unitId;
	
	private Date enquiryTime;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "ORDER_ENQUIRY_ITEM_ID", unique = true, nullable = false)
	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	@Column(name = "PRODUCT_ID", nullable = false)
	public int getProductId() {
		return productId;
	}

	public void setProductId(int product) {
		this.productId = product;
	}

	@Column(name = "ORDERED_QUANTITY", nullable = false)
	public int getOrderderedQuantity() {
		return orderderedQuantity;
	}

	public void setOrderderedQuantity(int orderderedQuantity) {
		this.orderderedQuantity = orderderedQuantity;
	}

	@Column(name = "AVAILABLE_QUANTITY", nullable = false)
	public int getAvailableQuantity() {
		return availableQuantity;
	}

	public void setAvailableQuantity(int availableQuantity) {
		this.availableQuantity = availableQuantity;
	}

	@Column(name = "IS_REPLACEMENT_SERVED", nullable = false)
	public boolean isReplacementServed() {
		return isReplacementServed;
	}

	public void setReplacementServed(boolean isReplacementServed) {
		this.isReplacementServed = isReplacementServed;
	}

	@Column(name = "ORDER_ID", nullable = true)
	public Integer getOrderId() {
		return orderId;
	}

	public void setOrderId(Integer orderId) {
		this.orderId = orderId;
	}

	@Column(name = "CUSTOMER_ID", nullable = true)
	public Integer getCustomerId() {
		return customerId;
	}

	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}

	@Column(name = "UNIT_ID", nullable = true)
	public Integer getUnitId() {
		return unitId;
	}

	public void setUnitId(Integer unit) {
		this.unitId = unit;
	}

	@Column(name = "ENQUIRY_TIME", nullable = false)
	public Date getEnquiryTime() {
		return enquiryTime;
	}

	public void setEnquiryTime(Date enquiryTime) {
		this.enquiryTime = enquiryTime;
	}

	@Column(name = "ENQUIRY_ORDER_ID", nullable = true)
	public String getEnquiryOrderId() {
		return enquiryOrderId;
	}

	public void setEnquiryOrderId(String enquiryOrderId) {
		this.enquiryOrderId = enquiryOrderId;
	}
	
}
