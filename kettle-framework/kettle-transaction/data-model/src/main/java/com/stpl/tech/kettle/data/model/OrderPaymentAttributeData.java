/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "ORDER_PAYMENT_ATTRIBUTE_DATA")
public class OrderPaymentAttributeData {

	private int id;
	private int orderPaymentDetailId;
	private String attributeKey;
	private String attributeValue;
	private String attributeType;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "ORDER_PAYMENT_ATTRIBUTE_DATA_ID", unique = true, nullable = false)
	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	@Column(name = "ORDER_PAYMENT_DETAIL_ID", nullable = false)
	public int getOrderPaymentDetailId() {
		return orderPaymentDetailId;
	}

	public void setOrderPaymentDetailId(int paymentModeId) {
		this.orderPaymentDetailId = paymentModeId;
	}

	@Column(name = "ATTRIBUTE_KEY", nullable = true)
	public String getAttributeKey() {
		return attributeKey;
	}

	public void setAttributeKey(String attributeType) {
		this.attributeKey = attributeType;
	}

	@Column(name = "ATTRIBUTE_VALUE", nullable = true)
	public String getAttributeValue() {
		return attributeValue;
	}

	public void setAttributeValue(String attributeValue) {
		this.attributeValue = attributeValue;
	}

	@Column(name = "ATTRIBUTE_TYPE", nullable = true)
	public String getAttributeType() {
		return attributeType;
	}

	public void setAttributeType(String attributeStatus) {
		this.attributeType = attributeStatus;
	}

}
