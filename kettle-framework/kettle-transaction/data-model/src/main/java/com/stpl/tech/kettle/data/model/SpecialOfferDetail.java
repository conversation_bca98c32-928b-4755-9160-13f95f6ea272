package com.stpl.tech.kettle.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Entity
@Table(name = "SPECIAL_OFFER_DETAIL")
public class SpecialOfferDetail {
    private Integer specialOfferDetailId;
    private Integer customerId;
    private String contactNumber;
    private String offerType;
    private Integer campaignId;
    private String  campaignStrategy;
    private Date recordTime;
    private String utmSource;
    private String utmMedium;
    private String recordStatus;
    private Integer customerCampaignOfferDetailId;
    private Integer subscriptionPlanId;
    private Integer maxUsage;
    private String startDate;
    private String endDate;
    private String couponCode;
    private String offerText;
    private Integer cashPacketId;
    private BigDecimal cashAmount;
    private String tnc;
    private Integer flow;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "SPECIAL_OFFER_ID")
    public Integer getSpecialOfferDetailId() {
        return specialOfferDetailId;
    }

    public void setSpecialOfferDetailId(Integer specialOfferDetailId) {
        this.specialOfferDetailId = specialOfferDetailId;
    }

    @Column(name = "CUSTOMER_ID")
    public Integer getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    @Column(name = "CONTACT_NUMBER")
    public String getContactNumber() {
        return contactNumber;
    }

    public void setContactNumber(String contactNumber) {
        this.contactNumber = contactNumber;
    }

    @Column(name = "OFFER_TYPE")
    public String getOfferType() {
        return offerType;
    }

    public void setOfferType(String offerType) {
        this.offerType = offerType;
    }

    @Column(name = "CAMPAIGN_ID")
    public Integer getCampaignId() {
        return campaignId;
    }

    public void setCampaignId(Integer campaignId) {
        this.campaignId = campaignId;
    }

    @Column(name = "CAMPAIGN_STRATEGY")
    public String getCampaignStrategy() {
        return campaignStrategy;
    }

    public void setCampaignStrategy(String campaignStrategy) {
        this.campaignStrategy = campaignStrategy;
    }

    @Column(name = "RECORD_TIME")
    public Date getRecordTime() {
        return recordTime;
    }

    public void setRecordTime(Date creationTime) {
        this.recordTime = creationTime;
    }

    @Column(name = "UTM_SOURCE")
    public String getUtmSource() {
        return utmSource;
    }

    public void setUtmSource(String utmSource) {
        this.utmSource = utmSource;
    }

    @Column(name = "UTM_MEDIUM")
    public String getUtmMedium() {
        return utmMedium;
    }

    public void setUtmMedium(String utmMedium) {
        this.utmMedium = utmMedium;
    }

    @Column(name = "RECORD_STATUS")
    public String getRecordStatus() {
        return recordStatus;
    }

    public void setRecordStatus(String recordStatus) {
        this.recordStatus = recordStatus;
    }

    @Column(name = "CUSTOMER_CAMPAIGN_OFFER_DETAIL_ID")
    public Integer getCustomerCampaignOfferDetailId() {
        return customerCampaignOfferDetailId;
    }

    public void setCustomerCampaignOfferDetailId(Integer customerCampaignOfferDetailId) {
        this.customerCampaignOfferDetailId = customerCampaignOfferDetailId;
    }

    @Column(name = "SUBSCRIPTION_PLAN_ID")
    public Integer getSubscriptionPlanId() {
        return subscriptionPlanId;
    }

    public void setSubscriptionPlanId(Integer subscriptionPlanId) {
        this.subscriptionPlanId = subscriptionPlanId;
    }

    @Column(name = "MAX_USAGE")
    public Integer getMaxUsage() {
        return maxUsage;
    }

    public void setMaxUsage(Integer maxUsage) {
        this.maxUsage = maxUsage;
    }

    @Column(name = "START_DATE")
    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    @Column(name = "END_DATE")
    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    @Column(name = "COUPON_CODE")
    public String getCouponCode() {
        return couponCode;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }

    @Column(name = "OFFER_TEXT")
    public String getOfferText() {
        return offerText;
    }

    public void setOfferText(String offerText) {
        this.offerText = offerText;
    }

    @Column(name = "CASH_PACKET_ID")
    public Integer getCashPacketId() {
        return cashPacketId;
    }

    public void setCashPacketId(Integer cashPacketId) {
        this.cashPacketId = cashPacketId;
    }

    @Column(name = "CASH_AMOUNT")
    public BigDecimal getCashAmount() {
        return cashAmount;
    }

    public void setCashAmount(BigDecimal cashAmount) {
        this.cashAmount = cashAmount;
    }

    @Column(name = "TNC")
    public String getTnc() {
        return tnc;
    }

    public void setTnc(String tnc) {
        this.tnc = tnc;
    }

    @Column(name = "FLOW")
    public Integer getFlow() {
        return flow;
    }

    public void setFlow(Integer flow) {
        this.flow = flow;
    }
}
