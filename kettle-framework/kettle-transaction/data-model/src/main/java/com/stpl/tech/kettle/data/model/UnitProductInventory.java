/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

// Generated 21 Jul, 2015 8:03:01 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * UnitProductPricing generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "UNIT_PRODUCT_INVENTORY")
public class UnitProductInventory implements java.io.Serializable {

	private Integer unitProductInventoryId;
	private int unitId;
	private int productId;
	private int noOfUnits;
	private String productInventoryStatus;
	private Date lastUpdateTmstmp;
	private Date lastStockOutTime;
	private int expireQuantity;

	public UnitProductInventory() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "UNIT_PRODUCT_INVENTORY_ID", unique = true, nullable = false)
	public Integer getUnitProductInventoryId() {
		return this.unitProductInventoryId;
	}

	public void setUnitProductInventoryId(Integer unitProductInventoryId) {
		this.unitProductInventoryId = unitProductInventoryId;
	}

	@Column(name = "LAST_UPDATE_TMSTMP", nullable = false, length = 19)
	public Date getLastUpdateTmstmp() {
		return this.lastUpdateTmstmp;
	}

	public void setLastUpdateTmstmp(Date lastUpdateTmstmp) {
		this.lastUpdateTmstmp = lastUpdateTmstmp;
	}

	@Column(name = "LAST_STOCK_OUT_TIME", nullable = true, length = 19)
	public Date getLastStockOutTime() {
		return this.lastStockOutTime;
	}

	public void setLastStockOutTime(Date lastStockOutTime) {
		this.lastStockOutTime = lastStockOutTime;
	}

	@Column(name = "UNIT_ID", nullable = false)
	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	@Column(name = "PRODUCT_ID", nullable = false)
	public int getProductId() {
		return productId;
	}

	public void setProductId(int productId) {
		this.productId = productId;
	}

	@Column(name = "NO_OF_UNIT", nullable = false)
	public int getNoOfUnits() {
		return noOfUnits;
	}

	public void setNoOfUnits(int noOfUnits) {
		this.noOfUnits = noOfUnits;
	}

	@Column(name = "PRODUCT_INVENTORY_STATUS", nullable = false, length = 15)
	public String getProductInventoryStatus() {
		return productInventoryStatus;
	}

	public void setProductInventoryStatus(String productInventoryStatus) {
		this.productInventoryStatus = productInventoryStatus;
	}

	@Column(name = "EXPIRE_QUANTITY", nullable = false, length = 15)
	public int getExpireQuantity() {
		return expireQuantity;
	}

	public void setExpireQuantity(int expireQuantity) {
		this.expireQuantity = expireQuantity;
	}

}
