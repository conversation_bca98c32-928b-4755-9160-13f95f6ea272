package com.stpl.tech.kettle.data.model;


import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "SUPERU_ALERTS_DATA")
@Getter
@Setter
public class SuperUAlertsData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
     @Column(name = "ALERT_ID")
    Integer alertId;
    @Column(name = "UNIT_ID")
    Integer unitId;
    @Column(name = "STORE_NAME")
    String  storeName;
    @Column(name = "ERROR_TYPE")
    String errorType;
    @Column(name = "TIMESTAMP")
    String timestamp;
    @Column(name = "DESCRIPTION")
    String description;
}
