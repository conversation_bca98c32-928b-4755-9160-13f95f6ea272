package com.stpl.tech.kettle.clevertap.domain.model;

import java.io.Serializable;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.Setter;

@JsonInclude(JsonInclude.Include.NON_NULL)
@Getter
@Setter
public class ClevertapOfferEventData implements Serializable {

	private static final long serialVersionUID = -3725556041735278224L;

	@JsonProperty("OfferId")
	private Integer customerCampaignOfferDetailId;
	@JsonProperty("Available")
	private boolean available;
	@JsonProperty("OfferCode")
	private String offerCode;
	@JsonProperty("ValidityFrom")
	private String validityFrom;
	@JsonProperty("ValidityTill")
	private String validityTill;
	@JsonProperty("Text")
	private String text;
	@JsonProperty("CustomerType")
	private String customerType;
	@JsonProperty("ContentUrl")
	private String contentUrl;
	@JsonProperty("NotificationType")
	private String notificationType;
	@JsonProperty("ExistingOffer")
	private Boolean isExistingOffer;
	@JsonProperty("DaysLeft")
	private Integer daysLeft;
	@JsonProperty("ChannelPartnerId")
	private Integer channelPartnerId;
	@JsonProperty("CouponType")
	private String couponType;
	@JsonProperty("MaxUsage")
	private Integer maxUsage;
	@JsonProperty("Tnc")
	private String tnc;
	@JsonProperty("OfferType")
	private String offerType;
}
