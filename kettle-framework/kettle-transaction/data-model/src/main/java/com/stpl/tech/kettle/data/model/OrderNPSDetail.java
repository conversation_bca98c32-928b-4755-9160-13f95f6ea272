/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.CascadeType;

// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * OrderInfo generated by hbm2java
 */
@Entity
@Table(name = "ORDER_NPS_DETAIL")
public class OrderNPSDetail implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 2812859972251294612L;

	private Integer surveryResponseId;
	private Integer orderId;
	private String generatedOrderId;
	private String unitName;
	private Integer unitId;
	private String contactNumber;
	private Integer customerId;
	private Integer rating;
	private Date surveyCreationTime;
	private String question;
	private String response;
	private List<OrderNPSResponseData> questions = new ArrayList<OrderNPSResponseData>();

	public OrderNPSDetail() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "SURVEY_RESPONSE_ID", unique = true, nullable = false)
	public Integer getSurveryResponseId() {
		return this.surveryResponseId;
	}

	public void setSurveryResponseId(Integer feedbackId) {
		this.surveryResponseId = feedbackId;
	}

	@Column(name = "ORDER_ID", nullable = true)
	public Integer getOrderId() {
		return orderId;
	}

	public void setOrderId(Integer orderId) {
		this.orderId = orderId;
	}

	@Column(name = "CUSTOMER_ID")
	public Integer getCustomerId() {
		return this.customerId;
	}

	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}

	@Column(name = "CONTACT_NUMBER", nullable = true, length = 15)
	public String getContactNumber() {
		return contactNumber;
	}

	public void setContactNumber(String contactNumber) {
		this.contactNumber = contactNumber;
	}

	@Column(name = "GENERATED_ORDER_ID", nullable = true)
	public String getGeneratedOrderId() {
		return generatedOrderId;
	}

	public void setGeneratedOrderId(String generatedOrderId) {
		this.generatedOrderId = generatedOrderId;
	}

	@Column(name = "UNIT_NAME", nullable = true)
	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}

	@Column(name = "UNIT_ID", nullable = true)
	public Integer getUnitId() {
		return unitId;
	}

	public void setUnitId(Integer unitId) {
		this.unitId = unitId;
	}

	@Column(name = "NPS_SCORE", nullable = true)
	public Integer getRating() {
		return rating;
	}

	public void setRating(Integer rating) {
		this.rating = rating;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "SURVEY_CREATION_TIME", nullable = false, length = 19)
	public Date getSurveyCreationTime() {
		return surveyCreationTime;
	}

	public void setSurveyCreationTime(Date surveyCreationTime) {
		this.surveyCreationTime = surveyCreationTime;
	}
	@Column(name = "NPS_QUESTION", nullable = true)
	public String getQuestion() {
		return question;
	}

	public void setQuestion(String question) {
		this.question = question;
	}

	@Column(name = "NPS_RESPONSE", nullable = true)
	public String getResponse() {
		return response;
	}

	public void setResponse(String response) {
		this.response = response;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "surveryResponseId", cascade = CascadeType.PERSIST)
	public List<OrderNPSResponseData> getQuestions() {
		if(questions == null) {
			questions = new ArrayList<OrderNPSResponseData>();
		}
		return questions;
	}

	public void setQuestions(List<OrderNPSResponseData> questions) {
		this.questions = questions;
	}

}