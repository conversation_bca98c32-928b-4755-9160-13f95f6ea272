/*
 * Created By Shanmukh
 */

package com.stpl.tech.kettle.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "MONK_DIAGNOSIS_EVENT")
public class MonkDiagnosisEvent {

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "MONK_DIAGNOSIS_EVENT_ID", unique = true, nullable = false)
    private Integer monkDiagnosisEventId;

    @Column(name = "UNIT_ID",nullable = false)
    private Integer unitId;

    @Column(name = "USER_ID",nullable = false)
    private Integer userId;

    @Column(name = "ENTERED_USER_NAME")
    private String enteredUserName;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATED_AT",nullable = true)
    private Date createdAt;

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "LOGGED_AT_SERVER",nullable = true)
    private Date loggedAtServer;

    @Column(name = "MONK_NAME", nullable = true)
    private String monkName;

}
