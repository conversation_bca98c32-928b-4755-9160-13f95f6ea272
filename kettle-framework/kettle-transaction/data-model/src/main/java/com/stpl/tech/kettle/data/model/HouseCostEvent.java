/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * OrderInfo generated by hbm2java
 */
@Entity
@Table(name = "HOUSE_COST_EVENT")
public class HouseCostEvent implements java.io.Serializable {

	private static final long serialVersionUID = 5441681397850434848L;

	private Integer orderId;
	private Integer customerId;
	private int empId;
	private String cancelationReason;
	private Integer cancellationReasonId;
	private Integer complimentaryReasonId;
	private String wastageType;
	private Integer kettleOrderId;
	private Integer wastageSumoId;
	private int unitId;
	private Date generationTime;
	private BigDecimal totalAmount;
	private String orderSource;
	private Integer cancelledBy;
	private Integer cancelApprovedBy;
	private String customerName;
	private List<HouseCostItemData> orderItems = new ArrayList<HouseCostItemData>(0);
	private List<HouseCostConsumableData> consumableItems = new ArrayList<HouseCostConsumableData>(0);

	public HouseCostEvent() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "HOUSE_COST_EVENT_ID", unique = true, nullable = false)
	public Integer getOrderId() {
		return this.orderId;
	}

	public void setOrderId(Integer orderId) {
		this.orderId = orderId;
	}

	@Column(name = "CUSTOMER_ID", nullable = true)
	public Integer getCustomerId() {
		return this.customerId;
	}

	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}

	@Column(name = "EMP_ID", nullable = false)
	public int getEmpId() {
		return this.empId;
	}

	public void setEmpId(int empId) {
		this.empId = empId;
	}

	@Column(name = "CANCELATION_REASON", length = 100)
	public String getCancelationReason() {
		return this.cancelationReason;
	}

	public void setCancelationReason(String cancelationReason) {
		this.cancelationReason = cancelationReason;
	}

	@Column(name = "CANCELATION_REASON_ID")
	public Integer getCancellationReasonId() {
		return cancellationReasonId;
	}

	public void setCancellationReasonId(Integer cancelationReasonId) {
		this.cancellationReasonId = cancelationReasonId;
	}

	@Column(name = "COMPLIMENTARY_REASON_ID")
	public Integer getComplimentaryReasonId() {
		return complimentaryReasonId;
	}

	public void setComplimentaryReasonId(Integer complimentaryReasonId) {
		this.complimentaryReasonId = complimentaryReasonId;
	}

	@Column(name = "WASTAGE_TYPE", length = 20)
	public String getWastageType() {
		return wastageType;
	}

	public void setWastageType(String bookedWastage) {
		this.wastageType = bookedWastage;
	}

	@Column(name = "ORDER_SOURCE", nullable = true, length = 10)
	public String getOrderSource() {
		return this.orderSource;
	}

	public void setOrderSource(String orderSource) {
		this.orderSource = orderSource;
	}

	@Column(name = "UNIT_ID", nullable = false)
	public int getUnitId() {
		return this.unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "GENERATION_TIME", nullable = false, length = 19)
	public Date getGenerationTime() {
		return generationTime;
	}

	public void setGenerationTime(Date billingServerTime) {
		this.generationTime = billingServerTime;
	}

	@Column(name = "TOTAL_AMOUNT", precision = 10)
	public BigDecimal getTotalAmount() {
		return this.totalAmount;
	}

	public void setTotalAmount(BigDecimal totalAmount) {
		this.totalAmount = totalAmount;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "orderDetail")
	public List<HouseCostItemData> getOrderItems() {
		return this.orderItems;
	}

	public void setOrderItems(List<HouseCostItemData> orderItems) {
		this.orderItems = orderItems;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "orderDetail")
	public List<HouseCostConsumableData> getConsumableItems() {
		return consumableItems;
	}

	public void setConsumableItems(List<HouseCostConsumableData> consumableItems) {
		this.consumableItems = consumableItems;
	}

	@Column(name = "CANCELLED_BY", nullable = true)
	public Integer getCancelledBy() {
		return cancelledBy;
	}

	public void setCancelledBy(Integer cancelledBy) {
		this.cancelledBy = cancelledBy;
	}

	@Column(name = "CANCEL_APPROVED_BY", nullable = true)
	public Integer getCancelApprovedBy() {
		return cancelApprovedBy;
	}

	public void setCancelApprovedBy(Integer cancelApprovedBy) {
		this.cancelApprovedBy = cancelApprovedBy;
	}

	@Column(name = "CUSTOMER_NAME", nullable = true)
	public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	@Column(name = "KETTLE_ORDER_ID", nullable = true)
	public Integer getKettleOrderId() {
		return kettleOrderId;
	}

	public void setKettleOrderId(Integer wastageKettleId) {
		this.kettleOrderId = wastageKettleId;
	}

	@Column(name = "WASTAGE_SUMO_ID", nullable = true)
	public Integer getWastageSumoId() {
		return wastageSumoId;
	}

	public void setWastageSumoId(Integer wastageSumoId) {
		this.wastageSumoId = wastageSumoId;
	}

}
