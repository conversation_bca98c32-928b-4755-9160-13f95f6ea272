package com.stpl.tech.kettle.data.model;


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.util.ArrayList;
import java.util.List;

@SuppressWarnings("serial")
@Entity
@Table(name = "REFUND_OPTIONS")
public class RefundOptions implements java.io.Serializable{

    private Integer id;

    private Float amount;

    private String refundType;

    private OrderComplaint orderComplaint;


    @Id
//    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name="ID", unique = true, nullable = false)
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "AMOUNT", nullable = false)
    public Float getAmount() {
        return amount;
    }

    public void setAmount(Float amount) {
        this.amount = amount;
    }

    @Column(name = "REFUND_TYPE", nullable = false)
    public String getRefundType() {
        return refundType;
    }

    public void setRefundType(String refundType) {
        this.refundType = refundType;
    }

    @Id
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ORDER_ID", nullable = false)
    public OrderComplaint getOrderComplaint () {
        return orderComplaint;
    }

    public void setOrderComplaint(OrderComplaint orderComplaint) {
        this.orderComplaint = orderComplaint;
    }
}
