package com.stpl.tech.kettle.data.model;


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;
import java.util.List;

@Entity
@Table(name = "ORDER_FEEDBACK_RESPONSE")
public class OrderFeedbackResponse implements java.io.Serializable{

    private Integer orderFeedbackResponseId;
    private Integer feedbackId;
    private Integer orderId;
    private Integer customerId;
    private String customerName;
    private Date feedbackTime;
    private Integer orderRating;
    private Integer orderNPSRating;
    private String orderComment;
    private String userAgent;
    private String redirectUrl;
    private String channelPartnerCode;
    private String channelPartnerName;
    private String orderSource;
    private String customerCallback;
    private Integer maxRating;
    private Integer maxNPSRating;
    private String feedbackType;
    private String hasOrderRating;
    private String hasOrderNPSRating;
    private List<OrderItemFeedbackResponse> orderItemFeedbackResponse;


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ORDER_FEEDBACK_RESPONSE_ID")
    public Integer getOrderFeedbackResponseId() {
        return orderFeedbackResponseId;
    }

    public void setOrderFeedbackResponseId(Integer orderFeedbackResponseId) {
        this.orderFeedbackResponseId = orderFeedbackResponseId;
    }

    @Column(name = "FEEDBACK_ID")
    public Integer getFeedbackId() {
        return feedbackId;
    }

    public void setFeedbackId(Integer feedbackId) {
        this.feedbackId = feedbackId;
    }

    @Column(name = "ORDER_ID")
    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    @Column(name = "CUSTOMER_ID")
    public Integer getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    @Column(name = "CUSTOMER_NAME")
    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "FEEDBACK_TIME")
    public Date getFeedbackTime() {
        return feedbackTime;
    }

    public void setFeedbackTime(Date feedbackTime) {
        this.feedbackTime = feedbackTime;
    }

    @Column(name = "ORDER_RATING")
    public Integer getOrderRating() {
        return orderRating;
    }

    public void setOrderRating(Integer orderRating) {
        this.orderRating = orderRating;
    }

    @Column(name = "ORDER_NPS_RATING")
    public Integer getOrderNPSRating() {
        return orderNPSRating;
    }

    public void setOrderNPSRating(Integer orderNPSRating) {
        this.orderNPSRating = orderNPSRating;
    }

    @Column(name = "ORDER_COMMENT")
    public String getOrderComment() {
        return orderComment;
    }

    public void setOrderComment(String orderComment) {
        this.orderComment = orderComment;
    }

    @Column(name = "USER_AGENT")
    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    @Column(name = "REDIRECT_URL")
    public String getRedirectUrl() {
        return redirectUrl;
    }

    public void setRedirectUrl(String redirectUrl) {
        this.redirectUrl = redirectUrl;
    }

    @Column(name = "CHANNEL_PARTNER_CODE")
    public String getChannelPartnerCode() {
        return channelPartnerCode;
    }

    public void setChannelPartnerCode(String channelPartnerCode) {
        this.channelPartnerCode = channelPartnerCode;
    }

    @Column(name = "CHANNEL_PARTNER_NAME")
    public String getChannelPartnerName() {
        return channelPartnerName;
    }

    public void setChannelPartnerName(String channelPartnerName) {
        this.channelPartnerName = channelPartnerName;
    }

    @Column(name = "ORDER_SOURCE")
    public String getOrderSource() {
        return orderSource;
    }

    public void setOrderSource(String orderSource) {
        this.orderSource = orderSource;
    }

    @Column(name = "CUSTOMER_CALLBACK")
    public String getCustomerCallback() {
        return customerCallback;
    }

    public void setCustomerCallback(String customerCallback) {
        this.customerCallback = customerCallback;
    }

    @Column(name = "MAX_RATING")
    public Integer getMaxRating() {
        return maxRating;
    }

    public void setMaxRating(Integer maxRating) {
        this.maxRating = maxRating;
    }

    @Column(name = "MAX_NPS_RATING")
    public Integer getMaxNPSRating() {
        return maxNPSRating;
    }

    public void setMaxNPSRating(Integer maxNPSRating) {
        this.maxNPSRating = maxNPSRating;
    }

    @Column(name = "FEEDBACK_TYPE")
    public String getFeedbackType() {
        return feedbackType;
    }

    public void setFeedbackType(String feedbackType) {
        this.feedbackType = feedbackType;
    }

    @Column(name = "HAS_ORDER_RATING")
    public String getHasOrderRating() {
        return hasOrderRating;
    }

    public void setHasOrderRating(String hasOrderRating) {
        this.hasOrderRating = hasOrderRating;
    }

    @Column(name = "HAS_ORDER_NPS_RATING")
    public String getHasOrderNPSRating() {
        return hasOrderNPSRating;
    }

    public void setHasOrderNPSRating(String hasOrderNPSRating) {
        this.hasOrderNPSRating = hasOrderNPSRating;
    }

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "orderFeedbackResponse")
    public List<OrderItemFeedbackResponse> getOrderItemFeedbackResponse() {
        return orderItemFeedbackResponse;
    }

    public void setOrderItemFeedbackResponse(List<OrderItemFeedbackResponse> orderItemFeedbackResponse) {
        this.orderItemFeedbackResponse = orderItemFeedbackResponse;
    }
}
