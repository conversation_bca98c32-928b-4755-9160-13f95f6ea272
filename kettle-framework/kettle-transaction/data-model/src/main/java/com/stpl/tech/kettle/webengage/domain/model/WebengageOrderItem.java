package com.stpl.tech.kettle.webengage.domain.model;

import com.stpl.tech.kettle.domain.model.ComplimentaryDetail;

import java.math.BigDecimal;

/**
 * Created by Chaayos on 09-05-2017.
 */
public class WebengageOrderItem {

    private Integer productId;
    private String productName;
    private Integer quantity;
    private BigDecimal price;
    private BigDecimal amount;
    private String dimension;
    private ComplimentaryDetail complimentaryDetail;
    private Boolean hasBeenRedeemed;

    public WebengageOrderItem() {
    }

    public WebengageOrderItem(Integer productId, String productName, Integer quantity, BigDecimal price, BigDecimal amount, String dimension) {
        this.productId = productId;
        this.productName = productName;
        this.quantity = quantity;
        this.price = price;
        this.amount = amount;
        this.dimension = dimension;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    public ComplimentaryDetail getComplimentaryDetail() {
        return complimentaryDetail;
    }

    public void setComplimentaryDetail(ComplimentaryDetail complimentaryDetail) {
        this.complimentaryDetail = complimentaryDetail;
    }

    public Boolean getHasBeenRedeemed() {
        return hasBeenRedeemed;
    }

    public void setHasBeenRedeemed(Boolean hasBeenRedeemed) {
        this.hasBeenRedeemed = hasBeenRedeemed;
    }
}
