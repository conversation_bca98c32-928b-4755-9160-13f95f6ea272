package com.stpl.tech.kettle.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "WEB_OFFER_COUPON_REDEMPTION_DETAIL")
public class WebOfferCouponRedemptionDetail {
    Integer keyId;
    String phoneNumber;
    String couponCode;
    Date generationTime;

    public WebOfferCouponRedemptionDetail(String phoneNumber, String couponCode, Date generationTime) {
        this.phoneNumber = phoneNumber;
        this.couponCode = couponCode;
        this.generationTime = generationTime;
    }

    public WebOfferCouponRedemptionDetail() {

    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "KEY_ID")
    public Integer getKeyId() {
        return keyId;
    }

    public void setKeyId(Integer keyId) {
        this.keyId = keyId;
    }

    @Column(name = "PHONE_NUMBER")
    public String getPhoneNumber() {
        return phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    @Column(name = "COUPON_CODE")
    public String getCouponCode() {
        return couponCode;
    }

    public void setCouponCode(String couponCode) {
        this.couponCode = couponCode;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "GENERATION_TIME")
    public Date getGenerationTime() {
        return generationTime;
    }

    public void setGenerationTime(Date generationTime) {
        this.generationTime = generationTime;
    }
}
