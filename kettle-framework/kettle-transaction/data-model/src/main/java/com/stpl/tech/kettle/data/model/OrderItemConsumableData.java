package com.stpl.tech.kettle.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "ORDER_ITEM_CONSUMABLE_DATA")
public class OrderItemConsumableData {

	protected int id;
	protected int unitId;
	protected int month;
	protected int year;

	protected int menuProductId;
	protected String menuProductName;
	protected int menuProductQuanity;
	protected String menuProductDimension;
	protected int scmProductId;
	protected String scmProductName;
	protected BigDecimal scmProductQuantity;
	protected String scmProductUom;
	protected String source;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "CONSUMABLE_ID", unique = true, nullable = false)
	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	@Column(name = "UNIT_ID", nullable = false)
	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	@Column(name = "CONSUMPTION_MONTH", nullable = false)
	public int getMonth() {
		return month;
	}

	public void setMonth(int month) {
		this.month = month;
	}

	@Column(name = "CONSUMPTION_YEAR", nullable = false)
	public int getYear() {
		return year;
	}

	public void setYear(int year) {
		this.year = year;
	}

	@Column(name = "MENU_PRODUCT_ID", nullable = false)
	public int getMenuProductId() {
		return menuProductId;
	}

	public void setMenuProductId(int menuProductId) {
		this.menuProductId = menuProductId;
	}

	@Column(name = "MENU_PRODUCT_NAME", nullable = false)
	public String getMenuProductName() {
		return menuProductName;
	}

	public void setMenuProductName(String menuProductName) {
		this.menuProductName = menuProductName;
	}

	@Column(name = "MENU_PRODUCT_QUANTITY", nullable = false)
	public int getMenuProductQuanity() {
		return menuProductQuanity;
	}

	public void setMenuProductQuanity(int menuProductQuanity) {
		this.menuProductQuanity = menuProductQuanity;
	}

	@Column(name = "MENU_PRODUCT_DIMENSION", nullable = false)
	public String getMenuProductDimension() {
		return menuProductDimension;
	}

	public void setMenuProductDimension(String menuProductDimension) {
		this.menuProductDimension = menuProductDimension;
	}

	@Column(name = "SCM_PRODUCT_ID", nullable = false)
	public int getScmProductId() {
		return scmProductId;
	}

	public void setScmProductId(int scmProductId) {
		this.scmProductId = scmProductId;
	}

	@Column(name = "SCM_PRODUCT_NAME", nullable = false)
	public String getScmProductName() {
		return scmProductName;
	}

	public void setScmProductName(String scmProductName) {
		this.scmProductName = scmProductName;
	}

	@Column(name = "SCM_PRODUCT_QUANTITY", nullable = false)
	public BigDecimal getScmProductQuantity() {
		return scmProductQuantity;
	}

	public void setScmProductQuantity(BigDecimal scmProductQuantity) {
		this.scmProductQuantity = scmProductQuantity;
	}

	@Column(name = "SCM_PRODUCT_UOM", nullable = false)
	public String getScmProductUom() {
		return scmProductUom;
	}

	public void setScmProductUom(String scmProductUom) {
		this.scmProductUom = scmProductUom;
	}

	@Column(name = "ORDER_SOURCE", nullable = false)
	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

}
