/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are private by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */
package com.stpl.tech.kettle.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.stpl.tech.kettle.core.data.budget.vo.ExpenseField;
import com.stpl.tech.kettle.core.data.budget.vo.ExpenseField.ExpenseLabel;
import com.stpl.tech.kettle.core.data.budget.vo.ExpenseField.ExpenseRecordCategory;
import com.stpl.tech.kettle.core.data.budget.vo.ExpenseField.ScoreCardCategory;
import com.stpl.tech.kettle.core.data.budget.vo.ExpenseField.ServiceRecordCategory;

import java.math.BigDecimal;
import java.util.Date;

import static com.stpl.tech.kettle.core.data.budget.vo.ExpenseField.CalculationType.*;
import static com.stpl.tech.kettle.core.data.budget.vo.ExpenseField.ExpenseFieldEntity.VARIANCE;
import static com.stpl.tech.kettle.core.data.budget.vo.ExpenseField.ExpenseRecordCategory.*;
import static com.stpl.tech.kettle.core.data.budget.vo.ExpenseField.RestrictionType.ERROR;
import static com.stpl.tech.kettle.core.data.budget.vo.ExpenseField.RestrictionType.WARNING;
import static com.stpl.tech.kettle.core.data.budget.vo.ExpenseField.ScoreCardCategory.*;
import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "UNIT_EXPENDITURE_DETAIL")
public class UnitExpenditureDetail {

    // create ebita and net profit after depreciation

    private Integer detailId;
    private String status;

    /*
     * ExpenseFieldCategory.KEY_FIELDS
     */
    @ExpenseField(calculationType = NO_TYPE, category = KEY_FIELDS, detail = "Kettle Day Closue Id")
    private Integer dayClosureId;
    @ExpenseField(calculationType = NO_TYPE, category = KEY_FIELDS, detail = "SuMo Day Closue Id")
    private Integer sumoClosureId;
    @ExpenseField(calculationType = NO_TYPE, category = KEY_FIELDS, detail = "Record Type")
    private String calculation;
    @ExpenseField(calculationType = NO_TYPE, category = KEY_FIELDS, detail = "Unit Id", order = 0, orderLabel = ExpenseLabel.UNIT_ID)
    private int unitId;
    @ExpenseField(calculationType = NO_TYPE, category = KEY_FIELDS, detail = "Unit Name", order = 0, orderLabel = ExpenseLabel.UNIT_NAME)
    private String unitName;
    @ExpenseField(calculationType = NO_TYPE, category = KEY_FIELDS, detail = "Year")
    private int year;
    @ExpenseField(calculationType = NO_TYPE, category = KEY_FIELDS, detail = "Month")
    private int month;
    @ExpenseField(calculationType = NO_TYPE, category = KEY_FIELDS, detail = "Day")
    private int day;
    @ExpenseField(calculationType = NO_TYPE, category = KEY_FIELDS, detail = "Business Date", order = 0, orderLabel = ExpenseLabel.BUSINESS_DATE)
    private Date businessDate;
    @ExpenseField(calculationType = NO_TYPE, category = KEY_FIELDS, detail = "On Revenue Share")
    private String onRevenueShare;

    /*
     * ExpenseFieldCategory.SALES
     *
     */

    @ExpenseField(calculationType = EARNING, category = SALES, detail = "Total Tickets", order = 8, orderLabel = ExpenseLabel.TOTAL_TRANSACTIONS)
    private Integer ticket;
    @ExpenseField(calculationType = EARNING, category = SALES, detail = "Revenue")
    private BigDecimal revenue;
    @ExpenseField(calculationType = EARNING, category = SALES, detail = "Net Sales", order = 5, orderLabel = ExpenseLabel.NET_SALES)
    private BigDecimal netSales;
    @ExpenseField(calculationType = EARNING, category = SALES, detail = "Net Revenue", order = 3, orderLabel = ExpenseLabel.NET_REVENUE)
    private BigDecimal netRevenue;
    @ExpenseField(calculationType = EARNING, category = SALES, detail = "Employee Meal Sales")
    private BigDecimal employeeMealSales;
    @ExpenseField(calculationType = EARNING, category = SALES, detail = "Employee Meal GMV")
    private BigDecimal employeeMealGmv;
    @ExpenseField(calculationType = EARNING, category = SALES, detail = "Total APC", order = 9, orderLabel = ExpenseLabel.APC)
    private BigDecimal apc;
    @ExpenseField(calculationType = EARNING, category = SALES, detail = "Total GMV", order = 1, orderLabel = ExpenseLabel.GROSS_SALE)
    private BigDecimal gmv;
    @ExpenseField(calculationType = EARNING, category = SALES, detail = "Total Discount", order = 2, orderLabel = ExpenseLabel.DISCOUNT)
    private BigDecimal discount;
    @ExpenseField(calculationType = NO_TYPE, category = SALES, detail = "Total Discount Loyalty", order = 2, orderLabel = ExpenseLabel.DISCOUNT)
    private BigDecimal discountLoyalty;
    @ExpenseField(calculationType = NO_TYPE, category = SALES, detail = "Total Discount Marketing", order = 2, orderLabel = ExpenseLabel.DISCOUNT)
    private BigDecimal discountMarketing;
    @ExpenseField(calculationType = NO_TYPE, category = SALES, detail = "Total Discount Ops", order = 2, orderLabel = ExpenseLabel.DISCOUNT)
    private BigDecimal discountOps;
    @ExpenseField(calculationType = NO_TYPE, category = SALES, detail = "Total Discount BD", order = 2, orderLabel = ExpenseLabel.DISCOUNT)
    private BigDecimal discountBd;
    @ExpenseField(calculationType = NO_TYPE, category = SALES, detail = "Total Discount Employee FICO", order = 2, orderLabel = ExpenseLabel.DISCOUNT)
    private BigDecimal discountEmployeeFico;


    @ExpenseField(calculationType = NO_TYPE, category = SALES, detail = "Employee Discount Loyalty", order = 2, orderLabel = ExpenseLabel.DISCOUNT)
    private BigDecimal empDiscountLoyalty;
    @ExpenseField(calculationType = NO_TYPE, category = SALES, detail = "Employee Discount Marketing", order = 2, orderLabel = ExpenseLabel.DISCOUNT)
    private BigDecimal empDiscountMarketing;
    @ExpenseField(calculationType = NO_TYPE, category = SALES, detail = "Employee Discount Ops", order = 2, orderLabel = ExpenseLabel.DISCOUNT)
    private BigDecimal empDiscountOps;
    @ExpenseField(calculationType = NO_TYPE, category = SALES, detail = "Employee Discount Bd", order = 2, orderLabel = ExpenseLabel.DISCOUNT)
    private BigDecimal empDiscountBd;
    @ExpenseField(calculationType = NO_TYPE, category = SALES, detail = "Employee Discount Employee FICO", order = 2, orderLabel = ExpenseLabel.DISCOUNT)
    private BigDecimal empDiscountEmployeeFico;


    @ExpenseField(calculationType = EARNING, category = SALES, detail = "Delivery Gift Card Sales")
    private BigDecimal deliveryGiftCardSale;
    @ExpenseField(calculationType = EARNING, category = SALES, detail = "Delivery Gift Card Redemption")
    private BigDecimal deliveryGiftCardRedemption;
    @ExpenseField(calculationType = EARNING, category = SALES, detail = "Delivery Gift Card Net Sales")
    private BigDecimal deliveryGiftCardNetSale;
    @ExpenseField(calculationType = EARNING, category = SALES, detail = "Delivery Net Sales", order = 7, orderLabel = ExpenseLabel.DELIVERY_NET_SALE)
    private BigDecimal deliveryNetSales;
    @ExpenseField(calculationType = EARNING, category = SALES, detail = "Dine In Gift Card Sales")
    private BigDecimal dineInGiftCardSale;
    @ExpenseField(calculationType = EARNING, category = SALES, detail = "Dine In Gift Card Redemption")
    private BigDecimal dineInGiftCardRedemption;
    @ExpenseField(calculationType = EARNING, category = SALES, detail = "Dine In Gift Card Net Sales")
    private BigDecimal dineInGiftCardNetSale;
    @ExpenseField(calculationType = EARNING, category = SALES, detail = "Dine In Net Sales", order = 6, orderLabel = ExpenseLabel.DINE_IN_NET_SALE)
    private BigDecimal dineInNetSales;
    @ExpenseField(calculationType = EARNING, category = SALES, detail = "Dine In Tickets")
    private Integer dineInTicket;
    @ExpenseField(calculationType = EARNING, category = SALES, detail = "Dine In Sales")
    private BigDecimal dineInSales;
    @ExpenseField(calculationType = EARNING, category = SALES, detail = "Dine In APC")
    private BigDecimal dineInApc;
    @ExpenseField(calculationType = EARNING, category = SALES, detail = "Dine In GMV")
    private BigDecimal dineInGmv;
    @ExpenseField(calculationType = EARNING, category = SALES, detail = "Dine In Discount")
    private BigDecimal dineInDiscount;
    @ExpenseField(calculationType = NO_TYPE, category = SALES, detail = "Dine In Discount Loyalty")
    private BigDecimal dineInDiscountLoyalty;
    @ExpenseField(calculationType = NO_TYPE, category = SALES, detail = "Dine In Discount Marketing")
    private BigDecimal dineInDiscountMarketing;
    @ExpenseField(calculationType = NO_TYPE, category = SALES, detail = "Dine In Discount Ops")
    private BigDecimal dineInDiscountOps;
    @ExpenseField(calculationType = NO_TYPE, category = SALES, detail = "Dine In Discount BD")
    private BigDecimal dineInDiscountBd;
    @ExpenseField(calculationType = NO_TYPE, category = SALES, detail = "Dine In Discount Employee FICO")
    private BigDecimal dineInDiscountEmployeeFico;


    @ExpenseField(calculationType = EARNING, category = SALES, detail = "Delivery Tickets")
    private Integer deliveryTicket;
    @ExpenseField(calculationType = EARNING, category = SALES, detail = "Delivery Sales")
    private BigDecimal deliverySales;
    @ExpenseField(calculationType = EARNING, category = SALES, detail = "Delivery APC")
    private BigDecimal deliveryApc;
    @ExpenseField(calculationType = EARNING, category = SALES, detail = "Delivery GMV")
    private BigDecimal deliveryGmv;
    @ExpenseField(calculationType = EARNING, category = SALES, detail = "Delivery Discount")
    private BigDecimal deliveryDiscount;
    @ExpenseField(calculationType = NO_TYPE, category = SALES, detail = "Delivery Discount Loyalty")
    private BigDecimal deliveryDiscountLoyalty;
    @ExpenseField(calculationType = NO_TYPE, category = SALES, detail = "Delivery Discount Marketing")
    private BigDecimal deliveryDiscountMarketing;
    @ExpenseField(calculationType = NO_TYPE, category = SALES, detail = "Delivery Discount Ops")
    private BigDecimal deliveryDiscountOps;
    @ExpenseField(calculationType = NO_TYPE, category = SALES, detail = "Delivery Discount BD")
    private BigDecimal deliveryDiscountBd;
    @ExpenseField(calculationType = NO_TYPE, category = SALES, detail = "Delivery Discount Employee FICO")
    private BigDecimal deliveryDiscountEmployeeFico;
    @ExpenseField(calculationType = EARNING, category = SALES, detail = "Employee Meal Tickets")
    private Integer employeeMealTicket;
    @ExpenseField(calculationType = EARNING, category = SALES, detail = "Gift Card Sales")
    private BigDecimal giftCardSale;
    @ExpenseField(calculationType = EARNING, category = SALES, detail = "Gift Card Redemption")
    private BigDecimal giftCardRedemption;
    @ExpenseField(calculationType = EARNING, category = SALES, detail = "Gift Card Net Sales", order = 4, orderLabel = ExpenseLabel.NET_GC_BALANCE)
    private BigDecimal giftCardNetSale;

    /*
     * CalculationType.COST ExpenseFieldCategory.COGS
     *
     */

    @ExpenseField(calculationType = COST, category = COGS, detail = "Dine In COGS", order = 90, orderLabel = ExpenseLabel.COGS)
    private BigDecimal dineInCogs;
    @ExpenseField(calculationType = COST, category = COGS_TAX, detail = "Dine In COGS Tax", order = 91, orderLabel = ExpenseLabel.COGS_TAX)
    private BigDecimal dineInCogsTax;
    @ExpenseField(calculationType = COST, category = COGS, detail = "Delivery COGS", order = 90, orderLabel = ExpenseLabel.COGS)
    private BigDecimal deliveryCogs;
    @ExpenseField(calculationType = COST, category = COGS_TAX, detail = "Delivery COGS Tax", order = 91, orderLabel = ExpenseLabel.COGS_TAX)
    private BigDecimal deliveryCogsTax;
    @ExpenseField(calculationType = COST, category = COGS, detail = "Training COGS", order = 90, orderLabel = ExpenseLabel.COGS)
    private BigDecimal trainingCogs;
    @ExpenseField(calculationType = COST, category = COGS_TAX, detail = "Training COGS Tax", order = 91, orderLabel = ExpenseLabel.COGS_TAX)
    private BigDecimal trainingCogsTax;
    @ExpenseField(calculationType = COST, category = COGS, detail = "Stock Variance PCC", order = 90, orderLabel = ExpenseLabel.COGS)
    private BigDecimal variancePCC;
    @ExpenseField(calculationType = COST, category = COGS_TAX, detail = "Stock Variance PCC Tax", order = 91, orderLabel = ExpenseLabel.COGS_TAX)
    private BigDecimal variancePCCTax;
    @ExpenseField(calculationType = COST, category = COGS, detail = "Stock Variance YC", order = 90, orderLabel = ExpenseLabel.COGS)
    private BigDecimal varianceYC;
    @ExpenseField(calculationType = COST, category = COGS_TAX, detail = "Stock Variance YC Tax", order = 91, orderLabel = ExpenseLabel.COGS_TAX)
    private BigDecimal varianceYCTax;


    /*
     * Employee Meal
     *
     */

    @ExpenseField(calculationType = COST, category = ScoreCardCategory.EMPLOYEE_MEAL, detail = "Employee Meal COGS", order = 100, orderLabel = ExpenseLabel.EMPLOYEE_MEAL)
    private BigDecimal employeeMealCogs;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.EMPLOYEE_MEAL_TAX, detail = "Employee Meal COGS Tax", order = 101, orderLabel = ExpenseLabel.EMPLOYEE_MEAL_TAX)
    private BigDecimal employeeMealCogsTax;

    /*
     * ExpenseFieldCategory.WASTAGE_COST
     *
     */

    @ExpenseField(calculationType = COST, category = EXPIRY_WASTAGE, detail = "Unsatisfied Customer Cost", order = 110, orderLabel = ExpenseLabel.EXPIRY_WASTAGE)
    private BigDecimal unsatifiedCustomerCost;
    @ExpenseField(calculationType = COST, category = EXPIRY_WASTAGE_TAX, detail = "Unsatisfied Customer Cost Tax", order = 111, orderLabel = ExpenseLabel.EXPIRY_WASTAGE_TAX)
    private BigDecimal unsatifiedCustomerCostTax;
    @ExpenseField(calculationType = COST, category = EXPIRY_WASTAGE, detail = "PPE Cost", order = 110, orderLabel = ExpenseLabel.EXPIRY_WASTAGE)
    private BigDecimal ppeCost;
    @ExpenseField(calculationType = COST, category = EXPIRY_WASTAGE_TAX, detail = "PPE Cost Tax", order = 111, orderLabel = ExpenseLabel.EXPIRY_WASTAGE_TAX)
    private BigDecimal ppeCostTax;
    @ExpenseField(calculationType = COST, category = EXPIRY_WASTAGE, detail = "Expiry Wastage", order = 110, orderLabel = ExpenseLabel.EXPIRY_WASTAGE)
    private BigDecimal expiryWastage;
    @ExpenseField(calculationType = COST, category = EXPIRY_WASTAGE_TAX, detail = "Expiry Wastage Tax", order = 111, orderLabel = ExpenseLabel.EXPIRY_WASTAGE_TAX)
    private BigDecimal expiryWastageTax;
    @ExpenseField(calculationType = COST, category = EXPIRY_WASTAGE, detail = "Wastage Other", order = 110, orderLabel = ExpenseLabel.EXPIRY_WASTAGE)
    private BigDecimal wastageOther;
    @ExpenseField(calculationType = COST, category = EXPIRY_WASTAGE_TAX, detail = "Wastage Other Tax", order = 111, orderLabel = ExpenseLabel.EXPIRY_WASTAGE_TAX)
    private BigDecimal wastageOtherTax;

    /*
     * ExpenseFieldCategory.CONSUMABLE
     */

    @ExpenseField(calculationType = COST, category = CONSUMABLE, detail = "Consumable", order = 130, orderLabel = ExpenseLabel.CONSUMABLE, budgetType = ERROR)
    private BigDecimal consumable;
    @ExpenseField(calculationType = COST, category = CONSUMABLE_TAX, detail = "Consumable Tax", order = 131, orderLabel = ExpenseLabel.CONSUMABLE_TAX, budgetType = ERROR)
    private BigDecimal consumableTax;
    @ExpenseField(calculationType = COST, category = CONSUMABLE, detail = "Consumables Others", order = 130, orderLabel = ExpenseLabel.CONSUMABLE)
    private BigDecimal consumableOthers;
    @ExpenseField(calculationType = COST, category = CONSUMABLE, detail = "Consumables Others Tax", order = 131, orderLabel = ExpenseLabel.CONSUMABLE_TAX)
    private BigDecimal consumableOthersTax;
    @ExpenseField(calculationType = COST, category = CONSUMABLE, detail = "Consumables Utility")
    private BigDecimal consumableUtility;
    @ExpenseField(calculationType = COST, category = CONSUMABLE, detail = "Consumables Utility TAX")
    private BigDecimal consumableUtilityTax;
    @ExpenseField(calculationType = COST, category = CONSUMABLE, detail = "Consumables Stationary")
    private BigDecimal consumableStationary;
    @ExpenseField(calculationType = COST, category = CONSUMABLE, detail = "Consumables Stationary Tax")
    private BigDecimal consumableStationaryTax;
    @ExpenseField(calculationType = COST, category = CONSUMABLE, detail = "Consumables Uniform")
    private BigDecimal consumableUniform;
    @ExpenseField(calculationType = COST, category = CONSUMABLE, detail = "Consumables Uniform Tax")
    private BigDecimal consumableUniformTax;
    @ExpenseField(calculationType = COST, category = CONSUMABLE, detail = "Consumables Equipment")
    private BigDecimal consumableEquipment;
    @ExpenseField(calculationType = COST, category = CONSUMABLE, detail = "Consumables Equipment Tax")
    private BigDecimal consumableEquipmentTax;
    @ExpenseField(calculationType = COST, category = CONSUMABLE, detail = "Consumables Cutlery")
    private BigDecimal consumableCutlery;
    @ExpenseField(calculationType = COST, category = CONSUMABLE, detail = "Consumables Cutlery Tax")
    private BigDecimal consumableCutleryTax;
    @ExpenseField(calculationType = COST, category = CONSUMABLE, detail = "Consumables Disposable")
    private BigDecimal consumableDisposable;
    @ExpenseField(calculationType = COST, category = CONSUMABLE, detail = "Consumables Disposable Tax")
    private BigDecimal consumableDisposableTax;

    @ExpenseField(calculationType = COST, category = MAINTENANCE, detail = "Consumables Lhi")
    private BigDecimal consumableLhi;
    @ExpenseField(calculationType = COST, category = MAINTENANCE, detail = "Consumables Lhi Tax")
    private BigDecimal consumableLhiTax;
    @ExpenseField(calculationType = COST, category = CONSUMABLE, detail = "Consumables It")
    private BigDecimal consumableIt;
    @ExpenseField(calculationType = COST, category = CONSUMABLE, detail = "Consumables It Tax")
    private BigDecimal consumableItTax;
    @ExpenseField(calculationType = COST, category = MAINTENANCE, detail = "Consumable Maintenance")
    private BigDecimal consumableMaintenance;
    @ExpenseField(calculationType = COST, category = MAINTENANCE, detail = "Consumable Maintenance Tax")
    private BigDecimal consumableMaintenanceTax;
    @ExpenseField(calculationType = COST, category = CONSUMABLE, detail = "Consumables Kitchen Equipment")
    private BigDecimal consumableKitchenEquipment;
    @ExpenseField(calculationType = COST, category = CONSUMABLE, detail = "Consumables kitchen Equipment Tax")
    private BigDecimal consumableKitchenEquipmentTax;
    @ExpenseField(calculationType = COST, category = CONSUMABLE, detail = "Consumables Office Equipment")
    private BigDecimal consumableOfficeEquipment;
    @ExpenseField(calculationType = COST, category = CONSUMABLE, detail = "Consumables Office Equipment Tax")
    private BigDecimal consumableOfficeEquipmentTax;
    @ExpenseField(calculationType = COST, category = CONSUMABLE, detail = "Consumables Chai Monk")
    private BigDecimal consumableChaiMonk;
    @ExpenseField(calculationType = COST, category = CONSUMABLE, detail = "Consumables Chai Monk Tax")
    private BigDecimal consumableChaiMonkTax;



    /*
     * ExpenseFieldCategory.STOCK_VARIANCE
     */

    @ExpenseField(calculationType = COST, category = STOCK_VARIANCE, detail = "Stock Variance", entity = VARIANCE)
    private BigDecimal stockVariance;
    @ExpenseField(calculationType = COST, category = STOCK_VARIANCE, detail = "Stock Variance - Zero Variance", entity = VARIANCE, order = 140, orderLabel = ExpenseLabel.STOCK_VARIANCE)
    private BigDecimal varianceZero;
    @ExpenseField(calculationType = COST, category = STOCK_VARIANCE_TAX, detail = "Stock Variance Tax", entity = VARIANCE)
    private BigDecimal stockVarianceTax;
    @ExpenseField(calculationType = COST, category = STOCK_VARIANCE_TAX, detail = "Stock Variance Tax", entity = VARIANCE, order = 141, orderLabel = ExpenseLabel.STOCK_VARIANCE_TAX)
    private BigDecimal varianceZeroTax;

    /*
     * ExpenseFieldCategory.LOGISTICS
     */

    @ExpenseField(calculationType = COST, category = LOGISTICS, detail = "Logistic Charges", order = 150,
            orderLabel = ExpenseLabel.LOGISTICS, service = ServiceRecordCategory.LOGISTIC_CHARGES)
    private BigDecimal logisticCharges;
    @ExpenseField(calculationType = COST, category = LOGISTICS, detail = "Logistic Interstate Cold Vehicle", order = 150,
            orderLabel = ExpenseLabel.LOGISTICS, service = ServiceRecordCategory.LOGISTIC_INTRASTATE_COLD_VEHICLE)
    private BigDecimal logisticInterstateColdVehicle;
    @ExpenseField(calculationType = COST, category = LOGISTICS, detail = "Logistic Interstate Non Cold Vehicle", order = 150,
            orderLabel = ExpenseLabel.LOGISTICS, service = ServiceRecordCategory.LOGISTIC_INTRASTATE_NON_COLD_VEHICLE)
    private BigDecimal logisticInterstateNonColdVehicle;
    @ExpenseField(calculationType = COST, category = LOGISTICS, detail = "Logistic Interstate Air", order = 150,
            orderLabel = ExpenseLabel.LOGISTICS, service = ServiceRecordCategory.LOGISTIC_INTERSTATE_AIR)
    private BigDecimal logisticInterstateAir;
    @ExpenseField(calculationType = COST, category = LOGISTICS, detail = "Logistic Interstate Road", order = 150,
            orderLabel = ExpenseLabel.LOGISTICS, service = ServiceRecordCategory.LOGISTIC_INTERSTATE_ROAD)
    private BigDecimal logisticInterstateRoad;
    @ExpenseField(calculationType = COST, category = LOGISTICS, detail = "Logistic Interstate Train", order = 150,
            orderLabel = ExpenseLabel.LOGISTICS, service = ServiceRecordCategory.LOGISTIC_INTERSTATE_TRAIN)
    private BigDecimal logisticInterstateTrain;

    /*
     * ExpenseFieldCategory.FACILITIES_VARIABLE
     */
    @ExpenseField(calculationType = COST, category = FACILITIES_VARIABLE, detail = "Electricity Charges", order = 190, orderLabel = ExpenseLabel.FACILITIES_VARIABLE)
    private BigDecimal energyElectricity;
    @ExpenseField(calculationType = COST, category = FACILITIES_VARIABLE, detail = "DG Running Charges", order = 190, orderLabel = ExpenseLabel.FACILITIES_VARIABLE, budgetType = WARNING)
    private BigDecimal energyDGRunning;
    @ExpenseField(calculationType = COST, category = FACILITIES_VARIABLE, detail = "DG Running Charges Cafe", order = 190, expense = ExpenseRecordCategory.ENERGY_DG_RUNNING_CAFE, orderLabel = ExpenseLabel.FACILITIES_VARIABLE, budgetType = WARNING)
    private BigDecimal energyDGRunningCafe;
    @ExpenseField(calculationType = COST, category = FACILITIES_VARIABLE, detail = "Water Charges", order = 190, orderLabel = ExpenseLabel.FACILITIES_VARIABLE, budgetType = WARNING)
    private BigDecimal waterCharges;
    @ExpenseField(calculationType = COST, category = FACILITIES_VARIABLE, detail = "Water Charges Cafe", expense = ExpenseRecordCategory.WATER_CHARGES_CAFE, order = 190, orderLabel = ExpenseLabel.FACILITIES_VARIABLE, budgetType = WARNING)
    private BigDecimal waterChargesCafe;
    @ExpenseField(calculationType = COST, category = FACILITIES_VARIABLE, detail = "Maintenance Pest Control Cafe", expense = ExpenseRecordCategory.MAINTENANCE_PEST_CONTROL_CAFE, order = 190, orderLabel = ExpenseLabel.FACILITIES_VARIABLE, budgetType = WARNING)
    private BigDecimal maintenancePestControlCafe;
//	@ExpenseField(calculationType = COST, category = FACILITIES_VARIABLE, detail = "Maintenance Pest Control Cafe", expense = ExpenseRecordCategory.MAINTENANCE_PEST_CONTROL_CAFE, order = 190, orderLabel = ExpenseLabel.FACILITIES_VARIABLE, budgetType = WARNING)
//	private BigDecimal conveyanceOdc;

    /*
     * ExpenseFieldCategory.FACILITIES_FIXED
     */
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.FACILITIES_FIXED, detail = "Communication Internet", order = 250,
            orderLabel = ExpenseLabel.FACILITIES_FIXED, service = ServiceRecordCategory.COMMUNICATION_INTERNET)
    private BigDecimal communicationInternet;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.FACILITIES_FIXED, detail = "Communication Telephone", order = 250,
            orderLabel = ExpenseLabel.FACILITIES_FIXED, service = ServiceRecordCategory.COMMUNICATION_TELEPHONE)
    private BigDecimal communicationTelephone;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.FACILITIES_FIXED, detail = "Communication Internet Leased Line", order = 250,
            orderLabel = ExpenseLabel.FACILITIES_FIXED, service = ServiceRecordCategory.COMMUNICATION_ILL)
    private BigDecimal communicationILL;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.FACILITIES_FIXED, detail = "Payroll Processing Charges", order = 250,
            orderLabel = ExpenseLabel.FACILITIES_FIXED, service = ServiceRecordCategory.PAYROLL_PROCESSING_FEES)
    private BigDecimal payrollProcessingFee;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.FACILITIES_FIXED, detail = "EDC Rental", order = 250, orderLabel = ExpenseLabel.FACILITIES_FIXED,
        service = ServiceRecordCategory.EDC_RENTAL)
    private BigDecimal edcRental;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.FACILITIES_FIXED, detail = "System Rental", order = 250, orderLabel = ExpenseLabel.FACILITIES_FIXED,
        service = ServiceRecordCategory.SYSTEM_RENTAL)
    private BigDecimal systemRental;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.FACILITIES_FIXED, detail = "RO Rental", order = 250, orderLabel = ExpenseLabel.FACILITIES_FIXED,
        service = ServiceRecordCategory.RO_RENTAL)
    private BigDecimal roRental;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.FACILITIES_FIXED, detail = "Petty Cash Rental", order = 250, orderLabel = ExpenseLabel.FACILITIES_FIXED,
        service = ServiceRecordCategory.PETTY_CASH_RENTALS)
    private BigDecimal pettyCashRentals;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.FACILITIES_FIXED, detail = "Music Rental", order = 250, orderLabel = ExpenseLabel.FACILITIES_FIXED,
        service = ServiceRecordCategory.MUSIC_RENTALS)
    private BigDecimal musicRentals;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.FACILITIES_FIXED, detail = "Internet Partner Rental", order = 250, orderLabel = ExpenseLabel.FACILITIES_FIXED)
    private BigDecimal internetPartnerRental;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.FACILITIES_FIXED, detail = "Insurance - assets", order = 250, orderLabel = ExpenseLabel.FACILITIES_FIXED,
        service = ServiceRecordCategory.INSURANCE_ASSETS)
    private BigDecimal insuranceAssets;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.FACILITIES_FIXED, detail = "Insurance - CGL", order = 250, orderLabel = ExpenseLabel.FACILITIES_FIXED,
        service = ServiceRecordCategory.INSURANCE_CGL)
    private BigDecimal insuranceCGL;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.FACILITIES_FIXED, detail = "Insurance - D&O", order = 250, orderLabel = ExpenseLabel.FACILITIES_FIXED)
    private BigDecimal insuranceDnO;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.FACILITIES_FIXED, detail = "ODC Rental", order = 250,
            orderLabel = ExpenseLabel.FACILITIES_FIXED, service = ServiceRecordCategory.ODC_RENTAL)
    private BigDecimal odcRental;

    /*
     *
     * ExpenseFieldCategory.COMMISSION_CARD_OR_WALLETS
     *
     */
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.COMMISSION_CARD_OR_WALLETS, detail = "Credit Cards Transaction Charges", order = 200, orderLabel = ExpenseLabel.COMMISSION_CARD_WALLET)
    private BigDecimal creditCardTransactionCharges;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.COMMISSION_CARD_OR_WALLETS, detail = "Voucher Transaction Charges", order = 200, orderLabel = ExpenseLabel.COMMISSION_CARD_WALLET)
    private BigDecimal voucherTransactionCharges;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.COMMISSION_CARD_OR_WALLETS, detail = "Wallets Transaction Charges", order = 200, orderLabel = ExpenseLabel.COMMISSION_CARD_WALLET)
    private BigDecimal walletsTransactionCharges;

    /*
     * ExpenseFieldCategory.COMMISSION_CHANNEL_PARTNERS
     *
     */
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.COMMISSION_CHANNEL_PARTNERS, detail = "Channel Partner Commission", order = 201, orderLabel = ExpenseLabel.COMMISSION_CHANNEL_PARTNER)
    private BigDecimal commissionChannelPartners;

    @ExpenseField(calculationType = COST, category = ScoreCardCategory.CANCELLATION_CHARGES_CHANNEL_PARTNERS, detail = "Channel Partner Cancellation Charges", order = 201, orderLabel = ExpenseLabel.COMMISSION_CHANNEL_PARTNER)
    private BigDecimal cancellationChargesChannelPartners;

    /*
     * ExpenseFieldCategory.FACILITIES_PROPERTY
     */
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.FACILITIES_PROPERTY, detail = "Property Tax", order = 215, orderLabel = ExpenseLabel.FACILITIES_PROPERTY)
    private BigDecimal propertyTax;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.FACILITIES_PROPERTY, detail = "Proprty Fix Rent", order = 215, orderLabel = ExpenseLabel.FACILITIES_PROPERTY)
    private BigDecimal propertyFixRent;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.FACILITIES_PROPERTY, detail = "Revenue Share", order = 215, orderLabel = ExpenseLabel.FACILITIES_PROPERTY)
    private BigDecimal revenueShare;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.FACILITIES_PROPERTY, detail = "Revenue Share Dine In", order = 215, orderLabel = ExpenseLabel.FACILITIES_PROPERTY)
    private BigDecimal revenueShareDineIn;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.FACILITIES_PROPERTY, detail = "Revenue Share Delivery", order = 215, orderLabel = ExpenseLabel.FACILITIES_PROPERTY)
    private BigDecimal revenueShareDelivery;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.FACILITIES_PROPERTY, detail = "Fix CAM", order = 215, orderLabel = ExpenseLabel.FACILITIES_PROPERTY)
    private BigDecimal fixCAM;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.FACILITIES_PROPERTY, detail = "Fixed Chilling Charges", order = 215, orderLabel = ExpenseLabel.FACILITIES_PROPERTY)
    private BigDecimal chillingCharges;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.FACILITIES_PROPERTY, detail = "Fixed Marketing Charges", order = 215, orderLabel = ExpenseLabel.FACILITIES_PROPERTY)
    private BigDecimal marketingCharges;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.FACILITIES_PROPERTY, detail = "DG Rental", order = 215, orderLabel = ExpenseLabel.FACILITIES_PROPERTY,
        service = ServiceRecordCategory.DG_RENTAL)
    private BigDecimal dgRental;

    /*
     * ExpenseFieldCategory.ANY_OTHER_VARIABLE
     *
     */
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Conveyance Marketing", order = 195, expense = ExpenseRecordCategory.CONVEYANCE_MARKETING,
            orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
    private BigDecimal conveyanceMarketing;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Conveyance Operations", order = 195, expense = ExpenseRecordCategory.CONVEYANCE_OPERATIONS,
            orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
    private BigDecimal conveyanceOperations;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Conveyance Others", order = 195, expense = ExpenseRecordCategory.CONVEYANCE_OTHERS,
            orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
    private BigDecimal conveyanceOthers;
    @ExpenseField(calculationType = COST, category = COGS, detail = "Other Cogs", order = 90, expense = ExpenseRecordCategory.COGS_OTHERS,
            orderLabel = ExpenseLabel.COGS, budgetType = WARNING)
    private BigDecimal cogsOthers;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Marketing Data Analysis", expense = ExpenseRecordCategory.MARKETING_DATA_ANALYSIS,
            order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
    private BigDecimal marketingDataAnalysis;


    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Audit Fee", order = 195,
            orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE, service = ServiceRecordCategory.AUDIT_FEE)
    private BigDecimal auditFee;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Audit Fee - Out Of Pocket", order = 195,
            orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE, service = ServiceRecordCategory.AUDIT_FEE_OUT_OF_POCKET)
    private BigDecimal auditFeeOutOfPocket;
    //	@ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Bad Debts Written Off", order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
//	private BigDecimal badDebtsWrittenOff;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Brokrage", order = 195,
            orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE, service = ServiceRecordCategory.BROKERAGE)
    private BigDecimal brokerage;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Charity And Donations", order = 195,
            orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE, service = ServiceRecordCategory.CHARITY_AND_DONATIONS)
    private BigDecimal charityAndDonations;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Domestic Tickets And Hotels", order = 195,
            orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE, service = ServiceRecordCategory.DOMESTIC_TICKETS_AND_HOTELS)
    private BigDecimal domesticTicketsAndHotels;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "International Tickets And Hotels", order = 195,
            orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE, service = ServiceRecordCategory.INTERNATIONAL_TICKETS_AND_HOTELS)
    private BigDecimal internationalTicketsAndHotels;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "House Keeping Charges", order = 195,
            orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE, service = ServiceRecordCategory.HOUSEKEEPING_CHARGES)
    private BigDecimal houseKeepingCharges;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Late Fee Charges", order = 195,
            orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE, service = ServiceRecordCategory.LATE_FEE_CHARGES)
    private BigDecimal lateFeeCharges;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Opening Licenses Fees", order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
    private BigDecimal openingLicencesFees;

    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Miscellaneous Expenses", order = 195,
            orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE, service = ServiceRecordCategory.MISCELLANEOUS_EXPENSES)
    private BigDecimal miscellaneousExpenses;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Penalty", order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
    private BigDecimal penalty;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "PhotoCopy Expenses", expense = ExpenseRecordCategory.PHOTO_COPY_EXPENSES, order = 195,
            orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE, service = ServiceRecordCategory.PHOTO_COPY_EXPENSES)
    private BigDecimal photoCopyExpenses;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "PhotoCopy Expenses Cafe", expense = ExpenseRecordCategory.PHOTO_COPY_EXPENSES, order = 195,
            orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
    private BigDecimal photoCopyExpensesCafe;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "QCR Expense", order = 195,
            orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE, service = ServiceRecordCategory.QCR_EXPENSE)
    private BigDecimal qcrExpense;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Recuritment Consultants", order = 195,
            orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE, service = ServiceRecordCategory.RECRUITMENT_CONSULTANTS)
    private BigDecimal recuritmentConsultants;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Registration Charges", order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
    private BigDecimal registrationCharges;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "ROC Fees", order = 195,
            orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE, service = ServiceRecordCategory.ROC_FEES)
    private BigDecimal rocFees;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Security Guard Charges", order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE, service = ServiceRecordCategory.SECURITY_GUARD_CHARGES)
    private BigDecimal securityGuardCharges;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Conveyance ODC", expense = CONVEYANCE_ODC, order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
    private BigDecimal conveyanceOdc;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Travelling Expense", expense = ExpenseRecordCategory.TRAVELLING_EXPENSES, order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
    private BigDecimal travellingExpense;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Debit/Credit Balnce Written Off", order = 195,
            orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE, service = ServiceRecordCategory.DEBIT_CREDIT_WRITTEN_OFF)
    private BigDecimal debitCreditWrittenOff;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Designing Fees", order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
    private BigDecimal designingFees;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Difference In Exchange", order = 195,
            orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE, service = ServiceRecordCategory.DIFFENECE_IN_EXCHANGE)
    private BigDecimal differenceInExchange;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Service Charges Paid", order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
    private BigDecimal serviceChargesPaid;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Legal Charges", order = 195,
            orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE, service = ServiceRecordCategory.LEGAL_CHARGES)
    private BigDecimal legalCharges;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Professional Charges", order = 195,
            orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE, service = ServiceRecordCategory.PROFESSIONAL_CHARGES)
    private BigDecimal professionalCharges;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "News Paper Charges", order = 195, expense = NEWSPAPER_CHARGES_CAFE,
            orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE, budgetType = WARNING, service = ServiceRecordCategory.NEWSPAPER_CHARGES)
    private BigDecimal newsPaper;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "News Paper Charges Cafe", order = 195, expense = NEWSPAPER_CHARGES_CAFE,
            orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE, budgetType = WARNING)
    private BigDecimal newsPaperCafe;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Commission For Change", order = 195, expense = COMMISSION_CHANGE_CAFE, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE, budgetType = WARNING)
    private BigDecimal commissionChange;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Commission For Change Cafe", order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
    private BigDecimal commissionChangeCafe;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Staff Welfare Expenses", order = 195, expense = STAFF_WELFARE_EXPENSES,
            orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE, budgetType = WARNING, service = ServiceRecordCategory.STAFF_WELFARE_EXPENSES)
    private BigDecimal staffWelfareExpenses;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Staff Welfare Expenses Cafe", order = 195, expense = STAFF_WELFARE_EXPENSES,
            orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE, budgetType = WARNING)
    private BigDecimal staffWelfareExpensesCafe;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Courier Charges", order = 195, expense = COURIER_CHARGES,
            orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE, budgetType = WARNING, service = ServiceRecordCategory.COURIER_CHARGES)
    private BigDecimal courierCharges;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Courier Charges Cafe", order = 195, expense = COURIER_CHARGES,
            orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE, budgetType = WARNING)
    private BigDecimal courierChargesCafe;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Printing And Stationary Charges", order = 195, expense = PRINTING_AND_STATIONARY,
            orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE, budgetType = WARNING, service = ServiceRecordCategory.PRINTING_AND_STATIONARY)
    private BigDecimal printingAndStationary;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Printing And Stationary Charges Cafe", order = 195, expense = PRINTING_AND_STATIONARY,
            orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE, budgetType = WARNING)
    private BigDecimal printingAndStationaryCafe;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Business Promotion Charges", order = 195, expense = BUSINESS_PROMOTION,
            orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE, budgetType = WARNING, service = ServiceRecordCategory.BUSINESS_PROMOTION)
    private BigDecimal businessPromotion;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Business Promotion Charges Cafe", order = 195, expense = BUSINESS_PROMOTION,
            orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE, budgetType = WARNING)
    private BigDecimal businessPromotionCafe;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Stamp Duty Charges", order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
    private BigDecimal stampDutyCharges;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Rounded Off", order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
    private BigDecimal roundedOff;
    //	@ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Short and Excess", order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
//	private BigDecimal shortAndExcess;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Expense Others", order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
    private BigDecimal expenseOthers;

    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Fixed Parking Charges", order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
    private BigDecimal fixedParkingCharges;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "COGS Logistics", order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
    private BigDecimal cogsLogistics;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Pre Opening Cam Ele Water", order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
    private BigDecimal preOpeningCamEleWater;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Pre Opening Registration Charges", order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
    private BigDecimal preOpeningRegistrationCharges;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Pre Opening Stamp Duty Charges", order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
    private BigDecimal preOpeningStampDutyCharges;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Pre Opening Consumable Tax", order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
    private BigDecimal preOpeningConsumableTax;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Support", order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
    private BigDecimal support;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Corporate Marketing Channel Partner", order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE,
        service = ServiceRecordCategory.CORPORATE_MARKETING_CHANNEL_PARTNER)
    private BigDecimal corporateMarketingChannelPartner;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Pre Opening Ele Water", order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
    private BigDecimal preOpeningEleWater;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Pre Opening Cam", order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
    private BigDecimal preOpeningCam;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Interest On Fixed Deposit FICO", order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
    private BigDecimal interestOnFixedDepositFICO;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Liability No Longer Required Written Back", order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
    private BigDecimal liabilityNoLongerRequiredWrittenBack;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Interest On Term Loan", order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE,
        service = ServiceRecordCategory.INTEREST_ON_TERM_LOAN)
    private BigDecimal interestOnTermLoan;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Amortization Of Intangible Assets", order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
    private BigDecimal amortizationOfIntangibleAssets;


    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Security Deposit Property", order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
    private BigDecimal securityDepositProperty;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Security Deposit MVAT", order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
    private BigDecimal securityDepositMVAT;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Security Deposit Electricity", order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
    private BigDecimal securityDepositElectricity;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Marketing Discount Ecom", order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
    private BigDecimal marketingDiscountEcom;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Shipping Charges", order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
    private BigDecimal shippingCharges;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Other Transaction Charges", order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
    private BigDecimal otherTransactionCharges;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Discount Dealer Margin", order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
    private BigDecimal discountDealerMargin;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Performance Marketing Service", order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
    private BigDecimal performanceMarketingService;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Insurance Marine", order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
    private BigDecimal insuranceMarine;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Share Stamping Charges", order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
    private BigDecimal shareStampingCharges;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Other Charges Ecom", order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
    private BigDecimal otherChargesEcom;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Comission Channel Partner Fixed", order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
    private BigDecimal comissionChannelPartnerFixed;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Cogs Trading Goods", order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
    private BigDecimal cogsTradingGoods;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Royalty Fees", order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
    private BigDecimal royaltyFees;
    @ExpenseField(calculationType = COST, category = ANY_OTHER_VARIABLE, detail = "Freight Charges", order = 195, orderLabel = ExpenseLabel.ANY_OTHER_VARIABLE)
    private BigDecimal freightCharges;


    /*
     * ExpenseFieldCategory.DELIVERY_CHARGES
     */
    @ExpenseField(calculationType = COST, category = DELIVERY_CHARGES, detail = "Depreciation On Bike", order = 202, orderLabel = ExpenseLabel.DELIVERY_CHARGES)
    private BigDecimal depreciationOfBike;
    @ExpenseField(calculationType = COST, category = DELIVERY_CHARGES, detail = "Fuel Charges", expense = ExpenseRecordCategory.FUEL_CHARGES_CAFE, order = 202,
            orderLabel = ExpenseLabel.DELIVERY_CHARGES, service = ServiceRecordCategory.FUEL_CHARGES)
    private BigDecimal fuelCharges;
    @ExpenseField(calculationType = COST, category = DELIVERY_CHARGES, detail = "Fuel Charges Cafe", order = 202, orderLabel = ExpenseLabel.DELIVERY_CHARGES)
    private BigDecimal fuelChargesCafe;
    @ExpenseField(calculationType = COST, category = DELIVERY_CHARGES, detail = "Regular Vehicle Maintenance", expense = VEHICLE_REGULAR_MAINTENANCE_CAFE, order = 202, orderLabel = ExpenseLabel.DELIVERY_CHARGES, budgetType = WARNING)
    private BigDecimal vehicleRegularMaintenance;
    @ExpenseField(calculationType = COST, category = DELIVERY_CHARGES, detail = "Insurance Vehicle", order = 202, orderLabel = ExpenseLabel.DELIVERY_CHARGES)
    private BigDecimal insuranceVehicle;
    @ExpenseField(calculationType = COST, category = DELIVERY_CHARGES, detail = "Parking Charges", expense = PARKING_CHARGES_CAFE, order = 202, orderLabel = ExpenseLabel.DELIVERY_CHARGES, budgetType = WARNING)
    private BigDecimal parkingCharges;
    @ExpenseField(calculationType = COST, category = DELIVERY_CHARGES, detail = "Delivery Charge Variable", order = 202,
            orderLabel = ExpenseLabel.DELIVERY_CHARGES, service = ServiceRecordCategory.DELIVERY_CHARGES_VARIABLE)
    private BigDecimal deliveryChargesVariable;

    /*
     * ExpenseFieldCategory.MAINTENANCE
     */

    @ExpenseField(calculationType = COST, category = MAINTENANCE, detail = "Building Maintenance", order = 203, expense = BUILDING_MAINTENANCE_CAFE, orderLabel = ExpenseLabel.MAINTENANCE, budgetType = WARNING)
    private BigDecimal buildingMaintenance;
    @ExpenseField(calculationType = COST, category = MAINTENANCE, detail = "Cleaning Charges", order = 203, expense = CLEANING_CHARGES,
            orderLabel = ExpenseLabel.MAINTENANCE, budgetType = WARNING, service = ServiceRecordCategory.CLEANING_CHARGES)
    private BigDecimal cleaningCharges;
    @ExpenseField(calculationType = COST, category = MAINTENANCE, detail = "Cleaning Charges Cafe", order = 203, expense = CLEANING_CHARGES,
            orderLabel = ExpenseLabel.MAINTENANCE, budgetType = WARNING)
    private BigDecimal cleaningChargesCafe;
    @ExpenseField(calculationType = COST, category = MAINTENANCE, detail = "Maintenance Computer", order = 203, expense = COMPUTER_IT_MAINTENANCE_CAFE, orderLabel = ExpenseLabel.MAINTENANCE, budgetType = WARNING)
    private BigDecimal computerMaintenance;
    @ExpenseField(calculationType = COST, category = MAINTENANCE, detail = "Pest Control Charges", order = 203, expense = MAINTENANCE_PEST_CONTROL,
            orderLabel = ExpenseLabel.MAINTENANCE, budgetType = WARNING, service = ServiceRecordCategory.PEST_CONTROL_CHARGES)
    private BigDecimal pestControlCharges;
    @ExpenseField(calculationType = COST, category = MAINTENANCE, detail = "Equipment Maintenance", order = 203, expense = EQUIPMENT_MAINTENANCE_CAFE, orderLabel = ExpenseLabel.MAINTENANCE, budgetType = WARNING)
    private BigDecimal equipmentMaintenance;
    @ExpenseField(calculationType = COST, category = MAINTENANCE, detail = "Pronto AMC", order = 203, orderLabel = ExpenseLabel.MAINTENANCE, budgetType = WARNING,
        service = ServiceRecordCategory.PRONTO_AMC)
    private BigDecimal prontoAMC;
    @ExpenseField(calculationType = COST, category = MAINTENANCE, detail = "RO AMC", order = 203, orderLabel = ExpenseLabel.MAINTENANCE, budgetType = WARNING)
    private BigDecimal roAMC;
    @ExpenseField(calculationType = COST, category = MAINTENANCE, detail = "Others AMC", order = 203, orderLabel = ExpenseLabel.MAINTENANCE, budgetType = WARNING,
        service = ServiceRecordCategory.OTHERS_AMC)
    private BigDecimal othersAMC;
    @ExpenseField(calculationType = COST, category = MAINTENANCE, detail = "Others Maintenance", order = 203, orderLabel = ExpenseLabel.MAINTENANCE, budgetType = WARNING,
        service = ServiceRecordCategory.OTHERS_MAINTENANCE)
    private BigDecimal othersMaintenance;
    @ExpenseField(calculationType = COST, category = MAINTENANCE, detail = "R&D Engineering Expenses", expense = ExpenseRecordCategory.RND_ENGINEERING_EXPENSE, order = 203,
            orderLabel = ExpenseLabel.MAINTENANCE, budgetType = WARNING, service = ServiceRecordCategory.RND_ENGINEERING_EXPENSE)
    private BigDecimal rnDEngineeringExpenses;
    @ExpenseField(calculationType = COST, category = MAINTENANCE, detail = "Air Conditioner Amc", order = 203,
            orderLabel = ExpenseLabel.MAINTENANCE, budgetType = WARNING, service = ServiceRecordCategory.AIR_CONDITIONER_AMC)
    private BigDecimal airConditionerAmc;

    @ExpenseField(calculationType = COST, category = MAINTENANCE, detail = "Others Service Charges", order = 203,
            orderLabel = ExpenseLabel.MAINTENANCE, budgetType = WARNING, service = ServiceRecordCategory.NONE)
    private BigDecimal otherServiceCharges;

    /*
     * ExpenseFieldCategory.MANPOWER_INCENTIVE
     */

    @ExpenseField(calculationType = COST, category = ScoreCardCategory.MANPOWER_INCENTIVE, detail = "Salary Incentive", order = 204, orderLabel = ExpenseLabel.MANPOWER_INCENTIVE)
    private BigDecimal salaryIncentive;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.MANPOWER_INCENTIVE, detail = "Sales Incentive", order = 204, orderLabel = ExpenseLabel.MANPOWER_INCENTIVE)
    private BigDecimal salesIncentive;

    /*
     * ExpenseFieldCategory.MANPOWER_FIXED
     */

    @ExpenseField(calculationType = COST, category = ScoreCardCategory.MANPOWER_FIXED, detail = "BYOD Charges", order = 205, orderLabel = ExpenseLabel.MANPOWER_FIXED)
    private BigDecimal byodCharges;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.MANPOWER_FIXED, detail = "Car Lease", order = 205, orderLabel = ExpenseLabel.MANPOWER_FIXED)
    private BigDecimal carLease;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.MANPOWER_FIXED, detail = "Driver Salary", order = 205, orderLabel = ExpenseLabel.MANPOWER_FIXED)
    private BigDecimal driverSalary;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.MANPOWER_FIXED, detail = "Gratuity", order = 205, orderLabel = ExpenseLabel.MANPOWER_FIXED)
    private BigDecimal gratuity;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.MANPOWER_FIXED, detail = "Insurnace Accidental", order = 205, orderLabel = ExpenseLabel.MANPOWER_FIXED,
        service = ServiceRecordCategory.INSURANCE_ACCIDENTAL)
    private BigDecimal insurnaceAccidental;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.MANPOWER_FIXED, detail = "Insurnace Medical", order = 205, orderLabel = ExpenseLabel.MANPOWER_FIXED,
        service = ServiceRecordCategory.INSURANCE_MEDICAL)
    private BigDecimal insurnaceMedical;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.MANPOWER_FIXED, detail = "Salary", order = 205, orderLabel = ExpenseLabel.MANPOWER_FIXED)
    private BigDecimal salary;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.MANPOWER_FIXED, detail = "Supports Ops Turnover", order = 205, orderLabel = ExpenseLabel.MANPOWER_FIXED)
    private BigDecimal supportsOpsTurnover;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.MANPOWER_FIXED, detail = "Employee Facilitation Expenses", order = 205, orderLabel = ExpenseLabel.MANPOWER_FIXED,
        service = ServiceRecordCategory.EMPLOYEE_FACILITATION_CHARGES)
    private BigDecimal employeeFacilitationExpenses;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.MANPOWER_FIXED, detail = "Telephone (SR)", order = 205, orderLabel = ExpenseLabel.MANPOWER_FIXED)
    private BigDecimal telephoneSR;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.MANPOWER_FIXED, detail = "Vehicle Running And Maint (SR)", order = 205, orderLabel = ExpenseLabel.MANPOWER_FIXED)
    private BigDecimal vehicleRunningAndMaintSR;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.MANPOWER_FIXED, detail = "Employee Stock Option Expense", order = 205, orderLabel = ExpenseLabel.MANPOWER_FIXED)
    private BigDecimal employeeStockOptionExpense;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.MANPOWER_FIXED, detail = "Employer Contribution LWF", order = 205, orderLabel = ExpenseLabel.MANPOWER_FIXED)
    private BigDecimal employerContributionLWF;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.MANPOWER_FIXED, detail = "ESIC Employer Cont", order = 205, orderLabel = ExpenseLabel.MANPOWER_FIXED)
    private BigDecimal esicEmployerCont;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.MANPOWER_FIXED, detail = "Leave Travel Reimbursement", order = 205, orderLabel = ExpenseLabel.MANPOWER_FIXED)
    private BigDecimal leaveTravelReimbursement;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.MANPOWER_FIXED, detail = "PF Administration Charges", order = 205, orderLabel = ExpenseLabel.MANPOWER_FIXED)
    private BigDecimal pfAdministrationCharges;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.MANPOWER_FIXED, detail = "PF EmployerCont", order = 205, orderLabel = ExpenseLabel.MANPOWER_FIXED)
    private BigDecimal pfEmployerCont;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.MANPOWER_FIXED, detail = "Quarterly Incentive", order = 205, orderLabel = ExpenseLabel.MANPOWER_FIXED)
    private BigDecimal quarterlyIncentive;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.MANPOWER_FIXED, detail = "Business Promotion SR", order = 205, orderLabel = ExpenseLabel.MANPOWER_FIXED)
    private BigDecimal businessPromotionSR;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.MANPOWER_FIXED, detail = "Vehicle regular maintenance Hq", order = 205,
            orderLabel = ExpenseLabel.MANPOWER_FIXED, service = ServiceRecordCategory.VEHICLE_REGULAR_MAINTENANCE_HQ)
    private BigDecimal vehicleRegularMaintenanceHq;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.MANPOWER_FIXED, detail = "Building maintenance Hq", order = 205,
            orderLabel = ExpenseLabel.MANPOWER_FIXED, service = ServiceRecordCategory.BUILDING_MAINTENANCE_HQ)
    private BigDecimal buildingMaintenanceHq;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.MANPOWER_FIXED, detail = "Computer It maintenance Hq", order = 205,
            orderLabel = ExpenseLabel.MANPOWER_FIXED, service = ServiceRecordCategory.COMPUTER_IT_MAINTENANCE_HQ)
    private BigDecimal computerItMaintenanceHq;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.MANPOWER_FIXED, detail = "Equipment maintenance Hq", order = 205,
            orderLabel = ExpenseLabel.MANPOWER_FIXED, service = ServiceRecordCategory.EQUIPMENT_MAINTENANCE_HQ)
    private BigDecimal equipmentMaintenanceHq;

    @ExpenseField(calculationType = COST, category = ScoreCardCategory.MANPOWER_FIXED, detail = "Bonus Attendance", order = 205, orderLabel = ExpenseLabel.MANPOWER_FIXED)
    private BigDecimal bonusAttendance;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.MANPOWER_FIXED, detail = "Bonus Joining", order = 205, orderLabel = ExpenseLabel.MANPOWER_FIXED)
    private BigDecimal bonusJoining;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.MANPOWER_FIXED, detail = "Bonus Referral", order = 205, orderLabel = ExpenseLabel.MANPOWER_FIXED)
    private BigDecimal bonusReferral;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.MANPOWER_FIXED, detail = "Bonus Holiday", order = 205, orderLabel = ExpenseLabel.MANPOWER_FIXED)
    private BigDecimal bonusHoliday;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.MANPOWER_FIXED, detail = "Bonus Others", order = 205, orderLabel = ExpenseLabel.MANPOWER_FIXED)
    private BigDecimal bonusOthers;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.MANPOWER_FIXED, detail = "Allowance Remote Location", order = 205, orderLabel = ExpenseLabel.MANPOWER_FIXED)
    private BigDecimal allowanceRemoteLocation;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.MANPOWER_FIXED, detail = "Allowance Employee Benefit", order = 205, orderLabel = ExpenseLabel.MANPOWER_FIXED)
    private BigDecimal allowanceEmployeeBenefit;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.MANPOWER_FIXED, detail = "Allowance City Compensatory", order = 205, orderLabel = ExpenseLabel.MANPOWER_FIXED)
    private BigDecimal allowanceCityCompensatory;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.MANPOWER_FIXED, detail = "Allowance Monk", order = 205, orderLabel = ExpenseLabel.MANPOWER_FIXED)
    private BigDecimal allowanceMonk;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.MANPOWER_FIXED, detail = "Allowance Others", order = 205, orderLabel = ExpenseLabel.MANPOWER_FIXED)
    private BigDecimal allowanceOthers;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.MANPOWER_FIXED, detail = "Notice Period Buyout", order = 205, orderLabel = ExpenseLabel.MANPOWER_FIXED)
    private BigDecimal noticePeriodBuyout;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.MANPOWER_FIXED, detail = "Notice Period Deduction", order = 205, orderLabel = ExpenseLabel.MANPOWER_FIXED)
    private BigDecimal noticePeriodDeduction;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.MANPOWER_FIXED, detail = "Relocation Expenses", order = 205, orderLabel = ExpenseLabel.MANPOWER_FIXED)
    private BigDecimal relocationExpenses;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.MANPOWER_FIXED, detail = "Stipend Expenses", order = 205, orderLabel = ExpenseLabel.MANPOWER_FIXED)
    private BigDecimal stipendExpenses;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.MANPOWER_FIXED, detail = "Training Cost Recovery", order = 205, orderLabel = ExpenseLabel.MANPOWER_FIXED)
    private BigDecimal trainingCostRecovery;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.MANPOWER_FIXED, detail = "Severance Pay", order = 205, orderLabel = ExpenseLabel.MANPOWER_FIXED)
    private BigDecimal severancePay;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.MANPOWER_FIXED, detail = "Labour Charges", order = 205, orderLabel = ExpenseLabel.MANPOWER_FIXED)
    private BigDecimal labourCharges;


    /*
     * ExpenseFieldCategory.SUPPORT_OPS_MANAGEMENT
     *
     */

    @ExpenseField(calculationType = COST, category = ScoreCardCategory.SUPPORT_OPS_MANAGEMENT, detail = "Support Operations", order = 220, orderLabel = ExpenseLabel.SUPPORT_OPS_MANAGEMENT)
    private BigDecimal supportOpsManagement;

    /*
     * ExpenseFieldCategory.SUPPORT_HQ
     *
     */
//	@ExpenseField(calculationType = COST, category = ScoreCardCategory.SUPPORT_HQ, detail = "Support Audit", order = 225, orderLabel = ExpenseLabel.SUPPORT_HQ)
//	private BigDecimal supportAudit;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.SUPPORT_HQ, detail = "Support CCC", order = 225, orderLabel = ExpenseLabel.SUPPORT_HQ)
    private BigDecimal supportCCC;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.SUPPORT_HQ, detail = "Support IT", order = 225, orderLabel = ExpenseLabel.SUPPORT_HQ)
    private BigDecimal supportIT;
//	@ExpenseField(calculationType = COST, category = ScoreCardCategory.SUPPORT_HQ, detail = "Support Maintenance", order = 225, orderLabel = ExpenseLabel.SUPPORT_HQ)
//	private BigDecimal supportMaintenance;

    /*
     * ExpenseFieldCategory.SUPPORT_COMM_WH
     *
     */
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.SUPPORT_COMM_WH, detail = "Support Comm/WH", order = 226, orderLabel = ExpenseLabel.SUPPORT_COMM_WH)
    private BigDecimal supportCommWH;

    /*
     * ExpenseFieldCategory.TECHNOLOGY
     *
     */
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.TECHNOLOGY, detail = "Technology Platform Charges", order = 240, orderLabel = ExpenseLabel.TECHNOLOGY,
        service = ServiceRecordCategory.TECHNOLOGY_PLATFORM_CHARGES)
    private BigDecimal technologyPlatformCharges;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.TECHNOLOGY, detail = "Technology Training", order = 240,
            orderLabel = ExpenseLabel.TECHNOLOGY, service = ServiceRecordCategory.TECHNOLOGY_TRAINING)
    private BigDecimal technologyTraining;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.TECHNOLOGY, detail = "Technology Others", order = 240, orderLabel = ExpenseLabel.TECHNOLOGY,
        service = ServiceRecordCategory.TECHNOLOGY_OTHERS)
    private BigDecimal technologyOthers;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.TECHNOLOGY, detail = "Technology Variable", order = 240, orderLabel = ExpenseLabel.TECHNOLOGY)
    private BigDecimal technologyVariable;

    /*
     * ExpenseFieldCategory.MARKETING_LOCAL_STORE
     *
     */
    @ExpenseField(calculationType = COST, category = MARKETING_LOCAL_STORE, detail = "Sampling and Marketing Charges", order = 210, orderLabel = ExpenseLabel.MARKETING_LS)
    private BigDecimal marketingAndSampling;
    @ExpenseField(calculationType = COST, category = MARKETING_LOCAL_STORE, detail = "Sampling and Marketing Charges Tax", order = 210, orderLabel = ExpenseLabel.MARKETING_LS)
    private BigDecimal marketingAndSamplingTax;
    @ExpenseField(calculationType = COST, category = MARKETING_LOCAL_STORE, detail = "Marketing Consumbles", order = 210, orderLabel = ExpenseLabel.MARKETING_LS)
    private BigDecimal consumableMarketing;
    @ExpenseField(calculationType = COST, category = MARKETING_LOCAL_STORE, detail = "Marketing Consumbles Tax", order = 210, orderLabel = ExpenseLabel.MARKETING_LS)
    private BigDecimal consumableMarketingTax;
    @ExpenseField(calculationType = COST, category = MARKETING_LOCAL_STORE, detail = "Marketing NPI", expense = ExpenseRecordCategory.MARKETING_NPI_CAFE, order = 210, orderLabel = ExpenseLabel.MARKETING_LS)
    private BigDecimal marketingNPI;
    @ExpenseField(calculationType = COST, category = MARKETING_LOCAL_STORE, detail = "Marketing NPI", order = 210, orderLabel = ExpenseLabel.MARKETING_LS)
    private BigDecimal giftCardOffer;
    @ExpenseField(calculationType = COST, category = MARKETING_LOCAL_STORE, detail = "Marketing NPI", expense = ExpenseRecordCategory.MARKETING_NPI_CAFE, order = 210,
            orderLabel = ExpenseLabel.MARKETING_LS, service = ServiceRecordCategory.MARKETING_NPI_HQ)
    private BigDecimal marketingNpiHq;

    /*
     * ExpenseFieldCategory.MARKETING_CORPORATE
     *
     */
    @ExpenseField(calculationType = COST, category = MARKETING_CORPORATE, detail = "Corporate Marketing Digital", order = 211, expense = DIGITAL_MARKETING_EXPENSE,
            orderLabel = ExpenseLabel.MARKETING_CORP, budgetType = WARNING, service = ServiceRecordCategory.CORPORATE_MARKETING_DIGITAL)
    private BigDecimal corporateMarketingDigital;
    @ExpenseField(calculationType = COST, category = MARKETING_CORPORATE, detail = "Corporate Marketing Adv Offline", order = 211, expense = ADVERTISEMENT_OFFLINE,
            orderLabel = ExpenseLabel.MARKETING_CORP, budgetType = WARNING, service = ServiceRecordCategory.CORPORATE_MARKETING_AD_OFFLINE)
    private BigDecimal corporateMarketingAdvOffline;
    	@ExpenseField(calculationType = COST, category = MARKETING_CORPORATE, detail = "Corporate Marketing Adv Online", order = 211, expense = ADVERTISEMENT_ONLINE,
			orderLabel = ExpenseLabel.MARKETING_CORP, budgetType = WARNING, service = ServiceRecordCategory.CORPORATE_MARKETING_AD_ONLINE)
	private BigDecimal corporateMarketingAdvOnline;
    @ExpenseField(calculationType = COST, category = MARKETING_CORPORATE, detail = "Corporate Marketing Outdoor", order = 211, expense = MARKETING_OUTDOOR,
            orderLabel = ExpenseLabel.MARKETING_CORP, budgetType = WARNING, service = ServiceRecordCategory.CORPORATE_MARKETING_OUTDOOR)
    private BigDecimal corporateMarketingOutdoor;
    @ExpenseField(calculationType = COST, category = MARKETING_CORPORATE, detail = "Corporate Marketing Photography", order = 211, expense = ADVERTISEMENT_PNS,
            orderLabel = ExpenseLabel.MARKETING_CORP, budgetType = WARNING, service = ServiceRecordCategory.CORPORATE_MARKETING_PHOTO)
    private BigDecimal corporateMarketingPhotography;
    @ExpenseField(calculationType = COST, category = MARKETING_CORPORATE, detail = "Corporate Marketing Agency Fees", order = 211, expense = ADVERTISEMENT_AGENCY_FEES,
            orderLabel = ExpenseLabel.MARKETING_CORP, budgetType = WARNING, service = ServiceRecordCategory.CORPORATE_MARKETING_AGENCY_FEES)
    private BigDecimal corporateMarketingAgencyFees;
    @ExpenseField(calculationType = COST, category = MARKETING_CORPORATE, detail = "Corporate Marketing Alt Radio", order = 211, expense = ADVERTISEMENT_AGENCY_FEES,
            orderLabel = ExpenseLabel.MARKETING_CORP, budgetType = WARNING, service = ServiceRecordCategory.CORPORATE_MARKETING_ATL_RADIO)
    private BigDecimal corporateMarketingAtlRadio;
    @ExpenseField(calculationType = COST, category = MARKETING_CORPORATE, detail = "Corporate Marketing Alt TV", order = 211, expense = ADVERTISEMENT_AGENCY_FEES,
            orderLabel = ExpenseLabel.MARKETING_CORP, budgetType = WARNING, service = ServiceRecordCategory.CORPORATE_MARKETING_ATL_TV)
    private BigDecimal corporateMarketingAtlTv;
    @ExpenseField(calculationType = COST, category = MARKETING_CORPORATE, detail = "Corporate Marketing Alt Print Ad", order = 211, expense = ADVERTISEMENT_AGENCY_FEES,
            orderLabel = ExpenseLabel.MARKETING_CORP, budgetType = WARNING, service = ServiceRecordCategory.CORPORATE_MARKETING_ATL_PRINT_AD)
    private BigDecimal corporateMarketingAtlPrintAd;
    @ExpenseField(calculationType = COST, category = MARKETING_CORPORATE, detail = "Corporate Marketing Alt Cinema", order = 211, expense = ADVERTISEMENT_AGENCY_FEES,
            orderLabel = ExpenseLabel.MARKETING_CORP, budgetType = WARNING, service = ServiceRecordCategory.CORPORATE_MARKETING_ATL_CINEMA)
    private BigDecimal corporateMarketingAtlCinema;
    @ExpenseField(calculationType = COST, category = MARKETING_CORPORATE, detail = "Corporate Marketing Alt Digital", order = 211, expense = ADVERTISEMENT_AGENCY_FEES,
            orderLabel = ExpenseLabel.MARKETING_CORP, budgetType = WARNING, service = ServiceRecordCategory.CORPORATE_MARKETING_ATL_DIGITAL)
    private BigDecimal corporateMarketingAtlDigital;
    @ExpenseField(calculationType = COST, category = MARKETING_CORPORATE, detail = "Corporate Marketing SMS Email", order = 211, expense = ADVERTISEMENT_AGENCY_FEES,
            orderLabel = ExpenseLabel.MARKETING_CORP, budgetType = WARNING, service = ServiceRecordCategory.CORPORATE_MARKETING_SMS_EMAIL)
    private BigDecimal corporateMarketingSms;

    /*
     * ExpenseFieldCategory.FIXED_ASSETS
     *
     */
//	@ExpenseField(calculationType = COST, category = FIXED_ASSETS, detail = "Consumables Fixed Assets", order = 212, orderLabel = ExpenseLabel.FIXED_ASSETS, budgetType = ERROR)
//	private BigDecimal fixedAssets;
    @ExpenseField(calculationType = COST, category = FIXED_ASSETS, detail = "Consumables Fixed Assets Tax", order = 212, orderLabel = ExpenseLabel.FIXED_ASSETS, budgetType = ERROR)
    private BigDecimal fixedAssetsTax;
    @ExpenseField(calculationType = COST, category = FIXED_ASSETS, detail = "Consumables Fixed Assets")
    private BigDecimal fixedAssetsCapex;
    @ExpenseField(calculationType = COST, category = FIXED_ASSETS, detail = "Consumables Fixed Assets Tax")
    private BigDecimal fixedAssetsCapexTax;

    @ExpenseField(calculationType = COST, category = FIXED_ASSETS, detail = "Depreciation of Furniture Fixture", order = 212, orderLabel = ExpenseLabel.FIXED_ASSETS, budgetType = ERROR)
    private BigDecimal depreciationOfFurnitureFixture;
    @ExpenseField(calculationType = COST, category = FIXED_ASSETS, detail = "Depreciation of Office Equipment", order = 212, orderLabel = ExpenseLabel.FIXED_ASSETS, budgetType = ERROR)
    private BigDecimal depreciationOfOfficeEquipment;
    @ExpenseField(calculationType = COST, category = FIXED_ASSETS, detail = "Depreciation of Kitchen Equipment", order = 212, orderLabel = ExpenseLabel.FIXED_ASSETS, budgetType = ERROR)
    private BigDecimal depreciationOfKitchenEquipment;
    @ExpenseField(calculationType = COST, category = FIXED_ASSETS, detail = "Depreciation of Equipment", order = 212, orderLabel = ExpenseLabel.FIXED_ASSETS, budgetType = ERROR)
    private BigDecimal depreciationOfEquipment;
    @ExpenseField(calculationType = COST, category = FIXED_ASSETS, detail = "Depreciation of IT", order = 212, orderLabel = ExpenseLabel.FIXED_ASSETS, budgetType = ERROR)
    private BigDecimal depreciationOfIt;
    @ExpenseField(calculationType = COST, category = FIXED_ASSETS, detail = "Depreciation of Vehicle", order = 212, orderLabel = ExpenseLabel.FIXED_ASSETS, budgetType = ERROR)
    private BigDecimal depreciationOfVehicle;
    @ExpenseField(calculationType = COST, category = FIXED_ASSETS, detail = "Depreciation of Others", order = 212, orderLabel = ExpenseLabel.FIXED_ASSETS, budgetType = ERROR)
    private BigDecimal depreciationOfOthers;

    @ExpenseField(calculationType = COST, category = FIXED_ASSETS, detail = "Consumables Fixed Assets Depreciation")
    private BigDecimal fixedAssetsDepreciation;
    @ExpenseField(calculationType = COST, category = FIXED_ASSETS, detail = "Consumables Fixed Assets Lost")
    private BigDecimal fixedAssetsLost;
    @ExpenseField(calculationType = COST, category = FIXED_ASSETS, detail = "Consumables Fixed Assets Damage")
    private BigDecimal fixedAssetsDamage;

    /*
     *
     *
     * ExpenseFieldCategory.CAPEX_AND_FIXED_ASSETS
     *
     */

    @ExpenseField(calculationType = COST, category = ScoreCardCategory.CAPEX_AND_FIXED_ASSETS, detail = "Capital Improvement Expenses", order = 230,
            orderLabel = ExpenseLabel.CAPEX_AND_FIXED_ASSETS, service = ServiceRecordCategory.CAPITAL_IMPROVEMENT_EXPENSES)
    private BigDecimal capitalImprovementExpenses;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.CAPEX_AND_FIXED_ASSETS, detail = "Lease Hold Improvements", order = 230,
            orderLabel = ExpenseLabel.CAPEX_AND_FIXED_ASSETS, service = ServiceRecordCategory.LEASE_HOLD_IMPROVEMENTS)
    private BigDecimal leaseHoldImprovements;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.CAPEX_AND_FIXED_ASSETS, detail = "Fixed Assets Equipment", order = 230, orderLabel = ExpenseLabel.CAPEX_AND_FIXED_ASSETS)
    private BigDecimal fixedAssetsEquipment;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.CAPEX_AND_FIXED_ASSETS, detail = "Fixed Asset Furniture", order = 230, orderLabel = ExpenseLabel.CAPEX_AND_FIXED_ASSETS)
    private BigDecimal fixedAssetFurniture;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.CAPEX_AND_FIXED_ASSETS, detail = "Fixed Assets IT", order = 230, orderLabel = ExpenseLabel.CAPEX_AND_FIXED_ASSETS)
    private BigDecimal fixedAssetsIT;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.CAPEX_AND_FIXED_ASSETS, detail = "Fixed Assets Kitchen Equipment", order = 230, orderLabel = ExpenseLabel.CAPEX_AND_FIXED_ASSETS)
    private BigDecimal fixedAssetsKitchenEquipment;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.CAPEX_AND_FIXED_ASSETS, detail = "Fixed Assets Office Equipment", order = 230, orderLabel = ExpenseLabel.CAPEX_AND_FIXED_ASSETS)
    private BigDecimal fixedAssetsOfficeEquipment;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.CAPEX_AND_FIXED_ASSETS, detail = "Fixed Assets Others Sub Category", order = 230, orderLabel = ExpenseLabel.CAPEX_AND_FIXED_ASSETS)
    private BigDecimal fixedAssetsOthersSubCategory;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.CAPEX_AND_FIXED_ASSETS, detail = "Fixed Assets Vehicle ", order = 230, orderLabel = ExpenseLabel.CAPEX_AND_FIXED_ASSETS)
    private BigDecimal fixedAssetsVehicle;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.CAPEX_AND_FIXED_ASSETS, detail = "Lease Hold Improvements", order = 230,
            orderLabel = ExpenseLabel.CAPEX_AND_FIXED_ASSETS, service = ServiceRecordCategory.LEASE_HOLD_IMPROVEMENTS)
    private BigDecimal licenseExpenses;

    /*
     * ExpenseFieldCategory.PRE_OPENING
     *
     */

    @ExpenseField(calculationType = COST, category = ScoreCardCategory.PRE_OPENING, detail = "Marketing Launch", order = 231,
            orderLabel = ExpenseLabel.PRE_OPENING, service = ServiceRecordCategory.MARKETING_LAUNCH)
    private BigDecimal marketingLaunch;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.PRE_OPENING, detail = "Pre Opening Consumable", order = 231, orderLabel = ExpenseLabel.PRE_OPENING)
    private BigDecimal preOpeningConsumable;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.PRE_OPENING, detail = "Pre Opening Others", order = 231, orderLabel = ExpenseLabel.PRE_OPENING)
    private BigDecimal preOpeningOthers;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.PRE_OPENING, detail = "Pre Opening Rent", order = 231, orderLabel = ExpenseLabel.PRE_OPENING)
    private BigDecimal preOpeningRent;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.PRE_OPENING, detail = "Pre Opening Salary", order = 231, orderLabel = ExpenseLabel.PRE_OPENING)
    private BigDecimal preOpeningSalary;

    /*
     * ExpenseFieldCategory.FINANCIAL_EXPENSES
     *
     *
     */
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.FINANCIAL_EXPENSES, detail = "Bank Charges", order = 232, orderLabel = ExpenseLabel.FINANCIAL_EXPENSES)
    private BigDecimal bankCharges;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.FINANCIAL_EXPENSES, detail = "Interest On Loan", order = 232, orderLabel = ExpenseLabel.FINANCIAL_EXPENSES)
    private BigDecimal interestOnLoan;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.FINANCIAL_EXPENSES, detail = "Interest On TDS/GST", order = 232, orderLabel = ExpenseLabel.FINANCIAL_EXPENSES)
    private BigDecimal intrestOnTDSorGST;

    /*
     * ExpenseFieldCategory.INTEREST_INCOME
     *
     *
     */
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.INTEREST_INCOME, detail = "Interest On FDR", order = 233, orderLabel = ExpenseLabel.INTEREST_INCOME)
    private BigDecimal interestOnFDR;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.INTEREST_INCOME, detail = "Profit on Sale of Mutual Funds", order = 233, orderLabel = ExpenseLabel.INTEREST_INCOME)
    private BigDecimal profitSaleMutualFunds;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.INTEREST_INCOME, detail = "Interest on Income Tax Refund", order = 233, orderLabel = ExpenseLabel.INTEREST_INCOME)
    private BigDecimal interestIncomeTaxRefund;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.INTEREST_INCOME, detail = "Misc Income", order = 233, orderLabel = ExpenseLabel.INTEREST_INCOME)
    private BigDecimal miscIncome;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.INTEREST_INCOME, detail = "Discount Received", order = 233, orderLabel = ExpenseLabel.INTEREST_INCOME)
    private BigDecimal discountReceived;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.INTEREST_INCOME, detail = "Interior Designing Charge", order = 233, orderLabel = ExpenseLabel.INTEREST_INCOME)
    private BigDecimal interiorDesigningCharge;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.INTEREST_INCOME, detail = "Scrape", order = 233, orderLabel = ExpenseLabel.INTEREST_INCOME)
    private BigDecimal scrape;
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.INTEREST_INCOME, detail = "Service Charges", order = 233, orderLabel = ExpenseLabel.INTEREST_INCOME)
    private BigDecimal serviceCharges;

    /*
     * ExpenseFieldCategory.FICO_PAYOUTS
     *
     */
    @ExpenseField(calculationType = COST, category = ScoreCardCategory.FICO_PAYOUTS, detail = "ServiceChargesFICO")
    private BigDecimal serviceChargesFICO;

    @ExpenseField(calculationType = NO_TYPE, category = PROFIT, detail = "Gross Profit", order = 160, orderLabel = ExpenseLabel.GROSS_PROFIT)
    private BigDecimal grossProfit;
    @ExpenseField(calculationType = NO_TYPE, category = PROFIT, detail = "Gross Profit Percentage", order = 161, orderLabel = ExpenseLabel.GROSS_PROFIT_PERCENT)
    private BigDecimal grossProfitPercentage;

    @ExpenseField(calculationType = NO_TYPE, category = PROFIT, detail = "Gross Cost")
    private BigDecimal grossCost;
    @ExpenseField(calculationType = NO_TYPE, category = PROFIT, detail = "Net Cost", order = 260, orderLabel = ExpenseLabel.TOTAL_COST)
    private BigDecimal totalCost;
    @ExpenseField(calculationType = NO_TYPE, category = PROFIT, detail = "Net Profit", order = 270, orderLabel = ExpenseLabel.EBITDA)
    private BigDecimal netProfit;
    @ExpenseField(calculationType = NO_TYPE, category = PROFIT, detail = "Net Profit Percentage", order = 280, orderLabel = ExpenseLabel.EBITDA_PERCENT)
    private BigDecimal netProfitPercentage;
    @ExpenseField(calculationType = NO_TYPE, category = PROFIT, detail = "Net Profit With Depreciation", order = 270, orderLabel = ExpenseLabel.EBITDA)
    private BigDecimal netProfitWithDepreciation;
    @ExpenseField(calculationType = NO_TYPE, category = PROFIT, detail = "Net Profit Percentage With Depreciation", order = 280, orderLabel = ExpenseLabel.EBITDA_PERCENT)
    private BigDecimal netProfitPercentageWithDepreciation;

    @ExpenseField(calculationType = COST, category = FIXED_ASSETS, detail = "Fixed Assets Equipment Tax", order = 212, orderLabel = ExpenseLabel.FIXED_ASSETS, budgetType = ERROR)
    private BigDecimal fixedAssetsEquipmentTax;
    @ExpenseField(calculationType = COST, category = FIXED_ASSETS, detail = "Fixed Assets Furniture Tax", order = 212, orderLabel = ExpenseLabel.FIXED_ASSETS, budgetType = ERROR)
    private BigDecimal fixedAssetFurnitureTax;
    @ExpenseField(calculationType = COST, category = FIXED_ASSETS, detail = "Fixed Assets IT Tax", order = 212, orderLabel = ExpenseLabel.FIXED_ASSETS, budgetType = ERROR)
    private BigDecimal fixedAssetsItTax;
    @ExpenseField(calculationType = COST, category = FIXED_ASSETS, detail = "Fixed Assets Kitchen Equipment Tax", order = 212, orderLabel = ExpenseLabel.FIXED_ASSETS, budgetType = ERROR)
    private BigDecimal fixedAssetsKitchenEquipmentTax;
    @ExpenseField(calculationType = COST, category = FIXED_ASSETS, detail = "Fixed Assets Office Equipment Tax", order = 212, orderLabel = ExpenseLabel.FIXED_ASSETS, budgetType = ERROR)
    private BigDecimal fixedAssetsOfficeEquipmentTax;
    @ExpenseField(calculationType = COST, category = FIXED_ASSETS, detail = "Fixed Assets Vehicle Tax", order = 212, orderLabel = ExpenseLabel.FIXED_ASSETS, budgetType = ERROR)
    private BigDecimal fixedAssetsVehicleTax;
    @ExpenseField(calculationType = COST, category = FIXED_ASSETS, detail = "Fixed Assets Others SubCategory Cafe Tax", order = 212, orderLabel = ExpenseLabel.FIXED_ASSETS, budgetType = ERROR)
    private BigDecimal fixedAssetsOthersSubCategoryCafeTax;
    @ExpenseField(calculationType = COST, category = FIXED_ASSETS, detail = "Fixed Assets Equipment Hq", order = 212, orderLabel = ExpenseLabel.FIXED_ASSETS, budgetType = ERROR)
    private BigDecimal fixedAssetsEquipmentHq;
    @ExpenseField(calculationType = COST, category = FIXED_ASSETS, detail = "Fixed Asset Furniture Hq", order = 212, orderLabel = ExpenseLabel.FIXED_ASSETS, budgetType = ERROR)
    private BigDecimal fixedAssetFurnitureHq;
    @ExpenseField(calculationType = COST, category = FIXED_ASSETS, detail = "Fixed Assets It Hq", order = 212, orderLabel = ExpenseLabel.FIXED_ASSETS, budgetType = ERROR)
    private BigDecimal fixedAssetsItHq;
    @ExpenseField(calculationType = COST, category = FIXED_ASSETS, detail = "Fixed Assets Kitchen Equipment Hq", order = 212, orderLabel = ExpenseLabel.FIXED_ASSETS, budgetType = ERROR)
    private BigDecimal fixedAssetsKitchenEquipmentHq;
    @ExpenseField(calculationType = COST, category = FIXED_ASSETS, detail = "Fixed Assets Office Equipment Hq", order = 212, orderLabel = ExpenseLabel.FIXED_ASSETS, budgetType = ERROR)
    private BigDecimal fixedAssetsOfficeEquipmentHq;
    @ExpenseField(calculationType = COST, category = FIXED_ASSETS, detail = "Fixed Assets Vehicle Hq", order = 212, orderLabel = ExpenseLabel.FIXED_ASSETS, budgetType = ERROR)
    private BigDecimal fixedAssetsVehicleHq;
    @ExpenseField(calculationType = COST, category = FIXED_ASSETS, detail = "Fixed Assets Others SubCategory Hq", order = 212, orderLabel = ExpenseLabel.FIXED_ASSETS, budgetType = ERROR)
    private BigDecimal fixedAssetsOthersSubCategoryHq;
    @ExpenseField(calculationType = COST, category = FIXED_ASSETS, detail = "Fixed Assets Equipment Hq Tax", order = 212, orderLabel = ExpenseLabel.FIXED_ASSETS, budgetType = ERROR)
    private BigDecimal fixedAssetsEquipmentHqTax;
    @ExpenseField(calculationType = COST, category = FIXED_ASSETS, detail = "Fixed Asset Furniture Hq Tax", order = 212, orderLabel = ExpenseLabel.FIXED_ASSETS, budgetType = ERROR)
    private BigDecimal fixedAssetFurnitureHqTax;
    @ExpenseField(calculationType = COST, category = FIXED_ASSETS, detail = "Fixed Assets It Hq Tax", order = 212, orderLabel = ExpenseLabel.FIXED_ASSETS, budgetType = ERROR)
    private BigDecimal fixedAssetsItHqTax;
    @ExpenseField(calculationType = COST, category = FIXED_ASSETS, detail = "Fixed Assets Kitchen Equipment Hq Tax", order = 212, orderLabel = ExpenseLabel.FIXED_ASSETS, budgetType = ERROR)
    private BigDecimal fixedAssetsKitchenEquipmentHqTax;
    @ExpenseField(calculationType = COST, category = FIXED_ASSETS, detail = "Fixed Assets Office Equipment Hq Tax", order = 212, orderLabel = ExpenseLabel.FIXED_ASSETS, budgetType = ERROR)
    private BigDecimal fixedAssetsOfficeEquipmentHqTax;
    @ExpenseField(calculationType = COST, category = FIXED_ASSETS, detail = "Fixed Assets Vehicle Hq Tax", order = 212, orderLabel = ExpenseLabel.FIXED_ASSETS, budgetType = ERROR)
    private BigDecimal fixedAssetsVehicleHqTax;
    @ExpenseField(calculationType = COST, category = FIXED_ASSETS, detail = "Fixed Assets Others SubCategory Hq Tax", order = 212, orderLabel = ExpenseLabel.FIXED_ASSETS, budgetType = ERROR)
    private BigDecimal fixedAssetsOthersSubCategoryHqTax;


    private BigDecimal revenueAdjustment;
    private BigDecimal costAdjustment;

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "UNIT_EXPENDITURE_DETAIL_ID", unique = true, nullable = false)
    public Integer getDetailId() {
        return detailId;
    }

    public void setDetailId(Integer unitExpenseDetailId) {
        this.detailId = unitExpenseDetailId;
    }

    @Column(name = "KETTLE_DAY_CLOSE_EVENT_ID", nullable = true)
    public Integer getDayClosureId() {
        return dayClosureId;
    }

    public void setDayClosureId(Integer dayClosureId) {
        this.dayClosureId = dayClosureId;
    }

    @Column(name = "SUMO_DAY_CLOSE_EVENT_ID", nullable = true)
    public Integer getSumoClosureId() {
        return sumoClosureId;
    }

    public void setSumoClosureId(Integer sumoClosureId) {
        this.sumoClosureId = sumoClosureId;
    }

    @Column(name = "CALCULATION_TYPE", nullable = true)
    public String getCalculation() {
        return calculation;
    }

    public void setCalculation(String calculation) {
        this.calculation = calculation;
    }

    @Column(name = "UNIT_ID", nullable = true)
    public int getUnitId() {
        return unitId;
    }

    public void setUnitId(int unitId) {
        this.unitId = unitId;
    }

    @Column(name = "UNIT_NAME", nullable = true)
    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    @Column(name = "CALCULATION_YEAR", nullable = true)
    public int getYear() {
        return year;
    }

    public void setYear(int year) {
        this.year = year;
    }

    @Column(name = "CALCULATION_MONTH", nullable = true)
    public int getMonth() {
        return month;
    }

    public void setMonth(int month) {
        this.month = month;
    }

    @Column(name = "CALCULATION_DAY", nullable = true)
    public int getDay() {
        return day;
    }

    public void setDay(int day) {
        this.day = day;
    }

    @Temporal(TemporalType.DATE)
    @Column(name = "BUSINESS_DATE", nullable = true, length = 10)
    public Date getBusinessDate() {
        return businessDate;
    }

    public void setBusinessDate(Date businessDate) {
        this.businessDate = businessDate;
    }

    @Column(name = "TOTAL_TICKET", nullable = true)
    public Integer getTicket() {
        return ticket;
    }

    public void setTicket(Integer ticket) {
        this.ticket = ticket;
    }

    @Column(name = "TOTAL_SALES", nullable = true)
    public BigDecimal getRevenue() {
        return revenue;
    }

    public void setRevenue(BigDecimal sales) {
        this.revenue = sales;
    }

    @Column(name = "TOTAL_APC", nullable = true)
    public BigDecimal getApc() {
        return apc;
    }

    public void setApc(BigDecimal apc) {
        this.apc = apc;
    }

    @Column(name = "TOTAL_GMV", nullable = true)
    public BigDecimal getGmv() {
        return gmv;
    }

    public void setGmv(BigDecimal gmv) {
        this.gmv = gmv;
    }

    @Column(name = "TOTAL_DISCOUNT", nullable = true)
    public BigDecimal getDiscount() {
        return discount;
    }

    public void setDiscount(BigDecimal discount) {
        this.discount = discount;
    }


    @Column(name = "TOTAL_DISCOUNT_LOYAL_TEA", nullable = true)
    public BigDecimal getDiscountLoyalty() {
        return discountLoyalty;
    }

    public void setDiscountLoyalty(BigDecimal discountLoyalty) {
        this.discountLoyalty = discountLoyalty;
    }

    @Column(name = "TOTAL_DISCOUNT_MARKETING", nullable = true)
    public BigDecimal getDiscountMarketing() {
        return discountMarketing;
    }

    public void setDiscountMarketing(BigDecimal discountMarketing) {
        this.discountMarketing = discountMarketing;
    }

    @Column(name = "TOTAL_DISCOUNT_OPS", nullable = true)
    public BigDecimal getDiscountOps() {
        return discountOps;
    }

    public void setDiscountOps(BigDecimal discountOps) {
        this.discountOps = discountOps;
    }

    @Column(name = "TOTAL_DISCOUNT_BD", nullable = true)
    public BigDecimal getDiscountBd() {
        return discountBd;
    }

    public void setDiscountBd(BigDecimal discountBd) {
        this.discountBd = discountBd;
    }

    @Column(name = "TOTAL_DISCOUNT_EMPLOYEE_FICO", nullable = true)
    public BigDecimal getDiscountEmployeeFico() {
        return discountEmployeeFico;
    }

    public void setDiscountEmployeeFico(BigDecimal discountEmployeeFico) {
        this.discountEmployeeFico = discountEmployeeFico;
    }

    @Column(name = "DINE_IN_TICKET", nullable = true)
    public Integer getDineInTicket() {
        return dineInTicket;
    }

    public void setDineInTicket(Integer dineInTicket) {
        this.dineInTicket = dineInTicket;
    }

    @Column(name = "DINE_IN_SALES", nullable = true)
    public BigDecimal getDineInSales() {
        return dineInSales;
    }

    public void setDineInSales(BigDecimal dineInSales) {
        this.dineInSales = dineInSales;
    }

    @Column(name = "DINE_IN_APC", nullable = true)
    public BigDecimal getDineInApc() {
        return dineInApc;
    }

    public void setDineInApc(BigDecimal dineInApc) {
        this.dineInApc = dineInApc;
    }

    @Column(name = "DINE_IN_GMV", nullable = true)
    public BigDecimal getDineInGmv() {
        return dineInGmv;
    }

    public void setDineInGmv(BigDecimal dineInGmv) {
        this.dineInGmv = dineInGmv;
    }

    @Column(name = "DINE_IN_DISCOUNT", nullable = true)
    public BigDecimal getDineInDiscount() {
        return dineInDiscount;
    }

    public void setDineInDiscount(BigDecimal dineInDiscount) {
        this.dineInDiscount = dineInDiscount;
    }

    @Column(name = "DINE_IN_DISCOUNT_LOYAL_TEA", nullable = true)
    public BigDecimal getDineInDiscountLoyalty() {
        return dineInDiscountLoyalty;
    }

    public void setDineInDiscountLoyalty(BigDecimal dineInDiscountLoyalty) {
        this.dineInDiscountLoyalty = dineInDiscountLoyalty;
    }

    @Column(name = "DINE_IN_DISCOUNT_MARKETING", nullable = true)
    public BigDecimal getDineInDiscountMarketing() {
        return dineInDiscountMarketing;
    }

    public void setDineInDiscountMarketing(BigDecimal dineInDiscountMarketing) {
        this.dineInDiscountMarketing = dineInDiscountMarketing;
    }

    @Column(name = "DINE_IN_DISCOUNT_OPS", nullable = true)
    public BigDecimal getDineInDiscountOps() {
        return dineInDiscountOps;
    }

    public void setDineInDiscountOps(BigDecimal dineInDiscountOps) {
        this.dineInDiscountOps = dineInDiscountOps;
    }

    @Column(name = "DINE_IN_DISCOUNT_BD", nullable = true)
    public BigDecimal getDineInDiscountBd() {
        return dineInDiscountBd;
    }

    public void setDineInDiscountBd(BigDecimal dineInDiscountBd) {
        this.dineInDiscountBd = dineInDiscountBd;
    }

    @Column(name = "DINE_IN_DISCOUNT_EMPLOYEE_FICO", nullable = true)
    public BigDecimal getDineInDiscountEmployeeFico() {
        return dineInDiscountEmployeeFico;
    }

    public void setDineInDiscountEmployeeFico(BigDecimal dineInDiscountEmployeeFico) {
        this.dineInDiscountEmployeeFico = dineInDiscountEmployeeFico;
    }

    @Column(name = "DELIVERY_TICKET", nullable = true)
    public Integer getDeliveryTicket() {
        return deliveryTicket;
    }

    public void setDeliveryTicket(Integer deliveryTicket) {
        this.deliveryTicket = deliveryTicket;
    }

    @Column(name = "DELIVERY_SALES", nullable = true)
    public BigDecimal getDeliverySales() {
        return deliverySales;
    }

    public void setDeliverySales(BigDecimal deliverySales) {
        this.deliverySales = deliverySales;
    }

    @Column(name = "DELIVERY_APC", nullable = true)
    public BigDecimal getDeliveryApc() {
        return deliveryApc;
    }

    public void setDeliveryApc(BigDecimal deliveryApc) {
        this.deliveryApc = deliveryApc;
    }

    @Column(name = "DELIVERY_GMV", nullable = true)
    public BigDecimal getDeliveryGmv() {
        return deliveryGmv;
    }

    public void setDeliveryGmv(BigDecimal deliveryGmv) {
        this.deliveryGmv = deliveryGmv;
    }

    @Column(name = "DELIVERY_DISCOUNT", nullable = true)
    public BigDecimal getDeliveryDiscount() {
        return deliveryDiscount;
    }

    public void setDeliveryDiscount(BigDecimal deliveryDiscount) {
        this.deliveryDiscount = deliveryDiscount;
    }

    @Column(name = "DELIVERY_DISCOUNT_LOYAL_TEA", nullable = true)
    public BigDecimal getDeliveryDiscountLoyalty() {
        return deliveryDiscountLoyalty;
    }

    public void setDeliveryDiscountLoyalty(BigDecimal deliveryDiscountLoyalty) {
        this.deliveryDiscountLoyalty = deliveryDiscountLoyalty;
    }

    @Column(name = "DELIVERY_DISCOUNT_MARKETING", nullable = true)
    public BigDecimal getDeliveryDiscountMarketing() {
        return deliveryDiscountMarketing;
    }

    public void setDeliveryDiscountMarketing(BigDecimal deliveryDiscountMarketing) {
        this.deliveryDiscountMarketing = deliveryDiscountMarketing;
    }

    @Column(name = "DELIVERY_DISCOUNT_OPS", nullable = true)
    public BigDecimal getDeliveryDiscountOps() {
        return deliveryDiscountOps;
    }

    public void setDeliveryDiscountOps(BigDecimal deliveryDiscountOps) {
        this.deliveryDiscountOps = deliveryDiscountOps;
    }

    @Column(name = "DELIVERY_DISCOUNT_BD", nullable = true)
    public BigDecimal getDeliveryDiscountBd() {
        return deliveryDiscountBd;
    }

    public void setDeliveryDiscountBd(BigDecimal deliveryDiscountBd) {
        this.deliveryDiscountBd = deliveryDiscountBd;
    }

    @Column(name = "DELIVERY_DISCOUNT_EMPLOYEE_FICO", nullable = true)
    public BigDecimal getDeliveryDiscountEmployeeFico() {
        return deliveryDiscountEmployeeFico;
    }

    public void setDeliveryDiscountEmployeeFico(BigDecimal deliveryDiscountEmployeeFico) {
        this.deliveryDiscountEmployeeFico = deliveryDiscountEmployeeFico;
    }

    @Column(name = "GIFT_CARD_SALES", nullable = true)
    public BigDecimal getGiftCardSale() {
        return giftCardSale;
    }

    public void setGiftCardSale(BigDecimal giftCardSale) {
        this.giftCardSale = giftCardSale;
    }

    @Column(name = "GIFT_CARD_REDEMPTION", nullable = true)
    public BigDecimal getGiftCardRedemption() {
        return giftCardRedemption;
    }

    public void setGiftCardRedemption(BigDecimal giftCardRedemption) {
        this.giftCardRedemption = giftCardRedemption;
    }

    @Column(name = "DINE_IN_COGS", nullable = true)
    public BigDecimal getDineInCogs() {
        return dineInCogs;
    }

    public void setDineInCogs(BigDecimal dineInCogs) {
        this.dineInCogs = dineInCogs;
    }

    @Column(name = "DELIVERY_COGS", nullable = true)
    public BigDecimal getDeliveryCogs() {
        return deliveryCogs;
    }

    public void setDeliveryCogs(BigDecimal deliveryCogs) {
        this.deliveryCogs = deliveryCogs;
    }

    @Column(name = "EMPLOYEE_MEAL_COGS", nullable = true)
    public BigDecimal getEmployeeMealCogs() {
        return employeeMealCogs;
    }

    public void setEmployeeMealCogs(BigDecimal employeeMealCogs) {
        this.employeeMealCogs = employeeMealCogs;
    }

    @Column(name = "UNSATISFIED_CUSTOMER_COST", nullable = true)
    public BigDecimal getUnsatifiedCustomerCost() {
        return unsatifiedCustomerCost;
    }

    public void setUnsatifiedCustomerCost(BigDecimal unsatifiedCustomerCost) {
        this.unsatifiedCustomerCost = unsatifiedCustomerCost;
    }

    @Column(name = "PPE_COST", nullable = true)
    public BigDecimal getPPECost() {
        return this.ppeCost;
    }

    public void setPPECost(BigDecimal ppeCost) {
        this.ppeCost = ppeCost;
    }

    @Column(name = "EXPIRY_WASTAGE", nullable = true)
    public BigDecimal getExpiryWastage() {
        return expiryWastage;
    }

    public void setExpiryWastage(BigDecimal expiryWastage) {
        this.expiryWastage = expiryWastage;
    }

    @Column(name = "WASTAGE_OTHER", nullable = true)
    public BigDecimal getWastageOther() {
        return wastageOther;
    }

    public void setWastageOther(BigDecimal wastageOther) {
        this.wastageOther = wastageOther;
    }

    @Column(name = "CONSUMABLE", nullable = true)
    public BigDecimal getConsumable() {
        return consumable;
    }

    public void setConsumable(BigDecimal consumable) {
        this.consumable = consumable;
    }

    @Column(name = "CONSUMABLE_UTILITY", nullable = true)
    public BigDecimal getConsumableUtility() {
        return consumableUtility;
    }

    public void setConsumableUtility(BigDecimal consumableUtility) {
        this.consumableUtility = consumableUtility;
    }

    @Column(name = "CONSUMABLE_STATIONARY", nullable = true)
    public BigDecimal getConsumableStationary() {
        return consumableStationary;
    }

    public void setConsumableStationary(BigDecimal consumableStationary) {
        this.consumableStationary = consumableStationary;
    }

    @Column(name = "CONSUMABLE_UNIFORM", nullable = true)
    public BigDecimal getConsumableUniform() {
        return consumableUniform;
    }

    public void setConsumableUniform(BigDecimal consumableUniform) {
        this.consumableUniform = consumableUniform;
    }

    @Column(name = "CONSUMABLE_EQUIPMENT", nullable = true)
    public BigDecimal getConsumableEquipment() {
        return consumableEquipment;
    }

    public void setConsumableEquipment(BigDecimal consumableEquipment) {
        this.consumableEquipment = consumableEquipment;
    }

    @Column(name = "CONSUMABLE_CUTLERY", nullable = true)
    public BigDecimal getConsumableCutlery() {
        return consumableCutlery;
    }

    public void setConsumableCutlery(BigDecimal consumableCutlery) {
        this.consumableCutlery = consumableCutlery;
    }

    @Column(name = "CONSUMABLE_DISPOSABLE", nullable = true)
    public BigDecimal getConsumableDisposable() {
        return consumableDisposable;
    }

    public void setConsumableDisposable(BigDecimal consumableDisposable) {
        this.consumableDisposable = consumableDisposable;
    }

    @Column(name = "CONSUMABLE_LHI", nullable = true)
    public BigDecimal getConsumableLhi() {
        return consumableLhi;
    }

    public void setConsumableLhi(BigDecimal consumableLhi) {
        this.consumableLhi = consumableLhi;
    }

    @Column(name = "CONSUMABLE_IT", nullable = true)
    public BigDecimal getConsumableIt() {
        return consumableIt;
    }

    public void setConsumableIt(BigDecimal consumableIt) {
        this.consumableIt = consumableIt;
    }

    @Column(name = "CONSUMABLE_MAINTENANCE", nullable = true)
    public BigDecimal getConsumableMaintenance() {
        return consumableMaintenance;
    }

    public void setConsumableMaintenance(BigDecimal consumableMaintenance) {
        this.consumableMaintenance = consumableMaintenance;
    }

    @Column(name = "CONSUMABLE_KITCHEN_EQUIPMENT", nullable = true)
    public BigDecimal getConsumableKitchenEquipment() {
        return consumableKitchenEquipment;
    }

    public void setConsumableKitchenEquipment(BigDecimal consumableKitchenEquipment) {
        this.consumableKitchenEquipment = consumableKitchenEquipment;
    }

    @Column(name = "CONSUMABLE_OFFICE_EQUIPMENT", nullable = true)
    public BigDecimal getConsumableOfficeEquipment() {
        return consumableOfficeEquipment;
    }

    public void setConsumableOfficeEquipment(BigDecimal consumableOfficeEquipment) {
        this.consumableOfficeEquipment = consumableOfficeEquipment;
    }

    @Column(name = "CONSUMABLE_CHAI_MONK", nullable = true)
    public BigDecimal getConsumableChaiMonk() {
        return consumableChaiMonk;
    }

    public void setConsumableChaiMonk(BigDecimal consumableChaiMonk) {
        this.consumableChaiMonk = consumableChaiMonk;
    }

//	@Column(name = "CONSUMABLE_FIXED_ASSETS", nullable = true)
//	public BigDecimal getFixedAssets() {
//		return fixedAssets;
//	}
//
//	public void setFixedAssets(BigDecimal fixedAssets) {
//		this.fixedAssets = fixedAssets;
//	}

    @Column(name = "FIXED_ASSETS_HQ", nullable = true)
    public BigDecimal getFixedAssetsCapex() {
        return fixedAssetsCapex;
    }

    public void setFixedAssetsCapex(BigDecimal fixedAssetsCapex) {
        this.fixedAssetsCapex = fixedAssetsCapex;
    }

    @Column(name = "DINE_IN_COGS_TAX", nullable = true)
    public BigDecimal getDineInCogsTax() {
        return dineInCogsTax;
    }

    public void setDineInCogsTax(BigDecimal dineInCogsTax) {
        this.dineInCogsTax = dineInCogsTax;
    }

    @Column(name = "DELIVERY_COGS_TAX", nullable = true)
    public BigDecimal getDeliveryCogsTax() {
        return deliveryCogsTax;
    }

    public void setDeliveryCogsTax(BigDecimal deliveryCogsTax) {
        this.deliveryCogsTax = deliveryCogsTax;
    }

    @Column(name = "EMPLOYEE_MEAL_COGS_TAX", nullable = true)
    public BigDecimal getEmployeeMealCogsTax() {
        return employeeMealCogsTax;
    }

    public void setEmployeeMealCogsTax(BigDecimal employeeMealCogsTax) {
        this.employeeMealCogsTax = employeeMealCogsTax;
    }

    @Column(name = "UNSATISFIED_CUSTOMER_COST_TAX", nullable = true)
    public BigDecimal getUnsatifiedCustomerCostTax() {
        return unsatifiedCustomerCostTax;
    }

    public void setUnsatifiedCustomerCostTax(BigDecimal unsatifiedCustomerCostTax) {
        this.unsatifiedCustomerCostTax = unsatifiedCustomerCostTax;
    }

    @Column(name = "PPE_COST_TAX", nullable = true)
    public BigDecimal getPPECostTax() {
        return this.ppeCostTax;
    }

    public void setPPECostTax(BigDecimal ppeCostTax) {
        this.ppeCostTax = ppeCostTax;
    }

    @Column(name = "EXPIRY_WASTAGE_TAX", nullable = true)
    public BigDecimal getExpiryWastageTax() {
        return expiryWastageTax;
    }

    public void setExpiryWastageTax(BigDecimal expiryWastageTax) {
        this.expiryWastageTax = expiryWastageTax;
    }

    @Column(name = "WASTAGE_OTHER_TAX", nullable = true)
    public BigDecimal getWastageOtherTax() {
        return wastageOtherTax;
    }

    public void setWastageOtherTax(BigDecimal wastageOtherTax) {
        this.wastageOtherTax = wastageOtherTax;
    }

    @Column(name = "CONSUMABLE_TAX", nullable = true)
    public BigDecimal getConsumableTax() {
        return consumableTax;
    }

    public void setConsumableTax(BigDecimal consumableTax) {
        this.consumableTax = consumableTax;
    }

    @Column(name = "FIXED_ASSETS_TAX", nullable = true)
    public BigDecimal getFixedAssetsTax() {
        return fixedAssetsTax;
    }

    public void setFixedAssetsTax(BigDecimal fixedAssetsTax) {
        this.fixedAssetsTax = fixedAssetsTax;
    }

    @Column(name = "FIXED_ASSETS_HQ_TAX", nullable = true)
    public BigDecimal getFixedAssetsCapexTax() {
        return fixedAssetsCapexTax;
    }

    public void setFixedAssetsCapexTax(BigDecimal fixedAssetsCapexTax) {
        this.fixedAssetsCapexTax = fixedAssetsCapexTax;
    }

    @Column(name = "SALARY_INCENTIVE", nullable = true)
    public BigDecimal getSalaryIncentive() {
        return salaryIncentive;
    }

    public void setSalaryIncentive(BigDecimal salaryIncentive) {
        this.salaryIncentive = salaryIncentive;
    }

    @Column(name = "SECURITY_GUARD_CHARGES", nullable = true)
    public BigDecimal getSecurityGuardCharges() {
        return securityGuardCharges;
    }

    public void setSecurityGuardCharges(BigDecimal securityGuardCharges) {
        this.securityGuardCharges = securityGuardCharges;
    }

    @Column(name = "PERFORMANCE_INCENTIVE", nullable = true)
    public BigDecimal getSalesIncentive() {
        return salesIncentive;
    }

    public void setSalesIncentive(BigDecimal salesIncentive) {
        this.salesIncentive = salesIncentive;
    }

    @Column(name = "DEPRECIATION_ON_BIKE", nullable = true)
    public BigDecimal getDepreciationOfBike() {
        return depreciationOfBike;
    }

    public void setDepreciationOfBike(BigDecimal depreciationOfBike) {
        this.depreciationOfBike = depreciationOfBike;
    }

    @Column(name = "FUEL_CHARGES", nullable = true)
    public BigDecimal getFuelCharges() {
        return fuelCharges;
    }

    public void setFuelCharges(BigDecimal fuelCharges) {
        this.fuelCharges = fuelCharges;
    }

    @Column(name = "FUEL_CHARGES_CAFE", nullable = true)
    public BigDecimal getFuelChargesCafe() {
        return fuelChargesCafe;
    }

    public void setFuelChargesCafe(BigDecimal fuelChargesCafe) {
        this.fuelChargesCafe = fuelChargesCafe;
    }

    @Column(name = "VEHICLE_REGULAR_MAINTENANCE_CAFE", nullable = true)
    public BigDecimal getVehicleRegularMaintenance() {
        return vehicleRegularMaintenance;
    }

    public void setVehicleRegularMaintenance(BigDecimal vehicleRegularMaintenance) {
        this.vehicleRegularMaintenance = vehicleRegularMaintenance;
    }

    @Column(name = "PARKING_CHARGES_CAFE", nullable = true)
    public BigDecimal getParkingCharges() {
        return parkingCharges;
    }

    public void setParkingCharges(BigDecimal parkingCharges) {
        this.parkingCharges = parkingCharges;
    }

    @Column(name = "MARKETING_AND_SAMPLING", nullable = true)
    public BigDecimal getMarketingAndSampling() {
        return marketingAndSampling;
    }

    public void setMarketingAndSampling(BigDecimal marketingAndSampling) {
        this.marketingAndSampling = marketingAndSampling;
    }

    @Column(name = "MARKETING_AND_SAMPLING_TAX", nullable = true)
    public BigDecimal getMarketingAndSamplingTax() {
        return marketingAndSamplingTax;
    }

    public void setMarketingAndSamplingTax(BigDecimal marketingAndSamplingTax) {
        this.marketingAndSamplingTax = marketingAndSamplingTax;
    }

    @Column(name = "CONSUMABLE_MARKETING", nullable = true)
    public BigDecimal getConsumableMarketing() {
        return consumableMarketing;
    }

    public void setConsumableMarketing(BigDecimal consumableMarketing) {
        this.consumableMarketing = consumableMarketing;
    }

    @Column(name = "CONSUMABLE_MARKETING_TAX", nullable = true)
    public BigDecimal getConsumableMarketingTax() {
        return consumableMarketingTax;
    }

    public void setConsumableMarketingTax(BigDecimal consumableMarketingTax) {
        this.consumableMarketingTax = consumableMarketingTax;
    }

    @Column(name = "LOGISTIC_CHARGES", nullable = true)
    public BigDecimal getLogisticCharges() {
        return logisticCharges;
    }

    public void setLogisticCharges(BigDecimal logisticCharges) {
        this.logisticCharges = logisticCharges;
    }

    @Column(name = "ENERGY_ELECTRICITY", nullable = true)
    public BigDecimal getEnergyElectricity() {
        return energyElectricity;
    }

    public void setEnergyElectricity(BigDecimal energyElectricity) {
        this.energyElectricity = energyElectricity;
    }

    @Column(name = "ENERGY_DG_RUNNING", nullable = true)
    public BigDecimal getEnergyDGRunning() {
        return energyDGRunning;
    }

    public void setEnergyDGRunning(BigDecimal energyDGRunning) {
        this.energyDGRunning = energyDGRunning;
    }

    @Column(name = "ENERGY_DG_RUNNING_CAFE", nullable = true)
    public BigDecimal getEnergyDGRunningCafe() {
        return energyDGRunningCafe;
    }

    public void setEnergyDGRunningCafe(BigDecimal energyDGRunningCafe) {
        this.energyDGRunningCafe = energyDGRunningCafe;
    }

    @Column(name = "WATER_CHARGES", nullable = true)
    public BigDecimal getWaterCharges() {
        return waterCharges;
    }

    public void setWaterCharges(BigDecimal waterCharges) {
        this.waterCharges = waterCharges;
    }


    @Column(name = "WATER_CHARGES_CAFE", nullable = true)
    public BigDecimal getWaterChargesCafe() {
        return waterChargesCafe;
    }

    public void setWaterChargesCafe(BigDecimal waterChargesCafe) {
        this.waterChargesCafe = waterChargesCafe;
    }

    @Column(name = "COMMUNICATION_INTERNET", nullable = true)
    public BigDecimal getCommunicationInternet() {
        return communicationInternet;
    }

    public void setCommunicationInternet(BigDecimal communicationInternet) {
        this.communicationInternet = communicationInternet;
    }

    @Column(name = "COMMUNICATION_TELEPHONE", nullable = true)
    public BigDecimal getCommunicationTelephone() {
        return communicationTelephone;
    }

    public void setCommunicationTelephone(BigDecimal communicationTelephone) {
        this.communicationTelephone = communicationTelephone;
    }

    @Column(name = "COMMUNICATION_ILL", nullable = true)
    public BigDecimal getCommunicationILL() {
        return communicationILL;
    }

    public void setCommunicationILL(BigDecimal communicationILL) {
        this.communicationILL = communicationILL;
    }

    @Column(name = "CREDIT_CARD_TRANSACTION_CHARGES", nullable = true)
    public BigDecimal getCreditCardTransactionCharges() {
        return creditCardTransactionCharges;
    }

    public void setCreditCardTransactionCharges(BigDecimal creditCardTransactionCharges) {
        this.creditCardTransactionCharges = creditCardTransactionCharges;
    }

    @Column(name = "VOUCHER_TRANSACTION_CHARGES", nullable = true)
    public BigDecimal getVoucherTransactionCharges() {
        return voucherTransactionCharges;
    }

    public void setVoucherTransactionCharges(BigDecimal voucherTransactionCharges) {
        this.voucherTransactionCharges = voucherTransactionCharges;
    }

    @Column(name = "WALLETS_TRANSACTION_CHARGES", nullable = true)
    public BigDecimal getWalletsTransactionCharges() {
        return walletsTransactionCharges;
    }

    public void setWalletsTransactionCharges(BigDecimal walletsTransactionCharges) {
        this.walletsTransactionCharges = walletsTransactionCharges;
    }

    @Column(name = "COMMISSION_CHANNEL_PARTNERS", nullable = true)
    public BigDecimal getCommissionChannelPartners() {
        return commissionChannelPartners;
    }

    public void setCommissionChannelPartners(BigDecimal commissionChannelPartners) {
        this.commissionChannelPartners = commissionChannelPartners;
    }

    @Column(name = "CANCELLATION_CHARGES_CHANNEL_PARTNERS", nullable = true)
    public BigDecimal getCancellationChargesChannelPartners() {
        return cancellationChargesChannelPartners;
    }

    public void setCancellationChargesChannelPartners(BigDecimal cancellationChargesChannelPartners) {
        this.cancellationChargesChannelPartners = cancellationChargesChannelPartners;
    }

    @Column(name = "COMMISSION_CHANGE", nullable = true)
    public BigDecimal getCommissionChange() {
        return commissionChange;
    }

    public void setCommissionChange(BigDecimal commissionChange) {
        this.commissionChange = commissionChange;
    }

    @Column(name = "COMMISSION_CHANGE_CAFE", nullable = true)
    public BigDecimal getCommissionChangeCafe() {
        return commissionChangeCafe;
    }

    public void setCommissionChangeCafe(BigDecimal commissionChangeCafe) {
        this.commissionChangeCafe = commissionChangeCafe;
    }

    @Column(name = "PAYROLL_PROCESSING_FEES", nullable = true)
    public BigDecimal getPayrollProcessingFee() {
        return payrollProcessingFee;
    }

    public void setPayrollProcessingFee(BigDecimal payrollProcessingFee) {
        this.payrollProcessingFee = payrollProcessingFee;
    }

    @Column(name = "NEWSPAPER_CHARGES", nullable = true)
    public BigDecimal getNewsPaper() {
        return newsPaper;
    }

    public void setNewsPaper(BigDecimal newsPaper) {
        this.newsPaper = newsPaper;
    }

    @Column(name = "STAFF_WELFARE_EXPENSES", nullable = true)
    public BigDecimal getStaffWelfareExpenses() {
        return staffWelfareExpenses;
    }

    public void setStaffWelfareExpenses(BigDecimal staffWelfareExpenses) {
        this.staffWelfareExpenses = staffWelfareExpenses;
    }

    @Column(name = "EXPENSE_OTHER", nullable = true)
    public BigDecimal getExpenseOthers() {
        return expenseOthers;
    }

    public void setExpenseOthers(BigDecimal expenseOthers) {
        this.expenseOthers = expenseOthers;
    }

    @Column(name = "FIXED_PARKING_CHARGES", nullable = true)
    public BigDecimal getFixedParkingCharges() {
        return fixedParkingCharges;
    }

    public void setFixedParkingCharges(BigDecimal fixedParkingCharges) {
        this.fixedParkingCharges = fixedParkingCharges;
    }

    @Column(name = "COGS_LOGISTIC", nullable = true)
    public BigDecimal getCogsLogistics() {
        return cogsLogistics;
    }

    public void setCogsLogistics(BigDecimal COGsLogistics) {
        this.cogsLogistics = COGsLogistics;
    }

    @Column(name = "PRE_OPENING_CAM_ELE_WATER", nullable = true)
    public BigDecimal getPreOpeningCamEleWater() {
        return preOpeningCamEleWater;
    }

    public void setPreOpeningCamEleWater(BigDecimal preOpeningCamEleWater) {
        this.preOpeningCamEleWater = preOpeningCamEleWater;
    }

    @Column(name = "PRE_OPENING_REGISTRATION_CHARGES", nullable = true)
    public BigDecimal getPreOpeningRegistrationCharges() {
        return preOpeningRegistrationCharges;
    }

    public void setPreOpeningRegistrationCharges(BigDecimal preOpeningRegistrationCharges) {
        this.preOpeningRegistrationCharges = preOpeningRegistrationCharges;
    }

    @Column(name = "PRE_OPENING_STAMP_DUTY_CHARGES", nullable = true)
    public BigDecimal getPreOpeningStampDutyCharges() {
        return preOpeningStampDutyCharges;
    }

    public void setPreOpeningStampDutyCharges(BigDecimal preOpeningStampDutyCharges) {
        this.preOpeningStampDutyCharges = preOpeningStampDutyCharges;
    }

    @Column(name = "PRE_OPENING_CONSUMABLE_TAX", nullable = true)
    public BigDecimal getPreOpeningConsumableTax() {
        return preOpeningConsumableTax;
    }

    public void setPreOpeningConsumableTax(BigDecimal preOpeningConsumableTax) {
        this.preOpeningConsumableTax = preOpeningConsumableTax;
    }

    @Column(name = "SUPPORT", nullable = true)
    public BigDecimal getSupport() {
        return support;
    }

    public void setSupport(BigDecimal support) {
        this.support = support;
    }

    @Column(name = "CORPORATE_MARKETING_CHANNEL_PARTNER", nullable = true)
    public BigDecimal getCorporateMarketingChannelPartner() {
        return corporateMarketingChannelPartner;
    }

    public void setCorporateMarketingChannelPartner(BigDecimal corporateMarketingChannelPartner) {
        this.corporateMarketingChannelPartner = corporateMarketingChannelPartner;
    }

    @Column(name = "PRE_OPENING_ELE_WATER", nullable = true)
    public BigDecimal getPreOpeningEleWater() {
        return preOpeningEleWater;
    }

    public void setPreOpeningEleWater(BigDecimal preOpeningEleWater) {
        this.preOpeningEleWater = preOpeningEleWater;
    }

    @Column(name = "PRE_OPENING_CAM", nullable = true)
    public BigDecimal getPreOpeningCam() {
        return preOpeningCam;
    }

    public void setPreOpeningCam(BigDecimal preOpeningCam) {
        this.preOpeningCam = preOpeningCam;
    }

    @Column(name = "INTEREST_ON_FIXED_DEPOSIT_FICO", nullable = true)
    public BigDecimal getInterestOnFixedDepositFICO() {
        return interestOnFixedDepositFICO;
    }

    public void setInterestOnFixedDepositFICO(BigDecimal interestOnFixedDepositFICO) {
        this.interestOnFixedDepositFICO = interestOnFixedDepositFICO;
    }

    @Column(name = "LIABILITY_NO_LONGER_REQUIRED_WRITTEN_BACK", nullable = true)
    public BigDecimal getLiabilityNoLongerRequiredWrittenBack() {
        return liabilityNoLongerRequiredWrittenBack;
    }

    public void setLiabilityNoLongerRequiredWrittenBack(BigDecimal liabilityNoLongerRequiredWrittenBack) {
        this.liabilityNoLongerRequiredWrittenBack = liabilityNoLongerRequiredWrittenBack;
    }


    @Column(name = "INTEREST_ON_TERM_LOAN", nullable = true)
    public BigDecimal getInterestOnTermLoan() {
        return interestOnTermLoan;
    }

    public void setInterestOnTermLoan(BigDecimal interestOnTermLoan) {
        this.interestOnTermLoan = interestOnTermLoan;
    }

    @Column(name = "AMORTIZATION_OF_INTANGIBLE_ASSETS", nullable = true)
    public BigDecimal getAmortizationOfIntangibleAssets() {
        return amortizationOfIntangibleAssets;
    }

    public void setAmortizationOfIntangibleAssets(BigDecimal amortizationOfIntangibleAssets) {
        this.amortizationOfIntangibleAssets = amortizationOfIntangibleAssets;
    }

    @Column(name = "COURIER_CHARGES", nullable = true)
    public BigDecimal getCourierCharges() {
        return courierCharges;
    }

    public void setCourierCharges(BigDecimal courierCharges) {
        this.courierCharges = courierCharges;
    }

    @Column(name = "PRINTING_AND_STATIONARY", nullable = true)
    public BigDecimal getPrintingAndStationary() {
        return printingAndStationary;
    }

    public void setPrintingAndStationary(BigDecimal printingAndStationary) {
        this.printingAndStationary = printingAndStationary;
    }

    @Column(name = "BUSINESS_PROMOTION", nullable = true)
    public BigDecimal getBusinessPromotion() {
        return businessPromotion;
    }

    public void setBusinessPromotion(BigDecimal businessPromotion) {
        this.businessPromotion = businessPromotion;
    }

    @Column(name = "LEGAL_CHARGES", nullable = true)
    public BigDecimal getLegalCharges() {
        return legalCharges;
    }

    public void setLegalCharges(BigDecimal legalCharges) {
        this.legalCharges = legalCharges;
    }

    @Column(name = "PROFESSIONAL_CHARGES", nullable = true)
    public BigDecimal getProfessionalCharges() {
        return professionalCharges;
    }

    public void setProfessionalCharges(BigDecimal professionalCharges) {
        this.professionalCharges = professionalCharges;
    }

    @Column(name = "PROPERTY_TAX", nullable = true)
    public BigDecimal getPropertyTax() {
        return propertyTax;
    }

    public void setPropertyTax(BigDecimal propertyTax) {
        this.propertyTax = propertyTax;
    }

    @Column(name = "PRE_OPENING_LICENSES_FEES", nullable = true)
    public BigDecimal getOpeningLicencesFees() {
        return openingLicencesFees;
    }

    public void setOpeningLicencesFees(BigDecimal openingLicencesFees) {
        this.openingLicencesFees = openingLicencesFees;
    }

//	@Column(name = "REGISTRATION_CHARGES", nullable = true)
//	public BigDecimal getRegistrationCharges() {
//		return registrationCharges;
//	}
//
//	public void setRegistrationCharges(BigDecimal registrationCharges) {
//		this.registrationCharges = registrationCharges;
//	}

//	@Column(name = "STAMP_DUTY_CHARGES", nullable = true)
//	public BigDecimal getStampDutyCharges() {
//		return stampDutyCharges;
//	}
//
//	public void setStampDutyCharges(BigDecimal stampDutyCharges) {
//		this.stampDutyCharges = stampDutyCharges;
//	}

    @Column(name = "DESIGNING_FEES", nullable = true)
    public BigDecimal getDesigningFees() {
        return designingFees;
    }

    public void setDesigningFees(BigDecimal designingFees) {
        this.designingFees = designingFees;
    }

    @Column(name = "BUILDING_MAINTENANCE_CAFE", nullable = true)
    public BigDecimal getBuildingMaintenance() {
        return buildingMaintenance;
    }

    public void setBuildingMaintenance(BigDecimal buildingMaintenance) {
        this.buildingMaintenance = buildingMaintenance;
    }

    @Column(name = "CLEANING_CHARGES", nullable = true)
    public BigDecimal getCleaningCharges() {
        return cleaningCharges;
    }

    public void setCleaningCharges(BigDecimal cleaningCharges) {
        this.cleaningCharges = cleaningCharges;
    }

    @Column(name = "COMPUTER_IT_MAINTENANCE_CAFE", nullable = true)
    public BigDecimal getComputerMaintenance() {
        return computerMaintenance;
    }

    public void setComputerMaintenance(BigDecimal computerMaintenance) {
        this.computerMaintenance = computerMaintenance;
    }

    @Column(name = "PEST_CONTROL_CHARGES", nullable = true)
    public BigDecimal getPestControlCharges() {
        return pestControlCharges;
    }

    public void setPestControlCharges(BigDecimal pestControlCharges) {
        this.pestControlCharges = pestControlCharges;
    }

    @Column(name = "EQUIPMENT_MAINTENANCE_CAFE", nullable = true)
    public BigDecimal getEquipmentMaintenance() {
        return equipmentMaintenance;
    }

    public void setEquipmentMaintenance(BigDecimal equipmentMaintenance) {
        this.equipmentMaintenance = equipmentMaintenance;
    }

    @Column(name = "PRONTO_AMC", nullable = true)
    public BigDecimal getProntoAMC() {
        return prontoAMC;
    }

    public void setProntoAMC(BigDecimal prontoAMC) {
        this.prontoAMC = prontoAMC;
    }

    @Column(name = "RO_AMC", nullable = true)
    public BigDecimal getRoAMC() {
        return roAMC;
    }

    public void setRoAMC(BigDecimal roAMC) {
        this.roAMC = roAMC;
    }

    @Column(name = "DG_RENTAL", nullable = true)
    public BigDecimal getDgRental() {
        return dgRental;
    }

    public void setDgRental(BigDecimal dgRental) {
        this.dgRental = dgRental;
    }

    @Column(name = "EDC_RENTAL", nullable = true)
    public BigDecimal getEdcRental() {
        return edcRental;
    }

    public void setEdcRental(BigDecimal edcRental) {
        this.edcRental = edcRental;
    }

    @Column(name = "SYSTEM_RENTAL", nullable = true)
    public BigDecimal getSystemRental() {
        return systemRental;
    }

    public void setSystemRental(BigDecimal systemRental) {
        this.systemRental = systemRental;
    }

    @Column(name = "RO_RENTAL", nullable = true)
    public BigDecimal getRoRental() {
        return roRental;
    }

    public void setRoRental(BigDecimal roRental) {
        this.roRental = roRental;
    }

    @Column(name = "PROPERTY_FIX_RENT", nullable = true)
    public BigDecimal getPropertyFixRent() {
        return propertyFixRent;
    }

    public void setPropertyFixRent(BigDecimal propertyFixRent) {
        this.propertyFixRent = propertyFixRent;
    }

    @Column(name = "REVENUE_SHARE", nullable = true)
    public BigDecimal getRevenueShare() {
        return revenueShare;
    }

    public void setRevenueShare(BigDecimal revenueShare) {
        this.revenueShare = revenueShare;
    }


    @Column(name = "REVENUE_SHARE_DINE_IN", nullable = true)
    public BigDecimal getRevenueShareDineIn() {
        return revenueShareDineIn;
    }

    public void setRevenueShareDineIn(BigDecimal revenueShareDineIn) {
        this.revenueShareDineIn = revenueShareDineIn;
    }

    @Column(name = "REVENUE_SHARE_DELIVERY", nullable = true)
    public BigDecimal getRevenueShareDelivery() {
        return revenueShareDelivery;
    }

    public void setRevenueShareDelivery(BigDecimal revenueShareDelivery) {
        this.revenueShareDelivery = revenueShareDelivery;
    }

    @Column(name = "FIX_CAM", nullable = true)
    public BigDecimal getFixCAM() {
        return fixCAM;
    }

    public void setFixCAM(BigDecimal fixCAM) {
        this.fixCAM = fixCAM;
    }

    @Column(name = "CHILLING_CHARGES", nullable = true)
    public BigDecimal getChillingCharges() {
        return chillingCharges;
    }

    public void setChillingCharges(BigDecimal chillingCharges) {
        this.chillingCharges = chillingCharges;
    }

    @Column(name = "MARKETING_CHARGES", nullable = true)
    public BigDecimal getMarketingCharges() {
        return marketingCharges;
    }

    public void setMarketingCharges(BigDecimal marketingCharges) {
        this.marketingCharges = marketingCharges;
    }

    @Column(name = "PETTY_CASH_RENTALS", nullable = true)
    public BigDecimal getPettyCashRentals() {
        return pettyCashRentals;
    }

    public void setPettyCashRentals(BigDecimal pettyCashRentals) {
        this.pettyCashRentals = pettyCashRentals;
    }

    @Column(name = "MUSIC_RENTALS", nullable = true)
    public BigDecimal getMusicRentals() {
        return musicRentals;
    }

    public void setMusicRentals(BigDecimal musicRentals) {
        this.musicRentals = musicRentals;
    }

    @Column(name = "INTERNET_PARTNER_RENTAL", nullable = true)
    public BigDecimal getInternetPartnerRental() {
        return internetPartnerRental;
    }

    public void setInternetPartnerRental(BigDecimal internetPartnerRental) {
        this.internetPartnerRental = internetPartnerRental;
    }

    @Column(name = "SUPPORT_OPS_MANAGEMENT", nullable = true)
    public BigDecimal getSupportOpsManagement() {
        return supportOpsManagement;
    }

    public void setSupportOpsManagement(BigDecimal supportOperations) {
        this.supportOpsManagement = supportOperations;
    }

    @Column(name = "TECHNOLOGY_PLATFORM_CHARGES", nullable = true)
    public BigDecimal getTechnologyPlatformCharges() {
        return technologyPlatformCharges;
    }

    public void setTechnologyPlatformCharges(BigDecimal techologyPlatformCharges) {
        this.technologyPlatformCharges = techologyPlatformCharges;
    }

    @Column(name = "TECHNOLOGY_TRAINING", nullable = true)
    public BigDecimal getTechnologyTraining() {
        return technologyTraining;
    }

    public void setTechnologyTraining(BigDecimal techologyTraining) {
        this.technologyTraining = techologyTraining;
    }

    @Column(name = "TECHNOLOGY_OTHERS", nullable = true)
    public BigDecimal getTechnologyOthers() {
        return technologyOthers;
    }

    public void setTechnologyOthers(BigDecimal techologyOthers) {
        this.technologyOthers = techologyOthers;
    }

    @Column(name = "TECHNOLOGY_POS", nullable = true)
    public BigDecimal getTechnologyVariable() {
        return technologyVariable;
    }

    public void setTechnologyVariable(BigDecimal technologyVariable) {
        this.technologyVariable = technologyVariable;
    }

    @Column(name = "CORPORATE_MARKETING_DIGITAL", nullable = true)
    public BigDecimal getCorporateMarketingDigital() {
        return corporateMarketingDigital;
    }

    public void setCorporateMarketingDigital(BigDecimal corporateMarketingDigital) {
        this.corporateMarketingDigital = corporateMarketingDigital;
    }

    @Column(name = "CORPORATE_MARKETING_AD_OFFLINE", nullable = true)
    public BigDecimal getCorporateMarketingAdvOffline() {
        return corporateMarketingAdvOffline;
    }

    public void setCorporateMarketingAdvOffline(BigDecimal corporateMarketingAdvOffline) {
        this.corporateMarketingAdvOffline = corporateMarketingAdvOffline;
    }

	@Column(name = "CORPORATE_MARKETING_AD_ONLINE", nullable = true)
	public BigDecimal getCorporateMarketingAdvOnline() {
		return corporateMarketingAdvOnline;
	}

	public void setCorporateMarketingAdvOnline(BigDecimal corporateMarketingAdvOnline) {
		this.corporateMarketingAdvOnline = corporateMarketingAdvOnline;
	}

    @Column(name = "CORPORATE_MARKETING_OUTDOOR", nullable = true)
    public BigDecimal getCorporateMarketingOutdoor() {
        return corporateMarketingOutdoor;
    }

    public void setCorporateMarketingOutdoor(BigDecimal corporateMarketingOutdoor) {
        this.corporateMarketingOutdoor = corporateMarketingOutdoor;
    }

    @Column(name = "CORPORATE_MARKETING_PHOTO", nullable = true)
    public BigDecimal getCorporateMarketingPhotography() {
        return corporateMarketingPhotography;
    }

    public void setCorporateMarketingPhotography(BigDecimal corporateMarketingPhotography) {
        this.corporateMarketingPhotography = corporateMarketingPhotography;
    }

    @Column(name = "CORPORATE_MARKETING_AGENCY_FEES", nullable = true)
    public BigDecimal getCorporateMarketingAgencyFees() {
        return corporateMarketingAgencyFees;
    }

    public void setCorporateMarketingAgencyFees(BigDecimal corporateMarketingAgencyFees) {
        this.corporateMarketingAgencyFees = corporateMarketingAgencyFees;
    }

    @Column(name = "STOCK_VARIANCE", nullable = true)
    public BigDecimal getStockVariance() {
        return stockVariance;
    }

    public void setStockVariance(BigDecimal stockVariance) {
        this.stockVariance = stockVariance;
    }

    @Column(name = "STOCK_VARIANCE_TAX", nullable = true)
    public BigDecimal getStockVarianceTax() {
        return stockVarianceTax;
    }

    public void setStockVarianceTax(BigDecimal stockVarianceTax) {
        this.stockVarianceTax = stockVarianceTax;
    }

    @Column(name = "VARIANCE_ZERO", nullable = true)
    public BigDecimal getVarianceZero() {
        return varianceZero;
    }

    public void setVarianceZero(BigDecimal zeroVariance) {
        this.varianceZero = zeroVariance;
    }

    @Column(name = "VARIANCE_ZERO_TAX", nullable = true)
    public BigDecimal getVarianceZeroTax() {
        return varianceZeroTax;
    }

    public void setVarianceZeroTax(BigDecimal zeroVarianceTax) {
        this.varianceZeroTax = zeroVarianceTax;
    }

    @Column(name = "MARKETING_NPI_CAFE", nullable = true)
    public BigDecimal getMarketingNPI() {
        return marketingNPI;
    }

    public void setMarketingNPI(BigDecimal marketingNPI) {
        this.marketingNPI = marketingNPI;
    }

    @Column(name = "GIFT_CARD_OFFER", nullable = true)
    public BigDecimal getGiftCardOffer() {
        return giftCardOffer;
    }

    public void setGiftCardOffer(BigDecimal giftCardOffer) {
        this.giftCardOffer = giftCardOffer;
    }

    @Column(name = "DELIVERY_CHARGES_VARIABLE", nullable = true)
    public BigDecimal getDeliveryChargesVariable() {
        return deliveryChargesVariable;
    }

    public void setDeliveryChargesVariable(BigDecimal deliveryChargesVariable) {
        this.deliveryChargesVariable = deliveryChargesVariable;
    }

    @Column(name = "CONSUMABLE_OTHER", nullable = true)
    public BigDecimal getConsumableOthers() {
        return consumableOthers;
    }

    public void setConsumableOthers(BigDecimal consumableOthers) {
        this.consumableOthers = consumableOthers;
    }

    @Column(name = "CONSUMABLE_OTHER_TAX", nullable = true)
    public BigDecimal getConsumableOthersTax() {
        return consumableOthersTax;
    }

    public void setConsumableOthersTax(BigDecimal consumableOthersTax) {
        this.consumableOthersTax = consumableOthersTax;
    }

    @Column(name = "EMPLOYEE_MEAL_SALES", nullable = true)
    public BigDecimal getEmployeeMealSales() {
        return employeeMealSales;
    }

    public void setEmployeeMealSales(BigDecimal employeeMealSales) {
        this.employeeMealSales = employeeMealSales;
    }

    @Column(name = "EMPLOYEE_MEAL_SALES_GMV", nullable = true)
    public BigDecimal getEmployeeMealGmv() {
        return employeeMealGmv;
    }

    public void setEmployeeMealGmv(BigDecimal employeeMealGmv) {
        this.employeeMealGmv = employeeMealGmv;
    }

    @Column(name = "EMPLOYEE_MEAL_TICKET", nullable = true)
    public Integer getEmployeeMealTicket() {
        return employeeMealTicket;
    }

    public void setEmployeeMealTicket(Integer employeeMealTicket) {
        this.employeeMealTicket = employeeMealTicket;
    }

    @Column(name = "GIFT_CARD_NET_SALES", nullable = true)
    public BigDecimal getGiftCardNetSale() {
        return giftCardNetSale;
    }

    public void setGiftCardNetSale(BigDecimal giftCardNetSale) {
        this.giftCardNetSale = giftCardNetSale;
    }

    @Column(name = "ON_REVENUE_SHARE", nullable = true)
    public String getOnRevenueShare() {
        return onRevenueShare;
    }

    public void setOnRevenueShare(String onRevenueShare) {
        this.onRevenueShare = onRevenueShare;
    }

    @Column(name = "EXPEDITURE_STATUS", nullable = true)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "TRAINING_COGS", nullable = true)
    public BigDecimal getTrainingCogs() {
        return trainingCogs;
    }

    public void setTrainingCogs(BigDecimal trainingCogs) {
        this.trainingCogs = trainingCogs;
    }

    @Column(name = "TRAINING_COGS_TAX", nullable = true)
    public BigDecimal getTrainingCogsTax() {
        return trainingCogsTax;
    }

    public void setTrainingCogsTax(BigDecimal trainingCogsTax) {
        this.trainingCogsTax = trainingCogsTax;
    }

    @Column(name = "REVENUE_ADJUSTMENT", nullable = true)
    public BigDecimal getRevenueAdjustment() {
        return revenueAdjustment;
    }

    public void setRevenueAdjustment(BigDecimal revenueAdjustment) {
        this.revenueAdjustment = revenueAdjustment;
    }

    @Column(name = "COST_ADJUSTMENT", nullable = true)
    public BigDecimal getCostAdjustment() {
        return costAdjustment;
    }

    public void setCostAdjustment(BigDecimal costAdjustment) {
        this.costAdjustment = costAdjustment;
    }


    @Column(name = "PHOTO_COPY_EXPENSES_CAFE", nullable = true)
    public BigDecimal getPhotoCopyExpensesCafe() {
        return photoCopyExpensesCafe;
    }

    public void setPhotoCopyExpensesCafe(BigDecimal photoCopyExpensesCafe) {
        this.photoCopyExpensesCafe = photoCopyExpensesCafe;
    }


    @Column(name = "NEWSPAPER_CHARGES_CAFE", nullable = true)
    public BigDecimal getNewsPaperCafe() {
        return newsPaperCafe;
    }

    public void setNewsPaperCafe(BigDecimal newsPaperCafe) {
        this.newsPaperCafe = newsPaperCafe;
    }


    @Column(name = "STAFF_WELFARE_EXPENSES_CAFE", nullable = true)
    public BigDecimal getStaffWelfareExpensesCafe() {
        return staffWelfareExpensesCafe;
    }

    public void setStaffWelfareExpensesCafe(BigDecimal staffWelfareExpensesCafe) {
        this.staffWelfareExpensesCafe = staffWelfareExpensesCafe;
    }


    @Column(name = "COURIER_CHARGES_CAFE", nullable = true)
    public BigDecimal getCourierChargesCafe() {
        return courierChargesCafe;
    }

    public void setCourierChargesCafe(BigDecimal courierChargesCafe) {
        this.courierChargesCafe = courierChargesCafe;
    }


    @Column(name = "PRINTING_AND_STATIONARY_CAFE", nullable = true)
    public BigDecimal getPrintingAndStationaryCafe() {
        return printingAndStationaryCafe;
    }

    public void setPrintingAndStationaryCafe(BigDecimal printingAndStationaryCafe) {
        this.printingAndStationaryCafe = printingAndStationaryCafe;
    }


    @Column(name = "BUSINESS_PROMOTION_CAFE", nullable = true)
    public BigDecimal getBusinessPromotionCafe() {
        return businessPromotionCafe;
    }

    public void setBusinessPromotionCafe(BigDecimal businessPromotionCafe) {
        this.businessPromotionCafe = businessPromotionCafe;
    }

    @Column(name = "CLEANING_CHARGES_CAFE", nullable = true)
    public BigDecimal getCleaningChargesCafe() {
        return cleaningChargesCafe;
    }

    public void setCleaningChargesCafe(BigDecimal cleaningChargesCafe) {
        this.cleaningChargesCafe = cleaningChargesCafe;
    }

    public UnitExpenditureDetail copy() {
        UnitExpenditureDetail d = new UnitExpenditureDetail();

        d.setDayClosureId(this.getDayClosureId());
        d.setSumoClosureId(this.getSumoClosureId());
        d.setUnitId(this.getUnitId());
        d.setUnitName(this.getUnitName());
        d.setYear(this.getYear());
        d.setMonth(this.getMonth());
        d.setDay(this.getDay());
        d.setBusinessDate(this.getBusinessDate());
        d.setOnRevenueShare(this.getOnRevenueShare());
        d.setTicket(this.getTicket());
        d.setRevenue(this.getRevenue());
        d.setNetSales(this.getNetSales());
        d.setNetRevenue(this.getNetRevenue());
        d.setApc(this.getApc());
        d.setGmv(this.getGmv());
        d.setDiscount(this.getDiscount());
        d.setDiscountLoyalty(this.getDiscountLoyalty());
        d.setDiscountMarketing(this.getDiscountMarketing());
        d.setDiscountOps(this.getDiscountOps());
        d.setDiscountBd(this.getDiscountBd());
        d.setDiscountEmployeeFico(this.getDiscountEmployeeFico());

        d.setEmpDiscountLoyalty(this.getEmpDiscountLoyalty());
        d.setEmpDiscountMarketing(this.getEmpDiscountMarketing());
        d.setEmpDiscountOps(this.getEmpDiscountOps());
        d.setEmpDiscountBd(this.getEmpDiscountBd());
        d.setEmpDiscountEmployeeFico(this.getEmpDiscountEmployeeFico());

        d.setDineInTicket(this.getDineInTicket());
        d.setDineInSales(this.getDineInSales());
        d.setDineInApc(this.getDineInApc());
        d.setDineInGmv(this.getDineInGmv());
        d.setDineInDiscount(this.getDineInDiscount());
        d.setDineInDiscountLoyalty(this.getDineInDiscountLoyalty());
        d.setDineInDiscountMarketing(this.getDineInDiscountMarketing());
        d.setDineInDiscountOps(this.getDineInDiscountOps());
        d.setDineInDiscountBd(this.getDineInDiscountBd());
        d.setDineInDiscountEmployeeFico(this.getDineInDiscountEmployeeFico());
        d.setDeliveryTicket(this.getDeliveryTicket());
        d.setDeliverySales(this.getDeliverySales());
        d.setDeliveryApc(this.getDeliveryApc());
        d.setDeliveryGmv(this.getDeliveryGmv());
        d.setDeliveryDiscount(this.getDeliveryDiscount());
        d.setDeliveryDiscountLoyalty(this.getDeliveryDiscountLoyalty());
        d.setDeliveryDiscountMarketing(this.getDeliveryDiscountMarketing());
        d.setDeliveryDiscountOps(this.getDeliveryDiscountOps());
        d.setDeliveryDiscountBd(this.getDeliveryDiscountBd());
        d.setDeliveryDiscountEmployeeFico(this.getDeliveryDiscountEmployeeFico());
        d.setEmployeeMealSales(this.getEmployeeMealSales());
        d.setEmployeeMealGmv(this.getEmployeeMealGmv());
        d.setEmployeeMealTicket(this.getEmployeeMealTicket());
        d.setGiftCardSale(this.getGiftCardSale());
        d.setGiftCardRedemption(this.getGiftCardRedemption());
        d.setGiftCardNetSale(this.getGiftCardNetSale());
        d.setDineInCogs(this.getDineInCogs());
        d.setDeliveryCogs(this.getDeliveryCogs());
        d.setEmployeeMealCogs(this.getEmployeeMealCogs());
        d.setUnsatifiedCustomerCost(this.getUnsatifiedCustomerCost());
        d.setPPECost(this.getPPECost());
        d.setExpiryWastage(this.getExpiryWastage());
        d.setWastageOther(this.getWastageOther());
        d.setConsumable(this.getConsumable());
//		d.setFixedAssets(this.getFixedAssets());
        d.setFixedAssetsCapex(this.getFixedAssetsCapex());

        d.setDepreciationOfFurnitureFixture(this.getDepreciationOfFurnitureFixture());
        d.setDepreciationOfOfficeEquipment(this.getDepreciationOfOfficeEquipment());
        d.setDepreciationOfKitchenEquipment(this.getDepreciationOfKitchenEquipment());
        d.setDepreciationOfEquipment(this.getDepreciationOfEquipment());
        d.setDepreciationOfIt(this.getDepreciationOfIt());
        d.setDepreciationOfVehicle(this.getDepreciationOfVehicle());
        d.setDepreciationOfOthers(this.getDepreciationOfOthers());

        d.setFixedAssetsDamage(this.getFixedAssetsDamage());
        d.setFixedAssetsDepreciation(this.getFixedAssetsDepreciation());
        d.setFixedAssetsLost(this.getFixedAssetsLost());

        d.setConsumableCutlery(this.getConsumableCutlery());
        d.setConsumableEquipment(this.getConsumableEquipment());
        d.setConsumableStationary(this.getConsumableStationary());
        d.setConsumableUniform(this.getConsumableUniform());
        d.setConsumableUtility(this.getConsumableUtility());

        d.setConsumableLhi(this.getConsumableLhi());
        d.setConsumableIt(this.getConsumableIt());
        d.setConsumableMaintenance(this.getConsumableMaintenance());
        d.setConsumableOfficeEquipment(this.getConsumableOfficeEquipment());
        d.setConsumableKitchenEquipment(this.getConsumableKitchenEquipment());
        d.setConsumableChaiMonk(this.getConsumableChaiMonk());

        d.setDineInCogsTax(this.getDineInCogsTax());
        d.setDeliveryCogsTax(this.getDeliveryCogsTax());
        d.setEmployeeMealCogsTax(this.getEmployeeMealCogsTax());
        d.setUnsatifiedCustomerCostTax(this.getUnsatifiedCustomerCostTax());
        d.setPPECostTax(this.getPPECostTax());
        d.setExpiryWastageTax(this.getExpiryWastageTax());
        d.setWastageOtherTax(this.getWastageOtherTax());
        d.setConsumableTax(this.getConsumableTax());

        // TODO Abhishek Sirohi - verify
        d.setConsumableUtilityTax(this.getConsumableUtilityTax());
        d.setConsumableStationaryTax(this.getConsumableStationaryTax());
        d.setConsumableUniformTax(this.getConsumableUniformTax());
        d.setConsumableEquipmentTax(this.getConsumableEquipmentTax());
        d.setConsumableCutleryTax(this.getConsumableCutleryTax());
        d.setConsumableDisposableTax(this.getConsumableDisposableTax());

        d.setConsumableLhiTax(this.getConsumableLhiTax());
        d.setConsumableItTax(this.getConsumableItTax());
        d.setConsumableMaintenanceTax(this.getConsumableMaintenanceTax());
        d.setConsumableOfficeEquipmentTax(this.getConsumableOfficeEquipmentTax());
        d.setConsumableKitchenEquipmentTax(this.getConsumableKitchenEquipmentTax());
        d.setConsumableChaiMonkTax(this.getConsumableChaiMonkTax());

        d.setFixedAssetsTax(this.getFixedAssetsTax());
        d.setFixedAssetsCapexTax(this.getFixedAssetsCapexTax());


        d.setFixedAssetsEquipmentTax(this.getFixedAssetsEquipmentTax());
        d.setFixedAssetFurnitureTax(this.getFixedAssetFurnitureTax());
        d.setFixedAssetsItTax(this.getFixedAssetsItTax());
        d.setFixedAssetsKitchenEquipmentTax(this.getFixedAssetsKitchenEquipmentTax());
        d.setFixedAssetsOfficeEquipmentTax(this.getFixedAssetsOfficeEquipmentTax());
        d.setFixedAssetsVehicleTax(this.getFixedAssetsVehicleTax());
        d.setFixedAssetsOthersSubCategoryCafeTax(this.getFixedAssetsOthersSubCategoryCafeTax());
        d.setFixedAssetsEquipmentHq(this.getFixedAssetsEquipmentHq());
        d.setFixedAssetFurnitureHq(this.getFixedAssetFurnitureHq());
        d.setFixedAssetsItHq(this.getFixedAssetsItHq());
        d.setFixedAssetsKitchenEquipmentHq(this.getFixedAssetsKitchenEquipmentHq());
        d.setFixedAssetsOfficeEquipmentHq(this.getFixedAssetsOfficeEquipmentHq());
        d.setFixedAssetsVehicleHq(this.getFixedAssetsVehicleHq());
        d.setFixedAssetsOthersSubCategoryHq(this.getFixedAssetsOthersSubCategoryHq());
        d.setFixedAssetsEquipmentHqTax(this.getFixedAssetsEquipmentHqTax());
        d.setFixedAssetFurnitureHqTax(this.getFixedAssetFurnitureHqTax());
        d.setFixedAssetsItHqTax(this.getFixedAssetsItHqTax());
        d.setFixedAssetsKitchenEquipmentHqTax(this.getFixedAssetsKitchenEquipmentHqTax());
        d.setFixedAssetsOfficeEquipmentHqTax(this.getFixedAssetsOfficeEquipmentHqTax());
        d.setFixedAssetsVehicleHqTax(this.getFixedAssetsVehicleHqTax());
        d.setFixedAssetsOthersSubCategoryHqTax(this.getFixedAssetsOthersSubCategoryHqTax());

        d.setSalary(this.getSalary());
        d.setSalaryIncentive(this.getSalaryIncentive());
        d.setSecurityGuardCharges(this.getSecurityGuardCharges());
        d.setSalesIncentive(this.getSalesIncentive());
        d.setDepreciationOfBike(this.getDepreciationOfBike());
        d.setFuelCharges(this.getFuelCharges());
        d.setVehicleRegularMaintenance(this.getVehicleRegularMaintenance());
        d.setParkingCharges(this.getParkingCharges());
        d.setMarketingAndSampling(this.getMarketingAndSampling());
        d.setMarketingAndSamplingTax(this.getMarketingAndSamplingTax());
        d.setConsumableMarketing(this.getConsumableMarketing());
        d.setConsumableMarketingTax(this.getConsumableMarketingTax());
        d.setLogisticCharges(this.getLogisticCharges());
        d.setEnergyElectricity(this.getEnergyElectricity());
        d.setEnergyDGRunning(this.getEnergyDGRunning());
        d.setEnergyDGRunningCafe(this.getEnergyDGRunningCafe());
        d.setWaterCharges(this.getWaterCharges());
        d.setCommunicationInternet(this.getCommunicationInternet());
        d.setCommunicationTelephone(this.getCommunicationTelephone());
        d.setCommunicationILL(this.getCommunicationILL());
        d.setCreditCardTransactionCharges(this.getCreditCardTransactionCharges());
        d.setVoucherTransactionCharges(this.getVoucherTransactionCharges());
        d.setWalletsTransactionCharges(this.getWalletsTransactionCharges());
        d.setCommissionChannelPartners(this.getCommissionChannelPartners());
        d.setCancellationChargesChannelPartners(this.getCancellationChargesChannelPartners());
        d.setCommissionChange(this.getCommissionChange());
        d.setPayrollProcessingFee(this.getPayrollProcessingFee());
        d.setNewsPaper(this.getNewsPaper());
        d.setStaffWelfareExpenses(this.getStaffWelfareExpenses());
        d.setExpenseOthers(this.getExpenseOthers());

        d.setFixedParkingCharges(this.getFixedParkingCharges());
        d.setCogsLogistics(this.getCogsLogistics());
        d.setPreOpeningCamEleWater(this.getPreOpeningCamEleWater());
        d.setPreOpeningRegistrationCharges(this.getPreOpeningRegistrationCharges());
        d.setPreOpeningStampDutyCharges(this.getPreOpeningStampDutyCharges());
        d.setPreOpeningConsumableTax(this.getPreOpeningConsumableTax());
        d.setSupport(this.getSupport());
        d.setCorporateMarketingChannelPartner(this.getCorporateMarketingChannelPartner());
        d.setPreOpeningEleWater(this.getPreOpeningEleWater());
        d.setPreOpeningCam(this.getPreOpeningCam());
        d.setInterestOnFixedDepositFICO(this.getInterestOnFixedDepositFICO());
        d.setInterestOnTermLoan(this.getInterestOnTermLoan());
        d.setLiabilityNoLongerRequiredWrittenBack(this.getLiabilityNoLongerRequiredWrittenBack());
        d.setAmortizationOfIntangibleAssets(this.getAmortizationOfIntangibleAssets());

        d.setCourierCharges(this.getCourierCharges());
        d.setPrintingAndStationary(this.getPrintingAndStationary());
        d.setBusinessPromotion(this.getBusinessPromotion());
        d.setLegalCharges(this.getLegalCharges());
        d.setProfessionalCharges(this.getProfessionalCharges());
        d.setPropertyTax(this.getPropertyTax());
        d.setOpeningLicencesFees(this.getOpeningLicencesFees());
//		d.setRegistrationCharges(this.getRegistrationCharges());
//		d.setStampDutyCharges(this.getStampDutyCharges());
        d.setDesigningFees(this.getDesigningFees());
        d.setBuildingMaintenance(this.getBuildingMaintenance());
        d.setCleaningCharges(this.getCleaningCharges());
        d.setComputerMaintenance(this.getComputerMaintenance());
        d.setPestControlCharges(this.getPestControlCharges());
        d.setEquipmentMaintenance(this.getEquipmentMaintenance());
        d.setProntoAMC(this.getProntoAMC());
        d.setDgRental(this.getDgRental());
        d.setEdcRental(this.getEdcRental());
        d.setSystemRental(this.getSystemRental());
        d.setRoRental(this.getRoRental());
        d.setPropertyFixRent(this.getPropertyFixRent());
        d.setRevenueShare(this.getRevenueShare());
        d.setRevenueShareDineIn(this.getRevenueShareDineIn());
        d.setRevenueShareDelivery(this.getRevenueShareDelivery());
        d.setFixCAM(this.getFixCAM());
        d.setChillingCharges(this.getChillingCharges());
        d.setMarketingCharges(this.getMarketingCharges());
        d.setPettyCashRentals(this.getPettyCashRentals());
        d.setMusicRentals(this.getMusicRentals());
        d.setInternetPartnerRental(this.getInternetPartnerRental());
        d.setSupportOpsManagement(this.getSupportOpsManagement());
        d.setTechnologyPlatformCharges(this.getTechnologyPlatformCharges());
        d.setTechnologyTraining(this.getTechnologyTraining());
        d.setTechnologyOthers(this.getTechnologyOthers());
        d.setTechnologyVariable(this.getTechnologyVariable());
        d.setCorporateMarketingDigital(this.getCorporateMarketingDigital());
        d.setCorporateMarketingAdvOffline(this.getCorporateMarketingAdvOffline());
		d.setCorporateMarketingAdvOnline(this.getCorporateMarketingAdvOnline());
        d.setCorporateMarketingOutdoor(this.getCorporateMarketingOutdoor());
        d.setCorporateMarketingAgencyFees(this.getCorporateMarketingAgencyFees());
        d.setStockVariance(this.getStockVariance());
        d.setStockVarianceTax(this.getStockVarianceTax());
        d.setMarketingNPI(this.getMarketingNPI());
        d.setGiftCardOffer(this.getGiftCardOffer());
        d.setDeliveryChargesVariable(this.getDeliveryChargesVariable());
        d.setConsumableOthers(this.getConsumableOthers());
        d.setConsumableOthersTax(this.getConsumableOthersTax());
        d.setTrainingCogs(this.getTrainingCogs());
        d.setTrainingCogsTax(this.getTrainingCogsTax());
        d.setDeliveryGiftCardNetSale(this.getDeliveryGiftCardNetSale());
        d.setDeliveryGiftCardRedemption(this.getDeliveryGiftCardRedemption());
        d.setDeliveryGiftCardSale(this.getDeliveryGiftCardSale());
        d.setDeliveryNetSales(this.getDeliveryNetSales());

        d.setDineInGiftCardNetSale(this.getDineInGiftCardNetSale());
        d.setDineInGiftCardRedemption(this.getDineInGiftCardRedemption());
        d.setDineInGiftCardSale(this.getDineInGiftCardSale());
        d.setDineInNetSales(this.getDineInNetSales());

        d.setConveyanceMarketing(this.getConveyanceMarketing());
        d.setConveyanceOperations(this.getConveyanceOperations());
        d.setConveyanceOthers(this.getConveyanceOthers());
        d.setAuditFee(this.getAuditFee());
        d.setAuditFeeOutOfPocket(this.getAuditFeeOutOfPocket());
//		d.setBadDebtsWrittenOff(this.getBadDebtsWrittenOff());
        d.setBrokerage(this.getBrokerage());
        d.setCharityAndDonations(this.getCharityAndDonations());
        d.setDomesticTicketsAndHotels(this.getDomesticTicketsAndHotels());
        d.setInternationalTicketsAndHotels(this.getInternationalTicketsAndHotels());
        d.setHouseKeepingCharges(this.getHouseKeepingCharges());
        d.setLateFeeCharges(this.getLateFeeCharges());
        d.setMarketingDataAnalysis(this.getMarketingDataAnalysis());
        d.setMiscellaneousExpenses(this.getMiscellaneousExpenses());
        d.setPenalty(this.getPenalty());
        d.setPhotoCopyExpenses(this.getPhotoCopyExpenses());
        d.setQcrExpense(this.getQcrExpense());
        d.setRecuritmentConsultants(this.getRecuritmentConsultants());
        d.setRocFees(this.getRocFees());
        d.setConveyanceOdc(this.getConveyanceOdc());
        d.setTravellingExpense(this.getTravellingExpense());
        d.setDebitCreditWrittenOff(this.getDebitCreditWrittenOff());
        d.setDifferenceInExchange(this.getDifferenceInExchange());
        d.setServiceChargesPaid(this.getServiceChargesPaid());
        d.setInsuranceVehicle(this.getInsuranceVehicle());
        d.setOthersAMC(this.getOthersAMC());
        d.setOthersMaintenance(this.getOthersMaintenance());
        d.setRnDEngineeringExpenses(this.getRnDEngineeringExpenses());
        d.setByodCharges(this.getByodCharges());
        d.setCarLease(this.getCarLease());
        d.setDriverSalary(this.getDriverSalary());
        d.setGratuity(this.getGratuity());
        d.setInsurnaceAccidental(this.getInsurnaceAccidental());
        d.setInsurnaceMedical(this.getInsurnaceMedical());
        d.setSupportsOpsTurnover(this.getSupportsOpsTurnover());
        d.setEmployeeFacilitationExpenses(this.getEmployeeFacilitationExpenses());
        d.setTelephoneSR(this.getTelephoneSR());
        d.setVehicleRunningAndMaintSR(this.getVehicleRunningAndMaintSR());
        d.setEmployeeStockOptionExpense(this.getEmployeeStockOptionExpense());
        d.setEmployerContributionLWF(this.getEmployerContributionLWF());
        d.setEsicEmployerCont(this.getEsicEmployerCont());
        d.setLeaveTravelReimbursement(this.getLeaveTravelReimbursement());
        d.setPfAdministrationCharges(this.getPfAdministrationCharges());
        d.setPfEmployerCont(this.getPfEmployerCont());
        d.setQuarterlyIncentive(this.getQuarterlyIncentive());
//		d.setSupportAudit(this.getSupportAudit());
        d.setSupportCCC(this.getSupportCCC());
        d.setSupportIT(this.getSupportIT());
//		d.setSupportMaintenance(this.getSupportMaintenance());
        d.setSupportCommWH(this.getSupportCommWH());
        d.setCorporateMarketingPhotography(this.getCorporateMarketingPhotography());
        d.setCapitalImprovementExpenses(this.getCapitalImprovementExpenses());
        d.setLeaseHoldImprovements(this.getLeaseHoldImprovements());
        d.setFixedAssetsEquipment(this.getFixedAssetsEquipment());
        d.setFixedAssetFurniture(this.getFixedAssetFurniture());
        d.setFixedAssetsIT(this.getFixedAssetsIT());
        d.setFixedAssetsKitchenEquipment(this.getFixedAssetsKitchenEquipment());
        d.setFixedAssetsOfficeEquipment(this.getFixedAssetsOfficeEquipment());
        d.setFixedAssetsVehicle(this.getFixedAssetsVehicle());
        d.setFixedAssetsVehicle(this.getFixedAssetsVehicle());
        d.setFixedAssetsOthersSubCategory(this.getFixedAssetsOthersSubCategory());
        d.setMarketingLaunch(this.getMarketingLaunch());
        d.setPreOpeningConsumable(this.getPreOpeningConsumable());
        d.setPreOpeningOthers(this.getPreOpeningOthers());
        d.setPreOpeningRent(this.getPreOpeningRent());
        d.setPreOpeningSalary(this.getPreOpeningSalary());
        d.setBankCharges(this.getBankCharges());
        d.setInterestOnLoan(this.getInterestOnLoan());
        d.setIntrestOnTDSorGST(this.getIntrestOnTDSorGST());
        d.setInterestOnFDR(this.getInterestOnFDR());
        d.setProfitSaleMutualFunds(this.getProfitSaleMutualFunds());
        d.setInterestIncomeTaxRefund(this.getInterestIncomeTaxRefund());
        d.setMiscIncome(this.getMiscIncome());
        d.setDiscountReceived(this.getDiscountReceived());
        d.setInteriorDesigningCharge(this.getInteriorDesigningCharge());
        d.setScrape(this.getScrape());
        d.setServiceCharges(this.getServiceCharges());
        d.setServiceChargesFICO(this.getServiceChargesFICO());

        d.setBusinessPromotion(this.getBusinessPromotion());
        d.setBusinessPromotionSR(this.getBusinessPromotionSR());
        d.setRoundedOff(this.getRoundedOff());
//		d.setShortAndExcess(this.getShortAndExcess());
        d.setOdcRental(this.getOdcRental());
        d.setRoAMC(this.getRoAMC());
        d.setInsuranceAssets(this.getInsuranceAssets());
        d.setInsuranceCGL(this.getInsuranceCGL());
        d.setInsuranceDnO(this.getInsuranceDnO());
        d.setFuelChargesCafe(this.getFuelChargesCafe());
        d.setCogsOthers(this.getCogsOthers());
        d.setFixedAssetsDepreciation(this.getFixedAssetsDepreciation());
        d.setFixedAssetsDamage(this.getFixedAssetsDamage());
        d.setFixedAssetsLost(this.getFixedAssetsLost());

        d.setVehicleRegularMaintenanceHq(this.getVehicleRegularMaintenanceHq());
        d.setBuildingMaintenanceHq(this.getBuildingMaintenanceHq());
        d.setComputerItMaintenanceHq(this.getComputerItMaintenanceHq());
        d.setEquipmentMaintenanceHq(this.getEquipmentMaintenanceHq());
        d.setMarketingNpiHq(this.getMarketingNpiHq());
        d.setLicenseExpenses(this.getLicenseExpenses());
        d.setCorporateMarketingAtlRadio(this.getCorporateMarketingAtlRadio());
        d.setCorporateMarketingAtlTv(this.getCorporateMarketingAtlTv());
        d.setCorporateMarketingAtlPrintAd(this.getCorporateMarketingAtlPrintAd());
        d.setCorporateMarketingAtlCinema(this.getCorporateMarketingAtlCinema());
        d.setCorporateMarketingAtlDigital(this.getCorporateMarketingAtlDigital());
        d.setLogisticInterstateColdVehicle(this.getLogisticInterstateColdVehicle());
        d.setLogisticInterstateNonColdVehicle(this.getLogisticInterstateNonColdVehicle());
        d.setLogisticInterstateAir(this.getLogisticInterstateAir());
        d.setLogisticInterstateRoad(this.getLogisticInterstateRoad());
        d.setLogisticInterstateTrain(this.getLogisticInterstateTrain());
        d.setAirConditionerAmc(this.getAirConditionerAmc());
        d.setCorporateMarketingSms(this.getCorporateMarketingSms());
        d.setOtherServiceCharges(this.getOtherServiceCharges());

        d.setCostAdjustment(this.getCostAdjustment());
        d.setRevenueAdjustment(this.getRevenueAdjustment());

        d.setSecurityDepositProperty(this.getSecurityDepositProperty());
        d.setSecurityDepositMVAT(this.getSecurityDepositMVAT());
        d.setSecurityDepositElectricity(this.getSecurityDepositElectricity());
        d.setMarketingDiscountEcom(this.getMarketingDiscountEcom());
        d.setShippingCharges(this.getShippingCharges());
        d.setOtherTransactionCharges(this.getOtherTransactionCharges());
        d.setDiscountDealerMargin(this.getDiscountDealerMargin());
        d.setPerformanceMarketingService(this.getPerformanceMarketingService());
        d.setInsuranceMarine(this.getInsuranceMarine());
        d.setShareStampingCharges(this.getShareStampingCharges());
        d.setOtherChargesEcom(this.getOtherChargesEcom());
        d.setComissionChannelPartnerFixed(this.getComissionChannelPartnerFixed());
        d.setCogsTradingGoods(this.getCogsTradingGoods());
        d.setRoyaltyFees(this.getRoyaltyFees());
        d.setFreightCharges(this.getFreightCharges());
        d.setCommissionChangeCafe(this.getCommissionChangeCafe());
        d.setFuelChargesCafe(this.getFuelChargesCafe());
        d.setPhotoCopyExpensesCafe(this.getPhotoCopyExpensesCafe());
        d.setPrintingAndStationaryCafe(this.getPrintingAndStationaryCafe());
        d.setStaffWelfareExpensesCafe(this.getStaffWelfareExpensesCafe());
        d.setCleaningChargesCafe(this.getCleaningChargesCafe());
        d.setBusinessPromotionCafe(this.getBusinessPromotionCafe());
        d.setCourierChargesCafe(this.getCourierChargesCafe());
        d.setNewsPaperCafe(this.getNewsPaperCafe());

        return d;
    }

    @Column(name = "GROSS_COST", nullable = true)
    public BigDecimal getGrossCost() {
        return grossCost;
    }

    public void setGrossCost(BigDecimal grossCost) {
        this.grossCost = grossCost;
    }

    @Column(name = "GROSS_PROFIT", nullable = true)
    public BigDecimal getGrossProfit() {
        return grossProfit;
    }

    public void setGrossProfit(BigDecimal grossProfit) {
        this.grossProfit = grossProfit;
    }

    @Column(name = "GROSS_PROFIT_PERCENTAGE", nullable = true)
    public BigDecimal getGrossProfitPercentage() {
        return grossProfitPercentage;
    }

    public void setGrossProfitPercentage(BigDecimal grossProfitPercentage) {
        this.grossProfitPercentage = grossProfitPercentage;
    }

    @Column(name = "TOTAL_COST", nullable = true)
    public BigDecimal getTotalCost() {
        return totalCost;
    }

    public void setTotalCost(BigDecimal totalCost) {
        this.totalCost = totalCost;
    }

    @Column(name = "NET_PROFIT", nullable = true)
    public BigDecimal getNetProfit() {
        return netProfit;
    }

    public void setNetProfit(BigDecimal netProfit) {
        this.netProfit = netProfit;
    }

    @Column(name = "NET_PROFIT_PERCENTAGE", nullable = true)
    public BigDecimal getNetProfitPercentage() {
        return netProfitPercentage;
    }

    public void setNetProfitPercentage(BigDecimal netProfitPercentage) {
        this.netProfitPercentage = netProfitPercentage;
    }

    @Column(name = "NET_PROFIT_WITH_DEPRECIATION", nullable = true)
    public BigDecimal getNetProfitWithDepreciation() {
        return netProfitWithDepreciation;
    }

    public void setNetProfitWithDepreciation(BigDecimal netProfitWithDepreciation) {
        this.netProfitWithDepreciation = netProfitWithDepreciation;
    }

    @Column(name = "NET_PROFIT_PERCENTAGE_WITH_DEPRECIATION", nullable = true)
    public BigDecimal getNetProfitPercentageWithDepreciation() {
        return netProfitPercentageWithDepreciation;
    }

    public void setNetProfitPercentageWithDepreciation(BigDecimal netProfitPercentageWithDepreciation) {
        this.netProfitPercentageWithDepreciation = netProfitPercentageWithDepreciation;
    }

    @Column(name = "NET_SALES", nullable = true)
    public BigDecimal getNetSales() {
        return netSales;
    }

    public void setNetSales(BigDecimal netSales) {
        this.netSales = netSales;
    }


    @Column(name = "NET_REVENUE", nullable = true)
    public BigDecimal getNetRevenue() {
        return netRevenue;
    }

    public void setNetRevenue(BigDecimal netSales) {
        this.netRevenue = netSales;
    }

    @Column(name = "DELIVERY_GIFT_CARD_SALES", nullable = true)
    public BigDecimal getDeliveryGiftCardSale() {
        return deliveryGiftCardSale;
    }

    public void setDeliveryGiftCardSale(BigDecimal deliveryGiftCardSale) {
        this.deliveryGiftCardSale = deliveryGiftCardSale;
    }

    @Column(name = "DELIVERY_GIFT_CARD_REDEMPTION", nullable = true)
    public BigDecimal getDeliveryGiftCardRedemption() {
        return deliveryGiftCardRedemption;
    }

    public void setDeliveryGiftCardRedemption(BigDecimal deliveryGiftCardRedemption) {
        this.deliveryGiftCardRedemption = deliveryGiftCardRedemption;
    }

    @Column(name = "DELIVERY_GIFT_CARD_NET_SALES", nullable = true)
    public BigDecimal getDeliveryGiftCardNetSale() {
        return deliveryGiftCardNetSale;
    }

    public void setDeliveryGiftCardNetSale(BigDecimal deliveryGiftCardNetSale) {
        this.deliveryGiftCardNetSale = deliveryGiftCardNetSale;
    }

    @Column(name = "DELIVERY_NET_SALES", nullable = true)
    public BigDecimal getDeliveryNetSales() {
        return deliveryNetSales;
    }

    public void setDeliveryNetSales(BigDecimal deliveryNetSales) {
        this.deliveryNetSales = deliveryNetSales;
    }

    @Column(name = "DINE_IN_GIFT_CARD_SALES", nullable = true)
    public BigDecimal getDineInGiftCardSale() {
        return dineInGiftCardSale;
    }

    public void setDineInGiftCardSale(BigDecimal dineInGiftCardSale) {
        this.dineInGiftCardSale = dineInGiftCardSale;
    }

    @Column(name = "DINE_IN_GIFT_CARD_REDEMPTION", nullable = true)
    public BigDecimal getDineInGiftCardRedemption() {
        return dineInGiftCardRedemption;
    }

    public void setDineInGiftCardRedemption(BigDecimal dineInGiftCardRedemption) {
        this.dineInGiftCardRedemption = dineInGiftCardRedemption;
    }

    @Column(name = "DINE_IN_GIFT_CARD_NET_SALES", nullable = true)
    public BigDecimal getDineInGiftCardNetSale() {
        return dineInGiftCardNetSale;
    }

    public void setDineInGiftCardNetSale(BigDecimal dineInGiftCardNetSale) {
        this.dineInGiftCardNetSale = dineInGiftCardNetSale;
    }

    @Column(name = "DINE_IN_NET_SALES", nullable = true)
    public BigDecimal getDineInNetSales() {
        return dineInNetSales;
    }

    public void setDineInNetSales(BigDecimal dineInNetSales) {
        this.dineInNetSales = dineInNetSales;
    }

    public BigDecimal revenue() {
        return this.getRevenue();
    }

    @Column(name = "VARIANCE_PCC", nullable = true)
    public BigDecimal getVariancePCC() {
        return variancePCC;
    }

    public void setVariancePCC(BigDecimal variancePCC) {
        this.variancePCC = variancePCC;
    }

    @Column(name = "VARIANCE_PCC_TAX", nullable = true)
    public BigDecimal getVariancePCCTax() {
        return variancePCCTax;
    }

    public void setVariancePCCTax(BigDecimal variancePCCTax) {
        this.variancePCCTax = variancePCCTax;
    }

    @Column(name = "VARIANCE_YC", nullable = true)
    public BigDecimal getVarianceYC() {
        return varianceYC;
    }

    public void setVarianceYC(BigDecimal varianceYC) {
        this.varianceYC = varianceYC;
    }

    @Column(name = "VARIANCE_YC_TAX", nullable = true)
    public BigDecimal getVarianceYCTax() {
        return varianceYCTax;
    }

    public void setVarianceYCTax(BigDecimal varianceYCTax) {
        this.varianceYCTax = varianceYCTax;
    }

    @Column(name = "CONVEYANCE_MARKETING", nullable = true)
    public BigDecimal getConveyanceMarketing() {
        return conveyanceMarketing;
    }

    public void setConveyanceMarketing(BigDecimal conveyanceMarketing) {
        this.conveyanceMarketing = conveyanceMarketing;
    }

    @Column(name = "CONVEYANCE_OPERATION", nullable = true)
    public BigDecimal getConveyanceOperations() {
        return conveyanceOperations;
    }

    public void setConveyanceOperations(BigDecimal conveyanceOperations) {
        this.conveyanceOperations = conveyanceOperations;
    }

    @Column(name = "CONVEYANCE_OTHERS", nullable = true)
    public BigDecimal getConveyanceOthers() {
        return conveyanceOthers;
    }

    public void setConveyanceOthers(BigDecimal conveyanceOthers) {
        this.conveyanceOthers = conveyanceOthers;
    }

    @Column(name = "AUDIT_FEE", nullable = true)
    public BigDecimal getAuditFee() {
        return auditFee;
    }

    public void setAuditFee(BigDecimal auditFee) {
        this.auditFee = auditFee;
    }

    @Column(name = "AUDIT_FEE_OUT_OF_POCKET", nullable = true)
    public BigDecimal getAuditFeeOutOfPocket() {
        return auditFeeOutOfPocket;
    }

    public void setAuditFeeOutOfPocket(BigDecimal auditFeeOutOfPocket) {
        this.auditFeeOutOfPocket = auditFeeOutOfPocket;
    }

//	@Column(name = "BAD_DEBTS_WRITTEN_OFF", nullable = true)
//	public BigDecimal getBadDebtsWrittenOff() {
//		return badDebtsWrittenOff;
//	}
//
//	public void setBadDebtsWrittenOff(BigDecimal badDebtsWrittenOff) {
//		this.badDebtsWrittenOff = badDebtsWrittenOff;
//	}

    @Column(name = "BROKERAGE", nullable = true)
    public BigDecimal getBrokerage() {
        return brokerage;
    }

    public void setBrokerage(BigDecimal brokerage) {
        this.brokerage = brokerage;
    }

    @Column(name = "CHARITY_AND_DONATIONS", nullable = true)
    public BigDecimal getCharityAndDonations() {
        return charityAndDonations;
    }

    public void setCharityAndDonations(BigDecimal charityAndDonations) {
        this.charityAndDonations = charityAndDonations;
    }

    @Column(name = "DOMESTIC_TICKETS_AND_HOTELS", nullable = true)
    public BigDecimal getDomesticTicketsAndHotels() {
        return domesticTicketsAndHotels;
    }

    public void setDomesticTicketsAndHotels(BigDecimal domesticTicketsAndHotels) {
        this.domesticTicketsAndHotels = domesticTicketsAndHotels;
    }

    @Column(name = "INTERNATIONAL_TICKETS_AND_HOTELS", nullable = true)
    public BigDecimal getInternationalTicketsAndHotels() {
        return internationalTicketsAndHotels;
    }

    public void setInternationalTicketsAndHotels(BigDecimal internationalTicketsAndHotels) {
        this.internationalTicketsAndHotels = internationalTicketsAndHotels;
    }

    @Column(name = "HOUSEKEEPING_CHARGES", nullable = true)
    public BigDecimal getHouseKeepingCharges() {
        return houseKeepingCharges;
    }

    public void setHouseKeepingCharges(BigDecimal houseKeepingCharges) {
        this.houseKeepingCharges = houseKeepingCharges;
    }

    @Column(name = "LATE_FEE_CHARGES", nullable = true)
    public BigDecimal getLateFeeCharges() {
        return lateFeeCharges;
    }

    public void setLateFeeCharges(BigDecimal lateFeeCharges) {
        this.lateFeeCharges = lateFeeCharges;
    }

    @Column(name = "MARKETING_DATA_ANALYSIS", nullable = true)
    public BigDecimal getMarketingDataAnalysis() {
        return marketingDataAnalysis;
    }

    public void setMarketingDataAnalysis(BigDecimal marketingDataAnalysis) {
        this.marketingDataAnalysis = marketingDataAnalysis;
    }

    @Column(name = "MISCELLANEOUS_EXPENSES", nullable = true)
    public BigDecimal getMiscellaneousExpenses() {
        return miscellaneousExpenses;
    }

    public void setMiscellaneousExpenses(BigDecimal miscellaneousExpenses) {
        this.miscellaneousExpenses = miscellaneousExpenses;
    }

    @Column(name = "PENALTY", nullable = true)
    public BigDecimal getPenalty() {
        return penalty;
    }

    public void setPenalty(BigDecimal penalty) {
        this.penalty = penalty;
    }

    @Column(name = "PHOTO_COPY_EXPENSES", nullable = true)
    public BigDecimal getPhotoCopyExpenses() {
        return photoCopyExpenses;
    }

    public void setPhotoCopyExpenses(BigDecimal photoCopyExpenses) {
        this.photoCopyExpenses = photoCopyExpenses;
    }

    @Column(name = "QCR_EXPENSE", nullable = true)
    public BigDecimal getQcrExpense() {
        return qcrExpense;
    }

    public void setQcrExpense(BigDecimal qcrExpense) {
        this.qcrExpense = qcrExpense;
    }

    @Column(name = "RECRUITMENT_CONSULTANTS", nullable = true)
    public BigDecimal getRecuritmentConsultants() {
        return recuritmentConsultants;
    }

    public void setRecuritmentConsultants(BigDecimal recuritmentConsultants) {
        this.recuritmentConsultants = recuritmentConsultants;
    }

    @Column(name = "ROC_FEES", nullable = true)
    public BigDecimal getRocFees() {
        return rocFees;
    }

    public void setRocFees(BigDecimal rocFees) {
        this.rocFees = rocFees;
    }

    @Column(name = "CONVEYANCE_ODC", nullable = true)//TRAVELLING_EXPENSE_ODC
    public BigDecimal getConveyanceOdc() {
        return conveyanceOdc;
    }

    public void setConveyanceOdc(BigDecimal conveyanceOdc) {
        this.conveyanceOdc = conveyanceOdc;
    }

    @Column(name = "TRAVELLING_EXPENSES", nullable = true)
    public BigDecimal getTravellingExpense() {
        return travellingExpense;
    }

    public void setTravellingExpense(BigDecimal travellingExpence) {
        this.travellingExpense = travellingExpence;
    }

    @Column(name = "DEBIT_CREDIT_WRITTEN_OFF", nullable = true)
    public BigDecimal getDebitCreditWrittenOff() {
        return debitCreditWrittenOff;
    }

    public void setDebitCreditWrittenOff(BigDecimal debitCreditWrittenOff) {
        this.debitCreditWrittenOff = debitCreditWrittenOff;
    }

    @Column(name = "DIFFENECE_IN_EXCHANGE", nullable = true)
    public BigDecimal getDifferenceInExchange() {
        return differenceInExchange;
    }

    public void setDifferenceInExchange(BigDecimal differenceInExchange) {
        this.differenceInExchange = differenceInExchange;
    }

    @Column(name = "SERVICE_CHARGE_PAID", nullable = true)
    public BigDecimal getServiceChargesPaid() {
        return serviceChargesPaid;
    }

    public void setServiceChargesPaid(BigDecimal serviceChargesPaid) {
        this.serviceChargesPaid = serviceChargesPaid;
    }

    @Column(name = "INSURANCE_VEHICLE", nullable = true)
    public BigDecimal getInsuranceVehicle() {
        return insuranceVehicle;
    }

    public void setInsuranceVehicle(BigDecimal insuranceVehicle) {
        this.insuranceVehicle = insuranceVehicle;
    }

    @Column(name = "OTHERS_AMC", nullable = true)
    public BigDecimal getOthersAMC() {
        return othersAMC;
    }

    public void setOthersAMC(BigDecimal othersAMC) {
        this.othersAMC = othersAMC;
    }

    @Column(name = "OTHERS_MAINTENANCE", nullable = true)
    public BigDecimal getOthersMaintenance() {
        return othersMaintenance;
    }

    public void setOthersMaintenance(BigDecimal othersMaintenance) {
        this.othersMaintenance = othersMaintenance;
    }

    @Column(name = "RND_ENGINEERING_EXPENSE", nullable = true)
    public BigDecimal getRnDEngineeringExpenses() {
        return rnDEngineeringExpenses;
    }

    public void setRnDEngineeringExpenses(BigDecimal rnDEngineeringExpenses) {
        this.rnDEngineeringExpenses = rnDEngineeringExpenses;
    }

    @Column(name = "BYOD_CHARGES", nullable = true)
    public BigDecimal getByodCharges() {
        return byodCharges;
    }

    public void setByodCharges(BigDecimal byodCharges) {
        this.byodCharges = byodCharges;
    }

    @Column(name = "CAR_LEASE_SR", nullable = true)
    public BigDecimal getCarLease() {
        return carLease;
    }

    public void setCarLease(BigDecimal carLease) {
        this.carLease = carLease;
    }

    @Column(name = "DRIVER_SALARY_SR", nullable = true)
    public BigDecimal getDriverSalary() {
        return driverSalary;
    }

    public void setDriverSalary(BigDecimal driverSalary) {
        this.driverSalary = driverSalary;
    }

    @Column(name = "GRATUITY_EXPENSE", nullable = true)
    public BigDecimal getGratuity() {
        return gratuity;
    }

    public void setGratuity(BigDecimal gratuity) {
        this.gratuity = gratuity;
    }

    @Column(name = "INSURANCE_ACCIDENTAL", nullable = true)
    public BigDecimal getInsurnaceAccidental() {
        return insurnaceAccidental;
    }

    public void setInsurnaceAccidental(BigDecimal insurnaceAccidental) {
        this.insurnaceAccidental = insurnaceAccidental;
    }

    @Column(name = "INSURANCE_MEDICAL", nullable = true)
    public BigDecimal getInsurnaceMedical() {
        return insurnaceMedical;
    }

    public void setInsurnaceMedical(BigDecimal insurnaceMedical) {
        this.insurnaceMedical = insurnaceMedical;
    }

    @Column(name = "SALARY", nullable = true)
    public BigDecimal getSalary() {
        return salary;
    }

    public void setSalary(BigDecimal salary) {
        this.salary = salary;
    }

    @Column(name = "SUPPORT_OPS_TURNOVER", nullable = true)
    public BigDecimal getSupportsOpsTurnover() {
        return supportsOpsTurnover;
    }

    public void setSupportsOpsTurnover(BigDecimal supportsOpsTurnover) {
        this.supportsOpsTurnover = supportsOpsTurnover;
    }

    @Column(name = "EMPLOYEE_FACILITATION_CHARGES", nullable = true)
    public BigDecimal getEmployeeFacilitationExpenses() {
        return employeeFacilitationExpenses;
    }

    public void setEmployeeFacilitationExpenses(BigDecimal employeeFacilitationExpenses) {
        this.employeeFacilitationExpenses = employeeFacilitationExpenses;
    }

    @Column(name = "TELEPHONE_SR", nullable = true)
    public BigDecimal getTelephoneSR() {
        return telephoneSR;
    }

    public void setTelephoneSR(BigDecimal telephoneSR) {
        this.telephoneSR = telephoneSR;
    }

    @Column(name = "VEHICLE_RUNNING_AND_MAINT_SR", nullable = true)
    public BigDecimal getVehicleRunningAndMaintSR() {
        return vehicleRunningAndMaintSR;
    }

    public void setVehicleRunningAndMaintSR(BigDecimal vehicleRunningAndMaintSR) {
        this.vehicleRunningAndMaintSR = vehicleRunningAndMaintSR;
    }

    @Column(name = "EMPLOYEE_STOCK_OPTION_EXPENSE", nullable = true)
    public BigDecimal getEmployeeStockOptionExpense() {
        return employeeStockOptionExpense;
    }

    public void setEmployeeStockOptionExpense(BigDecimal employeeStockOptionExpense) {
        this.employeeStockOptionExpense = employeeStockOptionExpense;
    }

    @Column(name = "EMPLOYER_CONTRIBUTION_LWF", nullable = true)
    public BigDecimal getEmployerContributionLWF() {
        return employerContributionLWF;
    }

    public void setEmployerContributionLWF(BigDecimal employerContributionLWF) {
        this.employerContributionLWF = employerContributionLWF;
    }

    @Column(name = "ESIC_EMPLOYER_CONT", nullable = true)
    public BigDecimal getEsicEmployerCont() {
        return esicEmployerCont;
    }

    public void setEsicEmployerCont(BigDecimal esicEmployerCont) {
        this.esicEmployerCont = esicEmployerCont;
    }

    @Column(name = "LEAVE_TRAVEL_REIMBURSEMENT", nullable = true)
    public BigDecimal getLeaveTravelReimbursement() {
        return leaveTravelReimbursement;
    }

    public void setLeaveTravelReimbursement(BigDecimal leaveTravelReimbursement) {
        this.leaveTravelReimbursement = leaveTravelReimbursement;
    }

    @Column(name = "PF_ADMINISTRATION_CHARGES", nullable = true)
    public BigDecimal getPfAdministrationCharges() {
        return pfAdministrationCharges;
    }

    public void setPfAdministrationCharges(BigDecimal pfAdministrationCharges) {
        this.pfAdministrationCharges = pfAdministrationCharges;
    }

    @Column(name = "PF_EMPLOYER_CONT", nullable = true)
    public BigDecimal getPfEmployerCont() {
        return pfEmployerCont;
    }

    public void setPfEmployerCont(BigDecimal pfEmployerCont) {
        this.pfEmployerCont = pfEmployerCont;
    }

    @Column(name = "QUATERLY_INCENTIVE", nullable = true)
    public BigDecimal getQuarterlyIncentive() {
        return quarterlyIncentive;
    }

    public void setQuarterlyIncentive(BigDecimal quarterlyIncentive) {
        this.quarterlyIncentive = quarterlyIncentive;
    }

    @Column(name = "BUSINESS_PROMOTION_SR", nullable = true)
    public BigDecimal getBusinessPromotionSR() {
        return businessPromotionSR;
    }

    public void setBusinessPromotionSR(BigDecimal businessPromotionSR) {
        this.businessPromotionSR = businessPromotionSR;
    }

//	@Column(name = "SUPPORT_AUDIT", nullable = true)
//	public BigDecimal getSupportAudit() {
//		return supportAudit;
//	}
//
//	public void setSupportAudit(BigDecimal supportAudit) {
//		this.supportAudit = supportAudit;
//	}

    @Column(name = "SUPPORT_CCC", nullable = true)
    public BigDecimal getSupportCCC() {
        return supportCCC;
    }

    public void setSupportCCC(BigDecimal supportCCC) {
        this.supportCCC = supportCCC;
    }

    @Column(name = "SUPPORT_IT", nullable = true)
    public BigDecimal getSupportIT() {
        return supportIT;
    }

    public void setSupportIT(BigDecimal supportIT) {
        this.supportIT = supportIT;
    }

//	@Column(name = "SUPPORT_MAINTENANCE", nullable = true)
//	public BigDecimal getSupportMaintenance() {
//		return supportMaintenance;
//	}
//
//	public void setSupportMaintenance(BigDecimal supportMaintenance) {
//		this.supportMaintenance = supportMaintenance;
//	}

    @Column(name = "SUPPORT_COMM_WH", nullable = true)
    public BigDecimal getSupportCommWH() {
        return supportCommWH;
    }

    public void setSupportCommWH(BigDecimal supportCommWH) {
        this.supportCommWH = supportCommWH;
    }

    @Column(name = "CAPITAL_IMPROVEMENT_EXPENSES", nullable = true)
    public BigDecimal getCapitalImprovementExpenses() {
        return capitalImprovementExpenses;
    }

    public void setCapitalImprovementExpenses(BigDecimal capitalImprovementExpenses) {
        this.capitalImprovementExpenses = capitalImprovementExpenses;
    }

    @Column(name = "LEASE_HOLD_IMPROVEMENTS", nullable = true)
    public BigDecimal getLeaseHoldImprovements() {
        return leaseHoldImprovements;
    }

    public void setLeaseHoldImprovements(BigDecimal leaseHoldImprovements) {
        this.leaseHoldImprovements = leaseHoldImprovements;
    }

    @Column(name = "FIXED_ASSETS_EQUIPMENT_CAFE", nullable = true)
    public BigDecimal getFixedAssetsEquipment() {
        return fixedAssetsEquipment;
    }

    public void setFixedAssetsEquipment(BigDecimal fixedAssetsEquipment) {
        this.fixedAssetsEquipment = fixedAssetsEquipment;
    }

    @Column(name = "FIXED_ASSET_FURNITURE_CAFE", nullable = true)
    public BigDecimal getFixedAssetFurniture() {
        return fixedAssetFurniture;
    }

    public void setFixedAssetFurniture(BigDecimal fixedAssetFurniture) {
        this.fixedAssetFurniture = fixedAssetFurniture;
    }

    @Column(name = "FIXED_ASSETS_IT_CAFE", nullable = true)
    public BigDecimal getFixedAssetsIT() {
        return fixedAssetsIT;
    }

    public void setFixedAssetsIT(BigDecimal fixedAssetsIT) {
        this.fixedAssetsIT = fixedAssetsIT;
    }

    @Column(name = "FIXED_ASSETS_KITCHEN_EQUIPMENT_CAFE", nullable = true)
    public BigDecimal getFixedAssetsKitchenEquipment() {
        return fixedAssetsKitchenEquipment;
    }

    public void setFixedAssetsKitchenEquipment(BigDecimal fixedAssetsKitchenEquipment) {
        this.fixedAssetsKitchenEquipment = fixedAssetsKitchenEquipment;
    }

    @Column(name = "FIXED_ASSETS_OFFICE_EQUIPMENT_CAFE", nullable = true)
    public BigDecimal getFixedAssetsOfficeEquipment() {
        return fixedAssetsOfficeEquipment;
    }

    public void setFixedAssetsOfficeEquipment(BigDecimal fixedAssetsOfficeEquipment) {
        this.fixedAssetsOfficeEquipment = fixedAssetsOfficeEquipment;
    }

    @Column(name = "FIXED_ASSETS_VEHICLE", nullable = true)
    public BigDecimal getFixedAssetsVehicle() {
        return fixedAssetsVehicle;
    }

    public void setFixedAssetsVehicle(BigDecimal fixedAssetsVehicle) {
        this.fixedAssetsVehicle = fixedAssetsVehicle;
    }

    @Column(name = "FIXED_ASSETS_OTHERS_SUB_CATEGORY", nullable = true)
    public BigDecimal getFixedAssetsOthersSubCategory() {
        return fixedAssetsOthersSubCategory;
    }

    public void setFixedAssetsOthersSubCategory(BigDecimal fixedAssetsOthersSubCategory) {
        this.fixedAssetsOthersSubCategory = fixedAssetsOthersSubCategory;
    }

    @Column(name = "MARKETING_LAUNCH", nullable = true)
    public BigDecimal getMarketingLaunch() {
        return marketingLaunch;
    }

    public void setMarketingLaunch(BigDecimal marketingLaunch) {
        this.marketingLaunch = marketingLaunch;
    }

    @Column(name = "PRE_OPENING_CONSUMABLE", nullable = true)
    public BigDecimal getPreOpeningConsumable() {
        return preOpeningConsumable;
    }

    public void setPreOpeningConsumable(BigDecimal preOpeningConsumable) {
        this.preOpeningConsumable = preOpeningConsumable;
    }

    @Column(name = "PRE_OPENING_OTHERS", nullable = true)
    public BigDecimal getPreOpeningOthers() {
        return preOpeningOthers;
    }

    public void setPreOpeningOthers(BigDecimal preOpeningOthers) {
        this.preOpeningOthers = preOpeningOthers;
    }

    @Column(name = "PRE_OPENING_RENT", nullable = true)
    public BigDecimal getPreOpeningRent() {
        return preOpeningRent;
    }

    public void setPreOpeningRent(BigDecimal preOpeningRent) {
        this.preOpeningRent = preOpeningRent;
    }

    @Column(name = "PRE_OPEINING_SALARY", nullable = true)
    public BigDecimal getPreOpeningSalary() {
        return preOpeningSalary;
    }

    public void setPreOpeningSalary(BigDecimal preOpeningSalary) {
        this.preOpeningSalary = preOpeningSalary;
    }

    @Column(name = "BANK_CHARGES", nullable = true)
    public BigDecimal getBankCharges() {
        return bankCharges;
    }

    public void setBankCharges(BigDecimal bankCharges) {
        this.bankCharges = bankCharges;
    }

    @Column(name = "INTREST_ON_LOAN", nullable = true)
    public BigDecimal getInterestOnLoan() {
        return interestOnLoan;
    }

    public void setInterestOnLoan(BigDecimal interestOnLoan) {
        this.interestOnLoan = interestOnLoan;
    }

    @Column(name = "INTREST_ON_TDS_OR_GST", nullable = true)
    public BigDecimal getIntrestOnTDSorGST() {
        return intrestOnTDSorGST;
    }

    public void setIntrestOnTDSorGST(BigDecimal intrestOnTDSorGST) {
        this.intrestOnTDSorGST = intrestOnTDSorGST;
    }

    @Column(name = "INTEREST_ON_FDR", nullable = true)
    public BigDecimal getInterestOnFDR() {
        return interestOnFDR;
    }

    public void setInterestOnFDR(BigDecimal interestOnFDR) {
        this.interestOnFDR = interestOnFDR;
    }

    @Column(name = "PROFIT_OR_LOSS_SALE_MUTUTAL_FUND", nullable = true)
    public BigDecimal getProfitSaleMutualFunds() {
        return profitSaleMutualFunds;
    }

    public void setProfitSaleMutualFunds(BigDecimal profitSaleMutualFunds) {
        this.profitSaleMutualFunds = profitSaleMutualFunds;
    }

    @Column(name = "INTEREST_INCOME_TAX_REFUND", nullable = true)
    public BigDecimal getInterestIncomeTaxRefund() {
        return interestIncomeTaxRefund;
    }

    public void setInterestIncomeTaxRefund(BigDecimal interestIncomeTaxRefund) {
        this.interestIncomeTaxRefund = interestIncomeTaxRefund;
    }

    @Column(name = "MISC_INCOME", nullable = true)
    public BigDecimal getMiscIncome() {
        return miscIncome;
    }

    public void setMiscIncome(BigDecimal miscIncome) {
        this.miscIncome = miscIncome;
    }

    @Column(name = "DISCOUNT_RECEIVED", nullable = true)
    public BigDecimal getDiscountReceived() {
        return discountReceived;
    }

    public void setDiscountReceived(BigDecimal discountReceived) {
        this.discountReceived = discountReceived;
    }

    @Column(name = "INTERIOR_DESIGNING_CHARGE", nullable = true)
    public BigDecimal getInteriorDesigningCharge() {
        return interiorDesigningCharge;
    }

    public void setInteriorDesigningCharge(BigDecimal interiorDesigningCharge) {
        this.interiorDesigningCharge = interiorDesigningCharge;
    }

    @Column(name = "SCRAPE_CHARGES", nullable = true)
    public BigDecimal getScrape() {
        return scrape;
    }

    public void setScrape(BigDecimal scrape) {
        this.scrape = scrape;
    }

    @Column(name = "SERVICE_CHARGE", nullable = true)
    public BigDecimal getServiceCharges() {
        return serviceCharges;
    }

    public void setServiceCharges(BigDecimal serviceCharges) {
        this.serviceCharges = serviceCharges;
    }

    @Column(name = "SERVICE_CHARGE_FICO", nullable = true)
    public BigDecimal getServiceChargesFICO() {
        return serviceChargesFICO;
    }

    public void setServiceChargesFICO(BigDecimal serviceChargesFICO) {
        this.serviceChargesFICO = serviceChargesFICO;
    }

    @Column(name = "INSURANCE_ASSETS", nullable = true)
    public BigDecimal getInsuranceAssets() {
        return insuranceAssets;
    }

    public void setInsuranceAssets(BigDecimal insuranceAssets) {
        this.insuranceAssets = insuranceAssets;
    }

    @Column(name = "INSURANCE_CGL", nullable = true)
    public BigDecimal getInsuranceCGL() {
        return insuranceCGL;
    }

    public void setInsuranceCGL(BigDecimal insuranceCGL) {
        this.insuranceCGL = insuranceCGL;
    }

    @Column(name = "INSURANCE_D_AND_O", nullable = true)
    public BigDecimal getInsuranceDnO() {
        return insuranceDnO;
    }

    public void setInsuranceDnO(BigDecimal insuranceDnO) {
        this.insuranceDnO = insuranceDnO;
    }

    @Column(name = "ODC_RENTAL", nullable = true)
    public BigDecimal getOdcRental() {
        return odcRental;
    }

    public void setOdcRental(BigDecimal odcRental) {
        this.odcRental = odcRental;
    }

    @Column(name = "ROUNDED_OFF", nullable = true)
    public BigDecimal getRoundedOff() {
        return roundedOff;
    }

    public void setRoundedOff(BigDecimal roundedOff) {
        this.roundedOff = roundedOff;
    }

//	@Column(name = "SHORT_AND_EXCESS", nullable = true)
//	public BigDecimal getShortAndExcess() {
//		return shortAndExcess;
//	}
//
//	public void setShortAndExcess(BigDecimal shortAndExcess) {
//		this.shortAndExcess = shortAndExcess;
//	}

    @Column(name = "COGS_OTHERS", nullable = true)
    public BigDecimal getCogsOthers() {
        return cogsOthers;
    }

    public void setCogsOthers(BigDecimal cogsOthers) {
        this.cogsOthers = cogsOthers;
    }

    @Column(name = "FIXED_ASSETS_DEPRECIATION", nullable = true)
    public BigDecimal getFixedAssetsDepreciation() {
        return fixedAssetsDepreciation;
    }

    public void setFixedAssetsDepreciation(BigDecimal fixedAssetsDepreciation) {
        this.fixedAssetsDepreciation = fixedAssetsDepreciation;
    }

    @Column(name = "FIXED_ASSETS_LOST", nullable = true)
    public BigDecimal getFixedAssetsLost() {
        return fixedAssetsLost;
    }

    public void setFixedAssetsLost(BigDecimal fixedAssetsLost) {
        this.fixedAssetsLost = fixedAssetsLost;
    }

    @Column(name = "FIXED_ASSETS_DAMAGE", nullable = true)
    public BigDecimal getFixedAssetsDamage() {
        return fixedAssetsDamage;
    }

    public void setFixedAssetsDamage(BigDecimal fixedAssetsDamage) {
        this.fixedAssetsDamage = fixedAssetsDamage;
    }

    public BigDecimal grossCostValue() {
        BigDecimal value = get(this.dineInCogs).add(get(this.deliveryCogs)).add(get(this.trainingCogs))
                .add(get(this.unsatifiedCustomerCost)).add(get(this.ppeCost)).add(get(this.wastageOther)).add(get(this.expiryWastage))
                .add(get(this.employeeMealCogs)).add(get(this.consumable)).add(get(this.consumableOthers)).add(get(this.consumableDisposable))
                .add(get(this.dineInCogsTax)).add(get(this.deliveryCogsTax)).add(get(this.trainingCogsTax))
                .add(get(this.unsatifiedCustomerCostTax)).add(get(this.ppeCostTax)).add(get(this.wastageOtherTax)).add(get(this.expiryWastageTax))
                .add(get(this.employeeMealCogsTax)).add(get(this.consumableTax)).add(get(this.consumableOthersTax))
                .add(get(this.stockVariance)).add(get(this.stockVarianceTax)).add(get(this.logisticCharges));

        return value;
    }

    public BigDecimal totalCostValue() {
        BigDecimal value = get(this.dineInCogs).add(get(this.deliveryCogs)).add(get(this.employeeMealCogs))
                .add(get(this.trainingCogs)).add(get(this.unsatifiedCustomerCost)).add(get(this.ppeCost)).add(get(this.expiryWastage))
                .add(get(this.wastageOther)).add(get(this.consumable)).add(get(this.consumableDisposable))
                .add(get(this.salaryIncentive)).add(get(this.securityGuardCharges)).add(get(this.salesIncentive))
                .add(get(this.depreciationOfBike)).add(get(this.fuelCharges)).add(get(this.vehicleRegularMaintenance))
                .add(get(this.insuranceVehicle)).add(get(this.parkingCharges)).add(get(this.marketingAndSampling))
                .add(get(this.consumableMarketing)).add(get(this.logisticCharges)).add(get(this.energyElectricity))
                .add(get(this.energyDGRunning)).add(get(this.waterCharges)).add(get(this.communicationInternet))
                .add(get(this.communicationTelephone)).add(get(this.communicationILL))
                .add(get(this.creditCardTransactionCharges)).add(get(this.voucherTransactionCharges))
                .add(get(this.walletsTransactionCharges)).add(get(this.commissionChannelPartners)).add(get(this.cancellationChargesChannelPartners))
                .add(get(this.commissionChange)).add(get(this.payrollProcessingFee)).add(get(this.legalCharges))
                .add(get(this.professionalCharges)).add(get(this.newsPaper)).add(get(this.staffWelfareExpenses))
                .add(get(this.courierCharges)).add(get(this.printingAndStationary)).add(get(this.businessPromotion))
                .add(get(this.openingLicencesFees)).add(get(this.registrationCharges)).add(get(this.stampDutyCharges))
                .add(get(this.designingFees)).add(get(this.buildingMaintenance)).add(get(this.cleaningCharges))
                .add(get(this.computerMaintenance)).add(get(this.pestControlCharges))
                .add(get(this.equipmentMaintenance)).add(get(this.prontoAMC)).add(get(this.dgRental))
                .add(get(this.edcRental)).add(get(this.systemRental)).add(get(this.roRental)).add(get(this.propertyTax))
                .add(get(this.propertyFixRent)).add(get(this.revenueShare)).add(get(this.fixCAM))
                .add(get(this.chillingCharges)).add(get(this.marketingCharges)).add(get(this.pettyCashRentals))
                .add(get(this.musicRentals)).add(get(this.internetPartnerRental)).add(get(this.supportOpsManagement))
                .add(get(this.technologyPlatformCharges)).add(get(this.technologyTraining))
                .add(get(this.technologyOthers)).add(get(this.technologyVariable))
                .add(get(this.corporateMarketingDigital)).add(get(this.corporateMarketingAdvOffline))
                .add(get(this.corporateMarketingOutdoor))
                .add(get(this.corporateMarketingAgencyFees)).add(get(this.conveyanceMarketing))
                .add(get(this.conveyanceOperations)).add(get(this.conveyanceOthers)).add(get(this.auditFee))
                .add(get(this.auditFeeOutOfPocket)).add(get(this.brokerage))
                .add(get(this.charityAndDonations)).add(get(this.domesticTicketsAndHotels))
                .add(get(this.internationalTicketsAndHotels)).add(get(this.houseKeepingCharges))
                .add(get(this.lateFeeCharges)).add(get(this.marketingDataAnalysis)).add(get(this.miscellaneousExpenses))
                .add(get(this.penalty)).add(get(this.photoCopyExpenses)).add(get(this.qcrExpense))
                .add(get(this.recuritmentConsultants)).add(get(this.rocFees)).add(get(this.conveyanceOdc))
                .add(get(this.travellingExpense)).add(get(this.debitCreditWrittenOff))
                .add(get(this.differenceInExchange)).add(get(this.serviceChargesPaid)).add(get(this.insuranceVehicle))
                .add(get(this.othersAMC)).add(get(this.othersMaintenance)).add(get(this.rnDEngineeringExpenses))
                .add(get(this.byodCharges)).add(get(this.carLease)).add(get(this.driverSalary)).add(get(this.gratuity))
                .add(get(this.insurnaceAccidental)).add(get(this.insurnaceMedical)).add(get(this.supportsOpsTurnover))
                .add(get(this.employeeFacilitationExpenses)).add(get(this.telephoneSR))
                .add(get(this.vehicleRunningAndMaintSR)).add(get(this.employeeStockOptionExpense))
                .add(get(this.employerContributionLWF)).add(get(this.esicEmployerCont))
                .add(get(this.leaveTravelReimbursement)).add(get(this.pfAdministrationCharges))
                .add(get(this.pfEmployerCont)).add(get(this.quarterlyIncentive))
                .add(get(this.supportCCC)).add(get(this.supportIT))
                .add(get(this.supportCommWH)).add(get(this.corporateMarketingPhotography)).add(get(this.stockVariance))
                .add(get(this.marketingNPI)).add(get(this.deliveryChargesVariable)).add(get(this.consumableOthers))
                .add(get(this.dineInCogsTax)).add(get(this.deliveryCogsTax)).add(get(this.employeeMealCogsTax))
                .add(get(this.unsatifiedCustomerCostTax)).add(get(this.ppeCostTax)).add(get(this.expiryWastageTax)).add(get(this.wastageOtherTax))
                .add(get(this.consumableTax)).add(get(this.consumableOthersTax)).add(get(this.fixedAssetsTax))
                .add(get(this.marketingAndSamplingTax)).add(get(this.trainingCogsTax)).add(get(this.stockVarianceTax))
                .add(get(this.consumableMarketingTax)).add(get(this.insuranceAssets)).add(get(this.insuranceCGL))
                .add(get(this.insuranceDnO)).add(get(this.roAMC)).add(get(this.odcRental))
                .add(get(this.roundedOff)).add(get(this.businessPromotionSR)).add(get(this.salary)).add(this.fixedParkingCharges)
                .add(get(this.cogsLogistics)).add(get(this.preOpeningCamEleWater)).add(get(this.preOpeningRegistrationCharges))
                .add(get(this.preOpeningStampDutyCharges)).add(get(this.preOpeningConsumableTax)).add(get(this.support))
                .add(get(this.corporateMarketingChannelPartner)).add(get(this.preOpeningEleWater)).add(get(this.preOpeningCam))
                .add(get(this.interestOnFixedDepositFICO)).add(get(this.expenseOthers)).add(get(this.cogsOthers)).add(get(this.fuelChargesCafe))
                .add(get(this.vehicleRegularMaintenanceHq)).add(get(this.buildingMaintenanceHq)).add(get(this.computerItMaintenanceHq))
                .add(get(this.equipmentMaintenanceHq)).add(get(this.marketingNpiHq)).add(get(this.licenseExpenses))
                .add(get(this.corporateMarketingAtlRadio)).add(get(this.corporateMarketingAtlTv)).add(get(this.corporateMarketingAtlPrintAd))
                .add(get(this.corporateMarketingAtlCinema)).add(get(this.corporateMarketingAtlDigital)).add(get(this.logisticInterstateColdVehicle))
                .add(get(this.logisticInterstateNonColdVehicle)).add(get(this.logisticInterstateAir)).add(get(this.logisticInterstateRoad))
                .add(get(this.logisticInterstateTrain)).add(get(this.airConditionerAmc)).add(get(this.corporateMarketingSms))
                .add(get(this.otherServiceCharges));
        //.add(get(this.fixedAssets)).add(get(this.corporateMarketingAdvOnline)).add(get(this.badDebtsWrittenOff)).add(get(this.supportAudit))
//		.add(get(this.supportMaintenance)).add(get(this.shortAndExcess))

        return value;

    }

    private BigDecimal get(BigDecimal d) {
        return d == null ? BigDecimal.ZERO : d;
    }


    /*
	Consumable taxes
	 */
    @Column(name = "CONSUMABLE_UTILITY_TAX", nullable = true)
    public BigDecimal getConsumableUtilityTax() {
        return consumableUtilityTax;
    }

    public void setConsumableUtilityTax(BigDecimal consumableUtilityTax) {
        this.consumableUtilityTax = consumableUtilityTax;
    }

    @Column(name = "CONSUMABLE_STATIONARY_TAX", nullable = true)
    public BigDecimal getConsumableStationaryTax() {
        return consumableStationaryTax;
    }

    public void setConsumableStationaryTax(BigDecimal consumableStationaryTax) {
        this.consumableStationaryTax = consumableStationaryTax;
    }

    @Column(name = "CONSUMABLE_UNIFORM_TAX", nullable = true)
    public BigDecimal getConsumableUniformTax() {
        return consumableUniformTax;
    }

    public void setConsumableUniformTax(BigDecimal consumableUniformTax) {
        this.consumableUniformTax = consumableUniformTax;
    }

    @Column(name = "CONSUMABLE_EQUIPMENT_TAX", nullable = true)
    public BigDecimal getConsumableEquipmentTax() {
        return consumableEquipmentTax;
    }

    public void setConsumableEquipmentTax(BigDecimal consumableEquipmentTax) {
        this.consumableEquipmentTax = consumableEquipmentTax;
    }

    @Column(name = "CONSUMABLE_CUTLERY_TAX", nullable = true)
    public BigDecimal getConsumableCutleryTax() {
        return consumableCutleryTax;
    }

    public void setConsumableCutleryTax(BigDecimal consumableCutleryTax) {
        this.consumableCutleryTax = consumableCutleryTax;
    }

    @Column(name = "CONSUMABLE_DISPOSABLE_TAX", nullable = true)
    public BigDecimal getConsumableDisposableTax() {
        return consumableDisposableTax;
    }

    public void setConsumableDisposableTax(BigDecimal consumableDisposableTax) {
        this.consumableDisposableTax = consumableDisposableTax;
    }

    @Column(name = "CONSUMABLE_LHI_TAX", nullable = true)
    public BigDecimal getConsumableLhiTax() {
        return consumableLhiTax;
    }

    public void setConsumableLhiTax(BigDecimal consumableLhiTax) {
        this.consumableLhiTax = consumableLhiTax;
    }

    @Column(name = "CONSUMABLE_IT_TAX", nullable = true)
    public BigDecimal getConsumableItTax() {
        return consumableItTax;
    }

    public void setConsumableItTax(BigDecimal consumableItTax) {
        this.consumableItTax = consumableItTax;
    }

    @Column(name = "CONSUMABLE_MAINTENANCE_TAX", nullable = true)
    public BigDecimal getConsumableMaintenanceTax() {
        return consumableMaintenanceTax;
    }

    public void setConsumableMaintenanceTax(BigDecimal consumableMaintenanceTax) {
        this.consumableMaintenanceTax = consumableMaintenanceTax;
    }

    @Column(name = "CONSUMABLE_KITCHEN_EQUIPMENT_TAX", nullable = true)
    public BigDecimal getConsumableKitchenEquipmentTax() {
        return consumableKitchenEquipmentTax;
    }

    public void setConsumableKitchenEquipmentTax(BigDecimal consumableKitchenEquipmentTax) {
        this.consumableKitchenEquipmentTax = consumableKitchenEquipmentTax;
    }

    @Column(name = "CONSUMABLE_OFFICE_EQUIPMENT_TAX", nullable = true)
    public BigDecimal getConsumableOfficeEquipmentTax() {
        return consumableOfficeEquipmentTax;
    }

    public void setConsumableOfficeEquipmentTax(BigDecimal consumableOfficeEquipmentTax) {
        this.consumableOfficeEquipmentTax = consumableOfficeEquipmentTax;
    }

    @Column(name = "CONSUMABLE_CHAI_MONK_TAX", nullable = true)
    public BigDecimal getConsumableChaiMonkTax() {
        return consumableChaiMonkTax;
    }

    public void setConsumableChaiMonkTax(BigDecimal consumableChaiMonkTax) {
        this.consumableChaiMonkTax = consumableChaiMonkTax;
    }

	/*
		new header for service order
	 */

    @Column(name = "VEHICLE_REGULAR_MAINTENANCE_HQ", nullable = true)
    public BigDecimal getVehicleRegularMaintenanceHq() {
        return vehicleRegularMaintenanceHq;
    }

    public void setVehicleRegularMaintenanceHq(BigDecimal vehicleRegularMaintenanceHq) {
        this.vehicleRegularMaintenanceHq = vehicleRegularMaintenanceHq;
    }

    @Column(name = "BUILDING_MAINTENANCE_HQ", nullable = true)
    public BigDecimal getBuildingMaintenanceHq() {
        return buildingMaintenanceHq;
    }

    public void setBuildingMaintenanceHq(BigDecimal buildingMaintenanceHq) {
        this.buildingMaintenanceHq = buildingMaintenanceHq;
    }

    @Column(name = "COMPUTER_IT_MAINTENANCE_HQ", nullable = true)
    public BigDecimal getComputerItMaintenanceHq() {
        return computerItMaintenanceHq;
    }

    public void setComputerItMaintenanceHq(BigDecimal computerItMaintenanceHq) {
        this.computerItMaintenanceHq = computerItMaintenanceHq;
    }

    @Column(name = "EQUIPMENT_MAINTENANCE_HQ", nullable = true)
    public BigDecimal getEquipmentMaintenanceHq() {
        return equipmentMaintenanceHq;
    }

    public void setEquipmentMaintenanceHq(BigDecimal equipmentMaintenanceHq) {
        this.equipmentMaintenanceHq = equipmentMaintenanceHq;
    }

    @Column(name = "BONUS_ATTENDANCE", nullable = true)
    public BigDecimal getBonusAttendance() {
        return bonusAttendance;
    }

    public void setBonusAttendance(BigDecimal bonusAttendance) {
        this.bonusAttendance = bonusAttendance;
    }

    @Column(name = "BONUS_JOINING", nullable = true)
    public BigDecimal getBonusJoining() {
        return bonusJoining;
    }

    public void setBonusJoining(BigDecimal bonusJoining) {
        this.bonusJoining = bonusJoining;
    }

    @Column(name = "BONUS_REFERRAL", nullable = true)
    public BigDecimal getBonusReferral() {
        return bonusReferral;
    }

    public void setBonusReferral(BigDecimal bonusReferral) {
        this.bonusReferral = bonusReferral;
    }

    @Column(name = "BONUS_HOLIDAY", nullable = true)
    public BigDecimal getBonusHoliday() {
        return bonusHoliday;
    }

    public void setBonusHoliday(BigDecimal bonusHoliday) {
        this.bonusHoliday = bonusHoliday;
    }

    @Column(name = "BONUS_OTHERS", nullable = true)
    public BigDecimal getBonusOthers() {
        return bonusOthers;
    }

    public void setBonusOthers(BigDecimal bonusOthers) {
        this.bonusOthers = bonusOthers;
    }

    @Column(name = "ALLOWANCE_REMOTE_LOCATION", nullable = true)
    public BigDecimal getAllowanceRemoteLocation() {
        return allowanceRemoteLocation;
    }

    public void setAllowanceRemoteLocation(BigDecimal allowanceRemoteLocation) {
        this.allowanceRemoteLocation = allowanceRemoteLocation;
    }

    @Column(name = "ALLOWANCE_EMPLOYEE_BENEFIT", nullable = true)
    public BigDecimal getAllowanceEmployeeBenefit() {
        return allowanceEmployeeBenefit;
    }

    public void setAllowanceEmployeeBenefit(BigDecimal allowanceEmployeeBenefit) {
        this.allowanceEmployeeBenefit = allowanceEmployeeBenefit;
    }

    @Column(name = "ALLOWANCE_CITY_COMPENSATORY", nullable = true)
    public BigDecimal getAllowanceCityCompensatory() {
        return allowanceCityCompensatory;
    }

    public void setAllowanceCityCompensatory(BigDecimal allowanceCityCompensatory) {
        this.allowanceCityCompensatory = allowanceCityCompensatory;
    }

    @Column(name = "ALLOWANCE_MONK", nullable = true)
    public BigDecimal getAllowanceMonk() {
        return allowanceMonk;
    }

    public void setAllowanceMonk(BigDecimal allowanceMonk) {
        this.allowanceMonk = allowanceMonk;
    }

    @Column(name = "ALLOWANCE_OTHERS", nullable = true)
    public BigDecimal getAllowanceOthers() {
        return allowanceOthers;
    }

    public void setAllowanceOthers(BigDecimal allowanceOthers) {
        this.allowanceOthers = allowanceOthers;
    }

    @Column(name = "NOTICE_PERIOD_BUY_OUT", nullable = true)
    public BigDecimal getNoticePeriodBuyout() {
        return noticePeriodBuyout;
    }

    public void setNoticePeriodBuyout(BigDecimal noticePeriodBuyout) {
        this.noticePeriodBuyout = noticePeriodBuyout;
    }

    @Column(name = "NOTICE_PERIOD_DEDUCTION", nullable = true)
    public BigDecimal getNoticePeriodDeduction() {
        return noticePeriodDeduction;
    }

    public void setNoticePeriodDeduction(BigDecimal noticePeriodDeduction) {
        this.noticePeriodDeduction = noticePeriodDeduction;
    }

    @Column(name = "RELOCATION_EXPENSES", nullable = true)
    public BigDecimal getRelocationExpenses() {
        return relocationExpenses;
    }

    public void setRelocationExpenses(BigDecimal relocationExpenses) {
        this.relocationExpenses = relocationExpenses;
    }

    @Column(name = "STIPEND_EXPENSE", nullable = true)
    public BigDecimal getStipendExpenses() {
        return stipendExpenses;
    }

    public void setStipendExpenses(BigDecimal stipendExpenses) {
        this.stipendExpenses = stipendExpenses;
    }

    @Column(name = "TRAINING_COST_RECOVERY", nullable = true)
    public BigDecimal getTrainingCostRecovery() {
        return trainingCostRecovery;
    }

    public void setTrainingCostRecovery(BigDecimal trainingCostRecovery) {
        this.trainingCostRecovery = trainingCostRecovery;
    }

    @Column(name = "SEVERANCE_PAY", nullable = true)
    public BigDecimal getSeverancePay() {
        return severancePay;
    }

    public void setSeverancePay(BigDecimal severancePay) {
        this.severancePay = severancePay;
    }

    @Column(name = "LABOUR_CHARGES", nullable = true)
    public BigDecimal getLabourCharges() {
        return labourCharges;
    }

    public void setLabourCharges(BigDecimal labourCharges) {
        this.labourCharges = labourCharges;
    }

    @Column(name = "MARKETING_NPI_HQ", nullable = true)
    public BigDecimal getMarketingNpiHq() {
        return marketingNpiHq;
    }

    public void setMarketingNpiHq(BigDecimal marketingNpiHq) {
        this.marketingNpiHq = marketingNpiHq;
    }

    @Column(name = "LICENSE_EXPENSES", nullable = true)
    public BigDecimal getLicenseExpenses() {
        return licenseExpenses;
    }

    public void setLicenseExpenses(BigDecimal licenseExpenses) {
        this.licenseExpenses = licenseExpenses;
    }

    @Column(name = "CORPORATE_MARKETING_ATL_RADIO", nullable = true)
    public BigDecimal getCorporateMarketingAtlRadio() {
        return corporateMarketingAtlRadio;
    }

    public void setCorporateMarketingAtlRadio(BigDecimal corporateMarketingAtlRadio) {
        this.corporateMarketingAtlRadio = corporateMarketingAtlRadio;
    }

    @Column(name = "CORPORATE_MARKETING_ATL_TV", nullable = true)
    public BigDecimal getCorporateMarketingAtlTv() {
        return corporateMarketingAtlTv;
    }

    public void setCorporateMarketingAtlTv(BigDecimal corporateMarketingAtlTv) {
        this.corporateMarketingAtlTv = corporateMarketingAtlTv;
    }

    @Column(name = "CORPORATE_MARKETING_ATL_PRINT_AD", nullable = true)
    public BigDecimal getCorporateMarketingAtlPrintAd() {
        return corporateMarketingAtlPrintAd;
    }

    public void setCorporateMarketingAtlPrintAd(BigDecimal corporateMarketingAtlPrintAd) {
        this.corporateMarketingAtlPrintAd = corporateMarketingAtlPrintAd;
    }

    @Column(name = "CORPORATE_MARKETING_ATL_CINEMA", nullable = true)
    public BigDecimal getCorporateMarketingAtlCinema() {
        return corporateMarketingAtlCinema;
    }

    public void setCorporateMarketingAtlCinema(BigDecimal corporateMarketingAtlCinema) {
        this.corporateMarketingAtlCinema = corporateMarketingAtlCinema;
    }

    @Column(name = "CORPORATE_MARKETING_ATL_DIGITAL", nullable = true)
    public BigDecimal getCorporateMarketingAtlDigital() {
        return corporateMarketingAtlDigital;
    }

    public void setCorporateMarketingAtlDigital(BigDecimal corporateMarketingAtlDigital) {
        this.corporateMarketingAtlDigital = corporateMarketingAtlDigital;
    }

    @Column(name = "LOGISTIC_INTRASTATE_COLD_VEHICLE", nullable = true)
    public BigDecimal getLogisticInterstateColdVehicle() {
        return logisticInterstateColdVehicle;
    }

    public void setLogisticInterstateColdVehicle(BigDecimal logisticInterstateColdVehicle) {
        this.logisticInterstateColdVehicle = logisticInterstateColdVehicle;
    }

    @Column(name = "LOGISTIC_INTRASTATE_NON_COLD_VEHICLE", nullable = true)
    public BigDecimal getLogisticInterstateNonColdVehicle() {
        return logisticInterstateNonColdVehicle;
    }

    public void setLogisticInterstateNonColdVehicle(BigDecimal logisticInterstateNonColdVehicle) {
        this.logisticInterstateNonColdVehicle = logisticInterstateNonColdVehicle;
    }

    @Column(name = "LOGISTIC_INTERSTATE_AIR", nullable = true)
    public BigDecimal getLogisticInterstateAir() {
        return logisticInterstateAir;
    }

    public void setLogisticInterstateAir(BigDecimal logisticInterstateAir) {
        this.logisticInterstateAir = logisticInterstateAir;
    }

    @Column(name = "LOGISTIC_INTERSTATE_ROAD", nullable = true)
    public BigDecimal getLogisticInterstateRoad() {
        return logisticInterstateRoad;
    }

    public void setLogisticInterstateRoad(BigDecimal logisticInterstateRoad) {
        this.logisticInterstateRoad = logisticInterstateRoad;
    }

    @Column(name = "LOGISTIC_INTERSTATE_TRAIN", nullable = true)
    public BigDecimal getLogisticInterstateTrain() {
        return logisticInterstateTrain;
    }

    public void setLogisticInterstateTrain(BigDecimal logisticInterstateTrain) {
        this.logisticInterstateTrain = logisticInterstateTrain;
    }

    @Column(name = "AIR_CONDITIONER_AMC", nullable = true)
    public BigDecimal getAirConditionerAmc() {
        return airConditionerAmc;
    }

    public void setAirConditionerAmc(BigDecimal airConditionerAmc) {
        this.airConditionerAmc = airConditionerAmc;
    }

    @Column(name = "CORPORATE_MARKETING_SMS_EMAIL", nullable = true)
    public BigDecimal getCorporateMarketingSms() {
        return corporateMarketingSms;
    }

    public void setCorporateMarketingSms(BigDecimal corporateMarketingSms) {
        this.corporateMarketingSms = corporateMarketingSms;
    }

    @Column(name = "OTHER_SERVICE_CHARGES", nullable = true)
    public BigDecimal getOtherServiceCharges() {
        return otherServiceCharges;
    }

    public void setOtherServiceCharges(BigDecimal otherServiceCharges) {
        this.otherServiceCharges = otherServiceCharges;
    }

	/*
	 Fixed Asset Tax
	 */

    @Column(name = "FIXED_ASSETS_EQUIPMENT_TAX", nullable = true)
    public BigDecimal getFixedAssetsEquipmentTax() {
        return fixedAssetsEquipmentTax;
    }

    public void setFixedAssetsEquipmentTax(BigDecimal fixedAssetsEquipmentTax) {
        this.fixedAssetsEquipmentTax = fixedAssetsEquipmentTax;
    }

    @Column(name = "FIXED_ASSET_FURNITURE_TAX", nullable = true)
    public BigDecimal getFixedAssetFurnitureTax() {
        return fixedAssetFurnitureTax;
    }

    public void setFixedAssetFurnitureTax(BigDecimal fixedAssetFurnitureTax) {
        this.fixedAssetFurnitureTax = fixedAssetFurnitureTax;
    }

    @Column(name = "FIXED_ASSETS_IT_TAX", nullable = true)
    public BigDecimal getFixedAssetsItTax() {
        return fixedAssetsItTax;
    }

    public void setFixedAssetsItTax(BigDecimal fixedAssetsItTax) {
        this.fixedAssetsItTax = fixedAssetsItTax;
    }

    @Column(name = "FIXED_ASSETS_KITCHEN_EQUIPMENT_TAX", nullable = true)
    public BigDecimal getFixedAssetsKitchenEquipmentTax() {
        return fixedAssetsKitchenEquipmentTax;
    }

    public void setFixedAssetsKitchenEquipmentTax(BigDecimal fixedAssetsKitchenEquipmentTax) {
        this.fixedAssetsKitchenEquipmentTax = fixedAssetsKitchenEquipmentTax;
    }

    @Column(name = "FIXED_ASSETS_OFFICE_EQUIPMENT_TAX", nullable = true)
    public BigDecimal getFixedAssetsOfficeEquipmentTax() {
        return fixedAssetsOfficeEquipmentTax;
    }

    public void setFixedAssetsOfficeEquipmentTax(BigDecimal fixedAssetsOfficeEquipmentTax) {
        this.fixedAssetsOfficeEquipmentTax = fixedAssetsOfficeEquipmentTax;
    }

    @Column(name = "FIXED_ASSETS_VEHICLE_TAX", nullable = true)
    public BigDecimal getFixedAssetsVehicleTax() {
        return fixedAssetsVehicleTax;
    }

    public void setFixedAssetsVehicleTax(BigDecimal fixedAssetsVehicleTax) {
        this.fixedAssetsVehicleTax = fixedAssetsVehicleTax;
    }

    @Column(name = "FIXED_ASSETS_OTHERS_SUB_CATEGORY_CAFE_TAX", nullable = true)
    public BigDecimal getFixedAssetsOthersSubCategoryCafeTax() {
        return fixedAssetsOthersSubCategoryCafeTax;
    }

    public void setFixedAssetsOthersSubCategoryCafeTax(BigDecimal fixedAssetsOthersSubCategoryCafeTax) {
        this.fixedAssetsOthersSubCategoryCafeTax = fixedAssetsOthersSubCategoryCafeTax;
    }

    @Column(name = "FIXED_ASSETS_EQUIPMENT_HQ", nullable = true)
    public BigDecimal getFixedAssetsEquipmentHq() {
        return fixedAssetsEquipmentHq;
    }

    public void setFixedAssetsEquipmentHq(BigDecimal fixedAssetsEquipmentHq) {
        this.fixedAssetsEquipmentHq = fixedAssetsEquipmentHq;
    }

    @Column(name = "FIXED_ASSET_FURNITURE_HQ", nullable = true)
    public BigDecimal getFixedAssetFurnitureHq() {
        return fixedAssetFurnitureHq;
    }

    public void setFixedAssetFurnitureHq(BigDecimal fixedAssetFurnitureHq) {
        this.fixedAssetFurnitureHq = fixedAssetFurnitureHq;
    }

    @Column(name = "FIXED_ASSETS_IT_HQ", nullable = true)
    public BigDecimal getFixedAssetsItHq() {
        return fixedAssetsItHq;
    }

    public void setFixedAssetsItHq(BigDecimal fixedAssetsItHq) {
        this.fixedAssetsItHq = fixedAssetsItHq;
    }

    @Column(name = "FIXED_ASSETS_KITCHEN_EQUIPMENT_HQ", nullable = true)
    public BigDecimal getFixedAssetsKitchenEquipmentHq() {
        return fixedAssetsKitchenEquipmentHq;
    }

    public void setFixedAssetsKitchenEquipmentHq(BigDecimal fixedAssetsKitchenEquipmentHq) {
        this.fixedAssetsKitchenEquipmentHq = fixedAssetsKitchenEquipmentHq;
    }

    @Column(name = "FIXED_ASSETS_OFFICE_EQUIPMENT_HQ", nullable = true)
    public BigDecimal getFixedAssetsOfficeEquipmentHq() {
        return fixedAssetsOfficeEquipmentHq;
    }

    public void setFixedAssetsOfficeEquipmentHq(BigDecimal fixedAssetsOfficeEquipmentHq) {
        this.fixedAssetsOfficeEquipmentHq = fixedAssetsOfficeEquipmentHq;
    }

    @Column(name = "FIXED_ASSETS_VEHICLE_HQ", nullable = true)
    public BigDecimal getFixedAssetsVehicleHq() {
        return fixedAssetsVehicleHq;
    }

    public void setFixedAssetsVehicleHq(BigDecimal fixedAssetsVehicleHq) {
        this.fixedAssetsVehicleHq = fixedAssetsVehicleHq;
    }

    @Column(name = "FIXED_ASSETS_OTHERS_SUB_CATEGORY_HQ", nullable = true)
    public BigDecimal getFixedAssetsOthersSubCategoryHq() {
        return fixedAssetsOthersSubCategoryHq;
    }

    public void setFixedAssetsOthersSubCategoryHq(BigDecimal fixedAssetsOthersSubCategoryHq) {
        this.fixedAssetsOthersSubCategoryHq = fixedAssetsOthersSubCategoryHq;
    }

    @Column(name = "FIXED_ASSETS_EQUIPMENT_HQ_TAX", nullable = true)
    public BigDecimal getFixedAssetsEquipmentHqTax() {
        return fixedAssetsEquipmentHqTax;
    }

    public void setFixedAssetsEquipmentHqTax(BigDecimal fixedAssetsEquipmentHqTax) {
        this.fixedAssetsEquipmentHqTax = fixedAssetsEquipmentHqTax;
    }

    @Column(name = "FIXED_ASSET_FURNITURE_HQ_TAX", nullable = true)
    public BigDecimal getFixedAssetFurnitureHqTax() {
        return fixedAssetFurnitureHqTax;
    }

    public void setFixedAssetFurnitureHqTax(BigDecimal fixedAssetFurnitureHqTax) {
        this.fixedAssetFurnitureHqTax = fixedAssetFurnitureHqTax;
    }

    @Column(name = "FIXED_ASSETS_IT_HQ_TAX", nullable = true)
    public BigDecimal getFixedAssetsItHqTax() {
        return fixedAssetsItHqTax;
    }

    public void setFixedAssetsItHqTax(BigDecimal fixedAssetsItHqTax) {
        this.fixedAssetsItHqTax = fixedAssetsItHqTax;
    }

    @Column(name = "FIXED_ASSETS_KITCHEN_EQUIPMENT_HQ_TAX", nullable = true)
    public BigDecimal getFixedAssetsKitchenEquipmentHqTax() {
        return fixedAssetsKitchenEquipmentHqTax;
    }

    public void setFixedAssetsKitchenEquipmentHqTax(BigDecimal fixedAssetsKitchenEquipmentHqTax) {
        this.fixedAssetsKitchenEquipmentHqTax = fixedAssetsKitchenEquipmentHqTax;
    }

    @Column(name = "FIXED_ASSETS_OFFICE_EQUIPMENT_HQ_TAX", nullable = true)
    public BigDecimal getFixedAssetsOfficeEquipmentHqTax() {
        return fixedAssetsOfficeEquipmentHqTax;
    }

    public void setFixedAssetsOfficeEquipmentHqTax(BigDecimal fixedAssetsOfficeEquipmentHqTax) {
        this.fixedAssetsOfficeEquipmentHqTax = fixedAssetsOfficeEquipmentHqTax;
    }

    @Column(name = "FIXED_ASSETS_VEHICLE_HQ_TAX", nullable = true)
    public BigDecimal getFixedAssetsVehicleHqTax() {
        return fixedAssetsVehicleHqTax;
    }

    public void setFixedAssetsVehicleHqTax(BigDecimal fixedAssetsVehicleHqTax) {
        this.fixedAssetsVehicleHqTax = fixedAssetsVehicleHqTax;
    }

    @Column(name = "FIXED_ASSETS_OTHERS_SUB_CATEGORY_HQ_TAX", nullable = true)
    public BigDecimal getFixedAssetsOthersSubCategoryHqTax() {
        return fixedAssetsOthersSubCategoryHqTax;
    }

    public void setFixedAssetsOthersSubCategoryHqTax(BigDecimal fixedAssetsOthersSubCategoryHqTax) {
        this.fixedAssetsOthersSubCategoryHqTax = fixedAssetsOthersSubCategoryHqTax;
    }

    @Column(name = "EMP_DISCOUNT_LOYAL_TEA", nullable = true)
    public BigDecimal getEmpDiscountLoyalty() {
        return empDiscountLoyalty;
    }

    public void setEmpDiscountLoyalty(BigDecimal empDiscountLoyalty) {
        this.empDiscountLoyalty = empDiscountLoyalty;
    }

    @Column(name = "EMP_DISCOUNT_MARKETING", nullable = true)
    public BigDecimal getEmpDiscountMarketing() {
        return empDiscountMarketing;
    }

    public void setEmpDiscountMarketing(BigDecimal empDiscountMarketing) {
        this.empDiscountMarketing = empDiscountMarketing;
    }

    @Column(name = "EMP_DISCOUNT_OPS", nullable = true)
    public BigDecimal getEmpDiscountOps() {
        return empDiscountOps;
    }

    public void setEmpDiscountOps(BigDecimal empDiscountOps) {
        this.empDiscountOps = empDiscountOps;
    }

    @Column(name = "EMP_DISCOUNT_BD", nullable = true)
    public BigDecimal getEmpDiscountBd() {
        return empDiscountBd;
    }

    public void setEmpDiscountBd(BigDecimal empDiscountBd) {
        this.empDiscountBd = empDiscountBd;
    }

    @Column(name = "EMP_DISCOUNT_EMPLOYEE_FICO", nullable = true)
    public BigDecimal getEmpDiscountEmployeeFico() {
        return empDiscountEmployeeFico;
    }

    public void setEmpDiscountEmployeeFico(BigDecimal empDiscountEmployeeFico) {
        this.empDiscountEmployeeFico = empDiscountEmployeeFico;
    }

    @Column(name = "MAINTENANCE_PEST_CONTROL_CAFE", nullable = true)
    public BigDecimal getMaintenancePestControlCafe() {
        return maintenancePestControlCafe;
    }

    public void setMaintenancePestControlCafe(BigDecimal maintenancePestControlCafe) {
        this.maintenancePestControlCafe = maintenancePestControlCafe;
    }

//	@Column(name = "CONVEYANCE_ODC", nullable = true)
//	public BigDecimal getConveyanceOdc() {
//		return conveyanceOdc;
//	}
//
//	public void setConveyanceOdc(BigDecimal conveyanceOdc) {
//		this.conveyanceOdc = conveyanceOdc;
//	}

    @Column(name = "DEPRECIATION_OF_FURNITURE_FIXTURE", nullable = true)
    public BigDecimal getDepreciationOfFurnitureFixture() {
        return depreciationOfFurnitureFixture;
    }

    public void setDepreciationOfFurnitureFixture(BigDecimal depreciationOfFurnitureFixture) {
        this.depreciationOfFurnitureFixture = depreciationOfFurnitureFixture;
    }

    @Column(name = "DEPRECIATION_OF_OFFICE_EQUIPMENTS", nullable = true)
    public BigDecimal getDepreciationOfOfficeEquipment() {
        return depreciationOfOfficeEquipment;
    }

    public void setDepreciationOfOfficeEquipment(BigDecimal depreciationOfOfficeEquipment) {
        this.depreciationOfOfficeEquipment = depreciationOfOfficeEquipment;
    }

    @Column(name = "DEPRECIATION_OF_KITCHEN_EQUIPMENTS", nullable = true)
    public BigDecimal getDepreciationOfKitchenEquipment() {
        return depreciationOfKitchenEquipment;
    }

    public void setDepreciationOfKitchenEquipment(BigDecimal depreciationOfKitchenEquipment) {
        this.depreciationOfKitchenEquipment = depreciationOfKitchenEquipment;
    }

    @Column(name = "DEPRECIATION_OF_EQUIPMENTS", nullable = true)
    public BigDecimal getDepreciationOfEquipment() {
        return depreciationOfEquipment;
    }

    public void setDepreciationOfEquipment(BigDecimal depreciationOfEquipment) {
        this.depreciationOfEquipment = depreciationOfEquipment;
    }

    @Column(name = "DEPRECIATION_OF_IT", nullable = true)
    public BigDecimal getDepreciationOfIt() {
        return depreciationOfIt;
    }

    public void setDepreciationOfIt(BigDecimal depreciationOfIt) {
        this.depreciationOfIt = depreciationOfIt;
    }

    @Column(name = "DEPRECIATION_OF_VEHICLE", nullable = true)
    public BigDecimal getDepreciationOfVehicle() {
        return depreciationOfVehicle;
    }

    public void setDepreciationOfVehicle(BigDecimal depreciationOfVehicle) {
        this.depreciationOfVehicle = depreciationOfVehicle;
    }

    @Column(name = "DEPRECIATION_OF_OTHERS", nullable = true)
    public BigDecimal getDepreciationOfOthers() {
        return depreciationOfOthers;
    }

    public void setDepreciationOfOthers(BigDecimal depreciationOfOthers) {
        this.depreciationOfOthers = depreciationOfOthers;
    }

    @Column(name = "SECURITY_DEPOSIT_PROPERTY", nullable = true)
    public BigDecimal getSecurityDepositProperty() {
        return securityDepositProperty;
    }

    public void setSecurityDepositProperty(BigDecimal securityDepositProperty) {
        this.securityDepositProperty = securityDepositProperty;
    }

    @Column(name = "SECURITY_DEPOSIT_MVAT", nullable = true)
    public BigDecimal getSecurityDepositMVAT() {
        return securityDepositMVAT;
    }

    public void setSecurityDepositMVAT(BigDecimal securityDepositMVAT) {
        this.securityDepositMVAT = securityDepositMVAT;
    }

    @Column(name = "SECURITY_DEPOSIT_ELECTRICITY", nullable = true)
    public BigDecimal getSecurityDepositElectricity() {
        return securityDepositElectricity;
    }

    public void setSecurityDepositElectricity(BigDecimal securityDepositElectricity) {
        this.securityDepositElectricity = securityDepositElectricity;
    }

    @Column(name = "MARKETING_DISCOUNT_ECOM", nullable = true)
    public BigDecimal getMarketingDiscountEcom() {
        return marketingDiscountEcom;
    }

    public void setMarketingDiscountEcom(BigDecimal marketingDiscountEcom) {
        this.marketingDiscountEcom = marketingDiscountEcom;
    }

    @Column(name = "SHIPPING_CHARGES", nullable = true)
    public BigDecimal getShippingCharges() {
        return shippingCharges;
    }

    public void setShippingCharges(BigDecimal shippingCharges) {
        this.shippingCharges = shippingCharges;
    }

    @Column(name = "OTHER_TRANSACTION_CHARGES", nullable = true)
    public BigDecimal getOtherTransactionCharges() {
        return otherTransactionCharges;
    }

    public void setOtherTransactionCharges(BigDecimal otherTransactionCharges) {
        this.otherTransactionCharges = otherTransactionCharges;
    }

    @Column(name = "DISCOUNT_DEALER_MARGIN", nullable = true)
    public BigDecimal getDiscountDealerMargin() {
        return discountDealerMargin;
    }

    public void setDiscountDealerMargin(BigDecimal discountDealerMargin) {
        this.discountDealerMargin = discountDealerMargin;
    }

    @Column(name = "PERFORMANCE_MARKETING_SERVICE", nullable = true)
    public BigDecimal getPerformanceMarketingService() {
        return performanceMarketingService;
    }

    public void setPerformanceMarketingService(BigDecimal performanceMarketingService) {
        this.performanceMarketingService = performanceMarketingService;
    }

    @Column(name = "INSURANCE_MARINE", nullable = true)
    public BigDecimal getInsuranceMarine() {
        return insuranceMarine;
    }

    public void setInsuranceMarine(BigDecimal insuranceMarine) {
        this.insuranceMarine = insuranceMarine;
    }

    @Column(name = "SHARE_STAMPING_CHARGES", nullable = true)
    public BigDecimal getShareStampingCharges() {
        return shareStampingCharges;
    }

    public void setShareStampingCharges(BigDecimal shareStampingCharges) {
        this.shareStampingCharges = shareStampingCharges;
    }

    @Column(name = "OTHER_CHARGES_ECOM", nullable = true)
    public BigDecimal getOtherChargesEcom() {
        return otherChargesEcom;
    }

    public void setOtherChargesEcom(BigDecimal otherChargesEcom) {
        this.otherChargesEcom = otherChargesEcom;
    }

    @Column(name = "COMISSION_CHANNEL_PARTNER_FIXED", nullable = true)
    public BigDecimal getComissionChannelPartnerFixed() {
        return comissionChannelPartnerFixed;
    }

    public void setComissionChannelPartnerFixed(BigDecimal comissionChannelPartnerFixed) {
        this.comissionChannelPartnerFixed = comissionChannelPartnerFixed;
    }

    @Column(name = "COGS_TRADING_GOODS", nullable = true)
    public BigDecimal getCogsTradingGoods() {
        return cogsTradingGoods;
    }

    public void setCogsTradingGoods(BigDecimal cogsTradingGoods) {
        this.cogsTradingGoods = cogsTradingGoods;
    }

    @Column(name = "ROYALTY_FEES", nullable = true)
    public BigDecimal getRoyaltyFees() {
        return royaltyFees;
    }

    public void setRoyaltyFees(BigDecimal royaltyFees) {
        this.royaltyFees = royaltyFees;
    }

    @Column(name = "FREIGHT_CHARGES", nullable = true)
    public BigDecimal getFreightCharges() {
        return freightCharges;
    }

    public void setFreightCharges(BigDecimal freightCharges) {
        this.freightCharges = freightCharges;
    }
}