package com.stpl.tech.kettle.data.dao;

import com.stpl.tech.kettle.data.model.OrderNotification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface OrderNotificationDao extends JpaRepository<OrderNotification,Integer> {
    List<OrderNotification> findByOrderIdIn(List<Integer> orderIds);
}
