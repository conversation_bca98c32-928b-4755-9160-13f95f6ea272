package com.stpl.tech.kettle.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.OneToOne;
import javax.persistence.Table;

@Entity
@Table(name = "TABLE_ORDER_MAPPING")
public class TableOrderMappingDetail {

	private int mappingId;
	private int tableRequestId;
	private OrderDetail order;

	public TableOrderMappingDetail() {
		// TODO Auto-generated constructor stub
	}

	public TableOrderMappingDetail(int tableRequestId, OrderDetail order) {
		super();
		this.tableRequestId = tableRequestId;
		this.order = order;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "MAPPING_ID", unique = true, nullable = false)
	public int getMappingId() {
		return mappingId;
	}

	public void setMappingId(int mappingId) {
		this.mappingId = mappingId;
	}

	@Column(name = "TABLE_REQUEST_ID", nullable = false)
	public int getTableRequestId() {
		return tableRequestId;
	}

	public void setTableRequestId(int tableRequestId) {
		this.tableRequestId = tableRequestId;
	}

	@OneToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ORDER_ID", nullable = false)
	public OrderDetail getOrder() {
		return order;
	}

	public void setOrder(OrderDetail order) {
		this.order = order;
	}

}
