package com.stpl.tech.kettle.data.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "MONK_RECIPE_ADDONS_DATA")
@Getter
@Setter
public class MonkRecipeAddonsData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Integer id;

    @Column(name = "MONK_TASK_COMPLETION_STATS_ID", nullable = false)
    private Integer monkTaskCompletionStatsId;

    @Column(name = "ADDON_NAME", nullable = false)
    private String addonName;

    @Column(name = "QUANTITY", nullable = false)
    private Integer quantity;
}
