/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */
package com.stpl.tech.kettle.customer.dao;

import com.stpl.tech.kettle.core.LoyaltyEventType;
import com.stpl.tech.kettle.data.model.LoyaltyEvents;
import com.stpl.tech.kettle.data.model.LoyaltyLogHistory;
import com.stpl.tech.kettle.data.model.LoyaltyScore;
import com.stpl.tech.master.data.dao.AbstractDao;

import java.util.Date;
import java.util.List;

public interface LoyaltyDao extends AbstractDao {

	public boolean updateScore(int customerId, LoyaltyEventType type, int points, int cumulativePoints, Integer orderId,
			boolean hasSignupOffer, boolean updateLastOrderId);

	public boolean updateScore(int customerId, LoyaltyEventType type, int points, Integer orderId,
			boolean hasSignupOffer, boolean updateLastOrderId);

	public boolean createEventAndUpdate(int customerId, LoyaltyEventType type, int points, Integer orderId,
			boolean hasSignupOffer, boolean updateLastOrderId);

	public boolean createLoyaltyEvent(int customerId, LoyaltyEventType type, int points, Integer orderId);

	public LoyaltyScore getScore(int customerId);

	public boolean isLoyaltyAwarded(int customerId, Integer orderId);

	public void updateCustomerId(int customerId, Integer orderId);

	public LoyaltyEvents getTransactionPointsByOrderId(Integer orderId);

	public boolean isLoyaltyAwarded(int customerId, LoyaltyEventType emailVerification);

	public boolean createLoyaltyEvent(int customerId, int points, LoyaltyEventType type, String status, Integer orderId, int finalAcquiredPoints,
									  Integer acquiredPoints,String reason);

	public List<LoyaltyEvents> getLoyaltyEventsForCustomerId(String customerId,Integer limit);

	LoyaltyLogHistory getLoyaltyEventLog(Integer loyaltyEventId, Integer points, String transactionCodeType,
										 String transactionType, String status, Integer orderId, Integer transactionEventId);

	public List<LoyaltyEvents> findAllByCustomerIdAndLoyaltyEventStatusAndTransactionStatus(Integer customerId,
																							String loyaltyEventStatus,
																							String transactionStatus);


    public void saveLoyaltyEventLog(Integer loyaltyEventId, Integer points, String transactionCodeType,
                                    String transactionType, String status, Integer orderId, Integer transactionEventId);
	public void updateLoyaltyEvents(int customerId,int points,Integer orderId,Integer transactionEventId,String loyaltyEventStatus);

    List<LoyaltyEvents> findAllByExpirationTimeLessThanEqualAndLoyaltyEventStatus(Date currentDate,
                                                                                  String loyaltyEventStatus);

    public Boolean isCustomerAddedIsNew(Integer customerId);
	public LoyaltyEvents getExpiredLoyaltyEvent(Integer customerId,Integer expiredPoints,Integer acquiredPoints,Integer finalAcquiredPoints);
	public List<LoyaltyEvents> findAllLoyaltyEventForCustomerExpectGiftingEvent(Integer customerId,
																				String loyaltyEventStatus,
																				String transactionStatus);
}
