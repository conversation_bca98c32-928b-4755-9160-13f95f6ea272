/*
 * Created By Shanmukh
 */

package com.stpl.tech.kettle.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "ASSEMBLY_NOTIFICATION_UNIT_WISE_DATA")
public class AssemblyNotificationUnitWiseData {

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "ASSEMBLY_NOTIFICATION_UNIT_WISE_DATA_ID", unique = true, nullable = false)
    private Integer assemblyNotificationUnitWiseDataId;

    @Column(name = "UNIT_ID",nullable = false)
    private Integer unitId;

    @Column(name="NOTIFICATION_TYPE",nullable = false)
    private String notificationType;

    @Column(name = "LAST_ASSEMBLY_NOTIFICATION_DATA_ID",nullable = true)
    private Integer lastAssemblyNotificationId;
}
