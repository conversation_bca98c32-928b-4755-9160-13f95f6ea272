/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

import static javax.persistence.GenerationType.IDENTITY;

// Generated 19 Aug, 2015 4:37:51 PM by Hibernate Tools 4.0.0

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * ReportAttributes generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "REPORT_ATTRIBUTES")
public class ReportAttributes implements java.io.Serializable {

	private Integer reportAttributesId;
	private ReportDefinition reportDefinition;
	private AttributeDefinition attributeDefinition;
	private String stringValue;
	private Integer integerValue;
	private BigDecimal doubleValue;
	private String booleanValue;
	private Date dateValue;
	private Date timestampValue;

	public ReportAttributes() {
	}

	public ReportAttributes(ReportDefinition reportDefinition, AttributeDefinition attributeDefinition) {
		this.reportDefinition = reportDefinition;
		this.attributeDefinition = attributeDefinition;
	}

	public ReportAttributes(ReportDefinition reportDefinition, AttributeDefinition attributeDefinition,
			String stringValue, Integer integerValue, BigDecimal doubleValue, String booleanValue, Date dateValue,
			Date timestampValue) {
		this.reportDefinition = reportDefinition;
		this.attributeDefinition = attributeDefinition;
		this.stringValue = stringValue;
		this.integerValue = integerValue;
		this.doubleValue = doubleValue;
		this.booleanValue = booleanValue;
		this.dateValue = dateValue;
		this.timestampValue = timestampValue;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "REPORT_ATTRIBUTES_ID", unique = true, nullable = false)
	public Integer getReportAttributesId() {
		return this.reportAttributesId;
	}

	public void setReportAttributesId(Integer reportAttributesId) {
		this.reportAttributesId = reportAttributesId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "REPORT_DEF_ID", nullable = false)
	public ReportDefinition getReportDefinition() {
		return this.reportDefinition;
	}

	public void setReportDefinition(ReportDefinition reportDefinition) {
		this.reportDefinition = reportDefinition;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ATTRIBUTE_DEF_ID", nullable = false)
	public AttributeDefinition getAttributeDefinition() {
		return this.attributeDefinition;
	}

	public void setAttributeDefinition(AttributeDefinition attributeDefinition) {
		this.attributeDefinition = attributeDefinition;
	}

	@Column(name = "STRING_VALUE")
	public String getStringValue() {
		return this.stringValue;
	}

	public void setStringValue(String stringValue) {
		this.stringValue = stringValue;
	}

	@Column(name = "INTEGER_VALUE")
	public Integer getIntegerValue() {
		return this.integerValue;
	}

	public void setIntegerValue(Integer integerValue) {
		this.integerValue = integerValue;
	}

	@Column(name = "DOUBLE_VALUE", precision = 10)
	public BigDecimal getDoubleValue() {
		return this.doubleValue;
	}

	public void setDoubleValue(BigDecimal doubleValue) {
		this.doubleValue = doubleValue;
	}

	@Column(name = "BOOLEAN_VALUE", length = 1)
	public String getBooleanValue() {
		return this.booleanValue;
	}

	public void setBooleanValue(String booleanValue) {
		this.booleanValue = booleanValue;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "DATE_VALUE", length = 10)
	public Date getDateValue() {
		return this.dateValue;
	}

	public void setDateValue(Date dateValue) {
		this.dateValue = dateValue;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "TIMESTAMP_VALUE", length = 19)
	public Date getTimestampValue() {
		return this.timestampValue;
	}

	public void setTimestampValue(Date timestampValue) {
		this.timestampValue = timestampValue;
	}

}
