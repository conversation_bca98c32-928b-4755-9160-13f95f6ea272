package com.stpl.tech.kettle.stock.service;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.stpl.tech.kettle.core.data.vo.UnitTimeStockData;
import org.springframework.web.servlet.View;

import com.stpl.tech.kettle.core.data.vo.StockOutReportData;


public interface AutomatedStockEventReport {

	StockOutReportData execute(Date previousDate, Integer unitId,
                               boolean saveResults, List<Date> unitTime, boolean partner,Integer brand,
							   UnitTimeStockData unitTimeStockData, Map<Integer, Map<String, String>> latestPreviousStocEvent);

	View executeForDownload(Date startDate, Date endDate, List<Integer> unitId, Date calculationDate, boolean saveResults);

	public Map<Integer, UnitTimeStockData> getUnitClosingTimeMap(Date previousDate);

	public Map<Integer, Map<Integer,Map<String,String>>> getLatestPreviousStockEvent(Date eventTimestamp);

	public void saveScmStockOutData(Integer unitId , Date businessDate , Integer brandId ,
									Date cafeStartTime , Date cafeCloseTime);

}
