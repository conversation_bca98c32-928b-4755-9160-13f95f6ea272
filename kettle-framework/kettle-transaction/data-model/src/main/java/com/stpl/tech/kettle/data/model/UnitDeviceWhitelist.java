package com.stpl.tech.kettle.data.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.*;
import java.sql.Timestamp;

@Entity
@Table(name = "UNIT_DEVICE_WHITELIST", schema = "KETTLE_STAGE")
@Getter
@Setter
public class UnitDeviceWhitelist {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ID")
    private Integer id;

    @Column(name = "UNIT_ID", nullable = false, length = 50)
    private String unitId;

    @Column(name = "MAC_ADDRESS", nullable = false, length = 17, unique = true)
    private String macAddress;

    @Column(name = "STATUS", length = 20)
    private String status = "ACTIVE";

    @Column(name = "CREATED_AT", insertable = false, updatable = false)
    private Timestamp createdAt;

    @Column(name = "UPDATED_AT", insertable = false, updatable = false)
    private Timestamp updatedAt;
} 