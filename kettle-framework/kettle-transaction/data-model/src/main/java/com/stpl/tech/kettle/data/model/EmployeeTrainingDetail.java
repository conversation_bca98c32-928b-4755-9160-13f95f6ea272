/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

// Generated 14 Jul, 2015 1:35:13 AM by Hibernate Tools 4.0.0

import java.util.Date;

import javax.persistence.AttributeOverride;
import javax.persistence.AttributeOverrides;
import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * EmployeeTrainingDetail generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "EMPLOYEE_TRAINING_DETAIL")
public class EmployeeTrainingDetail implements java.io.Serializable {

	private EmployeeTrainingDetailId id;
	private TrainingDetail trainingDetail;
	private Integer employeeDetailByTrainiedBy;
	private Integer employeeDetailByEmpId;
	private String trainingStatus;
	private Date trainingStartDate;
	private Date trainingEndDate;
	private Integer trainingScore;
	private String needsReTraining;
	private String trainingFeedback;
	private Date inTmstmp;

	public EmployeeTrainingDetail() {
	}

	public EmployeeTrainingDetail(EmployeeTrainingDetailId id, TrainingDetail trainingDetail,
			Integer employeeDetailByEmpId, String trainingStatus, String needsReTraining, Date inTmstmp) {
		this.id = id;
		this.trainingDetail = trainingDetail;
		this.employeeDetailByEmpId = employeeDetailByEmpId;
		this.trainingStatus = trainingStatus;
		this.needsReTraining = needsReTraining;
		this.inTmstmp = inTmstmp;
	}

	public EmployeeTrainingDetail(EmployeeTrainingDetailId id, TrainingDetail trainingDetail,
			Integer employeeDetailByTrainiedBy, Integer employeeDetailByEmpId, String trainingStatus,
			Date trainingStartDate, Date trainingEndDate, Integer trainingScore, String needsReTraining,
			String trainingFeedback, Date inTmstmp) {
		this.id = id;
		this.trainingDetail = trainingDetail;
		this.employeeDetailByTrainiedBy = employeeDetailByTrainiedBy;
		this.employeeDetailByEmpId = employeeDetailByEmpId;
		this.trainingStatus = trainingStatus;
		this.trainingStartDate = trainingStartDate;
		this.trainingEndDate = trainingEndDate;
		this.trainingScore = trainingScore;
		this.needsReTraining = needsReTraining;
		this.trainingFeedback = trainingFeedback;
		this.inTmstmp = inTmstmp;
	}

	@EmbeddedId
	@AttributeOverrides({ @AttributeOverride(name = "empId", column = @Column(name = "EMP_ID", nullable = false)),
			@AttributeOverride(name = "trainingId", column = @Column(name = "TRAINING_ID", nullable = false)),
			@AttributeOverride(name = "outTmstmp", column = @Column(name = "OUT_TMSTMP", nullable = false, length = 19)) })
	public EmployeeTrainingDetailId getId() {
		return this.id;
	}

	public void setId(EmployeeTrainingDetailId id) {
		this.id = id;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "TRAINING_ID", nullable = false, insertable = false, updatable = false)
	public TrainingDetail getTrainingDetail() {
		return this.trainingDetail;
	}

	public void setTrainingDetail(TrainingDetail trainingDetail) {
		this.trainingDetail = trainingDetail;
	}

	@Column(name = "TRAINIED_BY")
	public Integer getEmployeeDetailByTrainiedBy() {
		return this.employeeDetailByTrainiedBy;
	}

	public void setEmployeeDetailByTrainiedBy(Integer employeeDetailByTrainiedBy) {
		this.employeeDetailByTrainiedBy = employeeDetailByTrainiedBy;
	}

	@Column(name = "EMP_ID", nullable = false, insertable = false, updatable = false)
	public Integer getEmployeeDetailByEmpId() {
		return this.employeeDetailByEmpId;
	}

	public void setEmployeeDetailByEmpId(Integer employeeDetailByEmpId) {
		this.employeeDetailByEmpId = employeeDetailByEmpId;
	}

	@Column(name = "TRAINING_STATUS", nullable = false, length = 15)
	public String getTrainingStatus() {
		return this.trainingStatus;
	}

	public void setTrainingStatus(String trainingStatus) {
		this.trainingStatus = trainingStatus;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "TRAINING_START_DATE", length = 10)
	public Date getTrainingStartDate() {
		return this.trainingStartDate;
	}

	public void setTrainingStartDate(Date trainingStartDate) {
		this.trainingStartDate = trainingStartDate;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "TRAINING_END_DATE", length = 10)
	public Date getTrainingEndDate() {
		return this.trainingEndDate;
	}

	public void setTrainingEndDate(Date trainingEndDate) {
		this.trainingEndDate = trainingEndDate;
	}

	@Column(name = "TRAINING_SCORE")
	public Integer getTrainingScore() {
		return this.trainingScore;
	}

	public void setTrainingScore(Integer trainingScore) {
		this.trainingScore = trainingScore;
	}

	@Column(name = "NEEDS_RE_TRAINING", nullable = false, length = 1)
	public String getNeedsReTraining() {
		return this.needsReTraining;
	}

	public void setNeedsReTraining(String needsReTraining) {
		this.needsReTraining = needsReTraining;
	}

	@Column(name = "TRAINING_FEEDBACK", length = 5000)
	public String getTrainingFeedback() {
		return this.trainingFeedback;
	}

	public void setTrainingFeedback(String trainingFeedback) {
		this.trainingFeedback = trainingFeedback;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "IN_TMSTMP", nullable = false, length = 19)
	public Date getInTmstmp() {
		return this.inTmstmp;
	}

	public void setInTmstmp(Date inTmstmp) {
		this.inTmstmp = inTmstmp;
	}

}
