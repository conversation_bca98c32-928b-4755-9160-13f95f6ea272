/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "PNL_ADJUSTMENT_DETAIL")
public class PnlAdjustmentDetail implements java.io.Serializable {

    private int adjustmentId;
    private String pnlHeaderName;
    private String pnlHeaderDetail;
    private String pnlHeaderColumnName;
    private String pnlHeaderType;
    private String adjustmentType;
    private BigDecimal adjustmentValue;
    private int unitId;
    private String status;
    private int month;
    private int year;
    private int createdBy;
    private Date creationTime;
    private String createComment;
    private String createCommentText;
    private int rejectedBy;
    private Date rejectionTime;
    private String rejectComment;
    private String rejectCommentText;
    private int approvedBy;
    private Date approvalTime;
    private String approvedComment;
    private String approvedCommentText;
    private int cancelledBy;
    private Date cancellationTime;
    private String cancellationComment;
    private String cancellationCommentText;
    private String isApplied;

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "ADJUSTMENT_ID", unique = true, nullable = false)
    public int getAdjustmentId() {
        return adjustmentId;
    }


    public void setAdjustmentId(int adjustmentId) {
        this.adjustmentId = adjustmentId;
    }

    @Column(name = "PNL_HEADER_NAME", nullable = false)
    public String getPnlHeaderName() {
        return pnlHeaderName;
    }

    public void setPnlHeaderName(String pnlHeaderName) {
        this.pnlHeaderName = pnlHeaderName;
    }

    @Column(name = "PNL_HEADER_DETAIL", nullable = false)
    public String getPnlHeaderDetail() {
        return pnlHeaderDetail;
    }

    public void setPnlHeaderDetail(String pnlHeaderDetail) {
        this.pnlHeaderDetail = pnlHeaderDetail;
    }

    @Column(name = "PNL_HEADER_COLUMN_NAME", nullable = false)
    public String getPnlHeaderColumnName() {
        return pnlHeaderColumnName;
    }

    public void setPnlHeaderColumnName(String pnlHeaderColumnName) {
        this.pnlHeaderColumnName = pnlHeaderColumnName;
    }

    @Column(name = "PNL_HEADER_TYPE", nullable = false)
    public String getPnlHeaderType() {
        return pnlHeaderType;
    }

    public void setPnlHeaderType(String pnlHeaderType) {
        this.pnlHeaderType = pnlHeaderType;
    }

    @Column(name = "ADJUSTMENT_TYPE", nullable = false)
    public String getAdjustmentType() {
        return adjustmentType;
    }

    public void setAdjustmentType(String adjustmentType) {
        this.adjustmentType = adjustmentType;
    }

    @Column(name = "ADJUSTMENT_VALUE", nullable = false)
    public BigDecimal getAdjustmentValue() {
        return adjustmentValue;
    }

    public void setAdjustmentValue(BigDecimal adjustmentValue) {
        this.adjustmentValue = adjustmentValue;
    }

    @Column(name = "UNIT_ID", nullable = false)
    public int getUnitId() {
        return unitId;
    }

    public void setUnitId(int unitId) {
        this.unitId = unitId;
    }

    @Column(name = "STATUS", nullable = false)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "MONTH", nullable = false)
    public int getMonth() {
        return month;
    }

    public void setMonth(int month) {
        this.month = month;
    }

    @Column(name = "YEAR", nullable = false)
    public int getYear() {
        return year;
    }

    public void setYear(int year) {
        this.year = year;
    }

    @Column(name = "CREATED_BY", nullable = false)
    public int getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(int createdBy) {
        this.createdBy = createdBy;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CREATION_TIME", nullable = false)
    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    @Column(name = "CREATE_COMMENT", nullable = false)
    public String getCreateComment() {
        return createComment;
    }

    public void setCreateComment(String createComment) {
        this.createComment = createComment;
    }

    @Column(name = "CREATE_COMMENT_TEXT", nullable = false)
    public String getCreateCommentText() {
        return createCommentText;
    }

    public void setCreateCommentText(String createCommentText) {
        this.createCommentText = createCommentText;
    }

    @Column(name = "REJECTED_BY")
    public int getRejectedBy() {
        return rejectedBy;
    }

    public void setRejectedBy(int rejectedBy) {
        this.rejectedBy = rejectedBy;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "REJECTION_TIME")
    public Date getRejectionTime() {
        return rejectionTime;
    }

    public void setRejectionTime(Date rejectionTime) {
        this.rejectionTime = rejectionTime;
    }

    @Column(name = "REJECT_COMMENT")
    public String getRejectComment() {
        return rejectComment;
    }

    public void setRejectComment(String rejectComment) {
        this.rejectComment = rejectComment;
    }

    @Column(name = "REJECT_COMMENT_TEXT")
    public String getRejectCommentText() {
        return rejectCommentText;
    }

    public void setRejectCommentText(String rejectCommentText) {
        this.rejectCommentText = rejectCommentText;
    }

    @Column(name = "APPROVED_BY")
    public int getApprovedBy() {
        return approvedBy;
    }

    public void setApprovedBy(int approvedBy) {
        this.approvedBy = approvedBy;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "APPROVAL_TIME")
    public Date getApprovalTime() {
        return approvalTime;
    }

    public void setApprovalTime(Date approvalTime) {
        this.approvalTime = approvalTime;
    }

    @Column(name = "APPROVED_COMMENT")
    public String getApprovedComment() {
        return approvedComment;
    }

    public void setApprovedComment(String approvedComment) {
        this.approvedComment = approvedComment;
    }

    @Column(name = "APPROVED_COMMENT_TEXT")
    public String getApprovedCommentText() {
        return approvedCommentText;
    }

    public void setApprovedCommentText(String approvedCommentText) {
        this.approvedCommentText = approvedCommentText;
    }

    @Column(name = "CANCELLED_BY")
    public int getCancelledBy() {
        return cancelledBy;
    }

    public void setCancelledBy(int cancelledBy) {
        this.cancelledBy = cancelledBy;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "CANCELLATION_TIME")
    public Date getCancellationTime() {
        return cancellationTime;
    }

    public void setCancellationTime(Date cancellationTime) {
        this.cancellationTime = cancellationTime;
    }

    @Column(name = "CANCELLATION_COMMENT")
    public String getCancellationComment() {
        return cancellationComment;
    }

    public void setCancellationComment(String cancellationComment) {
        this.cancellationComment = cancellationComment;
    }

    @Column(name = "CANCELLATION_COMMENT_TEXT")
    public String getCancellationCommentText() {
        return cancellationCommentText;
    }

    public void setCancellationCommentText(String cancellationCommentText) {
        this.cancellationCommentText = cancellationCommentText;
    }

    @Column(name = "IS_APPLIED")
    public String getIsApplied() {
        return isApplied;
    }

    public void setIsApplied(String isApplied) {
        this.isApplied = isApplied;
    }
}