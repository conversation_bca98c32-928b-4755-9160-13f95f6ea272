package com.stpl.tech.kettle.core.service.impl;

import com.paytm.pg.merchant.CheckSumServiceHelper;
import com.stpl.tech.kettle.core.exception.PaymentFailureException;
import com.stpl.tech.kettle.core.service.PayTMNewPaymentService;
import com.stpl.tech.kettle.core.service.PayTMPaymentService;
import com.stpl.tech.kettle.data.converter.DataConverter;
import com.stpl.tech.kettle.data.dao.PaymentGatewayDao;
import com.stpl.tech.kettle.data.model.OrderPaymentDetail;
import com.stpl.tech.kettle.data.model.PaytmPaymentDetails;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.payment.config.PaytmConfig;
import com.stpl.tech.master.payment.model.OrderPayment;
import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.master.payment.model.PaymentResponse;
import com.stpl.tech.master.payment.model.PaymentStatus;
import com.stpl.tech.master.payment.model.patymNew.PaytmStatusResponse;
import com.stpl.tech.master.payment.model.paytm.PaytmCreateQRResponse;
import com.stpl.tech.master.payment.model.paytm.PaytmCreateRequest;
import com.stpl.tech.master.payment.model.paytm.PaytmCreateResponse;
import com.stpl.tech.master.payment.model.paytm.PaytmPaymentStatus;
import com.stpl.tech.master.payment.model.paytm.PaytmPaymentStatusResponse;
import com.stpl.tech.master.payment.model.paytm.PaytmRefundAmountResponse;
import com.stpl.tech.master.payment.model.paytm.ResultInfo;
import com.stpl.tech.master.payment.model.paytmUpi.PaytmUpiQrRequest;
import com.stpl.tech.master.payment.model.paytmUpi.PaytmUpiQrResponse;
import com.stpl.tech.master.payment.model.paytmUpi.PaytmUpiResponse;
import com.stpl.tech.master.payment.model.paytmUpi.PaytmUpiS2SResponse;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;
import com.stpl.tech.util.notification.pubnub.PubnubService;
import com.stpl.tech.util.notification.pubnub.PushNotification;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.json.JSONException;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.HttpURLConnection;
import java.net.Proxy;
import java.net.URISyntaxException;
import java.net.URL;
import java.net.URLEncoder;
import java.sql.Timestamp;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.UUID;

@Service
public class PayTMPaymentServiceImpl implements PayTMPaymentService {

    private static final Logger LOG = LoggerFactory.getLogger(PayTMPaymentServiceImpl.class);

    private final CheckSumServiceHelper checkSumServiceHelper = CheckSumServiceHelper.getCheckSumServiceHelper();

    public static final String CHECK_PAYTM_PAYMENT_STATUS_ENDPOINT = "/merchant-status/getTxnStatus";

    @Autowired
    private PaymentGatewayDao paymentGatewayDao;

    @Autowired
    private PaytmConfig env;

    @Autowired
    private PubnubService pubnubService;

    @Autowired
    private MasterDataCache masterCache;

    @Autowired
    private PayTMNewPaymentService payTMNewPaymentService;

    public PaytmCreateRequest createRequest(OrderPaymentRequest order)
            throws PaymentFailureException, DataNotFoundException {
        PaytmCreateRequest request = createRequestObject(order.getGenerateOrderId(), order.getPaidAmount(),
                order.getContactNumber(), order.getRedirectUrl());
        String checkSum;
        try {
            TreeMap<String, String> parameters = request.getParameters();
            checkSum = checkSumServiceHelper.genrateCheckSum(env.getMerchantKey(), parameters);
            LOG.info("Generated checksum ::::::::: {}", checkSum);
        } catch (Exception e) {
            LOG.error("Exception while generating checksum for the request :::::: {}", order.getCartId(), e);
            throw new PaymentFailureException("Error in generating checksum for order " + order.getGenerateOrderId(), e);
        }
        request.setChecksumHash(checkSum);
        return request;
    }

    @Override
    public PaytmStatusResponse getPaytmStatusResponse(String orderId) throws Exception {
        PaytmStatusResponse paytmStatusResponse = payTMNewPaymentService.getPayTmPaymentStatus(orderId);
        return paytmStatusResponse;
    }

    @Override
    public boolean validateResponse(PaytmCreateResponse response) {
        boolean flag = false;
        try {
            if (checkSumServiceHelper.verifycheckSum(env.getMerchantKey(), response.getParameters(env.getMid()),
                    response.getChecksumHash())) {
                flag = response.getStatus().equalsIgnoreCase("TXN_SUCCESS");
            } else {
                LOG.info("Failed while validating checksum hash from PAYTM for external orderId :::", response.getOrderId());
            }
        } catch (Exception e) {
            LOG.error("Encountered error while validating response", e);
        }
        return flag;
    }





    @Override
    public PaytmCreateResponse getPaymentStatus(String externalOrderId, BigDecimal transactionAmount)
            throws PaymentFailureException, IOException, JSONException {

        env = env != null ? env : new PaytmConfig();
        JSONObject jsonRequestObj = new JSONObject();
        jsonRequestObj.put("MID", env.getMid());
        jsonRequestObj.put("ORDERID", externalOrderId);
        String body = jsonRequestObj.toString();
        LOG.info("body is :::::::::: {}", body);
        body = "?JsonData=" + body;
        return sendRequest("/oltp/HANDLER_INTERNAL/TXNSTATUS", "GET", env.getBaseUrl(), body, PaytmCreateResponse.class);
    }

    @Override
    public OrderPayment refundRequest(OrderPayment request) throws PaymentFailureException {
        try{
            env = env != null ? env : new PaytmConfig();
            String body = getJsonRequestObject(env, request);
            String url = "/oltp/HANDLER_INTERNAL/REFUND?JsonData=";
            String method = "POST";

            Map finalResponse = sendRequest(url, method, env.getBaseUrl(), body, Map.class);
            if (finalResponse != null) {
                String status = String.valueOf(finalResponse.containsKey("STATUS") ? finalResponse.get("STATUS") : null);
                if (status != null && (status.equals("TXN_SUCCESS") || status.equals("PENDING"))) {
                    request.setPaymentStatus(PaymentStatus.REFUND_PROCESSED);
                    request.setRefundStatus(PaymentStatus.CREATED);
                    request.setRefundId(String.valueOf(finalResponse.get("REFUNDID")));
                    return request;
                } else {
                    String error = "Error while creating refund request. Response from Paytm :::: " + JSONSerializer.toJSON(finalResponse);
                    LOG.error(error);
                    request.setPaymentStatus(PaymentStatus.REFUND_FAILED);
                    request.setRefundStatus(PaymentStatus.FAILED);
                }
            }

        } catch (Exception e){
            String error = "Error while creating refund request. Response from Paytm :::: " + JSONSerializer.toJSON(e);
            LOG.error(error);
            request.setPaymentStatus(PaymentStatus.REFUND_FAILED);
            request.setRefundStatus(PaymentStatus.FAILED);
        }
        return request;
    }

    public static String getJsonRequestObject(PaytmConfig env, OrderPayment orderPayment) throws JSONException {

        final String MID = "MID";
        final String ORDERID = "ORDERID";
        final String TXNID = "TXNID";
        final String REFUNDAMOUNT = "REFUNDAMOUNT";
        final String TXNTYPE = "TXNTYPE";
        final String REFID = "REFID";
        final String CHECKSUM = "CHECKSUM";

        TreeMap<String, String> requestObj = new TreeMap<>();
        requestObj.put(MID, env.getMid());
        requestObj.put(ORDERID, orderPayment.getExternalOrderId());
        requestObj.put(TXNID, orderPayment.getPartnerTransactionId());
        requestObj.put(REFUNDAMOUNT, String.valueOf(orderPayment.getTransactionAmount().setScale(2, RoundingMode.HALF_UP)));
        requestObj.put(TXNTYPE, "REFUND");
        requestObj.put(REFID, "CHAA" + orderPayment.getOrderPaymentDetailId());

        try {
            String checkSum = CheckSumServiceHelper.getCheckSumServiceHelper().genrateCheckSum(env.getMerchantKey(),
                    requestObj);
            JSONObject jsonRequestObj = new org.json.JSONObject();
            jsonRequestObj.put(MID, requestObj.get(MID));
            jsonRequestObj.put(ORDERID, requestObj.get(ORDERID));
            jsonRequestObj.put(TXNID, requestObj.get(TXNID));
            jsonRequestObj.put(REFUNDAMOUNT, requestObj.get(REFUNDAMOUNT));
            jsonRequestObj.put(TXNTYPE, requestObj.get(TXNTYPE));
            jsonRequestObj.put(REFID, requestObj.get(REFID));
            jsonRequestObj.put(CHECKSUM, URLEncoder.encode(checkSum, "UTF-8"));

            return jsonRequestObj.toString();
        } catch (Exception e) {
            LOG.error("Error while creating checksum for Paytm", e);
        }
        return null;
    }

    @Override
    public PaytmCreateRequest createPayTMQRForKIOSK(OrderPaymentRequest order, PaytmCreateRequest request) throws Exception {
        env = env != null ? env : new PaytmConfig();
        JSONObject requestBody = buildKIOSKCreateQRRequestBody(order.getGenerateOrderId(),
                order.getPosId(), order.getPaidAmount().toPlainString(), true);
        Map<String, String> headerVariables = new HashMap<>();
        headerVariables.put("Content-Type", MediaType.APPLICATION_JSON.toString());

        StringBuilder endPoint = new StringBuilder(env.getKIOSKBaseUrl() + "/qr/create");

        try {
            HttpPost httpPost = new HttpPost(endPoint.toString());
            httpPost.setHeader("content-type", "application/json");
            HttpEntity httpEntity = new StringEntity(requestBody.toString());
            httpPost.setEntity(httpEntity);

            HttpResponse response = WebServiceHelper.postRequest(httpPost);
            if (HttpStatus.SC_OK == response.getStatusLine().getStatusCode()) {
                PaytmCreateQRResponse qRResponse = WebServiceHelper.convertResponse(response, PaytmCreateQRResponse.class);
                if (qRResponse.getPaytmCreateQRResponseBody() != null) {
                    String qrId = qRResponse.getPaytmCreateQRResponseBody().getQrCodeId();
                    ResultInfo resultInfo = qRResponse.getPaytmCreateQRResponseBody().getResultInfo();
                    if (resultInfo != null
                            && resultInfo.getResultStatus().equalsIgnoreCase("SUCCESS")
                            && StringUtils.isNotBlank(qrId)) {
                        LOG.info("Qr Code created: ", qrId);
                        request.setQrCodeId(qrId);
                    }
                }
            }
        } catch (Exception ex) {
            LOG.error("Exception is :: ", ex);
        }
        return request;
    }

    @Override
    public PaytmUpiQrResponse createPayTMUPIQR(OrderPaymentRequest order, PaytmUpiQrRequest request) throws Exception {
        env = env != null ? env : new PaytmConfig();
        JSONObject requestBody = buildCreateUPIQRRequestBody(order.getGenerateOrderId(),
                order.getPosId(), order.getPaidAmount().toPlainString(), true);
        Map<String, String> headerVariables = new HashMap<>();
        headerVariables.put("Content-Type", MediaType.APPLICATION_JSON.toString());

        StringBuilder endPoint = new StringBuilder(env.getKIOSKBaseUrl() + "/qr/create");
        PaytmUpiResponse qRResponse = new PaytmUpiResponse();
        try {
            HttpPost httpPost = new HttpPost(endPoint.toString());
            httpPost.setHeader("content-type", "application/json");
            HttpEntity httpEntity = new StringEntity(requestBody.toString());
            httpPost.setEntity(httpEntity);

            HttpResponse response = WebServiceHelper.postRequest(httpPost);
            if (HttpStatus.SC_OK == response.getStatusLine().getStatusCode()) {
                qRResponse = WebServiceHelper.convertResponse(response, PaytmUpiResponse.class);

                if (qRResponse.getPaytmCreateQRResponseBody() != null) {
                    return qRResponse.getPaytmCreateQRResponseBody();
                }
            }
        } catch (Exception ex) {
            LOG.error("Exception is :: ", ex);
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public PaytmPaymentStatus checkKIOSKPaytmQRPaymentStatus(String orderId) throws Exception {
        try {
            LOG.info("checkKIOSKPaytmQRPaymentStatus where orderId is:: "+ orderId);
            PaytmPaymentDetails paytmPaymentDetails = null;
            if(StringUtils.isNotBlank(orderId)){
                PaytmPaymentStatusResponse statusResponse = callPaytmToCheckPaymentStatus(orderId);
                if (statusResponse != null && StringUtils.isNotBlank(statusResponse.getStatus())) {
                    paytmPaymentDetails = paymentGatewayDao.getPaytmPaymentStatus(orderId);
                    if (paytmPaymentDetails == null) {
                        LOG.info("Paytm details Not found! New order");
                        LOG.info("New Paytm payment status received " + JSONSerializer.toJSON(statusResponse));
                        paytmPaymentDetails = buildPaytmPaymentDetails(orderId, statusResponse, null);
                        paytmPaymentDetails = paymentGatewayDao.savePaytmPaymentDetails(paytmPaymentDetails);
                        if (paytmPaymentDetails.getPaytmPaymentDetailId() != null) {
                            LOG.info("Paytm details saved successfully, status is " + paytmPaymentDetails.getStatus());
                            return getPaytmPaymentStatus(orderId, paytmPaymentDetails, paytmPaymentDetails.getStatus());
                        }
                    } else {
                        if (!paytmPaymentDetails.getStatus().equalsIgnoreCase(statusResponse.getStatus())) {
                            PaytmPaymentDetails latestPaytmPaymentDetails = buildPaytmPaymentDetails(orderId, statusResponse, paytmPaymentDetails);
                            paymentGatewayDao.updatePaytmPaymentDetails(latestPaytmPaymentDetails);
                        }
                        return getPaytmPaymentStatus(orderId, paytmPaymentDetails, statusResponse.getStatus());

                    }
                }
            }
        } catch (Exception ex) {
            LOG.error("Exception Occured while in checkKIOSKPaytmQRPaymentStatus");
            throw new PaymentFailureException("Could not make payTM payment.");
        }
        return null;
    }

    @Override
    public boolean refundKIOSKPaytmQRPaymentAmount(String orderId, BigDecimal amountToRefund,
                                                   String refundReason) throws Exception {
        try {
            PaytmPaymentDetails paytmPaymentDetails = paymentGatewayDao.getPaytmPaymentStatus(orderId);
            if (paytmPaymentDetails != null) {

                env = env != null ? env : new PaytmConfig();
                JSONObject requestBody = buildKIOSKRefundRequestBody(paytmPaymentDetails, amountToRefund, refundReason);

                StringBuilder endPoint = new StringBuilder(env.getKIOSKBaseUrl() + "/refund/HANDLER_INTERNAL/REFUND");

                HttpPost httpPost = new HttpPost(endPoint.toString());
                httpPost.setHeader("content-type", "application/json");
                HttpEntity httpEntity = new StringEntity(requestBody.toString());
                httpPost.setEntity(httpEntity);

                HttpResponse response = WebServiceHelper.postRequest(httpPost);
                if (HttpStatus.SC_OK == response.getStatusLine().getStatusCode()) {
                    PaytmRefundAmountResponse refundResponse = WebServiceHelper.convertResponse(response, PaytmRefundAmountResponse.class);
                    if (refundResponse != null) {
                        if (refundResponse.getStatus().equalsIgnoreCase("TXN_SUCCESS")) {
                            LOG.info("Refund successfuly done for orderId " + orderId);
                            return true;
                        } else {
                            LOG.info("Refund failed , response code: " + refundResponse.getResponseCode() + " response message:  " + refundResponse.getResponseMessage());
                            return false;
                        }
                    }
                }
            } else {
                LOG.info("No order details found with orderId " + orderId);
                return false;
            }
        } catch (Exception ex) {
            LOG.error("Exception Occured while in refundKIOSKPaytmQRPaymentAmount");
            throw new PaymentFailureException("Could not refund payTM amount.");
        }
        return false;
    }


    @Override
    public OrderPayment refundPaytmUpiQR(OrderPayment paymentRefundRequest) throws PaymentFailureException {
        try {
            PaytmPaymentDetails paytmPaymentDetails = paymentGatewayDao.getPaytmPaymentStatus(paymentRefundRequest.getExternalOrderId());
            if (paytmPaymentDetails != null) {

                env = env != null ? env : new PaytmConfig();
                JSONObject requestBody = buildKIOSKRefundRequestBody(paytmPaymentDetails, paymentRefundRequest.getTransactionAmount(), paymentRefundRequest.getRefundReason());

                StringBuilder endPoint = new StringBuilder(env.getKIOSKBaseUrl() + "/refund/HANDLER_INTERNAL/REFUND");

                HttpPost httpPost = new HttpPost(endPoint.toString());
                httpPost.setHeader("content-type", "application/json");
                HttpEntity httpEntity = new StringEntity(requestBody.toString());
                httpPost.setEntity(httpEntity);

                HttpResponse response = WebServiceHelper.postRequest(httpPost);
                if (HttpStatus.SC_OK == response.getStatusLine().getStatusCode()) {
                    PaytmRefundAmountResponse refundResponse = WebServiceHelper.convertResponse(response, PaytmRefundAmountResponse.class);
                    if (refundResponse != null) {
                        if (refundResponse.getStatus().equalsIgnoreCase("TXN_SUCCESS")) {
                            paymentRefundRequest.setPaymentStatus(PaymentStatus.REFUND_PROCESSED);
                            paymentRefundRequest.setRefundStatus(PaymentStatus.SUCCESSFUL);
                            LOG.info("Refund successfuly done for orderId " + paytmPaymentDetails.getOrderId());
                        } else if (refundResponse.getStatus().equalsIgnoreCase("TXN_FAILURE")) {
                            LOG.info("Refund failed for orderId " + paytmPaymentDetails.getOrderId());
                            paymentRefundRequest.setPaymentStatus(PaymentStatus.REFUND_FAILED);
                            paymentRefundRequest.setRefundStatus(PaymentStatus.REFUND_FAILED);
                        } else if (refundResponse.getStatus().equalsIgnoreCase("PENDING")) {
                            LOG.info("Refund is in progress for orderId " + paytmPaymentDetails.getOrderId());
                            paymentRefundRequest.setPaymentStatus(PaymentStatus.REFUND_PROCESSED);
                            paymentRefundRequest.setRefundStatus(PaymentStatus.REFUND_INITIATED);
                        } else {
                            LOG.info("Refund failed , response code: " + refundResponse.getResponseCode() + " response message:  " + refundResponse.getResponseMessage());
                        }

                    }
                }
            } else {
                paymentRefundRequest.setPaymentStatus(PaymentStatus.REFUND_FAILED);
                paymentRefundRequest.setRefundStatus(PaymentStatus.REFUND_FAILED);
                LOG.info("No order details found with orderId " + paytmPaymentDetails.getOrderId());
                return paymentRefundRequest;
            }
        } catch (Exception ex) {

            LOG.error("Exception Occured while refunding PAYTM UPI payment");
            throw new PaymentFailureException("Could not refund payTM UPI amount.");
        }
        return paymentRefundRequest;
    }

    @Override
    public PaytmUpiQrResponse getPayTmUpiQRCodeId(OrderPaymentRequest order) throws PaymentFailureException {
        try {
            OrderPaymentDetail existingSuccessfulPayment = paymentGatewayDao
                    .getSuccessfulOrderPaymentDetail(order.getGenerateOrderId());
            if (existingSuccessfulPayment == null) {
                PaytmUpiQrRequest request = createPaytmUpiRequest(order);
                paymentGatewayDao.createRequest(request, order);
                return createPayTMUPIQR(order, request);
            } else {
                /**
                 * In case of a already existing successful payment we return null to the UI so
                 * that we can link the successful payment to the actual order
                 */
                return null;
            }
        } catch (Exception ex) {
            LOG.error("Exception Occurred ", ex);
            throw new PaymentFailureException("Could not create payTM UPI QR code.");
        }
    }

    @Override
    public PaytmCreateRequest getPayTMQRCodeIdForKIOSK(OrderPaymentRequest order) throws PaymentFailureException {
        try {
            OrderPaymentDetail existingSuceessfulPayment = paymentGatewayDao
                    .getSuccessfulOrderPaymentDetail(order.getGenerateOrderId());
            if (existingSuceessfulPayment == null) {
                PaytmCreateRequest request = createRequest(order);
                paymentGatewayDao.createRequest(request, order);
                createPayTMQRForKIOSK(order, request);
                return request;
            } else {
                /**
                 * In case of a already existing successful payment we return null to the UI so
                 * that we can link the successful payment to the actual order
                 */
                return null;
            }
        } catch (Exception ex) {
            LOG.error("Exception Occurred ", ex);
            throw new PaymentFailureException("Could not create payTM QR code.");
        }
    }

    @Override
    public OrderPayment checkPaytmPayment(OrderPaymentDetail paymentDetail) {

            try {
                PaytmCreateResponse paytmResponse = getPaymentStatus(paymentDetail.getExternalOrderId(),
                        paymentDetail.getTransactionAmount());
                if (paytmResponse != null) {
                    paymentDetail.setPartnerTransactionId(paytmResponse.getTransactionId());
                    paymentDetail.setUpdateTime(AppUtils.getCurrentTimestamp());
                    paymentDetail = paymentGatewayDao.save(paymentDetail);
                    paymentDetail.setPartnerPaymentStatus(paytmResponse.getStatus());
                    String paymentStatus = "TXN_SUCCESS".equals(paytmResponse.getStatus()) ?
                            PaymentStatus.SUCCESSFUL.name() : PaymentStatus.FAILED.name();
                    paymentDetail.setPaymentStatus(paymentStatus);
                    return DataConverter.convert(paymentDetail,
                            masterCache.getPaymentMode(paymentDetail.getPaymentModeId()));
                }
            } catch (PaymentFailureException e) {
                LOG.error("Error while paytm payment verification", e);
            } catch (IOException e) {
                LOG.error("Error while paytm payment verification", e);
            } catch (JSONException e) {
                LOG.error("Error while paytm payment verification", e);
            }
        return null;

    }

    @Override
    public Boolean checkPaytmPaymentStatus(OrderPaymentDetail paymentDetail) {
        OrderPayment payment = checkPaytmPayment(paymentDetail);
        if (payment != null && payment.getPaymentStatus() != null) {
            PaymentStatus status = payment.getPaymentStatus();
            if (status.equals(PaymentStatus.SUCCESSFUL)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public PaytmCreateRequest createPaytmRequest(OrderPaymentRequest orderPaymentRequest, Map<String, String> map) throws DataNotFoundException, PaymentFailureException {
        PaytmCreateRequest request = createRequest(orderPaymentRequest);
        LOG.info("::::::::::::::::: Paytm Request generated ::::::::::::::::");
        LOG.info(JSONSerializer.toJSON(request));
        paymentGatewayDao.createRequest(request, orderPaymentRequest);
        return request;
    }

    @Override
    public Map updatePaytmResponse(PaytmCreateResponse response) {
        boolean validation = validateResponse(response);
        return paymentGatewayDao.updateAndRedirect(response, validation);
    }

    @Override
    public OrderPayment refundPaytmUpiQRWrapper(OrderPayment paymentRefundRequest) throws PaymentFailureException {
        try {
            LOG.info("Refunding paytm payment amount for orderId " + paymentRefundRequest.getOrderId());
            return refundPaytmUpiQR(paymentRefundRequest);
        } catch (Exception ex) {
            LOG.error("Exception Occurred ", ex);
            throw new PaymentFailureException("Could not refund payTM amount.");
        }
    }

    @Override
    public Map updatePaytmUpiStatus(PaytmUpiS2SResponse response) {
        Map map = paymentGatewayDao.updateAndRedirect( response, response.getStatus().equals("SUCCESS"));
        if(map.containsKey("orderId")){
            PushNotification<Map<String, PaymentResponse>> notification = new PaytmUpiResponseNotification(env.getEnvironmentType(), response, response);
            pubnubService.sendNotification(notification);
        }
        return map;
    }

    private JSONObject buildKIOSKRefundRequestBody(PaytmPaymentDetails paytmPaymentDetails,
                                                   BigDecimal amountToRefund, String refundReason) {
        JSONObject requestBody = new JSONObject();
        try {
            requestBody.put("MID", paytmPaymentDetails.getmId());
            requestBody.put("REFID", paytmPaymentDetails.getRefundId());
            requestBody.put("TXNID", paytmPaymentDetails.getTransactionId());
            requestBody.put("ORDERID", paytmPaymentDetails.getOrderId());
            requestBody.put("REFUNDAMOUNT", amountToRefund.toString());
            requestBody.put("TXNTYPE", "REFUND");

            String signature = CheckSumServiceHelper.getCheckSumServiceHelper()
                    .genrateCheckSum(env.getKIOSKMerchantKey(), requestBody.toString());

            requestBody.put("CHECKSUM", signature);
            requestBody.put("COMMENTS", refundReason);
        } catch (Exception e) {
            LOG.error("Error while creating checksum for Paytm", e);
        }
        return requestBody;
    }

    private PaytmPaymentStatus getPaytmPaymentStatus(String orderId, PaytmPaymentDetails paytmPaymentDetails, String status) throws URISyntaxException {
        if (paytmPaymentDetails.getStatus().equalsIgnoreCase("TXN_SUCCESS")) {
            updatePayTMPaymentResponse(paytmPaymentDetails, PaymentStatus.SUCCESSFUL);
        }
        if (paytmPaymentDetails.getStatus().equalsIgnoreCase("TXN_FAILURE")) {
            updatePayTMPaymentResponse(paytmPaymentDetails, PaymentStatus.FAILED);
        }
        PaytmPaymentStatus paytmPaymentStatus = new PaytmPaymentStatus();
        paytmPaymentStatus.setOrderId(orderId);
        paytmPaymentStatus.setPaymentStatus(status);
        return paytmPaymentStatus;
    }

    private PaytmPaymentDetails buildPaytmPaymentDetails(String orderId,
                                                         PaytmPaymentStatusResponse statusResponse, PaytmPaymentDetails paytmPaymentDetailsInDB) {
        PaytmPaymentDetails paytmPaymentDetails = null;
        if (paytmPaymentDetailsInDB != null) {
            paytmPaymentDetails = paytmPaymentDetailsInDB;
            paytmPaymentDetailsInDB.setPaytmPaymentDetailId(paytmPaymentDetailsInDB.getPaytmPaymentDetailId());
        } else {
            paytmPaymentDetails = new PaytmPaymentDetails();
        }
        paytmPaymentDetails.setStatus(statusResponse.getStatus());
        paytmPaymentDetails.setResponseCode(statusResponse.getResponseCode());
        paytmPaymentDetails.setResponseMessage(statusResponse.getResponseMessage());
        paytmPaymentDetails.setTransactionType(statusResponse.getTransactionType());
        paytmPaymentDetails.setBankTransactionId(statusResponse.getBankTransactionId());
        paytmPaymentDetails.setTransactionId(statusResponse.getTransactionId());
        paytmPaymentDetails.setOrderId(orderId);
        paytmPaymentDetails.setTransactionAmount(new BigDecimal(statusResponse.getTransactionAmount()));
        paytmPaymentDetails.setmId(statusResponse.getmId());
        paytmPaymentDetails.setRefundAmount(new BigDecimal(statusResponse.getRefundAmount()));
        paytmPaymentDetails.setRefundId(UUID.randomUUID().toString());
        paytmPaymentDetails.setIsAmountRefunded(AppUtils.NO);
        paytmPaymentDetails.setTransactionDate(AppUtils.getDate(new Date()));
        paytmPaymentDetails.setUpdatedTime(AppUtils.getDate(new Date()));
        return paytmPaymentDetails;
    }

    private PaytmPaymentStatusResponse callPaytmToCheckPaymentStatus(String orderId) throws Exception {
        env = env != null ? env : new PaytmConfig();
        JSONObject jsonRequestBody = new JSONObject();
        jsonRequestBody.put("MID", env.getKIOSKMid());
        jsonRequestBody.put("ORDERID", orderId);
        String signature = CheckSumServiceHelper.getCheckSumServiceHelper()
                .genrateCheckSum(env.getKIOSKMerchantKey(), jsonRequestBody.toString());
        jsonRequestBody.put("CHECKSUMHASH", signature);

        String url = CHECK_PAYTM_PAYMENT_STATUS_ENDPOINT + "?JsonData=";
        String method = "POST";

        PaytmPaymentStatusResponse statusResponse = sendRequest(url, method, env.getKIOSKBaseUrl(),
                jsonRequestBody.toString(), PaytmPaymentStatusResponse.class);
        return statusResponse;
    }

    private JSONObject buildKIOSKCreateQRRequestBody(String orderId,
                                                     String kioskDeviceId,
                                                     String amount,
                                                     boolean imageRequired) {
        JSONObject requestBody = new JSONObject();
        JSONObject headJSON = new JSONObject();
        JSONObject bodyJSON = new JSONObject();
        try {
            Timestamp timestamp = new Timestamp(System.currentTimeMillis());

            bodyJSON.put("mid", env.getKIOSKMid());
            bodyJSON.put("orderId", orderId);
            bodyJSON.put("amount", amount);
            bodyJSON.put("businessType", env.getKIOSKBusinessType());
            bodyJSON.put("posId", kioskDeviceId);
            bodyJSON.put("imageRequired", imageRequired);
            bodyJSON.put("displayName", "Paytm");
            bodyJSON.put("orderDetails", "true");

            String signature = CheckSumServiceHelper.getCheckSumServiceHelper()
                    .genrateCheckSum(env.getKIOSKMerchantKey(), bodyJSON.toString());

            headJSON.put("clientId", env.getKIOSKClientId());
            headJSON.put("version", env.getKIOSKVersion());
            headJSON.put("requestTimestamp", Long.toString(timestamp.getTime()));
            headJSON.put("channelId", env.getKIOSKChannelId());
            headJSON.put("signature", signature);
            requestBody.put("head", headJSON);
            requestBody.put("body", bodyJSON);
        } catch (Exception e) {
            LOG.error("Error while creating checksum for Paytm", e);
        }
        return requestBody;
    }

    private JSONObject buildCreateUPIQRRequestBody(String orderId,
                                                   String kioskDeviceId,
                                                   String amount,
                                                   boolean imageRequired) {
        JSONObject requestBody = new JSONObject();
        JSONObject headJSON = new JSONObject();
        JSONObject bodyJSON = new JSONObject();
        try {
            Timestamp timestamp = new Timestamp(System.currentTimeMillis());

            bodyJSON.put("mid", env.getKIOSKMid());
            bodyJSON.put("orderId", orderId);
            bodyJSON.put("amount", amount);
            bodyJSON.put("businessType", env.getPaytmUpiBusinessType());
            bodyJSON.put("posId", kioskDeviceId);
            bodyJSON.put("displayName", "Paytm");
            bodyJSON.put("orderDetails", "true");
            bodyJSON.put("invoiceDetails", "true");

            String signature = CheckSumServiceHelper.getCheckSumServiceHelper()
                    .genrateCheckSum(env.getKIOSKMerchantKey(), bodyJSON.toString());

            headJSON.put("clientId", env.getKIOSKClientId());
            headJSON.put("version", env.getKIOSKVersion());
            headJSON.put("requestTimestamp", Long.toString(timestamp.getTime()));
            headJSON.put("channelId", env.getKIOSKChannelId());
            headJSON.put("signature", signature);
            requestBody.put("head", headJSON);
            requestBody.put("body", bodyJSON);
        } catch (Exception e) {
            LOG.error("Error while creating checksum for Paytm", e);
        }
        return requestBody;
    }

    private <T> T sendRequest(String hitUrl, String method, String baseUrl, String body, Class<T> output) {
        HttpURLConnection connection = null;
        T finalResponse = null;
        try {
            StringBuilder endPoint = new StringBuilder(baseUrl + hitUrl);
            endPoint.append(body); // Appending json data to request param
            // Create connection
            final URL url = new URL(endPoint.toString());
            connection = (HttpURLConnection) url.openConnection(Proxy.NO_PROXY);
            connection.setRequestMethod(method);
            connection.setConnectTimeout(60000);
            connection.setUseCaches(false);
            connection.setDoInput(true);
            connection.setDoOutput(true);
            // Get Response
            final InputStream is = connection.getInputStream();
            final BufferedReader rd = new BufferedReader(new InputStreamReader(is));
            String line;
            StringBuilder response = new StringBuilder();
            while ((line = rd.readLine()) != null) {
                response.append(line);
                response.append('\n');
            }
            rd.close();
            finalResponse = WebServiceHelper.convert(response.toString(), output);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
        return finalResponse;
    }

    private PaytmCreateRequest createRequestObject(String generatedOrderId, BigDecimal paidAmount,
                                                   String contactNumber, String callbackUrl) {
        PaytmCreateRequest object = new PaytmCreateRequest(generatedOrderId, paidAmount, contactNumber);
        object.setChannelId(env.getChannelId());
        object.setCallbackUrl(callbackUrl);
        object.setIndustryTypeId(env.getIndustryTypeId());
        object.setMid(env.getMid());
        object.setWebsite(env.getWebsite());
        return object;
    }

    private boolean updatePayTMPaymentResponse(PaytmPaymentDetails payTMPaymentDetails,
                                               PaymentStatus status) throws URISyntaxException {
        if (payTMPaymentDetails != null) {

            HashMap<String, String> parameters = new HashMap<String, String>();
            parameters.put("TXNID", payTMPaymentDetails.getTransactionId());
            parameters.put("BANKTXNID", payTMPaymentDetails.getBankTransactionId());
            parameters.put("ORDERID", payTMPaymentDetails.getOrderId());
            parameters.put("TXNAMOUNT", payTMPaymentDetails.getTransactionAmount().toString());
            parameters.put("STATUS", payTMPaymentDetails.getStatus());
            parameters.put("TXNTYPE", payTMPaymentDetails.getTransactionType());
            parameters.put("RESPCODE", payTMPaymentDetails.getResponseCode());
            parameters.put("RESPMSG", payTMPaymentDetails.getResponseMessage());
            parameters.put("MID", payTMPaymentDetails.getmId());
            parameters.put("REFUNDAMT", payTMPaymentDetails.getRefundAmount().toString());
            parameters.put("TXNDATE", payTMPaymentDetails.getTransactionDate().toString());

            paymentGatewayDao.updatePayTMResponse(status, payTMPaymentDetails.getOrderId(),
                    payTMPaymentDetails.getTransactionId(), payTMPaymentDetails.getStatus(), parameters);
        }

        return true;
    }

    private PaytmUpiQrRequest createRequestObjectForPaytmUpi(OrderPaymentRequest order) {
        PaytmUpiQrRequest object = new PaytmUpiQrRequest();
        object.setMid(env.getKIOSKMid());
        object.setOrderId(order.getGenerateOrderId());
        object.setAmount(order.getPaidAmount());
        object.setBusinessType(env.getPaytmUpiBusinessType());
        object.setPosId(order.getPosId());
        object.setOrderDetails("true");
        object.setInvoiceDetails("true");
        return object;
    }

    public PaytmUpiQrRequest createPaytmUpiRequest(OrderPaymentRequest order)
            throws PaymentFailureException, DataNotFoundException {
        PaytmUpiQrRequest request = createRequestObjectForPaytmUpi(order);
        String checkSum;
        try {
            TreeMap<String, String> parameters = request.getParameters(env.getPaytmUpiBusinessType());
            checkSum = checkSumServiceHelper.genrateCheckSum(env.getMerchantKey(), parameters);
            LOG.info("Generated checksum for Paytm UPI QR ::::::::: {}", checkSum);
        } catch (Exception e) {
            LOG.error("Exception while generating checksum for Paytm UPI QR request :::::: {}", order.getCartId(), e);
            throw new PaymentFailureException("Error in generating checksum for Paytm UPI QR of order " + order.getGenerateOrderId(), e);
        }
		request.setCheckSumHash(checkSum);
        return request;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRES_NEW)
    public Boolean syncOrderAndInitiatePaytmRefundInBulk(List<OrderPaymentDetail> opdList) {
        return paymentGatewayDao.syncOrderAndInitiatePaytmRefundInBulk(opdList);
    }

}
