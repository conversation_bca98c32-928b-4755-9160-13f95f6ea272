/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

import static javax.persistence.GenerationType.IDENTITY;

// Generated 19 Aug, 2015 4:03:09 PM by Hibernate Tools 4.0.0

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * ReportExecutionParams generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "REPORT_EXECUTION_PARAMS")
public class ReportExecutionParams implements java.io.Serializable {

	private Integer executionParamsId;
	private ReportParams reportParams;
	private ReportExecutionDetail reportExecutionDetail;
	private String stringValue;
	private Integer integerValue;
	private BigDecimal doubleValue;
	private String booleanValue;
	private Date dateValue;
	private Date timestampValue;

	public ReportExecutionParams() {
	}

	public ReportExecutionParams(ReportParams reportParams, ReportExecutionDetail reportExecutionDetail) {
		this.reportParams = reportParams;
		this.reportExecutionDetail = reportExecutionDetail;
	}

	public ReportExecutionParams(ReportParams reportParams, ReportExecutionDetail reportExecutionDetail,
			String stringValue, Integer integerValue, BigDecimal doubleValue, String booleanValue, Date dateValue,
			Date timestampValue) {
		this.reportParams = reportParams;
		this.reportExecutionDetail = reportExecutionDetail;
		this.stringValue = stringValue;
		this.integerValue = integerValue;
		this.doubleValue = doubleValue;
		this.booleanValue = booleanValue;
		this.dateValue = dateValue;
		this.timestampValue = timestampValue;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "EXECUTION_PARAMS_ID", unique = true, nullable = false)
	public Integer getExecutionParamsId() {
		return this.executionParamsId;
	}

	public void setExecutionParamsId(Integer executionParamsId) {
		this.executionParamsId = executionParamsId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "REPORT_PARAMS_ID", nullable = false)
	public ReportParams getReportParams() {
		return this.reportParams;
	}

	public void setReportParams(ReportParams reportParams) {
		this.reportParams = reportParams;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "EXECUTION_DETAIL_ID", nullable = false)
	public ReportExecutionDetail getReportExecutionDetail() {
		return this.reportExecutionDetail;
	}

	public void setReportExecutionDetail(ReportExecutionDetail reportExecutionDetail) {
		this.reportExecutionDetail = reportExecutionDetail;
	}

	@Column(name = "STRING_VALUE")
	public String getStringValue() {
		return this.stringValue;
	}

	public void setStringValue(String stringValue) {
		this.stringValue = stringValue;
	}

	@Column(name = "INTEGER_VALUE")
	public Integer getIntegerValue() {
		return this.integerValue;
	}

	public void setIntegerValue(Integer integerValue) {
		this.integerValue = integerValue;
	}

	@Column(name = "DOUBLE_VALUE", precision = 10)
	public BigDecimal getDoubleValue() {
		return this.doubleValue;
	}

	public void setDoubleValue(BigDecimal doubleValue) {
		this.doubleValue = doubleValue;
	}

	@Column(name = "BOOLEAN_VALUE", length = 1)
	public String getBooleanValue() {
		return this.booleanValue;
	}

	public void setBooleanValue(String booleanValue) {
		this.booleanValue = booleanValue;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "DATE_VALUE", length = 10)
	public Date getDateValue() {
		return this.dateValue;
	}

	public void setDateValue(Date dateValue) {
		this.dateValue = dateValue;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "TIMESTAMP_VALUE", length = 19)
	public Date getTimestampValue() {
		return this.timestampValue;
	}

	public void setTimestampValue(Date timestampValue) {
		this.timestampValue = timestampValue;
	}

}
