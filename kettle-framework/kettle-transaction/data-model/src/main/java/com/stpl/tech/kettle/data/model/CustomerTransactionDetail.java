package com.stpl.tech.kettle.data.model;

import javax.annotation.Generated;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "CUSTOMER_TRANSACTION_DATA")
public class CustomerTransactionDetail {

    private Integer id;
    private Integer customerId;
    private Integer brandId;
    private Integer firstOrderId;
    private String firstOrderSource;
    private Integer firstOrderChannelPartnerId;
    private Date firstOrderBusinessDate;
    private Integer firstOrderUnitId;
    private Integer lastOrderId;
    private String lastOrderSource;
    private Integer lastOrderChannelPartnerId;
    private Date lastOrderBusinessDate;
    private Integer lastOrderUnitId;
    private Integer totalDineInOrders;
    private Integer totalDeliveryOrders;
    private Integer totalOrders;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "CUSTOMER_TRANSACTION_ID")
    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    @Column(name = "CUSTOMER_ID")
    public Integer getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    @Column(name = "BRAND_ID")
    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    @Column(name = "FIRST_ORDER_ID")
    public Integer getFirstOrderId() {
        return firstOrderId;
    }

    public void setFirstOrderId(Integer firstOrderId) {
        this.firstOrderId = firstOrderId;
    }

    @Column(name = "FIRST_ORDER_SOURCE")
    public String getFirstOrderSource() {
        return firstOrderSource;
    }

    public void setFirstOrderSource(String firstOrderSource) {
        this.firstOrderSource = firstOrderSource;
    }

    @Column(name = "FIRST_CHANNEL_PARTNER_ID")
    public Integer getFirstOrderChannelPartnerId() {
        return firstOrderChannelPartnerId;
    }

    public void setFirstOrderChannelPartnerId(Integer firstOrderChannelPartnerId) {
        this.firstOrderChannelPartnerId = firstOrderChannelPartnerId;
    }

    @Column(name = "FIRST_BUSINESS_DATE")
    public Date getFirstOrderBusinessDate() {
        return firstOrderBusinessDate;
    }

    public void setFirstOrderBusinessDate(Date firstOrderBusinessDate) {
        this.firstOrderBusinessDate = firstOrderBusinessDate;
    }

    @Column(name = "FIRST_UNIT_ID")
    public Integer getFirstOrderUnitId() {
        return firstOrderUnitId;
    }

    public void setFirstOrderUnitId(Integer firstOrderUnitId) {
        this.firstOrderUnitId = firstOrderUnitId;
    }

    @Column(name = "LAST_ORDER_ID")
    public Integer getLastOrderId() {
        return lastOrderId;
    }

    public void setLastOrderId(Integer lastOrderId) {
        this.lastOrderId = lastOrderId;
    }

    @Column(name = "LAST_ORDER_SOURCE")
    public String getLastOrderSource() {
        return lastOrderSource;
    }

    public void setLastOrderSource(String lastOrderSource) {
        this.lastOrderSource = lastOrderSource;
    }

    @Column(name = "LAST_CHANNEL_PARTNER_ID")
    public Integer getLastOrderChannelPartnerId() {
        return lastOrderChannelPartnerId;
    }

    public void setLastOrderChannelPartnerId(Integer lastOrderChannelPartnerId) {
        this.lastOrderChannelPartnerId = lastOrderChannelPartnerId;
    }

    @Column(name = "LAST_BUSINESS_DATE")
    public Date getLastOrderBusinessDate() {
        return lastOrderBusinessDate;
    }

    public void setLastOrderBusinessDate(Date lastOrderBusinessDate) {
        this.lastOrderBusinessDate = lastOrderBusinessDate;
    }

    @Column(name = "LAST_UNIT_ID")
    public Integer getLastOrderUnitId() {
        return lastOrderUnitId;
    }

    public void setLastOrderUnitId(Integer lastOrderUnitId) {
        this.lastOrderUnitId = lastOrderUnitId;
    }

    @Column(name = "TOTAL_DINE_IN_ORDERS")
    public Integer getTotalDineInOrders() {
        return totalDineInOrders;
    }

    public void setTotalDineInOrders(Integer totalDineInOrders) {
        this.totalDineInOrders = totalDineInOrders;
    }

    @Column(name = "TOTAL_DELIVERY_ORDERS")
    public Integer getTotalDeliveryOrders() {
        return totalDeliveryOrders;
    }

    public void setTotalDeliveryOrders(Integer totalDeliveryOrders) {
        this.totalDeliveryOrders = totalDeliveryOrders;
    }

    @Column(name = "TOTAL_ORDERS")
    public Integer getTotalOrders() {
        return totalOrders;
    }

    public void setTotalOrders(Integer totalOrders) {
        this.totalOrders = totalOrders;
    }
}
