/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core;

import java.math.MathContext;
import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;

import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.domain.model.SettlementType;
import com.stpl.tech.kettle.domain.model.SubscriptionStatus;

public class TransactionConstants {

	/**
	 * Authentication
	 */
	public static final String ALGORITHM = "AES";
	public static final String PASSPHRASE_KEY = "C4@@y05a^3)H-5uN";

	/**
	 * Date formatters
	 */
	public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormat.forPattern("yyyy-MM-dd");
	public static final DateTimeFormatter MONTH_FORMATTER = DateTimeFormat.forPattern("yyyy-MM");
	public static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss.SSS");
	public static final DateTimeFormatter DATE_TIME_WITH_NO_MILISECOND_FORMATTER = DateTimeFormat
			.forPattern("yyyy-MM-dd HH:mm:ss");
	public static final DateTimeFormatter DATE_TIME_WITH_ONLY_TIME_FORMATTER = DateTimeFormat.forPattern("HH:mm");
	public static final DateTimeFormatter DATE_TIME_FORMATTER_WITH_NO_CHARACTERS = DateTimeFormat
			.forPattern("yyyyMMddHHmmss");
	public static final DateTimeFormatter DATE_TIME_ISO_FORMATTER = DateTimeFormat.forPattern("yyyy-MM-dd'T'HH:mmZ");

	/**
	 * Reference lookup related
	 */
	public static final String RTL_GROUP_CATEGORY = "CATEGORY";
	public static final String RTL_GROUP_DIMENSION = "DIMENSION";
	public static final String RTL_GROUP_ADDONS = "ADDONS";
	public static final String RTL_GROUP_DISCOUNT = "DISCOUNT";
	public static final String RTL_GROUP_COMPLIMENTARY = "COMPLIMENTARY";
	public static final String RTL_CODE_DISCOUNT_CODE = "DiscountCode";
	public static final String RTL_CODE_COMPLIMENTARY_CODE = "ComplimentaryCode";
	public static final String RTL_CODE_DEFAULT_DISCOUNT_CODE = "Other";
	public static final String RTL_CODE_DEFAULT_COMPLIMENTARY_CODE = "Any Other";

	/**
	 * Conversion Constants
	 */
	public static final String YES = "Y";
	public static final String NO = "N";
	public static final String ALL = "All";
	public static final int GET_FOR_ALL = -1;

	public static final String getValue(boolean value) {
		return value ? YES : NO;
	}

	public static final boolean getValue(String value) {
		return value == null ? false : YES.equals(value);
	}

	/**
	 * Defaults
	 */
	public static final String DEFAULT_HAS_PARCEL = NO;
	public static final SettlementType DEFAULT_SETTLEMENT_TYPE = SettlementType.DEBIT;
	public static final OrderStatus DEFAULT_ORDER_STATUS = OrderStatus.CREATED;
	public static final SubscriptionStatus DEFAULT_SUBSCRIPTION_STATUS = SubscriptionStatus.CREATED;
	public static final String DEFAULT_IS_COMPLIMENTARY = NO;
	public static final String DEFAULT_IS_NUMBER_VERIFIED = NO;
	public static final String DEFAULT_IS_EMAIL_VERIFIED = NO;
	public static final String DEFAULT_COUNTRY_CODE = "+91";
	public static final String DEFAULT_ADDRESS_TYPE = "RESEDENTIAL";
	public static final String DEFAULT_HAS_MULTIPLE_SECTION = NO;
	public static final String DEFAULT_AUTO_TRIGGERED = YES;
	public static final String DEFAULT_PASSCODE = "123123";

	public static final String getValue(String value, String defaultIfNull) {
		return value == null || "".equals(value.trim()) ? defaultIfNull : value;
	}

	/**
	 * Application Constants
	 */
	public static final int DINE_IN_CHANNEL_PARTNER = 1;
	public static final int NO_DIMENSION_CODE = 1;
	public static final String NO_DIMENSION_STRING = "None";
	public static final int VERIFICATION_LOYALTY_POINT = 60;
	public static final int CUTOFF_LOYALTY_POINTS = 300;
	public static final String PARCEL_CODE = "Parcel";
	public static final MathContext MATH_CONTEXT = new MathContext(10);
	public static final String CHAAYOS_RECEIPT = "Chaayos Receipt";
	public static final String CHAAYOS_SUBSCRIPTION = "Chaayos Subscription";
	public static final String CHAAYOS_COD = "Chaayos-COD";
	public static final String MARKETING_PARTNER = "Marketing-Partner";
	public static final int CHAAYOS_COMBO_PRODUCT_TYPE = 8;
	public static final int BEVERAGE_HOT_PRODUCT_TYPE = 5;
	public static final int BEVERAGE_COLD_PRODUCT_TYPE = 6;
	public static final int FOOD_PRODUCT_TYPE = 7;
	public static final String BEVERAGE_PRODUCT_TYPE_STR = "BEVERAGE";
	public static final String FOOD_PRODUCT_TYPE_STR = "FOOD";
	public static final Set<Integer> ADMIN_DEPARTMENTS = new HashSet<Integer>(Arrays.asList(102, 105));
	public static final int ADMIN_USER_ID = 100000;
	public static final int EMPLOYEE_MEAL_ID = 2100;

	public static boolean isDineIn(int channelPartnerId) {
		return channelPartnerId == DINE_IN_CHANNEL_PARTNER;
	}

	public static final String CAFE_BILL_PRINT_TEMPLATE = "template/OrderPrint.html";
	public static final String COD_BILL_PRINT_TEMPLATE = "template/OrderCODPrint.html";
	public static final String COD_BILL_PUNCH_TEMPLATE = "template/OrderCODPunch.html";

	/**
	 * Web Socket based configurations
	 */
	public static final String WEB_SOCKET_CHANNEL = "/ws-channel/";
	public static final String WEB_SOCKET_CHANNEL_ORDERS = "/orders";
	public static final String ORDER_QUEUE_CHANNEL = "/order-queue";
	public static final String UPDATE_ORDERS = "/updateOrder";

	/**
	 * Unit Status
	 */
	public static final String ACTIVE = "ACTIVE";
	public static final String IN_ACTIVE = "IN_ACTIVE";

	/**
	 * Complimentary Eligibility
	 */
	public static final String ACCOUNTABLE = "ACCOUNTABLE";
	public static final String NOT_ACCOUNTABLE = "NOT_ACCOUNTABLE";

	/**
	 * Delivery Partner
	 */
	public static final int DELIVERY_REQUEST_API_TIMEOUT = 10;
	public static final int DELIVERY_PARTNER_AUTOMATED = -1;
	public static final int DELIVERY_PARTNER_PICKUP = 5;
	public static final int DELIVERY_PARTNER_NONE = 1;
	public static final int TAKEAWAY_SEED = 100;

	/**
	 * Offer and Coupon Codes
	 */
	public static final String HQ_OFFER_CODE = "EMP35";
	public static final String SIGNUP_OFFER_CODE = "LOYALTEA";
	public static final String DOMAIN_NAME = "chaayos.com";
	public static final int MARKETING_VOUCHER_ID = 2004;

	/**
	 * Email of the Service Account
	 */
	public static final String SERVICE_ACCOUNT_ACCOUNT_EMAIL = "<EMAIL>";
	public static final String MIMETYPE_GOOGLE_SHEETS = "application/vnd.google-apps.spreadsheet";
	public static final String PREFIX_EXPENSE_REPORT = "expense";
	public static final String PREFIX_EXPENSE_REPORT_OUTPUT = "expense_output";
	public static final String APP_NAME_EXPENSE_REPORT = "scoreboard";
	public static final String MIMETYPE_GOOGLE_FOLDER = "application/vnd.google-apps.folder";
	public static final String FOLDER_EXPENSE_OUTPUT_WOW = "WOW-Output";
	public static final String FOLDER_EXPENSE_OUTPUT_MOM = "MOM-Output";
	public static final String FOLDER_EXPENSE_OUTPUT_PARENT = "expense-output";

	/**
	 * Channel Partners
	 */
	public static final String CHANNEL_PARTNER_CHAAYOS_TAKE_AWAY = "CHAAYOS_TAKE_AWAY";
	public static final String CHANNEL_PARTNER_CHAAYOS = "CHAAYOS";

	/**
	 * Transaction Strings
	 */
	public static final String ROUND_OFF = "Round Off";
	public static final String EXTRA_VOUCHERS = "Extra Vouchers";
	public static final Set<Integer> CASH_CARD_PRODUCT_IDS = new HashSet<>(Arrays.asList(1026, 1027, 1048, 1056));

	/**
	 * Payment Modes
	 */
	public static final int PAYMENT_MODE_CASH_CARD = 10;
	public static final int PAYMENT_MODE_RAZOR_PAY = 12;
	public static final int PAYMENT_MODE_PAYTM = 13;
	public static final String PAYMENT_MODE_CREDIT = "Credit";
	
	/**
	 * Payment Status
	 */
	public static final String PAYMENT_STATUS_CAPTURED = "CAPTURED";
	public static final String PAYMENT_STATUS_SUCCESSFUL = "SUCCESSFUL";
	public static final String QR_CODE_HEARDER = "Scan With Any QR Scanner And Earn Loyalty Benefits";
}
