/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2020] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@SuppressWarnings("serial")
@Entity
@Table(name = "PARTNER_ORDER_DISCOUNT_MAPPING")
public class PartnerOrderDiscountMapping implements java.io.Serializable {

	private Integer orderDiscountMappingId;
	private OrderDetail orderDetail;
	private Integer brandId;
	private String partnerName;
	private String discountName;
	private String discountType;
	private String discountCategory;
	private BigDecimal discountValue;
	private BigDecimal discountAmount;
	private String discountIsTaxed;
	private BigDecimal discountAppliedOn;
	private String voucherCode;
	private String isPartnerDiscount;
	
	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "ORDER_DISCOUNT_MAPPING_ID", unique = true, nullable = false)
	public Integer getOrderDiscountMappingId() {
		return orderDiscountMappingId;
	}
	public void setOrderDiscountMappingId(Integer orderDiscountMappingId) {
		this.orderDiscountMappingId = orderDiscountMappingId;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "ORDER_ID", nullable = false)
	public OrderDetail getOrderDetail() {
		return orderDetail;
	}
	public void setOrderDetail(OrderDetail orderDetail) {
		this.orderDetail = orderDetail;
	}
	
	@Column(name = "BRAND_ID", nullable = true)
	public Integer getBrandId() {
		return brandId;
	}
	public void setBrandId(Integer brandId) {
		this.brandId = brandId;
	}
	
	@Column(name = "PARTNER_NAME", nullable = true)
	public String getPartnerName() {
		return partnerName;
	}
	public void setPartnerName(String partnerName) {
		this.partnerName = partnerName;
	}
	
	@Column(name = "DISCOUNT_NAME", nullable = true)
	public String getDiscountName() {
		return discountName;
	}
	public void setDiscountName(String discountName) {
		this.discountName = discountName;
	}
	
	@Column(name = "DISCOUNT_TYPE", nullable = true)
	public String getDiscountType() {
		return discountType;
	}
	public void setDiscountType(String discountType) {
		this.discountType = discountType;
	}
	
	@Column(name = "DISCOUNT_CATEGORY", nullable = true)
	public String getDiscountCategory() {
		return discountCategory;
	}
	public void setDiscountCategory(String discountCategory) {
		this.discountCategory = discountCategory;
	}
	
	@Column(name = "DISCOUNT_VALUE", nullable = true)
	public BigDecimal getDiscountValue() {
		return discountValue;
	}
	public void setDiscountValue(BigDecimal discountValue) {
		this.discountValue = discountValue;
	}
	
	@Column(name = "DISCOUNT_AMOUNT", nullable = true)
	public BigDecimal getDiscountAmount() {
		return discountAmount;
	}
	public void setDiscountAmount(BigDecimal discountAmount) {
		this.discountAmount = discountAmount;
	}
	
	@Column(name = "DISCOUNT_TAXED", nullable = true)
	public String getDiscountIsTaxed() {
		return discountIsTaxed;
	}
	public void setDiscountIsTaxed(String discountIsTaxed) {
		this.discountIsTaxed = discountIsTaxed;
	}
	
	@Column(name = "DISCOUNT_APPLIEDON", nullable = true)
	public BigDecimal getDiscountAppliedOn() {
		return discountAppliedOn;
	}
	public void setDiscountAppliedOn(BigDecimal discountAppliedOn) {
		this.discountAppliedOn = discountAppliedOn;
	}
	
	@Column(name = "VOUCHER_CODE", nullable = true)
	public String getVoucherCode() {
		return voucherCode;
	}
	public void setVoucherCode(String voucherCode) {
		this.voucherCode = voucherCode;
	}
	
	@Column(name = "IS_PARTNER_DISCOUNT", nullable = true)
	public String getIsPartnerDiscount() {
		return isPartnerDiscount;
	}
	public void setIsPartnerDiscount(String isPartnerDiscount) {
		this.isPartnerDiscount = isPartnerDiscount;
	}

}
