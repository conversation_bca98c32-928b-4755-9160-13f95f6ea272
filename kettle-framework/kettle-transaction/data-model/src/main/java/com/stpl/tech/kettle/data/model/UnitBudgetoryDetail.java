/*
 * SUNSHINE TEAHOUSE @ExcelField private LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse @ExcelField private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse @ExcelField private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse @ExcelField private Limited
 * and its suppliers, and are @ExcelField private by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse @ExcelField private Limited.
 */
package com.stpl.tech.kettle.data.model;

import com.stpl.tech.util.excelparser.annotations.ExcelField;
import com.stpl.tech.util.excelparser.annotations.ExcelObject;
import com.stpl.tech.util.excelparser.annotations.ExcelSheet;
import com.stpl.tech.util.excelparser.annotations.ParseType;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.math.BigDecimal;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@ExcelSheet(value = "Unit Budget Detail")
@ExcelObject(parseType = ParseType.ROW, start = 1, end = 0)
@Entity
@Table(name = "UNIT_BUDGETORY_DETAIL")
public class UnitBudgetoryDetail {

    private int budgetDetailId;
    @ExcelField
    private int unitId;
    @ExcelField
    private String unitName;
    @ExcelField
    private int year;
    @ExcelField
    private int month;
    @ExcelField
    private String status;

    @ExcelField(headerName = "TOTAL_TICKET")
    private Integer ticket;
    @ExcelField(headerName = "TOTAL_SALES")
    private BigDecimal sales;
    @ExcelField(headerName = "TOTAL_APC")
    private BigDecimal apc;
    @ExcelField(headerName = "TOTAL_GMV")
    private BigDecimal gmv;
    @ExcelField(headerName = "TOTAL_DISCOUNT")
    private BigDecimal discount;
    @ExcelField(headerName = "TOTAL_DISCOUNT_LOYAL_TEA")
    private BigDecimal discountLoyalty;
    @ExcelField(headerName = "TOTAL_DISCOUNT_MARKETING")
    private BigDecimal discountMarketing;
    @ExcelField(headerName = "TOTAL_DISCOUNT_OPS")
    private BigDecimal discountOps;
    @ExcelField(headerName = "TOTAL_DISCOUNT_BD")
    private BigDecimal discountBd;
    @ExcelField(headerName = "TOTAL_DISCOUNT_EMPLOYEE_FICO")
    private BigDecimal discountEmployeeFico;

    @ExcelField
    private Integer dineInTicket;
    @ExcelField
    private BigDecimal dineInSales;
    @ExcelField
    private BigDecimal dineInApc;
    @ExcelField
    private BigDecimal dineInGmv;
    @ExcelField
    private BigDecimal dineInDiscount;
    @ExcelField(headerName = "DINE_IN_DISCOUNT_LOYAL_TEA")
    private BigDecimal dineInDiscountLoyalty;
    @ExcelField
    private BigDecimal dineInDiscountMarketing;
    @ExcelField
    private BigDecimal dineInDiscountOps;
    @ExcelField
    private BigDecimal dineInDiscountBd;
    @ExcelField
    private BigDecimal dineInDiscountEmployeeFico;

    @ExcelField
    private Integer deliveryTicket;
    @ExcelField
    private BigDecimal deliverySales;
    @ExcelField
    private BigDecimal deliveryApc;
    @ExcelField
    private BigDecimal deliveryGmv;
    @ExcelField
    private BigDecimal deliveryDiscount;
    @ExcelField(headerName = "DELIVERY_DISCOUNT_LOYAL_TEA")
    private BigDecimal deliveryDiscountLoyalty;
    @ExcelField
    private BigDecimal deliveryDiscountMarketing;
    @ExcelField
    private BigDecimal deliveryDiscountOps;
    @ExcelField
    private BigDecimal deliveryDiscountBd;
    @ExcelField
    private BigDecimal deliveryDiscountEmployeeFico;

    @ExcelField(headerName = "GIFT_CARD_SALES")
    private BigDecimal giftCardSale;

    @ExcelField
    private BigDecimal dineInCogs;
    @ExcelField
    private BigDecimal deliveryCogs;
    @ExcelField
    private BigDecimal employeeMealCogs;

    @ExcelField(headerName = "VARIANCE_PCC")
    private BigDecimal variancePCC;
    @ExcelField(headerName = "VARIANCE_YC")
    private BigDecimal varianceYC;

    @ExcelField
    private BigDecimal expiryWastage;

    @ExcelField
    private BigDecimal consumable;
    @ExcelField
    private BigDecimal consumableUtility;
    @ExcelField
    private BigDecimal consumableStationary;
    @ExcelField
    private BigDecimal consumableUniform;
    @ExcelField
    private BigDecimal consumableEquipment;
    @ExcelField
    private BigDecimal consumableCutlery;
    @ExcelField
    private BigDecimal consumableDisposable;
    @ExcelField
    private BigDecimal consumableOthers;

    @ExcelField
    private BigDecimal consumableLhi;
    @ExcelField
    private BigDecimal consumableIt;
    @ExcelField
    private BigDecimal consumableMaintenance;
    @ExcelField
    private BigDecimal consumableOfficeEquipment;
    @ExcelField
    private BigDecimal consumableKitchenEquipment;
    @ExcelField
    private BigDecimal consumableChaiMonk;

    @ExcelField
    private BigDecimal consumableTax;
    @ExcelField
    private BigDecimal consumableOthersTax;
    @ExcelField
    private BigDecimal consumableUtilityTax;
    @ExcelField
    private BigDecimal consumableStationaryTax;
    @ExcelField
    private BigDecimal consumableUniformTax;
    @ExcelField
    private BigDecimal consumableEquipmentTax;
    @ExcelField
    private BigDecimal consumableCutleryTax;
    @ExcelField
    private BigDecimal consumableDisposableTax;

    @ExcelField
    private BigDecimal consumableLhiTax;
    @ExcelField
    private BigDecimal consumableItTax;
    @ExcelField
    private BigDecimal consumableMaintenanceTax;
    @ExcelField
    private BigDecimal consumableOfficeEquipmentTax;
    @ExcelField
    private BigDecimal consumableKitchenEquipmentTax;
    @ExcelField
    private BigDecimal consumableChaiMonkTax;


    @ExcelField
    private BigDecimal fixedAssets;

    @ExcelField
    private BigDecimal salary;
    @ExcelField
    private BigDecimal salaryIncentive;
    @ExcelField
    private BigDecimal securityGuardCharges;
    @ExcelField
    private BigDecimal salesIncentive;
    @ExcelField
    private BigDecimal salesIncentivePercent;

    @ExcelField(headerName = "DEPRECIATION_ON_BIKE")
    private BigDecimal depreciationOfBike;
    @ExcelField
    private BigDecimal fuelCharges;
    @ExcelField
    private BigDecimal fuelChargesCafe;
    @ExcelField
    private BigDecimal maintenancePestControlCafe;
    @ExcelField(headerName = "VEHICLE_REGULAR_MAINTENANCE_CAFE")
    private BigDecimal vehicleRegularMaintenance;
    @ExcelField
    private BigDecimal vehicleMonthlyMaintenance;
    @ExcelField(headerName = "PARKING_CHARGES_CAFE")
    private BigDecimal parkingCharges;
    @ExcelField
    private BigDecimal cogsOthers;
    @ExcelField
    private BigDecimal marketingAndSampling;

    @ExcelField
    private BigDecimal consumableMarketing;
    @ExcelField
    private BigDecimal consumableMarketingTax;

    @ExcelField
    private BigDecimal logisticCharges;
    @ExcelField
    private BigDecimal energyElectricity;
    @ExcelField
    private BigDecimal energyDGRunning;
    @ExcelField(headerName = "ENERGY_DG_RUNNING_CAFE")
    private BigDecimal energyDGRunningCafe;
    @ExcelField
    private BigDecimal waterCharges;
    @ExcelField
    private BigDecimal waterChargesCafe;

    @ExcelField
    private BigDecimal communicationInternet;
    @ExcelField
    private BigDecimal communicationTelephone;
    @ExcelField(headerName = "COMMUNICATION_ILL")
    private BigDecimal communicationILL;

    @ExcelField
    private BigDecimal creditCardTransactionPercentage;
    @ExcelField
    private BigDecimal creditCardTransactionCharges;
    @ExcelField
    private BigDecimal voucherTransactionCharges;
    @ExcelField
    private BigDecimal walletsTransactionCharges;
    @ExcelField
    private BigDecimal commissionChannelPartners;
    @ExcelField
    private BigDecimal cancellationChargesChannelPartners;
    @ExcelField
    private BigDecimal commissionChange;
    @ExcelField
    private BigDecimal commissionChangeCafe;
    @ExcelField(headerName = "PAYROLL_PROCESSING_FEES")
    private BigDecimal payrollProcessingFee;
    @ExcelField(headerName = "NEWSPAPER_CHARGES")
    private BigDecimal newsPaper;
    @ExcelField
    private BigDecimal staffWelfareExpenses;
    @ExcelField
    private BigDecimal courierCharges;
    @ExcelField
    private BigDecimal printingAndStationary;
    @ExcelField
    private BigDecimal businessPromotion;
    @ExcelField
    private BigDecimal legalCharges;
    @ExcelField
    private BigDecimal professionalCharges;
    @ExcelField
    private BigDecimal propertyTax;

    @ExcelField(headerName = "PRE_OPENING_LICENSES_FEES")
    private BigDecimal openingLicencesFees;
    @ExcelField
    private BigDecimal registrationCharges;
    @ExcelField
    private BigDecimal stampDutyCharges;
    @ExcelField
    private BigDecimal designingFees;

    @ExcelField
    private BigDecimal fixedParkingCharges;
    @ExcelField(headerName = "COGS_LOGISTIC")
    private BigDecimal cogsLogistics;
    @ExcelField
    private BigDecimal preOpeningCamEleWater;
    @ExcelField
    private BigDecimal preOpeningRegistrationCharges;
    @ExcelField
    private BigDecimal preOpeningStampDutyCharges;
    @ExcelField
    private BigDecimal preOpeningConsumableTax;
    @ExcelField
    private BigDecimal support;
    @ExcelField
    private BigDecimal corporateMarketingChannelPartner;
    @ExcelField
    private BigDecimal preOpeningEleWater;
    @ExcelField
    private BigDecimal preOpeningCam;
    @ExcelField
    private BigDecimal interestOnFixedDepositFICO;
    @ExcelField
    private BigDecimal liabilityNoLongerRequiredWrittenBack;
    @ExcelField
    private BigDecimal interestOnTermLoan;
    @ExcelField
    private BigDecimal amortizationOfIntangibleAssets;
    @ExcelField(headerName = "BUILDING_MAINTENANCE_CAFE")
    private BigDecimal buildingMaintenance;
    @ExcelField
    private BigDecimal cleaningCharges;
    @ExcelField(headerName = "COMPUTER_IT_MAINTENANCE_CAFE")
    private BigDecimal computerMaintenance;
    @ExcelField
    private BigDecimal pestControlCharges;
    @ExcelField(headerName = "EQUIPMENT_MAINTENANCE_CAFE")
    private BigDecimal equipmentMaintenance;
    @ExcelField(headerName = "PRONTO_AMC")
    private BigDecimal prontoAMC;
    @ExcelField(headerName = "RO_AMC")
    private BigDecimal roAMC;

    @ExcelField
    private BigDecimal dgRental;
    @ExcelField
    private BigDecimal edcRental;
    @ExcelField
    private BigDecimal systemRental;
    @ExcelField
    private BigDecimal roRental;
    @ExcelField
    private BigDecimal insuranceAssets;
    @ExcelField(headerName = "INSURANCE_CGL")
    private BigDecimal insuranceCGL;
    @ExcelField(headerName = "INSURANCE_D_AND_O")
    private BigDecimal insuranceDnO;
    @ExcelField
    private BigDecimal odcRental;

    @ExcelField
    private BigDecimal propertyFixRent;
    @ExcelField
    private BigDecimal revenueShare;
    @ExcelField
    private BigDecimal revenueShareDineIn;
    @ExcelField
    private BigDecimal revenueShareDelivery;
    private BigDecimal revenueSharePercent;
    @ExcelField
    private BigDecimal revenueShareDineInPercent;
    @ExcelField
    private BigDecimal revenueShareDeliveryPercent;
    @ExcelField(headerName = "FIX_CAM")
    private BigDecimal fixCAM;
    @ExcelField
    private BigDecimal chillingCharges;
    @ExcelField
    private BigDecimal marketingCharges;
    @ExcelField
    private BigDecimal pettyCashRentals;
    @ExcelField
    private BigDecimal musicRentals;
    @ExcelField
    private BigDecimal internetPartnerRental;

    @ExcelField
    private BigDecimal supportOpsManagement;

    @ExcelField(headerName = "TECHNOLOGY_PLATFORM_CHARGES")
    private BigDecimal techologyPlatformCharges;
    @ExcelField(headerName = "TECHNOLOGY_TRAINING")
    private BigDecimal techologyTraining;
    @ExcelField(headerName = "TECHNOLOGY_OTHERS")
    private BigDecimal techologyOthers;

    @ExcelField
    private BigDecimal corporateMarketingDigital;
    @ExcelField(headerName = "CORPORATE_MARKETING_AD_OFFLINE")
    private BigDecimal corporateMarketingAdvOffline;
    @ExcelField(headerName = "CORPORATE_MARKETING_AD_ONLINE")
    private BigDecimal corporateMarketingAdvOnline;
    @ExcelField
    private BigDecimal corporateMarketingOutdoor;
    @ExcelField
    private BigDecimal corporateMarketingPhotography;
    @ExcelField
    private BigDecimal corporateMarketingAgencyFees;

    @ExcelField
    private BigDecimal varianceZero;
    @ExcelField
    private BigDecimal varianceZeroTax;

    @ExcelField(headerName = "TECHNOLOGY_POS")
    private BigDecimal technologyVariable;

    @ExcelField
    private BigDecimal deliveryChargesVariable;

    @ExcelField
    private String onRevenueShare;
    @ExcelField
    private BigDecimal electricityMeter1FixedCharge;
    @ExcelField
    private BigDecimal electricityMeter1PerUnitCharge;
    @ExcelField
    private BigDecimal electricityMeter1TaxPercentage;
    @ExcelField
    private BigDecimal electricityMeter1OtherCharge;
    @ExcelField
    private BigDecimal electricityMeter2FixedCharge;
    @ExcelField
    private BigDecimal electricityMeter2PerUnitCharge;
    @ExcelField
    private BigDecimal electricityMeter2TaxPercentage;
    @ExcelField
    private BigDecimal electricityMeter2OtherCharge;
    @ExcelField
    private BigDecimal electricityMeter3FixedCharge;
    @ExcelField
    private BigDecimal electricityMeter3PerUnitCharge;
    @ExcelField
    private BigDecimal electricityMeter3TaxPercentage;
    @ExcelField
    private BigDecimal electricityMeter3OtherCharge;
    @ExcelField
    private BigDecimal dgMeterFixedCharge;
    @ExcelField
    private BigDecimal dgMeterPerUnitCharge;
    @ExcelField
    private BigDecimal dgMeterTaxPercentage;
    @ExcelField
    private BigDecimal dgMeterOtherCharge;

    @ExcelField
    private BigDecimal conveyanceMarketing;
    @ExcelField(headerName = "CONVEYANCE_OPERATION")
    private BigDecimal conveyanceOperations;
    @ExcelField
    private BigDecimal conveyanceOthers;
    @ExcelField
    private BigDecimal auditFee;
    @ExcelField
    private BigDecimal auditFeeOutOfPocket;
    //	@ExcelField
//	private BigDecimal badDebtsWrittenOff;
    @ExcelField
    private BigDecimal brokerage;
    @ExcelField
    private BigDecimal charityAndDonations;
    @ExcelField
    private BigDecimal domesticTicketsAndHotels;
    @ExcelField
    private BigDecimal internationalTicketsAndHotels;
    @ExcelField(headerName = "HOUSEKEEPING_CHARGES")
    private BigDecimal houseKeepingCharges;
    @ExcelField
    private BigDecimal lateFeeCharges;
    @ExcelField
    private BigDecimal marketingDataAnalysis;
    @ExcelField
    private BigDecimal miscellaneousExpenses;
    @ExcelField
    private BigDecimal penalty;
    @ExcelField
    private BigDecimal photoCopyExpenses;
    @ExcelField
    private BigDecimal qcrExpense;
    @ExcelField(headerName = "RECRUITMENT_CONSULTANTS")
    private BigDecimal recuritmentConsultants;
    @ExcelField
    private BigDecimal rocFees;
    @ExcelField
    private BigDecimal travellingExpenseODC;
    @ExcelField(headerName = "TRAVELLING_EXPENSES")
    private BigDecimal travellingExpense;
    @ExcelField
    private BigDecimal debitCreditWrittenOff;
    @ExcelField(headerName = "DIFFENECE_IN_EXCHANGE")
    private BigDecimal differenceInExchange;
    @ExcelField
    private BigDecimal serviceChargesPaid;
    @ExcelField
    private BigDecimal insuranceVehicle;
    @ExcelField(headerName = "OTHERS_AMC")
    private BigDecimal othersAMC;
    @ExcelField
    private BigDecimal othersMaintenance;
    @ExcelField(headerName = "RND_ENGINEERING_EXPENSE")
    private BigDecimal RnDEngineeringExpenses;
    @ExcelField
    private BigDecimal byodCharges;
    @ExcelField(headerName = "CAR_LEASE_SR")
    private BigDecimal carLease;
    @ExcelField(headerName = "DRIVER_SALARY_SR")
    private BigDecimal driverSalary;
    @ExcelField(headerName = "GRATUITY_EXPENSE")
    private BigDecimal gratuity;
    @ExcelField(headerName = "INSURANCE_ACCIDENTAL")
    private BigDecimal insurnaceAccidental;
    @ExcelField(headerName = "INSURANCE_MEDICAL")
    private BigDecimal insurnaceMedical;
    @ExcelField(headerName = "SUPPORT_OPS_TURNOVER")
    private BigDecimal supportsOpsTurnover;
    @ExcelField
    private BigDecimal employeeFacilitationExpenses;
    @ExcelField(headerName = "TELEPHONE_SR")
    private BigDecimal telephoneSR;
    @ExcelField(headerName = "VEHICLE_RUNNING_AND_MAINT_SR")
    private BigDecimal vehicleRunningAndMaintSR;
    @ExcelField
    private BigDecimal employeeStockOptionExpense;
    @ExcelField(headerName = "EMPLOYER_CONTRIBUTION_LWF")
    private BigDecimal employerContributionLWF;
    @ExcelField
    private BigDecimal esicEmployerCont;
    @ExcelField
    private BigDecimal leaveTravelReimbursement;
    @ExcelField
    private BigDecimal pfAdministrationCharges;
    @ExcelField
    private BigDecimal pfEmployerCont;
    @ExcelField
    private BigDecimal quarterlyIncentive;
    @ExcelField(headerName = "BUSINESS_PROMOTION_SR")
    private BigDecimal businessPromotionSR;
    //	@ExcelField
//	private BigDecimal supportAudit;
    @ExcelField(headerName = "SUPPORT_CCC")
    private BigDecimal supportCCC;
    @ExcelField
    private BigDecimal supportIT;
    //	@ExcelField
//	private BigDecimal supportMaintenance;
    @ExcelField(headerName = "SUPPORT_COMM_WH")
    private BigDecimal supportCommWH;
    @ExcelField(headerName = "MARKETING_NPI_CAFE")
    private BigDecimal marketingNPI;
    @ExcelField
    private BigDecimal capitalImprovementExpenses;
    @ExcelField
    private BigDecimal leaseHoldImprovements;
    @ExcelField(headerName = "FIXED_ASSETS_EQUIPMENT_CAFE")
    private BigDecimal fixedAssetsEquipment;
    @ExcelField(headerName = "FIXED_ASSET_FURNITURE_CAFE")
    private BigDecimal fixedAssetFurniture;
    @ExcelField(headerName = "FIXED_ASSETS_IT_CAFE")
    private BigDecimal fixedAssetsIT;
    @ExcelField(headerName = "FIXED_ASSETS_KITCHEN_EQUIPMENT_CAFE")
    private BigDecimal fixedAssetsKitchenEquipment;
    @ExcelField(headerName = "FIXED_ASSETS_OFFICE_EQUIPMENT_CAFE")
    private BigDecimal fixedAssetsOfficeEquipment;
    @ExcelField
    private BigDecimal fixedAssetsVehicle;
    @ExcelField
    private BigDecimal marketingLaunch;
    @ExcelField
    private BigDecimal preOpeningConsumable;
    @ExcelField
    private BigDecimal preOpeningOthers;
    @ExcelField
    private BigDecimal preOpeningRent;
    @ExcelField
    private BigDecimal preOpeningSalary;
    @ExcelField
    private BigDecimal bankCharges;
    @ExcelField
    private BigDecimal interestOnLoan;
    @ExcelField(headerName = "INTREST_ON_TDS_OR_GST")
    private BigDecimal intrestOnTDSorGST;
    @ExcelField(headerName = "INTEREST_ON_FDR")
    private BigDecimal interestOnFDR;
    @ExcelField(headerName = "PROFIT_OR_LOSS_SALE_MUTUTAL_FUND")
    private BigDecimal profitSaleMutualFunds;
    @ExcelField
    private BigDecimal interestIncomeTaxRefund;
    @ExcelField
    private BigDecimal miscIncome;
    @ExcelField
    private BigDecimal discountReceived;
    @ExcelField
    private BigDecimal interiorDesigningCharge;
    @ExcelField(headerName = "SCRAPE_CHARGES")
    private BigDecimal scrape;
    @ExcelField(headerName = "SERVICE_CHARGE")
    private BigDecimal serviceCharges;
    @ExcelField(headerName = "SERVICE_CHARGE_FICO")
    private BigDecimal serviceChargesFICO;

    @ExcelField
    private BigDecimal bonusAttendance;
    @ExcelField
    private BigDecimal bonusJoining;
    @ExcelField
    private BigDecimal bonusReferral;
    @ExcelField
    private BigDecimal bonusHoliday;
    @ExcelField
    private BigDecimal bonusOthers;
    @ExcelField
    private BigDecimal allowanceRemoteLocation;
    @ExcelField
    private BigDecimal allowanceEmployeeBenefit;
    @ExcelField
    private BigDecimal allowanceCityCompensatory;
    @ExcelField
    private BigDecimal allowanceMonk;
    @ExcelField
    private BigDecimal allowanceOthers;
    @ExcelField(headerName = "NOTICE_PERIOD_BUY_OUT")
    private BigDecimal noticePeriodBuyout;
    @ExcelField(headerName = "NOTICE_PERIOD_DEDUCTION")
    private BigDecimal noticePeriodDeduction;
    @ExcelField
    private BigDecimal relocationExpenses;
    @ExcelField(headerName = "STIPEND_EXPENSE")
    private BigDecimal stipendExpenses;
    @ExcelField(headerName = "TRAINING_COST_RECOVERY")
    private BigDecimal trainingCostRecovery;
    @ExcelField
    private BigDecimal severancePay;
    @ExcelField
    private BigDecimal labourCharges;

    @ExcelField
    private BigDecimal grossCost;
    @ExcelField
    private BigDecimal grossProfit;
    @ExcelField
    private BigDecimal grossProfitPercentage;
    @ExcelField
    private BigDecimal totalCost;
    @ExcelField
    private BigDecimal netProfit;
    @ExcelField
    private BigDecimal netProfitPercentage;
    @ExcelField
    private BigDecimal roundedOff;
//	@ExcelField
//	private BigDecimal shortAndExcess;

    private Integer updatedBy;

    private Date updateTime;

    @ExcelField
    private BigDecimal vehicleRegularMaintenanceHq;
    @ExcelField
    private BigDecimal buildingMaintenanceHq;
    @ExcelField
    private BigDecimal computerItMaintenanceHq;
    @ExcelField
    private BigDecimal equipmentMaintenanceHq;
    @ExcelField
    private BigDecimal marketingNpiHq;
    @ExcelField
    private BigDecimal licenseExpenses;
    @ExcelField
    private BigDecimal corporateMarketingAtlRadio;
    @ExcelField
    private BigDecimal corporateMarketingAtlTv;
    @ExcelField
    private BigDecimal corporateMarketingAtlPrintAd;
    @ExcelField
    private BigDecimal corporateMarketingAtlCinema;
    @ExcelField
    private BigDecimal corporateMarketingAtlDigital;
    @ExcelField(headerName = "LOGISTIC_INTRASTATE_COLD_VEHICLE")
    private BigDecimal logisticInterstateColdVehicle;
    @ExcelField(headerName = "LOGISTIC_INTRASTATE_NON_COLD_VEHICLE")
    private BigDecimal logisticInterstateNonColdVehicle;
    @ExcelField
    private BigDecimal logisticInterstateAir;
    @ExcelField
    private BigDecimal logisticInterstateRoad;
    @ExcelField
    private BigDecimal logisticInterstateTrain;
    @ExcelField
    private BigDecimal airConditionerAmc;
    @ExcelField(headerName = "CORPORATE_MARKETING_SMS_EMAIL")
    private BigDecimal corporateMarketingSms;
    @ExcelField
    private BigDecimal otherServiceCharges;
    @ExcelField(headerName = "EMP_DISCOUNT_LOYAL_TEA")
    private BigDecimal empDiscountLoyalty;
    @ExcelField
    private BigDecimal empDiscountMarketing;
    @ExcelField
    private BigDecimal empDiscountOps;
    @ExcelField
    private BigDecimal empDiscountBd;
    @ExcelField
    private BigDecimal empDiscountEmployeeFico;


    @ExcelField
    private BigDecimal unsatisfiedCustomerCost;
    @ExcelField
    private BigDecimal ppeCost;
    @ExcelField
    private BigDecimal performanceIncentive;
//    @ExcelField(headerName = "TECHNOLOGY_POS")
//    private BigDecimal TechnologyPos;
    @ExcelField
    private BigDecimal trainingCogs;
    @ExcelField
    private BigDecimal dineInCogsTax;
    @ExcelField
    private BigDecimal deliveryCogsTax;
    @ExcelField
    private BigDecimal employeeMealCogsTax;
    @ExcelField
    private BigDecimal unsatisfiedCustomerCostTax;
    @ExcelField
    private BigDecimal ppeCostTax;
    @ExcelField
    private BigDecimal expiryWastageTax;
    @ExcelField
    private BigDecimal wastageOther;
    @ExcelField
    private BigDecimal wastageOtherTax;
    @ExcelField(headerName = "MARKETING_AND_SAMPLING_TAX")
    private BigDecimal MarketingAndSamplingTax;
    @ExcelField
    private BigDecimal trainingCogsTax;
    @ExcelField
    private BigDecimal variancePccTax;
    @ExcelField
    private BigDecimal varianceYcTax;
    @ExcelField
    private BigDecimal discountGiftCard;
    @ExcelField
    private BigDecimal insuranceMarine;
    @ExcelField
    private BigDecimal royaltyFees;
    @ExcelField
    private BigDecimal photoCopyExpensesCafe;
    @ExcelField
    private BigDecimal printingAndStationaryCafe;
    @ExcelField
    private BigDecimal staffWelfareExpensesCafe;
    @ExcelField
    private BigDecimal cleaningChargesCafe;
    @ExcelField
    private BigDecimal businessPromotionCafe;
    @ExcelField
    private BigDecimal courierChargesCafe;
    @ExcelField
    private BigDecimal newsPaperCafe;

    public UnitBudgetoryDetail() {
        // this is required for reflection
        // TODO Auto-generated constructor stub
    }

    public UnitBudgetoryDetail(int unitId, String unitName) {
        this.unitId = unitId;
        this.unitName = unitName;
    }

    public UnitBudgetoryDetail(int unitId, String unitName, int year, int month, String status) {
        this.unitId = unitId;
        this.unitName = unitName;
        this.year = year;
        this.month = month;
        this.status = status;
    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "UNIT_BUDGET_DETAIL_ID", unique = true, nullable = false)
    public int getBudgetDetailId() {
        return budgetDetailId;
    }

    public void setBudgetDetailId(int unitExpenseDetailId) {
        this.budgetDetailId = unitExpenseDetailId;
    }

    @Column(name = "UNIT_ID", nullable = true)
    public int getUnitId() {
        return unitId;
    }

    public void setUnitId(int unitId) {
        this.unitId = unitId;
    }

    @Column(name = "UNIT_NAME", nullable = true)
    public String getUnitName() {
        return unitName;
    }

    public void setUnitName(String unitName) {
        this.unitName = unitName;
    }

    @Column(name = "CALCULATION_YEAR", nullable = true)
    public int getYear() {
        return year;
    }

    public void setYear(int year) {
        this.year = year;
    }

    @Column(name = "CALCULATION_MONTH", nullable = true)
    public int getMonth() {
        return month;
    }

    public void setMonth(int month) {
        this.month = month;
    }

    @Column(name = "TOTAL_TICKET", nullable = true)
    public Integer getTicket() {
        return ticket;
    }

    public void setTicket(Integer ticket) {
        this.ticket = ticket;
    }

    @Column(name = "TOTAL_SALES", nullable = true)
    public BigDecimal getSales() {
        return sales;
    }

    public void setSales(BigDecimal sales) {
        this.sales = sales;
    }

    @Column(name = "TOTAL_APC", nullable = true)
    public BigDecimal getApc() {
        return apc;
    }

    public void setApc(BigDecimal apc) {
        this.apc = apc;
    }

    @Column(name = "TOTAL_GMV", nullable = true)
    public BigDecimal getGmv() {
        return gmv;
    }

    public void setGmv(BigDecimal gmv) {
        this.gmv = gmv;
    }

    @Column(name = "TOTAL_DISCOUNT", nullable = true)
    public BigDecimal getDiscount() {
        return discount;
    }

    public void setDiscount(BigDecimal discount) {
        this.discount = discount;
    }

    @Column(name = "TOTAL_DISCOUNT_LOYAL_TEA", nullable = true)
    public BigDecimal getDiscountLoyalty() {
        return discountLoyalty;
    }

    public void setDiscountLoyalty(BigDecimal discountLoyalty) {
        this.discountLoyalty = discountLoyalty;
    }

    @Column(name = "TOTAL_DISCOUNT_MARKETING", nullable = true)
    public BigDecimal getDiscountMarketing() {
        return discountMarketing;
    }

    public void setDiscountMarketing(BigDecimal discountMarketing) {
        this.discountMarketing = discountMarketing;
    }

    @Column(name = "TOTAL_DISCOUNT_OPS", nullable = true)
    public BigDecimal getDiscountOps() {
        return discountOps;
    }

    public void setDiscountOps(BigDecimal discountOps) {
        this.discountOps = discountOps;
    }

    @Column(name = "TOTAL_DISCOUNT_BD", nullable = true)
    public BigDecimal getDiscountBd() {
        return discountBd;
    }

    public void setDiscountBd(BigDecimal discountBd) {
        this.discountBd = discountBd;
    }

    @Column(name = "TOTAL_DISCOUNT_EMPLOYEE_FICO", nullable = true)
    public BigDecimal getDiscountEmployeeFico() {
        return discountEmployeeFico;
    }

    public void setDiscountEmployeeFico(BigDecimal discountEmployeeFico) {
        this.discountEmployeeFico = discountEmployeeFico;
    }

    @Column(name = "DINE_IN_TICKET", nullable = true)
    public Integer getDineInTicket() {
        return dineInTicket;
    }

    public void setDineInTicket(Integer dineInTicket) {
        this.dineInTicket = dineInTicket;
    }

    @Column(name = "DINE_IN_SALES", nullable = true)
    public BigDecimal getDineInSales() {
        return dineInSales;
    }

    public void setDineInSales(BigDecimal dineInSales) {
        this.dineInSales = dineInSales;
    }

    @Column(name = "DINE_IN_APC", nullable = true)
    public BigDecimal getDineInApc() {
        return dineInApc;
    }

    public void setDineInApc(BigDecimal dineInApc) {
        this.dineInApc = dineInApc;
    }

    @Column(name = "DINE_IN_GMV", nullable = true)
    public BigDecimal getDineInGmv() {
        return dineInGmv;
    }

    public void setDineInGmv(BigDecimal dineInGmv) {
        this.dineInGmv = dineInGmv;
    }

    @Column(name = "DINE_IN_DISCOUNT", nullable = true)
    public BigDecimal getDineInDiscount() {
        return dineInDiscount;
    }

    public void setDineInDiscount(BigDecimal dineInDiscount) {
        this.dineInDiscount = dineInDiscount;
    }

    @Column(name = "DINE_IN_DISCOUNT_LOYAL_TEA", nullable = true)
    public BigDecimal getDineInDiscountLoyalty() {
        return dineInDiscountLoyalty;
    }

    public void setDineInDiscountLoyalty(BigDecimal dineInDiscountLoyalty) {
        this.dineInDiscountLoyalty = dineInDiscountLoyalty;
    }

    @Column(name = "DINE_IN_DISCOUNT_MARKETING", nullable = true)
    public BigDecimal getDineInDiscountMarketing() {
        return dineInDiscountMarketing;
    }

    public void setDineInDiscountMarketing(BigDecimal dineInDiscountMarketing) {
        this.dineInDiscountMarketing = dineInDiscountMarketing;
    }

    @Column(name = "DINE_IN_DISCOUNT_OPS", nullable = true)
    public BigDecimal getDineInDiscountOps() {
        return dineInDiscountOps;
    }

    public void setDineInDiscountOps(BigDecimal dineInDiscountOps) {
        this.dineInDiscountOps = dineInDiscountOps;
    }

    @Column(name = "DINE_IN_DISCOUNT_BD", nullable = true)
    public BigDecimal getDineInDiscountBd() {
        return dineInDiscountBd;
    }

    public void setDineInDiscountBd(BigDecimal dineInDiscountBd) {
        this.dineInDiscountBd = dineInDiscountBd;
    }

    @Column(name = "DINE_IN_DISCOUNT_EMPLOYEE_FICO", nullable = true)
    public BigDecimal getDineInDiscountEmployeeFico() {
        return dineInDiscountEmployeeFico;
    }

    public void setDineInDiscountEmployeeFico(BigDecimal dineInDiscountEmployeeFico) {
        this.dineInDiscountEmployeeFico = dineInDiscountEmployeeFico;
    }

    @Column(name = "DELIVERY_TICKET", nullable = true)
    public Integer getDeliveryTicket() {
        return deliveryTicket;
    }

    public void setDeliveryTicket(Integer deliveryTicket) {
        this.deliveryTicket = deliveryTicket;
    }

    @Column(name = "DELIVERY_SALES", nullable = true)
    public BigDecimal getDeliverySales() {
        return deliverySales;
    }

    public void setDeliverySales(BigDecimal deliverySales) {
        this.deliverySales = deliverySales;
    }

    @Column(name = "DELIVERY_APC", nullable = true)
    public BigDecimal getDeliveryApc() {
        return deliveryApc;
    }

    public void setDeliveryApc(BigDecimal deliveryApc) {
        this.deliveryApc = deliveryApc;
    }

    @Column(name = "DELIVERY_GMV", nullable = true)
    public BigDecimal getDeliveryGmv() {
        return deliveryGmv;
    }

    public void setDeliveryGmv(BigDecimal deliveryGmv) {
        this.deliveryGmv = deliveryGmv;
    }

    @Column(name = "DELIVERY_DISCOUNT", nullable = true)
    public BigDecimal getDeliveryDiscount() {
        return deliveryDiscount;
    }

    public void setDeliveryDiscount(BigDecimal deliveryDiscount) {
        this.deliveryDiscount = deliveryDiscount;
    }

    @Column(name = "DELIVERY_DISCOUNT_LOYAL_TEA", nullable = true)
    public BigDecimal getDeliveryDiscountLoyalty() {
        return deliveryDiscountLoyalty;
    }

    public void setDeliveryDiscountLoyalty(BigDecimal deliveryDiscountLoyalty) {
        this.deliveryDiscountLoyalty = deliveryDiscountLoyalty;
    }

    @Column(name = "DELIVERY_DISCOUNT_MARKETING", nullable = true)
    public BigDecimal getDeliveryDiscountMarketing() {
        return deliveryDiscountMarketing;
    }

    public void setDeliveryDiscountMarketing(BigDecimal deliveryDiscountMarketing) {
        this.deliveryDiscountMarketing = deliveryDiscountMarketing;
    }

    @Column(name = "DELIVERY_DISCOUNT_OPS", nullable = true)
    public BigDecimal getDeliveryDiscountOps() {
        return deliveryDiscountOps;
    }

    public void setDeliveryDiscountOps(BigDecimal deliveryDiscountOps) {
        this.deliveryDiscountOps = deliveryDiscountOps;
    }

    @Column(name = "DELIVERY_DISCOUNT_BD", nullable = true)
    public BigDecimal getDeliveryDiscountBd() {
        return deliveryDiscountBd;
    }

    public void setDeliveryDiscountBd(BigDecimal deliveryDiscountBd) {
        this.deliveryDiscountBd = deliveryDiscountBd;
    }

    @Column(name = "DELIVERY_DISCOUNT_EMPLOYEE_FICO", nullable = true)
    public BigDecimal getDeliveryDiscountEmployeeFico() {
        return deliveryDiscountEmployeeFico;
    }

    public void setDeliveryDiscountEmployeeFico(BigDecimal deliveryDiscountEmployeeFico) {
        this.deliveryDiscountEmployeeFico = deliveryDiscountEmployeeFico;
    }

    @Column(name = "GIFT_CARD_SALES", nullable = true)
    public BigDecimal getGiftCardSale() {
        return giftCardSale;
    }

    public void setGiftCardSale(BigDecimal giftCardSale) {
        this.giftCardSale = giftCardSale;
    }

    @Column(name = "DINE_IN_COGS", nullable = true)
    public BigDecimal getDineInCogs() {
        return dineInCogs;
    }

    public void setDineInCogs(BigDecimal dineInCogs) {
        this.dineInCogs = dineInCogs;
    }

    @Column(name = "DELIVERY_COGS", nullable = true)
    public BigDecimal getDeliveryCogs() {
        return deliveryCogs;
    }

    public void setDeliveryCogs(BigDecimal deliveryCogs) {
        this.deliveryCogs = deliveryCogs;
    }

    @Column(name = "EMPLOYEE_MEAL_COGS", nullable = true)
    public BigDecimal getEmployeeMealCogs() {
        return employeeMealCogs;
    }

    public void setEmployeeMealCogs(BigDecimal employeeMealCogs) {
        this.employeeMealCogs = employeeMealCogs;
    }

    @Column(name = "EXPIRY_WASTAGE", nullable = true)
    public BigDecimal getExpiryWastage() {
        return expiryWastage;
    }

    public void setExpiryWastage(BigDecimal expiryWastage) {
        this.expiryWastage = expiryWastage;
    }

    @Column(name = "CONSUMABLE", nullable = true)
    public BigDecimal getConsumable() {
        return consumable;
    }

    public void setConsumable(BigDecimal consumable) {
        this.consumable = consumable;
    }

    @Column(name = "CONSUMABLE_UTILITY", nullable = true)
    public BigDecimal getConsumableUtility() {
        return consumableUtility;
    }

    public void setConsumableUtility(BigDecimal consumableUtility) {
        this.consumableUtility = consumableUtility;
    }

    @Column(name = "CONSUMABLE_STATIONARY", nullable = true)
    public BigDecimal getConsumableStationary() {
        return consumableStationary;
    }

    public void setConsumableStationary(BigDecimal consumableStationary) {
        this.consumableStationary = consumableStationary;
    }

    @Column(name = "CONSUMABLE_UNIFORM", nullable = true)
    public BigDecimal getConsumableUniform() {
        return consumableUniform;
    }

    public void setConsumableUniform(BigDecimal consumableUniform) {
        this.consumableUniform = consumableUniform;
    }

    @Column(name = "CONSUMABLE_EQUIPMENT", nullable = true)
    public BigDecimal getConsumableEquipment() {
        return consumableEquipment;
    }

    public void setConsumableEquipment(BigDecimal consumableEquipment) {
        this.consumableEquipment = consumableEquipment;
    }

    @Column(name = "CONSUMABLE_CUTLERY", nullable = true)
    public BigDecimal getConsumableCutlery() {
        return consumableCutlery;
    }

    public void setConsumableCutlery(BigDecimal consumableCutlery) {
        this.consumableCutlery = consumableCutlery;
    }

    @Column(name = "CONSUMABLE_DISPOSABLE", nullable = true)
    public BigDecimal getConsumableDisposable() {
        return consumableDisposable;
    }

    public void setConsumableDisposable(BigDecimal consumableDisposable) {
        this.consumableDisposable = consumableDisposable;
    }

    @Column(name = "CONSUMABLE_LHI", nullable = true)
    public BigDecimal getConsumableLhi() {
        return consumableLhi;
    }

    public void setConsumableLhi(BigDecimal consumableLhi) {
        this.consumableLhi = consumableLhi;
    }

    @Column(name = "CONSUMABLE_IT", nullable = true)
    public BigDecimal getConsumableIt() {
        return consumableIt;
    }

    public void setConsumableIt(BigDecimal consumableIt) {
        this.consumableIt = consumableIt;
    }

    @Column(name = "CONSUMABLE_MAINTENANCE", nullable = true)
    public BigDecimal getConsumableMaintenance() {
        return consumableMaintenance;
    }

    public void setConsumableMaintenance(BigDecimal consumableMaintenance) {
        this.consumableMaintenance = consumableMaintenance;
    }

    @Column(name = "CONSUMABLE_OFFICE_EQUIPMENT", nullable = true)
    public BigDecimal getConsumableOfficeEquipment() {
        return consumableOfficeEquipment;
    }

    public void setConsumableOfficeEquipment(BigDecimal consumableOfficeEquipment) {
        this.consumableOfficeEquipment = consumableOfficeEquipment;
    }

    @Column(name = "CONSUMABLE_KITCHEN_EQUIPMENT", nullable = true)
    public BigDecimal getConsumableKitchenEquipment() {
        return consumableKitchenEquipment;
    }

    public void setConsumableKitchenEquipment(BigDecimal consumableKitchenEquipment) {
        this.consumableKitchenEquipment = consumableKitchenEquipment;
    }

    @Column(name = "CONSUMABLE_CHAI_MONK", nullable = true)
    public BigDecimal getConsumableChaiMonk() {
        return consumableChaiMonk;
    }

    public void setConsumableChaiMonk(BigDecimal consumableChaiMonk) {
        this.consumableChaiMonk = consumableChaiMonk;
    }

    @Column(name = "FIXED_ASSETS", nullable = true)
    public BigDecimal getFixedAssets() {
        return fixedAssets;
    }

    public void setFixedAssets(BigDecimal fixedAssets) {
        this.fixedAssets = fixedAssets;
    }

    @Column(name = "SALARY", nullable = true)
    public BigDecimal getSalary() {
        return salary;
    }

    public void setSalary(BigDecimal salary) {
        this.salary = salary;
    }

    @Column(name = "SALARY_INCENTIVE", nullable = true)
    public BigDecimal getSalaryIncentive() {
        return salaryIncentive;
    }

    public void setSalaryIncentive(BigDecimal salaryIncentive) {
        this.salaryIncentive = salaryIncentive;
    }

    @Column(name = "SECURITY_GUARD_CHARGES", nullable = true)
    public BigDecimal getSecurityGuardCharges() {
        return securityGuardCharges;
    }

    public void setSecurityGuardCharges(BigDecimal securityGuardCharges) {
        this.securityGuardCharges = securityGuardCharges;
    }

    @Column(name = "SALES_INCENTIVE", nullable = true)
    public BigDecimal getSalesIncentive() {
        return salesIncentive;
    }

    public void setSalesIncentive(BigDecimal salesIncentive) {
        this.salesIncentive = salesIncentive;
    }

    @Column(name = "SALES_INCENTIVE_PERCENTAGE", nullable = true)
    public BigDecimal getSalesIncentivePercent() {
        return salesIncentivePercent;
    }

    public void setSalesIncentivePercent(BigDecimal salesIncentivePercent) {
        this.salesIncentivePercent = salesIncentivePercent;
    }

    @Column(name = "DEPRECIATION_ON_BIKE", nullable = true)
    public BigDecimal getDepreciationOfBike() {
        return depreciationOfBike;
    }

    public void setDepreciationOfBike(BigDecimal depreciationOfBike) {
        this.depreciationOfBike = depreciationOfBike;
    }

    @Column(name = "FUEL_CHARGES", nullable = true)
    public BigDecimal getFuelCharges() {
        return fuelCharges;
    }

    public void setFuelCharges(BigDecimal fuelCharges) {
        this.fuelCharges = fuelCharges;
    }

    @Column(name = "FUEL_CHARGES_CAFE", nullable = true)
    public BigDecimal getFuelChargesCafe() {
        return fuelChargesCafe;
    }

    public void setFuelChargesCafe(BigDecimal fuelChargesCafe) {
        this.fuelChargesCafe = fuelChargesCafe;
    }

    @Column(name = "VEHICLE_REGULAR_MAINTENANCE_CAFE", nullable = true)
    public BigDecimal getVehicleRegularMaintenance() {
        return vehicleRegularMaintenance;
    }

    public void setVehicleRegularMaintenance(BigDecimal vehicleRegularMaintenance) {
        this.vehicleRegularMaintenance = vehicleRegularMaintenance;
    }

    @Column(name = "VEHICLE_MONTHLY_MAINTENANCE", nullable = true)
    public BigDecimal getVehicleMonthlyMaintenance() {
        return vehicleMonthlyMaintenance;
    }

    public void setVehicleMonthlyMaintenance(BigDecimal vehicleMonthlyMaintenance) {
        this.vehicleMonthlyMaintenance = vehicleMonthlyMaintenance;
    }

    @Column(name = "PARKING_CHARGES_CAFE", nullable = true)
    public BigDecimal getParkingCharges() {
        return parkingCharges;
    }

    public void setParkingCharges(BigDecimal parkingCharges) {
        this.parkingCharges = parkingCharges;
    }

    @Column(name = "MARKETING_AND_SAMPLING", nullable = true)
    public BigDecimal getMarketingAndSampling() {
        return marketingAndSampling;
    }

    public void setMarketingAndSampling(BigDecimal marketingAndSampling) {
        this.marketingAndSampling = marketingAndSampling;
    }

    @Column(name = "CONSUMABLE_MARKETING", nullable = true)
    public BigDecimal getConsumableMarketing() {
        return consumableMarketing;
    }

    public void setConsumableMarketing(BigDecimal consumableMarketing) {
        this.consumableMarketing = consumableMarketing;
    }

    @Column(name = "LOGISTIC_CHARGES", nullable = true)
    public BigDecimal getLogisticCharges() {
        return logisticCharges;
    }

    public void setLogisticCharges(BigDecimal logisticCharges) {
        this.logisticCharges = logisticCharges;
    }

    @Column(name = "ENERGY_ELECTRICITY", nullable = true)
    public BigDecimal getEnergyElectricity() {
        return energyElectricity;
    }

    public void setEnergyElectricity(BigDecimal energyElectricity) {
        this.energyElectricity = energyElectricity;
    }

    @Column(name = "ENERGY_DG_RUNNING", nullable = true)
    public BigDecimal getEnergyDGRunning() {
        return energyDGRunning;
    }

    public void setEnergyDGRunning(BigDecimal energyDGRunning) {
        this.energyDGRunning = energyDGRunning;
    }

    @Column(name = "ENERGY_DG_RUNNING_CAFE", nullable = true)
    public BigDecimal getEnergyDGRunningCafe() {
        return energyDGRunningCafe;
    }

    public void setEnergyDGRunningCafe(BigDecimal energyDGRunningCafe) {
        this.energyDGRunningCafe = energyDGRunningCafe;
    }

    @Column(name = "WATER_CHARGES", nullable = true)
    public BigDecimal getWaterCharges() {
        return waterCharges;
    }

    public void setWaterCharges(BigDecimal waterCharges) {
        this.waterCharges = waterCharges;
    }

    @Column(name = "COMMUNICATION_INTERNET", nullable = true)
    public BigDecimal getCommunicationInternet() {
        return communicationInternet;
    }

    public void setCommunicationInternet(BigDecimal communicationInternet) {
        this.communicationInternet = communicationInternet;
    }

    @Column(name = "COMMUNICATION_TELEPHONE", nullable = true)
    public BigDecimal getCommunicationTelephone() {
        return communicationTelephone;
    }

    public void setCommunicationTelephone(BigDecimal communicationTelephone) {
        this.communicationTelephone = communicationTelephone;
    }

    @Column(name = "MAINTENANCE_PEST_CONTROL_CAFE", nullable = true)
    public BigDecimal getMaintenancePestControlCafe() {
        return maintenancePestControlCafe;
    }

    public void setMaintenancePestControlCafe(BigDecimal maintenancePestControlCafe) {
        this.maintenancePestControlCafe = maintenancePestControlCafe;
    }

    @Column(name = "COMMUNICATION_ILL", nullable = true)
    public BigDecimal getCommunicationILL() {
        return communicationILL;
    }

    public void setCommunicationILL(BigDecimal communicationILL) {
        this.communicationILL = communicationILL;
    }

    @Column(name = "CREDIT_CARD_TRANSACTION_PERCENTAGE", nullable = true)
    public BigDecimal getCreditCardTransactionPercentage() {
        return creditCardTransactionPercentage;
    }

    public void setCreditCardTransactionPercentage(BigDecimal creditCardTransactionPercentage) {
        this.creditCardTransactionPercentage = creditCardTransactionPercentage;
    }

    @Column(name = "CREDIT_CARD_TRANSACTION_CHARGES", nullable = true)
    public BigDecimal getCreditCardTransactionCharges() {
        return creditCardTransactionCharges;
    }

    public void setCreditCardTransactionCharges(BigDecimal creditCardTransactionCharges) {
        this.creditCardTransactionCharges = creditCardTransactionCharges;
    }

    @Column(name = "VOUCHER_TRANSACTION_CHARGES", nullable = true)
    public BigDecimal getVoucherTransactionCharges() {
        return voucherTransactionCharges;
    }

    public void setVoucherTransactionCharges(BigDecimal voucherTransactionCharges) {
        this.voucherTransactionCharges = voucherTransactionCharges;
    }

    @Column(name = "WALLETS_TRANSACTION_CHARGES", nullable = true)
    public BigDecimal getWalletsTransactionCharges() {
        return walletsTransactionCharges;
    }

    public void setWalletsTransactionCharges(BigDecimal walletsTransactionCharges) {
        this.walletsTransactionCharges = walletsTransactionCharges;
    }

    @Column(name = "COMMISSION_CHANNEL_PARTNERS", nullable = true)
    public BigDecimal getCommissionChannelPartners() {
        return commissionChannelPartners;
    }

    public void setCommissionChannelPartners(BigDecimal commissionChannelPartners) {
        this.commissionChannelPartners = commissionChannelPartners;
    }

    @Column(name = "CANCELLATION_CHARGES", nullable = true)
    public BigDecimal getCancellationChargesChannelPartners() {
        return cancellationChargesChannelPartners;
    }

    public void setCancellationChargesChannelPartners(BigDecimal cancellationChargesChannelPartners) {
        this.cancellationChargesChannelPartners = cancellationChargesChannelPartners;
    }

    @Column(name = "COMMISSION_CHANGE", nullable = true)
    public BigDecimal getCommissionChange() {
        return commissionChange;
    }

    public void setCommissionChange(BigDecimal commissionChange) {
        this.commissionChange = commissionChange;
    }

    @Column(name = "COMMISSION_CHANGE_CAFE", nullable = true)
    public BigDecimal getCommissionChangeCafe() {
        return commissionChangeCafe;
    }

    public void setCommissionChangeCafe(BigDecimal commissionChangeCafe) {
        this.commissionChangeCafe = commissionChangeCafe;
    }

    @Column(name = "PAYROLL_PROCESSING_FEES", nullable = true)
    public BigDecimal getPayrollProcessingFee() {
        return payrollProcessingFee;
    }

    public void setPayrollProcessingFee(BigDecimal payrollProcessingFee) {
        this.payrollProcessingFee = payrollProcessingFee;
    }

    @Column(name = "NEWSPAPER_CHARGES", nullable = true)
    public BigDecimal getNewsPaper() {
        return newsPaper;
    }

    public void setNewsPaper(BigDecimal newsPaper) {
        this.newsPaper = newsPaper;
    }

    @Column(name = "STAFF_WELFARE_EXPENSES", nullable = true)
    public BigDecimal getStaffWelfareExpenses() {
        return staffWelfareExpenses;
    }

    public void setStaffWelfareExpenses(BigDecimal staffWelfareExpenses) {
        this.staffWelfareExpenses = staffWelfareExpenses;
    }

    @Column(name = "COURIER_CHARGES", nullable = true)
    public BigDecimal getCourierCharges() {
        return courierCharges;
    }

    public void setCourierCharges(BigDecimal courierCharges) {
        this.courierCharges = courierCharges;
    }

    @Column(name = "PRINTING_AND_STATIONARY", nullable = true)
    public BigDecimal getPrintingAndStationary() {
        return printingAndStationary;
    }

    public void setPrintingAndStationary(BigDecimal printingAndStationary) {
        this.printingAndStationary = printingAndStationary;
    }

    @Column(name = "BUSINESS_PROMOTION", nullable = true)
    public BigDecimal getBusinessPromotion() {
        return businessPromotion;
    }

    public void setBusinessPromotion(BigDecimal businessPromotion) {
        this.businessPromotion = businessPromotion;
    }

    @Column(name = "LEGAL_CHARGES", nullable = true)
    public BigDecimal getLegalCharges() {
        return legalCharges;
    }

    public void setLegalCharges(BigDecimal legalCharges) {
        this.legalCharges = legalCharges;
    }

    @Column(name = "PROFESSIONAL_CHARGES", nullable = true)
    public BigDecimal getProfessionalCharges() {
        return professionalCharges;
    }

    public void setProfessionalCharges(BigDecimal professionalCharges) {
        this.professionalCharges = professionalCharges;
    }

    @Column(name = "PROPERTY_TAX", nullable = true)
    public BigDecimal getPropertyTax() {
        return propertyTax;
    }

    public void setPropertyTax(BigDecimal propertyTax) {
        this.propertyTax = propertyTax;
    }

    @Column(name = "PRE_OPENING_LICENSES_FEES", nullable = true)
    public BigDecimal getOpeningLicencesFees() {
        return openingLicencesFees;
    }

    public void setOpeningLicencesFees(BigDecimal openingLicencesFees) {
        this.openingLicencesFees = openingLicencesFees;
    }

//	@Column(name = "REGISTRATION_CHARGES", nullable = true)
//	public BigDecimal getRegistrationCharges() {
//		return registrationCharges;
//	}
//
//	public void setRegistrationCharges(BigDecimal registrationCharges) {
//		this.registrationCharges = registrationCharges;
//	}
//
//	@Column(name = "STAMP_DUTY_CHARGES", nullable = true)
//	public BigDecimal getStampDutyCharges() {
//		return stampDutyCharges;
//	}
//
//	public void setStampDutyCharges(BigDecimal stampDutyCharges) {
//		this.stampDutyCharges = stampDutyCharges;
//	}

    @Column(name = "DESIGNING_FEES", nullable = true)
    public BigDecimal getDesigningFees() {
        return designingFees;
    }

    public void setDesigningFees(BigDecimal designingFees) {
        this.designingFees = designingFees;
    }

    @Column(name = "FIXED_PARKING_CHARGES", nullable = true)
    public BigDecimal getFixedParkingCharges() {
        return fixedParkingCharges;
    }

    public void setFixedParkingCharges(BigDecimal fixedParkingCharges) {
        this.fixedParkingCharges = fixedParkingCharges;
    }

    @Column(name = "COGS_LOGISTIC", nullable = true)
    public BigDecimal getCogsLogistics() {
        return cogsLogistics;
    }

    public void setCogsLogistics(BigDecimal COGsLogistics) {
        this.cogsLogistics = COGsLogistics;
    }

    @Column(name = "PRE_OPENING_CAM_ELE_WATER", nullable = true)
    public BigDecimal getPreOpeningCamEleWater() {
        return preOpeningCamEleWater;
    }

    public void setPreOpeningCamEleWater(BigDecimal preOpeningCamEleWater) {
        this.preOpeningCamEleWater = preOpeningCamEleWater;
    }

    @Column(name = "PRE_OPENING_REGISTRATION_CHARGES", nullable = true)
    public BigDecimal getPreOpeningRegistrationCharges() {
        return preOpeningRegistrationCharges;
    }

    public void setPreOpeningRegistrationCharges(BigDecimal preOpeningRegistrationCharges) {
        this.preOpeningRegistrationCharges = preOpeningRegistrationCharges;
    }

    @Column(name = "PRE_OPENING_STAMP_DUTY_CHARGES", nullable = true)
    public BigDecimal getPreOpeningStampDutyCharges() {
        return preOpeningStampDutyCharges;
    }

    public void setPreOpeningStampDutyCharges(BigDecimal preOpeningStampDutyCharges) {
        this.preOpeningStampDutyCharges = preOpeningStampDutyCharges;
    }

    @Column(name = "PRE_OPENING_CONSUMABLE_TAX", nullable = true)
    public BigDecimal getPreOpeningConsumableTax() {
        return preOpeningConsumableTax;
    }

    public void setPreOpeningConsumableTax(BigDecimal preOpeningConsumableTax) {
        this.preOpeningConsumableTax = preOpeningConsumableTax;
    }

    @Column(name = "SUPPORT", nullable = true)
    public BigDecimal getSupport() {
        return support;
    }

    public void setSupport(BigDecimal support) {
        this.support = support;
    }

    @Column(name = "CORPORATE_MARKETING_CHANNEL_PARTNER", nullable = true)
    public BigDecimal getCorporateMarketingChannelPartner() {
        return corporateMarketingChannelPartner;
    }

    public void setCorporateMarketingChannelPartner(BigDecimal corporateMarketingChannelPartner) {
        this.corporateMarketingChannelPartner = corporateMarketingChannelPartner;
    }

    @Column(name = "PRE_OPENING_ELE_WATER", nullable = true)
    public BigDecimal getPreOpeningEleWater() {
        return preOpeningEleWater;
    }

    public void setPreOpeningEleWater(BigDecimal preOpeningEleWater) {
        this.preOpeningEleWater = preOpeningEleWater;
    }

    @Column(name = "PRE_OPENING_CAM", nullable = true)
    public BigDecimal getPreOpeningCam() {
        return preOpeningCam;
    }

    public void setPreOpeningCam(BigDecimal preOpeningCam) {
        this.preOpeningCam = preOpeningCam;
    }

    @Column(name = "INTEREST_ON_FIXED_DEPOSIT_FICO", nullable = true)
    public BigDecimal getInterestOnFixedDepositFICO() {
        return interestOnFixedDepositFICO;
    }

    public void setInterestOnFixedDepositFICO(BigDecimal interestOnFixedDepositFICO) {
        this.interestOnFixedDepositFICO = interestOnFixedDepositFICO;
    }


    @Column(name = "LIABILITY_NO_LONGER_REQUIRED_WRITTEN_BACK", nullable = true)
    public BigDecimal getLiabilityNoLongerRequiredWrittenBack() {
        return liabilityNoLongerRequiredWrittenBack;
    }

    public void setLiabilityNoLongerRequiredWrittenBack(BigDecimal liabilityNoLongerRequiredWrittenBack) {
        this.liabilityNoLongerRequiredWrittenBack = liabilityNoLongerRequiredWrittenBack;
    }


    @Column(name = "INTEREST_ON_TERM_LOAN", nullable = true)
    public BigDecimal getInterestOnTermLoan() {
        return interestOnTermLoan;
    }

    public void setInterestOnTermLoan(BigDecimal interestOnTermLoan) {
        this.interestOnTermLoan = interestOnTermLoan;
    }

    @Column(name = "AMORTIZATION_OF_INTANGIBLE_ASSETS", nullable = true)
    public BigDecimal getAmortizationOfIntangibleAssets() {
        return amortizationOfIntangibleAssets;
    }

    public void setAmortizationOfIntangibleAssets(BigDecimal amortizationOfIntangibleAssets) {
        this.amortizationOfIntangibleAssets = amortizationOfIntangibleAssets;
    }

    @Column(name = "BUILDING_MAINTENANCE_CAFE", nullable = true)
    public BigDecimal getBuildingMaintenance() {
        return buildingMaintenance;
    }

    public void setBuildingMaintenance(BigDecimal buildingMaintenance) {
        this.buildingMaintenance = buildingMaintenance;
    }

    @Column(name = "CLEANING_CHARGES", nullable = true)
    public BigDecimal getCleaningCharges() {
        return cleaningCharges;
    }

    public void setCleaningCharges(BigDecimal cleaningCharges) {
        this.cleaningCharges = cleaningCharges;
    }

    @Column(name = "COMPUTER_IT_MAINTENANCE_CAFE", nullable = true)
    public BigDecimal getComputerMaintenance() {
        return computerMaintenance;
    }

    public void setComputerMaintenance(BigDecimal computerMaintenance) {
        this.computerMaintenance = computerMaintenance;
    }

    @Column(name = "PEST_CONTROL_CHARGES", nullable = true)
    public BigDecimal getPestControlCharges() {
        return pestControlCharges;
    }

    public void setPestControlCharges(BigDecimal pestControlCharges) {
        this.pestControlCharges = pestControlCharges;
    }

    @Column(name = "EQUIPMENT_MAINTENANCE_CAFE", nullable = true)
    public BigDecimal getEquipmentMaintenance() {
        return equipmentMaintenance;
    }

    public void setEquipmentMaintenance(BigDecimal equipmentMaintenance) {
        this.equipmentMaintenance = equipmentMaintenance;
    }

    @Column(name = "PRONTO_AMC", nullable = true)
    public BigDecimal getProntoAMC() {
        return prontoAMC;
    }

    public void setProntoAMC(BigDecimal prontoAMC) {
        this.prontoAMC = prontoAMC;
    }

    @Column(name = "RO_AMC", nullable = true)
    public BigDecimal getRoAMC() {
        return roAMC;
    }

    public void setRoAMC(BigDecimal roAMC) {
        this.roAMC = roAMC;
    }

    @Column(name = "DG_RENTAL", nullable = true)
    public BigDecimal getDgRental() {
        return dgRental;
    }

    public void setDgRental(BigDecimal dgRental) {
        this.dgRental = dgRental;
    }

    @Column(name = "EDC_RENTAL", nullable = true)
    public BigDecimal getEdcRental() {
        return edcRental;
    }

    public void setEdcRental(BigDecimal edcRental) {
        this.edcRental = edcRental;
    }

    @Column(name = "SYSTEM_RENTAL", nullable = true)
    public BigDecimal getSystemRental() {
        return systemRental;
    }

    public void setSystemRental(BigDecimal systemRental) {
        this.systemRental = systemRental;
    }

    @Column(name = "RO_RENTAL", nullable = true)
    public BigDecimal getRoRental() {
        return roRental;
    }

    public void setRoRental(BigDecimal roRental) {
        this.roRental = roRental;
    }

    @Column(name = "INSURANCE_ASSETS", nullable = true)
    public BigDecimal getInsuranceAssets() {
        return insuranceAssets;
    }

    public void setInsuranceAssets(BigDecimal insuranceAssets) {
        this.insuranceAssets = insuranceAssets;
    }

    @Column(name = "INSURANCE_CGL", nullable = true)
    public BigDecimal getInsuranceCGL() {
        return insuranceCGL;
    }

    public void setInsuranceCGL(BigDecimal insuranceCGL) {
        this.insuranceCGL = insuranceCGL;
    }

    @Column(name = "INSURANCE_D_AND_O", nullable = true)
    public BigDecimal getInsuranceDnO() {
        return insuranceDnO;
    }

    public void setInsuranceDnO(BigDecimal insuranceDnO) {
        this.insuranceDnO = insuranceDnO;
    }

    @Column(name = "ODC_RENTAL", nullable = true)
    public BigDecimal getOdcRental() {
        return odcRental;
    }

    public void setOdcRental(BigDecimal odcRental) {
        this.odcRental = odcRental;
    }

    @Column(name = "PROPERTY_FIX_RENT", nullable = true)
    public BigDecimal getPropertyFixRent() {
        return propertyFixRent;
    }

    public void setPropertyFixRent(BigDecimal propertyFixRent) {
        this.propertyFixRent = propertyFixRent;
    }

    @Column(name = "REVENUE_SHARE", nullable = true)
    public BigDecimal getRevenueShare() {
        return revenueShare;
    }

    public void setRevenueShare(BigDecimal revenueShare) {
        this.revenueShare = revenueShare;
    }

    @Column(name = "REVENUE_SHARE_DINE_IN", nullable = true)
    public BigDecimal getRevenueShareDineIn() {
        return revenueShareDineIn;
    }

    public void setRevenueShareDineIn(BigDecimal revenueShareDineIn) {
        this.revenueShareDineIn = revenueShareDineIn;
    }

    @Column(name = "REVENUE_SHARE_DELIVERY", nullable = true)
    public BigDecimal getRevenueShareDelivery() {
        return revenueShareDelivery;
    }

    public void setRevenueShareDelivery(BigDecimal revenueShareDelivery) {
        this.revenueShareDelivery = revenueShareDelivery;
    }


    @Column(name = "REVENUE_SHARE_PERCENTAGE", nullable = true)
    public BigDecimal getRevenueSharePercent() {
        return revenueSharePercent;
    }

    public void setRevenueSharePercent(BigDecimal revenueSharePercentage) {
        this.revenueSharePercent = revenueSharePercentage;
    }

    @Column(name = "REVENUE_SHARE_DINE_IN_PERCENTAGE", nullable = true)
    public BigDecimal getRevenueShareDineInPercent() {
        return revenueShareDineInPercent;
    }

    public void setRevenueShareDineInPercent(BigDecimal revenueShareDineInPercent) {
        this.revenueShareDineInPercent = revenueShareDineInPercent;
    }

    @Column(name = "REVENUE_SHARE_DELIVERY_PERCENTAGE", nullable = true)
    public BigDecimal getRevenueShareDeliveryPercent() {
        return revenueShareDeliveryPercent;
    }

    public void setRevenueShareDeliveryPercent(BigDecimal revenueShareDeliveryPercent) {
        this.revenueShareDeliveryPercent = revenueShareDeliveryPercent;
    }

    @Column(name = "FIX_CAM", nullable = true)
    public BigDecimal getFixCAM() {
        return fixCAM;
    }

    public void setFixCAM(BigDecimal fixCAM) {
        this.fixCAM = fixCAM;
    }

    @Column(name = "CHILLING_CHARGES", nullable = true)
    public BigDecimal getChillingCharges() {
        return chillingCharges;
    }

    public void setChillingCharges(BigDecimal chillingCharges) {
        this.chillingCharges = chillingCharges;
    }

    @Column(name = "MARKETING_CHARGES", nullable = true)
    public BigDecimal getMarketingCharges() {
        return marketingCharges;
    }

    public void setMarketingCharges(BigDecimal marketingCharges) {
        this.marketingCharges = marketingCharges;
    }

    @Column(name = "PETTY_CASH_RENTALS", nullable = true)
    public BigDecimal getPettyCashRentals() {
        return pettyCashRentals;
    }

    public void setPettyCashRentals(BigDecimal pettyCashRentals) {
        this.pettyCashRentals = pettyCashRentals;
    }

    @Column(name = "MUSIC_RENTALS", nullable = true)
    public BigDecimal getMusicRentals() {
        return musicRentals;
    }

    public void setMusicRentals(BigDecimal musicRentals) {
        this.musicRentals = musicRentals;
    }

    @Column(name = "INTERNET_PARTNER_RENTAL", nullable = true)
    public BigDecimal getInternetPartnerRental() {
        return internetPartnerRental;
    }

    public void setInternetPartnerRental(BigDecimal internetPartnerRental) {
        this.internetPartnerRental = internetPartnerRental;
    }

    @Column(name = "SUPPORT_OPS_MANAGEMENT", nullable = true)
    public BigDecimal getSupportOpsManagement() {
        return supportOpsManagement;
    }

    public void setSupportOpsManagement(BigDecimal supportOpsManagement) {
        this.supportOpsManagement = supportOpsManagement;
    }

    @Column(name = "TECHNOLOGY_PLATFORM_CHARGES", nullable = true)
    public BigDecimal getTechologyPlatformCharges() {
        return techologyPlatformCharges;
    }

    public void setTechologyPlatformCharges(BigDecimal techologyPlatformCharges) {
        this.techologyPlatformCharges = techologyPlatformCharges;
    }

    @Column(name = "TECHNOLOGY_TRAINING", nullable = true)
    public BigDecimal getTechologyTraining() {
        return techologyTraining;
    }

    public void setTechologyTraining(BigDecimal techologyTraining) {
        this.techologyTraining = techologyTraining;
    }

    @Column(name = "TECHNOLOGY_OTHERS", nullable = true)
    public BigDecimal getTechologyOthers() {
        return techologyOthers;
    }

    public void setTechologyOthers(BigDecimal techologyOthers) {
        this.techologyOthers = techologyOthers;
    }

    @Column(name = "CORPORATE_MARKETING_DIGITAL", nullable = true)
    public BigDecimal getCorporateMarketingDigital() {
        return corporateMarketingDigital;
    }

    public void setCorporateMarketingDigital(BigDecimal corporateMarketingDigital) {
        this.corporateMarketingDigital = corporateMarketingDigital;
    }

    @Column(name = "CORPORATE_MARKETING_AD_OFFLINE", nullable = true)
    public BigDecimal getCorporateMarketingAdvOffline() {
        return corporateMarketingAdvOffline;
    }

    public void setCorporateMarketingAdvOffline(BigDecimal corporateMarketingAdvOffline) {
        this.corporateMarketingAdvOffline = corporateMarketingAdvOffline;
    }

    @Column(name = "CORPORATE_MARKETING_AD_ONLINE", nullable = true)
    public BigDecimal getCorporateMarketingAdvOnline() {
        return corporateMarketingAdvOnline;
    }

    public void setCorporateMarketingAdvOnline(BigDecimal corporateMarketingAdvOnline) {
        this.corporateMarketingAdvOnline = corporateMarketingAdvOnline;
    }

    @Column(name = "CORPORATE_MARKETING_OUTDOOR", nullable = true)
    public BigDecimal getCorporateMarketingOutdoor() {
        return corporateMarketingOutdoor;
    }

    public void setCorporateMarketingOutdoor(BigDecimal corporateMarketingOutdoor) {
        this.corporateMarketingOutdoor = corporateMarketingOutdoor;
    }

    @Column(name = "CORPORATE_MARKETING_AGENCY_FEES", nullable = true)
    public BigDecimal getCorporateMarketingAgencyFees() {
        return corporateMarketingAgencyFees;
    }

    public void setCorporateMarketingAgencyFees(BigDecimal corporateMarketingAgencyFees) {
        this.corporateMarketingAgencyFees = corporateMarketingAgencyFees;
    }

    @Column(name = "VARIANCE_ZERO", nullable = true)
    public BigDecimal getVarianceZero() {
        return varianceZero;
    }

    public void setVarianceZero(BigDecimal varianceZero) {
        this.varianceZero = varianceZero;
    }

    @Column(name = "VARIANCE_ZERO_TAX", nullable = true)
    public BigDecimal getVarianceZeroTax() {
        return varianceZeroTax;
    }

    public void setVarianceZeroTax(BigDecimal varianceZeroTax) {
        this.varianceZeroTax = varianceZeroTax;
    }

    public void setTechnologyVariable(BigDecimal technologyVariable) {
        this.technologyVariable = technologyVariable;
    }

    @Column(name = "TECHNOLOGY_POS", nullable = true)
    public BigDecimal getTechnologyVariable() {
        return technologyVariable;
    }

    @Column(name = "DELIVERY_CHARGES_VARIABLE", nullable = true)
    public BigDecimal getDeliveryChargesVariable() {
        return deliveryChargesVariable;
    }

    public void setDeliveryChargesVariable(BigDecimal deliveryChargesVariable) {
        this.deliveryChargesVariable = deliveryChargesVariable;
    }

    @Column(name = "ON_REVENUE_SHARE", nullable = true)
    public String getOnRevenueShare() {
        return onRevenueShare;
    }

    public void setOnRevenueShare(String onRevenueShare) {
        this.onRevenueShare = onRevenueShare;
    }

    @Column(name = "ELECTRICITY_METER_1_FIXED_CHARGE", nullable = true)
    public BigDecimal getElectricityMeter1FixedCharge() {
        return electricityMeter1FixedCharge;
    }

    public void setElectricityMeter1FixedCharge(BigDecimal electricityMeter1FixedCharge) {
        this.electricityMeter1FixedCharge = electricityMeter1FixedCharge;
    }

    @Column(name = "ELECTRICITY_METER_1_PER_UNIT_CHARGE", nullable = true)
    public BigDecimal getElectricityMeter1PerUnitCharge() {
        return electricityMeter1PerUnitCharge;
    }

    public void setElectricityMeter1PerUnitCharge(BigDecimal electricityMeter1PerUnitCharge) {
        this.electricityMeter1PerUnitCharge = electricityMeter1PerUnitCharge;
    }

    @Column(name = "ELECTRICITY_METER_1_TAX_PERCENTAGE", nullable = true)
    public BigDecimal getElectricityMeter1TaxPercentage() {
        return electricityMeter1TaxPercentage;
    }

    public void setElectricityMeter1TaxPercentage(BigDecimal electricityMeter1TaxPercentage) {
        this.electricityMeter1TaxPercentage = electricityMeter1TaxPercentage;
    }

    @Column(name = "ELECTRICITY_METER_1_OTHER_CHARGE", nullable = true)
    public BigDecimal getElectricityMeter1OtherCharge() {
        return electricityMeter1OtherCharge;
    }

    public void setElectricityMeter1OtherCharge(BigDecimal electricityMeter1OtherCharge) {
        this.electricityMeter1OtherCharge = electricityMeter1OtherCharge;
    }

    @Column(name = "ELECTRICITY_METER_2_FIXED_CHARGE", nullable = true)
    public BigDecimal getElectricityMeter2FixedCharge() {
        return electricityMeter2FixedCharge;
    }

    public void setElectricityMeter2FixedCharge(BigDecimal electricityMeter2FixedCharge) {
        this.electricityMeter2FixedCharge = electricityMeter2FixedCharge;
    }

    @Column(name = "ELECTRICITY_METER_2_PER_UNIT_CHARGE", nullable = true)
    public BigDecimal getElectricityMeter2PerUnitCharge() {
        return electricityMeter2PerUnitCharge;
    }

    public void setElectricityMeter2PerUnitCharge(BigDecimal electricityMeter2PerUnitCharge) {
        this.electricityMeter2PerUnitCharge = electricityMeter2PerUnitCharge;
    }

    @Column(name = "ELECTRICITY_METER_2_TAX_PERCENTAGE", nullable = true)
    public BigDecimal getElectricityMeter2TaxPercentage() {
        return electricityMeter2TaxPercentage;
    }

    public void setElectricityMeter2TaxPercentage(BigDecimal electricityMeter2TaxPercentage) {
        this.electricityMeter2TaxPercentage = electricityMeter2TaxPercentage;
    }

    @Column(name = "ELECTRICITY_METER_2_OTHER_CHARGE", nullable = true)
    public BigDecimal getElectricityMeter2OtherCharge() {
        return electricityMeter2OtherCharge;
    }

    public void setElectricityMeter2OtherCharge(BigDecimal electricityMeter2OtherCharge) {
        this.electricityMeter2OtherCharge = electricityMeter2OtherCharge;
    }

    @Column(name = "ELECTRICITY_METER_3_FIXED_CHARGE", nullable = true)
    public BigDecimal getElectricityMeter3FixedCharge() {
        return electricityMeter3FixedCharge;
    }

    public void setElectricityMeter3FixedCharge(BigDecimal electricityMeter3FixedCharge) {
        this.electricityMeter3FixedCharge = electricityMeter3FixedCharge;
    }

    @Column(name = "ELECTRICITY_METER_3_PER_UNIT_CHARGE", nullable = true)
    public BigDecimal getElectricityMeter3PerUnitCharge() {
        return electricityMeter3PerUnitCharge;
    }

    public void setElectricityMeter3PerUnitCharge(BigDecimal electricityMeter3PerUnitCharge) {
        this.electricityMeter3PerUnitCharge = electricityMeter3PerUnitCharge;
    }

    @Column(name = "ELECTRICITY_METER_3_TAX_PERCENTAGE", nullable = true)
    public BigDecimal getElectricityMeter3TaxPercentage() {
        return electricityMeter3TaxPercentage;
    }

    public void setElectricityMeter3TaxPercentage(BigDecimal electricityMeter3TaxPercentage) {
        this.electricityMeter3TaxPercentage = electricityMeter3TaxPercentage;
    }

    @Column(name = "ELECTRICITY_METER_3_OTHER_CHARGE", nullable = true)
    public BigDecimal getElectricityMeter3OtherCharge() {
        return electricityMeter3OtherCharge;
    }

    public void setElectricityMeter3OtherCharge(BigDecimal electricityMeter3OtherCharge) {
        this.electricityMeter3OtherCharge = electricityMeter3OtherCharge;
    }

    @Column(name = "DG_METER_FIXED_CHARGE", nullable = true)
    public BigDecimal getDgMeterFixedCharge() {
        return dgMeterFixedCharge;
    }

    public void setDgMeterFixedCharge(BigDecimal dgMeterFixedCharge) {
        this.dgMeterFixedCharge = dgMeterFixedCharge;
    }

    @Column(name = "DG_METER_PER_UNIT_CHARGE", nullable = true)
    public BigDecimal getDgMeterPerUnitCharge() {
        return dgMeterPerUnitCharge;
    }

    public void setDgMeterPerUnitCharge(BigDecimal dgMeterPerUnitCharge) {
        this.dgMeterPerUnitCharge = dgMeterPerUnitCharge;
    }

    @Column(name = "DG_METER_TAX_PERCENTAGE", nullable = true)
    public BigDecimal getDgMeterTaxPercentage() {
        return dgMeterTaxPercentage;
    }

    public void setDgMeterTaxPercentage(BigDecimal dgMeterTaxPercentage) {
        this.dgMeterTaxPercentage = dgMeterTaxPercentage;
    }

    @Column(name = "DG_METER_OTHER_CHARGE", nullable = true)
    public BigDecimal getDgMeterOtherCharge() {
        return dgMeterOtherCharge;
    }

    public void setDgMeterOtherCharge(BigDecimal dgMeterOtherCharge) {
        this.dgMeterOtherCharge = dgMeterOtherCharge;
    }

    @Column(name = "CONVEYANCE_MARKETING", nullable = true)
    public BigDecimal getConveyanceMarketing() {
        return conveyanceMarketing;
    }

    public void setConveyanceMarketing(BigDecimal conveyanceMarketing) {
        this.conveyanceMarketing = conveyanceMarketing;
    }

    @Column(name = "CONVEYANCE_OPERATION", nullable = true)
    public BigDecimal getConveyanceOperations() {
        return conveyanceOperations;
    }

    public void setConveyanceOperations(BigDecimal conveyanceOperations) {
        this.conveyanceOperations = conveyanceOperations;
    }

    @Column(name = "CONVEYANCE_OTHERS", nullable = true)
    public BigDecimal getConveyanceOthers() {
        return conveyanceOthers;
    }

    public void setConveyanceOthers(BigDecimal conveyanceOthers) {
        this.conveyanceOthers = conveyanceOthers;
    }

    @Column(name = "AUDIT_FEE", nullable = true)
    public BigDecimal getAuditFee() {
        return auditFee;
    }

    public void setAuditFee(BigDecimal auditFee) {
        this.auditFee = auditFee;
    }

    @Column(name = "AUDIT_FEE_OUT_OF_POCKET", nullable = true)
    public BigDecimal getAuditFeeOutOfPocket() {
        return auditFeeOutOfPocket;
    }

    public void setAuditFeeOutOfPocket(BigDecimal auditFeeOutOfPocket) {
        this.auditFeeOutOfPocket = auditFeeOutOfPocket;
    }

//	@Column(name = "BAD_DEBTS_WRITTEN_OFF", nullable = true)
//	public BigDecimal getBadDebtsWrittenOff() {
//		return badDebtsWrittenOff;
//	}
//
//	public void setBadDebtsWrittenOff(BigDecimal badDebtsWrittenOff) {
//		this.badDebtsWrittenOff = badDebtsWrittenOff;
//	}

    @Column(name = "BROKERAGE", nullable = true)
    public BigDecimal getBrokerage() {
        return brokerage;
    }

    public void setBrokerage(BigDecimal brokerage) {
        this.brokerage = brokerage;
    }

    @Column(name = "CHARITY_AND_DONATIONS", nullable = true)
    public BigDecimal getCharityAndDonations() {
        return charityAndDonations;
    }

    public void setCharityAndDonations(BigDecimal charityAndDonations) {
        this.charityAndDonations = charityAndDonations;
    }

    @Column(name = "DOMESTIC_TICKETS_AND_HOTELS", nullable = true)
    public BigDecimal getDomesticTicketsAndHotels() {
        return domesticTicketsAndHotels;
    }

    public void setDomesticTicketsAndHotels(BigDecimal domesticTicketsAndHotels) {
        this.domesticTicketsAndHotels = domesticTicketsAndHotels;
    }

    @Column(name = "INTERNATIONAL_TICKETS_AND_HOTELS", nullable = true)
    public BigDecimal getInternationalTicketsAndHotels() {
        return internationalTicketsAndHotels;
    }

    public void setInternationalTicketsAndHotels(BigDecimal internationalTicketsAndHotels) {
        this.internationalTicketsAndHotels = internationalTicketsAndHotels;
    }

    @Column(name = "HOUSEKEEPING_CHARGES", nullable = true)
    public BigDecimal getHouseKeepingCharges() {
        return houseKeepingCharges;
    }

    public void setHouseKeepingCharges(BigDecimal houseKeepingCharges) {
        this.houseKeepingCharges = houseKeepingCharges;
    }

    @Column(name = "LATE_FEE_CHARGES", nullable = true)
    public BigDecimal getLateFeeCharges() {
        return lateFeeCharges;
    }

    public void setLateFeeCharges(BigDecimal lateFeeCharges) {
        this.lateFeeCharges = lateFeeCharges;
    }

    @Column(name = "MARKETING_DATA_ANALYSIS", nullable = true)
    public BigDecimal getMarketingDataAnalysis() {
        return marketingDataAnalysis;
    }

    public void setMarketingDataAnalysis(BigDecimal marketingDataAnalysis) {
        this.marketingDataAnalysis = marketingDataAnalysis;
    }

    @Column(name = "MISCELLANEOUS_EXPENSES", nullable = true)
    public BigDecimal getMiscellaneousExpenses() {
        return miscellaneousExpenses;
    }

    public void setMiscellaneousExpenses(BigDecimal miscellaneousExpenses) {
        this.miscellaneousExpenses = miscellaneousExpenses;
    }

    @Column(name = "PENALTY", nullable = true)
    public BigDecimal getPenalty() {
        return penalty;
    }

    public void setPenalty(BigDecimal penalty) {
        this.penalty = penalty;
    }

    @Column(name = "PHOTO_COPY_EXPENSES", nullable = true)
    public BigDecimal getPhotoCopyExpenses() {
        return photoCopyExpenses;
    }

    public void setPhotoCopyExpenses(BigDecimal photoCopyExpenses) {
        this.photoCopyExpenses = photoCopyExpenses;
    }

    @Column(name = "QCR_EXPENSE", nullable = true)
    public BigDecimal getQcrExpense() {
        return qcrExpense;
    }

    public void setQcrExpense(BigDecimal qcrExpense) {
        this.qcrExpense = qcrExpense;
    }

    @Column(name = "RECRUITMENT_CONSULTANTS", nullable = true)
    public BigDecimal getRecuritmentConsultants() {
        return recuritmentConsultants;
    }

    public void setRecuritmentConsultants(BigDecimal recuritmentConsultants) {
        this.recuritmentConsultants = recuritmentConsultants;
    }

    @Column(name = "ROC_FEES", nullable = true)
    public BigDecimal getRocFees() {
        return rocFees;
    }

    public void setRocFees(BigDecimal rocFees) {
        this.rocFees = rocFees;
    }

    @Column(name = "TRAVELLING_EXPENSE_ODC", nullable = true)
    public BigDecimal getTravellingExpenseODC() {
        return travellingExpenseODC;
    }

    public void setTravellingExpenseODC(BigDecimal travellingExpenceODC) {
        this.travellingExpenseODC = travellingExpenceODC;
    }

    @Column(name = "TRAVELLING_EXPENSES", nullable = true)
    public BigDecimal getTravellingExpense() {
        return travellingExpense;
    }

    public void setTravellingExpense(BigDecimal travellingExpence) {
        this.travellingExpense = travellingExpence;
    }

    @Column(name = "DEBIT_CREDIT_WRITTEN_OFF", nullable = true)
    public BigDecimal getDebitCreditWrittenOff() {
        return debitCreditWrittenOff;
    }

    public void setDebitCreditWrittenOff(BigDecimal debitCreditWrittenOff) {
        this.debitCreditWrittenOff = debitCreditWrittenOff;
    }

    @Column(name = "DIFFENECE_IN_EXCHANGE", nullable = true)
    public BigDecimal getDifferenceInExchange() {
        return differenceInExchange;
    }

    public void setDifferenceInExchange(BigDecimal differenceInExchange) {
        this.differenceInExchange = differenceInExchange;
    }

    @Column(name = "SERVICE_CHARGE_PAID", nullable = true)
    public BigDecimal getServiceChargesPaid() {
        return serviceChargesPaid;
    }

    public void setServiceChargesPaid(BigDecimal serviceChargesPaid) {
        this.serviceChargesPaid = serviceChargesPaid;
    }

    @Column(name = "INSURANCE_VEHICLE", nullable = true)
    public BigDecimal getInsuranceVehicle() {
        return insuranceVehicle;
    }

    public void setInsuranceVehicle(BigDecimal insuranceVehicle) {
        this.insuranceVehicle = insuranceVehicle;
    }

    @Column(name = "OTHERS_AMC", nullable = true)
    public BigDecimal getOthersAMC() {
        return othersAMC;
    }

    public void setOthersAMC(BigDecimal othersAMC) {
        this.othersAMC = othersAMC;
    }

    @Column(name = "OTHERS_MAINTENANCE", nullable = true)
    public BigDecimal getOthersMaintenance() {
        return othersMaintenance;
    }

    public void setOthersMaintenance(BigDecimal othersMaintenance) {
        this.othersMaintenance = othersMaintenance;
    }

    @Column(name = "RND_ENGINEERING_EXPENSE", nullable = true)
    public BigDecimal getRnDEngineeringExpenses() {
        return RnDEngineeringExpenses;
    }

    public void setRnDEngineeringExpenses(BigDecimal RnDEngineeringExpenses) {
        this.RnDEngineeringExpenses = RnDEngineeringExpenses;
    }

    @Column(name = "BYOD_CHARGES", nullable = true)
    public BigDecimal getByodCharges() {
        return byodCharges;
    }

    public void setByodCharges(BigDecimal byodCharges) {
        this.byodCharges = byodCharges;
    }

    @Column(name = "CAR_LEASE_SR", nullable = true)
    public BigDecimal getCarLease() {
        return carLease;
    }

    public void setCarLease(BigDecimal carLease) {
        this.carLease = carLease;
    }

    @Column(name = "DRIVER_SALARY_SR", nullable = true)
    public BigDecimal getDriverSalary() {
        return driverSalary;
    }

    public void setDriverSalary(BigDecimal driverSalary) {
        this.driverSalary = driverSalary;
    }

    @Column(name = "GRATUITY_EXPENSE", nullable = true)
    public BigDecimal getGratuity() {
        return gratuity;
    }

    public void setGratuity(BigDecimal gratuity) {
        this.gratuity = gratuity;
    }

    @Column(name = "INSURANCE_ACCIDENTAL", nullable = true)
    public BigDecimal getInsurnaceAccidental() {
        return insurnaceAccidental;
    }

    public void setInsurnaceAccidental(BigDecimal insurnaceAccidental) {
        this.insurnaceAccidental = insurnaceAccidental;
    }

    @Column(name = "INSURANCE_MEDICAL", nullable = true)
    public BigDecimal getInsurnaceMedical() {
        return insurnaceMedical;
    }

    public void setInsurnaceMedical(BigDecimal insurnaceMedical) {
        this.insurnaceMedical = insurnaceMedical;
    }

    @Column(name = "SUPPORT_OPS_TURNOVER", nullable = true)
    public BigDecimal getSupportsOpsTurnover() {
        return supportsOpsTurnover;
    }

    public void setSupportsOpsTurnover(BigDecimal supportsOpsTurnover) {
        this.supportsOpsTurnover = supportsOpsTurnover;
    }

    @Column(name = "EMPLOYEE_FACILITATION_CHARGES", nullable = true)
    public BigDecimal getEmployeeFacilitationExpenses() {
        return employeeFacilitationExpenses;
    }

    public void setEmployeeFacilitationExpenses(BigDecimal employeeFacilitationExpenses) {
        this.employeeFacilitationExpenses = employeeFacilitationExpenses;
    }

    @Column(name = "TELEPHONE_SR", nullable = true)
    public BigDecimal getTelephoneSR() {
        return telephoneSR;
    }

    public void setTelephoneSR(BigDecimal telephoneSR) {
        this.telephoneSR = telephoneSR;
    }

    @Column(name = "VEHICLE_RUNNING_AND_MAINT_SR", nullable = true)
    public BigDecimal getVehicleRunningAndMaintSR() {
        return vehicleRunningAndMaintSR;
    }

    public void setVehicleRunningAndMaintSR(BigDecimal vehicleRunningAndMaintSR) {
        this.vehicleRunningAndMaintSR = vehicleRunningAndMaintSR;
    }

    @Column(name = "EMPLOYEE_STOCK_OPTION_EXPENSE", nullable = true)
    public BigDecimal getEmployeeStockOptionExpense() {
        return employeeStockOptionExpense;
    }

    public void setEmployeeStockOptionExpense(BigDecimal employeeStockOptionExpense) {
        this.employeeStockOptionExpense = employeeStockOptionExpense;
    }

    @Column(name = "EMPLOYER_CONTRIBUTION_LWF", nullable = true)
    public BigDecimal getEmployerContributionLWF() {
        return employerContributionLWF;
    }

    public void setEmployerContributionLWF(BigDecimal employerContributionLWF) {
        this.employerContributionLWF = employerContributionLWF;
    }

    @Column(name = "ESIC_EMPLOYER_CONT", nullable = true)
    public BigDecimal getEsicEmployerCont() {
        return esicEmployerCont;
    }

    public void setEsicEmployerCont(BigDecimal esicEmployerCont) {
        this.esicEmployerCont = esicEmployerCont;
    }

    @Column(name = "LEAVE_TRAVEL_REIMBURSEMENT", nullable = true)
    public BigDecimal getLeaveTravelReimbursement() {
        return leaveTravelReimbursement;
    }

    public void setLeaveTravelReimbursement(BigDecimal leaveTravelReimbursement) {
        this.leaveTravelReimbursement = leaveTravelReimbursement;
    }

    @Column(name = "PF_ADMINISTRATION_CHARGES", nullable = true)
    public BigDecimal getPfAdministrationCharges() {
        return pfAdministrationCharges;
    }

    public void setPfAdministrationCharges(BigDecimal pfAdministrationCharges) {
        this.pfAdministrationCharges = pfAdministrationCharges;
    }

    @Column(name = "PF_EMPLOYER_CONT", nullable = true)
    public BigDecimal getPfEmployerCont() {
        return pfEmployerCont;
    }

    public void setPfEmployerCont(BigDecimal pfEmployerCont) {
        this.pfEmployerCont = pfEmployerCont;
    }

    @Column(name = "QUATERLY_INCENTIVE", nullable = true)
    public BigDecimal getQuarterlyIncentive() {
        return quarterlyIncentive;
    }

    public void setQuarterlyIncentive(BigDecimal quarterlyIncentive) {
        this.quarterlyIncentive = quarterlyIncentive;
    }

    @Column(name = "BUSINESS_PROMOTION_SR", nullable = true)
    public BigDecimal getBusinessPromotionSR() {
        return businessPromotionSR;
    }

    public void setBusinessPromotionSR(BigDecimal businessPromotionSR) {
        this.businessPromotionSR = businessPromotionSR;
    }

//	@Column(name = "SUPPORT_AUDIT", nullable = true)
//	public BigDecimal getSupportAudit() {
//		return supportAudit;
//	}
//
//	public void setSupportAudit(BigDecimal supportAudit) {
//		this.supportAudit = supportAudit;
//	}

    @Column(name = "SUPPORT_CCC", nullable = true)
    public BigDecimal getSupportCCC() {
        return supportCCC;
    }

    public void setSupportCCC(BigDecimal supportCCC) {
        this.supportCCC = supportCCC;
    }

    @Column(name = "SUPPORT_IT", nullable = true)
    public BigDecimal getSupportIT() {
        return supportIT;
    }

    public void setSupportIT(BigDecimal supportIT) {
        this.supportIT = supportIT;
    }

//	@Column(name = "SUPPORT_MAINTENANCE", nullable = true)
//	public BigDecimal getSupportMaintenance() {
//		return supportMaintenance;
//	}
//
//	public void setSupportMaintenance(BigDecimal supportMaintenance) {
//		this.supportMaintenance = supportMaintenance;
//	}

    @Column(name = "SUPPORT_COMM_WH", nullable = true)
    public BigDecimal getSupportCommWH() {
        return supportCommWH;
    }

    public void setSupportCommWH(BigDecimal supportCommWH) {
        this.supportCommWH = supportCommWH;
    }

    @Column(name = "MARKETING_NPI_CAFE", nullable = true)
    public BigDecimal getMarketingNPI() {
        return marketingNPI;
    }

    public void setMarketingNPI(BigDecimal marketingNPI) {
        this.marketingNPI = marketingNPI;
    }

    @Column(name = "CORPORATE_MARKETING_PHOTO", nullable = true)
    public BigDecimal getCorporateMarketingPhotography() {
        return corporateMarketingPhotography;
    }

    public void setCorporateMarketingPhotography(BigDecimal corporateMarketingPhotography) {
        this.corporateMarketingPhotography = corporateMarketingPhotography;
    }

    @Column(name = "CAPITAL_IMPROVEMENT_EXPENSES", nullable = true)
    public BigDecimal getCapitalImprovementExpenses() {
        return capitalImprovementExpenses;
    }

    public void setCapitalImprovementExpenses(BigDecimal capitalImprovementExpenses) {
        this.capitalImprovementExpenses = capitalImprovementExpenses;
    }

    @Column(name = "LEASE_HOLD_IMPROVEMENTS", nullable = true)
    public BigDecimal getLeaseHoldImprovements() {
        return leaseHoldImprovements;
    }

    public void setLeaseHoldImprovements(BigDecimal leaseHoldImprovements) {
        this.leaseHoldImprovements = leaseHoldImprovements;
    }

    @Column(name = "FIXED_ASSETS_EQUIPMENT_CAFE", nullable = true)
    public BigDecimal getFixedAssetsEquipment() {
        return fixedAssetsEquipment;
    }

    public void setFixedAssetsEquipment(BigDecimal fixedAssetsEquipment) {
        this.fixedAssetsEquipment = fixedAssetsEquipment;
    }

    @Column(name = "FIXED_ASSET_FURNITURE_CAFE", nullable = true)
    public BigDecimal getFixedAssetFurniture() {
        return fixedAssetFurniture;
    }

    public void setFixedAssetFurniture(BigDecimal fixedAssetFurniture) {
        this.fixedAssetFurniture = fixedAssetFurniture;
    }

    @Column(name = "FIXED_ASSETS_IT_CAFE", nullable = true)
    public BigDecimal getFixedAssetsIT() {
        return fixedAssetsIT;
    }

    public void setFixedAssetsIT(BigDecimal fixedAssetsIT) {
        this.fixedAssetsIT = fixedAssetsIT;
    }

    @Column(name = "FIXED_ASSETS_KITCHEN_EQUIPMENT_CAFE", nullable = true)
    public BigDecimal getFixedAssetsKitchenEquipment() {
        return fixedAssetsKitchenEquipment;
    }

    public void setFixedAssetsKitchenEquipment(BigDecimal fixedAssetsKitchenEquipment) {
        this.fixedAssetsKitchenEquipment = fixedAssetsKitchenEquipment;
    }

    @Column(name = "FIXED_ASSETS_OFFICE_EQUIPMENT_CAFE", nullable = true)
    public BigDecimal getFixedAssetsOfficeEquipment() {
        return fixedAssetsOfficeEquipment;
    }

    public void setFixedAssetsOfficeEquipment(BigDecimal fixedAssetsOfficeEquipment) {
        this.fixedAssetsOfficeEquipment = fixedAssetsOfficeEquipment;
    }

    @Column(name = "FIXED_ASSETS_VEHICLE", nullable = true)
    public BigDecimal getFixedAssetsVehicle() {
        return fixedAssetsVehicle;
    }

    public void setFixedAssetsVehicle(BigDecimal fixedAssetsVehicle) {
        this.fixedAssetsVehicle = fixedAssetsVehicle;
    }

    @Column(name = "MARKETING_LAUNCH", nullable = true)
    public BigDecimal getMarketingLaunch() {
        return marketingLaunch;
    }

    public void setMarketingLaunch(BigDecimal marketingLaunch) {
        this.marketingLaunch = marketingLaunch;
    }

    @Column(name = "PRE_OPENING_CONSUMABLE", nullable = true)
    public BigDecimal getPreOpeningConsumable() {
        return preOpeningConsumable;
    }

    public void setPreOpeningConsumable(BigDecimal preOpeningConsumable) {
        this.preOpeningConsumable = preOpeningConsumable;
    }

    @Column(name = "PRE_OPENING_OTHERS", nullable = true)
    public BigDecimal getPreOpeningOthers() {
        return preOpeningOthers;
    }

    public void setPreOpeningOthers(BigDecimal preOpeningOthers) {
        this.preOpeningOthers = preOpeningOthers;
    }

    @Column(name = "PRE_OPENING_RENT", nullable = true)
    public BigDecimal getPreOpeningRent() {
        return preOpeningRent;
    }

    public void setPreOpeningRent(BigDecimal preOpeningRent) {
        this.preOpeningRent = preOpeningRent;
    }

    @Column(name = "PRE_OPEINING_SALARY", nullable = true)
    public BigDecimal getPreOpeningSalary() {
        return preOpeningSalary;
    }

    public void setPreOpeningSalary(BigDecimal preOpeningSalary) {
        this.preOpeningSalary = preOpeningSalary;
    }

    @Column(name = "BANK_CHARGES", nullable = true)
    public BigDecimal getBankCharges() {
        return bankCharges;
    }

    public void setBankCharges(BigDecimal bankCharges) {
        this.bankCharges = bankCharges;
    }

    @Column(name = "INTREST_ON_LOAN", nullable = true)
    public BigDecimal getInterestOnLoan() {
        return interestOnLoan;
    }

    public void setInterestOnLoan(BigDecimal interestOnLoan) {
        this.interestOnLoan = interestOnLoan;
    }

    @Column(name = "INTREST_ON_TDS_OR_GST", nullable = true)
    public BigDecimal getIntrestOnTDSorGST() {
        return intrestOnTDSorGST;
    }

    public void setIntrestOnTDSorGST(BigDecimal intrestOnTDSorGST) {
        this.intrestOnTDSorGST = intrestOnTDSorGST;
    }

    @Column(name = "INTEREST_ON_FDR", nullable = true)
    public BigDecimal getInterestOnFDR() {
        return interestOnFDR;
    }

    public void setInterestOnFDR(BigDecimal interestOnFDR) {
        this.interestOnFDR = interestOnFDR;
    }

    @Column(name = "PROFIT_OR_LOSS_SALE_MUTUTAL_FUND", nullable = true)
    public BigDecimal getProfitSaleMutualFunds() {
        return profitSaleMutualFunds;
    }

    public void setProfitSaleMutualFunds(BigDecimal profitSaleMutualFunds) {
        this.profitSaleMutualFunds = profitSaleMutualFunds;
    }

    @Column(name = "INTREST_INCOME_TAX_REFUND", nullable = true)
    public BigDecimal getInterestIncomeTaxRefund() {
        return interestIncomeTaxRefund;
    }

    public void setInterestIncomeTaxRefund(BigDecimal interestIncomeTaxRefund) {
        this.interestIncomeTaxRefund = interestIncomeTaxRefund;
    }

    @Column(name = "MISC_INCOME", nullable = true)
    public BigDecimal getMiscIncome() {
        return miscIncome;
    }

    public void setMiscIncome(BigDecimal MiscIncome) {
        this.miscIncome = MiscIncome;
    }

    @Column(name = "DISCOUNT_RECEIVED", nullable = true)
    public BigDecimal getDiscountReceived() {
        return discountReceived;
    }

    public void setDiscountReceived(BigDecimal discountReceived) {
        this.discountReceived = discountReceived;
    }

    @Column(name = "INTERIOR_DESIGNING_CHARGE", nullable = true)
    public BigDecimal getInteriorDesigningCharge() {
        return interiorDesigningCharge;
    }

    public void setInteriorDesigningCharge(BigDecimal interiorDesigningCharge) {
        this.interiorDesigningCharge = interiorDesigningCharge;
    }

    @Column(name = "SCRAPE_CHARGES", nullable = true)
    public BigDecimal getScrape() {
        return scrape;
    }

    public void setScrape(BigDecimal scrape) {
        this.scrape = scrape;
    }

    @Column(name = "SERVICE_CHARGE", nullable = true)
    public BigDecimal getServiceCharges() {
        return serviceCharges;
    }

    public void setServiceCharges(BigDecimal serviceCharges) {
        this.serviceCharges = serviceCharges;
    }

    @Column(name = "SERVICE_CHARGE_FICO", nullable = true)
    public BigDecimal getServiceChargesFICO() {
        return serviceChargesFICO;
    }

    public void setServiceChargesFICO(BigDecimal serviceChargesFICO) {
        this.serviceChargesFICO = serviceChargesFICO;
    }

    @Column(name = "VARIANCE_PCC", nullable = true)
    public BigDecimal getVariancePCC() {
        return variancePCC;
    }

    public void setVariancePCC(BigDecimal variancePCC) {
        this.variancePCC = variancePCC;
    }

    @Column(name = "VARIANCE_YC", nullable = true)
    public BigDecimal getVarianceYC() {
        return varianceYC;
    }

    public void setVarianceYC(BigDecimal varianceYC) {
        this.varianceYC = varianceYC;
    }

    @Column(name = "COGS_OTHERS", nullable = true)
    public BigDecimal getCogsOthers() {
        return cogsOthers;
    }

    public void setCogsOthers(BigDecimal trainingCogs) {
        this.cogsOthers = trainingCogs;
    }

    @Column(name = "BUDGET_STATUS", nullable = true)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    @Column(name = "GROSS_COST", nullable = true)
    public BigDecimal getGrossCost() {
        return grossCost;
    }

    public void setGrossCost(BigDecimal grossCost) {
        this.grossCost = grossCost;
    }

    @Column(name = "GROSS_PROFIT", nullable = true)
    public BigDecimal getGrossProfit() {
        return grossProfit;
    }

    public void setGrossProfit(BigDecimal grossProfit) {
        this.grossProfit = grossProfit;
    }

    @Column(name = "GROSS_PROFIT_PERCENTAGE", nullable = true)
    public BigDecimal getGrossProfitPercentage() {
        return grossProfitPercentage;
    }

    public void setGrossProfitPercentage(BigDecimal grossProfitPercentage) {
        this.grossProfitPercentage = grossProfitPercentage;
    }

    @Column(name = "TOTAL_COST", nullable = true)
    public BigDecimal getTotalCost() {
        return totalCost;
    }

    public void setTotalCost(BigDecimal totalCost) {
        this.totalCost = totalCost;
    }

    @Column(name = "NET_PROFIT", nullable = true)
    public BigDecimal getNetProfit() {
        return netProfit;
    }

    public void setNetProfit(BigDecimal netProfit) {
        this.netProfit = netProfit;
    }

    @Column(name = "NET_PROFIT_PERCENTAGE", nullable = true)
    public BigDecimal getNetProfitPercentage() {
        return netProfitPercentage;
    }

    public void setNetProfitPercentage(BigDecimal netProfitPercentage) {
        this.netProfitPercentage = netProfitPercentage;
    }

    @Column(name = "ROUNDED_OFF", nullable = true)
    public BigDecimal getRoundedOff() {
        return roundedOff;
    }

    public void setRoundedOff(BigDecimal roundedOff) {
        this.roundedOff = roundedOff;
    }

//	@Column(name = "SHORT_AND_EXCESS", nullable = true)
//	public BigDecimal getShortAndExcess() {
//		return shortAndExcess;
//	}
//
//	public void setShortAndExcess(BigDecimal shortAndExcess) {
//		this.shortAndExcess = shortAndExcess;
//	}

    @Column(name = "UPDATED_BY", nullable = true)
    public Integer getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Integer updatedBy) {
        this.updatedBy = updatedBy;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "UPDATE_TIME", nullable = true, length = 19)
    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }


	/*
		new header for service order
	 */

    @Column(name = "VEHICLE_REGULAR_MAINTENANCE_HQ", nullable = true)
    public BigDecimal getVehicleRegularMaintenanceHq() {
        return vehicleRegularMaintenanceHq;
    }

    public void setVehicleRegularMaintenanceHq(BigDecimal vehicleRegularMaintenanceHq) {
        this.vehicleRegularMaintenanceHq = vehicleRegularMaintenanceHq;
    }

    @Column(name = "BUILDING_MAINTENANCE_HQ", nullable = true)
    public BigDecimal getBuildingMaintenanceHq() {
        return buildingMaintenanceHq;
    }

    public void setBuildingMaintenanceHq(BigDecimal buildingMaintenanceHq) {
        this.buildingMaintenanceHq = buildingMaintenanceHq;
    }

    @Column(name = "COMPUTER_IT_MAINTENANCE_HQ", nullable = true)
    public BigDecimal getComputerItMaintenanceHq() {
        return computerItMaintenanceHq;
    }

    public void setComputerItMaintenanceHq(BigDecimal computerItMaintenanceHq) {
        this.computerItMaintenanceHq = computerItMaintenanceHq;
    }

    @Column(name = "EQUIPMENT_MAINTENANCE_HQ", nullable = true)
    public BigDecimal getEquipmentMaintenanceHq() {
        return equipmentMaintenanceHq;
    }

    public void setEquipmentMaintenanceHq(BigDecimal equipmentMaintenanceHq) {
        this.equipmentMaintenanceHq = equipmentMaintenanceHq;
    }

    @Column(name = "MARKETING_NPI_HQ", nullable = true)
    public BigDecimal getMarketingNpiHq() {
        return marketingNpiHq;
    }

    public void setMarketingNpiHq(BigDecimal marketingNpiHq) {
        this.marketingNpiHq = marketingNpiHq;
    }

    @Column(name = "LICENSE_EXPENSES", nullable = true)
    public BigDecimal getLicenseExpenses() {
        return licenseExpenses;
    }

    public void setLicenseExpenses(BigDecimal licenseExpenses) {
        this.licenseExpenses = licenseExpenses;
    }

    @Column(name = "CORPORATE_MARKETING_ATL_RADIO", nullable = true)
    public BigDecimal getCorporateMarketingAtlRadio() {
        return corporateMarketingAtlRadio;
    }

    public void setCorporateMarketingAtlRadio(BigDecimal corporateMarketingAtlRadio) {
        this.corporateMarketingAtlRadio = corporateMarketingAtlRadio;
    }

    @Column(name = "CORPORATE_MARKETING_ATL_TV", nullable = true)
    public BigDecimal getCorporateMarketingAtlTv() {
        return corporateMarketingAtlTv;
    }

    public void setCorporateMarketingAtlTv(BigDecimal corporateMarketingAtlTv) {
        this.corporateMarketingAtlTv = corporateMarketingAtlTv;
    }

    @Column(name = "CORPORATE_MARKETING_ATL_PRINT_AD", nullable = true)
    public BigDecimal getCorporateMarketingAtlPrintAd() {
        return corporateMarketingAtlPrintAd;
    }

    public void setCorporateMarketingAtlPrintAd(BigDecimal corporateMarketingAtlPrintAd) {
        this.corporateMarketingAtlPrintAd = corporateMarketingAtlPrintAd;
    }

    @Column(name = "CORPORATE_MARKETING_ATL_CINEMA", nullable = true)
    public BigDecimal getCorporateMarketingAtlCinema() {
        return corporateMarketingAtlCinema;
    }

    public void setCorporateMarketingAtlCinema(BigDecimal corporateMarketingAtlCinema) {
        this.corporateMarketingAtlCinema = corporateMarketingAtlCinema;
    }

    @Column(name = "CORPORATE_MARKETING_ATL_DIGITAL", nullable = true)
    public BigDecimal getCorporateMarketingAtlDigital() {
        return corporateMarketingAtlDigital;
    }

    public void setCorporateMarketingAtlDigital(BigDecimal corporateMarketingAtlDigital) {
        this.corporateMarketingAtlDigital = corporateMarketingAtlDigital;
    }

    @Column(name = "LOGISTIC_INTRASTATE_COLD_VEHICLE", nullable = true)
    public BigDecimal getLogisticInterstateColdVehicle() {
        return logisticInterstateColdVehicle;
    }

    public void setLogisticInterstateColdVehicle(BigDecimal logisticInterstateColdVehicle) {
        this.logisticInterstateColdVehicle = logisticInterstateColdVehicle;
    }

    @Column(name = "LOGISTIC_INTRASTATE_NON_COLD_VEHICLE", nullable = true)
    public BigDecimal getLogisticInterstateNonColdVehicle() {
        return logisticInterstateNonColdVehicle;
    }

    public void setLogisticInterstateNonColdVehicle(BigDecimal logisticInterstateNonColdVehicle) {
        this.logisticInterstateNonColdVehicle = logisticInterstateNonColdVehicle;
    }

    @Column(name = "LOGISTIC_INTERSTATE_AIR", nullable = true)
    public BigDecimal getLogisticInterstateAir() {
        return logisticInterstateAir;
    }

    public void setLogisticInterstateAir(BigDecimal logisticInterstateAir) {
        this.logisticInterstateAir = logisticInterstateAir;
    }

    @Column(name = "LOGISTIC_INTERSTATE_ROAD", nullable = true)
    public BigDecimal getLogisticInterstateRoad() {
        return logisticInterstateRoad;
    }

    public void setLogisticInterstateRoad(BigDecimal logisticInterstateRoad) {
        this.logisticInterstateRoad = logisticInterstateRoad;
    }

    @Column(name = "LOGISTIC_INTERSTATE_TRAIN", nullable = true)
    public BigDecimal getLogisticInterstateTrain() {
        return logisticInterstateTrain;
    }

    public void setLogisticInterstateTrain(BigDecimal logisticInterstateTrain) {
        this.logisticInterstateTrain = logisticInterstateTrain;
    }

    @Column(name = "AIR_CONDITIONER_AMC", nullable = true)
    public BigDecimal getAirConditionerAmc() {
        return airConditionerAmc;
    }

    public void setAirConditionerAmc(BigDecimal airConditionerAmc) {
        this.airConditionerAmc = airConditionerAmc;
    }

    @Column(name = "CORPORATE_MARKETING_SMS_EMAIL", nullable = true)
    public BigDecimal getCorporateMarketingSms() {
        return corporateMarketingSms;
    }

    public void setCorporateMarketingSms(BigDecimal corporateMarketingSms) {
        this.corporateMarketingSms = corporateMarketingSms;
    }

    @Column(name = "OTHER_SERVICE_CHARGES", nullable = true)
    public BigDecimal getOtherServiceCharges() {
        return otherServiceCharges;
    }

    public void setOtherServiceCharges(BigDecimal otherServiceCharges) {
        this.otherServiceCharges = otherServiceCharges;
    }

    @Column(name = "BONUS_ATTENDANCE", nullable = true)
    public BigDecimal getBonusAttendance() {
        return bonusAttendance;
    }

    public void setBonusAttendance(BigDecimal bonusAttendance) {
        this.bonusAttendance = bonusAttendance;
    }

    @Column(name = "BONUS_JOINING", nullable = true)
    public BigDecimal getBonusJoining() {
        return bonusJoining;
    }

    public void setBonusJoining(BigDecimal bonusJoining) {
        this.bonusJoining = bonusJoining;
    }

    @Column(name = "BONUS_REFERRAL", nullable = true)
    public BigDecimal getBonusReferral() {
        return bonusReferral;
    }

    public void setBonusReferral(BigDecimal bonusReferral) {
        this.bonusReferral = bonusReferral;
    }

    @Column(name = "BONUS_HOLIDAY", nullable = true)
    public BigDecimal getBonusHoliday() {
        return bonusHoliday;
    }

    public void setBonusHoliday(BigDecimal bonusHoliday) {
        this.bonusHoliday = bonusHoliday;
    }

    @Column(name = "BONUS_OTHERS", nullable = true)
    public BigDecimal getBonusOthers() {
        return bonusOthers;
    }

    public void setBonusOthers(BigDecimal bonusOthers) {
        this.bonusOthers = bonusOthers;
    }

    @Column(name = "ALLOWANCE_REMOTE_LOCATION", nullable = true)
    public BigDecimal getAllowanceRemoteLocation() {
        return allowanceRemoteLocation;
    }

    public void setAllowanceRemoteLocation(BigDecimal allowanceRemoteLocation) {
        this.allowanceRemoteLocation = allowanceRemoteLocation;
    }

    @Column(name = "ALLOWANCE_EMPLOYEE_BENEFIT", nullable = true)
    public BigDecimal getAllowanceEmployeeBenefit() {
        return allowanceEmployeeBenefit;
    }

    public void setAllowanceEmployeeBenefit(BigDecimal allowanceEmployeeBenefit) {
        this.allowanceEmployeeBenefit = allowanceEmployeeBenefit;
    }

    @Column(name = "ALLOWANCE_CITY_COMPENSATORY", nullable = true)
    public BigDecimal getAllowanceCityCompensatory() {
        return allowanceCityCompensatory;
    }

    public void setAllowanceCityCompensatory(BigDecimal allowanceCityCompensatory) {
        this.allowanceCityCompensatory = allowanceCityCompensatory;
    }

    @Column(name = "ALLOWANCE_MONK", nullable = true)
    public BigDecimal getAllowanceMonk() {
        return allowanceMonk;
    }

    public void setAllowanceMonk(BigDecimal allowanceMonk) {
        this.allowanceMonk = allowanceMonk;
    }

    @Column(name = "ALLOWANCE_OTHERS", nullable = true)
    public BigDecimal getAllowanceOthers() {
        return allowanceOthers;
    }

    public void setAllowanceOthers(BigDecimal allowanceOthers) {
        this.allowanceOthers = allowanceOthers;
    }

    @Column(name = "NOTICE_PERIOD_BUY_OUT", nullable = true)
    public BigDecimal getNoticePeriodBuyout() {
        return noticePeriodBuyout;
    }

    public void setNoticePeriodBuyout(BigDecimal noticePeriodBuyout) {
        this.noticePeriodBuyout = noticePeriodBuyout;
    }

    @Column(name = "NOTICE_PERIOD_DEDUCTION", nullable = true)
    public BigDecimal getNoticePeriodDeduction() {
        return noticePeriodDeduction;
    }

    public void setNoticePeriodDeduction(BigDecimal noticePeriodDeduction) {
        this.noticePeriodDeduction = noticePeriodDeduction;
    }

    @Column(name = "RELOCATION_EXPENSES", nullable = true)
    public BigDecimal getRelocationExpenses() {
        return relocationExpenses;
    }

    public void setRelocationExpenses(BigDecimal relocationExpenses) {
        this.relocationExpenses = relocationExpenses;
    }

    @Column(name = "STIPEND_EXPENSE", nullable = true)
    public BigDecimal getStipendExpenses() {
        return stipendExpenses;
    }

    public void setStipendExpenses(BigDecimal stipendExpenses) {
        this.stipendExpenses = stipendExpenses;
    }

    @Column(name = "TRAINING_COST_RECOVERY", nullable = true)
    public BigDecimal getTrainingCostRecovery() {
        return trainingCostRecovery;
    }

    public void setTrainingCostRecovery(BigDecimal trainingCostRecovery) {
        this.trainingCostRecovery = trainingCostRecovery;
    }

    @Column(name = "SEVERANCE_PAY", nullable = true)
    public BigDecimal getSeverancePay() {
        return severancePay;
    }

    public void setSeverancePay(BigDecimal severancePay) {
        this.severancePay = severancePay;
    }

    @Column(name = "LABOUR_CHARGES", nullable = true)
    public BigDecimal getLabourCharges() {
        return labourCharges;
    }

    public void setLabourCharges(BigDecimal labourCharges) {
        this.labourCharges = labourCharges;
    }

    @Column(name = "CONSUMABLE_TAX", nullable = true)
    public BigDecimal getConsumableTax() {
        return consumableTax;
    }

    public void setConsumableTax(BigDecimal consumableTax) {
        this.consumableTax = consumableTax;
    }

    @Column(name = "CONSUMABLE_OTHER", nullable = true)
    public BigDecimal getConsumableOthers() {
        return consumableOthers;
    }

    public void setConsumableOthers(BigDecimal consumableOthers) {
        this.consumableOthers = consumableOthers;
    }

    @Column(name = "CONSUMABLE_OTHER_TAX", nullable = true)
    public BigDecimal getConsumableOthersTax() {
        return consumableOthersTax;
    }

    public void setConsumableOthersTax(BigDecimal consumableOthersTax) {
        this.consumableOthersTax = consumableOthersTax;
    }

    @Column(name = "CONSUMABLE_UTILITY_TAX", nullable = true)
    public BigDecimal getConsumableUtilityTax() {
        return consumableUtilityTax;
    }

    public void setConsumableUtilityTax(BigDecimal consumableUtilityTax) {
        this.consumableUtilityTax = consumableUtilityTax;
    }

    @Column(name = "CONSUMABLE_STATIONARY_TAX", nullable = true)
    public BigDecimal getConsumableStationaryTax() {
        return consumableStationaryTax;
    }

    public void setConsumableStationaryTax(BigDecimal consumableStationaryTax) {
        this.consumableStationaryTax = consumableStationaryTax;
    }

    @Column(name = "CONSUMABLE_UNIFORM_TAX", nullable = true)
    public BigDecimal getConsumableUniformTax() {
        return consumableUniformTax;
    }

    public void setConsumableUniformTax(BigDecimal consumableUniformTax) {
        this.consumableUniformTax = consumableUniformTax;
    }

    @Column(name = "CONSUMABLE_EQUIPMENT_TAX", nullable = true)
    public BigDecimal getConsumableEquipmentTax() {
        return consumableEquipmentTax;
    }

    public void setConsumableEquipmentTax(BigDecimal consumableEquipmentTax) {
        this.consumableEquipmentTax = consumableEquipmentTax;
    }

    @Column(name = "CONSUMABLE_CUTLERY_TAX", nullable = true)
    public BigDecimal getConsumableCutleryTax() {
        return consumableCutleryTax;
    }

    public void setConsumableCutleryTax(BigDecimal consumableCutleryTax) {
        this.consumableCutleryTax = consumableCutleryTax;
    }

    @Column(name = "CONSUMABLE_DISPOSABLE_TAX", nullable = true)
    public BigDecimal getConsumableDisposableTax() {
        return consumableDisposableTax;
    }

    public void setConsumableDisposableTax(BigDecimal consumableDisposableTax) {
        this.consumableDisposableTax = consumableDisposableTax;
    }

    @Column(name = "CONSUMABLE_MARKETING_TAX", nullable = true)
    public BigDecimal getConsumableMarketingTax() {
        return consumableMarketingTax;
    }

    public void setConsumableMarketingTax(BigDecimal consumableMarketingTax) {
        this.consumableMarketingTax = consumableMarketingTax;
    }

    @Column(name = "CONSUMABLE_LHI_TAX", nullable = true)
    public BigDecimal getConsumableLhiTax() {
        return consumableLhiTax;
    }

    public void setConsumableLhiTax(BigDecimal consumableLhiTax) {
        this.consumableLhiTax = consumableLhiTax;
    }

    @Column(name = "CONSUMABLE_IT_TAX", nullable = true)
    public BigDecimal getConsumableItTax() {
        return consumableItTax;
    }

    public void setConsumableItTax(BigDecimal consumableItTax) {
        this.consumableItTax = consumableItTax;
    }

    @Column(name = "CONSUMABLE_MAINTENANCE_TAX", nullable = true)
    public BigDecimal getConsumableMaintenanceTax() {
        return consumableMaintenanceTax;
    }

    public void setConsumableMaintenanceTax(BigDecimal consumableMaintenanceTax) {
        this.consumableMaintenanceTax = consumableMaintenanceTax;
    }

    @Column(name = "CONSUMABLE_OFFICE_EQUIPMENT_TAX", nullable = true)
    public BigDecimal getConsumableOfficeEquipmentTax() {
        return consumableOfficeEquipmentTax;
    }

    public void setConsumableOfficeEquipmentTax(BigDecimal consumableOfficeEquipmentTax) {
        this.consumableOfficeEquipmentTax = consumableOfficeEquipmentTax;
    }

    @Column(name = "CONSUMABLE_KITCHEN_EQUIPMENT_TAX", nullable = true)
    public BigDecimal getConsumableKitchenEquipmentTax() {
        return consumableKitchenEquipmentTax;
    }

    public void setConsumableKitchenEquipmentTax(BigDecimal consumableKitchenEquipmentTax) {
        this.consumableKitchenEquipmentTax = consumableKitchenEquipmentTax;
    }

    @Column(name = "CONSUMABLE_CHAI_MONK_TAX", nullable = true)
    public BigDecimal getConsumableChaiMonkTax() {
        return consumableChaiMonkTax;
    }

    public void setConsumableChaiMonkTax(BigDecimal consumableChaiMonkTax) {
        this.consumableChaiMonkTax = consumableChaiMonkTax;
    }

    @Column(name = "EMP_DISCOUNT_LOYAL_TEA", nullable = true)
    public BigDecimal getEmpDiscountLoyalty() {
        return empDiscountLoyalty;
    }

    public void setEmpDiscountLoyalty(BigDecimal empDiscountLoyalty) {
        this.empDiscountLoyalty = empDiscountLoyalty;
    }

    @Column(name = "EMP_DISCOUNT_MARKETING", nullable = true)
    public BigDecimal getEmpDiscountMarketing() {
        return empDiscountMarketing;
    }

    public void setEmpDiscountMarketing(BigDecimal empDiscountMarketing) {
        this.empDiscountMarketing = empDiscountMarketing;
    }

    @Column(name = "EMP_DISCOUNT_OPS", nullable = true)
    public BigDecimal getEmpDiscountOps() {
        return empDiscountOps;
    }

    public void setEmpDiscountOps(BigDecimal empDiscountOps) {
        this.empDiscountOps = empDiscountOps;
    }

    @Column(name = "EMP_DISCOUNT_BD", nullable = true)
    public BigDecimal getEmpDiscountBd() {
        return empDiscountBd;
    }

    public void setEmpDiscountBd(BigDecimal empDiscountBd) {
        this.empDiscountBd = empDiscountBd;
    }

    @Column(name = "EMP_DISCOUNT_EMPLOYEE_FICO", nullable = true)
    public BigDecimal getEmpDiscountEmployeeFico() {
        return empDiscountEmployeeFico;
    }

    public void setEmpDiscountEmployeeFico(BigDecimal empDiscountEmployeeFico) {
        this.empDiscountEmployeeFico = empDiscountEmployeeFico;
    }

    @Column(name = "UNSATISFIED_CUSTOMER_COST", nullable = true)
    public BigDecimal getUnsatisfiedCustomerCost() {
        return unsatisfiedCustomerCost;
    }

    public void setUnsatisfiedCustomerCost(BigDecimal unsatisfiedCustomerCost) {
        this.unsatisfiedCustomerCost = unsatisfiedCustomerCost;
    }

    @Column(name = "PPE_COST", nullable = true)
    public BigDecimal getPPECost() {
        return this.ppeCost;
    }

    public void setPPECost(BigDecimal ppeCost) {
        this.ppeCost = ppeCost;
    }

    @Column(name = "PERFORMANCE_INCENTIVE", nullable = true)
    public BigDecimal getPerformanceIncentive() {
        return performanceIncentive;
    }

    public void setPerformanceIncentive(BigDecimal performanceIncentive) {
        this.performanceIncentive = performanceIncentive;
    }

//    @Column(name = "TECHNOLOGY_POS", nullable = true)
//    public BigDecimal getTechnologyPos() {
//        return TechnologyPos;
//    }
//
//    public void setTechnologyPos(BigDecimal technologyPos) {
//        TechnologyPos = technologyPos;
//    }

    @Column(name = "TRAINING_COGS", nullable = true)
    public BigDecimal getTrainingCogs() {
        return trainingCogs;
    }

    public void setTrainingCogs(BigDecimal trainingCogs) {
        this.trainingCogs = trainingCogs;
    }

    @Column(name = "DINE_IN_COGS_TAX", nullable = true)
    public BigDecimal getDineInCogsTax() {
        return dineInCogsTax;
    }

    public void setDineInCogsTax(BigDecimal dineInCogsTax) {
        this.dineInCogsTax = dineInCogsTax;
    }

    @Column(name = "DELIVERY_COGS_TAX", nullable = true)
    public BigDecimal getDeliveryCogsTax() {
        return deliveryCogsTax;
    }

    public void setDeliveryCogsTax(BigDecimal deliveryCogsTax) {
        this.deliveryCogsTax = deliveryCogsTax;
    }

    @Column(name = "EMPLOYEE_MEAL_COGS_TAX", nullable = true)
    public BigDecimal getEmployeeMealCogsTax() {
        return employeeMealCogsTax;
    }

    public void setEmployeeMealCogsTax(BigDecimal employeeMealCogsTax) {
        this.employeeMealCogsTax = employeeMealCogsTax;
    }

    @Column(name = "UNSATISFIED_CUSTOMER_COST_TAX", nullable = true)
    public BigDecimal getUnsatisfiedCustomerCostTax() {
        return unsatisfiedCustomerCostTax;
    }

    public void setUnsatisfiedCustomerCostTax(BigDecimal unsatisfiedCustomerCostTax) {
        this.unsatisfiedCustomerCostTax = unsatisfiedCustomerCostTax;
    }

    @Column(name = "PPE_COST_TAX", nullable = true)
    public BigDecimal getPPECostTax() {
        return this.ppeCostTax;
    }

    public void setPPECostTax(BigDecimal ppeCostTax) {
        this.ppeCostTax = ppeCostTax;
    }

    @Column(name = "EXPIRY_WASTAGE_TAX", nullable = true)
    public BigDecimal getExpiryWastageTax() {
        return expiryWastageTax;
    }

    public void setExpiryWastageTax(BigDecimal expiryWastageTax) {
        this.expiryWastageTax = expiryWastageTax;
    }

    @Column(name = "WASTAGE_OTHER", nullable = true)
    public BigDecimal getWastageOther() {
        return wastageOther;
    }

    public void setWastageOther(BigDecimal wastageOther) {
        this.wastageOther = wastageOther;
    }

    @Column(name = "WASTAGE_OTHER_TAX", nullable = true)
    public BigDecimal getWastageOtherTax() {
        return wastageOtherTax;
    }

    public void setWastageOtherTax(BigDecimal wastageOtherTax) {
        this.wastageOtherTax = wastageOtherTax;
    }

    @Column(name = "MARKETING_AND_SAMPLING_TAX", nullable = true)
    public BigDecimal getMarketingAndSamplingTax() {
        return MarketingAndSamplingTax;
    }

    public void setMarketingAndSamplingTax(BigDecimal marketingAndSamplingTax) {
        MarketingAndSamplingTax = marketingAndSamplingTax;
    }


    @Column(name = "TRAINING_COGS_TAX", nullable = true)
    public BigDecimal getTrainingCogsTax() {
        return trainingCogsTax;
    }

    public void setTrainingCogsTax(BigDecimal trainingCogsTax) {
        this.trainingCogsTax = trainingCogsTax;
    }

    @Column(name = "VARIANCE_PCC_TAX", nullable = true)
    public BigDecimal getVariancePccTax() {
        return variancePccTax;
    }

    public void setVariancePccTax(BigDecimal variancePccTax) {
        this.variancePccTax = variancePccTax;
    }

    @Column(name = "VARIANCE_YC_TAX", nullable = true)
    public BigDecimal getVarianceYcTax() {
        return varianceYcTax;
    }

    public void setVarianceYcTax(BigDecimal varianceYcTax) {
        this.varianceYcTax = varianceYcTax;
    }

    @Column(name = "DISCOUNT_GIFT_CARD", nullable = true)
    public BigDecimal getDiscountGiftCard() {
        return discountGiftCard;
    }

    public void setDiscountGiftCard(BigDecimal discountGiftCard) {
        this.discountGiftCard = discountGiftCard;
    }

    @Column(name = "INSURANCE_MARINE", nullable = true)
    public BigDecimal getInsuranceMarine() {
        return insuranceMarine;
    }

    public void setInsuranceMarine(BigDecimal insuranceMarine) {
        this.insuranceMarine = insuranceMarine;
    }

    @Column(name = "ROYALTY_FEES", nullable = true)
    public BigDecimal getRoyaltyFees() {
        return royaltyFees;
    }

    public void setRoyaltyFees(BigDecimal royaltyFees) {
        this.royaltyFees = royaltyFees;
    }

    @Column(name = "PHOTO_COPY_EXPENSES_CAFE", nullable = true)
    public BigDecimal getPhotoCopyExpensesCafe() {
        return photoCopyExpensesCafe;
    }

    public void setPhotoCopyExpensesCafe(BigDecimal photoCopyExpensesCafe) {
        this.photoCopyExpensesCafe = photoCopyExpensesCafe;
    }


    @Column(name = "NEWSPAPER_CHARGES_CAFE", nullable = true)
    public BigDecimal getNewsPaperCafe() {
        return newsPaperCafe;
    }

    public void setNewsPaperCafe(BigDecimal newsPaperCafe) {
        this.newsPaperCafe = newsPaperCafe;
    }


    @Column(name = "STAFF_WELFARE_EXPENSES_CAFE", nullable = true)
    public BigDecimal getStaffWelfareExpensesCafe() {
        return staffWelfareExpensesCafe;
    }

    public void setStaffWelfareExpensesCafe(BigDecimal staffWelfareExpensesCafe) {
        this.staffWelfareExpensesCafe = staffWelfareExpensesCafe;
    }


    @Column(name = "COURIER_CHARGES_CAFE", nullable = true)
    public BigDecimal getCourierChargesCafe() {
        return courierChargesCafe;
    }

    public void setCourierChargesCafe(BigDecimal courierChargesCafe) {
        this.courierChargesCafe = courierChargesCafe;
    }


    @Column(name = "PRINTING_AND_STATIONARY_CAFE", nullable = true)
    public BigDecimal getPrintingAndStationaryCafe() {
        return printingAndStationaryCafe;
    }

    public void setPrintingAndStationaryCafe(BigDecimal printingAndStationaryCafe) {
        this.printingAndStationaryCafe = printingAndStationaryCafe;
    }


    @Column(name = "BUSINESS_PROMOTION_CAFE", nullable = true)
    public BigDecimal getBusinessPromotionCafe() {
        return businessPromotionCafe;
    }

    public void setBusinessPromotionCafe(BigDecimal businessPromotionCafe) {
        this.businessPromotionCafe = businessPromotionCafe;
    }

    @Column(name = "CLEANING_CHARGES_CAFE", nullable = true)
    public BigDecimal getCleaningChargesCafe() {
        return cleaningChargesCafe;
    }

    public void setCleaningChargesCafe(BigDecimal cleaningChargesCafe) {
        this.cleaningChargesCafe = cleaningChargesCafe;
    }

}