package com.stpl.tech.kettle.core;

import com.stpl.tech.kettle.data.model.CustomerMappingTypes;
import com.stpl.tech.util.AppConstants;

import java.util.ArrayList;
import java.util.List;

public enum CustomerRepeatType {

	REGISTER(30,3, null,5),
	DORMANT(15,3, null,5),
	REPEAT(15,3, null,5),
	NEW(30,3, AppConstants.LOYAL_TEA_COUPON_CODE,5),
	DEFAULT(30,1, null,5);

	private final int validityInDays;
	private final int journeyCount;
	private final String defaultCloneCode;
	private final int reminderDays;


	private CustomerRepeatType(int validityInDays, int journeyCount, String defaultCloneCode,int reminderDays){
		this.validityInDays = validityInDays;
		this.journeyCount = journeyCount;
		this.defaultCloneCode = defaultCloneCode;
		this.reminderDays = reminderDays;
	}

	public int getValidityInDays() {
		return validityInDays;
	}
	public int getJourneyCount() {
		return journeyCount;
	}
	public String getDefaultCloneCode() {return defaultCloneCode;}
	public int getReminderDays(){return reminderDays;}

	public static CustomerRepeatType getCustomerRepeatTypeAfterPlacingOrder(int overallOrders, int activeOrder, boolean availedSignupOffer) {
			if (overallOrders >= 0 && !availedSignupOffer) {
				return NEW;
			}
			if (overallOrders >= 1 && activeOrder == 0) {
				return DORMANT;
			}
			if (overallOrders >= 1 && activeOrder >= 1) {
				return REPEAT;
			}
			return NEW;
	}

	public static CustomerRepeatType getCustomerRepeatType(int overallOrders, int activeOrder, boolean availedSignupOffer) {
			if (overallOrders == 0) {
				return REGISTER;
			}
			if (overallOrders >= 1 && activeOrder > 0 && !availedSignupOffer) {
				return NEW;
			}
			if (overallOrders >= 1 && activeOrder > 0 && availedSignupOffer) {
				return REPEAT;
			}
			if (overallOrders >= 1 && activeOrder == 0) {
				return DORMANT;
			}
			return REGISTER;
	}

	public static List<CustomerMappingTypes> getCustomerMappingTypes() {
		List<CustomerMappingTypes> mappingTypesList = new ArrayList<>();
		for (CustomerRepeatType type : CustomerRepeatType.values()) {
			mappingTypesList.add(new CustomerMappingTypes(type.name(), type.getJourneyCount(), type.getValidityInDays(),
					type.getDefaultCloneCode(), type.getReminderDays()));
		}
		return mappingTypesList;
	}

	public static CustomerRepeatType getTypeFromName(String name) {
		for (CustomerRepeatType type : CustomerRepeatType.values()) {
			if (type.name().equals(name)) {
				return type;
			}
		}
		return CustomerRepeatType.DEFAULT;
	}

	public static Integer getMaxJourney() {
		Integer maxValue = 0;
		for (CustomerRepeatType type : CustomerRepeatType.values()) {
			maxValue = Math.max(maxValue, type.getJourneyCount());
		}
		return maxValue;
	}

}
