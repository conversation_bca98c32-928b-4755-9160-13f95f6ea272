package com.stpl.tech.kettle.offer.strategy;

import com.stpl.tech.kettle.core.TransactionConstants;
import com.stpl.tech.kettle.core.notification.OfferOrder;
import com.stpl.tech.kettle.domain.model.DiscountDetail;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.PercentageDetail;
import com.stpl.tech.kettle.domain.model.TransactionDetail;
import com.stpl.tech.master.core.WebErrorCode;
import com.stpl.tech.master.core.exception.OfferValidationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.CouponMapping;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.ListData;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductBasicDetail;
import com.stpl.tech.util.AppUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

public class FreeBieStrategy extends PercentageItemStrategy implements OfferActionStrategy {


    @Override
    public OfferOrder applyStrategy(OfferOrder offerOrder, CouponDetail coupon, MasterDataCache cache,
                                    Map<String, OrderItem> foundItems) throws OfferValidationException {

        Map<Integer, IdCodeName> offerProducts = new HashMap<>();
        Map<Integer, IdCodeName> offerCategory = new HashMap<>();
        Map<Integer, IdCodeName> offerSubCategory = new HashMap<>();
        Map<String, OrderItem> orderProducts = new HashMap<>();
        Map<String, IdCodeName> freebieProduct = new HashMap<>();
        Map<String,String> freebieProductDimension = new HashMap<>();
        for (OrderItem orderItem : offerOrder.getOrder().getOrders()) {
            if(orderProducts.containsKey(orderItem.getProductId())){
                OrderItem item = orderProducts.get(orderItem.getProductId());
                if(!item.getDimension().replaceAll(" ", "").equalsIgnoreCase(orderItem.getDimension())){
                    orderProducts.put(orderItem.getProductId() + "_" + orderItem.getDimension(), orderItem);
                    continue;
                }
            }
            orderProducts.put(orderItem.getProductId() + "_" + orderItem.getDimension(), orderItem);
        }
        for (IdCodeName offerProd : coupon.getOffer().getMetaDataMappings()) {
            if (offerProd.getName().equalsIgnoreCase("FREEBIE_PRODUCT")) {
                Set<CouponMapping> couponMapping = coupon.getMappings().get("FREEBIE_PRODUCT");
                for (CouponMapping obj : couponMapping) {
                    if(offerProd.getCode().toString().equals(obj.getValue())) {
                        freebieProduct.put(offerProd.getCode() + "_" + obj.getDimension(), offerProd);
                        freebieProductDimension.put(offerProd.getCode() + "_" + obj.getDimension(), obj.getDimension());
                    }
                }
            }
            if (offerProd.getName().equalsIgnoreCase("PRODUCT_CATEGORY")) {
                offerCategory.put(Integer.valueOf(offerProd.getCode()), offerProd);
            }
            if (offerProd.getName().equalsIgnoreCase("PRODUCT_SUB_CATEGORY")) {
                offerSubCategory.put(Integer.valueOf(offerProd.getCode()), offerProd);
            }
            if (offerProd.getName().equalsIgnoreCase("PRODUCT")) {
                offerProducts.put(Integer.valueOf(offerProd.getCode()), offerProd);
            }
        }
        validateFreeBieProduct(orderProducts, freebieProduct, freebieProductDimension);
        validateOrderProducts(orderProducts, offerProducts, offerCategory, offerSubCategory, cache, coupon, freebieProduct,offerOrder);

        offerOrder.setAppliedOfferMessage(getOfferMessage());
        return offerOrder;
    }

    public void validateFreeBieProduct(Map<String, OrderItem> orderProducts, Map<String, IdCodeName> freebieProduct,
                                       Map<String,String> freebieProductDimension) throws OfferValidationException {
        boolean isFreebieProductAvailable = false;
        boolean isFreebieProductDimensionAvailable = false;
        String prod = null;
        String freebieName = "";
        for (Map.Entry<String, IdCodeName> freebie : freebieProduct.entrySet()) {
            if (orderProducts.containsKey(freebie.getKey().replaceAll(" ", ""))) {
                isFreebieProductAvailable = true;
                OrderItem orderItem = orderProducts.get(freebie.getKey().replaceAll(" ", ""));
                freebieName = orderItem.getProductName();
                if (orderItem.getDimension().replaceAll(" ", "").equalsIgnoreCase(freebieProductDimension.get(freebie.getKey()).replaceAll(" ", ""))) {
                    isFreebieProductDimensionAvailable = true;
                    if(isFreebieProductAvailable && isFreebieProductDimensionAvailable)
                        break;
                }
            }
        }
        if (!isFreebieProductAvailable) {
            throw new OfferValidationException("Freebie product doesn't exist in this order ",
                    WebErrorCode.FREEBIE_PRODUCT_NOT_FOUND);
        }
        if (!isFreebieProductDimensionAvailable) {
            List<String> dim = new ArrayList<>();
            for(Map.Entry<String,String> freeDimension : freebieProductDimension.entrySet()){
                dim.add(freeDimension.getValue());
            }
            throw new OfferValidationException("Freebie product is :" + freebieName + " ,but available with different dimension.",
                    WebErrorCode.FREEBIE_PRODUCT_NOT_FOUND);
        }
    }

    public void validateOrderProducts(Map<String, OrderItem> orderProducts, Map<Integer, IdCodeName> offerProducts, Map<Integer, IdCodeName> offerCategory,
                                      Map<Integer, IdCodeName> offerSubCategory, MasterDataCache cache,
                                      CouponDetail coupon, Map<String, IdCodeName> freebieProduct,OfferOrder offerOrder) throws OfferValidationException {
        boolean flag = true;
        OrderItem item = new OrderItem();
        int count = 0;
        for (Map.Entry<String, IdCodeName> freebie : freebieProduct.entrySet()) {
            for (Map.Entry<String, OrderItem> map : orderProducts.entrySet()) {
                OrderItem orderItem = map.getValue();
                OrderItem freeProd = orderProducts.get(freebie.getKey().replaceAll(" ",""));
                if (offerProducts.containsKey(Integer.valueOf(map.getKey().substring(0,map.getKey().indexOf("_"))))) {
                    Product product = cache.getProduct(Integer.valueOf(map.getKey().substring(0,map.getKey().indexOf("_"))));
                    ListData dimensionProfile = cache.getDimensionProfile(product.getDimensionProfileId());
                    List<String> dimensions = new ArrayList<>();
                    if(!CollectionUtils.isEmpty(dimensionProfile.getContent())){
                        for(IdCodeName name : dimensionProfile.getContent())
                            dimensions.add(name.getName());
                    }
                    if (Objects.nonNull(freeProd) && (dimensionProfile.getDetail().getName().equalsIgnoreCase(orderItem.getDimension()) || dimensions.contains(orderItem.getDimension()) )) {
                        item = checkProductToDiscount(freeProd, item);
                        flag = false;
                    }
                }
                Map<Integer, ListData> cat = cache.getListCategoryData();
                ProductBasicDetail product = cache.getProductBasicDetail(orderItem.getProductId());
                for (Map.Entry<Integer, IdCodeName> offerCat : offerCategory.entrySet()) {
                    if (offerCat.getValue().getCode().equals(String.valueOf(product.getType()))) {
                        if(Objects.nonNull(item) && Objects.nonNull(freeProd)) {
                            item = checkProductToDiscount(freeProd, item);
                            flag = false;
                        }
                    }
                }
                for (Map.Entry<Integer, IdCodeName> offerSubCat : offerSubCategory.entrySet()) {
                    ListData catData = cat.get(product.getType());
                    for(IdCodeName codeName : catData.getContent()) {
                        if (offerSubCat.getValue().getCode().equals(String.valueOf(codeName.getId()))) {
                            if (Objects.nonNull(item) && Objects.nonNull(freeProd)) {
                                item = checkProductToDiscount(freeProd, item);
                                flag = false;
                            }
                        }
                    }
                }
            }
            count++;
            if(count == freebieProduct.size() && flag==false){
                addDiscountDetails(item,coupon,offerOrder);
            }
        }
        if (flag) {
            throw new OfferValidationException("Offer product not found ",
                    WebErrorCode.OFFER_PRODUCT_NOT_FOUND);
        }
    }

    public OrderItem checkProductToDiscount(OrderItem orderItem, OrderItem item) {
        if(Objects.isNull(item.getPrice())){
            item = orderItem;
        }
        else{
            if(orderItem.getPrice().compareTo(item.getPrice()) == -1){
                item = orderItem;
            }
            else if(orderItem.getPrice().compareTo(item.getPrice())==0){
                item = orderItem;
            }
        }
        return item;
    }
    public void addDiscountDetails(OrderItem orderItem, CouponDetail coupon,OfferOrder offerOrder) throws OfferValidationException {
        BigDecimal amountLeft = offerOrder.getOrder().getTransactionDetail().getTotalAmount().subtract(orderItem.getPrice());
        if(amountLeft.compareTo(BigDecimal.valueOf(coupon.getOffer().getMinValue())) == -1){
            throw new OfferValidationException("Coupon cannot be applied. Cart should contain minimum value of " + coupon.getOffer().getMinValue() + " plus the offer product",
                    WebErrorCode.CART_VALUE_NOT_FOUND);
        }
        BigDecimal discountItemValue = orderItem.getPrice().multiply(BigDecimal.valueOf(coupon.getOffer().getOfferValue())).divide(BigDecimal.valueOf(100)).setScale(0, RoundingMode.HALF_UP);
        if(Objects.nonNull(orderItem.getDiscountDetail().getDiscount())) {
            orderItem.getDiscountDetail().getDiscount().setValue(discountItemValue);
            orderItem.getDiscountDetail().getDiscount().setPercentage(BigDecimal.valueOf(coupon.getOffer().getOfferValue()));
        }else{
            PercentageDetail discountDetail = new PercentageDetail();
            discountDetail.setValue(discountItemValue);
            discountDetail.setPercentage(BigDecimal.valueOf(coupon.getOffer().getOfferValue()));
            orderItem.getDiscountDetail().setDiscount(discountDetail);
        }
        BigDecimal totalPercentageOff = BigDecimal.ZERO;
        TransactionDetail transactionDetail = offerOrder.getOrder().getTransactionDetail();
        if (Objects.nonNull(transactionDetail)) {
            if (Objects.nonNull(transactionDetail.getTotalAmount())) {
                totalPercentageOff = AppUtils.percentage(orderItem.getPrice(), offerOrder.getOrder().getTransactionDetail().getTotalAmount());
            }
        }
        if (Objects.nonNull(offerOrder.getOrder().getTransactionDetail())) {
            if (Objects.nonNull(offerOrder.getOrder().getTransactionDetail().getDiscountDetail().getDiscount())) {
                transactionDetail.getDiscountDetail().getDiscount().setPercentage(totalPercentageOff.setScale(0, RoundingMode.HALF_UP));
                transactionDetail.getDiscountDetail().getDiscount().setValue(discountItemValue);
            } else if (Objects.nonNull(offerOrder.getOrder().getTransactionDetail().getDiscountDetail()) && Objects.isNull(Objects.nonNull(offerOrder.getOrder().getTransactionDetail().getDiscountDetail().getDiscount()))) {
                PercentageDetail discountDetail = new PercentageDetail();
                discountDetail.setValue(discountItemValue);
                discountDetail.setPercentage(totalPercentageOff.setScale(0, RoundingMode.HALF_UP));
                transactionDetail.getDiscountDetail().setDiscount(discountDetail);
            } else if (Objects.nonNull(offerOrder.getOrder().getTransactionDetail()) && Objects.isNull(offerOrder.getOrder().getTransactionDetail()) && Objects.isNull(offerOrder.getOrder().getTransactionDetail().getDiscountDetail())) {
                DiscountDetail detail = new DiscountDetail();
                PercentageDetail discountDetail = new PercentageDetail();
                discountDetail.setValue(discountItemValue);
                discountDetail.setPercentage(totalPercentageOff.setScale(0, RoundingMode.HALF_UP));
                transactionDetail.setDiscountDetail(detail);
                transactionDetail.getDiscountDetail().setDiscount(discountDetail);
            }
            offerOrder.getOrder().setTransactionDetail(transactionDetail);
        }
        orderItem.getDiscountDetail().getDiscount().setValue(discountItemValue);
        orderItem.getDiscountDetail().getDiscount().setPercentage(BigDecimal.valueOf(coupon.getOffer().getOfferValue()));
        orderItem.getDiscountDetail().setTotalDiscount(orderItem.getPrice().setScale(0, RoundingMode.HALF_UP));
        orderItem.getDiscountDetail().setDiscountCode(TransactionConstants.MARKETING_VOUCHER_ID);
        orderItem.getDiscountDetail().setDiscountReason(coupon.getCode());
    }

}
