package com.stpl.tech.kettle.data.model;

public class SpecialOfferRequest {
    private String contactNumber;
    private String campaignToken;
    private String authToken;
    private String utmSource;
    private String utmMedium;
    private String email;
    private Integer flow;
    private Boolean whatsappOpt;

    public String getContactNumber() {
        return contactNumber;
    }

    public void setContactNumber(String contactNumber) {
        this.contactNumber = contactNumber;
    }

    public String getCampaignToken() {
        return campaignToken;
    }

    public void setCampaignToken(String campaignToken) {
        this.campaignToken = campaignToken;
    }

    public String getAuthToken() {
        return authToken;
    }

    public void setAuthToken(String authToken) {
        this.authToken = authToken;
    }

    public String getUtmSource() {
        return utmSource;
    }

    public void setUtmSource(String utmSource) {
        this.utmSource = utmSource;
    }

    public String getUtmMedium() {
        return utmMedium;
    }

    public void setUtmMedium(String utmMedium) {
        this.utmMedium = utmMedium;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Integer getFlow() {
        return flow;
    }

    public void setFlow(Integer flow) {
        this.flow = flow;
    }

    public Boolean getWhatsappOpt() {
        return whatsappOpt;
    }

    public void setWhatsappOpt(Boolean whatsappOpt) {
        this.whatsappOpt = whatsappOpt;
    }
}
