/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

import static javax.persistence.GenerationType.IDENTITY;

// Generated 14 Jul, 2015 1:35:13 AM by Hibernate Tools 4.0.0

import java.util.HashSet;
import java.util.Set;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;

/**
 * TrainingDetail generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "TRAINING_DETAIL")
public class TrainingDetail implements java.io.Serializable {

	private Integer trainingId;
	private String trainingName;
	private String trainingDesc;
	private String isMaterialAvailable;
	private String onJobTraining;
	private Set<EmployeeTrainingDetail> employeeTrainingDetails = new HashSet<EmployeeTrainingDetail>(0);
	private Set<TrainingDesignationMapping> trainingDesignationMappings = new HashSet<TrainingDesignationMapping>(0);

	public TrainingDetail() {
	}

	public TrainingDetail(String trainingName, String trainingDesc, String isMaterialAvailable, String onJobTraining) {
		this.trainingName = trainingName;
		this.trainingDesc = trainingDesc;
		this.isMaterialAvailable = isMaterialAvailable;
		this.onJobTraining = onJobTraining;
	}

	public TrainingDetail(String trainingName, String trainingDesc, String isMaterialAvailable, String onJobTraining,
			Set<EmployeeTrainingDetail> employeeTrainingDetails,
			Set<TrainingDesignationMapping> trainingDesignationMappings) {
		this.trainingName = trainingName;
		this.trainingDesc = trainingDesc;
		this.isMaterialAvailable = isMaterialAvailable;
		this.onJobTraining = onJobTraining;
		this.employeeTrainingDetails = employeeTrainingDetails;
		this.trainingDesignationMappings = trainingDesignationMappings;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "TRAINING_ID", unique = true, nullable = false)
	public Integer getTrainingId() {
		return this.trainingId;
	}

	public void setTrainingId(Integer trainingId) {
		this.trainingId = trainingId;
	}

	@Column(name = "TRAINING_NAME", nullable = false)
	public String getTrainingName() {
		return this.trainingName;
	}

	public void setTrainingName(String trainingName) {
		this.trainingName = trainingName;
	}

	@Column(name = "TRAINING_DESC", nullable = false, length = 5000)
	public String getTrainingDesc() {
		return this.trainingDesc;
	}

	public void setTrainingDesc(String trainingDesc) {
		this.trainingDesc = trainingDesc;
	}

	@Column(name = "IS_MATERIAL_AVAILABLE", nullable = false, length = 1)
	public String getIsMaterialAvailable() {
		return this.isMaterialAvailable;
	}

	public void setIsMaterialAvailable(String isMaterialAvailable) {
		this.isMaterialAvailable = isMaterialAvailable;
	}

	@Column(name = "ON_JOB_TRAINING", nullable = false, length = 1)
	public String getOnJobTraining() {
		return this.onJobTraining;
	}

	public void setOnJobTraining(String onJobTraining) {
		this.onJobTraining = onJobTraining;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "trainingDetail")
	public Set<EmployeeTrainingDetail> getEmployeeTrainingDetails() {
		return this.employeeTrainingDetails;
	}

	public void setEmployeeTrainingDetails(Set<EmployeeTrainingDetail> employeeTrainingDetails) {
		this.employeeTrainingDetails = employeeTrainingDetails;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "trainingDetail")
	public Set<TrainingDesignationMapping> getTrainingDesignationMappings() {
		return this.trainingDesignationMappings;
	}

	public void setTrainingDesignationMappings(Set<TrainingDesignationMapping> trainingDesignationMappings) {
		this.trainingDesignationMappings = trainingDesignationMappings;
	}

}
