/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Entity
@Table(name = "UNIT_EXPENSE_DRILLDOWN")
public class UnitExpenseDrilldown {

	private int drilldownId;
	private UnitExpenseDetail unitExpenseDetail;
	private int expenseUpdateEventId;
	private int referenceId;
	private String referenceName;
	private String expenseType;
	private BigDecimal expenseValue;
	private int noOfTickets;
	private BigDecimal expenseRate;
	private BigDecimal adjustments;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "DRILLDOWN_ID", unique = true, nullable = false)
	public int getDrilldownId() {
		return drilldownId;
	}

	public void setDrilldownId(int drilldownId) {
		this.drilldownId = drilldownId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "UNIT_EXPENSE_DETAIL_ID", nullable = false)
	public UnitExpenseDetail getUnitExpenseDetail() {
		return unitExpenseDetail;
	}

	public void setUnitExpenseDetail(UnitExpenseDetail unitExpenseDetail) {
		this.unitExpenseDetail = unitExpenseDetail;
	}

	@Column(name = "EXPENSE_UPDATE_EVENT_ID", nullable = false)
	public int getExpenseUpdateEventId() {
		return expenseUpdateEventId;
	}

	public void setExpenseUpdateEventId(int eventId) {
		this.expenseUpdateEventId = eventId;
	}

	@Column(name = "REFERENCE_ID", nullable = false)
	public int getReferenceId() {
		return referenceId;
	}

	public void setReferenceId(int referenceId) {
		this.referenceId = referenceId;
	}

	@Column(name = "REFERENCE_NAME", nullable = false)
	public String getReferenceName() {
		return referenceName;
	}

	public void setReferenceName(String referenceName) {
		this.referenceName = referenceName;
	}

	@Column(name = "EXPENSE_TYPE", nullable = false)
	public String getExpenseType() {
		return expenseType;
	}

	public void setExpenseType(String expenseType) {
		this.expenseType = expenseType;
	}

	@Column(name = "EXPENSE_VALUE", nullable = true)
	public BigDecimal getExpenseValue() {
		return expenseValue;
	}

	public void setExpenseValue(BigDecimal expenseValue) {
		this.expenseValue = expenseValue;
	}

	@Column(name = "NO_OF_TICKETS", nullable = true)
	public int getNoOfTickets() {
		return noOfTickets;
	}

	public void setNoOfTickets(int noOfTickets) {
		this.noOfTickets = noOfTickets;
	}

	@Column(name = "EXPENSE_RATE", nullable = true)
	public BigDecimal getExpenseRate() {
		return expenseRate;
	}

	public void setExpenseRate(BigDecimal expenseRate) {
		this.expenseRate = expenseRate;
	}

	@Column(name = "ADJUSTMENTS", nullable = true)
	public BigDecimal getAdjustments() {
		return adjustments;
	}

	public void setAdjustments(BigDecimal adjustments) {
		this.adjustments = adjustments;
	}

}
