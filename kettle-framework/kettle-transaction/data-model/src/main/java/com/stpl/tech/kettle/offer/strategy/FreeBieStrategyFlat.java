package com.stpl.tech.kettle.offer.strategy;

import com.stpl.tech.kettle.core.TransactionConstants;
import com.stpl.tech.kettle.core.notification.OfferOrder;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.master.core.WebErrorCode;
import com.stpl.tech.master.core.exception.OfferValidationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.CouponMapping;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.ListData;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductBasicDetail;
import com.stpl.tech.util.AppUtils;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeMap;

public class FreeBieStrategyFlat extends PercentageItemStrategy implements OfferActionStrategy {

    @Override
    public OfferOrder applyStrategy(OfferOrder offerOrder, CouponDetail coupon, MasterDataCache cache,
                                    Map<String, OrderItem> foundItems) throws OfferValidationException {

        Map<Integer, IdCodeName> offerProducts = new HashMap<>();
        Map<Integer, IdCodeName> offerCategory = new HashMap<>();
        Map<Integer, IdCodeName> offerSubCategory = new HashMap<>();
        Map<String, OrderItem> orderProducts = new HashMap<>();
        Map<String, IdCodeName> freebieProduct = new HashMap<>();
        Map<String, String> freebieProductDimension = new HashMap<>();
        for (OrderItem orderItem : offerOrder.getOrder().getOrders()) {
            if (orderProducts.containsKey(orderItem.getProductId())) {
                OrderItem item = orderProducts.get(orderItem.getProductId());
                if (!item.getDimension().replaceAll(" ", "").equalsIgnoreCase(orderItem.getDimension())) {
                    orderProducts.put(orderItem.getProductId() + "_" + orderItem.getDimension(), orderItem);
                    continue;
                }
            }
            orderProducts.put(orderItem.getProductId() + "_" + orderItem.getDimension(), orderItem);
        }
        for (IdCodeName offerProd : coupon.getOffer().getMetaDataMappings()) {
            if (offerProd.getName().equalsIgnoreCase("FREEBIE_PRODUCT")) {
                Set<CouponMapping> couponMapping = coupon.getMappings().get("FREEBIE_PRODUCT");
                for (CouponMapping obj : couponMapping) {
                    if (offerProd.getCode().toString().equals(obj.getValue())) {
                        freebieProduct.put(offerProd.getCode() + "_" + obj.getDimension(), offerProd);
                        freebieProductDimension.put(offerProd.getCode() + "_" + obj.getDimension(), obj.getDimension());
                    }
                }
            }
            if (offerProd.getName().equalsIgnoreCase("PRODUCT_CATEGORY")) {
                offerCategory.put(Integer.valueOf(offerProd.getCode()), offerProd);
            }
            if (offerProd.getName().equalsIgnoreCase("PRODUCT_SUB_CATEGORY")) {
                offerSubCategory.put(Integer.valueOf(offerProd.getCode()), offerProd);
            }
            if (offerProd.getName().equalsIgnoreCase("PRODUCT")) {
                offerProducts.put(Integer.valueOf(offerProd.getCode()), offerProd);
            }
        }
        validateFreeBieProduct(orderProducts, freebieProduct, freebieProductDimension);
        validateOrderProducts(orderProducts, offerProducts, offerCategory, offerSubCategory, cache, coupon, freebieProduct, offerOrder);

        offerOrder.setAppliedOfferMessage(getOfferMessage());
        offerOrder.getOrder().getTransactionDetail().getDiscountDetail().setDiscountReason(coupon.getCode());
        return offerOrder;
    }

    public void validateFreeBieProduct(Map<String, OrderItem> orderProducts, Map<String, IdCodeName> freebieProduct,
                                       Map<String, String> freebieProductDimension) throws OfferValidationException {
        boolean isFreebieProductAvailable = false;
        boolean isFreebieProductDimensionAvailable = false;
        String freebieName = "";
        for (Map.Entry<String, IdCodeName> freebie : freebieProduct.entrySet()) {
            if (orderProducts.containsKey(freebie.getKey().replaceAll(" ", ""))) {
                isFreebieProductAvailable = true;
                OrderItem orderItem = orderProducts.get(freebie.getKey().replaceAll(" ", ""));
                freebieName = orderItem.getProductName();
                if (orderItem.getDimension().replaceAll(" ", "").equalsIgnoreCase(freebieProductDimension.get(freebie.getKey()).replaceAll(" ", ""))) {
                    isFreebieProductDimensionAvailable = true;
                    if (isFreebieProductAvailable && isFreebieProductDimensionAvailable)
                        break;
                }
            }
        }
        if (!isFreebieProductAvailable) {
            throw new OfferValidationException("Freebie product doesn't exist in this order ",
                    WebErrorCode.FREEBIE_PRODUCT_NOT_FOUND);
        }
        if (!isFreebieProductDimensionAvailable) {
            List<String> dim = new ArrayList<>();
            for (Map.Entry<String, String> freeDimension : freebieProductDimension.entrySet()) {
                dim.add(freeDimension.getValue());
            }
            throw new OfferValidationException("Freebie product is :" + freebieName + " ,but available with different dimension.",
                    WebErrorCode.FREEBIE_PRODUCT_NOT_FOUND);
        }
    }

    public void validateOrderProducts(Map<String, OrderItem> orderProducts, Map<Integer, IdCodeName> offerProducts, Map<Integer, IdCodeName> offerCategory,
                                      Map<Integer, IdCodeName> offerSubCategory, MasterDataCache cache,
                                      CouponDetail coupon, Map<String, IdCodeName> freebieProduct, OfferOrder offerOrder) throws OfferValidationException {
        boolean flag = true;
        TreeMap<BigDecimal, Pair<Integer, OrderItem>> maxOfDiscountableItems = new TreeMap<>();
        int count = 0;
        for (Map.Entry<String, IdCodeName> freebie : freebieProduct.entrySet()) {
            for (Map.Entry<String, OrderItem> map : orderProducts.entrySet()) {
                OrderItem orderItem = map.getValue();
                OrderItem freeProd = orderProducts.get(freebie.getKey().replaceAll(" ", ""));
                if (offerProducts.containsKey(Integer.valueOf(map.getKey().substring(0, map.getKey().indexOf("_"))))) {
                    Product product = cache.getProduct(Integer.valueOf(map.getKey().substring(0, map.getKey().indexOf("_"))));
                    ListData dimensionProfile = cache.getDimensionProfile(product.getDimensionProfileId());
                    List<String> dimensions = new ArrayList<>();
                    if (!CollectionUtils.isEmpty(dimensionProfile.getContent())) {
                        for (IdCodeName name : dimensionProfile.getContent())
                            dimensions.add(name.getName());
                    }
                    if (Objects.nonNull(freeProd) && (dimensionProfile.getDetail().getName().equalsIgnoreCase(orderItem.getDimension()) || dimensions.contains(orderItem.getDimension()))) {
                        checkProductToDiscount(freeProd, maxOfDiscountableItems);
                        flag = false;
                    }
                }
                Map<Integer, ListData> cat = cache.getListCategoryData();
                ProductBasicDetail product = cache.getProductBasicDetail(orderItem.getProductId());
                for (Map.Entry<Integer, IdCodeName> offerCat : offerCategory.entrySet()) {
                    if (offerCat.getValue().getCode().equals(String.valueOf(product.getType()))) {
                        if (Objects.nonNull(freeProd)) {
                            checkProductToDiscount(freeProd, maxOfDiscountableItems);
                            flag = false;
                        }
                    }
                }
                for (Map.Entry<Integer, IdCodeName> offerSubCat : offerSubCategory.entrySet()) {
                    ListData catData = cat.get(product.getType());
                    for (IdCodeName codeName : catData.getContent()) {
                        if (offerSubCat.getValue().getCode().equals(String.valueOf(codeName.getId()))) {
                            if (Objects.nonNull(freeProd)) {
                                checkProductToDiscount(freeProd, maxOfDiscountableItems);
                                flag = false;
                            }
                        }
                    }
                }
            }
            count++;
            if (count == freebieProduct.size() && flag == false) {
                addDiscountDetails(coupon, offerOrder, maxOfDiscountableItems);
            }
        }
        if (flag) {
            throw new OfferValidationException("Offer product not found ",
                    WebErrorCode.OFFER_PRODUCT_NOT_FOUND);
        }
    }

    public void checkProductToDiscount(OrderItem orderItem, Map<BigDecimal, Pair<Integer, OrderItem>> maxOfDiscountableItems) {
        Pair<Integer, OrderItem> pair = new Pair<>();
        pair.setKey(orderItem.getQuantity());
        pair.setValue(orderItem);
        maxOfDiscountableItems.put(orderItem.getPrice(), pair);
    }

    public void addDiscountDetails(CouponDetail coupon, OfferOrder offerOrder, Map<BigDecimal, Pair<Integer, OrderItem>> maxOfDiscountableItems) throws OfferValidationException {

        int count = coupon.getOffer().getMinQuantity();
        BigDecimal discountValue = AppUtils.multiply(BigDecimal.valueOf(coupon.getOffer().getOfferValue()), BigDecimal.valueOf(count));
        BigDecimal totalDiscount = BigDecimal.ZERO;
        for (Map.Entry<BigDecimal, Pair<Integer, OrderItem>> map : maxOfDiscountableItems.entrySet()) {
            OrderItem orderValue = map.getValue().getValue();
            BigDecimal value = BigDecimal.valueOf(Math.abs(discountValue.setScale(0, RoundingMode.HALF_UP).floatValue()));
            if (count <= 0 || value.compareTo(BigDecimal.ZERO) == 0) {
                break;
            }
            if (orderValue.getTotalAmount().compareTo(discountValue) == 1 && orderValue.getQuantity() >= count) {
                map.getValue().getValue().getDiscountDetail().getDiscount().setValue(discountValue);
                map.getValue().getValue().getDiscountDetail().setTotalDiscount(discountValue);
                totalDiscount = AppUtils.add(totalDiscount, discountValue);
                discountValue = AppUtils.subtract(discountValue, discountValue);
            } else {
                BigDecimal discountedPrice = (orderValue.getTotalAmount().compareTo(BigDecimal.valueOf(coupon.getOffer().getOfferValue())) == -1) ?
                        orderValue.getTotalAmount() : BigDecimal.valueOf(coupon.getOffer().getOfferValue());
                map.getValue().getValue().getDiscountDetail().getDiscount().setValue(discountedPrice);
                map.getValue().getValue().getDiscountDetail().setTotalDiscount(discountedPrice);
                totalDiscount = AppUtils.add(totalDiscount, discountedPrice);
                discountValue = AppUtils.subtract(discountValue, discountedPrice);
            }
            count -= map.getValue().getKey();
            map.getValue().getValue().getDiscountDetail().setDiscountCode(TransactionConstants.MARKETING_VOUCHER_ID);
            map.getValue().getValue().getDiscountDetail().setDiscountReason(coupon.getCode());
        }
        setTransactionDetail(offerOrder, totalDiscount, coupon);
    }

    private void setTransactionDetail(OfferOrder offerOrder, BigDecimal totalDiscount, CouponDetail coupon) throws OfferValidationException {
        if (AppUtils.subtract(offerOrder.getOrder().getTransactionDetail().getTotalAmount(), totalDiscount).compareTo(BigDecimal.valueOf(coupon.getOffer().getMinValue())) == -1) {
            throw new OfferValidationException("Coupon cannot be applied. Cart should contain minimum value of " + coupon.getOffer().getMinValue() + " plus the offer product",
                    WebErrorCode.CART_VALUE_NOT_FOUND);
        }
        offerOrder.getOrder().getTransactionDetail().getDiscountDetail().getDiscount().setValue(totalDiscount.setScale(0, RoundingMode.HALF_UP));
        offerOrder.getOrder().getTransactionDetail().getDiscountDetail().setDiscountCode(TransactionConstants.MARKETING_VOUCHER_ID);
        offerOrder.getOrder().getTransactionDetail().getDiscountDetail().setDiscountReason(coupon.getCode());
        offerOrder.getOrder().getTransactionDetail().getDiscountDetail().setTotalDiscount(totalDiscount.setScale(0, RoundingMode.HALF_UP));
    }
}
