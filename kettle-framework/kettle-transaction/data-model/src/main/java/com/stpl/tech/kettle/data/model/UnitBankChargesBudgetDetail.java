/*
 * SUNSHINE TEAHOUSE @ExcelField private LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse @ExcelField private Limited
 * All Rights Reservethis.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse @ExcelField private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse @ExcelField private Limited
 * and its suppliers, and are @ExcelField private by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse @ExcelField private Limitethis.
 */
package com.stpl.tech.kettle.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.stpl.tech.util.excelparser.annotations.ExcelField;
import com.stpl.tech.util.excelparser.annotations.ExcelObject;
import com.stpl.tech.util.excelparser.annotations.ExcelSheet;
import com.stpl.tech.util.excelparser.annotations.ParseType;

@ExcelSheet(value = "Unit Bank Charges Budget Detail")
@ExcelObject(parseType = ParseType.ROW, start = 1, end = 0)
@Entity
@Table(name = "UNIT_BANK_CHARGES_BUDGET_DETAIL")
public class UnitBankChargesBudgetDetail {

	private int bankChargesBudgetDetailId;
	@ExcelField
	private int unitId;
	@ExcelField
	private String unitName;
	@ExcelField
	private int year;
	@ExcelField
	private int month;
	@ExcelField
	private String status;

	@ExcelField
	private BigDecimal oldBankCharges;

	@ExcelField
	private BigDecimal bankCharges;

	private Integer updatedBy;

	private Date updateTime;

	public UnitBankChargesBudgetDetail() {

	}
	
	public UnitBankChargesBudgetDetail(int unitId, String unitName, int year, int month, String status) {
		this.unitId = unitId;
		this.unitName = unitName;
		this.year = year;
		this.month = month;
		this.status = status;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "UNIT_BANK_CHARGES_BUDGET_DETAIL_ID", unique = true, nullable = false)
	public int getBankChargesBudgetDetailId() {
		return bankChargesBudgetDetailId;
	}

	public void setBankChargesBudgetDetailId(int unitExpenseDetailId) {
		this.bankChargesBudgetDetailId = unitExpenseDetailId;
	}

	@Column(name = "UNIT_ID", nullable = true)
	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	@Column(name = "UNIT_NAME", nullable = true)
	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}

	@Column(name = "CALCULATION_YEAR", nullable = true)
	public int getYear() {
		return year;
	}

	public void setYear(int year) {
		this.year = year;
	}

	@Column(name = "CALCULATION_MONTH", nullable = true)
	public int getMonth() {
		return month;
	}

	public void setMonth(int month) {
		this.month = month;
	}

	@Column(name = "BUDGET_STATUS", nullable = true)
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@Column(name = "OLD_BANK_CHARGES", nullable = true)
	public BigDecimal getOldBankCharges() {
		return oldBankCharges;
	}

	public void setOldBankCharges(BigDecimal oldBankCharges) {
		this.oldBankCharges = oldBankCharges;
	}

	@Column(name = "BANK_CHARGES", nullable = true)
	public BigDecimal getBankCharges() {
		return bankCharges;
	}

	public void setBankCharges(BigDecimal bankCharges) {
		this.bankCharges = bankCharges;
	}

	@Column(name = "UPDATED_BY", nullable = true)
	public Integer getUpdatedBy() {
		return updatedBy;
	}
	
	public void setUpdatedBy(Integer updatedBy) {
		this.updatedBy = updatedBy;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "UPDATE_TIME", nullable = true, length = 19)
	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}

	public void copyToOld(UnitBudgetoryDetail b) {
		this.setOldBankCharges(b.getBankCharges());
	}
}