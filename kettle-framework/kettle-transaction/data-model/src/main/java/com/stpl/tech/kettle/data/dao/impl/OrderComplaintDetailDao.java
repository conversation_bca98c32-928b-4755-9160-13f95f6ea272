package com.stpl.tech.kettle.data.dao.impl;

import com.stpl.tech.kettle.data.model.OrderComplaintDetailData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface OrderComplaintDetailDao extends JpaRepository<OrderComplaintDetailData,Integer> {
    OrderComplaintDetailData findByOrderSourceId(String orderSourceId);
}
