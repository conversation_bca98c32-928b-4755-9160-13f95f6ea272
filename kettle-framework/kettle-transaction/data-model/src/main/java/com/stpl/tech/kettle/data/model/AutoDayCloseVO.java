/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

import java.util.Date;

import com.stpl.tech.master.domain.model.UnitBasicDetail;

public class AutoDayCloseVO {

	private Date businessDate;
	private String errorMessage;
	private Date closureStartTime;
	private Date closureEndTime;
	private String closureComment;
	private String currentStatus;
	private UnitBasicDetail unitBasicDetail;
	private String employee;

	public AutoDayCloseVO() {

	}

	// Construct for Incorrect Day close
	public AutoDayCloseVO(UnitBasicDetail unitBasicDetail, String employee, UnitClosureDetails unitClosureDetails,
			Date businessDate) {
		this.unitBasicDetail = unitBasicDetail;
		this.employee = employee;
		this.closureStartTime = unitClosureDetails.getClosureStartTime();
		this.closureEndTime = unitClosureDetails.getClosureEndTime();
		this.closureComment = unitClosureDetails.getClosureComment();
		this.currentStatus = unitClosureDetails.getCurrentStatus();
		this.businessDate = businessDate;
	}

	// Construct for Auto Day Close
	public AutoDayCloseVO(UnitBasicDetail unitBasicDetail, Date businessDate) {
		this.unitBasicDetail = unitBasicDetail;
		this.businessDate = businessDate;
	}

	// Construct for Day Close Failure
	public AutoDayCloseVO(UnitBasicDetail unitBasicDetail, Date businessDate, String errorMessage) {
		this.unitBasicDetail = unitBasicDetail;
		this.businessDate = businessDate;
		this.errorMessage = errorMessage;
	}

	public Date getBusinessDate() {
		return businessDate;
	}

	public void setBusinessDate(Date businessDate) {
		this.businessDate = businessDate;
	}

	public String getErrorMessage() {
		return errorMessage;
	}

	public void setErrorMessage(String errorMessage) {
		this.errorMessage = errorMessage;
	}

	public UnitBasicDetail getUnitBasicDetail() {
		return unitBasicDetail;
	}

	public void setUnitBasicDetail(UnitBasicDetail unitBasicDetail) {
		this.unitBasicDetail = unitBasicDetail;
	}

	
	public String getEmployee() {
		return employee;
	}

	public void setEmployee(String employee) {
		this.employee = employee;
	}

	public Date getClosureStartTime() {
		return closureStartTime;
	}

	public void setClosureStartTime(Date closureStartTime) {
		this.closureStartTime = closureStartTime;
	}

	public Date getClosureEndTime() {
		return closureEndTime;
	}

	public void setClosureEndTime(Date closureEndTime) {
		this.closureEndTime = closureEndTime;
	}

	public String getClosureComment() {
		return closureComment;
	}

	public void setClosureComment(String closureComment) {
		this.closureComment = closureComment;
	}

	public String getCurrentStatus() {
		return currentStatus;
	}

	public void setCurrentStatus(String currentStatus) {
		this.currentStatus = currentStatus;
	}

}
