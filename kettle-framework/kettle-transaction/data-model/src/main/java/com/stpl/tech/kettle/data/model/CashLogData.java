package com.stpl.tech.kettle.data.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "CASH_LOG_DATA")
public class CashLogData {

	private Integer cashLogDataId;
	private Integer cashDataId;
	private String transactionType;
	private String transactionCode; /* SignUpRefrral, Cash Bonus 5th SignUp, Cash Bonus 10th Sign Up */
	private String transactionCodeType; /* Referral,Cash Bonus, Initial Load */
	private BigDecimal transactionAmount;
	private Date transactiontime;
	private Integer orderId;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "CASH_LOG_DATA_ID", unique = true, nullable = false)
	public Integer getCashLogDataId() {
		return cashLogDataId;
	}

	public void setCashLogDataId(Integer cashLogDataId) {
		this.cashLogDataId = cashLogDataId;
	}

	@Column(name = "CASH_DATA_ID", nullable = false)
	public Integer getCashDataId() {
		return cashDataId;
	}

	public void setCashDataId(Integer cashDataId) {
		this.cashDataId = cashDataId;
	}

	@Column(name = "TRANSACTION_TYPE", nullable = false)
	public String getTransactionType() {
		return transactionType;
	}

	public void setTransactionType(String transactionType) {
		this.transactionType = transactionType;
	}

	@Column(name = "TRANSACTION_CODE", nullable = false)
	public String getTransactionCode() {
		return transactionCode;
	}

	public void setTransactionCode(String transactionCode) {
		this.transactionCode = transactionCode;
	}

	@Column(name = "TRANSACTION_CODE_TYPE", nullable = false)
	public String getTransactionCodeType() {
		return transactionCodeType;
	}

	public void setTransactionCodeType(String transactionCodeType) {
		this.transactionCodeType = transactionCodeType;
	}

	@Column(name = "TRANSACTION_AMOUNT", nullable = false)
	public BigDecimal getTransactionAmount() {
		return transactionAmount;
	}

	public void setTransactionAmount(BigDecimal transactionAmount) {
		this.transactionAmount = transactionAmount;
	}

	@Column(name = "ORDER_ID", nullable = true)
	public Integer getOrderId() {
		return orderId;
	}

	public void setOrderId(Integer orderId) {
		this.orderId = orderId;
	}

	@Column(name = "TRANSACTION_TIME", nullable = false)
	public Date getTransactiontime() {
		return transactiontime;
	}

	public void setTransactiontime(Date transactiontime) {
		this.transactiontime = transactiontime;
	}

}
