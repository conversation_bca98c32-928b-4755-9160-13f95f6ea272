package com.stpl.tech.kettle.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@SuppressWarnings("serial")
@Entity
@Table(name = "ORDER_ITEM_COMPLAINT")
public class OrderItemComplaint implements java.io.Serializable{

    private Integer orderItemId;

    private Integer quantity;

    private String name;

    private String dishId;

    private Float totalCost;

    private OrderComplaint orderComplaint;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name="ORDER_ITEM_ID", unique = true, nullable = false)
    public Integer getOrderItemId() {
        return orderItemId;
    }

    public void setOrderItemId(Integer orderItemId) {
        this.orderItemId = orderItemId;
    }

    @Column(name="QUANTITY", nullable = false)
    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    @Column(name="NAME", nullable = false)
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Column(name="DISH_ID", nullable=false)
    public String getDishId() {
        return dishId;
    }

    public void setDishId(String dishId) {
        this.dishId = dishId;
    }

    @Column(name="TOTAL_COST", nullable = false)
    public Float getTotalCost() {
        return totalCost;
    }

    public void setTotalCost(Float totalCost) {
        this.totalCost = totalCost;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name="ORDER_ID", nullable = false)
    public OrderComplaint getOrderComplaint() {
        return orderComplaint;
    }

    public void setOrderComplaint(OrderComplaint orderComplaint) {
        this.orderComplaint = orderComplaint;
    }
}
