/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;

@SuppressWarnings("serial")
@Entity
@Table(name = "SETTLEMENT_DETAIL")
public class SettlementDetail implements Serializable {

	private Integer id;

	private int unitId;

	private Date time;

	private String settlementServiceProvider;

	private BigDecimal originalAmount;
	
	private BigDecimal extraAmount;

	private BigDecimal settlementAmount;

	private int settlementTypeId;

	private String settlementServiceProviderReceipt;

	private BigDecimal unsettledAmount;

	private BigDecimal totalAmount;

	private BigDecimal closingAmount;

	private String settlementStatus;

	private String settlementClosingReceipt;

	private String settlementReceiptPath;

	private List<PullDetail> pullDetails;

	private List<SettlementDenomination> settlementDenominations;

	private Date settlementDate;

	private String serialNumber;

	private String ticketNumber;

	private String slipNumber;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "SETTLEMENT_DETAIL_ID", unique = true, nullable = false)
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@Column(name = "SETTLEMENT_UNIT", nullable = false)
	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unit) {
		this.unitId = unit;
	}

	@Column(name = "SETTLEMENT_TIME", nullable = false)
	public Date getTime() {
		return time;
	}

	public void setTime(Date time) {
		this.time = time;
	}

	@Column(name = "SETTLEMENT_SERVICE_PROVIDER", nullable = true)
	public String getSettlementServiceProvider() {
		return settlementServiceProvider;
	}

	public void setSettlementServiceProvider(String settlementServiceProvider) {
		this.settlementServiceProvider = settlementServiceProvider;
	}

	@Column(name = "SETTLEMENT_AMOUNT", nullable = true)
	public BigDecimal getSettlementAmount() {
		return settlementAmount;
	}

	public void setSettlementAmount(BigDecimal settlementAmount) {
		this.settlementAmount = settlementAmount;
	}
	
	@Column(name = "ORIGINAL_AMOUNT", nullable = true)
	public BigDecimal getOriginalAmount() {
		return originalAmount;
	}

	public void setOriginalAmount(BigDecimal originalAmount) {
		this.originalAmount = originalAmount;
	}
	
	@Column(name = "EXTRA_AMOUNT", nullable = true)
	public BigDecimal getExtraAmount() {
		return extraAmount;
	}

	public void setExtraAmount(BigDecimal extraAmount) {
		this.extraAmount = extraAmount;
	}

	@Column(name = "PAYMENT_MODE", nullable = false)
	public int getSettlementTypeId() {
		return settlementTypeId;
	}

	public void setSettlementTypeId(int settlementType) {
		this.settlementTypeId = settlementType;
	}

	@Column(name = "SETTLEMENT_SERVICE_PROVIDER_RECEIPT", nullable = true)
	public String getSettlementServiceProviderReceipt() {
		return settlementServiceProviderReceipt;
	}

	public void setSettlementServiceProviderReceipt(String settlementServiceProviderReceipt) {
		this.settlementServiceProviderReceipt = settlementServiceProviderReceipt;
	}

	@Column(name = "UNSETTLED_AMOUNT", nullable = true)
	public BigDecimal getUnsettledAmount() {
		return unsettledAmount;
	}

	public void setUnsettledAmount(BigDecimal unsettledAmount) {
		this.unsettledAmount = unsettledAmount;
	}

	@Column(name = "TOTAL_AMOUNT", nullable = false)
	public BigDecimal getTotalAmount() {
		return totalAmount;
	}

	public void setTotalAmount(BigDecimal totalAmount) {
		this.totalAmount = totalAmount;
	}

	@Column(name = "CLOSING_AMOUNT", nullable = true)
	public BigDecimal getClosingAmount() {
		return closingAmount;
	}

	public void setClosingAmount(BigDecimal closingAmount) {
		this.closingAmount = closingAmount;
	}

	@Column(name = "SETTLEMENT_STATUS", nullable = false)
	public String getSettlementStatus() {
		return settlementStatus;
	}

	public void setSettlementStatus(String settlementStatus) {
		this.settlementStatus = settlementStatus;
	}

	@Column(name = "SETTLEMENT_CLOSING_RECEIPT", nullable = true)
	public String getSettlementClosingReceipt() {
		return settlementClosingReceipt;
	}

	public void setSettlementClosingReceipt(String settlementClosingReceipt) {
		this.settlementClosingReceipt = settlementClosingReceipt;
	}

	@Column(name = "SETTLEMENT_RECEIPT_PATH", nullable = true)
	public String getSettlementReceiptPath() {
		return settlementReceiptPath;
	}

	public void setSettlementReceiptPath(String settlementReceiptPath) {
		this.settlementReceiptPath = settlementReceiptPath;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "settlementDetail")
	public List<PullDetail> getPullDetails() {
		return pullDetails;
	}

	public void setPullDetails(List<PullDetail> pullDetails) {
		this.pullDetails = pullDetails;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "settlementDetail")
	public List<SettlementDenomination> getSettlementDenominations() {
		return settlementDenominations;
	}

	public void setSettlementDenominations(List<SettlementDenomination> settlementDenominations) {
		this.settlementDenominations = settlementDenominations;
	}

	@Column(name = "SETTLEMENT_DATE")
	public Date getSettlementDate() {
		return settlementDate;
	}

	public void setSettlementDate(Date settlementDate) {
		this.settlementDate = settlementDate;
	}

	@Column(name = "SERIAL_NUMBER")
	public String getSerialNumber() {
		return serialNumber;
	}

	public void setSerialNumber(String serialNumber) {
		this.serialNumber = serialNumber;
	}

	@Column(name = "TICKET_NUMBER")
	public String getTicketNumber() {
		return ticketNumber;
	}

	public void setTicketNumber(String ticketNumber) {
		this.ticketNumber = ticketNumber;
	}

	@Column(name = "SLIP_NUMBER")
	public String getSlipNumber() {
		return slipNumber;
	}

	public void setSlipNumber(String slipNumber) {
		this.slipNumber = slipNumber;
	}
}
