package com.stpl.tech.kettle.customer.dao;

import java.util.Date;
import java.util.List;
import java.util.Set;

import com.stpl.tech.kettle.core.FeedbackEventStatus;
import com.stpl.tech.kettle.core.FeedbackEventType;
import com.stpl.tech.kettle.core.FeedbackFrequency;
import com.stpl.tech.kettle.core.FeedbackSource;
import com.stpl.tech.kettle.core.FeedbackStatus;
import com.stpl.tech.kettle.core.data.vo.AuditTokenInfo;
import com.stpl.tech.kettle.core.data.vo.FeedbackEventInfo;
import com.stpl.tech.kettle.core.data.vo.FeedbackOrderMetadata;
import com.stpl.tech.kettle.core.data.vo.FeedbackRatingData;
import com.stpl.tech.kettle.core.data.vo.FeedbackTokenInfo;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.FeedbackDetail;
import com.stpl.tech.kettle.data.model.FeedbackEvent;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.data.model.OrderDetailForFeedback;
import com.stpl.tech.kettle.data.model.OrderFeedbackResponse;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.external.EventDetail;
import com.stpl.tech.kettle.domain.model.webengage.survey.WebEngageSurveyForm;
import com.stpl.tech.master.core.notification.sms.ShortUrlData;
import com.stpl.tech.master.data.dao.AbstractDao;

public interface FeedbackManagementDao extends AbstractDao {

    public List<FeedbackEventInfo> getPendingFeedbackEvents(FeedbackSource source, Date startTime, Date endTime);

    public boolean updateFeedbackEventStatus(List<Integer> eventIds, FeedbackEventStatus status);

    public FeedbackRatingData addFeedback(EventDetail event, FeedbackTokenInfo tokenInfo);

    public FeedbackOrderMetadata getOrderFeedbackDetail(int orderId, FeedbackSource source);

    public Date updateFeedbackEventStatus(int eventId, ShortUrlData shortUrl, String longUrl,
                                          FeedbackEventStatus status);

    void generateFeedBackEvent(int orderId, String orderSource, Date currentTimestamp,
                               FeedbackEventType eventType, FeedbackDetail feedback, FeedbackSource[] eventSource);

    public FeedbackEvent createFeedbackEvent(FeedbackSource source, int orderId, String orderSource,
                                             FeedbackDetail feedback, Date currentTimestamp, FeedbackEventType eventType, Integer rating, int brandId);

    public FeedbackEvent createFeedbackEvent(FeedbackSource source, int orderId, String orderSource, int feedbackId,
                                             Date currentTimestamp, FeedbackEventType eventType, Integer rating, int brandId);

    FeedbackDetail getFeedback(int orderId, FeedbackSource source, FeedbackEventType eventType);

    public FeedbackDetail generateFeedbackData(int orderId, int unitId, String orderSource, Set<Integer> productIds,
                                               Customer customer, Date currentTimestamp, FeedbackEventType eventType, FeedbackSource... eventSource);

    public List<FeedbackEventInfo> getPendingElaboratedFeedbackEvents(FeedbackSource source, Date startTime,
                                                                      Date endTime);

    public void updateFeedbackData(FeedbackFrequency frequency);

    public Integer addAudit(EventDetail event, AuditTokenInfo auditInfo);

    public Integer addOrderNPSDetail(WebEngageSurveyForm form);

    public void runNpsProc();

    public Date getTriggerTime(FeedbackSource source, String orderSource, Date currentTimestamp, FeedbackEventType eventType);

    public List<FeedbackEventInfo> getPendingNPSEvents(FeedbackSource source);

    public List<FeedbackEventInfo> getInAppPendingNPSEvents(FeedbackSource source);

    public boolean updateFeedbackDetail(Integer feedbackId, FeedbackStatus status);

    public boolean cancelFeedBackforOrder(Integer orderId, FeedbackEventType nps);

    public boolean availableForNPSEvent(int customerId, Date triggerTime);

    public List<FeedbackEventInfo> getNotifiedNPSEventsForLastDay(FeedbackSource source);

    public FeedbackEventInfo getFeedbackEventInfo(int feedbackId, FeedbackSource qr);

    public FeedbackEvent getFeedbackEvent(int feedbackId);

    public boolean updateCustomerInfoInFeedbackData(int feedbackId, int customerId, String emailId);

    Integer getOrderId(Integer feedbackId);

    public OrderFeedbackResponse getResponseForFeedbackId(Integer feedbackId);

    public boolean updateOrderFeedbackEvent(OrderDetailForFeedback feedbackData, Integer rating);

    FeedbackDetail getFeedbackForSource(Integer orderId, FeedbackSource source);

    public void sendCallBackNpsSlack(OrderDetailForFeedback nps, CustomerInfo customerInfo, String managerName,
                                     OrderDetail order);

    OrderDetail getOrderDetail(Integer orderId);

    void addNpsDetailForOrderFeedback(OrderDetailForFeedback feedbackData, CustomerInfo customerInfo);

    FeedbackEventInfo getPendingNPSEventsForCustomer(FeedbackSource source, Integer orderId, Integer customerId);

    public boolean isFeedbackResponseExist(Integer feedbackId,Integer orderId);
}
