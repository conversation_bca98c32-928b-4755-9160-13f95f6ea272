package com.stpl.tech.kettle.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

@Entity
@Table(name = "CASH_PACKET_DATA")
public class CashPacketData {

	private Integer cashPacketId;
	private Integer customerId;
	private Integer cashDataId;
	private BigDecimal initialAmount;
	private BigDecimal redeemedAmount;
	private BigDecimal retainedAmount;
	private BigDecimal expiredAmount;
	private BigDecimal currentAmount;
	private Date expirationDate;
	private Date initialExpirationDate; // ( Default = Expiration Date)
	private String transactionCode; /* SignUpRefrral, Cash Bonus 5th SignUp, Cash Bonus 10th Sign Up */
	private String transactionCodeType; /* Referral,Cash Bonus, Initial Load */
	private Date lastUpdateTime;
	private Integer referentId; // ( Customer Id nullable)
	private Integer referralDataId;// ( Record Id on sign up )
	private Date creationDate;
	private Date creationTime;
	private Date activationTime;
	private String eventStatus; // Initiated,Active,Expired,Retained

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "CASH_PACKET_ID", unique = true, nullable = false)
	public Integer getCashPacketId() {
		return cashPacketId;
	}

	public void setCashPacketId(Integer cashPacketId) {
		this.cashPacketId = cashPacketId;
	}

	@Column(name = "CUSTOMER_ID", nullable = false)
	public Integer getCustomerId() {
		return customerId;
	}

	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}

	@Column(name = "CASH_DATA_ID", nullable = false)
	public Integer getCashDataId() {
		return cashDataId;
	}

	public void setCashDataId(Integer cashDataId) {
		this.cashDataId = cashDataId;
	}

	@Column(name = "INITIAL_AMOUNT", nullable = false)
	public BigDecimal getInitialAmount() {
		return initialAmount;
	}

	public void setInitialAmount(BigDecimal initialAmount) {
		this.initialAmount = initialAmount;
	}

	@Column(name = "REDEEMED_AMOUNT", nullable = false)
	public BigDecimal getRedeemedAmount() {
		return redeemedAmount;
	}

	public void setRedeemedAmount(BigDecimal redeemedAmount) {
		this.redeemedAmount = redeemedAmount;
	}

	@Column(name = "RETAINED_AMOUNT", nullable = false)
	public BigDecimal getRetainedAmount() {
		return retainedAmount;
	}

	public void setRetainedAmount(BigDecimal retainedAmount) {
		this.retainedAmount = retainedAmount;
	}

	@Column(name = "EXPIRED_AMOUNT", nullable = false)
	public BigDecimal getExpiredAmount() {
		return expiredAmount;
	}

	public void setExpiredAmount(BigDecimal expiredAmount) {
		this.expiredAmount = expiredAmount;
	}

	@Column(name = "CURRENT_AMOUNT")
	public BigDecimal getCurrentAmount() {
		return currentAmount;
	}

	public void setCurrentAmount(BigDecimal currentAmount) {
		this.currentAmount = currentAmount;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "EXPIRATION_DATE")
	public Date getExpirationDate() {
		return expirationDate;
	}

	public void setExpirationDate(Date expirationDate) {
		this.expirationDate = expirationDate;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "INITIAL_EXPIRATION_DATE")
	public Date getInitialExpirationDate() {
		return initialExpirationDate;
	}

	public void setInitialExpirationDate(Date extendedExpiryDate) {
		this.initialExpirationDate = extendedExpiryDate;
	}

	@Column(name = "TRANSACTION_CODE")
	public String getTransactionCode() {
		return transactionCode;
	}

	public void setTransactionCode(String transactionCode) {
		this.transactionCode = transactionCode;
	}

	@Column(name = "TRANSACTION_CODE_TYPE")
	public String getTransactionCodeType() {
		return transactionCodeType;
	}

	public void setTransactionCodeType(String transactionCodeType) {
		this.transactionCodeType = transactionCodeType;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LAST_UPDATE_TIME")
	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdatedTime) {
		this.lastUpdateTime = lastUpdatedTime;
	}

	@Column(name = "REFERENT_ID")
	public Integer getReferentId() {
		return referentId;
	}

	public void setReferentId(Integer referentId) {
		this.referentId = referentId;
	}

	@Column(name = "REFERRAL_DATA_ID")
	public Integer getReferralDataId() {
		return referralDataId;
	}

	public void setReferralDataId(Integer referralDataId) {
		this.referralDataId = referralDataId;
	}

	@Temporal(TemporalType.DATE)
	@Column(name = "CREATION_DATE")
	public Date getCreationDate() {
		return creationDate;
	}

	public void setCreationDate(Date creationDate) {
		this.creationDate = creationDate;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATION_TIME")
	public Date getCreationTime() {
		return creationTime;
	}

	public void setCreationTime(Date creationTime) {
		this.creationTime = creationTime;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "ACTIVATION_TIME")
	public Date getActivationTime() {
		return activationTime;
	}

	public void setActivationTime(Date activationTime) {
		this.activationTime = activationTime;
	}

	@Column(name = "EVENT_STATUS")
	public String getEventStatus() {
		return eventStatus;
	}

	public void setEventStatus(String eventStatus) {
		this.eventStatus = eventStatus;
	}

}
