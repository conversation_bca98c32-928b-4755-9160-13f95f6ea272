package com.stpl.tech.kettle.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;


@Entity
@Table(name = "BCX_SUB_CATEGORY_SUPERU")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class BcxSubCategorySuperU implements Serializable {


    private static final long serialVersionUID = 9060271515088777955L;
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "BCX_SUB_CATEGORY_SUPER_ID")
    Integer bcxSubCategoryId;
    @Column(name = "BCX_SUB_CATEGORY_NAME")
    String subCategoryName;

    @Column(name = "FEEDBACK")
    String feedBack;


    @Column(name = "PRIORITY_ORDER")
    Integer priorityOrder;

    @Column(name = "DEFAULT_FEEDBACK")
    String defaultFeedback;

    @Column(name = "SUB_CATEGORY_ALIAS")
    String subCatAlias;
    @Transient
    BigDecimal weight;
    @Transient
    int value;

    @Transient
    BigDecimal subCategoryRating;

    @Transient
    boolean isApplicable;
}
