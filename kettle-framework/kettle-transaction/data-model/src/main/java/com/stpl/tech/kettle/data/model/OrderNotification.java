package com.stpl.tech.kettle.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;

import static javax.persistence.GenerationType.IDENTITY;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
@Entity
@Table(name = "ORDER_NOTIFICATION_DATA")
public class OrderNotification implements Serializable {
    private static final long serialVersionUID = 3273438475305222547L;

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "ORDER_NOTIFICATION_ID", unique = true, nullable = false)
    private Integer orderNotificationId;
    @Column(name = "ORDER_FEEDBACK_URL",nullable = true,length = 500)
    private String orderFeedbackUrl ;
    @Column(name = "ORDER_RECIEPT_URL",nullable = true,length = 500)
    private String orderRecieptUrl ;
    @Column(name = "LOYAL_TEA_TOTAL_COUNT",nullable = true)
    private Integer loyalTeaTotalCount;
    @Column(name = "TOTAL_LOYAL_TEA_POINTS",nullable = true)
    private Integer totalLoyalTeaPoint;
    @Column(name = "LOYAL_TEA_POINTS",nullable = true)
    private Integer loyalTeaPoints;
    @Column(name = "LOYAL_TEA_COUNT",nullable = true)
    private Integer loyalTeaCount;
    @Column(name = "UNIT_NAME",nullable = false,length = 150)
    private String unitName;
    @Column(name = "ORDER_AMT", precision = 10)
    private BigDecimal orderAmt;
    @Column(name = "EARNED_LOYAL_TEA_POINT",  nullable = true)
    private int earnedLoyalTeaPoint;
    @Column(name = "SAVING_TEXT", length=450, nullable = true)
    private String savingText;
    @Column(name = "SAVING_AMT",  precision = 10,nullable = true)
    private BigDecimal savingAmt;
    @Column(name = "IS_SUBSCRIPTION_USED",  length=2,nullable = true)
    private String isSubscriptionUsed;
    @Column(name = "IS_SUBSCRIPTION_PURCHASED",  length=2,nullable = true)
    private String isSubscriptionPurched;
    @Column(name = "IS_SMS_SUBSCRIBER",  length=2,nullable = true)
    private String isSmsSubscriber;
    @Column(name = "IS_WHATSAPP_OPT_IN",  length=2,nullable = true)
    private String isWhatsappOptIn;
    @Column(name = "CUSTOMER_FNAME",  length=45,nullable = false)
    private String customerName;
    @Column(name = "CUSTOMER_CONTACT",  length=45 ,nullable = false)
    private String customerContact;
    @Column(name = "GENERATED_ORDER_ID",  unique = true,nullable = false, length=45)
    private String generatedOrderId;
    @Column(name = "WALLET_PENDING_AMT",  nullable = true)
    private Integer walletPendingAmt;
    @Column(name = "WALLET_EXTRA_AMT",nullable = true,  precision = 10)
    private BigDecimal walletExtraAmt;
    @Column(name = "WALLET_SAVING_AMT",nullable = true,length = 45)
    private String walletSavingAmt;
    @Column(name = "WALLET_PURCHASE_AMT",nullable = true)
    private Integer walletPurchaseAmt;
    @Column(name = "ITEM_CODE",nullable = true,length = 45)
    private String itemCode;
    @Column(name = "CASH_PENDING_AMT",nullable = true,length = 45)
    private String cashPendingAmt;
    @Column(name = "VOUCHER_CODE",nullable = true,length = 150)
    private String voucherCode;
    @Column(name = "CASHBACK_AMT",nullable = true,  precision = 10)
    private BigDecimal cashBackAmt;
    @Column(name = "CASHBACK_START_DATE",nullable = true,  length = 150)
    private String cashBackStartDate;
    @Column(name = "REFUND_AMT",nullable = true)
    private Integer refundAmt;
    @Column(name = "USED_AMT",nullable = true)
    private Integer usedAmt;
    @Column(name = "SUBSCRIPTION_NAME",nullable = true,  length = 45)
    private String subscriptionName;
    @Column(name = "SUBSCR_OFFER_DESCRIPTION",nullable = true, length=550)
    private String offerDescription;
    @Column(name = "VALID_DAYS",nullable = true,  precision = 10)
    private  Integer validDays;
    @Column(name = "PLAN_END_DATE",nullable = true,length = 45)
    private String planEndDate;
    @Column(name = "SELECT_OVERALL_SAVING",nullable = true)
    private Integer selectOverallSaving;
    @Column(name = "SELECT_SAVING_AMOUNT",nullable = true,  precision = 10)
    private BigDecimal selectSavingAmt;
    @Column(name = "NEXT_OFFER_TEXT",nullable = true, length=450)
    private String nextOfferText;
    @Column(name = "OFFER_CODE",nullable = true,  length=450)
    private String offerCode;
    @Column(name = "VALIDITY_TILL",nullable = true,  length=45)
    private String validityTill;
    @Column(name = "DAYS_LEFT",nullable = true)
    private Integer daysLeft;
    @Column(name = "CHANNEL_PARTNER",nullable = true, length = 45)
    private String channelPartner;
    @Column(name = "SUBSCR_VALIDITY_IN_DAYS",nullable = true)
    private Integer subscriptionValidityInDays;
    @Column(name = "IS_LOYALTY_UNLOCKED",nullable = true,  length= 2)
    private String isLoyaltyUnlocked;
    @Column(name = "ORDER_ID",unique = true ,nullable = false)
    private Integer orderId;

}
