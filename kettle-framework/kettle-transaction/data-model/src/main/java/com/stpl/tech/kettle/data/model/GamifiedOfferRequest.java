package com.stpl.tech.kettle.data.model;

import com.stpl.tech.kettle.core.OfferSource;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.master.domain.model.CampaignDetail;
import com.stpl.tech.master.domain.model.CampaignDetailResponse;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class GamifiedOfferRequest implements Serializable {

    private static final long serialVersionUID = -1759727324831877560L;
    private String contactNumber;
    private String campaignToken;
    private String authToken;
    private String utmSource;
    private String utmMedium;
    private String email;
    private Integer flow;
    private Boolean whatsappOpt;
    private Integer gameScore;

    private CampaignDetailResponse campaignDetailResponse;

    private CampaignDetail campaignDetail;

    private Customer customer;

    private OfferSource offerSource;

    private List<String> offerStrings;

    private Map<String,List<GamifiedOfferResponse>> activeGameOffer;
    private String offerType;
    private Integer unitId;

}
