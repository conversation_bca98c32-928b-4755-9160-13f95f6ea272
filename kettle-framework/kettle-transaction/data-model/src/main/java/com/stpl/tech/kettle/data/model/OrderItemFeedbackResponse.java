package com.stpl.tech.kettle.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@Entity
@Table(name = "ORDER_ITEM_FEEDBACK_RESPONSE")
public class OrderItemFeedbackResponse {

    private Integer orderItemFeedbackResponseId;
    private OrderFeedbackResponse orderFeedbackResponse;
    private Integer orderItemId;
    private Integer productId;
    private String productName;
    private Integer quantity;
    private String issueTags;
    private String dimension;
    private String customisation;
    private String question;
    private Integer itemRating;
    private String itemComment;
    private String imageUrl;
    private String videoUrl;
    private Integer maxRating;
    private String feedbackType;


    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "ORDER_ITEM_FEEDBACK_RESPONSE_ID", nullable = false)
    public Integer getOrderItemFeedbackResponseId() {
        return orderItemFeedbackResponseId;
    }

    public void setOrderItemFeedbackResponseId(Integer orderItemFeedbackResponseId) {
        this.orderItemFeedbackResponseId = orderItemFeedbackResponseId;
    }

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "ORDER_FEEDBACK_RESPONSE_ID", nullable = false)
    public OrderFeedbackResponse getOrderFeedbackResponse() {
        return orderFeedbackResponse;
    }

    public void setOrderFeedbackResponse(OrderFeedbackResponse orderFeedbackResponse) {
        this.orderFeedbackResponse = orderFeedbackResponse;
    }

    @Column(name = "ORDER_ITEM_ID")
    public Integer getOrderItemId() {
        return orderItemId;
    }

    public void setOrderItemId(Integer orderItemId) {
        this.orderItemId = orderItemId;
    }

    @Column(name = "PRODUCT_ID")
    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    @Column(name = "PRODUCT_NAME")
    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    @Column(name = "QUANTITY")
    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    @Column(name = "ISSUE_TAGS")
    public String getIssueTags() {
        return issueTags;
    }

    public void setIssueTags(String issueTags) {
        this.issueTags = issueTags;
    }

    @Column(name = "DIMENSION")
    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    @Column(name = "CUSTOMISATION")
    public String getCustomisation() {
        return customisation;
    }

    public void setCustomisation(String customisation) {
        this.customisation = customisation;
    }

    @Column(name = "QUESTION")
    public String getQuestion() {
        return question;
    }

    public void setQuestion(String question) {
        this.question = question;
    }

    @Column(name = "ITEM_RATING")
    public Integer getItemRating() {
        return itemRating;
    }

    public void setItemRating(Integer itemRating) {
        this.itemRating = itemRating;
    }

    @Column(name = "ITEM_COMMENT")
    public String getItemComment() {
        return itemComment;
    }

    public void setItemComment(String itemComment) {
        this.itemComment = itemComment;
    }

    @Column(name = "IMAGE_URL")
    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    @Column(name = "VIDEO_URL")
    public String getVideoUrl() {
        return videoUrl;
    }

    public void setVideoUrl(String videoUrl) {
        this.videoUrl = videoUrl;
    }

    @Column(name = "MAX_RATING")
    public Integer getMaxRating() {
        return maxRating;
    }

    public void setMaxRating(Integer maxRating) {
        this.maxRating = maxRating;
    }

    @Column(name = "FEEDBACK_TYPE")
    public String getFeedbackType() {
        return feedbackType;
    }

    public void setFeedbackType(String feedbackType) {
        this.feedbackType = feedbackType;
    }
}
