package com.stpl.tech.kettle.data.model;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class CustomerDataLookupId implements Serializable {

	private static final long serialVersionUID = 7152759860671112734L;

	private int id;

	private String type;

	@Override
	public int hashCode() {
		return super.hashCode();
	}

	@Override
	public boolean equals(Object obj) {
		return super.equals(obj);
	}

}
