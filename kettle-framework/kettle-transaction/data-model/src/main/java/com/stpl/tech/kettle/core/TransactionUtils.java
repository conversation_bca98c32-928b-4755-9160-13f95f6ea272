/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.DayOfWeek;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.springframework.data.util.Pair;

import com.stpl.tech.kettle.core.notification.AssemblySlipReceipt;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.core.notification.OrderKOTReceipt;
import com.stpl.tech.kettle.core.notification.OrderPrintReceipt;
import com.stpl.tech.kettle.core.notification.OrderReceipt;
import com.stpl.tech.kettle.core.notification.receipt.AssemblySlipRawPrintReceipt;
import com.stpl.tech.kettle.core.notification.receipt.BookWastageKOTRawReceipt;
import com.stpl.tech.kettle.core.notification.receipt.CustomerCommunicationRawPrint;
import com.stpl.tech.kettle.core.notification.receipt.OrderKOTRawPrintReceipt;
import com.stpl.tech.kettle.core.notification.receipt.OrderRawPrintReceipt;
import com.stpl.tech.kettle.core.notification.receipt.TableSettlementReceipt;
import com.stpl.tech.kettle.customer.service.CustomerService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.model.CashCardDetail;
import com.stpl.tech.kettle.data.model.UnitTableMappingDetail;
import com.stpl.tech.kettle.domain.model.CashCardType;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.IterationType;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.domain.model.Settlement;
import com.stpl.tech.kettle.domain.model.Subscription;
import com.stpl.tech.master.core.CacheReferenceType;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Address;
import com.stpl.tech.master.domain.model.BillType;
import com.stpl.tech.master.domain.model.PaymentCategory;
import com.stpl.tech.master.domain.model.PaymentMode;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.TaxType;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.domain.model.UnitStatus;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.PrintTemplate;

public class TransactionUtils extends AppUtils {

	private static final String SCOREBOARD_FILE_NAME_FORMAT = "%s_%s_%s";

	private static final int OUTLET_VISIT_TRIP_TIME = 240;
	
	private static final long DEFAULT_ORDER_PREP_TIME = 9;

	private static final ReceiptType[] KOT_ITEM_LIST = { ReceiptType.KOT_FOOD_PRINT, ReceiptType.KOT_COLD_PRINT,
			ReceiptType.KOT_HOT_PRINT, ReceiptType.KOT_COMBO_PRINT, ReceiptType.KOT_MERCHANDISE_PRINT,
			ReceiptType.KOT_BAKERY_PRINT,ReceiptType.KOT_CLUBBED_PRINT };

	public static String getImage(String file) throws FileNotFoundException, IOException {
		byte[] imgBytes = IOUtils.toByteArray(Thread.currentThread().getContextClassLoader().getResourceAsStream(file));
		byte[] imgBytesAsBase64 = Base64.encodeBase64(imgBytes);
		String imgDataAsBase64 = new String(imgBytesAsBase64);
		return "data:image/png;base64," + imgDataAsBase64;
	}

	public static final PrintTemplate getReceiptObject(String urlBasePath, MasterDataCache metadataCache, ReceiptType type, OrderInfo info,
													   String basePath, boolean rawPrint, EnvironmentProperties env) throws DataNotFoundException {
		if (rawPrint) {
			return getRawReceiptObject(metadataCache, type, info, basePath,env);
		} else {
			return getHTMLReceiptObject(urlBasePath, metadataCache, type, info, basePath);
		}
	}

	private static PrintTemplate getRawReceiptObject(MasterDataCache metadataCache, ReceiptType type, OrderInfo info,
													 String basePath, EnvironmentProperties env) throws DataNotFoundException {

		Unit unit = metadataCache.getUnit(info.getOrder().getUnitId());
		switch (type) {
		case ORDER_CAFE_PRINT:
			return new OrderRawPrintReceipt(unit, info, basePath, getPromotionalCode(unit),env);
		case ORDER_COD_PRINT:
			return new OrderRawPrintReceipt(unit, info, basePath, getPromotionalCode(unit),env);
		case KOT_FOOD_PRINT:
			return new OrderKOTRawPrintReceipt(unit, metadataCache, info, KOTType.FOOD, basePath,env);
		case KOT_COLD_PRINT:
			return new OrderKOTRawPrintReceipt(unit, metadataCache, info, KOTType.COLD, basePath,env);
		case KOT_HOT_PRINT:
			return new OrderKOTRawPrintReceipt(unit, metadataCache, info, KOTType.HOT, basePath,env);
		case KOT_COMBO_PRINT:
			return new OrderKOTRawPrintReceipt(unit, metadataCache, info, KOTType.COMBOS, basePath,env);
		case KOT_MERCHANDISE_PRINT:
			return new OrderKOTRawPrintReceipt(unit, metadataCache, info, KOTType.MERCHANDISE, basePath,env);
		case KOT_CLUBBED_PRINT:
				return new OrderKOTRawPrintReceipt(unit, metadataCache, info, KOTType.CLUBBED, basePath,env);
		case KOT_BAKERY_PRINT:
			return new OrderKOTRawPrintReceipt(unit, metadataCache, info, KOTType.BAKERY, basePath,env);
		case CUSTOMER_COMMUNICATION:
			return new CustomerCommunicationRawPrint(unit, metadataCache, info, basePath,env);
		default:
			throw new IllegalArgumentException("Unsupported type " + type.name());
		}
	}

	private static PrintTemplate getRawReceiptObjectBookWastage(MasterDataCache metadataCache, ReceiptType type, Order detail,
													 String basePath, EnvironmentProperties env) throws DataNotFoundException {

		Unit unit = metadataCache.getUnit(detail.getUnitId());
		switch (type) {
			case KOT_FOOD_PRINT:
				return new BookWastageKOTRawReceipt(unit,detail,basePath,env,metadataCache,KOTType.FOOD);
			case KOT_COLD_PRINT:
				return new BookWastageKOTRawReceipt(unit,detail,basePath,env,metadataCache,KOTType.COLD);
			case KOT_HOT_PRINT:
				return new BookWastageKOTRawReceipt(unit,detail,basePath,env,metadataCache,KOTType.HOT);
			case KOT_COMBO_PRINT:
				return new BookWastageKOTRawReceipt(unit,detail,basePath,env,metadataCache,KOTType.COMBOS);
			case KOT_MERCHANDISE_PRINT:
				return new BookWastageKOTRawReceipt(unit,detail,basePath,env,metadataCache,KOTType.MERCHANDISE);
			case KOT_CLUBBED_PRINT:
				return new BookWastageKOTRawReceipt(unit,detail,basePath,env,metadataCache, KOTType.CLUBBED);
			case KOT_BAKERY_PRINT:
				return new BookWastageKOTRawReceipt(unit,detail,basePath,env,metadataCache,KOTType.BAKERY);
			default:
				throw new IllegalArgumentException("Unsupported type " + type.name());
		}
	}

	public static final OrderReceipt getHTMLReceiptObject(String urlBasePath,  MasterDataCache metadataCache, ReceiptType type,
			OrderInfo info, String basePath) throws DataNotFoundException {

		Unit unit = metadataCache.getUnit(info.getOrder().getUnitId());
		switch (type) {
		case ORDER_CAFE_PRINT:
			return new OrderPrintReceipt(urlBasePath, unit, info, basePath, TransactionConstants.CAFE_BILL_PRINT_TEMPLATE,
					getPromotionalCode(unit));
		case ORDER_COD_PRINT:
			return new OrderPrintReceipt(urlBasePath, unit, info, basePath, TransactionConstants.COD_BILL_PRINT_TEMPLATE,
					getPromotionalCode(unit));
		case ORDER_COD_PUNCH:
			return new OrderPrintReceipt(urlBasePath, unit, info, basePath, TransactionConstants.COD_BILL_PUNCH_TEMPLATE,
					getPromotionalCode(unit));
		case KOT_FOOD_PRINT:
			return new OrderKOTReceipt(urlBasePath, unit, metadataCache, info, KOTType.FOOD, basePath);
		case KOT_COLD_PRINT:
			return new OrderKOTReceipt(urlBasePath, unit, metadataCache, info, KOTType.COLD, basePath);
		case KOT_HOT_PRINT:
			return new OrderKOTReceipt(urlBasePath, unit, metadataCache, info, KOTType.HOT, basePath);
		case KOT_COMBO_PRINT:
			return new OrderKOTReceipt(urlBasePath, unit, metadataCache, info, KOTType.COMBOS, basePath);
		case KOT_MERCHANDISE_PRINT:
			return new OrderKOTReceipt(urlBasePath, unit, metadataCache, info, KOTType.MERCHANDISE, basePath);
		case KOT_BAKERY_PRINT:
			return new OrderKOTReceipt(urlBasePath, unit, metadataCache, info, KOTType.BAKERY, basePath);
		case ASSEMBLY_SLIP_PRINT:
			return new AssemblySlipReceipt(urlBasePath, unit, info, basePath);
		default:
			throw new IllegalArgumentException("Unsupported type " + type.name());
		}
	}

	public static List<String> getKOTReceipts(String urlBasePath, MasterDataCache cache, boolean needsCafePrint, OrderInfo info,
			String basePath, boolean rawPrint,EnvironmentProperties env) throws TemplateRenderingException, DataNotFoundException {
		Map<ReceiptType,String> receipts = new HashMap<>();
		info.setBillIncludedInReceipts(true);
		for (ReceiptType rt : KOT_ITEM_LIST) {
			addKOT(urlBasePath, cache, receipts, info, rt, basePath, rawPrint,env);
		}
		return (List<String>) receipts.values();
	}

	public static List<String> getWastageKOTReceipts(String urlBasePath, MasterDataCache cache, boolean needsCafePrint, Order detail,
											  String basePath, boolean rawPrint,EnvironmentProperties env) throws TemplateRenderingException, DataNotFoundException {
		Map<ReceiptType,String> receipts = new HashMap<>();
		addWastageKOT(urlBasePath, cache, receipts, detail, ReceiptType.KOT_CLUBBED_PRINT, basePath, rawPrint,env);
		List<String>  finalWastageReciepts = new ArrayList<>();
		for(ReceiptType rt : KOT_ITEM_LIST){
			if(receipts.containsKey(rt)){
				finalWastageReciepts.add(receipts.get(rt));
			}
		}
		return finalWastageReciepts;

	}


	public static Map<ReceiptType,String> getReceipts(String urlBasePath, MasterDataCache cache, boolean needsCafePrint, OrderInfo info,
			String basePath, boolean rawPrint,EnvironmentProperties env) throws TemplateRenderingException, DataNotFoundException {
		Map<ReceiptType, String> receipts = new LinkedHashMap<>();
		boolean isCODOrder = isCODOrder(info.getOrder().getSource());
		if (isCODOrder) {
			info.setBillIncludedInReceipts(true);
			receipts.put(ReceiptType.ORDER_COD_PRINT,
					TransactionUtils.getReceiptObject(urlBasePath, cache, ReceiptType.ORDER_COD_PRINT, info, basePath, rawPrint, env).getContent());
			if (info.getOrder().getChannelPartner() == AppConstants.BAZAAR_PARTNER_ID) {
				receipts.put(ReceiptType.ORDER_COD_PUNCH, TransactionUtils
						.getReceiptObject(urlBasePath, cache, ReceiptType.ORDER_COD_PUNCH, info, basePath, false, env)
						.getContent());
			}

		} else {
			if (needsCafePrint) {
				info.setBillIncludedInReceipts(true);
				String orderPrint = TransactionUtils
						.getReceiptObject(urlBasePath, cache, ReceiptType.ORDER_CAFE_PRINT, info, basePath, rawPrint, env).getContent();
				receipts.put(ReceiptType.ORDER_CAFE_PRINT,
						orderPrint);
			} else {
				receipts.put(ReceiptType.ORDER_CAFE_PRINT,
						"");
			}
		}
		List<OrderKOTRawPrintReceipt> kots = new ArrayList<>();
		if (TransactionUtils.isSpecialOrder(info.getOrder()) || TransactionUtils.isComplimentaryOrder(info.getOrder()) || TransactionUtils.isUnsatisfiedCustomerOrder(info.getOrder()) || TransactionUtils.isPpeOrder(info.getOrder())) {
			addToKotList(kots, addKOT(urlBasePath, cache, receipts, info, ReceiptType.KOT_CLUBBED_PRINT, basePath, rawPrint, env));
		}
		addToKotList(kots, addKOT(urlBasePath, cache, receipts, info, ReceiptType.KOT_HOT_PRINT, basePath, rawPrint,env));
		addToKotList(kots, addKOT(urlBasePath, cache, receipts, info, ReceiptType.KOT_COLD_PRINT, basePath, rawPrint,env));
		addToKotList(kots, addKOT(urlBasePath, cache, receipts, info, ReceiptType.KOT_FOOD_PRINT, basePath, rawPrint,env));
		// addKOT(cache, receipts, info, ReceiptType.KOT_COMBO_PRINT, basePath);
		addToKotList(kots, addKOT(urlBasePath, cache, receipts, info, ReceiptType.KOT_BAKERY_PRINT, basePath, rawPrint,env));
		addToKotList(kots, addKOT(urlBasePath, cache, receipts, info, ReceiptType.KOT_MERCHANDISE_PRINT, basePath, rawPrint,env));
		for (OrderItem item : info.getOrder().getOrders()) {
			Product product = cache.getProduct(item.getProductId());
			if (!(product.getTaxCode().equals(AppConstants.GIFT_CARD_TAX_CODE))) {
				AssemblySlipRawPrintReceipt assembly = new AssemblySlipRawPrintReceipt(
						cache.getUnit(info.getOrder().getUnitId()), cache, info, basePath, kots,env);
				receipts.put(ReceiptType.ASSEMBLY_SLIP_PRINT,assembly.getContent());
				break;
			}
		}
		return receipts;
	}

	/**
	 * @param kots
	 * @param addKOT
	 */
	private static void addToKotList(List<OrderKOTRawPrintReceipt> kots, PrintTemplate addKOT) {
		if (addKOT != null && addKOT instanceof OrderKOTRawPrintReceipt) {
			kots.add((OrderKOTRawPrintReceipt) addKOT);
		}
	}

	private static PrintTemplate addKOT(String urlBasePath, MasterDataCache cache, Map<ReceiptType,String> receipts, OrderInfo info,
			ReceiptType receiptType, String basePath, boolean rawPrint,EnvironmentProperties env)
			throws TemplateRenderingException, DataNotFoundException {
		PrintTemplate receipt = TransactionUtils.getReceiptObject(urlBasePath, cache, receiptType, info, basePath, rawPrint,env);
		String kotPrint = receipt.getContent();
		if (kotPrint != null) {
			receipts.put(receiptType,kotPrint);
			return receipt;
		}
		return null;
	}

	private static PrintTemplate addWastageKOT(String urlBasePath, MasterDataCache cache, Map<ReceiptType,String> receipts, Order detail,
										ReceiptType receiptType, String basePath, boolean rawPrint,EnvironmentProperties env)
			throws TemplateRenderingException, DataNotFoundException {
		PrintTemplate receipt = getRawReceiptObjectBookWastage(cache,  receiptType, detail,basePath,  env);
		String kotPrint = receipt.getContent();
		if (kotPrint != null) {
			receipts.put(receiptType,kotPrint);
			return receipt;
		}
		return null;
	}

	public static boolean isCODOrder(String orderSource) {
		return orderSource != null && orderSource.equals(UnitCategory.COD.name());
	}

	public static boolean isCODUnit(UnitCategory categoty) {
		return categoty != null && categoty.equals(UnitCategory.DELIVERY);
	}

//	public static boolean isDev(EnvType envType) {
//		return !EnvType.PROD.equals(envType) && !EnvType.SPROD.equals(envType);
//		//		return EnvType.DEV.equals(envType);
//	}

	public static String getPrefix(EnvType envType) {
		return isDev(envType) ? "DEV " : "";
	}

	public static boolean isDayCloseEligible(UnitCategory family) {
		return family != null && (UnitCategory.CAFE.equals(family) || UnitCategory.DELIVERY.equals(family)
				|| UnitCategory.CHAI_MONK.equals(family));
	}

	public static ServiceType getServiceType(MasterDataCache cache, int channelPartnerId) {
		return ServiceType.valueOf(cache.getChannelPartner(channelPartnerId).getType());
	}

	public static String getAddressStringForOrder(Integer id, List<Address> addresses) {
		Address address = getAddressFromList(id, addresses);
		return address != null ? address.toString() : "No address found";
	}

	public static Address getAddressForOrder(Integer id, List<Address> addresses) {
		return getAddressFromList(id, addresses);
	}

	private static Address getAddressFromList(Integer id, List<Address> addresses) {
		if (id != null) {
			for (Address address : addresses) {
				if (id.equals(address.getId())) {
					return address;
				}
			}
		}
		return null;
	}

	public static boolean isMRP(BillType billType) {
		return billType != null && BillType.MRP.equals(billType);
	}

	public static boolean isNetPrice(BillType billType) {
		return billType != null && BillType.NET_PRICE.equals(billType);
	}

	public static boolean isActiveUnit(UnitStatus status) {
		return status != null && (UnitStatus.ACTIVE.equals(status));
	}

	public static boolean isSkipOrder(Order o) {
		return OrderStatus.CANCELLED.equals(o.getStatus())
				|| (TransactionUtils.isSpecialOrder(o) && !TransactionUtils.isEmployeeMeal(o));
	}

	public static boolean isSpecialOrder(Order order) {
		return isSpecialOrder(order.getOrderType());
	}

	public static boolean isSpecialOrder(String orderType) {
		return !(AppConstants.ORDER_TYPE_REGULAR.equals(orderType) || AppConstants.ORDER_TYPE_TESTING_ORDER.equals(orderType));
	}

	public static boolean isRegularOrder(Order order) {
		return AppConstants.ORDER_TYPE_REGULAR.equals(order.getOrderType()) || AppConstants.ORDER_TYPE_TESTING_ORDER.equals(order.getOrderType());
	}

	public static boolean isEmployeeMeal(Order order) {
		return AppConstants.ORDER_TYPE_EMPLOYEE_MEAL.equals(order.getOrderType()) || isPaidEmployeeMeal(order);
	}

	public static boolean isPaidEmployeeMeal(Order order) {
		return AppConstants.ORDER_TYPE_PAID_EMPLOYEE_MEAL.equals(order.getOrderType());
	}

	public static boolean isComplimentaryOrder(Order order) {
		return AppConstants.ORDER_TYPE_COMPLIMENTARY.equals(order.getOrderType());
	}

	public static boolean isUnsatisfiedCustomerOrder(Order order) {
		return AppConstants.ORDER_TYPE_UNSTAISFIED_CUSTOMER.equals(order.getOrderType());
	}

	public static boolean isPpeOrder(Order order) {
		return AppConstants.ORDER_TYPE_PPE_ORDER.equals(order.getOrderType());
	}

	public static String getDeliveryDays(Subscription subscription) {
		switch (subscription.getType()) {
		case MONTHLY:
			Collections.sort(subscription.getDaysOfTheMonth());
			return StringUtils.join(subscription.getDaysOfTheMonth(), ",");
		case WEEKLY:
			Collections.sort(subscription.getDaysOfTheWeek());
			List<String> list = new ArrayList<>();
			for (Integer days : subscription.getDaysOfTheWeek()) {
				list.add(getDay(days));
			}
			return StringUtils.join(list, ",");
		default:
			break;
		}
		return null;
	}

	private static String getDay(int day) {
		switch (day) {
		case 1:
			return DayOfWeek.SUNDAY.name();
		case 2:
			return DayOfWeek.MONDAY.name();
		case 3:
			return DayOfWeek.TUESDAY.name();
		case 4:
			return DayOfWeek.WEDNESDAY.name();
		case 5:
			return DayOfWeek.THURSDAY.name();
		case 6:
			return DayOfWeek.FRIDAY.name();
		case 7:
			return DayOfWeek.SATURDAY.name();
		default:
			break;
		}
		return null;
	}

	public static String getDeliveryTimes(Subscription subscription) {
		Date currentDate = AppUtils.getCurrentDate();
		Collections.sort(subscription.getTimeOfTheDay());
		List<String> list = new ArrayList<>();
		for (Integer days : subscription.getTimeOfTheDay()) {
			Date newDate = AppUtils.getTimeOfDay(currentDate, 15 * days);
			list.add(AppUtils.getOnlyTime(newDate));
		}
		return StringUtils.join(list, ",");
	}

	public static String getPromotionalCode(Unit unit) {

		if (AppUtils.getCurrentDate().before(AppUtils.getStartOfMonth(117, 2))) {
			switch (unit.getRegion()) {
			case "NCR":
				return "Don't  miss  5%  additional  value  on  Chaayos  Pre-Paid  Cards  and  Gift-Cards ";
			case "MUMBAI":
				return "Don't  miss  5%  additional  value  on  Chaayos  Pre-Paid  Cards  and  Gift-Cards ";
			default:
				break;
			}
			return null;
		} else {
			return null;
		}
	}

	public static boolean isTakeawayOrder(String orderSource) {
		return orderSource != null && orderSource.equals(UnitCategory.TAKE_AWAY.name());
	}

	public static int getIterationNumber(IterationType type, Date date) {

		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		switch (type) {
		case MOM:
			return cal.get(Calendar.MONTH);
		case WOW:
			return cal.get(Calendar.WEEK_OF_YEAR);
		default:
			return -1;
		}

	}

	public static String getFileName(String prefix, IterationType type, Date date) {
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		switch (type) {
		case MOM:
			return String.format(SCOREBOARD_FILE_NAME_FORMAT, prefix, type.name().toLowerCase(),
					new DateTime(cal.getTime().getTime(), DateTimeZone.forID("Asia/Kolkata"))
							.toString(TransactionConstants.MONTH_FORMATTER));
		case WOW:
			return String.format(SCOREBOARD_FILE_NAME_FORMAT, prefix, type.name().toLowerCase(),
					new DateTime(cal.getTime().getTime(), DateTimeZone.forID("Asia/Kolkata"))
							.toString(TransactionConstants.DATE_FORMATTER));
		default:
			return null;
		}
	}

	public static int getYear(IterationType type, Date date) {

		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		return cal.get(Calendar.YEAR);

	}

	public static boolean checkWithinTripThresholdTime(Date lastOutletVisitTime,MasterDataCache masterDataCache) {
		if (lastOutletVisitTime != null) {
			Integer loyaltyAwardThresholdTime = null;
			try {
				loyaltyAwardThresholdTime = Integer.parseInt(masterDataCache.getCacheReferenceMetadata(CacheReferenceType.
						LOYALTY_AWARD_THRESHOLD_TIME));
			}catch (Exception e){
				loyaltyAwardThresholdTime = OUTLET_VISIT_TRIP_TIME;
			}
			Date currentTime = AppUtils.getCurrentTimestamp();

			long duration = currentTime.getTime() - lastOutletVisitTime.getTime();

			long diffInMinutes = TimeUnit.MILLISECONDS.toMinutes(duration);
			if (diffInMinutes < loyaltyAwardThresholdTime) {
				return true;
			}
		}
		return false;
	}

	public static boolean eligibleForSignupOffer(CustomerService customerService, Customer customer,MasterDataCache masterDataCache) {
		if (customer.isAvailedSignupOffer()) {
			return false;
		} else {
			if (customer.getOrderCount() == null || customer.getOrderCount() == 0) {
				return false;
			}
			if (customer.getOrderCount() != null && customer.getOrderCount() == 1) {
				return !TransactionUtils.checkWithinTripThresholdTime(customer.getLastOrderTime(),masterDataCache)
						&& customerService.eligibleForSignupOffer(customer.getId());
			} else if (customer.getOrderCount() != null && customer.getOrderCount() == 2) {
				return !TransactionUtils.checkWithinTripThresholdTime(customer.getLastOrderTime(),masterDataCache);
			} else {
				return customerService.eligibleForSignupOffer(customer.getId());
			}
		}
	}

	public static Map<String, Pair<Integer, Integer>> getEmployeeMealProductThreshold() {
		Map<String, Pair<Integer, Integer>> map = new HashMap<>();
		map.put(TransactionConstants.BEVERAGE_PRODUCT_TYPE_STR, Pair.of(2, 0));
		map.put(TransactionConstants.FOOD_PRODUCT_TYPE_STR, Pair.of(1, 0));
		return map;
	}

	public static boolean isTaxApplicable(String orderSource, BillType billType, TaxType taxType) {
		boolean isApplicable = true;
		if (BillType.ZERO_TAX.equals(billType)) {
			return false;
		}
		if (isCODOrder(orderSource) || BillType.MRP.equals(billType)) {
			switch (taxType) {
			case SERVICE_TAX:
				isApplicable = false;
				break;
			case SB_CESS:
				isApplicable = false;
				break;
			case KK_CESS:
				isApplicable = false;
				break;
			default:
				isApplicable = true;
				break;
			}
		}
		return isApplicable;
	}

	/**
	 * @param orderSource
	 * @param channelPartner
	 * @return
	 */
	public static boolean isWebDineIn(String orderSource, int channelPartner) {
		return orderSource != null
				&& (UnitCategory.CAFE.name().equals(orderSource) || UnitCategory.TAKE_AWAY.name().equals(orderSource))
				&& channelPartner == AppConstants.CHANNEL_PARTNER_WEB_APP;
	}

	public static boolean isOnlinePayment(List<Settlement> settlements) {
		boolean flag;
		flag = settlements != null && !settlements.isEmpty()
				&& settlements.get(0).getModeDetail().getCategory().equals(PaymentCategory.ONLINE);
		return flag;
	}

	public static boolean isBillBookOrder(Order order) {
		return order.getBillBookNo() != null;
	}

	public static boolean isPartnetOrder(String type) {
		return "THIRD_PARTY".equals(type);
	}

	public static String getGyftrPurchaseCashCardMessage(OrderInfo info, OrderItem item, CashCardDetail cardDetail,
			BigDecimal pendingAmount) {
		String fName = info.getCustomer().getFirstName();
		fName = Character.toString(fName.charAt(0)).toUpperCase() + fName.substring(1);
		String msg = null;
		msg = String.format(
				"%s, Congrats on purchase of Chaayos Gift Card #%s (%s). Amnt. Rs%s, valid till %s. Bal. in your account is Rs %s",
				fName, item.getItemCode().toUpperCase(), item.getVoucherCode(),
				cardDetail.getCashPendingAmount().setScale(0, BigDecimal.ROUND_HALF_UP).toString(),
				AppUtils.getSMSTemplateDate(item.getValidUpto()),
				pendingAmount.setScale(0, BigDecimal.ROUND_HALF_UP).toString());
		return msg;
	}

	public static boolean isCancelled(String orderStatus) {
		return OrderStatus.CANCELLED.name().equals(orderStatus)
				|| OrderStatus.CANCELLED_REQUESTED.name().equals(orderStatus);
	}

	public static boolean hasSignupOffer(Order order) {
		return order.isContainsSignupOffer() || hasSignupOffer(order.getOfferCode());
	}

	public static boolean hasSignupOffer(String offerCode) {
		return TransactionConstants.SIGNUP_OFFER_CODE.equals(offerCode);
	}

	public static String getTableSettlementReceipt(Map<Integer, BigDecimal> settlementMap,
			Map<Integer, PaymentMode> paymentModes, UnitTableMappingDetail table, String basePath) throws TemplateRenderingException {
		return new TableSettlementReceipt(settlementMap, paymentModes, table, basePath).getContent();
	}

	public static boolean isTableOrder(Order order) {
		return order.getTableRequestId() != null && order.getTableRequestId() > 0;
	}

	public static boolean isGyftrCard(boolean gyftrActive, String cardType) {
		return gyftrActive && isGyftrOrder(cardType);
	}

	public static boolean isGyftrOrder(String type) {
		return CashCardType.GYFTR.name().equals(type);
	}

	public static String getAssemblyRelay(int unitId, String source) {
		return "ALL";
	}

	public static boolean isCafeUnit(String orderSource) {
		return orderSource != null && orderSource.equals(UnitCategory.CAFE.name());
	}
	
	public static void calculateOrderPrepTime(Order order, MasterDataCache cache) {
		BigDecimal gnt = BigDecimal.ZERO, hot = BigDecimal.ZERO, cold = BigDecimal.ZERO, food = BigDecimal.ZERO,
				others = BigDecimal.ZERO;
		for (OrderItem orderItem : order.getOrders()) {
			Product product = cache.getProduct(orderItem.getProductId());
			if (Objects.nonNull(product) && (orderItem.getQuantity() > 0)) {
				if(Objects.isNull(product.getPrepTime())){
					product.setPrepTime(BigDecimal.ZERO);
				}
				orderItem.setPrepTime(
						product.getPrepTime().multiply(BigDecimal.valueOf(Math.sqrt(orderItem.getQuantity()))));
				switch (AppUtils.getTypeForProductType(product.getType(), product.getBrandId())) {
				case HOT:
					hot = hot.add(orderItem.getPrepTime());
					break;
				case COLD:
					cold = cold.add(orderItem.getPrepTime());
					break;
				case FOOD:
					food = food.add(orderItem.getPrepTime());
					break;
				case GNT:
					gnt = gnt.add(orderItem.getPrepTime());
					break;
				case OTHERS:
					others = others.add(orderItem.getPrepTime());
					break;
				}
			}
		}
		BigDecimal max = hot.max(cold).max(food).max(gnt).max(others);
		order.setPrepTime(max.compareTo(BigDecimal.valueOf(DEFAULT_ORDER_PREP_TIME)) != 1 ? BigDecimal.valueOf(DEFAULT_ORDER_PREP_TIME) : max);
	}
}
