/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 * OrderInfo generated by hbm2java
 */
@Entity
@Table(name = "FEEDBACK_FIELD")
public class Feedback<PERSON>ield implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 8381213803405685376L;
	
	private Integer feedbackFieldId;
	private String feedbackSourceFieldId;
	private FeedbackForm feedbackForm;
	private String fieldType;
	private String fieldTitle;

	public FeedbackField() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "FEEDBACK_FIELD_ID", unique = true, nullable = false)
	public Integer getFeedbackFieldId() {
		return this.feedbackFieldId;
	}

	public void setFeedbackFieldId(Integer feedbackFieldId) {
		this.feedbackFieldId = feedbackFieldId;
	}

	@Column(name = "FEEDBACK_SOURCE_FIELD_ID", nullable = false)
	public String getFeedbackSourceFieldId() {
		return feedbackSourceFieldId;
	}

	public void setFeedbackSourceFieldId(String feedbackSourceFieldId) {
		this.feedbackSourceFieldId = feedbackSourceFieldId;
	}

	@Column(name = "FIELD_TYPE", nullable = false, length = 20)
	public String getFieldType() {
		return fieldType;
	}

	
	public void setFieldType(String fieldType) {
		this.fieldType = fieldType;
	}
	
	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "FORM_ID", nullable = false)
	public FeedbackForm getFeedbackForm() {
		return feedbackForm;
	}

	public void setFeedbackForm(FeedbackForm feedbackForm) {
		this.feedbackForm = feedbackForm;
	}

	@Column(name = "FIELD_TITLE", nullable = false, length = 500)
	public String getFieldTitle() {
		return fieldTitle;
	}

	public void setFieldTitle(String fieldTitle) {
		this.fieldTitle = fieldTitle;
	}

}