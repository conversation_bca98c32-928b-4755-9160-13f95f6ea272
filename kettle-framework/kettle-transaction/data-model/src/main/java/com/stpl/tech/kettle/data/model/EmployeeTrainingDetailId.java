/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

// Generated 14 Jul, 2015 1:35:13 AM by Hibernate Tools 4.0.0

import javax.persistence.Column;
import javax.persistence.Embeddable;
import java.util.Date;

/**
 * EmployeeTrainingDetailId generated by hbm2java
 */
@SuppressWarnings("serial")
@Embeddable
public class EmployeeTrainingDetailId implements java.io.Serializable {

	private int empId;
	private int trainingId;
	private Date outTmstmp;

	public EmployeeTrainingDetailId() {
	}

	public EmployeeTrainingDetailId(int empId, int trainingId, Date outTmstmp) {
		this.empId = empId;
		this.trainingId = trainingId;
		this.outTmstmp = outTmstmp;
	}

	@Column(name = "EMP_ID", nullable = false)
	public int getEmpId() {
		return this.empId;
	}

	public void setEmpId(int empId) {
		this.empId = empId;
	}

	@Column(name = "TRAINING_ID", nullable = false)
	public int getTrainingId() {
		return this.trainingId;
	}

	public void setTrainingId(int trainingId) {
		this.trainingId = trainingId;
	}

	@Column(name = "OUT_TMSTMP", nullable = false, length = 19)
	public Date getOutTmstmp() {
		return this.outTmstmp;
	}

	public void setOutTmstmp(Date outTmstmp) {
		this.outTmstmp = outTmstmp;
	}

	public boolean equals(Object other) {
		if ((this == other)) {
			return true;
		}
		if ((other == null)) {
			return false;
		}
		if (!(other instanceof EmployeeTrainingDetailId)) {
			return false;
		}
		EmployeeTrainingDetailId castOther = (EmployeeTrainingDetailId) other;

		return (this.getEmpId() == castOther.getEmpId()) && (this.getTrainingId() == castOther.getTrainingId())
				&& ((this.getOutTmstmp() == castOther.getOutTmstmp()) || (this.getOutTmstmp() != null
						&& castOther.getOutTmstmp() != null && this.getOutTmstmp().equals(castOther.getOutTmstmp())));
	}

	public int hashCode() {
		int result = 17;

		result = 37 * result + this.getEmpId();
		result = 37 * result + this.getTrainingId();
		result = 37 * result + (getOutTmstmp() == null ? 0 : this.getOutTmstmp().hashCode());
		return result;
	}

}
