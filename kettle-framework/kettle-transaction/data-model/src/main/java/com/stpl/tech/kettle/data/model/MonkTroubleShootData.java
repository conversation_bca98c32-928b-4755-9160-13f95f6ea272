/*
 * Created By Shanmukh
 */

package com.stpl.tech.kettle.data.model;


import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Getter
@Setter
@Table(name = "MONK_TROUBLE_SHOOT_DATA")
public class MonkTroubleShootData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "MONK_TROUBLE_SHOOT_DATA_ID")
    private Integer monkTroubleShootDataId;
    @Column(name = "MONK_CALIBRATION_EVENT_ID")
    private Integer monkCalibrationEventId;
    @Column(name = "TROUBLE_SHOOT_CODE")
    private Integer troubleShootCode;
    @Column(name = "TROUBLE_SHOOT_CODE_MEANING")
    private String troubleShootCodeMeaning;

}
