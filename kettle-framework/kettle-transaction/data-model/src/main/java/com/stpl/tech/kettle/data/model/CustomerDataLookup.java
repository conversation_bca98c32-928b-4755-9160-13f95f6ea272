package com.stpl.tech.kettle.data.model;

import java.io.Serializable;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.IdClass;
import javax.persistence.Table;
import javax.persistence.UniqueConstraint;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Entity
@Table(name = "CUSTOMER_DATA_LOOKUP", uniqueConstraints = @UniqueConstraint(columnNames = { "ID", "TYPE" }))
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@IdClass(CustomerDataLookupId.class)
public class CustomerDataLookup implements Serializable {

	private static final long serialVersionUID = -5604143960533115297L;

	@Id
	@Column(name = "ID", nullable = false)
	private int id;

	@Column(name = "CONTACT_NUMBER")
	private String contactNumber;

	@Column(name = "EMAIL_ID")
	private String emailId;

	@Column(name = "CONTACT_NUMBER_DATA")
	private String contactNumberData;

	@Column(name = "EMAIL_ID_DATA")
	private String emailIdData;

	@Id
	@Column(name = "TYPE", nullable = false)
	private String type;

}
