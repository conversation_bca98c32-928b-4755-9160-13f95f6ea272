package com.stpl.tech.kettle.data.model;

import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * Model for logging addons associated with manual tasks performed by monks
 */
@Entity
@Table(name = "MONK_MANUAL_TASK_ADDON")
@Getter
@Setter
public class MonkManualTaskAddon {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "MONK_MANUAL_TASK_ADDON_ID")
    private Integer monkManualTaskAddonId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "MON<PERSON>_MANUAL_TASK_ID", nullable = false)
    private MonkManualTask monkManualTask;

    @Column(name = "ADDON_ID", nullable = false)
    private Integer addonId;

    @Column(name = "PRODUCT_SOURCE_SYSTEM", length = 15)
    private String productSourceSystem;

    @Column(name = "DIMENSION", length = 15)
    private String dimension;

    @Column(name = "QUANTITY", precision = 16, scale = 6)
    private BigDecimal quantity;

    @Column(name = "ADDON_NAME", length = 55)
    private String addonName;

    @Column(name = "ADDON_TYPE", length = 15)
    private String addonType;

    @Column(name = "UNIT_OF_MEASURE", length = 10)
    private String unitOfMeasure;

    @Column(name = "DEFAULT_SETTING", length = 1, nullable = false)
    private String defaultSetting = "N";

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof MonkManualTaskAddon)) return false;
        MonkManualTaskAddon that = (MonkManualTaskAddon) o;
        
        // Compare business key fields that uniquely identify an addon
        return Objects.equals(getMonkManualTaskAddonId(), that.getMonkManualTaskAddonId()) &&
               Objects.equals(getAddonId(), that.getAddonId());
    }

    @Override
    public int hashCode() {
        // Use the same fields as in equals() for consistency
        return Objects.hash(
            getMonkManualTaskAddonId(),
            getAddonId()
        );
    }
} 