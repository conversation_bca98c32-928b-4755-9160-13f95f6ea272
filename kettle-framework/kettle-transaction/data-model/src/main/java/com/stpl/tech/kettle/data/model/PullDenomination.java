/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

import java.io.Serializable;
import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

@SuppressWarnings("serial")
@Entity
@Table(name = "PULL_DENOMINATION")
public class PullDenomination implements Serializable {

	private Integer id;

	private PullDetail pullDetail;

	private int denominationId;

	private Integer packetCount;

	private Integer looseCurrencyCount;

	private BigDecimal totalAmount;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "PULL_DENOMINATION_ID", unique = true, nullable = false)
	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "PULL_DETAIL", nullable = false)
	public PullDetail getPullDetail() {
		return pullDetail;
	}

	public void setPullDetail(PullDetail pullDetail) {
		this.pullDetail = pullDetail;
	}

	@Column(name = "DENOMINATION", nullable = false)
	public int getDenominationId() {
		return denominationId;
	}

	public void setDenominationId(int denomination) {
		this.denominationId = denomination;
	}

	@Column(name = "PACKET_COUNT", nullable = true)
	public Integer getPacketCount() {
		return packetCount;
	}

	public void setPacketCount(Integer packetCount) {
		this.packetCount = packetCount;
	}

	@Column(name = "LOOSE_CURRENCY_COUNT", nullable = true)
	public Integer getLooseCurrencyCount() {
		return looseCurrencyCount;
	}

	public void setLooseCurrencyCount(Integer looseCurrencyCount) {
		this.looseCurrencyCount = looseCurrencyCount;
	}

	@Column(name = "TOTAL_AMOUNT", nullable = false)
	public BigDecimal getTotalAmount() {
		return totalAmount;
	}

	public void setTotalAmount(BigDecimal totalAmount) {
		this.totalAmount = totalAmount;
	}
}
