package com.stpl.tech.kettle.data.model;


import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "ORDER_INVOICE_DETAIL")
public class OrderInvoiceDetail {

    private Integer orderInvoiceDetailId;
    private String orderId;
    private String gstIn;
    private String companyName;
    private String companyAddress;
    private String generatedBy;
    private Date generationTime;
    private String stateCode;
    private String taxType;

    public OrderInvoiceDetail(){

    }

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "ORDER_INVOICE_DETAIL_ID", unique = true, nullable = false)
    public Integer getOrderInvoiceDetailId() {
        return orderInvoiceDetailId;
    }

    public void setOrderInvoiceDetailId(Integer orderInvoiceDetailId) {
        this.orderInvoiceDetailId = orderInvoiceDetailId;
    }

    @Column(name = "ORDER_ID", unique = true, nullable = false, length = 100)
    public String getOrderId() {
        return orderId;
    }

    public void setOrderId(String orderId) {
        this.orderId = orderId;
    }
    @Column(name = "GST_IN", unique = true, nullable = false, length = 50)
    public String getGstIn() {
        return gstIn;
    }

    public void setGstIn(String gst) {
        this.gstIn = gst;
    }

    @Column(name = "COMPANY_NAME", unique = true, nullable = false, length = 255)
    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    @Column(name = "COMPANY_ADDRESS", unique = true, nullable = false, length = 255)
    public String getCompanyAddress() {
        return companyAddress;
    }

    public void setCompanyAddress(String companyAddress) {
        this.companyAddress = companyAddress;
    }

    @Column(name = "GENERATED_BY", unique = true, nullable = false, length = 50)
    public String getGeneratedBy() {
        return generatedBy;
    }

    public void setGeneratedBy(String generatedBy) {
        this.generatedBy = generatedBy;
    }

    @Temporal(TemporalType.TIMESTAMP)
    @Column(name="GENERATION_TIME", nullable = false)
    public Date getGenerationTime() {
        return generationTime;
    }

    public void setGenerationTime(Date generationTime) {
        this.generationTime = generationTime;
    }

    @Column(name = "STATE_CODE", nullable = false)
    public String getStateCode() {
        return stateCode;
    }

    public void setStateCode(String stateCode) {
        this.stateCode = stateCode;
    }

    @Column(name = "TAX_TYPE", nullable = false)
    public String getTaxType() {
        return taxType;
    }

    public void setTaxType(String taxType) {
        this.taxType = taxType;
    }

}
