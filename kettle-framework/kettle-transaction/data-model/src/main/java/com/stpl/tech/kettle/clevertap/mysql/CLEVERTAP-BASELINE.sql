CREATE TABLE KETTLE_DEV.CLEVERTAP_PROFILE_PUSH_TRACK(
    ID INT PRIMARY KEY AUTO_INCREMENT,
    CUSTOMER_ID INT NOT NULL,
    STATUS VARCHAR(10) NOT NULL,
    UPLOADED_IN_BULK VARCHAR(1) NOT NULL,
    UPDATED_AT TIMESTAMP NOT NULL
);

CREATE TABLE KETTLE_DEV.CLEVERTAP_EVENT_PUSH_TRACK(
    ID INT PRIMARY KEY AUTO_INCREMENT,
    EVENT_NAME VARCHAR(100) NOT NULL,
    ORDER_ID INT NOT NULL,
    STATUS VARCHAR(10) NOT NULL,
    UPLOADED_IN_BULK VARCHAR(1) NOT NULL,
    UPDATED_AT TIMESTAMP NOT NULL
);



ALTER TABLE `KETTLE_DEV`.`CUSTOMER_INFO`
ADD COLUMN `APP_ID` VARCHAR(55) NULL AFTER `ANNIVERSARY`;

ALTER TABLE `KETTLE_DEV`.`CUSTOMER_INFO`
CHANGE COLUMN `APP_ID` `CUSTOMER_APP_ID` VARCHAR(55) NULL DEFAULT NULL ;

ALTER TABLE `KETTLE_DEV`.`CLEVERTAP_EVENT_PUSH_TRACK`
CHANGE COLUMN `UPLOADED_IN_BULK` `UPDATE_TYPE` VARCHAR(15) NOT NULL ;

ALTER TABLE `KETTLE_DEV`.`CLEVERTAP_PROFILE_PUSH_TRACK`
CHANGE COLUMN `UPLOADED_IN_BULK` `UPDATE_TYPE` VARCHAR(15) NOT NULL ;

DELIMITER $$
CREATE  PROCEDURE KETTLE_DEV.`CLEVERTAP_PROFILE_ATTRIBUTES`(IN IN_CUSTOMER_ID INTEGER)
BEGIN

SELECT
    (SELECT CASE WHEN A.OFFER_APPLY_COUNT IS NULL THEN 1
                 WHEN A.OFFER_APPLY_COUNT < B.USAGE_LIMIT THEN 1 ELSE 0 END
     FROM KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL A
              INNER JOIN KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA B ON A.CAMPAIGN_ID = B.CAMPAIGN_ID
     WHERE A.COUPON_START_DATE <= DATE_ADD(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), INTERVAL 2 DAY) AND A.COUPON_END_DATE >= DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00'))
    AND B.CAMPAIGN_STRATEGY IN ('NBO')
    AND A.BRAND_ID = 1 AND  A.CUSTOMER_ID = C.CUSTOMER_ID)  ,
    (SELECT   CASE WHEN A.OFFER_APPLY_COUNT IS NULL THEN 1
    WHEN A.OFFER_APPLY_COUNT < B.USAGE_LIMIT THEN 1 ELSE 0 END
FROM KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL A
    LEFT JOIN KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA B ON A.CAMPAIGN_ID = B.CAMPAIGN_ID
WHERE A.COUPON_START_DATE <= DATE_ADD(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), INTERVAL 2 DAY) AND A.COUPON_END_DATE >= DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00'))
  AND B.CAMPAIGN_STRATEGY IN ('DELIVERY_NBO')
  AND A.BRAND_ID = 1 AND A.CUSTOMER_ID = C.CUSTOMER_ID ) dnboAvailableFlag ,
    (SELECT SUM(CASE WHEN CARD_STATUS IN ('ACTIVE') THEN CARD_PENDING_AMOUNT ELSE 0 END)
FROM KETTLE_DEV.CASH_CARD_DETAIL ccd WHERE ccd.CUSTOMER_ID = C.CUSTOMER_ID ) gcBalance ,
                                         ( SELECT  CASE WHEN (DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')) BETWEEN PLAN_START_DATE AND PLAN_END_DATE) AND (PLAN_STATUS IN ('ACTIVE')) THEN 1 ELSE 0 END
FROM KETTLE_DEV.SUBSCRIPTION_PLAN sp WHERE sp.CUSTOMER_ID = C.CUSTOMER_ID ORDER BY sp.SUBSCRIPTION_PLAN_ID DESC LIMIT 1 ) subscriptionActiveFlag ,
                                         ( SELECT SUM(CURRENT_AMOUNT) FROM KETTLE_DEV.CASH_DATA cd WHERE cd.CUSTOMER_ID = C.CUSTOMER_ID ) chyCashBalance ,
                                                                                                       (SELECT  ACQUIRED_POINTS FROM KETTLE_DEV.LOYALTY_SCORE ls WHERE ls.CUSTOMER_ID = C.CUSTOMER_ID ) loyaltyPointsBalance ,
                                                                                                                                                                     (SELECT SUM(POINTS_REDEEMED)*(-1/60) from KETTLE_DEV.ORDER_DETAIL od WHERE od.CUSTOMER_ID = C.CUSTOMER_ID ) loyaltyRedeemedCount,
                                         (select sum(SETTLED_AMOUNT) from KETTLE_DEV.ORDER_DETAIL od where od.CUSTOMER_ID =  IN_CUSTOMER_ID ) totalSpent

FROM KETTLE_DEV.CUSTOMER_INFO C WHERE CUSTOMER_ID= IN_CUSTOMER_ID;
END$$
DELIMITER ;

DELIMITER $$
CREATE PROCEDURE KETTLE_DEV.`CLEVERTAP_CHARGED_EVENT_ATTRIBUTES`(IN IN_OFFER_CODE varchar(255), IN IN_CUSTOMER_ID INTEGER, IN IN_ORDER_ID INTEGER, IN IN_ORDER_SOURCE VARCHAR(255))
BEGIN
SELECT
    (SELECT cou.OFFER_DETAIL_ID FROM KETTLE_MASTER_DEV.COUPON_DETAIL_DATA cou WHERE cou.COUPON_CODE = IN_OFFER_CODE ) offerDetailId ,
    ( SELECT CASE WHEN COUNT(*) <=1 THEN 'Y' ELSE 'N' END
      FROM KETTLE_DEV.ORDER_DETAIL  WHERE CUSTOMER_ID = IN_CUSTOMER_ID ORDER BY BILLING_SERVER_TIME DESC ) isNewCustomer ,
    (SELECT ORDER_ID FROM KETTLE_DEV.ORDER_DETAIL
     WHERE CUSTOMER_ID = IN_CUSTOMER_ID AND ORDER_ID <> IN_ORDER_ID ORDER BY BILLING_SERVER_TIME DESC LIMIT 1) previousOrderId ,
(SELECT ORDER_ID FROM KETTLE_DEV.ORDER_DETAIL
	WHERE CUSTOMER_ID = IN_CUSTOMER_ID AND ORDER_ID <> IN_ORDER_ID AND ORDER_SOURCE = IN_ORDER_SOURCE ORDER BY BILLING_SERVER_TIME DESC LIMIT 1 ) previousSourceOrderId,
( SELECT BILLING_SERVER_TIME FROM KETTLE_DEV.ORDER_DETAIL
	WHERE CUSTOMER_ID = IN_CUSTOMER_ID AND ORDER_ID <> IN_ORDER_ID ORDER BY BILLING_SERVER_TIME DESC LIMIT 1 ) previousBillingServerTime ,
(SELECT  TIMESTAMPDIFF(MINUTE,previousBillingServerTime,BILLING_SERVER_TIME) FROM KETTLE_DEV.ORDER_DETAIL
	WHERE CUSTOMER_ID = IN_CUSTOMER_ID AND ORDER_ID = IN_ORDER_ID ORDER BY BILLING_SERVER_TIME DESC LIMIT 1 ) previousOrderTimeDiff ,
(SELECT BILLING_SERVER_TIME FROM KETTLE_DEV.ORDER_DETAIL
	WHERE CUSTOMER_ID = IN_CUSTOMER_ID AND ORDER_ID <> IN_ORDER_ID AND ORDER_SOURCE = IN_ORDER_SOURCE ORDER BY BILLING_SERVER_TIME DESC LIMIT 1 ) previousSourceBillingServerTime ,
(SELECT TIMESTAMPDIFF(MINUTE,previousSourceBillingServerTime,BILLING_SERVER_TIME) FROM KETTLE_DEV.ORDER_DETAIL
	WHERE CUSTOMER_ID = IN_CUSTOMER_ID AND ORDER_ID = IN_ORDER_ID AND ORDER_SOURCE = IN_ORDER_SOURCE ORDER BY BILLING_SERVER_TIME DESC LIMIT 1 ) previousSourceOrderTimeDiff ,
(SELECT COUNT(*)  FROM KETTLE_DEV.ORDER_DETAIL WHERE CUSTOMER_ID = IN_CUSTOMER_ID ) overallOrderCount ,
(SELECT  COUNT(*) FROM KETTLE_DEV.ORDER_DETAIL WHERE CUSTOMER_ID = IN_CUSTOMER_ID AND ORDER_SOURCE = IN_ORDER_SOURCE ORDER BY BILLING_SERVER_TIME DESC ) orderSourceOrderCount ,(SELECT CASE
	 WHEN cdd.CAMPAIGN_STRATEGY IN ('NBO') THEN 'NBO'
	 WHEN cdd.CAMPAIGN_STRATEGY IN ('DELIVERY_NBO') THEN 'DELIVERY_NBO'
	 WHEN cdd.CAMPAIGN_STRATEGY IN ('GENERAL') THEN 'GENERAL'
	 ELSE 'OTHERS' END AS OFFER_CLASS FROM KETTLE_DEV.ORDER_DETAIL od LEFT JOIN
     KETTLE_DEV.CUSTOMER_CAMPIAGN_OFFER_DETAIL ccod ON ccod.COUPON_CODE = od.OFFER_CODE
     LEFT JOIN
     KETTLE_MASTER_DEV.CAMPAIGN_DETAIL_DATA
     cdd ON cdd.CAMPAIGN_ID = ccod.CAMPAIGN_ID WHERE od.ORDER_ID = IN_ORDER_ID  ) offerClass;
END$$
DELIMITER ;

DELIMITER $$
CREATE  PROCEDURE KETTLE_DEV.`CLEVERTAP_SUBSCRIPTION_EVENT_ATTRIBUTES`(IN IN_CUSTOMER_ID INTEGER, IN IN_ORDER_ID INTEGER)
BEGIN
SELECT ( SELECT CASE WHEN COUNT(*) <=1 THEN 'Y' ELSE 'N' END
         FROM KETTLE_DEV.ORDER_DETAIL  WHERE CUSTOMER_ID = IN_CUSTOMER_ID ORDER BY BILLING_SERVER_TIME DESC )
                                                                              isNewCustomer, spe.SUBSCRIPTION_PLAN_CODE AS planCode,
       UNIX_TIMESTAMP(DATE_ADD(spe.PLAN_START_DATE, INTERVAL -330 MINUTE)) AS planStartDate,
       UNIX_TIMESTAMP(DATE_ADD(spe.PLAN_END_DATE, INTERVAL -330 MINUTE)) AS planEndDate,
       spe.EVENT_TYPE AS eventType FROM
    KETTLE_DEV.ORDER_DETAIL  od inner join KETTLE_DEV.SUBSCRIPTION_PLAN_EVENT spe
                                           on spe.ORDER_ID = od.ORDER_ID AND od.ORDER_ID = IN_ORDER_ID ;

END$$
DELIMITER ;
