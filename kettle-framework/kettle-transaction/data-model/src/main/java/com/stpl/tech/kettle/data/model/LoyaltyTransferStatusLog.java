package com.stpl.tech.kettle.data.model;


import javax.persistence.*;
import java.io.Serializable;
import java.util.Date;

@SuppressWarnings("serial")
@Entity
@Table(name = "LOYALTY_TRANSFER_STATUS_LOG")
public class LoyaltyTransferStatusLog implements java.io.Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "LOYALTY_TRANSFER_STATUS_ID", unique = true, nullable = false)
    private Integer loyaltyTransferStatusId;
    @Column(name = "EVENT_ID")
    private Integer eventId;
    @Column(name = "FROM_STATUS", nullable = true)
    private String fromStatus;
    @Column(name = "TO_STATUS")
    private String toStatus;
    @Temporal(TemporalType.TIMESTAMP)
    @Column(name = "STATUS_UPDATE_TIME")
    private Date statusUpdateTime;
    @Column(name = "COMMENT")
    private String comment;

    public Integer getLoyaltyTransferStatusId() {
        return loyaltyTransferStatusId;
    }

    public void setLoyaltyTransferStatusId(Integer loyaltyTransferStatusId) {
        this.loyaltyTransferStatusId = loyaltyTransferStatusId;
    }

    public Integer getEventId() {
        return eventId;
    }

    public void setEventId(Integer eventId) {
        this.eventId = eventId;
    }

    public String getFromStatus() {
        return fromStatus;
    }

    public void setFromStatus(String fromStatus) {
        this.fromStatus = fromStatus;
    }

    public String getToStatus() {
        return toStatus;
    }

    public void setToStatus(String toStatus) {
        this.toStatus = toStatus;
    }

    public Date getStatusUpdateTime() {
        return statusUpdateTime;
    }

    public void setStatusUpdateTime(Date statusUpdateTime) {
        this.statusUpdateTime = statusUpdateTime;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }
}
