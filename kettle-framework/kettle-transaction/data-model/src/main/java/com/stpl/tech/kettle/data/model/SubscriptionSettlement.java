/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;
// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 * OrderSettlement generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "SUBSCRIPTION_SETTLEMENT"

)
public class SubscriptionSettlement implements java.io.Serializable {

	private Integer subscriptionSettlementId;
	private SubscriptionDetail subscriptionDetail;
	private int paymentModeId;
	private BigDecimal amountPaid;
	private BigDecimal roundOffAmount;

	public SubscriptionSettlement() {
	}

	public SubscriptionSettlement(SubscriptionDetail orderDetail, int paymentModeId) {
		this.subscriptionDetail = orderDetail;
		this.paymentModeId = paymentModeId;
	}

	public SubscriptionSettlement(SubscriptionDetail orderDetail, int paymentModeId, BigDecimal amountPaid,
			BigDecimal roundOffAmount) {
		this.subscriptionDetail = orderDetail;
		this.paymentModeId = paymentModeId;
		this.amountPaid = amountPaid;
		this.roundOffAmount = roundOffAmount;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)

	@Column(name = "SUBSCRIPTION_SETTLEMENT_ID", unique = true, nullable = false)
	public Integer getSubscriptionSettlementId() {
		return this.subscriptionSettlementId;
	}

	public void setSubscriptionSettlementId(Integer settlementId) {
		this.subscriptionSettlementId = settlementId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "SUBSCRIPTION_ID", nullable = false)
	public SubscriptionDetail getSubscriptionDetail() {
		return this.subscriptionDetail;
	}

	public void setSubscriptionDetail(SubscriptionDetail orderDetail) {
		this.subscriptionDetail = orderDetail;
	}

	@Column(name = "PAYMENT_MODE_ID", nullable = false)
	public int getPaymentModeId() {
		return this.paymentModeId;
	}

	public void setPaymentModeId(int paymentModeId) {
		this.paymentModeId = paymentModeId;
	}

	@Column(name = "AMOUNT_PAID", precision = 10)
	public BigDecimal getAmountPaid() {
		return this.amountPaid;
	}

	public void setAmountPaid(BigDecimal amountPaid) {
		this.amountPaid = amountPaid;
	}

	@Column(name = "ROUND_OFF_AMOUNT", precision = 10)
	public BigDecimal getRoundOffAmount() {
		return this.roundOffAmount;
	}

	public void setRoundOffAmount(BigDecimal roundOffAmount) {
		this.roundOffAmount = roundOffAmount;
	}

}
