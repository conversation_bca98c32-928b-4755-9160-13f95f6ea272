/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * OrderItem generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "ITEM_CONSUMPTION_ESTIMATE")
public class ItemConsumptionEstimate implements java.io.Serializable {

	private Integer itemConsumptionEstimateId;
	private int unitId;
	private String unitName;
	private int productId;
	private String productName;
	private String dimension;
	private int dayOfWeek;
	private BigDecimal quantity;
	private BigDecimal quantityDineIn;
	private BigDecimal quantityDelivery;
	private BigDecimal quantityTakeaway;
	private String itemStatus;
	private Date creationTime;

	public ItemConsumptionEstimate() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "ITEM_CONSUMPTION_ESTIMATE_ID", unique = true, nullable = false)
	public Integer getItemConsumptionEstimateId() {
		return this.itemConsumptionEstimateId;
	}

	public void setItemConsumptionEstimateId(Integer orderItemId) {
		this.itemConsumptionEstimateId = orderItemId;
	}

	@Column(name = "PRODUCT_ID", nullable = false)
	public int getProductId() {
		return this.productId;
	}

	public void setProductId(int productId) {
		this.productId = productId;
	}

	@Column(name = "PRODUCT_NAME", nullable = false)
	public String getProductName() {
		return this.productName;
	}

	public void setProductName(String productName) {
		this.productName = productName;
	}

	@Column(name = "UNIT_ID", nullable = false)
	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	@Column(name = "UNIT_NAME", nullable = false)
	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}

	@Column(name = "DIMENSION", nullable = false)
	public String getDimension() {
		return dimension;
	}

	public void setDimension(String dimension) {
		this.dimension = dimension;
	}

	@Column(name = "DAY_OF_WEEK", nullable = false)
	public int getDayOfWeek() {
		return dayOfWeek;
	}

	public void setDayOfWeek(int dayOfWeek) {
		this.dayOfWeek = dayOfWeek;
	}

	@Column(name = "TOTAL_QUANTITY", nullable = true)
	public BigDecimal getQuantity() {
		return quantity;
	}

	public void setQuantity(BigDecimal estimatedQuantity) {
		this.quantity = estimatedQuantity;
	}

	@Column(name = "QUANTITY_DINE_IN", nullable = true)
	public BigDecimal getQuantityDineIn() {
		return quantityDineIn;
	}

	public void setQuantityDineIn(BigDecimal percentageDineIn) {
		this.quantityDineIn = percentageDineIn;
	}

	@Column(name = "QUANTITY_DELIVERY", nullable = true)
	public BigDecimal getQuantityDelivery() {
		return quantityDelivery;
	}

	public void setQuantityDelivery(BigDecimal percentageDelivery) {
		this.quantityDelivery = percentageDelivery;
	}

	@Column(name = "QUANTITY_TAKEAWAY", nullable = true)
	public BigDecimal getQuantityTakeaway() {
		return quantityTakeaway;
	}

	public void setQuantityTakeaway(BigDecimal percentageTakeaway) {
		this.quantityTakeaway = percentageTakeaway;
	}

	@Column(name = "ITEM_STATUS", nullable = false)
	public String getItemStatus() {
		return itemStatus;
	}

	public void setItemStatus(String itemStatus) {
		this.itemStatus = itemStatus;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "CREATION_TIME", nullable = false, length = 19)
	public Date getCreationTime() {
		return creationTime;
	}

	public void setCreationTime(Date creationTime) {
		this.creationTime = creationTime;
	}

}
