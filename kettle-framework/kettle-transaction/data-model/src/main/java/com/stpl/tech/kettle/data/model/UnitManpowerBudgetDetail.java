/*
 * SUNSHINE TEAHOUSE @ExcelField private LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse @ExcelField private Limited
 * All Rights Reservethis.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse @ExcelField private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse @ExcelField private Limited
 * and its suppliers, and are @ExcelField private by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse @ExcelField private Limitethis.
 */
package com.stpl.tech.kettle.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

import com.stpl.tech.util.excelparser.annotations.ExcelField;
import com.stpl.tech.util.excelparser.annotations.ExcelObject;
import com.stpl.tech.util.excelparser.annotations.ExcelSheet;
import com.stpl.tech.util.excelparser.annotations.ParseType;

@ExcelSheet(value = "Unit Manpower Budget Detail")
@ExcelObject(parseType = ParseType.ROW, start = 1, end = 0)
@Entity
@Table(name = "UNIT_MANPOWER_BUDGET_DETAIL")
public class UnitManpowerBudgetDetail {

	private int manpowerBudgetDetailId;
	@ExcelField
	private int unitId;
	@ExcelField
	private String unitName;
	@ExcelField
	private int year;
	@ExcelField
	private int month;
	@ExcelField
	private String status;

	@ExcelField
	private BigDecimal oldSalary;
//	@ExcelField
//	private BigDecimal oldSalaryIncentive;
	@ExcelField(headerName = "OLD_PERFORMANCE_INCENTIVE")
	private BigDecimal oldSalesIncentive;

	@ExcelField
	private BigDecimal oldByodCharges;
	@ExcelField
	private BigDecimal oldCarLease;
	@ExcelField
	private BigDecimal oldDriverSalary;
	@ExcelField
	private BigDecimal oldGratuity;
	@ExcelField
	private BigDecimal oldInsurnaceAccidental;
	@ExcelField
	private BigDecimal oldInsurnaceMedical;
	@ExcelField
	private BigDecimal oldSupportsOpsTurnover;
	@ExcelField
	private BigDecimal oldEmployeeFacilitationExpenses;
	@ExcelField
	private BigDecimal oldTelephoneSR;
	@ExcelField
	private BigDecimal oldVehicleRunningAndMaintSR;
	@ExcelField
	private BigDecimal oldEmployeeStockOptionExpense;
	@ExcelField
	private BigDecimal oldEmployerContributionLWF;
	@ExcelField
	private BigDecimal oldEsicEmployerCont;
	@ExcelField
	private BigDecimal oldLeaveTravelReimbursement;
	@ExcelField
	private BigDecimal oldPfAdministrationCharges;
	@ExcelField
	private BigDecimal oldPfEmployerCont;
	@ExcelField
	private BigDecimal oldQuarterlyIncentive;
	@ExcelField
	private BigDecimal oldPreOpeningSalary;

	@ExcelField
	private BigDecimal oldBonusAttendance;
	@ExcelField
	private BigDecimal oldBonusJoining;
	@ExcelField
	private BigDecimal oldBonusReferral;
	@ExcelField
	private BigDecimal oldBonusHoliday;
	@ExcelField
	private BigDecimal oldBonusOthers;
	@ExcelField
	private BigDecimal oldAllowanceRemoteLocation;
	@ExcelField
	private BigDecimal oldAllowanceCityCompensatory;
	@ExcelField
	private BigDecimal oldAllowanceMonk;
	@ExcelField
	private BigDecimal oldAllowanceEmployeeBenefit;
	@ExcelField
	private BigDecimal oldAllowanceOthers;
	@ExcelField
	private BigDecimal oldNoticePeriodBuyout;
	@ExcelField
	private BigDecimal oldNoticePeriodDeduction;
	@ExcelField
	private BigDecimal oldRelocationExpenses;
	@ExcelField
	private BigDecimal oldStipendExpenses;
	@ExcelField
	private BigDecimal oldTrainingCostRecovery;
	@ExcelField
	private BigDecimal oldSeverancePay;
	@ExcelField
	private BigDecimal oldLabourCharges;

	@ExcelField
	private BigDecimal salary;
//	@ExcelField
//	private BigDecimal salaryIncentive;
	@ExcelField(headerName = "PERFORMANCE_INCENTIVE")
	private BigDecimal salesIncentive;

	@ExcelField
	private BigDecimal byodCharges;
	@ExcelField
	private BigDecimal carLease;
	@ExcelField
	private BigDecimal driverSalary;
	@ExcelField
	private BigDecimal gratuity;
	@ExcelField
	private BigDecimal insurnaceAccidental;
	@ExcelField
	private BigDecimal insurnaceMedical;
	@ExcelField
	private BigDecimal supportsOpsTurnover;
	@ExcelField
	private BigDecimal employeeFacilitationExpenses;
	@ExcelField
	private BigDecimal telephoneSR;
	@ExcelField
	private BigDecimal vehicleRunningAndMaintSR;
	@ExcelField
	private BigDecimal employeeStockOptionExpense;
	@ExcelField
	private BigDecimal employerContributionLWF;
	@ExcelField
	private BigDecimal esicEmployerCont;
	@ExcelField
	private BigDecimal leaveTravelReimbursement;
	@ExcelField
	private BigDecimal pfAdministrationCharges;
	@ExcelField
	private BigDecimal pfEmployerCont;
	@ExcelField
	private BigDecimal quarterlyIncentive;
	@ExcelField
	private BigDecimal preOpeningSalary;

	@ExcelField
	private BigDecimal bonusAttendance;
	@ExcelField
	private BigDecimal bonusJoining;
	@ExcelField
	private BigDecimal bonusReferral;
	@ExcelField
	private BigDecimal bonusHoliday;
	@ExcelField
	private BigDecimal bonusOthers;
	@ExcelField
	private BigDecimal allowanceRemoteLocation;
	@ExcelField
	private BigDecimal allowanceCityCompensatory;
	@ExcelField
	private BigDecimal allowanceMonk;
	@ExcelField
	private BigDecimal allowanceEmployeeBenefit;
	@ExcelField
	private BigDecimal allowanceOthers;
	@ExcelField
	private BigDecimal noticePeriodBuyout;
	@ExcelField
	private BigDecimal noticePeriodDeduction;
	@ExcelField
	private BigDecimal relocationExpenses;
	@ExcelField
	private BigDecimal stipendExpenses;
	@ExcelField
	private BigDecimal trainingCostRecovery;
	@ExcelField
	private BigDecimal severancePay;
	@ExcelField
	private BigDecimal labourCharges;

	private Integer updatedBy;

	private Date updateTime;

	public UnitManpowerBudgetDetail() {

	}

	public UnitManpowerBudgetDetail(int unitId, String unitName, int year, int month, String status) {
		this.unitId = unitId;
		this.unitName = unitName;
		this.year = year;
		this.month = month;
		this.status = status;
	}


	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "UNIT_MANPOWER_BUDGET_DETAIL_ID", unique = true, nullable = false)
	public int getManpowerBudgetDetailId() {
		return manpowerBudgetDetailId;
	}

	public void setManpowerBudgetDetailId(int unitExpenseDetailId) {
		this.manpowerBudgetDetailId = unitExpenseDetailId;
	}

	@Column(name = "UNIT_ID", nullable = true)
	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	@Column(name = "UNIT_NAME", nullable = true)
	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}

	@Column(name = "CALCULATION_YEAR", nullable = true)
	public int getYear() {
		return year;
	}

	public void setYear(int year) {
		this.year = year;
	}

	@Column(name = "CALCULATION_MONTH", nullable = true)
	public int getMonth() {
		return month;
	}

	public void setMonth(int month) {
		this.month = month;
	}

	@Column(name = "BUDGET_STATUS", nullable = true)
	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	@Column(name = "OLD_SALARY", nullable = true)
	public BigDecimal getOldSalary() {
		return oldSalary;
	}

	public void setOldSalary(BigDecimal oldSalary) {
		this.oldSalary = oldSalary;
	}

//	@Column(name = "OLD_SALARY_INCENTIVE", nullable = true)
//	public BigDecimal getOldSalaryIncentive() {
//		return oldSalaryIncentive;
//	}
//
//	public void setOldSalaryIncentive(BigDecimal oldSalaryIncentive) {
//		this.oldSalaryIncentive = oldSalaryIncentive;
//	}

	@Column(name = "OLD_PERFORMANCE_INCENTIVE", nullable = true)
	public BigDecimal getOldSalesIncentive() {
		return oldSalesIncentive;
	}

	public void setOldSalesIncentive(BigDecimal oldSalesIncentive) {
		this.oldSalesIncentive = oldSalesIncentive;
	}

	@Column(name = "OLD_BYOD_CHARGES", nullable = true)
	public BigDecimal getOldByodCharges() {
		return oldByodCharges;
	}

	public void setOldByodCharges(BigDecimal oldByodCharges) {
		this.oldByodCharges = oldByodCharges;
	}

	@Column(name = "OLD_CAR_LEASE_SR", nullable = true)
	public BigDecimal getOldCarLease() {
		return oldCarLease;
	}

	public void setOldCarLease(BigDecimal oldCarLease) {
		this.oldCarLease = oldCarLease;
	}

	@Column(name = "OLD_DRIVER_SALARY_SR", nullable = true)
	public BigDecimal getOldDriverSalary() {
		return oldDriverSalary;
	}

	public void setOldDriverSalary(BigDecimal oldDriverSalary) {
		this.oldDriverSalary = oldDriverSalary;
	}

	@Column(name = "OLD_GRATUITY", nullable = true)
	public BigDecimal getOldGratuity() {
		return oldGratuity;
	}

	public void setOldGratuity(BigDecimal oldGratuity) {
		this.oldGratuity = oldGratuity;
	}

	@Column(name = "OLD_INSUARANCE_ACCIDENTAL", nullable = true)
	public BigDecimal getOldInsurnaceAccidental() {
		return oldInsurnaceAccidental;
	}

	public void setOldInsurnaceAccidental(BigDecimal oldInsurnaceAccidental) {
		this.oldInsurnaceAccidental = oldInsurnaceAccidental;
	}

	@Column(name = "OLD_INSUARANCE_MEDICAL", nullable = true)
	public BigDecimal getOldInsurnaceMedical() {
		return oldInsurnaceMedical;
	}

	public void setOldInsurnaceMedical(BigDecimal oldInsurnaceMedical) {
		this.oldInsurnaceMedical = oldInsurnaceMedical;
	}

	@Column(name = "OLD_SUPPORT_OPS_TURNOVER", nullable = true)
	public BigDecimal getOldSupportsOpsTurnover() {
		return oldSupportsOpsTurnover;
	}

	public void setOldSupportsOpsTurnover(BigDecimal oldSupportsOpsTurnover) {
		this.oldSupportsOpsTurnover = oldSupportsOpsTurnover;
	}

	@Column(name = "OLD_EMPLOYEE_FACILITATION_EXPENSES", nullable = true)
	public BigDecimal getOldEmployeeFacilitationExpenses() {
		return oldEmployeeFacilitationExpenses;
	}

	public void setOldEmployeeFacilitationExpenses(BigDecimal oldEmployeeFacilitationExpenses) {
		this.oldEmployeeFacilitationExpenses = oldEmployeeFacilitationExpenses;
	}

	@Column(name = "OLD_TELEPHONE_SR", nullable = true)
	public BigDecimal getOldTelephoneSR() {
		return oldTelephoneSR;
	}

	public void setOldTelephoneSR(BigDecimal oldTelephoneSR) {
		this.oldTelephoneSR = oldTelephoneSR;
	}

	@Column(name = "OLD_VEHICLE_RUNNING_AND_MAINT_SR", nullable = true)
	public BigDecimal getOldVehicleRunningAndMaintSR() {
		return oldVehicleRunningAndMaintSR;
	}

	public void setOldVehicleRunningAndMaintSR(BigDecimal oldVehicleRunningAndMaintSR) {
		this.oldVehicleRunningAndMaintSR = oldVehicleRunningAndMaintSR;
	}

	@Column(name = "OLD_EMPLOYEE_STOCK_OPTION_EXPENSE", nullable = true)
	public BigDecimal getOldEmployeeStockOptionExpense() {
		return oldEmployeeStockOptionExpense;
	}

	public void setOldEmployeeStockOptionExpense(BigDecimal oldEmployeeStockOptionExpense) {
		this.oldEmployeeStockOptionExpense = oldEmployeeStockOptionExpense;
	}

	@Column(name = "OLD_EMPLOYER_CONTRIBUTION_LWF", nullable = true)
	public BigDecimal getOldEmployerContributionLWF() {
		return oldEmployerContributionLWF;
	}

	public void setOldEmployerContributionLWF(BigDecimal oldEmployerContributionLWF) {
		this.oldEmployerContributionLWF = oldEmployerContributionLWF;
	}

	@Column(name = "OLD_ESIC_EMPLOYER_CONT", nullable = true)
	public BigDecimal getOldEsicEmployerCont() {
		return oldEsicEmployerCont;
	}

	public void setOldEsicEmployerCont(BigDecimal oldEsicEmployerCont) {
		this.oldEsicEmployerCont = oldEsicEmployerCont;
	}

	@Column(name = "OLD_LEAVE_TRAVEL_REIMBURSEMENT", nullable = true)
	public BigDecimal getOldLeaveTravelReimbursement() {
		return oldLeaveTravelReimbursement;
	}

	public void setOldLeaveTravelReimbursement(BigDecimal oldLeaveTravelReimbursement) {
		this.oldLeaveTravelReimbursement = oldLeaveTravelReimbursement;
	}

	@Column(name = "OLD_PF_ADMINISTRATION_CHARGES", nullable = true)
	public BigDecimal getOldPfAdministrationCharges() {
		return oldPfAdministrationCharges;
	}

	public void setOldPfAdministrationCharges(BigDecimal oldPfAdministrationCharges) {
		this.oldPfAdministrationCharges = oldPfAdministrationCharges;
	}

	@Column(name = "OLD_PF_EMPLOYER_CONT", nullable = true)
	public BigDecimal getOldPfEmployerCont() {
		return oldPfEmployerCont;
	}

	public void setOldPfEmployerCont(BigDecimal oldPfEmployerCont) {
		this.oldPfEmployerCont = oldPfEmployerCont;
	}

	@Column(name = "OLD_QUATERLY_INCENTIVE", nullable = true)
	public BigDecimal getOldQuarterlyIncentive() {
		return oldQuarterlyIncentive;
	}

	public void setOldQuarterlyIncentive(BigDecimal oldQuarterlyIncentive) {
		this.oldQuarterlyIncentive = oldQuarterlyIncentive;
	}

	@Column(name = "OLD_PRE_OPEINING_SALARY", nullable = true)
	public BigDecimal getOldPreOpeningSalary() {
		return oldPreOpeningSalary;
	}

	public void setOldPreOpeningSalary(BigDecimal oldPreOpeningSalary) {
		this.oldPreOpeningSalary = oldPreOpeningSalary;
	}

	@Column(name = "OLD_BONUS_ATTENDANCE", nullable = true)
	public BigDecimal getOldBonusAttendance() {
		return oldBonusAttendance;
	}

	public void setOldBonusAttendance(BigDecimal oldBonusAttendance) {
		this.oldBonusAttendance = oldBonusAttendance;
	}

	@Column(name = "OLD_BONUS_JOINING", nullable = true)
	public BigDecimal getOldBonusJoining() {
		return oldBonusJoining;
	}

	public void setOldBonusJoining(BigDecimal oldBonusJoining) {
		this.oldBonusJoining = oldBonusJoining;
	}

	@Column(name = "OLD_BONUS_REFERRAL", nullable = true)
	public BigDecimal getOldBonusReferral() {
		return oldBonusReferral;
	}

	public void setOldBonusReferral(BigDecimal oldBonusReferral) {
		this.oldBonusReferral = oldBonusReferral;
	}

	@Column(name = "OLD_BONUS_HOLIDAY", nullable = true)
	public BigDecimal getOldBonusHoliday() {
		return oldBonusHoliday;
	}

	public void setOldBonusHoliday(BigDecimal oldBonusHoliday) {
		this.oldBonusHoliday = oldBonusHoliday;
	}

	
	@Column(name = "OLD_BONUS_OTHERS", nullable = true)
	public BigDecimal getOldBonusOthers() {
		return oldBonusOthers;
	}

	public void setOldBonusOthers(BigDecimal oldBonusOthers) {
		this.oldBonusOthers = oldBonusOthers;
	}

	@Column(name = "OLD_ALLOWANCE_REMOTE_LOCATION", nullable = true)
	public BigDecimal getOldAllowanceRemoteLocation() {
		return oldAllowanceRemoteLocation;
	}

	public void setOldAllowanceRemoteLocation(BigDecimal oldAllowanceRemoteLocation) {
		this.oldAllowanceRemoteLocation = oldAllowanceRemoteLocation;
	}

	@Column(name = "OLD_ALLOWANCE_CITY_COMPENSATORY", nullable = true)
	public BigDecimal getOldAllowanceCityCompensatory() {
		return oldAllowanceCityCompensatory;
	}

	public void setOldAllowanceCityCompensatory(BigDecimal oldAllowanceCityCompensatory) {
		this.oldAllowanceCityCompensatory = oldAllowanceCityCompensatory;
	}

	@Column(name = "OLD_ALLOWANCE_MONK", nullable = true)
	public BigDecimal getOldAllowanceMonk() {
		return oldAllowanceMonk;
	}

	public void setOldAllowanceMonk(BigDecimal oldAllowanceMonk) {
		this.oldAllowanceMonk = oldAllowanceMonk;
	}
	
	@Column(name = "OLD_ALLOWANCE_EMPLOYEE_BENEFIT", nullable = true)
	public BigDecimal getOldAllowanceEmployeeBenefit() {
		return oldAllowanceEmployeeBenefit;
	}

	public void setOldAllowanceEmployeeBenefit(BigDecimal oldAllowanceEmployeeBenefit) {
		this.oldAllowanceEmployeeBenefit = oldAllowanceEmployeeBenefit;
	}

	@Column(name = "OLD_ALLOWANCE_OTHERS", nullable = true)
	public BigDecimal getOldAllowanceOthers() {
		return oldAllowanceOthers;
	}

	public void setOldAllowanceOthers(BigDecimal oldAllowanceOthers) {
		this.oldAllowanceOthers = oldAllowanceOthers;
	}

	@Column(name = "OLD_NOTICE_PERIOD_BUY_OUT", nullable = true)
	public BigDecimal getOldNoticePeriodBuyout() {
		return oldNoticePeriodBuyout;
	}

	public void setOldNoticePeriodBuyout(BigDecimal oldNoticePeriodBuyout) {
		this.oldNoticePeriodBuyout = oldNoticePeriodBuyout;
	}

	@Column(name = "OLD_NOTICE_PERIOD_DEDUCTION", nullable = true)
	public BigDecimal getOldNoticePeriodDeduction() {
		return oldNoticePeriodDeduction;
	}

	public void setOldNoticePeriodDeduction(BigDecimal oldNoticePeriodDeduction) {
		this.oldNoticePeriodDeduction = oldNoticePeriodDeduction;
	}

	@Column(name = "OLD_RELOCATION_EXPENSES", nullable = true)
	public BigDecimal getOldRelocationExpenses() {
		return oldRelocationExpenses;
	}

	public void setOldRelocationExpenses(BigDecimal oldRelocationExpenses) {
		this.oldRelocationExpenses = oldRelocationExpenses;
	}

	@Column(name = "OLD_STIPEND_EXPENSE", nullable = true)
	public BigDecimal getOldStipendExpenses() {
		return oldStipendExpenses;
	}

	public void setOldStipendExpenses(BigDecimal oldStipendExpenses) {
		this.oldStipendExpenses = oldStipendExpenses;
	}

	@Column(name = "OLD_TRAINING_COST_RECOVERY", nullable = true)
	public BigDecimal getOldTrainingCostRecovery() {
		return oldTrainingCostRecovery;
	}

	public void setOldTrainingCostRecovery(BigDecimal oldTrainingCostRecovery) {
		this.oldTrainingCostRecovery = oldTrainingCostRecovery;
	}

	@Column(name = "OLD_SEVERANCE_PAY", nullable = true)
	public BigDecimal getOldSeverancePay() {
		return oldSeverancePay;
	}

	public void setOldSeverancePay(BigDecimal oldSeverancePay) {
		this.oldSeverancePay = oldSeverancePay;
	}

	@Column(name = "OLD_LABOUR_CHARGES", nullable = true)
	public BigDecimal getOldLabourCharges() {
		return oldLabourCharges;
	}

	public void setOldLabourCharges(BigDecimal oldLabourCharges) {
		this.oldLabourCharges = oldLabourCharges;
	}

	@Column(name = "SALARY", nullable = true)
	public BigDecimal getSalary() {
		return salary;
	}

	public void setSalary(BigDecimal salary) {
		this.salary = salary;
	}

//	@Column(name = "SALARY_INCENTIVE", nullable = true)
//	public BigDecimal getSalaryIncentive() {
//		return salaryIncentive;
//	}
//
//	public void setSalaryIncentive(BigDecimal salaryIncentive) {
//		this.salaryIncentive = salaryIncentive;
//	}

	@Column(name = "PERFORMANCE_INCENTIVE", nullable = true)
	public BigDecimal getSalesIncentive() {
		return salesIncentive;
	}

	public void setSalesIncentive(BigDecimal salesIncentive) {
		this.salesIncentive = salesIncentive;
	}

	@Column(name = "BYOD_CHARGES", nullable = true)
	public BigDecimal getByodCharges() {
		return byodCharges;
	}

	public void setByodCharges(BigDecimal byodCharges) {
		this.byodCharges = byodCharges;
	}

	@Column(name = "CAR_LEASE_SR", nullable = true)
	public BigDecimal getCarLease() {
		return carLease;
	}

	public void setCarLease(BigDecimal carLease) {
		this.carLease = carLease;
	}

	@Column(name = "DRIVER_SALARY_SR", nullable = true)
	public BigDecimal getDriverSalary() {
		return driverSalary;
	}

	public void setDriverSalary(BigDecimal driverSalary) {
		this.driverSalary = driverSalary;
	}

	@Column(name = "GRATUITY", nullable = true)
	public BigDecimal getGratuity() {
		return gratuity;
	}

	public void setGratuity(BigDecimal gratuity) {
		this.gratuity = gratuity;
	}

	@Column(name = "INSUARANCE_ACCIDENTAL", nullable = true)
	public BigDecimal getInsurnaceAccidental() {
		return insurnaceAccidental;
	}

	public void setInsurnaceAccidental(BigDecimal insurnaceAccidental) {
		this.insurnaceAccidental = insurnaceAccidental;
	}

	@Column(name = "INSUARANCE_MEDICAL", nullable = true)
	public BigDecimal getInsurnaceMedical() {
		return insurnaceMedical;
	}

	public void setInsurnaceMedical(BigDecimal insurnaceMedical) {
		this.insurnaceMedical = insurnaceMedical;
	}

	@Column(name = "SUPPORT_OPS_TURNOVER", nullable = true)
	public BigDecimal getSupportsOpsTurnover() {
		return supportsOpsTurnover;
	}

	public void setSupportsOpsTurnover(BigDecimal supportsOpsTurnover) {
		this.supportsOpsTurnover = supportsOpsTurnover;
	}

	@Column(name = "EMPLOYEE_FACILITATION_EXPENSES", nullable = true)
	public BigDecimal getEmployeeFacilitationExpenses() {
		return employeeFacilitationExpenses;
	}

	public void setEmployeeFacilitationExpenses(BigDecimal employeeFacilitationExpenses) {
		this.employeeFacilitationExpenses = employeeFacilitationExpenses;
	}

	@Column(name = "TELEPHONE_SR", nullable = true)
	public BigDecimal getTelephoneSR() {
		return telephoneSR;
	}

	public void setTelephoneSR(BigDecimal telephoneSR) {
		this.telephoneSR = telephoneSR;
	}

	@Column(name = "VEHICLE_RUNNING_AND_MAINT_SR", nullable = true)
	public BigDecimal getVehicleRunningAndMaintSR() {
		return vehicleRunningAndMaintSR;
	}

	public void setVehicleRunningAndMaintSR(BigDecimal vehicleRunningAndMaintSR) {
		this.vehicleRunningAndMaintSR = vehicleRunningAndMaintSR;
	}

	@Column(name = "EMPLOYEE_STOCK_OPTION_EXPENSE", nullable = true)
	public BigDecimal getEmployeeStockOptionExpense() {
		return employeeStockOptionExpense;
	}

	public void setEmployeeStockOptionExpense(BigDecimal employeeStockOptionExpense) {
		this.employeeStockOptionExpense = employeeStockOptionExpense;
	}

	@Column(name = "EMPLOYER_CONTRIBUTION_LWF", nullable = true)
	public BigDecimal getEmployerContributionLWF() {
		return employerContributionLWF;
	}

	public void setEmployerContributionLWF(BigDecimal employerContributionLWF) {
		this.employerContributionLWF = employerContributionLWF;
	}

	@Column(name = "ESIC_EMPLOYER_CONT", nullable = true)
	public BigDecimal getEsicEmployerCont() {
		return esicEmployerCont;
	}

	public void setEsicEmployerCont(BigDecimal esicEmployerCont) {
		this.esicEmployerCont = esicEmployerCont;
	}

	@Column(name = "LEAVE_TRAVEL_REIMBURSEMENT", nullable = true)
	public BigDecimal getLeaveTravelReimbursement() {
		return leaveTravelReimbursement;
	}

	public void setLeaveTravelReimbursement(BigDecimal leaveTravelReimbursement) {
		this.leaveTravelReimbursement = leaveTravelReimbursement;
	}

	@Column(name = "PF_ADMINISTRATION_CHARGES", nullable = true)
	public BigDecimal getPfAdministrationCharges() {
		return pfAdministrationCharges;
	}

	public void setPfAdministrationCharges(BigDecimal pfAdministrationCharges) {
		this.pfAdministrationCharges = pfAdministrationCharges;
	}

	@Column(name = "PF_EMPLOYER_CONT", nullable = true)
	public BigDecimal getPfEmployerCont() {
		return pfEmployerCont;
	}

	public void setPfEmployerCont(BigDecimal pfEmployerCont) {
		this.pfEmployerCont = pfEmployerCont;
	}

	@Column(name = "QUATERLY_INCENTIVE", nullable = true)
	public BigDecimal getQuarterlyIncentive() {
		return quarterlyIncentive;
	}

	@Column(name = "PRE_OPEINING_SALARY", nullable = true)
	public BigDecimal getPreOpeningSalary() {
		return preOpeningSalary;
	}

	public void setPreOpeningSalary(BigDecimal preOpeningSalary) {
		this.preOpeningSalary = preOpeningSalary;
	}

	public void setQuarterlyIncentive(BigDecimal quarterlyIncentive) {
		this.quarterlyIncentive = quarterlyIncentive;
	}

	@Column(name = "BONUS_ATTENDANCE", nullable = true)
	public BigDecimal getBonusAttendance() {
		return bonusAttendance;
	}

	public void setBonusAttendance(BigDecimal bonusAttendance) {
		this.bonusAttendance = bonusAttendance;
	}

	@Column(name = "BONUS_JOINING", nullable = true)
	public BigDecimal getBonusJoining() {
		return bonusJoining;
	}

	public void setBonusJoining(BigDecimal bonusJoining) {
		this.bonusJoining = bonusJoining;
	}

	@Column(name = "BONUS_REFERRAL", nullable = true)
	public BigDecimal getBonusReferral() {
		return bonusReferral;
	}

	public void setBonusReferral(BigDecimal bonusReferral) {
		this.bonusReferral = bonusReferral;
	}

	@Column(name = "BONUS_HOLIDAY", nullable = true)
	public BigDecimal getBonusHoliday() {
		return bonusHoliday;
	}

	public void setBonusHoliday(BigDecimal bonusHoliday) {
		this.bonusHoliday = bonusHoliday;
	}

	@Column(name = "BONUS_OTHERS", nullable = true)
	public BigDecimal getBonusOthers() {
		return bonusOthers;
	}

	public void setBonusOthers(BigDecimal bonusOthers) {
		this.bonusOthers = bonusOthers;
	}

	@Column(name = "ALLOWANCE_REMOTE_LOCATION", nullable = true)
	public BigDecimal getAllowanceRemoteLocation() {
		return allowanceRemoteLocation;
	}

	public void setAllowanceRemoteLocation(BigDecimal allowanceRemoteLocation) {
		this.allowanceRemoteLocation = allowanceRemoteLocation;
	}

	@Column(name = "ALLOWANCE_CITY_COMPENSATORY", nullable = true)
	public BigDecimal getAllowanceCityCompensatory() {
		return allowanceCityCompensatory;
	}

	public void setAllowanceCityCompensatory(BigDecimal allowanceCityCompensatory) {
		this.allowanceCityCompensatory = allowanceCityCompensatory;
	}

	@Column(name = "ALLOWANCE_MONK", nullable = true)
	public BigDecimal getAllowanceMonk() {
		return allowanceMonk;
	}
	
	@Column(name = "ALLOWANCE_EMPLOYEE_BENEFIT", nullable = true)
	public BigDecimal getAllowanceEmployeeBenefit() {
		return allowanceEmployeeBenefit;
	}

	public void setAllowanceEmployeeBenefit(BigDecimal allowanceEmployeeBenefit) {
		this.allowanceEmployeeBenefit = allowanceEmployeeBenefit;
	}
	public void setAllowanceMonk(BigDecimal allowanceMonk) {
		this.allowanceMonk = allowanceMonk;
	}

	@Column(name = "ALLOWANCE_OTHERS", nullable = true)
	public BigDecimal getAllowanceOthers() {
		return allowanceOthers;
	}

	public void setAllowanceOthers(BigDecimal allowanceOthers) {
		this.allowanceOthers = allowanceOthers;
	}

	@Column(name = "NOTICE_PERIOD_BUY_OUT", nullable = true)
	public BigDecimal getNoticePeriodBuyout() {
		return noticePeriodBuyout;
	}

	public void setNoticePeriodBuyout(BigDecimal noticePeriodBuyout) {
		this.noticePeriodBuyout = noticePeriodBuyout;
	}

	@Column(name = "NOTICE_PERIOD_DEDUCTION", nullable = true)
	public BigDecimal getNoticePeriodDeduction() {
		return noticePeriodDeduction;
	}

	public void setNoticePeriodDeduction(BigDecimal noticePeriodDeduction) {
		this.noticePeriodDeduction = noticePeriodDeduction;
	}

	@Column(name = "RELOCATION_EXPENSES", nullable = true)
	public BigDecimal getRelocationExpenses() {
		return relocationExpenses;
	}

	public void setRelocationExpenses(BigDecimal relocationExpenses) {
		this.relocationExpenses = relocationExpenses;
	}

	@Column(name = "STIPEND_EXPENSE", nullable = true)
	public BigDecimal getStipendExpenses() {
		return stipendExpenses;
	}

	public void setStipendExpenses(BigDecimal stipendExpenses) {
		this.stipendExpenses = stipendExpenses;
	}

	@Column(name = "TRAINING_COST_RECOVERY", nullable = true)
	public BigDecimal getTrainingCostRecovery() {
		return trainingCostRecovery;
	}

	public void setTrainingCostRecovery(BigDecimal trainingCostRecovery) {
		this.trainingCostRecovery = trainingCostRecovery;
	}

	@Column(name = "SEVERANCE_PAY", nullable = true)
	public BigDecimal getSeverancePay() {
		return severancePay;
	}

	public void setSeverancePay(BigDecimal severancePay) {
		this.severancePay = severancePay;
	}

	@Column(name = "LABOUR_CHARGES", nullable = true)
	public BigDecimal getLabourCharges() {
		return labourCharges;
	}

	public void setLabourCharges(BigDecimal labourCharges) {
		this.labourCharges = labourCharges;
	}

	@Column(name = "UPDATED_BY", nullable = true)
	public Integer getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(Integer updatedBy) {
		this.updatedBy = updatedBy;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "UPDATE_TIME", nullable = true, length = 19)
	public Date getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	
	public void copyToOld(UnitBudgetoryDetail b){

        this.setOldSalary(b.getSalary());
//        this.setOldSalaryIncentive(b.getSalaryIncentive());
        this.setOldSalesIncentive(b.getSalesIncentive());
        this.setOldByodCharges(b.getByodCharges());
        this.setOldCarLease(b.getCarLease());
        this.setOldDriverSalary(b.getDriverSalary());
        this.setOldGratuity(b.getGratuity());
        this.setOldInsurnaceAccidental(b.getInsurnaceAccidental());
        this.setOldInsurnaceMedical(b.getInsurnaceMedical());
        this.setOldSupportsOpsTurnover(b.getSupportsOpsTurnover());
        this.setOldEmployeeFacilitationExpenses(b.getEmployeeFacilitationExpenses());
        this.setOldTelephoneSR(b.getTelephoneSR());
        this.setOldVehicleRunningAndMaintSR(b.getVehicleRunningAndMaintSR());
        this.setOldEmployeeStockOptionExpense(b.getEmployeeStockOptionExpense());
        this.setOldEmployerContributionLWF(b.getEmployerContributionLWF());
        this.setOldEsicEmployerCont(b.getEsicEmployerCont());
        this.setOldLeaveTravelReimbursement(b.getLeaveTravelReimbursement());
        this.setOldPfAdministrationCharges(b.getPfAdministrationCharges());
        this.setOldPfEmployerCont(b.getPfEmployerCont());
        this.setOldQuarterlyIncentive(b.getQuarterlyIncentive());
        this.setOldPreOpeningSalary(b.getPreOpeningSalary());
        this.setOldBonusAttendance(b.getBonusAttendance());
        this.setOldBonusJoining(b.getBonusJoining());
        this.setOldBonusReferral(b.getBonusReferral());
        this.setOldBonusHoliday(b.getBonusHoliday());
        this.setOldBonusOthers(b.getBonusOthers());
        this.setOldAllowanceRemoteLocation(b.getAllowanceRemoteLocation());
        this.setOldAllowanceCityCompensatory(b.getAllowanceCityCompensatory());
        this.setOldAllowanceEmployeeBenefit(b.getAllowanceEmployeeBenefit());
        this.setOldAllowanceMonk(b.getAllowanceMonk());
        this.setOldAllowanceOthers(b.getAllowanceOthers());
        this.setOldNoticePeriodBuyout(b.getNoticePeriodBuyout());
        this.setOldNoticePeriodDeduction(b.getNoticePeriodDeduction());
        this.setOldRelocationExpenses(b.getRelocationExpenses());
        this.setOldStipendExpenses(b.getStipendExpenses());
        this.setOldTrainingCostRecovery(b.getTrainingCostRecovery());
        this.setOldSeverancePay(b.getSeverancePay());
        this.setOldLabourCharges(b.getLabourCharges());
    
	}
}