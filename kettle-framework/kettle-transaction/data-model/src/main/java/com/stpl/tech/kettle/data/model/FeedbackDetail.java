/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static javax.persistence.GenerationType.IDENTITY;

/**
 * OrderInfo generated by hbm2java
 */
@Entity
@Table(name = "ORDER_FEEDBACK_DETAIL")
public class FeedbackDetail implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 2812859972251294612L;

	private Integer feedbackId;
	private Integer orderId;
	private String feedbackStatus;
	private String orderSource;
	private String productIds;
	private String customerName;
	private Integer unitId;
	private int customerId;
	private String contactNumber;
	private String emailId;
	private String source;
	private Date feedbackCreationTime;
	private Date feedbackTime;
	private Integer feedbackUnitId;
	private List<FeedbackEvent> feedbackEvents = new ArrayList<FeedbackEvent>(0);
	private Integer latestFeedbackInfoId;
	private Integer rating;
	private String eventType;
	private String ratingType;
	private Integer maxRating;

	public FeedbackDetail() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "FEEDBACK_ID", unique = true, nullable = false)
	public Integer getFeedbackId() {
		return this.feedbackId;
	}

	public void setFeedbackId(Integer feedbackId) {
		this.feedbackId = feedbackId;
	}

	@Column(name = "ORDER_ID", nullable = true)
	public Integer getOrderId() {
		return orderId;
	}

	public void setOrderId(Integer orderId) {
		this.orderId = orderId;
	}

	@Column(name = "CUSTOMER_ID")
	public int getCustomerId() {
		return this.customerId;
	}

	public void setCustomerId(int customerId) {
		this.customerId = customerId;
	}

	@Column(name = "CONTACT_NUMBER", nullable = false, length = 15)
	public String getContactNumber() {
		return contactNumber;
	}

	public void setContactNumber(String contactNumber) {
		this.contactNumber = contactNumber;
	}

	@Column(name = "EMAIL_ID", nullable = true, length = 100)
	public String getEmailId() {
		return emailId;
	}

	public void setEmailId(String emailId) {
		this.emailId = emailId;
	}

	@Column(name = "FEEDBACK_STATUS", nullable = false, length = 30)
	public String getFeedbackStatus() {
		return this.feedbackStatus;
	}

	public void setFeedbackStatus(String orderStatus) {
		this.feedbackStatus = orderStatus;
	}

	@Column(name = "FEEDBACK_SOURCE", nullable = true, length = 15)
	public String getSource() {
		return this.source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	@Column(name = "PRODUCT_IDS", nullable = true, length = 60)
	public String getProductIds() {
		return productIds;
	}

	public void setProductIds(String productIds) {
		this.productIds = productIds;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "FEEDBACK_CREATION_TIME", nullable = false, length = 19)
	public Date getFeedbackCreationTime() {
		return feedbackCreationTime;
	}

	public void setFeedbackCreationTime(Date feedbackCreationTime) {
		this.feedbackCreationTime = feedbackCreationTime;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "FEEDBACK_TIME", nullable = true, length = 19)
	public Date getFeedbackTime() {
		return this.feedbackTime;
	}

	public void setFeedbackTime(Date feedbackTime) {
		this.feedbackTime = feedbackTime;
	}

	@Column(name = "FEEDBACK_UNIT_ID", length = 19)
	public Integer getFeedbackUnitId() {
		return this.feedbackUnitId;
	}

	public void setFeedbackUnitId(Integer feedbackUnitId) {
		this.feedbackUnitId = feedbackUnitId;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "feedbackDetail")
	public List<FeedbackEvent> getFeedbackEvents() {
		return feedbackEvents;
	}

	public void setFeedbackEvents(List<FeedbackEvent> feedbackEvents) {
		this.feedbackEvents = feedbackEvents;
	}

	@Column(name = "LATEST_FEEDBACK_INFO_ID", nullable = true)
	public Integer getLatestFeedbackInfoId() {
		return latestFeedbackInfoId;
	}

	public void setLatestFeedbackInfoId(Integer latestFeedbackInfoId) {
		this.latestFeedbackInfoId = latestFeedbackInfoId;
	}

	@Column(name = "ORDER_SOURCE", nullable = true, length = 20)
	public String getOrderSource() {
		return orderSource;
	}

	public void setOrderSource(String orderSource) {
		this.orderSource = orderSource;
	}

	@Column(name = "UNIT_ID", nullable = true)
	public Integer getUnitId() {
		return unitId;
	}

	public void setUnitId(Integer unitId) {
		this.unitId = unitId;
	}

	@Column(name = "CUSTOMER_NAME", nullable = true, length = 100)
	public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}
	
	@Column(name = "FEEDBACK_RATING", nullable = true)
	public Integer getRating() {
		return rating;
	}

	public void setRating(Integer rating) {
		this.rating = rating;
	}

	@Column(name = "EVENT_TYPE", nullable = true, length = 15)
	public String getEventType() {
		return eventType;
	}

	public void setEventType(String eventType) {
		this.eventType = eventType;
	}

	@Column(name = "RATING_TYPE")
	public String getRatingType() {
		return ratingType;
	}

	public void setRatingType(String ratingType) {
		this.ratingType = ratingType;
	}

	@Column(name = "MAX_RATING")
	public Integer getMaxRating() {
		return maxRating;
	}

	public void setMaxRating(Integer maxRating) {
		this.maxRating = maxRating;
	}
}
