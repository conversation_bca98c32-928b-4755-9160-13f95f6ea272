/*
 * Created By Shanmukh
 */

package com.stpl.tech.kettle.data.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Table(name = "MONK_X_TWO_LOG_DATA")
public class MonkXTwoLogData {

    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "MONK_X_TWO_LOG_DATA_ID", unique = true)
    private Integer monkXTwoLogDataId;

    @Column(name = "TASK_SENT_TO_MONK_TIME")
    private Date taskSentToMonkTime;

    @Column(name = "TASK_ACCEPTED_TIME")
    private Date taskAcceptedTime;

    @Column(name = "TASK_COMPLETED_TIME")
    private Date taskCompletedTime;

    @Column(name = "TASK_STOPPED_TIME")
    private Date taskStoppedTime;

    @Column(name = "TASK_STOP_ACCEPTED_TIME")
    private Date taskStopAcceptedTime;

    @Column(name = "TASK_STOP_REJECTED_TIME")
    private Date taskStopRejectedTime;

    @Column(name = "TASK_COOKING_COMPLETED_TIME")
    private Date taskCookingCompletedTime;

    @Column(name = "TIME_TO_DETECT_VESSEL")
    private Integer timeToDetectVessel;

    @Column(name = "VESSEL_OFF_POSITION_TIME")
    private Integer vesselOffPositionTime;


    @Column(name = "WATER_QUANTITY")
    private Integer waterQuantity;

    @Column(name = "MILK_QUANTITY")
    private Integer milkQuantity;

    @Column(name = "WATER_POURING_TIME")
    private Integer waterPouringTime;

    @Column(name = "MILK_POURING_TIME")
    private Integer milkPouringTime;

    @Column(name = "TOTAL_TIME_FOR_POURING")
    private Integer totalTimeForPouring;

    @Column(name = "TOTAL_TIME_FOR_ADD_PATTI")
    private Integer totalTimeForAddPatti;

    @Column(name = "TOTAL_TIME_FOR_BREWING")
    private Integer totalTimeForBrewing;

    @Column(name = "TOTAL_TIME_FOR_TASK_COMPLETION")
    private Integer totalTimeForTaskCompletion;

    @Column(name = "WEB_APP_VERSION")
    private String webAppVersion;

    @Column(name = "TOTAL_TIME_TO_COMPLETE_COOKING")
    private Integer totalTimeToCompleteCooking;

    @Column(name = "TIME_TO_REMOVE_PAN_AFTER_COMPLETION")
    private Integer timeToRemovePanAfterCompletion;

}
