package com.stpl.tech.kettle.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "STOCK_DATA_UNIT_WISE")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockDataUnitWise {

    @Id
    @Column(name = "STOCK_DATA_UNIT_WISE_ID", nullable = false, unique = true)
    @GeneratedValue(strategy = IDENTITY)
    private Integer stockDataUnitId;

    @Column(name = "UNIT_ID")
    private Integer unitId;

    @Column(name = "BRAND_ID")
    private Integer brandId;

    @Column(name = "BUSINESS_DATE")
    private Date businessDate;

    @Column(name = "CAFE_OPENING_TIME")
    private Date cafeOpeningTime;

    @Column(name = "CAFE_CLOSING_TIME")
    private Date cafeClosingTime;

    @Column(name = "DELIVERY_OPENING_TIME")
    private Date deliveryOpeningTime;

    @Column(name = "DELIVERY_CLOSING_TIME")
    private Date deliveryClosingTime;

    @Column(name = "NO_OF_SKU")
    private Integer noOfSku;

    @Column(name = "DINEIN_OP_TIME")
    private Integer dineInOperationalTime;

    @Column(name = "DELIVERY_OP_TIME")
    private Integer deliveryOperationalTime;

    @Column(name = "TOTAL_DINEIN_OP_TIME")
    private Integer totalDineInOperationalTime;

    @Column(name = "TOTAL_DELIVERY_OP_TIME")
    private Integer totalDeliveryOperationalTime;

    @Column(name = "TOTAL_DINEIN_DOWN_TIME")
    private Integer totalDineInDownTime;

    @Column(name = "IS_TWENTY_FOUR_CAFE")
    private String isTwentyFourCafe;
}
