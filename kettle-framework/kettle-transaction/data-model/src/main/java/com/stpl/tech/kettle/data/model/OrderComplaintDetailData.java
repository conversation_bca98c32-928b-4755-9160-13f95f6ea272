package com.stpl.tech.kettle.data.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

import java.math.BigDecimal;
import java.util.Date;

import static javax.persistence.GenerationType.IDENTITY;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Table(name = "ORDER_COMPLAINT_DETAIL_DATA")
@Entity
public class OrderComplaintDetailData {
    @Id
    @GeneratedValue(strategy = IDENTITY)
    @Column(name = "ORDER_COMPLAINT_ID", unique = true, nullable = false)
    private Integer id;

    @Column(name = "ISSUE_TIME")
    private Date issueTime;

    @Column(name = "BILLING_SERVER_TIME")
    private Date billingServerTime;

    @Column(name = "READY_TO_DISPATCH")
    private Date readyToDispatch;

    @Column(name = "RIDER_ASSIGNED_AT")
    private Date riderAssignedAt;

    @Column(name = "RIDER_ARRIVED_AT")
    private Date riderArrivedAt;

    @Column(name = "ORDER_PICKUP_TIME")
    private Date orderPickupTime;

    @Column(name = "ORDER_DELIVERY_TIME")
    private Date orderDeliveryTime;

    @Column(name = "PREPERATION_TIME")
    private Long preperationTime;

    @Column(name = "RIDER_ASSIGNED_TIME")
    private Long riderAssignedTime;

    @Column(name = "RIDER_ARRIVED_TIME")
    private Long riderArrivedTime;

    @Column(name = "RIDER_PICKED_TIME")
    private Long riderPickedTime;

    @Column(name = "RIDER_DELIVERY_TIME")
    private Long riderDeliveryTime;

    @Column(name = "TOTAL_DELIVERY_TIME")
    private Long totalDeliveryTime;

    @Column(name = "RIDER_LATE_ARRIVED")
    private String riderLateArrived;

    @Column(name = "ORDER_LATE_DELIVERED")
    private String orderLateDelivered;

    @Column(name = "FOOD_PREPARED_LATE")
    private String foodPreparedLate;

    @Column(name = "BRAND_NAME")
    private String brandName;

    @Column(name = "UNIT_REGION")
    private String unitRegion;

    @Column(name = "UNIT_ID")
    private Integer unitId;

    @Column(name = "BRANCH_ID")
    private Integer branchId;

    @Column(name = "BUSINESS_UID")
    private String businessUid;

    @Column(name = "UNIT_NAME")
    private String unitName;

    @Column(name = "UNIT_EMAIL")
    private String unitEmail;

    @Column(name = "CAFE_MANAGER")
    private String cafeManager;

    @Column(name = "UNIT_CAFE_MANAGER")
    private String unitCafeManager;

    @Column(name = "UNIT_MANAGER")
    private String unitManager;

    @Column(name = "CAFE_MANAGER_ID")
    private String cafeManagerId;

    @Column(name = "UNIT_MANAGER_ID")
    private String unitManagerId;

    @Column(name = "CAFE_MANAGER_CONTACT")
    private String cafeManagerContact;

    @Column(name = "UNIT_MANAGER_CONTACT")
    private String unitManagerContact;

    @Column(name = "GENERATED_ORDER_ID")
    private String generatedOrderId;

    @Column(name = "CUSTOMER_NAME")
    private String customerName;

    @Column(name = "ORDER_SOURCE_ID")
    private String orderSourceId;

    @Column(name = "PARTNER_CODE")
    private String partnerCode;

    @Column(name = "ITEMS", columnDefinition = "TEXT")
    private String items;

    @Column(name = "ITEM_NAMES", columnDefinition = "TEXT")
    private String itemNames;

    @Column(name = "REVENUE")
    private BigDecimal revenue;

    @Column(name = "TOTAL_AMOUNT")
    private BigDecimal totalAmount;

    @Column(name = "DISCOUNT")
    private BigDecimal discount;

    @Column(name = "RIDER_NAME")
    private String riderName;

    @Column(name = "RIDER_CONTACT")
    private String riderContact;

    @Column(name = "ORDER_ID")
    private Integer orderId;

    @Column(name = "CUTSOMER_COMPLAINT", columnDefinition = "TEXT")
    private String customerComplaint;

    @Column(name = "ISSUE_TYPE", columnDefinition = "TEXT")
    private String issueType;

    @Column(name = "COMPLAINT_ITEMS",columnDefinition = "TEXT")
    private String complaintItems;

    @Column(name = "COMPLAINT_TIME",columnDefinition = "TEXT")
    private String complaintTime;

    @Column(name = "PREVIOUS_ITMES",columnDefinition = "TEXT")
    private String previousItems;

    @Column(name = "MODIFIED_ITEMS",columnDefinition = "TEXT")
    private String modifiedItems;

    @Column(name = "COMPLAINT_TYPE")
    private String complaintType;

    @Column(name = "FALLBACK_ERRORS",columnDefinition = "TEXT")
    private String fallbackErrors;


}
