package com.stpl.tech.kettle.data.dao;

import com.stpl.tech.kettle.data.model.SuperUOrderAnalysisData;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import  java.util.List;

@Repository
public interface SuperUOrderAnalysisDataDao extends JpaRepository<SuperUOrderAnalysisData,Integer> {

    List<SuperUOrderAnalysisData> findByUnitIdAndIsProcessed(Integer unitId,String isProcessed);
}
