package com.stpl.tech.kettle.data.model;

import java.math.BigDecimal;
import java.math.BigInteger;

public class CustomerProfileCleverTap {

    private String Name;
    private String Email;
    private String Phone;
    private Integer CustomerId;
    private String AddTime;
    private Integer LoyaltyRedeemedCount;
    private String CountryCode;
    private String IsNumberVerified;
    private String IsEmailVerified;
    private String IsBlacklisted;
    private String IsDnd;
    private String MSG_sms;
    private String MSG_whatsapp;
    private BigDecimal GcBalance;
    private BigDecimal ChyCashBalance;
    private Integer LoyaltyPointsBalance;
    private Integer NboAvailableFlag;
    private Integer DnboAvailableFlag;
    private Integer SubscriptionActionFlag;

    public String getName() {
        return Name;
    }

    public void setName(String name) {
        Name = name;
    }

    public String getEmail() {
        return Email;
    }

    public void setEmail(String email) {
        Email = email;
    }

    public String getPhone() {
        return Phone;
    }

    public void setPhone(String phone) {
        Phone = phone;
    }

    public Integer getCustomerId() {
        return CustomerId;
    }

    public void setCustomerId(Integer customerId) {
        CustomerId = customerId;
    }

    public String getAddTime() {
        return AddTime;
    }

    public void setAddTime(String addTime) {
        AddTime = addTime;
    }

    public Integer getLoyaltyRedeemedCount() {
        return LoyaltyRedeemedCount;
    }

    public void setLoyaltyRedeemedCount(Integer loyaltyRedeemedCount) {
        LoyaltyRedeemedCount = loyaltyRedeemedCount;
    }

    public String getCountryCode() {
        return CountryCode;
    }

    public void setCountryCode(String countryCode) {
        CountryCode = countryCode;
    }

    public String getIsNumberVerified() {
        return IsNumberVerified;
    }

    public void setIsNumberVerified(String isNumberVerified) {
        IsNumberVerified = isNumberVerified;
    }

    public String getIsEmailVerified() {
        return IsEmailVerified;
    }

    public void setIsEmailVerified(String isEmailVerified) {
        IsEmailVerified = isEmailVerified;
    }

    public String getIsBlacklisted() {
        return IsBlacklisted;
    }

    public void setIsBlacklisted(String isBlacklisted) {
        IsBlacklisted = isBlacklisted;
    }

    public String getIsDnd() {
        return IsDnd;
    }

    public void setIsDnd(String isDnd) {
        IsDnd = isDnd;
    }

    public String getMSG_sms() {
        return MSG_sms;
    }

    public void setMSG_sms(String MSG_sms) {
        this.MSG_sms = MSG_sms;
    }

    public String getMSG_whatsapp() {
        return MSG_whatsapp;
    }

    public void setMSG_whatsapp(String MSG_whatsapp) {
        this.MSG_whatsapp = MSG_whatsapp;
    }

    public BigDecimal getGcBalance() {
        return GcBalance;
    }

    public void setGcBalance(BigDecimal gcBalance) {
        GcBalance = gcBalance;
    }

    public BigDecimal getChyCashBalance() {
        return ChyCashBalance;
    }

    public void setChyCashBalance(BigDecimal chyCashBalance) {
        ChyCashBalance = chyCashBalance;
    }

    public Integer getLoyaltyPointsBalance() {
        return LoyaltyPointsBalance;
    }

    public void setLoyaltyPointsBalance(Integer loyaltyPointsBalance) {
        LoyaltyPointsBalance = loyaltyPointsBalance;
    }

    public Integer getNboAvailableFlag() {
        return NboAvailableFlag;
    }

    public void setNboAvailableFlag(Integer nboAvailableFlag) {
        NboAvailableFlag = nboAvailableFlag;
    }

    public Integer getDnboAvailableFlag() {
        return DnboAvailableFlag;
    }

    public void setDnboAvailableFlag(Integer dnboAvailableFlag) {
        DnboAvailableFlag = dnboAvailableFlag;
    }

    public Integer getSubscriptionActionFlag() {
        return SubscriptionActionFlag;
    }

    public void setSubscriptionActionFlag(Integer subscriptionActionFlag) {
        SubscriptionActionFlag = subscriptionActionFlag;
    }
}
