/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import static javax.persistence.GenerationType.IDENTITY;
import javax.persistence.Id;
import javax.persistence.Table;

@SuppressWarnings("serial")
@Entity
@Table(name = "UNIT_TOKEN_SEQUENCE")
public class UnitTokenSequence implements java.io.Serializable {

	private Integer sequenceId;
	private int unitId;
	private int nextValue;

	public UnitTokenSequence() {
	}

	public UnitTokenSequence(int unitId, int nextValue) {
		this.unitId = unitId;
		this.nextValue = nextValue;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "SEQUENCE_ID", unique = true, nullable = false)
	public Integer getSequenceId() {
		return this.sequenceId;
	}

	public void setSequenceId(Integer sequenceId) {
		this.sequenceId = sequenceId;
	}

	@Column(name = "UNIT_ID", nullable = false)
	public int getUnitId() {
		return this.unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	@Column(name = "NEXT_VALUE", nullable = false)
	public int getNextValue() {
		return this.nextValue;
	}

	public void setNextValue(int nextValue) {
		this.nextValue = nextValue;
	}
}
