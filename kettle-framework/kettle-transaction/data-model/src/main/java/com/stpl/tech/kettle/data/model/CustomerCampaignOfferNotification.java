/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;
// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * ChannelPartner generated by hbm2java
 */
@Entity
@Table(name = "CUSTOMER_CAMPIAGN_OFFER_NOTIFICATION")
public class CustomerCampaignOfferNotification implements java.io.Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 3232848262580649158L;
	private Integer capmpaignOfferNotificationId;
	private Integer customerCampaignOfferDetailId;
	private Integer customerId;
	private String countryCode;
	private String contactNumber;
	private String isNotificationRequired;
	private String isNotified;
	private String notificationType;
	private String notificationMedium;
	private String notificationMode;
	private String notificationText;
	private Date notificationTime;

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "CUSTOMER_CAMPIAGN_OFFER_NOTIFICATION_ID", unique = true, nullable = false)
	public Integer getCapmpaignOfferNotificationId() {
		return capmpaignOfferNotificationId;
	}

	public void setCapmpaignOfferNotificationId(Integer capmpaignOfferNotificationId) {
		this.capmpaignOfferNotificationId = capmpaignOfferNotificationId;
	}

    @Column(name = "CUSTOMER_CAMPIAGN_OFFER_DETAIL_ID", nullable = true)
	public Integer getCustomerCampaignOfferDetailId() {
		return customerCampaignOfferDetailId;
	}

	public void setCustomerCampaignOfferDetailId(Integer customerCampaignOfferDetailId) {
		this.customerCampaignOfferDetailId = customerCampaignOfferDetailId;
	}

    @Column(name = "CUSTOMER_ID", nullable = true)
	public Integer getCustomerId() {
		return customerId;
	}

	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}

    @Column(name = "COUNTRY_CODE", nullable = true)
	public String getCountryCode() {
		return countryCode;
	}

	public void setCountryCode(String countryCode) {
		this.countryCode = countryCode;
	}

    @Column(name = "CONTACT_NUMBER", nullable = true)
	public String getContactNumber() {
		return contactNumber;
	}

	public void setContactNumber(String contactNumber) {
		this.contactNumber = contactNumber;
	}

    @Column(name = "NOTIFICATION_REQUIRED", nullable = true)
	public String getIsNotificationRequired() {
		return isNotificationRequired;
	}

	public void setIsNotificationRequired(String isNotificationRequired) {
		this.isNotificationRequired = isNotificationRequired;
	}

    @Column(name = "IS_NOTIFIED", nullable = true)
	public String getIsNotified() {
		return isNotified;
	}

	public void setIsNotified(String isNotified) {
		this.isNotified = isNotified;
	}

    @Column(name = "NOTIFICATION_TYPE", nullable = true)
	public String getNotificationType() {
		return notificationType;
	}

	public void setNotificationType(String notificationType) {
		this.notificationType = notificationType;
	}

    @Column(name = "NOTIFICATION_MEDIUM", nullable = true)
	public String getNotificationMedium() {
		return notificationMedium;
	}

	public void setNotificationMedium(String notificationMedium) {
		this.notificationMedium = notificationMedium;
	}

    @Column(name = "NOTIFICATION_MODE", nullable = true)
	public String getNotificationMode() {
		return notificationMode;
	}

	public void setNotificationMode(String notificationMode) {
		this.notificationMode = notificationMode;
	}

    @Column(name = "NOTIFICATION_TEXT", nullable = true)
	public String getNotificationText() {
		return notificationText;
	}

	public void setNotificationText(String notificationText) {
		this.notificationText = notificationText;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "NOTIFICATION_TIME", nullable = true, length = 19)
	public Date getNotificationTime() {
		return notificationTime;
	}

	public void setNotificationTime(Date notificationTime) {
		this.notificationTime = notificationTime;
	}

}
