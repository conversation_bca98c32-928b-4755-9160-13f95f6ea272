package com.stpl.tech.kettle.data.model;

import java.math.BigDecimal;
import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "CASH_DATA")
public class CashData {

	private Integer cashDataId;
	private Integer customerId;
	private BigDecimal accumulatedAmount;
	private BigDecimal currentAmount;
	private BigDecimal redeemedAmount;
	private BigDecimal expiredAmount;
	private BigDecimal retainedAmount;
	private String referralCode;
	private Date creationTime;
	private Date lastUpdateTime;

	public CashData() {
		super();
	}

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "CASH_DATA_ID", unique = true, nullable = false)
	public Integer getCashDataId() {
		return cashDataId;
	}

	public void setCashDataId(Integer cashDataId) {
		this.cashDataId = cashDataId;
	}

	@Column(name = "CUSTOMER_ID", nullable = false)
	public Integer getCustomerId() {
		return customerId;
	}

	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}

	@Column(name = "ACCUMULATED_AMOUNT", nullable = false)
	public BigDecimal getAccumulatedAmount() {
		return accumulatedAmount;
	}

	public void setAccumulatedAmount(BigDecimal accumulatedAmount) {
		this.accumulatedAmount = accumulatedAmount;
	}

	@Column(name = "CURRENT_AMOUNT", nullable = false)
	public BigDecimal getCurrentAmount() {
		return currentAmount;
	}

	public void setCurrentAmount(BigDecimal currentAmount) {
		this.currentAmount = currentAmount;
	}

	@Column(name = "REDEEMED_AMOUNT", nullable = false)
	public BigDecimal getRedeemedAmount() {
		return redeemedAmount;
	}

	public void setRedeemedAmount(BigDecimal redeemedAmount) {
		this.redeemedAmount = redeemedAmount;
	}

	@Column(name = "EXPIRED_AMOUNT", nullable = false)
	public BigDecimal getExpiredAmount() {
		return expiredAmount;
	}

	public void setExpiredAmount(BigDecimal expiredAmount) {
		this.expiredAmount = expiredAmount;
	}

	@Column(name = "RETAINED_AMOUNT", nullable = false)
	public BigDecimal getRetainedAmount() {
		return retainedAmount;
	}

	public void setRetainedAmount(BigDecimal retainedAmount) {
		this.retainedAmount = retainedAmount;
	}

	@Column(name = "REFERRAL_CODE")
	public String getReferralCode() {
		return referralCode;
	}

	public void setReferralCode(String referralCode) {
		this.referralCode = referralCode;
	}

	@Column(name = "CREATION_TIME", nullable = false)
	public Date getCreationTime() {
		return creationTime;
	}

	public void setCreationTime(Date creationTime) {
		this.creationTime = creationTime;
	}

	@Column(name = "LAST_UPDATE_TIME", nullable = false)
	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date lastUpdateTime) {
		this.lastUpdateTime = lastUpdateTime;
	}

}
