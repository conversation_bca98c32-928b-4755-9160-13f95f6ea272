/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.core;

import org.apache.http.entity.ContentType;

public class DeliveryConstants {
	/*
	 * public static final String SHADOW_FAX = "SHADOW_FAX"; public static final
	 * String ROAD_RUNNR = "ROAD_RUNNR"; public static final String OPINIO =
	 * "OPINIO"; public static final String DELHIVERY = "DELHIVERY";
	 */

	public static final String REQUEST_OBJECT = "REQUEST_OBJECT";
	public static final String REQUEST_OBJECT_ADAPTER = "REQUEST_OBJECT_ADAPTER";

	public static final String RESPONSE_OBJECT = "RESPONSE_OBJECT";
	public static final String RESPONSE_OBJECT_ADAPTER = "RESPONSE_OBJECT_ADAPTER";

	public static final String EXECUTOR = "EXECUTOR";

	public static final String CREATE_ENDPOINT = "CREATE_ENDPOINT";
	public static final String CANCEL_ENDPOINT = "CANCEL_ENDPOINT";

	public static final String REGISTER_MERCHANT = "REGISTER_MERCHANT";

	public static final String ACCEPTS = ContentType.APPLICATION_JSON.toString();
	public static final String CHARSET = "UTF-8";

	public static final String AUTHORIZATION_TOKEN = "AUTHORIZATION_TOKEN";
	public static final String AUTHORIZATION_TOKEN_SECRET = "AUTHORIZATION_TOKEN_SECRET";

}
