package com.stpl.tech.kettle.clm.dao.impl;

import com.stpl.tech.kettle.clm.dao.SelectPotentialSavingDao;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.persistence.NoResultException;
import javax.persistence.Query;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Repository
@Slf4j
public class SelectPotentialSavingDaoImpl extends CLMDataAbstractDaoImpl implements SelectPotentialSavingDao  {

    @Autowired
    private EnvironmentProperties environmentProperties;

    @Override
    public Object[] getPotentialSavingData(Integer customerId, Integer brandId) {
        try {
            String queryString = "SELECT ORDERS_CNT, ORDERS_CNT_90DAYS, NET_SALES, NET_SALES_90DAYS, TOTAL_SALES, TOTAL_SALES_90DAYS from KETTLE_DATA_LAKE.CLM_CUSTOMER_DROOL_PROPERTIES where CUSTOMER_ID = :customerId and BRAND_ID = :brandId";
            if(!environmentProperties.getEnvironmentType().equals(EnvType.SPROD)){
                queryString ="SELECT ORDERS_CNT, ORDERS_CNT_90DAYS, NET_SALES, NET_SALES_90DAYS, TOTAL_SALES, TOTAL_SALES_90DAYS from KETTLE_DATA_LAKE_DEV.CLM_CUSTOMER_DROOL_PROPERTIES where CUSTOMER_ID = :customerId and BRAND_ID = :brandId";
            }
            Query query = manager.createNativeQuery(queryString);
            query.setParameter("customerId",customerId);
            query.setParameter("brandId",brandId);
            List<Object[]> data = query.getResultList();
            if(!data.isEmpty()){
                return data.get(0);
            }else{
                log.info("No PotentialSavingData result found for Customer id : {} and brand Id : {}", customerId, brandId);
            }
        }catch (NoResultException e){
            log.error("No PotentialSavingData result found for Customer id : {} and brand Id : {}", customerId, brandId);
        }catch (Exception e){
            log.error("Error while fetching PotentialSavingData for Customer id : {} and brand Id : {}", customerId, brandId, e);
        }
        return null;
    }

    @Override
    public Map<Integer,String> getCustomerType(List<Integer> customerIds){
        try {
            log.info("Query to Fetch Customer Visit Type");
            String queryString = "";
            if(AppUtils.isProd(environmentProperties.getEnvironmentType())) {
                queryString = "SELECT CUSTOMER_ID,CUST_VISIT_TYPE from KETTLE_DATA_LAKE.CLM_CUSTOMER_DROOL_PROPERTIES where CUSTOMER_ID in :customerId and BRAND_ID = 1";
            }else {
                queryString = "SELECT CUSTOMER_ID,CUST_VISIT_TYPE from KETTLE_DATA_LAKE.CLM_CUSTOMER_DROOL_PROPERTIES where CUSTOMER_ID in :customerId and BRAND_ID = 1";
            }
            Query query = manager.createNativeQuery(queryString);
            query.setParameter("customerId",customerIds);
            List<Object[]> data = query.getResultList();
            Map<Integer,String> customerVisitTypeMap = new HashMap<>();
            for(Object[] obj : data){
                customerVisitTypeMap.put((Integer) obj[0],(String) obj[1]);
            }
            return customerVisitTypeMap;
        }catch (Exception e){
            log.info("Error in fetching customer type and Error is : {}",e);
        }
        return new HashMap<>();
    }
}
