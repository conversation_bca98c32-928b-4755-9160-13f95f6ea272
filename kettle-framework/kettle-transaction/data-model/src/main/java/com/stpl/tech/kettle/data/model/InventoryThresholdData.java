/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;
// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * OrderSettlement generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "INVENTORY_THRESHOLD_DATA"

)
public class InventoryThresholdData implements java.io.Serializable {

	private Integer inventoryThresholdDataId;
	private int dayOfTheWeek;
	private int unitId;
	private int productId;
	private int minQuantity;
	private int maxQuantity;
	private int averageQuantity;
	private int totalQuantity;
	private int totalDays;
	private String thresholdDataStatus;
	private Date lastUpdateTime;

	public InventoryThresholdData() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)

	@Column(name = "INVENTORY_THRESHOLD_DATA_ID", unique = true, nullable = false)
	public Integer getInventoryThresholdDataId() {
		return this.inventoryThresholdDataId;
	}

	public void setInventoryThresholdDataId(Integer orderStatusId) {
		this.inventoryThresholdDataId = orderStatusId;
	}

	@Column(name = "UNIT_ID", nullable = false)
	public int getUnitId() {
		return this.unitId;
	}

	public void setUnitId(int orderId) {
		this.unitId = orderId;
	}

	@Column(name = "MIN_QUANTITY", nullable = false)
	public int getMinQuantity() {
		return minQuantity;
	}

	public void setMinQuantity(int employeeId) {
		this.minQuantity = employeeId;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "LAST_UPDATE_TIME", nullable = false, length = 19)
	public Date getLastUpdateTime() {
		return lastUpdateTime;
	}

	public void setLastUpdateTime(Date updateTime) {
		this.lastUpdateTime = updateTime;
	}

	@Column(name = "PRODUCT_ID", nullable = false)
	public int getProductId() {
		return productId;
	}

	public void setProductId(int recordCount) {
		this.productId = recordCount;
	}

	@Column(name = "DAY_OF_THE_WEEK", nullable = false)
	public int getDayOfTheWeek() {
		return dayOfTheWeek;
	}

	public void setDayOfTheWeek(int inventoryUpdateEventId) {
		this.dayOfTheWeek = inventoryUpdateEventId;
	}

	@Column(name = "MAX_QUANTITY", nullable = false)
	public int getMaxQuantity() {
		return maxQuantity;
	}

	public void setMaxQuantity(int thresholdQuantity) {
		this.maxQuantity = thresholdQuantity;
	}

	@Column(name = "AVG_QUANTITY", nullable = false)
	public int getAverageQuantity() {
		return averageQuantity;
	}

	public void setAverageQuantity(int averageQuantity) {
		this.averageQuantity = averageQuantity;
	}

	@Column(name = "TOTAL_QUANTITY", nullable = false)
	public int getTotalQuantity() {
		return totalQuantity;
	}

	public void setTotalQuantity(int totalQuantity) {
		this.totalQuantity = totalQuantity;
	}

	@Column(name = "TOTAL_DAYS", nullable = false)
	public int getTotalDays() {
		return totalDays;
	}

	public void setTotalDays(int totalDays) {
		this.totalDays = totalDays;
	}

	@Column(name = "THRESHOLD_DATA_STATUS", nullable = false, length = 15)
	public String getThresholdDataStatus() {
		return thresholdDataStatus;
	}

	public void setThresholdDataStatus(String thresholdDataStatus) {
		this.thresholdDataStatus = thresholdDataStatus;
	}

}
