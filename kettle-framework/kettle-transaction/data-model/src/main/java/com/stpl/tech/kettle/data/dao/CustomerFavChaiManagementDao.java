package com.stpl.tech.kettle.data.dao;

import com.stpl.tech.kettle.data.model.CustomerFavChaiMapping;
import com.stpl.tech.kettle.domain.model.CustomerFavChaiMappingVO;
import com.stpl.tech.master.data.dao.AbstractMasterDao;

import java.util.List;

public interface CustomerFavChaiManagementDao extends AbstractMasterDao {

    List<CustomerFavChaiMapping> findCustomerFavChaiByConsumeTypeAndTagType(int customerId , String consumeType , String tagType);

    List<CustomerFavChaiMapping> findAllActiveCustomerFavChai(int customerId, String status);


    CustomerFavChaiMapping findLastCustomerFavChaiMapping(int customerId, String tagType, String consumeType);

    void saveNewCustomerFavChaiMapping(CustomerFavChaiMapping newCustomerFavChaiMappping);

    List<CustomerFavChaiMappingVO> findAllActiveCustomerFavChaiMappings(int customerId, String status);

    CustomerFavChaiMapping findLastCustomerFavChaiMappingByTagType(int customerId, String tagType);

    CustomerFavChaiMapping findCustomerFavChaiMappingByTagTypeAndConsumeTypeAndStatus(int customerId, String consumeType, String tagType, String status);

    CustomerFavChaiMapping findCustomerFavChaiMappingByTagTypeAndStatus(int customerId, String tagType, String status);
}
