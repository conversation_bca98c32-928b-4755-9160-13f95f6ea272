package com.stpl.tech.kettle.core.payment;

import com.stpl.tech.kettle.core.exception.PaymentFailureException;
import com.stpl.tech.master.payment.model.PaymentRequest;

import java.util.Map;

public abstract class PaymentAdapter<T,V extends PaymentRequest> {

    public abstract V createPaymentRequest(T t, Map<String,String> map) throws Exception;

    public abstract Object updatePayment(Object object, boolean skipSignatureVerification,Integer brandId) throws PaymentFailureException;
}
