/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @date 06-Apr-2016 12:46:59 pm
 * 
 */
@Entity
@Table(name = "ASSEMBLY_LOG_DATA")
public class AssemblyLogData {

	private int assemblyLogDataId;
	private long billingServerTime;
	private int orderId;
	private int unitId;
	private String orderSource;
	private String channelPartner;
	private String deliveryPartner;
	private int timeToAcknowledge;
	private int timeToProcessHot;
	private int timeToProcessCold;
	private int timeToProcessFood;
	private int timeToProcessByWorkstations; // ( max of the above 3),
	private int timeToReadyForDispatch;
	private int timeToDispatch;
	private int timeToCancel;
	private int cooktopStation;
	private int stationEventsForOrder;
	protected int timeToDeliver;

	@Id
	@GeneratedValue(strategy = GenerationType.IDENTITY)
	@Column(name = "ASSEMBLY_LOG_DATA_ID", unique = true, nullable = false)
	public int getAssemblyLogDataId() {
		return assemblyLogDataId;
	}

	public void setAssemblyLogDataId(int assemblyLogDataId) {
		this.assemblyLogDataId = assemblyLogDataId;
	}

	@Column(name = "BILLING_SERVER_TIME")
	public long getBillingServerTime() {
		return billingServerTime;
	}

	public void setBillingServerTime(long billingServerTime) {
		this.billingServerTime = billingServerTime;
	}

	@Column(name = "UNIT_ID", nullable = false)
	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	@Column(name = "ORDER_SOURCE")
	public String getOrderSource() {
		return orderSource;
	}

	public void setOrderSource(String orderSource) {
		this.orderSource = orderSource;
	}

	@Column(name = "CHANNEL_PARTNER")
	public String getChannelPartner() {
		return channelPartner;
	}

	public void setChannelPartner(String channelPartner) {
		this.channelPartner = channelPartner;
	}

	@Column(name = "DELIVERY_PARTNER")
	public String getDeliveryPartner() {
		return deliveryPartner;
	}

	public void setDeliveryPartner(String deliveryPartner) {
		this.deliveryPartner = deliveryPartner;
	}

	@Column(name = "TIME_TO_ACKNOWLEDGE")
	public int getTimeToAcknowledge() {
		return timeToAcknowledge;
	}

	public void setTimeToAcknowledge(int timeToAcknowledge) {
		this.timeToAcknowledge = timeToAcknowledge;
	}

	@Column(name = "TIME_TO_PROCESS_HOT")
	public int getTimeToProcessHot() {
		return timeToProcessHot;
	}

	public void setTimeToProcessHot(int timeToProcessHot) {
		this.timeToProcessHot = timeToProcessHot;
	}

	@Column(name = "TIME_TO_PROCESS_COLD")
	public int getTimeToProcessCold() {
		return timeToProcessCold;
	}

	public void setTimeToProcessCold(int timeToProcessCold) {
		this.timeToProcessCold = timeToProcessCold;
	}

	@Column(name = "TIME_TO_PROCESS_FOOD")
	public int getTimeToProcessFood() {
		return timeToProcessFood;
	}

	public void setTimeToProcessFood(int timeToProcessFood) {
		this.timeToProcessFood = timeToProcessFood;
	}

	@Column(name = "TIME_TO_PROCESS_BY_WORKSTATIONS")
	public int getTimeToProcessByWorkstations() {
		return timeToProcessByWorkstations;
	}

	public void setTimeToProcessByWorkstations(int timeToProcessByWorkstations) {
		this.timeToProcessByWorkstations = timeToProcessByWorkstations;
	}

	@Column(name = "TIME_TO_READY_FOR_DISPATCH")
	public int getTimeToReadyForDispatch() {
		return timeToReadyForDispatch;
	}

	public void setTimeToReadyForDispatch(int timeToReadyForDispatch) {
		this.timeToReadyForDispatch = timeToReadyForDispatch;
	}

	@Column(name = "TIME_TO_DISPATCH")
	public int getTimeToDispatch() {
		return timeToDispatch;
	}

	public void setTimeToDispatch(int timeToDispatch) {
		this.timeToDispatch = timeToDispatch;
	}

	@Column(name = "COOKTOP_STATION")
	public int getCooktopStation() {
		return cooktopStation;
	}

	public void setCooktopStation(int cooktopStation) {
		this.cooktopStation = cooktopStation;
	}

	@Column(name = "STATION_EVENTS_FOR_ORDER")
	public int getStationEventsForOrder() {
		return stationEventsForOrder;
	}

	public void setStationEventsForOrder(int stationEventsForOrder) {
		this.stationEventsForOrder = stationEventsForOrder;
	}

	@Column(name = "TIME_TO_CANCEL")
	public int getTimeToCancel() {
		return timeToCancel;
	}

	public void setTimeToCancel(int timeToCancel) {
		this.timeToCancel = timeToCancel;
	}

	@Column(name = "ORDER_ID")
	public int getOrderId() {
		return orderId;
	}

	public void setOrderId(int orderId) {
		this.orderId = orderId;
	}
	
	@Column(name = "TIME_TO_DELIVER")
	public int getTimeToDeliver() {
		return timeToDeliver;
	}

	public void setTimeToDeliver(int timeToDeliver) {
		this.timeToDeliver = timeToDeliver;
	}
	
}
