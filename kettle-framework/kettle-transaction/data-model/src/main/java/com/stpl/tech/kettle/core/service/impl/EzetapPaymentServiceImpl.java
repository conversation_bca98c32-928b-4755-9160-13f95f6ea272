package com.stpl.tech.kettle.core.service.impl;

import com.stpl.tech.kettle.core.exception.PaymentFailureException;
import com.stpl.tech.kettle.core.service.EzetapPaymentService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.dao.PaymentGatewayDao;
import com.stpl.tech.master.payment.model.ezetap.EzetapCreateRequest;
import com.stpl.tech.master.payment.model.ezetap.EzetapPaymentResponse;
import com.stpl.tech.master.payment.model.OrderPayment;
import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.master.payment.model.PaymentStatus;
import com.stpl.tech.util.EnvType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Map;

@Service
public class EzetapPaymentServiceImpl implements EzetapPaymentService {

	@Autowired
	private PaymentGatewayDao paymentGatewayDao;

	@Autowired
	private EnvironmentProperties props;

	private static final Logger LOG = LoggerFactory.getLogger(EzetapPaymentServiceImpl.class);

	public EzetapCreateRequest createRequest(EnvType type, OrderPaymentRequest order)
			throws PaymentFailureException {
		/*RazorPayEnv env = RazorPayEnv.get(type);
		RazorpayClient razorpayClient = new RazorpayClient(env.getKey(), env.getAuthKey());*/
		//try {
			/*EzetapServiceRequest request = new EzetapServiceRequest(order.getGenerateOrderId(), order.getPaidAmount());
			ObjectMapper mapper = new ObjectMapper();
			String jsonString = mapper.writeValueAsString(request);
			System.out.println("jsonString : " + jsonString);
			JSONObject object = new JSONObject(jsonString);*/
			//com.razorpay.Order paymentRequest = razorpayClient.orders.create(object);
			EzetapCreateRequest finalRequest = new EzetapCreateRequest(order.getGenerateOrderId(), order.getPaidAmount());
			return finalRequest;
		/*} catch (JsonProcessingException e) {
			String message = "Error while creating request using razor pay for order with id "
					+ order.getGenerateOrderId();
			LOG.error(message, e);
			SlackNotificationService.getInstance().sendNotification(type, "", "kettle_errors", message);
			new ErrorNotification("Payment Request Failure", message, e, type).sendEmail();
			throw new PaymentFailureException(message, e);
		}*/
	}

	@Override
	public EzetapPaymentResponse fetchPayment(EnvType type, String paymentId) throws PaymentFailureException {
		return null;
	}

	@Override
	public EzetapPaymentResponse fetchOrder(EnvType type, String ezetapOrderId) throws PaymentFailureException {
		return null;
	}

	@Override
	public PaymentStatus getPaymentStatus(EnvType type, String paymentId, BigDecimal transactionAmount) throws PaymentFailureException {
		EzetapPaymentResponse response = fetchPayment(type,paymentId);
		String status = response.getStatus();
		if(status.equals("success") || status.equals("captured")){
			return PaymentStatus.SUCCESSFUL;
		}
		return PaymentStatus.FAILED;
	}

	@Override
	public OrderPayment refundRequest(EnvType type, OrderPayment request) throws PaymentFailureException {
		return null;
	}

	@Override
	public EzetapCreateRequest createEzetapRequest(OrderPaymentRequest order) throws PaymentFailureException {
		EzetapCreateRequest request = createRequest(props.getEnvironmentType(), order);
		paymentGatewayDao.createRequest(request, order);
		return request;
	}

	@Override
	public Map updateEzetapResponse(EzetapPaymentResponse response) {
		boolean validation = true;
		//ezetapPaymentService.validateResponse(props.getEnvironmentType(), response);
		response.setStatus(validation ? "successful" : "failed");
		return paymentGatewayDao.updateAndRedirect(response, validation);
	}

}