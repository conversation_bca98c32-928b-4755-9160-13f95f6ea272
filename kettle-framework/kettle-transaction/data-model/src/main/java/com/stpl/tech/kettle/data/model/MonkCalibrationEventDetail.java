package com.stpl.tech.kettle.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "MONK_CALIBRATION_EVENT_DETAIL")
public class MonkCalibrationEventDetail {

    private Integer monkCalibrationEventId;
    private Integer unitId;
    private Integer orderId;
    private Integer orderItemId;
    private Integer monkNo;
    private String processStatus;
    private String calibrationStatus;
    private Date orderTime;
    private Date startTime;
    private Date endTime;
    private Date serverTime;
    private Integer expectedQuantity;
    private Integer machineQuantity;
    private Integer enteredQuantity;
    private Integer milkQuantity;
    private Integer totalMonks;
    private Integer activeMonks;
    private Integer inActiveMonks;
    private Integer calibratedMonks;
    private Integer activeNonCalibratedMonks;

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "CALIBRATION_EVENT_ID")
    public Integer getMonkCalibrationEventId() {
        return monkCalibrationEventId;
    }

    public void setMonkCalibrationEventId(Integer monkCalibrationEventId) {
        this.monkCalibrationEventId = monkCalibrationEventId;
    }

    @Column(name = "UNIT_ID")
    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    @Column(name = "ORDER_ID")
    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    @Column(name = "ORDER_ITEM_ID")
    public Integer getOrderItemId() {
        return orderItemId;
    }

    public void setOrderItemId(Integer orderItemId) {
        this.orderItemId = orderItemId;
    }

    @Column(name = "MONK_NO")
    public Integer getMonkNo() {
        return monkNo;
    }

    public void setMonkNo(Integer monkNo) {
        this.monkNo = monkNo;
    }

    @Column(name = "PROCESS_STATUS")
    public String getProcessStatus() {
        return processStatus;
    }

    public void setProcessStatus(String processStatus) {
        this.processStatus = processStatus;
    }

    @Column(name = "CALIBRATION_STATUS")
    public String getCalibrationStatus() {
        return calibrationStatus;
    }

    public void setCalibrationStatus(String calibrationStatus) {
        this.calibrationStatus = calibrationStatus;
    }

    @Column(name = "ORDER_TIME")
    public Date getOrderTime() {
        return orderTime;
    }

    public void setOrderTime(Date orderTime) {
        this.orderTime = orderTime;
    }

    @Column(name = "START_TIME")
    public Date getStartTime() {
        return startTime;
    }

    public void setStartTime(Date startTime) {
        this.startTime = startTime;
    }

    @Column(name = "END_TIME")
    public Date getEndTime() {
        return endTime;
    }

    public void setEndTime(Date endTime) {
        this.endTime = endTime;
    }

    @Column(name = "SERVER_TIME")
    public Date getServerTime() {
        return serverTime;
    }

    public void setServerTime(Date serverTime) {
        this.serverTime = serverTime;
    }

    @Column(name = "EXPECTED_QUANTITY")
    public Integer getExpectedQuantity() {
        return expectedQuantity;
    }

    public void setExpectedQuantity(Integer expectedQuantity) {
        this.expectedQuantity = expectedQuantity;
    }

    @Column(name = "MACHINE_QUANTITY")
    public Integer getMachineQuantity() {
        return machineQuantity;
    }

    public void setMachineQuantity(Integer machineQuantity) {
        this.machineQuantity = machineQuantity;
    }

    @Column(name = "ENTERED_QUANTITY")
    public Integer getEnteredQuantity() {
        return enteredQuantity;
    }

    public void setEnteredQuantity(Integer enteredQuantity) {
        this.enteredQuantity = enteredQuantity;
    }

    @Column(name = "MILK_QUANTITY")
    public Integer getMilkQuantity() {
        return milkQuantity;
    }

    public void setMilkQuantity(Integer milkQuantity) {
        this.milkQuantity = milkQuantity;
    }

    @Column(name = "TOTAL_MONKS")
    public Integer getTotalMonks() {
        return totalMonks;
    }

    public void setTotalMonks(Integer totalMonks) {
        this.totalMonks = totalMonks;
    }

    @Column(name = "ACTIVE_MONKS")
    public Integer getActiveMonks() {
        return activeMonks;
    }

    public void setActiveMonks(Integer activeMonks) {
        this.activeMonks = activeMonks;
    }

    @Column(name = "IN_ACTIVE_MONKS")
    public Integer getInActiveMonks() {
        return inActiveMonks;
    }

    public void setInActiveMonks(Integer inActiveMonks) {
        this.inActiveMonks = inActiveMonks;
    }

    @Column(name = "CALIBRATED_MONKS")
    public Integer getCalibratedMonks() {
        return calibratedMonks;
    }

    public void setCalibratedMonks(Integer calibratedMonks) {
        this.calibratedMonks = calibratedMonks;
    }

    @Column(name = "ACTIVE_NON_CALIB_MONKS")
    public Integer getActiveNonCalibratedMonks() {
        return activeNonCalibratedMonks;
    }

    public void setActiveNonCalibratedMonks(Integer activeNonCalibratedMonks) {
        this.activeNonCalibratedMonks = activeNonCalibratedMonks;
    }
}
