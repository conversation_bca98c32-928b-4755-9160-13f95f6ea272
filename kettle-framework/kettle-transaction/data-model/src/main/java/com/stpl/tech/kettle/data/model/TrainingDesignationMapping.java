/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

// Generated 14 Jul, 2015 1:35:13 AM by Hibernate Tools 4.0.0

import javax.persistence.AttributeOverride;
import javax.persistence.AttributeOverrides;
import javax.persistence.Column;
import javax.persistence.EmbeddedId;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 * TrainingDesignationMapping generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "TRAINING_DESIGNATION_MAPPING")
public class TrainingDesignationMapping implements java.io.Serializable {

	private TrainingDesignationMappingId id;
	private TrainingDetail trainingDetail;
	private int designationId;
	private int departmentId;
	private String isMandatory;

	public TrainingDesignationMapping() {
	}

	public TrainingDesignationMapping(TrainingDesignationMappingId id, TrainingDetail trainingDetail, int designation,
			int department, String isMandatory) {
		this.id = id;
		this.trainingDetail = trainingDetail;
		this.designationId = designation;
		this.departmentId = department;
		this.isMandatory = isMandatory;
	}

	@EmbeddedId
	@AttributeOverrides({
			@AttributeOverride(name = "trainingId", column = @Column(name = "TRAINING_ID", nullable = false)),
			@AttributeOverride(name = "departmentId", column = @Column(name = "DEPARTMENT_ID", nullable = false)),
			@AttributeOverride(name = "designationId", column = @Column(name = "DESIGNATION_ID", nullable = false)) })
	public TrainingDesignationMappingId getId() {
		return this.id;
	}

	public void setId(TrainingDesignationMappingId id) {
		this.id = id;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "TRAINING_ID", nullable = false, insertable = false, updatable = false)
	public TrainingDetail getTrainingDetail() {
		return this.trainingDetail;
	}

	public void setTrainingDetail(TrainingDetail trainingDetail) {
		this.trainingDetail = trainingDetail;
	}

	@Column(name = "DESIGNATION_ID", nullable = false, insertable = false, updatable = false)
	public int getDesignationId() {
		return this.designationId;
	}

	public void setDesignationId(int designation) {
		this.designationId = designation;
	}

	@Column(name = "DEPARTMENT_ID", nullable = false, insertable = false, updatable = false)
	public int getDepartmentId() {
		return this.departmentId;
	}

	public void setDepartmentId(int department) {
		this.departmentId = department;
	}

	@Column(name = "IS_MANDATORY", nullable = false, length = 1)
	public String getIsMandatory() {
		return this.isMandatory;
	}

	public void setIsMandatory(String isMandatory) {
		this.isMandatory = isMandatory;
	}

}
