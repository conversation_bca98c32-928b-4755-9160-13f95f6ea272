package com.stpl.tech.kettle.data.dao;

import com.stpl.tech.kettle.data.model.OrderDetail;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface OrderDetailDao extends JpaRepository<OrderDetail,Integer>{

    @Query("SELECT od FROM OrderDetail od WHERE od.billingServerTime between :startDate And :endDate AND od.customerId NOT IN :customerIds AND od.orderSource = :orderSource AND od.brandId = :brandId AND od.orderStatus NOT IN :orderStatuses AND " +
            "od.orderId IN (:orderIds)")
    List<OrderDetail> findByBusinessDateAndCustomerIdNotInAndOrderSourceAndBrandIdAndOrderStatusNotIn(
            @Param("startDate") Date startDate,
            @Param("endDate") Date endDate,
            @Param("customerIds") List<Integer> customerIds,
            @Param("orderSource") String orderSource,
            @Param("brandId") Integer brandId,
            @Param("orderStatuses") List<String> orderStatuses,
            @Param("orderIds") List<Integer> orderIds
    );
}
