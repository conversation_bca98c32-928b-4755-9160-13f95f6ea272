package com.stpl.tech.kettle.data.model;

import java.io.Serializable;

public class CustomerMappingTypes implements Serializable {
    private String customerTypes;
    private Integer journeyCount;
    private Integer validityInDays;
    private String defaultCloneCode;
    private Integer reminderDays;

    public CustomerMappingTypes() {
    }

    public CustomerMappingTypes(String customerTypes, Integer journeyCount, Integer validityInDays,
                                String defaultCloneCode, Integer reminderDays) {
        this.customerTypes = customerTypes;
        this.journeyCount = journeyCount;
        this.validityInDays = validityInDays;
        this.defaultCloneCode = defaultCloneCode;
        this.reminderDays = reminderDays;
    }

    public String getCustomerTypes() {
        return customerTypes;
    }

    public void setCustomerTypes(String customerTypes) {
        this.customerTypes = customerTypes;
    }

    public Integer getJourneyCount() {
        return journeyCount;
    }

    public void setJourneyCount(Integer journeyCount) {
        this.journeyCount = journeyCount;
    }

    public Integer getValidityInDays() {
        return validityInDays;
    }

    public void setValidityInDays(Integer validityInDays) {
        this.validityInDays = validityInDays;
    }

    public String getDefaultCloneCode() {
        return defaultCloneCode;
    }

    public void setDefaultCloneCode(String defaultCloneCode) {
        this.defaultCloneCode = defaultCloneCode;
    }

    public Integer getReminderDays() {
        return reminderDays;
    }

    public void setReminderDays(Integer reminderDays) {
        this.reminderDays = reminderDays;
    }
}
