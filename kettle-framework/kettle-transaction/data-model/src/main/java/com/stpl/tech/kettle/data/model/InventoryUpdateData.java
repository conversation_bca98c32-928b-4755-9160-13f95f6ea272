/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;
// Generated 27 Jul, 2015 12:04:59 PM by Hibernate Tools 4.0.0

import static javax.persistence.GenerationType.IDENTITY;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Temporal;
import javax.persistence.TemporalType;

/**
 * OrderSettlement generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "INVENTORY_UPDATE_DATA"

)
public class InventoryUpdateData implements java.io.Serializable {

	private Integer inventoryUpdateDataId;
	private int inventoryUpdateEventId;
	private int unitId;
	private int productId;
	private int quantity;
	private int thresholdQuantity;
	private Date addTime;
	private Date businessDate;
	private int expireQuantity;

	public InventoryUpdateData() {
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)

	@Column(name = "INVENTORY_UPDATE_DATA_ID", unique = true, nullable = false)
	public Integer getInventoryUpdateDataId() {
		return this.inventoryUpdateDataId;
	}

	public void setInventoryUpdateDataId(Integer orderStatusId) {
		this.inventoryUpdateDataId = orderStatusId;
	}

	@Column(name = "UNIT_ID", nullable = false)
	public int getUnitId() {
		return this.unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	@Column(name = "QUANTITY", nullable = false)
	public int getQuantity() {
		return quantity;
	}

	public void setQuantity(int employeeId) {
		this.quantity = employeeId;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "ADD_TIME", nullable = false, length = 19)
	public Date getAddTime() {
		return addTime;
	}

	public void setAddTime(Date updateTime) {
		this.addTime = updateTime;
	}

	@Column(name = "PRODUCT_ID", nullable = false)
	public int getProductId() {
		return productId;
	}

	public void setProductId(int recordCount) {
		this.productId = recordCount;
	}

	@Temporal(TemporalType.TIMESTAMP)
	@Column(name = "BUSINESS_DATE", nullable = false, length = 19)
	public Date getBusinessDate() {
		return businessDate;
	}

	public void setBusinessDate(Date businessDate) {
		this.businessDate = businessDate;
	}

	@Column(name = "INVENTORY_UPDATE_EVENT_ID", nullable = false)
	public int getInventoryUpdateEventId() {
		return inventoryUpdateEventId;
	}

	public void setInventoryUpdateEventId(int inventoryUpdateEventId) {
		this.inventoryUpdateEventId = inventoryUpdateEventId;
	}

	@Column(name = "THRESHOLD_QUANTITY", nullable = false)
	public int getThresholdQuantity() {
		return thresholdQuantity;
	}

	public void setThresholdQuantity(int thresholdQuantity) {
		this.thresholdQuantity = thresholdQuantity;
	}

	@Column(name = "EXPIRE_QUANTITY", nullable = false)
	public int getExpireQuantity() {
		return expireQuantity;
	}

	public void setExpireQuantity(int expireQuantity) {
		this.expireQuantity = expireQuantity;
	}

}
