/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigDecimal;

import static javax.persistence.GenerationType.IDENTITY;

@Entity
@Table(name = "DELIVERY_PARTNER")
public class DeliveryPartner implements java.io.Serializable {

	private static final long serialVersionUID = 1L;
	private Integer partnerId;
	private String partnerCode;
	private String partnerDisplayName;
	private String partnerStatus;
	private String partnerType;
	private String automated;
	private BigDecimal deliveryCost;
	private String eligibleForCash;

	public DeliveryPartner() {
	}

	public DeliveryPartner(String partnerCode, String partnerDisplayName) {
		this.partnerCode = partnerCode;
		this.partnerDisplayName = partnerDisplayName;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)

	@Column(name = "PARTNER_ID", unique = true, nullable = false)
	public Integer getPartnerId() {
		return this.partnerId;
	}

	public void setPartnerId(Integer partnerId) {
		this.partnerId = partnerId;
	}

	@Column(name = "PARTNER_CODE", nullable = false, length = 50)
	public String getPartnerCode() {
		return this.partnerCode;
	}

	public void setPartnerCode(String partnerCode) {
		this.partnerCode = partnerCode;
	}

	@Column(name = "PARTNER_DISPLAY_NAME", nullable = false, length = 100)
	public String getPartnerDisplayName() {
		return this.partnerDisplayName;
	}

	public void setPartnerDisplayName(String partnerDisplayName) {
		this.partnerDisplayName = partnerDisplayName;
	}

	@Column(name = "PARTNER_STATUS", nullable = false, length = 15)
	public String getPartnerStatus() {
		return partnerStatus;
	}

	public void setPartnerStatus(String partnerStatus) {
		this.partnerStatus = partnerStatus;
	}

	@Column(name = "PARTNER_TYPE", nullable = false, length = 15)
	public String getPartnerType() {
		return partnerType;
	}

	public void setPartnerType(String partnerType) {
		this.partnerType = partnerType;
	}

	@Column(name = "AUTOMATED", nullable = false, length = 1)
	public String getAutomated() {
		return automated;
	}

	public void setAutomated(String automated) {
		this.automated = automated;
	}

	@Column(name = "PER_DELIVERY_COST", nullable = false)
	public BigDecimal getDeliveryCost() {
		return deliveryCost;
	}

	public void setDeliveryCost(BigDecimal deliveryCost) {
		this.deliveryCost = deliveryCost;
	}

	@Column(name = "CASH_ELIGIBILITY", nullable = true)
	public String getEligibleForCash() {
		return eligibleForCash;
	}

	public void setEligibleForCash(String eligibleForCash) {
		this.eligibleForCash = eligibleForCash;
	}
}
