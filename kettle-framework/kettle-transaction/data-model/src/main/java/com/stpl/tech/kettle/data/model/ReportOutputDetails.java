/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

import static javax.persistence.GenerationType.IDENTITY;

// Generated 28 Jul, 2015 1:08:59 PM by Hibernate Tools 4.0.0

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.Table;

/**
 * ReportOutputDetails generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "REPORT_OUTPUT_DETAILS")
public class ReportOutputDetails implements java.io.Serializable {

	private Integer reportOutputId;
	private ReportExecutionDetail reportExecutionDetail;
	private String outputLocation;
	private String reportGenerated;
	private String emailedReport;
	private String emailedTo;

	public ReportOutputDetails() {
	}

	public ReportOutputDetails(ReportExecutionDetail reportExecutionDetail, String reportGenerated,
			String emailedReport) {
		this.reportExecutionDetail = reportExecutionDetail;
		this.reportGenerated = reportGenerated;
		this.emailedReport = emailedReport;
	}

	public ReportOutputDetails(ReportExecutionDetail reportExecutionDetail, String outputLocation,
			String reportGenerated, String emailedReport, String emailedTo) {
		this.reportExecutionDetail = reportExecutionDetail;
		this.outputLocation = outputLocation;
		this.reportGenerated = reportGenerated;
		this.emailedReport = emailedReport;
		this.emailedTo = emailedTo;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "REPORT_OUTPUT_ID", unique = true, nullable = false)
	public Integer getReportOutputId() {
		return this.reportOutputId;
	}

	public void setReportOutputId(Integer reportOutputId) {
		this.reportOutputId = reportOutputId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "EXECUTION_DETAIL_ID", nullable = false)
	public ReportExecutionDetail getReportExecutionDetail() {
		return this.reportExecutionDetail;
	}

	public void setReportExecutionDetail(ReportExecutionDetail reportExecutionDetail) {
		this.reportExecutionDetail = reportExecutionDetail;
	}

	@Column(name = "OUTPUT_LOCATION")
	public String getOutputLocation() {
		return this.outputLocation;
	}

	public void setOutputLocation(String outputLocation) {
		this.outputLocation = outputLocation;
	}

	@Column(name = "REPORT_GENERATED", nullable = false, length = 1)
	public String getReportGenerated() {
		return this.reportGenerated;
	}

	public void setReportGenerated(String reportGenerated) {
		this.reportGenerated = reportGenerated;
	}

	@Column(name = "EMAILED_REPORT", nullable = false, length = 1)
	public String getEmailedReport() {
		return this.emailedReport;
	}

	public void setEmailedReport(String emailedReport) {
		this.emailedReport = emailedReport;
	}

	@Column(name = "EMAILED_TO")
	public String getEmailedTo() {
		return this.emailedTo;
	}

	public void setEmailedTo(String emailedTo) {
		this.emailedTo = emailedTo;
	}

}
