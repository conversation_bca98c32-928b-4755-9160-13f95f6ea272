package com.stpl.tech.kettle.data.dao;

import com.stpl.tech.kettle.clevertap.data.model.EventPushTrack;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;

@Repository
public interface EventPushTrackDao extends JpaRepository<EventPushTrack,Integer> {

    @Query("SELECT e FROM EventPushTrack e WHERE e.updatedAt between :startDate and :endDate AND e.status = :status AND e.eventName = :eventName")
    List<EventPushTrack> findByStatusInAndEventNameAndU(@Param("startDate") Date startDate,
                                                        @Param("endDate") Date endDate,
                                                        @Param("status") String status,
                                                        @Param("eventName") String eventName);
}
