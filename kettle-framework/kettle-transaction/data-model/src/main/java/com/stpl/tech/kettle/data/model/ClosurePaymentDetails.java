/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.data.model;

import static javax.persistence.GenerationType.IDENTITY;

// Generated 27 Jul, 2015 8:18:00 PM by Hibernate Tools 4.0.0

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.FetchType;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.JoinColumn;
import javax.persistence.ManyToOne;
import javax.persistence.OneToMany;
import javax.persistence.Table;

/**
 * ClosurePaymentDetails generated by hbm2java
 */
@SuppressWarnings("serial")
@Entity
@Table(name = "CLOSURE_PAYMENT_DETAILS")
public class ClosurePaymentDetails implements java.io.Serializable {

	private Integer paymentClosureId;
	private UnitClosureDetails unitClosureDetails;
	private int paymentModeId;
	private BigDecimal expectedAmount;
	private BigDecimal actualAmount;
	private String reconciliationStatus;
	private BigDecimal gmv;
	private BigDecimal discount;
	private BigDecimal netSalesAmount;
	private BigDecimal roundOff;
	private BigDecimal totalAmount;
	private BigDecimal totalTax;
	private Integer billCount;
	private List<ClosurePaymentTaxDetails> closureTaxes = new ArrayList<>(0);

	public ClosurePaymentDetails() {
	}

	public ClosurePaymentDetails(UnitClosureDetails unitClosureDetails, int paymentModeId,
			String reconciliationStatus) {
		this.unitClosureDetails = unitClosureDetails;
		this.paymentModeId = paymentModeId;
		this.reconciliationStatus = reconciliationStatus;
	}

	public ClosurePaymentDetails(UnitClosureDetails unitClosureDetails, int paymentModeId, BigDecimal expectedAmount,
			BigDecimal actualAmount, String reconciliationStatus) {
		this.unitClosureDetails = unitClosureDetails;
		this.paymentModeId = paymentModeId;
		this.expectedAmount = expectedAmount;
		this.actualAmount = actualAmount;
		this.reconciliationStatus = reconciliationStatus;
	}

	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "PAYMENT_CLOSURE_ID", unique = true, nullable = false)
	public Integer getPaymentClosureId() {
		return this.paymentClosureId;
	}

	public void setPaymentClosureId(Integer paymentClosureId) {
		this.paymentClosureId = paymentClosureId;
	}

	@ManyToOne(fetch = FetchType.LAZY)
	@JoinColumn(name = "CLOSURE_ID", nullable = false)
	public UnitClosureDetails getUnitClosureDetails() {
		return this.unitClosureDetails;
	}

	public void setUnitClosureDetails(UnitClosureDetails unitClosureDetails) {
		this.unitClosureDetails = unitClosureDetails;
	}

	@Column(name = "PAYMENT_MODE_ID", nullable = false)
	public int getPaymentModeId() {
		return this.paymentModeId;
	}

	public void setPaymentModeId(int paymentModeId) {
		this.paymentModeId = paymentModeId;
	}

	@Column(name = "EXPECTED_AMOUNT", precision = 10)
	public BigDecimal getExpectedAmount() {
		return this.expectedAmount;
	}

	public void setExpectedAmount(BigDecimal expectedAmount) {
		this.expectedAmount = expectedAmount;
	}

	@Column(name = "ACTUAL_AMOUNT", precision = 10)
	public BigDecimal getActualAmount() {
		return this.actualAmount;
	}

	public void setActualAmount(BigDecimal actualAmount) {
		this.actualAmount = actualAmount;
	}

	@Column(name = "RECONCILIATION_STATUS", nullable = true, length = 20)
	public String getReconciliationStatus() {
		return this.reconciliationStatus;
	}

	public void setReconciliationStatus(String reconciliationStatus) {
		this.reconciliationStatus = reconciliationStatus;
	}

	@Column(name = "GMV", nullable = false)
	public BigDecimal getGmv() {
		return gmv;
	}

	public void setGmv(BigDecimal gmv) {
		this.gmv = gmv;
	}

	@Column(name = "DISCOUNT", nullable = false)
	public BigDecimal getDiscount() {
		return discount;
	}

	public void setDiscount(BigDecimal discount) {
		this.discount = discount;
	}

	@Column(name = "NET_SALES_AMOUNT", nullable = false)
	public BigDecimal getNetSalesAmount() {
		return netSalesAmount;
	}

	public void setNetSalesAmount(BigDecimal netSalesAmount) {
		this.netSalesAmount = netSalesAmount;
	}

	@Column(name = "ROUND_OFF_AMOUNT", nullable = false)
	public BigDecimal getRoundOff() {
		return roundOff;
	}

	public void setRoundOff(BigDecimal roundOff) {
		this.roundOff = roundOff;
	}

	@Column(name = "TOTAL_AMOUNT", nullable = false)
	public BigDecimal getTotalAmount() {
		return totalAmount;
	}

	public void setTotalAmount(BigDecimal totalAmount) {
		this.totalAmount = totalAmount;
	}
	
	@Column(name = "TOTAL_TAX", nullable = true)
	public BigDecimal getTotalTax() {
		return totalTax;
	}

	public void setTotalTax(BigDecimal totalTax) {
		this.totalTax = totalTax;
	}

	@Column(name = "BILL_COUNT", nullable = false)
	public Integer getBillCount() {
		return billCount;
	}

	public void setBillCount(Integer billCount) {
		this.billCount = billCount;
	}

	@OneToMany(fetch = FetchType.LAZY, mappedBy = "closurePaymentDetail")
	public List<ClosurePaymentTaxDetails> getClosureTaxes() {
		return closureTaxes;
	}

	public void setClosureTaxes(List<ClosurePaymentTaxDetails> closureTaxes) {
		this.closureTaxes = closureTaxes;
	}

}
