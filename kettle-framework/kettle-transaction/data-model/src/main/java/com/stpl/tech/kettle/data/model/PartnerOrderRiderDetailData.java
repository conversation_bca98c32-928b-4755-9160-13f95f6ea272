package com.stpl.tech.kettle.data.model;

import static javax.persistence.GenerationType.IDENTITY;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;

@Entity
@Table(name = "PARTNER_ORDER_RIDER_DATA")
public class PartnerOrderRiderDetailData {
	
	private Integer id;
	private Integer partnerId;
	private String orderId;
	private String riderPhone;
	private String rbt;
	private String isHighTemp;
	private String isRiderWearingMask;
	
	@Id
	@GeneratedValue(strategy = IDENTITY)
	@Column(name = "ID", unique = true, nullable = false)
	public Integer getId() {
		return id;
	}
	public void setId(Integer id) {
		this.id = id;
	}
	
	@Column(name = "PARTNER_ID")
	public Integer getPartnerId() {
		return partnerId;
	}
	public void setPartnerId(Integer partnerId) {
		this.partnerId = partnerId;
	}
	
	@Column(name = "ORDER_ID")
	public String getOrderId() {
		return orderId;
	}
	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}
	
	@Column(name = "RIDER_PHONE")
	public String getRiderPhone() {
		return riderPhone;
	}
	public void setRiderPhone(String riderPhone) {
		this.riderPhone = riderPhone;
	}
	
	@Column(name = "RBT")
	public String getRbt() {
		return rbt;
	}
	public void setRbt(String rbt) {
		this.rbt = rbt;
	}
	
	@Column(name = "IS_HIGH_TEMP")
	public String isHighTemp() {
		return isHighTemp;
	}
	public void setHighTemp(String isHighTemp) {
		this.isHighTemp = isHighTemp;
	}
	
	@Column(name = "IS_RIDER_WEARING_MASK")
	public String getIsRiderWearingMask() {
		return isRiderWearingMask;
	}
	public void setIsRiderWearingMask(String isRiderWearingMask) {
		this.isRiderWearingMask = isRiderWearingMask;
	}
	
}
