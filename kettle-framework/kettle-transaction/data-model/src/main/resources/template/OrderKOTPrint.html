<#setting locale="en_US"><#setting date_format="dd/MM/yyyy HH:mm">
<table style="width:100%;" cellpadding="0" cellspacing="2" >
	<tr>
		<td>-</td>
		<td></td>
	</tr>
	<tr>
		<td>-</td>
		<td></td>
	</tr>
	<tr>
		<td>-</td>
		<td></td>
	</tr>
	<tr>
		<td>-</td>
		<td></td>
	</tr>
	<#if (unit.tableService) && (order.tableNumber)??>
		<tr>
			<td><b>Table No </b></td>
			<td><b>${order.tableNumber}</b></td>
		</tr>
	</#if>
	<#if (order.customerName)??>
	<tr>
		<td>Name </td>
		<td>${order.customerName}</td>
	</tr>
	</#if>
	<tr>
		<td>Type </td>
		<td>${description}</td>
	</tr>
	<tr>
		<td>Order No </td>
		<td>${order.generateOrderId}</td>
	</tr>
	<#if (order.orderRemark)?? >
		<tr>
			<td>Order Remark </td>
			<td><span>${order.orderRemark}</span></td>
		</tr>
	</#if>
</table>
<hr />
<table cellpadding="0" cellspacing="4" width="8cm">
    <#list orderItems><#items as orderItem>
	<tr>
		<td width="5cm"><span>${orderItem.productName}</span></td>
		<td>${orderItem.dimension?substring(0,1)}</td>
		<td>${orderItem.quantity}</td>
	</tr>
	<#if orderItem.composition.hasDefaultVariant()>
	<tr>
		<td colspan="3"><#list orderItem.composition.variants><#items as variant><#if
			variant.alias?? && !variant.defaultSetting><span>&nbsp;&nbsp;${variant.alias} -</span></#if></#items></#list></td>
	</tr>
	</#if>
	<#if orderItem.composition.products?? && orderItem.composition.products?has_content>
	<tr>
		<td colspan="3"><#list orderItem.composition.products><#items as product><#if
			product.product.name??><span>&nbsp;&nbsp;${product.product.name} -</span></#if></#items></#list></td>
	</tr>
	</#if>
	<#if orderItem.composition.addons?? && orderItem.composition.addons?has_content>
	<tr>
		<td colspan="3">&nbsp;&nbsp;<#list orderItem.composition.addons><#items as addon><#if
			addon.product.shortCode??><span>${addon.product.shortCode}, </span></#if></#items></#list></td>
	</tr>
	</#if>
	<#if orderItem.composition.options?? && orderItem.composition.options?has_content>
	<tr>
		<td colspan="3">&nbsp;&nbsp;<#list orderItem.composition.options><#items as option><span>${option}, </span></#items></#list></td>
	</tr>
	</#if>		
	<#if orderItem.composition.menuProducts?? && orderItem.composition.menuProducts?has_content>
		<#list orderItem.composition.menuProducts><#items as menuProduct>
	<tr>
		<td><span>(C) ${menuProduct.productName}</span></td>
		<td>${menuProduct.dimension?substring(0,1)}</td>
		<td>${menuProduct.quantity}</td>
	</tr>
	
	<#if menuProduct.composition.hasDefaultVariant()>
	<tr>
		<td colspan="3"><#list menuProduct.composition.variants><#items as variant><#if
			variant.alias?? && !variant.defaultSetting><span>&nbsp;&nbsp;&nbsp;&nbsp;${variant.alias} -</span></#if></#items></#list></td>
	</tr>
	</#if>
	<#if menuProduct.composition.products?? && menuProduct.composition.products?has_content>
	<tr>
		<td colspan="3"><#list menuProduct.composition.products><#items as product><#if
			product.product.name??><span>&nbsp;&nbsp;&nbsp;&nbsp;${product.product.name} -</span></#if></#items></#list></td>
	</tr>
	</#if>
	<#if menuProduct.composition.addons?? && menuProduct.composition.addons?has_content>
	<tr>
		<td colspan="3">&nbsp;&nbsp;&nbsp;&nbsp;<#list menuProduct.composition.addons><#items as addon><#if
			addon.product.shortCode??><span>${addon.product.shortCode}, </span></#if></#items></#list></td>
	</tr>
	</#if>
	<#if menuProduct.composition.options?? && menuProduct.composition.options?has_content>
	<tr>
		<td colspan="3">&nbsp;&nbsp;&nbsp;&nbsp;<#list menuProduct.composition.options><#items as option><span>${option}, </span></#items></#list></td>
	</tr>
	</#if>
		</#items></#list>
	</#if>
</#items></#list>
</table>