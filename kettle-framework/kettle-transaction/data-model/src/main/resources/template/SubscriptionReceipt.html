<#setting locale="en_US"> <#setting date_format="MM/dd/yyyy HH:mm:ss">
<#setting number_format="0.##">
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
<title>Chai Subscription No: ${order.generateOrderId}</title>
</head>
<body>

	<meta http-equiv=Content-Type content="text/html; charset=UTF-8">
	<style type="text/css">
body, td, div, p, a, input {
	font-family: arial, sans-serif;
}
</style>
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>Chai Subscription No: ${order.generateOrderId}</title>

	<style type="text/css">
body, td {
	font-size: 13px
}

a:link, a:active {
	color: #1155CC;
	text-decoration: none
}

a:hover {
	text-decoration: underline;
	cursor: pointer
}

a:visited {
	color: ##6611CC
}

img {
	border: 0px
}

pre {
	white-space: pre;
	white-space: -moz-pre-wrap;
	white-space: -o-pre-wrap;
	white-space: pre-wrap;
	word-wrap: break-word;
	max-width: (window.innerWidth> 0)? window.innerWidth: screen.width;
	overflow: auto;
}

.btn {
	border-radius: 5px;
	padding: 14px 14px;
	font-size: 14px;
	text-decoration: none;
	margin: 14px;
	color: #fff;
	position: relative;
	display: inline-block;
}

.green {
	background-color: #50773e;
}
</style>
	<table style="width: 100%; border-collapse: collapse; border: 0">
		<tbody>
			<tr>
				<td style="padding-top: 2px; padding-left: 9px; padding-bottom: 2px">
					<div style="overflow: hidden; text-align: left">
						<img alt="Chaayos"
							src="http://campaign.chaayos.com/chaayos/logo/chaayos-logo.png"
							style="padding-top: 9px" height="150" width="150" />
					</div>
				</td>
				<td>
					<table style="width: 100%; border-collapse: collapse; border: 0">
						<tbody>
							<tr>
								<td>
									<div style="overflow: hidden; text-align: left">
										<a href="https://twitter.com/chaayos"
											class="twitter-follow-button" data-show-count="false"
											data-size="large">Follow @chaayos</a>
										<script>
											!function(d, s, id) {
												var js, fjs = d
														.getElementsByTagName(s)[0], p = /^http:/
														.test(d.location) ? 'http'
														: 'https';
												if (!d.getElementById(id)) {
													js = d.createElement(s);
													js.id = id;
													js.src = p
															+ '://platform.twitter.com/widgets.js';
													fjs.parentNode
															.insertBefore(js,
																	fjs);
												}
											}
													(document, 'script',
															'twitter-wjs');
										</script>
									</div>
								</td>
								<td>
									<div class="fb-like"
										data-href="https://www.facebook.com/Chaayos"
										data-layout="button_count" data-action="like"
										data-show-faces="false" data-share="true"></div>
								</td>
						</tbody>
					</table>
				</td>
			</tr>
		</tbody>
	</table>
	<div style=" padding:5px; padding-left: 13px;text-align: center; font-size: 20px">
	We Deliver 1800 120 2424
	</div>
	<hr
		style="display: block; border-top: solid 1px; border-bottom: 0; color: #479147" />
	<div style="text-align: left">
	<b style="font-family: Arial; font-size: 15px; color: #936940; font-weight: 100">Thank you for placing a subscription order with Chaayos.</b>
	</div>
	<table width="100%" style="border-collapse: collapse; border: 0">
		<tr>
			<td style="padding-top: 2px; padding-left: 9px; padding-bottom: 2px">
				<b
				style="font-family: Arial; font-size: 15px; color: #936940; font-weight: 100">Subscription
					No: </b>
			</td>
			<td>${order.generateOrderId}</td>
			<td width="30%"></td>
		</tr>
		<tr>
			<td style="padding-top: 2px; padding-left: 9px; padding-bottom: 2px">
				<b
				style="font-family: Arial; font-size: 15px; color: #936940; font-weight: 100">Subscription
					Time: </b>
			</td>
			<td>${order.billCreationTime?date}</td>
			<td></td>
		</tr>
		<#if (customer.id) gt 5> <#if (customer.firstName)??>
		<tr>
			<td style="padding-top: 2px; padding-left: 9px; padding-bottom: 2px"><b
				style="font-family: Arial; font-size: 15px; color: #936940; font-weight: 100">Customer
					Name: </b></td>
			<td>${customer.firstName} <#if
				(customer.middleName)??>${customer.middleName}</#if> <#if
				(customer.lastName)??>${customer.lastName}</#if></td>
			<td></td>
		</tr>
		</#if>
		<tr>
			<td style="padding-top: 2px; padding-left: 9px; padding-bottom: 2px"><b
				style="font-family: Arial; font-size: 15px; color: #936940; font-weight: 100">Mobile
					No: </b></td>
			<td>${customer.countryCode}-${customer.contactNumber}</td>
			<td></td>
		</tr>
		<tr>
			<td style="padding-top: 2px; padding-left: 9px; padding-bottom: 2px"><b
				style="font-family: Arial; font-size: 15px; color: #936940; font-weight: 100">Loyalty
					Points: </b></td>
			<td>${customer.loyaltyPoints}</td>
			<td></td>
		</tr>
		<tr>
			<td style="padding-top: 2px; padding-left: 9px; padding-bottom: 2px"><b
				style="font-family: Arial; font-size: 15px; color: #936940; font-weight: 100">Email
					Address: </b></td>
			<td>${customer.emailId}</td>
			<td></td>
		</tr>
		<#if (deliveryAddress)??>
		<tr>
			<td style="padding-top: 2px; padding-left: 9px; padding-bottom: 2px"><b
				style="font-family: Arial; font-size: 15px; color: #936940; font-weight: 100">
					Delivery Address: </b></td>
			<td><#if (deliveryAddress.line1)??>${deliveryAddress.line1},
				</#if> <#if (deliveryAddress.line2)??>${deliveryAddress.line2},
				</#if> <#if (deliveryAddress.line3)??>${deliveryAddress.line3},
				</#if> <#if
				(deliveryAddress.locality)??>${deliveryAddress.locality}, </#if>
				${deliveryAddress.city}</td>
			<td></td>
		</tr>
		</#if> <#if (order.subscriptionDetail)??>
		<tr>
			<td style="padding-top: 2px; padding-left: 9px; padding-bottom: 2px">
				<b
				style="font-family: Arial; font-size: 15px; color: #936940; font-weight: 100">
					Subscription Type : </b>
			</td>
			<td>${order.subscriptionDetail.type}</td>
			<td></td>
		</tr>
		<tr>
			<td style="padding-top: 2px; padding-left: 9px; padding-bottom: 2px">
				<b
				style="font-family: Arial; font-size: 15px; color: #936940; font-weight: 100">
					Subscription Start Date: </b>
			</td>
			<td>${order.subscriptionDetail.startDate?string('dd.MM.yyyy')}</td>
			<td></td>
		</tr>
		<tr>
			<td style="padding-top: 2px; padding-left: 9px; padding-bottom: 2px">
				<b
				style="font-family: Arial; font-size: 15px; color: #936940; font-weight: 100">
					Subscription End Date: </b>
			</td>
			<td>${order.subscriptionDetail.endDate?string('dd.MM.yyyy')}</td>
			<td></td>
		</tr>
		<tr>
			<td style="padding-top: 2px; padding-left: 9px; padding-bottom: 2px">
				<b
				style="font-family: Arial; font-size: 15px; color: #936940; font-weight: 100">
					Subscription Days: </b>
			</td>
			<td>${subscriptionDays}</td>
			<td></td>
		</tr>
		<tr>
			<td style="padding-top: 2px; padding-left: 9px; padding-bottom: 2px">
				<b
				style="font-family: Arial; font-size: 15px; color: #936940; font-weight: 100">
					Subscription Delivery Time: </b>
			</td>
			<td>${subscriptionDeliveryTimes}</td>
			<td></td>
		</tr>
		<tr>
			<td style="padding-top: 2px; padding-left: 9px; padding-bottom: 2px">
				<b
				style="font-family: Arial; font-size: 15px; color: #936940; font-weight: 100">
					Email Notification: </b>
			</td>
			<td>${emailNotification}</td>
			<td></td>
		</tr>
		<tr>
			<td style="padding-top: 2px; padding-left: 9px; padding-bottom: 2px">
				<b
				style="font-family: Arial; font-size: 15px; color: #936940; font-weight: 100">
					SMS Notification: </b>
			</td>
			<td>${smsNotification}</td>
			<td></td>
		</tr>
		</#if> </#if>
		<!-- TODO Remove Channel Partner Id Check -->
		<#if (channelPartner)?? && (channelPartner.name)?? &&
		channelPartner.id gt 1>
		<tr>
			<td style="padding-top: 2px; padding-left: 9px; padding-bottom: 2px">
				<b
				style="font-family: Arial; font-size: 15px; color: #936940; font-weight: 100">
					Channel Partner: </b>
			</td>
			<td>${channelPartner.name}</td>
			<td></td>
		</tr>
		</#if> <#if (order.orderRemark)??>
		<tr>
			<td style="padding-top: 2px; padding-left: 9px; padding-bottom: 2px">
				<b
				style="font-family: Arial; font-size: 15px; color: #936940; font-weight: 100">
					Order Remark: </b>
			</td>
			<td>${order.orderRemark}</td>
			<td></td>
		</tr>
		</#if>

		</tbody>
	</table>
	<hr
		style="display: block; border-top: solid 1px; border-bottom: 0; color: #479147" />
	<table width="100%" border="0" cellpadding="12" cellspacing="0">
		<tbody>
			<tr>

				<td style="padding-top: 2px; padding-left: 9px; padding-bottom: 2px">
					<b
					style="font-family: Arial; font-size: 15px; color: #936940; font-weight: 100">Description
				</b>
				</td>
				<td style="padding-top: 2px; padding-left: 9px; padding-bottom: 2px">
					<b
					style="font-family: Arial; font-size: 15px; color: #936940; font-weight: 100">Quantity</b>
				</td>
				<td style="padding-top: 2px; padding-left: 9px; padding-bottom: 2px">
					<b
					style="font-family: Arial; font-size: 15px; color: #936940; font-weight: 100">Unit
						Price</b>
				</td>
				<td align="right"
					style="padding-top: 2px; padding-left: 9px; padding-bottom: 2px">
					<b
					style="font-family: Arial; font-size: 15px; color: #936940; font-weight: 100">Total
						Cost</b>
				</td>
			</tr>
			<#list order.orders>
			<ul>
				<#items as orderItem>
				<tr>
					<td
						style="padding-top: 2px; padding-left: 9px; padding-bottom: 2px">
						<#if ((orderItem.complimentaryDetail)?? && (orderItem.complimentaryDetail.isComplimentary))>
						*
						<#elseif ((orderItem.discountDetail)?? && (orderItem.discountDetail.discountCode)?? && (orderItem.discountDetail.discountCode > 0))>
						*
						<#elseif ((orderItem.discountDetail)?? && (orderItem.discountDetail.promotionalOffer)?? && (orderItem.discountDetail.promotionalOffer > 0))>
						*
						<#elseif ((orderItem.billType)?? && (orderItem.billType == "MRP"))>
						#&nbsp;
						</#if>
						${orderItem.productName}<#if
						orderItem.dimension != "None"> ${orderItem.dimension}</#if><#if
						(orderItem.complimentaryDetail)?? &&
						(orderItem.complimentaryDetail.isComplimentary)>(Complimentary)</#if></td>
					<td
						style="padding-top: 2px; padding-left: 9px; padding-bottom: 2px">${orderItem.quantity}</td>
					<td
						style="padding-top: 2px; padding-left: 9px; padding-bottom: 2px">${orderItem.price}</td>
					<td align="right"
						style="padding-top: 2px; padding-left: 9px; padding-bottom: 2px">${orderItem.totalAmount}</td>
				</tr>
				</#items>
			</ul>
			</#list>
		<tbody>
	</table>
	<hr
		style="display: block; border-top: solid 1px; border-bottom: 0; color: #479147">
	<table width="100%" border="0" cellpadding="12" cellspacing="0">
		<tbody>
			<tr align="right">
				<td colspan="3"
					style="padding-top: 2px; padding-left: 9px; padding-bottom: 2px; color: #936940;">TOTAL
				</td>
				<td style="padding-top: 2px; padding-left: 9px; padding-bottom: 2px">${order.transactionDetail.totalAmount}</td>
			</tr>
			<#if promotionalDiscount gt 0>
			<tr align="right">
				<td colspan="3"
					style="padding-top: 2px; padding-left: 9px; padding-bottom: 2px; color: #936940;">PROMOTIONAL
					OFFER</td>
				<td style="padding-top: 2px; padding-left: 9px; padding-bottom: 2px">${promotionalDiscount}</td>
			</tr>
			</#if> <#if discountPercent gt 0>
			<tr align="right">
				<td colspan="3"
					style="padding-top: 2px; padding-left: 9px; padding-bottom: 2px; color: #936940;">DISCOUNT
					@ ${discountPercent}%</td>
				<td style="padding-top: 2px; padding-left: 9px; padding-bottom: 2px">${discountValue}</td>
			</tr>
			</#if>
			<#list order.transactionDetail.taxes> <#items as tax>
			<tr align="right">
				<td colspan="3"
					style="padding-top: 2px; padding-left: 9px; padding-bottom: 2px; color: #936940;">${tax.code} @ ${tax.percentage} %</td>
				<td style="padding-top: 2px; padding-left: 9px; padding-bottom: 2px">${tax.value}</td>
			</tr>
			</#items></#list> 
			<#if order.transactionDetail.roundOffValue gt 0>
			<tr align="right">
				<td colspan="3"
					style="padding-top: 2px; padding-left: 9px; padding-bottom: 2px; color: #936940;">ROUNDING
					OFF</td>
				<td style="padding-top: 2px; padding-left: 9px; padding-bottom: 2px">${order.transactionDetail.roundOffValue}</td>
			</tr>
			</#if>
			<tr>
				<td colspan="6"
					style="padding-top: 2px; padding-left: 9px; padding-bottom: 2px"></td>
			</tr>
			<tr align="right">
				<td colspan="3"
					style="padding-top: 2px; padding-left: 9px; padding-bottom: 2px; color: #936940;">
					<h2>Bill Total</h2>
				</td>
				<td style="padding-top: 2px; padding-left: 9px; padding-bottom: 2px">
					<h2>${order.transactionDetail.paidAmount}</h2>
				</td>
			</tr>
		</tbody>
	</table>
	<table width="100%" border="0" cellpadding="12" cellspacing="0">
		<tbody>
			<tr>
				<td>
					<div style="width: 100%; min-height: 65px; background: #f1f2f2">
						<p style="padding-left: 13px; padding-top: 16px; font-size: 13px">
							<i>Note: This is an electronically generated receipt and does
								not require signature.For any queries, please drop an email at <a
								href="mailto:<EMAIL>" target="_blank"><EMAIL></a>.
							</i>
						</p>
						<p style="padding-left: 13px; padding-top: 16px; font-size: 13px">
							<i>^ indicates a zero tax product</i>
						</p>
					</div>
				</td>
			</tr>
			<tr align="center">
				<td>
					<div style="width: 100%; min-height: 65px; background: #f1f2f2">
						<p
							style="font-size: 13px; text-align: center; padding-left: 13px; padding-right: 13px">
							<b
								style="font-family: Arial; font-size: 15px; color: #936940; font-weight: 100">
								Company Identification Number:</b> U55204DL2012PTC304447 <b
								style="font-family: Arial; font-size: 15px; color: #936940; font-weight: 100">
								Service Tax Number:</b> AARCS3853MSD001<b
								style="font-family: Arial; font-size: 15px; color: #936940; font-weight: 100">
								Company Address:</b> Sunshine Teahouse Private Limited, 1st Fl, #382, 100' Rd, Ghitorni, New Delhi 30

						</p>
					</div>
				</td>
			</tr>
		</tbody>
	</table>
</body>
</html>