<#setting locale="en_US"> <#setting date_format="MM/dd/yyyy HH:mm:ss">
<#setting number_format="0.##">
<!DOCTYPE html PUBLIC "-//W3C//DTD HTML 4.01//EN" "http://www.w3.org/TR/html4/strict.dtd">
<html lang="en">
<head>
<title>Stock-out report ${currentTime}</title>
</head>
<body>

	<meta http-equiv=Content-Type content="text/html; charset=UTF-8">
	<style type="text/css">
body, td, div, p, a, input {
	font-family: arial, sans-serif;
}
</style>
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<title>Stock-out report </title>

	<style type="text/css">
body, td {
	font-size: 13px
}

a:link, a:active {
	color: #1155CC;
	text-decoration: none
}

a:hover {
	text-decoration: underline;
	cursor: pointer
}

a:visited {
	color: ##6611CC
}

img {
	border: 0px
}

pre {
	white-space: pre;
	white-space: -moz-pre-wrap;
	white-space: -o-pre-wrap;
	white-space: pre-wrap;
	word-wrap: break-word;
	max-width: (window.innerWidth> 0)? window.innerWidth: screen.width;
	overflow: auto;
}

.btn {
	border-radius: 5px;
	padding: 14px 14px;
	font-size: 14px;
	text-decoration: none;
	margin: 14px;
	color: #fff;
	position: relative;
	display: inline-block;
}

.green {
	background-color: #50773e;
}
</style>
	<#if thresholdType == "STOCK_OUT">
		<h2>Total Stocked Out Inventory Details</h2>
		<#assign unitIds = stockedOutInventory?keys>
		<#if unitIds?has_content>
		<table width="100%" border="0" cellpadding="12" cellspacing="0">
			<tbody>
				<tr>
					<td colspan="5" style="padding-top: 2px; padding-left: 9px; padding-bottom: 2px">
							<table width="100%" border="1" cellpadding="5" cellspacing="0" align="center">
								<tr>
									<th>Unit Name</th>
									<th>No Of Products</th>
									<th>Product Names</th>
								</tr>
							<#list unitIds as unitId>
							<#assign inventoryObjList = stockedOutInventory[unitId]>
							<#assign inventoryObj = inventoryObjList?first>
								<#if stockedOutInventory[unitId]?has_content>
										<tr>
											<td>${inventoryObj.unit.name}</td>
											<td>${stockedOutInventory[unitId]?size}</td>
										<td>
										<#list stockedOutInventory[unitId] as productInventory>
										${productInventory.product.detail.name}, 
										</#list>
										</td>
										</tr>
								</#if>
							</#list>
							</table>
						
						<hr style="display: block; border-top: solid 1px; border-bottom: 0; color: #479147">
					</td>
				</tr>
			<tbody>
		</table>
		<#else>
			<p text-color="green">Hurray! everything is in stock.</p>
		</#if>
		
	<#else>
	
		<h2>Probable Inventory Stock Out Details</h2>
		<#assign unitIds = stockedOutInventory?keys>
		<#if unitIds?has_content>
		<table width="100%" border="0" cellpadding="12" cellspacing="0">
			<tbody>
				<tr>
					<td colspan="5" style="padding-top: 2px; padding-left: 9px; padding-bottom: 2px">
							<table width="100%" border="1" cellpadding="5" cellspacing="0" align="center">
								<tr>
									<th>Unit Name</th>
									<th>No of Products</th>
									<th>Product Names</th>
								</tr>
							<#list unitIds as unitId>
							<#assign inventoryObjList = stockedOutInventory[unitId]>
							<#assign inventoryObj = inventoryObjList?first>
								<#if stockedOutInventory[unitId]?has_content>
										<tr>
											<td>${inventoryObj.unit.name}</td>
											<td>${stockedOutInventory[unitId]?size}</td>
										<td>
										<#list stockedOutInventory[unitId] as productInventory>
										${productInventory.product.detail.name}(${productInventory.quantity}, <#if productInventory.thresholdData??>${productInventory.thresholdData.avgQuantity}<#else>-1</#if>), 
										</#list>
										</td>
										</tr>
								</#if>
							</#list>
							</table>
						
						<hr style="display: block; border-top: solid 1px; border-bottom: 0; color: #479147">
					</td>
				</tr>
			<tbody>
		</table>
		<#else>
			<p>Hurray! No probable stock outs.</p>
		</#if>
		
	</#if>
</body>
</html>