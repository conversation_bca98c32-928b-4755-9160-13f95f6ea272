/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.service.report;

import com.stpl.tech.kettle.reports.model.SettlementReport;
import com.stpl.tech.util.notification.AbstractTemplate;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.util.AppUtils;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

public class SettlementReceipt extends AbstractTemplate {
	private Unit unit;
	private Collection<SettlementReport> details;
	private int noOfOrders;
	private String basePath;
	private String generationTime = AppUtils.getCurrentTimeISTString();

	public SettlementReceipt(String basePath, Unit unit, int orderCount, Collection<SettlementReport> details) {
		this.unit = unit;
		this.details = details;
		this.basePath = basePath;
		this.noOfOrders = orderCount;
	}

	@Override
	public String getTemplatePath() {
		return "template/SettlementReceipt.html";
	}

	public String getFilepath() {
		return basePath + "/" + unit.getId() + "/reports/SettlementReceipt-" + unit.getName() + "-"
				+ AppUtils.getCurrentTimeISTStringWithNoColons() + ".html";
	}

	public Map<String, Object> getData() {
		// Build the data-model
		Map<String, Object> data = new HashMap<String, Object>();
		data.put("settlements", details);
		data.put("unitName", unit.getName());
		data.put("reportName", "SettlementType Report");
		data.put("size", noOfOrders);
		data.put("generationTime", generationTime);
		return data;
	}

}
