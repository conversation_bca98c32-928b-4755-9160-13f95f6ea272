/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.service.controller;

import com.stpl.tech.kettle.core.service.WorkStationService;
import com.stpl.tech.kettle.domain.model.AssemblyLog;
import com.stpl.tech.kettle.domain.model.AssemblyTAT;
import com.stpl.tech.kettle.domain.model.MonkCalibrationEvent;
import com.stpl.tech.kettle.domain.model.MonkCalibrationStatus;
import com.stpl.tech.kettle.domain.model.MonkCalibrationTime;
import com.stpl.tech.kettle.domain.model.WorkstationLog;
import com.stpl.tech.kettle.domain.model.WorkstationManualTask;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.core.MediaType;
import java.util.List;

import static com.stpl.tech.kettle.service.core.ServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.service.core.ServiceConstants.SEPARATOR;
import static com.stpl.tech.kettle.service.core.ServiceConstants.WORKSTATION_SERVICES_ROOT_CONTEXT;

/**
 * <AUTHOR> Singh
 *
 * @date 05-Apr-2016 7:18:57 pm
 *
 */

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + WORKSTATION_SERVICES_ROOT_CONTEXT)
public class WorkStationResource {

	private static final Logger LOG = LoggerFactory.getLogger(WorkStationResource.class);

	@Autowired
	private WorkStationService workStationService;

	/**
	 * @param
	 * @return int
	 */
	@RequestMapping(method = RequestMethod.POST, value = "workstation/log/add", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public int addWorkStationLog(@RequestBody final List<WorkstationLog> workstationLog,
			final HttpServletResponse response, final HttpServletRequest request) {
		LOG.info("Saving Worktation Log");
		return workStationService.addWorkStationLog(workstationLog);
	}

	@RequestMapping(method = RequestMethod.POST, value = "assembly/log/add", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public int addAssemblyLog(@RequestBody final List<AssemblyLog> assemblyLog, final HttpServletResponse response,
			final HttpServletRequest request) {
		LOG.info("Saving Worktation Log");
		return workStationService.addAssemblyLog(assemblyLog);
	}

	@RequestMapping(method = RequestMethod.GET, value = "assembly/tat", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public AssemblyTAT getAssemblyTAT(@RequestParam Integer unitId) {
		LOG.info("Getting Workstation TAT Log for today and last hour for unitID :::: {}", unitId);
		return workStationService.getAssemblyTAT(unitId);
	}

	// CRON for triggering TAT calculation for all units every half an hour
	@Scheduled(cron = "0 0 * * * *", zone = "GMT+05:30")
	public void processTATForUnits(){
		workStationService.processTATForAllUnits();
	}

	@RequestMapping(method = RequestMethod.POST, value = "assembly/addManualTask", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public List<WorkstationManualTask> addWorkStationManualTasks(@RequestBody List<WorkstationManualTask> workstationTasks) {
		LOG.info("Request to add manual WorkStation order tasks: {}", workstationTasks);
		return workStationService.addWorkStationManualTasks(workstationTasks);
	}

	@RequestMapping(method = RequestMethod.POST, value = "workstation/add-monk-calibration-event", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public MonkCalibrationEvent addMonkCalibrationEvent(@RequestBody MonkCalibrationEvent monkCalibrationEvent) {
		LOG.info("Request to add Hot-Station Monk Calibration Event: {}", monkCalibrationEvent);
		return workStationService.addMonkCalibrationEvent(monkCalibrationEvent);
	}

	@RequestMapping(method = RequestMethod.GET, value = "workstation/get-last-monk-calibration-status", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public MonkCalibrationEvent getLastMonkCalibrationStatus(@RequestParam Integer unitId, @RequestParam Integer monkNo) {
		LOG.info("Request to get Last Monk Calibration Status for Unit: {} and Monk No.: {}", unitId, monkNo);
		return workStationService.getLastMonkCalibrationStatus(unitId, monkNo);
	}

	@RequestMapping(method = RequestMethod.GET, value = "workstation/get-monk-calibration-status", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public List<MonkCalibrationStatus> getMonkCalibrationStatus(@RequestParam Integer unitId) {
		LOG.info("Request to get Monk Calibration Status for Unit: {}", unitId);
		return workStationService.getMonkCalibrationStatus(unitId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "workstation/get-monk-calibration-time", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public MonkCalibrationTime getMonkCalibrationTime(@RequestParam Integer unitId) {
		LOG.info("Request to get Monk Calibration Time for Unit: {}", unitId);
		return workStationService.getMonkCalibrationTime(unitId);
	}
}
