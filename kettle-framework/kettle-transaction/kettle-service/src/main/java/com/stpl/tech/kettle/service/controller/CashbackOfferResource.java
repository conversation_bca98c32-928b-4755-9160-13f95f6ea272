package com.stpl.tech.kettle.service.controller;


import com.stpl.tech.kettle.customer.service.CashBackOfferCache;
import com.stpl.tech.kettle.core.service.CashbackOfferService;
import com.stpl.tech.kettle.offer.model.CashBackOfferDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.ws.rs.core.MediaType;

import java.util.List;

import static com.stpl.tech.kettle.service.core.ServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.service.core.ServiceConstants.CASHBACK_OFFER_RESOURCE;
import static com.stpl.tech.kettle.service.core.ServiceConstants.SEPARATOR;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + CASHBACK_OFFER_RESOURCE)
public class CashbackOfferResource {

    @Autowired
    private CashBackOfferCache cashBackOfferCache;

    @Autowired
    private CashbackOfferService cashbackOfferService;

    @RequestMapping(method = RequestMethod.POST, value = "clear-cache", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void clearCashbackOfferCache(){
        cashBackOfferCache.clearCashBackOfferCache();
    }

    @RequestMapping(method = RequestMethod.POST, value = "add-offer", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean addNewOffer(@RequestBody CashBackOfferDTO offerDTO){
        return cashbackOfferService.addCashbackOffer(offerDTO);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-offers", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<CashBackOfferDTO> getOffers(@RequestBody CashBackOfferDTO offerDTO){
        return cashbackOfferService.getCashbackOffers(offerDTO);
    }
}
