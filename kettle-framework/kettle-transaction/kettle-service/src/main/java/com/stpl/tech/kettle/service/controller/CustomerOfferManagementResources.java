/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.service.controller;

import com.stpl.tech.kettle.core.SignUpTimeSlotCount;
import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.core.cache.UnitSessionCache;
import com.stpl.tech.kettle.core.cache.UnitSessionDetail;
import com.stpl.tech.kettle.core.cache.UnitTerminalDetail;
import com.stpl.tech.kettle.core.notification.MembershipDiscount;
import com.stpl.tech.kettle.core.notification.MembershipProductDiscount;
import com.stpl.tech.kettle.core.notification.OfferOrder;
import com.stpl.tech.kettle.core.service.CustomerOfferManagementService;
import com.stpl.tech.kettle.core.service.VoucherValidator;
import com.stpl.tech.kettle.core.service.VoucherValidatorFactory;
import com.stpl.tech.kettle.customer.service.CustomerService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.CustomerBasicInfo;
import com.stpl.tech.kettle.domain.model.DiscountDetail;
import com.stpl.tech.kettle.offer.strategy.OfferActionStrategy;
import com.stpl.tech.kettle.offer.strategy.OfferStrategyHelper;
import com.stpl.tech.kettle.service.model.CustomerOfferModel;
import com.stpl.tech.kettle.service.model.SignUpOfferRequest;
import com.stpl.tech.master.core.OfferCategoryType;
import com.stpl.tech.master.core.WebErrorCode;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.OfferValidationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.master.data.model.CustomerWinbackOfferInfo;
import com.stpl.tech.master.data.model.DeliveryOfferDetailData;
import com.stpl.tech.master.domain.model.ChannelPartnerDetail;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.CustomerWinbackOfferInfoDomain;
import com.stpl.tech.master.domain.model.OfferDetail;
import com.stpl.tech.master.domain.model.OfferResponse;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.servlet.View;

import javax.annotation.Nullable;
import javax.ws.rs.core.MediaType;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.stpl.tech.kettle.service.core.ServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.service.core.ServiceConstants.CUSTOMER_OFFER_MANAGEMENT_ROOT_CONTEXT;
import static com.stpl.tech.kettle.service.core.ServiceConstants.SEPARATOR;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + CUSTOMER_OFFER_MANAGEMENT_ROOT_CONTEXT) // 'v1/customer-offer-management'
public class CustomerOfferManagementResources extends AbstractResources {

	private static final Logger LOG = LoggerFactory.getLogger(CustomerOfferManagementResources.class);

	@Autowired
	private CustomerService customerService;

	@Autowired
	private CustomerOfferManagementService customerOfferService;

	@Autowired
	private MasterDataCache masterCache;

	@Autowired
	private EnvironmentProperties properties;

	@Autowired
	private VoucherValidatorFactory voucherValidatorFactory;

	@RequestMapping(method = RequestMethod.POST, value = "customer/availed-offer", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public Boolean hasOfferAvailed(@RequestBody final CustomerOfferModel customerObject) throws DataNotFoundException {

		if (customerObject != null && customerObject.getCustomerId() != 0) {
			/*
			 * return !customerService.getOfferDetail(customerObject.getCustomerId(),
			 * customerObject.getOfferCode()) .isEmpty();
			 */
			Customer customer = customerService.getCustomer(customerObject.getCustomerId());
			return !TransactionUtils.eligibleForSignupOffer(customerService, customer,masterCache);
		} else {
			return true;
		}

	}

	@RequestMapping(method = RequestMethod.POST, value = "contactNumber/availed-offer", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean hasOfferAvailedforContact(@RequestBody final CustomerOfferModel customerObject)
			throws DataNotFoundException {

		if (customerObject != null && customerObject.getContactNumber() != null) {
			int customerId = customerService.getCustomer(customerObject.getContactNumber()).getId();
			return customerService.getOfferDetail(customerId, customerObject.getOfferCode()) != null;
		} else {
			return false;
		}
	}

	/**
	 * Apply Coupon on an order.
	 *
	 * @param offerOrder with order object as data
	 * @return OfferOrder
	 */
	@RequestMapping(method = RequestMethod.POST, value = "coupon/apply", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public OfferOrder applyCoupon(@RequestBody final OfferOrder offerOrder) {
		try {
			if (AppUtils.CHAAYOS_CASH_OFFER_CODE.contains(offerOrder.getCouponCode())
					|| masterCache.getSubscriptionSkuCodeDetail().containsKey(offerOrder.getCouponCode().toUpperCase())) {
				String message = String.format("Cannot Apply Internal Coupons %s using this API",
						offerOrder.getCouponCode());
				LOG.error(message);
				offerOrder.setError(true);
				offerOrder.setErrorMessage(message);
				offerOrder.setErrorCode(WebErrorCode.INVALID_COUPON.getCode());
				offerOrder.setOrder(null);
				return offerOrder;
			}else if (properties.getPartnerPaymentOfferCode().equals(offerOrder.getCouponCode())) {
				String message = String.format("Cannot Apply Partner Coupons %s using this API",
						offerOrder.getCouponCode());
				LOG.error(message);
				offerOrder.setError(true);
				offerOrder.setErrorMessage(message);
				offerOrder.setErrorCode(WebErrorCode.INVALID_COUPON.getCode());
				offerOrder.setOrder(null);
				return offerOrder;
			}
			setCustomerDetail(offerOrder);
			return customerOfferService.applyCoupoun(offerOrder, null);
		} catch (OfferValidationException e) {
			LOG.error("Error while applying coupon", e);
			LOG.error(e.getMessage());
			offerOrder.setError(true);
			offerOrder.setErrorMessage(e.getMessage());
			offerOrder.setErrorCode(e.getErrorCode().getCode());
			offerOrder.setOrder(null);
		} catch (DataNotFoundException e) {
			LOG.error("Error while applying coupon", e);
			LOG.error(e.getMessage());
			offerOrder.setError(true);
			offerOrder.setErrorMessage(e.getMessage());
			offerOrder.setErrorCode(WebErrorCode.DATA_NOT_FOUND.getCode());
			offerOrder.setOrder(null);
		}
		return offerOrder;
	}

	private void setCustomerDetail(OfferOrder offerOrder) {
		if (offerOrder.getOrder().getCustomerId() == null) {
			UnitSessionDetail session = UnitSessionCache.getInstance().get(
					new UnitTerminalDetail(offerOrder.getOrder().getUnitId(), offerOrder.getOrder().getTerminalId()));
			offerOrder.getOrder().setCustomerId(session.getCustomer() == null ? null : session.getCustomer().getId());
			offerOrder.setContact(session.getCustomer() == null ? null : session.getCustomer().getContactNumber());
		}

	}

	/**
	 * Apply Cash on an order.
	 *
	 * @param offerOrder with order object as data
	 * @return OfferOrder
	 */
	@RequestMapping(method = RequestMethod.POST, value = "cash/apply", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public OfferOrder applyCashDiscount(@RequestBody final OfferOrder offerOrder) {
		try {
			if (!AppUtils.CHAAYOS_CASH_OFFER_CODE.contains(offerOrder.getCouponCode())
					&& !masterCache.getSubscriptionSkuCodeDetail().containsKey(offerOrder.getCouponCode().toUpperCase())) {
				String message = String.format("Cannot Apply Internal Coupons %s using this API",
						offerOrder.getCouponCode());
				LOG.error(message);
				offerOrder.setError(true);
				offerOrder.setErrorMessage(message);
				offerOrder.setErrorCode(WebErrorCode.INVALID_COUPON.getCode());
				offerOrder.setOrder(null);
				return offerOrder;
			}
			setCustomerDetail(offerOrder);
			if(Objects.isNull(offerOrder.isSkipSubscriptionValidation())){
				offerOrder.setSkipSubscriptionValidation(false);
			}
			if (AppUtils.CHAAYOS_CASH_OFFER_CODE.contains(offerOrder.getCouponCode())) {
				// TODO move constants to property
				boolean availedCashOffer = false;
				if(!offerOrder.getCouponCode().equals(AppConstants.DOHFUL_CASH_OFFER_CODE)){
					 availedCashOffer = customerOfferService.availedCashOffer(offerOrder.getOrder().getCustomerId(),
							AppUtils.getStartOfBusinessDay(AppUtils
									.getDayBeforeOrAfterCurrentDay(AppConstants.CHAAYOS_CASH_OFFER_REDEMPTION_DAYS)),
							AppUtils.CHAAYOS_CASH_OFFER_CODE, AppConstants.CHAAYOS_CASH_OFFER_MAX_ALLOWED_REDEMPTION);
				}
				if (availedCashOffer) {
					throw new OfferValidationException("Customer has already availed cash offer",
							WebErrorCode.MAX_LIMIT_REACHED);
				}

				BigDecimal customerCash = setOfferCash(offerOrder);
				return customerOfferService.applyCoupoun(offerOrder, customerCash);
			} else if (masterCache.getSubscriptionSkuCodeDetail().containsKey(offerOrder.getCouponCode())) {
				// TODO move constants to property
				boolean hasSubscription = customerOfferService.hasSubscription(offerOrder.getOrder().getCustomerId(), offerOrder.getCouponCode());

				if (!hasSubscription && !offerOrder.isSkipSubscriptionValidation()) {
					throw new OfferValidationException(
							"Customer cannot avail subscription offer as the subscription is not present or expired",
							WebErrorCode.UNAVAILABLE_MEMNBERSHIP);
				}
				return customerOfferService.applyCoupoun(offerOrder, null);
			}
		} catch (OfferValidationException e) {
			LOG.error("Error while applying chaayos cash", e);
			LOG.error(e.getMessage());
			offerOrder.setError(true);
			offerOrder.setErrorMessage(e.getMessage());
			offerOrder.setErrorCode(e.getErrorCode().getCode());
			offerOrder.setOrder(null);
		} catch (DataNotFoundException e) {
			LOG.error("Error while applying chaayos cash", e);
			LOG.error(e.getMessage());
			offerOrder.setError(true);
			offerOrder.setErrorMessage(e.getMessage());
			offerOrder.setErrorCode(WebErrorCode.DATA_NOT_FOUND.getCode());
			offerOrder.setOrder(null);
		}
		return offerOrder;
	}
	
	@RequestMapping(method = RequestMethod.POST, value = "check/membership", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public MembershipDiscount applyCashDiscount(@RequestBody final MembershipDiscount offerOrder) {
		Map<Integer, MembershipProductDiscount> applicableDiscounts = new HashMap<>();
		for (Integer productId : offerOrder.getProductId()) {
			Product couponProduct = masterCache.getSubscriptionProductDetail(productId);

			String code = couponProduct.getSkuCode();
			CouponDetail coupon = masterCache.getSubscriptionSkuCodeDetail(code).getKey();
			if (coupon != null) {
				OfferDetail detail = coupon.getOffer();

				Class<? extends OfferActionStrategy> strategyClass = OfferStrategyHelper
						.getStrategy(OfferCategoryType.valueOf(coupon.getOffer().getType()));
				try {
					DiscountDetail discount = strategyClass.newInstance().getDiscountDetail(code,
							BigDecimal.valueOf(detail.getOfferValue()), offerOrder.getTotalAmount(),
							offerOrder.getPaidAmount(), detail.getMaxDiscountAmount());
					if (discount != null) {
						MembershipProductDiscount prd = new MembershipProductDiscount();
						prd.setApplicableDiscount(discount.getDiscount());
						prd.setCouponCode(code);
						prd.setDesc(coupon.getOffer().getDescription());
						Map<com.stpl.tech.master.domain.model.Pair<BigDecimal,String>,String> productAlias =
								masterCache.getUnitProductAlias(offerOrder.getUnitId(),productId);
						Optional<Pair<BigDecimal,String>> firstKey =productAlias.keySet().stream().findFirst();
						if (firstKey.isPresent()){
							prd.setDimension(firstKey.get().getValue());
							prd.setPrice(firstKey.get().getKey());
						}else if (couponProduct.getPrices() != null && couponProduct.getPrices().size() > 0) {
							prd.setDimension(couponProduct.getPrices().get(0).getDimension());
							prd.setPrice(couponProduct.getPrices().get(0).getPrice());
						} else {
							prd.setDimension("3 Months");
							prd.setPrice(new BigDecimal("99.00"));
						}
						prd.setProductId(productId);
						prd.setProductName(couponProduct.getName());
						applicableDiscounts.put(productId, prd);
					}
				} catch (InstantiationException | IllegalAccessException e) {
					LOG.error("error in getting instance class initiated for {}", offerOrder, e);
				}

			}
		}
		offerOrder.setApplicableDiscounts(applicableDiscounts);
		return offerOrder;
	}
	

	@RequestMapping(method = RequestMethod.POST, value = "channel-partners", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public Collection<ChannelPartnerDetail> getAllChannelPartners() throws DataNotFoundException {
		LOG.info("Request to get all Channel partners");
		return masterCache.getAllChannelPartners();
	}

	@RequestMapping(method = RequestMethod.POST, value = "sign-up/availed", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public Boolean checkIfSignUpOfferAvailed(@RequestBody String customerContact) throws Exception {
		try {
			LOG.info("Enter checkIfSignUpOfferAvailed where customerContact is :: " + customerContact);
			Customer customer = customerService.getCustomer(customerContact);
			if(customer!=null){
				return customerOfferService.hasSignUpOfferAvailed(customer.getId());
			}
			return false;
		}catch (Exception ex){
			LOG.error("Error while checkIfSignUpOfferAvailed .", ex);
			LOG.error(ex.getMessage());
			return false;
		}
	}

	@RequestMapping(method = RequestMethod.POST, value = "sign-up/avail", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public Boolean availSignUpOffer(@RequestBody final SignUpOfferRequest request) throws Exception  {
		LOG.info("Enter availSignUpOffer where SignUpOfferRequest is :: " + JSONSerializer.toJSON(request));
		try {
			if(request!=null){
				return customerOfferService.availSignUpOffer(request.getCustomerId(), request.getCustomerName(),
						request.getTimeOfDelivery(), request.getDateOfDelivery(), request.getCompleteAddress(), request.getCity(),
						request.getPinCode(), request.getProduct(), request.getUnitId(), request.getBrandId());
			}
		} catch (Exception e) {
			LOG.error("Error while availing sign up offer in kettle.", e);
			LOG.error(e.getMessage());
			return Boolean.FALSE;
		}
		return Boolean.FALSE;
	}

	@RequestMapping(method = RequestMethod.POST, value = "sign-up/timeslot", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public SignUpTimeSlotCount fetchSignUpOfferTimeSlots(@RequestBody final String dateOfDelivery) throws Exception  {
		LOG.info("Enter fetchSignUpOfferTimeslots where dateOfDelivery is :: " + dateOfDelivery);
		try {
			return customerOfferService.fetchSignUpOfferTimeSlots(dateOfDelivery);
		} catch (Exception e) {
			LOG.error("Error while fetching sign up time slot count in kettle.", e);
			return null;
		}
	}

	private BigDecimal setOfferCash(OfferOrder offerOrder) throws OfferValidationException {

		BigDecimal cashbackCash = customerService.getAvailableCashback(offerOrder.getOrder().getCustomerId());
		LOG.info("Available Cashback for customer id {} is {}", offerOrder.getOrder().getCustomerId(), cashbackCash);
		String usedCode = AppConstants.CHAAYOS_CASH_OFFER_CODE;
		if (cashbackCash != null && cashbackCash.compareTo(BigDecimal.ZERO) > 0) {
			usedCode = AppConstants.CHAAYOS_CASH_BACK_OFFER_CODE;
		}
		BigDecimal customerCash = customerService.getAvailableCash(offerOrder.getOrder().getCustomerId());
		if (customerCash == null || customerCash.compareTo(BigDecimal.ONE) <= 0) {
			throw new OfferValidationException("No Chaayos Cash Available for customer",
					WebErrorCode.NO_CHAAYOS_CASH_AVAILABLE);
		} else {
			LOG.info("************************  Cash Available ***********************" );
			LOG.info(customerCash.toString() );
			LOG.info("***********************" );
		}
		 /*BigDecimal redemptionCash = AppUtils.getMinimum(customerCash, AppConstants.CHAAYOS_CASH_REDEMPTION_PER_ORDER);
		BigDecimal offerValue = AppUtils.getMinimum(customerCash,
				offerOrder.getOrder().getTransactionDetail().getPaidAmount());
		offerOrder.getOrder().setCashRedeemed(offerValue);
		offerOrder.setCouponCode(offerOrder.getOrder().getOfferCode() == null ? usedCode
				: usedCode.equals(offerOrder.getOrder().getOfferCode()) ? offerOrder.getOrder().getOfferCode()
				: usedCode);*/
		// offerOrder.getOrder().setOfferCode(AppConstants.CHAAYOS_CASH_OFFER_CODE);
		return customerCash;
	}

	@RequestMapping(method = RequestMethod.GET,value = "is-emp-applicable",produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<CustomerBasicInfo> getEmpDiscount(){
		LOG.info("Fetching customers applicable for EMP35");
		return customerService.getEmpDiscount();
	}

	@RequestMapping(method = RequestMethod.POST,value = "deactivate-emp-benefits",consumes = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
    @ResponseBody
	public Boolean deactivateEmpDiscount(@RequestBody List<Integer> customerIds ){
		LOG.info("Deactivating employee benefit of EMP35");
		return customerService.deactivateEmpDiscount(customerIds);
	}

	@RequestMapping(method = RequestMethod.POST, value = "deactivate-old-subscriptions", consumes = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public void deactivateOldSubscriptions() {
		LOG.info("Request to mark Old Subscription Plans and Events as IN_ACTIVE");
		try {
			customerService.deactivateOldSubscriptionPlans();
		} catch (Exception e) {
			LOG.error("Error in marking Subscription plans as IN_ACTIVE", e);
		}

		try {
			customerService.deactivateOldSubscriptionPlanEvents();
		} catch (Exception e) {
			LOG.error("Error in marking Subscription plan events as IN_ACTIVE", e);
		}
	}


	@RequestMapping(method = RequestMethod.GET, value = "get/winback/offer/dineIn",produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<OfferResponse> getWinBackOffers(){
		LOG.info("Request to Fetch DineIn Offer detail Data for winBack customers");
		return customerOfferService.getWinbackOffers();
	}

	@RequestMapping(method = RequestMethod.GET, value = "get/winback/offer/delivery",produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<DeliveryOfferDetailData> getWinBackOffersForDelivery(){
		LOG.info("Request to Fetch Delivery Offer detail Data for winBack customers");
		return customerOfferService.getWinbackOfferForDelivery();
	}

	@RequestMapping(method = RequestMethod.POST,value = "generate/winback/coupon",consumes = MediaType.APPLICATION_JSON,produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public CustomerWinbackOfferInfo generateWinbackCoupon(@RequestBody CustomerWinbackOfferInfoDomain domain){
		LOG.info("Request to generate  coupon for customer with contact number : {}",domain.getContactNumber());
		String contactNumber = AppUtils.getValidContactNUmber(domain.getContactNumber());
		domain.setContactNumber(contactNumber);
		Integer customerId = null;
		try {
			customerId = customerService.getCustomerId(contactNumber);
			if(Objects.isNull(customerId)){
				customerId = customerService.addNewCustomer(contactNumber,domain.getCustomerName(),AppConstants.WINBACK,AppConstants.CALL_CENTER);
			}
		}catch (Exception e){
			LOG.info("Error in getting Customer for contact number : {} and Error is : {}",contactNumber,e);
		}
		return customerOfferService.generateWinbackCoupon(domain,customerId);
	}

	@RequestMapping(method = RequestMethod.POST,value = "mark-notified",produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public CustomerWinbackOfferInfo markNotified(@RequestBody Integer winbackId){
		LOG.info("Request to Mark Notified winback info for id : {}",winbackId);
		return customerOfferService.markNotified(winbackId);
	}

	@RequestMapping(method = RequestMethod.GET,value = "get/winback/info",produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<CustomerWinbackOfferInfo> getWinbackInfo(){
		LOG.info("Request to fetch winback Info");
		return customerOfferService.getWinbackInfo();
	}

	@RequestMapping(method = RequestMethod.GET,value = "download/winback/sheet")
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public View getWinbackSheet(@RequestParam String startDate, @RequestParam String endDate){
		LOG.info("Request to downlaod Winback sheet data");
		if(StringUtils.isEmpty(startDate) || StringUtils.isEmpty(endDate)){
			return null;
		}
		return customerOfferService.getWinbackSheet(AppUtils.parseDate(startDate),AppUtils.parseDate(endDate));
	}

	@RequestMapping(method = RequestMethod.GET,value = "download/winback/sheet/all")
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public View getWinbackSheetBetweenStartandEndDate(){
		return customerOfferService.getWinbackSheet();
	}

	@RequestMapping(method = RequestMethod.POST, value = "partner-payment-coupon/apply", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public OfferOrder applyPartnerPaymentCoupon(@RequestBody final OfferOrder offerOrder) {
		try {
			if (AppUtils.CHAAYOS_CASH_OFFER_CODE.contains(offerOrder.getCouponCode())
					|| masterCache.getSubscriptionSkuCodeDetail().containsKey(offerOrder.getCouponCode().toUpperCase())) {
				String message = String.format("Cannot Apply Internal Coupons %s using this API",
						offerOrder.getCouponCode());
				LOG.error(message);
				offerOrder.setError(true);
				offerOrder.setErrorMessage(message);
				offerOrder.setErrorCode(WebErrorCode.INVALID_COUPON.getCode());
				offerOrder.setOrder(null);
				return offerOrder;
			}
//			 DREAMFOLKS validation logic
			if (properties.getPartnerPaymentOfferCode().equalsIgnoreCase(offerOrder.getCouponCode())) {
				if(!offerOrder.getDreamFolksVoucherDetails().isSkipReuseCheck() && Objects.nonNull(offerOrder.getDreamFolksVoucherDetails().getVoucherCode()) &&
						masterCache.getDreamFolksVoucherCodesUsed().contains(offerOrder.getDreamFolksVoucherDetails().getVoucherCode())){
					String message = "Voucher code already used.";
					LOG.error(message);
					offerOrder.setError(true);
					offerOrder.setErrorMessage(message);
					offerOrder.setErrorCode(WebErrorCode.INVALID_COUPON.getCode());
					offerOrder.setOrder(null);
					return offerOrder;
				}
				VoucherValidator validator = voucherValidatorFactory.getValidator(offerOrder.getCouponCode());
				boolean isValid = validator.validate(offerOrder);
				if (!isValid) {
					String message = "Invalid DreamFolks voucher code.";
					LOG.error(message);
					offerOrder.setError(true);
					offerOrder.setErrorMessage(message);
					offerOrder.setErrorCode(WebErrorCode.INVALID_COUPON.getCode());
					offerOrder.setOrder(null);
					return offerOrder;
				}
			}

			setCustomerDetail(offerOrder);
			return customerOfferService.applyCoupoun(offerOrder, null);
		} catch (OfferValidationException e) {
			LOG.error("Error while applying coupon", e);
			LOG.error(e.getMessage());
			offerOrder.setError(true);
			offerOrder.setErrorMessage(e.getMessage());
			offerOrder.setErrorCode(e.getErrorCode().getCode());
			offerOrder.setOrder(null);
		} catch (DataNotFoundException e) {
			LOG.error("Error while applying coupon", e);
			LOG.error(e.getMessage());
			offerOrder.setError(true);
			offerOrder.setErrorMessage(e.getMessage());
			offerOrder.setErrorCode(WebErrorCode.DATA_NOT_FOUND.getCode());
			offerOrder.setOrder(null);
		}
		return offerOrder;
	}
}