package com.stpl.tech.kettle.service.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class CustomerOrderRequest implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -3387873724675134395L;
	private Integer customerId;
	private Integer maxSize;
	private Integer noOfDays;
	private Integer orderId;
	private List<Integer> filteredIds;

	public Integer getCustomerId() {
		return customerId;
	}

	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}

	public Integer getMaxSize() {
		return maxSize;
	}

	public void setMaxSize(Integer maxSize) {
		this.maxSize = maxSize;
	}

	public Integer getNoOfDays() {
		return noOfDays;
	}

	public void setNoOfDays(Integer noOfDays) {
		this.noOfDays = noOfDays;
	}

	public List<Integer> getFilteredIds() {
		if (filteredIds == null) {
			filteredIds = new ArrayList<Integer>();
		}
		return filteredIds;
	}

	public void setFilteredIds(List<Integer> filteredIds) {
		this.filteredIds = filteredIds;
	}

	public Integer getOrderId() {
		return orderId;
	}

	public void setOrderId(Integer orderId) {
		this.orderId = orderId;
	}

	@Override
	public String toString() {
		return "CustomerOrderRequest [customerId=" + customerId + ", maxSize=" + maxSize + ", noOfDays=" + noOfDays
				+ ", orderId=" + orderId + ", filteredIds=" + filteredIds + "]";
	}

	
}
