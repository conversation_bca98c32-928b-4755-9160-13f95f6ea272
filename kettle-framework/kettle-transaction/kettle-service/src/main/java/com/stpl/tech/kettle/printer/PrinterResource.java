/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.printer;

import static com.stpl.tech.kettle.service.core.ServiceConstants.*;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.MediaType;

import com.stpl.tech.kettle.core.service.util.MessageSigner;

/**
 * Root resource (exposed at "pos-metadata" path)
 */
@Path(API_VERSION + SEPARATOR + PRINTER_ROOT_CONTEXT)
public class PrinterResource {

	@POST
	@Path("secure/url/for")
	@Produces(MediaType.TEXT_PLAIN)
	public String authorizePrinting(String request) {
		try {
			return MessageSigner.getInstance().sign(request);
		} catch (Exception e) {
			return null;
		}
	}

}
