/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.service.controller;

import static com.stpl.tech.kettle.service.core.ServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.service.core.ServiceConstants.DELIVERY_MANAGEMENT_ROOT_CONTEXT;
import static com.stpl.tech.kettle.service.core.ServiceConstants.SEPARATOR;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.MediaType;

import org.apache.commons.codec.DecoderException;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import com.stpl.tech.kettle.core.service.DeliveryRequestService;
import com.stpl.tech.kettle.core.service.OrderSearchService;
import com.stpl.tech.kettle.data.model.DeliveryDetail;
import com.stpl.tech.kettle.data.util.RequestSigner;
import com.stpl.tech.kettle.delivery.adapter.DTResponseAdapter;
import com.stpl.tech.kettle.delivery.adapter.GrabResponseAdapter;
import com.stpl.tech.kettle.delivery.adapter.HLDResponseAdapter;
import com.stpl.tech.kettle.delivery.adapter.OPNResponseAdapter;
import com.stpl.tech.kettle.delivery.adapter.SFXResponseAdapter;
import com.stpl.tech.kettle.delivery.adapter.DUNResponseAdapter;
import com.stpl.tech.kettle.delivery.model.DTCallback;
import com.stpl.tech.kettle.delivery.model.DUNCallbackObject;
import com.stpl.tech.kettle.delivery.model.DeliveryPartners;
import com.stpl.tech.kettle.delivery.model.DeliveryResponse;
import com.stpl.tech.kettle.delivery.model.GrabCallback;
import com.stpl.tech.kettle.delivery.model.HLDCallbackObject;
import com.stpl.tech.kettle.delivery.model.OPNCallbackObject;
import com.stpl.tech.kettle.delivery.model.SFXCallbackObject;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderFeedback;
import com.stpl.tech.master.core.PasswordImpl;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.SDPAllocationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.master.core.service.model.RequestData;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.util.TemplateRenderingException;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + DELIVERY_MANAGEMENT_ROOT_CONTEXT)
public class DeliveryManagementResources extends AbstractResources {

	private static final Logger LOG = LoggerFactory.getLogger(DeliveryManagementResources.class);

	@Autowired
	private OrderSearchService orderSearchService;

	@Autowired
	private MasterDataCache masterCache;

	@Autowired
	private DeliveryRequestService deliveryService;

	@RequestMapping(method = RequestMethod.GET, value = "refresh-attributes")
	@ResponseStatus(HttpStatus.OK)
	public void refreshAttributes() throws DataNotFoundException {
		deliveryService.refreshPartnerAttributes();
	}

    @RequestMapping(method = RequestMethod.GET, value = "refresh-priority-cache")
    @ResponseStatus(HttpStatus.OK)
    public void refreshPriorityCache() throws DataNotFoundException {
        deliveryService.refreshPriorityCache();
    }

	@RequestMapping(method = RequestMethod.POST, value = "cancel/{orderId}", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public DeliveryResponse cancelDelivery(@PathVariable String orderId)
			throws DataNotFoundException, TemplateRenderingException {
		Order order = orderSearchService.getOrderDetail(orderId);
		return deliveryService.cancelDeliveryRequest(orderId, order.getOrderId(), order.getUnitId());
	}

	@RequestMapping(method = RequestMethod.POST, value = "create-manual", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public DeliveryResponse createManualTicket(@RequestBody RequestData requestData)
			throws AuthenticationFailureException, DataNotFoundException, TemplateRenderingException,
			SDPAllocationException {
		LOG.info("Request to assign delivery detail manually : " + requestData.getData());
		DeliveryResponse detail = getData(requestData, DeliveryResponse.class);
		return deliveryService.saveManualDelivery(detail);
	}
	
	@RequestMapping(method = RequestMethod.POST, value = "resend-sms", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public HashMap<String, String> resendMsgToRider(@RequestBody DeliveryResponse response) throws DataNotFoundException,
			TemplateRenderingException, SDPAllocationException, AuthenticationFailureException {
		LOG.info("Request to resend msg to rider : " + response.getDeliveryBoyPhoneNum());
		return deliveryService.resendMsgToRider(response);
	}
	
	@RequestMapping(method = RequestMethod.POST, value = "default-delivery", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public HashMap<Integer, IdCodeName> defaultDeliveryPartner(@RequestBody String type){
		LOG.info("Request for defaultDeliveryPartner: " + type);
		return deliveryService.defaultDeliveryPartner(type);
	}

	@RequestMapping(method = RequestMethod.POST, value = "create-manual/android", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public DeliveryResponse createManualTicketForAndroid(@RequestBody DeliveryResponse detail)
			throws AuthenticationFailureException, DataNotFoundException, TemplateRenderingException,
			SDPAllocationException {
		LOG.info("Request to assign delivery detail manually : " + detail);
		return deliveryService.saveManualDelivery(detail);
	}

	@RequestMapping(method = { RequestMethod.PUT, RequestMethod.POST }, value = "update/{authorization}", consumes = {
			MediaType.APPLICATION_JSON, MediaType.APPLICATION_FORM_URLENCODED }, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public Integer updateDelivery(@PathVariable String authorization, HttpServletRequest request)
			throws AuthenticationFailureException, IOException, DataNotFoundException, DecoderException,
			TemplateRenderingException {
		DeliveryPartners deliveryPartner = null;
		if(authorization.equals("DUNvrcPJFEW1xeyaY1+D==")) {
			deliveryPartner = DeliveryPartners.valueOf("DUNZO");
		} else {
			deliveryPartner = DeliveryPartners.valueOf(PasswordImpl.decrypt(authorization));	
		}
		DeliveryResponse updateDelivery = null;
		LOG.info("authorization header is :::: {}", deliveryPartner.toString());
		String requestBody = IOUtils.toString(request.getInputStream());
		LOG.info("recorded request body is :::: {}", requestBody);
		updateDelivery = getCallBackObject(requestBody, deliveryPartner);
		return deliveryService.updateDelivery(updateDelivery);
	}

	@RequestMapping(method = RequestMethod.POST, value = "details", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public DeliveryDetail getDeliveryDetails(@RequestBody String generatedOrderId)
			throws AuthenticationFailureException {
		List<DeliveryDetail> details = deliveryService.getDeliveryDetails(generatedOrderId);
		return details != null && details.size() > 0 ? details.get(0) : null;
	}

	@RequestMapping(method = RequestMethod.GET, value = "regMerchant/{partnerId}/{unitId}", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public String regiterMerchant(@PathVariable Integer partnerId, @PathVariable int unitId)
			throws DataNotFoundException {
		return deliveryService.registerMerchant(masterCache.getUnit(unitId), partnerId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "orders-for-feedback", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<OrderFeedback> getOrderListForFeedback(@RequestBody String deliveryPersonContact)
			throws DataNotFoundException {
		return deliveryService.getOrderListForFeedback(deliveryPersonContact, "N");
	}

	@RequestMapping(method = RequestMethod.POST, value = "submit-order-feedback", consumes = MediaType.APPLICATION_JSON, produces = MediaType.TEXT_PLAIN)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public String submitOrderDeliveryFeedback(@RequestBody Map requestData) throws DataNotFoundException {
		int deliveryId = (int) requestData.get("deliveryId");
		String code = requestData.get("code").toString();
		return deliveryService.submitOrderDeliveryFeedback(deliveryId, code);
	}

	private DeliveryResponse getCallBackObject(String requestBody, DeliveryPartners deliveryPartner)
			throws AuthenticationFailureException, IOException, DecoderException {

		DeliveryResponse updateDelivery = null;
		switch (deliveryPartner) {
		case DELHIVERY_5: {
			HLDCallbackObject callback = WebServiceHelper.convert(requestBody, HLDCallbackObject.class);
			updateDelivery = new HLDResponseAdapter().adaptCallback(callback);
			break;
		}

		case OPINIO_4: {
			OPNCallbackObject callback = WebServiceHelper.convert(RequestSigner.decodeValues(requestBody),
					OPNCallbackObject.class);
			LOG.info("callback object after conversion :::: {}", callback);
			updateDelivery = new OPNResponseAdapter().adaptCallback(callback);
			break;
		}

		case ROAD_RUNNR:
			break;
		case SHADOW_FAX_3: {
			SFXCallbackObject callback = WebServiceHelper.convert(requestBody, SFXCallbackObject.class);
			updateDelivery = new SFXResponseAdapter().adaptCallback(callback);
			break;
		}

		case DELIVERY_TRACK_7: {
			DTCallback callback = WebServiceHelper.convert(requestBody, DTCallback.class);
			updateDelivery = new DTResponseAdapter().adaptCallback(callback);
			break;
		}

		case GRAB_9: {
			GrabCallback callback = WebServiceHelper.convert(requestBody, GrabCallback.class);
			updateDelivery = new GrabResponseAdapter().adaptCallback(callback);
			break;
		}
		
		case DUNZO: {
			DUNCallbackObject callback = WebServiceHelper.convert(requestBody, DUNCallbackObject.class);
			updateDelivery = new DUNResponseAdapter().adaptCallback(callback);
			break;
		}

		default:
			break;
		}
		return updateDelivery;
	}
}