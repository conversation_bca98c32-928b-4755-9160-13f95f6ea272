/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.service.model;

import java.util.Date;

public class SubscriptionEventUpdateData {

	private int subscriptionEventStatusId;
	private Date startDate;
	private Date endDate;
	private boolean changeWithImmediateEffect;
	private String reason;

	public int getSubscriptionEventStatusId() {
		return subscriptionEventStatusId;
	}

	public void setSubscriptionEventStatusId(int subscriptionEventStatusId) {
		this.subscriptionEventStatusId = subscriptionEventStatusId;
	}

	public Date getStartDate() {
		return startDate;
	}

	public void setStartDate(Date startDate) {
		this.startDate = startDate;
	}

	public Date getEndDate() {
		return endDate;
	}

	public void setEndDate(Date endDate) {
		this.endDate = endDate;
	}

	public boolean isChangeWithImmediateEffect() {
		return changeWithImmediateEffect;
	}

	public void setChangeWithImmediateEffect(boolean changeWithImmediateEffect) {
		this.changeWithImmediateEffect = changeWithImmediateEffect;
	}

	public String getReason() {
		return reason;
	}

	public void setReason(String reason) {
		this.reason = reason;
	}

}