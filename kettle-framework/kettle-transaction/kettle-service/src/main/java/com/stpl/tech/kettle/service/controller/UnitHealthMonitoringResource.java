/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.service.controller;

import static com.stpl.tech.kettle.service.core.ServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.service.core.ServiceConstants.HEALTH_MONITOR_ROOT_CONTEXT;
import static com.stpl.tech.kettle.service.core.ServiceConstants.SEPARATOR;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.ws.rs.core.MediaType;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import com.stpl.tech.kettle.core.cache.OrderInfoCache;
import com.stpl.tech.kettle.core.monitoring.UnitHealthCache;
import com.stpl.tech.kettle.core.monitoring.UnitMonitorData;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.service.model.UnitHealthData;
import com.stpl.tech.kettle.service.model.UnitHealthStatus;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.master.core.service.model.RequestData;
import com.stpl.tech.master.core.service.model.ScreenType;
import com.stpl.tech.master.core.service.model.UserSessionDetail;
import com.stpl.tech.master.domain.model.UnitStatus;

/**
 */
@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + HEALTH_MONITOR_ROOT_CONTEXT)

public class UnitHealthMonitoringResource extends AbstractResources {

	@Autowired
	private EnvironmentProperties props;
	@Autowired
	private UnitHealthCache healthCache;
	@Autowired
	private OrderInfoCache orderInfoCache;

	static final String HEALTH_CACHE_OBJECTS = "healthCache";
	static final String FAILED_MESSAGES_OBJECTS = "failedMessages";

	@RequestMapping(method = RequestMethod.POST, value = "unit/ping", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public Map<String, Integer> getUnitMetaData(@RequestBody UnitMonitorData unitMetaData)
			throws DataNotFoundException {
		Map<String, Integer> returnObj = new HashMap<String, Integer>();
		int failedMessageCount = 0;
		long delayedTime = System.currentTimeMillis() + props.getPingDelayTime();
		unitMetaData.setStartTime(delayedTime);
		returnObj.put(HEALTH_CACHE_OBJECTS, healthCache.addToCache(unitMetaData));

		if (unitMetaData.getScreenType().equals(ScreenType.ASSEMBLY)) {
			failedMessageCount = orderInfoCache.getFailedMessagesSize(unitMetaData.getUnitId());
		}
		returnObj.put(FAILED_MESSAGES_OBJECTS, failedMessageCount);
		return returnObj;
	}

	@RequestMapping(method = RequestMethod.POST, value = "unit/failed", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public Integer getFailedMessages(@RequestBody final RequestData request) throws DataNotFoundException {
		int failedMessageCount = 0;
		UserSessionDetail session = request.getSession();
		if (session != null && ScreenType.ASSEMBLY.name().equals(session.getScreenType())) {
			failedMessageCount = orderInfoCache.getFailedMessagesSize(session.getUnitId());
		}
		return failedMessageCount;
	}

	@RequestMapping(method = RequestMethod.GET, value = "units/status", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<UnitHealthStatus> getUnitsStatus() {
		List<UnitHealthStatus> list = new ArrayList<UnitHealthStatus>();
		Map<Integer, Map<UnitMonitorData, Integer>> map = healthCache.getUnitStatusData();
		for (Integer key : map.keySet()) {
			UnitHealthStatus status = new UnitHealthStatus();
			status.setUnitId(key);
			Map<UnitMonitorData, Integer> value = map.get(key);
			for (UnitMonitorData screenData : value.keySet()) {
				int count = value.get(screenData);
				status.setUnitName(screenData.getUnitName());
				status.getScreenStatus().add(convert(screenData, count));
				status.setUnitCategory(screenData.getCategory());
			}
			list.add(status);
		}
		return list;
	}

	@RequestMapping(method = RequestMethod.POST, value = "unit/status", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean getUnitStatus(@RequestBody final UserSessionDetail userSession) {
		return healthCache.isScreenActive(userSession.getUnitId(), userSession.getScreenType(),
				userSession.getTerminalId());
	}

	private UnitHealthData convert(UnitMonitorData data, int count) {
		UnitHealthData value = new UnitHealthData();
		value.setDetail(data);
		value.setStatus(count <= 0 ? UnitStatus.IN_ACTIVE : UnitStatus.ACTIVE);
		return value;
	}
}
