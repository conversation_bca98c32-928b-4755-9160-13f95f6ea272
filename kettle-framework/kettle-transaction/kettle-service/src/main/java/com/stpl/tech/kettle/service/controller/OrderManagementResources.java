/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.service.controller;

import com.google.common.base.Stopwatch;
import com.google.gson.Gson;
import com.hazelcast.map.IMap;
import com.stpl.tech.kettle.core.data.vo.DelayReason;
import com.stpl.tech.kettle.core.data.vo.ResendEmailRequestData;
import com.stpl.tech.kettle.core.exception.PaymentFailureException;
import com.stpl.tech.kettle.data.dao.TableDataDao;
import com.stpl.tech.kettle.domain.model.OrderRefund;
import com.stpl.tech.kettle.data.model.OrderStatusEvent;
import com.stpl.tech.kettle.data.model.UnitTableMappingDetail;
import com.stpl.tech.kettle.domain.model.TransitionStatus;
import com.stpl.tech.master.data.model.CouponDetailData;
import com.stpl.tech.master.data.model.RefLookupInfo;
import com.stpl.tech.master.data.model.UnitProductAsKey;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.ClientProtocolException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import com.stpl.tech.analytics.model.PartnerOrderRiderData;
import com.stpl.tech.kettle.commission.MonthlyAOVDetail;
import com.stpl.tech.kettle.commission.PartnerAOVRequest;
import com.stpl.tech.kettle.core.CampaignStrategy;
import com.stpl.tech.kettle.core.CashCardStatus;
import com.stpl.tech.kettle.core.OfferSource;
import com.stpl.tech.kettle.core.ReceiptType;
import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.core.cache.CampaignCache;
import com.stpl.tech.kettle.core.cache.MappingCache;
import com.stpl.tech.kettle.core.cache.MetadataCache;
import com.stpl.tech.kettle.core.cache.OrderInfoCache;
import com.stpl.tech.kettle.core.cache.OrderMappingCache;
import com.stpl.tech.kettle.core.cache.OrderUnitMapping;
import com.stpl.tech.kettle.core.cache.PartnerOrderConsiderationCache;
import com.stpl.tech.kettle.core.cache.UnitSessionCache;
import com.stpl.tech.kettle.core.cache.UnitSessionDetail;
import com.stpl.tech.kettle.core.cache.UnitTerminalDetail;
import com.stpl.tech.kettle.core.data.vo.CustomerCardInfo;
import com.stpl.tech.kettle.core.data.vo.CustomerCashInfo;
import com.stpl.tech.kettle.core.data.vo.FeedbackTokenInfo;
import com.stpl.tech.kettle.core.data.vo.GiftOffer;
import com.stpl.tech.kettle.core.data.vo.OrderFetchStrategy;
import com.stpl.tech.kettle.core.data.vo.PartnerDataWithOrderConsideration;
import com.stpl.tech.kettle.core.exception.CardValidationException;
import com.stpl.tech.kettle.core.notification.NewOrderNotification;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.core.notification.UnitOrder;
import com.stpl.tech.kettle.core.notification.UnitOrderOld;
import com.stpl.tech.kettle.core.notification.sms.CustomerSMSNotificationType;
import com.stpl.tech.kettle.core.service.CustomerOfferManagementService;
import com.stpl.tech.kettle.core.service.DeliveryRequestService;
import com.stpl.tech.kettle.core.service.EmployeeMealService;
import com.stpl.tech.kettle.core.service.GamifiedOfferService;
import com.stpl.tech.kettle.core.service.OrderManagementService;
import com.stpl.tech.kettle.core.service.OrderSearchService;
import com.stpl.tech.kettle.core.service.PaymentServiceNew;
import com.stpl.tech.kettle.core.service.PosMetadataService;
import com.stpl.tech.kettle.core.service.TableService;
import com.stpl.tech.kettle.core.service.UnitInventoryManagementService;
import com.stpl.tech.kettle.customer.service.CardService;
import com.stpl.tech.kettle.customer.service.CashBackService;
import com.stpl.tech.kettle.customer.service.CustomerService;
import com.stpl.tech.kettle.customer.service.FeedbackManagementService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.dao.TableDataDao;
import com.stpl.tech.kettle.data.model.CashCardDetail;
import com.stpl.tech.kettle.data.model.CashPacketData;
import com.stpl.tech.kettle.data.model.CustomerAdditionalDetail;
import com.stpl.tech.kettle.data.model.CustomerDetailType;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.CustomerMappingTypes;
import com.stpl.tech.kettle.data.model.GamifiedOfferRequest;
import com.stpl.tech.kettle.data.model.LoyaltyEvents;
import com.stpl.tech.kettle.data.model.OrderDetailForFeedback;
import com.stpl.tech.kettle.data.model.OrderInvoiceDetail;
import com.stpl.tech.kettle.data.model.OrderStatusEvent;
import com.stpl.tech.kettle.data.model.SpecialOfferRequest;
import com.stpl.tech.kettle.data.model.SpecialOfferResponse;
import com.stpl.tech.kettle.data.model.SpecialOfferType;
import com.stpl.tech.kettle.data.model.UnitTableMappingDetail;
import com.stpl.tech.kettle.delivery.model.DeliveryReason;
import com.stpl.tech.kettle.domain.model.ActionRequest;
import com.stpl.tech.kettle.domain.model.CashCard;
import com.stpl.tech.kettle.domain.model.CreateNextOfferRequest;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.DayCloseEstimateData;
import com.stpl.tech.kettle.domain.model.ExternalPartnerDetail;
import com.stpl.tech.kettle.domain.model.F9SalesRequest;
import com.stpl.tech.kettle.domain.model.MyOfferResponse;
import com.stpl.tech.kettle.domain.model.MyOfferResponseStatus;
import com.stpl.tech.kettle.domain.model.NextOffer;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderDetailTrim;
import com.stpl.tech.kettle.domain.model.OrderInAppUrlResponse;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.OrderItemCancellationRequest;
import com.stpl.tech.kettle.domain.model.OrderItemConsumable;
import com.stpl.tech.kettle.domain.model.OrderNotification;
import com.stpl.tech.kettle.domain.model.OrderPaymentDetailData;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.domain.model.OrderStatusDomain;
import com.stpl.tech.kettle.domain.model.Receipt;
import com.stpl.tech.kettle.domain.model.RequestInvoiceDetail;
import com.stpl.tech.kettle.domain.model.SubscriptionInfoDetail;
import com.stpl.tech.kettle.domain.model.TableResponse;
import com.stpl.tech.kettle.domain.model.TableSettlement;
import com.stpl.tech.kettle.domain.model.TableStatus;
import com.stpl.tech.kettle.domain.model.TestCampaignNotificationRequest;
import com.stpl.tech.kettle.domain.model.TransitionStatus;
import com.stpl.tech.kettle.domain.model.UnitClosure;
import com.stpl.tech.kettle.domain.model.UnitTableMapping;
import com.stpl.tech.kettle.domain.model.WalletEventData;
import com.stpl.tech.kettle.reports.dao.impl.ReportUtil;
import com.stpl.tech.kettle.reports.dao.impl.SettlementReportInputData;
import com.stpl.tech.kettle.reports.dao.impl.SettlementReportProvider;
import com.stpl.tech.kettle.reports.dao.impl.UnitConsumptionReportProvider;
import com.stpl.tech.kettle.reports.model.ProductDimensionConsumption;
import com.stpl.tech.kettle.reports.model.ProductPriceConsumption;
import com.stpl.tech.kettle.reports.model.ProductSourceConsumption;
import com.stpl.tech.kettle.reports.model.SettlementReport;
import com.stpl.tech.kettle.service.NonWastageItemService;
import com.stpl.tech.kettle.service.model.CustomerFeedbackResponse;
import com.stpl.tech.kettle.service.model.CustomerOrderRequest;
import com.stpl.tech.kettle.service.model.CustomerOrderResponse;
import com.stpl.tech.kettle.service.model.InventorySyncRequest;
import com.stpl.tech.kettle.service.model.OrderResponse;
import com.stpl.tech.kettle.service.model.OrderSettlementChangeRequest;
import com.stpl.tech.kettle.service.model.OrderSettlementData;
import com.stpl.tech.kettle.service.model.OrderStatusRequest;
import com.stpl.tech.kettle.service.model.ReprintResponse;
import com.stpl.tech.kettle.service.report.ItemConsumptionRawReceipt;
import com.stpl.tech.kettle.service.report.ItemConsumptionReceipt;
import com.stpl.tech.kettle.service.report.SettlementRawReceipt;
import com.stpl.tech.kettle.service.report.SettlementReceipt;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.exception.OfferValidationException;
import com.stpl.tech.master.core.external.acl.service.CSRFTokenService;
import com.stpl.tech.master.core.external.acl.service.TokenService;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.TaxDataCache;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.core.external.notification.service.FirebaseNotificationService;
import com.stpl.tech.master.core.external.offer.service.OfferManagementExternalService;
import com.stpl.tech.master.core.external.offer.service.OfferManagementService;
import com.stpl.tech.master.core.service.model.RequestData;
import com.stpl.tech.master.core.service.model.UserSessionDetail;
import com.stpl.tech.master.domain.model.CampaignDetail;
import com.stpl.tech.master.domain.model.CampaignDetailResponse;
import com.stpl.tech.master.domain.model.Consumable;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.IdNameValue;
import com.stpl.tech.master.domain.model.IdNameValueMap;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.domain.model.UnitStatus;
import com.stpl.tech.master.inventory.model.InventoryAction;
import com.stpl.tech.master.inventory.model.InventorySource;
import com.stpl.tech.master.inventory.model.ProductQuantityData;
import com.stpl.tech.master.inventory.model.QuantityRequestData;
import com.stpl.tech.master.inventory.model.QuantityResponseData;
import com.stpl.tech.master.payment.model.OrderPayment;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;
import com.stpl.tech.util.PrintType;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.endpoint.Endpoints;
import com.stpl.tech.util.notification.PrintTemplate;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.client.ClientProtocolException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.jms.JMSException;
import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.stpl.tech.kettle.service.core.ServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.service.core.ServiceConstants.ORDER_MANAGEMENT_ROOT_CONTEXT;
import static com.stpl.tech.kettle.service.core.ServiceConstants.SEPARATOR;

//import com.stpl.tech.kettle.core.service.impl.KettleSocketIOServer;

/**
 * Root resource (exposed at "order-management" path)
 */
@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + ORDER_MANAGEMENT_ROOT_CONTEXT) // v1/order-management
public class OrderManagementResources extends AbstractOrderResource {

    private static final Logger LOG = LoggerFactory.getLogger(OrderManagementResources.class);

    @Autowired
    private OrderManagementService orderService;

    @Autowired
    private OrderSearchService orderSearchService;

    @Autowired
    private CustomerService customerService;

    @Autowired
    private MetadataCache cache;

    @Autowired
    private MasterDataCache masterCache;

    @Autowired
    private TaxDataCache taxDataCache;

    @Autowired
    private EnvironmentProperties props;

    @Autowired
    private OrderInfoCache ordersCache;

    @Autowired
    private CampaignCache campaignCache;

    @Autowired
    private UnitInventoryManagementService unitService;

    @Autowired
    private DeliveryRequestService deliveryService;

    @Autowired
    private CustomerOfferManagementService offerService;

    @Autowired
    private OfferManagementExternalService offerExternalService;

    @Autowired
    private OfferManagementService offer;

    @Autowired
    private ItemConsumptionHelper itemConsumptionHelper;

    @Autowired
    private CardService cardService;

    @Autowired
    private FirebaseNotificationService fireBaseService;

    @Autowired
    private PaymentServiceNew paymentServiceNew;

    @Autowired
    private EmployeeMealService employeeMealService;

    @Autowired
    private TableService tableService;
    @Autowired
    private PartnerOrderConsiderationCache partnerOrderConsiderationCache;

    @Autowired
    private MappingCache mappingCache;

    @Autowired
    private CashBackService cashBackService;

    @Autowired
    private CSRFTokenService csrftokenService;

    @Autowired
    private FeedbackManagementService feedbackManagementService;

    @Autowired
    private TokenService<FeedbackTokenInfo> tokenService;

    @Autowired
    private PosMetadataService posMetadataService;

    @Autowired
    private GamifiedOfferService gamifiedOfferService;

    @Autowired
    private TableDataDao tableDataDao;

    @Autowired
    private NonWastageItemService nonWastageItemService;

    @RequestMapping(method = RequestMethod.POST, value = "order/unsettled-kettle-orders",consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<Integer> getUnsettledKettleOrders(@RequestBody List<Integer> ids) {
        LOG.info("Getting All Kettle Unsettled Orders Details");
        return orderService.unsettledKettleOrdersDetailList(ids);
    }
    @RequestMapping(method = RequestMethod.POST, value = "order/add-invoice-detail",consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public OrderInvoiceDetail setInvoiceDetail(@RequestBody RequestInvoiceDetail requestInvoiceDetail) {
        LOG.info("Setting order Invoice detail");
        return orderService.setInvoiceDetail(requestInvoiceDetail);
    }

    @RequestMapping(method = RequestMethod.GET, value = "order/sync-dinein-orders")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void syncDineinOrders() throws Exception {
        LOG.info("Entered in Order Status Update From Kettle To Dine Every Day 6am");
        try {
            LOG.info("Running cron job");
            String dineInEndPoint = props.getDineInCRMBaseUrl()  + "v2/cart/update-unsettled-orders-status";
            WebServiceHelper.postWithAuth(dineInEndPoint, props.getDineInToken(), null, Object.class);
        } catch (Exception e) {
            LOG.info("Exception: ", e.toString());
        }
    }

    /**
     * Method handling HTTP GET requests. The returned object will be sent to the
     * client as "text/plain" media type.
     *
     * @return String that will be returned as a text/plain response.
     * @throws DataNotFoundException
     * @throws AuthenticationFailureException
     */
    @RequestMapping(method = RequestMethod.POST, value = "order/generated-order", consumes = {
            MediaType.APPLICATION_JSON, MediaType.TEXT_PLAIN}, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Order getOrder(@RequestBody final String generatedOrderId) throws DataNotFoundException {
        LOG.info("Getting Order details for Generated Order ID = " + generatedOrderId);
        return orderSearchService.getOrderDetail(generatedOrderId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "order/order-id", consumes = {
            MediaType.APPLICATION_JSON, MediaType.TEXT_PLAIN}, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Order getOrder(@RequestBody final Integer orderId) throws DataNotFoundException {
        LOG.info("Getting Order details for Order ID = " + orderId);
        return orderSearchService.getOrderDetail(orderId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "order/customer-id", consumes = {
            MediaType.APPLICATION_JSON, MediaType.TEXT_PLAIN}, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Integer getCustomerId(@RequestBody final Integer orderId) throws DataNotFoundException {
        LOG.info("Getting Order customer id for Order ID = " + orderId);
        return orderSearchService.getCustomerId(orderId);
    }


    @RequestMapping(method = RequestMethod.POST, value = "order/partner-order", consumes = {MediaType.APPLICATION_JSON,
            MediaType.TEXT_PLAIN}, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Order getPartnerOrder(@RequestBody final IdCodeName idCodeName) throws DataNotFoundException {
        LOG.info("Getting Partner Order details for External Order ID = " + idCodeName.getCode());
        return orderSearchService.getPartnerOrderDetail(idCodeName.getCode(), idCodeName.getId());
    }

    @RequestMapping(method = RequestMethod.POST, value = "order/partner-order-status", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public OrderStatus getOrderStatusByPartnerOrderId(@RequestBody final IdCodeName idCodeName)
            throws DataNotFoundException, TemplateRenderingException, AuthenticationFailureException {
        LOG.info("Request to get order status by partner order id = " + idCodeName.getCode());
        return orderSearchService.getPartnerOrderDetail(idCodeName.getCode(), idCodeName.getId()).getStatus();
    }


    @RequestMapping(method = RequestMethod.POST, value = "order/partner-order-status-optimized", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public OrderStatus getOrderStatusByPartnerOrderIdOptimized(@RequestBody final IdCodeName idCodeName)
            throws DataNotFoundException, TemplateRenderingException, AuthenticationFailureException {
        LOG.info("Request to get order status by partner order id = " + idCodeName.getCode());
        return orderSearchService.getPartnerOrderStatus(idCodeName.getCode(), idCodeName.getId());
    }

    @RequestMapping(method = RequestMethod.POST, value = "android/generated-order", consumes = {
            MediaType.APPLICATION_JSON, MediaType.TEXT_PLAIN}, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public OrderInfo getAndroidOrder(@RequestBody final String generatedOrderId) throws DataNotFoundException {
        LOG.info("Getting Order details for Android Item for Generated Order ID = " + generatedOrderId);
        Order order = orderSearchService.getOrderDetail(generatedOrderId);
        Customer customer = customerService.getCustomer(order.getCustomerId());

        if (!customer.getAddresses().isEmpty()) {
            customer.getAddresses().addAll(customer.getAddresses().stream()
                    .filter(address -> new Integer(address.getId()).equals(order.getDeliveryAddress()))
                    .collect(Collectors.toList()));
        }

        OrderInfo orderInfo = new OrderInfo();
        orderInfo.setOrder(order);
        orderInfo.setCustomer(customer);
        return orderInfo;
    }

    /**
     * Method handling HTTP GET requests. The returned object will be sent to the
     * client as "text/plain" media type.
     *
     * @return String that will be returned as a text/plain response.
     * @throws AuthenticationFailureException
     * @throws DataNotFoundException
     * @throws DataUpdationException
     * @throws TemplateRenderingException
     * @throws CardValidationException
     */
    @RequestMapping(method = RequestMethod.POST, value = "order/cancel", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Boolean cancelOrder(@RequestBody final RequestData order, @RequestParam(required = false) boolean isAdminRequest) throws AuthenticationFailureException,
            DataUpdationException, DataNotFoundException, TemplateRenderingException, CardValidationException {
        LOG.info("Cancel order for Generated Order ID = " + order.getData());
        ActionRequest request = getData(order, ActionRequest.class);

        if(Objects.nonNull(request.getUnitId()) && Boolean.TRUE.equals(masterCache.getUnit(request.getUnitId()).getIsTestingUnit())) {
            // if the unit is a testing unit then mark wastage as NO_WASTAGE.
            request.setBookWastage(AppConstants.NO_WASTAGE);
        }
        //validate(order.getSession());
        boolean deleted = false;
        LOG.info("Unit Category ::: " + request.getUnitCategory());
        OrderInfo info = orderSearchService.getOrderReceipt(request.getGeneratedOrderId());
        if(hasPartnerPaymentOffer(info.getOrder())){
            throw new DataUpdationException(
                    "Order cannot be cancelled as it is DreamFolks offer Order");
        }
        if(orderService.isOrderContainHoldItem(info)){
            throw new DataUpdationException(
                    "Order cannot be cancelled as it contain Hold Items");
        }
        Unit unit = masterCache.getUnit(info.getOrder().getUnitId());
        validateCancellation(request, info, isAdminRequest);
        boolean hasGiftCard = hasGiftCard(info.getOrder());
        boolean hasSelectOrder = hasSelectOrder(info.getOrder());
        Integer tableRequestId = info.getOrder().getTableRequestId();
        if(Objects.nonNull(tableRequestId)){
            if(isSettledTabledOrder(tableRequestId,info)){
                throw new DataUpdationException(
                        "Order cannot be cancelled as it was already settled");
            }
        }
        BigDecimal frequencyUsed = BigDecimal.ZERO;
        for (com.stpl.tech.kettle.domain.model.OrderItem orderItem : info.getOrder().getOrders()){
            if(Objects.nonNull(orderItem.getDiscountDetail().getDiscountReason()) &&
                    orderItem.getDiscountDetail().getDiscountReason().equals(AppConstants.CHAI_PREPAID)){
                frequencyUsed = AppUtils.add(frequencyUsed,AppUtils.divide(
                        AppUtils.subtract(
                                AppUtils.multiply(orderItem.getPrice(),
                                        BigDecimal.valueOf(orderItem.getQuantity())),orderItem.getAmount()),orderItem.getPrice()));
            }
        }
        if(BigDecimal.ZERO.compareTo(frequencyUsed) < 0){
            throw new DataUpdationException("Order cannot be cancelled as " + AppConstants.CHAI_PREPAID + " was used for discount");
        }
        if (hasSelectOrder) {
            if (info.getOrder().getChannelPartner() == AppConstants.CHAAYOS_DINEIN_PARTNER_ID|| info.getOrder().getChannelPartner() == AppConstants.DINE_IN_CHANNEL_PARTNER_CODE || info.getOrder().getChannelPartner() == AppConstants.WEB_APP_CHANNEL_PARTNER_CODE) {
                boolean isSubscriptionCancelled = cancelSubscription(info.getOrder());
                if (!isSubscriptionCancelled) {
                    throw new DataUpdationException(
                            "Order cannot be cancelled as overall savings of customer is more than 0");
                }

            }
        }
        if (Objects.nonNull(info.getOrder().getOfferCode()) && getMasterDataCache().getSubscriptionSkuCodeDetail().containsKey(info.getOrder().getOfferCode())) {
            getOrderManagementService().revertSubscriptionSaving(info.getCustomer().getId(),
                    info.getOrder().getTransactionDetail().getSavings());
        }
        else if (Objects.nonNull(info.getOrder().getOfferCode())) {  //Reverting coupon code for the customer for re-use
            CouponDetailData couponDetailData = getOrderManagementService().getCustomerCouponMapping(info.getOrder());
            if(Objects.nonNull(couponDetailData)){
                offerService.updateCustomerCouponOfferDetail(couponDetailData,info.getOrder().getCustomerId(),info.getOrder().getOrderId());
            }
        }
        try {
            if (isRoutedToAssembly(unit, info.getOrder(), hasGiftCard, hasSelectOrder)) {
                LOG.info("inside if clause of cancel order");
                if (orderSearchService.getLastDayCloseOrderId(request.getUnitId()) < request.getOrderId()) {
                    if (request.getOrderStatus() == null) {
                        request.setOrderStatus(OrderStatus.CANCELLED_REQUESTED);
                    }
                    deleted = updateOrderStatus(request, order.getSession());

                    if (deleted) {
                        if (UnitCategory.COD.equals(request.getUnitCategory())) {
                            deliveryService.cancelDeliveryRequest(request.getGeneratedOrderId(), request.getOrderId(),
                                    request.getUnitId());
                        }

//                        if (TransactionUtils.isWebDineIn(request.getOrderSource().name(),
//                                request.getChannelPartner())) {
                        if (info != null && info.getOrder() != null
                                && TransactionUtils.isOnlinePayment(info.getOrder().getSettlements())) {
                            try {
                                paymentServiceNew.refundPayment(request.getOrderId());
                            } catch (Exception e) {
                                String message = "Error while refunding payment during cancelling order on DINE IN from Web App for OrderID :::: "
                                        + request.getOrderId();
                                LOG.error(message, e);
                                SlackNotificationService.getInstance().sendNotification(props.getEnvironmentType(),
                                        "Kettle", null,
                                        SlackNotification.REFUNDS.getChannel(props.getEnvironmentType()), message);
                            }

                            if (info.getOrder().getChannelPartner() == AppConstants.DINE_IN_CHANNEL_PARTNER_CODE ||
                                    info.getOrder().getChannelPartner() == AppConstants.BAZAAR_PARTNER_ID ||
                                        info.getOrder().getChannelPartner() == AppConstants.WEB_APP_CHANNEL_PARTNER_CODE) {
                                orderService.postOrderCancellationUpdateToDineInServer(info.getOrder().getOrderId(),
                                        info.getOrder().getCustomerId(), info.getOrder().getChannelPartner(),request.getOrderSource());
                            }
                        } else if (info != null && info.getOrder() != null) {
                            Order tableOrder = info.getOrder();
                            
                            if (TransactionUtils.isTableOrder(tableOrder)) {
                                Integer orderId = tableOrder.getOrderId();
                                Integer customerId = tableOrder.getCustomerId();
                                Integer partnerId = tableOrder.getChannelPartner();
                                UnitCategory orderSource = request != null ? request.getOrderSource() : null;
                                
                                orderService.postOrderCancellationUpdateToDineInServer(
                                        orderId,
                                        customerId,
                                        partnerId,
                                        orderSource
                                );
                            }
                        }
                        
                        if(!isAdminRequest){
                            pushOrderNotification(info);
                        }
//                        }
                    }
                }
            } else {
                deleted = orderService.deleteOrder(info.getOrder().getUnitId(), request.getGeneratedOrderId(),
                        order.getSession().getUserId(), request.getApprovedBy(), request.getReason(),
                        request.getReasonId(), request.getWastageType());
            }

            if (deleted) {
                //orderService.resetOverallFrequency(info.getOrder());
                sendCashCardRefundNotification(info);
                OrderInfo orderData = orderSearchService.getOrderReceipt(request.getGeneratedOrderId());
                if (!TransactionUtils.isTableOrder(orderData.getOrder())) {
                    publish(orderData);
                }
                markNPSfeedbackCancelled(info.getOrder().getOrderId());
                updateTableData(info.getOrder().getTableRequestId());
            }
            // Publish to Web App for syncing with Kettle

            // add to cache
            // inactivate cash back
            orderService.invalidateCashBack(info.getOrder());
            if (OrderStatus.CANCELLED_REQUESTED.equals(info.getOrder().getStatus())) {
                ordersCache.addToCache(info);
            }
        } catch (Exception e) {
            LOG.error("Error while cancelling order, for request {}", JSONSerializer.toJSON(request), e);
        }
        orderService.pushDataToThirdPartyAnalytics(info, true);
        //Removing coupon usage from cache of the cancelled order
        if(Objects.nonNull(info) && Objects.nonNull(info.getOrder())){
            orderService.removingFromCacheAfterValidationFailure(info.getOrder());
        }

        return deleted;
    }


    @RequestMapping(method = RequestMethod.POST, value = "order/item/cancel", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Boolean cancelOrderItem(HttpServletRequest request , @RequestBody final OrderItemCancellationRequest orderItemCancellationRequest)
            throws DataNotFoundException, DataUpdationException, TemplateRenderingException, CardValidationException, AuthenticationFailureException {
          LOG.info("Request to cancel Order Item : {}  , order Id : {} , table request Id : {} " , orderItemCancellationRequest.getOrderItemId(),
                  orderItemCancellationRequest.getOrderId() , orderItemCancellationRequest.getTableRequestId());
          Integer userId = getLoggedInUser(request);
          Integer orderItemId = orderItemCancellationRequest.getOrderItemId();
        LOG.info("Request to cancel Order Item : {}  , order Id : {} , table request Id : {} , User Id : {} " , orderItemCancellationRequest.getOrderItemId(),
                orderItemCancellationRequest.getOrderId() , orderItemCancellationRequest.getTableRequestId() , userId);
          //orderService.createOrderItemStatusEvent(orderItemCancellationRequest,OrderStatus.CREATED.value(),OrderStatus.CANCELLED.value(),
        //         OrderStatus.INITIATED.value(), userId);
          Order order = getOrderSearchService().getOrderDetail(orderItemCancellationRequest.getGeneratedOrderId());
          validateItemForCancellation(orderItemCancellationRequest,order);
          Boolean isCancelled = orderService.cancelOrderItem(orderItemCancellationRequest,userId, order);
          //LOG.info("Order Item Cancelled : {} ::::: {} ", orderItemCancellationRequest.getOrderItemId(),isCancelled);
          if(Boolean.FALSE.equals(isCancelled)){
              return isCancelled;
          }
          Order updatedOrder = getOrderSearchService().getOrderDetail(orderItemCancellationRequest.getGeneratedOrderId());
          if(Boolean.TRUE.equals(isCancelled)){
              updateTableData(orderItemCancellationRequest.getTableRequestId());
          }
          LOG.info("Order Items :::: {} ", new Gson().toJson(order.getOrders()));
          if(updatedOrder.getOrders().size() == 0) {
              try{
                  order.setStatus(OrderStatus.CANCELLED_REQUESTED);
                  ActionRequest actionRequest = ActionRequest.builder().orderId(order.getOrderId()).orderSource(UnitCategory.valueOf(order.getSource()))
                          .generatedOrderId(order.getGenerateOrderId()).reason("Table Order All Items Cancelled!!").channelPartner(order.getChannelPartner())
                          .unitId(order.getUnitId()).orderStatus(order.getStatus()).approvedBy(orderItemCancellationRequest.getCancelledBy())
                          .build();
                  RequestData requestData = new RequestData();
                  requestData.setData(JSONSerializer.toJSON(actionRequest));
                  UserSessionDetail userSessionDetail = new UserSessionDetail(orderItemCancellationRequest.getCancelledBy(),
                          order.getUnitId(),1);
                  requestData.setSession(userSessionDetail);
                  cancelOrder(requestData,true);
              }catch (Exception e){
                  LOG.error("Error While Cancelling Whole Order After all  Order Items Are Cancelled");
              }
          }
          try {
            if(Boolean.TRUE.equals(orderItemCancellationRequest.getWastage())){
                    LOG.info("Calculating item Consumption for Generated Order ID For Item Cancellation :: {} with order Item Id  ::::: {} ",
                            orderItemCancellationRequest.getGeneratedOrderId(), orderItemId);
                    /*  Order order = getOrderSearchService().getOrderDetail(orderItemCancellationRequest.getGeneratedOrderId());*/
                    List<OrderItem> orderItemList = order.getOrders().stream().filter(od -> od.getItemId() ==
                            orderItemId).collect(Collectors.toList());
                    order.setOrders(orderItemList);
                    checkBookWastage(order);
                    bookWastage(order);
            }
         }catch (Exception e){
            LOG.error("Error While Verifying wastage for Order Item Id ::::: {} ",orderItemId);
        }
          return true;
    }

    @RequestMapping(method = RequestMethod.POST, value = "order/refund", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public OrderPayment refundOrder(@RequestParam(required = false) String partnerTransactionId) throws AuthenticationFailureException,            DataUpdationException, DataNotFoundException, TemplateRenderingException, CardValidationException {
        LOG.info("Refund order for Partner Transaction ID = " + partnerTransactionId);
        try {
            return paymentServiceNew.refundDoublePayments(partnerTransactionId);
        } catch (IOException | PaymentFailureException e) {
            throw new RuntimeException(e);
        }
    }

    @RequestMapping(method = RequestMethod.GET, value = "service-charge/refund/get", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<OrderRefund> getRefundServiceChargeOrder(@RequestParam String startDate, @RequestParam Integer unitId) throws DataNotFoundException {
        LOG.info("Refund Unit Id = " + unitId);
        try {
            Date businessDate = startDate != null ? AppUtils.getDate(AppUtils.parseDateIST(startDate))
                    : AppUtils.getCurrentBusinessDate();
            return orderService.getServiceChargeRefundOrder(unitId, businessDate);
        } catch (DataNotFoundException e) {
            throw new RuntimeException(e);
        }
    }

    private int getQuantityOfNonWastageItem(Map<Integer,Integer> nonWastageItemMap,int itemId){
        if(Objects.nonNull(nonWastageItemMap) || !nonWastageItemMap.containsKey(itemId)){
            return 0;
        }
        return nonWastageItemMap.get(itemId);
    }

    private void updateTableData(Integer tableRequestId) {
        if (tableRequestId == null) {
            return;
        }
        tableService.refreshTableSummary(tableRequestId);
    }

    private void markNPSfeedbackCancelled(Integer orderId) {
        try {
            orderService.markNPSfeedbackCancelled(orderId);
        } catch (Exception e) {
            LOG.error("ERROR While cancelling NPS", e);
        }
    }

    private Order validateItemForCancellation(OrderItemCancellationRequest orderItemCancellationRequest, Order order) throws DataUpdationException,
            DataNotFoundException, TemplateRenderingException {
        UnitTableMapping tableMapping =   tableService.getTableSummary(orderItemCancellationRequest.getTableRequestId());
        if(tableMapping.getTableStatus().equals(TableStatus.CLOSED)){
            throw new DataUpdationException("Table is Already Closed!!");
        }

        if (Objects.nonNull(order) && org.apache.commons.lang3.StringUtils.isNotBlank(order.getOfferCode())
                && order.getOfferCode().equals(AppConstants.LOYAL_TEA_OFFER_CODE)) {
            String m = "Order Item #" + orderItemCancellationRequest.getOrderItemId()
                    + " cannot be cancelled as its Order contains LOYALTEA redemption.";
            LOG.info(m);
            throw new DataUpdationException(m);
        }

        if(hasPartnerPaymentOffer(order)){
            throw new DataUpdationException(
                    "Order Item cannot be cancelled as it is DreamFolks offer Order");
        }

        return order;
    }



        private void validateCancellation(ActionRequest request, OrderInfo info, boolean isAdmin)
            throws DataUpdationException, DataNotFoundException {
        if (Objects.nonNull(info.getOrder()) && StringUtils.isNotBlank(info.getOrder().getOfferCode())
                && info.getOrder().getOfferCode().equals(AppConstants.LOYAL_TEA_OFFER_CODE)) {
            String m = "Order #" + info.getOrder().getGenerateOrderId()
                    + " cannot be cancelled as it contains LOYALTEA redemption.";
            LOG.info(m);
            throw new DataUpdationException(m);
        }
        if (request.getWastageType() != null && !request.getWastageType().equals("NO_WASTAGE")) {
            checkBookWastage(request.getGeneratedOrderId());
        }
        boolean hasGiftCards = false;
        for (com.stpl.tech.kettle.domain.model.OrderItem orderItem : info.getOrder().getOrders()) {
            if (AppUtils.isGiftCard(orderItem.getCode())) {
                hasGiftCards = true;
            }
        }
        if (hasGiftCards) {
            boolean hasCardBeenUsed = false;
            List<CashCardDetail> cards = cardService.getCardsByPurchaseOrderId(info.getOrder().getOrderId());
            if (cards != null) {
                for (CashCardDetail card : cards) {
                    if ((card.getCardStatus().equals(CashCardStatus.ACTIVE.name())
                            && AppUtils.add(card.getCashInitialAmount(), card.getInitialOffer())
                            .compareTo(card.getCashPendingAmount())!=0)
                            || (!card.getCardStatus().equals(CashCardStatus.ACTIVE.name())
                            && !card.getCardStatus().equals(CashCardStatus.READY_FOR_ACTIVATION.name()))) {
                        hasCardBeenUsed = true;
                    }
                }
                if (hasCardBeenUsed) {
                    String m = "Order #" + info.getOrder().getGenerateOrderId()
                            + " has gift cards that have already been used and hence cannot be cancelled.";
                    LOG.info(m);
                    throw new DataUpdationException(m);
                }
            }
        }
        if(!isAdmin) {
            String timeLimitDelivery = Objects.nonNull(getMasterDataCache().getBrandMetaData().get(info.getBrand().getBrandId()).getDeliveryOrderCancellationTime()) ?
                               getMasterDataCache().getBrandMetaData().get(info.getBrand().getBrandId()).getDeliveryOrderCancellationTime() :
                               getEnvironmentProperties().getDeliveryOrderCancellationTime();
            String timeLimitDineIn = Objects.nonNull(getMasterDataCache().getBrandMetaData().get(info.getBrand().getBrandId()).getDineInOrderCancellationTime()) ?
                               getMasterDataCache().getBrandMetaData().get(info.getBrand().getBrandId()).getDineInOrderCancellationTime() :
                               getEnvironmentProperties().getDineInOrderCancellationTime();
            if ((request.getNoTimeConstraint() == null || !request.getNoTimeConstraint())
                    && TransactionUtils.isCODOrder(info.getOrder().getSource())
                    && AppUtils.getSecondsDiff(AppUtils.getCurrentTimestamp(),
                    info.getOrder().getBillingServerTime()) > Integer.valueOf(timeLimitDelivery)) {
                String m = "Order #" + info.getOrder().getGenerateOrderId()
                        + " is delivery and cannot be cancelled after " + (Integer.valueOf(timeLimitDelivery)/60)  + " minutes.";
                LOG.info(m);
                throw new DataUpdationException(m);
            } else if ((request.getNoTimeConstraint() == null || !request.getNoTimeConstraint())
                    && !TransactionUtils.isCODOrder(info.getOrder().getSource())
                    && AppUtils.getSecondsDiff(AppUtils.getCurrentTimestamp(),
                    info.getOrder().getBillingServerTime()) > Integer.valueOf(timeLimitDineIn)) {
                String m = "Order #" + info.getOrder().getGenerateOrderId()
                        + " is dine in and cannot be cancelled after " +  (Integer.valueOf(timeLimitDineIn)/60)  + " minutes.";
                LOG.info(m);
                throw new DataUpdationException(m);
            }
        }
    }

    /**
     * Method handling HTTP GET requests. The returned object will be sent to the
     * client as "text/plain" media type.
     *
     * @return String that will be returned as a text/plain response.
     * @throws AuthenticationFailureException
     * @throws DataNotFoundException
     * @throws DataUpdationException
     * @throws TemplateRenderingException
     */
    @RequestMapping(method = RequestMethod.POST, value = "order/change", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Boolean changeSettlementMode(@RequestBody final OrderSettlementChangeRequest request)
            throws AuthenticationFailureException, DataUpdationException, DataNotFoundException,
            TemplateRenderingException {
        LOG.info("Settlement Change Request for order = " + request.getOrderId());
        List<Pair<Integer, Integer>> settlements = new ArrayList<>();
        for (OrderSettlementData data : request.getDetails()) {
            settlements.add(new Pair<Integer, Integer>(data.getOrderSettlementId(), data.getPaymentModeId()));
        }
        return orderService.changeSettlementMode(request.getUnitId(), request.getOrderId(), request.getEditedBy(),
                settlements, request.getOrderSource());
    }

    /**
     * Method handling HTTP POST requests. The returned object will be sent to the
     * client as "text/plain" media type.
     *
     * @return String that will be returned as a text/plain response.
     * @throws Exception
     */
    @RequestMapping(method = RequestMethod.POST, value = "order/updateStatus", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Boolean updateOrderStatus(@RequestBody final RequestData codOrder) throws Exception {
        Stopwatch watch = Stopwatch.createUnstarted();
        watch.start();
        LOG.info("Update status for Generated Order ID = " + codOrder.getData());
        ActionRequest request = getData(codOrder, ActionRequest.class);
        UserSessionDetail userSession = codOrder.getSession();
        validate(userSession);
        boolean result = updateOrderStatus(request, userSession);
        LOG.info("Status update for order {} is {}", request.getOrderId(), result);
        if (result) {
            OrderInfo order = ordersCache.getOrderById(request.getOrderId(), request.getOrderSource(), false);
            boolean isDelivery = TransactionUtils.isCODOrder(request.getOrderSource().name());
            Integer partnerId = null;
            if (order == null) {
                LOG.info("###NOT FOUND ORDER IN CACHE for {}", request);
                partnerId = orderSearchService.getChannelPartnerId(request.getOrderId());
            } else {
                LOG.info("### FOUND ORDER IN CACHE for {} and customer id {}", request,
                        order.getOrder().getCustomerId());
                partnerId = order.getOrder().getChannelPartner();
            }
            orderService.postUpdateActions(request.getOrderId(),
                    order != null && order.getOrder().getCustomerId() > 5 ? order.getOrder().getCustomerId() : null,
                    partnerId, isDelivery, request.getOrderStatus(),request.getOrderSource());
            if (OrderStatus.CANCELLED.equals(request.getOrderStatus())){
                order = orderSearchService.getOrderReceipt(request.getOrderId(),true,null);
                if(Objects.nonNull(order)){
                    bookWastageAndPublishInventory(order);
                }
            }
        }
        System.out.println("######### , STEP Full, - , Update Order Status ----------,"
                + watch.stop().elapsed(TimeUnit.MILLISECONDS));

        return result;
    }

    /**
     * TO check payment status of specific cart id
     *
     * @return true is payment is successfull otherwise false
     */
    @RequestMapping(method = RequestMethod.POST, value = "order/getPaymentStatus", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Boolean checkPaymentStatusOfOrder(@RequestBody String cartId) {
        return orderService.getPaymentStatus(cartId);
    }

    /**
     * Method handling HTTP POST requests. The returned object will be sent to the
     * client as "text/plain" media type.
     *
     * @return String that will be returned as a text/plain response.
     * @throws Exception
     */
    @RequestMapping(method = RequestMethod.POST, value = "order/updateStatus/android", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Boolean updateOrderStatusFromAssembly(@RequestBody final OrderStatusRequest request) throws Exception {
        long time = System.currentTimeMillis();
        boolean result = orderService.updateOrderStatusInCache(request.getOrderId(), request.getOrderStatus(),
                request.getUserId(), request.getUnitId(), request.getOrderSource(), null, false, null, null);
        LOG.info("Status update for order {} is {}", request.getOrderId(), result);
        if (result) {
            OrderInfo order = ordersCache.getOrderById(request.getOrderId(), request.getOrderSource(), false);
            if (order == null) {
                LOG.info("###NOT FOUND ORDER IN CACHE for {}", request);
            } else {
                LOG.info("### FOUND ORDER IN CACHE for {} and customer id {}", request, order.getOrder().getCustomerId());
            }
            orderService.postUpdateActions(request.getOrderId(),
                    order != null && order.getOrder().getCustomerId() > 5 ? order.getOrder().getCustomerId() : null,
                    order != null ? order.getOrder().getChannelPartner() : null, TransactionUtils.isCODOrder(request.getOrderSource().name()),
                    request.getOrderStatus(),request.getOrderSource());
            if (OrderStatus.CANCELLED.equals(request.getOrderStatus())){
                order = orderSearchService.getOrderReceipt(request.getOrderId(),true,null);
                if(Objects.nonNull(order)){
                    order.getOrder().setNonWastageItemMap(request.getNonWastageItemMap());
                    nonWastageItemService.saveNonWastageItems(order.getOrder());  // Fixed: Pass order.getOrder() instead of order
                    bookWastageAndPublishInventory(order);
                }
            }
        } else {
            try {
                LOG.info("updateOrderStatusInCache FROM ASSEMBLY returned false for Order Id : {}", request.getOrderId());
                if (request.getOrderSource() != null && UnitCategory.COD.equals(request.getOrderSource())) {
                    List<OrderStatusEvent> lastOrderTransactionEvent = orderService.getLastOrderTransactionEvent(request.getOrderId());
                    if (!CollectionUtils.isEmpty(lastOrderTransactionEvent)) {
                        OrderStatusEvent orderStatusEvent = lastOrderTransactionEvent.get(0);
                        if (OrderStatus.SETTLED.name().equalsIgnoreCase(orderStatusEvent.getFromStatus())
                                && OrderStatus.SETTLED.name().equalsIgnoreCase(orderStatusEvent.getToStatus()) &&
                                TransitionStatus.FAILURE.name().equals(orderStatusEvent.getTransitionStatus())) {
                            boolean isRemoved = getOrderInfoCache().removeFromCache(request.getUnitId(), request.getOrderId());
                            LOG.info("found SUCCESS EVENT returning TRUE to assembly and Tried to removed from cache too FOR ORDER ID :  {} and removal status : {}", request.getOrderId(), isRemoved);
                            return true;
                        }
                    }
                }
            } catch (Exception e) {
                LOG.error("Exception Occurred While Verifying Order Status Event to remove for assembly for Order Id : {}", request.getOrderId(), e);
            }
        }
        LOG.info("######### , STEP Full, - , Update Order Status From Android---------- {}ms",System.currentTimeMillis() - time);
        return result;
    }

    private void bookWastageAndPublishInventory(OrderInfo info){
        if (info.getOrder().getWastageType() != null && !info.getOrder().getWastageType().equals("NO_WASTAGE")) {
            // add wastage
            bookWastage(info.getOrder());
        }
        // add inventory
        updateUnitInventory(info.getOrder(), info.getOrder().getOrderId(), true);
    }

    /**
     * This Method updates Cache and Removes Orders. generatedBy and requestedBy is
     * same and reason would be null in this case CANCELLED and SETTLED orders are
     * removed from the cache. CANCELLED_REQUESTED orders are updated in cache.
     *
     * @param request
     * @param userSession
     * @return true if processed correctly else false
     * @throws Exception
     */
    private boolean updateOrderStatus(ActionRequest request, UserSessionDetail userSession) throws Exception {
        if (request.getOrderStatus().equals(OrderStatus.CANCELLED_REQUESTED) && request.getWastageType() != null
                && !request.getWastageType().equals("NO_WASTAGE")) {
            checkBookWastage(request.getGeneratedOrderId());
        }
        boolean result = orderService.updateOrderStatusInCache(request.getOrderId(), request.getOrderStatus(),
                userSession.getUserId(), request.getUnitId(), request.getOrderSource(), request.getReason(),
                request.isRefund(), request.getReasonId(), request.getWastageType());
        return result;
    }

    /**
     * Method handling HTTP GET requests. The returned object will be sent to the
     * client as "text/plain" media type.
     *
     * @return String that will be returned as a text/plain response.
     * @throws AuthenticationFailureException
     * @throws DataUpdationException
     * @throws TemplateRenderingException
     * @throws DataNotFoundException
     */
    @RequestMapping(method = RequestMethod.POST, value = "order/validateGiftCards", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<CashCard> validateGiftCardsInOrder(@RequestBody final List<CashCard> cashCards)
            throws AuthenticationFailureException, DataNotFoundException, DataIntegrityViolationException,
            CardValidationException {
        LOG.info("Validating gift cards in order " + JSONSerializer.toJSON(cashCards));
        try {
            return orderSearchService.validateGiftCards(cashCards);
        } catch (Exception e) {
            LOG.error("Error While Validating gift cards in order", e);
            throw e;
        }
    }

    /**
     * Method handling HTTP GET requests. The returned object will be sent to the
     * client as "text/plain" media type.
     *
     * @return String that will be returned as a text/plain response.
     * @throws AuthenticationFailureException
     * @throws DataUpdationException
     * @throws TemplateRenderingException
     * @throws DataNotFoundException
     */
    @RequestMapping(method = RequestMethod.POST, value = "order/create", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public OrderResponse createOrder(@RequestBody final Order request)
            throws AuthenticationFailureException, DataUpdationException, TemplateRenderingException,
            DataNotFoundException, DataIntegrityViolationException, CardValidationException, IOException, JMSException {

        OrderInfo info = null;
        System.out.println(request.getSourceId());
        //Revalidating
        if(!orderService.revalidatingCreateOrder(request)){
            throw new CardValidationException("Order Validation Failed");
        }
        try {
            info = createCurrentOrder(request, 0, true, false);
        } catch (Exception e) {
            LOG.error("Error While Creating order", e);
            orderService.removingFromCacheAfterValidationFailure(request);
            throw e;
        }

        OrderResponse orderResponse= new OrderResponse(info.getOrder().getOrderId(),info.getOrder().getGenerateOrderId(), info.isBillIncludedInReceipts(),
                info.getReceipts(), info.getAdditionalReceipts() , info.getNewCards(), info.getPrintType(), info.getOrder().getBillingServerTime());
       orderResponse.setOrderId(info.getOrder().getOrderId());
        if(Objects.nonNull(info.getNextOffer())){
            orderResponse.setNextOffer(info.getNextOffer());
        }if(Objects.nonNull(info.getNextDeliveryOffer())){
            orderResponse.setNextDeliveryOffer(info.getNextDeliveryOffer());
        }
        return orderResponse;
    }

//    @RequestMapping(method = RequestMethod.POST, value = "order/create/app", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
//    @ResponseStatus(HttpStatus.OK)
//    @ResponseBody
//    public OrderResponse createAppOrder(@RequestBody final Order request)
//            throws AuthenticationFailureException, DataUpdationException, TemplateRenderingException,
//            DataNotFoundException, DataIntegrityViolationException, CardValidationException {
//
//        LOG.info("Creating order " + JSONSerializer.toJSON(request));
//        OrderInfo info = null;
//        try {
//            info = createCurrentOrder(request, 0, true, false);
//        } catch (Exception e) {
//            LOG.error("Error While Creating order", e);
//            throw e;
//        }
//
//        return new OrderResponse(info.getOrder().getGenerateOrderId(), info.isBillIncludedInReceipts(),
//                info.getReceipts(), info.getNewCards(), info.getPrintType(), info.getOrder().getBillingServerTime());
//    }

    @RequestMapping(method = RequestMethod.POST, value = "order/create/app", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Order createAppOrder(@RequestBody final Order request, @RequestParam(required = false) Boolean addMetadata)
            throws AuthenticationFailureException, DataUpdationException, TemplateRenderingException,
            DataNotFoundException, DataIntegrityViolationException, CardValidationException, IOException, JMSException {

        OrderInfo info = null;
        if (addMetadata == null) {
            addMetadata = false;
        }
        if (request.getBrandId() == null) {
            request.setBrandId(AppConstants.CHAAYOS_BRAND_ID); //TODO remove this on kiosk and neo deployment
        }
        if(!orderService.revalidatingCreateOrder(request)){
            return null;
        }
        try {
            info = createCurrentOrder(request, 0, true, addMetadata);
        } catch (Exception e) {
            LOG.error("Error While Creating order", e);
            orderService.removingFromCacheAfterValidationFailure(request);
            throw e;
        }
        Order order = info.getOrder();
        if(Objects.nonNull(info.getNextOffer())){
            order.setNextOffer(info.getNextOffer());
        }
        return order;
    }

    @RequestMapping(method = RequestMethod.GET, value = "order/get-generated-order-id", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public String getGeneratedOrderId(@RequestParam final int unitId, @RequestParam final int terminalId,
                                      @RequestParam final String orderSource, @RequestParam final int employeeId,
                                      @RequestParam final String randomString) {
        OrderUnitMapping orderUnitMapping = new OrderUnitMapping(unitId, terminalId, orderSource, employeeId);
        return OrderMappingCache.getInstance().get(orderUnitMapping, randomString);
    }

    /**
     * Method handling HTTP GET requests. The returned object will be sent to the
     * client as "text/plain" media type.
     *
     * @return String that will be returned as a text/plain response.
     * @throws AuthenticationFailureException
     * @throws DataUpdationException
     * @throws TemplateRenderingException
     * @throws DataNotFoundException
     */
    @RequestMapping(method = RequestMethod.POST, value = "wastage/create", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public  List<ReprintResponse> createWastage(@RequestBody final Order order)
            throws AuthenticationFailureException, DataUpdationException, TemplateRenderingException,
            DataNotFoundException, DataIntegrityViolationException, CardValidationException {
        checkBookWastage(order);
        bookWastage(order);
        updateUnitInventory(order, null, false);
        publishInventory(order, InventoryAction.REMOVE, InventorySource.WASTAGE);
        List<ReprintResponse> reprintResponses = new ArrayList<>();

        List<String> receiptList = TransactionUtils.getWastageKOTReceipts(props.getChaayosBaseUrl(), masterCache, true, order, props.getBasePath(),  props.getRawPrintingSatus(),props);
        for (String str : receiptList) {
            ReprintResponse response = new ReprintResponse();
            response.setPrintString(str);
            if (props.getRawPrintingSatus()) {
                response.setPrintType(PrintType.RAW);
            } else {
                response.setPrintType(PrintType.HTML);
            }
            reprintResponses.add(response);
        }
        return reprintResponses;
    }

    /**
     * Method handling HTTP GET requests. The returned object will be sent to the
     * client as "text/plain" media type.
     *
     * @return String that will be returned as a text/plain response.
     * @throws AuthenticationFailureException
     * @throws DataUpdationException
     * @throws TemplateRenderingException
     * @throws DataNotFoundException
     */
    @RequestMapping(method = RequestMethod.POST, value = "order/create/web", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Order createWebOrder(@RequestBody final Order request, @RequestParam(required = false) Boolean addMetadata)
            throws AuthenticationFailureException, DataUpdationException, TemplateRenderingException,
            DataNotFoundException, DataIntegrityViolationException, CardValidationException, IOException, JMSException {

        OrderInfo info = null;
        if (addMetadata == null) {
            addMetadata = false;
        }
        if (request.getBrandId() == null) {
            request.setBrandId(AppConstants.CHAAYOS_BRAND_ID); //TODO remove this on kiosk and neo deployment
        }
        if(!orderService.revalidatingCreateOrder(request)){
            return null;
        }
        try {
            info = createCurrentOrder(request, 0, true, addMetadata);
        } catch (Exception e) {
            LOG.error("Error While Creating order", e);
            orderService.removingFromCacheAfterValidationFailure(request);
            throw e;
//            info.getOrder().setStatus(OrderStatus.);
        }
        return info.getOrder();
    }

    @RequestMapping(method = RequestMethod.GET, value = "clear-sku-definition-list-cache")
    public void clearAllSubscriptionDetail(){
        mappingCache.removeAllSubscription();
    }

    @RequestMapping(method = RequestMethod.POST, value = "order/create/noah")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public String createNoahOrder(@RequestBody final String request)
            throws AuthenticationFailureException, DataUpdationException, TemplateRenderingException,
            DataNotFoundException, DataIntegrityViolationException, CardValidationException, IOException, JMSException {
        LOG.info("Creating order " + request);
        Order order = JSONSerializer.toJSON(request, Order.class);
        OrderInfo info = null;
        if (order.getBrandId() == null) {
            order.setBrandId(AppConstants.CHAAYOS_BRAND_ID); //TODO remove this on kiosk and neo deployment
        }
        if(!orderService.revalidatingCreateOrder(order)){
            throw new CardValidationException("Order Validation Failed");
        }
        try {
            info = createCurrentOrder(order, 0, false, false);
        } catch (Exception e) {
            LOG.error("Error While Creating order", e);
            orderService.removingFromCacheAfterValidationFailure(order);
            throw e;
        }
        return info.getOrder().getGenerateOrderId();
    }

    private OrderInfo createCurrentOrder(Order request, int retryCount, boolean includeReceipts, boolean addMetadata)
            throws DataIntegrityViolationException, DataUpdationException, DataNotFoundException,
            TemplateRenderingException, CardValidationException, IOException, JMSException {

        OrderInfo info = null;
        OrderNotification orderNotification =OrderNotification.builder().build();

        try {
            info = createOrder(request, includeReceipts, addMetadata, orderNotification);
            if(!StringUtils.isEmpty(request.getPartnerCusId()) && AppConstants.COD.equals(request.getSource())){
                info.setPartnerCustomerId(request.getPartnerCusId());
                orderService.pushDataToCleverTapForPartner(info,false);
            }else {
                orderService.pushDataToThirdPartyAnalytics(info, false);
            }
            orderService.publishCustomerTransactionViewEvent(info.getCustomer().getId(), info.getOrder().getBrandId(), info.getOrder().getOrderId());
        } catch (DataIntegrityViolationException | IOException | JMSException e) {
            LOG.error("Error While Creating order", e);
            LOG.info("Recreating order, retry count : {}", retryCount);
            if (retryCount < 5) {
                request.setGenerateOrderId(AppUtils.generateRandomOrderId());
                info = createCurrentOrder(request, retryCount + 1, includeReceipts, addMetadata);
            } else {
                throw e;
            }
        }
        return info;
    }

    /**
     * Method handling HTTP GET requests. The returned object will be sent to the
     * client as "text/plain" media type.
     *
     * @return String that will be returned as a text/plain response.
     * @throws AuthenticationFailureException
     * @throws DataUpdationException
     * @throws TemplateRenderingException
     * @throws DataNotFoundException
     */
    @RequestMapping(method = RequestMethod.POST, value = "android/order/create", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public OrderInfo createOrderFromAndroid(@RequestBody final Order request) throws AuthenticationFailureException,
            DataUpdationException, TemplateRenderingException, DataNotFoundException, CardValidationException, IOException, JMSException {
        // validate(request.getSession());
        return createOrder(request, false, false,null);
    }


    /**
     * Method handling HTTP GET requests. The returned object will be sent to the
     * client as "text/plain" media type.
     *
     * @return String that will be returned as a text/plain response.
     * @throws AuthenticationFailureException
     * @throws DataUpdationException
     * @throws TemplateRenderingException
     * @throws DataNotFoundException
     */
    @RequestMapping(method = RequestMethod.POST, value = "customer/next-offer", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public NextOffer createNextOffer(@RequestBody final CreateNextOfferRequest request)
            throws DataUpdationException, DataNotFoundException {
        // validate(request.getSession());
        LOG.info("Creating next offer for " + JSONSerializer.toJSON(request));
        Customer customer = null;
        if (request.getCustomerId() != null) {
            customer = getCustomerService().getCustomer(request.getCustomerId());
        } else if (request.getContactNumber() != null) {
            customer = getCustomerService().getCustomer(request.getContactNumber());
        } else {
            throw new DataUpdationException(String.format(
                    "Cannot create next offer for customer as the customer id and contact number both are missing %s",
                    request));
        }
        //Fetch Applicable region and Applicable Unit from the campaign
        NextOffer nextOffer =  createNextOffer(request.getCampaignId(), null, request.getBrandId(), null,customer, null, null,request, null,false);
        return enrichNextOffer(nextOffer, request.getCampaignId());

    }

    @RequestMapping(method = RequestMethod.POST, value = "my-offer-wa", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<MyOfferResponse> getMyOfferWa(@RequestParam String contactNumber,@RequestParam String campaignToken,
                                              @RequestParam String utmSource,@RequestParam String utmedium)
            throws DataUpdationException, DataNotFoundException, AuthenticationFailureException {
        // validate(request.getSession());
//        validateRequest(request.getAuthToken(),true);
        CreateNextOfferRequest request = new CreateNextOfferRequest();
        request.setCampaignToken(campaignToken);
        contactNumber = contactNumber.substring(2);
        request.setContactNumber(contactNumber);
        request.setUtmMedium(utmedium);
        request.setUtmSource(utmSource);
        request.setBrandId(1);
        LOG.info("Creating next offer for " + JSONSerializer.toJSON(request));
        MyOfferResponse myOfferResponse = new MyOfferResponse();
        List<MyOfferResponse> myOfferResponses = new ArrayList<>();
        List<Integer> campaignDetailIds = new ArrayList<>();
        Customer customer = null;
        if (request.getCustomerId() != null) {
            LOG.info("Getting customer detail for customer id :: {}",request.getCustomerId());
            customer = getCustomerService().getCustomer(request.getCustomerId());
        } else if (request.getContactNumber() != null) {
            LOG.info("Getting customer detail for customer number :: {}",request.getContactNumber());
            customer = getCustomerService().getCustomer(request.getContactNumber());
        } else {
            throw new DataUpdationException(String.format(
                    "Cannot create any offer for customer as the customer id and contact number both are missing %s",
                    request));
        }

        if(Objects.nonNull(request.getCampaignToken())){
            request.setCampaignId(campaignCache.getCampaignByToken(request.getCampaignToken(), AppConstants.ACTIVE).getCampaignId());
        }
        if(!Objects.isNull(customer)){
            myOfferResponse.setCustomerName(customer.getFirstName());
        }
        campaignDetailIds.add(request.getCampaignId());
        List<Integer> detailList = campaignCache.getLinkedCampaigns(request.getCampaignId());
        if(Objects.nonNull(detailList) && detailList.size()>0){
            campaignDetailIds.addAll(detailList);
        }
        for(Integer campaignId : campaignDetailIds){
            CreateNextOfferRequest offerRequest = new CreateNextOfferRequest(customer.getId(),customer.getContactNumber(),
                    request.getBrandId(),campaignId,request.getUtmSource(),request.getUtmMedium());
            MyOfferResponse response = getLinkedOffer(offerRequest,customer);
            Boolean isAlreadyAdded = false;
            for(MyOfferResponse res : myOfferResponses){
                if(res.getCampaignId().equals(response.getCampaignId())){
                    isAlreadyAdded = true;
                    break;
                }
            }
            if(!isAlreadyAdded){
                myOfferResponses.add(response);
            }
        }
        return myOfferResponses;
    }

    @RequestMapping(method = RequestMethod.POST, value = "my-offer", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<MyOfferResponse> getMyOffer(@RequestBody final CreateNextOfferRequest request)
            throws DataUpdationException, DataNotFoundException, AuthenticationFailureException {
        // validate(request.getSession());
        validateRequest(request.getAuthToken(),true);
        LOG.info("Creating next offer for " + JSONSerializer.toJSON(request));
        MyOfferResponse myOfferResponse = new MyOfferResponse();
        List<MyOfferResponse> myOfferResponses = new ArrayList<>();
        List<Integer> campaignDetailIds = new ArrayList<>();
        Customer customer = null;
        if (request.getCustomerId() != null) {
            LOG.info("Getting customer detail for customer id :: {}",request.getCustomerId());
            customer = getCustomerService().getCustomer(request.getCustomerId());
        } else if (request.getContactNumber() != null) {
            LOG.info("Getting customer detail for customer number :: {}",request.getContactNumber());
            customer = getCustomerService().getCustomer(request.getContactNumber());
        } else {
            throw new DataUpdationException(String.format(
                    "Cannot create any offer for customer as the customer id and contact number both are missing %s",
                    request));
        }

        if(Objects.nonNull(request.getCampaignToken())){
            request.setCampaignId(campaignCache.getCampaignByToken(request.getCampaignToken(), AppConstants.ACTIVE).getCampaignId());
        }
        if(!Objects.isNull(customer)){
            myOfferResponse.setCustomerName(customer.getFirstName());
        }
        campaignDetailIds.add(request.getCampaignId());
        List<Integer> detailList = campaignCache.getLinkedCampaigns(request.getCampaignId());
        if(Objects.nonNull(detailList) && detailList.size()>0){
            campaignDetailIds.addAll(detailList);
        }
        for(Integer campaignId : campaignDetailIds){
            CreateNextOfferRequest offerRequest = new CreateNextOfferRequest(customer.getId(),customer.getContactNumber(),
                    request.getBrandId(),campaignId,request.getUtmSource(),request.getUtmMedium());
            MyOfferResponse response = getLinkedOffer(offerRequest,customer);
            Boolean isAlreadyAdded = false;
            for(MyOfferResponse res : myOfferResponses){
                if(res.getCampaignId().equals(response.getCampaignId())){
                    isAlreadyAdded = true;
                    break;
                }
            }
            if(!isAlreadyAdded){
                myOfferResponses.add(response);
            }
        }
        return myOfferResponses;
    }

    @RequestMapping(method = RequestMethod.POST, value = "test-campaign-notification", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    private boolean sendTestSMSForOffer(@RequestBody TestCampaignNotificationRequest request) throws JMSException, IOException {
        return orderService.testNotification(request);
    }

    private MyOfferResponse getMyOfferDNBO(CreateNextOfferRequest request, MyOfferResponse myOfferResponse, Customer customer) {
        NextOffer nextOffer =  createDNBOOffer(request.getCampaignId(), -1, request.getBrandId(), null, customer, null, null, request, null, false);
        if(Objects.nonNull(nextOffer) && Objects.nonNull(nextOffer.getOfferCode())){
            myOfferResponse.setCustomerName(nextOffer.getFirstName());
            myOfferResponse.setOfferDesc(nextOffer.getText());
            myOfferResponse.setOfferCouponCode(nextOffer.getOfferCode());
            myOfferResponse.setRedirectionUrl("https://cafes.chaayos.com");
            myOfferResponse.setCampaignId(request.getCampaignId());
            myOfferResponse.setContentUrl(nextOffer.getContentUrl());
            myOfferResponse.setValidityFrom(nextOffer.getValidityFrom());
            myOfferResponse.setValidityTill(nextOffer.getValidityTill());
            myOfferResponse.setChannelPartnerId(nextOffer.getChannelPartnerId());
            if(Objects.nonNull(nextOffer.getExistingOffer())){
                myOfferResponse.setExistingOffer(nextOffer.getExistingOffer());
            }else{
                myOfferResponse.setExistingOffer(false);
            }
            myOfferResponse.setOfferStatus(MyOfferResponseStatus.OFFER_FOUND.name());
        }else{
            myOfferResponse.setCustomerName(customer.getFirstName());
            myOfferResponse.setRedirectionUrl("https://cafes.chaayos.com");
            myOfferResponse.setCampaignId(request.getCampaignId());
            myOfferResponse.setOfferStatus(MyOfferResponseStatus.NO_OFFER_FOUND.name());
            myOfferResponse.setExistingOffer(false);
        }
        return myOfferResponse;
    }

    private MyOfferResponse getLinkedOffer(CreateNextOfferRequest request,Customer customer){
        LOG.info("Fetching Campaign Detail for campaign Id ::: {}",request.getCampaignId());
        CampaignDetail campaignDetail = campaignCache.getCampaign(request.getCampaignId());
        MyOfferResponse myOfferResponse = new MyOfferResponse();
        LOG.info("Campaign Detail for campaign Id {} is :::: {}",request.getCampaignId(), JSONSerializer.toJSON(campaignDetail));
        if(CampaignStrategy.NBO.name().equals(campaignDetail.getCampaignStrategy())){
            LOG.info("Creating next best offer as campaign strategy is :: {}",campaignDetail.getCampaignStrategy());
            return getMyOfferNBO(request, myOfferResponse, customer);
        }else if(CampaignStrategy.DELIVERY_NBO.name().equals(campaignDetail.getCampaignStrategy())){
            LOG.info("Creating delivery next best offer as campaign strategy is :: {}",campaignDetail.getCampaignStrategy());
            return getMyOfferDNBO(request, myOfferResponse, customer);
        }else if(CampaignStrategy.GENERAL.name().equals(campaignDetail.getCampaignStrategy()) ||
                CampaignStrategy.CAFE_LAUNCH.name().equals(campaignDetail.getCampaignStrategy())){
            LOG.info("Creating general offer as campaign strategy is :: {}",campaignDetail.getCampaignStrategy());
            return orderService.createGeneralOffer(request, customer,campaignDetail, 0).getKey();
        }else if(CampaignStrategy.DELIVERY_GENERAL.name().equals(campaignDetail.getCampaignStrategy())){
            LOG.info("Creating delivery general offer as campaign strategy is :: {}",campaignDetail.getCampaignStrategy());
            return orderService.createDeliveryGeneralOffer(request, customer,campaignDetail, 0).getKey();
        }else{
            LOG.info("Skipping offer creation as no valid strategy found for campaign id :: {}, campaign strategy :: {}",
                    campaignDetail.getCampaignId(),campaignDetail.getCampaignStrategy());
            return orderService.noOfferResponse(customer);
        }
    }

    public MyOfferResponse getMyOfferNBO(CreateNextOfferRequest request, MyOfferResponse myOfferResponse, Customer customer){
        NextOffer nextOffer =  createNextOffer(request.getCampaignId(), -1, request.getBrandId(), null,customer, null, null,request, null, false);
        if(Objects.nonNull(nextOffer) && Objects.nonNull(nextOffer.getOfferCode())){
            myOfferResponse.setCustomerName(nextOffer.getFirstName());
            myOfferResponse.setOfferDesc(nextOffer.getText());
            myOfferResponse.setOfferCouponCode(nextOffer.getOfferCode());
            myOfferResponse.setRedirectionUrl("https://cafes.chaayos.com");
            myOfferResponse.setCampaignId(request.getCampaignId());
            myOfferResponse.setContentUrl(nextOffer.getContentUrl());
            myOfferResponse.setValidityFrom(nextOffer.getValidityFrom());
            myOfferResponse.setValidityTill(nextOffer.getValidityTill());
            myOfferResponse.setChannelPartnerId(nextOffer.getChannelPartnerId());
            if(Objects.nonNull(nextOffer.getExistingOffer())){
                myOfferResponse.setExistingOffer(nextOffer.getExistingOffer());
            }else{
                myOfferResponse.setExistingOffer(false);
            }
            myOfferResponse.setOfferStatus(MyOfferResponseStatus.OFFER_FOUND.name());
        }else {
            myOfferResponse.setCustomerName(customer.getFirstName());
            myOfferResponse.setRedirectionUrl("https://cafes.chaayos.com");
            myOfferResponse.setCampaignId(request.getCampaignId());
            myOfferResponse.setOfferStatus(MyOfferResponseStatus.NO_OFFER_FOUND.name());
            myOfferResponse.setExistingOffer(false);
        }
        return myOfferResponse;
    }

    private NextOffer enrichNextOffer(NextOffer nextOffer, Integer campaignId) {
        return nextOffer;
    }

    /**
     * Method handling HTTP GET requests. The returned object will be sent to the
     * client as "text/plain" media type.
     *
     * @return String that will be returned as a text/plain response.
     * @throws AuthenticationFailureException
     */
    @RequestMapping(method = RequestMethod.POST, value = "order/enquiry/create", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean createOrderEnquiry(@RequestBody final RequestData request) throws AuthenticationFailureException {
        validate(request.getSession());
        LOG.info("Saving order enquiry " + request.getData());
        Order order = getData(request, Order.class);
        return orderService.createOrderEnquiry(order);
    }

    /**
     * Method handling HTTP GET requests. The returned object will be sent to the
     * client as "text/plain" media type.
     *
     * @return String that will be returned as a text/plain response.
     * @throws DataNotFoundException
     * @throws TemplateRenderingException
     * @throws AuthenticationFailureException
     */
    @RequestMapping(method = RequestMethod.POST, value = "order/reprint-order", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public ReprintResponse reprintOrder(@RequestBody final RequestData requestData)
            throws DataNotFoundException, TemplateRenderingException, AuthenticationFailureException {
        LOG.info("Reprint Order Request for Generated Order ID = " + requestData.getData());
        validate(requestData.getSession());
        ActionRequest request = getData(requestData, ActionRequest.class);
        OrderInfo info = orderSearchService.getOrderReceipt(request.getGeneratedOrderId());
        ReprintResponse response = new ReprintResponse();
        PrintTemplate receipt = TransactionUtils.getReceiptObject(props.getChaayosBaseUrl(), masterCache, ReceiptType.ORDER_CAFE_PRINT, info,
                props.getBasePath(), props.getRawPrintingSatus(),props);
        receipt.setReprint();
        String printString = receipt.getContent();
        response.setPrintString(printString);
        if (props.getRawPrintingSatus()) {
            response.setPrintType(PrintType.RAW);
        } else {
            response.setPrintType(PrintType.HTML);
        }
        orderService.addReprintRequest(info.getOrder().getOrderId(), requestData.getSession().getUserId(),
                request.getApprovedBy(), request.getReason());
        return response;
    }

    /**
     * Method handling HTTP GET requests. The returned object will be sent to the
     * client as "text/plain" media type.
     *
     * @return String that will be returned as a text/plain response.
     * @throws DataNotFoundException
     * @throws TemplateRenderingException
     * @throws AuthenticationFailureException
     */
    @RequestMapping(method = RequestMethod.POST, value = "order/email-order", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean emailOrder(@RequestBody final ResendEmailRequestData requestData)
            throws DataNotFoundException, TemplateRenderingException, AuthenticationFailureException {
        LOG.info("Resend Email Order Request for Order ID = " + requestData.getData().getOrderId());
        validate(requestData.getSession());
            if(!AppConstants.EXCLUDE_CUSTOMER_IDS.contains(requestData.getData().getCustomerId())) {
                return orderService.resendEmail(requestData.getData());
            }
            else {
                LOG.info("Unable to send email for orderId: {} as it is for customerId :{}",requestData.getData().getOrderId(),requestData.getData().getCustomerId());
            }
         return false;
    }

    /**
     * Method handling HTTP GET requests. The returned object will be sent to the
     * client as "text/plain" media type.
     *
     * @return String that will be returned as a text/plain response.
     */
    @RequestMapping(method = RequestMethod.POST, value = "order/session", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public UnitSessionDetail unitSession(@RequestBody final UserSessionDetail userSession) {
        LOG.info("Getting unit session object  for the   = " + userSession.getUserId());
        return UnitSessionCache.getInstance()
                .get(new UnitTerminalDetail(userSession.getUnitId(), userSession.getTerminalId()));
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/report/settlement", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public RequestData generateSettlementReport(@RequestBody final UserSessionDetail userSession)
            throws DataNotFoundException, TemplateRenderingException {
        LOG.info("Generating settlement report for the day for unit :" + userSession.getUnitId());
        OrderFetchStrategy strategy = new OrderFetchStrategy(true, false, false, false, false, null, -1, true);
        List<Order> orders = orderSearchService.getOrderDetails(userSession.getUnitId(),
                orderSearchService.getLastDayCloseOrderId(userSession.getUnitId()), Integer.MAX_VALUE, strategy);
        return getSetlementReportData(userSession, orders);
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/report/settlement/terminal", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public RequestData generateSettlementReportForATerminal(@RequestBody final UserSessionDetail userSession)
            throws DataNotFoundException, TemplateRenderingException {
        LOG.info("Generating settlement report for the day for unit :" + userSession.getUnitId() + "terminal id :"
                + userSession.getTerminalId());
        OrderFetchStrategy strategy = new OrderFetchStrategy(true, false, false, false, false, null,
                userSession.getTerminalId(), true);
        List<Order> orders = orderSearchService.getOrderDetails(userSession.getUnitId(),
                orderSearchService.getLastDayCloseOrderId(userSession.getUnitId()), Integer.MAX_VALUE, strategy);
        return getSetlementReportData(userSession, orders);
    }

    private RequestData getSetlementReportData(UserSessionDetail userSession, List<Order> orders)
            throws TemplateRenderingException {
        int unitId = userSession.getUnitId();
        Unit unit = masterCache.getUnit(unitId);
        SettlementReportInputData data = new SettlementReportInputData(AppUtils.getCurrentDate(), unit, orders, false,
                null, null,null);
        SettlementReportProvider settlementProvider = new SettlementReportProvider(masterCache, cache);
        settlementProvider.process(data,
                new TreeSet<>(taxDataCache.getSaleTaxations().get(unit.getLocation().getState().getId())));
        Collection<SettlementReport> products = settlementProvider.getByPaymentType();
        PrintTemplate reportRenderer = null;
        if (props.getRawPrintingSatus()) {
            reportRenderer = new SettlementRawReceipt(props.getBasePath(), unit, orders.size(), products);
        } else {
            reportRenderer = new SettlementReceipt(props.getBasePath(), unit, orders.size(), products);
        }
        RequestData response = new RequestData(userSession, reportRenderer.getContent());
        return response;
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/report/consumption/item", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public RequestData generateConsumptionReport(@RequestBody final UserSessionDetail userSession)
            throws DataNotFoundException, TemplateRenderingException {
        LOG.info("Generating item consumption report for the day for unit :" + userSession.getUnitId());
        int unitId = userSession.getUnitId();
        Unit unit = masterCache.getUnit(unitId);
        OrderFetchStrategy strategy = new OrderFetchStrategy(false, true, false, false, false, null, -1, true);
        List<Order> orders = orderSearchService.getOrderDetails(unitId,
                orderSearchService.getLastDayCloseOrderId(unitId), Integer.MAX_VALUE, strategy);
        SettlementReportInputData data = new SettlementReportInputData(AppUtils.getCurrentDate(), unit, orders, false,
                null, null,null);
        UnitConsumptionReportProvider consumptionProvider = new UnitConsumptionReportProvider(masterCache, cache);
        consumptionProvider.process(data);
        List<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>> products = ReportUtil
                .getConsumptions(consumptionProvider);
        PrintTemplate reportRenderer = null;
        if (props.getRawPrintingSatus()) {
            reportRenderer = new ItemConsumptionRawReceipt(props.getBasePath(), unit, products);
        } else {
            reportRenderer = new ItemConsumptionReceipt(props.getBasePath(), unit, products);
        }
        RequestData response = new RequestData(userSession, reportRenderer.getContent());
        return response;
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/orders", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public SettlementReportInputData getOrdersForTheDay(@RequestBody final UserSessionDetail userSession)
            throws DataNotFoundException, TemplateRenderingException {
        LOG.info("Getting orders for the day for unit :" + userSession.getUnitId());
        int unitId = 0;
        SettlementReportInputData data;
        try {
            unitId = userSession.getUnitId();
            OrderFetchStrategy strategy = new OrderFetchStrategy(true, false, false, false, false, null, -1, true);
            List<Order> orders = orderSearchService.getOrderDetails(unitId, orderSearchService.getLastDayCloseOrderId(unitId), Integer.MAX_VALUE, strategy);
            data = new SettlementReportInputData(null, null, orders, false, null, null,null);
        } catch (Exception e) {
            LOG.error("Error while getting Order Summary for unit {}", unitId, e);
            throw e;
        }
        return data;
    }

    @RequestMapping(method = RequestMethod.POST,value = "unit/orders/v1", consumes = MediaType.APPLICATION_JSON,produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public SettlementReportInputData getOrdersForTheDayPageable(@RequestBody final UserSessionDetail userSession,
                                                                @RequestParam(required = false) Integer pageValue)
            throws DataNotFoundException,TemplateRenderingException{
        LOG.info("Getting orders for the day for unit :" + userSession.getUnitId());
        int unitId = 0;
        SettlementReportInputData data;
        try{
            unitId = userSession.getUnitId();
            OrderFetchStrategy strategy = new OrderFetchStrategy(true, false, false, false, false, null, -1, true, true, pageValue, 50);
            List<OrderDetailTrim> ordersTrims = orderSearchService.getOrderDetailsTrim(unitId,orderSearchService.getLastDayCloseOrderId(unitId),Integer.MAX_VALUE,strategy);
            data = new SettlementReportInputData(null, null, null, false, null, null,ordersTrims);
        } catch (Exception e){
            LOG.error("Error while getting Order Summary for unit {}",unitId,e);
            throw e;
        }
        return data;
    }



    @RequestMapping(method = RequestMethod.POST, value = "unit/delivery/orders", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public SettlementReportInputData getDeliveryOrdersForTheDay(@RequestBody final UserSessionDetail userSession,
                                                                @RequestParam(required = false) Integer batchSize)
            throws DataNotFoundException, TemplateRenderingException {
        LOG.info("Getting delivery orders for the day for unit: {}, batchSize: {}", userSession.getUnitId(), batchSize);
        int unitId = 0;
        SettlementReportInputData data;
        try {
            unitId = userSession.getUnitId();
            if (Objects.nonNull(batchSize) && batchSize > 0 && batchSize <= 50) {
                OrderFetchStrategy strategy = new OrderFetchStrategy(false, false, false, false,
                        true, null, -1, false, true, 1, batchSize);
                List<Order> orders = orderSearchService.getOrderDetails(unitId,
                        orderSearchService.getLastDayCloseOrderId(unitId), Integer.MAX_VALUE, strategy);
                data = new SettlementReportInputData(null, null, orders, false, null, null, null);
            } else {
                OrderFetchStrategy strategy = new OrderFetchStrategy(false, false, false, false, true, null, -1, false);
                List<Order> orders = orderSearchService.getOrderDetails(unitId,
                        orderSearchService.getLastDayCloseOrderId(unitId), Integer.MAX_VALUE, strategy);
                data = new SettlementReportInputData(null, null, orders, false, null, null, null);
            }
        } catch (Exception e) {
            LOG.error("Error while getting Order Summary for unit {}", unitId, e);
            throw e;
        }
        return data;
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/orders/employee-meal", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public SettlementReportInputData getEmployeeMealOrders(@RequestBody final int userId)
            throws DataNotFoundException, TemplateRenderingException {
        LOG.info("Getting orders for the month for employee :{}", userId);
        SettlementReportInputData data;
        try {
            List<Order> orders = orderSearchService.getEmployeeMealOrders(userId);
            data = new SettlementReportInputData(null, null, orders, false, null, null,null);
        } catch (Exception e) {
            LOG.error("Error while Getting orders for the month for employee :{}", userId, e);
            throw e;
        }
        return data;
    }

    private List<Order> slimDown(List<Order> orders) {
        orders.forEach(o -> {
            o.getOrders().clear();
            o.getEnquiryItems().clear();
            // o.getSettlements().clear();
            o.setSubscriptionDetail(null);
            o.setUnitName(null);
            o.setCancellationDetails(null);
        });

        return orders;
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/orders-in-line", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<OrderInfo> getOrdersInLineForUnit(@RequestBody final UserSessionDetail userSession)
            throws DataNotFoundException, TemplateRenderingException, AuthenticationFailureException {
        int unitId = userSession.getUnitId();
        validate(userSession);
        return slimOrdersInLine(ordersCache.getOrders(unitId), true);
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/orders-in-line/new/android", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<OrderInfo> getOrdersInLineForUnitAndroid(@RequestBody final int unitId)
            throws DataNotFoundException, TemplateRenderingException, AuthenticationFailureException {
        return slimOrdersInLine(ordersCache.getOrders(unitId), true, true);
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/orders-in-line/android", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    @Deprecated
    public List<OrderInfo> getOrdersInLineForUnitAndroid(@RequestBody final UserSessionDetail userSession)
            throws DataNotFoundException, TemplateRenderingException, AuthenticationFailureException {
        int unitId = userSession.getUnitId();
        validate(userSession);
        return slimOrdersInLine(ordersCache.getOrders(unitId), true, true);
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/orders-in-line/android-test", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<OrderInfo> getOrdersInLineForUnitAndroidTest(@RequestBody final Integer unitId)
            throws DataNotFoundException, TemplateRenderingException, AuthenticationFailureException {
        return slimOrdersInLine(ordersCache.getOrders(unitId), true, true);
    }

    private List<OrderInfo> slimOrdersInLine(List<OrderInfo> orders, boolean getReceipts, boolean isAndroid) {
        List<OrderInfo> responseData = new ArrayList<>();
        if (orders != null && !orders.isEmpty()) {
            orders.forEach(
                    p -> {
                        p.getCustomer().setNewCustomerForBrand(orderService.checkIsNewCustomerBrandWise(p.getCustomer().getId(), p.getBrand().getBrandId()));
                        OrderInfo info = new OrderInfo(null, setProductStationCategory(p.getOrder()), p.getCustomer(), p.getDeliveryPartner(),
                                p.getChannelPartner(), isAndroid && getReceipts ? p.getAndroidReceipts() : null,
                                isAndroid ? null : slimUnit(p), p.getDeliveryDetails(), p.getPrintType());
                        info.setBrand(p.getBrand());
                        responseData.add(info);
                    });
        }
        return responseData;
    }

    private Order setProductStationCategory(Order order) {
        if (Objects.nonNull(order) && Objects.nonNull(order.getOrders()) && !order.getOrders().isEmpty()) {
            IMap<UnitProductAsKey, RefLookupInfo> productStationMapping = masterCache.getWorkstationRoutingCache();
            for (OrderItem orderItem : order.getOrders()) {
                setProductStationCategoryToOrderItem(order, orderItem, productStationMapping);
                if (Objects.nonNull(orderItem.getComposition())
                            && !CollectionUtils.isEmpty(orderItem.getComposition().getMenuProducts())) {
                    for (OrderItem menuOrderItem : orderItem.getComposition().getMenuProducts()) {
                        setProductStationCategoryToOrderItem(order, menuOrderItem, productStationMapping);
                    }
                }
            }
        }
        return order;
    }
    
    private void setProductStationCategoryToOrderItem(Order order, OrderItem orderItem, IMap<UnitProductAsKey, RefLookupInfo> productStationMapping) {
        Product productDetail = masterCache.getProduct(orderItem.getProductId());
        if (Objects.nonNull(productDetail) && Objects.nonNull(productDetail.getStationCategoryName())) {
            orderItem.setStationCategory(productDetail.getStationCategoryName());
            UnitProductAsKey key = new UnitProductAsKey(order.getUnitId(), productDetail.getId());
            RefLookupInfo lookupInfo = productStationMapping.get(key);
            if (lookupInfo != null) {
                orderItem.setStationCategory(lookupInfo.getRefLookupName());
            }
        }
    }
    
    private List<OrderInfo> slimOrdersInLine(List<OrderInfo> orders, boolean getReceipts) {
        List<OrderInfo> responseData = new ArrayList<>();
        if (orders != null && !orders.isEmpty()) {
            orders.forEach(p -> responseData.add(new OrderInfo(null, p.getOrder(), p.getCustomer(),
                    p.getDeliveryPartner(), p.getChannelPartner(), getReceipts ? p.getReceipts() : null,
                    getReceipts ? null : slimUnit(p), p.getDeliveryDetails(), p.getPrintType())));
        }
        return responseData;
    }

    private Unit slimUnit(OrderInfo info) {
        Unit unit = info.getUnit();
        Unit u = null;
        if (unit != null) {
            u = new Unit();
            u.setId(unit.getId());
            u.setName(unit.getName());
            u.setAddress(unit.getAddress());
            u.setTin(unit.getTin());
        }
        return u;
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/android/get-status", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Map<Integer, OrderStatus> getOrderStatus(@RequestBody final Set<Integer> orders) {
        try {
            return ordersCache.getOrderStatusForAssemblyOrders(orders);
        } catch (Exception e) {
            LOG.error("ERROR while getting Order Status For Assembly Orders", e);
        }
        return null;
    }
    @RequestMapping(method = RequestMethod.POST, value = "cod/unit-orders", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Map<String, Map<Integer, OrderInfo>> getOrdersForSingleUnit(@RequestBody final UserSessionDetail userSession)
            throws DataNotFoundException, TemplateRenderingException, AuthenticationFailureException {
        validate(userSession);
        // we want to get all the orders which are marked for delivery units
        // return ordersCache.getOrders(UnitCategory.DELIVERY);
        // TODO Fix this conversion with converters.
        return slimDown(ordersCache.getOrdersByUnitId(UnitCategory.COD,userSession.getOrderStatus(),userSession.getCurrentGeneratedOrderId(),userSession.getUnitId()));
    }

    @RequestMapping(method = RequestMethod.POST, value = "cod/all-orders", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Map<String, Map<Integer, OrderInfo>> getOrdersForAllUnits(@RequestBody final UserSessionDetail userSession)
            throws DataNotFoundException, TemplateRenderingException, AuthenticationFailureException {
        validate(userSession);
        // we want to get all the orders which are marked for delivery units
        // return ordersCache.getOrders(UnitCategory.DELIVERY);
        // TODO Fix this conversion with converters.
        return slimDown(ordersCache.getOrders(UnitCategory.COD,userSession.getOrderStatus(),userSession.getCurrentGeneratedOrderId()) );
    }

    private Map<String, Map<Integer, OrderInfo>> slimDown(Map<String, Map<Integer, OrderInfo>> map) {
        Map<String, Map<Integer, OrderInfo>> responseMap = new HashMap<>();
        try{
            map.keySet().forEach(key -> {
                responseMap.put(key, new HashMap<Integer, OrderInfo>());
                try {
                    map.get(key).keySet().forEach(inner -> {
                        try {
                            responseMap.get(key).put(inner, getSlimOrderInfo(map.get(key).get(inner)));
                        }catch (Exception e2){
                            LOG.info("Error level2 for key :: {} id :: {}  :: {} ", key,inner,e2);
                        }
                    });
                }catch (Exception e1){
                    LOG.error("Error For Slim Down for Key : {} :: {}  " , e1 , key);
                }
            });
        }catch (Exception e){
            LOG.error("Error while slimming down response : {} " , e);
        }
        return responseMap;

    }

    private OrderInfo getSlimOrderInfo(OrderInfo orderInfo) {

        // slim customer
        Customer customer = new Customer();
        customer.setId(orderInfo.getCustomer().getId());
        customer.setFirstName(orderInfo.getCustomer().getFirstName());
        customer.setMiddleName(orderInfo.getCustomer().getMiddleName());
        customer.setLastName(orderInfo.getCustomer().getLastName());
        customer.setContactNumber(orderInfo.getCustomer().getContactNumber());
        customer.setCountryCode(orderInfo.getCustomer().getCountryCode());
        customer.getAddresses().addAll(orderInfo.getCustomer().getAddresses());

        // slim Unit
        Unit unit = new Unit();
        unit.setId(orderInfo.getUnit().getId());
        unit.setName(orderInfo.getUnit().getName());

        // slim order
        Order order = new Order();
        order.setOrderId(orderInfo.getOrder().getOrderId());
        order.setGenerateOrderId(orderInfo.getOrder().getGenerateOrderId());
        order.setBillCreationTime(orderInfo.getOrder().getBillCreationTime());
        order.setTransactionDetail(orderInfo.getOrder().getTransactionDetail());
        order.getSettlements().addAll(orderInfo.getOrder().getSettlements());
        order.setSubscriptionDetail(orderInfo.getOrder().getSubscriptionDetail());
        order.setUnitId(orderInfo.getOrder().getUnitId());
        order.setUnitName(orderInfo.getOrder().getUnitName());
        order.setStatus(orderInfo.getOrder().getStatus());
        order.setSource(orderInfo.getOrder().getSource());
        order.setOod(orderInfo.getOrder().isOod());
        order.setChannelPartner(orderInfo.getOrder().getChannelPartner());
        order.setSourceId(orderInfo.getOrder().getSourceId());
        order.setPartnerCustomerId(orderInfo.getOrder().getPartnerCustomerId());
        order.setBrandId(orderInfo.getOrder().getBrandId());
        order.setPrepTime(orderInfo.getOrder().getPrepTime());
        OrderInfo info = new OrderInfo(orderInfo.getEnv(), order, customer, unit, orderInfo.getDeliveryPartner(),
                orderInfo.getChannelPartner());
        info.setPrintType(orderInfo.getPrintType());
        info.setDeliveryDetails(orderInfo.getDeliveryDetails());
        info.setDeliveryPartner(orderInfo.getDeliveryPartner());
        info.setPartnerOrderRiderStates(orderInfo.getPartnerOrderRiderStates());
        info.getOrder().setLastOrderStatusEventTime(orderInfo.getLastOrderStatusEventTime());
        if(Objects.isNull(orderInfo.getOrderDelayReason())){
            LOG.info("delay reason is null for order id : {} ",info.getOrder().getOrderId());
            return info;
        }

        if(Objects.nonNull(orderInfo.getOrderDelayReason().getCafeDelayUpdatedBy())){
            String cafeDelayUpdatedByName = masterCache.getEmployeeBasicDetail(orderInfo.getOrderDelayReason().getCafeDelayUpdatedBy()).getName();
            orderInfo.getOrderDelayReason().setCafeDelayUpdatedByName(cafeDelayUpdatedByName);
        }

        if(Objects.nonNull(orderInfo.getOrderDelayReason().getRiderDelayUpdatedBy())) {
            String riderDelayUpdatedByName = masterCache.getEmployeeBasicDetail(orderInfo.getOrderDelayReason().getRiderDelayUpdatedBy()).getName();
            orderInfo.getOrderDelayReason().setRiderDelayUpdatedByName(riderDelayUpdatedByName);
        }

        info.setOrderDelayReason(orderInfo.getOrderDelayReason());
        return info;
    }

    @RequestMapping(method = RequestMethod.POST, value = "cod/get-order-by-orderId", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public OrderInfo getOrderInfoByOrderId(@RequestBody Integer orderId) throws DataNotFoundException,TemplateRenderingException{
        LOG.info("Request to get order by orderId :{}",orderId);
        return ordersCache.getOrderById(orderId, UnitCategory.COD, true, false);
    }

    @RequestMapping(method = RequestMethod.POST, value = "save-reason-for-order-delay", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean saveReasonsForRiderDelay(@RequestBody DelayReason delayReason ) throws DataNotFoundException,TemplateRenderingException {
        LOG.info("Request to save reasons for delay for orderId :{}",delayReason.getOrderId());
        return getOrderManagementService().saveRiderDelayReason(delayReason);
    }


    @RequestMapping(method = RequestMethod.POST, value = "takeaway/orders", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<OrderInfo> getOrdersForTakeaway(@RequestBody final UserSessionDetail userSession)
            throws DataNotFoundException, TemplateRenderingException, AuthenticationFailureException {
        validate(userSession);
        // we want to get all the orders which are marked for take away units
        return ordersCache.getOrders(userSession.getUnitId(), UnitCategory.TAKE_AWAY);
    }

    @RequestMapping(method = RequestMethod.POST, value = "order/open-delivery", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<OrderInfo> getOrdersForDelivery(@RequestBody final UserSessionDetail userSession)
            throws DataNotFoundException, TemplateRenderingException, AuthenticationFailureException {
        validate(userSession);
        // we want to get all the orders which are marked for take away units
        return slimOrdersInLine(ordersCache.getDeliveryOrders(userSession.getUnitId(), OrderStatus.READY_TO_DISPATCH),
                false);
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/newOrders", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    @Deprecated
    public NewOrderNotification checkNewOrdersForUnit(@RequestBody final RequestData newOrderCheck)
            throws DataNotFoundException, TemplateRenderingException, ParseException, AuthenticationFailureException {

        NewOrderNotification notification = null;
        UserSessionDetail userSession = newOrderCheck.getSession();
        int unitId = userSession.getUnitId();
        String timeStamp = newOrderCheck.getData();
        try {
            // validate(userSession);
            notification = orderSearchService.returnNewOrders(unitId, timeStamp, ordersCache.getOrders(unitId));
            notification.setOrders(slimOrdersInLine(notification.getOrders(), false));
            LOG.info("Found {} orders for unit {} for time stamp {}", notification.getOrders().size(), unitId,
                    timeStamp);
        } catch (Exception e) {
            LOG.error("Error while getting new orders for assembly screen", e);
            throw e;
        }
        return notification;
    }

    @Deprecated
    @RequestMapping(method = RequestMethod.POST, value = "unit/orders/new", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public NewOrderNotification fetchNewOrdersForUnit(@RequestBody final UnitOrderOld unitOrder) throws Exception {
        NewOrderNotification notification = null;
        try {
            notification = orderService.fetchNewOrders(unitOrder);
            notification.setOrders(slimOrdersInLine(notification.getOrders(), false));
        } catch (Exception e) {
            LOG.error("Error while getting new orders for assembly screen", e);
            throw e;
        }
        return notification;
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/orders/new/android", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public NewOrderNotification fetchNewOrdersForUnit(@RequestBody final UnitOrder unitOrder) throws Exception {
        NewOrderNotification notification = null;
        try {
            notification = orderService.fetchNewOrders(unitOrder);
            notification.setOrders(slimOrdersInLine(notification.getOrders(), true, true));
            Map<Integer, OrderStatus> newStatuses = orderService.fetchNewStatuses(unitOrder);
            notification.setOrderStatus(newStatuses);
        } catch (Exception e) {
            LOG.error("Error while getting new orders for assembly screen", e);
            throw e;
        }
        return notification;
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/orders/change/check", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean checkForChange(@RequestBody final UnitOrder unitOrder) throws Exception {
        NewOrderNotification notification = null;
        boolean changed = false;
        //UnitBasicDetail unit = masterCache.getUnitBasicDetail(unitOrder.getUnitId());
        /*
         * String reason = String.format("%s,%s,%s,%s,%s", "ORDER_CHANGE_CHECK",
         * unit.getId(), unit.getName(), unitOrder.getOrders() != null ?
         * unitOrder.getOrders().size() : 0, AppUtils.getCurrentTimeISTString());
         */
        try {
            notification = orderService.fetchNewOrders(unitOrder);
            changed = notification.getOrders() != null && notification.getOrders().size() > 0;
            if (changed) {
                /*
                 * reason = String.format("%s,%s,%s,%s", reason, "ORDERS_FOUND",
                 * notification.getOrders().size(), changed); LOG.info(reason);
                 */
                return changed;
            } else {
                Map<Integer, OrderStatus> newStatuses = orderService.fetchNewStatuses(unitOrder);
                changed = newStatuses != null && newStatuses.size() > 0;
                if (changed) {
                    /*
                     * reason = String.format("%s,%s,%s,%s", reason, "STATUS_CHANGED",
                     * newStatuses.size(), changed); LOG.info(reason);
                     */
                    return changed;
                }
            }
            /*
             * reason = String.format("%s,%s,%s,%s", reason, "NO_CHANGE", 0, changed);
             * LOG.info(reason);
             */
            return changed;
        } catch (Exception e) {
            /*
             * reason = String.format("%s,%s,%s,%s", reason, "ERROR", 0, true);
             * LOG.info(reason);
             */
            LOG.error("Error while checking change set for assembly screen :" + unitOrder.getUnitId(), e);
            return true;
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/orders/change/get", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public NewOrderNotification getChangeInOrders(@RequestBody final UnitOrder unitOrder) throws Exception {
        NewOrderNotification notification = null;
        try {
            notification = orderService.fetchNewOrders(unitOrder);
            notification.setOrders(slimOrdersInLine(notification.getOrders(), true, true));
            Map<Integer, OrderStatus> newStatuses = orderService.fetchNewStatuses(unitOrder);
            notification.setOrderStatus(newStatuses);
        } catch (Exception e) {
            LOG.error("Error while getting change set for assembly screen for unit :" + unitOrder.getUnitId(), e);
            throw e;
        }
        return notification;
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/orders/v2/change/get", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public NewOrderNotification getChangeInOrdersV2(@RequestBody final UnitOrder unitOrder) throws Exception {
        NewOrderNotification notification = null;
        try {
            notification = orderService.fetchNewOrders(unitOrder);
            notification.setOrders(slimOrdersInLine(notification.getOrders(), true, true));
            Map<Integer, OrderStatus> newStatuses = orderService.fetchNewStatuses(unitOrder);
            notification.setOrderStatus(newStatuses);

            Map<String, Map<String, String>> orderItemNewStatus = orderService.fetchOrderItemNewStatus(unitOrder);
            notification.setOrderItemStatus(orderItemNewStatus);
        } catch (Exception e) {
            LOG.error("Error while getting change set for assembly screen for unit :" + unitOrder.getUnitId(), e);
            throw e;
        }
        return notification;
    }


    @RequestMapping(method = RequestMethod.POST, value = "order/acknowledge", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Boolean removeFromUndeliveredQueue(@RequestBody final RequestData requestData)
            throws DataNotFoundException, TemplateRenderingException, ParseException, AuthenticationFailureException {
        UserSessionDetail userSession = requestData.getSession();
        validate(userSession);
        String orderIdString = requestData.getData();
        if (orderIdString != null && !orderIdString.isEmpty()) {
            Integer orderId = Integer.parseInt(orderIdString);
            LOG.info("Calling to update undelivered queue for order ::: {} and unitId {}", orderId,
                    userSession.getUnitId());
            return ordersCache.removeFromUndelivered(orderId, userSession.getUnitId());
        }
        return false;
    }

    @RequestMapping(method = RequestMethod.POST, value = "reset/undelivered", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void resetUndeliveredQueue(@RequestBody final Integer unitId) {
        ordersCache.resetUndeliveredMessages(unitId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "removeFromCache/{orderId}")
    @ResponseBody
    public boolean removeFromCache(@PathVariable(value = "orderId") final String generatedOrderId)
            throws DataNotFoundException {
        Order order = orderSearchService.getOrderDetail(generatedOrderId);
        return ordersCache.removeFromCache(order.getUnitId(), order.getOrderId());
    }

    @RequestMapping(method = RequestMethod.GET,value = "refreshOrderInfo")
    @ResponseBody
    public void refreshOrderInfoCache(){
        ordersCache.clearOrderInfoCache();
    }

    @RequestMapping(method = RequestMethod.POST,value = "clearOrderInfoUnitCache/{unitId}")
    @ResponseBody
    public boolean clearUnitOrderCache(@PathVariable(value = "unitId") final int unitId)
            throws DataNotFoundException{
        LOG.info("Clearing order cache for Unit with id : {}",unitId);
        return ordersCache.clearUnitOrderCache(unitId);
    }

    @RequestMapping(method = RequestMethod.POST,value = "clearOrderInfoCache")
    @ResponseBody
    public void clearOrderInfoCache(){
        LOG.info("###### Refresh OrderInfo Cache");
        ordersCache.clearOrderInfoCache();
    }


    @RequestMapping(method = RequestMethod.POST, value = "order/calculateConsumption", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Collection<Consumable> calculateConsumption(@RequestBody final String generatedOrderId) throws Exception {

        LOG.info("Calculating item Consumption for Generated Order ID = " + generatedOrderId);
        Map<Integer, Consumable> map = new HashMap<>();
        try {
            Order o = orderSearchService.getOrderDetail(generatedOrderId);
            /*int deliveryUnitId = getMasterDataCache().getDeliveryUnit(o.getUnitId(),
                    TransactionUtils.isPartnetOrder(masterCache.getChannelPartner(o.getChannelPartner()).getType()));*/
            int deliveryUnitId = getMasterDataCache().getDeliveryUnit(o.getUnitId(),
                    o.getChannelPartner(), o.getBrandId(), TransactionUtils.isCODOrder(o.getSource()));
            itemConsumptionHelper.calculateConsumption(o, map, deliveryUnitId, false);
        } catch (Exception e) {
            LOG.error("Error while calculating Consumption", e);
            throw e;
        }
        return map.values();
    }

    @RequestMapping(method = RequestMethod.POST, value = "order/calculateAllConsumption", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<OrderItemConsumable> calculateAllConsumption(@RequestBody final String generatedOrderId)
            throws Exception {

        LOG.info("Calculating item Consumption for Generated Order ID = " + generatedOrderId);
        Order o = orderSearchService.getOrderDetail(generatedOrderId);
        try {
            /*int deliveryUnitId = getMasterDataCache().getDeliveryUnit(o.getUnitId(),
                    TransactionUtils.isPartnetOrder(masterCache.getChannelPartner(o.getChannelPartner()).getType()));*/
            int deliveryUnitId = getMasterDataCache().getDeliveryUnit(o.getUnitId(),
                    o.getChannelPartner(), o.getBrandId(), TransactionUtils.isCODOrder(o.getSource()));
            return itemConsumptionHelper.getConsumption(o, deliveryUnitId);
        } catch (Exception e) {
            LOG.error("Error while calculating Consumption", e);
            throw e;
        }
    }

    public Map<IdCodeName, IdNameValueMap> calculateAllConsumptionData(Order o) throws Exception {
        try {
            /*int deliveryUnitId = getMasterDataCache().getDeliveryUnit(o.getUnitId(),
                    TransactionUtils.isPartnetOrder(masterCache.getChannelPartner(o.getChannelPartner()).getType()));*/
            int deliveryUnitId = getMasterDataCache().getDeliveryUnit(o.getUnitId(),
                    o.getChannelPartner(), o.getBrandId(), TransactionUtils.isCODOrder(o.getSource()));
            return itemConsumptionHelper.getConsumptionMap(o, deliveryUnitId);
        } catch (Exception e) {
            LOG.error("Error while calculating Consumption", e);
            throw e;
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = "order/calculateAllConsumption/month/multi", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void calculateMultiMonthlyConsumption(@RequestParam final int startMonth, @RequestParam final int endMonth,
                                                 @RequestParam final int year) throws Exception {
        if (startMonth > endMonth) {
            throw new Exception("Start month cannot be greater then end month");
        }
        for (int i = startMonth; i <= endMonth; i++) {
            calculateMonthlyConsumption(i, year);
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = "order/calculateAllConsumption/month", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void calculateMonthlyConsumption(@RequestParam final int month, @RequestParam final int year)
            throws Exception {

        LOG.info("Calculating monthly item Consumption for month {} and year {}", month, year);

        Date startDate = AppUtils.getDate(1, month, year);
        Date endDate = AppUtils.getLastDayOfMonth(startDate);

        LocalDate start = startDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate end = endDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();

        List<String> sourceList = new ArrayList<>();
        sourceList.add(AppConstants.CAFE);
        sourceList.add(AppConstants.COD);
        sourceList.add(AppConstants.TAKE_AWAY);

        for (UnitBasicDetail ubd : masterCache.getUnitsBasicDetails().values()) {
            for (String source : sourceList) {
                calculateMonthlyConsumptionForUnit(ubd, start, end, month, year, source);
            }
        }
    }

    private void calculateMonthlyConsumptionForUnit(UnitBasicDetail ubd, LocalDate start, LocalDate end, int month,
                                                    int year, String source) throws Exception {

        Map<IdCodeName, IdNameValueMap> map = new HashMap<>();

        for (LocalDate date = start; date.isBefore(end) || date.isEqual(end); date = date.plusDays(1)) {

            Date businessDate = java.util.Date.from(date.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
            LOG.info("Calculating monthly item Consumption for unit {} and date {}", ubd.getName(), businessDate);
            List<Order> orders = orderSearchService.getOrderDetailsForDay(businessDate, ubd.getId(), source);
            for (Order o : orders) {
                Map<IdCodeName, IdNameValueMap> currentOrderMap = calculateAllConsumptionData(o);
                for (IdNameValueMap c : currentOrderMap.values()) {
                    IdNameValue v = c.getValue();
                    IdCodeName productKey = new IdCodeName(v.getId(), v.getName(), v.getCode());
                    IdNameValueMap data = map.get(productKey);
                    if (data == null) {
                        map.put(productKey, c);
                    } else {
                        data.getValue().setValue(data.getValue().getValue().add(v.getValue()));
                        for (IdNameValue sv : c.getValueMap().values()) {
                            IdCodeName sKey = new IdCodeName(sv.getId(), sv.getName(), sv.getCode());
                            IdNameValue scmValue = data.getValueMap().get(sKey);
                            if (scmValue == null) {
                                data.getValueMap().put(sKey, sv);
                            } else {
                                scmValue.setValue(scmValue.getValue().add(sv.getValue()));
                            }
                        }
                    }
                }
            }
        }

        List<OrderItemConsumable> list = new ArrayList<>();
        for (IdNameValueMap ivm : map.values()) {
            IdNameValue v = ivm.getValue();
            for (IdNameValue sv : ivm.getValueMap().values()) {
                OrderItemConsumable c = new OrderItemConsumable();
                c.setMenuProductId(v.getId());
                c.setMenuProductDimension(v.getCode());
                c.setMenuProductName(v.getName());
                c.setMenuProductQuanity(v.getValue().intValue());
                c.setScmProductId(sv.getId());
                c.setScmProductName(sv.getName());
                c.setScmProductQuantity(sv.getValue());
                c.setScmProductUom(sv.getCode());
                list.add(c);
            }
        }

        LOG.info("Saving Monthly Consumption for unit {} , month {}, year {}, source {}, count {} ", ubd.getName(),
                month, year, source, list.size());

        orderService.saveMonthlyConsumptionData(ubd, month, year, list, source);

    }

    @RequestMapping(method = RequestMethod.POST, value = "order/consumption/today", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public QuantityResponseData calculateConsumptionForCurrentDay(@RequestBody QuantityRequestData request)
            throws Exception {

        LOG.info("Calculating item Consumption for today for unit " + request.getUnitId());
        Map<Integer, Consumable> map = new HashMap<>();
        int maxOrder = 0;
        try {
            OrderFetchStrategy strategy = new OrderFetchStrategy(false, true, false, false, false, null, -1, true);
            List<Order> orders = orderSearchService.getOrderDetails(request.getUnitId(),
                    orderSearchService.getLastDayCloseOrderId(request.getUnitId()), Integer.MAX_VALUE, strategy);
            for (Order o : orders) {
                /*int deliveryUnitId = getMasterDataCache().getDeliveryUnit(o.getUnitId(),
                        TransactionUtils.isPartnetOrder(masterCache.getChannelPartner(o.getChannelPartner()).getType()));*/
                int deliveryUnitId = getMasterDataCache().getDeliveryUnit(o.getUnitId(),
                        o.getChannelPartner(), o.getBrandId(), TransactionUtils.isCODOrder(o.getSource()));
                itemConsumptionHelper.addConsumption(o, map, deliveryUnitId, false);
                if (maxOrder < o.getOrderId()) {
                    maxOrder = o.getOrderId();
                }
            }
        } catch (Exception e) {
            LOG.error("Error while calculating Consumption", e);
            throw e;
        }
        List<ProductQuantityData> data = new ArrayList<>();
        Set<Integer> productIds = new HashSet<>(request.getProductIds());
        for (Consumable c : map.values()) {
            if (productIds.contains(c.getProductId())) {
                data.add(new ProductQuantityData(c.getProductId(), c.getQuantity(), c.getUom()));
            }
        }
        return new QuantityResponseData(request.getUnitId(), data, InventoryAction.REMOVE, InventorySource.CAFE_ORDER,
                maxOrder, AppUtils.getCurrentTimestamp());
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-card", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CashCardDetail getCard(@RequestParam final int customerId, @RequestParam final String cardNumber)
            throws DataNotFoundException, CardValidationException {
        LOG.info("Getting Card details for customerId: {} and card number: {}", customerId, cardNumber);
        return cardService.getCardDetail(customerId, cardNumber, true);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-cards", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Object getCards(@RequestParam final int customerId) throws DataNotFoundException, CardValidationException {
        LOG.info("Getting Card details for customerId: {}", customerId);
        List<CashCardDetail> cardList = cardService.getCardDetails(customerId);
        HashMap<String, List<CashCardDetail>> hmap = new HashMap<>();
        List<CashCardDetail> activeList = new ArrayList<>();
        List<CashCardDetail> readyList = new ArrayList<>();
        for (CashCardDetail cashCardDetail : cardList) {
            if (cashCardDetail.getBuyerId() != null && cashCardDetail.getCustomerId() == null
                    && CashCardStatus.READY_FOR_ACTIVATION.name().equals(cashCardDetail.getCardStatus())) {
                readyList.add(cashCardDetail);
            } else if (cashCardDetail.getBuyerId() != null && cashCardDetail.getCustomerId() != null
                    && CashCardStatus.ACTIVE.name().equals(cashCardDetail.getCardStatus())) {
                activeList.add(cashCardDetail);
            }
        }
        hmap.put("self", activeList);
        hmap.put("gift", readyList);
        return hmap;
    }

    @RequestMapping(method = RequestMethod.POST, value = "associate-and-create", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public OrderResponse associatePaymentAndCreateOrder(@RequestBody Order order) throws DataNotFoundException,
            CardValidationException, TemplateRenderingException, AuthenticationFailureException, DataUpdationException, IOException, JMSException,OfferValidationException {
        return createOrder(order);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-cash-card-amount", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CustomerCardInfo getCashCardAmount(@RequestParam int customerId, @RequestParam int unitId) {
        LOG.info("Request to get card amount for the customer {} at unitId {}", customerId, unitId);
        return cardService.getCashCardAmount(customerId);
    }


    /*@RequestMapping(method = RequestMethod.GET, value = "get-chaayos-cash-amount", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CustomerCashInfo getChaayosCashAmount(@RequestParam int customerId) {
        LOG.info("Request to get chaayos cash amount for the customer {}", customerId);
        BigDecimal amount = customerService.getAvailableCash(customerId);
        SubscriptionInfoDetail subscriptionInfoDetail = offerService.getSubscriptionInfoDetail(customerId);
        if(Objects.nonNull(subscriptionInfoDetail)){
            return new CustomerCashInfo(amount,subscriptionInfoDetail);
        }
        return new CustomerCashInfo(amount);
    }*/

    @RequestMapping(method = RequestMethod.GET, value = "get-chaayos-cash-amount", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CustomerCashInfo getChaayosCashAmountForCustomer(@RequestParam int customerId) {
        LOG.info("Request to get chaayos cash amount for the customer {}", customerId);
        BigDecimal amount = customerService.getAvailableCash(customerId);
        SubscriptionInfoDetail subscriptionInfoDetail = null;
        try{
            subscriptionInfoDetail = offerService.getSubscriptionInfoDetailForCustomer(customerId);
        }catch (Exception e ){
            LOG.error("Exception while getting subscription detail for customer with id :::{}",customerId,e );
        }
        if(Objects.nonNull(subscriptionInfoDetail)){
            return new CustomerCashInfo(amount,subscriptionInfoDetail);
        }
        return new CustomerCashInfo(amount);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-subscription-info", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CustomerCashInfo getSubscriptionInfo(@RequestParam int customerId) {
        LOG.info("Request to get chaayos cash amount for the customer {}", customerId);
        BigDecimal amount = customerService.getAvailableCash(customerId);
        SubscriptionInfoDetail subscriptionInfoDetail = offerService.getSubscriptionInfoDetail(customerId);
        if(Objects.nonNull(subscriptionInfoDetail)){
            return new CustomerCashInfo(amount,subscriptionInfoDetail);
        }
        return new CustomerCashInfo(amount);
    }


    @RequestMapping(method = RequestMethod.POST, value = "get-cash-card-offer", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<GiftOffer> getCashCardOffer(@RequestBody int unitId, @RequestParam(required = false) Integer partnerId) {
        LOG.info("Request to get card amount offer for unit :" + unitId);
        if (partnerId == null) {
            partnerId = AppConstants.CHAAYOS_DINEIN_PARTNER_ID;
        }
        return cardService.getCurrentCardOffer(unitId, partnerId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "user/allowance-limit", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public BigDecimal getEmployeeAllowanceLimit(@RequestBody final int userId)
            throws AuthenticationFailureException, DataUpdationException {
        LOG.info("Getting Allowance Limit for Employee with empId {}", userId);
        return employeeMealService.getEmployeeAllowanceLimit(userId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "missed-call-response", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean missedCallResponse(HttpServletRequest request, @RequestParam final String callerNo,
                                      @RequestParam final String missCallNo) throws DataNotFoundException {
        LOG.info(String.format("Missed Call Response  callerNo  " + callerNo + " missCallNo   " + missCallNo));
        boolean result = orderService.updateOrderStatusOnSDPCallback(
                callerNo.substring(callerNo.length() - 10, callerNo.length()), missCallNo,
                DeliveryReason.MissedCallResponse.getReason(), DeliveryReason.MissedCallResponse.getReasonId());
        LOG.info("updateOrderStatusOnSDPCallback with result " + result);
        return result;
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-sdp-order-detail", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Map<String, String> getSDPOrderDetails(@RequestBody final String riderContactNo)
            throws DataNotFoundException, TemplateRenderingException {
        LOG.info("get-sdp-order-deatil for riderContactNo   " + riderContactNo);
        return orderService.getSDPOrderDetails(riderContactNo);
    }

    @RequestMapping(method = RequestMethod.POST, value = "reset-sdp-order-detail", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean resetSDPOrderDetails(@RequestBody final String riderContactNo)
            throws DataNotFoundException, TemplateRenderingException {
        LOG.info("reset-sdp-order-deatil for riderContactNo   " + riderContactNo);
        return orderService.resetSDPOrderDetails(riderContactNo);
    }

    @RequestMapping(method = RequestMethod.POST, value = "ood/notification", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean outOfDeliveryNotification(@RequestBody final int orderId)
            throws DataNotFoundException, TemplateRenderingException {
        LOG.info("outOfDeliveryNotification  " + orderId);
        return orderService.outOfDeliveryNotification(orderId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "order/transition", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<OrderStatusDomain> orderTransitionDetail(@RequestBody final int orderId)
            throws DataNotFoundException, TemplateRenderingException {
        LOG.info("orderTransitionDetail  " + orderId);
        return orderService.getOrderTransitionDetail(orderId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "publish-order-status", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean publishOrderFinalStatus(@RequestBody final List<Integer> orderIdList)
            throws DataNotFoundException, TemplateRenderingException {
        if (orderIdList == null || orderIdList.isEmpty()) {
            return true;
        }
        LOG.info("request to publish order status for {} orders", orderIdList.size());
        return orderSearchService.publishFinalOrderStatus(orderIdList);
    }

    @RequestMapping(method = RequestMethod.POST, value = "publish-orders", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean publishOrders(@RequestBody final String date) {
        Date businessDate = date != null ? AppUtils.getDate(AppUtils.parseDateIST(date))
                : AppUtils.getCurrentBusinessDate();
        LOG.info("request to publish orders for business date {}", businessDate);

        for (UnitBasicDetail u : masterCache.getUnitsBasicDetails().values()) {
            if (UnitStatus.ACTIVE.equals(u.getStatus())) {
                try {
                    List<OrderInfo> orders = orderSearchService.getOrderToPublish(u.getId(), businessDate);
                    for (OrderInfo o : orders) {
                        publish(o);
                    }
                } catch (Exception e) {
                    LOG.error("Error while publishing orders", e);
                }
            }
        }
        return true;
    }

    @RequestMapping(method = RequestMethod.POST, value = "order/reprint-order-kot", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<ReprintResponse> reprintOrderKOT(@RequestBody final RequestData requestData)
            throws DataNotFoundException, TemplateRenderingException, AuthenticationFailureException {
        LOG.info("Reprint Order  KOT Request for Generated Order ID = " + requestData.getData());
        validate(requestData.getSession());
        List<ReprintResponse> reprintResponses = new ArrayList<>();
        ActionRequest request = getData(requestData, ActionRequest.class);
        OrderInfo info = orderSearchService.getOrderReceipt(request.getGeneratedOrderId());
        List<String> receiptList = TransactionUtils.getKOTReceipts(props.getChaayosBaseUrl(),masterCache, true, info, props.getBasePath(),
                props.getRawPrintingSatus(),props);
        for (String str : receiptList) {
            ReprintResponse response = new ReprintResponse();
            response.setPrintString(str);
            if (props.getRawPrintingSatus()) {
                response.setPrintType(PrintType.RAW);
            } else {
                response.setPrintType(PrintType.HTML);
            }
            reprintResponses.add(response);
        }
        return reprintResponses;
    }

    @RequestMapping(method = RequestMethod.POST, value = "last-day-close", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public UnitClosure getLastDayClose(@RequestBody final int unitId) {
        return orderSearchService.getLastDayClose(unitId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "refresh-free-item-offer-cache", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean refreshFreeItemOfferCache() {
        return getFreeOfferItemService().refreshCache();
    }

    @RequestMapping(method = RequestMethod.POST, value = "sync-order-inventory", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean syncOrderInventory(@RequestBody final InventorySyncRequest request) {
        if (request.getClosureId() == 0 || request.getOrderIds() == null || request.getOrderIds().size() == 0) {
            return true;
        }
        UnitClosure closure = orderSearchService.getUnitClosure(request.getClosureId());
        OrderFetchStrategy strategy = new OrderFetchStrategy(false, true, false, false, false, null, -1, true);
        List<OrderInfo> orders = orderSearchService.getOrderDetailsAfterOrderId(request.getUnitId(),
                closure.getLastOrderId(), strategy, request.getOrderIds());
        for (OrderInfo order : orders) {
            if(Objects.isNull(order.getOrder().getTableRequestId())){
                publishInventory(order);
            }
        }
        return true;
    }

    @RequestMapping(method = RequestMethod.POST, value = "external-partner-detail", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public ExternalPartnerDetail getExternalPartnerDetail(@RequestBody final String partnerCode) {
        return orderService.getExternalPartnerDetail(partnerCode);
    }

    @RequestMapping(method = RequestMethod.GET, value = "unit-table-status", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<UnitTableMapping> getTablesForUnit(@RequestParam final int unitId) {
        LOG.info("Request to get tables for unitId {}", unitId);
        return tableService.getTablesForUnit(unitId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "unit-table-summary", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public UnitTableMapping getTableSummaryForUnit(@RequestParam final int tableRequestId) {
        LOG.info("Request to get table Summary for request Id {}", tableRequestId);
        return tableService.getTableSummary(tableRequestId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "reserve-table", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public UnitTableMapping reserveTableForUnit(@RequestParam final int unitId, @RequestParam final int tableNumber)
            throws DataNotFoundException {
        LOG.info("Request to reserver table for unitId {} and tableNumber {}", unitId, tableNumber);
        return tableService.reserveTableForUnit(unitId, tableNumber);
    }

    @RequestMapping(method = RequestMethod.GET, value = "change-table", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public UnitTableMapping changeTable(@RequestParam final int tableRequestId, @RequestParam final int tableNumber)
            throws DataNotFoundException, DataUpdationException {
        LOG.info("Request to change table for tableRequestId {} and tableNumber {}", tableRequestId, tableNumber);
        return tableService.changeTable(tableRequestId, tableNumber);
    }

    @RequestMapping(method = RequestMethod.GET, value = "close-table", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean closeTableForUnit(@RequestParam final int tableRequestId) throws DataNotFoundException {
        LOG.info("Request to close table for table request Id {}", tableRequestId);
        return tableService.closeTableForUnit(tableRequestId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "table-checkout", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public TableResponse tableCheckout(@RequestBody final TableSettlement settlement)
            throws DataNotFoundException, DataUpdationException, TemplateRenderingException {
        LOG.info("Request to table-checkout for table request Id {}", JSONSerializer.toJSON(settlement));
        tableService.tableCheckout(settlement);
        TableResponse t = tableService.generateSettlementReceipt(settlement.getTableRequestId());
        List<Integer> orderIds = tableService.getTableOrders(settlement.getTableRequestId());
        // orders are published here after setlements are completed
        publishAllOrders(orderIds);
        return t;
    }

    @RequestMapping(method = RequestMethod.POST, value = "order/reprint-settlement-receipt", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public TableResponse reprintSettlementSlip(@RequestBody final Integer tableRequest)
            throws DataNotFoundException, DataUpdationException, TemplateRenderingException {
        LOG.info("Request to reprint-settlement-receipt for table Request Id {}", tableRequest);
        if (tableRequest == null || tableRequest == 0) {
            return new TableResponse();
        }
        return tableService.generateSettlementReceipt(tableRequest);
    }

    @RequestMapping(method = RequestMethod.GET, value = "order/receipt/download")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Receipt getOrderReceipt(@RequestParam final String orderId) {
        LOG.info("Request to get receipt for orderId: ", orderId);
        byte[] receiptBytes = orderService.getOrderReceipt(orderId);
        Receipt receipt = new Receipt();
        receipt.setOrderReceipt(receiptBytes);
        return receipt;
    }

    @RequestMapping(method = RequestMethod.POST, value = "order/paymentStatus", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public OrderPayment fetchPaymentStatus(@RequestBody final Integer orderId) throws Exception {
        try {
            return paymentServiceNew.getPaymentStatus(orderId);
        } catch (Exception e) {
            String message = "Error while fetching payment status for OrderID :::: "
                    + orderId;
            LOG.error(message, e);
        }
        return null;
    }

    @Override
    public MetadataCache getMetadataCache() {
        return cache;
    }

    @Override
    public OrderManagementService getOrderManagementService() {
        return orderService;
    }

    @Override
    public EnvironmentProperties getEnvironmentProperties() {
        return props;
    }

    @Override
    public OrderInfoCache getOrderInfoCache() {
        return ordersCache;
    }

    @Override
    public CustomerService getCustomerService() {
        return customerService;
    }

    public CustomerOfferManagementService getCustomerOfferManagementService() {
        return offerService;
    }

    @Override
    public DeliveryRequestService getDeliveryRequestService() {
        return deliveryService;
    }

    @Override
    public MasterDataCache getMasterDataCache() {
        return masterCache;
    }

    @Override
    public UnitInventoryManagementService getUnitInventoryManagementService() {
        return unitService;
    }

    @Override
    public OfferManagementExternalService getOfferManagementService() {
        return offerExternalService;
    }

    @Override
    public CardService getCardService() {
        return cardService;
    }

    @Override
    public FirebaseNotificationService getFirebaseNotificationService() {
        return fireBaseService;
    }

    @Override
    public FeedbackManagementService getFeedbackManagementService() {
        return feedbackManagementService;
    }

    @Override
    public TokenService<FeedbackTokenInfo> getTokenService() {
        return tokenService ;
    }

    @Override
    public PosMetadataService getPosMetadataService() {
        return posMetadataService ;
    }

    @Override
    public OrderSearchService getOrderSearchService() {
        return orderSearchService;
    }


    @RequestMapping(method = RequestMethod.GET, value = "get-available-card-amount", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CustomerCardInfo getCashCardAmount(@RequestParam String customerId) {
        LOG.info("Request to get card amount for the customer {}", customerId);
        return cardService.getCashCardAmount(Integer.parseInt(customerId));
    }

    @RequestMapping(method = RequestMethod.POST, value = "rider-health-check", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean partnerOrderRiderData(@RequestBody final PartnerOrderRiderData request) {
        LOG.info("Entering into Rider Health check Method");
        boolean data = true;
        orderService.saveRiderDetails(request);
        LOG.info("Rider's data is saved and requested to partner with order id: " + request.getOrder_id());
        LOG.info("mask check, temp check: " + request.isMaskCheck() + ", " + request.isRbtCheck());
        String endPoint = props.getSCMServiceBasePath() + Endpoints.CHANNELPARTNER_SERVICE_ENTRY_POINT;
        if (request.isMaskCheck()) {
            String maskEndPoint = endPoint + Endpoints.GET_RIDER_MASK_STATUS;
            Map<String, String> paramMask = new HashMap<>();
            paramMask.put("order_id", request.getOrder_id());
            paramMask.put("is_rider_wearing_mask", request.getIs_rider_wearing_mask());
            try {
                LOG.info("Sendind Request To: " + maskEndPoint);
                WebServiceHelper.postRequestWithAuthInternalTimeout(maskEndPoint, props.getKettleClientToken(), paramMask);
            } catch (ClientProtocolException e) {
                LOG.error("Error while sending request to partner", e);
            } catch (IOException e) {
                LOG.error("Error while sending request to partner", e);
            }
        }
        if (request.isRbtCheck()) {
            if (request.isIs_high_temp()) {
                data = false;
            }
            Map<String, String> paramTemp = new HashMap<>();
            paramTemp.put("order_id", request.getOrder_id());
            paramTemp.put("rider_phone_number", request.getRider_phone());
            paramTemp.put("rbt", String.valueOf(request.getRbt()));
            paramTemp.put("is_high_temp", String.valueOf(request.isIs_high_temp()));
            String tempEndPoint = endPoint + Endpoints.GET_RIDER_TEMP_STATUS;
            try {
                LOG.info("Sendind Request To: " + tempEndPoint);
                WebServiceHelper.postRequestWithAuthInternalTimeout(tempEndPoint, props.getKettleClientToken(), paramTemp);
            } catch (ClientProtocolException e) {
                LOG.error("Error while sending request to partner", e);
            } catch (IOException e) {
                LOG.error("Error while sending request to partner", e);
            }
        }
        LOG.info("Exiting into Rider Health check Method");
        return data;
    }

    @RequestMapping(method = RequestMethod.POST, value = "customer-orders", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CustomerOrderResponse getCustomerOrders(@RequestBody final CustomerOrderRequest request) throws DataNotFoundException {
        LOG.info("Request to fetch order for request {}", request);
        CustomerOrderResponse response = new CustomerOrderResponse();
        if (request.getOrderId() == null) {
            response.setCustomerId(request.getCustomerId());
            Date date = AppUtils.getCurrentBusinessDate();
            Date fromDate = AppUtils.addDays(date, -1 * request.getNoOfDays());
            response.setOrderDetails(orderSearchService.getCustomerOrders(request.getCustomerId(), fromDate,
                    request.getMaxSize(), request.getFilteredIds()));
        } else {
            response.setCustomerId(request.getCustomerId());
            try {
                response.getOrderDetails().add(orderSearchService.getOrderDetail(request.getOrderId()));
            } catch (DataNotFoundException e) {
                LOG.error("Error in fetching order for request {}", request, e);
            }

        }
        orderService.attachFeedback(response.getOrderDetails());
        LOG.info("Completed fetch order request for request {}", request);
        return response;
    }

    @RequestMapping(method = RequestMethod.POST, value = "refresh-all-list-data", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean refreshOrderFeedbackQuestion() throws DataNotFoundException {
        try{
            LOG.info("Request to get refresh all list data");
            getOrderManagementService().refreshAllListData();
            LOG.info("all list data is refreshed");
            return true;
        }catch (Exception e){
            LOG.error("Error while refreshing all list data");
            return false;
        }

    }

    @RequestMapping(method = RequestMethod.POST, value = "dine-in-nps-feedback", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public OrderInAppUrlResponse getCustomerOrders(@RequestBody final List<Integer> orderIds) throws DataNotFoundException {
        return orderService.getOrdersWithDineInNPSFeedback(orderIds);
    }

    @RequestMapping(method = RequestMethod.POST, value = "customer-feedbacks", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CustomerFeedbackResponse getCustomerFeedbacks(@RequestBody final CustomerOrderRequest request)
            throws ParseException {
        LOG.info("Request to fetch order feedback data for request {} ", request);
        CustomerFeedbackResponse response = new CustomerFeedbackResponse();
        if (request.getOrderId() == null) {
            response.setCustomerId(request.getCustomerId());
            Date date = AppUtils.getCurrentDate();
            Date fromDate = AppUtils.addDays(date, -1 * request.getNoOfDays());
            response.setFeedbacks(orderSearchService.getCustomerFeedbacks(request.getCustomerId(), fromDate,
                    request.getMaxSize(), request.getFilteredIds()));
        } else {
            response.setCustomerId(request.getCustomerId());
            response.getFeedbacks().add(orderSearchService.getCustomerFeedbacks(request.getOrderId()));
        }
        LOG.info("Completed fetch order feedback data request for request {}", request);
        return response;
    }

    @RequestMapping(method = RequestMethod.GET, value = "customer-offer-last-time", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public String getCustomerOfferLastAppliedTime(@RequestParam String customerId, @RequestParam String offerCode) {
        LOG.info("Request to get time for customer {} {} for coupon code ", customerId, offerCode);
        return orderService.getCustomerOfferLastAppliedTime(Integer.parseInt(customerId), offerCode);
    }


    @RequestMapping(method = RequestMethod.GET, value = "refresh-mission-garam-chai-cache", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void refreshMissionGaramChai() throws DataNotFoundException {
        Map<Integer, Map<Integer, PartnerDataWithOrderConsideration>> map = partnerOrderConsiderationCache.getMissionGaramChai();
        orderService.loadPartnerOrderCache();
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-mission-garam-chai-cache/{unitId}", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Map<Integer, PartnerDataWithOrderConsideration> getMissionGaramChaiCountForUnit(@PathVariable Integer unitId) {
        return orderService.getMissionGaramChaiCountForUnit(unitId);
    }

    private void validateRequest(String token, boolean removeToken)
            throws AuthenticationFailureException {
        if (csrftokenService.contains(token)) {
            LOG.info("Found Token - " + token);
            if (removeToken) {
                removeToken(token);
            }
            return;
        }else {
            LOG.info("Not Found Token - " + token);
        }
        throw new AuthenticationFailureException("Invalid request");
    }
    public void removeToken(String token) {
        try {
            if (token != null) {
                csrftokenService.remove(token);
            }
        } catch (Exception e) {
            LOG.error("Error Removing Token", e);
        }
    }

    @RequestMapping(method = RequestMethod.GET, value="special-offer-lookup",produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Boolean lookupSpecialOffer(@RequestParam String contact, @RequestParam String token, @RequestParam String source){
        try {
            //return false;
            GamifiedOfferRequest request = new GamifiedOfferRequest();
            request.setContactNumber(contact);
            request.setCampaignToken(token);
            request.setOfferSource(OfferSource.valueOf(source));
            return gamifiedOfferService.isGamifiedOfferExist(request).getKey();
        }
        catch (Exception e){
            LOG.error("ERROR: ", e);
        }
        return false;
    }

    @RequestMapping(method = RequestMethod.GET, value="special-offer-lookup-v2",produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Pair<Boolean, String> lookupSpecialOfferV2(@RequestParam String contact, @RequestParam String token, @RequestParam String source){
        try {
            //return false;
            GamifiedOfferRequest request = new GamifiedOfferRequest();
            request.setContactNumber(contact);
            request.setCampaignToken(token);
            request.setOfferSource(OfferSource.valueOf(source));
            return gamifiedOfferService.isGamifiedOfferExist(request);
        }
        catch (Exception e){
            LOG.error("ERROR: ", e);
        }
        return new Pair<>(false,"PRE");
    }

    @RequestMapping(method = RequestMethod.GET, value = "check-free-chai-delivery/{customerId}", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Boolean checkFreeChaiDelivery(@PathVariable Integer customerId) throws DataNotFoundException {
        if (props.enableFreeChaiDeliveryConfirmationOfCustomerSMS()) {
            if (orderService.checkDeliveryOrderForCustomer(customerId) == 0) {
                Long transactionCount = orderService.checkDineInOrderForCustomer(customerId);
                Long customerCount = customerService.checkCustomerAdditionalDetail(customerId, CustomerDetailType.FREE_CHAI_DELIVERY.value());
                if (transactionCount >= props.getTranactionLimitForFreeChaiDeliveryCondition() && customerCount == 0) {
                    return true;
                }
            }
//            return false;
        }
        return false;
    }


    @RequestMapping(method = RequestMethod.POST, value = "send-free-chai-delivery/{customerId}", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CustomerAdditionalDetail sendFreeChaiDeliveryDetails(@PathVariable Integer customerId) throws DataNotFoundException {
        CustomerAdditionalDetail customerAdditionalDetail = new CustomerAdditionalDetail();
        if (props.enableFreeChaiDeliveryConfirmationOfCustomerSMS()) {
            String contactNumber = customerService.getCustomer(customerId).getContactNumber();
            Boolean status = orderService.sendFreeChaiDeliveryDetails(contactNumber);
            customerAdditionalDetail = orderService.saveFreeChaiDeliveryDetails(customerId, CustomerSMSNotificationType.FREE_CHAI_DELIVERY.name(), status);
        }
        return customerAdditionalDetail;
    }

    @RequestMapping(method = RequestMethod.POST, value = "order-detail-feedback", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public OrderDetailForFeedback getOrderDetailForFeedback(@RequestBody FeedbackTokenInfo feedbackTokenInfo) throws DataNotFoundException {
        LOG.info("Request to get order detail for kettle service");
        return getOrderManagementService().getOrderDetailForFeedback(feedbackTokenInfo);
    }


    @RequestMapping(method = RequestMethod.GET, value = "customer-mapping-types", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public  List<CustomerMappingTypes> getCustomerMappingTypes() {
        return getOrderManagementService().getCustomerMappingTypes();
    }

    @Override
    public OfferManagementService getOfferService() {
        return offer;
    }

    @RequestMapping(method = RequestMethod.POST, value = "day-close-estimates-data", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<DayCloseEstimateData> getDayCloseEstimatesData(@RequestBody final F9SalesRequest f9SalesRequest) {
        LOG.info("Got request to get the day close estimate data from {} to {}",f9SalesRequest.getStartDate(),f9SalesRequest.getEndDate());
        Date sDate = AppUtils.getDate(f9SalesRequest.getStartDate(),"yyyy-MM-dd");
        Date eDate = AppUtils.getDate(f9SalesRequest.getEndDate(),"yyyy-MM-dd");
        return getOrderManagementService().getDayCloseEstimatesData(f9SalesRequest.getUnitIds(),sDate,eDate);
    }

    @RequestMapping(method = RequestMethod.POST, value="add-free-cash/{contact}",produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CashPacketData addFreeCash(@PathVariable String contact, @RequestParam(required = true) Integer amount,
                                      @RequestParam(required = true) Integer validityInDays){
        try {
            return cashBackService.allotCashAsFreeGift(new BigDecimal(amount), contact, validityInDays, 0,null);
        }
        catch (Exception e){
            LOG.error("ERROR: ", e);
            return null;
        }
    }

    @RequestMapping(method = RequestMethod.POST, value="add-free-subscription/{contact}",produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Boolean addFreeSubscription(@PathVariable String contact, @RequestParam(required = true) String planCode,
                                       @RequestParam(required = true) Integer validityInDays, @RequestParam(required = true) Integer brandId,@RequestParam(required = true) BigDecimal price){
        try {
            CustomerInfo info = customerService.getCustomerInfoObject(contact);
            createSubscription(planCode,validityInDays,brandId,info.getCustomerId(),price,1,"SLOT_GAME");
            return true;
        }
        catch (Exception e){
            LOG.error("ERROR: ", e);
            return false;
        }
    }

    @RequestMapping(method = RequestMethod.POST, value="get-special-offer",produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    @Deprecated()
    public SpecialOfferResponse getSpecialOffer(@RequestBody SpecialOfferRequest request){
        try {
            validateRequest(request.getAuthToken(),true);
            //return orderService.getSpecialOffer(request);
            //Migrated to Gamified Offer
            return null;
        }
        catch (Exception e){
            LOG.error("ERROR: ", e.getMessage());
            return new SpecialOfferResponse(SpecialOfferType.NO_OFFER.name());
        }
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-campaign-by-token", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CampaignDetailResponse getCampaignByTokenAndStatus(@RequestParam String campaignToken, @RequestParam String status) {
        LOG.info("Request for getting Campaign by token: {} and status: {}", campaignToken, status);
        try {
            return campaignCache.getCampaignByToken(campaignToken, status);
        }catch (Exception e){
            LOG.error("Error while fetching campaign from token : {} and status : {}",campaignToken,status,e);
        }
        return  null;
    }

    @RequestMapping(method = RequestMethod.POST, value = "generate-token", produces = MediaType.TEXT_PLAIN)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public String getCSRFToken(@RequestBody String url) throws DataNotFoundException {
        return csrftokenService.getToken(url);
    }

    @RequestMapping(method = RequestMethod.POST, value = "generate-token-crm", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public String getCSRFTokenCrm(@RequestBody String url) throws DataNotFoundException {
        return csrftokenService.getToken(url);
    }

    @RequestMapping(method = RequestMethod.POST, value = "order/settle-order", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Boolean updateOrderStatusByAdmin(@RequestBody String generatedOrderId) throws DataNotFoundException {
        return orderService.updateOrderStatus(generatedOrderId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "order/monthly/aov", produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public List<MonthlyAOVDetail> getPartnerMonthAndBrandWiseAOV(@RequestBody PartnerAOVRequest partnerAOVRequest){
        return orderService.getPartnerMonthAndBrandWiseAOV(partnerAOVRequest);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-orders-not-in-commission", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<Order> getOrdersNotInCommission(@RequestParam(required = false) String startDate, @RequestParam(required = false) String endDate){
        return  orderService.getOrdersWithoutCommission(startDate,endDate);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-monthly-orders-of-recipe", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Map<String, BigDecimal> getMonthlyOrdersOfRecipe(@RequestParam Integer recipeId, @RequestParam(required = false) String region) {
        return orderService.getMonthlyOrdersOfRecipe(recipeId, region);
    }


    @PostMapping(value = "save-events/wallet/suggestion")
    @ResponseBody
    public boolean saveWalletSuggestion(@RequestBody WalletEventData walletEventData){
        LOG.info("Request to store wallet suggestion data for customerId: {}",walletEventData.getCustomerId());
        if(walletEventData.getCustomerId() == null){
            LOG.error("CustomerId is null for saving walletEventData : {}",new Gson().toJson(walletEventData));
            return false;
        }
        return orderService.saveWalletSuggestion(walletEventData);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-loyalty-events", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<LoyaltyEvents> getLoyaltyEvents(@RequestParam String id,@RequestParam String limit,@RequestParam String searchType)
            throws DataNotFoundException{
        return  orderService.getLoyaltyEvents(id,Integer.valueOf(limit),searchType);
    }

    @RequestMapping(method = RequestMethod.GET,value = "get-order-payment-management",produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<OrderPaymentDetailData> getOrderPaymentDetail(@RequestParam(required = false) Integer customerId, @RequestParam(required = false) Integer orderId,
                                                              @RequestParam(required = false) String paymentSource , @RequestParam(required = false) String contactNumber,
                                                               @RequestParam Integer pageValue){
        return orderService.getOrderPaymentDetail(customerId,orderId,paymentSource,contactNumber,pageValue);
    }

    @RequestMapping(method = RequestMethod.POST, value = "order-payment-detail", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<OrderPaymentDetailData> getOrderPaymentDetailBySourceAndStatus(@RequestParam(required = true) String paymentSource, @RequestParam(required = true) String paymentStatus, @RequestParam(required = false) String startDate) throws ParseException {
        LOG.info("Getting Order Payment Detail for paymentSource {} and paymentStatus {} and startDate {}", paymentSource, paymentStatus, startDate);
//        Date startingDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(startDate);
        Date startingDate = null;
        if (Objects.nonNull(startDate)) {
            startingDate = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(startDate);
        }
        return orderService.getOrderPaymentDetailBySourceAndStatus(paymentSource, paymentStatus, startingDate);
    }

    @RequestMapping(method = RequestMethod.POST, value = "publish-unit-orders", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean publishOrdersForUnit(@RequestBody final String date, @RequestParam Integer unitId) {
        Date businessDate = Objects.nonNull(date) ? AppUtils.getDate(AppUtils.parseDateIST(date))
                : AppUtils.getCurrentBusinessDate();
        LOG.debug("request to publish orders for business date {} and unit id {}", businessDate, unitId);
        UnitBasicDetail unit = masterCache.getUnitsBasicDetails().get(unitId);
        if (UnitStatus.ACTIVE.equals(unit.getStatus())) {
            try {
                List<OrderInfo> orders = orderSearchService.getOrderToPublish(unit.getId(), businessDate);
                for (OrderInfo o : orders) {
                    publish(o);
                }
            } catch (Exception e) {
                LOG.error("Error while publishing orders for unit {}", unitId, e);
            }
        }
        return true;
    }


    @RequestMapping(method = RequestMethod.POST, value = "send-order-validation-failure-mail", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void sendOrderValidationFailedMail(@RequestBody final Order order) {
        orderService.sendEmailAfterRevalidationFailure(order, order.getRevalidationReason(), order.getOfferCode() + "_" + order.getCustomerId());
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean isSettledTabledOrder(Integer tableRequestId,OrderInfo info){
        UnitTableMappingDetail unitTableMappingDetail = null;
        if(Objects.nonNull(tableRequestId)) {
            try {
                unitTableMappingDetail = tableDataDao.findUnitTableMappingForTableRequestId(tableRequestId);
            } catch (Exception e) {
                LOG.info("Error in fetching UnitTableMapping detail for table request id : {}", tableRequestId);
            }
        }
        if(Objects.nonNull(unitTableMappingDetail) && Objects.nonNull(unitTableMappingDetail.getSettledOrderId())){
            if(info.getOrder().getOrderId().equals(unitTableMappingDetail.getSettledOrderId()) &&
            !TableStatus.CLOSED.name().equals(unitTableMappingDetail.getTableStatus())){
//                throw new DataUpdationException(
//                        "Order cannot be cancelled as it was already settled");
                return  true;
            }
        }
        return false;
    }

    public boolean hasPartnerPaymentOffer(Order order){
        return Objects.nonNull(order) &&
                Objects.nonNull(order.getOfferCode()) &&
                Objects.nonNull(props.getPartnerPaymentOfferCode()) &&
                order.getOfferCode().equals(props.getPartnerPaymentOfferCode());
    }

}

