/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.service.model;

import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.master.domain.model.UnitCategory;

import java.util.Map;

public class OrderStatusRequest {

	private int orderId;

	private int userId;

	private OrderStatus orderStatus;

	private Integer unitId;

	private UnitCategory orderSource;

	private Map<Integer, Integer> nonWastageItemMap;

	public int getOrderId() {
		return orderId;
	}

	public void setOrderId(int orderId) {
		this.orderId = orderId;
	}

	public int getUserId() {
		return userId;
	}

	public void setUserId(int userId) {
		this.userId = userId;
	}

	public OrderStatus getOrderStatus() {
		return orderStatus;
	}

	public void setOrderStatus(OrderStatus orderStatus) {
		this.orderStatus = orderStatus;
	}


	public Integer getUnitId() {
		return unitId;
	}

	public void setUnitId(Integer unitId) {
		this.unitId = unitId;
	}

	public UnitCategory getOrderSource() {
		return orderSource;
	}

	public void setOrderSource(UnitCategory orderSource) {
		this.orderSource = orderSource;
	}

	public Map<Integer, Integer> getNonWastageItemMap() {
		return nonWastageItemMap;
	}

	public void setNonWastageItemMap(Map<Integer, Integer> nonWastageItemMap) {
		this.nonWastageItemMap = nonWastageItemMap;
	}
}