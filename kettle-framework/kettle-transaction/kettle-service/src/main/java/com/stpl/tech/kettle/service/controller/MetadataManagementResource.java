/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.service.controller;

import com.stpl.tech.kettle.core.data.vo.VerificationResponse;
import com.stpl.tech.kettle.core.service.MetadataMangementService;
import com.stpl.tech.kettle.data.model.DeliveryPartner;
import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.master.domain.model.BillBookOrder;
import com.stpl.tech.master.domain.model.ConsumptionMetadata;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.ListData;
import com.stpl.tech.master.domain.model.ListDataTrim;
import com.stpl.tech.master.domain.model.ManualBillBook;
import com.stpl.tech.master.domain.model.PartnerDetail;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.util.AppConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.ws.rs.core.MediaType;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.stpl.tech.kettle.service.core.ServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.service.core.ServiceConstants.METADATA_MANAGEMENT_ROOT_CONTEXT;
import static com.stpl.tech.kettle.service.core.ServiceConstants.SEPARATOR;

/**
 * Root resource (exposed at "metadata-mangement" path)
 */
@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + METADATA_MANAGEMENT_ROOT_CONTEXT)
public class MetadataManagementResource extends AbstractResources {

    private static final Logger LOG = LoggerFactory.getLogger(MetadataManagementResource.class);

    @Autowired
    private MetadataMangementService metadataService;

    @RequestMapping(method = RequestMethod.GET, value = "delivery-partner", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<IdCodeName> getAllDeliveyPartners() throws DataNotFoundException, AuthenticationFailureException {
        LOG.info("Request to get all Delivery Partners");
        return metadataService.getAllDeliveryPartners();
    }

    @RequestMapping(method = RequestMethod.POST, value = "delivery-partner/deactivate", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean deactivateDeliveryPartner(@RequestBody final int partnerId)
            throws AuthenticationFailureException, DataNotFoundException {
        LOG.info("Request to deactivate Delivery Partner " + partnerId);
        return metadataService.changeDeliveryPartnerStatus(partnerId, AppConstants.IN_ACTIVE);
    }

    @RequestMapping(method = RequestMethod.POST, value = "delivery-partner/activate", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean activateDeliveryPartner(@RequestBody final int partnerId)
            throws AuthenticationFailureException, DataNotFoundException {
        LOG.info("Request to activate Delivery Partner " + partnerId);
        return metadataService.changeDeliveryPartnerStatus(partnerId, AppConstants.ACTIVE);
    }

    @RequestMapping(method = RequestMethod.POST, value = "delivery-partner/update", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public IdCodeName updateDeliveryPartner(@RequestBody final DeliveryPartner deliveryPartner)
            throws AuthenticationFailureException, DataNotFoundException {
        LOG.info("Request to update Delivery Partner " + deliveryPartner.getPartnerDisplayName());
        return metadataService.updateDeliveryPartner(deliveryPartner);
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/delivery-partner", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<PartnerDetail> getAllDeliveyPartnersForUnit(@RequestBody final int unitId)
            throws AuthenticationFailureException, DataNotFoundException {
        LOG.info("Request to get all Delivery Partner for Unit " + unitId);
        return metadataService.getAllDeliveyPartnersForUnit(unitId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/delivery-partner/update", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean updateDeliveryPartnerForUnit(@RequestBody final Unit unit)
            throws AuthenticationFailureException, DataNotFoundException {
        LOG.info("Request to update Delivery Partner for Unit " + unit.getId());
        return metadataService.updateDeliveryPartnerForUnit(unit);
    }

    //Moved to MasterMetadataResource
	/*@RequestMapping(method = RequestMethod.GET, value = "channel-partner", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<IdCodeName> getAllChannelPartners() throws DataNotFoundException, AuthenticationFailureException {
		LOG.info("Request to get all Channel Partners");
		return channelPatrtnerService.getAllChannelPartners(AppUtils.getBusinessDate());
	}

	@RequestMapping(method = RequestMethod.POST, value = "channel-partner/update", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public IdCodeName updateChannelPartner(@RequestBody final ChannelPartner channelPartner)
			throws AuthenticationFailureException, DataNotFoundException {
		LOG.info("Request to update Channel Partner " + channelPartner.getPartnerDisplayName());
		return channelPatrtnerService.updateChannelPartner(channelPartner, AppUtils.getBusinessDate());
	}*/

    @RequestMapping(method = RequestMethod.POST, value = "create-manual-bill-book", produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public boolean createManaulBillBookEntry(@RequestBody final ManualBillBook manualBillBook)
            throws AuthenticationFailureException, DataNotFoundException {
        LOG.info("Request to create Manual Bill Book Detail " + manualBillBook.getGeneratedForUnitId());
        boolean result = metadataService.createManualBillBookEntry(manualBillBook);
        return result;
    }

    @RequestMapping(method = RequestMethod.POST, value = "cancel-manual-bill-book", produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public boolean cancelManaulBillBookEntry(@RequestBody final int transferOrderId)
            throws AuthenticationFailureException, DataNotFoundException {
        LOG.info("Request to cancel Manual Bill Book Detail " + transferOrderId);
        boolean result = metadataService.cancelManualBillBookEntry(transferOrderId);
        return result;
    }

    @RequestMapping(method = RequestMethod.POST, value = "update-manual-bill-book", produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public boolean updateManaulBillBookEntry(@RequestBody final ManualBillBook manualBillBook)
            throws AuthenticationFailureException, DataNotFoundException {
        LOG.info("Request to update Manual Bill Book Detail " + manualBillBook.getGeneratedForUnitId());
        boolean result = metadataService.updateManualBillBookEntry(manualBillBook);
        return result;
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-manual-bill-book-getail", produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public List<ManualBillBook> getManualBillBookDetail(@RequestBody final int unitId)
            throws AuthenticationFailureException, DataNotFoundException {
        LOG.info("Request for Manual Bill Book Detail " + unitId);
        return metadataService.getManualBillBookDetail(unitId, false);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-all-manual-bill-book-detail", produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public List<ManualBillBook> getAllManualBillBookDetail(@RequestBody final int unitId)
            throws AuthenticationFailureException, DataNotFoundException {
        LOG.info("Request for All Manual Bill Book Detail " + unitId);
        return metadataService.getManualBillBookDetail(unitId, true);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-all-manual-bill-detail", produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public List<BillBookOrder> getAllOrdersForBillBook(@RequestBody final int billBookId)
            throws AuthenticationFailureException, DataNotFoundException {
        LOG.info("Request for All Manual Bill Book Orders " + billBookId);
        return metadataService.getAllOrdersForBillBook(billBookId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "validate-manual-bill-book", produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public boolean validateManaulBillBookEntry(@RequestBody final ManualBillBook manualBillBook) {
        LOG.info("Request to validate Manual Bill Book Detail " + manualBillBook.getStartNo());
        boolean result = metadataService.validateManualBillBookEntry(manualBillBook);
        return result;
    }

    @RequestMapping(method = RequestMethod.POST, value = "validate-bill-book-no", produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public VerificationResponse validateBillBookNo(@RequestBody final HashMap<String, Integer> dataMap) {
        LOG.info("Validating Bill Book No" + dataMap);
        return metadataService.validateManualBillBookNo(dataMap.get("manualBillBookNo"), dataMap.get("unitId"));
    }

    @RequestMapping(method = RequestMethod.GET, value = "consumable-codes", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Map<String, Map<Integer, Map<String, ConsumptionMetadata>>> getConsumableCodes(
            @RequestParam(required = false) final String allSachetsFlag) {
        LOG.info("Request for getting Consumable Codes: {}", allSachetsFlag);
        return metadataService.getConsumableCodes(allSachetsFlag);
    }

    @RequestMapping(method = RequestMethod.GET, value = "list-by-group", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<ListData> getListDataByGroupName(@RequestParam String gName) {
        LOG.info("Request for getting Consumable Codes");
        return metadataService.getListDataByGroupName(gName);
    }

    @RequestMapping(method = RequestMethod.GET, value = "list-by-grp-cat", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<ListDataTrim> getListDataByGroupNameAndCat(@RequestParam String gName, @RequestParam String cat) {
        LOG.info("Request for getting Consumable Codes");
        return metadataService.getListDataByGroupNameAndCat(gName,cat);
    }

    @RequestMapping(method = RequestMethod.POST, value = "refresh-consumable-codes", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean refreshConsumableCodes() {
        LOG.info("Request for refreshing Consumable Codes");
        return metadataService.refreshConsumableCodes();
    }
}
