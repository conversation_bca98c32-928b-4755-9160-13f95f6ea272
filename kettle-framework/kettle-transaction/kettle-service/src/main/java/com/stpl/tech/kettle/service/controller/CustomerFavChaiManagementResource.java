package com.stpl.tech.kettle.service.controller;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.stpl.tech.kettle.core.exception.CardValidationException;
import com.stpl.tech.kettle.core.service.CustomerFavChaiManagementService;
import com.stpl.tech.kettle.domain.model.CustomerFavChaiMappingVO;
import com.stpl.tech.kettle.domain.model.SaveCustomerFavChaiRequest;
import com.stpl.tech.kettle.domain.model.SaveCustomerFavChaiResponse;
import com.stpl.tech.kettle.domain.model.SaveCustomerFavChaiResponseStatus;
import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.TemplateRenderingException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import javax.jms.JMSException;
import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

import static com.stpl.tech.kettle.service.core.ServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.service.core.ServiceConstants.CUSTOMER_FAV_CHAI_MANAGEMENT_ROOT_CONTEXT;
import static com.stpl.tech.kettle.service.core.ServiceConstants.SEPARATOR;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + CUSTOMER_FAV_CHAI_MANAGEMENT_ROOT_CONTEXT) //v1/customer-fav-chai-management
public class CustomerFavChaiManagementResource {

    private static final Logger LOG = LoggerFactory.getLogger(CustomerFavChaiManagementResource.class);

    @Autowired
    private CustomerFavChaiManagementService customerFavChaiManagementService;

    @RequestMapping(method = RequestMethod.POST, value = "save-my-fav-chai", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public SaveCustomerFavChaiResponse saveCustomerFavChai(@RequestBody Object customerFavChaiRequest) throws AuthenticationFailureException, DataUpdationException, TemplateRenderingException, DataNotFoundException, DataIntegrityViolationException, CardValidationException, IOException, JMSException {
        SaveCustomerFavChaiResponse saveCustomerFavChaiResponse = new SaveCustomerFavChaiResponse();
        String requestJson = new Gson().toJson(customerFavChaiRequest);
        ObjectMapper mapper = new ObjectMapper();
        mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        SaveCustomerFavChaiRequest saveCustomerFavChaiRequest = null;
        try{
            saveCustomerFavChaiRequest = mapper.readValue(requestJson, SaveCustomerFavChaiRequest.class);
            if(Objects.nonNull(saveCustomerFavChaiRequest) && Objects.nonNull(saveCustomerFavChaiRequest.getCustomerBasicInfo())){
                int customerId = saveCustomerFavChaiRequest.getCustomerBasicInfo().getCustomerId();
                LOG.info("Request to save customerFavChai for customerId {}: and status {}",customerId,saveCustomerFavChaiRequest.isFavChaiMarked());
                return customerFavChaiManagementService.saveCustomerFavChai(customerId,saveCustomerFavChaiRequest);
            }
        }catch (IOException e){
            LOG.error("Error parsing customer fav chai request:", e);
        };
        saveCustomerFavChaiResponse.setSaveCustomerFavChaiResponseStatus(SaveCustomerFavChaiResponseStatus.valueOf("UNABLE_TO_SAVE_FAV_CHAI"));
        saveCustomerFavChaiResponse.setCreatedAt(AppUtils.getCurrentTimestamp());
        return saveCustomerFavChaiResponse;
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-customer-active-fav-chai-mappings", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<CustomerFavChaiMappingVO> getAllActiveCustomerFavChai(@RequestParam int customerId) throws AuthenticationFailureException, DataUpdationException, TemplateRenderingException, DataNotFoundException, DataIntegrityViolationException, CardValidationException, IOException, JMSException{
        try{
            LOG.info("Getting Active Customer Fav Chai Mappings for :::{}",customerId);
            return customerFavChaiManagementService.getAllActiveCustomerFavChaiMappings(customerId);
        }catch(Exception e){
            LOG.error("Exception while getting active customer fav chai mappings for customerId :::{} and exception thrown is :{}",customerId,e);
            return null;
        }
    }

    @PostMapping(value = "update-incorrect-dine-in-saved-chais")
    public boolean updateIncorrectDineInSavedChais(){
        try{
            LOG.info("Updating Incorrect Dine in Saved Chais :::::::::");
            return customerFavChaiManagementService.updateIncorrectFavChaiFromDineIn();
        }catch(Exception e ){
            LOG.error("Exception while updating dine in  saved chai:::::::", e);
            return false ;
        }
    }
}

