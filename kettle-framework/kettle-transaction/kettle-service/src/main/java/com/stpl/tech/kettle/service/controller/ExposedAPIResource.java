/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.service.controller;

import com.google.gson.JsonObject;
import com.stpl.tech.kettle.core.data.vo.OrderFetchStrategy;
import com.stpl.tech.kettle.core.service.CustomerOfferManagementService;
import com.stpl.tech.kettle.core.service.OrderSearchService;
import com.stpl.tech.kettle.customer.service.CustomerService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.ObjectFactory;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.domain.model.TransactionDetail;
import com.stpl.tech.kettle.domain.model.OrderDetailEventRequest;
import com.stpl.tech.kettle.xml.model.PartnerSaleRecord;
import com.stpl.tech.kettle.xml.model.PartnerSaleRecords;
import com.stpl.tech.kettle.xml.model.SaleRecord;
import com.stpl.tech.kettle.xml.model.SaleRecords;
import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.exception.DuplicateRequestException;
import com.stpl.tech.master.core.external.acl.service.TokenService;
import com.stpl.tech.master.core.external.acl.service.impl.JWTToken;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.offer.service.OfferManagementService;
import com.stpl.tech.master.core.service.model.RequestData;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.MediaType;
import javax.xml.bind.JAXBException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.stpl.tech.kettle.service.core.ServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.service.core.ServiceConstants.EXPOSED_API;
import static com.stpl.tech.kettle.service.core.ServiceConstants.SEPARATOR;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + EXPOSED_API)
public class ExposedAPIResource {

	private static final Logger LOG = LoggerFactory.getLogger(ExposedAPIResource.class);

	private static final Map<String, Long> apiTraffic = new HashMap<>();

	@Autowired
	private CustomerService customerService;

	@Autowired
	private CustomerOfferManagementService customerOfferService;

	@Autowired
	private OrderSearchService orderSearchService;

	@Autowired(required = false)
	private OfferManagementService offerService;

	@Autowired(required = false)
	private TokenService<JWTToken> jwtService;

	@Autowired(required = false)
	private MasterDataCache masterCache;

	@Autowired
	private EnvironmentProperties environmentProperties;

	private ObjectFactory objectFactory = new ObjectFactory();

	@RequestMapping(method = RequestMethod.POST, value = "register/customer", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean registerCustomer(@RequestBody final RequestData request)
			throws AuthenticationFailureException, DataUpdationException {
		// validate(request.getSession());
		JsonObject map = getData(request, JsonObject.class);
		String name = map.get("name").getAsString();
		String contact = map.get("contact").getAsString();
		String email = map.get("email").getAsString();

		String key = map.get("key").getAsString();
		if (key == null || name == null || contact == null) {
			return false;
		}

		IdCodeName marketingPartner = offerService.getMarketingPartner(key);

		if (marketingPartner == null) {
			return false;
		}

		String source = AppConstants.MARKETING_PARTNER;
		String token = marketingPartner.getName();

		Customer customer = customerService.getCustomer(contact);

		// register customer
		if (customer == null) {
			LOG.info("Registering Customer name: {}, contact: {} ,email: {} , Marketing Partner: {}", name, contact,
					email, marketingPartner.getName());
			customer = customerService.addCustomer(getCustomer(null, contact, name, email, source, token));
		}

		// associate with coupon
		String coupon = map.get("coupon").getAsString();
		if (coupon != null && customer != null) {
			LOG.info("Adding Coupon Mapping for : coupon {}, contact: {}", coupon, contact);
			return customerOfferService.addCouponMappingTypeCustomer(coupon, customer);
		}
		return true;
	}

	private final Customer getCustomer(Integer unitId, String contactNumber, String name, String email, String source,
			String token) {
		Customer customer = objectFactory.createCustomer();
		customer.setContactNumber(contactNumber);
		customer.setFirstName(name);
		customer.setCountryCode(AppConstants.DEFAULT_COUNTRY_CODE);
		customer.setRegistrationUnitId(unitId);
		customer.setAcquisitionSource(source);
		customer.setAcquisitionToken(token);
		return customer;
	}

	public <T> T getData(RequestData request, Class<T> clazz) throws AuthenticationFailureException {
		return JSONSerializer.toJSON(request.getData(), clazz);
	}

	//@RequestMapping(method = RequestMethod.GET, value = "sales/hourly", produces = MediaType.APPLICATION_XML)
	//@ResponseStatus(HttpStatus.OK)
	//@ResponseBody
	public SaleRecords generateHourlyReportsXML(HttpServletRequest request)
			throws DataNotFoundException, JAXBException, AuthenticationFailureException, DuplicateRequestException {

		return generateHourlyReports("sales/hourly", request);

	}

	//@RequestMapping(method = RequestMethod.GET, value = "json/sales/partner/hourly", produces = MediaType.APPLICATION_JSON)
	//@ResponseStatus(HttpStatus.OK)
	//@ResponseBody
	public PartnerSaleRecords generatePartnerHourlyReportsXML(HttpServletRequest request)
			throws DataNotFoundException, JAXBException, AuthenticationFailureException, DuplicateRequestException {

		return generatePartnerHourlyReports("json/sales/partner/hourly", request);

	}

	//@RequestMapping(method = RequestMethod.GET, value = "json/sales/hourly", produces = MediaType.APPLICATION_JSON)
	//@ResponseStatus(HttpStatus.OK)
	//@ResponseBody
	public SaleRecords generateHourlyReportsJSON(HttpServletRequest request)
			throws DataNotFoundException, JAXBException, AuthenticationFailureException, DuplicateRequestException {

		return generateHourlyReports("json/sales/hourly", request);
	}

	public SaleRecords generateHourlyReports(String api, HttpServletRequest request)
			throws DataNotFoundException, JAXBException, AuthenticationFailureException, DuplicateRequestException {

		String authHeader = request.getHeader("auth") != null ? request.getHeader("auth").trim() : null;
		SaleRecords list = null;
		Date currentTime = AppUtils.getCurrentTimestamp();
		Date startTime = AppUtils.getStartOfPreviousHour(currentTime);
		Date endTime = AppUtils.getEndOfPreviousHour(currentTime);

		if (authHeader != null && !authHeader.equals("null") && !authHeader.equals("")) {
			JWTToken jwtToken = new JWTToken();
			jwtService.parseToken(jwtToken, authHeader);
			String sessionKey = jwtToken.getSessionKey();
			int terminalId = Integer.valueOf(jwtToken.getTerminalId());
			int userId = Integer.valueOf(jwtToken.getUserId());
			int unitId = Integer.valueOf(jwtToken.getUnitId());
			LOG.info("Generating sales report for unit {} and StartTime {} and EndTime{} and api ", unitId, startTime,
					endTime, api);
			validateTraffic(api, unitId, userId);
			list = generatePathfinderReport(unitId, startTime, endTime);
		} else {
			throw new AuthenticationFailureException("Unauthorised Access");
		}

		return list;
	}

	public PartnerSaleRecords generatePartnerHourlyReports(String api, HttpServletRequest request)
			throws DataNotFoundException, JAXBException, AuthenticationFailureException, DuplicateRequestException {

		String authHeader = request.getHeader("auth") != null ? request.getHeader("auth").trim() : null;
		PartnerSaleRecords list = null;
		Date currentTime = AppUtils.getCurrentTimestamp();
		Date startTime = AppUtils.getStartOfPreviousHour(currentTime);
		Date endTime = AppUtils.getEndOfPreviousHour(currentTime);

		if (authHeader != null && !authHeader.equals("null") && !authHeader.equals("")) {
			JWTToken jwtToken = new JWTToken();
			jwtService.parseToken(jwtToken, authHeader);
			String sessionKey = jwtToken.getSessionKey();
			int terminalId = Integer.valueOf(jwtToken.getTerminalId());
			int userId = Integer.valueOf(jwtToken.getUserId());
			int unitId = Integer.valueOf(jwtToken.getUnitId());
			LOG.info("Generating sales report for unit {} and StartTime {} and EndTime{} and api ", unitId, startTime,
					endTime, api);
			validateTraffic(api, unitId, userId);
			list = generatePartnerSaleReport(unitId, startTime, endTime);
		} else {
			throw new AuthenticationFailureException("Unauthorised Access");
		}

		return list;
	}

	private void validateTraffic(String api, int unitId, int userId)
			throws DuplicateRequestException, AuthenticationFailureException {
		if (unitId == 0 || userId == 0) {
			throw new AuthenticationFailureException("Unauthorised Access");
		}
		String key = getKey(unitId, userId);
		if (apiTraffic.containsKey(key)) {
			Long l = apiTraffic.get(key);
			if (l < System.currentTimeMillis()) {
				// 45 Minutes delay
				apiTraffic.put(key, System.currentTimeMillis() + 2700000);
			} else {
				LOG.info("Blocked call for unit {} because of duplicate request with api{}", unitId, api);
				throw new DuplicateRequestException("Duplicate Request for Sales Data");
			}
		} else {
			// 45 Minutes Delay
			apiTraffic.put(key, System.currentTimeMillis() + 2700000);
		}
	}

	private String getKey(int unitId, int userId) {
		return unitId + "@@" + userId;
	}

	//@RequestMapping(method = RequestMethod.GET, value = "sales/date", produces = MediaType.APPLICATION_XML)
	//@ResponseStatus(HttpStatus.OK)
	//@ResponseBody
	public SaleRecords generatePathfinderReportForDayXML(HttpServletRequest request, @RequestParam String businessDate)
			throws DataNotFoundException, JAXBException, AuthenticationFailureException, DuplicateRequestException {
		return generatePathfinderReportForDay("sales/date", request, businessDate);
	}

	//@RequestMapping(method = RequestMethod.GET, value = "json/sales/date", produces = MediaType.APPLICATION_JSON)
	//@ResponseStatus(HttpStatus.OK)
	//@ResponseBody
	public SaleRecords generatePathfinderReportForDayJSON(HttpServletRequest request, @RequestParam String businessDate)
			throws DataNotFoundException, JAXBException, AuthenticationFailureException, DuplicateRequestException {

		return generatePathfinderReportForDay("json/sales/date", request, businessDate);

	}

	//@RequestMapping(method = RequestMethod.GET, value = "json/sales/partner/date", produces = MediaType.APPLICATION_JSON)
	//@ResponseStatus(HttpStatus.OK)
	//@ResponseBody
	public PartnerSaleRecords generatePartnerSalesReportForDayJSON(HttpServletRequest request,
			@RequestParam String businessDate)
			throws DataNotFoundException, JAXBException, AuthenticationFailureException, DuplicateRequestException {

		return generatePartnerReportForDay("json/sales/partner/date", request, businessDate);

	}

	public PartnerSaleRecords generatePartnerReportForDay(String api, HttpServletRequest request,
			@RequestParam String businessDate)
			throws DataNotFoundException, JAXBException, AuthenticationFailureException, DuplicateRequestException {
		Date date = AppUtils.parseDate(businessDate);
		String authHeader = request.getHeader("auth") != null ? request.getHeader("auth").trim() : null;
		PartnerSaleRecords list = null;
		Date startTime = AppUtils.getStartOfDay(date);
		Date endTime = AppUtils.getEndOfDay(date);

		if (authHeader != null && !authHeader.equals("null") && !authHeader.equals("")) {
			JWTToken jwtToken = new JWTToken();
			jwtService.parseToken(jwtToken, authHeader);
			String sessionKey = jwtToken.getSessionKey();
			int terminalId = Integer.valueOf(jwtToken.getTerminalId());
			int userId = Integer.valueOf(jwtToken.getUserId());
			int unitId = Integer.valueOf(jwtToken.getUnitId());
			LOG.info("Generating sales report for unit {} and StartTime {} and EndTime{} and api ", unitId, startTime,
					endTime, api);
			validateTraffic(api, unitId, userId);
			list = generatePartnerSaleReport(unitId, startTime, endTime);
		} else {
			throw new AuthenticationFailureException("Unauthorised Access");
		}

		return list;

	}

	public SaleRecords generatePathfinderReportForDay(String api, HttpServletRequest request,
			@RequestParam String businessDate)
			throws DataNotFoundException, JAXBException, AuthenticationFailureException, DuplicateRequestException {
		Date date = AppUtils.parseDate(businessDate);
		String authHeader = request.getHeader("auth") != null ? request.getHeader("auth").trim() : null;
		SaleRecords list = null;
		Date startTime = AppUtils.getStartOfDay(date);
		Date endTime = AppUtils.getEndOfDay(date);

		if (authHeader != null && !authHeader.equals("null") && !authHeader.equals("")) {
			JWTToken jwtToken = new JWTToken();
			jwtService.parseToken(jwtToken, authHeader);
			String sessionKey = jwtToken.getSessionKey();
			int terminalId = Integer.valueOf(jwtToken.getTerminalId());
			int userId = Integer.valueOf(jwtToken.getUserId());
			int unitId = Integer.valueOf(jwtToken.getUnitId());
			LOG.info("Generating sales report for unit {} and StartTime {} and EndTime{} and api ", unitId, startTime,
					endTime, api);
			validateTraffic(api, unitId, userId);
			list = generatePathfinderReport(unitId, startTime, endTime);
		} else {
			throw new AuthenticationFailureException("Unauthorised Access");
		}

		return list;

	}

	private SaleRecords generatePathfinderReport(int unitId, Date startTime, Date endTime)
			throws DataNotFoundException, JAXBException {
		OrderFetchStrategy strategy = new OrderFetchStrategy(false, true, false, false, false, null, -1, true);
		List<Order> orders = orderSearchService.getOrderDetails(unitId, startTime, endTime, strategy);
		SaleRecords sr = new SaleRecords();
		sr.setRecords(new ArrayList<SaleRecord>());
		for (Order o : orders) {
			if (OrderStatus.CANCELLED.equals(o.getStatus())) {
				continue;
			}
			int zeroTaxItemCount = 0;
			for (OrderItem i : o.getOrders()) {
				if (AppUtils.isGiftCard(i.getCode())) {
					zeroTaxItemCount = zeroTaxItemCount + 1;
				}
			}
			if (zeroTaxItemCount >= o.getOrders().size()) {
				// skip for only zero tax bill
				continue;
			}
			SaleRecord r = createSaleRecord(o);
			sr.getRecords().add(r);
		}
		return sr;
	}

	private PartnerSaleRecords generatePartnerSaleReport(int unitId, Date startTime, Date endTime)
			throws DataNotFoundException, JAXBException {
		OrderFetchStrategy strategy = new OrderFetchStrategy(false, true, false, false, false, null, -1, true);
		List<Order> orders = orderSearchService.getOrderDetails(unitId, startTime, endTime, strategy);
		PartnerSaleRecords sr = new PartnerSaleRecords();
		sr.setPartnerRecords(new ArrayList<PartnerSaleRecord>());
		for (Order o : orders) {
			if (OrderStatus.CANCELLED.equals(o.getStatus())) {
				continue;
			}
			int zeroTaxItemCount = 0;
			for (OrderItem i : o.getOrders()) {
				if (AppUtils.isGiftCard(i.getCode())) {
					zeroTaxItemCount = zeroTaxItemCount + 1;
				}
			}
			if (zeroTaxItemCount >= o.getOrders().size()) {
				// skip for only zero tax bill
				continue;
			}
			PartnerSaleRecord r = createPartnerSaleRecord(o);
			sr.getPartnerRecords().add(r);
		}
		return sr;
	}

	private SaleRecord createSaleRecord(Order o) {
		SaleRecord r = new SaleRecord();
		TransactionDetail td = o.getTransactionDetail();

		BigDecimal netSales = BigDecimal.ZERO;
		BigDecimal discount = BigDecimal.ZERO;
		BigDecimal paidAmount = BigDecimal.ZERO;
		BigDecimal zeroTaxAmount = BigDecimal.ZERO;

		for (OrderItem i : o.getOrders()) {
			if (AppUtils.isGiftCard(i.getCode())) {
				zeroTaxAmount = zeroTaxAmount.add(i.getPrice().multiply(new BigDecimal(i.getQuantity())));
			}
		}
		discount = td.getDiscountDetail().getPromotionalOffer().add(td.getDiscountDetail().getDiscount().getValue());
		netSales = td.getTaxableAmount().subtract(zeroTaxAmount);
		paidAmount = netSales.add(o.getTransactionDetail().getTax());

		r.setReceiptNumber(String.valueOf(o.getOrderId()));
		r.setReceiptDate(AppUtils.generateSimpleDateFormat(o.getBillingServerTime()));
		r.setTransactionTime(AppUtils.generateSimpleTimeFormat(o.getBillingServerTime()));
		r.setInvoiceAmount(getValue(netSales));
		r.setDiscountAmount(getValue(discount));
		r.setVATAmount(getValue(o.getTransactionDetail().getTax()));
		r.setServiceTaxAmount(getValue(null));
		r.setServiceChargeAmount(getValue(null));
		r.setNetSale(getValue(paidAmount));
		r.setPaymentMode("CASH");
		r.setTransactionStatus("SALE");
		return r;
	}

	private PartnerSaleRecord createPartnerSaleRecord(Order o) {
		PartnerSaleRecord r = new PartnerSaleRecord();
		TransactionDetail td = o.getTransactionDetail();

		BigDecimal netSales = BigDecimal.ZERO;
		BigDecimal discount = BigDecimal.ZERO;
		BigDecimal paidAmount = BigDecimal.ZERO;
		BigDecimal zeroTaxAmount = BigDecimal.ZERO;

		for (OrderItem i : o.getOrders()) {
			if (AppUtils.isGiftCard(i.getCode())) {
				zeroTaxAmount = zeroTaxAmount.add(i.getPrice().multiply(new BigDecimal(i.getQuantity())));
			}
		}
		netSales = td.getTaxableAmount().subtract(zeroTaxAmount);
		discount = AppUtils.subtract(td.getTotalAmount().subtract(zeroTaxAmount), netSales);
		paidAmount = netSales.add(o.getTransactionDetail().getTax());
		r.setOrderId(String.valueOf(o.getOrderId()));
		r.setBillDate(AppUtils.generateSimpleDateFormat(AppUtils.getBusinessDate()));
		r.setNetSales(getValue(netSales));
		r.setDiscount(getValue(discount));
		r.setTotalTax(getValue(o.getTransactionDetail().getTax()));
		r.setGrossAmount(getValue(paidAmount));
		r.setAggregatorOrderId(o.getSourceId());
		r.setArea(masterCache.getChannelPartner(o.getChannelPartner()).getCode());
		r.setBrandId(masterCache.getBrandMetaData().get(o.getBrandId()).getBrandCode());
		r.setContainerCharges("0");
		r.setInvoiceNumber(o.getGenerateOrderId());
		r.setOrderId(String.valueOf(o.getOrderId()));
		r.setMenuPrice(getValue(o.getTransactionDetail().getTotalAmount()));
		r.setOrderType(o.getSource());
		r.setRestaurant(masterCache.getUnit(o.getUnitId()).getName());
		return r;
	}

	private String getValue(BigDecimal value) {
		return String.valueOf(value != null ? value.setScale(2, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO);
	}

	@RequestMapping(method = RequestMethod.GET, value = "check-auth", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public ResponseEntity<Integer> verificationRequestFromFB(@RequestParam(name = "hub.mode") String mode,
			@RequestParam(name = "hub.verify_token") String token,
			@RequestParam(name = "hub.challenge") Integer challengeNumber) {
		LOG.info("Verification Request from FB", AppUtils.getCurrentTimestamp());
		if ("subscribe".equals(mode) && environmentProperties.getFacebookLeadsVerificationToken().equals(token)) {
			LOG.info("Verified");
			return new ResponseEntity<>(challengeNumber, HttpStatus.OK);
		} else {
			LOG.error("Error at Verification Request from FB", AppUtils.getCurrentTimestamp());
			return new ResponseEntity<>(HttpStatus.BAD_REQUEST);
		}
	}

	@RequestMapping(method = RequestMethod.GET, value = "get-partner-order-detail",produces = MediaType.TEXT_PLAIN)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public String getPartnerOrderDetail(@RequestParam String partnerSourceId,@RequestParam String issueType){
		 return orderSearchService.getPartnerOrderDetail(partnerSourceId,issueType);
	}

	@RequestMapping(method = RequestMethod.GET,value = "fetch-partner-order-detail",produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public Map<String,Object> fetchPartnerOrderDetail(@RequestParam String partnerSourceId,@RequestParam String issueType){
		return orderSearchService.fetchPartnerOrderDetail(partnerSourceId,issueType);
	}

	@RequestMapping(method = RequestMethod.POST,value = "publish-order-detail",consumes = MediaType.APPLICATION_JSON,produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean publishPartnerOrderDetail(@RequestBody OrderDetailEventRequest req){
		return orderSearchService.publishPartnerOrderDetail(req);
	}
}
