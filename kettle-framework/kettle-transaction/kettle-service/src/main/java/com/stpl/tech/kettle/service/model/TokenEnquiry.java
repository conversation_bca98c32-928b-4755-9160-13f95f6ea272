package com.stpl.tech.kettle.service.model;

public class TokenEnquiry {

	private String contactNumber;
	private String accessCode;
	private boolean valid;
	private String reasonForDecline;

	public String getContactNumber() {
		return contactNumber;
	}

	public void setContactNumber(String contactNumber) {
		this.contactNumber = contactNumber;
	}

	public String getAccessCode() {
		return accessCode;
	}

	public void setAccessCode(String accessCode) {
		this.accessCode = accessCode;
	}

	public boolean isValid() {
		return valid;
	}

	public void setValid(boolean valid) {
		this.valid = valid;
	}

	public String getReasonForDecline() {
		return reasonForDecline;
	}

	public void setReasonForDecline(String reasonForDecline) {
		this.reasonForDecline = reasonForDecline;
	}

}
