/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.service.core;

public class ServiceConstants {

    public static final String API_VERSION = "v1";

    public static final String API_VERSION_V2 = "v2";

    public static final String API_VERSION_V3 = "v3";

    public static final String SEPARATOR = "/";

    public static final String PRINTER_ROOT_CONTEXT = "printer";

    public static final String OFFER_MANAGEMENT_ROOT_CONTEXT = "offer-management";

    public static final String POS_METADATA_ROOT_CONTEXT = "pos-metadata";

    public static final String REPORT_METADATA_ROOT_CONTEXT = "report-metadata";

    public static final String ORDER_MANAGEMENT_ROOT_CONTEXT = "order-management";

    public static final String DATA_LOAD_ROOT_CONTEXT = "load-resources";

    public static final String PAYMENT_MANAGEMENT_ROOT_CONTEXT = "payment-management";

    public static final String SUBSCRIPTION_MANAGEMENT_ROOT_CONTEXT = "subscription-management";

    public static final String DELIVERY_MANAGEMENT_ROOT_CONTEXT = "delivery-management";

    public static final String EXTERNAL_ROOT_CONTEXT = "external";

    public static final String HEALTH_MONITOR_ROOT_CONTEXT = "health-monitor";

    public static final String AUTHORIZATION_SERVICES_ROOT_CONTEXT = "authorization";

    public static final String CUSTOMER_OFFER_MANAGEMENT_ROOT_CONTEXT = "customer-offer-management";

    public static final String DINE_IN_RESOURCE_ROOT_CONTEXT = "dine-in-management";

    public static final String CASH_MANAGEMENT_ROOT_CONTEXT = "cash-management";

    public static final String WORKSTATION_SERVICES_ROOT_CONTEXT = "workstation-management";

    public static final String EXPOSED_API = "external";

    public static final String METADATA_MANAGEMENT_ROOT_CONTEXT = "metadata-management";

    public static final String WEB_ORDER_ROOT_CONTEXT = "web-order";

    public static final String RULE_ROOT_CONTEXT = "rules";

    public static final String GIFT_CARD_MANAGEMENT_ROOT_CONTEXT = "gift-card-management";

    public static final String EXPENSE_MANAGEMENT_ROOT_CONTEXT = "expense-management";

    public static final String BUDGET_METADATA_ROOT_CONTEXT = "budget-metadata";

    public static final String PNL_ADJUSTMET_ROOT_CONTEXT = "pnl-adjustment";

    public static final String STOCK_EVENT_REPORT_ROOT_CONTEXT = "event";

    public static final String CUSTOMER_FAV_CHAI_MANAGEMENT_ROOT_CONTEXT = "customer-fav-chai-management";

    public static final String GAMIFIED_OFFER_RESOURCE = "gamified-offer-resource";

    public static final String DROOL_RESOURCE = "drool-resource";

    public static final String RECOM_OFFER_RESOURCE = "recom-resource";

    public static final String CASHBACK_OFFER_RESOURCE = "cashbk-offer-resource";

    public static final String SUPER_U_RESOURCE = "superu-resource";
}
