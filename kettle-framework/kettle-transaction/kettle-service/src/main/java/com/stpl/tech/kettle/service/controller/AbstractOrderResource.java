/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.service.controller;

import com.amazon.sqs.javamessaging.SQSSession;
import com.amazonaws.regions.Regions;
import com.google.common.base.Stopwatch;
import com.google.gson.Gson;
import com.google.gson.internal.LinkedTreeMap;
import com.stpl.tech.kettle.clevertap.converter.CleverTapConverter;
import com.stpl.tech.kettle.clevertap.domain.model.CleverTapPushResponse;
import com.stpl.tech.kettle.clevertap.service.CleverTapDataPushService;
import com.stpl.tech.kettle.clevertap.util.CleverTapConstants;
import com.stpl.tech.kettle.clevertap.util.CleverTapEvents;
import com.stpl.tech.kettle.core.CampaignStrategy;
import com.stpl.tech.kettle.core.CustomerRepeatType;
import com.stpl.tech.kettle.core.FeedbackEventType;
import com.stpl.tech.kettle.core.FeedbackSource;
import com.stpl.tech.kettle.core.ReceiptType;
import com.stpl.tech.kettle.core.TransactionConstants;
import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.core.cache.CampaignCache;
import com.stpl.tech.kettle.core.cache.MappingCache;
import com.stpl.tech.kettle.core.cache.MetadataCache;
import com.stpl.tech.kettle.core.cache.OrderInfoCache;
import com.stpl.tech.kettle.core.cache.OrderMappingCache;
import com.stpl.tech.kettle.core.cache.OrderUnitMapping;
import com.stpl.tech.kettle.core.data.vo.CashCardNotificationData;
import com.stpl.tech.kettle.core.data.vo.CreateOrderResult;
import com.stpl.tech.kettle.core.data.vo.FeedbackEventInfo;
import com.stpl.tech.kettle.core.data.vo.FeedbackTokenInfo;
import com.stpl.tech.kettle.core.data.vo.SubscriptionProduct;
import com.stpl.tech.kettle.core.exception.CardValidationException;
import com.stpl.tech.kettle.core.exception.PaymentFailureException;
import com.stpl.tech.kettle.core.notification.ErrorNotification;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.core.notification.receipt.QRRawPrintReceipt;
import com.stpl.tech.kettle.core.notification.sms.CashCardSMSNotificationType;
import com.stpl.tech.kettle.core.notification.sms.CustomerSMSNotificationType;
import com.stpl.tech.kettle.core.service.CustomerFavChaiManagementService;
import com.stpl.tech.kettle.core.service.CustomerOfferManagementService;
import com.stpl.tech.kettle.core.service.DeliveryRequestService;
import com.stpl.tech.kettle.core.service.FreeItemOfferManagementService;
import com.stpl.tech.kettle.core.service.OrderManagementService;
import com.stpl.tech.kettle.core.service.OrderSearchService;
import com.stpl.tech.kettle.core.service.PosMetadataService;
import com.stpl.tech.kettle.core.service.UnitInventoryManagementService;
import com.stpl.tech.kettle.core.service.VoucherManagementService;
import com.stpl.tech.kettle.customer.service.CardService;
import com.stpl.tech.kettle.customer.service.CashBackService;
import com.stpl.tech.kettle.customer.service.CustomerService;
import com.stpl.tech.kettle.customer.service.FeedbackManagementService;
import com.stpl.tech.kettle.customer.service.LoyaltyService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.dao.PaidEmployeeMealDao;
import com.stpl.tech.kettle.data.model.CashCardDetail;
import com.stpl.tech.kettle.data.model.CustomerCampaignOfferDetail;
import com.stpl.tech.kettle.data.model.CustomerMappingTypes;
import com.stpl.tech.kettle.data.model.EmployeeMealData;
import com.stpl.tech.kettle.data.model.LoyaltyScore;
import com.stpl.tech.kettle.data.model.SubscriptionPlan;
import com.stpl.tech.kettle.domain.model.CashCardType;
import com.stpl.tech.kettle.domain.model.CreateNextOfferRequest;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.CustomerDineInView;
import com.stpl.tech.kettle.domain.model.ExternalSettlement;
import com.stpl.tech.kettle.domain.model.InventoryUpdateEvent;
import com.stpl.tech.kettle.domain.model.NextOffer;
import com.stpl.tech.kettle.domain.model.NotificationType;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.OrderNotification;
import com.stpl.tech.kettle.domain.model.OrderResponse;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.domain.model.ProductInventory;
import com.stpl.tech.kettle.domain.model.Settlement;
import com.stpl.tech.kettle.domain.model.SignupOfferStatus;
import com.stpl.tech.kettle.domain.model.SubscriptionInfoDetail;
import com.stpl.tech.kettle.domain.model.SubscriptionOfferInfoDetail;
import com.stpl.tech.kettle.domain.model.SubscriptionViewData;
import com.stpl.tech.master.core.external.notification.FireStoreNotificationType;
import com.stpl.tech.kettle.service.notification.OrderPushNotification;
import com.stpl.tech.master.core.data.vo.NotificationPayload;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.acl.service.TokenService;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.inventory.service.InventoryService;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.core.external.notification.publisher.CustomerCommunicationEventPublisher;
import com.stpl.tech.master.core.external.notification.service.FirebaseNotificationService;
import com.stpl.tech.master.core.external.notification.service.NotificationService;
import com.stpl.tech.master.core.external.notification.service.SMSClientProviderService;
import com.stpl.tech.master.core.external.offer.service.OfferManagementExternalService;
import com.stpl.tech.master.core.external.offer.service.OfferManagementService;
import com.stpl.tech.master.core.notification.sms.SMSWebServiceClient;
import com.stpl.tech.master.core.notification.sms.SolsInfiniWebServiceClient;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.master.core.service.model.ErrorInfo;
import com.stpl.tech.master.data.model.CouponDetailData;
import com.stpl.tech.master.data.model.DeliveryCouponDetailData;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.Brand;
import com.stpl.tech.master.domain.model.CampaignDetail;
import com.stpl.tech.master.domain.model.CampaignMapping;
import com.stpl.tech.master.domain.model.CondimentItemData;
import com.stpl.tech.master.domain.model.Consumable;
import com.stpl.tech.master.domain.model.CouponCloneResponse;
import com.stpl.tech.master.domain.model.CouponData;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductBasicDetail;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.domain.model.UnitStatus;
import com.stpl.tech.master.domain.model.scm.WastageData;
import com.stpl.tech.master.domain.model.scm.WastageEvent;
import com.stpl.tech.master.inventory.model.InventoryAction;
import com.stpl.tech.master.inventory.model.InventorySource;
import com.stpl.tech.master.inventory.model.ProductQuantityData;
import com.stpl.tech.master.inventory.model.QuantityResponseData;
import com.stpl.tech.master.notification.SQSNotification;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.JSONSerializer;
import com.stpl.tech.util.TemplateRenderingException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.data.util.Pair;
import org.springframework.http.HttpStatus;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.annotation.PostConstruct;
import javax.jms.JMSException;
import javax.jms.MessageProducer;
import javax.jms.Session;
import javax.persistence.NoResultException;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

public abstract class AbstractOrderResource extends AbstractResources {

	private static final Logger LOG = LoggerFactory.getLogger(AbstractOrderResource.class);

	private static final int WASTAGE_IN_SOCKET_TIMEOUT=3;
	private static final int WASTAGE_IN_CONNECTION_TIMEOUT=3;

	@Autowired
	private SimpMessagingTemplate template;
	@Autowired
	private NotificationService notificationService;
	@Autowired
	private SMSClientProviderService providerService;
	@Autowired
	private EnvironmentProperties properties;
	@Autowired
	private ItemConsumptionHelper itemConsumptionHelper;
	@Autowired
	private InventoryService inventoryService;
	@Autowired
	private PaidEmployeeMealDao paidEmpoyeeMealDao;

	@Autowired
	private FreeItemOfferManagementService freeItemOfferService;
	@Autowired
	private FeedbackManagementService feedbackManagementService;

	@Autowired
	private CampaignCache campaignCache;

	@Autowired
	private MappingCache mappingCache;

	@Autowired
	private VoucherManagementService voucherService;
	private MessageProducer producer;
	private SQSSession session;

	@Autowired
	private CashBackService cashBackService;

	@Autowired
	private CustomerCommunicationEventPublisher customerCommunicationEventPublisher;

	@Autowired
	private LoyaltyService loyaltyService;

	@Autowired
	private CleverTapDataPushService cleverTapDataPushService;

	@Autowired
	private CleverTapConverter cleverTapConverter;

	 @Autowired
	 private ApplicationContext applicationContext;

	 @Autowired
	 private CustomerFavChaiManagementService favChaiService;

	@PostConstruct
	public void setQueueSessions() throws JMSException {
		LOG.info("POST-CONSTRUCT AbstractOrderResource - STARTED");
		Regions region = AppUtils.getRegion(getEnvironmentProperties().getEnvironmentType());
		session = SQSNotification.getInstance().getSession(region, Session.AUTO_ACKNOWLEDGE);
		producer = SQSNotification.getInstance().getProducer(session,
				getEnvironmentProperties().getEnvironmentType().name(), "_ORDERS");
	}

	public OrderInfo createOrder(Order order, boolean includeReceipts, boolean addMetadata, OrderNotification orderNotification)
			throws DataUpdationException, DataNotFoundException, TemplateRenderingException, CardValidationException,
			IOException, JMSException {
		if (!UnitStatus.ACTIVE.equals(getMasterDataCache().getUnitBasicDetail(order.getUnitId()).getStatus())) {
			throw new DataUpdationException("Unit is IN_ACTIVE : Please activate unit to place orders");
		}
		order.setWhatsappNotificationPayloadType("ORDER");

		// 1
		long startTime = System.currentTimeMillis();
		long orderStartTime = System.currentTimeMillis();
		boolean newCustomer = order.isNewCustomer();
		StringBuilder sb = new StringBuilder();

		Unit unitData = getMasterDataCache().getUnit((order.getUnitId()));

		if(Boolean.TRUE.equals(unitData.getIsTestingUnit())) {
			order.setOrderType(AppConstants.ORDER_TYPE_TESTING_ORDER);
		} else if (order.getOrderType() == null || order.getOrderType().trim().length() == 0) {
			order.setOrderType(AppConstants.ORDER_TYPE_REGULAR);
		}

		// Check Wastage
		if (TransactionUtils.isSpecialOrder(order) && !TransactionUtils.isEmployeeMeal(order)) {
			checkBookWastage(order);
			validateSpecialOrder(order);
		}
		voucherService.verifyVoucher(order, false);
		if (!TransactionUtils.isCODOrder(order.getSource())) {
			validateEmployeeMeal(order);
		}

		validatePaidEmployeeMeal(order);
		// [WARNING] Removes customer if complimentary = SAMPLING
		complimentryValidation(order);

		// [HACKED] Difficult to set it up at the UI level. Needs to move to UI
		applyLoyaltyCode(order);

		// [HACKED] need to make dynamic via apply coupon check
		checkAmex(order);

		checkCashBack(order);

		awardCashBackOffer(order);

		//calculateCondimentaryItemQuantity(order);

		getOrderManagementService().updateSugarVariant(order);

		sb.append(String.format(
				"\n----------- ,STEP 0, - ,Employee Meal and Complimentry Validation, ----------- ,%d, milliseconds ",
				System.currentTimeMillis() - startTime));
		// 0

		startTime = System.currentTimeMillis();
		OrderUnitMapping orderUnitMapping = new OrderUnitMapping(order.getUnitId(), order.getTerminalId(),
				order.getSource(), order.getEmployeeId());
		String randonOrderGeneratedString = order.getGenerateOrderId();
		if (order.getSubscriptionDetail() == null) {
			OrderMappingCache.getInstance().add(orderUnitMapping, randonOrderGeneratedString);
			order.setGenerateOrderId(null);
		}
		sb.append(String.format(
				"\n----------- ,STEP 1, - ,Order Added to Order Mapping Cache, ----------- ,%d, milliseconds ",
				System.currentTimeMillis() - startTime));
		// 2
		startTime = System.currentTimeMillis();

		boolean hasGiftcards = hasGiftCard(order);
		if(hasGiftcards){
			validateGiftCard(order);
		}
		order.setGiftCardOrder(hasGiftcards);
		boolean hasSelectOrder = hasSelectOrder(order);
		// SETTING order status before creating database entry
		if (isRoutedToAssembly(unitData, order, hasGiftcards, hasSelectOrder)) {
			order.setStatus(OrderStatus.CREATED);
		} else {
			order.setStatus(OrderStatus.SETTLED);
		}

		if(order.getOrders().get(0).getProductId()!=3){
			order = applyFreeItemOffer(newCustomer, order);
		}
		CreateOrderResult result = getOrderManagementService().createOrder(order);
		// saving customer info brandwise
		boolean newBrandCustomer = false;
		long orderBrandTime = System.currentTimeMillis();
		try {
			if (order.getCustomerId() > 5) {
				boolean isSpecialOrder = false;
				for(OrderItem item : order.getOrders()){
					if(AppUtils.isGiftCard(item.getCode()) || Objects.nonNull(getMasterDataCache().getSubscriptionProductDetail(item.getProductId()))){
						isSpecialOrder =true;
					}
				}
				newBrandCustomer = getOrderManagementService().saveCustomerInfoBrandWise(order.getCustomerId(),
						order.getBrandId(), result.getOrderId(), order.getBillStartTime(),isSpecialOrder);
				LOG.info("customer brand mapping details saved in db in {} milliseconds",
						System.currentTimeMillis() - orderBrandTime);
			}
		} catch (Exception e) {
			LOG.error("error while saving customer {} brandwise info for brand {}", order.getCustomerId(),
					order.getBrandId());
		}

		int orderId = result.getOrderId();
		sb.append(String.format(
				"\n----------- ,STEP 2, - ,Order Persisted to DB, ------------------------ ,%d, milliseconds, %d ",
				System.currentTimeMillis() - startTime, orderId));
		LOG.info("Created order with ID  " + orderId);
		startTime = System.currentTimeMillis();
		com.stpl.tech.master.domain.model.Pair<String, String> feedbackUrl = createFeedbackUrl(result);
		sb.append(String.format(
				"\n----------- ,STEP 3, - ,Created Feedback URL, ------------------------ ,%d, milliseconds ",
				System.currentTimeMillis() - startTime));
		// 3
		startTime = System.currentTimeMillis();
		OrderInfo info = createOrderInfo(orderId, order, includeReceipts, newBrandCustomer, feedbackUrl, result,
				newCustomer);
		sb.append(String.format(
				"\n----------- ,STEP 4, - ,Created Order Info, --------------------------- ,%d, milliseconds ",
				System.currentTimeMillis() - startTime));

		// 4
		startTime = System.currentTimeMillis();
		updateOfferCode(order, orderId, info);
		sb.append(String.format(
				"\n----------- ,STEP 5, - ,Offer Code related updates, ------------------- ,%d, milliseconds ",
				System.currentTimeMillis() - startTime));

		// 5
		startTime = System.currentTimeMillis();
		addDeliveryPartner(orderId, order, info);
		sb.append(String.format(
				"\n----------- ,STEP 6, - ,Added Delivery Partner, ----------------------- ,%d, milliseconds ",
				System.currentTimeMillis() - startTime));

		// 6
		startTime = System.currentTimeMillis();
		notifyOverWebsocket(unitData, order, hasGiftcards, info, hasSelectOrder);
		sb.append(String.format(
				"\n----------- ,STEP 7, - ,WebSocket Sent, ------------------------------- ,%d, milliseconds ",
				System.currentTimeMillis() - startTime));

		// 7
		startTime = System.currentTimeMillis();
		updateUnitInventory(order, orderId, false);
		sb.append(String.format(
				"\n----------- ,STEP 8, - ,Inventory Updated, ---------------------------- ,%d, milliseconds ",
				System.currentTimeMillis() - startTime));

		// 8
		startTime = System.currentTimeMillis();
		runSubscription(order, info, result,orderNotification);
		sb.append(String.format("\n----------- ,STEP 9, - ,Run Subscription, ---------------- ,%d, milliseconds ",
				System.currentTimeMillis() - startTime));

		// 9

		startTime = System.currentTimeMillis();
		handleCashCard(order, info,orderNotification);
		sb.append(String.format(
				"\n----------- ,STEP 10"
						+ ", - ,Send Cash Card Notification, ---------------------------- ,%d, milliseconds ",
				System.currentTimeMillis() - startTime));
		// 10
		startTime = System.currentTimeMillis();
		generateOrderNotifcationEvent(info);
		// Publish
		publish(info);
		sb.append(String.format(
				"\n----------- ,STEP 11, - ,Order Published for analytics, ---------------- ,%d, milliseconds ",
				System.currentTimeMillis() - startTime));
		// 11
		startTime = System.currentTimeMillis();
		// Publish
		publishInventory(info);
		sb.append(String.format(
				"\n----------- ,STEP 12, - ,Order Published for inventory, ---------------- ,%d, milliseconds ",
				System.currentTimeMillis() - startTime));
		// Publish
		// 12
		startTime = System.currentTimeMillis();
		if (TransactionUtils.isSpecialOrder(info.getOrder()) && !TransactionUtils.isEmployeeMeal(info.getOrder())) {
			bookWastage(info.getOrder());
		}
		sb.append(String.format("\n----------- ,STEP 13, - ,Book Wastage, ---------------- ,%d, milliseconds ",
				System.currentTimeMillis() - startTime));
		// 13
		startTime = System.currentTimeMillis();
		// allot cashback
		allotCashBack(order, result, info,orderNotification);
		sb.append(String.format("\n----------- ,STEP 14, - ,Alot Cashback, ---------------- ,%d, milliseconds ",
				System.currentTimeMillis() - startTime));

		startTime = System.currentTimeMillis();
		if (getEnvironmentProperties().isDinePostOrderOfferEnabled()) {
			LOG.info("DINE_IN_POST_ORDER_OFFER - Starting the Process - Customer Id : {}, Order Id {} ",
					info.getCustomer().getId(), orderId);
			if (orderApplicableForPostOrderOffer(info.getOrder(), info.getUnit(),order.getCustomerId())) {
				Integer campaignId = campaignCache.getCurrentNBOCampaign(order.getUnitId());
				if (campaignId != null && campaignId > 0) {
					NextOffer offer = createNextOffer(campaignId, orderId, info.getBrand().getBrandId(),
							info.getOrder().getUnitId(), info.getCustomer(), null, null,null,orderNotification,true);
					if (offer != null) {
						info.setNextOffer(offer);
					}
				} else{
					LOG.info(
							"POST_ORDER_OFFER - Skipping as no campaign found for - Customer Id : {}, Order Id {}, unit id : {} ",
							info.getCustomer().getId(), orderId, order.getUnitId());
				}
			} else {
				LOG.info(
						"DINE_IN_POST_ORDER_OFFER - Skipping as the order is not applicale for post order Offer - Customer Id : {}, Order Id {} ",
						info.getCustomer().getId(), orderId);
			}
		} else {
			LOG.info(
					"DINE_IN_POST_ORDER_OFFER - Skipping as the environment property is disabled - Customer Id : {}, Order Id {} ",
					info.getCustomer().getId(), orderId);
		}
		sb.append(String.format(
				"\n----------- ,STEP 15-1, - ,Create Post Order Offers-NBO, ---------------- ,%d, milliseconds ",
				System.currentTimeMillis() - startTime));
		startTime = System.currentTimeMillis();
		if(getEnvironmentProperties().isDeliveryPostOrderOfferEnabled()){
			LOG.info("DELIVERY_POST_ORDER_OFFER - Starting the Process - Customer Id : {}, Order Id {} ",
					info.getCustomer().getId(), orderId);
			if(orderApplicableForDeliveryPostOrderOffer(info.getOrder(), info.getUnit(),order.getCustomerId())){
				Integer campaignId = campaignCache.getCurrentDNBOCampaign(order.getUnitId());
				if(Objects.nonNull(campaignId) && campaignId>0){
					NextOffer offer = createDNBOOffer(campaignId, orderId, info.getBrand().getBrandId(),
							info.getOrder().getUnitId(), info.getCustomer(), null, null,null,orderNotification, true);
					if(Objects.nonNull(offer)){
						info.setNextDeliveryOffer(offer);
					}
				} else{
					LOG.info(
							"POST_ORDER_OFFER - Skipping as no campaign found for - Customer Id : {}, Order Id {}, unit id : {} ",
							info.getCustomer().getId(), orderId, order.getUnitId());
				}
			}else{
				LOG.info(
						"DELIVERY_POST_ORDER_OFFER - Skipping as the order is not applicale for post order Offer - Customer Id : {}, Order Id {} ",
						info.getCustomer().getId(), orderId);
			}
		}else{
			LOG.info(
					"DELIVERY_POST_ORDER_OFFER - Skipping as the environment property is disabled - Customer Id : {}, Order Id {} ",
					info.getCustomer().getId(), orderId);
		}
		sb.append(String.format(
				"\n----------- ,STEP 15-2, - ,Create Post Order Offers-DNBO, ---------------- ,%d, milliseconds ",
				System.currentTimeMillis() - startTime));
		startTime = System.currentTimeMillis();
		if(Objects.nonNull(info.getNextOffer()) || Objects.nonNull(info.getNextDeliveryOffer())){
			try{
				boolean needsCafeOrderPrint = !TransactionUtils.isSpecialOrder(order)
						|| TransactionUtils.isPaidEmployeeMeal(order);
				if (needsCafeOrderPrint) {
					String communicationPrint = TransactionUtils
							.getReceiptObject(getEnvironmentProperties().getChaayosBaseUrl(),
									getMasterDataCache(), ReceiptType.CUSTOMER_COMMUNICATION, info,
									getEnvironmentProperties().getBasePath(),
									getEnvironmentProperties().getRawPrintingSatus(),properties)
							.getContent();
					info.getAdditionalReceipts().add(communicationPrint);
					info.getAndroidReceipts().put(ReceiptType.CUSTOMER_COMMUNICATION, communicationPrint);
				}
			} catch (Exception e) {
				LOG.error("Error in generating next best offer customer communication receipt", e);
			}
		}
		sb.append(String.format(
				"\n----------- ,STEP 15-3, - ,Customer print, ---------------- ,%d, milliseconds ",
				System.currentTimeMillis() - startTime));

		sb.append(String.format(
				"\n----------- ,,,Full Order Processing Time, ---------------------------- ,%d, milliseconds ",
				System.currentTimeMillis() - orderStartTime));
		if (addMetadata) {
			info.getOrder().setMetadataList(order.getMetadataList());
		}

		if(!getEnvironmentProperties().getSystemGeneratedNotifications()){
			//set order related metadata in orderNotification like unitName , customerName  etc
			getOrderMetadataNotificationPayload(info,orderNotification);
		}

		if (Objects.nonNull(order.getPointsAcquired()) ){
			LOG.info ("Checking if acquired points are set in order for orderId ::{} and points are::{} ", orderId ,order.getPointsAcquired());
		}

		OrderMappingCache.getInstance().setGeneratedOrderId(orderUnitMapping, randonOrderGeneratedString,
				info.getOrder().getGenerateOrderId());
		/*if (Objects.nonNull(order.getPointsAcquired()) && order.getPointsAcquired() > 0
				&& Objects.nonNull(info.getCustomer().getOptWhatsapp())
				&& info.getCustomer().getOptWhatsapp().equals(AppConstants.YES)){
			sendLoyalTeaWhatsappNotification(order,info,orderNotification);
		}*/
		if(Objects.nonNull(order.getPointsAcquired()) && order.getPointsAcquired() >0){
			//Two Cases ---> A. Send System Generated Notification , i this case send loyal tea notif over whatsapp
			//-------------> B. SET Attributes in orderNotification
			if(getEnvironmentProperties().getSystemGeneratedNotifications()){
				if(Objects.nonNull(info.getCustomer().getOptWhatsapp()) && AppConstants.YES.equalsIgnoreCase(info.getCustomer().getOptWhatsapp())){
					sendLoyalTeaWhatsappNotification(order,info,orderNotification);
				}
			}else{
				setLoyalTeaNotificationMetadata(info,orderNotification);
			}
		}
		if(getEnvironmentProperties().getSystemGeneratedNotifications()) {
			if ((!TransactionUtils.isCODOrder(info.getOrder().getSource()) || info.getOrder().getChannelPartner() == AppConstants.BAZAAR_PARTNER_ID)
					&& Objects.nonNull(info.getCustomer().getOptWhatsapp())
					&& info.getCustomer().getOptWhatsapp().equals(AppConstants.YES)
					&& !info.getOrder().isGiftCardOrder()
					&& !hasSelectOrder
					&& order.getCustomerId() > 5) {
				sendWhatsAppNotification(order, info, orderNotification);
			}
		}else{
			setOrderNotificationMetadata(info,orderNotification);
		}

		LOG.info(sb.toString());
		info.setOrderNotification(orderNotification);
		favChaiService.saveOrderCountOfSavedChai(order,info.getOrder().getOrderId());
		LOG.info("Printing order Notification Payload for orderId ::{} :::{}",order.getOrderId(),new Gson().toJson(orderNotification));
		return info;

	}

	private void setOrderNotificationMetadata(OrderInfo info, OrderNotification orderNotification) {
		if(Objects.nonNull(orderNotification)){
			orderNotification.setOrderAmt(info.getOrder().getTransactionDetail().getPaidAmount());
			orderNotification.setEarnedLoyalTeaPoint(info.getOrder().getEarnedLoyaltypoints());
			orderNotification.setTotalLoyalTeaPoint(info.getCustomer().getLoyaltyPoints());
			Map<String,String> payload = new HashMap<>();
			updateSavingTextAndSavingAmt(info,payload);
			if(Objects.nonNull(payload) && !payload.isEmpty()){
				if(payload.containsKey("savingText")){
					orderNotification.setSavingText(payload.get("savingText"));
				}
				if(payload.containsKey("savingAmt")){
					orderNotification.setSavingAmt(new BigDecimal(payload.get("savingAmt")));
				}
			}
		}
	}

	private void setLoyalTeaNotificationMetadata(OrderInfo info, OrderNotification orderNotification) {
		LoyaltyScore loyaltyScore=null;
		try {
			loyaltyScore = loyaltyService.getScore(info.getCustomer().getId());
		}catch (NoResultException e){
			LOG.error("Exception while getting loyalty score for customer:{}", info.getCustomer().getId(),e);
		}
		if(Objects.nonNull(loyaltyScore) && loyaltyScore.getAcquiredPoints() !=0 && loyaltyScore.getAcquiredPoints() %60 ==0 && orderNotification!=null){
			orderNotification.setLoyalTeaCount(1);
			orderNotification.setLoyalTeaTotalCount(loyaltyScore.getAcquiredPoints()/60);
			orderNotification.setTotalLoyalTeaPoint(info.getCustomer().getLoyaltyPoints());
			orderNotification.setLoyalTeaPoints(loyaltyScore.getAcquiredPoints());
			orderNotification.setIsLoyaltyUnlocked(Boolean.TRUE);
		}
	}

	private void validateGiftCard(Order order) throws CardValidationException {
		if(Objects.nonNull(order.getCustomerId()) && AppConstants.EXCLUDE_CUSTOMER_IDS.contains(order.getCustomerId())){
			throw new CardValidationException("Order Cannot be punched for the Excluded Customer ::: "+ order.getCustomerId());
		}
	}

	private void getOrderMetadataNotificationPayload(OrderInfo info, OrderNotification orderNotification) {
		if(Objects.nonNull(info) && Objects.nonNull(orderNotification)){
			orderNotification.setCustomerName(info.getCustomer().getFirstName());
			orderNotification.setCafeName(info.getUnit().getName());
			orderNotification.setCustomerContactNumber(info.getCustomer().getContactNumber());
			orderNotification.setGenerateOrderId(info.getOrder().getGenerateOrderId());
			boolean isCustomerAvilable = info.getCustomer().isSmsSubscriber() && !info.getCustomer().isBlacklisted()
					&& !info.getCustomer().isDND();
			if(Objects.nonNull(info.getCustomer().getOptWhatsapp()) && AppConstants.YES.equalsIgnoreCase(info.getCustomer().getOptWhatsapp())){
				orderNotification.setWhatsAppOptIn(true);
			}else if((Objects.isNull(info.getCustomer().getOptWhatsapp()) ||(Objects.nonNull(info.getCustomer().getOptWhatsapp()) && AppConstants.NO.equalsIgnoreCase(info.getCustomer().getOptWhatsapp()))) && isCustomerAvilable ){
				orderNotification.setSmsSubscriber(true);
			}
		}
	}

	protected boolean sendNPSMessage(boolean sendNotification, String contactNumber, FeedbackEventInfo token,
									 SMSWebServiceClient smsWebServiceClient) {
		try {
			if (token.getType().equals(FeedbackEventType.NPS)) {
				String message = CustomerSMSNotificationType.NPS_MESSAGE.getMessage(token);
				return notificationService.sendNotification(CustomerSMSNotificationType.NPS_MESSAGE.name(),
						message, contactNumber, smsWebServiceClient, sendNotification,null);
			} else if (token.getType().equals(FeedbackEventType.NPS_OFFER)) {
				String message = CustomerSMSNotificationType.NPS_OFFER_MESSAGE.getMessage(token);
				return notificationService.sendNotification(CustomerSMSNotificationType.NPS_OFFER_MESSAGE.name(),
						message, contactNumber, smsWebServiceClient, sendNotification,null);
			}else if(token.getType().equals(FeedbackEventType.ORDER_FEEDBACK)){
				String message = CustomerSMSNotificationType.ORDER_FEEDBACK_MESSAGE.getMessage(token);
				return notificationService.sendNotification(CustomerSMSNotificationType.ORDER_FEEDBACK_MESSAGE.name(),
						message, contactNumber, smsWebServiceClient, sendNotification,null);
			}
		} catch (IOException | JMSException e) {
			LOG.error("Error while sending the NPS message to " + contactNumber, e);
		}

		return false;
	}

	protected NextOffer createDNBOOffer(Integer campaignId, Integer orderId, Integer brandId, Integer unitId,
										Customer customer, Integer applicableUnitId, String applicableUnitRegion, CreateNextOfferRequest request, OrderNotification orderNotification, boolean isOfferGeneratedByOrder) {
		int customerId = customer.getId();
		CampaignDetail campaign = campaignCache.getCampaign(campaignId);
		DeliveryCouponDetailData deliveryCoupon = null;
		String notificationType = "SMS";
		CustomerCampaignOfferDetail postOrderOfferCreationSuccess = null;
		Map<String, Map<Integer, CampaignMapping>> couponMap = campaign.getMappings();
		try {
			LOG.info("DELIVERY_POST_ORDER_OFFER - Starting the Process - Customer Id : {}, Order Id {}, Campaign Id : {} ", customerId, orderId,campaignId);
			if (!getCustomerOfferManagementService().hasCustomerReceivedDNBO(customerId,
					getEnvironmentProperties().getDinePostOrderOfferCheckLastNDaysValue(),campaign.getCampaignId())) {
				CustomerDineInView oneView = getCustomerOfferManagementService().getCustomerDineInView(customerId,
						brandId, Arrays.asList(orderId));
				if (oneView != null) {
					LOG.info(
							"DELIVERY_POST_ORDER_OFFER - Found Customer One View - Customer Id : {}, Order Id {} and customer {}",
							customerId, orderId, oneView);
					if (isCustomerEligibleForPostOrderOffer(oneView)) {
						LOG.info(
								"DELIVERY_POST_ORDER_OFFER - Customer is eligble for the offer - Customer Id : {}, Order Id {} ",
								customerId, orderId);
						CustomerRepeatType type = null;
						if (orderId != null && orderId >= 0) {
							type = CustomerRepeatType.getCustomerRepeatTypeAfterPlacingOrder(
									oneView.getDineInOrders() == null ? 0 : oneView.getDineInOrders(),
									oneView.getActiveDineInOrders() == null ? 0 : oneView.getActiveDineInOrders(),
									AppConstants.getValue(oneView.getAvailedSignupOffer()));
						} else {
							type = CustomerRepeatType.getCustomerRepeatType(
									oneView.getDineInOrders() == null ? 0 : oneView.getDineInOrders(),
									oneView.getActiveDineInOrders() == null ? 0 : oneView.getActiveDineInOrders(),
									AppConstants.getValue(oneView.getAvailedSignupOffer()));
						}
						CampaignMapping cloneCode = campaignCache.getCloneCode(type, 1, campaignId);
						CustomerMappingTypes customerMappingTypes = new CustomerMappingTypes(type.name(), 1,
								couponMap.get(type.name()).get(1).getValidityInDays(), type.getDefaultCloneCode(), type.getReminderDays());
						if (cloneCode != null) {
							LOG.info("DELIVERY_POST_ORDER_OFFER : Clone Code found :: {} for customer type :: {} and validity in days :: {}",
									cloneCode.getCode(),type.name(),customerMappingTypes.getValidityInDays());
							if (campaign.isCouponClone()) {
								deliveryCoupon = getCustomerOfferManagementService().getDeliveryCloneCode(cloneCode.getCode(), brandId, true);
								if(Objects.nonNull(deliveryCoupon)){
									Integer couponDelay = Objects.nonNull(campaign.getCouponApplicableAfter()) ? campaign.getCouponApplicableAfter() : 0;
									Date startDate = AppUtils.getNextDate(AppUtils.addDays(AppUtils.getCurrentTimestamp(),couponDelay-1));
									if(AppUtils.isBefore(startDate,deliveryCoupon.getStartDate())){
										startDate = deliveryCoupon.getStartDate();
									}
									Date endDate = AppUtils.getDayBeforeOrAfterDay(startDate, cloneCode.getValidityInDays()-1);
									if(AppUtils.isBefore(deliveryCoupon.getEndDate(), endDate)){
										endDate = deliveryCoupon.getEndDate();
									}
									CouponData couponData = new CouponData(deliveryCoupon.getCouponCode(), AppUtils.getDateString(startDate,AppUtils.DATE_FORMAT_STRING),
											AppUtils.getDateString(endDate,AppUtils.DATE_FORMAT_STRING),campaign.getUsageLimit(),null,null);
									postOrderOfferCreationSuccess = getCustomerOfferManagementService()
											.createDeliveryPostOrderOffer(brandId, unitId, campaignId, customer, orderId,
													customer.getContactNumber(), customer.getCountryCode(),
													couponData, customer.getFirstName(), cloneCode.getDesc(),
													deliveryCoupon.getMasterCoupon(),deliveryCoupon,campaign.isCouponClone(),campaign, type, 1,request);

									if (postOrderOfferCreationSuccess != null) {
										LOG.info(
												"DELIVERY_POST_ORDER_OFFER - Generated Clone Coupon Successfully - Customer Id : {}, Order Id {} ",
												customerId, orderId);
										String contentUrl = getContentUrl(type);
										NextOffer offer = getNextOffer(brandId, customer.getContactNumber(), customerId,
												customer.getFirstName(), postOrderOfferCreationSuccess, type,
												notificationType, contentUrl);
										sendDeliveryPostOrderOfferNotification(offer, postOrderOfferCreationSuccess,
												customer.getOptWhatsapp(),orderNotification, isOfferGeneratedByOrder);
										offer.setExistingOffer(false);
										offer.setCrmAppBannerUrl(campaign.getCrmAppBannerUrl());
										return offer;
									} else {
										LOG.info(
												"DELIVERY_POST_ORDER_OFFER - Failed to Generate Clone Coupon - Customer Id : {}, Order Id {} ",
												customerId, orderId);
									}
								}else{
									LOG.info(
											"DELIVERY_POST_ORDER_OFFER - No partner coupon found for - Master coupon : {}",
											cloneCode.getCode());
								}
							} else {
								LOG.info(
										"DELIVERY_POST_ORDER_OFFER - Fetching master coupon detail as isClone Coupon is {}",
										campaign.isCouponClone());
								deliveryCoupon = getCustomerOfferManagementService().getDeliveryCloneCode(cloneCode.getCode(), brandId, false);
								if(Objects.nonNull(deliveryCoupon)){
									Integer couponDelay = Objects.nonNull(campaign.getCouponApplicableAfter()) ? campaign.getCouponApplicableAfter() : 0;
									Date startDate = AppUtils.getNextDate(AppUtils.addDays(AppUtils.getCurrentTimestamp(),couponDelay-1));
									if(AppUtils.isBefore(startDate,deliveryCoupon.getStartDate())){
										startDate = deliveryCoupon.getStartDate();
									}
									Date endDate = AppUtils.getDayBeforeOrAfterDay(startDate, cloneCode.getValidityInDays()-1);
									if(AppUtils.isBefore(deliveryCoupon.getEndDate(), endDate)){
										endDate = deliveryCoupon.getEndDate();
									}
									CouponData couponData = new CouponData(deliveryCoupon.getMasterCoupon(), AppUtils.getDateString(startDate,AppUtils.DATE_FORMAT_STRING),
											AppUtils.getDateString(endDate,AppUtils.DATE_FORMAT_STRING),campaign.getUsageLimit(),null,null);
									postOrderOfferCreationSuccess = getCustomerOfferManagementService()
											.createDeliveryPostOrderOffer(brandId, unitId, campaignId, customer, orderId,
													customer.getContactNumber(), customer.getCountryCode(),
													couponData, customer.getFirstName(), cloneCode.getDesc(),
													deliveryCoupon.getMasterCoupon(),deliveryCoupon,campaign.isCouponClone(),campaign,type, 1,request);
									if (postOrderOfferCreationSuccess != null) {
										LOG.info(
												"DELIVERY_POST_ORDER_OFFER - Generated Clone Coupon Successfully - Customer Id : {}, Order Id {} ",
												customerId, orderId);
										String contentUrl = getContentUrl(type);
										NextOffer offer = getNextOffer(brandId, customer.getContactNumber(), customerId,
												customer.getFirstName(), postOrderOfferCreationSuccess, type,
												notificationType, contentUrl);
										sendDeliveryPostOrderOfferNotification(offer, postOrderOfferCreationSuccess,
												customer.getOptWhatsapp(), orderNotification, isOfferGeneratedByOrder);
										offer.setExistingOffer(false);
										offer.setCrmAppBannerUrl(campaign.getCrmAppBannerUrl());
										return offer;
									} else {
										LOG.info(
												"DELIVERY_POST_ORDER_OFFER - Failed to Generate Clone Coupon - Customer Id : {}, Order Id {} ",
												customerId, orderId);
									}
								}else {
									LOG.info(
											"DELIVERY_POST_ORDER_OFFER - No Master coupon found for : {}",
											cloneCode.getCode());
								}
							}
						}else {
							LOG.info(
									"DELIVERY_POST_ORDER_OFFER - Skipping as no clone code is defined for : {}, Order Id {}, CustomerType {}, Journey No : {}, Campaign Id {}  ",
									customerId, orderId, type, 1, campaignId);

						}
					} else {
						LOG.info(
								"DELIVERY_POST_ORDER_OFFER - Skipping as the customer is not eligible for offer - Customer Id : {}, Order Id {} ",
								customerId, orderId);

					}
				} else {
					LOG.info(
							"DELIVERY_POST_ORDER_OFFER - Skipping as the customer one view data not found - Customer Id : {}, Order Id {} ",
							customerId, orderId);
				}
			} else {
				if (orderId != null && orderId > 0) {
					LOG.info(
							"DELIVERY_POST_ORDER_OFFER - Skipping as the customer has already recieved the offer - Customer Id : {}, Order Id {} ",
							customerId, orderId);
				} else {
					LOG.info("DELIVERY_POST_ORDER_OFFER - getting existing offer created for customer id : {}",customerId);
					CustomerDineInView oneView = getCustomerOfferManagementService().getCustomerDineInView(customerId,
							brandId, Arrays.asList(orderId));
					if (oneView != null) {
						postOrderOfferCreationSuccess = getCustomerOfferManagementService()
								.getActiveCustomerOffer(customerId, CampaignStrategy.DELIVERY_NBO.name());
						if (postOrderOfferCreationSuccess != null) {
							CustomerRepeatType type;
							String customerTypeName=getCustomerType(campaign,postOrderOfferCreationSuccess.getCampaignCloneCode());
							if(Objects.nonNull(customerTypeName)){
								type = CustomerRepeatType.getTypeFromName(customerTypeName);
							}else{
								type = CustomerRepeatType.getCustomerRepeatType(
										oneView.getDineInOrders() == null ? 0 : oneView.getDineInOrders(),
										oneView.getActiveDineInOrders() == null ? 0 : oneView.getActiveDineInOrders(),
										AppConstants.getValue(oneView.getAvailedSignupOffer()));
							}
							String contentUrl = getContentUrl(type);
							NextOffer offer = getNextOffer(brandId, customer.getContactNumber(), customerId,
									customer.getFirstName(), postOrderOfferCreationSuccess, type, notificationType,
									contentUrl);
							offer.setExistingOffer(true);
							offer.setCrmAppBannerUrl(campaign.getCrmAppBannerUrl());
							sendDeliveryPostOrderOfferNotification(offer, postOrderOfferCreationSuccess,
									customer.getOptWhatsapp(), orderNotification, isOfferGeneratedByOrder);
							return offer;
						}
					}
				}
			}
			LOG.info("DELIVERY_POST_ORDER_OFFER - Ending the Process - Customer Id : {}, Order Id {} ", customerId, orderId);

		} catch (Exception e) {
			LOG.error("DELIVERY_POST_ORDER_OFFER - Error In Creating Offer - Customer Id : {}, Order Id {} ", customerId,
					orderId, e);
		}
		return null;

	}

	private void sendLoyalTeaWhatsappNotification(Order order, OrderInfo info, OrderNotification orderNotification) throws JMSException {
		try {
			LoyaltyScore loyaltyScore = loyaltyService.getScore(info.getCustomer().getId());
			if (loyaltyScore.getAcquiredPoints() != 0 && loyaltyScore.getAcquiredPoints() % 60 == 0) {
				Map<String, String> payload = new HashMap<>();
				String type = "LOYALTEA";
				type = order.getWhatsappNotificationPayloadType() + "_"+type;
				if(!order.getWhatsappNotificationPayload().isEmpty()){
					payload = order.getWhatsappNotificationPayload();
				}
				payload.put("firstName", info.getCustomer().getFirstName());
				payload.put("loyalTeaCount", "1");
				payload.put("loyalTeaPoints", loyaltyScore.getAcquiredPoints().toString());
				payload.put("loyalTeaTotalCount", String.valueOf(loyaltyScore.getAcquiredPoints() / 60));
				payload.put("totalLoyalTeaPoint", String.valueOf(info.getCustomer().getLoyaltyPoints()));
				order.setWhatsappNotificationPayload(payload);
				order.setWhatsappNotificationPayloadType(type);
				if(!getEnvironmentProperties().getSystemGeneratedNotifications()){
					orderNotification.setLoyalTeaCount(1);
					orderNotification.setLoyalTeaTotalCount(loyaltyScore.getAcquiredPoints()/60);
					orderNotification.setTotalLoyalTeaPoint(info.getCustomer().getLoyaltyPoints());
					orderNotification.setLoyalTeaPoints(loyaltyScore.getAcquiredPoints());
					orderNotification.setIsLoyaltyUnlocked(Boolean.TRUE);
				}
//				customerCommunicationEventPublisher.publishCustomerCommunicationEvent(getEnvironmentProperties().getEnvironmentType().name(), getNotificationPayload("RECEIVED_LOYALTY_FREE_CHAI",info, payload));
			}
		}catch (Exception e){
			LOG.error("WHATSAPP_NOTIFICATOIN :: Exception Faced while sending whatsapp notification ::{}",info.getCustomer().getId(),e);
		}
	}

	private void sendWhatsAppNotification(Order order, OrderInfo info, OrderNotification orderNotification) {
		try {
			Map<String, String> payload = new HashMap<>();
			if(!order.getWhatsappNotificationPayload().isEmpty()){
				payload = order.getWhatsappNotificationPayload();
			}
			payload.put("firstName", info.getCustomer().getFirstName());
			payload.put("cafeName", info.getOrder().getUnitName());
			payload.put("orderAmt", info.getOrder().getTransactionDetail().getPaidAmount().toString());
			payload.put("earnedLoyalTeaPoint", info.getOrder().getEarnedLoyaltypoints().toString());
			payload.put("totalLoyalTeaPoint", String.valueOf(info.getCustomer().getLoyaltyPoints()));
			updateSavingTextAndSavingAmt(info, payload);
			/*if (Objects.nonNull(info.getOrder().getOfferCode())){
				if(getMasterDataCache().getSubscriptionSkuCodeDetail().containsKey(info.getOrder().getOfferCode())){
					Map<com.stpl.tech.master.domain.model.Pair<BigDecimal,String>,String> productAlias = getMasterDataCache().getUnitProductAlias(info.getUnit().getId(),
							getMasterDataCache().getSubscriptionSkuCodeDetail(info.getOrder().getOfferCode()).getValue().getId());
					Optional<com.stpl.tech.master.domain.model.Pair<BigDecimal,String>> firstKey =productAlias.keySet().stream().findFirst();
					if (firstKey.isPresent() && productAlias.get(firstKey.get())!=null){
						payload.put("savingText", productAlias.get(firstKey.get()));
					}else {
						payload.put("savingText", info.getOrder().getOfferCode());
					}
				}
				payload.put("savingAmt", info.getOrder().getTransactionDetail().getSavings().toString());
			}
			else {
				payload.put("savingText", "NO_REASON");
				payload.put("savingAmt", "0.00");
			}*/
			order.setWhatsappNotificationPayload(payload);
			if((order.getWhatsappNotificationPayloadType().equals("ORDER_WALLET") ||
				order.getWhatsappNotificationPayloadType().equals("ORDER_WALLET_LOYALTEA")) &&
				order.getWhatsappNotificationPayload().containsKey("walletSavingAmount") &&
				order.getWhatsappNotificationPayload().get("walletSavingAmount").equals("0.00")) {
					order.setWhatsappNotificationPayloadType("ORDER");
			}
			if(!getEnvironmentProperties().getSystemGeneratedNotifications()){
				orderNotification.setCafeName(info.getOrder().getUnitName());
				orderNotification.setOrderAmt(info.getOrder().getTransactionDetail().getPaidAmount());
				orderNotification.setEarnedLoyalTeaPoint(info.getOrder().getEarnedLoyaltypoints());
				orderNotification.setTotalLoyalTeaPoint(info.getCustomer().getLoyaltyPoints());
				if(Objects.nonNull(payload) && !payload.isEmpty()){
					if(payload.containsKey("savingText")){
						orderNotification.setSavingText(payload.get("savingText"));
					}
					if(payload.containsKey("savingAmt")){
						orderNotification.setSavingAmt(new BigDecimal(payload.get("savingAmt")));
					}
				}
			}
			else {
				if (getEnvironmentProperties().getisDineWhatsappNotificationFlag() ||
						(!(order.getChannelPartner() == AppConstants.CHANNEL_PARTNER_DINE_IN_APP ||
								(info.getOrder().getChannelPartner() == AppConstants.BAZAAR_PARTNER_ID &&
										(Objects.nonNull(order.getSourceId()) && order.getSourceId().contains(AppConstants.APP)))))) {
					customerCommunicationEventPublisher.publishCustomerCommunicationEvent(getEnvironmentProperties().getEnvironmentType().name(),
							getNotificationPayload(order.getWhatsappNotificationPayloadType(), info, order.getWhatsappNotificationPayload()));
				}
			}
		} catch (Exception e){
			LOG.error("WHATSAPP_NOTIFICATOIN :::Exception Publishing Order Notification on Whatsapp :::: {}", info.getCustomer().getId(),e);
		}
	}

	private void updateSavingTextAndSavingAmt(OrderInfo info, Map<String, String> payload) {
		try {
			if (Objects.nonNull(info.getOrder().getOfferCode())) {
				if (getMasterDataCache().getSubscriptionSkuCodeDetail().containsKey(info.getOrder().getOfferCode())) {
					Map<com.stpl.tech.master.domain.model.Pair<BigDecimal, String>, String> productAlias = getMasterDataCache().getUnitProductAlias(info.getUnit().getId(),
							getMasterDataCache().getSubscriptionSkuCodeDetail(info.getOrder().getOfferCode()).getValue().getId());
					if (Objects.nonNull(productAlias)) {
						Optional<com.stpl.tech.master.domain.model.Pair<BigDecimal, String>> firstKey = productAlias.keySet().stream().findFirst();
						if (firstKey.isPresent() && productAlias.get(firstKey.get()) != null) {
							payload.put("savingText", productAlias.get(firstKey.get()));
						} else {
							payload.put("savingText", info.getOrder().getOfferCode());
						}
					} else {
						payload.put("savingText", info.getOrder().getOfferCode());
					}
				}
				payload.put("savingAmt", info.getOrder().getTransactionDetail().getSavings().toString());
			} else {
				payload.put("savingText", "NO_REASON");
				payload.put("savingAmt", "0.00");
			}
		}catch (Exception e){
			LOG.info("Unable to updateSavingTextAndSavingAmt for order {} ",info.getOrder().getOrderId());
		}
	}

	private NotificationPayload getNotificationPayload(String type,OrderInfo info, Map<String, String> payload) {
		try {
			NotificationPayload load = new NotificationPayload();
			load.setCustomerId(info.getCustomer().getId());
			load.setContactNumber(info.getCustomer().getContactNumber());
			load.setOrderId(info.getOrder().getOrderId());
			load.setMessageType(type);
			load.setSendWhatsapp(false);
			if (Objects.nonNull(info.getCustomer().getOptWhatsapp())) {
				load.setWhatsappOptIn(info.getCustomer().getOptWhatsapp().equals(AppConstants.YES));
			} else {
				load.setWhatsappOptIn(false);
			}
			load.setRequestTime(AppUtils.getCurrentTimestamp());
			load.setPayload(payload);
			return load;
		}catch (Exception e){
			LOG.error("WHATAPP_NOTIFICATION ::: Exception Faced While Generating the Notification Payload ::: {}",info.getOrder().getOrderId());
			return null;
		}
	}

	protected NextOffer createNextOffer(Integer campaignId, Integer orderId, Integer brandId, Integer unitId,
										Customer customer, Integer applicableUnitId, String applicableUnitRegion, CreateNextOfferRequest request, OrderNotification orderNotification, boolean isOfferGeneratedByOffer) {
		int customerId = customer.getId();
		LOG.info("POST_ORDER_OFFER -- Fetching campaign detail for campaign id : {}, orderId : {}, customer id : {}, unit id : {}, brand id : {}",
				campaignId, orderId, customer.getId(), unitId, brandId);
		CampaignDetail campaign = campaignCache.getCampaign(campaignId);
		CouponCloneResponse clonedCoupon = null;
		String notificationType = "SMS";
		CustomerCampaignOfferDetail postOrderOfferCreationSuccess = null;
		Map<String, Map<Integer, CampaignMapping>> couponMap = campaign.getMappings();
		String startDay = AppUtils.getDateString(AppUtils.getNextDate(AppUtils.getBusinessDate()),
				AppConstants.DATE_FORMAT);
		try {
			LOG.info("DINE_IN_POST_ORDER_OFFER - Starting the Process - Customer Id : {}, Order Id {} ", customerId, orderId);
			if (!getCustomerOfferManagementService().hasCustomerReceivedPostOrderOffer(customerId,
					getEnvironmentProperties().getDinePostOrderOfferCheckLastNDaysValue(),campaign.getCampaignId())) {

				CustomerDineInView oneView = getCustomerOfferManagementService().getCustomerDineInView(customerId,
						brandId, Arrays.asList(orderId));
				if (oneView != null) {
					LOG.info(
							"DINE_IN_POST_ORDER_OFFER - Found Customer One View - Customer Id : {}, Order Id {} and customer {}",
							customerId, orderId, oneView);
					if (isCustomerEligibleForPostOrderOffer(oneView)) {
						LOG.info(
								"DINE_IN_POST_ORDER_OFFER - Customer is eligble for the offer - Customer Id : {}, Order Id {} ",
								customerId, orderId);
						CustomerRepeatType type = null;
						if (orderId != null && orderId >= 0) {
							type = CustomerRepeatType.getCustomerRepeatTypeAfterPlacingOrder(
									oneView.getDineInOrders() == null ? 0 : oneView.getDineInOrders(),
									oneView.getActiveDineInOrders() == null ? 0 : oneView.getActiveDineInOrders(),
									AppConstants.getValue(oneView.getAvailedSignupOffer()));
						} else {
							type = CustomerRepeatType.getCustomerRepeatType(
									oneView.getDineInOrders() == null ? 0 : oneView.getDineInOrders(),
									oneView.getActiveDineInOrders() == null ? 0 : oneView.getActiveDineInOrders(),
									AppConstants.getValue(oneView.getAvailedSignupOffer()));
						}
						CampaignMapping cloneCode = campaignCache.getCloneCode(type, 1, campaignId);
						CustomerMappingTypes customerMappingTypes = new CustomerMappingTypes(type.name(), 1,
								couponMap.get(type.name()).get(1).getValidityInDays(), type.getDefaultCloneCode(),
								couponMap.get(type.name()).get(1).getReminderDays());
						if (cloneCode != null) {
							LOG.info("POST-ORDER-OFFER : Clone Code found :: {} for customer type :: {} and validity in days :: {}",
									cloneCode.getCode(),type.name(),customerMappingTypes.getValidityInDays());
							if (campaign.isCouponClone()) {
								if(CustomerRepeatType.NEW.equals(type) && AppConstants.LOYAL_TEA_COUPON_CODE.equals(cloneCode.getCode())){
									startDay = AppUtils.getDateString(AppUtils.getNextDate(oneView.getLastOrderTime()),AppUtils.DATE_FORMAT_STRING);
									clonedCoupon = getCustomerOfferManagementService().getCloneCode(startDay,
											Arrays.asList(customer.getContactNumber()), type, cloneCode.getCode(), 1,
											type.getValidityInDays(), campaign.getCouponPrefix(), applicableUnitId, applicableUnitRegion);
								}else{
									clonedCoupon = getCustomerOfferManagementService().getCloneCode(startDay,
											Arrays.asList(customer.getContactNumber()), type, cloneCode.getCode(), 1,
											customerMappingTypes.getValidityInDays(), campaign.getCouponPrefix(), applicableUnitId, applicableUnitRegion);
								}
								postOrderOfferCreationSuccess = getCustomerOfferManagementService()
										.createPostOrderOffer(brandId, unitId, campaignId, customerId, orderId,
												customer.getContactNumber(), customer.getCountryCode(),
												clonedCoupon.getMappings().get(customer.getContactNumber()),
												customer.getFirstName(), cloneCode.getDesc(),
												clonedCoupon.getCode(),campaign,type, 1,request);
								if (postOrderOfferCreationSuccess != null) {
									LOG.info(
											"DINE_IN_POST_ORDER_OFFER - Generated Clone Coupon Successfully - Customer Id : {}, Order Id {} ",
											customerId, orderId);
									String contentUrl = getContentUrl(type);
									if(CustomerRepeatType.NEW.equals(type) && !AppConstants.LOYAL_TEA_COUPON_CODE.equals(cloneCode.getCode())){
										getOrderManagementService().removeSecondFreeChai(customerId, SignupOfferStatus.FORCE_EXPIRED.name());
									}
									NextOffer offer = getNextOffer(brandId, customer.getContactNumber(), customerId,
											customer.getFirstName(), postOrderOfferCreationSuccess, type,
											notificationType, contentUrl);
									sendPostOrderOfferNotification(offer, postOrderOfferCreationSuccess,
											customer.getOptWhatsapp(),orderNotification, isOfferGeneratedByOffer);
									offer.setExistingOffer(false);
									offer.setCrmAppBannerUrl(campaign.getCrmAppBannerUrl());
									return offer;
								} else {
									LOG.info(
											"DINE_IN_POST_ORDER_OFFER - Failed to Generate Clone Coupon - Customer Id : {}, Order Id {} ",
											customerId, orderId);
									getOfferService().setCouponCodeAsInactive(clonedCoupon.getMappings()
											.get(customer.getContactNumber()).getCouponDetailId());
								}
							} else {
								CouponDetailData couponDetail = getOfferManagementService()
										.getCoupon(cloneCode.getCode());
								CouponData couponData = getCouponData(couponDetail, customer.getContactNumber());
								getCustomerOfferManagementService().addCustomerMappingCouponData(couponDetail, customer.getContactNumber());
								postOrderOfferCreationSuccess = getCustomerOfferManagementService()
										.createPostOrderOffer(brandId, unitId, campaignId, customerId, orderId,
												customer.getContactNumber(), customer.getCountryCode(), couponData,
												customer.getFirstName(), cloneCode.getDesc(), cloneCode.getCode(), campaign,type, 1,request);
								if (postOrderOfferCreationSuccess != null) {
									LOG.info(
											"DINE_IN_POST_ORDER_OFFER - Generated Clone Coupon Successfully - Customer Id : {}, Order Id {} ",
											customerId, orderId);
									String contentUrl = getContentUrl(type);
									NextOffer offer = getNextOffer(brandId, customer.getContactNumber(), customerId,
											customer.getFirstName(), postOrderOfferCreationSuccess, type,
											notificationType, contentUrl);
									sendPostOrderOfferNotification(offer, postOrderOfferCreationSuccess,
											customer.getOptWhatsapp(), orderNotification, isOfferGeneratedByOffer);
									offer.setExistingOffer(false);
									offer.setCrmAppBannerUrl(campaign.getCrmAppBannerUrl());
									return offer;
								}

							}
						}else {
							LOG.info(
									"DINE_IN_POST_ORDER_OFFER - Skipping as no clone code is defined for : {}, Order Id {}, CustomerType {}, Journey No : {}, Campaign Id {}  ",
									customerId, orderId, type, 1, campaignId);

						}
					} else {
						LOG.info(
								"DINE_IN_POST_ORDER_OFFER - Skipping as the customer is not eligible for offer - Customer Id : {}, Order Id {} ",
								customerId, orderId);

					}
				} else {
					LOG.info(
							"DINE_IN_POST_ORDER_OFFER - Skipping as the customer one view data not found - Customer Id : {}, Order Id {} ",
							customerId, orderId);
				}
			} else {
				if (orderId != null && orderId > 0) {
					LOG.info(
							"DINE_IN_POST_ORDER_OFFER - Skipping as the customer has already recieved the offer - Customer Id : {}, Order Id {} ",
							customerId, orderId);
				} else {
					LOG.info("DINE_IN_POST_ORDER_OFFER - getting existing offer created for customer id : {}",customerId);
					CustomerDineInView oneView = getCustomerOfferManagementService().getCustomerDineInView(customerId,
							brandId, Arrays.asList(orderId));
					if (oneView != null) {
						postOrderOfferCreationSuccess = getCustomerOfferManagementService()
								.getActiveCustomerOffer(customerId, CampaignStrategy.NBO.name());
						if (postOrderOfferCreationSuccess != null) {
							CustomerRepeatType type;
							String customerTypeName=getCustomerType(campaign,postOrderOfferCreationSuccess.getCampaignCloneCode());
							if(Objects.nonNull(customerTypeName)){
								type = CustomerRepeatType.getTypeFromName(customerTypeName);
							}else{
								type = CustomerRepeatType.getCustomerRepeatType(
										oneView.getDineInOrders() == null ? 0 : oneView.getDineInOrders(),
										oneView.getActiveDineInOrders() == null ? 0 : oneView.getActiveDineInOrders(),
										AppConstants.getValue(oneView.getAvailedSignupOffer()));
							}
							String contentUrl = getContentUrl(type);
							NextOffer offer = getNextOffer(brandId, customer.getContactNumber(), customerId,
									customer.getFirstName(), postOrderOfferCreationSuccess, type, notificationType,
									contentUrl);
							offer.setExistingOffer(true);
							offer.setCrmAppBannerUrl(campaign.getCrmAppBannerUrl());
							sendPostOrderOfferNotification(offer, postOrderOfferCreationSuccess,
									customer.getOptWhatsapp(), orderNotification, isOfferGeneratedByOffer);
							return offer;
						}
					}
				}
			}
			LOG.info("DINE_IN_POST_ORDER_OFFER - Ending the Process - Customer Id : {}, Order Id {} ", customerId, orderId);

		} catch (Exception e) {
			LOG.error("DINE_IN_POST_ORDER_OFFER - Error In Creating Offer - Customer Id : {}, Order Id {} ", customerId,
					orderId, e);
			if (clonedCoupon != null && clonedCoupon.getMappings().containsKey(customer.getContactNumber())
					&& clonedCoupon.getMappings().get(customer.getContactNumber()).getCouponDetailId() != null) {
				getOfferService().setCouponCodeAsInactive(
						clonedCoupon.getMappings().get(customer.getContactNumber()).getCouponDetailId());
			}
		}
		return null;
	}

	private String getCustomerType(CampaignDetail campaignDetail, String code){
		for (Map.Entry<String, Map<Integer, CampaignMapping>> entry : campaignDetail.getMappings().entrySet()) {
			for (Map.Entry<Integer, CampaignMapping> innerEntry : entry.getValue().entrySet()) {
				if (innerEntry.getValue().getCode().equals(code)){
					return entry.getKey();
				}
			}
		}
		LOG.info("No customer type found for clone code :: {}",code);
		return null;
	}

	private CouponData getCouponData(CouponDetailData couponDetail, String contactNumber) {
		//TODO Ankit to implement this
		if(Objects.nonNull(couponDetail)){
			CouponData couponData  = new CouponData(couponDetail.getCouponCode(), AppUtils.getDateString(couponDetail.getStartDate()),
					AppUtils.getDateString(couponDetail.getEndDate()), couponDetail.getUsageCount(),
					couponDetail.getCouponDetailId(), couponDetail.getOfferDetail().getOfferDetailId());
			return couponData;
		}
		return null;
	}

	private String getContentUrl(CustomerRepeatType type) {
		String contentUrl = null;
		switch (type) {
			case REGISTER:
			contentUrl = "CLM_REGISTER";
			break;
			case REPEAT:
				contentUrl = "CLM_REPEAT";
				break;
			case DORMANT:
				contentUrl = "CLM_DORMANT";
				break;
			case NEW:
				contentUrl = "LOYAL_TEA";
				break;
			case DEFAULT:
				contentUrl = "DEFAULT";
			default:
				contentUrl = "LOYAL_TEA";
		}
		return contentUrl;
	}

	private NextOffer getNextOffer(Integer brandId, String contactNumber, Integer customerId, String firstName,
			CustomerCampaignOfferDetail r, CustomerRepeatType type, String notificationType, String contentUrl) {
		String channelPartner = null;
		if (Objects.nonNull(r) && Objects.nonNull(r.getChannelPartner())) {
			channelPartner = getMasterDataCache().getChannelPartner(r.getChannelPartner()).getName();
		}
		String customerType = Objects.nonNull(type) ? type.name() : null;
		if (r == null) {
			return new NextOffer(brandId, contactNumber, customerId, firstName, false, null, null, null, null, null,
					null, null, null, null, null);
		}
		return new NextOffer(brandId, contactNumber, customerId, firstName, true, r.getCouponCode(),
				AppUtils.getDateString(r.getCouponStartDate()), AppUtils.getDateString(r.getCouponEndDate()),
				r.getOfferText(), customerType, contentUrl, notificationType, channelPartner, r.getCouponType(),
				r.getChannelPartner());
	}

	private boolean isCustomerEligibleForPostOrderOffer(CustomerDineInView oneView) {
		return oneView.getActiveDineInOrders() >= 0 || oneView.getDineInOrders() >= 0;
	}


	private boolean orderApplicableForPostOrderOffer(Order order, Unit unit,Integer customerId) {
		return !AppConstants.EXCLUDE_CUSTOMER_IDS.contains(order.getCustomerId()) && !filterCafe(unit.getName())
				&& !TransactionUtils.isSpecialOrder(order.getOrderType()) && !order.isGiftCardOrder()
				&& !isSubscriptionProductOnly(order) && !TransactionUtils.isCODOrder(order.getSource())
				&& campaignCache.hasApplicableNBOOffer(unit.getId()) && !isSubscribedCustomer(customerId);
	}

	private boolean orderApplicableForDeliveryPostOrderOffer(Order order, Unit unit,Integer customerId) {
		return !AppConstants.EXCLUDE_CUSTOMER_IDS.contains(order.getCustomerId()) && !filterCafe(unit.getName())
				&& !TransactionUtils.isSpecialOrder(order.getOrderType()) && !order.isGiftCardOrder()
				&& !isSubscriptionProductOnly(order) && !TransactionUtils.isCODOrder(order.getSource())
				&& campaignCache.hasApplicableDNBOOffer(unit.getId()) && !isSubscribedCustomer(customerId);
	}

	private boolean isSubscribedCustomer(Integer customerId) {
		SubscriptionInfoDetail detail = getCustomerOfferManagementService().getSubscriptionInfoDetail(customerId);
		if(Objects.nonNull(detail)){
			LOG.info("Customer subscription status : {}",detail.isHasSubscription());
			return detail.isHasSubscription();
		}
		return false;
	}

	private boolean filterCafe(String name) {
		return name != null && (name.toLowerCase().contains("cod") || name.toLowerCase().contains(" dk ")
				|| name.toLowerCase().contains(" ck ") || name.toLowerCase().contains("odc")
				|| name.toLowerCase().contains("test") || name.toLowerCase().contains("kitchen")
				|| name.toLowerCase().contains("dark"));
	}

	private boolean isSubscriptionProductOnly(Order order){
		for(OrderItem orderItem : order.getOrders()){
			if(!getMasterDataCache().getSubscriptionProductDetails().containsKey(orderItem.getProductId())){
				return false;
			}
		}
		return true;
	}

	private com.stpl.tech.master.domain.model.Pair<String, String> createFeedbackUrl(CreateOrderResult result) {
		com.stpl.tech.master.domain.model.Pair<String, String> feedbackUrl = null;
		if (result.isGenerateQRCode()) {
			try {
				feedbackUrl = getFeedbackLinkForQRCode(result.getFeedbackId(), FeedbackSource.QR);
				getOrderManagementService().updateFeedbackUrl(result.getOrderId(), feedbackUrl.getValue());
			} catch (Exception e) {
				LOG.error("Error in setting QR link", e);
				new ErrorNotification("Error in setting QR link", "Error in setting QR link", e,
						getEnvironmentProperties().getEnvironmentType()).sendEmail();
			}
		} else if (result.isGenerateInAppFeedback()) {
			try {
				feedbackUrl = getFeedbackLinkForQRCode(result.getFeedbackId(), FeedbackSource.IN_APP);
			} catch (Exception e) {
				LOG.error("Error in setting IN APP Feedback link", e);
				new ErrorNotification("Error in setting IN APP Feedback  link",
						"Error in setting IN APP Feedback  link", e, getEnvironmentProperties().getEnvironmentType())
						.sendEmail();
			}
		}
		return feedbackUrl;
	}

	private void handleCashCard(Order order, OrderInfo info, OrderNotification orderNotification) {
		sendCashCardNotification(order,info,orderNotification);
		sendCashCardPurchaseNotification(order, info,orderNotification);
	}

	protected OrderInfo createOrderInfo(Integer orderId, Order order, boolean includeReceipts, boolean newBrandCustomer,
										com.stpl.tech.master.domain.model.Pair<String, String> feedbackUrl, CreateOrderResult result,
										boolean newCustomer) throws DataNotFoundException, TemplateRenderingException {
		if (TransactionUtils.isBillBookOrder(order)) {
			includeReceipts = false;
		}
		OrderInfo info = getOrderSearchService().getOrderReceipt(orderId, includeReceipts, order.getCustomerName());
		info.getCustomer().setNewCustomerForBrand(newBrandCustomer);
		try {
			if (feedbackUrl != null && result.isGenerateQRCode()) {
				String qrReceiptContent = null;
				// qrCode = qrService.getQRCodeImage(feedbackUrl, 100, 100);
				QRRawPrintReceipt assembly = new QRRawPrintReceipt(getMasterDataCache().getUnit(order.getUnitId()),
						getMasterDataCache(), info, getEnvironmentProperties().getBasePath(),
						TransactionConstants.QR_CODE_HEARDER, feedbackUrl.getValue(),properties);
				qrReceiptContent = assembly.getContent();

				if (info.getAndroidReceipts() != null && info.getAndroidReceipts().size() > 0
						&& qrReceiptContent != null) {
					info.getAndroidReceipts().put(ReceiptType.QR_CODE, qrReceiptContent);
				}
				if (info.getReceipts() != null && info.getReceipts().size() > 0 && qrReceiptContent != null) {
					info.getReceipts().add(qrReceiptContent);
				}
				info.setFeedbackUrl(feedbackUrl.getValue());
				info.setQrCode(feedbackUrl.getValue());
				info.getOrder().setQrLink(feedbackUrl.getValue());
				info.getOrder().setQrHeader(TransactionConstants.QR_CODE_HEARDER);
			} else if (feedbackUrl != null && result.isGenerateInAppFeedback()) {
				info.setFeedbackUrl(feedbackUrl.getKey());
			}
		} catch (Exception e) {
			LOG.error("Error in setting QR link", e);
			new ErrorNotification("Error in setting QR link", "Error in setting QR link", e,
					getEnvironmentProperties().getEnvironmentType()).sendEmail();
		}
		info.getOrder().setNewCustomer(newCustomer);
		info.setNewCards(result.getGiftCard());
		//Explicitly set consumables to orderInfo

		for(OrderItem item : order.getOrders()){
			info.getOrder().getOrders().stream().filter(orderItem->orderItem.getProductId()==item.getProductId()).forEach(ele->{
				if(Objects.nonNull(item.getComposition())){
					if(Objects.nonNull(item.getComposition().getOthers()) && !item.getComposition().getOthers().isEmpty() && (ele.getComposition().getOthers()==null || ele.getComposition().getOthers().isEmpty())){
						ele.getComposition().getOthers().addAll(item.getComposition().getOthers());
						//LOG.info("Modified orderItem :::::: {}1", new Gson().toJson(ele));
					}
				}
			});
		}
		try {
			calculateCondimentaryItemQuantity(info.getOrder());
			if(TransactionUtils.isCODOrder(order.getSource()) && properties.isPriortizationOfOrdersEnabled()){
				info.getOrder().setPrioritizedOrder(order.getPrioritizedOrder());
			}

		}catch (Exception e){
			LOG.error("Error Calculating Recipe Item Condiment",e);
		}

		//LOG.info("Modified orderinfo :::::: {}", new Gson().toJson(info));
		return info;
	}

	protected void updateOfferCode(Order order, Integer orderId, OrderInfo info) {
		// updating coupon code applied for the order
		if (order.getOfferCode() != null && !order.getOfferCode().trim().equals("")
				&& !TransactionConstants.SIGNUP_OFFER_CODE.equals(order.getOfferCode())) {
			getCustomerService().addOfferDetail(info.getCustomer().getId(), order.getOfferCode(), orderId);
			getOfferManagementService().updateCouponUsageByOne(info.getCustomer().getId(), order.getOfferCode(),
					orderId);
		} else if (order.getOfferCode() != null && !order.getOfferCode().trim().equals("")
				&& (order.isContainsSignupOffer()
				|| TransactionConstants.SIGNUP_OFFER_CODE.equals(order.getOfferCode()))) {
			getCustomerService().addOfferDetail(info.getCustomer().getId(), TransactionConstants.SIGNUP_OFFER_CODE,
					orderId);
		}
		try {
			LOG.info("Updating Customer Campaign Offer Detail for orderId {}", info.getOrder().getOrderId());
			getCustomerOfferManagementService().updateOfferApplicationDetails(order.getOfferCode(),
					order.getCustomerId(), orderId, order.getTransactionDetail().getSavings());
		} catch (Exception e) {
			LOG.info("Error Updating Customer Campaign Offer Detail for orderId {}", info.getOrder().getOrderId(), e);
		}
	}

	protected void addDeliveryPartner(Integer orderId, Order order, OrderInfo info) {
		if (TransactionUtils.isCODOrder(order.getSource())) {
			try {
				Integer deliveryPartnerId = order.getDeliveryPartner();
				LOG.info("Creating delivery partner ticket  " + orderId);
				// in case none or pickup is not selected
				if (deliveryPartnerId != null && deliveryPartnerId != AppConstants.DELIVERY_PARTNER_NONE
						&& deliveryPartnerId != AppConstants.DELIVERY_PARTNER_PICKUP) {

					order.setDeliveryPartner(1); // error handling for null
					// value

					if (deliveryPartnerId == AppConstants.DELIVERY_PARTNER_AUTOMATED) {
						// adding delivery details to the order info object
						info = getDeliveryRequestService().addDeliveryDetails(info,
								getMasterDataCache().getUnit(order.getUnitId()));
					} else {
						IdCodeName partnerObj = getMetadataCache().getDeliveryPartner(deliveryPartnerId);
						if (getDeliveryRequestService().checkIfAutomated(deliveryPartnerId)) {
							// adding delivery details to the order info object
							info = getDeliveryRequestService().addDeliveryDetails(info,
									getMasterDataCache().getUnit(order.getUnitId()), partnerObj);
						} else {
							info.setDeliveryPartner(partnerObj);
						}
					}
				}
			} catch (Exception e) {
				LOG.error("Encountered error while creating delivery request :::::", e);
				order.setDeliveryPartner(1); // no delivery partner assigned
			}
		}
	}

	protected void notifyOverWebsocket(Unit unitData, Order order, boolean hasGiftcards, OrderInfo info, boolean hasSelectOrder) {
		// preparing socket channel to publish to subscribers

		String webSocketChannel = AppConstants.WEB_SOCKET_CHANNEL + order.getUnitId()
				+ AppConstants.WEB_SOCKET_CHANNEL_ORDERS;
		if (isRoutedToAssembly(unitData, order, hasGiftcards, hasSelectOrder)) {
			// adding to orderInfo cache
			long startTime = System.currentTimeMillis();
			LOG.info("##### Add to cache Start Time");
			getOrderInfoCache().addToCache(info);
			LOG.info("Add to cache End Time {}",System.currentTimeMillis() - startTime);
			// add to list of orders which are yet to be delivered via sockets
			startTime = System.currentTimeMillis();
			LOG.info("##### Add to undelivered cache Start Time");
			getOrderInfoCache().addToUndelivered(info);
			LOG.info("Add to cache End Time {}",System.currentTimeMillis() - startTime);
			LOG.info("Sending to the assembly screen of {} :::::: {}", info.getOrder().getUnitName(),
					info.getOrder().getGenerateOrderId());

			if (unitData.isWorkstationEnabled()) {
				LOG.info("##### Push Order Notification");
				startTime = System.currentTimeMillis();
				pushOrderNotification(info);
				LOG.info("Push Order Notification End Time {}",System.currentTimeMillis() - startTime);
			} else {
				// publishing to web socket channel subscriber
				OrderInfo duplicate = new OrderInfo(null, info.getOrder(), info.getCustomer(),
						info.getDeliveryPartner(), info.getChannelPartner(),
						unitData.isWorkstationEnabled() ? null : info.getReceipts(), null, info.getDeliveryDetails(),
						info.getPrintType());
				template.convertAndSend(webSocketChannel, duplicate);
			}
		}

	}

	private String sendPostOrderOfferNotification(NextOffer offer, CustomerCampaignOfferDetail postOrderOfferCreationSuccess, String optWhatsapp, OrderNotification orderNotification, boolean isOfferGeneratedByOffer) {
		boolean status = false;
		Brand brand = getMasterDataCache().getBrandMetaData().get(offer.getBrandId());
		SMSWebServiceClient smsWebServiceClient = SolsInfiniWebServiceClient.getTransactionalClient(brand);
		LOG.info("Next Off De {} ::::: {}", offer.toString(), offer.isAvailable());
		if (Objects.nonNull(offer) && offer.isAvailable()) {
			LOG.info("Next Best Offer SMS Details to be send to customer :: {}", offer.getContactNumber());
			status = sendNextBestOfferNotification(offer, smsWebServiceClient,postOrderOfferCreationSuccess,optWhatsapp,orderNotification, isOfferGeneratedByOffer);
		}
		LOG.info("Message Status For Customer {} :: {}", offer.getContactNumber(), status);
		return "SMS";
	}

	private boolean sendNextBestOfferNotification(NextOffer offer, SMSWebServiceClient smsWebServiceClient,
			CustomerCampaignOfferDetail postOrderOfferCreationSuccess, String optWhatsapp,
			OrderNotification orderNotification, boolean isOfferGeneratedByOrder) {
		try {
			long startTime = System.currentTimeMillis();
			String message = null;
			boolean clvFlag = getEnvironmentProperties().getCleverTapEnabled();
			boolean sysGenNotification = getEnvironmentProperties().getSystemGeneratedNotifications();
			if (Objects.nonNull(orderNotification) && !sysGenNotification) {
				getOfferNotificationMetadata(orderNotification, offer, postOrderOfferCreationSuccess);
			}
			if (offer.getContentUrl() != null && !AppConstants.LOYAL_TEA_COUPON_CODE
					.equals(postOrderOfferCreationSuccess.getCampaignCloneCode())) {
				message = CustomerSMSNotificationType.CLM_OFFER.getMessage(offer);
				if (sysGenNotification
						|| getEnvironmentProperties().getIsSendSmsForCampaignBySystem() ) {
					return notificationService.sendNotification(CustomerSMSNotificationType.CLM_OFFER.name(), message,
							offer.getContactNumber(), smsWebServiceClient, true, getNotificationPayload(
									CustomerSMSNotificationType.CLM_OFFER, postOrderOfferCreationSuccess, optWhatsapp));
				}  if (clvFlag && offer.getBrandId().equals(Integer.valueOf(1))) {
					if(isOfferGeneratedByOrder) {
						ThreadPoolTaskExecutor executor = (ThreadPoolTaskExecutor) applicationContext
								.getBean("taskExecutor");
						executor.execute(() -> {
							cleverTapDataPushService.uploadProfileAttributes(offer.getCustomerId(),
									AppUtils.getCurrentTimestamp().toInstant().getEpochSecond(),
									CleverTapConstants.REGULAR, cleverTapConverter.convert(offer,
											CleverTapEvents.NEXT_BEST_OFFER, postOrderOfferCreationSuccess));
							LOG.info("Sending NBO Data to Clevertap in seprate thread took {} ms",
									System.currentTimeMillis() - startTime);
						});
						return true;
					}else {
						LOG.info("Sending offer Details to clevertap for Customer {} ", offer.getCustomerId());
						CleverTapPushResponse response = cleverTapDataPushService.uploadProfileAttributes(
								offer.getCustomerId(), AppUtils.getCurrentTimestamp().toInstant().getEpochSecond(),
								CleverTapConstants.REGULAR, cleverTapConverter.convert(offer,
										CleverTapEvents.NEXT_BEST_OFFER, postOrderOfferCreationSuccess));
						LOG.info("Sending NBO Data to Clevertap took {} ms",System.currentTimeMillis()-startTime);
						return response.getStatus().equalsIgnoreCase(CleverTapConstants.SUCCESS);								}
				}
			} else {
				// DONT_SEND LOYALTEA SMS
				return true;

			}

		} catch (Exception e) {
			LOG.error("Error while sending CLM Repeat SMS to Customer :: {}", offer.getContactNumber());
		}
		return false;
	}

	private String sendDeliveryPostOrderOfferNotification(NextOffer offer,
			CustomerCampaignOfferDetail postOrderOfferCreationSuccess, String optWhatsapp,
			OrderNotification orderNotification, boolean isOfferGeneratedByOrder) {
		boolean status = false;
		Brand brand = getMasterDataCache().getBrandMetaData().get(offer.getBrandId());
		SMSWebServiceClient smsWebServiceClient = SolsInfiniWebServiceClient.getTransactionalClient(brand);
		LOG.info("Delivery Next Off De {} ::::: {}", offer.toString(), offer.isAvailable());
		if (Objects.nonNull(offer) && offer.isAvailable()) {
			if (isOfferGeneratedByOrder && !getEnvironmentProperties().getSystemGeneratedNotifications()) {
				if (Objects.nonNull(orderNotification)) {
					getOfferNotificationMetadata(orderNotification, offer, postOrderOfferCreationSuccess);
				}
			}
			LOG.info("Delivery Next Best Offer SMS Details to be send to customer :: {}", offer.getContactNumber());
			status = sendDeliveryNextBestOfferNotification(offer, smsWebServiceClient, postOrderOfferCreationSuccess,
					optWhatsapp, isOfferGeneratedByOrder);
		}
		LOG.info("Message Status For Customer {} :: {}", offer.getContactNumber(), status);
		return "SMS";
	}

	private boolean sendDeliveryNextBestOfferNotification(NextOffer offer, SMSWebServiceClient smsWebServiceClient,
			CustomerCampaignOfferDetail postOrderOfferCreationSuccess, String optWhatsapp, boolean isOfferGeneratedByOrder) {
		try {
			long startTime = System.currentTimeMillis();
			String message = null;
			boolean clvFlag = getEnvironmentProperties().getCleverTapEnabled();
			boolean sysGenNotification = getEnvironmentProperties().getSystemGeneratedNotifications();
			if (offer.getContentUrl() != null && !AppConstants.LOYAL_TEA_COUPON_CODE
					.equals(postOrderOfferCreationSuccess.getCampaignCloneCode())) {
				message = CustomerSMSNotificationType.CLM_OFFER_DELIVERY.getMessage(offer);
				if (sysGenNotification || (getEnvironmentProperties().getIsSendSmsForCampaignBySystem() )) {
					return notificationService.sendNotification(CustomerSMSNotificationType.CLM_OFFER_DELIVERY.name(),
							message, offer.getContactNumber(), smsWebServiceClient, true,
							getNotificationPayload(CustomerSMSNotificationType.CLM_OFFER_DELIVERY,
									postOrderOfferCreationSuccess, optWhatsapp));

				}
				if (clvFlag && offer.getBrandId().equals(Integer.valueOf(1))) {
					LOG.info("Sending offer Details to clevertap for Customer {} ",offer.getCustomerId());
					if (isOfferGeneratedByOrder) {
						ThreadPoolTaskExecutor executor = (ThreadPoolTaskExecutor) applicationContext
								.getBean("taskExecutor");
						executor.execute(() -> {
							cleverTapDataPushService.uploadProfileAttributes(offer.getCustomerId(),
									AppUtils.getCurrentTimestamp().toInstant().getEpochSecond(),
									CleverTapConstants.REGULAR, cleverTapConverter.convert(offer,
											CleverTapEvents.DELIVERY_NEXT_BEST_OFFER, postOrderOfferCreationSuccess));
							LOG.info("Sending DNBO Data to Clevertap in seprate thread took {} ms",
									System.currentTimeMillis() - startTime);
						});
						return true;
					} else {
						CleverTapPushResponse response = cleverTapDataPushService.uploadProfileAttributes(
								offer.getCustomerId(), AppUtils.getCurrentTimestamp().toInstant().getEpochSecond(),
								CleverTapConstants.REGULAR, cleverTapConverter.convert(offer,
										CleverTapEvents.DELIVERY_NEXT_BEST_OFFER, postOrderOfferCreationSuccess));
						LOG.info("Sending DNBO Data to Clevertap took {} ms", System.currentTimeMillis() - startTime);
						return response.getStatus().equalsIgnoreCase(CleverTapConstants.SUCCESS);
					}
				}
			} else {
				// DONT_SEND LOYALTEA SMS
				return true;
			}
		} catch (Exception e) {
			LOG.error("Error while sending CLM Repeat SMS to Customer :: {}", offer.getContactNumber());
		}
		return false;
	}

	private NotificationPayload getNotificationPayload(CustomerSMSNotificationType type, CustomerCampaignOfferDetail customerCampaignOfferDetail, String optWhatsapp) {
		try {
			Map<String,String> map= new HashMap<>();
			map.put("couponValidity",AppUtils.getDateInMonth(customerCampaignOfferDetail.getCouponEndDate()));
			map.put("couponCode",customerCampaignOfferDetail.getCouponCode());
			map.put("offerDescription",customerCampaignOfferDetail.getOfferText());
			NotificationPayload payload= new NotificationPayload();
			payload.setContactNumber(customerCampaignOfferDetail.getContactNumber());
			payload.setCustomerId(customerCampaignOfferDetail.getCustomerId());
			payload.setSendWhatsapp(type.isWhatsapp());
			payload.setMessageType(type.name());
			payload.setWhatsappOptIn(AppConstants.YES.equals(optWhatsapp));
			payload.setPayload(map);
			payload.setRequestTime(AppUtils.getCurrentTimestamp());
			return payload;
		} catch (Exception e){
			LOG.error("Exception Faced While Generating Notification Payload for Contact ::: {}",customerCampaignOfferDetail.getContactNumber());
			return null;
		}
	}

	private OrderNotification getOfferNotificationMetadata(OrderNotification orderNotification, NextOffer offer, CustomerCampaignOfferDetail postOrderOfferCreationSuccess){
		orderNotification.setNextOfferText(offer.getText());
		orderNotification.setOfferCode(offer.getOfferCode());
		orderNotification.setValidityTill(offer.getValidityTill());
		orderNotification.setDaysLeft(offer.getDaysLeft());
		return orderNotification;

	}

	protected void runSubscription(Order order, OrderInfo info, CreateOrderResult result, OrderNotification orderNotification)
			throws JMSException, IOException {
		try {
			if (order.getCustomerId() > 5 && Objects.nonNull(order.getOfferCode())){
				com.stpl.tech.master.domain.model.Pair<CouponDetail,Product> subscriptionObj =
						getMasterDataCache().getSubscriptionSkuCodeDetail().get(order.getOfferCode());
				if (Objects.nonNull(subscriptionObj)) {
					getCustomerOfferManagementService().addSubscriptionSaving(order.getCustomerId(),
							order,subscriptionObj);
//				if (Objects.nonNull(info.getCustomer().getOptWhatsapp()) && info.getCustomer().getOptWhatsapp().equals(AppConstants.YES)) {
				sendWhatsappNOtificationForChaayosSelectSaving(order, info,orderNotification);
//				}
				}

				if (result.getSubscriptionPlan() != null) {
					int subscriptionProduct = 0;
					for (OrderItem item : info.getOrder().getOrders()) {
						if (Objects.nonNull(getMasterDataCache().getSubscriptionProductDetail(item.getProductId()))) {
							subscriptionProduct = item.getProductId();
							break;
						}
					}
					sendSubscriptionPurchaseNotification(result.getSubscriptionPlan(), info.getBrand(), info.getCustomer(),
							subscriptionProduct, info.getOrder().getUnitId(), info.getOrder().getOrderId(),order.getChannelPartner(),orderNotification);
				}
			}


		} catch (Exception e) {
			LOG.error(
					"WHATSAPP_NOTIFICATOIN :: Exception faced while sending Subscription notification on whatsapp :: {}",
					info.getCustomer().getId(), e);
		}
	}

	protected void createSubscription(String planCode, Integer validityInDays, Integer brandId, Integer customerId,
			BigDecimal price, Integer campaignId, String source) throws JMSException, IOException {
		try {
			com.stpl.tech.master.domain.model.Pair<CouponDetail, Product> sku = getMasterDataCache()
					.getSubscriptionSkuCodeDetail(planCode);

			if (sku != null) {
				Product product = sku.getValue();
				SubscriptionProduct subs = new SubscriptionProduct();
				subs.setDimensionCode(AppConstants.NO_DIMENSION_STRING);
				subs.setPrice(price);
				subs.setProductId(product.getId());
				subs.setProductName(product.getName());
				subs.setSubscriptionCode(planCode);
				subs.setValidityInDays(validityInDays);
				Customer customer = getCustomerService().getCustomer(customerId);
				SubscriptionPlan plan = getOrderManagementService().createSubscription(subs, customer, campaignId, source, 0);
				if (plan != null) {
					sendSubscriptionPurchaseNotification(plan, getMasterDataCache().getBrandMetaData().get(brandId),
							customer, subs.getProductId(), AppConstants.BAZAAR_UNIT_ID, -1);

				}
			}

		} catch (Exception e) {
			LOG.error(
					"WHATSAPP_NOTIFICATOIN :: Exception faced while sending Subscription notification on whatsapp :: {}",
					customerId, e);
		}
	}

	private void sendSubscriptionPurchaseNotification(SubscriptionPlan subscriptionPlan, Brand brand, Customer customer,
			Integer subscriptionProduct, Integer unitId, Integer orderId) {
		if (Objects.nonNull(subscriptionPlan)) {
			SMSWebServiceClient smsWebServiceClient = SolsInfiniWebServiceClient.getTransactionalClient(brand);
			SubscriptionOfferInfoDetail subscriptionInfoDetail = mappingCache
					.getSubscriptionInfoDetail(subscriptionPlan);
			if (Objects.nonNull(subscriptionInfoDetail)) {
				LOG.info("Subscription SMS Details to be send to customer :: {}", customer.getContactNumber());
				sendSubscriptionNotification(subscriptionPlan, brand, customer, smsWebServiceClient,
						subscriptionInfoDetail, subscriptionProduct, unitId, orderId);
			}
		}

	}

	private void sendSubscriptionPurchaseNotification(SubscriptionPlan subscriptionPlan, Brand brand, Customer customer,
													  Integer subscriptionProduct, Integer unitId, Integer orderId,int channelPartnerId,OrderNotification orderNotification) {
		if (Objects.nonNull(subscriptionPlan)) {
			SMSWebServiceClient smsWebServiceClient = SolsInfiniWebServiceClient.getTransactionalClient(brand);
			SubscriptionOfferInfoDetail subscriptionInfoDetail = mappingCache
					.getSubscriptionInfoDetail(subscriptionPlan);
			if (Objects.nonNull(subscriptionInfoDetail)) {
				LOG.info("Subscription SMS Details to be send to customer :: {}", customer.getContactNumber());
				sendSubscriptionNotification(subscriptionPlan, brand, customer, smsWebServiceClient,
						subscriptionInfoDetail, subscriptionProduct, unitId, orderId,channelPartnerId,orderNotification);
			}
		}

	}
	private void sendWhatsappNOtificationForChaayosSelectSaving(Order order, OrderInfo info, OrderNotification orderNotification) throws JMSException {
		try {
			Map<String, String> map = new HashMap<>();
			Map<String,String> orderPayloadMap = new HashMap<>();
			getSubscriptionName(info, map);
			orderPayloadMap = map;
			SubscriptionInfoDetail subscriptionInfoDetail = getCustomerOfferManagementService().getSubscriptionInfoDetail(info.getCustomer().getId());
			map.put("generateOrderId", info.getOrder().getGenerateOrderId());
			map.put("savingAmount", order.getTransactionDetail().getSavings().toString());
			map.put("overAllSaving", subscriptionInfoDetail.getOverAllSaving().toString());
			orderPayloadMap.put("generateOrderId", info.getOrder().getGenerateOrderId());
			orderPayloadMap.put("selectSavingAmount", order.getTransactionDetail().getSavings().toString());
			orderPayloadMap.put("selectOverAllSaving", subscriptionInfoDetail.getOverAllSaving().toString());
			order.setWhatsappNotificationPayload(orderPayloadMap);
			String type = "SELECT";
			if(!order.getWhatsappNotificationPayloadType().isEmpty()){
				type = order.getWhatsappNotificationPayloadType()+"_"+type;
			}
			order.setWhatsappNotificationPayloadType(type);
			SubscriptionViewData subscriptionViewData = getSubscriptionView(subscriptionInfoDetail,info);
			if(!getEnvironmentProperties().getSystemGeneratedNotifications()){
				getSubscriptionNotificationMetaData(subscriptionViewData,orderNotification, order,null,null,true);
			} else {
				if (Objects.isNull(info.getCustomer().getOptWhatsapp()) || (Objects.nonNull(info.getCustomer().getOptWhatsapp()) && info.getCustomer().getOptWhatsapp().equals(AppConstants.NO))) {
					SMSWebServiceClient smsWebServiceClient = SolsInfiniWebServiceClient
							.getTransactionalClient(info.getBrand());
					// sendSubscriptionUsageNotification(subscriptionViewData, smsWebServiceClient, info, map);
				}
			}


		} catch (Exception e){
			LOG.error("WHATSAPP_NOTIFICATOIN :: Exception faced while sending Subscription saving notification on whatsapp :: {}",info.getCustomer().getId(),e);
		}
	}

	private OrderNotification getSubscriptionNotificationMetaData(SubscriptionViewData subscriptionViewData, OrderNotification orderNotification, Order order, SubscriptionOfferInfoDetail subscriptionOfferInfoDetail, Boolean isSelectPurchased,Boolean isSelectUsed){
		if(Objects.nonNull(subscriptionViewData)){
			orderNotification.setSubscriptionName(subscriptionViewData.getSubscriptionName());
			orderNotification.setSelectOverallSaving(subscriptionViewData.getTotalSaving());
			orderNotification.setPlanEndDate(AppUtils.getCalendarDate(subscriptionViewData.getPlanEndDate()));
			orderNotification.setCustomerName(subscriptionViewData.getCustomerName());
			orderNotification.setDaysLeft(subscriptionViewData.getValidityDays());
			if(Objects.nonNull(subscriptionOfferInfoDetail) && Objects.nonNull(subscriptionOfferInfoDetail.getValidDays()) && subscriptionOfferInfoDetail.getValidDays()>0){
				orderNotification.setSubscriptionValidityInDays(subscriptionOfferInfoDetail.getValidDays());
			}
			orderNotification.setIsSubscriptionPurched(isSelectPurchased);
			orderNotification.setIsSubscriptionUsed(isSelectUsed);
			if(Objects.nonNull(order) && Objects.nonNull(order.getTransactionDetail()) && Objects.nonNull(order.getTransactionDetail().getSavings()) &&  BigDecimal.ZERO.compareTo(order.getTransactionDetail().getSavings())<0 ){
				orderNotification.setSelectSavingAmount(order.getTransactionDetail().getSavings());
			}
			orderNotification.setOfferDescription(Objects.nonNull(subscriptionOfferInfoDetail) && Objects.nonNull(subscriptionOfferInfoDetail.getOfferText())?subscriptionOfferInfoDetail.getOfferText():subscriptionViewData.getOfferDescription());
		}
		return orderNotification;
	}

	private boolean sendSubscriptionUsageNotification(SubscriptionViewData subscriptionView,
			SMSWebServiceClient smsWebServiceClient, OrderInfo info, Map<String, String> map) {
		try {
			String message = CustomerSMSNotificationType.CHAAYOS_SUBSCRIPTION_USED.getMessage(subscriptionView);
			return notificationService.sendNotification(CustomerSMSNotificationType.CHAAYOS_SUBSCRIPTION_USED.name(),
					message, info.getCustomer().getContactNumber(), smsWebServiceClient,
					getEnvironmentProperties().getAutomatedNPSSMS(),
					getNotificationPayload(CustomerSMSNotificationType.CHAAYOS_SUBSCRIPTION_USED, info.getCustomer(),
							map, info.getOrder().getOrderId()));
		} catch (Exception e) {
			LOG.error("Error while sending Subscription SMS to Customer :: {}", info.getCustomer().getContactNumber());
			return false;
		}
	}

	private SubscriptionViewData getSubscriptionView(SubscriptionInfoDetail subscriptionInfoDetail, OrderInfo info) {
		SubscriptionViewData subscriptionViewData = new SubscriptionViewData();
		subscriptionViewData.setCustomerName(info.getCustomer().getFirstName());
		com.stpl.tech.master.domain.model.Pair<CouponDetail, Product> couponMapping = getMasterDataCache().getSubscriptionSkuCodeDetail(subscriptionInfoDetail.getSubscriptionCode());
		subscriptionViewData.setSubscriptionName(couponMapping.getValue().getName());
		subscriptionViewData.setOfferDescription(couponMapping.getKey().getOffer().getDescription());
		subscriptionViewData.setValidityDays(subscriptionInfoDetail.getDaysLeft());
		subscriptionViewData.setPlanEndDate(subscriptionInfoDetail.getEndDate());
		subscriptionViewData.setTotalSaving(subscriptionInfoDetail.getOverAllSaving().intValue());
		return subscriptionViewData;
	}

	private void getSubscriptionName(OrderInfo info, Map<String, String> map) {
		try {
			Map<com.stpl.tech.master.domain.model.Pair<BigDecimal,String>, String> productVOS =
					getMasterDataCache().getUnitProductAlias(info.getUnit().getId(),
							getMasterDataCache().getSubscriptionSkuCodeDetail(info.getOrder().getOfferCode()).getValue().getId());
			if(!productVOS.isEmpty()){
				Optional<com.stpl.tech.master.domain.model.Pair<BigDecimal,String>> firstKey =productVOS.keySet().stream().findFirst();
				if (firstKey.isPresent() && productVOS.get(firstKey.get())!=null){
					map.put("productName", productVOS.get(firstKey.get()));
				} else {
					map.put("productName", getMasterDataCache().getSubscriptionSkuCodeDetail(info.getOrder().getOfferCode()).getValue().getName());
				}
			}
			else {
				map.put("productName", getMasterDataCache().getSubscriptionSkuCodeDetail(info.getOrder().getOfferCode()).getValue().getName());
			}
		} catch (Exception e){
			LOG.error("CHAAYOS_SUBSCRIPTIOM ::: Exception Faced While Fetching Chaayos Subscription Name",e);
			map.put("productName", "Chaayos Select");
		}

	}

	private String getSubscriptionName(Integer subscriptionProduct, Integer unitId) {
		try {

			Map<com.stpl.tech.master.domain.model.Pair<BigDecimal, String>, String> productVOS = getMasterDataCache()
					.getUnitProductAlias(unitId, subscriptionProduct);
			if (!productVOS.isEmpty()) {
				Optional<com.stpl.tech.master.domain.model.Pair<BigDecimal, String>> firstKey = productVOS.keySet()
						.stream().findFirst();
				if (firstKey.isPresent() && productVOS.get(firstKey.get()) != null) {
					return productVOS.get(firstKey.get());
				} else {
					return getMasterDataCache().getProduct(subscriptionProduct).getName();
				}
			} else {
				return getMasterDataCache().getProduct(subscriptionProduct).getName();
			}
		} catch (Exception e) {
			LOG.error("CHAAYOS_SUBSCRIPTION ::: Exception Faced While Fetching Subscription Name", e);
		}
		return "Chaayos Membership";
	}


	private NotificationPayload getNotificationPayload(CustomerSMSNotificationType type, Customer customer,
			Map<String, String> map, Integer orderId) {
		try {
			NotificationPayload load = new NotificationPayload();
			if (Objects.nonNull(customer)) {
				load.setCustomerId(customer.getId());
			}
			load.setContactNumber(customer.getContactNumber());
			load.setOrderId(orderId);
			load.setMessageType(type.name());
			load.setSendWhatsapp(type.isWhatsapp());
			if (Objects.nonNull(customer.getOptWhatsapp())) {
				load.setWhatsappOptIn(customer.getOptWhatsapp().equals(AppConstants.YES));
			} else {
				load.setWhatsappOptIn(false);
			}

			load.setRequestTime(AppUtils.getCurrentTimestamp());
			load.setPayload(map);
			return load;
		} catch (Exception e) {
			LOG.error("WHATAPP_NOTIFICATION ::: Exception Faced While Generating the Notification Payload ::: {}",
					orderId);
			return null;
		}
	}

	private NotificationPayload getNotificationPayload(CustomerSMSNotificationType type, Customer customer,
													   Map<String, String> map, Integer orderId,int channelPartnerId) {
		try {
			NotificationPayload load = new NotificationPayload();
			if (Objects.nonNull(customer)) {
				load.setCustomerId(customer.getId());
			}
			load.setContactNumber(customer.getContactNumber());
			load.setOrderId(orderId);
			load.setMessageType(type.name());
			if(channelPartnerId == AppConstants.CHANNEL_PARTNER_DINE_IN_APP){
				load.setSendWhatsapp(getEnvironmentProperties().getisDineWhatsappNotificationFlag());
			}else{
				load.setSendWhatsapp(type.isWhatsapp());
			}
			if (Objects.nonNull(customer.getOptWhatsapp())) {
				load.setWhatsappOptIn(customer.getOptWhatsapp().equals(AppConstants.YES));
			} else {
				load.setWhatsappOptIn(false);
			}

			load.setRequestTime(AppUtils.getCurrentTimestamp());
			load.setPayload(map);
			return load;
		} catch (Exception e) {
			LOG.error("WHATAPP_NOTIFICATION ::: Exception Faced While Generating the Notification Payload ::: {}",
					orderId);
			return null;
		}
	}

	private NotificationPayload getNotificationPayload(CashCardSMSNotificationType type, OrderInfo orderInfo, Map<String, String> map) {
		try {
			NotificationPayload load = new NotificationPayload();
			if (Objects.nonNull(orderInfo.getCustomer())) {
				load.setCustomerId(orderInfo.getCustomer().getId());
			}
			load.setOrderId(orderInfo.getOrder().getOrderId());
			load.setContactNumber(orderInfo.getCustomer().getContactNumber());
			load.setMessageType(type.name());
			load.setSendWhatsapp(type.isWhatsapp());
			if (Objects.nonNull(orderInfo.getCustomer().getOptWhatsapp())) {
				load.setWhatsappOptIn(orderInfo.getCustomer().getOptWhatsapp().equals(AppConstants.YES));
			} else {
				load.setWhatsappOptIn(false);
			}
			load.setRequestTime(AppUtils.getCurrentTimestamp());
			load.setPayload(map);
			return load;
		} catch (Exception e){
			LOG.error("WHATAPP_NOTIFICATION ::: Exception Faced While Generating the Notification Payload ::: {}",orderInfo.getOrder().getOrderId());
			return null;
		}
	}

	private NotificationPayload getNotificationPayload(CashCardSMSNotificationType type, OrderInfo orderInfo, Map<String, String> map,int channelPartnerId) {
		try {
			NotificationPayload load = new NotificationPayload();
			if (Objects.nonNull(orderInfo.getCustomer())) {
				load.setCustomerId(orderInfo.getCustomer().getId());
			}
			load.setOrderId(orderInfo.getOrder().getOrderId());
			load.setContactNumber(orderInfo.getCustomer().getContactNumber());
			load.setMessageType(type.name());
			if(channelPartnerId == AppConstants.CHANNEL_PARTNER_DINE_IN_APP){
				load.setSendWhatsapp(getEnvironmentProperties().getisDineWhatsappNotificationFlag());
			}else{
				load.setSendWhatsapp(type.isWhatsapp());
			}
			if (Objects.nonNull(orderInfo.getCustomer().getOptWhatsapp())) {
				load.setWhatsappOptIn(orderInfo.getCustomer().getOptWhatsapp().equals(AppConstants.YES));
			} else {
				load.setWhatsappOptIn(false);
			}
			load.setRequestTime(AppUtils.getCurrentTimestamp());
			load.setPayload(map);
			return load;
		} catch (Exception e){
			LOG.error("WHATAPP_NOTIFICATION ::: Exception Faced While Generating the Notification Payload ::: {}",orderInfo.getOrder().getOrderId());
			return null;
		}
	}

	protected void sendSubscriptionNotification(SubscriptionPlan subscriptionPlan, Brand brand, Customer customer,
			SMSWebServiceClient smsWebServiceClient, SubscriptionOfferInfoDetail subscriptionInfoDetail,
			Integer subscriptionProduct, Integer unitId, Integer orderId) {
		try {
			String subscriptionName = getSubscriptionName(subscriptionProduct, unitId);
			subscriptionInfoDetail.setCustomerName(customer.getFirstName());
			subscriptionInfoDetail.setValidDays((int) AppUtils.getDayDifference(subscriptionPlan.getPlanStartDate(),
					subscriptionPlan.getPlanEndDate()));
			subscriptionInfoDetail.setExpiryDate(subscriptionPlan.getPlanEndDate());
			subscriptionInfoDetail
					.setSubscriptionUrl(Objects.nonNull(brand.getChaayosSubscription()) ? brand.getChaayosSubscription()
							: "chaayos.com/pages/chaayos-select");
			Map<String, String> map = new HashMap<>();
			map.put("firstName", customer.getFirstName());
			map.put("productName", subscriptionName);
			map.put("validityInDays", subscriptionInfoDetail.getValidDays().toString());
			map.put("offerDescription", subscriptionInfoDetail.getOfferText());
			map.put("endDate", AppUtils.getDateInMonth(subscriptionInfoDetail.getExpiryDate()));
			sendSubscriptionNotification(getSubscriptionView(subscriptionInfoDetail), smsWebServiceClient, customer,
					map, orderId);
		} catch (Exception e) {
			LOG.error("WHATSAPP_NOTIFICATOIN :: Exception faced while sending Subscription notification :: {}",
					customer.getId(), e);
		}
	}

	protected void sendSubscriptionNotification(SubscriptionPlan subscriptionPlan, Brand brand, Customer customer,
												SMSWebServiceClient smsWebServiceClient, SubscriptionOfferInfoDetail subscriptionInfoDetail,
												Integer subscriptionProduct, Integer unitId, Integer orderId,int channelPartnerId,OrderNotification change) {
		try {
			String subscriptionName = getSubscriptionName(subscriptionProduct, unitId);
			subscriptionInfoDetail.setCustomerName(customer.getFirstName());
			subscriptionInfoDetail.setValidDays((int) AppUtils.getActualDayDifference(AppUtils.getBusinessDate(),
					subscriptionPlan.getPlanEndDate()));
			subscriptionInfoDetail.setExpiryDate(subscriptionPlan.getPlanEndDate());
			subscriptionInfoDetail
					.setSubscriptionUrl(Objects.nonNull(brand.getChaayosSubscription()) ? brand.getChaayosSubscription()
							: "chaayos.com/pages/chaayos-select");
			Map<String, String> map = new HashMap<>();
			map.put("firstName", customer.getFirstName());
			map.put("productName", subscriptionName);
			map.put("validityInDays", subscriptionInfoDetail.getValidDays().toString());
			map.put("offerDescription", subscriptionInfoDetail.getOfferText());
			map.put("endDate", AppUtils.getDateInMonth(subscriptionInfoDetail.getExpiryDate()));
			SubscriptionViewData subscriptionViewData =getSubscriptionView(subscriptionInfoDetail);
			if(getEnvironmentProperties().getSystemGeneratedNotifications()){
				sendSubscriptionNotification(subscriptionViewData, smsWebServiceClient, customer,
						map, orderId, channelPartnerId);
			}else{
				getSubscriptionNotificationMetaData(subscriptionViewData,change,null,subscriptionInfoDetail,true,null);
			}
		} catch (Exception e) {
			LOG.error("WHATSAPP_NOTIFICATOIN :: Exception faced while sending Subscription notification :: {}",
					customer.getId(), e);
		}
	}

	private SubscriptionViewData getSubscriptionView(SubscriptionOfferInfoDetail subscriptionInfoDetail) {
		SubscriptionViewData subscriptionViewData = new SubscriptionViewData();
		subscriptionViewData.setCustomerName(subscriptionInfoDetail.getCustomerName());
		com.stpl.tech.master.domain.model.Pair<CouponDetail, Product> couponMapping = getMasterDataCache().getSubscriptionSkuCodeDetail(subscriptionInfoDetail.getSubscriptionCode());
		subscriptionViewData.setSubscriptionName(couponMapping.getValue().getName());
		subscriptionViewData.setOfferDescription(couponMapping.getKey().getOffer().getDescription());
		subscriptionViewData.setValidityDays(subscriptionInfoDetail.getValidDays());
		subscriptionViewData.setPlanEndDate(subscriptionInfoDetail.getExpiryDate());
		return subscriptionViewData;
	}

	protected boolean sendSubscriptionNotification(SubscriptionViewData subscriptionInfoDetail,
			SMSWebServiceClient smsWebServiceClient, Customer customer, Map<String, String> map, Integer orderId)
			throws IOException {
		try {
			String message = CustomerSMSNotificationType.CHAAYOS_SUBSCRIPTION.getMessage(subscriptionInfoDetail);
			return notificationService.sendNotification(CustomerSMSNotificationType.CHAAYOS_SUBSCRIPTION.name(),
					message, customer.getContactNumber(), smsWebServiceClient,
					getEnvironmentProperties().getAutomatedNPSSMS(),
					getNotificationPayload(CustomerSMSNotificationType.CHAAYOS_SUBSCRIPTION, customer, map, orderId));
		} catch (Exception e) {
			LOG.error("Error while sending Subscription SMS to Customer :: {}", customer.getContactNumber());
			return false;
		}
	}

	protected boolean sendSubscriptionNotification(SubscriptionViewData subscriptionInfoDetail,
												   SMSWebServiceClient smsWebServiceClient, Customer customer, Map<String, String> map, Integer orderId,int channelPartnerId)
			throws IOException {
		try {
			String message = CustomerSMSNotificationType.CHAAYOS_SUBSCRIPTION.getMessage(subscriptionInfoDetail);
			return notificationService.sendNotification(CustomerSMSNotificationType.CHAAYOS_SUBSCRIPTION.name(),
					message, customer.getContactNumber(), smsWebServiceClient,
					getEnvironmentProperties().getAutomatedNPSSMS(),
					getNotificationPayload(CustomerSMSNotificationType.CHAAYOS_SUBSCRIPTION, customer, map, orderId,channelPartnerId));
		} catch (Exception e) {
			LOG.error("Error while sending Subscription SMS to Customer :: {}", customer.getContactNumber());
			return false;
		}
	}

	private void allotCashBack(Order order, CreateOrderResult result, OrderInfo info, OrderNotification orderNotification) {
		LOG.info("Allocating Cash Back to customer for order {} and customer id");
		if (order.getCashBackReceived()) {
			try {
				LOG.info(
						"Added Cash Back to customer for order {} and customer id {} and amount {} and startDate {} and Enddate {}",
						order.getOrderId(), order.getCustomerId(), order.getCashBackAwarded(),
						order.getCashBackStartDate(), order.getCashBackEndDate());
				if(Objects.nonNull(order.getCustomerId()) && AppConstants.EXCLUDE_CUSTOMER_IDS.contains(order.getCustomerId())){
					LOG.info("Will not add cashback as customer is in excluded list :: customer id = {}",order.getCustomerId());
					return ;
				}
				Date startDate = Objects.nonNull(order.getCashBackStartDate()) ? order.getCashBackStartDate() : cashBackService.getStartDate();
				Date endDate = Objects.nonNull(order.getCashBackEndDate()) ? order.getCashBackEndDate() : cashBackService.getEndDate();
				boolean awarded = cashBackService.allotCashBack(order.getCashBackAwarded(), order.getCustomerId(),
						result.getOrderId(), startDate, endDate, order.getCashBackLagDays());
				if (awarded) {
					order.setCashBackAwarded(order.getCashBackAwarded());
					order.setCashBackReceived(true);
					order.setCashBackStartDate(startDate);
					order.setCashBackEndDate(endDate);
					// sendCustomerNotification
					try {

						String startDateString = AppUtils.dateInddthMMMFormat(startDate);
						String endDateString = AppUtils.dateInddthMMMFormat(endDate);
						// skiping sending notification on cash back
						// sendCashBackNotification(info, order.getCashBackAwarded(), startDateString, endDateString,orderNotification);

					} catch (Exception e) {
						LOG.error("Error in sending cash back notification");
					}
				} else {
					order.setCashBackAwarded(null);
					order.setCashBackReceived(false);
					order.setCashBackStartDate(null);
					order.setCashBackEndDate(null);
				}
				info.getOrder().setCashBackReceived(order.getCashBackReceived());
				info.getOrder().setCashBackAwarded(order.getCashBackAwarded());
				info.getOrder().setCashBackEndDate(order.getCashBackEndDate());
				info.getOrder().setCashBackStartDate(order.getCashBackStartDate());
			} catch (Exception e) {
				LOG.error("Error in assigning cash back ", e);
			}
		}
	}

	private void checkCashBack(Order order) {
		cashBackService.checkCashBack(order);

	}

	private void awardCashBackOffer(Order order){
		cashBackService.awardCashBackOffer(order);
	}

	private com.stpl.tech.master.domain.model.Pair<String, String> getFeedbackLinkForQRCode(int feedbackId,
																							FeedbackSource feedbackSource) {
		return feedbackManagementService.getFeedbackLinkForSource(feedbackId, feedbackSource);
	}

	private void checkAmex(Order order) throws DataUpdationException {
		if ("CHAIAMEX".equalsIgnoreCase(order.getOfferCode())) {
			for (Settlement s : order.getSettlements()) {
				if (s.getMode() != AppConstants.PAYMENT_MODE_AMEX) {
					throw new DataUpdationException("Coupon can be used with AMEX payments only");
				}
			}
		}
	}

	private void applyLoyaltyCode(Order order) {
		boolean hasLoyaltyRedemption = false;
		for (OrderItem item : order.getOrders()) {
			if (item.getComplimentaryDetail() != null && item.getComplimentaryDetail().getReasonCode() != null
					&& item.getComplimentaryDetail().getReasonCode() == AppConstants.COMPLEMENTARY_CODE_LOYALTY) {
				item.getComplimentaryDetail().setReason(TransactionConstants.SIGNUP_OFFER_CODE);
				hasLoyaltyRedemption = true;
			}
		}
		if (hasLoyaltyRedemption) {
			order.setOfferCode(TransactionConstants.SIGNUP_OFFER_CODE);
		}
	}

	private void calculateCondimentaryItemQuantity(Order order){
		if(Objects.nonNull(order.getSource()) && Objects.nonNull(order.getOrders()) &&  order.getOrders().size()>0){
			List<CondimentItemData> condimentItemDataList = itemConsumptionHelper.getCondimentItemQuantity(order,order.getSource());
			order.setItemCondiment(condimentItemDataList);
		}
	}

	public void pushOrderNotification(OrderInfo order) {
		OrderPushNotification notification = new OrderPushNotification(order.getOrder().getOrderId(),
				order.getOrder().getStatus(), order.getOrder().getSource(), order.getUnit().getId(),
				getEnvironmentProperties().getAssemblyFirestoreUnits(), String.valueOf(getEnvironmentProperties().isAssemblyFirestoreEnabledForAll()), FireStoreNotificationType.ORDER);
		Integer unitId = order.getUnit().getId();
		EnvType envType = getEnvironmentProperties().getEnvironmentType();
		String relayType = TransactionUtils.getAssemblyRelay(unitId, order.getOrder().getSource());
		notification.setTopic(AppUtils.getAssemblyChannelName(envType.name(), unitId, relayType));
		notification.setSendToAndroid(AppConstants.YES);
		try {
			getFirebaseNotificationService().sendNotification(envType, notification);
		} catch (Exception e) {
			LOG.error("Error while sending push notification to the client", e);
			new ErrorNotification("FCM Push Notification Faliure",
					"Error while sending push notification to the client", e, envType).sendEmail();
		}
	}

	/**
	 * @param order
	 */
	private Order applyFreeItemOffer(boolean newCustomer, Order order) {
		Date businessDate = AppUtils.getBusinessDate();
		if (freeItemOfferService.hasFreeItemOffer(businessDate)
				&& AppConstants.ORDER_TYPE_REGULAR.equals(order.getOrderType()) && order.getOfferCode() == null) {
			if (!hasGiftCardOrComboOrComplimentary(order)) {
				int productId = freeItemOfferService.freeItemOfferProductId(businessDate);
				boolean isInventoryEnabled = getMasterDataCache().getProductBasicDetail(productId).isInventoryTracked();
				try {
					if (isInventoryEnabled) {
						List<Integer> productIds = new ArrayList<>();
						productIds.add(productId);
						List<ProductInventory> inventory = getUnitInventoryManagementService()
								.getUnitInventoryForProducts(order.getUnitId(), productIds);
						if (inventory != null && inventory.size() == 1 && inventory.get(0).getQuantity() > 0) {
							return freeItemOfferService.applyFreeItemOffer(businessDate, newCustomer, order);
						}

					} else {
						return freeItemOfferService.applyFreeItemOffer(businessDate, newCustomer, order);
					}
				} catch (DataNotFoundException e) {
					LOG.error("Error in fetching inventory details for free item offer", e);
				}
			}
		}
		return order;
	}

	/**
	 * @param order
	 * @return
	 */
	private boolean hasGiftCardOrComboOrComplimentary(Order order) {
		for (OrderItem i : order.getOrders()) {
			if (AppUtils.isGiftCard(i.getCode()) || AppUtils.isCombo(i.getCode())) {
				return true;
			}
			if (i.getComplimentaryDetail() != null && i.getComplimentaryDetail().isIsComplimentary()) {
				return true;
			}
		}
		return false;
	}

	private void validatePaidEmployeeMeal(Order order) throws DataUpdationException {
		if (!TransactionUtils.isPaidEmployeeMeal(order)) {
			return;
		}

		BigDecimal orderAmount = order.getTransactionDetail().getPaidAmount();

		BigDecimal availableAllowanceLimit = paidEmpoyeeMealDao
				.getAvailableAllownaceLimit(order.getEmployeeIdForMeal());
		if (orderAmount.compareTo(availableAllowanceLimit) > 0) {
			throw new DataUpdationException(String.format(
					"Employee Meal Issue : order amount (%f) is greater then available allowance limit (%f)",
					orderAmount.setScale(2, BigDecimal.ROUND_HALF_UP),
					availableAllowanceLimit.setScale(2, BigDecimal.ROUND_HALF_UP)));
		}

	}

	/**
	 *
	 */
	protected void updateUnitInventory(Order order, Integer orderId, boolean isCancellation) {
		try {

			UnitBasicDetail unit = getMasterDataCache().getUnitBasicDetail(order.getUnitId());
			// Non live inventory Units
			if (getEnvironmentProperties().getTrackInventory() && !unit.isLiveInventoryEnabled()) {
				List<OrderItem> itemList = new ArrayList<>();
				for (OrderItem item : order.getOrders()) {
					if (!(CashCardType.ECARD.name().equalsIgnoreCase(item.getCardType()))
							&& !voucherService.isGyftrCard(item.getCardType())) {
						itemList.add(item);
					}
				}
				List<ProductInventory> inventoryUpdate = getProductInventoryUpdates(unit, itemList);
				InventoryUpdateEvent event = new InventoryUpdateEvent();
				event.setUnitId(order.getUnitId());
				event.getCurrentInventory().addAll(inventoryUpdate);
				getUnitInventoryManagementService().updateUnitInventory(event, false, true, order.getEmployeeId(),
						orderId, isCancellation);
			}
			// live inventory sync
			if (unit.isLiveInventoryEnabled() && isCancellation) {
				publishInventory(order, InventoryAction.ADD, InventorySource.NON_WASTAGE_ORDER_CANCELLATION);
			}
		} catch (Exception e) {
			LOG.error("Error While Updating Inventory", e);
		}
	}

	protected boolean isRoutedToAssembly(Unit unit, Order order, boolean hasGiftcards, boolean hasSelectOrder) {
		if (UnitCategory.CHAI_MONK.equals(unit.getFamily()) || TransactionUtils.isBillBookOrder(order)
				|| hasGiftcards || hasSelectOrder) {
			return false;
		}
		return TransactionUtils.isTakeawayOrder(order.getSource()) || TransactionUtils.isCODOrder(order.getSource())
				|| TransactionUtils.isWebDineIn(order.getSource(), order.getChannelPartner())
				|| unit.isWorkstationEnabled();
	}

	public void publishInventory(OrderInfo info) {
		if (TransactionUtils.isSpecialOrder(info.getOrder()) && !TransactionUtils.isPaidEmployeeMeal(info.getOrder())) {
			publishInventory(info.getOrder(), InventoryAction.REMOVE, InventorySource.COMPLIMENTARY_ORDER);
		} else {
			publishInventory(info.getOrder(), InventoryAction.REMOVE, InventorySource.CAFE_ORDER);
		}
	}

	public void publishInventory(Order order, InventoryAction action, InventorySource source) {
		LOG.info("Calculating item Consumption for Generated Order ID = " + order.getOrderId());
		try {
			/*
			 * int deliveryUnitId = getMasterDataCache().getDeliveryUnit(order.getUnitId(),
			 * TransactionUtils
			 * .isPartnetOrder(getMasterDataCache().getChannelPartner(order.
			 * getChannelPartner()).getType()));
			 */
			int deliveryUnitId = getMasterDataCache().getDeliveryUnit(order.getUnitId(), order.getChannelPartner(),
					order.getBrandId(), TransactionUtils.isCODOrder(order.getSource()));
			Map<Integer, ProductQuantityData> map = itemConsumptionHelper.getCriticalConsumption(order, deliveryUnitId);
			if (map != null && map.size() > 0) {
				QuantityResponseData response = new QuantityResponseData(order.getUnitId(),
						new ArrayList<>(map.values()), action, source, order.getOrderId(),
						order.getBillingServerTime());
				inventoryService.publishInventorySQSFifo(properties.getEnvironmentType().name(), response);
			}
		} catch (Exception e) {
			LOG.error("Error while calculating Consumption for Generated Order ID = " + order.getOrderId(), e);
		}

	}

	private void sendCashCardNotification(Order order, OrderInfo info, OrderNotification orderNotification) {
		boolean cashCardRedemption = false;
		BigDecimal settledValue = BigDecimal.ZERO;
		if (info.getCustomer().getId() > 5) {
			for (Settlement settlement : info.getOrder().getSettlements()) {
				if (settlement.getMode() == TransactionConstants.PAYMENT_MODE_CASH_CARD) {
					cashCardRedemption = true;
					settledValue = settlement.getAmount();
					break;
				}
			}
		}
		if (cashCardRedemption) {
			 sendCashCardRedemptionNotification(order,info, settledValue,orderNotification);
		}
	}

	private void sendCashBackNotification(OrderInfo info, BigDecimal cashBack, String startDate, String endDate, OrderNotification orderNotification) {

		sendCashCardNotification(info, BigDecimal.ZERO, CashCardSMSNotificationType.CASH_CARD_CASH_BACK_ALLOTMENT,
				BigDecimal.ZERO, cashBack, startDate, endDate,orderNotification);

	}

	private void sendCashCardRedemptionNotification(Order order, OrderInfo info, BigDecimal settledValue, OrderNotification orderNotification) {
		sendCashCardNotification(order,info, BigDecimal.ZERO, CashCardSMSNotificationType.CASH_CARD_REDEMPTION, settledValue,
				BigDecimal.ZERO, null, null,orderNotification);
	}

	private void sendCashCardNotification(OrderInfo info, BigDecimal refundAmount, CashCardSMSNotificationType type,
										  BigDecimal settledAmount, BigDecimal cashBack, String startDate, String endDate, OrderNotification orderNotification) {
		String fName = info.getCustomer().getFirstName();
		fName = Character.toString(fName.charAt(0)).toUpperCase() + fName.substring(1);
		List<CashCardDetail> card = getCardService().getActiveCashCards(info.getCustomer().getId());
		BigDecimal pendingAmount = BigDecimal.ZERO;
		BigDecimal initialAmount = BigDecimal.ZERO;
		BigDecimal extraAmount = BigDecimal.ZERO;
		for (CashCardDetail c : card) {
			pendingAmount = AppUtils.add(pendingAmount, c.getCashPendingAmount()); //var1
			initialAmount = AppUtils.add(initialAmount, c.getCashInitialAmount());
			extraAmount = AppUtils.add(extraAmount, c.getInitialOffer());
		}
		BigDecimal percentageOffer = AppUtils.percentage(extraAmount,initialAmount);
		BigDecimal savingAmount = AppUtils.percentageOf(percentageOffer,settledAmount);
		CashCardNotificationData data = new CashCardNotificationData();
		data.setCustomerName(fName);
		data.setPendingAmount(pendingAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
		data.setUnitName(info.getUnit().getName());
		data.setRefundAmount(refundAmount);
		data.setUsedAmount(settledAmount);
		data.setCashBackAmount(cashBack);
		data.setStartDate(startDate);
		data.setEndDate(endDate);
		orderNotification = getCashCardCashBackAllotmentNotificationMetaData(orderNotification,data);
		try {
			Map<String,String> payloadMap = new HashMap<>();
			payloadMap.put("pendingAmount",pendingAmount.toString());//v1
			payloadMap.put("savingAmount",savingAmount.toString());//v2
			payloadMap.put("generateOrderId",info.getOrder().getGenerateOrderId());//v2
			String message = type.getMessage(data);
			notificationService.sendNotification(type.name(), message, info.getCustomer().getContactNumber(),
					providerService.getSMSClient(type.getTemplate().getSMSType(), ApplicationName.KETTLE_SERVICE),
					getEnvironmentProperties().getSendAutomatedOTPSMS(), getNotificationPayload(type, info, payloadMap));
			/*if (getEnvironmentProperties().getSystemGeneratedNotifications() || type.name().equals("CASH_CARD_REFUND") ) {//since refund flow to be made
				notificationService.sendNotification(type.name(), message, info.getCustomer().getContactNumber(),
						providerService.getSMSClient(type.getTemplate().getSMSType(), ApplicationName.KETTLE_SERVICE),
						getEnvironmentProperties().getSendAutomatedOTPSMS(), getNotificationPayload(type, info, payloadMap));
			}
			else {
				//cashCardCashbackAllotment and cashCardRefund goes through here
				if(type.name().equals("CASH_CARD_CASH_BACK_ALLOTMENT")){
					getCashCardCashBackAllotmentNotificationMetaData(orderNotification, data);
				}
			}*/
		} catch (IOException | JMSException e) {
			LOG.error("WHATSAPP_NOTIFICATOIN :::Error while sending the Cash Card Redemption message for " + info.getOrder().getOrderId(), e);
		}
	}

	private OrderNotification getCashCardCashBackAllotmentNotificationMetaData(OrderNotification orderNotification, CashCardNotificationData cashCardNotificationData) {
		if (Objects.nonNull(orderNotification) && Objects.nonNull(cashCardNotificationData)) {
			//cashCardCashbackAllotment
			orderNotification.setCashBackAmount(cashCardNotificationData.getCashBackAmount());
			orderNotification.setCashBackAllotmentStartDate(cashCardNotificationData.getStartDate());
			orderNotification.setCashBackAllotmentEndDate(cashCardNotificationData.getEndDate());
			//cashCardRefund flow to be made
//			orderNotification.setCustomerName(cashCardNotificationData.getCustomerName());
//			orderNotification.setRefundAmount(cashCardNotificationData.getRefundAmount().intValue());
//			orderNotification.setPendingAmount(cashCardNotificationData.getPendingAmount().intValue());
		}
		return orderNotification;
	}

	private void sendCashCardNotification(Order order,OrderInfo info, BigDecimal refundAmount, CashCardSMSNotificationType type,
										  BigDecimal settledAmount, BigDecimal cashBack, String startDate, String endDate,OrderNotification orderNotification) {
		String fName = info.getCustomer().getFirstName();
		fName = Character.toString(fName.charAt(0)).toUpperCase() + fName.substring(1);
		List<CashCardDetail> card = getCardService().getActiveCashCards(info.getCustomer().getId());
		BigDecimal pendingAmount = BigDecimal.ZERO;
		BigDecimal initialAmount = BigDecimal.ZERO;
		BigDecimal extraAmount = BigDecimal.ZERO;
		for (CashCardDetail c : card) {
			pendingAmount = AppUtils.add(pendingAmount, c.getCashPendingAmount()); //var1
			initialAmount = AppUtils.add(initialAmount, c.getCashInitialAmount());
			extraAmount = AppUtils.add(extraAmount, c.getInitialOffer());
		}
		BigDecimal percentageOffer = AppUtils.percentage(extraAmount,initialAmount);
		BigDecimal savingAmount = AppUtils.percentageOf(percentageOffer,settledAmount);
		CashCardNotificationData data = new CashCardNotificationData();
		data.setCustomerName(fName);
		data.setPendingAmount(pendingAmount.setScale(2, BigDecimal.ROUND_HALF_UP));
		data.setUnitName(info.getUnit().getName());
		data.setRefundAmount(refundAmount);
		data.setUsedAmount(settledAmount);
		data.setCashBackAmount(cashBack);
		data.setStartDate(startDate);
		data.setEndDate(endDate);
		try {
			Map<String,String> payloadMap = new HashMap<>();
			String payloadType = "WALLET";
			Map<String,String> orderPayloadMap = new HashMap<>();
			if(!order.getWhatsappNotificationPayload().isEmpty()){
				orderPayloadMap = order.getWhatsappNotificationPayload();
			}
			if(!order.getWhatsappNotificationPayloadType().isEmpty()){
				payloadType = order.getWhatsappNotificationPayloadType() +"_"+payloadType;
			}
			payloadMap.put("pendingAmount",pendingAmount.toString());//v1
			payloadMap.put("savingAmount",savingAmount.toString());//v2
			payloadMap.put("generateOrderId",info.getOrder().getGenerateOrderId());//v2
			orderPayloadMap.put("walletPendingAmount",pendingAmount.toString());//v1
			orderPayloadMap.put("walletSavingAmount",savingAmount.toString());//v2
			if(!orderPayloadMap.containsKey("generateOrderId")){
				orderPayloadMap.put("generateOrderId",info.getOrder().getGenerateOrderId());//v
			}
			order.setWhatsappNotificationPayload(orderPayloadMap);
			order.setWhatsappNotificationPayloadType(payloadType);
			String message = type.getMessage(data);
//			if(Objects.isNull(info.getCustomer().getOptWhatsapp()) || (Objects.nonNull(info.getCustomer().getOptWhatsapp()) && info.getCustomer().getOptWhatsapp().equals(AppConstants.NO))){
				if (getEnvironmentProperties().getSystemGeneratedNotifications()) {
//				notificationService.sendNotification(type.name(), message, info.getCustomer().getContactNumber(),
//						providerService.getSMSClient(type.getTemplate().getSMSType(), ApplicationName.KETTLE_SERVICE),
//						getEnvironmentProperties().getSendAutomatedOTPSMS(),getNotificationPayload(type,info,payloadMap));
                }else {
					orderNotification.setWalletSavingAmount(savingAmount.toString());
					orderNotification.setWalletUsed(Boolean.TRUE);
                    getCashCardRedemptionNotificationMetaData(orderNotification, data);
                }
//			}

		} catch (Exception e) {
			LOG.error("WHATSAPP_NOTIFICATOIN :::Error while sending the Cash Card Redemption message for " + info.getOrder().getOrderId(), e);
		}

	}

    private OrderNotification getCashCardRedemptionNotificationMetaData(OrderNotification orderNotification, CashCardNotificationData cashCardNotificationData) {
		Stopwatch watch = Stopwatch.createUnstarted();
		watch.start();
        if (Objects.nonNull(cashCardNotificationData)) {
            orderNotification.setUsedAmount(cashCardNotificationData.getUsedAmount().intValue());
            orderNotification.setCafeName(cashCardNotificationData.getUnitName());
            orderNotification.setWalletPendingAmount(cashCardNotificationData.getPendingAmount().intValue());
        }
		LOG.info("<--------Setting Cash Card Redemption Notification metadata took :{} ms-------->",watch.stop().elapsed());
        return orderNotification;
    }

    private void sendCashCardPurchaseNotification(Order order, OrderInfo info, OrderNotification orderNotification) {
		if (order.getCustomerId() > 5) {
			List<OrderItem> cards = new ArrayList<>();
			for (OrderItem item : order.getOrders()) {
				if (AppUtils.isGiftCard(item.getCode())) {
					cards.add(item);
				}
			}
			if (!cards.isEmpty()) {
				List<CashCardDetail> card = getCardService().getActiveCashCards(info.getCustomer().getId());
				BigDecimal pendingAmount = BigDecimal.ZERO;
				BigDecimal pendingAmountOfOrder = BigDecimal.ZERO;
				BigDecimal purchaseAmount = BigDecimal.ZERO;
				for (CashCardDetail c : card) {
					pendingAmount = AppUtils.add(pendingAmount, c.getCashPendingAmount());
					if(Objects.equals(info.getOrder().getOrderId(), c.getPurchaseOrderId())) {
						BigDecimal extraAmount = AppUtils.subtract(c.getCashPendingAmount(),c.getCashInitialAmount());
						pendingAmountOfOrder = AppUtils.add(pendingAmountOfOrder,extraAmount);
					}
				}
				boolean sendNotification = false;
				for (OrderItem item : cards) {
					try {
						if (CashCardType.GYFTR.name().equals(item.getCardType())) {
							String message = TransactionUtils.getGyftrPurchaseCashCardMessage(info, item,
									getCardService().getCardDetail(info.getCustomer().getId(), item.getItemCode(),
											false),
									pendingAmount);
							CashCardSMSNotificationType type = CashCardSMSNotificationType.CASH_CARD_PURCHASE;

                            if (getEnvironmentProperties().getSystemGeneratedNotifications()) {
                                /*notificationService.sendNotification(type.name(), message,
                                        info.getCustomer().getContactNumber(),
                                        SolsInfiniWebServiceClient.getTransactionalClient(), true, null);*/
                            } else {
                                orderNotification.setCustomerName(info.getCustomer().getFirstName());
                                orderNotification.setItemCode(item.getItemCode().toUpperCase());
                                orderNotification.setCashPendingAmount(getCardService().getCardDetail(info.getCustomer().getId(), item.getItemCode(),
                                        false).getCashPendingAmount().setScale(0, BigDecimal.ROUND_HALF_UP).toString());
                                orderNotification.setVoucherCode(item.getVoucherCode());
                                orderNotification.setSmsTemplateDate(AppUtils.getSMSTemplateDate(item.getValidUpto()));
                                orderNotification.setWalletPendingAmount(pendingAmount.setScale(0, BigDecimal.ROUND_HALF_UP).intValue());
                            }
                        } else {
							purchaseAmount = AppUtils.add(purchaseAmount, item.getAmount());
							sendNotification = true;
						}
					} catch (Exception e) {
						LOG.error("Error while sending notification to the customer for cash card purchase : "
								+ info.getOrder().getOrderId(), e);
					}
				}
				if (sendNotification) {
					try {
						Map<String,String> payload = new HashMap<>();
						payload.put("firstName",info.getCustomer().getFirstName());
						payload.put("purchaseAmount",purchaseAmount.toString());
						payload.put("extraAmount",pendingAmountOfOrder.toString());
						CashCardNotificationData notification = new CashCardNotificationData();
						notification.setPendingAmount(pendingAmount);
						notification.setPurchaseAmount(purchaseAmount);
						notification.setCustomerName(info.getCustomer().getFirstName());

						CashCardSMSNotificationType type = CashCardSMSNotificationType.CASH_CARD_PURCHASE;
						String message = type.getMessage(notification);

						if (getEnvironmentProperties().getSystemGeneratedNotifications()) {
							/*notificationService.sendNotification(type.name(), message,
									info.getCustomer().getContactNumber(),
									providerService.getSMSClient(type.getTemplate().getSMSType(),
											ApplicationName.KETTLE_SERVICE),
									getEnvironmentProperties().getSendAutomatedOTPSMS(),
									getNotificationPayload(CashCardSMSNotificationType.CASH_CARD_PURCHASE, info, payload, order.getChannelPartner()));*/
						}
						else {
							orderNotification.setWalletExtraAmount(pendingAmountOfOrder);
							getCashCardPurchaseNotificationMetaData(orderNotification, notification);
						}
					} catch (Exception e) {
						LOG.error("WHATSAPP_NOTIFICATOIN ::Error while sending notification to the customer for cash card purchase : "
								+ info.getOrder().getOrderId(), e);
					}
				}

			}
		}
	}

	private OrderNotification getCashCardPurchaseNotificationMetaData(OrderNotification orderNotification, CashCardNotificationData cashCardNotificationData) {
		if (Objects.nonNull(cashCardNotificationData)) {
			orderNotification.setCustomerName(cashCardNotificationData.getCustomerName());
			orderNotification.setWalletPurchaseAmt(cashCardNotificationData.getPurchaseAmount().intValue());
			orderNotification.setWalletPendingAmount(cashCardNotificationData.getPendingAmount().intValue());
			orderNotification.setIsWalletPurchased(Boolean.TRUE);
		}
		return orderNotification;
	}

	public void sendCashCardRefundNotification(OrderInfo info) throws CardValidationException {
		if (info.getCustomer() != null && info.getCustomer().getId() > 5 && info.getOrder() != null) {
			BigDecimal refundAmount = BigDecimal.ZERO;
			boolean refund = false;
			for (Settlement settlement : info.getOrder().getSettlements()) {
				if (settlement.getMode() == TransactionConstants.PAYMENT_MODE_CASH_CARD) {
					if (settlement.getExternalSettlements() != null) {
						refund = true;
						for (ExternalSettlement externalSettlement : settlement.getExternalSettlements()) {
							refundAmount = AppUtils.add(refundAmount, externalSettlement.getAmount());
						}
					}
				}
			}
			if (refund && refundAmount.intValue() > 0) {
				sendCashCardNotification(info, refundAmount, CashCardSMSNotificationType.CASH_CARD_REFUND,
						BigDecimal.ZERO, BigDecimal.ZERO, null, null,null);
			}
		}
	}


	/**
	 * This methods removes customer from order when order has a complimentary item
	 * marked as SAMPLING
	 *
	 * @param order
	 */
	private void complimentryValidation(Order order) {
		if (TransactionUtils.isSpecialOrder(order)) {
			if (order.getCustomerId() == null || (!TransactionUtils.isCODOrder(order.getSource())
					&& properties.getDummyCustomerId() != order.getCustomerId())) {
				order.setCustomerId(properties.getDummyCustomerId());
			}
		}
	}

	public boolean hasGiftCard(Order order) {
		for (OrderItem item : order.getOrders()) {
			if (AppUtils.isGiftCard(item.getCode())) {
				return true;
			}
		}
		return false;
	}

	public boolean hasSelectOrder(Order order) {
		return order.getOrders() != null && order.getOrders().size() > 0
				&& isSubscriptionPresent(order);
	}

	public boolean cancelSubscription(Order order) throws DataUpdationException {
		return getCustomerOfferManagementService().cancelSubscription(order.getCustomerId());
	}

	private boolean isSubscriptionPresent(Order order){
		for (com.stpl.tech.kettle.domain.model.OrderItem item : order.getOrders()) {
			if (Objects.nonNull(getMasterDataCache().getSubscriptionProductDetail(item.getProductId()))) {
				return true;
			}
		}
		return false;
	}

	private void validateEmployeeMeal(Order order) throws DataUpdationException {
		if (!TransactionUtils.isEmployeeMeal(order)) {
			return;
		}
		StringBuffer buffer = new StringBuffer();
		boolean hasEmployeeMeal = false;
		int employeeMealCount = 0;
		Map<String, Pair<Integer, Integer>> map = TransactionUtils.getEmployeeMealProductThreshold();
		List<EmployeeMealData> existingEmployeeMeal = getOrderSearchService()
				.getEmployeeMealData(order.getEmployeeIdForMeal());
		if (existingEmployeeMeal != null && existingEmployeeMeal.size() > 0) {
			for (EmployeeMealData data : existingEmployeeMeal) {
				if (data.getProductTypeId() == TransactionConstants.BEVERAGE_COLD_PRODUCT_TYPE
						|| data.getProductTypeId() == TransactionConstants.BEVERAGE_HOT_PRODUCT_TYPE) {
					Pair<Integer, Integer> pair = map.get(TransactionConstants.BEVERAGE_PRODUCT_TYPE_STR);
					Pair<Integer, Integer> newPair = Pair.of(pair.getFirst(), pair.getSecond() + data.getQuantity());
					map.put(TransactionConstants.BEVERAGE_PRODUCT_TYPE_STR, newPair);
				} else if (data.getProductTypeId() == TransactionConstants.FOOD_PRODUCT_TYPE) {
					Pair<Integer, Integer> pair = map.get(TransactionConstants.FOOD_PRODUCT_TYPE_STR);
					Pair<Integer, Integer> newPair = Pair.of(pair.getFirst(), pair.getSecond() + data.getQuantity());
					map.put(TransactionConstants.FOOD_PRODUCT_TYPE_STR, newPair);
				}
			}
		}
		for (OrderItem item : order.getOrders()) {
			if (item.getComplimentaryDetail() != null && item.getComplimentaryDetail().isIsComplimentary()
					&& item.getComplimentaryDetail().getReasonCode() == AppConstants.EMPLOYEE_MEAL_ID) {
				hasEmployeeMeal = true;
				employeeMealCount++;
				IdCodeName category = getMasterDataCache()
						.getProductCategory(getMasterDataCache().getProduct(item.getProductId()).getType()).getDetail();
				item.setProductCategory(category);
				if (category.getId() != TransactionConstants.BEVERAGE_COLD_PRODUCT_TYPE
						&& category.getId() != TransactionConstants.BEVERAGE_HOT_PRODUCT_TYPE
						&& category.getId() != TransactionConstants.FOOD_PRODUCT_TYPE) {
					buffer.append("Cannot Order products in category " + category.getCode() + " for employee meal\n");
				}
				if (!getMasterDataCache().getEmployeeMealProducts().contains(item.getProductId())) {
					buffer.append("Cannot Order " + item.getProductName() + " for employee meal\n");
				} else {
					if (!getMasterDataCache().getEmployeeMealDimensions().contains(item.getDimension())) {
						buffer.append("Cannot Order " + item.getProductName() + " of size " + item.getDimension()
								+ " for employee meal\n");
					}
					Pair<Integer, Integer> pair = null;
					Pair<Integer, Integer> newPair = null;
					if (category.getId() == TransactionConstants.BEVERAGE_COLD_PRODUCT_TYPE
							|| category.getId() == TransactionConstants.BEVERAGE_HOT_PRODUCT_TYPE) {
						pair = map.get(TransactionConstants.BEVERAGE_PRODUCT_TYPE_STR);
						newPair = Pair.of(pair.getFirst(), pair.getSecond() + item.getQuantity());
						map.put(TransactionConstants.BEVERAGE_PRODUCT_TYPE_STR, newPair);
					} else {
						pair = map.get(TransactionConstants.FOOD_PRODUCT_TYPE_STR);
						newPair = Pair.of(pair.getFirst(), pair.getSecond() + item.getQuantity());
						map.put(TransactionConstants.FOOD_PRODUCT_TYPE_STR, newPair);
					}
					if (newPair.getFirst() < (newPair.getSecond())) {
						buffer.append("Cannot Order more than " + pair.getFirst() + " quantity for category "
								+ category.getCode() + ", Total Quanity is " + newPair.getSecond()
								+ " which is greater than threshold\n");
					}
				}
			}
		}
		if (hasEmployeeMeal && employeeMealCount != order.getOrders().size()) {
			buffer.append("One of the items is not marked as employee meal. Please mark all items as employee meal\n");
		}

		/*
		 * if (hasEmployeeMeal && employeeMealCount > 2) { buffer.append(
		 * "Cannot Order more than 2 items in employee meal\n"); }
		 */
		if (buffer.toString().length() > 0) {
			throw new DataUpdationException("Employee Meal Issue : \n" + buffer.toString());
		}

		// No customer for employee meal
		// hence no Loyalty score problems
		if (order.isEmployeeMeal()) {
			order.setCustomerId(properties.getDummyCustomerId());
			order.setCustomerName(null);
		}
	}

	protected void publish(OrderInfo info) {
		if (!TransactionUtils.isTableOrder(info.getOrder())) {

			if (getEnvironmentProperties().publishOrders()) {
				try {
					OrderResponse response = new OrderResponse(
							getMasterDataCache().getUnitBasicDetail(info.getOrder().getUnitId()), info.getOrder(),
							info.getCustomer(), info.getDeliveryDetails(),info.getCommunicationType());
					producer.send(session.createObjectMessage(response));
				} catch (JMSException e) {
					LOG.error("Error while adding order to the message queue", e);
				}
			}
		}

	}

	protected void publishAllOrders(List<Integer> orderIdList) {
		for (Integer orderId : orderIdList) {
			try {
				OrderInfo info = getOrderSearchService().getOrderReceipt(orderId, false, null);
				publish(info);
			} catch (Exception e) {
				LOG.error("Error while publishing customer", e);
			}
		}
	}

	private List<ProductInventory> getProductInventoryUpdates(UnitBasicDetail unit, List<OrderItem> items) {
		List<ProductInventory> list = new ArrayList<>();
		for (OrderItem item : items) {
			ProductBasicDetail detail = getMasterDataCache().getProductBasicDetail(item.getProductId());
			if (detail.isInventoryTracked()) {
				list.add(get(unit, detail, item.getQuantity()));
			} else if (detail.getType() == AppConstants.CHAAYOS_COMBO_PRODUCT_TYPE) {
				for (OrderItem addon : item.getComposition().getMenuProducts()) {
					ProductBasicDetail addonProduct = getMasterDataCache().getProductBasicDetail(addon.getProductId());
					if (addonProduct.isInventoryTracked()) {
						list.add(get(unit, addonProduct, item.getQuantity()));
					}
				}
			}
		}
		return list;
	}

	private ProductInventory get(UnitBasicDetail unit, ProductBasicDetail detail, int quantity) {
		ProductInventory product = new ProductInventory();
		product.setUnit(unit);
		product.setProduct(detail);
		product.setQuantity(quantity);
		return product;
	}

	/*
	 * @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
	 *
	 * @ExceptionHandler(CardValidationException.class)
	 *
	 * @ResponseBody public ErrorInfo handleCardValidationException(Exception ex) {
	 * LOG.error(HttpStatus.INTERNAL_SERVER_ERROR.name(), ex); return new
	 * ErrorInfo(HttpStatus.INTERNAL_SERVER_ERROR.name(), ex); }
	 */

	@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
	@ExceptionHandler(PaymentFailureException.class)
	@ResponseBody
	public ErrorInfo handlePaymentFailureException(Exception ex) {
		LOG.error(HttpStatus.INTERNAL_SERVER_ERROR.name(), ex);
		return new ErrorInfo(HttpStatus.INTERNAL_SERVER_ERROR.name(), ex);
	}

	/**
	 * @param generatedOrderId
	 * @throws Exception
	 */
	protected void bookWastage(String generatedOrderId) throws Exception {
		LOG.info("Calculating item Consumption for Generated Order ID = " + generatedOrderId);
		Order o = getOrderSearchService().getOrderDetail(generatedOrderId);
		bookWastage(o);

	}

	/**
	 * @param generatedOrderId
	 * @throws DataNotFoundException
	 * @throws Exception
	 */
	protected void checkBookWastage(String generatedOrderId) throws DataUpdationException, DataNotFoundException {
		LOG.info("Calculating item Consumption for Generated Order ID = " + generatedOrderId);
		Order o = getOrderSearchService().getOrderDetail(generatedOrderId);
		checkBookWastage(o);
	}

	protected void checkBookWastage(Order o) throws DataUpdationException {
		Map<Integer, Consumable> map = new HashMap<>();
		try {
			/*
			 * int deliveryUnitId = getMasterDataCache().getDeliveryUnit(o.getUnitId(),
			 * TransactionUtils
			 * .isPartnetOrder(getMasterDataCache().getChannelPartner(o.getChannelPartner())
			 * .getType()));
			 */
			int deliveryUnitId = getMasterDataCache().getDeliveryUnit(o.getUnitId(), o.getChannelPartner(),
					o.getBrandId(), TransactionUtils.isCODOrder(o.getSource()));
			itemConsumptionHelper.calculateConsumption(o, map, deliveryUnitId, false);
		} catch (Exception e) {
			LOG.error("Error while calculating Consumption", e);
			throw e;
		}
		if (o != null && map.values().size() > 0) {
			WastageEvent event = getWastageEvent(o, map.values(), true);
			List<String> errors = verifyWastageBookingInSumo(event);
			if (errors != null && errors.size() > 0) {
				String errorMsg = String.join("<br/>", errors);
				throw new DataUpdationException("Error while verifying the inventory price data for unit "
						+ o.getUnitId() + "<br/>" + errorMsg);
			}
		}
	}

	protected void bookWastage(Order o) {
		Map<Integer, Consumable> map = new HashMap<>();
		try {
			/*
			 * int deliveryUnitId = getMasterDataCache().getDeliveryUnit(o.getUnitId(),
			 * TransactionUtils
			 * .isPartnetOrder(getMasterDataCache().getChannelPartner(o.getChannelPartner())
			 * .getType()));
			 */
			int deliveryUnitId = getMasterDataCache().getDeliveryUnit(o.getUnitId(), o.getChannelPartner(),
					o.getBrandId(), TransactionUtils.isCODOrder(o.getSource()));
			itemConsumptionHelper.calculateConsumption(o, map, deliveryUnitId, false);
		} catch (Exception e) {
			LOG.error("Error while calculating Consumption", e);
			throw e;
		}
		if (o != null && map.values().size() > 0) {
			int wastageEventId = getOrderManagementService().addCost(o, map.values());
			WastageEvent event = getWastageEvent(o, map.values(), false);
			persistWastageToSumo(event, wastageEventId);
		}
	}

	/**
	 * @param o
	 * @param values
	 * @param forVerification
	 */
	private WastageEvent getWastageEvent(Order o, Collection<Consumable> values, boolean forVerification) {
		WastageEvent event = new WastageEvent();
		event.setBusinessDate(AppUtils.getBusinessDate());
		String reason = null;
		if (TransactionUtils.isSpecialOrder(o)) {
			event.setGeneratedBy(o.getEmployeeId());
			event.setGenerationTime(o.getBillingServerTime());
			Integer reasonId = -1;
			for (com.stpl.tech.kettle.domain.model.OrderItem item : o.getOrders()) {
				if (item.getComplimentaryDetail().getReasonCode() != AppConstants.COMPLEMENTARY_CODE_COMBO) {
					reasonId = item.getComplimentaryDetail().getReasonCode();
					break;
				}
			}
			if (reasonId != -1) {
				for (IdCodeName code : getMetadataCache().getAllComplimentaryCodes()) {
					if (code.getId() == reasonId) {
						reason = code.getCode();
						break;
					}
				}
			}
		} else if (!TransactionUtils.isSpecialOrder(o) && o.getCancellationDetails() != null) {
			if (!forVerification) {
				event.setGeneratedBy(o.getCancellationDetails().getGeneratedBy());
				event.setGenerationTime(o.getCancellationDetails().getActionTime());
			}
			reason = AppConstants.ORDER_CANCELLATION;
		} else {
			return null;
		}
		event.setKettleReason(reason);
		event.setLinkedKettleIdType("HouseCostEventId");
		event.setStatus("SETTLED");
		event.setType("PRODUCT");
		event.setUnitId(o.getUnitId());
		for (Consumable c : values) {
			WastageData d = new WastageData();
			d.setComment(reason);
			d.setProductId(c.getProductId());
			d.setQuantity(c.getQuantity());
			event.getItems().add(d);
		}

		return event;
	}

	private void persistWastageToSumo(WastageEvent event, int wastageEventId) {
		if (event == null) {
			return;
		}
		event.setLinkedKettleId(wastageEventId);
		try {
			List<WastageEvent> events = Arrays.asList(event);
			List<?> results = callWebServiceWithTimeout(List.class, getEnvironmentProperties().addWastageURL(),
					events,WASTAGE_IN_SOCKET_TIMEOUT,WASTAGE_IN_CONNECTION_TIMEOUT);
			@SuppressWarnings("unchecked")
			LinkedTreeMap<String, Object> linkedTreeMap = (LinkedTreeMap<String, Object>) results.get(0);
			getOrderManagementService().setWastageSumoId(wastageEventId,
					((Double) linkedTreeMap.get("wastageId")).intValue());
		} catch (Exception e) {
			String message = "Error while adding cancelled order wastage to sumo for wastage event id :"
					+ wastageEventId;
			LOG.error(message, e);
			SlackNotificationService.getInstance().sendNotification(getEnvironmentProperties().getEnvironmentType(),
					ApplicationName.KETTLE_SERVICE.name(), SlackNotification.SYSTEM_ERRORS, message);
		}
	}

	private List<String> verifyWastageBookingInSumo(WastageEvent event) throws DataUpdationException {
		try {
			List<WastageEvent> events = Arrays.asList(event);
			List<?> results = callWebServiceWithTimeout(List.class,
					getEnvironmentProperties().verifyPriceDataURL(), events,WASTAGE_IN_SOCKET_TIMEOUT,WASTAGE_IN_CONNECTION_TIMEOUT);
			LinkedTreeMap<String, Object> linkedTreeMap = (LinkedTreeMap<String, Object>) results.get(0);
			Object data = linkedTreeMap.get("errors");
			return data == null ? null : ((List<String>) data);
		} catch (Exception e) {
			String message = "Error while adding verifying order wastage in sumo for wastage event :"
					+ JSONSerializer.toJSON(event);
			LOG.error(message, e);
			SlackNotificationService.getInstance().sendNotification(getEnvironmentProperties().getEnvironmentType(),
					ApplicationName.KETTLE_SERVICE.name(), SlackNotification.SYSTEM_ERRORS, message);
			throw new DataUpdationException("Error while Verifying Wastage", e);
		}
	}

	private void generateOrderNotifcationEvent(OrderInfo info){
		Map<NotificationType,Boolean> hashmap = new HashMap<>();
		hashmap.put(NotificationType.EMAIL,false);
		info.setCommunicationType(hashmap);
	}


	public abstract MetadataCache getMetadataCache();

	public abstract MasterDataCache getMasterDataCache();

	public abstract OrderManagementService getOrderManagementService();

	public abstract DeliveryRequestService getDeliveryRequestService();

	public abstract EnvironmentProperties getEnvironmentProperties();

	public abstract OrderInfoCache getOrderInfoCache();

	public abstract CustomerService getCustomerService();

	public abstract UnitInventoryManagementService getUnitInventoryManagementService();

	public abstract CustomerOfferManagementService getCustomerOfferManagementService();

	public abstract OfferManagementExternalService getOfferManagementService();

	public abstract OfferManagementService getOfferService();

	public abstract CardService getCardService();

	public abstract FirebaseNotificationService getFirebaseNotificationService();
	public abstract FeedbackManagementService getFeedbackManagementService();
	public abstract TokenService<FeedbackTokenInfo> getTokenService();
	public abstract PosMetadataService getPosMetadataService();

	/**
	 * @return
	 */
	public abstract OrderSearchService getOrderSearchService();

	public FreeItemOfferManagementService getFreeOfferItemService() {
		return freeItemOfferService;
	}

	private void validateSpecialOrder(Order order) throws DataUpdationException {
		LOG.info("Request to validate special order of type : {}",order.getOrderType());
		getOrderManagementService().validateSpecialOrder(order);
	}


}
