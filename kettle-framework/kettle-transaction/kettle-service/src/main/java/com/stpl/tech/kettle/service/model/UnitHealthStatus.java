/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.service.model;

import java.util.ArrayList;
import java.util.List;

import com.stpl.tech.master.domain.model.UnitCategory;

public class UnitHealthStatus {

	private int unitId;

	private String unitName;

	private UnitCategory unitCategory;

	private List<UnitHealthData> screenStatus;

	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		if (this.unitName == null) {
			this.unitName = unitName;
		}
	}

	public List<UnitHealthData> getScreenStatus() {
		if (screenStatus == null) {
			screenStatus = new ArrayList<UnitHealthData>();
		}
		return screenStatus;
	}

	public void setScreenStatus(List<UnitHealthData> screenStatus) {
		this.screenStatus = screenStatus;
	}

	public UnitCategory getUnitCategory() {
		return unitCategory;
	}

	public void setUnitCategory(UnitCategory unitCategory) {
		this.unitCategory = unitCategory;
	}

}
