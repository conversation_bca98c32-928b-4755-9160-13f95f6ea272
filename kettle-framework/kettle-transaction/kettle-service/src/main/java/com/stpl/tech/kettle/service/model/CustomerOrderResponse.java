package com.stpl.tech.kettle.service.model;

import java.io.Serializable;
import java.util.List;

import com.stpl.tech.kettle.domain.model.Order;

public class CustomerOrderResponse implements Serializable{

    /**
	 * 
	 */
	private static final long serialVersionUID = -7337703465022630467L;
	int customerId;
    List<Order> orderDetails;

    public CustomerOrderResponse() {
    }

    public CustomerOrderResponse(int customerId, List<Order> orderDetails) {
        this.customerId = customerId;
        this.orderDetails = orderDetails;
    }

    public int getCustomerId() {
        return customerId;
    }

    public void setCustomerId(int customerId) {
        this.customerId = customerId;
    }

    public List<Order> getOrderDetails() {
        return orderDetails;
    }

    public void setOrderDetails(List<Order> orderDetails) {
        this.orderDetails = orderDetails;
    }
}
