package com.stpl.tech.kettle.service.process;

import com.itextpdf.text.BaseColor;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.ExceptionConverter;
import com.itextpdf.text.Image;
import com.itextpdf.text.pdf.PdfContentByte;
import com.itextpdf.text.pdf.PdfName;
import com.itextpdf.text.pdf.PdfPCell;
import com.itextpdf.text.pdf.PdfPTable;
import com.itextpdf.text.pdf.PdfPageEventHelper;
import com.itextpdf.text.pdf.PdfTemplate;
import com.itextpdf.text.pdf.PdfWriter;

import java.io.IOException;
import java.net.MalformedURLException;

public class HeaderFooterPageEvents extends PdfPageEventHelper {
	private PdfTemplate t;
	private PdfPTable headerTable;

	private Image total;
	private float tableHeight1;


	public float getHeaderTableHeight() {
		return tableHeight1;
	}

	public HeaderFooterPageEvents() throws DocumentException, IOException {
//		initialising the header table ---->
		headerTable = new PdfPTable(1);
		headerTable.setWidths(new int[]{100});
		headerTable.setTotalWidth(527);
		headerTable.setLockedWidth(true);
		headerTable.getDefaultCell().setFixedHeight(40);
		headerTable.getDefaultCell().setBorder(0);
		headerTable.getDefaultCell().setBackgroundColor(BaseColor.GREEN);
		Image logo = Image.getInstance(HeaderFooterPageEvents.class.getResource("/img/ChaayosHeader.png"));
		PdfPCell headercell = new PdfPCell(logo,true);
		headercell.setBorder(0);
		headerTable.addCell(headercell);
		tableHeight1 = headerTable.getTotalHeight();
	}

	public void onOpenDocument(PdfWriter writer, Document document) {
		t = writer.getDirectContent().createTemplate(30, 16);
		try {
			total = Image.getInstance(t);
			total.setRole(PdfName.ARTIFACT);
		} catch (DocumentException de) {
			throw new ExceptionConverter(de);
		}
	}

	@Override
	public void onEndPage(PdfWriter writer, Document document) {
		addHeader(writer, document);
		addFooter(writer);
	}

	private void addHeader(PdfWriter writer, Document document) {
		PdfPTable header = new PdfPTable(1);
		try {
			// set defaults
			headerTable.setWidthPercentage(100);
			headerTable.getDefaultCell().setBorder(0);

			// write content
			headerTable.writeSelectedRows(0, -1, document.left(), 835,
		    writer.getDirectContent());

		} catch (Exception e) {
			e.printStackTrace();
		}
	}


	private void addFooter(PdfWriter writer){
		PdfPTable footer = new PdfPTable(1);
		try {

			footer.setWidths(new int[]{100});
			footer.setTotalWidth(527);
			footer.setLockedWidth(true);
			footer.getDefaultCell().setFixedHeight(40);
			footer.getDefaultCell().setBorder(0);


			// add image
			Image logo = Image.getInstance(HeaderFooterPageEvents.class.getResource("/img/RevenueCertificateFooter.png"));
			PdfPCell cell = new PdfPCell(logo,true);
			cell.setBorder(0);
			footer.addCell(cell);

			// write page
			PdfContentByte canvas = writer.getDirectContent();
			canvas.beginMarkedContentSequence(PdfName.ARTIFACT);
			footer.writeSelectedRows(0, -1, 34, 200, canvas);
			canvas.endMarkedContentSequence();
		} catch(DocumentException de) {
			throw new ExceptionConverter(de);
		} catch (MalformedURLException e) {
			throw new ExceptionConverter(e);
		} catch (IOException e) {
			throw new ExceptionConverter(e);
		}
	}

	public void onCloseDocument(PdfWriter writer, Document document) {
	}
}
