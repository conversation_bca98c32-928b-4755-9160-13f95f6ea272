/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.service.controller;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.stpl.tech.kettle.core.monitoring.UnitHealthCache;
import com.stpl.tech.kettle.core.monitoring.UnitMonitorData;
import com.stpl.tech.master.core.exception.DataNotFoundException;

@Component
public class UnitHealthResource {

	@Autowired
	private UnitHealthCache cache;

	private static final Logger LOG = LoggerFactory.getLogger(UnitHealthResource.class);

	public void removeFromQueue() throws InterruptedException, DataNotFoundException {
		// Take elements out from the DelayQueue object.
		if (cache.getQueue().peek() == null) {
			return;
		}
		UnitMonitorData data = cache.getQueue().take();
		LOG.info(String.format("[%s] - Take object = %s%n", Thread.currentThread().getName(), data));
		cache.removeFromCache(data, false);

	}
}
