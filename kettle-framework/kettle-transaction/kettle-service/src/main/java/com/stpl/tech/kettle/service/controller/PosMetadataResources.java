/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.service.controller;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.itextpdf.html2pdf.HtmlConverter;
import com.stpl.tech.kettle.core.CalculationType;
import com.stpl.tech.kettle.core.CampaignOfferDetail;
import com.stpl.tech.kettle.core.CampaignStrategy;
import com.stpl.tech.kettle.core.CustomerRepeatType;
import com.stpl.tech.kettle.core.FeedbackSource;
import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.core.cache.CampaignCache;
import com.stpl.tech.kettle.core.cache.MetadataCache;
import com.stpl.tech.kettle.core.cache.OrderInfoCache;
import com.stpl.tech.kettle.core.data.budget.vo.BudgetDetail;
import com.stpl.tech.kettle.core.data.budget.vo.BudgetKey;
import com.stpl.tech.kettle.core.data.budget.vo.CalculationStatus;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.core.notification.sms.CustomerSMSNotificationType;
import com.stpl.tech.kettle.core.service.CashManagementService;
import com.stpl.tech.kettle.core.service.CustomerOfferManagementService;
import com.stpl.tech.kettle.core.service.DroolsDecisionService;
import com.stpl.tech.kettle.core.service.ExpenseManagementService;
import com.stpl.tech.kettle.core.service.OrderManagementService;
import com.stpl.tech.kettle.core.service.OrderNotificationService;
import com.stpl.tech.kettle.core.service.OrderSearchService;
import com.stpl.tech.kettle.core.service.PosMetadataService;
import com.stpl.tech.kettle.core.service.PullTransferSettlementReasonService;
import com.stpl.tech.kettle.core.service.SubscriptionManagementService;
import com.stpl.tech.kettle.core.service.TableService;
import com.stpl.tech.kettle.core.service.UnitBudgetService;
import com.stpl.tech.kettle.core.service.UnitInventoryManagementService;
import com.stpl.tech.kettle.core.service.util.FileNameFilter;
import com.stpl.tech.kettle.customer.service.CardService;
import com.stpl.tech.kettle.customer.service.CustomerService;
import com.stpl.tech.kettle.customer.service.LoyaltyTransferService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.converter.DataConverter;
import com.stpl.tech.kettle.data.model.CreditAccountDetail;
import com.stpl.tech.kettle.data.model.CrmAppScreenDetail;
import com.stpl.tech.kettle.data.model.CrmAppScreenDetailDefinition;
import com.stpl.tech.kettle.data.model.CrmScreenResponse;
import com.stpl.tech.kettle.data.model.CrmScreenType;
import com.stpl.tech.kettle.data.model.CustomerCampaignOfferDetail;
import com.stpl.tech.kettle.data.model.CustomerMappingTypes;
import com.stpl.tech.kettle.data.model.PartnerAttributes;
import com.stpl.tech.kettle.data.model.PullDetailReasons;
import com.stpl.tech.kettle.data.model.UnitClosureDetails;
import com.stpl.tech.kettle.data.model.UnitExpenditureAggregateDetail;
import com.stpl.tech.kettle.data.model.UnitExpenditureDetail;
import com.stpl.tech.kettle.data.model.UnitPullDetail;
import com.stpl.tech.kettle.data.model.WalletRecommendationDetail;
import com.stpl.tech.kettle.data.model.WalletSuggestionCustomerInfo;
import com.stpl.tech.kettle.domain.model.ClosureState;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.CustomerDineInView;
import com.stpl.tech.kettle.domain.model.DayWiseOrderConsumptionRequest;
import com.stpl.tech.kettle.domain.model.InventoryInfo;
import com.stpl.tech.kettle.domain.model.InventoryUpdateEvent;
import com.stpl.tech.kettle.domain.model.NextOffer;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderFeedbackMetadata;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.OrderItemConsumable;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.domain.model.ProductInventory;
import com.stpl.tech.kettle.domain.model.SignupOfferStatus;
import com.stpl.tech.kettle.domain.model.UnitClosure;
import com.stpl.tech.kettle.domain.model.UnitMetadata;
import com.stpl.tech.kettle.google.service.GoogleDataPushService;
import com.stpl.tech.kettle.reports.core.ReportOutput;
import com.stpl.tech.kettle.reports.dao.impl.SettlementReportOutputData;
import com.stpl.tech.kettle.reports.process.AuditReportNotification;
import com.stpl.tech.kettle.service.model.DataRequest;
import com.stpl.tech.kettle.service.model.DayCloseRequest;
import com.stpl.tech.kettle.service.model.FileOutputDetails;
import com.stpl.tech.kettle.service.model.PackagingVO;
import com.stpl.tech.kettle.service.model.PnLRequestInputData;
import com.stpl.tech.kettle.service.model.StockoutInventoryUpdate;
import com.stpl.tech.kettle.stock.service.AutomatedStockEventReport;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.master.core.data.vo.NotificationPayload;
import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.exception.WebServiceCallException;
import com.stpl.tech.master.core.external.cache.EnvironmentPropertiesCache;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.RecipeCache;
import com.stpl.tech.master.core.external.cache.TaxDataCache;
import com.stpl.tech.master.core.external.notification.service.NotificationService;
import com.stpl.tech.master.core.external.offer.service.OfferManagementExternalService;
import com.stpl.tech.master.core.notification.sms.SMSWebServiceClient;
import com.stpl.tech.master.core.notification.sms.SolsInfiniWebServiceClient;
import com.stpl.tech.master.data.converter.MasterDataConverter;
import com.stpl.tech.master.data.model.CampaignCouponMapping;
import com.stpl.tech.master.data.model.CouponDetailData;
import com.stpl.tech.master.data.model.DeliveryCouponDetailData;
import com.stpl.tech.master.data.model.UnitDetail;
import com.stpl.tech.master.data.model.UnitPartnerBrandMappingData;
import com.stpl.tech.master.domain.model.Brand;
import com.stpl.tech.master.domain.model.CampaignDetail;
import com.stpl.tech.master.domain.model.CampaignMapping;
import com.stpl.tech.master.domain.model.ChannelPartnerDetail;
import com.stpl.tech.master.domain.model.Consumable;
import com.stpl.tech.master.domain.model.ConsumptionData;
import com.stpl.tech.master.domain.model.CouponCloneResponse;
import com.stpl.tech.master.domain.model.CouponData;
import com.stpl.tech.master.domain.model.DayCloseRequestStatus;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.IdNameList;
import com.stpl.tech.master.domain.model.IdNameValue;
import com.stpl.tech.master.domain.model.IdNameValueMap;
import com.stpl.tech.master.domain.model.MimeType;
import com.stpl.tech.master.domain.model.OfferImageDetail;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductStatus;
import com.stpl.tech.master.domain.model.ScmProductRequestData;
import com.stpl.tech.master.domain.model.TaxProfile;
import com.stpl.tech.master.domain.model.TransactionMetadata;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.domain.model.UnitPartnerBrandKey;
import com.stpl.tech.master.domain.model.scm.UnitWiseDayOfWeekWiseItemEstimate;
import com.stpl.tech.master.domain.model.scm.UnitWiseDayOfWeekWiseItemEstimateRequest;
import com.stpl.tech.spring.model.FileDetail;
import com.stpl.tech.spring.service.FileArchiveService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.JSONSerializer;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.domain.adapter.DateDeserializer2;
import com.stpl.tech.util.endpoint.Endpoints;
import com.stpl.tech.util.notification.model.ResponseData;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.jms.JMSException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.core.MediaType;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.reflect.Type;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.stpl.tech.kettle.service.core.ServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.service.core.ServiceConstants.POS_METADATA_ROOT_CONTEXT;
import static com.stpl.tech.kettle.service.core.ServiceConstants.SEPARATOR;


/**
 * Root resource (exposed at "pos-metadata" path)
 */
@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + POS_METADATA_ROOT_CONTEXT) //    v1/pos-metadata/
public class PosMetadataResources extends AbstractMetadataResource {

    private static final Logger LOG = LoggerFactory.getLogger(PosMetadataResources.class);
    private static final int FILE_LIMIT = 45;
    private static final String ORDERS_MAP_DATA_MUTEX = "ORDERS_MAP_DATA_MUTEX";

    @Autowired
    private PosMetadataService posMetadataService;

    @Autowired
    private PullTransferSettlementReasonService pullTransferSettlementReasonService;

    @Autowired
    private UnitInventoryManagementService unitInventoryService;
    @Autowired
    private OrderManagementService orderDao;

    @Autowired
    private OrderNotificationService orderNotificationService;

    @Autowired
    private OrderSearchService orderSearchService;
    @Autowired
    private MetadataCache metadataCache;
    @Autowired
    private MasterDataCache masterCache;
    @Autowired
    private TaxDataCache taxCache;
    @Autowired
    private EnvironmentProperties props;
    @Autowired
    private SalesReportResource reportResource;
    @Autowired
    private ItemConsumptionHelper helper;
    @Autowired
    private OrderInfoCache ordersCache;
    @Autowired
    private CashManagementService cashService;
    @Autowired
    private UnitBudgetService unitBudgetService;
    @Autowired
    private ExpenseManagementService expenseService;
    @Autowired
    private EnvironmentPropertiesCache propsCache;
    @Autowired
    private MenuItemConsumptionHelper menuItemConsumptionHelper;
    @Autowired
    private TableService tableService;
    @Autowired
    private OfferManagementExternalService offerService;
    @Autowired
    private CardService cardService;
    @Autowired
    private MutexFactory<Integer> mutex;
    @Autowired
    private AutomatedStockEventReport reportService;
    @Autowired
    private CustomerService customerService;
    @Autowired
    private CustomerOfferManagementService customerOfferManagementService;
    @Autowired
    private NotificationService notificationService;
    @Autowired
    private LoyaltyTransferService loyaltyTransferService;
    @Autowired
    private CampaignCache campaignCache;
    @Autowired
    private GoogleDataPushService googleDataPushService;

    @Autowired
    private SubscriptionManagementService subscriptionManagementService;

    @Autowired
    private DroolsDecisionService droolsDecisionService;
    
    @Autowired
    private RecipeCache recipeCache;
    /**
     * Method handling HTTP GET requests. The returned object will be sent to the
     * client as "text/plain" media type.
     *
     * @return String that will be returned as a text/plain response.
     * @throws AuthenticationFailureException
     * @throws DataNotFoundException
     */
    @RequestMapping(method = RequestMethod.POST, value = "metadata", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public TransactionMetadata getMetadata(@RequestBody final Integer unitId)
        throws DataNotFoundException, AuthenticationFailureException {
        LOG.info("Getting Request Metadata from Unit with ID : " + unitId);
        return posMetadataService.getTransactionData();
    }

    /**
     * Method handling HTTP GET requests. The returned object will be sent to the
     * client as "text/plain" media type.
     *
     * @return String that will be returned as a text/plain response.
     * @throws AuthenticationFailureException
     * @throws DataNotFoundException
     */
    @RequestMapping(method = RequestMethod.POST, value = "metadata-by-category", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public TransactionMetadata getMetadata(@RequestBody final String category)
        throws DataNotFoundException, AuthenticationFailureException {
        LOG.info("Getting Request Metadata from Unit with ID : " + category);
        if (category == null) {
            return null;
        }
        return posMetadataService.getTransactionData(UnitCategory.valueOf(category));
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit-metadata", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public UnitMetadata getUnitMetadata(@RequestBody final Integer unitId)
        throws DataNotFoundException, AuthenticationFailureException {
        LOG.info("Getting Unit Metadata for Unit with ID : " + unitId);
        return orderSearchService.getUnitMetadata(unitId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/inventory", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<ProductInventory> getUnitInventory(@RequestBody final Integer unitId)
        throws AuthenticationFailureException, DataNotFoundException {
        LOG.info("Getting Unit Inventory details for ID = " + unitId);
        List<ProductInventory> inventory = unitInventoryService.getUnitInventory(unitId);
        return inventory;
    }


    @RequestMapping(method = RequestMethod.POST, value = "unit/trim/inventory", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<InventoryInfo> getTrimmedDownUnitInventory(@RequestBody final Integer unitId)
        throws AuthenticationFailureException, DataNotFoundException {
        LOG.info("Getting Unit Inventory details for ID = " + unitId);

        if (getMasterDataCache().getUnitBasicDetail(unitId).isLiveInventoryEnabled()) {
            return getLiveInventory(unitId);
        }

        List<ProductInventory> inventory = unitInventoryService.getUnitInventory(unitId);
        List<InventoryInfo> data = new ArrayList<>();
        if (inventory != null && !inventory.isEmpty()) {
            for (ProductInventory info : inventory) {
                if (ProductStatus.ACTIVE.equals(info.getProduct().getStatus())) {
                    data.add(new InventoryInfo(info.getProduct().getDetail().getId(), info.getQuantity(),
                        info.getExpireQuantity()));
                }
            }
        }
        return data;
    }

	public List<InventoryInfo> getTrimmedDownUnitInventory(Integer unitId, List<Integer> productIds)
			throws AuthenticationFailureException, DataNotFoundException {
		LOG.info("Getting Unit Inventory details for ID = " + unitId);

		if (getMasterDataCache().getUnitBasicDetail(unitId).isLiveInventoryEnabled()) {
			Map map = new HashMap();
			map.put("unitId", unitId + "");
			map.put("productIds", productIds);
			return getLiveInventoryForProducts(map);
		}

        return getUnitProductsInventoryForWebV1(unitId, productIds);
	}
    private List<InventoryInfo> getUnitProductsInventoryForWebV1(Integer unitId, List<Integer> productIds) throws DataNotFoundException {
        List<ProductInventory> inventory = unitInventoryService.getUnitInventory(unitId, productIds);
        List<InventoryInfo> data = new ArrayList<>();
        if (inventory != null && !inventory.isEmpty()) {
            for (ProductInventory info : inventory) {
                if (ProductStatus.ACTIVE.equals(info.getProduct().getStatus())) {
                    data.add(new InventoryInfo(info.getProduct().getDetail().getId(), info.getQuantity(),
                            info.getExpireQuantity()));
                }
            }
        }
        return data;
    }
    private List<InventoryInfo> getLiveInventory(Integer unitId) {
        long startTime = System.currentTimeMillis();
        String unitZone =masterCache.getUnitBasicDetail(unitId).getUnitZone();
        String endPoint = props.getInventoryServiceBasePath() + Endpoints.INVENTORY_SERVICE_ENTRY_POINT
                + (Objects.nonNull(unitZone) ? unitZone.toLowerCase() : AppConstants.DEFAULT_UNIT_ZONE) +
                Endpoints.INVENTORY_SERVICE_VERSION + Endpoints.GET_CAFE_INVENTORY_INFO;
        Map<String, String> params = new HashMap<>();
        params.put("unitId", String.valueOf(unitId));
        List<InventoryInfo> data = new ArrayList<>();
        try {
            List<?> list = WebServiceHelper.postRequestWithAuthInternalWithTimeout(endPoint, unitId, List.class, null);
            GsonBuilder gSonBuilder = new GsonBuilder().registerTypeAdapter(Date.class, new DateDeserializer2());
            list.forEach(p -> {
                Gson gson = gSonBuilder.create();
                String str = gson.toJson(p);
                InventoryInfo cat = gson.fromJson(str, InventoryInfo.class);
                if (cat != null) {
                    data.add(cat);
                }
            });
        } catch (Exception e) {
            LOG.error("Error while creating request for inventory for unit Id {}", unitId, e);
        }
        LOG.info("Inventory Data collected from inventory Service in {} miliseconds",
            System.currentTimeMillis() - startTime);
        return data;
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/trim/inventory/stockouts", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<InventoryInfo> getStockedOutUnitInventory(@RequestBody final Integer unitId)
        throws AuthenticationFailureException, DataNotFoundException {
        LOG.info("Getting Stocked out Unit Inventory details for ID = " + unitId);
        List<ProductInventory> inventory = unitInventoryService.getUnitInventory(unitId);
        List<InventoryInfo> data = new ArrayList<>();
        if (inventory != null && !inventory.isEmpty()) {
            for (ProductInventory info : inventory) {
                if (ProductStatus.ACTIVE.equals(info.getProduct().getStatus()) && info.getQuantity() <= 0) {
                    data.add(new InventoryInfo(info.getProduct().getDetail().getId(),
                        info.getProduct().getDetail().getName(), info.getQuantity(), info.getExpireQuantity()));
                }
            }
        }
        return data;
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/inventory/web", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Map<Integer, Integer> getUnitInventoryForWeb(@RequestBody final int unitId)
        throws AuthenticationFailureException, DataNotFoundException {
        LOG.info("Getting Unit Inventory details for unit Id {} for Web Service", unitId);
        return unitInventoryService.getUnitInventoryForWeb(unitId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/inventory/live/web", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Map<Integer, InventoryInfo> getLiveUnitInventoryForWeb(@RequestBody final int unitId)
        throws AuthenticationFailureException, DataNotFoundException {
        LOG.info("Getting Unit Inventory details for unit Id {} for Web Service", unitId);
        Map<Integer, InventoryInfo> inventoryInfoMap = new HashMap<>();
        for (InventoryInfo info : getTrimmedDownUnitInventory(unitId)) {
            inventoryInfoMap.put(info.getId(), info);
        }
        return inventoryInfoMap;
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/inventory/products", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<InventoryInfo> getUnitInventoryForProducts(@RequestBody final Map productIdsMap)
        throws AuthenticationFailureException, DataNotFoundException {
        LOG.info("Getting Unit Inventory details for ID = " + productIdsMap);
        List<Integer> productList = (ArrayList<Integer>) productIdsMap.get("productIds");
        int unitId = Integer.parseInt(productIdsMap.get("unitId").toString());

        if (getMasterDataCache().getUnitBasicDetail(unitId).isLiveInventoryEnabled()) {
            return getLiveInventoryForProducts(productIdsMap);
        }

        List<ProductInventory> inventory = unitInventoryService.getUnitInventoryForProducts(unitId, productList);
        List<InventoryInfo> data = new ArrayList<>();
        if (inventory != null && inventory.size() > 0) {
            for (ProductInventory info : inventory) {
                data.add(new InventoryInfo(info.getProduct().getDetail().getId(), info.getQuantity(),
                    info.getExpireQuantity()));
            }
        }
        return data;
    }

    private List<InventoryInfo> getLiveInventoryForProducts(Map productIdsMap) {
        long startTime = System.currentTimeMillis();
        String unitZone =masterCache.getUnitBasicDetail(Integer.parseInt(productIdsMap.get("unitId").toString())).getUnitZone();
        String endPoint = props.getInventoryServiceBasePath() + Endpoints.INVENTORY_SERVICE_ENTRY_POINT
                + (Objects.nonNull(unitZone) ? unitZone.toLowerCase() : AppConstants.DEFAULT_UNIT_ZONE) +
                Endpoints.INVENTORY_SERVICE_VERSION + Endpoints.GET_CAFE_INVENTORY_INFO_PRODUCTS;
        List<InventoryInfo> data = new ArrayList<>();
        try {
            List<?> list = WebServiceHelper.postRequestWithAuthInternalWithTimeout(endPoint, productIdsMap, List.class,
                null);
            GsonBuilder gSonBuilder = new GsonBuilder().registerTypeAdapter(Date.class, new DateDeserializer2());
            list.forEach(p -> {
                Gson gson = gSonBuilder.create();
                String str = gson.toJson(p);
                InventoryInfo cat = gson.fromJson(str, InventoryInfo.class);
                if (cat != null) {
                    data.add(cat);
                }
            });
        } catch (Exception e) {
            LOG.error("Error while creating request for inventory for unit products", e);
        }
        LOG.info("Inventory Data collected from inventory Service in {} miliseconds",
            System.currentTimeMillis() - startTime);
        return data;
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/inventory/products/web", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Map<Integer, Integer> getUnitInventoryForProductsForWeb(@RequestBody final IdNameList data)
        throws AuthenticationFailureException, DataNotFoundException {
        LOG.info("Getting Product Inventory details for unit Id {} for Web Service", data.getId());
        return unitInventoryService.getUnitInventoryForProductsForWeb(data.getId(), data.getList());
    }

	@RequestMapping(method = RequestMethod.POST, value = "unit/inventory/products/live/web", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public Map<Integer, InventoryInfo> getUnitLiveInventoryForProductsForWeb(@RequestBody final IdNameList data)
			throws AuthenticationFailureException, DataNotFoundException {
		LOG.info("Getting Product Inventory details for unit Id {} for Web Service", data.getId());
		Map<Integer, InventoryInfo> inventoryInfoMap = new HashMap<>();
		for (InventoryInfo info : getTrimmedDownUnitInventory(data.getId(), data.getList())) {
			inventoryInfoMap.put(info.getId(), info);
		}
		return inventoryInfoMap;
	}

    @RequestMapping(method = RequestMethod.POST, value = "unit/inventory/products/live/web/v1", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Map<String, InventoryInfo> getUnitLiveInventoryForProductsForWebV1(@RequestBody final IdNameList data)
            throws AuthenticationFailureException, DataNotFoundException {
        LOG.info("Getting Product Inventory details for unit Id {} for Web Service", data.getId());
        Map<String, InventoryInfo> inventoryInfoMap = new HashMap<>();
        for (InventoryInfo info : getUnitProductsInventoryForWebV1(data.getId(), data.getList())) {
            inventoryInfoMap.put(Integer.toString(info.getId()), info);
        }
        return inventoryInfoMap;
    }


    @RequestMapping(method = RequestMethod.POST, value = "unit/inventory/update", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean updateUnitInventory(@RequestBody final InventoryUpdateEvent updateData)
        throws AuthenticationFailureException {
        return unitInventoryService.updateUnitInventory(updateData, true, false, updateData.getUpdatedBy(), null,
            false);
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/inventory/stockout", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean stockoutUnitInventory(@RequestBody final StockoutInventoryUpdate updateData)
        throws AuthenticationFailureException, DataUpdationException {
        LOG.info("Getting Unit Inventory details for unit ID = " + updateData.getUnitId());
        return unitInventoryService.stockOutInventory(updateData.getUnitId(), updateData.getProductId());
    }

    /**
     * Method handling HTTP GET requests. The returned object will be sent to the
     * client as "text/plain" media type.
     *
     * @return String that will be returned as a text/plain response.
     * @throws WebServiceCallException
     * @throws DataUpdationException
     * @throws DataNotFoundException
     */
    @RequestMapping(method = RequestMethod.POST, value = "unit/close", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Boolean closeDay(@RequestBody final Map map)
        throws DataUpdationException, DataNotFoundException, WebServiceCallException {
        try {
            boolean readyForDayClose = AppUtils.checkForNewDayCloseTimeline(AppUtils.getCurrentTimestamp());
            if (AppUtils.isProd(props.getEnvironmentType()) && !readyForDayClose) {
                throw new DataUpdationException(
                    "Day close cannot be done between 5am and 5pm.");
            }
            String reason = map.get("reason").toString();
            int unitId = (int) map.get("unitId");
            int userId = (int) map.get("userId");
            String pullTransferReason = Objects.nonNull(map.get("pullTransferReason")) ? map.get("pullTransferReason").toString() :null;
            LOG.info("Printing Value pullTransferReason::{}",pullTransferReason);
            LOG.info("Printing Value::{}",map.get("unitPullTransfersList"));
            List<UnitPullDetail> unitPullDetailList =null;
            try {
                if(Objects.nonNull(map.get("unitPullTransfersList"))){
                    Object obj = map.get("unitPullTransfersList");
                    UnitPullDetail[] ans = new Gson().fromJson(new Gson().toJson(obj),UnitPullDetail[].class);
                     unitPullDetailList = Arrays.asList(ans);
                    LOG.info("UnitPullDetail ans::{}",new Gson().toJson(unitPullDetailList));
                }
            }catch (Exception e){
                LOG.info("Printing Exception Unit Pull Detail List PosMedataResources.java",e);
            }

            DayCloseRequest dayClose = new DayCloseRequest();
            dayClose.setReason(reason);
            dayClose.setCloseDate(AppUtils.getBusinessDate());
            // check if orders are pending
            List<OrderInfo> pendingOrders = ordersCache.getOrders(unitId);
            if (pendingOrders != null && pendingOrders.size() > 0) {
                throw new DataUpdationException(
                    "Please Make sure there are NO Pending/Unsettled orders on Assembly Screen.");
            }
            synchronized (mutex.getMutex(unitId)) {
                return dayClose(unitId, userId, dayClose.getCloseDate(), dayClose.getReason(), props,pullTransferReason,unitPullDetailList);
            }
        } catch (Exception e) {
            LOG.error("Error in day close", e);
            throw e;
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-order-consumption-data", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public DayWiseOrderConsumptionRequest getDayWiseOrderConsumption(@RequestBody DayWiseOrderConsumptionRequest dayWiseOrderConsumptionRequest) {
        return getOrderConsumptionsDayWise(dayWiseOrderConsumptionRequest);
    }


    @RequestMapping(method = RequestMethod.GET, value = "unit/pull/reason", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Map<String,List<PullDetailReasons>> unitPullReasons(){

        Map<String,List<PullDetailReasons>> pullReasons= new HashMap<>();
        List<PullDetailReasons> res = posMetadataService.fetchPullDetailReasonMetadata();
        pullReasons.put("UnitPullReasonList",res);
        return pullReasons;

    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/pending-pull-transfer", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Map<String ,List<UnitPullDetail>> unitPendingPullTransfers(@RequestParam  int unitId){
        Map<String,List<UnitPullDetail>> pullTransfers= new HashMap<>();
        List<UnitPullDetail> res = posMetadataService.fetchAllPendingUnitPullDetails(unitId);
        pullTransfers.put("UnitPullTransfersList",res);
        return pullTransfers;
    }


    @RequestMapping(method = RequestMethod.POST, value = "unit-kettleDayClose", consumes = MediaType.APPLICATION_JSON)
    public Boolean closeDayFromSumo(@RequestBody final Integer unitId)
        throws DataUpdationException, DataNotFoundException, WebServiceCallException {
        return dayCloseFromSumo(unitId, AppUtils.getCurrentDate(), props);
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/forceclose", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Boolean forceCloseDay(@RequestBody final Map map)
        throws DataUpdationException, DataNotFoundException, WebServiceCallException {
        try {
            String reason = map.get("reason").toString();
            int unitId = (int) map.get("unitId");
            int userId = (int) map.get("userId");
            DayCloseRequest dayClose = new DayCloseRequest();
            dayClose.setReason(reason);
            dayClose.setCloseDate(AppUtils.getBusinessDate());
            // check if orders are pending
            List<OrderInfo> pendingOrders = ordersCache.getOrders(unitId);
            if (pendingOrders != null && pendingOrders.size() > 0) {
                throw new DataUpdationException(
                    "Please Make sure there are NO Pending/Unsettled orders on Assembly Screen.");
            }
            return dayClose(unitId, userId, dayClose.getCloseDate(), dayClose.getReason(), props,null,null);
        } catch (Exception e) {
            LOG.error("Error in day close", e);
            throw e;
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/dayclose/cancel", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Boolean cancelCloseDay(@RequestBody final Map map)
        throws DataUpdationException, DataNotFoundException, WebServiceCallException {

        try {
            boolean readyForDayClose = AppUtils.checkForNewDayCloseTimeline(AppUtils.getCurrentTimestamp());

            if (AppUtils.isProd(props.getEnvironmentType()) && !readyForDayClose) {
                throw new DataUpdationException(
                    "Day close cannot be done between 5am and 5pm.");
            }
            int unitId = (int) map.get("unitId");

            boolean isAlreadyClosed = getMetadataService().isDayClosed(unitId, AppUtils.getCurrentDate());

            if (isAlreadyClosed) {
                UnitClosure closure = posMetadataService.getUnitsClosure(unitId, AppUtils.getCurrentDate());
                getMetadataService().markDayCloseAsCancelled(closure.getId());
                int pnlDetailId = unitBudgetService.getPnlDetailId(unitId, closure.getId());
                getUnitBudgetService().markAsCancelled(pnlDetailId);
                ConsumptionData data = new ConsumptionData();
                data.setClosureId(closure.getId());
                data.setUnitId(unitId);
                Object obj = callWebService(Object.class, getEnvironmentProperties().getDayCloseURLinSCM(), data);
                if (obj != null) {
                    Type type = new TypeToken<ResponseData<String>>() {
                    }.getType();
                    ResponseData<String> response = JSONSerializer.toJSON(obj, type);
                    if (response != null) {
                        LOG.info("Response " + response.getMessage() + " : " + response.isSuccess());
                    }
                } else {
                    throw new DataNotFoundException();
                }
            } else {
                String msg = "Day Close is not done for unit";
                LOG.info(msg);
                throw new DataUpdationException(msg);
            }
            return true;
        } catch (Exception e) {
            LOG.error("Error in day close", e);
            throw e;
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/process-cash-management", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean generatePulls(@RequestBody final DataRequest request) {
        Date businessDate = request.getBusinessDate();
        int unitId = request.getUnitId();
        Unit unit = getMasterDataCache().getUnit(unitId);
        try {
            List<Order> orders = orderSearchService.getOrderDetailsForDay(businessDate, Arrays.asList(unitId));
            processCashManagement(businessDate, orders, unitId, props,
                new TreeSet<>(getTaxDataCache().getSaleTaxations().get(unit.getLocation().getState().getId())));
        } catch (Exception e) {
            LOG.error("Error Processing Cash Management for unit {}", unitId, e);
            LOG.error("Error Processing Cash Management", e);
            return false;
        }
        return true;
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/regenerate-pnl", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Boolean recalculateDayClosure(@RequestBody final int unitId) throws DataNotFoundException, DataUpdationException, WebServiceCallException {
        return recalculatePnLDayClosure(unitId, true);
    }

//    @Scheduled(cron = "0 0 0 * * *", zone = "GMT+05:30")
    @RequestMapping(method = RequestMethod.POST, value = "unit/regenerate-pnl-all-days", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void recalculatePnLDayClosure() {
        ExecutorService taskExecutor = Executors.newFixedThreadPool(2);
        for (UnitBasicDetail unit : masterCache.getAllUnits()) {
            if (TransactionUtils.isActiveUnit(unit.getStatus()) && unit.isLive()
                    && UnitCategory.CAFE.equals(unit.getCategory())) {
                taskExecutor.execute(() -> {
                    try {

                        LOG.info("Generating PnL For Unit Id {}", unit.getId());
                        recalculatePnLDayClosure(unit.getId(), false);
                    } catch (Exception e) {
                        LOG.error("Exception Caught while recalculating pnl for unit {}", unit.getId(), e);
                    }
                });
            }
        }
        taskExecutor.shutdown();
    }

    @Scheduled(cron = "0 30 5 * * *", zone = "GMT+05:30")
    public void createPreviousDateStockOutEntry() {
        posMetadataService.createPreviousDateStockOutEntry(AppUtils.getPreviousDate());
    }

    @Scheduled(cron = "0 0 6 * * *", zone = "GMT+05:30")
    public void dailyGoogleOfflineConversionsUploadForBrandChaayos() {
        if(!AppUtils.isProd(props.getEnvironmentType())) {
            return;
        }
        Date currentDate = AppUtils.getBusinessDate();
        LOG.info("Starting daily Google Conversions upload job @6:00 a.m");
        Brand brandMetaData = getMasterDataCache().getBrandMetaData().get(AppConstants.CHAAYOS_BRAND_ID);
        if(props.getGooglePushEnabledForStoreSalesConversion()) {
            googleDataPushService.uploadGoogleOfflineConversionDataBulk(currentDate, AppConstants.CHAAYOS_BRAND_ID,
                    brandMetaData.getGoogleAdsCustomerId());
        }
        if(props.getGooglePushEnabledForOfflineConversion()) {
            googleDataPushService.uploadDataForConversionsForLeads(currentDate, AppConstants.CHAAYOS_BRAND_ID,
                    brandMetaData.getGoogleAdsCustomerId());
        }
    }

    @Scheduled(cron = "0 10 6 * * *", zone = "GMT+05:30")
    public void dailyGoogleOfflineConversionsUploadForBrandGNT() {
        if(!AppUtils.isProd(props.getEnvironmentType())) {
            return;
        }
        Date currentDate = AppUtils.getBusinessDate();
        LOG.info("Starting daily Google Conversions upload job @6:10 a.m");
        Brand brandMetaData = getMasterDataCache().getBrandMetaData().get(AppConstants.GNT_BRAND_ID);
//        googleDataPushService.uploadGoogleOfflineConversionDataBulk(currentDate, AppConstants.GNT_BRAND_ID,
//                brandMetaData.getGoogleAdsCustomerId());
    }

    @RequestMapping(value = "upload/google/offline-conversions", method = RequestMethod.GET)
    public boolean triggerGoogleOfflineConversionUploadForDate(@RequestParam("date") String businessDateString,
                                                               @RequestParam("brandId") Integer brandId) {
        Brand brandMetaData = getMasterDataCache().getBrandMetaData().get(brandId);
        if(props.getGooglePushEnabledForStoreSalesConversion()) {
            googleDataPushService.uploadGoogleOfflineConversionDataBulk(
                    AppUtils.getDate(businessDateString, "dd-MM-yyyy"), brandId, brandMetaData.getGoogleAdsCustomerId());
        }
//        if(props.getGooglePushEnabledForOfflineConversion()) {
//            googleDataPushService.uploadDataForConversionsForLeads(AppUtils.getDate(businessDateString, "dd-MM-yyyy"), AppConstants.CHAAYOS_BRAND_ID,
//                    brandMetaData.getGoogleAdsCustomerId());
//        }
        return true;
    }

    @RequestMapping(value = "get/stock-out-data/date-range")
    public void runDailyStockOutReportForDateRange(@RequestParam(name = "startDate") String startDate,@RequestParam(name = "endDate") String endDate){
        for(Date date : AppUtils.getDaysWithoutTimeBetweenDates(AppUtils.getDate(startDate + " 00:00:00.0", "yyyy-MM-dd HH:mm:ss.SSS"),
                AppUtils.getDate(endDate + " 23:59:59.0", "yyyy-MM-dd HH:mm:ss.SSS"), false)){
            LOG.info("Running daily stock out report for Date {}", JSONSerializer.toJSON(date));
            dailyStockOutReport(date);
            LOG.info("Done - Running daily stock out report for Date {}", JSONSerializer.toJSON(date));
        }
    }

    // @Scheduled(cron = "0 10 8 * * *", zone = "GMT+05:30")
    public void runDailyStockOutReport(){
        dailyStockOutReport(null);
    }

    public void dailyStockOutReport(Date date) {
        Date runningForDate = Objects.nonNull(date) ? date : AppUtils.getPreviousDate();
        Map<Integer, List<Date>> unitTimeMap= posMetadataService.getUnitClosingTimeMap(runningForDate);
        if(unitTimeMap==null || unitTimeMap.isEmpty()){
            return;
        }
        List<Date> partnerHardCodedTimeChaayos = new ArrayList<>();
        if(Objects.isNull(date)){
            partnerHardCodedTimeChaayos.add(AppUtils.addMinutesToDate(AppUtils.getPreviousBusinessDate(), 480));
            partnerHardCodedTimeChaayos.add(AppUtils.addMinutesToDate(AppUtils.getPreviousBusinessDate(), 1320));
        }else{
            partnerHardCodedTimeChaayos.add(AppUtils.addMinutesToDate(AppUtils.getDate(date), 480));
            partnerHardCodedTimeChaayos.add(AppUtils.addMinutesToDate(AppUtils.getDate(date), 1320));
        }

        List<Date> partnerHardCodedTimeGnT = new ArrayList<>();
        if(Objects.isNull(date)){
            partnerHardCodedTimeGnT.add(AppUtils.addMinutesToDate(AppUtils.getPreviousBusinessDate(), 660));
            partnerHardCodedTimeGnT.add(AppUtils.addMinutesToDate(AppUtils.getPreviousBusinessDate(), 1439));
        }else{
            partnerHardCodedTimeGnT.add(AppUtils.addMinutesToDate(AppUtils.getDate(date), 660));
            partnerHardCodedTimeGnT.add(AppUtils.addMinutesToDate(AppUtils.getDate(date), 1439));
        }
        for (UnitBasicDetail unit : masterCache.getAllUnits()) {
            try {
                if (TransactionUtils.isActiveUnit(unit.getStatus()) && unit.isLive()
                        && UnitCategory.CAFE.equals(unit.getCategory())
                        && unitTimeMap.get(unit.getId()) != null) {
                    LOG.info("Creating Report For Unit {}",unit.getId());
                    List<Date> unitTime = unitTimeMap.get(unit.getId());
                    reportService.execute(runningForDate, unit.getId(), true, unitTime, false, AppConstants.CHAAYOS_BRAND_ID, null, null);
                    reportService.execute(runningForDate, unit.getId(), true, unitTime, false, AppConstants.GNT_BRAND_ID, null, null);

                    reportService.execute(runningForDate, unit.getId(), true, partnerHardCodedTimeChaayos, true, AppConstants.CHAAYOS_BRAND_ID, null, null);
                    reportService.execute(runningForDate, unit.getId(), true, partnerHardCodedTimeGnT, true, AppConstants.GNT_BRAND_ID, null, null);
                }
            }catch (Exception e){
                LOG.error("Exception Caught While Generating the Report for Unit {}",unit.getId(),e);
            }
        }
    }

    private boolean recalculatePnLDayClosure(int unitId, boolean flag) throws DataNotFoundException, DataUpdationException, WebServiceCallException {
        UnitExpenditureDetail lastSuccessful = unitBudgetService.getLatestUnitExpenditureDetail(unitId,
            AppUtils.getCurrentMonth(), AppUtils.getCurrentYear(), CalculationStatus.COMPLETED,
            CalculationType.MTD);
        int startDay = lastSuccessful == null ? 1 : lastSuccessful.getDay() + 1;
        int month = lastSuccessful == null ? AppUtils.getCurrentMonth() : lastSuccessful.getMonth();
        int year = lastSuccessful == null ? AppUtils.getCurrentYear() : lastSuccessful.getYear();
        Date businessDate = AppUtils.getDate(startDay, month, year);
        /**
         * Check if there are any non-successful MTDS/CURRENT which are not in completed
         * state if Yes then cancel all MTDS and Current Records. From the start Day
         * until last Day of unit Closure Create Daily Revenue Data Update Expense
         * Calculations Update SuMO calculations Calculate MTD Calculations
         *
         */
        Date lastDayCloseDate = orderSearchService.getLastBusinessDate(unitId);
        lastDayCloseDate = flag ? lastDayCloseDate : AppUtils.getPreviousDate(lastDayCloseDate);
        if (businessDate.equals(lastDayCloseDate)) {
            return true;
        }
        if (lastDayCloseDate.equals(businessDate) || lastDayCloseDate.after(businessDate)) {

            while (lastDayCloseDate.compareTo(businessDate) >= 0) {
                LOG.info("Calculating PnL for unit Id {} and business Date {}" , unitId, businessDate);
                UnitClosure closure = posMetadataService.getUnitsClosure(unitId, businessDate);
                if (closure != null) {
                    List<Order> ordersForTheDay = getOrderSearchService().getOrderDetailsForDay(unitId,
                        closure.getStartOrderId(), closure.getLastOrderId());
                    BigDecimal creditCardPercentage = getUnitBudgetService()
                        .getCreditCardPercentageForCurrentMonth(unitId, businessDate);
                    BudgetDetail budget = generatePnLReports(unitId, businessDate, ordersForTheDay,
                        creditCardPercentage, getChannelPartners(businessDate));
                    Unit unit = getMasterDataCache().getUnit(unitId);
                    BudgetKey budgetKey = createBudgetKey(unit, closure.getId(), businessDate);
                    budget.setKey(budgetKey);
                    List<UnitExpenditureDetail> oldPnls = unitBudgetService.getAllUnitExpenditureDetail(unitId,
                        businessDate);
                    if (oldPnls != null && !oldPnls.isEmpty()) {
                        for (UnitExpenditureDetail d : oldPnls) {
                            unitBudgetService.markAsCancelled(d.getDetailId());
                        }
                    }
                    //Added Cancelling flow for aggregate expenditure data
                    List<UnitExpenditureAggregateDetail> oldAggregatesPnls = unitBudgetService.getAllUnitExpenditureAggregateDetail(unitId,
                        businessDate);
                    if (oldAggregatesPnls != null && !oldAggregatesPnls.isEmpty()) {
                        for (UnitExpenditureAggregateDetail d : oldAggregatesPnls) {
                            unitBudgetService.markAggregateAsCancelled(d.getDetailId());
                        }
                    }
                    getUnitBudgetService().savePnL(budget);
                    // Run Other Expense Calculations for Current
                    generatePnLReports(businessDate);
                    updateSuMOPnL(businessDate);
                    generateMTDPnLReports(businessDate);
                }
                businessDate = AppUtils.getNextDate(businessDate);
            }
        }
        return true;
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/generate-mtd-pnl/all", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void generateMTDPnlReport() throws IOException, EmailGenerationException, DataNotFoundException {
        super.generateMTDPnL();
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/generate-mtd-pnl/report", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void generateMTDPnlReports(@RequestBody String businessDate) throws IOException, EmailGenerationException, DataNotFoundException {
        super.generateMTDPnL(AppUtils.parseDate(businessDate));
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/generate-current-post-day-close-reports/all", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void generateReportsPostDayClose() throws IOException, EmailGenerationException, DataNotFoundException {
        super.generateReportsPostDayClose();
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/generate-previous-post-day-close-reports/all", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void generateReportsPostDayClose(@RequestBody String businessDate)
        throws IOException, EmailGenerationException, DataNotFoundException {
        super.generateReportsPostDayClose(AppUtils.getDate(AppUtils.parseDate(businessDate)));
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/add-previous-sales-to-dsr/all", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void addSalesToDSR(@RequestBody String businessDate)
        throws IOException, EmailGenerationException, DataNotFoundException {
        super.writeDataToDSR(AppUtils.getDate(AppUtils.parseDate(businessDate)));
    }

    @RequestMapping(method = RequestMethod.POST, value = "dsr-configuration/add-partner-id", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<PartnerAttributes> addDsrConfiguration(@RequestBody List<PartnerAttributes> input) throws DataNotFoundException, DataUpdationException, WebServiceCallException {
        //DSRConfiguration
        for (PartnerAttributes partner : input) {
            posMetadataService.addPartnerAttributes(partner);
        }
        return new ArrayList<>();
    }

    @RequestMapping(method = RequestMethod.POST, value = "dsr-configuration/update", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<PartnerAttributes> updatePartner(@RequestBody List<PartnerAttributes> request)
        throws DataUpdationException, DataNotFoundException {
        LOG.info("Updating DSR configuration with partnerid " + request.get(0).getPartnerId());
        for (PartnerAttributes partner : request) {
            posMetadataService.updatePartnerAttributes(partner);
        }
        return request;
    }

    @RequestMapping(method = RequestMethod.GET, value = "dsr-configuration/get-partnerid", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<UnitDetail> getDsrConfigurationpartnerId() throws DataNotFoundException {
        return posMetadataService.getDSRConfigpartnerId();
    }

    @RequestMapping(method = RequestMethod.GET, value = "dsr-configuration/get-config", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<PartnerAttributes> getDsrConfiguration(@RequestParam Integer partnerId) throws DataNotFoundException {
        return posMetadataService.getDSRConfig(partnerId);
    }

    //@Scheduled(cron = "0 10 6 * * *", zone = "GMT+05:30")
    public void reGeneratePnlScheduler() throws DataNotFoundException, WebServiceCallException, DataUpdationException {
        try {
            regeneratePnLForAllUnits(new PnLRequestInputData(
                    AppUtils.getSQLFormattedDate(AppUtils.getPreviousBusinessDate()),
                    AppUtils.getSQLFormattedDate(AppUtils.getPreviousBusinessDate())));
        } catch (Exception e){
            LOG.error("Exception Faced While ReGenerating the PnL for {} ::::",
                    AppUtils.getPreviousBusinessDate(),e);
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/regenerate-pnl/all", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Boolean regeneratePnLForAllUnits(@RequestBody PnLRequestInputData input) throws DataNotFoundException, DataUpdationException, WebServiceCallException {
        Date minimumDate = input.getStartDate() == null
            ? AppUtils.getStartOfMonth(AppUtils.getCurrentYear(), AppUtils.getCurrentMonth())
            : AppUtils.getDate(AppUtils.parseDate(input.getStartDate()));
        Date maximumDate = input.getEndDate() == null ? AppUtils.getPreviousDate()
            : AppUtils.getDate(AppUtils.parseDate(input.getEndDate()));
        while (maximumDate.equals(minimumDate) || maximumDate.after(minimumDate)) {
            LOG.info("Generating PNL data for business Date : {}", minimumDate);
            Map<Integer, BudgetDetail> budgets = getUnitBudgetMap(minimumDate, getChannelPartners(minimumDate), null);
            unitBudgetService.cancelAllPnLs(minimumDate, budgets.keySet());
            getUnitBudgetService().savePnL(budgets.values());
            // Run Other Expense Calculations for Current
            generatePnLReports(minimumDate);
            updateSuMOPnL(minimumDate);
            generateMTDPnLReports(minimumDate);
            minimumDate = AppUtils.getNextDate(minimumDate);
        }
        return true;
    }


    @RequestMapping(method = RequestMethod.POST, value = "unit/regenerate-pnl/unit", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Boolean regeneratePnLForSingleUnits(@RequestBody PnLRequestInputData input) throws DataNotFoundException, DataUpdationException, WebServiceCallException {
        Date minimumDate = input.getStartDate() == null
            ? AppUtils.getStartOfMonth(AppUtils.getCurrentYear(), AppUtils.getCurrentMonth())
            : AppUtils.getDate(AppUtils.parseDate(input.getStartDate()));
        Date maximumDate = input.getEndDate() == null ? AppUtils.getPreviousDate()
            : AppUtils.getDate(AppUtils.parseDate(input.getEndDate()));
        Integer targetUnitId = input.getUnitId();
        if (targetUnitId == null) {
            LOG.info("Unit not provided");
            return false;
        }
        while (maximumDate.equals(minimumDate) || maximumDate.after(minimumDate)) {
            LOG.info("Generating PNL data for business Date : {} and UnitId : {}", minimumDate, targetUnitId);
            Map<Integer, BudgetDetail> budgets = getUnitBudgetMap(minimumDate, getChannelPartners(minimumDate), targetUnitId);
            unitBudgetService.cancelAllPnLs(minimumDate, budgets.keySet());
            getUnitBudgetService().savePnL(budgets.values());
            // Run Other Expense Calculations for Current
            generatePnLReports(minimumDate);
            updateSuMOPnL(minimumDate);
            generateMTDPnLReports(minimumDate);
            minimumDate = AppUtils.getNextDate(minimumDate);
        }
        return true;
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-screen-url", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CrmScreenResponse getCrmScreenUrl(@RequestParam String data) throws DataNotFoundException {
        return posMetadataService.getCrmScreenUrl(data);

    }

    @RequestMapping(method = RequestMethod.GET, value = "get-unit-screen-url", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Map<String,Map<String,String>> getCrmScreenUrlForUnit(@RequestParam Integer unitId) throws DataNotFoundException {
        return posMetadataService.getCrmScreenUrlForUnit(unitId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-unit-screen-url-v1", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Map<String,Map<String,String>> getCrmScreenUrlForUnitV1(@RequestParam Integer unitId) throws DataNotFoundException {
        return posMetadataService.getCrmScreenUrlForUnitV1(unitId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-unit-screen-url-v2", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Map<String,Map<String,List<String>>> getCrmScreenUrlForUnitV2(@RequestParam Integer unitId) throws DataNotFoundException {
        return posMetadataService.getCrmScreenUrlForUnitV2(unitId);
    }

    private Map<Integer, BudgetDetail> getUnitBudgetMap(Date businessDate,
                                                        Map<Integer, ChannelPartnerDetail> partners, Integer targetUnitId) {

        Map<Integer, BudgetDetail> map = new HashMap<>();
        List<UnitClosureDetails> unitClosureDetails;
        if(targetUnitId == null){
            unitClosureDetails = posMetadataService.getClosureForBusinessDate(businessDate);
        }
        else{
            unitClosureDetails = posMetadataService.getClosureForBusinessDate(businessDate, Collections.singleton(targetUnitId));
        }

        List<UnitClosureDetails> singularList = new ArrayList<>();
		List<Integer> unitIds = unitClosureDetails.stream().map(UnitClosureDetails::getUnitId)
				.collect(Collectors.toList());
        if (targetUnitId != null) {
            for (UnitClosureDetails u : unitClosureDetails) {
                if (u.getUnitId() == targetUnitId) {
                    singularList.add(u);
                }
            }
            unitClosureDetails = singularList;
        }

        List<Order> ordersForTheDay = orderSearchService.getOrderDetailsForDay(businessDate, unitIds);
		Map<Integer, List<Order>> ordersForTheDayMap = new HashMap<>();
		ordersForTheDay.forEach(p -> {
			if (!ordersForTheDayMap.containsKey(p.getUnitId())) {
				synchronized (ORDERS_MAP_DATA_MUTEX) {
					if (!ordersForTheDayMap.containsKey(p.getUnitId())) {
						ordersForTheDayMap.put(p.getUnitId(), new ArrayList<>());
					}
				}
			}
			ordersForTheDayMap.get(p.getUnitId()).add(p);
		});

		for(UnitClosureDetails unitClosure : unitClosureDetails) {
            int unitId = unitClosure.getUnitId();
            BudgetDetail detail = generatePnLReports(unitId, businessDate,
                    ordersForTheDayMap.containsKey(unitId) ? ordersForTheDayMap.get(unitId) : new ArrayList<Order>(),
                    unitBudgetService.getCreditCardPercentageForCurrentMonth(unitId, businessDate), partners);
            Unit unit = getMasterDataCache().getUnit(unitId);
            BudgetKey budgetKey = createBudgetKey(unit, unitClosure.getClosureId(), businessDate);
            detail.setKey(budgetKey);
            map.put(unitId, detail);
        }

//        unitClosureDetails.parallelStream().forEach(unitClosure ->{
//            int unitId = unitClosure.getUnitId();
//            BudgetDetail detail = generatePnLReports(unitId, businessDate,
//                    ordersForTheDayMap.containsKey(unitId) ? ordersForTheDayMap.get(unitId) : new ArrayList<Order>(),
//                    unitBudgetService.getCreditCardPercentageForCurrentMonth(unitId, businessDate), partners);
//            Unit unit = getMasterDataCache().getUnit(unitId);
//            BudgetKey budgetKey = createBudgetKey(unit, unitClosure.getClosureId(), businessDate);
//            detail.setKey(budgetKey);
//            map.put(unitId, detail);
//        });
        return map;
    }

    /**
     * @param businessDate
     */
    private void updateSuMOPnL(Date businessDate) throws WebServiceCallException {
        callWebService(Boolean.class, getSCMURL(Endpoints.SCM_PNL_URL),
            new SimpleDateFormat("yyyy-MM-dd").format(businessDate));
    }

    private void updateSuMoFinalizedPnL(Date businessDate) {
        try {
            WebServiceHelper.postRequestWithAuthInternalNoTimeout(
                getSCMURL(Endpoints.SCM_PNL_URL_FINALIZED),
                new SimpleDateFormat("yyyy-MM-dd").format(businessDate), Boolean.class,
                propsCache.getKettleClientToken());
        } catch (IOException e) {
            LOG.error("Update sumo for finalised PnL Failed with error", e);
        }
    }

    private String getSCMURL(String url) {
        return getEnvironmentProperties().getSCMServiceBasePath() + AppConstants.FORWARD_SLASH + url;
    }

    /**
     * Method handling HTTP GET requests. The returned object will be sent to the
     * client as "text/plain" media type.
     *
     * @return String that will be returned as a text/plain response.
     * @throws AuthenticationFailureException
     * @throws DataUpdationException
     * @throws DataNotFoundException
     */
    @RequestMapping(method = RequestMethod.POST, value = "audit/unit", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Boolean auditUnit(@RequestBody final int unitId)
        throws AuthenticationFailureException, DataUpdationException, DataNotFoundException {
        try {
            return auditReport(unitId, AppUtils.getCurrentBusinessDate(), props);
        } catch (Exception e) {
            LOG.error("Error in day close", e);
            throw e;
        }
    }

    public boolean auditReport(int unitId, Date businessDate, EnvironmentProperties props)
        throws DataUpdationException, DataNotFoundException {
        int startOrderId = getOrderSearchService().getLastDayCloseOrderId(unitId);
        int lastOrderId = getOrderSearchService().getLastOrderDetail(unitId);
        List<Order> ordersForTheDay = getOrderSearchService().getOrderDetailsForDay(unitId, startOrderId, lastOrderId);
        SettlementReportOutputData output = generateReports(unitId, businessDate, ordersForTheDay, false, false, null,
            null);
        if (output.isReportGenerated()) {
            try {
                emailReport(output, new AuditReportNotification(output));
                return true;
            } catch (Exception e) {
                LOG.error("Error sending email for the report for unit " + unitId, e);
                return false;
            }
        }
        return false;
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/generate/external")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Boolean generateExternalReports(@RequestParam final int unitId, @RequestParam final String businessDate)
        throws AuthenticationFailureException, DataUpdationException, DataNotFoundException {

        Date date = businessDate != null ? AppUtils.getDate(AppUtils.parseDateIST(businessDate))
            : AppUtils.getCurrentBusinessDate();
        LOG.info("Regenerating external sales report for unit {} and businessDate {}", unitId, date);
        try {
            UnitClosure closure = posMetadataService.getUnitsClosure(unitId, date);
            if (closure != null) {
                if (processExternalReports(closure) == null) {
                    throw new DataNotFoundException("Partner Attributes not found");
                }
            } else {
                LOG.error("Day Close not done for unit");
                throw new DataNotFoundException("Day close not done for unit");
//                return false;
            }
            return true;
        } catch (Exception e) {
            LOG.error("Regenerating external sales report for unit {} and businessDate {}", unitId, date, e);
            throw e;
        }

    }

    @RequestMapping(method = RequestMethod.POST, value = "kettle/consumption")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Boolean generateConsumptionReport(@RequestParam final String businessDate) throws Exception {

        Date date = businessDate != null ? AppUtils.getDate(AppUtils.parseDateIST(businessDate))
            : AppUtils.getCurrentBusinessDate();
        LOG.info("Regenerating Consumption report for businessDate {}", date);
        try {
            generateConsumptionReport(date);
            return true;
        } catch (Exception e) {
            LOG.error("Regenerating external sales report for unit {} and businessDate {}", date, e);
            throw e;
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = "scm/consumption")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean calculateSCMConsumption(@RequestParam final int unitId, @RequestParam final String businessDate)
        throws Exception {

        Date date = businessDate != null ? AppUtils.getDate(AppUtils.parseDateIST(businessDate))
            : AppUtils.getCurrentBusinessDate();
        LOG.info("Recalculating item Consumption for unit {} and businessDate {}", unitId, date);
        try {
            UnitClosure closure = posMetadataService.getUnitsClosure(unitId, date);
            if (closure != null) {
                processSCMProductConsumption(closure, false);
            } else {
                LOG.error("Day Close not done for unit");
                return false;
            }
            return true;
        } catch (Exception e) {
            LOG.error("Error while Recalculating Consumption", e);
            throw e;
        }

    }

    @RequestMapping(method = RequestMethod.POST, value = "scm/consumption/for-a-date")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean calculateSCMConsumptions(@RequestParam final String businessDate) throws Exception {

        Date date = businessDate != null ? AppUtils.getDate(AppUtils.parseDateIST(businessDate))
            : AppUtils.getCurrentBusinessDate();
        List<UnitClosureDetails> closures = posMetadataService.getClosureForBusinessDate(date);
        for (UnitClosureDetails closure : closures) {
            LOG.info("Recalculating scm and menu Consumption for unit {} and businessDate {}", closure.getUnitId(),
                date);
            try {
                processSCMProductConsumption(DataConverter.convert(closure), true);
            } catch (Exception e) {
                LOG.error("Error while Recalculating Consumption", e);
            }
        }
        return true;
    }


    @RequestMapping(method = RequestMethod.POST, value = "unit/report/manager", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<FileOutputDetails> allReports(@RequestBody final Integer unitId)
        throws AuthenticationFailureException, DataUpdationException, DataNotFoundException {
        LOG.info("Getting All Reports for unit with productId " + unitId);
        String dirName = props.getBasePath() + "/" + unitId + "/reports";
        File[] files = FileNameFilter.listFile(dirName, "xlsx");
        Arrays.sort(files, new Comparator<File>() {
            public int compare(File f1, File f2) {
                return Long.compare(f2.lastModified(), f1.lastModified());
            }
        });
        List<FileOutputDetails> output = new ArrayList<FileOutputDetails>();
        int count = 0;
        for (File file : files) {
            if (count >= FILE_LIMIT) {
                break;
            }
            output.add(new FileOutputDetails(file.getName(), new Date(file.lastModified())));
            count++;
        }
        return output;
    }

    @RequestMapping(value = "unit/report/manager/download", method = RequestMethod.GET)
    public void doDownload(@RequestParam("sessionKey") String sessionKey, @RequestParam("userId") int userId,
                           @RequestParam("unitId") int unitId, @RequestParam("terminalId") Integer terminalId,
                           @RequestParam("fileName") String fileName, HttpServletResponse response)
        throws IOException, AuthenticationFailureException {

        LOG.info("Downloading report for unit with productId " + unitId + " and file name " + fileName);
        // validateSession(unitId, terminalId, userId, sessionKey);
        String dirName = props.getBasePath() + "/" + unitId + "/reports";
        String fullPath = dirName + "/" + fileName;
        File downloadFile = new File(fullPath);
        FileInputStream inputStream = new FileInputStream(downloadFile);
        response.setContentType(AppConstants.EXCEL_MIME_TYPE);
        response.setContentLength((int) downloadFile.length());

        // set headers for the response
        String headerKey = "Content-Disposition";
        String headerValue = String.format("attachment; filename=\"%s\"", downloadFile.getName());
        response.setHeader(headerKey, headerValue);

        // get output stream of the response
        OutputStream outStream = response.getOutputStream();

        byte[] buffer = new byte[4096];
        int bytesRead = -1;

        // write bytes read from the input stream into the output stream
        while ((bytesRead = inputStream.read(buffer)) != -1) {
            outStream.write(buffer, 0, bytesRead);
        }

        inputStream.close();
        outStream.close();

    }

    @RequestMapping(method = RequestMethod.GET, value = "unit/taxprofile", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Collection<TaxProfile> getUnitTaxProfile(@RequestParam("unitId") final int unitId)
        throws DataNotFoundException {
        LOG.info("Getting Tax Profile for Units with Id " + unitId);
        return masterCache.getTaxProfile(unitId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "unit/packaging-profile", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public PackagingVO getUnitPackagingProfile(@RequestParam("unitId") final int unitId)
        throws DataNotFoundException {
        LOG.info("Getting Packaging Profile for Units with Id " + unitId);
        return new PackagingVO(masterCache.getUnit(unitId).getPackagingType(), masterCache.getUnit(unitId).getPackagingValue());
    }

    @RequestMapping(method = RequestMethod.GET, value = "brand/partner/packaging-profile", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public PackagingVO getRegionProductProfile(@RequestParam("brandId") final Integer brandId,
                                               @RequestParam("unitId") final int unitId,
                                               @RequestParam("partnerId") final Integer partnerId)
        throws DataNotFoundException {
        LOG.info("Getting Packaging Profile for Units Id {} partnerId {} brandId {}", unitId, partnerId, brandId);
        UnitPartnerBrandKey unitPartner = new UnitPartnerBrandKey(unitId, brandId, partnerId);
        UnitPartnerBrandMappingData unitPartnerBrandMappingData = masterCache.getUnitPartnerBrandMappingMetaData().get(unitPartner);
        Integer priceProfileUnitId = unitPartnerBrandMappingData.getPriceProfileUnitId();
        Unit pricingUnit = masterCache.getUnit(priceProfileUnitId);
        return new PackagingVO(pricingUnit.getPackagingType(), pricingUnit.getPackagingValue());
    }

    @RequestMapping(method = RequestMethod.GET, value = "unit/delivery-partners", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Collection<IdCodeName> getUnitDeliveryPartners(@RequestParam("unitId") final int unitId)
        throws DataNotFoundException {
        LOG.info("Getting delivery partners for unit");
        return unitInventoryService.getDeliveryPartners(unitId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "creditAccount/add", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CreditAccountDetail createCreditAccount(@RequestBody final CreditAccountDetail request)
        throws DataUpdationException, DataNotFoundException {
        LOG.info("Adding credit account with name " + request.getLegalName());
        CreditAccountDetail data = posMetadataService.addCreditAccount(request);
        metadataCache.refreshCreditAccounts();
        return data;
    }

    @RequestMapping(method = RequestMethod.POST, value = "creditAccount/update", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CreditAccountDetail updateCreditAccount(@RequestBody final CreditAccountDetail request)
        throws DataUpdationException, DataNotFoundException {
        LOG.info("Updating credit account with name " + request.getLegalName());
        CreditAccountDetail data = posMetadataService.updateCreditAccount(request);
        metadataCache.refreshCreditAccounts();
        return data;
    }

    @RequestMapping(method = RequestMethod.POST, value = "creditAccount/all", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<CreditAccountDetail> getAllCreditAccount() throws DataUpdationException, DataNotFoundException {
        return posMetadataService.getAllCreditAccounts(null);
    }

    @RequestMapping(method = RequestMethod.POST, value = "creditAccount/names", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<IdCodeName> getALlCreditAccountNames() throws DataUpdationException, DataNotFoundException {
        return metadataCache.getCreditAccounts();
    }

    @Override
    public PosMetadataService getMetadataService() {
        return posMetadataService;
    }

    @Override
    public MetadataCache getMetadataCache() {
        return metadataCache;
    }

    @Override
    public MasterDataCache getMasterDataCache() {
        return masterCache;
    }

    @Override
    public OrderManagementService getOrderManagementService() {
        return orderDao;
    }

    @Override
    public EnvironmentProperties getEnvironmentProperties() {
        return props;
    }

    public List<ReportOutput> processExternalReports(int unitId, List<Order> orders, Date businessDate) {
        return reportResource.generateTerminalSalesReport(getMasterDataCache().getUnit(unitId), orders, businessDate);
    }

    @RequestMapping(method = RequestMethod.GET, value = "delivery-partners", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Collection<IdCodeName> allDeliveryPartners() throws AuthenticationFailureException, DataNotFoundException {
        LOG.info("Getting All Delivery Partners ");
        return metadataCache.getAllDeliveryPartners();
    }

    @Scheduled(cron = "0 30 6 1,2,3,4,5 * *", zone = "GMT+05:30")
    public void generateFinalizedEntry() {
        generateFinalizedEntry(AppUtils.getCurrentTimestamp());
    }

    @Scheduled(cron = "0 30 6 6,7,8,9 * *", zone = "GMT+05:30")
    public void generateClosedPnlEntry() {
        generateClosedPnlEntry(AppUtils.getCurrentTimestamp());
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit/regenerate-pnl/finalized", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean generateFinalizedPnLData(@RequestBody String date, @RequestParam(required = false) Integer unitId) {
        generateFinalizedEntry(AppUtils.getDate(date + " 00:00:00.0", "yyyy-MM-dd HH:mm:ss.SSS"));
        return true;
    }


    @RequestMapping(method = RequestMethod.POST, value = "unit/regenerate-pnl/closed", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean generateClosedPnLData(@RequestBody String date) {
        generateClosedPnlEntry(AppUtils.getDate(date + " 00:00:00.0", "yyyy-MM-dd HH:mm:ss.SSS"));
        return true;
    }

    @RequestMapping(method = RequestMethod.POST, value = "upt-calculation", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean generateUPTCalc(@RequestParam String date, @RequestParam Integer unitId) {
        LOG.info("Creating Day Close Call for :" + unitId);
        posMetadataService.updateSuggestiveConsumptionEstimates(AppUtils.getDateWithoutTime(AppUtils.getDate(date, "yyyy-MM-dd")), unitId);
        return true;
    }

    @RequestMapping(method = RequestMethod.GET, value = "set-upts", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean calculateUpt(@RequestParam String date) {
        List<UnitClosureDetails> closureDetailsList = posMetadataService.getClosureFromBusinessDate(AppUtils.getDateWithoutTime(AppUtils.getDate(date, "yyyy-MM-dd")));
        if (closureDetailsList != null && closureDetailsList.size() > 0) {
            for (UnitClosureDetails unitClosureDetails : closureDetailsList) {
                LOG.info("Calculating UPT for unit :" + unitClosureDetails.getUnitId() + " " + "for date:" + unitClosureDetails.getBusinessDate());
                posMetadataService.updateUptsForPreviousDate(unitClosureDetails.getBusinessDate(), unitClosureDetails.getUnitId());
            }
        }
        return true;
    }

    @RequestMapping(method = RequestMethod.POST, value = "set-crm-app-banner-detail", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean setCrmAppScreenDetail(@RequestBody CrmAppScreenDetailDefinition detail) {
        LOG.info("Into CRM App Banner Detail");
        return posMetadataService.setCrmAppScreenDetail(detail);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-crm-screen-type", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Object[] getCrmScreenType() {
        return CrmScreenType.values();
    }

    @RequestMapping(method = RequestMethod.POST, value = "upload-crm-screen-image", produces = MediaType.APPLICATION_JSON,
        consumes = MediaType.MULTIPART_FORM_DATA)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public OfferImageDetail uploadOfferImage(HttpServletRequest request,
                                             @RequestParam(value = "mimeType") MimeType mimeType,
                                             @RequestParam(value = "couponCode") String couponCode,
                                             @RequestParam(value = "imageType") String imageType,
                                             @RequestParam(value = "file") final MultipartFile file) {
         FileDetail s3File = posMetadataService.saveOfferImage(mimeType, couponCode, imageType, file, props.getS3CrmBucket(), props.getCrmHostUrl());
         return !Objects.isNull(s3File)?new OfferImageDetail(s3File.getName(), props.getCrmHostUrl() + s3File.getName()):null;

    }

    private void generateFinalizedEntry(Date date) {
        // TODO Create Final entry here
        // get all unit Ids with Monthly day close done - sumo
        Date businessDate = date != null ? AppUtils.getBusinessDate(date)
            : AppUtils.getBusinessDate(AppUtils.getCurrentTimestamp());
        List<Integer> unitsWithMonthlyDone = getUnitsWithMonthlyDone(businessDate);
        // get units with FINALIZED entry
        List<Integer> unitsWithFinalizedEntries = getUnitsWithFinalizedEntries(businessDate);

        LOG.info("Units with Finalized entries {}", unitsWithFinalizedEntries.size());

        Set<Integer> targetUnits = new HashSet<>();

        if (unitsWithMonthlyDone != null) {
            for (Integer i : unitsWithMonthlyDone) {
                if (!unitsWithFinalizedEntries.contains(i)) {
                    targetUnits.add(i);
                }
            }
        }

        // generate finalized entry for the units not having finalized entry
        if (!targetUnits.isEmpty()) {

            // To create finalized entry copy data from last day of the month
            Date lastDayoftheMonth = AppUtils.getLastDayOfPreviousMonth(businessDate);
            for (Integer i : targetUnits) {
                unitBudgetService.createFinalizedEntry(i, lastDayoftheMonth);
                // get SUMO data for monthly
                updateSuMoFinalizedPnL(lastDayoftheMonth);
                // update entry and mark as Finalized.
                LOG.info("Finalized entry generated for Unit {} and businessDate {}", i, businessDate);
            }

        }
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-pnl-status-map", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<IdCodeName> getPnLMap(@RequestParam Integer unitId) throws DataNotFoundException {
        LOG.info("Getting Requested Map for Map ");
        return posMetadataService.getPnLMap(unitId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "reset-meter-reading", produces = MediaType.APPLICATION_JSON)
    public boolean resetMeterReading(@RequestParam Integer unitId) {
        LOG.info("Getting Requested Map for Map ");
        return posMetadataService.resetMeterReading(unitId);
    }

    private void generateClosedPnlEntry(Date date) {
        // get all unit Ids with FINALISED entries
        Date businessDate = date != null ? AppUtils.getBusinessDate(date)
            : AppUtils.getBusinessDate(AppUtils.getCurrentTimestamp());
        List<Integer> unitsWithFinalizedEntries = getUnitsWithFinalizedEntries(businessDate);
        // get units with CLOSED entry
        List<Integer> unitsWithClosedEntries = unitBudgetService.getUnitsWithClosedEntries(businessDate);

        LOG.info("Units with CLosed entries {}", unitsWithClosedEntries.size());

        Set<Integer> targetUnits = new HashSet<>();

        if (unitsWithFinalizedEntries != null) {
            for (Integer i : unitsWithFinalizedEntries) {
                if (!unitsWithClosedEntries.contains(i)) {
                    targetUnits.add(i);
                }
            }
        }
        // generate closed entry for the units not having closed entry
        if (!targetUnits.isEmpty()) {
            // To create closed entry copy data from last day of the month
            Date lastDayoftheMonth = AppUtils.getLastDayOfPreviousMonth(businessDate);
            for (Integer i : targetUnits) {
                unitBudgetService.createClosedPnlEntry(i, lastDayoftheMonth);
                // get SUMO data for monthly
//                updateSuMoFinalizedPnL(lastDayoftheMonth);
                // update entry and mark as Finalized.
                LOG.info("Closed entry generated for Unit {} and businessDate {}", i, businessDate);
            }
        }
    }

    private List<Integer> getUnitsWithFinalizedEntries(Date businessDate) {
        return unitBudgetService.getUnitsWithFinalizedEntries(businessDate);
    }

    private List<Integer> getUnitsWithMonthlyDone(Date businessDate) {
        List<Integer> result = new ArrayList<>();
        try {
            List<?> l = WebServiceHelper.postRequestWithAuthInternalNoTimeout(
                getSCMURL(Endpoints.SCM_PNL_URL_MONTHLY_DAY_CLOSE),
                new SimpleDateFormat("yyyy-MM-dd").format(businessDate), List.class,
                propsCache.getKettleClientToken());
            if (l != null && !l.isEmpty()) {
                for (Object o : l) {
                    Double d = Double.parseDouble(String.valueOf(o));
                    result.add(d.intValue());
                }
            }
        } catch (Exception e) {
            LOG.error("Error", e);
        }
        return result;
    }

    @Override
    public ItemConsumptionHelper getItemConsumptionHelper() {
        return helper;
    }

    @Override
    public CashManagementService getCashManagementService() {
        return cashService;
    }

    @Override
    public PullTransferSettlementReasonService getPullTransferSettlementReasonService(){
        return pullTransferSettlementReasonService;
    }
    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.kettle.service.controller.AbstractMetadataResource#
     * getOrderSearchService()
     */
    @Override
    public OrderSearchService getOrderSearchService() {
        return orderSearchService;
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.kettle.service.controller.AbstractMetadataResource#
     * getTaxDataCache()
     */
    @Override
    public TaxDataCache getTaxDataCache() {
        return taxCache;
    }

    public UnitBudgetService getUnitBudgetService() {
        return unitBudgetService;
    }

    /*
     * (non-Javadoc)
     *
     * @see com.stpl.tech.kettle.service.controller.AbstractMetadataResource#
     * getExpenseService()
     */
    @Override
    public ExpenseManagementService getExpenseService() {
        return expenseService;
    }

    @Override
    public MenuItemConsumptionHelper getMenuItemConsumptionHelper() {
        return menuItemConsumptionHelper;
    }

    @Override
    public TableService getTableService() {
        return tableService;
    }

    @Override
    public OfferManagementExternalService getOfferManagementExternalService() {
        return offerService;
    }

    @Override
    public CardService getCardService() {
        return cardService;
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-crm-app-banner-detail", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<CrmAppScreenDetail> getCrmAppScreenDetail(@RequestParam String region) {
        LOG.info("Getting CRM App Banner Detail");
        return posMetadataService.getCrmAppScreenDetail(region);
    }

    @RequestMapping(method = RequestMethod.POST, value = "clone-crm-app-banner-detail", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean cloneCrmAppScreenDetail(@RequestBody IdCodeName detail) {
        LOG.info("Cloning CRM App Banner Detail for {}", detail.getId());
        try {
            return posMetadataService.cloneCrmAppScreenDetail(detail);
        } catch (Exception e) {
            LOG.info("Exception Caught while Cloning CrmAppScreenDetail ::: ", e);
            return false;
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = "send-inventory-down-notification", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean sendSlackNotificationForInventoryDown(@RequestBody IdCodeName request) {
        LOG.info("Sending inventory Down and Order punch notification for unit {} and customer {}",request.getId(),request.getCode());
        try {
            return posMetadataService.sendSlackNotificationForInventoryDown(request);
        } catch (Exception e) {
            LOG.info("Exception error while sending slack notification for inventory down ::: {} ", e);
            return false;
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = "create-previous-day-stock-out-entry", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void createPreviousDayStockOutEntry(@RequestParam String date) {
        LOG.info("Create Previous Day Stock Out/In Entry For Date {} ",date);
        try {
             posMetadataService.createPreviousDateStockOutEntry(AppUtils.getDate(date,"yyyy-MM-dd HH:mm:ss.SSS"));
        } catch (Exception e) {
            LOG.info("Exception error while Creating Stock Out/in Entry", e);
        }
    }


    @RequestMapping(method = RequestMethod.GET, value = "send-next-best-offer-notification", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void sendNextBestOfferNotificationInfo() throws DataUpdationException, IOException, DataNotFoundException, JMSException {
        sendNextBestOfferNotification();
    }

    @RequestMapping(method = RequestMethod.GET, value = "expire-loyalty-gifting", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void expireLoyaltyGifting() throws DataUpdationException, IOException {
        loyaltyTransferService.markExpired();
    }

	@Scheduled(cron = "0 30 15 * * *", zone = "GMT+05:30")
	public void sendNextBestOfferNotification() throws IOException, DataUpdationException, DataNotFoundException, JMSException {
		LOG.info("Running Job to Send Next Best Offer to Customers");
        // sendReminderForOffer(AppConstants.CHAAYOS_BRAND_ID);
        createOfferForNextJourney();
	}

	private void sendReminder(int nThDay, Integer brandId, Date couponStartDate) {
		Brand brand = masterCache.getBrandMetaData().get(brandId);
		LOG.info("Running Reminder process for {}, {}, {}", nThDay, brand, couponStartDate);
		Map<String, List<CustomerCampaignOfferDetail>> nextOffersMap = customerService
				.getNotUsedNextBestOfferDetails(brandId, couponStartDate);
		for (String cloneCouponCode : nextOffersMap.keySet()) {
			CustomerSMSNotificationType type = null;
			if (AppConstants.LOYAL_TEA_COUPON_CODE.equals(cloneCouponCode)) {
				//DONT SEND LOYALTY SMS
				continue;
			} else {
				type = CustomerSMSNotificationType.CLM_OFFER_REMINDER;
			}
			List<CustomerCampaignOfferDetail> nextOffers = nextOffersMap.get(cloneCouponCode);
			SMSWebServiceClient smsWebServiceClient = SolsInfiniWebServiceClient.getTransactionalClient(brand);
			if (nextOffers != null && nextOffers.size() > 0) {
				List<String> customerContact = new ArrayList<>();
				for (CustomerCampaignOfferDetail detail : nextOffers) {
					customerContact.add(detail.getContactNumber());
				}
				if (customerContact.isEmpty()) {
					return;
				}
				Map<String, Pair<Boolean, Boolean>> smsAndWhastappOptinData = customerService
						.getNotificationFlags(customerContact);
				for (CustomerCampaignOfferDetail customerCampaignOfferDetail : nextOffers) {
					String contactNumber = customerCampaignOfferDetail.getContactNumber();
                    String channelPartner = null;
                    if(Objects.nonNull(customerCampaignOfferDetail.getChannelPartner())){
                        channelPartner = masterCache.getChannelPartner(customerCampaignOfferDetail.getChannelPartner()).getName();
                    }
					Pair<Boolean, Boolean> notificationFlag = smsAndWhastappOptinData.get(contactNumber);
					if (Objects.nonNull(notificationFlag)
							&& (notificationFlag.getKey() || notificationFlag.getValue())) {
						NextOffer offer = new NextOffer(brandId, contactNumber,
								customerCampaignOfferDetail.getCustomerId(), customerCampaignOfferDetail.getFirstName(),
								true, customerCampaignOfferDetail.getCouponCode(),
								AppUtils.getDateString(customerCampaignOfferDetail.getCouponStartDate()),
								AppUtils.getDateString(customerCampaignOfferDetail.getCouponEndDate()),
								customerCampaignOfferDetail.getOfferText(),
								customerCampaignOfferDetail.getCampaignCloneCode(),
								customerCampaignOfferDetail.getCampaignCloneCode(), AppConstants.SMS,
                                channelPartner,
                                customerCampaignOfferDetail.getCouponType(),
                                customerCampaignOfferDetail.getChannelPartner());
						offer.setDaysLeft(nThDay);
						String message = type.getMessage(offer);
						/*try {
							sendNextBestOfferNotification(type.name(), message, contactNumber, smsWebServiceClient,
									getNotificationPayload(type, customerCampaignOfferDetail, notificationFlag));
						} catch (IOException | JMSException e) {
							LOG.info("Error in sending messages for {}, {}, {}, {} , {}, {}, {}", type.name(), nThDay,
									brand, cloneCouponCode, couponStartDate, nextOffers);
						}*/
					}
				}
			}
		}
	}

    private void sendReminderForOffer(Integer brandId) {
        Brand brand = masterCache.getBrandMetaData().get(brandId);
        List<CampaignOfferDetail> nextOffers = customerService
                .getNotUsedNextOfferDetails(brandId);
        CustomerSMSNotificationType type = null;
        SMSWebServiceClient smsWebServiceClient = SolsInfiniWebServiceClient.getTransactionalClient(brand);
        for (CampaignOfferDetail customerCampaignOfferDetail : nextOffers) {
            Map<String, Pair<Boolean, Boolean>> smsAndWhastappOptinData = customerService
                    .getNotificationFlags(Arrays.asList(customerCampaignOfferDetail.getContactNumber()));
            CampaignDetail campaignDetail = campaignCache.getCampaign(customerCampaignOfferDetail.getCampaignId());
            type = getSMSType(customerCampaignOfferDetail, campaignDetail);
            if (Objects.isNull(type)) {
                continue;
            }
            String contactNumber = customerCampaignOfferDetail.getContactNumber();
            String channelPartner = null;
            if (Objects.nonNull(customerCampaignOfferDetail.getChannelPartner())) {
                channelPartner = masterCache.getChannelPartner(customerCampaignOfferDetail.getChannelPartner()).getName();
            }
            Pair<Boolean, Boolean> notificationFlag = smsAndWhastappOptinData.get(contactNumber);
            if (Objects.nonNull(notificationFlag)
                    && (notificationFlag.getKey() || notificationFlag.getValue())) {
                NextOffer offer = new NextOffer(brandId, contactNumber,
                        customerCampaignOfferDetail.getCustomerId(), customerCampaignOfferDetail.getFirstName(),
                        true, customerCampaignOfferDetail.getCouponCode(),
                        AppUtils.getDateString(customerCampaignOfferDetail.getCouponStartDate()),
                        AppUtils.getDateString(customerCampaignOfferDetail.getCouponEndDate()),
                        customerCampaignOfferDetail.getOfferText(),
                        customerCampaignOfferDetail.getCampaignCloneCode(),
                        customerCampaignOfferDetail.getCampaignCloneCode(), AppConstants.SMS,
                        channelPartner,
                        customerCampaignOfferDetail.getCouponType(),
                        customerCampaignOfferDetail.getChannelPartner());
                offer.setDaysLeft(customerCampaignOfferDetail.getReminderDays());
                String message = type.getMessage(offer);
                try {
                    NotificationPayload payload = getNotificationPayload(type, customerCampaignOfferDetail, notificationFlag);
                    if(Objects.nonNull(payload)){
                        sendNextBestOfferNotification(type.name(), message, contactNumber, smsWebServiceClient, payload);
                    }else{
                        LOG.info("Skip sending reminder SMS as notification payload is null for customer campaign offer detail id {}",
                                customerCampaignOfferDetail.getCapmpaignOfferDetailId());
                    }
                } catch (IOException | JMSException e) {
                    LOG.error("Error in sending messages for {}, {}, {}, {}, {}", type.name(), offer.getDaysLeft(),
                            brand, customerCampaignOfferDetail.getCouponCode(), offer.getValidityFrom());
                }
            }
        }
    }
    private CustomerSMSNotificationType getSMSType(CampaignOfferDetail offerDetail, CampaignDetail campaignDetail){
        if (AppConstants.LOYAL_TEA_COUPON_CODE.equals(offerDetail.getCampaignCloneCode())) {
            return null;
        }
        if(CampaignStrategy.DELIVERY_NBO.name().equals(campaignDetail.getCampaignStrategy()) ||
                CampaignStrategy.DELIVERY_GENERAL.name().equals(campaignDetail.getCampaignStrategy())){
            return null;
        }else {
            return CustomerSMSNotificationType.CLM_OFFER_REMINDER;
        }
    }

    private void createOfferForNextJourney() throws DataNotFoundException, DataUpdationException, JMSException, IOException {
        List<String> strategies = new ArrayList<>();
        Date midnightDate = AppUtils.getNextDate(AppUtils.addDays(AppUtils.getCurrentTimestamp(), -1));
        strategies.add(CampaignStrategy.NBO.name());
        strategies.add(CampaignStrategy.DELIVERY_NBO.name());
        List<CustomerCampaignOfferDetail> pendingNextOffer = customerService.getPendingNextJourneyEligibleOffer(AppConstants.CHAAYOS_BRAND_ID, midnightDate);
        for (CustomerCampaignOfferDetail detail : pendingNextOffer) {
            CampaignDetail campaignDetail = campaignCache.getCampaign(detail.getCampaignId());
            Customer customer = customerService.getCustomer(detail.getCustomerId());
            Brand brand = masterCache.getBrandMetaData().get(AppConstants.CHAAYOS_BRAND_ID);
            CustomerRepeatType repeatType = CustomerRepeatType.getTypeFromName(detail.getCustomerType());
            if (CampaignStrategy.NBO.name().equals(campaignDetail.getCampaignStrategy())) {
                createNBONextJourneyOffer(repeatType, campaignDetail, brand, detail.getNextJourneyNumber(), customer, detail);
            } else if(CampaignStrategy.DELIVERY_NBO.name().equals(campaignDetail.getCampaignStrategy())){
                createDNBONextJourneyOffer(repeatType, campaignDetail, brand, detail.getNextJourneyNumber(), customer, detail);
            }else{
                LOG.error("Invalid campaign strategy for next journey offer creation");
            }
        }
    }

    private void createNBONextJourneyOffer(CustomerRepeatType type,CampaignDetail campaignDetail, Brand brand,
                                           Integer journey, Customer customer, CustomerCampaignOfferDetail offerDetail) throws DataUpdationException, JMSException, IOException {
        CampaignMapping cloneCode = campaignCache.getCloneCode(type, journey, campaignDetail.getCampaignId());
        CustomerMappingTypes customerMappingTypes = new CustomerMappingTypes(type.name(), journey,
                cloneCode.getValidityInDays(), type.getDefaultCloneCode(), cloneCode.getReminderDays());
        SMSWebServiceClient smsWebServiceClient = SolsInfiniWebServiceClient.getTransactionalClient(brand);
        CustomerDineInView oneView = customerOfferManagementService.getCustomerDineInView(customer.getId(),
                brand.getBrandId(), Arrays.asList(-1));
        CouponCloneResponse clonedCoupon = null;
        CustomerCampaignOfferDetail postOrderOfferCreationSuccess = null;
        CustomerSMSNotificationType notificationType = null;
        String startDay = null;
        Map<String, Pair<Boolean, Boolean>> smsAndWhastappOptinData = customerService
                .getNotificationFlags(Arrays.asList(customer.getContactNumber()));
        Pair<Boolean, Boolean> notificationFlag = smsAndWhastappOptinData.get(customer.getContactNumber());
        if (AppConstants.YES.equals(offerDetail.getIsOfferApplied())) {
            notificationType = CustomerSMSNotificationType.CLM_NEXT_OFFER_FOR_AVAILED;
        } else {
            notificationType = CustomerSMSNotificationType.CLM_NEXT_OFFER_FOR_NOT_AVAILED;
        }
        if (cloneCode != null) {
            LOG.info("NEXT-JOURNEY-OFFER-NBO : Clone Code found :: {} for customer type :: {} and validity in days :: {}",
                    cloneCode.getCode(), type.name(), customerMappingTypes.getValidityInDays());
            if (campaignDetail.isCouponClone()) {
                if (CustomerRepeatType.NEW.equals(type) && AppConstants.LOYAL_TEA_COUPON_CODE.equals(cloneCode.getCode())) {
                    startDay = AppUtils.getDateString(AppUtils.getNextDate(oneView.getLastOrderTime()), AppUtils.DATE_FORMAT_STRING);
                    clonedCoupon = customerOfferManagementService.getCloneCode(startDay,
                            Arrays.asList(customer.getContactNumber()), type, cloneCode.getCode(), 1,
                            type.getValidityInDays(), campaignDetail.getCouponPrefix(), null, null);
                } else {
                    clonedCoupon = customerOfferManagementService.getCloneCode(startDay,
                            Arrays.asList(customer.getContactNumber()), type, cloneCode.getCode(), 1,
                            customerMappingTypes.getValidityInDays(), campaignDetail.getCouponPrefix(), null, null);
                }
                postOrderOfferCreationSuccess = customerOfferManagementService
                        .createPostOrderOffer(brand.getBrandId(), offerDetail.getUnitId(), campaignDetail.getCampaignId(), customer.getId(), offerDetail.getOfferCreateOrderId(),
                                customer.getContactNumber(), customer.getCountryCode(),
                                clonedCoupon.getMappings().get(customer.getContactNumber()),
                                customer.getFirstName(), cloneCode.getDesc(),
                                clonedCoupon.getCode(), campaignDetail, type, offerDetail.getNextJourneyNumber(),null);

                if (postOrderOfferCreationSuccess != null) {
                    String contentUrl = getContentUrl(type);
                    if (CustomerRepeatType.NEW.equals(type) && !AppConstants.LOYAL_TEA_COUPON_CODE.equals(cloneCode.getCode())) {
                        getOrderManagementService().removeSecondFreeChai(customer.getId(), SignupOfferStatus.FORCE_EXPIRED.name());
                    }
                    NextOffer offer = getNextOffer(brand.getBrandId(), customer.getContactNumber(), customer.getId(),
                            customer.getFirstName(), postOrderOfferCreationSuccess, type,
                            AppConstants.SMS, contentUrl);
                    NotificationPayload payload = getNotificationPayload(notificationType, customer.getContactNumber(),
                            clonedCoupon.getMappings().get(customer.getContactNumber()), notificationFlag);
                    if(Objects.nonNull(payload)){
                        /*sendNextBestOfferNotification(type.name(), notificationType.getMessage(offer),
                                postOrderOfferCreationSuccess.getContactNumber(), smsWebServiceClient, payload);*/
                    }else{
                        LOG.info("Skip sending next journey SMS as notification payload is null");
                    }
                    offer.setExistingOffer(false);
                }
            } else {
                CouponDetailData couponDetail = getOfferManagementExternalService()
                        .getCoupon(cloneCode.getCode());
                CouponData couponData = getCouponData(couponDetail);
                customerOfferManagementService.addCustomerMappingCouponData(couponDetail, customer.getContactNumber());
                postOrderOfferCreationSuccess = customerOfferManagementService
                        .createPostOrderOffer(brand.getBrandId(), offerDetail.getUnitId(), campaignDetail.getCampaignId(), customer.getId(), null,
                                customer.getContactNumber(), customer.getCountryCode(), couponData,
                                customer.getFirstName(), cloneCode.getDesc(), cloneCode.getCode(), campaignDetail, type, offerDetail.getNextJourneyNumber(),null);
                if (postOrderOfferCreationSuccess != null) {
                    String contentUrl = getContentUrl(type);
                    NextOffer offer = getNextOffer(brand.getBrandId(), customer.getContactNumber(), customer.getId(),
                            customer.getFirstName(), postOrderOfferCreationSuccess, type,
                            AppConstants.SMS, contentUrl);
                    NotificationPayload payload = getNotificationPayload(notificationType, customer.getContactNumber(),
                            clonedCoupon.getMappings().get(customer.getContactNumber()), notificationFlag);
                    if(Objects.nonNull(payload)){
                        /*sendNextBestOfferNotification(type.name(), notificationType.getMessage(offer),
                                postOrderOfferCreationSuccess.getContactNumber(), smsWebServiceClient, payload);*/
                    }else{
                        LOG.info("Skip sending next journey SMS as notification payload is null");
                    }
                    offer.setExistingOffer(false);
                }
            }
        }
    }

    private void createDNBONextJourneyOffer(CustomerRepeatType type,CampaignDetail campaignDetail, Brand brand,
                                            Integer journey, Customer customer, CustomerCampaignOfferDetail offerDetail) throws JMSException, IOException {
        CampaignMapping cloneCode = campaignCache.getCloneCode(type, journey, campaignDetail.getCampaignId());
        CustomerMappingTypes customerMappingTypes = null;
        SMSWebServiceClient smsWebServiceClient = SolsInfiniWebServiceClient.getTransactionalClient(brand);
        CouponCloneResponse clonedCoupon = null;
        CustomerCampaignOfferDetail nextJourneyOffer = null;
        CustomerSMSNotificationType notificationType = null;
        DeliveryCouponDetailData deliveryCoupon = null;
        Map<String, Map<Integer, CampaignMapping>> couponMap = campaignDetail.getMappings();
        Map<String, Pair<Boolean, Boolean>> smsAndWhastappOptinData = customerService
                .getNotificationFlags(Arrays.asList(customer.getContactNumber()));
        if(AppConstants.YES.equals(offerDetail.getIsOfferApplied())){
            notificationType = CustomerSMSNotificationType.CLM_DELIVERY_NEXT_OFFER_FOR_AVAILED;
        }else{
            notificationType = CustomerSMSNotificationType.CLM_DELIVERY_NEXT_OFFER_FOR_NOT_AVAILED;
        }
        Pair<Boolean, Boolean> notificationFlag = smsAndWhastappOptinData.get(customer.getContactNumber());
         customerMappingTypes = new CustomerMappingTypes(type.name(), journey,
                couponMap.get(type.name()).get(journey).getValidityInDays(), type.getDefaultCloneCode(), type.getReminderDays());
        if (cloneCode != null) {
            LOG.info("DELIVERY_NEXT_OFFER_DNBO : Clone Code found :: {} for customer type :: {} and validity in days :: {}",
                    cloneCode.getCode(),type.name(),customerMappingTypes.getValidityInDays());
            if (campaignDetail.isCouponClone()) {
                deliveryCoupon = customerOfferManagementService.getDeliveryCloneCode(cloneCode.getCode(), brand.getBrandId(), true);
                if(Objects.nonNull(deliveryCoupon)){
                    Integer couponDelay = Objects.nonNull(campaignDetail.getCouponApplicableAfter()) ? campaignDetail.getCouponApplicableAfter() : 0;
                    Date startDate = AppUtils.getNextDate(AppUtils.addDays(AppUtils.getCurrentTimestamp(),couponDelay-1));
                    if(AppUtils.isBefore(startDate,deliveryCoupon.getStartDate())){
                        startDate = deliveryCoupon.getStartDate();
                    }
                    Date endDate = AppUtils.getDayBeforeOrAfterDay(startDate, cloneCode.getValidityInDays()-1);
                    if(AppUtils.isBefore(deliveryCoupon.getEndDate(), endDate)){
                        endDate = deliveryCoupon.getEndDate();
                    }
                    CouponData couponData = new CouponData(deliveryCoupon.getCouponCode(), AppUtils.getDateString(startDate,AppUtils.DATE_FORMAT_STRING),
                            AppUtils.getDateString(endDate,AppUtils.DATE_FORMAT_STRING),campaignDetail.getUsageLimit(),null,null);
                    nextJourneyOffer = customerOfferManagementService
                            .createDeliveryPostOrderOffer(brand.getBrandId(), offerDetail.getUnitId(), campaignDetail.getCampaignId(), customer, offerDetail.getOfferCreateOrderId(),
                                    customer.getContactNumber(), customer.getCountryCode(),
                                    couponData, customer.getFirstName(), cloneCode.getDesc(),
                                    deliveryCoupon.getMasterCoupon(),deliveryCoupon,campaignDetail.isCouponClone(),campaignDetail, type, offerDetail.getNextJourneyNumber(),null);
                    if (nextJourneyOffer != null) {
                        String contentUrl = getContentUrl(type);
                        NextOffer offer = getNextOffer(brand.getBrandId(), customer.getContactNumber(), customer.getId(),
                                customer.getFirstName(), nextJourneyOffer, type,
                                AppConstants.SMS, contentUrl);
                        CouponData data = new CouponData(offer.getOfferCode(), offer.getValidityFrom(), offer.getValidityTill(),
                                null,nextJourneyOffer.getCouponDetailId(),nextJourneyOffer.getOfferDetailId());
                        NotificationPayload payload = getNotificationPayload(notificationType, customer.getContactNumber(),
                                data, notificationFlag);
                        if(Objects.nonNull(payload)){
                            /*sendNextBestOfferNotification(type.name(), notificationType.getMessage(offer),
                                    nextJourneyOffer.getContactNumber(), smsWebServiceClient, payload);*/
                        }else{
                            LOG.info("Skip sending next journey SMS as notification payload is null");
                        }
                        offer.setExistingOffer(false);
                    }
                }else{
                    LOG.info(
                            "DELIVERY_POST_ORDER_OFFER - No partner coupon found for - Master coupon : {}",
                            cloneCode.getCode());
                }
            } else {
                LOG.info(
                        "DELIVERY_POST_ORDER_OFFER - Fetching master coupon detail as isClone Coupon is {}",
                        campaignDetail.isCouponClone());
                deliveryCoupon = customerOfferManagementService.getDeliveryCloneCode(cloneCode.getCode(), brand.getBrandId(), false);
                if(Objects.nonNull(deliveryCoupon)){
                    Integer couponDelay = Objects.nonNull(campaignDetail.getCouponApplicableAfter()) ? campaignDetail.getCouponApplicableAfter() : 0;
                    Date startDate = AppUtils.getNextDate(AppUtils.addDays(AppUtils.getCurrentTimestamp(),couponDelay-1));
                    if(AppUtils.isBefore(startDate,deliveryCoupon.getStartDate())){
                        startDate = deliveryCoupon.getStartDate();
                    }
                    Date endDate = AppUtils.getDayBeforeOrAfterDay(startDate, cloneCode.getValidityInDays()-1);
                    if(AppUtils.isBefore(deliveryCoupon.getEndDate(), endDate)){
                        endDate = deliveryCoupon.getEndDate();
                    }
                    CouponData couponData = new CouponData(deliveryCoupon.getMasterCoupon(), AppUtils.getDateString(startDate,AppUtils.DATE_FORMAT_STRING),
                            AppUtils.getDateString(endDate,AppUtils.DATE_FORMAT_STRING),campaignDetail.getUsageLimit(),null,null);
                    nextJourneyOffer = customerOfferManagementService
                            .createDeliveryPostOrderOffer(brand.getBrandId(), offerDetail.getUnitId(), campaignDetail.getCampaignId(), customer, offerDetail.getOfferCreateOrderId(),
                                    customer.getContactNumber(), customer.getCountryCode(),
                                    couponData, customer.getFirstName(), cloneCode.getDesc(),
                                    deliveryCoupon.getMasterCoupon(),deliveryCoupon,campaignDetail.isCouponClone(),campaignDetail,type, offerDetail.getNextJourneyNumber(),null);
                    if (nextJourneyOffer != null) {
                        String contentUrl = getContentUrl(type);
                        NextOffer offer = getNextOffer(brand.getBrandId(), customer.getContactNumber(), customer.getId(),
                                customer.getFirstName(), nextJourneyOffer, type,
                                AppConstants.SMS, contentUrl);
                        NotificationPayload payload = getNotificationPayload(notificationType, customer.getContactNumber(),
                                clonedCoupon.getMappings().get(customer.getContactNumber()), notificationFlag);
                        if(Objects.nonNull(payload)){
                            /*sendNextBestOfferNotification(type.name(), notificationType.getMessage(offer),
                                    nextJourneyOffer.getContactNumber(), smsWebServiceClient, payload);*/
                        }else{
                            LOG.info("Skip sending next journey SMS as notification payload is null");
                        }
                        offer.setExistingOffer(false);
                    } else {
                    }
                }else {
                    LOG.info(
                            "DELIVERY_POST_ORDER_OFFER - No Master coupon found for : {}",
                            cloneCode.getCode());
                }
            }
        }
    }

    private CouponData getCouponData(CouponDetailData couponDetail) {
        if (Objects.nonNull(couponDetail)) {
            return new CouponData(couponDetail.getCouponCode(), AppUtils.getDateString(couponDetail.getStartDate()),
                    AppUtils.getDateString(couponDetail.getEndDate()), couponDetail.getUsageCount(),
                    couponDetail.getCouponDetailId(), couponDetail.getOfferDetail().getOfferDetailId());
        }
        return null;
    }

    private NextOffer getNextOffer(Integer brandId, String contactNumber, Integer customerId, String firstName,
                                   CustomerCampaignOfferDetail r, CustomerRepeatType type, String notificationType, String contentUrl) {
        String channelPartner = null;
        if (Objects.nonNull(r.getChannelPartner())) {
            channelPartner = getMasterDataCache().getChannelPartner(r.getChannelPartner()).getName();
        }
        if (Objects.isNull(r)) {
            return new NextOffer(brandId, contactNumber, customerId, firstName, false, null, null, null, null, null,
                    null, null, null, null, null);
        }
        return new NextOffer(brandId, contactNumber, customerId, firstName, true, r.getCouponCode(),
                AppUtils.getDateString(r.getCouponStartDate()), AppUtils.getDateString(r.getCouponEndDate()),
                r.getOfferText(), type.name(), contentUrl, notificationType, channelPartner, r.getCouponType(), r.getChannelPartner());
    }

    private String getContentUrl(CustomerRepeatType type) {
        String contentUrl = null;
        switch (type) {
            case REGISTER:
                contentUrl = "CLM_REGISTER";
                break;
            case REPEAT:
                contentUrl = "CLM_REPEAT";
                break;
            case DORMANT:
                contentUrl = "CLM_DORMANT";
                break;
            case NEW:
                contentUrl = "LOYAL_TEA";
                break;
            case DEFAULT:
                contentUrl = "DEFAULT";
            default:
                contentUrl = "LOYAL_TEA";
        }
        return contentUrl;
    }


	private void createCouponAndSendReminder(CustomerRepeatType customerRepeatType,
			int nThDay, Integer brandId, Date couponStartDate, boolean used, Integer journey) {
		if (journey == null) {
			return;
		}
		Brand brand = masterCache.getBrandMetaData().get(brandId);
		LOG.info("Running Reminder process for {}, {}, {}, {} , {}", nThDay, brand, couponStartDate, used);
		List<CampaignCouponMapping> couponMappings = getOfferManagementExternalService()
				.getActiveCampaignsByJourney(customerRepeatType.name(), journey);
		if (couponMappings == null || couponMappings.size() == 0) {
			return;
		}
		CustomerSMSNotificationType type = null;
		for (CampaignCouponMapping campaign : couponMappings) {
			CampaignMapping cloneCode = campaignCache.getCloneCode(customerRepeatType, journey,
					campaign.getCampaignDetailData().getCampaignId());
			if (cloneCode == null) {
				return;
			}
			List<CustomerCampaignOfferDetail> nextOffers = null;
			CampaignMapping existingCode = campaignCache.getCloneCode(customerRepeatType, journey - 1,
					campaign.getCampaignDetailData().getCampaignId());
			if (existingCode == null) {
				return;
			}
			Map<String, CustomerCampaignOfferDetail> existingCoupons = new HashMap<>();
			if (used) {
				type = CustomerSMSNotificationType.CLM_NEXT_OFFER_FOR_AVAILED;
				nextOffers = customerService.getUsedNextBestOfferDetails(brandId, existingCode.getCode(),
						couponStartDate);
			} else {
				type = CustomerSMSNotificationType.CLM_NEXT_OFFER_FOR_NOT_AVAILED;
				nextOffers = customerService.getNotUsedNextBestOfferDetails(brandId, existingCode.getCode(),
						couponStartDate);
			}
			if (nextOffers != null && nextOffers.size() > 0) {
				LOG.info("Next Best Offer Found {} for {}", nextOffers.size(), existingCode.getCode());
				List<CustomerCampaignOfferDetail> alreadyCreatedOffers = customerService
						.getAllNextBestOfferDetails(brandId, cloneCode.getCode(), AppUtils.getBusinessDate());
				if (alreadyCreatedOffers != null && alreadyCreatedOffers.size() > 0) {
					LOG.info("Already Created Offers Found {}", alreadyCreatedOffers.size());
					existingCoupons = alreadyCreatedOffers.stream().collect(
							Collectors.toMap(CustomerCampaignOfferDetail::getContactNumber, Function.identity()));
				}
				String startDay = AppUtils.getDateString(AppUtils.getBusinessDate(), AppConstants.DATE_FORMAT);
				List<String> customerContact = new ArrayList<>();
				for (CustomerCampaignOfferDetail detail : nextOffers) {
					customerContact.add(detail.getContactNumber());
				}
				LOG.info("Customer Found {}", customerContact.size());
				Map<String, Pair<Boolean, Boolean>> smsAndWhastappOptinData = customerService
						.getNotificationFlags(customerContact);
				LOG.info("Notification Flags Found  {}", smsAndWhastappOptinData.size());
				CouponCloneResponse response = getCouponCloneResponseData(startDay, customerRepeatType,
						cloneCode.getCode(), customerContact, null, null, customerRepeatType.getValidityInDays());
				LOG.info("Coupon Generated Size {}", response.getMappings().size());
				if (Objects.nonNull(response) && !response.getErrors().isEmpty()) {
					LOG.info("Coupon Generated Size {}", StringUtils.join(response.getErrors(), ","));
					LOG.info("Coupon Generated Size {}", response.getErrors());
				}
				SMSWebServiceClient smsWebServiceClient = SolsInfiniWebServiceClient.getTransactionalClient(brand);
				if (response != null && response.getMappings() != null && response.getMappings().size() > 0) {
					for (CustomerCampaignOfferDetail customerCampaignOfferDetail : nextOffers) {
						String contactNumber = customerCampaignOfferDetail.getContactNumber();
						if (existingCoupons.containsKey(contactNumber)) {
							LOG.info("Skipping Customer {} as Coupon Already Generated", contactNumber);
							continue;
						}
						Pair<Boolean, Boolean> notificationFlag = smsAndWhastappOptinData.get(contactNumber);
						if (Objects.nonNull(notificationFlag)
								&& (notificationFlag.getKey() || notificationFlag.getValue())) {
							if (response.getMappings().containsKey(contactNumber)) {
								CouponData data = response.getMappings().get(contactNumber);
								LOG.info("Coupon Data Found with Coupon Code {}", data.getCoupon());
                                String channelPartner = Objects.nonNull(customerCampaignOfferDetail.getChannelPartner()) ?
                                        masterCache.getChannelPartner(customerCampaignOfferDetail.getChannelPartner()).getName() : null;
								NextOffer offer = new NextOffer(brandId, contactNumber,
										customerCampaignOfferDetail.getCustomerId(),
										customerCampaignOfferDetail.getFirstName(), true, data.getCoupon(),
										data.getStartDate(), data.getEndDate(), response.getDescription(),
										customerRepeatType.name(), customerCampaignOfferDetail.getCampaignCloneCode(),
										AppConstants.SMS,channelPartner,customerCampaignOfferDetail.getCouponType(),
                                        customerCampaignOfferDetail.getChannelPartner());
								String message = type.getMessage(offer);
								try {
									sendNextBestOfferNotification(type.name(), message,
											customerCampaignOfferDetail.getContactNumber(), smsWebServiceClient,
											getNotificationPayload(type, contactNumber, data, notificationFlag));
									customerOfferManagementService.createPostOrderOffer(
											customerCampaignOfferDetail.getBrandId(),
											customerCampaignOfferDetail.getUnitId(),
											customerCampaignOfferDetail.getCampaignId(),
											customerCampaignOfferDetail.getCustomerId(),
											customerCampaignOfferDetail.getOfferCreateOrderId(), contactNumber,
											AppConstants.DEFAULT_COUNTRY_CODE, data,
											customerCampaignOfferDetail.getFirstName(), response.getDescription(),
											cloneCode.getCode(), MasterDataConverter.convert(campaign.getCampaignDetailData()),customerRepeatType, journey,null);
								} catch (IOException | JMSException e) {
									LOG.info("Error in sending messages for {}, {}, {}, {} , {}, {}, {}, {}, {}",
											type.name(), nThDay, brand, existingCode.getCode(), couponStartDate, used,
											cloneCode.getCode(), nextOffers);
								}
							}
						}
					}

				}
			}
		}
	}

    private NotificationPayload getNotificationPayload(CustomerSMSNotificationType type, CampaignOfferDetail customerCampaignOfferDetail, Pair<Boolean, Boolean> notificationFlag) {
        try {
            Map<String,String> map= new HashMap<>();
            map.put("couponValidity",AppUtils.getDateInMonth(customerCampaignOfferDetail.getCouponEndDate()));
            map.put("couponCode",customerCampaignOfferDetail.getCouponCode());
            NotificationPayload payload= new NotificationPayload();
            payload.setContactNumber(customerCampaignOfferDetail.getContactNumber());
            payload.setCustomerId(customerCampaignOfferDetail.getCustomerId());
            payload.setSendWhatsapp(type.isWhatsapp());
            payload.setWhatsappOptIn(notificationFlag.getValue());
            payload.setPayload(map);
            payload.setRequestTime(AppUtils.getCurrentTimestamp());
            return payload;
        } catch (Exception e){
            LOG.error("Exception Faced While Generating Notification Payload for Contact ::: {}",customerCampaignOfferDetail.getContactNumber());
            return null;
        }
    }private NotificationPayload getNotificationPayload(CustomerSMSNotificationType type, CustomerCampaignOfferDetail customerCampaignOfferDetail, Pair<Boolean, Boolean> notificationFlag) {
        try {
            Map<String,String> map= new HashMap<>();
            map.put("couponValidity",AppUtils.getDateInMonth(customerCampaignOfferDetail.getCouponEndDate()));
            map.put("couponCode",customerCampaignOfferDetail.getCouponCode());
            NotificationPayload payload= new NotificationPayload();
            payload.setContactNumber(customerCampaignOfferDetail.getContactNumber());
            payload.setCustomerId(customerCampaignOfferDetail.getCustomerId());
            payload.setSendWhatsapp(type.isWhatsapp());
            payload.setWhatsappOptIn(notificationFlag.getValue());
            payload.setPayload(map);
            payload.setRequestTime(AppUtils.getCurrentTimestamp());
            return payload;
        } catch (Exception e){
            LOG.error("Exception Faced While Generating Notification Payload for Contact ::: {}",customerCampaignOfferDetail.getContactNumber());
            return null;
        }
    }

	private NotificationPayload getNotificationPayload(CustomerSMSNotificationType type, String contactNumber, CouponData data, Pair<Boolean, Boolean> notificationFlag) {
		try {
			Map<String, String> map = new HashMap<>();
			map.put("couponValidity",
					AppUtils.getDateInMonth(AppUtils.getDate(data.getEndDate(), AppConstants.DATE_FORMAT)));
			map.put("couponCode", data.getCoupon());
			NotificationPayload payload = new NotificationPayload();
            payload.setMessageType(type.name());
			payload.setContactNumber(contactNumber);
			payload.setPayload(map);
            payload.setSendWhatsapp(type.isWhatsapp());
            payload.setWhatsappOptIn(notificationFlag.getValue());
            payload.setRequestTime(AppUtils.getCurrentTimestamp());
			return payload;
		} catch (Exception e) {
			LOG.error("Exception Faced While Generating Notification Payload for Contact ::: {}", contactNumber);
			return null;
		}
	}

    private void sendNextBestOfferNotification(String type, String message, String contactNumber,
                                               SMSWebServiceClient smsWebServiceClient, NotificationPayload payload) throws IOException, JMSException {
        payload.setMessageType(type);
        notificationService.sendNotification(type,
                message, contactNumber, smsWebServiceClient, true,payload);
    }

	private CouponCloneResponse getCouponCloneResponseData(String startDay, CustomerRepeatType customerRepeatType,
			String cloneCouponCode, List<String> customerContact, Integer applicableUnitId, String applicableRegion,
			Integer validityInDays) {
		try {
			return customerOfferManagementService.getCloneCode(startDay, customerContact, customerRepeatType,
					cloneCouponCode, 1, validityInDays, "CLM", applicableUnitId, applicableRegion);
		} catch (Exception e) {
			LOG.error("Exception Faced While Cloning Coupon Response for Type :: {}", cloneCouponCode, e);
			return null;
		}
	}

    @RequestMapping(method = RequestMethod.POST, value = "get-item-estimate", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public UnitWiseDayOfWeekWiseItemEstimate getItemEstimate(
			@RequestBody UnitWiseDayOfWeekWiseItemEstimateRequest request) {
		LOG.info("get unit wise day wise item estimate {} ", request);
		try {
			return posMetadataService.getConsumptionEstimates(request);
		} catch (Exception e) {
			LOG.info("Exception error while getting unit wise day wise item estimate {}", request, e);
			return null;
		}
	}

    @RequestMapping(method = RequestMethod.GET, value = "validate-kettle-day-close", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Boolean validateKettleDayClose(@RequestParam Integer dayClosureId) {
        LOG.info("Validating Kettle Day Close For Kettle Day Close Id : {} ", dayClosureId);
        return posMetadataService.validateKettleDayClose(dayClosureId);
    }

    @Autowired
    private FileArchiveService fileArchiveService;


    @RequestMapping(method = RequestMethod.POST, value = "get-order-feedback-url", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public IdCodeName getOrderReceiptForNotification(@RequestParam Integer orderId,@RequestParam boolean feedback) throws DataNotFoundException, TemplateRenderingException, EmailGenerationException {
        try{
        	OrderInfo info = getOrderSearchService().getOrderReceipt(orderId, false, null);
        //    return orderNotificationService.generateOrderFeedbackDetails(FeedbackSource.WHATS_APP, info,feedback, true,new OrderFeedbackMetadata()).getFeedbackAndReceiptDetails();
            return orderNotificationService.generateOrderFeedbackDetails(FeedbackSource.SMS, info,feedback, true,new OrderFeedbackMetadata()).getFeedbackAndReceiptDetails();
        }catch(Exception e){
            LOG.error("Exception Faced while Retrieving Order Feedback ::{}", orderId ,e);
            return null;
        }
    }
    private void createReceiptPdf(String receiptContent, String orderId,
                                  Unit unit, IdCodeName codeName) {
        try {
            if (StringUtils.isNotBlank(receiptContent) && StringUtils.isNotBlank(orderId)) {
                String kioskPath = props.getBasePath() + "/" + unit.getId() + "/whatsapp/orders";
                File kioskFolder = new File(kioskPath);
                if (!kioskFolder.exists()) {
                    kioskFolder.mkdirs();
                }
                String fileName = "orderReceipt-" + orderId + ".pdf";
                String receiptPath = kioskPath + "/" + fileName;
                File pdfFile = new File(receiptPath);
                if (!pdfFile.exists()) {
                    pdfFile.createNewFile();
                }
                try (OutputStream outputStream = new FileOutputStream(pdfFile)) {
                    HtmlConverter.convertToPdf(receiptContent, outputStream);
                    outputStream.flush();

                    String baseDir = "whatsapp/" + unit.getId() + "/orders";
                    try {
                        FileDetail s3File = fileArchiveService.saveFileToS3(props.getS3Bucket(), baseDir, pdfFile,
                                true,7);
                        if (s3File != null) {
//                            codeName.setName(SolsInfiniWebServiceClient.getTransactionalClient().getShortUrl(s3File.getUrl()).getUrl());
                            codeName.setName(SolsInfiniWebServiceClient.getTransactionalClient()
                                    .getShortUrl(getEnvironmentProperties().getOrderReceipt()+"/"+ unit.getId() + "/orders/"+fileName).getUrl());
                        } else {
                            LOG.error("Error uploading report to S3.");
                        }
                        pdfFile.delete();
                    } catch (Exception e) {
                        LOG.error("Encountered error while uploading report to S3", e);
                    }

                } catch (IOException e) {
                    /*
                     * String errorMsg = "Unable to create receipt pdf "; LOG.error(errorMsg, e);
                     */
                    LOG.error("Exception Occurred while converting html to pdf");
                }
            }
        } catch (Exception ex) {
            LOG.error("Exception Occurred while creating PDF of Receipt");
        }
    }

    @RequestMapping(method = RequestMethod.GET, value = "clear-campaign-cache", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public  boolean clearCampaignCache() {
        try {
            campaignCache.reloadCache();
        }catch (Exception e){
            LOG.error("Unable to reload campaignCache");
            return false;
        }
        return true;
    }

    @RequestMapping(method = RequestMethod.GET, value = "trigger-subscription-reminder", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void chaayosSubscriptionMessages() {
        LOG.info("CHAAYOS_SUBSCRIPTION_REMINDER ### Generating Messages on {}", AppUtils.getCurrentDate());
//        sendReminderForNotUsingSubscriptionNthDay(-15, CustomerSMSNotificationType.CHAAYOS_SUBSCRIPTION_REMINDER_1.name());
//        sendReminderForNotUsingSubscriptionNthDay(-30, CustomerSMSNotificationType.CHAAYOS_SUBSCRIPTION_REMINDER_2.name());
//        sendReminderForNotUsingSubscriptionNthDay(-45, CustomerSMSNotificationType.CHAAYOS_SUBSCRIPTION_REMINDER_1.name());
//        sendReminderForNotUsingSubscriptionNthDay(-60, CustomerSMSNotificationType.CHAAYOS_SUBSCRIPTION_REMINDER_1.name());
        sendReminderForNotUsingSubscriptionNthDay(-10, CustomerSMSNotificationType.CHAAYOS_SUBSCRIPTION_REMINDER.name());
        sendReminderForNotUsingSubscriptionNthDay(-20, CustomerSMSNotificationType.CHAAYOS_SUBSCRIPTION_REMINDER.name());
        sendReminderForNotUsingSubscriptionNthDay(-30, CustomerSMSNotificationType.CHAAYOS_SUBSCRIPTION_REMINDER.name());
        LOG.info("CHAAYOS_SUBSCRIPTION_EXPIRY_REMINDER ### Generating Messages on {}", AppUtils.getCurrentDate());
        subscriptionManagementService.sendChaayosSubscriptionExpiryReminder(7);
        subscriptionManagementService.sendChaayosSubscriptionExpiryReminder(15);
        subscriptionManagementService.sendChaayosSubscriptionExpiryReminder(30);
    }

    private void sendReminderForNotUsingSubscriptionNthDay(int nthDay, String type) {
        LOG.info("CHAAYOS_SUBSCRIPTION_REMINDER ### Generating Messages for customers for not using select {} days", nthDay);
        subscriptionManagementService.sendReminderForNotUsingSubscriptionNthDay(nthDay, type);
    }

    @RequestMapping(method = RequestMethod.GET, value = "get/unit/dayClose", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public DayCloseRequestStatus getKettleDayClose(@RequestParam Integer unitId) {
        Unit unit = masterCache.getUnit(unitId);
        if(props.dayCloseExcludeCompany() == unit.getCompany().getId()){
            return DayCloseRequestStatus.builder().unitId(unitId).businessDate(AppUtils.getPreviousBusinessDate())
            .dayCloseStatus(true).dayCloseExclusion(true).build();
        }
        UnitClosureDetails unitClosure = posMetadataService.getKettleDayClose(unitId,AppUtils.getPreviousBusinessDate());
       return Objects.nonNull(unitClosure) ? DayCloseRequestStatus.
               builder()
               .businessDate(unitClosure.getBusinessDate())
               .unitId(unitClosure.getUnitId())
               .dayCloseStatus(unitClosure.getCurrentStatus().equals(ClosureState.INITIATED.name()))
               .kettleStartOrderId(unitClosure.getStartOrderId())
               .kettleEndOrderId(unitClosure.getLastOrderId())
               .dayCloseExclusion(false).build() : null;
    }

    @RequestMapping(method = RequestMethod.POST, value = "wallet-recommendation",consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public WalletRecommendationDetail getSuggestWalletRecommendation(@RequestBody WalletSuggestionCustomerInfo customerData) {
        return droolsDecisionService.getCustomerWalletRecommendation(customerData);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-menu-scm-products", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<ScmProductRequestData> getScmProductsForMenuProductNew(
            @RequestBody List<ScmProductRequestData> scmProductRequestData,
            @RequestParam(required = false) Integer unitId,
            @RequestParam(required = false) String source,
            @RequestParam(required = false) Integer brandId,
            @RequestParam(required = false, defaultValue = "1") Integer quantity) throws DataNotFoundException {
        List<ScmProductRequestData> scmConsumableList = new ArrayList<>();
        for(ScmProductRequestData data : scmProductRequestData) {
            ScmProductRequestData requestData = new ScmProductRequestData();
            requestData.setDate(data.getDate());
            List<Consumable> scmProducts = new ArrayList<>();
//        LOG.info("Getting SCM products for menu product ID: {}, dimension: {}, unitId: {}, quantity: {}",
//                , dimension, unitId, quantity);

            try {
                // Use default values if not provided
                int resolvedUnitId = unitId != null ? unitId : 1;
                String resolvedSource = source != null ? source : "CAFE";
                int resolvedBrandId = brandId != null ? brandId : AppConstants.CHAAYOS_BRAND_ID;

                // Get product details for validation
                Set<Integer> productsIds = new HashSet<>();
                List<OrderItem> orderItemList = new ArrayList<>();
                for(Consumable productData : data.getProducts()){
                    productsIds.add(productData.getProductId());
                    Product menuProduct = getMasterDataCache().getProduct(productData.getProductId());
                    if (menuProduct == null) {
                        throw new DataNotFoundException("Menu product not found with ID: " + productData.getProductId());
                    }
                    // Create a mock order item with all necessary details
                    OrderItem mockItem = new OrderItem();
                    mockItem.setProductId(productData.getProductId());
                    mockItem.setProductName(menuProduct.getName());
                    mockItem.setDimension(productData.getDimension());
                    mockItem.setQuantity(productData.getQuantity().intValue());
                    mockItem.setTakeAway("TAKE_AWAY".equals(resolvedSource));

                    // Set product category for proper processing (using product type ID)
                    mockItem.setProductCategory(new IdCodeName(menuProduct.getType(),
                            "PRODUCT_TYPE_" + menuProduct.getType(), "Product Type " + menuProduct.getType()));
                    orderItemList.add(mockItem);
                }

                // Create a mock order with proper setup
                Order mockOrder = new Order();
                mockOrder.setUnitId(resolvedUnitId);
                mockOrder.setSource(resolvedSource);
                mockOrder.setBrandId(resolvedBrandId);
                mockOrder.setStatus(OrderStatus.DELIVERED); // Set to delivered to ensure consumption calculation
                mockOrder.setOrders(orderItemList);

                // Use ItemConsumptionHelper to get consumption with all business logic
                List<OrderItemConsumable> consumables = helper.getConsumption(mockOrder, resolvedUnitId);

                // Extract and return the SCM products for the specific menu product
                Map<Integer,Integer> productIndexMap = new HashMap<>();
                Integer index =0;
                for (OrderItemConsumable orderItemConsumable : consumables) {
                    if (productsIds.contains(orderItemConsumable.getMenuProductId())) {
                        // Create Consumable object from OrderItemConsumable data
                        Consumable scmProduct = new Consumable();
                        if(productIndexMap.containsKey(orderItemConsumable.getScmProductId())){
                            scmProduct = scmProducts.get(productIndexMap.get(orderItemConsumable.getScmProductId()));
                            scmProduct.setQuantity(AppUtils.add(scmProduct.getQuantity(),orderItemConsumable.getScmProductQuantity()));
                        }else {
                            scmProduct.setProductId(orderItemConsumable.getScmProductId());
                            scmProduct.setName(orderItemConsumable.getScmProductName());
                            scmProduct.setQuantity(orderItemConsumable.getScmProductQuantity());
                            scmProduct.setUom(orderItemConsumable.getScmProductUom());
                            scmProducts.add(scmProduct);
                            productIndexMap.put(scmProduct.getProductId(),index++);
                        }
                    }
                }
                LOG.info("Found {} SCM products for menu product ID: {}", scmProducts.size());
            } catch (Exception e) {
                LOG.error("Error getting SCM products for menu product ID: {}, dimension: {}", e);
                throw new DataNotFoundException("Unable to fetch SCM products for the given menu product: " + e.getMessage());
            }
            requestData.setProducts(scmProducts);
            scmConsumableList.add(requestData);
        }
        return scmConsumableList;
    }

    @RequestMapping(method = RequestMethod.GET, value = "menu-product/scm-products/detailed", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Map<String, Object> getDetailedScmProductsForMenuProduct(
            @RequestParam Integer menuProductId,
            @RequestParam String dimension,
            @RequestParam(required = false) Integer unitId,
            @RequestParam(required = false) String source,
            @RequestParam(required = false) Integer brandId,
            @RequestParam(required = false, defaultValue = "1") Integer quantity) throws DataNotFoundException {
        
        LOG.info("Getting detailed SCM products for menu product ID: {}, dimension: {}, quantity: {}", menuProductId, dimension, quantity);
        
        try {
            // Use default values if not provided
            int resolvedUnitId = unitId != null ? unitId : 1;
            String resolvedSource = source != null ? source : "CAFE";
            int resolvedBrandId = brandId != null ? brandId : AppConstants.CHAAYOS_BRAND_ID;
            
            // Get product details
            Product menuProduct = getMasterDataCache().getProduct(menuProductId);
            
            // Create a mock order item
            OrderItem mockItem = new OrderItem();
            mockItem.setProductId(menuProductId);
            mockItem.setProductName(menuProduct.getName());
            mockItem.setDimension(dimension);
            mockItem.setQuantity(quantity);
            mockItem.setTakeAway("TAKE_AWAY".equals(resolvedSource));
            
            // Create a mock order
            Order mockOrder = new Order();
            mockOrder.setUnitId(resolvedUnitId);
            mockOrder.setSource(resolvedSource);
            mockOrder.setBrandId(resolvedBrandId);
            mockOrder.setOrders(Collections.singletonList(mockItem));
            
            // Get consumption map which provides detailed breakdown
            Map<IdCodeName, IdNameValueMap> consumptionMap = helper.getConsumptionMap(mockOrder, resolvedUnitId);
            
            // Prepare response
            Map<String, Object> response = new HashMap<>();
            response.put("menuProductId", menuProductId);
            response.put("menuProductName", menuProduct.getName());
            response.put("dimension", dimension);
            response.put("quantity", quantity);
            response.put("unitId", resolvedUnitId);
            response.put("source", resolvedSource);
            response.put("brandId", resolvedBrandId);
            
            List<Map<String, Object>> scmProductsList = new ArrayList<>();
            
            for (Map.Entry<IdCodeName, IdNameValueMap> entry : consumptionMap.entrySet()) {
                IdCodeName menuProductKey = entry.getKey();
                if (menuProductKey.getId() == menuProductId) {
                    IdNameValueMap valueMap = entry.getValue();
                    
                    Map<String, Object> menuProductData = new HashMap<>();
                    menuProductData.put("menuProduct", menuProductKey);
                    menuProductData.put("soldQuantity", valueMap.getValue());
                    
                    List<Map<String, Object>> ingredients = new ArrayList<>();
                    for (Map.Entry<IdCodeName, IdNameValue> scmEntry : valueMap.getValueMap().entrySet()) {
                        Map<String, Object> ingredient = new HashMap<>();
                        ingredient.put("scmProductId", scmEntry.getKey().getId());
                        ingredient.put("scmProductName", scmEntry.getKey().getName());
                        ingredient.put("uom", scmEntry.getKey().getCode());
                        ingredient.put("consumedQuantity", scmEntry.getValue().getValue());
                        ingredients.add(ingredient);
                    }
                    menuProductData.put("scmIngredients", ingredients);
                    scmProductsList.add(menuProductData);
                }
            }
            
            response.put("scmProducts", scmProductsList);
            return response;
            
        } catch (Exception e) {
            LOG.error("Error getting detailed SCM products for menu product ID: {}, dimension: {}", menuProductId, dimension, e);
            throw new DataNotFoundException("Unable to fetch detailed SCM products for the given menu product: " + e.getMessage());
        }
    }


}
