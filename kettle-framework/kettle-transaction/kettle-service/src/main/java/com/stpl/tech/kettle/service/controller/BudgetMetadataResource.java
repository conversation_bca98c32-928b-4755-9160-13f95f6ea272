package com.stpl.tech.kettle.service.controller;

import com.stpl.tech.kettle.core.CalculationType;
import com.stpl.tech.kettle.core.budget.service.BudgetMetadataService;
import com.stpl.tech.kettle.core.data.budget.vo.BudgetDetail;
import com.stpl.tech.kettle.core.data.budget.vo.CalculationStatus;
import com.stpl.tech.kettle.core.data.budget.vo.ConsumableBudgetRequest;
import com.stpl.tech.master.core.service.AbstractExceptionHandler;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static com.stpl.tech.kettle.service.core.ServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.service.core.ServiceConstants.BUDGET_METADATA_ROOT_CONTEXT;
import static com.stpl.tech.kettle.service.core.ServiceConstants.SEPARATOR;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + BUDGET_METADATA_ROOT_CONTEXT) // 'v1/budget-metadata'
public class BudgetMetadataResource extends AbstractExceptionHandler {

    private static final Logger LOG = LoggerFactory.getLogger(BudgetMetadataResource.class);

    @Autowired
    private BudgetMetadataService budgetService;

    @RequestMapping(method = RequestMethod.GET, value = "budget-sheet")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public View getBudgetSheet() {
        LOG.info("Request to get overall budgets");
        return budgetService.getBudgetView();
    }

    @RequestMapping(method = RequestMethod.GET, value = "budget-sheet/actual")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public View getBudgetSheet(@RequestParam int year, @RequestParam int month) {
        LOG.info("Request to get overall budgets");
        return budgetService.getBudgetView(year, month);
    }

    @RequestMapping(method = RequestMethod.GET, value = "budget-sheet/manpower")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public View getManpowerBudgetSheet(@RequestParam int year, @RequestParam int month) {
        LOG.info("Request to get manpower budgets");
        return budgetService.getManpowerBudgetView(year, month);
    }

    @RequestMapping(method = RequestMethod.GET, value = "budget-sheet/channel-partner")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public View getChannelPartnerBudgetSheet(@RequestParam int year, @RequestParam int month) {
        LOG.info("Request to get channel partner charges budgets");
        return budgetService.getChannelPartnerChargesBudgetView(year, month);
    }

    @RequestMapping(method = RequestMethod.GET, value = "budget-sheet/bank-charges")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public View getBankChargesBudgetSheet(@RequestParam int year, @RequestParam int month) {
        LOG.info("Request to get bank charges budgets");
        return budgetService.getBankChargesBudgetView(year, month);
    }

    @RequestMapping(method = RequestMethod.GET, value = "budget-sheet/facility-charges")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public View getFacilityChargesBudgetSheet(@RequestParam int year, @RequestParam int month) {
        LOG.info("Request to get Facility charges budgets");
        return budgetService.getFacilityChargesBudgetView(year, month);
    }

    @RequestMapping(method = RequestMethod.GET, value = "budget-sheet/service-charges")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public View getServiceChargesBudgetSheet(@RequestParam int year, @RequestParam int month) {
        LOG.info("Request to get Services charges budgets");
        return budgetService.getServiceChargesBudgetView(year, month);
    }

    @RequestMapping(method = RequestMethod.POST, value = "upload-budgets", consumes = MediaType.MULTIPART_FORM_DATA)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void uploadDocument(HttpServletRequest request, @RequestParam(value = "file") final MultipartFile file, @RequestParam int userId)
        throws IOException {
        LOG.info("Request to upload budgets");
        budgetService.uploadBudgetDocument(file, userId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "upload-budgets/manpower", consumes = MediaType.MULTIPART_FORM_DATA)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void uploadManpowerBudgetDocument(HttpServletRequest request,
                                             @RequestParam(value = "file") final MultipartFile file, @RequestParam int userId) throws IOException {
        LOG.info("Request to upload manpower budgets");
        budgetService.uploadManpowerBudgetDocument(file, userId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "upload-budgets/channel-partner", consumes = MediaType.MULTIPART_FORM_DATA)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void uploadChannelPartnerChargesBudgetDocument(HttpServletRequest request,
                                                          @RequestParam(value = "file") final MultipartFile file, @RequestParam int userId) throws IOException {
        LOG.info("Request to upload channel partner charges budgets");
        budgetService.uploadChannelPartnerChargesBudgetDocument(file, userId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "upload-budgets/bank-charges", consumes = MediaType.MULTIPART_FORM_DATA)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void uploadBankChargesBudgetDocument(HttpServletRequest request,
                                                @RequestParam(value = "file") final MultipartFile file, @RequestParam int userId) throws IOException {
        LOG.info("Request to upload bank charges budgets");
        budgetService.uploadBankChargesBudgetDocument(file, userId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "upload-budgets/facility-charges", consumes = MediaType.MULTIPART_FORM_DATA)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void uploadFacilityChargesBudgetDocument(HttpServletRequest request,
                                                    @RequestParam(value = "file") final MultipartFile file, @RequestParam int userId) throws IOException {
        LOG.info("Request to upload Facility charges budgets");
        budgetService.uploadFacilityChargesBudgetDocument(file, userId);
    }


    @RequestMapping(method = RequestMethod.POST, value = "upload-budgets/service-charges", consumes = MediaType.MULTIPART_FORM_DATA)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void uploadServiceChargesBudgetDocument(HttpServletRequest request,
                                                   @RequestParam(value = "file") final MultipartFile file, @RequestParam int userId) throws IOException {
        LOG.info("Request to upload Service charges budgets");
        budgetService.uploadServiceChargesBudgetDocument(file, userId);
    }

    @RequestMapping(method = RequestMethod.GET, value = "pnl-units")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<Integer> getPnLUnits() {
        LOG.info("Request to get unit for budget calculation");
        Date d = AppUtils.getPreviousDate();
        return budgetService.getPnLUnits(d);
    }

    @RequestMapping(method = RequestMethod.GET, value = "pnl-units-by-date")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<Integer> getPnLUnitsForBusinessDate(@RequestParam String businessDate) {
        LOG.info("Request to get unit for budget calculation");
        Date d = AppUtils.parseDateSimple(businessDate);
        return budgetService.getPnLUnits(d);
    }

    @RequestMapping(method = RequestMethod.GET, value = "pnl-units-by-date/finalized")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<Integer> getFinalizedPnLUnitsForBusinessDate(@RequestParam String businessDate) {
        LOG.info("Request to get unit for budget calculation");
        Date d = AppUtils.parseDateSimple(businessDate);
        return budgetService.getFinalizedPnLUnits(d);
    }


    @RequestMapping(method = RequestMethod.POST, value = "save-pnl-units")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean savePnLUnitsForBusinessDate(@RequestBody List<BudgetDetail> budgetDetails) {
        LOG.info("Request to save PNL data from SUMO");
        return budgetService.savePnLData(budgetDetails, CalculationStatus.PENDING_SUMO_CALCULATION,
            CalculationType.CURRENT);
    }

    @RequestMapping(method = RequestMethod.POST, value = "save-pnl-units/finalized")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean saveFinalizedPnLUnitsForBusinessDate(@RequestBody List<BudgetDetail> budgetDetails) {
        LOG.info("Request to save Finalized PNL data from SUMO");
        return budgetService.savePnLData(budgetDetails, CalculationStatus.PENDING_FINALIZED_CALCULATION,
            CalculationType.FINALIZED);
    }

    @RequestMapping(method = RequestMethod.POST, value = "budget-check-RO")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Map<String, Map<String, BigDecimal>> applyBudgetConstraintOnRo(@RequestBody ConsumableBudgetRequest requestData) {
        LOG.info("Request to check budget for RO : " + requestData.getUnitId());
        return budgetService.applyBudgetConstraintRO(requestData);
    }
}
