package com.stpl.tech.kettle.service.controller;

import com.stpl.tech.kettle.core.service.CustomerFavChaiManagementService;
import com.stpl.tech.kettle.customer.service.CustomerInfoService;
import com.stpl.tech.kettle.customer.service.ReferralService;
import com.stpl.tech.kettle.data.model.*;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.CustomerCashPacketLogList;
import com.stpl.tech.kettle.domain.model.CustomerInfoDineIn;
import com.stpl.tech.kettle.domain.model.CustomerLoyaltyEntryList;
import com.stpl.tech.kettle.domain.model.CustomerWalletEntryList;
import com.stpl.tech.kettle.domain.model.EmailResponse;
import com.stpl.tech.kettle.domain.model.FavouriteChai;
import com.stpl.tech.kettle.service.model.FeedbackData;
import com.stpl.tech.kettle.service.model.ReferentsStatusResponse;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;
import lombok.Synchronized;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.ws.rs.core.MediaType;
import java.util.List;

import static com.stpl.tech.kettle.service.core.ServiceConstants.API_VERSION_V2;
import static com.stpl.tech.kettle.service.core.ServiceConstants.DINE_IN_RESOURCE_ROOT_CONTEXT;
import static com.stpl.tech.kettle.service.core.ServiceConstants.SEPARATOR;

@Controller
@RequestMapping(value = API_VERSION_V2 + SEPARATOR + DINE_IN_RESOURCE_ROOT_CONTEXT)
@Slf4j
public class DineInResource extends AbstractResources {

	private static final Logger LOG = LoggerFactory.getLogger(DineInResource.class);

	@Autowired
	private CustomerInfoService customerInfoService;

	@Autowired
	private CustomerFavChaiManagementService customerFavChaiManagementService;

	@Autowired
	private ReferralService referralService;
	@Autowired
	private MutexFactory<String> mutex;

	@RequestMapping(method = RequestMethod.POST, value = "customer/delete-customer-data", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public String deleteCustomerData(@RequestBody Integer customerId) throws DataNotFoundException {
		try{
			return customerInfoService.deleteCustomerData(customerId);
		} catch (DataNotFoundException e) {
			LOG.info("ERROR: ", e.toString());
			return "";
		}
	}

	@RequestMapping(method = RequestMethod.GET, value = "customer/loyalty-cash-wallet-info", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public CustomerInfoDineIn getCustomerInfoDineIn(@RequestParam("customerId") int customerId) {
		return customerInfoService.getCustomerInfoForDineIn(customerId);
	}

	@RequestMapping(method = RequestMethod.GET, value = "customer/wallet-ledger", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public CustomerWalletEntryList getWalletLedger(@RequestParam("customerId") int customerId,
			@RequestParam(value = "pageNo", defaultValue = "0") int pageNo,
			@RequestParam(value = "pageSize", defaultValue = "30") int pageSize) {
		return customerInfoService.getWalletLedger(customerId, pageNo, pageSize);
	}

	@RequestMapping(method = RequestMethod.GET, value = "customer/loyalty-ledger", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public CustomerLoyaltyEntryList getLoyaltyLedger(@RequestParam("customerId") int customerId,
			@RequestParam(value = "pageNo", defaultValue = "0") int pageNo,
			@RequestParam(value = "pageSize", defaultValue = "30") int pageSize) {
		CustomerLoyaltyEntryList li=customerInfoService.getLoyaltyLedger(customerId, pageNo, pageSize);
		return li;
	}

	@RequestMapping(method = RequestMethod.GET, value = "customer/chaayos-cash-ledger", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public CustomerCashPacketLogList getCashLedger(@RequestParam("customerId") int customerId,
			@RequestParam(value = "pageNo", defaultValue = "0") int pageNo,
			@RequestParam(value = "pageSize", defaultValue = "30") int pageSize) {
		return customerInfoService.getCashLedger(customerId, pageNo, pageSize);
	}

	@RequestMapping(method = RequestMethod.GET, value = "customer/chaayos-cash-expiry-ledger", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public CustomerCashPacketLogList getCashExpLedger(@RequestParam("customerId") int customerId,
			@RequestParam("dateString") String dateStr) {
		return customerInfoService.getCashExpLedger(customerId, AppUtils.getDate(dateStr, "yyyy-MM-dd"));
	}

	@RequestMapping(method = RequestMethod.GET, value = "customer/customerInfo-byId", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public Customer getCustomerInfoByCustomerId(@RequestParam("customerId") int customerId)
			throws DataNotFoundException {
		LOG.info("Get Customer By customerId {}", customerId);
		return customerInfoService.getCustomerInfo(customerId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "customer/update", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public String updateDetails(@RequestBody CustomerInfo customerInfo) throws DataUpdationException, DataNotFoundException {
		LOG.info("Got update request for name {} with customer Id {}", customerInfo.getFirstName(),
			customerInfo.getCustomerId());
		return customerInfoService.updateCustomerBasicDetail(customerInfo.getCustomerId(), customerInfo);
	}

	@RequestMapping(method = RequestMethod.POST, value = "customer/update-name", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public String updateName(@RequestBody CustomerInfo customerInfo) throws DataUpdationException, DataNotFoundException {
		LOG.info("Got update request for name {} with customer Id {}", customerInfo.getFirstName(),
				customerInfo.getCustomerId());
		return customerInfoService.updateCustomerBasicDetail(customerInfo.getCustomerId(), customerInfo);
	}

	@RequestMapping(method = RequestMethod.POST, value = "customer/update-email", consumes = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public EmailResponse updateAndVerifyEmail(@RequestBody CustomerInfo customerInfo)
			throws DataUpdationException, DataNotFoundException {
		LOG.info("Got update request for email {} with customer Id {}", customerInfo.getEmailId(),
				customerInfo.getCustomerId());
		if(!AppConstants.EXCLUDE_CUSTOMER_IDS.contains(customerInfo.getCustomerId())) {
			return customerInfoService.verifyCustomerEmailById(customerInfo.getCustomerId(), customerInfo.getEmailId());
		}
		return null;
	}

	@RequestMapping(method = RequestMethod.POST, value = "customer/verify-email", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public EmailResponse verifyEmail(@RequestBody EmailVerificationRequest emailVerificationResponse) throws DataUpdationException, DataNotFoundException {
		LOG.info("Got verify request for email {} with customer Id {}", emailVerificationResponse.getCustomerId());
		return customerInfoService.verifyCustomerEmail(emailVerificationResponse.getCustomerId(),emailVerificationResponse.getBrandId());
	}

	@RequestMapping(method = RequestMethod.POST, value = "customer/product-feedback", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean productFeedback(@RequestBody CustomerProductFeedback f)
			throws DataUpdationException, DataNotFoundException {
		LOG.info("Got feedback request for {}", f);
		if (AppUtils.isActive(f.getStatus())) {
			return customerInfoService.addCustomerProductFeedback(f.getCustomerId(), f.getProductId(), f.getRating(),
					f.getSourceId(), f.getSource());

		} else {
			return customerInfoService.removeCustomerProductFeedback(f.getCustomerId(), f.getProductId(),
					f.getSource());

		}
	}

	@RequestMapping(method = RequestMethod.POST, value = "customer/product-feedback/count", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public FeedbackData productFeedback(@RequestBody Integer customerId)
			throws DataUpdationException, DataNotFoundException {
		LOG.info("Got feedback data request for {}", customerId);
		FeedbackData data = new FeedbackData();
		data.setProducts(customerInfoService.getCustomerSpecificFeedbackCount(customerId, "APP"));
		return data;
	}

	@RequestMapping(method = RequestMethod.GET, value = "product-feedback/count", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public FeedbackData productFeedback() throws DataUpdationException, DataNotFoundException {
		LOG.info("Got product feedback data request");
		FeedbackData data = new FeedbackData();
		data.setProducts(customerInfoService.getProductSpecificFeedbackCount());
		return data;
	}

	@RequestMapping(method = RequestMethod.GET, value = "customer/referents", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public ReferentsStatusResponse getCustomerReferents(@RequestParam("customerId") int customerId) {
		LOG.info("Enter getCustomerReferents where customerId:: "+ customerId);
		ReferentsStatusResponse data = new ReferentsStatusResponse();
		data.setReferents(referralService.getCustomerReferents(customerId));
		LOG.info("data:: "+ JSONSerializer.toJSON(data));
		return data;
	}

	@RequestMapping(method = RequestMethod.GET, value = "get-active-customer-fav-chai", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<FavouriteChai> getCustomerActiveFavChai(@RequestParam("unitId") int unitId, @RequestParam int customerId) {
		LOG.info("Enter getCustomerActiveFavChai where customerId:: "+ customerId);
		try{
			return customerFavChaiManagementService.convertActiveFavChaiMappingsToFavChaiDineIN(customerId,unitId);
		}catch(Exception e){
			LOG.error("Exception while converting to favChaiDineIn objectfor customerId :{} and error is ::{}", customerId, e);
			return null;
		}
	}

	@RequestMapping(method = RequestMethod.POST, value = "save-customer-fav-chai", consumes = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean saveCustomerFavChai(@RequestBody FavouriteChai favouriteChai) {
		LOG.info("Enter saveCustomerActiveFavChai where customerId:: "+ favouriteChai.getCustomerId());
		try{
			return customerFavChaiManagementService.saveAndUpdateFavChaiFromDineIn(favouriteChai);
		}catch(Exception e){
			LOG.error("Exception while saving customer fav chai from dineIn for customerId :{} and exception is :{} ", favouriteChai.getCustomerId(), e);
			return false;
		}
	}

	@RequestMapping(method = RequestMethod.POST, value = "customer/update/profile", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public ProfileUpdateResponse updateDetailsAndGenerateCoupons(@RequestBody CustomerInfo customerInfo) throws DataUpdationException, DataNotFoundException {
		LOG.info("Got update request for profile update for name {} with customer Id {}", customerInfo.getFirstName(),
				customerInfo.getCustomerId());
		synchronized (mutex.getMutex(customerInfo.getContactNumber())) {
			return customerInfoService.updateCustomerBasicDetailAndGenerateCoupon(customerInfo.getCustomerId(), customerInfo);
		}
	}

}
