
/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.service.controller;

import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.core.cache.MetadataCache;
import com.stpl.tech.kettle.core.cache.OrderInfoCache;
import com.stpl.tech.kettle.core.notification.AutoDayCloseMailReceipt;
import com.stpl.tech.kettle.core.notification.AutoDayCloseNotification;
import com.stpl.tech.kettle.core.notification.ErrorNotification;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.core.service.CashManagementService;
import com.stpl.tech.kettle.core.service.ExpenseManagementService;
import com.stpl.tech.kettle.core.service.OrderManagementService;
import com.stpl.tech.kettle.core.service.OrderSearchService;
import com.stpl.tech.kettle.core.service.PosMetadataService;
import com.stpl.tech.kettle.core.service.PullTransferSettlementReasonService;
import com.stpl.tech.kettle.core.service.TableService;
import com.stpl.tech.kettle.core.service.UnitBudgetService;
import com.stpl.tech.kettle.customer.service.CardService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.converter.DataConverter;
import com.stpl.tech.kettle.data.dao.DeliveryDao;
import com.stpl.tech.kettle.data.model.AutoDayCloseVO;
import com.stpl.tech.kettle.data.model.PartnerAttributes;
import com.stpl.tech.kettle.data.model.UnitClosureDetails;
import com.stpl.tech.kettle.domain.model.ClosureState;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.reports.core.ReportOutput;
import com.stpl.tech.master.core.SCMServiceEndpoints;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.TaxDataCache;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.core.external.offer.service.OfferManagementExternalService;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.domain.model.UnitPartnerStatusVO;
import com.stpl.tech.master.domain.model.UnitStatus;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.endpoint.Endpoints;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@RestController
public class DayClosureResource extends AbstractMetadataResource {

	@Autowired
	private  PullTransferSettlementReasonService pullSettlementService;

	@Autowired
	private PosMetadataService posMetadataService;
	@Autowired
	private OrderManagementService orderManagementService;
	@Autowired
	private UnitBudgetService unitBudgetService;
	@Autowired
	private OrderSearchService orderSearchService;
	@Autowired
	private MetadataCache cache;
	@Autowired
	private MasterDataCache masterCache;
	@Autowired
	private TaxDataCache taxCache;
	@Autowired
	private EnvironmentProperties props;
	@Autowired
	private SalesReportResource reportResource;
	@Autowired
	private OrderInfoCache ordersCache;
	@Autowired
	private CashManagementService cashService;
	@Autowired
	private OfferManagementExternalService offerService;
	@Autowired
	private ExpenseManagementService expenseService;
	@Autowired
	private ItemConsumptionHelper helper;
	@Autowired
	private MenuItemConsumptionHelper menuHelper;
	@Autowired
	private TableService tableService;
	@Autowired
	private DeliveryDao deliveryDao;

	@Autowired
	private CardService cardService;

	private static final Logger LOG = LoggerFactory.getLogger(DayClosureResource.class);

	@Scheduled(cron = "0 45 04 * * *", zone = "GMT+05:30")
	@PostMapping("auto/dayClose")
	public void autoDayClose() throws EmailGenerationException, DataUpdationException, DataNotFoundException {
		if (!props.getRunAutoDayClose()) {
			LOG.info("Auto Day Close Cancelled...");
			return;
		}
		LOG.info("Initiating Auto Day Close...");

		Date businessDate = AppUtils.getPreviousDateIST();
		List<AutoDayCloseVO> incorrectDayCloses = new ArrayList<>();
		List<AutoDayCloseVO> dayCloseDefaulters = new ArrayList<>();
		List<AutoDayCloseVO> dayCloseFailures = new ArrayList<>();

		// Get All Closures
		List<UnitClosureDetails> unitClousureDetails = posMetadataService.getClosureFromBusinessDate(businessDate);

		// Get incorrect Closure
		for (UnitClosureDetails unitClosureDetail : unitClousureDetails) {
			if (!unitClosureDetail.getBusinessDate().equals(businessDate)) {
				LOG.info("Incorrect Day Close for unit {}" , unitClosureDetail.getUnitId());
				incorrectDayCloses.add(new AutoDayCloseVO(masterCache.getUnitBasicDetail(unitClosureDetail.getUnitId()),
						masterCache.getEmployee(unitClosureDetail.getEmployeeId()), unitClosureDetail, businessDate));
				posMetadataService.updateDayCloseDate(unitClosureDetail.getClosureId(), businessDate);
			}
		}
		List<Integer> failedUnitDayClose = new ArrayList<>();
		List<Integer> successfulUnitDayClose = new ArrayList<>();
		for (UnitBasicDetail unit : masterCache.getAllUnits()) {
			int unitId = unit.getId();
			if (TransactionUtils.isActiveUnit(unit.getStatus())
					&& unit.isLive()
					&& !(posMetadataService.isDayClosed(unitId, businessDate))) {
				LOG.info("Day close for unit: {}", unitId);
				try {
					// this marks orders as SETTLED
					settlePendingOrders(unitId);
					if(Boolean.TRUE.equals(masterCache.getUnit(unitId).getIsTestingUnit())) {
						orderManagementService.cancelPendingOrdersOfTestingUnits(unitId);
					}
					dayClose(unitId, 100000, businessDate, "Auto Closure By System", props, null, null);
					dayCloseDefaulters.add(new AutoDayCloseVO(masterCache.getUnitBasicDetail(unitId), businessDate));
					if (!AppUtils.isUnitEligibleForDayClosureNotification(unit.getName())) {
						successfulUnitDayClose.add(unit.getId());
					}
				} catch (Exception e) {
					LOG.error("Error in closing day for unit #{}", unitId, e);
					dayCloseFailures.add(new AutoDayCloseVO(masterCache.getUnitBasicDetail(unitId), businessDate,
							e.getMessage() + " " + e.toString()));
					if (unit.isLive() && unit.getCategory().equals(UnitCategory.CAFE)
							&& unit.getStatus().equals(UnitStatus.ACTIVE)
							&& !AppUtils.isUnitEligibleForDayClosureNotification(unit.getName())) {
						failedUnitDayClose.add(unit.getId());
						String msg = "Unit Operations are shut down due to pending day close on Kettle ("+ unitId + ":::" + unit.getName()+")";
						SlackNotificationService.getInstance().sendNotification(props.getEnvironmentType(), null, SlackNotification.DAY_CLOSURE_STATUS, msg);
						SlackNotificationService.getInstance().sendNotification(props.getEnvironmentType(), null, unit.getCafeManagerId()+"_notify", msg);
						SlackNotificationService.getInstance().sendNotification(props.getEnvironmentType(), null, unit.getUnitManagerId()+"_notify", msg);
					}
				}
			}
		}
//		 Send Email Notification
		new AutoDayCloseNotification(new AutoDayCloseMailReceipt(incorrectDayCloses, dayCloseDefaulters,
				dayCloseFailures, businessDate, props.getBasePath()), props).sendEmail();
		LOG.info("calling SP_INSERT_SALES_DATA procedure ::::::::::::::::::::::::::");
		posMetadataService.updateSalesData(businessDate);
		LOG.info("calling SP_INSERT_SALES_DATA procedure ::::::::::::::::::::::::::");
		posMetadataService.updateDailySalesData(businessDate);
		LOG.info("calling ITEM_CONSUMPTION_ESTIMATE_CALCULATE procedure ::::::::::::::::::::::::::");
		posMetadataService.updateConsumptionEstimates(businessDate);
		try {
			generateReportsPostDayClose(businessDate);
		} catch (IOException e) {
			LOG.error("Error in generating post closure reports ", e);
		}
		try {
			generatePnLReports(businessDate);
		} catch (Exception e) {
			LOG.error("Error in generating PnL Reports for business date " + businessDate, e);
			new ErrorNotification("Error in generating PnL Report", "Error in generating PnL Report After Day Close", e,
					props.getEnvironmentType()).sendEmail();
		}
		try {
			writeDataToDSR(businessDate);
		} catch (IOException e) {
			LOG.error("Error in Sending Data To DSR ", e);
			new ErrorNotification("Error in Sending Data To DSR", "Error in Sending Data To DSR After Day Close", e,
					props.getEnvironmentType()).sendEmail();
		}
		if(!failedUnitDayClose.isEmpty()){
			postRequestToInValidateCache(failedUnitDayClose);
			postRequestToTogglePartnerOff(failedUnitDayClose, AppConstants.CHAAYOS_BRAND_ID);
			postRequestToTogglePartnerOff(failedUnitDayClose, AppConstants.GNT_BRAND_ID);
		}
		verifySumoDayClose(successfulUnitDayClose);
	}

	private void postRequestToInValidateCache(List<Integer> failedUnitDayClose) {
		try {
			LOG.info("In Validating User Session for {} Unit ", failedUnitDayClose.size());
			WebServiceHelper.postRequestWithAuthInternal(getEnvironmentProperties().getMasterBasePath() +
							Endpoints.IN_VALIDATE_USER_SESSION, getEnvironmentProperties().getKettleClientToken(),
					failedUnitDayClose);
		} catch (Exception e) {
			LOG.error("Exception caught while In Validating User Session for {} Unit ::: ",failedUnitDayClose.size(),e);
		}
	}

	private void verifySumoDayClose(List<Integer> successfulUnitDayClose) {
		try {
			LOG.info("Requesting SUMO to verify {} unit successful day closes ", successfulUnitDayClose.size());
			WebServiceHelper.postRequestWithAuthInternal(getEnvironmentProperties().getSCMServiceBasePath()
					+ SCMServiceEndpoints.VERIFY_DAY_CLOSES, getEnvironmentProperties().getKettleClientToken(), successfulUnitDayClose);
		} catch (Exception e) {
			LOG.error("Error while requesting sumo to verify {} unit day closes ::: ", successfulUnitDayClose, e);
		}
	}

	private void postRequestToTogglePartnerOff(List<Integer> failedUnitDayClose, int brandId) {
		try {
			LOG.info("Toggle {} Unit off on partner", failedUnitDayClose.size());
			WebServiceHelper.postRequestWithAuthInternal(getEnvironmentProperties().getKnockBaseUrl()+
							Endpoints.PARTNER_UNIT_TOGGLE_OFF, getEnvironmentProperties().getKnockMasterToken(),
					UnitPartnerStatusVO.builder()
							.partnerIds(Arrays.asList(AppConstants.CHANNEL_PARTNER_ZOMATO, AppConstants.CHANNEL_PARTNER_SWIGGY))
							.brandId(brandId)
							.status(false)
							.unitIds(failedUnitDayClose));
		} catch (Exception e) {
			LOG.error("Exception caught while toggling off the {} units on partner for brand Id {} ::: ",failedUnitDayClose.size(),brandId,e);
		}
	}

	private void settlePendingOrders(int unitId) throws DataNotFoundException, TemplateRenderingException {
		// check if orders are pending
		List<OrderInfo> pendingOrders = ordersCache.getOrders(unitId);
		if (pendingOrders == null || pendingOrders.size() == 0) {
			// No pending orders
			return;
		}
		for (OrderInfo info : pendingOrders) {
			if (!OrderStatus.SETTLED.equals(info.getOrder().getStatus())
					&& !OrderStatus.CANCELLED.equals(info.getOrder().getStatus())) {

				// refund sent as null for no action
				if (OrderStatus.CANCELLED_REQUESTED.equals(info.getOrder().getStatus())) {
					orderManagementService.updateOrderStatusInCache(info.getOrder().getOrderId(), OrderStatus.CANCELLED,
							AppConstants.SYSTEM_EMPLOYEE_ID, unitId, UnitCategory.fromValue(info.getOrder().getSource()),
							"Auto cancelled by system", null, null, null);
				} else if (OrderStatus.CREATED.equals(info.getOrder().getStatus())) {
					orderManagementService.updateOrderStatusInCache(info.getOrder().getOrderId(), OrderStatus.PROCESSING,
							AppConstants.SYSTEM_EMPLOYEE_ID, unitId, UnitCategory.fromValue(info.getOrder().getSource()),
							"Auto processed by system", null, null, null);
					// settle order after processing.
					settleOrdersInAutoDayClose(info, unitId);
				} else {
					settleOrdersInAutoDayClose(info, unitId);
				}
			}
		}
	}

	public void settleOrdersInAutoDayClose(OrderInfo info, int unitId) {
		orderManagementService.updateOrderStatusInCache(info.getOrder().getOrderId(), OrderStatus.SETTLED,
				AppConstants.SYSTEM_EMPLOYEE_ID, unitId, UnitCategory.fromValue(info.getOrder().getSource()),
				"Auto settled by system", null, null, null);
		if (info.getDeliveryDetails()
				.getDeliveryPartnerId() == AppConstants.DELIVERY_PARTNER_CHAAYOS_DELIVERY) {
			orderManagementService.updateOrderStatusInCache(info.getOrder().getOrderId(),
					OrderStatus.DELIVERED, 120056, unitId,
					UnitCategory.fromValue(info.getOrder().getSource()), "Auto settled by system", null,
					null, null);
		}
	}


	@Scheduled(cron = "0 30 05 * * *", zone = "GMT+05:30")
	public void clearMissionGaramChaiCache() throws DataNotFoundException {
		LOG.info("refreshing mission chai cache");
		orderManagementService.loadPartnerOrderCache();
	}

	// @Scheduled(fixedRate = 120000)
	@Scheduled(cron = "0 30 05 * * *", zone = "GMT+05:30")
	public void autoProductConsumptionReport() throws DataNotFoundException, IOException, EmailGenerationException {
		LOG.info("Generating Consolidated Consumption Report");
		if (props.getRunAutoDayClose()) {
			try {
				generateConsumptionReport(AppUtils.getPreviousDateIST());
			} catch (Exception e) {
				LOG.error("ERROR while generating Consolidated Product Consumption Report for {}",
						AppUtils.getPreviousDateIST(), e);
				new ErrorNotification("Consolidated Product Consumption Report",
						"ERROR while generating Consolidated Product Consumption Report", e, props.getEnvironmentType())
								.sendEmail();
				throw e;
			}
		} else {
			LOG.info("Consolidated Product Consumption Report generation Cancelled as run.auto.dayclose value is {}",
					props.getRunAutoDayClose());
		}
	}

	@Scheduled(cron = "0 30 9 * * *", zone = "GMT+05:30")
	public void sendExternlReports() throws DataNotFoundException, IOException, EmailGenerationException {
		LOG.info("Uploading External Reports to Partners Consumption Report");
		// Get All Closures
		List<UnitClosureDetails> unitClousureDetails = posMetadataService
				.getClosureForBusinessDate(AppUtils.getPreviousBusinessDate());
		// Send Reports
		for (UnitClosureDetails unitClosureDetail : unitClousureDetails) {
			try {
				List<PartnerAttributes> attributeList = deliveryDao
						.getPartnerAttributeList(unitClosureDetail.getUnitId(), "SALES_REPORT");
				if (attributeList == null || attributeList.isEmpty()) {
					// EXIT IF NO Attributes
					continue;
				}
				Map<String, String> configMap = getConfigMap(attributeList);
				if (!AppConstants.ACTIVE.equals(configMap.get("STATUS"))) {
					LOG.info("CANCELLED Sending external report for unit:{}", unitClosureDetail.getUnitId());
					continue;
				}
				LOG.info("Processing external report for #" + unitClosureDetail.getUnitId());
				processExternalReports(DataConverter.convert(unitClosureDetail));
			} catch (Exception e) {
				LOG.error("Error while sending external report", e);
			}
		}
	}

	private Map<String, String> getConfigMap(List<PartnerAttributes> attributeList) {
		Map<String, String> configMap = new HashMap<String, String>();
		for (PartnerAttributes attribute : attributeList) {
			configMap.put(attribute.getMappingType(), attribute.getMappingValue());
		}
		return configMap;
	}

	// @Scheduled(fixedRate = 120000)
	@Scheduled(cron = "0 0 0/1 1/1 * *", zone = "GMT+05:30")
	public void intraDayExternalSalesReport() throws DataNotFoundException, IOException, EmailGenerationException {
		LOG.info("Generating Hourly Intra Day Sales Report");
		if (props.getRunIntraDayExternalReport()) {
			try {
				reportResource.generateHourlyTerminalSalesReport();
			} catch (Exception e) {
				LOG.error("ERROR while generating Intra Day External Sales Report {}", AppUtils.getPreviousDateIST(),
						e);
				new ErrorNotification("Intra Day External Sales Report",
						"ERROR while generating Intra Day External Sales Report {}t", e, props.getEnvironmentType())
								.sendEmail();
				throw e;
			}
		} else {
			LOG.info("Intra Day External Sales Report Cancelled as run.intraday.external.reports value is {}",
					props.getRunIntraDayExternalReport());
		}
	}

	// @Scheduled(fixedRate = 120000)
	@Scheduled(cron = "0 */15 0,1,2,3,4,17,18,19,20,21,22,23 * * *", zone = "GMT+05:30")
	public void generateReportsPostDayClose() throws DataNotFoundException, IOException, EmailGenerationException {
		super.generateReportsPostDayClose();
	}

	// @Scheduled(fixedRate = 120000)
	@Scheduled(cron = "0 0 9 * * *", zone = "GMT+05:30")
	public void generateMTDPnL() throws DataNotFoundException, IOException, EmailGenerationException {
		super.generateMTDPnL();

	}

	@Override
	public PosMetadataService getMetadataService() {
		return posMetadataService;
	}

	@Override
	public MetadataCache getMetadataCache() {
		return cache;
	}

	@Override
	public OrderManagementService getOrderManagementService() {
		return orderManagementService;
	}

	@Override
	public EnvironmentProperties getEnvironmentProperties() {
		return props;
	}

	@Override
	public List<ReportOutput> processExternalReports(int unitId, List<Order> orders, Date businessDate) {
		return reportResource.generateTerminalSalesReport(getMasterDataCache().getUnit(unitId), orders, businessDate);
	}

	@Override
	public MasterDataCache getMasterDataCache() {
		return masterCache;
	}

	@Override
	public ItemConsumptionHelper getItemConsumptionHelper() {
		return helper;
	}

	@Override
	public CashManagementService getCashManagementService() {
		return cashService;
	}

	@Override
	public PullTransferSettlementReasonService getPullTransferSettlementReasonService(){
		return pullSettlementService;
	}
	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.kettle.service.controller.AbstractMetadataResource#
	 * getOrderSearchService()
	 */
	@Override
	public OrderSearchService getOrderSearchService() {
		return orderSearchService;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.stpl.tech.kettle.service.controller.AbstractMetadataResource#
	 * getTaxDataCache()
	 */
	@Override
	public TaxDataCache getTaxDataCache() {
		return taxCache;
	}

	public UnitBudgetService getUnitBudgetService() {
		return unitBudgetService;
	}

	public ExpenseManagementService getExpenseService() {
		return expenseService;
	}

	@Override
	public MenuItemConsumptionHelper getMenuItemConsumptionHelper() {
		return menuHelper;
	}

	@Override
	public TableService getTableService() {
		return tableService;
	}

	@Override
	public OfferManagementExternalService getOfferManagementExternalService() {
		return offerService;
	}

	@Override
	public CardService getCardService() {
		return cardService;
	}
}
