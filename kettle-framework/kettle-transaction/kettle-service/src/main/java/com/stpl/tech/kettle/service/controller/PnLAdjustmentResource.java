package com.stpl.tech.kettle.service.controller;

import static com.stpl.tech.kettle.service.core.ServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.service.core.ServiceConstants.PNL_ADJUSTMET_ROOT_CONTEXT;
import static com.stpl.tech.kettle.service.core.ServiceConstants.SEPARATOR;

import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.persistence.Column;
import javax.ws.rs.core.MediaType;

import com.stpl.tech.kettle.core.data.budget.vo.ExpenseField;
import com.stpl.tech.kettle.core.service.PnLAdjustmentService;
import com.stpl.tech.kettle.data.model.PnlAdjustmentDetail;
import com.stpl.tech.kettle.domain.model.PnlAdjustment;
import com.stpl.tech.kettle.domain.model.PnlAdjustmentRequest;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.master.domain.model.ListData;
import com.stpl.tech.master.domain.model.PnlAdjustmentStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import com.stpl.tech.kettle.data.model.UnitExpenditureDetail;
import com.stpl.tech.kettle.domain.model.AdjustmentFieldData;
import com.stpl.tech.kettle.domain.model.AdjustmentFieldsResponse;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + PNL_ADJUSTMET_ROOT_CONTEXT) // 'v1/pnl-adjustment'
public class PnLAdjustmentResource extends AbstractResources {

    private static final Logger LOG = LoggerFactory.getLogger(PnLAdjustmentResource.class);


    @Autowired
    private PnLAdjustmentService pnLAdjustmentService;


    @RequestMapping(method = RequestMethod.GET, value = "adjustment-fields")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public AdjustmentFieldsResponse getAdjustmentFields(@RequestParam String type) {
        LOG.info("Request to get all adjustment fields for {}", type);
        return pnLAdjustmentService.getAdjustmentFields(type);
    }


    @RequestMapping(method = RequestMethod.POST, value = "create-adjustment")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public PnlAdjustment createPnLAdjustment(@RequestBody PnlAdjustmentDetail adjustmentDetail) {
        LOG.info("creating pnl adjustment for type {}", adjustmentDetail.getPnlHeaderName());
        return pnLAdjustmentService.createPnLAdjustment(adjustmentDetail);
    }


    @RequestMapping(method = RequestMethod.POST, value = "update-adjustment-status", produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public PnlAdjustment updatePnLAdjustmentStatus(@RequestBody PnlAdjustmentDetail pnlAdjustmentDetail, @RequestParam String status) {
        LOG.info("updating status of adjustment with id {} from {} to {}", pnlAdjustmentDetail.getAdjustmentId(), pnlAdjustmentDetail.getStatus(), status);
        return pnLAdjustmentService.updatePnLAdjustmentStatus(pnlAdjustmentDetail, PnlAdjustmentStatus.valueOf(status));
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-adjustment-reasons")
    public Map<String, ListData> getAdjustmentReasons() throws DataNotFoundException {
        LOG.info("getting adjustment reasons");
        return pnLAdjustmentService.getAdjustmentReasons();
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-adjustments")
    public List<PnlAdjustment> getAdjustments(@RequestBody PnlAdjustmentRequest pnlAdjustmentRequest) throws DataNotFoundException {
        return pnLAdjustmentService.getAdjustments(PnlAdjustmentStatus.valueOf(pnlAdjustmentRequest.getStatus()),
                pnlAdjustmentRequest.getUnitId(), pnlAdjustmentRequest.getMonth(), pnlAdjustmentRequest.getYear());
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-adjustment-impact")
    public List<PnlAdjustment> getAdjustmentImpact(@RequestBody PnlAdjustmentRequest pnlAdjustmentRequest) throws DataNotFoundException {
        return pnLAdjustmentService.getAdjustmentImpact(pnlAdjustmentRequest.getPnlHeaderName(), pnlAdjustmentRequest.getUnitId(),
                pnlAdjustmentRequest.getAdjustmentValue(), pnlAdjustmentRequest.getMonth(), pnlAdjustmentRequest.getYear());
    }


}
