/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.service.report;

import com.stpl.tech.kettle.reports.model.ProductDimensionConsumption;
import com.stpl.tech.kettle.reports.model.ProductPriceConsumption;
import com.stpl.tech.kettle.reports.model.ProductSourceConsumption;
import com.stpl.tech.util.notification.AbstractTemplate;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.util.AppUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ItemConsumptionReceipt extends AbstractTemplate {
	private Unit unit;
	private List<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>> productConsumptions;
	private String basePath;
	private String generationTime = AppUtils.getCurrentTimeISTString();

	public ItemConsumptionReceipt(String basePath, Unit unit,
			List<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>> productConsumptions) {
		this.unit = unit;
		this.productConsumptions = productConsumptions;
		this.basePath = basePath;
	}

	@Override
	public String getTemplatePath() {
		return "template/ItemConsumptionReceipt.html";
	}

	public String getFilepath() {
		return basePath + "/" + unit.getId() + "/reports/ItemConsumptionReceipt-" + unit.getName() + "-"
				+ AppUtils.getCurrentTimeISTStringWithNoColons() + ".html";
	}

	public Map<String, Object> getData() {
		// Build the data-model
		Map<String, Object> data = new HashMap<String, Object>();
		data.put("products", productConsumptions);
		data.put("unitName", unit.getName());
		data.put("reportName", "Item Consumption Report");
		data.put("generationTime", generationTime);
		return data;
	}

}
