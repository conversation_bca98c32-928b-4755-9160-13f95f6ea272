/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.service.controller;

import com.stpl.tech.kettle.core.cache.MetadataCache;
import com.stpl.tech.kettle.core.cache.OrderInfoCache;
import com.stpl.tech.kettle.core.data.vo.FeedbackTokenInfo;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.core.service.CustomerOfferManagementService;
import com.stpl.tech.kettle.core.service.DeliveryRequestService;
import com.stpl.tech.kettle.core.service.OrderManagementService;
import com.stpl.tech.kettle.core.service.OrderSearchService;
import com.stpl.tech.kettle.core.service.PosMetadataService;
import com.stpl.tech.kettle.core.service.SubscriptionManagementService;
import com.stpl.tech.kettle.core.service.UnitInventoryManagementService;
import com.stpl.tech.kettle.customer.service.CardService;
import com.stpl.tech.kettle.customer.service.CustomerService;
import com.stpl.tech.kettle.customer.service.FeedbackManagementService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.model.SubscriptionEventDetail;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.SubscriptionEventStatus;
import com.stpl.tech.master.core.external.acl.service.TokenService;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.notification.service.FirebaseNotificationService;
import com.stpl.tech.master.core.external.offer.service.OfferManagementExternalService;
import com.stpl.tech.master.core.external.offer.service.OfferManagementService;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

@Component
public class SubscriptionResource extends AbstractOrderResource {

	private static final Logger LOG = LoggerFactory.getLogger(OrderManagementResources.class);

	@Autowired
	private OrderManagementService orderDao;
	@Autowired
	private OrderSearchService orderSearchService;
	
	@Autowired
	private OfferManagementService offer;

	@Autowired
	private CustomerService customerService;

	@Autowired
	private MetadataCache cache;

	@Autowired
	private MasterDataCache masterCache;

	@Autowired
	private EnvironmentProperties props;

	@Autowired
	private OrderInfoCache ordersCache;

	@Autowired
	private UnitInventoryManagementService unitService;

	@Autowired
	private SubscriptionManagementService subscriptionService;

	@Autowired
	private CustomerOfferManagementService customerOfferService;

	private OfferManagementExternalService offerService;

	@Autowired
	private DeliveryRequestService deliveryService;

	@Autowired
	private FirebaseNotificationService fcmService;

	@Autowired
	private FeedbackManagementService feedbackManagementService;

	@Autowired
	private TokenService<FeedbackTokenInfo> tokenService;

	@Autowired
	private PosMetadataService posMetadataService;
	//@Scheduled(cron = "0 */2 8-19 * * *", zone = "GMT+05:30")
	// @Scheduled(cron = "*/20 * 8-19 * * *", zone = "GMT+05:30")
	public void createSubscriptionOrders() throws InterruptedException {
		Date currentTimestamp = AppUtils.getCurrentTimestamp();
		List<SubscriptionEventDetail> list = null;
		LOG.info(String.format("Started subscription thread for time %s ", currentTimestamp));
		list = subscriptionService.getOrdersToBeCreated(currentTimestamp);
		if (list != null) {
			for (SubscriptionEventDetail data : list) {
				try {
					Order order = subscriptionService.getOrder(data.getSubscriptionEventDetailId());
					OrderInfo orderDetail = createOrder(order, true, false,null);
					LOG.info(String.format("Created Order for subscription %d with productId %d ",
							data.getSubscriptionDetail().getSubscriptionId(), orderDetail.getOrder().getOrderId()));
					boolean updated = subscriptionService.updateEventStatus(data.getSubscriptionEventDetailId(),
							orderDetail.getOrder().getOrderId(), SubscriptionEventStatus.SUCCESS);
					if (updated) {
						LOG.info(String.format(
								"Updated the status of the subscription event with event productId %d , subscription productId %d and order productId %d",
								data.getSubscriptionEventDetailId(), data.getSubscriptionDetail().getSubscriptionId(),
								order.getOrderId()));
					} else {
						subscriptionService.updateEventStatus(data.getSubscriptionEventDetailId(),
								orderDetail.getOrder().getOrderId(), SubscriptionEventStatus.FAILED);
						LOG.error(String.format(
								"Marked subscription with productId %d and and event detail productId %d and event productId %d for the time %s: as failed",
								data.getSubscriptionDetail().getSubscriptionId(), data.getSubscriptionEventDetailId(),
								data.getEventId(), currentTimestamp));
					}
				} catch (Exception e) {
					LOG.error("Error in creating order with subscriptionId : "
							+ data.getSubscriptionDetail().getSubscriptionId(), e);
					if (data.getRetryCount() < 5) {
						SubscriptionEventDetail clone = subscriptionService
								.cloneEventStatus(data.getSubscriptionEventDetailId(), SubscriptionEventStatus.CREATED);
						LOG.error(String.format(
								"Created a clone event for subscription with productId %d and and event detail productId %d and event productId %d for the time %s: ",
								clone.getSubscriptionDetail().getSubscriptionId(), clone.getSubscriptionEventDetailId(),
								clone.getEventId(), currentTimestamp), e);
					} else {
						subscriptionService.updateEventStatus(data.getSubscriptionEventDetailId(), null,
								SubscriptionEventStatus.FAILED);
						LOG.error(String.format(
								"Marked subscription with productId %d and and event detail productId %d and event productId %d for the time %s: as failed",
								data.getSubscriptionDetail().getSubscriptionId(), data.getSubscriptionEventDetailId(),
								data.getEventId(), currentTimestamp), e);
					}
				}

			}
		}
	}

	//@Scheduled(cron = "0 0 6-20 * * *", zone = "GMT+05:30")
	// @Scheduled(cron = "*/20 * 8-19 * * *", zone = "GMT+05:30")
	public void createSunscriptionEvents() throws InterruptedException {
		LOG.info("Starting to take subscriptions off hold for today");
		subscriptionService.takeSubscriptionsOffHoldForToday();
		LOG.info("Starting to put subscriptions on hold for today");
		subscriptionService.putSubscriptionsOnHoldForToday();
		LOG.info("Starting to cancel subscriptions starting today");
		subscriptionService.cancelSubscriptionsForToday();
		LOG.info("Starting to create all subscription orders for today ");
		subscriptionService.createAllSubscriptionsForToday();
	}

	@Override
	public MetadataCache getMetadataCache() {
		return cache;
	}

	@Override
	public OrderManagementService getOrderManagementService() {
		return orderDao;
	}

	@Override
	public EnvironmentProperties getEnvironmentProperties() {
		return props;
	}

	@Override
	public OrderInfoCache getOrderInfoCache() {
		return ordersCache;
	}

	@Override
	public CustomerService getCustomerService() {
		return customerService;
	}

	@Override
	public UnitInventoryManagementService getUnitInventoryManagementService() {
		return unitService;
	}

	@Override
	public CustomerOfferManagementService getCustomerOfferManagementService() {
		return customerOfferService;
	}

	@Override
	public DeliveryRequestService getDeliveryRequestService() {

		return deliveryService;
	}

	@Override
	public MasterDataCache getMasterDataCache() {
		return masterCache;
	}

	@Override
	public OfferManagementExternalService getOfferManagementService() {
		return offerService;
	}

	@Override
	public CardService getCardService() {
		return null;
	}

	@Override
	public FirebaseNotificationService getFirebaseNotificationService() {
		return fcmService;
	}

	@Override
	public FeedbackManagementService getFeedbackManagementService() {
		return feedbackManagementService;
	}

	@Override
	public TokenService<FeedbackTokenInfo> getTokenService() {
		return tokenService;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.stpl.tech.kettle.service.controller.AbstractOrderResource#
	 * getOrderSearchService()
	 */
	@Override
	public OrderSearchService getOrderSearchService() {
		return orderSearchService;
	}

	@Override
	public OfferManagementService getOfferService() {
		return offer;
	}

	@Override
	public PosMetadataService getPosMetadataService() {
		return posMetadataService;
	}
}
