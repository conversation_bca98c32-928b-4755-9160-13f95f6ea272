package com.stpl.tech.kettle.service.controller;

import com.stpl.tech.kettle.core.service.DroolsDecisionService;
import com.stpl.tech.kettle.data.model.DroolsDecisionTableData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import static com.stpl.tech.kettle.service.core.ServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.service.core.ServiceConstants.DROOL_RESOURCE;
import static com.stpl.tech.kettle.service.core.ServiceConstants.SEPARATOR;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + DROOL_RESOURCE)
@Slf4j
public class DroolsResource {

    @Autowired
    private DroolsDecisionService droolsDecisionService;

    @PostMapping( value = "reset-offer-decision-drool/{droolFileType}",consumes = MediaType.MULTIPART_FORM_DATA, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean reInitializeDroolContainer(@RequestBody MultipartFile file, @PathVariable String droolFileType) throws IOException {
        try {
            return droolsDecisionService.addNewDroolFile(file, droolFileType, true);
        }catch (Exception e){
            log.error("Error while re-initializing drool congif for file : {}", file.getName(), e);
        }
        return false;
    }

    @GetMapping( value = "download-decision-sheet/{droolFileType}")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void downloadDecisionSheet(HttpServletResponse response, @RequestParam String fileName, @PathVariable String droolFileType, @RequestParam String version) throws IOException {
        try {
            droolsDecisionService.downloadRecipeMedia(response, fileName, droolFileType, version);
        }catch (Exception e){
            log.error("Error while downloading file : {}", fileName, e);
        }
    }

    @GetMapping( value = "activate-version/{droolFileType}", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean downloadDecisionSheet( @RequestParam String fileName, @PathVariable String droolFileType, @RequestParam String version) throws IOException {
        try {
            return droolsDecisionService.activateVersion(fileName, droolFileType, version);
        }catch (Exception e){
            log.error("Error while downloading file : {}", fileName, e);
        }
        return false;
    }

    @GetMapping( value = "get-all-file", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<DroolsDecisionTableData> getAllFile(@RequestParam String fileType) throws IOException {
        try {
            return droolsDecisionService.fetchAllFileByType(fileType);
        }catch (Exception e){
            log.error("Error while fetching all file of type : {}", fileType, e);
        }
        return new ArrayList<>();
    }

    @PostMapping(value = "set-default-drool/{droolFileType}", produces = org.springframework.http.MediaType.APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean setDefaultDroolSheet(@PathVariable String droolFileType, @RequestParam String version, @RequestParam String fileName) throws IOException {
        try {
            log.error("Request to set DROOL TYPE ::::: {}, with VERSION :::: {} as default ", droolFileType,version);
            return droolsDecisionService.setDefaultDroolSheet(droolFileType, fileName, version);
        } catch (Exception e) {
            log.error("Error while setting drool sheet of type ::::: {}, with version :::: {} as default ", droolFileType,version, e);
        }
        return false;
    }


    @PostMapping(value = "inactive-version/{droolFileType}", produces = org.springframework.http.MediaType.APPLICATION_JSON_VALUE)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean inactivateVersion(@PathVariable String droolFileType, @RequestParam String version, @RequestParam String fileName) throws IOException {
        try {
            log.error("Request to in-active DROOL TYPE ::::: {}, with VERSION :::: {} ", droolFileType,version);
            return droolsDecisionService.inactivateVersion(droolFileType, fileName, version);
        } catch (Exception e) {
            log.error("Error while setting drool sheet of type ::::: {}, with version :::: {} as default ", droolFileType,version, e);
        }
        return false;
    }
}
