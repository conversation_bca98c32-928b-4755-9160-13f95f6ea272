package com.stpl.tech.kettle.service.controller;

import static com.stpl.tech.kettle.service.core.ServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.service.core.ServiceConstants.EXPENSE_MANAGEMENT_ROOT_CONTEXT;
import static com.stpl.tech.kettle.service.core.ServiceConstants.SEPARATOR;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.ws.rs.core.MediaType;

import com.stpl.tech.kettle.core.data.vo.PnlRecord;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.service.model.UserSessionDetail;
import com.stpl.tech.master.domain.model.IdNameList;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.servlet.View;

import com.stpl.tech.kettle.core.data.budget.vo.ElectricityStaticData.ElectricityBillType;
import com.stpl.tech.kettle.core.service.ExpenseManagementService;
import com.stpl.tech.kettle.core.service.UnitBudgetService;
import com.stpl.tech.kettle.data.model.UnitExpenditureDetail;
import com.stpl.tech.kettle.domain.model.ExpenseDetail;
import com.stpl.tech.kettle.domain.model.MeterDetail;
import com.stpl.tech.kettle.domain.model.UnitBudgetExceeded;
import com.stpl.tech.kettle.domain.model.UnitExpenditure;
import com.stpl.tech.kettle.service.model.ExpenseDetailRequest;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.MeterDetailEntryException;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.util.AppUtils;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + EXPENSE_MANAGEMENT_ROOT_CONTEXT)
public class ExpenseManagementResource extends AbstractResources {

    private static final Logger LOG = LoggerFactory.getLogger(ExpenseManagementResource.class);

    @Autowired
    private ExpenseManagementService expenseService;
    @Autowired
    private UnitBudgetService budgetService;

    @RequestMapping(method = RequestMethod.POST, value = "add-expense")
    @ResponseBody
    public ExpenseDetail addExpense(@RequestBody ExpenseDetail detail) {
        LOG.info("Adding expense of type : " + detail.getExpenseType() + " for unit : " + detail.getUnitId());
        return expenseService.addExpense(detail);
    }

    @RequestMapping(method = RequestMethod.POST, value = "add-expenses")
    @ResponseBody
    public boolean addExpenses(@RequestBody List<ExpenseDetail> detail) {
        LOG.info("Adding expenses of type : " + detail.get(0).getExpenseType());
        return expenseService.addExpenses(detail);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-expense", produces = MediaType.APPLICATION_JSON)
    @ResponseBody
    public List<ExpenseDetail> getExpenseDetail(@RequestBody ExpenseDetailRequest request) {
        LOG.info("get expense detail of unit id : " + request.getUnitId());
        return expenseService.getExpenseDetail(request.getUnitId(), request.getExpenseCategory(),
            request.getExpenseType(),
            request.getStartdate() != null && request.getStartdate().trim().length() > 0
                ? AppUtils.getDate(AppUtils.parseDate(request.getStartdate())) : null,
            request.getEndDate() != null && request.getEndDate().trim().length() > 0
                ? AppUtils.getDate(AppUtils.parseDate(request.getEndDate())) : null,
            request.getStatus());
    }

    @RequestMapping(method = RequestMethod.POST, value = "cancel-expense")
    @ResponseBody
    public boolean updateExpenseStatus(@RequestBody ExpenseDetail expenseDetail) {
        LOG.info("updating expense status :  " + expenseDetail.getId());
        return expenseService.updateExpenseStatus(expenseDetail);
    }

    @RequestMapping(method = RequestMethod.POST, value = "add-meter-reading")
    @ResponseBody
    public boolean addMeterReading(@RequestBody List<MeterDetail> details)
        throws DataNotFoundException, MeterDetailEntryException {
        LOG.info("Add meter reading detail for unit " + details.get(0).getUnitId());
        return expenseService.addMeterReading(details);
    }

    @RequestMapping(method = RequestMethod.POST, value = "last-meter-reading")
    @ResponseBody
    public List<Object[]> getLastMeterReading(@RequestBody int unitId) {
        LOG.info("Get last meter reading detail for unit " + unitId);
        return expenseService.getLastMeterReading(unitId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "MTD-PnL-for-unit")
    @ResponseBody
    public List<UnitExpenditure> getMTDPnlForUnit(@RequestBody HashMap<String, String> dataMap) {
        LOG.info("Get MTD PnL report " + dataMap);
        return budgetService.getMTDPnlForUnit(Integer.parseInt(dataMap.get("unitId")),
            dataMap.get("tillDate") != null && dataMap.get("tillDate").trim().length() > 0
                ? AppUtils.getDate(AppUtils.parseDate(dataMap.get("tillDate"))) : null);
    }

    @RequestMapping(method = RequestMethod.GET, value = "Meter-Reading-for-unit")
    @ResponseBody
    public Map<ElectricityBillType, Map<Integer, Integer>> getMeterReading(@RequestParam final Integer unitId,
                                                                           @RequestParam final Integer month, @RequestParam final Integer year) {
        LOG.info("Get Meter Reading for unit " + unitId);
        return expenseService.getMeterReading(unitId, month, year);
    }

    @RequestMapping(method = RequestMethod.POST, value = "budget-exceeded-datas")
    @ResponseBody
    public List<UnitBudgetExceeded> getBudgetExceededDetails(@RequestBody HashMap<String, String> dataMap) {
        LOG.info("getBudgetExceededDetails " + dataMap);
        return expenseService.getBudgetExceededDetails(Integer.parseInt(dataMap.get("unitId")),
            dataMap.get("notificationType"), dataMap.get("tillDate") != null && dataMap.get("tillDate").trim().length() > 0
                ? AppUtils.getDate(AppUtils.parseDate(dataMap.get("tillDate"))) : null);
    }

    @RequestMapping(method = RequestMethod.POST, value = "update-last-Reading")
    @ResponseBody
    public boolean updateLastMeterReading(@RequestBody List<MeterDetail> details) {
        LOG.info("Update Last Meter Reading for unit");
        return expenseService.updateLastMeterReading(details);
    }


    @RequestMapping(method = RequestMethod.POST, value = "last-meter-reading-list")
    @ResponseBody
    public List<MeterDetail> getLastMeterReadingList(@RequestBody MeterDetail detail) {
        LOG.info("Get last meter reading list for unit " + detail.getUnitId());
        return expenseService.getLastMeterDetailList(detail);
    }

    @RequestMapping(method = RequestMethod.POST, value = "unit-meter-reading-list")
    @ResponseBody
    public List<MeterDetail> getMeterReadingListByUnitId(@RequestBody int unitId) {
        LOG.info("Get All meter reading list for unit " + unitId);
        return expenseService.getAllMeterDetailList(unitId);
    }

    @RequestMapping(method = RequestMethod.POST, value = "reset-meter-readings")
    @ResponseBody
    public boolean resetMeterReadingForUnit(@RequestBody UserSessionDetail sessionDetail) {
        LOG.info("Update Last Meter Reading for unit");
        return expenseService.resetAllMeterReadingForUnit(sessionDetail);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-mtd-pnl-detail")
    @ResponseBody
    public View getMTDPnlDetailView(@RequestBody HashMap<String, String> dataMap) {
        return expenseService.getMTDPnlDetailView(Integer.parseInt(dataMap.get("unitId")), dataMap.get("tillDate") != null && dataMap.get("tillDate").trim().length() > 0
            ? AppUtils.getDate(AppUtils.parseDate(dataMap.get("tillDate"))) : null);
    }


    @RequestMapping(method = RequestMethod.POST, value = "get-mtd-pnl-details")
    @ResponseBody
    public List<UnitExpenditureDetail> getMTDPnlDetails(@RequestBody HashMap<String, String> dataMap) {
        LOG.info("Requesting MTD PNL data for unitId : {}, tillDate : {}", dataMap.get("unitId"),
            dataMap.get("tillDate"));
        return expenseService.getMTDPnlDetails(Integer.parseInt(dataMap.get("unitId")),
            dataMap.get("tillDate") != null && dataMap.get("tillDate").trim().length() > 0
                ? AppUtils.getDate(AppUtils.parseDate(dataMap.get("tillDate")))
                : null);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-finalized-pnl-detail")
    @ResponseBody
    public View getFinalizedPnlDetailView(@RequestBody HashMap<String, String> dataMap) {
        Integer unitId = Integer.parseInt(dataMap.get("unitId"));
        Date date = (dataMap.get("date") != null && dataMap.get("date").trim().length() > 0)
            ? AppUtils.getDate(AppUtils.parseDate(dataMap.get("date"))) : null;
        return expenseService.getFinalizedPnlDetailView(unitId, AppUtils.getMonth(date), AppUtils.getYear(date));
    }


    @RequestMapping(method = RequestMethod.POST, value = "get-pnl-representation")
    @ResponseBody
    public List<PnlRecord> getPnlRepresentation(@RequestBody HashMap<String, String> dataMap) throws DataUpdationException {
        Integer unitId = Integer.parseInt(dataMap.get("unitId"));
        Date date = (dataMap.get("tillDate") != null && dataMap.get("tillDate").trim().length() > 0)
            ? AppUtils.getDate(AppUtils.parseDate(dataMap.get("tillDate"))) : null;
            if (AppUtils.getLastDayOfMonth(date).compareTo(date) == 0 && budgetService.checkFinalizedPnlAggregateForUnitForMonth(unitId, AppUtils.getMonth(date), AppUtils.getYear(date)) != null) {
                return budgetService.getFinalizedPnlAggregateForUnitForMonth(unitId, AppUtils.getMonth(date), AppUtils.getYear(date));
            }

        return budgetService.getPnlRepresentationForUnit(unitId, date);
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-mtd-pnl-aggregate-detail")
    @ResponseBody
    public View getMTDPnlAggregateDetailView(@RequestBody HashMap<String, String> dataMap) {
        return expenseService.getMTDPnlAggregateDetailView(Integer.parseInt(dataMap.get("unitId")), dataMap.get("tillDate") != null && dataMap.get("tillDate").trim().length() > 0
            ? AppUtils.getDate(AppUtils.parseDate(dataMap.get("tillDate"))) : null);

    }


    @RequestMapping(method = RequestMethod.POST, value = "get-finalized-pnl-aggregate-detail")
    @ResponseBody
    public View getFinalizedPnlAggregateDetailView(@RequestBody HashMap<String, String> dataMap) {
        Integer unitId = Integer.parseInt(dataMap.get("unitId"));
        Date date = (dataMap.get("date") != null && dataMap.get("date").trim().length() > 0)
            ? AppUtils.getDate(AppUtils.parseDate(dataMap.get("date"))) : null;
        return expenseService.getFinalizedPnlAggregateDetailView(unitId, AppUtils.getMonth(date), AppUtils.getYear(date));
    }

    @RequestMapping(method = RequestMethod.POST, value = "get-mtd-pnl-aggregate-detail-DAM")
    @ResponseBody
    public View getMTDPnlAggregateDetailViewDAM(@RequestBody IdNameList dataMap) {
        return expenseService.getMTDPnlAggregateDetailViewDAM(dataMap.getList(), dataMap.getName() != null && dataMap.getName().trim().length() > 0
            ? AppUtils.getDate(AppUtils.parseDate(dataMap.getName())) : null);
    }

}