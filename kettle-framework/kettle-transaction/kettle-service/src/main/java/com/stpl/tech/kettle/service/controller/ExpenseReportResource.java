/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.service.controller;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.stpl.tech.kettle.core.ReportStatus;
import com.stpl.tech.kettle.core.cache.MetadataCache;
import com.stpl.tech.kettle.core.service.ReportingService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.domain.model.ExpenseUpdateEvent;
import com.stpl.tech.kettle.domain.model.IterationType;
import com.stpl.tech.kettle.reports.dao.impl.ExpenseReportData;
import com.stpl.tech.kettle.reports.dao.impl.ReportFileData;
import com.stpl.tech.kettle.reports.process.ExpenseReportController;
import com.stpl.tech.kettle.reports.process.ExpenseReportNotification;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.notification.AttachmentData;

@Component
public class ExpenseReportResource {

	private static final Logger LOG = LoggerFactory.getLogger(ExpenseReportResource.class);

	@Autowired
	private ReportingService reportingService;

	@Autowired
	private MetadataCache cache;

	@Autowired
	private EnvironmentProperties props;

	// @Scheduled(cron = "0 0 12 ? * TUE", zone = "GMT+05:30")
	public void autoGenerateWOWUnitExpenseReport()
			throws EmailGenerationException, DataUpdationException, DataNotFoundException {
		LOG.info("Generating WOW Unit Expense Report");
		if (props.getRunExpenseReport()) {
			generateUnitExpenseReport(IterationType.WOW, AppUtils.getCurrentDate(), "Auto Generated by System");
		} else {
			LOG.info("WOW Unit Expense Report generation Cancelled as run.auto.expense.report value is {}",
					props.getRunExpenseReport());
		}
	}

	// @Scheduled(cron = "0 15 12 3 1/1 ?", zone = "GMT+05:30")
	public void autoGenerateMOMUnitExpenseReport()
			throws EmailGenerationException, DataUpdationException, DataNotFoundException {
		LOG.info("Generating MOM Unit Expense Report");
		if (props.getRunExpenseReport()) {
			generateUnitExpenseReport(IterationType.MOM, AppUtils.getCurrentDate(), "Auto Generated by System");
		} else {
			LOG.info("MOM Unit Expense Report generation Cancelled as run.auto.expense.report value is {}",
					props.getRunExpenseReport());
		}
	}

	public void generateUnitExpenseReport(IterationType type, Date date, String addedBy)
			throws EmailGenerationException, DataUpdationException, DataNotFoundException {
		try {
			ExpenseUpdateEvent event = reportingService.loadExpenseReport(type, date, addedBy);
			if (ReportStatus.SUCCESS.name().equals(event.getStatus())) {
				ExpenseReportData expenseData = new ExpenseReportController(cache, props, reportingService)
						.execute(event);
				emailReport(expenseData);
			}
		} catch (Exception e) {
			LOG.error("Error while generating Unit Expense Report", e);
		}
	}

	private void emailReport(ExpenseReportData output)
			throws FileNotFoundException, EmailGenerationException, IOException {
		ExpenseReportNotification notification = new ExpenseReportNotification(output);
		List<AttachmentData> attachments = new ArrayList<AttachmentData>();
		for (ReportFileData fileData : output.getReportFiles()) {
			if (fileData.isGenerated()) {
				File file = new File(fileData.getFilePath());
				if (file.exists()) {
					AttachmentData reports = new AttachmentData(IOUtils.toByteArray(new FileInputStream(file)),
							fileData.getFileName(), fileData.getMimeType());
					attachments.add(reports);
					reportingService.uploadExpenseReport(fileData.getFilePath(), fileData.getFileName(),
							fileData.getMimeType(), output.getEvent());
				}
			}
		}
		notification.sendRawMail(attachments);
	}
}
