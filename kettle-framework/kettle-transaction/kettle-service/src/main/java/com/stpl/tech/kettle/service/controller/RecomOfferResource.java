package com.stpl.tech.kettle.service.controller;

import com.stpl.tech.kettle.core.service.DroolsDecisionService;
import com.stpl.tech.kettle.data.dao.GamifiedOfferDao;
import com.stpl.tech.kettle.data.model.DroolsCustomerProperties;
import com.stpl.tech.kettle.data.model.RecomOfferData;
import com.stpl.tech.util.AppConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.MediaType;
import java.io.IOException;

import static com.stpl.tech.kettle.service.core.ServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.service.core.ServiceConstants.RECOM_OFFER_RESOURCE;
import static com.stpl.tech.kettle.service.core.ServiceConstants.SEPARATOR;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + RECOM_OFFER_RESOURCE)
@Slf4j
public class RecomOfferResource {

    @Autowired
    private DroolsDecisionService droolsDecisionService;

    @Autowired
    private GamifiedOfferDao gamifiedOfferDao;

    @GetMapping( value = "get-offer-data", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public RecomOfferData getRecomOfferData(@RequestParam int customerId) throws IOException {
        try {
            DroolsCustomerProperties properties = gamifiedOfferDao.getCustomerProperties(customerId, AppConstants.CHAAYOS_BRAND_ID);
            return droolsDecisionService.getRecomOfferData(properties,null);
        }catch (Exception e){
            log.error("Error while getting recom offer data for customer id :{}",customerId, e);
        }
        return null;
    }

    @GetMapping( value = "get-offer", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public RecomOfferData getRecomOfferDataByVersion(@RequestParam int customerId, @RequestParam Integer unitId) throws IOException {
        try {
            DroolsCustomerProperties properties = gamifiedOfferDao.getCustomerProperties(customerId, AppConstants.CHAAYOS_BRAND_ID);
            return droolsDecisionService.getRecomOfferData(properties,unitId);
        }catch (Exception e){
            log.error("Error while getting recom offer data for customer id :{}",customerId, e);
        }
        return null;
    }
}
