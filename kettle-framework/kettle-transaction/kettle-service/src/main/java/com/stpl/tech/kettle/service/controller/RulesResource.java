/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.service.controller;

import static com.stpl.tech.kettle.service.core.ServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.service.core.ServiceConstants.RULE_ROOT_CONTEXT;
import static com.stpl.tech.kettle.service.core.ServiceConstants.SEPARATOR;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import javax.ws.rs.core.MediaType;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import com.stpl.tech.kettle.core.cache.RecommendationCache;
import com.stpl.tech.kettle.core.service.RuleService;
import com.stpl.tech.kettle.core.service.UnitInventoryManagementService;
import com.stpl.tech.kettle.domain.model.ProductInventory;
import com.stpl.tech.kettle.offer.model.OptionResponseData;
import com.stpl.tech.kettle.offer.model.OrderData;
import com.stpl.tech.kettle.offer.model.RecommendationDetail;
import com.stpl.tech.kettle.offer.model.RuleData;
import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + RULE_ROOT_CONTEXT)
public class RulesResource {

	private static final Logger LOG = LoggerFactory.getLogger(RulesResource.class);

	@Autowired
	private RecommendationCache cache;

	@Autowired
	private UnitInventoryManagementService inventoryService;

	@Autowired
	private RuleService ruleService;

	@Autowired
	private MasterDataCache masterCache;

	@RequestMapping(method = RequestMethod.POST, value = "recommend", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public RecommendationDetail getRecommendation(@RequestBody final OrderData orderData)
			throws DataNotFoundException, AuthenticationFailureException {
		LOG.info("Getting Remcommendation for the order: " + orderData);
		RuleData rule = ruleService.getRule(orderData);
		Set<Integer> stockedOutProducts = new HashSet<>();
		try {
			List<ProductInventory> inventory = inventoryService.getUnitInventory(orderData.getUnitId(),
					new ArrayList<>(RuleData.PRODUCT_IDS));
			for (ProductInventory data : inventory) {
				if (data.getQuantity() <= 0) {
					stockedOutProducts.add(data.getProduct().getDetail().getId());
				}
			}
		} catch (Exception e) {
			LOG.error("Error while getting inventory for the cafe", e);
		}
		Set<Integer> unitProductIds = masterCache.getUnitProductIds(orderData.getUnitId());
		for (Integer id : RuleData.PRODUCT_IDS) {
			if (!unitProductIds.contains(id)) {
				stockedOutProducts.add(id);
			}
		}
		Set<Integer> skippedProducts = new HashSet<>(stockedOutProducts);
		if (orderData.getProductIds() != null) {
			skippedProducts.addAll(orderData.getProductIds());
		}
		OptionResponseData detail = cache.getOption(orderData.getUnitId(), rule, orderData.isOffer(), skippedProducts);
		LOG.info("Option Response after getting the option data {}", detail);
		if (detail != null) {
			detail.setStockedOutProductIds(new ArrayList<>(stockedOutProducts));
			detail.setDetail(orderData);

			RecommendationDetail recommendation = ruleService.create(detail);
			LOG.info("Recommendation " + recommendation);
			return recommendation;
		}
		return null;

	}

	@RequestMapping(method = RequestMethod.POST, value = "recommend/update", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public void updateRecommendation(@RequestBody final RecommendationDetail orderData)
			throws DataNotFoundException, AuthenticationFailureException {
		LOG.info("Updating Remcommendation for the order: " + orderData);
		ruleService.update(orderData);
	}
}