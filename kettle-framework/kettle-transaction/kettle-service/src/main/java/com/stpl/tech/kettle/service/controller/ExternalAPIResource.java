package com.stpl.tech.kettle.service.controller;

import static com.stpl.tech.kettle.service.core.ServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.service.core.ServiceConstants.EXTERNAL_ROOT_CONTEXT;
import static com.stpl.tech.kettle.service.core.ServiceConstants.SEPARATOR;

import javax.ws.rs.core.MediaType;

import org.apache.commons.lang.NotImplementedException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import com.stpl.tech.kettle.core.service.TempAccessService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.model.TempAccessCodeUsageData;
import com.stpl.tech.kettle.service.model.TokenEnquiry;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;

//@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + EXTERNAL_ROOT_CONTEXT)
public class ExternalAPIResource {

	private static final Logger LOG = LoggerFactory.getLogger(ExternalAPIResource.class);

	@Autowired
	private TempAccessService service;

	@Autowired
	private EnvironmentProperties props;

/*	@RequestMapping(method = RequestMethod.POST, value = "i2e1/token/valid", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public TokenEnquiry validateToken(@RequestBody TokenEnquiry enquiry) throws DataNotFoundException {
		LOG.info("Request to check coupon validity from i2e1 for contact number {} and token {} ",
				enquiry.getContactNumber(), enquiry.getAccessCode());
		TempAccessCodeUsageData data = service.grantAccess(enquiry.getAccessCode(), enquiry.getContactNumber());
		LOG.info("Usage Data : {}", data);
		enquiry.setValid(AppConstants.getValue(data.getAccessGranted()));
		enquiry.setReasonForDecline(data.getReasonForDenial());
		return enquiry;
	}

	@RequestMapping(method = RequestMethod.POST, value = "i2e1/token/create", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public TokenEnquiry createToken(@RequestBody TokenEnquiry enquiry) throws DataNotFoundException {
		LOG.info("Request to create coupon from i2e1 for contact number {} ", enquiry.getContactNumber());
		if (AppUtils.isProd(props.getEnvironmentType())) {
			throw new NotImplementedException("This API is not valid for production environment");
		}
		String tempCode = service.generateAccessCode(-1);
		enquiry.setAccessCode(tempCode);
		enquiry.setValid(true);
		return enquiry;
	}
*/

}
