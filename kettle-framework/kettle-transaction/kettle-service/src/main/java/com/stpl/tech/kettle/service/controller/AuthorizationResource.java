/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.service.controller;

import static com.stpl.tech.kettle.service.core.ServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.service.core.ServiceConstants.AUTHORIZATION_SERVICES_ROOT_CONTEXT;
import static com.stpl.tech.kettle.service.core.ServiceConstants.SEPARATOR;

import javax.annotation.PostConstruct;
import javax.jms.JMSException;
import javax.jms.MessageProducer;
import javax.jms.Session;
import javax.ws.rs.core.MediaType;

import com.amazon.sqs.javamessaging.SQSSession;
import com.amazonaws.regions.Regions;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.model.CustomerContactInfoMapping;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.CustomerVO;
import com.stpl.tech.master.notification.SQSNotification;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import com.stpl.tech.kettle.customer.service.AuthorizationService;
import com.stpl.tech.kettle.customer.service.CustomerService;
import com.stpl.tech.master.core.PasswordImpl;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + AUTHORIZATION_SERVICES_ROOT_CONTEXT)
public class AuthorizationResource {

    private static final Logger LOG = LoggerFactory.getLogger(AuthorizationResource.class);

    @Autowired
    private AuthorizationService authorizationDao;

    @Autowired
    private CustomerService customerDao;

    @Autowired
    private EnvironmentProperties environmentProperties;
    private MessageProducer producer;
    private SQSSession session;

    @PostConstruct
    public void setQueueSessions() throws JMSException {
    	LOG.info("POST-CONSTRUCT AuthorizationResource - STARTED");
        Regions region = Regions.fromName(environmentProperties.getAppOrderStatusQueueRegion());
        session = SQSNotification.getInstance().getSession(region, Session.AUTO_ACKNOWLEDGE);
        producer = SQSNotification.getInstance().getProducer(session,
                environmentProperties.getEnvironmentType().name(), "_CUSTOMER_PROFILE");
    }

    @RequestMapping(method = RequestMethod.GET, value = "validate", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean validate(@RequestParam("token") final String token) throws Exception {
        LOG.info("Got email verification request for token " + token);
        try {
            String decryptedString = PasswordImpl.decrypt(token);
            String[] splits = decryptedString.split("\\|");
            if (splits != null && splits.length == 3) {
                String contactNumber = splits[0];
                String emailId = splits[1];
                String randomString = splits[2];
                boolean exists = authorizationDao.exists(emailId, randomString);
                if (exists) {
                    customerDao.verifyEmailAddress(contactNumber);
                    Customer customer = customerDao.getCustomer(contactNumber);
                    producer.send(session.createObjectMessage(getCustomerVO(customer)));
                }
                return exists;
            }
        } catch (Exception e) {
            LOG.error("Error in email verification", e);
        }
        return false;
    }

    private CustomerVO getCustomerVO(Customer customer) {
        CustomerVO customerVO = new CustomerVO();
        customerVO.set_id(customer.get_id());
        customerVO.setAvailedSignupOffer(customer.isAvailedSignupOffer());
        customerVO.setBlacklisted(customer.isBlacklisted());
        customerVO.setChaayosCash(customer.getChaayosCash());
        customerVO.setContactNumber(customer.getContactNumber());
        customerVO.setContactNumberVerified(customer.isContactNumberVerified());
        customerVO.setCountryCode(customer.getCountryCode());
        customerVO.setEmailId(customer.getEmailId());
        customerVO.setEmailSubscriber(customer.isEmailSubscriber());
        customerVO.setEmailVerified(customer.isEmailVerified());
        customerVO.setFirstName(customer.getFirstName());
        customerVO.setId(customer.getId());
        customerVO.setInternal(customer.isInternal());
        customerVO.setLastName(customer.getLastName());
        customerVO.setLoyaltyPoints(customer.getLoyaltyPoints());
        customerVO.setLoyaltySubscriber(customer.isLoyaltySubscriber());
        customerVO.setMiddleName(customer.getMiddleName());
        customerVO.setSmsSubscriber(customer.isSmsSubscriber());
        return customerVO;
    }
}
