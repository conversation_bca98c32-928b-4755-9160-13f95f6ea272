
/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.service.controller;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.stpl.tech.kettle.core.service.SubscriptionManagementService;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.Subscription;
import com.stpl.tech.kettle.domain.model.SubscriptionEvent;
import com.stpl.tech.kettle.domain.model.SubscriptionStatusEvents;
import com.stpl.tech.kettle.service.model.SubscriptionEventUpdateData;
import com.stpl.tech.kettle.service.model.SubscriptionUpdateData;
import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.master.core.service.model.RequestData;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.util.List;

import static com.stpl.tech.kettle.service.core.ServiceConstants.*;

/**
 * Root resource (exposed at "order-management" path)
 */
@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + SUBSCRIPTION_MANAGEMENT_ROOT_CONTEXT) // v1/subscription-management
public class SubscriptionManagementResources extends AbstractResources {

	private static final Logger LOG = LoggerFactory.getLogger(SubscriptionManagementResources.class);

	@Autowired
	private SubscriptionManagementService subscriptionDao;

	/**
	 * Method handling HTTP GET requests. The returned object will be sent to
	 * the client as "text/plain" media type.
	 * 
	 * @return String that will be returned as a text/plain response.
	 * @throws AuthenticationFailureException
	 * @throws DataUpdationException
	 * @throws TemplateRenderingException
	 * @throws DataNotFoundException
	 * @throws IOException
	 * @throws JsonMappingException
	 * @throws JsonParseException
	 */
	@RequestMapping(method = RequestMethod.POST, value = "subscription/create", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public String createOrder(@RequestBody final Order request)
			throws AuthenticationFailureException, DataUpdationException, DataNotFoundException {

		//validate(request.getSession());
		LOG.info("Creating subscription " + JSONSerializer.toJSON(request));
		//Order order = getData(request, Order.class);
		String orderId = subscriptionDao.createSubscription(request);
		LOG.info("Creating subscription with productId " + orderId);
		return orderId;
	}

	@RequestMapping(method = RequestMethod.POST, value = "subscription/update", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public Subscription updateSubscription(@RequestBody final RequestData request)
			throws AuthenticationFailureException, DataUpdationException, DataNotFoundException {

		validate(request.getSession());
		LOG.info("Updating subscription " + request.getData());
		Subscription subscription = getData(request, Subscription.class);
		return subscriptionDao.updateSubscription(subscription);
	}

	/**
	 * Method handling HTTP GET requests. The returned object will be sent to
	 * the client as "text/plain" media type.
	 * 
	 * @return String that will be returned as a text/plain response.
	 * @throws AuthenticationFailureException
	 * @throws DataUpdationException
	 * @throws TemplateRenderingException
	 * @throws DataNotFoundException
	 * @throws IOException
	 * @throws JsonMappingException
	 * @throws JsonParseException
	 */
	@RequestMapping(method = RequestMethod.POST, value = "subscription/hold/create", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean holdSubscription(@RequestBody final RequestData request) throws AuthenticationFailureException {

		validate(request.getSession());
		LOG.info("Putting subscription on hold " + request);
		SubscriptionUpdateData updateRequest = getData(request, SubscriptionUpdateData.class);
		return subscriptionDao.holdOrCancelSubscription(updateRequest.getSubscriptionId(), updateRequest.getStatus(),
				updateRequest.isChangeWithImmediateEffect(), updateRequest.getStartDate(),
				updateRequest.getEndDate() == null ? AppUtils.getInfiniteDate() : updateRequest.getEndDate(),
				updateRequest.getChangedBy(), updateRequest.getReasonText());
	}

	@RequestMapping(method = RequestMethod.POST, value = "subscription/statusEvents", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<SubscriptionStatusEvents> getSubscriptionHolds(@RequestBody final RequestData request)
			throws AuthenticationFailureException, DataNotFoundException {
		validate(request.getSession());
		LOG.info("Getting holds for subscription " + request);
		Integer subscriptionId = getData(request, Integer.class);
		List<SubscriptionStatusEvents> detail = subscriptionDao.getStatusEventsForSubscription(subscriptionId);
		return detail;
	}

	/**
	 * Method handling HTTP GET requests. The returned object will be sent to
	 * the client as "text/plain" media type.
	 * 
	 * @return String that will be returned as a text/plain response.
	 * @throws AuthenticationFailureException
	 * @throws DataUpdationException
	 * @throws TemplateRenderingException
	 * @throws DataNotFoundException
	 * @throws IOException
	 * @throws JsonMappingException
	 * @throws JsonParseException
	 */
	@RequestMapping(method = RequestMethod.POST, value = "subscription/hold/cancel", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean cancelHoldSubscription(@RequestBody final RequestData request)
			throws AuthenticationFailureException {

		validate(request.getSession());
		LOG.info("Cancelling Hold for the subscription " + request);
		SubscriptionEventUpdateData subscriptionEventDetailId = getData(request, SubscriptionEventUpdateData.class);
		return subscriptionDao.cancelHold(subscriptionEventDetailId.getSubscriptionEventStatusId());
	}

	/**
	 * Method handling HTTP GET requests. The returned object will be sent to
	 * the client as "text/plain" media type.
	 * 
	 * @return String that will be returned as a text/plain response.
	 * @throws AuthenticationFailureException
	 * @throws DataUpdationException
	 * @throws TemplateRenderingException
	 * @throws DataNotFoundException
	 * @throws IOException
	 * @throws JsonMappingException
	 * @throws JsonParseException
	 */
	@RequestMapping(method = RequestMethod.POST, value = "subscription/hold/update", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean updateHoldSubscription(@RequestBody final RequestData request)
			throws AuthenticationFailureException {

		validate(request.getSession());
		LOG.info("Cancelling Hold for the subscription " + request);
		SubscriptionEventUpdateData subscriptionEventDetailData = getData(request, SubscriptionEventUpdateData.class);
		return subscriptionDao.updateHold(subscriptionEventDetailData.getSubscriptionEventStatusId(),
				subscriptionEventDetailData.getStartDate(), subscriptionEventDetailData.getEndDate(),
				subscriptionEventDetailData.isChangeWithImmediateEffect(), subscriptionEventDetailData.getReason());
	}

	/**
	 * Method handling HTTP GET requests. The returned object will be sent to
	 * the client as "text/plain" media type.
	 * 
	 * @return String that will be returned as a text/plain response.
	 * @throws AuthenticationFailureException
	 * @throws DataUpdationException
	 * @throws TemplateRenderingException
	 * @throws DataNotFoundException
	 * @throws IOException
	 * @throws JsonMappingException
	 * @throws JsonParseException
	 */
	@RequestMapping(method = RequestMethod.POST, value = "subscription/cancel", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean cancelSubscription(@RequestBody final RequestData request) throws AuthenticationFailureException {

		validate(request.getSession());
		LOG.info("Cancelling the subscription " + request);
		SubscriptionUpdateData updateRequest = getData(request, SubscriptionUpdateData.class);
		return subscriptionDao.holdOrCancelSubscription(updateRequest.getSubscriptionId(), updateRequest.getStatus(),
				updateRequest.isChangeWithImmediateEffect(), updateRequest.getStartDate(), AppUtils.getInfiniteDate(),
				updateRequest.getChangedBy(), updateRequest.getReasonText());
	}

	/**
	 * Method handling HTTP GET requests. The returned object will be sent to
	 * the client as "text/plain" media type.
	 * 
	 * @return String that will be returned as a text/plain response.
	 * @throws AuthenticationFailureException
	 * @throws DataUpdationException
	 * @throws TemplateRenderingException
	 * @throws DataNotFoundException
	 * @throws IOException
	 * @throws JsonMappingException
	 * @throws JsonParseException
	 */
	@RequestMapping(method = RequestMethod.POST, value = "subscription/cancel/update", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public boolean updateCancelSubscription(@RequestBody final RequestData request)
			throws AuthenticationFailureException {

		validate(request.getSession());
		LOG.info("Updating cancel event " + request);
		SubscriptionEventUpdateData subscriptionEventDetailData = getData(request, SubscriptionEventUpdateData.class);
		return subscriptionDao.updateCancel(subscriptionEventDetailData.getSubscriptionEventStatusId(),
				subscriptionEventDetailData.getStartDate(), subscriptionEventDetailData.getReason());
	}

	/**
	 * Method handling HTTP GET requests. The returned object will be sent to
	 * the client as "text/plain" media type.
	 * 
	 * @return String that will be returned as a text/plain response.
	 * @throws AuthenticationFailureException
	 * @throws DataUpdationException
	 * @throws TemplateRenderingException
	 * @throws DataNotFoundException
	 * @throws IOException
	 * @throws JsonMappingException
	 * @throws JsonParseException
	 */
	@RequestMapping(method = RequestMethod.POST, value = "subscription/customer", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<Order> getSubscriptions(@RequestBody final RequestData request)
			throws AuthenticationFailureException, DataNotFoundException {

		validate(request.getSession());
		LOG.info("Get all subscriptions for the customer " + request.getData());
		String contactNumber = request.getData();
		return subscriptionDao.getSubscriptions(contactNumber);

	}

	/**
	 * Method handling HTTP GET requests. The returned object will be sent to
	 * the client as "text/plain" media type.
	 * 
	 * @return String that will be returned as a text/plain response.
	 * @throws AuthenticationFailureException
	 * @throws DataUpdationException
	 * @throws TemplateRenderingException
	 * @throws DataNotFoundException
	 * @throws IOException
	 * @throws JsonMappingException
	 * @throws JsonParseException
	 */
	@RequestMapping(method = RequestMethod.POST, value = "subscription/event/update", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public SubscriptionEvent updateEvent(@RequestBody final RequestData request)
			throws AuthenticationFailureException, DataNotFoundException, DataUpdationException {

		validate(request.getSession());
		LOG.info("Updating event data for subscription" + request.getData());
		SubscriptionEvent event = getData(request, SubscriptionEvent.class);
		return subscriptionDao.updateEventData(event);

	}

	/**
	 * Method handling HTTP GET requests. The returned object will be sent to
	 * the client as "text/plain" media type.
	 * 
	 * @return String that will be returned as a text/plain response.
	 * @throws AuthenticationFailureException
	 * @throws DataUpdationException
	 * @throws TemplateRenderingException
	 * @throws DataNotFoundException
	 * @throws IOException
	 * @throws JsonMappingException
	 * @throws JsonParseException
	 */
	@RequestMapping(method = RequestMethod.POST, value = "subscription/order/customer", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<SubscriptionEvent> getSubscriptionOrders(@RequestBody final RequestData request)
			throws AuthenticationFailureException, DataNotFoundException {

		// validate(request.getSession());
		LOG.info("Get all subscriptions for the customer " + request.getData());
		String contactNumber = request.getData();
		return subscriptionDao.getSubscriptionOrders(contactNumber);

	}

	/**
	 * Method handling HTTP GET requests. The returned object will be sent to
	 * the client as "text/plain" media type.
	 * 
	 * @return String that will be returned as a text/plain response.
	 * @throws AuthenticationFailureException
	 * @throws DataUpdationException
	 * @throws TemplateRenderingException
	 * @throws DataNotFoundException
	 * @throws IOException
	 * @throws JsonMappingException
	 * @throws JsonParseException
	 */
	@RequestMapping(method = RequestMethod.POST, value = "subscription/order/unit", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<SubscriptionEvent> getSubscriptionOrdersForAUnit(@RequestBody final RequestData request)
			throws AuthenticationFailureException, DataNotFoundException {

		// validate(request.getSession());
		LOG.info("Get all subscriptions for the unit " + request.getData());
		Integer unitId = getData(request, Integer.class);
		return subscriptionDao.getSubscriptionOrders(unitId);

	}
}
