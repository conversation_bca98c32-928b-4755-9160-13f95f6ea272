/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.service.config;

import com.google.auth.oauth2.GoogleCredentials;
import com.google.firebase.FirebaseApp;
import com.google.firebase.FirebaseOptions;
import com.stpl.tech.kettle.core.config.CLMConfig;
import com.stpl.tech.kettle.core.config.DroolsConfig;
import com.stpl.tech.kettle.core.config.TransactionConfig;
import com.stpl.tech.kettle.core.config.TransactionExternalConfig;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.master.core.MasterProperties;
import com.stpl.tech.master.core.config.KettleInterceptorConfig;
import com.stpl.tech.master.core.config.MasterExternalConfig;
import com.stpl.tech.master.core.config.ServiceConfig;
import com.stpl.tech.master.core.payment.config.IngenicoConfig;
import com.stpl.tech.master.core.payment.config.PaytmConfig;
import com.stpl.tech.spring.config.MasterSecurityConfiguration;
import com.stpl.tech.spring.config.SpringUtilityServiceConfig;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.FileInputStream;
import java.net.URL;
import java.util.TimeZone;

@SpringBootApplication
@EnableWebMvc
@EnableAutoConfiguration
@EnableTransactionManagement
@EnableScheduling
@EnableCaching
@Log4j2
@ComponentScan({ "com.stpl.tech.kettle", "com.stpl.tech.spring.crypto","com.stpl.tech.master.lock"})
@Import(value = {KettleHazelcastServerConfig.class, MasterExternalConfig.class, TransactionConfig.class, TransactionExternalConfig.class,
		PaytmConfig.class, IngenicoConfig.class, SpringUtilityServiceConfig.class,
		KettleInterceptorConfig.class, MasterSecurityConfiguration.class, CLMConfig.class, DroolsConfig.class, ServiceConfig.class})
@EnableMongoRepositories(basePackages = {"com.stpl.tech.kettle.data.mongo.stock.dao"})
public class PersistenceJPAConfig extends SpringBootServletInitializer {

	static {
		TimeZone.setDefault(TimeZone.getTimeZone("Asia/Kolkata"));
	}

	@Autowired
	EnvironmentProperties environmentProperties;

	public static void main(String[] args) {
		SpringApplication.run(PersistenceJPAConfig.class, args);
	}
	@PostConstruct
	public void initializeFirebaseApp() {
		log.info("POST CONSTRUCT FIRESTORE - STARTED");
		FileInputStream serviceAccount = null;
		try {
			try {
				URL resource = getClass().getClassLoader().getResource("firebase-" +
						environmentProperties.getEnvironmentType().name().toLowerCase()+".json");
				if (resource != null) {
					File file = new File(resource.getFile());
					serviceAccount = new FileInputStream(file);
				} else {
					log.info("No File Found ..!");
					return;
				}
			} catch (Exception e) {
				log.info("Error Occurred while loading resources ..!" , e);
				return;
			}
			FirebaseOptions options = FirebaseOptions.builder()
					.setCredentials(GoogleCredentials.fromStream(serviceAccount))
					.build();
			FirebaseApp.initializeApp(options);
		} catch (Exception e) {
			log.error("File not found exception :",e);
		}
	}

	@Override
	protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
		return application.sources(PersistenceJPAConfig.class);
	}

}
