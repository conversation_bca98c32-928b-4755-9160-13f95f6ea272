/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.service.controller;

import com.stpl.tech.kettle.core.monitoring.TempCodeCache;
import com.stpl.tech.kettle.core.monitoring.TempCodeData;
import com.stpl.tech.kettle.core.service.TempAccessService;
import com.stpl.tech.util.AppConstants;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
public class TempCodeResource {

	@Autowired
	private TempCodeCache cache;
	@Autowired
	private TempAccessService tempAccessService;

	private static final Logger LOG = LoggerFactory.getLogger(TempCodeResource.class);

	@Scheduled(fixedRate = 60000)
	public void removeFromQueue() throws InterruptedException {
		// Take elements out from the DelayQueue object.
		if (cache.getQueue().peek() == null) {
			return;
		}
		TempCodeData data = cache.getQueue().take();
		LOG.info(String.format("[%s] - Removed token", data));
		cache.removeFromCache(data.getCode());
		tempAccessService.upadteStatus(data.getAccessCodeId(), AppConstants.IN_ACTIVE);
	}
}
