package com.stpl.tech.kettle.service.webengage.controller;

import com.google.gson.Gson;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.LoyaltyScore;
import com.stpl.tech.kettle.webengage.data.model.CustomerDataPushTrack;
import com.stpl.tech.kettle.webengage.data.model.OrderDataPushTrack;
import com.stpl.tech.kettle.webengage.domain.model.ResponseVO;
import com.stpl.tech.kettle.webengage.domain.model.WebengageEvent;
import com.stpl.tech.kettle.webengage.domain.model.WebengageUser;
import com.stpl.tech.kettle.webengage.service.WebEngageDataPushService;
import com.stpl.tech.kettle.webengage.util.AbstractRestTemplate;
import com.stpl.tech.kettle.webengage.util.WebengageConstants;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.MediaType;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.stpl.tech.kettle.webengage.converter.WebEngageConverter.convert;

/**
 * Created by Chaayos on 07-05-2017.
 */

@RestController
@RequestMapping(value = WebengageConstants.API_VERSION + WebengageConstants.SEPARATOR
    + WebengageConstants.ROOT_CONTEXT + WebengageConstants.SEPARATOR + WebengageConstants.WEBENEGAGE_PUSH_ROOT_CONTEXT)
public class WebengagePushResource {

    private static final Logger LOG = LoggerFactory.getLogger(WebengagePushResource.class);


    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private WebEngageDataPushService pushService;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private EnvironmentProperties environmentProperties;

    @RequestMapping(method = RequestMethod.GET, value = "start-order-data-push", produces = MediaType.APPLICATION_JSON)
    public void startOrderDataPush(@RequestParam Integer startOrderId, @RequestParam Integer batchSize) {
        if (batchSize == null || batchSize == 0) {
            batchSize = 100;
        }
        if (startOrderId != null) {
            List<Integer> orderIds = pushService.getSettledKettleOrderBatch(startOrderId, batchSize);
            ThreadPoolTaskExecutor executor = (ThreadPoolTaskExecutor) applicationContext.getBean("taskExecutor");
            for (Integer orderId : orderIds) {
                executor.execute(() -> {
                    try {
                        WebengageEvent webengageEvent = pushService.getOrderBatch(orderId);
                        LOG.info("Sending order id: " + orderId);
                        LOG.info("Request Body:::::::: " + new Gson().toJson(webengageEvent));
                        ResponseVO responseVO = AbstractRestTemplate.postWithAuth(getWebengageEventEndpoint(),
                            getWebengageAuth(), webengageEvent, ResponseVO.class);
                        LOG.info(new Gson().toJson(responseVO));
                        pushService.persistTrackOrder(new OrderDataPushTrack(null, orderId, responseVO.getResponse().getStatus()));
                    } catch (Exception e) {
                        LOG.info("Error pushing order data to Webengage:", e);
                        e.printStackTrace();
                        pushService.persistTrackOrder(new OrderDataPushTrack(null, orderId, "ERROR"));
                    }
                });
            }
            //Map<Integer, WebengageEvent> events = pushService.getOrderBatch(startOrderId, batchSize);
            //ThreadPoolTaskExecutor executor = (ThreadPoolTaskExecutor) applicationContext.getBean("taskExecutor");
            /*for (Integer orderId : events.keySet()) {
                executor.execute(new Runnable() {
                    @Override
                    public void run() {
                        try{
                            LOG.info("Sending order id: " + orderId);
                            LOG.info("Request Body:::::::: " + new Gson().toJson(events.get(orderId)));
                            ResponseVO responseVO = AbstractRestTemplate.postWithAuth(getWebengageEventEndpoint(),
                                getWebengageAuth(), events.get(orderId), ResponseVO.class);
                            LOG.info(new Gson().toJson(responseVO));
                            pushService.persistTrackOrder(new OrderDataPushTrack(null, orderId, responseVO.getResponse().getStatus()));
                        }catch (Exception e){
                            pushService.persistTrackOrder(new OrderDataPushTrack(null, orderId, "ERROR"));
                        }
                    }
                });
            }*/
        } else {
            LOG.info("No order Id to start with!");
        }
    }

    @RequestMapping(method = RequestMethod.GET, value = "start-user-push", produces = MediaType.APPLICATION_JSON)
    public void startUserPush(@RequestParam Integer startUserId, @RequestParam Integer batchSize) {
        if (batchSize == null || batchSize == 0) {
            batchSize = 100;
        }
        if (startUserId != null) {
            List<CustomerInfo> customerInfos = pushService.getCustomerBatch(startUserId, batchSize);
            Map<String, Integer> integerCustomerInfoMap = new HashMap<>();
            customerInfos.stream().forEach(customerInfo -> {
                integerCustomerInfoMap.put(customerInfo.getContactNumber(), customerInfo.getCustomerId());
            });
            List<LoyaltyScore> scores = pushService.getCustomerLoyaltyScores(integerCustomerInfoMap.values());
            Map<Integer, Integer> scoreMap = new HashMap<>();
            scores.stream().forEach(loyaltyScore -> {
                scoreMap.put(loyaltyScore.getCustomerId(), loyaltyScore.getAcquiredPoints());
            });
            List<WebengageUser> webengageUsers = new ArrayList<>();
            for (CustomerInfo customerInfo : customerInfos) {
                webengageUsers.add(convert(customerInfo, scoreMap.get(customerInfo.getCustomerId())));
            }
            ThreadPoolTaskExecutor executor = (ThreadPoolTaskExecutor) applicationContext.getBean("taskExecutor");
            for (WebengageUser webengageUser : webengageUsers) {
                executor.execute(() -> {
                    try {
                        LOG.info("Sending customer id: " + webengageUser.getUserId());
                        //WebengageUser webengageUser = convert(customerInfo, scoreMap.get(customerInfo.getCustomerId()));
                        LOG.info("Request Body:::::::: " + new Gson().toJson(webengageUser));
                        ResponseVO responseVO = AbstractRestTemplate.postWithAuth(getWebengageUserEndpoint(),
                            getWebengageAuth(), webengageUser, ResponseVO.class);
                        LOG.info(new Gson().toJson(responseVO));
                        pushService.persistTrackCustomer(new CustomerDataPushTrack(null, integerCustomerInfoMap.get(webengageUser.getUserId()),
                            responseVO.getResponse().getStatus()));
                    } catch (Exception e) {
                        pushService.persistTrackCustomer(new CustomerDataPushTrack(null, integerCustomerInfoMap.get(webengageUser.getUserId()), "ERROR"));
                    }
                });
            }
        } else {
            LOG.info("No customer Id to start with!");
        }
    }

    private String getWebengageAuth() {
        return "bearer " + environmentProperties.getWebengageKey();
    }

    private String getWebengageUserEndpoint() {
        return environmentProperties.getWebengageBaseUrl() + "v1/accounts/" + environmentProperties.getWebengageLicence() + "/users";
    }

    private String getWebengageEventEndpoint() {
        return environmentProperties.getWebengageBaseUrl() + "v1/accounts/" + environmentProperties.getWebengageLicence() + "/events";
    }


}
