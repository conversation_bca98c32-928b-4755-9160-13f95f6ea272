/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.service.report;

import java.util.Collection;

import com.stpl.tech.kettle.core.notification.RawPrintReceipt;
import com.stpl.tech.kettle.reports.model.SettlementReport;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.util.AppUtils;

public class SettlementRawReceipt extends RawPrintReceipt {
	private Unit unit;
	private Collection<SettlementReport> details;
	private int noOfOrders;
	private String basePath;
	private String generationTime = AppUtils.getCurrentTimeISTString();

	public SettlementRawReceipt(String basePath, Unit unit, int orderCount, Collection<SettlementReport> details) {
		this.unit = unit;
		this.details = details;
		this.basePath = basePath;
		this.noOfOrders = orderCount;
	}

	@Override
	public String getFilepath() {
		return basePath + "/" + unit.getId() + "/reports/SettlementReceipt-" + unit.getName() + "-"
				+ AppUtils.getCurrentTimeISTStringWithNoColons() + ".html";
	}

	@Override
	public StringBuilder processData() {
		reset();
		left(rpad("Unit Name:", 18) + unit.getName());
		left(rpad("Report Name:", 18) + "SettlementType Report");
		left(rpad("Time:", 18) + generationTime);
		left(rpad("No Of Bills:", 18) + noOfOrders);
		separator();

		for (SettlementReport settlement : details) {
			if (hasValue(settlement.getGrossAmount()) || hasValue(settlement.getDiscountAmount())
					|| hasValue(settlement.getAmount()) || hasValue(settlement.getExtraVouchers())
					|| hasValue(settlement.getTotal())) {
				left(bold(settlement.getName()));
				left(rpad("GMV", 18) + settlement.getGrossAmount());
				left(rpad("Discount", 18) + settlement.getDiscountAmount());
				left(rpad("Taxable", 18) + settlement.getAmount());
				if (settlement.getId() == 4 || settlement.getId() == 5) {
					left(rpad("Extra Vouchers", 18) + settlement.getExtraVouchers());
				}
				left(rpad("Amount", 18) + settlement.getTotal());
				left(" ");
			}
		}
		cut();
		return getSb();
	}

}
