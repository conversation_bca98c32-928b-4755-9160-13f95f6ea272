/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.service.model;

import com.stpl.tech.kettle.core.monitoring.UnitMonitorData;
import com.stpl.tech.master.domain.model.UnitStatus;

public class UnitHealthData {

	private UnitMonitorData detail;
	private UnitStatus status;

	public UnitHealthData() {
		super();
	}

	public UnitHealthData(UnitMonitorData detail, UnitStatus status) {
		super();
		this.detail = detail;
		this.status = status;
	}

	public UnitMonitorData getDetail() {
		return detail;
	}

	public void setDetail(UnitMonitorData detail) {
		this.detail = detail;
	}

	public UnitStatus getStatus() {
		return status;
	}

	public void setStatus(UnitStatus status) {
		this.status = status;
	}

}
