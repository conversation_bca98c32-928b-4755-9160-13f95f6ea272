
/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.service.controller;

import java.io.IOException;
import java.text.ParseException;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.stpl.tech.kettle.core.service.PosMetadataService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.stpl.tech.kettle.core.FeedbackFrequency;
import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.customer.service.FeedbackManagementService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.report.metadata.model.ReportCategories;
import com.stpl.tech.kettle.reports.external.impl.FTPTransmission;
import com.stpl.tech.kettle.stock.service.AutomatedStockEventReport;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.service.AbstractAutomatedReports;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.notification.GenericNotification;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.JaxbUtil;

@Component
public class AutomatedReportsResource extends AbstractAutomatedReports {

	private static final Logger LOG = LoggerFactory.getLogger(AutomatedReportsResource.class);

	@Autowired
	private EnvironmentProperties props;

	@Autowired
	private FeedbackManagementService feedbackService;
	
	@Autowired
	private AutomatedStockEventReport reportService;
	
	@Autowired
	private MasterDataCache masterCache;

	@Autowired
	PosMetadataService posMetadataService;

	// @Autowired
	// private CardService cardService;

	/*// @Scheduled(fixedRate = 120000)
	@Scheduled(cron = "0 0 6 * * *", zone = "GMT+05:30")
	public void autoReport() throws ParseException, EmailGenerationException, IOException {
		if (props.getRunAutoReports()) {
			ReportCategories reportCategories = JaxbUtil.jaxbXMLToObject(ReportCategories.class,
					props.getBasePath() + "/reports/AutomatedReports.xml");

			executeReports(reportCategories, AppUtils.getFormattedDate(AppUtils.getPreviousDate()));
		}
	}*/

	/*// @Scheduled(fixedRate = 120000)
	@Scheduled(cron = "0 0 18 * * *", zone = "GMT+05:30")
	public void autoPullReport() throws ParseException, EmailGenerationException, IOException {
		if (props.getRunAutoReports()) {
			ReportCategories reportCategories = JaxbUtil.jaxbXMLToObject(ReportCategories.class,
					props.getBasePath() + "/reports/AccountReports.xml");

			executeReports(reportCategories, AppUtils.getFormattedTime(AppUtils.getCurrentTimestamp()));
		}
	}*/

	/*// @Scheduled(fixedRate = 120000)
	@Scheduled(cron = "0 0 0,1,2,3,4,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23 * * *", zone = "GMT+05:30")
	public void intraDayReports() throws ParseException, EmailGenerationException, IOException {
		if (props.getRunAutoIntradayReports()) {
			ReportCategories reportCategories = JaxbUtil.jaxbXMLToObject(ReportCategories.class,
					props.getBasePath() + "/reports/IntraDayReports.xml");
			String businessDate = AppUtils.getDateString(AppUtils.getCurrentBusinessDate()) + " @ "
					+ AppUtils.getFormattedTime(AppUtils.getCurrentTimestamp());
			executeReports(reportCategories, businessDate);
		}
	}*/

	// @Scheduled(fixedRate = 120000)
	/*@Scheduled(cron = "0 0 5,11,17,23 * * *", zone = "GMT+05:30")
	public void intraDayFicoReports() throws ParseException, EmailGenerationException, IOException {
		if (props.getRunAutoIntradayReports()) {
			ReportCategories reportCategories = JaxbUtil.jaxbXMLToObject(ReportCategories.class,
					props.getBasePath() + "/reports/FicoIntraDayReports.xml");
			String businessDate = AppUtils.getDateString(AppUtils.getCurrentBusinessDate()) + " @ "
					+ AppUtils.getFormattedTime(AppUtils.getCurrentTimestamp());
			executeReports(reportCategories, businessDate);

		}
	}*/

	/*@Scheduled(cron = "0 0/30 0,1,2,3,4,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23 * * *", zone = "GMT+05:30")
	public void intraDayHalfHourlyReports() throws ParseException, EmailGenerationException, IOException {
		if (props.getRunAutoIntradayReports()) {
			ReportCategories reportCategories = JaxbUtil.jaxbXMLToObject(ReportCategories.class,
					props.getBasePath() + "/reports/IntraDayHalfHourlyReports.xml");
			String businessDate = AppUtils.getDateString(AppUtils.getCurrentBusinessDate()) + " @ "
					+ AppUtils.getFormattedTime(AppUtils.getCurrentTimestamp());
			executeReports(reportCategories, businessDate);
		}
	}*/

	/*// @Scheduled(fixedRate = 120000)
	@Scheduled(cron = "0 2 0,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23 * * *", zone = "GMT+05:30")
	public void intraDayDeliveryReports() throws ParseException, EmailGenerationException, IOException {
		if (props.getRunAutoIntradayReports()) {
			ReportCategories reportCategories = JaxbUtil.jaxbXMLToObject(ReportCategories.class,
					props.getBasePath() + "/reports/IntraDayDeliveryReports.xml");
			String businessDate = AppUtils.getDateString(AppUtils.getCurrentBusinessDate()) + " @ "
					+ AppUtils.getFormattedTime(AppUtils.getCurrentTimestamp());
			executeReports(reportCategories, businessDate);
		}
	}*/

	/*@Scheduled(cron = "0 55 4 * * *", zone = "GMT+05:30")
	public void intraDayReportsAtDayClose() throws ParseException, EmailGenerationException, IOException {
		if (props.getRunAutoIntradayReports()) {
			ReportCategories reportCategories = JaxbUtil.jaxbXMLToObject(ReportCategories.class,
					props.getBasePath() + "/reports/IntraDayReports.xml");
			String businessDate = AppUtils.getDateString(AppUtils.getCurrentBusinessDate()) + " @ "
					+ AppUtils.getFormattedTime(AppUtils.getCurrentTimestamp());
			executeReports(reportCategories, businessDate);
		}
	}*/

	//@Scheduled(fixedRate = 120000)
	@Scheduled(cron = "0 45 5 * * *", zone = "GMT+05:30")
	public void sendOHCSalesReport() throws ParseException, EmailGenerationException, IOException {
		ReportCategories reportCategories = JaxbUtil.jaxbXMLToObject(ReportCategories.class,
				props.getBasePath() + "/reports/OHCSalesReport.xml");
		String businessDate = AppUtils.getDateString(AppUtils.getCurrentBusinessDate());
		List<GenericNotification> notification = executeReports(reportCategories, businessDate, true);

		// FTPTransmission ftp = new FTPTransmission("54.169.147.233", 21,
		// "ftpusr", "Chaayos@1234", null);
		FTPTransmission ftp = new FTPTransmission("182.72.171.246", 21, "posdsr", "pos[dsr]##123", null);
		ftp.transfer(props.getEnvironmentType(), props.getBasePath() + "/tmp/",
				notification.get(0).getAttachmentData().get(0));
	}

	/*// @Scheduled(fixedRate = 120000)
	@Scheduled(cron = "0 0 11,15,19,23 * * *", zone = "GMT+05:30")
	public void codDataDump() throws ParseException, EmailGenerationException, IOException {
		if (props.getRunAutoIntradayReports()) {
			ReportCategories reportCategories = JaxbUtil.jaxbXMLToObject(ReportCategories.class,
					props.getBasePath() + "/reports/CODDataDump.xml");
			executeReports(reportCategories, AppUtils.getFormattedTime(AppUtils.getCurrentTimestamp()));
		}
	}*/

	/*// @Scheduled(fixedRate = 120000)
	@Scheduled(cron = "0 0 10,13,18 * * *", zone = "GMT+05:30")
	public void intraDayEnquiryReports() throws ParseException, EmailGenerationException, IOException {
		if (props.getRunAutoIntradayReports()) {
			ReportCategories reportCategories = JaxbUtil.jaxbXMLToObject(ReportCategories.class,
					props.getBasePath() + "/reports/EnquiryIntraDayReport.xml");
			executeReports(reportCategories, AppUtils.getFormattedTime(AppUtils.getCurrentTimestamp()));
		}
	}*/

 /*   @Scheduled(cron = "0 0 11,15,19 * * *", zone = "GMT+05:30")
    public void intraDayOrderSummaryReports() throws ParseException, EmailGenerationException, IOException {
        if (props.getRunAutoIntradayReports()) {
            ReportCategories reportCategories = JaxbUtil.jaxbXMLToObject(ReportCategories.class,
                    props.getBasePath() + "/reports/IntraDayOrderSummary.xml");
            executeReports(reportCategories, AppUtils.getFormattedTime(AppUtils.getCurrentTimestamp()));
        }
    }
*/

	// @Scheduled(fixedRate = 120000)
	// Cron for Monday 6:30 AM
	@Scheduled(cron = "0 30 6 * * MON", zone = "GMT+05:30")
	public void clmReport() throws ParseException, EmailGenerationException, IOException {
		if (props.getRunAutoReports()) {
			ReportCategories reportCategories = JaxbUtil.jaxbXMLToObject(ReportCategories.class,
					props.getBasePath() + "/reports/ClmReports.xml");

			executeReports(reportCategories, AppUtils.getFormattedDate(AppUtils.getPreviousDate()));
		}
	}

	/*// @Scheduled(fixedRate = 120000)
	@Scheduled(cron = "0 0 * * * *", zone = "GMT+05:30")
	public void channelPartnerReport() throws ParseException, EmailGenerationException, IOException {
		if (props.getRunAutoIntradayReports()) {
			ReportCategories reportCategories = JaxbUtil.jaxbXMLToObject(ReportCategories.class,
					props.getBasePath() + "/reports/ChannelPartnerReport.xml");
			executeReports(reportCategories, AppUtils.getFormattedTime(AppUtils.getCurrentTimestamp()));
		}
	}*/

	/*// @Scheduled(fixedRate = 120000)
	@Scheduled(cron = "0 15 10 1 * ?", zone = "GMT+05:30")
	public void billWiseReportChaayosPhoenixMarketCityKurla()
			throws ParseException, EmailGenerationException, IOException {
		if (props.getRunAutoIntradayReports()) {
			ReportCategories reportCategories = JaxbUtil.jaxbXMLToObject(ReportCategories.class,
					props.getBasePath() + "/reports/PhoenixMarketCityKurla.xml");
			executeReports(reportCategories, AppUtils.getFormattedTime(AppUtils.getCurrentTimestamp()));
		}
	}*/

	@Scheduled(cron = "0 10 7 * * *", zone = "GMT+05:30")
	public void dailyFeedback() throws ParseException, EmailGenerationException, IOException {
		runFeedbackReport(FeedbackFrequency.CUMULATIVE, "/reports/CumulativeFeedback.xml");
		runFeedbackReport(FeedbackFrequency.DAILY, "/reports/DailyFeedback.xml");
		if (AppUtils.isTodayMonday()) {
			runFeedbackReport(FeedbackFrequency.WEEKLY, "/reports/WeeklyFeedback.xml");
		}
		if (AppUtils.isTodayTheFirstDayOfMonth()) {
			runFeedbackReport(FeedbackFrequency.MONTHLY, "/reports/MonthlyFeedback.xml");
		}
		if (AppUtils.isTodayTheSecondDayOfMonth()) {
			monthlyCsatScoreReport();
		}
	}



//	@Scheduled(cron = "0 10 8 * * *", zone = "GMT+05:30")
//	public void dailyStockOutReport() throws ParseException, EmailGenerationException, IOException {
//		Map<Integer, List<Date>> unitTimeMap= posMetadataService.getUnitClosingTimeMap(AppUtils.getPreviousDate());
//		if(unitTimeMap==null || unitTimeMap.isEmpty()){
//			return;
//		}
//		for (UnitBasicDetail unit : masterCache.getAllUnits()) {
//			try {
//				LOG.info("Creating Report For Unit {}",unit.getId());
//				if (TransactionUtils.isActiveUnit(unit.getStatus()) && unit.isLive()
//						&& UnitCategory.CAFE.equals(unit.getCategory())
//						&& unitTimeMap.get(unit.getId()) != null) {
//					List<Date> unitTime = unitTimeMap.get(unit.getId());
//					reportService.execute(AppUtils.getPreviousDate(), unit.getId(), true, unitTime);
//				}
//			}catch (Exception e){
//				LOG.error("Exception Caught While Generating the Report for Unit {}",unit.getId(),e);
//			}
//		}
//	}

	@Scheduled(cron = "0 0 4,5,6,7,8,9,10 * * *", zone = "GMT+05:30")
	public void createPreviousDateStockOutEntry() {
		posMetadataService.createPreviousDateStockOutEntry(AppUtils.getPreviousDate());
	}

	private void monthlyCsatScoreReport() throws ParseException, EmailGenerationException, IOException {
		if (props.getRunAutoIntradayReports()) {
			ReportCategories reportCategories = JaxbUtil.jaxbXMLToObject(ReportCategories.class,
					props.getBasePath() + "/reports/MonthlyCSATReport.xml");
			executeReports(reportCategories, AppUtils.getFormattedTime(AppUtils.getCurrentTimestamp()));
		}
	}

	/*
	 * @Scheduled(cron = "0 0 8 * * *", zone = "GMT+05:30") public void
	 * deactivateCashCards() throws ParseException, EmailGenerationException,
	 * IOException { if (props.getRunAutoReports()) {
	 * cardService.deactivateCashCards(); } }
	 */

	private void runFeedbackReport(FeedbackFrequency frequency, String reportFile)
			throws ParseException, EmailGenerationException, IOException {
		feedbackService.updateFeedbackData(frequency);
		if (props.getRunAutoReports()) {
			ReportCategories reportCategories = JaxbUtil.jaxbXMLToObject(ReportCategories.class,
					props.getBasePath() + reportFile);
			executeReports(reportCategories, AppUtils.getFormattedDate(AppUtils.getPreviousDate()));
		}
	}

	/*// @Scheduled(fixedRate = 120000)
	// Cron for Monday 6:30 AM
	@Scheduled(cron = "0 30 6 * * *", zone = "GMT+05:30")
	public void paidEmployeeMealReport() throws ParseException, EmailGenerationException, IOException {
		if (props.getRunAutoReports()) {
			if (AppUtils.getCurrentDayofMonth() == props.getEmployeeMealMonthlyStartDate()) {
				ReportCategories reportCategories = JaxbUtil.jaxbXMLToObject(ReportCategories.class,
						props.getBasePath() + "/reports/EmployeeMealReport.xml");
				executeReports(reportCategories, AppUtils.getFormattedDate(AppUtils.getPreviousDate()));
			}
		}
	}*/

	@Scheduled(cron = "0 30 4 * * *", zone = "GMT+05:30")
	public void npsCron() throws ParseException, EmailGenerationException, IOException {
		if (props.getRunAutoReports()) {
			LOG.info("Running NPS Proc at {}", AppUtils.getTimeISTString(AppUtils.getCurrentTimestamp()));
			try {
				feedbackService.runNpsProc();
			} catch (Exception e) {
				LOG.error("Error While running NPS Proc");
			}
		}
	}

	@Override
	public EnvType getEnvironmentType() {
		return props.getEnvironmentType();
	}

	@Override
	public String getBasePath() {
		return props.getBasePath();
	}
}
