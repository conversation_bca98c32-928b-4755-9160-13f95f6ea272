package com.stpl.tech.kettle.service;

import com.stpl.tech.kettle.data.dao.NonWastageItemRepository;
import com.stpl.tech.kettle.data.model.NonWastageItem;
import com.stpl.tech.kettle.domain.model.Order;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Service
public class NonWastageItemService {

    @Autowired
    private NonWastageItemRepository nonWastageItemRepository;

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void saveNonWastageItems(Order order) {
        if (order == null || order.getNonWastageItemMap() == null || order.getNonWastageItemMap().isEmpty()) {
            return;
        }

        // Delete existing non-wastage items for this order
        nonWastageItemRepository.deleteByOrderId(order.getOrderId());

        // Create and save new non-wastage items
        List<NonWastageItem> nonWastageItems = new ArrayList<>();
        for (Map.Entry<Integer, Integer> entry : order.getNonWastageItemMap().entrySet()) {
            NonWastageItem item = new NonWastageItem();
            item.setOrderId(order.getOrderId());
            item.setUnitId(order.getUnitId());
            item.setItemId(entry.getKey());
            item.setQuantity(entry.getValue());
            nonWastageItems.add(item);
        }

        nonWastageItemRepository.saveAll(nonWastageItems);
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Map<Integer, Integer> getNonWastageItemsForOrder(Integer orderId) {
        List<NonWastageItem> items = nonWastageItemRepository.findByOrderId(orderId);
        Map<Integer, Integer> nonWastageMap = items.stream().collect(
            java.util.stream.Collectors.toMap(
                NonWastageItem::getItemId,
                NonWastageItem::getQuantity
            ));
        
        return nonWastageMap;
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public List<NonWastageItem> getNonWastageItemsByUnit(Integer unitId) {
        return nonWastageItemRepository.findByUnitId(unitId);
    }
} 