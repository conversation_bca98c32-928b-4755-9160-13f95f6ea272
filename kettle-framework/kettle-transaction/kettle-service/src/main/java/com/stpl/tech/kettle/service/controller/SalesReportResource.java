/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.service.controller;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.core.service.OrderSearchService;
import com.stpl.tech.kettle.core.service.ReportingService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.dao.DeliveryDao;
import com.stpl.tech.kettle.data.model.PartnerAttributes;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.domain.model.ReportStatusEvent;
import com.stpl.tech.kettle.reports.core.ReportAggregationType;
import com.stpl.tech.kettle.reports.core.ReportFrequency;
import com.stpl.tech.kettle.reports.core.ReportOutput;
import com.stpl.tech.kettle.reports.dao.impl.ReportFileData;
import com.stpl.tech.kettle.reports.external.ExternalReportUtil;
import com.stpl.tech.kettle.reports.external.impl.ExternalReportInputData;
import com.stpl.tech.kettle.reports.external.impl.ExternalReportOutputData;
import com.stpl.tech.kettle.reports.external.impl.logix.LogixReportExecutor;
import com.stpl.tech.kettle.reports.external.impl.moin.MoINReportExecutor;
import com.stpl.tech.kettle.reports.external.impl.pathfinder.PathFinderReportExecutor;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.notification.ReportEmailNotification;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.AttachmentData;

@Component
public class SalesReportResource {

	private static final String CONFIG_ATTRIBUTE_TENANT_ID = "TENANT_ID";

	private static final String CONFIG_ATTRIBUTE_REPORT_TYPE = "REPORT_TYPE";

	private static final String CONFIG_ATTRIBUTE_STATUS = "STATUS";

	private static final String CONFIG_ATTRIBUTE_SALES_REPORT = "SALES_REPORT";

	private static final String CONFIG_ATTRIBTE_REPORT_EMAIL = "REPORT_EMAIL";

	private static final Logger LOG = LoggerFactory.getLogger(SalesReportResource.class);

	@Autowired
	private OrderSearchService orderSearchService;
	@Autowired
	private EnvironmentProperties props;
	@Autowired
	private DeliveryDao deliveryDao;
	@Autowired
	private ReportingService reportingService;
	@Autowired
	private MasterDataCache cache;

	public EnvironmentProperties getEnvironmentProperties() {
		return props;
	}

	public DeliveryDao getDeliveryDao() {
		return deliveryDao;
	}

	public ReportingService getReportingService() {
		return reportingService;
	}

	public List<ReportOutput> generateTerminalSalesReport(Unit unit, List<Order> orders, Date businessDate) {
		return generateTerminalSalesReport(unit, orders, businessDate, ReportFrequency.EOD);
	}

	public List<ReportOutput> generateHourlyTerminalSalesReport(Unit unit, List<Order> orders, Date businessDate) {
		return generateTerminalSalesReport(unit, orders, businessDate, ReportFrequency.HOURLY);
	}

	public void generateHourlyTerminalSalesReport() throws DataNotFoundException {
		List<Integer> units = getDeliveryDao().getHourlyReportPartners();
		for (Integer i : units) {
			int startOrderId = orderSearchService.getLastDayCloseOrderId(i);
			int lastOrderId = orderSearchService.getLastOrderDetail(i);
			List<Order> ordersForTheDay = orderSearchService.getOrderDetailsForDay(i, startOrderId, lastOrderId);
			generateHourlyTerminalSalesReport(cache.getUnit(i), ordersForTheDay, AppUtils.getCurrentBusinessDate());
		}
	}

	private List<ReportOutput> generateTerminalSalesReport(Unit unit, List<Order> orders, Date businessDate,
			ReportFrequency f) {

		List<PartnerAttributes> attributeList = getDeliveryDao().getPartnerAttributeList(unit.getId(),
				CONFIG_ATTRIBUTE_SALES_REPORT);

		if (attributeList == null || attributeList.isEmpty()) {
			// EXIT IF NO Attributes
			return null;
		}

		Map<String, String> configMap = getConfigMap(attributeList);

		configMap.put(AppConstants.REPORT_FREQUENCY, f.name());

		if (!AppConstants.ACTIVE.equals(configMap.get(CONFIG_ATTRIBUTE_STATUS))) {
			LOG.info("CANCELLED Sending external report for unit:{}", unit.getId());
			return null;
		}
		List<ReportOutput> outputList = new ArrayList<>();
		boolean reportGenerated = false;
		String subject = (!AppUtils.isProd(props.getEnvironmentType()) ? props.getEnvironmentType().name() : "")
				+ "Chaayos " + f.getName() + " Report for " + unit.getName() + " at "
				+ AppUtils.getCurrentTimeISTString();
		try {
			LOG.info("Sending external report for : {}", subject);

			String reportType = configMap.get(CONFIG_ATTRIBUTE_REPORT_TYPE);
			if (ReportAggregationType.DLF_CONSOLIDATED.name().equals(reportType)) {
				outputList = getConsolidatedReports(unit, orders, businessDate, configMap);
			} else if (ReportAggregationType.DLF_TERMINAL_WISE.name().equals(reportType)) {
				outputList = getTerminalWiseReports(unit, orders, businessDate, configMap);
			} else if (ReportAggregationType.LOGIX_SIMPLE.name().equals(reportType)) {
				outputList = getSimpleConsolidatedReport(unit, orders, businessDate, configMap);
			} else if (ReportAggregationType.PATHFINDER.name().equals(reportType)) {
				outputList = getPathFinderReport(unit, orders, businessDate, configMap);
			}
			reportGenerated = true;
		} catch (Exception e) {
			String message = String.format("Error in sending external report FILE for : %s", subject);
			SlackNotificationService.getInstance().sendNotification(getEnvironmentProperties().getEnvironmentType(),
					AppConstants.KETTLE, SlackNotification.EXTERNAL_REPORTS, message);
			reportGenerated = false;
			LOG.error(message, e);
		}

		if (reportGenerated) {
			try {
				LOG.info("Sending email for external report: " + subject);
				String toEmails = "<EMAIL>";
				if (configMap.get(CONFIG_ATTRIBTE_REPORT_EMAIL) != null) {
					toEmails = toEmails + "," + configMap.get(CONFIG_ATTRIBTE_REPORT_EMAIL);
				}
				ReportEmailNotification email = new ReportEmailNotification(subject,
						getEnvironmentProperties().getEnvironmentType());
				email.setFromEmail("<EMAIL>");
				email.setToEmails(toEmails.split(","));
				email.setAttachFile(true);
				for (ReportOutput r : outputList) {
					email.getAttachmentData().addAll(convert(r.getReportFiles()));
				}
				email.sendRawMail(email.getAttachmentData());
			} catch (Exception e) {
				String message = String.format("Error in sending external report EMAIL for : %s", subject);
				SlackNotificationService.getInstance().sendNotification(getEnvironmentProperties().getEnvironmentType(),
						AppConstants.KETTLE, SlackNotification.EXTERNAL_REPORTS, message);
				LOG.error(message, e);
			}
		}
		return outputList;
	}

	private Collection<? extends AttachmentData> convert(List<ReportFileData> reportFiles)
			throws FileNotFoundException, IOException {
		List<AttachmentData> attachments = new ArrayList<AttachmentData>();
		for (ReportFileData fileData : reportFiles) {
			if (fileData.isGenerated()) {
				File file = new File(fileData.getFilePath());
				if (file.exists()) {
					AttachmentData reports = new AttachmentData(IOUtils.toByteArray(new FileInputStream(file)),
							fileData.getFileName(), fileData.getMimeType());
					attachments.add(reports);
				}
			}
		}
		return attachments;
	}

	private List<ReportOutput> getPathFinderReport(Unit unit, List<Order> orders, Date businessDate,
			Map<String, String> configMap) {
		List<ReportOutput> outputList = new ArrayList<>();
		try {
			ReportOutput reportOutput = generatePathFinderReport(unit, getOrderInfoList(orders), 1,
					AppConstants.ADMIN_USER_ID, businessDate, configMap);
			outputList.add(reportOutput);
		} catch (Exception e) {
			LOG.error("ERROR while generating consolidated sales report for unit {} ", unit.getId(), e);
		}
		return outputList;
	}

	private ReportOutput generatePathFinderReport(Unit unit, List<OrderInfo> orderInfoList, int terminalId,
			int adminUserId, Date businessDate, Map<String, String> configMap) {
		LOG.info("Generating PathFinder Sales Report for Unit #{} with {} orders at terminal #{}", unit.getId(),
				orderInfoList.size(), terminalId);
		ReportStatusEvent event = getReportStatusEvent(orderInfoList, businessDate, terminalId, adminUserId,
				unit.getId(), configMap.get(CONFIG_ATTRIBUTE_TENANT_ID));

		ExternalReportInputData inputData = new ExternalReportInputData(orderInfoList, unit, businessDate, event,
				getEnvironmentProperties(), configMap);
		ExternalReportOutputData outputData = new PathFinderReportExecutor(inputData).execute(inputData);
		closeReportEventStatus(event);
		return outputData;
	}

	private List<ReportOutput> getSimpleConsolidatedReport(Unit unit, List<Order> orders, Date businessDate,
			Map<String, String> configMap) {
		List<ReportOutput> outputList = new ArrayList<>();
		try {
			ReportOutput reportOutput = generateLogixSalesReport(unit, getOrderInfoList(orders), 1,
					AppConstants.ADMIN_USER_ID, businessDate, configMap);
			outputList.add(reportOutput);
		} catch (Exception e) {
			LOG.error("ERROR while generating consolidated sales report for unit {} ", unit.getId(), e);
		}
		return outputList;
	}

	private List<ReportOutput> getConsolidatedReports(Unit unit, List<Order> orders, Date businessDate,
			Map<String, String> configMap) {
		List<ReportOutput> outputList = new ArrayList<>();
		// FOR CONSOLIDATED
		try {
			ReportOutput reportOutput = generateMoINSalesReport(unit, getOrderInfoList(orders), 1,
					AppConstants.ADMIN_USER_ID, businessDate, configMap);
			outputList.add(reportOutput);
		} catch (Exception e) {
			LOG.error("ERROR while generating consolidated sales report for unit {} ", unit.getId(), e);
		}
		return outputList;
	}

	private List<ReportOutput> getTerminalWiseReports(Unit unit, List<Order> orders, Date businessDate,
			Map<String, String> configMap) {
		Map<Integer, List<Order>> terminalToOrdersMap = getTerminalToOrdersMap(unit, orders);
		List<ReportOutput> outputList = new ArrayList<>();
		// FOR CAFE & COD TERMINALS
		for (int terminal = 0; terminal <= unit.getNoOfTerminals(); terminal++) {
			try {
				ReportOutput reportOutput = generateMoINSalesReport(unit,
						getOrderInfoList(terminalToOrdersMap.get(terminal)), terminal, AppConstants.ADMIN_USER_ID,
						businessDate, configMap);
				outputList.add(reportOutput);
			} catch (Exception e) {
				LOG.error("ERROR while generating sales report for unit {} and terminal {}", unit.getId(), terminal, e);
			}
		}
		// FOR TAKE_AWAY TERMINALS
		for (int terminal = 1; terminal <= unit.getNoOfTakeawayTerminals(); terminal++) {
			try {
				int takeAwayTerminal = AppConstants.TAKEAWAY_SEED + terminal;
				ReportOutput reportOutput = generateMoINSalesReport(unit,
						getOrderInfoList(terminalToOrdersMap.get(takeAwayTerminal)), takeAwayTerminal,
						AppConstants.ADMIN_USER_ID, businessDate, configMap);
				outputList.add(reportOutput);
			} catch (Exception e) {
				LOG.error("ERROR while generating sales report for unit {} and terminal {}", unit.getId(), terminal, e);
			}
		}
		return outputList;
	}

	private ReportOutput generateLogixSalesReport(Unit unit, List<OrderInfo> orderInfos, int terminalId, int userId,
			Date businessDate, Map<String, String> configMap) {

		LOG.info("Generating Sales Report for Unit #{} with {} orders at terminal #{}", unit.getId(), orderInfos.size(),
				terminalId);

		ReportStatusEvent event = getReportStatusEvent(orderInfos, businessDate, terminalId, userId, unit.getId(),
				configMap.get(CONFIG_ATTRIBUTE_TENANT_ID));

		ExternalReportInputData inputData = new ExternalReportInputData(orderInfos, unit, businessDate, event,
				getEnvironmentProperties(), configMap);
		ExternalReportOutputData outputData = new LogixReportExecutor(inputData).execute(inputData);
		closeReportEventStatus(event);
		return outputData;
	}

	private ReportOutput generateMoINSalesReport(Unit unit, List<OrderInfo> orderInfos, int terminalId, int userId,
			Date businessDate, Map<String, String> configMap) {

		LOG.info("Generating Sales Report for Unit #{} with {} orders at terminal #{}", unit.getId(), orderInfos.size(),
				terminalId);

		ReportStatusEvent event = getReportStatusEvent(orderInfos, businessDate, terminalId, userId, unit.getId(),
				configMap.get(CONFIG_ATTRIBUTE_TENANT_ID));

		ExternalReportInputData inputData = new ExternalReportInputData(orderInfos, unit, businessDate, event,
				getEnvironmentProperties(), configMap);
		ExternalReportOutputData outputData = new MoINReportExecutor(inputData).execute(inputData);
		closeReportEventStatus(event);
		return outputData;
	}

	private ReportStatusEvent getReportStatusEvent(List<OrderInfo> orderInfos, Date businessDate, int terminalId,
			int userId, int unitId, String tenantId) {
		ReportStatusEvent previousEvent = getReportingService().getPeviousReportStatusEvent(unitId, terminalId);
		ReportStatusEvent reportEvent = null;
		int endOrderId = 0;
		int startOrderId = 0;

		Collections.sort(orderInfos, new Comparator<OrderInfo>() {
			@Override
			public int compare(OrderInfo o1, OrderInfo o2) {
				return o1.getOrder().getUnitOrderId().compareTo(o2.getOrder().getUnitOrderId());
			}
		});
		if (previousEvent == null) {
			endOrderId = orderInfos.isEmpty() ? 0 : orderInfos.get(orderInfos.size() - 1).getOrder().getUnitOrderId();
			startOrderId = orderInfos.isEmpty() ? 0 : orderInfos.get(0).getOrder().getUnitOrderId();
			reportEvent = createReportEventStatus(businessDate, endOrderId, startOrderId, 1, tenantId, unitId, userId,
					terminalId);
		} else {
			startOrderId = previousEvent.getEndOrderId();
			endOrderId = orderInfos.isEmpty() ? previousEvent.getEndOrderId()
					: orderInfos.get(orderInfos.size() - 1).getOrder().getUnitOrderId();
			reportEvent = createReportEventStatus(businessDate, endOrderId, startOrderId,
					ExternalReportUtil.calculateMoINFileNumber(previousEvent.getFileNum()), tenantId, unitId, userId,
					terminalId);
		}
		return reportEvent;
	}

	private ReportStatusEvent createReportEventStatus(Date businessDate, int endOrderId, int startOrderId, int fileNum,
			String tenantId, int unitId, int userId, int terminalId) {
		ReportStatusEvent event = new ReportStatusEvent();
		event.setUnitId(unitId);
		event.setTerminalId(terminalId);
		event.setBusinessDate(businessDate);
		event.setStartTime(AppUtils.getCurrentTimestamp());
		event.setEndOrderId(endOrderId);
		event.setStartOrderId(startOrderId);
		event.setEventStatus("OPENED");
		event.setFileNum(fileNum);
		event.setTenantId(tenantId);
		event.setUserId(userId);
		event.setDescription("SALES Report");
		return getReportingService().addReportStatusEvent(event);
	}

	private boolean closeReportEventStatus(ReportStatusEvent event) {
		event.setEventStatus("CLOSED");
		return getReportingService().updateReportStatusEvent(event);
	}

	private List<OrderInfo> getOrderInfoList(List<Order> orders)
			throws DataNotFoundException, TemplateRenderingException {
		List<OrderInfo> orderInfos = new ArrayList<>();
		if (orders != null) {
			for (Order o : orders) {
				if (OrderStatus.CANCELLED.equals(o.getStatus())) {
					continue;
				}
				orderInfos.add(orderSearchService.getOrderReceipt(o.getOrderId(), false, null));
			}
		}
		return orderInfos;
	}

	private Map<Integer, List<Order>> getTerminalToOrdersMap(Unit unit, List<Order> orders) {
		Map<Integer, List<Order>> map = new HashMap<>();
		for (Order order : orders) {
			if (OrderStatus.CANCELLED.equals(order.getStatus())) {
				continue;
			}
			if (TransactionUtils.isCODOrder(order.getSource())) {
				addToMap(0, order, map);
			} else {
				addToMap(order.getTerminalId(), order, map);
			}
		}
		return map;
	}

	private void addToMap(int key, Order order, Map<Integer, List<Order>> map) {
		if (map.containsKey(key)) {
			map.get(key).add(order);
		} else {
			map.put(key, new ArrayList<Order>());
			map.get(key).add(order);
		}
	}

	private Map<String, String> getConfigMap(List<PartnerAttributes> attributeList) {
		Map<String, String> configMap = new HashMap<String, String>();
		for (PartnerAttributes attribute : attributeList) {
			configMap.put(attribute.getMappingType(), attribute.getMappingValue());
		}
		return configMap;
	}

}
