/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.service.controller;

import static com.stpl.tech.kettle.service.core.ServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.service.core.ServiceConstants.DATA_LOAD_ROOT_CONTEXT;
import static com.stpl.tech.kettle.service.core.ServiceConstants.SEPARATOR;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import javax.ws.rs.core.MediaType;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import com.stpl.tech.kettle.core.service.OrderSearchService;
import com.stpl.tech.kettle.customer.service.CustomerService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.mongo.dao.CustomerDataDao;
import com.stpl.tech.kettle.data.mongo.dao.OrderDataDao;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.master.core.exception.DataNotFoundException;

/**
 * Root resource (exposed at "load-resources" path)
 */

/*
 * @Controller
 * 
 * @RequestMapping(value = API_VERSION + SEPARATOR + DATA_LOAD_ROOT_CONTEXT) //
 */// v1/load-resources
public class LoadResource {

	private static final Logger LOG = LoggerFactory.getLogger(LoadResource.class);

	//@Autowired
	private OrderSearchService service;
	//@Autowired
	private EnvironmentProperties props;
	//@Autowired
	private OrderDataDao orderDataDao;
	//@Autowired
	private CustomerService customerService;
	//@Autowired
	private CustomerDataDao customerDataDao;

	/*
	 * @RequestMapping(value = "/orders", method = RequestMethod.POST)
	 * 
	 * @ResponseBody
	 */
	public Boolean loadOrdersData(@RequestParam int batchSize, @RequestParam int threadPoolSize) {

		// Get max order id from Mongo = startOrderId
		try {
			Integer startOrderId;
			Order startOrder = orderDataDao.findFirstByOrderByOrderIdDesc();
			if (startOrder == null)
				startOrderId = 0;
			else
				startOrderId = startOrder.getOrderId();
			Boolean flag = true;
			// LOG.info("logging with startOrderId : " + startOrderId);
			while (flag) {
				// Start Your Batches Here
				List<Integer> orderIds = service.getSettleOrders(startOrderId, batchSize);
				if (orderIds.isEmpty())
					break;
				// Runtime.getRuntime().availableProcessors()
				ExecutorService taskExecutor = Executors.newFixedThreadPool((int) threadPoolSize);
				// List<Order> orders = new ArrayList<Order>();

				for (Integer orderId : orderIds) {
					taskExecutor.execute(() -> {
						Order order = null;
						try {
							order = service.getOrderDetail(orderId);
						} catch (DataNotFoundException e1) {
							e1.printStackTrace();
						}
						orderDataDao.save(order);
						// LOG.info("logging with thread " + Thread.currentThread().getName());
					});
				}

				taskExecutor.shutdown();
				try {
					taskExecutor.awaitTermination(Long.MAX_VALUE, TimeUnit.NANOSECONDS);
				} catch (InterruptedException e) {
					LOG.error("Error", e);
				}

				// max id in collections
				startOrderId = orderDataDao.findFirstByOrderByOrderIdDesc().getOrderId();

			}
		} catch (Exception e) {
			LOG.error("Error", e);
		}
		return true;
	}

	/*
	 * @RequestMapping(method = RequestMethod.POST, value = "load-customer-data",
	 * produces = MediaType.APPLICATION_JSON)
	 * 
	 * @ResponseBody
	 * 
	 * @ResponseStatus(HttpStatus.OK)
	 */
	public Boolean loadCustomerData(@RequestParam int batchSize, @RequestParam int threadPoolSize)
			throws DataNotFoundException {
		int startCustomerId;
		if (customerDataDao.findTopByOrderByIdDesc() == null) {
			startCustomerId = 0;
		} else {
			startCustomerId = customerDataDao.findTopByOrderByIdDesc().getId();
		}
		while (true) {
			List<Integer> customerIds = customerService.getCustomerIds(startCustomerId, batchSize);
			if (customerIds.isEmpty()) {
				break;
			}
			ExecutorService taskExecutor = Executors.newFixedThreadPool(threadPoolSize);
			for (Integer customerId : customerIds) {
				taskExecutor.execute(() -> {
					Customer customer = null;
					try {
						customer = customerService.getCustomer(customerId);
					} catch (DataNotFoundException e) {
						LOG.error("Error while saving customer  info", e);
					}
					customerDataDao.save(customer);
					// LOG.info("running thread is" + Thread.currentThread().getName());
				});
			}
			startCustomerId = customerIds.get(customerIds.size() - 1);
			taskExecutor.shutdown();
			try {
				taskExecutor.awaitTermination(Long.MAX_VALUE, TimeUnit.NANOSECONDS);
			} catch (InterruptedException e) {
				LOG.error("Error in completion of  threads", e);
				return false;
			}
		}
		return true;
	}
}