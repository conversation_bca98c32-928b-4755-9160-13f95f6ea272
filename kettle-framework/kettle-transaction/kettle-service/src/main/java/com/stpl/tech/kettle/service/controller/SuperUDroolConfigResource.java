package com.stpl.tech.kettle.service.controller;

import com.stpl.tech.kettle.core.service.DroolsDecisionService;
import com.stpl.tech.kettle.data.model.SuperUDomain;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static com.stpl.tech.kettle.service.core.ServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.service.core.ServiceConstants.RECOM_OFFER_RESOURCE;
import static com.stpl.tech.kettle.service.core.ServiceConstants.SEPARATOR;
import static com.stpl.tech.kettle.service.core.ServiceConstants.SUPER_U_RESOURCE;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + SUPER_U_RESOURCE)
@Log4j2
public class SuperUDroolConfigResource {

    @Autowired
    DroolsDecisionService droolsDecisionService;

    @PostMapping("/superu-params")
    public SuperUDomain getSuperParams(@RequestBody SuperUDomain superUDomain){
        log.info("Get super u params");
        return droolsDecisionService.getSuperUParam(superUDomain);
    }

}
