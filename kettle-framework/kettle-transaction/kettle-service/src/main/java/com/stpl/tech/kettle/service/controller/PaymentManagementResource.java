
/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.service.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import com.stpl.tech.kettle.core.exception.PaymentFailureException;
import com.stpl.tech.kettle.core.service.ForwardRequestService;
import com.stpl.tech.kettle.core.service.IngenicoPaymentService;
import com.stpl.tech.kettle.core.service.PaymentService;
import com.stpl.tech.kettle.core.service.PaymentServiceNew;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.model.StandaloneTransactionDetail;
import com.stpl.tech.kettle.delivery.model.PaymentPartner;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.master.payment.model.AGS.AGSCreateRequest;
import com.stpl.tech.master.payment.model.AGS.AGSPaymentCMResponse;
import com.stpl.tech.master.payment.model.AGS.AGSPaymentCMStatus;
import com.stpl.tech.master.payment.model.AGS.AGSPaymentS2SResponse;
import com.stpl.tech.master.payment.model.AGS.AGSPaymentS2SStatus;
import com.stpl.tech.master.payment.model.OrderPayment;
import com.stpl.tech.master.payment.model.OrderPaymentRequest;
import com.stpl.tech.master.payment.model.PaymentRefundRequest;
import com.stpl.tech.master.payment.model.PaymentRequest;
import com.stpl.tech.master.payment.model.PaymentStatusChangeRequest;
import com.stpl.tech.master.payment.model.PaymentVO;
import com.stpl.tech.master.payment.model.ezetap.EzetapCreateRequest;
import com.stpl.tech.master.payment.model.ezetap.EzetapPaymentResponse;
import com.stpl.tech.master.payment.model.gpay.GPayPaymentStatus;
import com.stpl.tech.master.payment.model.gpay.GPayQRResponse;
import com.stpl.tech.master.payment.model.ingenico.IngenicoQrResponse;
import com.stpl.tech.master.payment.model.patymNew.PaytmFetchPaymentOptionsRequest;
import com.stpl.tech.master.payment.model.patymNew.PaytmOauth;
import com.stpl.tech.master.payment.model.patymNew.PaytmOauthRequest;
import com.stpl.tech.master.payment.model.patymNew.PaytmOauthResponse;
import com.stpl.tech.master.payment.model.patymNew.PaytmParamResponse;
import com.stpl.tech.master.payment.model.patymNew.PaytmParams;
import com.stpl.tech.master.payment.model.patymNew.PaytmStatusResponse;
import com.stpl.tech.master.payment.model.patymNew.PaytmValidateSSOTokenResponse;
import com.stpl.tech.master.payment.model.paytm.PaytmCreateRequest;
import com.stpl.tech.master.payment.model.paytm.PaytmCreateRequestResponseWrapper;
import com.stpl.tech.master.payment.model.paytm.PaytmCreateResponse;
import com.stpl.tech.master.payment.model.paytm.PaytmPaymentStatus;
import com.stpl.tech.master.payment.model.paytmUpi.PaytmUpiQrResponse;
import com.stpl.tech.master.payment.model.razorpay.BasicTransactionInfo;
import com.stpl.tech.master.payment.model.razorpay.RazorPayCreateRequest;
import com.stpl.tech.master.payment.model.razorpay.RazorPayEventData;
import com.stpl.tech.master.payment.model.razorpay.RazorPayPaymentResponse;
import com.stpl.tech.util.JSONSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.util.List;
import java.util.Map;

import static com.stpl.tech.kettle.service.core.ServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.service.core.ServiceConstants.API_VERSION_V2;
import static com.stpl.tech.kettle.service.core.ServiceConstants.API_VERSION_V3;
import static com.stpl.tech.kettle.service.core.ServiceConstants.PAYMENT_MANAGEMENT_ROOT_CONTEXT;
import static com.stpl.tech.kettle.service.core.ServiceConstants.SEPARATOR;

/**
 * Root resource (exposed at "order-management" path)
 */
@Deprecated
@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + PAYMENT_MANAGEMENT_ROOT_CONTEXT) // v1/payment-management
public class PaymentManagementResource extends AbstractResources {

	private static final Logger LOG = LoggerFactory.getLogger(PaymentManagementResource.class);

	@Autowired
	private PaymentService payment;

	@Autowired
	private EnvironmentProperties props;

	@Autowired
	private IngenicoPaymentService ingenicoPaymentService;

	@Autowired
	private ForwardRequestService forwardRequestService;

	@RequestMapping(method = RequestMethod.POST, value = "payment/create", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public PaymentRequest processPayment(@RequestBody final OrderPaymentRequest order) throws Exception {
		LOG.info("V1 Requesting Payment for order " + order.getGenerateOrderId());
		return payment.createRequest(order);
	}

	@RequestMapping(method = RequestMethod.POST, value = "payment/cancel", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public Boolean cancelPayment(@RequestBody final PaymentStatusChangeRequest cancel) throws Exception {
		LOG.info("V1 Requesting Cancellation of Payment Request with Id " + cancel.getReceiptId());
		return payment.cancelPayment(cancel);
	}

	@RequestMapping(method = RequestMethod.POST, value = "payment/failure", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public Boolean paymentFailure(@RequestBody final PaymentStatusChangeRequest failure) throws Exception {
		LOG.info("V1 Updating Failure of Payment Request with Id " + failure.getReceiptId());
		return payment.failurePayment(failure);
	}

	@RequestMapping(method = RequestMethod.POST, value = "payment/razorpay/create", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public RazorPayCreateRequest createRazorPayPayment(@RequestBody final OrderPaymentRequest order) throws Exception {
		LOG.info("V1 Requesting Razor Pay Payment for order " + order.getGenerateOrderId());
		LOG.info("Requesting Razor Pay Payment for order {} ", order);
		return payment.createRazorPayRequest(order);
	}

	@RequestMapping(method = RequestMethod.POST, value = "payment/ezetap/create", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public EzetapCreateRequest createEzetapPayment(@RequestBody final OrderPaymentRequest order) throws Exception {
		LOG.info("V1 Requesting Ezetap Payment for order " + order.getGenerateOrderId());
		return payment.createEzetapRequest(order);
	}

	@RequestMapping(method = RequestMethod.POST, value = "payment/ingenico/create", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public IngenicoQrResponse createIngenicoRequest(@RequestBody final OrderPaymentRequest order) throws Exception {
		LOG.info("V1 Requesting Ingenico Payment for order " + order.getGenerateOrderId());
		order.setPaymentModeId(PaymentPartner.INGENICO.getSystemId(null));
		return ingenicoPaymentService.createRequest(order);
	}

    @RequestMapping(method = RequestMethod.POST, value = "payment/ingenico/paymentStatus", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
    public IngenicoQrResponse getIngenicoPaymentStatus(@RequestBody final OrderPaymentRequest order) throws Exception {
        LOG.info("V1 Checking Ingenico Payment for order " + order.getGenerateOrderId());
        order.setPaymentModeId(PaymentPartner.INGENICO.getSystemId(null));
        return ingenicoPaymentService.checkIngenicoPaymentStatus(order);
    }

    // Webhook call for ingenico
	@RequestMapping(method = RequestMethod.GET, value = "payment/ingenico/update", produces = MediaType.APPLICATION_JSON)
	public IngenicoQrResponse updateIngenicoRequest(@RequestParam final String msg, @RequestParam final String tpsl_mrct_cd)
			throws PaymentFailureException {
		LOG.info("V1 Encrypted Response for payment status from ingenico {} ", msg);
		return payment.validateIngenicoCallback(msg);
	}

	@RequestMapping(method = RequestMethod.POST, value = "payment/transaction/failed", produces = MediaType.APPLICATION_JSON)
	public void markTransactionCancelOrFail(@RequestBody final OrderPaymentRequest order)
			throws Exception {
		LOG.info("V1 Mark transaction fail/cancel for order id:  ", order.getGenerateOrderId());
		payment.markTransactionCancel(order);
	}

	@RequestMapping(method = RequestMethod.POST, value = "payment/ingenico/paymentMode/update", produces = MediaType.APPLICATION_JSON)
	public void updateIngenicoRequest(@RequestBody final OrderPaymentRequest order) throws Exception {
		LOG.info("V1 Updating payment mode name of ingenico for order: ", order.getGenerateOrderId());
		ingenicoPaymentService.updatePaymentModeForOrder(order);
	}


	@RequestMapping(method = RequestMethod.POST, value = "payment/paytm/create", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public PaytmCreateRequest createPaytmPayment(@RequestBody final OrderPaymentRequest order) throws Exception {
		LOG.info("V1 Requesting Paytm Payment for order " + order.getGenerateOrderId());
		LOG.info("JSON Order " + JSONSerializer.toJSON(order));
		return payment.createPaytmRequest(order);
	}

	@RequestMapping(method = { RequestMethod.POST,
			RequestMethod.GET }, value = "payment/paytm/update", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public Map processPayment(@RequestBody Map parameters) throws Exception {
		String orderJson = JSONSerializer.toJSON(parameters);
		ObjectMapper mapper = new ObjectMapper();
		PaytmCreateResponse paytmCreateResponse = mapper.readValue(orderJson, PaytmCreateResponse.class);
		LOG.info("V1 Response received for Payment for order " + orderJson);
		return payment.updatePaytmResponse(paytmCreateResponse);
	}

	@RequestMapping(method = RequestMethod.POST, value = "payment/razorpay/update", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public void processPayment(@RequestBody final RazorPayPaymentResponse response,@RequestParam(required = false) Integer brandId) throws Exception {
		String orderJson = JSONSerializer.toJSON(response);
		LOG.info("V1 Response received for Payment for order " + orderJson);
		payment.updateRazorPayResponse(response,brandId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "payment/razorpay/update/dinein", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public Map processPaymentForDineIn(@RequestBody final RazorPayPaymentResponse response,@RequestParam(required = false) Integer brandId) throws Exception {
		String orderJson = JSONSerializer.toJSON(response);
		LOG.info("V1 Response received for Payment for order " + orderJson);
		return payment.updateRazorPayResponse(response,brandId);
	}



	@RequestMapping(method = RequestMethod.POST, value = "payment/razorpay/update/dinein/no-v", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public Map processPaymentForDineInWithoutVerification(@RequestBody final RazorPayPaymentResponse response) throws Exception {
		String orderJson = JSONSerializer.toJSON(response);
		LOG.info("V1 Response received for Payment for order " + orderJson);
		return payment.updateRazorPayResponseWithoutVerification(response);
	}

	@RequestMapping(method = RequestMethod.POST, value = "payment/ezetap/update", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public void processEzetapPayment(@RequestBody final EzetapPaymentResponse response) throws Exception {
		String orderJson = JSONSerializer.toJSON(response);
		LOG.info("V1 Response received for Payment for order " + orderJson);
		payment.updateEzetapResponse(response);
	}

	@RequestMapping(method = RequestMethod.POST, value = "payment/razorpay/fetch", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public RazorPayPaymentResponse fetchPayment(@RequestBody final Map<String, Object> data) throws Exception {
		LOG.info("V1 Fetching payment Details for payment Id " + JSONSerializer.toJSON(data));
		return payment.fetchRazorPayPayment((String) data.get("paymentId"));
	}

    @RequestMapping(method = RequestMethod.POST, value = "payment/razorpay/fetch/dinein", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    public RazorPayPaymentResponse fetchPaymentDineIn(@RequestBody final Map<String, Object> data) throws Exception {
        LOG.info("V1 Fetching payment Details for payment Id " + JSONSerializer.toJSON(data));
        return payment.fetchResponseByRazorPayByPartnerOrderId((String) data.get("partnerOrderId"),(Integer) data.get("brandId"));
    }

	@RequestMapping(method = RequestMethod.POST, value = "payment/razorpay/event", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public void razorPayWebHook(@RequestBody RazorPayEventData data) throws DataNotFoundException {
		LOG.info("V1 Got Razor Pay Update Request {}", JSONSerializer.toJSON(data));
		payment.createPaymentEvent(data);
	}

	@RequestMapping(method = RequestMethod.POST, value = "payment/razorpay/event/covid", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public void razorPayWebHookStandalone(@RequestBody RazorPayEventData event) throws DataNotFoundException {
		BasicTransactionInfo info = new BasicTransactionInfo();
		info.setCampaignDescription("Covid 19 Relief Fund");
		info.setCampaignId("COVID19");
		info.setEmailFailureNotification(false);
		info.setEmailSuccessNotification(true);
		info.setSmsFailureNotification(true);
		info.setSmsSuccessNotification(false);
		event.setInfo(info);
		LOG.info("V1 Got Razor Pay Update Standalone Request {}", JSONSerializer.toJSON(event));
		StandaloneTransactionDetail transaction = payment.createStandalonePaymentEvent(event);
		if (transaction != null) {
			payment.sendStandaloneNotification(transaction.getPaymentId(), event.getEvent(), event.getInfo());
		}
	}

	@RequestMapping(method = RequestMethod.POST, value = "payment/check", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public Boolean checkPaymentStatus(@RequestBody PaymentVO paymentVO) throws DataNotFoundException {
		LOG.info("V1 Request to check payment status of {}", JSONSerializer.toJSON(paymentVO));
		return payment.checkPaymentStatus(paymentVO);
	}

	@RequestMapping(method = RequestMethod.POST, value = "payment/hanging-payment", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public OrderPayment getDisassociatedPayment(@RequestBody String contact) throws DataNotFoundException, PaymentFailureException {
		LOG.info("V1 Request to get hanging payment for contact {}", contact);
		payment.updateStatusOfDisassociatedPayments(contact);
		return payment.getDisassociatedPayment(contact);
	}

	@RequestMapping(method = RequestMethod.GET, value = "payment/pending-refunds", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public List<OrderPayment> getPendingRefunds() throws DataNotFoundException {
		LOG.info("V1 Request to get pending refunds for last three days");
		return payment.getPendingRefunds();
	}

	@RequestMapping(method = RequestMethod.POST, value = "payment/refund", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public OrderPayment refundPayment(@RequestBody Integer paymentId) throws DataNotFoundException,
			IOException, PaymentFailureException {
		LOG.info("V1 Request to get pending refunds for last three days");
		return payment.refundByPaymentId(paymentId);
	}

	@RequestMapping(method = RequestMethod.POST, value = "payment/paytm/kiosk/qr/create", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public PaytmCreateRequest createKIOSKPaytmQRCodeId(@RequestBody final OrderPaymentRequest order) throws Exception {
		LOG.info("V1 Creating KIOSK Paytm QR code Id for orderId " + order.getGenerateOrderId());
		return payment.getPayTMQRCodeIdForKIOSK(order);
	}

	// will be used to check payment status of both PAYTM and PAYTM UPI
	@RequestMapping(method = RequestMethod.POST, value = "payment/paytm/kiosk/qr/status", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public PaytmPaymentStatus checkKIOSKPaytmQRPaymentStatus(@RequestBody final OrderPaymentRequest order) throws Exception {
		LOG.info("V1 Checking KIOSK Paytm QR payment status " + order.getGenerateOrderId());
		return payment.checkKIOSKPaytmQRPaymentStatus(order.getGenerateOrderId());
	}

	@RequestMapping(method = RequestMethod.POST, value = "payment/gpay/qr/create", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public GPayQRResponse createGpayQRCodeId(@RequestBody final OrderPaymentRequest order) throws Exception {
		LOG.info("V1 Creating GPay QR code Id for orderId " + order.getGenerateOrderId());
		return payment.getGPayQRCode(order);
	}

	// will be used to check payment status of both PAYTM and PAYTM UPI
	@RequestMapping(method = RequestMethod.POST, value = "payment/gpay/qr/status", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public GPayPaymentStatus checkGpayQRPaymentStatus(@RequestBody final OrderPaymentRequest order) throws Exception {
		LOG.info("V1 Checking GPay QR payment status " + order.getGenerateOrderId());
		return payment.checkGPayQRPaymentStatus(order);
	}


	@RequestMapping(method = RequestMethod.POST, value = "payment/paytm/kiosk/qr/refund", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public boolean refundKIOSKPaytmQRPaymentAmount(@RequestBody final PaymentRefundRequest refundRequest) throws Exception {
		LOG.info("V1 Refunding KIOSK Paytm QR payment amount " + refundRequest.getOrderId());
		return payment.refundKIOSKPaytmQRPaymentAmount(refundRequest.getOrderId(), refundRequest.getRefundAmount(),
				refundRequest.getRefundReason());
	}

	// To create QR for paytm upi
	@RequestMapping(method = RequestMethod.POST, value = "payment/paytm-upi/qr/create", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public PaytmUpiQrResponse createPaytmUpiRequest(@RequestBody final OrderPaymentRequest order) throws Exception {
		LOG.info("V1 Requesting to generate paytm upi QR for order: " + order.getGenerateOrderId());
		order.setPaymentModeId(PaymentPartner.PAYTM.getSystemId(null));
		return payment.getPayTmUpiQRCodeId(order);
	}

	// refund payment of paytm upi qr
	@RequestMapping(method = RequestMethod.POST, value = "payment/paytm-upi/qr/refund", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public OrderPayment refundPaytmUpiPayment(@RequestBody final OrderPayment refundRequest) throws Exception {
		LOG.info("V1 Refunding Paytm UPI QR payment amount " + refundRequest.getOrderId());
		return payment.refundPaytmUpiQR(refundRequest);
	}

	// check status of refund upi payment
	@RequestMapping(method = RequestMethod.POST, value = "payment/paytm-upi/refund/status", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public OrderPayment checkRefundPaytmUpiStatus(@RequestBody final OrderPayment refundRequest) throws Exception {
		LOG.info("V1 Check refund status of paytm upi QR for order: " + refundRequest.getOrderId());
		return payment.refundPaytmUpiQR(refundRequest);
	}

	/**
	 * Webhook call to get status of payTM upi
	* @param request
	 * @throws Exception
	 */
	@RequestMapping(method = RequestMethod.POST, value = "payment/paytm-upi/status", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	public void getPayTMUpiStatus(HttpServletRequest request, HttpServletResponse response) throws Exception {
		LOG.info("V1 Enter getPayTMUPIStatus ");
		//TODO: Below forwarding code will be commented/Removed once we move to production after dev test.
		LOG.info("Forwarding UPI status to dev server ");
		forwardRequestService.forwardRequest("http://dev.kettle.chaayos.com:9595", "POST", request, response);

		//TODO: Below commented code will be restored on dev for testing this S2S service.
		/*String requestBody = new String();
		requestBody = request.getReader().lines().collect(Collectors.joining(System.lineSeparator()));
		if(StringUtils.isNotBlank(requestBody)){
			LOG.info("Enter getPayTMUPIStatus ");
			org.codehaus.jackson.map.ObjectMapper objectMapper =  new org.codehaus.jackson.map.ObjectMapper();
			PaytmUpiS2SResponse upiStatus = objectMapper.readValue(requestBody, PaytmUpiS2SResponse.class);
			LOG.info("UPI S2S response received : " + JSONSerializer.toJSON(upiStatus));
			payment.updatePaytmUpiStatus(upiStatus);
		}*/
	}

	@RequestMapping(method = RequestMethod.POST, value = "payment/ags/create", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public AGSCreateRequest createAGSPayment(@RequestBody final OrderPaymentRequest order) throws Exception {
		LOG.info("V1 Requesting AGS Payment for order " + order.getGenerateOrderId());
		return payment.createAGSRequest(order);
	}

	@RequestMapping(method = RequestMethod.POST, value = "payment/ags/update", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public void processAGSPayment(@RequestBody final AGSPaymentCMStatus cardMachineStatus) throws Exception {
		String orderJson = JSONSerializer.toJSON(cardMachineStatus);
		LOG.info("V1 Updating AGS response update " + orderJson);
		payment.updateAGSPaymentCMStatus(cardMachineStatus);
	}

	@RequestMapping(method = RequestMethod.POST, value = "payment/ags/status", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public AGSPaymentS2SResponse updateAGSPaymentStatus(@RequestBody final AGSPaymentS2SStatus serverToServerStatus) throws Exception {
		LOG.info("V1 Response received from AGS");
		String statusRequestJson = JSONSerializer.toJSON(serverToServerStatus);
		LOG.info("Payment status received for AGS Payment for order " + statusRequestJson);
		return payment.updateAGSPaymentS2SStatus(serverToServerStatus);
	}

	@RequestMapping(method = RequestMethod.POST, value = "payment/ags/status/check", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public AGSPaymentCMResponse checkAGSPaymentStatus(@RequestBody final String externalOrderId) throws Exception {
		LOG.info("V1 Check payment status of AGS Payment for order " + externalOrderId);
		return payment.checkAGSPaymentS2SStatus(externalOrderId);
	}

	//@Scheduled(fixedRate = 600000)
	public void autoRefunds(){
		payment.initiateAutoRefunds();
	}


	///////////////////////////////////////////////////////////////////////////////////
	//////////////////    DINE IN APP PAYTM PAYMENT APIS       ////////////////////////
	///////////////////////////////////////////////////////////////////////////////////


	@RequestMapping(method = RequestMethod.POST, value = "payment/paytm/dinein/create", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public PaytmCreateRequestResponseWrapper createPaytmPaymentForDineIn(
			@RequestBody final OrderPaymentRequest order,
			@RequestParam final String ssoToken) throws Exception {
		LOG.info("V1 Requesting Paytm Payment for order " + order.getGenerateOrderId());
		LOG.info("JSON Order " + JSONSerializer.toJSON(order));
		return payment.createPaytmRequestForDineIn(order, ssoToken);
	}

	@RequestMapping(method = { RequestMethod.POST,
		RequestMethod.GET }, value = "payment/paytm/dinein/update", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public Map processPaymentForDineIn(@RequestBody PaytmCreateResponse paytmCreateResponse) throws Exception {
		String orderJson = JSONSerializer.toJSON(paytmCreateResponse);
		LOG.info("V1 Response received for Payment for order " + orderJson);
		return payment.updatePaytmResponse(paytmCreateResponse, true);
	}

	@RequestMapping(method = { RequestMethod.POST,
			RequestMethod.GET }, value = "payment/paytm/dinein/update/no-v", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public Map processPaymentForDineInWithoutverification(@RequestBody PaytmCreateResponse paytmCreateResponse) throws Exception {
		String orderJson = JSONSerializer.toJSON(paytmCreateResponse);
		LOG.info("V1 Response received for Payment for order " + orderJson);
		return payment.updatePaytmResponseWithoutVerification(paytmCreateResponse);
	}

	@RequestMapping(method = { RequestMethod.GET,
			RequestMethod.GET }, value = "payment/paytm/dinein/status", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public PaytmStatusResponse getPaytmStatusResponse(@RequestParam String orderId) throws Exception {
		LOG.info("V1 getPaytmStatusResponse ");
    	return payment.getPaytmStatusResponse(orderId);
	}

	@Deprecated
	@RequestMapping(method = { RequestMethod.POST,
			RequestMethod.GET }, value = "payment/paytm/dinein/initiate", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public PaytmParamResponse getToken(@RequestBody PaytmParams paytmParams) throws Exception {
    	LOG.info("V1 paytm initiate txn called");
		return payment.initiatePaytmPayment(paytmParams);
	}

	@RequestMapping(method = { RequestMethod.POST,
			RequestMethod.GET }, value = "payment/paytm/dinein/oauth", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public PaytmOauth getPaytmOauth(@RequestBody PaytmOauthRequest paytmOauthRequest) throws Exception {
		LOG.info("V1 paytm oauth called");
		return payment.getPaytmOauth(paytmOauthRequest);
	}

	@RequestMapping(method = RequestMethod.POST, value = "payment/paytm/dinein/oauth2v3", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public PaytmOauthResponse getPaytmOauth2V3(@RequestBody PaytmOauthRequest paytmOauthRequest) throws Exception {
		LOG.info("V1 paytm oauth2V3 called");
		return payment.getPaytmOauth2V3(paytmOauthRequest);
	}

	@RequestMapping(method = RequestMethod.POST, value = "payment/paytm/dinein/revokeToken", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public boolean revokePaytmToken(@RequestBody PaytmOauthRequest paytmOauthRequest) {
		LOG.info("V1 paytm revoke token called");
		return payment.revokePaytmToken(paytmOauthRequest);
	}

	@RequestMapping(method = RequestMethod.POST, value = "payment/paytm/dinein/validateToken", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public PaytmValidateSSOTokenResponse validatePaytmSSOToken(@RequestBody String ssoToken) {
		LOG.info("V1 paytm validate SSO token called:" + ssoToken);
		String token = new Gson().fromJson(ssoToken, String.class);
		LOG.info("paytm sso token parsed:" + token);
		return payment.validatePaytmSSOToken(token);
	}

	@RequestMapping(method = RequestMethod.POST, value = "payment/paytm/dinein/access", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public String getPaytmAccessToken(@RequestBody PaytmFetchPaymentOptionsRequest request) throws Exception {
		LOG.info("V1 paytm access token called" + new Gson().toJson(request));
		return payment.getPaytmAccessToken(request);
	}

	@RequestMapping(method = RequestMethod.POST, value = "payment/paytm/dinein/options", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public String fetchPaytmPaymentOptions(@RequestBody PaytmFetchPaymentOptionsRequest request) {
		LOG.info("V1 paytm fetch options called");
		return payment.fetchPaytmPaymentOptions(request);
	}

	@RequestMapping(method = RequestMethod.POST, value = "payment/paytm/dinein/options/v2", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	public String fetchPaytmPaymentOptionsV2(@RequestBody PaytmFetchPaymentOptionsRequest request) {
		LOG.info("V1 paytm fetch options v2 called"+ new Gson().toJson(request));
		return payment.fetchPaytmPaymentOptionsV2(request);
	}
}
