package com.stpl.tech.kettle.service.model;

import com.stpl.tech.master.domain.model.UnitCategory;

import java.util.ArrayList;
import java.util.List;

public class OrderSettlementChangeRequest {

	private int unitId;
	private int orderId;
	private int editedBy;
	private List<OrderSettlementData> details;
	private UnitCategory orderSource;

	public int getOrderId() {
		return orderId;
	}

	public void setOrderId(int orderId) {
		this.orderId = orderId;
	}

	public List<OrderSettlementData> getDetails() {
		if (details == null) {
			details = new ArrayList<OrderSettlementData>();
		}
		return details;
	}

	public void setDetails(List<OrderSettlementData> details) {
		this.details = details;
	}

	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	public int getEditedBy() {
		return editedBy;
	}

	public void setEditedBy(int editedBy) {
		this.editedBy = editedBy;
	}


	public UnitCategory getOrderSource() {
		return orderSource;
	}

	public void setOrderSource(UnitCategory orderSource) {
		this.orderSource = orderSource;
	}
}
