/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.service.report;

import java.util.List;

import com.stpl.tech.kettle.core.notification.RawPrintReceipt;
import com.stpl.tech.kettle.reports.model.ProductDimensionConsumption;
import com.stpl.tech.kettle.reports.model.ProductPriceConsumption;
import com.stpl.tech.kettle.reports.model.ProductSourceConsumption;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;

public class ItemConsumptionRawReceipt extends RawPrintReceipt {
	private Unit unit;
	private List<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>> productConsumptions;
	private String basePath;
	private String generationTime = AppUtils.getCurrentTimeISTString();

	public ItemConsumptionRawReceipt(String basePath, Unit unit,
			List<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>> productConsumptions) {
		this.unit = unit;
		this.productConsumptions = productConsumptions;
		this.basePath = basePath;
	}

	public String getFilepath() {
		return basePath + "/" + unit.getId() + "/reports/ItemConsumptionReceipt-" + unit.getName() + "-"
				+ AppUtils.getCurrentTimeISTStringWithNoColons() + ".html";
	}

	@Override
	public StringBuilder processData() {

		reset();
		left(rpad("Unit Name:", 18) + unit.getName());
		left(rpad("Report Name:", 18) + "Item Consumption Report");
		left(rpad("Generation Time:", 18) + generationTime);
		separator();
		left(rpad("Item", 20) + lpad("Source", 9) + lpad("Qty", 4) + lpad("Combo", 6) + lpad("Compl.", 7));

		List<String> nameSplit = null;
		String name = null;
		for (ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>> source : productConsumptions) {
			for (ProductDimensionConsumption<ProductPriceConsumption> product : source.getAllItems()) {
				if (product.getQuantity() > 0 || product.getComplimentaryQuantity() > 0
						|| product.getCompositeQuantity() > 0) {

					name = product.getName();
					if (!AppConstants.NO_DIMENSION_STRING.equals(product.getDimension())) {
						name = name + " " + product.getDimension();
					}
					nameSplit = wordList(name, 20);
					int i = 0;
					for (String s : nameSplit) {
						if (i == 0) {
							left(rpad(s.trim(), 20) + lpad(source.getName(), 9) + lpad(product.getQuantity(), 4)
									+ lpad(product.getCompositeQuantity(), 6)
									+ lpad(product.getComplimentaryQuantity(), 6));
						} else {
							left(s.trim());
						}
						i++;
					}
				}
			}
		}
		cut();
		return getSb();
	}
}
