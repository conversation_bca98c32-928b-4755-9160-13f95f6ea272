/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.service.controller;

import com.stpl.tech.kettle.core.service.CustomerOfferManagementService;
import com.stpl.tech.kettle.core.service.PosMetadataService;
import com.stpl.tech.kettle.customer.service.CustomerService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.model.CustomerCampaignJourney;
import com.stpl.tech.kettle.data.model.WebOfferCouponRedemptionDetail;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.acl.service.CSRFTokenService;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.notification.service.NotificationService;
import com.stpl.tech.master.core.external.notification.service.SMSClientProviderService;
import com.stpl.tech.master.core.external.offer.service.OfferManagementService;
import com.stpl.tech.master.core.notification.sms.SMSType;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.master.data.model.LaunchOfferData;
import com.stpl.tech.master.data.model.OfferDetailData;
import com.stpl.tech.master.data.model.SignupOffersCouponDetails;
import com.stpl.tech.master.domain.model.ApplicationName;
import com.stpl.tech.master.domain.model.CouponDetail;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.OfferDetail;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.stpl.tech.kettle.service.core.ServiceConstants.API_VERSION;
import static com.stpl.tech.kettle.service.core.ServiceConstants.OFFER_MANAGEMENT_ROOT_CONTEXT;
import static com.stpl.tech.kettle.service.core.ServiceConstants.SEPARATOR;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + OFFER_MANAGEMENT_ROOT_CONTEXT) // 'v1/offer-management'
public class OfferManagementResource extends AbstractResources {

    private static final Logger LOG = LoggerFactory.getLogger(OfferManagementResource.class);
    private static final SimpleDateFormat DATE_FORMATTER = new SimpleDateFormat("dd MMM");
    private static final Pattern MOBILE_NUMBER_REGEX_PATTERN = Pattern.compile("[6-9][0-9]{9}");

    @Autowired
    private OfferManagementService offerService;

    @Autowired
    private CustomerService customerService;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private NotificationService notificationService;

    @Autowired
    private SMSClientProviderService providerService;

    @Autowired
    private CSRFTokenService tokenService;

    @Autowired
    private PosMetadataService metadataService;

    @Autowired
    private CustomerOfferManagementService customerOfferManagementService;

    @Autowired
    private EnvironmentProperties props;

    private LaunchOfferData getFreeItemCouponCode(LaunchOfferData input, boolean checkExistingCustomer,
                                                  boolean addUnitMapping) {

        String error = null;
        LOG.info("Token Received ::: " + input.getToken());
        if (tokenService.contains(input.getToken())) {
            tokenService.remove(input.getToken());
        } else {
            error = "Invalid token for the request";
            input.setErrorMessage(error);
            return input;
        }

        OfferDetailData offer = offerService.getOfferDetailData(input.getOfferDetailId());
        if (offer == null) {
            error = "Invalid Offer In The System";
        } else {

            Date businessDate = AppUtils.getBusinessDate();
            if (businessDate.compareTo(offer.getLaunchStartDate()) < 0) {
                error = "Offer can be availed only after "
                        + new SimpleDateFormat("dd MMM yyyy").format(offer.getLaunchStartDate());
            }
            if (businessDate.compareTo(offer.getLaunchEndDate()) > 0) {
                error = "Offer has expired on " + new SimpleDateFormat("dd MMM yyyy").format(offer.getLaunchEndDate());
            }
            if (AppConstants.IN_ACTIVE.equals(offer.getOfferStatus())) {
                error = "Offer is no more active in the system";
            }
        }
        String number = input.getContactNumber() != null && input.getContactNumber().length() >= 10 ? input
                .getContactNumber().substring(input.getContactNumber().length() - 10, input.getContactNumber().length())
                : input.getContactNumber();
        input.setContactNumber(number);
        input.setStartDate(offer.getStartDate());
        if (number == null) {
            error = "Contact Number Cannot be empty";
        } else if (!validNumber(number)) {
            error = "Contact Number " + number + " is invalid!";
        }
        if (checkExistingCustomer) {
            boolean existing = customerService.existsContactNumber(number);
            if (existing) {
                error = "#" + number + " is an existing customer number in our system.";
            }
        }
        boolean existsCustomer = offerService.existCustomerOfferMappingData(input.getOfferDetailId(), number);

        if (existsCustomer) {
            error = "#" + number + " has already availed the offer.";
        }

        if (error != null && error.length() > 0) {
            input.setErrorMessage(error);
            return input;
        } else {
            synchronized ("LAUNCH_OFFER") {
                LaunchOfferData output = offerService.availLaunchOffer(props.getEnvironmentType(), input,
                        addUnitMapping);
                Unit unit = masterDataCache.getUnit(output.getUnitId());
                output.setUnitName(unit.getName());
                output.setUnitLocation(unit.getAddress().getLine1()
                        + (unit.getAddress().getLine2() != null ? unit.getAddress().getLine2() : ""));
                if (!AppUtils.isDev(props.getEnvironmentType())) {
                    try {
                        // Sending SMS to customer
                        String message = String.format(
                                "Awesome! You've won FREE Chaayos Chai for %d Days. Code: %s(Val %s) Make your friends win too - http://cafes.chaayos.com/%s?s=%s",
                                output.getOfferDayCount(), output.getCouponCode(),
                                DATE_FORMATTER.format(output.getEndDate()), output.getUrlEndPoint(),
                                output.getContactNumber());
                        boolean sentMessage = notificationService.sendNotification("OFFER_NOTIFICATION", message,
                                output.getContactNumber(),
                                providerService.getSMSClient(SMSType.TRANSACTIONAL, ApplicationName.KETTLE_SERVICE),
                                true,null);
                        output.setNotified(sentMessage);
                    } catch (Exception e) {
                        LOG.error("Error in sending offer notification to customer", e);
                    }
                }
            }
        }
        return input;
    }

    @RequestMapping(method = RequestMethod.POST, value = "secure-launch-offer", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public LaunchOfferData getLaunchCouponCodeWithToken(@RequestBody LaunchOfferData input)
            throws DataNotFoundException {
        return getFreeItemCouponCode(input, false, false);
    }

    @RequestMapping(method = RequestMethod.POST, value = "secure-free-item-offer", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public LaunchOfferData getFreeItemCouponCodeWithToken(@RequestBody LaunchOfferData input)
            throws DataNotFoundException {
        return getFreeItemCouponCode(input, true, false);
    }

    private boolean validNumber(String number) {
        Matcher m = MOBILE_NUMBER_REGEX_PATTERN.matcher(number);
        return (m.find() && m.group().equals(number));
    }

    @RequestMapping(method = RequestMethod.POST, value = "generate-token", produces = MediaType.TEXT_PLAIN)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public String getCSRFToken(@RequestBody String url) throws DataNotFoundException {
        return tokenService.getToken(url);
    }


    @Scheduled(cron = "0 15 05 * * *", zone = "GMT+05:30")
    public void refreshCache() throws DataNotFoundException, IOException, EmailGenerationException {
        LOG.info("CRON to refresh CSRF Tokens");
        try {
            tokenService.clearAll();
            LOG.info("Refresh CSRF Tokens :: SUCCESSFUL");
        } catch (Exception e) {
            LOG.error("Refresh CSRF Tokens :: FAILED", e);
        }
    }

    public OfferDetail convertOfferDetail(OfferDetailData data) {
        OfferDetail detail = new OfferDetail();
        detail.setId(data.getOfferDetailId());

        return detail;
    }

    @RequestMapping(method = RequestMethod.POST, value = "coupon/signup", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public IdCodeName getSignUpUniqueCoupon(@RequestBody String contact) {
        try {
            SignupOffersCouponDetails couponDetails = offerService.getCouponDetails();
            OfferDetailData offerDetail = offerService.getOfferDetailData(couponDetails.getOfferDetailId());
            List<String> couponCode = offerService.getUniqueCoupons(couponDetails.getCouponPrefix(), couponDetails.getCouponCode(), 1);
            CouponDetail couponDetail = new CouponDetail();
            couponDetail.setCode(couponCode.get(0));
            couponDetail.setOffer(convertOfferDetail(offerDetail));
            couponDetail.setReusable(false);
            couponDetail.setReusableByCustomer(false);
            couponDetail.setStartDate(AppUtils.getCurrentDate());
            couponDetail.setEndDate(AppUtils.getDateAfterDays(AppUtils.getCurrentDate(), Integer.parseInt(couponDetails.getCouponValidity())));
            couponDetail.setStatus(AppConstants.ACTIVE);
            couponDetail.setUsage(0);
            couponDetail.setMaxUsage(1);
            couponDetail.setMaxCustomerUsage(1);
            couponDetail.setManualOverride(false);

            CouponDetail coupon = offerService.addCoupon(couponDetail);
            IdCodeName output = new IdCodeName(contact, coupon.getCode());
            WebOfferCouponRedemptionDetail redemptionDetail = new WebOfferCouponRedemptionDetail(contact, coupon.getCode(), AppUtils.getCurrentTimestamp());

            customerOfferManagementService.addCouponRedemptionDetail(redemptionDetail);
            return output;
        } catch (Exception e) {
            return new IdCodeName("error faced", e.toString());
        }

    }

    @RequestMapping(method = RequestMethod.POST, value = "campaign-journey", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Boolean setCustomerJourneyState(@RequestBody CustomerCampaignJourney journeyDetail){
        LOG.info("Request to add a journey state {} for customerId : {} and contactNumber : {} ",
                journeyDetail.getFinalState(), journeyDetail.getCustomerId(),
                journeyDetail.getContactNumber());
        return customerOfferManagementService.setCustomerJourneyState(journeyDetail);
    }

}
