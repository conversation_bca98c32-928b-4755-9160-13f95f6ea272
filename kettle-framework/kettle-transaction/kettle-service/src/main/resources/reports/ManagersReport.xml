<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<ReportCategories xmlns="http://www.w3schools.com">
    <categories>
        <category name="Processing Time Report" type="Automated"
                  accessCode="Automated">
            <reports>
                <report name="Order Wise Processing Time Report"
                        executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[

SELECT
    BUSINESS_DATE,
    UNIT_ID,
    UNIT_NAME,
    ORDER_STATUS,
    A.ORDER_ID,
    GENERATED_ORDER_ID,
    ORDER_TIME,
    EMP_ID,
    EMP_NAME,
    TAXABLE_AMOUNT,
    ORDER_SOURCE,
    (CASE
        WHEN BRAND_ID = 1 THEN 'Chaayos'
        ELSE 'G&T'
    END) BRAND_NAME,
    (CASE
        WHEN BUSINESS_HOUR BETWEEN 5 AND 11 THEN '1_BREAKFAST'
        WHEN BUSINESS_HOUR BETWEEN 12 AND 14 THEN '2_LUNCH'
        WHEN BUSINESS_HOUR BETWEEN 15 AND 19 THEN '3_EVENING'
        WHEN BUSINESS_HOUR BETWEEN 20 AND 21 THEN '4_DINNER'
        WHEN BUSINESS_HOUR BETWEEN 22 AND 23 THEN '5_POST_DINNER'
        ELSE '6_OVERNIGHT'
    END) AS DAYPART,
    ORDER_PROCESSING_TIME,
    CASE
        WHEN
            ORDER_SOURCE <> 'COD'
                AND ORDER_PROCESSING_TIME > 7
        THEN
            'DELAYED'
        WHEN
            ORDER_SOURCE <> 'COD'
                AND ORDER_PROCESSING_TIME < 7
        THEN
            'ON_TIME'
        WHEN
            ORDER_SOURCE = 'COD'
                AND ORDER_PROCESSING_TIME > 9
        THEN
            'DELAYED'
        WHEN
            ORDER_SOURCE = 'COD'
                AND ORDER_PROCESSING_TIME < 9
        THEN
            'ON_TIME'
    END PROCESSING_STATUS,
	GROUP_CONCAT(CONCAT(oi.PRODUCT_NAME, '-', oi.QUANTITY)
                SEPARATOR '##') PRODUCTS,
    CASE
        WHEN
            A.IS_PRIORTIZED IS NULL
        THEN
           'N'
        ELSE
            A.IS_PRIORTIZED
    END IS_PRIORITIZED
FROM
    (SELECT
        od.BUSINESS_DATE,
            ald.ORDER_ID,
            ud.UNIT_NAME,
            od.GENERATED_ORDER_ID,
            od.BILLING_SERVER_TIME ORDER_TIME,
            ald.UNIT_ID,
            od.BRAND_ID,
            emp.EMP_ID,
            emp.EMP_NAME,
            od.ORDER_SOURCE,
            od.TAXABLE_AMOUNT,
            od.ORDER_STATUS,
            HOUR(od.BILLING_SERVER_TIME) BUSINESS_HOUR,
            TRUNCATE(((MAX((wl.TIME_TO_PROCESS + wl.TIME_TO_START) / 1000)) / 60), 2) ORDER_PROCESSING_TIME,
            pod.IS_PRIORTIZED
    FROM
        KETTLE.WORKSTATION_LOG wl
    INNER JOIN KETTLE.ASSEMBLY_LOG_DATA ald ON ald.ORDER_ID = wl.ORDER_ID
    INNER JOIN KETTLE.ORDER_DETAIL od ON od.ORDER_ID = ald.ORDER_ID
    INNER JOIN KETTLE_MASTER.PRODUCT_DETAIL pd ON pd.PRODUCT_ID = wl.PRODUCT_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    INNER JOIN KETTLE_MASTER.EMPLOYEE_DETAIL emp ON emp.EMP_ID = od.EMP_ID
    INNER JOIN KETTLE.PARTNER_ORDER_DETAIL pod ON pod.KETTLE_ORDER_ID = od.ORDER_ID
    WHERE
        ald.ORDER_ID IS NOT NULL
            AND od.BUSINESS_DATE = :businessDate
           AND pd.PRODUCT_TYPE NOT IN (9 , 10)
          AND od.UNIT_ID = :unitId
    GROUP BY ald.ORDER_ID) A
    INNER JOIN KETTLE.ORDER_ITEM oi ON A.ORDER_ID = oi.ORDER_ID

GROUP BY A.ORDER_ID
ORDER BY A.UNIT_NAME, A.ORDER_ID;

 ]]>
                    </content>
                    <params>
                        <param  multiValued="false" name="businessDate" displayName="Business Date"
                                dataType="STRING" />
                        <param  multiValued="false" name="unitId" displayName="unitId"
                                dataType="INTEGER" />
                    </params>

                </report>
            </reports>
        </category>

        <category name="NPS Report" type="Automated"
                  accessCode="Automated">
            <reports>


                <report name="NPS Rank"
                        executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[

	select BUSINESS_DATE,
	'DAILY' CALCULATION,
    UNIT_ID,
    UNIT_NAME,
    NPS_SCORE,
    COALESCE(CAFE_NPS_SCORE, 0) CAFE_NPS_SCORE,
    COALESCE(COD_NPS_SCORE, 0) COD_NPS_SCORE,
    COALESCE(POSITIVE_PERCENTAGE, 0) POSITIVE_PERCENTAGE,
    COALESCE(NEGATIVE_PERCENTAGE, 0) NEGATIVE_PERCENTAGE,
    COALESCE(TOTAL_POSITIVE, 0) TOTAL_POSITIVE,
    COALESCE(TOTAL_NEGATIVE, 0) TOTAL_NEGATIVE,
    COALESCE(TOTAL_TICKET, 0) TOTAL_TICKET,
    COALESCE(TOTAL_CAFE_POSITIVE, 0) TOTAL_CAFE_POSITIVE,
    COALESCE(TOTAL_CAFE_NEGATIVE, 0) TOTAL_CAFE_NEGATIVE,
    COALESCE(TOTAL_CAFE_NEUTRAL, 0) TOTAL_CAFE_NEUTRAL,
    COALESCE(TOTAL_CAFE_TICKETS, 0) TOTAL_CAFE_TICKETS,
    COALESCE(TOTAL_COD_POSITIVE, 0) TOTAL_COD_POSITIVE,
    COALESCE(TOTAL_COD_NEGATIVE, 0) TOTAL_COD_NEGATIVE,
    COALESCE(TOTAL_COD_NEUTRAL, 0) TOTAL_COD_NEUTRAL,
    COALESCE(TOTAL_COD_TICKETS, 0) TOTAL_COD_TICKETS from (
SELECT
    :businessDate BUSINESS_DATE,
    a.UNIT_ID,
    ud.UNIT_NAME,
    ud.UNIT_CATEGORY,
    SUM(a.TOTAL_TICKET) TOTAL_TICKET,
    SUM(a.TOTAL_NEGATIVE) TOTAL_NEGATIVE,
    SUM(a.TOTAL_NEUTRAL) TOTAL_NEUTRAL,
    SUM(a.TOTAL_POSITIVE) TOTAL_POSITIVE,
    TRUNCATE(SUM(a.TOTAL_POSITIVE) / SUM(a.TOTAL_TICKET) * 100,
        2) POSITIVE_PERCENTAGE,
    TRUNCATE(SUM(a.TOTAL_NEGATIVE) / SUM(a.TOTAL_TICKET) * 100,
        2) NEGATIVE_PERCENTAGE,
    TRUNCATE(SUM(a.TOTAL_NEUTRAL) / SUM(a.TOTAL_TICKET) * 100,
        2) NEUTRAL_PERCENTAGE,
    SUM(CASE
        WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_TICKET
    END) AS TOTAL_CAFE_TICKETS,
    SUM(CASE
        WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_NEGATIVE
    END) AS TOTAL_CAFE_NEGATIVE,
    SUM(CASE
        WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_NEUTRAL
    END) AS TOTAL_CAFE_NEUTRAL,
    SUM(CASE
        WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_POSITIVE
    END) AS TOTAL_CAFE_POSITIVE,
    TRUNCATE(SUM(CASE
            WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_POSITIVE
        END) / SUM(CASE
            WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_TICKET
        END) * 100,
        2) CAFE_POSITIVE_PERCENTAGE,
    TRUNCATE(SUM(CASE
            WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_NEGATIVE
        END) / SUM(CASE
            WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_TICKET
        END) * 100,
        2) CAFE_NEGATIVE_PERCENTAGE,
    TRUNCATE(SUM(CASE
            WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_NEUTRAL
        END) / SUM(CASE
            WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_TICKET
        END) * 100,
        2) CAFE_NEUTRAL_PERCENTAGE,
    SUM(CASE
        WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_TICKET
    END) AS TOTAL_COD_TICKETS,
    SUM(CASE
        WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_NEGATIVE
    END) AS TOTAL_COD_NEGATIVE,
    SUM(CASE
        WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_NEUTRAL
    END) AS TOTAL_COD_NEUTRAL,
    SUM(CASE
        WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_POSITIVE
    END) AS TOTAL_COD_POSITIVE,
    TRUNCATE(SUM(CASE
            WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_POSITIVE
        END) / SUM(CASE
            WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_TICKET
        END) * 100,
        2) COD_POSITIVE_PERCENTAGE,
    TRUNCATE(SUM(CASE
            WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_NEGATIVE
        END) / SUM(CASE
            WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_TICKET
        END) * 100,
        2) COD_NEGATIVE_PERCENTAGE,
    TRUNCATE(SUM(CASE
            WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_NEUTRAL
        END) / SUM(CASE
            WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_TICKET
        END) * 100,
        2) COD_NEUTRAL_PERCENTAGE,
    ROUND(TRUNCATE(SUM(a.TOTAL_POSITIVE) / SUM(a.TOTAL_TICKET) * 100,
                2)) - ROUND(TRUNCATE(SUM(a.TOTAL_NEGATIVE) / SUM(a.TOTAL_TICKET) * 100,
                2)) NPS_SCORE,
    ROUND(TRUNCATE(SUM(CASE
                    WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_POSITIVE
                END) / SUM(CASE
                    WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_TICKET
                END) * 100,
                2)) - ROUND(TRUNCATE(SUM(CASE
                    WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_NEGATIVE
                END) / SUM(CASE
                    WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_TICKET
                END) * 100,
                2)) CAFE_NPS_SCORE,
    ROUND(TRUNCATE(SUM(CASE
                    WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_POSITIVE
                END) / SUM(CASE
                    WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_TICKET
                END) * 100,
                2)) - ROUND(TRUNCATE(SUM(CASE
                    WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_NEGATIVE
                END) / SUM(CASE
                    WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_TICKET
                END) * 100,
                2)) COD_NPS_SCORE
FROM
    (SELECT
        nd.UNIT_ID,
            ORDER_SOURCE,
            COUNT(*) TOTAL_TICKET,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 0 AND nd.NPS_SCORE <= 6 THEN 1
                ELSE 0
            END) TOTAL_NEGATIVE,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 7 AND nd.NPS_SCORE <= 8 THEN 1
                ELSE 0
            END) TOTAL_NEUTRAL,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 9 AND nd.NPS_SCORE <= 10 THEN 1
                ELSE 0
            END) TOTAL_POSITIVE
    FROM
        KETTLE.ORDER_NPS_DETAIL nd
    INNER JOIN KETTLE.ORDER_DETAIL ud ON ud.ORDER_ID = nd.ORDER_ID
    WHERE
        DATE(nd.SURVEY_CREATION_TIME) >= :businessDate
            AND DATE(nd.SURVEY_CREATION_TIME) <= :businessDate
		AND ud.UNIT_ID = :unitId
    GROUP BY ud.ORDER_SOURCE) a,
    KETTLE_MASTER.UNIT_DETAIL ud
WHERE
    a.UNIT_ID = ud.UNIT_ID
GROUP BY 1 , 2 , 3 , 4
) a
UNION ALL
select BUSINESS_DATE,
	'MTD' CALCULATION,
    UNIT_ID,
    UNIT_NAME,
    NPS_SCORE,
    COALESCE(CAFE_NPS_SCORE, 0) CAFE_NPS_SCORE,
    COALESCE(COD_NPS_SCORE, 0) COD_NPS_SCORE,
    COALESCE(POSITIVE_PERCENTAGE, 0) POSITIVE_PERCENTAGE,
    COALESCE(NEGATIVE_PERCENTAGE, 0) NEGATIVE_PERCENTAGE,
    COALESCE(TOTAL_POSITIVE, 0) TOTAL_POSITIVE,
    COALESCE(TOTAL_NEGATIVE, 0) TOTAL_NEGATIVE,
    COALESCE(TOTAL_TICKET, 0) TOTAL_TICKET,
    COALESCE(TOTAL_CAFE_POSITIVE, 0) TOTAL_CAFE_POSITIVE,
    COALESCE(TOTAL_CAFE_NEGATIVE, 0) TOTAL_CAFE_NEGATIVE,
    COALESCE(TOTAL_CAFE_NEUTRAL, 0) TOTAL_CAFE_NEUTRAL,
    COALESCE(TOTAL_CAFE_TICKETS, 0) TOTAL_CAFE_TICKETS,
    COALESCE(TOTAL_COD_POSITIVE, 0) TOTAL_COD_POSITIVE,
    COALESCE(TOTAL_COD_NEGATIVE, 0) TOTAL_COD_NEGATIVE,
    COALESCE(TOTAL_COD_NEUTRAL, 0) TOTAL_COD_NEUTRAL,
    COALESCE(TOTAL_COD_TICKETS, 0) TOTAL_COD_TICKETS from (
SELECT
    :businessDate BUSINESS_DATE,
    a.UNIT_ID,
    ud.UNIT_NAME,
    ud.UNIT_CATEGORY,
    SUM(a.TOTAL_TICKET) TOTAL_TICKET,
    SUM(a.TOTAL_NEGATIVE) TOTAL_NEGATIVE,
    SUM(a.TOTAL_NEUTRAL) TOTAL_NEUTRAL,
    SUM(a.TOTAL_POSITIVE) TOTAL_POSITIVE,
    TRUNCATE(SUM(a.TOTAL_POSITIVE) / SUM(a.TOTAL_TICKET) * 100,
        2) POSITIVE_PERCENTAGE,
    TRUNCATE(SUM(a.TOTAL_NEGATIVE) / SUM(a.TOTAL_TICKET) * 100,
        2) NEGATIVE_PERCENTAGE,
    TRUNCATE(SUM(a.TOTAL_NEUTRAL) / SUM(a.TOTAL_TICKET) * 100,
        2) NEUTRAL_PERCENTAGE,
    SUM(CASE
        WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_TICKET
    END) AS TOTAL_CAFE_TICKETS,
    SUM(CASE
        WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_NEGATIVE
    END) AS TOTAL_CAFE_NEGATIVE,
    SUM(CASE
        WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_NEUTRAL
    END) AS TOTAL_CAFE_NEUTRAL,
    SUM(CASE
        WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_POSITIVE
    END) AS TOTAL_CAFE_POSITIVE,
    TRUNCATE(SUM(CASE
            WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_POSITIVE
        END) / SUM(CASE
            WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_TICKET
        END) * 100,
        2) CAFE_POSITIVE_PERCENTAGE,
    TRUNCATE(SUM(CASE
            WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_NEGATIVE
        END) / SUM(CASE
            WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_TICKET
        END) * 100,
        2) CAFE_NEGATIVE_PERCENTAGE,
    TRUNCATE(SUM(CASE
            WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_NEUTRAL
        END) / SUM(CASE
            WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_TICKET
        END) * 100,
        2) CAFE_NEUTRAL_PERCENTAGE,
    SUM(CASE
        WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_TICKET
    END) AS TOTAL_COD_TICKETS,
    SUM(CASE
        WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_NEGATIVE
    END) AS TOTAL_COD_NEGATIVE,
    SUM(CASE
        WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_NEUTRAL
    END) AS TOTAL_COD_NEUTRAL,
    SUM(CASE
        WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_POSITIVE
    END) AS TOTAL_COD_POSITIVE,
    TRUNCATE(SUM(CASE
            WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_POSITIVE
        END) / SUM(CASE
            WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_TICKET
        END) * 100,
        2) COD_POSITIVE_PERCENTAGE,
    TRUNCATE(SUM(CASE
            WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_NEGATIVE
        END) / SUM(CASE
            WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_TICKET
        END) * 100,
        2) COD_NEGATIVE_PERCENTAGE,
    TRUNCATE(SUM(CASE
            WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_NEUTRAL
        END) / SUM(CASE
            WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_TICKET
        END) * 100,
        2) COD_NEUTRAL_PERCENTAGE,
    ROUND(TRUNCATE(SUM(a.TOTAL_POSITIVE) / SUM(a.TOTAL_TICKET) * 100,
                2)) - ROUND(TRUNCATE(SUM(a.TOTAL_NEGATIVE) / SUM(a.TOTAL_TICKET) * 100,
                2)) NPS_SCORE,
    ROUND(TRUNCATE(SUM(CASE
                    WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_POSITIVE
                END) / SUM(CASE
                    WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_TICKET
                END) * 100,
                2)) - ROUND(TRUNCATE(SUM(CASE
                    WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_NEGATIVE
                END) / SUM(CASE
                    WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_TICKET
                END) * 100,
                2)) CAFE_NPS_SCORE,
    ROUND(TRUNCATE(SUM(CASE
                    WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_POSITIVE
                END) / SUM(CASE
                    WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_TICKET
                END) * 100,
                2)) - ROUND(TRUNCATE(SUM(CASE
                    WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_NEGATIVE
                END) / SUM(CASE
                    WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_TICKET
                END) * 100,
                2)) COD_NPS_SCORE
FROM
    (SELECT
        nd.UNIT_ID,
            ORDER_SOURCE,
            COUNT(*) TOTAL_TICKET,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 0 AND nd.NPS_SCORE <= 6 THEN 1
                ELSE 0
            END) TOTAL_NEGATIVE,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 7 AND nd.NPS_SCORE <= 8 THEN 1
                ELSE 0
            END) TOTAL_NEUTRAL,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 9 AND nd.NPS_SCORE <= 10 THEN 1
                ELSE 0
            END) TOTAL_POSITIVE
    FROM
        KETTLE.ORDER_NPS_DETAIL nd
    INNER JOIN KETTLE.ORDER_DETAIL ud ON ud.ORDER_ID = nd.ORDER_ID
    WHERE
        DATE(nd.SURVEY_CREATION_TIME) >= :monthStartDate
            AND DATE(nd.SURVEY_CREATION_TIME) <= :businessDate
		AND ud.UNIT_ID = :unitId
    GROUP BY ud.ORDER_SOURCE) a,
    KETTLE_MASTER.UNIT_DETAIL ud
WHERE
    a.UNIT_ID = ud.UNIT_ID
GROUP BY 1 , 2 , 3 , 4
) a
UNION ALL
select BUSINESS_DATE,
	'QTD' CALCULATION,
    UNIT_ID,
    UNIT_NAME,
    NPS_SCORE,
    COALESCE(CAFE_NPS_SCORE, 0) CAFE_NPS_SCORE,
    COALESCE(COD_NPS_SCORE, 0) COD_NPS_SCORE,
    COALESCE(POSITIVE_PERCENTAGE, 0) POSITIVE_PERCENTAGE,
    COALESCE(NEGATIVE_PERCENTAGE, 0) NEGATIVE_PERCENTAGE,
    COALESCE(TOTAL_POSITIVE, 0) TOTAL_POSITIVE,
    COALESCE(TOTAL_NEGATIVE, 0) TOTAL_NEGATIVE,
    COALESCE(TOTAL_TICKET, 0) TOTAL_TICKET,
    COALESCE(TOTAL_CAFE_POSITIVE, 0) TOTAL_CAFE_POSITIVE,
    COALESCE(TOTAL_CAFE_NEGATIVE, 0) TOTAL_CAFE_NEGATIVE,
    COALESCE(TOTAL_CAFE_NEUTRAL, 0) TOTAL_CAFE_NEUTRAL,
    COALESCE(TOTAL_CAFE_TICKETS, 0) TOTAL_CAFE_TICKETS,
    COALESCE(TOTAL_COD_POSITIVE, 0) TOTAL_COD_POSITIVE,
    COALESCE(TOTAL_COD_NEGATIVE, 0) TOTAL_COD_NEGATIVE,
    COALESCE(TOTAL_COD_NEUTRAL, 0) TOTAL_COD_NEUTRAL,
    COALESCE(TOTAL_COD_TICKETS, 0) TOTAL_COD_TICKETS from (
SELECT
    :businessDate BUSINESS_DATE,
    a.UNIT_ID,
    ud.UNIT_NAME,
    ud.UNIT_CATEGORY,
    SUM(a.TOTAL_TICKET) TOTAL_TICKET,
    SUM(a.TOTAL_NEGATIVE) TOTAL_NEGATIVE,
    SUM(a.TOTAL_NEUTRAL) TOTAL_NEUTRAL,
    SUM(a.TOTAL_POSITIVE) TOTAL_POSITIVE,
    TRUNCATE(SUM(a.TOTAL_POSITIVE) / SUM(a.TOTAL_TICKET) * 100,
        2) POSITIVE_PERCENTAGE,
    TRUNCATE(SUM(a.TOTAL_NEGATIVE) / SUM(a.TOTAL_TICKET) * 100,
        2) NEGATIVE_PERCENTAGE,
    TRUNCATE(SUM(a.TOTAL_NEUTRAL) / SUM(a.TOTAL_TICKET) * 100,
        2) NEUTRAL_PERCENTAGE,
    SUM(CASE
        WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_TICKET
    END) AS TOTAL_CAFE_TICKETS,
    SUM(CASE
        WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_NEGATIVE
    END) AS TOTAL_CAFE_NEGATIVE,
    SUM(CASE
        WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_NEUTRAL
    END) AS TOTAL_CAFE_NEUTRAL,
    SUM(CASE
        WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_POSITIVE
    END) AS TOTAL_CAFE_POSITIVE,
    TRUNCATE(SUM(CASE
            WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_POSITIVE
        END) / SUM(CASE
            WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_TICKET
        END) * 100,
        2) CAFE_POSITIVE_PERCENTAGE,
    TRUNCATE(SUM(CASE
            WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_NEGATIVE
        END) / SUM(CASE
            WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_TICKET
        END) * 100,
        2) CAFE_NEGATIVE_PERCENTAGE,
    TRUNCATE(SUM(CASE
            WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_NEUTRAL
        END) / SUM(CASE
            WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_TICKET
        END) * 100,
        2) CAFE_NEUTRAL_PERCENTAGE,
    SUM(CASE
        WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_TICKET
    END) AS TOTAL_COD_TICKETS,
    SUM(CASE
        WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_NEGATIVE
    END) AS TOTAL_COD_NEGATIVE,
    SUM(CASE
        WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_NEUTRAL
    END) AS TOTAL_COD_NEUTRAL,
    SUM(CASE
        WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_POSITIVE
    END) AS TOTAL_COD_POSITIVE,
    TRUNCATE(SUM(CASE
            WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_POSITIVE
        END) / SUM(CASE
            WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_TICKET
        END) * 100,
        2) COD_POSITIVE_PERCENTAGE,
    TRUNCATE(SUM(CASE
            WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_NEGATIVE
        END) / SUM(CASE
            WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_TICKET
        END) * 100,
        2) COD_NEGATIVE_PERCENTAGE,
    TRUNCATE(SUM(CASE
            WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_NEUTRAL
        END) / SUM(CASE
            WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_TICKET
        END) * 100,
        2) COD_NEUTRAL_PERCENTAGE,
    ROUND(TRUNCATE(SUM(a.TOTAL_POSITIVE) / SUM(a.TOTAL_TICKET) * 100,
                2)) - ROUND(TRUNCATE(SUM(a.TOTAL_NEGATIVE) / SUM(a.TOTAL_TICKET) * 100,
                2)) NPS_SCORE,
    ROUND(TRUNCATE(SUM(CASE
                    WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_POSITIVE
                END) / SUM(CASE
                    WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_TICKET
                END) * 100,
                2)) - ROUND(TRUNCATE(SUM(CASE
                    WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_NEGATIVE
                END) / SUM(CASE
                    WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_TICKET
                END) * 100,
                2)) CAFE_NPS_SCORE,
    ROUND(TRUNCATE(SUM(CASE
                    WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_POSITIVE
                END) / SUM(CASE
                    WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_TICKET
                END) * 100,
                2)) - ROUND(TRUNCATE(SUM(CASE
                    WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_NEGATIVE
                END) / SUM(CASE
                    WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_TICKET
                END) * 100,
                2)) COD_NPS_SCORE
FROM
    (SELECT
        nd.UNIT_ID,
            ORDER_SOURCE,
            COUNT(*) TOTAL_TICKET,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 0 AND nd.NPS_SCORE <= 6 THEN 1
                ELSE 0
            END) TOTAL_NEGATIVE,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 7 AND nd.NPS_SCORE <= 8 THEN 1
                ELSE 0
            END) TOTAL_NEUTRAL,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 9 AND nd.NPS_SCORE <= 10 THEN 1
                ELSE 0
            END) TOTAL_POSITIVE
    FROM
        KETTLE.ORDER_NPS_DETAIL nd
    INNER JOIN KETTLE.ORDER_DETAIL ud ON ud.ORDER_ID = nd.ORDER_ID
    WHERE
        DATE(nd.SURVEY_CREATION_TIME) >= :quarterStartDate
            AND DATE(nd.SURVEY_CREATION_TIME) <= :businessDate
		AND ud.UNIT_ID = :unitId
    GROUP BY ud.ORDER_SOURCE) a,
    KETTLE_MASTER.UNIT_DETAIL ud
WHERE
    a.UNIT_ID = ud.UNIT_ID
GROUP BY 1 , 2 , 3 , 4
) a

				]]>
                    </content>
                    <params>
                        <param  multiValued="false" name="businessDate" displayName="Business Date"
                                dataType="STRING" />
                        <param  multiValued="false" name="monthStartDate" displayName="Business Date"
                                dataType="STRING" />
                        <param  multiValued="false" name="quarterStartDate" displayName="Business Date"
                                dataType="STRING" />
                        <param  multiValued="false" name="unitId" displayName="unitId"
                                dataType="INTEGER" />
                    </params>

                </report>
                <report name="New Customers NPS Score"
                        executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[

	select BUSINESS_DATE,
	'DAILY' CALCULATION,
    UNIT_ID,
    UNIT_NAME,
    NPS_SCORE,
    COALESCE(CAFE_NPS_SCORE, 0) CAFE_NPS_SCORE,
    COALESCE(COD_NPS_SCORE, 0) COD_NPS_SCORE,
    COALESCE(POSITIVE_PERCENTAGE, 0) POSITIVE_PERCENTAGE,
    COALESCE(NEGATIVE_PERCENTAGE, 0) NEGATIVE_PERCENTAGE,
    COALESCE(TOTAL_POSITIVE, 0) TOTAL_POSITIVE,
    COALESCE(TOTAL_NEGATIVE, 0) TOTAL_NEGATIVE,
    COALESCE(TOTAL_TICKET, 0) TOTAL_TICKET,
    COALESCE(TOTAL_CAFE_POSITIVE, 0) TOTAL_CAFE_POSITIVE,
    COALESCE(TOTAL_CAFE_NEGATIVE, 0) TOTAL_CAFE_NEGATIVE,
    COALESCE(TOTAL_CAFE_NEUTRAL, 0) TOTAL_CAFE_NEUTRAL,
    COALESCE(TOTAL_CAFE_TICKETS, 0) TOTAL_CAFE_TICKETS,
    COALESCE(TOTAL_COD_POSITIVE, 0) TOTAL_COD_POSITIVE,
    COALESCE(TOTAL_COD_NEGATIVE, 0) TOTAL_COD_NEGATIVE,
    COALESCE(TOTAL_COD_NEUTRAL, 0) TOTAL_COD_NEUTRAL,
    COALESCE(TOTAL_COD_TICKETS, 0) TOTAL_COD_TICKETS from (
SELECT
    :businessDate BUSINESS_DATE,
    a.UNIT_ID,
    ud.UNIT_NAME,
    ud.UNIT_CATEGORY,
    SUM(a.TOTAL_TICKET) TOTAL_TICKET,
    SUM(a.TOTAL_NEGATIVE) TOTAL_NEGATIVE,
    SUM(a.TOTAL_NEUTRAL) TOTAL_NEUTRAL,
    SUM(a.TOTAL_POSITIVE) TOTAL_POSITIVE,
    TRUNCATE(SUM(a.TOTAL_POSITIVE) / SUM(a.TOTAL_TICKET) * 100,
        2) POSITIVE_PERCENTAGE,
    TRUNCATE(SUM(a.TOTAL_NEGATIVE) / SUM(a.TOTAL_TICKET) * 100,
        2) NEGATIVE_PERCENTAGE,
    TRUNCATE(SUM(a.TOTAL_NEUTRAL) / SUM(a.TOTAL_TICKET) * 100,
        2) NEUTRAL_PERCENTAGE,
    SUM(CASE
        WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_TICKET
    END) AS TOTAL_CAFE_TICKETS,
    SUM(CASE
        WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_NEGATIVE
    END) AS TOTAL_CAFE_NEGATIVE,
    SUM(CASE
        WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_NEUTRAL
    END) AS TOTAL_CAFE_NEUTRAL,
    SUM(CASE
        WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_POSITIVE
    END) AS TOTAL_CAFE_POSITIVE,
    TRUNCATE(SUM(CASE
            WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_POSITIVE
        END) / SUM(CASE
            WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_TICKET
        END) * 100,
        2) CAFE_POSITIVE_PERCENTAGE,
    TRUNCATE(SUM(CASE
            WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_NEGATIVE
        END) / SUM(CASE
            WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_TICKET
        END) * 100,
        2) CAFE_NEGATIVE_PERCENTAGE,
    TRUNCATE(SUM(CASE
            WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_NEUTRAL
        END) / SUM(CASE
            WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_TICKET
        END) * 100,
        2) CAFE_NEUTRAL_PERCENTAGE,
    SUM(CASE
        WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_TICKET
    END) AS TOTAL_COD_TICKETS,
    SUM(CASE
        WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_NEGATIVE
    END) AS TOTAL_COD_NEGATIVE,
    SUM(CASE
        WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_NEUTRAL
    END) AS TOTAL_COD_NEUTRAL,
    SUM(CASE
        WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_POSITIVE
    END) AS TOTAL_COD_POSITIVE,
    TRUNCATE(SUM(CASE
            WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_POSITIVE
        END) / SUM(CASE
            WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_TICKET
        END) * 100,
        2) COD_POSITIVE_PERCENTAGE,
    TRUNCATE(SUM(CASE
            WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_NEGATIVE
        END) / SUM(CASE
            WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_TICKET
        END) * 100,
        2) COD_NEGATIVE_PERCENTAGE,
    TRUNCATE(SUM(CASE
            WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_NEUTRAL
        END) / SUM(CASE
            WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_TICKET
        END) * 100,
        2) COD_NEUTRAL_PERCENTAGE,
    ROUND(TRUNCATE(SUM(a.TOTAL_POSITIVE) / SUM(a.TOTAL_TICKET) * 100,
                2)) - ROUND(TRUNCATE(SUM(a.TOTAL_NEGATIVE) / SUM(a.TOTAL_TICKET) * 100,
                2)) NPS_SCORE,
    ROUND(TRUNCATE(SUM(CASE
                    WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_POSITIVE
                END) / SUM(CASE
                    WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_TICKET
                END) * 100,
                2)) - ROUND(TRUNCATE(SUM(CASE
                    WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_NEGATIVE
                END) / SUM(CASE
                    WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_TICKET
                END) * 100,
                2)) CAFE_NPS_SCORE,
    ROUND(TRUNCATE(SUM(CASE
                    WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_POSITIVE
                END) / SUM(CASE
                    WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_TICKET
                END) * 100,
                2)) - ROUND(TRUNCATE(SUM(CASE
                    WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_NEGATIVE
                END) / SUM(CASE
                    WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_TICKET
                END) * 100,
                2)) COD_NPS_SCORE
FROM
    (SELECT
        nd.UNIT_ID,
            ORDER_SOURCE,
            COUNT(*) TOTAL_TICKET,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 0 AND nd.NPS_SCORE <= 6 THEN 1
                ELSE 0
            END) TOTAL_NEGATIVE,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 7 AND nd.NPS_SCORE <= 8 THEN 1
                ELSE 0
            END) TOTAL_NEUTRAL,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 9 AND nd.NPS_SCORE <= 10 THEN 1
                ELSE 0
            END) TOTAL_POSITIVE
    FROM
        KETTLE.ORDER_NPS_DETAIL nd
    INNER JOIN KETTLE.ORDER_DETAIL ud ON ud.ORDER_ID = nd.ORDER_ID
    WHERE
		ud.IS_NEW_CUSTOMER = 'Y' AND
        DATE(nd.SURVEY_CREATION_TIME) >= :businessDate
            AND DATE(nd.SURVEY_CREATION_TIME) <= :businessDate
		AND ud.UNIT_ID = :unitId
    GROUP BY ud.ORDER_SOURCE) a,
    KETTLE_MASTER.UNIT_DETAIL ud
WHERE
    a.UNIT_ID = ud.UNIT_ID
GROUP BY 1 , 2 , 3 , 4
) a
UNION ALL
select BUSINESS_DATE,
	'MTD' CALCULATION,
    UNIT_ID,
    UNIT_NAME,
    NPS_SCORE,
    COALESCE(CAFE_NPS_SCORE, 0) CAFE_NPS_SCORE,
    COALESCE(COD_NPS_SCORE, 0) COD_NPS_SCORE,
    COALESCE(POSITIVE_PERCENTAGE, 0) POSITIVE_PERCENTAGE,
    COALESCE(NEGATIVE_PERCENTAGE, 0) NEGATIVE_PERCENTAGE,
    COALESCE(TOTAL_POSITIVE, 0) TOTAL_POSITIVE,
    COALESCE(TOTAL_NEGATIVE, 0) TOTAL_NEGATIVE,
    COALESCE(TOTAL_TICKET, 0) TOTAL_TICKET,
    COALESCE(TOTAL_CAFE_POSITIVE, 0) TOTAL_CAFE_POSITIVE,
    COALESCE(TOTAL_CAFE_NEGATIVE, 0) TOTAL_CAFE_NEGATIVE,
    COALESCE(TOTAL_CAFE_NEUTRAL, 0) TOTAL_CAFE_NEUTRAL,
    COALESCE(TOTAL_CAFE_TICKETS, 0) TOTAL_CAFE_TICKETS,
    COALESCE(TOTAL_COD_POSITIVE, 0) TOTAL_COD_POSITIVE,
    COALESCE(TOTAL_COD_NEGATIVE, 0) TOTAL_COD_NEGATIVE,
    COALESCE(TOTAL_COD_NEUTRAL, 0) TOTAL_COD_NEUTRAL,
    COALESCE(TOTAL_COD_TICKETS, 0) TOTAL_COD_TICKETS from (
SELECT
    :businessDate BUSINESS_DATE,
    a.UNIT_ID,
    ud.UNIT_NAME,
    ud.UNIT_CATEGORY,
    SUM(a.TOTAL_TICKET) TOTAL_TICKET,
    SUM(a.TOTAL_NEGATIVE) TOTAL_NEGATIVE,
    SUM(a.TOTAL_NEUTRAL) TOTAL_NEUTRAL,
    SUM(a.TOTAL_POSITIVE) TOTAL_POSITIVE,
    TRUNCATE(SUM(a.TOTAL_POSITIVE) / SUM(a.TOTAL_TICKET) * 100,
        2) POSITIVE_PERCENTAGE,
    TRUNCATE(SUM(a.TOTAL_NEGATIVE) / SUM(a.TOTAL_TICKET) * 100,
        2) NEGATIVE_PERCENTAGE,
    TRUNCATE(SUM(a.TOTAL_NEUTRAL) / SUM(a.TOTAL_TICKET) * 100,
        2) NEUTRAL_PERCENTAGE,
    SUM(CASE
        WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_TICKET
    END) AS TOTAL_CAFE_TICKETS,
    SUM(CASE
        WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_NEGATIVE
    END) AS TOTAL_CAFE_NEGATIVE,
    SUM(CASE
        WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_NEUTRAL
    END) AS TOTAL_CAFE_NEUTRAL,
    SUM(CASE
        WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_POSITIVE
    END) AS TOTAL_CAFE_POSITIVE,
    TRUNCATE(SUM(CASE
            WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_POSITIVE
        END) / SUM(CASE
            WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_TICKET
        END) * 100,
        2) CAFE_POSITIVE_PERCENTAGE,
    TRUNCATE(SUM(CASE
            WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_NEGATIVE
        END) / SUM(CASE
            WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_TICKET
        END) * 100,
        2) CAFE_NEGATIVE_PERCENTAGE,
    TRUNCATE(SUM(CASE
            WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_NEUTRAL
        END) / SUM(CASE
            WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_TICKET
        END) * 100,
        2) CAFE_NEUTRAL_PERCENTAGE,
    SUM(CASE
        WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_TICKET
    END) AS TOTAL_COD_TICKETS,
    SUM(CASE
        WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_NEGATIVE
    END) AS TOTAL_COD_NEGATIVE,
    SUM(CASE
        WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_NEUTRAL
    END) AS TOTAL_COD_NEUTRAL,
    SUM(CASE
        WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_POSITIVE
    END) AS TOTAL_COD_POSITIVE,
    TRUNCATE(SUM(CASE
            WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_POSITIVE
        END) / SUM(CASE
            WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_TICKET
        END) * 100,
        2) COD_POSITIVE_PERCENTAGE,
    TRUNCATE(SUM(CASE
            WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_NEGATIVE
        END) / SUM(CASE
            WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_TICKET
        END) * 100,
        2) COD_NEGATIVE_PERCENTAGE,
    TRUNCATE(SUM(CASE
            WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_NEUTRAL
        END) / SUM(CASE
            WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_TICKET
        END) * 100,
        2) COD_NEUTRAL_PERCENTAGE,
    ROUND(TRUNCATE(SUM(a.TOTAL_POSITIVE) / SUM(a.TOTAL_TICKET) * 100,
                2)) - ROUND(TRUNCATE(SUM(a.TOTAL_NEGATIVE) / SUM(a.TOTAL_TICKET) * 100,
                2)) NPS_SCORE,
    ROUND(TRUNCATE(SUM(CASE
                    WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_POSITIVE
                END) / SUM(CASE
                    WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_TICKET
                END) * 100,
                2)) - ROUND(TRUNCATE(SUM(CASE
                    WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_NEGATIVE
                END) / SUM(CASE
                    WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_TICKET
                END) * 100,
                2)) CAFE_NPS_SCORE,
    ROUND(TRUNCATE(SUM(CASE
                    WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_POSITIVE
                END) / SUM(CASE
                    WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_TICKET
                END) * 100,
                2)) - ROUND(TRUNCATE(SUM(CASE
                    WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_NEGATIVE
                END) / SUM(CASE
                    WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_TICKET
                END) * 100,
                2)) COD_NPS_SCORE
FROM
    (SELECT
        nd.UNIT_ID,
            ORDER_SOURCE,
            COUNT(*) TOTAL_TICKET,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 0 AND nd.NPS_SCORE <= 6 THEN 1
                ELSE 0
            END) TOTAL_NEGATIVE,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 7 AND nd.NPS_SCORE <= 8 THEN 1
                ELSE 0
            END) TOTAL_NEUTRAL,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 9 AND nd.NPS_SCORE <= 10 THEN 1
                ELSE 0
            END) TOTAL_POSITIVE
    FROM
        KETTLE.ORDER_NPS_DETAIL nd
    INNER JOIN KETTLE.ORDER_DETAIL ud ON ud.ORDER_ID = nd.ORDER_ID
    WHERE
		ud.IS_NEW_CUSTOMER = 'Y' AND
        DATE(nd.SURVEY_CREATION_TIME) >= :monthStartDate
            AND DATE(nd.SURVEY_CREATION_TIME) <= :businessDate
		AND ud.UNIT_ID = :unitId
    GROUP BY ud.ORDER_SOURCE) a,
    KETTLE_MASTER.UNIT_DETAIL ud
WHERE
    a.UNIT_ID = ud.UNIT_ID
GROUP BY 1 , 2 , 3 , 4
) a
UNION ALL
select BUSINESS_DATE,
	'QTD' CALCULATION,
    UNIT_ID,
    UNIT_NAME,
    NPS_SCORE,
    COALESCE(CAFE_NPS_SCORE, 0) CAFE_NPS_SCORE,
    COALESCE(COD_NPS_SCORE, 0) COD_NPS_SCORE,
    COALESCE(POSITIVE_PERCENTAGE, 0) POSITIVE_PERCENTAGE,
    COALESCE(NEGATIVE_PERCENTAGE, 0) NEGATIVE_PERCENTAGE,
    COALESCE(TOTAL_POSITIVE, 0) TOTAL_POSITIVE,
    COALESCE(TOTAL_NEGATIVE, 0) TOTAL_NEGATIVE,
    COALESCE(TOTAL_TICKET, 0) TOTAL_TICKET,
    COALESCE(TOTAL_CAFE_POSITIVE, 0) TOTAL_CAFE_POSITIVE,
    COALESCE(TOTAL_CAFE_NEGATIVE, 0) TOTAL_CAFE_NEGATIVE,
    COALESCE(TOTAL_CAFE_NEUTRAL, 0) TOTAL_CAFE_NEUTRAL,
    COALESCE(TOTAL_CAFE_TICKETS, 0) TOTAL_CAFE_TICKETS,
    COALESCE(TOTAL_COD_POSITIVE, 0) TOTAL_COD_POSITIVE,
    COALESCE(TOTAL_COD_NEGATIVE, 0) TOTAL_COD_NEGATIVE,
    COALESCE(TOTAL_COD_NEUTRAL, 0) TOTAL_COD_NEUTRAL,
    COALESCE(TOTAL_COD_TICKETS, 0) TOTAL_COD_TICKETS from (
SELECT
    :businessDate BUSINESS_DATE,
    a.UNIT_ID,
    ud.UNIT_NAME,
    ud.UNIT_CATEGORY,
    SUM(a.TOTAL_TICKET) TOTAL_TICKET,
    SUM(a.TOTAL_NEGATIVE) TOTAL_NEGATIVE,
    SUM(a.TOTAL_NEUTRAL) TOTAL_NEUTRAL,
    SUM(a.TOTAL_POSITIVE) TOTAL_POSITIVE,
    TRUNCATE(SUM(a.TOTAL_POSITIVE) / SUM(a.TOTAL_TICKET) * 100,
        2) POSITIVE_PERCENTAGE,
    TRUNCATE(SUM(a.TOTAL_NEGATIVE) / SUM(a.TOTAL_TICKET) * 100,
        2) NEGATIVE_PERCENTAGE,
    TRUNCATE(SUM(a.TOTAL_NEUTRAL) / SUM(a.TOTAL_TICKET) * 100,
        2) NEUTRAL_PERCENTAGE,
    SUM(CASE
        WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_TICKET
    END) AS TOTAL_CAFE_TICKETS,
    SUM(CASE
        WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_NEGATIVE
    END) AS TOTAL_CAFE_NEGATIVE,
    SUM(CASE
        WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_NEUTRAL
    END) AS TOTAL_CAFE_NEUTRAL,
    SUM(CASE
        WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_POSITIVE
    END) AS TOTAL_CAFE_POSITIVE,
    TRUNCATE(SUM(CASE
            WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_POSITIVE
        END) / SUM(CASE
            WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_TICKET
        END) * 100,
        2) CAFE_POSITIVE_PERCENTAGE,
    TRUNCATE(SUM(CASE
            WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_NEGATIVE
        END) / SUM(CASE
            WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_TICKET
        END) * 100,
        2) CAFE_NEGATIVE_PERCENTAGE,
    TRUNCATE(SUM(CASE
            WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_NEUTRAL
        END) / SUM(CASE
            WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_TICKET
        END) * 100,
        2) CAFE_NEUTRAL_PERCENTAGE,
    SUM(CASE
        WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_TICKET
    END) AS TOTAL_COD_TICKETS,
    SUM(CASE
        WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_NEGATIVE
    END) AS TOTAL_COD_NEGATIVE,
    SUM(CASE
        WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_NEUTRAL
    END) AS TOTAL_COD_NEUTRAL,
    SUM(CASE
        WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_POSITIVE
    END) AS TOTAL_COD_POSITIVE,
    TRUNCATE(SUM(CASE
            WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_POSITIVE
        END) / SUM(CASE
            WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_TICKET
        END) * 100,
        2) COD_POSITIVE_PERCENTAGE,
    TRUNCATE(SUM(CASE
            WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_NEGATIVE
        END) / SUM(CASE
            WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_TICKET
        END) * 100,
        2) COD_NEGATIVE_PERCENTAGE,
    TRUNCATE(SUM(CASE
            WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_NEUTRAL
        END) / SUM(CASE
            WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_TICKET
        END) * 100,
        2) COD_NEUTRAL_PERCENTAGE,
    ROUND(TRUNCATE(SUM(a.TOTAL_POSITIVE) / SUM(a.TOTAL_TICKET) * 100,
                2)) - ROUND(TRUNCATE(SUM(a.TOTAL_NEGATIVE) / SUM(a.TOTAL_TICKET) * 100,
                2)) NPS_SCORE,
    ROUND(TRUNCATE(SUM(CASE
                    WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_POSITIVE
                END) / SUM(CASE
                    WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_TICKET
                END) * 100,
                2)) - ROUND(TRUNCATE(SUM(CASE
                    WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_NEGATIVE
                END) / SUM(CASE
                    WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_TICKET
                END) * 100,
                2)) CAFE_NPS_SCORE,
    ROUND(TRUNCATE(SUM(CASE
                    WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_POSITIVE
                END) / SUM(CASE
                    WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_TICKET
                END) * 100,
                2)) - ROUND(TRUNCATE(SUM(CASE
                    WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_NEGATIVE
                END) / SUM(CASE
                    WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_TICKET
                END) * 100,
                2)) COD_NPS_SCORE
FROM
    (SELECT
        nd.UNIT_ID,
            ORDER_SOURCE,
            COUNT(*) TOTAL_TICKET,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 0 AND nd.NPS_SCORE <= 6 THEN 1
                ELSE 0
            END) TOTAL_NEGATIVE,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 7 AND nd.NPS_SCORE <= 8 THEN 1
                ELSE 0
            END) TOTAL_NEUTRAL,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 9 AND nd.NPS_SCORE <= 10 THEN 1
                ELSE 0
            END) TOTAL_POSITIVE
    FROM
        KETTLE.ORDER_NPS_DETAIL nd
    INNER JOIN KETTLE.ORDER_DETAIL ud ON ud.ORDER_ID = nd.ORDER_ID
    WHERE
		ud.IS_NEW_CUSTOMER = 'Y' AND
        DATE(nd.SURVEY_CREATION_TIME) >= :quarterStartDate
            AND DATE(nd.SURVEY_CREATION_TIME) <= :businessDate
		AND ud.UNIT_ID = :unitId
    GROUP BY ud.ORDER_SOURCE) a,
    KETTLE_MASTER.UNIT_DETAIL ud
WHERE
    a.UNIT_ID = ud.UNIT_ID
GROUP BY 1 , 2 , 3 , 4
) a

				]]>
                    </content>
                    <params>
                        <param  multiValued="false" name="businessDate" displayName="Business Date"
                                dataType="STRING" />
                        <param  multiValued="false" name="monthStartDate" displayName="Business Date"
                                dataType="STRING" />
                        <param  multiValued="false" name="quarterStartDate" displayName="Business Date"
                                dataType="STRING" />
                        <param  multiValued="false" name="unitId" displayName="unitId"
                                dataType="INTEGER" />
                    </params>

                </report>

                <report name="NPS Dump"
                        executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[
SELECT
A.GENERATED_ORDER_ID,
A.NPS_SCORE,
B.NPS_QUESTION,
B.NPS_RESPONSE,
A.ORDER_ITEMS, A.ORDER_SOURCE, A.IS_NEW_CUSTOMER,
A.BILL_TIME, A.DISPATCH_TIME, A.DISPATCH_TIME_IN_MINUTES,
A.DELIVERY_TIME, A.DELIVERY_TIME_IN_MINUTES, A.SETTLED_AMOUNT,
A.LOCALITY,
A.PARTNER_DISPLAY_NAME
 FROM
(
    SELECT
    ond.SURVEY_RESPONSE_ID,
    ond.GENERATED_ORDER_ID,
    ond.NPS_SCORE,
	ond.NPS_QUESTION,
    ond.NPS_RESPONSE,
    GROUP_CONCAT(CONCAT(oi.PRODUCT_NAME, '(', oi.QUANTITY, ')')
        SEPARATOR '|') ORDER_ITEMS,
    od.ORDER_SOURCE,
    od.IS_NEW_CUSTOMER,
    od.BILLING_SERVER_TIME BILL_TIME,
    ose.UPDATE_TIME DISPATCH_TIME,
    CASE
        WHEN
            ose.UPDATE_TIME IS NOT NULL
        THEN
            (TIMESTAMPDIFF(MINUTE,
                od.BILLING_SERVER_TIME,
                ose.UPDATE_TIME))
        ELSE NULL
    END DISPATCH_TIME_IN_MINUTES,
    ose1.UPDATE_TIME DELIVERY_TIME,
    CASE
        WHEN
            ose1.UPDATE_TIME IS NOT NULL
        THEN
            (TIMESTAMPDIFF(MINUTE,
                od.BILLING_SERVER_TIME,
                ose1.UPDATE_TIME))
        ELSE NULL
    END DELIVERY_TIME_IN_MINUTES,
    od.SETTLED_AMOUNT,
    cia.LOCALITY,
    cp.PARTNER_DISPLAY_NAME
FROM
    KETTLE.ORDER_NPS_DETAIL ond
        INNER JOIN
    KETTLE.ORDER_DETAIL od ON od.ORDER_ID = ond.ORDER_ID
        INNER JOIN
    KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
        INNER JOIN
    KETTLE.CHANNEL_PARTNER cp ON od.CHANNEL_PARTNER_ID = cp.PARTNER_ID
        LEFT OUTER JOIN
    KETTLE.CUSTOMER_ADDRESS_INFO cia ON od.DELIVERY_ADDRESS = cia.ADDRESS_ID
        LEFT OUTER JOIN
    KETTLE.ORDER_STATUS_EVENT ose ON ose.ORDER_ID = od.ORDER_ID
        AND ose.TO_STATUS = 'SETTLED'
        LEFT OUTER JOIN
    KETTLE.ORDER_STATUS_EVENT ose1 ON ose1.ORDER_ID = od.ORDER_ID
        AND ose1.TO_STATUS = 'DELIVERED'
WHERE
    ond.UNIT_ID = :unitId
        AND ond.SURVEY_CREATION_TIME > (SELECT
            CLOSURE_START_TIME
        FROM
            UNIT_CLOSURE_DETAILS
        WHERE
            UNIT_ID = :unitId
                AND BUSINESS_DATE = DATE_ADD(:businessDate, INTERVAL -1 DAY)
                AND CURRENT_STATUS = 'INITIATED')
GROUP BY ond.GENERATED_ORDER_ID , ond.NPS_SCORE , od.ORDER_SOURCE , od.BILLING_SERVER_TIME , od.SETTLED_AMOUNT , cia.LOCALITY ) A
LEFT JOIN
KETTLE.ORDER_NPS_RESPONSE_DATA B ON B.SURVEY_RESPONSE_ID = A.SURVEY_RESPONSE_ID;


				]]>
                    </content>
                    <params>
                        <param  multiValued="false" name="businessDate" displayName="Business Date"
                                dataType="STRING" />
                        <param  multiValued="false" name="unitId" displayName="unitId"
                                dataType="INTEGER" />
                    </params>

                </report>
            </reports>
        </category>


        <category name="Delivery Report" type="Automated" accessCode="Automated">
            <reports>
                <report name="QTD OTD Summary"
                        executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[

	SELECT
    x.UNIT_ID,
    u.UNIT_NAME,
    'OVERALL' OTD_TYPE,
    TRUNCATE(COALESCE(x.ON_TIME_ORDERS / x.TOTAL_ORDERS * 100),
        0) OTD,
    x.TOTAL_ORDERS,
    x.ON_TIME_ORDERS,
    x.DELAYED_ORDERS
FROM
    (SELECT
        a.UNIT_ID,
            COUNT(*) TOTAL_ORDERS,
            SUM(CASE
                WHEN
                   (DELIVERY_PARTNER_ID = 8 AND DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME > 40)
                        OR (DELIVERY_PARTNER_ID = 5 AND DISPATCH_TIME IS NOT NULL
                        AND DISPATCH_TIME > 14)
                THEN
                    1
                ELSE 0
            END) DELAYED_ORDERS,
            SUM(CASE
                WHEN
                    (DELIVERY_PARTNER_ID = 8 AND DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME <= 40)
                        OR (DELIVERY_PARTNER_ID = 5 AND DISPATCH_TIME IS NOT NULL
                        AND DISPATCH_TIME <= 14)
                THEN
                    1
                ELSE 0
            END) ON_TIME_ORDERS
    FROM
        (SELECT
        od.UNIT_ID,
            od.ORDER_ID,
            od.DELIVERY_PARTNER_ID,
            TIMESTAMPDIFF(MINUTE, od.BILLING_SERVER_TIME, MAX(ose.UPDATE_TIME)) DISPATCH_TIME,
            TIMESTAMPDIFF(MINUTE, MAX(ose1.START_TIME), MAX(ose1.UPDATE_TIME)) DELIVERY_TIME
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE.ORDER_STATUS_EVENT ose ON od.ORDER_ID = ose.ORDER_ID
        AND ose.TO_STATUS = 'SETTLED' and ose.TRANSITION_STATUS="SUCCESS"
    LEFT OUTER JOIN KETTLE.ORDER_STATUS_EVENT ose1 ON od.ORDER_ID = ose1.ORDER_ID
        AND ose1.TO_STATUS = 'DELIVERED' and ose1.TRANSITION_STATUS="SUCCESS"
    WHERE
        od.ORDER_SOURCE = 'COD'
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND od.DELIVERY_PARTNER_ID IN (5 , 8)
            AND od.BUSINESS_DATE >= :quarterStartDate
            AND od.BUSINESS_DATE <= :businessDate
            AND od.UNIT_ID = :unitId GROUP BY od.ORDER_ID) a
    GROUP BY a.UNIT_ID) x,
    KETTLE_MASTER.UNIT_DETAIL u
WHERE
    x.UNIT_ID = u.UNIT_ID
UNION ALL SELECT
    x.UNIT_ID,
    u.UNIT_NAME,
    'DELIVERY' OTD_TYPE,
    TRUNCATE(COALESCE(x.ON_TIME_ORDERS / x.TOTAL_ORDERS * 100),
        0) OTD,
    x.TOTAL_ORDERS,
    x.ON_TIME_ORDERS,
    x.DELAYED_ORDERS
FROM
    (SELECT
        a.UNIT_ID,
            COUNT(*) TOTAL_ORDERS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME > 40
                THEN
                    1
                ELSE 0
            END) DELAYED_ORDERS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME <= 40
                THEN
                    1
                ELSE 0
            END) ON_TIME_ORDERS
    FROM
        (SELECT
        od.UNIT_ID,
            od.ORDER_ID,
            TIMESTAMPDIFF(MINUTE, MAX(ose1.START_TIME), MAX(ose1.UPDATE_TIME)) DELIVERY_TIME
    FROM
        KETTLE.ORDER_DETAIL od
    LEFT OUTER JOIN KETTLE.ORDER_STATUS_EVENT ose1 ON od.ORDER_ID = ose1.ORDER_ID
        AND ose1.TO_STATUS = 'DELIVERED' and ose1.TRANSITION_STATUS="SUCCESS"
    WHERE
        od.ORDER_SOURCE = 'COD'
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND od.DELIVERY_PARTNER_ID = 8
            AND od.BUSINESS_DATE >= :quarterStartDate
            AND od.BUSINESS_DATE <= :businessDate
            AND od.UNIT_ID = :unitId GROUP BY od.ORDER_ID) a
    GROUP BY a.UNIT_ID) x,
    KETTLE_MASTER.UNIT_DETAIL u
WHERE
    x.UNIT_ID = u.UNIT_ID
UNION ALL SELECT
    x.UNIT_ID,
    u.UNIT_NAME,
    'DISPATCH' OTD_TYPE,
    TRUNCATE(COALESCE(x.ON_TIME_ORDERS / x.TOTAL_ORDERS * 100),
        0) OTD,
    x.TOTAL_ORDERS,
    x.ON_TIME_ORDERS,
    x.DELAYED_ORDERS
FROM
    (SELECT
        a.UNIT_ID,
            COUNT(*) TOTAL_ORDERS,
            SUM(CASE
                WHEN
                    DISPATCH_TIME IS NOT NULL
                        AND DISPATCH_TIME > 14
                THEN
                    1
                ELSE 0
            END) DELAYED_ORDERS,
            SUM(CASE
                WHEN
                    DISPATCH_TIME IS NOT NULL
                        AND DISPATCH_TIME <= 14
                THEN
                    1
                ELSE 0
            END) ON_TIME_ORDERS
    FROM
        (SELECT
        od.UNIT_ID,
            od.ORDER_ID,
            TIMESTAMPDIFF(MINUTE, od.BILLING_SERVER_TIME, MAX(ose.UPDATE_TIME)) DISPATCH_TIME
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE.ORDER_STATUS_EVENT ose ON od.ORDER_ID = ose.ORDER_ID
        AND ose.TO_STATUS = 'SETTLED' and ose.TRANSITION_STATUS="SUCCESS"
    WHERE
        od.ORDER_SOURCE = 'COD'
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND od.DELIVERY_PARTNER_ID = 5
            AND od.BUSINESS_DATE >= :quarterStartDate
            AND od.BUSINESS_DATE <= :businessDate
            AND od.UNIT_ID = :unitId GROUP BY od.ORDER_ID) a
    GROUP BY a.UNIT_ID) x,
    KETTLE_MASTER.UNIT_DETAIL u
WHERE
    x.UNIT_ID = u.UNIT_ID



				]]>
                    </content>
                    <params>
                        <param  multiValued="false" name="quarterStartDate" displayName="Quarter Start Date"
                                dataType="STRING" />
                        <param  multiValued="false" name="businessDate" displayName="Business Date"
                                dataType="STRING" />
                        <param  multiValued="false" name="unitId" displayName="unitId"
                                dataType="INTEGER" />
                    </params>

                </report>
                <report name="QTD Store Performance"
                        executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[

	SELECT
    x.UNIT_ID,
    x.TOTAL_COD_ORDERS,
    x.AVG_DISPATCH_TIME,
    x.AVG_DELIVERY_TIME,
    x.DELIVERED_ORDERS,
    x.DELAYED_DELIVERED_ORDERS,
    TRUNCATE(COALESCE(x.DELAYED_DELIVERED_ORDERS / x.DELIVERED_ORDERS * 100),
        0) PERCENTAGE_DELAY,
    nps.COD_NPS_SCORE,
    nps.TOTAL_COD_POSITIVE,
    nps.TOTAL_COD_NEGATIVE,
    nps.RANK_OF_THE_DAY NPS_RANK,
    nps.NPS_SCORE
FROM
    (SELECT
        a.UNIT_ID,
            COUNT(*) TOTAL_COD_ORDERS,
            TRUNCATE(AVG(DISPATCH_TIME), 1) AVG_DISPATCH_TIME,
            TRUNCATE(AVG(DELIVERY_TIME), 1) AVG_DELIVERY_TIME,
            SUM(CASE
                WHEN DELIVERY_TIME IS NULL THEN 0
                ELSE 1
            END) DELIVERED_ORDERS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME > 40
                THEN
                    1
                ELSE 0
            END) DELAYED_DELIVERED_ORDERS
    FROM
        (SELECT
        od.UNIT_ID,
            od.ORDER_ID,
            TIMESTAMPDIFF(MINUTE, od.BILLING_SERVER_TIME, MAX(ose.UPDATE_TIME)) DISPATCH_TIME,
            TIMESTAMPDIFF(MINUTE, MAX(ose1.START_TIME), MAX(ose1.UPDATE_TIME)) DELIVERY_TIME
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE.ORDER_STATUS_EVENT ose ON od.ORDER_ID = ose.ORDER_ID
        AND ose.TO_STATUS = 'SETTLED' and ose.TRANSITION_STATUS="SUCCESS"
    LEFT OUTER JOIN KETTLE.ORDER_STATUS_EVENT ose1 ON od.ORDER_ID = ose1.ORDER_ID
        AND ose1.TO_STATUS = 'DELIVERED' and ose1.TRANSITION_STATUS="SUCCESS"
    WHERE
        od.ORDER_SOURCE = 'COD'
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND od.BUSINESS_DATE >= :quarterStartDate AND od.BUSINESS_DATE <= :businessDate
            AND od.UNIT_ID = :unitId GROUP BY od.ORDER_ID) a
    GROUP BY a.UNIT_ID) x,
    KETTLE.DAY_WISE_NPS_SCORE nps
WHERE
    nps.UNIT_ID = x.UNIT_ID
        AND nps.BUSINESS_DATE = DATE_ADD(:businessDate, INTERVAL -1 DAY)


				]]>
                    </content>
                    <params>
                        <param  multiValued="false" name="quarterStartDate" displayName="Quarter Start Date"
                                dataType="STRING" />
                        <param  multiValued="false" name="businessDate" displayName="Business Date"
                                dataType="STRING" />
                        <param  multiValued="false" name="unitId" displayName="unitId"
                                dataType="INTEGER" />
                    </params>

                </report>
                <report name="QTD Summary"
                        executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[

	SELECT
    x.TOTAL_COD_ORDERS,
    x.DISPATCH_IN_LESS_THAN_2_MINS,
    x.DISPATCH_IN_LESS_THAN_8_MINS,
    x.DISPATCH_IN_GREATER_THAN_8_MINS,
    x.DELIVERY_LESS_THAN_20_MINS,
    x.DELIVERY_LESS_THAN_30_MINS,
    x.DELIVERY_LESS_THAN_40_MINS,
    x.DELIVERY_GREATER_THAN_40_MINS,
    x.DELIVERED_ORDERS,
    x.AVG_DISPATCH_TIME,
    TRUNCATE(x.DISPATCH_IN_GREATER_THAN_8_MINS / x.TOTAL_COD_ORDERS * 100,
        0) PERCENTAGE_DISPATCH_DELAYS,
    x.AVG_DELIVERY_TIME,
    TRUNCATE(x.DELIVERY_GREATER_THAN_40_MINS / x.DELIVERED_ORDERS * 100,
        0) PERCENTAGE_DELIVERY_DELAYS,
    x.RIDE_GREATER_THAN_37_MINS,
    x.AVG_RIDE_TIME,
    TRUNCATE(x.RIDE_GREATER_THAN_37_MINS / x.DELIVERED_ORDERS * 100,
        0) PERCENTAGE_RIDE_DELAYS
FROM
    (SELECT
        a.UNIT_ID,
            COUNT(*) TOTAL_COD_ORDERS,
            TRUNCATE(AVG(DISPATCH_TIME), 1) AVG_DISPATCH_TIME,
            TRUNCATE(AVG(DELIVERY_TIME), 1) AVG_DELIVERY_TIME,
            TRUNCATE(AVG(RIDE_TIME), 1) AVG_RIDE_TIME,
            SUM(CASE
                WHEN
                    DISPATCH_TIME IS NOT NULL
                        AND DISPATCH_TIME <= 2
                THEN
                    1
                ELSE 0
            END) DISPATCH_IN_LESS_THAN_2_MINS,
            SUM(CASE
                WHEN
                    DISPATCH_TIME IS NOT NULL
                        AND DISPATCH_TIME > 2
                        AND DISPATCH_TIME <= 8
                THEN
                    1
                ELSE 0
            END) DISPATCH_IN_LESS_THAN_8_MINS,
            SUM(CASE
                WHEN
                    DISPATCH_TIME IS NOT NULL
                        AND DISPATCH_TIME > 8
                THEN
                    1
                ELSE 0
            END) DISPATCH_IN_GREATER_THAN_8_MINS,
            SUM(CASE
                WHEN DELIVERY_TIME IS NULL THEN 0
                ELSE 1
            END) DELIVERED_ORDERS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME <= 20
                THEN
                    1
                ELSE 0
            END) DELIVERY_LESS_THAN_20_MINS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME > 20
                        AND DELIVERY_TIME <= 30
                THEN
                    1
                ELSE 0
            END) DELIVERY_LESS_THAN_30_MINS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME > 30
                        AND DELIVERY_TIME <= 40
                THEN
                    1
                ELSE 0
            END) DELIVERY_LESS_THAN_40_MINS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME > 40
                THEN
                    1
                ELSE 0
            END) DELIVERY_GREATER_THAN_40_MINS,
            SUM(CASE
                WHEN RIDE_TIME IS NOT NULL AND RIDE_TIME > 37 THEN 1
                ELSE 0
            END) RIDE_GREATER_THAN_37_MINS
    FROM
        (SELECT
        od.UNIT_ID,
            od.ORDER_ID,
            TIMESTAMPDIFF(MINUTE, od.BILLING_SERVER_TIME, MAX(ose.UPDATE_TIME)) DISPATCH_TIME,
            TIMESTAMPDIFF(MINUTE, od.BILLING_SERVER_TIME, MAX(ose1.UPDATE_TIME)) DELIVERY_TIME,
            TIMESTAMPDIFF(MINUTE, MAX(ose1.START_TIME), MAX(ose1.UPDATE_TIME)) RIDE_TIME
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE.ORDER_STATUS_EVENT ose ON od.ORDER_ID = ose.ORDER_ID
        AND ose.TO_STATUS = 'SETTLED' and ose.TRANSITION_STATUS="SUCCESS"
    LEFT OUTER JOIN KETTLE.ORDER_STATUS_EVENT ose1 ON od.ORDER_ID = ose1.ORDER_ID
        AND ose1.TO_STATUS = 'DELIVERED' and ose1.TRANSITION_STATUS="SUCCESS"
    WHERE
        od.ORDER_SOURCE = 'COD'
            AND od.ORDER_STATUS <> 'CANCELLED'
              AND od.BUSINESS_DATE >= :quarterStartDate AND od.BUSINESS_DATE <= :businessDate
            AND od.UNIT_ID = :unitId GROUP BY od.ORDER_ID) a
    GROUP BY a.UNIT_ID) x


				]]>
                    </content>
                    <params>
                        <param  multiValued="false" name="businessDate" displayName="Business Date"
                                dataType="STRING" />
                        <param  multiValued="false" name="quarterStartDate" displayName="Quarter Start Date"
                                dataType="STRING" />
                        <param  multiValued="false" name="unitId" displayName="unitId"
                                dataType="INTEGER" />
                    </params>

                </report>

                <report name="QTD Day Part Wise Summary"
                        executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[

	SELECT
    x.DAY_PART,
    x.TOTAL_COD_ORDERS,
    x.DISPATCH_IN_LESS_THAN_2_MINS,
    x.DISPATCH_IN_LESS_THAN_8_MINS,
    x.DISPATCH_IN_GREATER_THAN_8_MINS,
    x.DELIVERY_LESS_THAN_20_MINS,
    x.DELIVERY_LESS_THAN_30_MINS,
    x.DELIVERY_LESS_THAN_40_MINS,
    x.DELIVERY_GREATER_THAN_40_MINS,
    x.DELIVERED_ORDERS,
    x.AVG_DISPATCH_TIME,
    TRUNCATE(x.DISPATCH_IN_GREATER_THAN_8_MINS / x.TOTAL_COD_ORDERS * 100,
        0) PERCENTAGE_DISPATCH_DELAYS,
    x.AVG_DELIVERY_TIME,
    TRUNCATE(x.DELIVERY_GREATER_THAN_40_MINS / x.DELIVERED_ORDERS * 100,
        0) PERCENTAGE_DELIVERY_DELAYS,
    x.RIDE_GREATER_THAN_37_MINS,
    x.AVG_RIDE_TIME,
    TRUNCATE(x.RIDE_GREATER_THAN_37_MINS / x.DELIVERED_ORDERS * 100,
        0) PERCENTAGE_RIDE_DELAYS
FROM
    (SELECT
        a.UNIT_ID,
            a.DAY_PART,
            COUNT(*) TOTAL_COD_ORDERS,
            TRUNCATE(AVG(DISPATCH_TIME), 1) AVG_DISPATCH_TIME,
            TRUNCATE(AVG(DELIVERY_TIME), 1) AVG_DELIVERY_TIME,
            TRUNCATE(AVG(RIDE_TIME), 1) AVG_RIDE_TIME,
            SUM(CASE
                WHEN
                    DISPATCH_TIME IS NOT NULL
                        AND DISPATCH_TIME <= 2
                THEN
                    1
                ELSE 0
            END) DISPATCH_IN_LESS_THAN_2_MINS,
            SUM(CASE
                WHEN
                    DISPATCH_TIME IS NOT NULL
                        AND DISPATCH_TIME > 2
                        AND DISPATCH_TIME <= 8
                THEN
                    1
                ELSE 0
            END) DISPATCH_IN_LESS_THAN_8_MINS,
            SUM(CASE
                WHEN
                    DISPATCH_TIME IS NOT NULL
                        AND DISPATCH_TIME > 8
                THEN
                    1
                ELSE 0
            END) DISPATCH_IN_GREATER_THAN_8_MINS,
            SUM(CASE
                WHEN DELIVERY_TIME IS NULL THEN 0
                ELSE 1
            END) DELIVERED_ORDERS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME <= 20
                THEN
                    1
                ELSE 0
            END) DELIVERY_LESS_THAN_20_MINS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME > 20
                        AND DELIVERY_TIME <= 30
                THEN
                    1
                ELSE 0
            END) DELIVERY_LESS_THAN_30_MINS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME > 30
                        AND DELIVERY_TIME <= 40
                THEN
                    1
                ELSE 0
            END) DELIVERY_LESS_THAN_40_MINS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME > 40
                THEN
                    1
                ELSE 0
            END) DELIVERY_GREATER_THAN_40_MINS,
            SUM(CASE
                WHEN RIDE_TIME IS NOT NULL AND RIDE_TIME > 37 THEN 1
                ELSE 0
            END) RIDE_GREATER_THAN_37_MINS
    FROM
        (SELECT
        od.UNIT_ID,
            od.ORDER_ID,
            CASE
                WHEN
                    HOUR(od.BILLING_SERVER_TIME) >= 6
                        AND HOUR(od.BILLING_SERVER_TIME) < 12
                THEN
                    'BREAKFAST'
                WHEN
                    HOUR(od.BILLING_SERVER_TIME) >= 12
                        AND HOUR(od.BILLING_SERVER_TIME) < 15
                THEN
                    'LUNCH'
                WHEN
                    HOUR(od.BILLING_SERVER_TIME) >= 15
                        AND HOUR(od.BILLING_SERVER_TIME) < 20
                THEN
                    'EVENING'
                WHEN
                    HOUR(od.BILLING_SERVER_TIME) >= 20
                        AND HOUR(od.BILLING_SERVER_TIME) < 22
                THEN
                    'DINNER'
                WHEN
                    HOUR(od.BILLING_SERVER_TIME) >= 22
                        AND HOUR(od.BILLING_SERVER_TIME) <= 23
                THEN
                    'POST_DINNER'
                WHEN
                    HOUR(od.BILLING_SERVER_TIME) >= 0
                        AND HOUR(od.BILLING_SERVER_TIME) < 6
                THEN
                    'NIGHT'
            END DAY_PART,
            TIMESTAMPDIFF(MINUTE, od.BILLING_SERVER_TIME, MAX(ose.UPDATE_TIME)) DISPATCH_TIME,
            TIMESTAMPDIFF(MINUTE, od.BILLING_SERVER_TIME, MAX(ose1.UPDATE_TIME)) DELIVERY_TIME,
            TIMESTAMPDIFF(MINUTE, MAX(ose1.START_TIME), MAX(ose1.UPDATE_TIME)) RIDE_TIME
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE.ORDER_STATUS_EVENT ose ON od.ORDER_ID = ose.ORDER_ID
        AND ose.TO_STATUS = 'SETTLED' and ose.TRANSITION_STATUS="SUCCESS"
    LEFT OUTER JOIN KETTLE.ORDER_STATUS_EVENT ose1 ON od.ORDER_ID = ose1.ORDER_ID
        AND ose1.TO_STATUS = 'DELIVERED' and ose1.TRANSITION_STATUS="SUCCESS"
    WHERE
        od.ORDER_SOURCE = 'COD'
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND od.BUSINESS_DATE >= :quarterStartDate AND od.BUSINESS_DATE <= :businessDate
            AND od.UNIT_ID = :unitId GROUP BY od.ORDER_ID) a
    GROUP BY a.UNIT_ID , a.DAY_PART) x


				]]>
                    </content>
                    <params>
                        <param  multiValued="false" name="businessDate" displayName="Business Date"
                                dataType="STRING" />
                        <param  multiValued="false" name="quarterStartDate" displayName="Quarter Start Date"
                                dataType="STRING" />
                        <param  multiValued="false" name="unitId" displayName="unitId"
                                dataType="INTEGER" />
                    </params>

                </report>


                <report name="QTD SDP Performance Summary"
                        executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[

	SELECT
    x.DELIVERY_BOY_ID,
    x.DELIVERY_BOY_NAME,
    x.DELIVERED_ORDERS,
    x.NO_OF_DAYS,
    TRUNCATE(x.DELIVERED_ORDERS/x.NO_OF_DAYS, 2) NO_OF_ORDERS_PER_DAY,
    x.DELIVERY_LESS_THAN_20_MINS,
    x.DELIVERY_LESS_THAN_30_MINS,
    x.DELIVERY_LESS_THAN_40_MINS,
    x.DELIVERY_GREATER_THAN_40_MINS,
    x.AVG_DELIVERY_TIME,
    TRUNCATE(x.DELIVERY_GREATER_THAN_40_MINS / x.DELIVERED_ORDERS * 100,
        0) PERCENTAGE_DELIVERY_DELAYS,
    x.RIDE_GREATER_THAN_37_MINS,
    x.AVG_RIDE_TIME,
    TRUNCATE(x.RIDE_GREATER_THAN_37_MINS / x.DELIVERED_ORDERS * 100,
        0) PERCENTAGE_RIDE_DELAYS
FROM
    (SELECT
        a.UNIT_ID,
            a.DELIVERY_BOY_ID,
            a.DELIVERY_BOY_NAME,
            DAY(:businessDate) NO_OF_DAYS,
            COUNT(*) TOTAL_COD_ORDERS,
            TRUNCATE(AVG(DELIVERY_TIME), 1) AVG_DELIVERY_TIME,
            TRUNCATE(AVG(RIDE_TIME), 1) AVG_RIDE_TIME,
            SUM(CASE
                WHEN DELIVERY_TIME IS NULL THEN 0
                ELSE 1
            END) DELIVERED_ORDERS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME <= 20
                THEN
                    1
                ELSE 0
            END) DELIVERY_LESS_THAN_20_MINS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME > 20
                        AND DELIVERY_TIME <= 30
                THEN
                    1
                ELSE 0
            END) DELIVERY_LESS_THAN_30_MINS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME > 30
                        AND DELIVERY_TIME <= 40
                THEN
                    1
                ELSE 0
            END) DELIVERY_LESS_THAN_40_MINS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME > 40
                THEN
                    1
                ELSE 0
            END) DELIVERY_GREATER_THAN_40_MINS,
            SUM(CASE
                WHEN RIDE_TIME IS NOT NULL AND RIDE_TIME > 37 THEN 1
                ELSE 0
            END) RIDE_GREATER_THAN_37_MINS
    FROM
        (SELECT
        od.UNIT_ID,
            od.ORDER_ID,
            dd.DELIVERY_BOY_ID,
            dd.DELIVERY_BOY_NAME,
            TIMESTAMPDIFF(MINUTE, od.BILLING_SERVER_TIME, MAX(ose1.UPDATE_TIME)) DELIVERY_TIME,
            TIMESTAMPDIFF(MINUTE, MAX(ose1.START_TIME), MAX(ose1.UPDATE_TIME)) RIDE_TIME
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE.DELIVERY_DETAIL dd ON od.ORDER_ID = dd.ORDER_ID
    INNER JOIN KETTLE.ORDER_STATUS_EVENT ose1 ON od.ORDER_ID = ose1.ORDER_ID
        AND ose1.TO_STATUS = 'DELIVERED' and ose1.TRANSITION_STATUS="SUCCESS"
    WHERE
        od.ORDER_SOURCE = 'COD'
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND od.BUSINESS_DATE >= :quarterStartDate AND od.BUSINESS_DATE <= :businessDate
            AND od.UNIT_ID = :unitId GROUP BY od.ORDER_ID) a
    GROUP BY a.UNIT_ID , a.DELIVERY_BOY_ID , a.DELIVERY_BOY_NAME) x


				]]>
                    </content>
                    <params>
                        <param  multiValued="false" name="businessDate" displayName="Business Date"
                                dataType="STRING" />
                        <param  multiValued="false" name="quarterStartDate" displayName="Quarter Start Date"
                                dataType="STRING" />
                        <param  multiValued="false" name="unitId" displayName="unitId"
                                dataType="INTEGER" />
                    </params>

                </report>

                <report name="MTD Store Performance"
                        executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[

	SELECT
    x.UNIT_ID,
    x.TOTAL_COD_ORDERS,
    x.AVG_DISPATCH_TIME,
    x.AVG_DELIVERY_TIME,
    x.DELIVERED_ORDERS,
    x.DELAYED_DELIVERED_ORDERS,
    TRUNCATE(COALESCE(x.DELAYED_DELIVERED_ORDERS / x.DELIVERED_ORDERS * 100),
        0) PERCENTAGE_DELAY,
    nps.COD_NPS_SCORE,
    nps.TOTAL_COD_POSITIVE,
    nps.TOTAL_COD_NEGATIVE,
    nps.RANK_OF_THE_DAY NPS_RANK,
    nps.NPS_SCORE
FROM
    (SELECT
        a.UNIT_ID,
            COUNT(*) TOTAL_COD_ORDERS,
            TRUNCATE(AVG(DISPATCH_TIME), 1) AVG_DISPATCH_TIME,
            TRUNCATE(AVG(DELIVERY_TIME), 1) AVG_DELIVERY_TIME,
            SUM(CASE
                WHEN DELIVERY_TIME IS NULL THEN 0
                ELSE 1
            END) DELIVERED_ORDERS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME > 40
                THEN
                    1
                ELSE 0
            END) DELAYED_DELIVERED_ORDERS
    FROM
        (SELECT
        od.UNIT_ID,
            od.ORDER_ID,
            TIMESTAMPDIFF(MINUTE, od.BILLING_SERVER_TIME, MAX(ose.UPDATE_TIME)) DISPATCH_TIME,
            TIMESTAMPDIFF(MINUTE, MAX(ose1.START_TIME), MAX(ose1.UPDATE_TIME)) DELIVERY_TIME
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE.ORDER_STATUS_EVENT ose ON od.ORDER_ID = ose.ORDER_ID
        AND ose.TO_STATUS = 'SETTLED' and ose.TRANSITION_STATUS="SUCCESS"
    LEFT OUTER JOIN KETTLE.ORDER_STATUS_EVENT ose1 ON od.ORDER_ID = ose1.ORDER_ID
        AND ose1.TO_STATUS = 'DELIVERED' and ose1.TRANSITION_STATUS="SUCCESS"
    WHERE
        od.ORDER_SOURCE = 'COD'
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND MONTH(od.BUSINESS_DATE) = MONTH(:businessDate)
			AND YEAR(od.BUSINESS_DATE) = YEAR(:businessDate)
            AND od.UNIT_ID = :unitId GROUP BY od.ORDER_ID) a
    GROUP BY a.UNIT_ID) x,
    KETTLE.DAY_WISE_NPS_SCORE nps
WHERE
    nps.UNIT_ID = x.UNIT_ID
        AND nps.BUSINESS_DATE = DATE_ADD(:businessDate, INTERVAL -1 DAY)


				]]>
                    </content>
                    <params>
                        <param  multiValued="false" name="businessDate" displayName="Business Date"
                                dataType="STRING" />
                        <param  multiValued="false" name="unitId" displayName="unitId"
                                dataType="INTEGER" />
                    </params>

                </report>



                <report name="MTD Summary"
                        executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[

	SELECT
    x.TOTAL_COD_ORDERS,
    x.DISPATCH_IN_LESS_THAN_2_MINS,
    x.DISPATCH_IN_LESS_THAN_8_MINS,
    x.DISPATCH_IN_GREATER_THAN_8_MINS,
    x.DELIVERY_LESS_THAN_20_MINS,
    x.DELIVERY_LESS_THAN_30_MINS,
    x.DELIVERY_LESS_THAN_40_MINS,
    x.DELIVERY_GREATER_THAN_40_MINS,
    x.DELIVERED_ORDERS,
    x.AVG_DISPATCH_TIME,
    TRUNCATE(x.DISPATCH_IN_GREATER_THAN_8_MINS / x.TOTAL_COD_ORDERS * 100,
        0) PERCENTAGE_DISPATCH_DELAYS,
    x.AVG_DELIVERY_TIME,
    TRUNCATE(x.DELIVERY_GREATER_THAN_40_MINS / x.DELIVERED_ORDERS * 100,
        0) PERCENTAGE_DELIVERY_DELAYS,
    x.RIDE_GREATER_THAN_37_MINS,
    x.AVG_RIDE_TIME,
    TRUNCATE(x.RIDE_GREATER_THAN_37_MINS / x.DELIVERED_ORDERS * 100,
        0) PERCENTAGE_RIDE_DELAYS
FROM
    (SELECT
        a.UNIT_ID,
            COUNT(*) TOTAL_COD_ORDERS,
            TRUNCATE(AVG(DISPATCH_TIME), 1) AVG_DISPATCH_TIME,
            TRUNCATE(AVG(DELIVERY_TIME), 1) AVG_DELIVERY_TIME,
            TRUNCATE(AVG(RIDE_TIME), 1) AVG_RIDE_TIME,
            SUM(CASE
                WHEN
                    DISPATCH_TIME IS NOT NULL
                        AND DISPATCH_TIME <= 2
                THEN
                    1
                ELSE 0
            END) DISPATCH_IN_LESS_THAN_2_MINS,
            SUM(CASE
                WHEN
                    DISPATCH_TIME IS NOT NULL
                        AND DISPATCH_TIME > 2
                        AND DISPATCH_TIME <= 8
                THEN
                    1
                ELSE 0
            END) DISPATCH_IN_LESS_THAN_8_MINS,
            SUM(CASE
                WHEN
                    DISPATCH_TIME IS NOT NULL
                        AND DISPATCH_TIME > 8
                THEN
                    1
                ELSE 0
            END) DISPATCH_IN_GREATER_THAN_8_MINS,
            SUM(CASE
                WHEN DELIVERY_TIME IS NULL THEN 0
                ELSE 1
            END) DELIVERED_ORDERS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME <= 20
                THEN
                    1
                ELSE 0
            END) DELIVERY_LESS_THAN_20_MINS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME > 20
                        AND DELIVERY_TIME <= 30
                THEN
                    1
                ELSE 0
            END) DELIVERY_LESS_THAN_30_MINS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME > 30
                        AND DELIVERY_TIME <= 40
                THEN
                    1
                ELSE 0
            END) DELIVERY_LESS_THAN_40_MINS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME > 40
                THEN
                    1
                ELSE 0
            END) DELIVERY_GREATER_THAN_40_MINS,
            SUM(CASE
                WHEN RIDE_TIME IS NOT NULL AND RIDE_TIME > 37 THEN 1
                ELSE 0
            END) RIDE_GREATER_THAN_37_MINS
    FROM
        (SELECT
        od.UNIT_ID,
            od.ORDER_ID,
            TIMESTAMPDIFF(MINUTE, od.BILLING_SERVER_TIME, MAX(ose.UPDATE_TIME)) DISPATCH_TIME,
            TIMESTAMPDIFF(MINUTE, od.BILLING_SERVER_TIME, MAX(ose1.UPDATE_TIME)) DELIVERY_TIME,
            TIMESTAMPDIFF(MINUTE, MAX(ose1.START_TIME), MAX(ose1.UPDATE_TIME)) RIDE_TIME
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE.ORDER_STATUS_EVENT ose ON od.ORDER_ID = ose.ORDER_ID
        AND ose.TO_STATUS = 'SETTLED' and ose.TRANSITION_STATUS="SUCCESS"
    LEFT OUTER JOIN KETTLE.ORDER_STATUS_EVENT ose1 ON od.ORDER_ID = ose1.ORDER_ID
        AND ose1.TO_STATUS = 'DELIVERED' and ose1.TRANSITION_STATUS="SUCCESS"
    WHERE
        od.ORDER_SOURCE = 'COD'
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND MONTH(od.BUSINESS_DATE) = MONTH(:businessDate)
			AND YEAR(od.BUSINESS_DATE) = YEAR(:businessDate)
            AND od.UNIT_ID = :unitId GROUP BY od.ORDER_ID) a
    GROUP BY a.UNIT_ID) x


				]]>
                    </content>
                    <params>
                        <param  multiValued="false" name="businessDate" displayName="Business Date"
                                dataType="STRING" />
                        <param  multiValued="false" name="unitId" displayName="unitId"
                                dataType="INTEGER" />
                    </params>

                </report>

                <report name="MTD Day Part Wise Summary"
                        executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[

	SELECT
    x.DAY_PART,
    x.TOTAL_COD_ORDERS,
    x.DISPATCH_IN_LESS_THAN_2_MINS,
    x.DISPATCH_IN_LESS_THAN_8_MINS,
    x.DISPATCH_IN_GREATER_THAN_8_MINS,
    x.DELIVERY_LESS_THAN_20_MINS,
    x.DELIVERY_LESS_THAN_30_MINS,
    x.DELIVERY_LESS_THAN_40_MINS,
    x.DELIVERY_GREATER_THAN_40_MINS,
    x.DELIVERED_ORDERS,
    x.AVG_DISPATCH_TIME,
    TRUNCATE(x.DISPATCH_IN_GREATER_THAN_8_MINS / x.TOTAL_COD_ORDERS * 100,
        0) PERCENTAGE_DISPATCH_DELAYS,
    x.AVG_DELIVERY_TIME,
    TRUNCATE(x.DELIVERY_GREATER_THAN_40_MINS / x.DELIVERED_ORDERS * 100,
        0) PERCENTAGE_DELIVERY_DELAYS,
    x.RIDE_GREATER_THAN_37_MINS,
    x.AVG_RIDE_TIME,
    TRUNCATE(x.RIDE_GREATER_THAN_37_MINS / x.DELIVERED_ORDERS * 100,
        0) PERCENTAGE_RIDE_DELAYS
FROM
    (SELECT
        a.UNIT_ID,
            a.DAY_PART,
            COUNT(*) TOTAL_COD_ORDERS,
            TRUNCATE(AVG(DISPATCH_TIME), 1) AVG_DISPATCH_TIME,
            TRUNCATE(AVG(DELIVERY_TIME), 1) AVG_DELIVERY_TIME,
            TRUNCATE(AVG(RIDE_TIME), 1) AVG_RIDE_TIME,
            SUM(CASE
                WHEN
                    DISPATCH_TIME IS NOT NULL
                        AND DISPATCH_TIME <= 2
                THEN
                    1
                ELSE 0
            END) DISPATCH_IN_LESS_THAN_2_MINS,
            SUM(CASE
                WHEN
                    DISPATCH_TIME IS NOT NULL
                        AND DISPATCH_TIME > 2
                        AND DISPATCH_TIME <= 8
                THEN
                    1
                ELSE 0
            END) DISPATCH_IN_LESS_THAN_8_MINS,
            SUM(CASE
                WHEN
                    DISPATCH_TIME IS NOT NULL
                        AND DISPATCH_TIME > 8
                THEN
                    1
                ELSE 0
            END) DISPATCH_IN_GREATER_THAN_8_MINS,
            SUM(CASE
                WHEN DELIVERY_TIME IS NULL THEN 0
                ELSE 1
            END) DELIVERED_ORDERS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME <= 20
                THEN
                    1
                ELSE 0
            END) DELIVERY_LESS_THAN_20_MINS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME > 20
                        AND DELIVERY_TIME <= 30
                THEN
                    1
                ELSE 0
            END) DELIVERY_LESS_THAN_30_MINS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME > 30
                        AND DELIVERY_TIME <= 40
                THEN
                    1
                ELSE 0
            END) DELIVERY_LESS_THAN_40_MINS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME > 40
                THEN
                    1
                ELSE 0
            END) DELIVERY_GREATER_THAN_40_MINS,
            SUM(CASE
                WHEN RIDE_TIME IS NOT NULL AND RIDE_TIME > 37 THEN 1
                ELSE 0
            END) RIDE_GREATER_THAN_37_MINS
    FROM
        (SELECT
        od.UNIT_ID,
            od.ORDER_ID,
            CASE
                WHEN
                    HOUR(od.BILLING_SERVER_TIME) >= 6
                        AND HOUR(od.BILLING_SERVER_TIME) < 12
                THEN
                    'BREAKFAST'
                WHEN
                    HOUR(od.BILLING_SERVER_TIME) >= 12
                        AND HOUR(od.BILLING_SERVER_TIME) < 15
                THEN
                    'LUNCH'
                WHEN
                    HOUR(od.BILLING_SERVER_TIME) >= 15
                        AND HOUR(od.BILLING_SERVER_TIME) < 20
                THEN
                    'EVENING'
                WHEN
                    HOUR(od.BILLING_SERVER_TIME) >= 20
                        AND HOUR(od.BILLING_SERVER_TIME) < 22
                THEN
                    'DINNER'
                WHEN
                    HOUR(od.BILLING_SERVER_TIME) >= 22
                        AND HOUR(od.BILLING_SERVER_TIME) <= 23
                THEN
                    'POST_DINNER'
                WHEN
                    HOUR(od.BILLING_SERVER_TIME) >= 0
                        AND HOUR(od.BILLING_SERVER_TIME) < 6
                THEN
                    'NIGHT'
            END DAY_PART,
            TIMESTAMPDIFF(MINUTE, od.BILLING_SERVER_TIME, MAX(ose.UPDATE_TIME)) DISPATCH_TIME,
            TIMESTAMPDIFF(MINUTE, od.BILLING_SERVER_TIME, MAX(ose1.UPDATE_TIME)) DELIVERY_TIME,
            TIMESTAMPDIFF(MINUTE, MAX(ose1.START_TIME), MAX(ose1.UPDATE_TIME)) RIDE_TIME
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE.ORDER_STATUS_EVENT ose ON od.ORDER_ID = ose.ORDER_ID
        AND ose.TO_STATUS = 'SETTLED' and ose.TRANSITION_STATUS="SUCCESS"
    LEFT OUTER JOIN KETTLE.ORDER_STATUS_EVENT ose1 ON od.ORDER_ID = ose1.ORDER_ID
        AND ose1.TO_STATUS = 'DELIVERED' and ose1.TRANSITION_STATUS="SUCCESS"
    WHERE
        od.ORDER_SOURCE = 'COD'
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND MONTH(od.BUSINESS_DATE) = MONTH(:businessDate)
			AND YEAR(od.BUSINESS_DATE) = YEAR(:businessDate)
            AND od.UNIT_ID = :unitId GROUP BY od.ORDER_ID) a
    GROUP BY a.UNIT_ID , a.DAY_PART) x


				]]>
                    </content>
                    <params>
                        <param  multiValued="false" name="businessDate" displayName="Business Date"
                                dataType="STRING" />
                        <param  multiValued="false" name="unitId" displayName="unitId"
                                dataType="INTEGER" />
                    </params>

                </report>


                <report name="MTD SDP Performance Summary"
                        executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[

	SELECT
    x.DELIVERY_BOY_ID,
    x.DELIVERY_BOY_NAME,
    x.DELIVERED_ORDERS,
    x.NO_OF_DAYS,
    TRUNCATE(x.DELIVERED_ORDERS/x.NO_OF_DAYS, 2) NO_OF_ORDERS_PER_DAY,
    x.DELIVERY_LESS_THAN_20_MINS,
    x.DELIVERY_LESS_THAN_30_MINS,
    x.DELIVERY_LESS_THAN_40_MINS,
    x.DELIVERY_GREATER_THAN_40_MINS,
    x.AVG_DELIVERY_TIME,
    TRUNCATE(x.DELIVERY_GREATER_THAN_40_MINS / x.DELIVERED_ORDERS * 100,
        0) PERCENTAGE_DELIVERY_DELAYS,
    x.RIDE_GREATER_THAN_37_MINS,
    x.AVG_RIDE_TIME,
    TRUNCATE(x.RIDE_GREATER_THAN_37_MINS / x.DELIVERED_ORDERS * 100,
        0) PERCENTAGE_RIDE_DELAYS
FROM
    (SELECT
        a.UNIT_ID,
            a.DELIVERY_BOY_ID,
            a.DELIVERY_BOY_NAME,
            DAY(:businessDate) NO_OF_DAYS,
            COUNT(*) TOTAL_COD_ORDERS,
            TRUNCATE(AVG(DELIVERY_TIME), 1) AVG_DELIVERY_TIME,
            TRUNCATE(AVG(RIDE_TIME), 1) AVG_RIDE_TIME,
            SUM(CASE
                WHEN DELIVERY_TIME IS NULL THEN 0
                ELSE 1
            END) DELIVERED_ORDERS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME <= 20
                THEN
                    1
                ELSE 0
            END) DELIVERY_LESS_THAN_20_MINS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME > 20
                        AND DELIVERY_TIME <= 30
                THEN
                    1
                ELSE 0
            END) DELIVERY_LESS_THAN_30_MINS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME > 30
                        AND DELIVERY_TIME <= 40
                THEN
                    1
                ELSE 0
            END) DELIVERY_LESS_THAN_40_MINS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME > 40
                THEN
                    1
                ELSE 0
            END) DELIVERY_GREATER_THAN_40_MINS,
            SUM(CASE
                WHEN RIDE_TIME IS NOT NULL AND RIDE_TIME > 37 THEN 1
                ELSE 0
            END) RIDE_GREATER_THAN_37_MINS
    FROM
        (SELECT
        od.UNIT_ID,
            od.ORDER_ID,
            dd.DELIVERY_BOY_ID,
            dd.DELIVERY_BOY_NAME,
            TIMESTAMPDIFF(MINUTE, od.BILLING_SERVER_TIME, MAX(ose.UPDATE_TIME)) DISPATCH_TIME,
            TIMESTAMPDIFF(MINUTE, od.BILLING_SERVER_TIME, MAX(ose1.UPDATE_TIME)) DELIVERY_TIME,
            TIMESTAMPDIFF(MINUTE, MAX(ose1.START_TIME), MAX(ose1.UPDATE_TIME)) RIDE_TIME
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE.DELIVERY_DETAIL dd ON od.ORDER_ID = dd.ORDER_ID
    INNER JOIN KETTLE.ORDER_STATUS_EVENT ose ON od.ORDER_ID = ose.ORDER_ID
        AND ose.TO_STATUS = 'SETTLED' and ose.TRANSITION_STATUS="SUCCESS"
    LEFT OUTER JOIN KETTLE.ORDER_STATUS_EVENT ose1 ON od.ORDER_ID = ose1.ORDER_ID
        AND ose1.TO_STATUS = 'DELIVERED' and ose1.TRANSITION_STATUS="SUCCESS"
    WHERE
        od.ORDER_SOURCE = 'COD'
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND MONTH(od.BUSINESS_DATE) = MONTH(:businessDate)
			AND YEAR(od.BUSINESS_DATE) = YEAR(:businessDate)
            AND od.UNIT_ID = :unitId GROUP BY od.ORDER_ID) a
    GROUP BY a.UNIT_ID , a.DELIVERY_BOY_ID , a.DELIVERY_BOY_NAME) x


				]]>
                    </content>
                    <params>
                        <param  multiValued="false" name="businessDate" displayName="Business Date"
                                dataType="STRING" />
                        <param  multiValued="false" name="unitId" displayName="unitId"
                                dataType="INTEGER" />
                    </params>

                </report>
                <report name="Current Day Store Performance"
                        executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[

	SELECT
    x.UNIT_ID,
    x.TOTAL_COD_ORDERS,
    x.AVG_DISPATCH_TIME,
    x.AVG_DELIVERY_TIME,
    x.DELIVERED_ORDERS,
    x.DELAYED_DELIVERED_ORDERS,
    TRUNCATE(COALESCE(x.DELAYED_DELIVERED_ORDERS / x.DELIVERED_ORDERS * 100),
        0) PERCENTAGE_DELAY,
    nps.COD_NPS_SCORE,
    nps.TOTAL_COD_POSITIVE,
    nps.TOTAL_COD_NEGATIVE,
    nps.RANK_OF_THE_DAY NPS_RANK,
    nps.NPS_SCORE
FROM
    (SELECT
        a.UNIT_ID,
            COUNT(*) TOTAL_COD_ORDERS,
            TRUNCATE(AVG(DISPATCH_TIME), 1) AVG_DISPATCH_TIME,
            TRUNCATE(AVG(DELIVERY_TIME), 1) AVG_DELIVERY_TIME,
            SUM(CASE
                WHEN DELIVERY_TIME IS NULL THEN 0
                ELSE 1
            END) DELIVERED_ORDERS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME > 40
                THEN
                    1
                ELSE 0
            END) DELAYED_DELIVERED_ORDERS
    FROM
        (SELECT
        od.UNIT_ID,
            od.ORDER_ID,
            TIMESTAMPDIFF(MINUTE, od.BILLING_SERVER_TIME, MAX(ose.UPDATE_TIME)) DISPATCH_TIME,
            TIMESTAMPDIFF(MINUTE, MAX(ose1.START_TIME), MAX(ose1.UPDATE_TIME)) DELIVERY_TIME
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE.ORDER_STATUS_EVENT ose ON od.ORDER_ID = ose.ORDER_ID
        AND ose.TO_STATUS = 'SETTLED' and ose.TRANSITION_STATUS = "SUCCESS"
    LEFT OUTER JOIN KETTLE.ORDER_STATUS_EVENT ose1 ON od.ORDER_ID = ose1.ORDER_ID
        AND ose1.TO_STATUS = 'DELIVERED' and ose1.TRANSITION_STATUS = "SUCCESS"
    WHERE
        od.ORDER_SOURCE = 'COD'
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND od.BUSINESS_DATE = :businessDate
            AND od.UNIT_ID = :unitId GROUP BY od.ORDER_ID) a
    GROUP BY a.UNIT_ID) x,
    KETTLE.DAY_WISE_NPS_SCORE nps
WHERE
    nps.UNIT_ID = x.UNIT_ID
        AND nps.BUSINESS_DATE = DATE_ADD(:businessDate, INTERVAL -1 DAY)


				]]>
                    </content>
                    <params>
                        <param  multiValued="false" name="businessDate" displayName="Business Date"
                                dataType="STRING" />
                        <param  multiValued="false" name="unitId" displayName="unitId"
                                dataType="INTEGER" />
                    </params>

                </report>

                <report name="Current Day Summary"
                        executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[

	SELECT
    x.TOTAL_COD_ORDERS,
    x.DISPATCH_IN_LESS_THAN_2_MINS,
    x.DISPATCH_IN_LESS_THAN_8_MINS,
    x.DISPATCH_IN_GREATER_THAN_8_MINS,
    x.DELIVERY_LESS_THAN_20_MINS,
    x.DELIVERY_LESS_THAN_30_MINS,
    x.DELIVERY_LESS_THAN_40_MINS,
    x.DELIVERY_GREATER_THAN_40_MINS,
    x.DELIVERED_ORDERS,
    x.AVG_DISPATCH_TIME,
    TRUNCATE(x.DISPATCH_IN_GREATER_THAN_8_MINS / x.TOTAL_COD_ORDERS * 100,
        0) PERCENTAGE_DISPATCH_DELAYS,
    x.AVG_DELIVERY_TIME,
    TRUNCATE(x.DELIVERY_GREATER_THAN_40_MINS / x.DELIVERED_ORDERS * 100,
        0) PERCENTAGE_DELIVERY_DELAYS,
    x.RIDE_GREATER_THAN_37_MINS,
    x.AVG_RIDE_TIME,
    TRUNCATE(x.RIDE_GREATER_THAN_37_MINS / x.DELIVERED_ORDERS * 100,
        0) PERCENTAGE_RIDE_DELAYS
FROM
    (SELECT
        a.UNIT_ID,
            COUNT(*) TOTAL_COD_ORDERS,
            TRUNCATE(AVG(DISPATCH_TIME), 1) AVG_DISPATCH_TIME,
            TRUNCATE(AVG(DELIVERY_TIME), 1) AVG_DELIVERY_TIME,
            TRUNCATE(AVG(RIDE_TIME), 1) AVG_RIDE_TIME,
            SUM(CASE
                WHEN
                    DISPATCH_TIME IS NOT NULL
                        AND DISPATCH_TIME <= 2
                THEN
                    1
                ELSE 0
            END) DISPATCH_IN_LESS_THAN_2_MINS,
            SUM(CASE
                WHEN
                    DISPATCH_TIME IS NOT NULL
                        AND DISPATCH_TIME > 2
                        AND DISPATCH_TIME <= 8
                THEN
                    1
                ELSE 0
            END) DISPATCH_IN_LESS_THAN_8_MINS,
            SUM(CASE
                WHEN
                    DISPATCH_TIME IS NOT NULL
                        AND DISPATCH_TIME > 8
                THEN
                    1
                ELSE 0
            END) DISPATCH_IN_GREATER_THAN_8_MINS,
            SUM(CASE
                WHEN DELIVERY_TIME IS NULL THEN 0
                ELSE 1
            END) DELIVERED_ORDERS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME <= 20
                THEN
                    1
                ELSE 0
            END) DELIVERY_LESS_THAN_20_MINS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME > 20
                        AND DELIVERY_TIME <= 30
                THEN
                    1
                ELSE 0
            END) DELIVERY_LESS_THAN_30_MINS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME > 30
                        AND DELIVERY_TIME <= 40
                THEN
                    1
                ELSE 0
            END) DELIVERY_LESS_THAN_40_MINS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME > 40
                THEN
                    1
                ELSE 0
            END) DELIVERY_GREATER_THAN_40_MINS,
            SUM(CASE
                WHEN RIDE_TIME IS NOT NULL AND RIDE_TIME > 37 THEN 1
                ELSE 0
            END) RIDE_GREATER_THAN_37_MINS
    FROM
        (SELECT
        od.UNIT_ID,
            od.ORDER_ID,
            TIMESTAMPDIFF(MINUTE, od.BILLING_SERVER_TIME, MAX(ose.UPDATE_TIME)) DISPATCH_TIME,
            TIMESTAMPDIFF(MINUTE, od.BILLING_SERVER_TIME, MAX(ose1.UPDATE_TIME)) DELIVERY_TIME,
            TIMESTAMPDIFF(MINUTE, MAX(ose1.START_TIME), MAX(ose1.UPDATE_TIME)) RIDE_TIME
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE.ORDER_STATUS_EVENT ose ON od.ORDER_ID = ose.ORDER_ID
        AND ose.TO_STATUS = 'SETTLED' and ose.TRANSITION_STATUS = "SUCCESS"
    LEFT OUTER JOIN KETTLE.ORDER_STATUS_EVENT ose1 ON od.ORDER_ID = ose1.ORDER_ID
        AND ose1.TO_STATUS = 'DELIVERED' and ose1.TRANSITION_STATUS = "SUCCESS"
    WHERE
        od.ORDER_SOURCE = 'COD'
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND od.BUSINESS_DATE = :businessDate
            AND od.UNIT_ID = :unitId GROUP BY od.ORDER_ID) a
    GROUP BY a.UNIT_ID) x


				]]>
                    </content>
                    <params>
                        <param  multiValued="false" name="businessDate" displayName="Business Date"
                                dataType="STRING" />
                        <param  multiValued="false" name="unitId" displayName="unitId"
                                dataType="INTEGER" />
                    </params>

                </report>

                <report name="Current Day Part Wise Summary"
                        executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[

	SELECT
    x.DAY_PART,
    x.TOTAL_COD_ORDERS,
    x.DISPATCH_IN_LESS_THAN_2_MINS,
    x.DISPATCH_IN_LESS_THAN_8_MINS,
    x.DISPATCH_IN_GREATER_THAN_8_MINS,
    x.DELIVERY_LESS_THAN_20_MINS,
    x.DELIVERY_LESS_THAN_30_MINS,
    x.DELIVERY_LESS_THAN_40_MINS,
    x.DELIVERY_GREATER_THAN_40_MINS,
    x.DELIVERED_ORDERS,
    x.AVG_DISPATCH_TIME,
    TRUNCATE(x.DISPATCH_IN_GREATER_THAN_8_MINS / x.TOTAL_COD_ORDERS * 100,
        0) PERCENTAGE_DISPATCH_DELAYS,
    x.AVG_DELIVERY_TIME,
    TRUNCATE(x.DELIVERY_GREATER_THAN_40_MINS / x.DELIVERED_ORDERS * 100,
        0) PERCENTAGE_DELIVERY_DELAYS,
    x.RIDE_GREATER_THAN_37_MINS,
    x.AVG_RIDE_TIME,
    TRUNCATE(x.RIDE_GREATER_THAN_37_MINS / x.DELIVERED_ORDERS * 100,
        0) PERCENTAGE_RIDE_DELAYS
FROM
    (SELECT
        a.UNIT_ID,
            a.DAY_PART,
            COUNT(*) TOTAL_COD_ORDERS,
            TRUNCATE(AVG(DISPATCH_TIME), 1) AVG_DISPATCH_TIME,
            TRUNCATE(AVG(DELIVERY_TIME), 1) AVG_DELIVERY_TIME,
            TRUNCATE(AVG(RIDE_TIME), 1) AVG_RIDE_TIME,
            SUM(CASE
                WHEN
                    DISPATCH_TIME IS NOT NULL
                        AND DISPATCH_TIME <= 2
                THEN
                    1
                ELSE 0
            END) DISPATCH_IN_LESS_THAN_2_MINS,
            SUM(CASE
                WHEN
                    DISPATCH_TIME IS NOT NULL
                        AND DISPATCH_TIME > 2
                        AND DISPATCH_TIME <= 8
                THEN
                    1
                ELSE 0
            END) DISPATCH_IN_LESS_THAN_8_MINS,
            SUM(CASE
                WHEN
                    DISPATCH_TIME IS NOT NULL
                        AND DISPATCH_TIME > 8
                THEN
                    1
                ELSE 0
            END) DISPATCH_IN_GREATER_THAN_8_MINS,
            SUM(CASE
                WHEN DELIVERY_TIME IS NULL THEN 0
                ELSE 1
            END) DELIVERED_ORDERS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME <= 20
                THEN
                    1
                ELSE 0
            END) DELIVERY_LESS_THAN_20_MINS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME > 20
                        AND DELIVERY_TIME <= 30
                THEN
                    1
                ELSE 0
            END) DELIVERY_LESS_THAN_30_MINS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME > 30
                        AND DELIVERY_TIME <= 40
                THEN
                    1
                ELSE 0
            END) DELIVERY_LESS_THAN_40_MINS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME > 40
                THEN
                    1
                ELSE 0
            END) DELIVERY_GREATER_THAN_40_MINS,
            SUM(CASE
                WHEN RIDE_TIME IS NOT NULL AND RIDE_TIME > 37 THEN 1
                ELSE 0
            END) RIDE_GREATER_THAN_37_MINS
    FROM
        (SELECT
        od.UNIT_ID,
            od.ORDER_ID,
            CASE
                WHEN
                    HOUR(od.BILLING_SERVER_TIME) >= 6
                        AND HOUR(od.BILLING_SERVER_TIME) < 12
                THEN
                    'BREAKFAST'
                WHEN
                    HOUR(od.BILLING_SERVER_TIME) >= 12
                        AND HOUR(od.BILLING_SERVER_TIME) < 15
                THEN
                    'LUNCH'
                WHEN
                    HOUR(od.BILLING_SERVER_TIME) >= 15
                        AND HOUR(od.BILLING_SERVER_TIME) < 20
                THEN
                    'EVENING'
                WHEN
                    HOUR(od.BILLING_SERVER_TIME) >= 20
                        AND HOUR(od.BILLING_SERVER_TIME) < 22
                THEN
                    'DINNER'
                WHEN
                    HOUR(od.BILLING_SERVER_TIME) >= 22
                        AND HOUR(od.BILLING_SERVER_TIME) <= 23
                THEN
                    'POST_DINNER'
                WHEN
                    HOUR(od.BILLING_SERVER_TIME) >= 0
                        AND HOUR(od.BILLING_SERVER_TIME) < 6
                THEN
                    'NIGHT'
            END DAY_PART,
            TIMESTAMPDIFF(MINUTE, od.BILLING_SERVER_TIME, MAX(ose.UPDATE_TIME)) DISPATCH_TIME,
            TIMESTAMPDIFF(MINUTE, od.BILLING_SERVER_TIME, MAX(ose1.UPDATE_TIME)) DELIVERY_TIME,
            TIMESTAMPDIFF(MINUTE, MAX(ose1.START_TIME), MAX(ose1.UPDATE_TIME)) RIDE_TIME
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE.ORDER_STATUS_EVENT ose ON od.ORDER_ID = ose.ORDER_ID
        AND ose.TO_STATUS = 'SETTLED' and ose.TRANSITION_STATUS = "SUCCESS"
    LEFT OUTER JOIN KETTLE.ORDER_STATUS_EVENT ose1 ON od.ORDER_ID = ose1.ORDER_ID
        AND ose1.TO_STATUS = 'DELIVERED' and ose1.TRANSITION_STATUS = "SUCCESS"
    WHERE
        od.ORDER_SOURCE = 'COD'
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND od.BUSINESS_DATE = :businessDate
            AND od.UNIT_ID = :unitId GROUP BY od.ORDER_ID) a
    GROUP BY a.UNIT_ID , a.DAY_PART) x


				]]>
                    </content>
                    <params>
                        <param  multiValued="false" name="businessDate" displayName="Business Date"
                                dataType="STRING" />
                        <param  multiValued="false" name="unitId" displayName="unitId"
                                dataType="INTEGER" />
                    </params>

                </report>

                <report name="Current Day SDP Performance Summary"
                        executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[

	SELECT
    x.DELIVERY_BOY_ID,
    x.DELIVERY_BOY_NAME,
    x.DELIVERED_ORDERS,
    x.NO_OF_DAYS,
    TRUNCATE(x.DELIVERED_ORDERS/x.NO_OF_DAYS, 2) NO_OF_ORDERS_PER_DAY,
    x.DELIVERY_LESS_THAN_20_MINS,
    x.DELIVERY_LESS_THAN_30_MINS,
    x.DELIVERY_LESS_THAN_40_MINS,
    x.DELIVERY_GREATER_THAN_40_MINS,
    x.AVG_DELIVERY_TIME,
    TRUNCATE(x.DELIVERY_GREATER_THAN_40_MINS / x.DELIVERED_ORDERS * 100,
        0) PERCENTAGE_DELIVERY_DELAYS,
    x.RIDE_GREATER_THAN_37_MINS,
    x.AVG_RIDE_TIME,
    TRUNCATE(x.RIDE_GREATER_THAN_37_MINS / x.DELIVERED_ORDERS * 100,
        0) PERCENTAGE_RIDE_DELAYS
FROM
    (SELECT
        a.UNIT_ID,
            a.DELIVERY_BOY_ID,
            a.DELIVERY_BOY_NAME,
            DAY(:businessDate) NO_OF_DAYS,
            COUNT(*) TOTAL_COD_ORDERS,
            TRUNCATE(AVG(DELIVERY_TIME), 1) AVG_DELIVERY_TIME,
            TRUNCATE(AVG(RIDE_TIME), 1) AVG_RIDE_TIME,
            SUM(CASE
                WHEN DELIVERY_TIME IS NULL THEN 0
                ELSE 1
            END) DELIVERED_ORDERS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME <= 20
                THEN
                    1
                ELSE 0
            END) DELIVERY_LESS_THAN_20_MINS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME > 20
                        AND DELIVERY_TIME <= 30
                THEN
                    1
                ELSE 0
            END) DELIVERY_LESS_THAN_30_MINS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME > 30
                        AND DELIVERY_TIME <= 40
                THEN
                    1
                ELSE 0
            END) DELIVERY_LESS_THAN_40_MINS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME > 40
                THEN
                    1
                ELSE 0
            END) DELIVERY_GREATER_THAN_40_MINS,
            SUM(CASE
                WHEN RIDE_TIME IS NOT NULL AND RIDE_TIME > 37 THEN 1
                ELSE 0
            END) RIDE_GREATER_THAN_37_MINS
    FROM
        (SELECT
        od.UNIT_ID,
            od.ORDER_ID,
            dd.DELIVERY_BOY_ID,
            dd.DELIVERY_BOY_NAME,
            TIMESTAMPDIFF(MINUTE, od.BILLING_SERVER_TIME, MAX(ose1.UPDATE_TIME)) DELIVERY_TIME,
            TIMESTAMPDIFF(MINUTE, MAX(ose1.START_TIME), MAX(ose1.UPDATE_TIME)) RIDE_TIME
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE.DELIVERY_DETAIL dd ON od.ORDER_ID = dd.ORDER_ID
    INNER JOIN KETTLE.ORDER_STATUS_EVENT ose1 ON od.ORDER_ID = ose1.ORDER_ID
        AND ose1.TO_STATUS = 'DELIVERED' and ose1.TRANSITION_STATUS = "SUCCESS"
    WHERE
        od.ORDER_SOURCE = 'COD'
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND od.BUSINESS_DATE = :businessDate
            AND od.UNIT_ID = :unitId GROUP BY od.ORDER_ID) a
    GROUP BY a.UNIT_ID , a.DELIVERY_BOY_ID , a.DELIVERY_BOY_NAME) x


				]]>
                    </content>
                    <params>
                        <param  multiValued="false" name="businessDate" displayName="Business Date"
                                dataType="STRING" />
                        <param  multiValued="false" name="unitId" displayName="unitId"
                                dataType="INTEGER" />
                    </params>

                </report>

                <report name="Current Day Performance"
                        executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[

	SELECT
    x.DELIVERED_ORDERS,
    x.NO_OF_SDP,
    x.DELIVERY_LESS_THAN_20_MINS,
    x.DELIVERY_LESS_THAN_30_MINS,
    x.DELIVERY_LESS_THAN_40_MINS,
    x.DELIVERY_GREATER_THAN_40_MINS,
    x.AVG_DELIVERY_TIME,
    TRUNCATE(x.DELIVERY_GREATER_THAN_40_MINS / x.DELIVERED_ORDERS * 100,
        0) PERCENTAGE_DELIVERY_DELAYS,
    x.RIDE_GREATER_THAN_37_MINS,
    x.AVG_RIDE_TIME,
    TRUNCATE(x.RIDE_GREATER_THAN_37_MINS / x.DELIVERED_ORDERS * 100,
        0) PERCENTAGE_RIDE_DELAYS
FROM
    (SELECT
        a.UNIT_ID,
            COUNT(DISTINCT a.DELIVERY_BOY_ID) NO_OF_SDP,
            COUNT(*) TOTAL_COD_ORDERS,
            TRUNCATE(AVG(DELIVERY_TIME), 1) AVG_DELIVERY_TIME,
            TRUNCATE(AVG(RIDE_TIME), 1) AVG_RIDE_TIME,
            SUM(CASE
                WHEN DELIVERY_TIME IS NULL THEN 0
                ELSE 1
            END) DELIVERED_ORDERS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME <= 20
                THEN
                    1
                ELSE 0
            END) DELIVERY_LESS_THAN_20_MINS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME > 20
                        AND DELIVERY_TIME <= 30
                THEN
                    1
                ELSE 0
            END) DELIVERY_LESS_THAN_30_MINS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME > 30
                        AND DELIVERY_TIME <= 40
                THEN
                    1
                ELSE 0
            END) DELIVERY_LESS_THAN_40_MINS,
            SUM(CASE
                WHEN
                    DELIVERY_TIME IS NOT NULL
                        AND DELIVERY_TIME > 40
                THEN
                    1
                ELSE 0
            END) DELIVERY_GREATER_THAN_40_MINS,
            SUM(CASE
                WHEN RIDE_TIME IS NOT NULL AND RIDE_TIME > 37 THEN 1
                ELSE 0
            END) RIDE_GREATER_THAN_37_MINS
    FROM
        (SELECT
        od.UNIT_ID,
            od.ORDER_ID,
            dd.DELIVERY_BOY_ID,
            dd.DELIVERY_BOY_NAME,
            TIMESTAMPDIFF(MINUTE, od.BILLING_SERVER_TIME, MAX(ose1.UPDATE_TIME)) DELIVERY_TIME,
            TIMESTAMPDIFF(MINUTE, MAX(ose1.START_TIME), MAX(ose1.UPDATE_TIME)) RIDE_TIME
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE.DELIVERY_DETAIL dd ON od.ORDER_ID = dd.ORDER_ID
    INNER JOIN KETTLE.ORDER_STATUS_EVENT ose1 ON od.ORDER_ID = ose1.ORDER_ID
        AND ose1.TO_STATUS = 'DELIVERED' and ose1.TRANSITION_STATUS = "SUCCESS"
    WHERE
        od.ORDER_SOURCE = 'COD'
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND od.BUSINESS_DATE = :businessDate
            AND od.UNIT_ID = :unitId GROUP BY od.ORDER_ID) a
    GROUP BY a.UNIT_ID) x


				]]>
                    </content>
                    <params>
                        <param  multiValued="false" name="businessDate" displayName="Business Date"
                                dataType="STRING" />
                        <param  multiValued="false" name="unitId" displayName="unitId"
                                dataType="INTEGER" />
                    </params>

                </report>
                <report name="Locality Wise Data"
                        executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[

	SELECT
    a.LOCALITY,
    COUNT(*) NO_OF_DELAYED_ORDERS,
    AVG(DELIVERY_TIME) AVG_DELIVERY_TIME,
    AVG(RIDE_TIME) AVG_RIDE_TIME
FROM
    (SELECT
        cia.LOCALITY,
            TIMESTAMPDIFF(MINUTE, od.BILLING_SERVER_TIME, MAX(ose.UPDATE_TIME)) DISPATCH_TIME,
            TIMESTAMPDIFF(MINUTE, od.BILLING_SERVER_TIME, MAX(ose1.UPDATE_TIME)) DELIVERY_TIME,
            TIMESTAMPDIFF(MINUTE, MAX(ose1.START_TIME), MAX(ose1.UPDATE_TIME)) RIDE_TIME
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE.DELIVERY_DETAIL dd ON od.ORDER_ID = dd.ORDER_ID
    INNER JOIN KETTLE.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
    INNER JOIN KETTLE.CUSTOMER_ADDRESS_INFO cia ON cia.ADDRESS_ID = od.DELIVERY_ADDRESS
    INNER JOIN KETTLE.ORDER_STATUS_EVENT ose ON od.ORDER_ID = ose.ORDER_ID
        AND ose.TO_STATUS = 'SETTLED' and ose.TRANSITION_STATUS = "SUCCESS"
    INNER JOIN KETTLE.ORDER_STATUS_EVENT ose1 ON od.ORDER_ID = ose1.ORDER_ID
        AND ose1.TO_STATUS = 'DELIVERED' and ose1.TRANSITION_STATUS = "SUCCESS"
    WHERE
        od.ORDER_SOURCE = 'COD'
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND MONTH(od.BUSINESS_DATE) = MONTH(:businessDate)
			AND YEAR(od.BUSINESS_DATE) = YEAR(:businessDate)
            AND od.UNIT_ID = :unitId GROUP BY od.ORDER_ID) a
GROUP BY a.LOCALITY
ORDER BY AVG_DELIVERY_TIME DESC



				]]>
                    </content>
                    <params>
                        <param  multiValued="false" name="businessDate" displayName="Business Date"
                                dataType="STRING" />
                        <param  multiValued="false" name="unitId" displayName="unitId"
                                dataType="INTEGER" />
                    </params>

                </report>
                <report name="List Of Delayed Orders"
                        executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[

	SELECT
    od.GENERATED_ORDER_ID,
    dd.DELIVERY_BOY_ID SDP_ID,
    dd.DELIVERY_BOY_NAME SDP_NAME,
    ci.FIRST_NAME CUSTOMER_NAME,
    cia.LOCALITY,
    cia.ADDRESS_LINE_1,
    TIMESTAMPDIFF(MINUTE, od.BILLING_SERVER_TIME, MAX(ose.UPDATE_TIME)) DISPATCH_TIME,
    TIMESTAMPDIFF(MINUTE,od.BILLING_SERVER_TIME,MAX(ose1.UPDATE_TIME)) DELIVERY_TIME,
    TIMESTAMPDIFF(MINUTE,MAX(ose1.START_TIME),MAX(ose1.UPDATE_TIME)) RIDE_TIME
FROM
    KETTLE.ORDER_DETAIL od
        INNER JOIN
    KETTLE.DELIVERY_DETAIL dd ON od.ORDER_ID = dd.ORDER_ID
        INNER JOIN
    KETTLE.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
        INNER JOIN
    KETTLE.CUSTOMER_ADDRESS_INFO cia ON cia.ADDRESS_ID = od.DELIVERY_ADDRESS
        INNER JOIN
    KETTLE.ORDER_STATUS_EVENT ose ON od.ORDER_ID = ose.ORDER_ID
        AND ose.TO_STATUS = 'SETTLED' and ose.TRANSITION_STATUS="SUCCESS"
        INNER JOIN
    KETTLE.ORDER_STATUS_EVENT ose1 ON od.ORDER_ID = ose1.ORDER_ID
        AND ose1.TO_STATUS = 'DELIVERED' and ose1.TRANSITION_STATUS="SUCCESS"
WHERE
    od.ORDER_SOURCE = 'COD'
        AND od.ORDER_STATUS <> 'CANCELLED'
        AND od.BUSINESS_DATE = :businessDate
        AND od.UNIT_ID = :unitId
GROUP BY od.ORDER_ID HAVING DELIVERY_TIME > 40


				]]>
                    </content>
                    <params>
                        <param  multiValued="false" name="businessDate" displayName="Business Date"
                                dataType="STRING" />
                        <param  multiValued="false" name="unitId" displayName="unitId"
                                dataType="INTEGER" />
                    </params>

                </report>
            </reports>
        </category>
    </categories>
</ReportCategories>



