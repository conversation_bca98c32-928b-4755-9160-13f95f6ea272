<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="modal-header" data-ng-init="init()">
    <h3 style="margin: 0;">Select Redemption Product : {{redemptionCount}}</h3>
</div>

<div class="modal-body">
    <div class="container-fluid">
        <div class ="row" style="display: flex;align-items: center;position: relative">
                <button class='btn btn-lg posProductButton' type="button"
                        ng-repeat="num in redemptionSelectionProducts"
                        ng-click="setProduct(num)">
                    {{num.name}}
                </button>
        </div>
        <div class="row">
            <div style = "display: flex ; flex-direction: row ; overflow-x: scroll"ng-if =" customerFavChaiMappings!=null && customerFavChaiMappings.length>0 && customerBasicInfo !=null && customerBasicInfo!=undefined && customerBasicInfo.id !=undefined && customerBasicInfo.id !=null && customerBasicInfo.id>5">
                <div  style ="display: inline-block ; margin-top:0px;" ng-repeat="customerFavChaiMapping in customerFavChaiMappings " >
                    <div style="display: flex;justify-content: center;align-items: center;position: relative;top:15px;z-index: 2" ng-if="isShowFavChaiInRedemption(customerFavChaiMapping)">
                        <div style="background-color: #E5E5E5;" ></div>
                        <div style =" width:60% ;border-radius :20px;height:30px;background-color: red; display:flex; justify-content: center;align-items: center">
                            <text style="color: white;font-weight: bold">{{customerFavChaiMapping.tagType}}</text>
                        </div>
                        <div style="background-color: #E5E5E5;"></div>
                    </div>

                    <button class='btn btn-lg favChaiButton'  style="text-align: left;padding-left: 6px ; margin-right: 10px"type="button"
                            ng-class="{vegButton:item.attribute == 'VEG',nonVegButton:item.attribute == 'NON_VEG'}"
                            ng-if="isShowFavChaiInRedemption(customerFavChaiMapping)"
                            ng-click="setProductFromCustomerFavChaiMapping(customerFavChaiMapping)">
                        <span style="color: black;font-weight: bold;">{{getFavChaiCustomizationShortCodes(customerFavChaiMapping)}}</span>
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal-footer">
</div>
