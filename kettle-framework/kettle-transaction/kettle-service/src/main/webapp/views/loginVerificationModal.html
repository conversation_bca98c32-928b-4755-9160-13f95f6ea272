<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="modal-header">
    <div flash-message="5000" ></div>
    <h3 class="text-center">Verification Login</h3>
</div>

<div class="modal-body">
    <form novalidate role="form" name="LoginModalForm" autocomplete="off">

        <div class="form-group">
            <label for="username" style="margin-left: 15px">User Id</label>
            <input ng-disabled="presetUserCode" type="number" name="username" id="username" class="input-form-control"
                   ng-minlength="6" ng-maxlength="6"
                   style="width:200px; margin-left: 20px;margin-top: 15px"
                   ng-model="usercode" autocomplete="off" required />
        </div>

        <input type="text" style="display:none">

        <div ng-messages="LoginModalForm.username.$error" style="color:maroon" role="alert">
            <div ng-if="LoginModalForm.username.$touched">
                <div ng-message="required">You did not enter user id</div>
                <div ng-message="minlength">user id min length is 6</div>
                <div ng-message="maxlength">user id max length is 6</div>
            </div>
        </div>


        <div class="form-group">
            <label for="Password" style="margin-left: 15px">Passcode</label>
            <input type="password" name="password" id="Password"
                   class="input-form-control"
                   style="width:200px;font-family: sans-serif;font-size: 18px;margin-top: 15px"
                   ng-model="pwd" autocomplete="off" required/>
        </div>

        <div ng-messages="LoginModalForm.Password.$error" style="color:maroon" role="alert">
            <div ng-if="LoginModalForm.Password.$touched">
                <div ng-message="required">You did not enter passcode field</div>
            </div>
        </div>

        <div class="form-group" ng-show="showComment">
            <label for="comment" style="margin-left: 15px">Comment: <span ng-if="mandatoryComment">**</span></label>
            <textarea class="form-control" rows="3" name="comment" id="comment"
                      style="margin: 18px; width: 200px"
                      ng-model="commentText" required>
                    </textarea>
        </div>
    </form>
</div>


<div class="modal-footer">

    <div class="btn-group">
        <a href="#" class="btn btn-danger" ng-click="cancel()">Cancel</a>
        <a href="#" class="btn btn-default" ng-click="authenticate()"
           ng-disabled="dataLoading">Submit</a>
    </div>
</div>
