<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="modal-header">
    <div flash-message="5000" ></div>
    <h3 class="text-center" >Select Terminal</h3>
</div>

<div class="modal-body">
        <div class="row productRow" >
            <button ng-repeat="terminal in terminalArray" class='btn btn-lg'
                    ng-class="{addOnButton: !terminal.checked, addOnButtonSelected: terminal.checked}"
                    type="button"
                    ng-click="selectedTerminal($index)">{{terminal.name}}</button>
        </div>

</div>

<div class="modal-footer">

    <div class="btn-group">
        <a href="#" class="btn btn-danger" ng-click="cancel()">Cancel</a>
        <a href="#" class="btn btn-default" ng-click="print()">Proceed</a>
    </div>

</div>
