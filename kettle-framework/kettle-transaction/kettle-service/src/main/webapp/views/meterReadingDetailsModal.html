<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style>
.firstItem {
	margin-left: 5%;
	font-size: 25px;
}

.increaseSize {
	font-size: 20px;
}

.reading-card {
	border: 2px solid red;
	margin: 0px;
	padding: 10px;
	margin-bottom: 8px;
}
</style>
<div class="modal-content">
	<div class="modal-header" data-ng-init="init()">
		<div flash-message="10000"></div>
		<div class="row">
			<div class="col-xs-10">
				<h2 class="text-center" style="margin-top: 20px;">Meter
					Readings Details</h2>
			</div>
			<div class="col-xs-2">
			<button class="btn btn-info  pull-right"
			data-ng-click="changeViewType('update')" data-ng-if= "viewType == 'add'">Update Reading</button> 
			<button class="btn btn-info  pull-right"
			data-ng-click="changeViewType('add')" data-ng-if= "viewType == 'update'">Add Reading</button> 
			</div>
		</div>
	</div>
	<div class="modal-body">
		<div data-ng-if= "viewType == 'add'">
			<div data-ng-show="false">
				<label class="col-lg-12 firstItem">No meter installed at
					this unit</label>
			</div>
			<form name="meterDetailForm">
				<div class="row">
					<div class="col-xs-12">
						<label>Entry Type</label>
						<div class="form-group">
							<div class="radio">
								<label> <input type="radio" name="entryTypeStart"
									value="DAY_START" data-ng-model="$parent.entryType"> Day Start
									<span style="color: red;">(Timing : 10:00 am To 12:00 am
										OR Within one hour of Dine In Opening Time)</span>
								</label>
							</div>
							<div class="radio">
								<label> <input type="radio" name="entryTypeEnd"
									value="DAY_CLOSE" data-ng-model="$parent.entryType"> Day Close
									<span style="color: red;">(Timing : 10:00 pm To 12:00 pm
										OR Before one hour of Dine In Closing Time)</span>
								</label>
							</div>
						</div>
					</div>
				</div>
				<div class="row reading-card" data-ng-repeat="count in meterCount">
					<div class="col-xs-12">
						<div class="row form-group">
							<div class="col-xs-2">
								<label>Last Reading </label>
							</div>
							<div class="col-xs-4">{{meterReadings[count].lastReading}}</div>
							<div class="col-xs-2">
								<label>Meter Type</label>
							</div>
							<div class="col-xs-4">{{meterReadings[count].billType}}</div>
						</div>
						<div class="row form-group">
							<div class="col-xs-6 ">
								<label>Current Reading No :<span style="color: red;">(Decimal
										numbers are not allowed)</span></label> <input type="number"
									data-ng-model="meterReadings[count].currentReading"
									required="required" class="form-control"
									name="{{count}}{{meterReadings[count].uniqueIndex}}" />
							</div>
						</div>
					</div>
				</div>
				<div class="row reading-card" data-ng-if="unitDetails.dGAvailable">
					<div class="col-xs-12">
						<div class="row form-group">
							<div class="col-xs-2">
								<label>Last Reading </label>
							</div>
							<div class="col-xs-4">{{meterReadings[unitDetails.noOfMeter].lastReading}}</div>
							<div class="col-xs-2">
								<label>Meter Type</label>
							</div>
							<div class="col-xs-4">{{meterReadings[unitDetails.noOfMeter].billType}}</div>
						</div>
						<div class="row form-group">
							<div class="col-xs-6 ">
								<label>Current Reading No :<span style="color: red;">(Decimal
										numbers are not allowed)</span></label> <input type="number"
									data-ng-model="meterReadings[unitDetails.noOfMeter].currentReading"
									required="required" class="form-control"
									name="{{unitDetails.noOfMeter}}{{meterReadings[unitDetails.noOfMeter].uniqueIndex}}" />
							</div>
						</div>
					</div>
				</div>
			</form>
		</div>
		<div data-ng-if="viewType == 'update'">
			<div class="row">
				<div class="col-xs-4">
					<div class="form-group">
						<label class="control-label" for="billType">Bill Type  </label> <select
							class="form-control" id="billType"
							data-ng-model="readingRequestObj.billType"
							data-ng-change="getLastMeterReadingList(readingRequestObj.billType, readingRequestObj.meterNo)">
							<option value="ELECTRICITY">ELECTRICITY</option>
							<option value="DG" data-ng-if="unitDetails.dGAvailable">DG</option>
						</select>
					</div>
				</div>
				<div class="col-xs-3" data-ng-if="!singleMeter">
					<div class="form-group">
						<label class="control-label" for="meterNo">Select Meter 
						</label> <select class="form-control" id="meterNo"
							data-ng-model="readingRequestObj.meterNo"
							data-ng-change="getLastMeterReadingList(readingRequestObj.billType, readingRequestObj.meterNo)">
							<option value="1">1</option>
							<option value="2">2</option>
						</select>
						<div></div>
					</div>
				</div>
				<!-- <div class="col-xs-4">
					<button class="btn btn-warning"
						data-ng-click="getLastMeterReadingList()">Get
						Details</button>
				</div> -->
			</div>
			<div class="row" data-ng-if="currentMeterDetail == undefined">
				<label class="col-lg-12 firstItem" style="color: red;">Entry
					not found to update.</label>
			</div>
			<div class="row reading-card"
				data-ng-if="currentMeterDetail != undefined">
				<div class="col-xs-12">
					<div class="row form-group">
						<div class="col-xs-2">
							<label>Last Reading </label>
						</div>
						<div class="col-xs-4">{{lastMeterDetail.currentReading}}</div>
						<div class="col-xs-2">
							<label>Meter Type</label>
						</div>
						<div class="col-xs-4">{{currentMeterDetail.billType}}</div>
					</div>
					<div class="row form-group">
						<div class="col-xs-6 ">
							<label>Current Reading No :<span style="color: red;">(Decimal
									numbers are not allowed)</span></label> <input type="number"
								data-ng-model="currentMeterDetail.currentReading"
								required="required" class="form-control" />
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="modal-footer">
		<button type="button" class="btn btn-danger pull-left"
			ng-click="closeModal()">Close</button>
		<button class="btn btn-warning  pull-right"
			data-ng-click="submitMeterReadings(meterReadings)"
			data-ng-disabled="meterDetailForm.$invalid" data-ng-if="viewType == 'add'">Submit</button>
		<button class="btn btn-warning  pull-right"
			data-ng-click="updateMeterReadings()"
			data-ng-if="viewType == 'update'  && currentMeterDetail != undefined">Update</button>
	</div>
</div>

