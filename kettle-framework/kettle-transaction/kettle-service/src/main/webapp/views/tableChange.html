
<div class="modal-header" data-ng-init="init()">
  <h3 class="text-center" >Select Table</h3>
</div>
	<!-- Table Selection View -->
<div class="modal-body">
  <div class="row">
		<div class="col-xs-12 text-center">
			<button class='btn btn-lg posProductButton lime-background'
				data-ng-class="{'red-orange-background': table.tableStatus == 'OCCUPIED'}"
				type="button" data-ng-click="selectTable(table)"
				data-ng-disabled="table.tableStatus == 'OCCUPIED'"
				data-ng-repeat="table in tables">{{table.tableNumber}}</button>
		</div>
	</div>
</div>
<div class="modal-footer">
  <div class="btn-group">
    <a href="#" class="btn btn-danger" data-ng-click="cancel()">Cancel</a>
  </div>
</div>