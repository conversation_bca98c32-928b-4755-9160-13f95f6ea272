<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
  <div
  class="modal-header"
  data-ng-init="init()">
<div class="row">
  <div class="col-xs-6">
      <h3 style="margin: 0;">Please Enter Last 4 digits of Transaction Id</h3>
  </div>
  <div class="col-xs-6">
      <button
              class='btn btn-danger pull-right'
              type="button"
              data-ng-click="goBack()">Cancel</button>
  </div>
</div>
</div>
<div class="modal-body">
    <div class="container-fluid">

        <div style="padding-top: 10px">
            <div class="row">
                <div class="col-xs-12">
                    <div style="width: 800px; margin-left: 30px">
                        <div class="col-xs-6">
                            <label>Transaction ID</label>
                        </div>
                        <div class="col-xs-6" >
                            <input type="text" minlength="4" maxlength="4" class="form-control pull-right"
                                data-ng-model="transactionId"
                                onkeypress='return event.charCode >= 48 && event.charCode <= 57'>
                        </div>
                        <div class="col-xs-10" style="margin-top: 30px">
                            <button class='btn btn-danger pull-right'
                            data-ng-class="{'disabled':transactionId == null || transactionId.length < 4}"
                            data-ng-disabled="transactionId == null || transactionId.length < 4" type="button"
                            data-ng-click="verifyTransactionId(transactionId)">Verify</button>
                        </div>
                    </div>
                </div>

            </div>
            <div class="alert alert-danger" style="margin-top: 10px;" data-ng-if="errorMessage != null">
                <b>{{errorMessage}}</b>
            </div>
        </div>
    </div>
<div class="modal-footer"></div>
