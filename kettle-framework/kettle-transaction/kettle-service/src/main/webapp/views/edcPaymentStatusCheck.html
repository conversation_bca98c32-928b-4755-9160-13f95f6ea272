<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<div
        class="modal-header"
        data-ng-init="init()">
    <div class="row">
        <div class="col-xs-6">
            <h3 style="margin: 0;">Validate Transaction Status </h3>
        </div>
        <div class="col-xs-6">
            <button
                    class='btn btn-danger pull-right'
                    type="button"
                    data-ng-click="goBack()">Cancel</button>
        </div>
    </div>
</div>
<div class="modal-body">
    <div class="container-fluid">

        <div style="padding-top: 10px">
            <div class="row">
                <div class="col-xs-12">
                        <button class="btn btn-success btn-lg btn-block"
                                type="button"
                                data-ng-click="validateEdcPaymentStatus()">Check Payment Status</button>
                </div>

<!--                <div class="alert alert-danger" style="margin-top: 10px;" data-ng-if="errorMessage != null">-->
<!--                    <b>{{errorMessage}}</b>-->
<!--                </div>-->
            </div>
        </div>
    </div>
</div>

    <div class="modal-footer">
        <div style="padding-top: 10px">
            <div class="row">
                <div class="col-xs-12">
                    <div class="alert alert-danger" style="margin-top: 15px; display: flex; flex-direction: row;justify-content: space-around" data-ng-if="errorMessage != null">
                        <b>{{errorMessage}}</b>
                    </div>
                </div>
            </div>
    </div>
