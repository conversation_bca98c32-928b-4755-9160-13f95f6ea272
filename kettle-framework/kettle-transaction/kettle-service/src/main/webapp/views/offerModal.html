<!--
~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
~ __________________
~
~ [2015] - [2017] Sunshine Teahouse Private Limited
~ All Rights Reserved.
~
~ NOTICE:  All information contained herein is, and remains
~ the property of Sunshine Teahouse Private Limited and its suppliers,
~ if any.  The intellectual and technical concepts contained
~ herein are proprietary to Sunshine Teahouse Private Limited
~ and its suppliers, and are protected by trade secret or copyright law.
~ Dissemination of this information or reproduction of this material
~ is strictly forbidden unless prior written permission is obtained
~ from Sunshine Teahouse Private Limited.
-->

<div class="modal-header" data-ng-init="init()">
    <div class="row container-fluid">
        <h3 data-ng-if="fromChaayosCash" class="modal-title pull-left">Apply Chaayos Cash</h3>
        <h3 data-ng-if="!fromChaayosCash" class="modal-title pull-left">Apply Coupon</h3>
        <button class="btn btn-default pull-right chaayosCancel" type="button" data-ng-click="cancel()">&times;</button>
    </div>
</div>
<div class="modal-body">
    <div class="alert alert-danger" data-ng-if="error.length > 0">
        <span ng-bind-html="error"></span>
    </div>
    <div class="alert alert-info" data-ng-if="offerDescription.length > 0">
        <span><strong>OFFER:</strong>&nbsp;{{offerDescription}}</span>
    </div>
    <div class="row container-fluid" data-ng-if="!disableCoupons">
        <div class="col-xs-offset-3 col-xs-9" data-ng-if="!fromChaayosCash">
            <div class="input-group">
                <input type="text" class="couponCode" ng-model="couponCode.coupon" data-ng-disabled="applyDisabled"
                       ng-change="setCouponCode(couponCode.coupon)">
                <div class="btn-group">
                    <button class="btn btn-primary" type="button" data-ng-click="applyOffer()"
                            data-ng-disabled="applyDisabled">Apply
                    </button>
                    <button class="btn btn-warning" type="button" data-ng-click="clear()">Remove</button>
                </div>
            </div>
        </div>
        <div class="col-xs-12" style="margin-top: 5px;" data-ng-if="showOtpInput">
            <label style="margin-right: 5px; margin-left: 103px;">OTP: </label>
            <input type="text" placeholder="Enter OTP here" ng-model="otpByCustomer"
                   ng-change="verifyOTP(otpByCustomer)">
        </div>
        <div class="col-xs-12 ng-scope" data-ng-if="otpBanner != ''">
            <br>
            <div class="text-center" data-ng-if="!showOtpInput">
                <otp-model></otp-model>
            </div>

            <div class="alert text-center" style="margin-top: 5px"
                 ng-class="otpBanner != 'OTP Verified...' ? (otpBanner != 'OTP Sent...' ? 'otp-verifying' : 'otp-sent'): 'otp-verified'">
                {{otpBanner}}
            </div>
            <br>
        </div>
        <br/>
        <div class="container-fluid">
            <div class="row couponCodeContainer" data-ng-if="!fromChaayosCash && !disableCoupons">
                <div class="container-fluid">
                    <div class="row">
                        <div class="col-xs-3 text-center couponCodes"
                             data-ng-repeat="offer in offers" data-ng-if="offer.maxUsage !== offer.usage" data-ng-click="applyThisOffer(offer)">
                            <span class="chaayosBadge" >{{offer.code}}</span>
                            <br/>
                            <span>{{offer.offer.description}}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="container-fluid" data-ng-if="disableCoupons">
        <div class="row couponCodeContainer">
            <div class="col-xs-12 alert alert-danger">Offers not available! Order items marked complimentary</div>
        </div>
    </div>
    <div class="container-fluid" data-ng-if="!referralRedemptionAvailable">
        <div class="row couponCodeContainer">
            <div class="col-xs-12 alert alert-danger">Chaayos Cash is not applicable on Kettle. Please ask customer to place order via Chaayos App</div>
        </div>
    </div>
</div>

