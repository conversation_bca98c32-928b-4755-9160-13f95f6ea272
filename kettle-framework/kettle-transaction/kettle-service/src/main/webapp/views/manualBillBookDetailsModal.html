<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style>
.firstItem {
	margin-left: 5%;
	font-size: 25px;
}

.increaseSize {
	font-size: 20px;
}
</style>
<div class="modal-content">
	<div class="modal-header" data-ng-init="init()">
		<button type="button" class="btn btn-danger" ng-click="closeModal()">X</button>

		<div flash-message="5000"></div>
		<h3 class="text-center">Manual Bill Book List</h3>

	</div>
	<div data-ng-if="!isPaidEmployeeMeal && !isGiftCardModalOpen"><label
			style="float: left;font-size: 19px;margin-left: 50px;margin-right: 20px;">GST:
	</label><input type="text" class="form-control col-sm-2" style="width:150px" data-ng-model="gstNo"
				   data-ng-change="gstNoChangeListener(gstNo)"/>
		<div class="pull-right">
			<button class="btn btn-warning  increaseSize" data-ng-click="validateGst()"> Validate GST </button>
		</div>
	</div>
	<div class="modal-body" data-ng-if="matchesGst">
		<div class="row">
			<div class="col-lg-12" style="margin-top: 20px;">
				<h2 class="text-center">Active Bill Details</h2>
			</div>
		</div>
		
    <div class="cancel">
        <button data-ng-show="loader.loading" class="btn btn-success">Loading...
        <i class="fa fa-spinner fa-spin"></i>
        </button>
        
    </div>
		<div data-ng-show="activatedManualBillBookDetails.length==0 && !loader.loading">
		<label class="col-lg-12 firstItem">No Active bills</label>
		</div>
		<div data-ng-show="activatedManualBillBookDetails.length>0">
		<div class="row">

			<div class="col-lg-6 firstItem"><label>Start Bill No :</label> {{activatedManualBillBookDetails[activatedManualBillBookDetails.length-1].startNo}}</div>
			<div class="col-lg-6 firstItem"><label>End Bill No : </label>{{activatedManualBillBookDetails[activatedManualBillBookDetails.length-1].endNo}}</div>
		</div>
		<div class="row">

			<div class="col-md-6 firstItem"><label>Total Bill :</label> {{activatedManualBillBookDetails[activatedManualBillBookDetails.length-1].totalBill}}</div>
			<div class="col-lg-6 firstItem"><label>Remaining Bill : </label> {{activatedManualBillBookDetails[activatedManualBillBookDetails.length-1].remainingBillCount}}</div>
		</div>
		</div>
		<div  data-ng-show="createdManualBillBookDetails.length>0">
		<div class="col-lg-12" style="margin-top: 20px;margin-bottom: 20px;">
			<h2 class="text-center">Available Bill Details </h2>
		</div>


		<div class="row">
			<div class="col-lg-3 increaseSize" style="margin-left: 5%;"><label>Start Bill No :</label> {{createdManualBillBookDetails[createdManualBillBookDetails.length-1].startNo}}</div>
			<div class="col-lg-3 increaseSize"><label>End Bill No :</label> {{createdManualBillBookDetails[createdManualBillBookDetails.length-1].endNo}}</div>
			<div class="col-lg-4 ">
				<button class="btn btn-warning  increaseSize"
					data-ng-click="activateManualBillBook(createdManualBillBookDetails[createdManualBillBookDetails.length-1])">Activate</button>
			</div>

		</div>
		</div>
	</div>
	<div class="modal-footer">
		<button type="button" data-ng-if="matchesGst" class="btn btn-danger" ng-click="closeModal()">Close</button>
	</div>
</div>

