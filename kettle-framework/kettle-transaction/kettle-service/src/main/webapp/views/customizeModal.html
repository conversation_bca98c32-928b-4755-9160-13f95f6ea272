<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="modal-header">
    <div data-flash-message="5000" ></div>
  <h3 class="text-center" >Customization Options</h3>
</div>

<div class="modal-body">
  <form novalidate role="form" name="customizationForm">
    <div class="form-group">

      <div class="checkbox" data-ng-if="orderType!='subscription'">
        <label>
          <input type="checkbox" value="" id="isComplimentary"
                 data-ng-model="selectedOrderItem.orderDetails.complimentaryDetail.isComplimentary">
          Complimentary
        </label>
      </div>

      <div data-ng-if="orderType!='subscription'" collapse="!selectedOrderItem.orderDetails.complimentaryDetail.isComplimentary">

        <div class="form-group">
          <label for="sel1" style="margin: 15px;">Complimentary reason:</label>
          <select class="form-control"
                  id="sel1"
                  style="margin-left: 15px; width: 180px"
                  ng-model="selectedOrderItem.orderDetails.complimentaryDetail.reasonCode"
                  ng-options="complimentary.id as complimentary.name for complimentary in transactionMetadata.complimentaryCodes.content" required>
          </select>
        </div>

        <div class="form-group">
          <label for="comment" style="margin-left: 15px">Comment:</label>
                    <textarea class="form-control" rows="3" name="comment" id="comment"
                              style="margin: 15px; width: 240px"
                              ng-model="selectedOrderItem.orderDetails.complimentaryDetail.reason">
                    </textarea>
        </div>

      </div>

      <label style="margin: 15px;">Choose add-ons:</label>

        <div ng-if="addOnArray.length > 0">
            <button ng-repeat="addon in addOnArray" class='btn btn-lg'
               ng-class="{addOnButton: !addon.checked, addOnButtonSelected: addon.checked}" type="button"
                ng-click="selectedAddOns(addon)">{{addon.name}}</button>
        </div>

     <!-- <div class="checkbox" ng-repeat="addon in addOnArray">
        <label>
          <input type="checkbox" value="" id="addOns" ng-model="addon.checked" ng-change="selectedAddOns()">
          {{addon.name}}
        </label>
      </div>-->

    </div>
  </form>
</div>

<div class="modal-footer">

  <div class="btn-group">
    <button class="btn btn-danger" data-ng-click="cancelCustomize()">Cancel</button>
    <button class="btn btn-default" data-ng-click="submit()">Submit</button>
  </div>

  <!--<button type="button" class="btn btn-danger"
          style="margin: 15px" ng-click="cancelCustomize()" > Cancel </button>

  <button type="submit" class="btn btn-info"
          style="margin: 15px" ng-click="submit()" > Submit </button>-->
</div>
