<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="modal-header">
    <div data-flash-message="5000" ></div>
    <h3 class="text-center">Items Falling Short</h3>
</div>

<div class="modal-body" data-ng-init="init()">
	<div class="row">
		<div class="col-xs-12">
			<table class="table table-bordered tabled-striped">
				<tr>
					<th>Product Name</th>
					<!-- <th>Product Dimension</th> -->
					<th>Ordered Quantity</th>
					<th>Available Quantity</th>
				</tr>
				<tr data-ng-repeat="item in shortageItems">
					<td>{{item.name}}</td>
					<td>{{item.orderQuantity}}</td>
					<td>{{item.quantity}}</td>
				</tr>
			</table>
			
			<p>**Please note that these items will be automatically removed from your order if you press OK button.</p>
		</div>
	</div>
</div>


<div class="modal-footer" data-ng-init="init()">
    <div class="btn-group">
        <button class="btn btn-danger" data-ng-click="cancel()">Cancel</button>
        <button class="btn btn-default" data-ng-click="ok()">OK</button>
    </div>
</div>