 /*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

angular.module('posApp').directive("calendar",['AppUtil', function(AppUtil) {
    return {
        restrict: "E",
        templateUrl: window.version+"scripts/directives/templates/calendar.html",
        scope: {
            selected: "="
        },
        link: function(scope) {
            var now = moment();
            /*//console.log(scope.selected);
            //console.log(now);*/
            scope.selected = now;
            /*//console.log(scope.selected);*/
            scope.month = scope.selected.clone();

            var start = scope.selected.clone();
            start.date(1);

            _removeTime(start.day(0));

            _buildMonth(scope, start, scope.month);

            scope.isPastDate = function (date) {

                var lastBusinessDate = moment(AppUtil.getUnitDetails().lastBusinessDate);

                if((lastBusinessDate > date.date) || (date.date > now) || lastBusinessDate.date() == date.date.date()){
                        return true;
                    } else {
                        return false;
                    }
            };

            scope.select = function(day) {
                var date = day.date;
                var lastBusinessDate = moment(AppUtil.getUnitDetails().lastBusinessDate);
                //console.log(lastBusinessDate);
                if((lastBusinessDate > date) || (date > now) || lastBusinessDate.date() == date.date()){
                    //console.log('disable');
                } else {
                    //console.log('enable');
                    scope.selected = day.date;
                }


            };

            scope.next = function() {
                var next = scope.month.clone();
                _removeTime(next.month(next.month()+1));
                scope.month.month(scope.month.month()+1);
                _buildMonth(scope, next, scope.month);
            };

            scope.previous = function() {
                var previous = scope.month.clone();
                _removeTime(previous.month(previous.month()-1).date(1));
                scope.month.month(scope.month.month()-1);
                _buildMonth(scope, previous, scope.month);
            };
        }
    };

    function _removeTime(date) {
        return date.day(0).hour(0).minute(0).second(0).millisecond(0);
    }

    function _buildMonth(scope, start, month) {
        scope.weeks = [];
        var done = false, date = start.clone(), monthIndex = date.month(), count = 0;
        //console.log(done,date,monthIndex,count);
        while (!done) {
            scope.weeks.push({ days: _buildWeek( date.clone(), month) });
            date.add(1, "w");
            done = count++ > 2 && monthIndex !== date.month();
            monthIndex = date.month();
        }
    }

    function _buildWeek(date, month) {
        var days = [];
        for (var i = 0; i < 7; i++) {
            days.push({
                name: date.format("dd").substring(0, 1),
                number: date.date(),
                isCurrentMonth: date.month() === month.month(),
                isToday: date.isSame(new Date(), "day"),
                date: date
            });
            date = date.clone();
            date.add(1, "d");

        }
        return days;
    }
}]);
