/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

(function (root) {
    'use strict';

    function factory(angular, Spinner) {

        angular
            .module('angularSpinner', [])

            .factory('usSpinnerService', ['$rootScope', function ($rootScope) {
                var config = {};

                config.spin = function (key) {
                    $rootScope.$broadcast('us-spinner:spin', key);
                };

                config.stop = function (key) {
                    $rootScope.$broadcast('us-spinner:stop', key);
                };

                return config;
            }])

            .directive('usSpinner', ['$window', function ($window) {
                return {
                    scope: true,
                    link: function (scope, element, attr) {
                        var SpinnerConstructor = Spinner || $window.Spinner;

                        scope.spinner = null;

                        scope.key = angular.isDefined(attr.spinnerKey) ? attr.spinnerKey : false;

                        scope.startActive = angular.isDefined(attr.spinnerStartActive) ?
                            attr.spinnerStartActive : scope.key ?
                            false : true;

                        scope.spin = function () {
                            if (scope.spinner) {
                                scope.spinner.spin(element[0]);
                            }
                        };

                        scope.stop = function () {
                            if (scope.spinner) {
                                scope.spinner.stop();
                            }
                        };

                        scope.$watch(attr.usSpinner, function (options) {
                            scope.stop();
                            scope.spinner = new SpinnerConstructor(options);
                            if (!scope.key || scope.startActive) {
                                scope.spinner.spin(element[0]);
                            }
                        }, true);

                        scope.$on('us-spinner:spin', function (event, key) {
                            if (key === scope.key) {
                                scope.spin();
                            }
                        });

                        scope.$on('us-spinner:stop', function (event, key) {
                            if (key === scope.key) {
                                scope.stop();
                            }
                        });

                        scope.$on('$destroy', function () {
                            scope.stop();
                            scope.spinner = null;
                        });
                    }
                };
            }]);
    }

    if (typeof define === 'function' && define.amd) {
        /* AMD module */
        define(['angular', 'spin'], factory);
    } else {
        /* Browser global */
        factory(root.angular);
    }
}(window));