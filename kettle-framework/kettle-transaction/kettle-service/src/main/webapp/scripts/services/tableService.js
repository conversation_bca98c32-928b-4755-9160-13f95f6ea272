/**
 * 
 */

'use strict';

angular.module('posApp').service(
		'tableService',
		[
				'$modal',
				'posAPI',
				'AppUtil',
				'$rootScope',
				'$cookieStore',
				'trackingService',
				function($modal, posAPI, AppUtil, $rootScope, $cookieStore,
						trackingService) {
					var service = {};
					service.currentTable = null;
					
					service.setCurrentTable = function(table) {
						service.currentTable = table
					};
					
					service.getCurrentTable = function(table) {
						return service.currentTable;
					};

					return service;
				} ]);