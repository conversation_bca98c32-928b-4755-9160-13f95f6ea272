/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

(function () {
    'use strict';

    angular
        .module('posApp')
        .factory('coverUtils', coverUtils);

    coverUtils.$inject = ['$cookieStore', '$rootScope','$location','Flash','AppUtil','PrintService','posAPI','$modal','AuthenticationService'];
    function coverUtils( $cookieStore ,$rootScope,$location,Flash,AppUtil,PrintService,posAPI,$modal,AuthenticationService) {
        var service = {};

        $rootScope.showViewMoreButton = false;
        service.lastThreeOrders = lastThreeOrders;
        service.openOrderSearch = openOrderSearch;
        service.logOut = logOut;
        service.openOrderSearchScreen = openOrderSearchScreen;
        service.goToOrderSummary = goToOrderSummary;
        service.goToDeliveryOrderSummary = goToDeliveryOrderSummary;
        service.goToCustomerOrderSummary = goToCustomerOrderSummary;
        service.testPrinter = testPrinter;
        service.testPrint = testPrint;
        service.openInventoryScreen = openInventoryScreen;
        service.initDiscount = initDiscount;
        service.goToEmployeeMealSummary = goToEmployeeMealSummary;

        return service;
        
        

        function lastThreeOrders() {
            var lastThreeOrders = $cookieStore.get('lastThreeOrders');
            ////console.log("Inside lat three orders");
            ////console.log(lastThreeOrders);
            var orderArray = [];
            if (lastThreeOrders == undefined) {
                ////console.log('last three orders don\'t exist in the system for this unit');
            } else {
                if(lastThreeOrders.unitId == AppUtil.getUnitDetails().id){
                	orderArray = lastThreeOrders.orders;
                }
            }
            return orderArray;
        }

        function openOrderSearch(generateOrderId) {
            //var reqObj = AppUtil.GetRequest(generateOrderId);
            ////console.log(reqObj);
            posAPI.allUrl('/',AppUtil.restUrls.order.generatedOrder).post(generateOrderId)
                .then(function (response) {
                    AppUtil.OrderObj = {};
                    AppUtil.OrderObj = response.plain();
                    $location.url('/orderSearch');
                }, function (err) {
                    AppUtil.myAlert(err.data.errorMessage);
                });
        }

        //logout

        function logOut() {
            ////console.log('logout');
            AuthenticationService.Logout(function (response) {
                AuthenticationService.ClearCredentials();
                window.location = window.location.href + '?eraseCache=true';
                window.location.reload();
                $location.url('/login');
            });
        }

        //order search
        function openOrderSearchScreen() {
        	$rootScope.orderType="order";
            $location.url('/orderSearch');
        }

        //order summary
        function goToOrderSummary() {
            bootbox.confirm("Are you Sure you want to generate order Summary report?", function(result){
                if (result == true) {
                    getOrderSummaryObj('/cover', false);
                }
            });
        }
        
      //order summary
        function goToDeliveryOrderSummary() {
            bootbox.confirm("Are you Sure you want to generate order Summary report?", function(result){
                if (result == true) {
                    getOrderSummaryObj('/assembly',  true);
                }
            });
        }

		function goToEmployeeMealSummary() {
			var unitId = AppUtil.getUnitDetails().id
			var url = AppUtil.restUrls.userManagement.empMealUsers;
			posAPI.allUrl('/',url).post(unitId).then(function(response) {
				var returnObj = response.plain();
				$rootScope.showFullScreenLoader = false;
				selectEmployee(returnObj);
			}, function(err) {
				AppUtil.myAlert(err.data.errorMessage);
				$rootScope.showFullScreenLoader = false;
			})
		}


		function selectEmployee(list) {
			var employeeList = [];
			var employeeMap = [];
			if (list != null && list.length > 0) {
				var i;
				for (i = 0; i < list.length; i++) {
					employeeList.push({
						text : list[i].name,
						value : list[i].id
					});
					employeeMap[list[i].id]=list[i];
				}
			}
			bootbox.prompt({
				title : "Please select Employee",
				inputType : 'select',
				inputOptions : employeeList,
				callback : function(result) {
					if (result != null) {
						getEmployeeMealSummaryObj('/cover', employeeMap[result]);
					}
				}
			});
		}
       
        function getEmployeeMealSummaryObj(backUrl, employee) {
            var returnObj = null;
            $rootScope.showFullScreenLoader = true;
            var url = AppUtil.restUrls.order.employeeMealOrdersForUser;
            posAPI.allUrl('/',url).post(employee.id).then(
                function (response) {
                    returnObj = response.plain();
                    AppUtil.orderSummaryObj = returnObj;
                    AppUtil.orderSummaryObj.header = "Employee Meal Summary Report";
                    AppUtil.orderSummaryObj.backUrl = backUrl;
                    AppUtil.orderSummaryObj.employee = employee;
                    getEmployeeMealAllowanceData(employee);
                    $location.url('orderSummary');
                    $rootScope.showFullScreenLoader = false;
                }, function (err) {
                    AppUtil.myAlert(err.data.errorMessage);
                    $rootScope.showFullScreenLoader = false;
                })
        }
        

		function getEmployeeMealAllowanceData(emp) {
			posAPI.allUrl('/',AppUtil.restUrls.order.empAllowancelimit).post(emp.id).then(function(response) {
				if (AppUtil.orderSummaryObj.employee != undefined) {
					AppUtil.orderSummaryObj.employee.mealAllowanceLimit = response;
				}
			}, function(err) {
				AppUtil.myAlert(err.data.errorMessage);
			});
		}

        function getOrderSummaryObj(backUrl, onlyDelivery) {
            var requestObj = $rootScope.globals.currentUser;
            var returnObj = null;
            $rootScope.showFullScreenLoader = true;
            $rootScope.showViewMoreButton = false;
            var url = onlyDelivery ? AppUtil.restUrls.order.deliveryOrdersForDay : AppUtil.restUrls.order.ordersForDayPageable;
            posAPI.allUrl('/',url).post(requestObj).then(
                function (response) {
                    returnObj = response.plain();
                    ////console.log(returnObj);
                    if(returnObj.orderDetailTrims.length < 50){
                        $rootScope.showViewMoreButton = true;
                    }
                    AppUtil.orderSummaryObj = returnObj;
                    AppUtil.orderSummaryObj.header = onlyDelivery ? "Delivery Order Summary Report" : "Order Summary Report";
                    AppUtil.orderSummaryObj.backUrl = backUrl;
                    $location.url('orderSummary');
                    $rootScope.showFullScreenLoader = false;
                }, function (err) {
                    AppUtil.myAlert(err.data.errorMessage);
                    $rootScope.showFullScreenLoader = false;
                })
        }

        //order summary
        function goToCustomerOrderSummary() {
            $location.url('/customerOrders')
        }
        //test printer

        function testPrinter() {
        	var standardFont = '\x1D' + '\x21' + '\x00';
			var line = "----------------------------------------------";
			var cut = '\x0A' + '\x0A' + '\x0A' + '\x0A' + '\x0A' + '\x0A' + '\x1B' + '\x69';
			var testPrint =  standardFont + line + '\x0A' + "Test Print" + '\x0A' + line + cut; 
        	if(AppUtil.isAndroid) {
        		 testPrinterAndroid(testPrint);
        	}else{
        		testPrinterWindows(testPrint);
        	}
        }
        
        function testPrint(htmlText) {
			var cut = '\x0A' + '\x0A' + '\x0A' + '\x0A' + '\x0A' + '\x0A' + '\x1B' + '\x69';
			htmlText = htmlText + cut;
        	if(AppUtil.isAndroid) {
        		testPrinterAndroid(htmlText);
        	}else{
        		testPrinterWindows(htmlText);
        	}
        }
        

		function testPrinterWindows(testPrint) {
			PrintService.printOnBilling(testPrint, "RAW");
			if (AppUtil.hasSeparateKotPrinting()) {
				PrintService.printOnKot(testPrint, "RAW");
			}
		}
        
        function testPrinterAndroid(testPrint){
        	Android.printTest(testPrint);
        }

        function openInventoryScreen () {
        	$location.url("/inventory");	
        }

        function initDiscount(){
            AppUtil.discountObj = {
                value: 0,
                percentage: 0,
                discountReason: null,
                discountCode: null
            }
        }

    }

})();
