/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

/*delete from LOYALTY_SCORE where CUSTOMER_ID IN (select CUSTOMER_ID from CUSTOMER_INFO where CONTACT_NUMBER = '8860563383');
delete from CUSTOMER_INFO where CONTACT_NUMBER = '8860563383';*/
angular.module('posApp')
    .service('subscriptionService', ['$modal','posAPI','AppUtil', function ($modal,posAPI,AppUtil) {
    	
    	var service = {};

		service.weekDays = weekDays();
		service.monthDays = monthDays;
		service.frequencyTimes = frequencyTimes();
		service.monthDays = monthDays;
		service.processSubscriptionDays = processSubscriptionDays;
		service.processSubscriptionTimes = processSubscriptionTimes;
		service.convertTimeFromIndex = convertTimeFromIndex;
		service.convertWeekDayFromIndex = convertWeekDayFromIndex;
		service.subscriptionMinDate = new Date(new Date().setDate(new Date().getDate() - 1)).toString();
		service.getCurrentDateFormatted = getCurrentDateFormatted;
		service.orderList = null;
		service.subscriptionList = null;
		service.customerContact = null;
		service.dateGTE = dateGTE;
		service.dateGT = dateGT;
		service.getNextDateFromDate = getNextDateFromDate;
		service.getDateFromTimeStamp = getDateFromTimeStamp;
		service.getSubscriptionTimesUsingIndex = getSubscriptionTimesUsingIndex;
		service.getSubscriptionDaysUsingIndex = getSubscriptionDaysUsingIndex;
		service.getTimeValueFromTimestamp = getTimeValueFromTimestamp;
		service.getFrequencyTimeObject = getFrequencyTimeObject;
		service.getTimestampFromTime = getTimestampFromTime;
		service.outletList = null;
		service.unitOrderList = null;
		service.selectedOutlet = null;
		service.joinArray = joinArray;
		service.getPreviousDate = getPreviousDate;
		service.getCurrentDate = new Date().toString();
		service.getPreviousDateTimestamp = getPreviousDateTimestamp;
        return service;
        
        function weekDays(){
       	 var days = [
             {id:1,value:'Sunday'},
             {id:2,value:'Monday'},
             {id:3,value:'Tuesday'},
             {id:4,value:'Wednesday'},
             {id:5,value:'Thursday'},
             {id:6,value:'Friday'},
  			 {id:7,value:'Saturday'}];
       	 return days;
        }
        
       function monthDays(){
   		var days = [];
   		for(var i=1;i<32;i++){
   			days.push({id:i,value:i});
   		}
   		return days;
   	   }
       
       function frequencyTimes(){
	   		var time = [];
	   		//for(var i=0;i<96;i++){  // patch for testing
	   		for(var i=32;i<80;i++){
	   			var hours = parseInt((i/4)-(parseInt(i/52)*12));
	   			if(i<4){
	   				hours = hours + 12;
	   			}
	   			if(hours<10){
	   				hours = "0"+hours;
	   			}
	   			var minutes = parseInt((i%4)*15);
	   			if(minutes<10){
	   				minutes = "0"+minutes;
	   			}
	   			if(i<48){
	   				minutes = minutes+" am";
	   			}else{
	   				minutes = minutes+" pm";
	   			}
	   			time.push({id:i,value:hours+":"+minutes});
	   		}
	   		return time;
	   }
   	
	   	function processSubscriptionDays(subscriptionDays){
	   		////console.log(subscriptionDays);
	   		var itemArr = [];
	   		subscriptionDays.forEach(function(v){
	   			itemArr.push(v.id);
	   		});
	   		////console.log(itemArr);
	   		return itemArr;
	   	}
	   	
	   	function processSubscriptionTimes(subscriptionTimes){
	   		////console.log(subscriptionTimes);
	   		var itemArr = [];
	   		subscriptionTimes.forEach(function(v){
	   			itemArr.push(v.id);
	   		});
	   		////console.log(itemArr);
	   		return itemArr;
	   	}
       
       function convertTimeFromIndex(timeArr){
       	var frequency = frequencyTimes();
       	var outPutTimeArr = [];
       	timeArr.forEach(function(time){
       		frequency.forEach(function(v){
       			if(v.id===time){
       				outPutTimeArr.push(v.value);
       			}
       		});
       	});
       	return outPutTimeArr.join(", ");
       }
       
       function convertWeekDayFromIndex(weekArr){
       	var outPutArr = [];
       	var week = weekDays();
       	weekArr.forEach(function(weekDay){
       		week.forEach(function(v){
       			if(v.id === weekDay){
       				outPutArr.push(v.value);
       			}
       		});    		
       	});
       	return outPutArr.join(", ");
       }
       
       function getSubscriptionTimesUsingIndex(timeArr){
    	   var frequency = frequencyTimes();
          	var outPutTimeArr = [];
          	timeArr.forEach(function(time){
          		frequency.forEach(function(v){
          			if(v.id===time){
          				outPutTimeArr.push(v);
          			}
          		})
          	});
          	return outPutTimeArr;
       }
       
       function getSubscriptionDaysUsingIndex(indexArr,type){
    	   var outPutArr = [];
           var days;
     	   if(type=="WEEKLY"){
               days = weekDays();
     	   }else if(type=="MONTHLY"){
     		  days = monthDays();
     	   }
          	indexArr.forEach(function(day){
          		days.forEach(function(v){
          			if(v.id === day){
          				outPutArr.push(v);
          			}
          		});    		
          	});
          	return outPutArr;
    		   
       }
       
       function getCurrentDateFormatted(format){
    	   var today = new Date();
    	    var dd = today.getDate();
    	    var mm = today.getMonth()+1; //January is 0!

    	    var yyyy = today.getFullYear();
    	    if(dd<10){
    	        dd='0'+dd
    	    } 
    	    if(mm<10){
    	        mm='0'+mm
    	    }
    	   if(format=="yyyy-mm-dd"){
    		   return yyyy+"-"+mm+"-"+dd;
    	   }
       }
       
       function getNextDateFromDate(date,format){
    	   var inputDate = new Date(date);
    	    var dd = inputDate.getDate()+1;
    	    var mm = inputDate.getMonth()+1; //January is 0!

    	    var yyyy = inputDate.getFullYear();
    	    if(dd<10){
    	        dd='0'+dd
    	    } 
    	    if(mm<10){
    	        mm='0'+mm
    	    }
    	   if(format=="yyyy-mm-dd"){
    		   return yyyy+"-"+mm+"-"+dd;
    	   }
       }
       
       function dateGTE(date){
    	   return date >= new Date().getTime();
       }
       
       function dateGT(date){
    	   return date > new Date().getTime();
       }
       
       function getDateFromTimeStamp(timeStamp){
    	   var inputDate = new Date(timeStamp);
	   	   var dd = inputDate.getDate();
	   	   var mm = inputDate.getMonth()+1; //January is 0!
	   	   var yyyy = inputDate.getFullYear();
		   if(dd<10){
		       dd='0'+dd
		   } 
		   if(mm<10){
		       mm='0'+mm
		   }
		   return yyyy+"-"+mm+"-"+dd;
       }
       
       function getTimeValueFromTimestamp(timestamp){
    	   var date = new Date(timestamp);
    	   var hours = date.getHours();
    	   var minutes = date.getMinutes();
    	   var seconds = date.getSeconds();
    	   var xx = "pm";
    	   if(minutes<10){
    		   minutes="0"+minutes;
    	   }
    	   if(seconds<10){
    		   seconds="0"+seconds;
    	   }
    	   if(parseInt(hours)<12){
    		   xx = "am";
    	   }
    	   if(hours>12){
    		   hours=hours-12;
    	   }
    	   if(hours<10){
    		   hours="0"+hours;
    	   }
    	   ////console.log(hours+":"+minutes+" "+xx);
    	   return hours+":"+minutes+" "+xx;
       }
       
       function getFrequencyTimeObject(time){
    	   var frequency = frequencyTimes();
    	   var ret = null;
    	   frequency.forEach(function(v){
    			if(v.value.trim()==time.trim()){
    				ret = v;
    				return true;
    			}
    	   });
    	   return ret;
       }
       
       function getTimestampFromTime(time){
    	   var date = new Date();
    	   var hours = time.trim().substr(0,2);
    	   var minutes = time.trim().substr(time.indexOf(":")+1,2);
    	   if(time.indexOf("pm")!=-1){
    		   hours = parseInt(hours)+12;
    	   }
    	   date.setHours(hours);
    	   date.setMinutes(minutes);
    	   date.setSeconds("00");
    	   return date.getTime();
       }
       
       function joinArray(arr){
    	   return arr.join(", ");
       }
       
       function getPreviousDateTimestamp(date){
    	   var date = new Date(date);
    	   date.setDate(date.getDate()-1);
    	   return date.getTime();
       }
       
       function getPreviousDate(date){
    	   var date = new Date(date);
    	   date.setDate(date.getDate()-1);
    	   return date.toString();
       }
       
    }]);
