/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

angular
    .module('posApp')
    .service(
        'swiggyOrderService',
        ['$modal', 'posAPI', 'AppUtil', '$rootScope', '$cookieStore', 'trackingService',
            function ($modal, posAPI, AppUtil, $rootScope, $cookieStore, trackingService) {
                var service = {};
                service.orderObj = {
                    "callback_url": "http://rms.swiggy.in/external/order/confirm",
                    "cart_cgst": 0,
                    "cart_cgst_percent": 0,
                    "cart_gst": 0,
                    "cart_gst_percent": 0,
                    "cart_igst": 0,
                    "cart_igst_percent": 0,
                    "cart_sgst": 0,
                    "cart_sgst_percent": 0,
                    "customer_address": "Bangalore",
                    "customer_area": "Bangaloretus",
                    "customer_city": "Bangalore",
                    "customer_name": "SWIGGY",
                    "customer_phone": "9599598307",
                    "delivery_type": "PICKUP",
                    "instructions": null,
                    "items": [],
                    "order_date_time": "2017-11-17 14:35:57",
                    "order_edit": false,
                    "order_edit_reason": null,
                    "order_id": "111828121118",
                    "order_packing_charges": 31.5,
                    "outlet_id": "10000",
                    "payment_type": "Online",
                    "restaurant_discount": 0,
                    "restaurant_gross_bill": 0,
                    "restaurant_service_charges": 0
                };
                service.packaging = 0;
                service.discount_percent = 0;
                service.subTotal = 0;
                service.order = {};
                service.addOrderItems = addOrderItems;
                service.createSwiggyOrder = createSwiggyOrder;

                function createSwiggyOrder(order, orderItemArray) {
                    service.order = angular.copy(service.orderObj);
                    service.order.instructions = order.orderRemark;
                    service.order.outlet_id = order.unitId;
                    service.order.order_id = new Date().getTime();
                    service.packaging = 0;
                    service.discount_percent = 0;
                    service.order.restaurant_discount = order.transactionDetail.savings != null ? order.transactionDetail.savings : 0;
                    var subtotal = getSubtotal(orderItemArray);
                    service.subTotal = subtotal;
                    if (service.order.restaurant_discount > 0) {
                        service.discount_percent = (service.order.restaurant_discount / subtotal) * 100;
                    }
                    addOrderItems(orderItemArray);
                    calculateCartTransaction(order);
                    //console.log("Swiggy order:::::::::::", JSON.stringify(service.order));
                    downloadSwiggyOrderJson();
                }

                function getSubtotal(orderItemArray) {
                    var s = 0;
                    orderItemArray.map(function (item) {
                        if (item.productDetails.id != 1043 && item.productDetails.id != 1044)
                            s = s + item.orderDetails.totalAmount;
                    });
                    return s;
                }

                function addOrderItems(orderItemArray) {
                    var orderItemObj = {
                        "addons": [],
                        "cgst": 0,
                        "cgst_percent": 0,
                        "id": null,
                        "igst": 0,
                        "igst_percent": 0,
                        "name": null,
                        "packing_charges": 0,
                        "price": 0,
                        "quantity": 1,
                        "reward_type": null,
                        "sgst": 0,
                        "sgst_percent": 0,
                        "subtotal": 0,
                        "variants": []
                    };
                    var dummyCustomization = {
                        "cgst": 0,
                        "cgst_percent": 0,
                        "id": null,
                        "igst": 0,
                        "igst_percent": 0,
                        "name": "",
                        "price": 0,
                        "sgst": 0,
                        "sgst_percent": 0
                    };
                    service.order.items = [];
                    orderItemArray.map(function (item) {
                        item.productDetails.prices.sort(function (a,b) {
                            return a.price - b.price;
                        });
                        if (item.productDetails.id != 1204 && item.productDetails.id != 1043 && item.productDetails.id != 1044) {
                            var orderItem = angular.copy(orderItemObj);
                            //orderItem.id = item.productDetails.id;
                            orderItem.id = item.productDetails.id+"_"+item.productDetails.type+"_"+item.productDetails.subType;
                            orderItem.name = item.productDetails.name;
                            if([10,11,12,50].indexOf(item.productDetails.id) >= 0) {
                                //orderItem.id = 10;
                                orderItem.id = 10+"_"+item.productDetails.type+"_"+item.productDetails.subType;
                            }
                            orderItem.quantity = item.orderDetails.quantity;
                            orderItem.price = item.productDetails.prices[0].price;
                            orderItem.subtotal = item.orderDetails.totalAmount;
                            orderItem.variants = [];
                            orderItem.addons = [];
                            orderItem.packaging = ((10/100) * orderItem.price) * orderItem.quantity;
                            item.productDetails.prices.map(function (price) {
                                if (price.dimension == item.orderDetails.dimension) {
                                    var dim = getDimensionVariant(orderItem, price);
                                    orderItem.variants.push(dim);
                                    if([10,11,12,50].indexOf(item.productDetails.id) >= 0) {
                                        orderItem.name = "Desi chai";
                                        //orderItem.name = item.productDetails.name + " " + dim.name;
                                        //orderItem.id = 10 + "_" + dim.name;
                                        orderItem.id = 10+"_"+dim.name.replace(/([a-z])/g, '')+"_"+item.productDetails.type+"_"+item.productDetails.subType;
                                    }
                                }
                            });
                            if([11,12,50].indexOf(item.productDetails.id) >= 0) {
                                var milkVar = dummyCustomization;
                                milkVar.name = item.productDetails.name;
                                orderItem.variants.push(milkVar);
                            }
                            if (item.orderDetails.composition != null) {
                                if (item.orderDetails.composition.products != null) {
                                    item.orderDetails.composition.products.map(function (product) {
                                        var pr = angular.copy(dummyCustomization);
                                        pr.name = product.product.name;
                                        orderItem.variants.push(pr);
                                    });
                                }
                                if (item.orderDetails.composition.variants != null) {
                                    item.orderDetails.composition.variants.map(function (variant) {
                                        var va = angular.copy(dummyCustomization);
                                        va.name = variant.alias;
                                        orderItem.variants.push(va);
                                    });
                                }
                                if (item.orderDetails.composition.menuProducts != null) {
                                    item.orderDetails.composition.menuProducts.map(function (menuProduct) {
                                        var variant = angular.copy(dummyCustomization);
                                        variant.id = menuProduct.product.productId;
                                        variant.name = menuProduct.product.name;
                                        orderItem.variants.push(variant);
                                    });
                                }
                                if (item.orderDetails.composition.addons != null) {
                                    item.orderDetails.composition.addons.map(function (addon) {
                                        var variant = angular.copy(dummyCustomization);
                                        variant.id = addon.product.productId;
                                        variant.name = addon.product.name;
                                        orderItem.addons.push(variant);
                                    });
                                }
                            }
                            setItemTaxData(orderItem, item);
                            calculateItemTaxes(orderItem);
                            service.order.items.push(orderItem);
                        } else {
                            if (item.productDetails.id == 1204 || item.productDetails.id == 1043) {
                                service.packaging = item.orderDetails.price;
                            }
                        }
                    });
                }

                function getDimensionVariant(orderItem, price) {
                    var dimension = {
                        "cgst": 0,
                        "cgst_percent": 0,
                        "id": null,
                        "igst": 0,
                        "igst_percent": 0,
                        "name": price.dimension,
                        "price": 0,
                        "sgst": 0,
                        "sgst_percent": 0
                    };
                    var diff = price.price - orderItem.price;
                    if (diff > 0) {
                        dimension.price = diff;
                    }
                    return dimension;
                }

                function setItemTaxData(orderItem, item) {
                    setTaxPercentage(orderItem, item.orderDetails.taxes);
                    orderItem.variants.map(function (variant) {
                        if (variant.price > 0) {
                            setTaxPercentage(variant, item.orderDetails.taxes);
                        }
                    });
                }

                function setTaxPercentage(obj, taxes) {
                    taxes.map(function (tax) {
                        if (tax.code == "CGST") {
                            obj['cgst_percent'] = tax.percentage;
                        } else if (tax.code == "SGST/UTGST") {
                            obj['sgst_percent'] = tax.percentage;
                        } else if (tax.code == "IGST") {
                            obj['igst_percent'] = 0; //tax.percentage;
                        }
                    });
                }

                function calculateItemTaxes(orderItem) {
                    var itemDiscount = 0;
                    if (service.discount_percent > 0) {
                        itemDiscount = (service.discount_percent / 100) * orderItem.price;
                    }
                    if (orderItem.cgst_percent > 0) {
                        orderItem.cgst = (orderItem.cgst_percent / 100) * (orderItem.price - itemDiscount);
                    }
                    if (orderItem.sgst_percent > 0) {
                        orderItem.sgst = (orderItem.sgst_percent / 100) * (orderItem.price - itemDiscount);
                    }
                    if (orderItem.igst_percent > 0) {
                        orderItem.igst = (orderItem.igst_percent / 100) * (orderItem.price - itemDiscount);
                    }
                }

                function calculateCartTransaction(order) {
                    var grossBill = 0;
                    service.order.items.map(function (item) {
                        grossBill = grossBill + item.subtotal;
                        /*item.variants.map(function (variant) {
                            if(variant.price>0){
                                grossBill = grossBill + variant.price;
                            }
                        });*/
                    });

                    service.order.cart_cgst_percent = service.order.items[0].cgst_percent;
                    service.order.cart_sgst_percent = service.order.items[0].sgst_percent;
                    service.order.cart_igst_percent = service.order.items[0].igst_percent;
                    if (service.order.cart_cgst_percent > 0) {
                        service.order.cart_cgst = (service.order.cart_cgst_percent / 100) * (grossBill - service.order.restaurant_discount);
                    }
                    if (service.order.cart_sgst_percent > 0) {
                        service.order.cart_sgst = (service.order.cart_sgst_percent / 100) * (grossBill - service.order.restaurant_discount);
                    }
                    if (service.order.cart_igst_percent > 0) {
                        service.order.cart_igst = (service.order.cart_igst_percent / 100) * (grossBill - service.order.restaurant_discount);
                    }
                    service.order.cart_gst = service.order.cart_cgst + service.order.cart_sgst + service.order.cart_igst;
                    service.order.order_packing_charges = service.packaging;
                    //service.order.restaurant_gross_bill = Math.round(grossBill + service.order.cart_gst + service.packaging);
                    service.order.restaurant_gross_bill = order.transactionDetail.paidAmount + order.transactionDetail.savings;
                }

                function downloadSwiggyOrderJson() {
                    var link = document.createElement("a");
                    link.id = "swgdwnldlnk";

                    // this part will append the anchor tag and remove it after automatic
                    // click
                    document.body.appendChild(link);

                    var json = JSON.stringify(service.order);
                    var blob = new Blob([json], {
                        type: 'application/json'
                    });
                    var csvUrl = window.webkitURL.createObjectURL(blob);
                    var filename = 'swiggyOrder.json';
                    $("#swgdwnldlnk").attr({
                        'download': filename,
                        'href': csvUrl
                    });

                    $('#swgdwnldlnk')[0].click();
                    document.body.removeChild(link);
                }


                ///////////////////////////////swiggy catalog generation //////////////////////////
                var catalogItemObj = {
                    "restaurantId": null,
                    "mainCategoryId": null,
                    "mainCategoryName": "Hot Beverages",
                    "mainCategoryOrder": 99,
                    "subCategoryId": null,
                    "subCategoryName": "Indian Chai",
                    "subCategoryOrder": 1,
                    "itemId": null,
                    "Name": "Desi Chai (Choti Kettle)",
                    "description": "",
                    "Parent": "I1",
                    "Variant Group Id": "",
                    "Variant Group Name": "",
                    "Variant Group Order": "",
                    "Variant Id": "",
                    "Variant Default Value": "",
                    "Order": 0,
                    "in_stock": 1,
                    "price": null,
                    "veg_egg_non": "veg",
                    "packingCharges": "",
                    "is_spicy": 0,
                    "serves how many": 1,
                    "Service_Charges (%)": 0,
                    "Item_SGST": 0.025,
                    "Item_CGST": 0.025,
                    "Item_IGST": 0,
                    "Item_inclusive": 0,
                    "Slab Count": 1,
                    "Disable": 0,
                    "Preparation Style": "",
                    "Monday Open 1": "",
                    "Monday Close 1": "",
                    "Monday Open 2": "",
                    "Monday Close 2": "",
                    "Monday Open 3": "",
                    "Monday Close 3": "",
                    "Tuesday Open 1": "",
                    "Tuesday Close 1": "",
                    "Tuesday Open 2": "",
                    "Tuesday Close 2": "",
                    "Tuesday Open 3": "",
                    "Tuesday Close 3": "",
                    "Wednesday Open 1": "",
                    "Wednesday Close 1": "",
                    "Wednesday Open 2": "",
                    "Wednesday Close 2": "",
                    "Wednesday Open 3": "",
                    "Wednesday Close 3": "",
                    "Thursday Open 1": "",
                    "Thursday Close 1": "",
                    "Thursday Open 2": "",
                    "Thursday Close 2": "",
                    "Thursday Open 3": "",
                    "Thursday Close 3": "",
                    "Friday Open 1": "",
                    "Friday Close 1": "",
                    "Friday Open 2": "",
                    "Friday Close 2": "",
                    "Friday Open 3": "",
                    "Friday Close 3": "",
                    "Saturday Open 1": "",
                    "Saturday Close 1": "",
                    "Saturday Open 2": "",
                    "Saturday Close 2": "",
                    "Saturday Open 3": "",
                    "Saturday Close 3": "",
                    "Sunday Open 1": "",
                    "Sunday Close 1": "",
                    "Sunday Open 2": "",
                    "Sunday Close 2": "",
                    "Sunday Open 3": "",
                    "Sunday Close 3": "",
                    "delete": "",
                    "external_id": "",
                    "restuarantCategoryName": "",
                    "restuarantSubCategoryName": "",
                    "restuarantType": "",
                    "Eligible For Long Distance": 1,
                    "Item type": "REGULAR_ITEM"
                };

                var addonItemObj = {
                    "Rest Id": 20784,
                    "Items Id": "11372261 , 11372262",
                    "Addon Id": null,
                    "Addon Name": "Regular Sugar",
                    "Addon Order": 0,
                    "Addon Price": 0,
                    "Addon IsVeg": 1,
                    "Addon Instock": 1,
                    "AddonGroup Name": "Preparation Type",
                    "Delete": "",
                    "external_addon_id": "",
                    "Addon_SGST": 0,
                    "Addon_CGST": 0,
                    "Addon_IGST": 0,
                    "Addon_inclusive": ""
                };

                service.prepareCatalogData = prepareCatalogData;
                service.itemList = [];
                service.addonIds = [];
                service.addonList = [];

                function prepareCatalogData() {
                    var unit = angular.copy(AppUtil.unitDetails);
                    var reqObj = [];
                    unit.products.map(function (product) {
                        if (product.classification == "MENU" && product.billType != "ZERO_TAX" && product.type != 12) {
                            var obj = {};
                            obj.productId = product.id;
                            obj.recipes = {};
                            product.prices.map(function (price) {
                                obj.recipes[price.dimension] = null;
                            });
                            reqObj.push(obj);
                        }
                    });
                    posAPI.allUrl('/', "https://relax.chaayos.com/neo-service/rest/v1/nc/p/r")
                        .post(reqObj).then(function (response) {
                            if(response != null){
                                var res = response.plain();
                                if (res != null && res.length > 0) {
                                    res.map(function (item) {
                                        unit.products.map(function (product) {
                                            if(product.id == item.productId){
                                                product.webRecipe = item;
                                            }
                                        });
                                    });
                                    createCatalog(unit);
                                }
                            }
                    }, function (err) {
                        AppUtil.myAlert(err.data.errorMessage);
                    });
                }

                function createCatalog(unit) {
                    service.itemList = [];
                    service.addonIds = [];
                    service.addonList = [];
                    //var unit = AppUtil.unitDetails;
                    var index = 1;
                    //console.log(JSON.stringify(unit));
                    var catList = [], subCatList = [];
                    unit.products.map(function (product) {
                        if (product.classification == "MENU" && product.billType != "ZERO_TAX" && product.type != 12) {
                            if([10, 1205].indexOf(product.id) >= 0) {
                                product.prices.map(function (priceObj) {
                                    var prod = Object.assign({}, product);
                                    prod.id = product.id+"_"+priceObj.dimension.replace(/([a-z])/g, '');
                                    prod.name = product.name + priceObj.dimension.replace(/([A-Z])/g, ' $1') + " (" + getDimensionDescription(priceObj.dimension) + ")";
                                    prod.prices = [];
                                    prod.prices.push(priceObj);
                                    index = addProduct(unit, prod, catList, subCatList, index);
                                })
                            } else {
                                if([11,12,50].indexOf(product.id) < 0) {
                                    index = addProduct(unit, product, catList, subCatList, index);
                                }
                            }
                        }
                    });
                    AppUtil.JSONToCSVConvertor(service.itemList, "swiggy_catalog_" + AppUtil.getSelectedUnitId(), true);

                    service.addonList.map(function (addon) {
                        var ids = addon["Items Id"].join(", ");
                        addon["Items Id"] = ids;
                    });

                    AppUtil.JSONToCSVConvertor(service.addonList, "swiggy_catalog_" + AppUtil.getSelectedUnitId() + "_addons", true);
                }

                function addProduct(unit, product, catList, subCatList, index) {
                    var item = angular.copy(catalogItemObj);
                    var cat = null, subCat = null;
                    AppUtil.transactionMetadata.categories.map(function (category) {
                        if (category.detail.id == product.type) {
                            cat = category.detail.name;
                            category.content.map(function (subcategory) {
                                if (subcategory.id == product.subType) {
                                    subCat = subcategory.name;
                                }
                            });
                        }
                    });
                    if (catList.indexOf(cat) < 0) {
                        catList.push(cat);
                    }
                    if (subCatList.indexOf(subCat) < 0) {
                        subCatList.push(subCat);
                    }
                    item.restaurantId = AppUtil.getSelectedUnitId();
                    item.mainCategoryId = product.type;
                    item.mainCategoryName = cat;
                    item.mainCategoryOrder = catList.length;
                    item.subCategoryId = product.subType;
                    item.subCategoryName = subCat;
                    item.subCategoryOrder = subCatList.length;
                    item.external_id = product.id;
                    item.Name = product.name;
                    item.description = product.description;
                    item.Parent = "I" + index;
                    item.price = getItemPrice(product);
                    item.veg_egg_non = getVegNonVeg(product.attribute);
                    item.Item_SGST = getTaxPercent("sgst", product.taxCode, unit.taxes);
                    item.Item_CGST = getTaxPercent("cgst", product.taxCode, unit.taxes);
                    item.Item_IGST = 0; //getTaxPercent("igst", product.taxCode, unit.taxes);
                    if(unit.packagingType == "PERCENTAGE") {
                        item.packingCharges = ((unit.packagingValue/100)*getItemPrice(product))
                    } else if(unit.packagingType == "FIXED") {
                        item.packingCharges = unit.packagingValue;
                    }
                    service.itemList.push(item);
                    addVariantItems(product, item, index);
                    index++;
                    return index;
                }

                function addVariantItems(product, item, index) {
                    addDimension(product, item, index);
                    if (product.prices[0].recipe != null) {
                        addIngredientVariants(product, item, index);
                        addIngredientProducts(product, item, index);
                        addIngredientComposites(product, item, index);
                        addAddons(product, item, index);
                    }
                }

                function addDimension(product, item, index) {
                    var unit = AppUtil.unitDetails;
                    if (product.prices.length > 1) {
                        product.prices.map(function (price, ind) {
                            if (price.recipe != null) {
                                var variant = getVariantObj(index);
                                variant.Name = price.dimension.replace(/([A-Z])/g, ' $1');
                                variant.Name += " (" + getDimensionDescription(price.dimension) + ")";
                                variant["Variant Group Id"] = product.id + "_size";
                                variant["Variant Group Name"] = "Size";
                                variant["Variant Group Order"] = "1";
                                variant["external_id"] = product.id + "_" + price.dimension.substr(0, 1);
                                variant["Variant Default Value"] = (ind == 0) ? 1 : 0;
                                variant.price = parseFloat(price.price - item.price).toFixed(2);
                                variant.Item_SGST = getTaxPercent("sgst", product.taxCode, unit.taxes);
                                variant.Item_CGST = getTaxPercent("cgst", product.taxCode, unit.taxes);
                                variant.Item_IGST = getTaxPercent("igst", product.taxCode, unit.taxes);
                                service.itemList.push(variant);
                            }
                        });
                    }
                }

                function getDimensionDescription(dimension) {
                    var desc = "";
                    switch (dimension) {
                        case "MiniKetli":
                            desc = "Serves 1-2, 250ml";
                            break;
                        case "ChotiKetli":
                            desc = "Serves 4, 400ml";
                            break;
                        case "BadiKetli":
                            desc = "Serves 10, 1000ml";
                            break;
                    }
                    return desc;
                }

                function addIngredientVariants(product, item, index) {
                    var unit = AppUtil.unitDetails;
                    var recipe = product.webRecipe.recipes[product.prices[0].dimension];
                    var tInd = 0;
                    recipe.ingredient.variants.map(function (varItem, ind) {
                        varItem.details.map(function (varDetail) {
                            var displayName = varItem.product.displayName || "Option";
                            var variant = getVariantObj(index);
                            variant.Name = varDetail.alias;
                            variant["Variant Group Id"] = product.id + "_" + varItem.product.productId;
                            variant["Variant Group Name"] = displayName;
                            variant["Variant Group Order"] = index + ind;
                            variant["external_id"] = product.id + "_" + varDetail.productId + varDetail.alias.substr(0, 1)
                                + varDetail.alias.substr(varDetail.alias.length - 1) + displayName.length;
                            variant["Variant Default Value"] = varDetail.defaultSetting ? 1 : 0;
                            service.itemList.push(variant);
                        });
                        tInd = index + ind;
                    });
                    if(product.id.toString().match(/^(10_)/)) {
                        ["Regular Milk", "Full Doodh", "Doodh Kum", "Paani Kum"].map(function (varnt, ind) {
                            var displayName = "Milk Option";
                            var variant = getVariantObj(index);
                            variant.Name = varnt;
                            variant["Variant Group Id"] = product.id + "_" + "MLK_OPT";
                            variant["Variant Group Name"] = displayName;
                            variant["Variant Group Order"] = tInd + 1;
                            variant["external_id"] = product.id + "_" + varnt.substr(0, 1)
                                + varnt.substr(varnt.length - 1) + displayName.length;
                            variant["Variant Default Value"] = (ind == 0) ? 1 : 0;
                            service.itemList.push(variant);
                        })
                    }
                }

                function addIngredientProducts(product, item, index) {
                    var unit = AppUtil.unitDetails;
                    var recipe = product.webRecipe.recipes[product.prices[0].dimension];
                    recipe.ingredient.products.map(function (prodItem, ind) {
                        prodItem.details.map(function (prodDetail) {
                            var displayName = prodItem.display || "Option";
                            var variant = getVariantObj(index);
                            variant.Name = prodDetail.product.name;
                            variant["Variant Group Id"] = product.id + "_" + displayName;
                            variant["Variant Group Name"] = displayName;
                            variant["Variant Group Order"] = index + ind;
                            variant["external_id"] = product.id + "_" + prodDetail.product.productId;
                            variant["Variant Default Value"] = prodDetail.defaultSetting ? 1 : 0;
                            service.itemList.push(variant);
                        });
                    });
                }

                function addIngredientComposites(product, item, index) {
                    product.prices[0].recipe.ingredient.compositeProduct.details.map(function (copItem, ind) {
                        copItem.menuProducts.map(function (menuItem, ind) {
                            var variant = getVariantObj(index);
                            variant.Name = menuItem.product.name;
                            variant["Variant Group Id"] = product.id + "_" + copItem.name;
                            variant["Variant Group Name"] = copItem.name;
                            variant["Variant Group Order"] = index + ind;
                            variant["external_id"] = product.id + "_" + menuItem.product.productId;
                            variant["Variant Default Value"] = (ind == 0) ? 1 : 0;
                            service.itemList.push(variant);
                        });
                    });
                }

                function addAddons(product, item, index) {
                    if(product.type == 5){
                        product.prices[0].recipe.addons.map(function (add) {
                            var productId = add.product.productId;
                            if (service.addonIds.indexOf(productId) == -1) {
                                service.addonIds.push(productId);
                                var obj = angular.copy(addonItemObj);
                                obj["Rest Id"] = AppUtil.getSelectedUnitId();
                                obj["Items Id"] = [product.id];
                                obj["Addon Name"] = add.product.name;
                                obj["Addon Order"] = service.addonIds.length + 1;
                                obj["AddonGroup Name"] = "Addons";
                                obj["external_addon_id"] = productId;
                                service.addonList.push(obj);
                            } else {
                                service.addonList.map(function (addon) {
                                    if (addon.external_addon_id == add.product.productId) {
                                        if (addon["Items Id"].indexOf(product.id) == -1) {
                                            addon["Items Id"].push(product.id);
                                        }
                                    }
                                });
                            }
                        });
                    }
                }

                function getVariantObj(index) {
                    var variant = angular.copy(catalogItemObj);
                    variant.restaurantId = AppUtil.getSelectedUnitId();
                    variant.mainCategoryId = "";
                    variant.mainCategoryName = "";
                    variant.mainCategoryOrder = "";
                    variant.subCategoryId = "";
                    variant.subCategoryName = "";
                    variant.subCategoryOrder = "";
                    variant.itemId = "";
                    variant.Name = "";
                    variant.description = "";
                    variant.Parent = "V" + index;
                    variant["Variant Group Id"] = "";
                    variant["Variant Group Name"] = "";
                    variant["Variant Group Order"] = "";
                    variant["Variant Id"] = "";
                    variant["Variant Default Value"] = "";
                    variant.price = 0;
                    variant.veg_egg_non = "veg";
                    variant.Item_SGST = 0;
                    variant.Item_CGST = 0;
                    variant.Item_IGST = 0;
                    return variant;
                }

                function getItemPrice(product) {
                    var price = null;
                    if (product.prices.length == 1) {
                        price = product.prices[0].price;
                    } else {
                        product.prices.map(function (priceObj) {
                            if (price == null) {
                                price = priceObj.price;
                            } else {
                                if (price > priceObj.price) {
                                    price = priceObj.price;
                                }
                            }
                        });
                    }
                    return price;
                }

                function getVegNonVeg(type) {
                    var ret = null;
                    if (type == "VEG") {
                        ret = "veg";
                    } else if (type == "NON_VEG") {
                        ret = "non-veg";
                    } else {
                        ret = "veg";
                    }
                    return ret;
                }

                function getTaxPercent(code, taxCode, taxes) {
                    var percentage = 0;
                    Object.keys(taxes).map(function (key) {
                        var tax = taxes[key];
                        if (tax.taxCode == taxCode) {
                            percentage = parseFloat(parseFloat(tax.state[code]) / 100).toFixed(3);
                        }
                    });
                    return percentage;
                }

                return service;
            }]);
