/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

(function () {
    'use strict';

    angular.module('posApp').factory('AppUtil2', AppUtil);

    AppUtil.$inject = ['$cookieStore', '$rootScope', '$location', '$timeout', 'Flash', 'posAPI', 'usSpinnerService',
        'APIJson', 'PrintService', 'socketUtils', '$modal', 'trackingService', '$interval'];

    function AppUtil($cookieStore, $rootScope, $location, $timeout, Flash, posAPI, usSpinnerService, APIJson,
                     PrintService, socketUtils, $modal, trackingService, $interval) {

        var service = {};
        var swiggyCustomerNo = "9599598307";
        var amazonCustomerNo = "9599598306";

        service.offers = [];
        service.massOfferData;
        service.redemptionDisplayChaiProducts = [{id:10, name: "Desi Chai"},{id:170, name: "Green Tea"}, {id:1205, name: "Black Tea"}];
        service.GetRequest = GetRequest;
        service.verifyLogin = verifyLogin;
        service.GetLoggedInUser = GetLoggedInUser;
        service.getAllComplimentaryCodes = getAllComplimentaryCodes;
        service.customerSessionObj = {};
        service.disableForTakeaway = false;
        service.unitFamily = getUnitFamily();
        service.checkIsMonk = checkIsMonk;
        service.isCafe = checkIsCafe;
        service.isDelivery = checkIsDelivery;
        service.isCOD = checkIsCOD;
        service.isTakeaway = checkTakeaway;
        service.hasLiveInventory = hasLiveInventory;
        service.hasTableService = hasTableService;
        service.tableServiceType = tableServiceType;
        service.hasExtendedTableService = hasExtendedTableService;
        service.hasSeparateKotPrinting = hasSeparateKotPrinting;
        service.checkUnitProductsData = checkUnitProductsData;
        service.getUnitProductsData = getUnitProductsData;
        service.getEmployeeMealProductsData = getEmployeeMealProductsData;
        service.isWorkstationEnabled = checkWorkstationEnabled;
        service.isTokenEnabled = checkTokenEnabled;
        service.getCreditAccounts = getCreditAccounts;
        service.creditAccounts = null;
        service.JSONToCSVConvertor = JSONToCSVConvertor;
        service.JSONToCSVConvertorLabelFromatter = JSONToCSVConvertorLabelFromatter;
        service.showCancel = true;
        service.showEditSettlement = false;
        service.isCustomerScreen = false;
        service.previousLocation = ['/login'];
        service.unitDetails = {};
        service.getUnitDetails = getUnitDetails;
        service.transactionMetadata = {};
        service.giftCardOffers = {};
        service.getTransactionMetadata = getTransactionMetadata;
        service.isEmptyObject = isEmptyObject;
        service.orderSummaryObj = {};
        service.customerSearchObject = {}; // created for customer order
        // summary
        service.refreshRedeemLock = refreshRedeemLock;
        service.lockRedeem = lockRedeem;
        service.OrderObj = $rootScope.OrderObj;
        service.discountObj = {
            value: 0,
            percentage: 0,
            discountReason: null,
            discountCode: null
        };
        service.localityMapping = [];
        service.cities = [];
        service.companyMapping = [];
        service.freeKettle = false;
        service.freeKettleCode = 'LOYALTEA';
        service.CSObj = {
            firstname: null,
            middlename: null,
            lastname: null,
            loyaltyPoints: null,
            emailId: null,
            contactNumber: null,
            acquisitionSource: 'Chaayos-COD',
            countryCode: '+91',
            registrationUnitId: null,
            acquisitionToken: null,
            addressess: null
        };
        service.outlet = {
            pri_unitId: null,
            pri_name: 'Primary Outlet',
            sec_unitId: null,
            sec_name: 'Secondary Outlet',
            ter_unitId: null,
            ter_name: 'Tertiary Outlet',
            selectedId: 1
        };
        service.getCSAddress = getCSAddress();
        service.openOrderSearch = openOrderSearch;

        // Alerts

        service.myAlert = myAlert;
        service.mySuccessAlert = mySuccessAlert;
        service.myInfoAlert = myInfoAlert;
        service.myWarningAlert = myWarningAlert;
        service.stdDialogueAlert = stdDialogueAlert;

        service.customerDeliveryAddress = 0;
        service.getTaxProfile = getTaxProfile;
        service.getPackagingProfile = getPackagingProfile;
        service.getDeliveryProfile = getDeliveryProfile;
        service.getProductProfile = getProductProfile;
        service.getDeliveryProfileForUnit = getDeliveryProfileForUnit;
        service.checkInArrayOfPartners = checkInArrayOfPartners;
        service.startSpin = startSpin;
        service.stopSpin = stopSpin;
        service.getSettlements = getSettlements;
        service.getDeliveryDetail = getDeliveryDetail;
        service.discountValue = 0;
        service.isAndroid = isAndroid();
        service.sendOrder = sendOrder;
        service.restUrls = APIJson.urls;
        service.addOrderItems = addOrderItems;
        service.resetOrderItemCustomization = resetOrderItemCustomization;
        service.resetOtpStatus = resetOtpStatus;
        service.calculateCustomization = calculateCustomization;
        service.hasRecipeContents = hasRecipeContents;
        service.addNewItemForCombo = addNewItemForCombo;
        service.getCustomizationAbb = getCustomizationAbb;
        service.addNewItem = addNewItem;
        service.addNewItemForIndex = addNewItemForIndex;
        service.checkNumber = checkNumber;
        service.isDev = isDev;
        service.downloadConsumption = isDev();
        service.hasProductForId = hasProductForId;
        service.setRedemptionProductList = null;
        service.setUnitFamily = setUnitFamily;
        service.autoConfigData = {};
        service.getAutoConfigData = getAutoConfigData;
        service.setAutoConfigData = setAutoConfigData;
        service.removeAutoConfigData = removeAutoConfigData;
        service.getCoveredCustomerContact = getCoveredCustomerContact;
        service.swiggyCustomer = undefined;
        service.amazonCustomer = undefined;
        service.getSwiggyCustomer = getSwiggyCustomer;
        service.loadSwiggyCustomer = loadSwiggyCustomer;
        service.loadAmazonCustomer = loadAmazonCustomer;
        service.getProductProfileByCity = getProductProfileByCity;
        service.activeChannelPartnerId = -1;
        service.defaultDeliveryDetail = undefined;
        service.isWebDineIn = function (order) {
            // channel partner 14 is Webapp
            return order != null && (order.channelPartner == 14 && order.source == "CAFE");
        };

        service.resetCustomerSocket = function () {
            return {
                id: null,
                name: null,
                contact: null,
                email: null,
                loyalityPoints: 0,
                contactVerified: false,
                emailVerified: false,
                unitId: service.unitDetails.id,
                newCustomer: false,
                otp: null,
                chaiRedeemed: 0,
                productId: 10,
                otpVerified: false
            };
        };

        service.customerSocket = null;

        service.getMassOffers = getMassOffers;

        service.yesterday = yesterday;
        service.validate = validate;
        service.backToCover = backToCover;
        service.getCurrentDate = getCurrentDate;
        service.formatDate = formatDate;
        service.GetCustomerInfo = GetCustomerInfo;
        service.getEmptyComposition = getEmptyComposition;
        service.checkInventory = true;
        service.getSubscriber = function () {
            var currentUser = $rootScope.globals.currentUser;
            return {
                unit: currentUser.unitId,
                terminal: currentUser.terminalId,
                type: currentUser.screenType
            };
        };

        service.getParseList = function () {
            var parseList = APIJson.PROD_PARSE_LIST;
            if ($location.host().indexOf("prod") != -1) {
                parseList = APIJson.PROD_PARSE_LIST;
            } else {
                parseList = APIJson.DEV_PARSE_LIST;
            }
            // //console.log("returning parse list :::::::::::: ", parseList);
            return parseList;
        };

        // cache maintained for COD
        service.regionCache = [];
        service.deliveryProfileCache = [];
        service.taxProfileCache = [];
        service.packagingProfileCache = [];
        service.getProductForId = getProductForId;
        service.isInventoryTrackedProductForId = isInventoryTrackedProductForId;
        service.getOrderPointsRedeemed = getOrderPointsRedeemed;
        service.getProductsString = getProductsString;
        service.getSelectedAddress = getSelectedAddress;
        service.getSelectedUnitIdName = getSelectedUnitIdName;
        service.getOrderModeFromSource = getOrderModeFromSource;
        service.isPaidEmployeeMeal = isPaidEmployeeMeal;
        service.arrangeAddress = arrangeAddress;
        service.cardType = 'GIFT';
        service.setCardType = setCardType;
        service.reprintOrder = reprintOrder;
        service.reprintSettlementSlip = reprintSettlementSlip;
        service.reprintOrderKOT = reprintOrderKOT;
        service.sortBy = sortBy;
        service.currentTransactionGiftCards = [];
        service.setCurrentTransactionGiftCards = setCurrentTransactionGiftCards;
        service.getCurrentTransactionGiftCards = getCurrentTransactionGiftCards;
        service.getProductsOfCategory = getProductsOfCategory;
        service.productMap = {};
        service.categoryList = [];
        service.arrangeProductCategory = arrangeProductCategory;
        service.clearSpecialChars = clearSpecialChars;
        service.clearProductCache = clearProductCache;
        service.getOrderSourceName = getOrderSourceName;
        service.getSelectedUnitId = getSelectedUnitId;
        service.outletList = [];
        service.getUnitList = getUnitList;
        service.unitBasicDetailList = [];
        service.sendOrderStart = sendOrderStart;
        service.sendTableOrderStart = sendTableOrderStart;
        service.otpStatus = {text : "",status : -1};
        service.isCSOtpVerified = isCSOtpVerified;
        
        return service;

        /**
         * function definitions
         */

        function isCSOtpVerified() {
        	 return service.customerSocket != null ? service.customerSocket.otpVerified : false;
        }
        
        function isPaidEmployeeMeal() {
            return $rootScope.orderType == "paid-employee-meal";
        }

        function reprintSettlementSlip(tableRequestId) {
            var promise = posAPI.allUrl('/', service.restUrls.order.reprintSettlementReceipt).post(tableRequestId).then(function (response) {
                var receipt = response.plain();
                if (service.isAndroid) {
                    Android.printText([receipt.settlementReceipt]);
                } else {
                    PrintService.printOnBilling(receipt.settlementReceipt, receipt.printType);
                }
                $rootScope.showFullScreenLoader = false;
                return receipt;
            }, function (err) {
                $rootScope.showFullScreenLoader = false;
                service.myAlert(err.data.errorMessage);
            });
            return promise;
        }
        
        function reprintOrder(generatedOrderId) {
            var reqObj = {
                generatedOrderId: generatedOrderId,
                approvedBy: $rootScope.globals.currentUser.userId,
                reason: "Required"
            };
            //console.log(reqObj);
            var promise = posAPI.allUrl('/', service.restUrls.order.reprintOrder).post(service.GetRequest(reqObj)).then(function (response) {
                var receipt = response.plain();
                if (service.isAndroid) {
                    Android.printText(receipt.printString);
                } else {
                    PrintService.printOnBilling(receipt.printString, receipt.printType);
                }
                $rootScope.showFullScreenLoader = false;
                return receipt;
            }, function (err) {
                //console.log(err);
                $rootScope.showFullScreenLoader = false;
                service.myAlert(err.data.errorMessage);
            });
            return promise;
        }


        function reprintOrderKOT(generatedOrderId) {
            var reqObj = {
                generatedOrderId: generatedOrderId,
                approvedBy: $rootScope.globals.currentUser.userId,
                reason: "Required"
            };
            posAPI.allUrl('/', service.restUrls.order.reprintOrderKOT).post(service.GetRequest(reqObj)).then(function (response) {
                var receiptList = response.plain();
                if (receiptList.length > 0) {
                    if (service.isAndroid) {
                        var list = [];
                        for (var i = 0; i < receiptList.length; i++) {
                            list.push(receiptList[i].printString);
                        }
                        Android.printKots(list);
                    } else {
                        for (var i = 0; i < receiptList.length; i++) {
                            if (hasSeparateKotPrinting()) {
                                PrintService.printOnKot(receiptList[i].printString, receiptList[i].printType);
                            } else {
                                PrintService.printOnBilling(receiptList[i].printString,
                                    receiptList[i].printType);
                            }
                        }
                    }
                    $rootScope.showFullScreenLoader = false;
                } else {
                    service.myAlert("This Order does not contain items to be printed on KOT.");
                    $rootScope.showFullScreenLoader = false;
                }
            }, function (err) {
                //console.log(err);
                $rootScope.showFullScreenLoader = false;
                service.myAlert(err.data.errorMessage);
            });
        }

        function arrangeAddress(addressObj, isDetail) {
            if (addressObj == undefined) {
                return "-";
            }
            var address = "(";
            address = address + addressObj.addressType + ") ";
            if (addressObj.addressType == "OFFICE" && addressObj.company != null) {
                address = address + " " + addressObj.company + "<br>";
            }
            address = address + addressObj.line1;
            if (addressObj.line2 != null) {
                address = address + "," + addressObj.line2;
            }
            if (addressObj.locality != null) {
                address = address + "<br>" + addressObj.locality;
            }
            if (isDetail && addressObj.locality != null) {
                address = address + "," + addressObj.subLocality;
            }
            if (addressObj.landmark != null) {
                address = address + "<br>" + addressObj.landmark;
            }

            if (addressObj.city != null) {
                address = address + "<br>" + addressObj.city;
            }

            if (isDetail && addressObj.state != null) {
                address = address + "," + addressObj.state;
            }
            if (addressObj.country != null) {
                address = address + "," + addressObj.country;
            }

            return address;
        };

        function setUnitFamily(unitFamily) {
            if (unitFamily != undefined) {
                service.unitFamily = unitFamily;
            } else {
                service.unitFamily = getUnitFamily();
            }
        }

        function checkNumber(number) {
            var regex = /^([6-9])(\d{9})$/;
            var regexReturn = regex.test(number);
            // //console.log(regexReturn);
            return regexReturn;
        }

        function checkIsCOD() {
            var unitFamily = getUnitFamily();
            if (unitFamily != undefined) {
                return unitFamily == 'COD';
            }
            return false;

        }

        function getEnvType() {
            return ($location.host().indexOf("prod") != -1 ||
                        $location.host().indexOf("internal") != -1)? "PROD" : "DEV";
        }

        function isDev() {
            return getEnvType() == "DEV";
        }

        function checkTakeaway() {
            var unitFamily = getUnitFamily();
            if (unitFamily != undefined) {
                return (unitFamily == 'TAKE_AWAY' || unitFamily == "CHAI_MONK");
            }
            return false;
        }

        function getEmptyComposition() {
            var data = {
                variants: [],
                products: [],
                menuProducts: [],
                addons: [],
                options: []
            };
            return data;
        }

        function hasRecipeContents(recipe) {
            // //console.log('hasRecipeContents : ', recipe);
            if (recipe == null) {
                return false;
            }
            if (recipe.ingredient != null) {
                if (recipe.ingredient.variants != null && recipe.ingredient.variants.length > 0) {
                    return true;
                }
                if (recipe.ingredient.products != null && recipe.ingredient.products.length > 0) {
                    return true;
                }
                if (recipe.ingredient.compositeProduct != null && recipe.ingredient.compositeProduct.details != null
                    && recipe.ingredient.compositeProduct.details.length > 0) {
                    return true;
                }
                if (recipe.addons != null && recipe.addons.length > 0) {
                    return true;
                }
                if (recipe.options != null && recipe.options.length > 0) {
                    return true;
                }
            }
            return false;
        }

        function verifyLogin(userId, showComment, mandatoryComment, callback) {
            var modalInstance = $modal.open({
                animation: true,
                templateUrl: window.version + 'views/loginVerificationModal.html',
                controller: 'LoginVerificationModalCtrl',
                backdrop: 'static',
                size: 'sm',
                resolve: {
                    requestData: function () {
                        var data = {
                            id: userId,
                            showComment: showComment,
                            mandatoryComment: mandatoryComment
                        }
                        return data;
                    }
                }
            });
            modalInstance.result.then(function (result) {
                callback(result);
            }, function (result) {
                callback(result);
            });
        }
		
        function resetOtpStatus() {
			service.otpStatus = {
				text : "",
				status : -1
			};
		}
        
        function resetOrderItemCustomization(recipe) {
            if (recipe == null) {
                return null;
            }
            if (recipe.ingredient != null) {
                if (recipe.ingredient.variants != null && recipe.ingredient.variants.length > 0) {
                    for (var k in recipe.ingredient.variants) {
                        for (var i in recipe.ingredient.variants[k].details) {
                            recipe.ingredient.variants[k].details[i].selected = recipe.ingredient.variants[k].details[i].defaultSetting;
                        }
                    }
                }
                if (recipe.ingredient.products != null && recipe.ingredient.products.length > 0) {
                    for (var k in recipe.ingredient.products) {
                        for (var i in recipe.ingredient.products[k].details) {
                            recipe.ingredient.products[k].details[i].selected = recipe.ingredient.products[k].details[i].defaultSetting;
                        }
                    }
                }
                if (recipe.ingredient.compositeProduct != null && recipe.ingredient.compositeProduct.details != null
                    && recipe.ingredient.compositeProduct.details.length > 0) {
                    for (var k in recipe.ingredient.compositeProduct.details) {
                        for (var i in recipe.ingredient.compositeProduct.details[k].menuProducts) {
                            recipe.ingredient.compositeProduct.details[k].menuProducts[i].selected = false;
                        }
                    }
                }
                if (recipe.addons != null && recipe.addons.length > 0) {
                    for (var i in recipe.addons) {
                        recipe.addons[i].selected = false;
                    }
                }
                if (recipe.options != null && recipe.options.length > 0) {
                    for (var i in recipe.options) {
                        recipe.options[i].selected = false;
                    }
                }
            }

            return recipe;
        }

        function getCustomizationAbb(orderItem) {
            if (orderItem == undefined || orderItem.orderDetails.composition == undefined
                || orderItem.orderDetails.composition == null) {
                if (orderItem != undefined && orderItem.orderDetails != undefined && orderItem.orderDetails.takeAway) {
                    return 'Take Away';
                }
                return;
            }
            var allAddons = [];
            if (orderItem.orderDetails.takeAway) {
                allAddons.push('Take Away')
            }
            if (orderItem.orderDetails.composition.variants != null
                && orderItem.orderDetails.composition.variants.length > 0) {
                for (var i = 0; i < orderItem.orderDetails.composition.variants.length; i++) {
                    if (orderItem.orderDetails.composition.variants[i].selected)
                        allAddons.push(orderItem.orderDetails.composition.variants[i].alias);
                }
            }
            if (orderItem.orderDetails.composition.products != null
                && orderItem.orderDetails.composition.products.length > 0) {
                for (var i = 0; i < orderItem.orderDetails.composition.products.length; i++) {
                    if (orderItem.orderDetails.composition.products[i].selected)
                        allAddons.push(orderItem.orderDetails.composition.products[i].product.name);
                }
            }
            var returnAddons = '';
            if (allAddons.length > 0) {
                returnAddons = allAddons.join(" - ");
                allAddons = [];
            }
            if (orderItem.orderDetails.composition.addons.length > 0) {
                for (var i = 0; i < orderItem.orderDetails.composition.addons.length; i++) {
                    allAddons.push(orderItem.orderDetails.composition.addons[i].product.shortCode);
                }
            }
            if (orderItem.orderDetails.composition.options.length > 0) {
                for (var i = 0; i < orderItem.orderDetails.composition.options.length; i++) {
                    allAddons.push(orderItem.orderDetails.composition.options[i]);
                }
            }
            if (allAddons.length > 0) {
                returnAddons = returnAddons + ' :: ' + allAddons.join(",");
            }

            return returnAddons;
        }

        function calculateCustomization(orderData, itemId) {
            if (orderData.recipeDetails == null) {
                return orderData;
            }
            orderData.orderDetails.composition = getEmptyComposition();
            if (orderData.recipeDetails.ingredient != null) {
                if (orderData.recipeDetails.ingredient.variants != null
                    && orderData.recipeDetails.ingredient.variants.length > 0) {
                    for (var k in orderData.recipeDetails.ingredient.variants) {
                        for (var i in orderData.recipeDetails.ingredient.variants[k].details) {
                            if (orderData.recipeDetails.ingredient.variants[k].details[i].selected) {
                                orderData.orderDetails.composition.variants
                                    .push(orderData.recipeDetails.ingredient.variants[k].details[i]);
                            }
                        }
                    }
                }
                if (orderData.recipeDetails.ingredient.products != null
                    && orderData.recipeDetails.ingredient.products.length > 0) {
                    for (var k in orderData.recipeDetails.ingredient.products) {
                        for (var i in orderData.recipeDetails.ingredient.products[k].details) {
                            if (orderData.recipeDetails.ingredient.products[k].details[i].selected) {
                                orderData.orderDetails.composition.products
                                    .push(orderData.recipeDetails.ingredient.products[k].details[i]);
                            }
                        }
                    }
                }
                if (orderData.recipeDetails.ingredient.compositeProduct != null
                    && orderData.recipeDetails.ingredient.compositeProduct.details != null
                    && orderData.recipeDetails.ingredient.compositeProduct.details.length > 0) {
                    var finalArray = [];
                    for (var k in orderData.recipeDetails.ingredient.compositeProduct.details) {
                        for (var i in orderData.recipeDetails.ingredient.compositeProduct.details[k].menuProducts) {
                            if (orderData.recipeDetails.ingredient.compositeProduct.details[k].menuProducts[i].selected) {
                                var menuProduct = angular
                                    .copy(orderData.recipeDetails.ingredient.compositeProduct.details[k].menuProducts[i]);
                                menuProduct.name = orderData.recipeDetails.ingredient.compositeProduct.details[k].name;
                                menuProduct.item = addNewItemForCombo(menuProduct.product.productId,
                                    menuProduct.dimension.code, menuProduct.quantity, itemId + finalArray.length);
                                delete menuProduct['$$hashKey'];
                                // //console.log('Menu Clone Product ',
                                // menuProduct);
                                finalArray.push(menuProduct);
                            }
                        }
                    }
                    /*
                     * //console.log('Before Final Array ',finalArray);
                     * finalArray = JSON.parse(angular.toJson(finalArray));
                     * //console.log('After Final Array ',finalArray);
                     */
                    orderData.orderDetails.composition.menuProducts = finalArray;
                }
                if (orderData.recipeDetails.addons != null && orderData.recipeDetails.addons.length > 0) {
                    for (var i in orderData.recipeDetails.addons) {
                        if (orderData.recipeDetails.addons[i].selected) {
                            orderData.orderDetails.composition.addons.push(orderData.recipeDetails.addons[i]);
                        }
                    }
                }
                if (orderData.recipeDetails.options != null && orderData.recipeDetails.options.length > 0) {
                    for (var i in orderData.recipeDetails.options) {
                        if (orderData.recipeDetails.options[i].selected) {
                            orderData.orderDetails.composition.options.push(orderData.recipeDetails.options[i].name);
                        }
                    }
                }
            }
            // //console.log('After Calculation Composition ',
            // orderData.orderDetails.composition);

            return orderData;

        }

        function getSwiggyCustomer() {
            return service.swiggyCustomer;
        }

        function loadSwiggyCustomer() {
            var reqObj = {};
            reqObj.contactNumber = swiggyCustomerNo;
            reqObj.customer = {};
            reqObj.customer.contactNumber = swiggyCustomerNo;
            reqObj.customer.registrationUnitId = $rootScope.globals.currentUser.unitId;
            reqObj.customer.acquisitionToken = $rootScope.globals.currentUser.userId;
            $rootScope.showFullScreenLoader = true;
            var response = posAPI.allUrl('/', service.restUrls.codCustomer.lookup).post(reqObj)
                .then(function (response) {
                    service.swiggyCustomer = response.customer;
                    $rootScope.showFullScreenLoader = false;
                    return response.customer;
                }, function (err) {
                    $rootScope.showFullScreenLoader = false;
                    myAlert.myAlert(err.data.errorMessage);
                });
            return response;
        }
        function loadAmazonCustomer() {
            var reqObj = {};
            reqObj.contactNumber = amazonCustomerNo;
            reqObj.customer = {};
            reqObj.customer.contactNumber = amazonCustomerNo;
            reqObj.customer.registrationUnitId = $rootScope.globals.currentUser.unitId;
            reqObj.customer.acquisitionToken = $rootScope.globals.currentUser.userId;
            $rootScope.showFullScreenLoader = true;
            console.log(reqObj);
            var response = posAPI.allUrl('/', service.restUrls.codCustomer.lookup).post(reqObj)
                .then(function (response) {
                    service.amazonCustomer = response.customer;
                    $rootScope.showFullScreenLoader = false;
                    return response.customer;
                }, function (err) {
                    $rootScope.showFullScreenLoader = false;
                    myAlert.myAlert(err.data.errorMessage);
                });
            return response;
        }

        function getEmployeeMealProductsData(callback) {
            if (service.unitDetails.employeeMealProducts == undefined || service.unitDetails.employeeMealProducts == null || service.unitDetails.employeeMealProducts.length == 0) {
                $rootScope.showFullScreenLoader = true;
                posAPI.allUrl('/', service.restUrls.unitMetaData.employeeMealProducts)
                    .post(service.unitDetails.id)
                    .then(function (response) {
                            var data = response.plain();
                            service.unitDetails.employeeMealProducts = data;
                            addEmployeeMealProductPricesToUnit(data);
                            //localStorage.setItem("unitDetails", JSON.stringify(service.unitDetails));
                            $rootScope.showFullScreenLoader = false;
                            if (typeof callback == "function") {
                                callback();
                            }
                        },
                        function (err) {
                            $rootScope.showFullScreenLoader = false;
                            service.myAlert(err.data.errorMessage);
                            if (typeof callback == "function") {
                                callback();
                            }
                        });
            } else if (typeof callback == "function") {
                callback();
            }
        };

        function addEmployeeMealProductPricesToUnit(products) {
            service.unitDetails.empMealPrices = {};
            for (var i in products) {
                var product = products[i];
                for (var j in product.prices) {
                    var price = product.prices[j];
                    var priceCode = product.id + '_' + price.dimension;
                    service.unitDetails.empMealPrices[priceCode] = {
                        price: price.price,
                        taxCode: product.taxCode
                    };
                    localStorage.setItem("unitDetails", JSON.stringify(service.unitDetails));
                }
            }
        };

        function checkUnitProductsData() {
            service.unitDetails = localStorage.getItem("unitDetails") != null ? JSON.parse(localStorage.getItem("unitDetails")) : null;
            if (service.unitDetails.products != null || service.unitDetails.products.length > 0) {
                $rootScope.showFullScreenLoader = true;
                var req = {unitId: service.unitDetails.id, productDimensionRecipeIdMap: {}};
                service.unitDetails.products.map(function (product) {
                    req.productDimensionRecipeIdMap[product.id] = {};
                    product.prices.map(function (price) {
                        req.productDimensionRecipeIdMap[product.id][price.dimension] = price.recipeId;
                    })
                });
                posAPI.allUrl('/', service.restUrls.unitMetaData.checkUnitProductRecipes).post(req)
                    .then(function (response) {
                        if (response != true) {
                            service.clearProductCache();
                        }
                    }, function (err) {
                        $rootScope.showFullScreenLoader = false;
                        service.myAlert(err.data.errorMessage);
                    });
            }
        };

        function getUnitProductsData(callback) {
            service.unitDetails = localStorage.getItem("unitDetails") != null ? JSON.parse(localStorage.getItem("unitDetails")) : null;
            if (service.unitDetails.products == undefined || service.unitDetails.products == null || service.unitDetails.products.length == 0) {
                $rootScope.showFullScreenLoader = true;
                posAPI.allUrl('/', service.restUrls.unitMetaData.unitProducts)
                //.all("unit")
                    .post(service.unitDetails.id)
                    .then(function (response) {
                            var data = response.plain();
                            service.unitDetails.products = data.products;
                            arrangeProducts(service.unitDetails.products, service.categoryList, service.productMap);
                            var taxData = {};
                            for (var i in data.taxes) {
                                var tax = data.taxes[i];
                                taxData[tax.taxCode] = tax;
                            }
                            service.unitDetails.taxes = taxData;
                            addProductPricesToUnit(data.products);
                            localStorage.setItem("unitDetails", JSON.stringify(service.unitDetails));
                            //console.log('Selected Unit', service.unitDetails);
                            $rootScope.showFullScreenLoader = false;
                            if (typeof callback == "function") {
                                callback();
                            }
                        },
                        function (err) {
                            $rootScope.showFullScreenLoader = false;
                            service.myAlert(err.data.errorMessage);
                        });
            } else {
                if (isEmptyObject(service.productMap)) {
                    arrangeProducts(service.unitDetails.products, service.categoryList, service.productMap);
                }
                if (typeof callback == "function") {
                    callback();
                }
            }
        };

        function clearProductCache() {
            service.unitDetails.products = [];
            localStorage.setItem("unitDetails", JSON.stringify(service.unitDetails));
        }

        function addProductPricesToUnit(products) {
            service.unitDetails.prices = {};
            for (var i in products) {
                var product = products[i];
                for (var j in product.prices) {
                    var price = product.prices[j];
                    var priceCode = product.id + '_' + price.dimension;
                    service.unitDetails.prices[priceCode] = {
                        price: price.price,
                        taxCode: product.taxCode
                    };
                }
            }
        }

        function addNewItem(productItem, quantity, hasRedeemed, itemId) {
            return addNewItemForIndex(productItem, quantity, hasRedeemed, productItem.prices[0], itemId);
        }

        function addNewItemForCombo(productId, dimensionCode, quantity, itemId) {
            var productItem = getProductForId(productId);
            var orderItem = null;
            for (var index in productItem.prices) {
                if (productItem.prices[index].dimension == dimensionCode) {
                    orderItem = addNewItemForIndex(productItem, quantity, false, productItem.prices[index], itemId);
                }
            }
            if (orderItem == null) {
                orderItem = addNewItemForIndex(productItem, quantity, false, productItem.prices[0], itemId);
            }
            // //console.log('Menu Order Item', orderItem);
            return orderItem;

        }

        function addNewItemForIndex(productItem, quantity, hasRedeemed, priceData, itemId) {
            var amount = (priceData.price) * quantity;
            var isCombo = productItem.type == 8;
            // //console.log('Is Combo ', isCombo);
            var orderDetails = {
                itemId: itemId,
                productId: productItem.id,
                productName: productItem.name,
                productType: productItem.type,
                billType: productItem.billType,
                addons: [],
                complimentaryDetail: {
                    isComplimentary: false,
                    reasonCode: null,
                    reason: null
                },
                quantity: quantity,
                price: priceData.price,
                amount: amount,
                discountDetail: {
                    discountCode: null,
                    discountReason: null,
                    promotionalOffer: 0,
                    discount: {
                        percentage: 0,
                        value: 0
                    },
                    totalDiscount: 0
                },
                dimension: priceData.dimension,
                hasBeenRedeemed: hasRedeemed,
                isCombo: isCombo
            };

            var singleOrderItem = {
                orderDetails: orderDetails,
                productDetails: productItem
            };
            singleOrderItem.recipeDetails = resetOrderItemCustomization(angular.copy(priceData.recipe));
            singleOrderItem = calculateCustomization(singleOrderItem, itemId);
            // //console.log('singleOrderItem ', singleOrderItem);
            singleOrderItem.orderDetails.recipeId = priceData.recipeId;
            return singleOrderItem;
        }

        function getProductForId(productId) {
            for (var i = 0; i < service.unitDetails.products.length; i++) {
                var productObj = service.unitDetails.products[i];
                if (productObj.id == productId) {
                    return productObj;
                }
            }
        }

        function isInventoryTrackedProductForId(productId) {
            for (var i = 0; i < service.unitDetails.products.length; i++) {
                var productObj = service.unitDetails.products[i];
                if (productObj.id == productId) {
                    return productObj.inventoryTracked;
                }
            }
            return false;
        }

        function hasProductForId(productId) {
            for (var i = 0; i < service.unitDetails.products.length; i++) {
                if (service.unitDetails.products[i].id == productId) {
                    return true;
                    ;
                }
            }
            return false;
        }

        function checkWorkstationEnabled() {
            if (service.unitDetails != undefined) {
                return service.unitDetails.workstationEnabled;
            } else {
                return false;
            }
        }

        function checkTokenEnabled() {
            if (service.unitDetails != undefined) {
                return service.unitDetails.tokenEnabled;
            } else {
                return false;
            }
        }

        function checkIsCafe() {
            // console.log('Inside checkIsCafe')
            var unitFamily = getUnitFamily();
            if (unitFamily != undefined) {
                return unitFamily == 'CAFE' || unitFamily == "CHAI_MONK";
            }
            return true;
        }

        function checkIsMonk() {
            var unitFamily = getUnitFamily();
            if (unitFamily != undefined) {
                return unitFamily == 'CHAI_MONK';
            }
            return true;
        }

        function checkIsDelivery() {
            // console.log('Inside checkIsDelivery')
            var unitFamily = getUnitFamily();
            if (unitFamily != undefined) {
                return unitFamily == 'DELIVERY';
            }
            return true;
        }

        // TODO This needs to be moved to the backend. As of now there is a
        // check on unit id for Galleria
        function hasSeparateKotPrinting() {
            return false;//service.unitDetails != null && service.unitDetails.id == 10005;
        }

        function getUnitFamily() {
            if (isEmptyObject(service.unitFamily) && $rootScope.globals != undefined) {
                var currentUser = $rootScope.globals.currentUser;
                if (currentUser != undefined) {
                    return currentUser.unitFamily;
                }
            }
            return service.unitFamily;
        }

        function getSettlements(orderObj) {
            var settlementString = "";
            var settlementsArray = [];
            // //console.log("inside getSettlements");
            if (orderObj != undefined && orderObj.settlements != undefined) {
                for (var i = 0; i < orderObj.settlements.length; i++) {
                    settlementsArray.push(orderObj.settlements[i].modeDetail.name + ":"
                        + orderObj.settlements[i].amount);
                }
            }
            settlementString = settlementsArray.join(",");
            // //console.log(settlementString);
            return settlementString;
        }

        function getCSAddress() {
            return service.CSObj.addressess;
        }

        function stdDialogueAlert(text, yesCallback, noCallback) {
            bootbox.confirm(text, function (result) {
                if (result == true) {
                    yesCallback();
                } else {
                    noCallback();
                }
            });
        }

        function GetRequest(obj) {
            var requestObj = {};
            if (typeof obj != 'string') {
                requestObj = {
                    session: $rootScope.globals.currentUser,
                    data: JSON.stringify(obj)
                };
            } else {
                requestObj = {
                    session: $rootScope.globals.currentUser,
                    data: obj
                };
            }

            return requestObj;
        }

        function clearSpecialChars(str) {
            return str.replace(/(a1)|(a0)|(i)|(!)|(M0)|(E)|(E)/g, '');
        }

        function isEmptyObject(obj) {
            if (obj != undefined && obj != null) {
                if (typeof obj == 'string' || typeof obj == 'number')
                    return obj.toString().length == 0;
                else if (typeof obj == 'boolean' )
                	return false;
                else
                    return Object.keys(obj).length == 0;
            }
            return true;
        }

        function GetLoggedInUser() {
            return $rootScope.globals.currentUser.userId;
        }

        function myAlert(message) {
            Flash.create('danger', message, null);
        }

        function mySuccessAlert(message) {
            Flash.create('success', message, null);
        }

        function myInfoAlert(message) {
            Flash.create('info', message, null);
        }

        function myWarningAlert(message) {
            Flash.create('warning', message, null);
        }

        function refreshRedeemLock() {
            setPointsRedeemed(0, false);
        }

        function lockRedeem(loyaltyPointsRedeemed) {
            setPointsRedeemed(loyaltyPointsRedeemed, true);
        }

        function setPointsRedeemed(loyaltyPointsRedeemed, status) {
            var pointsRedeemed = {
                pointsRedeemed: loyaltyPointsRedeemed,
                pointsRedeemedSuccessfully: status
            };
            $cookieStore.put('pointsRedeemed', pointsRedeemed);
            // //console.log($cookieStore.get('pointsRedeemed'));
        }

        function getTaxProfile() {
            startSpin('spinner-1');
            var unitId = getSelectedUnitId();
            if (service.taxProfileCache[unitId] == undefined || service.taxProfileCache[unitId] == null) {
                posAPI.allUrl('/', service.restUrls.posMetaData.taxProfile).customGET("", {
                    unitId: unitId
                }).then(function (response) {
                    // //console.log(response.plain());
                    service.taxProfileCache[unitId] = response.plain();
                    service.unitDetails.taxProfiles = service.taxProfileCache[unitId];
                }, function (err) {
                    service.myAlert(err.data.errorMessage);
                });
            } else {
                service.unitDetails.taxProfiles = service.taxProfileCache[unitId];
            }

        }

        function getPackagingProfile() {
            startSpin('spinner-1');
            var unitId = getSelectedUnitId();
            if (service.packagingProfileCache[unitId] == undefined || service.packagingProfileCache[unitId] == null) {
                posAPI.allUrl('/', service.restUrls.posMetaData.packagingProfile).customGET("", {
                    unitId: unitId
                }).then(function (response) {
                    // //console.log(response.plain());
                    service.packagingProfileCache[unitId] = response.plain();
                    service.unitDetails.packagingType = service.packagingProfileCache[unitId].packagingType;
                    service.unitDetails.packagingValue = service.packagingProfileCache[unitId].packagingValue;
                }, function (err) {
                    service.myAlert(err.data.errorMessage);
                });
            } else {
                service.unitDetails.packagingType = service.packagingProfileCache[unitId].packagingType;
                service.unitDetails.packagingValue = service.packagingProfileCache[unitId].packagingValue;
            }
        }

        function validCache(region) {
            var isPartnerOrder = $rootScope.isPartnerOrder != null ? $rootScope.isPartnerOrder : false;
            var regionObj = service.regionCache[getRegionKey(region, isPartnerOrder)];
            return regionObj != null && regionObj.products != null && regionObj.taxData != null && regionObj.prices != null;
        }


        function getProductProfile(customer, callback) {
            var city = getSelectedCity(customer);
            getProductProfileByCity(city, callback);
        }
        
        function getProductProfileByCity(city, callback) {
            startSpin('spinner-1');
            $rootScope.showFullScreenLoader = true;

            var isPartnerOrder = $rootScope.isPartnerOrder != null && $rootScope.isPartnerOrder != undefined ? $rootScope.isPartnerOrder
                : false;
            var region = null;
            getUnitBasicDetails(function () {
                service.unitBasicDetailList.map(function (ubd) {
                    if (getSelectedUnitId() === ubd.id) {
                        region = ubd.region;
                    }
                });
            });
            if (region == null) {
                region = getCODRegion(city);
            }

            if (!validCache(region)) {
                posAPI.allUrl('/', service.restUrls.unitMetaData.productProfile).customGET("", {
                    region: region,
                    unitId: getSelectedUnitId(),
                    partnerOrder: isPartnerOrder
                }).then(function (response) {
                    var data = response.plain();
                    var taxData = {};
                    for (var i in data.taxes) {
                        var tax = data.taxes[i];
                        taxData[tax.taxCode] = tax;
                    }
                    var cacheData = {};
                    cacheData.products = data.products;
                    cacheData.taxData = taxData;
                    setUnitDetailData(cacheData);
                    service.unitDetails.prices = cacheData.prices;
                    console.log('Selected Unit', service.unitDetails);
                    service.unitDetails.products = data.products;
                    service.unitDetails.taxes = taxData;
                    service.regionCache[getRegionKey(region, isPartnerOrder)] = cacheData;
                    // This code needs to be changed and merged with
                    // coverController.
                    // Its a duplicate
                    stopSpin('spinner-1');
                    $rootScope.showFullScreenLoader = false;
                    if (typeof callback == "function") {
                        callback();
                    }
                    $location.url('/pos');
                }, function (err) {
                    stopSpin('spinner-1');
                    $rootScope.showFullScreenLoader = false;
                    service.myAlert(err.data.errorMessage);
                });
            } else {
                var data = service.regionCache[getRegionKey(region, isPartnerOrder)];
                service.unitDetails.products = data.products;
                service.unitDetails.taxes = data.taxData;
                // This code needs to be changed and merged with
                // coverController.
                // Its a duplicate
                service.unitDetails.prices = data.prices;
                if (typeof callback == "function") {
                    callback();
                }
                stopSpin('spinner-1');
                $rootScope.showFullScreenLoader = false;
                $location.url('/pos');
            }

        }


        function getUnitBasicDetails() {
            if (service.unitBasicDetailList == null || service.unitBasicDetailList.length === 0) {
                $rootScope.showFullScreenLoader = true;
                posAPI.allUrl('/', service.restUrls.unitMetaData.allUnitsList).customGET("", {}).then(function (response) {
                    if (response != null && response.length > 0) {
                        service.unitBasicDetailList = response;
                    }
                    $rootScope.showFullScreenLoader = false;
                    if (typeof callback == "function") {
                        callback();
                    }
                }, function (err) {
                    $rootScope.showFullScreenLoader = false;
                    service.myAlert(err.data.errorMessage);
                });
            } else {
                if (typeof callback == "function") {
                    callback();
                }
            }
        }

        function setUnitDetailData(data) {
            data.prices = {};
            for (var i in data.products) {
                var product = data.products[i];
                for (var j in product.prices) {
                    var price = product.prices[j];
                    var priceCode = product.id + '_' + price.dimension;
                    data.prices[priceCode] = {
                        price: price.price,
                        taxCode: product.taxCode
                    };
                }
            }
        }

        function getSelectedCity(customer) {
            var city = "DELHI";
            if (customer.newAddress != undefined) {
                city = customer.newAddress.city;
            } else {
                customer.addresses.forEach(function (address) {
                    if (address.isSelectedAddress) {
                        city = address.city;
                    }
                });
            }
            return city;
        }

        function getCODRegion(city) {
            var region = "NCR";
            if (city.toUpperCase() == "MUMBAI") {
                region = "MUMBAI";
            }
            if (city.toUpperCase() == "CHANDIGARH") {
                region = "CHANDIGARH";
            }
            if (city.toUpperCase() == "CHANDIGARH") {
                region = "CHANDIGARH";
            }
            //Chutzpah for IIT Delhi
            if (getSelectedUnitId() == 26039) {
                region = "NCR_EDU";
            }
            return region;
        }

        function getRegionKey(region, isPartnerOrder) {
            return isPartnerOrder ? region + "-PARTNER" : region;
        }

        function getSelectedUnitId() {
            // //console.log("inside getSelectedUnitId function");
            var selectedUnitId = null;
            // //console.log(service.outlet);
            if (service.outlet.selectedId == 1) {
                selectedUnitId = service.outlet.pri_unitId;
            } else if (service.outlet.selectedId == 2) {
                selectedUnitId = service.outlet.sec_unitId;
            } else {
                selectedUnitId = service.outlet.ter_unitId;
            }
            // //console.log("selectedUnit is ::: " + selectedUnitId);
            return selectedUnitId;
        }

        function getDeliveryProfile(callback) {
            var unitId = getSelectedUnitId();
            if (service.deliveryProfileCache[unitId] == undefined || service.deliveryProfileCache[unitId] == null) {
                getDeliveryProfileForUnit(unitId, callback);
            } else {
                service.unitDetails.deliveryPartners = service.deliveryProfileCache[unitId];
            }
        }

        function getDeliveryProfileForUnit(selectedUnitId, callback) {
            startSpin('spinner-1');
            posAPI.allUrl('/', service.restUrls.posMetaData.deliveryProfile).customGET("", {
                unitId: selectedUnitId
            }).then(function (response) {
                service.deliveryProfileCache[selectedUnitId] = response.plain(); // setting
                // cache
                service.unitDetails.deliveryPartners = service.deliveryProfileCache[selectedUnitId];
                stopSpin('spinner-1');
                if (callback != undefined && typeof callback == "function") {
                    callback(service.unitDetails.deliveryPartners);
                }
            }, function (err) {
                service.myAlert(err.data.errorMessage);
            });

        }

        function checkInArrayOfPartners(partners, partnerid) {
            for (var i = 0; i < partners.length; i++) {
                var partner = partners[i];

                if (partner.id == partnerid) {
                    // //console.log("returning true");
                    return true;
                }
            }
            // //console.log("returning false");
            return false;
        }

        function startSpin(spinnerKey) {
            // //console.log("inside start spin function");
            usSpinnerService.spin(spinnerKey);
        }

        function stopSpin(spinnerKey) {
            // //console.log("inside stop spin function");
            usSpinnerService.stop(spinnerKey);
        }

        function openOrderSearch(generateOrderId, orderType) {
            $rootScope.orderType = orderType;
            // var reqObj = service.GetRequest(generateOrderId);
            posAPI.allUrl('/', service.restUrls.order.generatedOrder).post(generateOrderId)
                .then(
                    function (response) {
                        service.OrderObj = $rootScope.OrderObj = {};
                        if (response != undefined) {
                            service.OrderObj = $rootScope.OrderObj = response.plain();
                            service.showCancel = service.OrderObj.source == service.unitFamily;
                            service.showEditSettlement = service.OrderObj.source == service.unitFamily;
                            // //console.log(response.plain());
                            if (service.OrderObj.deliveryAddress != undefined
                                && service.OrderObj.deliveryAddress != null
                                && service.OrderObj.deliveryAddress > 0) {
                                GetCustomerInfo(service.OrderObj.deliveryAddress);
                                getDeliveryDetail(generateOrderId);
                            } else if (!service.isEmptyObject($rootScope.CustomerObj)) {
                                $rootScope.CustomerObj = {};
                            }

                            $location.url('/orderSearch');
                        }
                    }, function (err) {
                        service.myAlert(err.data.errorMessage);
                    });

        }

        function GetCustomerInfo($deliveryAddressId) {
            // var reqObj = service.GetRequest($deliveryAddressId + "");
            posAPI.allUrl('/', service.restUrls.customer.lookupAddress).post($deliveryAddressId).then(function (response) {
                $rootScope.CustomerObj = {};
                $rootScope.CustomerObj = response.plain();
                angular.forEach($rootScope.CustomerObj.addresses, function (deliveryAddress) {
                    if (deliveryAddress.id === $deliveryAddressId) {
                        $rootScope.deliveryAddress = deliveryAddress;
                    }
                });
            }, function (err) {
                $rootScope.CustomerObj = {};
                service.myAlert(err.data.errorMessage);
            });
        }

        function getDeliveryDetail(generatedOrderId) {
            // var reqObj = service.GetRequest(generatedOrderId);
            // //console.log("inside getDelivery Detail for order Id :::: "
            // + generatedOrderId);
            posAPI.allUrl('/', service.restUrls.delivery.details)
                .post(generatedOrderId)
                .then(
                    function (response) {
                        $rootScope.deliveryObject = null;
                        if (response != undefined && response != null) {
                            $rootScope.deliveryObject = response.plain();
                            if ($rootScope.deliveryObject != null) {
                                console.log("inside if loop of empty check for deliveryObject");
                                $rootScope.deliveryObject.deliveryPartnerName = getDeliveryPartner($rootScope.deliveryObject.deliveryPartnerId);
                            }
                        }
                    }, function (err) {
                        service.myAlert(err.data.errorMessage);
                        $rootScope.deliveryObject = null;
                    });
        }

        function isAndroid() {
            var isAndroid = false;
            // console.log('Called Is Android');
            // console.log(navigator.userAgent);
            if (/Android/i.test(navigator.userAgent)) {
                isAndroid = true;
            }
            return isAndroid;
        }

        function getDeliveryPartner(partnerId) {
            if (!isEmptyObject(service.transactionMetadata)) {
                var partners = service.transactionMetadata.deliveryPartner;
                // //console.log("inside getDeliveryPartner for partner id ::::
                // "
                // + partnerId);
                for (var i = 0; i < partners.length; i++) {
                    if (partnerId == partners[i].id) {
                        return partners[i].name;
                    }
                }
            }
            return "Delivery Partner not found";
        }

        function addOrderItems(items) {
            var orderItems = angular.copy(items);
            var orderItemResolvedArray = [];
            for (var i = 0; i < orderItems.length; i++) {
                if (orderItems[i].orderDetails.isCombo) {
                    for (var j in orderItems[i].orderDetails.composition.menuProducts) {
                        orderItems[i].orderDetails.composition.menuProducts[j] = orderItems[i].orderDetails.composition.menuProducts[j].item.orderDetails;
                    }
                }
                orderItemResolvedArray.push(orderItems[i].orderDetails);
            }
            console.log(orderItemResolvedArray);
            return orderItemResolvedArray;
        }

        function getOrderPointsRedeemed(orderItems) {
            var pointsRedeemed = 0;
            if (!service.customerSocket.eligibleForSignupOffer && !service.freeKettle) {
                orderItems.forEach(function (item) {
                    if (item.orderDetails.hasBeenRedeemed) {
                        pointsRedeemed = pointsRedeemed + 60;
                    }
                });
            }
            return pointsRedeemed != 0 ? -pointsRedeemed : 0;
        }

        function getProductsString(orderItemArray) {
            var orderItems = '';
            if (orderItemArray != undefined && orderItemArray != null) {
                orderItemArray.forEach(function (item) {
                    orderItems = orderItems + ',' + item.orderDetails.productId + '(' + item.orderDetails.quantity
                        + ')';
                });
                return orderItems.substring(1);
            } else {
                return '';
            }
        }

        function setCardType(value) {
            service.cardType = value;
        }

        function sendOrder(order, $modalInstance, callback, customerPresent, orderItemArray) {
            // //console.log(order);
            var orderCreationCallbackFunction = function () {
                if (callback != undefined && typeof callback == 'function') {
                    callback();
                    //			service.startSpin('spinner-2');
                }
                $modalInstance.dismiss('submit');
                sendOrderforCreation(order, service.unitDetails, service.isAndroid, customerPresent);
            };
            if (service.cardType != 'ECARD' && service.cardType != 'GYFTR' && service.cardType!='AP01' && checkGiftCardInOrder(order)) {
                validateGiftCardsInOrder(order, orderCreationCallbackFunction, orderItemArray);
            } else {
                orderCreationCallbackFunction();
            }

        }

        function checkGiftCardInOrder(order) {
            var zeroTaxItemList = order.orders.filter(function (orderItem) {
                return orderItem.code == "GIFT_CARD";
            });
            return zeroTaxItemList.length > 0;
        }
        ;

        function sendOrderforCreation(order, unitDetails, isAndroid, customerPresent) {
            // var reqObj = service.GetRequest(order);
            if ($rootScope.orderType == "order" || $rootScope.orderType == "employee-meal" || $rootScope.orderType == "paid-employee-meal") {
                createOrder(order, unitDetails, isAndroid, customerPresent);

            } else if ($rootScope.orderType == "complimentary-order") {
                createOrder(order, unitDetails, isAndroid, customerPresent);
            } else if ($rootScope.orderType == "wastage-order") {
                createWastageOrder(order);
            } else if ($rootScope.orderType == "unsatisfied-customer-order" || $rootScope.orderType == "PPE") {
                order.linkedOrderId = $rootScope.orderIdforUnsatisfiedOrder;
                createOrder(order, unitDetails, isAndroid, customerPresent);
                $rootScope.orderIdforUnsatisfiedOrder = null;
            }
            else if ($rootScope.orderType == "subscription") {
                createSubscriptionEventOrder(order, unitDetails);
            }
        }

        function createSubscriptionEventOrder(reqObj, unitDetails) {
            if (service.isCOD()) {
                var requestUrl = service.restUrls.subscription.createSubscription;
                posAPI.allUrl('/', requestUrl).post(reqObj).then(function (response) {
                    var dataObj = response;
                    service.mySuccessAlert("Subscription with subscription id: " + dataObj + " created successfully");
                    addUpdatelastThreeOrderArray(dataObj, unitDetails);
                    $rootScope.showFullScreenLoader = false;
                }, function (err) {
                    service.myAlert(err.data.errorMessage);
                    $rootScope.showFullScreenLoader = false;
                });
            } else {
                service.myAlert("Subscriptions can be created from call centers only!");
            }
        }

        function getAllComplimentaryCodes(list) {
            var array = [];
            for (var i in list) {
                if (list[i].id == 1 || list[i].id == 2100 || list[i].id == 2101) {
                    continue;
                }
                if ((service.isCOD() == true) && (list[i].id == 2105 || list[i].id == 2106)) {
                    continue;
                }
                array.push(list[i]);
            }
            return array;
        }

        function validateGiftCardsInOrder(reqObj, createOrderCallback, orderItemArray) {
            var gCards = [];
            var configData = service.getAutoConfigData();
            reqObj.orders.map(function (orderItem) {
                if (orderItem.code == "GIFT_CARD") {
                    gCards.push({
                        itemId: orderItem.itemId,
                        productName: orderItem.productName,
                        cardValue: orderItem.price,
                        cardNumber: orderItem.itemCode,
                        isValid: true,
                        error: "",
                        buyerId: service.customerSocket.id,
                        empId: $rootScope.globals.currentUser.userId,
                        unitId: configData.unitId,
                        terminalId: configData.selectedTerminalId,
                        type: service.cardType
                    });
                }
            });
            posAPI.allUrl('/', service.restUrls.order.validateGiftCardsInOrder).post(gCards).then(function (response) {
                if (response.errorType != undefined && response.errorType != '') {
                    var msg = /*(response.errorType != undefined) ? */"Error validating gift cards!" + response.errorMessage;
                    service.myAlert(msg);
                    return false;
                }
                var dataObj = response.plain();
                if (dataObj != null) {
                    var valid = true;
                    dataObj.map(function (cardObj) {
                        if (valid && !cardObj.isValid) {
                            valid = false;
                        }
                    });
                    if (valid) {
                        if (createOrderCallback != undefined && typeof createOrderCallback == 'function') {
                            createOrderCallback();
                        }
                    } else {
                        service.mySuccessAlert("Please ask customer to fill the correct gift card codes.");
                        if (service.cardType == 'GIFT') {
                            sendGiftCardsRevalidation(dataObj, orderItemArray);
                        }
                    }
                } else {
                    var msg = (dataObj.errorType == undefined) ? "Error validating gift cards!" : dataObj.errorMessage;
                    service.myAlert(msg);
                    $rootScope.showFullScreenLoader = false;
                }
            }, function (err) {
                service.myAlert(err.data.errorMessage);
            });
        }

        function sendGiftCardsRevalidation(dataObj, orderItemArray) {
            dataObj.map(function (cardObj) {
                if (!cardObj.isValid) {
                    cardObj.cardNumber = "";
                    orderItemArray.map(function (item) {
                        if (item.orderDetails.itemId == cardObj.itemId) {
                            item.orderDetails.itemCode = "";
                            item.orderDetails.isCardValid = false;
                        }
                    });
                }
            });
            socketUtils.emitMessage({
                GIFT_CARDS_REVALIDATE: dataObj
            });
        }

        function createWastageOrder(reqObj) {

            if (orderSanitized(reqObj)) {

                reqObj = JSON.parse(JSON.stringify(reqObj, function (key, value) {
                    if (key === "recipeDetails" || key === 'productDetails' || key === 'recipe') {
                        return undefined;
                    }

                    return value;
                }));
                var requestUrl = service.restUrls.order.createComplimentaryOrder;
                posAPI.allUrl('/', requestUrl).post(reqObj).then(
                    function (response) {
                        var msg = response == undefined ? "Wastage booked successfully"
                            : response.plain().errorMessage;

                        $timeout($rootScope.fetchCurrentAllSalesData, 10000); // get
                        if (response == undefined) {
                            service.mySuccessAlert(msg);
                        } else {
                            bootbox.alert(msg);
                        }
                        service.setRedemptionProductList = null;
                        $rootScope.showFullScreenLoader = false;
                        if (!service.isCOD()) {
                            service.refreshRedeemLock();
                        }
                    }, function (err) {
                        service.myAlert(err.data.errorMessage);
                    });
            } else {
                service.myAlert("Error in complimentary order please and re-punch the order!");
                console.log("Error in complimentary order please and re-punch the order!");
            }
        }

        function createOrder(reqObj, unitDetails, isAndroid, customerPresent) {

            if (orderSanitized(reqObj)) {

                reqObj = JSON.parse(JSON.stringify(reqObj, function (key, value) {
                    if (key === "recipeDetails" || key === 'productDetails' || key === 'recipe') {
                        return undefined;
                    }

                    return value;
                }));
                /*var requestUrl = !isAndroid ? service.restUrls.order.createOrder
                    : service.restUrls.order.androidCreateOrder;*/
                var requestUrl = service.restUrls.order.createOrder;
                posAPI.allUrl('/', requestUrl).post(reqObj).then(
                    function (response) {
                        $rootScope.showFullScreenLoader = false;
                        var dataObj = response.plain();
                        service.setCurrentTransactionGiftCards(dataObj.giftCards);
                        var msg = (dataObj.errorType == undefined) ? "Order placed successfully"
                            : "Order Creation Failed! " + dataObj.errorMessage;
                        var displayError = (dataObj.errorType == undefined) ? false : true;
                        if(displayError){
                        	service.myAlert(msg);
                        	bootbox.alert(msg);
                        } else {
                        	service.mySuccessAlert(msg);
                        }
                        try {
                            if (dataObj.errorType == undefined) {
                                reqObj.channelPartnerName = getOrderSourceName(reqObj.channelPartner);
                                reqObj.generatedOrderId = dataObj.generatedOrderId;
                                trackingService.trackSuccessOrder(reqObj);
                            }
                        } catch (e) {
                            console.log(e);
                        }
                        // get analytics data for this order
                        $timeout($rootScope.fetchCurrentAllSalesData, 10000);
                        if (dataObj.errorType != undefined && msg.startsWith("Employee Meal Issue")) {
                            alert(msg);
                        }

                        service.setRedemptionProductList = null;
                        addUpdatelastThreeOrderArray(dataObj, unitDetails);
                        if (reqObj.isGiftOrder) {
                            $rootScope.$broadcast("updateGiftAmountFired", {myParam: {}});
                        }

                        try {
                            trackingService.logout();
                        } catch (e) {
                        }
                        // default object
                        if (!service.isCOD() && dataObj.receipts != null && dataObj.receipts.length > 0) {
                            // //console.log(dataObj);
                            service.refreshRedeemLock();

                            // This is for Bill printing Only
                            if (service.isTakeaway() && !customerPresent) {
                                if (isAndroid) {
                                    printAndroidReceipt(dataObj.receipts);
                                    //Android.printReceipt(JSON.stringify(dataObj.receipts));
                                } else {
                                    // printing just the bill in case of
                                    // take away
                                    PrintService.printOnBilling(dataObj.receipts[0], dataObj.printType);
                                }
                            }

                            // this is for bill print only
                            if (service.isCafe() && service.isWorkstationEnabled()) {
                                if (isAndroid) {
                                    printAndroidReceipt([dataObj.receipts[0]]);
                                    //Android.printReceipt(JSON.stringify([dataObj.receipts[0]]));
                                } else {
                                    PrintService.printOnBilling(dataObj.receipts[0], dataObj.printType);
                                }
                            } else if (service.isCafe()) {
                                if (isAndroid) {
                                    printAndroidReceipt(dataObj.receipts);
                                    //Android.printReceipt(JSON.stringify(dataObj.receipts));
                                } else {
                                    printBills(dataObj);
                                }
                            }

                        }
                        if (service.downloadConsumption && dataObj.generatedOrderId != null
                            && dataObj.generatedOrderId != "") {
                            posAPI.allUrl('/', service.restUrls.order.calculateConsumption).post(dataObj.generatedOrderId)
                                .then(
                                    function (response) {
                                        // download file here
                                        // //console.log("Success in
                                        // consumption calulation");
                                        JSONToCSVConvertor(response, "ItemConsumption_"
                                            + dataObj.generatedOrderId, true);
                                    }, function (err) {
                                        // console.log("Error While
                                        // calulation consumption");
                                    });
                            posAPI.allUrl('/', service.restUrls.order.calculateAllConsumption).post(
                                dataObj.generatedOrderId).then(
                                function (response) {
                                    // download file here
                                    // //console.log("Success in all
                                    // consumption calulation");
                                    JSONToCSVConvertor(response, "AllItemConsumption_"
                                        + dataObj.generatedOrderId, true);
                                }, function (err) {
                                    // console.log("Error While
                                    // calulation all consumption");
                                });
                        }
                    }, function (err) {
                        service.myAlert(err.data.errorMessage);
                        $rootScope.showFullScreenLoader = false;
                    });
            } else {
                service.myAlert("Error in order please and re-punch the order!");
                console.log("Error in order please and re-punch the order!");
            }
        }

        function printAndroidReceipt(data) {
            if($rootScope.globals.currentUser.unitId == 26111) { //indiranagar exception for android version 8.1
                Android.printReceipt(JSON.stringify(data));
            } else {
                Android.printReceipt(data);
            }
        }

        function orderSanitized(reqObj) {
            var sum = 0;
            reqObj.settlements.map(function (settlement) {
                sum += settlement.amount;
            });
            console.log(sum, reqObj.transactionDetail.paidAmount);
            if (sum == reqObj.transactionDetail.paidAmount) {
                return true;
            } else {
                return false;
            }
        }

        function printBills(dataObj) {
            for (var printReceipt in dataObj.receipts) {

                if (dataObj.billIncludedInReceipts) {
                    if (printReceipt == 0) {
                        PrintService.printOnBilling(dataObj.receipts[printReceipt], dataObj.printType);
                    } else {
                        if (hasSeparateKotPrinting()) {
                            PrintService.printOnKot(dataObj.receipts[printReceipt], dataObj.printType);
                        } else {
                            PrintService.printOnBilling(dataObj.receipts[printReceipt], dataObj.printType);
                        }
                    }
                } else {
                    if (hasSeparateKotPrinting()) {
                        PrintService.printOnKot(dataObj.receipts[printReceipt], dataObj.printType);
                    } else {
                        PrintService.printOnBilling(dataObj.receipts[printReceipt], dataObj.printType);
                    }
                }
            }

        }

        function getOrderSourceName(channelPartner) {
            var name = "";
            for (var i = 0; i < service.getTransactionMetadata().channelPartner.length; i++) {
                if (service.getTransactionMetadata().channelPartner[i].id == channelPartner) {
                    name = service.getTransactionMetadata().channelPartner[i].name;
                    break;
                } else if (channelPartner == 1) {
                    name = "Chaayos (Dine In)";
                    break;
                }
            }
            return name;
        }

        function addUpdatelastThreeOrderArray(dataObj, unitDetails) {
            var lastThreeOrders = $cookieStore.get('lastThreeOrders');
            // //console.log(lastThreeOrders);
            if (typeof lastThreeOrders === 'undefined') {
                lastThreeOrders = getDefaultLastThreeOrders(dataObj, unitDetails);
            } else {
                var currentUnitId = lastThreeOrders.unitId;
                if (currentUnitId == unitDetails.id) {
                    if (lastThreeOrders.orders.length >= 3) {
                        lastThreeOrders.orders.shift();
                    }
                    lastThreeOrders.orders.push(dataObj.generatedOrderId);
                    // //console.log(lastThreeOrders);
                } else {
                    lastThreeOrders = getDefaultLastThreeOrders(dataObj, unitDetails);
                }
            }
            $cookieStore.put('lastThreeOrders', lastThreeOrders);
            $rootScope.lastThreeOrders = $cookieStore.get('lastThreeOrders').orders;
            // //console.log("after addUpdatelastThreeOrderArray:::",
            // $rootScope.lastThreeOrders);
        }

        function getDefaultLastThreeOrders(dataObj, unitDetails) {
            var lastThreeOrders = {
                unitId: unitDetails.id,
                orders: [dataObj.generatedOrderId]
            };
            // //console.log("inside default object method");
            return lastThreeOrders;
        }

        function getMassOffers() {
            posAPI.allUrl('/', service.restUrls.offers.allOffers).post(service.GetRequest("")).then(function (response) {
                if (response != undefined) {
                    service.offers = response.plain();
                    service.massOfferData = {
                        validate: false,
                        minimumAmount: 1000000,
                    }
                    if (service.offers != null && service.offers.length > 0) {
                        for (var i in service.offers) {
                            if (service.offers[i].offer.type == 'OFFER_WITH_FREE_ITEM_STRATEGY') {
                                service.massOfferData.validate = true;
                                if (service.offers[i].offer.minValue < service.massOfferData.minimumAmount) {
                                    service.massOfferData.minimumAmount = service.offers[i].offer.minValue;
                                    service.massOfferData.productId = service.offers[i].offer.offerWithFreeItem.productId;
                                    service.massOfferData.quantity = service.offers[i].offer.offerWithFreeItem.quantity;
                                }
                            }
                        }
                    }
                    // //console.log(service.offers);
                }
            }, function (err) {
                // console.log("could not fetch offers");
            });
        }

        function getCreditAccounts() {
            posAPI.allUrl('/', service.restUrls.posMetaData.creditAccounts).post(service.GetRequest("")).then(
                function (response) {
                    if (response != undefined) {
                        service.creditAccounts = response.plain();
                    }
                }, function (err) {
                    // console.log(err);
                });
        }

        function getUnitList(callback) {
            if (service.outletList == null || service.outletList.length == 0) {
                posAPI.allUrl('/', service.restUrls.unitMetaData.activeUnits).customGET("", {category: "CAFE"})
                    .then(function (response) {
                        if (service.outletList.length == 0) {
                            service.outletList = response.plain();
                        } else {
                            angular.forEach(response.plain(), function (v) {
                                service.outletList.push(v);
                            });
                        }
                        if (typeof callback == "function") {
                            callback(service.outletList);
                        }
                    }, function (err) {
                        service.myAlert(err.data.errorMessage);
                    });
            } else {
                if (typeof callback == "function") {
                    callback(service.outletList);
                }
            }
        }

        function validate() {
            if (angular.isUndefined($rootScope.globals) || service.transactionMetadata == null) {
                $location.url("/login");
            }
        }

        function backToCover() {
            if (service.isCOD()) {
                $location.url('/CODCover');
            } else {
                $location.url('/cover');
            }
        };

        function getCurrentDate(format) {
            var date = new Date();
            if (format == 'yyyy-mm-dd') {
                return date.getFullYear() + "-" + ("0" + date.getMonth()).slice(-2) + "-"
                    + ("0" + date.getDate()).slice(-2);
            }
        }

        function formatDate(date, format) {
            var time = new Date(date);
            var yyyy = time.getFullYear();
            var M = time.getMonth() + 1;
            var d = time.getDate();
            var MM = M;
            var dd = d;
            var hh = time.getHours();
            var mm = time.getMinutes();
            var ss = time.getSeconds();
            if (M < 10) {
                MM = "0" + M;
            }
            if (d < 10) {
                dd = "0" + d;
            }
            if (format == "yyyy-MM-dd") {
                return yyyy + "-" + MM + "-" + dd;
            }
            if (format == "dd/MM/yyyy") {
                return dd + "/" + MM + "/" + yyyy;
            }
            if (format == "dd-MM-yyyy-hh-mm-ss") {
                return dd + "-" + MM + "-" + yyyy + "-" + hh + "-" + mm + "-" + ss;
            }
            if (format == "yyyy-MM-dd hh:mm:ss") {
                return yyyy + "-" + MM + "-" + dd + " " + hh + ":" + mm + ":" + ss;
            }
            if (format == "dd-MM-yyyy") {
                return dd + "-" + MM + "-" + yyyy;
            }
        }
  
        
        function sendTableOrderStart(customerObj) {
            $rootScope.orderStartCount = $rootScope.orderStartCount==undefined ? 0 : $rootScope.orderStartCount;
            if($rootScope.csTimeout != null){
            	// required to close previous loop
            	$interval.cancel($rootScope.csTimeout);
            	$rootScope.csTimeout = null;
            }
            $rootScope.csTimeout = $interval(function () {
                if($rootScope.csScreenTimer == undefined){
                    $rootScope.csScreenTimer = true;
                }
                if($rootScope.csScreenTimer || $rootScope.orderStartCount == 0){
                    console.log("Sending table order start message");
                    socketUtils.emitMessage({TABLE_ORDER: customerObj});
                    $rootScope.csScreenTimer = true;
                    if($rootScope.orderStartCount>5){
                        console.log("Cancelling order start interval due to five failures");
                        socketUtils.setPairingStatus(false);
                        $rootScope.csScreenTimer = false;
                        $interval.cancel($rootScope.csTimeout);
                        $rootScope.orderStartCount = 0;
                        $rootScope.$broadcast("customerScreenDown");
                    }else{
                        $rootScope.orderStartCount++;
                    }
                }else{
                    console.log("Cancelling order start interval");
                    $rootScope.csScreenTimer = false;
                    $interval.cancel($rootScope.csTimeout);
                    $rootScope.orderStartCount = 0;
                }
            }, 1000);
        }
        
        function sendOrderStart() {
            $rootScope.orderStartCount = $rootScope.orderStartCount==undefined ? 0 : $rootScope.orderStartCount;
            if($rootScope.csTimeout != null){
            	// required to close previous loop
            	$interval.cancel($rootScope.csTimeout);
            	$rootScope.csTimeout = null;
            }
            var sessionId = new Date().getTime() + "_" + service.getSubscriber().unit + "_" + service.getSubscriber().terminal;
            $rootScope.csTimeout = $interval(function () {
                if($rootScope.csScreenTimer == undefined){
                    $rootScope.csScreenTimer = true;
                }
                if($rootScope.csScreenTimer || $rootScope.orderStartCount == 0){
                    console.log("Sending order start message", service.resetCustomerSocket());
                    var orderMessage = service.resetCustomerSocket();
                    orderMessage.sessionId = sessionId;
                    socketUtils.emitMessage({ORDER_START: orderMessage});
                    $rootScope.csScreenTimer = true;
                    if($rootScope.orderStartCount>5){
                        //bootbox.alert("Not able to open customer screen for you. Please check if customer screen is logged in!");
                        console.log("Cancelling order start interval due to five failures");
                        socketUtils.setPairingStatus(false);
                        $rootScope.csScreenTimer = false;
                        $interval.cancel($rootScope.csTimeout);
                        $rootScope.orderStartCount = 0;
                        $rootScope.$broadcast("customerScreenDown");
                    }else{
                        $rootScope.orderStartCount++;
                    }
                }else{
                    console.log("Cancelling order start interval");
                    $rootScope.csScreenTimer = false;
                    $interval.cancel($rootScope.csTimeout);
                    $rootScope.orderStartCount = 0;
                }
            }, 1000);
        }
        
    }
   

    function yesterday() {
        var currentDate = new Date();
        // //console.log(currentDate);
        currentDate = currentDate.setDate(currentDate.getDate() - 1);
        return currentDate;
    }

    
    function getTransactionMetadata() {
        /*
         * if(this.isEmptyObject(this.transactionMetadata)){ if (typeof(Storage)
         * !== "undefined"){ this.transactionMetadata =
         * JSON.parse(localStorage.getItem("transactionMetadata")); }else{
         * this.transactionMetadata = {}; } }
         */
        return this.transactionMetadata;
    }

    function getUnitDetails() {
        /*
         * if(this.isEmptyObject(this.unitDetails)){ if (typeof(Storage) !==
         * "undefined"){ this.unitDetails =
         * JSON.parse(localStorage.getItem("unitDetails")); }else{
         * this.unitDetails = {}; } }
         */
        return this.unitDetails;
    }

    function hasLiveInventory() {
        /*
         * if(this.isEmptyObject(this.unitDetails)){ if (typeof(Storage) !==
         * "undefined"){ this.unitDetails =
         * JSON.parse(localStorage.getItem("unitDetails")); }else{
         * this.unitDetails = {}; } }
         */
        return this.unitDetails.liveInventoryEnabled;
    }

    function hasTableService(){
    	return this.unitDetails.tableService;
    }
    
    function hasExtendedTableService(){
    	return this.unitDetails.tableService && this.unitDetails.tableServiceType > 0;
    }
  
    function tableServiceType(){
    	return this.unitDetails.tableServiceType;
    }

    function JSONToCSVConvertor(JSONData, ReportTitle, ShowLabel) {

        // If JSONData is not an object then JSON.parse will parse the JSON
        // string in an Object
        var arrData = typeof JSONData != 'object' ? JSON.parse(JSONData) : JSONData;
        var CSV = '';
        // This condition will generate the Label/Header
        if (ShowLabel) {
            var row = "";

            // This loop will extract the label from 1st index of on array
            for (var index in arrData[0]) {
                // Now convert each value to string and comma-seprated
                row += index + ',';
            }
            row = row.slice(0, -1);
            // append Label row with line break
            CSV += row + '\r\n';
        }

        // 1st loop is to extract each row
        for (var i = 0; i < arrData.length; i++) {
            var row = "";
            // 2nd loop will extract each column and convert it in string
            // comma-seprated
            for (var index in arrData[i]) {
                row += '"' + arrData[i][index] + '",';
            }
            row.slice(0, row.length - 1);
            // add a line break after each row
            CSV += row + '\r\n';
        }

        if (CSV == '') {
            alert("Invalid data");
            return;
        }

        // this trick will generate a temp "a" tag
        var link = document.createElement("a");
        link.id = "lnkDwnldLnk";

        // this part will append the anchor tag and remove it after automatic
        // click
        document.body.appendChild(link);

        var csv = CSV;
        var blob = new Blob([csv], {
            type: 'text/csv'
        });
        var csvUrl = window.webkitURL.createObjectURL(blob);
        var filename = ReportTitle + '.csv';
        $("#lnkDwnldLnk").attr({
            'download': filename,
            'href': csvUrl
        });

        $('#lnkDwnldLnk')[0].click();
        document.body.removeChild(link);
    }

    function covertToSentenceCase(text) {
        var result = text.replace(/([A-Z])/g, " $1");
        return result.charAt(0).toUpperCase() + result.slice(1);
    }

    function JSONToCSVConvertorLabelFromatter(JSONData, ReportTitle, ShowLabel) {
        // If JSONData is not an object then JSON.parse will parse the JSON
        // string in an Object
        var arrData = typeof JSONData != 'object' ? JSON.parse(JSONData) : JSONData;
        var CSV = '';
        // This condition will generate the Label/Header
        if (ShowLabel) {
            var row = "";

            // This loop will extract the label from 1st index of on array
            for (var index in arrData[0]) {
                // Now convert each value to string and comma-seprated
                row += covertToSentenceCase(index) + ',';
            }
            row = row.slice(0, -1);
            // append Label row with line break
            CSV += row + '\r\n';
        }

        // 1st loop is to extract each row
        for (var i = 0; i < arrData.length; i++) {
            var row = "";
            // 2nd loop will extract each column and convert it in string
            // comma-seprated
            for (var index in arrData[i]) {
                row += '"' + arrData[i][index] + '",';
            }
            row.slice(0, row.length - 1);
            // add a line break after each row
            CSV += row + '\r\n';
        }

        if (CSV == '') {
            alert("Invalid data");
            return;
        }

        // this trick will generate a temp "a" tag
        var link = document.createElement("a");
        link.id = "lnkDwnldLnk";

        // this part will append the anchor tag and remove it after automatic
        // click
        document.body.appendChild(link);

        var csv = CSV;
        var blob = new Blob([csv], {
            type: 'text/csv'
        });
        var csvUrl = window.webkitURL.createObjectURL(blob);
        var filename = ReportTitle + '.csv';
        $("#lnkDwnldLnk").attr({
            'download': filename,
            'href': csvUrl
        });

        $('#lnkDwnldLnk')[0].click();
        document.body.removeChild(link);
    }

    function getAutoConfigData() {
        if (this.autoConfigData == null || this.isEmptyObject(this.autoConfigData)) {
            this.autoConfigData = JSON.parse(localStorage.getItem("autoConfigData"));
        }
        return this.autoConfigData;
    }

    function setAutoConfigData(autoConfigData) {
        this.autoConfigData = autoConfigData;
        localStorage.setItem("autoConfigData", JSON.stringify(autoConfigData));
    }

    function removeAutoConfigData() {
        this.autoConfigData = null;
        localStorage.removeItem("autoConfigData");
    }

    function getCoveredCustomerContact(contactNumber) {
        return "*******" + contactNumber.substring(7, contactNumber.length);
    }

    function getSelectedAddress() {
        var addr = null;
        for (var i = 0; i < this.CSObj.addresses.length; i++) {
            if (this.CSObj.addresses[i].isSelectedAddress) {
                addr = this.CSObj.addresses[i];
            }
        }
        return addr;
    }

    function getSelectedUnitIdName() {
        var outletObj = [{
            id: this.outlet.pri_unitId,
            name: this.outlet.pri_name
        }, {
            id: this.outlet.sec_unitId,
            name: this.outlet.sec_name
        }, {
            id: this.outlet.ter_unitId,
            name: this.outlet.ter_name
        }];
        return outletObj[this.outlet.selectedId - 1];
    }

    function getOrderModeFromSource(source) {
        var sourceModeMap = {
            COD: "DELIVERY",
            CAFE: "DINE_IN",
            TAKE_AWAY: "TAKE_AWAY"
        };
        return sourceModeMap[source];
    }

    /**
     * for sorting array of objects on any given field
     */
    function sortBy(array, key) {
        array.sort(function (a, b) {
            if (a[key] < b[key]) {
                return -1;
            } else if (a[key] > b[key]) {
                return 1;
            }
            return 0;
        });
    }

    function setCurrentTransactionGiftCards(cards) {
        this.currentTransactionGiftCards = cards;
    }

    function getCurrentTransactionGiftCards() {
        return this.currentTransactionGiftCards;
    }

    function getProductsOfCategory(type) {
        return this.productMap[type];
    }

    function arrangeProducts(products, categoryList, productMap) {
        for (var i = 0; i < categoryList.length; i++) {
            productMap[categoryList[i].detail.id] = [];
        }
        for (var i = 0; i < products.length; i++) {
            if (products[i].type != 7) {
                if (products[i].subType != 1202) {
                    if(productMap[products[i].type] != null) {
                        productMap[products[i].type].push(products[i]);
                    }
                }
            } else {
                if (products[i].subType == 701 || products[i].subType == 704) {
                    productMap[701].push(products[i]);
                } else {
                    productMap[702].push(products[i]);
                }
            }

        }
        //console.log("productMap",productMap);
    }

    function arrangeProductCategory(categories) {
        //console.log("categories",categories);
        var productCategory = [];
        for (var i = 0; i < categories.length; i++) {
            if(categories[i].detail.id != 43) {
                if (categories[i].detail.id != 7) {
                    productCategory.push(categories[i]);
                } else {
                    var food1 = {
                        detail: {id: 701, name: "MLS/SWCH", code: "Meals", shortCode: null, type: "CATEGORY"},
                        content: [{id: 701, name: "Meals"}, {id: 704, name: "Sandwich"}]
                    }
                    var food2 = {
                        detail: {id: 702, name: "NST/BRKFST", code: "Nasta", shortCode: null, type: "CATEGORY"},
                        content: [{id: 702, name: "Breakfast"}, {id: 703, name: "Nashta"}]
                    }
                    productCategory.push(food1);
                    productCategory.push(food2);
                }
            }
        }
        this.categoryList = productCategory;
    }

})();
