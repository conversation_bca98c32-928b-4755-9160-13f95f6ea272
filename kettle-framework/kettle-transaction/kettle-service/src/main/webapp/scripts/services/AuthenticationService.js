/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

(function () {
    'use strict';

    angular
        .module('posApp')
        .factory('AuthenticationService', AuthenticationService);

    AuthenticationService.$inject = ['$http', '$interval', '$cookieStore', '$rootScope', '$timeout', 'UserService', 'posAPI', 'AppUtil', '$location','AuthService','AssemblyService','trackingService'];
    function AuthenticationService($http, $interval, $cookieStore, $rootScope, $timeout, UserService, posAPI, AppUtil, $location,AuthService,AssemblyService, trackingService) {
        var service = {};

        service.Login = Login;
        service.Verify = Verify;
        service.Logout = Logout;
        service.SetCredentials = SetCredentials;
        service.PasscodeReset = PasscodeReset;
        service.ClearCredentials = ClearCredentials;
        //service.failedMessages = failedMessages;
        service.isAssembly = isAssembly;
        return service;


        function Login(userId, password, unitId, terminalId, screenType, callback) {
            /*for authentication
             -------------------*/
        	if( screenType != undefined && isAssembly(screenType)){
        		terminalId = 0; // in case of assembly since there is only one screen
        	}
        	var userObj =  createUser(unitId, userId, password, terminalId, screenType);
            ////console.log(userObj);  AppUtil.restUrls.users.login
            posAPI.allUrl('/',AppUtil.restUrls.users.login).post(userObj)
                .then(function (response) {
                    localStorage.setItem("serverTime",JSON.stringify(setServerTimeDifference(response.serverTime)));
                callback(response);
            }, function (err) {
                callback(err);
                ////console.log(err);
            });
        }

        function setServerTimeDifference(serverTime){
            var currentTime= new Date();
            var serverHour=new Date(serverTime).getHours()*60*60;
            var serverMin=new Date(serverTime).getMinutes()*60;
            var serverSecond= new Date(serverTime).getSeconds();
            var timeDifference= (serverHour+serverMin+serverSecond)-((currentTime.getHours()*60*60)+(currentTime.getMinutes()*60)+currentTime.getSeconds());
            return timeDifference/3600;
        }

        function Verify(userId, password, unitId, terminalId, screenType, callback) {
            /*for authentication
             -------------------*/
        	if( screenType != undefined && isAssembly(screenType)){
        		terminalId = 0; // in case of assembly since there is only one screen
        	}

        	var userObj =  createUser(unitId, userId, password, terminalId, screenType);

            ////console.log(userObj);  AppUtil.restUrls.users.login
            posAPI.allUrl('/',AppUtil.restUrls.users.verify).post(userObj)
                .then(function (response) {
                callback(response);
            }, function (err) {
                callback(err);
            });
        }
        
        function createUser (unitId,userId,password,terminalId, screenType){
            var userObj = {
                    unitId: unitId,
                    userId: userId,
                    password: password,
                    terminalId: terminalId,
                    screenType:screenType,
                    macAddress:  $location.search().mac,
                    application: "KETTLE_SERVICE"
                };
            return userObj;
        }
        
        function PasscodeReset(userId, password, unitId, newPassword, callback) {

            var userObj = {
                unitId: unitId,
                userId: userId,
                password: password,
                newPassword: newPassword
            };

            posAPI.allUrl('/',AppUtil.restUrls.users.changePassCode).post(JSON.stringify(userObj))
                .then(function (response) {
                callback(response);
            }, function (err) {
                AppUtil.myAlert(err.data.errorMessage);
            });

        }

       /* function failedMessages(screenType){
        	if(isAssembly(screenType)){
            	$interval(unitFailedMessagesRequest,60000); //get failed messages if there exists any
            }
        }*/
        
        function isAssembly(screenType){
        	return screenType == "ASSEMBLY";
        }
        
        
        
        function Logout(callback) {

            posAPI.allUrl('/',AppUtil.restUrls.users.logout).post($rootScope.globals.currentUser)
                .then(function (response) {
                ////console.log('logged out');
                callback(response);
            }, function (err) {
                AppUtil.myAlert(err.data.errorMessage);
            });
        }

        function SetCredentials(userId, unitId, sessionKey, userName, designation,terminalId,screenType,unitFamily,jwtToken, application,googleMerchantId) {
        	////console.log("Received scrrenType::::::"+screenType);
        	if(isAssembly(screenType)){
        		terminalId = 0; // in case of assembly since there is only one screen
        	}
        	$rootScope.globals = {
                currentUser: {
                    userId: userId,
                    unitId: unitId,
                    terminalId: terminalId,
                    sessionKeyId: sessionKey,
                    userName: userName,
                    designation: designation,
                    screenType:screenType,
                    unitFamily:unitFamily,
                    issuer: application,
                    jwtToken:jwtToken,
                    googleMerchantId:googleMerchantId
                }
            };

            $cookieStore.put('globals', $rootScope.globals);
            AppUtil.setUnitFamily(unitFamily);
            AuthService.setAuthorization(jwtToken);
            AppUtil.refreshRedeemLock();

            //$interval(unitPingRequest,60000); // once the user has successfully logged in  we start ping service
        }
        
        /*function unitPingRequest(){

        	if($cookieStore.get('globals')!=undefined && $rootScope.user!=undefined){
        		
        		var unitId = $rootScope.globals.currentUser.unitId;
            	var terminalId = $rootScope.globals.currentUser.terminalId;
            	var screenType = $rootScope.globals.currentUser.screenType;
            	var unitObject = {unitId:unitId,terminalId:terminalId,screenType:screenType,startTime:Date.now()};
            	////console.log("unitObject");
            	////console.log(unitObject);
        		
        		
        		posAPI.allUrl('/',"health-monitor").all("unit").all("ping")
           	 	.post(unitObject)
                .then(function (response) {
                    if(!angular.isUndefined(response) && response!=null){
                        response = response.plain();
                        ////console.log(response);
                        if(unitObject.screenType=='ASSEMBLY' && response.failedMessages>0){
                            AssemblyService.refresh();
                        }
                    }
                }, function (err) {
                	//console.log("Either server is offline or your network is down");
                    //console.log(err);            	 
                });
        	}
        	
        }*/
        
        /*function unitFailedMessagesRequest(){
        	if($rootScope.globals!=undefined && $rootScope.user!=undefined){
        		var requestObj = AppUtil.GetRequest("");
        		posAPI.allUrl('/',AppUtil.restUrls.healthMonitor.unitFailed)
           	 	.post(requestObj)
                .then(function (response) {
                    if(!angular.isUndefined(response) && response!=null){
                        response = response.plain();
                        ////console.log(response);
                        if(isAssembly(requestObj.screenType) && response > 0){
                            AssemblyService.refresh();
                        }
                    }
                }, function (err) {
                	//console.log("Either server is offline or your network is down");
                    //console.log(err);            	 
                });
        	}
        	
        }*/


        function ClearCredentials() {
            $rootScope.globals = {};
            $rootScope.user = {};
            $cookieStore.remove('globals');
           /* $cookieStore.remove('lastThreeOrderArray');*/
            AppUtil.refreshRedeemLock();
            $http.defaults.headers.common.Authorization = 'Basic ';
            localStorage.removeItem("transactionMetadata");
            localStorage.removeItem("autoApplicableOfferForUnit");
            localStorage.removeItem("kettleDayCloseDone");
            localStorage.removeItem("sumoDayCloseDone");
            //localStorage.removeItem("unitDetails");
        }
    }

    // Base64 encoding service used by AuthenticationService
    var Base64 = {

        keyStr: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=',

        encode: function (input) {
            var output = "";
            var chr1, chr2, chr3 = "";
            var enc1, enc2, enc3, enc4 = "";
            var i = 0;

            do {
                chr1 = input.charCodeAt(i++);
                chr2 = input.charCodeAt(i++);
                chr3 = input.charCodeAt(i++);

                enc1 = chr1 >> 2;
                enc2 = ((chr1 & 3) << 4) | (chr2 >> 4);
                enc3 = ((chr2 & 15) << 2) | (chr3 >> 6);
                enc4 = chr3 & 63;

                if (isNaN(chr2)) {
                    enc3 = enc4 = 64;
                } else if (isNaN(chr3)) {
                    enc4 = 64;
                }

                output = output +
                    this.keyStr.charAt(enc1) +
                    this.keyStr.charAt(enc2) +
                    this.keyStr.charAt(enc3) +
                    this.keyStr.charAt(enc4);
                chr1 = chr2 = chr3 = "";
                enc1 = enc2 = enc3 = enc4 = "";
            } while (i < input.length);

            return output;
        },

        decode: function (input) {
            var output = "";
            var chr1, chr2, chr3 = "";
            var enc1, enc2, enc3, enc4 = "";
            var i = 0;

            // remove all characters that are not A-Z, a-z, 0-9, +, /, or =
            var base64test = /[^A-Za-z0-9\+\/\=]/g;
            if (base64test.exec(input)) {
                window.alert("There were invalid base64 characters in the input text.\n" +
                    "Valid base64 characters are A-Z, a-z, 0-9, '+', '/',and '='\n" +
                    "Expect errors in decoding.");
            }
            input = input.replace(/[^A-Za-z0-9\+\/\=]/g, "");

            do {
                enc1 = this.keyStr.indexOf(input.charAt(i++));
                enc2 = this.keyStr.indexOf(input.charAt(i++));
                enc3 = this.keyStr.indexOf(input.charAt(i++));
                enc4 = this.keyStr.indexOf(input.charAt(i++));

                chr1 = (enc1 << 2) | (enc2 >> 4);
                chr2 = ((enc2 & 15) << 4) | (enc3 >> 2);
                chr3 = ((enc3 & 3) << 6) | enc4;

                output = output + String.fromCharCode(chr1);

                if (enc3 != 64) {
                    output = output + String.fromCharCode(chr2);
                }
                if (enc4 != 64) {
                    output = output + String.fromCharCode(chr3);
                }

                chr1 = chr2 = chr3 = "";
                enc1 = enc2 = enc3 = enc4 = "";

            } while (i < input.length);

            return output;
        }
    };

})();
