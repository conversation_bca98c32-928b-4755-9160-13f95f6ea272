/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by shikhar on 11-01-2016.
 */
(function () {
    'use strict';

    angular.module('posApp').factory('APIJson', APIJson);

    function APIJson() {

        var analyticsUrl = window.location.origin+"/";//window.analyticsUrl;
        var scmUrl = window.location.origin+"/";//window.scmUrl;
        var masterUrl = "http://localhost:9696/";//window.masterUrl;
        var crmUrl = window.location.origin+"/";//window.crmUrl;
        var kettleUrl = window.location.origin+"/";//window.kettleUrl;
        var channelPartnerUrl = window.location.origin+"/";//window.channelPartnerUrl;
        var formsUrl = window.location.origin+"/";//window.formsUrl;
        var inventoryUrl = window.location.origin+"/";//window.invent
            var crmServiceUrl = URL+"/";//window.crmServiceUrl;
        if (window.location.origin.indexOf("internal.chaayos.com") >= 0) {
            inventoryUrl= "https://internal.chaayos.com/";
            crmServiceUrl= "https://internal.chaayos.com/";
        }else if(window.location.origin.indexOf("relax.chaayos.com") >= 0){
            inventoryUrl=  "https://relax.chaayos.com/";
            crmServiceUrl=  "https://relax.chaayos.com/";
        } else if (window.location.origin.indexOf("stage") > -1) {
            var pivotUrl  =  "http://stage.kettle.chaayos.com:8081/";
            inventoryUrl= "http://stage.kettle.chaayos.com:8081/";
            crmServiceUrl= "http://stage.kettle.chaayos.com:8081/";
        } else if (window.location.origin.indexOf("dev") > -1) {
            var pivotUrl  =  "http://dev.kettle.chaayos.com:9696/";
            inventoryUrl= "http://dev.kettle.chaayos.com:8081/";
            crmServiceUrl= "http://dev.kettle.chaayos.com:8081/";
        } else if (window.location.origin.indexOf("localhost") > -1) {
            var pivotUrl  =  "http://localhost:9696/";
            inventoryUrl= "http://localhost:8080/";
            crmServiceUrl= "http://localhost:8081/";
        }
        var pivotUrl =  window.location.origin + "/";
        var knockUrl = window.location.origin+"/";
         if (window.location.origin.indexOf("internal.chaayos.com") >= 0) {
                        var kettleUIV2Url  = "https://internal.chaayos.com/";
             }else if(window.location.origin.indexOf("relax.chaayos.com") >= 0){
                        var kettleUIV2Url =  "https://relax.chaayos.com/";
             }else if (window.location.origin.indexOf("stage") > -1) {
                        var kettleUIV2Url  =  "http://stage.kettle.chaayos.com:9595/"
             } else if (window.location.origin.indexOf("dev") > -1) {
                        var kettleUIV2Url  =  "http://dev.kettle.chaayos.com:9595/"
             } else if (window.location.origin.indexOf("localhost") > -1) {
                        var kettleUIV2Url  =  "http://localhost:9595/"
             }




        var SEPARATOR = "/";
        var POSTFIX = "/rest/v1/";
        var TRANSACTION_SERVICE_CONTEXT = "http://localhost:9595/" + "kettle-service/rest/v1/";
        var TRANSACTION_SERVICE_CONTEXT_V2 = kettleUrl + "kettle-service/rest/v2/";
        var MASTER_SERVICE_CONTEXT = masterUrl + "master-service/rest/v1/";
        var OFFER_SERVICE_CONTEXT = masterUrl + "offer-service/rest/v1/";
        var CRM_SERVICE_CONTEXT = "http://stage.kettle.chaayos.com:8080/" + "kettle-crm/rest/v1/";
        var CRM_SERVICE_V2_CONTEXT = "http://stage.kettle.chaayos.com:8080/" + "crm-service/rest/v2/";
        var CHANNEL_PARTNER_SERVICE_CONTEXT = "http://stage.kettle.chaayos.com:8080/" + "channel-partner/rest/v1/";
        var FORMS_FRONT_END_CONTEXT = formsUrl + "forms-ui/";
        var FORMS_ROOT_CONTEXT = formsUrl + "forms-service/rest/v1/";
        var INVENTORY_BASE_URL= inventoryUrl+"kettle-inventory";
        var INVENTORY_SERVICE_CONTEXT = inventoryUrl + "kettle-inventory/rest/v1/";
        var ANALYTICS_SERVICE = analyticsUrl + "kettle-analytics/rest/v1/";
        var SCM_SERVICE = "http://stage.kettle.chaayos.com:8080/" + "scm-service/rest/v1/";
        var REPORT_SERVICE = kettleUrl + "report-service/rest/v1/";
        var KNOCK_SERVICE = knockUrl + "knock-service/rest/";
        var PIVOT__SERVICE = pivotUrl + "pivot/";
        var NEO_SERVICE_CONTEXT = kettleUrl + "neo-service/rest/v1/";
        var DINE_IN_RESOURCE = kettleUrl + "kettle-service/rest/v2/dine-in-management";

        var ANALYTICS_SERVICES_ROOT_CONTEXT = ANALYTICS_SERVICE + "analytics" + SEPARATOR;
        var KNOCK_KETTLE_ANALYTICS_ROOT_CONTEXT = KNOCK_SERVICE + "kettle-analytics" + SEPARATOR;
        var PRINTER_ROOT_CONTEXT = TRANSACTION_SERVICE_CONTEXT + "printer" + SEPARATOR;
        var POS_METADATA_ROOT_CONTEXT = TRANSACTION_SERVICE_CONTEXT + "pos-metadata" + SEPARATOR;
        var REPORT_METADATA_ROOT_CONTEXT = REPORT_SERVICE + "report-metadata" + SEPARATOR;
        var UNIT_METADATA_ROOT_CONTEXT = MASTER_SERVICE_CONTEXT + "unit-metadata" + SEPARATOR;
        var ORDER_MANAGEMENT_ROOT_CONTEXT = TRANSACTION_SERVICE_CONTEXT + "order-management" + SEPARATOR;
        var SUBSCRIPTION_MANAGEMENT_ROOT_CONTEXT = TRANSACTION_SERVICE_CONTEXT + "subscription-management" + SEPARATOR;
        var DELIVERY_MANAGEMENT_ROOT_CONTEXT = TRANSACTION_SERVICE_CONTEXT + "delivery-management" + SEPARATOR;
        var HEALTH_MONITOR_ROOT_CONTEXT = TRANSACTION_SERVICE_CONTEXT + "health-monitor" + SEPARATOR;
        var USER_SERVICES_ROOT_CONTEXT = MASTER_SERVICE_CONTEXT + "users" + SEPARATOR;
        var USER_MANAGEMENT_SERVICES_ROOT_CONTEXT = MASTER_SERVICE_CONTEXT + "user-management" + SEPARATOR;
        var AUTHORIZATION_SERVICES_ROOT_CONTEXT = TRANSACTION_SERVICE_CONTEXT + "authorization" + SEPARATOR;
        var OFFER_MANAGEMENT_ROOT_CONTEXT = MASTER_SERVICE_CONTEXT + "offer-management" + SEPARATOR;
        var OFFER_MANAGEMENT_ROOT_CONTEXT_V1 = OFFER_SERVICE_CONTEXT + "offer-management" + SEPARATOR;
        var CUSTOMER_OFFER_MANAGEMENT_ROOT_CONTEXT = TRANSACTION_SERVICE_CONTEXT + "customer-offer-management" + SEPARATOR;
        var CASH_MANAGEMENT_SERVICES_ROOT_CONTEXT = TRANSACTION_SERVICE_CONTEXT + "cash-management" + SEPARATOR;
        var RULES_MANAGEMENT_SERVICES_ROOT_CONTEXT = TRANSACTION_SERVICE_CONTEXT + "rules" + SEPARATOR;
        var PAYMENT_MANAGEMENT_CONTEXT = TRANSACTION_SERVICE_CONTEXT_V2 + "payment-management/";
        var CUSTOMER_FAV_CHAI_MANAGEMENT_ROOT_CONTEXT=TRANSACTION_SERVICE_CONTEXT+"customer-fav-chai-management"+SEPARATOR;

        var MASTER_CACHE_MANAGEMENT_ROOT_CONTEXT = MASTER_SERVICE_CONTEXT + "master-cache-management" + SEPARATOR;
        var KETTLE_METADATA_MANAGEMENT_ROOT_CONTEXT = TRANSACTION_SERVICE_CONTEXT + "metadata-management/";

        //CUSTOMER_SERVICES
        var CRM_SERVICES_ROOT_CONTEXT = CRM_SERVICE_CONTEXT + "crm" + SEPARATOR;
        var CUSTOMER_SERVICES_ROOT_CONTEXT = CRM_SERVICE_CONTEXT + "customer" + SEPARATOR;
        var CUSTOMER_PROFILE_SERVICES_ROOT_CONTEXT = CRM_SERVICE_CONTEXT + "customer-profile" + SEPARATOR;
        var COD_CUSTOMER_SERVICES_ROOT_CONTEXT = CRM_SERVICE_CONTEXT + "cod-customer" + SEPARATOR;

        //NEW CRM SERVICE
        var CRM_SERVICES_V2_ROOT_CONTEXT = CRM_SERVICE_V2_CONTEXT + "crm" + SEPARATOR;
        var SCM_STOCK_MANAGEMENT = SCM_SERVICE + "stock-management" + SEPARATOR;

        var EXPENSE_MANAGEMENT_ROOT_CONTEXT = TRANSACTION_SERVICE_CONTEXT + "expense-management" + SEPARATOR;
        var MASTER_METADATA_ROOT_CONTEXT = MASTER_SERVICE_CONTEXT + "metadata" + SEPARATOR;
        var PARTNER_ORDER_ROOT_CONTEXT = CHANNEL_PARTNER_SERVICE_CONTEXT + "partner-order/";
        var PARTNER_METADATA_MANAGEMENT_CONTEXT = CHANNEL_PARTNER_SERVICE_CONTEXT + "partner-metadata-management/";
        var INVENTORY_DATA_CONTEXT = INVENTORY_SERVICE_CONTEXT + "inventory-data/";
        var INVENTORY_DATA_CONTEXT2 =  "inventory-data/";
        var BRAND_METADATA_ROOT_CONTEXT = MASTER_SERVICE_CONTEXT + "brand-metadata/";
        var CAFE_LOOKUP_MANAGEMENT_CONTEXT= CHANNEL_PARTNER_SERVICE_CONTEXT+"lookUp/";
        var SWIGGY_CONTEXT = CHANNEL_PARTNER_SERVICE_CONTEXT+"swiggy/"

        //PAYTM_EDC_SERVICE
        var EDC_PAYMENT_ROOT_CONTEXT = PIVOT__SERVICE + "payment/";
        var EXTERNAL_ORDER_MANAGEMENT_CONTEXT = REPORT_SERVICE + "external" + SEPARATOR;
        var CLAIM_MANAGEMENT_CONTEXT = FORMS_ROOT_CONTEXT + "claim-management/"

        var service = {};
        service.DEV_PARSE_LIST = "DEV_COD_Area_List";
        service.PROD_PARSE_LIST = "COD_Area_List";
        service.POST_FIX = POSTFIX;

        service.urls = {
            analyticsData: {
                //unitSales: ANALYTICS_SERVICES_ROOT_CONTEXT + "unit/sales/new",
               // targetAllData: ANALYTICS_SERVICES_ROOT_CONTEXT + "unit/penetration/target-all-data",
               // currentAllData: ANALYTICS_SERVICES_ROOT_CONTEXT + "unit/penetration/current-all-data",
                //unitPenetrationTarget: ANALYTICS_SERVICES_ROOT_CONTEXT + "unit/penetration/target",
               // unitPenetrationCurrent: ANALYTICS_SERVICES_ROOT_CONTEXT + "unit/penetration/current",
               // unitPenetrationTargetForDay: ANALYTICS_SERVICES_ROOT_CONTEXT + "unit/penetration/target-for-day",
                unitPenetrationCurrentForDay: ANALYTICS_SERVICES_ROOT_CONTEXT + "unit/penetration/current-for-day",
                unitPenetrationLastMonth: ANALYTICS_SERVICES_ROOT_CONTEXT + "unit/penetration/previous-month",
               // unitSalesReportData:  ANALYTICS_SERVICES_ROOT_CONTEXT + "unit/sales",
                //systemSalesReportData: ANALYTICS_SERVICES_ROOT_CONTEXT+"overall/system/sales",
                intraDaySalesData : KNOCK_KETTLE_ANALYTICS_ROOT_CONTEXT + "analytics-data"
            },
            /*rules : {
                recommend: RULES_MANAGEMENT_SERVICES_ROOT_CONTEXT
                + "recommend",
                updateRecommendation: RULES_MANAGEMENT_SERVICES_ROOT_CONTEXT
                + "recommend/update"
            },*/
            order: {
                generatedOrder: ORDER_MANAGEMENT_ROOT_CONTEXT + "order/generated-order",
                addInvoiceDetails: ORDER_MANAGEMENT_ROOT_CONTEXT + "order/add-invoice-detail",
                cancelOrder: ORDER_MANAGEMENT_ROOT_CONTEXT + "order/cancel",
                updateStatus: ORDER_MANAGEMENT_ROOT_CONTEXT + "order/updateStatus",
                paymentStatus: ORDER_MANAGEMENT_ROOT_CONTEXT + "order/getPaymentStatus",
                createOrder: ORDER_MANAGEMENT_ROOT_CONTEXT + "order/create",
                createComplimentaryOrder: ORDER_MANAGEMENT_ROOT_CONTEXT + "wastage/create",
                createPayment: ORDER_MANAGEMENT_ROOT_CONTEXT + "payment/create",
                validateRazorPayPayment: ORDER_MANAGEMENT_ROOT_CONTEXT + "payment/razorpay/update",
                createOrderEnquiry: ORDER_MANAGEMENT_ROOT_CONTEXT + "order/enquiry/create",
                androidCreateOrder: ORDER_MANAGEMENT_ROOT_CONTEXT + "android/order/create",
                reprintOrder: ORDER_MANAGEMENT_ROOT_CONTEXT + "order/reprint-order",
                reprintOrderKOT: ORDER_MANAGEMENT_ROOT_CONTEXT + "order/reprint-order-kot",
                emailOrder: ORDER_MANAGEMENT_ROOT_CONTEXT + "order/email-order",
                orderSession: ORDER_MANAGEMENT_ROOT_CONTEXT + "order/session",
                settlementReport: ORDER_MANAGEMENT_ROOT_CONTEXT + "unit/report/settlement",
                terminalSettlementReport: ORDER_MANAGEMENT_ROOT_CONTEXT + "unit/report/settlement/terminal",
                consumptionReport: ORDER_MANAGEMENT_ROOT_CONTEXT + "unit/report/consumption/item",
                ordersForDay: ORDER_MANAGEMENT_ROOT_CONTEXT + "unit/orders",
                ordersForDayPageable: ORDER_MANAGEMENT_ROOT_CONTEXT + "unit/orders/v1",
                deliveryOrdersForDay: ORDER_MANAGEMENT_ROOT_CONTEXT + "unit/delivery/orders",
                ordersInLine: ORDER_MANAGEMENT_ROOT_CONTEXT + "unit/orders-in-line",
                codOrders: ORDER_MANAGEMENT_ROOT_CONTEXT + "cod/all-orders",
                newOrdersForUnit: ORDER_MANAGEMENT_ROOT_CONTEXT + "unit/newOrders",
                acknowledgeOrder: ORDER_MANAGEMENT_ROOT_CONTEXT + "order/acknowledge",
                resetUndelivered: ORDER_MANAGEMENT_ROOT_CONTEXT + "reset/undelivered",
                takeAwayOrders: ORDER_MANAGEMENT_ROOT_CONTEXT + "takeaway/orders",
                openDeliveryOrders: ORDER_MANAGEMENT_ROOT_CONTEXT + "order/open-delivery",
                calculateConsumption: ORDER_MANAGEMENT_ROOT_CONTEXT + "order/calculateConsumption",
                calculateAllConsumption: ORDER_MANAGEMENT_ROOT_CONTEXT + "order/calculateAllConsumption",
                getGiftCard: ORDER_MANAGEMENT_ROOT_CONTEXT + "get-card",
                getGiftCards: ORDER_MANAGEMENT_ROOT_CONTEXT + "get-cards",

                editSettlement: ORDER_MANAGEMENT_ROOT_CONTEXT + "order/change",
                validateGiftCardsInOrder: ORDER_MANAGEMENT_ROOT_CONTEXT + "order/validateGiftCards",
                getCustomerCardInfo: ORDER_MANAGEMENT_ROOT_CONTEXT + "get-cash-card-amount",
                getChaayosCash: ORDER_MANAGEMENT_ROOT_CONTEXT + "get-chaayos-cash-amount",
                getCustomerCardOffer: ORDER_MANAGEMENT_ROOT_CONTEXT + "get-cash-card-offer",
                empAllowancelimit: ORDER_MANAGEMENT_ROOT_CONTEXT + "user/allowance-limit",
                employeeMealOrdersForUser: ORDER_MANAGEMENT_ROOT_CONTEXT + "unit/orders/employee-meal",
                getOrdersOfRider: ORDER_MANAGEMENT_ROOT_CONTEXT + "get-sdp-order-detail",
                resetOrdersOfRider: ORDER_MANAGEMENT_ROOT_CONTEXT + "reset-sdp-order-detail",
                outOfDeliveryNotification: ORDER_MANAGEMENT_ROOT_CONTEXT + "ood/notification",
                orderTransitionDetail: ORDER_MANAGEMENT_ROOT_CONTEXT + "order/transition",
                partnerOrder: ORDER_MANAGEMENT_ROOT_CONTEXT + "order/partner-order",
                getExternalPartnerDetail : ORDER_MANAGEMENT_ROOT_CONTEXT + "external-partner-detail",
                getUnitTables : ORDER_MANAGEMENT_ROOT_CONTEXT + "unit-table-status",
                getTableSummary : ORDER_MANAGEMENT_ROOT_CONTEXT + "unit-table-summary",
                reserveTable : ORDER_MANAGEMENT_ROOT_CONTEXT + "reserve-table",
                riderHealthCheck:ORDER_MANAGEMENT_ROOT_CONTEXT +"rider-health-check",
                closeTable : ORDER_MANAGEMENT_ROOT_CONTEXT + "close-table",
                tableCheckOut : ORDER_MANAGEMENT_ROOT_CONTEXT + "table-checkout",
                reprintSettlementReceipt: ORDER_MANAGEMENT_ROOT_CONTEXT + "order/reprint-settlement-receipt",
                changeTable: ORDER_MANAGEMENT_ROOT_CONTEXT + "change-table",
                getPaymentStatus: ORDER_MANAGEMENT_ROOT_CONTEXT + "order/paymentStatus",
                getIngenicoStatus: TRANSACTION_SERVICE_CONTEXT_V2 + "payment-management/payment/ingenico/paymentStatus",
                checkPaymentStatus: TRANSACTION_SERVICE_CONTEXT_V2 + "payment-management/payment/paytm/kiosk/qr/status",
                markTransactionCancel: TRANSACTION_SERVICE_CONTEXT_V2 + "payment-management/payment/transaction/failed",
                generateGpayStatus: TRANSACTION_SERVICE_CONTEXT_V2 + "payment-management/payment/gpay/qr/status",
                removeFromCache: ORDER_MANAGEMENT_ROOT_CONTEXT + "removeFromCache",
                checkFreeChaiDelivery: ORDER_MANAGEMENT_ROOT_CONTEXT + "check-free-chai-delivery",
                sendFreeChaiDeliverySms: ORDER_MANAGEMENT_ROOT_CONTEXT + "send-free-chai-delivery/",
                walletSuggestion :ORDER_MANAGEMENT_ROOT_CONTEXT + "save-events/wallet/suggestion",
                clearOrderInfoUnitCache: ORDER_MANAGEMENT_ROOT_CONTEXT + "clearOrderInfoUnitCache",
                createServiceChargeRefund: ORDER_MANAGEMENT_ROOT_CONTEXT + "service-charge/refund",
                getServiceChargeRefundDetails: ORDER_MANAGEMENT_ROOT_CONTEXT + "service-charge/refund/get",
                getRejectionReasonsZomato: CHANNEL_PARTNER_SERVICE_CONTEXT + "zomato/get-rejection-reasons",
                rejectOrderZomato: CHANNEL_PARTNER_SERVICE_CONTEXT + "zomato/order-reject",
                getRejectionReasonsSwiggy: CHANNEL_PARTNER_SERVICE_CONTEXT + "swiggy/get-rejection-reasons",
                rejectOrderSwiggy: CHANNEL_PARTNER_SERVICE_CONTEXT + "swiggy/order-reject",
            },
            subscription: {
                createSubscription: SUBSCRIPTION_MANAGEMENT_ROOT_CONTEXT + "subscription/create",
                editSubscription: SUBSCRIPTION_MANAGEMENT_ROOT_CONTEXT + "subscription/update",
                getCustomerSubscriptions: SUBSCRIPTION_MANAGEMENT_ROOT_CONTEXT + "subscription/customer",
                cancelSubscription: SUBSCRIPTION_MANAGEMENT_ROOT_CONTEXT + "subscription/cancel",
                getCustomerSubscriptionOrders: SUBSCRIPTION_MANAGEMENT_ROOT_CONTEXT + "subscription/order/customer",
                getSubscriptionOrdersForUnit: SUBSCRIPTION_MANAGEMENT_ROOT_CONTEXT + "subscription/order/unit",
                getSubscriptionStatusEvents: SUBSCRIPTION_MANAGEMENT_ROOT_CONTEXT + "subscription/statusEvents",
                cancelSubscriptionHoldEvent: SUBSCRIPTION_MANAGEMENT_ROOT_CONTEXT + "subscription/hold/cancel",
                createSubscriptionHoldEvent: SUBSCRIPTION_MANAGEMENT_ROOT_CONTEXT + "subscription/hold/create",
                updateSubscriptionHoldEvent: SUBSCRIPTION_MANAGEMENT_ROOT_CONTEXT + "subscription/hold/update",
                updateSubscriptionCancelEvent: SUBSCRIPTION_MANAGEMENT_ROOT_CONTEXT + "subscription/cancel/update",
                updateSubscriptionEvent: SUBSCRIPTION_MANAGEMENT_ROOT_CONTEXT + "subscription/event/update"
            },
            favChai:{
                saveMyChai:CUSTOMER_FAV_CHAI_MANAGEMENT_ROOT_CONTEXT+"save-my-fav-chai",
                customerActiveFavChaiMappings:CUSTOMER_FAV_CHAI_MANAGEMENT_ROOT_CONTEXT+"get-customer-active-fav-chai-mappings"
            },
            unitMetaData: {
                metaData: UNIT_METADATA_ROOT_CONTEXT + "metadata",
                allUnits: UNIT_METADATA_ROOT_CONTEXT + "all-units",
                units: UNIT_METADATA_ROOT_CONTEXT + "all-units",
                allUnitsList: UNIT_METADATA_ROOT_CONTEXT + "all-units-list",
                activeUnits: UNIT_METADATA_ROOT_CONTEXT + "all-active-units",
                unit: UNIT_METADATA_ROOT_CONTEXT + "unit",
                unitData:UNIT_METADATA_ROOT_CONTEXT + "unit-data",
                unitProducts: UNIT_METADATA_ROOT_CONTEXT + "unit-products",
                checkUnitProductRecipes: UNIT_METADATA_ROOT_CONTEXT + "check-product-recipes",
                productProfile: UNIT_METADATA_ROOT_CONTEXT + "region/products",
                brandPartnerProductProfile: UNIT_METADATA_ROOT_CONTEXT + "brand/partner/products",
                allTAUnits: UNIT_METADATA_ROOT_CONTEXT + "takeaway-units",
                addUnit: UNIT_METADATA_ROOT_CONTEXT + "unit/add",
                updateUnit: UNIT_METADATA_ROOT_CONTEXT + "unit/update",
                activateUnit: UNIT_METADATA_ROOT_CONTEXT + "unit/activate",
                deactivateUnit: UNIT_METADATA_ROOT_CONTEXT + "unit/deactivate",
                departments: UNIT_METADATA_ROOT_CONTEXT + "departmments",
                divisions: UNIT_METADATA_ROOT_CONTEXT + "divisions",
                deliveryPartners: UNIT_METADATA_ROOT_CONTEXT + "delivery-partners",
                regions: UNIT_METADATA_ROOT_CONTEXT + "regions",
                families: UNIT_METADATA_ROOT_CONTEXT + "families",
                taxProfiles: UNIT_METADATA_ROOT_CONTEXT + "tax-profiles",
                cancellationReasons: UNIT_METADATA_ROOT_CONTEXT + "cancellation-reasons",
                localityMappings: UNIT_METADATA_ROOT_CONTEXT + "localities",
                employeeMealProducts: UNIT_METADATA_ROOT_CONTEXT + "employee-meal-products",
                unitCities: MASTER_CACHE_MANAGEMENT_ROOT_CONTEXT + "unit-cities",
                unitCityMapping: MASTER_CACHE_MANAGEMENT_ROOT_CONTEXT + "unit-city-mapping",
                cafeRegularExpense: UNIT_METADATA_ROOT_CONTEXT + "cafe-adhoc-expense",
                //unitPartnerProductsTrimmed: UNIT_METADATA_ROOT_CONTEXT + "unit/partner/products/trimmed",
                updateHandoverDate: UNIT_METADATA_ROOT_CONTEXT + "update-handover-Date",
                unitContactData: UNIT_METADATA_ROOT_CONTEXT + "unit-contact-data"
            },
            customer: {
                lookup: CUSTOMER_SERVICES_ROOT_CONTEXT + "lookup",
                generateOTP: CUSTOMER_SERVICES_ROOT_CONTEXT + "generate/otp",
                generateComplimentaryOrderOTP: CUSTOMER_SERVICES_ROOT_CONTEXT + "generate/complimentary/order/otp",
                generateCancelOrderOTP: CUSTOMER_SERVICES_ROOT_CONTEXT + "generate/cancel/order/otp",
                verifyContact: CUSTOMER_SERVICES_ROOT_CONTEXT + "verify/signup",
                verifyOTP: CUSTOMER_SERVICES_ROOT_CONTEXT + "verify/otp",
                update: CUSTOMER_SERVICES_ROOT_CONTEXT + "update",
                updateScore: CUSTOMER_SERVICES_ROOT_CONTEXT + "update/score",
                profileLookup: CUSTOMER_SERVICES_ROOT_CONTEXT + "profile/lookup",
                logout: CUSTOMER_SERVICES_ROOT_CONTEXT + "logout",
                lookupAddress: CUSTOMER_SERVICES_ROOT_CONTEXT + "lookup/address",
                cancelFeedback: CRM_SERVICES_ROOT_CONTEXT + "feedback/cancel",
                getNpsForCustomerDetail: CUSTOMER_SERVICES_ROOT_CONTEXT + "nps-detail-require",
                getFeedbackForCustomerDetail: CUSTOMER_SERVICES_ROOT_CONTEXT + "feedback-detail-require",
                customerVisit: CUSTOMER_SERVICES_ROOT_CONTEXT + "customer-visit-detail",
                recommendedProduct:CUSTOMER_SERVICES_ROOT_CONTEXT+"customer-recommendation"
            },
            delivery: {
                manualDelivery: DELIVERY_MANAGEMENT_ROOT_CONTEXT + "create-manual",
                cancel: DELIVERY_MANAGEMENT_ROOT_CONTEXT + "cancel",
                details: DELIVERY_MANAGEMENT_ROOT_CONTEXT + "details",
                defaultDelivery: DELIVERY_MANAGEMENT_ROOT_CONTEXT + "default-delivery",
                resendSMS: DELIVERY_MANAGEMENT_ROOT_CONTEXT + "resend-sms"
            },
            authorization: {
                validate: AUTHORIZATION_SERVICES_ROOT_CONTEXT + "validate"
            },
            customerOffers: {
                availedOffer: {
                    customer: CUSTOMER_OFFER_MANAGEMENT_ROOT_CONTEXT + "customer/availed-offer",
                    contact: CUSTOMER_OFFER_MANAGEMENT_ROOT_CONTEXT + "contactNumber/availed-offer",
                },
                applyCoupon: CUSTOMER_OFFER_MANAGEMENT_ROOT_CONTEXT + "coupon/apply",
                applyCash: CUSTOMER_OFFER_MANAGEMENT_ROOT_CONTEXT + "cash/apply"
            },
            offers: {
                allOffers: OFFER_MANAGEMENT_ROOT_CONTEXT + "coupon/offers",
                offerManagement: CUSTOMER_OFFER_MANAGEMENT_ROOT_CONTEXT + "check/membership",
                getAutoApplicableOfferForUnit: OFFER_MANAGEMENT_ROOT_CONTEXT_V1 + "get-auto-applicable-offer",
                getCustomerOffers: OFFER_MANAGEMENT_ROOT_CONTEXT + "coupon/customer"
            },
            posMetaData: {
                //unit:POS_METADATA_ROOT_CONTEXT+"unit",
                metaData: POS_METADATA_ROOT_CONTEXT + "metadata",
                metaDataByCategory: POS_METADATA_ROOT_CONTEXT + "metadata-by-category",
                unitMetadata: POS_METADATA_ROOT_CONTEXT + "unit-metadata",
                inventory: POS_METADATA_ROOT_CONTEXT + "unit/inventory",
                inventoryTrimmed: POS_METADATA_ROOT_CONTEXT + "unit/trim/inventory",
                stockouts: POS_METADATA_ROOT_CONTEXT + "unit/trim/inventory/stockouts",
                inventoryForProducts: POS_METADATA_ROOT_CONTEXT + "unit/inventory/products",
                updateInventory: POS_METADATA_ROOT_CONTEXT + "unit/inventory/update",
                stockoutInventory: POS_METADATA_ROOT_CONTEXT + "unit/inventory/stockout",
                closeUnit: POS_METADATA_ROOT_CONTEXT + "unit/close",
                pullUnitReason : POS_METADATA_ROOT_CONTEXT + "unit/pull/reason",
                pendingUnitPullTransfer:POS_METADATA_ROOT_CONTEXT + "unit/pending-pull-transfer",
                forceCloseUnit: POS_METADATA_ROOT_CONTEXT + "unit/forceclose",
                cancelDayClose: POS_METADATA_ROOT_CONTEXT + "unit/dayclose/cancel",
                allReports: POS_METADATA_ROOT_CONTEXT + "unit/report/manager",
                downloadReport: POS_METADATA_ROOT_CONTEXT + "unit/report/manager/download",
                taxProfile: POS_METADATA_ROOT_CONTEXT + "unit/taxprofile",
                packagingProfile: POS_METADATA_ROOT_CONTEXT + "unit/packaging-profile",
                brandPartnerPackagingProfile: POS_METADATA_ROOT_CONTEXT + "brand/partner/packaging-profile",
                deliveryProfile: POS_METADATA_ROOT_CONTEXT + "unit/delivery-partners",
                listTypes: POS_METADATA_ROOT_CONTEXT + "listTypes",
                refLookupUpsert: POS_METADATA_ROOT_CONTEXT + "refLookUp/upsert",
                creditAccounts: POS_METADATA_ROOT_CONTEXT + "creditAccount/names",
                getPnLStatusMap : POS_METADATA_ROOT_CONTEXT +"get-pnl-status-map",
                sendInventoryDownNotification : POS_METADATA_ROOT_CONTEXT +"send-inventory-down-notification",
                getKettleDayCloseStatus : POS_METADATA_ROOT_CONTEXT +"get/unit/dayClose",
                walletRecommendation: POS_METADATA_ROOT_CONTEXT + "wallet-recommendation",
            },
            users: {
                login: USER_SERVICES_ROOT_CONTEXT + "login",
                verify: USER_SERVICES_ROOT_CONTEXT + "verifyUser",
                verifyUserForCancellation : USER_SERVICES_ROOT_CONTEXT + "verifyUserForCancellation",
                changePassCode: USER_SERVICES_ROOT_CONTEXT + "changePasscode",
                logout: USER_SERVICES_ROOT_CONTEXT + "logout",
                authenticateMac: USER_SERVICES_ROOT_CONTEXT + "authenticateMac",
                adminLogin: USER_SERVICES_ROOT_CONTEXT + "admin/login",
                encryptAuth: USER_SERVICES_ROOT_CONTEXT + "encrypt-auth"
            },
            userManagement: {
                add: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "user/add",
                activeUnitsForUser: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "user/units",
                allUsers: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "users",
                allUsersForUnit: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "users/unit",
                empMealUsers: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "users/unit/employee-meal",
                user: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "user",
                activeUser: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "user/active",
                managers: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "managers",
                update: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "user/update",
                updateMapping: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "user/update-mapping",
                activateEmployee: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "user/activate",
                resetPasscode: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "reset-passcode",
                deactivateEmployee: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "user/deactivate",
                activeRider: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "rider/active",
                allRider: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "rider/all",
                deleteRider: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "rider/delete",
                disableRider: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "rider/disable",
                enableRider: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "rider/enable",
                addRider: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "rider/add",
                setSDPContact: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "rider/set-sdp-contact",
                clearSDPContact: USER_MANAGEMENT_SERVICES_ROOT_CONTEXT + "rider/clear-sdp-contact"
            },
            cashManagement: {
                getOpenPullsForUnit: CASH_MANAGEMENT_SERVICES_ROOT_CONTEXT + "pull/open/get",
                submitPull: CASH_MANAGEMENT_SERVICES_ROOT_CONTEXT + "pull/submit",
                transferPull: CASH_MANAGEMENT_SERVICES_ROOT_CONTEXT + "pull/transfer",
                getPullsForTransfer: CASH_MANAGEMENT_SERVICES_ROOT_CONTEXT + "pull/transfer/get",
                getPullDenominationsForPaymentMode: CASH_MANAGEMENT_SERVICES_ROOT_CONTEXT + "paymentMode/denominations",
                getPullSettlementsForUnit: CASH_MANAGEMENT_SERVICES_ROOT_CONTEXT + "pullSettlements/get",
                enrichPullSettlementsForUnit: CASH_MANAGEMENT_SERVICES_ROOT_CONTEXT + "pullSettlements/enrichPull",
                getCouponDenominations: CASH_MANAGEMENT_SERVICES_ROOT_CONTEXT + "couponDenominations/get",
                transferCouponDenominations: CASH_MANAGEMENT_SERVICES_ROOT_CONTEXT + "transferCouponDenominations/get",
                uploadTransferSlip: CASH_MANAGEMENT_SERVICES_ROOT_CONTEXT + "upload/slip",
                getValidPullTransferDates: CASH_MANAGEMENT_SERVICES_ROOT_CONTEXT + "pull/validDates/get"
            },
            /*healthMonitor:{
                unitStatus:HEALTH_MONITOR_ROOT_CONTEXT+"unit/status",
                allUnitStatus:HEALTH_MONITOR_ROOT_CONTEXT+"units/status",
                unitFailed:HEALTH_MONITOR_ROOT_CONTEXT+"unit/failed"
            },*/
            codCustomer: {
                lookup: COD_CUSTOMER_SERVICES_ROOT_CONTEXT + "lookup",
                addAddress: COD_CUSTOMER_SERVICES_ROOT_CONTEXT + "add/address",
                update: COD_CUSTOMER_SERVICES_ROOT_CONTEXT + "update"
            },
            paymentManagement: {
                hangingPayment: PAYMENT_MANAGEMENT_CONTEXT + "payment/hanging-payment",
                pendingRefunds: PAYMENT_MANAGEMENT_CONTEXT + "payment/pending-refunds",
                refund: PAYMENT_MANAGEMENT_CONTEXT + "payment/refund",
                refundApplicable: PAYMENT_MANAGEMENT_CONTEXT + "payment/refund-applicable"
            },
            manualBillBook: {
                getDetailsForUnit: KETTLE_METADATA_MANAGEMENT_ROOT_CONTEXT + "get-manual-bill-book-getail",
                updateBillForUnit: KETTLE_METADATA_MANAGEMENT_ROOT_CONTEXT + "update-manual-bill-book",
                validateBillBookNo: KETTLE_METADATA_MANAGEMENT_ROOT_CONTEXT + "validate-bill-book-no"
            },
            CrmServiceV2: {
                getAllCafeAppProperties: CRM_SERVICES_V2_ROOT_CONTEXT + "get-all-cafe-app-properties"
            },
            scmService: {
                pendingGRs: SCM_STOCK_MANAGEMENT + "pending-gr-check",
                pendingStockEvent: SCM_STOCK_MANAGEMENT + "pending-stock-event",
                validatingUnitCheck: SCM_STOCK_MANAGEMENT + "pending-po-ro-so-check",
                getSumoDayCloseStatus: SCM_STOCK_MANAGEMENT + "get/unit/dayClose",
                getPendingSpecialRos: SCM_STOCK_MANAGEMENT + "pending-milk-bread-ros"
            },
            expenseManagement: {
                addExpense: EXPENSE_MANAGEMENT_ROOT_CONTEXT + "add-expense",
                getExpenseDetail: EXPENSE_MANAGEMENT_ROOT_CONTEXT + "get-expense",
                cancelExpenseDetail: EXPENSE_MANAGEMENT_ROOT_CONTEXT + "cancel-expense",
                addMeterDetails: EXPENSE_MANAGEMENT_ROOT_CONTEXT + "add-meter-reading",
                lastMeterReading: EXPENSE_MANAGEMENT_ROOT_CONTEXT + "last-meter-reading",
                PnLReportMTD: EXPENSE_MANAGEMENT_ROOT_CONTEXT + "MTD-PnL-for-unit",
                PnLAggregateReport: EXPENSE_MANAGEMENT_ROOT_CONTEXT + "get-pnl-representation",
                budgetExceededTransactions: EXPENSE_MANAGEMENT_ROOT_CONTEXT + "budget-exceeded-datas",
                lastMeterReadingList: EXPENSE_MANAGEMENT_ROOT_CONTEXT + "last-meter-reading-list",
                updatelastMeterReading: EXPENSE_MANAGEMENT_ROOT_CONTEXT + "update-last-Reading",
                getMTDPnlDetailSheet: EXPENSE_MANAGEMENT_ROOT_CONTEXT + "get-mtd-pnl-detail",
                getMTDPnlAggregateDetailSheet: EXPENSE_MANAGEMENT_ROOT_CONTEXT + "get-mtd-pnl-aggregate-detail",
                getFinalizedPnlDetailSheet: EXPENSE_MANAGEMENT_ROOT_CONTEXT + "get-finalized-pnl-detail",
                getFinalizedPnlAggregateDetailSheet: EXPENSE_MANAGEMENT_ROOT_CONTEXT + "get-finalized-pnl-aggregate-detail"
            },
            budgetManagement: {
                getExpenseHeader: MASTER_METADATA_ROOT_CONTEXT + "expense-detail"
            },
            partnerOrder: {
                getPendingOrders: PARTNER_ORDER_ROOT_CONTEXT + "get-pending-orders",
                callSwiggySupport: PARTNER_ORDER_ROOT_CONTEXT + "call-swiggy-support",
                markResolved: PARTNER_ORDER_ROOT_CONTEXT + "mark-resolved",
                manualProcess: PARTNER_ORDER_ROOT_CONTEXT + "manual-process",
                getMagicpinOrderItems: PARTNER_ORDER_ROOT_CONTEXT + "get-magicpin-order-items",
                forceProcess: PARTNER_ORDER_ROOT_CONTEXT + "force-process",
                markCancelled: PARTNER_ORDER_ROOT_CONTEXT + "mark-cancelled",
                getSwiggyRiderTimeOfArrival: PARTNER_ORDER_ROOT_CONTEXT + "swiggy-rider-time",
                partnerOrderByNameAndOrderId: PARTNER_ORDER_ROOT_CONTEXT + "get-product-detail-by-name-orderId",
                rejectionProductDetail: PARTNER_ORDER_ROOT_CONTEXT + "rejection-product-detail",
                connectCustomer: PARTNER_ORDER_ROOT_CONTEXT + "customer-connect",
                addkettleOrderForRejection : PARTNER_ORDER_ROOT_CONTEXT + "add-kettle-order",
                sendStockOutNotification : PARTNER_ORDER_ROOT_CONTEXT + "stock-out-notification",
                fallbackProcessedBy: PARTNER_ORDER_ROOT_CONTEXT + "fallback-processed-by",
                partnerOrderResponse : PARTNER_ORDER_ROOT_CONTEXT + "partner-order-response"
            },
            partnerMetadataManagement: {
                unitToggle: PARTNER_METADATA_MANAGEMENT_CONTEXT + "unit-toggle",
                unitToggleCafeDashboard: PARTNER_METADATA_MANAGEMENT_CONTEXT + "unit-toggle-cafedashboard",
                unitTurnOn: PARTNER_METADATA_MANAGEMENT_CONTEXT + "unit-toggle-unit-off-dashboard",
                itemStockUpdate: PARTNER_METADATA_MANAGEMENT_CONTEXT + "item-stock-update",
                getDeliveryStatus: PARTNER_METADATA_MANAGEMENT_CONTEXT + "get-delivery-status",
                neoUnitTurnOn: NEO_SERVICE_CONTEXT + "nc/unit-toggle-on",
                neoUnitTurnOff: NEO_SERVICE_CONTEXT + "nc/unit-toggle-off",
            },
            dineInResources: {
                getCustomerInfo: DINE_IN_RESOURCE + "/customer/customerInfo-byId"
            },
            forms: {
                autoLogin: FORMS_FRONT_END_CONTEXT + "#auto-login",
                refundOrder: CLAIM_MANAGEMENT_CONTEXT + "refund-voucher",
                getExcludeCustomers: CLAIM_MANAGEMENT_CONTEXT + "get/exclude-customers"
            },
            inventory: {
                baseurl : INVENTORY_BASE_URL+"",
                postfix : POSTFIX+"",
            	cafe : INVENTORY_DATA_CONTEXT2 + "cafe-inventory?fetchMilk=true",
            	cafeProducts : INVENTORY_DATA_CONTEXT2 + "cafe-inventory/products",
            	scmProductsExpiry : INVENTORY_DATA_CONTEXT2 + "get-scm-products/expire",
            	scmSnapShot: INVENTORY_DATA_CONTEXT2 + "snapshot",
                cafeExpiryProducts: INVENTORY_DATA_CONTEXT2 + "get-cafe-products/expire"
            },
            brandMetadata: {
                getAllUnitPartnerBrandMappings : BRAND_METADATA_ROOT_CONTEXT + "unit-partner-brand-mappings",
                getUnitPartnerBrandMetadata : BRAND_METADATA_ROOT_CONTEXT + "unit-partner-brand-metadata/get",
            },
            cafeLookUp:{
             getCafeStatus:  CAFE_LOOKUP_MANAGEMENT_CONTEXT +"cafe-status",
             getCafeStatusForUnit:  CAFE_LOOKUP_MANAGEMENT_CONTEXT +"cafe-status-for-unit",
             getCafeOfflineStatusForUnit: CAFE_LOOKUP_MANAGEMENT_CONTEXT + "cafe-offline-status",
                getCafeStatusFromChannelPartner: CAFE_LOOKUP_MANAGEMENT_CONTEXT + "cafe-status-channel-partner",
                getUnitSwitchOffRequestsFromKnock : CAFE_LOOKUP_MANAGEMENT_CONTEXT + "unit-off-requests",
                updateUnitRequestFromUnitOffDashboard: CAFE_LOOKUP_MANAGEMENT_CONTEXT + "update-request-unit-off-dashboard"
            },
            reportMetadata: {
                getUnitClosureReports : REPORT_METADATA_ROOT_CONTEXT + "report/unit-closure"
            },
            kettleUIV2 : {
              kettleUIV2Url : kettleUIV2Url
            },
            edcPaymentManagement :{
                initiatePayment : EDC_PAYMENT_ROOT_CONTEXT + "initiate/transaction",
                validateTransactionId : EDC_PAYMENT_ROOT_CONTEXT + "validate",
                checkEdcTransactionStatus : EDC_PAYMENT_ROOT_CONTEXT + "update/transaction",
                initiatePayphiPayment : EDC_PAYMENT_ROOT_CONTEXT + "initiate/transaction/payphi",
                checkPayphiStatus : EDC_PAYMENT_ROOT_CONTEXT + "update/transaction/payphi"
            },
            swiggy :{
              connectCustomer: SWIGGY_CONTEXT + "customer-connect"
            },
          externalOrderManagement : {
            getPartnerOrderDetail : EXTERNAL_ORDER_MANAGEMENT_CONTEXT + "get-partner-order-detail",
            fetchPartnerOrderDetail : EXTERNAL_ORDER_MANAGEMENT_CONTEXT + "fetch-partner-order-detail",
            getCustomerDetail : EXTERNAL_ORDER_MANAGEMENT_CONTEXT + "get-customer-detail",
            getCustomerDetailForFallbackOrder : EXTERNAL_ORDER_MANAGEMENT_CONTEXT + "get-fallback-order-customer-detail"
          }
        };

        return service;
    }


})();





