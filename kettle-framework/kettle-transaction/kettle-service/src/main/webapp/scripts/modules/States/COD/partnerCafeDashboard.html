<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->


<div flash-message="5000"></div>
<div class="container-fluid" data-ng-init="init()">
    <div class="row">
        <form class="navbar-form bs-navbar-top-example navbar-static-top"
              style="margin-top: 20px" role="search" name="searchOrderForm">
            <button type="button" class="btn btn-warning pull-left"
                    style="margin-left: 50px" data-ng-click="backToCODCover()">Back</button>
            <button type="button" class="btn btn-warning pull-right"
                    style="margin-left: 50px" data-ng-click="refreshCafeStatus(true)">Refresh</button>
            <h2 class="text-center" style="color: #737370;text-align: center;">Partner Cafe Dashboard</h2>
        </form>
    </div>
    <div class="row">
        <div class="col-xs-12">
            <div data-ng-repeat="cafe in cafeList " style="border: #ccc 1px solid; background: #fff;">
                <div class="row"
                     style="padding: 10px; margin: 0; background: #efefef; border-bottom: #ccc 1px solid;  cursor: pointer;">
                    <div class="col-xs-1">
                        <label>Unit ID:</label> {{cafe.unitId}}
                    </div>
                    <div class="col-xs-2">
                        <label>Cafe Name:</label> {{cafe.unitName}}
                    </div>
                    <div class="col-xs-1">
                        <label>Unit City:</label> {{cafe.unitCity}}
                    </div>
                    <div class="col-xs-1">
                        <label>Brand Name:</label> {{cafe.brandName}}
                    </div>
                    <div class="col-xs-1">
                        <label>Partner Name:</label> {{cafe.channelPartner}}
                    </div>
                    <div class="col-xs-1" >
                        <label>Manager ID:</label> {{cafe.empId}}
                    </div>
                    <div class="col-xs-1">
                        <label>Source:</label> {{cafe.applicationSource}}
                    </div>
                    <div class="col-xs-1" data-ng-if="cafe.channelPartner == 'SWIGGY'" >
                        <label>Reason</label> {{"Not Available"}}
                    </div>
                    <div class="col-xs-1" data-ng-if="cafe.channelPartner == 'ZOMATO'" >
                        <label>Reason</label> {{cafe.reason}}
                    </div>
                    <div class="col-xs-1">
                        <label>Status:</label> {{cafe.statusUpdate}}
                    </div>
                    <div class="row" style="padding: 10px;">
                        <div class="col-xs-12 text-right">
                            <button class="btn btn-primary"  data-ng-click="activateUnitUrl(cafe)">Activate</button>
                        </div>
                    </div>
                </div>


            </div>
            </div>

        </div>
    </div>
</div>





