<!--&lt;!&ndash;-->
<!--  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL-->
<!--  ~ __________________-->
<!--  ~-->
<!--  ~ [2015] - [2017] Sunshine Teahouse Private Limited-->
<!--  ~ All Rights Reserved.-->
<!--  ~-->
<!--  ~ NOTICE:  All information contained herein is, and remains-->
<!--  ~ the property of Sunshine Teahouse Private Limited and its suppliers,-->
<!--  ~ if any.  The intellectual and technical concepts contained-->
<!--  ~ herein are proprietary to Sunshine Teahouse Private Limited-->
<!--  ~ and its suppliers, and are protected by trade secret or copyright law.-->
<!--  ~ Dissemination of this information or reproduction of this material-->
<!--  ~ is strictly forbidden unless prior written permission is obtained-->
<!--  ~ from Sunshine Teahouse Private Limited.-->
<!--  &ndash;&gt;-->

<!--<style>-->
<!--table th td {-->
<!--	text-align: center;-->
<!--}-->

<!--table>tbody {-->
<!--	margin-top: 100px;-->
<!--}-->
<!--</style>-->
<!--<div flash-message="5000"></div>-->
<!--<div class="row">-->
<!--	<h2 class="text-center"-->
<!--		style="color: #737370; margin-top: 50px; font-family: 'typewriter';">{{OrderObj.header}}</h2>-->

<!--	<button type="button" class="btn btn-warning pull-left"-->
<!--		style="margin-left: 50px" ng-click="backToCover()">back</button>-->
<!--	<h4 class="text-center"-->
<!--		style="color: #737370; margin-top: 50px; font-family: 'typewriter';">No-->
<!--		Of Bills : {{OrderObj.orders.length}}</h4>-->
<!--</div>-->
<!--<div class="row">-->
<!--<div class="col-xs-4"></div>-->
<!--<div class="col-xs-4">-->
<!--<table class="table table-bordered table-responsive" data-ng-if="OrderObj.employee != undefined && OrderObj.employee != null"-->
<!--	style="margin: 10px; margin-top: 50px;">-->
<!--	<thead>-->
<!--		<tr>-->
<!--			<td>Employee Name</td>-->
<!--			<td>{{OrderObj.employee.name}}</td>-->
<!--		</tr>-->
<!--	</thead>-->
<!--	<tbody>-->
<!--		<tr>-->
<!--			<td>Available Allowance Limit</td>-->
<!--			<td>{{OrderObj.employee.mealAllowanceLimit}}</td>-->
<!--		</tr>-->
<!--	</tbody>-->
<!--</table>-->
<!--</div>-->
<!--<div class="col-xs-4"></div>-->
<!--</div>-->
<!--&lt;!&ndash;<div ui-grid="{ data: jsonOrderObj }" class="myGrid"></div>&ndash;&gt;-->
<!--<table class="table table-striped"-->
<!--	style="margin: 10px; margin-top: 50px">-->
<!--	<thead>-->
<!--		<tr>-->
<!--			<th>Generated Bill No</th>-->
<!--			<th>Type</th>-->
<!--			<th>Brand</th>-->
<!--			<th>Bill Book No</th>-->
<!--			<th>Source</th>-->
<!--			<th>Unit Order Id</th>-->
<!--			<th>Creation Time</th>-->
<!--			<th>Gross Amount</th>-->
<!--			<th>Discount</th>-->
<!--			<th>Taxable Amount</th>-->
<!--			<th>Total Tax</th>-->
<!--			<th>Round Off</th>-->
<!--			<th>Paid Amount</th>-->
<!--			<th>Is Parcel</th>-->
<!--			<th>Status</th>-->
<!--		</tr>-->
<!--	</thead>-->
<!--	<tbody>-->
<!--		<tr ng-repeat="orderItem in OrderObj.orders">-->
<!--			<td><a ng-click="openOrderSearch(orderItem.generateOrderId)"-->
<!--				style="color: dodgerblue"><b>{{orderItem.generateOrderId}}</b></a></td>-->
<!--			<td>{{orderItem.orderType}}</td>-->
<!--			<td>{{orderItem.brand.brandName}}</td>-->
<!--			<td>{{orderItem.billBookNo}}</td>-->
<!--			<td>{{orderItem.source}}</td>-->
<!--			<td>{{orderItem.unitOrderId}}</td>-->
<!--			<td>{{orderItem.billCreationTime | date:'yyyy-MM-dd HH:mm:ss'}}</td>-->
<!--			<td>{{orderItem.transactionDetail.totalAmount}}</td>-->
<!--			<td>{{orderItem.transactionDetail.discountDetail.discount.value}}</td>-->
<!--			<td>{{orderItem.transactionDetail.taxableAmount}}</td>-->
<!--			<td>{{orderItem.transactionDetail.tax}}</td>-->
<!--			<td>{{orderItem.transactionDetail.roundOffValue}}</td>-->
<!--			<td>{{orderItem.transactionDetail.paidAmount}}</td>-->
<!--			<td>{{orderItem.hasParcel}}</td>-->
<!--			<td>{{orderItem.status}}</td>-->
<!--		</tr>-->
<!--	</tbody>-->
<!--</table>-->

<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<style>
    table th td {
        text-align: center;
    }

    table > tbody {
        margin-top: 100px;
    }

    .table-row:hover {
        background-color: lightgreen;
    }

    .selected {
        background-color: lightgreen !important;
    }
</style>
<div flash-message="5000"></div>
<div class="row">
    <h2 class="text-center"
        style="color: #737370; margin-top: 50px; font-family: 'typewriter';">{{OrderObj.header}}</h2>

    <button type="button" class="btn btn-warning pull-left"
            style="margin-left: 50px" ng-click="backToCover()">Back
    </button>
    <h4 class="text-center"
        style="color: #737370; margin-top: 50px; font-family: 'typewriter';"
        data-ng-if="OrderObj.employee == null || OrderObj.employee == undefined">No
        Of Bills : {{OrderObj.orderDetailTrims.length}}</h4>

    <h4 class="text-center"
        style="color: #737370; margin-top: 50px; font-family: 'typewriter';"
        data-ng-if="OrderObj.employee != null && OrderObj.employee != undefined">No
        Of Bills : {{OrderObj.orders.length}}</h4>
</div>
<div class="row">
    <div class="col-xs-4"></div>
    <div class="col-xs-4">
        <table class="table table-bordered table-responsive"
               data-ng-if="OrderObj.employee != undefined && OrderObj.employee != null"
               style="margin: 10px; margin-top: 50px;">
            <thead>
            <tr>
                <td>Employee Name</td>
                <td>{{OrderObj.employee.name}}</td>
            </tr>
            </thead>
            <tbody>
            <tr>
                <td>Available Allowance Limit</td>
                <td>{{OrderObj.employee.mealAllowanceLimit}}</td>
            </tr>
            </tbody>
        </table>
    </div>
    <div class="col-xs-4"></div>
</div>
<!--<div ui-grid="{ data: jsonOrderObj }" class="myGrid"></div>-->
<!--<div class="mx-auto col-s12">-->
<!--		<table class="table table-striped"-->
<!--		   style="margin: 10px; margin-top: 50px">-->
<!--			<thead>-->
<!--				<tr>-->
<!--					<td scope="col">Generated Bill No</td>-->
<!--					<th scope="col">Channel Partner</th>-->
<!--					<th scope="col">Type</th>-->
<!--					<th scope="col">Brand</th>-->
<!--					<th scope="col">Bill Book No</th>-->
<!--					<th scope="col">Table Number</th>-->
<!--					<th scope="col">Source</th>-->
<!--					<th scope="col">Creation Time</th>-->
<!--					<th scope="col">Taxable Amount</th>-->
<!--					<th scope="col">Paid Amount</th>-->
<!--					<th scope="col">Settlement</th>-->
<!--				</tr>-->
<!--			</thead>-->
<!--			<tbody>-->
<!--				<tr ng-repeat-start="orderItem in OrderObj.orders"-->
<!--					data-ng-click="SelectedRow(orderItem)" style="cursor: pointer;"-->
<!--					data-ng-class="{'rowSelected':orderItem.generateOrderId===idSelected}">-->
<!--					<th><a ng-click="openOrderSearch(orderItem.generateOrderId)"-->
<!--						   style="color: #1e90ff" scope="row"><b>{{orderItem.generateOrderId}}</b></a></th>-->
<!--					<td>{{orderItem.channelPartner}}</td>-->
<!--					<td>{{orderItem.orderType}}</td>-->
<!--					<td>{{AppUtil.getBrandByBrandId(orderItem.brandId)}}</td>-->
<!--					<td>{{orderItem.billBookNo}}</td>-->
<!--					<td>{{orderItem.tableNumber}}</td>-->
<!--					<td>{{orderItem.source}}</td>-->
<!--					<td>{{orderItem.billCreationTime | date:'yyyy-MM-dd HH:mm:ss'}}</td>-->
<!--					<td>{{orderItem.transactionDetail.taxableAmount}}</td>-->
<!--					<td>{{orderItem.transactionDetail.paidAmount}}</td>-->
<!--					<td>{{orderItem.settlementType}}</td>-->
<!--				<tr ng-repeat-end=""-->
<!--					ng-show="orderItem.generateOrderId===idSelected && groupSelected">-->
<!--					<td colspan="8">-->
<!--							<table class="table table-bordered" >-->
<!--								<tr data-ng-class="{'rowSelected':true}">-->
<!--									<td>{{"settlementId :"+orderItem.settlements.settlementId}}</td>-->
<!--									<td>{{"mode :"+orderItem.settlements.mode}}</td>-->
<!--									<td>{{"modeDetail :"+orderItem.settlements.modeDetail}}</td>-->
<!--									<td>{{"amount :"+orderItem.settlements.amount}}</td>-->
<!--									<td>{{"extraVouchers :"+orderItem.settlements.extraVouchers}}</td>-->
<!--									<td>{{"amount :"+orderItem.settlements.amount}}</td>-->
<!--								</tr>-->
<!--							</table>-->
<!--					</td>-->
<!--				</tr>-->
<!--			</tbody>-->
<!--	</table>-->
<!--</div>-->
<div class="table" data-ng-if="OrderObj.employee == null || OrderObj.employee == undefined">
    <!--<div class="table table-striped">-->
        <!--<div style="display: flex">-->
            <!--<div class="col col-xs-1" style="width: 10%;display: inline-block;font-weight: bold;">Generated Bill No</div>-->
            <!--<div class="col col-xs-1" style="width: 3%;display: inline-block;font-weight: bold;">Channel Partner</div>-->
            <!--<div class="col col-xs-1" style="width: 4%;display: inline-block;font-weight: bold;">Type</div>-->
            <!--<div class="col col-xs-1" style="width: 5%;display: inline-block;font-weight: bold;">Brand</div>-->
            <!--<div class="col col-xs-1" style="width: 7%;display: inline-block;font-weight: bold;">Bill Book No</div>-->
            <!--<div class="col col-xs-1" style="width: 5%;display: inline-block;font-weight: bold;">Table Number</div>-->
            <!--<div class="col col-xs-1" style="width: 4%;display: inline-block;font-weight: bold;">Source</div>-->
            <!--<div class="col col-xs-1" style="width: 29%;display: inline-block;font-weight: bold;">Settlement</div>-->
            <!--<div class="col col-xs-1" style="width: 10%;display: inline-block;font-weight: bold;">Creation Time</div>-->
            <!--<div class="col col-xs-1" style="width: 5%;display: inline-block;font-weight: bold;">Taxable Amount</div>-->
            <!--<div class="col col-xs-1" style="width: 5%;display: inline-block;font-weight: bold;">Paid Amount</div>-->
            <!--<div class="col col-xs-1" style="width: 7%;display: inline-block;font-weight: bold;">Points Redeemed</div>-->
            <!--<div class="col col-xs-1" style="width: 7%;display: inline-block;font-weight: bold;">Points Acquired</div>-->
        <!--</div>-->
    <!--</div>-->

    <!--<div class="accordion">-->
        <!--<div class="table table-striped" ng-repeat="orderItem in OrderObj.orders" style="display: flex">-->
            <!--<div class="col col-xs-1" style="text-align: center;width: 10%"><a ng-click="openOrderSearch(orderItem.generateOrderId)"-->
                                         <!--style="color: #1e90ff;" scope="row"><b>{{orderItem.generateOrderId}}</b></a>-->
            <!--</div>-->
            <!--<div class="col col-xs-1" style="text-align: center;width: 3%">{{orderItem.channelPartner}}</div>-->
            <!--<div class="col col-xs-1" style="text-align: center;width: 4%">{{orderItem.orderType}}</div>-->
            <!--<div class="col col-xs-1" style="text-align: center;width: 5%">{{getBrandName(orderItem.brandId)}}</div>-->
            <!--<div class="col col-xs-1" style="text-align: center;width: 7%">{{orderItem.billBookNo}}</div>-->
            <!--<div class="col col-xs-1" style="text-align: center;width: 5%;">{{orderItem.tableNumber}}</div>-->
            <!--<div class="col col-xs-1" style="text-align: center;width: 4%;">{{orderItem.source}}</div>-->
            <!--<div id="accordion" style="width: 29%">-->
                <!--<div class="card">-->
                    <!--<div class="card-header" id="heading{{orderItem.generateOrderId}}">-->
                        <!--<h5 class="mb-0">-->
                            <!--<button class="btn btn-link collapsed" data-toggle="collapse"-->
                                    <!--data-target="#{{orderItem.generateOrderId}}" aria-expanded="false"-->
                                    <!--aria-controls="collapseThree" ng-click="openSettlementTable(orderItem.settlements)">{{orderItem.settlementType}}-->
                            <!--</button>-->
                        <!--</h5>-->
                    <!--</div>-->
                    <!--<div id={{orderItem.generateOrderId}} class="collapse"-->
                         <!--aria-labelledby="heading1{{orderItem.generateOrderId}}" data-parent="#accordion">-->
                        <!--<div class="card-body">-->
                            <!--&lt;!&ndash;<div class="table">&ndash;&gt;-->
                                <!--&lt;!&ndash;<div class="table table-striped">&ndash;&gt;-->
                                    <!--&lt;!&ndash;<div style="display: flex;flex-direction: column">&ndash;&gt;-->
                                        <!--&lt;!&ndash;<div class="col col-xs-1"></div>&ndash;&gt;-->
                                        <!--&lt;!&ndash;<div class="col col-xs-1" style="text-align: center"><b>SettlementID</b></div>&ndash;&gt;-->
                                        <!--&lt;!&ndash;<div class="col col-xs-1" style="text-align: center"><b>Mode</b></div>&ndash;&gt;-->
                                        <!--&lt;!&ndash;<div class="col col-xs-1" style="text-align: center"><b>Mode Detail</b></div>&ndash;&gt;-->
                                        <!--&lt;!&ndash;<div class="col col-xs-1" style="text-align: center"><b>Amount</b></div>&ndash;&gt;-->
                                        <!--&lt;!&ndash;<div class="col col-xs-1" style="text-align: center"><b>ExtraVouchers</b></div>&ndash;&gt;-->
                                        <!--&lt;!&ndash;<div class="col col-xs-1" style="text-align: center"><b>External Settlements</b>&ndash;&gt;-->
                                        <!--&lt;!&ndash;</div>&ndash;&gt;-->
                                    <!--&lt;!&ndash;</div>&ndash;&gt;-->
                                <!--&lt;!&ndash;</div>&ndash;&gt;-->
                                <!--&lt;!&ndash;<div>&ndash;&gt;-->
                                    <!--&lt;!&ndash;<div class="col col-xs-1"></div>&ndash;&gt;-->
                                    <!--&lt;!&ndash;<div class="col col-xs-1" style="text-align: center;">&ndash;&gt;-->
                                        <!--&lt;!&ndash;{{orderItem.settlements.settlementId}}&ndash;&gt;-->
                                    <!--&lt;!&ndash;</div>&ndash;&gt;-->
                                    <!--&lt;!&ndash;<div class="col col-xs-1" style="text-align: center">&ndash;&gt;-->
                                        <!--&lt;!&ndash;{{orderItem.settlements.mode}}&ndash;&gt;-->
                                    <!--&lt;!&ndash;</div>&ndash;&gt;-->
                                    <!--&lt;!&ndash;<div class="col col-xs-1" style="text-align: center">&ndash;&gt;-->
                                        <!--&lt;!&ndash;{{orderItem.settlements.modeDetail}}&ndash;&gt;-->
                                    <!--&lt;!&ndash;</div>&ndash;&gt;-->
                                    <!--&lt;!&ndash;<div class="col col-xs-1" style="text-align: center">&ndash;&gt;-->
                                        <!--&lt;!&ndash;{{orderItem.settlements.amount}}&ndash;&gt;-->
                                    <!--&lt;!&ndash;</div>&ndash;&gt;-->
                                    <!--&lt;!&ndash;<div class="col col-xs-1" style="text-align: center">&ndash;&gt;-->
                                        <!--&lt;!&ndash;{{orderItem.settlements.extraVouchers}}&ndash;&gt;-->
                                    <!--&lt;!&ndash;</div>&ndash;&gt;-->
                                    <!--&lt;!&ndash;<div class="col col-xs-1" style="text-align: center">&ndash;&gt;-->
                                        <!--&lt;!&ndash;{{orderItem.settlements.externalSettlements}}&ndash;&gt;-->
                                    <!--&lt;!&ndash;</div>&ndash;&gt;-->
                                <!--&lt;!&ndash;</div>&ndash;&gt;-->
                            <!--&lt;!&ndash;</div>&ndash;&gt;-->
                            <!--&lt;!&ndash;<table>&ndash;&gt;-->
                                <!--&lt;!&ndash;<tr style="background-color: lightgray">&ndash;&gt;-->
                                    <!--&lt;!&ndash;<th style="text-align: center; padding: 4px; border: 0.5px solid;">SettlementID</th>&ndash;&gt;-->
                                    <!--&lt;!&ndash;<th style="text-align: center; padding: 4px; border: 0.5px solid;">Mode</th>&ndash;&gt;-->
                                    <!--&lt;!&ndash;<th style="text-align: center; padding: 4px; border: 0.5px solid;">Mode Detail</th>&ndash;&gt;-->
                                    <!--&lt;!&ndash;<th style="text-align: center; padding: 4px; border: 0.5px solid;">Amount</th>&ndash;&gt;-->
                                    <!--&lt;!&ndash;<th style="text-align: center; padding: 4px; border: 0.5px solid;">ExtraVouchers</th>&ndash;&gt;-->
                                    <!--&lt;!&ndash;<th style="text-align: center; padding: 4px; border: 0.5px solid;">External Settlements</th>&ndash;&gt;-->
                                <!--&lt;!&ndash;</tr>&ndash;&gt;-->
                                <!--&lt;!&ndash;<tr ng-repeat="orderSettlement in orderItem.settlements">&ndash;&gt;-->
                                   <!--&lt;!&ndash;<td style="text-align: center; border: 0.5px solid;">{{orderSettlement.settlementId}}</td>&ndash;&gt;-->
                                   <!--&lt;!&ndash;<td style="text-align: center; border: 0.5px solid;">{{orderSettlement.mode}}</td>&ndash;&gt;-->
                                   <!--&lt;!&ndash;<td style="text-align: center; border: 0.5px solid;">{{orderSettlement.modeDetail.name}}</td>&ndash;&gt;-->
                                   <!--&lt;!&ndash;<td style="text-align: center; border: 0.5px solid;">{{orderSettlement.amount}}</td>&ndash;&gt;-->
                                   <!--&lt;!&ndash;<td style="text-align: center; border: 0.5px solid;">{{orderSettlement.extraVouchers}}</td>&ndash;&gt;-->
                                   <!--&lt;!&ndash;<td style="text-align: center; border: 0.5px solid;"></td>&ndash;&gt;-->
                                <!--&lt;!&ndash;</tr>&ndash;&gt;-->
                            <!--&lt;!&ndash;</table>&ndash;&gt;-->
                        <!--</div>-->
                    <!--</div>-->
                <!--</div>-->
            <!--</div>-->
            <!--<div class="col col-xs-1" style="text-align: center;width: 10%">{{orderItem.billCreationTime | date:'yyyy-MM-ddHH:mm:ss'}}-->
            <!--</div>-->
            <!--<div class="col col-xs-1" style="text-align: center;width: 5%;">{{orderItem.transactionDetail.taxableAmount}}</div>-->
            <!--<div class="col col-xs-1" style="text-align: center;width: 5%">{{orderItem.transactionDetail.paidAmount}}</div>-->
            <!--<div class="col col-xs-1" style="text-align: center;width: 7%;">{{orderItem.pointsRedeemed*-1}}</div>-->
            <!--<div class="col col-xs-1" style="text-align: center;width: 7%;">{{orderItem.pointsAcquired}}</div>-->
        <!--</div>-->
    <!--</div>-->
<!--                <div class="col-xs-3" style="margin-top: 20px" >-->
<!--                   <b> Results per page</b>: <select-->
<!--                        ng-model="batchSize"-->
<!--                        class="form-control"-->
<!--                >-->
<!--                    <option value="5">5</option>-->
<!--                    <option value="10">10</option>-->
<!--                    <option value="20">20</option>-->
<!--                    <option value="50">50</option>-->
<!--                    <option value="100">100</option>-->
<!--                </select>-->
<!--                </div>-->
                <table>
        <tr style="background-color: lightgray">
            <th class="col col-xs-1" style="text-align: center">Generated Bill No</th>
            <th class="col col-xs-1" style="text-align: center">Channel Partner</th>
            <th class="col col-xs-1" style="text-align: center">Type</th>
            <th class="col col-xs-1" style="text-align: center">Brand</th>
            <th class="col col-xs-1" style="text-align: center">Bill Book No</th>
            <th class="col col-xs-1" style="text-align: center">Table Number</th>
            <th class="col col-xs-1" style="text-align: center">Source</th>
            <th class="col col-xs-1" style="text-align: center">Creation Time</th>
            <th class="col col-xs-1" style="text-align: center">Taxable Amount</th>
            <th class="col col-xs-1" style="text-align: center">Paid Amount</th>
            <th class="col col-xs-1" style="text-align: center">Settlement</th>
            <th class="col col-xs-1" style="text-align: center">Points Redeemed</th>
            <th class="col col-xs-1" style="text-align: center">Points Acquired</th>
        </tr>
        <tr class="table table-row" ng-repeat="orderItem in OrderObj.orderDetailTrims" id="row-{{orderItem.generatedOrderId}}">
            <td class="col col-xs-1"><a ng-click="openOrderSearch(orderItem.generatedOrderId)"
            style="color: #1e90ff" scope="row"><b>{{orderItem.generatedOrderId}}</b></a>
            </td>
            <td class="col col-xs-1" style="text-align: center">{{orderItem.channelPartnerId}}</td>
            <td class="col col-xs-1" style="text-align: center">{{orderItem.orderType}}</td>
            <td class="col col-xs-1" style="text-align: center">{{getBrandName(orderItem.brandId)}}</td>
            <td class="col col-xs-1" style="text-align: center">{{orderItem.manualBillBookNo}}</td>
            <td class="col col-xs-1" style="text-align: center">{{orderItem.tableNumber}}</td>
            <td class="col col-xs-1" style="text-align: center">{{orderItem.orderSource}}</td>
            <td class="col col-xs-1" style="text-align: center">{{orderItem.billGenerationTime | date:'yyyy-MM-ddHH:mm:ss'}}</td>
            <td class="col col-xs-1" style="text-align: center">{{orderItem.taxableAmount}}</td>
            <td class="col col-xs-1" style="text-align: center">{{orderItem.settledAmount}}</td>
            <td id="accordion" style="text-align: center">
                <div class="card">
                    <div class="card-header" id="heading{{orderItem.generateOrderId}}">
                        <h5 class="mb-0">
                            <button class="btn btn-link collapsed" data-toggle="collapse"
                                    data-target="#{{orderItem.generateOrderId}}" aria-expanded="false"
                                    aria-controls="collapseThree" ng-click="openSettlementTable(orderItem.settlements, orderItem.generatedOrderId)">{{orderItem.settlementType}}
                            </button>
                        </h5>
                    </div>
                </div>
            </td>
            <td class="col col-xs-1" style="text-align: center">{{orderItem.pointsRedeemed*-1}}</td>
            <td class="col col-xs-1" style="text-align: center">{{orderItem.pointsAcquired}}</td>
        </tr>
    </table>

    <div class="container" style="margin-left:40%;margin-right:40%">
        <button data-ng-hide="showViewMoreButton " type="button" class="btn btn-default btn-sm">
            <span class="glyphicon glyphicon-triangle-bottom"
                  ng-click="nextOrderSummary()"> ViewMore</span>
        </button>
    </div>
</div>

<div class="table" data-ng-if="OrderObj.employee != null && OrderObj.employee != undefined">
    <table>
        <tr style="background-color: lightgray">
            <th class="col col-xs-1" style="text-align: center">Generated Bill No</th>
            <th class="col col-xs-1" style="text-align: center">Channel Partner</th>
            <th class="col col-xs-1" style="text-align: center">Type</th>
            <th class="col col-xs-1" style="text-align: center">Brand</th>
            <!-- <th class="col col-xs-1" style="text-align: center">Bill Book No</th> -->
            <!-- <th class="col col-xs-1" style="text-align: center">Table Number</th> -->
            <th class="col col-xs-1" style="text-align: center">Order Items</th>
            <th class="col col-xs-1" style="text-align: center">Source</th>
            <th class="col col-xs-1" style="text-align: center">Creation Time</th>
            <th class="col col-xs-1" style="text-align: center">Taxable Amount</th>
            <th class="col col-xs-1" style="text-align: center">Paid Amount</th>
            <th class="col col-xs-1" style="text-align: center">Settlement</th>
            <th class="col col-xs-1" style="text-align: center">Points Redeemed</th>
            <th class="col col-xs-1" style="text-align: center">Points Acquired</th>
        </tr>
        <tr class="table table-row" ng-repeat="orderItem in OrderObj.employeeMealOrderItems">
            <td class="col col-xs-1" style="text-align: center">{{orderItem.generateOrderId}}</td>
            <td class="col col-xs-1" style="text-align: center">{{orderItem.channelPartner}}</td>
            <td class="col col-xs-1" style="text-align: center">{{orderItem.orderType}}</td>
            <td class="col col-xs-1" style="text-align: center">{{getBrandName(orderItem.brandId)}}</td>
            <!-- <td class="col col-xs-1" style="text-align: center">{{orderItem.manualBillBookNo}}</td> -->
            <!-- <td class="col col-xs-1" style="text-align: center">{{orderItem.tableNumber}}</td> -->
            <td class="col col-xs-1" style="text-align: center">
                <button class="btn btn-sm btn-primary" 
                        ng-click="openOrderItemsModal(orderItem.orders)">
                    {{orderItem.orders.length}} Items
                </button>
            </td>
            <td class="col col-xs-1" style="text-align: center">{{orderItem.source}}</td>
            <td class="col col-xs-1" style="text-align: center">{{orderItem.billCreationTime | date:'yyyy-MM-ddHH:mm:ss'}}</td>
            <td class="col col-xs-1" style="text-align: center">{{orderItem.transactionDetail.taxableAmount}}</td>
            <td class="col col-xs-1" style="text-align: center">{{orderItem.transactionDetail.paidAmount}}</td>
            <td class="col col-xs-1" style="text-align: center">{{orderItem.settlementType}}</td>
            <td class="col col-xs-1" style="text-align: center">{{orderItem.pointsRedeemed*-1}}</td>
            <td class="col col-xs-1" style="text-align: center">{{orderItem.pointsAcquired}}</td>
        </tr>
    </table>
</div>


<!-- Modal -->
<div class="modal fade" id="orderItemsModal" tabindex="-1" role="dialog">
  <div class="modal-dialog" style="width: 600px">
    <div class="modal-content">
      <div class="modal-header">
        <h4 class="modal-title">Order Items</h4>
        <button type="button" class="close" data-dismiss="modal">&times;</button>
      </div>
      <div class="modal-body">
        <table class="table table-bordered">
          <thead>
            <tr>
              <th>Product Name</th>
              <th>Product Price</th>
            </tr>
          </thead>
          <tbody>
            <tr ng-repeat="item in selectedOrderItems">
              <td>{{item.productName}}</td>
              <td>{{item.price}}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>
