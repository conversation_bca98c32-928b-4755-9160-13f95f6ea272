<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div data-flash-message="5000"></div>
<div class="container-fluid" data-ng-init="init()">
	<div class="row">
		<div class="col-xs-12">
			<h2 class="text-center" style="font-family: 'typewriter';">Untransferred Settlement Components For Unit</h2>
		</div>
	</div>
	<div class="row" style="margin-top:20px;">
		<div class="col-xs-2">
			<button class="btn btn-warning pull-left" data-ng-click="backToCover()">Back</button>
		</div>
		<div class="col-xs-4">
			<select class="form-control" data-ng-model="selectedPaymentMode" data-ng-options="mode as mode.description for mode in paymentModes |filter: { autoTransfer: false } track by mode.id | orderBy : 'id' "></select>
		</div>
		<div class="col-xs-2">
			<button data-ng-click="getPullPackets()" class="btn btn-warning">Get Components</button>
		</div>
	</div>
	<div class="row" style="margin-top:20px;" data-ng-if="hasTransfers()">
		<div class="col-xs-12">
			<button class="btn btn-warning pull-right" data-ng-click="createTransfer()">Create transfer</button>
		</div>
	</div>
	<div class="row" style="margin-top:20px;">
		<div class="col-xs-12">
			<div data-ng-show="listLoading" class="text-center"><img src="images/loader.gif" /></div>
			<table class="table table-striped" style="background:#fff;" data-ng-if="openPulls.length>0">
				<tr>
					<th>Component Id</th>
					<th>Payment Mode</th>
					<th>Amount</th>
					<th>Business Date</th>
					<th>Status</th>
					<th>Created By</th>
					<th>Witnessed By</th>
					<th>Comment</th>
					<th>Actions</th>
				</tr>
				<tr data-ng-repeat="pull in openPulls">
					<td>{{pull.pullPacketId}}</td>
					<td>{{pull.paymentMode.description}}</td>
					<td>{{pull.pullAmount}}</td>
					<td>{{pull.pullDate | date:'yyyy-MM-dd'}}</td>
					<td>{{pull.pullPacketStatus}}</td>
					<td>{{pull.createdByName}}</td>
					<td>{{pull.witnessedBy}}</td>
					<td>{{pull.comment}}</td>
					<td>
						<span data-ng-if="pull.pullPacketStatus=='CREATED'" >
						 	<input type="checkbox" data-ng-model="pull.transfer" /> Transfer Components
						</span>
					</td>
				</tr>
			</table>
			<div class="alert alert-info" data-ng-if="openPulls.length==0">No open components found.</div>
		</div>
	</div>
</div>