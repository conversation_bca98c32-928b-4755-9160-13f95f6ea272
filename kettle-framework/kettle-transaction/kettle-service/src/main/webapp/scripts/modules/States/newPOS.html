<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="row" data-ng-if="isCOD && freeKettle && orderType!='subscription' && !isSpecialOrder">
    <div class="alert free-kettle text-center">This customer is
        eligible for availing Free Kettle
    </div>
</div>
<span us-spinner spinner-key="spinner-2" spinner-theme="smallRed"></span>

<!-- <div data-flash-message="5000"></div> -->
<div class="row" data-flash-message="3000"></div>
<div class="row" data-ng-init="init()">
    <div class="col-xs-5">
        <div class="row">
            <div class="col-xs-6" data-ng-if="isCOD">
                <h3
                        style="font-family: Arial, sans-serif; font-weight: bold; font-size: 20px; text-align: left;">
                    {{outletName()}}</h3>
            </div>
            <div class="col-xs-8" data-ng-if="( isCafe || isTakeaway ) && !isSpecialOrder"></div>
            <div class="col-xs-8"
                 data-ng-if="( isCafe || isTakeaway ) && isSpecialOrder && complimentaryReasonCode != null">
                <div class="row">
                    <div class="col-xs-6">
                        <p style="font-family: Arial, sans-serif; font-weight: bold; font-size: 20px; text-align: lrft; color: red;">
                            {{complimentaryReasonCode}}</p>
                    </div>
                </div>
            </div>
            <div class="col-xs-8"
                 data-ng-if="(isCOD || isCafe || isTakeaway ) && (isEmployeeMeal || isPaidEmployeeMeal)">
                <div class="row">
                    <div class="col-xs-3">
                        <p style="font-family: Arial, sans-serif; font-weight: bold; font-size: 20px; text-align: right;">
                            Id : {{employeeMealUser.id}}</p>
                    </div>
                    <div class="col-xs-3">
                        <p style="font-family: Arial, sans-serif; font-weight: bold; font-size: 20px; text-align: right;">
                            Name : {{employeeMealUser.name}}</p>
                    </div>
                    <div class="col-xs-2">
                        <p style="font-family: Arial, sans-serif; font-weight: bold; font-size: 20px; text-align: right;">
                            Allowance Limit : {{employeeMealUser.mealAllowanceLimit}}</p>
                    </div>
                </div>
            </div>
            <!--<div class="col-xs-4"-->
            <!--style="font-family: Arial, sans-serif; font-weight: bold; font-size: 20px; text-align: right; color: red;">-->
            <!--<timer interval="1000">{{mminutes}} min{{minutesS}} :-->
            <!--{{sseconds}} sec{{secondsS}}-->
            <!--</timer>-->
            <!--</div>-->
        </div>
        <div class="row">
            <div class="col-xs-12 navbar-collapse collapse navigacija">
                <ul id="menu-header" class="nav navbar-nav navbar-left"
                    style="padding-left: 10px;cursor: default;">
                    <li
                            data-ng-repeat="catID in productCategory track by $index"><a
                            href="#" class="categoryListAnchorClass"
                            data-ng-click="getProductsForCategory(catID.content,catID.detail.name,catID.detail.id)">{{getFirstWord(catID.detail.name)}}
                    </a>
                        <ul role="menu" data-ng-if="catID.detail.name =='Food'"
                            data-ng-class="{'dropdown-menu ddmenu':(catID.content.length > 0)}"
                            data-ng-style="{'min-width': catID.content.length * 125}">
                            <li id="categoryRow" class="dropdown pull-left"
                                data-ng-repeat="subCatId in catID.content"><a
                                    data-ng-click="getProductsForSubCategory(subCatId.id)">
                                <!-- ui-sref=".products({ catId: subCatId.id })"> -->
                                <button class='btn btn-lg posSubCategoryButton'>{{subCatId.name}}</button>
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
        <div class="row" >
            <div class ="col-xs-12 container-fluid container-product-display row-fluid"
                 ng-if ="(selectedCategoryType == 5 || selectedCategoryType==99999) && customerFavChaiMappings!=null && customerFavChaiMappings.length>0 && customerBasicInfo !=null && customerBasicInfo!=undefined && customerBasicInfo.id !=undefined && customerBasicInfo.id !=null && customerBasicInfo.id>5"
                 style="height: 150px; max-height: 150px; overflow-x: scroll; display: flex; flex-direction: row;"
            >
                <div  style ="height: 120px;width: 190px;display: inline-block ; margin:10px; margin-left: 20px ; margin-top:0px;" ng-repeat="customerFavChaiMapping in customerFavChaiMappings " >
                    <div style="display: flex;justify-content: center;align-items: center;position: relative;top:15px;z-index: 2">
                        <div style="background-color: #E5E5E5;" ></div>
                        <div style =" width:60% ;border-radius :20px;height:30px;background-color: red; display:flex; justify-content: center;align-items: center">
                            <text style="color: white;font-weight: bold">{{customerFavChaiMapping.tagType}}</text>
                        </div>
                        <div style="background-color: #E5E5E5;"></div>
                    </div>

                    <button class='btn btn-lg favChaiButton'  style="text-align: left;padding-left: 6px"type="button"
                            ng-class="{vegButton:item.attribute == 'VEG',nonVegButton:item.attribute == 'NON_VEG'}"
                            ng-click="addNewProductToOrderItemArrayForProductId(customerFavChaiMapping)">
                        <span style="color: black;font-weight: bold;">{{getFavChaiCustomizationShortCodes(customerFavChaiMapping)}}</span>
                    </button>
                </div>
            </div>
        </div>

        <div class="row"
             style="height: 300px; max-height: 300px; overflow: scroll">
            <div
                    class="col-xs-12 container-fluid container-product-display row-fluid"
                    ng-if="productsForSubCategory.length > 0">
                <button class='btn btn-lg posProductButton' type="button"
                        ng-repeat="item in productsForSubCategory"
                        ng-if="desiChaiFilterIds.indexOf(item.id)<0 && baarishWaliChaiFilterIds.indexOf(item.id)<0 && item.taxCode!= 'GIFT_CARD' "
                        ng-class="{vegButton:item.attribute == 'VEG',nonVegButton:item.attribute == 'NON_VEG'}"
                        ng-click="addNewProductToOrderItemArray(item)"
                >
                        <span data-ng-if="item.inventoryTracked" class="inventory-badge">
                        {{getDimensionInventoryForProductFormatted(item.id)}}</span>
                    <span>{{getProductAliasNameIfPresent(item.name,item.productAliasName)}}</span>
                    <span data-ng-if="getExpiryInventoryForProduct(item.id) > 0" class="inventory-badge-left">
                        {{getExpiryInventoryForProduct(item.id)}}</span>
                    <span style="position: absolute; top: -10px; right: 10px; background-color: red; padding: 0 5px; color: white;">{{item["orderStatus"]}}</span>
                    <!-- <span class="inventory-badge" data-ng-if="item.inventoryTracked && !isLiveInventoryEnabled()">
                    {{getInventoryForProductFormatted(item.id)}}</span> -->
                </button>
                <div class="col-xs-12 container-fluid container-product-display"
                     ng-show="productsForSubCategory.length == 0">
                    <p class="text-center" style="background-color: #b3d4fc">Sorry,
                        no products to display in this subcategory</p>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-6 transactionDisplayCOD" ng-if="isCOD">
                <label>Enquiry Items for {{outletName()}}</label>
                <table class="table table-bordered table-striped"
                       style="background: #fff;">
                    <tr>
                        <th>Name</th>
                        <th>Available</th>
                        <th>Requested</th>
                        <th>Was Replaced?</th>
                    </tr>
                    <tr data-ng-repeat="item in enquiryItems">
                        <td>{{item.name}} - {{item.dimension}}</td>
                        <td>{{item.availableQuantity}}</td>
                        <td><input type="number" data-ng-model="item.orderedQuantity"
                                   class="form-control"/></td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn"
                                        data-ng-class="{'btn-success':item.replacementServed=='true','btn-default':item.replacementServed!='true'}"
                                        data-ng-click="item.replacementServed='true'">Y
                                </button>
                                <button type="button" class="btn"
                                        data-ng-class="{'btn-success':item.replacementServed=='false','btn-default':item.replacementServed!='false'}"
                                        data-ng-click="item.replacementServed='false'">N
                                </button>
                            </div>
                        </td>
                    </tr>
                </table>
                <input type="button" class="btn btn-success pull-right"
                       style="margin-rigth: 10px;" value="Save Enquiry"
                       data-ng-click="saveEnquiry()"/>
            </div>
            <div class="col-xs-6 transactionDisplayCAFE"
                 id="customerSocketWidget" data-ng-if="(isCafe || isTakeaway) && !isSpecialOrder">
                <div class="row">
                    <div data-ng-if="isCustomerSocketConnected || detailsEntered"
                         class="col-xs-12 alert-success text-center text-uppercase"><strong>Customer Screen
                        Connected</strong>
                    </div>
                    <!--pairing status : {{isCustomerSocketConnected}}-->
                    <div data-ng-if="!isCustomerSocketConnected"
                         class="col-xs-12 alert-warning text-center text-uppercase">
                        <strong>Customer screen is not paired</strong>
                    </div>
                    <div data-ng-if="customerPendingCardInfo != null && customerPendingCardInfo.hasCard && customerPendingCardInfo.cardAmount > 0.00"
                         class="col-xs-12 text-center text-uppercase" style="height: 16px; color: blue;">
                        <blink><strong>Card Amount : {{customerPendingCardInfo.cardAmount}}</strong></blink>
                    </div>
                    <div class="col-xs-12 text-center text-uppercase" style="height: 16px; color: blue;" data-ng-show="loyalteaPoints > 0 && showLoyalTeaInfo">
                        <blink><strong>Loyaltea Points: {{loyalteaPoints}}</strong></blink>
                        </div>
                    <div class="col-xs-12 text-center text-uppercase" style="height: 16px; color: blue;" data-ng-show="freeChaiCount > 0 && showLoyalTeaInfo">
                        <blink><strong>LoyalTeas count: {{freeChaiCount}}</strong></blink>
                    </div>
                    <div data-ng-if="chaayosCash.subscriptionInfoDetail == null || !chaayosCash.subscriptionInfoDetail.hasSubscription"
                         class="col-xs-12 text-center text-uppercase" style="height: 30px; color: red;">
                        <blink><strong>Suggest Chaayos Select Membership</strong></blink>
                    </div>
                    <div data-ng-if="customerPendingCardInfo != null && (!customerPendingCardInfo.hasCard || customerPendingCardInfo.cardAmount <= 0.00)"
                         class="col-xs-12 text-center text-uppercase" style="height: 25px; color: red;">
                        <blink><strong>Suggest Gift Card</strong></blink>
                    </div>
                    <button name="availFreeChai" class="btn btn-warning center-block"
                            style="margin-top: 10px; margin-bottom: 10px;"
                            data-ng-if="!isCOD && showLoyalTeaRedeemption"
                            data-ng-click="loyalteaRedeemptionCtrl()">Redeem Loyaltea
                     </button>                  
                    
                    <div data-ng-if="isCustomerSocketConnected || detailsEntered" class="col-xs-12 socketActive">
                        <div data-ng-repeat="(key,template) in renderedTemplate track by $index"
                             class="activity" data-ng-class="{activeState : $last}">
                            <span data-ng-bind-html="template"></span>
                        </div>
                        <div class="activity" data-ng-show="chaayosCash.hasCash">Chaayos Cash:
                            {{chaayosCash.cashAmount}}
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-xs-12">
                        <button name="customerRejected" class="btn btn-warning center-block"
                                data-ng-if="!customerNameManual"
                                data-ng-hide="customerRejectBtnHidden"
                                data-ng-click="customerNotInterested();">Customer
                            Rejected
                        </button>
                        <!--<button name="customerRejected" class="btn btn-warning center-block" style="margin-top: 5px"
                                data-ng-if="!customerNameManual"
                                data-ng-hide="customerRejectBtnHidden || faceProcessStarted"
                                data-ng-click="customerFaceSkipped();">Face Skip
                        </button>-->
                        <button name="otpButton" class="btn btn-warning center-block"
                                data-ng-if="!customerNameManual" data-ng-hide="otpBtnHidden"
                                data-ng-click="otpNotReceived();">OTP not received
                        </button>
                        <!-- <button name="otpButton" class="btn btn-warning center-block"
                                data-ng-if="!customerNameManual" data-ng-show="switchOtpBtn"
                                data-ng-click="switchOtp();">Switch OTP
                        </button> -->

                        <button name="availFreeChai" class="btn btn-warning center-block"
                                data-ng-if="showReapplyFreeChai"
                                data-ng-click="addRedemptionToOrder()">Avail Free Chai
                        </button>
                        <button name="skipFreeChai" class="btn btn-warning center-block"
                                data-ng-if="showSkipFreeChai"
                                data-ng-click="removeRedemption()">Skip Free Chai
                        </button>
                        <!-- <button name="skipFeedBack" class="btn btn-warning center-block"
                                data-ng-show="feedbackPending"
                                data-ng-click="skipFeedback()">Skip feedback
                        </button> -->
                        <button name="skipEmail" class="btn btn-warning center-block"
                                data-ng-show="showEmailSkipBtn"
                                data-ng-click="skipEmailByCRE()">Skip Email
                        </button>

                        <!--<button name="freeChaiDelivery" class="btn btn-danger center-block"-->
                        <!--data-ng-show="showFreeChaiDelivery"-->
                        <!--data-ng-click="sendFreeChaiDeliverySms()">Free Chai Delivery-->
                        <!--</button>-->
                        <!--<button class="btn btn-warning center-block"
                            data-ng-hide="hideFirstFreeChaiBtn"
                            data-ng-click="skipFreeChai()">Skip first free chai</button>-->
                    </div>
                </div>
            </div>
            <div class="col-xs-6"
                 ng-class="{transactionDisplayCOD : isCOD, transactionDisplayCAFE : (isCafe || isTakeaway)}">
                <div class="transactionDisplay">
                    <p class="transactionRow">
                        Total <span class="transactionAmount">{{transactionObj.totalAmount.toFixed(2)}}</span>
                    </p>
                    <p class="transactionRow">
                        Promotional Offer <span class="transactionAmount">{{transactionObj.discountDetail.promotionalOffer.toFixed(2)}}</span>
                    </p>

                    <p class="transactionRow">
                        Discount <span class="transactionAmount">{{transactionObj.discountDetail.discount.value.toFixed(2)}}</span>
                    </p>
                    <p class="transactionRow">
                        Total Discount <span class="transactionAmount">{{transactionObj.discountDetail.totalDiscount.toFixed(2)}}</span>
                    </p>
                    <p class="transactionRow">
                        Taxable <span class="transactionAmount">{{transactionObj.taxableAmount.toFixed(2)}}</span>
                    </p>
                    <p class="transactionRow"
                       data-ng-if="offerCode.trim().length > 0 && (offerApplied || offerManual)">
                        Coupon: <strong>{{offerCode}}</strong> - Actions : <span
                            data-ng-bind-html="appliedOfferMessage"></span>
                    </p>

                    <p data-ng-repeat="taxItem in transactionObj.taxes track by $index" class="transactionRow">
                        {{taxItem.code}} @ {{taxItem.percentage}} %<span
                            class="transactionAmount">{{taxItem.value.toFixed(2)}}</span>
                    </p>
                    <p class="transactionRow">
                        Total Tax<span
                            class="transactionAmount">{{transactionObj.tax.toFixed(2)}}</span>
                    </p>
                    <p class="transactionRow" style="color: blue">
                        Savings <span class="transactionAmount" style="color: blue">{{transactionObj.savings.toFixed(2)}}</span>
                    </p>
                    <p data-ng-if="prepaidAmount" class="transactionRow" style="color: blue">
                        Prepaid Amount <span class="transactionAmount" style="color: blue">{{prepaidAmount}}</span>
                    </p>
                    <p
                            style="text-align: left; margin-left: 10px; font: 26px bold; font-family: 'lane';">
                        Payable
                        <span
                                style="float: right; margin-right: 10px; color: peru; font: 26px bold; font-family: 'lane'">

                                {{prepaidAmount != undefined
                                    ? transactionObj.paidAmount.toFixed(2) - prepaidAmount
                                    : transactionObj.paidAmount.toFixed(2)}}
                        </span>
                    </p>
                    <p data-ng-if="purchasedGiftCardAmount!=0" class="transactionRow">
                        Gift Card Amount<span
                            class="transactionAmount">{{purchasedGiftCardAmount}}</span>
                    </p>
                    <p data-ng-if="purchasedChaayosSelectAmount!=0" class="transactionRow">
                        Chaayos Select Amount<span
                            class="transactionAmount">{{purchasedChaayosSelectAmount}}</span>
                    </p>
<!--                    <p data-ng-if="(purchasedGiftCardAmount!=0 || purchasedChaayosSelectAmount!=0)"-->
<!--                            style="text-align: left; margin-left: 10px; font: 26px bold; font-family: 'lane';">-->
<!--                        Total Amount-->
<!--                        <span-->
<!--                                style="float: right; margin-right: 10px; color: black; font: 26px bold; font-family: 'lane'">-->

<!--                                {{prepaidAmount != undefined ? (transactionObj.paidAmount - prepaidAmount + purchasedGiftCardAmount + purchasedChaayosSelectAmount).toFixed(2) : (transactionObj.paidAmount + purchasedGiftCardAmount + purchasedChaayosSelectAmount).toFixed(2)}}-->
<!--                        </span>-->
<!--                    </p>-->

                    <p ng-if="tableService"
                       style="text-align: left; margin-left: 10px; font: 26px bold; font-family: 'lane';">
                        Table Number <span
                            style="float: right; margin-right: 10px; color: peru; font: 26px bold; font-family: 'lane'">{{tableNumber}}</span>
                    </p>
                </div>
            </div>
            <input type="button" class="btn-primary" value="Swiggy Catalog" style="display: none;"
                   data-ng-click="getSwiggyCatalog()"/>
        </div>

    </div>
    <div class="col-xs-4 container-order-display">
        <div class="col-xs-4"
             style="font-family: Arial, sans-serif; font-weight: bold; font-size: 20px; text-align: right; color: red; width: auto;">
            <timer interval="1000">{{mminutes}} min{{minutesS}} :
                {{sseconds}} sec{{secondsS}}
            </timer>
        </div>
        <div class="row" style="margin: 3px;">
            <div class="col-xs-12" style="position: relative;">
                <!-- <button data-ng-if="isCOD" type="button" class="btn btn-lg btn-primary btn-lg-text-sm"
                   data-ng-click="discountModalOpen()">Discount</button> -->
                <button class="btn btn-lg btn-primary btn-lg-text-sm" data-ng-click="openGiftCardModal('SELF')"
                        data-ng-if="false && !isSpecialOrder && orderType!='subscription' &&  !isCOD && !isPaidEmployeeMeal">
                    Gift Card
                </button>
                <button class="btn btn-lg btn-primary btn-lg-text-sm" data-ng-click="validateGiftCard()"
                        style="width: 87px;"
                        data-ng-show=" offerApplicableForUnitExists.showIcon
                   && orderType!='subscription' && !isCOD && orderItemArray.length > 0
                   && (((isCustomerSocketConnected && cutomerInterested) &&(!isPaidEmployeeMeal && !isWastageOrder &&  !isComplimentaryOrder)|| detailsEntered != null )) && !activateDirectGiftCardPurchase">
                    Wallet <br/> Suggestion
                </button>
                <button class="btn btn-lg btn-primary btn-lg-text-sm" data-ng-click="openGiftCardModal('ECARD')"
                        style="width: 87px;"
                        data-ng-if="!isSpecialOrder && orderType!='subscription' && !isCOD && !isPaidEmployeeMeal &&activateDirectGiftCardPurchase">
                    Wallet <br/> Purchase
                </button>
                <button class="btn btn-lg btn-primary btn-lg-text-sm" data-ng-click="openGiftCardModal('GYFTR')"
                        style="width: 87px;"
                        data-ng-if="!isSpecialOrder && orderType!='subscription' && !isCOD && !isPaidEmployeeMeal">
                    Gyftr <br/> Voucher
                </button>
                <button class="btn btn-lg btn-primary btn-lg-text-sm" data-ng-click="openChaayosSelectModal('SELECT')"
                        style="width: 87px;"
                        data-ng-if="!isSpecialOrder && orderType!='subscription' && !isCOD && !isPaidEmployeeMeal &&
                        (chaayosCash.subscriptionInfoDetail===null || (chaayosCash.subscriptionInfoDetail != null && subscriptionValidiltyDays <= 30)
                        || (chaayosCash.subscriptionInfoDetail != null && !chaayosCash.subscriptionInfoDetail.hasSubscription))
                         ">
                    Purchase <br/> Select
                </button>
                <button type="button" class="btn btn-lg btn-warning btn-lg-text-sm"
                        data-ng-click="offerModalOpen()"
                        data-ng-show="!offerApplied && showCouponBtn()">Coupon
                </button>
                <button type="button" class="btn btn-lg btn-warning btn-lg-text-sm"
                        style="width: 105px; text-align: center"
                        data-ng-click="offerModalOpen(true)"
                        data-ng-show="!offerApplied && chaayosCash.hasCash && !secondChai">Chaayos Cash
                </button>
                <button type="button" class="btn btn-lg btn-warning btn-lg-text-sm"
                        style="width: 105px; text-align: center"
                        data-ng-click="suscriptionModalOpen(chaayosCash.subscriptionInfoDetail)"
                        data-ng-show="!offerApplied && chaayosCash.subscriptionInfoDetail.hasSubscription">Apply
                    Select
                </button>
                <button type="button"
                        class="btn btn-lg btn-success btn-lg-text-sm"
                        data-ng-click="remarkModalOpen()">Remarks
                </button>

                <button class="btn btn-lg btn-warning btn-lg-text-sm" data-ng-click="clear()">Clear</button>
                <button class="btn btn-lg btn-danger btn-lg-text-sm"
                        data-ng-click="goToCoverScreen(true)">Cancel
                </button>
            </div>
        </div>
        <!-- <div class="row" style="margin: 3px;" data-ng-if="recommendationDetail != null">
         <div class="col-xs-6">
             <p class="orderText" style="text-align: left; margin: 10px;">{{recommendationDetail.name}} - {{recommendationDetail.desc}}</p>

         </div>
            <div class="col-xs-6">
            	<button data-ng-if="!recommendationNotification" type="button" class="btn btn-md btn-success btn-md-text-sm"
                        data-ng-click="skipRecommendationOffer()">Skip
                </button>
                <button type="button" class="btn btn-md btn-success btn-md-text-sm"
                        data-ng-click="addRecommendation()">Add
                </button>
                 <button data-ng-if="recommendationDetail.offer && !recommendationOfferApplied" type="button" class="btn btn-md btn-success btn-md-text-sm"
                        data-ng-click="applyRecommendationOffer()">Apply Offer
                </button>
                 <button data-ng-if="recommendationDetail.offer && recommendationOfferApplied" type="button" class="btn btn-md btn-success btn-md-text-sm"
                        data-ng-click="clearRecommendation()">Skip Offer
                </button>
                <button data-ng-if="isDev()" type="button" class="btn btn-md btn-success btn-md-text-sm"
                        data-ng-click="getRecommendation()">Re Recommend
                </button>
            </div>
        </div> -->
        <div class="row" style="height: 500px; max-height: 500px;">
            <div class="col-xs-12" style="position: relative;">
                <div class="orderItemsDisplay">
                    <div class="orderItemBox container-fluid"
                         data-ng-class="{orderItemComplimentaryBox:orderItem.orderDetails.hasBeenRedeemed == true}"
                         data-ng-repeat="orderItem in orderItemArray.slice().reverse() track by $index"
                         close="orderItemArray.splice(index, 1)">

                        <!--All In-->

                        <div class="row">
                            <div class="col-xs-2" style="padding: 2px;">
                                <button class="btn btn-primary"
                                        data-ng-click="customizeNewModalOpen(orderItem)"
                                        data-ng-show="!isSubscriptionProduct(orderItem)">Customize
                                </button>
                            </div>

                            <div class="col-xs-6">
                                <div class="row">
                                    <div class="col-xs-9">
                                        <p class="orderText" style="text-align: left; margin: 10px;">
                                        <span class="inventory-badge" data-ng-if="item.inventoryTracked">
                        					{{getDimensionInventoryForProductFormatted(orderItem.productDetails.id)}}
                        				</span>
                                            {{getProductAliasNameIfPresent(orderItem.productDetails.name,orderItem.productDetails.productAliasName)}} <span class="inventory-badge-left"
                                                                                    data-ng-if="item.inventoryTracked">{{getInventoryForProductFormatted(orderItem.productDetails.id)}}
                                        </span>
                                            <span data-ng-show="orderItem.productDetails.parentProductId!=null">
                                                -{{orderItem.productDetails.parentProductName}}
                                            </span>
                                        </p>
                                    </div>
                                    <div class="col-xs-3">
                                        <p class="itemPrice"
                                           ng-show="orderItem.orderDetails.amount != orderItem.orderDetails.totalAmount">
                                            {{orderItem.orderDetails.amount.toFixed(2)}}
                                            ({{orderItem.orderDetails.totalAmount.toFixed(2)}})</p>
                                        <p class="itemPrice"
                                           ng-show="orderItem.orderDetails.amount == orderItem.orderDetails.totalAmount">
                                            {{orderItem.orderDetails.amount.toFixed(2)}}</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xs-4 btn-group" style="padding-left: 20px;" role="group"
                                 data-ng-show="orderItem.orderDetails.hasBeenRedeemed == false && orderItem.orderDetails.isCombo == false">
                                <button type="button" class="btn btn-default"
                                        data-ng-repeat="price in orderItem.productDetails.prices"
                                        data-ng-disabled="(price.isDeliveryOnlyProduct!=null && price.isDeliveryOnlyProduct) || isMonkCalibration()"
                                        data-ng-click="sizeBtnClicked(orderItem,$index)"
                                        data-ng-class="{ 'sizeBtnClicked': orderItem.orderDetails.dimension == price.dimension}"
                                        data-ng-if="price.dimension != 'TF2' && price.dimension != 'TF4'">
                                    {{price.dimension.substring(0,1)}}
                                </button>
                                <button type="button" class="btn btn-default"
                                        data-ng-repeat="price in orderItem.productDetails.prices"
                                        data-ng-disabled="(price.isDeliveryOnlyProduct!=null && price.isDeliveryOnlyProduct) || isMonkCalibration()"
                                        data-ng-click="sizeBtnClicked(orderItem,$index)"
                                        data-ng-class="{ 'sizeBtnClicked': orderItem.orderDetails.dimension == price.dimension}"
                                        data-ng-if="price.dimension == 'TF2' || price.dimension == 'TF4'">
                                    {{price.dimension.substring(0,1) + price.dimension.substring(2,3)}}
                                </button>
                            </div>
                        </div>
                        <div class="row"
                             data-ng-repeat="data in orderItem.orderDetails.composition.menuProducts track by $index">
                            <div class="col-xs-2" style="padding: 0px;">
                                <p class="orderText" style="text-align: center; margin: 10px;">{{data.name}}</p>
                            </div>
                            <div class="col-xs-2" style="padding: 2px; text-align: right">
                                <button class="btn-md btn-warning"
                                        data-ng-click="customizeNewModalOpen(data.item, data)">Customize
                                </button>
                            </div>

                            <div class="col-xs-8">
                                <div class="row">
                                    <div class="col-xs-6">
                                        <p class="orderText" style="text-align: left; margin: 10px;">
                                            {{data.product.name}}
                                            - {{data.dimension.code}}</p>
                                    </div>
                                    <div class="col-xs-6">
                                        <p class="orderText" style="margin: 10px;">
                                            {{getCustomizationAbb(data.item)}}</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-xs-2" style="padding: 0px;">
                                <div class="input-group" style="z-index: 2;">
									<span data-ng-click="decreaseCount(orderItem)"
                                          data-ng-show="orderItem.orderDetails.hasBeenRedeemed == false && orderItem.productDetails.taxCode !='GIFT_CARD' &&
                                          orderItem.productDetails.taxCode !='COMBO' && orderItem.productDetails.parentProductId==null && !isSubscriptionProduct(orderItem) && !(orderItem.orderDetails.complimentaryDetail.isComplimentary && orderItem.productDetails.subType == 3868)"
                                          class="input-group-addon"
                                          style="font-weight: bolder; cursor: pointer;">-</span>
                                    <input type="number" name="quantity" class="form-control"
                                           min = 1
                                           style="text-align: center; width: 50px;"
                                           data-ng-model="orderItem.orderDetails.quantity"
                                           data-ng-change="calculatePaidAmount()"
                                           placeholder="orderItem.orderDetails.quantity"
                                           data-ng-disabled="orderItem.orderDetails.hasBeenRedeemed == true || orderItem.productDetails.parentProductId!=null || isSubscriptionProduct(orderItem) || (orderItem.orderDetails.complimentaryDetail.isComplimentary && orderItem.productDetails.subType == 3868)">
                                    <span data-ng-click="increaseCount(orderItem)"
                                          data-ng-show="orderItem.orderDetails.hasBeenRedeemed == false && orderItem.productDetails.taxCode !='GIFT_CARD' &&
                                          orderItem.productDetails.taxCode !='COMBO' && orderItem.productDetails.parentProductId==null  && !isSubscriptionProduct(orderItem) && !(orderItem.orderDetails.complimentaryDetail.isComplimentary && orderItem.productDetails.subType == 3868)"
                                          class="input-group-addon"
                                          style="font-weight: bolder; cursor: pointer;">+</span>
                                </div>
                            </div>
                            <div class="col-xs-6">
                                <p class="orderText" style="text-align: center; margin: 10px;">
                                    {{getCustomizationAbb(orderItem)}}</p>
                                <p data-ng-if="orderItem.productDetails.taxCode =='GIFT_CARD'"
                                   class="orderText" style="text-align: center; margin: 10px;">
                                    Card Valid:{{orderItem.orderDetails.isCardValid?"YES":"NO"}}</p>
                            </div>
                            <div class="col-xs-4">
                                <div class="row"
                                     data-ng-show="orderItem.orderDetails.hasBeenRedeemed == false && orderItem.productDetails.parentProductId==null">
                                    <div class="col-xs-6">
                                        <button class="btn btn-danger" data-ng-disabled="orderItem.productDetails.id == 1043"
                                                data-ng-click="deleteItem($index)">Delete
                                        </button>
                                    </div>
                                </div>
                                <div class="row">
									<span class="badge badge-info"
                                          data-ng-if="orderItem.orderDetails.discountDetail.promotionalOffer > 0">
										Discount:
										{{orderItem.orderDetails.discountDetail.promotionalOffer}}</span>
                                </div>
                            </div>
                        </div>
                        <div style="width:100%;display:flex; flex-direction: row;justify-content: flex-end;align-items: center">
                            <div ng-show = "orderItem.productDetails.type==5 && customerBasicInfo!= null && customerBasicInfo!=undefined && customerBasicInfo.id!=null && customerBasicInfo.id !=undefined && customerBasicInfo.id>5 && !orderItem.chaiSavedFromDineIn "class="row" style="background-color:#FFF8DC">
                                <div >
                                    <button class="btn-heart1" ng-click="markFavChai1(orderItem, true,$index)">
                                        <i data-ng-show="!orderItem.isFavChaiMarked "class="fa fa-2x fa-heart-o" style="color:red"></i>
                                        <i data-ng-show="orderItem.isFavChaiMarked " class="fa fa-2x fa-heart" style="color:red"></i>
                                        <text data-ng-show ="!orderItem.isFavChaiMarked " style="font-size: 13px;font-weight:bold">Save This Chai As Meri Wali Chai</text>
                                        <text data-ng-show ="orderItem.isFavChaiMarked" style="font-size: 13px;font-weight:bold;color:#00BD60">Saved As Meri Wali Chai</text>
                                    </button>
                                </div>
                                <!--<div ng-if="(orderItem.isFavChaiMarked==undefined || orderItem.isFavChaiMarked==null) &&(orderItem.productDetails.type==5 && customerBasicInfo!= null && customerBasicInfo!=undefined && customerBasicInfo.id!=null && customerBasicInfo.id !=undefined && customerBasicInfo.id>5)">
                                    <button class="btn-heart1" ng-click="markFavChai1(orderItem,true,$index)">
                                        <i data-ng-show="!orderItem.isFavChaiMarked " class="fa fa-2x fa-heart-o" style="color:red"></i>
                                        <i data-ng-show="orderItem.isFavChaiMarked  " class="fa fa-2x fa-heart" style="color:red"></i>
                                        <text data-ng-show ="!orderItem.isFavChaiMarked " style="font-size: 13px;font-weight:bold">Save This Chai As Meri Wali Chai</text>
                                        <text data-ng-show ="orderItem.isFavChaiMarked " style="font-size: 13px;font-weight:bold;color: #00BD60">Saved As Meri Wali Chai</text>
                                    </button>
                                </div>-->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-xs-12 text-center" style="margin-top: 30px;">
                <!-- <a href="#" class="btn btn-default" data-ng-show="!isCOD"
                    data-ng-click="redeemModalOpen()">Redeem Check</a> -->
                <a href="#" class="btn btn-default" data-ng-show="isCOD"
                   data-ng-click="switchOutlet()">Switch Outlet</a>
                <a href="#" class="btn btn-default" data-ng-show="isCOD && !orderChecked"
                   data-ng-click="checkOrder()">Order Check</a>
                <a href="#" class="btn btn-default" data-ng-show="isCOD && orderChecked"
                   data-ng-click="placeOrder()">Place Order</a>
                <a href="#" class="btn btn-default" data-ng-show="isCOD && orderChecked"
                   data-ng-click="uncheckOrder()">Edit Order</a>
                <!-- <a href="#" class="btn btn-danger pull-right" data-ng-show="!orderChecked"
                  data-ng-click="goToCoverScreen(true)">Cancel</a> -->
                <!-- <a href="#" class="btn btn-danger" style="margin-left: 10px;"
                   data-ng-show="orderType!='subscription' && !feedbackPending && recommendationPending && orderItemArray.length > 0 && isCustomerSocketConnected && customerEnteredName != null"
                   data-ng-click="getRecommendation()">Recommend</a> -->
                <a href="#" class="btn btn-warning" style="margin-left: 10px ; border-radius: 100px 100px 100px 100px;"
                   data-ng-show="offerApplicableForUnitExists!=null && offerApplicableForUnitExists.validateUnit && !offerApplicableForUnitExists.showIcon
                   && !isCOD && orderItemArray.length > 0"
                   data-ng-click="applyAutoApplicableOffer()">PROCESS ORDER</a>
                <a href="#" class="btn btn-success" style="margin-left: 10px ; border-radius: 100px 100px 100px 100px;"
                   data-ng-show="offerApplicableForUnitExists.showIcon &&  offerApplicableForUnitExists.showIcon && orderType!='subscription' && !isCOD && orderItemArray.length > 0 && !isLoyalTeaRedeemed() && isSelectPresent() && !offerApplied && (chaayosCash.subscriptionInfoDetail===null || (chaayosCash.subscriptionInfoDetail != null && !chaayosCash.subscriptionInfoDetail.hasSubscription)) &&
                   (((isCustomerSocketConnected && cutomerInterested) &&(!isPaidEmployeeMeal && !isWastageOrder &&  !isComplimentaryOrder)|| detailsEntered != null ))"
                   data-ng-click="suggestMembership('SUGGEST SELECT')">SUGGEST SELECT</a>
                <a href="#" class="btn btn-success" style="margin-left: 10px ; border-radius: 100px 100px 100px 100px;"
                   data-ng-show=" offerApplicableForUnitExists.showIcon
                   && orderType!='subscription' && !isCOD && orderItemArray.length > 0
                   && ((isCustomerSocketConnected && cutomerInterested) &&(!isPaidEmployeeMeal && !isWastageOrder &&  !isComplimentaryOrder)|| detailsEntered != null)"
                   data-ng-click="validateGiftCard()">SUGGEST WALLET</a>
                <a href="#" class="btn btn-danger"
                   data-ng-show=" offerApplicableForUnitExists.showIcon &&
                   orderType!='subscription' && !isCOD && orderItemArray.length > 0
                   && ((!isCustomerSocketConnected || !cutomerInterested) ||(isPaidEmployeeMeal|| isWastageOrder || isComplimentaryOrder)|| detailsEntered != null)"
                   data-ng-click="startRegularOrder()">Make Payment</a>
                <a href="#" class="btn btn-danger" style="margin-left: 10px;"
                   data-ng-show="orderType=='subscription'"
                   data-ng-click="settlementModalOpen()">Create Subscription</a>
                <a href="#" class="btn btn-default" data-ng-show="isCOD && showReapplyFreeChai"
                   data-ng-click="addRedemptionToOrder()">Avail Free Chai</a>
                <a href="#" class="btn btn-default" data-ng-show="isCOD && showSkipFreeChai"
                   data-ng-click="removeRedemption()">Skip Free Chai</a>
            </div>
        </div>
    </div>

    <div class="col-xs-3" ng-if="toShowCustomerOneViewSection || isNewCustomer">
        <div class="row"
             style="margin: 3px; background-color: #FFFFFF; padding: 1rem; box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;">
            <div class="col-xs-12" style="position: relative;">
                <h3 style="font-weight: bold; color: green; font-size: 30px;font-family: cursive;"
                    data-ng-if="chaayosCash != null && chaayosCash != undefined &&
                    chaayosCash.subscriptionInfoDetail != null && chaayosCash.subscriptionInfoDetail.hasSubscription
                    && !checkChaayosSelectExpiry(chaayosCash)">{{subProductName}} Active</h3>
                <h3 style="font-weight: bold; color: orange; font-size: 30px ;font-family: cursive;"
                    data-ng-if="chaayosCash != null && chaayosCash != undefined &&
                    chaayosCash.subscriptionInfoDetail != null && checkChaayosSelectExpiry(chaayosCash)">Chaayos Select
                    Expiring Soon</h3>
                <h3 style="font-weight: bold; color: red; font-size: 30px;font-family: cursive;"
                    data-ng-if="chaayosCash != null && chaayosCash != undefined &&
                    chaayosCash.subscriptionInfoDetail != null && (chaayosCash.subscriptionInfoDetail.daysLeft<=0 || chaayosCash.subscriptionInfoDetail.remainingChai==0)
                    && !chaayosCash.subscriptionInfoDetail.hasSubscription">{{subProductName}} Expired </h3>
                <p style="font-size: 20px" ng-if="chaayosCash != null && chaayosCash != undefined &&
                    chaayosCash.subscriptionInfoDetail != null && chaayosCash.subscriptionInfoDetail != undefined &&
                    chaayosCash.subscriptionInfoDetail.overAllSaving != null && chaayosCash.subscriptionInfoDetail.overAllSaving != undefined
                        && chaayosCash.subscriptionInfoDetail.hasSubscription">
                    <span style="font-weight: bold;font-family: cursive;">Valid Till ,{{chaayosCash.subscriptionInfoDetail.day}}th {{chaayosCash.subscriptionInfoDetail.month}} {{chaayosCash.subscriptionInfoDetail.year}}</span>
                </p>
                <p style="font-size: 20px" ng-if="chaayosCash != null && chaayosCash != undefined &&
                    chaayosCash.subscriptionInfoDetail != null && chaayosCash.subscriptionInfoDetail != undefined &&
                    chaayosCash.subscriptionInfoDetail.overAllSaving != null && chaayosCash.subscriptionInfoDetail.overAllSaving != undefined
                    && !chaayosCash.subscriptionInfoDetail.hasSubscription">
                    <span style="font-weight: bold;font-family: cursive;">Was Valid Till ,{{chaayosCash.subscriptionInfoDetail.day}}th {{chaayosCash.subscriptionInfoDetail.month}} {{chaayosCash.subscriptionInfoDetail.year}}</span>
                </p>
                <p style="font-size: medium" ng-if="chaayosCash != null && chaayosCash != undefined &&
                    chaayosCash.subscriptionInfoDetail != null && chaayosCash.subscriptionInfoDetail != undefined &&
                    chaayosCash.subscriptionInfoDetail.overAllSaving != null && chaayosCash.subscriptionInfoDetail.overAllSaving != undefined">
                    Overall {{subProductName}} Savings, <span style="font-weight: bold;font-size: large">Rs.{{chaayosCash.subscriptionInfoDetail.overAllSaving}}</span>
                </p>
                <p style="font-size: medium" ng-if="chaayosCash != null && chaayosCash != undefined &&
                    chaayosCash.subscriptionInfoDetail != null && chaayosCash.subscriptionInfoDetail != undefined && chaayosCash.isPrePaidPlan !=undefined && chaayosCash.isPrePaidPlan !=null && chaayosCash.isPrePaidPlan">
                    Remaining Chai , <span ng-class="{style1 : remainingChai !=undefined && remainingChai !=null && remainingChai>0 , style2 :remainingChai !=undefined && remainingChai !=null && remainingChai>0}">{{chaayosCash.subscriptionInfoDetail.remainingChai}}</span>
                </p>

                <h3 style="font-weight: bold; color: blue; font-size: 20px" ng-if="toShowCustomerOneViewSection">
                    {{customerType}}</h3>
                <h3 style="font-weight: bold; color: blue; font-size: 20px"
                    ng-if="isNewCustomer && !toShowCustomerOneViewSection">First Time Customer</h3>
                <div style="font-size: medium" ng-if="isNewCustomer || customerType=='First Time Customer'">
                    <ul>
                        <li style="margin-left: -20px;">Welcome to Chaayos</li>
                        <li style="margin-left: -20px;">Ask for Phone number (Explain 2nd Free chai)</li>
                        <li style="margin-left: -20px;">Suggest Desi chai (Explain personalize chai)</li>
                        <li style="margin-left: -20px;">Suggest Core products</li>
                    </ul>
                </div>
                <p style="font-size: medium" ng-if="customerType=='Regular Customer'">
                    Welcome back, <span style="font-weight: bold">{{customerEnteredName}}</span>, hope you enjoyed your
                    last order during your last visit, would you like me to repeat the same.
                </p>
                <p style="font-size: medium" ng-if="customerType=='Outstation Customer'">
                    Welcome to, {{currentCafeRegion}}, <span style="font-weight: bold">{{customerEnteredName}}</span>,
                    hope you enjoyed your last order during your last visit at <span style="font-weight: bold">{{lastUnit}}, {{previousCafeRegion}}</span>,
                    would you like me to repeat the same.
                </p>
                <div ng-if="customerType != 'First Time Customer' && !isNewCustomer">
                    <p style="font-weight: bold">
                        Last Order: <span
                            style="border-radius:20px;padding: 0 5px;margin-left: 20px;background-color: green;color: white;white-space: nowrap;">{{lastUnit}}</span>
                    </p>
                    <div style="height: 180px; overflow-y: scroll;">
                        <ul ng-repeat="order in lastOrder">
                            <li style="list-style-type: none;margin-left: -40px;border-bottom: 1px dashed lightgrey;">
                                <span style="font-weight: bold">{{order["productName"]}}</span>
                                <span style="float: right; margin-right: 40px; font-weight: bold">
                                    X {{order["quantity"]}}
                                </span>
                                <br>
                                <div ng-repeat="addOn in order['addOns']" ng-if="order['addOns'].length > 0"
                                     style="border: 1px solid green;border-radius: 20px;padding: 0 5px;margin-right: 5px;display: inline-block">
                                    <span style="white-space: nowrap;">{{addOn}}</span>
                                </div>
                            </li>
                        </ul>
                    </div>

                    <p style="margin-left: -40px; margin-top: 30px; display: inline-block;"><span
                            style="border-radius:20px;padding: 0 5px;margin-left: 40px;background-color: green;color: white;white-space: nowrap;font-weight: bold;">{{orderSource}}</span>
                    </p>
                    <p style="margin-left: -40px; margin-top: 30px; display: inline-block;"><span
                            style="border-radius:20px;padding: 0 5px;margin-left: 40px;background-color: green;color: white;white-space: nowrap;font-weight: bold;">{{channelPartnerName}}</span>
                    </p>
                    <p style="margin-left: -40px; margin-top: 30px; display: inline-block;" ng-if="dataShow == true">
                        <span style="border-radius:20px;padding: 0 5px;margin-left: 40px;background-color: green;color: white;white-space: nowrap;font-weight: bold;">Last Order NPS : {{showFeedbackValue}}</span>
                    </p>
                    <p style="margin-left: -40px; margin-top: 30px; display: inline-block;" ng-if=" dataShow == false">
                        <span style="border-radius:20px;padding: 0 5px;margin-left: 40px;background-color: red;color: white;white-space: nowrap;font-weight: bold;">Last Order NPS : {{showFeedbackValue}}</span>
                    </p>
                    <!--<p style="font-weight: bold">Last Unit: <span>{{lastUnit}}</span></p>-->
                    <p style="color: red; font-weight: bold" ng-if="isFirstZomatoOrder == true">This is first Zomato
                        Order</p>
                </div>
            </div>
        </div>
        <div class="row"
             style="margin: 3px; background-color: #FFFFFF;  padding: 1rem; box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;"
             ng-if="customerType != 'First Time Customer' && !isNewCustomer">
            <div ng-if="totalFeedback!=0">
                <div class="col-xs-12" style="position: relative; font-size: 6rem;">
                    <div ng-if="feedbackCategory == 'Promoter'" class="text-center">🙂</div>
                    <div ng-if="feedbackCategory == 'Passive'" class="text-center">😐</div>
                    <div ng-if="feedbackCategory == 'Detractor'" class="text-center">🙁</div>
                </div>
                <div>
                    <ul style="list-style-type: square; font-weight: bold">
                        <li>Total Order FeedBacks: {{totalFeedback}}</li>
                        <li>Feedback Category: <span>{{feedbackCategory}}</span></li>
                        <li>
                            Last 3 Feedback:
                            <div style="display: flex; align-items: center">
                                <div
                                        style="background-color: green;width: 40px;height: 40px;padding: 4px 5px;border-radius: 50%;display: inline-block;margin-right: 10px">
                                    <p style="font-size: 20px;color: white; text-align: center">{{last3Feedback[0]}}</p>
                                </div>
                                <div
                                        style="background-color: green;width: 25px;height: 25px;padding: 0px 5px;border-radius: 50%;display: inline-block;margin-right: 10px">
                                    <p style="font-size: 16px;color: white; text-align: center">{{last3Feedback[1]}}</p>
                                </div>
                                <div
                                        style="background-color: green;width: 25px;height: 25px;padding: 0px 5px;border-radius: 50%;display: inline-block;margin-right: 10px">
                                    <p style="font-size: 16px;color: white; text-align: center">{{last3Feedback[2]}}</p>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>

                <div style="font-size: 2rem; font-weight: bold">{{howToConvinceFeedbackCustomer}}</div>
            </div>
            <div ng-if="totalFeedback==0">
                <h3 style="font-weight: bold; text-align: center">No Feedback Data Found</h3>
            </div>
        </div>
        <div class="row"
             style="margin: 3px; background-color: #FFFFFF;  padding: 1rem; box-shadow: rgba(0, 0, 0, 0.35) 0px 5px 15px;"
             ng-if="customerType != 'First Time Customer' && !isNewCustomer">
            <div ng-if="totalNPS!=0">
                <div class="col-xs-12" style="position: relative; font-size: 6rem;">
                    <div ng-if="NPSCategory == 'Promoter'" class="text-center">🙂</div>
                    <div ng-if="NPSCategory == 'Passive'" class="text-center">😐</div>
                    <div ng-if="NPSCategory == 'Detractor'" class="text-center">🙁</div>
                </div>
                <div>
                    <ul style="list-style-type: square; font-weight: bold">
                        <li>Total NPS FeedBacks: {{totalNPS}}</li>
                        <li>NPS Category: <span>{{NPSCategory}}</span></li>
                        <li>
                            Last 3 NPS:
                            <div style="display: flex; align-items: center">
                                <div
                                        style="background-color: green;width: 40px;height: 40px;padding: 4px 5px;border-radius: 50%;display: inline-block;margin-right: 10px">
                                    <p style="font-size: 20px;color: white; text-align: center">{{last3NPS[0]}}</p>
                                </div>
                                <div
                                        style="background-color: green;width: 25px;height: 25px;padding: 0px 5px;border-radius: 50%;display: inline-block;margin-right: 10px">
                                    <p style="font-size: 16px;color: white; text-align: center">{{last3NPS[1]}}</p>
                                </div>
                                <div
                                        style="background-color: green;width: 25px;height: 25px;padding: 0px 5px;border-radius: 50%;display: inline-block;margin-right: 10px">
                                    <p style="font-size: 16px;color: white; text-align: center">{{last3NPS[2]}}</p>
                                </div>
                            </div>
                        </li>
                    </ul>
                </div>

                <div style="font-size: 2rem; font-weight: bold">{{howToConvinceCustomer}}</div>
            </div>
            <div ng-if="totalNPS==0">
                <h3 style="font-weight: bold; text-align: center">No NPS Data Found</h3>
            </div>
        </div>

    </div>

    <div class="col-xs-3" ng-if="!toShowCustomerOneViewSection && !isNewCustomer">

    </div>
</div>









