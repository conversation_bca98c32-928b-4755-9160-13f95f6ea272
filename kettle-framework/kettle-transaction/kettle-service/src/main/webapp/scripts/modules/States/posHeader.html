<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<!--Taken From: http://codepen.io/betdream/details/iupIJ -->
<!-- Static navbar -->
<div class="row" ng-if="freeKettle && orderType!='subscription'">
	<div class="alert free-kettle text-center">This customer is eligible for availing Free First Kettle</div>
</div>
<div flash-message="5000"></div>
<div class="navbar navbar-default">
    <div class="navbar-header ">
        <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
            <span class="icon-bar"></span>
        </button>
        <a class="navbar-brand" href="#"><img src="images/logo.png"></a>
    </div>

    <div class="navbar-collapse collapse navigacija">

        <button class='btn btn-lg pull-right' style="margin-top: 10px;background-color:darkseagreen" type="button"
                ng-click="goToCoverScreen()">Cover Screen
        </button>

        <!--<button class='btn btn-lg' style="margin-top: 10px;background-color:darkseagreen" type="button"
                ng-click="goDetectPrinter()">Detect Printer</button>-->
        <h4 class= 'pull-right' ng-show="isCOD" style="margin: 20px;">{{outletName()}}</h4>

        <timer  style="font-family: Arial, sans-serif; font-weight: bold;font-size: 20px; text-align: center;
  color: red; margin-left: 50px;" interval="1000">{{mminutes}} min{{minutesS}} : {{sseconds}} sec{{secondsS}}</timer>



        <ul id="menu-header" class="nav navbar-nav navbar-left" style="margin-left: -150px;">
            <li class="dropdown" ng-repeat="catID in transactionMetadata.categories">
                <a href="#" data-toggle="dropdown" class="dropdown-toggle">{{catID.detail.name}}
                    <span ng-class="{'caret':(catID.content.length > 0)}"></span></a>

                <!--<div class="row productRow">
                    <a ui-sref=".products({ catId: subCatId.id })">
                        <button class='btn  posSubCategoryButton' type="button"
                                ng-repeat="subCatId in catID.content"
                                ng-show="">{{subCatId.name}}
                        </button>
                    </a>
                </div>-->

                <ul role="menu" ng-class="{'dropdown-menu ddmenu':(catID.content.length > 0)} " ng-style="{'min-width': catID.content.length * 125}">
                    <li id="categoryRow" class="dropdown pull-left" ng-repeat="subCatId in catID.content">
                        <a ui-sref=".products({ catId: subCatId.id })">
                            <button class='btn btn-lg posSubCategoryButton'>{{subCatId.name}}</button>
                        </a>
                    </li>
                </ul>
            </li>
        </ul>
    </div>
</div>


<div class="row">
    <div ui-view="buttonInputs" class="col-md-7 container-product-display"></div>
    <div ui-view="orderSection" class="col-md-5 container-order-display"></div>
</div>








