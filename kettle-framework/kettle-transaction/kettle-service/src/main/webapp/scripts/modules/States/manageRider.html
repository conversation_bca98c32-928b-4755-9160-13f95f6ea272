<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<div flash-message="5000"></div>
<div class="container" data-ng-init="init()">
    <div class="row">
        <div class="col-xs-12">
            <h2 class="text-center" style="font-family: 'typewriter';">Manage SDP</h2>
        </div>
    </div>
    <div class="row" style="margin-bottom: 20px">
        <div class="col-xs-2">
            <button type="button" class="btn btn-alert pull-right"
                    ng-click="backToCover()">Back
            </button>
        </div>
        <div class="col-xs-8">
            <input class="form-control" data-ng-model="newEmployeeId"/>
        </div>
        <div class="col-xs-2">
            <button class="btn btn-primary pull-left"
                    data-ng-click="addRider(newEmployeeId)">
                <i class="fa fa-plus"></i> Add SDP
            </button>
        </div>
    </div>
    <div class="row" data-ng-if="showContact">
        <div class="col-xs-6">
            <p><strong>Contact</strong> - {{selectedEmployee.name}} - {{selectedEmployee.id}}</p>
            <input class="form-control" data-ng-model="contact"/>
            <p style="color: red; font-size: 12px;" data-ng-if="contactError!=null">{{contactError}}</p>
        </div>
        <div class="col-xs-6">
            <button class="btn btn-primary pull-left" data-ng-click="sendOTP(contact)" style="margin-top: 29px;">
                Submit
            </button>
        </div>
    </div>
    <div class="row" data-ng-if="showOTP">
        <div class="col-xs-6">
            <p><strong>OTP - {{contact}} - {{selectedEmployee.name}} - {{selectedEmployee.id}}</strong></p>
            <input class="form-control" data-ng-model="OTP"/>
            <p style="color: red; font-size: 12px;" data-ng-if="otpError!=null">{{otpError}}</p>
        </div>
        <div class="col-xs-6">
            <button class="btn btn-primary pull-left" style="margin-top: 29px;"
                    data-ng-click="verifyOTP(OTP)">
                Verify
            </button>
        </div>
    </div>
    <div class="row">
        <div class="col-xs-12" style="margin-top: 20px;">
            <table class="table table-striped table-bordered" style="background: #fff;"
                   data-ng-if="employeeDetails != null && employeeDetails.length > 0">
                <thead>
                <th>Employee Id</th>
                <th>Employee Name</th>
                <th>SDP Contact</th>
                <th>Mapping Status</th>
                <th>Change Status</th>
                <th>Edit Contact</th>
                <th>Clear Contact</th>
                <th>Remove SDP</th>
                <th>Pending SDP Orders</th>
                </thead>
                <tbody>
                <tr data-ng-repeat="employee in employeeDetails" ng-class="{'greenBg':selectedEmployee.id==employee.id}">
                    <td>{{employee.id}}</td>
                    <td>{{employee.name}}</td>
                    <td>{{employee.sdpContact}}</td>
                    <td>{{employee.mappingStatus}}</td>
                    <td>
                        <span data-ng-if="employee.mappingStatus == 'ENABLED'"
                              data-ng-click="disableRider(employee)" style="cursor: pointer" title="Disable">
                        <i class="fa fa-edit" style="font-size: 24px; margin-right: 5px"></i></span>
                        <span data-ng-click="enableRider(employee)"
                              data-ng-if="employee.mappingStatus == 'DISABLED'"
                              style="cursor: pointer" title="Enable">
                            <i class="fa fa-edit" style="font-size: 24px; margin-right: 5px"></i></span>
                    </td>
                    <td>
                        <span data-ng-click="editContact(employee)"
                              style="cursor: pointer" title="Edit Contact">
                            <i class="fa fa-edit" style="font-size: 24px; margin-right: 5px;"></i></span>
                    </td>
                    <td>
                        <span data-ng-click="clearContact(employee)"
                              style="cursor: pointer" title="Clear Contact">
                            <i class="fa fa-remove" style="font-size: 24px; margin-right: 5px; color: red"></i></span>
                    </td>
                    <td>
                        <span data-ng-click="deleteRider(employee)"
                              style="cursor: pointer" title="Delete">
                            <i class="fa fa-remove" style="font-size: 24px; margin-right: 5px; color: red"></i></span>
                    </td>
                    
                    <td>
                        <span data-ng-click="getSdpOrders(employee)"
                              style="cursor: pointer" title="Order Details" data-ng-if="employee.sdpContact">
                            <i class="fa fa-motorcycle" style="font-size: 24px;  color: red;margin-right: 5px;"></i></span>
                    </td>
                    <!--<td align="left">
                        <button class="btn btn-sm" data-ng-if="employee.mappingStatus == 'ENABLED'"
                                data-ng-click="disableRider(employee)" title="Disable">Disable</button>
                        <button class="btn btn-sm"  data-ng-click="enableRider(employee)"
                                data-ng-if="employee.mappingStatus == 'DISABLED'" title="Enable">Enable</button>
                        <button class="btn btn-sm" data-ng-click="deleteRider(employee)" title="Delete">Delete</button>
                        <button class="btn btn-sm" data-ng-click="clearContact(employee)" title="Disable">Clear Contact</button>
                        <button class="btn btn-sm" data-ng-click="editContact(employee)" title="Disable">Edit</button>-->
                        <!--<span data-ng-if="employee.mappingStatus == 'ENABLED'"
                              data-ng-click="disableRider(employee.id)" style="cursor: pointer" title="Disable">
                        <i class="fa fa-edit" style="font-size: 24px; margin-right: 5px"></i></span>
                        <span data-ng-click="enableRider(employee.id)"
                              data-ng-if="employee.mappingStatus == 'DISABLED'"
                              style="cursor: pointer" title="Enable">
                            <i class="fa fa-edit" style="font-size: 24px; margin-right: 5px"></i></span>
                        <span data-ng-click="deleteRider(employee.id)"
                              style="cursor: pointer" title="Delete">
                            <i class="fa fa-remove" style="font-size: 24px; margin-right: 5px; color: red"></i></span>
                        <span data-ng-click="clearContact(employee.id)"
                              style="cursor: pointer" title="Clear Contact">
                            <i class="fa fa-remove" style="font-size: 24px; margin-right: 5px; color: red"></i></span>-->
                    <!--</td>-->
                </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div class="row" data-ng-if="riderOrders != null && riderOrders != undefined">
        <div class="col-xs-12" style="margin-top: 20px;">
            <table class="table table-striped table-bordered" style="background: #fff;">
                <tr>
                <th>Generated Order Id</th>
                <th>Order Status</th>
                </tr>
                <tbody>
                <tr data-ng-repeat="(key,value) in riderOrders" >
                    <td>{{key}}</td>
                    <td>{{value}}</td>
                </tr>
                 <tr>
                 <td> <button class="btn btn-danger"
					data-ng-click="resetSdpOrders()">Reset Rider Orders</button></td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>


