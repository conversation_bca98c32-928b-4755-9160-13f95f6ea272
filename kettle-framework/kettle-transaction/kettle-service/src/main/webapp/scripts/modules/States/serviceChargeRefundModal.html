    <div>
    <div class="modal-header" data-ng-init="initServiceChargeRefund()">
      <div flash-message="5000"></div>
      <h3 class="text-center" style="font-weight:'900'">Service Order Refund details</h3>
    </div>

    <div class="modal-body">
      <form novalidate role="form" name="serviceOrderRefund" autocomplete="off">
        <div class="row" style="margin: 15px;">
          <label style="font-size:15px; font-weight:bold">Service Charge : {{OrderObj.transactionDetail.serviceCharge}}/-</label><br>
          <label style="font-size:15px; font-weight:bold">Service Tax Amount : {{OrderObj.transactionDetail.serviceTaxAmount}}/-</label><br>
          <label style="font-size:15px; font-weight:bold">Total Refund : {{totalServiceChargeRefundAmount}}/-</label>

          <br><br>

          <div data-ng-if="isOtpVerificationRequired">
            <div class="row">
                <label style="color:red">Please select service charge refund comment *</label>
                <div class="col-xs-12">
                  <select
                          class="form-control"
                          id="comment"
                          name="serviceChargeRefundComments"
                          data-ng-model="comment"
                          data-ng-change="commentSelected(comment)"
                          data-ng-options="comment for comment in serviceChargeRefundComments">
                  </select>
                </div>
            </div>

            <br>
            <br>

            <div>
              <label>Otp verification required</label>
              <button class="btn btn-default" data-ng-if="showCustomerOTP()" data-ng-click="selectedForOtpVerification('CUSTOMER')">OTP for Customer</button>
              <button class="btn btn-default" data-ng-click="selectedForOtpVerification('CAFE')">OTP for Cafe</button>
              <br>
              <div class="row" data-ng-if="otpVerificationType == 'CAFE'">
                <div class="col-xs-12">
                  <select
                          class="form-control"
                          id="employeeData"
                          name="employeeData"
                          data-ng-model="emp"
                          data-ng-change="getEmployeeData(emp)"
                          data-ng-options="data as data.name + ' - ' + data.id for data in employeeDetails">
                  </select>
                </div>
              </div>
            </div>
            <div data-ng-if="showSendOtp">
              <button class="btn btn-primary" data-ng-click="sendOTPForServiceChargeRefund()"> Send OTP to {{selectedEmployee != null ? selectedEmployee.name : customerSelected.firstName}}</button>
            </div>
            <div data-ng-if="showVerifyOtp">
              <input type="text" name="otp" id="otp" data-ng-model="otpEntered" placeholder="Enter OTP" required
                     pattern="\d*" maxlength="4"/>
              <button class="btn btn-primary" data-ng-click="verifyOTPForServiceChargeRefund(otpEntered)">Submit otp</button>
            </div>
          </div>
          <div data-ng-if="!isOtpVerificationRequired">
            <div class="col">
              <h4><b>OTP verification completed. <h4 data-ng-if="OrderObj.orderRefundDetailId == null">Please submit to process refund</h4></b><br></h4>
              <span><b>Order Id : {{OrderObj.orderId}}</b><br></span>
              <span><b>Generated order Id : {{OrderObj.generateOrderId}}</b><br></span>
              <span data-ng-if="OrderObj.orderRefundDetailId != null"><b>Refund detail Id : {{OrderObj.orderRefundDetailId}}</b><br></span>
              <span data-ng-if="OrderObj.orderRefundDetailId != null"><b>Refund status : CREATED</b><br></span>
            </div>
          </div>
        </div>
      </form>
    </div>

    <div class="modal-footer">
      <div class="btn-group">
        <a href="#" class="btn btn-danger" ng-click="cancelProcess()">Cancel</a>
        <a href="#" class="btn btn-default" ng-click="createServiceChargeRefund()"
           data-ng-if="OrderObj.orderRefundDetailId == null"
           ng-disabled="isOtpVerificationRequired">Submit</a>
      </div>
    </div>
</div>