<div class="container-fluid" data-ng-init="init()">
    <div class="row">
        <div class="col-xs-1"style="padding-top:10px;">
            <button class="btn btn-error" data-ng-click="goBack()">Back</button>
        </div>
        <div class="col-xs-11">
            <h2 class="center-block text-center">Pending Refunds</h2>
        </div>
    </div>
    <div class="row" data-ng-if="payments==null">
        <div class="text-center">No Results found</div>
    </div>
    <div class="row" data-ng-if="payments!=null">
        <table class="table table-striped">
            <thead style="background-color: #FFFFFF">
                <tr>
                    <th>Customer Name</th>
                    <th>Contact Number</th>
                    <th>Payment Partner</th>
                    <th>External Order ID</th>
                    <th>Partner Txn ID</th>
                    <th>Transaction Amount</th>
                    <th>Process Time</th>
                    <th>Payment Status</th>
                    <th>Refund Status</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>

                <tr  data-ng-repeat="payment in payments track by $index">
                    <td>{{payment.customerName!=null ? payment.customerName : 'N/A'}}</td>
                    <td>{{payment.contactNumber!=null ?  payment.contactNumber: 'N/A'}}</td>
                    <td>{{payment.paymentPartner}}</td>
                    <td>{{payment.externalOrderId}}</td>
                    <td>{{payment.partnerTransactionId}}</td>
                    <td>{{payment.transactionAmount}}</td>
                    <td>{{payment.paymentProcessTime | date:'dd/MM/yyyy h:mma'}}</td>
                    <td>{{payment.paymentStatus}}</td>
                    <td>{{payment.refundStatus!=null ? payment.refundStatus: 'N/A'}}</td>
                    <td>
                        <button class="btn btn-primary" data-ng-click="refundPayment($index, payment)">Process Refund</button>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>
