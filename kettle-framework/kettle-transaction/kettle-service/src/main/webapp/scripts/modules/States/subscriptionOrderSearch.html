<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div data-flash-message="5000"></div>
<div class="container-fluid" data-ng-init="init()">
	<div class="row">
		<div class="col-xs-12">
			<h2 class="text-center" style="font-family: 'typewriter';">Subscription Order Search</h2>
		</div>
	</div>
	<div class="row" style="margin-top:20px;">
		<div class="col-xs-2">
			<button class="btn btn-warning pull-left" data-ng-click="backToCover()">Back</button>
		</div>
		<div class="col-xs-10">
			<form class="form-horizontal">
				<div class="form-group">
					<div class="col-xs-4">
						<input type="text" class="form-control" data-ng-model="orderInput" placeholder="Search by contact number" />
					</div>
					<div class="col-xs-2">
						<button class="btn btn-info" data-ng-click="getSubscriptionOrders()">Search</button>
					</div>
				</div>
			</form>
		</div>
	</div>
	<div class="row">
		<div class="col-xs-12">
			<table class="table table-striped" style="background:#fff;" data-ng-if="orderList.length>0">
				<tr>
					<th>Order Event Id</th>
					<th>Subscription Id</th>
					<th>Generated Order Id</th>
					<th>Order Event Status</th>
					<th>Order Status</th>
					<th>Delivery Unit</th>
					<th>Locality</th>
					<th>Scheduled Delivery Time</th>
					<th>Actual Delivery Time</th>
					<th>Remark</th>
					<th>Event Time</th>
					<th>Reason</th>
					<th>Actions</th>
				</tr>
				<tr data-ng-repeat="order in orderList">
					<td>{{order.subscriptionEventId}}</td>
					<td><a href="javascript:;" data-ng-click="openOrderSearch(order.subscriptionDetail)"><b>{{order.subscriptionDetail.generateOrderId}}</b></a></td>
					<td data-ng-if="order.orderDetail!=null">
						<a href="javascript:;" data-ng-click="loadOrderScreen(order)"><b>{{order.orderDetail.generateOrderId}}</b></a>
					</td>
					<td data-ng-if="order.orderDetail==null">NA</td>
					<td>{{order.status}}</td>
					<td data-ng-if="order.orderDetail!=null">{{order.orderDetail.status}}</td>
					<td data-ng-if="order.orderDetail==null">NA</td>
					<td data-ng-if="order.orderDetail!=null">{{order.orderDetail.unitName}}</td>
					<td data-ng-if="order.orderDetail==null">NA</td>
					<td data-ng-if="order.orderDetail!=null">{{getLocalityFromDeliveryAddresId(order.orderDetail.deliveryAddress)}}</td>
					<td data-ng-if="order.orderDetail==null">NA</td>
					<td>{{order.originalDeliveryTime | date:'yyyy-MM-dd hh:mm:ss a'}}</td>
					<td>{{order.actualDeliveryTime | date:'yyyy-MM-dd hh:mm:ss a'}}</td>
					<td>{{order.remark}}</td>
					<td>{{order.eventTime | date:'yyyy-MM-dd hh:mm:ss a'}}</td>
					<td>{{order.reason}}</td>
					<td>
						<button class="btn btn-xs btn-danger" data-ng-click="editOrder(order)"
						data-ng-if="order.status!='SKIPPED' && order.orderDetail.status!='SETTLED' && dateGTE(order.eventTime)"
						>Edit Event</button>
					</td>
				</tr>
			</table>
			<div class="alert alert-info" data-ng-if="orderList.length==0">No order events found.</div>
		</div>
	</div>
</div>