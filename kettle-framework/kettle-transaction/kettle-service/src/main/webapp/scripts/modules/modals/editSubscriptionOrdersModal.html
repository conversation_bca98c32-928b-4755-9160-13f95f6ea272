<div class="modal-header" data-ng-init="init()">
    <h3 class="modal-title pull-left">Re-schedule Order Event</h3>
    <button class="btn btn-danger pull-right" type="button" data-ng-click="skipEvent()">Skip This Event</button>
    <div class="clearfix"></div>
</div>
<div class="modal-body">
    <div class="row" style="marin-bottom:20px;">
        <div class="col-xs-6">
            <div class="form-group">
                <label>New Time:</label>
                <select data-ng-model="newFrequencyTime" class="form-control"
                        data-ng-options="frequencyTime as frequencyTime.value for frequencyTime in frequencyTimes track by frequencyTime.id"></select>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-xs-12">
            <div class="form-group">
                <label>Reason:</label>
                <textarea class="form-control" rows="6" data-ng-model="actionReason"></textarea>
            </div>
        </div>
    </div>
</div>
<div class="modal-footer">
    <div class="btn-group col-xs-offset-10" style="margin-bottom: 10px">
        <button class="btn btn-danger" type="button" ng-click="cancel()">Cancel</button>
        <button class="btn btn-default" type="button" ng-click="ok()">Submit</button>
    </div>
</div>