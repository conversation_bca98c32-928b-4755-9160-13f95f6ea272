<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div flash-message="5000" ></div>
<button type="button" class="btn btn-success pull-left"
        style="margin-left: 50px"
        ng-click="restartSession()">Logout</button>
<div class="registrationPage">
    <img src="images/logo3.png" class="brandImg center-block" style="margin-top: 20px">
	
	 <form novalidate role="form" name="CSRegisterForm" class="col-xs-offset-4" autocomplete="off" style="margin-top:20px">


        <input collapse="hasName" type="text"
               name="name" id="name"
               style="width: 350px;font-size: 28px;text-align: center;
                                border: 1px solid #737370;
                              line-height: 1.5em; font-family:'typewriter';"
               placeholder="Name"
               class="input-form-control "
               ng-model="csName"  required/><span collapse="hasName" style="font-size: 30px"> *</span>


        <input type="text" style="display:none">

        <div collapse="hasName" ng-messages="CSRegisterForm.name.$error" style="color:maroon" role="alert">
            <div ng-if="CSRegisterForm.name.$touched">
                <div ng-message="required">User name is required</div>
            </div>
        </div>


        <input type="email"
               name="email" id="email"
               style="width: 350px;font-size: 28px;text-align: center;
                    border: 1px solid #737370;margin-top: 10px;
                  line-height: 1.5em; font-family:'typewriter';"
               placeholder="Enter your email"
               class="input-form-control "
               ng-model="csEmail"/>

        <div ng-messages="CSRegisterForm.email.$error" style="color:maroon" role="alert">
            <div ng-if="CSRegisterForm.email.$touched">
                <div ng-message="required">Email field can't be empty</div>
            </div>
        </div>



        <div class="btn-group col-xs-offset-2" style="margin-top: 20px">
            <a href="#" class="btn btn-default btn-lg customerBtn"
               ng-click="skipemail()" ng-disabled="CSRegisterForm.$invalid" style="margin-top: 10px;font-family: 'typewriter';">Skip email</a>

            <a href="#" class="btn btn-success btn-lg customerBtn"
               ng-click="newCustomerRegistration()" ng-disabled="CSRegisterForm.$invalid" style="margin-top: 10px;font-family: 'typewriter';">Register</a>

        </div>

    </form>
l



    <h2 collapse = "!isNewUser" class="text-center" id="topLine"
        style="color: #737370; margin: 50px;font-family: 'typewriter';">
        Hey, you just earned yourself 60 Tea Points which will get you a <span style="color:#EF574E;">free desi chai</span></h2>

    <h2 class="text-center" id="secondLine"
        style="color: #737370; margin: 50px;font-family: 'typewriter';">
        <span collapse="!hasName">Hey {{csName}},</span> Enter & verify your email id to get another <span style="color:#EF574E;">10 Tea Points</span> On collecting 60 points you will be eligible for free desi chai</h2>

   
</div>


<!-- <script>

            scrollTopPadding = 180;
            scrollTopLast = 0;
    $('#email,#name').focus(function() {
        // save the current scroll position for blur event
        scrollTopLast = $("div.registrationPage").scrollTop();
       //console.log(scrollTopLast);
        //console.log($(this).offset().top);
        //console.log($(this).offset().top - scrollTopPadding);
        // scroll to the textarea
       // $('html').scrollTop($(this).offset().top - scrollTopPadding);
       /*  var element = document.getElementById("topLine");
		element.outerHTML = "";
		delete element;
		var element = document.getElementById("secondLine");
		element.outerHTML = "";
		delete element; */


    }).blur(function() {
        // scroll back to position before textarea focus
       // $('html').scrollTop(scrollTopLast);
        document.getElementById("topLine").style.visibility = "visible";
        document.getElementById("secondLine").style.visibility = "visible";
        // hide fake keyboard

    });

</script> -->


