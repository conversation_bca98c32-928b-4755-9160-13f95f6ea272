<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="container-fluid">
    <div class="row">
        <div class="col-lg-5 col-xs-5"
             style="padding-right: 0px; margin-top: 15px;">
            <table style="border-style: groove;" class="table orderSearchText">
                <tr>
                    <th>Unit Name</th>
                    <th>Employee Id</th>
                    <th>Employee Name</th>
                </tr>
                <tr>
                    <td>{{unitDetails.name}}</td>
                    <td>{{currentUser.userId}}</td>
                    <td>{{currentUser.userName}}</td>
                </tr>
            </table>
        </div>

        <div class="col-lg-3 col-xs-3" style="margin-top: 15px;">
            <button class="btn btn-primary" type="button"
                    ng-click="testPrinter()">Test Printer
            </button>
            <button class="btn btn-primary" type="button" ng-click="getOrders()" style="margin-top: 20px">Delivery
                Orders
            </button>
        </div>
        <div class="col-lg-4 col-xs-4" style="margin-top: 15px; text-align: right">
            <button class="btn btn-warning" type="button"
                    data-ng-click="resetConfig()" data-ng-show="isManager">Reset
                Configuration
            </button>
            <button class="btn btn-warning" data-ng-click="logout()">Logout</button>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-9 col-xs-9 text-center" style="margin-left: 50px;">
            <h3>Assembly for Kettle</h3>
        </div>
        <div class="row">
            <button class="btn btn-success" data-ng-click="refresh(true)">Refresh Orders</button>
            <button class="btn btn-danger" data-ng-click="refreshUnitOrderCache(true)" acl-action="KETTLE_REF_ODR_CACHE">Refresh Cache</button>
        </div>
    </div>
    <div id="assembly" class="row">
        <div class="col-lg-12 col-xs-12">
            <h3 class="text-center" data-ng-if="orders.length==0">No orders in queue</h3>
        </div>
        <div class="col-lg-12 col-xs-12" data-ng-if="orders.length > 0">
            <ul style="list-style: none;">
                <li class="panel-collapse" data-ng-hide="dispatched"
                    data-ng-repeat="indexOrder in orders | reverse">
                    <div class="row" data-ng-class="{outOfDelivery : indexOrder.order.ood }">
                        <div class="panel-body col-lg-9 col-xs-9">

                            <div class="order-details"
                                 data-ng-class="{	created: indexOrder.order.status =='CREATED',
														 	acknowledged: indexOrder.order.status =='PROCESSING',
															 rtd: (indexOrder.order.status=='READY_TO_DISPATCH' && indexOrder.order.settlements[0].modeDetail.name != 'Cash'),
															 rtd_cash: (indexOrder.order.status=='READY_TO_DISPATCH' && indexOrder.order.settlements[0].modeDetail.name == 'Cash'), 
														 	cancelRequested: indexOrder.order.status=='CANCELLED_REQUESTED'
														  }">
                                <!-- <div class="col-lg-12 col-xs-12">
                            <div class="row">
                                <div class="col-lg-9 col-xs-9">
                                    <span><strong>ID:</strong>
                                        {{indexOrder.order.generateOrderId}}</span><span
                                        data-ng-if="indexOrder.channelPartner.name">,<strong>CP:</strong>
                                        {{indexOrder.channelPartner.name}}
                                    </span> <span data-ng-if="indexOrder.deliveryPartner.name">,<strong>DP:</strong>
                                        {{indexOrder.deliveryPartner.name}}
                                    </span>
                                </div>
                                <div class="col-lg-3 col-xs-3">
                                    <span class="pull-right">
                                        {{indexOrder.order.billCreationTime | date:'dd/MM/yyyy @ h:mma'}}
                                    </span>
                                </div>
                            </div>
                        </div> -->
                                <div class="col-lg-12 col-xs-12">

                                    <div
                                            data-ng-if="indexOrder.order.source=='COD' && indexOrder.order.deliveryPartner==8 && indexOrder.deliveryDetails != null && indexOrder.deliveryDetails.deliveryBoyName != null"
                                            class="row">
                                        <span><strong>Delivered By: </strong></span> <span>{{indexOrder.deliveryDetails.deliveryBoyName}}
											{{indexOrder.deliveryDetails.deliveryBoyPhoneNum}}</span>
                                        <span>Missed Call No {{indexOrder.deliveryDetails.allotedNo}}</span>
                                        <button class="btn btn-sm  btn-warning"
                                                data-ng-click="resendSMSToRider(indexOrder.deliveryDetails)">Resend SMS
                                        </button>
                                    </div>

                                    <div class="row">
                                        <div class="col-lg-6 col-xs-4">
											<span><strong>ID:</strong>
												{{indexOrder.order.generateOrderId}}</span><span
                                                data-ng-if="indexOrder.channelPartner.name">,<strong>CP:</strong>
												{{indexOrder.channelPartner.name}}
											</span> <span
                                                data-ng-if="indexOrder.deliveryPartner.name && indexOrder.order.source=='COD'">,<strong>DP:</strong>
												{{indexOrder.deliveryPartner.name}}
											</span> <br> <span><strong>Customer: </strong></span> <span>{{indexOrder.customer.firstName}}
												{{indexOrder.customer.lastName}}</span>, <span
                                                data-ng-if="indexOrder.customer.contactNumber.length>0">
												{{indexOrder.customer.countryCode}}-{{indexOrder.customer.contactNumber}}
											</span>
                                            <br>
                                            <span><strong>Remarks:</strong> {{indexOrder.order.orderRemark}}</span>
                                            <div data-ng-if="indexOrder.order.subscriptionDetail!=null"
                                                 style="font-size: 16px; color: #000;">
                                                <b>Subscription Order Delivery time:
                                                    {{getSubscriptionDeliveryTime(indexOrder.order)}}</b>
                                            </div>
                                        </div>
                                        <div class="col-lg-4 col-xs-6">
                                            <div class="row">
                                                <div class="col-lg-6 col-xs-6">
													<span class="amount btn"
                                                          data-ng-class="{ 'btn-primary' : indexOrder.order.source=='TAKE_AWAY' || indexOrder.order.source=='CAFE',
																				  'btn-default' :indexOrder.order.source=='COD'}">

														<span data-ng-if="indexOrder.order.source=='TAKE_AWAY'">TAKEAWAY
													</span> <span
                                                            data-ng-if="indexOrder.order.source=='COD'"
                                                            data-ng-click="showDeliveryDetails(indexOrder.order.orderId)">DELIVERY</span>
														<span data-ng-if="indexOrder.order.source=='CAFE'">DINE-IN
															<br> T-{{indexOrder.order.terminalId}}
													</span>
													</span>
                                                </div>
                                                <div class="col-lg-6 col-xs-6">
													<span data-ng-if="indexOrder.order.settlements.length > 1"
                                                          class="amount btn btn-success"
                                                          data-ng-click="populateSummary(indexOrder.order.orderId)">
														Rs.{{indexOrder.order.settlements[0].amount}} <span
                                                            class="mode badge">Multiple</span>
													</span> <span
                                                        data-ng-if="indexOrder.order.settlements.length == 1 && indexOrder.order.settlements[0].modeDetail.category=='OFFLINE'"
                                                        class="amount btn"
                                                        data-ng-class="{'btn-primary':indexOrder.order.settlements[0].modeDetail.name=='Cash',
									 					'btn-default':indexOrder.order.settlements[0].modeDetail.name!='Cash'}"
                                                        data-ng-click="populateSummary(indexOrder.order.orderId)">
														Rs.{{indexOrder.order.settlements[0].amount}} <br> <span
                                                        class="mode badge">{{indexOrder.order.settlements[0].modeDetail.name}}</span>
													</span> <span
                                                        data-ng-if="indexOrder.order.settlements.length == 1 && indexOrder.order.settlements[0].modeDetail.category=='ONLINE'"
                                                        class="amount btn"
                                                        data-ng-class="{'btn-primary':indexOrder.order.settlements[0].modeDetail.name=='Cash',
									 					'btn-default':indexOrder.order.settlements[0].modeDetail.name!='Cash'}"
                                                        data-ng-click="populateSummary(indexOrder.order.orderId)">
														Rs.{{indexOrder.order.settlements[0].amount}} <br> <span
                                                        class="mode badge">ONLINE</span>
													</span>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-2 col-xs-2">
											<span class="pull-right">
												{{indexOrder.order.billingServerTime | date:'dd/MM/yyyy @ h:mma'}}
											</span>
                                            <br>
                                            <span class="pull-right badge" style="margin-top: 10px;">{{indexOrder.order.status}}</span>
                                        </div>
                                    </div>
                                </div>

                            </div>
                            <div class="row subOrders">
                                <div
                                        data-ng-class="subOrder.isComboItem || subOrder.productCategory.code == 'Combos'? 'subOrderCombo col-lg-3 col-xs-3' : 'subOrder col-lg-3 col-xs-3'"
                                        data-ng-repeat="subOrder in getAssemblyOrderItems(indexOrder.order)">
                                    <strong> <span data-ng-if="subOrder.isComboItem">**(C)
									</span>{{subOrder.productName}}, {{subOrder.dimension}} -
                                        {{subOrder.quantity}}<span data-ng-if="subOrder.takeAway">Take Away
									</span>
                                    </strong>
                                    <div class="suborder-addOn" style="display: table;">
                                        <div class="row" style="margin: 0px"
                                             data-ng-if="subOrder.composition.menuProducts.length > 0">
                                            <p>Combo Items:</p>
                                            <span class="add-on"
                                                  data-ng-repeat="addon in subOrder.composition.menuProducts">{{addon.productName}}</span>
                                        </div>
                                        <div class="row" style="margin: 0px"
                                             data-ng-if="subOrder.composition.variants.length > 0">
                                            <p>Item Variants:</p>
                                            <span class="add-on"
                                                  data-ng-repeat="addon in subOrder.composition.variants">{{addon.alias}}</span>
                                        </div>
                                        <div class="row" style="margin: 0px"
                                             data-ng-if="subOrder.composition.products.length > 0">
                                            <p>Product Variants:</p>
                                            <span class="add-on"
                                                  data-ng-repeat="addon in subOrder.composition.products">{{addon.product.name}}</span>
                                        </div>
                                        <div class="row" style="margin: 0px"
                                             data-ng-if="subOrder.composition.addons.length > 0">
                                            <p>Add-Ons:</p>
                                            <span class="add-on"
                                                  data-ng-repeat="addon in subOrder.composition.addons">{{addon.product.name}}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="actions col-lg-3 col-xs-3" style="margin-top: 10px">
                            <div class="row" style="width: 97%">
                                <div

                                        class="col-lg-6 col-xs-12" style="padding-top: 3px;">
                                    <timer start-time="indexOrder.order.billingServerTime">
										<span data-ng-class="{timerDisplay: (hours * 60 + minutes) < 10,
										 'timerDisplay alertx' : (hours * 60 + minutes) >= 10 }">{{hours * 60 + minutes}}</span>
                                    </timer>
                                </div>
                                <div class="col-xs-12"
                                     data-ng-class="{	'col-lg-5': indexOrder.order.status !='PROCESSING',
														 	'col-lg-6': indexOrder.order.status =='PROCESSING',
														 }"
                                     style="padding-top: 3px;">
                                    <button class="btn btn-primary "
                                            data-ng-if="indexOrder.order.status == 'CREATED'"
                                            data-ng-disabled="isDisabled" data-ng-hide="isProcessing"
                                            data-ng-click="sendStatus(this,indexOrder.order.orderId,1,indexOrder.order.unitId,indexOrder.order.source)">
                                        ACKNOWLEDGE
                                    </button>
                                    <button class="btn btn-info "
                                            data-ng-if="indexOrder.order.status == 'PROCESSING'"
                                            data-ng-disabled="isDisabled" data-ng-hide="isRTD"
                                            data-ng-click="sendStatus(this,indexOrder.order.orderId,2,indexOrder.order.unitId,indexOrder.order.source)">
                                        <span data-ng-if="indexOrder.order.source=='COD'">READY TO DISPATCH</span>
                                        <span data-ng-if="indexOrder.order.source!='COD' && !isTokenEnabled">CALL CUSTOMER</span>
                                        <div data-ng-if="indexOrder.order.source!='COD' && isTokenEnabled"
                                             style="min-width:50px; font-size:40px;">{{indexOrder.order.tokenNumber}}
                                        </div>
                                    </button>
                                    <button class="btn btn-success "
                                            data-ng-if=" indexOrder.order.status == 'READY_TO_DISPATCH' && indexOrder.order.deliveryPartner!=8"
                                            data-ng-disabled="isDisabled" data-ng-hide="isDispatch"
                                            data-ng-click="sendStatus(this,indexOrder.order.orderId,4,indexOrder.order.unitId,indexOrder.order.source,indexOrder)">
                                        DISPATCH <b>{{indexOrder.order.tokenNumber}}</b></button>
                                    <button class="btn btn-success "
                                            data-ng-if="!indexOrder.order.pendingCash && indexOrder.order.source=='COD' && indexOrder.order.deliveryPartner==8 && indexOrder.order.status == 'READY_TO_DISPATCH' && indexOrder.deliveryDetails.deliveryBoyId != null"
                                            data-ng-disabled="isDisabled" data-ng-hide="isDispatch"
                                            data-ng-click="sendStatus(this,indexOrder.order.orderId,8,indexOrder.order.unitId,indexOrder.order.source, indexOrder)">
                                        OUT
                                        FOR DELIVERY
                                    </button>
                                    <button class="btn btn-success "
                                            data-ng-if="indexOrder.order.source=='COD' && indexOrder.order.deliveryPartner==8 && indexOrder.order.status == 'SETTLED'"
                                            data-ng-disabled="isDisabled"
                                            data-ng-click="sendStatus(this,indexOrder.order.orderId,7,indexOrder.order.unitId,indexOrder.order.source)">
                                        DELIVERED
                                    </button>
                                    <button class="btn btn-danger" data-ng-disabled="isDisabled"
                                            data-ng-hide="isCancelled"
                                            data-ng-if="indexOrder.order.status == 'CANCELLED_REQUESTED'"
                                            data-ng-click="sendStatus(this,indexOrder.order.orderId,6,indexOrder.order.unitId,indexOrder.order.source, indexOrder)">
                                        CANCEL
                                    </button>
                                </div>
                                <div
                                        data-ng-if="indexOrder.order.source=='COD' && indexOrder.order.deliveryPartner==8 && indexOrder.order.status == 'READY_TO_DISPATCH'"
                                        class="col-lg-6 col-xs-12" style="padding-top: 3px;">
                                    <button class="btn btn-default"
                                            data-ng-click="assignRider(indexOrder)">Assign
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </li>
            </ul>

        </div>
    </div>
</div>

<div aria-labelledby="RiderDataViewModalLabel" class="modal fade" id="RiderDataViewModal" role="dialog"
     tabindex="-1">
    <div class="modal-dialog modal-dialog-centered modal-sm" role="document">
        <div class="modal-content">
            <div class="modal-body" align="center">
                <div class="row">
                    <div class="tab-header-area">
                        <h4 style="color:#03AB03;text-align: center"><strong>RIDER TEMP DETAILS</strong></h4>
                    </div>
                </div>
                <div ng-show="showMask">
                    <div class="row ">
                    <span class="checkbox-inline" style="color:#4f964f;">
                        <b>Wearing Mask</b></span>
                        <input class="checkbox-inline" type="checkbox"
                               ng-model="wearingMask"/>

                    </div>
                </div>
                <div ng-show="showTemp">
                    <div class="row">
                    <span ng-if="currentOrderDetails.partner=='ZOMATO'"
                          class="checkbox-inline" style="color:#4f964f;">
                        <b>Rider Temp. > {{maxZomatoTemp}}</b> </span>
                        <span ng-if="currentOrderDetails.partner=='SWIGGY'"
                              class="checkbox-inline" style="color:#4f964f;">
                        <b>Rider Temp. > {{maxSwiggyTemp }}</b> </span>
                        <input class="checkbox-inline" type="checkbox"
                               ng-model="riderTemp"/>
                    </div>
                    <div ng-show="riderTemp">
                        <div class="row">
                    <span class="label" style="color:#4f964f;">
                        <h5> Body Temp</h5>
                    </span>
                            <input type="number" ng-model="bodyTemp">
                        </div>
                    </div>
                </div>
                <br>
                <div class="row">
                    <button class="btn-success" ng-click="onSubmittingRiderDetails(wearingMask,riderTemp)">SUBMIT
                        DETAILS
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>


