<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div data-flash-message="5000"></div>
<div class="container-fluid" data-ng-init="init()">
    <div class="row">
        <div class="col-xs-12">
            <h2 class="text-center" style="font-family: 'typewriter';">Cafe Timings Dashboard</h2>
        </div>
    </div>
    <div class="row" style="margin-top:20px;">
        <div class="col-xs-2">
            <button class="btn btn-warning pull-left" data-ng-click="backToCODCover()">Back</button>
        </div>
        <div class="col-xs-10">
            <form class="form-horizontal">
                <div class="form-group">
                    <div class="col-xs-8">
                        <label class="control-label col-xs-3">Select Unit</label>
                        <div class="col-xs-9">
                            <select data-ng-model="selectedUnit" class="form-control"
                                    data-ng-options="unit as unit.name for unit in outletList track by unit.id"></select>
                        </div>
                    </div>
                    <div class="col-xs-2">
                        <button class="btn btn-info" data-ng-click="getUnitData()">Search</button>
                    </div>
                </div>
            </form>
        </div>
    </div>
    <div class="row" data-ng-if = "UnitResponseData  !== null" style="margin:15px">
        <div class="col-xs-12" >
            <h4>
                <b>Business Hours </b>
            </h4>
            <table class="table table-striped table-bordered" style="font-size: 16px">
                <tr>
                    <td><b>Is Operational</b></td>
                    <td><b>Days</b></td>
                    <td>
                        <b>Delivery Timings</b>
                    </td>
                    <td>
                        <b>Dine in Timings</b>
                    </td>
                </tr>
                <tr ng-repeat="operationHour in UnitResponseData.operationalHours">
                    <td>{{operationHour.isOperational}}</td>
                    <td>{{operationHour.dayOfTheWeek}}</td>
                    <td ng-if="operationHour.isOperational && (UnitResponseData.family=='CAFE' || UnitResponseData.family=='EMPLOYEE_MEAL' || UnitResponseData.family=='DELIVERY')">
                        <b>Start Time :</b> {{operationHour.deliveryOpeningTime}}
                        <br/>
                        <b>End Time : </b> {{operationHour.deliveryClosingTime}}
                    </td>
                    <td ng-if="operationHour.isOperational && (UnitResponseData.family=='CAFE' || UnitResponseData.family=='EMPLOYEE_MEAL' || UnitResponseData.family=='DELIVERY')">
                        <b>Start Time :</b> {{operationHour.dineInOpeningTime}}
                        <br/>
                        <b>End Time : </b> {{operationHour.dineInClosingTime}}
                    </td>
                </tr>
            </table>
        </div>
    </div>
</div>