
<style>
    /* Custom Table Styling */
    .custom-table {
        background-color: #254b2a;
        color: #000000;
        border-radius: 8px;
        overflow: hidden;
    }

    .custom-table thead {
        background-color: #f5e0e0;
        color: #000000;
    }

    .custom-table th, .custom-table td {
        padding: 12px;
        text-align: center;
        border-color: #ff9f9f;
    }

    .custom-table tbody tr:nth-child(odd) {
        background-color: #90a893;
    }

    .custom-table tbody tr:nth-child(even) {
        background-color: #c1edc6;
    }

    .custom-table tbody tr:hover {
        background-color: #f3e7e7;
        color: #000000;
        font-weight: bold;
    }
</style>

<div>
    <div class="modal-header" data-ng-init="initOrderRefund()">
        <button type="button" class="btn btn-warning"
                data-ng-click="backToCover()">back
        </button>
        <div>
            <div flash-message="5000"></div>
            <h3 class="text-center" style="font-weight:'900'">Order Refund details</h3>
        </div>
        <div>
            <input type="date" ng-model="startDate" required />
            <button type="button" class="btn btn-primary" ng-click="getServiceChargeRefundDetails()">
                Get Refund Details
            </button>
        </div>
        <div class="table-responsive mt-4">
            <table class="table table-bordered table-striped custom-table" data-ng-if="refundOrdersList.length > 0">
                <thead>
                <tr>
                    <th>Order Refund ID</th>
                    <th>Order ID</th>
                    <th>Reference Refund ID</th>
                    <th>Refund Amount</th>
                    <th>Refund Status</th>
                    <th>Refund Type</th>
                    <th>Refund Reason</th>
                    <th>Creation Time</th>
                </tr>
                </thead>
                <tbody>
                <tr data-ng-repeat="item in refundOrdersList">
                    <td>{{ item.orderRefundDetailId }}</td>
                    <td>{{ item.orderId }}</td>
                    <td>{{ item.referenceRefundId }}</td>
                    <td class="text-success fw-bold">₹{{ item.refundAmount }}</td>
                    <td>
                    <span class="badge">
                        {{ item.refundStatus }}
                    </span>
                    </td>
                    <td>{{ item.refundType }}</td>
                    <td>{{ item.refundReason }}</td>
                    <td>{{ item.creationTime | date:'yyyy-MM-dd HH:mm:ss' }}</td>
                </tr>
                </tbody>
            </table>

            <div class="alert alert-warning text-center mt-3" data-ng-if="!refundOrdersList.length">
                No refund details available.
            </div>
        </div>
    </div>

</div>