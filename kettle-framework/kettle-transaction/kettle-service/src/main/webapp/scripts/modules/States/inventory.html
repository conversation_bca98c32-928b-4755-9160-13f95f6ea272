<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<style type="text/css">
.btn-danger {
	margin: 3px 5px;
}
</style>
<div flash-message="5000"></div>
<div ng-init="init()">
	<div class="row" class="myGrid">
		<div class="col-xs-9"  class="myGrid">
			<div class="row" class="myGrid">
				<div class="col-xs-12" class="myGrid">
					<div ui-grid="inventoryGrid" ui-grid-edit class="myGrid"></div>
				</div>
			</div>
		</div>
		<div class="col-xs-3">
			<div class="row">
				<div class="col-xs-12">
					<h2 class="text-center"
						style="color: #000000; margin-top: 50px; font-family: 'typewriter';">Inventory
						details</h2>
				</div>
			</div>
			<div class="row">
				<div class="col-xs-12">
					<div class="pull-right"
						style="margin-right: 50px; margin-top: 10px">
						<select class="pull-left form-control"
							style="line-height: 2.8em; font-size: 18px; margin-right: 10px;"
							ng-show="!showEditInventory"
							ng-options="outlet as outlet.name for outlet in outletList track by outlet.id"
							ng-model="unitId">
						</select>
						<button type="button" class="btn btn-warning pull-left"
							ng-if="!showEditInventory" ng-click="loadInventory()">Select</button>
					</div>
				</div>
			</div>

			<div class="row" data-ng-if="!isLiveInventoryEnabled">
				<div class="col-xs-12">
					<button type="button" class="btn btn-warning btn-lg"
						style="margin-right: 50px; margin-top: 10px"
						ng-if="showEditInventory && showUpdateInvBtn && !showUpdateBtn"
						ng-click="editInventory('UPDATE')">Update</button>
				</div>
			</div>
			<div class="row" data-ng-if="!isLiveInventoryEnabled">
				<div class="col-xs-12">
					<button type="button" class="btn btn-warning btn-lg"
						style="margin-right: 50px; margin-top: 10px"
						ng-if="showEditInventory && showTransferOutBtn && !showUpdateBtn"
						ng-click="editInventory('TRANSFER_OUT')">Transfer Out</button>
				</div>
			</div>
			<div class="row" data-ng-if="!isLiveInventoryEnabled">
				<div class="col-xs-12">
					<button type="button" class="btn btn-warning btn-lg"
						style="margin-right: 50px; margin-top: 10px"
						ng-if="showEditInventory && showWastageBtn && !showUpdateBtn"
						ng-click="editInventory('WASTAGE')">Wastage</button>
				</div>
			</div>
			<div class="row" data-ng-if="!isLiveInventoryEnabled">
				<div class="col-xs-12">
					<button type="button" class="btn btn-warning btn-lg"
						style="margin-right: 50px; margin-top: 10px"
						ng-if="showEditInventory && showStockinBtn && !showUpdateBtn"
						ng-click="editInventory('STOCK_IN')">Stock In</button>
				</div>
			</div>
			<div class="row" ng-show="showUpdateBtn">
				<div class="col-xs-12">
					<h4 class="text-center"
						style="color: #737370; margin-top: 50px; font-family: 'typewriter';">{{actionLabel}} Request</h4>
				</div>
			</div>
			<div class="row" ng-show="showUpdateBtn">
				<div class="col-xs-12">
					<button type="button" class="btn btn-warning btn-lg"
						style="margin-right: 10px; margin-top: 10px" ng-click="askInventoryComent()">Update Inventory</button>
					<button type="button" class="btn btn-warning btn-lg"
						style="margin-right: 10px; margin-top: 10px" ng-click="cancelUpdateInventory()">Cancel</button>
				</div>
			</div>
			<div class="row">
				<div class="col-xs-12">
					<button type="button" class="btn btn-alert pull-right btn-lg"
						style="margin-right: 50px; margin-top: 10px"
						ng-click="backToCover()">Back</button>
				</div>
			</div>
		</div>
	</div>

</div>