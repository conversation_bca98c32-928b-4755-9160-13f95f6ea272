<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div flash-message="5000"></div>
<div class="row" ng-if="freeKettle && orderType!='subscription'">
	<div class="alert free-kettle text-center">This customer is eligible for availing Free Kettle</div>
</div>
<div class="container-fluid">
<div class="row" ng-if="activePartner != null">
	<div class="alert free-kettle text-center" style="background-color: #337ab7;">{{activePartner.name}} Order</div>
</div>
    <form class="navbar-form bs-navbar-top-example navbar-static-top" style="margin-top: 20px"
          role="search"
          name="searchOrderForm">
        <button type="button" class="btn btn-warning pull-left"
                style="margin-left: 50px"
                ng-click="backToCODCover()">back</button>

        <div class="form-group col-sm-offset-4 col-sm-4">

            <input type="number" style="width: 200px" class="form-control" id="phno"
                   placeholder="Search by contact number" oninput="maxLengthCheck(this)"
                   maxlength="10" name="phno" ng-model="searchText">

            <button type="submit" class="btn btn-default" ng-click="GetCustomerObject()">
                <span class="glyphicon glyphicon-search"></span></button>
        </div>

    </form>



        <form collapse="!isCustomerObjLoaded"  name="customerEditableForm"
              class="col-xs-offset-2 col-xs-8 customerFormCSS">

            <h3 style="color:#88350f;">Customer Addresses</h3>

            <div ng-if="addresses.length > 0" style="margin-top: 10px;margin-left: -20px;">
                <div ng-repeat="row in rows()">
                    <div class="row productRow" >
                        <button ng-repeat="address in itemsInThisRow($index)" class="btn btn-lg addressBtn"
                                ng-class="{prefAddButton: address.preferredAddress, selectedAddButton: address.isSelectedAddress}"
                                type="button" ng-click="addressButtonClicked($parent.$index,$index)">
                            {{getStringAddressForIndex($parent.$index,$index)}}</button>
                    </div>
                </div>
            </div>
            <div flash-message="5000" ></div>
            <!--<div>
                &lt;!&ndash; editable username (text with validation) &ndash;&gt;
                <span class="title">Contact No: </span>
                <label class="newAddressFields" style="line-height: 1.8em;font-size: 17px;border: 0px;"></label>
            </div>-->

            <h3 style="color:#88350f;margin-bottom: 40px;">Customer Details - {{CSObj.contactNumber}}</h3>
            <div>
                <!-- editable firstname (text with validation) -->
                <span class="title">Name: </span>
                <!--<span class="profileField"  editable-text="CSObj.firstName"
                      ></span>-->
                <input type="text"
                       name="firstName" id="firstName"
                       placeholder="Customer Name"
                       class="input-form-control newAddressFields"
                       ng-model="CSObj.firstName" autocomplete="off" required/>
            </div>

            <div>
                <!-- editable username (text with validation) -->
                <span class="title">Email: </span>
                <!--<span class="profileField"editable-email="CSObj.emailId" </span>-->
                <input type="email"
                       name="email" id="email"
                       placeholder="Customer Email"
                       class="input-form-control newAddressFields"
                       ng-model="CSObj.emailId" autocomplete="off"/>
            </div>

            <span us-spinner spinner-key="spinner-1" spinner-theme="smallRed"></span>
            <!--{{isAddressNotSelected}}
            {{customerEditableForm.firstName.$invalid}}
            {{customerEditableForm.firstName.$invalid || isAddressNotSelected}}-->



            <div class="buttons" style=" margin-top: 20px">
                <!-- button to show form -->

                <button type="submit" class="btn btn-primary"
                        ng-click="startOrderRouting()" ng-disabled="customerEditableForm.firstName.$invalid || isAddressNotSelected">
                    Start Order
                </button>

                <!-- buttons to submit / cancel form -->
            </div>

        </form>

    <div collapse="orders.length == 0">

        <table class="table table-striped" style="margin-top: 100px">
            <thead>
            <tr>
                <th style="font:26px sans-serif;">Last 5 Orders</th>
            </tr>
            <tr>
                <th>Generated Bill No</th>
                <th>Bill No</th>
                <th>Unit Order Id</th>
                <th>Unit Name</th>
                <th>Creation Time</th>
                <th>Gross Amount</th>
                <th>Discount</th>
                <th>Taxable Amount</th>
                <th>Tax</th>
                <th>Round Off</th>
                <th>Paid Amount</th>
                <th>Is Parcel</th>
                <th>Status</th>
            </tr>
            </thead>
            <tbody>
            <tr ng-repeat="orderItem in orders">
                <td><a href ng-click="openOrderSearch(orderItem.generateOrderId)" style="color: dodgerblue"><b>{{orderItem.generateOrderId}}</b></a></td>
                <td>{{orderItem.orderId}}</td>
                <td>{{orderItem.unitOrderId}}</td>
                <td>{{orderItem.unitName}}</td>
                <td>{{orderItem.billCreationTime  | date:'yyyy-MM-dd HH:mm:ss'}}</td>
                <td>{{orderItem.transactionDetail.totalAmount}}</td>
                <td>{{orderItem.transactionDetail.discountDetail.discount.value}}</td>
                <td>{{orderItem.transactionDetail.taxableAmount}}</td>
                <td>{{orderItem.transactionDetail.tax}}</td>
                <td>{{orderItem.transactionDetail.roundOffValue}}</td>
                <td>{{orderItem.transactionDetail.paidAmount}}</td>
                <td>{{orderItem.hasParcel}}</td>
                <td>{{orderItem.status}}</td>
            </tr>
            </tbody>
        </table>
    </div>



</div>

<script>
    function maxLengthCheck(object) {
        if (object.value.length > object.maxLength)
            object.value = object.value.slice(0, object.maxLength)
    }
</script>