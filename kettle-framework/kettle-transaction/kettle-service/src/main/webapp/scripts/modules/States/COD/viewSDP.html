<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div flash-message="5000"></div>
<div ng-init="init()">
    <div class="container">
        <div class="row">
            <div class="col-xs-12">
                <h2 class="text-center" style="font-family: 'typewriter';">View SDP Details</h2>
            </div>
        </div>
        <div class="row" style="margin-bottom: 20px;">
            <div class="col-xs-2">
                <button type="button" class="btn btn-alert pull-right"
                        ng-click="backToCover()">Back</button>
            </div>
            <div class="col-xs-8">
                <select class="pull-left form-control"
                        style="line-height: 2.8em; font-size: 18px; margin-right: 10px;"
                        ng-options="outlet as outlet.name for outlet in outletList track by outlet.id"
                        ng-model="unit">
                </select>
            </div>
            <div class="col-xs-2">
                <button type="button" class="btn btn-warning pull-left" ng-click="getSDPs()">Select</button>
            </div>
        </div>
        <div class="row">
            <div class="col s12">
                <table class="table table-striped table-bordered" style="background: #fff;"
                       data-ng-if="employeeDetails != null && employeeDetails.length > 0">
                    <thead>
                        <th>Employee Id</th>
                        <th>Employee Name</th>
                        <th>SDP Contact</th>
                        <th>Mapping Status</th>
                    </thead>
                    <tbody>
                    <tr data-ng-repeat="employee in employeeDetails">
                        <td>{{employee.id}}</td>
                        <td>{{employee.name}}</td>
                        <td>{{employee.sdpContact}}</td>
                        <td>{{employee.mappingStatus}}</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>