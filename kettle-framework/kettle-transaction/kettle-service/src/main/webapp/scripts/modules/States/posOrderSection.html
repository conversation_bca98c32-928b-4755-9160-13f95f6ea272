<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="orderItemsDisplay" data-ng-init="init()">

    <div class="orderItemBox container-fluid"
         data-ng-class="{orderItemComplimentaryBox:orderItem.orderDetails.hasBeenRedeemed == true}"
         data-ng-repeat="orderItem in orderItemArray.slice().reverse()"
         close="orderItemArray.splice(index, 1)">

        <!--All In-->

        <div class="row"
             data-ng-if="(orderItem.productDetails.hasAddons == true) && (orderItem.productDetails.hasSizeProfile == true && (orderItem.orderDetails.hasBeenRedeemed == false))">

            <div class="col-xs-3">
                <button class="btn btn-sq-lg col-xs-12" data-ng-click="customizeModalOpen(orderItem)">
                    <p data-ng-if="orderItem.orderDetails.addons.length > 0" style="width: 80px;">
                        {{getCustomizationAbb(orderItem)}}
                    </p>

                    <div data-ng-if="orderItem.orderDetails.addons.length == 0">
                        Customize
                    </div>


                </button>

                <div class="input-group plusMinus">
                                <span class="input-group-btn ">
                                    <a href="javascript:;" class="btn btn-default btn-circle"
                                       data-ng-click="decreaseCount($index)"><span
                                            class="glyphicon glyphicon-minus"></span></a>
                                </span>

                    <div style="text-align: center; vertical-align: middle;width:35px;overflow: auto;">
                        <input type="number" name="quantity"
                               class="input-form-control"
                               style="width: 25px; margin: 5px; text-align:center;"
                               data-ng-model="orderItem.orderDetails.quantity"
                               placeholder="orderItem.orderDetails.quantity"/>
                        <!--<p>{{orderItem.orderDetails.quantity}}</p>-->
                    </div>
                                <span class="input-group-btn">
                                    <a href="javascript:;" class="btn btn-default btn-circle"
                                       data-ng-click="increaseCount($index)"><span
                                            class="glyphicon glyphicon-plus"></span></a>
                                </span>
                </div>
            </div>


            <div class="col-xs-6">
                <p class="col-md-12  orderText">{{getProductAliasNameIfPresent(orderItem.productDetails.name,orderItem.productDetails.productAliasName)}}</p>

                <div class="col-md-12  btn-group " role="group">

                    <button type="button" class="btn btn-default"
                            data-ng-repeat="price in orderItem.productDetails.prices"
                            data-ng-click="sizeBtnClicked(orderItem,$index)"
                            data-ng-class="{ 'sizeBtnClicked': orderItem.orderDetails.dimension == price.dimension}">
                        {{price.dimension.substring(0,1)}}
                    </button>
                </div>

            </div>
            <div class="col-xs-3">

                <button class="btn btn-danger col-xs-12" style="margin: 10px" data-ng-click="deleteItem($index)">
                    Delete
                </button>

                <div class="col-xs-12 itemPrice">
                    <p>{{getAmountForOrderItem(orderItem).toFixed(2)}}</p>
                </div>

            </div>
        </div>

        <!--loyalty complimentary-->

        <div class="row"
             data-ng-if="orderItem.orderDetails.hasBeenRedeemed == true">

            <div class="col-xs-3">
                <button class="btn btn-sq-lg-cm col-xs-12" data-ng-click="customizeModalOpen(orderItem)">
                    <p data-ng-if="orderItem.orderDetails.addons.length > 0" style="width: 80px;">
                        {{getCustomizationAbb(orderItem)}}
                    </p>

                    <div data-ng-if="orderItem.orderDetails.addons.length == 0">
                        Customize
                    </div>


                </button>

            </div>

            <div class="divWOSizeProfile">
                <span class="col-xs-6  orderTextWOSizeProfile">{{getProductAliasNameIfPresent(orderItem.productDetails.name,orderItem.productDetails.productAliasName)}}</span>
            </div>


            <div class="col-xs-3">

                <button class="btn btn-danger col-xs-12" data-ng-click="deleteItem($index)" style="margin:10px">
                    Delete
                </button>

                <div class="col-xs-12 itemPrice">
                    <p>{{getAmountForOrderItem(orderItem).toFixed(2)}}</p>
                </div>

            </div>

        </div>


        <!--No Size Profile-->

        <div class="row"
             data-ng-if="(orderItem.productDetails.hasAddons == true) && (orderItem.productDetails.hasSizeProfile == false) && (orderItem.orderDetails.hasBeenRedeemed == false)">

            <div class="col-xs-3">
                <button class="btn btn-sq-lg col-xs-12" data-ng-click="customizeModalOpen(orderItem)">
                    <p data-ng-if="orderItem.orderDetails.addons.length > 0" style="width: 80px;">
                        {{getCustomizationAbb(orderItem)}}
                    </p>

                    <div data-ng-if="orderItem.orderDetails.addons.length == 0">
                        Customize
                    </div>


                </button>

                <div class="input-group col-md-12 plusMinus">
                                <span class="input-group-btn ">
                                    <a href="javascript:;" class="btn btn-default btn-circle" data-ng-click="decreaseCount($index)"><span
                                            class="glyphicon glyphicon-minus"></span></a>
                                </span>

                    <div style="text-align: center; vertical-align: middle;width:35px;overflow: auto;">
                        <input type="number" name="quantity" id="quantity"
                               class="input-form-control"
                               style="width: 25px; margin: 5px; text-align:center;"
                               data-ng-model="orderItem.orderDetails.quantity"
                               placeholder="orderItem.orderDetails.quantity"/>
                        <!--<p>{{orderItem.orderDetails.quantity}}</p>-->
                    </div>
                                <span class="input-group-btn">
                                    <a href="jvascript:;" class="btn btn-default btn-circle" data-ng-click="increaseCount($index)"><span
                                            class="glyphicon glyphicon-plus"></span></a>
                                </span>
                </div>
            </div>

            <div class="divWOSizeProfile">
                <span class="col-xs-6  orderTextWOSizeProfile">{{getProductAliasNameIfPresent(orderItem.productDetails.name,orderItem.productDetails.productAliasName)}}</span>
            </div>


            <div class="col-xs-3">

                <button class="btn btn-danger col-xs-12" style="margin:10px" data-ng-click="deleteItem($index)">
                    Delete
                </button>

                <div class="col-xs-12 itemPrice">
                    <p>{{getAmountForOrderItem(orderItem).toFixed(2)}}</p>
                </div>

            </div>

        </div>


    </div>

</div>


<div class="transactionDisplay" style="height:105%;">
    <div class="text-center">
        <button type="button" class="btn btn-primary" data-ng-click="discountModalOpen()">Apply Discount</button>
        <button type="button" class="btn btn-warning" data-ng-click="offerModalOpen()" data-ng-if="orderType!='subscription'">Apply Coupon</button>
        <button type="button" class="btn btn-success" data-ng-click="remarkModalOpen()">Add Remarks</button>
    </div>
    <p style="text-align:left; margin-left: 10px">
        Sub Total
        <span style="float:right; margin-right:10px; color:deepskyblue;font-weight: bold;">{{subTotal().toFixed(2)}}</span>
    </p>

    <p style="text-align:left; margin-left: 10px">
        Promotional Offer
        <span style="float:right; margin-right:10px;color:lightcoral;font-weight: bold;">{{getPromotionalOffer().toFixed(2)}}</span>
    </p>

    <p style="text-align:left; margin-left: 10px;">
        Discount @ {{transactionObj.discountDetail.discount.percentage.toFixed(2)}}%
        <span style="float:right; margin-right:10px;color:lightcoral;font-weight: bold;">{{getDiscount().toFixed(2)}}</span>
    </p>

    <p style="text-align:left; margin-left: 10px">
        VAT on MRP @{{getTaxPercentage('MRP_VAT')}}%
        <span style="float:right; margin-right:10px; color:limegreen;font-weight: bold;">{{ getTAX("MRP_VAT","MRP").toFixed(2) }}</span>
    </p>

    <p style="text-align:left;margin-left: 10px">
        VAT on Net Price @{{getTaxPercentage('NET_PRICE_VAT')}}%
        <span style="float:right;margin-right:10px; color:limegreen;font-weight: bold;">{{getTAX('NET_PRICE_VAT','NET_PRICE').toFixed(2)}}</span>
    </p>

    <p style="text-align:left;margin-left: 10px">
        Surcharge on VAT @{{getTaxPercentage('SURCHARGE')}}%
        <span style="float:right;margin-right:10px; color:limegreen;font-weight: bold;">{{getSurcharge('SURCHARGE','NET_PRICE').toFixed(2)}}</span>
    </p>

    <p data-ng-show="!isCOD" style="text-align:left;margin-left: 10px">
        Service Tax @{{getTaxPercentage('SERVICE_TAX')}}%
        <span style="float:right;margin-right:10px; color:limegreen;font-weight: bold;">{{getTAX('SERVICE_TAX','NET_PRICE').toFixed(2)}}</span>
    </p>

    <p data-ng-show="!isCOD" style="text-align:left;margin-left: 10px">
        S.B. Cess @{{getTaxPercentage('SB_CESS')}}%
        <span style="float:right;margin-right:10px; color:limegreen;font-weight: bold;">{{getTAX('SB_CESS','NET_PRICE').toFixed(2)}}</span>
    </p>
    <p data-ng-show="!isCOD" style="text-align:left;margin-left: 10px">
        K.K. Cess @{{getTaxPercentage('KK_CESS')}}%
        <span style="float:right;margin-right:10px; color:limegreen;font-weight: bold;">{{getTAX('KK_CESS','NET_PRICE').toFixed(2)}}</span>
    </p>

    <p style="text-align:left;margin-left: 10px; font: 30px bold;font-family:'lane';">
        Total
        <span style="float:right;margin-right:10px; color:peru;font: 30px bold;font-family: 'lane' ">{{getPaidAmount().toFixed(2)}}</span>
    </p>

    <div class="text-center" style="margin-top: 30px;">
        <a href="#" class="btn btn-default" data-ng-show="!isCOD" data-ng-click="redeemModalOpen()">Redeem Check</a>
        <a href="#" class="btn btn-default" data-ng-show="isCOD" data-ng-click="switchOutlet()">Switch Outlet</a>
        <a href="#" class="btn btn-info" style="margin-left: 10px;" data-ng-if="orderType!='subscription'" data-ng-click="fullCustomizeModalOpen()">Full Order Complimentary</a>
        <a href="#" class="btn btn-danger" style="margin-left: 10px;" data-ng-show="orderType!='subscription'" data-ng-click="settlementModalOpen()">Make Payments</a>
        <a href="#" class="btn btn-danger" style="margin-left: 10px;" data-ng-show="orderType=='subscription'" data-ng-click="settlementModalOpen()">Create Subscription</a>
    </div>



</div>





