<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->


<div flash-message="5000"></div>
<div class="row" ng-if="freeKettle && orderType!='subscription'">
    <div class="alert free-kettle text-center">This customer is
        eligible for availing Free Kettle
    </div>
</div>
<div class="container-fluid" data-ng-init="init()">
    <div class="row">
        <div class="col-xs-3">
            <form class="navbar-form bs-navbar-top-example navbar-static-top"
                  style="margin-top: 20px" role="search" name="searchOrderForm">
                <button type="button" class="btn btn-warning pull-left"
                        style="margin-left: 50px" ng-click="backToCODCover()">back
                </button>
            </form>
        </div>
        <div class="col-xs-9">
            <label>Select Brand</label>
            <select class="form-control" data-ng-options="brand as brand.brandName for brand in brandList track by brand.brandId"
                    data-ng-model="selectedBrand" data-ng-change="setSelectedBrand(selectedBrand)"></select>
        </div>
    </div>
    <div class="row">
        <div class="col-sm-3">
            <label class="cityLabel">Select City</label>
        </div>
        <div class="col-sm-3">
            <label class="cityLabel">Select Cafe</label>
        </div>
    </div>
    <div class="row">
        <div class="col-sm-3">
            <div data-ng-repeat="city in cityList">
                <div class="cardCity"
                     data-ng-class="{'selectedCard': selectedCity.id == city.id}"
                     data-ng-click="getUnitCityMapping(city)">
                    <div class="containerCard">
                        <h4>
                            <b>{{city.code}}</b>
                        </h4>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-9">
            <div class="row">
                <div data-ng-repeat="unit in unitList | orderBy:'name'">
                    <div class="col-sm-4" data-ng-if="amazonMappedBrandPricingUnits.indexOf(unit.id)>=0">
                        <div class="cardCity"
                             data-ng-class="{'selectedCard': selectedUnit.id == unit.id}"
                             data-ng-click="selectUnit(unit)">
                            <div class="containerCard">
                                <h4>
                                    <b>{{unit.name}}</b>
                                </h4>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
