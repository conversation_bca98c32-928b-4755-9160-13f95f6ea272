/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

angular.module('posApp').controller('loyalteaRedeemptionCtrl',
    ['posAPI', '$scope', 'AppUtil', 'socketUtils', '$modalInstance', 'productService', 'trackingService', '$rootScope', '$timeout','freeChaiCount',
        function (posAPI, $scope, AppUtil, socketUtils, $modalInstance, productService, trackingService, $rootScope, $timeout,freeChaiCount) {
            $scope.couponCode = {coupon: ""};
            $scope.applyDisabled = false;
            $scope.disableCoupons = false;
            $scope.Qty = 0;
            $scope.init = function () {
                $scope.noOfFreeChais = freeChaiCount;
                var chaisString = $scope.noOfFreeChais > 1 ? "chais" : "chai" 
                $scope.loyalteaRedeemModalHeading = "Customer has "+ $scope.noOfFreeChais + " free " + chaisString + " available";
                $scope.noOfChais =[];
                for(var i=1;i<=$scope.noOfFreeChais;i++){
                    $scope.noOfChais.push(i); 
                }
            };

            $scope.setChaiRedeemQty = function (Qty) {
                console.log("chaiRedeemqty::::",Qty);
                $scope.Qty = Qty;
            }

            $scope.cancel = function () {
                $modalInstance.dismiss("cancel");
            };
            
            $scope.RedeemChais = function () {
                if($scope.Qty > 0){
                    socketUtils.emitMessage({POS_LOYALTEA_REDEMPTION: $scope.Qty});
                    $modalInstance.dismiss('submit');
                }else{
                    bootbox.alert("Please select Chai Quantity");
                }                
            }

        }]);