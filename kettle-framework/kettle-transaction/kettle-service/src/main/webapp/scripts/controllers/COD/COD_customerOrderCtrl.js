/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';
/* jshint sub:true */

angular.module('posApp').controller(
    'COD_customerOrderCtrl',
    ['$rootScope', '$scope', 'AppUtil', '$location', 'posAPI',
        function ($rootScope, $scope, AppUtil, $location, posAPI) {
            $scope.deliveryAddress = {};
            $scope.orders = AppUtil.orderSummaryObj;
            $scope.customer = AppUtil.customerSearchObject;
            $scope.isSearchEmpty = true;
            $scope.showFreeChaiDelivery = false;
            $scope.isCOD = $rootScope.globals.currentUser.unitFamily == "COD";
            $scope.getCustomerOrders = function () {
                //var reqObj = AppUtil.GetRequest($scope.searchText);
                if ($scope.searchText!=null && $scope.searchText.toString().length == 10) {
                    $rootScope.showFullScreenLoader = true;
                    posAPI.allUrl('/',AppUtil.restUrls.customer.profileLookup).post($scope.searchText).then(populateOrders, function (err) {
                        //console.log("Error in resposne :: " + err);
                        $rootScope.showFullScreenLoader = false;
                    });
                } else {
                    AppUtil.myAlert('Contact Number should be of 10 digits!');
                }
            };
            $scope.backToCover = function () {

                if ($scope.isCOD) {
                    $location.url('/CODCover');
                } else {
                    $location.url('/cover');
                }

                AppUtil.orderSummaryObj = {}; //to clear the order summary object
                AppUtil.customerSearchObject = {}; //to clear the customer search object
            };

            $scope.$watch('searchText', function (newvalue, oldvalue) {
                $scope.isSearchEmpty = $scope.isEmpty(newvalue);
            });

            $scope.isEmpty = function (obj) {
                if (obj != undefined && obj != null) {
                    if (typeof obj == 'string')
                        return obj.length == 0;
                    else
                        return Object.keys(obj).length == 0;
                }
                return true;
            };

            $scope.openOrderSearch = function (generateOrderId) {
                AppUtil.openOrderSearch(generateOrderId, "order");
                var reqObj = AppUtil.GetRequest(generateOrderId);
            };

            function populateOrders(response) {
                var responseData = response.plain();

                AppUtil.customerSearchObject = responseData.customer;
                AppUtil.orderSummaryObj = responseData.orders;

                $scope.customer = AppUtil.customerSearchObject;
                console.log($scope.customer);
                if(AppUtil.enablefreeChaiDelivery){
                    $scope.checkFreeChaiDelivery();
                }
                $scope.orders = AppUtil.orderSummaryObj;
                $scope.orders.map(function (order) {
                    order.brand = AppUtil.getBrandByBrandId(order.brandId);
                });
                $rootScope.showFullScreenLoader = false;
            }

            $scope.checkFreeChaiDelivery = function () {
                if ($scope.customer != undefined && $scope.customer != null
                    && $scope.customer.id != undefined && $scope.customer.id != null
                    && $scope.customer.id > 5) {

                    posAPI
                        .allUrl('/', AppUtil.restUrls.order.checkFreeChaiDelivery)
                        .get($scope.customer.id)
                        .then(function (response) {
                            console.log(response)
                            if(response!=undefined && response!=null && response==true){
                                $scope.showFreeChaiDelivery=true;
                            }
                        }, function (err) {
                            AppUtil.myAlert(err.data.errorMessage);
                        });
                }
            };

            $scope.sendFreeChaiDeliverySms = function () {
                if ($scope.customer != undefined && $scope.customer != null
                    && $scope.customer.id != undefined && $scope.customer.id != null
                    && $scope.customer.id > 5) {

                    posAPI
                        .allUrl('/', AppUtil.restUrls.order.sendFreeChaiDeliverySms+$scope.customer.id)
                        .post()
                        .then(function (response) {
                            console.log(response)
                            $scope.showFreeChaiDelivery=false;
                        }, function (err) {
                            AppUtil.myAlert(err.data.errorMessage);
                        });
                }
            };

        }

    ]).controller('pendingRefundsCtrl', ['$rootScope', '$scope', 'AppUtil', '$location', 'posAPI',
        function ($rootScope, $scope, AppUtil, $location, posAPI) {

            function getPendingRefunds() {
                $rootScope.showFullScreenLoader = true;
                posAPI.allUrl('/',AppUtil.restUrls.paymentManagement.pendingRefunds).getList()
                .then(function(response){
                    $scope.payments = response.plain();
                    $rootScope.showFullScreenLoader = false;
                }, function (err) {
                    $rootScope.showFullScreenLoader = false;
                    console.log("Encountered error while getting pending refunds");
                });
            }

            $scope.init = function(){
                $scope.payments = [];
                getPendingRefunds();
            };

            $scope.goBack = function(){
              $location.path("CODCover");
            };

            $scope.refundPayment = function(index,payment){
                $rootScope.showFullScreenLoader = true;
                posAPI.allUrl('/',AppUtil.restUrls.paymentManagement.refund)
                    .post(payment.orderPaymentDetailId).then(function(response){
                        $rootScope.showFullScreenLoader = false;
                        if(response!=undefined){
                            payment = response.plain();
                            if (payment.paymentStatus == "REFUND_PROCESSED"){
                                $scope.payments[index] = payment;
                                bootbox.alert("Refund successfully processed on " + payment.paymentPartner);
                            }else{
                                bootbox.alert("Refund cannot be processed for " + payment.paymentPartner +". Please contact Tech Team for further support");
                            }
                        }else{
                            bootbox.alert("Refund cannot be processed for " + payment.paymentPartner +". Please contact Tech Team for further support");
                        }

                    }, function (err) {
                        $rootScope.showFullScreenLoader = false;
                        console.log("Encountered error while getting pending refunds", err);
                    }
                );
            };
        }
    ]);

