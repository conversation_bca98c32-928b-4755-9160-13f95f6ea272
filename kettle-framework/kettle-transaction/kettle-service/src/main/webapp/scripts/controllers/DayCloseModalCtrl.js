/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

angular.module('posApp').controller('DayCloseModalCtrl',
    ['$scope', 'AuthenticationService', 'posAPI', 'AppUtil', '$location', '$modal', '$modalInstance', '$rootScope','showPullTransferOptions','DayCloseReasonList','unitPullTransfers',
        function ($scope, AuthenticationService, posAPI, AppUtil, $location, $modal, $modalInstance, $rootScope,showPullTransferOptions,DayCloseReasonList,unitPullTransfers) {


            //passcode reset
            $scope.usercode = null;
            $scope.oldPassword = null;
            $scope.newPassword = null;
            $scope.transactionMetadata = AppUtil.getTransactionMetadata();
            $scope.unitId = null;
            // order cancel
            $scope.dataLoading = false;
            $scope.pwd = null;
            $scope.userId = null;
            $scope.cancelOrderReason = null;
            //day close
            $scope.dayCloseComment = null;
            $scope.showPullTransferOptions=showPullTransferOptions;
            $scope.selectedDayCloseReason = null;

            //discount
            ////console.log(AppUtil.discountValue);
            if (AppUtil.discountValue > 0) {
                $scope.transactionObj.discountDetail.discount.percentage = 0;
            }
            $scope.discountValue = AppUtil.discountValue;

            $scope.discountPercentage = 0;

            if (!angular.isUndefined($scope.transactionObj) && $scope.transactionObj != null) {
                if ($scope.transactionObj.discountDetail.discount.wasValueSet) {
                    $scope.discountValue = $scope.transactionObj.savings;
                } else {
                    $scope.discountPercentage = $scope.transactionObj.discountDetail.discount.percentage;
                }
            }


            $scope.lastBusinessDate = moment(AppUtil.getUnitDetails().lastBusinessDate).format('MMM DD,YYYY');

            $scope.initDiscount = function () { // for init of discount modal
                $scope.transactionObj.discountDetail.discountCode = 0;
            };

            $scope.initDayClose = function(){
                $scope.DayCloseReasonList = DayCloseReasonList;
                $scope.unitPullTransfers=unitPullTransfers;
                $scope.showPullTransferOptions=showPullTransferOptions;
            }
            //scope functions and methods

            //Force Day Close
            $scope.forceDayClose = function () {

                if (!$scope.dayCloseForm.comment.$error.required) {
                    bootbox.confirm("Are you Sure?", function(result){
                        if (result == true) {
                            var requestObj = {
                                reason: $scope.dayCloseComment,
                                unitId: $rootScope.globals.currentUser.unitId,
                                userId: $rootScope.globals.currentUser.userId
                            };
                            $rootScope.showFullScreenLoader = true;
                            posAPI.allUrl('/',AppUtil.restUrls.posMetaData.forceCloseUnit).post(requestObj)
                                .then(function (response) {
                                    //console.log(response);
                                    if(response.errorType != undefined){
                                        AppUtil.myInfoAlert('<p>' + response.errorMessage + '</p>');
                                        $rootScope.showFullScreenLoader = false;
                                    }else{
                                        if (response) {
                                            AppUtil.mySuccessAlert("Day Closed Successfully!");
                                            // logout after day close

                                            posAPI.allUrl('/',AppUtil.restUrls.users.logout).post($rootScope.globals.currentUser)
                                                .then (function (response) {
                                                    $modalInstance.dismiss('dismiss');
                                                    $location.url('/login');
                                                    $rootScope.showFullScreenLoader = false;
                                                }, function (err) {
                                                    AppUtil.myAlert("Error with status code", response.status);
                                                    $rootScope.showFullScreenLoader = false;
                                                });
                                        } else {
                                            AppUtil.myInfoAlert("Error while day close, Please consult Technical Team");
                                            $rootScope.showFullScreenLoader = false;
                                        }
                                    }
                                }, function (err) {
                                    AppUtil.myAlert(err.data.errorMessage);
                                    $rootScope.showFullScreenLoader = false;
                                });
                        }else{
                            $modalInstance.dismiss('dismiss');
                        }
                    });
                } else {
                    AppUtil.myInfoAlert("Comment is required");
                }
            };

            //Day Close
            $scope.submitDayClose = function () {
                var temp = null;
                if($scope.selectedDayCloseReason!==undefined && $scope.selectedDayCloseReason!==null
                    && $scope.selectedDayCloseReason.category!==undefined && $scope.selectedDayCloseReason.category!==null
                    && $scope.selectedDayCloseReason.reason!==undefined && $scope.selectedDayCloseReason.reason!==null) {
                    temp=$scope.selectedDayCloseReason.category + ' - ' + $scope.selectedDayCloseReason.reason
                }
                if (!$scope.dayCloseForm.comment.$error.required) {
                    bootbox.confirm("Are you Sure?", function(result){
                        if (result == true) {
                            var requestObj = {
                                reason: $scope.dayCloseComment,
                                unitId: $rootScope.globals.currentUser.unitId,
                                userId: $rootScope.globals.currentUser.userId,
                                pullTransferReason: temp,
                                unitPullTransfersList : $scope.unitPullTransfers
                            };
                            $rootScope.showFullScreenLoader = true;
                            posAPI.allUrl('/',AppUtil.restUrls.posMetaData.closeUnit).post(requestObj)
                                .then(function (response) {
                                    //////console.log(response);
                                    if(response.errorType != undefined){
                                        AppUtil.myInfoAlert('<p>' + response.errorMessage + '</p>');
                                        $rootScope.showFullScreenLoader = false;
                                    }else{
                                        if (response) {
                                            AppUtil.mySuccessAlert("Day Closed Successfully!");
                                            // logout after day close

                                            posAPI.allUrl('/',AppUtil.restUrls.users.logout).post($rootScope.globals.currentUser)
                                                .then (function (response) {
                                                    $modalInstance.dismiss('dismiss');
                                                    $location.url('/login');
                                                    $rootScope.showFullScreenLoader = false;
                                                }, function (err) {
                                                    AppUtil.myAlert("Error with status code", response.status);
                                                    $rootScope.showFullScreenLoader = false;
                                                });
                                        } else {
                                            AppUtil.myInfoAlert("Error while day close, Please consult Technical Team");
                                            $rootScope.showFullScreenLoader = false;
                                        }
                                    }
                                }, function (err) {
                                    AppUtil.myAlert(err.data.errorMessage);
                                    $rootScope.showFullScreenLoader = false;
                                });
                        }else{
                            $modalInstance.dismiss('dismiss');
                        }
                    });
                } else {
                    AppUtil.myInfoAlert("Comment is required");
                }
            };

            $scope.cancelDayClose = function () {
                $modalInstance.dismiss('dismiss');
            };


            $scope.initCancellation = function(){
                $scope.isOnlineOrder = false;
                //scope variable for refund on cancel order
                var category = $scope.OrderObj.settlements[0].modeDetail.category;
                if (AppUtil.isCOD()){
                    $scope.refund = false;
                    $scope.isOnlineOrder = (category=="ONLINE");
                }else{
                    $scope.showRefundMessage = (category=="ONLINE");
                }
            };


            // Order Cancel with user authentication
            $scope.cancelOrderCancel = function () {
                $modalInstance.dismiss('dismiss');
            };

            $scope.submitOrderCancel = function () {
                if (!$scope.orderCancelForm.comment.$error.required) {
                    bootbox.confirm("Are you Sure, you want to cancel this order?", function(result){
                        if (result == true) {
                            cancelThisOrder();
                        }else {
                            $modalInstance.dismiss('dismiss');
                        }
                    });
                } else {
                    AppUtil.myInfoAlert("Comment is required");
                }
            };

            $scope.setRefund = function(refund){
                $scope.refund=refund;
            };

            function cancelThisOrder() {
                if($scope.selectedReason == null){
                    AppUtil.myAlert("Please select the mandatory cancellation Reason");
                    return;
                }
                var wastageType = null;
                if($scope.selectedReason.noWastage && !$scope.selectedReason.completeWastage){
                    wastageType = 'NO_WASTAGE';
                }else if(!$scope.selectedReason.noWastage && $scope.selectedReason.completeWastage){
                    wastageType = 'COMPLETE_WASTAGE';
                }else{
                    wastageType = 'COMPLETE_WASTAGE';
                }


                $modalInstance.dismiss('submit');
                $rootScope.showFullScreenLoader = true;
                $scope.cancelOrderReason = $scope.cancelOrderReason + ":Approved by " + $scope.userId;
                var requests = {
                    generatedOrderId: $scope.OrderObj.generateOrderId,
                    orderId: $scope.OrderObj.orderId,
                    approvedBy: $scope.userId,
                    reason: $scope.cancelOrderReason,
                    unitId: $scope.OrderObj.unitId,
                    channelPartner: $scope.OrderObj.channelPartner,
                    orderSource: $scope.OrderObj.source,
                    reasonId : $scope.selectedReason.id,
                    wastageType : wastageType,
                    noTimeConstraint : $scope.forceCancel === true
                };

                if (AppUtil.isCOD() || AppUtil.isTakeaway()) {
                    requests['orderId'] = $scope.OrderObj.orderId;
                    requests['category'] = $scope.OrderObj.source;
                    requests['unitCategory'] = AppUtil.unitFamily;
                    requests['orderStatus'] = "CANCELLED_REQUESTED";
                    requests['unitId'] = $scope.OrderObj.unitId;
                    if($scope.refund !=undefined) {
                        requests['refund'] = $scope.refund;
                    }
                }

                var reqObj = AppUtil.GetRequest(requests);
                //console.log("cancellation object");
                //console.log(reqObj);

                posAPI.allUrl('/', AppUtil.restUrls.order.cancelOrder).post(reqObj)
                    .then(function (response) {
                        if (response == undefined) {
                            $rootScope.showFullScreenLoader = false;
                            AppUtil.myAlert('There was an error while cancelling the order');
                            return;
                        }
                        if (response.errorType != undefined) {
                            AppUtil.myAlert(response.errorMessage);
                        } else {
                            if (response) {
                                //console.log("cancellation response :::", response);
                                AppUtil.myAlert((AppUtil.isCOD() || AppUtil.isTakeaway()) ? "Cancel Requested for Order Successfully" : "Order Successfully Cancelled");
                                //$location.url(AppUtil.isCOD() ? '/CODCover' : '/cover');
                                cancelChannelPartnerOrder();
                            } else {
                                //console.log(response);
                                AppUtil.myAlert("Order can not be cancelled");
                            }
                        }
                        $rootScope.showFullScreenLoader = false;
                    }, function (err) {
                        $rootScope.showFullScreenLoader = false;
                        //////console.log(err);
                        AppUtil.myAlert(err.data.errorMessage);
                    });
            }

            function cancelChannelPartnerOrder() {
                if($scope.OrderObj.channelPartner == 6){
                    posAPI.allUrl('/', AppUtil.restUrls.partnerOrder.markCancelled + "?kettleOrderId=" + $scope.OrderObj.orderId)
                        .post().then(function (response) {
                        if (response != undefined && response.errorType == undefined) {
                            if (response) {
                                AppUtil.myAlert((AppUtil.isCOD() || AppUtil.isTakeaway()) ? "Cancel Requested for Order Successfully" : "Order Successfully Cancelled");
                                $location.url(AppUtil.isCOD() ? '/CODCover' : '/cover');
                            } else {
                                AppUtil.myAlert("Order can not be cancelled");
                            }
                        }
                    }, function (err) {
                        console.log('Error in getting response', err);
                    });
                } else {
                    $location.url(AppUtil.isCOD() ? '/CODCover' : '/cover');
                }
            }

            function Logout(userObj) {

                posAPI.allUrl('/',AppUtil.restUrls.users.logout).post(userObj)
                    .then (function (response) {
                        //////console.log('logged out');

                    }, function (err) {
                        AppUtil.myAlert(err.data.errorMessage);

                    });
            }

            //Passcode Reset

            $scope.PasscodeReset = function () {
                //////console.log($scope.usercode,$scope.oldPassword);
                if($scope.oldPassword == $scope.newPassword){
                    AppUtil.myAlert("Your old and current Passcode can not be same. Please enter new Passcode");
                    return;
                }
                AuthenticationService.PasscodeReset($scope.usercode,
                    $scope.oldPassword, $scope.unitId, $scope.newPassword, function (response) {
                        AppUtil.mySuccessAlert("Pass code Change Success!");
                        $modalInstance.dismiss('cancel');
                    }, function (err) {
                        AppUtil.myAlert(err.data.errorMessage);
                    });
            };

            $scope.cancelReset = function () {
                $modalInstance.dismiss('cancel');
            };

            //remark modal

            $scope.submitRemark = function (orderRemark) {
                $scope.orderRemark = orderRemark;
                ////console.log("orderRemark from modal instance ::: "+$scope.orderRemark);
                $modalInstance.close($scope.orderRemark);
            };

            $scope.cancelRemark = function () {
                $modalInstance.dismiss('cancel');
            };

            //discount Modal
            function resetDiscount() {
                $scope.transactionObj.discountDetail.discount.value = 0;
                $scope.transactionObj.discountDetail.discount.percentage = 0;
                $scope.calculatePaidAmount();
            }

            $scope.checkForOrConditionsDiscountValue = function () {
                $scope.discountPercentage = 0;
                $scope.transactionObj.discountDetail.discount.wasValueSet = true;
                resetDiscount();
            };

            $scope.checkForOrConditionsDiscountPercentage = function () {
                $scope.discountValue = 0;
                $scope.transactionObj.discountDetail.discount.wasValueSet = false;
                resetDiscount();
            };

            $scope.submit = function () {
                //////console.log($scope);
                var totalAmount = $scope.transactionObj.totalAmount - $scope.promotionalOffer;

                if ($scope.transactionObj.discountDetail.discount.wasValueSet) {
                    var taxRate = (($scope.transactionObj.paidAmount) / totalAmount);
                    var effectiveDiscount = (($scope.discountValue) / taxRate);
                    $scope.transactionObj.discountDetail.discount.value = effectiveDiscount;
                    $scope.transactionObj.discountDetail.discount.percentage = (effectiveDiscount * 100) / totalAmount;
                } else {
                    $scope.transactionObj.discountDetail.discount.value = totalAmount * ($scope.discountPercentage / 100);
                    $scope.transactionObj.discountDetail.discount.percentage = $scope.discountPercentage;
                }

                if ($scope.transactionObj.discountDetail.discount.value < totalAmount) {
                    //console.log($scope.transactionObj.discountDetail);
                    $scope.updateTotalDiscount(true);
                    closeModal();
                } else {
                    $scope.transactionObj.discountDetail.discount.value = 0;
                    $scope.transactionObj.discountDetail.discount.percentage = 0;
                    AppUtil.myAlert('Invalid discount entered');
                }

            };

            function closeModal() {
                AppUtil.discountObj.value = $scope.transactionObj.discountDetail.discount.value;
                AppUtil.discountObj.percentage = $scope.transactionObj.discountDetail.discount.percentage;
                AppUtil.discountObj.discountReason = $scope.transactionObj.discountDetail.discountReason;
                AppUtil.discountObj.discountCode = $scope.transactionObj.discountDetail.discountCode;
                AppUtil.discountValue = $scope.discountValue;
                //console.log(AppUtil.discountValue);
                $modalInstance.close();
            }

            $scope.cancelDiscount = function () {

                $scope.transactionObj.discountDetail.discountCode = 0;
                $scope.transactionObj.discountDetail.discount.percentage = 0;
                $scope.transactionObj.discountDetail.discount.value = 0;
                $scope.transactionObj.discountDetail.discountReason = null;
                $scope.transactionObj.discountDetail.discount.wasValueSet = false;
                AppUtil.discountObj.value = 0;
                AppUtil.discountObj.percentage = 0;
                AppUtil.discountObj.discountReason = null;
                AppUtil.discountObj.discountCode = 0;
                AppUtil.discountValue = 0;
                $scope.updateTotalDiscount(false);
                $modalInstance.dismiss();

            };

            $scope.dismissModal = function () {
                $modalInstance.dismiss();
            }

        }]);
