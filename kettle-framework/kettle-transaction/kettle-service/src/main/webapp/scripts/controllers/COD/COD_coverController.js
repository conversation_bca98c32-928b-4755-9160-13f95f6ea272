/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

angular
    .module('posApp')
    .controller('COD_coverController', ['$location', '$scope', '$rootScope', '$cookieStore',
        'AuthenticationService', '$modal', 'AppUtil', '$timeout', 'posAPI', 'PrintService', 'coverUtils', function ($location, $scope, $rootScope, $cookieStore,
                                                                                                                    AuthenticationService, $modal, AppUtil, $timeout, posAPI, PrintService, coverUtils) {

            /*angular.module('posApp').controller('COD_cslookup',
             function ($location, $scope, $rootScope, posAPI, AppUtil, $modal, PrintService) {*/


            $scope.unitDetails = AppUtil.getUnitDetails();
            $scope.transactionMetadata = AppUtil.getTransactionMetadata();

            $scope.isDefaultPasscode = $rootScope.isDefaultPasscode;

            $scope.lastThreeOrderArray = [];

            $scope.init = function () {
                $rootScope.isPartnerOrder = false;
                AppUtil.CSObj = {};
                AppUtil.customerDeliveryAddress = 0;
                AppUtil.activeChannelPartnerId = -1;
                console.log($scope.unitDetails);
                if (AppUtil.isEmptyObject(AppUtil.getTransactionMetadata())) {
                    coverUtils.logOut();
                }
                $scope.isCOD = AppUtil.isCOD();
                if ($cookieStore.get("lastThreeOrders") != null && $cookieStore.get("lastThreeOrders").unitId == $scope.unitDetails.id) {
                    //console.log("inside if statement of unitId check");
                    $rootScope.lastThreeOrders = $cookieStore.get("lastThreeOrders").orders;
                    $scope.lastThreeOrderArray = $rootScope.lastThreeOrders;
                } else {
                    $rootScope.lastThreeOrders = null;
                    $scope.lastThreeOrderArray = null;
                }
                $scope.isManager = $scope.currentUser.designation.name == "Manager" ? true : false;
            };

            function markKettleOrderToRejectedPartnerOrder(generateOrderId, partnerOrderId, unitId) {
                $rootScope.showFullScreenLoader = true;
                posAPI.allUrl('/', AppUtil.restUrls.partnerOrder.addkettleOrderForRejection + "?orderId=" + partnerOrderId
                    + "&kettleOrderId=" + generateOrderId + "&unitId=" + unitId)
                    .post().then(function (response) {
                        if (response != null && response == true) {
                            console.log('SuccessFully Added Kettle Order To Partner Order Detail');
                        }
                        $rootScope.showFullScreenLoader = false;
                    }, function (err) {
                        console.log('Error in getting response', err);
                        $rootScope.showFullScreenLoader = false;
                    });
            };

            $rootScope.$watch('lastThreeOrders', function (newVal, oldVal) {
                //	console.log("oldVal and newVal",oldVal,newVal);

                if ($cookieStore.get("lastThreeOrders") != null && $cookieStore.get("lastThreeOrders").unitId == $scope.unitDetails.id) {
                    $rootScope.lastThreeOrders = newVal;
                    $scope.lastThreeOrderArray = $rootScope.lastThreeOrders;

                    if ($scope.isCOD) {
                        if (oldVal == null || oldVal[oldVal.length - 1] != newVal[newVal.length - 1]) {
                            if ($rootScope.rejectedPartnerOrder != null) {
                                markKettleOrderToRejectedPartnerOrder(newVal[newVal.length - 1], $rootScope.rejectedPartnerOrder.orderId, $rootScope.rejectedPartnerOrder.unitId)
                                $rootScope.rejectedPartnerOrder = null;
                            }
                        }
                    }

                }

            });

            $scope.openOrderSearch = function (generateOrderId) {
                AppUtil.openOrderSearch(generateOrderId, "order");
            };

            $scope.orderStart = function (partnerOrder) {
                AppUtil.freeKettle = false;
                $rootScope.orderType = "order";
                $rootScope.isPartnerOrder = partnerOrder;
                if(partnerOrder == false) {
                    AppUtil.setSelectedBrand(AppUtil.getBrandByBrandId(AppUtil.CHAAYOS_BRAND_ID));
                    AppUtil.setPartnerId(2); //chaayos delivery partner id
                }
                $location.url('/CODCSlookup');
            };

            $scope.swiggyOrderStart = function () {
                AppUtil.freeKettle = false;
                $rootScope.orderType = "order";
                $rootScope.isPartnerOrder = true;
                AppUtil.setPartnerId(6); //swiggy partner id
                $location.url('/swiggyOrderView');
            };

            $scope.zomatoOrderStart = function () {
                AppUtil.freeKettle = false;
                $rootScope.orderType = "order";
                $rootScope.isPartnerOrder = true;
                AppUtil.setPartnerId(3); //zomato partner id
                $location.url('/swiggyOrderView');
            };

            $scope.magicpinOrderStart = function () {
                AppUtil.freeKettle = false;
                $rootScope.orderType = "order";
                $rootScope.isPartnerOrder = true;
                AppUtil.setPartnerId(24); //magicpin partner id
                $location.url('/swiggyOrderView');
            };

            $scope.amazonOrderStart = function () {
                AppUtil.freeKettle = false;
                $rootScope.orderType = "order";
                $rootScope.isPartnerOrder = true;
                AppUtil.setPartnerId(18); //amazon partner id
                $location.url('/amazonOrderView');
            };

            $scope.subscriptionStart = function () {
                $rootScope.orderType = "subscription";
                $location.url('/CODCSlookup');
            };

            $scope.subscriptionSearch = function () {
                $location.url('/subscriptionSearch');
            };

            $scope.subscriptionOrderSearch = function () {
                $location.url('/subscriptionOrderSearch');
            };

            $scope.subscriptionOrderByUnit = function () {
                $location.url('/subscriptionOrderByUnit');
            };

            $scope.viewSDP = function () {
                $location.url('/viewSDP');
            };
            $scope.cafePartnerStatus = function () {
                $location.url('/cafePartnerStatus');
            };

            $scope.pendingRefunds = function () {
                $location.url('/pendingRefunds');
            };

            $scope.partnerOrderDashboard = function () {
                $location.url('/partnerOrderDashboard');
            };

            $scope.showCafeTimingsDashboard = function (){
                $location.url('/cafeTimingsDashboard');
            }

            $scope.partnerCafeDashboard = function () {
                console.log('came here');
                $location.url('/partnerCafeDashboard');
            };

            $scope.currentUser = $rootScope.globals.currentUser;


            coverUtils.initDiscount();
            timerClear();

            function timerClear() {
                $scope.$broadcast('timer-clear');
            }

            //logout
            $scope.logOut = function () {
                coverUtils.logOut();
            };

            //day close
            $scope.dayCloseModalOpen = function () {
                $modal.open({
                    animation: true,
                    templateUrl: window.version + 'views/dayCloseModal.html',
                    controller: 'DayCloseModalCtrl',
                    backdrop: 'static',
                    scope: $scope,
                    size: 'lg'
                });
            };

            //order search
            $scope.openOrderSearchScreen = function () {
                coverUtils.openOrderSearchScreen();
            };

            //order summary
            $scope.goToOrderSummary = function () {
                coverUtils.goToOrderSummary();
            };

            //order summary
            $scope.goToCustomerOrderSummary = function () {
                AppUtil.orderSummaryObj = {};
                coverUtils.goToCustomerOrderSummary();
            };
            //test printer

            $scope.testPrinter = function () {
                coverUtils.testPrinter();
            };

            $scope.openInventoryScreen = function () {
                coverUtils.openInventoryScreen();
            };


            $scope.openOrderSummary = function () {
                $location.url('/openOrderSummary');
            };

            $scope.resetConfig = function () {
                AppUtil.removeAutoConfigData();
                $scope.logOut();
            }


        }]);





