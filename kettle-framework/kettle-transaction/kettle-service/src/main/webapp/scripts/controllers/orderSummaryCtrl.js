/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';
/*jshint sub:true*/

angular.module('posApp')
    .controller('orderSummaryCtrl', ['$rootScope', '$http', '$scope', 'AppUtil', '$location', 'posAPI', '$sce',
        function ($rootScope, $http, $scope, AppUtil, $location, posAPI, $sce) {


            $scope.OrderObj = AppUtil.orderSummaryObj;
            if ($scope.OrderObj.employee != null && $scope.OrderObj.employee != undefined) {
                $scope.OrderObj.employeeMealOrderItems = $scope.OrderObj.orders;
            }
            $scope.selectedOrderItems = [];
            console.log($scope.OrderObj);
            $scope.redirectUrl = null;
            $scope.groupSelected = true;
            $scope.idSelected = null;
            $scope.startPosition = 1;
            $scope.batchSize = 50;
            $scope.backToCover = function () {
                $location.url($scope.OrderObj.backUrl);
            };

            $scope.openSettlementTable = function (settlement, billNumber) {
                document.querySelector("#row-" + billNumber).classList.add("selected");
                var htmlRow = "";
                for (var idx in settlement) {
                    var settlementRow = settlement[idx];
                    htmlRow += '<tr> <td style=\"text-align: center; border: 0.5px solid;\">' + settlementRow.settlementId + '</td> <td style=\"text-align: center; border: 0.5px solid;\">' + settlementRow.mode + '</td> <td style=\"text-align: center; border: 0.5px solid;\">' + settlementRow.modeDetail.name + '</td><td style=\"text-align: center; border: 0.5px solid;\">' + settlementRow.amount + '</td> <td style=\"text-align: center; border: 0.5px solid;\">' + settlementRow.extraVouchers + '</td> <td style=\"text-align: center; border: 0.5px solid;\"></td> </tr>';
                }
                bootbox.confirm('<h4 style="text-align: center;font-weight: bold;">Bill - ' + billNumber + '</h4>' + '<table><tr style=\"background-color: lightgray\"> <th style=\"text-align: center; padding: 4px; border: 0.5px solid;\">SettlementID</th> <th style=\"text-align: center; padding: 4px; border: 0.5px solid;\">Mode</th> <th style=\"text-align: center; padding: 4px; border: 0.5px solid;\">Mode Detail</th> <th style=\"text-align: center; padding: 4px; border: 0.5px solid;\">Amount</th> <th style=\"text-align: center;padding: 4px; border: 0.5px solid;\">ExtraVouchers</th> <th style=\"text-align: center; padding: 4px; border: 0.5px solid;\">External Settlements</th> </tr>' + htmlRow + '</table>', function (result) {
                    document.querySelector("#row-" + billNumber).classList.remove("selected");
                })
            };


            $scope.jsonOrderObj = function () {
                var jsonArray = [];

                for (var i = 0; i < AppUtil.orderSummaryObj.orders.length; i++) {
                    var singleOrderObj = AppUtil.orderSummaryObj.orders[i];

                    var singlejsonObj = {
                        generatedOrderId: singleOrderObj.generateOrderId,
                        orderId: singleOrderObj.orderId,
                        source: singleOrderObj.source,
                        unitOrderId: singleOrderObj.unitOrderId,
                        billCreationTime: singleOrderObj.billCreationTime,
                        totalAmount: singleOrderObj.transactionDetail.totalAmount,
                        discount: singleOrderObj.transactionDetail.discountDetail.discount.value,
                        tax: singleOrderObj.transactionDetail.tax,
                        taxableAmount: singleOrderObj.transactionDetail.taxableAmount,
                        roundOff: singleOrderObj.transactionDetail.roundOffValue,
                        isParcel: singleOrderObj.hasParcel,
                        status: singleOrderObj.status,
                        brand: AppUtil.getBrandByBrandId(singleOrderObj.brandId)
                    };
                    jsonArray.push(singlejsonObj);
                }
                //console.log(jsonArray);
                return jsonArray;
            };

            $scope.openOrderSearch = function (generateOrderId) {
                AppUtil.openOrderSearch(generateOrderId, "order");
            };

            $scope.getBrandName = function (brandId) {
                return AppUtil.getBrandByBrandId(brandId).brandName;
            }


            $scope.GetCustomerInfo = function ($deliveryAddressId) {
                //var reqObj = AppUtil.GetRequest($deliveryAddressId+"");
                posAPI.allUrl('/', AppUtil.restUrls.customer.lookupAddress).post($deliveryAddressId)
                    .then(function (response) {
                        $rootScope.CustomerObj = response.plain();
                    }, function (err) {
                        $rootScope.CustomerObj = {};
                        AppUtil.myAlert(err.data.errorMessage);
                    });
            };

            $scope.nextOrderSummary = function (){
                $rootScope.showFullScreenLoader = true;
                $scope.startPosition=$scope.startPosition+1
                $rootScope.showViewMoreButton = false;

                var returnObj = null;
                $http({
                    method: 'POST',
                    url:AppUtil.restUrls.order.ordersForDayPageable+"?pageValue="+$scope.startPosition,
                    data:$rootScope.globals.currentUser,
                }).then(function (response){
                    $rootScope.showFullScreenLoader = false;
                    if (response.status === 200 && response.data) {
                        returnObj = response.data;
                        var orderDetails = $scope.OrderObj.orderDetailTrims;
                        if($scope.OrderObj.orderDetailTrims == 0){
                            $rootScope.showViewMoreButton = true;
                        }
                        orderDetails.push.apply(orderDetails, returnObj.orderDetailTrims);
                        $scope.OrderObj.orderDetailTrims = orderDetails;
                        if($scope.OrderObj.orderDetailTrims.length % 50){
                            $rootScope.showViewMoreButton = true;
                        }
                    }
                }, function (err) {
                    $rootScope.showFullScreenLoader = false;
                    AppUtil.myAlert(err.data.errorMessage);
                })
            }

            // $scope.SelectedRow = function (orderItem){
            //     if ($scope.idSelected == orderItem.generateOrderId) {
            //         $scope.groupSelected = !$scope.groupSelected;
            //     }
            //     $scope.idSelected = orderItem.generateOrderId;
            // }

            $scope.openOrderItemsModal = function(orderItems) {
                $scope.selectedOrderItems = orderItems || [];
                $('#orderItemsModal').modal('show');
            };

        }]);
