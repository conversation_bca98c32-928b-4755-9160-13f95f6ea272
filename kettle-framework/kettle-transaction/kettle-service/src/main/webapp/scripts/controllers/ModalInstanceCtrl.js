/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

angular.module('posApp').controller('ModalInstanceCtrl',
    ['$scope', 'AuthenticationService', 'posAPI', 'AppUtil', '$location', '$modal', '$modalInstance', '$rootScope','$timeout', '$http',
    function ($scope, AuthenticationService, posAPI, AppUtil, $location, $modal, $modalInstance, $rootScope, $timeout, $http) {


        //passcode reset
        $scope.usercode = null;
        $scope.oldPassword = null;
        $scope.newPassword = null;
        $scope.transactionMetadata = AppUtil.getTransactionMetadata();
        $scope.unitId = null;
        // order cancel
        $scope.dataLoading = false;
        $scope.pwd = null;
        $scope.userId = null;
        $scope.cancelOrderReason = null;
        $scope.model = {
            otp: null
        };
        //day close
        // $scope.dayCloseComment = null;
        // $scope.showPullTransferOptions=showPullTransferOptions;
        // $scope.selectedDayCloseReason = null;

        //discount
        ////console.log(AppUtil.discountValue);
        if (AppUtil.discountValue > 0) {
            $scope.transactionObj.discountDetail.discount.percentage = 0;
        }
        $scope.discountValue = AppUtil.discountValue;

        $scope.discountPercentage = 0;
        $scope.userVerifiedForCancelation = false;
        $scope.otpVerified = false;

        if (!angular.isUndefined($scope.transactionObj) && $scope.transactionObj != null) {
            if ($scope.transactionObj.discountDetail.discount.wasValueSet) {
                $scope.discountValue = $scope.transactionObj.savings;
            } else {
                $scope.discountPercentage = $scope.transactionObj.discountDetail.discount.percentage;
            }
        }


        $scope.lastBusinessDate = moment(AppUtil.getUnitDetails().lastBusinessDate).format('MMM DD,YYYY');

        $scope.initDiscount = function () { // for init of discount modal
            $scope.transactionObj.discountDetail.discountCode = 0;
        };

        //scope functions and methods

        //Force Day Close
        $scope.forceDayClose = function () {

            if (!$scope.dayCloseForm.comment.$error.required) {
                bootbox.confirm("Are you Sure?", function(result){
                    if (result == true) {
                        var requestObj = {
                            reason: $scope.dayCloseComment,
                            unitId: $rootScope.globals.currentUser.unitId,
                            userId: $rootScope.globals.currentUser.userId
                        };
                        $rootScope.showFullScreenLoader = true;
                        posAPI.allUrl('/',AppUtil.restUrls.posMetaData.forceCloseUnit).post(requestObj)
                            .then(function (response) {
                                //console.log(response);
                                if(response.errorType != undefined){
                                    AppUtil.myInfoAlert('<p>' + response.errorMessage + '</p>');
                                    $rootScope.showFullScreenLoader = false;
                                }else{
                                    if (response) {
                                        AppUtil.mySuccessAlert("Day Closed Successfully!");
                                        // logout after day close

                                        posAPI.allUrl('/',AppUtil.restUrls.users.logout).post($rootScope.globals.currentUser)
                                            .then (function (response) {
                                                $modalInstance.dismiss('dismiss');
                                                $location.url('/login');
                                                $rootScope.showFullScreenLoader = false;
                                            }, function (err) {
                                                AppUtil.myAlert("Error with status code", response.status);
                                                $rootScope.showFullScreenLoader = false;
                                            });
                                    } else {
                                        AppUtil.myInfoAlert("Error while day close, Please consult Technical Team");
                                        $rootScope.showFullScreenLoader = false;
                                    }
                                }
                            }, function (err) {
                                AppUtil.myAlert(err.data.errorMessage);
                                $rootScope.showFullScreenLoader = false;
                            });
                    }else{
                        $modalInstance.dismiss('dismiss');
                    }
                });
            } else {
                AppUtil.myInfoAlert("Comment is required");
            }
        };

        //Day Close
        $scope.submitDayClose = function () {
            var temp = $scope.selectedDayCloseReason.category + ' - ' + $scope.selectedDayCloseReason.reason ;
            console.log($scope.selectedDayCloseReason);
            if (!$scope.dayCloseForm.comment.$error.required) {
                bootbox.confirm("Are you Sure?", function(result){
                    if (result == true) {
                        var requestObj = {
                            reason: $scope.dayCloseComment,
                            unitId: $rootScope.globals.currentUser.unitId,
                            userId: $rootScope.globals.currentUser.userId,
                            pullTransferReason: temp,
                            unitPullTransfersList : $scope.unitPullTransfers
                        };
                        $rootScope.showFullScreenLoader = true;
                        posAPI.allUrl('/',AppUtil.restUrls.posMetaData.closeUnit).post(requestObj)
                            .then(function (response) {
                                //////console.log(response);
                            	if(response.errorType != undefined){
                            		AppUtil.myInfoAlert('<p>' + response.errorMessage + '</p>');
                            		$rootScope.showFullScreenLoader = false;
                                }else{
	                                if (response) {
	                                    AppUtil.mySuccessAlert("Day Closed Successfully!");
	                                    // logout after day close

	                                    posAPI.allUrl('/',AppUtil.restUrls.users.logout).post($rootScope.globals.currentUser)
	                                        .then (function (response) {
	                                            $modalInstance.dismiss('dismiss');
	                                            $location.url('/login');
	                                            $rootScope.showFullScreenLoader = false;
	                                        }, function (err) {
	                                            AppUtil.myAlert("Error with status code", response.status);
	                                            $rootScope.showFullScreenLoader = false;
	                                        });
	                                } else {
	                                    AppUtil.myInfoAlert("Error while day close, Please consult Technical Team");
	                                    $rootScope.showFullScreenLoader = false;
	                                }
                                }
                            }, function (err) {
                                AppUtil.myAlert(err.data.errorMessage);
                                $rootScope.showFullScreenLoader = false;
                            });
                    }else{
                        $modalInstance.dismiss('dismiss');
                    }
                });
            } else {
                AppUtil.myInfoAlert("Comment is required");
            }
        };

        $scope.cancelDayClose = function () {
            $modalInstance.dismiss('dismiss');
        };


        $scope.initCancellation = function(){
            $scope.isOnlineOrder = false;
            $scope.errorMessage = null;
            $scope.otpRequested = false;
            $scope.unitAM = null;
            $scope.unitDAM = null;
            $scope.customerOrAm=null;
            $scope.employeeName = null;
            $scope.employeeNumber = null;
            $scope.skipOtpFlow=false;
            getExcludeCustomersList();
            //scope variable for refund on cancel order
            if($scope.OrderObj.brandId==6){
            $scope.skipOtpFlow=true;
            }
            var category = $scope.OrderObj.settlements[0].modeDetail.category;
            if (AppUtil.isCOD()){
                $scope.refund = false;
                $scope.isOnlineOrder = (category=="ONLINE");
            }else{
            	$scope.showRefundMessage = (category=="ONLINE");
            }
        };


        // Order Cancel with user authentication
        $scope.cancelOrderCancel = function () {
            $modalInstance.dismiss('dismiss');
        };

        $scope.submitOrderCancel = function (otp) {
            if(!$scope.forceCancel && !$scope.skipOtpFlow){
                $scope.verifyOTP(otp);
            }
            $timeout(function() {
                if (!$scope.forceCancel && !$scope.skipOtpFlow && !$scope.otpVerified) {
                    alert("OTP is incorrect");
                    return; // Stop further execution if OTP is incorrect
                }
                checkUserForCancellation();
                if (!$scope.orderCancelForm.comment.$error.required) {
                    bootbox.confirm("Are you Sure, you want to cancel this order?", function(result){
                        if (result == true) {
                            if($scope.userVerifiedForCancelation) {
                                cancelThisOrder();
                            }
                            else{
                                alert("User Id or Passcode is incorrect");
                            }
                        }else {
                            $modalInstance.dismiss('dismiss');
                        }
                    });
                } else {
                    AppUtil.myInfoAlert("Comment is required");
                }
          }, 500);
        };

        $scope.setRefund = function(refund){
          $scope.refund=refund;
        };

        function cancelThisOrder() {
            if($scope.selectedReason == null){
        	AppUtil.myAlert("Please select the mandatory cancellation Reason");
        	return;
            }
            var wastageType = null;
        	if($scope.selectedReason.noWastage && !$scope.selectedReason.completeWastage){
        	    wastageType = 'NO_WASTAGE';
        	}else if(!$scope.selectedReason.noWastage && $scope.selectedReason.completeWastage){
        	    wastageType = 'COMPLETE_WASTAGE';
        	}else{
        	    wastageType = 'COMPLETE_WASTAGE';
        	}


            $modalInstance.dismiss('submit');
            $rootScope.showFullScreenLoader = true;
            $scope.cancelOrderReason = $scope.cancelOrderReason + ":Approved by " + $scope.userId;
                var requests = {
                    generatedOrderId: $scope.OrderObj.generateOrderId,
                    orderId: $scope.OrderObj.orderId,
                    approvedBy: $scope.userId,
                    reason: $scope.cancelOrderReason,
                    unitId: $scope.OrderObj.unitId,
                    channelPartner: $scope.OrderObj.channelPartner,
                    orderSource: $scope.OrderObj.source,
                    reasonId: $scope.selectedReason.id,
                    wastageType: wastageType,
                    noTimeConstraint: $scope.forceCancel === true
                };

                if (AppUtil.isCOD() || AppUtil.isTakeaway()) {
                    requests['orderId'] = $scope.OrderObj.orderId;
                    requests['category'] = $scope.OrderObj.source;
                    requests['unitCategory'] = AppUtil.unitFamily;
                    requests['orderStatus'] = "CANCELLED_REQUESTED";
                    requests['unitId'] = $scope.OrderObj.unitId;
                    if ($scope.refund != undefined) {
                        requests['refund'] = $scope.refund;
                    }
                }

                var reqObj = AppUtil.GetRequest(requests);
                //console.log("cancellation object");
                //console.log(reqObj);

                posAPI.allUrl('/', AppUtil.restUrls.order.cancelOrder).post(reqObj)
                    .then(function (response) {
                        if (response == undefined) {
                            $rootScope.showFullScreenLoader = false;
                            AppUtil.myAlert('There was an error while cancelling the order');
                            return;
                        }
                        if (response.errorType != undefined) {
                            AppUtil.myAlert(response.errorMessage);
                        } else {
                            if (response) {
                                //console.log("cancellation response :::", response);
                                AppUtil.myAlert((AppUtil.isCOD() || AppUtil.isTakeaway()) ? "Cancel Requested for Order Successfully" : "Order Successfully Cancelled");
                                //$location.url(AppUtil.isCOD() ? '/CODCover' : '/cover');
                                cancelChannelPartnerOrder();
                            } else {
                                //console.log(response);
                                AppUtil.myAlert("Order can not be cancelled");
                            }
                        }
                        $rootScope.showFullScreenLoader = false;
                    }, function (err) {
                        $rootScope.showFullScreenLoader = false;
                        //////console.log(err);
                        AppUtil.myAlert(err.data.errorMessage);
                    });
        }

        function checkUserForCancellation(){
            var data ={
                "empId" : $scope.userId,
                "passcode" : $scope.pwd
            }
            posAPI.allUrl('/', AppUtil.restUrls.users.verifyUserForCancellation)
                .post(data).then(function (response) {
                if (response != undefined && response.errorType == undefined) {
                    if (response) {
                        $scope.userVerifiedForCancelation = response;
                    } else {
                        AppUtil.myAlert("Order can not be cancelled");
                    }
                }
            }, function (err) {
                console.log('Error in getting response', err);
            });
        }

        function cancelChannelPartnerOrder() {
            if($scope.OrderObj.channelPartner == 6){
                posAPI.allUrl('/', AppUtil.restUrls.partnerOrder.markCancelled + "?kettleOrderId=" + $scope.OrderObj.orderId)
                    .post().then(function (response) {
                    if (response != undefined && response.errorType == undefined) {
                        if (response) {
                            AppUtil.myAlert((AppUtil.isCOD() || AppUtil.isTakeaway()) ? "Cancel Requested for Order Successfully" : "Order Successfully Cancelled");
                            $location.url(AppUtil.isCOD() ? '/CODCover' : '/cover');
                        } else {
                            AppUtil.myAlert("Order can not be cancelled");
                        }
                    }
                }, function (err) {
                    console.log('Error in getting response', err);
                });
            } else {
                $location.url(AppUtil.isCOD() ? '/CODCover' : '/cover');
            }
        }

        function Logout(userObj) {

            posAPI.allUrl('/',AppUtil.restUrls.users.logout).post(userObj)
                .then (function (response) {
                    //////console.log('logged out');

                }, function (err) {
                    AppUtil.myAlert(err.data.errorMessage);

                });
        }

        //Passcode Reset

        $scope.PasscodeReset = function () {
            //////console.log($scope.usercode,$scope.oldPassword);
            if($scope.oldPassword == $scope.newPassword){
                AppUtil.myAlert("Your old and current Passcode can not be same. Please enter new Passcode");
                return;
            }
            AuthenticationService.PasscodeReset($scope.usercode,
                $scope.oldPassword, $scope.unitId, $scope.newPassword, function (response) {
                    if(response != null && response == true) {
                        AppUtil.mySuccessAlert("Pass code Change Success!");
                        $modalInstance.dismiss('cancel');
                    } else {
                        AppUtil.myAlert("Please enter correct old password!");
                    }
                }, function (err) {
                    AppUtil.myAlert(err.data.errorMessage);
                });
        };

        $scope.cancelReset = function () {
            $modalInstance.dismiss('cancel');
        };

        //remark modal

        $scope.submitRemark = function (orderRemark) {
            $scope.orderRemark = orderRemark;
            ////console.log("orderRemark from modal instance ::: "+$scope.orderRemark);
            $modalInstance.close($scope.orderRemark);
        };

        $scope.cancelRemark = function () {
            $modalInstance.dismiss('cancel');
        };

        //discount Modal
        function resetDiscount() {
            $scope.transactionObj.discountDetail.discount.value = 0;
            $scope.transactionObj.discountDetail.discount.percentage = 0;
            $scope.calculatePaidAmount();
        }

        $scope.checkForOrConditionsDiscountValue = function () {
            $scope.discountPercentage = 0;
            $scope.transactionObj.discountDetail.discount.wasValueSet = true;
            resetDiscount();
        };

        $scope.checkForOrConditionsDiscountPercentage = function () {
            $scope.discountValue = 0;
            $scope.transactionObj.discountDetail.discount.wasValueSet = false;
            resetDiscount();
        };

        $scope.submit = function () {
            //////console.log($scope);
            var totalAmount = $scope.transactionObj.totalAmount - $scope.promotionalOffer;

            if ($scope.transactionObj.discountDetail.discount.wasValueSet) {
                var taxRate = (($scope.transactionObj.paidAmount) / totalAmount);
                var effectiveDiscount = (($scope.discountValue) / taxRate);
                $scope.transactionObj.discountDetail.discount.value = effectiveDiscount;
                $scope.transactionObj.discountDetail.discount.percentage = (effectiveDiscount * 100) / totalAmount;
            } else {
                $scope.transactionObj.discountDetail.discount.value = totalAmount * ($scope.discountPercentage / 100);
                $scope.transactionObj.discountDetail.discount.percentage = $scope.discountPercentage;
            }

            if ($scope.transactionObj.discountDetail.discount.value < totalAmount) {
                //console.log($scope.transactionObj.discountDetail);
                $scope.updateTotalDiscount(true);
                closeModal();
            } else {
                $scope.transactionObj.discountDetail.discount.value = 0;
                $scope.transactionObj.discountDetail.discount.percentage = 0;
                AppUtil.myAlert('Invalid discount entered');
            }

        };

        function closeModal() {
            AppUtil.discountObj.value = $scope.transactionObj.discountDetail.discount.value;
            AppUtil.discountObj.percentage = $scope.transactionObj.discountDetail.discount.percentage;
            AppUtil.discountObj.discountReason = $scope.transactionObj.discountDetail.discountReason;
            AppUtil.discountObj.discountCode = $scope.transactionObj.discountDetail.discountCode;
            AppUtil.discountValue = $scope.discountValue;
            //console.log(AppUtil.discountValue);
            $modalInstance.close();
        }

        $scope.cancelDiscount = function () {

            $scope.transactionObj.discountDetail.discountCode = 0;
            $scope.transactionObj.discountDetail.discount.percentage = 0;
            $scope.transactionObj.discountDetail.discount.value = 0;
            $scope.transactionObj.discountDetail.discountReason = null;
            $scope.transactionObj.discountDetail.discount.wasValueSet = false;
            AppUtil.discountObj.value = 0;
            AppUtil.discountObj.percentage = 0;
            AppUtil.discountObj.discountReason = null;
            AppUtil.discountObj.discountCode = 0;
            AppUtil.discountValue = 0;
            $scope.updateTotalDiscount(false);
            $modalInstance.dismiss();

        };

        $scope.dismissModal = function () {
            $modalInstance.dismiss();
        }

        function getExcludeCustomersList() {
            $http({
                method: 'GET',
                url: AppUtil.restUrls.forms.getExcludeCustomers
            }).then(function success(response) {
                if(response != null) {
                     $scope.excludeCustomersList = response.data;
                }
                setAmDamDetails();
            }, function error(response) {
                    setAmDamDetails();
                    console.log("Error: ", response.data);
                   AppUtil.myAlert(response.data.errorMessage);
            })
        }

        $scope.otpVerificationForCustomer = function() {
                 return $http({
                        method: 'GET',
                        url: AppUtil.restUrls.dineInResources.getCustomerInfo,
                        params: {customerId : $scope.OrderObj.customerId}
                    }).then(function success(response) {
                        if(response != null) {
                            $scope.customerSelected = response.data;
                            $scope.contactNumber = $scope.customerSelected.contactNumber;
                            return $scope.customerSelected;
                        } else {
                            AppUtil.myAlert("Unable to find customer contact number");
                            return null;
                        }
                    }, function error(response) {
                           AppUtil.myAlert(response.data.errorMessage);
                           return null;
                    })
                }

        function setAmDamDetails(){
                            $scope.unitDetails = AppUtil.getUnitDetails();
                             if($scope.excludeCustomersList.includes($scope.OrderObj.customerId)){
                               if($scope.unitDetails.unitManager !=undefined && $scope.unitDetails.unitManager !=null){
                                $scope.unitAM = $scope.unitDetails.unitManager;
                                console.log("unitAM:", $scope.unitAM);
                                $scope.customerOrAm="AM";
                                $scope.employeeName =   $scope.unitAM.name!=undefined?$scope.unitAM.name:"";
                                $scope.employeeNumber =   $scope.unitAM.primaryContact!=undefined?$scope.unitAM.primaryContact:null;

                                if($scope.employeeNumber.length <9){
                                    $scope.employeeName = "";
                                    $scope.employeeNumber = null;
                                }
                              }
                            }
                            else{
                                $scope.otpVerificationForCustomer()
                                .then(function(customer){
                                    if(customer){
                                        $scope.customerOrAm="Customer";
                                        $scope.employeeName =   $scope.OrderObj.customerName!=undefined?$scope.OrderObj.customerName:"";
                                        $scope.employeeNumber =  customer.contactNumber!=undefined?customer.contactNumber:null;
                                        if($scope.employeeNumber.length <9){
                                            $scope.employeeName = "";
                                            $scope.employeeNumber = null;
                                        }
                                    }
                                }).catch(function(error) {
                                      console.error("Error fetching customer details:", error);
                                  });
                           }
        }

        $scope.requestOTP = function() {
                            $scope.errorMessage = null;
                            var contact = $scope.employeeNumber && $scope.employeeNumber.length > 20
                                              ? $scope.employeeNumber.slice(0, 20) + "..."
                                              : $scope.employeeNumber || null;
                            var name= $scope.employeeName!=null?$scope.employeeName:null;
                            $scope.otpError = null;
                            posAPI.allUrl('/',AppUtil.restUrls.customer.generateCancelOrderOTP).post({
                                contactNumber : contact,
                                name : name,
                                generatedOrderId : $scope.OrderObj.generateOrderId,
                                brandId: $scope.OrderObj.brandId
                            }).then(function(response) {
                                if (response) {
                                    $scope.otpRequested = true;
                                } else {
                                    $scope.showErrorMessage("Could not send OTP. Try again.");
                                }
                            }, function(err) {
                                $rootScope.showFullScreenLoader = false;
                                AppUtil.myAlert(err.data.errorMessage);
                            });
                        };

                        $scope.showErrorMessage = function(text) {
                            $scope.errorMessage = text;
                            $timeout(function() {
                                $scope.errorMessage = null;
                            }, 4000)
                        }

                        $scope.verifyOTP = function(otp) {
                            if (!otp || otp.trim() === "") {
                                $scope.showErrorMessage("OTP cannot be empty!");
                                return;
                            }
                            posAPI.allUrl('/',AppUtil.restUrls.customer.verifyOTP).post({
                                contactNumber : $scope.employeeNumber!=null?$scope.employeeNumber:null,
                                otpPin : otp,
                                unit : AppUtil.getUnitDetails().id
                            }).then(function(response) {
                                if (response == true) {
                                    $scope.otpVerified = true;
                                } else {
                                    $scope.showErrorMessage("Incorrect OTP. Please Try again.");
                                }
                            }, function(err) {
                                $rootScope.showFullScreenLoader = false;
                                AppUtil.myAlert(err.data.errorMessage);
                            });
                        };

    }]);
