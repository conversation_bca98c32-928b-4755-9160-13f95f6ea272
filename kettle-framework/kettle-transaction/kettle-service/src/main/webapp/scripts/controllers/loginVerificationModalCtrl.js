/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

angular.module('posApp').controller(
	'LoginVerificationModalCtrl',
	['$scope', '$modalInstance', 'posAPI', 'AppUtil', 'AuthenticationService', '$location', '$rootScope', 'requestData',
	function($scope, $modalInstance, posAPI, AppUtil, AuthenticationService, $location, $rootScope, requestData) {
	    //console.log('Request Data', requestData);
	    $scope.dataLoading = false;
	    $scope.usercode = requestData != undefined && requestData != null ? requestData.id : null;
	    $scope.mandatoryComment = requestData != undefined && requestData != null ? requestData.mandatoryComment
		    : true;
	    $scope.pwd = null;
	    $scope.showComment = requestData != undefined && requestData != null ? requestData.showComment : true;
	    $scope.commentText = null;
	    $scope.presetUserCode = requestData != undefined && requestData != null && requestData.id != undefined
		    && requestData.id != null;

	    function authenticate() {
			$scope.dataLoading = true;
			var currentUserCode = $rootScope.globals.currentUser.userId;
			var currentUnitId = $rootScope.globals.currentUser.unitId;
			var currentTerminalId = $rootScope.globals.currentUser.terminalId;
			var currentScreenType = $rootScope.globals.currentUser.screenType;
			var currentDesignation = $rootScope.globals.currentUser.designation.name;
			AuthenticationService.Verify($scope.usercode, $scope.pwd, currentUnitId, currentTerminalId,
			currentScreenType, function(response) {
				$scope.dataLoading = false;
				if (response  == undefined || response == null || response.errorType != null || response.errorType == 'UNAUTHORIZED') {
					AppUtil.myAlert('Incorrect Credentials. Session Cannot be verified');
					dismiss(null, false);
				} else {
					dismiss(response.user, true);
				}

			}, function(err) {
				$scope.dataLoading = false;
				dismiss(null, false);
			});
	    }

	    function cancel() {
		dismiss(false, $scope.commentText);
	    }

	    function dismiss(user, validated) {
		if (validated && $scope.mandatoryComment && ($scope.commentText == null || $scope.commentText == '')) {
		    AppUtil.myAlert('Comment is mandatory');
		    return;
		}
		if(user == null){
		    user = {
			   id: requestData.id,
		    };
		}
		var data = {
		    user : user,
		    validated : validated,
		    comment : $scope.commentText
		};
		$modalInstance.close(data);
	    }

	    $scope.authenticate = authenticate;
	    $scope.cancel = cancel;

	}]);
