/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

angular.module('posApp').controller('AssemblyController', AssemblyController);

AssemblyController.$inject = ['$rootScope', '$scope', '$location', 'AuthenticationService',
    'PrintService', '$modal', 'AppUtil', 'posAPI', '$interval',
    'AssemblyService', '$cookieStore', 'coverUtils', 'trackingService'];

function AssemblyController($rootScope, $scope, $location, AuthenticationService,
                            PrintService, $modal, AppUtil, posAPI, $interval, AssemblyService, $cookieStore, coverUtils, trackingService) {

    $scope.currentUser = $cookieStore.get('globals').currentUser;
    $scope.unitDetails = AppUtil.getUnitDetails();
    $scope.orders = AssemblyService.orders;
    $scope.orderStates = AssemblyService.orderStates;
    $scope.isManager = $scope.currentUser.designation.name == "Manager" ? true : false;
    $scope.isTokenEnabled = AppUtil.getUnitDetails().tokenEnabled;
    $scope.logout = function () {
        $scope.orders = AssemblyService.orders = [];
        $scope.clearSockets();
        AuthenticationService.Logout(function (response) {
            AuthenticationService.ClearCredentials();
            $location.url('/login');
        });
    };

    $scope.currentOrderDetails = {};
    $scope.isRiderInfoProvided = true;
    $scope.maxSwiggyTemp = AppUtil.MAX_BODY_TEMP_SWIGGY;
    $scope.maxZomatoTemp = AppUtil.MAX_BODY_TEMP_ZOMATO;
    $scope.showTemp = AppUtil.PARTNER_TEMP_FLAG;
    $scope.showMask = AppUtil.PARTNER_MASK_FLAG;

    $scope.getOrders = function () {
        coverUtils.goToDeliveryOrderSummary();
    };

    $scope.testPrinter = function () {
        AssemblyService.testPrinter();
    };

    $scope.refresh = function (click) {
        click = click == undefined ? false : click;
        AssemblyService.refresh(click);
    };

    $scope.resetConfig = function () {
        AppUtil.removeAutoConfigData();
        $scope.logout();
    };

    $scope.refreshUnitOrderCache = function (click) {
        click = click == undefined ? false : click;
        if($scope.currentUser != null && $scope.currentUser.unitId != null) {
            AssemblyService.refreshUnitOrderCache(click, $scope.currentUser.unitId);
        }
    };

    $scope.populateSummary = function (orderId) {
        var modalInstance = $modal.open({
            animation: true,
            templateUrl: window.version + 'views/orderSummaryAssembly.html',
            controller: 'modalInstance',
            backdrop: 'static',
            resolve: {
                order: function () {
                    return AssemblyService.getOrderforModal(orderId);
                }
            }
        });
    };

    $scope.assignRider = function (order) {
        var modalInstance = $modal.open({
            animation: true,
            templateUrl: window.version + 'views/assignRiderModal.html',
            controller: 'assignRiderController',
            backdrop: 'static',
            keyboard: false,
            scope: $scope,
            size: "lg",
            resolve: {
                order: function () {
                    return order;
                }
            }
        });
    };

    $scope.renderAllOrders = function (response) {
        //console.log("renderAll in progress");
        if (response != undefined) {
            AssemblyService.renderAllOrders(response);
            $scope.orders = AssemblyService.orders;
        }
    };

    function openAddressConfirmationModal(orderId, localScope) {
        var orderInfo = AssemblyService.getOrderforModal(orderId);
        var modalInstance = $modal.open({
            animation: true,
            templateUrl: window.version + 'scripts/modules/modals/addressConfirmationModal.html',
            controller: 'addressConfirmationController',
            backdrop: 'static',
            keyboard: false,
            scope: $scope,
            size: "lg",
            resolve: {
                orderInfo: function () {
                    return orderInfo;
                }
            }
        });

        modalInstance.result.then(function (selectedItem) {
            //console.log('Modal closed at: ' + selectedItem);
            if (selectedItem == 'accept') {
                try {
                    if (AssemblyService.printOrder(orderInfo.order.orderId, false)) {
                        $scope.sendUpdate(localScope, orderInfo.order.orderId, 1, orderInfo.order.unitId, orderInfo.order.source);

                    }
                } catch (err) {
                    console.log("err", err);
                    alert("Not able to print order" + JSON.stringify(err));
                }
            }
        }, function () {
            console.log('Modal dismissed at: ' + selectedItem);
        });
    };

    $scope.sendStatus = function (localScope, orderId, status, unitId, source, indexOrder) {

        if (($scope.showMask || $scope.showTemp) && ((status == 4 && indexOrder.channelPartner.code == 'ZOMATO'
            && AppUtil.zomatoFlag && $scope.isRiderInfoProvided) || (status == 4 && indexOrder.channelPartner.code == 'SWIGGY'
            && AppUtil.swiggyFlag && $scope.isRiderInfoProvided))) {
            if ((status == 4 && indexOrder.order.deliveryPartner != 8)) {
                $('#RiderDataViewModal').modal('show');
                $scope.currentOrderDetails = {
                    localScope: localScope,
                    orderId: orderId,
                    status: status,
                    unitId: unitId,
                    source: source,
                    indexOrder: indexOrder,
                    partner: indexOrder.channelPartner.code
                }
            }
        } else {
            if (status == 1) {
                var orderInfo = AssemblyService.getOrderforModal(orderId);
                if (orderInfo.customer.addresses[0] == undefined || source != 'COD') {
                    try {
                        if (AssemblyService.printOrder(orderId, false)) {
                            $scope.sendUpdate(localScope, orderId, 1, unitId, source);
                        }
                    } catch (err) {
                        console.log("err", err);
                        alert("Not able to print order" + JSON.stringify(err));
                    }
                } else {
                    openAddressConfirmationModal(orderId, localScope);
                }
            } else if (status == 4) {
                var orderInfo = AssemblyService.getOrderforModal(orderId);
                if (source == 'COD' && orderInfo.order.settlements[0].modeDetail.name == 'Cash') {
                    $scope.checkForCashCollection(localScope, orderId, status, unitId, source, indexOrder);
                } else {
                    $scope.sendUpdate(localScope, orderId, status, unitId, source, indexOrder);
                }
            } else {
                $scope.sendUpdate(localScope, orderId, status, unitId, source, indexOrder);
            }
        }
    };

    $scope.onSubmittingRiderDetails = function (mask, temp) {
        if (temp == true && $scope.bodyTemp == null) {
            bootbox.alert("Please provide temperature");
            return;
        }
        if ($scope.currentOrderDetails.partner == 'SWIGGY') {
            if (temp == true && $scope.bodyTemp != null && $scope.bodyTemp <= AppUtil.MAX_BODY_TEMP_SWIGGY) {
                bootbox.alert("Please provide correct value");
                return;
            }
        }

        if ($scope.currentOrderDetails.partner == 'ZOMATO') {
            if (temp == true && $scope.bodyTemp != null && $scope.bodyTemp <= AppUtil.MAX_BODY_TEMP_ZOMATO) {
                bootbox.alert("Please provide correct value");
                return;
            }
        }
        $('#RiderDataViewModal').modal('hide');
        $scope.riderTemp = null;
        $scope.wearingMask = null;
        var is_high_temp = temp == true ? true : false
        var riderMask = mask == true ? "Yes" : "No"
        var maskCheck = mask == true ? true : false
        if (is_high_temp == false) {
            $scope.bodyTemp = 0;
        }
        console.log($scope.currentOrderDetails.indexOrder)
        if ($scope.currentOrderDetails.partner == 'ZOMATO') {
            var payload = {
                "is_high_temp": is_high_temp,
                "is_rider_wearing_mask": riderMask,
                "maskCheck": maskCheck,
                "order_id": $scope.currentOrderDetails.indexOrder.order.sourceId,
                "partner_id": AppUtil.ZOMATO_PARTNER_ID,
                "rbt": $scope.bodyTemp,
                "rbtCheck": true
            }
        }
        if ($scope.currentOrderDetails.partner == 'SWIGGY') {
            var payload = {
                "is_high_temp": is_high_temp,
                "is_rider_wearing_mask": riderMask,
                "maskCheck": maskCheck,
                "order_id": $scope.currentOrderDetails.indexOrder.order.sourceId,
                "partner_id": AppUtil.SWIGGY_PARTNER_ID,
                "rbt": $scope.bodyTemp,
                "rbtCheck": true
            }
        }
        $rootScope.showFullScreenLoader = true;
        posAPI.allUrl('/', AppUtil.restUrls.order.riderHealthCheck).post(payload).then(function (response) {
            if (response == true) {
                $scope.isRiderInfoProvided = false;
                $scope.bodyTemp = null;
                $scope.sendStatus($scope.currentOrderDetails.localScope, $scope.currentOrderDetails.orderId, $scope.currentOrderDetails.status, $scope.currentOrderDetails.unitId, $scope.currentOrderDetails.source, $scope.currentOrderDetails.indexOrder)
                $rootScope.showFullScreenLoader = false;
            } else {
                bootbox.alert("Cannot dispatch order to  rider due to safety concerns");
                $scope.bodyTemp = null;
                $rootScope.showFullScreenLoader = false;
            }
        });
    }


    $scope.checkForCashCollection = function (localScope, orderId, status, unitId, source, indexOrder) {
        $scope.isRiderInfoProvided = true;
        bootbox.confirm({
            title: "Cash Payment Check",
            message: '<h3><i class="fa fa-warning" style="color:orange"></i> This is a Cash Payment order, Please make sure to collect Cash Amount.</h3>',
            buttons: {
                cancel: {
                    label: '<i class="fa fa-times"></i> Cancel'
                },
                confirm: {
                    label: '<i class="fa fa-check"></i> Confirm'
                }
            },
            callback: function (result) {
                if (result) {
                    $scope.sendUpdate(localScope, orderId, status, unitId, source, indexOrder);
                }
            }
        });
    };

    $scope.sendUpdate = function (localScope, orderId, status, unitId, source, indexOrder) {
        $scope.isRiderInfoProvided = true;
        localScope.isDisabled = true;
        //console.log("orderId ::::::: " + orderId);
        var orderStatus = $scope.orderStates[status];
        var requests = {
            orderId: orderId,
            orderStatus: orderStatus,
            unitId: unitId,
            unitCategory: source,
            orderSource: source
        };
        var reqObj = AppUtil.GetRequest(requests);
        //console.log("Request Object");
        //console.log(reqObj);
        $rootScope.showFullScreenLoader = true;
        posAPI.allUrl('/', AppUtil.restUrls.order.updateStatus)
            .post(reqObj)
            .then(
                function (response) {
                    var result = response == 'true' ? false : true;
                    switch (status) {
                        case 1: {
                            localScope.isProcessing = result;
                            AssemblyService.updateOrder(orderId, orderStatus);
                            break;
                        }
                        case 2: {
                            localScope.isRTD = result;
                            AssemblyService.updateOrder(orderId, orderStatus);
                            break;
                        }
                        case 4: {
                            localScope.isDispatch = result;
                            AssemblyService.removeOrder(orderId);
                            break;
                        }
                        case 6: {
                            localScope.isCancelled = result;
                            try {
                                var obj = indexOrder.order;
                                obj.channelPartnerName = AppUtil.getOrderSourceName(obj.channelPartner);
                                trackingService.trackOrderCancel(obj);
                            } catch (e) {
                            }
                            AssemblyService.removeOrder(orderId);
                            break;
                        }
                        case 7: {
                            localScope.isDispatch = result;
                            AssemblyService.removeOrder(orderId);
                            break;
                        }
                        case 8: {
                            localScope.isDispatch = result;
                            break;
                        }
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function (err) {
                    $rootScope.showFullScreenLoader = false;
                    //console.log(err);
                }
            );

    };

    $scope.renderOrders = function (serverMessage) {

        var orderInfo = JSON.parse(serverMessage.body);
        var order = orderInfo.order;
        //console.log("order info is :::::",orderInfo);
        if (!orderInfo.deliveryDetails.byAssemblyScreen && order.deliveryPartner == 8 && order.status == "DELIVERED") {
            AssemblyService.removeOrder(order.orderId);
        } else {
            $scope.$apply(function () {
                AssemblyService.pushOrder(orderInfo);
            });
        }
        AssemblyService.acknowledgeOrder(order.orderId);
        // Remove If checks is Beep Sound is not playing
        if (orderInfo.deliveryDetails == null
            || orderInfo.deliveryDetails.deliveryBoyId == null
            || orderInfo.deliveryDetails.deliveryPartnerId != 8) {
            $scope.playFile();
        }

    };

//code for initializing socket
    $scope.initSocket = function () {
        $scope.socket = new SockJS("/kettle-service/rest/ws");
        $scope.stompClient = Stomp.over($scope.socket);
        var userName = $cookieStore.get('globals').currentUser.userName;
        var unitId = $cookieStore.get('globals').currentUser.unitId;
        //console.log("UserName :: "+userName+" :: unitId ::"+unitId);
        $scope.stompClient.connect(userName, unitId,
            function (frame) {
                //console.log("UnitId ::::::: " + unitId);
                //console.log("user name of the socket :::::: " + frame);
                $scope.stompClient.subscribe('/ws-channel/' + unitId + '/orders',
                    $scope.renderOrders);
            }, function (err) {
                $scope.stompClient.subscribe('/ws-channel/' + unitId + '/orders',
                    $scope.renderOrders);
            }
        );
    };


    $scope.clearSockets = function () {
        if ($scope.socket != undefined) {
            $scope.socket.close();
            if ($scope.stompClient != undefined) {
                $scope.stompClient.disconnect(function () {
                    //console.log('connection closed');
                });
            }
        }
    };

    $scope.playFile = function () {
        if (AppUtil.isAndroid) {
            Android.playSound();
        } else {
            play_interval_sound();
        }
    };

    $scope.stopPlaying = function () {
        stop_sound();
    };

    $scope.getSubscriptionDeliveryTime = function (orderObj) {
        var date = new Date(orderObj.billingServerTime + (45 * 60000));
        var hh = date.getHours();
        var mm = date.getMinutes();
        var ss = "00";
        var xx = "am";
        if (parseInt(hh) >= 12) {
            hh = parseInt(hh) - 12;
            xx = "pm";
        }
        mm = mm - (mm % 15);
        if (mm < 10) {
            mm = "0" + mm;
        }
        if (hh < 10) {
            hh = "0" + hh;
        }
        return hh + ":" + mm + ":" + ss + " " + xx;
    };

    $scope.getAssemblyOrderItems = function (order) {
        var orderItems = [];
        for (var i in order.orders) {
            order.orders[i].isComboItem = false;
            orderItems.push(order.orders[i]);
            if (order.orders[i].composition.menuProducts != null && order.orders[i].composition.menuProducts.length > 0) {
                for (var j in order.orders[i].composition.menuProducts) {
                    order.orders[i].composition.menuProducts[j].isComboItem = true;
                    orderItems.push(order.orders[i].composition.menuProducts[j]);
                }
            }
        }
        return orderItems;
    };

//initiate refresh call
    (function init() {
        $scope.refresh();
        $scope.initSocket();
    })();

    var delayInterval = $interval(AssemblyService.checkForDelayInAction, 60000);
    var refreshInterval = $interval($scope.refresh, 60000);

    $scope.$on('$destroy', function () {
        $interval.cancel(delayInterval);
        $interval.cancel(refreshInterval);
    });

    $scope.resendSMSToRider = function (deliveryDetails) {
        $rootScope.showFullScreenLoader = true;
        posAPI.allUrl('/', AppUtil.restUrls.delivery.resendSMS).post(deliveryDetails).then(function (response) {
            //	console.log("response",response.plain());
            if (response.failureMessage != undefined) {
                bootbox.alert(response.failureMessage);
            } else {
                bootbox.alert("Messsage with order details resent to  " + deliveryDetails.deliveryBoyName + " succsessfully.");
            }
            $rootScope.showFullScreenLoader = false;

        }, function (err) {
            $rootScope.showFullScreenLoader = false;
            bootbox.alert("Failed to resend  message.Please try again after some time.");
        });
    };

    $scope.showDeliveryDetails = function (orderId) {
        var orderInfo = AssemblyService.getOrderforModal(orderId);
        var modalInstance = $modal.open({
            animation: true,
            templateUrl: window.version + 'scripts/modules/modals/addressDetailModal.html',
            controller: 'addressDetailModalCtrl',
            backdrop: 'static',
            keyboard: false,
            scope: $scope,
            size: "lg",
            resolve: {
                orderInfo: function () {
                    return orderInfo;
                }
            }
        });
    }

}

//modal functionality for populating order summary 
angular.module('posApp').controller('modalInstance', ['$scope', '$modalInstance', 'order', 'AssemblyService', 'AppUtil', '$timeout',
    function ($scope, $modalInstance, order, AssemblyService, AppUtil, $timeout) {
        //console.log("inside modal instance");
        var receipt = '<div style="text-align: center;"><pre>' + order.receipts[0] + '</pre></div>';
        $scope.receipt = receipt.replace(/width="\d+cm"/g, '');
        $scope.receipt = AppUtil.clearSpecialChars(receipt);
        $scope.showPrint = order.order.source == 'COD' || AppUtil.isWebDineIn(order.order);
        $scope.buttonEnabled = true
        $scope.ok = function () {
            $modalInstance.dismiss('ok');
        };
        $scope.printOrder = function () {
            $scope.buttonEnabled = false;
            $timeout(function () {
                $scope.buttonEnabled = true;
            }, 3000);
            return AssemblyService.printOrder(order.order.orderId, true);
        };

        $scope.printOrderKOT = function () {
            $scope.buttonEnabled = false;
            $timeout(function () {
                $scope.buttonEnabled = true;
            }, 3000);
            return AssemblyService.printOrderKOT(order.order.orderId, true);
        };
    }]
);


angular.module('posApp').controller('addressConfirmationController',
    ['$scope', '$modalInstance', 'orderInfo', 'AssemblyService', 'AppUtil', '$rootScope', 'posAPI',
        function ($scope, $modalInstance, orderInfo, AssemblyService, AppUtil, $rootScope, posAPI) {
            var address = orderInfo.customer.addresses[0];
            $scope.fullAddress = address.name + " (" + address.contact1 + ")<br>" + address.email + "<br>" + address.addressType + "<br>";
            if (address.company) {
                $scope.fullAddress = $scope.fullAddress + "(" + address.company + ")";
            }
            $scope.fullAddress = $scope.fullAddress + "<br>" + address.line1 + "<br>" + address.line2 + "<br>" + address.locality + "<br>" + address.subLocality + "<br>" + address.city + "<br>" + address.state;
            $scope.acceptOrder = function () {
                close("accept");
            };

            $scope.declineOrder = function () {
                bootbox.confirm("Are you Sure this order is out of delivery area for you?", function (result) {
                    if (result == true) {
                        sendNotificationForOODOrder();
                    }
                });
            };

            function sendNotificationForOODOrder() {
                $rootScope.showFullScreenLoader = true;
                posAPI.allUrl('/', AppUtil.restUrls.order.outOfDeliveryNotification)
                    .post(orderInfo.order.orderId)
                    .then(
                        function (response) {
                            //console.log("response ",response);
                            AssemblyService.markOODOrder(orderInfo.order.orderId, true);
                            close("ood");
                            $rootScope.showFullScreenLoader = false;
                        }, function (err) {
                            close("ood");
                            $rootScope.showFullScreenLoader = false;
                            //console.log(err);
                        }
                    );

            };

            function close(data) {
                $modalInstance.close(data);
            };
        }
    ]
);

angular.module('posApp').controller('addressDetailModalCtrl',
    ['$scope', '$modalInstance', 'orderInfo', function ($scope, $modalInstance, orderInfo) {
        $scope.orderInfo = orderInfo;
        $scope.address = orderInfo.customer.addresses[0];

        $scope.copyToClipboard = function(elemId) {
            var copyText = document.getElementById(elemId).innerText;
            if(navigator.clipboard) {
                navigator.clipboard.writeText(copyText);
            } else {
                var textArea = document.createElement("textarea");
                textArea.value = copyText;
                // Avoid scrolling to bottom
                textArea.style.top = "0";
                textArea.style.left = "0";
                textArea.style.position = "fixed";
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                try {
                    var successful = document.execCommand('copy');
                    var msg = successful ? 'successful' : 'unsuccessful';
                    console.log('Fallback: Copying text command was ' + msg);
                } catch (err) {
                    console.error('Fallback: Oops, unable to copy', err);
                }
                document.body.removeChild(textArea);
            }
        };

        $scope.close = function(data) {
            $modalInstance.close(data);
        };
    }]
);