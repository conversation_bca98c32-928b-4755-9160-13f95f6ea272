/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

angular.module('posApp').controller('partnerOrderDashboardCtrl',
    ['$location', '$scope','$http', 'posAPI', 'AppUtil', '$rootScope','$timeout','$modal',
        function ($location, $scope,$http, posAPI, AppUtil, $rootScope,$timeout,$modal) {

            $scope.backToCODCover = function () {
                if ($scope.isCOD) {
                    $location.url('/CODCover');
                } else {
                    $location.url('/cover');
                }
            };

            $scope.init = function () {
                $scope.createBrandMap();
                $scope.orderList = [];
                $scope.getUnitSwitchOffPendingRequests();
                $scope.UnitOffRequestList = [];
                $scope.isCOD = $rootScope.globals.currentUser.unitFamily === "COD";
                if ($rootScope.partnerOrderRefresh != null) {
                    $timeout.cancel($rootScope.partnerOrderRefresh);
                }
                $scope.getUnitList();
                //$scope.getPendingOrders(true);
                if ($rootScope.partnerOrderSound != null) {
                    $timeout.cancel($rootScope.partnerOrderSound);
                }
                $scope.autoConfigData = AppUtil.getAutoConfigData();
                $scope.playFile();
                $scope.inventoryGrid = $scope.getInventoryGrid();
                $rootScope.productPriceMap={};
                $scope.customerDetailString = [];
                $scope.currentUser = $rootScope.globals.currentUser;

                $scope.selectedOrder = null;
                $scope.showRejectionModal = false;
                $scope.rejectionReasons = [];
                $scope.rejectionObject = {
                    id: null,
                    message: null
                };
                $scope.rejectionItems = [];
            };
            $scope.fallbackProcess = function(order) {
                order.showPhone = !order.showPhone;

                if (order.showPhone) {
                    order.fallbackProcessedBy = $scope.currentUser.userId;
                    // order.fallbackProcessedByName = $scope.currentUser.userName;

                    console.log('Making API call to update pickedBy:', {
                        url: AppUtil.restUrls.partnerOrder.fallbackProcessedBy,
                        data: {
                            orderId: order.orderId,
                            fallbackProcessedBy: order.fallbackProcessedBy
                            // fallback_processed_by_name: order.fallbackProcessedByName
                        }
                    });

                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.partnerOrder.fallbackProcessedBy,
                        params: {
                            orderId: order.orderId,
                            fallbackProcessedBy: order.fallbackProcessedBy
                        },
                        responseType: 'arraybuffer',
                        headers: {
                            'Content-type': 'application/json'
                        }
                    }).then(function(response) {
                        console.log('Order updated successfully:', response);
                    }, function(error) {
                        console.error('Error updating order:', error);
                        AppUtil.myAlert("Failed to update picked by status. Please try again.");
                    });
                }
            };


            $scope.getInventoryGrid =  function(){
                return {
                    enableSorting: true,
                    enableColumnMenus: false,
                    saveFocus: false,
                    enableRowSelection: true,
                    enableFiltering: true,
                    saveScroll: false,
                    enableSelectAll: true,
                    multiSelect: true,
                    enableColumnResizing : true,
                    onRegisterApi: function( gridApi ) {
                        $scope.grid2Api = gridApi;
                    },
                    columnDefs : [
                        {name:"product", field:"productName",enableCellEdit:false},
                        {name:"expireQuantity", field:"expireQuantity", type: 'number', enableCellEdit: false},
                        {name:"quantity", field:"quantity", type: 'number', enableCellEdit: false},
                        {name:"lastUpdatedTime", field:"lastUpdatedTime",  cellFilter: 'date:"d/M/yy h:mm a"', enableCellEdit: false},
                        /*{name:"stockOut", visible : $scope.showEditInventory, enableCellEdit: false, cellTemplate: '<button class="btn btn-danger btn-xs" ng-click="grid.appScope.stokcOutHandler(row.entity)">Stock out</button>'},*/
                        {name:"productId", field:"productId", visible:false, enableCellEdit: false}
                    ],
                    data : []
                };
            }

            $scope.createBrandMap = function() {
                $scope.brandMap = {};
                AppUtil.transactionMetadata.brandList.map(function (brand) {
                    $scope.brandMap[brand.brandId] = brand;
                });
            };

            $scope.getUnitList = function () {
                posAPI.allUrl('/', AppUtil.restUrls.unitMetaData.activeUnits).customGET("", {category:"CAFE"})
                    .then(function (response) {
                    if (response) {
                        $scope.outlets = {};
                        response.plain().map(function (outlet) {
                            $scope.outlets[outlet.id] = outlet;
                            $scope.outlets[outlet.id+"GNT"] = outlet;
                            $scope.outlets[outlet.id+"DC"] = outlet;
                            $scope.outlets[outlet.id+"SC"] = outlet;
                        });
                    }
                    $rootScope.showFullScreenLoader = false;
                    $scope.getPendingOrders(true);
                }, function (err) {
                    console.log('Error in getting response', err);
                    $rootScope.showFullScreenLoader = false;
                });
            };


            $scope.refreshPendingRequests = function (clicked) {
                if (clicked != undefined && clicked) {
                    $scope.init();
                }
            };


            $scope.getUnitSwitchOffPendingRequests =function (){
                $scope.requestMap ={};

                posAPI.allUrl('/', AppUtil.restUrls.cafeLookUp.getUnitSwitchOffRequestsFromKnock).customGET()
                    .then(function (response) {
                        if (response) {
                            response.unitOffRequest.forEach(function (data){
                                var obj = {};
                                obj['id'] = data.id;
                                obj['unitId'] = data.unitId;
                                obj['cafeName'] = data.cafeName;
                                obj['channelPartner'] = data.partnerName;
                                obj['brandId'] = data.brandId;
                                obj['reason'] = data.reason;
                                obj['actionTaken'] = data.actionTaken;
                                obj['startTime'] = data.startTime;
                                obj['endTime'] = data.endTime;
                                obj['businessDate'] = data.businessDate;
                                obj['time'] = data.actionTimestamp;
                                obj['actionTimestamp'] = moment(data.actionTimestamp).format("DD-MM-YYYY HH:mm:ss");
                                obj['requestedByEmpId'] = data.requestedByEmpId;
                                obj['acceptedByEmpId'] = data.acceptedByEmpId;
                                obj['channelPartnerId'] = data.channelPartnerId;
                                obj['requestType'] = data.requestType;
                                
                                if (Date.now()-data.actionTimestamp > 1800000){
                                    obj['color'] = "red";
                                }
                                else{
                                    obj['color'] = "black";
                                }
                                $scope.UnitOffRequestList.push(obj);
                            });
                        }
                    }, function (err) {
                        console.log('Error in getting response', err);
                        $rootScope.showFullScreenLoader = false;
                    });
            }

            $scope.forceCafeShutdownRequestRemoval = function (request){
                var deleteObject={};
                if(request.id !=undefined && request.id !=null){
                    deleteObject['id'] = request.id;
                    posAPI.allUrl('/', AppUtil.restUrls.cafeLookUp.updateUnitRequestFromUnitOffDashboard)
                        .post(deleteObject).then(function (response) {
                        $scope.refreshUnitOffRequests(true);
                    },function (err){
                        console.log('Error in force request removal', err);
                    });
                }
            }

            $scope.deactivateUnitUrl=function (request){
                var UnitOnObject = {};
                UnitOnObject['unitId'] = request.unitId;
                UnitOnObject['partnerName'] = request.channelPartner;
                UnitOnObject['brandId'] = request.brandId;
                $rootScope.showFullScreenLoader = true;

                console.log("Entered Deactivate Url");
                console.log(request);
                var url = AppUtil.restUrls.partnerMetadataManagement.unitTurnOn;
                if (request.channelPartnerId === 14) {
                    url = AppUtil.restUrls.partnerMetadataManagement.neoUnitTurnOn;
                } else {
                    url = AppUtil.restUrls.partnerMetadataManagement.unitTurnOn;
                }

                posAPI.allUrl('/', url)
                    .post(UnitOnObject).then(function (response) {
                    if (response != null && response === true) {
                        AppUtil.mySuccessAlert("Unit " + request.unitId +" Switched On Successfully!");

                        var deleteObject={};
                        deleteObject['id'] = request.id;
                        posAPI.allUrl('/', AppUtil.restUrls.cafeLookUp.updateUnitRequestFromUnitOffDashboard)
                            .post(deleteObject).then(function (response) {
                            $scope.refreshUnitOffRequests(true);
                        },function (err){
                            console.log('Error in Saving Response', err);
                        });

                    }

                    if(typeof response == 'undefined'){
                        AppUtil.myAlert("Error in Switching On  Unit "+request.unitId);
                    }

                    $rootScope.showFullScreenLoader = false;
                }, function (err) {
                    AppUtil.myWarningAlert("Error in Activating Unit"+cafe.unitId);
                    console.log('Error in getting response', err);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.refreshUnitOffRequests = function (clicked) {
                if (clicked !== undefined && clicked) {
                    $scope.init();
                }
            };

            $scope.downloadUnitClosureSheet = function (noOfDays){
                if(!noOfDays || noOfDays<0 || noOfDays>7){
                    alert("Please enter valid number of days");
                    return;
                }
                $rootScope.showDetailLoader = true;
                $http(
                    {
                        method: 'GET',
                        url:  AppUtil.restUrls.reportMetadata.getUnitClosureReports,
                        params: {
                            noOfDays:noOfDays
                        },
                        responseType: 'arraybuffer',
                        headers: {
                            'Content-type': 'application/json'
                        }
                    })
                    .then(
                        function success(response) {
                            if (response !== undefined && response.status===200) {
                                var fileName ="Unit_Closure_Report_"
                                    + Date.now() + ".xlsx";
                                var blob = new Blob(
                                    [response.data],
                                    {
                                        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                                    }, fileName);
                                saveAs(blob, fileName);
                            }else {
                                bootbox.alert("Error while downloading report");
                            }
                            $rootScope.showDetailLoader = false;
                            $scope.budgetDate = null;
                        },
                        function error(response) {
                            console.log("error:" + response);
                            alert("Unable to download unit closure report");
                            $rootScope.showDetailLoader = false;
                        });
            };

            $scope.getPendingOrders = function (clicked) {
                var request = {
                    hours: 2000,
                    partnerOrderDetails: {},
                     unitId:null
                };
                if (!$scope.isCOD) {
                    request.unitId = $scope.autoConfigData.unitId;
                }
                if (clicked != undefined && clicked) {
                    $rootScope.showFullScreenLoader = true;
                } else {
                    $scope.orderList.map(function (order) {
                        request.partnerOrderDetails[order.orderId] = order.partnerOrderStatus;
                    });
                }
                posAPI.allUrl('/', AppUtil.restUrls.partnerOrder.getPendingOrders)
                    .post(request).then(function (response) {
                    if (response) {
                        var pickedByStates = {};
                        $scope.orderList.forEach(function(order) {
                            if (order.pickedBy) {
                                pickedByStates[order.orderId] = order.fallbackProcessedBy;
                            }
                        });

                        if (clicked != undefined && clicked) {
                            $scope.orderList = response.plain().NEW;
                        } else {
                            if (response.plain()) {
                                response.plain().CHANGED.map(function (item) {
                                    $scope.orderList.map(function (order) {
                                        if (order.orderId == item.orderId) {
                                            order = item;
                                        }
                                    })
                                });
                                $scope.orderList = $scope.orderList.concat(response.plain().NEW);
                                var list = [];
                                $scope.orderList.map(function (order) {
                                    var found = false;
                                    response.plain().FINISHED.map(function (item) {
                                        if (order.orderId == item.orderId) {
                                            found = true;
                                        }
                                    });
                                    if (!found) {
                                        list.push(order);
                                    }
                                });
                                $scope.orderList = list;
                            }
                        }
                        $scope.orderList.map(function (order) {
                            if(order.partnerName == "MAGICPIN"){
                                order.unitName = $scope.outlets[order.partnerOrder.merchantData.client_id];
                            }else{
                                order.unitName = $scope.outlets[order.partnerOrder.outlet_id];
                            }
                            order.brand = $scope.brandMap[order.brandId];
                            if (!order.fallbackProcessedBy && order.fallbackProcessedBy) {
//                                order.fallbackProcessedBy = order.fallback_processed_by_name;
                            }
                            else if (pickedByStates[order.orderId]) {
                                order.fallbackProcessedByName = pickedByStates[order.orderId];
                            }
                        });
                    }
                    $rootScope.showFullScreenLoader = false;
                    $scope.refreshOrders();
                }, function (err) {
                    console.log('Error in getting response', err);
                    $rootScope.showFullScreenLoader = false;
                    $scope.refreshOrders();
                });
            };

            $scope.toggleItems = function(order){
               if(order.showItems == null){
                  order.showItems = true;
                  if(order.partnerName == "MAGICPIN"){
                      $scope.setMagicPinOrderItems(order);
                  }
               }else{
                  order.showItems = !order.showItems;
               }

            }

            $scope.refreshOrders = function () {
                $rootScope.partnerOrderRefresh = $timeout(function () {
                    if ($location.url() == "/partnerOrderDashboard") {
                        $scope.getPendingOrders(false);
                    }
                }, 40 * 1000);
            };

            $scope.playFile = function () {
                $rootScope.partnerOrderSound = $timeout(function () {
                    if ($location.url() == "/partnerOrderDashboard") {
                        if ($scope.orderList != null && $scope.orderList.length > 0) {
                            if (AppUtil.isAndroid) {
                                Android.playSound();
                            } else {
                                play_interval_sound();
                            }
                        }
                    }
                    $scope.playFile();
                }, 60000);
            };

            $scope.showDetail = function (order) {
                order.showDetail ? order.showDetail = false : order.showDetail = true;
            };

            $scope.callSwiggySupport = function (order) {
                $rootScope.showFullScreenLoader = true;
                posAPI.allUrl('/', AppUtil.restUrls.partnerOrder.callSwiggySupport + "?orderId=" + order.orderId + "&fallbackProcessedBy=" + $scope.currentUser.userId)
                    .post().then(function (response) {
                    if (response != null && response == true) {
                            AppUtil.mySuccessAlert("Swiggy support requested successfully!");
                    }
                    $rootScope.showFullScreenLoader = false;
                    console.log('Swiggy support requested successfully:', order);
                }, function (err) {
                    console.log('Error in getting response', err);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.connectCustomer = function(order){
            var reqParam={};
            reqParam.orderId = order.partnerOrderId;
            reqParam.unitId = order.unitId;
            reqParam.brandId = order.brandId;
            if(order.partnerName == "SWIGGY"){
            reqParam.partnerId = 6;
            }
            $rootScope.showFullScreenLoader = true;
            posAPI.allUrl('/',AppUtil.restUrls.swiggy.connectCustomer)
            .post(reqParam).then(function(response){
             if(response!=null){
               $scope.customerConnect = response.data;
               bootbox.alert("Customer phone Number:"+$scope.customerConnect.number+" \n Customer Pin:"+ $scope.customerConnect.pin);
             }
              $rootScope.showFullScreenLoader = false;
             }, function (err) {
                 console.log('Error in getting response', err);
                 $rootScope.showFullScreenLoader = false;
              });
            };

            $scope.markResolved = function (order, index) {
                $rootScope.showFullScreenLoader = true;
                posAPI.allUrl('/', AppUtil.restUrls.partnerOrder.markResolved + "?orderId=" + order.orderId)
                    .post().then(function (response) {
                    if (response != null && response == true) {
                        var o = $scope.orderList.splice(index, 1);
                        AppUtil.mySuccessAlert("Order id " + o[0].partnerOrderId + " resolved successfully!");
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function (err) {
                    console.log('Error in getting response', err);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.manualProcess = function (order, index) {
                $rootScope.showFullScreenLoader = true;
                posAPI.allUrl('/', AppUtil.restUrls.partnerOrder.manualProcess + "?orderId=" + order.orderId)
                    .post().then(function (response) {
                    if (response != null && response == true) {
                        var o = $scope.orderList.splice(index, 1);
                        AppUtil.mySuccessAlert("Order id " + o[0].partnerOrderId + " processed successfully!");
                    }
                    $rootScope.showFullScreenLoader = false;
                    console.log('Manual Order processed successfully:', order);
                }, function (err) {
                    console.log('Error in getting response', err);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.setMagicPinOrderItems = function (order) {
                $rootScope.showFullScreenLoader = true;
                posAPI.allUrl('/', AppUtil.restUrls.partnerOrder.getMagicpinOrderItems + "?orderId=" + order.partnerOrderId)
                    .post().then(function (response) {
                    if (response != null) {
                        order.items = response;
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function (err) {
                    console.log('Error in getting response', err);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.isManualProcess = function (order) {
                var status = order.partnerOrderStatus;
                if (status == "RECEIVED" || status == "CHECKED") {
                    return order.toBeProcessed;
                } else {
                    return status == "PLACED" || status == "FAILED";
                }
            };

            $scope.isForceProcess = function (order) {
                if (order.partnerOrderStatus == "CHECKED") {
                    var count = 0;
                    var stockIssue = false;
                    order.orderErrors.map(function (error) {
                        if (error.errorCode == "STOCK_NOT_SUFFICIENT" || error.errorCode == "STOCK_NOT_AVAILABLE" || error.errorCode == "STOCK_NOT_FOUND") {
                            if (!stockIssue) {
                                stockIssue = true;
                            }
                        } else {
                            if (error.errorCode != "PRICE_MISMATCH" && error.errorCode != "TAX_MISMATCH" && error.errorCode != "TRANSACTION_MISMATCH") {
                                count++;
                            }
                        }
                    });
                    return stockIssue && count == 0;
                } else {
                    return false;
                }
            };

            $scope.canBeMarkedResolved = function (order) {
                var status = order.partnerOrderStatus;
                return order.partnerOrderStatus == "CHECKED" || status == "PLACED" || status == "FAILED";
            };

            $scope.forceProcess = function (order, index) {
                $rootScope.showFullScreenLoader = true;
                var result = confirm("Are you sure you want to place this order?");
                if(!result){
                    $rootScope.showFullScreenLoader = false;
                    return;
                }
                posAPI.allUrl('/', AppUtil.restUrls.partnerOrder.forceProcess + "?orderId=" + order.orderId + "&employeeId=" + $scope.currentUser.userId)
                    .post().then(function (response) {
                    if (response != null && response == true) {
                        var o = $scope.orderList.splice(index, 1);
                        AppUtil.mySuccessAlert("Order id " + o[0].partnerOrderId + " force processed successfully!");
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function (err) {
                    console.log('Error in getting response', err);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            $scope.sendStockOutNotification = function(order) {
                if (order) {
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.partnerOrder.sendStockOutNotification,
                        data: JSON.stringify(order.orderId),
                        responseType: 'arraybuffer',
                        headers: {
                            'Content-type': 'application/json'
                        }
                    }).then(function(response) {
                        console.log('Order processed successfully:', response);
                        if (response.status === 500) {
                            AppUtil.myAlert("Contact customer or support first.");
                        }
                        // AppUtil.myAlert("Swiggy link triggered successfully.");
                    }, function(error) {
                        AppUtil.myAlert("Contact customer or support first.");
                        console.error('Error updating order:', error);
                    });
                }
            };


            $scope.loadInventory = function(unitIdofPOD,brandId,partnerName){

                    callAndUpdateData(unitIdofPOD);
                    getPriceForProduct(unitIdofPOD,brandId,partnerName);
                    $scope.openInventoryModal();
                }

            function callAndUpdateData(unitIdofPOD){
                var unitId = unitIdofPOD;
                posAPI.allUrl('/',AppUtil.restUrls.posMetaData.inventory)
                    .post(unitId).then(function(response) {
                    console.log(response);
                    $scope.inventoryData = response;
                    $scope.inventoryGrid.data = getTableArrayFromInventory($scope.inventoryData);
                   console.log($scope.inventoryGrid.data);
                    setPriceForProducts();
                }, function(err) {
                    AppUtil.myAlert(err.data.errorMessage);
                });
            }

            function getPriceForProduct(unitId, brandId, partnerName) {
                var partnerId = 1;
                if (partnerName == "ZOMATO") {
                    partnerId = 3;
                }
                else if (partnerName == "SWIGGY") {
                    partnerId = 6;
                }else if(partnerName == "MAGICPIN"){
                    partnerId = 24;
                }
                var req = {
                    "brandId": brandId,
                    "unitId": unitId,
                    "partnerId": partnerId
                }

                posAPI.allUrl('/', AppUtil.restUrls.unitMetaData.brandPartnerProductProfile).customGET("", {
                    brandId: brandId,
                    unitId: unitId,
                    partnerId: partnerId
                }).then(function (response) {
                    if (response != null && response.products != null) {
                        $scope.PriceProducts = response.products;

                        console.log($scope.inventoryData);
                    }

                });

            }

            function delay(number) {
              setTimeout(number);
            }

            function setPriceForProducts() {

                delay(3000);
                $scope.inventoryGrid.data.map(function (prod) {
                    $rootScope.productPriceMap[prod.productId] = 0;
                })

                $scope.PriceProducts.map(function (prod) {
                    if (prod.type !== 5 && prod.subType !== 501) {
                        if ($rootScope.productPriceMap[prod.id] == 0) {
                            $rootScope.productPriceMap[prod.id] = prod.prices[0].price;
                        }
                    }
                    else {
                        delete  $rootScope.productPriceMap[prod.id];
                    }

                })

                $scope.inventoryGrid.data.sort(function (a, b) {
                    return $scope.productPriceMap[a.productId] - $scope.productPriceMap[b.productId];
                });
            }

            function getTableArrayFromInventory(inventoryArr){
                var data = new Array();
                angular.forEach(inventoryArr, function(val){
                    var productName = val.product.detail.name;
                    var productId = val.product.detail.id;
                    var lastUpdatedTime = val.lastUpdatedTime==null ? "NA" : val.lastUpdatedTime;
                    var quantity = val.quantity;
                    var thresholdQuantity = val.thresholdData==null ? -1 : val.thresholdData.avgQuantity;
                    var expireQuantity = val.expireQuantity;
                    var row = {productName:productName,
                        expireQuantity:expireQuantity,
                        quantity:quantity,
                        thresholdQuantity:thresholdQuantity,
                        lastUpdatedTime:lastUpdatedTime,
                        productId:productId};
                    data.push(row);
                });
                return data;
            }
            $scope.openInventoryModal = function () {
                $scope.isInventoryModalOpen = true;
                var selectObject = $scope.inventoryGrid;
                var modalInstance = $modal.open({
                    animation: true,
                    templateUrl: window.version + 'views/inventoryModal.html',
                    controller: 'inventoryModalCtrl',
                    scope: $scope,
                    backdrop: 'static',
                    resolve: {
                        selectObject: function () {
                            return selectObject;
                        }
                    }

                });


                modalInstance.result.then(function (action) {
                    $scope.isInventoryModalOpen  = false;
                }, function () {
                    $scope.isInventoryModalOpen  = false;
                });
            };
            $scope.closeInventoryModal = function (closeModal, confirmReq) {


                            // closeModal();
                            $scope.isInventoryModalOpen=false;

            };


            $scope.getCustomerDetailData = function (orderObject) {
                $rootScope.showFullScreenLoader = true;
                posAPI.allUrl('/', AppUtil.restUrls.externalOrderManagement.getCustomerDetailForFallbackOrder).customGET("", {
                    partnerCustomerId: orderObject.partnerOrder.customer_details.customer_id
                }).then(function (response) {
                    if (response) {
                        $("#customerDetailModal").modal("show");
                        $scope.customerDetailString = response.split('\\n');
                        $rootScope.showFullScreenLoader = false;
                    }
                    else{
                        $rootScope.showFullScreenLoader = false;
                        alert("No customer Found");
                    }
                }, function (err) {
                    console.log("NO")
                    $rootScope.showFullScreenLoader = false;
                    $scope.customerDetailString = ["No Data Found for customer"];
                });

            };

            $scope.onCloseCustomerDetail = function () {
                $scope.customerDetailString = [];
                $("#customerDetailModal").modal("hide");
            }

             $scope.logAction = function(orderId, userId, userName, action, fromStatus, toStatus, description) {
                console.log('Logging action:', {
                    orderId: orderId,
                    userId: userId,
                    userName: userName,
                    action: action,
                    fromStatus: fromStatus,
                    toStatus: toStatus,
                    description: description
                });

                return $http.post(AppUtil.getBaseUrl() + '/api/partner/order/action/log', {
                    orderId: orderId,
                    userId: userId,
                    userName: userName,
                    action: action,
                    fromStatus: fromStatus,
                    toStatus: toStatus,
                    description: description
                });
            };

            $scope.updateStatus = function(orderId, userId, status) {
                console.log('Updating status:', {
                    orderId: orderId,
                    userId: userId,
                    status: status
                });

                return $http.post(AppUtil.getBaseUrl() + '/api/partner/order/status/update', {
                    orderId: orderId,
                    userId: userId,
                    status: status
                });
            };


            $scope.showModal = false;
            $scope.Cancellation_Reason = null;
            $scope.Customer_Response = null;
            $scope.Partner_Response = null;
            $scope.isOtherSelected = false;

            $scope.modalData = {
                otherText: ""
            };

            // Contact Modal Variables
            $scope.showContactModal = false;
            $scope.contactList = [];
            $scope.currentOrder = null;
            $scope.selectAllContacts = false;

            // Contact Data Cache - Map for efficient lookup
            $scope.unitContactCache = new Map(); // unitId -> contactDetails
            $scope.contactDataLoading = new Map(); // unitId -> boolean (to prevent duplicate API calls)

            $scope.dropdowns = {
                cancel: {
                    label: "Cancellation Reason",
                    model: 'Cancellation_Reason',
                    options: ["Customer wants to cancel the order", "Customer doesn't want alternative ","Customer disagrees with replacement",
                        "Customer unresponsive","Customer wants only specific item ",
                        "Refund requested, but order not editable","Customer wants to take partial order (COD)",
                        "Customer cancelled due to internet issue in café","Cancelled by Swiggy support","Cancelled by Zomato support",
                        "Cancelled by customer","Cancelled automatically ","Order not shown in system","Placed and cancelled",
                        "Order taken up by Swiggy","No replacement available","Item Out of stock","Item not available and no replacement available",
                        "Others"]
                },
                customer: {
                    label: "Customer Response",
                    model: 'Customer_Response',
                    options: ["Call Answered", "Unresponsive", "Others"]
                },
                partner: {
                    label: "Partner Response",
                    model: 'Partner_Response',
                    options: ["Customer Taken", "Agent Taken", "Others"]
                }
            };


            $scope.openModal = function(type, order) {
                var dropdown = $scope.dropdowns[type];
                $scope.Cancellation_Reason = order.cancellationReason;
                $scope.Customer_Response = order.customerResponse;
                $scope.Partner_Response = order.partnerResponse;
                $scope.currentDropdown = {
                    label: dropdown.label,
                    model: $scope[dropdown.model],
                    options: dropdown.options,
                    key: dropdown.model
                };
                $scope.showModal = true;
                $scope.isOtherSelected = false;
                $scope.modalData.otherText = "";
            };

            $scope.closeModal = function() {
                $scope.showModal = false;
                $scope.currentDropdown = {};
                $scope.dropdownOpen = false;
                $scope.searchText = '';
                $scope.isOtherSelected = false;
                $scope.modalData.otherText = "";
            };

            $scope.dropdownOpen = false;

            $scope.toggleDropdown = function() {
                $scope.dropdownOpen = !$scope.dropdownOpen;
            };

            $scope.closeDropdownOnOutsideClick = function(event) {
                var dropdownContainer = document.getElementById('dropdown-container');

                if (dropdownContainer && !dropdownContainer.contains(event.target)) {
                    $timeout(function() {
                        $scope.dropdownOpen = false;
                    });
                }
            };


            $scope.handleModalContentClick = function(event) {
                var dropdownContainer = document.getElementById('dropdown-container');

                if (dropdownContainer && !dropdownContainer.contains(event.target)) {
                    $scope.dropdownOpen = false;
                }
            };


            $scope.selectOption = function(option) {
                $scope.currentDropdown.model = option;
                $scope.dropdownOpen = false;
                $scope.searchText = '';

                if (option === "Others") {
                    $scope.isOtherSelected = true;
                } else {
                    $scope.isOtherSelected = false;
                    $scope.modalData.otherText = "";
                }
            };

           $scope.submitModal = function(order) {
               var key = $scope.currentDropdown.key;
               var selectedValue = $scope.currentDropdown.model;

                if (selectedValue === "Others") {
                    console.log("Others selected, checking otherText...");

                    if (!$scope.modalData.otherText || $scope.modalData.otherText.trim() === "") {
                        console.log("Other text is empty or null");
                        AppUtil.myAlert("Please enter a reason for 'Others'.");
                        return;
                    }

                    selectedValue = $scope.modalData.otherText.trim();
                }

                if (!selectedValue || selectedValue.trim() === "") {
                    console.log("No value selected");
                   AppUtil.myAlert("Please select a value before submitting.");
                   return;
               }
                $scope[key] = selectedValue;

                console.log("Final submission data:", {
                    type: key,
                    value: selectedValue,
                    submittedBy: $scope.currentUser.userId,
                    orderId: order.orderId
                });

               $http({
                   method: 'POST',
                   url: AppUtil.restUrls.partnerOrder.partnerOrderResponse,
                   data: {
                       type: key,
                       value: selectedValue,
                       submittedBy: $scope.currentUser.userId,
                       orderId: order.orderId
                   }
                })
                .then(function(response) {
                    console.log("Dropdown data submitted successfully:", response);
                    AppUtil.myAlert("Submitted successfully.");
                    $scope[key] = selectedValue;
                   $scope.currentDropdown.model = null;
                   $scope.closeModal();
               }, function(error) {
                   console.error("Error submitting dropdown data:", error);
                   AppUtil.myAlert("Failed to submit. Please try again.");
               });
           };

            // Contact Modal Functions
            $scope.openContactModal = function(order) {
                $scope.currentOrder = order;
                $scope.showContactModal = true;

                // Get unitId from order
                var unitId = $scope.getUnitIdFromOrder(order);
                if (!unitId) {
                    console.error("Unable to determine unitId from order");
                    $scope.contactList = [{person: '', number: ''}];
                    return;
                }

                // Load contact data for this unit
                $scope.loadUnitContactData(unitId);
            };

            $scope.closeContactModal = function(event) {
                // Close modal only if clicking outside or on close button
                if (!event || event.target === event.currentTarget) {
                    $scope.showContactModal = false;
                    $scope.contactList = [];
                    $scope.currentOrder = null;
                }
            };

            $scope.handleContactModalContentClick = function(event) {
                // Prevent modal from closing when clicking inside modal content
                event.stopPropagation();
            };

            // This function is now defined later in the file with proper status handling

            $scope.removeContact = function(index) {
                if ($scope.contactList.length > 1) {
                    var contact = $scope.contactList[index];

                    // If contact has an ID (exists in database), mark as INACTIVE instead of removing
                    if (contact.id) {
                        contact.status = 'INACTIVE';
                        contact.isRemoved = true; // UI flag to show as removed
                        contact.person = contact.person || ''; // Preserve data for potential undo
                        contact.number = contact.number || '';
                    } else {
                        // If it's a new contact (no ID), remove from array
                        $scope.contactList.splice(index, 1);
                    }
                } else {
                    AppUtil.myAlert("At least one contact entry is required.");
                }
            };

            $scope.saveContacts = function() {
                // Validate contacts
                var validContacts = [];
                var hasValidationError = false;

                for (var i = 0; i < $scope.contactList.length; i++) {
                    var contact = $scope.contactList[i];

                    // Skip empty contacts
                    if (!contact.person || contact.person.trim() === '' ||
                        !contact.number || contact.number.trim() === '') {
                        continue;
                    }

                    // Validate contact number
                    var numberStr = contact.number.toString().trim();

                    // Check if number contains only digits
                    if (!/^\d+$/.test(numberStr)) {
                        AppUtil.myAlert("Contact number must contain only digits. Please check row " + (i + 1));
                        hasValidationError = true;
                        break;
                    }

                    // Check if number is exactly 10 digits
                    if (numberStr.length !== 10) {
                        AppUtil.myAlert("Contact number must be exactly 10 digits. Please check row " + (i + 1));
                        hasValidationError = true;
                        break;
                    }

                    validContacts.push(contact);
                }

                if (hasValidationError) {
                    return;
                }

                if (validContacts.length === 0) {
                    AppUtil.myAlert("Please enter at least one valid contact with both person and number.");
                    return;
                }
                var unitId = $scope.getUnitIdFromOrder($scope.currentOrder);
                if (!unitId) {
                    AppUtil.myAlert("Unable to determine unit ID for saving contacts.");
                    return;
                }

                // Prepare data for API - include all contacts (active, inactive, and new)
                var allContacts = [];

                // Process all contacts in the list
                $scope.contactList.forEach(function(contact) {
                    // Include removed/inactive contacts
                    if (contact.isRemoved || contact.status === 'INACTIVE') {
                        allContacts.push({
                            id: contact.id,
                            referenceName: contact.person ? contact.person.trim() : '',
                            contactNumber: contact.number ? parseInt(contact.number.toString().trim()) : null,
                            status: 'INACTIVE'
                        });
                    }
                    // Include valid active contacts
                    else if (contact.person && contact.person.trim() !== '' &&
                             contact.number && contact.number.toString().trim() !== '') {

                        var numberStr = contact.number.toString().trim();
                        if (/^\d{10}$/.test(numberStr)) {
                            allContacts.push({
                                id: contact.id || null,
                                referenceName: contact.person.trim(),
                                contactNumber: parseInt(numberStr),
                                status: 'ACTIVE'
                            });
                        }
                    }
                });

                var contactData = {
                    unitId: unitId,
                    contacts: allContacts
                };

                $rootScope.showFullScreenLoader = true;

                // Save to backend
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.unitMetaData.unitContactData,
                    data: contactData,
                    headers: {
                        'Content-Type': 'application/json'
                    }
                }).then(function(response) {
                    if (response.data === true) {
                        // Update cache with only active contacts
                        var activeContacts = allContacts.filter(function(contact) {
                            return contact.status === 'ACTIVE';
                        });

                        $scope.unitContactCache.set(unitId, {
                            unitId: unitId,
                            unitName: $scope.getUnitNameFromOrder($scope.currentOrder),
                            contactDetails: activeContacts.map(function(contact, index) {
                                return {
                                    id: contact.id || (Date.now() + index), // Generate temp ID if new
                                    code: contact.referenceName,
                                    name: contact.contactNumber.toString()
                                };
                            })
                        });

                        AppUtil.mySuccessAlert("Contact information saved successfully!");
                        $scope.closeContactModal();
                    } else {
                        AppUtil.myAlert("Failed to save contact information. Please try again.");
                    }
                    $rootScope.showFullScreenLoader = false;
                }, function(error) {
                    console.error("Error saving contact data:", error);
                    AppUtil.myAlert("Failed to save contact information. Please try again.");
                    $rootScope.showFullScreenLoader = false;
                });
            };

            // Load unit contact data with caching
            $scope.loadUnitContactData = function(unitId) {
                // Check if data is already in cache
                if ($scope.unitContactCache.has(unitId)) {
                    var cachedData = $scope.unitContactCache.get(unitId);
                    $scope.contactList = cachedData.contactDetails.map(function(contact) {
                        return {
                            id: contact.id,
                            person: contact.code,
                            number: contact.name,
                            selected: false,
                            status: 'ACTIVE'
                        };
                    });

                    // Ensure at least one empty row
                    if ($scope.contactList.length === 0) {
                        $scope.contactList.push({person: '', number: '', selected: false});
                    }
                    return;
                }

                // Check if already loading to prevent duplicate calls
                if ($scope.contactDataLoading.get(unitId)) {
                    return;
                }

                $scope.contactDataLoading.set(unitId, true);
                $rootScope.showFullScreenLoader = true;

                // Fetch from API
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.unitMetaData.unitContactData,
                    params: { unitId: unitId }
                }).then(function(response) {
                    var contactDataList = response.data;

                    if (contactDataList && contactDataList.length > 0) {
                        var unitContactData = contactDataList[0];

                        // Cache the data
                        $scope.unitContactCache.set(unitId, unitContactData);

                        // Convert to display format - only show active contacts
                        if (unitContactData.contactDetails && unitContactData.contactDetails.length > 0) {
                            $scope.contactList = unitContactData.contactDetails.map(function(contact) {
                                return {
                                    id: contact.id,
                                    person: contact.code,
                                    number: contact.name,
                                    status: 'ACTIVE', // Assume API returns only active contacts
                                    selected: false
                                };
                            });
                        } else {
                            $scope.contactList = [{person: '', number: '', selected: false}];
                        }
                    } else {
                        // No contacts found, initialize with empty row
                        $scope.contactList = [{person: '', number: ''}];

                        // Cache empty result to avoid repeated API calls
                        $scope.unitContactCache.set(unitId, {
                            unitId: unitId,
                            unitName: $scope.getUnitNameFromOrder($scope.currentOrder),
                            contactDetails: []
                        });
                    }

                    $scope.contactDataLoading.set(unitId, false);
                    $rootScope.showFullScreenLoader = false;
                }, function(error) {
                    console.error("Error loading contact data:", error);
                    $scope.contactList = [{person: '', number: ''}];
                    $scope.contactDataLoading.set(unitId, false);
                    $rootScope.showFullScreenLoader = false;
                });
            };

            // Helper function to get unit ID from order
            $scope.getUnitIdFromOrder = function(order) {
                if (!order) return null;

                // Try different possible unit ID fields
                return order.unitId ||
                       (order.partnerOrder && order.partnerOrder.outlet_id) ||
                       (order.partnerOrder && order.partnerOrder.merchantData && order.partnerOrder.merchantData.client_id) ||
                       null;
            };

            // Helper function to get unit name from order
            $scope.getUnitNameFromOrder = function(order) {
                if (!order) return '';

                var unitId = $scope.getUnitIdFromOrder(order);
                if (unitId && $scope.outlets && $scope.outlets[unitId]) {
                    return $scope.outlets[unitId].name;
                }
                return 'Unknown Unit';
            };

            // Real-time contact number validation
            $scope.validateContactNumber = function(contact, index) {
                if (!contact.number) {
                    contact.numberError = '';
                    return;
                }

                var numberStr = contact.number.toString();

                // Remove any non-digit characters
                var cleanNumber = numberStr.replace(/\D/g, '');

                // Limit to 10 digits
                if (cleanNumber.length > 10) {
                    cleanNumber = cleanNumber.substring(0, 10);
                }

                // Update the contact number with cleaned value
                contact.number = cleanNumber;

                // Set validation error messages
                if (cleanNumber.length === 0) {
                    contact.numberError = '';
                } else if (cleanNumber.length < 10) {
                    contact.numberError = 'Number must be 10 digits (' + cleanNumber.length + '/10)';
                } else if (cleanNumber.length === 10) {
                    contact.numberError = '';
                }
            };

            // Validate contact person name
            $scope.validateContactPerson = function(contact, index) {
                if (!contact.person) {
                    contact.personError = '';
                    return;
                }

                var personName = contact.person.trim();

                // Check minimum length
                if (personName.length > 0 && personName.length < 2) {
                    contact.personError = 'Name must be at least 2 characters';
                } else if (personName.length > 50) {
                    contact.personError = 'Name must be less than 50 characters';
                    contact.person = personName.substring(0, 50);
                } else {
                    contact.personError = '';
                }
            };

            // Check if there are any validation errors
            $scope.hasContactValidationErrors = function() {
                if (!$scope.contactList || $scope.contactList.length === 0) {
                    return true;
                }

                var hasValidContact = false;
                var hasErrors = false;

                for (var i = 0; i < $scope.contactList.length; i++) {
                    var contact = $scope.contactList[i];

                    // Check for validation errors
                    if (contact.personError || contact.numberError) {
                        hasErrors = true;
                    }

                    // Check if there's at least one valid contact
                    if (contact.person && contact.person.trim() !== '' &&
                        contact.number && contact.number.toString().trim() !== '' &&
                        contact.number.toString().trim().length === 10) {
                        hasValidContact = true;
                    }
                }

                return hasErrors || !hasValidContact;
            };

            // Undo remove contact (reactivate)
            $scope.undoRemoveContact = function(index) {
                var contact = $scope.contactList[index];
                if (contact.isRemoved) {
                    contact.status = 'ACTIVE';
                    contact.isRemoved = false;
                }
            };

            // Add new contact row
            $scope.addContact = function() {
                $scope.contactList.push({
                    person: '',
                    number: '',
                    status: 'ACTIVE',
                    selected: false
                });
            };

            // Toggle select all contacts
            $scope.toggleSelectAll = function() {
                $scope.contactList.forEach(function(contact) {
                    if (!contact.isRemoved) {
                        contact.selected = $scope.selectAllContacts;
                    }
                });
            };

            // Update select all checkbox based on individual selections
            $scope.updateSelectAll = function() {
                var activeContacts = $scope.contactList.filter(function(contact) {
                    return !contact.isRemoved;
                });

                var selectedActiveContacts = activeContacts.filter(function(contact) {
                    return contact.selected;
                });

                $scope.selectAllContacts = activeContacts.length > 0 &&
                                          selectedActiveContacts.length === activeContacts.length;
            };

            // Check if any contacts are selected
            $scope.hasSelectedContacts = function() {
                return $scope.contactList.some(function(contact) {
                    return contact.selected && !contact.isRemoved;
                });
            };

            // Remove selected contacts (bulk operation)
            $scope.removeSelectedContacts = function() {
                var selectedCount = 0;
                var activeContactsCount = 0;

                $scope.contactList.forEach(function(contact) {
                    if (!contact.isRemoved) {
                        activeContactsCount++;
                        if (contact.selected) {
                            selectedCount++;
                        }
                    }
                });

                // Prevent removing all contacts
                if (selectedCount === activeContactsCount) {
                    AppUtil.myAlert("At least one contact must remain active.");
                    return;
                }

                // Mark selected contacts for removal
                $scope.contactList.forEach(function(contact) {
                    if (contact.selected && !contact.isRemoved) {
                        if (contact.id) {
                            // Existing contact - mark as inactive
                            contact.status = 'INACTIVE';
                            contact.isRemoved = true;
                        } else {
                            // New contact - will be removed from array later
                            contact.isRemoved = true;
                        }
                        contact.selected = false;
                    }
                });

                // Remove new contacts that were marked for removal
                $scope.contactList = $scope.contactList.filter(function(contact) {
                    return !(contact.isRemoved && !contact.id);
                });

                $scope.selectAllContacts = false;
                AppUtil.mySuccessAlert(selectedCount + " contact(s) marked for removal.");
            };

            $scope.openRejectOrderModal = function (order) {
                $scope.showRejectionModal = true;
                $scope.selectedOrder = order;
                if (order.partnerName == "SWIGGY") {
                    $scope.getRejectionReasonsSwiggy();
                } else if (order.partnerName == "ZOMATO") {
                    $scope.getRejectionReasonsZomato();
                }
                $("#rejectOrderModal").modal("show");
            };

            $scope.closeRejectOrderModal = function () {
                $scope.showRejectionModal = false;
                $scope.rejectionObject = {
                    id: null,
                    message: null
                };
                $scope.rejectionReasons = [];
                $("#rejectOrderModal").modal("hide");
            };

            $scope.getRejectionReasonsZomato = function () {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.order.getRejectionReasonsZomato
                }).then(function success(response) {
                    if (response != null && response.status === 200) {
                        Object.keys(response.data).forEach(function(key) {
                            $scope.rejectionReasons.push(
                                { id: key, message: response.data[key] }
                            )
                        });
                        console.log("Zomato Rejection Reasons are : ", $scope.rejectionReasons);
                        $rootScope.showFullScreenLoader = false;
                    }
                }, function error(response) {
                    console.log("Error while fetching Zomato Rejection Reasons");
                    $rootScope.showFullScreenLoader = false;
                });
            }

            $scope.getRejectionReasonsSwiggy = function () {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.order.getRejectionReasonsSwiggy
                }).then(function success(response) {
                    if (response != null && response.status === 200) {
                        response.data.forEach(function(key, index) {
                            $scope.rejectionReasons.push(
                                { id: index + 1, message: key }
                            )
                        });
                        console.log("Swiggy Rejection Reasons are : ", $scope.rejectionReasons);
                        $rootScope.showFullScreenLoader = false;
                    }
                }, function error(response) {
                    console.log("Error while fetching Swiggy Rejection Reasons");
                    $rootScope.showFullScreenLoader = false;
                });
            }

            $scope.submitRejectOrder = function () {
                if ($scope.selectedOrder.partnerName == "ZOMATO") {
                    $scope.submitRejectOrderZomato();
                } else if ($scope.selectedOrder.partnerName == "SWIGGY") {
                    $scope.submitRejectOrderSwiggy();
                }
            }

            $scope.onRejectionReasonChange = function () {
                if ($scope.selectedOrder.partnerName == "ZOMATO") {
                    if (parseInt($scope.rejectionObject.id) === 1) {
                        $scope.rejectionItems = $scope.selectedOrder.partnerOrder.dishes.map(function (dish) {
                            return ({
                                catalogueId: dish.composition.catalogue_id,
                                catalogueName: dish.composition.catalogue_name,
                                selected: false
                            })
                        });
                    } else {
                        $scope.rejectionItems = [];
                    }
                } else if ($scope.selectedOrder.partnerName == "SWIGGY") {
                    if (parseInt($scope.rejectionObject.id) === 1) {
                        $scope.rejectionItems = $scope.selectedOrder.partnerOrder.items.map(function (item) {
                            return ({
                                catalogueId: item.id,
                                catalogueName: item.name,
                                nextAvailableTimeEpoch: AppUtil.getCurrentTimeInEpochSeconds() + 2 * 60, // 2 minutes from now
                                selected: false
                            })
                        });
                    } else {
                        $scope.rejectionItems = [];
                    }
                }
            }

            $scope.submitRejectOrderZomato = function () {
                if ($scope.rejectionObject.id == null || $scope.rejectionObject.id == undefined) {
                    bootbox.alert("Kindly select appropriate reason!");
                    return;
                }
                $rootScope.showFullScreenLoader = true;
                var requestObj = {
                    order_id: $scope.selectedOrder.partnerOrderId,
                    rejection_message_id: parseInt($scope.rejectionObject.id)
                }
                if ($scope.rejectionObject.id === '1') {
                    var rejectionItemIds = [];
                    $scope.rejectionItems.forEach(function(item) {
                        if (item.selected) {
                            rejectionItemIds.push(item.catalogueId);
                        }
                    });
                    requestObj = Object.assign({}, requestObj, { catalogue_vendor_entity_ids: rejectionItemIds });
                }
                if ($scope.rejectionObject.id === '1' && AppUtil.isEmptyObject(requestObj.catalogue_vendor_entity_ids)) {
                    bootbox.alert("Kindly select atleast 1 item(s)!");
                    return;
                }
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.order.rejectOrderZomato,
                    data: requestObj,
                    params: {
                        brandId: $scope.selectedOrder.brandId ? $scope.selectedOrder.brandId : 1,
                        rejectedBy: AppUtil.GetLoggedInUser()
                    }
                }).then(function success(response) {
                    if (response != null && response.status === 200) {
                        if (response.data.status === 'success') {
                            bootbox.alert("Order Rejection Successful!");
                            $scope.getPendingOrders();
                        } else if (response.data.status === 'failed') {
                            bootbox.alert("Order Rejection Failed with Reason: " + response.data.message);
                        } else {
                            bootbox.alert("Error occured while rejecting order!");
                        }
                        $rootScope.showFullScreenLoader = false;
                    } else {
                        bootbox.alert("Error occured while rejecting order!");
                        $rootScope.showFullScreenLoader = false;
                    }
                    $scope.closeRejectOrderModal();
                }, function error(response) {
                    bootbox.alert("Error occured while rejecting order!");
                    $rootScope.showFullScreenLoader = false;
                    $scope.closeRejectOrderModal();
                });
            }

            $scope.submitRejectOrderSwiggy = function () {
                if ($scope.rejectionObject.id == null || $scope.rejectionObject.id == undefined) {
                    bootbox.alert("Kindly select appropriate reason!");
                    return;
                }
                $rootScope.showFullScreenLoader = true;

                var rejectionMetadata;
                if ($scope.rejectionObject.id == 1) {
                    var outOfStockItems = [];
                    $scope.rejectionItems.forEach(function(item) {
                        if (item.selected) {
                            outOfStockItems.push({
                                item_id: item.catalogueId,
                                next_available_time_epoch: item.nextAvailableTimeEpoch
                            });
                        }
                    });
                    if (AppUtil.isEmptyObject(outOfStockItems)) {
                        bootbox.alert("Kindly select atleast 1 item(s)!");
                        $rootScope.showFullScreenLoader = false;
                        return;
                    }
                    rejectionMetadata = {
                        out_of_stock_items: outOfStockItems
                    }
                } else {
                    rejectionMetadata = {
                        next_available_time_epoch: AppUtil.getCurrentTimeInEpochSeconds() + 2 * 60 // 5 minutes from now
                    };
                }

                var requestObj = {
                    reference_id: $scope.selectedOrder.partnerOrderId,
                    action_type: "REJECT_ORDER",
                    metadata: {
                        rejection_reason: $scope.rejectionObject.message,
                        rejection_metadata: rejectionMetadata
                    }
                };

                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.order.rejectOrderSwiggy,
                    data: requestObj
                }).then(function success(response) {
                    if (response != null && response.status === 200) {
                        if (response.data.statusCode === 0) {
                            bootbox.alert("Order Rejection Successful!");
                            $scope.getPendingOrders();
                        } else {
                            bootbox.alert("Error occured while rejecting order! " + response.data.statusMessage);
                        }
                        $rootScope.showFullScreenLoader = false;
                    } else {
                        bootbox.alert("Error occured while rejecting order!");
                        $rootScope.showFullScreenLoader = false;
                    }
                    $scope.closeRejectOrderModal();
                }, function error(response) {
                    bootbox.alert("Error occured while rejecting order!");
                    $rootScope.showFullScreenLoader = false;
                    $scope.closeRejectOrderModal();
                });
            }
        }]);
