/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';
/*jshint sub:true*/

angular.module('posApp')
    .controller('managersreportCtrl', ['$scope', 'managersreportObj', '$modalInstance', 'AppUtil', 'posAPI', '$rootScope',
        function ($scope, managersreportObj, $modalInstance, AppUtil, posAPI, $rootScope) {
            $scope.managersreport = managersreportObj;
            $scope.currentUser = $rootScope.globals.currentUser;
            $scope.sessionKeyId = encodeURIComponent($rootScope.globals.currentUser.sessionKeyId);
            //console.log($scope.managersreport);

            $scope.cancel = function () {
                $modalInstance.dismiss('dismiss');
            };

            $scope.getLastModified = lastModifiedConvert;

            function lastModifiedConvert(index) {
                var currentLastModified = $scope.managersreport[index].lastModified;
                var dateString = moment.unix(currentLastModified / 1000).format("DD/MM/YYYY");
                return dateString;
            }




        }]);