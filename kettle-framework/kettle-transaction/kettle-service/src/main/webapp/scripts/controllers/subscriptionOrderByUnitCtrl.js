/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

angular.module('posApp')
    .controller('subscriptionOrderByUnitCtrl', ['$rootScope','$scope','AppUtil','$location','$http',
        'posAPI','subscriptionService',
        function ($rootScope,$scope,AppUtil,$location,$http,posAPI,subscriptionService) {

	    	$scope.init= function(){
	    		AppUtil.validate();
	    		$scope.backToCover = AppUtil.backToCover;
	    		if(AppUtil.previousLocation[AppUtil.previousLocation.length - 2]=="/orderSearch"){
	    			$scope.outletList = subscriptionService.outletList;
            		$scope.orderList = subscriptionService.unitOrderList;
            		$scope.selectedUnit = subscriptionService.selectedOutlet;
            	}else{
    	    		$scope.outletList = [];
            		$scope.orderList = null;
            		$scope.selectedUnit = null;
    	    		fetchOutlets("CAFE");
            	}
	    	}
	    	
	    	$scope.getSubscriptionOrdersForUnit = function(){
	    		subscriptionService.selectedOutlet = $scope.selectedUnit;
	    		var requestObj =  AppUtil.GetRequest($scope.selectedUnit.id);
 	    		posAPI.allUrl('/',AppUtil.restUrls.subscription.getSubscriptionOrdersForUnit)
     			.post(requestObj).then(function(response) {
     				if(response){
     					$scope.orderList = response.plain();
     					subscriptionService.unitOrderList = $scope.orderList;
     				}
     			}, function(err) {
     				AppUtil.myAlert(err.data.errorMessage);
     			});
	    	}
	    	
	    	$scope.openOrderSearch = function(subscriptionObj) {
	    		AppUtil.OrderObj = $rootScope.OrderObj = subscriptionObj;
	    		$rootScope.orderType = 'subscription';
                if (AppUtil.OrderObj.deliveryAddress != null) {
                	AppUtil.GetCustomerInfo(AppUtil.OrderObj.deliveryAddress);
                    AppUtil.getDeliveryDetail(AppUtil.OrderObj.generateOrderId);
                } else if (!AppUtil.isEmptyObject($rootScope.CustomerObj)) {
                    $rootScope.CustomerObj = {};
                }
                $location.url('/orderSearch');
            };
            
            $scope.loadOrderScreen = function(order){
            	AppUtil.OrderObj = $rootScope.OrderObj = order.orderDetail;
	    		$rootScope.orderType = 'order';
                if (AppUtil.OrderObj.deliveryAddress != null) {
                	AppUtil.GetCustomerInfo(AppUtil.OrderObj.deliveryAddress);
                    AppUtil.getDeliveryDetail(AppUtil.OrderObj.generateOrderId);
                } else if (!AppUtil.isEmptyObject($rootScope.CustomerObj)) {
                    $rootScope.CustomerObj = {};
                }
                $location.url('/orderSearch');
            }
	    	
	    	function fetchOutlets(code) {
                posAPI.allUrl('/',AppUtil.restUrls.unitMetaData.units).customGET("", {category: code})
                    .then(function (response) {
                    	if($scope.outletList.length==0){
                    		$scope.outletList = response.plain();
                    	}else{
                    		angular.forEach(response.plain(), function(v){
                    			$scope.outletList.push(v);
                    		});
                    	} 
                		convertOutletIdToNumber();
                		if(code!="DELIVERY"){
                			fetchOutlets("DELIVERY");
                		}            			
                }, function (err) {
                    AppUtil.myAlert(err.data.errorMessage);
                });
            }
	    	
	    	function convertOutletIdToNumber(){
    			var newOutletList = new Array();
    			angular.forEach($scope.outletList, function(v){
    				var obj = v
    				obj.id = parseInt(v.id);
    				newOutletList.push(obj);
    			});
    			$scope.outletList = newOutletList;
    			subscriptionService.outletList = $scope.outletList;
    		}
    		
	}]);