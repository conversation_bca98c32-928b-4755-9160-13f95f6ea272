/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

angular.module('posApp')
    .controller('cafeTimingsDashboardCtrl', ['$rootScope','$scope','AppUtil','$location','$http',
        'posAPI','subscriptionService',
        function ($rootScope,$scope,AppUtil,$location,$http,posAPI,subscriptionService) {

            $scope.backToCODCover = function () {
                $location.url('/CODCover');
            };
            $scope.init= function(){
                AppUtil.validate();
                $scope.outletList = [];
                $scope.selectedUnit = null;
                $scope.UnitResponseData = null;
                fetchOutlets("CAFE");
            }

            $scope.getUnitData = function () {
                $scope.unitIdData = $scope.selectedUnit.id;
                $scope.isUnitDetailLoaded($scope.unitIdData);
                if (!$scope.loaded) {
                    $rootScope.showFullScreenLoader = true;
                    var flagAction = true;
                    $http({
                        method: 'POST',
                        url: AppUtil.restUrls.unitMetaData.unitData,
                        data: $scope.unitIdData
                    }).then(function success(response) {
                        $scope.UnitResponseData = response.data;
                        $rootScope.showFullScreenLoader = false;
                        console.log('getUnitData',$scope.UnitResponseData)
                    },function error(response) {
                            console.log("error:" + response);
                    });
                }
            }

            $scope.isUnitDetailLoaded = function (unitId) {
                $scope.unitlist.forEach(function (unit) {
                    if (unit.id === unitId) {
                        if (angular.isUndefined(unit.unitDetail) || unit.unitDetail == null) {
                            $scope.loaded = false;
                        } else {
                            $scope.loaded = true;
                        }
                    }
                });
            };

            function fetchOutlets(code) {
                posAPI.allUrl('/',AppUtil.restUrls.unitMetaData.units).customGET("", {category: code})
                    .then(function (response) {
                        if($scope.outletList.length==0){
                            $scope.outletList = response.plain();
                            $scope.unitlist = response.plain();
                        }else{
                            angular.forEach(response.plain(), function(v){
                                $scope.outletList.push(v);
                            });
                        }
                        convertUnitIdToNumber();
                        if(code!="DELIVERY"){
                            fetchOutlets("DELIVERY");
                        }
                    }, function (err) {
                        AppUtil.myAlert(err.data.errorMessage);
                    });
            }

            function convertUnitIdToNumber(){
                var newUnitList = new Array();
                angular.forEach($scope.outletList, function(v){
                    var obj = v
                    obj.id = parseInt(v.id);
                    newUnitList.push(obj);
                });
                $scope.outletList = newUnitList;
                subscriptionService.outletList = $scope.outletList;
            }

        }]);