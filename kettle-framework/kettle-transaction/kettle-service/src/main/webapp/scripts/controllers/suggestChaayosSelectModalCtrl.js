/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';
/*jshint sub:true*/

angular.module('posApp').controller('suggestChaayosSelectModalCtrl',
    ['$scope', '$http', 'APIJson', '$modalInstance', 'AppUtil', '$rootScope', 'posAPI', 'productService', 'selectObject','totalAmount','offerApplied','amountPayable','socketUtils','customerFrequency','last90DaysSale','orderDetail',
        function ($scope, $http, APIJson, $modalInstance, AppUtil, $rootScope, posAPI, productService, selectObject,totalAmount,offerApplied,amountPayable,socketUtils,customerFrequency,last90DaysSale,orderDetail) {
            $scope.cardType = selectObject.type;
            $scope.subscriptionProductIds = [1000064];
            var giftCardId = [AppUtil.getTransactionMetadata().subscriptionProductId];
            var giftCardNumberList = [];
            $scope.giftCardsList = [];
            $scope.notEditable = {};
            $scope.cardNumber = {};
            var gyftrDetail = null;
            var card = {};
            $scope.cardList = [{"name": "SELECT", "code": "SELECT"}];
            $scope.voucherList = [0];
            $scope.vouherNumbers = {};
            $scope.vouherNumbers[1] = "";
            $rootScope.isChaayosSelectChoosen=false;
            $scope.amountPayable = amountPayable;
            $scope.totalAmount = totalAmount;
            $scope.offerApplied = offerApplied;
            $scope.customerFrequency = customerFrequency;
            $scope.last90DaysSale = last90DaysSale;
            $scope.offerPercentage = null;
            $scope.subscriptionEndDate = null;
            $scope.orderWithoutWallet = orderDetail;


            $scope.init = function () {
                $scope.backupCurrentState();
                $scope.giftCardOfferInfo = AppUtil.giftCardOffers; // to get offer which are on cover page
                $scope.giftCards = {};
                $scope.giftCardOfferInfo.forEach(function (data) {
                    $scope.giftCards[data.denomination] = (data.denomination * data.offer) / 100;
                });
                getGiftCards();
                if(!(!AppUtil.customerSocket || AppUtil.customerSocket.name == null) && !$scope.offerApplied ){
                    savings()
                }
                $scope.showOfferPrompt();
            };

            function savings(){
                var couponCode = JSON.parse(localStorage.getItem("Subscription_Products"));
                var unitId =$rootScope.globals.currentUser.unitId;
                var offerManagementObj = {
                    unitId:unitId,
                    totalAmount:$scope.totalAmount,
                    paidAmount:$scope.amountPayable,
                    productId:$scope.subscriptionProductIds,
                }
                var maxSavings = 0;
                var maxCoupon = 0;
                posAPI.allUrl('/',AppUtil.restUrls.offers.offerManagement).post(offerManagementObj).then(function (response) {
                    var data = response.plain();
                    if(!AppUtil.isEmptyObject(data) && !AppUtil.isEmptyObject(data.applicableDiscounts)){
                        couponCode.map(function(coupon){
                            if(!AppUtil.isEmptyObject(data.applicableDiscounts[coupon]) && maxSavings<data.applicableDiscounts[coupon].applicableDiscount.value ){
                                maxSavings = data.applicableDiscounts[coupon].applicableDiscount.value;
                                maxCoupon = coupon;
                            }
                        })
                        if(maxCoupon !== 0){
                            var dimension = data.applicableDiscounts[maxCoupon].dimension;
                            var duration = "months"
                            var currentDate = new Date();
                            if(dimension.startsWith("3")){
                                duration = "3 "+duration;
                                $scope.subscriptionEndDate = new Date(currentDate.setMonth(currentDate.getMonth()+3));
                            }else if(dimension.startsWith("6")){
                                duration = "6 "+duration;
                                $scope.subscriptionEndDate = new Date(currentDate.setMonth(currentDate.getMonth()+6));
                            }else if(dimension.startsWith("9")){
                                duration = "9 "+duration;
                                $scope.subscriptionEndDate = new Date(currentDate.setMonth(currentDate.getMonth()+9));
                            }else if(dimension.startsWith("12")){
                                duration = "12 "+duration;
                                $scope.subscriptionEndDate = new Date(currentDate.setMonth(currentDate.getMonth()+12));
                            }
                            var options = {
                                year: "numeric",
                                month: "long",
                                day: "numeric" };
                            $scope.subscriptionEndDate = $scope.subscriptionEndDate.toLocaleDateString("en-US", options);
                            var price = data.applicableDiscounts[maxCoupon].price;
                            $scope.offerPercentage = data.applicableDiscounts[maxCoupon].applicableDiscount.percentage;
                            var maxSavings3month = calculateSavings3Months(maxSavings);
                            var remainingAmount = $scope.amountPayable - maxSavings;
                            var selectAmountTobePaid = maxSavings>=price?0:Math.ceil(price - maxSavings);
                            maxSavings = maxSavings.toFixed(2);
                            var maxOffer = {
                                savings: maxSavings,
                                savings3month: maxSavings3month,
                                totalBill:$scope.amountPayable.toFixed(2),
                                remainingAmount:  remainingAmount.toFixed(2)
                            }
                            var offerMessage = "Inform customer" + "<br>" + "<h2><b> -> Save Rs. "+ maxSavings3month + "* in 3 months <br><br>"  +  "-> Pay Rs. "+ selectAmountTobePaid +
                                " extra and get "+ $scope.offerPercentage +"% off on all orders for next "+ duration + "</b></h2><br><h4>*Estimated based on savings history of similar customers</h4>"
                            bootbox.dialog({
                                className:"alertBox",
                                closeButton: false,
                                onEscape: false,
                                message: offerMessage,
                                size: "small",
                            })
                            socketUtils.emitMessage({SUBSCRIPTION_SAVINGS: maxOffer});
                            $rootScope.isSavingsScreenOpen = true;
                            productService.metaDataList.addAttribute("CHAAYOS_SELECT_SUGGESTED", moment().format());
                        }
                    }
                });

            }

            function calculateSavings3Months(savings){
                var savings3months;
                var maxRange = 500;
                var minRange = 400;
                var randomNumber = Math.ceil(Math.random() * (maxRange - minRange) + minRange);

                if(AppUtil.isEmptyObject(customerFrequency)){
                    savings3months = randomNumber;
                    return savings3months;
                }
                var currentOrderSavings3month = savings*customerFrequency;
                var currentOrderSales = $scope.totalAmount*customerFrequency;
                var last90DaysSalesSavings = (currentOrderSavings3month*last90DaysSale)/currentOrderSales;

                if(currentOrderSavings3month < 400 && last90DaysSalesSavings < 400){
                    savings3months = randomNumber;
                }else{
                    savings3months = Math.max(currentOrderSavings3month,(last90DaysSalesSavings+savings));
                }

                return savings3months.toFixed(2);
            }

            $scope.getVoucherList = function() {
                return $scope.voucherList;
            };

            $scope.updateVoucher = function(action){
                if(action == 'add'){
                    $scope.voucherList.push($scope.voucherList.length);
                }else if(action == 'remove'){
                    if($scope.voucherList.length == 1){
                        AppUtil.myAlert("One vouhcer required.");
                        return false;
                    }
                    $scope.voucherList.splice($scope.voucherList.length-1, 1);
                }
            };

            $scope.verifyVoucher = function() {
                if(gyftrDetail.recipeId == 0){
                    AppUtil.myAlert("Please update gyftr voucher recipe to calculate consumption.");
                    return false;
                }
                for (var i in $scope.vouherNumbers) {
                    if($scope.vouherNumbers[i].length < 6){
                        AppUtil.myAlert("Please Enter Valid Vouhcer No!");
                        return false;
                    }
                }
                var  result = false;
                for (var i in $scope.vouherNumbers) {
                    result = $scope.addGyftrVoucherInOrder(gyftrDetail.linkedProduct, $scope.vouherNumbers[i].toUpperCase());
                }
                if(result){
                    $scope.checkOrder();
                    $scope.$parent.disableOrderCheckBtnGift =true;
                }
            };

            function getGiftCards() {
                if ($scope.cardType == 'ECARD') {
                    AppUtil.setCardType('ECARD');
                }
                else if($scope.cardType == 'SELECT'){
                    AppUtil.setCardType('SELECT');
                } else if($scope.cardType == 'SUGGEST SELECT'){
                    AppUtil.setCardType('SELECT');

                }
                else {
                    AppUtil.setCardType('SELF');
                }
                //3810 for subscription 904 before
                var list = productService.getProductArrayForSubTypeCode(3810);
                console.log(list);
                for (var i = 0; i < list.length; i++) {
                    if($scope.cardType == 'SUGGEST SELECT'){
                        if(list[i].id === 1000064 && list[i].prices && list[i].prices.length > 0){
                            $scope.giftCardsList.push(list[i]);
                            $scope.addNewProductToOrderItemArray(list[i]);
                            break;
                        }
                    }
                }

            }

            $scope.changeCardType = function (cardType) {
                if (cardType == 'GYFTR') {
                    getGyftrPartnerDetail($scope.cardType);
                }
                $scope.cardType = cardType;
                AppUtil.setCardType(cardType)
            };

            function getGyftrPartnerDetail(cardType) {
                posAPI.allUrl('/', AppUtil.restUrls.order.getExternalPartnerDetail).post("GYFTR").then(function (response) {
                    if (response.errorType != undefined && response.errorType != '') {
                        AppUtil.myAlert("Gyftr Partner Detail Not Available.");
                        $scope.changeCardType(cardType);
                    } else {
                        gyftrDetail = response.plain();
                        if (gyftrDetail.recipeId == 0) {
                            AppUtil.myAlert("Please update gyftr voucher recipe to calculate consumption.");
                            return false;
                        }
                        gyftrDetail.linkedProduct.prices.push({
                            "dimension": "None",
                            "price": 0,
                            "recipe": null,
                            "recipeId": gyftrDetail.recipeId,
                            "customize": false
                        });
                        //	console.log("gyftrDetail.linkedProduct",gyftrDetail);
                    }
                }, function (err) {
                    AppUtil.myAlert(err.data.errorMessage);
                });
            }


            $scope.purchaseGiftCard = function () {
                if (AppUtil.cardType != 'ECARD'  && AppUtil.cardType != 'SELECT' ) {
                    for (var i = 0; i < $scope.orderItemArray.length; i++) {
                        if (!$scope.notEditable[$scope.orderItemArray[i].orderDetails.itemId]) {
                            AppUtil.myAlert("Please enter valid card number(s).");
                            return false;
                        }
                    }
                }
                $scope.$parent.disableOrderCheckBtnGift = true;
                productService.metaDataList.addAttribute("CHAAYOS_SELECT_PURCHASED", moment().format());
                var subscriptionOrder = {
                    offer:$scope.offerPercentage,
                    validTill:$scope.subscriptionEndDate
                }
                // socketUtils.emitMessage({SUBSCRIPTION_PURCHASED:subscriptionOrder})
                $rootScope.isItemPurchaseSelect= true;
                $scope.checkOrder ();
            };

            $scope.close = function (confirmRequired) {
                $scope.clearOnChaayosSelectModalClose(closeModal, false);
                AppUtil.setCardType('GIFT');
            };

            function closeModal() {
                $modalInstance.close('cancel');
                $scope.$parent.disableOrderCheckBtnGift = false;
            }

            $rootScope.closeChaayosSelectModal = function () {
                $scope.clearOnChaayosSelectModalClose(closeModal, false);
                AppUtil.setCardType('GIFT');
            };


            $scope.updateGiftCardCode = function (orderItem, cardNumber) {
                cardNumber = cardNumber.replace(/[^a-zA-Z0-9]/g, "");
                card.cardNumber = cardNumber;
                if (card.cardNumber.length == 6) {
                    if (!$scope.isDuplicateCard(card)) {
                        var gCards = [];
                        var configData = AppUtil.getAutoConfigData();

                        card.itemId = orderItem.itemId;
                        card.buyerId = AppUtil.customerSocket.id;
                        card.empId = $rootScope.globals.currentUser.userId;
                        card.unitId = configData.unitId;
                        card.terminalId = configData.selectedTerminalId;
                        card.type = AppUtil.cardType;
                        card.isValid = false;
                        card.error = "";
                        card.productName = orderItem.productName;
                        card.cardValue = orderItem.price;

                        gCards.push(card);

                        posAPI.allUrl('/', AppUtil.restUrls.order.validateGiftCardsInOrder).post(gCards).then(function (response) {
                            if (response.errorType != undefined && response.errorType != '') {
                                var msg = /*(response.errorType != undefined) ? */"Error validating gift cards!" + response.errorMessage;
                                AppUtil.myAlert(msg);
                                return false;
                            }
                            var dataObj = response.plain();
                            if (dataObj != null) {
                                var valid = true;
                                dataObj.map(function (cardObj) {
                                    if (valid && !cardObj.isValid) {
                                        valid = false;
                                    }
                                });
                                if (!valid) {
                                    AppUtil.myAlert("Please fill the correct gift card codes.");
                                    $scope.notEditable[orderItem.itemId] = false;
                                    $scope.$parent.resetGiftCardCode(card);
                                } else {
                                    $scope.$parent.setGiftCardCode(card);
                                    $scope.notEditable[orderItem.itemId] = true;
                                    orderItem.isCardValid = true;
                                    giftCardNumberList.push(card);
                                    card = {};
                                }
                            } else {
                                var msg = (dataObj.errorType == undefined) ? "Error validating gift cards!" : dataObj.errorMessage;
                                AppUtil.myAlert(msg);
                                $rootScope.showFullScreenLoader = false;
                            }
                        }, function (err) {
                            AppUtil.myAlert(err.data.errorMessage);
                        });
                    } else {
                        AppUtil.myAlert("Card already added!");
                        card = {};
                    }
                }
            };


            $scope.deleteCardEntry = function (cardNumber, itemId) {
                for (var i = 0; i < giftCardNumberList.length; i++) {
                    if (giftCardNumberList[i].cardNumber == cardNumber) {
                        giftCardNumberList.splice(i, 1);
                    }
                }
                $scope.notEditable[itemId] = false;
               $rootScope.isChaayosSelectChoosen=false;

            };

            $scope.isDuplicateCard = function (card) {
                var found = false;
                for (var i = 0; i < giftCardNumberList.length; i++) {
                    if (giftCardNumberList[i].cardNumber.length > 0) {
                        if (giftCardNumberList[i].cardNumber == card.cardNumber) {
                            found = true;
                            break;
                        }
                    }
                }
                return found;
            };

            $scope.showOfferPrompt = function (){
                if(!AppUtil.isCOD() && !AppUtil.isPaidEmployeeMeal() && $rootScope.orderType == "order" && $scope.orderWithoutWallet !=undefined && $scope.orderWithoutWallet.length >=2 && !$scope.offerApplied){
                    var offerMessage = null;
                    offerMessage = "<div style='width: 100%;background: #F0F0F0;border-radius: 30px;box-shadow: -16px -8px 7px rgba(0, 0, 0, 0.01), -9px -5px 6px rgba(0, 0, 0, 0.05), -4px -2px 4px rgba(0, 0, 0, 0.09), -1px -1px 2px rgba(0, 0, 0, 0.1), 0px 0px 0px rgba(0, 0, 0, 0.1);padding:5%'><span style='font-size:large; margin-left: 15%'><b>AVAILABLE OFFERS</b></span></div>";

                    var bakeryProducts = false;
                    var merchandiseProducts = false;
                    for(var i = 0; i<$scope.orderWithoutWallet.length;i++){
                        if($scope.orderWithoutWallet[i].orderDetails!=undefined && $scope.orderWithoutWallet[i].orderDetails!=null && $scope.orderWithoutWallet[i].orderDetails.productType!=undefined && $scope.orderWithoutWallet[i].orderDetails.productType!=null && ($scope.orderWithoutWallet[i].orderDetails.productType==9 || $scope.orderWithoutWallet[i].orderDetails.productType==10) ){
                            $scope.orderWithoutWallet[i].orderDetails.productType==9 ? (merchandiseProducts=true):(bakeryProducts=true);
                        }

                    }

                    if(!merchandiseProducts && $scope.totalAmount !=undefined &&  $scope.totalAmount !=undefined >198){
                        offerMessage = offerMessage +  "<br><br><span style='font-size:large;color:#4A8132'><b>SNACK30 Coupon Applicable</b></span><span style='margin-left:18%;font-size:large;color:#4A8132'><b></b></span>";
                    }
                    if(!bakeryProducts && $scope.totalAmount !=undefined &&  $scope.totalAmount !=undefined >350){
                        offerMessage = offerMessage +  "<br><br><span style='font-size:large;color:#4A8132'><b>BAKERY30 Coupon Applicable</b></span><span style='margin-left:18%;font-size:large;color:#4A8132'><b></b></span>";
                    }
                    if($scope.totalAmount !=undefined && $scope.totalAmount >198){
                        var dialog = bootbox.dialog({
                            className:"alertBox2",
                            closeButton: true,
                            onEscape: true,
                            message: offerMessage,
                            size: "small",
                            backdrop:true
                        });
                    }

                }
            }

            $scope.isChaayosSelectPurchased= function(){
                if($rootScope.isChaayosSelectChoosen===false){
                    $rootScope.isChaayosSelectChoosen=true;
                    return true;
                }
                 return false;
            };

            $scope.getProductAliasNameIfPresent = function(name,aliasName){
                 if(aliasName !== undefined && aliasName != null){
                     return aliasName;
                 }
                 return name;
             };


        }]);
