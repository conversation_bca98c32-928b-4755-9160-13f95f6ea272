/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';
/*jshint sub:true*/

angular.module('posApp')
    .controller('InventoryController', ['$rootScope','$scope','$filter','AppUtil','$location','$http','$compile','posAPI', '$modal',
        function ($rootScope,$scope,$filter,AppUtil,$location,$http,$compile,posAPI, $modal) {

	    	$scope.init= function(){
	            AppUtil.validate();
	    		$scope.unitFamily = $rootScope.globals.currentUser.unitFamily;
	    		$scope.outletList = [];
	    		$scope.actionLabel = null;
	    		$scope.inventoryData = [];
	    		$scope.showWastageBtn = true;
	    		$scope.showStockinBtn = true;
	    		$scope.showTransferOutBtn = true;
	    		$scope.showUpdateInvBtn = true;
	    		if($scope.unitFamily=="COD"){
	    			$scope.showEditInventory = false;
	    			fetchOutlets("CAFE");
	    			fetchOutlets("DELIVERY");
	    		}else{
	    			$scope.showEditInventory = true;
	                callAndUpdateData();
	    		}
	            $scope.showUpdateBtn = false;
	            
	            
	            $scope.inventoryGrid = $scope.getInventoryGrid();
	            $scope.changingQuantity = {};
	            $scope.changingExpireQuantity = {};
	            $scope.isLiveInventoryEnabled = AppUtil.hasLiveInventory();
	    	}
    		
	    	$scope.getInventoryGrid =  function(){
	    		return {
	        			enableSorting: true,
	        			enableColumnMenus: false,
	                    saveFocus: false,
	                    enableRowSelection: true,
	                    enableFiltering: true,
	                    saveScroll: false,
	                    enableSelectAll: true,
	                    multiSelect: true,
	                    enableColumnResizing : true,
	        		    onRegisterApi: function( gridApi ) {
	        		      $scope.grid2Api = gridApi;
	        		    },
	        			columnDefs : [
	        			     {name:"product", field:"productName",enableCellEdit:false},
		            	     {name:"expireQuantity", field:"expireQuantity", type: 'number', enableCellEdit: false},
		            	     {name:"quantity", field:"quantity", type: 'number', enableCellEdit: false},
		            	     {name:"lastUpdatedTime", field:"lastUpdatedTime",  cellFilter: 'date:"d/M/yy h:mm a"', enableCellEdit: false},
		            	     /*{name:"stockOut", visible : $scope.showEditInventory, enableCellEdit: false, cellTemplate: '<button class="btn btn-danger btn-xs" ng-click="grid.appScope.stokcOutHandler(row.entity)">Stock out</button>'},*/
		            	     {name:"productId", field:"productId", visible:false, enableCellEdit: false}
	        			],
	        			data : []
	        	};
	    	}
	    	
	    	
            $scope.backToCover = AppUtil.backToCover;
            
            $scope.loadInventory = function(){
            	if($scope.unitId == null){
            		AppUtil.myAlert("Please select unit!");
            	}else{
            		callAndUpdateData();
            	}
            }
            
            /*$scope.stokcOutHandler = function($rowEntity){
            	if(!$scope.showUpdateBtn){
            		var uniqueId = $rowEntity.uniqueId;
                	var productId = $rowEntity.productId;
                	var stockoutInventoryUpdate = {
               			 unitId:$cookieStore.get('globals').currentUser.unitId,
            			 productId:productId,
            			 updatedBy: $cookieStore.get('globals').currentUser.userId
            			 };
                	var requestObj = AppUtil.GetRequest(stockoutInventoryUpdate);
                	posAPI.allUrl('/',"pos-metadata").all("unit").all("inventory").all("stockout")
        			.post(requestObj).then(function(response) {
        				if(response){    					
        	            	$scope.inventoryData = updateQuantity($scope.inventoryData, productId, 0);
        	            	$scope.inventoryGrid.data = getTableArrayFromInventory($scope.inventoryData);
        				}else{
        					AppUtil.myAlert("Error occured. Try again!");
        				}
        			}, function(err) {
        				AppUtil.myAlert(err.data.errorMessage);
        			});
            	}            	
            };*/
            
            $scope.editInventory = function(editType){
        		$scope.inventoryEventType = editType;
        		$scope.actionLabel = null;
            	if(editType=='STOCK_IN'){
            	    	$scope.actionLabel = "Stock In";
            		$scope.showWastageBtn = false;
            		$scope.showTransferOutBtn = false;
            		$scope.showUpdateInvBtn = false;
            		//$scope.inventoryGrid.columnDefs.splice(2, 0, { name: 'updateExpiryQuantity', cellTemplate: '<input type="number=0" class="margin-5" ng-model="grid.appScope.changingExpireQuantity[row.entity.productId]" ng-change="grid.appScope.updateExpireQuantity(row.entity)"></input>' });
            		$scope.inventoryGrid.columnDefs.splice(3, 0, { name: 'stockInQuantity', cellTemplate: '<input type="number=0" ng-model="grid.appScope.changingQuantity[row.entity.productId]" ng-change="grid.appScope.updateQuantity(row.entity)"></input>' });
            	}else if(editType=='WASTAGE'){
        	    	$scope.actionLabel = "Wastage";
            		$scope.showStockinBtn = false;
            		$scope.showTransferOutBtn = false;
            		$scope.showUpdateInvBtn = false;
            		$scope.inventoryGrid.columnDefs.splice(3, 0, { name: 'wastageQuantity', cellTemplate: '<input type="number=0" class="margin-5" ng-model="grid.appScope.changingQuantity[row.entity.productId]" ng-change="grid.appScope.updateQuantity(row.entity)"></input>' });
            	}else if(editType=='TRANSFER_OUT'){
        	    	$scope.actionLabel = "Transfer Out";
            		$scope.showStockinBtn = false;
            		$scope.showWastageBtn = false;
            		$scope.showUpdateInvBtn = false;
            		$scope.inventoryGrid.columnDefs.splice(3, 0, { name: 'transferOutQuantity', cellTemplate: '<input type="number=0" class="margin-5" ng-model="grid.appScope.changingQuantity[row.entity.productId]" ng-change="grid.appScope.updateQuantity(row.entity)"></input>' });
            	}else if(editType=='UPDATE'){
        	    	$scope.actionLabel = "Update Stock";
            		$scope.showStockinBtn = false;
            		$scope.showWastageBtn = false;
            		$scope.showTransferOutBtn = false;
            		//$scope.inventoryGrid.columnDefs.splice(2, 0, { name: 'updateExpiryQuantity', cellTemplate: '<input type="number=0" class="margin-5" ng-model="grid.appScope.changingExpireQuantity[row.entity.productId]" ng-change="grid.appScope.updateExpireQuantity(row.entity)"></input>' });
            		$scope.inventoryGrid.columnDefs.splice(3, 0, { name: 'updateQuantity', cellTemplate: '<input type="number=0" class="margin-5" ng-model="grid.appScope.changingQuantity[row.entity.productId]" ng-change="grid.appScope.updateQuantity(row.entity)"></input>' });
            	}
            	$scope.updateInventoryList = [];
            	$scope.showUpdateBtn = true;
            }
            
            $scope.updateQuantity = function($rowEntity){
            	if($scope.changingQuantity[$rowEntity.productId] != null && !angular.isUndefined($scope.changingQuantity[$rowEntity.productId]) && ($scope.changingQuantity[$rowEntity.productId]>=0)){
            		updateInventoryList(
            				{
            					unitId : $scope.inventoryData[0].unit.id,
            				    productId : $rowEntity.productId,
            				    quantity : $scope.changingQuantity[$rowEntity.productId] != null ? $scope.changingQuantity[$rowEntity.productId] : $rowEntity.quantity,
            				    expireQuantity : $scope.changingExpireQuantity[$rowEntity.productId] != null ? $scope.changingExpireQuantity[$rowEntity.productId] : $rowEntity.expireQuantity,
            				    thresholdQuantity : $rowEntity.thresholdQuantity,
            				    type : $scope.inventoryEventType
            				});
            	}            	
            }
            $scope.updateExpireQuantity = function($rowEntity){
            	if($scope.changingExpireQuantity[$rowEntity.productId] != null && !angular.isUndefined($scope.changingExpireQuantity[$rowEntity.productId]) && ($scope.changingExpireQuantity[$rowEntity.productId]>=0)){
            		updateInventoryList(
            				{
            					unitId : $scope.inventoryData[0].unit.id,
            				    productId : $rowEntity.productId,
            				    quantity : $scope.changingQuantity[$rowEntity.productId] != null ? $scope.changingQuantity[$rowEntity.productId] : $rowEntity.quantity,
            				    expireQuantity : $scope.changingExpireQuantity[$rowEntity.productId] != null ? $scope.changingExpireQuantity[$rowEntity.productId] : $rowEntity.expireQuantity,
            				    thresholdQuantity : $rowEntity.thresholdQuantity,
            				    type : $scope.inventoryEventType
            				});
            	}            	
            }
            
            $scope.updateInventory = function(){
            	var req = {
            			unitId : $rootScope.globals.currentUser.unitId,
            			updatedBy: $rootScope.globals.currentUser.userId,
            			businessDate: $scope.businessDate,
            			updateTime: new Date().getTime(),
            			comment : $scope.inventoryUpdateComment,
            			type : $scope.inventoryEventType,
            			currentInventory : getCurrentUpatedInventory(),
            			updatedInventory : $scope.updateInventoryList
            	}
            	//console.log(req);
            	//var requestObj =  AppUtil.GetRequest(req);
            	posAPI.allUrl('/',AppUtil.restUrls.posMetaData.updateInventory)
    			.post(req).then(function(response) {
    				if(response){
    					callAndUpdateData();
    					$scope.cancelUpdateInventory();
                    	$scope.showUpdateBtn = false;
                    	$scope.changingQuantity = {};
                    	$scope.changingExpireQuantity = {};      
    				}else{
    					AppUtil.myAlert("Error occured. Try again!");
    				}
            		$scope.showWastageBtn = true;
            		$scope.showStockinBtn = true;
            		$scope.showTransferOutBtn = true;
            		$scope.showUpdateInvBtn = true;
    			}, function(err) {
    				AppUtil.myAlert(err.data.errorMessage);
    			});
            }
            
            $scope.cancelUpdateInventory = function(){
            	
            	var editType = $scope.inventoryEventType

            	if(editType=='STOCK_IN'){
            		$scope.inventoryGrid.columnDefs.splice(3, 1);
	        		//$scope.inventoryGrid.columnDefs.splice(2, 1);
	        	}else if(editType=='WASTAGE'){
	        		$scope.inventoryGrid.columnDefs.splice(3, 1);
	        	}else if(editType=='TRANSFER_OUT'){
	        		$scope.inventoryGrid.columnDefs.splice(3, 1);
	        	}else if(editType=='UPDATE'){
	        		$scope.inventoryGrid.columnDefs.splice(3, 1);
	        		//$scope.inventoryGrid.columnDefs.splice(2, 1);
	        	}
	            	
            	$scope.showUpdateBtn = false;
            	$scope.changingQuantity = {};
            	$scope.changingExpireQuantity = {};
        		$scope.showWastageBtn = true;
        		$scope.showStockinBtn = true;
        		$scope.showTransferOutBtn = true;
        		$scope.showUpdateInvBtn = true;
            }
            
            $scope.askInventoryComent = function(){
            	var modalInstance = $modal.open({
                    animation: true,
                    templateUrl: window.version+'scripts/modules/modals/inventoryUpdateCommentModal.html',
                    controller: 'inventoryUpdateCommentModalInstance',
                    backdrop: 'static',
    				scope: $scope,
                    size: 'lg'
                });
            	modalInstance.result.then(function (retObj) {
            		$scope.inventoryUpdateComment = retObj.comment;
            		$scope.businessDate = retObj.businessDate;
            		$scope.updateInventory();
            	}, function () {
            		$scope.cancelUpdateInventory();
            	});
            }
            
            function callAndUpdateData(){
            	var unitId = null;
            	if($scope.unitFamily=="COD"){
					unitId = $scope.unitId.id;
            	}else{
					unitId = $rootScope.globals.currentUser.unitId;
            	}
    	    	posAPI.allUrl('/',AppUtil.restUrls.posMetaData.inventory)
    			.post(unitId).then(function(response) {
    				$scope.inventoryData = response;
    				$scope.inventoryGrid.data = getTableArrayFromInventory($scope.inventoryData);
    			}, function(err) {
    				AppUtil.myAlert(err.data.errorMessage);
    			});
            }

    		function getCurrentUpatedInventory(){
    			var currentList = [];
    			$scope.inventoryData.forEach(function(val){
    				$scope.updateInventoryList.forEach(function(v){
                		if(val.product.detail.id==v.productId){
                			if($scope.inventoryEventType=='STOCK_IN'){
                    			//console.log(val.quantity+":"+v.quantity);            				
                				val.quantity = parseInt(val.quantity)+parseInt(v.quantity);
                				val.expireQuantity = parseInt(val.expireQuantity)+parseInt(v.expireQuantity)
                			}else if($scope.inventoryEventType=='WASTAGE' || $scope.inventoryEventType=='TRANSFER_OUT'){
                    			//console.log(val.quantity+":"+v.quantity);  
                				val.quantity = parseInt(val.quantity)-parseInt(v.quantity);
                				val.expireQuantity = parseInt(val.expireQuantity)-parseInt(v.expireQuantity);
                			}else if($scope.inventoryEventType=='UPDATE'){
                    			//console.log(val.quantity+":"+v.quantity);  
                				val.quantity = v.quantity;
                				val.expireQuantity = v.expireQuantity;
                			}
                		}
                	});
    				currentList.push(val);
    			});
    			
        		return currentList;
    		}
    		
    		function fetchOutlets(code) {
                posAPI.allUrl('/',AppUtil.restUrls.unitMetaData.activeUnits).customGET("", {category: code})
                    .then(function (response) {
                    	if($scope.outletList.length==0){
                    		$scope.outletList = response.plain();
                    	}else{
                    		angular.forEach(response.plain(), function(v){
                    			$scope.outletList.push(v);
                    		});
                    	} 
                		convertOutletIdToNumber();
                }, function (err) {
                    AppUtil.myAlert(err.data.errorMessage);
                });
            }
    		
    		function convertOutletIdToNumber(){
    			var newOutletList = new Array();
    			angular.forEach($scope.outletList, function(v){
    				var obj = v
    				obj.id = parseInt(v.id);
    				newOutletList.push(obj);
    			});
    			$scope.outletList = newOutletList;
    		}
    		
    		function updateInventoryList(item){
    			var newList = [];
    			var found = false;
    			$scope.updateInventoryList.forEach(function(v){
    				if(v.productId==item.productId){
    					v.quantity = item.quantity;
    					v.expireQuantity = item.expireQuantity;
    					found = true;
    				}
    				newList.push(v);
    			});
    			if(!found){
    				newList.push(item);
    			}
    			$scope.updateInventoryList = newList;
    		}

        }]);

		function getTableArrayFromInventory(inventoryArr){
			var data = new Array();
			angular.forEach(inventoryArr, function(val){
				var productName = val.product.detail.name;
				var productId = val.product.detail.id;
				var lastUpdatedTime = val.lastUpdatedTime==null ? "NA" : val.lastUpdatedTime;
				var quantity = val.quantity;
				var thresholdQuantity = val.thresholdData==null ? -1 : val.thresholdData.avgQuantity;
				var expireQuantity = val.expireQuantity;
				var row = {productName:productName,
						expireQuantity:expireQuantity,
						quantity:quantity,
						thresholdQuantity:thresholdQuantity,
						lastUpdatedTime:lastUpdatedTime,
						productId:productId};
				data.push(row);
			});
			return data;
		}
		
		function getUpdateInventryItem(unitId,productId,item,thresholdQuantity,quantity,eventType){
			var itemObj = {
					unitId : unitId,
				    productId : productId,
				    quantity : quantity,
				    thresholdQuantity : thresholdQuantity,
				    type : eventType
			}
			return itemObj;
		}


		angular.module('posApp').controller('inventoryUpdateCommentModalInstance', ['$scope', '$modalInstance', function ($scope, $modalInstance) {
			$scope.init = function(){
				$scope.inventoryUpdateComment = "";
				$scope.today = new Date(new Date().setDate(new Date().getDate() + 1)).toString();
				$scope.businessDateError = false;
			}
			$scope.ok = function () {
				if($scope.day != null){
					$modalInstance.close({
						businessDate : ""+$scope.day,
						comment : $scope.inventoryUpdateComment
					});
				}else{
					$scope.businessDateError = true;
				}
			};
			$scope.cancel = function () {
				$modalInstance.dismiss("cancel");
			};
		}]);