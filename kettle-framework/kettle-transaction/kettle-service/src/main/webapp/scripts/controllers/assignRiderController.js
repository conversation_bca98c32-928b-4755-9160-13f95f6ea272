/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

angular
    .module('posApp')
    .controller(
        'assignRiderController',
        ['$rootScope', '$scope', '$modalInstance', 'AppUtil', 'posAPI', '$location', 'order',
            function ($rootScope, $scope, $modalInstance, AppUtil, posAPI, $location, order) {
        	$scope.isSubmitDisabled = false;
        	$scope.lastDeliveryBoyPhoneNum={};
            $scope.init = function () {
                $scope.order = order;
                $rootScope.showFullScreenLoader = true;
                posAPI.allUrl('/',AppUtil.restUrls.userManagement.activeRider).post($scope.$parent.unitDetails.id)
                    .then(
                        function (response) {
                            $scope.employeeDetails = response.plain();
                            $scope.employeeDetails.forEach(function (item) {
                                if ($scope.order.deliveryDetails != null
                                    && $scope.order.deliveryDetails.deliveryBoyId == item.id) {
                                    item.selected = true;
                                    $scope.lastDeliveryBoyPhoneNum[$scope.order.order.orderId] = item.sdpContact;
                                } else {
                                    item.selected = false;
                                }
                            });
                            $rootScope.showFullScreenLoader = false;
                        }, function (err) {
                            $rootScope.showFullScreenLoader = false;
                            AppUtil.myAlert(err.data.errorMessage);
                        });
            };

            $scope.selectedRider = function (i, list,data) {
            	if(data.sdpContact== null || data.sdpContact== ""){
            		bootbox.alert("Please update sdp contact details after that SDP "+data.name+" will be available for delivery");
            		return false;
            	}
                for (var index in list) {
                    if (index == i) {
                        list[index].selected = true;
                    } else {
                        list[index].selected = false;
                    }
                }
            };

            $scope.submit = function () {
            	$scope.isSubmitDisabled = true;
                var selectedRiderDetail = null;
                $scope.employeeDetails.forEach(function (item) {
                    if (item.selected) {
                        selectedRiderDetail = item;
                    }
                });
                
                if (selectedRiderDetail == null) {
                	$scope.isSubmitDisabled = false;
                    return;
                } else {
                    var request = {};
                    request.deliveryPartnerId = $scope.order.order.deliveryPartner;
                    request.unitId = $scope.order.order.unitId;
                    request.orderId = $scope.order.order.orderId;
                    request.generatedOrderId = $scope.order.order.generateOrderId;
                    request.deliveryTaskId = $scope.order.order.generateOrderId;
                    request.deliveryDetailId = $scope.order.deliveryDetails.deliveryDetailId;
                    request.deliveryStatus = $scope.order.deliveryDetails.deliveryDetailId != null ? $scope.order.deliveryDetails.deliveryStatus
                        : 1;
                    request.deliveryBoyId = selectedRiderDetail.id;
                    request.deliveryBoyName = selectedRiderDetail.name;
                    request.deliveryBoyPhoneNum = (selectedRiderDetail.sdpContact !== null) ? selectedRiderDetail.sdpContact : selectedRiderDetail.contactNumber;
                    if(request.deliveryBoyPhoneNum == $scope.lastDeliveryBoyPhoneNum[$scope.order.order.orderId]){
                    	bootbox.alert("SDP " + selectedRiderDetail.name + " has been already assigned to you for delivery");
                    	$scope.isSubmitDisabled = false;
                    	 return false;
                	}
                    var reqObj = AppUtil.GetRequest(request);
                    $rootScope.showFullScreenLoader = true;
                    posAPI.allUrl('/',AppUtil.restUrls.delivery.manualDelivery).post(reqObj).then(function (response) {
                  //  	console.log("response",response);
                    	if(response.failureCode != undefined || response.failureCode != null){
                    		$scope.employeeDetails.forEach(function (item) {
                                if (item.sdpContact == selectedRiderDetail.sdpContact) {
                                	item.selected = false;  
                                }
                                if(item.sdpContact == $scope.lastDeliveryBoyPhoneNum[$scope.order.order.orderId]){
                                	item.selected = true; 
                                }
                            });
                    		bootbox.alert(response.failureMessage);
                        }else{
                        	bootbox.alert("Assigned " + selectedRiderDetail.name + " for delivery");
                            $modalInstance.close();
                            $scope.lastDeliveryBoyPhoneNum[$scope.order.order.orderId]=response.deliveryBoyPhoneNum;
                        }
                    	$scope.isSubmitDisabled = false;
                        $rootScope.showFullScreenLoader = false;
                        
                    }, function (err) {
                    	$scope.isSubmitDisabled = false;
                        $rootScope.showFullScreenLoader = false;
                        bootbox.alert("Failed to assign " + selectedRiderDetail.name + " for delivery");
                    });


                }

            };

            $scope.close = function () {
                $modalInstance.close();
            };
        }]);
