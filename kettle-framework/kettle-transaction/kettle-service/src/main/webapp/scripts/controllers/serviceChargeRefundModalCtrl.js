/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

angular.module('posApp').controller('serviceChargeRefundModalCtrl',
    ['$scope', 'AuthenticationService', 'posAPI', 'AppUtil', '$location', '$modal', '$modalInstance', '$rootScope', '$window', '$http',
    function ($scope, AuthenticationService, posAPI, AppUtil, $location, $modal, $modalInstance, $rootScope, $window, $http) {

        $scope.initServiceChargeRefund = function() {
            $scope.totalServiceChargeRefundAmount = Math.ceil(
                $scope.OrderObj.transactionDetail.serviceCharge +
                $scope.OrderObj.transactionDetail.serviceTaxAmount
            );
            $scope.isOtpVerificationRequired = $scope.OrderObj.orderRefundDetailId == null ? true : false;
            $scope.currentUser = $rootScope.globals.currentUser;
            $scope.serviceChargeRefund = {
                comment : null
            };
            $scope.serviceChargeRefundComments = [
                "Customer want refund",
                "Service not good"
            ];
            $scope.showSendOtp = false;
            $scope.showVerifyOtp = false;
            $scope.contactNumber = null;
            getExcludeCustomersList();
        }

        function getExcludeCustomersList() {
            $http({
                method: 'GET',
                url: AppUtil.restUrls.forms.getExcludeCustomers
            }).then(function success(response) {
                if(response != null) {
                     $scope.excludeCustomersList = response.data;
                }
            }, function error(response) {
                    console.log("Error: ", response.data);
                   AppUtil.myAlert(response.data.errorMessage);
            })
        }

        $scope.showCustomerOTP = function() {
            if($scope.excludeCustomersList.includes($scope.OrderObj.customerId)) {
                return false;
            }
            return true;
        }

        $scope.commentSelected = function(comment) {
            $scope.serviceChargeRefund.comment = comment;
        }

        $scope.gettingAllEmployeesDetails = function() {
            posAPI.allUrl('/',AppUtil.restUrls.userManagement.empMealUsers)
                .post($scope.$parent.unitDetails.id)
                .then(
                    function(response) {
                        $scope.employeeDetails = response;
                    }, function(err) {
                        AppUtil.myAlert(err.data.errorMessage);
                    }
                );
        }

        $scope.getEmployeeData = function(emp) {
            $scope.selectedEmployee = null;
            posAPI.allUrl('/',AppUtil.restUrls.order.empAllowancelimit)
                .post(emp.id)
                .then(
                    function(response) {
                        $scope.selectedEmployee = emp;
                        $scope.contactNumber = $scope.selectedEmployee.contactNumber;
                        $scope.showSendOtp = true;
                    }, function(err) {
                        AppUtil.myAlert(err.data.errorMessage);
                    }
                );
        };

        $scope.selectedForOtpVerification = function(type) {
            if($scope.serviceChargeRefund.comment == null) {
                AppUtil.myAlert("Please select service charge refund comment to proceed !");
                return;
            }
            $scope.showVerifyOtp = false;
            $scope.otpVerificationType = type;
            if(type == 'CUSTOMER') {
                $scope.otpVerificationForCustomer();
            } else {
                $scope.gettingAllEmployeesDetails();
            }
        }

        $scope.otpVerificationForCustomer = function() {
            $http({
                method: 'GET',
                url: AppUtil.restUrls.dineInResources.getCustomerInfo,
                params: {customerId : $scope.OrderObj.customerId}
            }).then(function success(response) {
                if(response != null) {
                    $scope.customerSelected = response.data;
                    $scope.contactNumber = $scope.customerSelected.contactNumber;
                    $scope.showSendOtp = true;
                } else {
                    AppUtil.myAlert("Unable to find customer contact number");
                }
            }, function error(response) {
                   AppUtil.myAlert(response.data.errorMessage);
            })
        }

        $scope.sendOTPForServiceChargeRefund = function() {
            posAPI.allUrl('/',AppUtil.restUrls.customer.generateOTP).post({
                contactNumber : $scope.contactNumber
            }).then(function(response) {
                if (response) {
                    $scope.showSendOtp = false;
                    $scope.showVerifyOtp = true;
                } else {
                    AppUtil.myAlert("Could not send OTP. Try again.");
                }
            }, function(err) {
                AppUtil.myAlert(err.data.errorMessage);
            });
        }

        $scope.verifyOTPForServiceChargeRefund = function(otp) {
            console.log("OTP : ", otp);
            posAPI.allUrl('/',AppUtil.restUrls.customer.verifyOTP).post({
                contactNumber : $scope.contactNumber,
                otpPin : otp,
                unit : AppUtil.getUnitDetails().id
            }).then(function(response) {
                if (response == true) {
                    $scope.isOtpVerificationRequired = false;
                } else {
                    AppUtil.myAlert("Incorrect OTP. Please Try again.");
                }
            }, function(err) {
                AppUtil.myAlert(err.data.errorMessage);
            });
        };

        $scope.createServiceChargeRefund = function() {
            var orderRefundClaimVo = {
                requestedBy: {
                    id : $scope.currentUser.userId,
                    name : $scope.currentUser.userName
                },
                orderRefundVouchers : []
            }
            var orderRefund = {
                orderId: $scope.OrderObj.orderId,
                refundReason: $scope.serviceChargeRefund.comment,
                otpVerifiedBy: $scope.otpVerificationType,
                otpVerifiedContact: $scope.contactNumber
            }
            orderRefundClaimVo.orderRefundVouchers.push(orderRefund);
            $http({
                method: 'POST',
                url: AppUtil.restUrls.forms.refundOrder,
                data : orderRefundClaimVo
            }).then(function success(response) {
                if(response.data) {
                    $scope.cancelProcess();
                } else {
                    AppUtil.myAlert("Error while processing service charge refund");
                }
            }, function error(response) {
                    console.log("Error : ", response);
                   AppUtil.myAlert("Error while processing service charge refund");
            });
        };

        $scope.cancelProcess = function() {
            $modalInstance.close();
        }

    }
  ]
);
