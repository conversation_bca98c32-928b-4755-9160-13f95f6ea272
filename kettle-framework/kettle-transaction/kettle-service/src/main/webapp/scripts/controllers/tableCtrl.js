/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';
/* jshint sub:true */

angular.module('posApp').controller(
		'tableCtrl',
		[
				'$rootScope',
				'$scope',
				'$filter',
				'AppUtil',
				'$location',
				'$http',
				'$compile',
				'posAPI',
				'$modal',
				'tableService',
				function($rootScope, $scope, $filter, AppUtil, $location,
						$http, $compile, posAPI, $modal, tableService) {

					$scope.init = function() {
						$scope.tables = [];
						$scope.currentTable = tableService.getCurrentTable();
						$scope.currentTableSummary = null;
						$scope.unitDetails = AppUtil.getUnitDetails();
						$scope.getTableStatus($scope.unitDetails.id);
						/*if (!AppUtil.isEmptyObject($scope.currentTable)) {
							$scope.openTable($scope.currentTable);
						}*/
					};

					$scope.getTableStatus = function(param) {
						var url = AppUtil.restUrls.order.getUnitTables;
						$scope.loadingData = true;
						posAPI.allUrl('/', url).customGET("", {
							unitId : param
						}).then(function(response) {
							$scope.tables = response;
							$scope.loadingData = false;
						}, function(err) {
							AppUtil.myAlert(err.data.errorMessage);
						});
					};

					$scope.openTable = function(table) {
						$scope.setTable(table);
						$location.url('/tableSummary');
					};

					$scope.setTable = function(table) {
						tableService.setCurrentTable(table);
						$scope.currentTable = table;
					};

					$scope.backToCover = function() {
						tableService.setCurrentTable(null);
						$location.url('/cover');
					};

				} ]);