'use strict';
angular
    .module('posApp')
    .controller(
        'edcPayphiStatusCheckCtrl',
        ['$rootScope', '$scope', '$modalInstance', 'AppUtil', 'posAPI', '$location', '$timeout','initiateTransactionResponse',
            function($rootScope, $scope, $modalInstance, AppUtil, posAPI, $location, $timeout,initiateTransactionResponse) {

                $scope.init = function() {
                    bootbox.hideAll();
                    $scope.errorMessage = null;
                    $scope.intiateTransactionResponse = initiateTransactionResponse;
                };

                $scope.validateEdcPaymentStatus = function (){

                    if($scope.intiateTransactionResponse !=undefined && $scope.intiateTransactionResponse!=null && !AppUtil.isEmptyObject($scope.intiateTransactionResponse) && $scope.intiateTransactionResponse.payPhiEdcCreateResponse!=undefined && !AppUtil.isEmptyObject($scope.intiateTransactionResponse.payPhiEdcCreateResponse)){

                        var reqObj = {
                            "aggregatorId": $scope.intiateTransactionResponse.aggregatorId,
                            "posAppId": $scope.intiateTransactionResponse.posAppId,
                            "referenceNo": $scope.intiateTransactionResponse.payPhiEdcCreateResponse.referenceNo,
                            "invoiceNo":$scope.intiateTransactionResponse.payPhiEdcCreateResponse.invoiceNo,
                            "storeCode": $scope.intiateTransactionResponse.storeCode,
                            "secretKey" : $scope.intiateTransactionResponse.secretKey,
                            "amount":$scope.intiateTransactionResponse.chargeAmount,
                            "transactionType": "STATUS"
                        }

                        posAPI.allUrl('/', AppUtil.restUrls.edcPaymentManagement.checkPayphiStatus)
                            .post(reqObj)
                            .then(function (response) {

                                if(response !=undefined && response!=null && response.payPhiStatusResponse!=undefined && response.payPhiStatusResponse!=null){
                                    if(response.payPhiStatusResponse.responseCode !=undefined && response.payPhiStatusResponse.responseCode !=null && (response.payPhiStatusResponse.responseCode == "0000" || response.payPhiStatusResponse.responseCode == "000") && response.payPhiStatusResponse.txnResponseCode !=undefined &&response.payPhiStatusResponse.txnResponseCode !=null && (response.payPhiStatusResponse.txnResponseCode == "0000" || response.payPhiStatusResponse.txnResponseCode == "000")){
                                        // $scope.edcExternalTransactionId = response.body.merchantTransactionId;
                                        if(AppUtil.getEdcOrderMerchantId() == null){
                                            var last4digits = response.referenceNo >3? response.referenceNo.substring(response.referenceNo-4) :null;
                                            AppUtil.setEdcOrderMerchantId(last4digits);
                                        }else if(AppUtil.getEdcOrderMerchantId() != null && response.referenceNo >3){
                                            var setOrderId = AppUtil.getEdcOrderMerchantId()  +","+response.referenceNo.substring(response.referenceNo.length-4);
                                            AppUtil.setEdcOrderMerchantId(setOrderId);
                                        }
                                        $modalInstance.close(response);
                                    }
                                    else if(response.payPhiStatusResponse.respDescription !=undefined && response.payPhiStatusResponse.respDescription !=null){
                                        $scope.showErrorMessage("Payment Status ::"+ response.payPhiStatusResponse.respDescription );
                                    }
                                }

                            },function(error){
                                console.log("Error verifying payment Status",error);
                                bootbox.alert("Cannot verify payment Status");
                            });
                    }
                    else{
                        $scope.showErrorMessage("Please Initiate Transaction First");
                    }
                }

                $scope.showErrorMessage = function(text) {
                    $scope.errorMessage = text;
                    $timeout(function() {
                        $scope.errorMessage = null;
                    }, 4000)
                }

                // $scope.selectPaymentMode = function (mode){
                //     $modalInstance.close(mode);
                // }

                $scope.goBack = function() {
                    $modalInstance.close(null);
                };
            }]);
