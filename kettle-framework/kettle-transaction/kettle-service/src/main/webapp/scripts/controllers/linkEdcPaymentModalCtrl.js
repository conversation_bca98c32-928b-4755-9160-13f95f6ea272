/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';
angular
    .module('posApp')
    .controller(
        'linkEdcPaymentModalCtrl',
        ['$rootScope', '$scope', '$modalInstance', 'AppUtil', 'posAPI', '$location', '$timeout','edcExternalTransactionId',
            function($rootScope, $scope, $modalInstance, AppUtil, posAPI, $location, $timeout,edcExternalTransactionId) {

                $scope.init = function() {
                    bootbox.hideAll();
                    $scope.transactionId = null;
                    $scope.retryCount=0;
                    $scope.errorMessage = null;
                    $scope.unitEdcMappingList = AppUtil.getUnitEdcMappingDetails();
                    $scope.edcExternalTransactionId = edcExternalTransactionId
                };

                $scope.verifyTransactionId = function(transactionId){
                    if(transactionId==undefined){
                        $scope.showErrorMessage("Renter Complete Merchant Transaction Id");
                        return ;
                    }
                    if($scope.edcExternalTransactionId !=undefined && $scope.edcExternalTransactionId!=null && $scope.edcExternalTransactionId.length>3 && ($scope.edcExternalTransactionId[0] == transactionId || $scope.edcExternalTransactionId[1] == transactionId)){
                        $scope.showErrorMessage("Cannot enter initiated transaction Id " +transactionId + " to link Payment");
                        return ;
                    }
                    if( $scope.edcExternalTransactionId[2] ==transactionId || $scope.edcExternalTransactionId[3] == transactionId){
                        $scope.showErrorMessage("You have already Linked a  Edc Payment " + transactionId + " Settle Remaining Order Amount" );
                        return ;
                    }

                    var validateUrl = AppUtil.restUrls.edcPaymentManagement.validateTransactionId + '/' + transactionId;
                    posAPI.allUrl('/', validateUrl)
                        .customGET()
                        .then(function (response) {
                                if (response != undefined && response!=null && response.message!=undefined && response.message!=null) {
                                    $scope.showErrorMessage(response.message + ": " + transactionId);
                                }
                                else if(response != undefined && response!=null && response.paymentSource !=undefined && response.paymentSource !=null &&
                                    response.paymentSource == "KETTLE_SERVICE" && response.paymentStatus !=undefined && response.paymentStatus !=null &&
                                    response.paymentStatus == "ACCEPTED_SUCCESS" && response.transactionAmount !=undefined && response.transactionAmount !=null
                                    && response.transactionAmount > 0 && response.paymentModeId!=undefined && response.paymentModeId!=null && response.paymentModeId!=38) {

                                    //$modalInstance.close(response);
                                    var reqObj =  {
                                        "paytmMid":($scope.unitEdcMappingList[0].merchantId !=undefined && $scope.unitEdcMappingList[0].merchantId !=null) ? $scope.unitEdcMappingList[0].merchantId : "1",
                                        "paytmTid":($scope.unitEdcMappingList[0].tid !=undefined && $scope.unitEdcMappingList[0].tid !=null) ? $scope.unitEdcMappingList[0].tid : "1",
                                        "version" : ($scope.unitEdcMappingList[0].version !=undefined && $scope.unitEdcMappingList[0].version !=null) ? $scope.unitEdcMappingList[0].version : "3.1",
                                        "merchantKey" : ($scope.unitEdcMappingList[0].merchantKey !=undefined && $scope.unitEdcMappingList[0].merchantKey !=null) ? $scope.unitEdcMappingList[0].merchantKey : "NO_KEY",
                                        "merchantTransactionId": transactionId,
                                        "paytmEDCTransactionResponse": {
                                            "head": {
                                                "requestTimeStamp": moment().tz('Asia/Kolkata').format('YYYY-MM-DD HH:mm:ss'),
                                                "channelId": "EDC",
                                                "checksum": "",
                                                "version": ""
                                            },
                                            "body": {
                                                "paytmMid": $scope.unitEdcMappingList[0].merchantId,
                                                "paytmTid":$scope.unitEdcMappingList[0].merchantKey,
                                                "transactionDateTime": null,
                                                "merchantTransactionId": transactionId,
                                                "merchantReferenceNo": null,
                                                "transactionAmount": null,
                                                "acquirementId": null,
                                                "retrievalReferenceNo": null,
                                                "authCode": null,
                                                "issuerMaskCardNo": null,
                                                "issuingBankName": null,
                                                "bankResponseCode": null,
                                                "bankResponseMessage": null,
                                                "bankMid": null,
                                                "bankTid": null,
                                                "acquiringBank": null,
                                                "merchantExtendedInfo": null,
                                                "extendedInfo": null,
                                                "aid": null,
                                                "payMethod": null,
                                                "cardType": null,
                                                "cardScheme": null,
                                                "resultInfo": {
                                                    "resultStatus": "ACCEPTED_SUCCESS",
                                                    "resultCodeId": "0009",
                                                    "resultCode": "A",
                                                    "resultMsg": "ACCEPTED_SUCCESS"
                                                }
                                            }
                                        }
                                    }
                                    posAPI.allUrl('/', AppUtil.restUrls.edcPaymentManagement.checkEdcTransactionStatus)
                                        .post(reqObj)
                                        .then(function (res) {

                                            if(res !=undefined && res!=null && res.body !=undefined && res.body !=null && res.body.resultInfo!=undefined && res.body.resultInfo!=null &&
                                                res.body.resultInfo.resultCodeId !=undefined && res.body.resultInfo.resultCodeId !=null && res.body.resultInfo.resultCodeId == "0000"
                                                && res.body.merchantTransactionId !=undefined && res.body.merchantTransactionId !=null){
                                                    $modalInstance.close(response);
                                            }else{
                                                $scope.showErrorMessage("Payment not Done");
                                                $modalInstance.close();
                                            }
                                        },function(error){
                                            console.log("Error verifying payment Status",error);
                                            $scope.showErrorMessage("Cannot verify payment Status");
                                        });
                                }else if(response != undefined && response!=null && response.paymentSource !=undefined && response.paymentSource !=null &&
                                    response.paymentSource == "KETTLE_SERVICE" && response.paymentStatus !=undefined && response.paymentStatus !=null &&
                                    response.paymentStatus == "ACCEPTED_SUCCESS" && response.transactionAmount !=undefined && response.transactionAmount !=null
                                    && response.transactionAmount > 0 && response.paymentModeId!=undefined && response.paymentModeId!=null && response.paymentModeId==38){



                                    var reqObj = {
                                        "aggregatorId":($scope.unitEdcMappingList[0].merchantId !=undefined && $scope.unitEdcMappingList[0].merchantId !=null) ? $scope.unitEdcMappingList[0].merchantId : "1",
                                        "posAppId":($scope.unitEdcMappingList[0].tid !=undefined && $scope.unitEdcMappingList[0].tid !=null) ? $scope.unitEdcMappingList[0].tid : "1",
                                        "referenceNo": transactionId,
                                        "invoiceNo":transactionId,
                                        "storeCode" : ($scope.unitEdcMappingList[0].version !=undefined && $scope.unitEdcMappingList[0].version !=null) ? $scope.unitEdcMappingList[0].version : "3.1",
                                        "secretKey" : ($scope.unitEdcMappingList[0].secretKey !=undefined && $scope.unitEdcMappingList[0].secretKey !=null) ? $scope.unitEdcMappingList[0].secretKey : "NO_KEY",
                                        "amount":response.transactionAmount,
                                        "transactionType": "STATUS"
                                    }

                                    posAPI.allUrl('/', AppUtil.restUrls.edcPaymentManagement.checkPayphiStatus)
                                        .post(reqObj)
                                        .then(function (resp) {

                                            if(resp !=undefined && resp!=null && resp.payPhiStatusResponse!=undefined && resp.payPhiStatusResponse!=null){

                                                if(resp.payPhiStatusResponse.responseCode !=undefined && resp.payPhiStatusResponse.responseCode !=null && (resp.payPhiStatusResponse.responseCode == "0000" || resp.payPhiStatusResponse.responseCode == "000") && resp.payPhiStatusResponse.txnResponseCode !=undefined &&resp.payPhiStatusResponse.txnResponseCode !=null && (resp.payPhiStatusResponse.txnResponseCode == "0000" || resp.payPhiStatusResponse.txnResponseCode == "000")) {
                                                    $modalInstance.close(response);
                                                }else{
                                                    $scope.showErrorMessage("Payment not Done");
                                                    $modalInstance.close();
                                                }
                                            }

                                        },function(error){
                                            console.log("Error verifying payment Status",error);
                                            bootbox.alert("Cannot verify payment Status");
                                        });

                                }
                                else{
                                    $scope.showErrorMessage("Error fetching payment");
                                }

                            },
                            function (err) {
                                AppUtil.myAlert(err.data.errorMessage);
                            }
                        );
                    $scope.retryCount = $scope.retryCount+1;
                    if($scope.retryCount!=null && $scope.retryCount>2){
                        $modalInstance.close(null);
                    }
                }

                $scope.showErrorMessage = function(text) {
                    $scope.errorMessage = text;
                    $timeout(function() {
                        $scope.errorMessage = null;
                    }, 4000)
                }

                $scope.goBack = function() {

                    $modalInstance.close();
                };
            }]);
