/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

angular.module('posApp').controller('swiggyOrderViewCtrl',
    ['$location', '$scope', 'posAPI', 'AppUtil', '$modal', 'PrintService', '$timeout', '$rootScope', 'trackingService', 'desiChaiService',
        function ($location, $scope, posAPI, AppUtil, $modal, PrintService, $timeout, $rootScope, trackingService, desiChaiService) {
            //var channelPartnerId = 6;
            var channelPartnerId = AppUtil.getPartnerId();
            $scope.selectedUnit = "";
            $scope.selectedCity = "";
            $scope.customer = {};

            $scope.backToCODCover = function () {
                $location.url('/CODCover');
            };

            $scope.cityList = [];

            $scope.init = function () {
                AppUtil.activeChannelPartnerId = channelPartnerId;
                $rootScope.showFullScreenLoader = true;
                if(AppUtil.activeChannelPartnerId === 6) {
                    AppUtil.loadSwiggyCustomer().then(function(d) {$scope.setCustomerData(d)});
                }
                if(AppUtil.activeChannelPartnerId === 3) {
                    AppUtil.loadZomatoCustomer().then(function(d) {$scope.setCustomerData(d)});
                }
                if(AppUtil.activeChannelPartnerId === 24){
                    $rootScope.showFullScreenLoader = false;
                    //AppUtil.loadZomatoCustomer().then(function(d) {$scope.setCustomerData(d)});
                }
                if (AppUtil.defaultDeliveryDetail === undefined) {
                    getDefaultDeliveryPartners();
                }
                $scope.brandList = AppUtil.transactionMetadata.brandList;
                $scope.selectedBrand = AppUtil.getSelectedBrand();
                $scope.getUnitCities();
                $scope.setBrandPricingUnits();
            };

            $scope.setCustomerData = function (d) {
                //console.log("loading from api call"+d);
                $rootScope.showFullScreenLoader = false;
                $scope.customer = d;
                AppUtil.CSObj = $scope.customer;
            }

            $scope.unitPricingMapped = function (unitId) {
                return $scope.brandPricingUnits.indexOf(unitId) >= 0;
            };

            $scope.setSelectedBrand = function(brand) {
                $scope.selectedBrand = brand;
                AppUtil.setSelectedBrand(brand);
                $scope.setBrandPricingUnits();
            };

            $scope.setBrandPricingUnits = function () {
                if(AppUtil.activeChannelPartnerId === 6) {
                    $scope.brandPricingUnits = AppUtil.getSwiggyMappedBrandPricingUnits();
                }
                if(AppUtil.activeChannelPartnerId === 3) {
                    $scope.brandPricingUnits = AppUtil.getZomatoMappedBrandPricingUnits();
                }
                if(AppUtil.activeChannelPartnerId === 24) {
                    $scope.brandPricingUnits = AppUtil.getMappedBrandPricingUnits(AppUtil.activeChannelPartnerId);
                }
            }

            function getDefaultDeliveryPartners() {
                /***
                 * id:  patrnerId
                 * name :mappingType
                 * code : mappingValue(Default delivery Partner)
                 */
                posAPI.allUrl('/', AppUtil.restUrls.delivery.defaultDelivery)
                    .post('Default Delivery').then(function (response) {
                    AppUtil.defaultDeliveryDetail = response.plain();
                }, function (err) {
                    console.log('Error in getting response', err);
                });
            }

            $scope.getUnitCities = function () {
                posAPI.allUrl('/', AppUtil.restUrls.unitMetaData.unitCities)
                    .post().then(function (response) {
                    $scope.cityList = response.plain();
                    for (var i = 0; i < $scope.cityList.length; i++) {
                        if ($scope.cityList[i].id == 185) {
                            var temp = $scope.cityList[0];
                            $scope.cityList[0] = $scope.cityList[i];
                            $scope.cityList[i] = temp;
                            $scope.getUnitCityMapping($scope.cityList[0]);
                            break;
                        }
                    }
                }, function (err) {
                    console.log('Error in getting response', err);
                });
            };

            $scope.getUnitCityMapping = function (city) {
                $scope.selectedCity = city;
                $scope.selectedUnit = '';
                var locationId = city.id;
                $scope.unitList = null;
                posAPI.allUrl('/', AppUtil.restUrls.unitMetaData.unitCityMapping)
                    .post(locationId).then(function (response) {
                    $scope.unitList = response.plain();
                    if($scope.unitList!=null && $scope.unitList!=undefined){
                        for(var i =0;i<$scope.unitList.length;i++){
                            if($scope.unitList[i].id !=null && $scope.unitList[i].id !=undefined && $scope.unitList[i].zone!=null && $scope.unitList[i].zone!=undefined){
                                AppUtil.setUnitIDZoneMap($scope.unitList[i].id,$scope.unitList[i].zone);
                            }
                        }
                    }

                }, function (err) {
                    console.log('Error in getting response', err);
                });
            };

            $scope.selectUnit = function (unit) {
                $scope.selectedUnit = unit;
                initOutletObject($scope.selectedUnit);
                $scope.startOrder();
            };

            $scope.startOrder = function () {
                updateCustomerAndGetTaxProfile();
            };

            function updateCustomerAndGetTaxProfile() {
                // var partnerId = 6; //For Swiggy
                AppUtil.setPartnerId(AppUtil.activeChannelPartnerId);
                AppUtil.getTaxProfile();
                AppUtil.getPackagingProfile();
                AppUtil.getDeliveryProfile();
                AppUtil.getBrandUnitDeliveryProductProfile($scope.selectedBrand.brandId, $scope.selectedUnit.id, AppUtil.activeChannelPartnerId, function () {
                    desiChaiService.setRecipes();
                });
                /*AppUtil.getProductProfileByCity($scope.selectedCity.code, function () {
                    desiChaiService.setRecipes();
                });*/
            }

            function initOutletObject(selectedUnit) {
                AppUtil.outlet = {
                    pri_unitId: selectedUnit.id,
                    pri_name: selectedUnit.name,
                    sec_unitId: selectedUnit.id,
                    sec_name: selectedUnit.name,
                    ter_unitId: null,
                    ter_name: 'Tertiary Outlet',
                    selectedId: 1
                };
            }

        }]);
