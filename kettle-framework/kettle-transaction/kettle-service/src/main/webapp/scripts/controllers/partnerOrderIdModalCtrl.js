'use strict';

angular
    .module('posApp')
    .controller(
        'partnerOrderIdModalCtrl',
        ['$scope', '$rootScope','$modalInstance', 'AppUtil','$location', function ($scope, $rootScope, $modalInstance, AppUtil,$location) {

            $scope.init = function () {
                $rootScope.partnerName=null;
                $scope.isCOD=AppUtil.isCOD;
                $scope.partnerName = getPartnerName(AppUtil.getPartnerId());
                $scope.externPartnerName($scope.partnerName);
               /* if($scope.partnerName!=null){
                    $rootScope.channelPartners=[$scope.partnerName];
                }else{
                    $rootScope.channelPartners=["ZOMATO","SWIGGY","MAGICPIN"];
                }*/
                $rootScope.channelPartners=["ZOMATO","SWIGGY","MAGICPIN"];
            };

            function getPartnerName(partnerId){
                switch (partnerId){
                    case 6 :
                      return  "SWIGGY";
                    case 3 :
                       return "ZOMATO";
                    case 24 :
                       return "MAGICPIN";
                    default:
                      return   "SWIGGY";
                }
            }

            $scope.enterExternOrderId = function (channelPartnerOrder) {
                if (channelPartnerOrder != null && channelPartnerOrder != "") {
                    $scope.channelPartnerOrder = channelPartnerOrder;
                } else {
                    $scope.channelPartnerOrder = null;
                }
            };
            $scope.externPartnerName = function(channelPartnerName){
                if (channelPartnerName != null && channelPartnerName != "") {
                    $rootScope.partnerName = channelPartnerName;
                }
                else{
                    $rootScope.partnerName = null;
                }
            }
            $scope.setChannelPartnerOrderId = function (orderId) {
             if((orderId != null && orderId != "") ||  AppUtil.activeChannelPartnerId == -1) {
                          $modalInstance.close(orderId);
             }else{
                          bootbox.alert("Please enter valid order id");
             }

            }
            $scope.goToCvrScreen = function (allow) {
                if (allow) {
                    $location.url('/CODCover');
                    $modalInstance.close();
                }

            }

        }]);

