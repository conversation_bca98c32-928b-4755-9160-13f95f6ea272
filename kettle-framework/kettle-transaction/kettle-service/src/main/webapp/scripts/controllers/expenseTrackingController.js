/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

angular.module('posApp').controller('expenseTrackingController',['$scope', '$rootScope' , 'AppUtil', 'posAPI','$modal','$filter',
	function ($scope, $rootScope , AppUtil, posAPI, $modal, $filter) {
	$scope.expenseCategory = {
			name : "PETTY_CASH",
			label : "PETTY CASH"
	};
	var fileName = null;
	var expenseSheetList = [] ;
	$scope.init = function () {
		$scope.backToCover = AppUtil.backToCover;
		$scope.expenseType = [];
		getExpenseType();
		$scope.expenseDetail = {};
		$scope.unitDetails = AppUtil.getUnitDetails();
		$scope.expenseRequest = {};
		$scope.expenseDetailList = null;
		$scope.viewName = '';
		$scope.errorDetail = null;
		$scope.totalCost = 0;
	};

	$scope.changeView = function(view){
		if(view == $scope.viewName){
			console.log("add");
			return false;
		}
		$scope.viewName = view;
		$scope.expenseRequest = {};
		$scope.expenseDetailList = null;
		$scope.expenseDetail = {};
		$scope.errorDetail = null;
	};

	function getExpenseType() {
		$rootScope.showFullScreenLoader = true;
		posAPI.allUrl('/',AppUtil.restUrls.budgetManagement.getExpenseHeader).post($scope.expenseCategory.name).then(function (response) {
			if (response.errorType != undefined) {
				AppUtil.myAlert("Error while getting expense types !");
			} else {
				$scope.expenseType = response.plain();
			//	console.log("response  :  ",response.plain());
			}
			$rootScope.showFullScreenLoader = false;
		}, function (err) {
			$rootScope.showFullScreenLoader = false;
			AppUtil.myAlert(err.data.errorMessage);
		});
	};

	$scope.updateExpenseDetail= function(expenseDetailObj){
		$scope.expenseDetail.expenseType = expenseDetailObj.desc;
		$scope.expenseDetail.expenseCategory  = expenseDetailObj.type;
		$scope.expenseDetail.expenseTypeId = expenseDetailObj.id;
		$scope.expenseDetail.budgetCategoryId = expenseDetailObj.budgetCatId;
		$scope.expenseDetail.budgetCategory = expenseDetailObj.budgetCat;
		$scope.expenseDetail.accountableInPnL = expenseDetailObj.accountable;
	};

	$scope.saveExpenseDetail = function() {
		
		if ($scope.expenseDetail.amount <= 0) {
			AppUtil.myAlert("Please enter valid expense amount!");
			return false;
		} else if ($scope.expenseDetail.amount > 10000) {
			AppUtil.myAlert("Expense amount can not be greater than 10000.");
			return false;
		}
		$scope.errorDetail = null;
		$scope.expenseDetail.unitId = getIdCodeName($scope.unitDetails.id);    
		$scope.expenseDetail.source = "KETTLE_SERVICE";   
		$scope.expenseDetail.createdBy = getIdCodeName(AppUtil.GetLoggedInUser()); 
		$rootScope.showFullScreenLoader = true;
		posAPI.allUrl('/',AppUtil.restUrls.expenseManagement.addExpense).post($scope.expenseDetail).then(function (response) {
			//console.log(response);
			if (response.errorMessage != undefined) {
				AppUtil.myAlert("Error while saving expense details !");
				$rootScope.showFullScreenLoader = false;
			} else {
				$rootScope.showFullScreenLoader = false;
				var result = response.plain(); 
				
				if(result.errorMsg != null && result.errorMsg != '' && result.errorType == "ERROR"){
					$scope.errorDetail ={
							errorMsg : 	result.errorMsg,
							errorType : result.errorType
					};
				}else{
					if(result.errorMsg != null && result.errorMsg != '' && result.errorType == "WARNING"){
						AppUtil.myWarningAlert("Expense saved successfully. "+ result.errorMsg);
					}else{
						AppUtil.mySuccessAlert("Expense saved successfully.");
					}
					$scope.changeView('');
				}
			}
		}, function (err) {
			$rootScope.showFullScreenLoader = false;
			AppUtil.myAlert(err.data.errorMessage);
		});
	};
	
	function getIdCodeName(dataId){
		var idCodeName ={
				id : dataId,
				code : "",
				name : ""
		};
		return idCodeName;
	}

	$scope.cancelExpenseDetail = function(expenseDetail){
		var modalInstance = $modal.open({
			templateUrl: window.version+'scripts/modules/modals/expenseCancellationModal.html',
			controller: 'expenseCancellationController',
			backdrop: 'static',
			keyboard: false,
			scope: $scope,
			size: "lg",
			resolve: {
				expenseDetail: function () {
					return expenseDetail;
				}
			}
		});
	};

	$scope.getExpenseDetail = function(print) {
		fileName = null;
		expenseSheetList = [];
		if($scope.selectedUnitCancel == null || $scope.selectedUnitCancel == undefined){
			$scope.expenseRequest.unitId = null;
			fileName = "ExpenseDetail_All";
		}else{
			$scope.expenseRequest.unitId = $scope.selectedUnitCancel.id;
			fileName = "ExpenseDetail_"+$scope.selectedUnitCancel.id;
		}
		$scope.totalCost = 0;
		$rootScope.showFullScreenLoader = true;
		$scope.expenseRequest.unitId = $scope.unitDetails.id;
		$scope.expenseRequest.expenseCategory = null;
		//	console.log("$scope.expenseRequest ",$scope.expenseRequest);
		posAPI.allUrl('/',AppUtil.restUrls.expenseManagement.getExpenseDetail).post($scope.expenseRequest).then(function (response) {
			if (response.errorType != undefined) {
				bootbox.alert("Error while getting expense types !");
			} else {
				$scope.expenseDetailList = response.plain();
				if(print){
					downloadExpenseSheet();
				}
				for(var i =0;i < $scope.expenseDetailList.length;i++){
					if($scope.expenseDetailList[i].status== 'ACTIVE'){
						$scope.totalCost = $scope.expenseDetailList[i].amount + $scope.totalCost;
					}
				}
			}
			$rootScope.showFullScreenLoader = false;
		}, function (err) {
			$rootScope.showFullScreenLoader = false;
			AppUtil.myAlert(err.data.errorMessage);
		});
	};
	
	function downloadExpenseSheet (){
		for(var i =0;i < $scope.expenseDetailList.length;i++){
			var temp = {
				unitName : $scope.expenseDetailList[i].unitId.name,
				expenseType : $scope.expenseDetailList[i].expenseType,
				budgetCategory : $scope.expenseDetailList[i].budgetCategory,
				amount : $scope.expenseDetailList[i].amount,
				comment : $scope.expenseDetailList[i].comment,
				status : $scope.expenseDetailList[i].status,
				createdBy : $scope.expenseDetailList[i].createdBy.name,
				createdOn : $filter('date')($scope.expenseDetailList[i].createdOn, "dd/MM/yyyy"),
				cancelledBy : $scope.expenseDetailList[i].cancelledBy == null ? '-' : $scope.expenseDetailList[i].cancelledBy.name,
				cancelledOn : $scope.expenseDetailList[i].cancelledOn == null ? '-' : $filter('date')($scope.expenseDetailList[i].cancelledOn, "dd/MM/yyyy"),
				cancellationReason : $scope.expenseDetailList[i].cancellationReason == null ? '-' : $scope.expenseDetailList[i].cancellationReason
			};
			expenseSheetList.push(temp);
		}
		AppUtil.JSONToCSVConvertorLabelFromatter(expenseSheetList, fileName, true);
	};

}]);


angular
.module('posApp').controller('expenseCancellationController', ['$rootScope','$scope','AppUtil','$http','$modalInstance','posAPI','$modal','$location','expenseDetail',
	function ($rootScope,$scope,AppUtil,$http,$modalInstance,posAPI,$modal,$location,expenseDetail) {
	$scope.expenseDetail=expenseDetail;

	$scope.cancelExpenseDetail = function (reason) {
		if(reason == null || reason == undefined || reason.length == 0){
			AppUtil.myAlert("Please enter Expense detail cancellation reason!");
			return false;
		}
		var idCodeName ={
				id : AppUtil.GetLoggedInUser(),
				code : "",
				name : ""
		};
		$scope.expenseDetail.cancelledBy = idCodeName;
		$scope.expenseDetail.cancellationReason = reason;
		$rootScope.showFullScreenLoader = true;
		posAPI.allUrl('/',AppUtil.restUrls.expenseManagement.cancelExpenseDetail).post($scope.expenseDetail).then(function (response) {
			$rootScope.showFullScreenLoader = false;
			if (response.errorTitle != undefined) {
				bootbox.alert("Expense detail can not be cancelled.Please try after some time!");
			} else {
				if(response){
					$scope.closeModal();
					AppUtil.myAlert("Expense detail cancelled successfully!");
					$scope.getExpenseDetail();
				}else{
					AppUtil.myAlert("Expense detail can not be cancelled.Please try after some time!");
				}
			}
		}, function (err) {
			$rootScope.showFullScreenLoader = false;
			AppUtil.myAlert(err.data.errorMessage);
		});
	};

	$scope.closeModal = function() {
		$modalInstance.close();
	};

}]
);
