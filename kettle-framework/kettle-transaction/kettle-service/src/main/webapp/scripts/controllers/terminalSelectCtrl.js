/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';
/*jshint sub:true*/

angular.module('posApp')
    .controller('terminalSelectCtrl', ['$scope','noOfTerminal','$modalInstance',
        function ($scope,noOfTerminal,$modalInstance) {

            $scope.terminalArray = [];
            $scope.terminalSelected =  1;

            terminalArray();

           function terminalArray(){
                for(var i=1;i<(noOfTerminal + 1);i++){
                    var terminal = {
                        name:'Terminal ' + i,
                        checked: false
                    };
                    $scope.terminalArray.push(terminal);
                }
                //console.log($scope.terminalArray);
            }

            $scope.selectedTerminal = function(index){
                for(var i=0;i<$scope.terminalArray.length;i++){
                    if(i == index){
                        $scope.terminalArray[index].checked = !$scope.terminalArray[index].checked;
                        if($scope.terminalArray[index].checked){
                            $scope.terminalSelected =  index;
                        }
                    } else {
                        $scope.terminalArray[i].checked = false;
                    }
                }
            };

            $scope.proceed = function () {

            };

        }]);