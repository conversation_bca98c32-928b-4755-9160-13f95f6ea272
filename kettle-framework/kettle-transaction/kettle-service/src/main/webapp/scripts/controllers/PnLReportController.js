/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

angular.module('posApp').controller('PnLReportController', ['$scope', '$rootScope', 'AppUtil', 'posAPI', '$modal', '$http',
    function ($scope, $rootScope, AppUtil, posAPI, $modal, $http) {

        $scope.init = function () {
            $scope.backToCover = AppUtil.backToCover;
            $scope.pnlDetail = {};
            $scope.budgetExceeded = null;
            $scope.unitDetails = AppUtil.getUnitDetails();
            $scope.request = {};
            $scope.request.unitId = $scope.unitDetails.id;
            $scope.netProfitPerCent = 0;
            $scope.netProfit = 0;
            $scope.today = new Date(new Date().setDate(new Date().getDate() - 1)).toString();
            $scope.request.tillDate = new Date(new Date().setDate(new Date().getDate() - 1)).toISOString().slice(0, 10);
            $scope.limit = 5;
            $scope.groupSelected = true;
        };

        $scope.getPnLReport = function () {
            if ($scope.request.tillDate == null || $scope.request.tillDate === undefined) {
                AppUtil.myAlert("Please select till date for report!");
                return false;
            }
            $rootScope.showFullScreenLoader = true;
            getBudgetExceededTransactrions();
            posAPI.allUrl('/', AppUtil.restUrls.expenseManagement.PnLReportMTD).post($scope.request).then(function (response) {
                if (response.errorTitle != undefined) {
                    AppUtil.myAlert("Error while getting PnL Report !");
                } else {
                    $scope.pnlDetail = response.plain();
                    //console.log("response  :  ",response.plain());
                    for (var i = 0; i < $scope.pnlDetail.length; i++) {
                        if ($scope.pnlDetail[i].order == 350) {
                            $scope.netProfitPerCent = $scope.pnlDetail[i].value;
                        }
                    }
                }
                $rootScope.showFullScreenLoader = false;
            }, function (err) {
                $rootScope.showFullScreenLoader = false;
                AppUtil.myAlert(err.data.errorMessage);
            });
        };

        function getBudgetExceededTransactrions() {
            $rootScope.showFullScreenLoader = true;
            $scope.limit = 5;
            var data = {
                unitId: $scope.unitDetails.id,
                tillDate: $scope.request.tillDate
            };
            posAPI.allUrl('/', AppUtil.restUrls.expenseManagement.budgetExceededTransactions).post(data).then(function (response) {
                if (response.errorType != undefined) {
                    AppUtil.myAlert("Error while getting Budget Exceeded  Transactions !");
                } else {
                    $scope.budgetExceeded = response.plain();
                    //console.log("response  :  ",response.plain());
                }
                $rootScope.showFullScreenLoader = false;
            }, function (err) {
                $rootScope.showFullScreenLoader = false;
                AppUtil.myAlert(err.data.errorMessage);
            });
        };

        $scope.downloadMTDPnlDetailSheet = function () {
            var data = {
                unitId: $scope.unitDetails.id,
                tillDate: $scope.request.tillDate
            };
            $rootScope.showFullScreenLoader = true;
            $http({
                method: 'POST',
                // url: AppUtil.restUrls.expenseManagement.getMTDPnlDetailSheet,
                url: AppUtil.restUrls.expenseManagement.getMTDPnlAggregateDetailSheet,
                data: data,
                responseType: 'arraybuffer',
                headers: {
                    'Content-type': 'application/json',
                    'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                }
            }).then(function success(response) {
                var fileName = "MTD Pnl Detail Sheet- " + Date.now() + ".xlsx";
                var blob = new Blob([response.data], {
                    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                }, fileName);
                saveAs(blob, fileName);
                $rootScope.showFullScreenLoader = false;
            }, function error(response) {
                AppUtil.myAlert("Unable to download MTD detail sheet.");
                $rootScope.showFullScreenLoader = false;
            });
        };

        $scope.updateLimit = function (value) {
            $scope.limit = value;
        };


        $scope.downloadFinalizedPnlDetailSheet = function () {

            var data = {
                unitId: $scope.unitDetails.id,
                date: $scope.request.tillDate
            };
            $rootScope.showFullScreenLoader = true;
            $http({
                method: 'POST',
                // url: AppUtil.restUrls.expenseManagement.getFinalizedPnlDetailSheet,
                url: AppUtil.restUrls.expenseManagement.getFinalizedPnlAggregateDetailSheet,
                data: data,
                responseType: 'arraybuffer',
                headers: {
                    'Content-type': 'application/json',
                    'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                }

            }).then(function success(response) {
                var fileName = "Finalized Pnl Detail Sheet- " + Date.now() + ".xlsx";
                var blob = new Blob([response.data], {
                    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                }, fileName);
                saveAs(blob, fileName);
                $rootScope.showFullScreenLoader = false;
            }, function error(response) {
                AppUtil.myAlert("Unable to download Monthly detail sheet.");
                $rootScope.showFullScreenLoader = false;
            });
        };

        $scope.getPnLAggregateReport = function () {
            if ($scope.request.tillDate == null || $scope.request.tillDate === undefined) {
                AppUtil.myAlert("Please select till date for report!");
                return false;
            }
            $rootScope.showFullScreenLoader = true;
            getBudgetExceededTransactrions();
            $http({
                method: 'POST',
                url: AppUtil.restUrls.expenseManagement.PnLAggregateReport,
                data: $scope.request
            }).then(function success(response) {
                console.log(response);
                if (response.data != null && response.data.length) {
                    $scope.aggregateList = response.data;
                    $scope.aggregateList.shift();
                    $scope.aggregateList.shift();
                    $scope.aggregateList.shift();
                    $scope.aggregateList = response.data.map(function (aggregate) {
                        if(aggregate.key==='17. Net Profit'){
                            $scope.netProfit=aggregate.mtdValue[0]
                        }

                        if(aggregate.key==='18. Net Profit%'){
                            $scope.netProfitPerCent=aggregate.mtdValue[0]
                        }
                    aggregate.budget=  aggregate.budget!=null && aggregate.budget!=""?parseInt(aggregate.budget):aggregate.budget;
                    aggregate.mtdValue[0]=aggregate.mtdValue[0]!="null"&& aggregate.mtdValue[0]!=""?parseInt(aggregate.mtdValue[0]):0;
                        if(aggregate.drilldowns.length>0){
                            aggregate.drilldowns.forEach(function (value){
                                value.budget=  value.budget!=null && value.budget!=""?parseInt(value.budget):value.budget;
                                value.mtdValue[0]=value.mtdValue[0]!="null"&& value.mtdValue[0]!=""?parseInt(value.mtdValue[0]):0;
                            });
                        }
                        return aggregate;
                    }) ;
                } else {
                    $scope.aggregateList=[];
                    AppUtil.myAlert("Not able to fetch data");

                }
                $rootScope.showFullScreenLoader = false;

            }, function error(response) {
                AppUtil.myAlert(response.data.errorMessage);
                console.log("error:" + response);
            });
        }


        $scope.setSelectedRow = function (idSelected) {
            $scope.drilldowns = []
            if ($scope.idSelected == idSelected.key) {
                $scope.groupSelected = !$scope.groupSelected;
            }
            $scope.idSelected = idSelected.key;
            $scope.drilldowns = idSelected.drilldowns;
        };


    }]);


