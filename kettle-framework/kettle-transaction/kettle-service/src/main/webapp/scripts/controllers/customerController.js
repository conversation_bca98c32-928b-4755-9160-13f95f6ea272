/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * Created by abhinav_chaayos on 19/08/15.
 */

'use strict';

angular.module('posApp')
		.controller(
				'customerController',
				['$scope', '$cookieStore', 'posAPI', 'AppUtil', '$location', '$modal', 'socketUtils', '$rootScope',
					function($scope, $cookieStore, posAPI, AppUtil, $location, $modal, socketUtils, $rootScope) {
					// //console.log($scope);

					$scope.csNumber = null;
					$scope.csNumberCheck = false;
					$scope.csName = null;

					$scope.csEmail = null;

					$scope.isNewUser = false;

					$scope.hasEmail = false;

					$scope.customerSessionObj = AppUtil.customerSessionObj;

					$scope.loginLookUp = false;

					$scope.hasName = false;

					$scope.profileDataLoading = false;

					$scope.checkNumber = function() {
						//console.log($scope.csNumber);
						if (AppUtil.checkNumber($scope.csNumber)) {
							$scope.csNumberCheck = true;
						} else {
							$scope.csNumberCheck = false;
							//console.log($scope.csNumber.length)
							if ($scope.csNumber != undefined
									&& $scope.csNumber.toString().length == 10) {
								alert("Please enter a valid number");
							}
						}
					};
					
					

					initChecks();

					function initChecks() {
						
						//console.log('calling initChecks');
						if (AppUtil.isEmptyObject(AppUtil.customerSessionObj)) {
							$scope.loyaltyPointsNum = 0;
							$scope.chaiCount = 0;
							$scope.visitsRemaining = 0;
							//console.log('customerSessionObj is empty');
						} else {
							// csName
							$scope.loyaltyPointsNum = parseInt(AppUtil.customerSessionObj.customer.loyaltyPoints);

							// //console.log(AppUtil.customerSessionObj.customer.firstName);
							if (typeof AppUtil.customerSessionObj.customer.firstName == "string") {
								$scope.csName = AppUtil.customerSessionObj.customer.firstName;
								// //console.log($scope.csName);
								// //console.log(AppUtil.customerSessionObj.customer.firstName);
								$scope.hasName = true;
							}
							// visits remaining
							if ($scope.loyaltyPointsNum > 60) {
								$scope.visitsRemaining = 6 - (($scope.loyaltyPointsNum % 60) / 10);
							} else {
								$scope.visitsRemaining = 6 - (($scope.loyaltyPointsNum) / 10);
							}

							if ($scope.visitsRemaining % 6 == 0) {
								$scope.visitsRemaining = 0;
							}
							// chaiCount, isNewUser, hasEmail
							$scope.chaiCount = $scope.loyaltyPointsNum / 60;
							if ($scope.customerSessionObj.sentOtp) {
								// csregister email msg
								$scope.isNewUser = true;
							}
							if (typeof $scope.customerSessionObj.customer.emailId == "string") {
								// csprofile top msg
								$scope.hasEmail = true;
							}

							// csprofile redemption button
							if ($scope.customerSessionObj.loyaltyPoints < 60) {
								$scope.isAllowedRedemption = false;
							} else {
								$scope.isAllowedRedemption = true;
							}
						}
					}

					$scope.restartSession = function() {
						var unitid = $rootScope.globals.currentUser.unitId;
						var reqObj = {
							unit : unitid,
							terminalId : $rootScope.globals.currentUser.terminalId
						};
						posAPI.allUrl('/',AppUtil.restUrls.customer.logout).post(reqObj).then(
								function(response) {
									// //console.log(response);
								}, function(err) {
									AppUtil.myAlert(err.data.errorMessage);
									$scope.loginLookUp = false;
								});
						AppUtil.customerSessionObj = {};
						$cookieStore.remove('miniCustomerSessionObj');
						$location.url('/csthankyou');
					};

					$scope.customerStartNow = function() {
						$location.url('/cslogin');
					};

					$scope.skipemail = function() {
						if (typeof $scope.csName == "string"
								|| typeof AppUtil.customerSessionObj.customer.firstName == "string") {
							var customerSessionObj = $cookieStore
									.get('miniCustomerSessionObj');
							//console.log(customerSessionObj);
							//console.log(AppUtil.customerSessionObj);

							if (typeof AppUtil.customerSessionObj.customer.firstName != "string") {
								AppUtil.customerSessionObj.customer.firstName = $scope.csName;
								customerSessionObj.firstName = $scope.csName;
							}

							$cookieStore.put('miniCustomerSessionObj',
									customerSessionObj);

							posAPI.allUrl('/',AppUtil.restUrls.customer.update).post(
									AppUtil.customerSessionObj).then(
									function(response) {
										$location.url('/csprofile');

									}, function(err) {
										AppUtil.myAlert(err.data.errorMessage);
									});
						} else {
							AppUtil.myAlert('Please enter a valid Username');
						}

					};

					$scope.csLookUp = function() {
						$scope.loginLookUp = true;
						$scope.csNumberCheck = false;
						var unitid = $rootScope.globals.currentUser.unitId;
						var reqObj = {
							unit : unitid,
							contactNumber : $scope.csNumber,
							terminalId : $rootScope.globals.currentUser.terminalId
						};
						//console.log('checking csLookup');
						posAPI.allUrl('/',AppUtil.restUrls.customer.lookup)
								.post(reqObj)
								.then(
										function(response) {
											var bigObj = response.plain();
											//console.log(bigObj);
											var customerSessionObj = {
												contactNumber : bigObj.customer.contactNumber,
												contactNumberVerified : bigObj.customer.contactNumberVerified,
												emailId : bigObj.customer.emailId,
												emailVerified : bigObj.customer.emailVerified,
												id : bigObj.customer.id,
												loyaltyPoints : bigObj.customer.loyaltyPoints,
												generatedOrderId : bigObj.generatedOrderId,
												hasRedemption : bigObj.hasRedemption,
												newCustomer : bigObj.newCustomer,
												pointsRedeemedSuccessfully : bigObj.pointsRedeemedSuccessfully,
												sentOtp : bigObj.sentOtp,
												firstName : bigObj.customer.firstName,
												terminalId : bigObj.terminalId
											};

											AppUtil.customerSessionObj = response
													.plain();
											$scope.customerSessionObj = AppUtil.customerSessionObj;
											$cookieStore.put(
													'miniCustomerSessionObj',
													customerSessionObj);

											$scope.loyaltyPointsNum = parseInt(AppUtil.customerSessionObj.customer.loyaltyPoints);
											$scope.chaiCount = $scope.loyaltyPointsNum / 60;
											console
													.log($scope.customerSessionObj);

											if (bigObj.sentOtp) {
												$scope.loginLookUp = false;
												requiresOTP(true);
											} else {
												if (bigObj.newCustomer == true
														|| bigObj.customer.emailId == null) {
													$scope.loginLookUp = false;
													$location
															.url('/csregister');
												} else {
													$scope.loginLookUp = false;
													initChecks();
													$location.url('/csprofile');
												}
											}
										},
										function(err) {
											AppUtil
													.myAlert(err.data.errorMessage);
											$scope.loginLookUp = false;
										});

					};

					function requiresOTP(isSignUp) {
						$modal.open({
							animation : true,
							templateUrl : 'views/otpModal.html',
							controller : 'otpModalCtrl',
							backdrop : 'static',
							size : 'sm',
							resolve : {
								isSignUp : function() {
									return isSignUp;
								}
							}
						});
					}

					$scope.newCustomerRegistration = function() {
						if (IsEmail($scope.csEmail)) {
							var customerSessionObj = $cookieStore
									.get('miniCustomerSessionObj');
							//console.log($scope.csEmail);
							//console.log(customerSessionObj);
							//console.log(AppUtil.customerSessionObj);
							customerSessionObj.emailId = $scope.csEmail;
							customerSessionObj.firstName = $scope.csName;
							AppUtil.customerSessionObj.customer.emailId = $scope.csEmail;
							AppUtil.customerSessionObj.customer.firstName = $scope.csName;
							$cookieStore.put('miniCustomerSessionObj',
									customerSessionObj);

							posAPI.allUrl('/',AppUtil.restUrls.customer.update).post(
									AppUtil.customerSessionObj).then(
									function(response) {
										$location.url('/csprofile');
									}, function(err) {
										AppUtil.myAlert(err.data.errorMessage);
									});
						} else {
							AppUtil
									.myAlert('Please enter valid a email address');
						}
					};

					$scope.customerDone = function() {
						AppUtil.customerSessionObj = {};
						$cookieStore.remove('miniCustomerSessionObj');
						$location.url('/csthankyou');
					};

					// CSProfile

					$scope.redeemOTPCheck = function() {
						var customerSessionObj = $cookieStore
								.get('miniCustomerSessionObj');

						var unitid = $rootScope.globals.currentUser.unitId;
						var reqObj = {
							unit : unitid,
							contactNumber : customerSessionObj.contactNumber
						};
						if (customerSessionObj.newCustomer == true
								|| customerSessionObj.loyaltyPoints == 0) {
							// test and check here
							requiresOTP(false);
						} else {
							if (customerSessionObj.loyaltyPoints < 60) {
								AppUtil
										.myAlert('Minimum points required are 60 for redemption');
							} else {
								//console.log('generating otp');
								$scope.profileDataLoading = true;
								posAPI.allUrl('/',"customer")
										.all("generate")
										.all("otp")
										.post(reqObj)
										.then(
												function(response) {
													if (response) {
														$scope.profileDataLoading = false;
														requiresOTP(false);
													} else {
														$scope.profileDataLoading = false;
														AppUtil
																.myAlert('There was an error response from server, Please Try Again.');
													}
												},
												function(err) {
													$scope.profileDataLoading = false;
													AppUtil
															.myAlert(err.data.errorMessage);
													;
												});
							}

						}

					};

					function IsEmail(email) {
						var regex = /^([a-zA-Z0-9_.+-])+\@(([a-zA-Z0-9-])+\.)+([a-zA-Z0-9]{2,4})+$/;
						return regex.test(email);
					}

					/*
					 * $scope.events = []; $scope.idle = 5; $scope.timeout = 5;
					 * $scope.$on('IdleStart', function() { addEvent({event:
					 * 'IdleStart', date: new Date()}); });
					 * $scope.$on('IdleEnd', function() { addEvent({event:
					 * 'IdleEnd', date: new Date()}); }); $scope.$on('IdleWarn',
					 * function(e, countdown) { addEvent({event: 'IdleWarn',
					 * date: new Date(), countdown: countdown}); });
					 * $scope.$on('IdleTimeout', function() { addEvent({event:
					 * 'IdleTimeout', date: new Date()}); });
					 * 
					 * function addEvent(evt) { $scope.$evalAsync(function() {
					 * //console.log(evt); $scope.events.push(evt); }) }
					 * 
					 * $scope.$watch('idle', function(value) { if (value !==
					 * null) Idle.setIdle(value); }); $scope.$watch('timeout',
					 * function(value) { if (value !== null)
					 * Idle.setTimeout(value); });
					 */

				}]);
