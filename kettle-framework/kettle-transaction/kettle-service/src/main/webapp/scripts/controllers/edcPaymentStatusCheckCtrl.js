/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';
angular
    .module('posApp')
    .controller(
        'edcPaymentStatusCheckCtrl',
        ['$rootScope', '$scope', '$modalInstance', 'AppUtil', 'posAPI', '$location', '$timeout','initiateTransactionResponse',
            function($rootScope, $scope, $modalInstance, AppUtil, posAPI, $location, $timeout,initiateTransactionResponse) {

                $scope.init = function() {
                    bootbox.hideAll();
                    $scope.errorMessage = null;
                    $scope.intiateTransactionResponse = initiateTransactionResponse;
                };

                $scope.validateEdcPaymentStatus = function (){

                    if($scope.intiateTransactionResponse !=undefined && $scope.intiateTransactionResponse!=null && !AppUtil.isEmptyObject($scope.intiateTransactionResponse) && $scope.intiateTransactionResponse.paytmEDCTransactionResponse!=undefined && !AppUtil.isEmptyObject($scope.intiateTransactionResponse.paytmEDCTransactionResponse)){

                        var reqObj = {
                            "paytmMid": $scope.intiateTransactionResponse.paytmMid,
                            "paytmTid": $scope.intiateTransactionResponse.paytmTid,
                            "merchantKey" : $scope.intiateTransactionResponse.merchantKey,
                            "merchantTransactionId": $scope.intiateTransactionResponse.merchantTransactionId,
                            "paytmEDCTransactionResponse": $scope.intiateTransactionResponse.paytmEDCTransactionResponse
                        }

                        posAPI.allUrl('/', AppUtil.restUrls.edcPaymentManagement.checkEdcTransactionStatus)
                            .post(reqObj)
                            .then(function (response) {

                                if(response !=undefined && response!=null && response.body !=undefined && response.body !=null && response.body.resultInfo!=undefined && response.body.resultInfo!=null){
                                    if(response.body.resultInfo.resultCodeId !=undefined && response.body.resultInfo.resultCodeId !=null && response.body.resultInfo.resultCodeId == "0000" && response.body.merchantTransactionId !=undefined && response.body.merchantTransactionId !=null){
                                        // $scope.edcExternalTransactionId = response.body.merchantTransactionId;
                                       if(AppUtil.getEdcOrderMerchantId() == null){
                                           var last4digits = response.body.merchantTransactionId >3? response.body.merchantTransactionId.substring(response.body.merchantTransactionId.length-4) :null;
                                           AppUtil.setEdcOrderMerchantId(last4digits);
                                       }else if(AppUtil.getEdcOrderMerchantId() != null && response.body.merchantTransactionId >3){
                                           var setOrderId = AppUtil.getEdcOrderMerchantId()  +","+response.body.merchantTransactionId.substring(response.body.merchantTransactionId.length-4);
                                           AppUtil.setEdcOrderMerchantId(setOrderId);
                                       }
                                        // var last4digits = response.body.merchantTransactionId >3? $scope.edcExternalTransactionId.substring($scope.edcExternalTransactionId.length-4) :null;
                                        // AppUtil.setEdcOrderMerchantId(last4digits);

                                        // $scope.submit();
                                        // if($scope.payment[35]== undefined || $scope.payment == null){
                                        //     $scope.payment[35] = parseFloat(response.body.transactionAmount) / 100;
                                        // }
                                        // else{
                                        //     $scope.payment[35] = $scope.payment[35] + (parseFloat(response.body.transactionAmount) / 100);
                                        // }
                                        $modalInstance.close((parseFloat(response.body.transactionAmount) / 100));
                                    }
                                    else{
                                        $scope.showErrorMessage("Payment Status ::"+ response.body.resultInfo.resultStatus);
                                    }
                                }

                            },function(error){
                                console.log("Error verifying payment Status",error);
                                bootbox.alert("Cannot verify payment Status");
                            });
                    }
                    else{
                        $scope.showErrorMessage("Please Initiate Transaction First");
                    }
                }

                $scope.showErrorMessage = function(text) {
                    $scope.errorMessage = text;
                    $timeout(function() {
                        $scope.errorMessage = null;
                    }, 4000)
                }

                // $scope.selectPaymentMode = function (mode){
                //     $modalInstance.close(mode);
                // }

                $scope.goBack = function() {
                    $modalInstance.close(null);
                };
            }]);
