/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';
angular
    .module('posApp')
    .controller(
        'edcMerchantIdVerificationModalCtrl',
        ['$rootScope', '$scope', '$modalInstance', 'AppUtil', 'posAPI', '$location', '$timeout',
            function($rootScope, $scope, $modalInstance, AppUtil, posAPI, $location, $timeout) {

                $scope.init = function() {
                    bootbox.hideAll();
                    $scope.transactionId = null;
                    $scope.orderMerchantIdLastDigits = AppUtil.getEdcOrderMerchantId();
                    $scope.wrongTransactionId = null;
                    $scope.retryCount=0;
                    $scope.errorMessage = null;
                };

                $scope.verifyTransactionId = function(transactionId){
                    if($scope.wrongTransactionId!=null && $scope.wrongTransactionId ==transactionId){
                        $scope.showErrorMessage("Enter Different Transaction ID");
                        return ;
                    }

                    if($scope.transactionId == $scope.orderMerchantIdLastDigits ){
                        $modalInstance.close(true);
                    }
                    if($scope.retryCount!=null && $scope.retryCount>1){
                        //AppUtil.setEdcOrderRetryCount($scope.retryCount);
                        $modalInstance.close(false);
                    }

                    $scope.showErrorMessage("Wrong Transaction Id");
                    $scope.wrongTransactionId = transactionId;
                    $scope.retryCount = $scope.retryCount+1;
                }

                $scope.showErrorMessage = function(text) {
                    $scope.errorMessage = text;
                    $timeout(function() {
                        $scope.errorMessage = null;
                    }, 4000)
                }

                $scope.goBack = function() {
                    if (!AppUtil.isCOD()) {
                        $location.url('/cover');
                    } else {
                        $location.url('/CODCover');
                    }
                    $modalInstance.close();
                };
            }]);
