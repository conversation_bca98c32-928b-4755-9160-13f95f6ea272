/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

angular.module('posApp')
    .controller('subscriptionOrderSearchCtrl', ['$rootScope','$scope','AppUtil','$location','$http',
        'posAPI','$modal','subscriptionService',
        function ($rootScope,$scope,AppUtil,$location,$http,posAPI,$modal,subscriptionService) {

	    	$scope.init= function(){
	    		AppUtil.validate();
	      	    $scope.transactionMetadata = AppUtil.getTransactionMetadata();
	      	    $scope.unitDetails = AppUtil.getUnitDetails();
	      	    $scope.convertTimeFromIndex = subscriptionService.convertTimeFromIndex;
	      	    $scope.convertWeekDayFromIndex = subscriptionService.convertWeekDayFromIndex;
            	$scope.dateGTE = subscriptionService.dateGTE;
	      	    $scope.showHoldDetails = false;
            	$scope.showHoldLoader = false;
            	if(AppUtil.previousLocation[AppUtil.previousLocation.length - 2]=="/orderSearch"){
            		$scope.orderList = subscriptionService.orderList;
                	$scope.orderInput = subscriptionService.customerContact;
                	$scope.CSObj = AppUtil.CSObj;
                    $scope.addresses = AppUtil.CSObj.addresses;
            	}else if(AppUtil.previousLocation[AppUtil.previousLocation.length - 2]=="/subscriptionSearch"){
            		$scope.orderList = subscriptionService.orderList;
                	$scope.orderInput = subscriptionService.customerContact;
                	$scope.CSObj = AppUtil.CSObj;
                    $scope.addresses = AppUtil.CSObj.addresses;
            	}else{
            		$scope.orderList = null;
                	$scope.orderInput = null;
            	}
            	if(AppUtil.previousLocation[AppUtil.previousLocation.length - 2]=="/subscriptionSearch"){
            		$scope.backToCover = function(){
            			$location.path("/subscriptionSearch");
            		}
            	}else{
            		$scope.backToCover = AppUtil.backToCover;
            	}
		    	
	    	}
    		
	    	$scope.getSubscriptionOrders = function(){
	    		 if ($scope.orderInput.length==10) {
	    			$scope.fetchCSObj();
	    			var requestObj =  AppUtil.GetRequest($scope.orderInput);
	 	    		posAPI.allUrl('/',AppUtil.restUrls.subscription.getCustomerSubscriptionOrders)
	     			.post(requestObj).then(function(response) {
	     				if(response){
	     					$scope.orderList = response.plain();
	     					subscriptionService.orderList = $scope.orderList;
	     					subscriptionService.customerContact = $scope.orderInput;
	     				}
	     			}, function(err) {
	     				AppUtil.myAlert(err.data.errorMessage);
	     			});
	    		 }else{
	    			 AppUtil.myAlert('Contact Number can not be less than 10 digits');
	    		 }
	    	}
	    	
	    	$scope.fetchCSObj = function(){
	    		var reqObj = {
    	            session: $rootScope.globals.currentUser,
    	            contactNumber: $scope.searchText,
    	            customer: AppUtil.CSObj,
    	            newAddress: null,
    	            newCustomer: false
    	        };
	    		
	            reqObj.contactNumber = $scope.orderInput;
	            reqObj.customer.contactNumber = $scope.orderInput;
	            reqObj.customer.registrationUnitId = $rootScope.globals.currentUser.unitId;
	            reqObj.customer.acquisitionToken = $rootScope.globals.currentUser.userId;
	            $scope.addresses = [];
	            AppUtil.CSObj = {};
	            posAPI.allUrl('/',AppUtil.restUrls.codCustomer.lookup).post(reqObj)
                .then(function (response) {
                    //console.log(response.plain());
                    //console.log(response);
                    AppUtil.CSObj = response.customer;
                    $scope.CSObj = response.customer;
                    $scope.addresses = AppUtil.CSObj.addresses;
                    //console.log($scope.addresses);
                }, function (err) {
                    AppUtil.myAlert(err.data.errorMessage);
                });
	    	}
	    	
	    	$scope.getLocalityFromDeliveryAddresId = function(deliveryAddressId){
	    		var locality = "NA";
	    		$scope.addresses.forEach(function(address){
	    			if(address.id==deliveryAddressId){
	    				locality = address.locality;
	    			}
	    		});
	    		return locality;
	    	}
	    	
	    	$scope.openOrderSearch = function(subscriptionObj) {
	    		AppUtil.OrderObj = $rootScope.OrderObj = subscriptionObj;
	    		$rootScope.orderType = 'subscription';
                if (AppUtil.OrderObj.deliveryAddress != null) {
                	AppUtil.GetCustomerInfo(AppUtil.OrderObj.deliveryAddress);
                    AppUtil.getDeliveryDetail(AppUtil.OrderObj.generateOrderId);
                } else if (!AppUtil.isEmptyObject($rootScope.CustomerObj)) {
                    $rootScope.CustomerObj = {};
                }
                $location.url('/orderSearch');
            };
            
            $scope.loadOrderScreen = function(order){
            	AppUtil.OrderObj = $rootScope.OrderObj = order.orderDetail;
	    		$rootScope.orderType = 'order';
                if (AppUtil.OrderObj.deliveryAddress != null) {
                	AppUtil.GetCustomerInfo(AppUtil.OrderObj.deliveryAddress);
                    AppUtil.getDeliveryDetail(AppUtil.OrderObj.generateOrderId);
                } else if (!AppUtil.isEmptyObject($rootScope.CustomerObj)) {
                    $rootScope.CustomerObj = {};
                }
                $location.url('/orderSearch');
            }
            
            $scope.editOrder = function(order){
            	$scope.selectedOrder = order;
            	var modalInstance = $modal.open({
                    animation: true,
                    templateUrl: window.version+"scripts/modules/modals/editSubscriptionOrdersModal.html" ,
                    controller: 'editSubscriptionOrdersModalCtrl',
                    backdrop: 'static',
                    scope: $scope,
                    size: "lg"
                });
            	modalInstance.result.then(function (response) {
            		if(!angular.isUndefined(response.errorType)){
            			AppUtil.myAlert(response.errorType);
            		}else{
            			AppUtil.mySuccessAlert("Event with event id: "+$scope.selectedOrder.subscriptionEventId+" updated successfully.");
            			$scope.getSubscriptionOrders();
            		}
        	    }, function () {
        	    });
            }
            
	}]);

angular.module('posApp').controller('editSubscriptionOrdersModalCtrl', function ($scope, $modalInstance,subscriptionService,AppUtil,posAPI) {
	$scope.init = function(){
		$scope.actionReason = $scope.selectedOrder.reason;
		$scope.frequencyTimes = subscriptionService.frequencyTimes;
		$scope.newFrequencyTime = subscriptionService.getFrequencyTimeObject(
				subscriptionService.getTimeValueFromTimestamp($scope.selectedOrder.actualDeliveryTime));
	}
    $scope.ok = function() {
    	$scope.selectedOrder.reason = $scope.actionReason;
    	$scope.selectedOrder.originalDeliveryTime = subscriptionService.getTimestampFromTime($scope.newFrequencyTime.value);
    	$scope.selectedOrder.regularTimeChanged = true;
    	var requestObj =  AppUtil.GetRequest($scope.selectedOrder);
 		posAPI.allUrl('/',AppUtil.restUrls.subscription.updateSubscriptionEvent)
		.post(requestObj).then(function(response) {
			//console.log(response.plain());
			$modalInstance.close(response.plain());
		}, function(err) {
			AppUtil.myAlert(err.data.errorMessage);
	    	$modalInstance.close();
		});
    }
    $scope.cancel = function() {
    	$modalInstance.dismiss("cancel");
    }
	$scope.skipEvent = function(){
		$scope.selectedOrder.status = "SKIPPED";
		var requestObj =  AppUtil.GetRequest($scope.selectedOrder);
 		posAPI.allUrl('/',AppUtil.restUrls.subscription.updateSubscriptionEvent)
		.post(requestObj).then(function(response) {
			$modalInstance.close(response.plain());
		}, function(err) {
			AppUtil.myAlert(err.data.errorMessage);
		});
	}
	
});