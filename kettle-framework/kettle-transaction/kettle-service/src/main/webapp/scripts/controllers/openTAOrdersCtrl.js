/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

angular
    .module('posApp')
    .controller('openTAOrdersCtrl',
		['$rootScope', '$location', '$scope', 'AuthenticationService', '$modal', 'AppUtil', 'posAPI',
			function ($rootScope, $location, $scope, AuthenticationService, $modal, AppUtil, posAPI) {
    	
    	
    	$scope.openOrderData = {};
        $scope.isOpen = true;
        $scope.isSecOpen = true;

    	$scope.init = function(){
    		fetchOrders();
    	};

        $scope.openOrderSearch = function (generateOrderId) {
            AppUtil.openOrderSearch(generateOrderId,"order");
        };

        $scope.backToCover = function () {
            $location.url("/cover");
        };
        
        $scope.refresh = function(){
        	fetchOrders();
        };
        
        
        $scope.getSettlements = function(orderObj){
        	return AppUtil.getSettlements(orderObj);
        };
    	
    	
    	function fetchOrders(){
    		posAPI.allUrl('/',AppUtil.restUrls.order.takeAwayOrders).post($rootScope.globals.currentUser)
	        .then(function (response) {
	        	$scope.openOrderData = getOrders(response.plain());
	        }, function (err) {
	            AppUtil.myAlert(err.data.errorMessage);
	        });
    	}
    	
    	function getOrders(orderList){
    		var openOrderData = {};
    		orderList.forEach(function(orderObj){
    			var ordersInStatus = openOrderData[orderObj.order.status];
    			if(ordersInStatus == null){
    				ordersInStatus = [];
    			}
    			orderObj['elapsedTime'] = ((Date.now() - new Date(orderObj.order.billCreationTime))/(1000*60)).toFixed(2);
    			ordersInStatus.push(orderObj);
    			openOrderData[orderObj.order.status] = ordersInStatus;
    		});
    		//console.log("openOrderData ::::",openOrderData);
    		return openOrderData;
    	}
    	
    }]);
