/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';
/*jshint sub:true*/

angular.module('posApp')
  .controller('settlementTypeModal', ['$scope','settlementTypeObj','$sce','$modalInstance','PrintService', 'AppUtil',
    function ($scope,settlementTypeObj,$sce,$modalInstance,PrintService, AppUtil) {
      var settlementStr = AppUtil.clearSpecialChars(settlementTypeObj.replace(/6px/g,"12px"));
      var settlementStr2 = settlementStr.split("----------------------------------------------");
      $scope.paymentObjMap= [];
      if(settlementStr2.length>1){
          var paymentStr =  settlementStr2[1].replace(/[\r\n]/gm, '')
          paymentStr = paymentStr.replace(/\s\s+/g, ' ').trim();
          paymentStr = paymentStr.replace(/\x00/g, ' ').trim();
          var paymentStrArr = paymentStr.split(" ");
          var paymentObj={};
          for(var i=0; i<paymentStrArr.length;i++){
              if(paymentStrArr[i]=="GMV"){
                  paymentObj={};
                  if(paymentStrArr[i-1] == "Card" || paymentStrArr[i-1] == "Pay" || paymentStrArr[i-1] == "UPI" || paymentStrArr[i-1] == "DEBIT/CREDIT" || paymentStrArr[i-1] == "AMEX" || paymentStrArr[i-1] == "Meal" || paymentStrArr[i-1] == "DEALS"){
                      paymentObj["paymentMode"] = paymentStrArr[i-2] + " " +paymentStrArr[i-1];
                  }
                  else{
                      paymentObj["paymentMode"] = paymentStrArr[i-1];
                  }
              }
              if(paymentStrArr[i-1] == "Taxable"){
                  paymentObj["taxableAmount"] = paymentStrArr[i];
              }
              if(paymentStrArr[i-1] == "Amount"){
                  paymentObj["amount"] = paymentStrArr[i];
              }
              if(!AppUtil.isEmptyObject(paymentObj) && paymentStrArr[i]=="GMV") {
                  $scope.paymentObjMap.push(paymentObj);
              }
          }
      }
      $scope.settlementType = $sce.trustAsHtml('<div style="text-align: center;"><pre style="display: inline-block; text-align: left;">'
		+ settlementStr2[0] + '</pre></div>');

      $scope.cancel= function(){
        $modalInstance.dismiss('dismiss');
      };

      $scope.print = function() {
        $modalInstance.dismiss('dismiss');
		if (AppUtil.isAndroid) {
			Android.printText(settlementTypeObj);
		} else {
			PrintService.printOnBilling(settlementTypeObj, "RAW");
		}
      };
    }]);
