/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

angular
    .module('posApp')
    .controller(
        'desiChaiModalCtrl',
        ['$scope', '$modalInstance', 'orderItem', '$filter', 'AppUtil', 'productService', 'desiChaiService', 'isDesiChai','customerObj','typeOfChai',
            function ($scope, $modalInstance, orderItem, $filter, AppUtil, productService, desiChaiService, isDesiChai,customerObj, typeOfChai) {


            $scope.initCustom = function () {
                $scope.customerObj = customerObj;
                $scope.orderItem = orderItem;
                $scope.orderItem.isHeartButtonClicked=undefined;
                $scope.chaiSavedFromDineIn = $scope.orderItem.chaiSavedFromDineIn;
                $scope.isFavChai= $scope.orderItem.isFavChaiMarked;
                $scope.checkIsHeartButtonClicked();
                $scope.itemQuantity = $scope.orderItem.orderDetails.quantity;
                //$scope.selectedOrderItem = orderItem;
                $scope.itemIndex = $scope.orderItem.orderDetails.itemId;
                $scope.desiChaiProducts = (typeOfChai === "DESI" ? desiChaiService.getDesiChaiProducts(): (typeOfChai === "BAARISH" ? desiChaiService.getBaarishWaliChaiProducts() : []));
                $scope.orderItems = {};
                $scope.selectedAddonsList = [];
                $scope.selectedOptionsList = [];
                $scope.selectedVariantMap = {};
                $scope.selectedProductsMap = {};
                $scope.customizationsList =[];
                $scope.currentCustomizationList=[];
                $scope.unitsProductMap = AppUtil.getUnitsProductMap();
                $scope.isMonkCalibration = function () {
                     return $scope.complimentaryReasonId == 2110;
                }
                $scope.desiChaiProducts.map(function (product) {
                    if ($scope.orderItem.productDetails.id != product.id) {
                        //$scope.orderItems[product.id] = AppUtil.addNewItem(product, 1, false, $scope.itemIndex);
                        var priceObj = null;
                        product.prices.map(function (price) {
                            if (price.dimension == orderItem.orderDetails.dimension) {
                                priceObj = price;
                            }
                            if (product.id == 80 && ["Regular", "Full"].indexOf(orderItem.orderDetails.dimension) >= 0 && price.dimension == "None") {
                                priceObj = price;
                            }
                        });
                        if(priceObj != null){
	                        $scope.orderItems[product.id] = AppUtil.addNewItemForIndex(product, $scope.itemQuantity, false, priceObj, $scope.itemIndex);
	                        $scope.initCustomizations(product);
                        }
                    }
                });
                $scope.getAllComplimentaryCodes = AppUtil.getAllComplimentaryCodes;
                $scope.complimentaryDetail = $scope.orderItem.orderDetails.complimentaryDetail.isComplimentary ? $scope.orderItem.orderDetails.complimentaryDetail
                    : {
                        reasonCode: null,
                        isComplimentary: null,
                        reason: null
                    };
                $scope.orderItems[$scope.orderItem.productDetails.id] = $scope.orderItem;
                $scope.initCustomizations($scope.orderItem.productDetails);
                $scope.selectedOrderItem = $scope.orderItems[$scope.orderItem.productDetails.id];
                $scope.verifySugarVariantWithPaidAddon();
                console.log($scope.desiChaiProducts);
            };

            $scope.verifySugarVariantWithPaidAddon = function () {
                if (!$scope.selectedOrderItem.orderDetails.isCombo &&
                    $scope.selectedOrderItem.recipeDetails != null && $scope.selectedOrderItem.recipeDetails.ingredient != null && $scope.selectedOrderItem.recipeDetails.ingredient.variants != null
                    && $scope.selectedOrderItem.recipeDetails.ingredient.variants.length > 0) {
                    angular.forEach($scope.selectedOrderItem.recipeDetails.ingredient.variants, function (variant) {
                        var checkForRemovingDefaultSugar = false;
                         angular.forEach(variant.details, function (variantDetails) {
                             if (variantDetails.alias.toLocaleLowerCase().includes('sugar') && $scope.selectedOrderItem.recipeDetails.options != null &&
                                 $scope.selectedOrderItem.recipeDetails.options.length > 0 && $scope.checkForPaidAddonsNeedsToMergeWithSugar()) {
                                 var filteredPaidAddonsMergedWithSugar = $scope.filterAllPaidAddonsMergedWithSugar($scope.selectedOrderItem.recipeDetails.options);
                                 checkForRemovingDefaultSugar = false;
                                 angular.forEach(filteredPaidAddonsMergedWithSugar, function (filteredPaidAddon) {
                                        if ($scope.selectedOptionsList.indexOf(filteredPaidAddon.id) >= 0) {
                                            checkForRemovingDefaultSugar = true;
                                        }
                                 });
                                 if (checkForRemovingDefaultSugar) {
                                     $scope.checkIfPresentInCustomizationList(variantDetails.alias, false);
                                 }
                             }
                         });
                         if (checkForRemovingDefaultSugar) {
                             var key = "";
                             angular.forEach(variant.details, function (sugarVariant) {
                                 key = key + sugarVariant.alias;
                             });
                             $scope.selectedVariantMap[key] = "SELECTED_PAID_ADDON";
                         }
                    });
                }


                $scope.selectOptions = function (option, sugarVariants, paidAddonsMergedWithSugarVariants) {
                    $scope.removePaidAddonsMergedWithSugarVariants(paidAddonsMergedWithSugarVariants);
                    if ($scope.selectedOptionsList.indexOf(option.id) < 0) {
                        $scope.selectedOptionsList.push(option.id);
                        $scope.checkIfPresentInCustomizationList(option.name,true);
                        $scope.checkAndRemoveSugarVariantSelection(option, sugarVariants);
                    } else {
                        $scope.selectedOptionsList.splice($scope.selectedOptionsList.indexOf(option.id), 1);
                        $scope.checkIfPresentInCustomizationList(option.name,false);
                    }
                };
            };

            $scope.checkIsHeartButtonClicked =function (){
                $scope.favChaiInsideModal = $scope.orderItem.isFavChaiMarked;
            }

            $scope.initCustomizations = function (product) {
                $scope.customizationsList =[];
                if ($scope.orderItems[product.id].recipeDetails.ingredient != null && $scope.orderItems[product.id].recipeDetails.ingredient.variants != null &&
                    $scope.orderItems[product.id].recipeDetails.ingredient.variants.length > 0) {
                    $scope.orderItems[product.id].recipeDetails.ingredient.variants.map(function (variant) {
                        variant.details.map(function (detail) {
                            if (detail.selected == true) {
                                $scope.selectVariantsData(variant, detail);
                            }
                        });
                    });
                }
                if ($scope.orderItems[product.id].recipeDetails.ingredient != null && $scope.orderItems[product.id].recipeDetails.ingredient.products != null &&
                    $scope.orderItems[product.id].recipeDetails.ingredient.products.length > 0) {
                    $scope.orderItems[product.id].recipeDetails.ingredient.products.map(function (product) {
                        product.details.map(function (detail) {
                            if (detail.selected == true) {
                                $scope.selectedProductsMap[product.display] = detail.product.productId;
                                $scope.customizationsList.push(detail.product.name);
                            }
                        });
                    });
                }
                if ($scope.orderItems[product.id].recipeDetails.addons != null &&
                    $scope.orderItems[product.id].recipeDetails.addons.length > 0) {
                    $scope.orderItems[product.id].recipeDetails.addons.map(function (addon) {
                        if (addon.selected == true) {
                            $scope.selectedAddonsList.push(addon.product.productId);
                            $scope.customizationsList.push(addon.product.name);
                        }
                    });
                }
                if ($scope.orderItems[product.id].recipeDetails.options != null &&
                    $scope.orderItems[product.id].recipeDetails.options.length > 0) {
                    $scope.orderItems[product.id].recipeDetails.options.map(function (option) {
                        if (option.selected == true) {
                            $scope.selectedOptionsList.push(option.id);
                            $scope.customizationsList.push(option.name);
                        }
                    });
                }
                console.log("Initial customization lIst  ::::::::",$scope.customizationsList);
            };

            $scope.selectDesiChaiProduct = function (productItem) {
                /*if(AppUtil.checkIsDeliveryOnlyProduct($scope.selectedOrderItem.productDetails)){
                    $modalInstance.close($scope.selectedOrderItem);
                    bootbox.alert("This is a delivery only product !");
                    return;
                }*/
                if($scope.orderItem.isFavChaiMarked){
                    if($scope.orderItem.productDetails.id!=productItem.id){
                        $scope.favChaiInsideModal= false;
                    }else{
                        $scope.favChaiInsideModal= true;
                    }
                }
                $scope.selectedOrderItem = $scope.orderItems[productItem.id];
                if ($scope.selectedOrderItem.productDetails.productAliasName != null && $scope.selectedOrderItem.productDetails.productAliasName !== undefined) {
                    $scope.selectedOrderItem.orderDetails.productAliasName = $scope.selectedOrderItem.productDetails.productAliasName;
                }
                else{
                     $scope.selectedOrderItem.orderDetails.productAliasName = $scope.selectedOrderItem.productDetails.name;
                }
                var foundOptions = [];
                var foundOptionName = [];
                $scope.selectedOrderItem.recipeDetails.options.map(function (option) {
                    if($scope.selectedOptionsList.indexOf(option.id) >= 0) {
                        option.selected = true;
                        foundOptions.push(option.id);
                        foundOptionName.push(option.name);
                    } else {
                        option.selected = false;
                    }
                });
                // removing other options which doesn't exist in current recipe
                angular.forEach($scope.selectedOptionsList, function (selectedOption) {
                    if (foundOptions.indexOf(selectedOption) < 0) {
                        $scope.selectedOptionsList.splice($scope.selectedOptionsList.indexOf(selectedOption), 1);
                        $scope.checkIfPresentInCustomizationList(selectedOption.name,false);
                    }
                });

                angular.forEach($scope.customizationsList, function (selectedOption) {
                    if (foundOptionName.indexOf(selectedOption) < 0) {
                        $scope.checkIfPresentInCustomizationList(selectedOption,false);
                    }
                });
                console.log($scope.selectedOrderItem);
            };
            
            $scope.selectTakeAway  = function(item) {
                if(item.orderDetails.takeAway == undefined || item.orderDetails.takeAway == null){
            	item.orderDetails.takeAway = true;
                }else{
            	item.orderDetails.takeAway = !item.orderDetails.takeAway;
                }
                
            };

            $scope.removePaidAddonsMergedWithSugarVariants = function (paidAddonsMergedWithSugarVariants) {
                if (paidAddonsMergedWithSugarVariants !== undefined && paidAddonsMergedWithSugarVariants != null && paidAddonsMergedWithSugarVariants.length > 0) {
                    angular.forEach(paidAddonsMergedWithSugarVariants, function (value) {
                        if ($scope.selectedOptionsList.indexOf(value.id) >= 0) {
                            $scope.selectedOptionsList.splice($scope.selectedOptionsList.indexOf(value.id), 1);
                            $scope.checkIfPresentInCustomizationList(value.name,false);
                            // REMOVING FROM ORDER ITEM TOO.

                        }
                    });
                }
            };

            $scope.selectVariantsData = function (variant, detail,isAnyVariantButtonClicked, paidAddonsMergedWithSugarVariants) {
                var key = "";
                variant.details.map(function (detail) {
                    key = key + detail.alias;
                });
                $scope.selectedVariantMap[key] = detail.alias;
                if(isAnyVariantButtonClicked){
                    for(var i in variant.details){
                        if(variant.details[i].alias==detail.alias){
                            $scope.checkIfPresentInCustomizationList(variant.details[i].alias,true);
                        }else{
                            $scope.checkIfPresentInCustomizationList(variant.details[i].alias,false);
                        }
                    }
                }else {
                    $scope.customizationsList.push(detail.alias);
                }
                $scope.removePaidAddonsMergedWithSugarVariants(paidAddonsMergedWithSugarVariants);
            };

            $scope.selectProductsData = function (product, detail) {
                $scope.selectedProductsMap[product.display] = detail.product.productId;
            };

            $scope.checkSelectedVariant = function (variant, alias) {
                var key = "";
                variant.details.map(function (detail) {
                    key = key + detail.alias;
                });
                return $scope.selectedVariantMap[key] == alias;
            };

            $scope.selectAddOns = function (addon) {
                if ($scope.selectedAddonsList.indexOf(addon.product.productId) < 0) {
                    $scope.selectedAddonsList.push(addon.product.productId);
                    $scope.checkIfPresentInCustomizationList(addon.product.name,true);
                } else {
                    $scope.selectedAddonsList.splice($scope.selectedAddonsList.indexOf(addon.product.productId), 1);
                    $scope.checkIfPresentInCustomizationList(addon.product.name,false);
                }
            };

            $scope.checkAndRemoveSugarVariantSelection = function (option,sugarVariants ) {
                var paidAddonsNeedToMergeWithSugar = $scope.checkForPaidAddonsNeedsToMergeWithSugar() ? AppUtil.getAllCafeAppProperties().paidAddonsNeedToMergeWithSugar : [];
                var isMergedWithSugar = paidAddonsNeedToMergeWithSugar.includes(option.productId.toString());
                if (isMergedWithSugar) {
                    var key = "";
                    angular.forEach(sugarVariants, function (sugarVariant) {
                        $scope.checkIfPresentInCustomizationList(sugarVariant.alias, false);
                        key = key + sugarVariant.alias;
                    });
                    $scope.selectedVariantMap[key] = "SELECTED_PAID_ADDON";
                }
            };

            $scope.checkIfPresentInCustomizationList=function(alias, isSelected){
                if ($scope.currentCustomizationList != undefined && $scope.currentCustomizationList.length == 0) {
                    $scope.currentCustomizationList = angular.copy($scope.customizationsList);
                }
                if (isSelected) {
                    if ($scope.currentCustomizationList != null && $scope.currentCustomizationList.length >=0 && $scope.currentCustomizationList.indexOf(alias) < 0) {
                        $scope.currentCustomizationList.push(alias);
                    }
                } else {
                    $scope.currentCustomizationList = $scope.currentCustomizationList.filter(function (ele) {
                        return ele != alias;
                    });
                }
                console.log("Current Customizations List------->", $scope.currentCustomizationList);
                if ($scope.currentCustomizationList.length == $scope.customizationsList.length) {
                    if ($scope.currentCustomizationList.length > 0 && $scope.customizationsList.length > 0) {
                        var isEqual = $scope.currentCustomizationList.every(function(customization){
                            return $scope.customizationsList.indexOf(customization)!==-1;
                        });
                        if (isEqual && $scope.isFavChai!=undefined && $scope.isFavChai) {
                            $scope.favChaiInsideModal = true;
                        } else {
                            $scope.favChaiInsideModal = false;
                        }
                    }
                } else {
                    $scope.favChaiInsideModal = false;
                }
                $scope.selectedOrderItem.isFavChaiMarked =$scope.favChaiInsideModal;
            };

                $scope.checkProduct = function (value) {
                var found = false;
                if (value.type == "PRODUCT") {
                    AppUtil.getUnitDetails().products.map(function (item) {
                        if (item.id == value.productId) {
                            found = true;
                            if (AppUtil.isPaidEmployeeMeal()) {
                                var empMealPrice = AppUtil.getUnitDetails().empMealPrices[item.id + '_' + item.prices[0].dimension];
                                if (empMealPrice != null) {
                                    found = true;
                                } else {
                                    found = false;
                                }
                            }
                        }
                    });
                } else {
                    found = true;
                }
                return found && (!$scope.paidAddonsNeedsToMergedWithSugar(value) && !AppUtil.isEmptyObject($scope.unitsProductMap[value.productId]) &&
                    $scope.unitsProductMap[value.productId].prices !== undefined && $scope.unitsProductMap[value.productId].prices != null && $scope.unitsProductMap[value.productId].prices.length > 0);
            };

            $scope.filterAllPaidAddonsMergedWithSugar = function (allPaidAddons) {
                  return allPaidAddons.filter(function (addOn) {
                      return $scope.paidAddonsNeedsToMergedWithSugar(addOn);
                  });
            };

            $scope.paidAddonsNeedsToMergedWithSugar = function (paidAddon) {
                var paidAddonsNeedToMergeWithSugar = $scope.checkForPaidAddonsNeedsToMergeWithSugar() ? AppUtil.getAllCafeAppProperties().paidAddonsNeedToMergeWithSugar : [];
                return paidAddonsNeedToMergeWithSugar.includes(paidAddon.productId.toString()) && !AppUtil.isEmptyObject($scope.unitsProductMap[paidAddon.productId]) &&
                    $scope.unitsProductMap[paidAddon.productId].prices !== undefined && $scope.unitsProductMap[paidAddon.productId].prices != null && $scope.unitsProductMap[paidAddon.productId].prices.length > 0;
            };

            $scope.checkForPaidAddonsNeedsToMergeWithSugar = function () {
                var allCafeProperties = AppUtil.getAllCafeAppProperties();
                return allCafeProperties != null && !AppUtil.isEmptyObject(allCafeProperties.paidAddonsNeedToMergeWithSugar);
            };

            $scope.checkRedemption = function (value) {
                if($scope.orderItem.orderDetails.hasBeenRedeemed == true){
                    return value.id != 80;
                }else{
                    return true
                }
            };

            $scope.cancelCustomize = function () {
                if($scope.selectedOrderItem.isHeartButtonClicked!=undefined && $scope.orderItem.isHeartButtonClicked){
                    $scope.selectedOrderItem.isHeartButtonClicked=undefined;
                    if($scope.favChaiInsideModal!=undefined && $scope.favChaiInsideModal){
                        $scope.favChaiInsideModal=false;
                        $scope.orderItem.isFavChaiMarked=$scope.favChaiInsideModal;
                    }
                }
                orderItem = $scope.selectedOrderItem;
                $modalInstance.dismiss('cancel');
            };

            $scope.checkFavChaiMarked = function () {
                if($scope.favChaiInsideModal==undefined){
                    $scope.favChaiInsideModal=true
                }else{
                    $scope.favChaiInsideModal=!$scope.favChaiInsideModal;
                }
                $scope.selectedOrderItem.isFavChaiMarked =$scope.favChaiInsideModal;
                $scope.selectedOrderItem.isHeartButtonClicked = true;
                return $scope.favChaiInsideModal;
            }

            $scope.updateFavChai=function(){
                $scope.selectedOrderItem.isFavChaiMarked=!$scope.selectedOrderItem.isFavChaiMarked;
            }

            $scope.getProductAliasNameIfPresent = function(name,aliasName){
                if(aliasName !== undefined && aliasName != null){
                     return aliasName;
                }
                return name;
            };

            $scope.submit = function () {
                /*if(AppUtil.checkIsDeliveryOnlyProduct($scope.selectedOrderItem.productDetails)){
                    $modalInstance.close($scope.selectedOrderItem);
                    bootbox.alert("This is a delivery only product !");
                    return;
                }*/
                console.log($scope.complimentaryDetail);
                $scope.selectedOrderItem.orderDetails.complimentaryDetail = $scope.complimentaryDetail;
                $scope.selectedOrderItem.isModified = true;
                $scope.selectedOrderItem.orderDetails.hasBeenRedeemed = $scope.orderItem.orderDetails.hasBeenRedeemed;
                $scope.selectedOrderItem.recipeDetails.ingredient.variants.map(function (variant) {
                    var key = "";
                    variant.details.map(function (detail) {
                        key = key + detail.alias;
                    });
                    variant.details.map(function (detail) {
                        detail.selected = ($scope.selectedVariantMap[key] == detail.alias);
                        if (detail.alias.toLocaleLowerCase().includes('no sugar')) {
                            if ($scope.selectedVariantMap[key] === "SELECTED_PAID_ADDON") {
                                detail.selected = detail.alias;
                            }
                        }
                    });
                });
                $scope.selectedOrderItem.recipeDetails.ingredient.products.map(function (product) {
                    product.details.map(function (detail) {
                        detail.selected = ($scope.selectedProductsMap[product.display] == detail.product.productId);
                    });
                });
                $scope.selectedOrderItem.recipeDetails.addons.map(function (addon) {
                    addon.selected = ($scope.selectedAddonsList.indexOf(addon.product.productId) >= 0);
                });
               var foundOptions = [];
               var foundOptionName = [];
                $scope.selectedOrderItem.recipeDetails.options.map(function (option) {
                     if($scope.selectedOptionsList.indexOf(option.id) >= 0) {
                         option.selected = true;
                         foundOptions.push(option.id);
                         foundOptionName.push(option.name);
                     } else {
                         option.selected = false;
                     }
                });
                // removing other options which doesn't exist in current recipe
                angular.forEach($scope.selectedOptionsList, function (selectedOption) {
                     if (foundOptions.indexOf(selectedOption) < 0) {
                         $scope.selectedOptionsList.splice($scope.selectedOptionsList.indexOf(selectedOption), 1);
                         $scope.checkIfPresentInCustomizationList(selectedOption.name,false);
                     }
                });

                angular.forEach($scope.customizationsList, function (selectedOption) {
                    if (foundOptionName.indexOf(selectedOption) < 0) {
                        $scope.checkIfPresentInCustomizationList(selectedOption,false);
                    }
                });

                $scope.selectedOrderItem = angular.copy(AppUtil.calculateCustomization($scope.selectedOrderItem, $scope.itemIndex));
                //remove all product type options
                var length = 0;
                $scope.$parent.orderItemArray.map(function (item) {
                    if (item.productDetails.parentProductId == $scope.selectedOrderItem.orderDetails.itemId) {
                        length++;
                    }
                });
                for (var i = 0; i < length; i++) {
                    for (var j = 0; j < $scope.$parent.orderItemArray.length; j++) {
                        if ($scope.$parent.orderItemArray[j].productDetails.parentProductId == $scope.selectedOrderItem.orderDetails.itemId) {
                            var ind = $scope.$parent.orderItemArray.length - (j + 1);
                            $scope.$parent.deleteItem(ind);
                            break;
                        }
                    }
                }
                if ($scope.selectedOrderItem.recipeDetails.options != null && $scope.selectedOrderItem.recipeDetails.options.length > 0) {
                    for (var i in $scope.selectedOrderItem.recipeDetails.options) {
                        if ($scope.selectedOrderItem.recipeDetails.options[i].selected &&
                            $scope.selectedOrderItem.recipeDetails.options[i].type == "PRODUCT") {
                            var found = false;
                            $scope.$parent.orderItemArray.map(function (item) {
                                if (item.productDetails.parentProductId == $scope.selectedOrderItem.orderDetails.itemId &&
                                    item.productDetails.id == $scope.selectedOrderItem.recipeDetails.options[i].productId) {
                                    found = true;
                                }
                            });
                            if (!found) {
                                productService.addNewProductToOrderItemArrayByProductId($scope.selectedOrderItem.recipeDetails.options[i].productId,
                                    $scope.selectedOrderItem.orderDetails.itemId, $scope.selectedOrderItem.productDetails.name, $scope.selectedOrderItem.orderDetails.quantity);
                            }
                        }
                    }
                }
                console.log("OrderItem on submitting inside modal",$scope.selectedOrderItem);
                $modalInstance.close($scope.selectedOrderItem);
            };

        }]);
