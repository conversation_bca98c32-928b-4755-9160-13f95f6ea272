/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

angular.module('posApp').controller('orderSearchController',
    ['$location', '$scope', '$rootScope', 'posAPI', 'AppUtil', '$modal', 'PrintService', 'subscriptionService','$window','$http',
        function ($location, $scope, $rootScope, posAPI, AppUtil, $modal, PrintService, subscriptionService,$window,$http) {
            $scope.searchText = null;
            $scope.externalOrderId = null;
            $scope.showCancel = AppUtil.showCancel;
            $scope.showUnsatisfiedCustomer = AppUtil.OrderObj != undefined && AppUtil.OrderObj != null && AppUtil.OrderObj.status != 'CANCELLED' && AppUtil.OrderObj.source != 'COD' ;
            $scope.showEditSettlement = AppUtil.showEditSettlement;
            $scope.isCOD = AppUtil.isCOD();
            $scope.dataLoading = false;
            $scope.currentUser = $rootScope.globals.currentUser;
            $scope.forceCancel = false;
            $scope.convertTimeFromIndex = subscriptionService.convertTimeFromIndex;
            $scope.convertWeekDayFromIndex = subscriptionService.convertWeekDayFromIndex;
            $scope.joinArray = subscriptionService.joinArray;
            $scope.getPreviousDate = subscriptionService.getPreviousDateTimestamp;
            $scope.isGiftCardOrder = true;
            $scope.orderSourceName = "";
            $scope.chaayosCashOrder = false;
            $scope.orderPaymentObject = [];
            $scope.invoiceDetail={};
            $scope.isGstClicked=false;
            $scope.unitDetails=AppUtil.getUnitDetails();
            $scope.showAction = true;
            $scope.orderDetailString = [];
            $scope.customerDetailString = [];
            $scope.is40MinTimeLimitExceed = false;
            $scope.showRejectionModal = false;
            $scope.rejectionReasons = [];
            $scope.isMoreThan45Minutes=false;
            $scope.rejectionObject = {
                id: null,
                message: null
            };
            if($scope.OrderObj != undefined && $scope.OrderObj.billingServerTime != undefined){
                console.log("billingServerTime"+$scope.OrderObj.billingServerTime);
                var orderTime = new Date($scope.OrderObj.billingServerTime); // Convert timestamp to Date
                var currentTime = new Date();
                if((currentTime - orderTime) / (1000 * 60) > 45){
                    $scope.isMoreThan45Minutes = true;
                }else{
                    $scope.isMoreThan45Minutes = false;
                }
            }
            $scope.rejectionItems = [];
            var aclData = $rootScope.aclData;
            $scope.hasOrderUpdationAdminAccess = false;
            if(aclData!=null){
               if(aclData.action!=null && aclData.action["ODUADMN"]!=null) {
                           $scope.hasOrderUpdationAdminAccess = true;
                     }
            }
            $scope.isEmptyObject = function (obj) {
                ////console.log("inside is empty ::: "+obj);
                if (obj != undefined)
                    return AppUtil.isEmptyObject(obj);
                else
                    return true;
            };

            if (!AppUtil.isEmptyObject(AppUtil.OrderObj)) {
                $scope.OrderObj = AppUtil.OrderObj;
                $scope.OrderObj.brand = AppUtil.getBrandByBrandId($scope.OrderObj.brandId);
                AppUtil.OrderObj = {}; // clearing object from AppUtil
                $scope.searchText = $scope.OrderObj.generateOrderId;
                if ($scope.OrderObj.source == "COD") {
                    $scope.showCancel = AppUtil.unitFamily == "COD";
                } else if ($scope.OrderObj.source != "COD") {
                    $scope.showCancel = $scope.OrderObj.unitId == $rootScope.globals.currentUser.unitId;
                }

                checkOnlineModeCategory();
                setOrderSourceName();
            } else {
                $scope.OrderObj = {};
            }

            if ($scope.OrderObj != undefined) {
                $scope.settlement = AppUtil.getSettlements($scope.OrderObj);
            }

            if ($scope.OrderObj != undefined && $scope.OrderObj.orders != undefined) {
                for (var i = 0; i < $scope.OrderObj.orders.length; i++) {
                    if ($scope.OrderObj.orders[i].code != "GIFT_CARD") {
                        $scope.isGiftCardOrder = false;
                        break;
                    }
                }
            }

            if($scope.OrderObj != undefined && $scope.OrderObj.orders != undefined){
                var currentTime = new Date().getTime();
                var timeLimit = 40 * 60 * 1000;
                if(currentTime <= ($scope.OrderObj.billingServerTime + timeLimit)){
                    $scope.is40MinTimeLimitExceed = false;
                }else{
                    $scope.is40MinTimeLimitExceed = true;
                }
            }

            if ($scope.OrderObj != undefined && $scope.OrderObj.orders != undefined) {
                $scope.chaayosCashOrder = $scope.OrderObj.offerCode == "CHAAYOS_CASH";
            }

            $rootScope.$watch('CustomerObj', function (newValue, oldValue) {
                $scope.CustomerObj = $rootScope.CustomerObj;
                if ($scope.OrderObj.source == "COD") {
                    $scope.showCancel = AppUtil.unitFamily == "COD";
                } else if ($scope.OrderObj.source != "COD") {
                    $scope.showCancel = $scope.OrderObj.unitId == $rootScope.globals.currentUser.unitId;
                }
                $scope.showLoading = false;
            });

            $scope.cancelDelivery = function (generatedOrderId) {
                //console.log("inside cancel delivery ticket function");
                bootbox.confirm("Are you sure that you want to cancel this delivery ticket?", function (result) {
                    if (result == true) {
                        var reqObj = AppUtil.GetRequest(generatedOrderId);
                        posAPI.allUrl('/', AppUtil.restUrls.delivery.cancel).all(generatedOrderId).post(reqObj)
                            .then(function (response) {
                                if (response != null) {
                                    response = response.plain();
                                    if (response.deliveryStatus == '-2' || response.deliveryStatus == -2 || response.failureCode != null) {
                                        AppUtil.myAlert(response.failureMessage);
                                    } else {
                                        $rootScope.deliveryObject = null;
                                        AppUtil.myAlert("Cancelled the delivery on this order");
                                    }
                                }
                            }, function (err) {
                                AppUtil.myAlert(err.data.errorMessage);
                            });
                    }
                });
            };

            ////console.log(AppUtil.showCancel);
            $scope.getOrderItems = function () {
                var orderItems = [];
                for (var i in $scope.OrderObj.orders) {
                    $scope.OrderObj.orders[i].isComboItem = false;
                    orderItems.push($scope.OrderObj.orders[i]);
                    if ($scope.OrderObj.orders[i].composition != null
                        && $scope.OrderObj.orders[i].composition.menuProducts != null
                        && $scope.OrderObj.orders[i].composition.menuProducts.length > 0) {
                        for (var j in $scope.OrderObj.orders[i].composition.menuProducts) {
                            $scope.OrderObj.orders[i].composition.menuProducts[j].isComboItem = true;
                            orderItems.push($scope.OrderObj.orders[i].composition.menuProducts[j]);
                        }
                    }
                }
                return orderItems;

            };

            $scope.cancelBtnVisibility = function(isForceCancel) {
                if(isForceCancel) {
                    if($scope.hasOrderUpdationAdminAccess  && $scope.orderType!='subscription' &&
                        $scope.OrderObj.billBookNo==null && !$scope.chaayosCashOrder && $scope.showAction &&
                        $scope.OrderObj.orderRefundDetailId == null )
                        {
                            return true;
                        }
                    return false;
                }
                if( $scope.showCancel && $scope.orderType!='subscription' && $scope.OrderObj.billBookNo==null &&
                    !$scope.chaayosCashOrder && $scope.showAction && $scope.OrderObj.orderRefundDetailId == null )
                    {
                        return true;
                    }
                return false;
            }

            $scope.unsatisfiedCustomerOrder = function (customerType) {

                if(customerType === 'Unsatisfied Customer') {
                    $rootScope.orderType = "unsatisfied-customer-order";
                } else {
                    $rootScope.orderType = "PPE";
                }

                // //console.log('start');
                if($rootScope.orderType == "PPE" && ($scope.OrderObj.source == 'CAFE' || $scope.OrderObj.source == 'TAKE_AWAY' || $scope.OrderObj.source == 'COD')) {
                    AppUtil.getUnitProductsData();
                    $rootScope.orderIdforUnsatisfiedOrder = $scope.OrderObj.orderId;
                    $location.url('/pos');
                } else if (!AppUtil.isCOD() && ($scope.OrderObj.source == 'CAFE' || $scope.OrderObj.source == 'TAKE_AWAY')) {
                    AppUtil.getUnitProductsData();
                    $rootScope.orderIdforUnsatisfiedOrder = $scope.OrderObj.orderId;
                    $location.url('/pos');
                    //}else if($scope.OrderObj.source = 'COD'){
                } else if (AppUtil.isCOD() && $scope.OrderObj.source == 'COD') {
                    AppUtil.freeKettle = false;
                    $rootScope.isPartnerOrder = false;
                    $rootScope.orderIdforUnsatisfiedOrder = $scope.OrderObj.orderId;
                    $location.url('/CODCSlookup');
                }
            };

            $scope.getDeliveryTime = function (orderId) {
                $rootScope.showFullScreenLoader = true;
                posAPI.allUrl('/', AppUtil.restUrls.partnerOrder.getSwiggyRiderTimeOfArrival).post(orderId)
                    .then(function (response) {
                        if (response.plain() != null) {
                            bootbox.alert("Delivery time is " + response.plain() + " minutes");
                        } else {
                            bootbox.alert("Error getting delivery time. Try again later!");
                        }
                        $rootScope.showFullScreenLoader = false;
                    }, function (err) {
                        AppUtil.myAlert(err.data.errorMessage);
                        $rootScope.showFullScreenLoader = false;
                    });
            };

            $scope.getDeliveryStatusData = function (orderId) {
                $rootScope.showFullScreenLoader = true;
                posAPI.allUrl('/', AppUtil.restUrls.partnerMetadataManagement.getDeliveryStatus).post(orderId)
                    .then(function (response) {
                        $scope.deliveryStatus = response.plain();
                        if ($scope.deliveryStatus != null && $scope.deliveryStatus.length > 0) {
                            $scope.deliveryStatusModalOpen();
                        } else {
                            bootbox.alert("Delivery Status not available yet.")
                        }
                        $rootScope.showFullScreenLoader = false;
                    }, function (err) {
                        AppUtil.myAlert(err.data.errorMessage);
                        $rootScope.showFullScreenLoader = false;
                    });
            };

            $scope.getOrderDetailData = function (orderObject) {
                $rootScope.showFullScreenLoader = true;
                posAPI.allUrl('/', AppUtil.restUrls.externalOrderManagement.getPartnerOrderDetail).customGET("", {
                    partnerSourceId: orderObject.generateOrderId,
                    issueType: "",
                }).then(function (response) {
                    if (response) {
                        $("#getOrderDetail").modal("show");
                        $scope.orderDetailString = response.split('\\n')
                        $scope.orderDetailString = $scope.orderDetailString.slice(4, $scope.orderDetailString.length - 4);
                        $rootScope.showFullScreenLoader = false;
                    }
                    else {
                        $rootScope.showFullScreenLoader = false;
                        alert("No Order Found");
                    }
                }, function (err) {
                    console.log("NO")
                    $rootScope.showFullScreenLoader = false;
                });

            };
            $scope.onCloseOrderDetail = function () {
                $scope.orderDetailString = [];
                $("#getOrderDetail").modal("hide");
            }
            $scope.deliveryStatusModalOpen = function () {
                $modal.open({
                    animation: true,
                    templateUrl: window.version + 'views/deliveryStatusModal.html',
                    controller: 'ModalInstanceCtrl',
                    scope: $scope,
                    size: 'lg'
                });
            };

            $scope.getCustomerDetailData = function (orderObject) {
                $rootScope.showFullScreenLoader = true;
                posAPI.allUrl('/', AppUtil.restUrls.externalOrderManagement.getCustomerDetail).customGET("", {
                    partnerSourceId: orderObject.generateOrderId
                }).then(function (response) {
                    if (response) {
                        $("#customerDetailModal").modal("show");
                        $scope.customerDetailString = response.split('\\n');
                        $rootScope.showFullScreenLoader = false;
                    }
                    else{
                        $rootScope.showFullScreenLoader = false;
                        alert("No customer Found");
                    }
                }, function (err) {
                    console.log("NO")
                    $rootScope.showFullScreenLoader = false;
                    $scope.customerDetailString = ["No Data Found for customer"]
                });

            };

            $scope.onCloseCustomerDetail = function () {
                $scope.customerDetailString = [];
                $("#customerDetailModal").modal("hide");
            }

            $scope.GetSearchedOrder = function () {
                $scope.orderPaymentObject = [];
                $scope.externalOrderId = null;
                $scope.forceCancel = false;
                //var reqObj = AppUtil.GetRequest($scope.searchText);
                $rootScope.showFullScreenLoader = true;
                console.log("206   "+$scope.searchText);
                $scope.invoiceDetail.orderId=$scope.searchText;
                $scope.invoiceDetail.generatedBy=$scope.currentUser.userName;
                console.log( $scope.currentUser);
                posAPI.allUrl('/', AppUtil.restUrls.order.generatedOrder).post($scope.searchText)
                    .then(function (response) {
                        $scope.OrderObj = {};
                        if (!angular.isUndefined(response.errorType)) {
                            AppUtil.myAlert(response.errorMessage);
                            $scope.orderSourceName = "";
                        } else {
                            $scope.OrderObj = response.plain();
                            $scope.OrderObj.brand = AppUtil.getBrandByBrandId($scope.OrderObj.brandId);

                            var orderTime = new Date($scope.OrderObj.billingServerTime); // Convert timestamp to Date
                            var currentTime = new Date();
                            if((currentTime - orderTime) / (1000 * 60) > 45){
                                $scope.isMoreThan45Minutes = true;
                            }else{
                                $scope.isMoreThan45Minutes = false;
                            }

                            setOrderSourceName();
                            $scope.showEditSettlement = (($scope.OrderObj.source == 'CAFE' || $scope.OrderObj.source == 'TAKE_AWAY') && $scope.OrderObj.orderType == 'order' && $scope.OrderObj.status == 'SETTLED');

                            if ($scope.OrderObj.source == "COD") {
                                $scope.showCancel = AppUtil.unitFamily == "COD";
                            } else if ($scope.OrderObj.source != "COD") {
                                $scope.showCancel = $scope.OrderObj.unitId == $rootScope.globals.currentUser.unitId;
                            }

                            //not filling AppUtil here because of check in back function
                            if ($scope.OrderObj.deliveryAddress != null && $scope.OrderObj.deliveryAddress > 0) {
                                $scope.GetCustomerInfo($scope.OrderObj.deliveryAddress);
                            } else if (!AppUtil.isEmptyObject($scope.CustomerObj)) {
                                $scope.CustomerObj = {};
                            }
                            $scope.settlement = AppUtil.getSettlements($scope.OrderObj);
                            checkOnlineModeCategory();
                            $scope.showAction = true;
                        }
                        $rootScope.showFullScreenLoader = false;
                    }, function (err) {
                        AppUtil.myAlert(err.data.errorMessage);
                        $rootScope.showFullScreenLoader = false;

                    });
            };

            $scope.GetSearchedGst = function () {
                var date = new Date($scope.OrderObj.billingServerTime);
                var month = date.getMonth();
                var year = date.getFullYear();
                var fy = year % 2000;
                if (month > 2) {
                    fy = fy + 1;
                }

                var financialYearEndDate = new Date(2000 + fy, 8, 30);
                var financialYearStartDate = new Date(2000 + fy - 1, 3, 1);

                var currentDate = new Date();
                if (currentDate >= financialYearStartDate && currentDate <= financialYearEndDate) {
                    $scope.isGstClicked = true;
                    console.log($scope.isGstClicked);
                    window.open('https://services.gst.gov.in/services/searchtp', '_blank');
                }

                else {
                    var monthNames = ["January", "February", "March", "April", "May", "June",
                        "July", "August", "September", "October", "November", "December"
                    ];
                    bootbox.alert("Exceeded the date to issue invoice bill, could be filed between " + financialYearStartDate.getDate() + " " + monthNames[financialYearStartDate.getMonth()] + " " + financialYearStartDate.getFullYear() + " and " + financialYearEndDate.getDate() + " " + monthNames[financialYearEndDate.getMonth()] + " " + financialYearEndDate.getFullYear());
                }

            }

            $scope.addInvoiceDialog = function () {
                $scope.isAddNewOffer = true;
                $scope.invoiceDetail.orderId=$scope.searchText;
                $scope.invoiceDetail.generatedBy=$scope.currentUser.userName;
            }

            $scope.addCrmScreenDetail = function (invoiceDetail) {
                if (!invoiceDetail.companyName) {
                    alert("please enter company  Name")
                    return;
                }
                if (!invoiceDetail.companyAddress) {
                    alert("please enter company Address")
                    return;
                }
                if (!invoiceDetail.gst) {
                    alert("please enter gst")
                    return;
                }
                if (!invoiceDetail.orderId) {
                    alert("please enter orderId")
                    return;
                }
                if (!invoiceDetail.generatedBy) {
                    alert("please enter generator's name")
                    return;
                }

                invoiceDetail.orderId=$scope.searchText;
                console.log(invoiceDetail.orderId);
                console.log("Unit Details",$scope.unitDetails);
                var invoiceDetail = {
                   companyName: invoiceDetail.companyName,
                   companyAddress: invoiceDetail.companyAddress,
                    gst: invoiceDetail.gst,
                    orderId: invoiceDetail.orderId,
                    generatedBy:  invoiceDetail.generatedBy,
                    stateCode: $scope.unitDetails.location.state.code,
                };

                console.log(invoiceDetail);
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.order.addInvoiceDetails,
                    data: invoiceDetail
                }).then(function success(response) {
                    if (response.status === 200 && response.data.length==0) {
                        console.log(response);
                        alert("Exceeded the date to issue invoice bill");
                        $rootScope.showFullScreenLoader = false;
                    }
                    else if(response.status === 200){
                        console.log(response);
                        alert("Added Successfully");
                        $rootScope.showFullScreenLoader = false;
                    }

                    else {
                        alert("Error to Add Data!!!");
                        $rootScope.showFullScreenLoader = false;
                    }
                }, function error(response) {
                    alert("Error to Add Data!!!");
                    $rootScope.showFullScreenLoader = false;
                });
                $scope.invoiceDetail={};
            }

            function checkOnlineModeCategory() {
                for (var i = 0; i < $scope.OrderObj.settlements.length; i++) {
                    if ($scope.OrderObj.settlements[i].modeDetail.category == 'ONLINE') {
                        getPaymentStatus();
                    }
                }
            }

            function getPaymentStatus() {

                $rootScope.showFullScreenLoader = true;

                posAPI.allUrl('/', AppUtil.restUrls.order.getPaymentStatus).post($scope.OrderObj.orderId)
                    .then(function (response) {
                        console.log('response getPaymentStatus ', response);
                        $scope.orderPaymentObject.push(response);
                        $rootScope.showFullScreenLoader = false;
                    }, function (err) {
                        AppUtil.myAlert(err.data.errorMessage);
                        $rootScope.showFullScreenLoader = false;

                    });
            }

            $scope.GetPartnerSearchedOrder = function () {
                $scope.searchText = null;
                $scope.forceCancel = false;
                var reqObj = {
                    id: 6,
                    name: 'SWIGGY',
                    code: $scope.externalOrderId
                }
                $rootScope.showFullScreenLoader = true;
                posAPI.allUrl('/', AppUtil.restUrls.order.partnerOrder).post(reqObj)
                    .then(function (response) {
                        $scope.OrderObj = {};
                        if (!angular.isUndefined(response.errorType)) {
                            AppUtil.myAlert(response.errorMessage);
                            $scope.orderSourceName = "";
                        } else {
                            $scope.OrderObj = response.plain();
                            $scope.OrderObj.brand = AppUtil.getBrandByBrandId($scope.OrderObj.brandId);
                            //console.log("$scope.OrderObj",$scope.OrderObj);
                            setOrderSourceName();
                            $scope.showEditSettlement = (($scope.OrderObj.source == 'CAFE' || $scope.OrderObj.source == 'TAKE_AWAY') && $scope.OrderObj.orderType == 'order' && $scope.OrderObj.status == 'SETTLED');

                            if ($scope.OrderObj.source == "COD") {
                                $scope.showCancel = AppUtil.unitFamily == "COD";
                            } else if ($scope.OrderObj.source != "COD") {
                                $scope.showCancel = $scope.OrderObj.unitId == $rootScope.globals.currentUser.unitId;
                            }

                            //not filling AppUtil here because of check in back function
                            if ($scope.OrderObj.deliveryAddress != null && $scope.OrderObj.deliveryAddress > 0) {
                                $scope.GetCustomerInfo($scope.OrderObj.deliveryAddress);
                            } else if (!AppUtil.isEmptyObject($scope.CustomerObj)) {
                                $scope.CustomerObj = {};
                            }
                            $scope.settlement = AppUtil.getSettlements($scope.OrderObj);
                            $scope.showAction = false;
                        }
                        $rootScope.showFullScreenLoader = false;
                    }, function (err) {
                        AppUtil.myAlert(err.data.errorMessage);
                        $rootScope.showFullScreenLoader = false;

                    });
            };

            function setOrderSourceName() {
                for (var i = 0; i < AppUtil.getTransactionMetadata().channelPartner.length; i++) {
                    if (AppUtil.getTransactionMetadata().channelPartner[i].id == $scope.OrderObj.channelPartner) {
                        $scope.orderSourceName = AppUtil.getTransactionMetadata().channelPartner[i].name;
                        break;
                    } else if ($scope.OrderObj.channelPartner == 1) {
                        $scope.orderSourceName = "Chaayos (Dine In)";
                        break;
                    }
                }
            }

            $scope.GetCustomerInfo = function ($deliveryAddressId) {
                //var reqObj = AppUtil.GetRequest($deliveryAddressId+"");
                posAPI.allUrl('/', AppUtil.restUrls.customer.lookupAddress).post($deliveryAddressId)
                    .then(function (response) {
                        //console.log(response);
                        $scope.CustomerObj = response.plain();
                        $scope.CustomerObj.addresses.map(function (address) {
                            if (address.id == $deliveryAddressId) {
                                $scope.deliveryAddress = address;
                            }
                        });
                        $rootScope.showFullScreenLoader = false;
                    }, function (err) {
                        AppUtil.myAlert(err.data.errorMessage);
                        $rootScope.showFullScreenLoader = false;
                    });
            };


            $scope.cancelOrderModalOpen = function (cancelForcibly) {
                if ($scope.OrderObj.orders[0].productName == "GYFTR") {
                    AppUtil.myAlert("GYFTR voucher order cannot be cancelled !");
                    return false;
                }

                if($scope.OrderObj.orders[0].productId == 3){
                    AppUtil.myAlert("Advance Payment order cannot be cancelled !");
                    return false;
                }

                $scope.forceCancel = cancelForcibly;
                var cancelRequested = $scope.OrderObj.status != "CANCELLED_REQUESTED";
                var cancelled = $scope.OrderObj.status !== "CANCELLED";
                var isCOD = AppUtil.isCOD();
                var isTA = AppUtil.isTakeaway();
                var openModal = false;
                if (!isCOD && !isTA) {
                    if (cancelled) {
                        var openModal = true;
                    }
                } else {
                    if (cancelled && cancelRequested) {
                        var openModal = true;
                    }
                }

                if (openModal) {
                    $modal.open({
                        animation: true,
                        templateUrl: window.version + 'views/cancelOrderModal.html',
                        controller: 'ModalInstanceCtrl',
                        scope: $scope,
                        size: 'md'
                    });
                } else {
                    if (!isCOD && !isTA)
                        AppUtil.myAlert('Order is already cancelled');
                    else
                        AppUtil.myAlert('Order cancellation is already requested');
                }
            };

            $scope.showServiceCharge = function() {
                if($scope.OrderObj.transactionDetail.serviceCharge == null ||
                 $scope.OrderObj.channelPartner != 1 || $scope.OrderObj.orderRefundDetailId != null) {
                    return false;
                }
                return true;
            }

            $scope.serviceChargeRefundModalOpen = function() {
                var modalInstance = $modal.open({
                    animation: true,
                    templateUrl: window.version + 'scripts/modules/States/serviceChargeRefundModal.html',
                    controller: 'serviceChargeRefundModalCtrl',
                    scope: $scope,
                    size: 'md'
                });

                 modalInstance.result.then(function () {
                    $scope.GetSearchedOrder();
                }, function (err) {
                        console.log("Error : ", err);
                        AppUtil.myAlert('Error while processing service charge refund');
                 });

            }


            /**
             * Reprint with authentication
             */
            /*	$scope.reprint = function (type) {
                    if (!$scope.isEmptyObject($scope.OrderObj)) {
                        ////console.log("Print Count :"+$scope.OrderObj.printCount);
                        if($scope.OrderObj.printCount < 2   || type=='KOT'){
                            var modalInstance = $modal.open({
                                animation: true,
                                templateUrl: window.version+'views/loginModal.html',
                                controller: 'LoginModalCtrl',
                                backdrop: 'static',
                                size: 'sm',
                                resolve: {
                                    order: function () {
                                        var orderObject;
                                        if(type=='KOT'){
                                            orderObject	= {
                                                    text:$scope.searchText,
                                                    isReprintKOT:true
                                            };
                                        }else{
                                            orderObject	= {
                                                    text:$scope.searchText,
                                                    isReprint:true
                                            };
                                        }
                                        return orderObject;
                                    },
                                    orderItemArray: function(){
                                        return [];
                                    },
                                    employeeMealUser : function(){
                                        return null;
                                    }
                                }
                            });

                            modalInstance.result.then(function () {
                                $scope.GetSearchedOrder();
                            }, function (err) {

                            });
                        } else {
                            AppUtil.myAlert('Reprints for orders are limited to only once');
                        }

                    } else {
                        AppUtil.myAlert('Please fill a valid order id');
                    }

                };*/

            $scope.reprint = function (type) {
                if (!$scope.isEmptyObject($scope.OrderObj)) {
                    ////console.log("Print Count :"+$scope.OrderObj.printCount);
                    if ($scope.OrderObj.printCount < 2 || type == 'KOT') {
                        $rootScope.showFullScreenLoader = true;
                        if (type == 'KOT') {
                            AppUtil.reprintOrderKOT($scope.searchText);
                        } else {
                            AppUtil.reprintOrder($scope.searchText).then(function () {
                                $scope.GetSearchedOrder();
                            });
                        }
                    } else {
                        AppUtil.myAlert('Reprints for orders are limited to only once');
                    }

                } else {
                    AppUtil.myAlert('Please fill a valid order id');
                }

            };

            $scope.reprintSettlementSlip = function () {
                if (!$scope.isEmptyObject($scope.OrderObj)) {
                    $rootScope.showFullScreenLoader = true;
                    AppUtil.reprintSettlementSlip($scope.OrderObj.tableRequestId).then(function () {
                        AppUtil.mySuccessAlert("Settlement Reprint Successful.")
                    });
                } else {
                    AppUtil.myAlert('Please fill a valid order id');
                }
            };

            $scope.resendEmail = function () {
                if (!$scope.isEmptyObject($scope.OrderObj)) {
                    $modal.open({
                        animation: true,
                        templateUrl: window.version + 'views/loginModal.html',
                        controller: 'LoginModalCtrl',
                        backdrop: 'static',
                        size: 'sm',
                        resolve: {
                            order: function () {
                                var orderObject = {
//                                    text: $scope.searchText,
                                    orderId : $scope.OrderObj.orderId,
                                    isReprint: false,
                                    customerId : $scope.OrderObj.customerId,
                                    entryType : $scope.OrderObj.orderType,
                                    resendEmail : true
                                };
                                return orderObject;
                            },
                            orderItemArray: function () {
                                return [];
                            },
                            employeeMealUser: function () {
                                return null;
                            }
                        }
                    });
                } else {
                    AppUtil.myAlert('Please fill a valid order id');
                }
            };

            $scope.backToCover = function () {
                if (AppUtil.previousLocation[AppUtil.previousLocation.length - 2] == '/orderSummary') {
                    $location.url('/orderSummary');
                } else if (AppUtil.previousLocation[AppUtil.previousLocation.length - 2] == '/customerOrders') {
                    $location.url('/customerOrders');
                } else if (AppUtil.previousLocation[AppUtil.previousLocation.length - 2] == '/openOrderSummary') {
                    $location.url('/openOrderSummary');
                } else if (AppUtil.previousLocation[AppUtil.previousLocation.length - 2] == '/subscriptionSearch') {
                    $location.url('/subscriptionSearch');
                } else if (AppUtil.previousLocation[AppUtil.previousLocation.length - 2] == '/subscriptionOrderSearch') {
                    $location.url('/subscriptionOrderSearch');
                } else if (AppUtil.previousLocation[AppUtil.previousLocation.length - 2] == '/subscriptionOrderByUnit') {
                    $location.url('/subscriptionOrderByUnit');
                } else if (AppUtil.previousLocation[AppUtil.previousLocation.length - 2] == '/openTakeawayOrders') {
                    $location.url('/openTakeawayOrders');
                } else if (AppUtil.previousLocation[AppUtil.previousLocation.length - 2] == '/tableSummary') {
                    $location.url('/tableSummary');
                } else {
                    if (!AppUtil.isCOD()) {
                        $location.url('/cover');
                    } else {
                        $location.url('/CODCover');
                    }
                }
            };

            $scope.getAddress = function () {
                return AppUtil.arrangeAddress($scope.deliveryAddress, true);
            };

            $scope.editSettlement = function (settlement) {
                if (!$scope.isEmptyObject($scope.OrderObj)) {
                    var modalInstance = $modal.open({
                        animation: true,
                        templateUrl: 'views/editSettlementModal.html',
                        controller: 'editSettlementModalCtrl',
                        backdrop: 'static',
                        size: 'sm',
                        resolve: {
                            orderUnitId: function () {
                                return $scope.OrderObj.unitId;
                            },
                            orderDataId: function () {
                                return $scope.OrderObj.orderId;
                            },
                            settlementData: function () {
                                return settlement;
                            },
                            orderSource: function () {
                                $scope.OrderObj.source
                            }

                        }
                    });
                    modalInstance.result.then(function () {
                        $scope.GetSearchedOrder();
                    }, function (err) {

                    });
                } else {
                    AppUtil.myAlert('Please fill a valid order id');
                }
            }

            $scope.addDeliveryDetails=function () {
                var url=AppUtil.getDeliveryDetails()+"unit="+encodeURIComponent($scope.OrderObj.unitName)+"&order="+$scope.OrderObj.generateOrderId;
                $window.open(url,'_blank');
            };

            $scope.removeFromAssembly = function (orderObj) {
                $rootScope.showFullScreenLoader = true;
                posAPI.allUrl('/', AppUtil.restUrls.order.removeFromCache).customGET(orderObj.generateOrderId, {})
                    .then(function (response) {
                        $rootScope.showFullScreenLoader = false;
                    }, function (err) {
                        $rootScope.showFullScreenLoader = false;
                        AppUtil.myAlert(err.data.errorMessage);
                    });
            }

            $scope.openRejectOrderModal = function (isSwiggyOrder, isZomatoOrder) {
                $scope.showRejectionModal = true;
                if (isSwiggyOrder) {
                    $scope.getRejectionReasonsSwiggy();
                } else if (isZomatoOrder) {
                    $scope.getRejectionReasonsZomato();
                }
                $("#rejectOrderModal").modal("show");
            };

            $scope.closeRejectOrderModal = function () {
                $scope.showRejectionModal = false;
                $scope.rejectionObject = {
                    id: null,
                    message: null
                };
                $scope.rejectionReasons = [];
                $("#rejectOrderModal").modal("hide");
            };

            $scope.getRejectionReasonsZomato = function () {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.order.getRejectionReasonsZomato
                }).then(function success(response) {
                    if (response != null && response.status === 200) {
                        Object.keys(response.data).forEach(function(key) {
                            $scope.rejectionReasons.push(
                                { id: key, message: response.data[key] }
                            )
                        });
                        $rootScope.showFullScreenLoader = false;
                    }
                }, function error(response) {
                    console.log("Error while fetching Zomato Rejection Reasons");
                    $rootScope.showFullScreenLoader = false;
                });
            }

            $scope.getRejectionReasonsSwiggy = function () {
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'GET',
                    url: AppUtil.restUrls.order.getRejectionReasonsSwiggy
                }).then(function success(response) {
                    if (response != null && response.status === 200) {
                        response.data.forEach(function(key, index) {
                            $scope.rejectionReasons.push(
                                { id: index + 1, message: key }
                            )
                        });
                        console.log("Swiggy Rejection Reasons are : ", $scope.rejectionReasons);
                        $rootScope.showFullScreenLoader = false;
                    }
                }, function error(response) {
                    console.log("Error while fetching Swiggy Rejection Reasons");
                    $rootScope.showFullScreenLoader = false;
                });
            }

            $scope.submitRejectOrder = function () {
                if ($scope.OrderObj.channelPartner === 3) {
                    $scope.submitRejectOrderZomato();
                } else if ($scope.OrderObj.channelPartner === 6) {
                    $scope.submitRejectOrderSwiggy();
                }
            }

            $scope.onRejectionReasonChange = function () {
                if (parseInt($scope.rejectionObject.id) === 1) {
                    $scope.getProductDetail();
                } else {
                    $scope.rejectionItems = [];
                }
            }

            $scope.getProductDetail = function () {
                $rootScope.showFullScreenLoader = true;
                var channelPartnerName = "";
                if ($scope.OrderObj.channelPartner === 3) {
                    channelPartnerName = "ZOMATO";
                } else if ($scope.OrderObj.channelPartner === 6) {
                    channelPartnerName = "SWIGGY";
                }
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.partnerOrder.rejectionProductDetail,
                    data: {
                        partnerName: channelPartnerName,
                        partnerOrderId: $scope.OrderObj.sourceId
                    }
                }).then(function success(response) {
                    if (response != null && response.status === 200) {
                        if ($scope.OrderObj.channelPartner === 3) {
                            $scope.rejectionItems = response.data.partnerOrder.dishes.map(function(dish) {
                                return ({
                                    catalogueId: dish.composition.catalogue_id,
                                    catalogueName: dish.composition.catalogue_name,
                                    selected: false
                                })
                            });
                        } else if ($scope.OrderObj.channelPartner === 6) {
                            $scope.rejectionItems = response.data.partnerOrder.items.map(function (item) {
                                return ({
                                    catalogueId: item.id,
                                    catalogueName: item.name,
                                    nextAvailableTimeEpoch: AppUtil.getCurrentTimeInEpochSeconds() + 2 * 60, // 2 minutes from now
                                    selected: false
                                })
                            });
                        }
                        $rootScope.showFullScreenLoader = false;
                    }
                }, function error(response) {
                    console.log("Error while fetching Product Detail");
                    $rootScope.showFullScreenLoader = false;
                });
            }

            $scope.submitRejectOrderZomato = function () {
                if ($scope.rejectionObject.id == null || $scope.rejectionObject.id == undefined) {
                    bootbox.alert("Kindly select appropriate reason!");
                    return;
                }
                $rootScope.showFullScreenLoader = true;
                var requestObj = {
                    order_id: $scope.OrderObj.sourceId,
                    rejection_message_id: parseInt($scope.rejectionObject.id)
                }
                if ($scope.rejectionObject.id === '1') {
                    var rejectionItemIds = [];
                    $scope.rejectionItems.forEach(function(item) {
                        if (item.selected) {
                            rejectionItemIds.push(item.catalogueId);
                        }
                    });
                    requestObj = Object.assign({}, requestObj, { catalogue_vendor_entity_ids: rejectionItemIds });
                }
                if ($scope.rejectionObject.id === '1' && AppUtil.isEmptyObject(requestObj.catalogue_vendor_entity_ids)) {
                    bootbox.alert("Kindly select atleast 1 item(s)!");
                    return;
                }
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.order.rejectOrderZomato,
                    data: requestObj,
                    params: {
                        brandId: $scope.OrderObj.brandId ? $scope.OrderObj.brandId : 1,
                        rejectedBy: AppUtil.GetLoggedInUser()
                    }
                }).then(function success(response) {
                    if (response != null && response.status === 200) {
                        if (response.data.status === 'success') {
                            bootbox.alert("Order Rejection Successful!");
                        } else if (response.data.status === 'failed') {
                            bootbox.alert("Order Rejection Failed with Reason: " + response.data.message);
                        } else {
                            bootbox.alert("Error occured while rejecting order!");
                        }
                        $rootScope.showFullScreenLoader = false;
                    } else {
                        bootbox.alert("Error occured while rejecting order!");
                        $rootScope.showFullScreenLoader = false;
                    }
                    $scope.closeRejectOrderModal();
                }, function error(response) {
                    bootbox.alert("Error occured while rejecting order!");
                    $rootScope.showFullScreenLoader = false;
                    $scope.closeRejectOrderModal();
                });
            }

            $scope.submitRejectOrderSwiggy = function () {
                if ($scope.rejectionObject.id == null || $scope.rejectionObject.id == undefined) {
                    bootbox.alert("Kindly select appropriate reason!");
                    return;
                }
                $rootScope.showFullScreenLoader = true;

                var rejectionMetadata;
                if ($scope.rejectionObject.id == 1) {
                    var outOfStockItems = [];
                    $scope.rejectionItems.forEach(function(item) {
                        if (item.selected) {
                            outOfStockItems.push({
                                item_id: item.catalogueId,
                                next_available_time_epoch: item.nextAvailableTimeEpoch
                            });
                        }
                    });
                    if (AppUtil.isEmptyObject(outOfStockItems)) {
                        bootbox.alert("Kindly select atleast 1 item(s)!");
                        $rootScope.showFullScreenLoader = false;
                        return;
                    }
                    rejectionMetadata = {
                        out_of_stock_items: outOfStockItems
                    }
                } else {
                    rejectionMetadata = {
                        next_available_time_epoch: AppUtil.getCurrentTimeInEpochSeconds() + 2 * 60 // 5 minutes from now
                    };
                }

                var requestObj = {
                    reference_id: $scope.OrderObj.sourceId,
                    action_type: "REJECT_ORDER",
                    metadata: {
                        rejection_reason: $scope.rejectionObject.message,
                        rejection_metadata: rejectionMetadata
                    }
                };

                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.order.rejectOrderSwiggy,
                    data: requestObj
                }).then(function success(response) {
                    if (response != null && response.status === 200) {
                        if (response.data.statusCode === 0) {
                            bootbox.alert("Order Rejection Successful!");
                        } else {
                            bootbox.alert("Error occured while rejecting order! " + response.data.statusMessage);
                        }
                        $scrootScopeope.showFullScreenLoader = false;
                    } else {
                        bootbox.alert("Error occured while rejecting order!");
                        $rootScope.showFullScreenLoader = false;
                    }
                    $scope.closeRejectOrderModal();
                }, function error(response) {
                    bootbox.alert("Error occured while rejecting order!");
                    $rootScope.showFullScreenLoader = false;
                    $scope.closeRejectOrderModal();
                });
            }

        }]);