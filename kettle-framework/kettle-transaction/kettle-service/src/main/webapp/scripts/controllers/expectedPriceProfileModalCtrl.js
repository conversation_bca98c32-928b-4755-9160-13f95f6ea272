angular
    .module('posApp').controller('expectedPriceProfileModalCtrl', ['$rootScope','$scope','AppUtil','$http','$modalInstance','posAPI','requestData',
    function ($rootScope,$scope,AppUtil,$http,$modalInstance,posAPI,requestData) {

        $scope.product1=requestData[0];
        $scope.product2=requestData[1];
        console.log(requestData)

        var lastReadingDetails = null;
        $scope.viewType = 'add';
        function init(){
            $scope.entryType = null;
            $scope.meterReadings={};
            $scope.meterCount = [];
            $scope.loader = {
                loading: false,
            };
            lastReadingDetails = [];
        }
        $scope.rounded=function(price){
            if(parseInt(price)!=price){
                return parseInt(price)+1;
            }
            return price;
        }
        $scope.checkExpectedPrice= function(){
            var  expectedPrice1= $scope.product1.enteredExpectedPrice;
            var  expectedPrice2= $scope.product2.enteredExpectedPrice;
            var regx=/^[1-9]\d*(\.\d+)?$/;
            if(expectedPrice1==undefined || expectedPrice2==undefined || String(expectedPrice1).trim()=="" || String(expectedPrice2).trim()==""){
                bootbox.alert("Enter all values");
            }
            else if((!regx.test(expectedPrice1) || !regx.test(expectedPrice2))){
                bootbox.alert("Enter valid expected price");
            }

            else if($scope.rounded(Number($scope.product1.expectedPrice))==Number($scope.product1.enteredExpectedPrice)
                && $scope.rounded(Number($scope.product2.expectedPrice))==Number($scope.product2.enteredExpectedPrice) ){
                // localStorage.setItem('expectedPriceMatch',"true");
                AppUtil.setPriceMap();
                $modalInstance.close();
            }
            else{
                bootbox.alert("Please get your overhead menu changed");
                // localStorage.setItem('expectedPriceMatch',"false");
                $modalInstance.close();
            }
        }
        init();
    }]
);