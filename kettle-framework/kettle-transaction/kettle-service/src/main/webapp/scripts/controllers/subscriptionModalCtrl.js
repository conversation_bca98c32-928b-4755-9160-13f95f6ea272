/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

angular.module('posApp').controller('subscriptionModalCtrl',
    ['posAPI', '$scope', 'AppUtil', 'socketUtils', '$modalInstance', 'productService', 'trackingService', '$rootScope', '$timeout', 'fromChaayosSelect',
        function (posAPI, $scope, AppUtil, socketUtils, $modalInstance, productService, trackingService, $rootScope, $timeout, fromChaayosSelect) {
            $scope.couponCode = {coupon: ""};
            $scope.applyDisabled = false;
            $scope.disableCoupons = false;
            $scope.init = function () {
                $scope.fromChaayosSelect = fromChaayosSelect;
                $scope.offerDescription = "";
                $scope.otpByCustomer = "";
                $scope.otpBanner = "";
                $scope.showOtpInput = false;
                $scope.error = "";
                $scope.offers = [];
                $scope.newCustomer = $scope.$parent.isNewCustomer;
                if ($scope.offerCode != $scope.fromChaayosSelect.subscriptionCode) {
                    $scope.couponCode.coupon = $scope.offerCode;
                } else {
                    $scope.couponCode.coupon = "";
                }
                $scope.applyDisabled = $scope.couponCode.coupon.length > 0 && $scope.$parent.offerApplied ? true : false;
                $scope.getMassOffers();
                $scope.order = productService.prepareOrder($scope.$parent.orderItemArray, $scope.$parent.transactionObj);
                $scope.checkComplimentary();
                if ($scope.fromChaayosSelect.hasSubscription && !$scope.disableCoupons) {
                    $scope.couponCode.coupon = $scope.fromChaayosSelect.subscriptionCode;
                    $scope.applyDisabled = true;
                    $scope.applyOffer();
                }

            };

            $scope.checkComplimentary = function () {
                $scope.disableCoupons = $scope.checkForComplimentaryItems($scope.$parent.orderItemArray);
            };

            $scope.checkForComplimentaryItems = function (orderItemArray) {
                var flag = false;
                orderItemArray.forEach(function (orderItem) {
                    if (orderItem.orderDetails.complimentaryDetail.isComplimentary) {
                        flag = true;
                    }
                });
                return flag;
            };

            socketUtils.receiveMessage(function (message) {
                // console.log("#### subscription modal callback called ####", message);
                if (message.hasOwnProperty("OTP_VERIFIED")) {
                    $scope.otpBanner = "OTP Verified...";
                    AppUtil.otpStatus.status = 1;
                    $scope.applyOffer();
                } else if (message.hasOwnProperty("OTP_SENT")) {
                    $scope.otpBanner = "OTP Sent...";
                } else {
                    console.log("Something went wrong while verifying OTP");
                }
            }, "OFFER_CTRL");


            $scope.clear = function () {
                $scope.checkForClearOffer = false;
                productService.clearOffer($scope.$parent.orderItemArray, $scope.$parent.offerCode,
                    $scope.$parent.transactionObj);
                $scope.$parent.prepaidAmount = undefined;
                $scope.$parent.offerCode = "";
                $scope.$parent.offerApplied = false;
                try {
                    trackingService.trackCouponRemoved($scope.couponCode.coupon)
                } catch (e) {
                }
                $scope.couponCode = {coupon: ""};
                //console.log($scope.couponCode);
                $scope.applyDisabled = false;
                $scope.calculatePaidAmount();
            };


            $scope.setCouponCode = function (offerCode) {
                $scope.couponCode.coupon = offerCode;
                //console.log($scope.couponCode.coupon);
            };

            $scope.applyOffer = function () {
                $timeout(
                    $scope.applyCouponTimeOut()
                );
            };

            $scope.applyCouponTimeOut = function () {
                $scope.error = "";
                $scope.offerDescription = "";
                var reqObj = {
                    order: $scope.order,
                    couponCode: $scope.couponCode.coupon,
                    newCustomer: $scope.newCustomer
                };
                if (AppUtil.isCOD()) {
                    reqObj.contact = AppUtil.CSObj.contactNumber;
                }
                if (AppUtil.customerSocket != null && AppUtil.customerSocket.id != null && AppUtil.customerSocket.id > 5) {
                    reqObj.order.customerId = AppUtil.customerSocket.id;
                }
                if (AppUtil.isCafe()) {
                    reqObj.order.channelPartner = 1;
                }
                if ($scope.couponCode.coupon.length != 0 && !AppUtil.isEmptyObject(reqObj.order) && ($scope.fromChaayosSelect.hasSubscription ||
                    (!$scope.fromChaayosSelect.hasSubscription  && $scope.couponCode.coupon.toUpperCase() != $scope.fromChaayosSelect.subscriptionCode))) {
                    $scope.couponCode.coupon = $scope.couponCode.coupon.toUpperCase();
                    try {
                        trackingService.trackCouponTried($scope.couponCode.coupon)
                    } catch (e) {
                    }
                    productService.metaDataList.appendAttribute("COUPON_CODE_APPLIED", $scope.couponCode.coupon);
                    //reqObj = AppUtil.GetRequest(reqObj);
                    var apiLink = $scope.fromChaayosSelect.hasSubscription ? AppUtil.restUrls.customerOffers.applyCash : AppUtil.restUrls.customerOffers.applyCoupon;
                    posAPI.allUrl('/', apiLink).post(reqObj).then(function (response) {
                        $scope.applyDisabled = true;
                        if (response != undefined) {
                            var responseObj = JSON.stringify(response.plain());
                            var responseObject = JSON.parse(responseObj);
                            var otpVerified = AppUtil.otpStatus.status == 1 || AppUtil.isCSOtpVerified();
                            if(("order" in responseObject) && ((responseObject.order != null && responseObject.order != undefined) && ("bypassLoyateaAward" in responseObject.order))){
                                $scope.$parent.bypassLoyateaAward = responseObject.order.bypassLoyateaAward;
                            }
                            if(("order" in responseObject) && ((responseObject.order != null || responseObject.order != undefined) && ("revalidate" in responseObject.order))){
                                $scope.$parent.revalidate = responseObject.order.revalidate;
                            }
                            if(("order" in responseObject) && ((responseObject.order != null || responseObject.order != undefined) && ("revalidationReason" in responseObject.order))){
                                $scope.$parent.revalidationReason = responseObject.order.revalidationReason;
                            }
                            if (AppUtil.isCOD()) {
                                if (!responseObject.error) {
                                    $modalInstance.close(responseObject);
                                } else {
                                    $scope.error = responseObject.errorMessage || "Coupon not valid";
                                    $scope.offerDescription = responseObject.offerDescription;
                                    $scope.$parent.offerManual = responseObject.manualOverride;
                                    $scope.applyDisabled = false;
                                    try {
                                        trackingService.trackOfferFailed({
                                            coupon: responseObject.couponCode,
                                            errorCode: responseObject.errorCode,
                                            reason: $scope.error
                                        });
                                    } catch (e) {

                                    }
                                }
                            } else {
                                if ((responseObject.otpRequired && !otpVerified)) {
                                    $scope.otpBanner = "Requesting OTP...";
                                    AppUtil.customerSocket.otpRequired = responseObject.otpRequired;
                                    socketUtils.emitMessage({"CHAAYOS_SELECT_VERIFICATION_REQUIRED": AppUtil.customerSocket});
                                } else if (!responseObject.error) {
                                    $modalInstance.close(responseObject);
                                } else {
                                    $scope.error = responseObject.errorMessage || "Coupon not valid";
                                    $scope.offerDescription = responseObject.offerDescription;
                                    $scope.$parent.offerManual = responseObject.manualOverride;
                                    $scope.applyDisabled = false;
                                    try {
                                        trackingService.trackOfferFailed({
                                            coupon: responseObject.couponCode,
                                            errorCode: responseObject.errorCode,
                                            reason: $scope.error
                                        });
                                    } catch (e) {

                                    }
                                }
                            }
                        } else {
                            $scope.applyDisabled = false;
                            try {
                                trackingService.trackOfferFailed({
                                    coupon: $scope.couponCode.coupon,
                                    errorCode: "",
                                    reason: "API sent incorrect response KETTLE"
                                });
                            } catch (e) {
                            }
                        }
                    }, function (err) {
                        console.log();
                        try {
                            trackingService.trackOfferFailed({
                                coupon: $scope.couponCode.coupon,
                                errorCode: "",
                                reason: "API FAILED KETTLE"
                            });
                        } catch (e) {
                        }
                    });
                } else {
                    if ($scope.couponCode.coupon.length == 0) {
                        alert("Please enter a coupon code first");
                    } else if (!$scope.fromChaayosSelect.hasSubscription && $scope.couponCode.coupon.toUpperCase() == $scope.fromChaayosSelect.subscriptionCode) {
                        alert("Please enter a valid coupon");
                    }
                    if (AppUtil.isEmptyObject(reqObj.order)) {
                        alert("Could not get order. Please try again!");
                    }
                }
            };

            $scope.generateOTP = function (customerObj) {

                posAPI.allUrl('/', AppUtil.restUrls.customer.generateOTP).post(
                    customerObj
                ).then(function (response) {
                    if (response) {
                        $scope.otpBanner = "OTP Sent...";
                    } else {
                        $scope.otpBanner = "Error in sending OTP...";
                    }
                }, function (err) {
                    AppUtil.myAlert(err.data.errorMessage);
                });

            };

            $scope.outletId = function () {
                if (AppUtil.outlet.selectedId == 1) {
                    return AppUtil.outlet.pri_unitId;
                } else if (AppUtil.outlet.selectedId == 2) {
                    return AppUtil.outlet.sec_unitId;
                } else {
                    return AppUtil.outlet.ter_unitId;
                }
            };

            $scope.verifyOTP = function (otpByCustomer) {
                $scope.otpByCustomer = otpByCustomer;
                if ($scope.otpByCustomer.length == 4) {
                    var customerObj = {
                        otpPin: $scope.otpByCustomer,
                        contactNumber: AppUtil.CSObj.contactNumber
                    };
                    posAPI.allUrl('/', AppUtil.restUrls.customer.verifyOTP).post(
                        customerObj
                    ).then(function success(response) {
                        if (response) {
                            $scope.otpBanner = "OTP Verified...";
                            $scope.applyOffer();
                        } else {
                            $scope.otpBanner = "Wrong OTP...";
                            $scope.otpByCustomer = null;
                        }
                    }, function error(response) {
                    });
                }
            };


            $scope.getMassOffers = function () {
                $scope.offers = AppUtil.offers;
                //console.log("$scope.offers");
                //console.log($scope.offers);
            };

            $scope.applyThisOffer = function (offer) {
                $scope.error = "";
                $scope.offerDescription = "";
                $scope.checkAndAddFreeItem(offer);
                if (($scope.error == null || $scope.error == "") && !$scope.applyDisabled) {
                    $scope.couponCode.coupon = offer.code;
                }
            };

            $scope.cancel = function () {
                if (!$scope.fromChaayosSelect.hasSubscription) {
                    $scope.$parent.offerCode = $scope.couponCode.coupon;
                }
                socketUtils.emitMessage({"CHAAYOS_SUBSCRIPTION_MODAL_CLOSED": AppUtil.customerSocket});
                socketUtils.removeListener("OFFER_CTRL");
                $modalInstance.dismiss("cancel");
            };

            $scope.checkAndAddFreeItem = function (offer) {
                $scope.error = "";
                if (offer.offer.type == "OFFER_WITH_FREE_ITEM_STRATEGY") {
                    if (AppUtil.isEmptyObject(offer.offer.minValue)
                        || ($scope.$parent.transactionObj.paidAmount >= offer.offer.minValue)) {
                        if (!AppUtil.isEmptyObject(offer.offer.offerWithFreeItem)) {
                            var productObj = null;
                            for (var i in $scope.$parent.orderItemArray) {
                                if ($scope.$parent.orderItemArray[i].orderDetails.productId == offer.offer.offerWithFreeItem.productId &&
                                    $scope.$parent.orderItemArray[i].orderDetails.quantity >= offer.offer.offerWithFreeItem.quantity) {
                                    return;
                                }
                            }
                            for (var i = 0; i < AppUtil.getUnitDetails().products.length; i++) {
                                if (AppUtil.getUnitDetails().products[i].id == offer.offer.offerWithFreeItem.productId) {
                                    productObj = angular.copy(AppUtil.getUnitDetails().products[i]);
                                    break;
                                }
                            }

                            if (productObj != null) {
                                $scope.$parent.addNewProductToOrderItemArray(productObj, offer.offer.offerWithFreeItem.quantity);
                                var lastItem = $scope.$parent.orderItemArray[$scope.$parent.orderItemArray.length - 1];
                                productService.changeDimension(lastItem, offer.offer.offerWithFreeItem.dimension);
                                lastItem.freeOfferItem = true;
                                $scope.order = productService.prepareOrder($scope.$parent.orderItemArray, $scope.$parent.transactionObj);
                            } else {
                                $scope.clear();
                                $scope.error = "No product found to add to Order";
                            }
                        }
                    } else {
                        if (offer.offer.minValue != null) {
                            $scope.clear();
                            $scope.error = "Minimum valid value for the Offer is Rs." + offer.offer.minValue.toFixed(2) + ". Please check again.";
                        }
                    }
                }

            };

            $scope.checkInOrderArray = function (id, dimension) {
                var flag = false;
                for (var i in $scope.$parent.orderItemArray) {
                    var orderItem = $scope.$parent.orderItemArray[i];
                    if (orderItem.productDetails.id == id && orderItem.orderDetails.dimension == dimension) {
                        flag = true;
                        break;
                    }
                }
                return flag;
            }

        }]);
