/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';
angular.module('posApp').controller('addEditAddressModalCtrl',
    ['$location', '$scope', 'posAPI', 'AppUtil', '$modalInstance', 'index', 'isNewAddress', function ($location, $scope, posAPI, AppUtil, $modalInstance, index,isNewAddress) {
        $scope.address = AppUtil.CSObj.addresses[index];
        $scope.noResults = false;
        $scope.noCompanyResults = false;
        $scope.localities = [];
        $scope.isNewAddress = isNewAddress;
        $scope.cities = [];
        $scope.indian_states = [];
        $scope.addressType = ["HOME","OFFICE","OTHER"];
        for(var city in AppUtil.cities){
            $scope.cities.push(city);
            if($scope.indian_states.indexOf(AppUtil.cities[city].state)==-1){
                $scope.indian_states.push(AppUtil.cities[city].state);
            }
        }

        var Localityresults = [];
        var companyResults = [];

        if(isNewAddress){
            $scope.address.city = $scope.cities.filter(function (city) {
                return city == "Gurgaon";
            })[0];
            $scope.address.state = $scope.indian_states.filter(function (state) {
                return state == "Haryana";
            })[0];
            $scope.address.addressType = $scope.addressType.filter(function(type){
                return type=="HOME";
            })[0];
        }

        initOutletObject();
        $scope.$watch('address.city', function(newVal) {
            initOutletObject();
            if(isNewAddress){
                $scope.address.locality = '';
            }
            $scope.address.state = $scope.indian_states.filter(function(state){
                return state == AppUtil.cities[newVal].state;
            })[0];
        });

        $scope.onSelect = function ($item, $model, $label) {

            var filteredSearch= $item.toLowerCase();
            for(var i=0; i<Localityresults.length; i++){
                if(Localityresults[i].locality.toLocaleLowerCase() == filteredSearch){

                    AppUtil.outlet.pri_unitId = Localityresults[i].primaryUnitId;
                    AppUtil.outlet.pri_name = Localityresults[i].primaryCOD;
                    AppUtil.outlet.sec_unitId = Localityresults[i].secondaryUnitId;
                    AppUtil.outlet.sec_name = Localityresults[i].secondaryCOD;
                    AppUtil.outlet.ter_unitId = Localityresults[i].tertiaryUnitId;
                    AppUtil.outlet.ter_name = Localityresults[i].tertiaryCOD;
                    AppUtil.outlet.primary_unit_delivery_time = Localityresults[i].primaryUnitDeliveryTime;
                    AppUtil.outlet.sec_unit_delivery_time = Localityresults[i].secUnitDeliveryTime;
                    AppUtil.outlet.ter_unit_delivery_time = Localityresults[i].tertiaryUnitDeliveryTime;
                    $scope.outlet = AppUtil.outlet;
                }
            }
        };

        $scope.selectPrimaryOutlet = function(){
            if($scope.outlet.pri_unitId !=0){
                $scope.outlet.selectedId = 1;
                AppUtil.outlet.selectedId = 1;
            } else {
                AppUtil.myAlert("Primary Outlet Can't be selected");
            }
        };

        $scope.selectSecondaryOutlet = function(){
            if($scope.outlet.sec_unitId !=0){
                $scope.outlet.selectedId = 2;
                AppUtil.outlet.selectedId = 2;
            } else {
                AppUtil.myAlert("Secondary Outlet Can't be selected");
            }
        };

        $scope.selectTertiaryOutlet = function(){
            if($scope.outlet.ter_unitId !=0){
                $scope.outlet.selectedId = 3;
                AppUtil.outlet.selectedId = 3;
            } else {
                AppUtil.myAlert("Tertiary Outlet Can't be selected");
            }
        };

        $scope.getLocation = function(val) {
            if(Localityresults.length !=0){
                Localityresults = [];
            }
            var localSearchMap = [];
            var filteredSearch= val.toLowerCase();
            var city_lw = $scope.address.city.toLowerCase();
            for(var i=0; i<AppUtil.localityMapping.length; i++){
                if(AppUtil.localityMapping[i].city.toLowerCase() == city_lw){
                    if(stringStartsWith(AppUtil.localityMapping[i].locality.toLowerCase() , filteredSearch)){
                        Localityresults.push(AppUtil.localityMapping[i]);
                    }
                }
            }

            for (var j = 0; j < Localityresults.length; j++) {
                if(j<10){
                    localSearchMap.push(Localityresults[j].locality);
                }
            }
            //uniqueness check
            eliminateDuplicates(localSearchMap);
            return localSearchMap;
        };

        $scope.getCompany = function(val) {
            if(companyResults.length !=0){
                companyResults = [];
            }
            var localSearchMap = [];
            var filteredSearch= val.toLowerCase();
            var city_lw = $scope.address.city.toLowerCase();
            for(var i=0; i<AppUtil.companyMapping.length; i++){
                if(AppUtil.companyMapping[i].city.toLowerCase() == city_lw){
                    if(stringStartsWith(AppUtil.companyMapping[i].company_name.toLowerCase() , filteredSearch)){
                        companyResults.push(AppUtil.companyMapping[i]);
                    }
                }
            }

            for (var j = 0; j < companyResults.length; j++) {
                if(j<10){
                    localSearchMap.push(companyResults[j].company_name);
                }
            }
            if(localSearchMap.length){
                $scope.noCompanyResults = false;
            } else {
                $scope.noCompanyResults = true;

            }
            return localSearchMap;
        };

        function eliminateDuplicates(arr) {
            var uniqueNames = [];
            $.each(arr, function(i, el){
                if($.inArray(el, uniqueNames) === -1) uniqueNames.push(el);
            });
            return uniqueNames;
        }

        function stringStartsWith (string, prefix) {
            var re = new RegExp(prefix);
            if(string.match(re) !=null){
                return true;
            } else {
                return false;
            }
        }

        $scope.selectOutlet = function () {
            //Locality has been searched and mapped
            if($scope.outlet.pri_unitId !=null){
                //make other addresses not default
                for(var i=0;i<AppUtil.CSObj.addresses.length; i++){
                    if(AppUtil.CSObj.addresses[i].isSelectedAddress){
                        AppUtil.CSObj.addresses[i].isSelectedAddress = false;
                    }
                }
                $scope.address.isSelectedAddress = true;
                //console.log($scope.address);
                if($scope.noCompanyResults){
                    //console.log('new company found');
                    saveNewCompanyInParse($scope.address.company);
                }
                $modalInstance.close($scope.address);
            } else {
                //again check in customer search page
                AppUtil.myAlert("No Outlet Selected");
            }
        };

        function saveNewCompanyInParse(companyName){
            var NewCompanies = Parse.Object.extend("new_companies");
            var newCompanies = new NewCompanies();

            newCompanies.set("company_name", companyName);
            newCompanies.save(null, {
                success: function(newCompanies) {
                    // Execute any logic that should take place after the object is saved.

                },
                error: function(gameScore, error) {
                    // Execute any logic that should take place if the save fails.
                    // error is a Parse.Error with an error code and message.

                }
            });
        }

        $scope.cancel  = function() {
            $modalInstance.dismiss('dismiss');
        };

        function initOutletObject() {
            //console.log($scope.address.locality);
            if ($scope.address.locality.length !=0) {
                var localitae = $scope.address.locality.toLowerCase();
                var city_lw = $scope.address.city.toLowerCase();
                for (var i = 0; i < AppUtil.localityMapping.length; i++) {
                    if (AppUtil.localityMapping[i].city.toLowerCase() == city_lw) {
                        if (AppUtil.localityMapping[i].locality.toLowerCase() == localitae) {
                            AppUtil.outlet.pri_unitId = AppUtil.localityMapping[i].primaryUnitId;
                            AppUtil.outlet.pri_name = AppUtil.localityMapping[i].primaryCOD;
                            AppUtil.outlet.sec_unitId = AppUtil.localityMapping[i].secondaryUnitId;
                            AppUtil.outlet.sec_name = AppUtil.localityMapping[i].secondaryCOD;
                            AppUtil.outlet.ter_unitId = AppUtil.localityMapping[i].tertiaryUnitId;
                            AppUtil.outlet.ter_name = AppUtil.localityMapping[i].tertiaryCOD;
                            $scope.outlet = AppUtil.outlet;
                            //console.log($scope.outlet);
                            break;
                        }
                    }
                }
            } else {
                //console.log($scope.address.locality);
                $scope.outlet = {
                    pri_unitId: null,
                    pri_name: 'Primary Outlet',
                    sec_unitId: null,
                    sec_name: 'Secondary Outlet',
                    ter_unitId: null,
                    ter_name: 'Tertiary Outlet',
                    selectedId: 1
                };
                AppUtil.outlet = $scope.outlet;
            }
        }

    }]);