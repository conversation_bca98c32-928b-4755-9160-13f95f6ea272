var gulp = require('gulp'),
    {src, dest, series, parallel, watch} = require('gulp'),
    fs = require('fs'),
    handlebars = require('gulp-compile-handlebars'),
    rename = require('gulp-rename'),
    jshint = require('gulp-jshint'),
    stylish = require('jshint-stylish'),
    concat = require('gulp-concat'),
    uglify = require('gulp-uglify'),
    cleanCSS = require('gulp-clean-css'),
    hash = require('gulp-hash'),
    clean = require('gulp-clean'),
    angularFilesort = require('gulp-angular-filesort'),
    noop = require("gulp-noop"),
    argv = require('minimist')(process.argv.slice(2)),
    htmlhint = require('gulp-htmlhint'),
    htmlmin = require('gulp-htmlmin'),
    ngHtml2Js = require('gulp-ng-html2js'),
    browserSync = require('browser-sync'),
    gulpif = require('gulp-if');

var s3 = require('gulp-s3-upload')({
    accessKeyId: "********************",
    secretAccessKey: "waqQ/XK541NHcKM7xIWi5WflDunCSqfmTAA9XoSY"
});

var properties = {
    externalScripts: [
        "./bower_components/jquery/dist/jquery.min.js",
        "./bower_components/angular/angular.min.js",
        "./bower_components/bootstrap/dist/js/bootstrap.min.js",
        "./bower_components/angular-animate/angular-animate.min.js",
        "./bower_components/angular-cookies/angular-cookies.min.js",
        "./bower_components/angular-resource/angular-resource.min.js",
        "./bower_components/angular-route/angular-route.min.js",
        "./bower_components/angular-sanitize/angular-sanitize.min.js",
        "./bower_components/lodash/lodash.min.js",
        "./bower_components/restangular/dist/restangular.min.js",
        "./bower_components/angular-ui-grid/ui-grid.min.js",
        "./bower_components/angular-ui-router/release/angular-ui-router.min.js",
        "./bower_components/angular-bootstrap/ui-bootstrap.min.js",
        "./bower_components/angular-bootstrap/ui-bootstrap-tpls.min.js",
        "./bower_components/angular-messages/angular-messages.js",
        //"./js/3rdparty/deployJava.js",
        "./js/angular-datepicker.min.js",
        "./js/qz/rsvp-3.1.0.min.js",
        "./js/qz/sha-256.min.js",
        "./js/qz/qz-tray.js",
        //"./js/3rdparty/html2canvas.js",
        //"./js/3rdparty/jquery.plugin.html2canvas.js",
        //"./js/print-loader.js",
        "./js/angular-flash.min.js",
        "./js/angular-timer.js",
        "./js/humanize-duration.js",
        //"./js/typeahead.js",
        "./js/stomp.min.js",
        "./js/sockjs.min.js",
        "./js/spin.min.js",
        //"./js/jquery.floatThead-slim.min.js",
        //"./js/angular-block-ui.min.js",
        "./js/socket.io-min.js",
        "./js/pubnub.7.1.2.min.js",
        "./js/pubnub-angular-4.2.0.min.js",
        "./js/bootbox.min.js",
        "./js/pusher.min.js",
        "./js/jsrsasign-latest-all-min.js",
        "./js/moment.min.js",
        "./js/moment-timezone-with-data.min.js",
        "./js/FileSaver/FileSaver.min.js",
        "./js/ng-Idle.min.js",
        "./js/mqtt.min.js"
    ],
    internalScripts: ["./scripts/app.js",
        './scripts/controllers/**/*.js',
        './scripts/services/**/*.js',
        './scripts/directives/**/*.js',
        './scripts/filters/**/*.js',
        //'./scripts/less.min.js',
        //'./scripts/common.js'
    ],
    appName: "kettle",
    version: argv.version ? argv.version : '0.0.1',
    cdn: {
        prod: {
            content: "https://d3h6l03ass8kbe.cloudfront.net",
            static: "https://d10xumu8txm47n.cloudfront.net",
            images: "https://d1lavd03oivcft.cloudfront.net"
        },
        uat: {
            content: "http://d2vp6ojxp1wydt.cloudfront.net",
            static: "http://dgnrxrmptmxnb.cloudfront.net",
            images: "http://ddzn5kf5np8vs.cloudfront.net"
        },
        stage: {
            content: "http://d3r7iq18k2qhtt.cloudfront.net",
            static: "http://d2849s4qwwon4s.cloudfront.net",
            images: "http://d1zz9rmqhfecse.cloudfront.net"
        },
        dev: {
            content: "https://d23hbal83ofwle.cloudfront.net",
            static: "https://d1xi2hhqgtjr19.cloudfront.net",
            images: "https://dk2ubzqt73sd9.cloudfront.net"
        },
        local: {
            content: "http://localhost:8080/kettle-service/dist/",
            static: "http://localhost:8080/kettle-service/dist/",
            images: "http://localhost:8080/kettle-service/dist/"
        }
    },
    buckets: {
        dev: {
            content: "dev.cdn.content.distribution",
            static: "dev.cdn.static.distribution",
            images: "dev.cdn.images.distribution"
        },
        uat: {
            content: "uat.cdn.content.distribution",
            static: "uat.cdn.static.distribution",
            images: "uat.cdn.images.distribution"
        },
        stage: {
            content: "stage.cdn.content.distribution",
            static: "stage.cdn.static.distribution",
            images: "stage.cdn.images.distribution"
        },
        prod: {
            content: "prod.cdn.content.distribution",
            static: "prod.cdn.static.distribution",
            images: "prod.cdn.images.distribution"
        }
    },
    baseUrls: {
        prod: {
            master: "https://internal.chaayos.com/",
            analytics: "https://internal.chaayos.com/",
            kettle: "https://internal.chaayos.com/",
            scm: "https://internal.chaayos.com/",
            crm: "https://internal.chaayos.com/",
            channelPartner: "https://internal.chaayos.com/",
            forms: "https://internal.chaayos.com/",
            inventory: "https://internal.chaayos.com/"
        },
        uat: {
            master: "http://uat.kettle.chaayos.com:9191/",
            analytics: "http://uat.kettle.chaayos.com:9191/",
            kettle: "http://uat.kettle.chaayos.com:9191/",
            scm: "http://uat.kettle.chaayos.com:9191/",
            crm: "http://uat.kettle.chaayos.com:9191/",
            channelPartner: "http://uat.kettle.chaayos.com:9191/",
            forms: "http://uat.kettle.chaayos.com:9191/",
            inventory: "http://uat.kettle.chaayos.com:9191/"
        },
        stage: {
            master: "http://stage.kettle.chaayos.com:9191/",
            analytics: "http://stage.kettle.chaayos.com:9191/",
            kettle: "http://stage.kettle.chaayos.com:9191/",
            scm: "http://stage.kettle.chaayos.com:9191/",
            crm: "http://stage.kettle.chaayos.com:9191/",
            channelPartner: "http://stage.kettle.chaayos.com:9191/",
            forms: "http://stage.kettle.chaayos.com:9191/",
            inventory: "http://stage.kettle.chaayos.com:9191/"
        },
        dev: {
            master: "https://dev.kettle.chaayos.com:9595/",
            analytics: "https://dev.kettle.chaayos.com:9595/",
            kettle: "https://dev.kettle.chaayos.com:9595/",
            scm: "https://dev.kettle.chaayos.com:9595/",
            crm: "https://dev.kettle.chaayos.com:9595/",
            channelPartner: "https://dev.kettle.chaayos.com:9595/",
            forms: "https://dev.kettle.chaayos.com:9595/",
            inventory: "https://dev.kettle.chaayos.com:9595/"
        },
        local: {
            master: "http://localhost:8080/",
            analytics: "http://localhost:8080/",
            kettle: "http://localhost:8080/",
            scm: "http://localhost:8080/",
            crm: "http://localhost:8080/",
            channelPartner: "http://localhost:8080/",
            forms: "http://localhost:8080/",
            inventory: "http://localhost:8080/"
        }
    },
    webengageLicence: {
        prod: "~15ba1daaa",
        uat: "d3a4ab7d",
        dev: "d3a4ab7d",
        stage: "d3a4ab7d",
        local: "d3a4ab7d"
    }

};

function lint() {
    return src(['scripts/**/*.js', '!scripts/less.min.js'])
        .pipe(jshint())
        .pipe(jshint.reporter(stylish));
}

function staticJs() {
    return src(properties.externalScripts) //
        .pipe(gulpif('!*.min.js', uglify())).on('error', console.error)
        .pipe(concat('static.js'))
        .pipe(hash({
            version: properties.version,
            template: '<%= name %>.<%= hash %><%= ext %>'
        }))
        .pipe(dest('./dist/static/js/'))
        .pipe(hash.manifest('staticJS.json', {
            deleteOld: false,
            sourceDir: './dist/static/js/'
        }))
        .pipe(dest('./dist/assets'));
}

// Concatenate & Minify JS internal js scripts
function contentJs() {
    return src(properties.internalScripts)
        .pipe(angularFilesort())
        .pipe(concat('content.js'))
        .pipe(uglify().on('error', console.log))
        .pipe(hash({
            version: properties.version,
            template: '<%= name %>.<%= hash %><%= ext %>'
        }))
        .pipe(dest('./dist/content/js/'))
        .pipe(hash.manifest('contentJS.json', {
            deleteOld: true,
            sourceDir: './dist/content/js/'
        }))
        .pipe(dest('./dist/assets'));
}

// Concatenate & Minify static CSS
function staticCss() {
    return src(['bower_components/bootstrap/dist/css/bootstrap.min.css', 'bower_components/angular-ui-grid/ui-grid.min.css',
        'styles/angular-datepicker.min.css', 'styles/font-awesome.min.css', 'styles/angular-block-ui.min.css'])
        .pipe(concat('static.css'))
        .pipe(cleanCSS({compatibility: 'ie8'}))
        .pipe(hash({
            version: properties.version,
            template: '<%= name %>.<%= hash %><%= ext %>'
        }))
        .pipe(dest('./dist/static/css'))
        .pipe(hash.manifest('staticCSS.json', {
            deleteOld: true,
            sourceDir: './dist/static/css/'
        }))
        .pipe(dest('./dist/assets'));
}

// Concatenate & Minify content CSS
function contentCss() {
    return src(['styles/main.css'])
        .pipe(concat('content.css'))
        .pipe(cleanCSS({compatibility: 'ie8'}))
        .pipe(hash({
            version: properties.version,
            template: '<%= name %>.<%= hash %><%= ext %>'
        }))
        .pipe(dest('./dist/content/css/'))
        .pipe(hash.manifest('contentCSS.json', {
            deleteOld: true,
            sourceDir: './dist/content/css/'
        }))
        .pipe(dest('./dist/assets'));
}

// Bundle images and push to S3 not required as local images are used
function images() {
    return src(['images/**'])
        .pipe(dest('./dist/images'))
        .pipe(argv.deployment === "local" ? noop() : s3({
            Bucket: properties.buckets[argv.deployment].images,
            ACL: 'public-read',
            keyTransform: function (relative_filename) {
                return "kettle-service/" + argv.version + "/images/" + relative_filename;
            }
        }, {
            maxRetries: 5
        }));
}

// these are icon fonts
function copyFonts() {
    return src(['fonts/**'])
        .pipe(dest('./dist/static/fonts'));
}

function buildHtml() {
    return src(['./**/*.html', '!index.html', '!indexTest.html', '!./bower_components/**', '!./node_modules/**', '!./WEB-INF/**'])
        .pipe(htmlhint({'doctype-first': false}))
        .pipe(htmlmin({
            collapseWhitespace: true,
            removeComments: true,
            removeEmptyAttributes: false,
            removeEmptyElements: false
        })).on('error', console.error)
        .pipe(ngHtml2Js({
            moduleName: "posApp",
            prefix: (typeof argv.version != "undefined" ? argv.version + "/" : "")
        }))
        .pipe(concat("partials.js"))
        .pipe(uglify())
        .pipe(hash({
            version: properties.version,
            template: '<%= name %>.<%= hash %><%= ext %>'
        }))
        .pipe(dest('dist/content/js'))
        .pipe(hash.manifest('contentJS.json', {
            deleteOld: true,
            sourceDir: './dist/content/js/'
        }))
        .pipe(dest('./dist/assets'));
}

function cleanBuild() {
    return src('./dist', {read: false, allowEmpty: true}).pipe(clean());
}

function compileIndex() {
    var data = {};
    data.staticJS = JSON.parse(fs.readFileSync('dist/assets/staticJS.json', 'utf8'));
    data.contentJS = JSON.parse(fs.readFileSync('dist/assets/contentJS.json', 'utf8'));
    data.staticCSS = JSON.parse(fs.readFileSync('dist/assets/staticCSS.json', 'utf8'));
    data.contentCSS = JSON.parse(fs.readFileSync('dist/assets/contentCSS.json', 'utf8'));
    data.baseUrl = properties.cdn[argv.deployment]; //base url for content files
    data.isLocal = argv.deployment == "local";
    data.version = (typeof argv.version != "undefined" ? argv.version + "/" : "");
    data.baseUrls = properties.baseUrls[argv.deployment]; //base urls for rest APIs
    data.webengageLicence = properties.webengageLicence[argv.deployment];
    var handlebarOpts = {
        ignorePartials: true,
        helpers: {
            eachInMapCss: function (map, block) {
                var out = '';
                Object.keys(map).map(function (prop) {
                    if (prop.indexOf(".css") >= 0) {
                        out += block.fn({key: prop, value: map[prop], data: data});
                    }
                });
                return out;
            },
            eachInMapJs: function (map, block) {
                var out = '';
                Object.keys(map).map(function (prop) {
                    if (prop.indexOf(".js") >= 0) {
                        out += block.fn({key: prop, value: map[prop], data: data});
                    }
                });
                return out;
            }
        }
    };
    return src('build/templates/index.hbs')
        .pipe(handlebars(data, handlebarOpts))
        .pipe(rename('index.html'))
        .pipe(dest('./'));
}

function s3UploadStatic() {
    return src('./dist/static/**')
        .pipe(argv.deployment === "local" ? noop() : s3({
            Bucket: properties.buckets[argv.deployment].static,
            ACL: 'public-read',
            keyTransform: function (relative_filename) {
                return "kettle-service/" + argv.version + "/" + relative_filename;
            }
        }, {
            maxRetries: 5
        }))
}

function s3UploadContent() {
    return src('./dist/content/**')
        .pipe(argv.deployment === "local" ? noop() : s3({
            Bucket: properties.buckets[argv.deployment].content,
            ACL: 'public-read',
            keyTransform: function (relative_filename) {
                return "kettle-service/" + argv.version + "/" + relative_filename;
            }
        }, {
            maxRetries: 5
        }))
}

// Watch Files For Changes
function watchChanges() {
    browserSync.init({
        server: {
            baseDir: "./"
        }
    });
    watch(properties.internalScripts, {delay: 500}, series(lint, contentJs, compileIndex));
    watch('styles/main.css', {delay: 500}, series(contentCss, compileIndex));
    watch(['./**/*.html', '!index.html', '!indexTest.html', '!./bower_components/**', '!./node_modules/**'], series(buildHtml, compileIndex));
    watch(['./dist/**/*', 'index.html']).on('change', browserSync.reload);
}

function build(done) {
    console.log("NODE_ENV:" + process.env.NODE_ENV);
    if (process.env.NODE_ENV != undefined) {
        argv.deployment = process.env.NODE_ENV;
    }
    if (argv.deployment != 'local' && (typeof argv.version == 'undefined' || argv.version == null || argv.version.length == 0)) {
        console.log("Please provide valid version parameter i.e --version=0.0.1 please check previous version");
        done();
    } else {
        if (argv.deployment != 'local') {
            series(cleanBuild, lint, parallel(staticJs, staticCss, contentJs, contentCss, copyFonts), buildHtml, compileIndex, s3UploadStatic, s3UploadContent)();
            done();
        } else {
            series(cleanBuild, lint, parallel(staticJs, staticCss, contentJs, contentCss, copyFonts), buildHtml, compileIndex, watchChanges)();
            done();
        }
    }

}

exports.cleanBuild = cleanBuild;
exports.lint = lint;
exports.staticJs = staticJs;
exports.staticCss = staticCss;
exports.contentJs = contentJs;
exports.contentCss = contentCss;
exports.images = images;
exports.copyFonts = copyFonts;
exports.buildHtml = buildHtml;
exports.compileIndex = compileIndex;
exports.s3UploadStatic = s3UploadStatic;
exports.s3UploadContent = s3UploadContent;
exports.watchChanges = watchChanges;
gulp.task('default', build);

//for prod:   gulp --deployment=prod --version=0.0.1
//for local:   gulp --deployment=local --version=0.0.1
//for dev:   gulp --deployment=dev --version=0.0.1