{"name": "angular-sanitize", "version": "1.4.2", "description": "AngularJS module for sanitizing HTML", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/angular/angular.js.git"}, "keywords": ["angular", "framework", "browser", "html", "client-side"], "author": "Angular Core Team <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/angular/angular.js/issues"}, "homepage": "http://angularjs.org"}