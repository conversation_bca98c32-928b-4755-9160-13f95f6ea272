{"name": "angular-ui-grid", "description": "A data grid for Angular", "main": ["./less", "./ui-grid.css", "./ui-grid.eot", "./ui-grid.js", "./ui-grid.svg", "./ui-grid.ttf", "./ui-grid.woff"], "ignore": [], "dependencies": {"angular": ">=1.2.16 1.4.x"}, "repository": {"type": "git", "url": "https://github.com/angular-ui/ui-grid.git"}, "homepage": "http://ui-grid.info", "bugs": {"url": "https://github.com/angular-ui/ui-grid/issues"}, "keywords": ["angular", "ng-grid", "nggrid", "grid", "<PERSON><PERSON>s", "slickgrid", "kog<PERSON>", "ui-grid", "ui grid", "data grid"], "license": "MIT"}