@import '../../../less/variables';

.ui-grid-column-resizer {
  top: 0;
  bottom: 0;
  width: 5px;
  position: absolute;
  cursor: col-resize;

  &.left {
    left: 0;
  }

  &.right {
    right: 0;
  }
}

// Add a visual border for final column's resizer element
.ui-grid-header-cell:last-child .ui-grid-column-resizer.right {
  border-right: @gridBorderWidth solid @borderColor;
}

// Put visual border on left of last header cell when direction is rtl
.ui-grid[dir=rtl]  .ui-grid-header-cell:last-child {
  .ui-grid-column-resizer.right {
    border-right: 0;
  }

  .ui-grid-column-resizer.left {
    border-left: @gridBorderWidth solid @borderColor;
  }
}

.ui-grid {
  &.column-resizing {
    cursor: col-resize;
  }
}

.ui-grid.column-resizing .ui-grid-resize-overlay {
  position: absolute;
  top: 0;
  height: 100%;
  width: @gridBorderWidth;
  background-color: darken(@verticalBarColor, 15%);
}