{"name": "angular-ui-grid", "description": "A data grid for Angular", "main": ["./less", "./ui-grid.css", "./ui-grid.eot", "./ui-grid.js", "./ui-grid.svg", "./ui-grid.ttf", "./ui-grid.woff"], "ignore": [], "dependencies": {"angular": ">=1.2.16 1.4.x"}, "repository": {"type": "git", "url": "https://github.com/angular-ui/ui-grid.git"}, "homepage": "http://ui-grid.info", "bugs": {"url": "https://github.com/angular-ui/ui-grid/issues"}, "keywords": ["angular", "ng-grid", "nggrid", "grid", "<PERSON><PERSON>s", "slickgrid", "kog<PERSON>", "ui-grid", "ui grid", "data grid"], "license": "MIT", "version": "3.0.6", "_release": "3.0.6", "_resolution": {"type": "version", "tag": "v3.0.6", "commit": "0d4c8d56e115d53313f9cf998e7b03e9b0734dc1"}, "_source": "git://github.com/angular-ui/bower-ui-grid.git", "_target": "~3.0.6", "_originalSource": "angular-ui-grid", "_direct": true}