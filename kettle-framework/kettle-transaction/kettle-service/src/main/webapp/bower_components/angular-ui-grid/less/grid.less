
.ui-grid {
  border: @gridBorderWidth solid @borderColor;
  box-sizing: content-box; // If bootstrap (or another included library) makes the default sizing on element "border-box", then calculations get messed up

  .rounded(@gridBorderRadius);

  .transform(translateZ(0));

}

.ui-grid-vertical-bar {
  position: absolute;
  right: 0;
  width: 0;
}

.ui-grid-header-cell:not(:last-child) .ui-grid-vertical-bar, .ui-grid-cell:not(:last-child) .ui-grid-vertical-bar {
  width: @gridBorderWidth;
}

.ui-grid-scrollbar-placeholder{
  background-color: transparent;
}

.ui-grid-header-cell:not(:last-child) .ui-grid-vertical-bar {
  background-color: @headerVerticalBarColor;
}

.ui-grid-cell:not(:last-child) .ui-grid-vertical-bar {
  background-color: @verticalBarColor;
}

.ui-grid-header-cell:last-child .ui-grid-vertical-bar {
  right: -1px; // TODO(c0bra): Should this be grid width? Test column resizing with custom grid border width
  width: @gridBorderWidth;
  background-color: @headerVerticalBarColor;
}

// .ui-grid-vertical-bar-visible {
//   width: 1px;
//   background-color: @borderColor;
// }

.ui-grid-clearfix {
  &:before, &:after {
    content: "";
    display: table;
  }

  &:after {
    clear:both;
  }
}

.ui-grid-invisible {
  visibility: hidden;;
}

// Wraps the contents inside the grid directive with a relatively-positioned element so that all absolute elements are positioned relative to the grid, and not the page
.ui-grid-contents-wrapper {
  position: relative;
  height: 100%;
  width: 100%;
}

// Only display content to screen readers
//
// See: http://a11yproject.com/posts/how-to-hide-content/

.ui-grid-sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  margin: -1px;
  padding: 0;
  overflow: hidden;
  clip: rect(0,0,0,0);
  border: 0;
}
