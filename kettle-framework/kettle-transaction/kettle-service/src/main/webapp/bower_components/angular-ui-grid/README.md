This repo is for distribution on `npm` and `bower`. The source for this module is in the [main UI Grid repo](https://github.com/angular-ui/ui-grid). Please file issues and pull requests against that repo.

## Install

You can install this package either with `npm` or with `bower`.

### npm

```shell
npm install angular-ui-grid
```

Then add a `<script>` to your `index.html`:

```html
<link rel="stylesheet" type="text/css" href="/node_modules/angular-ui-grid/ui-grid.css" />
<script src="/node_modules/angular-ui-grid/ui-grid.js"></script>
```

### bower

```shell
bower install angular-ui-grid
```

Then add a `<script>` to your `index.html`:

```html
<link rel="stylesheet" type="text/css" href="/bower_components/angular-ui-grid/ui-grid.css" />
<script src="/bower_components/angular-ui-grid/ui-grid.js"></script>
```

## Documentation

Documentation is available on the [main UI Grid site](http://ui-grid.info).

## License

The MIT License

Copyright (c) 2013-2015 the Angular-UI team, http://angular-ui.github.com

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.
