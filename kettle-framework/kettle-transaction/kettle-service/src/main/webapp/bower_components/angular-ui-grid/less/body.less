
.ui-grid-render-container {
  position: inherit;
  // overflow: hidden;

  .border-radius(0, @gridBorderRadius, @gridBorderRadius, 0);

  // Prevent an outline from showing if we focus the render container element
  &:focus {
    outline: none;
  }
}

.ui-grid-viewport {
  // overflow: auto; // TODO(c0bra): turn back on when virtual repeater is hooked up
  min-height: 20px;
  position: relative;
  overflow-y: scroll;
  -webkit-overflow-scrolling: touch;

  &:focus {
    outline: none !important;
  }
}

.ui-grid-canvas {
  position: relative;
  padding-top:1px; //to prevent canvas from absorbing the 1st rendered row's margin
}

.ui-grid-row {
  //position: absolute;
  // border-bottom: 1px solid @borderColor;

  &:nth-child(odd) .ui-grid-cell {
    background-color: @rowColorOdd; //rgb(253, 253, 253);
  }

  &:nth-child(even) .ui-grid-cell {
    background-color: @rowColorEven;
  }

  &:last-child .ui-grid-cell {
    border-bottom-color: @borderColor;
    border-bottom-style: solid;

  }
}

.ui-grid-no-row-overlay {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: 10%;

  .ui-grid-top-panel-background;
  .border-radius(@gridBorderRadius);
  border: @gridBorderWidth solid @borderColor;

  font-size: 2em;
  text-align: center;

  > * {
    position: absolute;
    display: table;
    margin: auto 0;
    width: 100%;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    opacity: 0.66;
  }
}