// Include gulp
var gulp = require('gulp');
// Include Our Plugins
var fs = require('fs');
var handlebars = require('gulp-compile-handlebars');
var rename = require('gulp-rename');
var jshint = require('gulp-jshint');
var stylish = require('jshint-stylish');
var concat = require('gulp-concat');
var uglify = require('gulp-uglify');
var cleanCSS = require('gulp-clean-css');
var hash = require('gulp-hash');
var clean = require('gulp-clean');
var angularFilesort = require('gulp-angular-filesort');
var runSequence = require('run-sequence');
var noop = require("gulp-noop");
var argv = require('minimist')(process.argv.slice(2));
var htmlhint = require('gulp-htmlhint');
var htmlmin = require('gulp-htmlmin');
var ngHtml2Js = require('gulp-ng-html2js');
var s3 = require('gulp-s3-upload')({
    accessKeyId: "********************",
    secretAccessKey: "waqQ/XK541NHcKM7xIWi5WflDunCSqfmTAA9XoSY"
});

var properties = {
    externalScripts: [
        "./bower_components/jquery/dist/jquery.min.js",
        "./bower_components/angular/angular.min.js",
        "./bower_components/bootstrap/dist/js/bootstrap.min.js",
        "./bower_components/angular-animate/angular-animate.min.js",
        "./bower_components/angular-cookies/angular-cookies.min.js",
        "./bower_components/angular-resource/angular-resource.min.js",
        "./bower_components/angular-route/angular-route.min.js",
        "./bower_components/angular-sanitize/angular-sanitize.min.js",
        "./bower_components/lodash/lodash.min.js",
        "./bower_components/restangular/dist/restangular.min.js",
        "./bower_components/angular-ui-grid/ui-grid.min.js",
        "./bower_components/angular-ui-router/release/angular-ui-router.min.js",
        "./bower_components/angular-bootstrap/ui-bootstrap.min.js",
        "./bower_components/angular-bootstrap/ui-bootstrap-tpls.min.js",
        "./bower_components/angular-messages/angular-messages.js",
        //"./js/3rdparty/deployJava.js",
        "./js/angular-datepicker.min.js",
        "./js/qz/rsvp-3.1.0.min.js",
        "./js/qz/sha-256.min.js",
        "./js/qz/qz-tray.js",
        //"./js/3rdparty/html2canvas.js",
        //"./js/3rdparty/jquery.plugin.html2canvas.js",
        //"./js/print-loader.js",
        "./js/angular-flash.min.js",
        "./js/angular-timer.js",
        "./js/humanize-duration.js",
        //"./js/typeahead.js",
        "./js/stomp.min.js",
        "./js/sockjs.min.js",
        "./js/spin.min.js",
        //"./js/jquery.floatThead-slim.min.js",
        //"./js/angular-block-ui.min.js",
        "./js/socket.io-min.js",
        "./js/pubnub.7.1.2.min.js",
        "./js/pubnub-angular-4.2.0.min.js",
        "./js/bootbox.min.js",
        "./js/pusher.min.js",
        "./js/jsrsasign-latest-all-min.js",
        "./js/moment.min.js",
        "./js/moment-timezone-with-data.min.js",
        "./js/FileSaver/FileSaver.min.js"],
    internalScripts: ["./scripts/app.js",
        './scripts/controllers/**/*.js',
        './scripts/services/**/*.js',
        './scripts/directives/**/*.js',
        './scripts/filters/**/*.js',
        './scripts/less.min.js',
        './scripts/common.js'],
    appName: "kettle",
    version: '0.0.1',
    cdn: {
        prod: {
            content: "https://d3h6l03ass8kbe.cloudfront.net",
            static: "https://d10xumu8txm47n.cloudfront.net",
            images: "https://d1lavd03oivcft.cloudfront.net"
        },
        uat: {
            content: "http://d2vp6ojxp1wydt.cloudfront.net",
            static: "http://dgnrxrmptmxnb.cloudfront.net",
            images: "http://ddzn5kf5np8vs.cloudfront.net"
        },
        dev: {
            content: "https://d23hbal83ofwle.cloudfront.net",
            static: "https://d1xi2hhqgtjr19.cloudfront.net",
            images: "https://dk2ubzqt73sd9.cloudfront.net"
        },
        local: {
            content: "https://internal.chaayos.com/kettle-service",
            static: "https://internal.chaayos.com/kettle-service",
            images: "https://internal.chaayos.com/kettle-service"
        }
    },
    buckets: {
        dev: {
            content: "dev.cdn.content.distribution",
            static: "dev.cdn.static.distribution",
            images: "dev.cdn.images.distribution"
        },
        uat: {
            content: "uat.cdn.content.distribution",
            static: "uat.cdn.static.distribution",
            images: "uat.cdn.images.distribution"
        },
        prod: {
            content: "prod.cdn.content.distribution",
            static: "prod.cdn.static.distribution",
            images: "prod.cdn.images.distribution"
        }
    },
    baseUrls: {
        prod: {
            master: "https://internal.chaayos.com/",
            analytics: "https://internal.chaayos.com/",
            kettle: "https://internal.chaayos.com/",
            scm: "https://internal.chaayos.com/",
            crm: "https://internal.chaayos.com/",
            channelPartner: "https://internal.chaayos.com/",
            forms: "https://internal.chaayos.com/",
            inventory: "https://internal.chaayos.com/"
        },
        uat: {
            master: "http://uat.kettle.chaayos.com:9191/",
            analytics: "http://uat.kettle.chaayos.com:9191/",
            kettle: "http://uat.kettle.chaayos.com:9191/",
            scm: "http://uat.kettle.chaayos.com:9191/",
            crm: "http://uat.kettle.chaayos.com:9191/",
            channelPartner: "http://uat.kettle.chaayos.com:9191/",
            forms: "http://uat.kettle.chaayos.com:9191/",
            inventory: "http://uat.kettle.chaayos.com:9191/"
        },
        dev: {
            master: "https://internal.chaayos.com/",
            analytics: "https://internal.chaayos.com/",
            kettle: "https://internal.chaayos.com/",
            scm: "https://internal.chaayos.com/",
            crm: "https://internal.chaayos.com/",
            channelPartner: "https://internal.chaayos.com/",
            forms: "https://internal.chaayos.com/",
            inventory: "https://internal.chaayos.com/"
        }
    },
    webengageLicence: {
        prod: "~15ba1daaa",
        uat: "d3a4ab7d",
        dev: "d3a4ab7d",
        local: "d3a4ab7d"
    }

};

// Lint Task
gulp.task('lint', function () {
    return gulp.src(['scripts/**/*.js', '!scripts/less.min.js'])
        .pipe(jshint())
        .pipe(jshint.reporter(stylish));
});

// Concatenate & Minify JS third party js scripts
gulp.task('staticJs', function () {
    return gulp.src(properties.externalScripts)
        .pipe(argv.merge === 'true' ? concat('static.js') : noop())
        .pipe(argv.compress === 'true' ? uglify() : noop())
        .pipe(argv.hash === 'true' ? hash({
                version: properties.version,
                template: '<%= name %>.<%= hash %><%= ext %>'
            }) :
            hash({version: properties.version, template: '<%= name %><%= ext %>'}))
        .pipe(gulp.dest('./dist/static/js/'))
        .pipe(hash.manifest('staticJS.json', {
            deleteOld: false,
            sourceDir: './dist/static/js/'
        }))
        .pipe(gulp.dest('./dist/assets'));
});

// Concatenate & Minify JS internal js scripts
gulp.task('contentJs', function () {
    return gulp.src(properties.internalScripts)
        .pipe(angularFilesort())
        .pipe(argv.merge === 'true' ? concat('content.js') : noop())
        .pipe(argv.compress === 'true' ? uglify().on('error', console.log) : noop())
        .pipe(argv.hash === 'true' ? hash({
                version: properties.version,
                template: '<%= name %>.<%= hash %><%= ext %>'
            }) :
            hash({version: properties.version, template: '<%= name %><%= ext %>'}))
        .pipe(gulp.dest('./dist/content/js/'))
        .pipe(hash.manifest('contentJS.json', {
            deleteOld: true,
            sourceDir: './dist/content/js/'
        }))
        .pipe(gulp.dest('./dist/assets'));
});

// Concatenate & Minify static CSS
gulp.task('staticCss', () => {
    return gulp.src(['bower_components/bootstrap/dist/css/bootstrap.min.css', 'bower_components/angular-ui-grid/ui-grid.min.css',
        'styles/angular-datepicker.min.css', 'styles/font-awesome.min.css', 'styles/angular-block-ui.min.css'])
        .pipe(argv.merge === 'true' ? concat('static.css') : noop())
        .pipe(argv.compress === 'true' ? cleanCSS({compatibility: 'ie8'}) : noop())
        .pipe(argv.hash === 'true' ? hash({
                version: properties.version,
                template: '<%= name %>.<%= hash %><%= ext %>'
            }) :
            hash({version: properties.version, template: '<%= name %><%= ext %>'}))
        .pipe(gulp.dest('./dist/static/css'))
        .pipe(hash.manifest('staticCSS.json', {
            deleteOld: true,
            sourceDir: './dist/static/css/'
        }))
        .pipe(gulp.dest('./dist/assets'));
});

// Concatenate & Minify content CSS
gulp.task('contentCss', () => {
    return gulp.src(['styles/main.css'])
        .pipe(argv.merge === 'true' ? concat('content.css') : noop())
        .pipe(argv.compress === 'true' ? cleanCSS({compatibility: 'ie8'}) : noop())
        .pipe(argv.hash === 'true' ? hash({
                version: properties.version,
                template: '<%= name %>.<%= hash %><%= ext %>'
            }) :
            hash({version: properties.version, template: '<%= name %><%= ext %>'}))
        .pipe(gulp.dest('./dist/content/css/'))
        .pipe(hash.manifest('contentCSS.json', {
            deleteOld: true,
            sourceDir: './dist/content/css/'
        }))
        .pipe(gulp.dest('./dist/assets'));
});

// Concatenate & Minify CSS
gulp.task('images', () => {
    return gulp.src(['images/**'])
        .pipe(gulp.dest('./dist/images'))
        .pipe(argv.deployment == "local" ? noop() : s3({
            Bucket: properties.buckets[argv.deployment].images,
            ACL: 'public-read',
            keyTransform: function (relative_filename) {
                return "kettle-service/" + argv.version + "/images/" + relative_filename;
            }
        }, {
            maxRetries: 5
        }));
});

// Concatenate & Minify CSS
gulp.task('fonts', () => {
    return gulp.src(['fonts/**'])
        .pipe(gulp.dest('./dist/static/fonts'));
});

gulp.task('html', () => {
    return gulp.src(['./**/*.html', '!index.html', '!indexTest.html', '!./bower_components/**', '!./node_modules/**', '!./WEB-INF/**'])
        .pipe(htmlhint({'doctype-first': false}))
        .pipe(htmlmin())
        .pipe(ngHtml2Js({
            moduleName: "posApp",
            prefix: (typeof argv.version != "undefined" ? argv.version + "/" : "")
        }))
        .pipe(concat("partials.js"))
        .pipe(uglify())
        .pipe(argv.hash === 'true' ? hash({
                version: properties.version,
                template: '<%= name %>.<%= hash %><%= ext %>'
            }) :
            hash({version: properties.version, template: '<%= name %><%= ext %>'}))
        .pipe(gulp.dest('dist/content/js'))
        .pipe(hash.manifest('contentJS.json', {
            deleteOld: true,
            sourceDir: './dist/content/js/'
        }))
        .pipe(gulp.dest('./dist/assets'));
});

gulp.task('clean', () => {
    return gulp.src('./dist')
        .pipe(clean());
});

gulp.task('compileIndex', function () {

    var data = {};
    data.staticJS = JSON.parse(fs.readFileSync('dist/assets/staticJS.json', 'utf8'));
    data.contentJS = JSON.parse(fs.readFileSync('dist/assets/contentJS.json', 'utf8'));
    data.staticCSS = JSON.parse(fs.readFileSync('dist/assets/staticCSS.json', 'utf8'));
    data.contentCSS = JSON.parse(fs.readFileSync('dist/assets/contentCSS.json', 'utf8'));
    data.baseUrl = properties.cdn[argv.deployment]; //base url for content files
    data.isLocal = argv.deployment == "local";
    data.version = (typeof argv.version != "undefined" ? argv.version + "/" : "");
    data.baseUrls = properties.baseUrls[argv.deployment]; //base urls for rest APIs
    data.webengageLicence = properties.webengageLicence[argv.deployment];
    var handlebarOpts = {
        ignorePartials: true,
        helpers: {
            eachInMapCss: function (map, block) {
                var out = '';
                Object.keys(map).map(function (prop) {
                    if (prop.indexOf(".css") >= 0) {
                        out += block.fn({key: prop, value: map[prop], data: data});
                    }
                });
                return out;
            },
            eachInMapJs: function (map, block) {
                var out = '';
                Object.keys(map).map(function (prop) {
                    if (prop.indexOf(".js") >= 0) {
                        out += block.fn({key: prop, value: map[prop], data: data});
                    }
                });
                return out;
            }
        }
    }
    return gulp.src('build/templates/index.hbs')
        .pipe(handlebars(data, handlebarOpts))
        .pipe(rename('index.html'))
        .pipe(gulp.dest('./'));
});

gulp.task('s3UploadStatic', () => {
    return gulp.src('./dist/static/**')
        .pipe(argv.deployment == "local" ? noop() : s3({
            Bucket: properties.buckets[argv.deployment].static,
            ACL: 'public-read',
            keyTransform: function (relative_filename) {
                return "kettle-service/" + argv.version + "/" + relative_filename;
            }
        }, {
            maxRetries: 5
        }))
});

gulp.task('s3UploadContent', () => {
    return gulp.src('./dist/content/**')
        .pipe(argv.deployment == "local" ? noop() : s3({
            Bucket: properties.buckets[argv.deployment].content,
            ACL: 'public-read',
            keyTransform: function (relative_filename) {
                return "kettle-service/" + argv.version + "/" + relative_filename;
            }
        }, {
            maxRetries: 5
        }))
});

// Watch Files For Changes
gulp.task('watch', function () {
    gulp.watch(properties.internalScripts, ['lint', 'contentJs', 'compileIndex']);
    gulp.watch('styles/main.css', ['contentCss', 'compileIndex']);
    gulp.watch(['./**/*.html', '!index.html', '!indexTest.html', '!./bower_components/**', '!./node_modules/**'], ['html', 'compileIndex']);
});

gulp.task('check-config', function () {
    if (typeof argv.deployment == 'undefined' || argv.deployment == null || argv.deployment.length == 0) {
        console.log("Please provide valid deployment parameter i.e --deployment=local valid oprions are local,dev,uat,prod");
    } else if (argv.deployment != 'local' && (typeof argv.version == 'undefined' || argv.version == null || argv.version.length == 0)) {
        console.log("Please provide valid version parameter i.e --version=0.0.1 please check previous version");
    } else {
        if (argv.deployment != 'local') {
            return runSequence('clean', 'lint', 'staticJs', 'contentJs', 'staticCss', 'contentCss', 'images', 'fonts', 'html', 'compileIndex', 's3UploadStatic', 's3UploadContent');
        } else {
            return runSequence('clean', 'lint', 'staticJs', 'contentJs', 'staticCss', 'contentCss', 'images', 'fonts', 'html', 'compileIndex', 'watch');
        }
    }
});

// Default Task
gulp.task('default', ['check-config']);

//for run:   gulp --merge=true --compress=true --hash=true --deployment=prod --version=0.0.1