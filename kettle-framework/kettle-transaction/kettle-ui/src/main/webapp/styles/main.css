/*
 *
 *  SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Adobe Systems Incorporated
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

@font-face {
    font-family: system;
    font-style: normal;
    font-weight: 300;
    src: local(".SFNSText-Light"), local(".HelveticaNeueDeskInterface-Light"), local(".LucidaGrandeUI"), local("Ubuntu Light"), local("Segoe UI Light"), local("Roboto-Light"), local("DroidSans"), local("Tahoma");
}

*{
	-webkit-user-select: text;
    font-family: "system", sans-serif;
    -webkit-font-smoothing: antialiased;
}

.browsehappy {
    margin: 0.2em 0;
    background: #ccc;
    color: #000;
    padding: 0.2em 0;
}

.nav, .pagination, .carousel, .panel-title a {
    cursor: pointer;
}

body {
    padding: 0px;
    background-color: #ffeeb4;
    height:100%

}

.btn,.btn:focus, .btn.focus{
	outline:none;
}

/* Everything but the jumbotron gets side spacing for mobile first views */
.header,
.footer {
    padding-left: 0px;
    padding-right: 0px;
}

/* Custom page header */
.header {
    border-bottom: 1px solid #e5e5e5;
}

/* Make the masthead heading the same height as the navigation */
.header h3 {
    margin-top: 0;
    margin-bottom: 0;
    line-height: 40px;
    padding-bottom: 19px;
}

/* Custom page footer */
.footer {
    padding-top: 19px;
    color: #777;
    border-top: 1px solid #e5e5e5;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    /* display: none; <- Crashes Chrome on hover */
    -webkit-appearance: none;
    margin: 0; /* <-- Apparently some margin are still there even though it's hidden */
}

.container-narrow > hr {
    margin: 30px 0;
}

/* Responsive: Portrait tablets and up */
@media screen and (min-width: 768px) {
    .container {
        max-width: 730px;
    }

    /* Remove the padding we set earlier */
    .header,
    .footer {
        padding-left: 0px;
        padding-right: 0px;
    }

    /* Space out the masthead */
    .header {
        margin-bottom: 30px;
    }
}

/* side menu styles*/
body {
    margin-top: 0px;
}

.glyphicon {
    margin-right: 10px;
}

.panel-body {
    padding: 0px;
}

.panel-body table tr td {
    padding-left: 15px
}

.panel-body .table {
    margin-bottom: 0px;
}

.greenText{
    color: #008000;
}

.redText{
    color: red;
}

/*button*/

.posProductButton {
    margin: 5px;
    height: 90px;
    width: 103px;
    padding: 0px;
    position: relative;
    white-space: normal;
    background-color: lightblue;
}

.favChaiButton{
    /*
    margin: 5px;
    */
    height: 90px;
    width: 190px;
    padding: 0px;
    position: relative;
    white-space: normal;
    background-color: #E5E5E5;
    border-radius: 5px;
    /*position: relative;
    top:-15px;*/
}
.vegButton {
    background-color: limegreen;
}

.nonVegButton {
    background-color: #d43f3a;
}

.coverButton {
    margin: 30px;
    height: 100px;
    width: 100px;
    padding: 0px;
    white-space: normal;
    background-color: #577c39;
    color: #efefef;
}
.coverLogoutButton {
    margin:10px;
    padding: 20px;
    white-space: normal;
    background-color: #577c39;
    color: #efefef;
}

.coverLogoutButton.focus, .coverLogoutButton:focus, .coverLogoutButton:hover {
    color: #fff;
}

.coverButton.focus, .coverButton:focus, .coverButton:hover {
    color: #fff;
}

.riderButton {
    margin: 30px;
    height: 80px;
    width: 80px;
    padding: 0px;
    white-space: normal;
    background-color: #ced3ea;
}

.riderSelectedButton, .riderSelectedButton:active, .riderSelectedButton.active, .riderSelectedButton:focus {
	margin: 30px;
    height: 80px;
    width: 80px;
    padding: 0px;
    white-space: normal;
    color : #fff;
    background-color: green;
}
.coverButton a{
	text-decoration: none;
    color: #fff;
    height: 100px;
    display: table-cell;
    vertical-align: middle;
    width: 100px;
}
.coverButton a:hover{
    text-decoration: none;
    color: #fff;
}

.container-product-display {
    background-color: lemonchiffon;
}

.yellow-background {
    background-color: yellow;
}

.green-background {
    background-color: greenyellow;
}

.lime-background {
  background-color:#5df15d;
}

.red-orange-background {
  background-color:#ff864a;
}

.productRow {
    margin-left: 20px;
    margin-top: 20px;
    margin-bottom: 20px;
}

/*navbar*/

.navbar-default {
    background-color: beige;
    border-color: #5CB85C;
    margin-bottom: 0px;
}

/* NAVIGATION ======================================================================== */

body {
    overflow-y: scroll;
    overflow-x: hidden;
}

.navigacija {
    text-trans: uppercase;
    position: static;
    background-color: beige;
}

.navbar-collapse.collapse {
    overflow: hidden;
}

@media (max-width: 768px) {
    .navigacija > ul {
        margin-top: 0;
        margin-left: 0;
    }

    .navbar-collapse.collapse {
        overflow: visible !important
    }
}

.dropdown-menu {
    /*width: 50%;*/;
}

.modal-content {
    min-height: 100px;
}

.ddmenu {
    position: absolute ;
    /* z-index: 1000;*/
    display: none;
    float: left;
    /*min-width: 500px;*/
    max-width: 100%;
    height: 110px;
    padding: 5px 0;
    margin: 0 auto;
    text-align: center
}

.addOnMenu {
    position: absolute;
    z-index: 1000;
    display: none;
    float: right;
    min-width: 660px;
    max-width: 100%;
    padding: 5px 0;
    margin: 0 auto;
    text-align: center
}

.ddstraightMenu {
    position: relative;
    margin-left: 40px;
    padding: 5px 0;
    text-align: center
}

.posSubCategoryButton {
    margin-top: 0px;
    margin-left: 0px;
    margin-right: 0px;
    height: 80px;
    width: 80px;
    padding: 0px;
    font: 14px sans-serif;
    white-space: normal;
    background-color: lightskyblue;
}

.dropdown-menu li {
    display: inline-block;
}

.dropdown-menu li.dropdown .dropdown-menu {
    top: 210px;
}

.dropdown-menu li.dropdown {
    background-position: 5px 9px !important;
}

.navbar-brand img {
    max-width: 130px;
}

/*OrderScreen*/

.container-order-display {
    background-color: beige;
    margin: 0px;
    padding-left: 0px;
}

.orderItemsDisplay {
	border-top: #ffeeb4 8px solid;
    overflow: auto;
    overflow-x: hidden;
    height: 520px;
}

.transactionDisplay {
    overflow: hidden;
    background-color: whitesmoke;
}

.transactionDisplayCOD {
    height: 200px;
    max-height: 200px;
    overflow: auto;
    background-color: whitesmoke;
}

.transactionDisplayCAFE {
    height: 280px;
    max-height: 280px;
    overflow: auto;
    background-color: whitesmoke;
}

.btn-group-lg>.btn, .btn-group-lg>.btn-lg {
    padding: 0px 0px; 
}

.btn-lg-text-sm {
	margin : 5px;
	padding: 6px 8px; 
	font-size :14px;
	height: 55px;
	width: 70px
}

.btn-lg-text-auto {
	margin: 1em;
	padding: 1em;
	font-size: 14px;
}

.btn-md-text-sm {
	margin : 2px;
	padding: 2px 3px; 
	font-size :14px;
	height: 35px;
    width: 90px;
}
.orderItemBox {
    background-color: white;
    margin-top: 10px;
    margin-left: 0px;
    border-color: #88350f;

}

.orderItemComplimentaryBox {
    background-color: azure;
    margin-top: 10px;   
    margin-left: 0px;
    border-color: #88350f;
}

.btn-sq-lg {
    width: 100px;
    height: 50px;
    background-color: #E3E1B8;
    font: 15px sans-serif;
    text-decoration: none;
    border: 1px solid #000;
}

.btn-sq-lg-cm {
    width: 100px;
    height: 50px;
    background-color: #E3E1B8;
    font: 15px sans-serif;
    text-decoration: none;
    border: 1px solid #000;
    margin-top: 5px;
}

.orderText {
    margin-top: 2px;
    font-family: Arial, Verdana, serif;
    font-weight: bold;
    font-size: 15px;
    text-align: right;
    color: #3B83BD;
    max-height: 50px;
    background-color: lightyellow;
}

.inventory {
    font-size: 14px !important;
    text-align: center;
}


.inventory-badge  {
  position: absolute;
    padding: 2px;
    top: -10px;
    left: 3px;
    border-radius: 10%;
    background: orange;
    font-size: 14px;
    min-width: 20px;
}

.inventory-badge-left {
  position: absolute;
    padding: 2px 4px;
    border-radius: 20%;
    background: #f788ba;
    font-size: 14px;
    top: 15px;
    left: -12px;
    min-width: 20px;
}

.orderTextWOSizeProfile {

    font-family: Arial, Verdana, serif;
    font-weight: bold;
    font-size: 15px;
    text-align: center;
    color: #3B83BD;
    overflow: scroll;
    height: 70px;
    background-color: lightyellow;
    display: inline-block;
}

.plusMinus {
    padding-top: 10%;
}

.itemPrice {
    font-family: Arial, sans-serif;
    font-weight: bold;
    font-size: 20px;
    text-align: center;
    color: firebrick;
}

.itemPriceWOCustomise {
    margin-top: -70%;
    font-family: Arial, sans-serif;
    font-weight: bold;
    font-size: 24px;
    text-align: center;
    color: firebrick;
}

.btn-circle {
    width: 30px;
    height: 30px;
    text-align: center;
    padding: 6px;
    font-size: 12px;
    line-height: 1.42;
    border-radius: 15px;
}

.btn-circle-otp {
    width: 40px;
    height: 40px;
    text-align: center;
    padding: 6px;
    font-size: 12px;
    line-height: 1.42;
    border-radius: 20px;
    border-width: 1px;
    background-color: #00BD60;
    color: white;
    font-size: 20px;
}

/*input[type="number"] {
    margin-left: 15px;
    width: 150px;
    font-family: sans-serif;
    font-size: 18px;
    appearance: none;
    box-shadow: none;
}*/

/*input[type="text"] {
    margin: 15px;
    width: 250px;
    overflow: scroll;
    font-family: sans-serif;
    font-size: 18px;
    appearance: none;
    box-shadow: none;
    border-radius: 2px;
}*/

.centralDiv {
    display: inline-block;
    margin-top: 15px;
    text-align: center;
    align-items: center;
    display: flex;
    justify-content: center;
}

.input-form-control {
    width: 300px;
    margin-left: 20px;
}

.sizeBtnClicked {
    background-color: lavender;
}

/*Customer Screen*/

.brandImg {
    width: 300px;
}

.btn-cs-lg {
    width: 80px;
    height: 40px;
    background-color: #E3E1B8;
    font: 22px sans-serif;
    margin-top: 20px;
    margin-left: 36%;
    text-decoration: none;
    border: 2px solid #000;
}

.csLoginForm {
    /*background-color: cornsilk;*/
    margin-top: 40px;
}

input[type=number]::-webkit-outer-spin-button,
input[type=number]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

input[type=number] {
    -moz-appearance: textfield;
}

.customerBtn:disabled {
    opacity: 0.4;
    background: saddlebrown;
    border-color: saddlebrown;
    color: white;
}

.col-md-12 .column form {
    display: inline-block;
    margin: auto;
}

.count {
    line-height: 100px;
    color: white;
    margin-left: 30px;
    font-size: 25px;
}

.col-centered {
    float: none;
    margin: 0 auto;
}

/*grid css*/
.myGrid {
    height: 600px;
    background: #fff;
	margin: 10px auto;
}

.addOnButton {
    margin-top: 10px;
    margin-left: 5px;
    margin-right: 5px;
    height: 75px;
    width: 75px;
    padding: 0px;
    white-space: normal;
    background-color: lightcyan;
    border: 1px solid darkkhaki;
    font-size: 15px;
}

.logoutButton {
    margin-left: 5px;
    margin-right: 5px;
    height: 40px;
    padding: 5px;
    white-space: normal;
    background-color: lightcyan;
    border: 1px solid darkkhaki;
    font-size: 15px;
}

.orderButton {
    margin-top: 10px;
    margin-left: 5px;
    margin-right: 5px;
    width: 60px;
    height: 60px;
    padding: 0px;
    white-space: normal;
    background-color: lightcyan;
    border: 1px solid darkkhaki;
}

.addOnButtonSelected {
    margin-top: 10px;
    margin-left: 5px;
    margin-right: 5px;
    height: 75px;
    width: 75px;
    padding: 0px;
    white-space: normal;
    background-color: #FFAAAC;
    border: 1px solid darkslategrey;
    font-size: 15px;
}

.variantButton {
    margin-right: 5px;
    height: 40px;
    width: 135px;
    padding: 0px;
    white-space: normal;
    background-color: lightcyan;
    border: 1px solid darkkhaki;
    font-size: 14px;
}


.variantButtonSelected {
    margin-right: 5px;
    height: 40px;
    width: 135px;
    padding: 0px;
    white-space: normal;
    background-color: #FFAAAC;
    border: 1px solid darkslategrey;
    font-size: 14px;
}
table.floatThead-table {
    border-top: none;
    border-bottom: none;
    background-color: #FFF;
}
.lastOrderCss{
    color: red;
}

.firstTwo{
    color: #1e1aff;
}


form[name="customerEditableForm"] > div {
    height: auto;
    padding: 10px 0;

}

form[name="customerEditableForm"] .title {
    display: inline-block;
    font-weight: bold;
    padding-top: 5px;
    vertical-align: top;
    min-width: 90px;
    text-align: left;
}

form[name="customerEditableForm"] .profileField {
    display: inline-block;
    padding-top: 3px;
    vertical-align: top;
    margin-left: 15px;
}

form[name="customerEditableForm"] input {
    display: inline-block;
    padding-top: 5px;
    vertical-align: top;
    margin-left: 35px;
}

.addressBtn{
    margin-top: 10px;
    margin-left: 25px;
    margin-right: 25px;
    height: 150px;
    width: 150px;
    padding: 0px;
    white-space: normal;
    background-color: lightcyan;
    border: 1px solid #50E3C2;
    font-size: 18px;
}

.newAddressFields{
    width: 250px;
    font-size: 20px;
    text-align: center;
    border: 1px solid #737370;
    line-height: 1.2em;
    margin-left: 8px;
    margin-bottom: 10px;
}

.prefAddButton{
    border: 2px solid #4A90E2;
}

.selectedAddButton {
    background-color: darksalmon;
}

/****Changes for assembly screen******/
#assembly .subOrders{     
	margin-left: 1px;
    width: 100%;
}
#assembly .subOrder{
	background-color: #BFF321;
    color: #000;
    padding: 3px;
    line-height: 18px;
    border-right:1px solid #fff;
    border-bottom:1px solid #fff;
}

#assembly .subOrderCombo{
	background-color: #63D2F6;
    color: #000;
    padding: 3px;
    line-height: 18px;
    border-right:1px solid #fff;
    border-bottom:1px solid #fff;
}


#assembly .add-on{
	background-color: #4F5047;
	color:#fff;
    padding: 3px ;
    margin: 3px;
    border-radius: 5px;
    float:left;
}

#assembly .order-details{
	background-color:#D6D8C7;
	color:#fff;
	padding:10px;
	border:1px solid #ddd;
	width:100%;
	display:table;
	font-weight:600;
}
#assembly .order-details.created,  #assembly .order-details.cancelRequested,
.panel-heading.created, .panel-heading.cancelled_requested{
	background-color:#F34C4C;
}
#assembly .order-details.acknowledged, .panel-heading.processing{
	background-color: #FBBC05;
    color: #2B2828;
}
#assembly .order-details.rtd, .panel-heading.ready_to_dispatch{
	background-color:#03AB03;
}
#assembly .order-details.rtd_cash, .panel-heading.ready_to_dispatch{
	background-color:#6495ed;
}
#assembly .order-details.ofd, .panel-heading.out_for_delivery{
	background-color:#b733b5;
}

#assembly .panel-body{
	margin:10px 0px;
}
#assembly .actions .btn{
	height: 60px;
    padding: 5px;
    text-transform: uppercase;
    font-size: 12px;
    margin : 10px 0px;
    
}

#assembly .col-lg-5 .actions .btn{
	width:110px;
}

#assembly .col-lg-6 .actions .btn{
	width:138px;
}

#assembly .suborder-addOn{
	display:table;
}

#assembly .suborder-addOn p{
	float: left;
    margin-top: 7px;
    margin-bottom: 0px;
}

#assembly .amount{
	font-size:15px;
	font-weight:600;
}

#assembly .amount .badge{
	text-transform: uppercase;
}

/******************** CODorderSearch styles *****************/

.customerInfoPanel{
	background:#fff;
	padding:10px;
	box-shadow: #ccc 0 0 4px 0;
}

.free-kettle {
    color: #EFE8E8;
    background-color: #D82F2F;
    border-color: #ebccd1;
    font-size: 20px;
    padding: 10px;
    margin-bottom:0px;
}

.xalert{
	padding:10px;
    font-size: 16px;
    font-weight: bold;
    text-align: center;
}
.yalert{
	margin: 7px;
	height: 46px;
    width: 80px;
	padding:7px;
    font-size: 14px;
    text-align: center;
}
 
.xalert.error{	
	background:red;
}

.xalert.success{	
	background:green;
}

.xalert.basic{	
	background:#ccf2ff;
}

.yalert.error{	
	background:red;
}

.yalert.success{	
	background:green;
}

.yalert.basic{	
	background:#ccf2ff;
}

.panel-info > .panel-heading, .panel-success > .panel-heading {
    text-align: center;
    color:#fff;
}

.customerFormCSS {
    margin-top: 90px;
    border: 1px solid #88350f;
    text-align: center;
    height:100% !important;
}

.strike {
    display: block;
    text-align: center;
    overflow: hidden;
    white-space: nowrap;
}

.strike > span {
    position: relative;
    display: inline-block;
}

.strike > span:before,
.strike > span:after {
    content: "";
    position: absolute;
    top: 50%;
    width: 9999px;
    height: 1px;
    background: red;
}

.strike > span:before {
    right: 100%;
    margin-right: 15px;
}

.strike > span:after {
    left: 100%;
    margin-left: 15px;
}


/******************** Video Background *****************/

.articleBorder {
    /*  just a fancy border  */
    position: absolute;
    /*top: 120px;
    left: 90px;
    /!*right: 0;*!/
    bottom: 10px;*/
    padding: 10px;
    border: 10px solid rgba(192, 183, 167, 0.5);
    margin: 20px;
}

#video-background {
    /*  making the video fullscreen  */
    position: fixed;
    right: 0;
    bottom: 0;
    min-width: 100%;
    min-height: 100%;
    width: auto;
    height: auto;
    z-index: -100;
}

video{
    filter: grayscale(100%);
}

.borderText {
    color: #737370;
    /*text-shadow: -1px 0 black, 0 1px black, 1px 0 black, 0 -1px black;*/
}

.couponCode{
	height: 35px;margin-right: 5px;text-align: center;text-transform: uppercase;
}
.couponCodeContainer{
	margin-top:20px;
}
.col-xs-3.couponCodes{
	padding: 10px 5px;
    border: 1px solid #ddd;
    background-color: #8DA456;
    color: #fff;
    cursor: pointer;
    border-radius: 5px;
}
.chaayosCancel{
	font-size: 24px;
    padding: 0px 10px;
    font-weight: 100;
}
.chaayosBadge{
	display: inline-block;
    min-width: 10px;
    padding: 3px 7px;
    font-size: 12px;
    font-weight: 100;
    line-height: 1;
    color: #4A4141;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    background-color: #FFFEFE;
    border-radius: 5px;
    margin-bottom: 5px;
}
.holdDetailsSection{
	margin-top:5px;
}
.subscriptionDetails{
	padding:10px;
	margin-top:5px;
}
.subscriptionDetails span{
	padding: 5px 10px;
    margin: 3px;
    color: #015505;
    border-radius: 2px;
    border: #ddd 1px solid;
    display: inline-block;
    background: #eee;
}
.appliedCoupon{
	width:250px;
	float:right;
	margin-right:10px;
}
.delivery-img{
	background-image: url("../images/free-delivery-vector-black.jpg");
}
#customerSocketWidget{
	background-color: white;
	border: 1px solid #ddd;
}

#customerSocketWidget .alert-warning{
	margin-bottom: 0px;
    color: #fcf8e3;
    background-color: #B87E5C;
    border-color: #faebcc;
}
#customerSocketWidget .socketActive{
	padding:10px;
}
#customerSocketWidget .activity{
    padding-top: 5px;
    padding-left: 10px;
}
#customerSocketWidget .activity:last-child span{
    background-color: #069E06;
    color: white;
    padding: 3px 5px;
    border-radius: 3px;
    font-weight: 700;
}
#customerSocketWidget .title{
	font-size:16px;
}

.fullScreenLoader{
    position: fixed;
    top:0;
    bottom: 0;
    left: 0;
    right: 0;
    text-align: center;
    background: rgba(255,255,255,.8);
    z-index: 99999999999999;
}

.fullScreenLoader img{
    margin-top: 10%;
}

.app-modal-window .modal-dialog {
  width: 1200px;
  height: 1200px;
  max-height: 1200px;
}

#login-pills .nav>li>a{
    padding: 10px 15px;
}

.nav>li>a{
	padding:10px 25px;
}

.upValue {
	background-color: green;
	color: white;
}

.downValue {
	background-color: red;
	color: white;
}

.tableSpanText {
	color: lightskyblue;
	font: 19px sans-serif;
	margin-left: 5px;
	padding-top: 3px;
}

.tableLabelText {
	color: black;
	background-color: chartreuse;
	font: 19px sans-serif;
	margin-left: 5px;
	padding-top: 3px;
}

.tableLabelHighlightText {
	color: black;
	background-color: red;
	font: 19px sans-serif;
	margin-left: 5px;
	padding-top: 3px;
}

.unitLabelText {
	color: lightskyblue;
	font: 15px sans-serif;
	font-weight: bold;
}
.firstColumnTable {
	text-align: left;
}

.transactionRow {
	text-align: left; 
	margin : 0 0 0
}
.transactionAmount {
	float: right; 
	margin-right: 10px; 
	color: limegreen; 
	font-weight: bold;
}

/*************** redemption modal styles *****************/
.greenBtn{
    background: green !important;
}

.inputOrderNumber {
	height: 34px;
    padding: 6px 12px;
    font-size: 14px;
    line-height: 1.42857143;
    color: #555;
    background-color: #fff;
    background-image: none;
    border: 1px solid #ccc;
    border-radius: 4px;
}

.pageHeader {
	text-align: center;
	color: #737370; 
	margin-top: 30px; 
}

.orderSearchText {
	color: blue; 
	font: 18px sans-serif; 
	margin-left: 5px;
}

.timerDisplay {
	border-radius : 50%;
	font-size: 20px;
	font-weight : bold;
	background : #ddd;
	text-align: center;
	line-height : 55px;
	margin : 10px;
	display : inline-block;
	border : #29dac3 8px solid;
	width : 70px;
	height :70px;
}
.timerDisplay.alertx {
	background: orange;
}

.settlementMeta{
    margin: -15px -15px 20px -15px;
    padding: 15px 0;
    color: #31708f;
    background-color: #d9edf7;
    border-color: #bce8f1;
    text-align: center;
}

.categoryListAnchorClass {
	border: 1px solid #337ab7;
    border-radius: 5px;
    background-color: #337ab7;
    color: #fff;
    font-size: 15px;
    width: 90px;
    height: 55px;
    margin: 0 5px 5PX 0;
    padding: 15px 0px !important;
    text-align: center;
}

.greenBg{
    background: #f7a4a4 !important;
}

.outOfDelivery{
	    border: 5px red solid;
}

.cardCity {
	/* Add shadows to create the "card" effect */
	box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2);
	transition: 0.3s;
	background: #eee;
	color: #333;
	font-size: x-large;
	height: 60px;
	margin: 10px;
    cursor: pointer;
}

.selectedCard {
	border: 5px green solid;
}

.cityLabel {
	font-size: x-large;
	color: red;
	margin-top: 20px;
	margin-left: 42px;
}

/* On mouse-over, add a deeper shadow */
.cardCity:hover {
	box-shadow: 0 8px 16px 0 rgba(0, 0, 0, 0.2);
}

/* Add some padding inside the card container */
.containerCard {
	padding: 2px 16px;
}

.removeSelfCard {
	margin-left: 10px;
	font-size: 20px;
	color: red;
	font-weight: bolder;
	cursor: pointer;
}

.otp-verifying {
    color: #EFE8E8;
    background-color: #D82F2F;
    border-color: #ebccd1;
    font-size: 14px;
    padding: 3px;
    margin-bottom:0px;
}

.otp-verified {
    color: #EFE8E8;
    background-color: #3c763d;
    border-color: #ebccd1;
    font-size: 14px;
    padding: 3px;
    margin-bottom:0px;
}

.otp-sent {
    color: #EFE8E8;
    background-color: #f0ad4e;
    border-color: #ebccd1;
    font-size: 14px;
    padding: 3px;
    margin-bottom:0px;
}

.otp-verified-already {
    color: #EFE8E8;
    background-color: #838535;
    border-color: #ebccd1;
    font-size: 14px;
    padding: 3px;
    margin-bottom:0px;
}

/********************** cover view styles ***************************/
.coverView {

}

.frontLoadSection{
    position: relative;
    background: #d3d1d2;
}

.coverView .header{
    background: #577c39;
    padding: 5px 0;
    color: #fff;
    font-weight: bold;
    font-size: 18px;
    border: none;
    height: 70px;
    margin-bottom: 0px;
}

.coverView .header .leftSection{
    display: block;
    float: left;
}

.coverView .header .rightSection{
    display: block;
    float: right;
}

.coverView .header .hbtn, .coverView .header .indicator{
    background: transparent;
    display: inline-block;
    text-align: center;
    padding: 0 10px 5px 10px;
    border-radius: 0px;
    text-transform: uppercase;
    float: inherit;
}

.indicator1{
    display: flex;
    text-align: center;
    text-transform: uppercase;
    justify-content: flex-end;
    align-items: center;
    /*float: inherit;*/
}
.circle
{   width : 60px;
    height : 60px;
    border-radius : 50%;
    /*border : 2px solid black;	!* can alter thickness and colour of circle on this line *!*/
    background-color:#dffcc6;
}

.tilt {
    transform: rotate(30deg);
}

/* Circle Text Container - constrains text area to within the circle */
.circle_text_container
{   /* area constraints */
    width : 70%;
    height : 70%;
    max-width : 70%;
    max-height : 70%;
    margin : 0;
    padding : 0;

    /* some position nudging to center the text area */
    position : relative;
    left : 15%;
    top : 15%;

    /* preserve 3d prevents blurring sometimes caused by the text centering in the next class */
    transform-style : preserve-3d;
}

/* Circle Text - the appearance of the text within the circle plus vertical centering */
.circle_text
{
    /* change font/size/etc here */
    font-size: 15px;
    color:#577c39 ;
    text-align : center;

    /* vertical centering technique */
    position : relative;
    top : 50%;
    transform : translateY(-50%);
    overflow-x:auto;

}

.coverView .header .hbtn:hover{
    color: #fff;
    cursor: pointer;
}

.coverView .header .hbtn img, .coverView .header .indicator img{
    height: 20px;
    margin: 5px 0;
}

.coverView .header .verticalSplit {
    width: 2px;
    background: #ccc;
    display: inline-block;
    margin: 0 10px;
    height: 60px;
    float: inherit;
}
.verticalSplit1{
    width: 2px;
    background: #ccc;
    display: inline-block;
    margin: 0 10px;
    height: 60px;
    float: inherit;
   transform: rotate(30deg);
}

.coverView .header .indicator.error{
    background:red !important;
}

.coverView .header .indicator.success{
    background:green !important;
}

.coverView .last3Orders{
    height: 40px;
    line-height: 40px;
    font-size: 18px;
    padding: 0 10px;
    background: #20b5a8;
}

.coverView .last3Orders a{
    font-size: 20px;
}

.coverView .mainBtnSection{
    width: 160px;
    position: absolute;
    bottom: 0;
    left: 0;
    top: 110px;
}

.coverView .analyticsSection{
    left: 160px;
    bottom: 0;
    right: 0;
    top: 110px;
    position: absolute;
}

.coverView .analyticsSection .table1{
	position:absolute;
	left:0;
	top:0;
	height:100%;
	width:25%;
	overflow:auto;
}

.coverView .analyticsSection .tablesWrapper {
	position: absolute;
    top:0;
    left: 0;
    right: 0;
    height: 100%;
    padding: 2px;
}

.coverView .analyticsSection .tablesWrapper.liveInventory {
	position: absolute;
    top:0;
    left: 25%;
    right: 20%;
    height: 100%;
    padding: 2px;
}

.coverView .analyticsSection .table2{
    position: absolute;
    top:0;
    left: 0;
    width: 60%;
    height: 60%;
    padding: 2px;
    overflow: auto;
}

.coverView .analyticsSection .table3{
    position: absolute;
    top:60%;
    left: 0%;
    width: 60%;
    bottom: 0;
    padding: 2px;
    overflow: auto;
}

/* .coverView .analyticsSection .table4{
    position: absolute;
    top:0;
    left: 60%;
    right: 0;
    height: 70%;
    padding: 2px;
    overflow: auto;
    width: 20%;
} */

.coverView .analyticsSection .table4{
    position: absolute;
    top: 0%;
    left: 60%;
    right: 0;
    height: 70%;
    padding: 2px;
    overflow: auto;
}

.coverView .analyticsSection .table4.full{
    position: absolute;
    top: 0;
    left: 60%;
    right: 0;
    height: 100%;
    padding: 2px;
    overflow: auto;
}

.coverView .analyticsSection .table5{
    position: absolute;
    top: 70%;
    left: 60%;
    right: 0;
    bottom:0;
    padding: 2px;
    overflow: auto;
}

.coverView .analyticsSection .table6{
    position: absolute;
    top: 0;
    left: 80%;
    right: 0;
    height: 100%;
    padding: 2px;
    overflow: auto;
}

.coverView .analyticsSection .table1 table{
    margin-bottom: 0px;
    font-size: 14px;
    line-height: 1.25;
}

.coverView .analyticsSection .table2 table,
.coverView .analyticsSection .table3 table,
.coverView .analyticsSection .table4 table,
.coverView .analyticsSection .table5 table,
.coverView .analyticsSection .table6 table{
    margin-bottom: 0px;
    font-size: 14px;
    line-height: 1.25;
}

.coverView .analyticsSection table th{
    font-size: 14px;
}

.coverView .analyticsSection .table1 th{
    font-size: 11px;
}

.coverView .analyticsSection .table1 td{
    background: #f5e492;
}

.coverView .analyticsSection .table2 td{
    background: #dffcc6;
}

.coverView .analyticsSection .table3 td{
    background: #fff;
}


.coverView .analyticsSection .table6 td{
    background: #ffefb4;
}

.coverView .analyticsSection .table4 td, .coverView .analyticsSection .table5 td{
    background: #ffefb4;
}

.coverView .analyticsSection th.yellow{
    background: #f2a904;
}

.coverView .analyticsSection th.brown{
    background: #4e3025;
}

.coverView .analyticsSection th.orange{
    background: #f46715;
}

.coverView .analyticsSection th.teal{
    background: #198160;
}

.coverView .analyticsSection th.firebrick{
    background: #b22222;
}

.coverView .analyticsSection .table4 th{
    background: #577c39;
}

.coverView .analyticsSection th{
    color: #fff;
}

.coverView table td,.coverView table th{
    border: 1px solid #d3d1d2;
}

.coverView .coverButton{
    background: #384f23;
    color: #fff;
    margin: 1% 2%;
    height: 16%;
    border: none;
    border-radius: 6px;
    font-size: 18px;
    width: 95%;
    text-align: center;
}

.coverView .coverButton img{
    height: 25%;
}

.margin-5 {
	margin: 5px !important;
	width: 95%  !important;
}

.orderLineItem {
	background: white;
	margin: 1em;
	padding: 1em;
	border-style: groove;
}

.brandName.CH {
    background: #4f964f;
    color: #FFF;
}

.brandName.DC {
    background: #8e4f96;
    color: #FFF;
}
.rowSelected {
    background: #aef1b0;
}

.reject-btn{
    margin-right: 390px;
}

.like-btn-div{
    padding-right: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
}
.like-btn{
    height:50px;
    width:70px;
    background-color: white;
    border: none;
}
.btn-heart1{
    height:50px;
    /*width:70px;*/
    background-color: white;
    border: none;
}