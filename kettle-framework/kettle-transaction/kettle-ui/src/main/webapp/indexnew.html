<!doctype html>
<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<html>
<head>
    <meta charset="utf-8">
    <title>Kettle - POS</title>

    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <meta name="description" content="kettle POS">
    <meta name="author" content="@chaayos">

    <!-- Place favicon.ico and apple-touch-icon.png in the root directory -->

    <link rel="icon" type="image/png" sizes="32x32" href="favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="96x96" href="favicon-96x96.png">
    <link rel="icon" type="image/png" sizes="16x16" href="favicon-16x16.png">
    <!-- build:css(.) styles/vendor.css -->
    <!-- bower:css -->
    <link rel="stylesheet" href="./bower_components/bootstrap/dist/css/bootstrap.min.css"/>

    <link rel="stylesheet" type="text/css" href="./bower_components/angular-ui-grid/css/ui-grid.min.css"/>
    <link href="styles/angular-datepicker.min.css" rel="stylesheet" type="text/css">
    <link rel="stylesheet" href="styles/font-awesome.min.css">
    <link rel="stylesheet" href="styles/angular-block-ui.min.css"/>


    <!-- endbower -->
    <!-- endbuild -->
    <!-- build:css(.tmp) styles/main.css -->
    <link rel="stylesheet" href="styles/main.css">
    <!--<link rel="stylesheet/less" type="text/css" href="styles/main.less" />-->
    <!-- endbuild -->
    <script type = "text/javascript" >
        window.version = "";
        window.analyticsUrl = window.location.protocol+"//"+window.location.host;
        window.scmUrl = window.location.protocol+"//"+window.location.host;
        window.masterUrl = window.location.protocol+"//"+window.location.host;
        window.crmUrl = window.location.protocol+"//"+window.location.host;
        window.kettleUrl = window.location.protocol+"//"+window.location.host;
        window.channelPartnerUrl = window.location.protocol+"//"+window.location.host;
        window.inventoryUrl = window.location.protocol+"//"+window.location.host;

        history.pushState(null, null, '');
    	window.addEventListener('popstate', function(event) {
   			history.pushState(null, null, '');
    	});
    </script>
    <!-- Hotjar Tracking Code for https://internal.chaayos.com/kettle-service/indexnew.html#/login -->
    <script>
        (function(h,o,t,j,a,r){
            h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
            h._hjSettings={hjid:3087023,hjsv:6};
            a=o.getElementsByTagName('head')[0];
            r=o.createElement('script');r.async=1;
            r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
            a.appendChild(r);
        })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
    </script>
</head>
<body>
<div id="handwashAlert" style="padding: 20px; background: green; color: #FFF; font-size:21px; display: none;">Please wash your hands with sanitizer.</div>
<!--[if lte IE 8]>
<p class="browsehappy">You are using an <strong>outdated</strong> browser. Please <a href="http://browsehappy.com/">upgrade
    your browser</a> to improve your experience.</p>
<![endif]-->

<noscript>
    Please enable Javascript to experience this app :)
</noscript>


<div data-ng-app="posApp" data-ng-controller="MainCtrl">
    <div data-ui-view></div>
    <div class="fullScreenLoader" data-ng-show="showFullScreenLoader">
        <img src="images/ring.gif">
    </div>
    <div class="fullScreenLoader" data-ng-show="showModalLoader">
        <img src="images/ring.gif">
    </div>
</div>


<!--  &lt;!&ndash; Google Analytics: change UA-XXXXX-X to be your site's ID &ndash;&gt;
   <script>
     !function(A,n,g,u,l,a,r){A.GoogleAnalyticsObject=l,A[l]=A[l]||function(){
     (A[l].q=A[l].q||[]).push(arguments)},A[l].l=+new Date,a=n.createElement(g),
     r=n.getElementsByTagName(g)[0],a.src=u,r.parentNode.insertBefore(a,r)
     }(window,document,'script','https://www.google-analytics.com/analytics.js','ga');

     ga('create', 'UA-XXXXX-X');
     ga('send', 'pageview');
  </script>-->

<script>
    var audio = new Audio('./audio/pop_up_alert.mp3');
    var handwashAudio = new Audio('./audio/handwashAlert.mpeg');
    audio.loop = true;
    handwashAudio.loop = true;
    var play_interval_sound  = function(){
        audio.load();
        play_single_sound();
        setTimeout(stop_sound,5000);
    };
    var play_handwash = function(){
        document.getElementById("handwashAlert").style.display = "block";
        handwashAudio.load();
        handwashAudio.play();
        setTimeout(stop_handwash,4000);
        setTimeout(function () {
            document.getElementById("handwashAlert").style.display = "none";
        },6000);
    };

    function stop_handwash() {
        handwashAudio.pause();
    }

    function stop_sound(){
        audio.pause();
    }

    function play_single_sound() {
        audio.play();
    }
    function raiseAlert(hours, minutes, text){
        var date = new Date();
        date.setHours(hours);
        date.setMinutes(minutes);
        date.setSeconds(0);
        var ms = date.getTime() - new Date().getTime();
        if(ms>0){
            setTimeout(function(){
                bootbox.alert(text);
            }, ms);
        }
    }
    function playHourlyHandWash() {
        setInterval(function () {
            play_handwash();
        }, 60*60*1000)
    }
    playHourlyHandWash();
</script>

<!-- build:js(.) ./scripts/vendor.js -->
<!-- bower:js -->
<script src="./bower_components/jquery/dist/jquery.min.js"></script>
<script src="./bower_components/angular/angular.min.js"></script>
<script src="./bower_components/bootstrap/dist/js/bootstrap.min.js"></script>
<script src="./bower_components/angular-animate/angular-animate.min.js"></script>
<script src="./bower_components/angular-cookies/angular-cookies.min.js"></script>
<script src="./bower_components/angular-resource/angular-resource.min.js"></script>
<script src="./bower_components/angular-route/angular-route.min.js"></script>
<script src="./bower_components/angular-sanitize/angular-sanitize.min.js"></script>
<script src="./bower_components/lodash/lodash.min.js"></script>
<script src="./bower_components/restangular/dist/restangular.min.js"></script>
<script src="./bower_components/angular-ui-grid/ui-grid.min.js"></script>
<!-- endbower -->
<!--<script type="text/javascript" src="./js/3rdparty/deployJava.js"></script>-->
<script src="js/angular-datepicker.min.js"></script>
<!-- <script type="text/javascript" src="./js/qz-websocket.js"></script> -->

<script type="text/javascript" src="js/qz/rsvp-3.1.0.min.js"></script>
<script type="text/javascript" src="js/qz/sha-256.min.js"></script>
<script type="text/javascript" src="js/qz/qz-tray.js"></script>

<!--<script type="text/javascript" src="./js/3rdparty/html2canvas.js"></script>
<script type="text/javascript" src="./js/3rdparty/jquery.plugin.html2canvas.js"></script>-->
<script type="text/javascript" src="./js/angular-flash.min.js"></script>
<script type="text/javascript" src="./js/angular-timer.js"></script>
<script type="text/javascript" src="./js/humanize-duration.js"></script>
<!--<script type="text/javascript" src="./js/typeahead.js"></script>-->
<script type="text/javascript" src="./js/stomp.min.js"></script>
<script type="text/javascript" src="./js/sockjs.min.js"></script>
<script type="text/javascript" src="./js/spin.min.js"></script>
<!--<script type="text/javascript" src="./js/jquery.floatThead-slim.min.js"></script>-->
<!--<script type="text/javascript" src="./js/angular-block-ui.min.js"></script>-->
<script type="text/javascript" src="./js/socket.io-min.js"></script>
<script type="text/javascript" src="./js/pubnub.7.1.2.min.js"></script>
<script type="text/javascript" src="./js/pubnub-angular-4.2.0.min.js"></script>
<script type="text/javascript" src="./js/bootbox.min.js"></script>
<script type="text/javascript" src="./js/pusher.min.js"></script>
<script type="text/javascript" src="js/jsrsasign-latest-all-min.js"></script>
<script type="text/javascript" src="./js/moment.min.js"></script>
<script type="text/javascript" src = "./js/moment-timezone-with-data.min.js"></script>
 <script type="text/javascript" src="./js/FileSaver/FileSaver.min.js"></script>


<!-- App core & templates -->
<script src="./scripts/app.js?v=1.8"></script>

<!-- Data Models -->
<script src="./scripts/services/posAPI.js?v=1.8"></script>
<script src="./scripts/services/AppUtil.js?v=1.8"></script>
<script src="./scripts/services/PrintService.js?v=1.8"></script>
<script src="./scripts/services/coverUtils.js?v=1.8"></script>
<script src="./scripts/services/socketUtils.js?v=1.8"></script>

<!-- Directives -->
<!--<script src="./scripts/directives/autoFillSync.js"></script>-->
<script src="./scripts/directives/calendar.js"></script>
<script src="./scripts/directives/angular-spinner.js"></script>

<!-- Filters -->
<script src="./scripts/filters/reverse.js"></script>

<!-- Controllers -->
<script src="./scripts/controllers/main.js?v=1.8"></script>
<!--<script src="./scripts/controllers/about.js?v=1.8"></script>-->
<script src="./scripts/controllers/posCtrl.js?v=1.8"></script>
<script src="./scripts/controllers/ModalInstanceCtrl.js?v=1.8"></script>
<!--<script src="./scripts/controllers/addOnModalInstanceCtrl.js?v=1.8"></script>-->
<script src="./scripts/controllers/addOnNewModalInstanceCtrl.js?v=1.8"></script>
<script src="./scripts/controllers/settlementModalCtrl.js?v=1.8"></script>
<script src="./scripts/controllers/LoginController.js?v=1.8"></script>
<script src="./scripts/controllers/coverController.js?v=1.8"></script>
<script src="./scripts/controllers/orderSearchController.js?v=1.8"></script>
<script src="./scripts/controllers/customerController.js?v=1.8"></script>
<script src="./scripts/controllers/itemConsumptionModal.js?v=1.8"></script>
<script src="./scripts/controllers/supportLink.js?v=1.8"></script>
<script src="./scripts/controllers/settlementTypeModal.js?v=1.8"></script>
<script src="./scripts/controllers/printModal.js?v=1.8"></script>
<script src="./scripts/controllers/rawPrintModal.js?v=1.8"></script>
<script src="./scripts/controllers/loginModalCtrl.js?v=1.8"></script>
<script src="./scripts/controllers/orderSummaryCtrl.js?v=1.8"></script>
<!--<script src="./scripts/controllers/otpModalCtrl.js?v=1.8"></script>-->
<script src="./scripts/controllers/redeemModalCtrl.js?v=1.8"></script>
<script src="./scripts/controllers/managersreportCtrl.js?v=1.8"></script>
<script src="./scripts/controllers/terminalSelectCtrl.js?v=1.8"></script>
<script src="./scripts/controllers/AssemblyController.js?v=1.8"></script>
<script src="./scripts/controllers/inventoryCtrl.js?v=1.8"></script>
<!--<script src="./scripts/controllers/UnitHealthController.js?v=1.8"></script>-->
<script src="./scripts/controllers/fullOrderComplimentary.js?v=1.8"></script>
<script src="./scripts/controllers/offerModalCtrl.js?v=1.8"></script>
<script src="./scripts/controllers/subscriptionModalCtrl.js?v=1.8"></script>
<script src="./scripts/controllers/chaayosSelectModalCtrl.js?v=1.8"></script>
<script src="./scripts/controllers/suggestChaayosSelectModalCtrl.js?v=1.8"></script>
<script src="./scripts/controllers/inventoryModalCtrl.js?v=1.8"></script>
<script src="./scripts/controllers/openTAOrdersCtrl.js?v=1.8"></script>
<script src="./scripts/controllers/openDeliveryOrdersCtrl.js?v=1.8"></script>
<script src="./scripts/controllers/checkOrderAgainstInventoryModalCtrl.js?v=1.8"></script>
<script src="./scripts/controllers/pullManagementCtrl.js?v=1.8"></script>
<script src="./scripts/controllers/pullSettlementCtrl.js?v=1.8"></script>
<script src="./scripts/controllers/paymentRefundController.js?v=1.8"></script>
<script src="./scripts/controllers/pullSettlementsViewCtrl.js?v=1.8"></script>
<!--<script src="./scripts/controllers/freeChaiModalCtrl.js?v=1.8"></script>-->
<script src="./scripts/controllers/tableNumberModalCtrl.js?v=1.8"></script>
<script src="./scripts/controllers/redemptionSelectionCtrl.js?v=1.8"></script>
<script src="./scripts/controllers/employeeMealModalCtrl.js?v=1.8"></script>
<script src="./scripts/controllers/paidEmployeeMealModalCtrl.js?v=1.8"></script>
<script src="./scripts/controllers/complimentaryOrderModalCtrl.js?v=1.8"></script>
<script src="./scripts/controllers/assignRiderController.js?v=1.8"></script>
<script src="./scripts/controllers/manageRiderController.js?v=1.8"></script>
<script src="./scripts/controllers/loginVerificationModalCtrl.js?v=1.8"></script>
<script src="./scripts/controllers/expectedPriceProfileModalCtrl.js?v=1.8"></script>
<script src="./scripts/controllers/editSettlementModalCtrl.js?v=1.8"></script>
<script src="./scripts/controllers/giftCardModalCtrl.js?v=1.8"></script>
<script src="./scripts/controllers/expenseTrackingController.js?v=1.8"></script>
<script src="./scripts/controllers/meterReadingModalCtrl.js?v=1.8"></script>
<script src="./scripts/controllers/handOverDateController.js?v=1.8"></script>
<script src="./scripts/controllers/PnLReportController.js?v=1.8"></script>
<script src="./scripts/controllers/suggestGiftCardModalCtrl.js?v=1.8"></script>

<!-- COD -->
<script src="./scripts/controllers/COD/COD_coverController.js?v=1.8"></script>
<script src="./scripts/controllers/COD/COD_cslookup.js?v=1.8"></script>
<script src="./scripts/controllers/COD/addEditAddressModalCtrl.js?v=1.8"></script>
<script src="./scripts/controllers/COD/COD_customerOrderCtrl.js?v=1.8"></script>
<script src="./scripts/controllers/COD/openOrderSummaryCtrl.js?v=1.8"></script>
<script src="./scripts/controllers/COD/viewSDPCtrl.js?v=1.8"></script>
<script src="./scripts/controllers/COD/cafePartnerStatusCtrl.js?v=1.8"></script>
<script src="./scripts/controllers/COD/swiggyOrderViewCtrl.js?v=1.8"></script>
<script src="./scripts/controllers/COD/amazonOrderViewCtrl.js?v=1.8"></script>
<script src="./scripts/controllers/COD/cafeTimingsDashboardCtrl.js?v=1.8"></script>
<script src="./scripts/controllers/subscriptionSearchCtrl.js?v=1.8"></script>
<script src="./scripts/controllers/subscriptionOrderSearchCtrl.js?v=1.8"></script>
<script src="./scripts/controllers/subscriptionOrderByUnitCtrl.js?v=1.8"></script>
<script src="./scripts/controllers/desiChaiModalCtrl.js?v=7.9.7"></script>
<script src="./scripts/controllers/COD/partnerOrderDashboardCtrl.js?v=7.9.7"></script>
<script src="./scripts/controllers/COD/partnerCafeDashboardCtrl.js?v=7.9.7"></script>
<script src="./scripts/controllers/tableCtrl.js?v=7.9.7"></script>
<script src="./scripts/controllers/tableSummaryCtrl.js?v=7.9.7"></script>
<script src="./scripts/controllers/tableChangeCtrl.js?v=7.9.7"></script>

<!-- Services -->
<script src="./scripts/services/productService.js?v=1.8"></script>
<script src="./scripts/services/tableService.js?v=1.8"></script>
<script src="./scripts/services/InventoryService.js?v=1.8"></script>
<script src="./scripts/services/subscriptionService.js?v=1.8"></script>
<script src="./scripts/services/AuthenticationService.js?v=1.8"></script>
<script src="./scripts/services/UserService.js?v=1.8"></script>
<script src="./scripts/services/AssemblyService.js?v=1.8"></script>
<script src="./scripts/services/TrackingService.js?v=1.8"></script>
<script src="./scripts/services/desiChaiService.js?v=7.9.7"></script>
<script src="scripts/services/TrackingService.js"></script>
<script src="scripts/services/HttpRequestWithTimeout.js"></script>
<script src="scripts/services/swiggyOrderService.js"></script>

<script src="./scripts/services/APIJson.js?v=1.8"></script>


<!-- build:js({.tmp,app}) ./scripts/scripts.js -->
<!--<script src="./scripts/common.js"></script>-->
<script src="./bower_components/angular-ui-router/release/angular-ui-router.min.js"></script>
<script src="./bower_components/angular-bootstrap/ui-bootstrap.min.js"></script>
<script src="./bower_components/angular-bootstrap/ui-bootstrap-tpls.min.js"></script>
<script src="./bower_components/angular-messages/angular-messages.js"></script>


<!-- endbuild -->
</body>
</html>






