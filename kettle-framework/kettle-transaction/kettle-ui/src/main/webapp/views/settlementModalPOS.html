<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="modal-header" data-ng-init="init()">
    <div data-flash-message="5000"></div>
    <div class="cancel">
        <button data-ng-show="loader.loading" class="btn btn-success">Verifying...
            <i class="fa fa-spinner fa-spin"></i>
        </button>

    </div>
    <h3 class="modal-title pull-left">Bill settlement</h3>
    <div data-ng-if="!isPaidEmployeeMeal && !isGiftCardModalOpen"><label
            style="float: left;font-size: 19px;margin-left: 50px;margin-right: 20px;">Manual Bill No:
    </label><input type="number" class="form-control col-sm-2" style="width:150px" data-ng-model="manualBillBookNo"
                   data-ng-change="billNoChangeListener(manualBillBookNo)"/>
    </div>
    <h2 class="modal-title pull-right" style="color: #FF9933">Rs {{transactionObj.paidAmount}}
        <span style="color: #e0e8e0">|</span>
        <span style="color: #138808">Rs {{settlementRemaining()}}</span>
    </h2>
    <div class="clearfix"></div>
</div>

<div class="modal-body">
    <div class="row settlementMeta">
        <div class="col-xs-3">
            <label class="control-label" style="margin-left: 15px">Sub
                Total </label><span> {{subTotal.toFixed(2)}}</span>
        </div>
        <div class="col-xs-3">
            <label class="control-label" style="margin-left: 15px">Promotional Offer </label> <span> {{promotionalOffer.toFixed(2)}}</span>
        </div>
        <div class="col-xs-3">
            <label class="control-label" style="margin-left: 15px">Discount </label> <span> {{transactionObj.discountDetail.totalDiscount.toFixed(2)}}</span>
        </div>
        <div class="col-xs-3">
            <label class="control-label" style="margin-left: 15px">Tax </label>
            <span> {{transactionObj.tax.toFixed(2)}}</span>
        </div>
    </div>
    <div data-ng-if="!paymentFormPOS" style="text-align: center">
        <div class="btn btn-primary" data-ng-click="checkPaymentStatus()">
            <span data-ng-show="!showLoadingOnButton">Check Payment Status</span>
            <i data-ng-show="showLoadingOnButton" class="fa fa-refresh fa-spin" style="font-size:17px"></i>
        </div>
        <div data-ng-if="paymentBanner == 'Transaction is in progress...'" style="margin: 10px">Customer is paying through {{paymentMode}}. Please wait...</div>
        <div data-ng-if="paymentBanner == 'Transaction done...'"style="margin: 10px">Transaction has been done by {{paymentMode}}. Please proceed to place order...</div>
        <div data-ng-if="paymentBanner == 'Transaction Failed...'"style="margin: 10px">Transaction has been failed. Please ask customer to choose different payment mode...</div>
        <div data-ng-if="paymentBanner == 'Transaction pending...'"style="margin: 10px">Transaction is still pending. Please ask customer to make payment...</div>
        <div data-ng-if="paymentBanner == 'Transaction timed out...'"style="margin: 10px">Transaction has been timed out. Please ask customer to choose different payment mode...</div>
        <div class="alert text-center" style="margin-top: 5px" ng-class="paymentBanner == 'Transaction done...' ? 'otp-verified': (paymentBanner == 'Transaction is in progress...' ? 'otp-sent' : 'otp-verifying')">{{paymentBanner}}</div>
    </div>
    <div data-ng-if="paymentFormPOS">
        <form novalidate name="form.settlementForm" class="form-horizontal">
            <div class="row" data-ng-if="!isGiftCardModalOpen">
                <div class="col-xs-6">
                    <div class="form-group">
                        <label for="channelPartner" class="control-label col-sm-4">Channel Partner</label>
                        <select class="form-control col-sm-6" id="channelPartner" name="channelPartner"
                                style="width: 175px; margin-left: 30px"
                                data-ng-model="channelPartner"
                                data-ng-options="channel as channel.name for channel in transactionMetadata.channelPartner"
                                data-ng-change="changeCreditAccount(channelPartner)"
                                required>
                        </select>
                    </div>
                </div>
                <div class="col-xs-6" data-ng-if="showCustomerName && customerNameManual">
                    <div class="form-group">
                        <label class="control-label col-sm-4">Customer Name: </label>
                        <input type="text" class="form-control col-sm-2" style="width:250px" data-ng-model="name"
                               data-ng-change="addCustomerName(name)" required/>
                    </div>
                </div>
            </div>

			<div  data-ng-if="!isTableOrder || isTableCheckout" style="">

					<div class="row">
						<div class="col-xs-5">
							<label class="pull-right">Find Payment Details:</label>
						</div>
						<div class="col-xs-7">
							<div class="form-group row pull-left">
								<div class="col-xs-12 center-block">
									<button class="btn btn-primary"
											data-ng-click="associatedPaymentForDineIn()">Search Payments</button>
								</div>
							</div>
						</div>
					</div>
					<div class="row" data-ng-if="paymentDetail!=null">
						<h4>Payment Details:</h4>
						<div class="center-block">
							<div>Transaction Amount: {{paymentDetail.transactionAmount}}</div>
							<div>Associated OrderID: {{paymentDetail.externalOrderId}}</div>
							<div>Customer Name: {{paymentDetail.customerName}}</div>
							<div>Contact Number: {{paymentDetail.contactNumber}}</div>
							<button class="btn btn-primary" data-ng-if="paymentDetail.transactionAmount==transactionObj.paidAmount"
									data-ng-click="submitWithPayment(paymentDetail)">Create And Associate Order</button>
							<div class="alert alert-warning"
								 data-ng-if="(paymentDetail.transactionAmount-transactionObj.paidAmount)>=4">Amounts don't match</div>
						</div>
						<a href="#" class="btn btn-danger pull-right" data-ng-click="cancelSettlement()">Cancel</a>
					</div>
					<div class="alert alert-warning" data-ng-show="notFound">No payments found to attach</div>

			</div>

            <!-- <div class="form-group" data-ng-show="unitFamily == 'COD'">
                <label for="deliveryPartner"  class="control-label col-sm-4">Delivery Partner</label>
                <select class="form-control col-sm-6" id="deliveryPartner" name="deliveryPartner" style="width: 150px; margin-left: 30px"
                        data-ng-model="deliveryPartner"
                        data-ng-options="partner.id as partner.name for partner in transactionMetadata.deliveryPartner" required>
                </select>
            </div> -->
            <div data-ng-if="!isTableOrder || isTableCheckout">

                <ul class="nav nav-tabs track" style="margin-bottom: 20px;display: flex;flex-direction: row;justify-content: center;align-items: center">
                    <li role="presentation" data-ng-class="{'active':activeIndex == 1}"
                        data-ng-click="makePayment('CASH')"><a style="text-align: center">Pay By CASH</a></li>
                    <li role="presentation" data-ng-class="{'active':activeIndex == 2}"
                        data-ng-if="!isPaidEmployeeMeal && !isGiftCardModalOpen"
                        data-ng-click="payViaGiftCard('GIFT_CARD')">
                        <a style="text-align: center">Pay by Gift Cards'
                                <strong style="color: blue;"
                                        data-ng-if="customerPendingCardInfo != null && customerPendingCardInfo.hasCard && customerPendingCardInfo.cardAmount > 0.00">
                                    Card Amount : {{customerPendingCardInfo.cardAmount}}</strong>
                        </a></li>
                    <li role="presentation" data-ng-class="{'active':activeIndex == 3}"
                        data-ng-click="makePayment('CARD')"><a style="text-align: center">Pay by CARD</a></li>
                    <li role="presentation" data-ng-class="{'active': activeIndex == 4}"
                        data-ng-click="makePayment('QR')"><a style="text-align: center">Pay by QR/UPI/WALLET </a></li>
                    <li role="presentation" data-ng-class="{'active': activeIndex == 5}"
                        data-ng-click="makePayment('OTHER')"><a style="text-align: center">Pay by OTHER </a></li>
                </ul>

                <div data-ng-if="!showCardDetails">

<!--                    work here-->
                    <div class="row">
                        <div class="col-xs-6" data-ng-repeat="paymentMode in transactionMetadata.paymentModes"
                             data-ng-if="paymentMode.id!=10 && paymentMode.id!=21 && paymentMode.enabled && paymentMode.id!=4 && paymentMode.id!=5 && paymentMode.id!=18 && billSetlementType &&((defaultPid !=null && defaultPid==paymentMode.id) || (!isPaidEmployeeMeal && checkIdInPaymentModeType(paymentMode.id)))">
                            <div class="form-group" data-ng-if="!(paymentMode.id==10 && isSpecialOrder)">
                                <label style="margin-left: 15px" for="{{paymentMode.id}}" class="col-sm-6">
                                    {{paymentMode.description}}</label>
                                <input type="number" min="0" name="{{paymentMode.id}}"
                                       data-ng-model="payment[paymentMode.id]"
                                       data-ng-disabled="!paymentMode.enabled || paymentMode.id==4 || paymentMode.id==5 || paymentMode.id==18"
                                       data-ng-change="checkForCredit(paymentMode.id)" class="form-control col-sm-2"
                                       style="width: 100px;" id="{{paymentMode.id}}" required>
                                <button class="btn btn-info col-sm-2" style="margin-left: 5px"
                                        data-ng-disabled="!paymentMode.enabled"
                                        data-ng-click="settleAmountDirectly(paymentMode.id,paymentMode.needsSettlementSlip)"
                                        data-ng-show="paymentMode.id!=4 && paymentMode.id!=5 && paymentMode.id!=10 && paymentMode.id!=18">
                                    Go
                                </button>
                                <button class="btn btn-info col-sm-2" style="margin-left: 5px"
                                        data-ng-disabled="!paymentMode.enabled"
                                        data-ng-click="payViaCoupons(paymentMode.id)"
                                        data-ng-show="paymentMode.id==4 || paymentMode.id==5">Go
                                </button>
                                <!--<button class="btn btn-info col-sm-2" style="margin-left: 5px"
                                        data-ng-disabled="!paymentMode.enabled"
                                        data-ng-click="payViaCard(paymentMode.id)"
                                        data-ng-show="paymentMode.id==10 && !containsGiftCard">Validate
                                </button>-->
                            </div>
                            <div class="form-group"
                                 data-ng-if="!(paymentMode.id==10 && isSpecialOrder) && paymentMode.id == currentPaymentModeId && openOrderSourceIdInput ">
                                <label style="margin-left: 15px" for="{{paymentMode.id}}" class="col-sm-6">
                                    Order Source Id</label>
                                <input type="number" min="0" name="{{paymentMode.id}}"
                                       data-ng-model="billSettlementNo"
                                       data-ng-disabled="!paymentMode.enabled || paymentMode.id==4 || paymentMode.id==5 || paymentMode.id==18"
                                       class="form-control col-sm-2"
                                       data-ng-change="addbillSettlementNoToOrderSourceId(billSettlementNo)"
                                       style="width: 100px;" id="{{paymentMode.id}}" required>
                            </div>
                        </div>

                        <div class="col-xs-6" data-ng-repeat="paymentMode in transactionMetadata.paymentModes"
                             data-ng-if="paymentMode.id!=10 && paymentMode.id!=21 && paymentMode.enabled && paymentMode.id!=4 && paymentMode.id!=5 && paymentMode.id!=18 && !billSetlementType && ((defaultPid !=null && defaultPid==paymentMode.id)|| (checkIdInPaymentModeType(paymentMode.id)))">
                            <div class="form-group" data-ng-if="!(paymentMode.id==10 && isSpecialOrder)">
                                    <label style="margin-left: 15px" class="col-sm-6">
                                        {{paymentMode.description}}</label>
                                    <input type="number" min="0" name="{{paymentMode.id}}"
                                           data-ng-model="payment[paymentMode.id]"
                                           data-ng-disabled="!paymentMode.enabled || paymentMode.id==4 || paymentMode.id==5 || paymentMode.id==18"
                                           data-ng-change="checkForCredit(paymentMode.id)" class="form-control col-sm-2"
                                           style="width: 100px;" id="{{paymentMode.id}}" required>
                                    <button class="btn btn-info col-sm-2" style="margin-left: 5px"
                                            data-ng-disabled="!paymentMode.enabled"
                                            data-ng-click="settleAmountDirectly(paymentMode.id,paymentMode.needsSettlementSlip)"
                                            data-ng-show="paymentMode.id!=4 && paymentMode.id!=5 && paymentMode.id!=10 && paymentMode.id!=18">
                                        Go
                                    </button>
                                    <button class="btn btn-info col-sm-2" style="margin-left: 5px"
                                            data-ng-disabled="!paymentMode.enabled"
                                            data-ng-click="payViaCoupons(paymentMode.id)"
                                            data-ng-show="paymentMode.id==4 || paymentMode.id==5">Go
                                    </button>
                                <!--<button class="btn btn-info col-sm-2" style="margin-left: 5px"
                                        data-ng-disabled="!paymentMode.enabled"
                                        data-ng-click="payViaCard(paymentMode.id)"
                                        data-ng-show="paymentMode.id==10 && !containsGiftCard">Validate
                                </button>-->
                            </div>
                            <div class="form-group"
                                 data-ng-if="!(paymentMode.id==10 && isSpecialOrder) && paymentMode.id == currentPaymentModeId && openOrderSourceIdInput ">
                                <label style="margin-left: 15px" for="{{paymentMode.id}}" class="col-sm-6">
                                    Order Source Id :</label>
                                <input type="number" min="0" name="{{paymentMode.id}}"
                                       data-ng-model="billSettlementNo"
                                       data-ng-disabled="!paymentMode.enabled || paymentMode.id==4 || paymentMode.id==5 || paymentMode.id==18 "
                                       class="form-control col-sm-2"
                                       data-ng-change="addbillSettlementNoToOrderSourceId(billSettlementNo)"
                                       style="width: 100px;" id="{{paymentMode.id}}" required>
                            </div>
                        </div>
                        <!--<div class="col-xs-6">
                            <div class="form-group">
                                <label style="margin-left: 15px" class="col-sm-6">Extra Vouchers</label>
                                <input type="number" min="0" data-ng-model="extraVouchers" data-ng-disabled="true"
                                       class="form-control col-sm-2" style="width: 100px;" required>
                            </div>
                        </div>-->
                    </div>
                    <h3 data-ng-if="showSodexoDenoms"
                        style="margin-bottom:20px; pdding-bottom:10px; border-bottom:#ccc 1px solid;">
                        Sodexo Coupon Denominations
                    </h3>

                    <div class="row" data-ng-if="showSodexoDenoms">
                        <div class="col-xs-6" data-ng-repeat="denom in sodexoDenominations"
                             data-ng-if="denom.status='ACTIVE'">
                            <div class="form-group">
                                <label style="margin-left: 15px" class="col-sm-4">Coupons {{denom.denominationValue}}
                                    x </label>
                                <input type="number" min="0" data-ng-model="denom.denominationCount"
                                       data-ng-change="calculateSodexoCoupon()" class="form-control col-sm-4"
                                       style="width: 100px;"
                                       required>
                            </div>
                        </div>
                    </div>

                    <h3 data-ng-if="showTRDenoms"
                        style="margin-bottom:20px; padding-bottom:10px; border-bottom:#ccc 1px solid;">
                        Ticket Restaurant Coupon Denominations
                    </h3>

                    <div class="row" data-ng-if="showTRDenoms">
                        <div class="col-xs-6" data-ng-repeat="denom in trDenominations"
                             data-ng-if="denom.status='ACTIVE'">
                            <div class="form-group">
                                <label style="margin-left: 15px" class="col-sm-4">Coupons {{denom.denominationValue}}
                                    x</label>
                                <input type="number" min="0" data-ng-model="denom.denominationCount"
                                       data-ng-change="calculateTRCoupon()" class="form-control col-sm-4"
                                       style="width: 100px;"
                                       required>
                            </div>
                        </div>
                    </div>

                </div>

                <div data-ng-if="showCardDetails">
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <label class="col-sm-3" style="width: 20%;line-height:30px;">Add New Gift
                                        Card</label>
                                    <input type="text" class="form-control col-sm-2" data-ng-model="giftCard"
                                           style="text-transform: uppercase;width: 145px;margin-right: 5px;" required>
                                    <button data-ng-click="validateCard(giftCard)" class="btn btn-info">Submit</button>
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-12 text-center" data-ng-if="showRefreshOTPbutton()">
                            <otp-model></otp-model>
                        </div>
                        <div class="col-xs-12" data-ng-if="hasOTPText() && !isOtpVerified">
                            <div class="alert text-center"
                                 data-ng-class="{'otp-verifying': getOTPStatus() == 0, 'otp-verified': getOTPStatus() == 1}">
                                {{getOTPText()}}
                            </div>
                        </div>
                        <div class="col-xs-12" data-ng-if="isOtpVerified">
                            <div class="alert text-center otp-verified-already">Customer has already verified OTP in
                                this order.
                            </div>
                        </div>
                        <div class="col-xs-12" style="max-height: 200px; overflow-y: scroll">
                            <div class="alert alert-warning" data-ng-if="showCardError">{{giftCardError}}</div>
                            <div class="row" data-ng-if="giftCards.length>0" style="margin:0 auto;">
                                <div style="border-bottom: #ccc 1px solid;padding: 10px;background: aliceblue;">Applied
                                    Cards:
                                </div>
                                <table class="table table-condensed table-striped">
                                    <thead>
                                    <tr>
                                        <td>Card Number</td>
                                        <td>Actual Value</td>
                                        <td>Available Value</td>
                                        <td>Amount deducted</td>
                                        <td>Balance left</td>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr data-ng-repeat="card in giftCards track by $index">
                                        <td>{{card.maskedNumber}}</td>
                                        <td>{{card.initialValue}}</td>
                                        <td>{{card.currentValue}}</td>
                                        <td>{{card.amount}}</td>
                                        <td>{{card.balance}}</td>
                                    </tr>
                                    </tbody>
                                </table>
                                <!--<div style="display: inline-block;margin-left:5%; font-size: 100%;">Applied Cards:</div>
                                <div data-ng-repeat="card in giftCards track by $index" class="label label-default"
                                     style="margin-right:5px;">
                                    <span style="margin-right:3px;">{{card.giftCard}} - Value:{{card.currentValue}}, Deducted {{card.amount}}, Balance: {{card.balance}}</span>
                                    <span ng-click="removeGiftCard(giftCards,$index)" style="cursor: pointer;">&times;</span>
                                </div>-->
                            </div>
                            <div class="row" style="margin:0 auto;">
                                <div style="border-bottom: #ccc 1px solid;padding: 10px;background: aliceblue;">
                                    Available Self Cards:
                                </div>
                                <table class="table table-condensed table-striped"
                                       data-ng-if="giftCards.length<unusedSelfCards.length">
                                    <thead>
                                    <tr>
                                        <td>Card Number</td>
                                        <td>Actual Value</td>
                                        <td>Available Value</td>
                                        <td>Card Type</td>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr data-ng-repeat="card in unusedSelfCards track by $index"
                                        data-ng-if="!card.applied">
                                        <td>{{card.maskedNumber}}</td>
                                        <td>{{card.initialValue}}</td>
                                        <td>{{card.currentValue}}</td>
                                        <td>{{card.cardType}}<i class="fa fa-trash-o removeSelfCard" aria-hidden="true"
                                                                data-ng-click="removeFromSelfCards(card)"
                                                                data-ng-if="card.cardType=='GIFT'"></i></td>
                                    </tr>
                                    </tbody>
                                </table>
                                <p data-ng-if="giftCards.length>=unusedGiftCards.length">No self cards available.</p>
                            </div>
                            <div class="row" style="margin:0 auto;">
                                <div style="border-bottom: #ccc 1px solid;padding: 10px;background: aliceblue;">
                                    Available Gift Cards:
                                </div>
                                <table class="table table-condensed table-striped"
                                       data-ng-if="unusedGiftCards.length>0">
                                    <thead>
                                    <tr>
                                        <td>Card Number</td>
                                        <td>Actual Value</td>
                                        <td>Available Value</td>
                                        <td>Add To Self</td>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <tr data-ng-repeat="card in unusedGiftCards track by $index"
                                        data-ng-if="!card.applied">
                                        <td>{{card.maskedNumber}}</td>
                                        <td>{{card.initialValue}}</td>
                                        <td>{{card.currentValue}}</td>
                                        <td>
                                            <button class="btn btn-sm btn-warning"
                                                    data-ng-click="convertToSelfCards(card)">Add
                                            </button>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                                <p data-ng-if="unusedGiftCards.length < 1 ">No gift cards available.</p>
                            </div>
                        </div>
                    </div>

                    <button class="btn btn-primary" data-ng-click="applyGiftCards()">Go</button>

                </div>

            </div>
            <div data-ng-if="isTableOrder && !isTableCheckout ">
                <ul class="nav nav-tabs nav-justified" style="margin-bottom: 20px;">
                    <li role="presentation" data-ng-class="{'active':!showCardDetails}"
                        data-ng-click="showCardDetails=false"><a>Make Payment</a></li>
                </ul>
                <div class="row">
                    <div class="col-xs-6" data-ng-repeat="paymentMode in transactionMetadata.paymentModes"
                         data-ng-if="paymentMode.id==21">
                        <div class="form-group">
                            <label style="margin-left: 15px" for="{{paymentMode.id}}" class="col-sm-6">
                                {{paymentMode.description}}</label>
                            <input type="number" min="0" name="{{paymentMode.id}}"
                                   data-ng-model="payment[paymentMode.id]"
                                   data-ng-change="checkForCredit(paymentMode.id)" class="form-control col-sm-2"
                                   style="width: 100px;" id="{{paymentMode.id}}" required>
                            <button class="btn btn-info col-sm-2" style="margin-left: 5px"
                                    data-ng-click="settleAmountDirectly(paymentMode.id)">Go
                            </button>
                        </div>
                    </div>
                    <div class="col-xs-6">
                        <div class="form-group">
                            <label style="margin-left: 15px" class="col-sm-6">Extra Vouchers</label>
                            <input type="number" min="0" data-ng-model="extraVouchers" data-ng-disabled="true"
                                   class="form-control col-sm-2" style="width: 100px;" required>
                        </div>
                    </div>
                </div>
            </div>

            <div data-ng-if="showNameField" style="float:right;display:inline-block;">
                Credit Account Name: &nbsp;
                <select name="CSname" class="form-control col-sm-2"
                        placeholder="Customer's Name" style="margin-bottom: 10px; text-align: center;"
                        data-ng-model="csName" data-ng-options="account as account.name for account in creditAccounts"
                        data-ng-change="cName(csName)" required></select>
            </div>
            <div class="clearfix"></div>

        </form>
    </div>
</div>
<div class="modal-footer">

    <div class="btn-group" data-ng-if="paymentFormPOS">
        <div data-ng-show="showNameField">
            <button class="btn btn-danger" data-ng-click="cancelSettlement()">Cancel</button>
            <button class="btn btn-default" data-ng-disabled="!form.settlementForm.CSname.$valid"
                    data-ng-click="submit()">Submit
            </button>
        </div>
        <div data-ng-show="!showNameField">
            <button class="btn btn-danger" data-ng-click="cancelSettlement()">Cancel</button>
            <button class="btn btn-default" data-ng-click="submit()">Submit</button>
        </div>
    </div>
    <div class="btn-group" data-ng-if="!paymentFormPOS">
        <div data-ng-show="showNameField">
            <button class="btn btn-danger" data-ng-if="!paymentDone" data-ng-click="cancelSettlement()">Cancel</button>
            <button class="btn btn-default" data-ng-if="paymentDone" data-ng-disabled="!form.settlementForm.CSname.$valid"
                    data-ng-click="submit()">Submit
            </button>
        </div>
        <div data-ng-show="!showNameField">
            <button class="btn btn-danger" data-ng-if="!paymentDone" data-ng-click="cancelSettlement()">Cancel</button>
            <button class="btn btn-default" data-ng-if="paymentDone" data-ng-click="submit()">Submit</button>
        </div>
    </div>
    <button data-ng-show="manualBillBookNo" data-ng-click="validateManualBillBookNo(manualBillBookNo)"
            style="float:left" class="btn btn-warning">Verify Manual Bill
    </button>
</div>
