<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="modal-header" data-ng-init="init()">
	<div flash-message="5000"></div>
	<h3 class="text-center">Assign SDP</h3>
</div>
<div class="modal-body">
	<div class="row">
		<div class="col-xs-12">
			<button ng-repeat="data in employeeDetails track by $index"
				class="btn btn-md"
				ng-class="{riderButton: !data.selected, riderSelectedButton: data.selected}"
				type="button" ng-click="selectedRider($index, employeeDetails,data)">
				{{data.name}}<br /> ({{data.id}})
			</button>
		</div>
	</div>
	<div class="row">
		<div class="col-xs-12">
			<span  class="btn btn-primary pull-center" ng-click="submit()" data-ng-disabled="isSubmitDisabled">Submit</span>
			<span class="btn btn-primary pull-center" ng-click="close()">Cancel</span>
		</div>
	</div>
</div>
