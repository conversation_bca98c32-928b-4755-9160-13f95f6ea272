<style>
.heading-tab{
    color: red;
    font-size: 25px;
    font-weight: 600;
}
</style>
<div class="modal-header" data-ng-init="init()">
	<h3 class="text-center"> Order Detail</h3>
</div>
<div class="modal-body">
	<div class="row">
		<div class="col-xs-12">
			<span class="heading-tab">Customer Details</span>
		</div>
	</div>
	<div class="row">
		<table class="table table-striped">
			<thead>
				<tr>
					<th>Customer Name</th>
					<th>Contact No</th>
					<th>Address</th>
				</tr>
			</thead>
			<tbody>
				<tr>
					<td>{{orderInfo.customer.firstName}}</td>
					<td>{{orderInfo.customer.contactNumber}}</td>
					<td data-ng-bind-html="getAddress(orderInfo.customer.addresses[0])"></td>
				</tr>
			</tbody>
		</table>
	</div>
	<div class="row" data-ng-if="orderInfo.deliveryDetails.deliveryBoyPhoneNum != null">
		<div class="col-xs-12">
			<span class="heading-tab">SDP Details</span>
		</div>
	</div>
	<div class="row" data-ng-if="orderInfo.deliveryDetails.deliveryBoyPhoneNum != null">
		<table class="table table-striped">
			<thead>
				<tr>
					<th>SDP Name</th>
					<th>SDP No</th>
				</tr>
			</thead>
			<tbody>
				<tr>
					<td>{{orderInfo.deliveryDetails.deliveryBoyName}}</td>
					<td>{{orderInfo.deliveryDetails.deliveryBoyPhoneNum}}</td>
				</tr>
			</tbody>
		</table>
	</div>
	<div class="row">
		<div class="col-xs-12">
			<span class="heading-tab" >Order Status Details</span>
		</div>
	</div>
<div class="row">
		<table class="table table-striped">

			<thead style="background-color: #FFFFFF">
				<tr>
					<th>From Status</th>
					<th>To Status</th>
					<th>Elapsed Time(mins)</th>
					<th>Reason</th>
					<th>Status</th>
				</tr>
			</thead>
			<tbody>
				<tr ng-repeat="transition in orderTransition">

					<td>{{transition.fromStatus}}</td>
					<td>{{transition.toStatus}}</td>
					<td>{{transition.elapsedTime}}</td>
					<td>{{transition.reasonText}}</td>
					<td>{{transition.transitionStatus}}</td>
				</tr>
			</tbody>
		</table>
	</div>
</div>
<div class="modal-footer">
	<button type="button" class="btn btn-danger"
		data-ng-click="closeModal()">Close</button>
</div>
