<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Title</title>
    <script src="scripts/services/AppUtil.js"></script>
    <style>
        .tableHeading{
            font-size: 20px;
            font-weight: bold;
        }
        .seperator{
            height: 30px;
        }
        #divider{
            height: 3px;
            width: 100%;
            background-color: #c0a16b;
        }
    </style>
</head>
<body >

        <h3>&nbsp;&nbsp;&nbsp;Please Enter Expected Price for Current Day Part</h3>
        <div id="divider"/>
        <div style="width: 800px; margin-left: 30px">
        <table style="width: 600px; height: 150px">
            <tr>
                <th class="tableHeading"><b>Product Name</b></th>
                <th class="tableHeading"><b>Current Price</b></th>
                <th class="tableHeading"><b>Expected Price</b></th>
            </tr>
            <div class="seperator" />
            <tr>
                <th>{{product1.productName}}</th>
                <th>{{product1.currentPrice}}</th>
                <th><input type="text" data-ng-model="product1.enteredExpectedPrice"></th>
            </tr>
            <tr>
                <th>{{product2.productName}}</th>
                <th>{{product2.currentPrice}}</th>
                <th><input type="text" data-ng-model="product2.enteredExpectedPrice"></th>
            </tr>
            <tr>
                <th></th>
                <th></th>
                <th>
                    <button class="btn btn-primary pull-right" type="submit" name="submit" data-ng-click="checkExpectedPrice()" >Submit</button>
                </th>
            </tr>
        </table>

        <div class="seperator" />
    </div>
</body>
</html>