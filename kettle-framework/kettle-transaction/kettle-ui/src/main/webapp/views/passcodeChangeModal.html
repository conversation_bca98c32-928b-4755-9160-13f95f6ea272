<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="modal-header">
    <div flash-message="5000" ></div>
  <h3 class="text-center">Change Passcode</h3>
</div>

<div class="modal-body">
  <form novalidate role="form" name="passcodeChangeForm">

    <div class="form-group">
      <label for="username">User iD</label>
      <input type="number" name="username" id="username" class="input-form-control"
             ng-minlength="6" ng-maxlength="6"
             style="width:200px; margin-left: 21px"
             ng-model-options="{ updateOn: 'blur' }"
             ng-model="usercode" required/>
    </div>

    <div ng-messages="passcodeChangeForm.username.$error" style="color:maroon" role="alert">
      <div ng-if="passcodeChangeForm.username.$touched">
        <div ng-message="required">You did not enter user code</div>
        <div ng-message="minlength">user id min length is 6</div>
        <div ng-message="maxlength">user id max length is 6</div>
      </div>
    </div>

    <div class="form-group">
      <label for="unitId">Unit iD</label>
      <input type="number" name="unitId"
             id="unitId" class="input-form-control"
             style="width:200px; margin-left: 21px"
             ng-minlength="5" ng-maxlength="5"
             ng-model-options="{ updateOn: 'blur' }"
             ng-model="unitId" required/>
    </div>

    <div ng-messages="passcodeChangeForm.unitId.$error" style="color:maroon" role="alert">
      <div ng-if="passcodeChangeForm.unitId.$touched">
        <div ng-message="required">You did not enter unit id</div>
        <div ng-message="minlength">unit id min length is 5</div>
        <div ng-message="maxlength">unit id min length is 5</div>
      </div>
    </div>

    <div class="form-group">
      <label for="oldPassword">Old Passcode</label>
      <input type="password" name="password" id="oldPassword"
             class="input-form-control"
             style="width:200px;"
             ng-model-options="{ updateOn: 'blur' }"
             ng-model="oldPassword" required/>
    </div>

    <div ng-messages="passcodeChangeForm.oldPassword.$error" style="color:maroon" role="alert">
      <div ng-if="passcodeChangeForm.oldPassword.$touched">
        <div ng-message="required">You did not enter password field</div>
      </div>
    </div>

    <div class="form-group">
      <label for="newPassword">New Passcode</label>
      <input type="password" name="password" id="newPassword"
             style="width:200px;"
             class="input-form-control"
             ng-model-options="{ updateOn: 'blur' }"
             ng-model="newPassword" required/>
      <!--<span ng-show="passcodeChangeForm.newPassword.$dirty && passcodeChangeForm.newPassword.$error.required" class="help-block">PassCode is required</span>-->
    </div>


    <div ng-messages="passcodeChangeForm.newPassword.$error" style="color:maroon" role="alert">
      <div ng-if="passcodeChangeForm.newPassword.$touched">
        <div ng-message="required">You did not enter password field</div>
      </div>
    </div>

  </form>
</div>


<div class="modal-footer">

  <div class="btn-group">
    <a href="#" class="btn btn-danger" ng-click="cancelReset()">Cancel</a>
    <a href="#" class="btn btn-default" ng-click="PasscodeReset()"
       ng-disabled="!passcodeChangeForm.$valid">Submit</a>
  </div>

 <!-- <button type="button" class="btn btn-danger"
          style="margin: 15px" ng-click="cancelReset()" > Cancel
  </button>

  <button type="submit" class="btn btn-info"
          style="margin: 15px" ng-click="PasscodeReset()"
          ng-disabled="!passcodeChangeForm.$valid"> Submit
  </button>-->
</div>
