<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="modal-header" data-ng-init="init()">
    <div data-flash-message="5000" ></div>
    <h3 class="pull-left" >{{outletName}}</h3>
    <h2 class="pull-right" style="color: #FF9933">Rs {{transactionObj.paidAmount}}
      <span style="color: #e0e8e0">|</span>
      <span style="color: #138808">Rs {{settlementRemaining()}}</span> </h2>
    <div class="clearfix"></div>
    <h4 class="text-center">Name: <span style="color: #00b3ee">{{CSObj.firstName}}</span>
            Contact No: <span style="color: #00b3ee">{{CSObj.contactNumber}}</span>
            Address: <span style="color: #00b3ee">{{addressString}}</span></h4>
</div>

<div class="modal-body">
	<div class="container-fluid">

		<tabset active="active">
			<tab index="0" heading="Make Payment Options">
				<form novalidate name="form.settlementForm" class="form-horizontal" style="margin-top:10px;">
					<div class="row">
						<div class="col-xs-6">
							<div class="form-group">
								<label for="channelPartner" class="control-label col-xs-4">Channel Partner</label>
								<div class="col-xs-6">
									<select class="form-control col-xs-6" id="channelPartner" name="channelPartner"
											data-ng-model="channelPartner"
											data-ng-options="channel as channel.name for channel in transactionMetadata.channelPartner | filter: channelFilter"
											data-ng-change="changeCreditAccount(channelPartner)"
											required data-ng-disabled="isChannelPartnerOrder">
									</select>
								</div>
							</div>
							<div class="form-group" data-ng-if="channelPartner.creditAccount != null">
								<label class="control-label col-xs-4">Fill Channel Partner OrderID:</label>
								<div class="col-xs-6">
									<input type="text" data-ng-model="channelPartnerOrder" data-ng-change="enterExternOrderId(channelPartnerOrder)"/>
								</div>
							</div>
						</div>
						<div class="col-xs-6">
							<div class="form-group" data-ng-show="unitFamily == 'COD' && orderType!='subscription'">
								<label for="deliveryPartner"  class="control-label col-xs-6">Delivery Partner</label>
								<div class="col-xs-6">
									<select class="form-control col-xs-6" id="deliveryPartner" name="deliveryPartner"
											data-ng-model="deliveryPartner"
											data-ng-options="partner.id as partner.name for partner in deliveryPartners"
											data-ng-change="selectDeliveryPartner(deliveryPartner)" required data-ng-disabled="channelPartner.id==6">
									</select>
								</div>
							</div>
							<div class="form-group" data-ng-if="channelPartner.creditAccount != null && channelPartner.id==6">
								<label class="control-label col-xs-4">Fill Channel Partner CustomerID:</label>
								<div class="col-xs-6">
									<input type="text" minlength="4" data-ng-model="channelPartnerCustomer" data-ng-change="enterExternCustomerId(channelPartnerCustomer)"/>
								</div>
							</div>
							<div class="form-group" data-ng-if="orderType=='subscription'">
								<label for="deliveryPartner" class="control-label col-xs-5">Payment Mode</label>
								<div class="col-xs-7">
									<a href="#" class="btn" data-ng-repeat="paymentMode in transactionMetadata.paymentModes"
									   data-ng-class="{'btn-success':payment[paymentMode.id]>0, 'btn-info':(payment[paymentMode.id]>0)===false}"
									   data-ng-if="orderType=='subscription' && (paymentMode.type=='CASH' || paymentMode.type=='CREDIT')"
									   data-ng-click="settleAmountDirectly(paymentMode.id)" style="margin-right:5px;">{{paymentMode.description}}</a>
								</div>
							</div>
						</div>
					</div>

					<div class="row" data-ng-if="orderType!='subscription'" style="border-top:#ccc 1px solid; padding-top: 15px;">
						<div class="col-xs-6" data-ng-repeat="paymentMode in transactionMetadata.paymentModes">
							<div class="form-group" data-ng-if="paymentMode.id != 10">
								<label for="{{paymentMode.id}}" class="control-label col-xs-6">{{paymentMode.description}}</label>
								<div class="col-xs-6">
									<input type="number" min="0" name="{{paymentMode.id}}" data-ng-model="payment[paymentMode.id]" class="form-control"
										   style="width:97px; float:left;" id="{{paymentMode.id}}" data-ng-disabled="!paymentMode.enabled || !(paymentMode.id==1 || paymentMode.id==2 || paymentMode.id==3 || paymentMode.id==6 || paymentMode.id==11)" required>
									<button class="btn" data-ng-if="(paymentMode.id==1 || paymentMode.id==2 || paymentMode.id==3 || paymentMode.id==6 || paymentMode.id==11)" data-ng-class="{'btn-success':payment[paymentMode.id]>0, 'btn-info':(payment[paymentMode.id]>0)===false}"
											style="margin-left: 5px; width: 50px;" data-ng-click="settleAmountDirectly(paymentMode.id)" data-ng-disabled = "!paymentMode.enabled">Go</button>
								</div>
							</div>
						</div>
					</div>

					<div data-ng-if="showNameField" style="float:right;display:inline-block;">
						Credit Account Name: &nbsp;
						<select name="CSname"  class="form-control col-sm-2"
								placeholder="Customer's Name" style="margin-bottom: 10px; text-align: center;"
								data-ng-model="csName" data-ng-options="account as account.name for account in creditAccounts"
								data-ng-change="cName(csName)" required data-ng-disabled="isChannelPartnerOrder"></select>
					</div>
					<div class="clearfix"></div>

					<div class="row" data-ng-if="orderType=='subscription'" style="border-top:#ccc 1px solid;">
						<div class="col-xs-12">
							<div class="row" style="margin-top: 30px;">
								<div class="col-xs-3">
									<div class="form-group">
										<!-- <label for="subscriptionFrequency" class="control-label col-xs-4">Select interval type:</label> -->
										<div class="col-xs-12">
											<div class="btn-group">
												<a href="javascript:;" class="btn" data-ng-click="getSubscriptionFrequency('WEEKLY')"
												   data-ng-class="{'btn-success':subscriptionFrequencyType=='WEEKLY','btn-info':subscriptionFrequencyType!='WEEKLY'}">WEEKLY</a>
												<a href="javascript:;" class="btn" data-ng-click="getSubscriptionFrequency('MONTHLY')"
												   data-ng-class="{'btn-success':subscriptionFrequencyType=='MONTHLY','btn-info':subscriptionFrequencyType!='MONTHLY'}">MONTHLY</a>
											</div>
										</div>
									</div>
								</div>
								<div class="col-xs-9">
									<div class="row">
										<div class="col-xs-6">
											<div class="form-group">
												<label class="control-label col-xs-5">Start date:</label>
												<div class="col-xs-7">
													<div class="datepicker" data-date-format="yyyy-MM-dd" data-date-min-limit="{{subscriptionMinDate}}">
														<input type="text" data-ng-model="subscriptionStartDate" class="form-control"
															   data-ng-change="setSubscriptionStartDate(subscriptionStartDate)" placeholder="yyyy-mm-dd" />
													</div>
												</div>
											</div>
										</div>
										<div class="col-xs-6">
											<div class="form-group">
												<label class="control-label col-xs-5">End date:</label>
												<div class="col-xs-7">
													<div class="datepicker" data-date-format="yyyy-MM-dd" data-date-min-limit="{{subscriptionMinDate}}">
														<input type="text" data-ng-model="subscriptionEndDate" class="form-control"
															   data-ng-change="setSubscriptionEndDate(subscriptionEndDate)" placeholder="yyyy-mm-dd" />
													</div>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>

							<div class="row" style="margin:20px 0 30px 0;">
								<div class="col-xs-4">
									<div class="checkbox">
										<label>
											<input type="checkbox" data-ng-model="emailNotification" data-ng-change="notifyEmail(emailNotification)"> Email Notification
										</label>
									</div>
								</div>
								<div class="col-xs-4">
									<div class="checkbox">
										<label>
											<input type="checkbox" data-ng-model="smsNotification" data-ng-change="notifySMS(smsNotification)"> SMS Notification
										</label>
									</div>
								</div>
								<div class="col-xs-4">
									<div class="checkbox">
										<label>
											<input type="checkbox" data-ng-model="automatedDelivery" data-ng-change="automateDelivery(automatedDelivery)"> Automated Delivery
										</label>
									</div>
								</div>
							</div>

							<div class="row">
								<div class="col-xs-6">
									<div class="form-group">
										<label class="control-label col-xs-4">Select day(s):</label>
										<div class="col-xs-6">
											<select class="form-control" data-ng-model="frequencyDay"
													data-ng-options="frequencyDay as frequencyDay.value for frequencyDay in frequencyDays track by frequencyDay.id"></select>
										</div>
										<div class="col-xs-2">
											<button class="btn btn-success" data-ng-click="addFrequencyDay(frequencyDay)"><i class="fa fa-plus-circle"></i></button>
										</div>
									</div>
									<div class="form-group" data-ng-if="subscriptionFrequencyType=='WEEKLY'">
										<div class="col-sm-offset-2 col-sm-10">
											<div class="checkbox">
												<label>
													<input type="checkbox" data-ng-model="allDays" data-ng-change="addAllDays(allDays)"> Add all
												</label>
											</div>
										</div>
									</div>
									<div class="row">
										<div class="col-xs-12 removableTagContainer">
											<div class="removableTag" data-ng-repeat="subscriptionDay in subscriptionDays track by subscriptionDay.id"
												 data-ng-click="removeSubscriptionDay(subscriptionDay)">
												{{subscriptionDay.value}}
												<span><i class="fa fa-close"></i></span>
											</div>
										</div>
									</div>
								</div>
								<div class="col-xs-6">
									<div class="form-group">
										<label class="control-label col-xs-4">Select time(s):</label>
										<div class="col-xs-6">
											<select data-ng-model="frequencyTime" class="form-control"
													data-ng-options="frequencyTime as frequencyTime.value for frequencyTime in frequencyTimes track by frequencyTime.id"></select>
										</div>
										<div class="col-xs-2">
											<button class="btn btn-success" data-ng-click="addFrequencyTime(frequencyTime)"><i class="fa fa-plus-circle"></i></button>
										</div>
									</div>
									<div class="row">
										<div class="col-xs-12 removableTagContainer">
											<div class="removableTag" data-ng-repeat="subscriptionTime in subscriptionTimes track by subscriptionTime.id"
												 data-ng-click="removeSubscriptionTime(subscriptionTime)">
												{{subscriptionTime.value}}
												<span><i class="fa fa-close"></i></span>
											</div>
										</div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</form>
				<div class="btn-group col-xs-offset-10" style="margin-bottom: 10px">
					<a href="#" class="btn btn-danger" data-ng-click="cancelSettlement()">Cancel</a>
					<a href="#" class="btn btn-default" data-ng-click="submit()">Submit</a>
				</div>
			</tab>

			<tab index="1" heading="Associate Payment" select="alertMe()">
				<form novalidate name="form.associatePayment" class="form-horizontal"
					  style="margin-top:10px;text-align: center">
					<div class="row">
						<div class="col-xs-5">
							<label class="pull-right">Find Payment Details:</label>
						</div>
						<div class="col-xs-7">
							<div class="form-group row pull-left">
								<div class="col-xs-12 center-block">
									<button class="btn btn-primary"
											data-ng-click="associatedPayment(CSObj.contactNumber)">Search Payments</button>
								</div>
							</div>
						</div>
					</div>
					<div class="row" data-ng-if="paymentDetail!=null">
						<h4>Payment Details:</h4>
						<div class="center-block">
							<div>Transaction Amount: {{paymentDetail.transactionAmount}}</div>
							<div>Associated OrderID: {{paymentDetail.externalOrderId}}</div>
							<div>Customer Name: {{paymentDetail.customerName}}</div>
							<div>Contact Number: {{paymentDetail.contactNumber}}</div>
							<button class="btn btn-primary" data-ng-if="paymentDetail.transactionAmount==transactionObj.paidAmount"
									data-ng-click="associateAndCreate(paymentDetail)">Create And Associate Order</button>
							<div class="alert alert-warning"
								 data-ng-if="(paymentDetail.transactionAmount-transactionObj.paidAmount)>=4">Amounts don't match</div>
						</div>
						<a href="#" class="btn btn-danger pull-right" data-ng-click="cancelSettlement()">Cancel</a>
					</div>
					<div class="alert alert-warning" data-ng-show="notFound">No payments found to attach</div>
				</form>
			</tab>
		</tabset>
	</div>
</div>

