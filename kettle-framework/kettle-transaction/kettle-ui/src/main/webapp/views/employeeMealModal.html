<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="modal-header" data-ng-init="init()">
	<h3 style="margin: 0;">Select Employee</h3>
</div>

<div class="modal-body">
	<div class="container-fluid">
		<div class="row">
			<div class="col-xs-12">
				<select class="form-control" id="employeeData" name="employeeData"
					data-ng-model="selectedEmployee" ng-disabled="otherEmployeeIdFlag"
					data-ng-options="data as data.name for data in employeeDetails">
				</select>
			</div>
		</div>
		<div class="row">
			<div class="col-xs-3" style="margin-top: 10px;">
				<label> <input type="checkbox" id="otherEmployeeIdFlagId"
					ng-model="otherEmployeeIdFlag"> Any Other
				</label>
			</div>
			<div data-ng-show="otherEmployeeIdFlag" class="col-xs-9"
				style="margin-top: 10px;">
				<input maxlength="7" data-ng-model="otherEmployeeId" onkeypress='return event.charCode >= 48 && event.charCode <= 57'>
			</div>
		</div>
		<div class="row">
			<div class="col-xs-6" style="margin-top: 10px;">
				<button class='btn btn-default btn-lg' type="button"
					ng-click="setEmployeeDetail()">Submit</button>
			</div>
			<div class="col-xs-6" style="margin-top: 10px;">
				<button class='btn btn-default btn-lg pull-right' type="button"
					ng-click="goBack()">Cancel order</button>
			</div>
		</div>
	</div>
	<div class="modal-footer"></div>