<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<style>
    .modal-backdrop.in {
        opacity: 1.0;
    }

    .giftItemlist {
        height: 125px;
        margin: 3px;
        max-height: 200px;
        padding: 2px;
        border: 5px solid green;
    }

    .giftOrderItemBox {
        background-color: #eee;
        margin-top: 5px;
        margin-left: 0px;
        border: 3px solid;
        border-color: #88350f;
    }

    .giftOrderList {
        margin-top: 15px;
    }

    .giftOrderItemsDisplay {
        overflow: auto;
        overflow-x: hidden;
    }

    .giftModalTitle {
        color: red;
        font-size: xx-large;
        font-weight: 600;
    }

    .giftOrderText {
        margin-top: 2px;
        font-family: <PERSON><PERSON>, <PERSON>erdana, serif;
        font-weight: bold;
        font-size: 15px;
        text-align: right;
        color: #3B83BD;
        max-height: 50px;
        background-color:
    }

    .giftCardError {
        color: #cf2d1d;
        font-size: 15px;
        clear: both;
        text-align: right;
        font-weight: 700;
    }
    .table-striped>tbody>tr:nth-of-type(odd) {
        background-color: #f9f9f9;
    }

    .row-spacing {
        margin-top: 10px;
    }
</style>

<div class="modal-header">
    <div data-flash-message="5000"></div>

    <div class="clearfix">
        <h3 class="modal-title giftModalTitle">Inventory</h3>
        <button class="btn btn-danger pull-right pull-top" data-ng-click="closeModal()"> X </button>
    </div>

</div>

<div class="modal-body" data-ng-init="init()">
    <table class="table table-bordered table-striped">
        <tbody>
        <tr style="text-align: center; margin: 10px;">
            <th>Product Id</th>
            <th>Product Name</th>
            <th>Quantity Available</th>
            <th>Expire Quantity</th>
            <th>Last Updated Time</th>
        </tr>
        <tr data-ng-repeat="item in inventoryData.data">
            <td>{{item.productId}}</td>
            <td>{{item.productName}}</td>
            <td>{{item.quantity}}</td>
            <td>{{item.expireQuantity}}</td>
            <td>{{item.lastUpdatedTime}}</td>
        </tr>
        </tbody>
    </table>
</div>


<div class="modal-footer row-spacing">
    <button class="btn btn-danger pull-left" data-ng-click="closeModal()">Cancel</button>
</div>