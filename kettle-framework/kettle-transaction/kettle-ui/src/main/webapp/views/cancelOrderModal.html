<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="modal-header" data-ng-init="initCancellation()">
	<div flash-message="5000"></div>
	<h3 class="text-center">Order Cancel Details</h3>
</div>

<div class="modal-body">
	<form novalidate role="form" name="orderCancelForm" autocomplete="off">
		<div class="row" style="margin: 15px;">
			<div class="col-xs-3">
				<label for="username">User Id</label>
			</div>
			<div class="col-xs-9">
				<input type="number" name="username" id="username" ng-minlength="6"
					ng-maxlength="6" ng-model="userId" autocomplete="off" required />
				<div ng-messages="orderCancelForm.username.$error"
					style="color: maroon" role="alert">
					<div ng-if="LoginModalForm.username.$touched">
						<div ng-message="required">You did not enter user id</div>
						<div ng-message="minlength">user id min length is 6</div>
						<div ng-message="maxlength">user id max length is 6</div>
					</div>
				</div>
			</div>
		</div>
		<div class="row" style="margin: 15px;">
			<div class="col-xs-3">
				<label for="Password">Passcode</label>

			</div>
			<div class="col-xs-9">
				<input type="password" name="password" id="Password" ng-model="pwd"
					autocomplete="off" required />

				<div ng-messages="orderCancelForm.Password.$error"
					style="color: maroon" role="alert">
					<div ng-if="LoginModalForm.Password.$touched">
						<div ng-message="required">You did not enter passcode field</div>
					</div>
				</div>
			</div>
		</div>
		<div class="row" style="margin: 15px;">
			<div class="col-xs-3">
				<label>Cancellation Reason?</label>
			</div>
			<div class="col-xs-9">
				<select style="height: 30px;" id="cancellationReasonId" name="cancellationReasonId"
					data-ng-model="selectedReason"
					data-ng-options="data as data.desc for data in transactionMetadata.cancellationReasons">
				</select> <label style="margin: 15px; color: red;"
					data-ng-if="selectedReason != null && selectedReason.noWastage && !selectedReason.completeWastage">NO
					WASTAGE</label> <label style="margin: 15px; color: red;"
					data-ng-if="selectedReason != null && selectedReason.completeWastage">COMPLETE
					WASTAGE</label>
			</div>
		</div>
		<div class="row" data-ng-if="isOnlineOrder" style="margin: 15px;">
			<div class="col-xs-3">
				<label>Initiate Refund?</label>
			</div>
			<div class="col-xs-9">
				<div>
					<input type="radio" name="refund" data-ng-model="refund"
						value="true" data-ng-change="setRefund(refund)" />&nbsp;<span>Yes</span>
				</div>

				<div>
					<input type="radio" name="refund" data-ng-model="refund"
						value="false" data-ng-change="setRefund(refund)" />&nbsp;<span>No</span>
				</div>

				<div class="alert alert-warning" data-ng-if="showRefundMessage">
					Note: The amount would be refunded within 3-5 working days.</div>
			</div>
		</div>
		<div class="row" style="margin: 15px;">
			<div class="col-xs-3">
				<label for="comment">Reason:</label>
			</div>
			<div class="col-xs-9">
				<textarea style="width: 100%;" rows="5" name="comment" id="comment"
					ng-model="cancelOrderReason" required>
                        </textarea>
				<div ng-messages="orderCancelForm.comment.$error"
					style="color: maroon" role="alert">
					<div ng-if="orderCancelForm.comment.$touched">
						<div ng-message="required">Reason can't be empty field</div>
					</div>
				</div>
			</div>
		</div>
	</form>
</div>

<div class="modal-footer">
	<div class="btn-group">
		<a href="#" class="btn btn-danger" ng-click="cancelOrderCancel()">Cancel</a>
		<a href="#" class="btn btn-default" ng-click="submitOrderCancel()"
			ng-disabled="!orderCancelForm.$valid || dataLoading">Submit</a>
	</div>
</div>
