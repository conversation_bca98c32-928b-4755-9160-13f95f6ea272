<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<style>
.modal-backdrop.in {
	opacity: 1.0;
}

.giftItemlist {
	height: 160px;
	margin: 3px;
	max-height: 200px;
	padding: 15px;
	overflow: auto;
	border: 5px solid green;
}

.giftOrderItemBox {
	background-color: #eee;
	margin-top: 5px;
	margin-left: 0px;
	border: 3px solid;
	border-color: #88350f;
}

.giftOrderList {
	margin-top: 15px;
}

.giftOrderItemsDisplay {
	overflow: auto;
	overflow-x: hidden;
}

.giftModalTitle {
	color: red;
	font-size: xx-large;
	font-weight: 600;
}

.giftOrderText {
	margin-top: 2px;
	font-family: Arial, Verdana, serif;
	font-weight: bold;
	font-size: 15px;
	text-align: right;
	color: #3B83BD;
	max-height: 50px;
	background-color:
}

.giftCardError {
	color: #cf2d1d;
	font-size: 15px;
	clear: both;
	text-align: right;
	font-weight: 700;
}

.row-spacing {
	margin-top: 10px;
}
</style>

<div class="modal-header">
	<div data-flash-message="5000"></div>

	<h3 class="modal-title giftModalTitle">Gift Card</h3>
	<div class="row">
		<div class="col-xs-12">
			<div class="alert text-center otp-verified">Please ask the
				customer to confirm his contact number and remember to take payment
				FIRST.</div>
		</div>
	</div>
	<div class="clearfix"></div>
	<ul class="nav nav-tabs row-spacing">
		<li data-ng-class="{active : cardType == card.code}"
			data-ng-click="changeCardType(card.code)"
			data-ng-repeat="card in cardList"><a href="">{{card.name}}</a></li>
	</ul>
</div>

<div class="modal-body" data-ng-init="init()">
	<div data-ng-if="cardType == 'SELF' || cardType == 'ECARD'">
		<div class="row giftItemlist">
			<div class="col-xs-12 container-fluid  row-fluid"
				data-ng-if="giftCardsList.length > 0">
				<button class="btn btn-lg posProductButton" type="button"
					data-ng-repeat="item in giftCardsList  | orderBy:'prices[0].price'"
				    data-ng-class="{vegButton:item.attribute == 'VEG',nonVegButton:item.attribute == 'NON_VEG'}"
					data-ng-click="addNewProductToOrderItemArray(item)"
					data-ng-disabled="cardType == 'SELF' && item.inventoryTracked && getInventoryForProduct(item.id) <= 0 && !isCOD">
					{{item.name}}
					<span data-ng-if="item.inventoryTracked && cardType == 'SELF'">{{getInventoryForProductFormatted(item.id)}}</span>
					<p style="font-size: 13px; font-weight: bold" ng-if="giftCards[item.prices[0].price]">Extra Rs. {{giftCards[item.prices[0].price]}}</p>
				</button>
				<div class="col-xs-12 container-fluid container-product-display"
					data-ng-show="giftCardsList.length == 0">
					<p class="text-center" style="background-color: #b3d4fc">Sorry,
						no products to display in this subcategory</p>
				</div>
			</div>
		</div>
		<div class="row giftOrderList" data-ng-if="orderItemArray.length>0">
			<div class="col-xs-12">
				<div class="giftOrderItemsDisplay" style="border-top: none;">
					<div class="giftOrderItemBox container-fluid"
						data-ng-class="{orderItemComplimentaryBox:orderItem.orderDetails.hasBeenRedeemed == true}"
						data-ng-repeat="orderItem in orderItemArray.slice().reverse() track by $index"
						close="orderItemArray.splice(index, 1)">

						<!--All In-->

						<div class="row">
							<div class="col-xs-6">
								<div class="row">
									<div class="col-xs-9">
										<p class="giftOrderText"
											style="text-align: left; margin: 10px;">
											{{orderItem.productDetails.name}} <span
												data-ng-if="item.inventoryTracked">{{getInventoryForProductFormatted(orderItem.productDetails.id)}}</span>
										</p>
									</div>
									<div class="col-xs-3">
										<p class="itemPrice"
											ng-show="orderItem.orderDetails.amount != orderItem.orderDetails.totalAmount">{{orderItem.orderDetails.amount.toFixed(2)}}
											({{orderItem.orderDetails.totalAmount.toFixed(2)}})</p>
										<p class="itemPrice"
											ng-show="orderItem.orderDetails.amount == orderItem.orderDetails.totalAmount">{{orderItem.orderDetails.amount.toFixed(2)}}</p>
									</div>
								</div>
							</div>
							<div class="col-xs-4 btn-group" style="padding-left: 20px;"
								role="group"
								data-ng-show="orderItem.orderDetails.hasBeenRedeemed == false && orderItem.orderDetails.isCombo == false">
								<button type="button" class="btn btn-default"
									data-ng-repeat="price in orderItem.productDetails.prices"
									data-ng-click="sizeBtnClicked(orderItem,$index)"
									data-ng-class="{ 'sizeBtnClicked': orderItem.orderDetails.dimension == price.dimension}">
									{{price.dimension.substring(0,1)}}</button>
							</div>
						</div>
						<div class="row"
							data-ng-repeat="data in orderItem.orderDetails.composition.menuProducts track by $index">
							<div class="col-xs-2" style="padding: 0px;">
								<p class="giftOrderText"
									style="text-align: center; margin: 10px;">{{data.name}}</p>
							</div>
							<div class="col-xs-2" style="padding: 2px; text-align: right">
								<button class="btn-md btn-warning"
									data-ng-show="hasRecipeContents(data.item.recipeDetails)"
									data-ng-click="customizeNewModalOpen(data.item)">Customize
								</button>
							</div>

							<div class="col-xs-8">
								<div class="row">
									<div class="col-xs-6">
										<p class="giftOrderText"
											style="text-align: left; margin: 10px;">
											{{data.product.name}} - {{data.dimension.code}}</p>
									</div>
									<div class="col-xs-6">
										<p class="giftOrderText" style="margin: 10px;">
											{{getCustomizationAbb(data.item)}}</p>
									</div>
								</div>
							</div>
						</div>

						<div class="row">
							<div class="col-xs-2" style="padding: 0px;">
								<div class="input-group" style="z-index: 2;">
									<span data-ng-click="decreaseCount(orderItem)"
										data-ng-show="orderItem.orderDetails.hasBeenRedeemed == false && orderItem.productDetails.taxCode !='GIFT_CARD' && orderItem.productDetails.taxCode !='COMBO'"
										class="input-group-addon"
										style="font-weight: bolder; cursor: pointer;">-</span> <input
										type="number" name="quantity" class="form-control"
										style="text-align: center; width: 50px;"
										data-ng-model="orderItem.orderDetails.quantity"
										data-ng-change="calculatePaidAmount()"
										placeholder="orderItem.orderDetails.quantity"
										data-ng-disabled="true"> <span
										data-ng-click="increaseCount(orderItem)"
										data-ng-show="orderItem.orderDetails.hasBeenRedeemed == false && orderItem.productDetails.taxCode !='GIFT_CARD' && orderItem.productDetails.taxCode !='COMBO'"
										class="input-group-addon"
										style="font-weight: bolder; cursor: pointer;">+</span>
								</div>
							</div>
							<div class="col-xs-6">
								<p data-ng-if="hasRecipeContents(orderItem.recipeDetails)"
									class="giftOrderText" style="text-align: center; margin: 10px;">
									{{getCustomizationAbb(orderItem)}}</p>
								<p data-ng-if="orderItem.productDetails.taxCode =='GIFT_CARD'"
									class="giftOrderText" style="text-align: center; margin: 10px;">
									Card Valid:{{orderItem.orderDetails.isCardValid?"YES":"NO"}}</p>
							</div>
							<div class="col-xs-4">
								<div class="row"
									data-ng-show="orderItem.orderDetails.hasBeenRedeemed == false">
									<div class="col-xs-6">
										<button class="btn btn-sm btn-warning"
											data-ng-click="deleteItem($index);deleteCardEntry(cardNumber[orderItem.orderDetails.itemId],orderItem.orderDetails.itemId)">Delete</button>
									</div>
								</div>
								<div class="row">
									<span class="badge badge-info"
										data-ng-if="orderItem.orderDetails.discountDetail.promotionalOffer > 0">
										Discount:
										{{orderItem.orderDetails.discountDetail.promotionalOffer}}</span>
								</div>

							</div>
						</div>
						<div class="row" style="margin-bottom: 5px;"
							data-ng-if="cardType == 'SELF'">
							<div class="col-xs-3">
								<label class="giftOrderText">Card Number</label>
							</div>
							<div class="col-xs-3">
								<input type="text" class="form-control"
									data-ng-disabled="notEditable[orderItem.orderDetails.itemId]"
									style="text-align: center; text-transform: uppercase; font-size: 18px; box-shadow: none;"
									data-ng-model="cardNumber[orderItem.orderDetails.itemId]"
									data-ng-change="updateGiftCardCode(orderItem.orderDetails,cardNumber[orderItem.orderDetails.itemId])"
									maxlength="6">
							</div>
							<!--
							    <div class="col-xs-6" data-ng-if="cardError[orderItem.orderDetails.itemId].length>0">
							 		<span class="giftCardError">{{cardError[orderItem.orderDetails.itemId]}}</span>
								</div>
							-->
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
<div data-ng-if="cardType == 'GYFTR'">
	<div class="row">
		<div class="col-xs-offset-1 col-xs-10">
			<div class="row row-spacing"
				data-ng-repeat="voucher in getVoucherList()">
				<div class="col-xs-8">
					<input type="text" class="form-control" data-ng-model ="vouherNumbers[$index+1]"/>
				</div>
			</div>
			<div class="row row-spacing">
				<div class="col-xs-4">
					<button class="btn btn-primary"
						data-ng-click="updateVoucher('add')">Add More</button>
				</div>
				<div class="col-xs-4" data-ng-if="voucherList.length > 1">
					<button class="btn btn-warning"
						data-ng-click="updateVoucher('remove')" >Remove</button>
				</div>
			</div>
		</div>
	</div>
</div>
    <div data-ng-if="cardType == 'AP01'">
		<div class="row giftItemlist">
			<div class="col-xs-12 container-fluid  row-fluid"
				 data-ng-if="giftCardsListAdv.length > 0">
				<button class="btn btn-lg posProductButton" type="button"
						data-ng-repeat="item in giftCardsListAdv  | orderBy:'prices[0].price'"
						data-ng-class="{vegButton:item.attribute == 'VEG',nonVegButton:item.attribute == 'NON_VEG'}"
						data-ng-click="addNewProductToOrderItemArray(item)"
						data-ng-disabled="cardType == 'SELF' && item.inventoryTracked && getInventoryForProduct(item.id) <= 0 && !isCOD">
					{{item.name}}
					<span data-ng-if="item.inventoryTracked && cardType == 'SELF'">{{getInventoryForProductFormatted(item.id)}}</span>
					<!--<p style="font-size: 13px; font-weight: bold" ng-if="giftCards[item.prices[0].price]">Extra Rs. {{giftCards[item.prices[0].price]}}</p>-->
				</button>
				<div class="col-xs-12 container-fluid container-product-display"
					 data-ng-show="giftCardsListAdv.length == 0">
					<p class="text-center" style="background-color: #b3d4fc">Sorry,
						no products to display in this subcategory</p>
				</div>
			</div>
		</div>
		<div class="row giftOrderList" data-ng-if="orderItemArray.length>0">
			<div class="col-xs-12">
				<div class="giftOrderItemsDisplay" style="border-top: none;">
					<div class="giftOrderItemBox container-fluid"
						 data-ng-class="{orderItemComplimentaryBox:orderItem.orderDetails.hasBeenRedeemed == true}"
						 data-ng-repeat="orderItem in orderItemArray.slice().reverse() track by $index"
						 close="orderItemArray.splice(index, 1)7">

						<!--All In-->

						<div class="row">
							<div class="col-xs-6">
								<div class="row">
									<div class="col-xs-9">
										<p class="giftOrderText"
										   style="text-align: left; margin: 10px;">
											{{orderItem.productDetails.name}} <span
												data-ng-if="item.inventoryTracked">{{getInventoryForProductFormatted(orderItem.productDetails.id)}}</span>
										</p>
									</div>
									<div class="col-xs-3">
										<p class="itemPrice"
										   ng-show="orderItem.orderDetails.amount != orderItem.orderDetails.totalAmount">{{orderItem.orderDetails.amount.toFixed(2)}}
											({{orderItem.orderDetails.totalAmount.toFixed(2)}})</p>
										<p class="itemPrice"
										   ng-show="orderItem.orderDetails.amount == orderItem.orderDetails.totalAmount">{{orderItem.orderDetails.amount.toFixed(2)}}</p>
									</div>
								</div>
							</div>
							<div class="col-xs-4 btn-group" style="padding-left: 20px;"
								 role="group"
								 data-ng-show="orderItem.orderDetails.hasBeenRedeemed == false && orderItem.orderDetails.isCombo == false">
								<button type="button" class="btn btn-default"
										data-ng-repeat="price in orderItem.productDetails.prices"
										data-ng-click="sizeBtnClicked(orderItem,$index)"
										data-ng-class="{ 'sizeBtnClicked': orderItem.orderDetails.dimension == price.dimension}">
									{{price.dimension.substring(0,1)}}</button>
							</div>
						</div>
						<div class="row"
							 data-ng-repeat="data in orderItem.orderDetails.composition.menuProducts track by $index">
							<div class="col-xs-2" style="padding: 0px;">
								<p class="giftOrderText"
								   style="text-align: center; margin: 10px;">{{data.name}}</p>
							</div>
							<div class="col-xs-2" style="padding: 2px; text-align: right">
								<button class="btn-md btn-warning"
										data-ng-show="hasRecipeContents(data.item.recipeDetails)"
										data-ng-click="customizeNewModalOpen(data.item)">Customize
								</button>
							</div>

							<div class="col-xs-8">
								<div class="row">
									<div class="col-xs-6">
										<p class="giftOrderText"
										   style="text-align: left; margin: 10px;">
											{{data.product.name}} - {{data.dimension.code}}</p>
									</div>
									<div class="col-xs-6">
										<p class="giftOrderText" style="margin: 10px;">
											{{getCustomizationAbb(data.item)}}</p>
									</div>
								</div>
							</div>
						</div>

						<div class="row">
							<div class="col-xs-2" style="padding: 0px;">
								<div class="input-group" style="z-index: 2;">
									<span data-ng-click="decreaseCount(orderItem)"
										  data-ng-show="orderItem.orderDetails.hasBeenRedeemed == false && orderItem.productDetails.taxCode !='GIFT_CARD' && orderItem.productDetails.taxCode !='COMBO'"
										  class="input-group-addon"
										  style="font-weight: bolder; cursor: pointer;">-</span> <input
										type="number" name="quantity" class="form-control"
										style="text-align: center; width: 50px;"
										data-ng-model="orderItem.orderDetails.quantity"
										data-ng-change="calculatePaidAmount()"
										placeholder="orderItem.orderDetails.quantity"
										data-ng-disabled="true"> <span
										data-ng-click="increaseCount(orderItem)"
										data-ng-show="orderItem.orderDetails.hasBeenRedeemed == false && orderItem.productDetails.taxCode !='GIFT_CARD' && orderItem.productDetails.taxCode !='COMBO'"
										class="input-group-addon"
										style="font-weight: bolder; cursor: pointer;">+</span>
								</div>
							</div>
							<div class="col-xs-6">
								<p data-ng-if="hasRecipeContents(orderItem.recipeDetails)"
								   class="giftOrderText" style="text-align: center; margin: 10px;">
									{{getCustomizationAbb(orderItem)}}</p>
								<p data-ng-if="orderItem.productDetails.taxCode =='GIFT_CARD'"
								   class="giftOrderText" style="text-align: center; margin: 10px;">
									Card Valid:{{orderItem.orderDetails.isCardValid?"YES":"NO"}}</p>
							</div>
							<div class="col-xs-4">
								<div class="row"
									 data-ng-show="orderItem.orderDetails.hasBeenRedeemed == false">
									<div class="col-xs-6">
										<button class="btn btn-sm btn-warning"
												data-ng-click="deleteItem($index);deleteCardEntry(cardNumber[orderItem.orderDetails.itemId],orderItem.orderDetails.itemId)">Delete</button>
									</div>
								</div>
								<div class="row">
									<span class="badge badge-info"
										  data-ng-if="orderItem.orderDetails.discountDetail.promotionalOffer > 0">
										Discount:
										{{orderItem.orderDetails.discountDetail.promotionalOffer}}</span>
								</div>

							</div>
						</div>
						<div class="row" style="margin-bottom: 5px;"
							 data-ng-if="cardType == 'SELF'">
							<div class="col-xs-3">
								<label class="giftOrderText">Card Number</label>
							</div>
							<div class="col-xs-3">
								<input type="text" class="form-control"
									   data-ng-disabled="notEditable[orderItem.orderDetails.itemId]"
									   style="text-align: center; text-transform: uppercase; font-size: 18px; box-shadow: none;"
									   data-ng-model="cardNumber[orderItem.orderDetails.itemId]"
									   data-ng-change="updateGiftCardCode(orderItem.orderDetails,cardNumber[orderItem.orderDetails.itemId])"
									   maxlength="6">
							</div>
							<!--
							    <div class="col-xs-6" data-ng-if="cardError[orderItem.orderDetails.itemId].length>0">
							 		<span class="giftCardError">{{cardError[orderItem.orderDetails.itemId]}}</span>
								</div>
							-->
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>


<div class="modal-footer row-spacing">
	<button class="btn btn-danger pull-left" data-ng-click="close()">Cancel</button>
	<button class="btn btn-default pull-right"
		data-ng-disabled="orderItemArray.length<1 || disableOrderCheckBtnGift"
		data-ng-click="purchaseGiftCard()" data-ng-if="cardType != 'GYFTR'">Order Check</button>
	<button class="btn btn-default pull-right" data-ng-if="cardType == 'GYFTR'"
		data-ng-click="verifyVoucher()">Verify Voucher</button>
</div>