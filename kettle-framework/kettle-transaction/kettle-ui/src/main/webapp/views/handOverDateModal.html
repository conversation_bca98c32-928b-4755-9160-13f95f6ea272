<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style>
.firstItem {
	margin-left: 5%;
	font-size: 25px;
}

.increaseSize {
	font-size: 20px;
}

.reading-card {
	border: 2px solid red;
	margin: 0px;
	padding: 10px;
	margin-bottom: 8px;
}
</style>
<div class="modal-content">
	<div class="modal-header" data-ng-init="init()">
		<div flash-message="10000"></div>
		<div class="row">
			<div class="col-xs-10">
				<h2 class="text-center" style="margin-top: 20px;">Update
					Handover Date</h2>
			</div>
		</div>
	</div>
	<div class="modal-body">
		<form name="handoverDateForm">
			<div class="row">
				<div class="col-xs-2">
					<label>Handover Date: </label>
				</div>
				<div class="col-xs-10">
					<div class="datepicker" data-date-format="yyyy-MM-dd">
							<input type="text" data-ng-model="handOverD" class="form-control"
									  placeholder="yyyy-mm-dd" />
							</div>
				</div>
			</div>
		</form>
		<div class="modal-footer">
			<button type="button" class="btn btn-danger pull-left"
				ng-click="closeModal()">Close</button>
			<button class="btn btn-warning  pull-right"
				data-ng-click="submithandoOverDate(handOverD)">Submit</button>
		</div>
	</div>