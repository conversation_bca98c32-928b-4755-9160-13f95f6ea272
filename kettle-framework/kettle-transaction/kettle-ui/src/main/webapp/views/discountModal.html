    <!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="modal-header">
        <div flash-message="5000" ></div>
        <h3 class="text-center" >Discount Details</h3>
    </div>

        <div class="modal-body">
            <form novalidate role="form" name="discountForm" data-ng-init="initDiscount()">

                <div class="form-group">
                    <label for="sel1" style="margin: 15px;">Choose reason:</label>
                    <select class="form-control" id="sel1"
                            style="margin-left: 15px; width: 90%;"
                            ng-model="transactionObj.discountDetail.discountCode"
                            ng-options="discount.id as discount.name for discount in transactionMetadata.discountCodes.content" required>
                    </select>
                </div>

                <div class="form-group" style="margin-top: 10px;">
                    <label style="margin-left: 15px" for="prcnt">Discount Percentage:</label>
                    <input type="number" min="0" max="100"class="form-control"
                           style="margin-left: 14px;width:90%;margin-top: 10px;"
                           ng-change="checkForOrConditionsDiscountPercentage()"
                           id="prcnt" ng-model="discountPercentage" required>
                </div>

                <div class="strike">
                    <span>Or</span>
                </div>

                <div class="form-group" style="margin-top: 20px;">
                    <label style="margin-left: 15px" for="value">Discount Value:</label>
                    <input type="number" min="0"class="form-control"
                           style="margin-left: 14px;width:90%;"
                           id="value" ng-change="checkForOrConditionsDiscountValue()" ng-model="discountValue" required>
                </div>

                <div class="form-group">
                    <label for="comment" style="margin-left: 15px">Comment:</label>
                    <textarea class="form-control" rows="3" name="comment" id="comment"
                              style="margin: 15px; width: 240px"
                              ng-model="transactionObj.discountDetail.discountReason" required>
                    </textarea>
                </div>

            </form>
        </div>

    <div class="modal-footer">

      <div class="btn-group">
        <button class="btn btn-danger" ng-click="cancelDiscount()">Cancel</button>
        <button class="btn btn-default" ng-disabled="!discountForm.comment.$valid" ng-click="submit()">Submit</button>
      </div>


    </div>
