<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="modal-header">
	<div data-flash-message="5000"></div>
	<h4 class="text-center">Customization Options</h4>
</div>

<div class="modal-body">
	<form novalidate role="form" name="customizationForm" data-ng-init="initCustomization()">
		<div class="row" style="border: thin solid black;"
			ng-if="!selectedOrderItem.orderDetails.isCombo && selectedOrderItem.recipeDetails != null && selectedOrderItem.recipeDetails.ingredient != null && selectedOrderItem.recipeDetails.ingredient.variants != null && selectedOrderItem.recipeDetails.ingredient.variants.length > 0")>
			<div class="col-xs-12"
				style="text-align: left; font-size: 16px; margin-top: 5px;">
				<label>Select Variants</label>
			</div>
			<div class="col-xs-12 form-group" style="margin-top: 5px;"
				ng-repeat="variant in selectedOrderItem.recipeDetails.ingredient.variants track by $index">
				<div class="row">
					<div class="col-xs-2"
						style="text-align: right; font-size: 14px; margin-top: 8px;">
						<label>{{variant.product.name}}</label>
					</div>
					<div class="col-xs-10">
						<button ng-repeat="data in variant.details track by $index" class="btn btn-md"
							style="margin-right: 5px;"
							ng-class="{variantButton : !data.selected, variantButtonSelected : data.selected}"
							type="button"
							ng-click="selectVariantsData($index, variant.details)">{{data.alias}}</button>
					</div>
				</div>

			</div>
		</div>
		<div class="row" style="border: thin solid black;"
			ng-if="!selectedOrderItem.orderDetails.isCombo && selectedOrderItem.recipeDetails != null && selectedOrderItem.recipeDetails.ingredient != null && selectedOrderItem.recipeDetails.ingredient.products != null && selectedOrderItem.recipeDetails.ingredient.products.length > 0")>
			<div class="col-xs-12"
				style="text-align: left; font-size: 16px; margin-top: 5px;">
				<label>Select Product Variation</label>
			</div>
			<div class="col-xs-12 form-group" style="margin-top: 5px;"
				ng-repeat="product in selectedOrderItem.recipeDetails.ingredient.products  track by $index">
				<div class="row">
					<div class="col-xs-2"
						style="text-align: right; font-size: 14px; margin-top: 8px;">
						<label>{{product.category.name}}</label>
					</div>
					<div class="col-xs-10">
						<button ng-repeat="data in product.details  track by $index" class="btn btn-md"
							style="margin-right: 5px;"
							ng-class="{variantButton: !data.selected, variantButtonSelected: data.selected}"
							type="button"
							ng-click="selectVariantsData($index,product.details)">{{data.product.name}}</button>
					</div>
				</div>

			</div>
		</div>
		<div class="row" style="border: thin solid black;"
			ng-if="!selectedOrderItem.orderDetails.isCombo && selectedOrderItem.recipeDetails != null && selectedOrderItem.recipeDetails.addons != null && selectedOrderItem.recipeDetails.addons.length > 0")>
			<div class="col-xs-12 form-group" style="margin-top: 5px;">
				<div class="row">
					<div class="col-xs-12"
						style="text-align: left; font-size: 16px; margin-top: 5px;">
						<label>Select Addons</label>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-2"
						style="text-align: right; font-size: 14px; margin-top: 8px;">
						<label>Addons</label>
					</div>
					<div class="col-xs-10">
						<button ng-repeat="data in selectedOrderItem.recipeDetails.addons  track by $index"
								ng-show="data.product.productId!=1000127"
								class="btn btn-md" style="margin-right: 5px;"
								ng-class="{variantButton: !data.selected, variantButtonSelected: data.selected}"
								type="button" ng-click="selectedAddOns(data)">{{data.product.name}}</button>

					</div>
				</div>

			</div>
		</div>
		<div class="row" style="border: thin solid black;"
			ng-if="selectedOrderItem.orderDetails.isCombo && selectedOrderItem.recipeDetails != null && selectedOrderItem.recipeDetails.ingredient != null && selectedOrderItem.recipeDetails.ingredient.compositeProduct != null && selectedOrderItem.recipeDetails.ingredient.compositeProduct.details != null && selectedOrderItem.recipeDetails.ingredient.compositeProduct.details.length > 0")>
			<div class="col-xs-12"
				style="text-align: left; font-size: 16px; margin-top: 5px;">
				<label>Select Composition</label>
			</div>
			<div class="col-xs-12 form-group" style="margin-top: 5px;"
				ng-repeat="item in selectedOrderItem.recipeDetails.ingredient.compositeProduct.details  track by $index">
				<div class="row">
					<div class="col-xs-2"
						style="text-align: right; font-size: 14px; margin-top: 8px;">
						<label>{{item.name}}</label>
					</div>
					<div class="col-xs-10">
						<button ng-repeat="data in item.menuProducts | filter : comboProductFilter track by data.product.productId" class="btn btn-md"
							style="margin-right: 5px;"
							data-ng-class="{variantButton: !data.selected, variantButtonSelected: data.selected}"
							type="button"
							ng-click="selectVariantsProductData(data.product.productId,item.menuProducts)">{{data.product.name}}
							<span data-ng-if="data.dimension.name != 'None'">{{data.dimension.name}}</span></button>
					</div>
				</div>

			</div>
		</div>
		<div class="row" style="border: thin solid black;"  data-ng-if="selectedOrderItem.productDetails.taxCode != 'COMBO' && selectedOrderItem.productDetails.taxCode != 'GIFT_CARD'" >
			<div class="col-xs-12 form-group" style="margin-top: 5px;">
				<div class="row">
					<div class="col-xs-12"
						style="text-align: left; font-size: 16px; margin-top: 5px;">
						<label>Select Options</label>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-2"
						style="text-align: right; font-size: 14px; margin-top: 8px;">
						<label>Options</label>
					</div>
					<div class="col-xs-10">
						<button class="btn btn-md" style="margin-right: 5px;"
							ng-class="{variantButton: (selectedOrderItem.orderDetails.takeAway == undefined  || !selectedOrderItem.orderDetails.takeAway), variantButtonSelected: (selectedOrderItem.orderDetails.takeAway != undefined  && selectedOrderItem.orderDetails.takeAway)}"
							type="button" ng-click="selectTakeAway(selectedOrderItem)">Take Away</button>
						<button ng-repeat="data in selectedOrderItem.recipeDetails.options | filter:checkProduct track by $index"
								ng-show="data.productId!=1000127"
								class="btn btn-md" style="margin-right: 5px;"
							ng-class="{variantButton: !data.selected, variantButtonSelected: data.selected}"
							type="button" ng-click="selectedOptions(data)">{{data.name}}</button>
					</div>
				</div>

			</div>
		</div>
	</form>
</div>

<div class="modal-footer" ng-show =" selectedOrderItem.productDetails!=null && selectedOrderItem.productDetails.type==5">
	<div ng-show ="customerObj!= null && customerObj!=undefined && customerObj.id!=null && customerObj.id !=undefined && customerObj.id>5 && !chaiSavedFromDineIn " style ="width:50%;text-align: center">
		<div class="like-btn-div">
			<button class="like-btn" style="margin-right: 20px;" type="button" data-ng-click="checkFavChaiMarked()">
				<i data-ng-show="!favChaiInsideModal"class="fa fa-3x fa-heart-o" style="color:red"></i>
				<i data-ng-show="favChaiInsideModal" class="fa fa-3x fa-heart" style="color:red"></i>
			</button>
			<text data-ng-show ="!favChaiInsideModal" style="font-size: 16px;font-weight:bold">Save This Chai As Meri Wali Chai</text>
			<text data-ng-show ="favChaiInsideModal" style="font-size: 16px;font-weight:bold">Saved As Meri Wali Chai</text>
		</div>
	</div>
	<div style="width:50%; text-align: right;padding-top: 5px">
		<button class="btn btn-danger" data-ng-click="cancelCustomize()">Cancel</button>
		<button class="btn btn-default" data-ng-click="submit()">Submit</button>
	</div>
</div>

<div class="modal-footer" ng-hide="selectedOrderItem.productDetails!=null && selectedOrderItem.productDetails.type==5">
	<div class="btn-group">
		<button class="btn btn-danger" data-ng-click="cancelCustomize()">Cancel</button>
		<button class="btn btn-default" data-ng-click="submit()">Submit</button>
	</div>
</div>
