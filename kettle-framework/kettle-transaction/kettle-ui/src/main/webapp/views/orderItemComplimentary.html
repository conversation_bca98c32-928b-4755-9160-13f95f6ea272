<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="modal-header" data-ng-init="initCustomization()">
	<div data-flash-message="5000"></div>
	<h4 class="text-center">{{selectedOrderItem.productDetails.name}} Complimentary Options</h4>
</div>

<div class="modal-body">
	<form novalidate role="form" name="customizationForm">
		<div class="row">
			<div class="col-xs-12 form-group"
				data-ng-if="orderType!='subscription'">
				<label class="checkbox-inline"> <input type="checkbox"
					value="" id="isComplimentary"
					data-ng-model="complimentaryDetail.isComplimentary">Complimentary
				</label>
			</div>
			</div>
			<div class="row">
			<div class="col-xs-12 form-group"
				data-ng-if="orderType!='subscription'"
				collapse="!complimentaryDetail.isComplimentary">
				<div class="row">
					<div class="col-xs-4 form-group">
						<label for="sel1">Complimentary reason:</label> <select
							class="form-control" id="sel1"
							ng-model="complimentaryDetail.reasonCode"
							ng-change="selectComplimentaryDetail(complimentary)"
							ng-options="complimentary.id as complimentary.name for complimentary in getAllComplimentaryCodes(transactionMetadata.complimentaryCodes.content)"
							required>
						</select>
					</div>
					</div>
					<div class="row">
					<div class="col-xs-12 form-group">
						<label for="comment" style="margin-left: 15px">Comment:</label>
						<textarea class="form-control" rows="3" name="comment"
							id="comment"
							ng-model="complimentaryDetail.reason">
                    </textarea>
					</div>
				</div>
			</div>
		</div>
	</form>
</div>

<div class="modal-footer">

	<div class="btn-group">
		<button class="btn btn-danger" data-ng-click="cancelComplimentary()">Cancel</button>
		<button class="btn btn-default" data-ng-click="submitComplimentary()">Submit</button>
	</div>

	<!--<button type="button" class="btn btn-danger"
          style="margin: 15px" ng-click="cancelCustomize()" > Cancel </button>

  <button type="submit" class="btn btn-info"
          style="margin: 15px" ng-click="submit()" > Submit </button>-->
</div>
