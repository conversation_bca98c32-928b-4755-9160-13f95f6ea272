<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div class="modal-header">
    <div flash-message="5000" ></div>
    <h3 ng-show="isNewAddress" class="text-center" >Customer Address form</h3>
    <h3 ng-show="!isNewAddress" class="text-center" >Select Outlet form</h3>
</div>

<div class="modal-body">
    <form novalidate role="form" name="addressForm">
        <select class="form-control newAddressFields" id="city"
                ng-model="address.city"
                ng-options="city as city for city in cities" required>
        </select>

        <input type="text"
               id="inputDatabaseName"
               ng-model="address.locality"
               placeholder="Locality"
               typeahead="locality for locality in getLocation($viewValue)"
               typeahead-min-length="3"
               typeahead-loading="isLoading"
               typeahead-no-results="noResults"
               ng-keyup=""
               typeahead-on-select="onSelect($item, $model, $label)"
               class="form-control newAddressFields" autocomplete="off">

        <div ng-show="noResults">
            <i class="glyphicon glyphicon-remove"></i> No Results Found
        </div>

		<div class="container-fluid">
		
			<div class="row">
				<span data-ng-if = "outlet.primary_unit_delivery_time > 0" class="badge">{{outlet.primary_unit_delivery_time}}</span>
				<label class="newAddressFields" style="width: 160px;line-height: 1.8em; font-size: 17px;">{{outlet.pri_name}}</label>
		        <button type="submit" class="btn" ng-class="{'btn-default': outlet.selectedId != 1 || outlet.pri_unitId == null, 'btn-info': outlet.selectedId ==1}"
		                style="margin-left: 14px;margin-top: -5px;" ng-click="selectPrimaryOutlet()">
		            <span class="glyphicon glyphicon-ok"></span>
		        </button>
			</div>
	
			<div class="row">
			 <span data-ng-if = "outlet.sec_unit_delivery_time > 0" class="badge">{{outlet.sec_unit_delivery_time}}</span>
			 <label class="newAddressFields" style="width: 160px;line-height: 1.8em;font-size: 17px;">{{outlet.sec_name}}</label>
			
	         <button type="submit" class="btn" ng-class="{'btn-default': outlet.selectedId != 2 || outlet.sec_unitId == null, 'btn-info': outlet.selectedId ==2}"
	                style="margin-left: 14px;margin-top: -5px;" ng-click="selectSecondaryOutlet()">
	            <span class="glyphicon glyphicon-ok"></span>
	         </button>
			</div>
			
			<div class="row">
			 <span data-ng-if = "outlet.ter_unit_delivery_time > 0" class="badge">{{outlet.ter_unit_delivery_time}}</span>
			 <label class="newAddressFields" style="width: 160px;line-height: 1.8em;font-size: 17px;">{{outlet.ter_name}}</label>
			
	         <button type="submit" class="btn" ng-class="{'btn-default': outlet.selectedId != 3 || outlet.ter_unitId == null, 'btn-info': outlet.selectedId ==3}"
	                style="margin-left: 14px;margin-top: -5px;" ng-click="selectTertiaryOutlet()">
	            <span class="glyphicon glyphicon-ok"></span>
	         </button>
			</div>
		</div>
        
        <div ng-show="isNewAddress">
            <hr style="margin-top: 10px;">
            <input type="text"
                   name="line1" id="line1"
                   placeholder="Line 1"
                   class="input-form-control newAddressFields"
                   ng-model="address.line1" autocomplete="off" required/>

            <input type="text"
                   name="line2" id="line2"
                   placeholder="Line 2"
                   class="input-form-control newAddressFields"
                   ng-model="address.line2" autocomplete="off" />

            <input type="text"
                   name="subLocality" id="subLocality"
                   placeholder="Sub Locality"
                   class="input-form-control newAddressFields"
                   ng-model="address.subLocality" autocomplete="off" />


            <select class="form-control newAddressFields" id="state"
                    ng-model="address.state"
                    ng-options="state as state for state in indian_states">
            </select>


            <input type="number"
                   name="zipcode" id="zipcode"
                   placeholder="Zipcode"
                   class="input-form-control newAddressFields"
                   ng-model="address.zipcode" autocomplete="off"/>


            <select class="form-control newAddressFields" id="addressType"
                    ng-model="address.addressType"
                    ng-options="state as state for state in addressType">
            </select>

            <input type="text" id="company" ng-model="address.company" placeholder="Company" class="form-control newAddressFields" autocomplete="off">

        </div>

        <div class="checkbox">
            <label>
                <input type="checkbox" ng-model="address.preferredAddress"> Preferred address
            </label>
        </div>
    </form>
</div>

<div class="modal-footer">
    <div class="btn-group">
        <a href="#" class="btn btn-danger" ng-click="cancel()">Cancel</a>
        <a href="#" class="btn btn-default" ng-click="selectOutlet()" ng-disabled="addressForm.line1.$invalid">Submit</a>
    </div>
</div>

<script>
    function maxLengthCheck(object) {
        if (object.value.length > object.maxLength)
            object.value = object.value.slice(0, object.maxLength)
    }
</script>



