!function(e){function n(r){if(t[r])return t[r].exports;var a=t[r]={exports:{},id:r,loaded:!1};return e[r].call(a.exports,a,a.exports,n),a.loaded=!0,a.exports}var t={};n.m=e,n.c=t,n.p="",n(0)}([function(e,n,t){"use strict";t(1),t(2),t(13),t(14)},function(e,n){"use strict";"function"!=typeof Object.create&&(Object.create=function(){var e=function(){};return function(n){if(arguments.length>1)throw new Error("Second argument not supported");if(n!==Object(n)&&null!==n)throw new TypeError("Argument must be an object or null");if(null===n)throw Error("null [[Prototype]] not supported");e.prototype=n;var t=new e;return e.prototype=null,t}}()),Array.prototype.map||(Array.prototype.map=function(e,n){var t,r,a;if(null==this)throw new TypeError(" this is null or not defined");var s=Object(this),o=s.length>>>0;if("function"!=typeof e)throw new TypeError(e+" is not a function");for(arguments.length>1&&(t=n),r=new Array(o),a=0;a<o;){var c,i;a in s&&(c=s[a],i=e.call(t,c,a,s),r[a]=i),a++}return r})},function(e,n,t){"use strict";var r=t(3),a=t(4),s=t(5),o=t(9);angular.module("pubnub.angular.service",[]).factory("Pubnub",["$rootScope",function(e){if("undefined"==typeof PUBNUB&&"undefined"==typeof PubNub)throw new Error("PUBNUB is not in global scope. Ensure that pubnub.js file is included before pubnub-angular.js");var n={},t={};return n.getPubNubVersion=function(){return"undefined"==typeof PUBNUB?"4":"3"},n.init=function(e){return n.getInstance(r.default_instance_name).init(e)},n.getInstance=function(r){var c=t[r];return angular.isDefined(c)&&c instanceof a?c:"string"==typeof r&&r.length>0?("3"===this.getPubNubVersion()?t[r]=new s(r,n,e):"4"===this.getPubNubVersion()&&(t[r]=new o(r,n,e)),t[r]):c},n.getEventNameFor=function(e,n){var t=arguments.length>2&&void 0!==arguments[2]?arguments[2]:r.default_instance_name;return[r.pubnub_prefix,t,e,n].join(":")},n.getMessageEventNameFor=function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:r.default_instance_name;return[r.pubnub_prefix,n,"subscribe","callback",e].join(":")},n.getPresenceEventNameFor=function(e){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:r.default_instance_name;return[r.pubnub_prefix,n,"subscribe","presence",e].join(":")},n.subscribe=function(e){this.getInstance(r.default_instance_name).subscribe(e)},n}])},function(e,n){e.exports={pubnub_prefix:"pubnub",default_instance_name:"default"}},function(e,n,t){"use strict";function r(e,n){if(!(e instanceof n))throw new TypeError("Cannot call a class as a function")}var a=function(){function e(e,n){for(var t=0;t<n.length;t++){var r=n[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(n,t,r){return t&&e(n.prototype,t),r&&e(n,r),n}}(),s=t(3);e.exports=function(){function e(n,t,a,o){var c=this;r(this,e),this.label=n,this.mockingInstance=null,this.pubnubInstance=null,o.methods_to_wrap.forEach(function(e){if(angular.isObject(e)){var n=Object.keys(e)[0],r=e[n];c[n]={},t[n]={},r.forEach(function(e){c.wrapMethod(e,n),t[n][e]=function(r,a){return t.getInstance(s.default_instance_name)[n][e](r,a)}})}else c.wrapMethod(e),t[e]=function(n,r){return t.getInstance(s.default_instance_name)[e](n,r)}}),o.methods_to_delegate.forEach(function(e){c[e]=function(n){return c.getOriginalInstance()[e](n)},t[e]=function(n){return this.getInstance(s.default_instance_name)[e](n)}})}return a(e,[{key:"getLabel",value:function(){return this.label}},{key:"getOriginalInstance",value:function(){if(this.pubnubInstance)return this.pubnubInstance;throw new ReferenceError("Pubnub default instance is not initialized yet. Invoke #init() method first.")}}]),e}()},function(e,n,t){"use strict";function r(e,n){if(!(e instanceof n))throw new TypeError("Cannot call a class as a function")}function a(e,n){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!n||"object"!=typeof n&&"function"!=typeof n?e:n}function s(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function, not "+typeof n);e.prototype=Object.create(n&&n.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),n&&(Object.setPrototypeOf?Object.setPrototypeOf(e,n):e.__proto__=n)}var o=function(){function e(e,n){for(var t=0;t<n.length;t++){var r=n[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(n,t,r){return t&&e(n.prototype,t),r&&e(n,r),n}}(),c=t(4),i=t(6),u=t(8);e.exports=function(e){function n(e,t,s){r(this,n);var o=a(this,(n.__proto__||Object.getPrototypeOf(n)).call(this,e,t,s,u));return o.mockingInstance=new i(e,t,s),o}return s(n,e),o(n,[{key:"init",value:function(e){this.pubnubInstance=new PUBNUB(e)}},{key:"subscribe",value:function(e){var n=this.mockingInstance.getCallbacksToMock(e,u.subscribe_callbacks_to_wrap);this.mockingInstance.mockCallbacks(this.getLabel(),"subscribe",e,n),this.getOriginalInstance().subscribe(e)}},{key:"wrapMethod",value:function(e){var n=this;this[e]=function(t){if(angular.isObject(t)){var r=n.mockingInstance.getCallbacksToMock(t,u.common_callbacks_to_wrap);n.mockingInstance.mockCallbacks(n.getLabel(),e,t,r)}return n.getOriginalInstance()[e](t)}}}]),n}(c)},function(e,n,t){"use strict";function r(e){if(Array.isArray(e)){for(var n=0,t=Array(e.length);n<e.length;n++)t[n]=e[n];return t}return Array.from(e)}function a(e,n){if(!(e instanceof n))throw new TypeError("Cannot call a class as a function")}function s(e,n){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!n||"object"!=typeof n&&"function"!=typeof n?e:n}function o(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function, not "+typeof n);e.prototype=Object.create(n&&n.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),n&&(Object.setPrototypeOf?Object.setPrototypeOf(e,n):e.__proto__=n)}var c=function(){function e(e,n){for(var t=0;t<n.length;t++){var r=n[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(n,t,r){return t&&e(n.prototype,t),r&&e(n,r),n}}(),i=t(7);e.exports=function(e){function n(){return a(this,n),s(this,(n.__proto__||Object.getPrototypeOf(n)).apply(this,arguments))}return o(n,e),c(n,[{key:"generateMockedVersionOfCallback",value:function(e,n,t,a,s){var o=this.$rootScope,c=this.service,i=s.channel||s.channel_group;return function(){var s,u,l;if((s=o.$broadcast).bind.apply(s,r([o,c.getEventNameFor(t,n,a)].concat(Array.prototype.slice.call(arguments))))(),n&&angular.isFunction(e)&&e.apply(void 0,arguments),"subscribe"===t)switch(n){case"callback":(u=o.$broadcast).bind.apply(u,r([o,c.getMessageEventNameFor(i,a)].concat(Array.prototype.slice.call(arguments))))();break;case"presence":(l=o.$broadcast).bind.apply(l,r([o,c.getPresenceEventNameFor(i,a)].concat(Array.prototype.slice.call(arguments))))()}}}},{key:"mockCallbacks",value:function(e,n,t,r){var a=void 0,s=void 0,o=r.length,c=void 0;for(c=0;c<o;c+=1){if(!angular.isObject(t))return;s=r[c],a=t[s],t[s]=this.generateMockedVersionOfCallback(a,s,n,e,t)}}}]),n}(i)},function(e,n){"use strict";function t(e,n){if(!(e instanceof n))throw new TypeError("Cannot call a class as a function")}var r=function(){function e(e,n){for(var t=0;t<n.length;t++){var r=n[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(n,t,r){return t&&e(n.prototype,t),r&&e(n,r),n}}();e.exports=function(){function e(n,r,a){t(this,e),this.label=n,this.$rootScope=a,this.service=r}return r(e,[{key:"getCallbacksToMock",value:function(e,n){var t=e.triggerEvents,r=[],a=void 0,s=void 0,o=void 0;if(!0===t)return n;if(angular.isObject(t)){for(a=t.length,o=0;o<a;o+=1)s=t[o],n.indexOf(s)>=0&&r.push(s);return r}return[]}}]),e}()},function(e,n){e.exports={methods_to_delegate:["replay","unsubscribe","revoke","audit","time","channel_group","channel_group_list_groups","channel_group_list_namespaces","channel_group_remove_namespace","channel_group_cloak","get_subscribed_channels","set_uuid","get_uuid","auth","set_cipher_key","get_cipher_key","raw_encrypt","raw_decrypt","set_heartbeat","get_heartbeat","set_heartbeat_interval","get_heartbeat_interval"],methods_to_wrap:["here_now","history","publish","fire","here_now","where_now","state","grant","revoke","channel_group_add_channel","channel_group_list_channels","channel_group_remove_channel","channel_group_remove_group","mobile_gw_provision"],subscribe_callbacks_to_wrap:["callback","connect","reconnect","disconnect","error","idle","presence"],common_callbacks_to_wrap:["callback","error"]}},function(e,n,t){"use strict";function r(e,n){if(!(e instanceof n))throw new TypeError("Cannot call a class as a function")}function a(e,n){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!n||"object"!=typeof n&&"function"!=typeof n?e:n}function s(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function, not "+typeof n);e.prototype=Object.create(n&&n.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),n&&(Object.setPrototypeOf?Object.setPrototypeOf(e,n):e.__proto__=n)}var o=function(){function e(e,n){for(var t=0;t<n.length;t++){var r=n[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(n,t,r){return t&&e(n.prototype,t),r&&e(n,r),n}}(),c=t(4),i=t(10),u=t(11),l=t(12);e.exports=function(e){function n(e,t,s){r(this,n);var o=a(this,(n.__proto__||Object.getPrototypeOf(n)).call(this,e,t,s,l));return o.mockingInstance=new i(e,t,s),o.subscribeEventsBroadcaster=new u(e,t,s,o),o}return s(n,e),o(n,[{key:"init",value:function(e){this.pubnubInstance=new PubNub(e)}},{key:"subscribe",value:function(e){var n=this.mockingInstance.getCallbacksToMock(e,l.subscribe_listener_events_to_broadcast);this.subscribeEventsBroadcaster.enableEventsBroadcast(n,e),this.getOriginalInstance().subscribe(e)}},{key:"wrapMethod",value:function(e,n){var t=this;void 0!==n?this[n][e]=function(r,a){if(angular.isObject(r)){if(t.mockingInstance.getCallbacksToMock(r,l.common_callbacks_to_wrap).length>0){var s=n+"."+e;a=t.mockingInstance.generateMockedVersionOfCallback(a,"callback",s,t.getLabel())}}return t.getOriginalInstance()[n][e](r,a)}:this[e]=function(n,r){if(angular.isObject(n)){t.mockingInstance.getCallbacksToMock(n,l.common_callbacks_to_wrap).length>0&&(r=t.mockingInstance.generateMockedVersionOfCallback(r,"callback",e,t.getLabel()))}return t.getOriginalInstance()[e](n,r)}}}]),n}(c)},function(e,n,t){"use strict";function r(e){if(Array.isArray(e)){for(var n=0,t=Array(e.length);n<e.length;n++)t[n]=e[n];return t}return Array.from(e)}function a(e,n){if(!(e instanceof n))throw new TypeError("Cannot call a class as a function")}function s(e,n){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!n||"object"!=typeof n&&"function"!=typeof n?e:n}function o(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Super expression must either be null or a function, not "+typeof n);e.prototype=Object.create(n&&n.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),n&&(Object.setPrototypeOf?Object.setPrototypeOf(e,n):e.__proto__=n)}var c=function(){function e(e,n){for(var t=0;t<n.length;t++){var r=n[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(n,t,r){return t&&e(n.prototype,t),r&&e(n,r),n}}(),i=t(7);e.exports=function(e){function n(){return a(this,n),s(this,(n.__proto__||Object.getPrototypeOf(n)).apply(this,arguments))}return o(n,e),c(n,[{key:"generateMockedVersionOfCallback",value:function(e,n,t,a){var s=this.$rootScope,o=this.service;return function(){var c;(c=s.$broadcast).bind.apply(c,r([s,o.getEventNameFor(t,n,a)].concat(Array.prototype.slice.call(arguments))))(),n&&angular.isFunction(e)&&e.apply(void 0,arguments)}}}]),n}(i)},function(e,n){"use strict";function t(e,n){if(!(e instanceof n))throw new TypeError("Cannot call a class as a function")}var r=function(){function e(e,n){for(var t=0;t<n.length;t++){var r=n[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(n,t,r){return t&&e(n.prototype,t),r&&e(n,r),n}}();e.exports=function(){function e(n,r,a,s){t(this,e),this.wrapper=s,this.label=n,this.$rootScope=a,this.service=r,this.broadcastStatus=!1,this.broadcastedChannels={},this.broadcastedPresenceChannels={},this.subscribeListener=null}return r(e,[{key:"initializeSubscribeListener",value:function(){var e=this.$rootScope,n=this.service,t=this;this.subscribeListener=this.service.getInstance(this.label).addListener({message:function(r){(r.subscription&&t.broadcastedChannels[r.subscription]||r.channel&&t.broadcastedChannels[r.channel])&&e.$broadcast.bind.apply(e.$broadcast,[e,n.getMessageEventNameFor(r.subscribedChannel,t.label)].concat(Array.prototype.slice.call(arguments)))()},presence:function(r){var a=null;null!==r.subscription&&t.broadcastedPresenceChannels[r.subscription]?a=r.subscription:null!==r.channel&&t.broadcastedPresenceChannels[r.channel]&&(a=r.channel),null!==a&&e.$broadcast.bind.apply(e.$broadcast,[e,n.getPresenceEventNameFor(a,t.label)].concat(Array.prototype.slice.call(arguments)))()},status:function(){if(t.broadcastStatus){var e=t.service.getEventNameFor("subscribe","status",t.label);t.$rootScope.$broadcast.bind.apply(t.$rootScope.$broadcast,[t.$rootScope,e].concat(Array.prototype.slice.call(arguments)))()}}})}},{key:"enableEventsBroadcast",value:function(e,n){var t=this,r=[];return!!n.triggerEvents&&(r=!0===n.triggerEvents?["status","message","presence"]:n.triggerEvents,e.forEach(function(e){"status"===e&&r.includes("status")&&(t.broadcastStatus=!0),"message"===e&&r.includes("message")&&(n.channels&&n.channels.length>0&&n.channels.forEach(function(e){"-pnpres"!==e.slice(-7)&&(t.broadcastedChannels[e]=!0)}),n.channelGroups&&n.channelGroups.length>0&&n.channelGroups.forEach(function(e){"-pnpres"!==e.slice(-7)&&(t.broadcastedChannels[e]=!0)})),"presence"===e&&r.includes("presence")&&(n.withPresence?(n.channels&&n.channels.length>0&&n.channels.forEach(function(e){return t.broadcastedPresenceChannels[e]=!0}),n.channelGroups&&n.channelGroups&&n.channelGroups.forEach(function(e){return t.broadcastedPresenceChannels[e]=!0})):(n.channels&&n.channels.length>0&&n.channels.forEach(function(e){"-pnpres"===e.slice(-7)&&(t.broadcastedPresenceChannels[e.slice(0,-7)]=!0)}),n.channelGroups&&n.channelGroups&&n.channelGroups.forEach(function(e){"-pnpres"===e.slice(-7)&&(t.broadcastedPresenceChannels[e.slice(0,-7)]=!0)})))}),null===this.subscribeListener&&this.initializeSubscribeListener(),!0)}}]),e}()},function(e,n){e.exports={methods_to_delegate:["setUUID","getUUID","setAuthKey","addListener","removeListener","unsubscribe","unsubscribeAll","time","stop","encrypt","decrypt","setFilterExpression","setHeartbeatInterval"],methods_to_wrap:["publish","fire","deleteMessages","fetchMessages","hereNow","whereNow","setState","getState","grant","history",{push:["addChannels","deleteDevice","listChannels","removeChannels"]},{channelGroups:["addChannels","deleteGroup","listChannels","listGroups","removeChannels"]}],common_callbacks_to_wrap:["callback"],subscribe_listener_events_to_broadcast:["message","presence","status"]}},function(e,n,t){"use strict";var r=t(3);angular.module("pubnub.angular.service").factory("$pubnubChannel",["$rootScope","Pubnub","$q",function(e,n,t){function a(t){var s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!(this instanceof a))return new a(t,s);if(!t)throw new Error("The channel name is required");if(s.autosubscribe&&"boolean"!=typeof s.autosubscribe)throw new Error("The autosubscribe parameter should be a boolean");if(s.presence&&"boolean"!=typeof s.presence)throw new Error("The presence parameter should be a boolean");if(s.autostore&&"boolean"!=typeof s.autostore)throw new Error("The autostore parameter should be a boolean");var o=this;this._channel=t,this.$messages=[],this._timeTokenFirstMessage=null,this._messagesAllFetched=!1,this._pubnubInstance=s.instance?n.getInstance(s.instance):n.getInstance(r.default_instance_name),this._autoload=null==s.autoload?0:s.autoload,this._presence=null!=s.presence&&s.presence,this._autosubscribe=null==s.autosubscribe||s.autosubscribe,this._autostore=null==s.autostore||s.autostore,this.$$getPublicMethods(function(e,n){o.$messages[n]=e.bind(o)}),this._unsubscribeHandler=null,0!==this._autoload&&this.$load(this._autoload);var c=null;if(c="3"===n.getPubNubVersion()?["callback","connect","reconnect","disconnect","error","idle"]:["status","message"],this._presence&&c.push("presence"),this._autosubscribe){var i={triggerEvents:c};"3"===n.getPubNubVersion()?(i.channel=this._channel,i.noheresync=!0):(i.channels=[this._channel],this._presence&&(i.withPresence=!0)),this._pubnubInstance.subscribe(i)}if(this._autostore){var u=n.getMessageEventNameFor(o._channel,this._pubnubInstance.label);this._unsubscribeHandler=e.$on(u,o.$$newMessage.bind(o))}return this.$messages}return a.prototype={$load:function(r){if(!(r>0&&r<=100))throw new Error("The number of messages to load should be a number between 0 and 100");var a=this,s=t.defer(),o={channel:a._channel,count:r,reverse:!1},c=null;return"3"===n.getPubNubVersion()?(o.callback=function(n){a._timeTokenFirstMessage=n[1],a.$$storeBatch(n[0]),n[0].length<r&&(a._messagesAllFetched=!0),s.resolve(n),e.$digest()},o.error=function(e){s.reject(e)}):c=function(n,t){n.error?s.reject(t):(a._timeTokenFirstMessage=t.startTimeToken,a.$$storeBatch(t.messages.map(function(e){return e.entry})),t.messages.length<r&&(a._messagesAllFetched=!0),s.resolve(t),e.$digest())},a._timeTokenFirstMessage&&(o.start=a._timeTokenFirstMessage),a._pubnubInstance.history(o,c),s.promise},$publish:function(e){var r=this,a=t.defer(),s={channel:r._channel,message:e},o=null;return"3"===n.getPubNubVersion()?(s.callback=function(e){a.resolve(e)},s.error=function(e){a.reject(e)}):o=function(e,n){e.error?a.reject(n):a.resolve(n)},r._pubnubInstance.publish(s,o),a.promise},$pubnubInstance:function(){return this._pubnubInstance},$channel:function(){return this._channel},$allLoaded:function(){return this._messagesAllFetched},$destroy:function(){this._unsubscribeHandler&&this._unsubscribeHandler(),this.$messages.length=0},$$newMessage:function(t,r){"3"===n.getPubNubVersion()?this.$$store(r):this.$$store(r.message),e.$digest()},$$store:function(e){this.$messages.push(e)},$$storeBatch:function(e){0===this.$messages.length?angular.extend(this.$messages,e):Array.prototype.unshift.apply(this.$messages,e)},$$getPublicMethods:function(e,n){this.$$getPrototypeMethods(function(t,r){"function"==typeof t&&"_"!==r.charAt(0)&&e.call(n,t,r)})},$$getPrototypeMethods:function(e,n){for(var t={},r=Object.getPrototypeOf({}),a=angular.isFunction(this)&&angular.isObject(this.prototype)?this.prototype:Object.getPrototypeOf(this);a&&a!==r;)Object.keys(a).forEach(function(r){({}).hasOwnProperty.call(a,r)&&!{}.hasOwnProperty.call(t,r)&&(t[r]=!0,e.call(n,a[r],r,a))}),a=Object.getPrototypeOf(a)}},a.$extend=function(e){if(!angular.isObject(e))throw new Error("The methods parameter should be an object");var n=function e(n,t){return this instanceof a?(a.apply(this,arguments),this.$messages):new e(n,t)};return n.prototype=Object.create(a.prototype),angular.extend(n.prototype,e),n},a}])},function(e,n,t){"use strict";var r=t(3);angular.module("pubnub.angular.service").factory("$pubnubChannelGroup",["$rootScope","$q","Pubnub","$pubnubChannel",function(e,n,t,a){function s(n,o){if(!(this instanceof s))return new s(n,o);var c=this,i=o||{};if(!n)throw new Error("The channel group name is required");if(i.autosubscribe&&"boolean"!=typeof i.autosubscribe)throw new Error("The autosubscribe parameter should be a boolean");if(i.presence&&"boolean"!=typeof i.presence)throw new Error("The presence parameter should be a boolean");if(i.channelExtension&&!angular.isObject(i.channelExtension))throw new Error("The channelExtension should be an object");this._channelGroup=n,this.$channels={},this._pubnubInstance=i.instance?t.getInstance(i.instance):t.getInstance(r.default_instance_name),this._presence=null!=i.presence&&i.presence,this._autosubscribe=null==i.autosubscribe||i.autosubscribe,this._extendedChannel=i.channelExtension?a.$extend(i.channelExtension):null,this._unsubscribeHandler=null;var u=null;if(u="3"===t.getPubNubVersion()?["callback","connect","reconnect","disconnect","error","idle"]:["status","message"],this._presence&&u.push("presence"),this._autosubscribe){var l={triggerEvents:u};"3"===t.getPubNubVersion()?l.channel_group=this._channelGroup:l.channelGroups=[this._channelGroup],this._pubnubInstance.subscribe(l)}var b=t.getMessageEventNameFor(c._channelGroup,c._pubnubInstance.label);return this._unsubscribeHandler=e.$on(b,c.$$newMessage.bind(c)),this}return s.prototype={$channel:function(e){if(!angular.isDefined(this.$channels[e])){var n={instance:this._pubnubInstance.label,autosubscribe:!1,presence:!1,autostore:!0},t=this._extendedChannel?new this._extendedChannel(e,n):a(e,n);this.$channels[e]=t}return this.$channels[e]},$pubnubInstance:function(){return this._pubnubInstance},$channelGroup:function(){return this._channelGroup},$destroy:function(){var e=this;this._unsubscribeHandler(),Object.keys(this.$channels).forEach(function(n){({}).hasOwnProperty.call(e.$channels,n)&&delete e.$channels[n]})},$$newMessage:function(e,n,r){var a=null;a="3"===t.getPubNubVersion()?r[3]:n.channel,this.$channel(a).$$newMessage(e,n,r)}},s}])}]);