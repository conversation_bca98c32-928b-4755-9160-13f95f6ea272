// @preserve jQuery.floatThead 1.3.1 - http://mkoryak.github.io/floatThead/ - Copyright (c) 2012 - 2015 <PERSON><PERSON>
// @license MIT
!function(a){function b(a,b){if(8==j){var c=o.width(),d=h.debounce(function(){var a=o.width();c!=a&&(c=a,b())},1);o.on(a,d)}else o.on(a,h.debounce(b,1))}function c(a){window&&window.console&&window.console.error&&window.console.error("jQuery.floatThead: "+a)}function d(a){var b=a.getBoundingClientRect();return b.width||b.right-b.left}function e(){var b=a('<div style="width:50px;height:50px;overflow-y:scroll;position:absolute;top:-200px;left:-200px;"><div style="height:100px;width:100%"></div>');a("body").append(b);var c=b.innerWidth(),d=a("div",b).innerWidth();return b.remove(),c-d}function f(a){if(a.dataTableSettings)for(var b=0;b<a.dataTableSettings.length;b++){var c=a.dataTableSettings[b].nTable;if(a[0]==c)return!0}return!1}function g(a,b,c){var d=c?"outerWidth":"width";if(m&&a.css("max-width")){var e=0;c&&(e+=parseInt(a.css("borderLeft"),10),e+=parseInt(a.css("borderRight"),10));for(var f=0;f<b.length;f++)e+=b.get(f).offsetWidth;return e}return a[d]()}a.floatThead=a.floatThead||{},a.floatThead.defaults={headerCellSelector:"tr:visible:first>*:visible",zIndex:1001,position:"auto",top:0,bottom:0,scrollContainer:function(){return a([])},getSizingRow:function(a){return a.find("tbody tr:visible:first>*:visible")},floatTableClass:"floatThead-table",floatWrapperClass:"floatThead-wrapper",floatContainerClass:"floatThead-container",copyTableClass:!0,enableAria:!1,autoReflow:!1,debug:!1};var h=window._,i="undefined"!=typeof MutationObserver,j=function(){for(var a=3,b=document.createElement("b"),c=b.all||[];a=1+a,b.innerHTML="<!--[if gt IE "+a+"]><i><![endif]-->",c[0];);return a>4?a:document.documentMode}(),k=/Gecko\//.test(navigator.userAgent),l=/WebKit\//.test(navigator.userAgent),m=function(){if(l){var b=a('<div style="width:0px"><table style="max-width:100%"><tr><th><div style="min-width:100px;">X</div></th></tr></table></div>');a("body").append(b);var c=0==b.find("table").width();return b.remove(),c}return!1},n=!k&&!j,o=a(window);a.fn.floatThead=function(k){if(k=k||{},!h&&(h=window._||a.floatThead._,!h))throw new Error("jquery.floatThead-slim.js requires underscore. You should use the non-lite version since you do not have underscore.");if(8>j)return this;var p=null;if(h.isFunction(m)&&(m=m()),h.isString(k)){var q=k,r=this;return this.filter("table").each(function(){var b=a(this),c=b.data("floatThead-lazy");c&&b.floatThead(c);var d=b.data("floatThead-attached");if(d&&h.isFunction(d[q])){var e=d[q]();"undefined"!=typeof e&&(r=e)}}),r}var s=a.extend({},a.floatThead.defaults||{},k);if(a.each(k,function(b){b in a.floatThead.defaults||!s.debug||c("Used ["+b+"] key to init plugin, but that param is not an option for the plugin. Valid options are: "+h.keys(a.floatThead.defaults).join(", "))}),s.debug){var t=a.fn.jquery.split(".");1==parseInt(t[0],10)&&parseInt(t[1],10)<=7&&c("jQuery version "+a.fn.jquery+" detected! This plugin supports 1.8 or better, or 1.7.x with jQuery UI 1.8.24 -> http://jqueryui.com/resources/download/jquery-ui-1.8.24.zip")}return this.filter(":not(."+s.floatTableClass+")").each(function(){function k(a){return a+".fth-"+E+".floatTHead"}function m(){var b=0;if(G.children("tr:visible").each(function(){b+=a(this).outerHeight(!0)}),"collapse"==F.css("border-collapse")){var c=parseInt(F.css("border-top-width"),10),d=parseInt(F.find("thead tr:first").find(">*:first").css("border-top-width"),10);c>d&&(b-=c/2)}fb.outerHeight(b),gb.outerHeight(b)}function q(){var a=g(F,jb,!0),b=O.width()||a,c="hidden"!=O.css("overflow-y")?b-L.vertical:b;if(cb.width(c),P){var d=100*a/c;Y.css("width",d+"%")}else Y.outerWidth(a)}function r(){I=(h.isFunction(s.top)?s.top(F):s.top)||0,J=(h.isFunction(s.bottom)?s.bottom(F):s.bottom)||0}function t(){var b,c=G.find(s.headerCellSelector);if(ab?b=$.find("col").length:(b=0,c.each(function(){b+=parseInt(a(this).attr("colspan")||1,10)})),b!=N){N=b;for(var d,e=[],f=[],g=[],h=0;b>h;h++)e.push(s.enableAria&&(d=c.eq(h).text())?'<th scope="col" class="floatThead-col">'+d+"</th>":'<th class="floatThead-col"/>'),f.push("<col/>"),g.push("<fthtd style='display:table-cell;height:0;width:auto;'/>");f=f.join(""),e=e.join(""),n&&(g=g.join(""),bb.html(g),jb=bb.find("fthtd")),fb.html(e),gb=fb.find("th"),ab||$.html(f),hb=$.find("col"),Z.html(f),ib=Z.find("col")}return b}function u(){if(!K){if(K=!0,Q){var a=g(F,jb,!0),b=W.width();a>b&&F.css("minWidth",a)}F.css(mb),Y.css(mb),Y.append(G),H.before(eb),m()}}function v(){K&&(K=!1,Q&&F.width(ob),eb.detach(),F.prepend(G),F.css(nb),Y.css(nb),F.css("minWidth",pb),F.css("minWidth",g(F,jb)))}function w(a){qb!=a&&(qb=a,F.triggerHandler("floatThead",[a,cb]))}function x(a){Q!=a&&(Q=a,cb.css({position:Q?"absolute":"fixed"}))}function y(a,b,c,d){return n?c:d?s.getSizingRow(a,b,c):b}function z(){var a,b=t();return function(){hb=$.find("col");var c=y(F,hb,jb,j);if(c.length==b&&b>0){if(!ab)for(a=0;b>a;a++)hb.eq(a).css("width","");v();var e=[];for(a=0;b>a;a++)e[a]=d(c.get(a));for(a=0;b>a;a++)ib.eq(a).width(e[a]),hb.eq(a).width(e[a]);u()}else Y.append(G),F.css(nb),Y.css(nb),m()}}function A(a){var b=O.css("border-"+a+"-width"),c=0;return b&&~b.indexOf("px")&&(c=parseInt(b,10)),c}function B(){var a,b=O.scrollTop(),c=0,d=S?R.outerHeight(!0):0,e=T?d:-d,f=cb.height(),g=F.offset(),h=0,i=0;if(P){var j=O.offset();c=g.top-j.top+b,S&&T&&(c+=d),h=A("left"),i=A("top"),c-=i}else a=g.top-I-f+J+L.horizontal;var k=o.scrollTop(),m=o.scrollLeft(),n=O.scrollLeft();return function(j){var p=F[0].offsetWidth<=0&&F[0].offsetHeight<=0;if(!p&&db)return db=!1,setTimeout(function(){F.triggerHandler("reflow")},1),null;if(p&&(db=!0,!Q))return null;if("windowScroll"==j?(k=o.scrollTop(),m=o.scrollLeft()):"containerScroll"==j?(b=O.scrollTop(),n=O.scrollLeft()):"init"!=j&&(k=o.scrollTop(),m=o.scrollLeft(),b=O.scrollTop(),n=O.scrollLeft()),!l||!(0>k||0>m)){if(X)x("windowScrollDone"==j?!0:!1);else if("windowScrollDone"==j)return null;g=F.offset(),S&&T&&(g.top+=d);var q,r,s=F.outerHeight();if(P&&Q){if(c>=b){var t=c-b+i;q=t>0?t:0,w(!1)}else q=V?i:b,w(!0);r=h}else!P&&Q?(k>a+s+e?q=s-f+e:g.top>=k+I?(q=0,v(),w(!1)):(q=I+k-g.top+c+(T?d:0),u(),w(!0)),r=0):P&&!Q?(c>b||b-c>s?(q=g.top-k,v(),w(!1)):(q=g.top+b-k-c,u(),w(!0)),r=g.left+n-m):P||Q||(k>a+s+e?q=s+I-k+a+e:g.top>k+I?(q=g.top-k,u(),w(!1)):(q=I,w(!0)),r=g.left-m);return{top:q,left:r}}}}function C(){var a=null,b=null,c=null;return function(d,e,f){null==d||a==d.top&&b==d.left||(cb.css({top:d.top,left:d.left}),a=d.top,b=d.left),e&&q(),f&&m();var g=O.scrollLeft();Q&&c==g||(cb.scrollLeft(g),c=g)}}function D(){if(O.length)if(O.data().perfectScrollbar)L={horizontal:0,vertical:0};else{var a=O.width(),b=O.height(),c=F.height(),d=g(F,jb),e=d>a?M:0,f=c>b?M:0;L.horizontal=d>a-f?M:0,L.vertical=c>b-e?M:0}}var E=h.uniqueId(),F=a(this);if(F.data("floatThead-attached"))return!0;if(!F.is("table"))throw new Error('jQuery.floatThead must be run on a table element. ex: $("table").floatThead();');i=s.autoReflow&&i;var G=F.children("thead:first"),H=F.children("tbody:first");if(0==G.length||0==H.length)return F.data("floatThead-lazy",s),void F.unbind("reflow").one("reflow",function(){F.floatThead(s)});F.data("floatThead-lazy")&&F.unbind("reflow"),F.data("floatThead-lazy",!1);var I,J,K=!1,L={vertical:0,horizontal:0},M=e(),N=0,O=s.scrollContainer(F)||a([]),P=O.length>0,Q=null;"undefined"!=typeof s.useAbsolutePositioning&&(s.position="auto",s.useAbsolutePositioning&&(s.position=s.useAbsolutePositioning?"absolute":"fixed"),c("option 'useAbsolutePositioning' has been removed in v1.3.0, use `position:'"+s.position+"'` instead. See docs for more info: http://mkoryak.github.io/floatThead/#options")),"undefined"!=typeof s.scrollingTop&&(s.top=s.scrollingTop,c("option 'scrollingTop' has been renamed to 'top' in v1.3.0. See docs for more info: http://mkoryak.github.io/floatThead/#options")),"undefined"!=typeof s.scrollingBottom&&(s.bottom=s.scrollingBottom,c("option 'scrollingBottom' has been renamed to 'bottom' in v1.3.0. See docs for more info: http://mkoryak.github.io/floatThead/#options")),"auto"==s.position?Q=null:"fixed"==s.position?Q=!1:"absolute"==s.position?Q=!0:s.debug&&c('Invalid value given to "position" option, valid is "fixed", "absolute" and "auto". You passed: ',s.position),null==Q&&(Q=P),Q||(K=!0);var R=F.find("caption"),S=1==R.length;if(S)var T="top"===(R.css("caption-side")||R.attr("align")||"top");var U=a('<fthfoot style="display:table-footer-group;border-spacing:0;height:0;border-collapse:collapse;"/>'),V=!1,W=a([]),X=9>=j&&!P&&Q,Y=a("<table/>"),Z=a("<colgroup/>"),$=F.children("colgroup:first"),ab=!0;0==$.length&&($=a("<colgroup/>"),ab=!1);var bb=a('<fthtr style="display:table-row;border-spacing:0;height:0;border-collapse:collapse"/>'),cb=a('<div style="overflow: hidden;" aria-hidden="true"></div>'),db=!1,eb=a("<thead/>"),fb=a('<tr class="size-row"/>'),gb=a([]),hb=a([]),ib=a([]),jb=a([]);eb.append(fb),F.prepend($),n&&(U.append(bb),F.append(U)),Y.append(Z),cb.append(Y),s.copyTableClass&&Y.attr("class",F.attr("class")),Y.attr({cellpadding:F.attr("cellpadding"),cellspacing:F.attr("cellspacing"),border:F.attr("border")});var kb=F.css("display");if(Y.css({borderCollapse:F.css("borderCollapse"),border:F.css("border"),display:kb}),"none"==kb&&(db=!0),Y.addClass(s.floatTableClass).css({margin:0,"border-bottom-width":0}),Q){var lb=function(a,b){var c=a.css("position"),d="relative"==c||"absolute"==c,e=a;if(!d||b){var f={paddingLeft:a.css("paddingLeft"),paddingRight:a.css("paddingRight")};cb.css(f),e=a.data("floatThead-containerWrap")||a.wrap("<div class='"+s.floatWrapperClass+"' style='position: relative; clear:both;'></div>").parent(),a.data("floatThead-containerWrap",e),V=!0}return e};P?(W=lb(O,!0),W.prepend(cb)):(W=lb(F),F.before(cb))}else F.before(cb);cb.css({position:Q?"absolute":"fixed",marginTop:0,top:Q?0:"auto",zIndex:s.zIndex}),cb.addClass(s.floatContainerClass),r();var mb={"table-layout":"fixed"},nb={"table-layout":F.css("tableLayout")||"auto"},ob=F[0].style.width||"",pb=F.css("minWidth")||"",qb=!1;D();var rb,sb=function(){(rb=z())()};sb();var tb=B(),ub=C();ub(tb("init"),!0);var vb=h.debounce(function(){ub(tb("windowScrollDone"),!1)},1),wb=function(){ub(tb("windowScroll"),!1),X&&vb()},xb=function(){ub(tb("containerScroll"),!1)},yb=function(){F.is(":hidden")||(r(),D(),sb(),tb=B(),(ub=C())(tb("resize"),!0,!0))},zb=h.debounce(function(){F.is(":hidden")||(D(),r(),sb(),tb=B(),ub(tb("reflow"),!0))},1);if(P?Q?O.on(k("scroll"),xb):(O.on(k("scroll"),xb),o.on(k("scroll"),wb)):o.on(k("scroll"),wb),o.on(k("load"),zb),b(k("resize"),yb),F.on("reflow",zb),f(F)&&F.on("filter",zb).on("sort",zb).on("page",zb),o.on(k("shown.bs.tab"),zb),o.on(k("tabsactivate"),zb),i){var Ab=null;_.isFunction(s.autoReflow)&&(Ab=s.autoReflow(F,O)),Ab||(Ab=O.length?O[0]:F[0]),p=new MutationObserver(function(a){for(var b=function(a){return a&&a[0]&&("THEAD"==a[0].nodeName||"TD"==a[0].nodeName||"TH"==a[0].nodeName)},c=0;c<a.length;c++)if(!b(a[c].addedNodes)&&!b(a[c].removedNodes)){zb();break}}),p.observe(Ab,{childList:!0,subtree:!0})}F.data("floatThead-attached",{destroy:function(){var a=".fth-"+E;v(),F.css(nb),$.remove(),n&&U.remove(),eb.parent().length&&eb.replaceWith(G),i&&(p.disconnect(),p=null),F.off("reflow"),O.off(a),V&&(O.length?O.unwrap():F.unwrap()),P?O.data("floatThead-containerWrap",!1):F.data("floatThead-containerWrap",!1),F.css("minWidth",pb),cb.remove(),F.data("floatThead-attached",!1),o.off(a)},reflow:function(){zb()},setHeaderHeight:function(){m()},getFloatContainer:function(){return cb},getRowGroups:function(){return K?cb.find(">table>thead").add(F.children("tbody,tfoot")):F.children("thead,tbody,tfoot")}})}),this}}(jQuery);