/*** Directives and services for responding to idle users in AngularJS
 * <AUTHOR> <<EMAIL>>
 * @version v1.1.0
 * @link https://github.com/HackedByChinese/ng-idle.git
 * @license MIT
 */

!function(a,b,c){"use strict";b.module("ngIdle",["ngIdle.keepalive","ngIdle.idle","ngIdle.countdown","ngIdle.title","ngIdle.localStorage"]),b.module("ngIdle.keepalive",[]).provider("Keepalive",function(){var a={http:null,interval:600};this.http=function(c){if(!c)throw new Error("Argument must be a string containing a URL, or an object containing the HTTP request configuration.");b.isString(c)&&(c={url:c,method:"GET"}),c.cache=!1,a.http=c};var c=this.interval=function(b){if(b=parseInt(b),isNaN(b)||0>=b)throw new Error("Interval must be expressed in seconds and be greater than 0.");a.interval=b};this.$get=["$rootScope","$log","$interval","$http",function(d,e,f,g){function h(a,b){d.$broadcast("KeepaliveResponse",a,b)}function i(){d.$broadcast("Keepalive"),b.isObject(a.http)&&g(a.http).success(h).error(h)}var j={ping:null};return{_options:function(){return a},setInterval:c,start:function(){return f.cancel(j.ping),j.ping=f(i,1e3*a.interval),j.ping},stop:function(){f.cancel(j.ping)},ping:function(){i()}}}]}),b.module("ngIdle.idle",["ngIdle.keepalive","ngIdle.localStorage"]).provider("Idle",function(){var a={idle:1200,timeout:30,autoResume:"idle",interrupt:"mousemove keydown DOMMouseScroll mousewheel mousedown touchstart touchmove scroll",keepalive:!0,titleDisabled:!1},c=this.timeout=function(c){if(c===!1)a.timeout=0;else{if(!(b.isNumber(c)&&c>=0))throw new Error("Timeout must be zero or false to disable the feature, or a positive integer (in seconds) to enable it.");a.timeout=c}};this.interrupt=function(b){a.interrupt=b};var d=this.idle=function(b){if(0>=b)throw new Error("Idle must be a value in seconds, greater than 0.");a.idle=b},e=this.titleDisabled=function(b){a.titleDisabled=b===!0};this.autoResume=function(b){b===!0?a.autoResume="idle":b===!1?a.autoResume="off":a.autoResume=b},this.keepalive=function(b){a.keepalive=b===!0},this.$get=["$interval","$log","$rootScope","$document","Keepalive","IdleLocalStorage","$window",function(f,g,h,i,j,k,l){function m(){a.keepalive&&(u.running&&j.ping(),j.start())}function n(){a.keepalive&&j.stop()}function o(){u.idling=!u.idling;var b=u.idling?"Start":"End";h.$broadcast("Idle"+b),u.idling?(n(),a.timeout&&(u.countdown=a.timeout,p(),u.timeout=f(p,1e3,a.timeout,!1))):m(),f.cancel(u.idle)}function p(){return u.countdown<=0?void q():(h.$broadcast("IdleWarn",u.countdown),void u.countdown--)}function q(){n(),f.cancel(u.idle),f.cancel(u.timeout),u.idling=!0,u.running=!1,u.countdown=0,h.$broadcast("IdleTimeout")}function r(a,b,c){var d=a.running();a.unwatch(),b(c),d&&a.watch()}function s(){var a=k.get("expiry");return a&&a.time?new Date(a.time):null}function t(a){a?k.set("expiry",{id:v,time:a}):k.remove("expiry")}var u={idle:null,timeout:null,idling:!1,running:!1,countdown:null},v=(new Date).getTime(),w={_options:function(){return a},_getNow:function(){return new Date},getIdle:function(){return a.idle},getTimeout:function(){return a.timeout},setIdle:function(a){r(this,d,a)},setTitleDisabled:function(a){r(this,e,a)},isTitleDisabled:function(){return a.titleDisabled},setTimeout:function(a){r(this,c,a)},isExpired:function(){var a=s();return null!==a&&a<=this._getNow()},running:function(){return u.running},idling:function(){return u.idling},watch:function(b){f.cancel(u.idle),f.cancel(u.timeout);var c=a.timeout?a.timeout:0;b||t(new Date((new Date).getTime()+1e3*(a.idle+c))),u.idling?o():u.running||m(),u.running=!0,u.idle=f(o,1e3*a.idle,0,!1)},unwatch:function(){f.cancel(u.idle),f.cancel(u.timeout),u.idling=!1,u.running=!1,t(null),n()},interrupt:function(b){return u.running?a.timeout&&this.isExpired()?void q():void(("idle"===a.autoResume||"notIdle"===a.autoResume&&!u.idling)&&this.watch(b)):void 0}};i.find("body").on(a.interrupt,function(){w.interrupt()});var x=function(a){if("ngIdle.expiry"===a.key&&a.newValue!==a.oldValue){var c=b.fromJson(a.newValue);if(c.id===v)return;w.interrupt(!0)}};return l.addEventListener?l.addEventListener("storage",x,!1):l.attachEvent("onstorage",x),w}]}),b.module("ngIdle.countdown",["ngIdle.idle"]).directive("idleCountdown",["Idle",function(a){return{restrict:"A",scope:{value:"=idleCountdown"},link:function(b){b.value=a.getTimeout(),b.$on("IdleWarn",function(a,c){b.$evalAsync(function(){b.value=c})}),b.$on("IdleTimeout",function(){b.$evalAsync(function(){b.value=0})})}}}]),b.module("ngIdle.title",[]).factory("Title",["$document","$interpolate",function(a,c){function d(a,b,c){return new Array(b-String(a).length+1).join(c||"0")+a}var e={original:null,idle:"{{minutes}}:{{seconds}} until your session times out!",timedout:"Your session has expired."};return{original:function(a){return b.isUndefined(a)?e.original:void(e.original=a)},store:function(a){(a||!e.original)&&(e.original=this.value())},value:function(c){return b.isUndefined(c)?a[0].title:void(a[0].title=c)},idleMessage:function(a){return b.isUndefined(a)?e.idle:void(e.idle=a)},timedOutMessage:function(a){return b.isUndefined(a)?e.timedout:void(e.timedout=a)},setAsIdle:function(a){this.store();var b={totalSeconds:a};b.minutes=Math.floor(a/60),b.seconds=d(a-60*b.minutes,2),this.value(c(this.idleMessage())(b))},setAsTimedOut:function(){this.store(),this.value(this.timedOutMessage())},restore:function(){this.original()&&this.value(this.original())}}}]).directive("title",["Idle","Title",function(a,b){return{restrict:"E",link:function(c,d,e){a.isTitleDisabled()||e.idleDisabled||(b.store(!0),c.$on("IdleStart",function(){b.original(d[0].innerText)}),c.$on("IdleWarn",function(a,c){b.setAsIdle(c)}),c.$on("IdleEnd",function(){b.restore()}),c.$on("IdleTimeout",function(){b.setAsTimedOut()}))}}}]),b.module("ngIdle.localStorage",[]).service("IdleLocalStorage",["$window",function(a){var c=a.localStorage;return{set:function(a,d){c.setItem("ngIdle."+a,b.toJson(d))},get:function(a){return b.fromJson(c.getItem("ngIdle."+a))},remove:function(a){c.removeItem("ngIdle."+a)}}}])}(window,window.angular);
//# sourceMappingURL=angular-idle.map