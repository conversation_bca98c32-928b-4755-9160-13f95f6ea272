!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).PubNub=t()}(this,(function(){"use strict";
/*! *****************************************************************************
    Copyright (c) Microsoft Corporation.

    Permission to use, copy, modify, and/or distribute this software for any
    purpose with or without fee is hereby granted.

    THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
    REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
    AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
    INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
    LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
    OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
    PERFORMANCE OF THIS SOFTWARE.
    ***************************************************************************** */var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};function t(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}var n=function(){return n=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var i in t=arguments[n])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e},n.apply(this,arguments)};function r(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(e);i<r.length;i++)t.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]])}return n}function i(e,t,n,r){return new(n||(n=Promise))((function(i,o){function s(e){try{u(r.next(e))}catch(e){o(e)}}function a(e){try{u(r.throw(e))}catch(e){o(e)}}function u(e){var t;e.done?i(e.value):(t=e.value,t instanceof n?t:new n((function(e){e(t)}))).then(s,a)}u((r=r.apply(e,t||[])).next())}))}function o(e,t){var n,r,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(o){return function(a){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;s;)try{if(n=1,r&&(i=2&o[0]?r.return:o[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,o[1])).done)return i;switch(r=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,r=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(!(i=s.trys,(i=i.length>0&&i[i.length-1])||6!==o[0]&&2!==o[0])){s=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){s.label=o[1];break}if(6===o[0]&&s.label<i[1]){s.label=i[1],i=o;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(o);break}i[2]&&s.ops.pop(),s.trys.pop();continue}o=t.call(e,s)}catch(e){o=[6,e],r=0}finally{n=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,a])}}}function s(e){var t="function"==typeof Symbol&&Symbol.iterator,n=t&&e[t],r=0;if(n)return n.call(e);if(e&&"number"==typeof e.length)return{next:function(){return e&&r>=e.length&&(e=void 0),{value:e&&e[r++],done:!e}}};throw new TypeError(t?"Object is not iterable.":"Symbol.iterator is not defined.")}function a(e,t){var n="function"==typeof Symbol&&e[Symbol.iterator];if(!n)return e;var r,i,o=n.call(e),s=[];try{for(;(void 0===t||t-- >0)&&!(r=o.next()).done;)s.push(r.value)}catch(e){i={error:e}}finally{try{r&&!r.done&&(n=o.return)&&n.call(o)}finally{if(i)throw i.error}}return s}function u(e,t,n){if(n||2===arguments.length)for(var r,i=0,o=t.length;i<o;i++)!r&&i in t||(r||(r=Array.prototype.slice.call(t,0,i)),r[i]=t[i]);return e.concat(r||Array.prototype.slice.call(t))}var c="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},l={exports:{}};!function(e){!function(t,n){var r=Math.pow(2,-24),i=Math.pow(2,32),o=Math.pow(2,53);var s={encode:function(e){var t,r=new ArrayBuffer(256),s=new DataView(r),a=0;function u(e){for(var n=r.byteLength,i=a+e;n<i;)n*=2;if(n!==r.byteLength){var o=s;r=new ArrayBuffer(n),s=new DataView(r);for(var u=a+3>>2,c=0;c<u;++c)s.setUint32(4*c,o.getUint32(4*c))}return t=e,s}function c(){a+=t}function l(e){c(u(1).setUint8(a,e))}function p(e){for(var t=u(e.length),n=0;n<e.length;++n)t.setUint8(a+n,e[n]);c()}function h(e,t){t<24?l(e<<5|t):t<256?(l(e<<5|24),l(t)):t<65536?(l(e<<5|25),function(e){c(u(2).setUint16(a,e))}(t)):t<4294967296?(l(e<<5|26),function(e){c(u(4).setUint32(a,e))}(t)):(l(e<<5|27),function(e){var t=e%i,n=(e-t)/i,r=u(8);r.setUint32(a,n),r.setUint32(a+4,t),c()}(t))}if(function e(t){var r;if(!1===t)return l(244);if(!0===t)return l(245);if(null===t)return l(246);if(t===n)return l(247);switch(typeof t){case"number":if(Math.floor(t)===t){if(0<=t&&t<=o)return h(0,t);if(-o<=t&&t<0)return h(1,-(t+1))}return l(251),function(e){c(u(8).setFloat64(a,e))}(t);case"string":var i=[];for(r=0;r<t.length;++r){var s=t.charCodeAt(r);s<128?i.push(s):s<2048?(i.push(192|s>>6),i.push(128|63&s)):s<55296?(i.push(224|s>>12),i.push(128|s>>6&63),i.push(128|63&s)):(s=(1023&s)<<10,s|=1023&t.charCodeAt(++r),s+=65536,i.push(240|s>>18),i.push(128|s>>12&63),i.push(128|s>>6&63),i.push(128|63&s))}return h(3,i.length),p(i);default:var f;if(Array.isArray(t))for(h(4,f=t.length),r=0;r<f;++r)e(t[r]);else if(t instanceof Uint8Array)h(2,t.length),p(t);else{var d=Object.keys(t);for(h(5,f=d.length),r=0;r<f;++r){var g=d[r];e(g),e(t[g])}}}}(e),"slice"in r)return r.slice(0,a);for(var f=new ArrayBuffer(a),d=new DataView(f),g=0;g<a;++g)d.setUint8(g,s.getUint8(g));return f},decode:function(e,t,o){var s=new DataView(e),a=0;function u(e,t){return a+=t,e}function c(t){return u(new Uint8Array(e,a,t),t)}function l(){return u(s.getUint8(a),1)}function p(){return u(s.getUint16(a),2)}function h(){return u(s.getUint32(a),4)}function f(){return 255===s.getUint8(a)&&(a+=1,!0)}function d(e){if(e<24)return e;if(24===e)return l();if(25===e)return p();if(26===e)return h();if(27===e)return h()*i+h();if(31===e)return-1;throw"Invalid length encoding"}function g(e){var t=l();if(255===t)return-1;var n=d(31&t);if(n<0||t>>5!==e)throw"Invalid indefinite length element";return n}function y(e,t){for(var n=0;n<t;++n){var r=l();128&r&&(r<224?(r=(31&r)<<6|63&l(),t-=1):r<240?(r=(15&r)<<12|(63&l())<<6|63&l(),t-=2):(r=(15&r)<<18|(63&l())<<12|(63&l())<<6|63&l(),t-=3)),r<65536?e.push(r):(r-=65536,e.push(55296|r>>10),e.push(56320|1023&r))}}"function"!=typeof t&&(t=function(e){return e}),"function"!=typeof o&&(o=function(){return n});var b=function e(){var i,h,b=l(),v=b>>5,m=31&b;if(7===v)switch(m){case 25:return function(){var e=new ArrayBuffer(4),t=new DataView(e),n=p(),i=32768&n,o=31744&n,s=1023&n;if(31744===o)o=261120;else if(0!==o)o+=114688;else if(0!==s)return s*r;return t.setUint32(0,i<<16|o<<13|s<<13),t.getFloat32(0)}();case 26:return u(s.getFloat32(a),4);case 27:return u(s.getFloat64(a),8)}if((h=d(m))<0&&(v<2||6<v))throw"Invalid length";switch(v){case 0:return h;case 1:return-1-h;case 2:if(h<0){for(var _=[],O=0;(h=g(v))>=0;)O+=h,_.push(c(h));var P=new Uint8Array(O),S=0;for(i=0;i<_.length;++i)P.set(_[i],S),S+=_[i].length;return P}return c(h);case 3:var w=[];if(h<0)for(;(h=g(v))>=0;)y(w,h);else y(w,h);return String.fromCharCode.apply(null,w);case 4:var T;if(h<0)for(T=[];!f();)T.push(e());else for(T=new Array(h),i=0;i<h;++i)T[i]=e();return T;case 5:var k={};for(i=0;i<h||h<0&&!f();++i){k[e()]=e()}return k;case 6:return t(e(),h);case 7:switch(h){case 20:return!1;case 21:return!0;case 22:return null;case 23:return n;default:return o(h)}}}();if(a!==e.byteLength)throw"Remaining bytes";return b}};e.exports?e.exports=s:t.CBOR||(t.CBOR=s)}(c)}(l);var p=l.exports,h={exports:{}};
/*! lil-uuid - v0.1 - MIT License - https://github.com/lil-js/uuid */
!function(e,t){!function(e){var t="0.1.0",n={3:/^[0-9A-F]{8}-[0-9A-F]{4}-3[0-9A-F]{3}-[0-9A-F]{4}-[0-9A-F]{12}$/i,4:/^[0-9A-F]{8}-[0-9A-F]{4}-4[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i,5:/^[0-9A-F]{8}-[0-9A-F]{4}-5[0-9A-F]{3}-[89AB][0-9A-F]{3}-[0-9A-F]{12}$/i,all:/^[0-9A-F]{8}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{12}$/i};function r(){var e,t,n="";for(e=0;e<32;e++)t=16*Math.random()|0,8!==e&&12!==e&&16!==e&&20!==e||(n+="-"),n+=(12===e?4:16===e?3&t|8:t).toString(16);return n}function i(e,t){var r=n[t||"all"];return r&&r.test(e)||!1}r.isUUID=i,r.VERSION=t,e.uuid=r,e.isUUID=i}(t),null!==e&&(e.exports=t.uuid)}(h,h.exports);var f,d,g,y,b,v=h.exports,m=function(){return v.uuid?v.uuid():v()},_=function(){function e(e){var t,n,r,i=e.setup;if(this._PNSDKSuffix={},this.instanceId="pn-".concat(m()),this.secretKey=i.secretKey||i.secret_key,this.subscribeKey=i.subscribeKey||i.subscribe_key,this.publishKey=i.publishKey||i.publish_key,this.sdkName=i.sdkName,this.sdkFamily=i.sdkFamily,this.partnerId=i.partnerId,this.setAuthKey(i.authKey),this.setCipherKey(i.cipherKey),this.setFilterExpression(i.filterExpression),"string"!=typeof i.origin&&!Array.isArray(i.origin)&&void 0!==i.origin)throw new Error("Origin must be either undefined, a string or a list of strings.");this.origin=i.origin||Array.from({length:20},(function(e,t){return"ps".concat(t+1,".pndsn.com")})),this.secure=i.ssl||!1,this.restore=i.restore||!1,this.proxy=i.proxy,this.keepAlive=i.keepAlive,this.keepAliveSettings=i.keepAliveSettings,this.autoNetworkDetection=i.autoNetworkDetection||!1,this.dedupeOnSubscribe=i.dedupeOnSubscribe||!1,this.maximumCacheSize=i.maximumCacheSize||100,this.customEncrypt=i.customEncrypt,this.customDecrypt=i.customDecrypt,this.fileUploadPublishRetryLimit=null!==(t=i.fileUploadPublishRetryLimit)&&void 0!==t?t:5,this.useRandomIVs=null===(n=i.useRandomIVs)||void 0===n||n,this.enableSubscribeBeta=null!==(r=i.enableSubscribeBeta)&&void 0!==r&&r,"undefined"!=typeof location&&"https:"===location.protocol&&(this.secure=!0),this.logVerbosity=i.logVerbosity||!1,this.suppressLeaveEvents=i.suppressLeaveEvents||!1,this.announceFailedHeartbeats=i.announceFailedHeartbeats||!0,this.announceSuccessfulHeartbeats=i.announceSuccessfulHeartbeats||!1,this.useInstanceId=i.useInstanceId||!1,this.useRequestId=i.useRequestId||!1,this.requestMessageCountThreshold=i.requestMessageCountThreshold,this.setTransactionTimeout(i.transactionalRequestTimeout||15e3),this.setSubscribeTimeout(i.subscribeRequestTimeout||31e4),this.setSendBeaconConfig(i.useSendBeacon||!0),i.presenceTimeout?this.setPresenceTimeout(i.presenceTimeout):this._presenceTimeout=300,null!=i.heartbeatInterval&&this.setHeartbeatInterval(i.heartbeatInterval),this.setUUID(i.uuid)}return e.prototype.getAuthKey=function(){return this.authKey},e.prototype.setAuthKey=function(e){return this.authKey=e,this},e.prototype.setCipherKey=function(e){return this.cipherKey=e,this},e.prototype.getUUID=function(){return this.UUID},e.prototype.setUUID=function(e){if(!e||"string"!=typeof e||0===e.trim().length)throw new Error("Missing uuid parameter. Provide a valid string uuid");return this.UUID=e,this},e.prototype.getFilterExpression=function(){return this.filterExpression},e.prototype.setFilterExpression=function(e){return this.filterExpression=e,this},e.prototype.getPresenceTimeout=function(){return this._presenceTimeout},e.prototype.setPresenceTimeout=function(e){return e>=20?this._presenceTimeout=e:(this._presenceTimeout=20,console.log("WARNING: Presence timeout is less than the minimum. Using minimum value: ",this._presenceTimeout)),this.setHeartbeatInterval(this._presenceTimeout/2-1),this},e.prototype.setProxy=function(e){this.proxy=e},e.prototype.getHeartbeatInterval=function(){return this._heartbeatInterval},e.prototype.setHeartbeatInterval=function(e){return this._heartbeatInterval=e,this},e.prototype.getSubscribeTimeout=function(){return this._subscribeRequestTimeout},e.prototype.setSubscribeTimeout=function(e){return this._subscribeRequestTimeout=e,this},e.prototype.getTransactionTimeout=function(){return this._transactionalRequestTimeout},e.prototype.setTransactionTimeout=function(e){return this._transactionalRequestTimeout=e,this},e.prototype.isSendBeaconEnabled=function(){return this._useSendBeacon},e.prototype.setSendBeaconConfig=function(e){return this._useSendBeacon=e,this},e.prototype.getVersion=function(){return"7.1.2"},e.prototype._addPnsdkSuffix=function(e,t){this._PNSDKSuffix[e]=t},e.prototype._getPnsdkSuffix=function(e){var t=this;return Object.keys(this._PNSDKSuffix).reduce((function(n,r){return n+e+t._PNSDKSuffix[r]}),"")},e}(),O=O||function(e,t){var n={},r=n.lib={},i=function(){},o=r.Base={extend:function(e){i.prototype=this;var t=new i;return e&&t.mixIn(e),t.hasOwnProperty("init")||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},s=r.WordArray=o.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=null!=t?t:4*e.length},toString:function(e){return(e||u).stringify(this)},concat:function(e){var t=this.words,n=e.words,r=this.sigBytes;if(e=e.sigBytes,this.clamp(),r%4)for(var i=0;i<e;i++)t[r+i>>>2]|=(n[i>>>2]>>>24-i%4*8&255)<<24-(r+i)%4*8;else if(65535<n.length)for(i=0;i<e;i+=4)t[r+i>>>2]=n[i>>>2];else t.push.apply(t,n);return this.sigBytes+=e,this},clamp:function(){var t=this.words,n=this.sigBytes;t[n>>>2]&=4294967295<<32-n%4*8,t.length=e.ceil(n/4)},clone:function(){var e=o.clone.call(this);return e.words=this.words.slice(0),e},random:function(t){for(var n=[],r=0;r<t;r+=4)n.push(4294967296*e.random()|0);return new s.init(n,t)}}),a=n.enc={},u=a.Hex={stringify:function(e){var t=e.words;e=e.sigBytes;for(var n=[],r=0;r<e;r++){var i=t[r>>>2]>>>24-r%4*8&255;n.push((i>>>4).toString(16)),n.push((15&i).toString(16))}return n.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r+=2)n[r>>>3]|=parseInt(e.substr(r,2),16)<<24-r%8*4;return new s.init(n,t/2)}},c=a.Latin1={stringify:function(e){var t=e.words;e=e.sigBytes;for(var n=[],r=0;r<e;r++)n.push(String.fromCharCode(t[r>>>2]>>>24-r%4*8&255));return n.join("")},parse:function(e){for(var t=e.length,n=[],r=0;r<t;r++)n[r>>>2]|=(255&e.charCodeAt(r))<<24-r%4*8;return new s.init(n,t)}},l=a.Utf8={stringify:function(e){try{return decodeURIComponent(escape(c.stringify(e)))}catch(e){throw Error("Malformed UTF-8 data")}},parse:function(e){return c.parse(unescape(encodeURIComponent(e)))}},p=r.BufferedBlockAlgorithm=o.extend({reset:function(){this._data=new s.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=l.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var n=this._data,r=n.words,i=n.sigBytes,o=this.blockSize,a=i/(4*o);if(t=(a=t?e.ceil(a):e.max((0|a)-this._minBufferSize,0))*o,i=e.min(4*t,i),t){for(var u=0;u<t;u+=o)this._doProcessBlock(r,u);u=r.splice(0,t),n.sigBytes-=i}return new s.init(u,i)},clone:function(){var e=o.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0});r.Hasher=p.extend({cfg:o.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){p.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){return e&&this._append(e),this._doFinalize()},blockSize:16,_createHelper:function(e){return function(t,n){return new e.init(n).finalize(t)}},_createHmacHelper:function(e){return function(t,n){return new h.HMAC.init(e,n).finalize(t)}}});var h=n.algo={};return n}(Math);!function(e){for(var t=O,n=(i=t.lib).WordArray,r=i.Hasher,i=t.algo,o=[],s=[],a=function(e){return 4294967296*(e-(0|e))|0},u=2,c=0;64>c;){var l;e:{l=u;for(var p=e.sqrt(l),h=2;h<=p;h++)if(!(l%h)){l=!1;break e}l=!0}l&&(8>c&&(o[c]=a(e.pow(u,.5))),s[c]=a(e.pow(u,1/3)),c++),u++}var f=[];i=i.SHA256=r.extend({_doReset:function(){this._hash=new n.init(o.slice(0))},_doProcessBlock:function(e,t){for(var n=this._hash.words,r=n[0],i=n[1],o=n[2],a=n[3],u=n[4],c=n[5],l=n[6],p=n[7],h=0;64>h;h++){if(16>h)f[h]=0|e[t+h];else{var d=f[h-15],g=f[h-2];f[h]=((d<<25|d>>>7)^(d<<14|d>>>18)^d>>>3)+f[h-7]+((g<<15|g>>>17)^(g<<13|g>>>19)^g>>>10)+f[h-16]}d=p+((u<<26|u>>>6)^(u<<21|u>>>11)^(u<<7|u>>>25))+(u&c^~u&l)+s[h]+f[h],g=((r<<30|r>>>2)^(r<<19|r>>>13)^(r<<10|r>>>22))+(r&i^r&o^i&o),p=l,l=c,c=u,u=a+d|0,a=o,o=i,i=r,r=d+g|0}n[0]=n[0]+r|0,n[1]=n[1]+i|0,n[2]=n[2]+o|0,n[3]=n[3]+a|0,n[4]=n[4]+u|0,n[5]=n[5]+c|0,n[6]=n[6]+l|0,n[7]=n[7]+p|0},_doFinalize:function(){var t=this._data,n=t.words,r=8*this._nDataBytes,i=8*t.sigBytes;return n[i>>>5]|=128<<24-i%32,n[14+(i+64>>>9<<4)]=e.floor(r/4294967296),n[15+(i+64>>>9<<4)]=r,t.sigBytes=4*n.length,this._process(),this._hash},clone:function(){var e=r.clone.call(this);return e._hash=this._hash.clone(),e}});t.SHA256=r._createHelper(i),t.HmacSHA256=r._createHmacHelper(i)}(Math),d=(f=O).enc.Utf8,f.algo.HMAC=f.lib.Base.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=d.parse(t));var n=e.blockSize,r=4*n;t.sigBytes>r&&(t=e.finalize(t)),t.clamp();for(var i=this._oKey=t.clone(),o=this._iKey=t.clone(),s=i.words,a=o.words,u=0;u<n;u++)s[u]^=1549556828,a[u]^=909522486;i.sigBytes=o.sigBytes=r,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher;return e=t.finalize(e),t.reset(),t.finalize(this._oKey.clone().concat(e))}}),y=(g=O).lib.WordArray,g.enc.Base64={stringify:function(e){var t=e.words,n=e.sigBytes,r=this._map;e.clamp(),e=[];for(var i=0;i<n;i+=3)for(var o=(t[i>>>2]>>>24-i%4*8&255)<<16|(t[i+1>>>2]>>>24-(i+1)%4*8&255)<<8|t[i+2>>>2]>>>24-(i+2)%4*8&255,s=0;4>s&&i+.75*s<n;s++)e.push(r.charAt(o>>>6*(3-s)&63));if(t=r.charAt(64))for(;e.length%4;)e.push(t);return e.join("")},parse:function(e){var t=e.length,n=this._map;(r=n.charAt(64))&&-1!=(r=e.indexOf(r))&&(t=r);for(var r=[],i=0,o=0;o<t;o++)if(o%4){var s=n.indexOf(e.charAt(o-1))<<o%4*2,a=n.indexOf(e.charAt(o))>>>6-o%4*2;r[i>>>2]|=(s|a)<<24-i%4*8,i++}return y.create(r,i)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},function(e){function t(e,t,n,r,i,o,s){return((e=e+(t&n|~t&r)+i+s)<<o|e>>>32-o)+t}function n(e,t,n,r,i,o,s){return((e=e+(t&r|n&~r)+i+s)<<o|e>>>32-o)+t}function r(e,t,n,r,i,o,s){return((e=e+(t^n^r)+i+s)<<o|e>>>32-o)+t}function i(e,t,n,r,i,o,s){return((e=e+(n^(t|~r))+i+s)<<o|e>>>32-o)+t}for(var o=O,s=(u=o.lib).WordArray,a=u.Hasher,u=o.algo,c=[],l=0;64>l;l++)c[l]=4294967296*e.abs(e.sin(l+1))|0;u=u.MD5=a.extend({_doReset:function(){this._hash=new s.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,o){for(var s=0;16>s;s++){var a=e[u=o+s];e[u]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8)}s=this._hash.words;var u=e[o+0],l=(a=e[o+1],e[o+2]),p=e[o+3],h=e[o+4],f=e[o+5],d=e[o+6],g=e[o+7],y=e[o+8],b=e[o+9],v=e[o+10],m=e[o+11],_=e[o+12],O=e[o+13],P=e[o+14],S=e[o+15],w=t(w=s[0],N=s[1],k=s[2],T=s[3],u,7,c[0]),T=t(T,w,N,k,a,12,c[1]),k=t(k,T,w,N,l,17,c[2]),N=t(N,k,T,w,p,22,c[3]);w=t(w,N,k,T,h,7,c[4]),T=t(T,w,N,k,f,12,c[5]),k=t(k,T,w,N,d,17,c[6]),N=t(N,k,T,w,g,22,c[7]),w=t(w,N,k,T,y,7,c[8]),T=t(T,w,N,k,b,12,c[9]),k=t(k,T,w,N,v,17,c[10]),N=t(N,k,T,w,m,22,c[11]),w=t(w,N,k,T,_,7,c[12]),T=t(T,w,N,k,O,12,c[13]),k=t(k,T,w,N,P,17,c[14]),w=n(w,N=t(N,k,T,w,S,22,c[15]),k,T,a,5,c[16]),T=n(T,w,N,k,d,9,c[17]),k=n(k,T,w,N,m,14,c[18]),N=n(N,k,T,w,u,20,c[19]),w=n(w,N,k,T,f,5,c[20]),T=n(T,w,N,k,v,9,c[21]),k=n(k,T,w,N,S,14,c[22]),N=n(N,k,T,w,h,20,c[23]),w=n(w,N,k,T,b,5,c[24]),T=n(T,w,N,k,P,9,c[25]),k=n(k,T,w,N,p,14,c[26]),N=n(N,k,T,w,y,20,c[27]),w=n(w,N,k,T,O,5,c[28]),T=n(T,w,N,k,l,9,c[29]),k=n(k,T,w,N,g,14,c[30]),w=r(w,N=n(N,k,T,w,_,20,c[31]),k,T,f,4,c[32]),T=r(T,w,N,k,y,11,c[33]),k=r(k,T,w,N,m,16,c[34]),N=r(N,k,T,w,P,23,c[35]),w=r(w,N,k,T,a,4,c[36]),T=r(T,w,N,k,h,11,c[37]),k=r(k,T,w,N,g,16,c[38]),N=r(N,k,T,w,v,23,c[39]),w=r(w,N,k,T,O,4,c[40]),T=r(T,w,N,k,u,11,c[41]),k=r(k,T,w,N,p,16,c[42]),N=r(N,k,T,w,d,23,c[43]),w=r(w,N,k,T,b,4,c[44]),T=r(T,w,N,k,_,11,c[45]),k=r(k,T,w,N,S,16,c[46]),w=i(w,N=r(N,k,T,w,l,23,c[47]),k,T,u,6,c[48]),T=i(T,w,N,k,g,10,c[49]),k=i(k,T,w,N,P,15,c[50]),N=i(N,k,T,w,f,21,c[51]),w=i(w,N,k,T,_,6,c[52]),T=i(T,w,N,k,p,10,c[53]),k=i(k,T,w,N,v,15,c[54]),N=i(N,k,T,w,a,21,c[55]),w=i(w,N,k,T,y,6,c[56]),T=i(T,w,N,k,S,10,c[57]),k=i(k,T,w,N,d,15,c[58]),N=i(N,k,T,w,O,21,c[59]),w=i(w,N,k,T,h,6,c[60]),T=i(T,w,N,k,m,10,c[61]),k=i(k,T,w,N,l,15,c[62]),N=i(N,k,T,w,b,21,c[63]);s[0]=s[0]+w|0,s[1]=s[1]+N|0,s[2]=s[2]+k|0,s[3]=s[3]+T|0},_doFinalize:function(){var t=this._data,n=t.words,r=8*this._nDataBytes,i=8*t.sigBytes;n[i>>>5]|=128<<24-i%32;var o=e.floor(r/4294967296);for(n[15+(i+64>>>9<<4)]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),n[14+(i+64>>>9<<4)]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8),t.sigBytes=4*(n.length+1),this._process(),n=(t=this._hash).words,r=0;4>r;r++)i=n[r],n[r]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8);return t},clone:function(){var e=a.clone.call(this);return e._hash=this._hash.clone(),e}}),o.MD5=a._createHelper(u),o.HmacMD5=a._createHmacHelper(u)}(Math),function(){var e,t=O,n=(e=t.lib).Base,r=e.WordArray,i=(e=t.algo).EvpKDF=n.extend({cfg:n.extend({keySize:4,hasher:e.MD5,iterations:1}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var n=(a=this.cfg).hasher.create(),i=r.create(),o=i.words,s=a.keySize,a=a.iterations;o.length<s;){u&&n.update(u);var u=n.update(e).finalize(t);n.reset();for(var c=1;c<a;c++)u=n.finalize(u),n.reset();i.concat(u)}return i.sigBytes=4*s,i}});t.EvpKDF=function(e,t,n){return i.create(n).compute(e,t)}}(),O.lib.Cipher||function(e){var t=(f=O).lib,n=t.Base,r=t.WordArray,i=t.BufferedBlockAlgorithm,o=f.enc.Base64,s=f.algo.EvpKDF,a=t.Cipher=i.extend({cfg:n.extend(),createEncryptor:function(e,t){return this.create(this._ENC_XFORM_MODE,e,t)},createDecryptor:function(e,t){return this.create(this._DEC_XFORM_MODE,e,t)},init:function(e,t,n){this.cfg=this.cfg.extend(n),this._xformMode=e,this._key=t,this.reset()},reset:function(){i.reset.call(this),this._doReset()},process:function(e){return this._append(e),this._process()},finalize:function(e){return e&&this._append(e),this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(e){return{encrypt:function(t,n,r){return("string"==typeof n?d:h).encrypt(e,t,n,r)},decrypt:function(t,n,r){return("string"==typeof n?d:h).decrypt(e,t,n,r)}}}});t.StreamCipher=a.extend({_doFinalize:function(){return this._process(!0)},blockSize:1});var u=f.mode={},c=function(e,t,n){var r=this._iv;r?this._iv=undefined:r=this._prevBlock;for(var i=0;i<n;i++)e[t+i]^=r[i]},l=(t.BlockCipherMode=n.extend({createEncryptor:function(e,t){return this.Encryptor.create(e,t)},createDecryptor:function(e,t){return this.Decryptor.create(e,t)},init:function(e,t){this._cipher=e,this._iv=t}})).extend();l.Encryptor=l.extend({processBlock:function(e,t){var n=this._cipher,r=n.blockSize;c.call(this,e,t,r),n.encryptBlock(e,t),this._prevBlock=e.slice(t,t+r)}}),l.Decryptor=l.extend({processBlock:function(e,t){var n=this._cipher,r=n.blockSize,i=e.slice(t,t+r);n.decryptBlock(e,t),c.call(this,e,t,r),this._prevBlock=i}}),u=u.CBC=l,l=(f.pad={}).Pkcs7={pad:function(e,t){for(var n,i=(n=(n=4*t)-e.sigBytes%n)<<24|n<<16|n<<8|n,o=[],s=0;s<n;s+=4)o.push(i);n=r.create(o,n),e.concat(n)},unpad:function(e){e.sigBytes-=255&e.words[e.sigBytes-1>>>2]}},t.BlockCipher=a.extend({cfg:a.cfg.extend({mode:u,padding:l}),reset:function(){a.reset.call(this);var e=(t=this.cfg).iv,t=t.mode;if(this._xformMode==this._ENC_XFORM_MODE)var n=t.createEncryptor;else n=t.createDecryptor,this._minBufferSize=1;this._mode=n.call(t,this,e&&e.words)},_doProcessBlock:function(e,t){this._mode.processBlock(e,t)},_doFinalize:function(){var e=this.cfg.padding;if(this._xformMode==this._ENC_XFORM_MODE){e.pad(this._data,this.blockSize);var t=this._process(!0)}else t=this._process(!0),e.unpad(t);return t},blockSize:4});var p=t.CipherParams=n.extend({init:function(e){this.mixIn(e)},toString:function(e){return(e||this.formatter).stringify(this)}}),h=(u=(f.format={}).OpenSSL={stringify:function(e){var t=e.ciphertext;return((e=e.salt)?r.create([1398893684,1701076831]).concat(e).concat(t):t).toString(o)},parse:function(e){var t=(e=o.parse(e)).words;if(1398893684==t[0]&&1701076831==t[1]){var n=r.create(t.slice(2,4));t.splice(0,4),e.sigBytes-=16}return p.create({ciphertext:e,salt:n})}},t.SerializableCipher=n.extend({cfg:n.extend({format:u}),encrypt:function(e,t,n,r){r=this.cfg.extend(r);var i=e.createEncryptor(n,r);return t=i.finalize(t),i=i.cfg,p.create({ciphertext:t,key:n,iv:i.iv,algorithm:e,mode:i.mode,padding:i.padding,blockSize:e.blockSize,formatter:r.format})},decrypt:function(e,t,n,r){return r=this.cfg.extend(r),t=this._parse(t,r.format),e.createDecryptor(n,r).finalize(t.ciphertext)},_parse:function(e,t){return"string"==typeof e?t.parse(e,this):e}})),f=(f.kdf={}).OpenSSL={execute:function(e,t,n,i){return i||(i=r.random(8)),e=s.create({keySize:t+n}).compute(e,i),n=r.create(e.words.slice(t),4*n),e.sigBytes=4*t,p.create({key:e,iv:n,salt:i})}},d=t.PasswordBasedCipher=h.extend({cfg:h.cfg.extend({kdf:f}),encrypt:function(e,t,n,r){return n=(r=this.cfg.extend(r)).kdf.execute(n,e.keySize,e.ivSize),r.iv=n.iv,(e=h.encrypt.call(this,e,t,n.key,r)).mixIn(n),e},decrypt:function(e,t,n,r){return r=this.cfg.extend(r),t=this._parse(t,r.format),n=r.kdf.execute(n,e.keySize,e.ivSize,t.salt),r.iv=n.iv,h.decrypt.call(this,e,t,n.key,r)}})}(),function(){for(var e=O,t=e.lib.BlockCipher,n=e.algo,r=[],i=[],o=[],s=[],a=[],u=[],c=[],l=[],p=[],h=[],f=[],d=0;256>d;d++)f[d]=128>d?d<<1:d<<1^283;var g=0,y=0;for(d=0;256>d;d++){var b=(b=y^y<<1^y<<2^y<<3^y<<4)>>>8^255&b^99;r[g]=b,i[b]=g;var v=f[g],m=f[v],_=f[m],P=257*f[b]^16843008*b;o[g]=P<<24|P>>>8,s[g]=P<<16|P>>>16,a[g]=P<<8|P>>>24,u[g]=P,P=16843009*_^65537*m^257*v^16843008*g,c[b]=P<<24|P>>>8,l[b]=P<<16|P>>>16,p[b]=P<<8|P>>>24,h[b]=P,g?(g=v^f[f[f[_^v]]],y^=f[f[y]]):g=y=1}var S=[0,1,2,4,8,16,32,64,128,27,54];n=n.AES=t.extend({_doReset:function(){for(var e=(n=this._key).words,t=n.sigBytes/4,n=4*((this._nRounds=t+6)+1),i=this._keySchedule=[],o=0;o<n;o++)if(o<t)i[o]=e[o];else{var s=i[o-1];o%t?6<t&&4==o%t&&(s=r[s>>>24]<<24|r[s>>>16&255]<<16|r[s>>>8&255]<<8|r[255&s]):(s=r[(s=s<<8|s>>>24)>>>24]<<24|r[s>>>16&255]<<16|r[s>>>8&255]<<8|r[255&s],s^=S[o/t|0]<<24),i[o]=i[o-t]^s}for(e=this._invKeySchedule=[],t=0;t<n;t++)o=n-t,s=t%4?i[o]:i[o-4],e[t]=4>t||4>=o?s:c[r[s>>>24]]^l[r[s>>>16&255]]^p[r[s>>>8&255]]^h[r[255&s]]},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._keySchedule,o,s,a,u,r)},decryptBlock:function(e,t){var n=e[t+1];e[t+1]=e[t+3],e[t+3]=n,this._doCryptBlock(e,t,this._invKeySchedule,c,l,p,h,i),n=e[t+1],e[t+1]=e[t+3],e[t+3]=n},_doCryptBlock:function(e,t,n,r,i,o,s,a){for(var u=this._nRounds,c=e[t]^n[0],l=e[t+1]^n[1],p=e[t+2]^n[2],h=e[t+3]^n[3],f=4,d=1;d<u;d++){var g=r[c>>>24]^i[l>>>16&255]^o[p>>>8&255]^s[255&h]^n[f++],y=r[l>>>24]^i[p>>>16&255]^o[h>>>8&255]^s[255&c]^n[f++],b=r[p>>>24]^i[h>>>16&255]^o[c>>>8&255]^s[255&l]^n[f++];h=r[h>>>24]^i[c>>>16&255]^o[l>>>8&255]^s[255&p]^n[f++],c=g,l=y,p=b}g=(a[c>>>24]<<24|a[l>>>16&255]<<16|a[p>>>8&255]<<8|a[255&h])^n[f++],y=(a[l>>>24]<<24|a[p>>>16&255]<<16|a[h>>>8&255]<<8|a[255&c])^n[f++],b=(a[p>>>24]<<24|a[h>>>16&255]<<16|a[c>>>8&255]<<8|a[255&l])^n[f++],h=(a[h>>>24]<<24|a[c>>>16&255]<<16|a[l>>>8&255]<<8|a[255&p])^n[f++],e[t]=g,e[t+1]=y,e[t+2]=b,e[t+3]=h},keySize:8});e.AES=t._createHelper(n)}(),O.mode.ECB=((b=O.lib.BlockCipherMode.extend()).Encryptor=b.extend({processBlock:function(e,t){this._cipher.encryptBlock(e,t)}}),b.Decryptor=b.extend({processBlock:function(e,t){this._cipher.decryptBlock(e,t)}}),b);var P=O;function S(e){var t,n=[];for(t=0;t<e.length;t+=1)n[t/4|0]|=e[t]<<24-8*t;return P.lib.WordArray.create(n,e.length)}var w=function(){function e(e){var t=e.config;this._config=t,this._iv="0123456789012345",this._allowedKeyEncodings=["hex","utf8","base64","binary"],this._allowedKeyLengths=[128,256],this._allowedModes=["ecb","cbc"],this._defaultOptions={encryptKey:!0,keyEncoding:"utf8",keyLength:256,mode:"cbc"}}return e.prototype.HMACSHA256=function(e){return P.HmacSHA256(e,this._config.secretKey).toString(P.enc.Base64)},e.prototype.SHA256=function(e){return P.SHA256(e).toString(P.enc.Hex)},e.prototype._parseOptions=function(e){var t=e||{};return t.hasOwnProperty("encryptKey")||(t.encryptKey=this._defaultOptions.encryptKey),t.hasOwnProperty("keyEncoding")||(t.keyEncoding=this._defaultOptions.keyEncoding),t.hasOwnProperty("keyLength")||(t.keyLength=this._defaultOptions.keyLength),t.hasOwnProperty("mode")||(t.mode=this._defaultOptions.mode),-1===this._allowedKeyEncodings.indexOf(t.keyEncoding.toLowerCase())&&(t.keyEncoding=this._defaultOptions.keyEncoding),-1===this._allowedKeyLengths.indexOf(parseInt(t.keyLength,10))&&(t.keyLength=this._defaultOptions.keyLength),-1===this._allowedModes.indexOf(t.mode.toLowerCase())&&(t.mode=this._defaultOptions.mode),t},e.prototype._decodeKey=function(e,t){return"base64"===t.keyEncoding?P.enc.Base64.parse(e):"hex"===t.keyEncoding?P.enc.Hex.parse(e):e},e.prototype._getPaddedKey=function(e,t){return e=this._decodeKey(e,t),t.encryptKey?P.enc.Utf8.parse(this.SHA256(e).slice(0,32)):e},e.prototype._getMode=function(e){return"ecb"===e.mode?P.mode.ECB:P.mode.CBC},e.prototype._getIV=function(e){return"cbc"===e.mode?P.enc.Utf8.parse(this._iv):null},e.prototype._getRandomIV=function(){return P.lib.WordArray.random(16)},e.prototype.encrypt=function(e,t,n){return this._config.customEncrypt?this._config.customEncrypt(e):this.pnEncrypt(e,t,n)},e.prototype.decrypt=function(e,t,n){return this._config.customDecrypt?this._config.customDecrypt(e):this.pnDecrypt(e,t,n)},e.prototype.pnEncrypt=function(e,t,n){if(!t&&!this._config.cipherKey)return e;n=this._parseOptions(n);var r=this._getMode(n),i=this._getPaddedKey(t||this._config.cipherKey,n);if(this._config.useRandomIVs){var o=this._getRandomIV(),s=P.AES.encrypt(e,i,{iv:o,mode:r}).ciphertext;return o.clone().concat(s.clone()).toString(P.enc.Base64)}var a=this._getIV(n);return P.AES.encrypt(e,i,{iv:a,mode:r}).ciphertext.toString(P.enc.Base64)||e},e.prototype.pnDecrypt=function(e,t,n){if(!t&&!this._config.cipherKey)return e;n=this._parseOptions(n);var r=this._getMode(n),i=this._getPaddedKey(t||this._config.cipherKey,n);if(this._config.useRandomIVs){var o=S((u=Buffer.from(e,"base64")).slice(0,16)),s=S(u.slice(16));try{var a=P.AES.decrypt({ciphertext:s},i,{iv:o,mode:r}).toString(P.enc.Utf8);return JSON.parse(a)}catch(e){return null}}else{o=this._getIV(n);try{var u=P.enc.Base64.parse(e);a=P.AES.decrypt({ciphertext:u},i,{iv:o,mode:r}).toString(P.enc.Utf8);return JSON.parse(a)}catch(e){return null}}},e}(),T=function(){function e(e){var t=e.timeEndpoint;this._timeEndpoint=t}return e.prototype.onReconnection=function(e){this._reconnectionCallback=e},e.prototype.startPolling=function(){this._timeTimer=setInterval(this._performTimeLoop.bind(this),3e3)},e.prototype.stopPolling=function(){clearInterval(this._timeTimer)},e.prototype._performTimeLoop=function(){var e=this;this._timeEndpoint((function(t){t.error||(clearInterval(e._timeTimer),e._reconnectionCallback())}))},e}(),k=function(){function e(e){var t=e.config;this.hashHistory=[],this._config=t}return e.prototype.getKey=function(e){var t=function(e){var t=0;if(0===e.length)return t;for(var n=0;n<e.length;n+=1)t=(t<<5)-t+e.charCodeAt(n),t&=t;return t}(JSON.stringify(e.payload)).toString(),n=e.publishMetaData.publishTimetoken;return"".concat(n,"-").concat(t)},e.prototype.isDuplicate=function(e){return this.hashHistory.includes(this.getKey(e))},e.prototype.addEntry=function(e){this.hashHistory.length>=this._config.maximumCacheSize&&this.hashHistory.shift(),this.hashHistory.push(this.getKey(e))},e.prototype.clearHistory=function(){this.hashHistory=[]},e}();function N(e){return encodeURIComponent(e).replace(/[!~*'()]/g,(function(e){return"%".concat(e.charCodeAt(0).toString(16).toUpperCase())}))}function C(e){return function(e){var t=[];return Object.keys(e).forEach((function(e){return t.push(e)})),t}(e).sort()}var E={signPamFromParams:function(e){return C(e).map((function(t){return"".concat(t,"=").concat(N(e[t]))})).join("&")},endsWith:function(e,t){return-1!==e.indexOf(t,this.length-t.length)},createPromise:function(){var e,t;return{promise:new Promise((function(n,r){e=n,t=r})),reject:t,fulfill:e}},encodeString:N},A={PNNetworkUpCategory:"PNNetworkUpCategory",PNNetworkDownCategory:"PNNetworkDownCategory",PNNetworkIssuesCategory:"PNNetworkIssuesCategory",PNTimeoutCategory:"PNTimeoutCategory",PNBadRequestCategory:"PNBadRequestCategory",PNAccessDeniedCategory:"PNAccessDeniedCategory",PNUnknownCategory:"PNUnknownCategory",PNReconnectedCategory:"PNReconnectedCategory",PNConnectedCategory:"PNConnectedCategory",PNRequestMessageCountExceededCategory:"PNRequestMessageCountExceededCategory"},M=function(){function e(e){var t=e.subscribeEndpoint,n=e.leaveEndpoint,r=e.heartbeatEndpoint,i=e.setStateEndpoint,o=e.timeEndpoint,s=e.getFileUrl,a=e.config,u=e.crypto,c=e.listenerManager;this._listenerManager=c,this._config=a,this._leaveEndpoint=n,this._heartbeatEndpoint=r,this._setStateEndpoint=i,this._subscribeEndpoint=t,this._getFileUrl=s,this._crypto=u,this._channels={},this._presenceChannels={},this._heartbeatChannels={},this._heartbeatChannelGroups={},this._channelGroups={},this._presenceChannelGroups={},this._pendingChannelSubscriptions=[],this._pendingChannelGroupSubscriptions=[],this._currentTimetoken=0,this._lastTimetoken=0,this._storedTimetoken=null,this._subscriptionStatusAnnounced=!1,this._isOnline=!0,this._reconnectionManager=new T({timeEndpoint:o}),this._dedupingManager=new k({config:a})}return e.prototype.adaptStateChange=function(e,t){var n=this,r=e.state,i=e.channels,o=void 0===i?[]:i,s=e.channelGroups,a=void 0===s?[]:s;return o.forEach((function(e){e in n._channels&&(n._channels[e].state=r)})),a.forEach((function(e){e in n._channelGroups&&(n._channelGroups[e].state=r)})),this._setStateEndpoint({state:r,channels:o,channelGroups:a},t)},e.prototype.adaptPresenceChange=function(e){var t=this,n=e.connected,r=e.channels,i=void 0===r?[]:r,o=e.channelGroups,s=void 0===o?[]:o;n?(i.forEach((function(e){t._heartbeatChannels[e]={state:{}}})),s.forEach((function(e){t._heartbeatChannelGroups[e]={state:{}}}))):(i.forEach((function(e){e in t._heartbeatChannels&&delete t._heartbeatChannels[e]})),s.forEach((function(e){e in t._heartbeatChannelGroups&&delete t._heartbeatChannelGroups[e]})),!1===this._config.suppressLeaveEvents&&this._leaveEndpoint({channels:i,channelGroups:s},(function(e){t._listenerManager.announceStatus(e)}))),this.reconnect()},e.prototype.adaptSubscribeChange=function(e){var t=this,n=e.timetoken,r=e.channels,i=void 0===r?[]:r,o=e.channelGroups,s=void 0===o?[]:o,a=e.withPresence,u=void 0!==a&&a,c=e.withHeartbeats,l=void 0!==c&&c;this._config.subscribeKey&&""!==this._config.subscribeKey?(n&&(this._lastTimetoken=this._currentTimetoken,this._currentTimetoken=n),"0"!==this._currentTimetoken&&0!==this._currentTimetoken&&(this._storedTimetoken=this._currentTimetoken,this._currentTimetoken=0),i.forEach((function(e){t._channels[e]={state:{}},u&&(t._presenceChannels[e]={}),(l||t._config.getHeartbeatInterval())&&(t._heartbeatChannels[e]={}),t._pendingChannelSubscriptions.push(e)})),s.forEach((function(e){t._channelGroups[e]={state:{}},u&&(t._presenceChannelGroups[e]={}),(l||t._config.getHeartbeatInterval())&&(t._heartbeatChannelGroups[e]={}),t._pendingChannelGroupSubscriptions.push(e)})),this._subscriptionStatusAnnounced=!1,this.reconnect()):console&&console.log&&console.log("subscribe key missing; aborting subscribe")},e.prototype.adaptUnsubscribeChange=function(e,t){var n=this,r=e.channels,i=void 0===r?[]:r,o=e.channelGroups,s=void 0===o?[]:o,a=[],u=[];i.forEach((function(e){e in n._channels&&(delete n._channels[e],a.push(e),e in n._heartbeatChannels&&delete n._heartbeatChannels[e]),e in n._presenceChannels&&(delete n._presenceChannels[e],a.push(e))})),s.forEach((function(e){e in n._channelGroups&&(delete n._channelGroups[e],u.push(e),e in n._heartbeatChannelGroups&&delete n._heartbeatChannelGroups[e]),e in n._presenceChannelGroups&&(delete n._presenceChannelGroups[e],u.push(e))})),0===a.length&&0===u.length||(!1!==this._config.suppressLeaveEvents||t||this._leaveEndpoint({channels:a,channelGroups:u},(function(e){e.affectedChannels=a,e.affectedChannelGroups=u,e.currentTimetoken=n._currentTimetoken,e.lastTimetoken=n._lastTimetoken,n._listenerManager.announceStatus(e)})),0===Object.keys(this._channels).length&&0===Object.keys(this._presenceChannels).length&&0===Object.keys(this._channelGroups).length&&0===Object.keys(this._presenceChannelGroups).length&&(this._lastTimetoken=0,this._currentTimetoken=0,this._storedTimetoken=null,this._region=null,this._reconnectionManager.stopPolling()),this.reconnect())},e.prototype.unsubscribeAll=function(e){this.adaptUnsubscribeChange({channels:this.getSubscribedChannels(),channelGroups:this.getSubscribedChannelGroups()},e)},e.prototype.getHeartbeatChannels=function(){return Object.keys(this._heartbeatChannels)},e.prototype.getHeartbeatChannelGroups=function(){return Object.keys(this._heartbeatChannelGroups)},e.prototype.getSubscribedChannels=function(){return Object.keys(this._channels)},e.prototype.getSubscribedChannelGroups=function(){return Object.keys(this._channelGroups)},e.prototype.reconnect=function(){this._startSubscribeLoop(),this._registerHeartbeatTimer()},e.prototype.disconnect=function(){this._stopSubscribeLoop(),this._stopHeartbeatTimer(),this._reconnectionManager.stopPolling()},e.prototype._registerHeartbeatTimer=function(){this._stopHeartbeatTimer(),0!==this._config.getHeartbeatInterval()&&void 0!==this._config.getHeartbeatInterval()&&(this._performHeartbeatLoop(),this._heartbeatTimer=setInterval(this._performHeartbeatLoop.bind(this),1e3*this._config.getHeartbeatInterval()))},e.prototype._stopHeartbeatTimer=function(){this._heartbeatTimer&&(clearInterval(this._heartbeatTimer),this._heartbeatTimer=null)},e.prototype._performHeartbeatLoop=function(){var e=this,t=this.getHeartbeatChannels(),n=this.getHeartbeatChannelGroups(),r={};if(0!==t.length||0!==n.length){this.getSubscribedChannels().forEach((function(t){var n=e._channels[t].state;Object.keys(n).length&&(r[t]=n)})),this.getSubscribedChannelGroups().forEach((function(t){var n=e._channelGroups[t].state;Object.keys(n).length&&(r[t]=n)}));this._heartbeatEndpoint({channels:t,channelGroups:n,state:r},function(t){t.error&&e._config.announceFailedHeartbeats&&e._listenerManager.announceStatus(t),t.error&&e._config.autoNetworkDetection&&e._isOnline&&(e._isOnline=!1,e.disconnect(),e._listenerManager.announceNetworkDown(),e.reconnect()),!t.error&&e._config.announceSuccessfulHeartbeats&&e._listenerManager.announceStatus(t)}.bind(this))}},e.prototype._startSubscribeLoop=function(){var e=this;this._stopSubscribeLoop();var t={},n=[],r=[];if(Object.keys(this._channels).forEach((function(r){var i=e._channels[r].state;Object.keys(i).length&&(t[r]=i),n.push(r)})),Object.keys(this._presenceChannels).forEach((function(e){n.push("".concat(e,"-pnpres"))})),Object.keys(this._channelGroups).forEach((function(n){var i=e._channelGroups[n].state;Object.keys(i).length&&(t[n]=i),r.push(n)})),Object.keys(this._presenceChannelGroups).forEach((function(e){r.push("".concat(e,"-pnpres"))})),0!==n.length||0!==r.length){var i={channels:n,channelGroups:r,state:t,timetoken:this._currentTimetoken,filterExpression:this._config.filterExpression,region:this._region};this._subscribeCall=this._subscribeEndpoint(i,this._processSubscribeResponse.bind(this))}},e.prototype._processSubscribeResponse=function(e,t){var i=this;if(e.error){if(e.errorData&&"Aborted"===e.errorData.message)return;e.category===A.PNTimeoutCategory?this._startSubscribeLoop():e.category===A.PNNetworkIssuesCategory?(this.disconnect(),e.error&&this._config.autoNetworkDetection&&this._isOnline&&(this._isOnline=!1,this._listenerManager.announceNetworkDown()),this._reconnectionManager.onReconnection((function(){i._config.autoNetworkDetection&&!i._isOnline&&(i._isOnline=!0,i._listenerManager.announceNetworkUp()),i.reconnect(),i._subscriptionStatusAnnounced=!0;var t={category:A.PNReconnectedCategory,operation:e.operation,lastTimetoken:i._lastTimetoken,currentTimetoken:i._currentTimetoken};i._listenerManager.announceStatus(t)})),this._reconnectionManager.startPolling(),this._listenerManager.announceStatus(e)):e.category===A.PNBadRequestCategory?(this._stopHeartbeatTimer(),this._listenerManager.announceStatus(e)):this._listenerManager.announceStatus(e)}else{if(this._storedTimetoken?(this._currentTimetoken=this._storedTimetoken,this._storedTimetoken=null):(this._lastTimetoken=this._currentTimetoken,this._currentTimetoken=t.metadata.timetoken),!this._subscriptionStatusAnnounced){var o={};o.category=A.PNConnectedCategory,o.operation=e.operation,o.affectedChannels=this._pendingChannelSubscriptions,o.subscribedChannels=this.getSubscribedChannels(),o.affectedChannelGroups=this._pendingChannelGroupSubscriptions,o.lastTimetoken=this._lastTimetoken,o.currentTimetoken=this._currentTimetoken,this._subscriptionStatusAnnounced=!0,this._listenerManager.announceStatus(o),this._pendingChannelSubscriptions=[],this._pendingChannelGroupSubscriptions=[]}var s=t.messages||[],a=this._config,u=a.requestMessageCountThreshold,c=a.dedupeOnSubscribe;if(u&&s.length>=u){var l={};l.category=A.PNRequestMessageCountExceededCategory,l.operation=e.operation,this._listenerManager.announceStatus(l)}s.forEach((function(e){var t=e.channel,o=e.subscriptionMatch,s=e.publishMetaData;if(t===o&&(o=null),c){if(i._dedupingManager.isDuplicate(e))return;i._dedupingManager.addEntry(e)}if(E.endsWith(e.channel,"-pnpres"))(g={channel:null,subscription:null}).actualChannel=null!=o?t:null,g.subscribedChannel=null!=o?o:t,t&&(g.channel=t.substring(0,t.lastIndexOf("-pnpres"))),o&&(g.subscription=o.substring(0,o.lastIndexOf("-pnpres"))),g.action=e.payload.action,g.state=e.payload.data,g.timetoken=s.publishTimetoken,g.occupancy=e.payload.occupancy,g.uuid=e.payload.uuid,g.timestamp=e.payload.timestamp,e.payload.join&&(g.join=e.payload.join),e.payload.leave&&(g.leave=e.payload.leave),e.payload.timeout&&(g.timeout=e.payload.timeout),i._listenerManager.announcePresence(g);else if(1===e.messageType){(g={channel:null,subscription:null}).channel=t,g.subscription=o,g.timetoken=s.publishTimetoken,g.publisher=e.issuingClientId,e.userMetadata&&(g.userMetadata=e.userMetadata),g.message=e.payload,i._listenerManager.announceSignal(g)}else if(2===e.messageType){if((g={channel:null,subscription:null}).channel=t,g.subscription=o,g.timetoken=s.publishTimetoken,g.publisher=e.issuingClientId,e.userMetadata&&(g.userMetadata=e.userMetadata),g.message={event:e.payload.event,type:e.payload.type,data:e.payload.data},i._listenerManager.announceObjects(g),"uuid"===e.payload.type){var a=i._renameChannelField(g);i._listenerManager.announceUser(n(n({},a),{message:n(n({},a.message),{event:i._renameEvent(a.message.event),type:"user"})}))}else if("channel"===e.payload.type){a=i._renameChannelField(g);i._listenerManager.announceSpace(n(n({},a),{message:n(n({},a.message),{event:i._renameEvent(a.message.event),type:"space"})}))}else if("membership"===e.payload.type){var u=(a=i._renameChannelField(g)).message.data,l=u.uuid,p=u.channel,h=r(u,["uuid","channel"]);h.user=l,h.space=p,i._listenerManager.announceMembership(n(n({},a),{message:n(n({},a.message),{event:i._renameEvent(a.message.event),data:h})}))}}else if(3===e.messageType){(g={}).channel=t,g.subscription=o,g.timetoken=s.publishTimetoken,g.publisher=e.issuingClientId,g.data={messageTimetoken:e.payload.data.messageTimetoken,actionTimetoken:e.payload.data.actionTimetoken,type:e.payload.data.type,uuid:e.issuingClientId,value:e.payload.data.value},g.event=e.payload.event,i._listenerManager.announceMessageAction(g)}else if(4===e.messageType){(g={}).channel=t,g.subscription=o,g.timetoken=s.publishTimetoken,g.publisher=e.issuingClientId;var f=e.payload;if(i._config.cipherKey){var d=i._crypto.decrypt(e.payload);"object"==typeof d&&null!==d&&(f=d)}e.userMetadata&&(g.userMetadata=e.userMetadata),g.message=f.message,g.file={id:f.file.id,name:f.file.name,url:i._getFileUrl({id:f.file.id,name:f.file.name,channel:t})},i._listenerManager.announceFile(g)}else{var g;(g={channel:null,subscription:null}).actualChannel=null!=o?t:null,g.subscribedChannel=null!=o?o:t,g.channel=t,g.subscription=o,g.timetoken=s.publishTimetoken,g.publisher=e.issuingClientId,e.userMetadata&&(g.userMetadata=e.userMetadata),i._config.cipherKey?g.message=i._crypto.decrypt(e.payload):g.message=e.payload,i._listenerManager.announceMessage(g)}})),this._region=t.metadata.region,this._startSubscribeLoop()}},e.prototype._stopSubscribeLoop=function(){this._subscribeCall&&("function"==typeof this._subscribeCall.abort&&this._subscribeCall.abort(),this._subscribeCall=null)},e.prototype._renameEvent=function(e){return"set"===e?"updated":"removed"},e.prototype._renameChannelField=function(e){var t=e.channel,n=r(e,["channel"]);return n.spaceId=t,n},e}(),j={PNTimeOperation:"PNTimeOperation",PNHistoryOperation:"PNHistoryOperation",PNDeleteMessagesOperation:"PNDeleteMessagesOperation",PNFetchMessagesOperation:"PNFetchMessagesOperation",PNMessageCounts:"PNMessageCountsOperation",PNSubscribeOperation:"PNSubscribeOperation",PNUnsubscribeOperation:"PNUnsubscribeOperation",PNPublishOperation:"PNPublishOperation",PNSignalOperation:"PNSignalOperation",PNAddMessageActionOperation:"PNAddActionOperation",PNRemoveMessageActionOperation:"PNRemoveMessageActionOperation",PNGetMessageActionsOperation:"PNGetMessageActionsOperation",PNCreateUserOperation:"PNCreateUserOperation",PNUpdateUserOperation:"PNUpdateUserOperation",PNDeleteUserOperation:"PNDeleteUserOperation",PNGetUserOperation:"PNGetUsersOperation",PNGetUsersOperation:"PNGetUsersOperation",PNCreateSpaceOperation:"PNCreateSpaceOperation",PNUpdateSpaceOperation:"PNUpdateSpaceOperation",PNDeleteSpaceOperation:"PNDeleteSpaceOperation",PNGetSpaceOperation:"PNGetSpacesOperation",PNGetSpacesOperation:"PNGetSpacesOperation",PNGetMembersOperation:"PNGetMembersOperation",PNUpdateMembersOperation:"PNUpdateMembersOperation",PNGetMembershipsOperation:"PNGetMembershipsOperation",PNUpdateMembershipsOperation:"PNUpdateMembershipsOperation",PNListFilesOperation:"PNListFilesOperation",PNGenerateUploadUrlOperation:"PNGenerateUploadUrlOperation",PNPublishFileOperation:"PNPublishFileOperation",PNGetFileUrlOperation:"PNGetFileUrlOperation",PNDownloadFileOperation:"PNDownloadFileOperation",PNGetAllUUIDMetadataOperation:"PNGetAllUUIDMetadataOperation",PNGetUUIDMetadataOperation:"PNGetUUIDMetadataOperation",PNSetUUIDMetadataOperation:"PNSetUUIDMetadataOperation",PNRemoveUUIDMetadataOperation:"PNRemoveUUIDMetadataOperation",PNGetAllChannelMetadataOperation:"PNGetAllChannelMetadataOperation",PNGetChannelMetadataOperation:"PNGetChannelMetadataOperation",PNSetChannelMetadataOperation:"PNSetChannelMetadataOperation",PNRemoveChannelMetadataOperation:"PNRemoveChannelMetadataOperation",PNSetMembersOperation:"PNSetMembersOperation",PNSetMembershipsOperation:"PNSetMembershipsOperation",PNPushNotificationEnabledChannelsOperation:"PNPushNotificationEnabledChannelsOperation",PNRemoveAllPushNotificationsOperation:"PNRemoveAllPushNotificationsOperation",PNWhereNowOperation:"PNWhereNowOperation",PNSetStateOperation:"PNSetStateOperation",PNHereNowOperation:"PNHereNowOperation",PNGetStateOperation:"PNGetStateOperation",PNHeartbeatOperation:"PNHeartbeatOperation",PNChannelGroupsOperation:"PNChannelGroupsOperation",PNRemoveGroupOperation:"PNRemoveGroupOperation",PNChannelsForGroupOperation:"PNChannelsForGroupOperation",PNAddChannelsToGroupOperation:"PNAddChannelsToGroupOperation",PNRemoveChannelsFromGroupOperation:"PNRemoveChannelsFromGroupOperation",PNAccessManagerGrant:"PNAccessManagerGrant",PNAccessManagerGrantToken:"PNAccessManagerGrantToken",PNAccessManagerAudit:"PNAccessManagerAudit",PNAccessManagerRevokeToken:"PNAccessManagerRevokeToken",PNHandshakeOperation:"PNHandshakeOperation",PNReceiveMessagesOperation:"PNReceiveMessagesOperation"},R=function(){function e(e){this._maximumSamplesCount=100,this._trackedLatencies={},this._latencies={},this._maximumSamplesCount=e.maximumSamplesCount||this._maximumSamplesCount}return e.prototype.operationsLatencyForRequest=function(){var e=this,t={};return Object.keys(this._latencies).forEach((function(n){var r=e._latencies[n],i=e._averageLatency(r);i>0&&(t["l_".concat(n)]=i)})),t},e.prototype.startLatencyMeasure=function(e,t){e!==j.PNSubscribeOperation&&t&&(this._trackedLatencies[t]=Date.now())},e.prototype.stopLatencyMeasure=function(e,t){if(e!==j.PNSubscribeOperation&&t){var n=this._endpointName(e),r=this._latencies[n],i=this._trackedLatencies[t];r||(this._latencies[n]=[],r=this._latencies[n]),r.push(Date.now()-i),r.length>this._maximumSamplesCount&&r.splice(0,r.length-this._maximumSamplesCount),delete this._trackedLatencies[t]}},e.prototype._averageLatency=function(e){return Math.floor(e.reduce((function(e,t){return e+t}),0)/e.length)},e.prototype._endpointName=function(e){var t=null;switch(e){case j.PNPublishOperation:t="pub";break;case j.PNSignalOperation:t="sig";break;case j.PNHistoryOperation:case j.PNFetchMessagesOperation:case j.PNDeleteMessagesOperation:case j.PNMessageCounts:t="hist";break;case j.PNUnsubscribeOperation:case j.PNWhereNowOperation:case j.PNHereNowOperation:case j.PNHeartbeatOperation:case j.PNSetStateOperation:case j.PNGetStateOperation:t="pres";break;case j.PNAddChannelsToGroupOperation:case j.PNRemoveChannelsFromGroupOperation:case j.PNChannelGroupsOperation:case j.PNRemoveGroupOperation:case j.PNChannelsForGroupOperation:t="cg";break;case j.PNPushNotificationEnabledChannelsOperation:case j.PNRemoveAllPushNotificationsOperation:t="push";break;case j.PNCreateUserOperation:case j.PNUpdateUserOperation:case j.PNDeleteUserOperation:case j.PNGetUserOperation:case j.PNGetUsersOperation:case j.PNCreateSpaceOperation:case j.PNUpdateSpaceOperation:case j.PNDeleteSpaceOperation:case j.PNGetSpaceOperation:case j.PNGetSpacesOperation:case j.PNGetMembersOperation:case j.PNUpdateMembersOperation:case j.PNGetMembershipsOperation:case j.PNUpdateMembershipsOperation:t="obj";break;case j.PNAddMessageActionOperation:case j.PNRemoveMessageActionOperation:case j.PNGetMessageActionsOperation:t="msga";break;case j.PNAccessManagerGrant:case j.PNAccessManagerAudit:t="pam";break;case j.PNAccessManagerGrantToken:case j.PNAccessManagerRevokeToken:t="pamv3";break;default:t="time"}return t},e}(),U=function(){function e(e,t,n){this._payload=e,this._setDefaultPayloadStructure(),this.title=t,this.body=n}return Object.defineProperty(e.prototype,"payload",{get:function(){return this._payload},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"title",{set:function(e){this._title=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"subtitle",{set:function(e){this._subtitle=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"body",{set:function(e){this._body=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"badge",{set:function(e){this._badge=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"sound",{set:function(e){this._sound=e},enumerable:!1,configurable:!0}),e.prototype._setDefaultPayloadStructure=function(){},e.prototype.toObject=function(){return{}},e}(),x=function(e){function r(){return null!==e&&e.apply(this,arguments)||this}return t(r,e),Object.defineProperty(r.prototype,"configurations",{set:function(e){e&&e.length&&(this._configurations=e)},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"notification",{get:function(){return this._payload.aps},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"title",{get:function(){return this._title},set:function(e){e&&e.length&&(this._payload.aps.alert.title=e,this._title=e)},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"subtitle",{get:function(){return this._subtitle},set:function(e){e&&e.length&&(this._payload.aps.alert.subtitle=e,this._subtitle=e)},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"body",{get:function(){return this._body},set:function(e){e&&e.length&&(this._payload.aps.alert.body=e,this._body=e)},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"badge",{get:function(){return this._badge},set:function(e){null!=e&&(this._payload.aps.badge=e,this._badge=e)},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"sound",{get:function(){return this._sound},set:function(e){e&&e.length&&(this._payload.aps.sound=e,this._sound=e)},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"silent",{set:function(e){this._isSilent=e},enumerable:!1,configurable:!0}),r.prototype._setDefaultPayloadStructure=function(){this._payload.aps={alert:{}}},r.prototype.toObject=function(){var e=this,t=n({},this._payload),r=t.aps,i=r.alert;if(this._isSilent&&(r["content-available"]=1),"apns2"===this._apnsPushType){if(!this._configurations||!this._configurations.length)throw new ReferenceError("APNS2 configuration is missing");var o=[];this._configurations.forEach((function(t){o.push(e._objectFromAPNS2Configuration(t))})),o.length&&(t.pn_push=o)}return i&&Object.keys(i).length||delete r.alert,this._isSilent&&(delete r.alert,delete r.badge,delete r.sound,i={}),this._isSilent||Object.keys(i).length?t:null},r.prototype._objectFromAPNS2Configuration=function(e){var t=this;if(!e.targets||!e.targets.length)throw new ReferenceError("At least one APNS2 target should be provided");var n=[];e.targets.forEach((function(e){n.push(t._objectFromAPNSTarget(e))}));var r=e.collapseId,i=e.expirationDate,o={auth_method:"token",targets:n,version:"v2"};return r&&r.length&&(o.collapse_id=r),i&&(o.expiration=i.toISOString()),o},r.prototype._objectFromAPNSTarget=function(e){if(!e.topic||!e.topic.length)throw new TypeError("Target 'topic' undefined.");var t=e.topic,n=e.environment,r=void 0===n?"development":n,i=e.excludedDevices,o=void 0===i?[]:i,s={topic:t,environment:r};return o.length&&(s.excluded_devices=o),s},r}(U),I=function(e){function r(){return null!==e&&e.apply(this,arguments)||this}return t(r,e),Object.defineProperty(r.prototype,"backContent",{get:function(){return this._backContent},set:function(e){e&&e.length&&(this._payload.back_content=e,this._backContent=e)},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"backTitle",{get:function(){return this._backTitle},set:function(e){e&&e.length&&(this._payload.back_title=e,this._backTitle=e)},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"count",{get:function(){return this._count},set:function(e){null!=e&&(this._payload.count=e,this._count=e)},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"title",{get:function(){return this._title},set:function(e){e&&e.length&&(this._payload.title=e,this._title=e)},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"type",{get:function(){return this._type},set:function(e){e&&e.length&&(this._payload.type=e,this._type=e)},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"subtitle",{get:function(){return this.backTitle},set:function(e){this.backTitle=e},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"body",{get:function(){return this.backContent},set:function(e){this.backContent=e},enumerable:!1,configurable:!0}),Object.defineProperty(r.prototype,"badge",{get:function(){return this.count},set:function(e){this.count=e},enumerable:!1,configurable:!0}),r.prototype.toObject=function(){return Object.keys(this._payload).length?n({},this._payload):null},r}(U),D=function(e){function i(){return null!==e&&e.apply(this,arguments)||this}return t(i,e),Object.defineProperty(i.prototype,"notification",{get:function(){return this._payload.notification},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"data",{get:function(){return this._payload.data},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"title",{get:function(){return this._title},set:function(e){e&&e.length&&(this._payload.notification.title=e,this._title=e)},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"body",{get:function(){return this._body},set:function(e){e&&e.length&&(this._payload.notification.body=e,this._body=e)},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"sound",{get:function(){return this._sound},set:function(e){e&&e.length&&(this._payload.notification.sound=e,this._sound=e)},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"icon",{get:function(){return this._icon},set:function(e){e&&e.length&&(this._payload.notification.icon=e,this._icon=e)},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"tag",{get:function(){return this._tag},set:function(e){e&&e.length&&(this._payload.notification.tag=e,this._tag=e)},enumerable:!1,configurable:!0}),Object.defineProperty(i.prototype,"silent",{set:function(e){this._isSilent=e},enumerable:!1,configurable:!0}),i.prototype._setDefaultPayloadStructure=function(){this._payload.notification={},this._payload.data={}},i.prototype.toObject=function(){var e=n({},this._payload.data),t=null,i={};if(Object.keys(this._payload).length>2){var o=this._payload;o.notification,o.data;var s=r(o,["notification","data"]);e=n(n({},e),s)}return this._isSilent?e.notification=this._payload.notification:t=this._payload.notification,Object.keys(e).length&&(i.data=e),t&&Object.keys(t).length&&(i.notification=t),Object.keys(i).length?i:null},i}(U),G=function(){function e(e,t){this._payload={apns:{},mpns:{},fcm:{}},this._title=e,this._body=t,this.apns=new x(this._payload.apns,e,t),this.mpns=new I(this._payload.mpns,e,t),this.fcm=new D(this._payload.fcm,e,t)}return Object.defineProperty(e.prototype,"debugging",{set:function(e){this._debugging=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"title",{get:function(){return this._title},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"body",{get:function(){return this._body},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"subtitle",{get:function(){return this._subtitle},set:function(e){this._subtitle=e,this.apns.subtitle=e,this.mpns.subtitle=e,this.fcm.subtitle=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"badge",{get:function(){return this._badge},set:function(e){this._badge=e,this.apns.badge=e,this.mpns.badge=e,this.fcm.badge=e},enumerable:!1,configurable:!0}),Object.defineProperty(e.prototype,"sound",{get:function(){return this._sound},set:function(e){this._sound=e,this.apns.sound=e,this.mpns.sound=e,this.fcm.sound=e},enumerable:!1,configurable:!0}),e.prototype.buildPayload=function(e){var t={};if(e.includes("apns")||e.includes("apns2")){this.apns._apnsPushType=e.includes("apns")?"apns":"apns2";var n=this.apns.toObject();n&&Object.keys(n).length&&(t.pn_apns=n)}if(e.includes("mpns")){var r=this.mpns.toObject();r&&Object.keys(r).length&&(t.pn_mpns=r)}if(e.includes("fcm")){var i=this.fcm.toObject();i&&Object.keys(i).length&&(t.pn_gcm=i)}return Object.keys(t).length&&this._debugging&&(t.pn_debug=!0),t},e}(),K=function(){function e(){this._listeners=[]}return e.prototype.addListener=function(e){this._listeners.push(e)},e.prototype.removeListener=function(e){var t=[];this._listeners.forEach((function(n){n!==e&&t.push(n)})),this._listeners=t},e.prototype.removeAllListeners=function(){this._listeners=[]},e.prototype.announcePresence=function(e){this._listeners.forEach((function(t){t.presence&&t.presence(e)}))},e.prototype.announceStatus=function(e){this._listeners.forEach((function(t){t.status&&t.status(e)}))},e.prototype.announceMessage=function(e){this._listeners.forEach((function(t){t.message&&t.message(e)}))},e.prototype.announceSignal=function(e){this._listeners.forEach((function(t){t.signal&&t.signal(e)}))},e.prototype.announceMessageAction=function(e){this._listeners.forEach((function(t){t.messageAction&&t.messageAction(e)}))},e.prototype.announceFile=function(e){this._listeners.forEach((function(t){t.file&&t.file(e)}))},e.prototype.announceObjects=function(e){this._listeners.forEach((function(t){t.objects&&t.objects(e)}))},e.prototype.announceUser=function(e){this._listeners.forEach((function(t){t.user&&t.user(e)}))},e.prototype.announceSpace=function(e){this._listeners.forEach((function(t){t.space&&t.space(e)}))},e.prototype.announceMembership=function(e){this._listeners.forEach((function(t){t.membership&&t.membership(e)}))},e.prototype.announceNetworkUp=function(){var e={};e.category=A.PNNetworkUpCategory,this.announceStatus(e)},e.prototype.announceNetworkDown=function(){var e={};e.category=A.PNNetworkDownCategory,this.announceStatus(e)},e}(),F=function(){function e(e,t){this._config=e,this._cbor=t}return e.prototype.setToken=function(e){e&&e.length>0?this._token=e:this._token=void 0},e.prototype.getToken=function(){return this._token},e.prototype.extractPermissions=function(e){var t={read:!1,write:!1,manage:!1,delete:!1,get:!1,update:!1,join:!1};return 128==(128&e)&&(t.join=!0),64==(64&e)&&(t.update=!0),32==(32&e)&&(t.get=!0),8==(8&e)&&(t.delete=!0),4==(4&e)&&(t.manage=!0),2==(2&e)&&(t.write=!0),1==(1&e)&&(t.read=!0),t},e.prototype.parseToken=function(e){var t=this,n=this._cbor.decodeToken(e);if(void 0!==n){var r=n.res.uuid?Object.keys(n.res.uuid):[],i=Object.keys(n.res.chan),o=Object.keys(n.res.grp),s=n.pat.uuid?Object.keys(n.pat.uuid):[],a=Object.keys(n.pat.chan),u=Object.keys(n.pat.grp),c={version:n.v,timestamp:n.t,ttl:n.ttl,authorized_uuid:n.uuid},l=r.length>0,p=i.length>0,h=o.length>0;(l||p||h)&&(c.resources={},l&&(c.resources.uuids={},r.forEach((function(e){c.resources.uuids[e]=t.extractPermissions(n.res.uuid[e])}))),p&&(c.resources.channels={},i.forEach((function(e){c.resources.channels[e]=t.extractPermissions(n.res.chan[e])}))),h&&(c.resources.groups={},o.forEach((function(e){c.resources.groups[e]=t.extractPermissions(n.res.grp[e])}))));var f=s.length>0,d=a.length>0,g=u.length>0;return(f||d||g)&&(c.patterns={},f&&(c.patterns.uuids={},s.forEach((function(e){c.patterns.uuids[e]=t.extractPermissions(n.pat.uuid[e])}))),d&&(c.patterns.channels={},a.forEach((function(e){c.patterns.channels[e]=t.extractPermissions(n.pat.chan[e])}))),g&&(c.patterns.groups={},u.forEach((function(e){c.patterns.groups[e]=t.extractPermissions(n.pat.grp[e])})))),Object.keys(n.meta).length>0&&(c.meta=n.meta),c.signature=n.sig,c}},e}(),L=function(e){function n(t,n){var r=this.constructor,i=e.call(this,t)||this;return i.name=i.constructor.name,i.status=n,i.message=t,Object.setPrototypeOf(i,r.prototype),i}return t(n,e),n}(Error);function B(e){return(t={message:e}).type="validationError",t.error=!0,t;var t}function H(e,t,n){return e.usePost&&e.usePost(t,n)?e.postURL(t,n):e.usePatch&&e.usePatch(t,n)?e.patchURL(t,n):e.useGetFile&&e.useGetFile(t,n)?e.getFileURL(t,n):e.getURL(t,n)}function q(e){if(e.sdkName)return e.sdkName;var t="PubNub-JS-".concat(e.sdkFamily);e.partnerId&&(t+="-".concat(e.partnerId)),t+="/".concat(e.getVersion());var n=e._getPnsdkSuffix(" ");return n.length>0&&(t+=n),t}function z(e,t,n){return t.usePost&&t.usePost(e,n)?"POST":t.usePatch&&t.usePatch(e,n)?"PATCH":t.useDelete&&t.useDelete(e,n)?"DELETE":t.useGetFile&&t.useGetFile(e,n)?"GETFILE":"GET"}function V(e,t,n,r,i){var o=e.config,s=e.crypto,a=z(e,i,r);n.timestamp=Math.floor((new Date).getTime()/1e3),"PNPublishOperation"===i.getOperation()&&i.usePost&&i.usePost(e,r)&&(a="GET"),"GETFILE"===a&&(a="GET");var u="".concat(a,"\n").concat(o.publishKey,"\n").concat(t,"\n").concat(E.signPamFromParams(n),"\n");if("POST"===a)u+="string"==typeof(c=i.postPayload(e,r))?c:JSON.stringify(c);else if("PATCH"===a){var c;u+="string"==typeof(c=i.patchPayload(e,r))?c:JSON.stringify(c)}var l="v2.".concat(s.HMACSHA256(u));l=(l=(l=l.replace(/\+/g,"-")).replace(/\//g,"_")).replace(/=+$/,""),n.signature=l}function J(e,t){for(var r=[],i=2;i<arguments.length;i++)r[i-2]=arguments[i];var o=e.networking,s=e.config,a=e.telemetryManager,u=e.tokenManager,c=m(),l=null,p=null,h={};t.getOperation()===j.PNTimeOperation||t.getOperation()===j.PNChannelGroupsOperation?l=r[0]:(h=r[0],l=r[1]),"undefined"==typeof Promise||l||(p=E.createPromise());var f=t.validateParams(e,h);if(f)return l?l(B(f)):p?(p.reject(new L("Validation failed, check status for details",B(f))),p.promise):void 0;var d,g=t.prepareParams(e,h),y=H(t,e,h),b={url:y,operation:t.getOperation(),timeout:t.getRequestTimeout(e),headers:t.getRequestHeaders?t.getRequestHeaders():{},ignoreBody:"function"==typeof t.ignoreBody&&t.ignoreBody(e),forceBuffered:"function"==typeof t.forceBuffered?t.forceBuffered(e,h):null,abortSignal:"function"==typeof t.getAbortSignal?t.getAbortSignal(e,h):null};g.uuid=s.UUID,g.pnsdk=q(s);var v=a.operationsLatencyForRequest();if(Object.keys(v).length&&(g=n(n({},g),v)),s.useInstanceId&&(g.instanceid=s.instanceId),s.useRequestId&&(g.requestid=c),t.isAuthSupported()){var _=u.getToken()||s.getAuthKey();_&&(g.auth=_)}s.secretKey&&V(e,y,g,h,t);var O=function(n,r){if(n.error)return t.handleError&&t.handleError(e,h,n),void(l?l(n):p&&p.reject(new L("PubNub call failed, check status for details",n)));a.stopLatencyMeasure(t.getOperation(),c);var i=t.handleResponse(e,r,h);"function"!=typeof(null==i?void 0:i.then)&&(i=Promise.resolve(i)),i.then((function(e){l?l(n,e):p&&p.fulfill(e)})).catch((function(e){if(l){var n=e;t.getOperation()===j.PNSubscribeOperation&&(n={statusCode:400,error:!0,operation:t.getOperation(),errorData:e,category:A.PNUnknownCategory}),l(n,null)}else p&&p.reject(new L("PubNub call failed, check status for details",e))}))};if(a.startLatencyMeasure(t.getOperation(),c),"POST"===z(e,t,h)){var P=t.postPayload(e,h);d=o.POST(g,P,b,O)}else if("PATCH"===z(e,t,h)){P=t.patchPayload(e,h);d=o.PATCH(g,P,b,O)}else d="DELETE"===z(e,t,h)?o.DELETE(g,b,O):"GETFILE"===z(e,t,h)?o.GETFILE(g,b,O):o.GET(g,b,O);return t.getOperation()===j.PNSubscribeOperation?d:p?p.promise:void 0}var W=Object.freeze({__proto__:null,getOperation:function(){return j.PNAddChannelsToGroupOperation},validateParams:function(e,t){var n=t.channels,r=t.channelGroup,i=e.config;return r?n&&0!==n.length?i.subscribeKey?void 0:"Missing Subscribe Key":"Missing Channels":"Missing Channel Group"},getURL:function(e,t){var n=t.channelGroup,r=e.config;return"/v1/channel-registration/sub-key/".concat(r.subscribeKey,"/channel-group/").concat(E.encodeString(n))},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n=t.channels;return{add:(void 0===n?[]:n).join(",")}},handleResponse:function(){return{}}});var X=Object.freeze({__proto__:null,getOperation:function(){return j.PNRemoveChannelsFromGroupOperation},validateParams:function(e,t){var n=t.channels,r=t.channelGroup,i=e.config;return r?n&&0!==n.length?i.subscribeKey?void 0:"Missing Subscribe Key":"Missing Channels":"Missing Channel Group"},getURL:function(e,t){var n=t.channelGroup,r=e.config;return"/v1/channel-registration/sub-key/".concat(r.subscribeKey,"/channel-group/").concat(E.encodeString(n))},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n=t.channels;return{remove:(void 0===n?[]:n).join(",")}},handleResponse:function(){return{}}});var $=Object.freeze({__proto__:null,getOperation:function(){return j.PNRemoveGroupOperation},validateParams:function(e,t){var n=t.channelGroup,r=e.config;return n?r.subscribeKey?void 0:"Missing Subscribe Key":"Missing Channel Group"},getURL:function(e,t){var n=t.channelGroup,r=e.config;return"/v1/channel-registration/sub-key/".concat(r.subscribeKey,"/channel-group/").concat(E.encodeString(n),"/remove")},isAuthSupported:function(){return!0},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},prepareParams:function(){return{}},handleResponse:function(){return{}}});var Q=Object.freeze({__proto__:null,getOperation:function(){return j.PNChannelGroupsOperation},validateParams:function(e){if(!e.config.subscribeKey)return"Missing Subscribe Key"},getURL:function(e){var t=e.config;return"/v1/channel-registration/sub-key/".concat(t.subscribeKey,"/channel-group")},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(){return{}},handleResponse:function(e,t){return{groups:t.payload.groups}}});var Y=Object.freeze({__proto__:null,getOperation:function(){return j.PNChannelsForGroupOperation},validateParams:function(e,t){var n=t.channelGroup,r=e.config;return n?r.subscribeKey?void 0:"Missing Subscribe Key":"Missing Channel Group"},getURL:function(e,t){var n=t.channelGroup,r=e.config;return"/v1/channel-registration/sub-key/".concat(r.subscribeKey,"/channel-group/").concat(E.encodeString(n))},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(){return{}},handleResponse:function(e,t){return{channels:t.payload.channels}}});var Z=Object.freeze({__proto__:null,getOperation:function(){return j.PNPushNotificationEnabledChannelsOperation},validateParams:function(e,t){var n=t.device,r=t.pushGateway,i=t.channels,o=t.topic,s=e.config;return n?r?"apns2"!==r||o?i&&0!==i.length?s.subscribeKey?void 0:"Missing Subscribe Key":"Missing Channels":"Missing APNS2 topic":"Missing GW Type (pushGateway: gcm, apns or apns2)":"Missing Device ID (device)"},getURL:function(e,t){var n=t.device,r=t.pushGateway,i=e.config;return"apns2"===r?"/v2/push/sub-key/".concat(i.subscribeKey,"/devices-apns2/").concat(n):"/v1/push/sub-key/".concat(i.subscribeKey,"/devices/").concat(n)},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var r=t.pushGateway,i=t.channels,o=void 0===i?[]:i,s=t.environment,a=void 0===s?"development":s,u=t.topic,c={type:r,add:o.join(",")};return"apns2"===r&&delete(c=n(n({},c),{environment:a,topic:u})).type,c},handleResponse:function(){return{}}});var ee=Object.freeze({__proto__:null,getOperation:function(){return j.PNPushNotificationEnabledChannelsOperation},validateParams:function(e,t){var n=t.device,r=t.pushGateway,i=t.channels,o=t.topic,s=e.config;return n?r?"apns2"!==r||o?i&&0!==i.length?s.subscribeKey?void 0:"Missing Subscribe Key":"Missing Channels":"Missing APNS2 topic":"Missing GW Type (pushGateway: gcm, apns or apns2)":"Missing Device ID (device)"},getURL:function(e,t){var n=t.device,r=t.pushGateway,i=e.config;return"apns2"===r?"/v2/push/sub-key/".concat(i.subscribeKey,"/devices-apns2/").concat(n):"/v1/push/sub-key/".concat(i.subscribeKey,"/devices/").concat(n)},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var r=t.pushGateway,i=t.channels,o=void 0===i?[]:i,s=t.environment,a=void 0===s?"development":s,u=t.topic,c={type:r,remove:o.join(",")};return"apns2"===r&&delete(c=n(n({},c),{environment:a,topic:u})).type,c},handleResponse:function(){return{}}});var te=Object.freeze({__proto__:null,getOperation:function(){return j.PNPushNotificationEnabledChannelsOperation},validateParams:function(e,t){var n=t.device,r=t.pushGateway,i=t.topic,o=e.config;return n?r?"apns2"!==r||i?o.subscribeKey?void 0:"Missing Subscribe Key":"Missing APNS2 topic":"Missing GW Type (pushGateway: gcm, apns or apns2)":"Missing Device ID (device)"},getURL:function(e,t){var n=t.device,r=t.pushGateway,i=e.config;return"apns2"===r?"/v2/push/sub-key/".concat(i.subscribeKey,"/devices-apns2/").concat(n):"/v1/push/sub-key/".concat(i.subscribeKey,"/devices/").concat(n)},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var r=t.pushGateway,i=t.environment,o=void 0===i?"development":i,s=t.topic,a={type:r};return"apns2"===r&&delete(a=n(n({},a),{environment:o,topic:s})).type,a},handleResponse:function(e,t){return{channels:t}}});var ne=Object.freeze({__proto__:null,getOperation:function(){return j.PNRemoveAllPushNotificationsOperation},validateParams:function(e,t){var n=t.device,r=t.pushGateway,i=t.topic,o=e.config;return n?r?"apns2"!==r||i?o.subscribeKey?void 0:"Missing Subscribe Key":"Missing APNS2 topic":"Missing GW Type (pushGateway: gcm, apns or apns2)":"Missing Device ID (device)"},getURL:function(e,t){var n=t.device,r=t.pushGateway,i=e.config;return"apns2"===r?"/v2/push/sub-key/".concat(i.subscribeKey,"/devices-apns2/").concat(n,"/remove"):"/v1/push/sub-key/".concat(i.subscribeKey,"/devices/").concat(n,"/remove")},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var r=t.pushGateway,i=t.environment,o=void 0===i?"development":i,s=t.topic,a={type:r};return"apns2"===r&&delete(a=n(n({},a),{environment:o,topic:s})).type,a},handleResponse:function(){return{}}});var re=Object.freeze({__proto__:null,getOperation:function(){return j.PNUnsubscribeOperation},validateParams:function(e){if(!e.config.subscribeKey)return"Missing Subscribe Key"},getURL:function(e,t){var n=e.config,r=t.channels,i=void 0===r?[]:r,o=i.length>0?i.join(","):",";return"/v2/presence/sub-key/".concat(n.subscribeKey,"/channel/").concat(E.encodeString(o),"/leave")},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n=t.channelGroups,r=void 0===n?[]:n,i={};return r.length>0&&(i["channel-group"]=r.join(",")),i},handleResponse:function(){return{}}});var ie=Object.freeze({__proto__:null,getOperation:function(){return j.PNWhereNowOperation},validateParams:function(e){if(!e.config.subscribeKey)return"Missing Subscribe Key"},getURL:function(e,t){var n=e.config,r=t.uuid,i=void 0===r?n.UUID:r;return"/v2/presence/sub-key/".concat(n.subscribeKey,"/uuid/").concat(E.encodeString(i))},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(){return{}},handleResponse:function(e,t){return t.payload?{channels:t.payload.channels}:{channels:[]}}});var oe=Object.freeze({__proto__:null,getOperation:function(){return j.PNHeartbeatOperation},validateParams:function(e){if(!e.config.subscribeKey)return"Missing Subscribe Key"},getURL:function(e,t){var n=e.config,r=t.channels,i=void 0===r?[]:r,o=i.length>0?i.join(","):",";return"/v2/presence/sub-key/".concat(n.subscribeKey,"/channel/").concat(E.encodeString(o),"/heartbeat")},isAuthSupported:function(){return!0},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},prepareParams:function(e,t){var n=t.channelGroups,r=void 0===n?[]:n,i=t.state,o=void 0===i?{}:i,s=e.config,a={};return r.length>0&&(a["channel-group"]=r.join(",")),a.state=JSON.stringify(o),a.heartbeat=s.getPresenceTimeout(),a},handleResponse:function(){return{}}});var se=Object.freeze({__proto__:null,getOperation:function(){return j.PNGetStateOperation},validateParams:function(e){if(!e.config.subscribeKey)return"Missing Subscribe Key"},getURL:function(e,t){var n=e.config,r=t.uuid,i=void 0===r?n.UUID:r,o=t.channels,s=void 0===o?[]:o,a=s.length>0?s.join(","):",";return"/v2/presence/sub-key/".concat(n.subscribeKey,"/channel/").concat(E.encodeString(a),"/uuid/").concat(i)},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n=t.channelGroups,r=void 0===n?[]:n,i={};return r.length>0&&(i["channel-group"]=r.join(",")),i},handleResponse:function(e,t,n){var r=n.channels,i=void 0===r?[]:r,o=n.channelGroups,s=void 0===o?[]:o,a={};return 1===i.length&&0===s.length?a[i[0]]=t.payload:a=t.payload,{channels:a}}});var ae=Object.freeze({__proto__:null,getOperation:function(){return j.PNSetStateOperation},validateParams:function(e,t){var n=e.config,r=t.state,i=t.channels,o=void 0===i?[]:i,s=t.channelGroups,a=void 0===s?[]:s;return r?n.subscribeKey?0===o.length&&0===a.length?"Please provide a list of channels and/or channel-groups":void 0:"Missing Subscribe Key":"Missing State"},getURL:function(e,t){var n=e.config,r=t.channels,i=void 0===r?[]:r,o=i.length>0?i.join(","):",";return"/v2/presence/sub-key/".concat(n.subscribeKey,"/channel/").concat(E.encodeString(o),"/uuid/").concat(E.encodeString(n.UUID),"/data")},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n=t.state,r=t.channelGroups,i=void 0===r?[]:r,o={};return o.state=JSON.stringify(n),i.length>0&&(o["channel-group"]=i.join(",")),o},handleResponse:function(e,t){return{state:t.payload}}});var ue=Object.freeze({__proto__:null,getOperation:function(){return j.PNHereNowOperation},validateParams:function(e){if(!e.config.subscribeKey)return"Missing Subscribe Key"},getURL:function(e,t){var n=e.config,r=t.channels,i=void 0===r?[]:r,o=t.channelGroups,s=void 0===o?[]:o,a="/v2/presence/sub-key/".concat(n.subscribeKey);if(i.length>0||s.length>0){var u=i.length>0?i.join(","):",";a+="/channel/".concat(E.encodeString(u))}return a},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var r=t.channelGroups,i=void 0===r?[]:r,o=t.includeUUIDs,s=void 0===o||o,a=t.includeState,u=void 0!==a&&a,c=t.queryParameters,l=void 0===c?{}:c,p={};return s||(p.disable_uuids=1),u&&(p.state=1),i.length>0&&(p["channel-group"]=i.join(",")),p=n(n({},p),l)},handleResponse:function(e,t,n){var r=n.channels,i=void 0===r?[]:r,o=n.channelGroups,s=void 0===o?[]:o,a=n.includeUUIDs,u=void 0===a||a,c=n.includeState,l=void 0!==c&&c;return i.length>1||s.length>0||0===s.length&&0===i.length?function(){var e={};return e.totalChannels=t.payload.total_channels,e.totalOccupancy=t.payload.total_occupancy,e.channels={},Object.keys(t.payload.channels).forEach((function(n){var r=t.payload.channels[n],i=[];return e.channels[n]={occupants:i,name:n,occupancy:r.occupancy},u&&r.uuids.forEach((function(e){l?i.push({state:e.state,uuid:e.uuid}):i.push({state:null,uuid:e})})),e})),e}():function(){var e={},n=[];return e.totalChannels=1,e.totalOccupancy=t.occupancy,e.channels={},e.channels[i[0]]={occupants:n,name:i[0],occupancy:t.occupancy},u&&t.uuids&&t.uuids.forEach((function(e){l?n.push({state:e.state,uuid:e.uuid}):n.push({state:null,uuid:e})})),e}()},handleError:function(e,t,n){402!==n.statusCode||this.getURL(e,t).includes("channel")||(n.errorData.message="You have tried to perform a Global Here Now operation, your keyset configuration does not support that. Please provide a channel, or enable the Global Here Now feature from the Portal.")}});var ce=Object.freeze({__proto__:null,getOperation:function(){return j.PNAddMessageActionOperation},validateParams:function(e,t){var n=e.config,r=t.action,i=t.channel;return t.messageTimetoken?n.subscribeKey?i?r?r.value?r.type?r.type.length>15?"Action.type value exceed maximum length of 15":void 0:"Missing Action.type":"Missing Action.value":"Missing Action":"Missing message channel":"Missing Subscribe Key":"Missing message timetoken"},usePost:function(){return!0},postURL:function(e,t){var n=e.config,r=t.channel,i=t.messageTimetoken;return"/v1/message-actions/".concat(n.subscribeKey,"/channel/").concat(E.encodeString(r),"/message/").concat(i)},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},getRequestHeaders:function(){return{"Content-Type":"application/json"}},isAuthSupported:function(){return!0},prepareParams:function(){return{}},postPayload:function(e,t){return t.action},handleResponse:function(e,t){return{data:t.data}}});var le=Object.freeze({__proto__:null,getOperation:function(){return j.PNRemoveMessageActionOperation},validateParams:function(e,t){var n=e.config,r=t.channel,i=t.actionTimetoken;return t.messageTimetoken?i?n.subscribeKey?r?void 0:"Missing message channel":"Missing Subscribe Key":"Missing action timetoken":"Missing message timetoken"},useDelete:function(){return!0},getURL:function(e,t){var n=e.config,r=t.channel,i=t.actionTimetoken,o=t.messageTimetoken;return"/v1/message-actions/".concat(n.subscribeKey,"/channel/").concat(E.encodeString(r),"/message/").concat(o,"/action/").concat(i)},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(){return{}},handleResponse:function(e,t){return{data:t.data}}});var pe=Object.freeze({__proto__:null,getOperation:function(){return j.PNGetMessageActionsOperation},validateParams:function(e,t){var n=e.config,r=t.channel;return n.subscribeKey?r?void 0:"Missing message channel":"Missing Subscribe Key"},getURL:function(e,t){var n=e.config,r=t.channel;return"/v1/message-actions/".concat(n.subscribeKey,"/channel/").concat(E.encodeString(r))},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n=t.limit,r=t.start,i=t.end,o={};return n&&(o.limit=n),r&&(o.start=r),i&&(o.end=i),o},handleResponse:function(e,t){var n={data:t.data,start:null,end:null};return n.data.length&&(n.end=n.data[n.data.length-1].actionTimetoken,n.start=n.data[0].actionTimetoken),n}}),he={getOperation:function(){return j.PNListFilesOperation},validateParams:function(e,t){if(!(null==t?void 0:t.channel))return"channel can't be empty"},getURL:function(e,t){var n=e.config;return"/v1/files/".concat(n.subscribeKey,"/channels/").concat(E.encodeString(t.channel),"/files")},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n={};return t.limit&&(n.limit=t.limit),t.next&&(n.next=t.next),n},handleResponse:function(e,t){return{status:t.status,data:t.data,next:t.next,count:t.count}}},fe={getOperation:function(){return j.PNGenerateUploadUrlOperation},validateParams:function(e,t){return(null==t?void 0:t.channel)?(null==t?void 0:t.name)?void 0:"name can't be empty":"channel can't be empty"},usePost:function(){return!0},postURL:function(e,t){var n=e.config;return"/v1/files/".concat(n.subscribeKey,"/channels/").concat(E.encodeString(t.channel),"/generate-upload-url")},postPayload:function(e,t){return{name:t.name}},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(){return{}},handleResponse:function(e,t){return{status:t.status,data:t.data,file_upload_request:t.file_upload_request}}},de={getOperation:function(){return j.PNPublishFileOperation},validateParams:function(e,t){return(null==t?void 0:t.channel)?(null==t?void 0:t.fileId)?(null==t?void 0:t.fileName)?void 0:"file name can't be empty":"file id can't be empty":"channel can't be empty"},getURL:function(e,t){var n=e.config,r=n.publishKey,i=n.subscribeKey,o=function(e,t){var n=e.crypto,r=e.config,i=JSON.stringify(t);return r.cipherKey&&(i=n.encrypt(i),i=JSON.stringify(i)),i||""}(e,{message:t.message,file:{name:t.fileName,id:t.fileId}});return"/v1/files/publish-file/".concat(r,"/").concat(i,"/0/").concat(E.encodeString(t.channel),"/0/").concat(E.encodeString(o))},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n={};return t.ttl&&(n.ttl=t.ttl),void 0!==t.storeInHistory&&(n.store=t.storeInHistory?"1":"0"),t.meta&&"object"==typeof t.meta&&(n.meta=JSON.stringify(t.meta)),n},handleResponse:function(e,t){return{timetoken:t[2]}}},ge=function(e){var t=function(e){var t=this,n=e.generateUploadUrl,r=e.publishFile,s=e.modules,a=s.PubNubFile,u=s.config,c=s.cryptography,l=s.networking;return function(e){var s=e.channel,p=e.file,h=e.message,f=e.cipherKey,d=e.meta,g=e.ttl,y=e.storeInHistory;return i(t,void 0,void 0,(function(){var e,t,i,b,v,m,_,O,P,S,w,T,k,N,C,E,A,M,j,R,U,x,I,D,G,K,F,H;return o(this,(function(o){switch(o.label){case 0:if(!s)throw new L("Validation failed, check status for details",B("channel can't be empty"));if(!p)throw new L("Validation failed, check status for details",B("file can't be empty"));return e=a.create(p),[4,n({channel:s,name:e.name})];case 1:return t=o.sent(),i=t.file_upload_request,b=i.url,v=i.form_fields,m=t.data,_=m.id,O=m.name,a.supportsEncryptFile&&(null!=f?f:u.cipherKey)?[4,c.encryptFile(null!=f?f:u.cipherKey,e,a)]:[3,3];case 2:e=o.sent(),o.label=3;case 3:P=v,e.mimeType&&(P=v.map((function(t){return"Content-Type"===t.key?{key:t.key,value:e.mimeType}:t}))),o.label=4;case 4:return o.trys.push([4,18,,20]),a.supportsFileUri&&p.uri?(T=(w=l).POSTFILE,k=[b,P],[4,e.toFileUri()]):[3,7];case 5:return[4,T.apply(w,k.concat([o.sent()]))];case 6:return S=o.sent(),[3,17];case 7:return a.supportsFile?(C=(N=l).POSTFILE,E=[b,P],[4,e.toFile()]):[3,10];case 8:return[4,C.apply(N,E.concat([o.sent()]))];case 9:return S=o.sent(),[3,17];case 10:return a.supportsBuffer?(M=(A=l).POSTFILE,j=[b,P],[4,e.toBuffer()]):[3,13];case 11:return[4,M.apply(A,j.concat([o.sent()]))];case 12:return S=o.sent(),[3,17];case 13:return a.supportsBlob?(U=(R=l).POSTFILE,x=[b,P],[4,e.toBlob()]):[3,16];case 14:return[4,U.apply(R,x.concat([o.sent()]))];case 15:return S=o.sent(),[3,17];case 16:throw new Error("Unsupported environment");case 17:return[3,20];case 18:return I=o.sent(),[4,(q=I.response,new Promise((function(e){var t="";q.on("data",(function(e){t+=e.toString("utf8")})),q.on("end",(function(){e(t)}))})))];case 19:throw D=o.sent(),G=/<Message>(.*)<\/Message>/gi.exec(D),new L(G?"Upload to bucket failed: ".concat(G[1]):"Upload to bucket failed.",I);case 20:if(204!==S.status)throw new L("Upload to bucket was unsuccessful",S);K=u.fileUploadPublishRetryLimit,F=!1,H={timetoken:"0"},o.label=21;case 21:return o.trys.push([21,23,,24]),[4,r({channel:s,message:h,fileId:_,fileName:O,meta:d,storeInHistory:y,ttl:g})];case 22:return H=o.sent(),F=!0,[3,24];case 23:return o.sent(),K-=1,[3,24];case 24:if(!F&&K>0)return[3,21];o.label=25;case 25:if(F)return[2,{timetoken:H.timetoken,id:_,name:O}];throw new L("Publish failed. You may want to execute that operation manually using pubnub.publishFile",{channel:s,id:_,name:O})}var q}))}))}}(e);return function(e,n){var r=t(e);return"function"==typeof n?(r.then((function(e){return n(null,e)})).catch((function(e){return n(e,null)})),r):r}},ye=function(e,t){var n=t.channel,r=t.id,i=t.name,o=e.config,s=e.networking;if(!n)throw new L("Validation failed, check status for details",B("channel can't be empty"));if(!r)throw new L("Validation failed, check status for details",B("file id can't be empty"));if(!i)throw new L("Validation failed, check status for details",B("file name can't be empty"));var a="/v1/files/".concat(o.subscribeKey,"/channels/").concat(E.encodeString(n),"/files/").concat(r,"/").concat(i),u={};u.uuid=o.getUUID(),u.pnsdk=q(o),o.getAuthKey()&&(u.auth=o.getAuthKey()),o.secretKey&&V(e,a,u,{},{getOperation:function(){return"PubNubGetFileUrlOperation"}});var c=Object.keys(u).map((function(e){return"".concat(encodeURIComponent(e),"=").concat(encodeURIComponent(u[e]))})).join("&");return""!==c?"".concat(s.getStandardOrigin()).concat(a,"?").concat(c):"".concat(s.getStandardOrigin()).concat(a)},be={getOperation:function(){return j.PNDownloadFileOperation},validateParams:function(e,t){return(null==t?void 0:t.channel)?(null==t?void 0:t.name)?(null==t?void 0:t.id)?void 0:"id can't be empty":"name can't be empty":"channel can't be empty"},useGetFile:function(){return!0},getFileURL:function(e,t){var n=e.config;return"/v1/files/".concat(n.subscribeKey,"/channels/").concat(E.encodeString(t.channel),"/files/").concat(t.id,"/").concat(t.name)},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},ignoreBody:function(){return!0},forceBuffered:function(){return!0},prepareParams:function(){return{}},handleResponse:function(e,t,n){var r=e.PubNubFile,s=e.config,a=e.cryptography;return i(void 0,void 0,void 0,(function(){var e,i,u,c;return o(this,(function(o){switch(o.label){case 0:return e=t.response.body,r.supportsEncryptFile&&(null!==(i=n.cipherKey)&&void 0!==i?i:s.cipherKey)?[4,a.decrypt(null!==(u=n.cipherKey)&&void 0!==u?u:s.cipherKey,e)]:[3,2];case 1:e=o.sent(),o.label=2;case 2:return[2,r.create({data:e,name:null!==(c=t.response.name)&&void 0!==c?c:n.name,mimeType:t.response.type})]}}))}))}},ve={getOperation:function(){return j.PNListFilesOperation},validateParams:function(e,t){return(null==t?void 0:t.channel)?(null==t?void 0:t.id)?(null==t?void 0:t.name)?void 0:"file name can't be empty":"file id can't be empty":"channel can't be empty"},useDelete:function(){return!0},getURL:function(e,t){var n=e.config;return"/v1/files/".concat(n.subscribeKey,"/channels/").concat(E.encodeString(t.channel),"/files/").concat(t.id,"/").concat(t.name)},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(){return{}},handleResponse:function(e,t){return{status:t.status}}},me={getOperation:function(){return j.PNGetAllUUIDMetadataOperation},validateParams:function(){},getURL:function(e){var t=e.config;return"/v2/objects/".concat(t.subscribeKey,"/uuids")},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n,r,i,o,s,u,c,l,p,h={include:["status","type"]};return(null==t?void 0:t.include)&&(null===(n=t.include)||void 0===n?void 0:n.customFields)&&h.include.push("custom"),h.include=h.include.join(","),(null===(r=null==t?void 0:t.include)||void 0===r?void 0:r.totalCount)&&(h.count=null===(i=t.include)||void 0===i?void 0:i.totalCount),(null===(o=null==t?void 0:t.page)||void 0===o?void 0:o.next)&&(h.start=null===(s=t.page)||void 0===s?void 0:s.next),(null===(u=null==t?void 0:t.page)||void 0===u?void 0:u.prev)&&(h.end=null===(c=t.page)||void 0===c?void 0:c.prev),(null==t?void 0:t.filter)&&(h.filter=t.filter),h.limit=null!==(l=null==t?void 0:t.limit)&&void 0!==l?l:100,(null==t?void 0:t.sort)&&(h.sort=Object.entries(null!==(p=t.sort)&&void 0!==p?p:{}).map((function(e){var t=a(e,2),n=t[0],r=t[1];return"asc"===r||"desc"===r?"".concat(n,":").concat(r):n}))),h},handleResponse:function(e,t){return{status:t.status,data:t.data,totalCount:t.totalCount,next:t.next,prev:t.prev}}},_e={getOperation:function(){return j.PNGetUUIDMetadataOperation},validateParams:function(){},getURL:function(e,t){var n,r=e.config;return"/v2/objects/".concat(r.subscribeKey,"/uuids/").concat(E.encodeString(null!==(n=null==t?void 0:t.uuid)&&void 0!==n?n:r.getUUID()))},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n,r,i=e.config,o={};return o.uuid=null!==(n=null==t?void 0:t.uuid)&&void 0!==n?n:i.getUUID(),o.include=["status","type","custom"],(null==t?void 0:t.include)&&!1===(null===(r=t.include)||void 0===r?void 0:r.customFields)&&o.include.pop(),o.include=o.include.join(","),o},handleResponse:function(e,t){return{status:t.status,data:t.data}}},Oe={getOperation:function(){return j.PNSetUUIDMetadataOperation},validateParams:function(e,t){if(!(null==t?void 0:t.data))return"Data cannot be empty"},usePatch:function(){return!0},patchURL:function(e,t){var n,r=e.config;return"/v2/objects/".concat(r.subscribeKey,"/uuids/").concat(E.encodeString(null!==(n=t.uuid)&&void 0!==n?n:r.getUUID()))},patchPayload:function(e,t){return t.data},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n,r,i=e.config,o={};return o.uuid=null!==(n=null==t?void 0:t.uuid)&&void 0!==n?n:i.getUUID(),o.include=["status","type","custom"],(null==t?void 0:t.include)&&!1===(null===(r=t.include)||void 0===r?void 0:r.customFields)&&o.include.pop(),o.include=o.include.join(","),o},handleResponse:function(e,t){return{status:t.status,data:t.data}}},Pe={getOperation:function(){return j.PNRemoveUUIDMetadataOperation},validateParams:function(){},getURL:function(e,t){var n,r=e.config;return"/v2/objects/".concat(r.subscribeKey,"/uuids/").concat(E.encodeString(null!==(n=null==t?void 0:t.uuid)&&void 0!==n?n:r.getUUID()))},useDelete:function(){return!0},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n,r=e.config;return{uuid:null!==(n=null==t?void 0:t.uuid)&&void 0!==n?n:r.getUUID()}},handleResponse:function(e,t){return{status:t.status,data:t.data}}},Se={getOperation:function(){return j.PNGetAllChannelMetadataOperation},validateParams:function(){},getURL:function(e){var t=e.config;return"/v2/objects/".concat(t.subscribeKey,"/channels")},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n,r,i,o,s,u,c,l,p,h={include:["status","type"]};return(null==t?void 0:t.include)&&(null===(n=t.include)||void 0===n?void 0:n.customFields)&&h.include.push("custom"),h.include=h.include.join(","),(null===(r=null==t?void 0:t.include)||void 0===r?void 0:r.totalCount)&&(h.count=null===(i=t.include)||void 0===i?void 0:i.totalCount),(null===(o=null==t?void 0:t.page)||void 0===o?void 0:o.next)&&(h.start=null===(s=t.page)||void 0===s?void 0:s.next),(null===(u=null==t?void 0:t.page)||void 0===u?void 0:u.prev)&&(h.end=null===(c=t.page)||void 0===c?void 0:c.prev),(null==t?void 0:t.filter)&&(h.filter=t.filter),h.limit=null!==(l=null==t?void 0:t.limit)&&void 0!==l?l:100,(null==t?void 0:t.sort)&&(h.sort=Object.entries(null!==(p=t.sort)&&void 0!==p?p:{}).map((function(e){var t=a(e,2),n=t[0],r=t[1];return"asc"===r||"desc"===r?"".concat(n,":").concat(r):n}))),h},handleResponse:function(e,t){return{status:t.status,data:t.data,totalCount:t.totalCount,prev:t.prev,next:t.next}}},we={getOperation:function(){return j.PNGetChannelMetadataOperation},validateParams:function(e,t){if(!(null==t?void 0:t.channel))return"Channel cannot be empty"},getURL:function(e,t){var n=e.config;return"/v2/objects/".concat(n.subscribeKey,"/channels/").concat(E.encodeString(t.channel))},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n,r={include:["status","type","custom"]};return(null==t?void 0:t.include)&&!1===(null===(n=t.include)||void 0===n?void 0:n.customFields)&&r.include.pop(),r.include=r.include.join(","),r},handleResponse:function(e,t){return{status:t.status,data:t.data}}},Te={getOperation:function(){return j.PNSetChannelMetadataOperation},validateParams:function(e,t){return(null==t?void 0:t.channel)?(null==t?void 0:t.data)?void 0:"Data cannot be empty":"Channel cannot be empty"},usePatch:function(){return!0},patchURL:function(e,t){var n=e.config;return"/v2/objects/".concat(n.subscribeKey,"/channels/").concat(E.encodeString(t.channel))},patchPayload:function(e,t){return t.data},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n,r={include:["status","type","custom"]};return(null==t?void 0:t.include)&&!1===(null===(n=t.include)||void 0===n?void 0:n.customFields)&&r.include.pop(),r.include=r.include.join(","),r},handleResponse:function(e,t){return{status:t.status,data:t.data}}},ke={getOperation:function(){return j.PNRemoveChannelMetadataOperation},validateParams:function(e,t){if(!(null==t?void 0:t.channel))return"Channel cannot be empty"},getURL:function(e,t){var n=e.config;return"/v2/objects/".concat(n.subscribeKey,"/channels/").concat(E.encodeString(t.channel))},useDelete:function(){return!0},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(){return{}},handleResponse:function(e,t){return{status:t.status,data:t.data}}},Ne={getOperation:function(){return j.PNGetMembersOperation},validateParams:function(e,t){if(!(null==t?void 0:t.channel))return"UUID cannot be empty"},getURL:function(e,t){var n=e.config;return"/v2/objects/".concat(n.subscribeKey,"/channels/").concat(E.encodeString(t.channel),"/uuids")},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n,r,i,o,s,u,c,l,p,h,f,d,g={include:["uuid.status","uuid.type","type"]};return(null==t?void 0:t.include)&&((null===(n=t.include)||void 0===n?void 0:n.customFields)&&g.include.push("custom"),(null===(r=t.include)||void 0===r?void 0:r.customUUIDFields)&&g.include.push("uuid.custom"),(null===(o=null===(i=t.include)||void 0===i?void 0:i.UUIDFields)||void 0===o||o)&&g.include.push("uuid")),g.include=g.include.join(","),(null===(s=null==t?void 0:t.include)||void 0===s?void 0:s.totalCount)&&(g.count=null===(u=t.include)||void 0===u?void 0:u.totalCount),(null===(c=null==t?void 0:t.page)||void 0===c?void 0:c.next)&&(g.start=null===(l=t.page)||void 0===l?void 0:l.next),(null===(p=null==t?void 0:t.page)||void 0===p?void 0:p.prev)&&(g.end=null===(h=t.page)||void 0===h?void 0:h.prev),(null==t?void 0:t.filter)&&(g.filter=t.filter),g.limit=null!==(f=null==t?void 0:t.limit)&&void 0!==f?f:100,(null==t?void 0:t.sort)&&(g.sort=Object.entries(null!==(d=t.sort)&&void 0!==d?d:{}).map((function(e){var t=a(e,2),n=t[0],r=t[1];return"asc"===r||"desc"===r?"".concat(n,":").concat(r):n}))),g},handleResponse:function(e,t){return{status:t.status,data:t.data,totalCount:t.totalCount,prev:t.prev,next:t.next}}},Ce={getOperation:function(){return j.PNSetMembersOperation},validateParams:function(e,t){return(null==t?void 0:t.channel)?(null==t?void 0:t.uuids)&&0!==(null==t?void 0:t.uuids.length)?void 0:"UUIDs cannot be empty":"Channel cannot be empty"},usePatch:function(){return!0},patchURL:function(e,t){var n=e.config;return"/v2/objects/".concat(n.subscribeKey,"/channels/").concat(E.encodeString(t.channel),"/uuids")},patchPayload:function(e,t){var n;return(n={set:[],delete:[]})[t.type]=t.uuids.map((function(e){return"string"==typeof e?{uuid:{id:e}}:{uuid:{id:e.id},custom:e.custom,status:e.status}})),n},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n,r,i,o,s,u,c,l,p,h={include:["uuid.status","uuid.type","type"]};return(null==t?void 0:t.include)&&((null===(n=t.include)||void 0===n?void 0:n.customFields)&&h.include.push("custom"),(null===(r=t.include)||void 0===r?void 0:r.customUUIDFields)&&h.include.push("uuid.custom"),(null===(i=t.include)||void 0===i?void 0:i.UUIDFields)&&h.include.push("uuid")),h.include=h.include.join(","),(null===(o=null==t?void 0:t.include)||void 0===o?void 0:o.totalCount)&&(h.count=!0),(null===(s=null==t?void 0:t.page)||void 0===s?void 0:s.next)&&(h.start=null===(u=t.page)||void 0===u?void 0:u.next),(null===(c=null==t?void 0:t.page)||void 0===c?void 0:c.prev)&&(h.end=null===(l=t.page)||void 0===l?void 0:l.prev),(null==t?void 0:t.filter)&&(h.filter=t.filter),null!=t.limit&&(h.limit=t.limit),(null==t?void 0:t.sort)&&(h.sort=Object.entries(null!==(p=t.sort)&&void 0!==p?p:{}).map((function(e){var t=a(e,2),n=t[0],r=t[1];return"asc"===r||"desc"===r?"".concat(n,":").concat(r):n}))),h},handleResponse:function(e,t){return{status:t.status,data:t.data,totalCount:t.totalCount,prev:t.prev,next:t.next}}},Ee={getOperation:function(){return j.PNGetMembershipsOperation},validateParams:function(){},getURL:function(e,t){var n,r=e.config;return"/v2/objects/".concat(r.subscribeKey,"/uuids/").concat(E.encodeString(null!==(n=null==t?void 0:t.uuid)&&void 0!==n?n:r.getUUID()),"/channels")},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n,r,i,o,s,u,c,l,p,h,f,d={include:["channel.status","channel.type","status"]};return(null==t?void 0:t.include)&&((null===(n=t.include)||void 0===n?void 0:n.customFields)&&d.include.push("custom"),(null===(r=t.include)||void 0===r?void 0:r.customChannelFields)&&d.include.push("channel.custom"),(null===(i=t.include)||void 0===i?void 0:i.channelFields)&&d.include.push("channel")),d.include=d.include.join(","),(null===(o=null==t?void 0:t.include)||void 0===o?void 0:o.totalCount)&&(d.count=null===(s=t.include)||void 0===s?void 0:s.totalCount),(null===(u=null==t?void 0:t.page)||void 0===u?void 0:u.next)&&(d.start=null===(c=t.page)||void 0===c?void 0:c.next),(null===(l=null==t?void 0:t.page)||void 0===l?void 0:l.prev)&&(d.end=null===(p=t.page)||void 0===p?void 0:p.prev),(null==t?void 0:t.filter)&&(d.filter=t.filter),d.limit=null!==(h=null==t?void 0:t.limit)&&void 0!==h?h:100,(null==t?void 0:t.sort)&&(d.sort=Object.entries(null!==(f=t.sort)&&void 0!==f?f:{}).map((function(e){var t=a(e,2),n=t[0],r=t[1];return"asc"===r||"desc"===r?"".concat(n,":").concat(r):n}))),d},handleResponse:function(e,t){return{status:t.status,data:t.data,totalCount:t.totalCount,prev:t.prev,next:t.next}}},Ae={getOperation:function(){return j.PNSetMembershipsOperation},validateParams:function(e,t){if(!(null==t?void 0:t.channels)||0===(null==t?void 0:t.channels.length))return"Channels cannot be empty"},usePatch:function(){return!0},patchURL:function(e,t){var n,r=e.config;return"/v2/objects/".concat(r.subscribeKey,"/uuids/").concat(E.encodeString(null!==(n=t.uuid)&&void 0!==n?n:r.getUUID()),"/channels")},patchPayload:function(e,t){var n;return(n={set:[],delete:[]})[t.type]=t.channels.map((function(e){return"string"==typeof e?{channel:{id:e}}:{channel:{id:e.id},custom:e.custom,status:e.status}})),n},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n,r,i,o,s,u,c,l,p,h={include:["channel.status","channel.type","status"]};return(null==t?void 0:t.include)&&((null===(n=t.include)||void 0===n?void 0:n.customFields)&&h.include.push("custom"),(null===(r=t.include)||void 0===r?void 0:r.customChannelFields)&&h.include.push("channel.custom"),(null===(i=t.include)||void 0===i?void 0:i.channelFields)&&h.include.push("channel")),h.include=h.include.join(","),(null===(o=null==t?void 0:t.include)||void 0===o?void 0:o.totalCount)&&(h.count=!0),(null===(s=null==t?void 0:t.page)||void 0===s?void 0:s.next)&&(h.start=null===(u=t.page)||void 0===u?void 0:u.next),(null===(c=null==t?void 0:t.page)||void 0===c?void 0:c.prev)&&(h.end=null===(l=t.page)||void 0===l?void 0:l.prev),(null==t?void 0:t.filter)&&(h.filter=t.filter),null!=t.limit&&(h.limit=t.limit),(null==t?void 0:t.sort)&&(h.sort=Object.entries(null!==(p=t.sort)&&void 0!==p?p:{}).map((function(e){var t=a(e,2),n=t[0],r=t[1];return"asc"===r||"desc"===r?"".concat(n,":").concat(r):n}))),h},handleResponse:function(e,t){return{status:t.status,data:t.data,totalCount:t.totalCount,prev:t.prev,next:t.next}}};var Me=Object.freeze({__proto__:null,getOperation:function(){return j.PNAccessManagerAudit},validateParams:function(e){if(!e.config.subscribeKey)return"Missing Subscribe Key"},getURL:function(e){var t=e.config;return"/v2/auth/audit/sub-key/".concat(t.subscribeKey)},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!1},prepareParams:function(e,t){var n=t.channel,r=t.channelGroup,i=t.authKeys,o=void 0===i?[]:i,s={};return n&&(s.channel=n),r&&(s["channel-group"]=r),o.length>0&&(s.auth=o.join(",")),s},handleResponse:function(e,t){return t.payload}});var je=Object.freeze({__proto__:null,getOperation:function(){return j.PNAccessManagerGrant},validateParams:function(e,t){var n=e.config;return n.subscribeKey?n.publishKey?n.secretKey?null==t.uuids||t.authKeys?null==t.uuids||null==t.channels&&null==t.channelGroups?void 0:"Both channel/channelgroup and uuid cannot be used in the same request":"authKeys are required for grant request on uuids":"Missing Secret Key":"Missing Publish Key":"Missing Subscribe Key"},getURL:function(e){var t=e.config;return"/v2/auth/grant/sub-key/".concat(t.subscribeKey)},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!1},prepareParams:function(e,t){var n=t.channels,r=void 0===n?[]:n,i=t.channelGroups,o=void 0===i?[]:i,s=t.uuids,a=void 0===s?[]:s,u=t.ttl,c=t.read,l=void 0!==c&&c,p=t.write,h=void 0!==p&&p,f=t.manage,d=void 0!==f&&f,g=t.get,y=void 0!==g&&g,b=t.join,v=void 0!==b&&b,m=t.update,_=void 0!==m&&m,O=t.authKeys,P=void 0===O?[]:O,S=t.delete,w={};return w.r=l?"1":"0",w.w=h?"1":"0",w.m=d?"1":"0",w.d=S?"1":"0",w.g=y?"1":"0",w.j=v?"1":"0",w.u=_?"1":"0",r.length>0&&(w.channel=r.join(",")),o.length>0&&(w["channel-group"]=o.join(",")),P.length>0&&(w.auth=P.join(",")),a.length>0&&(w["target-uuid"]=a.join(",")),(u||0===u)&&(w.ttl=u),w},handleResponse:function(){return{}}});function Re(e){var t=0;return e.join&&(t|=128),e.update&&(t|=64),e.get&&(t|=32),e.delete&&(t|=8),e.manage&&(t|=4),e.write&&(t|=2),e.read&&(t|=1),t}var Ue=Object.freeze({__proto__:null,getOperation:function(){return j.PNAccessManagerGrantToken},extractPermissions:Re,validateParams:function(e,t){var n=e.config;return n.subscribeKey?n.publishKey?n.secretKey?t.resources||t.patterns?(!t.resources||t.resources.uuids&&0!==Object.keys(t.resources.uuids).length||t.resources.channels&&0!==Object.keys(t.resources.channels).length||t.resources.groups&&0!==Object.keys(t.resources.groups).length)&&(!t.patterns||t.patterns.uuids&&0!==Object.keys(t.patterns.uuids).length||t.patterns.channels&&0!==Object.keys(t.patterns.channels).length||t.patterns.groups&&0!==Object.keys(t.patterns.groups).length)?void 0:"Missing values for either Resources or Patterns.":"Missing either Resources or Patterns.":"Missing Secret Key":"Missing Publish Key":"Missing Subscribe Key"},postURL:function(e){var t=e.config;return"/v3/pam/".concat(t.subscribeKey,"/grant")},usePost:function(){return!0},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!1},prepareParams:function(){return{}},postPayload:function(e,t){return function(e,t){var n=t.ttl,r=t.resources,i=t.patterns,o=t.meta,s=t.authorized_uuid,a={ttl:0,permissions:{resources:{channels:{},groups:{},uuids:{},users:{},spaces:{}},patterns:{channels:{},groups:{},uuids:{},users:{},spaces:{}},meta:{}}};if(r){var u=r.uuids,c=r.channels,l=r.groups;u&&Object.keys(u).forEach((function(e){a.permissions.resources.uuids[e]=Re(u[e])})),c&&Object.keys(c).forEach((function(e){a.permissions.resources.channels[e]=Re(c[e])})),l&&Object.keys(l).forEach((function(e){a.permissions.resources.groups[e]=Re(l[e])}))}if(i){var p=i.uuids,h=i.channels,f=i.groups;p&&Object.keys(p).forEach((function(e){a.permissions.patterns.uuids[e]=Re(p[e])})),h&&Object.keys(h).forEach((function(e){a.permissions.patterns.channels[e]=Re(h[e])})),f&&Object.keys(f).forEach((function(e){a.permissions.patterns.groups[e]=Re(f[e])}))}return(n||0===n)&&(a.ttl=n),o&&(a.permissions.meta=o),s&&(a.permissions.uuid="".concat(s)),a}(0,t)},handleResponse:function(e,t){return t.data.token}}),xe={getOperation:function(){return j.PNAccessManagerRevokeToken},validateParams:function(e,t){return e.config.secretKey?t?void 0:"token can't be empty":"Missing Secret Key"},getURL:function(e,t){var n=e.config;return"/v3/pam/".concat(n.subscribeKey,"/grant/").concat(E.encodeString(t))},useDelete:function(){return!0},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!1},prepareParams:function(e){return{uuid:e.config.getUUID()}},handleResponse:function(e,t){return{status:t.status,data:t.data}}};function Ie(e,t){var n=e.crypto,r=e.config,i=JSON.stringify(t);return r.cipherKey&&(i=n.encrypt(i),i=JSON.stringify(i)),i}var De=Object.freeze({__proto__:null,getOperation:function(){return j.PNPublishOperation},validateParams:function(e,t){var n=e.config,r=t.message;return t.channel?r?n.subscribeKey?void 0:"Missing Subscribe Key":"Missing Message":"Missing Channel"},usePost:function(e,t){var n=t.sendByPost;return void 0!==n&&n},getURL:function(e,t){var n=e.config,r=t.channel,i=Ie(e,t.message);return"/publish/".concat(n.publishKey,"/").concat(n.subscribeKey,"/0/").concat(E.encodeString(r),"/0/").concat(E.encodeString(i))},postURL:function(e,t){var n=e.config,r=t.channel;return"/publish/".concat(n.publishKey,"/").concat(n.subscribeKey,"/0/").concat(E.encodeString(r),"/0")},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},postPayload:function(e,t){return Ie(e,t.message)},prepareParams:function(e,t){var n=t.meta,r=t.replicate,i=void 0===r||r,o=t.storeInHistory,s=t.ttl,a={};return null!=o&&(a.store=o?"1":"0"),s&&(a.ttl=s),!1===i&&(a.norep="true"),n&&"object"==typeof n&&(a.meta=JSON.stringify(n)),a},handleResponse:function(e,t){return{timetoken:t[2]}}});var Ge=Object.freeze({__proto__:null,getOperation:function(){return j.PNSignalOperation},validateParams:function(e,t){var n=e.config,r=t.message;return t.channel?r?n.subscribeKey?void 0:"Missing Subscribe Key":"Missing Message":"Missing Channel"},getURL:function(e,t){var n,r=e.config,i=t.channel,o=t.message,s=(n=o,JSON.stringify(n));return"/signal/".concat(r.publishKey,"/").concat(r.subscribeKey,"/0/").concat(E.encodeString(i),"/0/").concat(E.encodeString(s))},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(){return{}},handleResponse:function(e,t){return{timetoken:t[2]}}});function Ke(e,t){var n=e.config,r=e.crypto;if(!n.cipherKey)return t;try{return r.decrypt(t)}catch(e){return t}}var Fe=Object.freeze({__proto__:null,getOperation:function(){return j.PNHistoryOperation},validateParams:function(e,t){var n=t.channel,r=e.config;return n?r.subscribeKey?void 0:"Missing Subscribe Key":"Missing channel"},getURL:function(e,t){var n=t.channel,r=e.config;return"/v2/history/sub-key/".concat(r.subscribeKey,"/channel/").concat(E.encodeString(n))},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n=t.start,r=t.end,i=t.reverse,o=t.count,s=void 0===o?100:o,a=t.stringifiedTimeToken,u=void 0!==a&&a,c=t.includeMeta,l=void 0!==c&&c,p={include_token:"true"};return p.count=s,n&&(p.start=n),r&&(p.end=r),u&&(p.string_message_token="true"),null!=i&&(p.reverse=i.toString()),l&&(p.include_meta="true"),p},handleResponse:function(e,t){var n={messages:[],startTimeToken:t[1],endTimeToken:t[2]};return Array.isArray(t[0])&&t[0].forEach((function(t){var r={timetoken:t.timetoken,entry:Ke(e,t.message)};t.meta&&(r.meta=t.meta),n.messages.push(r)})),n}});var Le=Object.freeze({__proto__:null,getOperation:function(){return j.PNDeleteMessagesOperation},validateParams:function(e,t){var n=t.channel,r=e.config;return n?r.subscribeKey?void 0:"Missing Subscribe Key":"Missing channel"},useDelete:function(){return!0},getURL:function(e,t){var n=t.channel,r=e.config;return"/v3/history/sub-key/".concat(r.subscribeKey,"/channel/").concat(E.encodeString(n))},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n=t.start,r=t.end,i={};return n&&(i.start=n),r&&(i.end=r),i},handleResponse:function(e,t){return t.payload}});var Be=Object.freeze({__proto__:null,getOperation:function(){return j.PNMessageCounts},validateParams:function(e,t){var n=t.channels,r=t.timetoken,i=t.channelTimetokens,o=e.config;return n?r&&i?"timetoken and channelTimetokens are incompatible together":r&&i&&i.length>1&&n.length!==i.length?"Length of channelTimetokens and channels do not match":o.subscribeKey?void 0:"Missing Subscribe Key":"Missing channel"},getURL:function(e,t){var n=t.channels,r=e.config,i=n.join(",");return"/v3/history/sub-key/".concat(r.subscribeKey,"/message-counts/").concat(E.encodeString(i))},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n=t.timetoken,r=t.channelTimetokens,i={};if(r&&1===r.length){var o=a(r,1)[0];i.timetoken=o}else r?i.channelsTimetoken=r.join(","):n&&(i.timetoken=n);return i},handleResponse:function(e,t){return{channels:t.channels}}});var He=Object.freeze({__proto__:null,getOperation:function(){return j.PNFetchMessagesOperation},validateParams:function(e,t){var n=t.channels,r=t.includeMessageActions,i=void 0!==r&&r,o=e.config;if(!n||0===n.length)return"Missing channels";if(!o.subscribeKey)return"Missing Subscribe Key";if(i&&n.length>1)throw new TypeError("History can return actions data for a single channel only. Either pass a single channel or disable the includeMessageActions flag.")},getURL:function(e,t){var n=t.channels,r=void 0===n?[]:n,i=t.includeMessageActions,o=void 0!==i&&i,s=e.config,a=o?"history-with-actions":"history",u=r.length>0?r.join(","):",";return"/v3/".concat(a,"/sub-key/").concat(s.subscribeKey,"/channel/").concat(E.encodeString(u))},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n=t.channels,r=t.start,i=t.end,o=t.includeMessageActions,s=t.count,a=t.stringifiedTimeToken,u=void 0!==a&&a,c=t.includeMeta,l=void 0!==c&&c,p=t.includeUuid,h=t.includeUUID,f=void 0===h||h,d=t.includeMessageType,g=void 0===d||d,y={};return y.max=s||(n.length>1||!0===o?25:100),r&&(y.start=r),i&&(y.end=i),u&&(y.string_message_token="true"),l&&(y.include_meta="true"),f&&!1!==p&&(y.include_uuid="true"),g&&(y.include_message_type="true"),y},handleResponse:function(e,t){var n={channels:{}};return Object.keys(t.channels||{}).forEach((function(r){n.channels[r]=[],(t.channels[r]||[]).forEach((function(t){var i={};i.channel=r,i.timetoken=t.timetoken,i.message=function(e,t){var n=e.config,r=e.crypto;if(!n.cipherKey)return t;try{return r.decrypt(t)}catch(e){return t}}(e,t.message),i.messageType=t.message_type,i.uuid=t.uuid,t.actions&&(i.actions=t.actions,i.data=t.actions),t.meta&&(i.meta=t.meta),n.channels[r].push(i)}))})),t.more&&(n.more=t.more),n}});var qe=Object.freeze({__proto__:null,getOperation:function(){return j.PNTimeOperation},getURL:function(){return"/time/0"},getRequestTimeout:function(e){return e.config.getTransactionTimeout()},prepareParams:function(){return{}},isAuthSupported:function(){return!1},handleResponse:function(e,t){return{timetoken:t[0]}},validateParams:function(){}});var ze=Object.freeze({__proto__:null,getOperation:function(){return j.PNSubscribeOperation},validateParams:function(e){if(!e.config.subscribeKey)return"Missing Subscribe Key"},getURL:function(e,t){var n=e.config,r=t.channels,i=void 0===r?[]:r,o=i.length>0?i.join(","):",";return"/v2/subscribe/".concat(n.subscribeKey,"/").concat(E.encodeString(o),"/0")},getRequestTimeout:function(e){return e.config.getSubscribeTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n=e.config,r=t.state,i=t.channelGroups,o=void 0===i?[]:i,s=t.timetoken,a=t.filterExpression,u=t.region,c={heartbeat:n.getPresenceTimeout()};return o.length>0&&(c["channel-group"]=o.join(",")),a&&a.length>0&&(c["filter-expr"]=a),Object.keys(r).length&&(c.state=JSON.stringify(r)),s&&(c.tt=s),u&&(c.tr=u),c},handleResponse:function(e,t){var n=[];t.m.forEach((function(e){var t={publishTimetoken:e.p.t,region:e.p.r},r={shard:parseInt(e.a,10),subscriptionMatch:e.b,channel:e.c,messageType:e.e,payload:e.d,flags:e.f,issuingClientId:e.i,subscribeKey:e.k,originationTimetoken:e.o,userMetadata:e.u,publishMetaData:t};n.push(r)}));var r={timetoken:t.t.t,region:t.t.r};return{messages:n,metadata:r}}}),Ve={getOperation:function(){return j.PNHandshakeOperation},validateParams:function(e,t){if(!(null==t?void 0:t.channels)&&!(null==t?void 0:t.channelGroups))return"channels and channleGroups both should not be empty"},getURL:function(e,t){var n=e.config,r=t.channels?t.channels.join(","):",";return"/v2/subscribe/".concat(n.subscribeKey,"/").concat(E.encodeString(r),"/0")},getRequestTimeout:function(e){return e.config.getSubscribeTimeout()},isAuthSupported:function(){return!0},prepareParams:function(e,t){var n={};return t.channelGroups&&t.channelGroups.length>0&&(n["channel-group"]=t.channelGroups.join(",")),n.tt=0,n},handleResponse:function(e,t){return{region:t.t.r,timetoken:t.t.t}}},Je={getOperation:function(){return j.PNReceiveMessagesOperation},validateParams:function(e,t){return(null==t?void 0:t.channels)||(null==t?void 0:t.channelGroups)?(null==t?void 0:t.timetoken)?(null==t?void 0:t.region)?void 0:"region can not be empty":"timetoken can not be empty":"channels and channleGroups both should not be empty"},getURL:function(e,t){var n=e.config,r=t.channels?t.channels.join(","):",";return"/v2/subscribe/".concat(n.subscribeKey,"/").concat(E.encodeString(r),"/0")},getRequestTimeout:function(e){return e.config.getSubscribeTimeout()},isAuthSupported:function(){return!0},getAbortSignal:function(e,t){return t.abortSignal},prepareParams:function(e,t){var n={};return t.channelGroups&&t.channelGroups.length>0&&(n["channel-group"]=t.channelGroups.join(",")),n.tt=t.timetoken,n.tr=t.region,n},handleResponse:function(e,t){var n=[];return t.m.forEach((function(e){var t={shard:parseInt(e.a,10),subscriptionMatch:e.b,channel:e.c,messageType:e.e,payload:e.d,flags:e.f,issuingClientId:e.i,subscribeKey:e.k,originationTimetoken:e.o,publishMetaData:{timetoken:e.p.t,region:e.p.r}};n.push(t)})),{messages:n,metadata:{region:t.t.r,timetoken:t.t.t}}}},We=function(){function e(e){void 0===e&&(e=!1),this.sync=e,this.listeners=new Set}return e.prototype.subscribe=function(e){var t=this;return this.listeners.add(e),function(){t.listeners.delete(e)}},e.prototype.notify=function(e){var t=this,n=function(){t.listeners.forEach((function(t){t(e)}))};this.sync?n():setTimeout(n,0)},e}(),Xe=function(){function e(e){this.label=e,this.transitionMap=new Map,this.enterEffects=[],this.exitEffects=[]}return e.prototype.transition=function(e,t){var n;if(this.transitionMap.has(t.type))return null===(n=this.transitionMap.get(t.type))||void 0===n?void 0:n(e,t)},e.prototype.on=function(e,t){return this.transitionMap.set(e,t),this},e.prototype.with=function(e,t){return[this,e,null!=t?t:[]]},e.prototype.onEnter=function(e){return this.enterEffects.push(e),this},e.prototype.onExit=function(e){return this.exitEffects.push(e),this},e}(),$e=function(e){function n(){return null!==e&&e.apply(this,arguments)||this}return t(n,e),n.prototype.describe=function(e){return new Xe(e)},n.prototype.start=function(e,t){this.currentState=e,this.currentContext=t,this.notify({type:"engineStarted",state:e,context:t})},n.prototype.transition=function(e){var t,n,r,i,o,u;if(!this.currentState)throw new Error("Start the engine first");this.notify({type:"eventReceived",event:e});var c=this.currentState.transition(this.currentContext,e);if(c){var l=a(c,3),p=l[0],h=l[1],f=l[2];try{for(var d=s(this.currentState.exitEffects),g=d.next();!g.done;g=d.next()){var y=g.value;this.notify({type:"invocationDispatched",invocation:y(this.currentContext)})}}catch(e){t={error:e}}finally{try{g&&!g.done&&(n=d.return)&&n.call(d)}finally{if(t)throw t.error}}var b=this.currentState;this.currentState=p;var v=this.currentContext;this.currentContext=h,this.notify({type:"transitionDone",fromState:b,fromContext:v,toState:p,toContext:h,event:e});try{for(var m=s(f),_=m.next();!_.done;_=m.next()){y=_.value;this.notify({type:"invocationDispatched",invocation:y})}}catch(e){r={error:e}}finally{try{_&&!_.done&&(i=m.return)&&i.call(m)}finally{if(r)throw r.error}}try{for(var O=s(this.currentState.enterEffects),P=O.next();!P.done;P=O.next()){y=P.value;this.notify({type:"invocationDispatched",invocation:y(this.currentContext)})}}catch(e){o={error:e}}finally{try{P&&!P.done&&(u=O.return)&&u.call(O)}finally{if(o)throw o.error}}}},n}(We),Qe=function(){function e(e){this.dependencies=e,this.instances=new Map,this.handlers=new Map}return e.prototype.on=function(e,t){this.handlers.set(e,t)},e.prototype.dispatch=function(e){if("CANCEL"!==e.type){var t=this.handlers.get(e.type);if(!t)throw new Error("Unhandled invocation '".concat(e.type,"'"));var n=t(e.payload,this.dependencies);e.managed&&this.instances.set(e.type,n),n.start()}else if(this.instances.has(e.payload)){var r=this.instances.get(e.payload);null==r||r.cancel(),this.instances.delete(e.payload)}},e}();function Ye(e,t){var n=function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return{type:e,payload:null==t?void 0:t.apply(void 0,u([],a(n),!1))}};return n.type=e,n}function Ze(e,t){var n=function(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];return{type:e,payload:t.apply(void 0,u([],a(n),!1)),managed:!0}};return n.type=e,n.cancel={type:"CANCEL",payload:e,managed:!1},n}var et,tt,nt,rt=function(e){function n(){var t=this.constructor,n=e.call(this,"The operation was aborted.")||this;return n.name="AbortError",Object.setPrototypeOf(n,t.prototype),n}return t(n,e),n}(Error),it=function(e){function n(){var t=null!==e&&e.apply(this,arguments)||this;return t._aborted=!1,t}return t(n,e),Object.defineProperty(n.prototype,"aborted",{get:function(){return this._aborted},enumerable:!1,configurable:!0}),n.prototype.throwIfAborted=function(){if(this._aborted)throw new rt},n.prototype.abort=function(){this._aborted=!0,this.notify(new rt)},n}(We),ot=function(e){function n(t,n,r){var i=e.call(this,t,n)||this;return i.asyncFunction=r,i.abortSignal=new it,i}return t(n,e),n.prototype.start=function(){this.asyncFunction(this.payload,this.abortSignal,this.dependencies)},n.prototype.cancel=function(){this.abortSignal.abort()},n}((function(e,t){this.payload=e,this.dependencies=t})),st=function(e){return function(t,n){return new ot(t,n,e)}},at=Ze("HANDSHAKE",(function(e,t){return{channels:e,groups:t}})),ut=Ze("RECEIVE_EVENTS",(function(e,t,n){return{channels:e,groups:t,cursor:n}})),ct=(tt=function(e){return e},nt=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return{type:et,payload:tt.apply(void 0,u([],a(e),!1)),managed:!1}},nt.type=et="EMIT_EVENTS",nt),lt=Ze("RECONNECT",(function(e){return e})),pt=Ze("HANDSHAKE_RECONNECT",(function(e){return e})),ht=Ye("SUBSCRIPTION_CHANGE",(function(e,t){return{channels:e,groups:t}})),ft=Ye("DISCONNECT",(function(){return{}})),dt=Ye("RECONNECT",(function(){return{}}));Ye("RESTORE",(function(e,t,n,r){return{channels:e,groups:t,timetoken:n,region:r}}));var gt=Ye("HANDSHAKING_SUCCESS",(function(e){return e})),yt=Ye("HANDSHAKING_FAILURE",(function(e){return e})),bt=Ye("HANDSHAKING_RECONNECTING_SUCCESS",(function(e){return{cursor:e}})),vt=Ye("HANDSHAKING_RECONNECTING_FAILURE",(function(e){return e})),mt=Ye("HANDSHAKING_RECONNECTING_GIVEUP",(function(){return{}})),_t=Ye("HANDSHAKING_RECONNECTING_RETRY",(function(){return{}})),Ot=Ye("RECEIVING_SUCCESS",(function(e,t){return{cursor:e,events:t}})),Pt=Ye("RECEIVING_FAILURE",(function(e){return e})),St=Ye("RECONNECTING_SUCCESS",(function(e,t){return{cursor:e,events:t}})),wt=Ye("RECONNECTING_FAILURE",(function(e){return e})),Tt=Ye("RECONNECTING_GIVEUP",(function(){return{}})),kt=Ye("RECONNECTING_RETRY",(function(){return{}})),Nt=function(e){function n(t,n){var r=e.call(this,n)||this;return r.on(at.type,st((function(e,n,s){var a=s.handshake;return i(r,void 0,void 0,(function(){var r,i;return o(this,(function(o){switch(o.label){case 0:n.throwIfAborted(),o.label=1;case 1:return o.trys.push([1,3,,4]),[4,a({abortSignal:n,channels:e.channels,channelGroups:e.groups})];case 2:return r=o.sent(),t.transition(gt(r)),[3,4];case 3:return(i=o.sent())instanceof Error&&"Aborted"===i.message?[2]:i instanceof L?[2,t.transition(yt(i))]:[3,4];case 4:return[2]}}))}))}))),r.on(ut.type,st((function(e,n,s){var a=s.receiveEvents;return i(r,void 0,void 0,(function(){var r,i;return o(this,(function(o){switch(o.label){case 0:n.throwIfAborted(),o.label=1;case 1:return o.trys.push([1,3,,4]),[4,a({abortSignal:n,channels:e.channels,channelGroups:e.groups,timetoken:e.cursor.timetoken,region:e.cursor.region})];case 2:return r=o.sent(),t.transition(Ot(r.metadata,r.messages)),[3,4];case 3:return(i=o.sent())instanceof Error&&"Aborted"===i.message?[2]:i instanceof L?[2,t.transition(Pt(i))]:[3,4];case 4:return[2]}}))}))}))),r.on(ct.type,st((function(e,t,n){return n.receiveEvents,i(r,void 0,void 0,(function(){return o(this,(function(t){return e.length>0&&console.log(e),[2]}))}))}))),r.on(lt.type,st((function(e,n,s){var a=s.receiveEvents,u=s.shouldRetry,c=s.getRetryDelay,l=s.delay;return i(r,void 0,void 0,(function(){var r,i;return o(this,(function(o){switch(o.label){case 0:return u(e.reason,e.attempts)?(n.throwIfAborted(),[4,l(c(e.attempts))]):[2,t.transition(Tt())];case 1:o.sent(),n.throwIfAborted(),o.label=2;case 2:return o.trys.push([2,4,,5]),[4,a({abortSignal:n,channels:e.channels,channelGroups:e.groups,timetoken:e.cursor.timetoken,region:e.cursor.region})];case 3:return r=o.sent(),[2,t.transition(St(r.metadata,r.messages))];case 4:return(i=o.sent())instanceof Error&&"Aborted"===i.message?[2]:i instanceof L?[2,t.transition(wt(i))]:[3,5];case 5:return[2]}}))}))}))),r.on(pt.type,st((function(e,n,s){var a=s.handshake,u=s.shouldRetry,c=s.getRetryDelay,l=s.delay;return i(r,void 0,void 0,(function(){var r,i;return o(this,(function(o){switch(o.label){case 0:return u(e.reason,e.attempts)?(n.throwIfAborted(),[4,l(c(e.attempts))]):[2,t.transition(mt())];case 1:o.sent(),n.throwIfAborted(),o.label=2;case 2:return o.trys.push([2,4,,5]),[4,a({abortSignal:n,channels:e.channels,channelGroups:e.groups})];case 3:return r=o.sent(),[2,t.transition(bt(r.metadata))];case 4:return(i=o.sent())instanceof Error&&"Aborted"===i.message?[2]:i instanceof L?[2,t.transition(vt(i))]:[3,5];case 5:return[2]}}))}))}))),r}return t(n,e),n}(Qe),Ct=new Xe("STOPPED");Ct.on(ht.type,(function(e,t){return Ct.with({channels:t.payload.channels,groups:t.payload.groups})})),Ct.on(dt.type,(function(e){return xt.with(n({},e))}));var Et=new Xe("HANDSHAKE_FAILURE");Et.on(_t.type,(function(e){return Ut.with(n(n({},e),{attempts:0}))})),Et.on(ft.type,(function(e){return Ct.with({channels:e.channels,groups:e.groups})}));var At=new Xe("STOPPED");At.on(ht.type,(function(e,t){return At.with({channels:t.payload.channels,groups:t.payload.groups,cursor:e.cursor})})),At.on(dt.type,(function(e){return Rt.with(n({},e))}));var Mt=new Xe("RECEIVE_FAILURE");Mt.on(kt.type,(function(e){return jt.with(n(n({},e),{attempts:0}))})),Mt.on(ft.type,(function(e){return At.with({channels:e.channels,groups:e.groups,cursor:e.cursor})}));var jt=new Xe("RECEIVE_RECONNECTING");jt.onEnter((function(e){return lt(e)})),jt.onExit((function(){return lt.cancel})),jt.on(St.type,(function(e,t){return Rt.with({channels:e.channels,groups:e.groups,cursor:t.payload.cursor},[ct(t.payload.events)])})),jt.on(wt.type,(function(e,t){return jt.with(n(n({},e),{attempts:e.attempts+1,reason:t.payload}))})),jt.on(Tt.type,(function(e){return Mt.with({groups:e.groups,channels:e.channels,cursor:e.cursor,reason:e.reason})})),jt.on(ft.type,(function(e){return At.with({channels:e.channels,groups:e.groups,cursor:e.cursor})}));var Rt=new Xe("RECEIVING");Rt.onEnter((function(e){return ut(e.channels,e.groups,e.cursor)})),Rt.onExit((function(){return ut.cancel})),Rt.on(Ot.type,(function(e,t){return Rt.with(n(n({},e),{cursor:t.payload.cursor}),[ct(t.payload.events)])})),Rt.on(ht.type,(function(e,t){return 0===t.payload.channels.length&&0===t.payload.groups.length?It.with(void 0):Rt.with(n(n({},e),{channels:t.payload.channels,groups:t.payload.groups}))})),Rt.on(Pt.type,(function(e,t){return jt.with(n(n({},e),{attempts:0,reason:t.payload}))})),Rt.on(ft.type,(function(e){return At.with({channels:e.channels,groups:e.groups,cursor:e.cursor})}));var Ut=new Xe("HANDSHAKE_RECONNECTING");Ut.onEnter((function(e){return pt(e)})),Ut.onExit((function(){return lt.cancel})),Ut.on(St.type,(function(e,t){return Rt.with({channels:e.channels,groups:e.groups,cursor:t.payload.cursor},[ct(t.payload.events)])})),Ut.on(wt.type,(function(e,t){return Ut.with(n(n({},e),{attempts:e.attempts+1,reason:t.payload}))})),Ut.on(Tt.type,(function(e){return Et.with({groups:e.groups,channels:e.channels,reason:e.reason})})),Ut.on(ft.type,(function(e){return Ct.with({channels:e.channels,groups:e.groups})}));var xt=new Xe("HANDSHAKING");xt.onEnter((function(e){return at(e.channels,e.groups)})),xt.onExit((function(){return at.cancel})),xt.on(ht.type,(function(e,t){return 0===t.payload.channels.length&&0===t.payload.groups.length?It.with(void 0):xt.with({channels:t.payload.channels,groups:t.payload.groups})})),xt.on(gt.type,(function(e,t){return Rt.with({channels:e.channels,groups:e.groups,cursor:t.payload})})),xt.on(yt.type,(function(e,t){return Ut.with(n(n({},e),{attempts:0,reason:t.payload}))})),xt.on(ft.type,(function(e){return Ct.with({channels:e.channels,groups:e.groups})}));var It=new Xe("UNSUBSCRIBED");It.on(ht.type,(function(e,t){return xt.with({channels:t.payload.channels,groups:t.payload.groups})}));var Dt=function(){function e(e){var t=this;this.engine=new $e,this.channels=[],this.groups=[],this.dispatcher=new Nt(this.engine,e),this.engine.subscribe((function(e){"invocationDispatched"===e.type&&t.dispatcher.dispatch(e.invocation)})),this.engine.start(It,void 0)}return Object.defineProperty(e.prototype,"_engine",{get:function(){return this.engine},enumerable:!1,configurable:!0}),e.prototype.subscribe=function(e){var t=e.channels,n=e.groups;this.channels=u(u([],a(this.channels),!1),a(null!=t?t:[]),!1),this.groups=u(u([],a(this.groups),!1),a(null!=n?n:[]),!1),this.engine.transition(ht(this.channels,this.groups))},e.prototype.unsubscribe=function(e){var t=e.channels,n=e.groups;this.channels=this.channels.filter((function(e){var n;return null===(n=!(null==t?void 0:t.includes(e)))||void 0===n||n})),this.groups=this.groups.filter((function(e){var t;return null===(t=!(null==n?void 0:n.includes(e)))||void 0===t||t})),this.engine.transition(ht(this.channels.slice(0),this.groups.slice(0)))},e.prototype.unsubscribeAll=function(){this.channels=[],this.groups=[],this.engine.transition(ht(this.channels.slice(0),this.groups.slice(0)))},e.prototype.reconnect=function(){this.engine.transition(dt())},e.prototype.disconnect=function(){this.engine.transition(ft())},e}(),Gt=function(){function e(e){var t=this,r=e.networking,i=e.cbor,o=new _({setup:e});this._config=o;var s=new w({config:o}),c=e.cryptography;r.init(o);var l=new F(o,i);this._tokenManager=l;var p=new R({maximumSamplesCount:6e4});this._telemetryManager=p;var h={config:o,networking:r,crypto:s,cryptography:c,tokenManager:l,telemetryManager:p,PubNubFile:e.PubNubFile};this.File=e.PubNubFile,this.encryptFile=function(e,n){return c.encryptFile(e,n,t.File)},this.decryptFile=function(e,n){return c.decryptFile(e,n,t.File)};var f=J.bind(this,h,qe),d=J.bind(this,h,re),g=J.bind(this,h,oe),y=J.bind(this,h,ae),b=J.bind(this,h,ze),v=new K;if(this._listenerManager=v,this.iAmHere=J.bind(this,h,oe),this.iAmAway=J.bind(this,h,re),this.setPresenceState=J.bind(this,h,ae),this.handshake=J.bind(this,h,Ve),this.receiveMessages=J.bind(this,h,Je),!0===o.enableSubscribeBeta){var m=new Dt({handshake:this.handshake,receiveEvents:this.receiveMessages});this.subscribe=m.subscribe.bind(m),this.unsubscribe=m.unsubscribe.bind(m),this.eventEngine=m}else{var O=new M({timeEndpoint:f,leaveEndpoint:d,heartbeatEndpoint:g,setStateEndpoint:y,subscribeEndpoint:b,crypto:h.crypto,config:h.config,listenerManager:v,getFileUrl:function(e){return ye(h,e)}});this.subscribe=O.adaptSubscribeChange.bind(O),this.unsubscribe=O.adaptUnsubscribeChange.bind(O),this.disconnect=O.disconnect.bind(O),this.reconnect=O.reconnect.bind(O),this.unsubscribeAll=O.unsubscribeAll.bind(O),this.getSubscribedChannels=O.getSubscribedChannels.bind(O),this.getSubscribedChannelGroups=O.getSubscribedChannelGroups.bind(O),this.setState=O.adaptStateChange.bind(O),this.presence=O.adaptPresenceChange.bind(O),this.destroy=function(e){O.unsubscribeAll(e),O.disconnect()}}this.addListener=v.addListener.bind(v),this.removeListener=v.removeListener.bind(v),this.removeAllListeners=v.removeAllListeners.bind(v),this.parseToken=l.parseToken.bind(l),this.setToken=l.setToken.bind(l),this.getToken=l.getToken.bind(l),this.channelGroups={listGroups:J.bind(this,h,Q),listChannels:J.bind(this,h,Y),addChannels:J.bind(this,h,W),removeChannels:J.bind(this,h,X),deleteGroup:J.bind(this,h,$)},this.push={addChannels:J.bind(this,h,Z),removeChannels:J.bind(this,h,ee),deleteDevice:J.bind(this,h,ne),listChannels:J.bind(this,h,te)},this.hereNow=J.bind(this,h,ue),this.whereNow=J.bind(this,h,ie),this.getState=J.bind(this,h,se),this.grant=J.bind(this,h,je),this.grantToken=J.bind(this,h,Ue),this.audit=J.bind(this,h,Me),this.revokeToken=J.bind(this,h,xe),this.publish=J.bind(this,h,De),this.fire=function(e,n){return e.replicate=!1,e.storeInHistory=!1,t.publish(e,n)},this.signal=J.bind(this,h,Ge),this.history=J.bind(this,h,Fe),this.deleteMessages=J.bind(this,h,Le),this.messageCounts=J.bind(this,h,Be),this.fetchMessages=J.bind(this,h,He),this.addMessageAction=J.bind(this,h,ce),this.removeMessageAction=J.bind(this,h,le),this.getMessageActions=J.bind(this,h,pe),this.listFiles=J.bind(this,h,he);var P=J.bind(this,h,fe);this.publishFile=J.bind(this,h,de),this.sendFile=ge({generateUploadUrl:P,publishFile:this.publishFile,modules:h}),this.getFileUrl=function(e){return ye(h,e)},this.downloadFile=J.bind(this,h,be),this.deleteFile=J.bind(this,h,ve),this.objects={getAllUUIDMetadata:J.bind(this,h,me),getUUIDMetadata:J.bind(this,h,_e),setUUIDMetadata:J.bind(this,h,Oe),removeUUIDMetadata:J.bind(this,h,Pe),getAllChannelMetadata:J.bind(this,h,Se),getChannelMetadata:J.bind(this,h,we),setChannelMetadata:J.bind(this,h,Te),removeChannelMetadata:J.bind(this,h,ke),getChannelMembers:J.bind(this,h,Ne),setChannelMembers:function(e){for(var r=[],i=1;i<arguments.length;i++)r[i-1]=arguments[i];return J.call.apply(J,u([t,h,Ce,n({type:"set"},e)],a(r),!1))},removeChannelMembers:function(e){for(var r=[],i=1;i<arguments.length;i++)r[i-1]=arguments[i];return J.call.apply(J,u([t,h,Ce,n({type:"delete"},e)],a(r),!1))},getMemberships:J.bind(this,h,Ee),setMemberships:function(e){for(var r=[],i=1;i<arguments.length;i++)r[i-1]=arguments[i];return J.call.apply(J,u([t,h,Ae,n({type:"set"},e)],a(r),!1))},removeMemberships:function(e){for(var r=[],i=1;i<arguments.length;i++)r[i-1]=arguments[i];return J.call.apply(J,u([t,h,Ae,n({type:"delete"},e)],a(r),!1))}},this.createUser=function(e){return t.objects.setUUIDMetadata({uuid:e.userId,data:e.data,include:e.include})},this.updateUser=this.createUser,this.removeUser=function(e){return t.objects.removeUUIDMetadata({uuid:null==e?void 0:e.userId})},this.fetchUser=function(e){return t.objects.getUUIDMetadata({uuid:null==e?void 0:e.userId,include:null==e?void 0:e.include})},this.fetchUsers=this.objects.getAllUUIDMetadata,this.createSpace=function(e){return t.objects.setChannelMetadata({channel:e.spaceId,data:e.data,include:e.include})},this.updateSpace=this.createSpace,this.removeSpace=function(e){return t.objects.removeChannelMetadata({channel:e.spaceId})},this.fetchSpace=function(e){return t.objects.getChannelMetadata({channel:e.spaceId,include:e.include})},this.fetchSpaces=this.objects.getAllChannelMetadata,this.addMemberships=function(e){var n,r;return"string"==typeof e.spaceId?t.objects.setChannelMembers({channel:e.spaceId,uuids:null===(n=e.users)||void 0===n?void 0:n.map((function(e){return"string"==typeof e?e:{id:e.userId,custom:e.custom,status:e.status}})),limit:0}):t.objects.setMemberships({uuid:e.userId,channels:null===(r=e.spaces)||void 0===r?void 0:r.map((function(e){return"string"==typeof e?e:{id:e.spaceId,custom:e.custom,status:e.status}})),limit:0})},this.updateMemberships=this.addMemberships,this.removeMemberships=function(e){return"string"==typeof e.spaceId?t.objects.removeChannelMembers({channel:e.spaceId,uuids:e.userIds,limit:0}):t.objects.removeMemberships({uuid:e.userId,channels:e.spaceIds,limit:0})},this.fetchMemberships=function(e){return"string"==typeof e.spaceId?t.objects.getChannelMembers({channel:e.spaceId,filter:e.filter,limit:e.limit,page:e.page,include:{customFields:e.include.customFields,UUIDFields:e.include.userFields,customUUIDFields:e.include.customUserFields,totalCount:e.include.totalCount},sort:null!=e.sort?Object.fromEntries(Object.entries(e.sort).map((function(e){var t=a(e,2),n=t[0],r=t[1];return[n.replace("user","uuid"),r]}))):null}).then((function(e){var t;return e.data=null===(t=e.data)||void 0===t?void 0:t.map((function(e){return{user:e.uuid,custom:e.custom,updated:e.updated,eTag:e.eTag}})),e})):t.objects.getMemberships({uuid:e.userId,filter:e.filter,limit:e.limit,page:e.page,include:{customFields:e.include.customFields,channelFields:e.include.spaceFields,customChannelFields:e.include.customSpaceFields,totalCount:e.include.totalCount},sort:null!=e.sort?Object.fromEntries(Object.entries(e.sort).map((function(e){var t=a(e,2),n=t[0],r=t[1];return[n.replace("space","channel"),r]}))):null}).then((function(e){var t;return e.data=null===(t=e.data)||void 0===t?void 0:t.map((function(e){return{space:e.channel,custom:e.custom,updated:e.updated,eTag:e.eTag}})),e}))},this.time=f,this.stop=this.destroy,this.encrypt=s.encrypt.bind(s),this.decrypt=s.decrypt.bind(s),this.getAuthKey=h.config.getAuthKey.bind(h.config),this.setAuthKey=h.config.setAuthKey.bind(h.config),this.setCipherKey=h.config.setCipherKey.bind(h.config),this.getUUID=h.config.getUUID.bind(h.config),this.setUUID=h.config.setUUID.bind(h.config),this.getFilterExpression=h.config.getFilterExpression.bind(h.config),this.setFilterExpression=h.config.setFilterExpression.bind(h.config),this.setHeartbeatInterval=h.config.setHeartbeatInterval.bind(h.config),r.hasModule("proxy")&&(this.setProxy=function(e){h.config.setProxy(e),t.reconnect()})}return e.prototype.getVersion=function(){return this._config.getVersion()},e.prototype._addPnsdkSuffix=function(e,t){this._config._addPnsdkSuffix(e,t)},e.prototype.networkDownDetected=function(){this._listenerManager.announceNetworkDown(),this._config.restore?this.disconnect():this.destroy(!0)},e.prototype.networkUpDetected=function(){this._listenerManager.announceNetworkUp(),this.reconnect()},e.notificationPayload=function(e,t){return new G(e,t)},e.generateUUID=function(){return m()},e.OPERATIONS=j,e.CATEGORIES=A,e}(),Kt=function(){function e(e){var t=this;this._modules={},Object.keys(e).forEach((function(n){t._modules[n]=e[n].bind(t)}))}return e.prototype.init=function(e){this._config=e,Array.isArray(this._config.origin)?this._currentSubDomain=Math.floor(Math.random()*this._config.origin.length):this._currentSubDomain=0,this._coreParams={},this.shiftStandardOrigin()},e.prototype.nextOrigin=function(){var e=this._config.secure?"https://":"http://";if("string"==typeof this._config.origin)return"".concat(e).concat(this._config.origin);this._currentSubDomain+=1,this._currentSubDomain>=this._config.origin.length&&(this._currentSubDomain=0);var t=this._config.origin[this._currentSubDomain];return"".concat(e).concat(t)},e.prototype.hasModule=function(e){return e in this._modules},e.prototype.shiftStandardOrigin=function(){return this._standardOrigin=this.nextOrigin(),this._standardOrigin},e.prototype.getStandardOrigin=function(){return this._standardOrigin},e.prototype.POSTFILE=function(e,t,n){return this._modules.postfile(e,t,n)},e.prototype.GETFILE=function(e,t,n){return this._modules.getfile(e,t,n)},e.prototype.POST=function(e,t,n,r){return this._modules.post(e,t,n,r)},e.prototype.PATCH=function(e,t,n,r){return this._modules.patch(e,t,n,r)},e.prototype.GET=function(e,t,n){return this._modules.get(e,t,n)},e.prototype.DELETE=function(e,t,n){return this._modules.del(e,t,n)},e.prototype._detectErrorCategory=function(e){if("ENOTFOUND"===e.code)return A.PNNetworkIssuesCategory;if("ECONNREFUSED"===e.code)return A.PNNetworkIssuesCategory;if("ECONNRESET"===e.code)return A.PNNetworkIssuesCategory;if("EAI_AGAIN"===e.code)return A.PNNetworkIssuesCategory;if(0===e.status||e.hasOwnProperty("status")&&void 0===e.status)return A.PNNetworkIssuesCategory;if(e.timeout)return A.PNTimeoutCategory;if("ETIMEDOUT"===e.code)return A.PNNetworkIssuesCategory;if(e.response){if(e.response.badRequest)return A.PNBadRequestCategory;if(e.response.forbidden)return A.PNAccessDeniedCategory}return A.PNUnknownCategory},e}();function Ft(e){var t=e.replace(/==?$/,""),n=Math.floor(t.length/4*3),r=new ArrayBuffer(n),i=new Uint8Array(r),o=0;function s(){var e=t.charAt(o++),n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(e);if(-1===n)throw new Error("Illegal character at ".concat(o,": ").concat(t.charAt(o-1)));return n}for(var a=0;a<n;a+=3){var u=s(),c=s(),l=s(),p=s(),h=(63&u)<<2|c>>4,f=(15&c)<<4|l>>2,d=(3&l)<<6|p>>0;i[a]=h,64!=l&&(i[a+1]=f),64!=p&&(i[a+2]=d)}return r}function Lt(e){var t=function(e){return e&&"object"==typeof e&&e.constructor===Object};if(!t(e))return e;var n={};return Object.keys(e).forEach((function(r){var i=function(e){return"string"==typeof e||e instanceof String}(r),o=r,s=e[r];Array.isArray(r)||i&&r.indexOf(",")>=0?o=(i?r.split(","):r).reduce((function(e,t){return e+=String.fromCharCode(t)}),""):(function(e){return"number"==typeof e&&isFinite(e)}(r)||i&&!isNaN(r))&&(o=String.fromCharCode(i?parseInt(r,10):10));n[o]=t(s)?Lt(s):s})),n}var Bt=function(){function e(e,t){this._base64ToBinary=t,this._decode=e}return e.prototype.decodeToken=function(e){var t="";e.length%4==3?t="=":e.length%4==2&&(t="==");var n=e.replace(/-/gi,"+").replace(/_/gi,"/")+t,r=this._decode(this._base64ToBinary(n));if("object"==typeof r)return r},e}(),Ht={exports:{}},qt={exports:{}};!function(e){function t(e){if(e)return function(e){for(var n in t.prototype)e[n]=t.prototype[n];return e}(e)}e.exports=t,t.prototype.on=t.prototype.addEventListener=function(e,t){return this._callbacks=this._callbacks||{},(this._callbacks["$"+e]=this._callbacks["$"+e]||[]).push(t),this},t.prototype.once=function(e,t){function n(){this.off(e,n),t.apply(this,arguments)}return n.fn=t,this.on(e,n),this},t.prototype.off=t.prototype.removeListener=t.prototype.removeAllListeners=t.prototype.removeEventListener=function(e,t){if(this._callbacks=this._callbacks||{},0==arguments.length)return this._callbacks={},this;var n,r=this._callbacks["$"+e];if(!r)return this;if(1==arguments.length)return delete this._callbacks["$"+e],this;for(var i=0;i<r.length;i++)if((n=r[i])===t||n.fn===t){r.splice(i,1);break}return 0===r.length&&delete this._callbacks["$"+e],this},t.prototype.emit=function(e){this._callbacks=this._callbacks||{};for(var t=new Array(arguments.length-1),n=this._callbacks["$"+e],r=1;r<arguments.length;r++)t[r-1]=arguments[r];if(n){r=0;for(var i=(n=n.slice(0)).length;r<i;++r)n[r].apply(this,t)}return this},t.prototype.listeners=function(e){return this._callbacks=this._callbacks||{},this._callbacks["$"+e]||[]},t.prototype.hasListeners=function(e){return!!this.listeners(e).length}}(qt);var zt=Qt;Qt.default=Qt,Qt.stable=tn,Qt.stableStringify=tn;var Vt="[...]",Jt="[Circular]",Wt=[],Xt=[];function $t(){return{depthLimit:Number.MAX_SAFE_INTEGER,edgesLimit:Number.MAX_SAFE_INTEGER}}function Qt(e,t,n,r){var i;void 0===r&&(r=$t()),Zt(e,"",0,[],void 0,0,r);try{i=0===Xt.length?JSON.stringify(e,t,n):JSON.stringify(e,rn(t),n)}catch(e){return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;0!==Wt.length;){var o=Wt.pop();4===o.length?Object.defineProperty(o[0],o[1],o[3]):o[0][o[1]]=o[2]}}return i}function Yt(e,t,n,r){var i=Object.getOwnPropertyDescriptor(r,n);void 0!==i.get?i.configurable?(Object.defineProperty(r,n,{value:e}),Wt.push([r,n,t,i])):Xt.push([t,n,e]):(r[n]=e,Wt.push([r,n,t]))}function Zt(e,t,n,r,i,o,s){var a;if(o+=1,"object"==typeof e&&null!==e){for(a=0;a<r.length;a++)if(r[a]===e)return void Yt(Jt,e,t,i);if(void 0!==s.depthLimit&&o>s.depthLimit)return void Yt(Vt,e,t,i);if(void 0!==s.edgesLimit&&n+1>s.edgesLimit)return void Yt(Vt,e,t,i);if(r.push(e),Array.isArray(e))for(a=0;a<e.length;a++)Zt(e[a],a,a,r,e,o,s);else{var u=Object.keys(e);for(a=0;a<u.length;a++){var c=u[a];Zt(e[c],c,a,r,e,o,s)}}r.pop()}}function en(e,t){return e<t?-1:e>t?1:0}function tn(e,t,n,r){void 0===r&&(r=$t());var i,o=nn(e,"",0,[],void 0,0,r)||e;try{i=0===Xt.length?JSON.stringify(o,t,n):JSON.stringify(o,rn(t),n)}catch(e){return JSON.stringify("[unable to serialize, circular reference is too complex to analyze]")}finally{for(;0!==Wt.length;){var s=Wt.pop();4===s.length?Object.defineProperty(s[0],s[1],s[3]):s[0][s[1]]=s[2]}}return i}function nn(e,t,n,r,i,o,s){var a;if(o+=1,"object"==typeof e&&null!==e){for(a=0;a<r.length;a++)if(r[a]===e)return void Yt(Jt,e,t,i);try{if("function"==typeof e.toJSON)return}catch(e){return}if(void 0!==s.depthLimit&&o>s.depthLimit)return void Yt(Vt,e,t,i);if(void 0!==s.edgesLimit&&n+1>s.edgesLimit)return void Yt(Vt,e,t,i);if(r.push(e),Array.isArray(e))for(a=0;a<e.length;a++)nn(e[a],a,a,r,e,o,s);else{var u={},c=Object.keys(e).sort(en);for(a=0;a<c.length;a++){var l=c[a];nn(e[l],l,a,r,e,o,s),u[l]=e[l]}if(void 0===i)return u;Wt.push([i,t,e]),i[t]=u}r.pop()}}function rn(e){return e=void 0!==e?e:function(e,t){return t},function(t,n){if(Xt.length>0)for(var r=0;r<Xt.length;r++){var i=Xt[r];if(i[1]===t&&i[0]===n){n=i[2],Xt.splice(r,1);break}}return e.call(this,t,n)}}var on=String.prototype.replace,sn=/%20/g,an="RFC3986",un={default:an,formatters:{RFC1738:function(e){return on.call(e,sn,"+")},RFC3986:function(e){return String(e)}},RFC1738:"RFC1738",RFC3986:an},cn=un,ln=Object.prototype.hasOwnProperty,pn=Array.isArray,hn=function(){for(var e=[],t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e}(),fn=function(e,t){for(var n=t&&t.plainObjects?Object.create(null):{},r=0;r<e.length;++r)void 0!==e[r]&&(n[r]=e[r]);return n},dn={arrayToObject:fn,assign:function(e,t){return Object.keys(t).reduce((function(e,n){return e[n]=t[n],e}),e)},combine:function(e,t){return[].concat(e,t)},compact:function(e){for(var t=[{obj:{o:e},prop:"o"}],n=[],r=0;r<t.length;++r)for(var i=t[r],o=i.obj[i.prop],s=Object.keys(o),a=0;a<s.length;++a){var u=s[a],c=o[u];"object"==typeof c&&null!==c&&-1===n.indexOf(c)&&(t.push({obj:o,prop:u}),n.push(c))}return function(e){for(;e.length>1;){var t=e.pop(),n=t.obj[t.prop];if(pn(n)){for(var r=[],i=0;i<n.length;++i)void 0!==n[i]&&r.push(n[i]);t.obj[t.prop]=r}}}(t),e},decode:function(e,t,n){var r=e.replace(/\+/g," ");if("iso-8859-1"===n)return r.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(r)}catch(e){return r}},encode:function(e,t,n,r,i){if(0===e.length)return e;var o=e;if("symbol"==typeof e?o=Symbol.prototype.toString.call(e):"string"!=typeof e&&(o=String(e)),"iso-8859-1"===n)return escape(o).replace(/%u[0-9a-f]{4}/gi,(function(e){return"%26%23"+parseInt(e.slice(2),16)+"%3B"}));for(var s="",a=0;a<o.length;++a){var u=o.charCodeAt(a);45===u||46===u||95===u||126===u||u>=48&&u<=57||u>=65&&u<=90||u>=97&&u<=122||i===cn.RFC1738&&(40===u||41===u)?s+=o.charAt(a):u<128?s+=hn[u]:u<2048?s+=hn[192|u>>6]+hn[128|63&u]:u<55296||u>=57344?s+=hn[224|u>>12]+hn[128|u>>6&63]+hn[128|63&u]:(a+=1,u=65536+((1023&u)<<10|1023&o.charCodeAt(a)),s+=hn[240|u>>18]+hn[128|u>>12&63]+hn[128|u>>6&63]+hn[128|63&u])}return s},isBuffer:function(e){return!(!e||"object"!=typeof e)&&!!(e.constructor&&e.constructor.isBuffer&&e.constructor.isBuffer(e))},isRegExp:function(e){return"[object RegExp]"===Object.prototype.toString.call(e)},maybeMap:function(e,t){if(pn(e)){for(var n=[],r=0;r<e.length;r+=1)n.push(t(e[r]));return n}return t(e)},merge:function e(t,n,r){if(!n)return t;if("object"!=typeof n){if(pn(t))t.push(n);else{if(!t||"object"!=typeof t)return[t,n];(r&&(r.plainObjects||r.allowPrototypes)||!ln.call(Object.prototype,n))&&(t[n]=!0)}return t}if(!t||"object"!=typeof t)return[t].concat(n);var i=t;return pn(t)&&!pn(n)&&(i=fn(t,r)),pn(t)&&pn(n)?(n.forEach((function(n,i){if(ln.call(t,i)){var o=t[i];o&&"object"==typeof o&&n&&"object"==typeof n?t[i]=e(o,n,r):t.push(n)}else t[i]=n})),t):Object.keys(n).reduce((function(t,i){var o=n[i];return ln.call(t,i)?t[i]=e(t[i],o,r):t[i]=o,t}),i)}},gn=dn,yn=un,bn=Object.prototype.hasOwnProperty,vn={brackets:function(e){return e+"[]"},comma:"comma",indices:function(e,t){return e+"["+t+"]"},repeat:function(e){return e}},mn=Array.isArray,_n=String.prototype.split,On=Array.prototype.push,Pn=function(e,t){On.apply(e,mn(t)?t:[t])},Sn=Date.prototype.toISOString,wn=yn.default,Tn={addQueryPrefix:!1,allowDots:!1,charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encoder:gn.encode,encodeValuesOnly:!1,format:wn,formatter:yn.formatters[wn],indices:!1,serializeDate:function(e){return Sn.call(e)},skipNulls:!1,strictNullHandling:!1},kn=function e(t,n,r,i,o,s,a,u,c,l,p,h,f,d){var g,y=t;if("function"==typeof a?y=a(n,y):y instanceof Date?y=l(y):"comma"===r&&mn(y)&&(y=gn.maybeMap(y,(function(e){return e instanceof Date?l(e):e}))),null===y){if(i)return s&&!f?s(n,Tn.encoder,d,"key",p):n;y=""}if("string"==typeof(g=y)||"number"==typeof g||"boolean"==typeof g||"symbol"==typeof g||"bigint"==typeof g||gn.isBuffer(y)){if(s){var b=f?n:s(n,Tn.encoder,d,"key",p);if("comma"===r&&f){for(var v=_n.call(String(y),","),m="",_=0;_<v.length;++_)m+=(0===_?"":",")+h(s(v[_],Tn.encoder,d,"value",p));return[h(b)+"="+m]}return[h(b)+"="+h(s(y,Tn.encoder,d,"value",p))]}return[h(n)+"="+h(String(y))]}var O,P=[];if(void 0===y)return P;if("comma"===r&&mn(y))O=[{value:y.length>0?y.join(",")||null:void 0}];else if(mn(a))O=a;else{var S=Object.keys(y);O=u?S.sort(u):S}for(var w=0;w<O.length;++w){var T=O[w],k="object"==typeof T&&void 0!==T.value?T.value:y[T];if(!o||null!==k){var N=mn(y)?"function"==typeof r?r(n,T):n:n+(c?"."+T:"["+T+"]");Pn(P,e(k,N,r,i,o,s,a,u,c,l,p,h,f,d))}}return P},Nn=dn,Cn=Object.prototype.hasOwnProperty,En=Array.isArray,An={allowDots:!1,allowPrototypes:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decoder:Nn.decode,delimiter:"&",depth:5,ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictNullHandling:!1},Mn=function(e){return e.replace(/&#(\d+);/g,(function(e,t){return String.fromCharCode(parseInt(t,10))}))},jn=function(e,t){return e&&"string"==typeof e&&t.comma&&e.indexOf(",")>-1?e.split(","):e},Rn=function(e,t,n,r){if(e){var i=n.allowDots?e.replace(/\.([^.[]+)/g,"[$1]"):e,o=/(\[[^[\]]*])/g,s=n.depth>0&&/(\[[^[\]]*])/.exec(i),a=s?i.slice(0,s.index):i,u=[];if(a){if(!n.plainObjects&&Cn.call(Object.prototype,a)&&!n.allowPrototypes)return;u.push(a)}for(var c=0;n.depth>0&&null!==(s=o.exec(i))&&c<n.depth;){if(c+=1,!n.plainObjects&&Cn.call(Object.prototype,s[1].slice(1,-1))&&!n.allowPrototypes)return;u.push(s[1])}return s&&u.push("["+i.slice(s.index)+"]"),function(e,t,n,r){for(var i=r?t:jn(t,n),o=e.length-1;o>=0;--o){var s,a=e[o];if("[]"===a&&n.parseArrays)s=[].concat(i);else{s=n.plainObjects?Object.create(null):{};var u="["===a.charAt(0)&&"]"===a.charAt(a.length-1)?a.slice(1,-1):a,c=parseInt(u,10);n.parseArrays||""!==u?!isNaN(c)&&a!==u&&String(c)===u&&c>=0&&n.parseArrays&&c<=n.arrayLimit?(s=[])[c]=i:"__proto__"!==u&&(s[u]=i):s={0:i}}i=s}return i}(u,t,n,r)}},Un={formats:un,parse:function(e,t){var n=function(e){if(!e)return An;if(null!==e.decoder&&void 0!==e.decoder&&"function"!=typeof e.decoder)throw new TypeError("Decoder has to be a function.");if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var t=void 0===e.charset?An.charset:e.charset;return{allowDots:void 0===e.allowDots?An.allowDots:!!e.allowDots,allowPrototypes:"boolean"==typeof e.allowPrototypes?e.allowPrototypes:An.allowPrototypes,arrayLimit:"number"==typeof e.arrayLimit?e.arrayLimit:An.arrayLimit,charset:t,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:An.charsetSentinel,comma:"boolean"==typeof e.comma?e.comma:An.comma,decoder:"function"==typeof e.decoder?e.decoder:An.decoder,delimiter:"string"==typeof e.delimiter||Nn.isRegExp(e.delimiter)?e.delimiter:An.delimiter,depth:"number"==typeof e.depth||!1===e.depth?+e.depth:An.depth,ignoreQueryPrefix:!0===e.ignoreQueryPrefix,interpretNumericEntities:"boolean"==typeof e.interpretNumericEntities?e.interpretNumericEntities:An.interpretNumericEntities,parameterLimit:"number"==typeof e.parameterLimit?e.parameterLimit:An.parameterLimit,parseArrays:!1!==e.parseArrays,plainObjects:"boolean"==typeof e.plainObjects?e.plainObjects:An.plainObjects,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:An.strictNullHandling}}(t);if(""===e||null==e)return n.plainObjects?Object.create(null):{};for(var r="string"==typeof e?function(e,t){var n,r={},i=t.ignoreQueryPrefix?e.replace(/^\?/,""):e,o=t.parameterLimit===1/0?void 0:t.parameterLimit,s=i.split(t.delimiter,o),a=-1,u=t.charset;if(t.charsetSentinel)for(n=0;n<s.length;++n)0===s[n].indexOf("utf8=")&&("utf8=%E2%9C%93"===s[n]?u="utf-8":"utf8=%26%2310003%3B"===s[n]&&(u="iso-8859-1"),a=n,n=s.length);for(n=0;n<s.length;++n)if(n!==a){var c,l,p=s[n],h=p.indexOf("]="),f=-1===h?p.indexOf("="):h+1;-1===f?(c=t.decoder(p,An.decoder,u,"key"),l=t.strictNullHandling?null:""):(c=t.decoder(p.slice(0,f),An.decoder,u,"key"),l=Nn.maybeMap(jn(p.slice(f+1),t),(function(e){return t.decoder(e,An.decoder,u,"value")}))),l&&t.interpretNumericEntities&&"iso-8859-1"===u&&(l=Mn(l)),p.indexOf("[]=")>-1&&(l=En(l)?[l]:l),Cn.call(r,c)?r[c]=Nn.combine(r[c],l):r[c]=l}return r}(e,n):e,i=n.plainObjects?Object.create(null):{},o=Object.keys(r),s=0;s<o.length;++s){var a=o[s],u=Rn(a,r[a],n,"string"==typeof e);i=Nn.merge(i,u,n)}return Nn.compact(i)},stringify:function(e,t){var n,r=e,i=function(e){if(!e)return Tn;if(null!==e.encoder&&void 0!==e.encoder&&"function"!=typeof e.encoder)throw new TypeError("Encoder has to be a function.");var t=e.charset||Tn.charset;if(void 0!==e.charset&&"utf-8"!==e.charset&&"iso-8859-1"!==e.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var n=yn.default;if(void 0!==e.format){if(!bn.call(yn.formatters,e.format))throw new TypeError("Unknown format option provided.");n=e.format}var r=yn.formatters[n],i=Tn.filter;return("function"==typeof e.filter||mn(e.filter))&&(i=e.filter),{addQueryPrefix:"boolean"==typeof e.addQueryPrefix?e.addQueryPrefix:Tn.addQueryPrefix,allowDots:void 0===e.allowDots?Tn.allowDots:!!e.allowDots,charset:t,charsetSentinel:"boolean"==typeof e.charsetSentinel?e.charsetSentinel:Tn.charsetSentinel,delimiter:void 0===e.delimiter?Tn.delimiter:e.delimiter,encode:"boolean"==typeof e.encode?e.encode:Tn.encode,encoder:"function"==typeof e.encoder?e.encoder:Tn.encoder,encodeValuesOnly:"boolean"==typeof e.encodeValuesOnly?e.encodeValuesOnly:Tn.encodeValuesOnly,filter:i,format:n,formatter:r,serializeDate:"function"==typeof e.serializeDate?e.serializeDate:Tn.serializeDate,skipNulls:"boolean"==typeof e.skipNulls?e.skipNulls:Tn.skipNulls,sort:"function"==typeof e.sort?e.sort:null,strictNullHandling:"boolean"==typeof e.strictNullHandling?e.strictNullHandling:Tn.strictNullHandling}}(t);"function"==typeof i.filter?r=(0,i.filter)("",r):mn(i.filter)&&(n=i.filter);var o,s=[];if("object"!=typeof r||null===r)return"";o=t&&t.arrayFormat in vn?t.arrayFormat:t&&"indices"in t?t.indices?"indices":"repeat":"indices";var a=vn[o];n||(n=Object.keys(r)),i.sort&&n.sort(i.sort);for(var u=0;u<n.length;++u){var c=n[u];i.skipNulls&&null===r[c]||Pn(s,kn(r[c],c,a,i.strictNullHandling,i.skipNulls,i.encode?i.encoder:null,i.filter,i.sort,i.allowDots,i.serializeDate,i.format,i.formatter,i.encodeValuesOnly,i.charset))}var l=s.join(i.delimiter),p=!0===i.addQueryPrefix?"?":"";return i.charsetSentinel&&("iso-8859-1"===i.charset?p+="utf8=%26%2310003%3B&":p+="utf8=%E2%9C%93&"),l.length>0?p+l:""}};function xn(e){return xn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},xn(e)}var In=function(e){return null!==e&&"object"===xn(e)};function Dn(e){return Dn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Dn(e)}var Gn=In,Kn=Fn;function Fn(e){if(e)return function(e){for(var t in Fn.prototype)Object.prototype.hasOwnProperty.call(Fn.prototype,t)&&(e[t]=Fn.prototype[t]);return e}(e)}Fn.prototype.clearTimeout=function(){return clearTimeout(this._timer),clearTimeout(this._responseTimeoutTimer),clearTimeout(this._uploadTimeoutTimer),delete this._timer,delete this._responseTimeoutTimer,delete this._uploadTimeoutTimer,this},Fn.prototype.parse=function(e){return this._parser=e,this},Fn.prototype.responseType=function(e){return this._responseType=e,this},Fn.prototype.serialize=function(e){return this._serializer=e,this},Fn.prototype.timeout=function(e){if(!e||"object"!==Dn(e))return this._timeout=e,this._responseTimeout=0,this._uploadTimeout=0,this;for(var t in e)if(Object.prototype.hasOwnProperty.call(e,t))switch(t){case"deadline":this._timeout=e.deadline;break;case"response":this._responseTimeout=e.response;break;case"upload":this._uploadTimeout=e.upload;break;default:console.warn("Unknown timeout option",t)}return this},Fn.prototype.retry=function(e,t){return 0!==arguments.length&&!0!==e||(e=1),e<=0&&(e=0),this._maxRetries=e,this._retries=0,this._retryCallback=t,this};var Ln=new Set(["ETIMEDOUT","ECONNRESET","EADDRINUSE","ECONNREFUSED","EPIPE","ENOTFOUND","ENETUNREACH","EAI_AGAIN"]),Bn=new Set([408,413,429,500,502,503,504,521,522,524]);Fn.prototype._shouldRetry=function(e,t){if(!this._maxRetries||this._retries++>=this._maxRetries)return!1;if(this._retryCallback)try{var n=this._retryCallback(e,t);if(!0===n)return!0;if(!1===n)return!1}catch(e){console.error(e)}if(t&&t.status&&Bn.has(t.status))return!0;if(e){if(e.code&&Ln.has(e.code))return!0;if(e.timeout&&"ECONNABORTED"===e.code)return!0;if(e.crossDomain)return!0}return!1},Fn.prototype._retry=function(){return this.clearTimeout(),this.req&&(this.req=null,this.req=this.request()),this._aborted=!1,this.timedout=!1,this.timedoutError=null,this._end()},Fn.prototype.then=function(e,t){var n=this;if(!this._fullfilledPromise){var r=this;this._endCalled&&console.warn("Warning: superagent request was sent twice, because both .end() and .then() were called. Never call .end() if you use promises"),this._fullfilledPromise=new Promise((function(e,t){r.on("abort",(function(){if(!(n._maxRetries&&n._maxRetries>n._retries))if(n.timedout&&n.timedoutError)t(n.timedoutError);else{var e=new Error("Aborted");e.code="ABORTED",e.status=n.status,e.method=n.method,e.url=n.url,t(e)}})),r.end((function(n,r){n?t(n):e(r)}))}))}return this._fullfilledPromise.then(e,t)},Fn.prototype.catch=function(e){return this.then(void 0,e)},Fn.prototype.use=function(e){return e(this),this},Fn.prototype.ok=function(e){if("function"!=typeof e)throw new Error("Callback required");return this._okCallback=e,this},Fn.prototype._isResponseOK=function(e){return!!e&&(this._okCallback?this._okCallback(e):e.status>=200&&e.status<300)},Fn.prototype.get=function(e){return this._header[e.toLowerCase()]},Fn.prototype.getHeader=Fn.prototype.get,Fn.prototype.set=function(e,t){if(Gn(e)){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&this.set(n,e[n]);return this}return this._header[e.toLowerCase()]=t,this.header[e]=t,this},Fn.prototype.unset=function(e){return delete this._header[e.toLowerCase()],delete this.header[e],this},Fn.prototype.field=function(e,t){if(null==e)throw new Error(".field(name, val) name can not be empty");if(this._data)throw new Error(".field() can't be used if .send() is used. Please use only .send() or only .field() & .attach()");if(Gn(e)){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&this.field(n,e[n]);return this}if(Array.isArray(t)){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&this.field(e,t[r]);return this}if(null==t)throw new Error(".field(name, val) val can not be empty");return"boolean"==typeof t&&(t=String(t)),this._getFormData().append(e,t),this},Fn.prototype.abort=function(){return this._aborted||(this._aborted=!0,this.xhr&&this.xhr.abort(),this.req&&this.req.abort(),this.clearTimeout(),this.emit("abort")),this},Fn.prototype._auth=function(e,t,n,r){switch(n.type){case"basic":this.set("Authorization","Basic ".concat(r("".concat(e,":").concat(t))));break;case"auto":this.username=e,this.password=t;break;case"bearer":this.set("Authorization","Bearer ".concat(e))}return this},Fn.prototype.withCredentials=function(e){return void 0===e&&(e=!0),this._withCredentials=e,this},Fn.prototype.redirects=function(e){return this._maxRedirects=e,this},Fn.prototype.maxResponseSize=function(e){if("number"!=typeof e)throw new TypeError("Invalid argument");return this._maxResponseSize=e,this},Fn.prototype.toJSON=function(){return{method:this.method,url:this.url,data:this._data,headers:this._header}},Fn.prototype.send=function(e){var t=Gn(e),n=this._header["content-type"];if(this._formData)throw new Error(".send() can't be used if .attach() or .field() is used. Please use only .send() or only .field() & .attach()");if(t&&!this._data)Array.isArray(e)?this._data=[]:this._isHost(e)||(this._data={});else if(e&&this._data&&this._isHost(this._data))throw new Error("Can't merge these send calls");if(t&&Gn(this._data))for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(this._data[r]=e[r]);else"string"==typeof e?(n||this.type("form"),(n=this._header["content-type"])&&(n=n.toLowerCase().trim()),this._data="application/x-www-form-urlencoded"===n?this._data?"".concat(this._data,"&").concat(e):e:(this._data||"")+e):this._data=e;return!t||this._isHost(e)||n||this.type("json"),this},Fn.prototype.sortQuery=function(e){return this._sort=void 0===e||e,this},Fn.prototype._finalizeQueryString=function(){var e=this._query.join("&");if(e&&(this.url+=(this.url.includes("?")?"&":"?")+e),this._query.length=0,this._sort){var t=this.url.indexOf("?");if(t>=0){var n=this.url.slice(t+1).split("&");"function"==typeof this._sort?n.sort(this._sort):n.sort(),this.url=this.url.slice(0,t)+"?"+n.join("&")}}},Fn.prototype._appendQueryString=function(){console.warn("Unsupported")},Fn.prototype._timeoutError=function(e,t,n){if(!this._aborted){var r=new Error("".concat(e+t,"ms exceeded"));r.timeout=t,r.code="ECONNABORTED",r.errno=n,this.timedout=!0,this.timedoutError=r,this.abort(),this.callback(r)}},Fn.prototype._setTimeouts=function(){var e=this;this._timeout&&!this._timer&&(this._timer=setTimeout((function(){e._timeoutError("Timeout of ",e._timeout,"ETIME")}),this._timeout)),this._responseTimeout&&!this._responseTimeoutTimer&&(this._responseTimeoutTimer=setTimeout((function(){e._timeoutError("Response timeout of ",e._responseTimeout,"ETIMEDOUT")}),this._responseTimeout))};var Hn={};function qn(e,t){var n;if("undefined"==typeof Symbol||null==e[Symbol.iterator]){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"==typeof e)return zn(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return zn(e,t)}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,i=function(){};return{s:i,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:i}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,s=!0,a=!1;return{s:function(){n=e[Symbol.iterator]()},n:function(){var e=n.next();return s=e.done,e},e:function(e){a=!0,o=e},f:function(){try{s||null==n.return||n.return()}finally{if(a)throw o}}}}function zn(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}Hn.type=function(e){return e.split(/ *; */).shift()},Hn.params=function(e){var t,n={},r=qn(e.split(/ *; */));try{for(r.s();!(t=r.n()).done;){var i=t.value.split(/ *= */),o=i.shift(),s=i.shift();o&&s&&(n[o]=s)}}catch(e){r.e(e)}finally{r.f()}return n},Hn.parseLinks=function(e){var t,n={},r=qn(e.split(/ *, */));try{for(r.s();!(t=r.n()).done;){var i=t.value.split(/ *; */),o=i[0].slice(1,-1);n[i[1].split(/ *= */)[1].slice(1,-1)]=o}}catch(e){r.e(e)}finally{r.f()}return n},Hn.cleanHeader=function(e,t){return delete e["content-type"],delete e["content-length"],delete e["transfer-encoding"],delete e.host,t&&(delete e.authorization,delete e.cookie),e};var Vn=Hn,Jn=Wn;function Wn(e){if(e)return function(e){for(var t in Wn.prototype)Object.prototype.hasOwnProperty.call(Wn.prototype,t)&&(e[t]=Wn.prototype[t]);return e}(e)}function Xn(e){return function(e){if(Array.isArray(e))return $n(e)}(e)||function(e){if("undefined"!=typeof Symbol&&Symbol.iterator in Object(e))return Array.from(e)}(e)||function(e,t){if(!e)return;if("string"==typeof e)return $n(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return $n(e,t)}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function $n(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Qn(){this._defaults=[]}Wn.prototype.get=function(e){return this.header[e.toLowerCase()]},Wn.prototype._setHeaderProperties=function(e){var t=e["content-type"]||"";this.type=Vn.type(t);var n=Vn.params(t);for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(this[r]=n[r]);this.links={};try{e.link&&(this.links=Vn.parseLinks(e.link))}catch(e){}},Wn.prototype._setStatusProperties=function(e){var t=e/100|0;this.statusCode=e,this.status=this.statusCode,this.statusType=t,this.info=1===t,this.ok=2===t,this.redirect=3===t,this.clientError=4===t,this.serverError=5===t,this.error=(4===t||5===t)&&this.toError(),this.created=201===e,this.accepted=202===e,this.noContent=204===e,this.badRequest=400===e,this.unauthorized=401===e,this.notAcceptable=406===e,this.forbidden=403===e,this.notFound=404===e,this.unprocessableEntity=422===e},["use","on","once","set","query","type","accept","auth","withCredentials","sortQuery","retry","ok","redirects","timeout","buffer","serialize","parse","ca","key","pfx","cert","disableTLSCerts"].forEach((function(e){Qn.prototype[e]=function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return this._defaults.push({fn:e,args:n}),this}})),Qn.prototype._setDefaults=function(e){this._defaults.forEach((function(t){e[t.fn].apply(e,Xn(t.args))}))};var Yn=Qn;!function(e,t){function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}var r;"undefined"!=typeof window?r=window:"undefined"==typeof self?(console.warn("Using browser-only version of superagent in non-browser environment"),r=void 0):r=self;var i=qt.exports,o=zt,s=Un,a=Kn,u=In,c=Jn,l=Yn;function p(){}e.exports=function(e,n){return"function"==typeof n?new t.Request("GET",e).end(n):1===arguments.length?new t.Request("GET",e):new t.Request(e,n)};var h=t=e.exports;t.Request=m,h.getXHR=function(){if(r.XMLHttpRequest&&(!r.location||"file:"!==r.location.protocol||!r.ActiveXObject))return new XMLHttpRequest;try{return new ActiveXObject("Microsoft.XMLHTTP")}catch(e){}try{return new ActiveXObject("Msxml2.XMLHTTP.6.0")}catch(e){}try{return new ActiveXObject("Msxml2.XMLHTTP.3.0")}catch(e){}try{return new ActiveXObject("Msxml2.XMLHTTP")}catch(e){}throw new Error("Browser-only version of superagent could not find XHR")};var f="".trim?function(e){return e.trim()}:function(e){return e.replace(/(^\s*|\s*$)/g,"")};function d(e){if(!u(e))return e;var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&g(t,n,e[n]);return t.join("&")}function g(e,t,n){if(void 0!==n)if(null!==n)if(Array.isArray(n))n.forEach((function(n){g(e,t,n)}));else if(u(n))for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&g(e,"".concat(t,"[").concat(r,"]"),n[r]);else e.push(encodeURI(t)+"="+encodeURIComponent(n));else e.push(encodeURI(t))}function y(e){for(var t,n,r={},i=e.split("&"),o=0,s=i.length;o<s;++o)-1===(n=(t=i[o]).indexOf("="))?r[decodeURIComponent(t)]="":r[decodeURIComponent(t.slice(0,n))]=decodeURIComponent(t.slice(n+1));return r}function b(e){return/[/+]json($|[^-\w])/i.test(e)}function v(e){this.req=e,this.xhr=this.req.xhr,this.text="HEAD"!==this.req.method&&(""===this.xhr.responseType||"text"===this.xhr.responseType)||void 0===this.xhr.responseType?this.xhr.responseText:null,this.statusText=this.req.xhr.statusText;var t=this.xhr.status;1223===t&&(t=204),this._setStatusProperties(t),this.headers=function(e){for(var t,n,r,i,o=e.split(/\r?\n/),s={},a=0,u=o.length;a<u;++a)-1!==(t=(n=o[a]).indexOf(":"))&&(r=n.slice(0,t).toLowerCase(),i=f(n.slice(t+1)),s[r]=i);return s}(this.xhr.getAllResponseHeaders()),this.header=this.headers,this.header["content-type"]=this.xhr.getResponseHeader("content-type"),this._setHeaderProperties(this.header),null===this.text&&e._responseType?this.body=this.xhr.response:this.body="HEAD"===this.req.method?null:this._parseBody(this.text?this.text:this.xhr.response)}function m(e,t){var n=this;this._query=this._query||[],this.method=e,this.url=t,this.header={},this._header={},this.on("end",(function(){var e,t=null,r=null;try{r=new v(n)}catch(e){return(t=new Error("Parser is unable to parse the response")).parse=!0,t.original=e,n.xhr?(t.rawResponse=void 0===n.xhr.responseType?n.xhr.responseText:n.xhr.response,t.status=n.xhr.status?n.xhr.status:null,t.statusCode=t.status):(t.rawResponse=null,t.status=null),n.callback(t)}n.emit("response",r);try{n._isResponseOK(r)||(e=new Error(r.statusText||r.text||"Unsuccessful HTTP response"))}catch(t){e=t}e?(e.original=t,e.response=r,e.status=r.status,n.callback(e,r)):n.callback(null,r)}))}function _(e,t,n){var r=h("DELETE",e);return"function"==typeof t&&(n=t,t=null),t&&r.send(t),n&&r.end(n),r}h.serializeObject=d,h.parseString=y,h.types={html:"text/html",json:"application/json",xml:"text/xml",urlencoded:"application/x-www-form-urlencoded",form:"application/x-www-form-urlencoded","form-data":"application/x-www-form-urlencoded"},h.serialize={"application/x-www-form-urlencoded":s.stringify,"application/json":o},h.parse={"application/x-www-form-urlencoded":y,"application/json":JSON.parse},c(v.prototype),v.prototype._parseBody=function(e){var t=h.parse[this.type];return this.req._parser?this.req._parser(this,e):(!t&&b(this.type)&&(t=h.parse["application/json"]),t&&e&&(e.length>0||e instanceof Object)?t(e):null)},v.prototype.toError=function(){var e=this.req,t=e.method,n=e.url,r="cannot ".concat(t," ").concat(n," (").concat(this.status,")"),i=new Error(r);return i.status=this.status,i.method=t,i.url=n,i},h.Response=v,i(m.prototype),a(m.prototype),m.prototype.type=function(e){return this.set("Content-Type",h.types[e]||e),this},m.prototype.accept=function(e){return this.set("Accept",h.types[e]||e),this},m.prototype.auth=function(e,t,r){1===arguments.length&&(t=""),"object"===n(t)&&null!==t&&(r=t,t=""),r||(r={type:"function"==typeof btoa?"basic":"auto"});var i=function(e){if("function"==typeof btoa)return btoa(e);throw new Error("Cannot use basic auth, btoa is not a function")};return this._auth(e,t,r,i)},m.prototype.query=function(e){return"string"!=typeof e&&(e=d(e)),e&&this._query.push(e),this},m.prototype.attach=function(e,t,n){if(t){if(this._data)throw new Error("superagent can't mix .send() and .attach()");this._getFormData().append(e,t,n||t.name)}return this},m.prototype._getFormData=function(){return this._formData||(this._formData=new r.FormData),this._formData},m.prototype.callback=function(e,t){if(this._shouldRetry(e,t))return this._retry();var n=this._callback;this.clearTimeout(),e&&(this._maxRetries&&(e.retries=this._retries-1),this.emit("error",e)),n(e,t)},m.prototype.crossDomainError=function(){var e=new Error("Request has been terminated\nPossible causes: the network is offline, Origin is not allowed by Access-Control-Allow-Origin, the page is being unloaded, etc.");e.crossDomain=!0,e.status=this.status,e.method=this.method,e.url=this.url,this.callback(e)},m.prototype.agent=function(){return console.warn("This is not supported in browser version of superagent"),this},m.prototype.ca=m.prototype.agent,m.prototype.buffer=m.prototype.ca,m.prototype.write=function(){throw new Error("Streaming is not supported in browser version of superagent")},m.prototype.pipe=m.prototype.write,m.prototype._isHost=function(e){return e&&"object"===n(e)&&!Array.isArray(e)&&"[object Object]"!==Object.prototype.toString.call(e)},m.prototype.end=function(e){this._endCalled&&console.warn("Warning: .end() was called twice. This is not supported in superagent"),this._endCalled=!0,this._callback=e||p,this._finalizeQueryString(),this._end()},m.prototype._setUploadTimeout=function(){var e=this;this._uploadTimeout&&!this._uploadTimeoutTimer&&(this._uploadTimeoutTimer=setTimeout((function(){e._timeoutError("Upload timeout of ",e._uploadTimeout,"ETIMEDOUT")}),this._uploadTimeout))},m.prototype._end=function(){if(this._aborted)return this.callback(new Error("The request has been aborted even before .end() was called"));var e=this;this.xhr=h.getXHR();var t=this.xhr,n=this._formData||this._data;this._setTimeouts(),t.onreadystatechange=function(){var n=t.readyState;if(n>=2&&e._responseTimeoutTimer&&clearTimeout(e._responseTimeoutTimer),4===n){var r;try{r=t.status}catch(e){r=0}if(!r){if(e.timedout||e._aborted)return;return e.crossDomainError()}e.emit("end")}};var r=function(t,n){n.total>0&&(n.percent=n.loaded/n.total*100,100===n.percent&&clearTimeout(e._uploadTimeoutTimer)),n.direction=t,e.emit("progress",n)};if(this.hasListeners("progress"))try{t.addEventListener("progress",r.bind(null,"download")),t.upload&&t.upload.addEventListener("progress",r.bind(null,"upload"))}catch(e){}t.upload&&this._setUploadTimeout();try{this.username&&this.password?t.open(this.method,this.url,!0,this.username,this.password):t.open(this.method,this.url,!0)}catch(e){return this.callback(e)}if(this._withCredentials&&(t.withCredentials=!0),!this._formData&&"GET"!==this.method&&"HEAD"!==this.method&&"string"!=typeof n&&!this._isHost(n)){var i=this._header["content-type"],o=this._serializer||h.serialize[i?i.split(";")[0]:""];!o&&b(i)&&(o=h.serialize["application/json"]),o&&(n=o(n))}for(var s in this.header)null!==this.header[s]&&Object.prototype.hasOwnProperty.call(this.header,s)&&t.setRequestHeader(s,this.header[s]);this._responseType&&(t.responseType=this._responseType),this.emit("request",this),t.send(void 0===n?null:n)},h.agent=function(){return new l},["GET","POST","OPTIONS","PATCH","PUT","DELETE"].forEach((function(e){l.prototype[e.toLowerCase()]=function(t,n){var r=new h.Request(e,t);return this._setDefaults(r),n&&r.end(n),r}})),l.prototype.del=l.prototype.delete,h.get=function(e,t,n){var r=h("GET",e);return"function"==typeof t&&(n=t,t=null),t&&r.query(t),n&&r.end(n),r},h.head=function(e,t,n){var r=h("HEAD",e);return"function"==typeof t&&(n=t,t=null),t&&r.query(t),n&&r.end(n),r},h.options=function(e,t,n){var r=h("OPTIONS",e);return"function"==typeof t&&(n=t,t=null),t&&r.send(t),n&&r.end(n),r},h.del=_,h.delete=_,h.patch=function(e,t,n){var r=h("PATCH",e);return"function"==typeof t&&(n=t,t=null),t&&r.send(t),n&&r.end(n),r},h.post=function(e,t,n){var r=h("POST",e);return"function"==typeof t&&(n=t,t=null),t&&r.send(t),n&&r.end(n),r},h.put=function(e,t,n){var r=h("PUT",e);return"function"==typeof t&&(n=t,t=null),t&&r.send(t),n&&r.end(n),r}}(Ht,Ht.exports);var Zn=Ht.exports;function er(e){var t=(new Date).getTime(),n=(new Date).toISOString(),r=console&&console.log?console:window&&window.console&&window.console.log?window.console:console;r.log("<<<<<"),r.log("[".concat(n,"]"),"\n",e.url,"\n",e.qs),r.log("-----"),e.on("response",(function(n){var i=(new Date).getTime()-t,o=(new Date).toISOString();r.log(">>>>>>"),r.log("[".concat(o," / ").concat(i,"]"),"\n",e.url,"\n",e.qs,"\n",n.text),r.log("-----")}))}function tr(e,t,n){var r=this;this._config.logVerbosity&&(e=e.use(er)),this._config.proxy&&this._modules.proxy&&(e=this._modules.proxy.call(this,e)),this._config.keepAlive&&this._modules.keepAlive&&(e=this._modules.keepAlive(e));var i=e;if(t.abortSignal)var o=t.abortSignal.subscribe((function(){i.abort(),o()}));return!0===t.forceBuffered?i="undefined"==typeof Blob?i.buffer().responseType("arraybuffer"):i.responseType("arraybuffer"):!1===t.forceBuffered&&(i=i.buffer(!1)),(i=i.timeout(t.timeout)).on("abort",(function(){return n({category:A.PNUnknownCategory,error:!0,operation:t.operation,errorData:new Error("Aborted")},null)})),i.end((function(e,i){var o,s={};if(s.error=null!==e,s.operation=t.operation,i&&i.status&&(s.statusCode=i.status),e){if(e.response&&e.response.text&&!r._config.logVerbosity)try{s.errorData=JSON.parse(e.response.text)}catch(t){s.errorData=e}else s.errorData=e;return s.category=r._detectErrorCategory(e),n(s,null)}if(t.ignoreBody)o={headers:i.headers,redirects:i.redirects,response:i};else try{o=JSON.parse(i.text)}catch(e){return s.errorData=i,s.error=!0,n(s,null)}return o.error&&1===o.error&&o.status&&o.message&&o.service?(s.errorData=o,s.statusCode=o.status,s.error=!0,s.category=r._detectErrorCategory(s),n(s,null)):(o.error&&o.error.message&&(s.errorData=o.error),n(s,o))})),i}function nr(e,t,n){return i(this,void 0,void 0,(function(){var r;return o(this,(function(i){switch(i.label){case 0:return r=Zn.post(e),t.forEach((function(e){var t=e.key,n=e.value;r=r.field(t,n)})),r.attach("file",n,{contentType:"application/octet-stream"}),[4,r];case 1:return[2,i.sent()]}}))}))}function rr(e,t,n){var r=Zn.get(this.getStandardOrigin()+t.url).set(t.headers).query(e);return tr.call(this,r,t,n)}function ir(e,t,n){var r=Zn.get(this.getStandardOrigin()+t.url).set(t.headers).query(e);return tr.call(this,r,t,n)}function or(e,t,n,r){var i=Zn.post(this.getStandardOrigin()+n.url).query(e).set(n.headers).send(t);return tr.call(this,i,n,r)}function sr(e,t,n,r){var i=Zn.patch(this.getStandardOrigin()+n.url).query(e).set(n.headers).send(t);return tr.call(this,i,n,r)}function ar(e,t,n){var r=Zn.delete(this.getStandardOrigin()+t.url).set(t.headers).query(e);return tr.call(this,r,t,n)}function ur(e,t){var n=new Uint8Array(e.byteLength+t.byteLength);return n.set(new Uint8Array(e),0),n.set(new Uint8Array(t),e.byteLength),n.buffer}var cr,lr=function(){function e(){}return Object.defineProperty(e.prototype,"algo",{get:function(){return"aes-256-cbc"},enumerable:!1,configurable:!0}),e.prototype.encrypt=function(e,t){return i(this,void 0,void 0,(function(){var n;return o(this,(function(r){switch(r.label){case 0:return[4,this.getKey(e)];case 1:if(n=r.sent(),t instanceof ArrayBuffer)return[2,this.encryptArrayBuffer(n,t)];if("string"==typeof t)return[2,this.encryptString(n,t)];throw new Error("Cannot encrypt this file. In browsers file encryption supports only string or ArrayBuffer")}}))}))},e.prototype.decrypt=function(e,t){return i(this,void 0,void 0,(function(){var n;return o(this,(function(r){switch(r.label){case 0:return[4,this.getKey(e)];case 1:if(n=r.sent(),t instanceof ArrayBuffer)return[2,this.decryptArrayBuffer(n,t)];if("string"==typeof t)return[2,this.decryptString(n,t)];throw new Error("Cannot decrypt this file. In browsers file decryption supports only string or ArrayBuffer")}}))}))},e.prototype.encryptFile=function(e,t,n){return i(this,void 0,void 0,(function(){var r,i,s;return o(this,(function(o){switch(o.label){case 0:return[4,this.getKey(e)];case 1:return r=o.sent(),[4,t.toArrayBuffer()];case 2:return i=o.sent(),[4,this.encryptArrayBuffer(r,i)];case 3:return s=o.sent(),[2,n.create({name:t.name,mimeType:"application/octet-stream",data:s})]}}))}))},e.prototype.decryptFile=function(e,t,n){return i(this,void 0,void 0,(function(){var r,i,s;return o(this,(function(o){switch(o.label){case 0:return[4,this.getKey(e)];case 1:return r=o.sent(),[4,t.toArrayBuffer()];case 2:return i=o.sent(),[4,this.decryptArrayBuffer(r,i)];case 3:return s=o.sent(),[2,n.create({name:t.name,data:s})]}}))}))},e.prototype.getKey=function(e){return i(this,void 0,void 0,(function(){var t,n,r;return o(this,(function(i){switch(i.label){case 0:return t=Buffer.from(e),[4,crypto.subtle.digest("SHA-256",t.buffer)];case 1:return n=i.sent(),r=Buffer.from(Buffer.from(n).toString("hex").slice(0,32),"utf8").buffer,[2,crypto.subtle.importKey("raw",r,"AES-CBC",!0,["encrypt","decrypt"])]}}))}))},e.prototype.encryptArrayBuffer=function(e,t){return i(this,void 0,void 0,(function(){var n,r,i;return o(this,(function(o){switch(o.label){case 0:return n=crypto.getRandomValues(new Uint8Array(16)),r=ur,i=[n.buffer],[4,crypto.subtle.encrypt({name:"AES-CBC",iv:n},e,t)];case 1:return[2,r.apply(void 0,i.concat([o.sent()]))]}}))}))},e.prototype.decryptArrayBuffer=function(e,t){return i(this,void 0,void 0,(function(){var n;return o(this,(function(r){return n=t.slice(0,16),[2,crypto.subtle.decrypt({name:"AES-CBC",iv:n},e,t.slice(16))]}))}))},e.prototype.encryptString=function(e,t){return i(this,void 0,void 0,(function(){var n,r,i,s;return o(this,(function(o){switch(o.label){case 0:return n=crypto.getRandomValues(new Uint8Array(16)),r=Buffer.from(t).buffer,[4,crypto.subtle.encrypt({name:"AES-CBC",iv:n},e,r)];case 1:return i=o.sent(),s=ur(n.buffer,i),[2,Buffer.from(s).toString("utf8")]}}))}))},e.prototype.decryptString=function(e,t){return i(this,void 0,void 0,(function(){var n,r,i,s;return o(this,(function(o){switch(o.label){case 0:return n=Buffer.from(t),r=n.slice(0,16),i=n.slice(16),[4,crypto.subtle.decrypt({name:"AES-CBC",iv:r},e,i)];case 1:return s=o.sent(),[2,Buffer.from(s).toString("utf8")]}}))}))},e.IV_LENGTH=16,e}(),pr=(cr=function(){function e(e){if(e instanceof File)this.data=e,this.name=this.data.name,this.mimeType=this.data.type;else if(e.data){var t=e.data;this.data=new File([t],e.name,{type:e.mimeType}),this.name=e.name,e.mimeType&&(this.mimeType=e.mimeType)}if(void 0===this.data)throw new Error("Couldn't construct a file out of supplied options.");if(void 0===this.name)throw new Error("Couldn't guess filename out of the options. Please provide one.")}return e.create=function(e){return new this(e)},e.prototype.toBuffer=function(){return i(this,void 0,void 0,(function(){return o(this,(function(e){throw new Error("This feature is only supported in Node.js environments.")}))}))},e.prototype.toStream=function(){return i(this,void 0,void 0,(function(){return o(this,(function(e){throw new Error("This feature is only supported in Node.js environments.")}))}))},e.prototype.toFileUri=function(){return i(this,void 0,void 0,(function(){return o(this,(function(e){throw new Error("This feature is only supported in react native environments.")}))}))},e.prototype.toBlob=function(){return i(this,void 0,void 0,(function(){return o(this,(function(e){return[2,this.data]}))}))},e.prototype.toArrayBuffer=function(){return i(this,void 0,void 0,(function(){var e=this;return o(this,(function(t){return[2,new Promise((function(t,n){var r=new FileReader;r.addEventListener("load",(function(){if(r.result instanceof ArrayBuffer)return t(r.result)})),r.addEventListener("error",(function(){n(r.error)})),r.readAsArrayBuffer(e.data)}))]}))}))},e.prototype.toString=function(){return i(this,void 0,void 0,(function(){var e=this;return o(this,(function(t){return[2,new Promise((function(t,n){var r=new FileReader;r.addEventListener("load",(function(){if("string"==typeof r.result)return t(r.result)})),r.addEventListener("error",(function(){n(r.error)})),r.readAsBinaryString(e.data)}))]}))}))},e.prototype.toFile=function(){return i(this,void 0,void 0,(function(){return o(this,(function(e){return[2,this.data]}))}))},e}(),cr.supportsFile="undefined"!=typeof File,cr.supportsBlob="undefined"!=typeof Blob,cr.supportsArrayBuffer="undefined"!=typeof ArrayBuffer,cr.supportsBuffer=!1,cr.supportsStream=!1,cr.supportsString=!0,cr.supportsEncryptFile=!0,cr.supportsFileUri=!1,cr);function hr(e){if(!navigator||!navigator.sendBeacon)return!1;navigator.sendBeacon(e)}var fr=function(e){function n(t){var n=this,r=t.listenToBrowserNetworkEvents,i=void 0===r||r;return t.sdkFamily="Web",t.networking=new Kt({del:ar,get:ir,post:or,patch:sr,sendBeacon:hr,getfile:rr,postfile:nr}),t.cbor=new Bt((function(e){return Lt(p.decode(e))}),Ft),t.PubNubFile=pr,t.cryptography=new lr,n=e.call(this,t)||this,i&&(window.addEventListener("offline",(function(){n.networkDownDetected()})),window.addEventListener("online",(function(){n.networkUpDetected()}))),n}return t(n,e),n}(Gt);return fr}));
