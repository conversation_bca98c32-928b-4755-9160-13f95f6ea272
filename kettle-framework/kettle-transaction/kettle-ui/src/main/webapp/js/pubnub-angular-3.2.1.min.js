!function(e){function n(r){if(t[r])return t[r].exports;var a=t[r]={exports:{},id:r,loaded:!1};return e[r].call(a.exports,a,a.exports,n),a.loaded=!0,a.exports}var t={};return n.m=e,n.c=t,n.p="",n(0)}([function(e,n,t){"use strict";t(1),t(2),t(6),t(7)},function(e,n){"use strict";"function"!=typeof Object.create&&(Object.create=function(){var e=function(){};return function(n){if(arguments.length>1)throw new Error("Second argument not supported");if(n!==Object(n)&&null!==n)throw new TypeError("Argument must be an object or null");if(null===n)throw Error("null [[Prototype]] not supported");e.prototype=n;var t=new e;return e.prototype=null,t}}())},function(e,n,t){"use strict";var r=t(3),a=t(4);angular.module("pubnub.angular.service",[]).factory("Pubnub",["$rootScope",function(e){if(!angular.isDefined(PUBNUB))throw new Error("PUBNUB is not in global scope. Ensure that pubnub.js file is included before pubnub-angular.js");var n={},t={};return n.init=function(e){return n.getInstance(r.default_instance_name).init(e)},n.getInstance=function(s){var o=t[s];return angular.isDefined(o)&&o instanceof a?o:"string"==typeof s&&s.length>0?(t[s]=new a(s,n,e),r.methods_to_delegate.forEach(function(e){t[s].wrapMethod(e),n[e]=function(n){return this.getInstance(r.default_instance_name)[e](n)}}),t[s]):o},n.getEventNameFor=function(e,n,t){return t||(t=r.default_instance_name),[r.pubnub_prefix,t,e,n].join(":")},n.getMessageEventNameFor=function(e,n){return n||(n=r.default_instance_name),[r.pubnub_prefix,n,"subscribe","callback",e].join(":")},n.getPresenceEventNameFor=function(e,n){return n||(n=r.default_instance_name),[r.pubnub_prefix,n,"subscribe","presence",e].join(":")},n.subscribe=function(e){this.getInstance(r.default_instance_name).subscribe(e)},n}])},function(e,n){e.exports={pubnub_prefix:"pubnub",default_instance_name:"default",methods_to_delegate:["history","replay","publish","unsubscribe","here_now","grant","revoke","audit","time","where_now","state","channel_group","channel_group_list_channels","channel_group_list_groups","channel_group_list_namespaces","channel_group_remove_channel","channel_group_remove_group","channel_group_remove_namespace","channel_group_add_channel","channel_group_cloak","set_uuid","get_uuid","uuid","auth","set_cipher_key","get_cipher_key","raw_encrypt","raw_decrypt","set_heartbeat","get_heartbeat","set_heartbeat_interval","get_heartbeat_interval","mobile_gw_provision"],common_callbacks_to_wrap:["callback","error"],subscribe_callbacks_to_wrap:["callback","connect","reconnect","disconnect","error","idle","presence"]}},function(e,n,t){"use strict";function r(e,n){if(!(e instanceof n))throw new TypeError("Cannot call a class as a function")}var a=function(){function e(e,n){for(var t=0;t<n.length;t++){var r=n[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(n,t,r){return t&&e(n.prototype,t),r&&e(n,r),n}}(),s=t(3),o=t(5);e.exports=function(){function e(n,t,a){r(this,e),this.label=n,this.mockingInstance=new o(n,t,a),this.pubnubInstance=null}return a(e,[{key:"init",value:function(e){this.pubnubInstance=new PUBNUB(e)}},{key:"getLabel",value:function(){return this.label}},{key:"subscribe",value:function(e){var n=this.mockingInstance.getCallbacksToMock(e,s.subscribe_callbacks_to_wrap);this.mockingInstance.mockCallbacks(this.getLabel(),"subscribe",e,n),this.getOriginalInstance().subscribe(e)}},{key:"getOriginalInstance",value:function(){if(this.pubnubInstance)return this.pubnubInstance;throw new ReferenceError("Pubnub default instance is not initialized yet. Invoke #init() method first.")}},{key:"wrapMethod",value:function(e){var n=this;this[e]=function(t){if(angular.isObject(t)){var r=n.mockingInstance.getCallbacksToMock(t,s.common_callbacks_to_wrap);n.mockingInstance.mockCallbacks(n.getLabel(),e,t,r)}return n.getOriginalInstance()[e](t)}}}]),e}()},function(e,n){"use strict";function t(e,n){if(!(e instanceof n))throw new TypeError("Cannot call a class as a function")}var r=function(){function e(e,n){for(var t=0;t<n.length;t++){var r=n[t];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(n,t,r){return t&&e(n.prototype,t),r&&e(n,r),n}}();e.exports=function(){function e(n,r,a){t(this,e),this.label=n,this.$rootScope=a,this.service=r}return r(e,[{key:"getCallbacksToMock",value:function(e,n){var t=e.triggerEvents,r=[],a=void 0,s=void 0,o=void 0;if(t===!0)return n;if(angular.isObject(t)){for(a=t.length,o=0;a>o;o++)s=t[o],n.indexOf(s)>=0&&r.push(s);return r}return[]}},{key:"mockCallbacks",value:function(e,n,t,r){var a=void 0,s=void 0,o=r.length,i=void 0;for(i=0;o>i;i++){if(!angular.isObject(t))return;s=r[i],a=t[s],t[s]=this.generateMockedVersionOfCallback(a,s,n,e,t)}}},{key:"generateMockedVersionOfCallback",value:function(e,n,t,r,a){var s=this.$rootScope,o=this.service,i=a.channel||a.channel_group;return function(){if(s.$broadcast.bind.apply(s.$broadcast,[s,o.getEventNameFor(t,n,r)].concat(Array.prototype.slice.call(arguments)))(),n&&angular.isFunction(e)&&e.apply(null,arguments),"subscribe"===t)switch(n){case"callback":s.$broadcast.bind.apply(s.$broadcast,[s,o.getMessageEventNameFor(i,r)].concat(Array.prototype.slice.call(arguments)))();break;case"presence":s.$broadcast.bind.apply(s.$broadcast,[s,o.getPresenceEventNameFor(i,r)].concat(Array.prototype.slice.call(arguments)))()}}}}]),e}()},function(e,n,t){"use strict";var r=t(3);angular.module("pubnub.angular.service").factory("$pubnubChannel",["$rootScope","Pubnub","$q",function(e,n,t){function a(t,s){if(!(this instanceof a))return new a(t,s);if(s=s||{},!t)throw new Error("The channel name is required");if(s.autosubscribe&&"boolean"!=typeof s.autosubscribe)throw new Error("The autosubscribe parameter should be a boolean");if(s.presence&&"boolean"!=typeof s.presence)throw new Error("The presence parameter should be a boolean");if(s.autostore&&"boolean"!=typeof s.autostore)throw new Error("The autostore parameter should be a boolean");var o=this;this._channel=t,this.$messages=[],this._timeTokenFirstMessage=null,this._messagesAllFetched=!1,this._pubnubInstance=s.instance?n.getInstance(s.instance):n.getInstance(r.default_instance_name),this._autoload=null==s.autoload?0:s.autoload,this._presence=null==s.presence?!1:s.presence,this._autosubscribe=null==s.autosubscribe?!0:s.autosubscribe,this._autostore=null==s.autostore?!0:s.autostore,this.$$getPublicMethods(function(e,n){o.$messages[n]=e.bind(o)}),this._unsubscribeHandler=null,0!==this._autoload&&this.$load(this._autoload);var i=["callback","connect","reconnect","disconnect","error","idle"];if(this._presence&&i.push("presence"),this._autosubscribe&&this._pubnubInstance.subscribe({channel:this._channel,triggerEvents:i}),this._autostore){var c=n.getMessageEventNameFor(o._channel,this._pubnubInstance.label);this._unsubscribeHandler=e.$on(c,o.$$newMessage.bind(o))}return this.$messages}return a.prototype={$load:function(n){if(!(n>0&&100>=n))throw new Error("The number of messages to load should be a number between 0 and 100");var r=this,a=t.defer(),s={channel:r._channel,count:n,reverse:!1,callback:function(t){r._timeTokenFirstMessage=t[1],r.$$storeBatch(t[0]),t[0].length<n&&(r._messagesAllFetched=!0),a.resolve(t),e.$digest()},error:function(e){a.reject(e)}};return r._timeTokenFirstMessage&&(s.start=r._timeTokenFirstMessage),r._pubnubInstance.history(s),a.promise},$publish:function(e){var n=this,r=t.defer();return n._pubnubInstance.publish({channel:n._channel,message:e,callback:function(e){r.resolve(e)},error:function(e){r.reject(e)}}),r.promise},$pubnubInstance:function(){return this._pubnubInstance},$channel:function(){return this._channel},$allLoaded:function(){return this._messagesAllFetched},$destroy:function(){this._unsubscribeHandler&&this._unsubscribeHandler(),this.$messages.length=0},$$newMessage:function(n,t){this.$$store(t),e.$digest()},$$store:function(e){this.$messages.push(e)},$$storeBatch:function(e){0===this.$messages.length?angular.extend(this.$messages,e):Array.prototype.unshift.apply(this.$messages,e)},$$getPublicMethods:function(e,n){this.$$getPrototypeMethods(function(t,r){"function"==typeof t&&"_"!==r.charAt(0)&&e.call(n,t,r)})},$$getPrototypeMethods:function(e,n){for(var t={},r=Object.getPrototypeOf({}),a=angular.isFunction(this)&&angular.isObject(this.prototype)?this.prototype:Object.getPrototypeOf(this);a&&a!==r;){for(var s in a)a.hasOwnProperty(s)&&!t.hasOwnProperty(s)&&(t[s]=!0,e.call(n,a[s],s,a));a=Object.getPrototypeOf(a)}}},a.$extend=function(e){if(!angular.isObject(e))throw new Error("The methods parameter should be an object");var n=function t(e,n){return this instanceof a?(a.apply(this,arguments),this.$messages):new t(e,n)};return n.prototype=Object.create(a.prototype),angular.extend(n.prototype,e),n},a}])},function(e,n,t){"use strict";var r=t(3);angular.module("pubnub.angular.service").factory("$pubnubChannelGroup",["$rootScope","$q","Pubnub","$pubnubChannel",function(e,n,t,a){function s(n,o){if(!(this instanceof s))return new s(n,o);var i=this,c=o||{};if(!n)throw new Error("The channel group name is required");if(c.autosubscribe&&"boolean"!=typeof c.autosubscribe)throw new Error("The autosubscribe parameter should be a boolean");if(c.presence&&"boolean"!=typeof c.presence)throw new Error("The presence parameter should be a boolean");if(c.channelExtension&&!angular.isObject(c.channelExtension))throw new Error("The channelExtension should be an object");this._channelGroup=n,this.$channels={},this._pubnubInstance=c.instance?t.getInstance(c.instance):t.getInstance(r.default_instance_name),this._presence=null==c.presence?!1:c.presence,this._autosubscribe=null==c.autosubscribe?!0:c.autosubscribe,this._extendedChannel=c.channelExtension?a.$extend(c.channelExtension):null,this._unsubscribeHandler=null;var u=["callback","connect","reconnect","disconnect","error","idle"];this._presence&&u.push("presence"),this._autosubscribe&&this._pubnubInstance.subscribe({channel_group:this._channelGroup,triggerEvents:u});var l=t.getMessageEventNameFor(i._channelGroup,i._pubnubInstance.label);return this._unsubscribeHandler=e.$on(l,i.$$newMessage.bind(i)),this}return s.prototype={$channel:function(e){if(!angular.isDefined(this.$channels[e])){var n={instance:this._pubnubInstance.label,autosubscribe:!1,presence:!1,autostore:!0},t=this._extendedChannel?new this._extendedChannel(e,n):a(e,n);this.$channels[e]=t}return this.$channels[e]},$pubnubInstance:function(){return this._pubnubInstance},$channelGroup:function(){return this._channelGroup},$destroy:function(){this._unsubscribeHandler();for(var e in this.$channels)this.$channels.hasOwnProperty(e)&&delete this.$channels[e]},$$newMessage:function(e,n,t){var r=t[3];this.$channel(r).$$newMessage(e,n,t)}},s}])}]);