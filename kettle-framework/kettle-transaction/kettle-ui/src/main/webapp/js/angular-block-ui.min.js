/*
 * Copyright (C) 2016, Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Written by Chaayos Technology Team, 2015
 */

/*!
   angular-block-ui v0.2.1
   (c) 2015 (null) McNull https://github.com/McNull/angular-block-ui
   License: MIT
*/
!function(t){function e(e){try{t.module(e)}catch(n){return!1}return!0}function n(t,n,o){function r(){t.$on("$locationChangeStart",function(t){n.$_blockLocationChange&&n.state().blockCount>0&&t.preventDefault()}),t.$on("$locationChangeSuccess",function(){n.$_blockLocationChange=o.blockBrowserNavigation})}if(o.blockBrowserNavigation)if(e("ngRoute"))var c=t.$on("$viewContentLoaded",function(){c(),r()});else r()}var o=t.module("blockUI",[]);o.config(["$provide","$httpProvider",function(t,e){t.decorator("$exceptionHandler",["$delegate","$injector",function(t,e){var n,o;return function(r,c){if(o=o||e.get("blockUIConfig"),o.resetOnException)try{n=n||e.get("blockUI"),n.instances.reset()}catch(i){console.log("$exceptionHandler",r)}t(r,c)}}]),e.interceptors.push("blockUIHttpInterceptor")}]),o.run(["$document","blockUIConfig","$templateCache",function(t,e,n){e.autoInjectBodyBlock&&t.find("body").attr("block-ui","main"),e.template&&(e.templateUrl="$$block-ui-template$$",n.put(e.templateUrl,e.template))}]),o.config(["$provide",function(t){t.decorator("$location",r)}]);var r=["$delegate","blockUI","blockUIConfig",function(e,n,o){function r(t){var o=e[t];e[t]=function(){var t=o.apply(e,arguments);return t===e&&(n.$_blockLocationChange=!1),t}}if(o.blockBrowserNavigation){n.$_blockLocationChange=!0;var c=["url","path","search","hash","state"];t.forEach(c,r)}return e}];o.directive("blockUiContainer",["blockUIConfig","blockUiContainerLinkFn",function(t,e){return{scope:!0,restrict:"A",templateUrl:t.templateUrl,compile:function(){return e}}}]).factory("blockUiContainerLinkFn",["blockUI","blockUIUtils",function(){return function(t,e){var n=e.inheritedData("block-ui");if(!n)throw new Error("No parent block-ui service instance located.");t.state=n.state()}}]),o.directive("blockUi",["blockUiCompileFn",function(t){return{scope:!0,restrict:"A",compile:t}}]).factory("blockUiCompileFn",["blockUiPreLinkFn",function(t){return function(e){return e.append('<div block-ui-container class="block-ui-container"></div>'),{pre:t}}}]).factory("blockUiPreLinkFn",["blockUI","blockUIUtils","blockUIConfig",function(t,e,o){return function(r,c,i){c.hasClass("block-ui")||c.addClass(o.cssClass),i.$observe("blockUiMessageClass",function(t){r.$_blockUiMessageClass=t});var a=i.blockUi||"_"+r.$id,l=t.instances.get(a);if("main"===a)n(r,l,o);else{var s=c.inheritedData("block-ui");s&&(l._parent=s)}r.$on("$destroy",function(){l.release()}),l.addRef(),r.$_blockUiState=l.state(),r.$watch("$_blockUiState.blocking",function(t){c.attr("aria-busy",!!t),c.toggleClass("block-ui-visible",!!t)}),r.$watch("$_blockUiState.blockCount > 0",function(t){c.toggleClass("block-ui-active",!!t)});var u=i.blockUiPattern;if(u){var f=e.buildRegExp(u);l.pattern(f)}c.data("block-ui",l)}}]),o.constant("blockUIConfig",{templateUrl:"angular-block-ui/angular-block-ui.ng.html",delay:250,message:"Loading ...",autoBlock:!0,resetOnException:!0,requestFilter:t.noop,autoInjectBodyBlock:!0,cssClass:"block-ui block-ui-anim-fade",blockBrowserNavigation:!1}),o.factory("blockUIHttpInterceptor",["$q","$injector","blockUIConfig","$templateCache",function(t,e,n,o){function r(){a=a||e.get("blockUI")}function c(t){n.autoBlock&&t&&!t.$_noBlock&&t.$_blocks&&(r(),t.$_blocks.stop())}function i(e){try{c(e.config)}catch(n){console.log("httpRequestError",n)}return t.reject(e)}var a;return{request:function(t){if(n.autoBlock&&("GET"!=t.method||!o.get(t.url))){var e=n.requestFilter(t);e===!1?t.$_noBlock=!0:(r(),t.$_blocks=a.instances.locate(t),t.$_blocks.start(e))}return t},requestError:i,response:function(t){return t&&c(t.config),t},responseError:i}}]),o.factory("blockUI",["blockUIConfig","$timeout","blockUIUtils","$document",function(e,n,o,r){function c(c){var l,u=this,f={id:c,blockCount:0,message:e.message,blocking:!1},k=[];this._id=c,this._refs=0,this.start=function(c){function s(){l=null,f.blocking=!0}c=c||{},t.isString(c)?c={message:c}:t.forEach(a,function(t){if(c[t])throw new Error("The property "+t+" is reserved for the block state.")}),t.extend(f,c),f.message=f.blockCount>0?c.message||f.message||e.message:c.message||e.message,f.blockCount++;var k=t.element(r[0].activeElement);k.length&&o.isElementInBlockScope(k,u)&&(u._restoreFocus=k[0],n(function(){u._restoreFocus&&u._restoreFocus!==i[0]&&u._restoreFocus.blur()})),l||0===e.delay?0===e.delay&&s():l=n(s,e.delay)},this._cancelStartTimeout=function(){l&&(n.cancel(l),l=null)},this.stop=function(){f.blockCount=Math.max(0,--f.blockCount),0===f.blockCount&&u.reset(!0)},this.isBlocking=function(){return f.blocking},this.message=function(t){f.message=t},this.pattern=function(t){return void 0!==t&&(u._pattern=t),u._pattern},this.reset=function(e){if(u._cancelStartTimeout(),f.blockCount=0,f.blocking=!1,u._restoreFocus&&(!r[0].activeElement||r[0].activeElement===i[0])){try{u._restoreFocus.focus()}catch(o){!function(){var t=u._restoreFocus;n(function(){if(t)try{t.focus()}catch(e){}},100)}()}u._restoreFocus=null}try{e&&t.forEach(k,function(t){t()})}finally{k.length=0}},this.done=function(t){k.push(t)},this.state=function(){return f},this.addRef=function(){u._refs+=1},this.release=function(){--u._refs<=0&&s.instances._destroy(u)}}var i=r.find("body"),a=["id","blockCount","blocking"],l=[];l.get=function(t){if(!isNaN(t))throw new Error("BlockUI id cannot be a number");var e=l[t];return e||(e=l[t]=new c(t),l.push(e)),e},l._destroy=function(e){if(t.isString(e)&&(e=l[e]),e){e.reset();var n=o.indexOf(l,e);l.splice(n,1),delete l[e.state().id]}},l.locate=function(t){var e=[];o.forEachFnHook(e,"start"),o.forEachFnHook(e,"stop");for(var n=l.length;n--;){var r=l[n],c=r._pattern;c&&c.test(t.url)&&e.push(r)}return 0===e.length&&e.push(s),e},o.forEachFnHook(l,"reset");var s=l.get("main");return s.addRef(),s.instances=l,s}]),o.factory("blockUIUtils",function(){var e=t.element,n={buildRegExp:function(t){var e,n=t.match(/^\/(.*)\/([gim]*)$/);if(!n)throw Error("Incorrect regular expression format: "+t);return e=new RegExp(n[1],n[2])},forEachFn:function(t,e,n){for(var o=t.length;o--;){var r=t[o];r[e].apply(r,n)}},forEachFnHook:function(t,e){t[e]=function(){n.forEachFn(this,e,arguments)}},isElementInBlockScope:function(t,e){for(var n=t.inheritedData("block-ui");n;){if(n===e)return!0;n=n._parent}return!1},findElement:function(t,o,r){var c=null;if(o(t))c=t;else{var i;i=r?t.parent():t.children();for(var a=i.length;!c&&a--;)c=n.findElement(e(i[a]),o,r)}return c},indexOf:function(t,e,n){for(var o=n||0,r=t.length;r>o;o++)if(t[o]===e)return o;return-1}};return n}),t.module("blockUI").run(["$templateCache",function(t){t.put("angular-block-ui/angular-block-ui.ng.html",'<div class="block-ui-overlay"></div><div class="block-ui-message-container" aria-live="assertive" aria-atomic="true"><div class="block-ui-message" ng-class="$_blockUiMessageClass">{{ state.message }}</div></div>')}])}(angular);
//# sourceMappingURL=angular-block-ui.min.js.map