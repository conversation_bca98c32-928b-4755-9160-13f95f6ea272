
@topPanelRadius: @gridBorderRadius - @gridBorderWidth;

.ui-grid-group-panel {
  .gradient(@headerBackgroundColor, @headerGradientStart, @headerGradientStop);
  border-bottom: 1px solid @borderColor; // #D4D4D4
  .border-radius(@topPanelRadius, 0, 0, @topPanelRadius);
  min-height: 30px;
}
  .ui-grid-group-panel .hidden {
    display: none;
  }
  .ui-grid-group-panel .description {
    margin-top: 5px;
    margin-left: 5px;
  }

.ui-grid-group-list {
  list-style-type: none;
  margin: 0;
  padding: 0;
}