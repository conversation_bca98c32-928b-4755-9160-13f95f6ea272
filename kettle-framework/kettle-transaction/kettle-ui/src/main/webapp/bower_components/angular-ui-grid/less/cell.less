@import 'variables';

.ui-grid-cell {
  overflow: hidden;
  // position: absolute;
  // position: relative; // NOTE: removing so border is visible
  float: left;
  background-color: inherit;
  border-right: @gridBorderWidth solid;
  border-color: @borderColor;
  box-sizing: border-box;

  &:last-child {
    border-right: 0;
  }
}

.ui-grid-cell-contents {
  padding: 5px;
  -moz-box-sizing: border-box;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  white-space: nowrap;
  -ms-text-overflow: ellipsis;
  -o-text-overflow: ellipsis;
  text-overflow: ellipsis;
  overflow: hidden;
  height: 100%;
  // width: 100%;
}

.ui-grid-cell-contents-hidden {
  visibility: hidden;
  width: 0;
  height:0;
  display: none;
}

.ui-grid-row .ui-grid-cell.ui-grid-row-header-cell {
  background-color: @rowHeaderCell;
  border-bottom: solid @gridBorderWidth @borderColor;
}

