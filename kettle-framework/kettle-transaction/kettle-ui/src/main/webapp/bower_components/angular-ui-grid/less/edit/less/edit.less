@import '../../../less/variables';

div.ui-grid-cell {
  input {
    border-radius: inherit;
    padding: 0;
    width: 100%;
    color: inherit;
    height: auto;
    font: inherit;
    outline: none;
  }
  input:focus {
    color: inherit;
    outline: none;
  }
  input[type="checkbox"] {
    margin: 9px 0 0 6px;
    width: auto;
  }
  input.ng-invalid {
    border: @invalidValueBorder;
  }
  input.ng-valid {
    border: @validValueBorder;
  }
}