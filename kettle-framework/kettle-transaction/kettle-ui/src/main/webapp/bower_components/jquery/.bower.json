{"name": "j<PERSON>y", "version": "2.1.4", "main": "dist/jquery.js", "license": "MIT", "ignore": ["**/.*", "build", "dist/cdn", "speed", "test", "*.md", "AUTHORS.txt", "Gruntfile.js", "package.json"], "devDependencies": {"sizzle": "2.1.1-jquery.2.1.2", "requirejs": "2.1.10", "qunit": "1.14.0", "sinon": "1.8.1"}, "keywords": ["j<PERSON>y", "javascript", "library"], "homepage": "https://github.com/jquery/jquery", "_release": "2.1.4", "_resolution": {"type": "version", "tag": "2.1.4", "commit": "7751e69b615c6eca6f783a81e292a55725af6b85"}, "_source": "git://github.com/jquery/jquery.git", "_target": ">= 1.9.1", "_originalSource": "j<PERSON>y"}