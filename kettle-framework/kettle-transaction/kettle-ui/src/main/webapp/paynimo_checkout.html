<!doctype html>
<html>

<head>
    <title>Checkout Demo</title>
    <meta name="viewport" content="user-scalable=no, width=device-width, initial-scale=1" />
    <script src="https://www.paynimo.com/paynimocheckout/client/lib/jquery.min.js" type="text/javascript"></script>
</head>

<body>

    <button id="btnSubmit">Make a Payment</button>
    <script type="text/javascript" src="https://www.paynimo.com/paynimocheckout/server/lib/checkout.js"></script>

    <script type="text/javascript">
        $(document).ready(function() {
            function handleResponse(res) {
                  if (typeof res != 'undefined' && typeof res.paymentMethod != 'undefined' && typeof res.paymentMethod.paymentTransaction != 'undefined' && typeof res.paymentMethod.paymentTransaction.statusCode != 'undefined' && res.paymentMethod.paymentTransaction.statusCode == '0300') {
                      // success block
                  } else if (typeof res != 'undefined' && typeof res.paymentMethod != 'undefined' && typeof res.paymentMethod.paymentTransaction != 'undefined' && typeof res.paymentMethod.paymentTransaction.statusCode != 'undefined' && res.paymentMethod.paymentTransaction.statusCode == '0398') {
                      // initiated block
                  } else {
                      // error block
                  }
            }

            function getConsumerData(){
              var consumerData = {
                  "generateOrderId": "982399237277710",
                  "paidAmount":10,
                  "contactNumber":"7742052349",
                  "customerName":"Shikhar Srivastava",
                  "customerId":"8882401"
              };
              return consumerData;
            }

            function generateHashToken(consumerData){
                var url = "http://localhost:8080/kettle-service/rest/v2/payment-management/payment/ingenico/create";
                var auth = "eyJhbGciOiJIUzI1NiJ9.eyJwYXJ0bmVyTmFtZSI6Im5lby1yZWRpcy1jbGllbnQiLCJlbnZUeXBlIjoiUFJPRCIsInBhc3NDb2RlIjoiOTIwMjYiLCJpYXQiOjE0ODE4MDY1Njh9.yIFAX0uqgY7MO5LaFbwVS6703F9wtauMonOeeI_z4Bw";
                $.ajax({
                    "url":url,
                    "type": "POST",
                    "headers": {
                        'auth-internal':auth
                    },
                    "contentType":"application/json",
                    "dataType": 'json',
                    "data": JSON.stringify(consumerData),
                    "success": hashSuccessCallback
                });
            }

            function hashSuccessCallback(res){
                var configJson = {
                    'tarCall': false,
                    'features': {
                        'showPGResponseMsg': true,
                        'enableExpressPay': true,
                        'enableNewWindowFlow': true    //for hybrid applications please disable this by passing false
                    },
                    'consumerData': {
                        'deviceId': res.deviceId,	//possible values 'WEBSH1', 'WEBSH2' and 'WEBMD5'
                        'token': res.hashToken,
                        'returnUrl': res.callbackUrl,
                        'responseHandler': handleResponse,
                        'paymentMode': 'qr',
                        'merchantLogoUrl': 'https://www.paynimo.com/CompanyDocs/company-logo-md.png',  //provided merchant logo will be displayed
                        'merchantId': res.merchantId,
                        'currency': 'INR',
                        'consumerId': res.customerId,
                        'consumerMobileNo': res.contactNumber,
                        'txnId': res.orderId,   //Unique merchant transaction ID
                        'items': [{
                            'itemId': 'FIRST',
                            'amount': '10',
                            'comAmt': '0'
                        }],
                        'customStyle': {
                            'PRIMARY_COLOR_CODE': '#3977b7',   //merchant primary color code
                            'SECONDARY_COLOR_CODE': '#FFFFFF',   //provide merchant's suitable color code
                            'BUTTON_COLOR_CODE_1': '#1969bb',   //merchant's button background color code
                            'BUTTON_COLOR_CODE_2': '#FFFFFF'   //provide merchant's suitable color code for button text
                        }
                    }
                };

                console.log(JSON.stringify(configJson));

                $.pnCheckout(configJson);
                if(configJson.features.enableNewWindowFlow){
                    pnCheckoutShared.openNewWindow();
                }
            }
            $(document).off('click', '#btnSubmit').on('click', '#btnSubmit', function(e) {
                e.preventDefault();
                generateHashToken(getConsumerData());
            });
        });
    </script>
</body>
</html>
