/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */
'use strict';

angular
    .module('posApp')
    .service('desiChaiService', ['$http', '$rootScope', 'posAPI', 'AppUtil', function ($http, $rootScope, posAPI, AppUtil) {
        var service = {};

        service.redemptionChaiProductIds = [10,11,12,50,14,15,170,1205];
        service.desiChaiProductIds = [10,11,12,50,14,15];
        service.desiChaiFilterIds = [11,12,50,14,15];
        service.desiChaiProducts = [];

        service.baarishWaliChaiProductIds = [1282,1292,1293,1294];
        service.baarishWaliChaiFilterIds = [1292,1293,1294];
        service.baarishWaliChaiProducts = [];

        service.setRecipes = function () {
            service.desiChaiProducts = [];
            service.desiChaiProductIds.map(function (productId) {
                    var p = AppUtil.getProductForId(productId);
                    if(p != null && p != undefined) {
                        service.desiChaiProducts.push(p);
                    }
            });

            service.baarishWaliChaiProducts = [];
            service.baarishWaliChaiProductIds.map(function (productId) {
                var p = AppUtil.getProductForId(productId);
                if(p != null && p != undefined) {
                    service.baarishWaliChaiProducts.push(p);
                }
            });
        };

        service.getDesiChaiProducts = function () {
            return service.desiChaiProducts;
        };

        service.getDesiChaiProductIds = function () {
            return service.desiChaiProductIds;
        };
        
        service.getRedemptionChaiProductIds = function () {
            return service.redemptionChaiProductIds;
        };


        service.getDesiChaiFilterIds = function () {
            return service.desiChaiFilterIds;
        };

        service.getBaarishWaliChaiProducts = function() {
            return service.baarishWaliChaiProducts;
        };

        service.getBaarishWaliChaiProductIds = function() {
            return service.baarishWaliChaiProductIds;
        };

        service.getBaarishWaliChaiFilterIds = function() {
            return service.baarishWaliChaiFilterIds;
        };

        service.getClonedCustomizationInNewDimension = function (orderItem, index) {
            var recipeDetails = orderItem.recipeDetails;
            if(orderItem.orderDetails.composition!=null){
                if (orderItem.orderDetails.composition.variants != null
                    && orderItem.orderDetails.composition.variants.length > 0) {
                    recipeDetails.ingredient.variants.map(function (ing) {
                        ing.details.map(function (detail) {
                            var found = false;
                            orderItem.orderDetails.composition.variants.map(function (detail1) {
                                if(detail.alias==detail1.alias){
                                    found = true;
                                }
                            });
                            detail.selected = found;
                        });
                    });
                }
                if (orderItem.orderDetails.composition.products != null
                    && orderItem.orderDetails.composition.products.length > 0) {
                    recipeDetails.ingredient.products.map(function (pro) {
                        pro.details.map(function (detail) {
                            var found = false;
                            orderItem.orderDetails.composition.products.map(function (detail1) {
                                if(detail.product.productId==detail1.product.productId){
                                    found = true;
                                }
                            });
                            detail.selected = found;
                        });
                    });
                }
            }

            if(orderItem.orderDetails.composition!=null){
                if (orderItem.orderDetails.composition.addons != null && orderItem.orderDetails.composition.addons.length > 0) {
                    recipeDetails.addons.map(function (addon) {
                        var found = false;
                        orderItem.orderDetails.composition.addons.map(function (addon1) {
                            if(addon.product.productId==addon1.product.productId){
                                found = true;
                            }
                        });
                        if(found){
                            addon.selected = found;
                        }
                    });
                }
                if (orderItem.orderDetails.composition.options != null && orderItem.orderDetails.composition.options.length > 0) {
                    recipeDetails.options.map(function (option) {
                        var found = false;
                        orderItem.orderDetails.composition.options.map(function (option1) {
                            if(option.code==option1){
                                found = true;
                            }
                        });
                        if(found){
                            option.selected = true;
                        }
                    });
                }
            }
            return recipeDetails;
        };

        return service;

    }]);