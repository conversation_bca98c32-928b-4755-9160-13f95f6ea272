angular
    .module('posApp')
    .service('inventoryService',
        ['$modal', 'posAPI', 'AppUtil', '$rootScope', '$cookieStore', '$http', function ($modal, posAPI, AppUtil, $rootScope, $cookieStore,$http) {

            var service = {};
            var inventoryData = {};

            service.getInventory = function(callback){
                $http({
                    method:"GET",
                    url:AppUtil.restUrls.order.getInventory,
                    params:{
                        unitId : $rootScope.globals.currentUser.unitId
                    }
                }).then(function (response) {
                    this.inventoryData = response.data;
                    if(callback!=undefined && typeof callback=='function'){
                        callback(this.inventoryData);
                    }
                }, function (err) {
                    AppUtil.myAlert(err.data.errorMessage);
                });
            };


            function calculateInventory(product,price,toBeAdded){
                var key = product.id + ":" + price.dimension;
                for(var index in this.inventoryData.scmMenuProduct){
                    var mappings = this.inventoryData.scmMenuProduct[index].mappings;
                    if (mappings.indexOf(key)!=-1){
                        this.inventoryData.scmProducts.filter(function(product){
                            return product.id == this.inventoryData.scmMenuProduct[index].id;
                        }).forEach(function(product){
                            console.log(":::: Before Updating inventory of :::::::",product.id, product.q);
                            product.q -= toBeAdded;
                            console.log("::::: After Updating inventory of :::::::",product.id, product.q);
                        });
                    }
                }
            }


            service.addToInventory = function(product,quantity){

            };

            service.removeFromInventory = function(product,quantity){

            };

            service.updateInventory = function(productItem,price,quantity){
                if(price==undefined || price==null){
                    price = productItem.prices[0];
                }
                if(quantity==undefined || quantity==null){
                    quantity = 1;
                }
                if(productItem.inventoryTracked || price.recipe.containsCriticalProducts){
                    calculateInventory(productItem,price,quantity);
                }

            };

            return service;
        }]
    );