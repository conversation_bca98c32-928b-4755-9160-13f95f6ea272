/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

(function () {
    'use strict';

    angular
        .module('posApp')
        .factory('AssemblyService', AssemblyService);

    AssemblyService.$inject = ['$http', '$rootScope', '$timeout', 'UserService', 'posAPI', 'AppUtil', 'PrintService'];
    function AssemblyService($http, $rootScope, $timeout, UserService, posAPI, AppUtil, PrintService) {

        var assemblyService = {};
        assemblyService.orders = [];
        assemblyService.orderIdForModal = 0;
        assemblyService.sortOrders = sortOrdersByStates;
        assemblyService.orderStates = {
            1: 'PROCESSING',
            2: 'READY_TO_DISPATCH',
            4: 'SETTLED',
            5: 'CANCELLED_REQUESTED',
            6: 'CANCELLED',
            7: 'DELIVERED',
            8: 'SETTLED'
        };
        var rulesForDelayCheck = {
            'CREATED': 5,
            'PROCESSING': 10,
            'READY_TO_DISPATCH': 15,
        };

        assemblyService.getOrderforModal = function (orderId) {
            return assemblyService.orders[getIndexofOrderId(orderId)];
        };

		assemblyService.testPrinter = function() {
			var testString = "-----------------------" + '\x0A' + "Test Print" + '\x0A' + "-------------------------"
			+ '\x0A' + '\x0A' + '\x0A' + '\x0A' + '\x0A' + '\x0A' + '\x1B' + '\x69';
			if (AppUtil.isAndroid) {
				Android.printTest(testString);
			} else {
				PrintService.printOnBilling(testString, "RAW");
				if (AppUtil.hasSeparateKotPrinting()) {
					PrintService.printOnKot(testString, "RAW");
				}
			}
		};

        assemblyService.renderAllOrders = function (response) {
            var orders = response.plain();
            var orderIds = {};
            for (var index in orders) {
            	if(!AppUtil.isEmptyObject(orders) && orders[index].order.generateOrderId!=null){
                    assemblyService.pushOrder(orders[index]);
                    orderIds[orders[index].order.orderId] = orders[index];
            	} 
            }
            var idsTobeRemoved = [];
            for(var i in assemblyService.orders){
        	if(orderIds[assemblyService.orders[i].order.orderId] == undefined){
        	    idsTobeRemoved.push(assemblyService.orders[i].order.orderId);
        	}
            }
            for(var j in idsTobeRemoved){
        	assemblyService.removeOrder(idsTobeRemoved[j]);
            }
            //reset undelivered messages for this unitId
            posAPI.allUrl('/',AppUtil.restUrls.order.resetUndelivered).post($rootScope.globals.currentUser.unitId)
                .then(function(response){
                    //console.log("Undelivered cache reset");
                },function(err){
                    //console.log(err);
                }
            );
        };

        assemblyService.refresh = function (clicked) {
            if(clicked!=undefined && clicked){
                $rootScope.showFullScreenLoader = true;
            }
            posAPI.allUrl('/',AppUtil.restUrls.order.ordersInLine).post(
                $rootScope.globals.currentUser).then(function(response){
                	assemblyService.renderAllOrders(response);
                    $rootScope.showFullScreenLoader = false;
                }, function (err) {
                    //console.log(err);
                    $rootScope.showFullScreenLoader = false;
                });
        };

        assemblyService.acknowledgeOrder = function (orderId) {
            var reqObj = AppUtil.GetRequest(orderId);
            posAPI.allUrl('/',AppUtil.restUrls.order.acknowledgeOrder).post(reqObj)
                .then(function (response) {
                    if (response!=undefined && response){
                        //console.log("Acknowledged successfully");
                    }else{
                        //console.log("Could not acknowledge order ::: "+orderId);
                    }
                }, function (err) {
                    //console.log(err);
                });
        };

        assemblyService.pushOrder = function (orderInfo) {
            var index = getIndexofOrderId(orderInfo.order.orderId);
            if (index == -1) {
                assemblyService.orders.push(orderInfo);
            } else {
                assemblyService.orders[index] = orderInfo;
                /*assemblyService.orders[index].deliveryDetails = orderInfo.deliveryDetails;
                assemblyService.orders[index].deliveryPartner = orderInfo.deliveryPartner;*/
            }

        };
        assemblyService.getOrders = function () {
            return assemblyService.orders;
        };
        assemblyService.removeOrder = function (orderId) {
            if (orderId != undefined) {
                assemblyService.orders.splice(getIndexofOrderId(orderId), 1);
            }
        };
        assemblyService.updateOrder = function (orderId, orderStatus) {
            if (orderId != undefined) {
                assemblyService.orders[getIndexofOrderId(orderId)].order.status = orderStatus;
            }
        };
        
        assemblyService.markOODOrder = function (orderId, value) {
            if (orderId != undefined) {
                assemblyService.orders[getIndexofOrderId(orderId)].order.ood = value;
            }
        };
        
        assemblyService.getReceipts = function(orderInfo,isReprint){
    		var receiptsToPrint = [];
    		//TODO fix this
        	if(orderInfo.order.source=="COD" || (orderInfo.order.channelPartner == 14) || (orderInfo.order.source=="TAKE_AWAY"
        		&& orderInfo.customer != null && orderInfo.customer.emailId!=null
        		&& orderInfo.customer.emailId.length!=0
        		&& orderInfo.customer.emailId.indexOf("receipt") == -1 )){
        		
        		receiptsToPrint = orderInfo.receipts;
        	}else{
        		for(var index in orderInfo.receipts){
        			if(index != 0){
        				receiptsToPrint.push(orderInfo.receipts[index]);
        			}
        		}
        	}
        	////console.log("receipts",receiptsToPrint);
        	return receiptsToPrint;
        };
        
        assemblyService.printDeliveryRiderSlip = function (orderId) {
			var orderInfo = assemblyService.orders[getIndexofOrderId(orderId)];
			var slipValues = {
				orderId : orderInfo.order.generateOrderId,
				orderTime : orderInfo.order.billingServerTime,
				riderName : orderInfo.deliveryDetails.deliveryBoyName,
				customerName : orderInfo.customer.firstName,
				amount : orderInfo.order.transactionDetail.totalAmount,
			}
			var slipHtml = getRiderSlip(slipValues);
			slipHtml = slipHtml + getPaymentSlip(orderInfo);
			if (AppUtil.hasSeparateKotPrinting()) {
				PrintService.printHTMLOnKot(slipHtml);
			} else {
				PrintService.printHTMLOnBilling(slipHtml);
			}
			return true;
		}
        
        function getPaymentSlip(orderInfo){
        	var text = '<table cellpadding="0" cellspacing="4" width="8cm">';
        	var settlements = orderInfo.order.settlements;
        	var i = 0;
        	for (i in settlements) {
        	     text += '<tr><td>'+settlements[i].modeDetail.description+'</td><td>'+settlements[i].amount+'</td></tr>';
        	}
        	text += '</table>';
        	return text;
        }
        
        function getRiderSlip(slipValues){
        	return '<b>Rider Slip</b><table cellpadding="0" cellspacing="4" width="8cm"><tr><td>OrderNo:</td><td>'+slipValues.orderId+'</td></tr><tr><td>Order Time:</td><td>'+ AppUtil.formatDate(slipValues.orderTime,"yyyy-MM-dd hh:mm:ss") +'</td></tr><tr><td>Rider Name:</td><td>'+slipValues.riderName+'</td></tr><tr><td>Customer Name:</td><td>'+slipValues.customerName+'</td></tr></table>';
        }
        
        assemblyService.printOrder = function (orderId, isReprint) {
            //console.log("reprint" + isReprint);
            var order = assemblyService.orders[getIndexofOrderId(orderId)];
            var printType = order.printType;
            var receipts = assemblyService.getReceipts(order,isReprint);
            if (AppUtil.isAndroid) {
                order.unit = AppUtil.getUnitDetails();
                if(isReprint){
                    Android.printBill(receipts);
                }else{
                    Android.printReceipt(receipts);
                }
			}else {
	            if (isReprint) {
	                ////console.log("reprint if stat" + isReprint);
	                try {
	                    PrintService.printOnBilling(receipts[0], printType);
	                } catch (err) {
	                	 console.log("err service : ",err);
	                    return false;
	                }
	            } else {
					////console.log("reprint else stat" + isReprint);
					try {
						for ( var kotIndex in receipts) {
							if (kotIndex == 0) {
								PrintService.printOnBilling(receipts[kotIndex], printType);
							} else if (kotIndex == receipts.length - 1) {
								if (AppUtil.hasSeparateKotPrinting()) {
									PrintService.printOnKot(receipts[kotIndex], printType);
								} else {
									PrintService.printOnBilling(receipts[kotIndex], printType);
								}	
							} else {
								if (AppUtil.hasSeparateKotPrinting()) {
									PrintService.printOnKot(receipts[kotIndex], printType);
								} else {
									PrintService
											.printOnBilling(receipts[kotIndex], printType);
								}
							}
						}
					} catch (err) {
						console.log("err service : ",err);
						return false;
					}
				}
            }
            return true;
        };
        
        assemblyService.printOrderKOT = function (orderId, isReprint) {
        	//console.log("reprint" + isReprint);
        	var order = assemblyService.orders[getIndexofOrderId(orderId)];
        	var printType = order.printType;
        	var receipts = assemblyService.getReceipts(order,isReprint);
        	if (AppUtil.isAndroid) {
        		//order.unit = AppUtil.getUnitDetails();
        		Android.printKots(receipts);
        	}else {
        		////console.log("reprint else stat" + isReprint);
        		try {
        			for ( var  kotIndex=1;kotIndex<receipts.length-1;kotIndex++) {
        				if (AppUtil.hasSeparateKotPrinting()) {
        					PrintService.printOnKot(receipts[kotIndex], printType);
        				} else {
        					PrintService
        					.printOnBilling(receipts[kotIndex], printType);
        				}
        			}
        		} catch (err) {
        			console.log("err service : ",err);
        			return false;
        		}
        	}
        	return true;
        };

        assemblyService.checkForDelayInAction = function () {
            if (assemblyService.orders != undefined && assemblyService.orders.length > 0) {
                for (var index in assemblyService.orders) {
                    var orderInfo = assemblyService.orders[index];
                    var billCreation = new Date(orderInfo.order.billCreationTime);
                    var difference = Date.now() - billCreation.getTime();
                    var minuteDiff = (difference / (1000 * 60));
                    if (minuteDiff > rulesForDelayCheck[orderInfo.order.status]) {
                        if (AppUtil.isAndroid){
                            Android.playSound();
                        }else {
                            play_interval_sound();
                        }
                        break;
                    }
                }
            }
        };

        function getIndexofOrderId(orderId) {
            if (assemblyService.orders.length != 0) {
                for (var orderInfoIndex in assemblyService.orders) {
                    ////console.log("from getIndexofOrderId");
                   // //console.log(orderInfoIndex);
                    var orderInfo = assemblyService.orders[orderInfoIndex];
                    ////console.log(orderInfo);
                    if (orderInfo.order.orderId == orderId) {
                        return assemblyService.orders.indexOf(orderInfo);
                    }
                }
            }
            return -1;
        }


        function sortOrdersByStates() {
            ////console.log("inside sorting function");
            assemblyService.orders.sort(function (order1, order2) {
                ////console.log("inside if statement of sorting function :: " + order1.order.status);
               // //console.log("inside if statement :: " + assemblyService.orderStates['5']);
                if (order1.order.status == assemblyService.orderStates[5]) {
                    ////console.log("inside if statement of sorting function :: " + order1.order.status);
                    ////console.log("inside if statement :: " + assemblyService.orderStates['5']);
                    return -1;
                } else {
                    return 0;
                }
            });
        }

        return assemblyService;
    }
})();