/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

angular.module('posApp').service('socketUtils', ['$rootScope', '$window', '$location', 'Pubnub', function ($rootScope, $window, $location, Pubnub) {
    var socket = null; //getting object from window context
    var pairingStatus = true;
    var messageListener = [];

    function emitOnSocket(eventName, data, callback) {
        data = JSON.stringify(data);
        if (socket != null) {
            socket.emit(eventName, data, function () {
                ////console.log("socket : sending information....");
                var args = arguments;
                $rootScope.$apply(function () {
                    if (callback) {
                        callback.apply(socket, args);
                    }
                });
            });
        }

    }

    function receiveOnSocket(eventName, callback) {
        if (socket != null) {
            socket.on(eventName, function () {
                ////console.log("socket : waiting for message from nginx server....");
                var args = arguments;
                $rootScope.$apply(function () {
                    callback.apply(socket, args);
                });
            });
        }

    }

    function getChannelName() {
        var currentUser = $rootScope.globals.currentUser;
        var suffix = getEnvType() + '_' + 'CustomerScreenChannel_' + currentUser.unitId + '_' + currentUser.terminalId;
        ////console.log(suffix);
        return suffix;
    }

    function getEnvType() {
        var envType = "DEV";
        if ($location.host().indexOf("internal") != -1) {
            envType = "SPROD";
        } else if ($location.host().indexOf("prod") != -1) {
            envType = "PROD";
        }
        return envType;
    }

    function reSubscribe() {
        Pubnub.unsubscribe({
            channels: [getChannelName()]
        });
        Pubnub.subscribe({
            channels: [getChannelName()],
            triggerEvents: ['message', 'status', 'presence'],
            withPresence: true
        });
    }


    return {
        getPairingStatus: function () {
            return pairingStatus;
        },
        setPairingStatus: function (status) {
            pairingStatus = status;
        },
        getSocket: function (subscriber) {
            ////console.log("Getting object from window object :: ",socket.io.engine.id);
            return socket;
        },
        register: function (subscriber) {
            var publisherKey = null;
            var subscribeKey = null;
            var currentUser = $rootScope.globals.currentUser;
            var date = Date.now();
            var Uuid = currentUser.unitId + '_' + currentUser.terminalId + '_' + date;
            if (getEnvType().toLowerCase().indexOf("prod") != -1) {
                publisherKey = '******************************************';
                subscribeKey = '******************************************';
            } else {
                publisherKey = '******************************************';
                subscribeKey = '******************************************';
            }

            if (!$rootScope.initialized) {
                // Initialize the PubNub service
                Pubnub.init({
                    publishKey: publisherKey,
                    subscribeKey: subscribeKey,
                    uuid:Uuid
                });
                $rootScope.initialized = true;
            }

            /*
                ////console.log("socket : register function called");
                var host = "uat.kettle.chaayos.com";
                if ($location.host().indexOf("prod") != -1) {
                    host = "backup.kettle.chaayos.com";
                }
                if ($location.host().indexOf("dev") != -1) {
                    host = "uat.kettle.chaayos.com";
                }

                var url = $location.protocol() + "://" + host + ":3000";
                ////console.log("node process url :: ",url);
                var options = {
                    "force new connection": true,
                    "reconnection": true,
                    "reconnectionDelay": 2000,                  //starts with 2 secs delay, then 4, 6, 8, until 60 where it stays forever until it reconnects
                    "reconnectionDelayMax": 5000,             //5 seconds maximum delay between connections
                    "reconnectionAttempts": "Infinity",         //to prevent dead clients, having the user to having to manually reconnect after a server restart.
                    "timeout": 10000,                           //before connect_error and connect_timeout are emitted.
                    "transports": ["websocket", "polling"]
                };
                socket = io.connect(url, options);
                socket.on('connect', function () {
                    ////console.log("socket connect called"+socket.io.engine.id);
                    ////console.log("registering client with id on nginx server :::::: "+ socket.io.engine.id);
                    emitOnSocket("register", subscriber);
                });

                socket.on('reconnect', function () {
                    var currentUser = $rootScope.globals.currentUser;
                    var subscriber = {
                        unit: currentUser.unitId,
                        terminal: currentUser.terminalId,
                        type: currentUser.screenType,
                        family: currentUser.unitFamily
                    };
                    emitOnSocket("register", subscriber);
                });

            */
        },
        emitMessage: function (data) {
            try {
                Pubnub.publish({
                        channel: getChannelName(),
                        message: data
                    },
                    function (status, response) {
                        if (status.error) {
                            // handle error
                            console.log("Emit Message Status ", status);
                        } else {
                            //  console.log("Message Published w/ timetoken", Object.keys(data)[0]);
                        }
                    });
            } catch (e) {
                //console.log("Got error",e);
            }
        },
        receiveMessage: function (callback, key) {
            try {
                if (messageListener[key] != undefined || messageListener[key] != null) {
                    Pubnub.removeListener(messageListener[key]);
                    messageListener[key] = null;
                }

                if (messageListener[key] == undefined || messageListener[key] == null) {
                    var listener = {
                        status: function (statusEvent) {
                            //console.log("Receive Message Status Event", statusEvent);
                            if (statusEvent.category == 'PNNetworkUpCategory') {
                                reSubscribe();
                            }
                        },
                        message: function (m) {
                            console.log("Received message from customer screen ::::: ", m.message, key);
                            $rootScope.$apply(function () {
                                if (callback) {
                                    callback(m.message);
                                }
                            });
                        },
                        signal:function(s){
                            console.log("Received signal from customer screen ::::: ", s.message, key);
                            $rootScope.$apply(function () {
                                if (callback) {
                                    callback(s.message);
                                }
                            });
                        },
                        presence: function (m) {
                            //console.log("Receive Message Presence", m)
                        },

                    };
                    Pubnub.addListener(listener);
                    Pubnub.subscribe({
                        channels: [getChannelName()],
                        triggerEvents: ['message', 'status', 'presence'],
                        withPresence: true
                    });
                    messageListener[key] = listener;
                }

            } catch (e) {
                //console.log("Got error",e);
            }

        },
        pairingDone: function (callback) {
            //receiveOnSocket("partnerOnline", callback);
        },
        pairingFailed: function (callback) {
            //receiveOnSocket("partnerOffline", callback);
        },
        removeAllListeners: function (callback) {
            try {
                if ($rootScope.initialized) {
                    Pubnub.unsubscribe({
                        channels: [getChannelName()]
                    });
                    for (var key in messageListener) {
                        Pubnub.removeListener(messageListener[key]);
                    }
                    messageListener = [];
                }
            } catch (e) {
                //console.log(e);
            }
            /*if (socket != null) {
                socket.off("messageReceive");
                socket.off("partnerOffline");
                socket.off("partnerOnline");
            }*/
        },
        disconnect: function () {
            ////console.log('Disconnected all sockets');
            emitOnSocket("end", "end");
            try {
                if ($rootScope.initialized) {
                    Pubnub.unsubscribe({
                        channels: [getChannelName()]
                    });
                }
            } catch (e) {
                //console.log(e);
            }
            /*
            socket = null;
            pairingStatus = false;
            io.j = [];
            io.sockets = [];
            */
        },
        removeListener: function (sourceKey) {
            for (var key in messageListener) {
                if (key == sourceKey) {
                    Pubnub.removeListener(messageListener[key]);
                    messageListener[key] = null;
                }
            }
        }
    };
}]);


