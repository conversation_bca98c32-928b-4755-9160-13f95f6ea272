/**
 * Created by Chaayos on 20-04-2017.
 *
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

angular
    .module('posApp')
    .service(
        'trackingService',
        ['$modal', '$rootScope', '$cookieStore', 'webEngageService', function ($modal, $rootScope, $cookieStore, webEngageService) {

            var service = {};
            service.paymentModes = {};

            service.setDefaultAttributes = function(orderMode, city, locality, outlet){
                // webEngageService.setDefaultAttributes(orderMode, city, locality, outlet);
            };

            service.setPaymentMode = function(data){
                // webEngageService.setPaymentMode(data);
            };

            service.isEmptyObject = function(obj) {
                if (obj != undefined && obj != null) {
                    if (typeof obj == 'string' || typeof obj == 'number')
                        return obj.toString().length == 0;
                    else
                        return Object.keys(obj).length == 0;
                }
                return true;
            };

            service.trackUser = function(data) {
                // try {
                //     !service.isEmptyObject(data) ? webEngageService.trackUser(data) : null;
                // } catch (e) {
                //     console.log(e);
                // }
            };

            service.trackUserData = function(data) {
                // try {
                //     !service.isEmptyObject(data) ? webEngageService.setUserAttributes(data) : null;
                // } catch (e) {
                //     console.log(e);
                // }
            };

            service.logout = function() {
                // try {
                //     webEngageService.logoutUser();
                // } catch (e) {
                //     console.log(e);
                // }
            };

            service.trackSuccessOrder = function(data) {
                // try {
                //     webEngageService.trackEvent("ORDER_SUCCESS_NEW", data);
                // } catch (e) {
                //     console.log(e);
                // }
            };

            service.trackOrderCancel = function(data) {
                // try {
                //     webEngageService.trackEvent("ORDER_CANCEL_NEW", data);
                // } catch (e) {
                //     console.log(e);
                // }
            };

            service.trackCouponTried = function(data){
                // try {
                //     data!=null?webEngageService.trackEvent("COUPON_TRIED", data):null;
                // } catch (e) {
                //     console.log(e);
                // }
            };

            service.trackCouponRemoved = function(data){
                // try {
                //     data!=null?webEngageService.trackEvent("COUPON_REMOVED", data):null;
                // } catch (e) {
                //     console.log(e);
                // }
            };

            service.trackOfferSuccess = function(data){
                // try{
                //     !service.isEmptyObject(data)?webEngageService.trackEvent("OFFER_SUCCESS", data):null;
                // }catch (e){
                //     console.log(e);
                // }
            };

            service.trackOfferFailed = function(data){
                // try{
                //     !service.isEmptyObject(data)?webEngageService.trackEvent("OFFER_FAILED", data):null;
                // }catch (e){
                //     console.log(e);
                // }
            };

            service.trackOTPResent = function (data) {
                // try{
                //     !service.isEmptyObject(data)?webEngageService.trackEvent("OTP_RESENT", data):null;
                // }catch (e){
                //     console.log(e);
                // }
            };

            service.trackEmailRegistered = function (data) {
                // try{
                //     !service.isEmptyObject(data)?webEngageService.trackEvent("EMAIL_REGISTERED", data):null;
                // }catch (e){
                //     console.log(e);
                // }
            };

            return service;
        }]);

angular
    .module('posApp')
    .service(
        'webEngageService',
        [function () {

            var service = {};

            service.isTracking = false;


            service.isEmptyObject = function(obj) {
                if (obj != undefined && obj != null) {
                    if (typeof obj == 'string' || typeof obj == 'number')
                        return obj.toString().length == 0;
                    else
                        return Object.keys(obj).length == 0;
                }
                return true;
            };

            service.getConvertedObject = function(event, data){
                switch(event) {
                    case "ORDER_SUCCESS_NEW":{
                        return service.convertOrderSuccess(data);
                    }
                    case "ORDER_CANCEL_NEW":{
                        return service.convertOrderCancel(data);
                    }
                    case "COUPON_TRIED":{
                        return service.convertCoupon(data);
                    }
                    case "COUPON_REMOVED":{
                        return service.convertCoupon(data);
                    }
                    case "OFFER_SUCCESS":{
                        return service.convertOfferSuccess(data);
                    }
                    case "OFFER_FAILED":{
                        return service.convertOfferFailed(data);
                    }
                    case "OTP_RESENT":{
                        return service.convertOTPResend(data);
                    }
                    case "EMAIL_REGISTERED":{
                        return service.convertEmail(data);
                    }
                    default : {
                        var defaultAttributes = service.getDefaultAttributes();
                        defaultAttributes[event] = data;
                        return defaultAttributes;
                    }
                }
            };

            service.paymentModes = {};

            service.defaultAttributes = {
                orderMode:"",
                city:"",
                locality:"",
                outlet:"",
                orderSrc:"KETTLE",
                host:window.location.origin
                //acquisitionSrc:"",
                //customerId:"",
                //empId:"",
                //deviceId:"",
            };

            service.setDefaultAttributes = function(orderMode, city, locality, outlet){
                orderMode!=null?service.defaultAttributes.orderMode=orderMode:null;
                city!=null?service.defaultAttributes.city=city:null;
                locality!=null?service.defaultAttributes.locality=locality:null;
                outlet!=null?service.defaultAttributes.outlet=outlet:null;
                //service.defaultAttributes.orderSrc="KETTLE";
                //empId!=null?service.defaultAttributes.empId=empId+"":null;
                //deviceId!=null?service.defaultAttributes.deviceId=deviceId+"":null;
                //customerId!=null?service.defaultAttributes.customerId=customerId+"":null;
                //acquisitionSrc!=null?service.defaultAttributes.acquisitionSrc=acquisitionSrc:null;
            };

            service.getDefaultAttributes = function(){
                return service.defaultAttributes;
            };

            service.trackUser = function(userId) {
                try{
                    service.isTracking = true;
                    //console.log("user login:::::::"+userId);
                    webengage.user.login(userId);
                }catch(e){
                    console.log(e);
                }
            };

            service.setUserAttributes = function(data){
                try{
                    //console.log("user attributes:::::::"+JSON.stringify(service.convertUserData(data)));
                    data.contact != null ? webengage.user.setAttribute(service.convertUserData(data)): null;
                }catch(e){
                    console.log(e);
                }
            };

            service.logoutUser = function(){
                try{
                    service.isTracking = false;
                    //console.log("user logout::::::::::::::::::::::::");
                    webengage.user.logout();
                }catch(e){
                    console.log(e);
                }
            };

            service.trackEvent = function(event, data){
                try{
                    if(service.isTracking){
                        //console.log(event+":::::::"+JSON.stringify(service.getConvertedObject(event,data)));
                        webengage.track(event, service.getConvertedObject(event,data));
                    }
                }catch(e){
                    console.log(e);
                }
            };

            service.convertUserData = function(data){
                var defaultAttributes = {};//angular.copy(service.getDefaultAttributes());
                !service.isEmptyObject(data.name) ? defaultAttributes.we_first_name = data.name : null;
                defaultAttributes.we_last_name = "";
                //defaultAttributes.we_last_name = data.name.split(" ")[1]!=null?data.name.split(" ")[1]:"";
                !service.isEmptyObject(data.email) ? defaultAttributes.we_email = data.email: null;
                !service.isEmptyObject(data.contact) ? defaultAttributes.we_phone = ("+91" + data.contact): null;
                !service.isEmptyObject(data.loyaltea) ? defaultAttributes.loyaltea = data.loyaltea: null;
                !service.isEmptyObject(data.aquisitionSrc) ? defaultAttributes.aquisitionSrc = data.aquisitionSrc: null;
                !service.isEmptyObject(data.addTime) ? defaultAttributes.addTime = new Date(data.addTime): null;
                !service.isEmptyObject(data.numberVerificationTime) ? defaultAttributes.numberVerificationTime = new Date(data.numberVerificationTime): null;
                (data.blacklisted != undefined && data.blacklisted != null) ? defaultAttributes.blacklisted = data.blacklisted : null;
                (data.emailSubscriber != undefined && data.emailSubscriber != null) ? defaultAttributes.emailSubscriber = data.emailSubscriber : null;
                (data.emailVerified != undefined && data.emailVerified != null) ? defaultAttributes.emailVerified = data.emailVerified : null;
                !service.isEmptyObject(data.countryCode) ? defaultAttributes.countryCode = data.countryCode: null;
                !service.isEmptyObject(data.numberVerified) ? defaultAttributes.numberVerified = data.numberVerified: null;
                return defaultAttributes;
            };

            service.convertOrderSuccess = function(data){
                var defaultAttributes = angular.copy(service.getDefaultAttributes());
                var orderItems = [];
                var giftCardOnly = true;
                data.orders.map(function(orderItem){
                    orderItems.push(orderItem.productName+"x"+orderItem.quantity);
                    if(giftCardOnly){
                        giftCardOnly = [1026, 1027, 1048, 1056].indexOf(orderItem.productId) >= 0;
                    }
                });
                orderItems = orderItems.join(",");
                defaultAttributes.orderSource=data.source;
                defaultAttributes.totalAmount=data.transactionDetail.totalAmount;
                defaultAttributes.taxableAmount=data.transactionDetail.taxableAmount;
                defaultAttributes.paidAmount=data.transactionDetail.paidAmount;
                defaultAttributes.totalDiscount=data.transactionDetail.discountDetail.totalDiscount;
                defaultAttributes.offerCode=data.offerCode==null?"":data.offerCode;
                defaultAttributes.containsSignupOffer=data.containsSignupOffer===true;
                defaultAttributes.orderItems=orderItems;
                defaultAttributes.giftCardOnly=giftCardOnly === true;
                defaultAttributes.generatedOrderId=data.generatedOrderId;
                defaultAttributes.externalOrderId=data.externalOrderId!=null?data.externalOrderId:"";
                defaultAttributes.paymentMode=service.getPaymentMode(data.settlements);
                defaultAttributes.pointsRedeemed=data.pointsRedeemed;
                defaultAttributes.billingServerTime = new Date(data.billingServerTime);
                defaultAttributes.settlements = service.getSettlements(data.settlements);
                defaultAttributes.unitId = parseInt(data.unitId);
                defaultAttributes.unitName = defaultAttributes.outlet;
                defaultAttributes.channelPartner = data.channelPartnerName;
                return defaultAttributes;
            };

            service.convertOrderCancel = function(data){
                var defaultAttributes = angular.copy(service.getDefaultAttributes());
                var orderItems = [];
                var giftCardOnly = true;
                data.orders.map(function(orderItem) {
                    orderItems.push(orderItem.productName+"x"+orderItem.quantity);
                    if(giftCardOnly){
                        giftCardOnly = [1026,1027,1048,1056].indexOf(orderItem.productId)>=0;
                    }
                });
                orderItems = orderItems.join(",");
                defaultAttributes.orderSource=data.source;
                defaultAttributes.totalAmount=data.transactionDetail.totalAmount;
                defaultAttributes.taxableAmount=data.transactionDetail.taxableAmount;
                defaultAttributes.paidAmount=data.transactionDetail.paidAmount;
                defaultAttributes.totalDiscount=data.transactionDetail.discountDetail.totalDiscount;
                defaultAttributes.offerCode=data.offerCode;
                defaultAttributes.containsSignupOffer=data.containsSignupOffer===true;
                defaultAttributes.orderItems=orderItems;
                defaultAttributes.giftCardOnly=giftCardOnly===true;
                defaultAttributes.generatedOrderId=data.generatedOrderId;
                defaultAttributes.externalOrderId=data.externalOrderId!=null?data.externalOrderId:"";
                defaultAttributes.paymentMode=service.getPaymentMode(data.settlements);
                defaultAttributes.pointsRedeemed=data.pointsRedeemed;
                defaultAttributes.billingServerTime = new Date(data.billingServerTime);
                defaultAttributes.settlements = service.getSettlements(data.settlements);
                defaultAttributes.unitId = data.unitId;
                defaultAttributes.unitName = defaultAttributes.outlet;
                defaultAttributes.channelPartner = data.channelPartnerName;
                return defaultAttributes;
            };

            service.getPaymentMode = function(settlements){
                var  mode = [];
                settlements.map(function (settlement) {
                    mode.push(service.paymentModes[settlement.mode]);
                });
                return mode.join(",");
            };

            service.setPaymentMode = function(data){
                data.map(function(mode) {
                    service.paymentModes[mode.id] = mode.name.toUpperCase();
                });
            };

            service.getSettlements = function(settlements){
                var data = angular.copy(settlements);
                var ret = [];
                data.map(function (settlement) {
                    ret.push(service.paymentModes[settlement.mode]+"x"+settlement.amount);
                    //settlement.mode = service.paymentModes[settlement.mode];
                });
                return ret.join(",");
            };

            service.convertCoupon = function(data){
                var defaultAttributes = angular.copy(service.getDefaultAttributes());
                defaultAttributes.coupon= data;
                return defaultAttributes;
            };

            service.convertOfferSuccess = function(data){
                var defaultAttributes = angular.copy(service.getDefaultAttributes());
                defaultAttributes.coupon = data.coupon;
                defaultAttributes.discount = data.discount;
                return defaultAttributes;
            };

            service.convertOfferFailed = function(data){
                var defaultAttributes = angular.copy(service.getDefaultAttributes());
                defaultAttributes.coupon= data.coupon;
                defaultAttributes.errorCode=data.errorCode;
                defaultAttributes.reason=data.reason;
                return defaultAttributes;
            };

            service.convertOTPResend = function(data){
                var defaultAttributes = angular.copy(service.getDefaultAttributes());
                defaultAttributes.contact = data.contact;
                    defaultAttributes.status = data.status;
            };

            service.convertEmail = function(data){
                var defaultAttributes = angular.copy(service.getDefaultAttributes());
                defaultAttributes.email = data.email;
            };

            return service;
        }]);