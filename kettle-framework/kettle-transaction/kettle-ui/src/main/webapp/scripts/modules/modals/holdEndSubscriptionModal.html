<div class="modal-header" data-ng-init="init()">
    <h3 class="modal-title" data-ng-if="actionType=='ON_HOLD'">Hold Subscription</h3>
    <h3 class="modal-title" data-ng-if="actionType=='CANCELLED'">Close Subscription</h3>
</div>
<div class="modal-body">
    <div class="row" style="marin-bottom:20px;">
        <div class="col-xs-6">
            <div class="form-group">
                <label>Start date:</label>
                <div class="datepicker" data-date-format="yyyy-MM-dd" data-date-min-limit="{{actionType=='CANCELLED'?getCurrentDate:subscriptionMinDate}}">
                    <input type="text" data-ng-model="startDate" class="form-control"
                           data-ng-change="setStartDate(startDate)" placeholder="yyyy-mm-dd" />
                </div>
            </div>
        </div>
        <div class="col-xs-6">
            <div class="form-group" data-ng-if="actionType=='ON_HOLD'">
                <label>End date:</label>
                <div class="datepicker" data-date-format="yyyy-MM-dd" data-date-min-limit="{{startDate==null?subscriptionMinDate:getPreviousDate(startDate)}}">
                    <input type="text" data-ng-model="endDate" class="form-control"
                           data-ng-change="setEndDate(endDate)" placeholder="yyyy-mm-dd" />
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-xs-12">
            <div class="checkbox" data-ng-if="actionType=='ON_HOLD'">
                <label><input type="checkbox" data-ng-model="withImmediateEffect" data-ng-change="updateStartDate()"> With immediate effect.</label>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-xs-12">
            <div class="form-group">
                <label>Reason:</label>
                <textarea class="form-control" rows="6" data-ng-model="actionReason"></textarea>
            </div>
        </div>
    </div>
</div>
<div class="modal-footer">
    <div class="btn-group col-xs-offset-10" style="margin-bottom: 10px">
        <button class="btn btn-danger" type="button" ng-click="cancel()">Cancel</button>
        <button class="btn btn-default" type="button" ng-click="ok()">Submit</button>
    </div>
</div>