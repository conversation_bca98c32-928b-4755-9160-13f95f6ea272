<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div data-flash-message="5000" ></div>
<div class="container-fluid">
	<div class="row">
		<h2 class="text-center"
			style="color: #737370; margin-top: 50px; font-family: 'typewriter';">Customer
			Order Summary Report</h2>
	</div>



	<form class="navbar-form bs-navbar-top-example navbar-static-top"
		style="margin-top: 20px" role="search" name="searchOrderForm">
		<button type="button" class="btn btn-warning pull-left"
			style="margin-left: 50px" ng-click="backToCover()">back</button>

		<div class="form-group col-sm-offset-2 col-sm-6">
			<label>Enter Customer Contact:</label> <input type="number"
				style="width: 200px" class="form-control" id="phno" oninput="maxLengthCheck(this)"
				placeholder="Search by contact number" maxlength="10" name="phno"
				ng-model="searchText">

			<button type="submit" class="btn btn-default"
				ng-click="getCustomerOrders()">
				<span class="glyphicon glyphicon-search"></span>
			</button>
		</div>

	</form>





	<div class="row" ng-if="!isEmpty(customer)">
		<span class="col-lg-2 col-sm-2"><strong>Name :</strong>{{customer.firstName}}
			{{customer.lastName}}</span> <span class="col-lg-2 col-sm-2"><strong>Loyalty
				points :</strong>{{customer.loyaltyPoints}}</span><br>
		<button  type="button" name="freeChaiDelivery" class="btn btn-danger pull-right" style="margin-right: 150px"
				data-ng-show="showFreeChaiDelivery"
				data-ng-click="sendFreeChaiDeliverySms()">Free Chai Delivery
		</button>
	</div>



	<div class="row text-center" style="margin-top:30px;" ng-if="isEmpty(orders)">
		<h3 class="col-lg-12 col-sm-12 col-xs-12">No customer orders found</h3>
	</div>

	<table class="table table-striped" ng-if="!isEmpty(orders)"
		style="margin: 10px; margin-top: 90px">
		<thead>
			<tr>
				<th>Generated Bill No</th>
				<th>Bill No</th>
				<th>Source</th>
				<th>Brand</th>
				<th>Unit Order Id</th>
				<th>Unit Name</th>
				<th>Creation Time</th>
				<th>Gross Amount</th>
				<th>Discount</th>
				<th>Taxable Amount</th>
				<th>Tax</th>
				<th>Round Off</th>
				<th>Paid Amount</th>
				<th>Is Parcel</th>
				<th>Status</th>
			</tr>
		</thead>
		<tbody>
			<tr ng-repeat="orderItem in orders" data-ng-class="{outOfDelivery : orderItem.ood }">
				<td><a href
					ng-click="openOrderSearch(orderItem.generateOrderId)"
					style="color: dodgerblue"><b>{{orderItem.generateOrderId}}</b></a></td>
				<td>{{orderItem.orderId}}</td>
				<td>{{orderItem.source}}</td>
				<td>{{orderItem.brand.brandName}}</td>
				<td>{{orderItem.unitOrderId}}</td>
				<td>{{orderItem.unitName}}</td>
				<td>{{orderItem.billCreationTime | date:'yyyy-MM-dd HH:mm:ss'}}</td>
				<td>{{orderItem.transactionDetail.totalAmount}}</td>
				<td>{{orderItem.transactionDetail.discountDetail.discount.value}}</td>
				<td>{{orderItem.transactionDetail.taxableAmount}}</td>
				<td>{{orderItem.transactionDetail.tax}}</td>
				<td>{{orderItem.transactionDetail.roundOffValue}}</td>
				<td>{{orderItem.transactionDetail.paidAmount}}</td>
				<td>{{orderItem.hasParcel}}</td>
				<td>{{orderItem.status}}</td>
			</tr>
		</tbody>
	</table>
</div>
<script>
	function maxLengthCheck(object) {
		if (object.value.length > object.maxLength)
			object.value = object.value.slice(0, object.maxLength)
	}
</script>