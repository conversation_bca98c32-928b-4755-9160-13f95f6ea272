<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style>
    table th {
        background-color: #ccf2ff;
    }

    table th td {
        text-align: center;
        font-weight: bold;
    }

    table tr td {
        text-align: center;
    }

    .coverFlash {
        position: absolute;
        width: 100%;
        z-index: 9999;
        margin: 0;
    }

    .success-text {
        color: limegreen;
        font-size: 20px;
        padding: 0 10px;
        height: 40px;
        line-height: 40px;
    }

    .danger {
        color: red;
        font-size: 20px;
        padding: 0 10px;
        height: 40px;
        line-height: 40px;
    }

</style>
<div class="row coverFlash" flash-message="5000" ng-init="init()"></div>

<div class="coverView">
    <div class="frontLoadSection" data-ng-style="{'height':screenHeight}">
        <div class="header">
            <div class="leftSection">
                <div class="hbtn" data-ng-click="resetConfig()" data-ng-show="isManager">
                    <img src="images/reset.svg"/><br/>Reset
                </div>
                <div class="verticalSplit" data-ng-show="isManager"></div>
                <div data-ng-show="!isAndroid"
                     data-ng-class="{'indicator':printerStatus, 'indicator error':!printerStatus}">
                    <img src="images/printer.svg"/><br/>Printer
                </div>
                <div data-ng-show="!isAndroid" class="verticalSplit"></div>
                <!--<div data-ng-class="{'indicator':customerScreen, 'indicator error':!customerScreen}">
                    <img src="images/customerScreen.svg" /><br/>Customer Screen
                </div>-->
            </div>
            <div class="rightSection">
                <div class="hbtn" data-ng-click="logOut()">
                    <img src="images/logout.svg"/><br/>Log Out
                </div>
                <div class="verticalSplit"></div>
                <div class="indicator"><img src="images/cafeName.svg"/><br/>
                    {{unitDetails.name}} T{{currentUser.terminalId}}
                </div>
                <div class="verticalSplit"></div>
                <div class="indicator"><img src="images/profile.svg"/><br/>
                    {{currentUser.userName}}({{currentUser.userId}})
                </div>
                <div class="verticalSplit"></div>
                <div class="hbtn" data-ng-click="markAttendance()">
                    <img src="images/profile.svg"/><br/>Attendance
                </div>

                <div class="verticalSplit "></div>

                <div class="indicator1">
                    <span style ="padding-right: 5px;">Chaayos Select : </span>
                    <div class="circle ">
                        <div class="circle_text_container">
                            <div class = "circle_text">{{unitsPenetrationCurrent.specificUnitReportData.penetration.unitSubscriptionCount}}
                            </div>
                        </div>
                    </div>
                        <div class="verticalSplit tilt"></div>
                    <div class="circle">
                        <div class="circle_text_container">
                            <div class = "circle_text">{{unitsPenetrationCurrent.overallSystemWideReportData.penetration.unitSubscriptionCount}}
                            </div>
                        </div>
                </div>
                </div>
            </div>
            <div style="clear: both;"></div>
        </div>
        <div style="flex-direction: row;justify-content: space-evenly;align-content: center">
            <div class="last3Orders col-xs-5" data-ng-if="lastThreeOrderArray != null">
                Last 3 Orders:
                <a style="font-size: 20px;" href="javascript:;"
                   data-ng-click="openOrderSearch(lastOrder)"
                   data-ng-repeat="lastOrder in lastThreeOrderArray track by $index"
                   data-ng-class="{lastOrderCss:$index == 2, firstTwo:$index < 2}">
                    {{lastOrder}}</a>
            </div>
            <div class="col-xs-4" >
                <timer  class="pull-left" style="font-weight: bold;font-size: 14px; margin: 10px; text-align: center;
  color: red;" interval="1000">{{mminutes}} min{{minutesS}} : {{sseconds}} sec{{secondsS}}</timer>
                <span class="pull-left" style="font-size: 14px" data-ng-class="{'success-text':cafeBrand.swiggyChaayos,'danger':!cafeBrand.swiggyChaayos}"><b>Swiggy(Chaayos)</b></span>
                <span class="pull-left" style="font-size: 14px" ng-if="cafeBrand.swiggyGnT != null" data-ng-class="{'success-text':cafeBrand.swiggyGnT,'danger':!cafeBrand.swiggyGnT}"><b>Swiggy(GnT)</b></span>
                <span class="pull-left" style="font-size: 14px" data-ng-class="{'success-text':cafeBrand.zomatoChaayos,'danger':!cafeBrand.zomatoChaayos}"><b>Zomato(Chaayos)</b></span>
                <span class="pull-left" style="font-size: 14px" ng-if="cafeBrand.zomatoGnT != null" data-ng-class="{'success-text':cafeBrand.zomatoGnT,'danger':!cafeBrand.zomatoGnT}"><b>Zomato(GnT)</b></span>
                <i class="fa fa-refresh pull-left" style="font-size:28px;" data-ng-click="getCafePartnerStatus(unitDetails.id)"></i>
            </div>

            <div class="col-xs-3">
                <strong>{{analyticsTimerSeconds | secondsTimer}}</strong>
                <button class="btn btn-primary" type="button"
                        data-ng-click="refreshAnalyticsData(false)">Refresh Analytics Data</button>
            </div>
        </div>

        <div class="mainBtnSection">
            <button class="coverButton" type="button"
                    data-ng-if="!extendedTableService && unitDetails.handoverDate != null" data-ng-click="orderStart()">
                <img src="images/orderStart.svg"/><br/>Order Start
            </button>
            <button class="coverButton" type="button"
                    data-ng-if="extendedTableService && unitDetails.handoverDate != null" data-ng-click="checkTable()">
                <img src="images/orderStart.svg"/><br/>Check Table
            </button>
            <button class="coverButton" type="button" data-ng-click="paidEmployeeMealStart()">
                <img src="images/paidEmployee.svg"/><br/>Paid Employee Meal
            </button>
            <button class="coverButton" type="button" data-ng-click="goToOrderSummary()">
                <img src="images/orderSummary.svg"/><br/>Order Summary
            </button>
            <button class="coverButton" type="button" data-ng-click="openDeliveryOrders()">
                <img src="images/openDeliveryOrder.svg"/><br/>Open Delivery Orders
            </button>
            <button class="coverButton" type="button" data-ng-click="openSettlementTypeObj()">
                <img src="images/settlement.svg"/><br/>Settlement type Report
            </button>
            <button class="coverButton" type="button" data-ng-click="openItemConsumptionModal()"
                    style="font-size: 17px;">
                <img src="images/itemConsumption.svg"/><br/>Item Consumption Report
            </button>
        </div>

        <div class="analyticsSection">
            <div class="table1" data-ng-if="liveInventory">
                <table class="table table-bordered">
                    <tr>
                        <th class="firebrick" colspan="3">
                            Expiration data
                        </th>
                    </tr>
                    <tr data-ng-if="snapShot != null">
                        <td>Total Waste Projected</td>
                        <td colspan="2">{{snapShot.totalCost | number : 0}}</td>
                    </tr>
                    <tr>
                        <th class="yellow" style="text-align: left; width:40%;">  </th>
                        <th class="orange">Chaayos</th>
                        <th class="teal"> GnT </th>
                    </tr>
                    <tr data-ng-if="snapShot != null">
                        <td>Current Waste</td>
                        <td >{{snapShot.currentCostChaayos | number : 0}}</td>
                        <td >{{snapShot.currentCostGnT | number : 0}}</td>
                    </tr>
                    <tr data-ng-if="snapShot != null">
                        <td>Percentage Achieved</td>
                        <td data-ng-class="{'redText':snapShot.percentageAchievedChaayos < 90,
							'greenText':snapShot.percentageAchievedChaayos >= 90}">{{snapShot.percentageAchievedChaayos | number :
                            0}}%
                        </td>
                        <td data-ng-class="{'redText':snapShot.percentageAchievedGnT < 90,
							'greenText':snapShot.percentageAchievedGnT >= 90}">{{snapShot.percentageAchievedGnT | number :
                            0}}%
                        </td>
                    </tr>
                    <tr data-ng-if="snapShotChaayos != null">
                        <th class="firebrick" colspan="3">
                            Chaayos
                        </th>
                    </tr>
                    <tr data-ng-if="snapShotChaayos != null">
                        <th class="yellow" style="text-align: left; width:40%;">Name (UOM)</th>
                        <th class="orange">Projected <br> Qty (Cost)</th>
                        <th class="teal">Current <br> Qty (Cost)</th>
                    </tr>
                    <tr
                            data-ng-repeat="item in snapShotChaayos.inventory | orderBy:  '-(curQty * price)'" data-ng-if="snapShotChaayos != null">
                        <td style="text-align: left; width:40%;">{{item.name}} ({{item.u}})</td>
                        <td>{{item.exQty}} ({{item.exQty * item.price | number : 0}})</td>
                        <td>{{item.curQty}} ({{item.curQty * item.price | number : 0}})</td>
                    </tr>
                    <tr data-ng-if="snapShotGnT != null">
                        <th class="firebrick" colspan="3">
                            GnT
                        </th>
                    </tr>
                    <tr data-ng-if="snapShotGnT != null">
                        <th class="yellow" style="text-align: left; width:40%;">Name (UOM)</th>
                        <th class="orange">Projected <br> Qty (Cost)</th>
                        <th class="teal">Current <br> Qty (Cost)</th>
                    </tr>
                    <tr
                            data-ng-repeat="item in snapShotGnT.inventory | orderBy:  '-(curQty * price)'" data-ng-if="snapShotGnT != null">
                        <td style="text-align: left; width:40%;">{{item.name}} ({{item.u}})</td>
                        <td>{{item.exQty}} ({{item.exQty * item.price | number : 0}})</td>
                        <td>{{item.curQty}} ({{item.curQty * item.price | number : 0}})</td>
                    </tr>
                </table>
            </div>
            <div data-ng-class="{'tablesWrapper liveInventory':liveInventory==true, 'tablesWrapper':liveInventory == false}">
                <!--<div class="table2">-->
                    <!--<table class="table table-bordered">-->
                        <!--<tr>-->
                            <!--<th class="yellow">Category</th>-->
                            <!--<th class="brown">Target MTD</th>-->
                            <!--<th class="orange">Ach MTD</th>-->
                            <!--<th class="teal">Target Daily</th>-->
                            <!--<th class="orange">Ach Daily</th>-->

                            <!--&lt;!&ndash; <th class="teal">Ach M-1</th> &ndash;&gt;-->
                        <!--</tr>-->
                        <!--<tr>-->
                            <!--<td class="firstColumnTable">Merchandise Sales</td>-->
                            <!--<td>{{unitsPenetrationTarget.merchandiseSales | getPercentageReport :-->
                                <!--unitsPenetrationTarget.netSale : unitsPenetrationTarget}}-->
                            <!--</td>-->
                            <!--<td data-ng-class="{'redText':unitsPenetrationTarget.merchandiseSales>unitsPenetrationCurrent.merchandiseSales,-->
							<!--'greenText':unitsPenetrationTarget.merchandiseSales<unitsPenetrationCurrent.merchandiseSales}">-->
                                <!--{{unitsPenetrationCurrent.merchandiseSales | getPercentageReport :-->
                                <!--unitsPenetrationCurrent.netSale : unitsPenetrationCurrent}}-->
                            <!--</td>-->
                            <!--<td>{{unitsPenetrationTargetForDay.merchandiseSales | getPercentageReport :-->
                                <!--unitsPenetrationTargetForDay.netSale : unitsPenetrationTargetForDay}}-->
                            <!--</td>-->
                            <!--<td data-ng-class="{'redText':unitsPenetrationTargetForDay.merchandiseSales>unitsPenetrationCurrentForDay.merchandiseSales,-->
							<!--'greenText':unitsPenetrationTargetForDay.merchandiseSales<unitsPenetrationCurrentForDay.merchandiseSales}">-->
                                <!--{{unitsPenetrationCurrentForDay.merchandiseSales | getPercentageReport :-->
                                <!--unitsPenetrationCurrentForDay.netSale : unitsPenetrationCurrentForDay}}-->
                            <!--</td>-->
                            <!--&lt;!&ndash; <td>{{unitsPenetrationLM.merchandise | getPercentageReport : unitsPenetrationLM.dineIn : unitsPenetrationLM}}</td> &ndash;&gt;-->
                        <!--</tr>-->
                        <!--<tr>-->
                            <!--<td class="firstColumnTable">Bakery Per Ticket</td>-->
                            <!--<td>{{unitsPenetrationTarget.cakes | getPercentageReport : unitsPenetrationTarget.dineIn :-->
                                <!--unitsPenetrationTarget}}-->
                            <!--</td>-->
                            <!--<td data-ng-class="{'redText':unitsPenetrationTarget.cakes>unitsPenetrationCurrent.cakes,-->
							<!--'greenText':unitsPenetrationTarget.cakes<unitsPenetrationCurrent.cakes}">-->
                                <!--{{unitsPenetrationCurrent.cakes | getPercentageReport : unitsPenetrationCurrent.dineIn :-->
                                <!--unitsPenetrationCurrent}}-->
                            <!--</td>-->
                            <!--<td>{{unitsPenetrationTargetForDay.cakes | getPercentageReport :-->
                                <!--unitsPenetrationTargetForDay.dineIn : unitsPenetrationTargetForDay}}-->
                            <!--</td>-->
                            <!--<td data-ng-class="{'redText':unitsPenetrationTargetForDay.cakes>unitsPenetrationCurrentForDay.cakes,-->
							<!--'greenText':unitsPenetrationTargetForDay.cakes<unitsPenetrationCurrentForDay.cakes}">-->
                                <!--{{unitsPenetrationCurrentForDay.cakes | getPercentageReport :-->
                                <!--unitsPenetrationCurrentForDay.dineIn : unitsPenetrationCurrentForDay}}-->
                            <!--</td>-->
                            <!--&lt;!&ndash; <td>{{unitsPenetrationLM.cakes | getPercentageReport : unitsPenetrationLM.dineIn : unitsPenetrationLM}}</td> &ndash;&gt;-->
                        <!--</tr>-->
                        <!--<tr>-->
                            <!--<td class="firstColumnTable">Cold Per Ticket</td>-->
                            <!--<td>{{unitsPenetrationTarget.cold | getPercentageReport : unitsPenetrationTarget.dineIn :-->
                                <!--unitsPenetrationTarget}}-->
                            <!--</td>-->
                            <!--<td data-ng-class="{'redText':unitsPenetrationTarget.cold>unitsPenetrationCurrent.cold,-->
							<!--'greenText':unitsPenetrationTarget.cold<unitsPenetrationCurrent.cold}">-->
                                <!--{{unitsPenetrationCurrent.cold | getPercentageReport : unitsPenetrationCurrent.dineIn :-->
                                <!--unitsPenetrationCurrent}}-->
                            <!--</td>-->
                            <!--<td>{{unitsPenetrationTargetForDay.cold | getPercentageReport :-->
                                <!--unitsPenetrationTargetForDay.dineIn : unitsPenetrationTargetForDay}}-->
                            <!--</td>-->
                            <!--<td data-ng-class="{'redText':unitsPenetrationTargetForDay.cold>unitsPenetrationCurrentForDay.cold,-->
							<!--'greenText':unitsPenetrationTargetForDay.cold<unitsPenetrationCurrentForDay.cold}">-->
                                <!--{{unitsPenetrationCurrentForDay.cold | getPercentageReport :-->
                                <!--unitsPenetrationCurrentForDay.dineIn : unitsPenetrationCurrentForDay}}-->
                            <!--</td>-->
                            <!--&lt;!&ndash; <td>{{unitsPenetrationLM.cold | getPercentageReport : unitsPenetrationLM.dineIn : unitsPenetrationLM}}</td> &ndash;&gt;-->
                        <!--</tr>-->
                        <!--<tr>-->
                            <!--<td class="firstColumnTable">Food Per Ticket</td>-->
                            <!--<td>{{unitsPenetrationTarget.food | getPercentageReport : unitsPenetrationTarget.dineIn :-->
                                <!--unitsPenetrationTarget}}-->
                            <!--</td>-->
                            <!--<td data-ng-class="{'redText':unitsPenetrationTarget.food>unitsPenetrationCurrent.food,-->
							<!--'greenText':unitsPenetrationTarget.food<unitsPenetrationCurrent.food}">-->
                                <!--{{unitsPenetrationCurrent.food | getPercentageReport : unitsPenetrationCurrent.dineIn :-->
                                <!--unitsPenetrationCurrent}}-->
                            <!--</td>-->
                            <!--<td>{{unitsPenetrationTargetForDay.food | getPercentageReport :-->
                                <!--unitsPenetrationTargetForDay.dineIn : unitsPenetrationTargetForDay}}-->
                            <!--</td>-->
                            <!--<td data-ng-class="{'redText':unitsPenetrationTargetForDay.food>unitsPenetrationCurrentForDay.food,-->
							<!--'greenText':unitsPenetrationTargetForDay.food<unitsPenetrationCurrentForDay.food}">-->
                                <!--{{unitsPenetrationCurrentForDay.food | getPercentageReport :-->
                                <!--unitsPenetrationCurrentForDay.dineIn : unitsPenetrationCurrentForDay}}-->
                            <!--</td>-->
                            <!--&lt;!&ndash; <td>{{unitsPenetrationLM.food | getPercentageReport : unitsPenetrationLM.dineIn : unitsPenetrationLM}}</td> &ndash;&gt;-->
                        <!--</tr>-->
                        <!--<tr>-->
                            <!--<td class="firstColumnTable">Regular to Full Ratio</td>-->
                            <!--<td>{{unitsPenetrationTarget.full | getPercentageReport : (unitsPenetrationTarget.regular) :-->
                                <!--unitsPenetrationTarget}}-->
                            <!--</td>-->
                            <!--<td data-ng-class="{'greenText':unitsPenetrationCurrent.regular>unitsPenetrationCurrent.full,-->
							<!--'redText':(unitsPenetrationCurrent.regular)<unitsPenetrationCurrent.full}">-->
                                <!--{{unitsPenetrationCurrent.full | getPercentageReport : (unitsPenetrationCurrent.regular)-->
                                <!--: unitsPenetrationCurrent}}-->
                            <!--</td>-->
                            <!--<td>{{unitsPenetrationTargetForDay.full | getPercentageReport :-->
                                <!--(unitsPenetrationTargetForDay.regular) : unitsPenetrationTargetForDay}}-->
                            <!--</td>-->
                            <!--<td data-ng-class="{'greenText':unitsPenetrationCurrentForDay.regular>unitsPenetrationCurrentForDay.full,-->
							<!--'redText':(unitsPenetrationCurrentForDay.regular)<unitsPenetrationCurrentForDay.full}">-->
                                <!--{{unitsPenetrationCurrentForDay.full | getPercentageReport :-->
                                <!--(unitsPenetrationCurrentForDay.regular) : unitsPenetrationCurrentForDay}}-->
                            <!--</td>-->
                            <!--&lt;!&ndash; <td>{{unitsPenetrationLM.full | getPercentageReport : (unitsPenetrationLM.regular) : unitsPenetrationLM}}</td> &ndash;&gt;-->
                        <!--</tr>-->
                        <!--<tr>-->
                            <!--<td class="firstColumnTable">Meals Per Ticket<br/><span style="font-size: 12px;">Parathas, Kulachas, Pav bhaji</span>-->
                            <!--</td>-->
                            <!--<td>{{unitsPenetrationTarget.meals | getPercentageReport : unitsPenetrationTarget.dineIn :-->
                                <!--unitsPenetrationTarget}}-->
                            <!--</td>-->
                            <!--<td data-ng-class="{'redText':unitsPenetrationTarget.meals>unitsPenetrationCurrent.meals,-->
							<!--'greenText':unitsPenetrationTarget.meals<unitsPenetrationCurrent.meals}">-->
                                <!--{{unitsPenetrationCurrent.meals | getPercentageReport : unitsPenetrationCurrent.dineIn :-->
                                <!--unitsPenetrationCurrent}}-->
                            <!--</td>-->
                            <!--<td>{{unitsPenetrationTargetForDay.meals | getPercentageReport :-->
                                <!--unitsPenetrationTargetForDay.dineIn : unitsPenetrationTargetForDay}}-->
                            <!--</td>-->
                            <!--<td data-ng-class="{'redText':unitsPenetrationTargetForDay.meals>unitsPenetrationCurrentForDay.meals,-->
							<!--'greenText':unitsPenetrationTargetForDay.meals<unitsPenetrationCurrentForDay.meals}">-->
                                <!--{{unitsPenetrationCurrentForDay.meals | getPercentageReport :-->
                                <!--unitsPenetrationCurrentForDay.dineIn : unitsPenetrationCurrentForDay}}-->
                            <!--</td>-->
                            <!--&lt;!&ndash; <td>{{unitsPenetrationLM.meals | getPercentageReport : unitsPenetrationLM.dineIn : unitsPenetrationLM}}</td> &ndash;&gt;-->
                        <!--</tr>-->
                        <!--<tr>-->
                            <!--<td class="firstColumnTable">Beverage Only Tickets</td>-->
                            <!--<td>{{unitsPenetrationTarget.beverage | getPercentageReport : unitsPenetrationTarget.dineIn-->
                                <!--: unitsPenetrationTarget}}-->
                            <!--</td>-->
                            <!--<td data-ng-class="{'redText':unitsPenetrationTarget.beverage>unitsPenetrationCurrent.beverage,-->
							<!--'greenText':unitsPenetrationTarget.beverage<unitsPenetrationCurrent.beverage}">-->
                                <!--{{unitsPenetrationCurrent.beverage | getPercentageReport :-->
                                <!--unitsPenetrationCurrent.dineIn : unitsPenetrationCurrent}}-->
                            <!--</td>-->
                            <!--<td>{{unitsPenetrationTargetForDay.beverage | getPercentageReport :-->
                                <!--unitsPenetrationTargetForDay.dineIn : unitsPenetrationTargetForDay}}-->
                            <!--</td>-->
                            <!--<td data-ng-class="{'redText':unitsPenetrationTargetForDay.beverage>unitsPenetrationCurrentForDay.beverage,-->
							<!--'greenText':unitsPenetrationTargetForDay.beverage<unitsPenetrationCurrentForDay.beverage}">-->
                                <!--{{unitsPenetrationCurrentForDay.beverage | getPercentageReport :-->
                                <!--unitsPenetrationCurrentForDay.dineIn : unitsPenetrationCurrentForDay}}-->
                            <!--</td>-->
                            <!--&lt;!&ndash; <td>{{unitsPenetrationLM.beverage | getPercentageReport : unitsPenetrationLM.dineIn : unitsPenetrationLM}}</td>  &ndash;&gt;-->
                        <!--</tr>-->


                        <!--<tr>-->
                            <!--<td class="firstColumnTable">Seasonal Product One<br/><span style="font-size: 12px;">Anda Parantha + Dhokla</span>-->
                            <!--</td>-->
                            <!--<td>{{unitsPenetrationTarget.seasonalProductOne | getPercentageReport : unitsPenetrationTarget.dineIn :-->
                                <!--unitsPenetrationTarget}}-->
                            <!--</td>-->
                            <!--<td data-ng-class="{'redText':unitsPenetrationTarget.seasonalProductOne>unitsPenetrationCurrent.seasonalProductOne,-->
							<!--'greenText':unitsPenetrationTarget.seasonalProductOne<unitsPenetrationCurrent.seasonalProductOne}">-->
                                <!--{{unitsPenetrationCurrent.seasonalProductOne | getPercentageReport : unitsPenetrationCurrent.dineIn :-->
                                <!--unitsPenetrationCurrent}}-->
                            <!--</td>-->
                            <!--<td>{{unitsPenetrationTargetForDay.seasonalProductOne | getPercentageReport :-->
                                <!--unitsPenetrationTargetForDay.dineIn : unitsPenetrationTargetForDay}}-->
                            <!--</td>-->
                            <!--<td data-ng-class="{'redText':unitsPenetrationTargetForDay.seasonalProductOne>unitsPenetrationCurrentForDay.seasonalProductOne,-->
							<!--'greenText':unitsPenetrationTargetForDay.seasonalProductOne<unitsPenetrationCurrentForDay.seasonalProductOne}">-->
                                <!--{{unitsPenetrationCurrentForDay.seasonalProductOne | getPercentageReport :-->
                                <!--unitsPenetrationCurrentForDay.dineIn : unitsPenetrationCurrentForDay}}-->
                            <!--</td>-->
                            <!--&lt;!&ndash; <td>{{unitsPenetrationLM.meals | getPercentageReport : unitsPenetrationLM.dineIn : unitsPenetrationLM}}</td> &ndash;&gt;-->
                        <!--</tr>-->


                        <!--<tr>-->
                            <!--<td class="firstColumnTable">Seasonal Product Two<br/><span style="font-size: 12px;">Mc Puff (Veg & Non-Veg)</span>-->
                            <!--</td>-->
                            <!--<td>{{unitsPenetrationTarget.seasonalProductTwo | getPercentageReport : unitsPenetrationTarget.dineIn :-->
                                <!--unitsPenetrationTarget}}-->
                            <!--</td>-->
                            <!--<td data-ng-class="{'redText':unitsPenetrationTarget.seasonalProductTwo>unitsPenetrationCurrent.seasonalProductTwo,-->
							<!--'greenText':unitsPenetrationTarget.seasonalProductTwo<unitsPenetrationCurrent.seasonalProductTwo}">-->
                                <!--{{unitsPenetrationCurrent.seasonalProductTwo | getPercentageReport : unitsPenetrationCurrent.dineIn :-->
                                <!--unitsPenetrationCurrent}}-->
                            <!--</td>-->
                            <!--<td>{{unitsPenetrationTargetForDay.seasonalProductTwo | getPercentageReport :-->
                                <!--unitsPenetrationTargetForDay.dineIn : unitsPenetrationTargetForDay}}-->
                            <!--</td>-->
                            <!--<td data-ng-class="{'redText':unitsPenetrationTargetForDay.seasonalProductTwo >unitsPenetrationCurrentForDay.seasonalProductTwo,-->
							<!--'greenText':unitsPenetrationTargetForDay.seasonalProductTwo<unitsPenetrationCurrentForDay.seasonalProductTwo}">-->
                                <!--{{unitsPenetrationCurrentForDay.seasonalProductTwo | getPercentageReport :-->
                                <!--unitsPenetrationCurrentForDay.dineIn : unitsPenetrationCurrentForDay}}-->
                            <!--</td>-->
                            <!--&lt;!&ndash; <td>{{unitsPenetrationLM.meals | getPercentageReport : unitsPenetrationLM.dineIn : unitsPenetrationLM}}</td> &ndash;&gt;-->
                        <!--</tr>-->


                        <!--<tr>-->
                            <!--<td class="firstColumnTable">Seasonal Product Three<br/><span style="font-size: 12px;">Kesar Chai</span>-->
                            <!--</td>-->
                            <!--<td>{{unitsPenetrationTarget.seasonalProductThree | getPercentageReport : unitsPenetrationTarget.dineIn :-->
                                <!--unitsPenetrationTarget}}-->
                            <!--</td>-->
                            <!--<td data-ng-class="{'redText':unitsPenetrationTarget.seasonalProductThree>unitsPenetrationCurrent.seasonalProductThree,-->
							<!--'greenText':unitsPenetrationTarget.seasonalProductThree<unitsPenetrationCurrent.seasonalProductThree}">-->
                                <!--{{unitsPenetrationCurrent.seasonalProductThree | getPercentageReport : unitsPenetrationCurrent.dineIn :-->
                                <!--unitsPenetrationCurrent}}-->
                            <!--</td>-->
                            <!--<td>{{unitsPenetrationTargetForDay.seasonalProductThree | getPercentageReport :-->
                                <!--unitsPenetrationTargetForDay.dineIn : unitsPenetrationTargetForDay}}-->
                            <!--</td>-->
                            <!--<td data-ng-class="{'redText':unitsPenetrationTargetForDay.seasonalProductThree>unitsPenetrationCurrentForDay.seasonalProductThree,-->
							<!--'greenText':unitsPenetrationTargetForDay.seasonalProductThree<unitsPenetrationCurrentForDay.seasonalProductThree}">-->
                                <!--{{unitsPenetrationCurrentForDay.seasonalProductThree | getPercentageReport :-->
                                <!--unitsPenetrationCurrentForDay.dineIn : unitsPenetrationCurrentForDay}}-->
                            <!--</td>-->
                            <!--&lt;!&ndash; <td>{{unitsPenetrationLM.meals | getPercentageReport : unitsPenetrationLM.dineIn : unitsPenetrationLM}}</td> &ndash;&gt;-->
                        <!--</tr>-->

                        <!--&lt;!&ndash; <tr>-->
                            <!--<td class="firstColumnTable">New Product Per Ticket<br /><span style="font-size: 12px;">New Shakes and salads</span></td>-->
                            <!--<td>{{unitsPenetrationTarget.seasonal | getPercentageReport : unitsPenetrationTarget.dineIn : unitsPenetrationTarget}}</td>-->
                            <!--<td data-ng-class="{'redText':unitsPenetrationTarget.seasonal>unitsPenetrationCurrent.seasonal,-->
                            <!--'greenText':unitsPenetrationTarget.seasonal<unitsPenetrationCurrent.seasonal}">-->
                                <!--{{unitsPenetrationCurrent.seasonal | getPercentageReport : unitsPenetrationCurrent.dineIn : unitsPenetrationCurrent}}</td>-->
                            <!--<td>{{unitsPenetrationTargetForDay.seasonal | getPercentageReport : unitsPenetrationTargetForDay.dineIn : unitsPenetrationTargetForDay}}</td>-->
                            <!--<td data-ng-class="{'redText':unitsPenetrationTargetForDay.seasonal>unitsPenetrationCurrentForDay.seasonal,-->
                            <!--'greenText':unitsPenetrationTargetForDay.seasonal<unitsPenetrationCurrentForDay.seasonal}">-->
                                <!--{{unitsPenetrationCurrentForDay.seasonal | getPercentageReport : unitsPenetrationCurrentForDay.dineIn : unitsPenetrationCurrentForDay}}</td>-->
                            <!--<td>{{unitsPenetrationLM.seasonal | getPercentageReport : unitsPenetrationLM.dineIn : unitsPenetrationLM}}</td>-->
                        <!--</tr> &ndash;&gt;-->
                    <!--</table>-->
                <!--</div>-->
                <div class="table2">
                    <table class="table table-bordered">
                        <tr>
                            <th class="yellow">Category</th>
                            <th class="brown">Target MTD</th>
                            <th class="orange">Ach MTD</th>
                            <th class="teal">Target Daily</th>
                            <th class="orange">Ach Daily</th>

                            <!-- <th class="teal">Ach M-1</th> -->
                        </tr>
                        <tr data-ng-repeat="data in unitsAllTargetData.metrics track by $index">
                            <td class="firstColumnTable">{{unitsAllTargetData.metrics[$index].label}}</td>
                            <td>{{unitsAllTargetData.metrics[$index].aggregate | getPercentageValue : 100 : unitsAllTargetData.metrics[$index]}}</td>
                            <td data-ng-class="unitsAllCurrentData.metrics[$index].aggregate > 0 && unitsAllCurrentData.metrics[$index].trend
                                                || unitsAllCurrentData.metrics[$index].aggregate < 0 && !unitsAllCurrentData.metrics[$index].trend
                            ? 'greenText': 'redText'">
                                {{unitsAllCurrentData.metrics[$index].aggregate | getPercentageValue : 100 : unitsAllCurrentData.metrics[$index]}}
                            </td>
                            <td>{{unitsAllTargetData.metrics[$index].currentOrTarget | getPercentageValue : 100 : unitsAllTargetData.metrics[$index]}}
                            </td>
                            <td data-ng-class="unitsAllCurrentData.metrics[$index].currentOrTarget > 0 && unitsAllCurrentData.metrics[$index].trend
                                                || unitsAllCurrentData.metrics[$index].currentOrTarget < 0 && !unitsAllCurrentData.metrics[$index].trend
                            ? 'greenText': 'redText'">
                                {{unitsAllCurrentData.metrics[$index].currentOrTarget | getPercentageValue : 100 : unitsAllCurrentData.metrics[$index]}}
                            </td>

                        </tr>
                        <!-- <tr>
                            <td class="firstColumnTable">New Product Per Ticket<br /><span style="font-size: 12px;">New Shakes and salads</span></td>
                            <td>{{unitsPenetrationTarget.seasonal | getPercentageReport : unitsPenetrationTarget.dineIn : unitsPenetrationTarget}}</td>
                            <td data-ng-class="{'redText':unitsPenetrationTarget.seasonal>unitsPenetrationCurrent.seasonal,
                            'greenText':unitsPenetrationTarget.seasonal<unitsPenetrationCurrent.seasonal}">
                                {{unitsPenetrationCurrent.seasonal | getPercentageReport : unitsPenetrationCurrent.dineIn : unitsPenetrationCurrent}}</td>
                            <td>{{unitsPenetrationTargetForDay.seasonal | getPercentageReport : unitsPenetrationTargetForDay.dineIn : unitsPenetrationTargetForDay}}</td>
                            <td data-ng-class="{'redText':unitsPenetrationTargetForDay.seasonal>unitsPenetrationCurrentForDay.seasonal,
                            'greenText':unitsPenetrationTargetForDay.seasonal<unitsPenetrationCurrentForDay.seasonal}">
                                {{unitsPenetrationCurrentForDay.seasonal | getPercentageReport : unitsPenetrationCurrentForDay.dineIn : unitsPenetrationCurrentForDay}}</td>
                            <td>{{unitsPenetrationLM.seasonal | getPercentageReport : unitsPenetrationLM.dineIn : unitsPenetrationLM}}</td>
                        </tr> -->
                    </table>
                </div>
                <!-- <div class="table3">
                    <table class="table table-bordered">
                        <tr>
                            <th class="yellow">Category</th>
                            <th class="brown">Target MTD</th>
                            <th class="orange">Ach MTD</th>
                            <th class="teal">Target Daily</th>
                            <th class="orange">Ach Daily</th>
                        </tr>
                        <tr>
                            <td class="firstColumnTable">Beverage Only Tickets</td>
                            <td>{{unitsPenetrationTarget.beverage | getPercentageReport : unitsPenetrationTarget.dineIn : unitsPenetrationTarget}}</td>
                            <td data-ng-class="{'redText':unitsPenetrationTarget.beverage>unitsPenetrationCurrent.beverage,
                            'greenText':unitsPenetrationTarget.beverage<unitsPenetrationCurrent.beverage}">
                                {{unitsPenetrationCurrent.beverage | getPercentageReport : unitsPenetrationCurrent.dineIn : unitsPenetrationCurrent}}</td>
                            <td>{{unitsPenetrationTargetForDay.beverage | getPercentageReport : unitsPenetrationTargetForDay.dineIn : unitsPenetrationTargetForDay}}</td>
                            <td data-ng-class="{'redText':unitsPenetrationTargetForDay.beverage>unitsPenetrationCurrentForDay.beverage,
                            'greenText':unitsPenetrationTargetForDay.beverage<unitsPenetrationCurrentForDay.beverage}">
                                {{unitsPenetrationCurrentForDay.beverage | getPercentageReport : unitsPenetrationCurrentForDay.dineIn : unitsPenetrationCurrentForDay}}</td>
                            <td>{{unitsPenetrationLM.beverage | getPercentageReport : unitsPenetrationLM.dineIn : unitsPenetrationLM}}</td>
                        </tr>
                        <tr>
                            <td class="firstColumnTable" style="max-width: 170px;">New Customers with No Desi Chai</td>
                            <td>{{unitsPenetrationTarget.newCustWithNoPriPrd | getPercentageReport : unitsPenetrationTarget.newCustomer : unitsPenetrationTarget}}</td>
                            <td data-ng-class="{'greenText':unitsPenetrationTarget.newCustWithNoPriPrd>unitsPenetrationCurrent.newCustWithNoPriPrd,
                            'redText':unitsPenetrationTarget.newCustWithNoPriPrd<unitsPenetrationCurrent.newCustWithNoPriPrd}">
                                {{unitsPenetrationCurrent.newCustWithNoPriPrd | getPercentageReport : unitsPenetrationCurrent.newCustomer : unitsPenetrationCurrent}}</td>
                            <td>{{unitsPenetrationTargetForDay.newCustWithNoPriPrd | getPercentageReport : unitsPenetrationTargetForDay.newCustomer : unitsPenetrationTargetForDay}}</td>
                            <td data-ng-class="{'greenText':unitsPenetrationTargetForDay.newCustWithNoPriPrd>unitsPenetrationCurrentForDay.newCustWithNoPriPrd,
                            'redText':unitsPenetrationTargetForDay.newCustWithNoPriPrd<unitsPenetrationCurrentForDay.newCustWithNoPriPrd}">
                                {{unitsPenetrationCurrentForDay.newCustWithNoPriPrd | getPercentageReport : unitsPenetrationCurrentForDay.newCustomer : unitsPenetrationCurrentForDay}}</td>
                            <td>{{unitsPenetrationLM.newCustWithNoPriPrd | getPercentageReport : unitsPenetrationLM.newCustomer : unitsPenetrationLM}}</td>
                        </tr>
                        <tr>
                            <td class="firstColumnTable">Gift cards Penetration</td>
                            <td>{{unitsPenetrationTarget.giftCards | getPercentageReport : unitsPenetrationTarget.dineIn : unitsPenetrationTarget}}</td>
                            <td data-ng-class="{'redText':unitsPenetrationTarget.giftCards>unitsPenetrationCurrent.giftCards,
                            'greenText':unitsPenetrationTarget.giftCards<unitsPenetrationCurrent.giftCards}">
                                {{unitsPenetrationCurrent.giftCards | getPercentageReport : unitsPenetrationCurrent.dineIn : unitsPenetrationCurrent}}</td>
                            <td>NA</td>
                            <td>NA</td>
                            <td>{{unitsPenetrationTargetForDay.giftCards | getPercentageReport : unitsPenetrationTargetForDay.dineIn : unitsPenetrationTargetForDay}}</td>
                            <td data-ng-class="{'redText':unitsPenetrationTargetForDay.giftCards>unitsPenetrationCurrentForDay.giftCards,
                            'greenText':unitsPenetrationTargetForDay.giftCards<unitsPenetrationCurrentForDay.giftCards}">
                                {{unitsPenetrationCurrentForDay.giftCards | getPercentageReport : unitsPenetrationCurrentForDay.dineIn : unitsPenetrationCurrentForDay}}</td>
                            <td>{{unitsPenetrationLM.giftCards | getPercentageReport : unitsPenetrationLM.dineIn : unitsPenetrationLM}}</td>
                        </tr>
                    </table>
                </div> -->
                <div data-ng-class="{'table4':giftCardOfferInfo != null && giftCardOfferInfo.length > 0,
				'table4 full':giftCardOfferInfo == undefined || giftCardOfferInfo == null || giftCardOfferInfo.length == 0}">
                    <table class="table table-bordered">
                        <tr>
                            <th class="yellow">Category</th>
                            <th class="orange">Today</th>
                            <th class="teal">LWSD</th>
                        </tr>
                        <tr data-ng-repeat=" item in unitsReportData.data">
                            <td class="firstColumnTable">{{item.name}}</td>
                            <!--<td>{{item.target}}</td>-->
                            <td>{{item.current | number : 0}}</td>
                            <td>{{item.lw | number : 0}}</td>
                        </tr>
                    </table>
                </div>
                <div class="table5"
                     data-ng-if="giftCardOfferInfo != undefined && giftCardOfferInfo != null && giftCardOfferInfo.length > 0">
                    <table class="table table-bordered">
                        <tr>
                            <th>Card</th>
                            <th>Extra</th>
                            <th>Last Date</th>
                        </tr>
                        <tr data-ng-repeat="offer in giftCardOfferInfo track by $index">
                            <td>{{offer.denomination}}</td>
                            <td>{{offer.offer * offer.denomination/100}}</td>
                            <td>{{offer.endDate}}</td>
                        </tr>
                    </table>
                </div>
            </div>


            <div class="table6" data-ng-if="liveInventory">
                <table class="table table-bordered">
                    <tr>
                        <th class="yellow" colspan="2">
                            Percentage
                        </th>
                    </tr>
                    <tr data-ng-repeat="item in pnlStatusMapdata | orderBy: 'name'">
                        <td colspan="1">{{item.name}}</td>
                        <td colspan="1">{{item.code}}</td>
                    </tr>
                    <tr data-ng-if="inventoryAndGR != null && inventoryAndGR.grs != null && inventoryAndGR.grs['pendingGR'] != 0">
                        <th class="yellow" colspan="2">
                            Pending GRs in SUMO
                        </th>
                    </tr>
                    <tr data-ng-if="inventoryAndGR != null && inventoryAndGR.grs != null && inventoryAndGR.grs['pendingGR'] != 0">
                        <td class="orange">Total GRs</td>
                        <td style="background-color:red; color:white">{{inventoryAndGR.grs['pendingGR']}}</td>
                    </tr>
                    <tr data-ng-if="inventoryAndGR != null && inventoryAndGR.grs != null && inventoryAndGR.grs['pendingGR'] != 0">
                        <td class="orange">Milk/Bread GRs</td>
                        <td style="background-color:red; color:white">{{inventoryAndGR.grs['specialGR']}}</td>
                    </tr>
                    <tr>
                        <th class="yellow" colspan="2">
                            Stocked Out Products
                        </th>
                    </tr>
                    <tr data-ng-repeat="item in inventoryAndGR.stockouts | orderBy: 'name'">
                        <td colspan="2">{{item.name}}</td>
                    </tr>
                </table>
            </div>

        </div>
    </div>

</div>


<!--<div class="row">
	<div class="col-xs-12">
		<div class="row" style="margin-top: 5px;">
			<div class="col-xs-4" ng-if="isCafe || isTakeaway">
				<div class="row">
					<div class="col-xs-3">
						<button class="btn yalert basic"
							data-ng-click="resetConfig()" data-ng-show="isManager">Reset Conf</button>
					</div>
					<div class="col-xs-3">
						<button class="btn yalert basic"
							data-ng-click="logOut()">Log Out</button>
					</div>
					<div class="col-xs-3">
						<div
							data-ng-class="{success:customerScreen, error:!customerScreen}"
							style="color:#fff;"
							class="yalert">Customer Screen</div>
					</div>
					<div class="col-xs-3">
						<div
							data-ng-class="{success:printerStatus, error:!printerStatus}"
							style="color:#fff;"
							class="yalert">Printer</div>
					</div>
				</div>
				<div class="row">
					<div class="col-xs-6">
						<button class="btn btn-lg coverButton" type="button"
							data-ng-click="orderStart()">Order Start</button>
					</div>
					<div class="col-xs-6">
						&lt;!&ndash; <button class="btn btn-lg coverButton" type="button"
							data-ng-click="employeeMealStart()">Employee Meal</button> &ndash;&gt;
						<button class="btn btn-lg coverButton" type="button"
							data-ng-click="paidEmployeeMealStart()">Paid Employee Meal</button>
					</div>

				</div>
				<div class="row">
					<div class="col-xs-6">
						<button class="btn btn-lg coverButton"
							data-ng-if="isCafe || isDelivery" type="button"
							data-ng-click="openDeliveryOrders()">Open Delivery
							Orders</button>
					</div>
					<div class="col-xs-6">
						<button class="btn btn-lg coverButton" type="button"
							data-ng-click="goToOrderSummary()">Order Summary</button>
					</div>

				</div>
				<div class="row">
					<div class="col-xs-12" style="font-size: 20px;"
						data-ng-if="lastThreeOrderArray != null">
						Last 3 Orders: <a style="font-size: 20px;" href="javascript:;"
							data-ng-click="openOrderSearch(lastOrder)"
							data-ng-repeat="lastOrder in lastThreeOrderArray track by $index"
							data-ng-class="{lastOrderCss:$index == 2, firstTwo:$index < 2}">
							{{lastOrder}}</a>
					</div>
				</div>
			</div>
			<div class="col-xs-8" ng-if="isCafe || isTakeaway">
			   <div class="row" style="margin: 5px;">
					<div class="col-xs-12 text-right">
						<span class="unitLabelText">{{currentUser.userName}}
							({{currentUser.userId}}) &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;{{unitDetails.name}} ( T -
							{{currentUser.terminalId}})</span>
					</div>
				</div>
				&lt;!&ndash; <div class="row">
					<div class="col-xs-12 " style="text-align: left;">
						<span class="tableLabelText">Cafe Incentive Structure</span>
					</div>
				</div> &ndash;&gt;
				&lt;!&ndash; <div class="row">
					<div class="col-xs-12">
						<table class="table table-striped table-bordered">
							<tr>
								<th style="background-color: chartreuse">Designation</th>
								<th style="background-color: chartreuse">90 % To 94.99 %</th>
								<th style="background-color: chartreuse">95 % To 99.99 %</th>
								<th style="background-color: chartreuse">100 % To 104.99 %</th>
								<th style="background-color: chartreuse">105 % To 109.99 %</th>
								<th style="background-color: chartreuse">110 % & Above</th>
							</tr>
							<tr>
						<td class="firstColumnTable">Food Capture</td>
						<td
							data-ng-class="{upValue: unitsReportData.data.foodCapture.trendUpward, downValue : !unitsReportData.data.foodCapture.trendUpward}">{{unitsReportData.data.foodCapture.current}}</td>
						<td>{{unitsReportData.data.foodCapture.target}}</td>
					</tr>
							<tr>
								<td class="firstColumnTable">SM/CRE/Sr. SM/S Sup</td>
								<td>1000</td>
								<td>2000</td>
								<td>2500</td>
								<td>3200</td>
								<td>4000</td>
							</tr>
							<tr>
								<td class="firstColumnTable">Asst. Mgr./Cafe Handler</td>
								<td>1500</td>
								<td>3000</td>
								<td>3750</td>
								<td>4700</td>
								<td>6000</td>
							</tr>
							<tr>
								<td class="firstColumnTable">Cafe Manager</td>
								<td>2000</td>
								<td>4000</td>
								<td>5000</td>
								<td>6250</td>
								<td>8000</td>
							</tr>
						</table>
					</div>
				</div> &ndash;&gt;
				<div class="row">
					<div class="col-xs-4" data-ng-if="giftCardOfferInfo != undefined && giftCardOfferInfo != null && giftCardOfferInfo.length > 0">
						<div class="row" >
							<div class="col-xs-12" style="text-align: left;height: 22px;">
								<blink><span class="tableLabelHighlightText">Gift Card Offers</span></blink>
							</div>
						</div>
						<div class="row">
							<div class="col-xs-12">
								<table class="table table-striped table-bordered">
									<tr>
										<th>Card Value</th>
										<th>Extra Value</th>
										<th>Last Date</th>
									</tr>
									<tr data-ng-repeat="offer in giftCardOfferInfo track by $index">
										<td>{{offer.denomination}}</td>
										<td>{{offer.offer * offer.denomination/100}}</td>
										<td>{{offer.endDate}}</td>
									</tr>
								</table>
							</div>
						</div>
					</div>
					<div ng-class="{'col-xs-8' : (giftCardOfferInfo != undefined && giftCardOfferInfo != null && giftCardOfferInfo.length > 0), 'col-xs-12' : (giftCardOfferInfo == undefined || giftCardOfferInfo == null || giftCardOfferInfo.length == 0)}">
						<div class="row">
							<div class="col-xs-12" style="text-align: left;">
								<span class="tableLabelText">Cafe Sales</span>
							</div>
						</div>
						<div class="row">
							<div class="col-xs-12">
								<table class="table table-striped table-bordered">
									<tr>
										<th></th>
										<th>Tgt</th>
										<th>Now</th>
										<th>LWSD</th>
									</tr>
									<tr data-ng-repeat=" item in unitsReportData.data">
										<td class="firstColumnTable">{{item.name}}</td>
										<td>{{item.target}}</td>
										<td>{{item.current}}</td>
										<td>{{item.lw}}</td>
									</tr>
								</table>
							</div>
						</div>
					</div>

				</div>
			</div>
		</div>

	</div>
</div>-->

<div class="row">
    <div style="background: #fff" class="col-xs-12">
        <button class="btn btn-lg coverButton" type="button"
                data-ng-click="complimentaryOrderStart()">Compl. Order
        </button>
        <button class="btn btn-lg coverButton" type="button"
                data-ng-click="wastageOrderStart()">Book Wastage
        </button>
        <button class="btn btn-lg coverButton" data-ng-if="isTakeaway"
                type="button" data-ng-click="openOrders()">Open Orders
        </button>

        <button class="btn btn-lg coverButton" type="button"
                data-ng-click="openInventoryScreen()">Inventory
        </button>
        <!--<button class="btn btn-lg coverButton" type="button"
            data-ng-click="openSettlementTypeObj()">Settlement type
            Report</button>-->
        <button class="btn btn-lg coverButton" type="button"
                data-ng-click="goToCustomerOrderSummary()">Customer Order
            Summary
        </button>

        <button class="btn btn-lg coverButton" type="button"
                data-ng-click="openOrderSearchScreen()">Order Search
        </button>

        <button class="btn btn-lg coverButton"
                data-ng-if="isCafe || isDelivery" type="button"
                data-ng-click="manageRider()">Manage SDP
        </button>

        <button data-ng-if="!disableForTakeaway"
                class="btn btn-lg coverButton" type="button"
                data-ng-click="dayCloseModalOpen()">Day Close
        </button>

        <!--<button data-ng-if="!disableForTakeaway"
                class="btn btn-lg coverButton" type="button"
                data-ng-click="cancelDayCloseModalOpen()">Cancel Day Close</button>-->

        <button class="btn btn-lg coverButton" type="button"
                data-ng-click="generateManagerReport()">Manager's Report
        </button>
        <!--<button class="btn btn-lg coverButton" type="button"
            data-ng-click="openItemConsumptionModal()">Item Consumption
            Report</button>-->

        <button class="btn btn-lg coverButton" type="button"
                data-ng-click="openTerminalSettlementTypeObj()">Terminal
            Report
        </button>

        <button class="btn btn-lg coverButton" type="button"
                data-ng-click="testPrinter()">Test Printer
        </button>

        <button data-ng-if="isDev()" class="btn btn-lg coverButton"
                type="button" data-ng-click="testPrint()">Test Print
        </button>

        <button data-ng-if="isDev()" class="btn btn-lg coverButton"
                type="button" data-ng-click="testRawPrint()">Test Raw Print
        </button>

        <!-- Not used by Accounts Team Anymore -->
        <!-- 		<button class="btn btn-lg coverButton" type="button">
                    <a href="https://form.jotform.me/**************" target="_blank">Cash
                        Management</a>
                </button> -->

        <button class="btn btn-lg coverButton">
            <a href="https://form.jotform.me/**************" target="_blank">
                Petrol Bills</a>
        </button>

        <button class="btn btn-lg coverButton" type="button"
                data-ng-click="showPullManagement()">Settlement type
            verification
        </button>

        <button class="btn btn-lg coverButton" type="button"
                data-ng-click="showPullSettlement()">Banking Transfer
        </button>

        <button class="btn btn-lg coverButton" type="button"
                data-ng-click="showSettlementsViewScreen()">Banking View
        </button>
        <!--
    <button class="btn btn-lg coverButton" type="button">
        <a href="https://form.jotform.me/**************" target="_blank">Sodexo & Ticket rst Management</a>
    </button>

    <button data-ng-if="isCafe" class="btn btn-lg coverButton" type="button"
            data-ng-click="csLogOut()">Customer Logout
    </button>
    -->

        <button class="btn btn-lg coverButton" data-ng-if="unitDetails.address.city=='Gurgaon'">
            <a href="https://docs.google.com/a/chaayos.com/forms/d/1Kh0PVu0uDE9xPAgvA7eqIyno0aG_oqzeaWPofia3a98/viewform?c=0&w=1"
               target="_blank">Change Commission Form</a>
        </button>

        <button class="btn btn-lg coverButton">
            <a href="https://track.chaayos.com/ticket/" target="_blank"
               class="btn btn-lg coverButton">Track BIR</a>
        </button>

        <button class="btn btn-lg coverButton">
            <a href="https://forms.gle/Th51ZaLV8qPj86az9" target="_blank">
                Mera Wala Idea </a>
        </button>

        <button class="btn btn-lg coverButton">
            <a href="https://stpltd.typeform.com/to/a1BKah" target="_blank">Trip Sheet</a>
        </button>

        <button class="btn btn-lg coverButton">
            <a href="https://form.jotform.me/80873780700459" target="_blank">
                Invoice Submission Form - Utilities
            </a>
        </button>

        <button class="btn btn-lg coverButton" type="button" data-ng-show="isManager"
                data-ng-click="viewManualBillBookDetails()">Bill Book
        </button>

        <button class="btn btn-lg coverButton" type="button"
                data-ng-click="meterReadingDetails()">Electricity Meter
        </button>

        <button class="btn btn-lg coverButton" type="button"
                data-ng-click="trackExpenses()">Expense Tracking
        </button>

        <button class="btn btn-lg coverButton" type="button"
                data-ng-click="goToEmployeeMealSummary()">Employee Meal Summary
        </button>

        <button class="btn btn-lg coverButton" type="button"
                data-ng-click="refreshProductsData()">Reload Products
        </button>

        <button class="btn btn-lg coverButton" type="button" data-ng-click="showMTDPnL()"
        >Show PnL
        </button>

        <button class="btn btn-lg coverButton">
            <a class="btn btn-lg coverButton"
               href="https://github.com/qzind/tray/releases/download/v2.0.4/qz-tray-2.0.4.exe" target="_blank">
                QZ Tray v2.0.4
            </a>
        </button>

        <!--<button class="btn btn-lg coverButton" type="button"
                data-ng-click="supportLink()">Support
        </button>-->

        <button class="btn btn-lg coverButton" type="button"
                data-ng-click="loginFormsSystem()">Forms Login
        </button>

        <button class="btn btn-lg coverButton" type="button"
                data-ng-click="paymentRefund()">Payment Refund
        </button>

        <button class="btn btn-lg coverButton" type="button"
                data-ng-click="partnerOrderDashboard()">Partner Order Dashboard
        </button>

        <button class="btn btn-lg coverButton" type="button" data-ng-if="unitDetails.handoverDate == null"
                data-ng-click="updateHandOverDate()">Handover Date
        </button>

    </div>
</div>
<span us-spinner spinner-key="spinner-2" spinner-theme="smallRed"></span>
