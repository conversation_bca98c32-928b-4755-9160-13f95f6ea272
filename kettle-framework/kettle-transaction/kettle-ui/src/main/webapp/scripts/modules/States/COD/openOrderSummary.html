<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<h2 class="text-center" style="margin-bottom: 50px;">
    <button type="button" class="btn btn-warning pull-left"
          style="margin-left: 50px" ng-click="backToCover()">Back</button>
          
    Open Orders Across All Units  
    
    <button type="button"  style="margin-right: 50px" class="btn btn-success pull-right" ng-click="refresh()">Refresh</button>  
</h2>
<accordion close-others="oneAtATime">
    <accordion-group class="panel-info" ng-repeat="(orderStatus, orderData) in openOrderData" changecolors="orderStatus" is-open="isOpen">
        <accordion-heading >
            <span class="status-row">{{orderStatus}}</span>
            <i class="pull-right glyphicon"
                          ng-class="{'glyphicon-chevron-down': isOpen, 'glyphicon-chevron-right': !isOpen}"></i>
        </accordion-heading>
            <table class="table table-striped">

            <thead style="background-color: #FFFFFF">
                <tr>
                    <th>Generated Bill No</th>
                    <th>External Order Id</th>
                    <th>Brand</th>
                    <th>Partner Name</th>
                    <th>Unit</th>
                    <th>Customer Name</th>
                    <th>Contact Number</th>
                    <th>Creation Time</th>
                    <th>Elapsed Time (m)</th>
                    <th>Settlement Type</th>
                    <th>Delivery Partner</th>
                    <th>Detail</th>
                </tr>
            </thead>
            <tbody>
                <tr  ng-repeat="order in orderData" data-ng-class="{outOfDelivery : order.order.ood }">
                    <td><a href
                           data-ng-click="openOrderSearch(order.order.generateOrderId, order.deliveryPartner)"
                           style="color: dodgerblue"><b>{{order.order.generateOrderId}}</b></a></td>
                    <td >
                    <span data-ng-if="order.order.channelPartner > 2 && order.order.channelPartner !=9  && order.order.channelPartner !=10 ">
                    {{order.order.sourceId}} </span>
                    <span data-ng-if="order.order.channelPartner < 3 || order.order.channelPartner ==9  || order.order.channelPartner ==10 ">
                    NA </span>
                    </td>
                    <td class="brandName {{order.brand.brandCode}}">{{order.brand.brandName}}</td>
                    <td data-ng-class="getPartnerClass(order.channelPartner.name)">{{order.channelPartner.name}}</td>
                    <td>{{order.order.unitName}}</td>
                    <td>
                        <span data-ng-if="order.customer.firstName.length>0">{{order.customer.firstName}} {{order.customer.lastName}}</span>
                        <span data-ng-if="order.customer.firstName.length<1">No name found</span>
                    </td>
                    <td>{{order.customer.countryCode}}-{{order.customer.contactNumber}}</td>
                    <td>{{order.order.billCreationTime | date:'yyyy-MM-dd HH:mm'}}</td>
                    <td>{{order.elapsedTime}}</td>
                    <td>{{getSettlements(order.order)}}</td>
                    <td ng-if="order.deliveryPartner.id > 1">
                    	<strong>{{order.deliveryPartner.name}}</strong>
                    	<button data-ng-if="order.deliveryDetails == null && order.deliveryPartner.id != 5"
                    		data-ng-click="assignManualTicket(order.order.orderId, order.order.generateOrderId, order.order.unitId)"
                    		class="btn btn-primary">Add Ticket</button>
                    	<span data-ng-if="order.deliveryDetails.deliveryStatus.length>0">[{{order.deliveryDetails.deliveryStatus}}]</span>
                    </td>
				<td ng-if="order.deliveryPartner.id <= 1 || order.deliveryPartner.id == null">
					<button class="btn-primary" 
						ng-click="assignManualTicket(order.order.orderId, order.order.generateOrderId, order.order.unitId)">
						Assign Manually
					</button>
				</td>
				<td>
				<button class="btn btn-info" data-ng-click="showDetails(order)">show</button>
				</td>

		</tr>
            </tbody>
            </table>
    </accordion-group>
</accordion>
