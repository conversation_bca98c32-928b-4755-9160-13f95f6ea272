<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div data-flash-message="5000"></div>
<div data-ng-if="isDefaultPasscode" class="xalert"><span class="blink">You are still using default password. Please change your password.</span></div>
<p class="text-center" style="margin-top: 40px; margin-left: 40px">&nbsp;&nbsp;
    Unit Id:<span style="color: lightskyblue;font: 22px sans-serif;"> {{currentUser.unitId}}</span>
    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
    Unit Name:<span style="color: lightskyblue;font: 19px sans-serif; margin-left: 5px;">{{unitDetails.name}}</span>
    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
    Employee Id:<span style="color: lightskyblue;font: 22px sans-serif;">{{currentUser.userId}}</span>
    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
    Employee Name:<span style="color: lightskyblue;font: 19px sans-serif;margin-left: 5px;">{{currentUser.userName}}</span>
    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
    Terminal Id:<span style="color: lightskyblue;font: 19px sans-serif;margin-left: 5px;">{{currentUser.terminalId}}</span>
    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
</p>

<div style="position: absolute; margin-top:-70px; margin-left:90%;">
    <button class="btn btn-lg coverLogoutButton"  type="button"
            data-ng-click="logOut()">Log Out
    </button>
</div>

<div style="margin: 70px; background-color:lightgoldenrodyellow">
    <button class="btn btn-lg coverButton" type="button"
            data-ng-click="orderStart(false)">Chaayos Order Start
    </button>

    <button class="btn btn-lg coverButton" type="button"
            data-ng-click="swiggyOrderStart()">Swiggy Order Start
    </button>

    <button class="btn btn-lg coverButton" type="button"
            data-ng-click="amazonOrderStart()">Amazon Order Start
    </button>
    <button class="btn btn-lg coverButton" type="button"
            data-ng-click="orderStart(true)">Other Order Start
    </button>

    <button class="btn btn-lg coverButton" type="button"
            data-ng-click="openOrderSearchScreen()">Order Search
    </button>

    <button class="btn btn-lg coverButton" type="button"
            data-ng-click="partnerOrderDashboard()">Partner Order Dashboard
    </button>

    <button class="btn btn-lg coverButton" type="button"
            data-ng-click="showCafeTimingsDashboard()">Cafe Timings Dashboard
    </button>

    <button class="btn btn-lg coverButton" type="button"
            data-ng-click="openInventoryScreen()">Inventory
    </button>

    <button class="btn btn-lg coverButton" type="button"
            data-ng-click="openOrderSummary()">Open Order Summary
    </button>

    <button class="btn btn-lg coverButton" type="button"
            data-ng-click="goToCustomerOrderSummary()">Customer Order Summary
    </button>

    <button class="btn btn-lg coverButton" type="button"
            data-ng-click="pendingRefunds()">Pending Refunds
    </button>


    <button class="btn btn-lg coverButton" type="button"
            data-ng-click="partnerCafeDashboard()">Partner Cafe Dashboard
    </button>


    <button class="btn btn-lg btn-teal coverButton" type="button"
            data-ng-click="subscriptionStart()">Subscription Start
    </button>

    <button class="btn btn-lg btn-teal coverButton" type="button"
            data-ng-click="subscriptionSearch()">Subscription Search
    </button>

    <button class="btn btn-lg btn-teal coverButton" type="button"
            data-ng-click="subscriptionOrderSearch()">Subscription Order Search
    </button>

    <button class="btn btn-lg btn-teal coverButton" type="button"
            data-ng-click="subscriptionOrderByUnit()">View Subscription Orders For Unit
    </button>

    <button class="btn btn-lg coverButton" type="button">
        <a href="https://form.jotform.me/63212623416447" target="_blank">Petrol Bills</a>
    </button>

    <button class="btn btn-lg coverButton" type="button"
            data-ng-click="viewSDP()">View SDP
    </button>

    <button class="btn btn-lg coverButton" type="button"
            data-ng-click="cafePartnerStatus()">Cafe Partner Status
    </button>

    <button class="btn btn-lg coverButton" type="button"
            data-ng-click="resetConfig()" data-ng-show="isManager">Reset Configuration</button>
</div>

 <p class="text-center" style="margin-top: 40px; margin-left: 40px" data-ng-init="init()">
    Last 3 Orders:<a href="javascript:" data-ng-click="openOrderSearch(lastOrder)"
                     class="lastThreeOrder"
                     data-ng-repeat="lastOrder in lastThreeOrderArray track by $index"
                     data-ng-class="{lastOrderCss:$index == 2, firstTwo:$index < 2}"> {{lastOrder}}</a>

</p>





