<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div data-flash-message="5000"></div>
<div class="container-fluid" data-ng-init="init()">
	<div class="row">
		<div class="col-xs-12">
			<h2 class="text-center" style="font-family: 'typewriter';">Subscription Search</h2>
		</div>
	</div>
	<div class="row" style="margin-top:20px;" data-ng-hide="showStatusEventDetails">
		<div class="col-xs-2">
			<button class="btn btn-warning pull-left" data-ng-click="backToCover()">Back</button>
		</div>
		<div class="col-xs-10">
			<form class="form-horizontal">
				<div class="form-group">
					<div class="col-xs-4">
						<input type="text" class="form-control" data-ng-model="subscriptionInput" placeholder="Search by contact number" />
					</div>
					<div class="col-xs-2">
						<button class="btn btn-info" data-ng-click="getSubscriptions()">Search</button>
					</div>
				</div>
			</form>
		</div>
	</div>
	<div class="row" data-ng-hide="showStatusEventDetails">
		<div class="col-xs-12">
			<table class="table table-striped" style="background:white" data-ng-if="subscriptionList.length>0">
				<tr>
					<th>Subscription Id</th>
					<th>Status</th>
					<th>Actions</th>
				</tr>
				<tr data-ng-repeat="subscription in subscriptionList">
					<td>{{subscription.generateOrderId}}</td>
					<td>{{subscription.subscriptionDetail.subscriptionStatus}}</td>
					<td>
						<button class="btn btn-xs btn-danger" data-ng-click="openOrderSearch(subscription)">View Detail</button>
						<button class="btn btn-xs btn-danger" 
						data-ng-if="subscription.subscriptionDetail.subscriptionStatus!='CANCELLED' && !subscriptionExpired(subscription)" 
						data-ng-click="editSubscription(subscription)">Edit subscription</button>
						<button class="btn btn-xs btn-danger" 
						data-ng-if="subscription.subscriptionDetail.subscriptionStatus!='CANCELLED' && !subscriptionExpired(subscription)" 
						data-ng-click="holdSubscription(subscription)">Hold subscription</button>
						<button class="btn btn-xs btn-danger"
						data-ng-if="subscription.subscriptionDetail.subscriptionStatus!='CANCELLED' && !subscriptionExpired(subscription)" 
						data-ng-click="closeSubscription(subscription)">Cancel subscription</button>
						<button class="btn btn-xs btn-danger"
						data-ng-if="subscription.subscriptionDetail.subscriptionStatus!='CANCELLED' && !subscriptionExpired(subscription)" 
						data-ng-click="showStatusEventsForSubscription(subscription)">View Status Events</button>
						<button class="btn btn-xs btn-danger"
						data-ng-if="subscription.subscriptionDetail.subscriptionStatus!='CANCELLED' && !subscriptionExpired(subscription)" 
						data-ng-click="showOrdersForSubscription(subscription)">View Orders</button>
					</td>
				</tr>
			</table>
			<div class="alert alert-info" data-ng-if="subscriptionList.length==0">No subscription found.</div>
		</div>
	</div>
	<div class="row" data-ng-if="showStatusEventDetails">
		<div class="col-xs-12 holdDetailsSection">
			<div>
				<button class="btn btn-warning" data-ng-click="backToSubscriptions()">Back</button>
			</div>
			<div class="well subscriptionDetails">
				<p>Subscription Details</p>
				<span>Id: {{selectedSubscription.subscriptionDetail.subscriptionId}}</span>
				<span>Status: {{selectedSubscription.subscriptionDetail.subscriptionStatus}}</span>
				<span>Start Date: {{selectedSubscription.subscriptionDetail.startDate | date:'yyyy-MM-dd hh:mm:ss a'}}</span>
				<span>End Date: {{selectedSubscription.subscriptionDetail.endDate | date:'yyyy-MM-dd hh:mm:ss a'}}</span>
				<span>Type: {{selectedSubscription.subscriptionDetail.type}}</span>
				<span data-ng-if="selectedSubscription.subscriptionDetail.type=='MONTHLY'">
					Days: {{selectedSubscription.subscriptionDetail.daysOfTheMonth}}
				</span>
				<span data-ng-if="selectedSubscription.subscriptionDetail.type=='WEEKLY'">
					Days: {{convertWeekDayFromIndex(selectedSubscription.subscriptionDetail.daysOfTheWeek)}}
				</span>
				<span>Time: {{convertTimeFromIndex(selectedSubscription.subscriptionDetail.timeOfTheDay)}}</span>
			</div>
			<p class="text-center" data-ng-show="showStatusEventLoader">Loading hold events...</p>
			<table class="table table-striped" style="background:white" data-ng-hide="showStatusEventLoader || statusEventList.length==0">
				<tr>
					<th>Event Id</th>
					<th>Event Type</th>
					<th>Event Start Date</th>
					<th>Event End Date</th>
					<th>Last Update Time</th>
					<th>Reason</th>
					<th>Actions</th>
				</tr>
				<tr data-ng-repeat="statusEvent in statusEventList">
					<td>{{statusEvent.subscriptionEventId}}</td>
					<td>{{statusEvent.eventType}}</td>
					<td>{{statusEvent.eventStartDate | date:'yyyy-MM-dd hh:mm:ss a'}}</td>
					<td>{{statusEvent.eventEndDate | date:'yyyy-MM-dd hh:mm:ss a'}}</td>
					<td>{{statusEvent.lastUpdateTime | date:'yyyy-MM-dd hh:mm:ss a'}}</td>
					<td>{{statusEvent.reasonText}}</td>
					<td>
						<button class="btn btn-xs btn-danger" data-ng-click="statusEventEditAction(statusEvent)">Edit Event</button>
						<button class="btn btn-xs btn-danger" data-ng-click="statusEventCancelAction(statusEvent)"
						 data-ng-if="dateGTE(statusEvent.eventStartDate) && statusEvent.eventType!='CANCELLED'">Cancel Event</button>
					</td>
				</tr>
			</table>
			<div class="alert alert-info" data-ng-show="statusEventList.length==0">No hold events found.</div>
		</div>
	</div>
</div>