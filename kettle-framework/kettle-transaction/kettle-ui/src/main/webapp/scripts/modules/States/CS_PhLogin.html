<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<script type="text/javascript">

    function stopRKey(evt) {
        var evt = (evt) ? evt : ((event) ? event : null);
        var node = (evt.target) ? evt.target : ((evt.srcElement) ? evt.srcElement : null);
        if ((evt.keyCode == 13) && (node.type == "text")) {
            return false;
        }
    }

    document.onkeypress = stopRKey;

</script>
<div flash-message="5000"></div>
<!--<button type="button" class="btn btn-success pull-left"
        style="margin-left: 50px"
        data-ng-click="customerDone()">Restart</button>-->
<div id="">
    <img src="images/logo3.png" class="brandImg center-block" style="margin-top: 20px">

    <h2 class="text-center" style="color: #737370; margin-top: 40px; font-family: 'typewriter';">Please enter your
        mobile
        number</h2>


    <form novalidate name="CSloginForm" role="form"
          class="csLoginForm col-xs-6 col-xs-offset-3">

        <div class="container" style="margin-left: 20px">
            <div class="row">

                <div class="form-group col-xs-7">

                    <input type="number" name="phno"
                           id="phno"
                           style="width: 400px;font: 28px sans-serif;text-align: center;
               border: 1px solid #737370;line-height: 1.6em; font-family: 'typewriter';"
                           class="phInput" oninput="maxLengthCheck(this)" maxlength="10" max="9999999999" data-ng-change="checkNumber()"
                           placeholder="Contact number"
                           data-ng-maxlength="10" data-ng-minlength="1"
                           data-ng-model="csNumber"  required/>
                </div>

                <div class="form-actions col-xs-2">
                    <button type="submit"
                            data-ng-disabled="!(csNumberCheck == true)"
                            class="btn btn-success btn-lg customerBtn"
                            data-ng-click="csLookUp()"
                            style=" font-family: 'typewriter';">Start
                    </button>
                </div>

            </div>
        </div>


        <div data-ng-messages="CSloginForm.phno.$error" style="color:maroon" role="alert">
            <div data-ng-if="CSloginForm.phno.$touched">
                <div data-ng-message="required">Mobile Number field is empty</div>
                <div data-ng-message="maxlength">Please enter a 10 digit valid Contact number</div>
                <div data-ng-message="minlength">Please enter a 10 digit valid Contact number</div>
            </div>
        </div>


    </form>
</div>

    <script>
            function maxLengthCheck(object) {
                if (object.value.length > object.maxLength)
                    object.value = object.value.slice(0, object.maxLength)
            }
        </script>
    <script>
            $(function () {
                $(this).bind("contextmenu", function (e) {
                    e.preventDefault();
                });
            });
        </script>



