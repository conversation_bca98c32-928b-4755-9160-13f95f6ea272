<style>
.row_class{
    padding-top: 30px;
    padding-bottom:20px;
    margin-left: 0px;
    margin-right: 0px;
    background-color: azure;
}
.row_result{
    padding-top: 30px;
    padding-bottom:20px;
    margin-left: 0px;
    margin-right: 0px;
    background-color: limegreen;
}
.notice{
    text-align: center;
    margin: 15px;
    font-weight: bold;
    font-size: 18px;
}
.noResult{
    font-size: 18px;
    text-align: center;
    padding-top: 30px;
    padding-bottom:20px;
    font-weight: bold;
    color: darkred;
}
</style>
<div data-flash-message="5000"></div>
    <div class="row" style="margin-top:20px;">
        <div class="col-xs-2">
            <button class="btn btn-warning pull-left" data-ng-click="backToCover()">Back</button>
        </div>
        <div class="col-xs-4">
            <input type="text" pattern="[6-9][0-9]{9}" data-ng-model="searchNumber" maxlength="10" placeholder="Enter the Phone Number"
                   style="width:50%;border-radius: 4px; cursor: pointer;" required="required">
            <button data-ng-click="GetSearchedContact()" class="btn btn-warning" >CHECK</button>
        </div>
</div>
<div class="table" style="margin-top:20px;border-radius: 5px;
        background-color: #f2f2f2;
        padding: 20px;" ng-if="resultFound != undefined && resultFound">
    <div class="row" >
        <div  class="col col-xs-2" ><b>Order Payment Id</b></div>
        <div  class="col col-xs-1"><b>Request Time</b></div>
        <div  class="col col-xs-2"><b>Customer Name</b></div>
        <div  class="col col-xs-2"><b>Contact Number</b></div>
        <div  class="col col-xs-1"><b>Transaction Amount</b></div>
        <div  class="col col-xs-2"><b>Payment Status</b></div>
        <div  class="col col-xs-2"><b>Refund</b></div>
    </div>
    <div ng-repeat="contact in  contactResponse.applicable">
        <div ng-class="{'row row_class':contact.paymentStatus == 'REFUND_PROCESSED' && contact.paymentStatus == 'REFUND_INITIATED',
        'row row_result':contact.paymentStatus == 'REFUND_PROCESSED' || contact.paymentStatus == 'REFUND_INITIATED'}">
                    <div  class="col col-xs-1">{{contact.orderPaymentDetailId}}</div>
                    <div  class="col col-xs-2">{{contact.requestTime| date:'yyyy-MM-dd HH:mm:ss'}}</div>
                    <div  class="col col-xs-2">{{contact.customerName}}</div>
                    <div  class="col col-xs-1">{{contact.contactNumber}}</div>
                    <div  class="col col-xs-1">{{contact.transactionAmount}}</div>
                    <div  class="col col-xs-2">{{contact.paymentStatus}}</div>
                    <div ng-if='contact.paymentStatus != "REFUND_PROCESSED" && contact.paymentStatus != "REFUND_INITIATED"'  class="col col-xs-3"><button class="btn btn-primary pull-left"
                                                       data-ng-click="refundPayment(contact)"
                                                        ng-disabled="refundSuccess">Refund</button></div>
                    <div ng-if='contact.paymentStatus == "REFUND_PROCESSED" || contact.paymentStatus == "REFUND_INITIATED"' class="col col-xs-3">Refunded</div>
        </div>
    </div>
    <div class="row notice" ng-if="contactResponse.toBeApplicable != undefined && contactResponse.toBeApplicable.length > 0"> Recent Payments not Applicble for refund</div>
    <div class="row" ng-if="contactResponse.toBeApplicable != undefined && contactResponse.toBeApplicable.length > 0">
        <div  class="col col-xs-2" ><b>Order Payment Id</b></div>
        <div  class="col col-xs-2"><b>Request Time</b></div>
        <div  class="col col-xs-2"><b>Customer Name</b></div>
        <div  class="col col-xs-1"><b>Contact Number</b></div>
        <div  class="col col-xs-2"><b>Transaction Amount</b></div>
        <div  class="col col-xs-3"><b>Remaining Time(In Minutes)</b></div>
    </div>
    <div ng-repeat="contact in  contactResponse.toBeApplicable" ng-if="contactResponse.toBeApplicable != undefined && contactResponse.toBeApplicable.length > 0">
        <div class="row row_class">
            <div  class="col col-xs-2" >{{contact.orderPaymentDetailId}}</div>
            <div  class="col col-xs-2">{{contact.requestTime| date:'yyyy-MM-dd HH:mm:ss'}}</div>
            <div  class="col col-xs-2">{{contact.customerName}}</div>
            <div  class="col col-xs-1">{{contact.contactNumber}}</div>
            <div  class="col col-xs-2">{{contact.transactionAmount}}</div>
            <div  class="col col-xs-3"><b>{{contact.remainingMinutes}}</b></div>
        </div>
    </div>
</div>
<div class="noResult" ng-if="resultFound != undefined && !resultFound">
    <p><b>No Request Found!!</b></p>
</div>