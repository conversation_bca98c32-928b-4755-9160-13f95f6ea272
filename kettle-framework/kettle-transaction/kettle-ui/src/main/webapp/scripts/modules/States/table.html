<div class="row coverFlash" flash-message="5000"></div>
<div class="container-fluid" data-ng-init="init()">

	<!-- Table Selection View -->

	<div class="row">
		<div class="col-xs-4 text-left">
			<button type="button" class="btn btn-warning" style="margin: 10px;"
				data-ng-click="backToCover()">back</button>
		</div>
		<div class="col-xs-4 text-center">
			<h3>Select Table</h3>
		</div>
		<div class="col-xs-4 text-center"></div>
	</div>
	<div class="row">
		<div class="col-xs-12 text-center" data-ng-if="loadingData">Loading Tables, Please wait.</div>
		<div class="col-xs-12 text-center">
			<button class='btn btn-lg posProductButton lime-background'
				data-ng-class="{'red-orange-background': table.tableStatus == 'OCCUPIED'}"
				type="button" data-ng-click="openTable(table)"
				data-ng-repeat="table in tables">{{table.tableNumber}}</button>
		</div>
	</div>
</div>
