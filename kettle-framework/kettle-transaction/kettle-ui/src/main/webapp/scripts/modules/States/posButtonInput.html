
    <!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div ng-if="productsForSubCategory.length > 0">
        <div ng-repeat="row in rows()">
            <div class="row productRow" >
                <button class='btn btn-lg posProductButton' type="button"
                        ng-repeat="item in itemsInThisRow($index)"
                        ng-class="{vegButton:item.attribute == 'VEG',nonVegButton:item.attribute == 'NON_VEG'}"
                        ng-click="addNewProductToOrderItemArray(item)">{{item.name}}</button>
            </div>
        </div>
    </div>
    <div ng-show="productsForSubCategory.length == 0">
        <p class="text-center" style="background-color: #b3d4fc">Sorry, no products to display in this subcategory</p>
    </div>
    <div flash-message="5000" ></div>


<!--Categories/SubCategories Names-->

<!--<div ng-repeat="(catID, catObj) in transactionMetadata.categories">
    <div ng-repeat="(detailHeading, detailInfo) in catObj.detail">
        <div ng-if="detailHeading == 'name'">
            <p> {{detailInfo}} </p></br>
        </div>
    </div>
    <div ng-repeat="(detailHeading, detailInfo) in catObj.content">
        <p> {{detailInfo.name}} </p></br>
    </div>
</div>-->





