<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div data-flash-message="5000"></div>
<div class="container-fluid" data-ng-init="init()">
	<div class="row" data-ng-hide="showPullView">
		<div class="col-xs-12">
			<h2 class="text-center" style="font-family: 'typewriter';">View Transferred Settlements For Unit</h2>
		</div>
	</div>
	<div class="row" style="margin-top:20px;" data-ng-hide="showPullView">
		<div class="col-xs-2">
			<button class="btn btn-warning pull-left" data-ng-click="backToCover()">Back</button>
		</div>
		<div class="col-xs-4">
			<div class="datepicker" data-date-format="yyyy-MM-dd hh:mm:ss" data-date-max-limit="{{today}}">
				<input type="text" data-ng-model="startDate" class="form-control" placeholder="yyyy-mm-dd hh:mm:ss" />
			</div>
		</div>
		<div class="col-xs-4">
			<div class="datepicker" data-date-format="yyyy-MM-dd hh:mm:ss" data-date-max-limit="{{today}}">
				<input type="text" data-ng-model="endDate" class="form-control" placeholder="yyyy-mm-dd hh:mm:ss" />
			</div>
		</div>
		<div class="col-xs-2">
			<button data-ng-click="getPullSettlements()" class="btn btn-warning">Get Settlements</button>
		</div>
	</div>
	<div class="row" style="margin-top:20px;" data-ng-hide="showPullView">
		<div class="col-xs-12">
			<div data-ng-show="listLoading" class="text-center"><img src="images/loader.gif" /></div>
			<div class="alert alert-info" data-ng-if="pullSettlements.length>0">Note: Please create Happay vouchers for <strong>Extra Amount</strong> highlighted, if not created already</div>
			<table class="table table-striped" style="background:#fff;" data-ng-if="pullSettlements.length>0">
				<tr>
					<th>Settlement Id</th>
					<th>Payment Mode</th>
					<th>Settlement Slip Number</th>
					<th>Original Amount</th>
					<th>Extra Amount</th>
					<th>Total Amount</th>
					<!-- <th>Settled Amount</th>
					<th>Unsettled Amount</th>
					<th>Closing Amount</th> -->
					<th>Creation Time</th>
					<!-- <th>Settlement Status</th> -->
					<th>Actions</th>
				</tr>
				<tr data-ng-repeat="settlement in pullSettlements">
					<td>{{settlement.id}}</td>
					<td>{{settlement.paymentMode.description}}</td>
					<td>{{settlement.settlementServiceProvider}}</td>
					<td>{{settlement.originalAmount}}</td>
					<td style="background-color: red;color:white;">{{settlement.extraAmount}}</td>
					<td>{{settlement.totalAmount}}</td>
					<!-- <td>{{settlement.settlementAmount==null?'NA':settlement.settlementAmount}}</td>
					<td>{{settlement.unsettledAmount==null?'NA':settlement.unsettledAmount}}</td>
					<td>{{settlement.closingAmount==null?'NA':settlement.closingAmount}}</td> -->
					<td>{{settlement.settlementTime | date:'yyyy-MM-dd HH:mm:ss'}}</td>
					<!-- <td>{{settlement.settlementStatus}}</td> -->
					<td>
						<button class="btn btn-danger btn-xs" data-ng-click="viewPull(settlement)">View Components</button>
						<button class="btn btn-danger btn-xs" data-ng-click="viewDenominations(settlement)">View Denominations</button>
					</td>
				</tr>
			</table>
			<div class="alert alert-info" data-ng-if="pullSettlements.length==0">No settlements found.</div>
		</div>

	</div>
	
	<div class="row" data-ng-show="showPullView">
		<div class="col-xs-12">
			<div class="row" style="margin-top:20px;">
				<div class="col-xs-2">
					<button class="btn btn-warning pull-left" data-ng-click="hidePullView()">Back</button>
				</div>
				<div class="col-xs-10">
					<h2 class="text-center" style="font-family:'typewriter';">Component Details</h2>
				</div>
			</div>
			<div class="row" style="margin-top:20px;">
				<div class="col-xs-12">
					<table class="table table-striped" style="background:#fff;" data-ng-if="selectedSettlement.pullDetails.length>0">
						<tr>
							<th>Component Id</th>
							<th>Payment Mode</th>
							<th>Amount</th>
							<th>Business Date</th>
							<th>Status</th>
							<th>Created By</th>
							<th>Witnessed By</th>
							<th>Comment</th>
							<!-- <th>Settlement Id</th> -->
							<th>Actions</th>
						</tr>
						<tr data-ng-repeat="pull in selectedSettlement.pullDetails">
							<td>{{pull.pullPacketId}}</td>
							<td>{{pull.paymentMode.description}}</td>
							<td>{{pull.pullAmount}}</td>
							<td>{{pull.pullDate | date:'yyyy-MM-dd'}}</td>
							<td>{{pull.pullPacketStatus}}</td>
							<td>{{pull.createdByName}}</td>
							<td>{{pull.witnessedBy}}</td>
							<td>{{pull.comment}}</td>
							<!-- <td>{{pull.pullSettlementDetail==null?'NA':pull.pullSettlementDetail.id}}</td> -->
							<td>
								<button class="btn btn-danger btn-xs" data-ng-if="pull.pullPacketStatus!='INITIATED'" 
								 data-ng-click="viewPullDenominations(pull.pullDenominations)">View Component Denominations</button>
								<button class="btn btn-danger btn-xs" 
								 data-ng-click="viewClosure(pull.closurePaymentDetail)">View Closure Details</button>
							</td>
						</tr>
					</table>
					<div class="alert alert-info" data-ng-if="selectedSettlement.pullDetails.length==0">No components found.</div>
				</div>
			</div>
		</div>
	</div>
	
</div>