<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style>
.expense-container {
	padding-right: 5px;
	padding-left: 5px;
	margin-right: 5%;
	margin-left: 5%;
}

.sale {
	color: green;
}

.cost {
	color: red;
}

.warning-budget {
	color: #c53737;
}

.notype {
	font-size: x-large;
}

.net-profit {
	border: 2px solid green;
	background-color: white;
	color: black;
	font-size: x-large;
	margin-top: 24px;
}

.table-border-PnL {
	border-collapse: separate;
}

.table-border-PnL {
	border-collapse: separate;
	width: 90%;
}

.table-border-Budget-Exceeded {
	border-collapse: separate;
}
</style>
<div flash-message="5000"></div>
<div data-ng-init="init()" class="expense-container">
	<div class="row">
		<div class="col-xs-2" style="margin-top: 20px">
			<button type="button" class="btn btn-danger pull-left"
				data-ng-click="backToCover()">Back</button>
		</div>
		<div class="col-xs-10">
			<h2 class="text-center" style="font-family: 'typewriter';">MTD
				PnL Report</h2>
		</div>
	</div>

	<div class="row">
		<div class="row">
			<div class="col-xs-12">
				<div class="row">
					<div class="col-xs-3">
						<div class="form-group">
							<label for="expenseEndDate" class="control-label">Till
								date </label>
							<div class="datepicker" data-date-format="yyyy-MM-dd"
								data-date-max-limit="{{today}}">
								<input type="text" id="tillDate"
									data-ng-model="request.tillDate" class="form-control"
									placeholder="yyyy-mm-dd" readonly="readonly" />
							</div>
						</div>
					</div>
					<div class="col-xs-2">
						<button class="btn btn-primary pull-left"
							data-ng-click="getPnLAggregateReport()" style="margin-top: 29px;">View
							Report</button>
					</div>
					<div class="col-xs-2">
						<button class="btn btn-primary pull-left"
							data-ng-click="downloadMTDPnlDetailSheet()" style="margin-top: 29px;">Download Daily</button>
					</div>
					<div class="col-xs-2">
						<button class="btn btn-primary pull-left"
							data-ng-click="downloadFinalizedPnlDetailSheet()" style="margin-top: 29px;">Download Monthly</button>
					</div>
					<div class="col-xs-3 net-profit">Net Profit ={{netProfit}} ({{netProfitPerCent}} %) </div>
				</div>
			</div>
		</div>
		<div class="col-xs-12">
			<div class="row">
				<div class="col-xs-12">
					<div class="col-xs-12"
						data-ng-if="pnlDetail==null || pnlDetail.length == 0"
						style="color: red; font-size: 30px;">Report Not Found</div>
				</div>
			</div>
		</div>
	</div>
	<div class="row">
		<div class="col-xs-7" style="margin-top: 20px;">
			<!--<table class="table table-striped table-bordered table-border-PnL"
				style="background: #fff;"
				data-ng-if="pnlDetail != null && pnlDetail.length > 0">
				<thead>
					<tr>
						<th>PnL Header</th>
						<th>For {{request.tillDate}}</th>
						<th>MTD Till {{request.tillDate}}</th>
						<th>Budget</th>
					</tr>
				</thead>
				<tbody>
					<tr data-ng-repeat="detail in pnlDetail | orderBy : 'order'"
						data-ng-if="detail.order>0">
						<td>{{detail.label}}</td>
						&lt;!&ndash; <td width="10px" data-ng-if = "detail.calculationType == 'COST'"></td> &ndash;&gt;
						<td>{{detail.current}}</td>
						<td>{{detail.value}}</td>
						<td>{{detail.budget != null && detail.budget != 0 ? detail.budget : '-'}} <span
							data-ng-if="detail.budget > 0 && detail.value > detail.budget"
							data-ng-class="{'cost': (detail.calculationType == 'COST'),'sale': (detail.calculationType == 'EARNING'),'notype': (detail.calculationType == 'NO_TYPE')}"
							class="fa fa-arrow-left" style="align: center;"></span></td>
					</tr>
				</tbody>
			</table>-->

			<table class="table  table-bordered "
				   style="background: #fff;"
				   data-ng-if="aggregateList != null && aggregateList.length > 0">
				<thead>
				<tr>
					<th>PnL Header</th>
					<th>For {{request.tillDate}}</th>
					<th>MTD Till {{request.tillDate}}</th>
					<th>Budget Till {{request.tillDate}}</th>
					<th>Total Budget</th>
				</tr>
				</thead>
				<tbody>
				<tr ng-repeat-start="group in aggregateList"
					data-ng-click="setSelectedRow(group)" style="cursor: pointer;"
					data-ng-class="{'rowSelected':group.key===idSelected && group.drilldowns.length>0}">
					<td>{{group.key}} <span data-ng-if="group.drilldowns.length>0" data-ng-class="{'fa fa-plus pull-right':group.key!==idSelected,'fa fa-minus pull-right':group.key===idSelected}" style="align: center;color: #00BD60"></span></td>
					<td>{{group.currentValue}}</td>
					<td>{{group.mtdValue[0]!=null && group.mtdValue[0]!=0?group.mtdValue[0]:'-'}}</td>
					<td>{{group.budgetMtd}}</td>
					<td>{{group.budget != null && group.budget != 0 ? group.budget : '-'}} <span
							data-ng-if="group.budget > 0 && group.mtdValue[0] > group.budget"
							class="fa fa-arrow-left" style="align: center;color: red"></span></td>
				</tr>
				<tr ng-repeat-end=""
					ng-show="group.key==idSelected && groupSelected && group.drilldowns.length>0">
					<td colspan="8">
						<table class="table table-bordered">
							<tr ng-repeat="subGroups in drilldowns  "
								data-ng-class="{'rowSelected':true}">
								<td>{{subGroups.key}}</td>
								<td>{{subGroups.currentValue}}</td>
								<td>{{subGroups.mtdValue[0]!=null && subGroups.mtdValue[0]!=0?subGroups.mtdValue[0]:'-'}}</td>
								<td>{{subGroups.budgetMtd}}</td>
								<td>{{subGroups.budget != null && subGroups.budget != 0 ? subGroups.budget : '-'}} <span
										data-ng-if="subGroups.budget > 0 && subGroups.mtdValue[0] > subGroups.budget"
										class="fa fa-arrow-left" style="align: center;color: red"></span></td>

							</tr>
						</table>

					</td>


				</tr>
				</tbody>
			</table>
		</div>
<!--		<div class="col-xs-5" style="margin-top: 20px;">-->
<!--			<table class="table table-bordered table-border-Budget-Exceeded"-->
<!--				style="background: #fff;"-->
<!--				data-ng-if="budgetExceeded != null && budgetExceeded.length > 0">-->
<!--				<thead>-->
<!--					<tr>-->
<!--						<th class="net-profit"><span>Budget Exceeded-->
<!--								Transactions</span> <select id="notificationType"-->
<!--							name="notificationType" data-ng-model="notificationType"-->
<!--							style="font-size: large; color: blue;">-->
<!--								<option value="ERROR">Error</option>-->
<!--								<option value="WARNING">Warning</option>-->
<!--								<option value="">ALL</option>-->
<!--						</select></th>-->
<!--					</tr>-->
<!--				</thead>-->
<!--				<tbody>-->
<!--					<tr>-->
<!--						<td><i class="fa fa-exclamation-circle"-->
<!--							style="font-size: 20px; color: red;"> - Error</i> <i-->
<!--							class="fa fa-warning" style="font-size: 20px; color: #c53737;">-->
<!--								- Warning</i></td>-->
<!--					</tr>-->
<!--					<tr-->
<!--						data-ng-repeat="budgetData in budgetExceeded | limitTo: limit | filter:{notificationType:notificationType} ">-->
<!--						<td-->
<!--							data-ng-class="{'cost': (budgetData.notificationType == 'ERROR'),'warning-budget': (budgetData.notificationType == 'WARNING'),'notype': (budgetData.notificationType == 'NOTHING')}">-->
<!--							{{$index+1}}. Budget of {{budgetData.expenseLabel}} is-->
<!--							{{budgetData.budgetAmount}} and current expense is-->
<!--							{{budgetData.currentAmount}}. <span-->
<!--							data-ng-if="budgetData.notificationType == 'ERROR'"> So-->
<!--								{{budgetData.requestedAmount}} can not be added on-->
<!--								{{budgetData.createdOn | date}}. <i-->
<!--								class="fa fa-exclamation-circle"-->
<!--								style="font-size: 20px; color: red;"></i>-->
<!--						</span> <span data-ng-if="budgetData.notificationType == 'WARNING'">You-->
<!--								added {{budgetData.requestedAmount}} on {{budgetData.createdOn |-->
<!--								date}} that exceeds your budget by-->
<!--								{{(budgetData.currentAmount+budgetData.requestedAmount) - -->
<!--								budgetData.budgetAmount}}. <i class="fa fa-warning"-->
<!--								style="font-size: 20px; color: #c53737;"></i>-->
<!--						</span>-->
<!--						</td>-->

<!--					</tr>-->
<!--					<tr data-ng-if="limit < budgetExceeded.length">-->
<!--						<td data-ng-click="updateLimit(budgetExceeded.length)"-->
<!--							style="cursor: pointer; color: blue; text-decoration: underline;">-->
<!--							Show All</td>-->
<!--					</tr>-->
<!--					<tr data-ng-if="limit == budgetExceeded.length">-->
<!--						<td data-ng-click="updateLimit(5)"-->
<!--							style="cursor: pointer; color: blue; text-decoration: underline;">-->
<!--							Show Less</td>-->
<!--					</tr>-->
<!--				</tbody>-->
<!--			</table>-->
<!--		</div>-->

	</div>
</div>


