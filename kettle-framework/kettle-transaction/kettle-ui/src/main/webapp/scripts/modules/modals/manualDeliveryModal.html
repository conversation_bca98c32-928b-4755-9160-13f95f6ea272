<div class="modal-header" ng-init="init()">
    <h3 class="modal-title">Provide following delivery details</h3>
</div>
<div class="modal-body">
    <div class="row">
        <div class="form-group col-xs-12">
            <label>Select Delivery Partner:</label>
            <select class="form-control" ng-model="manualDelivery.deliveryPartnerId"
                    ng-options="partner as partner.name for partner in deliveryPartners track by partner.id" >
            </select>
        </div>
        <div class="form-group col-xs-6">
            <label>Add Ticket Id:</label>
            <input type="text" class="form-control" ng-model="manualDelivery.deliveryTaskId"  />
        </div>
        <div class="form-group col-xs-6">
            <label>Add Delivery Task Status:</label>
            <select class="form-control" ng-model="manualDelivery.deliveryStatus"
                    ng-options="status as status.name for status in statusNames track by status.id" >
            </select>
        </div>
        <div class="form-group col-xs-6">
            <label>Add SDP Name:</label>
            <input type="text" class="form-control" ng-model="manualDelivery.deliveryBoyName" />
        </div>
        <div class="form-group col-xs-6">
            <label>Add SDP Phone Number:</label>
            <input type="text" class="form-control" ng-model="manualDelivery.deliveryBoyPhoneNum" />
        </div>
    </div>
</div>
<div class="modal-footer">
    <button class="btn btn-warning" type="button" ng-click="cancel()">Cancel</button>
    <button class="btn btn-primary" type="button" ng-click="ok()">OK</button>
</div>