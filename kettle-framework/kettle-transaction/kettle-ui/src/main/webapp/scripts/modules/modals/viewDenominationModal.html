<div class="modal-header" data-ng-init="init()">
    <h3 class="modal-title">Denomination Details</h3>
</div>
<div class="modal-body">
    <div class="row" style="margin-bottom:20px;">
        <div class="col-xs-12">
            <table class="table table-bordered table-striped" data-ng-if="denomEntity.length>0">
                <tr>
                    <th>Denomination</th>
                    <th>Denomination value</th>
                    <th>Bundle Size</th>
                    <th>No. Of Packets</th>
                    <th>Loose Currency</th>
                    <th>Total Amount</th>
                </tr>
                <tr data-ng-repeat="denom in denomEntity | orderBy : 'denominationDetail.displayOrder'">
                    <td>{{denom.denominationDetail.denominationText}}</td>
                    <td>{{denom.denominationDetail.denominationValue}}</td>
                    <td>{{denom.denominationDetail.bundleSize}}</td>
                    <td>{{denom.packetCount}}</td>
                    <td>{{denom.looseCurrencyCount}}</td>
                    <td>{{denom.totalAmount}}</td>
                </tr>
            </table>
            <div class="alert alert-info" data-ng-if="denomEntity.length==0">No denominations available.</div>
            <h3 class="text-right" style="color:green;" data-ng-if="denomEntity.length>0">Total Amount : {{denomTotalAmount}}</h3>
        </div>
    </div>
</div>
<div class="modal-footer">
    <div class="btn-group col-xs-offset-10" style="margin-bottom: 10px">
        <button class="btn btn-danger" type="button" ng-click="cancel()">Close</button>
        <button class="btn btn-default" type="button" ng-click="ok()">OK</button>
    </div>
</div>