<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->
<style>
    .success {
        color: limegreen;
    }

    .danger {
        color: red;
    }

    .makeSmall {
        position: absolute;
        top: -50px;
        transform: scaleY(0.7);
    }


</style>
<div flash-message="5000"></div>
<div ng-init="init()">
    <div class="container-fluid">
        <div class="row">
            <div class="col-xs-2">
                <button class="btn btn-alert pull-left"
                        style="margin-top: 10px"
                        ng-click="backToCover()">Back
                </button>
            </div>
            <div class="col-xs-6">
                <h2 class="text-center">CAFE STATUS</h2>
            </div>

            <div class="col-xs-2">
                <button class="btn btn-primary pull-right "
                        style="margin-top: 10px"
                        ng-click="getAllCafe()">Refresh
                </button>

            </div>
            <div class="col-xs-2">
                <button
                        class="btn btn-primary"
                        style="margin-top: 10px"
                        ng-click='downloadCSV({ filename: "partner-cafe-status.csv" });'>Download
                    CSV
                </button>
            </div>
        </div>

        <!--<div class="col-xs-5">-->
            <!--<select-->
                    <!--ui-select2-->
                    <!--class="form-control"-->
                    <!--style="width: 100% !important"-->
                    <!--data-ng-model="selectedBrandId"-->
                    <!--data-ng-change="selectBrand(selectedBrandId)"-->
                    <!--data-placeholder="Select Brand"-->
                    <!--data-ng-options="brandName for brandName in brand">-->
            <!--</select>-->
        <!--</div>-->
        <div class="col-xs-6">
            <!--<button type="button" data-ng-hide="loading" class="btn btn-primary"-->
                    <!--data-ng-click="getAllCafe()">GO-->
            <!--</button>-->
            <div class="form-group col-sm-offset-2 col-sm-6 margin-5 align-center">
                <input
                    type="text"
                    ng-model="search"
                    ng-change="filter()"
                    placeholder="Filter"
                    class="form-control ng-pristine ng-valid ng-touched"/>
            </div>
            <!--<button type="button" data-ng-hide="loading" class="btn btn-primary"-->
                    <!--data-ng-click="getCafes()">GO Fetch-->
            <!--</button>-->
        </div>

<div>
        <table class="table table-bordered table-striped" style="font-size: 11px">
            <tr>
                <th scope="col" style="font-size: 20px">Unit Id</th>
                <th scope="col" style="font-size: 20px">Unit Name</th>
                <th scope="col" style="font-size: 20px">Brand Id</th>
                <th scope="col" style="font-size: 20px">Brand Name</th>
                <th scope="col" style="font-size: 20px">Swiggy</th>
                <th scope="col" style="font-size: 20px">Zomato</th>
            </tr>
            <tr ng-repeat="dt in filtered = (resultFetch | filter:search )">
                <td style="font-size: 15px;">{{dt.unitId}}</td>
                <td style="font-size: 15px;">{{dt.unitName}}</td>
                <td style="font-size: 15px;">{{dt.brandId}}</td>
                <td style="font-size: 15px;">{{dt.brandName}}</td>
                <td style="font-size: 15px;">Time: {{dt.partnerStatus[0].offlineTime}}<br> Reason: {{dt.partnerStatus[0].reason}}</td>
                <td style="font-size: 15px;">Time: {{dt.partnerStatus[1].offlineTime}}<br> Reason: {{dt.partnerStatus[1].reason}}</td>
            </tr>
            <!--<td>{{resultTemp}}</td>-->
        </table>
</div>








        <div ng-if="showData != undefined && showData">
            <div class="row">
                <div class="col-sm-3">
                    <label class="cityLabel">Select Option</label>
                </div>
                <div class="col-sm-3">
                    <label class="cityLabel">Cafe List</label>
                </div>
            </div>

            <div class="row">
                <div class="col-sm-3">
                    <div data-ng-repeat="category in categories">
                        <div class="cardCity"
                             data-ng-class="{'selectedCard': selectedCategory == category}"
                             data-ng-click="getUnitCategoryWise(category)">
                            <div class="containerCard">
                                <h4>
                                    <b>{{category}}</b>
                                </h4>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-9">
                    <div class="row">
                        <div class="col-sm-4"
                             data-ng-repeat="unit in modifiedList|filter:categoryFilter | orderBy:'name'">
                            <div class="cardCity"
                                 data-ng-class="{'selectedCard': selectedUnit.id == unit.id}">
                                <div class="containerCard">
                                    <h4>
                                        <b>{{unit.name}}</b>
                                    </h4>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <!--<div class="row">
                <div class="makeSmall">
                    <table class="table table-striped table-bordered ">
                        <tbody>
                        <tr>
                            <td ng-repeat="cafes in regionalCafeList">
                                <table style="table-layout: fixed;width: 100%" class="table table-striped ">
                                    <tr ng-repeat="cafe in cafes">
                                        <td style="width: 50%; font-size: 11px;height: 50px;text-align: center">
                                            <span style="font-size: 10px"><b>{{cafe.region}}</b></span>
                                            <br><span
                                                data-ng-class="{'success':cafe.unitStatus=='ACTIVE','danger':cafe.unitStatus=='IN_ACTIVE'}"><b>{{cafe.name}}</b></span>
                                            <span data-ng-if="cafe.swiggyStatus"><img height="20px"
                                                                                      ng-src="images/swiggy.png"></span>
                                            <span data-ng-if="!cafe.swiggyStatus"><img height="20px"
                                                                                       ng-src="images/swiggyBlackAndWhite.png"></span>
                                            <span data-ng-if="cafe.zomatoStatus">
                                                <img height="10px" ng-src="images/zomato.png">
                                            </span>
                                            <span data-ng-if="!cafe.zomatoStatus">
                                                <img height="10px" ng-src="images/zomatoBlackAndWhite.png">
                                            </span>
                                        </td>
                                    </tr>
                                </table>
                            </td>

                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>-->

    </div>
</div>