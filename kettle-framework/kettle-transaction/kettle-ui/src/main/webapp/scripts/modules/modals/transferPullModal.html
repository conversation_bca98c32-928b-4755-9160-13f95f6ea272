<div class="modal-header" data-ng-init="init()">
    <h3 class="modal-title pull-left">Transfer Components</h3>
    <h2 class="modal-title pull-right" style="color: #FF9933">Rs {{settlementTotalAmount}}
        <span style="color: #e0e8e0">|</span>
        <span style="color: #138808">Rs {{denominationSum}}</span>
    </h2>
    <div class="clearfix"></div>
</div>
<div class="modal-body">
    <div class="row" style="margin-bottom:20px;">
        <div class="col-xs-12">
            <div data-ng-if="selectedPaymentMode.type == 'CASH'" class="alert alert-warning">Note: Please create Happay vouchers for <strong>Extra Amount</strong></div>
            <table class="table table-bordered table-striped" data-ng-if="selectedPaymentMode.type!='COUPON'
				&& editedDenominations.length>0">
                <tr>
                    <th>Denomination</th>
                    <th>Denomination value</th>
                    <th>Bundle Size</th>
                    <th>No. Of Packets</th>
                    <th>Loose Currency</th>
                    <th>Settlement Packets</th>
                    <th>Settlement Loose Currency</th>
                    <th>Total Amount</th>
                </tr>
                <tr data-ng-repeat="denom in editedDenominations | orderBy : 'denominationDetail.displayOrder'">
                    <td>{{denom.denominationDetail.denominationText}}</td>
                    <td>{{denom.denominationDetail.denominationValue}}</td>
                    <td>{{denom.denominationDetail.bundleSize}}</td>
                    <td>{{denom.packetCount}}</td>
                    <td>{{denom.looseCurrencyCount}}</td>
                    <td><input type="number" class="form-control" data-ng-change="calculateTotal(denom)" data-ng-model="denom.settlementPacketCount" /></td>
                    <td><input type="number" class="form-control" data-ng-change="calculateTotal(denom)" data-ng-model="denom.settlementLooseCurrencyCount" /></td>
                    <td>{{denom.totalAmount}}</td>
                </tr>
            </table>
            <div data-ng-show="gettingCouponDenoms" class="text-center"><img src="images/loader.gif" /></div>
            <table class="table table-bordered table-striped" data-ng-if="selectedPaymentMode.type=='COUPON'
				&& couponDenominations.length>0">
                <tr>
                    <th>Denomination</th>
                    <th>Denomination value</th>
                    <th>Bundle Size</th>
                    <th>No. Of Packets</th>
                    <th>Loose Currency</th>
                    <th>Total Amount</th>
                </tr>
                <tr data-ng-repeat="denom in couponDenominations | orderBy : 'denominationDetail.displayOrder'">
                    <td>{{denom.denominationDetail.denominationText}}</td>
                    <td>{{denom.denominationDetail.denominationValue}}</td>
                    <td>{{denom.denominationDetail.bundleSize}}</td>
                    <td>{{denom.packetCount}}</td>
                    <td>{{denom.looseCurrencyCount}}</td>
                    <td>{{denom.totalAmount}}</td>
                </tr>
            </table>
            <div class="row">
                <div class="col-xs-12">
                    <div class="tab-container">
                        <ul class="nav nav-tabs">
                            <li class="active"><a data-target="#bank_pickup" data-toggle="tab" data-ng-click="setSettlementProvider('BANK_PICKUP')" >Bank Pickup</a></li>
                            <li><a data-target="#manual_deposit" data-toggle="tab" data-ng-click="setSettlementProvider('MANUAL_DEPOSIT')">Manual Deposit</a></li>
                        </ul>
                        <div class="tab-content">
                            <div class="tab-pane active cont" id="bank_pickup">
                                <div class="row">
                                    <div class="col-xs-4">
                                        <label>Slip Number</label>
                                        <input type="text" class="form-control" data-ng-model="slipNumber" />
                                    </div>
                                    <div class="col-xs-4">
                                        <label>Ticket Number</label>
                                        <input type="text" class="form-control" data-ng-model="ticketNumber" />
                                    </div>
                                    <div class="col-xs-4">
                                        <label>Serial Number</label>
                                        <input type="text" class="form-control" data-ng-model="serialNumber" />
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-xs-4">
                                            <label>Select Date</label>
                                            <input type="date" name="settlementPullDate" min="{{minDate}}"  max="{{maxDate}}" class="form-control" data-ng-model="settlementDate" />
                                    </div>
                                    <div class="col-xs-8">
                                        <label>Select Upload Slip</label>
                                        <input type="file" accept="image/jpeg" class="form-control" file-model="settlementPullSlip" />
                                    </div>
                                </div>
                            </div>

                            <div class="tab-pane cont" id="manual_deposit">
                                <div class="row">
                                    <div class="col-xs-4">
                                        <label>Select Date</label>
                                        <input type="date" name="settlementPullDate" min="{{minDate}}"  max="{{maxDate}}"  class="form-control" data-ng-model="settlementDate" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row" data-ng-hide="showSettlementSlip">
                <div class="col-xs-12">
                    <p>Please verify the settlement amount given above.</p>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal-footer">
    <div class="btn-group col-xs-offset-9" style="margin-bottom: 10px">
        <button class="btn btn-danger" type="button" ng-click="cancel()" data-ng-hide="loading">Cancel</button>
        <div data-ng-show="loading"><img src="images/loader.gif" /></div>
        <button data-ng-if="showSettlementSlip" class="btn btn-default" type="button" data-ng-click="uploadSettlement()" data-ng-hide="loading">Upload & Submit</button>
        <button data-ng-if="!showSettlementSlip" class="btn btn-default" type="button" ng-click="uploadSettlement()" data-ng-hide="loading">Submit</button>
    </div>
</div>