<div class="modal-header" data-ng-init="init()">
    <h3 class="modal-title">Edit Subscription details</h3>
</div>
<div class="modal-body">
    <div class="row">
        <div class="col-xs-12">
            <div class="row" style="margin: 30px 0;">
                <div class="col-xs-4">
                    <div class="form-group">
                        <div class="col-xs-12">
                            <div class="btn-group">
                                <a href="javascript:;" class="btn" data-ng-click="getSubscriptionFrequency('WEEKLY')"
                                   data-ng-class="{'btn-success':subscriptionFrequencyType=='WEEKLY','btn-info':subscriptionFrequencyType!='WEEKLY'}">WEEKLY</a>
                                <a href="javascript:;" class="btn" data-ng-click="getSubscriptionFrequency('MONTHLY')"
                                   data-ng-class="{'btn-success':subscriptionFrequencyType=='MONTHLY','btn-info':subscriptionFrequencyType!='MONTHLY'}">MONTHLY</a>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xs-8">
                    <div class="row">
                        <div class="col-xs-6" data-ng-if="dateGT(selectedSubscription.subscriptionDetail.startDate)">
                            <div class="form-group">
                                <label class="control-label col-xs-5">Start date:</label>
                                <div class="col-xs-7">
                                    <div class="datepicker" data-date-format="yyyy-MM-dd" data-date-min-limit="{{subscriptionMinDate}}">
                                        <input type="text" data-ng-model="subscriptionStartDate" class="form-control"
                                               data-ng-change="setSubscriptionStartDate(subscriptionStartDate)" placeholder="yyyy-mm-dd" />
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xs-6">
                            <div class="form-group">
                                <label class="control-label col-xs-5">End date:</label>
                                <div class="col-xs-7">
                                    <div class="datepicker" data-date-format="yyyy-MM-dd" data-date-min-limit="{{subscriptionMinDate}}">
                                        <input type="text" data-ng-model="subscriptionEndDate" class="form-control"
                                               data-ng-change="setSubscriptionEndDate(subscriptionEndDate)" placeholder="yyyy-mm-dd" />
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row" style="margin:20px 0 30px 0;">
                <div class="col-xs-4">
                    <div class="checkbox">
                        <label>
                            <input type="checkbox" data-ng-model="emailNotification" data-ng-change="notifyEmail(emailNotification)"> Email Notification
                        </label>
                    </div>
                </div>
                <div class="col-xs-4">
                    <div class="checkbox">
                        <label>
                            <input type="checkbox" data-ng-model="smsNotification" data-ng-change="notifySMS(smsNotification)"> SMS Notification
                        </label>
                    </div>
                </div>
                <div class="col-xs-4">
                    <div class="checkbox">
                        <label>
                            <input type="checkbox" data-ng-model="automatedDelivery" data-ng-change="automateDelivery(automatedDelivery)"> Automated Delivery
                        </label>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="control-label col-xs-4">Select day(s):</label>
                        <div class="col-xs-6">
                            <select class="form-control" data-ng-model="frequencyDay"
                                    data-ng-options="frequencyDay as frequencyDay.value for frequencyDay in frequencyDays track by frequencyDay.id"></select>
                        </div>
                        <div class="col-xs-2">
                            <button class="btn btn-success" data-ng-click="addFrequencyDay(frequencyDay)"><i class="fa fa-plus-circle"></i></button>
                        </div>
                    </div>
                    <div class="form-group" data-ng-if="subscriptionFrequencyType=='WEEKLY'">
                        <div class="col-sm-offset-2 col-sm-10">
                            <div class="checkbox">
                                <label>
                                    <input type="checkbox" data-ng-model="allDays" data-ng-change="addAllDays(allDays)"> Add all
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-xs-12 removableTagContainer" data-ng-if="subscriptionFrequencyType=='WEEKLY'">
                            <div class="removableTag" data-ng-repeat="subscriptionDay in subscriptionWeekDays track by subscriptionDay.id"
                                 data-ng-click="removeSubscriptionDay(subscriptionDay)">
                                {{subscriptionDay.value}}
                                <span><i class="fa fa-close"></i></span>
                            </div>
                        </div>
                        <div class="col-xs-12 removableTagContainer" data-ng-if="subscriptionFrequencyType=='MONTHLY'">
                            <div class="removableTag" data-ng-repeat="subscriptionDay in subscriptionMonthDays track by subscriptionDay.id"
                                 data-ng-click="removeSubscriptionDay(subscriptionDay)">
                                {{subscriptionDay.value}}
                                <span><i class="fa fa-close"></i></span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="control-label col-xs-4">Select time(s):</label>
                        <div class="col-xs-6">
                            <select data-ng-model="frequencyTime" class="form-control"
                                    data-ng-options="frequencyTime as frequencyTime.value for frequencyTime in frequencyTimes track by frequencyTime.id"></select>
                        </div>
                        <div class="col-xs-2">
                            <button class="btn btn-success" data-ng-click="addFrequencyTime(frequencyTime)"><i class="fa fa-plus-circle"></i></button>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-xs-12 removableTagContainer">
                            <div class="removableTag" data-ng-repeat="subscriptionTime in subscriptionTimes track by subscriptionTime.id"
                                 data-ng-click="removeSubscriptionTime(subscriptionTime)">
                                {{subscriptionTime.value}}
                                <span><i class="fa fa-close"></i></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>


</div>
<div class="modal-footer">
    <div class="btn-group col-xs-offset-10" style="margin-bottom: 10px">
        <button class="btn btn-danger" type="button" ng-click="cancel()">Cancel</button>
        <button class="btn btn-default" type="button" ng-click="ok()">OK</button>
    </div>
</div>