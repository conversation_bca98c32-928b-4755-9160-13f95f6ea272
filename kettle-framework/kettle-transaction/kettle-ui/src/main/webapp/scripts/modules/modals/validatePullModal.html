<div class="modal-header" data-ng-init="init()">
    <h3 class="modal-title pull-left">Validate Pull Packet</h3>
    <h2 class="modal-title pull-right" style="color: #FF9933">Rs {{selectedPull.pullAmount}}
        <span style="color: #e0e8e0">|</span>
        <span style="color: #138808">Rs {{denominationSum}}</span>
    </h2>
    <div class="col-xs-12" style="font-weight:700;padding-left:0px;">{{selectedPull.paymentMode.name}} (Verify from: {{selectedPull.paymentMode.validationSource}})</div>
    <div class="clearfix"></div>
</div>
<div class="modal-body">
    <div class="row" style="marin-bottom:20px;">
        <div class="col-xs-12">
            <table class="table table-bordered table-striped" data-ng-if="selectedPull.paymentMode.type!='COUPON'
				&& selectedPull.pullDenominations.length>0">
                <tr>
                    <th>Denomination</th>
                    <th>Denomination value</th>
                    <th>Bundle Size</th>
                    <th>No. Of Packets</th>
                    <th>Loose Currency</th>
                    <th>Total Amount</th>
                </tr>
                <tr data-ng-repeat="denom in selectedPull.pullDenominations | orderBy : 'denominationDetail.displayOrder'">
                    <td>{{denom.denominationDetail.denominationText}}</td>
                    <td>{{denom.denominationDetail.denominationValue}}</td>
                    <td>{{denom.denominationDetail.bundleSize}}</td>
                    <td><input type="number" class="form-control" data-ng-change="updateTotal(denom)" data-ng-model="denom.packetCount" /></td>
                    <td><input type="number" class="form-control" data-ng-change="updateTotal(denom)" data-ng-model="denom.looseCurrencyCount" /></td>
                    <td>{{denom.totalAmount}}</td>
                </tr>
            </table>
            <div data-ng-show="gettingCouponDenoms" class="text-center"><img src="images/loader.gif" /></div>
            <table class="table table-bordered table-striped" data-ng-if="selectedPull.paymentMode.type=='COUPON'
				&& couponDenominations.length>0">
                <tr>
                    <th>Denomination</th>
                    <th>Denomination value</th>
                    <th>Bundle Size</th>
                    <th>No. Of Packets</th>
                    <th>Loose Currency</th>
                    <th>Total Amount</th>
                </tr>
                <tr data-ng-repeat="denom in couponDenominations | orderBy : 'denominationDetail.displayOrder'">
                    <td>{{denom.denominationDetail.denominationText}}</td>
                    <td>{{denom.denominationDetail.denominationValue}}</td>
                    <td>{{denom.denominationDetail.bundleSize}}</td>
                    <td>{{denom.packetCount}}</td>
                    <td>{{denom.looseCurrencyCount}}</td>
                    <td>{{denom.totalAmount}}</td>
                </tr>
            </table>
            <div class="row">
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="control-label">Created By</label>
                        <select class="form-control" data-ng-model="createdByUser" data-ng-change="changeCreator()"
                                data-ng-options="user as user.name for user in usersForUnit track by user.id"></select>
                    </div>
                </div>
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="control-label">Witnessed By</label>
                        <input type="text" class="form-control" data-ng-model="witnessedByUser" />
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-6">
                    <div class="form-group">
                        <label class="control-label">Password</label>
                        <input type="password" class="form-control" data-ng-model="creatorPassword" />
                    </div>
                </div>
                <div class="col-xs-2">
                    <div class="form-group">
                        <label class="control-label"> &nbsp; </label>
                        <input type="button" class="btn btn-primary form-control" data-ng-click="verifyCreator()" value="Verify" />
                    </div>
                </div>
                <div class="col-xs-2">
                    <div class="form-group" style="margin-top:32px;">
                        <div class="pull-right" style="color:green;" data-ng-if="!verifyingCreator && creatorVerified">Verified</div>
                        <div class="pull-right" style="color:red;" data-ng-if="!verifyingCreator && !creatorVerified">Unverified</div>
                        <div data-ng-if="verifyingCreator" class="text-center"><img src="images/loader.gif" /></div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-xs-12">
                    <div class="form-group">
                        <label class="control-label">Comment</label>
                        <textarea rows="5" class="form-control" data-ng-model="pullComment"></textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="modal-footer">
    <div class="btn-group col-xs-offset-10" style="margin-bottom: 10px">
        <button class="btn btn-danger" type="button" data-ng-click="cancel()" data-ng-hide="loading">Cancel</button>
        <div data-ng-show="loading"><img src="images/loader.gif" /></div>
        <button class="btn btn-default" type="button" data-ng-click="ok()" data-ng-hide="loading">Submit</button>
    </div>

</div>