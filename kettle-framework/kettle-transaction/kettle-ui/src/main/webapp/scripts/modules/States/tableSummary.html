<div class="row coverFlash" flash-message="5000"></div>
<div class="container-fluid" data-ng-init="init()">

	<!-- Table Selection View -->

	<div class="row">
		<div class="col-xs-12">
			<button type="button" class="btn btn-warning btn-lg-text-auto "
				data-ng-click="backToCover()">Back to Cover</button>
			<button type="button" class="btn btn-warning btn-lg-text-auto "
				data-ng-click="checkTables()">Check Tables</button>
			<button type="button" class="btn btn-success btn-lg-text-auto "
				style ="background-color: #577c39; color: #efefef;"
				data-ng-disabled="tableSummary == null || tableSummary.tableRequestId == 0"
				data-ng-click="orderStart()">Order Start</button>
			<button type="button" class="btn btn-danger btn-lg-text-auto "
				data-ng-disabled="tableSummary == null || tableSummary.orders.length == 0"
				data-ng-click="generateInvoice()">Settle Payment</button>
			<button type="button" class="btn btn-danger btn-lg-text-auto "
				data-ng-disabled="tableSummary == null || tableSummary.orders.length == 0"
				data-ng-click="openChangeTableModal()">Change table</button>
		</div>
	</div>
	<div class="row">
		<div class="col-xs-12 text-center">
			<h3>Table Number - {{currentTable.tableNumber}}</h3>
		</div>
	</div>
	
	<div class="row" data-ng-if="loadingData">
		<div class="col-xs-12 text-center">
			<h3>Loading Tables, Please wait.</h3>
		</div>
	</div>
 	
 	<!-- Customer and Table Summary View -->
	<div class="row"
		style="border-style: dashed; margin: 1em; padding: 1em;">
		<div class="col-xs-6 text-left">{{tableSummary.customerName !=
			null ? tableSummary.customerName : ''}}</div>
		<div class="col-xs-6 text-right">
			<b>Total Amount:</b> {{tableSummary.totalAmount}}
		</div>
	</div>

	<!-- Customer and Table Summary View -->
	<div class="row orderLineItem"
		data-ng-repeat="order in tableSummary.orders">
		<div class="col-xs-4">
			<a class="btn btn-default" style = "display: inline;" data-ng-click="openOrderSearch(order.generateOrderId)">{{order.generateOrderId}}</a></div>
		<div class="col-xs-3">{{order.customerName}}</div>
		<div class="col-xs-3" data-ng-if="order.status!='SETTLED'">{{order.status}}</div>
		<div class="col-xs-3" data-ng-if="order.status=='SETTLED'">PAYMENT PENDING</div>
		<div class="col-xs-2">Amount:
			{{order.transactionDetail.paidAmount}}</div>
	</div>
</div>
