<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<div data-flash-message="5000"></div>
<div class="container-fluid">
    <div class="row">
        <div class="col-xs-6">
            <h3 data-ng-if="orderType=='subscription'" class="pageHeader">Subscription
                Details</h3>
            <h3 data-ng-if="orderType!='subscription'" class="pageHeader">Order
                Details</h3>
        </div>
        <div class="col-xs-6">
            <h3 data-ng-if="orderSourceName !=''"
                style="color: green; font-weight: 900;float: left;" class="pageHeader">{{orderSourceName}}
                Order <span
                        data-ng-if="OrderObj.sourceId!=null && OrderObj.channelPartner !=2 ">({{OrderObj.sourceId}})</span>
            </h3>
        </div>
    </div>
    <div class="row" data-ng-if="!isCOD">
        <div class="col-xs-3">
            <button type="button" class="btn btn-warning"
                    data-ng-click="backToCover()">back
            </button>
        </div>
        <div class="col-xs-9">
            <input type="text" class="inputOrderNumber"
                   placeholder="Search by Order Id" data-ng-model="$parent.searchText">
            <button type="submit" class="btn btn-default"
                    data-ng-click="GetSearchedOrder()">
                <span class="glyphicon glyphicon-search"></span>
            </button>
        </div>
        <div class="col-xs-9">
            <label
                    style="float: left;font-size: 19px;margin-left: 50px;margin-right: 20px;">Search Gst
            </label>
            <button type="submit" class="btn btn-default"
                    data-ng-click="GetSearchedGst()">
                <span class="glyphicon glyphicon-search"></span>
            </button>
        </div>
        <div class="col-lg-2">
            <button data-ng-show="isGstClicked" class="btn btn-primary pull-center" data-toggle="modal"
                    data-target="#updateInvoice" ng-click="addInvoiceDialog()">
                <i class="fa fa-plus fw"></i> Add Invoice for Gst
            </button>
        </div>


        <div class="modal fade" role="dialog"  id="updateInvoice"
             data-keyboard="false" data-backdrop="static"
             tabindex="-1">
            <div class="modal-dialog" role="document">
                <div class="modal-content">

                    <div class="modal-header">
                        <button aria-label="Close" class="close" data-dismiss="modal" ng-click="reset()"
                                type="button"><span
                                aria-hidden="true">&times;</span></button>
                        <div ng-show="isAddNewOffer">
                        <h4 class="modal-title">Update Invoice Details</h4>
                        </div>
                    </div>

                    <div class="modal-body">
                        <form name="addInvoiceDetails" novalidate>
                            <fieldset ng-disabled="isViewOffer">
                                <div class="form-group">
                                    <label>GST</label>
                                    <div class="form-group">
                                        <input type="text" class="inputGst form-control" placeholder="Enter Gst" data-ng-model="invoiceDetail.gst" pattern="^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$">
                                    </div>
                                </div>
                                <div ng-show="invoiceDetail.gst">
                                <input name="options" ng-control="options" type="radio" ng-value="true"  ng-model="options" >GST Number Active<br/>

                                <input name="options" ng-control="options" type="radio" ng-value="false" ng-model="options" >GST Number Inactive<br/>

                                </div>
                                <div ng-show="options">
                                    <div class="form-group">
                                        <label>Company Name</label>
                                        <div class="form-group">
                                            <input type="text" class="inputCustomerName form-control"
                                                   placeholder="Enter Customer Name"
                                                   data-ng-model="invoiceDetail.companyName">
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label>Company Address</label>
                                        <div class="form-group">
                                            <input type="text" class="inputCustomerAddress form-control"
                                                   placeholder="Enter Customer Address"
                                                   data-ng-model="invoiceDetail.companyAddress">
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label>Generated Order Id</label>
                                        <div class="form-group">
                                            <input type="text" class="inputOrderId form-control" ng-readonly="true"
                                                   placeholder="Enter Generated Order Id" data-ng-model="invoiceDetail.orderId">
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label>Generated By</label>
                                        <div class="form-group">
                                            <input type="text" class="inputGeneratedBy form-control" ng-readonly="true"
                                                   placeholder="Enter Generating person's name"
                                                   data-ng-model="invoiceDetail.generatedBy">
                                        </div>
                                    </div>
                                </div>



                                <div ng-show="addInvoiceDetails">
                                    <div class="form-group clearfix">
                                        <button class="btn btn-primary pull-right"
                                                ng-click="addCrmScreenDetail(invoiceDetail)"
                                                data-dismiss="modal">
                                            Update Invoice
                                        </button>
                                    </div>
                                </div>
                                </fieldset>
                        </form>
                    </div>
                </div>
            </div>
        </div>




    </div>

    <div class="row" data-ng-if="isCOD">
        <div class="col-xs-2">
            <button type="button" class="btn btn-warning"
                    data-ng-click="backToCover()">back
            </button>
        </div>
        <div class="col-xs-5">
            <input type="text" class="inputOrderNumber"
                   placeholder="Search by Order Id" data-ng-model="$parent.searchText">
            <button type="submit" class="btn btn-default"
                    data-ng-click="GetSearchedOrder()">
                <span class="glyphicon glyphicon-search"></span>
            </button>
        </div>
        <div class="col-xs-5">
            <input type="text" class="inputOrderNumber"
                   placeholder="Search by SWIGGY Order Id" data-ng-model="$parent.externalOrderId"
                   style="width: 240px;">
            <button type="submit" class="btn btn-default"
                    data-ng-click="GetPartnerSearchedOrder()">
                <span class="glyphicon glyphicon-search"></span>
            </button>
        </div>
    </div>
    <div data-ng-if="!isEmptyObject(OrderObj) && OrderObj.orderType == 'order' && OrderObj.status != 'CANCELLED'"
         class="row">
        <div class="col-xs-2">
            <div class="btn-group"
                 data-ng-if="showCancel && orderType!='subscription'">
                <!--data-ng-if="!isCOD"-->
                <button type="button" class="btn btn-primary"
                        data-ng-click="reprint()" style="margin-top: 30px">Reprint
                </button>
                <button type="button" class="btn btn-default"
                        data-ng-disabled="dataLoading" data-ng-click="resendEmail()"
                        style="margin-top: 30px">Resend Email
                </button>
                <button type="button" data-ng-if="!isCOD" class="btn btn-primary"
                        data-ng-click="reprint('KOT')" style="margin-top: 30px">Reprint KOT
                </button>
                <button type="button" data-ng-if="!isCOD && OrderObj.tableRequestId != null" class="btn btn-primary"
                        data-ng-click="reprintSettlementSlip()" style="margin-top: 30px">Reprint Settlement Slip
                </button>
            </div>
        </div>
        <div class="col-xs-2" data-ng-if="orderType!='subscription'">
            <div class="btn-group">
                <button data-ng-if="orderType!='subscription'"
                        type="button" class="btn btn-danger" style="margin-top: 30px"
                        data-ng-click="removeFromAssembly(OrderObj)">Remove from assembly
                </button>
            </div>
        </div>
        <div class="col-xs-2" data-ng-if="showCancel && orderType!='subscription' && OrderObj.billBookNo==null && !chaayosCashOrder">
            <div class="btn-group">
                <button data-ng-if="showCancel && orderType!='subscription'"
                        type="button" class="btn btn-danger" style="margin-top: 30px"
                        data-ng-click="cancelOrderModalOpen(false)">Cancel This Order
                </button>
            </div>
        </div>
        <div class="col-xs-2" data-ng-if="currentUser.userId == 120418 && orderType!='subscription' && OrderObj.billBookNo==null && !chaayosCashOrder">
            <div class="btn-group">
                <button data-ng-if="orderType!='subscription'"
                        type="button" class="btn btn-danger" style="margin-top: 30px"
                        data-ng-click="cancelOrderModalOpen(true)">Force Cancel This Order
                </button>
            </div>
        </div>
        <div class="col-xs-2">
            <div class="btn-group"
                 data-ng-if="showUnsatisfiedCustomer && orderType!='subscription' && !isGiftCardOrder">
                <button
                        type="button" class="btn btn-danger" style="margin-top: 30px"
                        data-ng-click="unsatisfiedCustomerOrder()">Unsatisfied Customer
                </button>
            </div>
        </div>
        <div class="col-xs-2">
            <div class="btn-group"
                 data-ng-if="OrderObj.generateOrderId!=null && OrderObj.unitName!=null">
                <button
                        type="button" class="btn btn-danger" style="margin-top: 30px"
                        data-ng-click="addDeliveryDetails()">Add Delivery details
                </button>
            </div>
        </div>
        <div class="col-xs-2">
            <button type="button" class="btn btn-danger" data-ng-if="OrderObj.channelPartner==6"
                    style="margin-top: 30px"
                    data-ng-click="getDeliveryTime(OrderObj.orderId)">Delivery Time
            </button>
            <button type="button" class="btn btn-danger"
                    data-ng-if="OrderObj.channelPartner != null && OrderObj.deliveryPartner == 5"
                    style="margin-top: 30px"
                    data-ng-click="getDeliveryStatusData(OrderObj.orderId)">Rider Data
            </button>
        </div>
    </div>
    <div data-ng-if="!isEmptyObject(OrderObj) && OrderObj.orderType != 'order'" class="row">
        <div class="col-xs-3">
            <h3 style="color:red;">{{OrderObj.orderType}}</h3>
        </div>
    </div>
</div>
<div class="container-fluid" data-ng-if="!isEmptyObject(OrderObj)">
    <div class="row">

        <table style="margin-top: 30px; border-style: groove;" class="table orderSearchText">
            <tr>
                <td>Order Id</td>
                <td>Unit Name</td>
                <td>Brand Name</td>
            </tr>
            <tr>
                <td>{{OrderObj.generateOrderId}}</td>
                <td>{{OrderObj.unitName}}[{{OrderObj.unitId}}]</td>
                <td class="brandName {{OrderObj.brand.brandCode}}">{{OrderObj.brand.brandName}}</td>
            </tr>
        </table>

        <table style="margin-top: 30px; border-style: groove;"
               class="table orderSearchText">
            <tr>
                <th>Bill Book Number</th>
                <th>Generated By</th>
                <th>Bill Status</th>
                <th>Creation Time</th>
                <th>Settlements</th>
                <th>Cancellation Generated By</th>
                <th>Cancellation Approved By</th>
                <th>Cancellation Reason</th>
            </tr>
            <tr>
                <td>{{OrderObj.billBookNo}}</td>
                <td>{{OrderObj.employeeId}}</td>
                <td>{{OrderObj.status}}</td>
                <td>{{OrderObj.billCreationTime | date:'yyyy-MM-dd HH:mm:ss'}}</td>
                <td>{{settlement}}</td>
                <td>{{OrderObj.cancellationDetails.generatedBy}}</td>
                <td>{{OrderObj.cancellationDetails.approvedBy}}</td>
                <td>{{OrderObj.cancellationDetails.reason}}</td>
            </tr>
        </table>
        <hr>
    </div>
    <div class="row">
        <table style="margin-top: 30px; border-style: groove;"
               class="table orderSearchText">
            <tr>
                <th>Settlement Id</th>
                <th>Payment Mode</th>
                <th>Amount</th>
                <th>Edited</th>
                <th>Edited By</th>
                <th>Action</th>
            </tr>
            <tr data-ng-repeat="settlement in OrderObj.settlements">
                <td>{{settlement.settlementId}}</td>
                <td>{{settlement.modeDetail.name}}</td>
                <td>{{settlement.amount}}</td>
                <td>{{settlement.edited | yesNo}}</td>
                <td>{{settlement.editedBy}}</td>
                <td>
                    <button data-ng-if="OrderObj.orderType == 'order' && OrderObj.status == 'SETTLED' && showEditSettlement && settlement.modeDetail.editable && (settlement.edited == null || !settlement.edited) && !OrderObj.giftCardOrder && ((settlement.modeDetail.id != 17 && settlement.modeDetail.id != 30 && settlement.modeDetail.id !=29 )) && (settlement.modeDetail.type=='CARD' ||  settlement.modeDetail.type =='CASH' || settlement.modeDetail.type =='AMEX Card')"
                            type="button" class="btn btn-primary"
                            data-ng-click="editSettlement(settlement)" style="margin: 5px">Edit
                    </button>
                </td>
            </tr>
        </table>
        <hr>
    </div>

    <div class="row" data-ng-if="orderPaymentObject.length > 0">
        <table style="margin-top: 30px; border-style: groove;"
               class="table orderSearchText">
            <tr>
                <th>Payment Status</th>
                <th>Refund Status</th>
            </tr>
            <tr data-ng-repeat="settlement in orderPaymentObject">
                <td>{{settlement.paymentStatus}}</td>
                <td data-ng-if="settlement.refundStatus">{{settlement.refundStatus}}</td>
                <td data-ng-if="!settlement.refundStatus">NA</td>
            </tr>
        </table>
        <hr>
    </div>

    <div class="row">
        <table class="table table-striped"
               style="margin-top: 30px; border-style: double;"
               data-ng-if="!isEmptyObject(OrderObj)">
            <thead>
            <tr>
                <th>Product Name</th>
                <th>Item Variants</th>
                <th>Product Variants</th>
                <th>Add-on</th>
                <th>Quantity</th>
                <th>Dimension</th>
                <th>Amount</th>
            </tr>
            </thead>
            <tbody>
            <tr data-ng-repeat="orderItem in getOrderItems()">
                <td><span data-ng-if="orderItem.isComboItem">**(C) </span>{{orderItem.productName}}</td>
                <td><span
                        data-ng-repeat="addon in orderItem.composition.variants"
                        data-ng-if="!$last">{{addon.alias}}, </span><span
                        data-ng-repeat="addon in orderItem.composition.variants"
                        data-ng-if="$last">{{addon.alias}}</span></td>
                <td><span
                        data-ng-repeat="addon in orderItem.composition.products"
                        data-ng-if="!$last">{{addon.product.name}}, </span><span
                        data-ng-repeat="addon in orderItem.composition.products"
                        data-ng-if="$last">{{addon.product.name}}</span></td>
                <td><span
                        data-ng-repeat="addon in orderItem.composition.addons"
                        data-ng-if="!$last">{{addon.product.name}}, </span><span
                        data-ng-repeat="addon in orderItem.composition.addons"
                        data-ng-if="$last">{{addon.product.name}}</span></td>
                <td>{{orderItem.quantity}}</td>
                <td>{{orderItem.dimension}}</td>
                <td>{{orderItem.amount}}</td>
            </tr>
            </tbody>
        </table>
    </div>
    <div class="row">
        <div class="col-xs-9">
            <div class="row customerInfoPanel"
                 data-ng-if="!isEmptyObject(OrderObj.orderRemark) || !isEmptyObject(CustomerObj)">
                <div class="alert alert-warning"
                     data-ng-if="!isEmptyObject(OrderObj.orderRemark)">
                    <strong>Order Remark:</strong> {{OrderObj.orderRemark}}
                </div>
                <div class="col-sm-6" data-ng-if="!isEmptyObject(CustomerObj)">
                    <div data-ng-show="showLoading">Loading...</div>
                    <div><strong>Customer:</strong></div>
                    <strong>{{CustomerObj.firstName}}</strong><br>
                    {{CustomerObj.emailId}}<br>
                    {{CustomerObj.countryCode}}-{{"XXXXXX"+CustomerObj.contactNumber.slice(-4)}}<br>
                    <br> <strong>Address:</strong><br>
                    <span data-ng-bind-html="getAddress()">
					</span>
                    <!-- <p data-ng-if="deliveryAddress.addressType=='Office'">(OFFICE) {{deliveryAddress.company}}</p>
                    {{deliveryAddress.line1}}<br>
                    <p data-ng-if="deliveryAddress.line2">{{deliveryAddress.line2}}</p>
                    <p data-ng-if="deliveryAddress.line3">{{deliveryAddress.line3}}</p>
                    {{deliveryAddress.city}}, {{deliveryAddress.state}},
                    {{deliveryAddress.country}}<br> -->
                    <p data-ng-if="deliveryAddress.line3">
                        <i>{{deliveryAddress.zipCode}}</i>
                    </p>
                </div>

                <div class="col-sm-6" data-ng-if="!isEmptyObject(deliveryObject)">
                    <strong>DELIVERY DETAILS</strong><br> <strong>Partner:</strong>
                    {{deliveryObject.deliveryPartnerName}}
                    [{{deliveryObject.deliveryStatus}}]<br>
                    <strong data-ng-if="!orderObj.externalOrderId">External Order Id:</strong>
                    {{orderObj.externalOrderId}}<br> <br>
                    <div data-ng-if="deliveryObject.deliveryBoyName.length>0">
                        <strong>SDP DETAILS</strong><br>
                        {{deliveryObject.deliveryBoyName}}<br>
                        {{deliveryObject.deliveryBoyPhoneNum}}<br>
                    </div>

                    <button
                            data-ng-if="isCOD && OrderObj.status != 'SETTLED' && OrderObj.status != 'CANCELLED' && orderType!='subscription' && OrderObj.deliveryPartner != 8"
                            type="button" class="btn btn-warning center-block"
                            style="margin-top: 25px"
                            data-ng-click="cancelDelivery(deliveryObject.generatedOrderId)">Cancel
                        Delivery
                    </button>

                </div>
            </div>
            <div class="row customerInfoPanel"
                 data-ng-if="orderType=='subscription'">
                <p>
                    <strong>Subscription Details:</strong>
                </p>
                <div class="col-xs-12">
                    <p>
                        Subscription Id: <span class="pull-right">{{OrderObj.generateOrderId}}</span>
                    </p>
                    <p>
                        Subscription Status: <span
                            class="pull-right">{{OrderObj.subscriptionDetail.subscriptionStatus}}</span>
                    </p>
                    <p>
                        Subscription Start Date: <span class="pull-right">{{OrderObj.subscriptionDetail.startDate
							| date:'yyyy-MM-dd'}}</span>
                    </p>
                    <p>
                        Subscription End Date: <span class="pull-right">{{getPreviousDate(OrderObj.subscriptionDetail.endDate)
							| date:'yyyy-MM-dd'}}</span>
                    </p>
                    <p>
                        Subscription Type: <span class="pull-right">{{OrderObj.subscriptionDetail.type}}</span>
                    </p>
                    <p data-ng-if="OrderObj.subscriptionDetail.type=='MONTHLY'">
                        Subscription Interval: <span class="pull-right">{{joinArray(OrderObj.subscriptionDetail.daysOfTheMonth)}}</span>
                    </p>
                    <p data-ng-if="OrderObj.subscriptionDetail.type=='WEEKLY'">
                        Subscription Interval: <span class="pull-right">{{convertWeekDayFromIndex(OrderObj.subscriptionDetail.daysOfTheWeek)}}</span>
                    </p>
                    <p>
                        Subscription Time: <span class="pull-right">{{convertTimeFromIndex(OrderObj.subscriptionDetail.timeOfTheDay)}}</span>
                    </p>
                </div>
            </div>
        </div>

        <div class="col-xs-3" data-ng-if="!isEmptyObject(OrderObj)">
            <table class="table table-striped"
                   style="margin-top: 30px; border-style: double;">
                <tbody>
                <tr>
                    <td>Sub Total</td>
                    <td>{{OrderObj.transactionDetail.totalAmount}}</td>

                </tr>
                <tr>
                    <td>Discount</td>
                    <td>{{OrderObj.transactionDetail.discountDetail.totalDiscount}}</td>

                </tr>
                <tr>
                    <td>Taxable Amount</td>
                    <td>{{OrderObj.transactionDetail.taxableAmount}}</td>

                </tr>
                <tr data-ng-repeat="taxItem in OrderObj.transactionDetail.taxes track by $index">
                    <td>{{taxItem.code}} @ {{taxItem.percentage}} %</td>
                    <td>{{taxItem.value.toFixed(2)}}</td>
                </tr>
                <tr>
                    <td>Total Tax</td>
                    <td>{{OrderObj.transactionDetail.tax.toFixed(2)}}</td>

                </tr>
                <tr>
                    <td>Paid Amount</td>
                    <td>{{OrderObj.transactionDetail.paidAmount}}</td>

                </tr>
                <tr data-ng-if="OrderObj.transactionDetail.savings!=null">
                    <td>Net Savings</td>
                    <td>{{OrderObj.transactionDetail.savings}}</td>

                </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>



