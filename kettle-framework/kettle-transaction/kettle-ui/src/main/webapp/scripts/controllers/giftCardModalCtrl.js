/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';
/*jshint sub:true*/

angular.module('posApp').controller('GiftCardCtrl',
	['$scope', '$http','APIJson', '$modalInstance','AppUtil','$rootScope','posAPI','productService','giftObject',
	function ($scope,$http,APIJson, $modalInstance,AppUtil,$rootScope,posAPI,productService,giftObject) {
	$scope.cardType = giftObject.type;
	var giftCardId=[1000007,1026,1027,1048,1000159];
	var giftCardIdAdvance=[3];
	var giftCardNumberList=[];
	$scope.giftCardsList = [];
	$scope.giftCardsListAdv=[];
	$scope.notEditable={};
	$scope.cardNumber={};
	var gyftrDetail = null;
	var card={};
	$scope.cardList = [{"name" : "E-Card", "code" : "ECARD"},{"name" : "Gyftr", "code" : "GYFTR"},{"name" : "Additional Payment Option", "code" : "AP01"}];
	$scope.voucherList = [0];
	$scope.vouherNumbers = {};
	$scope.vouherNumbers[1] = "";

	$scope.init = function(){
		$scope.backupCurrentState();
		$scope.giftCardOfferInfo = AppUtil.giftCardOffers; // to get offer which are on cover page
		$scope.giftCards = {};
		$scope.giftCardOfferInfo.forEach(function (data) {
			$scope.giftCards[data.denomination] = (data.denomination * data.offer) / 100;
		});
		//console.log($scope.giftCards);
		getGiftCards();
	};

	$scope.getVoucherList = function() {
		return $scope.voucherList;
	};

	$scope.updateVoucher = function(action){
		if(action == 'add'){
			$scope.voucherList.push($scope.voucherList.length);
		}else if(action == 'remove'){
			if($scope.voucherList.length == 1){
				AppUtil.myAlert("One vouhcer required.");
				return false;
			}
			$scope.voucherList.splice($scope.voucherList.length-1, 1);
		}
	};

	$scope.verifyVoucher = function() {
		if(gyftrDetail.recipeId == 0){
			AppUtil.myAlert("Please update gyftr voucher recipe to calculate consumption.");
			return false;
		}
		for (var i in $scope.vouherNumbers) {
			if($scope.vouherNumbers[i].length < 6){
				AppUtil.myAlert("Please Enter Valid Vouhcer No!");
				return false;
			}
		}
		var  result = false;
		for (var i in $scope.vouherNumbers) {
			result = $scope.addGyftrVoucherInOrder(gyftrDetail.linkedProduct, $scope.vouherNumbers[i].toUpperCase());
		}
		if(result){
			$scope.checkOrder();
			$scope.$parent.disableOrderCheckBtnGift =true;
		}
	};
        $scope.came=false;
        function getGiftCards(){
		if($scope.cardType == 'ECARD'){
			AppUtil.setCardType('ECARD');
		}
		else if($scope.cardType == 'AP01') {
            AppUtil.setCardType('AP01');
        }else {
			AppUtil.setCardType('SELF');
		}


            var list = productService.getProductArrayForSubTypeCode(904);
            for (var i = 0; i < list.length; i++) {
                if (giftCardId.indexOf(list[i].id) > -1) {
                    if ($scope.giftCardsList.indexOf(list[i])==-1) {
                        $scope.giftCardsList.push(list[i]);
                    }
                }
            }

            var listAdv = productService.getProductArrayForSubTypeCode(3822);
            console.log(listAdv);
            for (var i = 0; i < listAdv.length; i++) {
                if (giftCardIdAdvance.indexOf(listAdv[i].id) > -1) {
                     if ($scope.giftCardsListAdv.indexOf(listAdv[i])==-1) {
                        $scope.giftCardsListAdv.push(listAdv[i]);
                    }
                }
            }


	};

	$scope.changeCardType = function(cardType) {
		if(cardType == 'GYFTR'){
			getGyftrPartnerDetail($scope.cardType);
		}
		$scope.cardType = cardType;
		AppUtil.setCardType(cardType);
        $scope.orderItemArray = productService.clearOrderArray();
	};

	function getGyftrPartnerDetail(cardType) {
		posAPI.allUrl('/',AppUtil.restUrls.order.getExternalPartnerDetail).post("GYFTR").then(function(response) {
			if(response.errorType != undefined && response.errorType != ''){
				AppUtil.myAlert("Gyftr Partner Detail Not Available.");
				$scope.changeCardType(cardType);
			}else{
				gyftrDetail = response.plain();
				if(gyftrDetail.recipeId == 0){
					AppUtil.myAlert("Please update gyftr voucher recipe to calculate consumption.");
					return false;
				}
				gyftrDetail.linkedProduct.prices.push({"dimension":"None","price":0,"recipe":null,"recipeId":gyftrDetail.recipeId,"customize":false});
			//	console.log("gyftrDetail.linkedProduct",gyftrDetail);
			}
		}, function(err) {
			AppUtil.myAlert(err.data.errorMessage);
		});
	}



	$scope.purchaseGiftCard=function(){
		if(AppUtil.cardType=='GYFTR'){
			for(var i=0;i<$scope.orderItemArray.length;i++){
				if(!$scope.notEditable[$scope.orderItemArray[i].orderDetails.itemId]){
					AppUtil.myAlert("Please enter valid card number(s).");
					return false;
				}
			}
		}
		$scope.$parent.disableOrderCheckBtnGift =true;
		$scope.checkOrder ();
	};

	$scope.close = function(confirmRequired) {
		$scope.clearOnGiftModalClose(closeModal,false);
		AppUtil.setCardType('GIFT');
	};
	function closeModal(){
		$modalInstance.close('cancel');
		$scope.$parent.disableOrderCheckBtnGift =false;
	}

	$rootScope.closeGiftModal = function(){
		$scope.clearOnGiftModalClose(closeModal,false);
		AppUtil.setCardType('GIFT');
	};



	$scope.updateGiftCardCode = function (orderItem,cardNumber) {
		cardNumber = cardNumber.replace(/[^a-zA-Z0-9]/g, "");
		card.cardNumber=cardNumber;
		console.log(cardNumber);
		if(card.cardNumber.length==6){
			if(!$scope.isDuplicateCard(card)){
				var gCards = [];
				var configData = AppUtil.getAutoConfigData();

				card.itemId = orderItem.itemId;
				card.buyerId = AppUtil.customerSocket.id;
				card.empId = $rootScope.globals.currentUser.userId;
				card.unitId =  configData.unitId;
				card.terminalId  =  configData.selectedTerminalId;
				card.type = AppUtil.cardType;
				card.isValid = false;
				card.error = "";
				card.productName = orderItem.productName;
				card.cardValue = orderItem.price;

				gCards.push(card);

				posAPI.allUrl('/',AppUtil.restUrls.order.validateGiftCardsInOrder).post(gCards).then(function(response) {
					if(response.errorType != undefined && response.errorType != ''){
						var msg = /*(response.errorType != undefined) ? */"Error validating gift cards!" + response.errorMessage;
						AppUtil.myAlert(msg);
						return false;
					}
					var dataObj = response.plain();
					if (dataObj != null) {
						var valid = true;
						dataObj.map(function(cardObj) {
							if (valid && !cardObj.isValid) {
								valid = false;
							}
						});
						if (!valid) {
							AppUtil.myAlert("Please fill the correct gift card codes.");
							$scope.notEditable[orderItem.itemId]=false;
							$scope.$parent.resetGiftCardCode(card);
						}else{
							$scope.$parent.setGiftCardCode(card);
							$scope.notEditable[orderItem.itemId]=true;
							orderItem.isCardValid = true;
							giftCardNumberList.push(card);
							card={};
						}
					} else {
						var msg = (dataObj.errorType == undefined) ? "Error validating gift cards!" : dataObj.errorMessage;
						AppUtil.myAlert(msg);
						$rootScope.showFullScreenLoader = false;
					}
				}, function(err) {
					AppUtil.myAlert(err.data.errorMessage);
				});
			}else{
				AppUtil.myAlert("Card already added!");
				card={};
			}
		}
	};


	$scope.deleteCardEntry= function(cardNumber,itemId){
		for(var i=0;i<giftCardNumberList.length;i++){
			if(giftCardNumberList[i].cardNumber== cardNumber){
				giftCardNumberList.splice(i, 1);
			}
		}
		$scope.notEditable[itemId]=false;
	};

	$scope.isDuplicateCard = function (card) {
		var found = false;
		for(var i=0;i<giftCardNumberList.length;i++){
			if(giftCardNumberList[i].cardNumber.length>0){
				if(giftCardNumberList[i].cardNumber == card.cardNumber){
					found = true;
					break;
				}
			}
		}
		return found;
	};
}]);
