/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

angular
	.module('posApp')
	.controller(
		'employeeMealModalCtrl',
		['$scope', '$modalInstance', 'AppUtil', 'posAPI', '$location', function($scope, $modalInstance, AppUtil, posAPI, $location) {

		    $scope.init = function() {
			$scope.otherEmployeeIdFlag = false;
			$scope.otherEmployeeId = null;
			posAPI.allUrl('/',AppUtil.restUrls.userManagement.allUsersForUnit)
				.post($scope.$parent.unitDetails.id)
				.then(
					function(response) {
					    $scope.employeeDetails = [];
					    if (response != null) {
						response
							.forEach(function(item) {
							    if (item.status == 'ACTIVE'
								    && (item.departmentName == 'Cafe Operations' || item.departmentName == 'COD Operations')) {
								$scope.employeeDetails.push(item);
							    }
							})
					    }
					    ;
					}, function(err) {
					    AppUtil.myAlert(err.data.errorMessage);
					});
		    };

		    $scope.setEmployeeDetail = function() {

			if ($scope.otherEmployeeIdFlag) {
			    if ($scope.otherEmployeeId == null || $scope.otherEmployeeId == "") {
				alert("Please enter the employee id in the text box")
				return;
			    }
			    posAPI.allUrl('/',AppUtil.restUrls.userManagement.activeUser).post($scope.otherEmployeeId)
				    .then(function(response) {

					if (response != null && response != "") {
					    $scope.selectedEmployee = response;
					    $modalInstance.close($scope.selectedEmployee);
					} else {
					    alert("Please enter a  valid employee id in the text box")
					    return;
					}
				    }, function(err) {
					AppUtil.myAlert(err.data.errorMessage);
				    });
			} else {
			    if ($scope.selectedEmployee == null) {
				alert("Please select the employee from drop down")
				return;
			    }else{
				$modalInstance.close($scope.selectedEmployee);
			    }

			}
			
		    };
		    
		    $scope.goBack = function(){
		          if(!AppUtil.isCOD()){
		              $location.url('/cover');
		          } else {
	        		  $location.url('/CODCover');
		          }
		          $modalInstance.close();
		      };
		}]);
