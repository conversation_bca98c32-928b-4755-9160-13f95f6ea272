/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';
/*jshint sub:true*/

angular.module('posApp')
    .controller('viewSDPCtrl', ['$rootScope', '$scope', '$filter', 'AppUtil', '$location', '$http', '$compile', 'posAPI',
        function ($rootScope, $scope, $filter, AppUtil, $location, $http, $compile, posAPI) {

            $scope.init = function () {
                $scope.outletList = [];
                $scope.showEditInventory = false;
                $scope.backToCover = AppUtil.backToCover;
                fetchOutlets("CAFE");
                fetchOutlets("DELIVERY");
            };

            $scope.getSDPs = function () {
                $scope.employeeDetails = null;
                posAPI.allUrl('/',AppUtil.restUrls.userManagement.allRider).post($scope.unit.id).then(
                    function (response) {
                        $scope.employeeDetails = response;
                        $rootScope.showFullScreenLoader = false;
                    }, function (err) {
                        $rootScope.showFullScreenLoader = false;
                        AppUtil.myAlert(err.data.errorMessage);
                    });
            };

            function fetchOutlets(code) {
                posAPI.allUrl('/',AppUtil.restUrls.unitMetaData.activeUnits).customGET("", {category: code})
                    .then(function (response) {
                        if ($scope.outletList.length === 0) {
                            $scope.outletList = response.plain();
                        } else {
                            angular.forEach(response.plain(), function (v) {
                                $scope.outletList.push(v);
                            });
                        }
                        convertOutletIdToNumber();
                    }, function (err) {
                        AppUtil.myAlert(err.data.errorMessage);
                    });
            }

            function convertOutletIdToNumber() {
                var newOutletList = [];
                angular.forEach($scope.outletList, function (v) {
                    var obj = v;
                    obj.id = parseInt(v.id);
                    newOutletList.push(obj);
                });
                $scope.outletList = newOutletList;
            }


        }]);