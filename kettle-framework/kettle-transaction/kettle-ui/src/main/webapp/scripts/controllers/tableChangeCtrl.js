/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';
/* jshint sub:true */

angular.module('posApp').controller(
		'tableChangeCtrl',
		[
				'$rootScope',
				'$scope',
				'$filter',
				'AppUtil',
				'$location',
				'$http',
				'$compile',
				'posAPI',
				'$modal',
				'tableService',
				'$modalInstance',
				function($rootScope, $scope, $filter, AppUtil, $location,
						$http, $compile, posAPI, $modal, tableService, $modalInstance) {

					$scope.init = function() {
						$scope.tables = [];
						$scope.unitDetails = AppUtil.getUnitDetails();
						$scope.getTableStatus($scope.unitDetails.id);
					};

					$scope.getTableStatus = function(param) {
						var url = AppUtil.restUrls.order.getUnitTables;
						posAPI.allUrl('/', url).customGET("", {
							unitId : param
						}).then(function(response) {
							$scope.tables = response;
						}, function(err) {
							AppUtil.myAlert(err.data.errorMessage);
						});
					};

					$scope.selectTable = function(table) {
						$modalInstance.close(table);
					};

					$scope.cancel = function() {
				        $modalInstance.dismiss('dismiss');
					};

				} ]);