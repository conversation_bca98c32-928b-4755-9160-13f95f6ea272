/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

angular.module('posApp').controller('redeemModalCtrl',
    ['$scope','AppUtil','$location','$modalInstance','customer','loyaltyPointsRedeemed','productService','productId',
        function ($scope,AppUtil,$location,$modalInstance,customer,loyaltyPointsRedeemed,productService,productId) {

        $scope.customer = customer;
        $scope.loyaltyPointsRedeemed = -loyaltyPointsRedeemed;
        $scope.productObj = {};
        $scope.disabled = false;
        $scope.pointsPerItemRedemption = 60;
        
        $scope.init =function(){
        	$scope.disabled = false;
        	getProductFromProductId(productId);
        };
        
        $scope.cancel = function(){
            $modalInstance.dismiss('dismiss');
        };
        
        function getProductFromProductId(productId){
            var productArray = AppUtil.getUnitDetails().products;
            for(var i =0 ; i < productArray.length; i ++ ) {
                if(productArray[i].id == productId){
                	$scope.productObj = productArray[i];
                    if(productId == 691){ // for chai-patti redemption
                        $scope.pointsPerItemRedemption = 120;
                    }
                }
            }

        }

        $scope.addToOrder = function(){
            AppUtil.lockRedeem($scope.loyaltyPointsRedeemed);
            var quantity = ((Math.abs($scope.loyaltyPointsRedeemed)) / $scope.pointsPerItemRedemption);
            productService.addNewProductviaRedeemedPoints($scope.productObj,quantity);
            $scope.disabled = true;
            $modalInstance.close(true);
        };
        
}]);