/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

angular.module('posApp').controller(
    'manageRiderController',
    ['$scope', '$rootScope' , 'AppUtil', 'posAPI',
        function ($scope, $rootScope , AppUtil, posAPI) {

        $scope.init = function () {
            $scope.backToCover = AppUtil.backToCover;
            $scope.employeeDetails = null;
            $scope.showContact = false;
            $scope.selectedEmployee = null;
            $scope.showOTP = false;
            $scope.contact = "";
            $scope.reload();
            $scope.riderOrders=null;
        };

        $scope.reload = function () {
            $scope.showContact = false;
            $scope.showOTP = false;
            $scope.contact = "";
            $scope.newEmployeeId = null;
            $scope.selectedEmployee = null;
            $rootScope.showFullScreenLoader = true;
            posAPI.allUrl('/',AppUtil.restUrls.userManagement.allRider).post(AppUtil.getUnitDetails().id).then(
                function (response) {
                    $scope.employeeDetails = response;
                    $rootScope.showFullScreenLoader = false;
                }, function (err) {
                    $rootScope.showFullScreenLoader = false;
                    AppUtil.myAlert(err.data.errorMessage);
                });
        };

        $scope.addRider = function (employeeId) {
            $scope.updateType = "ADD";
            $scope.selectedEmployee = null;
            $scope.contact = null;
            var object = createMappingObject(employeeId, AppUtil.getUnitDetails().id, 'ENABLED');
            $rootScope.showFullScreenLoader = true;
            posAPI.allUrl('/',AppUtil.restUrls.userManagement.addRider).post(object).then(function (response) {
                if (response.errorTitle == "CONTACT_NOT_AVAILABLE") {
                    $scope.selectedEmployee = {id:employeeId};
                    $scope.getContact();
                    bootbox.alert("Please add SDP contact for this employee.");
                } else {
                    var result = response;
                    if (result == true) {
                        bootbox.alert("Enabled " + employeeId + " rider for delivery");
                    } else {
                        bootbox.alert("Cannot Enable " + employeeId + " rider for delivery");
                    }
                    $scope.reload();
                    $scope.newEmployeeId = null;
                }
                $rootScope.showFullScreenLoader = false;
            }, function (err) {
                $rootScope.showFullScreenLoader = false;
                AppUtil.myAlert(err.data.errorMessage);
                $scope.reload();
            });
        };

        $scope.getContact = function () {
            $scope.contact = null;
            $scope.showContact = true;
            $scope.showOTP = false;
        };

        $scope.sendOTP = function (contact) {
            $scope.contact = contact;
            $scope.contactError = null;
            $scope.otpError = null;
            posAPI.allUrl('/',AppUtil.restUrls.customer.generateOTP).post({contactNumber: contact}).then(function (response) {
                if (response) {
                    $scope.showContact = false;
                    $scope.OTP = null;
                    $scope.showOTP = true;
                } else {
                    $scope.contactError = "Could not send OTP. Try again.";
                }
            }, function (err) {
                $rootScope.showFullScreenLoader = false;
                AppUtil.myAlert(err.data.errorMessage);
                $scope.reload();

            });
        };

        $scope.verifyOTP = function (otp) {
            posAPI.allUrl('/',AppUtil.restUrls.customer.verifyOTP).post({
                contactNumber: $scope.contact, otpPin: otp,
                unit: AppUtil.getUnitDetails().id
            }).then(function (response) {
                $scope.OTP = null;
                if (response == true) {
                    $scope.updateContact();
                } else {
                    $scope.otpError = "Incorrect OTP. Try again.";
                }
            }, function (err) {
                $rootScope.showFullScreenLoader = false;
                AppUtil.myAlert(err.data.errorMessage);
                $scope.reload();
            });
        };

        $scope.updateContact = function () {
            posAPI.allUrl('/',AppUtil.restUrls.userManagement.setSDPContact).post({
                id: $scope.selectedEmployee.id,
                sdpContact: $scope.contact
            }).then(function (response) {
                if (response === true) {
                    if ($scope.updateType == "ADD") {
                        $scope.addRider($scope.selectedEmployee.id);
                    } else if ($scope.updateType == "EDIT") {
                        $scope.addRider($scope.selectedEmployee.id);
                    } else {
                        $scope.enableRider($scope.selectedEmployee);
                    }
                } else if (response != null && response.errorMsg != null ){
                	bootbox.alert(response.errorMsg);
                } else {
                	bootbox.alert("Could not add sdp number. Try again.");
                }
            }, function (err) {
                $rootScope.showFullScreenLoader = false;
                AppUtil.myAlert(err.data.errorMessage);
                $scope.reload();

            });
        };
        
        $scope.clearContact = function (emp) {
            bootbox.confirm("Are you Sure you want to clear SDP Contact?", function(result){
                if (result == true) {
                    posAPI.allUrl('/',AppUtil.restUrls.userManagement.clearSDPContact).post({
                        id: emp.id
                    }).then(function (response) {
                        if (response === true) {
                            $scope.reload();
                        } else {
                            bootbox.alert("Could not clear sdp number. Try again.");
                        }
                    }, function (err) {
                        $rootScope.showFullScreenLoader = false;
                        AppUtil.myAlert(err.data.errorMessage);
                        $scope.reload();

                    });
                }
            });
        };

        $scope.enableRider = function (employee) {
            $scope.selectedEmployee = employee;
            $scope.enableEmployeeId = employee.id;
            $scope.updateType = "ENABLE";
            var object = createMappingObject(employee.id, AppUtil.getUnitDetails().id, 'ENABLED');
            posAPI.allUrl('/',AppUtil.restUrls.userManagement.enableRider).post(object).then(function (response) {
                if (response.errorTitle == "CONTACT_NOT_AVAILABLE") {
                    $scope.getContact();
                    bootbox.alert("SDP contact not available for this employee. Please add contact.");
                } else {
                    var result = response;
                    if (result == true) {
                        bootbox.alert("Enabled " + employee.id + " rider for delivery");
                    } else {
                        bootbox.alert("Cannot Enable " + employee.id + " rider for delivery");
                    }
                    $scope.reload();
                }
            }, function (err) {
                AppUtil.myAlert(err.data.errorMessage);
                $scope.reload();
            });
            $scope.newEmployeeId = null;
        };

        $scope.disableRider = function (employee) {
            $scope.selectedEmployee = employee;
            var object = createMappingObject(employee.id, AppUtil.getUnitDetails().id, 'DISABLED');
            posAPI.allUrl('/',AppUtil.restUrls.userManagement.disableRider).post(object).then(function (response) {
                var result = response;
                if (result == true) {
                    bootbox.alert("Disabled " + employee.id + " rider for delivery");
                } else {
                    bootbox.alert("Cannot Disable " + employee.id + " rider for delivery");
                }
                $scope.reload();
            }, function (err) {
                AppUtil.myAlert(err.data.errorMessage);
                $scope.reload();
            });
        };

        $scope.deleteRider = function (employee) {
            $scope.selectedEmployee = employee;
            var object = createMappingObject(employee.id, AppUtil.getUnitDetails().id, 'DELETED');
            posAPI.allUrl('/',AppUtil.restUrls.userManagement.deleteRider).post(object).then(function (response) {
                var result = response;
                if (result) {
                    bootbox.alert("Removed " + employee.id + " rider for delivery");
                } else {
                    bootbox.alert("Cannot Remove " + employee.id + " rider for delivery");
                }
                $scope.reload();
            }, function (err) {
                AppUtil.myAlert(err.data.errorMessage);
                $scope.reload();
            });
        };

        $scope.editContact = function (employee) {
            $scope.updateType = "EDIT";
            $scope.selectedEmployee = employee;
            $scope.getContact();
        };
        var currentEmployee;
        $scope.getSdpOrders=function(employee){
        	currentEmployee = employee;
        	posAPI.allUrl('/',AppUtil.restUrls.order.getOrdersOfRider).post(employee.sdpContact).then(function (response) {
                if (response != undefined) {
                	$scope.riderOrders=response.plain();
                } else {
                	$scope.riderOrders=null;
                    bootbox.alert("Rider " + employee.id + " has not been assigned any orders for delivery");
                }
                $scope.reload();
            }, function (err) {
                AppUtil.myAlert(err.data.errorMessage);
                $scope.reload();
            });
        };
        
        $scope.resetSdpOrders=function(){
        	for(var key in $scope.riderOrders){
        		if($scope.riderOrders[key] != "SETTLED" && $scope.riderOrders[key] != "DELIVERED"){
        			bootbox.alert("You can reset orders of Rider " + currentEmployee.id + " only when it is in SETTLED or DELIVERED state !");
        			return false;
        		}
        	}
        	bootbox.confirm("Are you Sure you want to reset order of this rider? As it is advised to use assembly screen for this purpose !", function(result){
                if (result == true) {
                	posAPI.allUrl('/',AppUtil.restUrls.order.resetOrdersOfRider).post(currentEmployee.sdpContact).then(function (response) {
                        var result = response;
                        if (result) {
                            bootbox.alert("Rider " + currentEmployee.id + " orders marked delivered successfully and removed from assembly screen.");
                            $scope.riderOrders=null;
                        } else {
                            bootbox.alert("Something went wrong cannot reset rider " + currentEmployee.id + " orders.");
                        }
                        $scope.reload();
                    }, function (err) {
                        AppUtil.myAlert(err.data.errorMessage);
                        $scope.reload();
                    });
                }
            });
        };

        function createMappingObject(employeeId, unitId, status) {
            return {
                employeeId: employeeId,
                unitId: unitId,
                status: status
            };
        }

    }]);
