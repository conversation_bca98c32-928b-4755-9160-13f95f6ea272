/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

(function () {
    'use strict';

    angular.module('posApp').controller('LoginController', LoginController);

    LoginController.$inject = ['$scope', '$rootScope', '$location',
        'AuthenticationService', '$modal', 'AppUtil', 'posAPI',
        '$cookieStore', 'socketUtils', '$http', 'trackingService', 'coverUtils', 'PrintService', '$timeout','httpRequestWithTimeout'];

    function LoginController($scope, $rootScope, $location,
                             AuthenticationService, $modal, AppUtil, posAPI,
                             $cookieStore, socketUtils, $http, trackingService, coverUtils, PrintService, $timeout,httpRequestWithTimeout) {


        var vm = this;
        vm.dataLoading = false;
        vm.isCustomerScreen = false;
        vm.localStorageLength = 0;
        $scope.isMultipleTerminal = false;
        $scope.isPOS = true;
        $scope.selectedCategory = null;
        $scope.selectedTerminalId = 1;
        $scope.screenType = 'POS';
        $scope.terminalArray = [];
        $scope.isDelivery = false;
        $scope.isCafe = true;
        $scope.unitName = '';
        $scope.autoConfig = false;
        $scope.isWorkStationEnabled = false;
        $scope.firstTimeLogin = true;
        vm.outletType = [{
            name: 'Cafe',
            code: 'CAFE'
        }, {
            name: 'COD',
            code: 'DELIVERY'
        }, {
            name: 'Call Center',
            code: 'COD'
        }, {
            name: 'Takeway',
            code: 'TAKE_AWAY'
        }, {
            name: 'Chai Monk',
            code: 'CHAI_MONK'
        }];
        vm.outletList = [];
        AppUtil.isCustomerScreen = false;

        AppUtil.previousLocation = [];
        //fetchOutlets('CAFE');

        $scope.init = function () {
            $scope.setAutoConfig();
        };

        $scope.setAutoConfig = function () {
            $scope.autoConfigData = AppUtil.getAutoConfigData();
            if ($scope.autoConfigData != null && !AppUtil.isEmptyObject($scope.autoConfigData)) {
                vm.unitId = $scope.autoConfigData.unitId;
                vm.localStorageLength = localStorage.length;
                $scope.selectedTerminalId = $scope.autoConfigData.selectedTerminalId;
                $scope.screenType = $scope.autoConfigData.screenType;
                $scope.unitName = $scope.autoConfigData.unitName;
                $scope.screenName = $scope.autoConfigData.screenName;
                $scope.selectedCategory = $scope.autoConfigData.selectedCategory;
                $scope.googleMerchantId = $scope.autoConfigData.googleMerchantId;
                $scope.autoConfig = true;
                try {
                    trackingService.setDefaultAttributes(AppUtil.getOrderModeFromSource($scope.selectedCategory),
                        null, null, $scope.unitName);
                } catch (e) {
                }
            } else {
                $scope.setTerminals();
            }
        };

        $scope.isCustomerScreen = function () {
            AppUtil.isCustomerScreen = $scope.screenType.toLowerCase() == "customer" ? true : false;
            //console.log(AppUtil.isCustomerScreen);
        };

        $scope.$watch('unitName', function () {
            ////console.log($scope.unitName);
            if ($scope.unitName) {
                for (var i = 0; i < vm.outletList.length; i++) {
                    if (vm.outletList[i].name == $scope.unitName) {
                        vm.unitId = vm.outletList[i].id;
                        $scope.isWorkStationEnabled = vm.outletList[i].workStationEnabled;
                    }
                }
            }
        });

        $scope.isWorkStationEnabled = function () {
            for (var i = 0; i < vm.outletList.length; i++) {
                if (vm.outletList[i].id == vm.unitId) {
                    return vm.outletList[i].workStationEnabled;
                }
            }
        };

        $scope.getOutlet = function (searchTexty) {

            var localSearchMap = [];
            var filteredSearch = searchTexty.toLowerCase();

            for (var i = 0; i < vm.outletList.length; i++) {
                if (stringStartsWith(vm.outletList[i].name.toLowerCase(),
                    filteredSearch)) {
                    localSearchMap.push(vm.outletList[i]);
                }

            }
            //console.log(localSearchMap);

            return localSearchMap;
        };

        function stringStartsWith(string, prefix) {
            var re = new RegExp(prefix);
            if (string.match(re) != null) {
                return true;
            } else {
                return false;
            }
        }

        $scope.outletTypeTabSelected = function (code) {
            vm.outletList = [];
            $scope.isDelivery = (code == 'DELIVERY');
            $scope.isCafe = (code == 'CAFE');
            $scope.isTakeaway = (code == 'TAKE_AWAY');
            if (code == 'COD') {
                $scope.isCafe = false;
                $scope.isDelivery = false;
            }

            if (code != 'CAFE') {
                $scope.isPOS = false;
            } else {
                $scope.isPOS = true;
            }
            $scope.selectedCategory = code;
            $scope.outletForEmployeeId();
        };

        $scope.outletForEmployeeId = function () {
            var str = vm.userId == null ? '' : vm.userId.toString();
            if (str.length == 6) {
                vm.outletList = [];
                if (!$scope.autoConfig) {
                    fetchOutletsForEmployee($scope.selectedCategory, $scope.isTakeaway, vm.userId);
                }
            }
        };

        function fetchOutletsForEmployee(code, isTakeaway, employeeId) {
            $rootScope.showFullScreenLoader = true;
            posAPI.allUrl('/', AppUtil.restUrls.userManagement.activeUnitsForUser).post(
                {
                    employeeId: employeeId,
                    onlyActive: true
                }).then(function (response) {
                $rootScope.showFullScreenLoader = false;
                var units = [];
                if (!AppUtil.isEmptyObject(response)) {
                    units = response.plain();
                }
                if (units != null && units.length > 0) {
                    for (var i in units) {
                        if (units[i].category == code && (units[i].live == undefined || (units[i].live != undefined && units[i].live))) {
                            vm.outletList.push(units[i])
                        }
                    }
                }
            }, function (err) {
                $rootScope.showFullScreenLoader = false;
                AppUtil.myAlert(err.data.errorMessage);
                //console.log(err);
            });
        }

        function fetchOutlets(code, isTakeaway) {
            if (!isTakeaway) {
                posAPI.allUrl('/', AppUtil.restUrls.unitMetaData.activeUnits).customGET("",
                    {
                        category: code
                    }).then(function (response) {
                    vm.outletList = response.plain();
                }, function (err) {
                    AppUtil.myAlert(err.data.errorMessage);
                    //console.log(err);
                });
            } else {
                posAPI.allUrl('/', AppUtil.restUrls.unitMetaData.allTAUnits).getList()
                    .then(function (response) {
                        vm.outletList = getActiveUnits(response.plain());
                    }, function (err) {
                        AppUtil.myAlert(err.data.errorMessage);
                        //console.log(err);
                    });
            }
        }

        function getActiveUnits(unitList) {
            var activeUnits = [];
            unitList.forEach(function (v) {
                if (v.status == "ACTIVE") {
                    activeUnits.push(v);
                }
            });
            return activeUnits;
        }

        (function initController() {
            // reset login status
            AuthenticationService.ClearCredentials();
        })();

        /**
         * Pusher for analytics
         */
        $rootScope.pusher = null;
        $rootScope.channel = null;
        $rootScope.unitsReportData = null;
        $rootScope.unitsAllCurrentData = null;
        $rootScope.unitsAllTargetData = null;
        $rootScope.unitsReportDataLW = null;

        $rootScope.initPusher = function () {
            $rootScope.fetchCurrentAllSalesData();
            $rootScope.fetchAllCurrentDataAtOnce();
            $rootScope.fetchPenetrationTargetData();
            $rootScope.fetchAllTargetDataAtOnce();
            $rootScope.fetchPenetrationDailyTargetData();
            $rootScope.fetchSpecificUnitReportData();
            $rootScope.fetchSystemWideReportData();
            $scope.firstTimeLogin=null;
            //$rootScope.fetchPenetrationLMData();
        };

        $rootScope.fetchCurrentAllSalesData = function () {
            //console.log("Inside fetch current all sales data");
            var data={
                method: 'GET',
                url: AppUtil.restUrls.analyticsData.unitSales + '?unitId=' + $rootScope.globals.currentUser.unitId,
            }
            httpRequestWithTimeout.sendApi(data,1000).then(function success(response) {
                $rootScope.unitsReportData = response.data;
            });
            $rootScope.fetchPenetrationCurrentData();
            $rootScope.fetchPenetrationDailyCurrentData();
            if (typeof $rootScope.refreshAnalyticsTimer !== 'undefined' && typeof $rootScope.refreshAnalyticsTimer === 'function') {
                $rootScope.refreshAnalyticsTimer();
            }
        };

        $rootScope.fetchAllCurrentDataAtOnce = function () {
            // console.log("Inside fetch All current data at once");
            var data={
                method: 'GET',
                url: AppUtil.restUrls.analyticsData.currentAllData + '?unitId=' + $rootScope.globals.currentUser.unitId+'&'+'loggedIn='+$scope.firstTimeLogin,
            };
            httpRequestWithTimeout.sendApi(data,1000).then(function success(response) {
                console.log(response.data);
                $rootScope.unitsAllCurrentData = response.data;
            });
        };

        $rootScope.fetchAllTargetDataAtOnce = function () {
            // console.log("Inside fetch All target data at once");
            var data={
                method: 'GET',
                url: AppUtil.restUrls.analyticsData.targetAllData + '?unitId=' + $rootScope.globals.currentUser.unitId+'&'+'loggedIn='+$scope.firstTimeLogin,
            };
            httpRequestWithTimeout.sendApi(data,1000).then(function success(response) {
                console.log(response.data);
                $rootScope.unitsAllTargetData = response.data;
                $rootScope.showFullScreenLoader = false;
            });
        };

        $rootScope.fetchPenetrationTargetData = function () {
            var data= {
                method: 'GET',
                url: AppUtil.restUrls.analyticsData.unitPenetrationTarget + '?unitId=' + $rootScope.globals.currentUser.unitId+'&'+'loggedIn='+$scope.firstTimeLogin,
            }
            httpRequestWithTimeout.sendApi(data,1000).then(function success(response) {
                $rootScope.unitsPenetrationTarget = response.data;
            });
        };

        $rootScope.fetchPenetrationDailyTargetData = function () {
            var data={
                method: 'GET',
                url: AppUtil.restUrls.analyticsData.unitPenetrationTargetForDay + '?unitId=' + $rootScope.globals.currentUser.unitId+'&'+'loggedIn='+$scope.firstTimeLogin,
            }
            httpRequestWithTimeout.sendApi(data,1000).then(function success(response) {
                $rootScope.unitsPenetrationTargetForDay = response.data;
            });
        };

        $rootScope.fetchPenetrationCurrentData = function () {
           var data= {
               method: 'GET',
               url: AppUtil.restUrls.analyticsData.unitPenetrationCurrent + '?unitId=' + $rootScope.globals.currentUser.unitId+'&'+'loggedIn='+$scope.firstTimeLogin,
           }
            httpRequestWithTimeout.sendApi(data,1000).then(function success(response) {
                $rootScope.unitsPenetrationCurrent = response.data;
            });
        };

        $rootScope.fetchPenetrationDailyCurrentData = function () {
           var data={
                method: 'GET',
                url: AppUtil.restUrls.analyticsData.unitPenetrationCurrentForDay + '?unitId=' + $rootScope.globals.currentUser.unitId
            }
            httpRequestWithTimeout.sendApi(data,1000).then(function success(response) {
                $rootScope.unitsPenetrationCurrentForDay = response.data;
            });
        };

        $rootScope.fetchPenetrationLMData = function () {
            var data={
                method: 'GET',
                url: AppUtil.restUrls.analyticsData.unitPenetrationLastMonth + '?unitId=' + $rootScope.globals.currentUser.unitId
            }
            httpRequestWithTimeout.sendApi(data,1000).then(function success(response) {
                $rootScope.unitsPenetrationLM = response.data;
            });
        };

        $rootScope.fetchSpecificUnitReportData = function () {
            var data= {
                method: 'GET',
                url: AppUtil.restUrls.analyticsData.unitSalesReportData + '?unitId=' + $rootScope.globals.currentUser.unitId
            }
            httpRequestWithTimeout.sendApi(data,1000).then(function success(response) {
                $rootScope.unitReportData = response.data;
            });
        };

        $rootScope.fetchSystemWideReportData = function () {
            var data= {
                method: 'GET',
                url: AppUtil.restUrls.analyticsData.systemSalesReportData
            }
            httpRequestWithTimeout.sendApi(data,1000).then(function success(response) {
                $rootScope.systemReportData = response.data;
            });
        };



        function createBinding() {
            // var channel = $scope.pusher.subscribe('sales_data');
            $rootScope.channel.bind($rootScope.globals.currentUser.unitId, function (data) {
                $rootScope.$apply(function () {
                    $rootScope.unitsReportData = data.reportData;
                });
            });
        }

        /*function subscribeChannel() {
        	var envType = "dev";
            if ($location.host().indexOf("prod") != -1) {
            	envType = "prod";
            }
            //console.log("initializing...");
            Pusher.logToConsole = true;
            $rootScope.pusher = new Pusher('668c61c6259750d8ab74', {
                cluster: 'eu',
                encrypted: true
            });

            $rootScope.channel = $rootScope.pusher.subscribe(envType
                + '_sales_data');

        }*/

        /**
         * End of Pusher for analytics
         */

        function login() {
            vm.dataLoading = true;
            console.log($scope.googleMerchantId)
            ////console.log(vm.userId, vm.password, vm.unitId);
            if (vm.unitId == null || vm.unitId == "") {
                AppUtil.myAlert('Plese select Unit!');
                vm.dataLoading = false;
                return;
            }
            AuthenticationService.Login(vm.userId, vm.password, vm.unitId, $scope.selectedTerminalId, $scope.screenType, function (res) {
                vm.dataLoading = false;
                $rootScope.aclData = res.acl;
                $rootScope.analyticsRefreshTime = new Date();
                console.log(res);
                if(res.errorMessage === 'Passcode expired'){
                   AppUtil.myAlert('Your passcode has expired. Please Enter new passcode');
                }
                else if (res == undefined || res.sessionKeyId == null) {
                    AppUtil.myAlert('Username & Passcode don\'t match');
                 }
                 else {
                    // need to set credentials to call AppUtil.GetRequest
                    AuthenticationService.SetCredentials(
                        vm.userId,
                        vm.unitId,
                        res.sessionKeyId,
                        res.user.name,
                        res.user.designation,
                        $scope.selectedTerminalId,
                        $scope.screenType,
                        "",
                        res.jwtToken,
                        res.application,
                        $scope.googleMerchantId
                    );
                    var unitName = null;
                    vm.outletList.forEach(function (outlet) {
                        if (outlet.id == vm.unitId) {
                            unitName = outlet.name;
                        }
                    });
                    var screenName = "Transaction";
                    if ($scope.screenType != "POS") {
                        screenName = "Assembly";
                    }
                    if (!$scope.autoConfig) {
                        AppUtil.setAutoConfigData({
                            unitId: vm.unitId,
                            unitName: unitName,
                            selectedTerminalId: $scope.selectedTerminalId,
                            screenType: $scope.screenType,
                            screenName: screenName,
                            selectedCategory: $scope.selectedCategory,
                            googleMerchantId: $scope.googleMerchantId
                        });
                    }
                    try {
                        trackingService.setDefaultAttributes(AppUtil.getOrderModeFromSource($scope.autoConfig ? $scope.autoConfigData.selectedCategory : $scope.selectedCategory),
                            null, null, !$scope.autoConfig ? unitName : $scope.unitName);
                    } catch (e) {
                    }

                    $rootScope.showFullScreenLoader = true;
                    //var requestObj = AppUtil.GetRequest(vm.unitId);
                    posAPI.allUrl('/', AppUtil.restUrls.unitMetaData.metaData).post(vm.unitId).then(function (response) {
                        AppUtil.transactionMetadata = response.plain();
                        //AppUtil.transactionMetadata.brandList = response.brandList;
                        AppUtil.setSelectedBrand(AppUtil.getBrandByBrandId(AppUtil.CHAAYOS_BRAND_ID));
                        try {
                            trackingService.setPaymentMode(AppUtil.transactionMetadata.paymentModes);
                        } catch (e) {
                        }
                        AppUtil.getMassOffers(); // getting offers after login
                        console.log(AppUtil.transactionMetadata.brandList);
                        //  AppUtil.transactionMetadata.brandList = obj.brandList;
                        var selectedCat = $scope.autoConfig ? $scope.autoConfigData.selectedCategory : $scope.selectedCategory;
                        if (selectedCat == "CAFE") {
                            $scope.getGiftCardOfferInfo(vm.unitId);
                        }
                        posAPI.allUrl('/', AppUtil.restUrls.posMetaData.metaDataByCategory).post(selectedCat).then(function (response) {
                            $rootScope.showFullScreenLoader = false;
                            var obj = response.plain();
                            AppUtil.transactionMetadata.complimentaryCodes = obj.complimentaryCodes;
                            AppUtil.transactionMetadata.deliveryPartner = obj.deliveryPartner;
                            AppUtil.transactionMetadata.channelPartner = obj.channelPartner;
                            AppUtil.transactionMetadata.cancellationReasons = obj.cancellationReasons;
                            AppUtil.arrangeProductCategory(AppUtil.transactionMetadata.categories);
                            localStorage.setItem("transactionMetadata", JSON.stringify(AppUtil.transactionMetadata));
                            $scope.getUnitdata(res);
                            $rootScope.initPusher();
                        }, function (err) {
                            $rootScope.showFullScreenLoader = false;
                            AppUtil.myAlert(err.data.errorMessage);
                        });

                    }, function (err) {
                        $rootScope.showFullScreenLoader = false;
                        AppUtil.myAlert(err.data.errorMessage);
                    });
                }
            }, function (err) {
                vm.dataLoading = false;
                AppUtil.myAlert("Username & Passcode is invalid");
            });
        }

        $scope.getGiftCardOfferInfo = function (unitId) {
            AppUtil.giftCardOffers = null;
            posAPI.allUrl('/', AppUtil.restUrls.order.getCustomerCardOffer + "?partnerId=1").post(unitId).then(function (response) {
                AppUtil.giftCardOffers = response.plain();
            }, function (err) {
                AppUtil.myAlert(err.data.errorMessage);
            });
        };

        $scope.setActivePaymentModes = function () {
            if (AppUtil.transactionMetadata != null && AppUtil.unitDetails != null) {
                AppUtil.transactionMetadata.paymentModes.forEach(function (v) {
                    v.enabled = false;
                    if (AppUtil.unitDetails.paymentModes == null) {
                        return;
                    }
                    for (var i in AppUtil.unitDetails.paymentModes) {
                        if (AppUtil.unitDetails.paymentModes[i] == v.id) {
                            v.enabled = true;
                            break;
                        }
                    }
                });
            }
        };

        $scope.getUnitdata = function (res) {
            //var requestObj = AppUtil.GetRequest(vm.unitId);
            $rootScope.showFullScreenLoader = true;
            posAPI.allUrl('/', AppUtil.restUrls.unitMetaData.unit).post(vm.unitId).then(function (response) {
                AppUtil.unitDetails = response.plain();
                if (AppUtil.unitDetails.family != "COD"
                    && !AuthenticationService.isAssembly($scope.screenType)) {
                    // set alert reminder for RO
                    raiseAlert(20, 21, "Please raise RO for milk if not done!");
                    raiseAlert(10, 30, "Please raise RO for milk if not done!");
                    raiseAlert(14, 0, "Please raise RO for breads if not done!");
                    raiseAlert(14, 30, "Please raise RO for breads if not done!");
                }
                $scope.setActivePaymentModes();
                posAPI.allUrl('/', AppUtil.restUrls.posMetaData.unitMetadata)
                    .post(vm.unitId).then(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    var obj = response.plain();
                    AppUtil.unitDetails.lastBusinessDate = obj.lastBusinessDate;
                    AppUtil.unitDetails.deliveryPartners = obj.deliveryPartners;
                }, function (err) {
                    $rootScope.showFullScreenLoader = false;
                    AppUtil.myAlert(err.data.errorMessage);
                });
                AppUtil.getCreditAccounts();
                AppUtil.disableForTakeaway = $scope.isTakeaway && AppUtil.unitDetails.family == "CAFE";
                AppUtil.unitDetails.family = $scope.isTakeaway ? "TAKE_AWAY" : AppUtil.unitDetails.family;
                var ud = localStorage.getItem("unitDetails") != null ? JSON.parse(localStorage.getItem("unitDetails")) : null;
                if (ud != null && ud.id == AppUtil.unitDetails.id) {
                    AppUtil.unitDetails.products = ud.products;
                    AppUtil.unitDetails.taxes = ud.taxes;
                    AppUtil.unitDetails.prices = ud.prices;
                    AppUtil.unitDetails.priceMap = ud.priceMap;
                    AppUtil.unitDetails.originalPrices = ud.originalPrices;
                    AppUtil.unitDetails.currentMenuType = ud.currentMenuType;

                }
                localStorage.setItem("unitDetails", JSON.stringify(AppUtil.unitDetails));
                AuthenticationService.SetCredentials(
                    vm.userId,
                    vm.unitId,
                    res.sessionKeyId,
                    res.user.name,
                    res.user.designation,
                    $scope.selectedTerminalId,
                    $scope.screenType,
                    AppUtil.unitDetails.family,
                    res.jwtToken,
                    res.application,
                    $scope.googleMerchantId
                );

                if (AppUtil.unitDetails.family == "CAFE" || AppUtil.unitDetails.family == "TAKE_AWAY") {
                    //registering socket on nginx server
                    socketUtils.register({
                        unit: vm.unitId,
                        terminal: $scope.selectedTerminalId,
                        type: $scope.screenType
                    });
                }

                /*//calling failed messages request for repeated pings
                if ($scope.screenType == "ASSEMBLY") {
                    AuthenticationService.failedMessages($scope.screenType);
                }*/


                if (AppUtil.isCustomerScreen) {
                    $location.url('/csthankyou');
                } else {
                    $rootScope.isDefaultPasscode = res.isDefaultPasscode;
                    $scope.switchLocation();
                    if (!AppUtil.isAndroid && !AppUtil.isCOD()) {
                        PrintService.loadQZPrinter();
                        $timeout(function () {
                            coverUtils.testPrinter();
                        }, 5000);
                    }
                    if (($cookieStore.get('isQZLoaded') === 'undefined'
                        || !$cookieStore.get('isQZLoaded'))
                        && (!AppUtil.isCOD())) {
                        if (!AppUtil.isAndroid) {
                            $cookieStore.put('isQZLoaded', true);
                        }
                    }
                }
            }, function (err) {
                $rootScope.showFullScreenLoader = false;
                AppUtil.myAlert(err.data.errorMessage);
            });

            posAPI.allUrl('/', AppUtil.restUrls.scmService.pendingStockEvent).post(vm.unitId).then(function (response) {
                if (response.plain() != null) {
                    AppUtil.setPendingStockCalendarEvent(response.plain().length != 0);
                } else {
                    AppUtil.setPendingStockCalendarEvent(false);
                }
            }, function (err) {
                AppUtil.myAlert(err.data.errorMessage);
                AppUtil.setPendingStockCalendarEvent(false);
            });
            $scope.getUnitPartnerBrandMetadata(vm.unitId, 1, 1);
        };

        $scope.getUnitPartnerBrandMetadata = function(unitId, partnerId, brandId) {
            posAPI.allUrl('/', AppUtil.restUrls.brandMetadata.getUnitPartnerBrandMetadata)
                .post({unitId:unitId, partnerId:partnerId, brandId:brandId})
                .then(function (response) {
                if (response.plain() != null) {
                    AppUtil.setUnitPartnerBrandMetadata(response.plain());
                }
            }, function (err) {
                AppUtil.myAlert(err.data.errorMessage);
            });
        };

        $scope.switchLocation = function () {
            var url;
            if (AppUtil.unitDetails.family == "COD") {
                url = '/CODCover';
                getLocalityMappings();
                getUnitPartnerBrandMappings();
            } else {
                url = '/cover';
            }
            if ($scope.screenType.toLowerCase() == 'assembly') {
                url = '/assembly';
            }
            $location.url(url);
        };

        $scope.setTerminals = function () {
            $scope.$watch("vm.unitId",
                function (newValue, oldValue) {
                    $scope.terminalArray = [];
                    $scope.isMultipleTerminal = false;
                    $scope.selectedTerminalId = 1;
                    for (var i = 0; i < vm.outletList.length; i++) {
                        if (vm.outletList[i].id == vm.unitId) {
                            console.log("setting google merchant Id")
                            $scope.googleMerchantId = vm.outletList[i].googleMerchantId
                            $scope.terminalArray = $scope.getTerminalArray(vm.outletList[i], $scope.isTakeaway);
                            if ($scope.terminalArray.length > 0) {
                                $scope.terminalArray[0].checked = true;
                                $scope.selectedTerminalId = $scope.terminalArray[0].index;
                                $scope.isMultipleTerminal = true;
                            }
                        }
                    }
                    //console.log("terminal array ::: ", $scope.terminalArray);
                    //console.log("multiple terminal flag ::: ", $scope.isMultipleTerminal);
                });
        };

        function getLocalityMappings() {
            // There was an error.
            $scope.isLoading = true;
            posAPI.allUrl('/', AppUtil.restUrls.unitMetaData.localityMappings).getList().then(function (response) {
                if (response.length > 0) {
                    for (var i = 0; i < response.length; i++) {
                        AppUtil.localityMapping.push(response[i]);
                        if (AppUtil.cities.indexOf(response[i].city) == -1) {
                            AppUtil.cities[response[i].city] = {
                                city: response[i].city,
                                state: response[i].state
                            };
                        }
                    }
                } else {
                    AppUtil.myAlert("No results found");
                }
                $scope.isLoading = false;
            }, function () {
                // There was an error.
                $scope.isLoading = false;
            });
        }

        function getUnitPartnerBrandMappings() {
            posAPI.allUrl('/', AppUtil.restUrls.brandMetadata.getAllUnitPartnerBrandMappings).getList().then(function (response) {
                var data = response.plain();
                if (data.length > 0) {
                    var brandMap = {};
                    AppUtil.transactionMetadata.brandList.map(function (brand) {
                        brandMap[brand.id] = brand;
                    });
                    data.map(function (mapping) {
                        if(brandMap[mapping.brandId] != null) {
                            mapping.brand = brandMap[mapping.brandId];
                        }
                    });
                    AppUtil.unitPartnerBrandMappings = data;
                } else {
                    AppUtil.myAlert("No unit partner brand mappings found");
                }
            }, function () {
            });
        }

        $scope.getTerminalArray = function (outlet, isTakeAway) {
            var noOfTerminals = !isTakeAway ? outlet.noOfTerminal : outlet.noOfTakeawayTerminals;
            var terminalArray = [];
            //console.log("outlet terminals are of takeaway", isTakeAway, noOfTerminals);
            if (noOfTerminals > 0) {
                var seed = isTakeAway ? 100 : 0;
                for (var j = 0; j < noOfTerminals; j++) {
                    var index = (eval(seed) + j + 1);
                    var terminal = {
                        index: index,
                        name: 'Terminal ' + index,
                        checked: false
                    };
                    terminalArray.push(terminal);
                }
            }
            return terminalArray;
        };

        $scope.selectedTerminal = function (terminalIndex) {
            $scope.terminalArray.forEach(function (terminal) {
                if ($scope.terminalArray.indexOf(terminal) == terminalIndex) {
                    $scope.selectedTerminalId = terminal.index;
                    terminal.checked = true;
                } else {
                    terminal.checked = false;
                }
            });
            //console.log("terminal array::::",$scope.terminalArray);

            //console.log("terminal index::::", $scope.selectedTerminalId);
        };

        function passcodeChangeModalOpen() {
            $modal.open({
                animation: true,
                templateUrl: window.version + 'views/passcodeChangeModal.html',
                controller: 'ModalInstanceCtrl',
                size: 'sm'
            });
        }

        function clearSiteData() {
            localStorage.clear();
            sessionStorage.clear();
            window.location.reload();
        }

        vm.login = login;
        vm.passcodeChangeModalOpen = passcodeChangeModalOpen;
        vm.clearSiteData = clearSiteData;
    }

})();
