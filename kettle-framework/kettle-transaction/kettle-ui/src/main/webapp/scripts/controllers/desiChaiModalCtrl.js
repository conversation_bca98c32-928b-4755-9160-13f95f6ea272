/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

angular
    .module('posApp')
    .controller(
        'desiChaiModalCtrl',
        ['$scope', '$modalInstance', 'orderItem', '$filter', 'AppUtil', 'productService', 'desiChaiService', 'isDesiChai','customerObj',
            function ($scope, $modalInstance, orderItem, $filter, AppUtil, productService, desiChaiService, isDesiChai,customerObj) {


            $scope.initCustom = function () {
                $scope.customerObj = customerObj;
                $scope.orderItem = orderItem;
                $scope.orderItem.isHeartButtonClicked=undefined;
                $scope.chaiSavedFromDineIn = $scope.orderItem.chaiSavedFromDineIn;
                $scope.isFavChai= $scope.orderItem.isFavChaiMarked;
                $scope.checkIsHeartButtonClicked();
                $scope.itemQuantity = $scope.orderItem.orderDetails.quantity;
                //$scope.selectedOrderItem = orderItem;
                $scope.itemIndex = $scope.orderItem.orderDetails.itemId;
                $scope.desiChaiProducts = (isDesiChai ? desiChaiService.getDesiChaiProducts():desiChaiService.getBaarishWaliChaiProducts());
                $scope.orderItems = {};
                $scope.selectedAddonsList = [];
                $scope.selectedOptionsList = [];
                $scope.selectedVariantMap = {};
                $scope.selectedProductsMap = {};
                $scope.customizationsList =[];
                $scope.currentCustomizationList=[];
                $scope.desiChaiProducts.map(function (product) {
                    if ($scope.orderItem.productDetails.id != product.id) {
                        //$scope.orderItems[product.id] = AppUtil.addNewItem(product, 1, false, $scope.itemIndex);
                        var priceObj = null;
                        product.prices.map(function (price) {
                            if (price.dimension == orderItem.orderDetails.dimension) {
                                priceObj = price;
                            }
                            if (product.id == 80 && ["Regular", "Full"].indexOf(orderItem.orderDetails.dimension) >= 0 && price.dimension == "None") {
                                priceObj = price;
                            }
                        });
                        if(priceObj != null){
	                        $scope.orderItems[product.id] = AppUtil.addNewItemForIndex(product, $scope.itemQuantity, false, priceObj, $scope.itemIndex);
	                        $scope.initCustomizations(product);
                        }
                    }
                });
                $scope.getAllComplimentaryCodes = AppUtil.getAllComplimentaryCodes;
                $scope.complimentaryDetail = $scope.orderItem.orderDetails.complimentaryDetail.isComplimentary ? $scope.orderItem.orderDetails.complimentaryDetail
                    : {
                        reasonCode: null,
                        isComplimentary: null,
                        reason: null
                    };
                $scope.orderItems[$scope.orderItem.productDetails.id] = $scope.orderItem;
                $scope.initCustomizations($scope.orderItem.productDetails);
                $scope.selectedOrderItem = $scope.orderItems[$scope.orderItem.productDetails.id];

                console.log($scope.desiChaiProducts);
            };

            $scope.checkIsHeartButtonClicked =function (){
                $scope.favChaiInsideModal = $scope.orderItem.isFavChaiMarked;
            }

            $scope.initCustomizations = function (product) {
                $scope.customizationsList =[];
                if ($scope.orderItems[product.id].recipeDetails.ingredient != null && $scope.orderItems[product.id].recipeDetails.ingredient.variants != null &&
                    $scope.orderItems[product.id].recipeDetails.ingredient.variants.length > 0) {
                    $scope.orderItems[product.id].recipeDetails.ingredient.variants.map(function (variant) {
                        variant.details.map(function (detail) {
                            if (detail.selected == true) {
                                $scope.selectVariantsData(variant, detail);
                            }
                        });
                    });
                }
                if ($scope.orderItems[product.id].recipeDetails.ingredient != null && $scope.orderItems[product.id].recipeDetails.ingredient.products != null &&
                    $scope.orderItems[product.id].recipeDetails.ingredient.products.length > 0) {
                    $scope.orderItems[product.id].recipeDetails.ingredient.products.map(function (product) {
                        product.details.map(function (detail) {
                            if (detail.selected == true) {
                                $scope.selectedProductsMap[product.display] = detail.product.productId;
                                $scope.customizationsList.push(detail.product.name);
                            }
                        });
                    });
                }
                if ($scope.orderItems[product.id].recipeDetails.addons != null &&
                    $scope.orderItems[product.id].recipeDetails.addons.length > 0) {
                    $scope.orderItems[product.id].recipeDetails.addons.map(function (addon) {
                        if (addon.selected == true) {
                            $scope.selectedAddonsList.push(addon.product.productId);
                            $scope.customizationsList.push(addon.product.name);
                        }
                    });
                }
                if ($scope.orderItems[product.id].recipeDetails.options != null &&
                    $scope.orderItems[product.id].recipeDetails.options.length > 0) {
                    $scope.orderItems[product.id].recipeDetails.options.map(function (option) {
                        if (option.selected == true) {
                            $scope.selectedOptionsList.push(option.id);
                            $scope.customizationsList.push(option.name);
                        }
                    });
                }
                console.log("Initial customization lIst  ::::::::",$scope.customizationsList);
            };

            $scope.selectDesiChaiProduct = function (productItem) {
                if($scope.orderItem.isFavChaiMarked){
                    if($scope.orderItem.productDetails.id!=productItem.id){
                        $scope.favChaiInsideModal= false;
                    }else{
                        $scope.favChaiInsideModal= true;
                    }
                }
                $scope.selectedOrderItem = $scope.orderItems[productItem.id];
                console.log($scope.selectedOrderItem);
            };
            
            $scope.selectTakeAway  = function(item) {
                if(item.orderDetails.takeAway == undefined || item.orderDetails.takeAway == null){
            	item.orderDetails.takeAway = true;
                }else{
            	item.orderDetails.takeAway = !item.orderDetails.takeAway;
                }
                
            };
            $scope.selectVariantsData = function (variant, detail,isAnyVariantButtonClicked) {
                var key = "";
                variant.details.map(function (detail) {
                    key = key + detail.alias;
                });
                $scope.selectedVariantMap[key] = detail.alias;
                if(isAnyVariantButtonClicked){
                    for(var i in variant.details){
                        if(variant.details[i].alias==detail.alias){
                            $scope.checkIfPresentInCustomizationList(variant.details[i].alias,true);
                        }else{
                            $scope.checkIfPresentInCustomizationList(variant.details[i].alias,false);
                        }
                    }
                }else {
                    $scope.customizationsList.push(detail.alias);
                }
            };

            $scope.selectProductsData = function (product, detail) {
                $scope.selectedProductsMap[product.display] = detail.product.productId;
            };

            $scope.checkSelectedVariant = function (variant, alias) {
                var key = "";
                variant.details.map(function (detail) {
                    key = key + detail.alias;
                });
                return $scope.selectedVariantMap[key] == alias;
            };

            $scope.selectAddOns = function (addon) {
                if ($scope.selectedAddonsList.indexOf(addon.product.productId) < 0) {
                    $scope.selectedAddonsList.push(addon.product.productId);
                    $scope.checkIfPresentInCustomizationList(addon.product.name,true);
                } else {
                    $scope.selectedAddonsList.splice($scope.selectedAddonsList.indexOf(addon.product.productId), 1);
                    $scope.checkIfPresentInCustomizationList(addon.product.name,false);
                }
            };

            $scope.selectOptions = function (option) {
                if ($scope.selectedOptionsList.indexOf(option.id) < 0) {
                    $scope.selectedOptionsList.push(option.id);
                    $scope.checkIfPresentInCustomizationList(option.name,true);
                } else {
                    $scope.selectedOptionsList.splice($scope.selectedOptionsList.indexOf(option.id), 1);
                    $scope.checkIfPresentInCustomizationList(option.name,false);
                }
            };

            $scope.checkIfPresentInCustomizationList=function(alias, isSelected){
                if ($scope.currentCustomizationList != undefined && $scope.currentCustomizationList.length == 0) {
                    $scope.currentCustomizationList = angular.copy($scope.customizationsList);
                }
                if (isSelected) {
                    if ($scope.currentCustomizationList != null && $scope.currentCustomizationList.length >=0 && $scope.currentCustomizationList.indexOf(alias) < 0) {
                        $scope.currentCustomizationList.push(alias);
                    }
                } else {
                    $scope.currentCustomizationList = $scope.currentCustomizationList.filter(function (ele) {
                        return ele != alias;
                    });
                }
                console.log("Current Customizations List------->", $scope.currentCustomizationList);
                if ($scope.currentCustomizationList.length == $scope.customizationsList.length) {
                    if ($scope.currentCustomizationList.length > 0 && $scope.customizationsList.length > 0) {
                        var isEqual = $scope.currentCustomizationList.every(function(customization){
                            return $scope.customizationsList.indexOf(customization)!==-1;
                        });
                        if (isEqual && $scope.isFavChai!=undefined && $scope.isFavChai) {
                            $scope.favChaiInsideModal = true;
                        } else {
                            $scope.favChaiInsideModal = false;
                        }
                    }
                } else {
                    $scope.favChaiInsideModal = false;
                }
                $scope.selectedOrderItem.isFavChaiMarked =$scope.favChaiInsideModal;
            };

                $scope.checkProduct = function (value) {
                var found = false;
                if (value.type == "PRODUCT") {
                    AppUtil.getUnitDetails().products.map(function (item) {
                        if (item.id == value.productId) {
                            found = true;
                            if (AppUtil.isPaidEmployeeMeal()) {
                                var empMealPrice = AppUtil.getUnitDetails().empMealPrices[item.id + '_' + item.prices[0].dimension];
                                if (empMealPrice != null) {
                                    found = true;
                                } else {
                                    found = false;
                                }
                            }
                        }
                    });
                } else {
                    found = true;
                }
                return found;
            };

            $scope.checkRedemption = function (value) {
                if($scope.orderItem.orderDetails.hasBeenRedeemed == true){
                    return value.id != 80;
                }else{
                    return true
                }
            };

            $scope.cancelCustomize = function () {
                if($scope.selectedOrderItem.isHeartButtonClicked!=undefined && $scope.orderItem.isHeartButtonClicked){
                    $scope.selectedOrderItem.isHeartButtonClicked=undefined;
                    if($scope.favChaiInsideModal!=undefined && $scope.favChaiInsideModal){
                        $scope.favChaiInsideModal=false;
                        $scope.orderItem.isFavChaiMarked=$scope.favChaiInsideModal;
                    }
                }
                orderItem = $scope.selectedOrderItem;
                $modalInstance.dismiss('cancel');
            };

            $scope.checkFavChaiMarked = function () {
                if($scope.favChaiInsideModal==undefined){
                    $scope.favChaiInsideModal=true
                }else{
                    $scope.favChaiInsideModal=!$scope.favChaiInsideModal;
                }
                $scope.selectedOrderItem.isFavChaiMarked =$scope.favChaiInsideModal;
                $scope.selectedOrderItem.isHeartButtonClicked = true;
                return $scope.favChaiInsideModal;
            }

            $scope.updateFavChai=function(){
                $scope.selectedOrderItem.isFavChaiMarked=!$scope.selectedOrderItem.isFavChaiMarked;
            }

            $scope.submit = function () {
                console.log($scope.complimentaryDetail);
                $scope.selectedOrderItem.orderDetails.complimentaryDetail = $scope.complimentaryDetail;
                $scope.selectedOrderItem.isModified = true;
                $scope.selectedOrderItem.orderDetails.hasBeenRedeemed = $scope.orderItem.orderDetails.hasBeenRedeemed;
                $scope.selectedOrderItem.recipeDetails.ingredient.variants.map(function (variant) {
                    var key = "";
                    variant.details.map(function (detail) {
                        key = key + detail.alias;
                    });
                    variant.details.map(function (detail) {
                        detail.selected = ($scope.selectedVariantMap[key] == detail.alias);
                    });
                });
                $scope.selectedOrderItem.recipeDetails.ingredient.products.map(function (product) {
                    product.details.map(function (detail) {
                        detail.selected = ($scope.selectedProductsMap[product.display] == detail.product.productId);
                    });
                });
                $scope.selectedOrderItem.recipeDetails.addons.map(function (addon) {
                    addon.selected = ($scope.selectedAddonsList.indexOf(addon.product.productId) >= 0);
                });
                $scope.selectedOrderItem.recipeDetails.options.map(function (option) {
                    option.selected = ($scope.selectedOptionsList.indexOf(option.id) >= 0);
                });

                $scope.selectedOrderItem = angular.copy(AppUtil.calculateCustomization($scope.selectedOrderItem, $scope.itemIndex));
                //remove all product type options
                var length = 0;
                $scope.$parent.orderItemArray.map(function (item) {
                    if (item.productDetails.parentProductId == $scope.selectedOrderItem.orderDetails.itemId) {
                        length++;
                    }
                });
                for (var i = 0; i < length; i++) {
                    for (var j = 0; j < $scope.$parent.orderItemArray.length; j++) {
                        if ($scope.$parent.orderItemArray[j].productDetails.parentProductId == $scope.selectedOrderItem.orderDetails.itemId) {
                            var ind = $scope.$parent.orderItemArray.length - (j + 1);
                            $scope.$parent.deleteItem(ind);
                            break;
                        }
                    }
                }
                if ($scope.selectedOrderItem.recipeDetails.options != null && $scope.selectedOrderItem.recipeDetails.options.length > 0) {
                    for (var i in $scope.selectedOrderItem.recipeDetails.options) {
                        if ($scope.selectedOrderItem.recipeDetails.options[i].selected &&
                            $scope.selectedOrderItem.recipeDetails.options[i].type == "PRODUCT") {
                            var found = false;
                            $scope.$parent.orderItemArray.map(function (item) {
                                if (item.productDetails.parentProductId == $scope.selectedOrderItem.orderDetails.itemId &&
                                    item.productDetails.id == $scope.selectedOrderItem.recipeDetails.options[i].productId) {
                                    found = true;
                                }
                            });
                            if (!found) {
                                productService.addNewProductToOrderItemArrayByProductId($scope.selectedOrderItem.recipeDetails.options[i].productId,
                                    $scope.selectedOrderItem.orderDetails.itemId, $scope.selectedOrderItem.productDetails.name, $scope.selectedOrderItem.orderDetails.quantity);
                            }
                        }
                    }
                }
                console.log("OrderItem on submitting inside modal",$scope.selectedOrderItem);
                $modalInstance.close($scope.selectedOrderItem);
            };

        }]);
