/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

angular.module('posApp')
    .controller('pullManagementCtrl', ['$rootScope', '$scope', 'AppUtil', '$location', '$http',
        'posAPI', '$modal',
        function ($rootScope, $scope, AppUtil, $location, $http, posAPI, $modal) {

            $scope.init = function () {
                AppUtil.validate();
                $scope.backToCover = AppUtil.backToCover;
                $scope.resultCount = 20;
                $scope.listLoading = false;
            }

            $scope.getPaymentModes = function () {
                var modes = [];
                if (AppUtil.getTransactionMetadata().paymentModes != null) {
                    AppUtil.getTransactionMetadata().paymentModes.forEach(function (v) {
                        if (v.generatePull) {
                            modes.push(v);
                        }
                    });
                    $scope.paymentModes = angular.copy(modes);
                    $scope.paymentModes.unshift({
                        id: 0,
                        name: "All",
                        description: "All",
                        type: "CASH",
                        settlementType: "DEBIT"
                    });
                    $scope.selectedPaymentMode = $scope.paymentModes[0];
                } else {
                    $location.url('/login');
                }
            }

            $scope.getPaymentModes();

            $scope.getPullPackets = function () {
                $scope.listLoading = true;
                var obj = {
                    unitId: $rootScope.globals.currentUser.unitId,
                    paymentModeId: $scope.selectedPaymentMode == null ? 1 : $scope.selectedPaymentMode.id,
                    resultCount: $scope.resultCount == null ? 20 : $scope.resultCount,
                    statusList: ["INITIATED"]
                }
                //var requestObj = AppUtil.GetRequest(obj);
                posAPI.allUrl('/',AppUtil.restUrls.cashManagement.getOpenPullsForUnit)
                    .post(obj).then(function (response) {
                    if (response != null) {
                        $scope.openPulls = response.plain();
                    }
                    $scope.listLoading = false;
                }, function (err) {
                    AppUtil.myAlert(err.data.errorMessage);
                    $scope.listLoading = false;
                });
            }

            $scope.getUsersForUnit = function () {
                posAPI.allUrl('/',AppUtil.restUrls.userManagement.allUsersForUnit)
                    .post($rootScope.globals.currentUser.unitId).then(function (response) {
                    if (response != null) {
                        $scope.usersForUnit = response.plain();
                    }
                }, function (err) {
                    AppUtil.myAlert(err.data.errorMessage);
                });
            }

            $scope.getUsersForUnit();

            $scope.validatePull = function (pull) {
                $scope.selectedPull = pull;
                $modal.open({
                    animation: true,
                    templateUrl: window.version+"scripts/modules/modals/validatePullModal.html",
                    controller: 'validatePullModalCtrl',
                    backdrop: 'static',
                    scope: $scope,
                    size: "lg"
                });
            }

            $scope.transferPull = function (pull) {
                bootbox.confirm("Are you sure you want to transfer this packet?", function(result){
                    if (result == true) {
                        $scope.selectedPull = pull;
                        //var requestObj = AppUtil.GetRequest($scope.selectedPull);
                        posAPI.allUrl('/',AppUtil.restUrls.cashManagement.transferPull)
                            .post($scope.selectedPull).then(function (response) {
                            $scope.getPullPackets();
                        }, function (err) {
                            AppUtil.myAlert(err.data.errorMessage);
                        });
                    }
                });
            }
        }]);


angular.module('posApp').controller('validatePullModalCtrl', ['$scope', '$modalInstance', 'AppUtil', 'posAPI', '$filter',
    function ($scope, $modalInstance, AppUtil, posAPI, $filter) {
    $scope.init = function () {
        $scope.editedDenomination = [];
        $scope.pullComment = null;
        $scope.createdByUser = null;
        $scope.witnessedByUser = null;
        $scope.denominationSum = 0;
        $scope.couponDenominations = null;
        $scope.loading = false;
        $scope.gettingCouponDenoms = false;
        if ($scope.selectedPull.pullAmount > 0 && $scope.selectedPull.pullDenominations.length == 0) {
            $scope.denominationSum = $scope.selectedPull.pullAmount;
        }
        if ($scope.selectedPull.paymentMode.type == "COUPON") {
            $scope.getCouponDenominations();
        }
        $scope.creatorPassword = null;
        $scope.creatorVerified = false;
        $scope.resetPullDenominations();
    }
    $scope.ok = function () {
        if (!$scope.validateAmount()) {
            alert("Amounts don't match!");
        } else if (!$scope.validUsers()) {
            alert("Created by field cannot be null and system!");
        } else if (!$scope.creatorVerified) {
            alert("Please verify creator using password!");
        } else {
            $scope.submitPull();
        }
    };
    $scope.cancel = function () {
        $modalInstance.dismiss("cancel");
    };

    $scope.updateTotal = function (denom) {
        denom.totalAmount = (denom.denominationDetail.bundleSize * denom.denominationDetail.denominationValue * denom.packetCount) +
            denom.denominationDetail.denominationValue * denom.looseCurrencyCount;
        $scope.addToEditedDenomination(denom);
    }

    $scope.validateAmount = function () {
        if ($scope.selectedPull.pullDenominations.length > 0) {
            var returnVal = false;
            if ($scope.selectedPull.pullAmount === $scope.denominationSum) {
                returnVal = true;
            }
            return returnVal;
        } else {
            return true;
        }
    }

    $scope.validUsers = function () {
        var returnVal = false;
        if ($scope.createdByUser != null && $scope.createdByUser.name != "System") {
            returnVal = true;
        }
        return returnVal;
    }

    $scope.submitPull = function () {
        $scope.filterEditedDenominations();
        $scope.selectedPull.pullDenominations = $scope.selectedPull.paymentMode.type == "COUPON" ? $scope.couponDenominations : $scope.editedDenomination;
        $scope.selectedPull.createdBy = $scope.createdByUser.id;
        $scope.selectedPull.witnessedBy = $scope.witnessedByUser;
        $scope.selectedPull.comment = $scope.pullComment;
        $scope.loading = true;
        $scope.verifyingCreator = false;
        //var requestObj = AppUtil.GetRequest($scope.selectedPull);
        posAPI.allUrl('/',AppUtil.restUrls.cashManagement.submitPull)
            .post($scope.selectedPull).then(function (response) {
            var error = false;
            if (response != null) {
                response = response.plain();
                if (response.pullPacketId === $scope.selectedPull.pullPacketId) {
                    AppUtil.mySuccessAlert("Pull with pull Id : " + $scope.selectedPull.pullPacketId + " validated successfully!")
                    $scope.getPullPackets();
                } else {
                    error = true;
                }
            } else {
                error = true;
            }
            if (error) {
                AppUtil.myAlert("Error in validating pull!");
            }
            $modalInstance.close();
        }, function (err) {
            AppUtil.myAlert(err.data.errorMessage);
            $modalInstance.close();
        });
    }

    $scope.addToEditedDenomination = function (denom) {
        var contains = false;
        $scope.editedDenomination.forEach(function (v) {
            if (v.denominationDetail.denominationId === denom.denominationDetail.denominationId) {
                v = denom;
                contains = true;
            }
        });
        if (!contains) {
            $scope.editedDenomination.push(denom);
        }
        $scope.sumDenominations();
    }

    $scope.sumDenominations = function () {
        $scope.denominationSum = 0;
        $scope.editedDenomination.forEach(function (v) {
            $scope.denominationSum += v.totalAmount;
        });
    }

    $scope.getCouponDenominations = function () {
        $scope.gettingCouponDenoms = true;
        //var requestObj = AppUtil.GetRequest({pullId: $scope.selectedPull.pullPacketId});
        posAPI.allUrl('/',AppUtil.restUrls.cashManagement.getCouponDenominations)
            .post({pullId: $scope.selectedPull.pullPacketId}).then(function (response) {
            if (response != null) {
                response = response.plain();
                $scope.couponDenominations = response;
                $scope.denominationSum = 0;
                $scope.couponDenominations.forEach(function (v) {
                    $scope.denominationSum += v.totalAmount;
                });
            }
            $scope.gettingCouponDenoms = false;
        }, function (err) {
            AppUtil.myAlert(err.data.errorMessage);
            $scope.gettingCouponDenoms = false;
        });
    }

    $scope.verifyCreator = function () {
        $scope.verifyingCreator = true;
        if ($scope.creatorPassword == null || $scope.creatorPassword == "") {
            alert("Please enter password.")
        } else {
            //var requestObj = AppUtil.GetRequest({userId: $scope.createdByUser.id, passcode: $scope.creatorPassword});
            posAPI.allUrl('/',AppUtil.restUrls.users.verify)
                .post({userId: $scope.createdByUser.id, password: $scope.creatorPassword}).then(function (response) {
                if (response == true) {
                    $scope.creatorVerified = true;
                }else{
                    $scope.creatorVerified = false;
                }
                $scope.verifyingCreator = false;
            }, function (err) {
                $scope.verifyingCreator = false;
                AppUtil.myAlert(err.data.errorMessage);
            });
        }
    }

    $scope.changeCreator = function () {
        $scope.creatorPassword = null;
        $scope.creatorVerified = false;
    }

    $scope.filterEditedDenominations = function () {
        for (var i = $scope.editedDenomination; i > 0; i--) {
            if ($scope.editedDenomination[i].packetCount <= 0 && $scope.editedDenomination[i].looseCurrencyCount <= 0) {
                $scope.editedDenomination.splice(i, 1);
            }
        }
    }

    $scope.resetPullDenominations = function () {
        $scope.selectedPull.pullDenominations.forEach(function (v) {
            v.packetCount = null;
            v.looseCurrencyCount = null;
            v.totalAmount = null;
        })
    }

}]);