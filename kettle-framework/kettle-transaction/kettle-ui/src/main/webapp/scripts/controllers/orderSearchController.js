/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

angular.module('posApp').controller('orderSearchController',
    ['$location', '$scope', '$rootScope', 'posAPI', 'AppUtil', '$modal', 'PrintService', 'subscriptionService','$window','$http',
        function ($location, $scope, $rootScope, posAPI, AppUtil, $modal, PrintService, subscriptionService,$window,$http) {
            $scope.searchText = null;
            $scope.externalOrderId = null;
            $scope.showCancel = AppUtil.showCancel;
            $scope.showUnsatisfiedCustomer = AppUtil.OrderObj != undefined && AppUtil.OrderObj != null && AppUtil.OrderObj.status != 'CANCELLED';
            $scope.showEditSettlement = AppUtil.showEditSettlement;
            $scope.isCOD = AppUtil.isCOD();
            $scope.dataLoading = false;
            $scope.currentUser = $rootScope.globals.currentUser;
            $scope.forceCancel = false;
            $scope.convertTimeFromIndex = subscriptionService.convertTimeFromIndex;
            $scope.convertWeekDayFromIndex = subscriptionService.convertWeekDayFromIndex;
            $scope.joinArray = subscriptionService.joinArray;
            $scope.getPreviousDate = subscriptionService.getPreviousDateTimestamp;
            $scope.isGiftCardOrder = true;
            $scope.orderSourceName = "";
            $scope.chaayosCashOrder = false;
            $scope.orderPaymentObject = [];
            $scope.invoiceDetail={};
            $scope.isGstClicked=false;
            $scope.unitDetails=AppUtil.getUnitDetails();
            $scope.isEmptyObject = function (obj) {
                ////console.log("inside is empty ::: "+obj);
                if (obj != undefined)
                    return AppUtil.isEmptyObject(obj);
                else
                    return true;
            };

            if (!AppUtil.isEmptyObject(AppUtil.OrderObj)) {
                $scope.OrderObj = AppUtil.OrderObj;
                $scope.OrderObj.brand = AppUtil.getBrandByBrandId($scope.OrderObj.brandId);
                AppUtil.OrderObj = {}; // clearing object from AppUtil
                $scope.searchText = $scope.OrderObj.generateOrderId;
                if ($scope.OrderObj.source == "COD") {
                    $scope.showCancel = AppUtil.unitFamily == "COD";
                } else if ($scope.OrderObj.source != "COD") {
                    $scope.showCancel = $scope.OrderObj.unitId == $rootScope.globals.currentUser.unitId;
                }

                checkOnlineModeCategory();
                setOrderSourceName();
            } else {
                $scope.OrderObj = {};
            }

            if ($scope.OrderObj != undefined) {
                $scope.settlement = AppUtil.getSettlements($scope.OrderObj);
            }

            if ($scope.OrderObj != undefined && $scope.OrderObj.orders != undefined) {
                for (var i = 0; i < $scope.OrderObj.orders.length; i++) {
                    if ($scope.OrderObj.orders[i].code != "GIFT_CARD") {
                        $scope.isGiftCardOrder = false;
                        break;
                    }
                }
            }

            if ($scope.OrderObj != undefined && $scope.OrderObj.orders != undefined) {
                $scope.chaayosCashOrder = $scope.OrderObj.offerCode == "CHAAYOS_CASH";
            }

            $rootScope.$watch('CustomerObj', function (newValue, oldValue) {
                $scope.CustomerObj = $rootScope.CustomerObj;
                if ($scope.OrderObj.source == "COD") {
                    $scope.showCancel = AppUtil.unitFamily == "COD";
                } else if ($scope.OrderObj.source != "COD") {
                    $scope.showCancel = $scope.OrderObj.unitId == $rootScope.globals.currentUser.unitId;
                }
                $scope.showLoading = false;
            });

            $scope.cancelDelivery = function (generatedOrderId) {
                //console.log("inside cancel delivery ticket function");
                bootbox.confirm("Are you sure that you want to cancel this delivery ticket?", function (result) {
                    if (result == true) {
                        var reqObj = AppUtil.GetRequest(generatedOrderId);
                        posAPI.allUrl('/', AppUtil.restUrls.delivery.cancel).all(generatedOrderId).post(reqObj)
                            .then(function (response) {
                                if (response != null) {
                                    response = response.plain();
                                    if (response.deliveryStatus == '-2' || response.deliveryStatus == -2 || response.failureCode != null) {
                                        AppUtil.myAlert(response.failureMessage);
                                    } else {
                                        $rootScope.deliveryObject = null;
                                        AppUtil.myAlert("Cancelled the delivery on this order");
                                    }

                                }
                            }, function (err) {
                                AppUtil.myAlert(err.data.errorMessage);
                            });
                    }
                });
            };

            ////console.log(AppUtil.showCancel);
            $scope.getOrderItems = function () {
                var orderItems = [];
                for (var i in $scope.OrderObj.orders) {
                    $scope.OrderObj.orders[i].isComboItem = false;
                    orderItems.push($scope.OrderObj.orders[i]);
                    if ($scope.OrderObj.orders[i].composition != null
                        && $scope.OrderObj.orders[i].composition.menuProducts != null
                        && $scope.OrderObj.orders[i].composition.menuProducts.length > 0) {
                        for (var j in $scope.OrderObj.orders[i].composition.menuProducts) {
                            $scope.OrderObj.orders[i].composition.menuProducts[j].isComboItem = true;
                            orderItems.push($scope.OrderObj.orders[i].composition.menuProducts[j]);
                        }
                    }
                }
                return orderItems;

            };

            $scope.unsatisfiedCustomerOrder = function () {

                // //console.log('start');

                //if($scope.OrderObj.source = 'CAFE'){
                if (!AppUtil.isCOD() && ($scope.OrderObj.source == 'CAFE' || $scope.OrderObj.source == 'TAKE_AWAY')) {
                    AppUtil.getUnitProductsData();
                    $rootScope.orderType = "unsatisfied-customer-order";
                    $rootScope.orderIdforUnsatisfiedOrder = $scope.OrderObj.orderId;
                    $location.url('/pos');
                    //}else if($scope.OrderObj.source = 'COD'){
                } else if (AppUtil.isCOD() && $scope.OrderObj.source == 'COD') {
                    AppUtil.freeKettle = false;
                    $rootScope.orderType = "unsatisfied-customer-order";
                    $rootScope.isPartnerOrder = false;
                    $rootScope.orderIdforUnsatisfiedOrder = $scope.OrderObj.orderId;
                    $location.url('/CODCSlookup');
                }
            };

            $scope.getDeliveryTime = function (orderId) {
                $rootScope.showFullScreenLoader = true;
                posAPI.allUrl('/', AppUtil.restUrls.partnerOrder.getSwiggyRiderTimeOfArrival).post(orderId)
                    .then(function (response) {
                        if (response.plain() != null) {
                            bootbox.alert("Delivery time is " + response.plain() + " minutes");
                        } else {
                            bootbox.alert("Error getting delivery time. Try again later!");
                        }
                        $rootScope.showFullScreenLoader = false;
                    }, function (err) {
                        AppUtil.myAlert(err.data.errorMessage);
                        $rootScope.showFullScreenLoader = false;
                    });
            };

            $scope.getDeliveryStatusData = function (orderId) {
                $rootScope.showFullScreenLoader = true;
                posAPI.allUrl('/', AppUtil.restUrls.partnerMetadataManagement.getDeliveryStatus).post(orderId)
                    .then(function (response) {
                        $scope.deliveryStatus = response.plain();
                        if ($scope.deliveryStatus != null && $scope.deliveryStatus.length > 0) {
                            $scope.deliveryStatusModalOpen();
                        } else {
                            bootbox.alert("Delivery Status not available yet.")
                        }
                        $rootScope.showFullScreenLoader = false;
                    }, function (err) {
                        AppUtil.myAlert(err.data.errorMessage);
                        $rootScope.showFullScreenLoader = false;
                    });
            };

            $scope.deliveryStatusModalOpen = function () {
                $modal.open({
                    animation: true,
                    templateUrl: window.version + 'views/deliveryStatusModal.html',
                    controller: 'ModalInstanceCtrl',
                    scope: $scope,
                    size: 'lg'
                });
            };

            $scope.GetSearchedOrder = function () {
                $scope.orderPaymentObject = [];
                $scope.externalOrderId = null;
                $scope.forceCancel = false;
                //var reqObj = AppUtil.GetRequest($scope.searchText);
                $rootScope.showFullScreenLoader = true;
                console.log("206   "+$scope.searchText);
                $scope.invoiceDetail.orderId=$scope.searchText;
                $scope.invoiceDetail.generatedBy=$scope.currentUser.userName;
                console.log( $scope.currentUser);
                posAPI.allUrl('/', AppUtil.restUrls.order.generatedOrder).post($scope.searchText)
                    .then(function (response) {
                        $scope.OrderObj = {};
                        if (!angular.isUndefined(response.errorType)) {
                            AppUtil.myAlert(response.errorMessage);
                            $scope.orderSourceName = "";
                        } else {
                            $scope.OrderObj = response.plain();
                            $scope.OrderObj.brand = AppUtil.getBrandByBrandId($scope.OrderObj.brandId);
                            setOrderSourceName();
                            $scope.showEditSettlement = (($scope.OrderObj.source == 'CAFE' || $scope.OrderObj.source == 'TAKE_AWAY') && $scope.OrderObj.orderType == 'order' && $scope.OrderObj.status == 'SETTLED');

                            if ($scope.OrderObj.source == "COD") {
                                $scope.showCancel = AppUtil.unitFamily == "COD";
                            } else if ($scope.OrderObj.source != "COD") {
                                $scope.showCancel = $scope.OrderObj.unitId == $rootScope.globals.currentUser.unitId;
                            }

                            //not filling AppUtil here because of check in back function
                            if ($scope.OrderObj.deliveryAddress != null && $scope.OrderObj.deliveryAddress > 0) {
                                $scope.GetCustomerInfo($scope.OrderObj.deliveryAddress);
                            } else if (!AppUtil.isEmptyObject($scope.CustomerObj)) {
                                $scope.CustomerObj = {};
                            }
                            $scope.settlement = AppUtil.getSettlements($scope.OrderObj);
                            checkOnlineModeCategory();
                        }
                        $rootScope.showFullScreenLoader = false;
                    }, function (err) {
                        AppUtil.myAlert(err.data.errorMessage);
                        $rootScope.showFullScreenLoader = false;

                    });
            };

            $scope.GetSearchedGst = function () {
                var date = new Date($scope.OrderObj.billingServerTime);
                var month = date.getMonth();
                var year = date.getFullYear();
                var fy = year % 2000;
                if (month > 2) {
                    fy = fy + 1;
                }

                var financialYearEndDate = new Date(2000 + fy, 8, 30);
                var financialYearStartDate = new Date(2000 + fy - 1, 3, 1);

                var currentDate = new Date();
                if (currentDate >= financialYearStartDate && currentDate <= financialYearEndDate) {
                    $scope.isGstClicked = true;
                    console.log($scope.isGstClicked);
                    window.open('https://services.gst.gov.in/services/searchtp', '_blank');
                }

                else {
                    var monthNames = ["January", "February", "March", "April", "May", "June",
                        "July", "August", "September", "October", "November", "December"
                    ];
                    bootbox.alert("Exceeded the date to issue invoice bill, could be filed between " + financialYearStartDate.getDate() + " " + monthNames[financialYearStartDate.getMonth()] + " " + financialYearStartDate.getFullYear() + " and " + financialYearEndDate.getDate() + " " + monthNames[financialYearEndDate.getMonth()] + " " + financialYearEndDate.getFullYear());
                }

            }

            $scope.addInvoiceDialog = function () {
                $scope.isAddNewOffer = true;
            }

            $scope.addCrmScreenDetail = function (invoiceDetail) {
                if (!invoiceDetail.companyName) {
                    alert("please enter company  Name")
                    return;
                }
                if (!invoiceDetail.companyAddress) {
                    alert("please enter company Address")
                    return;
                }
                if (!invoiceDetail.gst) {
                    alert("please enter gst")
                    return;
                }
                if (!invoiceDetail.orderId) {
                    alert("please enter orderId")
                    return;
                }
                if (!invoiceDetail.generatedBy) {
                    alert("please enter generator's name")
                    return;
                }

                invoiceDetail.orderId=$scope.searchText;
                console.log(invoiceDetail.orderId);
                console.log("Unit Details",$scope.unitDetails);
                var invoiceDetail = {
                   companyName: invoiceDetail.companyName,
                   companyAddress: invoiceDetail.companyAddress,
                    gst: invoiceDetail.gst,
                    orderId: invoiceDetail.orderId,
                    generatedBy:  invoiceDetail.generatedBy,
                    stateCode: $scope.unitDetails.location.state.code,
                };

                console.log(invoiceDetail);
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.order.addInvoiceDetails,
                    data: invoiceDetail
                }).then(function success(response) {
                    if (response.status === 200 && response.data.length==0) {
                        console.log(response);
                        alert("Exceeded the date to issue invoice bill");
                        $rootScope.showFullScreenLoader = false;
                    }
                    else if(response.status === 200){
                        console.log(response);
                        alert("Added Successfully");
                        $rootScope.showFullScreenLoader = false;
                    }

                    else {
                        alert("Error to Add Data!!!");
                        $rootScope.showFullScreenLoader = false;
                    }
                }, function error(response) {
                    alert("Error to Add Data!!!");
                    $rootScope.showFullScreenLoader = false;
                });
                $scope.invoiceDetail={};
            }

            function checkOnlineModeCategory() {
                for (var i = 0; i < $scope.OrderObj.settlements.length; i++) {
                    if ($scope.OrderObj.settlements[i].modeDetail.category == 'ONLINE') {
                        getPaymentStatus();
                    }
                }
            }

            function getPaymentStatus() {

                $rootScope.showFullScreenLoader = true;

                posAPI.allUrl('/', AppUtil.restUrls.order.getPaymentStatus).post($scope.OrderObj.orderId)
                    .then(function (response) {
                        console.log('response getPaymentStatus ', response);
                        $scope.orderPaymentObject.push(response);
                        $rootScope.showFullScreenLoader = false;
                    }, function (err) {
                        AppUtil.myAlert(err.data.errorMessage);
                        $rootScope.showFullScreenLoader = false;

                    });
            }

            $scope.GetPartnerSearchedOrder = function () {
                $scope.searchText = null;
                $scope.forceCancel = false;
                var reqObj = {
                    id: 6,
                    name: 'SWIGGY',
                    code: $scope.externalOrderId
                }
                $rootScope.showFullScreenLoader = true;
                posAPI.allUrl('/', AppUtil.restUrls.order.partnerOrder).post(reqObj)
                    .then(function (response) {
                        $scope.OrderObj = {};
                        if (!angular.isUndefined(response.errorType)) {
                            AppUtil.myAlert(response.errorMessage);
                            $scope.orderSourceName = "";
                        } else {
                            $scope.OrderObj = response.plain();
                            $scope.OrderObj.brand = AppUtil.getBrandByBrandId($scope.OrderObj.brandId);
                            //console.log("$scope.OrderObj",$scope.OrderObj);
                            setOrderSourceName();
                            $scope.showEditSettlement = (($scope.OrderObj.source == 'CAFE' || $scope.OrderObj.source == 'TAKE_AWAY') && $scope.OrderObj.orderType == 'order' && $scope.OrderObj.status == 'SETTLED');

                            if ($scope.OrderObj.source == "COD") {
                                $scope.showCancel = AppUtil.unitFamily == "COD";
                            } else if ($scope.OrderObj.source != "COD") {
                                $scope.showCancel = $scope.OrderObj.unitId == $rootScope.globals.currentUser.unitId;
                            }

                            //not filling AppUtil here because of check in back function
                            if ($scope.OrderObj.deliveryAddress != null && $scope.OrderObj.deliveryAddress > 0) {
                                $scope.GetCustomerInfo($scope.OrderObj.deliveryAddress);
                            } else if (!AppUtil.isEmptyObject($scope.CustomerObj)) {
                                $scope.CustomerObj = {};
                            }
                            $scope.settlement = AppUtil.getSettlements($scope.OrderObj);
                        }
                        $rootScope.showFullScreenLoader = false;
                    }, function (err) {
                        AppUtil.myAlert(err.data.errorMessage);
                        $rootScope.showFullScreenLoader = false;

                    });
            };

            function setOrderSourceName() {
                for (var i = 0; i < AppUtil.getTransactionMetadata().channelPartner.length; i++) {
                    if (AppUtil.getTransactionMetadata().channelPartner[i].id == $scope.OrderObj.channelPartner) {
                        $scope.orderSourceName = AppUtil.getTransactionMetadata().channelPartner[i].name;
                        break;
                    } else if ($scope.OrderObj.channelPartner == 1) {
                        $scope.orderSourceName = "Chaayos (Dine In)";
                        break;
                    }
                }
            }

            $scope.GetCustomerInfo = function ($deliveryAddressId) {
                //var reqObj = AppUtil.GetRequest($deliveryAddressId+"");
                posAPI.allUrl('/', AppUtil.restUrls.customer.lookupAddress).post($deliveryAddressId)
                    .then(function (response) {
                        //console.log(response);
                        $scope.CustomerObj = response.plain();
                        $scope.CustomerObj.addresses.map(function (address) {
                            if (address.id == $deliveryAddressId) {
                                $scope.deliveryAddress = address;
                            }
                        });
                        $rootScope.showFullScreenLoader = false;
                    }, function (err) {
                        AppUtil.myAlert(err.data.errorMessage);
                        $rootScope.showFullScreenLoader = false;
                    });
            };


            $scope.cancelOrderModalOpen = function (cancelForcibly) {
                if ($scope.OrderObj.orders[0].productName == "GYFTR") {
                    AppUtil.myAlert("GYFTR voucher order cannot be cancelled !");
                    return false;
                }

                if($scope.OrderObj.orders[0].productId == 3){
                    AppUtil.myAlert("Advance Payment order cannot be cancelled !");
                    return false;
                }

                $scope.forceCancel = cancelForcibly;
                var cancelRequested = $scope.OrderObj.status != "CANCELLED_REQUESTED";
                var cancelled = $scope.OrderObj.status !== "CANCELLED";
                var isCOD = AppUtil.isCOD();
                var isTA = AppUtil.isTakeaway();
                var openModal = false;
                if (!isCOD && !isTA) {
                    if (cancelled) {
                        var openModal = true;
                    }
                } else {
                    if (cancelled && cancelRequested) {
                        var openModal = true;
                    }
                }

                if (openModal) {
                    $modal.open({
                        animation: true,
                        templateUrl: window.version + 'views/cancelOrderModal.html',
                        controller: 'ModalInstanceCtrl',
                        scope: $scope,
                        size: 'md'
                    });
                } else {
                    if (!isCOD && !isTA)
                        AppUtil.myAlert('Order is already cancelled');
                    else
                        AppUtil.myAlert('Order cancellation is already requested');
                }
            };
            /**
             * Reprint with authentication
             */
            /*	$scope.reprint = function (type) {
                    if (!$scope.isEmptyObject($scope.OrderObj)) {
                        ////console.log("Print Count :"+$scope.OrderObj.printCount);
                        if($scope.OrderObj.printCount < 2   || type=='KOT'){
                            var modalInstance = $modal.open({
                                animation: true,
                                templateUrl: window.version+'views/loginModal.html',
                                controller: 'LoginModalCtrl',
                                backdrop: 'static',
                                size: 'sm',
                                resolve: {
                                    order: function () {
                                        var orderObject;
                                        if(type=='KOT'){
                                            orderObject	= {
                                                    text:$scope.searchText,
                                                    isReprintKOT:true
                                            };
                                        }else{
                                            orderObject	= {
                                                    text:$scope.searchText,
                                                    isReprint:true
                                            };
                                        }
                                        return orderObject;
                                    },
                                    orderItemArray: function(){
                                        return [];
                                    },
                                    employeeMealUser : function(){
                                        return null;
                                    }
                                }
                            });

                            modalInstance.result.then(function () {
                                $scope.GetSearchedOrder();
                            }, function (err) {

                            });
                        } else {
                            AppUtil.myAlert('Reprints for orders are limited to only once');
                        }

                    } else {
                        AppUtil.myAlert('Please fill a valid order id');
                    }

                };*/

            $scope.reprint = function (type) {
                if (!$scope.isEmptyObject($scope.OrderObj)) {
                    ////console.log("Print Count :"+$scope.OrderObj.printCount);
                    if ($scope.OrderObj.printCount < 2 || type == 'KOT') {
                        $rootScope.showFullScreenLoader = true;
                        if (type == 'KOT') {
                            AppUtil.reprintOrderKOT($scope.searchText);
                        } else {
                            AppUtil.reprintOrder($scope.searchText).then(function () {
                                $scope.GetSearchedOrder();
                            });
                        }
                    } else {
                        AppUtil.myAlert('Reprints for orders are limited to only once');
                    }

                } else {
                    AppUtil.myAlert('Please fill a valid order id');
                }

            };

            $scope.reprintSettlementSlip = function () {
                if (!$scope.isEmptyObject($scope.OrderObj)) {
                    $rootScope.showFullScreenLoader = true;
                    AppUtil.reprintSettlementSlip($scope.OrderObj.tableRequestId).then(function () {
                        AppUtil.mySuccessAlert("Settlement Reprint Successful.")
                    });
                } else {
                    AppUtil.myAlert('Please fill a valid order id');
                }
            };

            $scope.resendEmail = function () {
                if (!$scope.isEmptyObject($scope.OrderObj)) {
                    $modal.open({
                        animation: true,
                        templateUrl: window.version + 'views/loginModal.html',
                        controller: 'LoginModalCtrl',
                        backdrop: 'static',
                        size: 'sm',
                        resolve: {
                            order: function () {
                                var orderObject = {
                                    text: $scope.searchText,
                                    isReprint: false
                                };
                                return orderObject;
                            },
                            orderItemArray: function () {
                                return [];
                            },
                            employeeMealUser: function () {
                                return null;
                            }
                        }
                    });
                } else {
                    AppUtil.myAlert('Please fill a valid order id');
                }
            };

            $scope.backToCover = function () {
                if (AppUtil.previousLocation[AppUtil.previousLocation.length - 2] == '/orderSummary') {
                    $location.url('/orderSummary');
                } else if (AppUtil.previousLocation[AppUtil.previousLocation.length - 2] == '/customerOrders') {
                    $location.url('/customerOrders');
                } else if (AppUtil.previousLocation[AppUtil.previousLocation.length - 2] == '/openOrderSummary') {
                    $location.url('/openOrderSummary');
                } else if (AppUtil.previousLocation[AppUtil.previousLocation.length - 2] == '/subscriptionSearch') {
                    $location.url('/subscriptionSearch');
                } else if (AppUtil.previousLocation[AppUtil.previousLocation.length - 2] == '/subscriptionOrderSearch') {
                    $location.url('/subscriptionOrderSearch');
                } else if (AppUtil.previousLocation[AppUtil.previousLocation.length - 2] == '/subscriptionOrderByUnit') {
                    $location.url('/subscriptionOrderByUnit');
                } else if (AppUtil.previousLocation[AppUtil.previousLocation.length - 2] == '/openTakeawayOrders') {
                    $location.url('/openTakeawayOrders');
                } else if (AppUtil.previousLocation[AppUtil.previousLocation.length - 2] == '/tableSummary') {
                    $location.url('/tableSummary');
                } else {
                    if (!AppUtil.isCOD()) {
                        $location.url('/cover');
                    } else {
                        $location.url('/CODCover');
                    }
                }
            };

            $scope.getAddress = function () {
                return AppUtil.arrangeAddress($scope.deliveryAddress, true);
            };

            $scope.editSettlement = function (settlement) {
                if (!$scope.isEmptyObject($scope.OrderObj)) {
                    var modalInstance = $modal.open({
                        animation: true,
                        templateUrl: 'views/editSettlementModal.html',
                        controller: 'editSettlementModalCtrl',
                        backdrop: 'static',
                        size: 'sm',
                        resolve: {
                            orderUnitId: function () {
                                return $scope.OrderObj.unitId;
                            },
                            orderDataId: function () {
                                return $scope.OrderObj.orderId;
                            },
                            settlementData: function () {
                                return settlement;
                            },
                            orderSource: function () {
                                $scope.OrderObj.source
                            }

                        }
                    });
                    modalInstance.result.then(function () {
                        $scope.GetSearchedOrder();
                    }, function (err) {

                    });
                } else {
                    AppUtil.myAlert('Please fill a valid order id');
                }
            }

            $scope.addDeliveryDetails=function () {
                var url=AppUtil.getDeliveryDetails()+"unit="+encodeURIComponent($scope.OrderObj.unitName)+"&order="+$scope.OrderObj.generateOrderId;
                $window.open(url,'_blank');
            };

            $scope.removeFromAssembly = function (orderObj) {
                $rootScope.showFullScreenLoader = true;
                posAPI.allUrl('/', AppUtil.restUrls.order.removeFromCache).customGET(orderObj.generateOrderId, {})
                    .then(function (response) {
                        $rootScope.showFullScreenLoader = false;
                    }, function (err) {
                        $rootScope.showFullScreenLoader = false;
                        AppUtil.myAlert(err.data.errorMessage);
                    });
            }

        }]);