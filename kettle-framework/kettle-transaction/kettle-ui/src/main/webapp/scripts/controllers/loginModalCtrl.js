/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

angular
    .module('posApp')
    .controller(
    'LoginModalCtrl',
    ['$scope', '$modalInstance', 'posAPI', 'productService', 'PrintService',
        'AppUtil', 'AuthenticationService', 'order', '$location','orderItemArray','$rootScope',
    function ($scope, $modalInstance, posAPI, productService, PrintService,
              AppUtil, AuthenticationService, order, $location,orderItemArray,$rootScope) {
        // //console.log($scope);O

        $scope.dataLoading = false;
        $scope.usercode = null;
        $scope.pwd = null;
        $scope.reprintOrderComment = null;
        $scope.hasReasonCode = false;

        function isOrderObject() {
            //reprint && resend
            if(order.hasOwnProperty('isReprint') || order.hasOwnProperty('isReprintKOT'))
            {
                $scope.hasReasonCode = false;
                return false;
            } else {
                //emplyoee meal
                $scope.hasReasonCode = true;
                return true;

            }
        }

        function authenticate() {

            $scope.dataLoading = true;

            var currentUserCode = $rootScope.globals.currentUser.userId;
            var currentUnitId = $rootScope.globals.currentUser.unitId;
            var currentDesignation = $rootScope.globals.currentUser.designation.name;
            var currentTerminalId = $rootScope.globals.currentUser.terminalId;
            var currentScreenType =  $rootScope.globals.currentUser.screenType;
            var unitFamily = $rootScope.globals.currentUser.unitFamily;
            
            //console.log($scope.usercode, $scope.pwd, currentDesignation, isOrderObject(), currentUserCode);
            
            if (currentUserCode == $scope.usercode) {
                if (isOrderObject() == false) {
                	//console.log("inside resend email call :::: "+AppUtil.isCOD);
                	$scope.dataLoading = false;
                	if(AppUtil.isCOD() || unitFamily == 'COD'){
                		if(order.isReprint){
                            reprintOrder(order);
                        }  else if(order.isReprintKOT){
                        	reprintOrderKOT(order);
                        }else {
                            resendEmail(order);
                        }
                	}else{
                		//console.log("currentDesignation ::: "+currentDesignation);
                		if (currentDesignation.indexOf("Manager")!=-1) {
                            
                            if(order.isReprint){
                                reprintOrder(order);
                            } else if(order.isReprintKOT){
                            	reprintOrderKOT(order);
                            } else {
                                resendEmail(order);
                            }
                        } else {
                            $scope.dataLoading = false;
                            AppUtil.myAlert('You don\'t have permission to access this function');
                        }
                	}
                    
                } else {
                    //for emplyoee meal
                    $scope.dataLoading = false;
                    $modalInstance.dismiss('submit');
                    order.orders = AppUtil.addOrderItems(orderItemArray);
                    AppUtil.sendOrder(order,$modalInstance,$scope.goBack,false);
                }
            } else {
                AuthenticationService.Login($scope.usercode, $scope.pwd, currentUnitId,currentTerminalId,currentScreenType, function (response) {
                    $scope.dataLoading = false;
                    //console.log(response.plain());
                    if (response.sessionKeyId == null) {
                        $modalInstance.dismiss('submit');
                        AppUtil.myAlert('Username & Passcode don\'t match');
                    } else {
                        $modalInstance.dismiss('submit');
                        if (isOrderObject()) {
                            for (var k = 0; k < order.orders.length; k++) {
                                if (order.orders[k].complimentaryDetail.isComplimentary == true && order.orders[k].complimentaryDetail.reasonCode == 2100) {
                                    order.orders[k].complimentaryDetail.reason = "Employee meal for" + $scope.usercode;
                                }
                            }
                        }
                        var userObj = {
                            userId: $scope.usercode,
                            unitId: currentUnitId,
                            sessionKeyId: response.sessionKeyId
                        };
                        Logout(userObj);
                        
                        if (isOrderObject() == false) {
                        	//console.log("inside resend email call :::: "+AppUtil.isCOD);
                        	if(AppUtil.isCOD() || unitFamily == 'COD'){
                        		if(order.isReprint){
                                    reprintOrder(order);
                                } else if(order.isReprintKOT){
                                	reprintOrderKOT(order);
                                }else {
                                    resendEmail(order);
                                }
                        	}else{
                        		if (response.user.designation.name == "Manager") {
                                    if(order.isReprint){
                                        reprintOrder(order);
                                    } else if(order.isReprintKOT){
                                    	reprintOrderKOT(order);
                                    } else {
                                        resendEmail(order);
                                    }
                                } else {
                                    AppUtil.myAlert('You don\'t have permission to access this function');
                                }	
                        	}
                            
                        } else {
                            //console.log("before if employee meal",order);
                            order.orders = AppUtil.addOrderItems(orderItemArray);
                            //console.log("if employee meal",order);
                            AppUtil.sendOrder(order,$modalInstance,$scope.goBack,false);
                        }


                    }
                }, function (err) {
                    $scope.dataLoading = false;
                    $modalInstance.dismiss('submit');
                    AppUtil.myAlert(err.data.errorMessage);
                });
            }

        }

        function reprintOrder(order) {
            if(!$scope.LoginModalForm.comment.$error.required){
                var reqObj = {
                    generatedOrderId: order.text,
                    approvedBy: $scope.usercode,
                    reason: $scope.reprintOrderComment
                };
                //console.log(reqObj);
                posAPI.allUrl('/',AppUtil.restUrls.order.reprintOrder).post(AppUtil.GetRequest(reqObj)).then(function (response) {
                        var receipt = response.plain();

                        /*//console.log((receipt.printString));*/

                        PrintService.printHTMLOnBilling(receipt.printString);


                    }, function (err) {
                        //console.log(err);
                        AppUtil.myAlert(err.data.errorMessage);
                    });
                $modalInstance.close('submit');
            } else {
                AppUtil.myAlert("Comment is required");
            }

        }
        
        
        function reprintOrderKOT(order) {
            if(!$scope.LoginModalForm.comment.$error.required){
                var reqObj = {
                    generatedOrderId: order.text,
                    approvedBy: $scope.usercode,
                    reason: $scope.reprintOrderComment
                };
                //console.log(reqObj);
                posAPI.allUrl('/',AppUtil.restUrls.order.reprintOrderKOT).post(AppUtil.GetRequest(reqObj)).then(function (response) {
                        var receiptList = response.plain();

                       // console.log("receiptList",receiptList);
                        for(var i=0;i<receiptList.length;i++){
                        	 PrintService.printHTMLOnKot(receiptList[i].printString);
                        }
                    }, function (err) {
                        //console.log(err);
                        AppUtil.myAlert(err.data.errorMessage);
                    });
                $modalInstance.close('submit');
            } else {
                AppUtil.myAlert("Comment is required");
            }

        }

        function resendEmail(order) {
            if(!$scope.LoginModalForm.comment.$error.required){
                var reqObj = {
                    generatedOrderId: order.text,
                    approvedBy: $scope.usercode,
                    reason: $scope.reprintOrderComment
                };
                //console.log(reqObj);
                posAPI.allUrl('/',AppUtil.restUrls.order.emailOrder).post(AppUtil.GetRequest(reqObj)).then(function (response) {
                        if(response){
                            AppUtil.mySuccessAlert('Email sent successfully');
                        } else {
                            AppUtil.myAlert('Email couldn\'t be resend');
                        }
                    }, function (err) {
                        //console.log(err);
                        AppUtil.myAlert(err.data.errorMessage);
                    });
                $modalInstance.close('submit');
            } else {
                AppUtil.myAlert("Comment is required");
            }

        }

        function cancel() {
            $modalInstance.dismiss('submit');
        }

        function Logout(userObj) {

            posAPI.allUrl('/',AppUtil.restUrls.users.logout).post(userObj)
                .then(function (response) {
                    //console.log('logged out');

                }, function (err) {
                    AppUtil.myAlert(err.data.errorMessage);
                });
        }

        
        $scope.goBack = function () {
            productService.clearOrderArray();
            $location.url('/cover');
        };

        $scope.authenticate = authenticate;
        $scope.cancel = cancel;

    }]);
