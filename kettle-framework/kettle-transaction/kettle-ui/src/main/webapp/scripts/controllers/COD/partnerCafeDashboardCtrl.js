/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';


angular.module('posApp').controller(
    'partnerCafeDashboardCtrl',
    ['$location', '$scope', 'posAPI', 'AppUtil', '$rootScope', '$timeout',
        function ($location, $scope, posAPI, AppUtil, $rootScope, $timeout) {

            console.log("WELCOME TO CHAAYOS");

            $scope.backToCODCover = function () {
                if ($scope.isCOD) {
                    $location.url('/CODCover');
                } else {
                    $location.url('/cover');
                }
            };

            $scope.init = function () {
                $scope.getCafeStatus();
                $scope.cafeList= [];
                $scope.orderList = [];
                $scope.isCOD = $rootScope.globals.currentUser.unitFamily === "COD";
                $scope.playFile();
            };

            $scope.getCafeStatus =function (){
                $scope.cafeMap ={};

                posAPI.allUrl('/', AppUtil.restUrls.cafeLookUp.getCafeStatusFromChannelPartner).customGET()
                    .then(function (response) {
                        console.log("Get Cafe Status");
                        console.log(response);
                        console.log(response.cafeStatusData.length);

                        if (response) {
                            response.cafeStatusData.forEach(function (data){
                                var obj = {};
                                obj['id'] = data.id;
                                obj['unitId'] = data.unitId;
                                obj['unitName'] = data.unitName;
                                obj['unitCity'] = data.unitCity;
                                obj['channelPartner'] = data.channelPartner;
                                obj['partnerId'] = data.partnerId;
                                obj['brandId'] = data.brandId;
                                obj['brandName'] = data.brandName;
                                obj['statusUpdate'] = data.statusUpdate;
                                obj['lastUpdatedTime'] = data.lastUpdatedTime;
                                obj['empId'] = data.empId;
                                obj['applicationSource'] = data.applicationSource;
                                console.log("Added Data to List");
                                $scope.cafeList.push(obj);
                                console.log($scope.cafeList);
                            });
                            console.log('response is not null');
                        }
                    }, function (err) {
                        console.log('Error in getting response', err);
                        $rootScope.showFullScreenLoader = false;
                });
            }

            $scope.refreshCafeStatus = function (clicked) {
                if (clicked != undefined && clicked) {
                    $scope.init();
                }
            };


            $scope.playFile = function () {
                $rootScope.partnerOrderSound = $timeout(function () {
                    if ($location.url() == "/partnerOrderDashboard") {
                        if ($scope.cafeList != null && $scope.cafeList.length > 0) {
                            if (AppUtil.isAndroid) {
                                Android.playSound();
                            } else {
                                play_interval_sound();
                            }
                        }
                    }
                    $scope.playFile();
                }, 60000);
            };


            $scope.activateUnitUrl=function (cafe){
                $rootScope.showFullScreenLoader = true;
                posAPI.allUrl('/', AppUtil.restUrls.partnerMetadataManagement.unitToggleCafeDashboard + "?id=" + cafe.id +  "&partnerId="  +cafe.partnerId + "&unitId=" + cafe.unitId + "&brandId=" + cafe.brandId )
                    .post().then(function (response) {
                        console.log("Entered activate Url Function")
                        console.log(response);
                    if (response != null && response == true) {
                        AppUtil.mySuccessAlert("Unit " + cafe.unitId +" Activated Successfully!");
                        $scope.refreshCafeStatus(true);
                    }
                    if(typeof response == 'undefined'){
                        AppUtil.myAlert("Error in Activating Unit "+cafe.unitId);
                    }

                    $rootScope.showFullScreenLoader = false;
                }, function (err) {
                    AppUtil.myWarningAlert("Error in Activating Unit"+cafe.unitId);
                    console.log('Error in getting response', err);
                    $rootScope.showFullScreenLoader = false;
                });
            };




        }]);








