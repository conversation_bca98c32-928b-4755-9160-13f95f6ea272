/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */
(function () {
    'use strict';

    angular
        .module('posApp')
        .controller(
            'addOnNewModalInstanceCtrl',
            ['$scope', '$modalInstance', 'orderItem', '$filter', 'AppUtil', 'productService', 'customerObj',
                function ($scope, $modalInstance, orderItem, $filter, AppUtil, productService, customerObj) {
                    //console.log('Item at the time of opening the modal',
                    //orderItem);


                    $scope.initCustomization = function () {
                        $scope.customerObj = customerObj;
                        $scope.orderItem = orderItem;
                        $scope.isFavChai= $scope.orderItem.isFavChaiMarked;
                        $scope.orderItem.isHeartButtonClicked=undefined;
                        $scope.chaiSavedFromDineIn = $scope.orderItem.chaiSavedFromDineIn;
                        $scope.checkIsHeartButtonClicked();
                        $scope.selectedOrderItem = orderItem;
                        $scope.selectedAddonsList = [];
                        $scope.selectedOptionsList = [];
                        $scope.selectedVariants = [];
                        $scope.selectedProductsMap = {};
                        $scope.customizationsList =[];
                        $scope.currentCustomizationList=[];
                        $scope.getAllComplimentaryCodes = AppUtil.getAllComplimentaryCodes;
                        $scope.complimentaryDetail = $scope.selectedOrderItem.orderDetails.complimentaryDetail.isComplimentary ? $scope.selectedOrderItem.orderDetails.complimentaryDetail
                            : {
                                reasonCode: null,
                                isComplimentary: null,
                                reason: null
                            };

                        if($scope.orderItem.productDetails.type==5){ //for hot beverages
                            $scope.initCustomizations($scope.orderItem);
                        }

                        if (($scope.selectedOrderItem.isModified == undefined || !$scope.selectedOrderItem.isModified) && $scope.selectedOrderItem.productDetails.type !=5) {
                            $scope.selectedOrderItem.recipeDetails = AppUtil
                                .resetOrderItemCustomization($scope.selectedOrderItem.recipeDetails);
                        }

                        if ($scope.selectedOrderItem.orderDetails.isCombo && $scope.selectedOrderItem.recipeDetails != null) {

                            for (var index in $scope.selectedOrderItem.recipeDetails.ingredient.compositeProduct.details) {
                                var itemDetails = $scope.selectedOrderItem.recipeDetails.ingredient.compositeProduct.details[index];
                                if (itemDetails.menuProducts.filter(function (meuProduct) {
                                    return meuProduct.selected;
                                }).length == 0) {
                                    itemDetails.menuProducts[0].selected = true;
                                }
                            }
                        }

                    };

                    $scope.initCustomizations = function (orderItem) {
                        if(orderItem.orderDetails.composition.addons!=null && orderItem.orderDetails.composition.addons.length>0){
                            for(var i =0;i<orderItem.orderDetails.composition.addons.length;i++){
                                $scope.customizationsList.push(orderItem.orderDetails.composition.addons[i].product.name);
                            }
                        }

                        if (orderItem.orderDetails.composition.variants != null && orderItem.orderDetails.composition.variants.length > 0) {
                            for (var i = 0; i < orderItem.orderDetails.composition.variants.length; i++) {
                                if (orderItem.orderDetails.composition.variants[i].selected){
                                    $scope.customizationsList.push(orderItem.orderDetails.composition.variants[i].alias);
                                }
                            }
                        }
                        if(orderItem.orderDetails.composition.options !=null && orderItem.orderDetails.composition.options.length>0){
                            for(var i =0;i<orderItem.orderDetails.composition.options.length;i++){
                                $scope.customizationsList.push(orderItem.orderDetails.composition.options[i]);
                            }
                        }

                        if (orderItem.recipeDetails.ingredient != null && orderItem.recipeDetails.ingredient.products != null && orderItem.recipeDetails.ingredient.products.length > 0) {
                            orderItem.recipeDetails.ingredient.products.map(function (product) {
                                product.details.map(function (detail) {
                                    if (detail.selected == true) {
                                        $scope.selectedProductsMap[product.display] = detail.product.productId;
                                        $scope.customizationsList.push(detail.product.name);
                                    }
                                });
                            });
                        }
                        console.log("Customizations List:::::::::::", $scope.customizationsList);
                    };

                    $scope.selectVariants = function (variant, detail) {
                        var key = "";
                        variant.details.map(function (detail) {
                            key = key + detail.alias;
                        });
                        $scope.selectedVariantMap[key] = detail.alias;
                        $scope.customizationsList.push(detail.alias);
                    };

                    $scope.checkIsHeartButtonClicked = function () {
                        $scope.favChaiInsideModal = $scope.orderItem.isFavChaiMarked;
                    }
                    $scope.checkProduct = function (value) {
                        var found = false;
                        if (value.type == "PRODUCT") {
                            AppUtil.getUnitDetails().products.map(function (item) {
                                if (item.id == value.productId) {
                                    found = true;
                                }
                            });
                        } else {
                            found = true;
                        }
                        return found;
                    };

                    $scope.selectComplimentaryDetail = function (item) {
                        if (item == undefined || item == null) {
                            return;
                        }
                        $scope.complimentaryDetail.reasonCode = item.reasonCode;
                    };

                    $scope.submitComplimentary = function () {
                        //console.log($scope.complimentaryDetail.isComplimentary);
                        if ($scope.complimentaryDetail != null && $scope.complimentaryDetail.isComplimentary) {
                            if ($scope.complimentaryDetail.reasonCode == undefined || $scope.complimentaryDetail.reasonCode == null) {
                                alert('Please select a valid reason code from the dropdown');
                                return;
                            }
                            console
                                .log('setting complimentary details in order item');
                            $scope.selectedOrderItem.orderDetails.complimentaryDetail = $scope.complimentaryDetail;
                        } else {
                            $scope.selectedOrderItem.orderDetails.complimentaryDetail.isComplimentary = false;
                            $scope.selectedOrderItem.orderDetails.complimentaryDetail.reasonCode = null;
                            $scope.selectedOrderItem.orderDetails.complimentaryDetail.reason = null;
                            $scope.selectedOrderItem.orderDetails.discountDetail.promotionalOffer = 0;
                        }
                        $scope.calculatePaidAmount();
                        $modalInstance.dismiss('submit');
                    };

                    $scope.selectedAddOns = function (addOn) {
                        addOn.selected = !addOn.selected;
                        $scope.checkIfPresentInCustomizationList(addOn.product.name,addOn.selected)
                    };

                    $scope.selectedOptions = function (option) {
                        option.selected = !option.selected;
                        $scope.checkIfPresentInCustomizationList(option.name,option.selected);
                    };

                    $scope.selectTakeAway = function (item) {
                        if (item.orderDetails.takeAway == undefined || item.orderDetails.takeAway == null) {
                            item.orderDetails.takeAway = true;
                        } else {
                            item.orderDetails.takeAway = !item.orderDetails.takeAway;
                        }
                        $scope.checkIfPresentInCustomizationList("Take Away",item.orderDetails.takeAway);
                    };

                    $scope.selectVariantsData = function (i, list) {
                        for (var index in list) {
                            if (index == i) {
                                list[index].selected = true;
                                $scope.checkIfPresentInCustomizationList(list[index].alias, list[index].selected);
                            } else {
                                list[index].selected = false;
                                $scope.checkIfPresentInCustomizationList(list[index].alias, list[index].selected);
                            }
                        }
                    };

                    $scope.selectVariantsProductData = function (productId, list) {
                        for (var index in list) {
                            if (list[index].product.productId == productId) {
                                var totalInventory = $scope.$parent.getInventoryForProduct(list[index].product.productId);
                                var consumedInventory = $scope.$parent.trackedItemMap[list[index].product.productId + "," + list[index].dimension.code];
                                if (totalInventory == null || !AppUtil.isInventoryTrackedProductForId(list[index].product.productId)) {
                                    list[index].selected = true;
                                } else {
                                    if (consumedInventory == undefined || consumedInventory == null) {
                                        consumedInventory = 0;
                                    }
                                    if ((totalInventory - consumedInventory) > 0) {
                                        list[index].selected = true;
                                    } else {
                                        bootbox.alert("Inventory Finished for the product : " + list[index].product.name);
                                        list[index].selected = false;
                                    }
                                }
                            } else {
                                list[index].selected = false;
                            }
                        }
                    };

                    $scope.checkInventoryForProduct = function (item) {
                        var totalInventory = $scope.$parent.getInventoryForProduct(item.product.productId);
                        var consumedInventory = $scope.$parent.trackedItemMap[item.product.productId + "," + item.dimension.code];
                        if (totalInventory == null) {
                            return "";
                        } else {
                            if (consumedInventory == undefined || consumedInventory == null) {
                                consumedInventory = 0;
                            }
                            if ((totalInventory - consumedInventory) > 0) {
                                return "";
                            } else {
                                return "Inventory Finished for the product : " + item.product.name;
                            }
                        }
                    };

                    $scope.submit = function () {
                        if ($scope.selectedOrderItem.orderDetails.isCombo) {
                            var inventoryMessage = "";
                            for (var i in $scope.selectedOrderItem.recipeDetails.ingredient.compositeProduct.details) {
                                var selected = false;
                                for (var j in $scope.selectedOrderItem.recipeDetails.ingredient.compositeProduct.details[i].menuProducts) {
                                    selected = $scope.selectedOrderItem.recipeDetails.ingredient.compositeProduct.details[i].menuProducts[j].selected;
                                    if (selected) {
                                        var pd = $scope.selectedOrderItem.recipeDetails.ingredient.compositeProduct.details[i].menuProducts[j];
                                        if (AppUtil.isInventoryTrackedProductForId(pd.product.productId)) {
                                            var m = $scope.checkInventoryForProduct(pd);
                                            if (m != null && m.length > 1) {
                                                inventoryMessage = inventoryMessage + "<br/>" + m
                                            }
                                        }
                                        break;
                                    }
                                }
                                if (!selected) {
                                    bootbox.alert("Need To Select at least one item in each item list of combo");
                                    $scope.$parent.deleteItem(0);
                                    $modalInstance.dismiss('cancel');
                                    break;
                                }
                            }

                            if (inventoryMessage.trim().length > 1) {
                                bootbox.alert(inventoryMessage);
                                $scope.$parent.deleteItem(0);
                                $modalInstance.dismiss('cancel');
                                //break;
                            }

                            for (i in $scope.selectedOrderItem.recipeDetails.ingredient.compositeProduct.details) {
                                for (j in $scope.selectedOrderItem.recipeDetails.ingredient.compositeProduct.details[i].menuProducts) {
                                    if ($scope.selectedOrderItem.recipeDetails.ingredient.compositeProduct.details[i].menuProducts[j].selected) {
                                        if ($scope.$parent.checkValuesInQuantityMap($scope.selectedOrderItem.recipeDetails.ingredient.compositeProduct.details[i].menuProducts[j].product.productId, $scope.selectedOrderItem.recipeDetails.ingredient.compositeProduct.details[i].menuProducts[j].dimension.code, 'add')) {

                                        }
                                    }
                                }
                            }
                        }
                        $scope.selectedOrderItem.isModified = true;
                        $scope.submitted = true;
                        $scope.selectedOrderItem = AppUtil.calculateCustomization($scope.selectedOrderItem, productService.orderItemIndex);
                        if ($scope.selectedOrderItem.orderDetails.composition != undefined) {
                            productService.orderItemIndex = productService.orderItemIndex + $scope.selectedOrderItem.orderDetails.composition.menuProducts.length;
                        }
                        //code to add paid addons via customization
                        if ($scope.selectedOrderItem.recipeDetails != null && $scope.selectedOrderItem.recipeDetails.options != undefined && $scope.selectedOrderItem.recipeDetails.options != null && $scope.selectedOrderItem.recipeDetails.options.length > 0) {
                            for (i in $scope.selectedOrderItem.recipeDetails.options) {
                                if ($scope.selectedOrderItem.recipeDetails.options[i].selected &&
                                    $scope.selectedOrderItem.recipeDetails.options[i].type == "PRODUCT") {
                                    var found = false;
                                    $scope.$parent.orderItemArray.map(function (item) {
                                        if (item.productDetails.parentProductId == $scope.selectedOrderItem.orderDetails.itemId &&
                                            item.productDetails.id == $scope.selectedOrderItem.recipeDetails.options[i].productId) {
                                            found = true;
                                        }
                                    });
                                    if (!found) {
                                        productService.addNewProductToOrderItemArrayByProductId($scope.selectedOrderItem.recipeDetails.options[i].productId,
                                            $scope.selectedOrderItem.orderDetails.itemId, $scope.selectedOrderItem.productDetails.name, $scope.selectedOrderItem.orderDetails.quantity);
                                    }
                                }
                                if (!$scope.selectedOrderItem.recipeDetails.options[i].selected &&
                                    $scope.selectedOrderItem.recipeDetails.options[i].type == "PRODUCT") {
                                    $scope.$parent.orderItemArray.map(function (item, index) {
                                        if (item.productDetails.parentProductId == $scope.selectedOrderItem.orderDetails.itemId &&
                                            item.productDetails.id == $scope.selectedOrderItem.recipeDetails.options[i].productId) {
                                            var ind = $scope.$parent.orderItemArray.length - (index + 1);
                                            $scope.$parent.deleteItem(ind);
                                        }
                                    });
                                }
                            }
                        }
                        // $scope.orderItem=$scope.selectedOrderItem;
                        if($scope.selectedOrderItem.productDetails.type==5){
                            $modalInstance.close($scope.selectedOrderItem);
                        }else{
                            $modalInstance.dismiss('submit');
                        }
                    };

                    $scope.comboProductFilter = function (item) {
                        return AppUtil.hasProductForId(item.product.productId);
                    };

                    $scope.cancelCustomize = function () {
                        if($scope.selectedOrderItem.isHeartButtonClicked!=undefined && $scope.orderItem.isHeartButtonClicked){
                            $scope.selectedOrderItem.isHeartButtonClicked=undefined;
                            if($scope.favChaiInsideModal!=undefined && $scope.favChaiInsideModal){
                                $scope.favChaiInsideModal=false;
                                $scope.orderItem.isFavChaiMarked=$scope.favChaiInsideModal;
                            }
                        }
                        if ($scope.selectedOrderItem.orderDetails.isCombo) {
                            $scope.$parent.deleteItem(0);
                            $modalInstance.dismiss('cancel');
                        } else {
                            $modalInstance.dismiss('cancel');
                        }
                    };


                    $scope.checkFavChaiMarked = function () {
                        if($scope.favChaiInsideModal==undefined){
                            $scope.favChaiInsideModal=true
                        }else{
                            $scope.favChaiInsideModal=!$scope.favChaiInsideModal;
                        }
                        $scope.selectedOrderItem.isFavChaiMarked =$scope.favChaiInsideModal;
                        $scope.selectedOrderItem.isHeartButtonClicked = true;
                        // return $scope.selectedOrderItem.isFavChaiMarked;
                        return $scope.favChaiInsideModal;
                    }

                    $scope.checkIfPresentInCustomizationList=function(alias, isSelected){
                        if($scope.currentCustomizationList!= undefined && $scope.currentCustomizationList.length==0){
                            $scope.currentCustomizationList=angular.copy($scope.customizationsList);
                        }
                        if(isSelected){
                            if($scope.currentCustomizationList!=null && $scope.currentCustomizationList.length>=0 && $scope.currentCustomizationList.indexOf(alias)<0){
                                $scope.currentCustomizationList.push(alias);
                            }
                        }else{
                            $scope.currentCustomizationList = $scope.currentCustomizationList.filter(function(ele){
                                return ele != alias;
                            });
                        }
                        console.log("Current Customizations List------->", $scope.currentCustomizationList);
                        if($scope.currentCustomizationList.length == $scope.customizationsList.length){
                            if($scope.currentCustomizationList.length>0 && $scope.customizationsList.length>0){
                                var isEqual = $scope.currentCustomizationList.toString()===$scope.customizationsList.toString();
                                if(isEqual && $scope.isFavChai!=undefined && $scope.isFavChai){
                                   $scope.favChaiInsideModal=true;
                                }else{
                                    $scope.favChaiInsideModal=false;
                                }
                            }
                        }else {
                            $scope.favChaiInsideModal=false;
                        }
                        $scope.selectedOrderItem.isFavChaiMarked =$scope.favChaiInsideModal;
                    };

                    $scope.cancelComplimentary = function () {
                        if ($scope.complimentaryDetail.isComplimentary) {
                            if ($scope.complimentaryDetail.reasonCode == undefined || $scope.complimentaryDetail.reasonCode == null) {
                                //console.log('resetting complimentary details');
                                $scope.complimentaryDetail = {
                                    reasonCode: null,
                                    isComplimentary: null,
                                    reason: null
                                };
                            }
                        }
                        // $scope.calculatePaidAmount();
                        $modalInstance.dismiss('cancel');
                    };
                }]);

})();
