angular
.module('posApp').controller('handOverDateController', ['$rootScope','$scope','AppUtil','$http', '$location','$modalInstance','posAPI',
	function ($rootScope,$scope,AppUtil,$http,$location,$modalInstance,posAPI) {
	
	$scope.unitDetails = AppUtil.getUnitDetails();
	var unitId = $scope.unitDetails.id;
	var count; 
	
	$scope.viewType = 'add';
	function init(){
		$scope.entryType = null;
	}
	
	$scope.submithandoOverDate = function(handOverD) {
		// validating unit.
		$rootScope.showFullScreenLoader = true;
		var unitId = $scope.unitDetails.id;
		var params = {
				handOverData: handOverD,
	             unitId: unitId
            };
        $http({
            method: "GET",
            url: AppUtil.restUrls.scmService.validatingUnitCheck,
            params: params
        }).then(function success(response) {
            if(response.data == ""){
            	$scope.updateHandoverDate(handOverD,unitId)
            } else{
                bootbox.alert(response.data + "Please close all this from SUMO");
            	AppUtil.myAlert(response.data + "Please close all this from SUMO");
            }
            $rootScope.showFullScreenLoader = true;
        },function error(err) {
        	 $rootScope.showFullScreenLoader = false;
             service.myAlert(err.data.errorMessage);
        });
    };

	
	$scope.updateHandoverDate = function(handOverD,unitId){
		//var unitId = $scope.unitDetails.id;
		 $rootScope.showFullScreenLoader = true;
		 posAPI.allUrl('/', AppUtil.restUrls.unitMetaData.updateHandoverDate).customGET("", {
             handOverData: handOverD,
             unitId: unitId,
         }).then(function (response) {
        	 if(response){
        		 AppUtil.myAlert("Handover Date updated Successfully.");
        		 $modalInstance.dismiss('dismiss');
                 $location.url('/login');
                 $rootScope.showFullScreenLoader = false;
        	 }
         }, function (err) {
             stopSpin('spinner-1');
             $rootScope.showFullScreenLoader = false;
             service.myAlert(err.data.errorMessage);
         });
	}

	$scope.closeModal = function() {
		$modalInstance.close();
	};

	function showloader(){
		$scope.loader.loading = true ;
	}
	function hideloader(){
		$scope.loader.loading = false ;
	}

	init();
}]
);