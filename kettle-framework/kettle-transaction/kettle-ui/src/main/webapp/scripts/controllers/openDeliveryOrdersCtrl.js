/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

angular
    .module('posApp')
    .controller('openDeliveryOrdersCtrl',
		['$rootScope', '$location', '$scope', '$modal', 'AppUtil', 'posAPI', 'AssemblyService',
			function ($rootScope, $location, $scope, $modal, AppUtil, posAPI, AssemblyService) {
    	
    	
    	$scope.openOrderData = {};
        $scope.isOpen = true;
        $scope.isSecOpen = true;
        $scope.orderStates = AssemblyService.orderStates;

    	$scope.init = function(){
    		fetchOrders();
    	};

    	 $scope.sendUpdate = function(orderId, status, unitId, source){
	     //console.log("orderId ::::::: " + orderId);
	     var orderStatus = $scope.orderStates[status];
	     var requests = {
	         orderId: orderId,
	         orderStatus: orderStatus,
	         unitId: unitId,
	         category: source
	     };
	     var reqObj = AppUtil.GetRequest(requests);
	     //console.log("Request Object");
	     //console.log(reqObj);

	posAPI.allUrl('/',AppUtil.restUrls.order.updateStatus)
		.post(reqObj)
		.then(
	        function (response) {
	        	var result = response == 'true' ? false : true;
	        	if(result){
	        	    bootbox.alert("Order Settled Successfully");
	        	}else{
	        	    bootbox.alert("Error While Settling Order");
	        	}
	        	 $scope.refresh();
	        }, function (err) {
	        	console.log(err);
	        }
	     );

    	 };

        $scope.backToCover = function () {
            $location.url("/cover");
        };
        
        $scope.refresh = function(){
        	fetchOrders();
        };
        
        $scope.getSettlements = function(orderObj){
    		return AppUtil.getSettlements(orderObj);
        };
        
    	function fetchOrders(){
    		posAPI.allUrl('/',AppUtil.restUrls.order.openDeliveryOrders).post($rootScope.globals.currentUser)
	        .then(function (response) {
	        	$scope.openOrderData = getOrders(response.plain());
	        }, function (err) {
	            AppUtil.myAlert(err.data.errorMessage);
	        });
    	}
    	
    	function getOrders(orderList){
    		var openOrderData = {};
    		orderList.forEach(function(orderObj){
    			var ordersInStatus = openOrderData[orderObj.order.status];
    			if(ordersInStatus == null){
    				ordersInStatus = [];
    			}
    			orderObj['elapsedTime'] = ((Date.now() - new Date(orderObj.order.billCreationTime))/(1000*60)).toFixed(2);
    			orderObj.brand = AppUtil.getBrandByBrandId(orderObj.order.brandId);
    			ordersInStatus.push(orderObj);
    			openOrderData[orderObj.order.status] = ordersInStatus;
    		});
    		//console.log("openOrderData ::::",openOrderData);
    		return openOrderData;
    	}
    	
    }]);
