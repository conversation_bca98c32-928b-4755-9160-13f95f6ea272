angular
.module('posApp').controller('meterReadingDetailsController', ['$rootScope','$scope','AppUtil','$http','$modalInstance','posAPI',
	function ($rootScope,$scope,AppUtil,$http,$modalInstance,posAPI) {
	
	$scope.unitDetails = AppUtil.getUnitDetails();
	var unitId = $scope.unitDetails.id;
	var count; 
	
	var lastReadingDetails = null;
	$scope.viewType = 'add';
	function init(){
		$scope.entryType = null;
		$scope.meterReadings={};
		$scope.meterCount = [];
		getLastMeterReadingOfUnit();
		$scope.loader = {
				loading: false,
		};
		lastReadingDetails = [];
		count =100;
	}
	
	function initilizeUpdateObjects(){
		$scope.singleMeter = ($scope.unitDetails.noOfMeter == 1) ? true : false;
		$scope.readingRequestObj = {};
		$scope.currentMeterDetail = undefined;
		$scope.lastMeterDetail = undefined;
		$scope.getLastMeterReadingList("ELECTRICITY", 1);
	}
	
	
	$scope.changeEntryType = function(type){
		$scope.entryType = type;
	};
	
	$scope.changeViewType = function(type){
		$scope.viewType  = type;
		if($scope.viewType == 'add'){
			init();
		}else if($scope.viewType == 'update'){
			initilizeUpdateObjects();
		}
	};

	function createReadingObject(){
		for(var i = 0; i < $scope.unitDetails.noOfMeter; i++){
			$scope.meterCount.push(i);
			$scope.meterReadings[i] = getMeterObject("ELECTRICITY", i+1);
		}
		if($scope.unitDetails.dGAvailable){
			$scope.meterReadings[$scope.unitDetails.noOfMeter] = getMeterObject("DG", $scope.unitDetails.noOfMeter+1);
		}
	//	console.log("$scope.meterReadings",$scope.meterReadings);
	};

	function getLastMeterReadingOfUnit(){
		posAPI.allUrl('/',AppUtil.restUrls.expenseManagement.lastMeterReading).post(unitId)
		.then(function (response) {
			lastReadingDetails = response.plain();
			createReadingObject();
		},
		function (err) {
			AppUtil
			.myAlert(err.data.errorMessage);
		});
	};
	
	$scope.getLastMeterReadingList = function(billType, meterNo){
		
		$scope.readingRequestObj.unitId = unitId;
		$scope.readingRequestObj.billType = billType ;
		if(!$scope.singleMeter && $scope.readingRequestObj.meterNo == undefined){
			AppUtil
			.myAlert("Please select meter no!");
			return false;
		}
		$scope.readingRequestObj.meterNo = meterNo;
		
		posAPI.allUrl('/',AppUtil.restUrls.expenseManagement.lastMeterReadingList).post($scope.readingRequestObj)
		.then(function (response) {
		//	console.log("response",response.plain());
			var result = response.plain()
			if(result.length == 2){
				$scope.currentMeterDetail = result[0];
				$scope.lastMeterDetail = result[1];
			}else if(result.length == 1){
				$scope.currentMeterDetail = result[0];
				$scope.lastMeterDetail = {
						currentReading : 0	
				};
			}
		},
		function (err) {
			AppUtil
			.myAlert(err.data.errorMessage);
		});
	};

	$scope.submitMeterReadings = function(meterReadings){
		var details = Object.values(meterReadings);
		if($scope.entryType == null || $scope.entryType == undefined){
			AppUtil
			.myAlert("Please select entry type!");
			return false;
		}
		for(var i=0 ;i < details.length;i++){
			details[i].entryType = $scope.entryType;
			if(details[i].currentReading < 0){
				AppUtil
				.myAlert("Please enter valid current reading of bill type "+details[i].billType);
				return false;
			}else if((details[i].currentReading % 1) !== 0){
				AppUtil
				.myAlert("Decimal numbers are not allowed : "+details[i].billType);
				return false;
			}else if(details[i].currentReading < details[i].lastReading){
				AppUtil
				.myAlert("Current Reading can not be less than Last Reading of bill type "+details[i].billType);
				return false;
			}else if(details[i].lastReading != 0 && details[i].currentReading - details[i].lastReading > 1000){
				AppUtil
				.myAlert("Current Reading can not be greater than Last Reading by 1000 unit of bill type "+details[i].billType);
				return false;
			}
		}
		//console.log("details",details);
		bootbox.confirm("Are you sure to details enetered are correct ?", function (result) {
			
			if (result == true) {
				posAPI.allUrl('/',AppUtil.restUrls.expenseManagement.addMeterDetails).post(details)
				.then(function (response) {
					if(response.errorType == undefined){
						AppUtil
						.mySuccessAlert("Meter details saved successfully");
						init();
					}else{
						AppUtil
						.myAlert(response.errorMessage);
					}
				},
				function (err) {
					AppUtil
					.myAlert(err.data.errorMessage);
				});
			}
		});
	};
	
	$scope.updateMeterReadings = function(){
		
		if($scope.currentMeterDetail.currentReading < 0){
			AppUtil
			.myAlert("Please enter valid current reading of bill type "+$scope.currentMeterDetail.billType);
			return false;
		}else if(($scope.currentMeterDetail.currentReading % 1) !== 0){
			AppUtil
			.myAlert("Decimal numbers are not allowed : "+$scope.currentMeterDetail.billType);
			return false;
		}else if($scope.currentMeterDetail.currentReading < $scope.lastMeterDetail.currentReading){
			AppUtil
			.myAlert("Current Reading can not be less than Last Reading of bill type "+$scope.currentMeterDetail.billType);
			return false;
		}
		
		var idCodeName ={
				id : AppUtil.GetLoggedInUser(),
				code : "",
				name : ""
		};
		$scope.currentMeterDetail.updatedBy = idCodeName;
		
		//console.log("details",details);
		bootbox.confirm("Are you sure to details enetered are correct ?", function (result) {
			var requestArr = [];
			requestArr.push($scope.currentMeterDetail);
			if (result == true) {
				posAPI.allUrl('/',AppUtil.restUrls.expenseManagement.updatelastMeterReading).post(requestArr)
				.then(function (response) {
					if(response.errorType == undefined){
						AppUtil
						.mySuccessAlert("Meter details updated successfully");
						initilizeUpdateObjects();
					}else{
						AppUtil
						.myAlert(response.errorMessage);
					}
				},
				function (err) {
					AppUtil
					.myAlert(err.data.errorMessage);
				});
			}
		});
	};

	$scope.closeModal = function() {
		$modalInstance.close();
	};

	function getMeterObject(type, no){
		var number = (type == "ELECTRICITY" ? no : 1);
		var lastReading = 0;
		for(var i=0 ;i < lastReadingDetails.length;i++){
			if(lastReadingDetails[i][0] == number && lastReadingDetails[i][2] == type){
				lastReading = lastReadingDetails[i][1];
			}
		}
		var idCodeName ={
				id : AppUtil.GetLoggedInUser(),
				code : "",
				name : ""
		};
		var meterDetail = {
				unitId : unitId,
				meterNo : number,
				lastReading : lastReading,
				currentReading : null,
				billType : type,
				createdBy : idCodeName,
				entryType : $scope.entryType,
				uniqueIndex : count++
		}
		return meterDetail;
	}

	function showloader(){
		$scope.loader.loading = true ;
	}
	function hideloader(){
		$scope.loader.loading = false ;
	}

	init();
}]
);