/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

angular
    .module('posApp')
    .controller(
        'settlementModalCtrl',
        ['$scope', '$modalInstance', 'posAPI', 'productService', 'socketUtils',
            'AppUtil', '$modal', 'PrintService', 'subscriptionService', '$rootScope', 'swiggyOrderService', 'tableService', 'tableCheckout', '$interval', '$timeout',
            function ($scope, $modalInstance, posAPI, productService, socketUtils,
                      AppUtil, $modal, PrintService, subscriptionService, $rootScope, swiggyOrderService, tableService, tableCheckout, $interval, $timeout) {

                $scope.showLoadingOnButton = false;
                $scope.giftCardModalOpen=AppUtil.cardPaymentModule; //check to open different payment module
                $scope.alreadyPaid = $scope.$parent.prepaidAmount;
                $scope.submitted = false;
                $scope.isChannelPartnerOrder = false;
                $scope.payment = {};
                $scope.giftCards = [];
                $scope.unusedGiftCards = [];
                $scope.unusedGiftCardNumbers = [];
                $scope.giftCardsLoaded = false;
                $scope.showNameField = false;
                $scope.creditAccounts = AppUtil.creditAccounts;
                $scope.customerName = null;
                $scope.yesterday = AppUtil.yesterday;
                $scope.outletName = null;
                $scope.showCustomerName = true;//!productService.customerSocketWidget.detailsEntered();
                $scope.channelPartner = getIdToGetForChannel();
                $scope.deliveryPartner = null;
                $scope.unitFamily = $rootScope.globals.currentUser.unitFamily;
                $scope.weekDays = subscriptionService.weekDays;
                $scope.monthDays = subscriptionService.monthDays;
                $scope.frequencyTimes = subscriptionService.frequencyTimes;
                $scope.manualBillBookNo = null;
                $scope.isBillNoValidationRequired = true;
                $scope.isPaidEmployeeMeal = AppUtil.isPaidEmployeeMeal();
                $scope.paymentFormPOS = true;
                $scope.paymentDone = false;

                $scope.unusedSelfCards = [];
                $scope.unusedSelfCardNumbers = [];
                $scope.cardSelected = {};
                $scope.manualBillBookNo = undefined;
                $scope.sodexoDenominations = [];
                $scope.trDenominations = [];
                $scope.isOtpVerified = AppUtil.isCSOtpVerified();
                $scope.isTableCheckout = AppUtil.isEmptyObject(tableCheckout) ? false : tableCheckout;
                $scope.paymentModeArray = [];
                $scope.isOrderSourceIdReqd = undefined;
                $scope.orderSourceId=null;
                $scope.paymentModeType = null;
                $scope.paymentModeMap = {};

                if (AppUtil.customerSocket && AppUtil.customerSocket.contact != null && AppUtil.customerSocket.contact != '' && $scope.transactionObj.paidAmount > 0) {
                    var transactionPaymentModes = $scope.transactionMetadata.paymentModes;
                    transactionPaymentModes = transactionPaymentModes.map(function (mode) {
                        return {
                            id: mode.id,
                            name: mode.name
                        }
                    });
                    // $scope.paymentId = 'POS_' + 'U' + $scope.$parent.unitDetails.id + 'T' + AppUtil.getAutoConfigData().selectedTerminalId + Date.now();
                   // sending converted order and transaction to CRM APP
                    var productArray = [];
                    for (var i in $scope.orderItemArray) {
                        var product = {
                            name: $scope.orderItemArray[i].orderDetails.productName,
                            quantity: $scope.orderItemArray[i].orderDetails.quantity,
                            amount: $scope.orderItemArray[i].orderDetails.totalAmount
                        }
                        productArray.push(product);
                    }
                    var transactionDetails = {
                        DiscountReason: $scope.transactionObj.discountDetail.discountReason,
                        totalDiscount: $scope.transactionObj.discountDetail.totalDiscount,
                        discountPercentage:$scope.transactionObj.discountDetail.discount.percentage,
                        totalTax:$scope.transactionObj.tax,
                        savings:$scope.transactionObj.discountDetail.savings,
                        paidAmount:$scope.transactionObj.discountDetail.paidAmount,
                        totalAmount:$scope.transactionObj.totalAmount,
                    }

                    var customerOrderDetail={
                        orderDetail:productArray,
                        transaction:transactionDetails
                    }
                    console.log("sending details to customer screen::::::::::::::::::;;;")
                    console.log("order detail is", $scope.orderItemArray)
                    console.log("transaction obj", $scope.transactionObj)
                    var paymentData = {
                        amount: $scope.transactionObj.paidAmount,
                        // orderId: $scope.paymentId,
                        cartId: $scope.$parent.orderIdForPayment,
                        paymentModes: transactionPaymentModes,
                        details: customerOrderDetail  // customized object to send data to CRM APP
                    };

                    // socketUtils.emitMessage({PAY_AMOUNT: paymentData});
                }
                $scope.transactionMetadata.paymentModes.forEach(function (v) {
                    if (v.name == "Sodexo") {
                        $scope.sodexoDenominations = angular.copy(v.denominations);
                    }
                    if (v.name == "TicketRestaurant") {
                        $scope.trDenominations = angular.copy(v.denominations);
                    }
                    if (v.name == "Prepaid" && $scope.alreadyPaid != undefined && $scope.alreadyPaid != null) {
                        $scope.payment[v.id] = angular.copy($scope.alreadyPaid);
                    }
                });
                $scope.showSodexoDenoms = false;
                $scope.showTRDenoms = false;
                /*check -> SuggestGift Card Module  if wallet Payment then open PayBYGiftCard Tab*/
                $scope.showCardDetails = $scope.giftCardModalOpen;

                $scope.showCardError = false;

                $scope.containsGiftCard = productService.checkGiftCardInOrder();

                $scope.selectDeliveryPartner = function (partner) {
                    $scope.deliveryPartner = partner;
                };

                $scope.isEmployeeMeal = $scope.$parent.isEmployeeMeal;
                $scope.isSpecialOrder = $scope.$parent.isSpecialOrder;

                $scope.billSetlementType  = $scope.$parent.billSetllementFromGiftCard ? true:false;
                $scope.checkIdInPaymentModeType = function (paymentModeId) {
                if (!AppUtil.isEmptyObject(paymentModeId) && !AppUtil.isEmptyObject($scope.paymentModeType) && !AppUtil.isEmptyObject($scope.paymentModeMap)){
                    return $scope.paymentModeMap[$scope.paymentModeType].indexOf(paymentModeId)>=0?true:false ;
                }
                }
                console.log("Printing  value of billSetlementType");
                console.log($scope.billSetlementType);
                $scope.hasTableService = $scope.$parent.tableService;
                $scope.hasExtendedTableService = $scope.$parent.hasExtendedTableService;
                $scope.isTableOrder = $scope.$parent.tableService && tableService.getCurrentTable() != null && tableService.getCurrentTable().tableRequestId > 0;
                if ($scope.isTableCheckout) {
                    socketUtils.receiveMessage(function (message) {
                        $scope.renderedTemplate = productService.customerSocketWidget
                            .updateTemplate(message, function (points, isNew, key) {
                                message = message;
                                var data = message[key];
                                if (key == "SCREEN_OPENED") {
                                    console.log("Cancelling order start interval from POS");
                                    $rootScope.csScreenTimer = false;
                                    $interval.cancel($rootScope.csTimeout);
                                    $rootScope.orderStartCount = 0;
                                    $timeout(function () {
                                        $scope.isCustomerSocketConnected = true;
                                        $scope.customerNameManual = false;
                                        $scope.customerRejectBtnHidden = false;
                                        socketUtils.setPairingStatus(true);
                                    });
                                }
                                if (!AppUtil
                                        .isEmptyObject(data)
                                    && (!AppUtil
                                        .isEmptyObject(data.contact) || !AppUtil
                                        .isEmptyObject(data.name))) {
                                    $scope.detailsEntered == true;
                                    $scope.isCustomerSocketConnected = true;
                                    $scope.customerNameManual = false;
                                }
                                if (data != null
                                    && data.name != undefined
                                    && data.name != null
                                    && $scope.customerEnteredName == null) {
                                    $scope.customerEnteredName = data.name;
                                    $scope.customerRejectBtnHidden = true;
                                }
                                if (key != undefined
                                    && key != null) {
                                    if (key == "OTP_SENT"
                                        || key == "OTP_RESENT"
                                        || key == "NEW_CUSTOMER") {
                                        $scope.otpBtnHidden = false;
                                    }
                                    if (key == "OTP_STATUS"
                                        && data.contactVerified) {
                                        $scope.otpBtnHidden = true;
                                    }
                                    if (key == "OTP_VERIFIED") {
                                        AppUtil.otpStatus.text = "OTP Verification successful!";
                                        AppUtil.otpStatus.status = 1;
                                        $scope.otpBtnHidden = true;
                                    }
                                }
                            });
                    }, "SETTLEMENT_CTRL");
                } else {
                    socketUtils.receiveMessage(function (message) {
                        $scope.renderedTemplate = productService.customerSocketWidget
                            .updateTemplate(message, function (points, isNew, key) {
                                message = message;
                                var data = message[key];
                                if (key == "SCREEN_OPENED") {
                                    console.log("Cancelling order start interval from POS");
                                    $rootScope.csScreenTimer = false;
                                    $interval.cancel($rootScope.csTimeout);
                                    $rootScope.orderStartCount = 0;
                                    $timeout(function () {
                                        $scope.isCustomerSocketConnected = true;
                                        $scope.customerNameManual = false;
                                        $scope.customerRejectBtnHidden = false;
                                        socketUtils.setPairingStatus(true);
                                    });
                                }
                                if (!AppUtil
                                        .isEmptyObject(data)
                                    && (!AppUtil
                                        .isEmptyObject(data.contact) || !AppUtil
                                        .isEmptyObject(data.name))) {
                                    $scope.detailsEntered == true;
                                    $scope.isCustomerSocketConnected = true;
                                    $scope.customerNameManual = false;
                                }
                                if (data != null
                                    && data.name != undefined
                                    && data.name != null
                                    && $scope.customerEnteredName == null) {
                                    $scope.customerEnteredName = data.name;
                                    $scope.customerRejectBtnHidden = true;
                                }
                                if (key != undefined
                                    && key != null) {
                                    if (key == "PAYMENT_VERIFIED") {
                                        console.log('PAYMENT_VERIFIED ');
                                        $scope.paymentBanner = 'Transaction done...';
                                        AppUtil.customerSocket.paymentDone = true;
                                        $scope.paymentDone = true;
                                        $scope.paymentMode = data.paymentMode;

                                        if (data.paymentMode == "PAYTM") {
                                            $scope.paymentId = AppUtil.customerSocket.paytmOrderId;
                                            $scope.settleAmountDirectly(13);
                                        } else if (data.paymentMode == "GOOGLE PAY" || data.paymentMode == "GPAY") {
                                            $scope.paymentId = AppUtil.customerSocket.gPayOrderId;
                                            $scope.settleAmountDirectly(27);
                                        } else {
                                            $scope.paymentId = data.paymentMode == 'BHARAT_QR' ? AppUtil.customerSocket.bharatOrderId : AppUtil.customerSocket.upiOrderId;
                                            $scope.settleAmountDirectly(25);
                                        }
                                    }
                                    if (key == "PAYMENT_MODE_BY_CUSTOMER") {
                                        console.log('payment mode selected by customer ', data.paymentMode);
                                        $scope.paymentMode = data.paymentMode;
                                        if ($scope.paymentModeArray.indexOf($scope.paymentMode) < 0) {
                                            $scope.paymentModeArray.push($scope.paymentMode);
                                        }
                                        $scope.paymentFormPOS = false;
                                        $scope.paymentBanner = 'Transaction is in progress...';
                                        $scope.showLoadingOnButton = false;
                                    }
                                    if (key == "TRANSACTION_FAILED_BY_CUSTOMER" || key == "TRANSACTION_TIMED_OUT_BY_CUSTOMER") {
                                        console.log('Transaction failed by customer ', data.paymentMode);
                                        $scope.paymentMode = data.paymentMode;
                                        $scope.paymentBanner = (key == "TRANSACTION_TIMED_OUT_BY_CUSTOMER") ? 'Transaction timed out...' : 'Transaction Failed...';
                                        $scope.paymentDone = false;

                                        var request = {
                                            paymentModeStatus: (key == "TRANSACTION_TIMED_OUT_BY_CUSTOMER") ? 'TIMED OUT' : 'FAILED',
                                            generateOrderId: $scope.paymentId
                                        }

                                        posAPI.allUrl('/', AppUtil.restUrls.order.markTransactionCancel)
                                            .post(request)
                                            .then(function (response) {
                                                    if (response) {

                                                    }
                                                },
                                                function (err) {
                                                }
                                            );

                                        $timeout(function () {
                                            $scope.paymentFormPOS = false;
                                        }, 5000);
                                        $scope.showLoadingOnButton = false;
                                    }
                                    if (key == "PAY_BY_GIFT_CARD") {
                                        $scope.payViaCard(function () {
                                            $scope.applyGiftCards();
                                            if ($scope.settlementRemaining() == 0) {
                                                $scope.submit();
                                            }
                                        });
                                    }
                                }
                            });
                    }, "SETTLEMENT_CTRL_1");
                }
                //console.log('Is Employee Meal ',$scope.isEmployeeMeal);
                if ($scope.isSpecialOrder) {
                    $scope.showCustomerName = false;
                } else if (AppUtil.isPaidEmployeeMeal()) {
                    $scope.showCustomerName = false;
                    AppUtil.customerSocket.name = $scope.$parent.employeeMealUser.name;
                } else {
                    if (!AppUtil.isCOD()) {
                        //console.log("Inside getting socket status");
                        if (socketUtils.getPairingStatus()) {
                            //console.log("Inside if statement of pairing status check");
                        } else {
                            if (AppUtil.customerSocket.name == null || AppUtil.customerSocket.name.trim().length == 0) {
                                AppUtil.myAlert("Customer Screen has gone offline. Please enter customer name below");
                                $scope.$parent.customerNameManual = true;
                            }
                            productService.metaDataList.addAttribute("CUSTOMER_SCREEN_DOWN", moment().format());
                            //console.log("Customer screen is not paired");
                        }
                    }
                }


                //console.log('Is Customer Name Manual ',$scope.$parent.customerNameManual);

                function getIdToGetForChannel() {
                    var partnerId = 1;
                    if (AppUtil.isTakeaway()) {
                        partnerId = 9;
                    }
                    if (AppUtil.isCafe()) {
                        partnerId = 1;
                    }
                    if (AppUtil.isCOD()) {
                        partnerId = 2;
                    }
                    //console.log("partnerId being sent is ::::: ", partnerId);
                    return $scope.transactionMetadata != null ? $scope.transactionMetadata.channelPartner.filter(function (channel) {
                        return channel.id == partnerId;
                    })[0] : null;
                }

                $scope.getTransactionModesId = function () {

                }


                $scope.setFrequencyDays = function () {
                    if ($scope.subscriptionFrequencyType == 'WEEKLY') {
                        $scope.frequencyDays = $scope.weekDays;
                    } else if ($scope.subscriptionFrequencyType == 'MONTHLY') {
                        $scope.frequencyDays = $scope.monthDays();
                    }
                    $scope.subscriptionTimes = [];
                    $scope.subscriptionDays = [];
                    $scope.frequencyDay = $scope.frequencyDays[0];
                    $scope.frequencyTime = $scope.frequencyTimes[0];
                };

                $scope.init = function () {
                    $scope.subscriptionFrequencyType = 'WEEKLY';
                    $scope.paymentFormPOS = true;
                    $scope.paymentDone = false;
                    $scope.setFrequencyDays();
                    $scope.subscriptionDays = [];
                    $scope.subscriptionTimes = [];
                    $scope.frequencyTime = $scope.frequencyTimes[0];
                    $scope.subscriptionStartDate = null;
                    $scope.subscriptionEndDate = null;
                    $scope.subscriptionMinDate = subscriptionService.subscriptionMinDate;
                    $scope.emailNotification = true;
                    $scope.smsNotification = true;
                    $scope.automatedDelivery = true;
                    $scope.channelPartnerOrder = null;
                    $scope.channelPartnerCustomer = null;
                    $scope.notFound = false;
                    if (AppUtil.activeChannelPartnerId != -1) {
                        updatePartnerDetails();
                    }
                    $scope.defaultPid = 1 ;
                    $scope.activeIndex = $scope.$parent.billSetllementFromGiftCard?1:2;
                };

                $scope.showRefreshOTPbutton = function () {
                    return AppUtil.otpStatus.text.length > 0 && AppUtil.otpStatus.status != 1
                };

                $scope.hasOTPText = function () {
                    return AppUtil.otpStatus.text.length > 0;
                }

                $scope.getOTPStatus = function () {
                    return AppUtil.otpStatus.status;
                }

                $scope.getOTPText = function () {
                    return AppUtil.otpStatus.text;
                }

                $scope.checkIfExistsInPartners = function (partners, partnerid) {
                    return AppUtil.checkInArrayOfPartners(partners, partnerid);
                };

                if (AppUtil.isCOD()) {
                    $scope.deliveryPartners = $scope.unitDetails.deliveryPartners;

                    if (!$scope.checkIfExistsInPartners($scope.deliveryPartners, 5)) {
                        $scope.deliveryPartners.unshift({id: 5, code: 'PICKUP', name: 'Pickup'});
                    }
                    if (!$scope.checkIfExistsInPartners($scope.deliveryPartners, 8)) {
                        $scope.deliveryPartners.push({id: 8, code: 'CHAAYOS_DELIVERY', name: 'Chaayos Delivery'});
                    }
                    //console.log($scope.deliveryPartners);
                    if (AppUtil.outlet.selectedId == 1) {
                        $scope.outletName = AppUtil.outlet.pri_name;
                    } else if (AppUtil.outlet.selectedId == 2) {
                        $scope.outletName = AppUtil.outlet.sec_name;
                    } else {
                        $scope.outletName = AppUtil.outlet.ter_name;
                    }

                    try {
                        $scope.deliveryPartner = $scope.deliveryPartners.filter(function (partner) {
                            return partner.id == 8;
                        })[0].id;
                    } catch (e) {
                        $scope.deliveryPartner = $scope.deliveryPartners[0].id;
                    }

                    $scope.channelPartner = $scope.transactionMetadata.channelPartner.filter(function (channel) {
                        return channel.code == "CHAAYOS_DELIVERY";
                    })[0];
                    $scope.CSObj = AppUtil.CSObj;
                    //console.log($scope.CSObj, AppUtil.CSObj);
                    $scope.addressString = getAddressString();
                    //console.log("Customer object after all the manipulations done");
                    //console.log($scope.CSObj);
                }

                function getAddressString() {
                    var addressString = '';
                    var address = null;
                    for (var i = 0; i < $scope.CSObj.addresses.length; i++) {
                        if ($scope.CSObj.addresses[i].isSelectedAddress) {
                            address = $scope.CSObj.addresses[i];
                            addressString = address.locality + ' '
                                + address.line1 + ' ' + address.line2 + ' '
                                + address.city;
                        }
                    }
                    return addressString;
                }

                $scope.settlementRemaining = function (cashCardRedemption) {
                    var remaining = $scope.transactionObj.paidAmount;

                    if (!AppUtil.isEmptyObject(cashCardRedemption) && cashCardRedemption) {
                        // this is fucked up
                        if (!AppUtil.isEmptyObject($scope.transactionObj.nonGcAmount)) {
                            remaining = $scope.transactionObj.nonGcAmount;
                        }
                    }

                    var paid = 0;
                    for (var key in $scope.payment) {
                        if (!isNaN($scope.payment[key])) {
                            paid += $scope.payment[key];
                        }
                    }
                    if (paid > 0) {
                        remaining = remaining - paid;
                    }

                    return remaining;
                };

                $scope.changeCreditAccount = function (channelPartner) {
                    $scope.channelPartner = channelPartner;
                    if (channelPartner != undefined && channelPartner != null) {
                        var creditAccountId = channelPartner.hasOwnProperty('creditAccount')
                            ? channelPartner.creditAccount : channelPartner.id;

                        if (creditAccountId != undefined && creditAccountId != null) {
                            $scope.creditAccount = $scope.creditAccounts.filter(function (acc) {
                                return acc.id == creditAccountId;
                            })[0];
                        } else {
                            $scope.creditAccount = $scope.creditAccounts[0];
                        }

                        if ($scope.creditAccount) {
                            $scope.csName = $scope.creditAccounts.filter(function (acc) {
                                return acc.id == $scope.creditAccount.id;
                            })[0];
                        }
                    }
                };

                $scope.checkForCredit = function (key) {
                    if (key == 6) {
                        if ($scope.payment[key] != undefined) {
                            var creditValue = $scope.payment[key];
                            $scope.payment = {};
                            $scope.payment[6] = creditValue;
                            $scope.showNameField = true;
                            $scope.changeCreditAccount($scope.channelPartner);
                            $scope.showSodexoDenoms = false;
                            $scope.showTRDenoms = false;
                            $scope.showCardDetails = false;
                        }
                    } else {
                        if ($scope.showNameField) {
                            $scope.payment[6] = undefined;
                            $scope.showNameField = false;
                        }
                    }
                };


                $scope.payViaCoupons = function (index) {
                    $scope.logCashData();
                    $scope.payment[4] = undefined;
                    $scope.payment[5] = undefined;
                    $scope.payment[6] = undefined;
                    $scope.payment[10] = undefined;
                    $scope.showNameField = false;
                    $scope.showCardDetails = false;
                    $scope.giftCards = [];
                    $scope.unusedGiftCards = [];
                    $scope.unusedSelfCards = [];
                    if (index == 4) {
                        $scope.showSodexoDenoms = true;
                        $scope.showTRDenoms = false;
                        $scope.payment[5] = undefined;
                    } else if (index == 5) {
                        $scope.showSodexoDenoms = false;
                        $scope.showTRDenoms = true;
                        $scope.payment[4] = undefined;
                    }
                    $scope.extraVouchers = undefined;
                };
                $scope.makePayment = function (modeType){
                    $scope.paymentModeType = modeType;
                    $scope.paymentModeMap = AppUtil.getPaymentModeIdsByModeType(modeType) ;
                    console.log("ModeType:", modeType, "PaymentModeIds", $scope.paymentModeMap[modeType]);
                    if($scope.defaultPid !=null ){
                        $scope.defaultPid = null;
                    }
                    $scope.showCardDetails=false;
                    switch (modeType) {
                        case 'CASH' : $scope.activeIndex = 1;
                                        break;
                        case 'GIFT_CARD' : $scope.activeIndex = 2;
                                        break;
                        case 'CARD' : $scope.activeIndex = 3;
                                        break;
                        case 'QR' : $scope.activeIndex = 4;
                                        break;
                        case 'OTHER' : $scope.activeIndex = 5;
                                        break;
                        // default : $scope.activeIndex = 1;
                    }
                }
                $scope.payViaGiftCard = function (modeType) {
                    // socketUtils.emitMessage({PAYMENT_FROM_POS: AppUtil.customerSocket});
                    $scope.paymentModeType = modeType;
                    $scope.paymentModeMap = AppUtil.getPaymentModeIdsByModeType(modeType) ;
                    console.log("ModeType:", modeType, "PaymentModeIds", $scope.paymentModeMap[modeType]);
                    var payviaGiftCard = false;
                    var x = 0;
                    for (x in $scope.transactionMetadata.paymentModes) {
                        if ($scope.transactionMetadata.paymentModes[x].name == 'GiftCard') {
                            if ($scope.transactionMetadata.paymentModes[x].enabled
                                && !$scope.containsGiftCard) {
                                $scope.payViaCard();
                                payviaGiftCard = true;
                            } else {
                                bootbox.alert("Order not eligible to be paid via gift card.");
                                payviaGiftCard = true;
                            }
                        }
                    }
                    if (!payviaGiftCard) {
                        bootbox.alert("Order not eligible to be paid via gift card.");
                    }
                    $scope.activeIndex = 2;
                }

                $scope.payViaCard = function (callback) {
                    $scope.logCashData();
                    $scope.payment[4] = undefined;
                    $scope.payment[5] = undefined;
                    $scope.payment[6] = undefined;

                    $scope.showNameField = false;
                    $scope.showTRDenoms = false;
                    $scope.showSodexoDenoms = false;

                    $scope.showCardDetails = true;
                    //$scope.showCardError = false;
                    /*$scope.giftCards = [];
                    $scope.unusedGiftCards = [];*/
                    if (!$scope.giftCardsLoaded) {
                        $scope.loadGiftCardsForCustomer(callback);
                    }
                };

                $scope.loadGiftCardsForCustomer = function (callback) {
                    if (AppUtil.customerSocket != null && AppUtil.customerSocket.id != null && AppUtil.customerSocket.id > 5) {
                        $scope.getGiftCardsForCustomer(AppUtil.customerSocket.id, callback);
                    } else if ($scope.isTableCheckout) {
                        var table = tableService.getCurrentTable();
                        if (table.customerId > 5) {
                            $scope.getGiftCardsForCustomer(table.customerId);
                        }
                    }
                };

                $scope.getGiftCardsForCustomer = function (gcCustomerId, callback) {
                    posAPI.allUrl('/', AppUtil.restUrls.order.getGiftCards)
                        .customGET("", {customerId: gcCustomerId})
                        .then(function (response) {
                            $scope.showCardError = false;
                            var dataObj = response.plain();
                            if (dataObj.errorType != undefined) {
                                $scope.giftCard = null;
                                $scope.showCardError = true;
                                $scope.giftCardError = dataObj.errorMessage;
                            } else {
                                $scope.giftCardsLoaded = true;
                                $scope.giftCardError = null;
                                //$scope.giftCardDetails=dataObj;
                                var selfDatObj = dataObj.self;
                                var giftDatObj = dataObj.gift;
                                var x = [];
                                giftDatObj.map(function (card) {
                                    if ($scope.unusedGiftCardNumbers.indexOf(card.cardNumber) < 0) {
                                        $scope.unusedGiftCards.push({
                                            giftCard: card.cardNumber,
                                            id: card.cashCardId,
                                            initialValue: card.cashInitialAmount,
                                            currentValue: card.cashPendingAmount,
                                            amount: 0,
                                            balance: card.cashPendingAmount,
                                            applied: false,
                                            autoAdded: true,
                                            cardType: 'GIFT',
                                            maskedNumber: $scope.maskGiftCardNumber(card.cardNumber)
                                        });
                                        $scope.unusedGiftCardNumbers.push(card.cardNumber);
                                    }
                                });
                                selfDatObj.map(function (card) {
                                    if ($scope.unusedSelfCardNumbers.indexOf(card.cardNumber) < 0) {
                                        $scope.unusedSelfCards.push({
                                            giftCard: card.cardNumber,
                                            id: card.cashCardId,
                                            initialValue: card.cashInitialAmount,
                                            currentValue: card.cashPendingAmount,
                                            amount: 0,
                                            balance: card.cashPendingAmount,
                                            applied: false,
                                            autoAdded: true,
                                            cardType: 'SELF',
                                            maskedNumber: $scope.maskGiftCardNumber(card.cardNumber)
                                        });
                                        $scope.unusedSelfCardNumbers.push(card.cardNumber);
                                    }
                                });
                            }
                            if (callback != null && typeof callback == 'function') {
                                callback();
                            }
                        }, function (err) {
                            console.log(err);
                        });
                }

                $scope.maskGiftCardNumber = function (cardNumber) {
                    return "****" + cardNumber.substring(4);
                }

                $scope.applyGiftCards = function () {
                    $scope.unusedSelfCards.map(function (cardx) {
                        var card = angular.copy(cardx);
                        var remaining = $scope.settlementRemaining(true);
                        if (remaining > 0 && !cardx.applied) {
                            var amount = (remaining >= card.currentValue)
                                ? card.currentValue
                                : remaining;
                            card.amount = amount;
                            card.balance = card.currentValue - amount;
                            card.applied = true;
                            $scope.giftCards.push(card);
                            cardx.applied = true;
                            $scope.payment[10] = getGCAmount($scope.giftCards);
                        }
                    });
                    if ($scope.checkGiftCardVerifyOTP()) {
                        socketUtils.emitMessage({GIFT_CARDS_OTP_VERIFY: null});
                        AppUtil.otpStatus.text = "Please ask customer to enter OTP on customer screen.";
                        AppUtil.otpStatus.status = 0;
                    }
                };

                $scope.checkOTPStatus = function () {
                    $scope.showLoadingOnButton = true;
                    socketUtils.emitMessage({GIFT_CARDS_OTP_CHECK: null});
                    $timeout(function () {
                        $scope.showLoadingOnButton = false;
                    }, 3000);
                };

                $scope.calculateSodexoCoupon = function () {
                    $scope.payment[6] = undefined;
                    $scope.showNameField = false;
                    $scope.showCardDetails = false;
                    $scope.trDenominations.forEach(function (v) {
                        v.denominationCount = null;
                    });
                    $scope.payment[4] = 0;
                    var total = 0;
                    for (var index in $scope.payment) {
                        if ($scope.payment[index] > 0) {
                            total += $scope.payment[index];
                        }
                    }
                    var sodexoAmount = 0;
                    $scope.sodexoDenominations.forEach(function (v) {
                        if (v.denominationCount > 0) {
                            sodexoAmount += v.denominationCount * v.denominationValue;
                        }
                    });
                    if (total + sodexoAmount > $scope.transactionObj.paidAmount) {
                        $scope.extraVouchers = total + sodexoAmount - $scope.transactionObj.paidAmount;
                        $scope.payment[4] = sodexoAmount - $scope.extraVouchers;
                    } else {
                        $scope.extraVouchers = null;
                        $scope.payment[4] = sodexoAmount;
                    }
                };

                $scope.calculateTRCoupon = function () {
                    $scope.payment[6] = undefined;
                    $scope.showNameField = false;
                    $scope.showCardDetails = false;
                    $scope.sodexoDenominations.forEach(function (v) {
                        v.denominationCount = null;
                    });
                    $scope.payment[5] = 0;
                    var total = 0;
                    for (var index in $scope.payment) {
                        if ($scope.payment[index] > 0) {
                            total += $scope.payment[index];
                        }
                    }
                    var trAmount = 0;
                    $scope.trDenominations.forEach(function (v) {
                        if (v.denominationCount > 0) {
                            trAmount += v.denominationCount * v.denominationValue;
                        }
                    });
                    if (total + trAmount > $scope.transactionObj.paidAmount) {
                        $scope.extraVouchers = total + trAmount - $scope.transactionObj.paidAmount;
                        $scope.payment[5] = trAmount - $scope.extraVouchers;
                    } else {
                        $scope.extraVouchers = null;
                        $scope.payment[5] = trAmount;
                    }
                };

                function getKey(name) {
                    for (var i in $scope.transactionMetadata.paymentModes) {
                        var mode = $scope.transactionMetadata.paymentModes[i];
                        if (mode.name == name) {
                            return mode.id;
                        }
                    }
                }

                function getPaidTillNow() {
                    var tillNow = 0;
                    for (var i in $scope.payment) {
                        tillNow += !AppUtil.isEmptyObject($scope.payment[i]) ? $scope.payment[i] : 0;
                    }
                    return tillNow;
                }

                $scope.settleAmountDirectly = function (index,needsSettlementSlipNo) {
                    if (!$scope.paymentDone) {
                        // socketUtils.emitMessage({PAYMENT_FROM_POS: AppUtil.customerSocket});
                    }
                    $scope.currentPaymentModeId = index ;
                    $scope.askForBillSettlementNo(index,needsSettlementSlipNo);
                    $scope.resetGiftCards();
                    /*$scope.giftCards = [];
                    $scope.unusedGiftCards = [];*/
                    //$scope.showCardDetails = false;
                    $scope.logCashData();
                    for (var key in $scope.payment) {
                        if (!isNaN($scope.payment[key]) && key != getKey("Prepaid")) {
                            /*$scope.payment[key] = null;*/
                            delete $scope.payment[key];
                        }
                    }

                    if (index == 6) {
                        $scope.showNameField = true;
                        $scope.changeCreditAccount($scope.channelPartner);
                        //console.log(":::::::going for credit:::::::");
                    } else {
                        $scope.showNameField = false;
                    }
                    var paid = $scope.transactionObj.paidAmount - getPaidTillNow();
                    $scope.payment[index] = paid;
                    $scope.showSodexoDenoms = false;
                    $scope.showTRDenoms = false;
                    $scope.sodexoDenominations.forEach(function (v) {
                        v.denominationCount = null;
                    });
                    $scope.trDenominations.forEach(function (v) {
                        v.denominationCount = null;
                    });
                    $scope.extraVouchers = undefined;
                    $scope.isOrderSourceIdReqd = needsSettlementSlipNo!=undefined && needsSettlementSlipNo ?true :false;
                    if($scope.isOrderSourceIdReqd){
                        $scope.orderSourceId = null ;// reset order source id for which needsSettlementSlip is false
                    }
                };

                $scope.askForBillSettlementNo = function(paymentModeId,needsSettlementSlipNo){
                    if ($scope.isPaidEmployeeMeal && (paymentModeId !== 1)) {
                        $scope.openOrderSourceIdInput = true;
                    } else if (!$scope.isPaidEmployeeMeal && needsSettlementSlipNo) {
                        $scope.openOrderSourceIdInput = true;
                    }else{
                        $scope.openOrderSourceIdInput=false;
                    }
                }
                $scope.resetGiftCards = function () {
                    $scope.giftCards = [];
                    $scope.unusedSelfCards.map(function (card) {
                        card.balance = card.currentValue;
                        card.amount = 0;
                        card.applied = false;
                    });
                };

                $scope.logCashData = function () {
                    var remaining = $scope.transactionObj.paidAmount;
                    var paid = 0;
                    for (var key in $scope.payment) {
                        if (!isNaN($scope.payment[key])) {
                            paid += $scope.payment[key];
                        }
                    }
                    if (paid) {
                        remaining = remaining - paid;
                    } else {
                        paid = $scope.transactionObj.paidAmount;
                        remaining = 0;
                    }
                    var data = 'amount:'
                        + parseFloat(paid)
                        + ',bill:'
                        + $scope.transactionObj.paidAmount
                        + ',change:'
                        + remaining;
                    productService.metaDataList.appendAttribute("SETTLEMENT_AMOUNT", data);
                };


                function getGCAmount(giftCards) {
                    var amount = 0;
                    if (giftCards.length > 0) {
                        for (var i in giftCards) {
                            amount += giftCards[i].amount;
                        }
                    }
                    return amount;
                }

                $scope.removeGiftCard = function (giftCards, index) {
                    var removedcard = giftCards.splice(index, 1);
                    $scope.payment[10] = getGCAmount($scope.giftCards);
                    if ($scope.payment[10] <= 0) {
                        delete $scope.payment[10];
                    }
                    $scope.unusedSelfCards.map(function (card) {
                        if (card.giftCard == removedcard[0].giftCard) {
                            card.applied = false;
                        }
                    });
                };

                //TODO  unused selfcards :
                $scope.removeUnusedGiftCard = function (unusedGiftCards, index) {
                    unusedGiftCards.splice(index, 1);
                    $scope.unusedGiftCardNumbers.splice(index, 1);
                };

                $scope.validateCard = function (card) {
                    $scope.giftCardError = "";
                    if (!AppUtil.isEmptyObject(card) && card.toString().length > 0) {
                        $scope.giftCard = card.toUpperCase();

                        var alreadyAdded = $scope.giftCards.filter(function (singleCard) {
                            return singleCard.giftCard == $scope.giftCard;
                        });

                        if (alreadyAdded.length > 0) {
                            $scope.showCardError = true;
                            $scope.giftCardError = "This card is already added. Add a different card.";
                        } else {

                            var customerId = AppUtil.customerSocket != undefined ? AppUtil.customerSocket.id : 5;
                            if (customerId > 5) {
                                posAPI.allUrl('/', AppUtil.restUrls.order.getGiftCard)
                                    .customGET("", {cardNumber: $scope.giftCard, customerId: customerId})
                                    .then(function (response) {
                                        $scope.showCardError = false;
                                        var dataObj = response.plain();
                                        if (dataObj.errorType != undefined) {
                                            $scope.giftCard = null;
                                            $scope.showCardError = true;
                                            $scope.giftCardError = dataObj.errorMessage;
                                        } else {
                                            $scope.giftCardError = null;
                                            if ($scope.unusedGiftCardNumbers.indexOf(dataObj.cardNumber) < 0) {
                                                $scope.unusedGiftCards.push({
                                                    giftCard: dataObj.cardNumber,
                                                    id: dataObj.cashCardId,
                                                    initialValue: dataObj.cashInitialAmount,
                                                    currentValue: dataObj.cashPendingAmount,
                                                    amount: 0,
                                                    balance: dataObj.cashPendingAmount,
                                                    applied: false,
                                                    autoAdded: false,
                                                    maskedNumber: $scope.maskGiftCardNumber(dataObj.cardNumber)
                                                });
                                                $scope.reShuffleUnusedGiftCards();
                                            }
                                            $scope.giftCard = "";
                                        }
                                    }, function (err) {
                                        console.log(err);
                                    });
                            } else {
                                $scope.showCardError = true;
                                $scope.giftCardError = "Please ask the customer to register before using this card";
                            }
                        }
                    }
                };

                $scope.reShuffleUnusedGiftCards = function () {
                    var autoAddedCards = $scope.unusedGiftCards.filter(function (card) {
                        return card.autoAdded == true;
                    });
                    $scope.unusedGiftCards = $scope.unusedGiftCards.filter(function (card) {
                        return card.autoAdded == false;
                    });
                    $scope.unusedGiftCards = $scope.unusedGiftCards.concat(autoAddedCards);
                    $scope.unusedGiftCardNumbers = $scope.unusedGiftCards.map(function (card) {
                        return card.giftCard;
                    });
                    $scope.resetGiftCards();
                    $scope.payment[10] = 0;
                };

                $scope.channelFilter = function (item) {
                    if(!AppUtil.unitPartnerBrandMappingAvailable(item.id)) {
                        return false;
                    }
                    if (item.id == 14) {
                        return false;
                    }
                    var isPartnerOrder = $rootScope.isPartnerOrder != null && $rootScope.isPartnerOrder != undefined ? $rootScope.isPartnerOrder
                        : false;
                    if (isPartnerOrder) {
                        return !(item.name.toLowerCase().indexOf("chaayos") > -1);
                    } else {
                        return item.name.toLowerCase().indexOf("chaayos") > -1;
                    }
                    return true;
                };

                $scope.cName = function (csName) {
                    $scope.csName = csName;
                    $scope.customerName = csName.name;
                    var selectedChannelPartner = $scope.transactionMetadata.channelPartner.filter(function (partner) {
                        return partner.hasOwnProperty("creditAccount") ? partner.creditAccount == csName.id : false;
                    })[0];
                    $scope.$apply(function () {
                        $scope.channelPartner = selectedChannelPartner;
                    });
                };

                $scope.addCustomerName = function (name) {
                    productService.metaDataList.addAttribute("CUSTOMER_NAME_MANUALLY", name);
                    AppUtil.customerSocket = {name: name};
                };

                $scope.enterExternOrderId = function (channelPartnerOrder) {
                    if (channelPartnerOrder != null && channelPartnerOrder != "") {
                        $scope.channelPartnerOrder = channelPartnerOrder;
                    } else {
                        $scope.channelPartnerOrder = null;
                    }
                };
                $scope.isValidInput=function (channelPartnerCustomer){
                    var value = channelPartnerCustomer;
                    var regex =/^[a-zA-Z0-9]*$/;
                    console.log(value);
                    if(!regex.test(value))
                        return true;

                    return false;
                }
                $scope.enterExternCustomerId = function (channelPartnerCustomer) {
                    if (channelPartnerCustomer != null && channelPartnerCustomer != "") {
                        if (!$scope.isValidInput(channelPartnerCustomer)) {
                            $scope.channelPartnerCustomer = channelPartnerCustomer;
                        }
                        else{
                            channelPartnerCustomer="";
                            $scope.channelPartnerCustomer = null;

                        }

                    }
                    else{
                        channelPartnerCustomer="";
                        $scope.channelPartnerCustomer = null;

                    }

                };

                function makeExternalSettlements(items, settlementObj) {

                    var externalSettlementArray = [];
                    items.forEach(function (item) {
                        externalSettlementArray.push({
                            amount: item.amount,
                            externalTransactionId: item.id
                        });
                    });
                    settlementObj.externalSettlements = externalSettlementArray;
                    return settlementObj;

                }

                $scope.checkGiftCardVerifyOTP = function () {
                    var currentCards = AppUtil.getCurrentTransactionGiftCards();
                    var autoAddedCards = $scope.giftCards.filter(function (card) {
                        return card.autoAdded == true && currentCards.indexOf(card.giftCard) < 0;
                    });
                    if (autoAddedCards.length > 0 && AppUtil.customerSocket != null && !AppUtil.customerSocket.otpVerified) {
                        return true;
                    } else {
                        return false;
                    }
                };

                $scope.submit = function () {
                    AppUtil.cardPaymentModule=false; // suggestGiftCardModule
                    $scope.submitWithPayment(null);
                };
                $scope.submitWithPayment = function (paymentDetail) {
                    if ($scope.manualBillBookNo && $scope.isBillNoValidationRequired) {
                        alert("Please verify the bill no you have entered.");
                        return false;
                    }
                    if($scope.isOrderSourceIdReqd && $scope.orderSourceId ==null){
                        alert("Please enter bill settlement slip no from bill");
                        return false ;
                    }
                    console.log("Order source ID ")
                    if ($scope.extraVouchers > 30) {
                        alert("Extra vouchers should not be more than 30 Rs.");
                        return false;
                    }
                    //added to allow employees to punch employee meals without entering name on customer screen
                    if (!$scope.isGiftCardModalOpen && $scope.showCustomerName && !AppUtil.isCOD() && (!AppUtil.customerSocket || AppUtil.customerSocket.name == null) && !$scope.isTableCheckout) {

                        alert("Please ask the customer to enter a name before submitting");
                        return false;
                    }
                    if ($scope.orderType == 'subscription' && ($scope.subscriptionStartDate == null || $scope.subscriptionStartDate == "")) {
                        alert("Please select start date.");
                        return false;
                    }
                    if ($scope.orderType == 'subscription' && $scope.settlementRemaining() > 0) {
                        alert("Please select payment option");
                        return false;
                    }
                    if ($scope.orderType == 'subscription' && $scope.subscriptionTimes.length == 0) {
                        alert("Please select subscription time.");
                        return false;
                    }
                    if ($scope.orderType == 'subscription' && $scope.subscriptionDays.length == 0) {
                        alert("Please select subscription days.");
                        return false;
                    }

                    if (AppUtil.isCOD() && $scope.channelPartner.creditAccount != null && $scope.channelPartnerOrder == null) {
                        bootbox.alert("Please enter Channel Partner Order ID before submitting");
                        return false;
                    }
                    if (AppUtil.isCOD() && $scope.channelPartner.creditAccount != null && $scope.channelPartner.id==6 && ($scope.channelPartnerCustomer == null || $scope.channelPartnerCustomer.length<4)) {
                        bootbox.alert("Please enter valid Channel Partner Customer ID before submitting");
                        return false;
                    }

                    if (AppUtil.isCOD() && $scope.channelPartner.id != null) {
                        if(!AppUtil.unitPartnerBrandMappingAvailable($scope.channelPartner.id)) {
                            bootbox.alert("No pricing unit found for brand: " + AppUtil.getSelectedBrand().brandName +
                                " and unit: " + AppUtil.getSelectedUnitIdName().name + " and partner: " +
                                AppUtil.getPartnerNameById($scope.channelPartner.id));
                            return false;
                        }
                    }

                    if ($scope.checkGiftCardVerifyOTP()) {
                        AppUtil.otpStatus.text = "Please ask customer to enter OTP on customer screen.";
                        AppUtil.otpStatus.status = 0;
                        return false;
                    }

                    $scope.submitted = true;
                    var totalSettled = 0;
                    var creationTime = moment().tz('Asia/Kolkata').format('YYYY-MM-DD HH:mm:ss');
                    //console.log("points redeemed in parent scope is :::::", $scope.pointsRedeemed);
                    var pointsRedeemed = $scope.pointsRedeemed;


                    $modalInstance.dismiss('submit');
                    var settlementArray = [];
                    if (paymentDetail) {
                        $scope.paymentId = paymentDetail.externalOrderId;
                        $scope.payment = {};
                        $scope.payment[paymentDetail.paymentModeId] = paymentDetail.transactionAmount;

                    }
                    for (var key in $scope.payment) {
                        if (typeof $scope.payment[key] !== 'undefined' && $scope.payment[key] != null) {

                            var settlementObj = {
                                mode: Math.floor(key),
                                amount: $scope.payment[key],
                                externalSettlements: null
                            };


                            if (key == 10 && $scope.giftCards.length > 0) {
                                settlementObj = makeExternalSettlements($scope.giftCards, settlementObj);
                            }

                            if (key == 6 && $scope.creditAccount && $scope.creditAccount.id != null) {
                                var externalSettlementArr = [{
                                    id: $scope.creditAccount.id,
                                    amount: $scope.payment[key]
                                }];
                                settlementObj = makeExternalSettlements(externalSettlementArr, settlementObj);
                            }
                            if (key == 27||key==13) {
                                var externalSettlementArr = [{
                                    id: $scope.paymentId,
                                    amount: $scope.payment[key]
                                }];
                                console.log("in settlement array for payment id:"+$scope.paymentId+" "+"and for amount:"+$scope.payment[key]);
                                settlementObj = makeExternalSettlements(externalSettlementArr, settlementObj);
                            }
                            settlementArray.push(settlementObj);
                        }

                    }


                    settlementArray.forEach(function (v) {
                        if (v.mode == 4 && v.amount > 0) {
                            v.extraVouchers = $scope.extraVouchers;
                            v.denominations = [];
                            $scope.sodexoDenominations.forEach(function (denom) {
                                if (denom.denominationCount > 0) {
                                    var item = {
                                        id: null,
                                        orderId: null,
                                        settlementId: null,
                                        denominationDetailId: denom.denominationId,
                                        count: denom.denominationCount,
                                        totalAmount: denom.denominationCount * denom.denominationValue
                                    };
                                    v.denominations.push(item);
                                }
                            });
                        } else if (v.mode == 5 && v.amount > 0) {
                            v.extraVouchers = $scope.extraVouchers;
                            v.denominations = [];
                            $scope.trDenominations.forEach(function (denom) {
                                if (denom.denominationCount > 0) {
                                    var item = {
                                        id: null,
                                        orderId: null,
                                        settlementId: null,
                                        denominationDetailId: denom.denominationId,
                                        count: denom.denominationCount,
                                        totalAmount: denom.denominationCount * denom.denominationValue
                                    };
                                    v.denominations.push(item);
                                }
                            });
                        }
                    });

                    for (var i = 0; i < settlementArray.length; i++) {
                        totalSettled += settlementArray[i].amount;
                    }

                    if (totalSettled != $scope.transactionObj.paidAmount) {
                        $scope.$parent.disableOrderCheckBtnGift = false;
                        AppUtil.myAlert("Transaction not settled");
                    } else {
                        //console.log("transaction object before sending for creation :: ", $scope.transactionObj);
                        if ($scope.transactionObj.discountDetail.discountCode == 0
                            && $scope.transactionObj.discountDetail.totalDiscount == 0) {
                            $scope.transactionObj.discountDetail.discountCode = null;
                        }


                        var orderItemResolvedArray = [];
                        var subscriptionDetail = {};
                        if ($scope.orderType == 'subscription') {
                            subscriptionDetail.subscriptionStatus = 'CREATED';
                            subscriptionDetail.startDate = $scope.subscriptionStartDate + " 00:00:00";
                            subscriptionDetail.endDate = ($scope.subscriptionEndDate == null ||
                                $scope.subscriptionEndDate == null) ? null : $scope.subscriptionEndDate + " 00:00:00";
                            subscriptionDetail.type = $scope.subscriptionFrequencyType;
                            subscriptionDetail.daysOfTheMonth = $scope.subscriptionFrequencyType == 'WEEKLY' ?
                                null : subscriptionService.processSubscriptionDays($scope.subscriptionDays);
                            subscriptionDetail.daysOfTheWeek = $scope.subscriptionFrequencyType == 'MONTHLY' ?
                                null : subscriptionService.processSubscriptionDays($scope.subscriptionDays);
                            subscriptionDetail.timeOfTheDay = subscriptionService.processSubscriptionTimes($scope.subscriptionTimes);
                            subscriptionDetail.emailNotification = $scope.emailNotification;
                            subscriptionDetail.smsNotification = $scope.smsNotification;
                            subscriptionDetail.automatedDelivery = $scope.automatedDelivery;
                            subscriptionDetail.overAllSavings = $scope.$parent.subscriptionDetailOverAll;
                            ////console.log("subscriptionDetail is :::",subscriptionDetail);
                        } else {
                            var offer =($scope.$parent.offerApplied || $scope.$parent.offerManual) ? $scope.$parent.offerCode : null;
                            if($scope.$parent.subscriptionDetailOverAll>0) {
                                subscriptionDetail.overAllSavings = $scope.$parent.subscriptionDetailOverAll;
                            }
                            else{
                                subscriptionDetail=null;
                            }
                        }
                        //console.log(productService.metaDataList.getList());

                        var customerId = AppUtil.customerSocket.id == 0 ? null : AppUtil.customerSocket.id;
                        if ($scope.isTableOrder && (customerId == null || customerId <= 5)) {
                            var table = tableService.getCurrentTable();
                            if (table.customerId > 5) {
                                customerId = table.customerId;
                            }
                        }
                        if (!AppUtil.isEmptyObject(customerId)) {
                            productService.metaDataList.removeAttribute("CUSTOMER_SCREEN_DOWN");
                        }
                        var metaDataList = productService.metaDataList.getList();

                        var billCreationSeconds = Math.ceil((new Date(moment().tz("Asia/Kolkata").format('YYYY-MM-DD HH:mm:ss')).getTime() - new Date($scope.orderStart).getTime()) / 1000);

                        var order = {
                            generateOrderId: $scope.$parent.randomOrderIdForUnit,
                            employeeId: AppUtil.GetLoggedInUser(),
                            externalOrderId: $scope.paymentId,
                            hasParcel: false,
                            status: $scope.getStatus(),
                            cancelReason: null,
                            orders: orderItemResolvedArray,
                            transactionDetail: $scope.transactionObj,
                            settlementType: "DEBIT",
                            source: $scope.getOrderSource(),
                            settlements: settlementArray,
                            unitId: $scope.unitDetails.id,
                            billStartTime: $scope.orderStart,
                            billCreationSeconds: billCreationSeconds,
                            billCreationTime: creationTime,
                            billingServerTime: creationTime,
                            channelPartner: $scope.channelPartner.id,
                            deliveryPartner: $scope.deliveryPartner,
                            pointsRedeemed: pointsRedeemed !== 0 ? -pointsRedeemed : 0,
                            terminalId: $rootScope.globals.currentUser.terminalId,
                            tableNumber: $scope.$parent.tableNumber,
                            tableRequestId: $scope.$parent.tableRequestId,
                            awardLoyalty: $scope.$parent.awardLoyalty,
                            offerCode: ($scope.$parent.offerApplied || $scope.$parent.offerManual) ? $scope.$parent.offerCode : null,
                            subscriptionDetail: subscriptionDetail,
                            customerId: customerId,
                            customerName: AppUtil.customerSocket.name,
                            enquiryItems: $scope.enquiryItems,
                            metadataList: metaDataList,
                            containsSignupOffer: AppUtil.customerSocket.eligibleForSignupOffer,
                            newCustomer: AppUtil.customerSocket.newCustomer,
                            optionResultEventId: null,
                            orderType: $rootScope.orderType,
                            billBookNo: $scope.manualBillBookNo,
                            cashRedeemed: $scope.$parent.cashRedeemed,
                            brandId: AppUtil.getSelectedBrand().brandId,
                            paymentDetailId: paymentDetail == null ? null : paymentDetail.orderPaymentDetailId
                        };
                        /*if($scope.recommendationDetail != undefined && $scope.recommendationDetail != null){
                            order.optionResultEventId = $scope.recommendationDetail.optionResultEventId
                        }*/

                        if(order.offerCode !== null && order.offerCode === 'CHAAYOS_SELECT'){
                            productService.metaDataList.addAttribute("CHAAYOS_SELECT_SETTLEMENT", moment().format());
                        }

                        if ($scope.orderRemark.trim() !== "") { // if order remark is not empty string
                            order.orderRemark = $scope.orderRemark;
                        }
                        if ($scope.showNameField) {
                            if ($scope.customerName !== undefined && $scope.customerName !== null && $scope.customerName.trim() !== "") { // if order remark is not empty string
                                $scope.orderRemark = $scope.orderRemark + " " + $scope.customerName;
                                order.orderRemark = $scope.orderRemark;
                            }
                        }
                        if (AppUtil.isCOD()) {
                            delete order.metadataList;
                            order.unitId = getCODUnit();

                            if (AppUtil.freeKettle) {
                                // loyaltea code should be put only when any other code is not applied
                                if (order.offerCode == null || order.offerCode == undefined || order.offerCode.trim() == "") {
                                    order.offerCode = AppUtil.freeKettleCode; // for availing free kettle
                                }
                            }
                            if ($scope.channelPartnerOrder != null) {
                                order.sourceId = $scope.channelPartnerOrder;
                            } else {
                                order.sourceId = $rootScope.globals.currentUser.unitId;
                            }
                            if ($scope.channelPartnerCustomer != null) {
                                order.partnerCustomerId = $scope.channelPartnerCustomer;
                            }

                            order.deliveryAddress = AppUtil.customerDeliveryAddress;
                            order.customerId = AppUtil.CSObj.id;
                            order.newCustomer = AppUtil.CSObj.newCustomer;
                            order.customerName = AppUtil.CSObj.firstName;
                        }
                        if($scope.getOrderSource()==="CAFE" || $scope.getOrderSource() ==="TAKE_AWAY"){
                            order.sourceId=$scope.orderSourceId;
                        }
                        console.log("order object before sending request", order);

                        if ($scope.isGiftCardModalOpen) {
                            order.isGiftOrder = true;
                        } else {
                            order.isGiftOrder = false;
                        }

                        if ($scope.isChaayosSelectModalOpen) {
                            order.isChaayosSelectOrder = true;
                        } else {
                            order.isChaayosSelectOrder = false;
                        }

                        if ($scope.isEmployeeMeal || AppUtil.isPaidEmployeeMeal()) {

                            var employeeId = AppUtil.GetLoggedInUser();
                            var employeeIdForMeal = $scope.$parent.employeeMealUser.id;
                            AppUtil.verifyLogin(employeeId, false, false, function (result) {
                                //console.log('Verification Status', result);
                                if (result.validated) {
                                    order.orders = AppUtil.addOrderItems($scope.orderItemArray);
                                    order.employeeMeal = true;
                                    order.employeeIdForMeal = employeeIdForMeal;
                                    sendRecommendationOrder(order, $modalInstance, $scope.goToCoverScreen, true);
                                } else {
                                    AppUtil.myAlert('Validation Failed for user ' + result.user.id);
                                }
                            });

                        } else if (!$scope.isEmployeeMeal && $scope.isSpecialOrder) {
                            order.orders = AppUtil.addOrderItems($scope.orderItemArray);
                            sendRecommendationOrder(order, $modalInstance, $scope.goToCoverScreen, true);
                        } else {
                            order.orders = AppUtil.addOrderItems($scope.orderItemArray);
                            order.pointsRedeemed = AppUtil.getOrderPointsRedeemed($scope.orderItemArray);
                            if ($scope.isTableCheckout) {
                                sendOrderSettlements(order);
                            } else if (!AppUtil.isCOD()) {
                                sendCafeOrder(order);
                            } else {
                                if ($scope.$parent != null) {
                                    $scope.$parent.enquiryItems = [];
                                }
                                sendRecommendationOrder(order, $modalInstance, $scope.goToCoverScreen, true);
                            }
                        }
                    }
                };

                $scope.getOrderSource = function () {
                    if (AppUtil.isCafe() || AppUtil.isDelivery()) {
                        if ($scope.channelPartner.id != 9) {
                            return "CAFE";
                        } else {
                            return "TAKE_AWAY";
                        }
                    }
                    if (AppUtil.isTakeaway()) {
                        return "TAKE_AWAY";
                    }
                    if (AppUtil.isCOD()) {
                        return "COD";
                    }
                };

                $scope.getStatus = function () {
                    if (AppUtil.isCOD() || AppUtil.isTakeaway() || AppUtil.isWorkstationEnabled()) {
                        return "CREATED";
                    } else {
                        return "SETTLED";
                    }
                };

                $scope.processPayment = function (index) {
                    if ($scope.paymentModeArray[index] == 'PAYTM') {
                        $scope.paymentMode = $scope.paymentModeArray[index];
                        var request = {generateOrderId: AppUtil.customerSocket.paytmOrderId, paymentPartner: "PAYTMQR"}

                        posAPI.allUrl('/', AppUtil.restUrls.order.checkPaymentStatus)
                            .post(request)
                            .then(function (response) {
                                    $scope.showLoadingOnButton = false
                                    if (response != null && response.paymentStatus !== undefined) {
                                        var payTMPaymentStaus = response.paymentStatus;
                                        if (payTMPaymentStaus === "TXN_SUCCESS") {
                                            $scope.settleAmountDirectly(13);
                                            $scope.paymentBanner = 'Transaction done...';
                                            AppUtil.customerSocket.paymentDone = true;
                                            $scope.paymentDone = true;
                                            $scope.paymentId = AppUtil.customerSocket.paytmOrderId;
                                        } else if (payTMPaymentStaus === "TXN_FAILURE") {
                                            $scope.paymentBanner = 'Transaction Failed...';
                                            AppUtil.customerSocket.paymentDone = false;
                                            if (index < $scope.paymentModeArray.length - 1) {
                                                $scope.processPayment(index + 1);
                                            }
                                        } else {
                                            $scope.paymentBanner = 'Transaction pending...';
                                            AppUtil.customerSocket.paymentDone = false;
                                            if (index < $scope.paymentModeArray.length - 1) {
                                                $scope.processPayment(index + 1);
                                            }

                                        }
                                    }
                                },
                                function (err) {
                                    $scope.showLoadingOnButton = false;
                                }
                            );
                    }
                    else if($scope.paymentModeArray[index] == 'GOOGLE PAY' || $scope.paymentModeArray[index] == 'GPAY'){
                        $scope.paymentMode = $scope.paymentModeArray[index];
                        var request = {
                            posId: $rootScope.globals.currentUser.googleMerchantId,
                            generateOrderId: AppUtil.customerSocket.gPayOrderId,
                            accessToken: AppUtil.customerSocket.accessToken
                        }

                        posAPI.allUrl('/', AppUtil.restUrls.order.generateGpayStatus)
                            .post(request)
                            .then(function (response) {
                                    $scope.showLoadingOnButton = false
                                    if (response != null && response.paymentStatus !== undefined) {
                                        var gPayPaymentStaus = response.paymentStatus;
                                        if (gPayPaymentStaus === "SUCCESSFUL") {
                                            $scope.settleAmountDirectly(27);
                                            $scope.paymentBanner = 'Transaction done...';
                                            AppUtil.customerSocket.paymentDone = true;
                                            $scope.paymentDone = true;
                                            $scope.paymentId = AppUtil.customerSocket.gPayOrderId;
                                        } else if (gPayPaymentStaus==="INITIATED") {
                                            $scope.paymentBanner = 'Transaction Pending...';
                                            AppUtil.customerSocket.paymentDone = false;
                                            if(index < $scope.paymentModeArray.length - 1) {
                                                $scope.processPayment(index + 1);
                                            }
                                        } else {
                                            $scope.paymentBanner = 'Transaction Failed...';
                                            AppUtil.customerSocket.paymentDone = false;
                                            if(index < $scope.paymentModeArray.length - 1) {
                                                $scope.processPayment(index + 1);
                                            }

                                        }
                                    }
                                },
                                function (err) {
                                    $scope.showLoadingOnButton = false;
                                }
                            );
                    }
                    else {
                        $scope.paymentMode = $scope.paymentModeArray[index];
                        var request = {
                            paymentSource: 'KETTLE_SERVICE',
                            contactNumber: AppUtil.customerSocket.contact,
                            customerName: AppUtil.customerSocket.name,
                            customerId: AppUtil.customerSocket.id,
                            posId: $scope.$parent.unitDetails.id + "#" + AppUtil.getAutoConfigData().selectedTerminalId,
                            paymentModeName: $scope.paymentMode,
                            ingenicoRequestType: 'O',
                            paidAmount: $scope.transactionObj.paidAmount,
                            generateOrderId: '',
                            cartId: $scope.$parent.orderIdForPayment,
                        }
                        request.generateOrderId = $scope.paymentModeArray[index] == 'BHARAT_QR' ? AppUtil.customerSocket.bharatOrderId : AppUtil.customerSocket.upiOrderId;
                        posAPI.allUrl('/', AppUtil.restUrls.order.getIngenicoStatus)
                            .post(request)
                            .then(function (response) {
                                    $scope.showLoadingOnButton = false;
                                    if (response && response.status == "SUCCESS") {
                                        $scope.settleAmountDirectly(25);
                                        $scope.paymentBanner = 'Transaction done...';
                                        AppUtil.customerSocket.paymentDone = true;
                                        AppUtil.customerSocket.externalOrderId = $scope.paymentId;
                                        $scope.paymentDone = true;
                                        $scope.paymentId = request.generateOrderId;
                                    } else if (response && response.status == "FAILED") {
                                        $scope.paymentBanner = 'Transaction Failed...';
                                        AppUtil.customerSocket.paymentDone = false;
                                        if (index < $scope.paymentModeArray.length - 1) {
                                            $scope.processPayment(index + 1);
                                        }
                                    }
                                    else {
                                        $scope.paymentBanner = 'Transaction pending...';
                                        AppUtil.customerSocket.paymentDone = false;
                                        if (index < $scope.paymentModeArray.length - 1) {
                                            $scope.processPayment(index + 1);
                                        }
                                    }
                                },
                                function (err) {
                                    $scope.showLoadingOnButton = false;
                                }
                            );
                    }
                }

                $scope.checkPaymentStatus = function () {
                    if ($scope.paymentModeArray.length > 0) {
                        $scope.showLoadingOnButton = true;
                        $scope.processPayment(0);
                    }
                }

                $scope.checkPaymentStatus = function () {
                    if ($scope.paymentModeArray.length > 0) {
                        $scope.showLoadingOnButton = true;
                        $scope.processPayment(0);
                    }
                }

                $scope.cancelSettlement = function () {
                    AppUtil.cardPaymentModule=false; //SuggestGiftCardModule
                    var data = AppUtil.getProductsString(productService.orderItemArray);
                    productService.metaDataList.appendAttribute("SETTLEMENT_CANCELLED", data);
                    $scope.$parent.disableOrderCheckBtnGift = false;
                    if ($scope.checkGiftCardVerifyOTP()) {
                        socketUtils.emitMessage({GIFT_CARDS_OTP_VERIFY_CANCEL: AppUtil.customerSocket});
                    }
                    AppUtil.otpStatus.text = "";
                    AppUtil.otpStatus.status = -1;
                    if ($scope.isTableCheckout) {
                        $scope.orderCancelled();
                    }
                    // socketUtils.emitMessage({PAYMENT_MODAL_CLOSE: AppUtil.customerSocket});
                    $modalInstance.dismiss('dismiss');
                };

                function sendOrderSettlements(order) {
                    var table = tableService.getCurrentTable();
                    var payload = {};
                    payload.settlements = order.settlements;
                    payload.tableRequestId = table.tableRequestId;
                    $rootScope.showFullScreenLoader = true;
                    posAPI.allUrl('/', AppUtil.restUrls.order.tableCheckOut)
                        .post(payload)
                        .then(function (response) {
                                $rootScope.showFullScreenLoader = false;
                                if (response.errorType != undefined) {
                                    AppUtil.myAlert(response.errorMessage);
                                } else {
                                    if (AppUtil.isAndroid) {
                                        AppUtil.printAndroidReceipt([response.settlementReceipt]);
                                        //Android.printReceipt([response.settlementReceipt]);
                                    } else {
                                        PrintService.printOnBilling(response.settlementReceipt, response.printType);
                                    }
                                    AppUtil.mySuccessAlert("Settlements Processes Successfully");
                                }
                                AppUtil.backToCover();
                            },
                            function (err) {
                                $rootScope.showFullScreenLoader = false;
                                AppUtil.myAlert(err.data.errorMessage);
                            }
                        );
                    socketUtils.emitMessage({
                        ORDER_PLACED: AppUtil.customerSocket
                    });
                }

                function sendCafeOrder(order) {
                    var requestObj = $rootScope.globals.currentUser;
                    $rootScope.showFullScreenLoader = true;
                    posAPI.allUrl('/', AppUtil.restUrls.order.orderSession)
                        .post(requestObj)
                        .then(function (response) {
                                $rootScope.showFullScreenLoader = false;
                                if (response == undefined || response == null) {
                                    bootbox.alert("Unable to create Order Session, Check Internet Connection.");
                                }
                                //Check for customer Session
                                var unitDetailSession = response.plain();
                                ////console.log(response.plain());
                                var sendOrder = true;
                                //if (unitDetailSession.customer == null || unitDetailSession.customer.emailId == null) {
                                sendCurrentOrder(order);
                            },
                            function (err) {
                                $rootScope.showFullScreenLoader = false;
                                AppUtil.myAlert(err.data.errorMessage);
                            }
                        );
                }

                function sendCurrentOrder(order) {
                    if ($scope.$parent != null) {
                        $scope.$parent.enquiryItems = [];
                    }
                    var customerCheck = (AppUtil.customerSocket.id != null && AppUtil.customerSocket.id != 0
                        && AppUtil.customerSocket.email != null);
                    sendRecommendationOrder(order, $modalInstance, $scope.goToCoverScreen, customerCheck);
                }

                function sendRecommendationOrder(order, $modalInstance, callback, customerPresent) {
                    /*if(!AppUtil.isCOD()){
                            //var hasRecommendedItem = false;
                            //$scope.recommendationDetail.quantity = 0;
                            if($scope.recommendationDetail != undefined && $scope.recommendationDetail != null){
                                $scope.orderItemArray.forEach(function(item){
                                    if(item.orderDetails.recommended != undefined
                                            && item.orderDetails.recommended != null && item.orderDetails.recommended){
                                        hasRecommendedItem = true;
                                        $scope.recommendationDetail.quantity += item.orderDetails.quantity;
                                    }
                                });
                                posAPI.allUrl('/',AppUtil.restUrls.rules.updateRecommendation)
                                    .post($scope.recommendationDetail).then(function (response) {
                                        console.log("Recommendation Saved Successfully");
                                    }, function (err) {
                                    console.log("Error while getting recommendation for the customer");
                                });

                            }

                            productService.metaDataList.addAttribute("RECOMMENDATION_AVAILED",hasRecommendedItem);
                            productService.metaDataList.addAttribute("RECOMMENDATION_ACTION",$scope.recommendationDetail.appliedBy);

                    }*/

                    if (order.employeeMeal != true && !order.isGiftOrder && !order.isChaayosSelectOrder) {
                        socketUtils.emitMessage({
                            ORDER_PLACED: AppUtil.customerSocket
                        });
                    }

                    order.metadataList = productService.metaDataList.getList();
                    if (!AppUtil.isAndroid && AppUtil.isDev()) {
                        swiggyOrderService.createSwiggyOrder(order, $scope.orderItemArray);
                    }
                    AppUtil.sendOrder(order, $modalInstance, callback, customerPresent, productService.getOrderArray());
                }

                $scope.orderCancelled = function () {
                    socketUtils.emitMessage({
                        ORDER_CANCELLED: AppUtil.customerSocket
                    });
                };
                $scope.addbillSettlementNoToOrderSourceId=function(orderSourceId){
                    if(orderSourceId !==undefined && orderSourceId!==null ){
                        $scope.orderSourceId =orderSourceId;
                    }
                }

                $scope.getSubscriptionFrequency = function (frequencyType) {
                    $scope.subscriptionDays = [];
                    $scope.subscriptionFrequencyType = frequencyType;
                    $scope.setFrequencyDays();
                };

                $scope.addSubscriptionDay = function (index) {
                    if ($scope.subscriptionDays.indexOf(index) > -1) {
                        $scope.subscriptionDays.splice($scope.subscriptionDays.indexOf(index), 1);
                    } else {
                        $scope.subscriptionDays.push(index);
                    }
                };

                $scope.addFrequencyDay = function (frequencyDay) {
                    var contains = false;
                    $scope.subscriptionDays.forEach(function (v) {
                        if (v.id === frequencyDay.id) {
                            contains = true;
                        }
                    });
                    if (!contains) {
                        $scope.subscriptionDays.push(frequencyDay);
                    }
                };

                $scope.removeSubscriptionDay = function (frequencyDay) {
                    $scope.allDays = false;
                    var days = [];
                    $scope.subscriptionDays.forEach(function (v) {
                        if (v.id !== frequencyDay.id) {
                            days.push(v);
                        }
                    });
                    $scope.subscriptionDays = days;
                };

                $scope.addFrequencyTime = function (frequencyTime) {
                    var contains = false;
                    $scope.subscriptionTimes.forEach(function (time) {
                        if (time.id === frequencyTime.id) {
                            contains = true;
                        }
                    });
                    if (!contains) {
                        $scope.subscriptionTimes.push(frequencyTime);
                    }
                };

                $scope.removeSubscriptionTime = function (frequencyTime) {
                    var times = [];
                    $scope.subscriptionTimes.forEach(function (v) {
                        if (v.id !== frequencyTime.id) {
                            times.push(v);
                        }
                    });
                    $scope.subscriptionTimes = times;
                };

                $scope.setSubscriptionStartDate = function (subscriptionStartDate) {
                    $scope.subscriptionStartDate = subscriptionStartDate;
                };

                $scope.setSubscriptionEndDate = function (subscriptionEndDate) {
                    $scope.subscriptionEndDate = subscriptionEndDate;
                };

                $scope.addAllDays = function (allDays) {
                    if (allDays) {
                        $scope.subscriptionDays = angular.copy($scope.frequencyDays);
                    } else {
                        $scope.subscriptionDays = [];
                    }
                };

                $scope.notifyEmail = function (emailNotification) {
                    $scope.emailNotification = emailNotification;
                };

                $scope.notifySMS = function (smsNotification) {
                    $scope.smsNotification = smsNotification;
                };

                $scope.automateDelivery = function (automatedDelivery) {
                    $scope.automatedDelivery = automatedDelivery;
                };

                $scope.enterContact = function (contact) {
                    $scope.paymentContactNumber = contact;
                };

                $scope.associatedPaymentForDineIn = function () {
                    $scope.associatedPayment(AppUtil.customerSocket.contact);
                }
                $scope.associatedPayment = function (contact) {

                    if (contact != undefined && contact != null && contact.trim().length > 0) {
                        $rootScope.showFullScreenLoader = true;
                        posAPI.allUrl('/', AppUtil.restUrls.paymentManagement.hangingPayment)
                            .post(contact)
                            .then(function (response) {
                                    if (response != undefined) {
                                        var payment = response.plain();
                                        if (payment != undefined && payment != null) {
                                            $scope.paymentDetail = payment;
                                        }
                                    } else {
                                        $scope.notFound = true;
                                    }
                                    $rootScope.showFullScreenLoader = false;
                                },
                                function (err) {
                                    $rootScope.showFullScreenLoader = false;
                                    AppUtil.myAlert(err.data.errorMessage);
                                }
                            );
                    } else {
                        bootbox.alert("Enter a valid number to search");
                    }

                };


                function getCODUnit() {
                    var unitId = $scope.unitDetails.id;
                    if (AppUtil.outlet.selectedId == 1) {
                        unitId = AppUtil.outlet.pri_unitId;
                    } else if (AppUtil.outlet.selectedId == 2) {
                        unitId = AppUtil.outlet.sec_unitId;
                    } else {
                        unitId = AppUtil.outlet.ter_unitId;
                    }
                    return unitId;
                }

                $scope.billNoChangeListener = function (manualBillBookNo) {
                    $scope.manualBillBookNo = manualBillBookNo;
                    $scope.isBillNoValidationRequired = true;
                }

                $scope.loader = {
                    loading: false,
                };

                $scope.validateManualBillBookNo = function (manualBillBookNo) {
                    showloader();
                    var dataMap = {};
                    dataMap.unitId = $rootScope.globals.currentUser.unitId;
                    dataMap.manualBillBookNo = manualBillBookNo
                    posAPI.allUrl('/', AppUtil.restUrls.manualBillBook.validateBillBookNo)
                        .post(dataMap)
                        .then(
                            function (response) {
                                hideloader();
                                if (response != undefined && response.errorType != undefined) {
                                    AppUtil.myAlert("Error while validating manual order number :" + response.errorType + ' : ' + response.errorMessage);
                                } else {
                                    if (response.success) {
                                        $scope.isBillNoValidationRequired = false;
                                        AppUtil.myAlert("Verified the bill number successfully!");
                                    } else {
                                        AppUtil.myAlert("Verification Failed : " + response.reasonForFailure);
                                    }
                                }
                            },
                            function (err) {
                                hideloader();
                                AppUtil
                                    .myAlert(err.data.errorMessage);
                            });
                };

                function showloader() {
                    $scope.loader.loading = true;
                }

                function hideloader() {
                    $scope.loader.loading = false;
                }

                $scope.associateAndCreate = function (paymentDetail) {
                    var creationTime = moment().tz('Asia/Kolkata').format('YYYY-MM-DD HH:mm:ss');
                    var billCreationSeconds = Math.ceil((new Date(moment().tz("Asia/Kolkata").format('YYYY-MM-DD HH:mm:ss')).getTime() - new Date($scope.orderStart).getTime()) / 1000);

                    var order = {
                        generateOrderId: $scope.$parent.randomOrderIdForUnit,
                        employeeId: AppUtil.GetLoggedInUser(),
                        status: "CREATED",
                        cancelReason: null,
                        orders: AppUtil.addOrderItems($scope.orderItemArray),
                        transactionDetail: $scope.transactionObj,
                        settlementType: "DEBIT",
                        source: "COD",
                        settlements: [{
                            mode: paymentDetail.paymentModeId,
                            amount: paymentDetail.transactionAmount
                        }],
                        unitId: getCODUnit(),
                        billStartTime: $scope.orderStart,
                        billCreationSeconds: billCreationSeconds,
                        billCreationTime: creationTime,
                        billingServerTime: creationTime,
                        channelPartner: $scope.channelPartner.id,
                        deliveryPartner: $scope.deliveryPartner,
                        pointsRedeemed: $scope.pointsRedeemed !== 0 ? -$scope.pointsRedeemed : 0,
                        terminalId: $rootScope.globals.currentUser.terminalId,
                        offerCode: ($scope.$parent.offerApplied || $scope.$parent.offerManual) ? $scope.$parent.offerCode : null,
                        customerId: paymentDetail.customerId,
                        customerName: paymentDetail.customerName,
                        deliveryAddress: AppUtil.customerDeliveryAddress,
                        newCustomer: AppUtil.CSObj.newCustomer,
                        paymentDetailId: paymentDetail.orderPaymentDetailId
                    };


                    AppUtil.sendOrder(order, $modalInstance, $scope.goToCoverScreen, true);
                };

                function updatePartnerDetails() {
                    $scope.isChannelPartnerOrder = true;
                    for (var i = 0; i < $scope.transactionMetadata.channelPartner.length; i++) {
                        if ($scope.transactionMetadata.channelPartner[i].id == AppUtil.activeChannelPartnerId) {
                            $scope.channelPartner = $scope.transactionMetadata.channelPartner[i];
                            break;
                        }
                    }
                    if (AppUtil.activeChannelPartnerId == 6) {
                        $scope.deliveryPartner = 5;
                    } else {
                        if (angular.isDefined(AppUtil.defaultDeliveryDetail) && angular.isDefined(AppUtil.defaultDeliveryDetail[AppUtil.activeChannelPartnerId])
                            && angular.isDefined(AppUtil.defaultDeliveryDetail[AppUtil.activeChannelPartnerId].code)) {
                            for (var i = 0; i < $scope.transactionMetadata.deliveryPartner.length; i++) {
                                if ($scope.transactionMetadata.deliveryPartner[i].id == AppUtil.defaultDeliveryDetail[AppUtil.activeChannelPartnerId].code) {
                                    $scope.deliveryPartner = $scope.transactionMetadata.deliveryPartner[i].id;
                                }
                            }
                        }
                    }
                }

                $scope.convertToSelfCards = function (card) {
                    for (var i = 0; i < $scope.unusedGiftCards.length; i++) {
                        if ($scope.unusedGiftCards[i].giftCard == card.giftCard) {
                            $scope.unusedGiftCards.splice(i, 1);
                        }
                    }
                    for (var i = 0; i < $scope.unusedGiftCardNumbers.length; i++) {
                        if ($scope.unusedGiftCardNumbers[i] == card.giftCard) {
                            $scope.unusedGiftCardNumbers.splice(i, 1);
                        }
                    }
                    $scope.unusedSelfCards.push(card);
                    $scope.unusedSelfCardNumbers.push(card.giftCard);
                };

                $scope.removeFromSelfCards = function (card) {
                    for (var i = 0; i < $scope.unusedSelfCards.length; i++) {
                        if ($scope.unusedSelfCards[i].giftCard == card.giftCard) {
                            $scope.unusedSelfCards.splice(i, 1);
                        }
                    }
                    for (var i = 0; i < $scope.unusedSelfCardNumbers.length; i++) {
                        if ($scope.unusedSelfCardNumbers[i] == card.giftCard) {
                            $scope.unusedSelfCardNumbers.splice(i, 1);
                        }
                    }
                    $scope.unusedGiftCards.push(card);
                    $scope.unusedGiftCardNumbers.push(card.giftCard);
                };
                if($scope.giftCardModalOpen){
                    $scope.payViaGiftCard();
                }


            }]);



