/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';
angular
	.module('posApp')
	.controller(
		'paidEmployeeMealModalCtrl',
		['$rootScope', '$scope', '$modalInstance', 'AppUtil', 'posAPI', '$location', '$timeout',
			function($rootScope, $scope, $modalInstance, AppUtil, posAPI, $location, $timeout) {

			$scope.init = function() {
				$scope.errorMessage = null;
				$scope.otpRequested = false;
				$scope.selectedEmployee = null;
				posAPI.allUrl('/',AppUtil.restUrls.userManagement.empMealUsers)
					.post($scope.$parent.unitDetails.id)
					.then(
						function(response) {
							$scope.employeeDetails = response;
						}, function(err) {
							AppUtil.myAlert(err.data.errorMessage);
						});
			};

			$scope.getEmployeeData = function(emp) {
				$scope.selectedEmployee = null;
				posAPI.allUrl('/',AppUtil.restUrls.order.empAllowancelimit)
					.post(emp.id)
					.then(
						function(response) {
							$scope.selectedEmployee = emp;
							$scope.selectedEmployee.mealAllowanceLimit = response;
						}, function(err) {
							AppUtil.myAlert(err.data.errorMessage);
						});
			};

			$scope.isValidContact = function() {
				return $scope.selectedEmployee != null && $scope.selectedEmployee.contactNumber != undefined &&
					$scope.selectedEmployee.contactNumber != null && $scope.selectedEmployee.contactNumber.length == 10;
			};

			$scope.requestOTP = function() {
				$scope.errorMessage = null;
				var contact = $scope.selectedEmployee.contactNumber;
				$scope.otpError = null;
				posAPI.allUrl('/',AppUtil.restUrls.customer.generateOTP).post({
					contactNumber : contact
				}).then(function(response) {
					if (response) {
						$scope.mealOTP = null;
						$scope.otpRequested = true;
					} else {
						$scope.showErrorMessage("Could not send OTP. Try again.");
					}
				}, function(err) {
					$rootScope.showFullScreenLoader = false;
					AppUtil.myAlert(err.data.errorMessage);
				});
			};

			$scope.showErrorMessage = function(text) {
				$scope.errorMessage = text;
				$timeout(function() {
					$scope.errorMessage = null;
				}, 4000)
			}

			$scope.verifyOTP = function(otp) {
				posAPI.allUrl('/',AppUtil.restUrls.customer.verifyOTP).post({
					contactNumber : $scope.selectedEmployee.contactNumber,
					otpPin : otp,
					unit : AppUtil.getUnitDetails().id
				}).then(function(response) {
					//$scope.mealOTP = null;
					if (response == true) {
						$modalInstance.close($scope.selectedEmployee);
					} else {
						$scope.showErrorMessage("Incorrect OTP. Please Try again.");
					}
				}, function(err) {
					$rootScope.showFullScreenLoader = false;
					AppUtil.myAlert(err.data.errorMessage);
				});
			};

			$scope.goBack = function() {
				if (!AppUtil.isCOD()) {
					$location.url('/cover');
				} else {
					$location.url('/CODCover');
				}
				$modalInstance.close();
			};
		}]);