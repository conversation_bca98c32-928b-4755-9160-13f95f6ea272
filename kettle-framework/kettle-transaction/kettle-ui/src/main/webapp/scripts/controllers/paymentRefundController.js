/* __________________
*
* [2015] - [2017] Sunshine Teahouse Private Limited
* All Rights Reserved.
*
* NOTICE:  All information contained herein is, and remains
* the property of Sunshine Teahouse Private Limited and its suppliers,
* if any.  The intellectual and technical concepts contained
* herein are proprietary to Sunshine Teahouse Private Limited
* and its suppliers, and are protected by trade secret or copyright law.
* Dissemination of this information or reproduction of this material
* is strictly forbidden unless prior written permission is obtained
* from Sunshine Teahouse Private Limited.
*/

angular.module('posApp')
    .controller('paymentRefundController', ['$rootScope','$scope','AppUtil','$location','$http',
        'posAPI','$modal',
        function ($rootScope,$scope,AppUtil,$location,$http) {
            $scope.searchNumber = null;
            $scope.init = function(){
                $scope.resultFound = false;
                $scope.refundSuccess=false;
            };

            $scope.backToCover = function(){
                $location.url('/cover');
            };

            $scope.GetSearchedContact=function (){
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.paymentManagement.refundApplicable,
                    data: $scope.searchNumber
                }).then(function (response) {
                    console.log(response)
                    $scope.contactResponse = response.data;
                    $rootScope.showFullScreenLoader = false;
                    if(angular.equals([], $scope.contactResponse.applicable) && angular.equals([], $scope.contactResponse.toBeApplicable)){
                        console.log($scope.contactResponse.applicable);
                        $scope.resultFound = false;
                    }
                    else{
                        $scope.resultFound = true;
                        $scope.refundSuccess=false;
                        if(!angular.equals([], $scope.contactResponse.toBeApplicable)){
                            for(var i = 0; i <  $scope.contactResponse.toBeApplicable.length; i++){
                                var today = new Date();
                                var diffMs = (today.getTime() - $scope.contactResponse.toBeApplicable[i].requestTime);
                                var diffMins = Math.round(((diffMs % 86400000) % 3600000) / 60000);
                                $scope.contactResponse.toBeApplicable[i]['remainingMinutes'] = 8 - diffMins;
                                console.log($scope.contactResponse.toBeApplicable[i]);
                            }
                        }

                    }
                }, function (err) {
                    $rootScope.showFullScreenLoader = false;
                   console.log("error found",err);
                });
            };

            $scope.refundPayment = function(paymentObj){
                console.log(paymentObj);
                $rootScope.showFullScreenLoader = true;
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.paymentManagement.refund,
                    data: paymentObj.orderPaymentDetailId
                }).then(function (response) {
                    $rootScope.showFullScreenLoader = false;
                    if(response!=undefined){
                        payment = response.data;
                        if (payment.paymentStatus == "REFUND_PROCESSED" || payment.paymentStatus == "REFUND_INITIATED"){
                            paymentObj.paymentStatus = payment.paymentStatus;
                            bootbox.alert("Refund successfully processed on " + payment.paymentPartner);
                            $scope.refundSuccess=true;
                        }else{
                            bootbox.alert("Refund cannot be processed for " + payment.paymentPartner +". Please contact Tech Team for further support");
                        }
                    }else{
                        bootbox.alert("Refund cannot be processed for " + payment.paymentPartner +". Please contact Tech Team for further support");
                    }
                }, function (err) {
                    $rootScope.showFullScreenLoader = false;
                    console.log("Encountered error while getting pending refunds", err);
                });
            }

        }
    ]);