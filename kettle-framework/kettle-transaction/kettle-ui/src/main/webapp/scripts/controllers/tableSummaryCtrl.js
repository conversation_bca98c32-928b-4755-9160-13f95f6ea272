/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';
/* jshint sub:true */

angular
		.module('posApp')
		.controller(
				'tableSummaryCtrl',
				[
						'$rootScope',
						'$scope',
						'$filter',
						'AppUtil',
						'$location',
						'$http',
						'$compile',
						'posAPI',
						'$modal',
						'tableService',
						'desiChaiService',
						'productService',
						'$interval',
						'socketUtils',
						function($rootScope, $scope, $filter, AppUtil,
								$location, $http, $compile, posAPI, $modal,
								tableService, desiChaiService, productService, $interval, socketUtils) {

							$scope.init = function() {
								$scope.createMetadata();
								$scope.currentTable = tableService
										.getCurrentTable();
								$scope.tableSummary = null;
								if (!AppUtil.isEmptyObject($scope.currentTable)) {
									$scope.openTable($scope.currentTable);
								} else {
									$scope.backToCover();
								}

							};

							
							$scope.createMetadata = function() {
								$scope.transactionMetadata = AppUtil
										.getTransactionMetadata();
								$scope.transactionObj = productService
										.getTransactionObject();
								AppUtil.customerSocket = AppUtil
										.resetCustomerSocket();
								//AppUtil.customerSocket.name = 'ChaayosTableService';
								$scope.customerSocket = AppUtil.customerSocket;
								$scope.unitDetails = AppUtil.getUnitDetails();
								$scope.orderRemark = '';
								$scope.orderItemArray = [];
								$scope.otpStatus = AppUtil.resetOtpStatus();
								$scope.tableService = AppUtil.hasTableService();
								$scope.hasExtendedTableService = AppUtil.hasExtendedTableService();
								$scope.customerPendingCardInfo = null;
							};

							$scope.openTable = function(table) {
								if (table.tableRequestId == null
										|| table.tableRequestId == 0) {
									$scope.reserveTable(table);
								} else {
									$scope.openTableSummary(table);
								}
							};

							$scope.reserveTable = function(table) {
								var url = AppUtil.restUrls.order.reserveTable;
								posAPI.allUrl('/', url).customGET("", {
									tableNumber : table.tableNumber,
									unitId : table.unitId
								}).then(function(response) {
									if (response.errorType != undefined && response.errorType != '') {
					                    var msg = "Error selecting table: " + response.errorMessage;
					                    AppUtil.myAlert(msg);
					                    $scope.checkTables();
					                }else{
					                	$scope.tableSummary = response;
										tableService.setCurrentTable(response);
										$scope.currentTable = response;
					                }
								}, function(err) {
									AppUtil.myAlert(err.data.errorMessage);
									$scope.checkTables();
								});
							};
							
							$scope.openChangeTableModal = function() {
					            var modalInstance = $modal.open({
					                animation: true,
					                templateUrl: window.version+'views/tableChange.html',
					                controller: 'tableChangeCtrl',
					                backdrop: 'static',
					                size: 'lg'
					            });
					            modalInstance.result.then(function (selectedItem) {
					            	if(!AppUtil.isEmptyObject(selectedItem)){
					            		$scope.changeTable(selectedItem.tableNumber);
					            	}
					            }, function(){ });
					        }
					        
							$scope.changeTable = function(changeTableNumber) {
								var currentTable = tableService.getCurrentTable();
								var url = AppUtil.restUrls.order.changeTable;
								posAPI.allUrl('/', url).customGET("", {
									tableNumber : changeTableNumber,
									tableRequestId : currentTable.tableRequestId
								}).then(function(response) {
					                if (response.errorType != undefined && response.errorType != '') {
					                    var msg = "Error changing table: " + response.errorMessage;
					                    AppUtil.myAlert(msg);
					                }else{
					                	AppUtil.mySuccessAlert("Table Changed please select table again");
					                	$scope.checkTables();
					                }
								}, function(err) {
									AppUtil.myAlert(err.data.errorMessage);
									$scope.checkTables();
								});
							};

							$scope.openTableSummary = function(table) {
								var url = AppUtil.restUrls.order.getTableSummary;
								$scope.loadingData = true;
								posAPI.allUrl('/', url).customGET("", {
									tableRequestId : table.tableRequestId
								}).then(function(response) {
									$scope.tableSummary = response;
									tableService.setCurrentTable(response);
									$scope.currentTable = response;
									$scope.loadingData = false;
								}, function(err) {
									AppUtil.myAlert(err.data.errorMessage);
								});
							};

							$scope.checkTotalOrders = function() {
								if($scope.tableSummary != null){
									$scope.tableSummary.orders.length == 0;
								}
								return true;
							};
							
							
							$scope.backToCover = function() {
								tableService.setCurrentTable(null);
								$scope.closeTable(function(){
									$location.url('/cover');
								});
							};

							$scope.checkTables = function() {
								tableService.setCurrentTable(null);
								$scope.closeTable(function(){
									$location.url('/table');
								});
							};

							$scope.orderStart = function() {
								if (!AppUtil.isAndroid
										&& !AppUtil.checkIsMonk()
										&& !$rootScope.printerStatus) {
									bootbox
											.alert("<h3>Qz is not running!<br/>Please run Qz Tray</h3>");
								} else {

									AppUtil
											.getUnitProductsData(function() {
												if (desiChaiService
														.getDesiChaiProducts().length == 0) {
													desiChaiService
															.setRecipes();
												}
												$scope.releaseCustomerSocket();
												$scope.goToPosScreen();
											});
								}
							};
							
							$scope.releaseCustomerSocket = function(){
								$rootScope.orderType = "order";
								if($scope.currentTable.customerId == null || $scope.currentTable.customerId <= 5) {
									AppUtil.sendOrderStart();
								} else {
									$scope.sendTableOrder();
								}
							};
							
							$rootScope.sendTableOrder = function () {
			                    console.log("Sending table order start message");
			                    var customerObj = AppUtil.resetCustomerSocket();
			                    customerObj.contact = $scope.currentTable.contact;
			                    customerObj.customerId = $scope.currentTable.customerId;
			                    customerObj.name = $scope.currentTable.customerName;
			                    AppUtil.sendTableOrderStart(customerObj);
					        };

							$scope.goToPosScreen = function() {
								if (!AppUtil
										.isEmptyObject(AppUtil.unitDetails.products)) {
									$location.url('/pos');
								}
							};

							$scope.closeTable = function(callback) {
								if (!AppUtil.isEmptyObject($scope.currentTable)) {
									if ($scope.tableSummary.orders.length == 0) {
										var table = $scope.currentTable;
										var url = AppUtil.restUrls.order.closeTable;
										var params = {
											tableRequestId : table.tableRequestId
										};
										posAPI
												.allUrl('/', url)
												.customGET("", params)
												.then(
														function(response) {
															tableService
																	.setCurrentTable(null);
															callback();
														},
														function(err) {
															AppUtil
																	.myAlert(err.data.errorMessage);
														});
									} else {
										callback()
									}
								} else {
									callback();
								}
							};

							$scope.generateInvoice = function() {
								AppUtil.customerSocket = AppUtil.resetCustomerSocket();
								$scope.transactionObj.paidAmount = $scope.tableSummary.totalAmount;
								$scope.transactionObj.giftCardAmount = $scope.getGiftCardAmount();
								$scope.transactionObj.nonGcAmount = $scope.tableSummary.totalAmount - $scope.transactionObj.giftCardAmount;
								$scope.getCustomerCardInfo();
								$scope.settlementModalOpen();
							};
							
				            $scope.getCustomerCardInfo = function () {
				                var customerId = $scope.tableSummary.customerId;
				                if (customerId > 5) {
                                    posAPI
                                        .allUrl('/', AppUtil.restUrls.order.getCustomerCardInfo)
                                        .customGET("",{
                                            customerId: customerId,
                                            unitId: $scope.unitDetails.id
                                        }).then(function (response) {
                                            $scope.customerPendingCardInfo = response.plain();
                                        }, function (err) {
                                            AppUtil.myAlert(err.data.errorMessage);
                                        });
				                } else {
				                    $scope.customerPendingCardInfo = {
				                        hasCard: false,
				                        cardAmount: 0.00
				                    };
				                }
				            };
							
							$scope.getGiftCardAmount = function() {
								var giftCardAmount = 0;
								var order = null;
								var item = null;
								for(var i in $scope.tableSummary.orders) {
									order = $scope.tableSummary.orders[i];
									if(order.status.indexOf('CANCELLED') == -1) {
										for(var j in order.items){
											item = order.items[j];
											giftCardAmount = giftCardAmount + item.totalAmount;
										}
									}
								}
								return giftCardAmount;  
							};

							$scope.settlementModalOpen = function() {
								
								if($scope.currentTable.customerId != null || $scope.currentTable.customerId >= 5) {
									$scope.sendTableOrder();
								}
								
								var template = window.version
										+ "views/settlementModalPOS.html";
								$modal.open({
									animation : true,
									templateUrl : template,
									controller : 'settlementModalCtrl',
									backdrop : 'static',
									scope : $scope,
									size : "lg",
									resolve: {
										tableCheckout: function () {
                                            return true;
                                        }
                                    }
								});
							};
							
				            $scope.openOrderSearch = function (generateOrderId) {
				                AppUtil.openOrderSearch(generateOrderId,"tableSummary");
				            };

						} ]);