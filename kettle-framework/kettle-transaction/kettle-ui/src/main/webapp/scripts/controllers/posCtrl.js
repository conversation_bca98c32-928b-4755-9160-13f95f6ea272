/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *otpS
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';
/*jshint sub:true*/

angular.module('posApp')
    .controller('posCtrl', ['$rootScope', '$scope', 'productService', 'AppUtil', 'coverUtils', '$modal', '$location', '$cookieStore', 'posAPI', 'socketUtils',
        'trackingService', 'desiChaiService', '$interval', '$timeout', 'swiggyOrderService', 'tableService', '$filter', '$http','APIJson',
        function ($rootScope, $scope, productService, AppUtil, coverUtils, $modal, $location, $cookieStore, posAPI, socketUtils,
                  trackingService, desiChaiService, $interval, $timeout, swiggyOrderService, tableService, $filter, $http,APIJson) {

            // for saving last state of order when placing ecard order
            var lastOrderItemArray, lastTransactionObj, lastOfferCode;

            $scope.unitDetails = {};
            $scope.transactionMetadata = {};
            $scope.productsForSubCategory = {};
            $scope.getAllComplimentaryCodes = AppUtil.getAllComplimentaryCodes;
            $scope.orderItemArray = {};
            $scope.orderItemArrayAdv= {};
            $scope.transactionObj = {};
            $scope.subscriptionDetailOverAll = null;
            $scope.subscriptionDetailCoupon = null;
            $scope.subscriptionValidiltyDays = null;
            $scope.orderStart = '';
            $scope.offerCode = "";
            $scope.isDev = AppUtil.isDev;
            $scope.awardLoyalty = true;
            $scope.switchOtpBtn = false;
            $scope.appliedOfferMessage = "";
            $scope.cutomerInterested = true;
            $scope.offerManual = false;
            $scope.isCOD = AppUtil.isCOD();
            $scope.isEmployeeMeal = isEmployeeMeal();
            $scope.isPaidEmployeeMeal = AppUtil.isPaidEmployeeMeal();
            $scope.isComplimentaryOrder = isComplimentaryOrder();
            $scope.isWastageOrder = isWastageOrder();
            $scope.isSpecialOrder = isSpecialOrder();
            $scope.isUnsatisfiedCustomerOrder = isUnsatisfiedCustomerOrder();
            $scope.isCafe = AppUtil.isCafe();
            $scope.isTakeaway = AppUtil.isTakeaway();
            $scope.onlyOneOfferAlert = "Only one of either redemption or discount or Combo can be taken on one order";
            $scope.atLeastOneItem = "Please select at least one item";
            $scope.discountApplied = false;
            $scope.offerApplied = false;
            $scope.chaayosSelect = true;
            $scope.subProductName = null;
            //$scope.recommendationOfferApplied = false;
            $scope.redemptionApplied = false;
            $scope.checkForClearOffer = true;
            $scope.freeKettle = AppUtil.freeKettle;
            $scope.orderIdForPayment = 0;
            $scope.randomOrderIdForUnit = 0; // in case anything out of the normal happens 0 will be sent
            $scope.pointsRedeemed = 0;
            $scope.isNewCustomer = false;
            $scope.customerEnteredName = null;
            $scope.customerNameManual = false;
            $scope.offerApplicableForUnitExists = {};
            $scope.offerApplicableForUnitExists.showIcon=false;

            $scope.otpBtnHidden = true;
            $scope.customerRejectBtnHidden = false;
            $scope.hideFirstFreeChaiBtn = true;
            $scope.checkGiftCardInOrder = productService.checkGiftCardInOrder;

            $scope.customerId = null;
            $scope.toShowCustomerOneViewSection = false;
            $scope.lastOrder = [];
            $scope.lastOrderIds = [];
            $scope.recomProductList = [];
            $rootScope.customerFavChaiMappings =[];
            $scope.totalNPS = 0;
            $scope.expiryProducts = [];
            $scope.isLastOrderNPSFilled = false;
            $scope.totalFeedback = 0;
            $scope.isLastOrderFeedbackFilled = false;

            $scope.channelPartnerName = "";
            // $scope.showFeedback=false;
            // $scope.showFeedbackValue= null;
            $scope.dataShow = null;
            $scope.dataValue = null;

            $scope.isGiftCardModalOpen = false;
            $scope.isChaayosSelectModalOpen = false;
            $scope.isOfferModalopen = false;

            $scope.couponFlagChangeModule = false;// suggestGiftCardModule

            $scope.disableOrderCheckBtnGift = false;
            $scope.showEmailSkipBtn = false;
            var skipEmailForm = false;
            var isRedemptionDone = false;
            $scope.showFreeChaiDelivery = false;
            $scope.$parent.defaultPaymentModeId = 1;//1-->CASH

            $scope.fullComplimentaryDetail = {
                isOrderComplimentary: false,
                reasonCode: null,
                reason: null
            };
            $scope.paymentDisable = false;
            AppUtil.resetOtpStatus();

            $scope.productCategory = AppUtil.categoryList;
            console.log($scope.productCategory);
            $scope.categoryMap = JSON.parse(localStorage.getItem("categoryMap"));
            if ($rootScope.orderType != "complimentary-order" && $scope.categoryMap && $scope.categoryMap[1]) {
                $scope.productCategory = $scope.productCategory.filter(function (category) {
                    return $scope.categoryMap[1].includes(category.detail.id);
                });
            }


            $scope.isCheckInventory = function () {
                //return $scope.isCOD && $scope.checkInventory && $rootScope.orderType != "subscription";
                return $scope.checkInventory && $rootScope.orderType != "subscription";
            };

            function isEmployeeMeal() {
                return $rootScope.orderType == "employee-meal";
            }

            function isComplimentaryOrder() {
                return $rootScope.orderType == "complimentary-order";
            }

            function isWastageOrder() {
                return $rootScope.orderType == "wastage-order";
            }

            function isUnsatisfiedCustomerOrder() {
                return $rootScope.orderType == "unsatisfied-customer-order";
            }

            function isSpecialOrder() {
                return isEmployeeMeal() || isComplimentaryOrder() || isUnsatisfiedCustomerOrder() || isWastageOrder() || AppUtil.isPaidEmployeeMeal();
            }

            $scope.$on("customerScreenDown", function () {
                productService.metaDataList.addAttribute("CUSTOMER_SCREEN_DOWN", Date.now());
                $timeout(function () {
                    var msg = socketUtils.getPairingStatus();
                    $scope.isCustomerSocketConnected = msg;
                    $scope.customerNameManual = !msg;
                    $scope.customerRejectBtnHidden = !msg;
                });
            });

            /* $scope.$on('$destroy',function(){
                 socketUtils.removeAllListeners();
             });*/

            socketUtils.receiveMessage(function (message) {
                ////console.log(message);
                $scope.renderedTemplate = productService.customerSocketWidget.updateTemplate(message, function (points, isNew, key) {
                    message = message;
                    var data = message[key];

                    if (key == "SCREEN_OPENED") {
                        console.log("Cancelling order start interval from POS");
                        $rootScope.csScreenTimer = false;
                        $interval.cancel($rootScope.csTimeout);
                        $rootScope.orderStartCount = 0;
                        $timeout(function () {
                            $scope.isCustomerSocketConnected = true;
                            $scope.customerNameManual = false;
                            $scope.customerRejectBtnHidden = false;
                            socketUtils.setPairingStatus(true);
                        });
                    }

                    if (key == "BIRTHDAY_OFFER") {
                        bootbox.alert(data);
                    }

                    if (!AppUtil.isEmptyObject(data) &&
                        (!AppUtil.isEmptyObject(data.contact) || !AppUtil.isEmptyObject(data.name))) {
                        $scope.detailsEntered == true;
                        $scope.isCustomerSocketConnected = true;
                        $scope.customerNameManual = false;
                    }

                    if (data != null && data.name != undefined && data.name != null && $scope.customerEnteredName == null) {
                        $scope.customerEnteredName = data.name;
                        $scope.customerRejectBtnHidden = true;
                    }
                    if (isNew != undefined && isNew != null) {
                        $scope.isNewCustomer = isNew;
                        if ($scope.isNewCustomer == true) {
                            var coreProducts = AppUtil.getTransactionMetadata().coreProducts.content;
                            var coreProductsIds = [];
                            coreProducts.forEach(function (data) {
                                var productId = parseInt(data["code"]);
                                coreProductsIds.push(productId);
                            });
                            updateRecommendedProducts(coreProductsIds, "C");
                        }
                        //console.log("isNewCustomer after receival is ::::",$scope.isNewCustomer);
                    }
                    if (points != undefined && points != null) {
                        $scope.pointsRedeemed = points;
                        //console.log("points redeemed after receival is ::::",$scope.pointsRedeemed);
                    }

                    if (key != undefined && key != null) {
                        $scope.hideFirstFreeChaiBtn = true;
                        $scope.customerRejectBtnHidden = true;
                        if (key == "DETAILS_ENTERED") {
                            console.log("Cancelling order start interval from POS");
                            $rootScope.csScreenTimer = false;
                            $interval.cancel($rootScope.csTimeout);
                            $rootScope.orderStartCount = 0;
                            $timeout(function () {
                                $scope.isCustomerSocketConnected = true;
                                socketUtils.setPairingStatus(true);
                            });
                            $scope.getCustomerCardInfo();
                            if(AppUtil.customerSocket !=null && AppUtil.customerSocket!=undefined && AppUtil.customerSocket.id !=null && AppUtil.customerSocket.id!=undefined && AppUtil.customerSocket.id >5 ){
                                AppUtil.getCustomerBasicInfo(function(customerBasicInfo){
                                    $scope.customerBasicInfo=customerBasicInfo;
                                });
                                console.log("Customer Information in line 230:::::::", $scope.customerBasicInfo);
                                $scope.getCustomerFavChaiMappings(AppUtil.customerSocket.id);
                            }

                            // if(AppUtil.enablefreeChaiDelivery){
                            //     $scope.checkFreeChaiDelivery();
                            // }
                            $scope.otpBtnHidden = true;
                        }
                        if (key == "OTP_SENT" || key == "OTP_RESENT" || key == "NEW_CUSTOMER") {
                            if ($scope.unitDetails.id == 26012) {
                                $scope.switchOtpBtn = true;
                            }
                            $scope.otpBtnHidden = false;
                        }
                        if (key == "REDEMPTION" && !isRedemptionDone) {
                            isRedemptionDone = true;
                            $scope.addRedemptionToOrder();
                        }
                        /*if (key == "FEEDBACK_PENDING") {
                            $scope.feedbackPending = true;
                        }*/
                        /*if (key == "FEEDBACK_SUBMITTED") {
                            $scope.feedbackPending = false;
                        }*/
                        /*if (key == "RECOMMENDATION_RESULT") {
                            console.log('Recommendation Result', data);
                            if (data.recommendationDetail != undefined
                                && data.recommendationDetail != null) {
                                $scope.recommendationPending = false;
                                if (data.recommendationDetail.availed) {
                                    $scope.addRecommendationToArray("CUSTOMER");
                                    $scope.recommendationNotification = true;
                                } else {
                                    $scope.skipRecommendation("CUSTOMER");
                                }
                            }
                        }*/
                        if (key == "GIFT_CARD_CODE_ADDED") {
                            $scope.setGiftCardCode(data);
                        }
                        if (key == "OTP_STATUS" && data.contactVerified) {
                            $scope.otpBtnHidden = true;
                        }
                        if (key == "SKIP_EMAIL_INPUT" && (data.email == null || data.email == "")) {
                            $scope.showEmailSkipBtn = true;
                        }
                        if (key == 'EMAIL_FORM_SKIPPED_BY_CUSTOMER' || key == 'EMAIL_ENTERED') {
                            $scope.showEmailSkipBtn = false;
                            skipEmailForm = false;
                        }
                        if (key == 'SKIP_EMAIL_FORM') {
                            $scope.showEmailSkipBtn = true;
                            skipEmailForm = true;
                        }
                        if (key == "OTP_VERIFIED") {
                            AppUtil.otpStatus.text = "OTP Verification successful!";
                            AppUtil.otpStatus.status = 1;
                            $scope.otpBtnHidden = true;
                        }
                        if (key == "FACE_PROCESS_STARTED") {
                            $scope.faceProcessStarted = true;
                        }
                        if (key == "FACE_LOGIN_SKIPPED") {
                            $scope.faceProcessStarted = true;
                        }
                        if(key == "PAYMENT_DISABLE"){
                            $scope.paymentDisable = true;
                        }
                        if(key == "PAYMENT_ENABLED"){
                            $scope.paymentDisable = false;
                        }
                    }
                });
            }, "POS_CTRL");


            $scope.getOtpStatus = function () {
                return AppUtil.otpStatus.status;
            };

            $scope.switchOtp = function () {
                productService.metaDataList.addAttribute("SWITCH_OTP", "Y");
                socketUtils.emitMessage({SWITCH_OTP: AppUtil.customerSocket});
            };

            $scope.initCustomerSocketWidget = function () {
                $scope.metadataList = productService.metaDataList.resetList();
                AppUtil.customerSocket = AppUtil.resetCustomerSocket();
                $scope.customerSocket = AppUtil.customerSocket;
                $scope.isCustomerSocketConnected = socketUtils.getPairingStatus();

                socketUtils.pairingDone(function (msg) {
                    //console.log("pairing successful");
                    $scope.isCustomerSocketConnected = true;
                    $scope.customerNameManual = !msg;
                    $scope.customerRejectBtnHidden = !msg;
                    socketUtils.setPairingStatus(msg);
                });
                socketUtils.pairingFailed(function (msg) {
                    //console.log("pairing failed");
                    $scope.isCustomerSocketConnected = true;
                    $scope.customerNameManual = msg;
                    $scope.customerRejectBtnHidden = msg;
                    socketUtils.setPairingStatus(!msg);
                });

                if (socketUtils.getPairingStatus()) {
                    //console.log("Inside if statement of pairing status check");
                } else {
                    $scope.customerNameManual = true;
                    productService.metaDataList.addAttribute("CUSTOMER_SCREEN_DOWN", Date.now());
                    $scope.customerRejectBtnHidden = true;
                    //console.log("Customer screen is not paired");
                }

                $scope.isCustomerSocketConnected = socketUtils.getPairingStatus();
                $scope.renderedTemplate = productService.customerSocketWidget.resetTemplate();
                $scope.renderedTemplate = productService.customerSocketWidget.renderedTemplate;
            };

            $scope.$on("$destroy", function () {
                socketUtils.removeAllListeners();
            });

            $scope.clear = function () {
                bootbox.confirm("Are you sure?", function (result) {
                    if (result == true) {
                        $scope.orderItemArray = productService.clearOrderArray();
                        $scope.calculatePaidAmount();
                        productService.resetorderItemIndex();
                        $scope.$apply();
                        socketUtils.emitMessage({GIFT_CARD_REMOVED_ALL: AppUtil.customerSocket});
                        $scope.orderChecked = false;
                        $scope.offerApplicableForUnitExists.showIcon=false;
                        getOfferApplicable();
                    }
                });
            };


            $scope.init = function () {
                $scope.initCustomerSocketWidget();// init customer Socket Widget on initialization
                AppUtil.setCurrentTransactionGiftCards([]);
                $scope.desiChaiFilterIds = desiChaiService.getDesiChaiFilterIds();
                $scope.baarishWaliChaiFilterIds = desiChaiService.getBaarishWaliChaiFilterIds();
                productService.resetorderItemIndex();
                $scope.secondChai = false;
                $scope.favChaiRequestObject={};
                $scope.favChaiCustomizationId=-1;
                $scope.customerBasicInfo={};
                $scope.isDefaultFavChaiSelected= false;
                //$scope.recommendationDetail = null;
                //$scope.recommendationNotification = false;
                $scope.customerEnteredName = null;
                $scope.customerPendingCardInfo = null;
                $scope.chaayosCash = null;
                $scope.tableNumber = null;
                $scope.tableRequestId = null;
                $scope.employeeMealUser = null;
                $rootScope.defaultComplimentaryCode = null;
                $rootScope.complimentaryReasonId = null;
                $rootScope.complimentaryReasonCode = null;
                $scope.selectedCategoryType=null;
                // $scope.feedbackPending = false;
                //$scope.recommendationPending = true;
                //$scope.recommendationOfferApplied = false;
                $scope.unitDetails = AppUtil.getUnitDetails();
                $scope.showReapplyFreeChai = false;
                $scope.showSkipFreeChai = false;
                $scope.randomOrderIdForUnit = Math.abs(Math.random() * 100000000).toFixed();
                $scope.orderIdForPayment = 'U' + $scope.unitDetails.id + 'T' + AppUtil.getAutoConfigData().selectedTerminalId + $scope.randomOrderIdForUnit;
                if (AppUtil.isEmptyObject(AppUtil.getTransactionMetadata()) || AppUtil.isEmptyObject(AppUtil.categoryList)) {
                    coverUtils.logOut();
                }
                $scope.pointsRedeemed = productService.getPointsRedeemed();
                $scope.cashRedeemed = 0;
                $scope.orderStart = moment().tz("Asia/Kolkata").format('YYYY-MM-DD HH:mm:ss');
                $scope.orderRemark = '';
                $scope.subTotal = 0;
                $scope.promotionalOffer = 0;
                $scope.orderItemArray = productService.getOrderArray();
                $scope.globaLProductBrandId=-1;
                $scope.isFavChaiMarked= false;

                $scope.transactionMetadata = AppUtil.getTransactionMetadata();
                $scope.transactionObj = productService.getTransactionObject();
                ////console.log($scope.transactionObj);
                $scope.isCOD = AppUtil.isCOD();
                $scope.offerManual = false;
                $scope.transactionObj.discountDetail.discountCode = 0;
                //$scope.productsForSubCategory = productService.getProductArrayForSubTypeCode($scope.transactionMetaData.categories[0].content[0].id);
                $scope.checkInventory = AppUtil.checkInventory;
                $scope.trackedItemMap = {};
                if ($scope.isCheckInventory()) {
                    $scope.getInventory();
                }
                $scope.enquiryItems = [];
                $scope.tableService = AppUtil.hasTableService();
                $scope.hasExtendedTableService = AppUtil.hasExtendedTableService();
                $scope.tableNumberModal();
                $scope.employeeMealModal();
                $scope.complimentaryOrderModal();
                if ($scope.isCOD && AppUtil.freeKettle) {
                    $scope.showReapplyFreeChai = true;
                    $scope.showSkipFreeChai = false;
                    //$scope.freeChaiModalOpen();
                    AppUtil.customerSocket.chaiRedeemed = 1;
                }
                var c = AppUtil.categoryList[0];
                if (!AppUtil.isEmptyObject(c)) {
                    $scope.getProductsForCategory(c.content, c.detail.name, c.detail.id);
                }
                $scope.orderChecked = false;
                $scope.faceProcessStarted = false;
                // if (!$scope.isCOD && !$scope.isPaidEmployeeMeal && !$scope.isWastageOrder && !$scope.isComplimentaryOrder) {
                //     $scope.initOnClick();
                // }
                $scope.inventoryDown = false;
                getOfferApplicable();
            };

            $scope.initOnClick = function () {
                bootbox.dialog({
                    closeButton: false,
                    title: 'Ask for customer Number ',
                    message: '<h5>Benefits of Giving Number</h5>' +
                    '<p>First Time Customer : 2nd chai Free </p> ' +
                    '<p>Regular Customer : 6th chai Free   </p>',
                    size: 'medium',
                    onEscape: false,
                    backdrop: true,
                    buttons: {
                        fum: {
                            label: '  OK  ',
                            className: 'btn-danger '
                        }
                    }
                })
            };


            $scope.$watch('orderItemArray', function (newVal, oldVal) {
                ////console.log(newVal);
                ////console.log("inside the watcher with offerApplied ::::: "+$scope.offerApplied);
                if ($scope.offerApplied && $scope.checkForClearOffer && !$scope.secondChai) { // code snippet to clear the offer if the order items are changed
                    if (!$scope.isOfferModalopen) {
                        clearOfferFromOrder();
                    }
                } else {
                    $scope.checkForClearOffer = true;
                }
                $scope.calculatePaidAmount();
            }, true);

            $scope.outletName = function () {
                if (AppUtil.outlet.selectedId == 1) {
                    return AppUtil.outlet.pri_name;
                } else if (AppUtil.outlet.selectedId == 2) {
                    return AppUtil.outlet.sec_name;
                } else {
                    return AppUtil.outlet.ter_name;
                }
            };

            $scope.outletId = function () {
                if (AppUtil.outlet.selectedId == 1) {
                    return AppUtil.outlet.pri_unitId;
                } else if (AppUtil.outlet.selectedId == 2) {
                    return AppUtil.outlet.sec_unitId;
                } else {
                    return AppUtil.outlet.ter_unitId;
                }
            };

            $scope.switchOutlet = function () {
                var selected = AppUtil.outlet.selectedId;
                if (selected == 1) {
                    if (AppUtil.outlet.sec_unitId != 0) {
                        AppUtil.outlet.selectedId = 2;
                    } else {
                        AppUtil.myAlert("Secondary Outlet is not defined for this Locality");
                    }
                } else {
                    if (AppUtil.outlet.pri_unitId != 0) {
                        AppUtil.outlet.selectedId = 1;
                    } else {
                        AppUtil.myAlert("Primary Outlet is not defined for this Locality");
                    }
                }
                if (selected != AppUtil.outlet.selectedId) {
                    AppUtil.getDeliveryProfile(function (deliveryPartners) {
                        $scope.unitDetails.deliveryPartners = deliveryPartners;
                    });
                    AppUtil.mySuccessAlert("New Outlet Successfully Selected");
                    AppUtil.getPackagingProfile();
                    $scope.uncheckOrder();
                    $scope.clearOffer();
                    $scope.calculatePaidAmount();
                    if ($scope.isCheckInventory()) {
                        $scope.getInventory();
                    }
                }
                try {
                    var outletObj = AppUtil.getSelectedUnitIdName();
                    trackingService.setDefaultAttributes(null, null, null, outletObj.name);
                } catch (e) {
                }
            };

            $scope.hasRecipeContents = AppUtil.hasRecipeContents;

            $scope.getCustomizationAbb = AppUtil.getCustomizationAbb;

            $scope.getFavChaiCustomizationShortCodes = function(customerFavChaiMapping){
                var customizationList =customerFavChaiMapping.favChaiCustomizationDetailList;
                var s =customerFavChaiMapping.productName;
                if(desiChaiService.getDesiChaiProductIds().indexOf(customerFavChaiMapping.productId) >= 0){
                    s=s+"( "+customerFavChaiMapping.shortCode+" )" +" - "
                }
                var detail =AppUtil.getFavChaiCustomizationShortCodes(customizationList ,customerFavChaiMapping, s);
                // console.log("Product and its Customizations shortcode details :::::::", detail);
                return detail;
            }
            $scope.getProductsForSubCategory = function (subCatId) {
                if (subCatId == 1202) {
                    $scope.productsForSubCategory = [];
                    return;
                }
                $scope.productsForSubCategory = productService.getProductArrayForSubTypeCode(subCatId);
            };

            $scope.getFirstOrderBrandId=function (productItem,qty){
                console.log("Printing From inside productService :",productItem.brandId);
                if(productService.orderItemArray.length==0){
                    $rootScope.globalBrandId = productItem.brandId;
                }
                return $rootscope.globalBrandId;

            }

            $scope.addNewProductToOrderItemArrayForProductId=function(customerFavChaiMapping){
                var productId = customerFavChaiMapping.productId;
                var product = null;
                if(productId!=undefined && productId !=null && productId >0){
                    product =AppUtil.getProductForId(customerFavChaiMapping.productId);
                }
                var customizationList =[];
                if(customerFavChaiMapping.favChaiCustomizationDetailList!=null && customerFavChaiMapping.favChaiCustomizationDetailList.length>0){
                    for( var j in customerFavChaiMapping.favChaiCustomizationDetailList ){
                        customizationList.push(customerFavChaiMapping.favChaiCustomizationDetailList[j].name);
                    }
                }
                if (product != null && product != undefined) {
                    $scope.addNewProductToOrderItemArray(product, undefined, true, customizationList, true,customerFavChaiMapping);
                }
            }

            $scope.addNewProductToOrderItemArray = function (productItem, qty, isFavChai,customizationList,isDefaultFavChaiSelected,customerFavChaiMapping) {
                // console.log("Product:",productItem.name,"Brand:", productItem.brandId);
                // $scope.globaLProductBrandId =$scope.getFirstOrderBrandId();
                // console.log("Global Product Brand Id :", $scope.globaLProductBrandId);
                // if(productService.orderItemArray.length>0 && productItem.brandId!==$scope.globaLProductBrandId){
                //     bootbox.alert("Please clear cart to enter products of different brands!");
                // }
                //on every item add to cart reset the value of  !$scope.isFavChaiMarked
                $scope.isFavChaiMarked=false;
                console.log("Product Details -------->",productItem);
                console.log("Product:", productItem.name, "Brand:", productItem.brandId);
                if ($scope.orderItemArray.length == 0) {
                    console.log("First item in order");
                    $scope.globaLProductBrandId = productItem.brandId;
                    addingNewProductInCartAndOrderItemArray(productItem, qty, isFavChai,customizationList,isDefaultFavChaiSelected,customerFavChaiMapping);
                } else if ($scope.orderItemArray.length > 0 && productItem.brandId != $scope.globaLProductBrandId && $scope.isComplimentaryOrder) {
                    bootbox.alert("Brands Vary .Please clear cart to add items of different brands");
                } else {
                    addingNewProductInCartAndOrderItemArray(productItem, qty, isFavChai,customizationList,isDefaultFavChaiSelected,customerFavChaiMapping);
                }
            }

            function addingNewProductInCartAndOrderItemArray(productItem,qty,isFavChai,customizationList,isDefaultFavChaiSelected,customerFavChaiMapping) {
                if($scope.subscriptionDetailCoupon !== undefined &&
                    $scope.subscriptionDetailCoupon !==null && productItem.subType === AppUtil.getTransactionMetadata().subscriptionProductId
                    && $scope.subscriptionDetailCoupon!==undefined && $scope.subscriptionDetailCoupon!==null
                    && productItem.skuCode !== $scope.subscriptionDetailCoupon && $scope.subscriptionValidiltyDays>0){
                    bootbox.alert("Subscription Cannot be purchased as customer has different subscription offer");
                    $rootScope.isChaayosSelectChoosen=false;
                    return;
                }
                var orderItem = null;
                if (AppUtil.cardType != 'ECARD'&& AppUtil.cardType != 'AP01' && $scope.isCheckInventory()) {
                    var pQuantity = $scope.getInventoryForProduct(productItem.id);
                    //var dimensionInventory = $scope.getDimensionInventoryForProduct(productItem.id);
                    if (productItem.inventoryTracked && pQuantity <= 0) {
                        $scope.isCOD ? $scope.addProductToEnquiryItems(productItem, pQuantity) : bootbox.alert("Inventory Not Available for the product : " + productItem.name);
                    } else {
                        if ($scope.checkInOrderMap(productItem, 'add')) {
                            $scope.addNewProductToOrderItemArrayAfterCheck(productItem, qty,isFavChai,customizationList,isDefaultFavChaiSelected,customerFavChaiMapping);
                        } else {
                            if ($scope.isCOD) {
                                bootbox.confirm("Do want to add this product to enquiry list?", function (result) {
                                    if (result == true) {
                                        $scope.addProductToEnquiryItems(productItem, pQuantity);
                                    }
                                });
                            } else {
                                bootbox.alert("Inventory Not Available for the product : " + productItem.name);
                            }
                        }
                    }
                } else {
                    $scope.addNewProductToOrderItemArrayAfterCheck(productItem, qty,isFavChai,customizationList,isDefaultFavChaiSelected,customerFavChaiMapping);
                }
            }

            $scope.addNewProductToOrderItemArrayAfterCheck = function (productItem, qty,isFavChai,customizationList,isDefaultFavChaiSelected,customerFavChaiMapping) {
                var orderItem = null;
                $scope.updateIfPackagingChargeItem(productItem);
                if ($scope.isCOD) {
                    orderItem = productService.addNewProductToOrderItemArray(productItem, qty,customizationList,isDefaultFavChaiSelected,customerFavChaiMapping);
                    $scope.calculatePaidAmount();
                } else {
                    if (productItem.taxCode == "GIFT_CARD" && !AppUtil.customerSocket.contactVerified) {
                        bootbox.alert("Gift cards can be purchased by registered customers only. Please ask customer to verify his contact number first.");
                    } else if (productItem.subType == AppUtil.getTransactionMetadata().subscriptionProductId && !AppUtil.customerSocket.contactVerified) {
                        bootbox.alert("Chaayos Select can be purchased by registered customers only. Please ask customer to verify his contact number first.");
                    }
                    else {
                        orderItem = productService.addNewProductToOrderItemArray(productItem,qty,customizationList,isDefaultFavChaiSelected,customerFavChaiMapping);
                        $scope.orderItemArray = productService.getOrderArray();
                        $scope.calculatePaidAmount();
                        if (!$scope.isGiftCardModalOpen && !$scope.isChaayosSelectModalOpen && orderItem.productDetails.taxCode == "GIFT_CARD") {
                            socketUtils.emitMessage({
                                "GIFT_CARD_ADDED": {
                                    itemId: orderItem.orderDetails.itemId,
                                    productName: orderItem.orderDetails.productName,
                                    cardNumber: "",
                                    cardValue: orderItem.orderDetails.price,
                                    error: "",
                                    isValid: false
                                }
                            });
                        } else if ($scope.isGiftCardModalOpen && AppUtil.cardType == 'ECARD') {
                            setEGiftCard(orderItem);
                        }
                          else if ($scope.isGiftCardModalOpen && AppUtil.cardType == 'AP01'){
                            setEGiftCard(orderItem);
                        }
                        else if ($scope.isChaayosSelectModalOpen && AppUtil.cardType == 'SELECT') {
                            setEGiftCard(orderItem);
                        }
                    }
                }
                if(orderItem.isFavChaiMarked==undefined){
                    orderItem.isFavChaiMarked= isFavChai;
                }
                if(customerFavChaiMapping!=undefined && customerFavChaiMapping.sourceId !=undefined && customerFavChaiMapping.sourceId !=null && customerFavChaiMapping.sourceId==21 && orderItem.orderDetails.productId== customerFavChaiMapping.productId){
                    orderItem.chaiSavedFromDineIn= true;
                    orderItem.tagType = customerFavChaiMapping.tagType;
                }

                console.log("OrderItem before adding it in cart ", orderItem);
                //Add selected option as separate product to orderItemCart

                if (orderItem.recipeDetails!=null && orderItem.recipeDetails.options != null && orderItem.recipeDetails.options.length > 0) {
                    for (var i in orderItem.recipeDetails.options) {
                        if (orderItem.recipeDetails.options[i].selected &&
                            orderItem.recipeDetails.options[i].type == "PRODUCT") {
                                productService.addNewProductToOrderItemArrayByProductId(orderItem.recipeDetails.options[i].id,
                                    orderItem.orderDetails.itemId, orderItem.productDetails.name, orderItem.orderDetails.quantity);
                        }
                    }
                }

                //open customize modal if orderItem is a combo
                if ((orderItem != null && (orderItem.orderDetails.isCombo || [10, 1282].indexOf(orderItem.productDetails.id) >= 0)) && !orderItem.isFavChaiMarked) { //10 for desi chai
                    $scope.customizeNewModalOpen(orderItem);
                }
            };

            $scope.updateIfPackagingChargeItem = function (productItem) {
                if (productItem.id == 1043) {
                    if (AppUtil.unitDetails.packagingType == "FIXED") {
                        productItem.prices[0].price = $scope.unitDetails.packagingValue;
                    } else if (AppUtil.unitDetails.packagingType == "PERCENTAGE") {
                        productItem.prices[0].price = (($scope.unitDetails.packagingValue / 100) * $scope.transactionObj.totalAmount);
                    }
                }
            };

            $scope.addGyftrVoucherInOrder = function (productItem, voucherCode) {
                if (productItem.taxCode == "GIFT_CARD" && !AppUtil.customerSocket.contactVerified) {
                    bootbox.alert("Gyftr cards can be used by registered customers only. Please ask customer to verify his contact number first.");
                    return false;
                }
                var item = productService.addNewProductToOrderItemArray(productItem);
                item.orderDetails.isCardValid = true;
                item.orderDetails.cardType = AppUtil.cardType;
                item.orderDetails.itemCode = voucherCode;
                $scope.calculatePaidAmount();
                return true;
            };

            $scope.calculatePaidAmount = function () {
                if (AppUtil.isPaidEmployeeMeal()) {
                    $scope.calculatePaidEmployeeMealAmount();
                } else {
                    $scope.calculateOrderPaidAmount();
                }
            };

            $scope.isSubscriptionProduct = function (orderItem) {
                return orderItem.productDetails.subType == AppUtil.getTransactionMetadata().subscriptionProductId;
            };

            $scope.calculatePaidEmployeeMealAmount = function () {
                var subTotal = 0;
                var promotionalOffer = 0;
                var offerDiscount = 0;
                $scope.transactionObj = productService.getTransactionObject();
                if ($scope.orderItemArray.length > 0) {
                    for (var i = 0; i < $scope.orderItemArray.length; i++) {
                        var orderItem = $scope.orderItemArray[i];
                        orderItem.orderDetails.amount = (orderItem.orderDetails.price * orderItem.orderDetails.quantity);
                        orderItem.orderDetails.totalAmount = (orderItem.orderDetails.price * orderItem.orderDetails.quantity);
                        var empMealPrice = AppUtil.getUnitDetails().empMealPrices[orderItem.orderDetails.productId + '_' + orderItem.orderDetails.dimension];
                        if (empMealPrice == undefined || empMealPrice == null) {
                            empMealPrice = orderItem.orderDetails.price;
                            bootbox.alert("Employee meal price not available for " + orderItem.orderDetails.productName);
                        }
                        orderItem.orderDetails.discountDetail.promotionalOffer = (orderItem.orderDetails.price - empMealPrice.price) * orderItem.orderDetails.quantity;
                        orderItem.orderDetails.discountDetail.discountCode = 2100;
                        orderItem.orderDetails.discountDetail.discountReason = "Paid Employee Meal";
                        promotionalOffer += parseFloat(orderItem.orderDetails.discountDetail.promotionalOffer);
                        orderItem.orderDetails.amount -= orderItem.orderDetails.discountDetail.discount.value;
                        orderItem.orderDetails.amount -= orderItem.orderDetails.discountDetail.promotionalOffer;
                        $scope.transactionObj.totalAmount += orderItem.orderDetails.totalAmount;
                        subTotal += parseFloat(orderItem.orderDetails.amount);
                        $scope.calculateTaxesOfItem(orderItem);
                    }
                } else {
                    $scope.transactionObj.discountDetail = productService.getDefaultDiscount();
                }
                $scope.transactionObj.taxableAmount = subTotal;
                var originalAmount = $scope.getOriginalAmount();
                var paidAmount = $scope.getPaidAmount();
                var finalPaid = Math.round(paidAmount);
                $scope.transactionObj.paidAmount = finalPaid;
                $scope.transactionObj.roundOffValue = finalPaid - paidAmount;
                $scope.transactionObj.savings = Math.round(originalAmount - finalPaid);
                $scope.transactionObj.discountDetail.promotionalOffer = promotionalOffer;
                $scope.transactionObj.discountDetail.discount.value = offerDiscount;
                $scope.transactionObj.discountDetail.totalDiscount = promotionalOffer + offerDiscount;
            };

            $scope.calculateOrderPaidAmount = function () {

                //promo offer and sub total
                var subTotal = 0;
                var promotionalOffer = 0;
                var offerDiscount = 0;
                $scope.transactionObj = productService.getTransactionObject();
                ////console.log($scope.orderItemArray);
                if ($scope.orderItemArray.length > 0) {
                    for (var i = 0; i < $scope.orderItemArray.length; i++) {
                        var orderItem = $scope.orderItemArray[i];
                        //reset amount of each item
                        orderItem.orderDetails.amount = (orderItem.orderDetails.price * orderItem.orderDetails.quantity);
                        orderItem.orderDetails.totalAmount = (orderItem.orderDetails.price * orderItem.orderDetails.quantity);
                        //set if complimentary
                        if (orderItem.orderDetails.complimentaryDetail.isComplimentary) {
                            $scope.transactionMetadata.complimentaryCodes.content.forEach(function (v) {
                                if (v.id == orderItem.orderDetails.complimentaryDetail.reasonCode) {
                                    if (v.type == "ACCOUNTABLE") {
                                        orderItem.orderDetails.discountDetail.promotionalOffer = orderItem.orderDetails.amount;
                                    } else {
                                        orderItem.orderDetails.amount = 0;
                                        orderItem.orderDetails.totalAmount = 0;
                                        orderItem.orderDetails.discountDetail.promotionalOffer = 0;
                                    }
                                }
                            });
                        }

                        offerDiscount += orderItem.orderDetails.discountDetail.discount.value;
                        promotionalOffer += parseFloat(orderItem.orderDetails.discountDetail.promotionalOffer);

                        orderItem.orderDetails.amount -= orderItem.orderDetails.discountDetail.discount.value;
                        orderItem.orderDetails.amount -= orderItem.orderDetails.discountDetail.promotionalOffer;
                        $scope.transactionObj.totalAmount += orderItem.orderDetails.totalAmount;
                        subTotal += parseFloat(orderItem.orderDetails.amount);
                        $scope.calculateTaxesOfItem(orderItem);

                    }
                    //console.log($scope.subTotal,$scope.promotionalOffer);
                } else {
                    $scope.transactionObj.discountDetail = productService.getDefaultDiscount();
                }
                $scope.transactionObj.taxableAmount = subTotal;

                ////console.log($scope.transactionObj.totalAmount+" ::::::::::::: "+$scope.transactionObj.discountDetail.promotionalOffer);
                var originalAmount = $scope.getOriginalAmount();
                ////console.log("originalamount :::: "+originalAmount);
                var paidAmount = $scope.getPaidAmount();
                var finalPaid = Math.round(paidAmount);
                $scope.transactionObj.paidAmount = finalPaid;
                $scope.transactionObj.roundOffValue = finalPaid - paidAmount;
                $scope.transactionObj.savings = Math.round(originalAmount - finalPaid);
                $scope.transactionObj.discountDetail.promotionalOffer = promotionalOffer;
                $scope.transactionObj.discountDetail.discount.value = offerDiscount;
                $scope.transactionObj.discountDetail.totalDiscount = promotionalOffer + offerDiscount;
                //console.log("::::: after all the calculations :::::");
                //console.log($scope.transactionObj);
            };

            $scope.calculateTaxesOfItem = function (orderItem) {
                var taxCode = orderItem.productDetails.taxCode;
                if (taxCode == 'COMBO') {
                    $scope.calculateTaxForComboItems(taxCode, orderItem);
                } else {
                    $scope.calculateTaxForRegularItems(taxCode, orderItem);
                }
            };

            $scope.calculateTaxForComboItems = function (taxType, item) {
                item.orderDetails.code = taxType;
                item.orderDetails.taxes = [];
                item.orderDetails.tax = 0.0;
                item.orderDetails.originalTax = 0.0;
                for (var x in item.orderDetails.composition.menuProducts) {
                    var orderItem = item.orderDetails.composition.menuProducts[x].item;
                    $scope.calculateComboItemDiscountedAmounts(item, orderItem);
                    $scope.calculateTaxForRegularItems(orderItem.productDetails.taxCode, orderItem);
                }
            }
            $scope.calculateComboItemDiscountedAmounts = function (item, orderItem) {
                orderItem.orderDetails.totalAmount = (orderItem.orderDetails.price * item.orderDetails.quantity);
                var totalPrice = 0.0;
                for (var x in item.orderDetails.composition.menuProducts) {
                    var i = item.orderDetails.composition.menuProducts[x].item;
                    totalPrice += i.orderDetails.price;
                }
                var itemDiscountedPrice = orderItem.orderDetails.price * item.orderDetails.price / totalPrice;
                orderItem.orderDetails.amount = (itemDiscountedPrice * item.orderDetails.quantity);

            }

            $scope.calculateTaxForRegularItems = function (taxCode, orderItem) {
                var tax = $scope.getTaxesForCode(taxCode);
                if (tax == null) {
                    return;
                }
                orderItem.orderDetails.code = taxCode;
                orderItem.orderDetails.taxes = [];
                orderItem.orderDetails.tax = 0.0;
                orderItem.orderDetails.originalTax = 0.0;
                $scope.calculateTaxData(orderItem, 'GST', 'CGST', tax.state.cgst);
                $scope.calculateTaxData(orderItem, 'GST', 'SGST/UTGST', tax.state.sgst);
                if (tax.others != null && tax.others.length > 0) {
                    for (var i in tax.others) {
                        $scope.calculateTaxData(orderItem, tax.others[i].type, tax.others[i].type, tax.others[i].tax);
                    }
                }
            }
            $scope.calculateTaxData = function (orderItem, taxType, code, percentage) {
                if (percentage == 0.00) {
                    return;
                }
                var amount = orderItem.orderDetails.amount;
                var taxInfo = {};
                taxInfo.type = taxType;
                taxInfo.code = code;
                taxInfo.percentage = percentage;
                taxInfo.value = percentage * amount / 100;
                taxInfo.total = orderItem.orderDetails.totalAmount;
                taxInfo.taxable = orderItem.orderDetails.amount;
                orderItem.orderDetails.taxes.push(taxInfo);
                orderItem.orderDetails.tax += taxInfo.value;
                var originalTax = percentage * orderItem.orderDetails.totalAmount / 100;
                orderItem.orderDetails.originalTax += originalTax;
                $scope.updateTransactionalTax(taxInfo);

            };

            $scope.updateTransactionalTax = function (taxInfo) {
                var found = false;
                for (var i in $scope.transactionObj.taxes) {
                    var obj = $scope.transactionObj.taxes[i];
                    if (obj.type == taxInfo.type && obj.code == taxInfo.code && obj.percentage == taxInfo.percentage) {
                        found = true;
                        obj.value += taxInfo.value;
                        obj.total += taxInfo.total;
                        obj.taxable += taxInfo.taxable;
                        break;
                    }
                }
                if (!found) {
                    var taxData = angular.copy(taxInfo);
                    $scope.transactionObj.taxes.push(taxData);
                }
                $scope.transactionObj.tax += taxInfo.value;
            };

            $scope.getTaxesForCode = function (taxCode) {
                if (taxCode == null) {
                    return null;
                }
                var applicableTax = $scope.unitDetails.taxes[taxCode];
                if (applicableTax == undefined || applicableTax == null) {
                    return null;
                }
                return applicableTax;
            };
            /*scope.skipFeedback = function () {
                $scope.feedbackPending = false;
                socketUtils.emitMessage({"SKIP_FEEDBACK": AppUtil.customerSocket});
                productService.metaDataList.addAttribute("SKIP_FEEDBACK", "Y");
            };*/

            $scope.setGiftCardCode = function (card) {
                $scope.orderItemArray.map(function (item) {
                    if (item.orderDetails.itemId == card.itemId) {
                        item.orderDetails.cardType = AppUtil.cardType;
                        item.orderDetails.itemCode = card.cardNumber;
                        item.orderDetails.isCardValid = card.isValid;
                    }
                });
            };

            $scope.addRedemptionToOrder = function () {
                if (!$scope.isNewCustomer) {
                    productService.orderItemArray.forEach(function (item) {
                        if (item.orderDetails.complimentaryDetail.isComplimentary && item.orderDetails.complimentaryDetail.reasonCode) {
                            console.log("duplicate request ");
                            return false;
                        }
                    });
                    //console.log("inside if statement for showing skip first free chai");
                    $scope.hideFirstFreeChaiBtn = false;
                    $scope.customerRejectBtnHidden = true;
                    $scope.otpBtnHidden = true;
                    $scope.showReapplyFreeChai = false;
                    $scope.showSkipFreeChai = true;
                    //$scope.freeChaiModalOpen();
                    var count = 0;
                    productService.orderItemArray.forEach(function (item) {
                        if (item.orderDetails.hasBeenRedeemed) {
                            count = count + item.orderDetails.quantity;
                        }
                    });
                    if (count < AppUtil.customerSocket.chaiRedeemed) {
                        productService.orderItemArray.forEach(function (item) {
                            if (item.orderDetails.hasBeenRedeemed != true && desiChaiService.getRedemptionChaiProductIds().indexOf(item.orderDetails.productId) >= 0 &&
                                item.orderDetails.dimension == item.productDetails.prices[0].dimension && item.orderDetails.quantity <= (AppUtil.customerSocket.chaiRedeemed - count) && count < AppUtil.customerSocket.chaiRedeemed) {
                                count = count + item.orderDetails.quantity;
                                item.orderDetails.hasBeenRedeemed = true;
                                item.orderDetails.complimentaryDetail = {
                                    isComplimentary: true,
                                    reasonCode: 2101,
                                    reason: null
                                };
                                item.orderDetails.discountDetail.promotionalOffer = item.orderDetails.amount;
                            }
                        });
                        isRedemptionDone = true;
                    }
                    if (count < AppUtil.customerSocket.chaiRedeemed) {
                        for (var i = 0; i < (AppUtil.customerSocket.chaiRedeemed - count); i++) {
                            productService.addRedemptionToOrderWithAllProducts(1, i, function () {
                            },$rootScope.customerFavChaiMappings,desiChaiService.getRedemptionChaiProductIds()); //add desi chai to redemption
                        }
                    }
                }
            };

            $scope.removeRedemption = function () {
                $scope.showReapplyFreeChai = true;
                $scope.showSkipFreeChai = false;
                isRedemptionDone = false;
                productService.removeRedeemedProducts();
            };


            ///////////////////////// helper methods end ///////////////////////////

            $scope.getOriginalAmount = function () {
                var paidAmount = 0;
                if ($scope.orderItemArray.length > 0) {
                    for (var i in $scope.orderItemArray) {
                        var orderItem = $scope.orderItemArray[i];
                        if (orderItem.productDetails.taxCode == 'COMBO') {
                            for (var x in orderItem.orderDetails.composition.menuProducts) {
                                var item = orderItem.orderDetails.composition.menuProducts[x].item;
                                paidAmount += item.orderDetails.totalAmount;
                                paidAmount += item.orderDetails.originalTax;
                            }
                        } else {
                            paidAmount += orderItem.orderDetails.totalAmount;
                            paidAmount += orderItem.orderDetails.originalTax;
                        }
                    }
                }
                return parseFloat(paidAmount);
            };

            $scope.getPaidAmount = function () {
                var paidAmount = 0;
                if ($scope.orderItemArray.length > 0) {
                    for (var i in $scope.orderItemArray) {
                        var orderItem = $scope.orderItemArray[i];
                        paidAmount += orderItem.orderDetails.amount;
                        if (orderItem.productDetails.taxCode == 'COMBO') {
                            for (var x in orderItem.orderDetails.composition.menuProducts) {
                                var item = orderItem.orderDetails.composition.menuProducts[x].item;
                                paidAmount += item.orderDetails.tax;
                            }
                        } else {
                            paidAmount += orderItem.orderDetails.tax;
                        }
                    }
                }
                return parseFloat(paidAmount);
            };

            $scope.sizeBtnClicked = function (orderItem, index) {
                var unitDetails = JSON.parse(localStorage.getItem("unitDetails"));
                if (unitDetails.prices !== undefined && unitDetails.prices !== null && unitDetails.originalPrices !== undefined && unitDetails.originalPrices !== null) {
                    orderItem.orderDetails.originalPrice = unitDetails.originalPrices[orderItem.productDetails.id + "_" + orderItem.productDetails.prices[index].dimension].price;
                    orderItem.orderDetails.price = unitDetails.prices[orderItem.productDetails.id + "_" + orderItem.productDetails.prices[index].dimension].price;
                }
                else {
                    orderItem.orderDetails.price = orderItem.productDetails.prices[index].price;
                    orderItem.orderDetails.originalPrice = orderItem.productDetails.prices[index].originalPrice;
                }
                orderItem.orderDetails.dimension = orderItem.productDetails.prices[index].dimension;
                orderItem.recipeDetails = AppUtil.resetOrderItemCustomization(angular.copy(orderItem.productDetails.prices[index].recipe));
                if (desiChaiService.getDesiChaiProductIds().indexOf(orderItem.productDetails.id) >= 0) {
                    orderItem.recipeDetails = angular.copy(desiChaiService.getClonedCustomizationInNewDimension(orderItem, index));
                    $scope.removeChildProducts(orderItem);
                }
                orderItem = AppUtil.calculateCustomization(orderItem, $scope.orderItemArray.length + 1);
                //$scope.applyRecommendationOfferIfApplicable(orderItem);
                //console.log('singleOrderItem   ', orderItem);
                $scope.calculatePaidAmount();
            };

            $scope.removeChildProducts = function (orderItem) {
                var selectedOptions = [];
                orderItem.recipeDetails.options.map(function (option) {
                    if (option.selected == true) {
                        selectedOptions.push(option.productId);
                    }
                });
                var childItems = [];
                $scope.orderItemArray.map(function (item) {
                    if (item.productDetails.parentProductId == orderItem.orderDetails.itemId) {
                        childItems.push(item);
                    }
                });
                for (var i = 0; i < childItems.length; i++) {
                    for (var j = 0; j < $scope.orderItemArray.length; j++) {
                        if ($scope.orderItemArray[j].productDetails.parentProductId == orderItem.orderDetails.itemId &&
                            selectedOptions.indexOf($scope.orderItemArray[j].productDetails.productId) < 0) {
                            var ind = $scope.orderItemArray.length - (j + 1);
                            $scope.deleteItem(ind);
                            break;
                        }
                    }
                }
            };

            /* $scope.applyRecommendationOfferIfApplicable =function(orderItem){
                 if(!$scope.offerApplied && $scope.recommendationDetail!=undefined && $scope.recommendationDetail.offer){
                     if (orderItem.orderDetails.recommended && $scope.recommendationOfferApplied) {
                     productService.applyRecommendationOffer(orderItem, $scope.recommendationDetail);
                     }
                 }
             };*/

            $scope.deleteItem = function (index, direct) {
                if (direct != true) {
                    index = $scope.orderItemArray.length - (index + 1);
                }
                var orderItem = $scope.orderItemArray.splice(index, 1);
                for (var i in $scope.orderItemArray) {
                    if ($scope.orderItemArray[i] != null && $scope.orderItemArray[i] != undefined &&
                        $scope.orderItemArray[i].orderDetails != null && $scope.orderItemArray[i].orderDetails != undefined &&
                        $scope.orderItemArray[i].orderDetails.itemId != null && $scope.orderItemArray[i].orderDetails.itemId != undefined) {
                        $scope.orderItemArray[i].orderDetails.itemId = parseInt(i)+1;
                    }
                }
                if (!$scope.isGiftCardModalOpen && orderItem[0].productDetails.taxCode == "GIFT_CARD") {
                    socketUtils.emitMessage({GIFT_CARD_REMOVED: {itemId: orderItem[0].orderDetails.itemId}});
                }
                $scope.calculatePaidAmount();
                if ($scope.isCheckInventory()) {
                    $scope.dropFromOrderMap(orderItem[0].productDetails, orderItem[0].orderDetails.quantity);
                }
                //removing customization from parent product
                if (orderItem[0].productDetails.parentProductId != null) {
                    $scope.orderItemArray.map(function (item) {
                        if (item.orderDetails.itemId == orderItem[0].productDetails.parentProductId) {
                            item.recipeDetails.options.map(function (option) {
                                if (option.productId == orderItem[0].productDetails.id) {
                                    option.selected = false;
                                }
                            });
                        }
                    })
                }
                //removing paid addon customization
                var customizationProductIndex = [];
                $scope.orderItemArray.map(function (item, index) {
                    if (item.productDetails.parentProductId == orderItem[0].orderDetails.itemId) {
                        customizationProductIndex.push(index);
                    }
                    if (orderItem[0].orderDetails.composition != null) {
                        orderItem[0].orderDetails.composition.menuProducts.map(function (menuProduct) {
                            if (item.productDetails.parentProductId == menuProduct.item.orderDetails.itemId) {
                                customizationProductIndex.push(index);
                            }
                        });
                    }
                });
                customizationProductIndex = customizationProductIndex.sort(function (a, b) {
                    a = parseInt(a);
                    b = parseInt(b);
                    if (a == b) return 0;
                    if (a > b) return -1;
                    if (a < b) return 1;
                });
                if (customizationProductIndex.length > 0) {
                    customizationProductIndex.map(function (item) {
                        $scope.orderItemArray.splice(customizationProductIndex, 1);
                    });
                }
                if ($scope.orderItemArray.length == 0) {
                    $scope.offerApplicableForUnitExists.showIcon=false;
                    getOfferApplicable();
                }
            };

            $scope.increaseCount = function (orderItem) {
                var increaseChildQty = false;
                if ($scope.isCheckInventory()) {
                    if ($scope.checkInOrderMap(orderItem.productDetails, 'add')) {
                        orderItem.orderDetails.quantity += 1;
                        increaseChildQty = true;
                        $scope.calculatePaidAmount();
                    } else {
                        if ($scope.isCOD) {
                            bootbox.confirm("Do want to add this product to enquiry list?", function (result) {
                                if (result == true) {
                                    $scope.addProductToEnquiryItems(orderItem.productDetails, orderItem.orderDetails.quantity);
                                }
                            });
                        } else {
                            bootbox.alert("Inventory Not Available for the product : " + orderItem.productDetails.name);
                        }
                    }
                } else {
                    orderItem.orderDetails.quantity += 1;
                    increaseChildQty = true;
                    $scope.calculatePaidAmount();
                }
                if (increaseChildQty) {
                    $scope.orderItemArray.map(function (item, index) {
                        if (item.productDetails.parentProductId == orderItem.orderDetails.itemId) {
                            item.orderDetails.quantity += 1;
                        }
                    });
                    $scope.calculatePaidAmount();
                }
                //$scope.applyRecommendationOfferIfApplicable(orderItem);
            };

            $scope.decreaseCount = function (orderItem) {
                var decreaseChildQty = false;

                if (orderItem.orderDetails.quantity > 1) {
                    if ($scope.isCheckInventory()) {
                        if ($scope.checkInOrderMap(orderItem.productDetails, 'remove')) {
                            orderItem.orderDetails.quantity -= 1;
                            decreaseChildQty = true;
                            $scope.calculatePaidAmount();
                        }
                    } else {
                        orderItem.orderDetails.quantity -= 1;
                        decreaseChildQty = true;
                        $scope.calculatePaidAmount();
                    }
                }
                if (decreaseChildQty) {
                    $scope.orderItemArray.map(function (item, index) {
                        if (item.productDetails.parentProductId == orderItem.orderDetails.itemId) {
                            item.orderDetails.quantity -= 1;
                        }
                    });
                    $scope.calculatePaidAmount();
                }
                //$scope.applyRecommendationOfferIfApplicable(orderItem);
            };

            $scope.customerNotInterested = function () {
                // bootbox.confirm("Are you sure, customer is not interested?", function (result) {
                //     if (result == true) {
                $scope.customerRejectBtnHidden = true;
                productService.metaDataList.addAttribute("CUSTOMER_NOT_INTERESTED", "Y");
                socketUtils.emitMessage({CUSTOMER_NOT_INTERESTED: AppUtil.customerSocket});
                $scope.customerRejectBtnHidden = true;
                $scope.otpBtnHidden = true;
                $scope.cutomerInterested = false;
                $scope.customerNameManual = true;
                //     }
                // });
            };

            $scope.customerFaceSkipped = function () {
                productService.metaDataList.addAttribute("FACE_SKIPPED", "BY EMPLOYEE");
                socketUtils.emitMessage({CUSTOMER_FACE_SKIPPED: AppUtil.customerSocket});
                $scope.faceProcessStarted = true;
            };

            $scope.otpNotReceived = function () {
                bootbox.confirm("Are you sure, OTP has not been received?", function (result) {
                    if (result == true) {
                        $scope.otpBtnHidden = true;
                        $scope.customerRejectBtnHidden = true;
                        AppUtil.customerSocket.otpNotReceivedClicked = true;
                        if (AppUtil.customerSocket.newCustomer) {
                            $scope.hideFirstFreeChaiBtn = true;
                            productService.metaDataList.addAttribute("OTP_NOT_RECEIVED_NEW_CUSTOMER", "Y");
                            productService.metaDataList.addAttribute("REFERENCE_NUMBER", AppUtil.customerSocket.contact);
                            // just to take user name in case otp is not received
                            socketUtils.emitMessage({CUSTOMER_NOT_INTERESTED: AppUtil.customerSocket});
                            $scope.otpBtnHidden = true;
                            $scope.cutomerInterested = false;
                            $scope.customerNameManual = true;
                            $scope.renderedTemplate = productService.customerSocketWidget.resetTemplate();
                            $scope.customerPendingCardInfo = null;
                            $scope.chaayosCash = null;
                        } else {
                            $scope.hideFirstFreeChaiBtn = true;
                            productService.metaDataList.addAttribute("OTP_NOT_RECEIVED_EXISTING_CUSTOMER", "Y");
                            socketUtils.emitMessage({ORDER_IN_PROGRESS: AppUtil.customerSocket});
                        }
                    }
                });
            };

            /*$scope.skipFreeChai = function(){
             var response = confirm("Are you sure, customer wants to skip first free chai?");
             if(response){
             $scope.orderItemArray.forEach(function(orderItem){
             if(orderItem.orderDetails.hasBeenRedeemed && orderItem.productDetails.id == 10 && orderItem.orderDetails.quantity == 1){
             var index = $scope.orderItemArray.indexOf(orderItem);
             if(index != -1){
             $scope.orderItemArray.splice(index,1);
             productService.metaDataList.addAttribute("SKIP_FREE_CHAI","Y");
             }
             }
             });
             $scope.hideFirstFreeChaiBtn = true;
             }else{
             $scope.hideFirstFreeChaiBtn = false;
             }
             };*/

            $scope.goToCoverScreen = function (confirmRequired) {
                AppUtil.setRedemptionProductList = null;
                if (confirmRequired) {
                    bootbox.confirm("Are you sure, customer wants to cancel order?", function (result) {
                        if (result == true) {
                            if (AppUtil.customerSocket != null && AppUtil.customerSocket.newCustomer) {
                                AppUtil.customerSocket.contactVerified = false;
                            }
                            socketUtils.emitMessage({'ORDER_CANCELLED': AppUtil.customerSocket});
                            /*if (AppUtil.customerSocket != null && AppUtil.customerSocket.feedbackRequired && !$scope.feedbackPending) {
                                $scope.cancelFeedback();
                            }*/
                            $scope.goBack(confirmRequired);
                        }
                    });
                } else {
                    if ($scope.isGiftCardModalOpen) {
                        $rootScope.closeGiftModal();
                        $rootScope.showFullScreenLoader = true;
                        $scope.randomOrderIdForUnit = Math.abs(Math.random() * 100000000).toFixed();
                    }
                    else if ($scope.isChaayosSelectModalOpen) {
                        $rootScope.closeChaayosSelectModal();
                        $rootScope.showFullScreenLoader = true;
                        $scope.randomOrderIdForUnit = Math.abs(Math.random() * 100000000).toFixed();
                        // $scope.initOnChaayosSelectPurchase();

                    }else {
                        $scope.goBack(confirmRequired);
                        $rootScope.showFullScreenLoader = true;
                    }
                }
            };
            $scope.goBack = function (confirmRequired) {
                productService.clearOrderArray();
                $scope.transactionObj = productService.getTransactionObject();
                $scope.orderStart = null;
                AppUtil.discountValue = 0;
                $scope.offerCode = "";


                if (!AppUtil.isCOD()) {
                    if ($scope.hasExtendedTableService && confirmRequired) {
                        $location.url('/tableSummary')
                    } else {
                        $location.url('/cover');
                    }
                } else {
                    if ($scope.enquiryItems.length > 0) {
                        $scope.saveEnquiry();
                    } else {
                        $location.url('/CODCover');
                        if (confirmRequired) {
                            $scope.$apply();
                        }
                    }
                }
                // $scope.$apply();
            };

            /*$scope.cancelFeedback = function () {
                posAPI.allUrl('/',AppUtil.restUrls.customer.cancelFeedback)
                    .post(AppUtil.customerSocket.feedbackOrderMetadata.feedbackToken).then(function (response) {
                        console.log("feedback cancelled successfully!!!!!!!!!!!!!!!!!");
                        console.log(response);
                    }, function (err) {
                        AppUtil.myAlert(err.data.errorMessage);
                    });
            };*/

            function updateCustomerNPSDetails(customerId) {
                if (AppUtil.customerSocket != undefined && AppUtil.customerSocket != null
                    && AppUtil.customerSocket.id != undefined && AppUtil.customerSocket.id != null
                    && AppUtil.customerSocket.id > 5) {
                    posAPI
                        .allUrl('/', AppUtil.restUrls.customer.getNpsForCustomerDetail)
                        .get(customerId)
                        .then(function (response) {
                            console.log(response);
                            $scope.last3NPS = [response.lastNps, response.secondNps, response.thirdNps];
                            if (response.totalNps != undefined) {
                                $scope.totalNPS = response.totalNps;
                            }
                            $scope.NPSCategory = response.npsCategory;
                            $scope.howToConvinceCustomer = response.result;
                            console.log($scope.totalNPS);
                            if (response.lastFeedbackOfLastOrder == "Y") {
                                $scope.isLastOrderNPSFilled = true;
                            }
                        }, function (err) {
                            console.log($scope.totalNPS);
                            AppUtil.myAlert(err.data.errorMessage);
                        });
                }
            }

            function updateCustomerFeedbackDetails(customerId) {
                $scope.feedbackCategory = null;
                $scope.last3Feedback = null;
                $scope.totalFeedback = null;
                $scope.howToConvinceFeedbackCustomer = null;
                $scope.isLastOrderFeedbackFilled = false;

                if (AppUtil.customerSocket != undefined && AppUtil.customerSocket != null
                    && AppUtil.customerSocket.id != undefined && AppUtil.customerSocket.id != null
                    && AppUtil.customerSocket.id > 5) {
                    posAPI
                        .allUrl('/', AppUtil.restUrls.customer.getFeedbackForCustomerDetail)
                        .get(customerId)
                        .then(function (response) {
                            console.log(response);
                            $scope.last3Feedback = [response.lastFeedback, response.secondFeedback, response.thirdFeedback];
                            if (response.totalFeedback != undefined) {
                                $scope.totalFeedback = response.totalFeedback;
                            }
                            $scope.feedbackCategory = response.feedbackCategory;
                            $scope.howToConvinceFeedbackCustomer = response.result;
                            console.log($scope.totalFeedback);
                            if (response.lastFeedbackOfLastOrderFeedback == "Y") {
                                $scope.isLastOrderFeedbackFilled = true;
                            }
                        }, function (err) {
                            console.log($scope.totalFeedback);
                            AppUtil.myAlert(err.data.errorMessage);
                        });
                }
            }

            function updateCustomerType(customerId, unitId) {
                if (AppUtil.customerSocket != undefined && AppUtil.customerSocket != null
                    && AppUtil.customerSocket.id != undefined && AppUtil.customerSocket.id != null
                    && AppUtil.customerSocket.id > 5) {
                    posAPI
                        .allUrl('/', AppUtil.restUrls.customer.customerVisit)
                        .customGET("", {
                            customerId: customerId,
                            unitId: unitId
                        })
                        .then(function (response) {
                            console.log("Called");
                            console.log(response);
                            if (response.firstTimeCustomer === true) {
                                $scope.customerType = "First Time Customer";
                            } else if (response.regularCustomer === true) {
                                $scope.customerType = "Regular Customer";
                                $scope.lastUnit = response.unitName;
                            } else {
                                $scope.customerType = "Outstation Customer";
                                $scope.lastUnit = response.unitName;
                                $scope.currentCafeRegion = response.currentRegion;
                                $scope.previousCafeRegion = response.previousRegion;
                            }
                            $scope.orderSource = response.orderSource;
                            $scope.channelPartnerName = AppUtil.getPartnerNameById(response.channelPartner);
                            $scope.showFeedback = response.lastFeedback;
                            $scope.showEvent = response.lastFeedbackEvent;
                            $scope.showFeedbackValue = response.lastFeedbackDetail;
                            $scope.isFirstZomatoOrder = response.firstZomatoOrder;
                            if ($scope.showFeedback == true && $scope.showEvent == true) {
                                $scope.dataShow = true;
                            }
                            if ($scope.showFeedback == true && $scope.showEvent == false) {
                                $scope.dataShow = false;
                            }
                            var lastOrderDetails = response.infoList;
                            for (var order in lastOrderDetails) {
                                var orderProductId = lastOrderDetails[order].productID;
                                var orderProductName = lastOrderDetails[order].productName;
                                var orderProductAddOns = lastOrderDetails[order]["productAddons"][orderProductId];
                                var orderProductQuantity = lastOrderDetails[order]["quantity"];
                                var orderProductDimension = lastOrderDetails[order]["dimension"];

                                var allAddOns = [];
                                for (var addOn in orderProductAddOns) {
                                    allAddOns.push(orderProductAddOns[addOn][1]);
                                }
                                if (orderProductDimension != "None") {
                                    allAddOns.push(orderProductDimension);
                                }
                                var orderObj = {};
                                orderObj["productName"] = orderProductName;
                                orderObj["quantity"] = orderProductQuantity;
                                orderObj["addOns"] = allAddOns;
                                $scope.lastOrder.push(orderObj);
                            }
                            var lastOrderIds = response.productId;
                            $scope.lastOrderIds = response.productId;


                            updateRecommendedProducts($scope.lastOrderIds, "L");

                            console.log($scope.customerType);
                            if ($scope.isNewCustomer == true || $scope.customerType == 'First Time Customer') {
                                console.log("hello");
                                var coreProducts = AppUtil.getTransactionMetadata().coreProducts.content;
                                var coreProductsIds = [];
                                coreProducts.forEach(function (data) {
                                    var productId = parseInt(data["code"]);
                                    coreProductsIds.push(productId);
                                });
                                updateRecommendedProducts(coreProductsIds, "C");
                            } else if ($scope.customerType == 'Regular Customer' || $scope.customerType == 'Outstation Customer') {
                                var categoryProducts = AppUtil.getTransactionMetadata().categoryProducts.content;
                                var categoryProductsIds = [];
                                categoryProducts.forEach(function (data) {
                                    var productId = parseInt(data["code"]);
                                    categoryProductsIds.push(productId);
                                });
                                updateRecommendedProducts(categoryProductsIds, "T");
                            }
                        }, function (err) {
                            AppUtil.myAlert(err.data.errorMessage);
                        });
                }
            }

            function updateCustomerRecommendation(customerId) {
                if (AppUtil.customerSocket != undefined && AppUtil.customerSocket != null
                    && AppUtil.customerSocket.id != undefined && AppUtil.customerSocket.id != null
                    && AppUtil.customerSocket.id > 5) {
                    posAPI
                        .allUrl('/', AppUtil.restUrls.customer.recommendedProduct)
                        .get(customerId)
                        .then(function (response) {
                            if (response != null) {
                                $scope.recomProductList = response;
                            }
                            console.log(response);
                            updateRecommendedProducts($scope.recomProductList, "R");

                        }, function (err) {
                            AppUtil.myAlert(err.data.errorMessage);
                        });
                }
            }

            function updateRecommendedProducts(listOfProductIds, type) {
                if ($scope.isNewCustomer == true || $scope.customerType == 'First Time Customer') {
                    for (var prod in $scope.recomProducts) {
                        if ($scope.recomProducts[prod].includes("E")) {
                            delete $scope.recomProducts[prod];
                        }
                    }
                }
                if (listOfProductIds != null) {
                    listOfProductIds.forEach(function (data) {
                        if ($scope.recomProducts[data]) {
                            if (!$scope.recomProducts[data].includes(type)) {
                                $scope.recomProducts[data] = $scope.recomProducts[data] + type;
                            }
                        } else {
                            $scope.recomProducts[data] = type;
                        }
                    })
                }
                var allProducts = JSON.parse(localStorage.getItem("unitDetails"))["products"];
                var recommendedProducts = Object.keys($scope.recomProducts);

                var productsToShow = [];
                recommendedProducts.forEach(function (data, idx) {
                    for (var product in allProducts) {
                        if (allProducts[product]["id"] == data) {
                            var tempProduct = JSON.parse(JSON.stringify(allProducts[product]));
                            var sortedRecomCategory = $scope.recomProducts[data].split('').sort().join('');
                            tempProduct["orderStatus"] = sortedRecomCategory;
                            productsToShow.push(tempProduct);
                        }
                    }
                });
                var displayOrder = [/[C]/i, /[T]/i, /ELR/i, /ER/i, /EL/i, /LR/i, /E/i, /L/i, /R/i];
                productsToShow.sort(function (product1, product2) {
                    var idx1, idx2;
                    var p1OrderStatus = product1["orderStatus"];
                    var p2OrderStatus = product2["orderStatus"];
                    for (var i = 0; i < displayOrder.length; i++) {
                        if (displayOrder[i].test(p1OrderStatus)) {
                            idx1 = i;
                            break;
                        }
                    }
                    for (var i = 0; i < displayOrder.length; i++) {
                        if (displayOrder[i].test(p2OrderStatus)) {
                            idx2 = i;
                            break;
                        }
                    }
                    return idx1 - idx2;
                });
                productsToShow.forEach(function (data, index) {
                    var category = index + data["orderStatus"];
                    var categoryToAdd = "";
                    for (var i = 0; i < category.length; i++) {
                        categoryToAdd += category.charAt(i);
                        if (i != category.length - 1) {
                            categoryToAdd += ",";
                        }
                    }
                    data["recomCategory"] = categoryToAdd;
                });


                $scope.productsForSubCategory = productsToShow;
            }

            function getMonthFromDate(date) {
                var months = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
                return months[date.getMonth()];
            }

            $scope.getCustomerCardInfo = function () {
                $scope.customerPendingCardInfo = null;
                if (AppUtil.customerSocket != undefined && AppUtil.customerSocket != null
                    && AppUtil.customerSocket.id != undefined && AppUtil.customerSocket.id != null
                    && AppUtil.customerSocket.id > 5) {
                    posAPI
                        .allUrl('/', AppUtil.restUrls.order.getCustomerCardInfo)
                        .customGET("", {
                            customerId: AppUtil.customerSocket.id,
                            unitId: $scope.unitDetails.id
                        })
                        .then(function (response) {
                            console.log(response);
                            $scope.customerId = response.reqParams.customerId;
                            $scope.toShowCustomerOneViewSection = true;
                            $scope.customerPendingCardInfo = response.plain();
                            $scope.unitId = $scope.unitDetails.id;
                            // socketUtils.emitMessage({"CHAAYOS_WALLET_BALANCE": $scope.customerPendingCardInfo.cardAmount});
                            updateCustomerType($scope.customerId, $scope.unitId);
                            if ($scope.isNewCustomer == false && $scope.customerType != 'First Time Customer') {
                                updateCustomerNPSDetails($scope.customerId);
                                updateCustomerFeedbackDetails($scope.customerId);
                                updateCustomerRecommendation($scope.customerId);
                            }
                        }, function (err) {
                            AppUtil.myAlert(err.data.errorMessage);
                        });
                    posAPI
                        .allUrl('/', AppUtil.restUrls.order.getChaayosCash)
                        .customGET("", {
                            customerId: AppUtil.customerSocket.id,
                            unitId: $scope.unitDetails.id
                        })
                        .then(function (response) {
                            $scope.chaayosCash = response.plain();
                            console.log($scope.chaayosCash);
                            if ($scope.chaayosCash.subscriptionInfoDetail != null) {
                                $scope.subscriptionDetailOverAll = $scope.chaayosCash.subscriptionInfoDetail.overAllSaving;
                                $scope.subscriptionDetailCoupon = $scope.chaayosCash.subscriptionInfoDetail.subscriptionCode;
                                $scope.subscriptionValidiltyDays = $scope.chaayosCash.subscriptionInfoDetail.daysLeft;
                                $scope.chaayosCash.subscriptionInfoDetail.day = new Date($scope.chaayosCash.subscriptionInfoDetail.endDate).getDate();
                                $scope.chaayosCash.subscriptionInfoDetail.year = new Date($scope.chaayosCash.subscriptionInfoDetail.endDate).getFullYear();
                                $scope.chaayosCash.subscriptionInfoDetail.month = getMonthFromDate(new Date($scope.chaayosCash.subscriptionInfoDetail.endDate));
                                var subProductName = JSON.parse(localStorage.getItem("Subscription_Products_Name"));
                                for(var i in subProductName){
                                    if(subProductName[i].productSKU === $scope.chaayosCash.subscriptionInfoDetail.subscriptionCode){
                                        $scope.subProductName = subProductName[i].productName
                                    }
                                }
                                if($scope.subProductName === null){
                                    $scope.subProductName = "Chaayos Subscription";
                                }
                            }
                        }, function (err) {
                            AppUtil.myAlert(err.data.errorMessage);
                        });
                } else {
                    $scope.customerPendingCardInfo = {
                        hasCard: false,
                        cardAmount: 0.00
                    };
                    $scope.chaayosCash = {
                        hasCash: false,
                        cashAmount: 0.00,
                        subscriptionInfoDetail: {
                            hasSubscription: false
                        }
                    };
                }
            };

            $scope.checkFreeChaiDelivery = function () {
                $scope.customerPendingCardInfo = null;
                if (AppUtil.customerSocket != undefined && AppUtil.customerSocket != null
                    && AppUtil.customerSocket.id != undefined && AppUtil.customerSocket.id != null
                    && AppUtil.customerSocket.id > 5) {

                    posAPI
                        .allUrl('/', AppUtil.restUrls.order.checkFreeChaiDelivery)
                        .get(AppUtil.customerSocket.id)
                        .then(function (response) {
                            console.log(response)
                            if (response != undefined && response != null && response == true) {
                                $scope.showFreeChaiDelivery = true;
                            }
                        }, function (err) {
                            AppUtil.myAlert(err.data.errorMessage);
                        });
                }
            };

            $scope.sendFreeChaiDeliverySms = function () {
                $scope.customerPendingCardInfo = null;
                if (AppUtil.customerSocket != undefined && AppUtil.customerSocket != null
                    && AppUtil.customerSocket.id != undefined && AppUtil.customerSocket.id != null
                    && AppUtil.customerSocket.id > 5) {

                    posAPI
                        .allUrl('/', AppUtil.restUrls.order.sendFreeChaiDeliverySms + AppUtil.customerSocket.id)
                        .post()
                        .then(function (response) {
                            console.log(response)
                            $scope.showFreeChaiDelivery = false;
                        }, function (err) {
                            AppUtil.myAlert(err.data.errorMessage);
                        });
                }
            };

            $scope.hasCustomerDetails = function () {
                return AppUtil.customerSocket != null && AppUtil.customerSocket.name != null;
            };

            $scope.freeChaiModalOpen = function () {
                if (!$scope.secondChai && $scope.offerApplied) {
                    bootbox.alert("Please remove already applied offer.");
                } else {
                    var modalInstance = $modal.open({
                        animation: true,
                        templateUrl: window.version + "views/freeChaiModal.html",
                        controller: 'freeChaiModalCtrl',
                        backdrop: 'static',
                        scope: $scope,
                        size: "lg"
                    });
                    modalInstance.result.then(function (result) {
                        if (AppUtil.customerSocket.eligibleForSignupOffer || $scope.freeKettle) {
                            $scope.secondChai = true;
                            $scope.offerCode = result;
                            $scope.offerApplied = true;
                        }
                        $scope.redemptionApplied = true;
                        productService.metaDataList.removeAttribute("SKIP_FREE_CHAI");
                    }, function () {
                        if (AppUtil.customerSocket.eligibleForSignupOffer || $scope.freeKettle) {
                            $scope.secondChai = false;
                            $scope.offerCode = null;
                            $scope.offerApplied = false;
                        }
                        $scope.redemptionApplied = false;
                        productService.metaDataList.removeAttribute("REDEMPTION");
                        //console.log("modal instance dismissed");
                    });
                }
            }

            $scope.tableNumberModal = function () {

                if (!AppUtil.isEmptyObject(tableService.getCurrentTable())) {
                    if ($scope.isSpecialOrder) {
                        tableService.setCurrentTable(null);
                    } else {
                        $scope.tableNumber = tableService.getCurrentTable().tableNumber;
                        $scope.tableRequestId = tableService.getCurrentTable().tableRequestId;
                    }
                } else if ($scope.unitDetails.tableService && !$scope.isSpecialOrder) {
                    var modalInstance = $modal.open({
                        animation: true,
                        templateUrl: window.version + "views/tableNumberModal.html",
                        controller: 'tableNumberModalCtrl',
                        backdrop: 'static',
                        scope: $scope,
                        size: "lg"
                    });
                    modalInstance.result.then(function (result) {
                        $scope.tableNumber = result;
                    }, function () {
                        //console.log("modal instance dismissed");
                    });
                }
            };

            $scope.complimentaryOrderModal = function () {
                if ($scope.isComplimentaryOrder) {
                    var modalInstance = $modal.open({
                        animation: true,
                        templateUrl: window.version + "views/complimentaryOrderModal.html",
                        controller: 'complimentaryOrderModalCtrl',
                        backdrop: 'static',
                        scope: $scope,
                        rootScope: $rootScope,
                        size: "lg"
                    });
                    modalInstance.result.then(function (result) {
                        $rootScope.complimentaryReasonId = result.id;
                        $rootScope.complimentaryReasonCode = result.name;
                    }, function () {
                        //console.log("modal instance dismissed");
                    });
                } else if ($scope.isWastageOrder) {
                    $rootScope.complimentaryReasonId = 2108;
                    $rootScope.complimentaryReasonCode = "Wastage";
                } else if ($scope.isUnsatisfiedCustomerOrder) {
                    $rootScope.complimentaryReasonId = 2102;
                    $rootScope.complimentaryReasonCode = "Unsatisfied Customer";
                }
            };

            $scope.employeeMealModal = function () {

                if ($scope.isEmployeeMeal) {
                    var modalInstance = $modal.open({
                        animation: true,
                        templateUrl: window.version + "views/employeeMealModal.html",
                        controller: 'employeeMealModalCtrl',
                        backdrop: 'static',
                        keyboard: false,
                        scope: $scope,
                        size: "lg"
                    });
                    modalInstance.result.then(function (result) {
                        $scope.employeeMealUser = result;
                        $rootScope.complimentaryReasonId = 2100;
                    }, function () {
                        //console.log("modal instance dismissed");
                    });
                }

                if (AppUtil.isPaidEmployeeMeal()) {
                    var modalInstance = $modal.open({
                        animation: true,
                        templateUrl: window.version + "views/paidEmployeeMealModal.html",
                        controller: 'paidEmployeeMealModalCtrl',
                        backdrop: 'static',
                        keyboard: false,
                        scope: $scope,
                        size: "lg"
                    });
                    modalInstance.result.then(function (result) {
                        $scope.employeeMealUser = result;
                    }, function () {
                        //console.log("modal instance dismissed");
                    });
                }
            };

            $scope.discountModalOpen = function () {
                if ($scope.orderItemArray.length > 0) {
                    if (!$scope.redemptionApplied && !$scope.offerApplied) {
                        var modalInstance = $modal.open({
                            animation: true,
                            templateUrl: window.version + 'views/discountModal.html',
                            controller: 'ModalInstanceCtrl',
                            backdrop: 'static',
                            scope: $scope,
                            size: 'sm'
                        });
                    } else {
                        AppUtil.myAlert($scope.onlyOneOfferAlert);
                    }
                } else {
                    AppUtil.myAlert($scope.atLeastOneItem);
                }
            };


            $scope.offerModalOpen = function (fromChaayosCash) {
                if (!$scope.hasComboItems() && !$scope.discountApplied && !$scope.redemptionApplied) {
                    if (($scope.orderItemArray.length > 0 && !$scope.fullComplimentaryDetail.isOrderComplimentary)) {
                        if ($scope.isCOD) {
                            $scope.isNewCustomer = AppUtil.CSObj.newCustomer;
                        }
                        var modalInstance = $modal.open({
                            animation: true,
                            templateUrl: window.version + 'views/offerModal.html',
                            controller: 'offerModalCtrl',
                            backdrop: 'static',
                            scope: $scope,
                            size: 'md',
                            resolve: {
                                fromChaayosCash: function () {
                                    return fromChaayosCash;
                                }
                            }
                        });
                        modalInstance.result.then(function (result) {
                            $scope.modifyOrderAfterCoupon(result);
                            $scope.calculatePaidAmount();
                            $scope.offerApplied = true;

                            // open settlement modal in case prepaid is enabled
                            if (result.prepaidAmount != undefined && result.prepaidAmount != null) {
                                $scope.prepaidAmount = result.prepaidAmount;
                                //$scope.validateGiftCard();
                            }

                            try {
                                trackingService.trackOfferSuccess({
                                    coupon: $scope.offerCode,
                                    discount: $scope.transactionObj.discountDetail.totalDiscount
                                });
                            } catch (e) {
                            }
                            //$scope.recommendationOfferApplied = false;
                        }, function () {
                            //console.log("modal instance dismissed");
                        });
                    } else {
                        if ($scope.orderItemArray.length == 0) {
                            AppUtil.myAlert($scope.atLeastOneItem);
                        }
                        /*if ($scope.recommendationOfferApplied) {
                            AppUtil.myAlert("Recommendation Offer is applied. Please remove the recommendation offer before applying coupon");
                        }*/
                        if ($scope.fullComplimentaryDetail.isOrderComplimentary) {
                            AppUtil.myAlert("Coupon cannot be applied if order marked as complimentary");
                        }
                    }
                } else {
                    AppUtil.myAlert($scope.onlyOneOfferAlert);
                }
            };

            $scope.suscriptionModalOpen = function (fromChaayosSelect) {
                if (!$scope.hasComboItems() && !$scope.discountApplied && !$scope.redemptionApplied) {
                    if (($scope.orderItemArray.length > 0 && !$scope.fullComplimentaryDetail.isOrderComplimentary)) {
                        if ($scope.isCOD) {
                            $scope.isNewCustomer = AppUtil.CSObj.newCustomer;
                        }
                        var modalInstance = $modal.open({
                            animation: true,
                            templateUrl: window.version + 'views/subscriptionModal.html',
                            controller: 'subscriptionModalCtrl',
                            backdrop: 'static',
                            scope: $scope,
                            size: 'md',
                            resolve: {
                                fromChaayosSelect: function () {
                                    return fromChaayosSelect;
                                }
                            }
                        });
                        modalInstance.result.then(function (result) {
                            $scope.modifyOrderAfterCoupon(result);
                            $scope.calculatePaidAmount();
                            $scope.offerApplied = true;
                            productService.metaDataList.addAttribute("CHAAYOS_SELECT_APPLIED", moment().format());
                            $scope.isOfferApplied = true;

                            // open settlement modal in case prepaid is enabled
                            if (result.prepaidAmount != undefined && result.prepaidAmount != null) {
                                $scope.prepaidAmount = result.prepaidAmount;
                                //$scope.validateGiftCard();
                            }

                            try {
                                trackingService.trackOfferSuccess({
                                    coupon: $scope.offerCode,
                                    discount: $scope.transactionObj.discountDetail.totalDiscount
                                });
                            } catch (e) {
                            }
                        }, function () {
                        });
                    } else {
                        if ($scope.orderItemArray.length == 0) {
                            AppUtil.myAlert($scope.atLeastOneItem);
                        }
                        if ($scope.fullComplimentaryDetail.isOrderComplimentary) {
                            AppUtil.myAlert("Coupon cannot be applied if order marked as complimentary");
                        }
                    }
                } else {
                    AppUtil.myAlert($scope.onlyOneOfferAlert);
                }
            };

            $scope.clearOffer = function () {
                $scope.checkForClearOffer = false;
                productService.clearOffer($scope.orderItemArray, $scope.offerCode,
                    $scope.transactionObj);
                $scope.prepaidAmount = undefined;
                $scope.offerCode = "";
                $scope.offerApplied = false;
                $scope.couponCode = {coupon: ""};
                //console.log($scope.couponCode);
                $scope.applyDisabled = false;
                $scope.cashRedeemed = null;
                $scope.calculatePaidAmount();
            };

            $scope.hasComboItems = function () {
                for (var i in $scope.orderItemArray) {
                    var orderItem = $scope.orderItemArray[i];
                    if (orderItem.productDetails.taxCode == 'COMBO') {
                        return true;
                    }

                }
                return false;
            };
            $scope.modifyOrderAfterCoupon = function (result) {
                //console.log("coupon in result is ::: ",result.couponCode);
                $scope.offerCode = result.couponCode.toUpperCase();
                $scope.awardLoyalty = result.awardLoyalty;
                $scope.appliedOfferMessage = result.appliedOfferMessage;
                $scope.orderItemArraypoints = productService.setOrderItemArray($scope.orderItemArray, result.order.orders);
                $scope.orderItemArray = productService.getOrderArray();
                $scope.transactionObj = productService.modifyTransactionObject(result.order.transactionDetail);
                //hard coded(Marketing voucher) for now, need to re wire from back end later on
                $scope.transactionObj.discountDetail.discountCode = 2004;
                $scope.transactionObj.discountDetail.discountReason = $scope.offerCode;
                $scope.checkForClearOffer = false; // don't clear offer only when they are applying coupon
                $scope.cashRedeemed = result.order.cashRedeemed != null ? result.order.cashRedeemed : 0;
            };

            $scope.remarkModalOpen = function () {
                var modalInstance = $modal.open({
                    animation: true,
                    templateUrl: window.version + 'views/remarkModal.html',
                    controller: 'ModalInstanceCtrl',
                    backdrop: 'static',
                    scope: $scope,
                    size: 'sm'
                });
                modalInstance.result.then(function (orderRemark) {
                    $scope.orderRemark = orderRemark;
                }, function () {
                    //console.log("remarkModalOpen dismissed");
                });
            };

            $scope.fullCustomizeModalOpen = function () {
                if ($scope.orderItemArray.length > 0) {

                    var modalInstance = $modal.open({
                        animation: true,
                        templateUrl: window.version + 'views/fullOrderComplimentaryModal.html',
                        controller: 'fullOrderComplimentary',
                        scope: $scope,
                        backdrop: 'static',
                        size: 'md'
                    });

                    modalInstance.result.then(function (orderComplimentaryDetail) {
                        $scope.fullComplimentaryDetail = orderComplimentaryDetail;
                    }, function (err) {

                    });

                } else {
                    AppUtil.myAlert($scope.atLeastOneItem);
                }

            };

            $scope.getFirstWord = function (str) {
                if (str.indexOf(" ") > 0) {
                    return str.substr(0, str.indexOf(" "));
                } else {
                    return str;
                }
            };

            $scope.markFavChai1 = function (orderItem, isHeartButtonClicked,index) {
                console.log("OrderItem's item id ie its index in cart ", orderItem.orderDetails.itemId);
                //get index of previously saved chai in order item cart .(there can be saved chai of same type multtiple times in cart)
                var copyOrderItemArray = [];
                var currentOrderItem=null;
                if($scope.orderItemArray!=null && $scope.orderItemArray.length!=0){
                    copyOrderItemArray = angular.copy($scope.orderItemArray);
                    if(orderItem.orderDetails.itemId>=0){
                        currentOrderItem = copyOrderItemArray.splice(orderItem.orderDetails.itemId-1,1);
                    }
                }
                var prevSavedChaiIndices =$scope.getIndexOfPreviousySavedChai($scope.orderItemArray);
                AppUtil.getCustomerBasicInfo(function (customerBasicInfo) {
                    $scope.customerBasicInfo = customerBasicInfo;
                });
                if (isHeartButtonClicked) {
                    $scope.isFavChaiMarked = (orderItem.isFavChaiMarked == undefined || orderItem.isFavChaiMarked==null )? true : !orderItem.isFavChaiMarked;
                    orderItem.isFavChaiMarked = $scope.isFavChaiMarked;
                    //    Step 1 : Mark the orderItem whose productId matches with customer's fav chai mapping product and it's not the last element of orderItemArray
                    //    Step 2: Update prev saved chai in orderItemArray
                    var previousFavChaiOrderItem = $scope.updatePreviousFavChaiMappings($scope.customerBasicInfo.id, orderItem,copyOrderItemArray);
                    if( previousFavChaiOrderItem!=null && prevSavedChaiIndices !=null && prevSavedChaiIndices.length>0 ){
                        for(var i in prevSavedChaiIndices ){
                            $scope.orderItemArray[prevSavedChaiIndices[i]].isFavChaiMarked=previousFavChaiOrderItem.isFavChaiMarked;
                            $scope.orderItemArray[prevSavedChaiIndices[i]].isDefaultFavChaiSelected=previousFavChaiOrderItem.isDefaultFavChaiSelected;
                        }
                    }
                    //    Step 3: Mark current orderItem as fav chai and then update the active customer fav chai mappings
                    //         3.1: get fav chai req object
                    $scope.favChaiRequestObject = AppUtil.getFavChaiRequestObject(orderItem, $scope.unitDetails.id, $scope.customerBasicInfo);
                    console.log("***Fav Chai Object from order item cart inside  markFavChai *** ", $scope.favChaiRequestObject);
                    if ($scope.favChaiRequestObject.isFavChaiMarked != undefined) {
                        $scope.saveCustomerFavChai($scope.favChaiRequestObject, function () {
                            $scope.getCustomerFavChaiMappings($scope.customerBasicInfo.id,orderItem);
                        });
                    }
                }
            }

            $scope.getIndexOfPreviousySavedChai=function(copyOrderItemArray){
                var indexOfPreviouslySavedChai=[];
                if(copyOrderItemArray.length>0){
                    for(var i in copyOrderItemArray){
                        if(copyOrderItemArray[i].isFavChaiMarked!=undefined && copyOrderItemArray[i].isFavChaiMarked){
                            indexOfPreviouslySavedChai.push(i);
                        }
                    }
                }
                return indexOfPreviouslySavedChai;
            }

            $scope.updatePreviousFavChaiMappings =function(customerId, orderItem,copyOrderItemArray){
                var previousFavChaiOrderItem = null;
                for(var i =0;i<copyOrderItemArray.length;i++){
                    var item =copyOrderItemArray[i];
                    if($rootScope.customerFavChaiMappings.length>0){
                        for(var j in $rootScope.customerFavChaiMappings){
                            if(item.isFavChaiMarked !=undefined && item.isFavChaiMarked && $rootScope.customerFavChaiMappings[j].productId==item.orderDetails.productId){
                                item.isFavChaiMarked= false;
                                item.isDefaultFavChaiSelected=undefined;
                                previousFavChaiOrderItem=angular.copy(item);
                            }
                        }
                    }
                }
                return previousFavChaiOrderItem;
            }

            $scope.getCustomerFavChaiMappings=function (customerId,orderItem){
                var list= [];
                AppUtil.getCustomerFavChaiMappings(customerId,function(arr){
                    // list =list.concat(arr);
                    if(arr!=undefined && arr !=null){
                       /* for(var i =0 ; i< arr.length ;i++){
                            var favChai = arr[i];
                            if (favChai.tagType !=undefined && favChai.tagType !=null && favChai.tagType.toUpperCase()==="MERI WALI CHAI"){
                                list=list.concat(favChai);
                            }
                        }*/
                        console.log("Before updating customerFavChaiMappings :::::::::", $rootScope.customerFavChaiMappings.length, $rootScope.customerFavChaiMappings);
                        $rootScope.customerFavChaiMappings=[];
                        $rootScope.customerFavChaiMappings =$rootScope.customerFavChaiMappings.concat(arr);
                        console.log("After updating customerFavChaiMappings :::::::::", $rootScope.customerFavChaiMappings.length, $rootScope.customerFavChaiMappings);
                    }
                });
            }

            $scope.customizeNewModalOpen = function (orderItem, menuProduct) {
                AppUtil.getCustomerBasicInfo(function(customerBasicInfo){
                    $scope.customerBasicInfo= customerBasicInfo;
                });
                var customerObj = $scope.customerBasicInfo;
                if (desiChaiService.getDesiChaiProductIds().indexOf(orderItem.productDetails.id) >= 0
                    || desiChaiService.getBaarishWaliChaiProductIds().indexOf(orderItem.productDetails.id) >= 0) { //for desi chai
                    var modalInstance = $modal.open({
                        animation: true,
                        templateUrl: window.version + 'views/desiChaiModal.html',
                        controller: 'desiChaiModalCtrl',
                        scope: $scope,
                        backdrop: 'static',
                        windowClass: 'app-modal-window',
                        resolve: {
                            orderItem: function () {
                                return orderItem;
                            },
                            isDesiChai: function () {
                                return desiChaiService.getDesiChaiProductIds().indexOf(orderItem.productDetails.id) >= 0;
                            },
                            customerObj:function(){
                                return customerObj;
                            }
                        }
                    });
                    modalInstance.result.then(function (selectedItem) {
                        var isFavChaiMarkedForOrderItem = selectedItem.isFavChaiMarked;
                        if(selectedItem.isHeartButtonClicked !=undefined && isFavChaiMarkedForOrderItem!=undefined && isFavChaiMarkedForOrderItem!=null){
                            selectedItem.isFavChaiMarked =!isFavChaiMarkedForOrderItem;
                            $scope.markFavChai1(selectedItem,selectedItem.isHeartButtonClicked);
                        }
                        if (typeof menuProduct != "undefined" && menuProduct != null) {
                            menuProduct.item = selectedItem;
                            menuProduct.product.name = selectedItem.productDetails.name;
                            menuProduct.product.productId = selectedItem.productDetails.id;
                            menuProduct.product.type = selectedItem.productDetails.type;
                            menuProduct.product.subType = selectedItem.productDetails.subType;
                            menuProduct.product.shortCode = selectedItem.productDetails.shortCode;
                            menuProduct.product.classification = selectedItem.productDetails.classification;
                            menuProduct.product.isInventoryTracked = selectedItem.productDetails.inventoryTracked;
                        } else {1
                            $scope.orderItemArray.map(function (item, index) {
                                if (item.orderDetails.itemId == selectedItem.orderDetails.itemId) {
                                    $scope.orderItemArray.splice(index, 1, selectedItem);
                                }
                            });
                        }

                    }, function () {
                        console.log('Modal dismissed at: ' + new Date());
                    });
                } else {
                    var modalInstance =$modal.open({
                        animation: true,
                        templateUrl: window.version + 'views/customizeNewModal.html',
                        controller: 'addOnNewModalInstanceCtrl',
                        scope: $scope,
                        backdrop: 'static',
                        windowClass: 'app-modal-window',
                        resolve: {
                            orderItem: function () {
                                return orderItem;
                            },
                            customerObj:function(){
                               return customerObj;
                            }
                        }
                    });
                    modalInstance.result.then(function(selectedItem){
                        var isFavChaiMarkedForOrderItem = selectedItem.isFavChaiMarked;
                        if(selectedItem.isHeartButtonClicked !=undefined && isFavChaiMarkedForOrderItem!=undefined && isFavChaiMarkedForOrderItem!=null){
                            selectedItem.isFavChaiMarked =!isFavChaiMarkedForOrderItem;
                            $scope.markFavChai1(selectedItem,selectedItem.isHeartButtonClicked);
                        }
                    },function(){
                        console.log('Modal dismissed at: ' + new Date());
                    });
                }
            };

            $scope.saveCustomerFavChai= function (reqObj,callback){
                if(reqObj.isFavChaiMarked ===false){
                    $rootScope.customerFavChaiMappings=[];
                }
                var url = AppUtil.restUrls.favChai.saveMyChai;
                if(reqObj.customerBasicInfo.id !=null && reqObj.customerBasicInfo.id !=undefined && reqObj.customerBasicInfo.id>5){
                    posAPI.allUrl('/', url).post(reqObj).then(
                        function(response){
                            if(response!=undefined && response!=null){
                                var responseObj = response.plain();
                                $scope.favChaiCustomizationId = responseObj.customizationId;
                                // bootbox.alert("Saved chai successfully");
                                if(callback!=undefined){
                                    callback();
                                }
                            }
                        },function (err){
                            AppUtil.myAlert(err.data.errorMessage);
                        })
                }
            }
            $scope.orderItemComplimentary = function (orderItem) {
                $modal.open({
                    animation: true,
                    templateUrl: window.version + 'views/orderItemComplimentary.html',
                    controller: 'addOnNewModalInstanceCtrl',
                    scope: $scope,
                    backdrop: 'static',
                    size: 'md',
                    resolve: {
                        orderItem: function () {
                            return orderItem;
                        }
                    }
                });
            };
            $scope.validateGiftCardModalOpen = function () {
                $scope.isGiftCardModalOpen = true;
                $scope.isOfferModalopen = true;
                var giftObject = {};
                giftObject.type = 'ECARD';
                $scope.$parent.billSetllementFromGiftCard = true;
                var modalInstance1 = $modal.open({
                    animation: true,
                    templateUrl: window.version + 'views/suggestGiftCardModal.html',
                    controller: 'SuggestGiftCardCtrl',
                    scope: $scope,
                    backdrop: 'static',
                    keyboard: false,
                    resolve: {
                        giftObject: function () {
                            return giftObject;
                        },
                        giftCard: function () {
                            return $scope.customerPendingCardInfo;
                        },
                        totalAmount:function(){
                            return $scope.transactionObj.totalAmount;
                        },
                        offerApplied:function(){
                            return $scope.offerApplied;
                        },
                        subscriptionDetails:function (){
                            if(!AppUtil.isEmptyObject($scope.chaayosCash) && !AppUtil.isEmptyObject($scope.chaayosCash.subscriptionInfoDetail)){
                                return $scope.chaayosCash.subscriptionInfoDetail;
                            }
                            else{
                                return null;
                            }
                        },
                        amountPayable: function () {
                            return $scope.transactionObj.paidAmount;
                        },
                        orderDetail: function () {
                            return $scope.orderItemArray;
                        }
                    }
                });

                modalInstance1.result.then(function (action) {
                    bootbox.hideAll();
                    console.log("gift card modal dismissed");
                    $scope.isGiftCardModalOpen = false;
                    $scope.isOfferModalopen = false;
                    // if($rootScope.isSavingsScreenOpen){
                    //     socketUtils.emitMessage({SUBSCRIPTION_SAVINGS_CLOSE:{}});
                    // }
                    restoreLastState();
                    AppUtil.cardPaymentModule = AppUtil.walletPayment == true;
                    if (!AppUtil.closeByButton) {
                        $scope.startRegularOrder();
                    }
                    $scope.$parent.billSetllementFromGiftCard = false;
                    /* to handle Applied Coupon */
                    $scope.couponFlagChangeModule = true; // it will be false when it goes into the function
                    AppUtil.closeByButton = false;
                }, function () {
                    $scope.isGiftCardModalOpen = false;
                });
            }

            function getOfferApplicable() {
                var offerObj = JSON.parse(localStorage.getItem("autoApplicableOfferForUnit"));
                if(offerObj !== null) {
                    $scope.offerApplicableForUnitExists.validateCustomer = offerObj.offer.validateCustomer;
                    $scope.offerApplicableForUnitExists.validateUnit = offerObj.status==='ACTIVE';
                    $scope.offerApplicableForUnitExists.coupon = offerObj.code;
                } else {
                    $scope.offerApplicableForUnitExists.showIcon = true;
                }
            }
            $scope.applyAutoApplicableOffer =function () {
                $rootScope.showFullScreenLoader = true;
                if($scope.offerApplicableForUnitExists !== null) {
                    $scope.offerApplicableForUnitExists.showIcon = true;
                    if(!$scope.offerApplied){
                        $scope.applyOffer();
                    }
                } else {
                    $scope.offerApplicableForUnitExists.showIcon = true;
                }
                $rootScope.showFullScreenLoader = false;
            }

            $scope.isLoyalTeaRedeemed = function (){
                return isRedemptionDone;
            }

            $scope.isSelectPresent = function (){
                var isSelectPresentFlag = false;
                var subProductIDs = JSON.parse(localStorage.getItem("Subscription_Products"));
                subProductIDs.forEach(function(proid){
                    if(proid === 1000064){
                        isSelectPresentFlag = true;
                    }
                });
                return isSelectPresentFlag;
            }
            $scope.applyOffer = function () {
                $timeout(
                    $scope.applyCouponTimeOut()
                );
            };

            $scope.checkForComplimentaryItems = function (orderItemArray) {
                var flag = false;
                orderItemArray.forEach(function (orderItem) {
                    if (orderItem.orderDetails.complimentaryDetail.isComplimentary) {
                        flag = true;
                    }
                });
                return flag;
            };

            $scope.applyCouponTimeOut = function () {
                $scope.error = "";
                $scope.offerDescription = "";
                var reqObj = {
                    order: productService.prepareOrder($scope.orderItemArray, $scope.transactionObj),
                    couponCode: $scope.offerApplicableForUnitExists.coupon,
                    newCustomer: $scope.isNewCustomer
                };
                if (AppUtil.isCOD()) {
                    reqObj.contact = AppUtil.CSObj.contactNumber;
                }
                if($scope.checkForComplimentaryItems($scope.orderItemArray)){
                    return;
                }
                if (!$scope.hasComboItems() && !$scope.discountApplied && !$scope.redemptionApplied) {
                    if (($scope.orderItemArray.length > 0 && !$scope.fullComplimentaryDetail.isOrderComplimentary)) {
                        if (AppUtil.customerSocket != null && AppUtil.customerSocket.id != null && AppUtil.customerSocket.id > 5) {
                            reqObj.order.customerId = AppUtil.customerSocket.id;
                        }
                        if (AppUtil.isCafe()) {
                            reqObj.order.channelPartner = 1;
                        }
                        if ($scope.offerApplicableForUnitExists.coupon.length != 0
                            && !AppUtil.isEmptyObject(reqObj.order) && ($scope.fromChaayosCash || (!$scope.fromChaayosCash && $scope.offerApplicableForUnitExists.coupon.toUpperCase() != 'CHAAYOS_CASH'))) {
                            $scope.offerApplicableForUnitExists.coupon = $scope.offerApplicableForUnitExists.coupon.toUpperCase();
                            try {
                                trackingService.trackCouponTried($scope.offerApplicableForUnitExists.coupon)
                            } catch (e) {
                            }
                            productService.metaDataList.appendAttribute("COUPON_CODE_APPLIED", $scope.offerApplicableForUnitExists.coupon);
                            var apiLink = $scope.fromChaayosCash ? AppUtil.restUrls.customerOffers.applyCash : AppUtil.restUrls.customerOffers.applyCoupon;
                            posAPI.allUrl('/', apiLink).post(reqObj).then(function (response) {
                                $scope.applyDisabled = true;
                                if (response != undefined) {
                                    var responseObj = JSON.stringify(response.plain());
                                    var result = JSON.parse(responseObj);
                                    $scope.modifyOrderAfterCoupon(result);
                                    $scope.calculatePaidAmount();
                                    $scope.offerApplied = true;
                                    $scope.isOfferApplied = true;

                                    // open settlement modal in case prepaid is enabled
                                    if (result.prepaidAmount != undefined && result.prepaidAmount != null) {
                                        $scope.prepaidAmount = result.prepaidAmount;
                                    }
                                    try {
                                        trackingService.trackOfferSuccess({
                                            coupon: $scope.offerCode,
                                            discount: $scope.transactionObj.discountDetail.totalDiscount
                                        });
                                    } catch (e) {
                                    }
                                }
                            }, function (err) {
                                console.log(err);
                                try {
                                    trackingService.trackOfferFailed({
                                        coupon: $scope.offerApplicableForUnitExists.coupon,
                                        errorCode: "",
                                        reason: "API FAILED KETTLE"
                                    });
                                } catch (e) {
                                }
                            });
                        }
                    }
                }
            };

            $scope.suggestMembership = function (type) {
                if (!AppUtil.customerSocket || !AppUtil.customerSocket.contactVerified) {
                    bootbox.alert("Chaayos Select can be purchased by registered customers only. Please ask customer to verify his contact number first.");
                    return false;
                }
                $scope.isChaayosSelectModalOpen = true;
                $scope.$parent.billSetllementFromGiftCard = true;
                $scope.isOfferModalopen = true;
                var selectObject = {};
                selectObject.type = type;
                var modalInstance = $modal.open({
                    animation: true,
                    templateUrl: window.version + 'views/suggestChaayosSelectModal.html',
                    controller: 'suggestChaayosSelectModalCtrl',
                    keyboard:false,
                    scope: $scope,
                    backdrop: 'static',
                    resolve: {
                        selectObject: function () {
                            return selectObject;
                        },
                        totalAmount:function(){
                            return $scope.transactionObj.totalAmount;
                        },
                        offerApplied:function(){
                            return $scope.offerApplied;
                        },
                        amountPayable: function () {
                            return $scope.transactionObj.paidAmount;
                        },
                    }

                });

                modalInstance.result.then(function (action) {
                    console.log("2874 ,Chaayos Select Card modal dismissed");
                    bootbox.hideAll();
                    if($rootScope.isSavingsScreenOpen){
                        socketUtils.emitMessage({SUBSCRIPTION_SAVINGS_CLOSE:{}});
                    }
                    $scope.isChaayosSelectModalOpen = false;
                    $scope.$parent.billSetllementFromGiftCard = false;
                    $scope.isOfferModalopen = false;
                    restoreLastState();
                }, function () {
                    $scope.isChaayosSelectModalOpen = false;
                });
            }
            $scope.validateGiftCard = function () {
                if ($scope.orderItemArray.length > 0) {
                    for (var i = 0; i < $scope.orderItemArray.length; i++) {
                        var orderItem = $scope.orderItemArray[i];
                        if (orderItem.orderDetails.quantity == 0 || orderItem.orderDetails.quantity == null) {
                            window.alert("The quantity of " + orderItem.orderDetails.productName + " is zero");
                            return;
                        }
                    }
                }
                if(!$scope.paymentDisable){
                    if ($scope.chaayosCash!=null && $scope.chaayosCash.subscriptionInfoDetail != null && $scope.chaayosCash.subscriptionInfoDetail != undefined
                        && $scope.chaayosCash.subscriptionInfoDetail.hasSubscription && $scope.offerCode != $scope.chaayosCash.subscriptionInfoDetail.subscriptionCode) {
                        bootbox.confirm({
                            size: "small",
                            message: "Customer is Chaayos Select Member Ask them to Avail Offer!!!",
                            buttons: {
                                cancel: {
                                    label: 'Continue without offer'
                                }
                            },
                            callback: function (result) {
                                if (result) {
                                    productService.metaDataList.addAttribute("CHAAYOS_SELECT_SELECTED", Date.now());
                                    return;
                                }
                                else {
                                    productService.metaDataList.addAttribute("CHAAYOS_SELECT_NOT_SELECTED", Date.now());
                                    $scope.validateGiftCardModalOpen();
                                }
                            }
                        });
                    }
                    else {
                        $scope.validateGiftCardModalOpen();
                    }
                }
                else{
                      bootbox.alert("Customer's Chaayos birthday offer is generating please wait..");
                }

            };

            $scope.startRegularOrder = function () {
                //  $scope.paymentId = 'POS_' + 'U' + $scope.unitDetails.id + 'T' + AppUtil.getAutoConfigData().selectedTerminalId + Date.now();
                //console.log($scope.orderItemArray);
                if ($scope.giftCardCodePending()) {
                    bootbox.alert("Please ask customer to enter pending gift card code.");
                    socketUtils.emitMessage({GIFT_CARD_ADDED: null});
                } else {
                    var isValid = true;
                    $scope.orderItemArray.forEach(function (item) {  //TODO Mohit Malik fix this add product type gift card
                        if (item.productDetails.taxCode == "GIFT_CARD" && (item.orderDetails.complimentaryDetail.isComplimentary ||
                            (item.orderDetails.discountDetail.discountCode != null) || ($scope.offerApplied && $scope.offerCode != "LOYALTEA"))) {
                            isValid = false;
                        }
                    });

                    $scope.calculatePaidAmount();
                    if (isValid) {
                        //$scope.settlementModalOpen(); don't open settlement but check inventory
                        $scope.checkOrder();
                    } else {
                        bootbox.alert("Offer code and complimentary is not applicable on gift cards.");
                    }
                }
            };

            $scope.giftCardCodePending = function () {
                var ret = false;
                $scope.orderItemArray.map(function (item) {
                    if (ret == false && item.productDetails.taxCode == "GIFT_CARD" && (item.orderDetails.itemCode == null
                        || item.orderDetails.itemCode.length != 6 || !item.orderDetails.isCardValid)) {
                        ret = true;
                    }
                });
                return ret;
            };

            $scope.settlementModalOpen = function () {
                if ($scope.orderItemArray.length > 0) {
                    var template = null;
                    var size = null;
                    if (AppUtil.isCOD()) {
                        template = window.version + "views/settlementModal.html";
                        size = "lg";
                    } else {
                        template = window.version + "views/settlementModalPOS.html";
                        size = "lg";
                        productService.metaDataList.appendAttribute("SETTLEMENT_MODEL_OPEN", moment().format());

                        var data = productService.metaDataList.getAttributeValue("SETTLEMENT_CANCELLED");
                        if (data != null) {
                            productService.metaDataList.addAttribute("SETTLEMENT_PRODUCTS",
                                AppUtil.getProductsString(productService.orderItemArray));
                        }
                    }

                    $modal.open({
                        animation: true,
                        templateUrl: template,
                        controller: 'settlementModalCtrl',
                        backdrop: 'static',
                        keyboard: false,
                        scope: $scope,
                        size: size,
                        resolve: {
                            tableCheckout: function () {
                                return false;
                            }
                        }
                    });

                } else {
                    AppUtil.myAlert($scope.atLeastOneItem);
                }
            };

            $scope.redeemModalOpen = function () {
                if (!$scope.offerApplied && !$scope.discountApplied) {
                    var pointsRedeemed = $cookieStore.get('pointsRedeemed');
                    //console.log(pointsRedeemed);
                    if (!pointsRedeemed.pointsRedeemedSuccessfully) {
                        productService.redeem(function (unitDetailSession) {
                            //console.log("unitDetailSession :::: ",unitDetailSession);
                            if (unitDetailSession != null && unitDetailSession.pointsRedeemedSuccessfully) {
                                var modalInstance = $modal.open({
                                    animation: true,
                                    templateUrl: window.version + 'views/redeemCustomerModal.html',
                                    controller: 'redeemModalCtrl',
                                    backdrop: 'static',
                                    size: 'sm',
                                    resolve: {
                                        customer: function () {
                                            return unitDetailSession.customer;
                                        },
                                        loyaltyPointsRedeemed: function () {
                                            return unitDetailSession.loyaltyPoints;
                                        },
                                        productId: function () {
                                            return unitDetailSession.redeemedProductId;
                                        }
                                    }
                                });

                                modalInstance.result.then(function (redemptionApplied) {
                                    if (redemptionApplied) {
                                        $scope.redemptionApplied = redemptionApplied;
                                        $scope.calculatePaidAmount();
                                    }
                                }, function () {
                                    //console.log("redeemModalCtrl dismissed");
                                });

                            } else {
                                AppUtil.myAlert("Customer didn\'t redeemed any points");
                            }
                        });
                    } else {
                        AppUtil.myAlert("Points already redeemed");
                    }
                } else {
                    AppUtil.myAlert($scope.onlyOneOfferAlert);
                }
            };

            $scope.getInventory = function () {
                var unitId;
                if ($scope.isCOD) {
                    if (AppUtil.outlet.selectedId == 1) {
                        unitId = AppUtil.outlet.pri_unitId;
                    } else if (AppUtil.outlet.selectedId == 2) {
                        unitId = AppUtil.outlet.sec_unitId;
                    } else {
                        unitId = AppUtil.outlet.ter_unitId;
                    }
                } else {
                    unitId = $scope.unitDetails.id
                }

                // case if live inventory then
                // send request to live inventory service
                var url = AppUtil.restUrls.posMetaData.inventoryTrimmed;
                if ($scope.unitDetails.liveInventoryEnabled) {
                    var unitZone=JSON.parse(localStorage.getItem('unitDetails')).unitZone;
                    // var InventoryAPI = APIJson.inventory;
                    if(unitZone!=null){
                        url=APIJson.urls.inventory.baseurl+"/"+unitZone.toLowerCase()+
                            APIJson.urls.inventory.postfix+APIJson.urls.inventory.cafe;
                    }else{
                        url=APIJson.urls.inventory.baseurl+"/north"+APIJson.urls.inventory.postfix
                            +APIJson.urls.inventory.cafe;
                    }
                }
                console.log(url);
                posAPI.allUrl('/', url)
                    .post(unitId).then(function (response) {
                    if (!AppUtil.isEmptyObject(response) && typeof response === 'object') {
                        $scope.inventoryData = response;
                        $scope.inventoryDown = false;
                    } else {
                        alert("Inventory is down. We have adjusted the default quantity, please recheck");
                        $scope.inventoryDown = true;
                    }
                }, function (err) {
                    AppUtil.myAlert(err.data.errorMessage);
                });
            };

            $scope.getDimensionInventoryForProductFormatted = function (itemId) {
                var map = $scope.getDimensionInventoryForProduct(itemId);
                var total = $scope.getInventoryForProductFormatted(itemId);
                var itemCount = "";
                if (map != null) {
                    for (var key in map) {
                        if (map.hasOwnProperty(key)) {
                            itemCount = itemCount + " " + key.replace(/[a-z ]/g, "") + ":" + map[key];
                        }
                    }
                }
                return itemCount.trim().length > 0 ? itemCount : total;
            };

            $scope.getInventoryForProductFormatted = function (itemId) {
                var itemCount = $scope.getInventoryForProduct(itemId);
                if (itemCount == null) {
                    itemCount = "";
                }
                return itemCount;
            };

            $scope.getInventoryForProduct = function (itemId) {
                var itemCount = 2;
                if ($scope.inventoryData != null && $scope.inventoryData.length > 0) {
                    $scope.inventoryData.forEach(function (v) {
                        if (v.id === itemId) {
                            itemCount = v.quantity;
                        }
                    });
                }
                return itemCount;
            };

            $scope.getExpiryInventoryForProduct = function (itemId) {
                var itemCount = 0;
                if ($scope.inventoryData != null && $scope.inventoryData.length > 0) {
                    $scope.inventoryData.forEach(function (v) {
                        if (v.id === itemId) {
                            itemCount = v.ex;
                        }
                    });
                }
                return itemCount;
            };

            $scope.getDimensionInventoryForProduct = function (itemId) {
                var map = null;
                if ($scope.inventoryData != null && $scope.inventoryData.length > 0) {
                    $scope.inventoryData.forEach(function (v) {
                        if (v.id === itemId && v.dim != undefined && v.dim != null) {
                            map = v.dim;
                        }
                    });
                }
                return map;
            };

            $scope.checkOrder = function () {
                console.log($scope.orderItemArray);
                if ($scope.orderItemArray.length > 0) {
                    if ($scope.isCOD) {
                        $scope.addDeliveryAndPackagingToOrder();
                    }
                    if ($scope.orderItemArray.length == 0) {
                        AppUtil.myAlert("No items in the order.");
                        return false;
                    }
                    if (!$scope.enquiryItemsChecked()) {
                        AppUtil.myAlert("Please fill quantity and replacement served values in enquiry items properly.");
                        return false;
                    }
                    productService.metaDataList.removeAttribute("FREE_ITEM_OFFER");
                    if ($scope.isCafe && $rootScope.orderType == 'order' && AppUtil.massOfferData != undefined && AppUtil.massOfferData != null
                        && AppUtil.massOfferData.validate
                        && $scope.transactionObj.paidAmount >= AppUtil.massOfferData.minimumAmount
                        && ($scope.offerCode == null || $scope.offerCode == "" || !$scope.offerApplied) && $scope.isInventoryAvailable(AppUtil.massOfferData.productId, AppUtil.massOfferData.quantity) && !$scope.hasComboOrGiftCardOrComplimentary()) {

                        bootbox.alert({
                            title: "Free Offer Confirmation",
                            message: "Customer is eligible for a FREE ITEM Offer. Do you wish to add it?",
                            callback: function (result) {
                                $scope.offerCode = "";
                                $scope.offerModalOpen();

                            }
                        });

                    }
                    $scope.orderChecked = true;
                    if ($scope.isCafe) {
                        $scope.placeOrder();
                    }
                    //var requestObj = AppUtil.GetRequest($scope.getUnitProductMap());
                }
            };

            $scope.addDeliveryAndPackagingToOrder = function () {
                $scope.unitDetails.products.map(function (item) {
                    if (item.id === 1043) {
                        $scope.addNewProductToOrderItemArray(angular.copy(item), 1);
                        $scope.calculatePaidAmount();
                    }
                });
                if ($scope.transactionObj.totalAmount < 200 && !$rootScope.isPartnerOrder) {
                    $scope.unitDetails.products.map(function (item) {
                        if (item.id === 1044) {
                            $scope.addNewProductToOrderItemArray(angular.copy(item), 1);
                            $scope.calculatePaidAmount();
                        }
                    });
                }
            };

            $scope.uncheckOrder = function () {
                //TODO remove delivery packaging items
                $scope.orderItemArray.map(function (item, index) {
                    console.log(item);
                    if ([1043, 1044].indexOf(item.productDetails.id) >= 0) {
                        $scope.deleteItem(index, true);
                    }
                });
                $scope.orderItemArray.map(function (item, index) {
                    console.log(item);
                    if ([1043, 1044].indexOf(item.productDetails.id) >= 0) {
                        $scope.deleteItem(index, true);
                    }
                });
                $scope.calculatePaidAmount();
                $scope.orderChecked = false;
            };

            $scope.placeOrder = function () {
                $scope.openSettlemetModalAfterValidation();
            };

            $scope.hasComboOrGiftCardOrComplimentary = function () {
                for (var i = 0; i < $scope.orderItemArray.length; i++) {
                    var item = $scope.orderItemArray[i];
                    var taxCode = item.productDetails.taxCode;
                    if (taxCode == 'COMBO' || taxCode == 'GIFT_CARD') {
                        return true;
                    }
                    if (item.orderDetails.complimentaryDetail.isComplimentary) {
                        return true;
                    }
                }
                return false;
            };

            $scope.isInventoryAvailable = function (productId, quantity) {
                for (var i = 0; i < $scope.inventoryData.length; i++) {
                    var pInventory = $scope.inventoryData[i];
                    if (pInventory.id == productId && pInventory.quantity >= quantity) {
                        return true;
                    }
                }
                return false;
            };

            $scope.showOrderCheckModal = function () {
                var modalInstance = $modal.open({
                    animation: true,
                    templateUrl: window.version + 'views/checkOrderAgainstInventoryModal.html',
                    controller: 'checkOrderAgainstInventoryModalCtrl',
                    backdrop: 'static',
                    size: 'lg',
                    scope: $scope
                });
                modalInstance.result.then(function (shortItems) {
                    $scope.removeShortItems($scope.shortageItems);
                }, function () {
                    //console.log("checkOrderAgainstInventoryModalCtrl dismissed");
                });
            };
            $scope.openSettlemetModalAfterValidation = function () {
                if (AppUtil.cardType == 'ECARD' || AppUtil.cardType == 'GYFTR' || AppUtil.cardType=='AP01') {
                    $scope.settlementModalOpen();
                } else {
                    $rootScope.showFullScreenLoader = true;
                    //inventory call to check selected products only before placing order
                    var url = AppUtil.restUrls.posMetaData.inventoryForProducts;
                    // products served from back-end with inventory tracked check
                    if (AppUtil.hasLiveInventory()) {
                        var unitZone=JSON.parse(localStorage.getItem('unitDetails')).unitZone;
                        if(unitZone!=null){
                            url=APIJson.urls.inventory.baseurl+"/"+unitZone.toLowerCase()+APIJson.urls.inventory.postfix
                                +APIJson.urls.inventory.cafeProducts;
                        }else{
                            url=APIJson.urls.inventory.baseurl+"/north"+APIJson.urls.inventory.postfix
                                +APIJson.urls.inventory.cafeProducts;
                        }
                    }
                    var productsMap = $scope.getUnitProductMap();
                    if (!AppUtil.isEmptyObject(productsMap.productIds)) {
                        posAPI.allUrl('/', url)
                            .post(productsMap).then(function (response) {
                            $rootScope.showFullScreenLoader = false;
                            if (typeof  response == 'object' && response.plain() != undefined && response.plain() != null && response.plain().length > 0) {
                                if (response.plain() != undefined && response.plain() != null) {
                                    // updates only when there is successful data collected
                                    $scope.productInventoryData = response.plain();
                                } else {
                                    $scope.productInventoryData = [];
                                }
                                $scope.shortageItems = [];
                                $scope.mergedOrderArray = null;
                                $scope.mergedOrderArray = angular.copy($scope.orderItemArray);
                                $scope.mergeSimilarItemsInOrder();
                                $scope.getShortItems();
                                if ($scope.productInventoryData.length > 0 && $scope.shortageItems.length > 0) {
                                    $scope.showOrderCheckModal();
                                } else {
                                    $scope.settlementModalOpen();
                                }
                            } else {
                                if (response == undefined || typeof response == 'string' || response.plain().errorCode || response.plain().length == 0) {
                                    $scope.sendSlackNotificationForInventoryServiceDown(productsMap.unitId);
                                    $scope.settlementModalOpen();
                                }
                            }

                        }, function (err) {
                            $rootScope.showFullScreenLoader = false;
                            AppUtil.myAlert(err.data.errorMessage);


                        });
                    } else {
                        $rootScope.showFullScreenLoader = false;
                        $scope.settlementModalOpen();
                    }

                }
            }
            $scope.sendSlackNotificationForInventoryServiceDown = function (unitId) {
                console.log(AppUtil.customerSocket)
                var payload = {
                    id: unitId,
                    code: AppUtil.customerSocket.id
                }
                $http({
                    method: 'POST',
                    url: AppUtil.restUrls.posMetaData.sendInventoryDownNotification,
                    data: payload
                }).then(function (response) {

                }, function (err) {
                });
            }

            $scope.mergeSimilarItemsInOrder = function () {
                if ($scope.mergedOrderArray.length > 1) {
                    for (var i = 0; i < $scope.mergedOrderArray.length; ++i) {
                        for (var j = i + 1; j < $scope.mergedOrderArray.length; ++j) {
                            if ($scope.mergedOrderArray[i].orderDetails.productId === $scope.mergedOrderArray[j].orderDetails.productId
                                && $scope.mergedOrderArray[i].orderDetails.dimension === $scope.mergedOrderArray[j].orderDetails.dimension) {
                                $scope.mergedOrderArray[i].orderDetails.quantity = $scope.mergedOrderArray[i].orderDetails.quantity +
                                    $scope.mergedOrderArray[j].orderDetails.quantity;
                                $scope.mergedOrderArray.splice(j--, 1);
                            }
                        }
                    }
                }
            }

            $scope.getShortItems = function () {
                // in case something goes wrong and we do not get inventory
                if ($scope.productInventoryData == undefined || $scope.productInventoryData == null) {
                    return;
                }
                $scope.mergedOrderArray.forEach(function (orderItem) {
                    if (orderItem.productDetails.inventoryTracked) {
                        $scope.productInventoryData.forEach(function (pInventory) {
                            if (pInventory.id == orderItem.orderDetails.productId && pInventory.dim != null
                                && pInventory.dim[orderItem.orderDetails.dimension] != null) {
                                if (pInventory.dim[orderItem.orderDetails.dimension] < orderItem.orderDetails.quantity) {
                                    var item = {
                                        id: pInventory.id,
                                        name: orderItem.productDetails.name + (orderItem.orderDetails.dimension == "None" ? "" : " (" + orderItem.orderDetails.dimension + ")"),
                                        orderQuantity: orderItem.orderDetails.quantity,
                                        quantity: pInventory.dim[orderItem.orderDetails.dimension]
                                    }
                                    $scope.shortageItems.push(item);
                                }
                            } else if (pInventory.id == orderItem.orderDetails.productId && pInventory.quantity < orderItem.orderDetails.quantity) {
                                if (pInventory.quantity < orderItem.orderDetails.quantity) {
                                    var item = {
                                        id: pInventory.id,
                                        name: orderItem.productDetails.name,
                                        orderQuantity: orderItem.orderDetails.quantity,
                                        quantity: pInventory.quantity
                                    }
                                    $scope.shortageItems.push(item);
                                }
                            }
                        });
                    }
                });
            };

            $scope.showCouponBtn = function () {
                if ($scope.isCOD && !$scope.orderChecked) {
                    return false;
                }
                return !$scope.isSpecialOrder && $scope.orderType != 'subscription' && !$scope.secondChai;
            };

            $scope.getUnitProductMap = function () {
                var unitId;
                //if($scope.isCheckInventory()){
                if ($scope.isCOD) {
                    if (AppUtil.outlet.selectedId == 1) {
                        unitId = AppUtil.outlet.pri_unitId;
                    } else if (AppUtil.outlet.selectedId == 2) {
                        unitId = AppUtil.outlet.sec_unitId;
                    } else {
                        unitId = AppUtil.outlet.ter_unitId;
                    }
                } else {
                    unitId = $scope.unitDetails.id
                }
                var productList = [];
                $scope.orderItemArray.forEach(function (v) {
                    if (v.productDetails.inventoryTracked) {
                        productList.push(v.productDetails.id);
                    }
                });
                return {unitId: unitId, productIds: productList};
            }

            $scope.removeShortItems = function (shortItems) {
                for (var i = $scope.orderItemArray.length - 1; i >= 0; i--) {
                    var loop = true;
                    shortItems.forEach(function (p) {
                        if (loop && p.id === $scope.orderItemArray[i].orderDetails.productId) {
                            var orderItem = $scope.orderItemArray.splice(i, 1);
                            if (p.quantity < orderItem[0].orderDetails.quantity) {
                                orderItem[0].productDetails.quantity = orderItem[0].orderDetails.quantity;
                                if ($scope.isCOD) {
                                    $scope.addProductToEnquiryItems(orderItem[0].productDetails, p.quantity);
                                }
                                $scope.dropFromOrderMap(orderItem[0].productDetails, p.quantity);
                            }
                            $scope.calculatePaidAmount();
                            loop = false;
                        }
                    });
                }
            };

            $scope.addProductToEnquiryItems = function (productItem, quantity) {
                var contains = false;
                if ($scope.enquiryItems != null) {
                    $scope.enquiryItems.forEach(function (v) {
                        if (v.id === productItem.id) {
                            contains = true;
                        }
                    });
                }
                if (!contains) {
                    var item = {
                        id: productItem.id,
                        name: productItem.name,
                        unitName: $scope.outletName(),
                        dimension: productItem.prices[0].dimension,
                        orderedQuantity: angular.isUndefined(productItem.quantity) ? null : productItem.quantity,
                        availableQuantity: quantity,
                        replacementServed: null,
                        linkedOrderId: null,
                        linkedCustomerId: AppUtil.CSObj.id,
                        linkedUnitId: $scope.outletId()
                    }
                    $scope.enquiryItems.push(item);
                }

            };

            $scope.enquiryItemsChecked = function () {
                var returnVal = true;
                if ($scope.enquiryItems != null) {
                    $scope.enquiryItems.forEach(function (v) {
                        if (v.replacementServed == null || v.replacementServed == "" || v.orderedQuantity == null || v.orderedQuantity == "") {
                            returnVal = false;
                        }
                    });
                }
                return returnVal;
            };
            $scope.checkInOrderMap = function (productItem, action) {
                if (productItem.inventoryTracked) {
                    return $scope.checkValuesInQuantityMap(productItem.id, productItem.prices[0].dimension, action);
                }
                return true;
            };
            $scope.checkValuesInQuantityMap = function (id, dimension, action) {
                var returnVal = true;
                if (action == "add") {
                    var productQuantity = $scope.trackedItemMap[id + "," + dimension];
                    if (productQuantity == null) {
                        $scope.trackedItemMap[id + "," + dimension] = 1;
                    } else {
                        if (productQuantity >= $scope.getInventoryForProduct(id)) {
                            returnVal = false;
                        } else {
                            $scope.trackedItemMap[id + "," + dimension] = productQuantity + 1;
                        }
                    }
                } else if (action == "remove") {
                    var productQuantity = $scope.trackedItemMap[id + "," + dimension];
                    if (productQuantity != null) {
                        $scope.trackedItemMap[id + "," + dimension] = productQuantity - 1;
                    }
                }
                return returnVal;
            };

            /* $scope.getRecommendation = function () {
                 if($scope.giftCardCodePending()){
                     bootbox.alert("Please ask customer to enter pending gift card code.");
                     socketUtils.emitMessage({GIFT_CARD_ADDED:null});
                 }else{
                     var recommendInput = {
                         "newCustomer": AppUtil.customerSocket.newCustomer,
                         "hotBeverageCount": 0,
                         "coldBeverageCount": 0,
                         "foodCount": 0,
                         "othersCount": 0,
                         "merchandiseCount": 0,
                         "unitId": $scope.unitDetails.id,
                         "productIds": [],
                         "offer": $scope.offerApplied
                     };
                     recommendInput = setProductInfo(recommendInput, $scope.orderItemArray);
                     $rootScope.showFullScreenLoader = true;
                     posAPI.allUrl('/',AppUtil.restUrls.rules.recommend).post(recommendInput).then(function (response) {
                         if (response == null) {
                             bootbox.alert("There is no recommendation for the customer");
                         } else {
                             $scope.recommendationDetail = response.plain();
                             AppUtil.customerSocket.recommendationDetail = $scope.recommendationDetail;
                             socketUtils.emitMessage({"RECOMMENDATION": AppUtil.customerSocket});
                         }
                         $scope.recommendationPending = false;
                         $rootScope.showFullScreenLoader = false;
                     }, function (err) {
                         bootbox.alert("Error while getting recommendation for the customer");
                         $rootScope.showFullScreenLoader = false;
                     });
                 }

             };*/

            /*$scope.skipRecommendationOffer = function () {
                $scope.skipRecommendation("POS");
                AppUtil.customerSocket.recommendationDetail = $scope.recommendationDetail;
                $scope.notifyRecommendation(true);
            }*/

            /*$scope.addRecommendation = function () {
                $scope.addRecommendationToArray("POS");
                AppUtil.customerSocket.recommendationDetail = $scope.recommendationDetail;
                $scope.notifyRecommendation(false);
            }*/

            /*$scope.clearRecommendation = function () {
                if ($scope.offerApplied) {
                    AppUtil.myAlert("Remove Coupon to apply redemption offer");
                    return;
                }
                $scope.recommendationDetail.skipRecommendationOfferData = true;
                if ($scope.recommendationOfferApplied) {
                    for (var k in $scope.orderItemArray) {
                        var orderItem = $scope.orderItemArray[k];
                        if (orderItem.orderDetails.recommended) {
                            productService.clearRecommendationOffer($scope.offerCode,
                                orderItem, $scope.recommendationDetail);
                        }
                    }
                    $scope.recommendationOfferApplied = false;
                    $scope.offerCode = "";
                    $scope.calculatePaidAmount();
                }

            }*/

            /*$scope.applyRecommendationOffer = function () {
                if ($scope.offerApplied) {
                    AppUtil.myAlert("Remove Coupon to apply redemption offer");
                    return;
                }
                $scope.recommendationDetail.skipRecommendationOfferData = false;
                if (!$scope.recommendationOfferApplied) {
                    for (var k in $scope.orderItemArray) {
                        var orderItem = $scope.orderItemArray[k];
                        if (orderItem.orderDetails.recommended) {
                            productService.applyRecommendationOffer(orderItem, $scope.recommendationDetail);
                        }
                    }
                    $scope.recommendationOfferApplied = true;
                    $scope.calculatePaidAmount();
                }
            }*/


            /* $scope.notifyRecommendation = function (isSkip) {
                 if (!$scope.recommendationNotification) {
                     $scope.recommendationNotification = true;
                     if(isSkip){
                         socketUtils.emitMessage({SKIP_RECOMMENDATION: AppUtil.customerSocket});
                     }else{
                         socketUtils.emitMessage({RECOMMENDATION_RESULT_POS: AppUtil.customerSocket});
                     }
                 }
             }*/

            /*$scope.addRecommendationToArray = function (source) {
                productService.addRecommendationToOrder($scope.recommendationDetail);
                $scope.recommendationDetail.appliedBy = source;
                $scope.recommendationDetail.availed = true;
                if ($scope.recommendationDetail.offer) {
                    $scope.offerCode = $scope.recommendationDetail.couponCode;
                    $scope.recommendationOfferApplied = true;
                }
                $scope.calculatePaidAmount();
            }*/

            /* $scope.skipRecommendation = function (source) {
                 $scope.recommendationDetail.availed = false;
                 $scope.recommendationDetail.appliedBy = source;
             };*/

            /*function setProductInfo(recommendInput, orderItemArray) {
                if (orderItemArray != null && orderItemArray.length > 0) {
                    orderItemArray.forEach(function (item) {
                        if (item.orderDetails.isCombo) {
                            console.log('Skipping Combo ' + item.productName);
                            return;
                        }
                        recommendInput.productIds.push(item.productDetails.id);
                        if (item.productDetails.type == 5) {
                            recommendInput.hotBeverageCount = recommendInput.hotBeverageCount + item.orderDetails.quantity;
                        } else if (item.productDetails.type == 6) {
                            recommendInput.coldBeverageCount = recommendInput.coldBeverageCount + item.orderDetails.quantity;
                        } else if (item.productDetails.type == 7) {
                            recommendInput.foodCount = recommendInput.foodCount + item.orderDetails.quantity;
                        } else if (item.productDetails.type == 9) {
                            recommendInput.merchandiseCount = recommendInput.merchandiseCount + item.orderDetails.quantity;
                        } else {
                            recommendInput.othersCount = recommendInput.othersCount + item.orderDetails.quantity;
                        }
                    });
                }
                return recommendInput;
            };
*/
            /*$scope.dropFromOrderMap = function (orderItem, quantity) {
                if (orderItem.productDetails.inventoryTracked) {
                   $scope.dropItemsFromOrderMap(orderItem.productDetails.id,orderItem.productDetails.prices[0].dimension, quantity )
                }else if(orderItem.productDetails.taxCode == 'COMBO'){
                    for(var i in orderItem.orderDetails.composition.menuProducts){
                	$scope.dropItemsFromOrderMap(orderItem.orderDetails.composition.menuProducts[i].product.productId,orderItem.orderDetails.composition.menuProducts[i].dimension.code,  quantity)
                    }
                }
            };

            $scope.dropItemsFromOrderMap = function(id, dimension, quantity){
        	 var productQuantity = $scope.trackedItemMap.get(id + "," + dimension);
                 if (productQuantity != null) {
                     if (productQuantity > quantity) {
                         $scope.trackedItemMap.set(id + "," + dimension, productQuantity - quantity);
                     } else {
                         $scope.trackedItemMap.set(id + "," + dimension, null);
                     }
                 }
            }*/
            $scope.dropFromOrderMap = function (productItem, quantity) {
                if (productItem.inventoryTracked) {
                    var productQuantity = $scope.trackedItemMap[productItem.id + "," + productItem.prices[0].dimension];
                    if (productQuantity != null) {
                        if (productQuantity > quantity) {
                            $scope.trackedItemMap[productItem.id + "," + productItem.prices[0].dimension] = productQuantity - quantity;
                        } else {
                            $scope.trackedItemMap[productItem.id + "," + productItem.prices[0].dimension] = null;
                        }
                    }
                }
            };

            $scope.saveEnquiry = function () {
                if (!$scope.enquiryItemsChecked()) {
                    AppUtil.myAlert("Please fill quantity and replacement served values in enquiry items properly.");
                    return false;
                }
                var order = {
                    enquiryItems: $scope.enquiryItems
                };
                var requestObj = AppUtil.GetRequest(order);
                posAPI.allUrl('/', AppUtil.restUrls.order.createOrderEnquiry)
                    .post(requestObj).then(function (response) {
                    if (response == true) {
                        $scope.enquiryItems = [];
                        AppUtil.mySuccessAlert("Enquiry Saved successfully!");
                        $scope.goToCoverScreen();
                        $rootScope.showFullScreenLoader = false;
                    }
                }, function (err) {
                    AppUtil.myAlert(err.data.errorMessage);
                    $rootScope.showFullScreenLoader = false;
                });
            }

            function clearOfferFromOrder() {
                if (!$scope.couponFlagChangeModule) {
                    productService.clearOffer($scope.orderItemArray, $scope.offerCode, $scope.transactionObj);
                    $scope.offerCode = "";
                    $scope.offerApplied = false;
                    bootbox.alert("Re-enter coupon code as the order has been modified");
                    $scope.offerApplicableForUnitExists.showIcon=false;
                    getOfferApplicable();
               }
                $scope.couponFlagChangeModule = false;
            }

            $scope.openGiftCardModal = function (type) {
                console.log("2816 Gift Card Modal Posctrl");
                if (!AppUtil.customerSocket || !AppUtil.customerSocket.contactVerified) {
                    bootbox.alert("Gift cards can be purchased by registered customers only. Please ask customer to verify his contact number first.");
                    return false;
                }
                $scope.isGiftCardModalOpen = true;
                $scope.isOfferModalopen = true;
                var giftObject = {};
                giftObject.type = type;
                console.log("Setting value of billSetllementFromGiftCard true inside openGiftCardModal in posCtrl");
                $scope.$parent.billSetllementFromGiftCard = true;
                var modalInstance = $modal.open({
                    animation: true,
                    templateUrl: window.version + 'views/giftCardModal.html',
                    controller: 'GiftCardCtrl',
                    keyboard:false,
                    scope: $scope,
                    backdrop: 'static',
                    size: "lg",
                    resolve: {
                        giftObject: function () {
                            return giftObject;
                        }
                    }
                });


                modalInstance.result.then(function (action) {
                    console.log("gift card modal dismissed");
                    $scope.$parent.billSetllementFromGiftCard = false;
                    $scope.isGiftCardModalOpen = false;
                    $scope.isOfferModalopen = false;
                    restoreLastState();
                }, function () {
                    $scope.isGiftCardModalOpen = false;
                });
            };

            $scope.openChaayosSelectModal = function (type) {
                if (!AppUtil.customerSocket || !AppUtil.customerSocket.contactVerified) {
                    bootbox.alert("Chaayos Select can be purchased by registered customers only. Please ask customer to verify his contact number first.");
                    return false;
                }
                $scope.isChaayosSelectModalOpen = true;
                $scope.$parent.billSetllementFromGiftCard = true;
                $scope.isOfferModalopen = true;
                var selectObject = {};
                selectObject.type = type;
                var modalInstance = $modal.open({
                    animation: true,
                    templateUrl: window.version + 'views/chaayosSelectModal.html',
                    controller: 'chaayosSelectModalCtrl',
                    keyboard:false,
                    scope: $scope,
                    backdrop: 'static',
                    resolve: {
                        selectObject: function () {
                            return selectObject;
                        }
                    }

                });


                modalInstance.result.then(function (action) {
                    console.log("2874 ,Chaayos Select Card modal dismissed");
                    $scope.isChaayosSelectModalOpen = false;
                    $scope.$parent.billSetllementFromGiftCard = false;
                    $scope.isOfferModalopen = false;
                    restoreLastState();
                }, function () {
                    $scope.isChaayosSelectModalOpen = false;
                });
            };

            $scope.clearOnGiftModalClose = function (closeModal, confirmReq) {
                if (confirmReq) {
                    bootbox.confirm("Are you sure you want to close modal?", function (result) {
                        if (result == true) {
                            $scope.orderItemArray = productService.clearOrderArray();
                            $scope.calculatePaidAmount();
                            productService.resetorderItemIndex();
                            $scope.$apply();
                            closeModal();
                        }
                    });
                } else {
                    $scope.orderItemArray = productService.clearOrderArray();
                    $scope.calculatePaidAmount();
                    productService.resetorderItemIndex();
                    closeModal();
                }
            };
            $scope.clearOnChaayosSelectModalClose = function (closeModal, confirmReq) {
                if (confirmReq) {
                    bootbox.confirm("Are you sure you want to close modal?", function (result) {
                        if (result == true) {
                            $scope.orderItemArray = productService.clearOrderArray();
                            $scope.calculatePaidAmount();
                            productService.resetorderItemIndex();
                            $scope.$apply();
                            closeModal();
                        }
                    });
                } else {
                    $scope.orderItemArray = productService.clearOrderArray();
                    $scope.calculatePaidAmount();
                    productService.resetorderItemIndex();
                    closeModal();
                }
            };

            $scope.backupCurrentState = function () {
                lastOrderItemArray = productService.getOrderArray();
                $scope.orderItemArray = productService.clearOrderArray();
                lastTransactionObj = angular.copy($scope.transactionObj);
                $scope.transactionObj = productService.getTransactionObject();
                lastOfferCode = $scope.offerCode;
                $scope.offerCode = "";
            };

            function restoreLastState() {
                $scope.transactionObj = lastTransactionObj;
                $scope.orderItemArray = productService.setOrderArray(lastOrderItemArray);
                $scope.offerCode = lastOfferCode;
            }

            function setEGiftCard(orderItem) {
                console.log("orderItem ",orderItem);
                console.log("orderItemArray", $scope.orderItemArray);
                if(!AppUtil.isEmptyObject(orderItem)){
                    $scope.orderItemArray.map(function (item) {
                        if (item.orderDetails.itemId == orderItem.orderDetails.itemId) {
                            item.orderDetails.isCardValid = true;
                            item.orderDetails.cardType = AppUtil.cardType;
                        }
                    });
                }

            };

            $scope.$on('updateGiftAmountFired', function (event, args) {
                $scope.getCustomerCardInfo();
            });

            $scope.resetGiftCardCode = function (card) {
                $scope.orderItemArray.map(function (item) {
                    if (item.orderDetails.itemId == card.itemId) {
                        item.orderDetails.cardType = AppUtil.cardType;
                        item.orderDetails.itemCode = '';
                        item.orderDetails.isCardValid = false;
                    }
                });
            };
            $scope.getProductsForCategory = function (subcategoryList, catName, type) {
                // get-cafe-products/expire
                $scope.selectedCategoryType=type;
                if ($rootScope.orderType != 'order' || $scope.isCOD) {
                    if (catName == 'Food') {
                        return false;
                    }
                    $scope.productsForSubCategory = [];
                    for (var i = 0; i < subcategoryList.length; i++) {
                        if (subcategoryList[i].id != 1202) {
                            var productArray = productService.getProductArrayForSubTypeCode(subcategoryList[i].id);
                            for (var j = 0; j < productArray.length; j++) {
                                $scope.productsForSubCategory.push(productArray[j]);
                            }
                        }
                    }
                    AppUtil.sortBy($scope.productsForSubCategory, 'name');
                } else {
                    if (type === 99999) {
                        if ($scope.expiryProducts.length == 0) {
                            var url;
                            var unitZone=JSON.parse(localStorage.getItem('unitDetails')).unitZone;
                            if(unitZone!=null){
                                url = APIJson.urls.inventory.baseurl+"/"+unitZone.toLowerCase()+APIJson.urls.inventory.postfix
                                    + APIJson.urls.inventory.cafeExpiryProducts;
                            }else{
                                url=APIJson.urls.inventory.baseurl+"/north"+APIJson.urls.inventory.postfix+APIJson.urls.inventory.cafeExpiryProducts;
                            }
                            posAPI.allUrl('/', url).customGET("", {
                                unitId: $scope.unitDetails.id,
                                count: 5,
                                brandId: 1
                            }).then(function (response) {
                                if (response != null) {
                                    $scope.expiryProducts = response.plain();
                                }
                                updateRecommendedProducts($scope.expiryProducts, "E");
                            }, function (err) {
                                AppUtil.myAlert(err.data.errorMessage);
                            });
                        }
                        var allRecommendedProducts = {};
                        $scope.recomProducts = allRecommendedProducts;

                        if ($scope.expiryProducts.length != 0 && ($scope.customerType == 'Regular Customer' || $scope.customerType == 'Outstation Customer')) {
                            updateRecommendedProducts($scope.expiryProducts, "E");
                        }
                        updateRecommendedProducts($scope.lastOrderIds, "L");
                        if ($scope.isNewCustomer == true || $scope.customerType == 'First Time Customer') {
                            var coreProducts = AppUtil.getTransactionMetadata().coreProducts.content;
                            var coreProductsIds = [];
                            coreProducts.forEach(function (data) {
                                var productId = parseInt(data["code"]);
                                coreProductsIds.push(productId);
                            });
                            updateRecommendedProducts(coreProductsIds, "C");
                        } else if ($scope.customerType == 'Regular Customer' || $scope.customerType == 'Outstation Customer') {
                            var categoryProducts = AppUtil.getTransactionMetadata().categoryProducts.content;
                            var categoryProductsIds = [];
                            categoryProducts.forEach(function (data) {
                                var productId = parseInt(data["code"]);
                                categoryProductsIds.push(productId);
                            });
                            updateRecommendedProducts(categoryProductsIds, "T");
                        }
                        updateRecommendedProducts($scope.recomProductList, "R");
                    } else {
                        $scope.productsForSubCategory = AppUtil.getProductsOfCategory(type);
                    }

                }
            };

            $scope.skipEmailByCRE = function () {
                if (skipEmailForm) {
                    socketUtils.emitMessage({"EMAIL_FORM_SKIPPED_BY_CRE": AppUtil.customerSocket});
                    skipEmailForm = false;
                } else {
                    socketUtils.emitMessage({"EMAIL_INPUT_SKIPPED_BY_CRE": AppUtil.customerSocket});
                }
                productService.metaDataList.addAttribute("EMAIL_SKIPPED", "BY CRE");
                $scope.showEmailSkipBtn = false;
            };

            $scope.getSwiggyCatalog = function () {
                swiggyOrderService.prepareCatalogData();
            };

            $scope.isLiveInventoryEnabled = function () {
                // true check to handle nulls and undefined
                return $scope.unitDetails.liveInventoryEnabled == true;
            };

            $scope.checkChaayosSelectExpiry = function (chaayosSelect) {
                return chaayosSelect.subscriptionInfoDetail.daysLeft <= 14 && chaayosSelect.subscriptionInfoDetail.daysLeft >=1;
            };

        }]);
