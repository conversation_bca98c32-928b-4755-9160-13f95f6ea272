/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

angular.module('posApp')
    .controller('pullSettlementsViewCtrl', ['$rootScope','$scope','AppUtil','$location','$http',
        'posAPI','$modal',
        function ($rootScope,$scope,AppUtil,$location,$http,posAPI,$modal) {

	    	$scope.init = function(){
	    		AppUtil.validate();
	    		$scope.backToCover = AppUtil.backToCover;
	    		$scope.paymentModes = angular.copy(AppUtil.getTransactionMetadata().paymentModes);
	    		if($scope.paymentModes!=null){
	    			$scope.selectedPaymentMode = $scope.paymentModes[0];
	    		}
	    		$scope.today = new Date(new Date().setDate(new Date().getDate() + 1)).toString();
	    		$scope.showPullView = false;
	    		$scope.listLoading = false;
	    	}
	    	
	    	$scope.getPullSettlements = function(){
	    		if($scope.startDate==null || $scope.startDate=="" || $scope.endDate==null || $scope.endDate==""){
	    			alert("Please select proper start date and end date!");
	    			return false;
	    		}
	    		$scope.listLoading = true;
	    		var obj = {
	    				unitId : $rootScope.globals.currentUser.unitId,
	    				startDate : $scope.startDate,
	    				endDate :  $scope.endDate
	    		}
	    		//var requestObj = AppUtil.GetRequest(obj);
	        	  posAPI.allUrl('/',AppUtil.restUrls.cashManagement.getPullSettlementsForUnit)
	    		  .post(obj).then(function(response) {
	    			 if(response!=null){
	    				$scope.pullSettlements = response.plain();
	    			 }
	  	    		 $scope.listLoading = false;
	    		  }, function(err) {
	    			  AppUtil.myAlert(err.data.errorMessage);
	  	    		  $scope.listLoading = false;
	    		  });
	    	}
	    	
	    	$scope.viewPull = function(settlement){
	    		$scope.listLoading = true;
	    		$scope.selectedSettlement = null;
	            posAPI.allUrl('/',AppUtil.restUrls.cashManagement.enrichPullSettlementsForUnit)
	    		  .post(settlement.id).then(function(response) {
	    			 if(response!=null){
	    				 $scope.selectedSettlement  = response.plain();
	    			 }
	  	    		  $scope.listLoading = false;
	 	    		  $scope.showPullView = true;
	    		  }, function(err) {
	    			  AppUtil.myAlert(err.data.errorMessage);
	  	    		  $scope.listLoading = false;
	    		  });
	    		
	    	}
	    	
	    	$scope.hidePullView = function(){
	    		$scope.showPullView = false;
	    	}
	    	
	    	$scope.viewDenominations = function(settlement){
	    		$scope.listLoading = true;
	    		$scope.selectedSettlement = null;
	    		$scope.denomEntity = null;
	            posAPI.allUrl('/',AppUtil.restUrls.cashManagement.enrichPullSettlementsForUnit)
	    		  .post(settlement.id).then(function(response) {
   	    		  $scope.listLoading = false;
	    			 if(response!=null){
	    				 $scope.selectedSettlement  = response.plain();
	    				 $scope.denomEntity = $scope.selectedSettlement.settlementDenominations;
	    				 $modal.open({
			                animation: true,
			                templateUrl: window.version+"scripts/modules/modals/viewDenominationModal.html" ,
			                controller: 'viewDenominationModalCtrl',
			                backdrop: 'static',
			                scope: $scope,
			                size: "lg"
			            });
	    			  }
	    		  }, function(err) {
	    			  AppUtil.myAlert(err.data.errorMessage);
	  	    		  $scope.listLoading = false;
	    		  });
	            
	    	}
	    	
	    	$scope.viewPullDenominations = function(denominations){
				 $scope.denomEntity = denominations;
				 $modal.open({
	                animation: true,
	                templateUrl: window.version+"scripts/modules/modals/viewDenominationModal.html" ,
	                controller: 'viewDenominationModalCtrl',
	                backdrop: 'static',
	                scope: $scope,
	                size: "lg"
	            });
	    	}
	    	
	    	$scope.viewClosure = function(closurePaymentDetail){
	    		$scope.closureEntity = closurePaymentDetail;
	    		$modal.open({
	                animation: true,
	                templateUrl: window.version+"scripts/modules/modals/viewClosureModal.html" ,
	                controller: 'viewClosureModalCtrl',
	                backdrop: 'static',
	                scope: $scope,
	                size: "lg"
	            });
	    	}
	}]);

angular.module('posApp').controller('viewDenominationModalCtrl', ['$scope', '$modalInstance','AppUtil','posAPI', '$filter',
	function ($scope, $modalInstance,AppUtil,posAPI, $filter) {
	$scope.init = function(){
		$scope.getTotal();
	};
	$scope.getTotal = function(){
		$scope.denomTotalAmount = 0;
		$scope.denomEntity.forEach(function(v){
			$scope.denomTotalAmount += v.totalAmount;
		});
	};
    $scope.ok = function() {
    	$modalInstance.close("cancel");
    };
    $scope.cancel = function() {
    	$modalInstance.dismiss("cancel");
    };
}]);

angular.module('posApp').controller('viewClosureModalCtrl', ['$scope', '$modalInstance','AppUtil','posAPI', '$filter',
	function ($scope, $modalInstance,AppUtil,posAPI, $filter) {
	$scope.init = function(){
	};
    $scope.ok = function() {
    	$modalInstance.close("cancel");
    };
    $scope.cancel = function() {
    	$modalInstance.dismiss("cancel");
    };
}]);