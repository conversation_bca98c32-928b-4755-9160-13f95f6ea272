/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';
/*jshint sub:true*/

angular.module('posApp').controller('SuggestGiftCardCtrl',
    ['$scope', '$http', 'APIJson', '$modalInstance', 'AppUtil', '$rootScope', 'posAPI', 'productService', 'giftObject', 'giftCard','subscriptionDetails','totalAmount','offerApplied','amountPayable','orderDetail','socketUtils','$timeout',
        function ($scope, $http, APIJson, $modalInstance,  AppUtil, $rootScope, posAPI, productService, giftObject, giftCard,subscriptionDetails,totalAmount,offerApplied,amountPayable,orderDetail,socketUtils,$timeout) {
            $scope.cardType = giftObject.type;
            $scope.customerPendingCardInfo = giftCard;
            $scope.cardAmount = null;
            $scope.prepaidAmount = amountPayable;
            $scope.amountPayable = amountPayable;
            $scope.totalAmount = totalAmount;
            $scope.subscriptionDetails = subscriptionDetails;
            $scope.offerApplied = offerApplied;
            $scope.totalWalletAmountShow = parseInt(0);
            $scope.totalWalletAmountShowOffer = parseInt(0);
            $scope.customerWalletAmountLeft = 0;
            $scope.orderWithoutWallet = orderDetail;
            $scope.isSubscriptionProduct = false;
            $rootScope.isSavingsScreenOpen = false;
            var giftCardId = [1000007, 1026, 1027, 1048];
            var giftCardNumberList = [];
            $scope.giftCardsList = [];
            $scope.notEditable = {};
            $scope.cardNumber = {};
            var card = {};
            $scope.cardList = [{"name": "E-Card", "code": "ECARD"}];
            $scope.voucherList = [0];
            $scope.vouherNumbers = {};
            $scope.vouherNumbers[1] = "";
            $scope.totalValue = 0;
            $scope.totalValueFlag = false;
            $scope.giftCardsArray = [2000, 1000, 500, 100];
            $scope.giftCardOfferArray = [0, 0, 0, 0];
            $scope.quantityAmountArray = [0, 0, 0, 0];
            $scope.giftCards = {};
            $scope.amountPresentFlag = [false, false, false, false];
            $scope.customerFinalPayableAmount=0;
            $scope.customerFinalExtraAmount=0;

            $scope.init = function () {
                $scope.backupCurrentState();
                getGiftCards();
                getCardOffer();
                console.log($scope.giftCardsArray);
                console.log($scope.amountPresentFlag);
                isSubProduct();
                $scope.suggestGiftCardAmount()
                // if(!(!AppUtil.customerSocket || AppUtil.customerSocket.name == null) && (subscriptionDetails === null || !subscriptionDetails.hasSubscription) && !$scope.isSubscriptionProduct && !$scope.offerApplied ){
                //     Savings()
                // }

            };

            function isSubProduct(){
                if($scope.orderWithoutWallet.length>1){
                    for(var i in $scope.orderWithoutWallet){
                        if($scope.orderWithoutWallet[i].productDetails.subType === 3810){
                            $scope.isSubscriptionProduct = true;
                            break;
                        }
                        if($scope.orderWithoutWallet[i].productDetails.subType === 904 && $scope.orderWithoutWallet[i].orderDetails.tax === 0){
                            $scope.totalAmount = $scope.totalAmount - $scope.orderWithoutWallet[i].orderDetails.amount;
                            $scope.amountPayable = $scope.amountPayable - $scope.orderWithoutWallet[i].orderDetails.amount;
                        }
                    }
                }
                else{
                    if($scope.orderWithoutWallet[0].productDetails.subType === 3810 || ($scope.orderWithoutWallet[0].productDetails.subType === 904 && $scope.orderWithoutWallet[0].orderDetails.tax === 0)){
                        $scope.isSubscriptionProduct = true;
                    }
                }

            }

            function Savings(){
                var couponCode = JSON.parse(localStorage.getItem("Subscription_Products"));
                var unitId =$rootScope.globals.currentUser.unitId;
                var offerManagementObj = {
                    unitId:unitId,
                    totalAmount:$scope.totalAmount,
                    paidAmount:$scope.amountPayable,
                    productId:[1000064],
                }
                var maxSavings = 0;
                var maxCoupon = 0;
                console.log("offerManagement - ",AppUtil.restUrls.offers.offerManagement);
                posAPI.allUrl('/',AppUtil.restUrls.offers.offerManagement).post(offerManagementObj).then(function (response) {
                    var data = response.plain();
                    if(!AppUtil.isEmptyObject(data) && !AppUtil.isEmptyObject(data.applicableDiscounts)){
                        couponCode.map(function(coupon){
                            if(!AppUtil.isEmptyObject(data.applicableDiscounts[coupon]) && maxSavings<data.applicableDiscounts[coupon].applicableDiscount.value ){
                                maxSavings = data.applicableDiscounts[coupon].applicableDiscount.value;
                                maxCoupon = coupon;
                            }
                        })
                        if(maxCoupon !== 0){
                            var dimension = data.applicableDiscounts[maxCoupon].dimension;
                            var duration = "months"
                            if(dimension.startsWith("3")){
                                duration = "3 "+duration;
                            }else if(dimension.startsWith("6")){
                                duration = "6 "+duration;
                            }else if(dimension.startsWith("9")){
                                duration = "9 "+duration;
                            }else if(dimension.startsWith("12")){
                                duration = "12 "+duration;
                            }
                            var maxOffer = {
                                savings: maxSavings,
                                percentage:data.applicableDiscounts[maxCoupon].applicableDiscount.percentage,
                                price:data.applicableDiscounts[maxCoupon].price,
                                productName:data.applicableDiscounts[maxCoupon].productName,
                                dimension:duration
                            }
                            var offerMessage = "Inform customer" + "<br>" + "<h2><b> You can save Rs. "+ maxOffer.savings + " now & much more later with "+ maxOffer.productName +
                                                " membership <br> Pay Rs. "+ maxOffer.price + " & get "+ maxOffer.percentage + " for "+ maxOffer.dimension +"</b></h2>"
                            bootbox.alert({
                                className:"alertBox",
                                closeButton: false,
                                backdrop:true,
                                message: offerMessage,
                                size: "small",
                            })
                            socketUtils.emitMessage({SUBSCRIPTION_SAVINGS: maxOffer});
                            $rootScope.isSavingsScreenOpen = true;

                        }
                    }
                });

            }


            function getCardOffer() {
                $scope.giftCardOfferInfo = AppUtil.giftCardOffers; // to get offer which are on cover page
                $scope.giftCardOfferInfo.forEach(function (data) {
                    $scope.giftCards[data.denomination] = (data.denomination * data.offer) / 100;
                    if (data.denomination == 2000) {
                        $scope.amountPresentFlag[0] = true;
                        $scope.giftCardOfferArray[0] = ((data.denomination * data.offer) / 100);
                    } else if (data.denomination == 1000) {
                        $scope.amountPresentFlag[1] = true;
                        $scope.giftCardOfferArray[1] = ((data.denomination * data.offer) / 100);
                    } else if (data.denomination == 500) {
                        $scope.amountPresentFlag[2] = true;
                        $scope.giftCardOfferArray[2] = ((data.denomination * data.offer) / 100);
                    } else if (data.denomination == 100) {
                        $scope.amountPresentFlag[3] = true;
                        $scope.giftCardOfferArray[3] = ((data.denomination * data.offer) / 100);
                    }
                });

            };


            $scope.getVoucherList = function () {
                return $scope.voucherList;
            };

            $scope.updateVoucher = function (action) {
                if (action == 'add') {
                    $scope.voucherList.push($scope.voucherList.length);
                } else if (action == 'remove') {
                    if ($scope.voucherList.length == 1) {
                        AppUtil.myAlert("One vouhcer required.");
                        return false;
                    }
                    $scope.voucherList.splice($scope.voucherList.length - 1, 1);
                }
            };

            function getGiftCards() {
                //if ($scope.cardType == 'ECARD') {
                AppUtil.setCardType('ECARD');
                //} else {
                //  AppUtil.setCardType('SELF');
                //}
                var list = productService.getProductArrayForSubTypeCode(904);
                console.log(list);
                for (var i = 0; i < list.length; i++) {
                    if (giftCardId.indexOf(list[i].id) > -1) {
                        $scope.giftCardsList.push(list[i]);
                    }
                }
                console.log($scope.giftCardsList);

            };

            $scope.changeCardType = function (cardType) {
                if (cardType == 'GYFTR') {
                    getGyftrPartnerDetail($scope.cardType);
                }
                $scope.cardType = cardType;
                AppUtil.setCardType(cardType)
            };

            $scope.close = function (confirmRequired) {
                AppUtil.closeByButton=true;
                AppUtil.walletPayment=false;
                $scope.clearOnGiftModalClose(closeModal, false);
                AppUtil.setCardType('GIFT');
            };

            function closeModal() {
                $modalInstance.close('cancel');
                $scope.$parent.disableOrderCheckBtnGift = false;
            }

            $rootScope.closeGiftModal = function () {
                $scope.clearOnGiftModalClose(closeModal, false);
                AppUtil.setCardType('GIFT');
            };


            $scope.updateGiftCardCode = function (orderItem, cardNumber) {
                cardNumber = cardNumber.replace(/[^a-zA-Z0-9]/g, "");
                card.cardNumber = cardNumber;
                if (card.cardNumber.length == 6) {
                    if (!$scope.isDuplicateCard(card)) {
                        var gCards = [];
                        var configData = AppUtil.getAutoConfigData();

                        card.itemId = orderItem.itemId;
                        card.buyerId = AppUtil.customerSocket.id;
                        card.empId = $rootScope.globals.currentUser.userId;
                        card.unitId = configData.unitId;
                        card.terminalId = configData.selectedTerminalId;
                        card.type = AppUtil.cardType;
                        card.isValid = false;
                        card.error = "";
                        card.productName = orderItem.productName;
                        card.cardValue = orderItem.price;

                        gCards.push(card);

                        posAPI.allUrl('/', AppUtil.restUrls.order.validateGiftCardsInOrder).post(gCards).then(function (response) {
                            if (response.errorType != undefined && response.errorType != '') {
                                var msg = /*(response.errorType != undefined) ? */"Error validating gift cards!" + response.errorMessage;
                                AppUtil.myAlert(msg);
                                return false;
                            }
                            var dataObj = response.plain();
                            if (dataObj != null) {
                                var valid = true;
                                dataObj.map(function (cardObj) {
                                    if (valid && !cardObj.isValid) {
                                        valid = false;
                                    }
                                });
                                if (!valid) {
                                    AppUtil.myAlert("Please fill the correct gift card codes.");
                                    $scope.notEditable[orderItem.itemId] = false;
                                    $scope.$parent.resetGiftCardCode(card);
                                } else {
                                    $scope.$parent.setGiftCardCode(card);
                                    $scope.notEditable[orderItem.itemId] = true;
                                    orderItem.isCardValid = true;
                                    giftCardNumberList.push(card);
                                    card = {};
                                }
                            } else {
                                var msg = (dataObj.errorType == undefined) ? "Error validating gift cards!" : dataObj.errorMessage;
                                AppUtil.myAlert(msg);
                                $rootScope.showFullScreenLoader = false;
                            }
                        }, function (err) {
                            AppUtil.myAlert(err.data.errorMessage);
                        });
                    } else {
                        AppUtil.myAlert("Card already added!");
                        card = {};
                    }
                }
            };


            $scope.deleteCardEntry = function (cardNumber, itemId) {
                for (var i = 0; i < giftCardNumberList.length; i++) {
                    if (giftCardNumberList[i].cardNumber == cardNumber) {
                        giftCardNumberList.splice(i, 1);
                    }
                }
                $scope.notEditable[itemId] = false;
                $scope.calculateWallet();
            };

            $scope.isDuplicateCard = function (card) {
                var found = false;
                for (var i = 0; i < giftCardNumberList.length; i++) {
                    if (giftCardNumberList[i].cardNumber.length > 0) {
                        if (giftCardNumberList[i].cardNumber == card.cardNumber) {
                            found = true;
                            break;
                        }
                    }
                }
                return found;
            };


            $scope.calculateWallet = function () {
                var total = 0;
                for (var i = 0; i < $scope.orderItemArray.length; i++) {
                    total += $scope.orderItemArray[i].orderDetails.amount;
                }
                $scope.totalValue = total;
                $scope.totalValueFlag = $scope.totalWalletAmountShow <= $scope.totalValue;
                //console.log(total);
                if ($scope.totalWalletAmountShow == 0 && $scope.orderItemArray.length == 0) {
                    $scope.totalValueFlag = false;
                }
                if ($scope.totalValueFlag) {
                    calculateCustomerAmountLeft();
                }else{
                    $scope.customerFinalPayableAmount=$scope.prepaidAmount;
                }
                //console.log($scope.customerWalletAmountLeft);
                //console.log($scope.totalValueFlag);
            };


            $scope.makePayment = function () {

                $scope.$parent.disableOrderCheckBtnGift = true;
                console.log("without wallet payment");
                AppUtil.setCardType('GIFT');
                AppUtil.walletPayment = false;
                closeModal();

            };


            $scope.purchaseGiftCardWallet = function () {
                $scope.$parent.disableOrderCheckBtnGift = true;
                console.log("with wallet payment");
                $scope.checkOrder(); //wall
                AppUtil.walletPayment = true;
            };

            $scope.suggestGiftCardAmount = function () { // extra Amount check here
                var customerCardAmount;
                if (!AppUtil.isEmptyObject($scope.customerPendingCardInfo) && $scope.customerPendingCardInfo.hasCard === false) {
                    customerCardAmount = parseInt(0);
                } else {
                    if(!AppUtil.isEmptyObject($scope.customerPendingCardInfo) && !AppUtil.isEmptyObject($scope.customerPendingCardInfo.cardAmount)){
                        customerCardAmount = parseInt($scope.customerPendingCardInfo.cardAmount);
                    }

                }
                var customerPrepaidAmount = parseInt($scope.prepaidAmount);
                if (customerCardAmount <= customerPrepaidAmount) {
                    var totalLeftAmount = Math.abs(customerPrepaidAmount - customerCardAmount);

                    for (var i = 0; i < 4; i++) {
                        if (totalLeftAmount >= $scope.giftCardsArray[i]) {
                            var quantity = (totalLeftAmount / ($scope.giftCardsArray[i]));
                            console.log(quantity);
                            quantity = parseInt(quantity, 10);
                            if (quantity > 0) {
                                totalLeftAmount = totalLeftAmount - (quantity * $scope.giftCardsArray[i] );
                            }
                            //console.log($scope.giftCardsArray[i]);
                            //console.log(quantity);
                            $scope.totalWalletAmountShow += $scope.giftCardsArray[i] * quantity;
                            $scope.totalWalletAmountShowOffer += $scope.giftCardOfferArray[i] * quantity;
                            $scope.quantityAmountArray[i] = $scope.quantityAmountArray[i] + quantity;
                            //console.log(totalLeftAmount);
                            //console.log($scope.totalWalletAmountShow);
                            //console.log($scope.totalWalletAmountShowOffer);
                        }
                    }
                    //case if amount left less then 100*
                    if (totalLeftAmount < 100 && totalLeftAmount > 0) {
                        totalLeftAmount = 0;
                        $scope.quantityAmountArray[3] = $scope.quantityAmountArray[3] + 1;
                        $scope.totalWalletAmountShow += $scope.giftCardsArray[3];
                        $scope.totalWalletAmountShowOffer += $scope.giftCardOfferArray[3];
                        // console.log("if amount is less then 100 add 100");
                        // console.log($scope.totalWalletAmountShow);
                        // console.log($scope.totalWalletAmountShowOffer);
                    }
                    // console.log($scope.quantityAmountArray);
                    // console.log($scope.totalWalletAmountShow);
                    // console.log($scope.totalWalletAmountShowOffer);
                    $scope.customerWalletAmountLeft = $scope.totalWalletAmountShow + $scope.totalWalletAmountShowOffer +
                        customerCardAmount - customerPrepaidAmount;
                    //console.log($scope.customerWalletAmountLeft);
                    renderAmount();

                }
            };

            function renderAmount() {
                $scope.giftCardsList.forEach(function (data) {
                    //console.log(data);
                    var numberOfGC;
                    console.log(data.prices[0].price);
                    if (data.prices[0].price == 2000) {
                        numberOfGC = $scope.quantityAmountArray[0];
                        for (var itr = 0; itr < numberOfGC; itr++) {
                            $scope.addNewProductToOrderItemArray(data);
                        }
                    } else if (data.prices[0].price == 1000) {
                        numberOfGC = $scope.quantityAmountArray[1];
                        for (var itr = 0; itr < numberOfGC; itr++) {
                            $scope.addNewProductToOrderItemArray(data);
                        }
                    } else if (data.prices[0].price == 500) {
                        numberOfGC = $scope.quantityAmountArray[2];
                        for (var itr = 0; itr < numberOfGC; itr++) {
                            $scope.addNewProductToOrderItemArray(data);
                        }
                    } else {
                        numberOfGC = $scope.quantityAmountArray[3];
                        for (var itr = 0; itr < numberOfGC; itr++) {
                            $scope.addNewProductToOrderItemArray(data);
                        }
                    }
                });
                $scope.calculateWallet();
            }

            function calculateCustomerAmountLeft() {
                $scope.customerWalletAmountLeft = 0;
                var customerCardAmount;
                if ($scope.customerPendingCardInfo.hasCard == false) {
                    customerCardAmount = parseInt(0);
                } else {
                    customerCardAmount = parseInt($scope.customerPendingCardInfo.cardAmount);
                }
                var customerPrepaidAmount = parseInt($scope.prepaidAmount);
                var total = 0, offer = 0;
                for (var i = 0; i < $scope.orderItemArray.length; i++) {
                    if ($scope.orderItemArray[i].orderDetails.amount == 2000) {
                        total += $scope.orderItemArray[i].orderDetails.amount;
                        offer += $scope.giftCardOfferArray[0];
                    } else if ($scope.orderItemArray[i].orderDetails.amount == 1000) {
                        total += $scope.orderItemArray[i].orderDetails.amount;
                        offer += $scope.giftCardOfferArray[1];
                    } else if ($scope.orderItemArray[i].orderDetails.amount == 500) {
                        total += $scope.orderItemArray[i].orderDetails.amount;
                        offer += $scope.giftCardOfferArray[2];
                    } else if ($scope.orderItemArray[i].orderDetails.amount == 100) {
                        total += $scope.orderItemArray[i].orderDetails.amount;
                        offer += $scope.giftCardOfferArray[3];
                    }

                }
                $scope.customerWalletAmountLeft = total + offer + customerCardAmount - customerPrepaidAmount;
                $scope.customerFinalPayableAmount=total;
                $scope.customerFinalExtraAmount=offer;
                //console.log($scope.customerWalletAmountLeft);
            }


        }]);