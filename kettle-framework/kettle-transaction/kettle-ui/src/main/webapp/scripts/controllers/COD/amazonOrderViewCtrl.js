/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

angular.module('posApp').controller('amazonOrderViewCtrl',
    ['$location', '$scope', 'posAPI', 'AppUtil', '$modal', 'PrintService', '$timeout', '$rootScope', 'trackingService', 'desiChaiService',
        function ($location, $scope, posAPI, AppUtil, $modal, PrintService, $timeout, $rootScope, trackingService, desiChaiService) {
            var channerPartnerId = 18;
            $scope.selectedUnit = "";
            $scope.selectedCity = "";
            $scope.customer = {};

            $scope.backToCODCover = function () {
                $location.url('/CODCover');
            };

            $scope.cityList = [];

            $scope.init = function () {
                AppUtil.activeChannelPartnerId = channerPartnerId;
                $rootScope.showFullScreenLoader = true;
                AppUtil.loadAmazonCustomer().then(function (d) {
                    //console.log("loading from api call"+d);
                    $rootScope.showFullScreenLoader = false;
                    $scope.customer = d;
                    AppUtil.CSObj = $scope.customer;
                });
                if (AppUtil.defaultDeliveryDetail === undefined) {
                    getDefaultDeliveryPartners();
                }
                $scope.brandList = AppUtil.transactionMetadata.brandList;
                $scope.selectedBrand = AppUtil.getSelectedBrand();
                $scope.getUnitCities();
                $scope.amazonMappedBrandPricingUnits = AppUtil.getAmazonMappedBrandPricingUnits();
            };

            $scope.unitPricingMapped = function(unitId) {
                return $scope.getAmazonMappedBrandPricingUnits.indexOf(unitId)>=0;
            };

            $scope.setSelectedBrand = function(brand) {
                $scope.selectedBrand = brand;
                AppUtil.setSelectedBrand(brand);
                $scope.amazonMappedBrandPricingUnits = AppUtil.getAmazonMappedBrandPricingUnits();
            };

            function getDefaultDeliveryPartners() {
                /***
                 * id:  patrnerId
                 * name :mappingType
                 * code : mappingValue(Default delivery Partner)
                 */
                posAPI.allUrl('/', AppUtil.restUrls.delivery.defaultDelivery)
                    .post('Default Delivery').then(function (response) {
                    AppUtil.defaultDeliveryDetail = response.plain();
                }, function (err) {
                    console.log('Error in getting response', err);
                });
            }

            $scope.getUnitCities = function () {
                posAPI.allUrl('/', AppUtil.restUrls.unitMetaData.unitCities)
                    .post().then(function (response) {
                    $scope.cityList = response.plain();
                    for (var i = 0; i < $scope.cityList.length; i++) {
                        if ($scope.cityList[i].id == 185) {
                            var temp = $scope.cityList[0];
                            $scope.cityList[0] = $scope.cityList[i];
                            $scope.cityList[i] = temp;
                            $scope.getUnitCityMapping($scope.cityList[0]);
                            break;
                        }
                    }
                }, function (err) {
                    console.log('Error in getting response', err);
                });
            };

            $scope.getUnitCityMapping = function (city) {
                $scope.selectedCity = city;
                $scope.selectedUnit = '';
                var locationId = city.id;
                $scope.unitList = null;
                posAPI.allUrl('/', AppUtil.restUrls.unitMetaData.unitCityMapping)
                    .post(locationId).then(function (response) {
                    $scope.unitList = response.plain();
                }, function (err) {
                    console.log('Error in getting response', err);
                });
            };

            $scope.selectUnit = function (unit) {
                $scope.selectedUnit = unit;
                initOutletObject($scope.selectedUnit);
                $scope.startOrder();
            };

            $scope.startOrder = function () {
                updateCustomerAndGetTaxProfile();
            };

            function updateCustomerAndGetTaxProfile() {
                var partnerId = 18;
                AppUtil.setPartnerId(partnerId);
                AppUtil.getTaxProfile();
                AppUtil.getPackagingProfile();
                AppUtil.getDeliveryProfile();
                AppUtil.getBrandUnitDeliveryProductProfile($scope.selectedBrand.brandId, $scope.selectedUnit.id, partnerId, function () {
                    desiChaiService.setRecipes();
                });
                /*AppUtil.getProductProfileByCity($scope.selectedCity.code, function () {
                    desiChaiService.setRecipes();
                });*/
            }

            function initOutletObject(selectedUnit) {
                AppUtil.outlet = {
                    pri_unitId: selectedUnit.id,
                    pri_name: selectedUnit.name,
                    sec_unitId: selectedUnit.id,
                    sec_name: selectedUnit.name,
                    ter_unitId: null,
                    ter_name: 'Tertiary Outlet',
                    selectedId: 1
                };
            }

        }]);