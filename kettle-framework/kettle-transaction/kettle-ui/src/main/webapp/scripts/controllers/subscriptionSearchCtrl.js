/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

angular.module('posApp')
    .controller('subscriptionSearchCtrl', ['$rootScope', '$scope', 'AppUtil', '$location', '$http',
        'posAPI', '$modal', 'subscriptionService',
        function ($rootScope, $scope, AppUtil, $location, $http, posAPI, $modal, subscriptionService) {

            $scope.init = function () {
                AppUtil.validate();
                $scope.backToCover = AppUtil.backToCover;
                $scope.transactionMetadata = AppUtil.getTransactionMetadata();
                $scope.unitDetails = AppUtil.getUnitDetails();
                $scope.convertTimeFromIndex = subscriptionService.convertTimeFromIndex;
                $scope.convertWeekDayFromIndex = subscriptionService.convertWeekDayFromIndex;
                $scope.showStatusEventDetails = false;
                $scope.showStatusEventLoader = false;
                $scope.dateGTE = subscriptionService.dateGTE;
                $scope.dateGT = subscriptionService.dateGT;
                $scope.getDateFromTimeStamp = subscriptionService.getDateFromTimeStamp;
                if (AppUtil.previousLocation[AppUtil.previousLocation.length - 2] == "/orderSearch"
                    || AppUtil.previousLocation[AppUtil.previousLocation.length - 2] == "/subscriptionOrderSearch") {
                    $scope.subscriptionList = subscriptionService.subscriptionList;
                    $scope.subscriptionInput = subscriptionService.customerContact;
                    $scope.CSObj = AppUtil.CSObj;
                    $scope.addresses = AppUtil.CSObj.addresses;
                } else {
                    $scope.subscriptionList = null;
                    $scope.subscriptionInput = null;
                }
            }

            $scope.getSubscriptions = function () {
                if ($scope.subscriptionInput.length == 10) {
                    $scope.fetchCSObj();
                    var requestObj = AppUtil.GetRequest($scope.subscriptionInput);
                    posAPI.allUrl('/',AppUtil.restUrls.subscription.getCustomerSubscriptions)
                        .post(requestObj).then(function (response) {
                        if (response) {
                            $scope.subscriptionList = response.plain();
                            subscriptionService.subscriptionList = $scope.subscriptionList;
                            subscriptionService.customerContact = $scope.subscriptionInput;
                        }
                    }, function (err) {
                        AppUtil.myAlert(err.data.errorMessage);
                    });
                } else {
                    AppUtil.myAlert('Contact Number should be of 10 digits!');
                }
            }

            $scope.fetchCSObj = function () {
                var reqObj = {
                    session: $rootScope.globals.currentUser,
                    contactNumber: null,
                    customer: AppUtil.CSObj,
                    newAddress: null,
                    newCustomer: false
                };
                reqObj.contactNumber = $scope.subscriptionInput;
                reqObj.customer.contactNumber = $scope.subscriptionInput;
                reqObj.customer.registrationUnitId = $rootScope.globals.currentUser.unitId;
                reqObj.customer.acquisitionToken = $rootScope.globals.currentUser.userId;
                $scope.addresses = [];
                AppUtil.CSObj = {};
                posAPI.allUrl('/',AppUtil.restUrls.codCustomer.lookup).post(reqObj)
                    .then(function (response) {
                        //console.log(response.plain());
                        //console.log(response);
                        AppUtil.CSObj = response.customer;
                        $scope.CSObj = response.customer;
                        $scope.addresses = AppUtil.CSObj.addresses;
                        //console.log($scope.addresses);
                    }, function (err) {
                        AppUtil.myAlert(err.data.errorMessage);
                    });
            }

            $scope.openOrderSearch = function (subscriptionObj) {
                AppUtil.OrderObj = $rootScope.OrderObj = subscriptionObj;
                $rootScope.orderType = 'subscription';
                if (AppUtil.OrderObj.deliveryAddress != null) {
                    AppUtil.GetCustomerInfo(AppUtil.OrderObj.deliveryAddress);
                    AppUtil.getDeliveryDetail(AppUtil.OrderObj.generateOrderId);
                } else if (!AppUtil.isEmptyObject($rootScope.CustomerObj)) {
                    $rootScope.CustomerObj = {};
                }
                $location.url('/orderSearch');
            };

            $scope.setSelectedAddress = function (addressId) {
                $scope.CSObj.addresses.forEach(function (address) {
                    if (address.addressId === addressId) {
                        address.isSelectedAddress = true;
                    } else {
                        address.isSelectedAddress = false;
                    }
                });
            }

            $scope.editSubscription = function (orderObj) {
                $scope.selectedSubscription = orderObj;
                $scope.setSelectedAddress(orderObj.deliveryAddress);
                var template = window.version+"scripts/modules/modals/editSubscriptionModal.html";
                var size = "lg";
                $modal.open({
                    animation: true,
                    templateUrl: window.version+template,
                    controller: 'editSubscriptionModalCtrl',
                    backdrop: 'static',
                    scope: $scope,
                    size: size
                });
            };

            $scope.closeSubscription = function (orderObj) {
                $scope.actionType = 'CANCELLED';
                $scope.holdEndModalOpen(orderObj);
            }

            $scope.holdSubscription = function (orderObj) {
                $scope.actionType = 'ON_HOLD';
                $scope.holdEndModalOpen(orderObj);
            }

            $scope.holdEndModalOpen = function (orderObj) {
                $scope.selectedSubscription = orderObj;
                $modal.open({
                    animation: true,
                    templateUrl: window.version+"scripts/modules/modals/holdEndSubscriptionModal.html",
                    controller: 'holdEndSubscriptionModalCtrl',
                    backdrop: 'static',
                    scope: $scope,
                    size: "lg"
                });
            };

            $scope.showOrdersForSubscription = function () {
                var requestObj = AppUtil.GetRequest($scope.subscriptionInput);
                posAPI.allUrl('/',AppUtil.restUrls.subscription.getCustomerSubscriptionOrders)
                    .post(requestObj).then(function (response) {
                    if (response) {
                        subscriptionService.orderList = response.plain();
                        subscriptionService.customerContact = $scope.subscriptionInput;
                        $location.path("/subscriptionOrderSearch");
                    }
                }, function (err) {
                    AppUtil.myAlert(err.data.errorMessage);
                });
            }

            $scope.showStatusEventsForSubscription = function (subscription) {
                $scope.selectedSubscription = subscription;
                $scope.showStatusEventDetails = true;
                $scope.showStatusEventLoader = true;
                $scope.loadSubscriptionStatusEvents();
            }

            $scope.loadSubscriptionStatusEvents = function () {
                var reqObj = $scope.selectedSubscription.subscriptionDetail.subscriptionId;
                var requestObj = AppUtil.GetRequest(reqObj);
                posAPI.allUrl('/',AppUtil.restUrls.subscription.getSubscriptionStatusEvents)
                    .post(requestObj).then(function (response) {
                    if (response) {
                        //console.log(response.plain());
                        $scope.statusEventList = response.plain();
                        $scope.showStatusEventLoader = false;
                    }
                }, function (err) {
                    AppUtil.myAlert(err.data.errorMessage);
                });
            }

            $scope.backToSubscriptions = function () {
                $scope.selectedSubscription = null;
                $scope.showStatusEventDetails = false;
                $scope.showStatusEventLoader = true;
                $scope.statusEventList = null;
            }

            $scope.statusEventEditAction = function (statusEvent) {
                $scope.selectedStatusEvent = statusEvent;
                var modalInstance = $modal.open({
                    animation: true,
                    templateUrl: window.version+"scripts/modules/modals/statusEventActionsModal.html",
                    controller: 'statusEventActionsModalCtrl',
                    backdrop: 'static',
                    scope: $scope,
                    size: "lg"
                });
                modalInstance.result.then(function () {
                    $scope.loadSubscriptionStatusEvents();
                }, function () {
                });
            }

            $scope.statusEventCancelAction = function (statusEvent) {
                $scope.selectedStatusEvent = statusEvent;
                var reqObj = {
                    subscriptionEventStatusId: $scope.selectedStatusEvent.subscriptionEventId
                }
                var requestObj = AppUtil.GetRequest(reqObj);
                posAPI.allUrl('/',AppUtil.restUrls.subscription.cancelSubscriptionHoldEvent)
                    .post(requestObj).then(function (response) {
                    if (response == true) {
                        $scope.loadSubscriptionStatusEvents();
                        AppUtil.mySuccessAlert("Hold with hold event id: " + $scope.selectedStatusEvent.subscriptionEventId + " cancelled successfully!");
                    }
                }, function (err) {
                    AppUtil.myAlert(err.data.errorMessage);
                });
            }

            $scope.subscriptionExpired = function (subscription) {
                return !$scope.dateGTE(subscription.subscriptionDetail.endDate);
            }

        }]);


angular.module('posApp').controller('editSubscriptionModalCtrl', function ($scope, $modalInstance, subscriptionService, AppUtil, posAPI) {
    $scope.init = function () {
        $scope.weekDays = subscriptionService.weekDays;
        $scope.monthDays = subscriptionService.monthDays;
        $scope.frequencyTimes = subscriptionService.frequencyTimes;
        $scope.subscriptionFrequencyType = $scope.selectedSubscription.subscriptionDetail.type;
        $scope.setFrequencyDays();
        if ($scope.subscriptionFrequencyType == "WEEKLY") {
            $scope.subscriptionWeekDays = subscriptionService.getSubscriptionDaysUsingIndex($scope.selectedSubscription.subscriptionDetail.daysOfTheWeek,
                $scope.subscriptionFrequencyType);
            $scope.subscriptionMonthDays = [];
        } else if ($scope.subscriptionFrequencyType == "MONTHLY") {
            $scope.subscriptionMonthDays = subscriptionService.getSubscriptionDaysUsingIndex($scope.selectedSubscription.subscriptionDetail.daysOfTheMonth,
                $scope.subscriptionFrequencyType);
            $scope.subscriptionWeekDays = [];
        }
        $scope.subscriptionTimes = subscriptionService.getSubscriptionTimesUsingIndex($scope.selectedSubscription.subscriptionDetail.timeOfTheDay);
        $scope.frequencyTime = $scope.frequencyTimes[0];
        $scope.subscriptionStartDate = subscriptionService.getDateFromTimeStamp($scope.selectedSubscription.subscriptionDetail.startDate);
        $scope.subscriptionEndDate = subscriptionService.getDateFromTimeStamp($scope.selectedSubscription.subscriptionDetail.endDate);
        $scope.subscriptionMinDate = subscriptionService.subscriptionMinDate;
        $scope.emailNotification = $scope.selectedSubscription.subscriptionDetail.emailNotification;
        $scope.smsNotification = $scope.selectedSubscription.subscriptionDetail.smsNotification;
        $scope.automatedDelivery = $scope.selectedSubscription.subscriptionDetail.automatedDelivery;
    }

    $scope.setFrequencyDays = function () {
        if ($scope.subscriptionFrequencyType == 'WEEKLY') {
            $scope.frequencyDays = $scope.weekDays;
        } else if ($scope.subscriptionFrequencyType == 'MONTHLY') {
            $scope.frequencyDays = $scope.monthDays();
        }
        $scope.frequencyDay = $scope.frequencyDays[0];
        $scope.frequencyTime = $scope.frequencyTimes[0];
    }

    $scope.getSubscriptionFrequency = function (frequencyType) {
        $scope.subscriptionDays = [];
        $scope.subscriptionFrequencyType = frequencyType;
        $scope.setFrequencyDays();
    }

    $scope.addSubscriptionDay = function (index) {
        if ($scope.subscriptionDays.indexOf(index) > -1) {
            $scope.subscriptionDays.splice($scope.subscriptionDays.indexOf(index), 1);
        } else {
            $scope.subscriptionDays.push(index);
        }
    }

    $scope.addFrequencyDay = function (frequencyDay) {
        var contains = false;
        if ($scope.subscriptionFrequencyType == "WEEKLY") {
            $scope.subscriptionWeekDays.forEach(function (v) {
                if (v.id === frequencyDay.id) {
                    contains = true;
                }
            });
            if (!contains) {
                $scope.subscriptionWeekDays.push(frequencyDay);
            }
        } else if ($scope.subscriptionFrequencyType == "MONTHLY") {
            $scope.subscriptionMonthDays.forEach(function (v) {
                if (v.id === frequencyDay.id) {
                    contains = true;
                }
            });
            if (!contains) {
                $scope.subscriptionMonthDays.push(frequencyDay);
            }
        }

    }

    $scope.removeSubscriptionDay = function (frequencyDay) {
        if ($scope.subscriptionFrequencyType == "WEEKLY") {
            $scope.allDays = false;
            $scope.subscriptionWeekDays.splice($scope.subscriptionWeekDays.indexOf(frequencyDay), 1);
        } else if ($scope.subscriptionFrequencyType == "MONTHLY") {
            $scope.subscriptionMonthDays.splice($scope.subscriptionMonthDays.indexOf(frequencyDay), 1);
        }
    }

    $scope.addFrequencyTime = function (frequencyTime) {
        if ($scope.subscriptionTimes.indexOf(frequencyTime) == -1) {
            $scope.subscriptionTimes.push(frequencyTime);
        }
    }

    $scope.removeSubscriptionTime = function (frequencyTime) {
        $scope.subscriptionTimes.splice($scope.subscriptionTimes.indexOf(frequencyTime), 1);
    }

    $scope.setSubscriptionStartDate = function (subscriptionStartDate) {
        $scope.subscriptionStartDate = subscriptionStartDate;
    }

    $scope.setSubscriptionEndDate = function (subscriptionEndDate) {
        $scope.subscriptionEndDate = subscriptionEndDate;
    }

    $scope.addAllDays = function (allDays) {
        if (allDays) {
            //console.log(allDays);
            $scope.subscriptionWeekDays = angular.copy($scope.frequencyDays);
        } else {
            $scope.subscriptionWeekDays = [];
        }
    }

    $scope.notifyEmail = function (emailNotification) {
        $scope.emailNotification = emailNotification;
    }

    $scope.notifySMS = function (smsNotification) {
        $scope.smsNotification = smsNotification;
    }

    $scope.automateDelivery = function (automatedDelivery) {
        $scope.automatedDelivery = automatedDelivery;
    }

    $scope.ok = function () {
        if ($scope.subscriptionStartDate == null) {
            alert("Please select start date.");
            return false;
        }
        if ($scope.subscriptionTimes.length == 0) {
            alert("Please select subscription time.");
            return false;
        }
        if (($scope.subscriptionFrequencyType == 'WEEKLY' && $scope.subscriptionWeekDays.length == 0)
            || ($scope.subscriptionFrequencyType == 'MONTHLY' && $scope.subscriptionMonthDays.length == 0)) {
            alert("Please select subscription days.");
            return false;
        }
        var reqObj = {
            subscriptionId: $scope.selectedSubscription.subscriptionDetail.subscriptionId,
            subscriptionStatus: $scope.selectedSubscription.subscriptionDetail.subscriptionStatus,
            startDate: $scope.subscriptionStartDate + " 00:00:00",
            endDate: $scope.subscriptionEndDate == null ? null : $scope.subscriptionEndDate + " 00:00:00",
            type: $scope.subscriptionFrequencyType,
            emailNotification: $scope.emailNotification,
            smsNotification: $scope.smsNotification,
            automatedDelivery: $scope.automatedDelivery,
            daysOfTheMonth: $scope.subscriptionFrequencyType == 'WEEKLY' ?
                null : subscriptionService.processSubscriptionDays($scope.subscriptionMonthDays),
            daysOfTheWeek: $scope.subscriptionFrequencyType == 'MONTHLY' ?
                null : subscriptionService.processSubscriptionDays($scope.subscriptionWeekDays),
            timeOfTheDay: subscriptionService.processSubscriptionTimes($scope.subscriptionTimes)
        }
        var requestObj = AppUtil.GetRequest(reqObj);
        //console.log(requestObj);
        posAPI.allUrl('/',AppUtil.restUrls.subscription.editSubscription)
            .post(requestObj).then(function (response) {
            if (response != null) {
                $scope.selectedSubscription.subscriptionDetail = response.plain();
                AppUtil.mySuccessAlert("Subscription with subscription id: "
                    + $scope.selectedSubscription.subscriptionDetail.subscriptionId + " edited sucessfully!");
            }
            $modalInstance.close();
        }, function (err) {
            AppUtil.myAlert(err.data.errorMessage);
            $modalInstance.close();
        });
    };
    $scope.cancel = function () {
        $modalInstance.dismiss("cancel");
    };
});

angular.module('posApp').controller('holdEndSubscriptionModalCtrl', function ($scope, $modalInstance, subscriptionService, AppUtil, posAPI,$rootScope) {
    $scope.init = function () {
        $scope.startDate = null;
        $scope.endDate = null;
        $scope.subscriptionMinDate = subscriptionService.subscriptionMinDate;
        $scope.actionReason = null;
        $scope.withImmediateEffect = false;
        $scope.getCurrentDate = subscriptionService.getCurrentDate;
        $scope.getPreviousDate = subscriptionService.getPreviousDate;
    }
    $scope.ok = function () {
        if ($scope.actionType == 'ON_HOLD') {
            if ($scope.startDate == null || $scope.endDate == null) {
                alert("Start date and end date cannot be null.");
                return false;
            } else {
                $scope.executeHoldAction();
            }
        } else if ($scope.actionType == 'CANCELLED') {
            if ($scope.startDate == null) {
                alert("Start date cannot be null.");
                return false;
            } else {
                $scope.executeCloseAction();
            }
        }
    };
    $scope.cancel = function () {
        $modalInstance.dismiss("cancel");
    };
    $scope.executeHoldAction = function () {
        var reqObj = {
            subscriptionId: $scope.selectedSubscription.subscriptionDetail.subscriptionId,
            status: $scope.actionType,
            reasonText: $scope.actionReason,
            changedBy: $rootScope.globals.currentUser.userId,
            startDate: $scope.startDate + " 00:00:00",
            endDate: $scope.endDate + " 00:00:00",
            changeWithImmediateEffect: $scope.withImmediateEffect
        }
        var requestObj = AppUtil.GetRequest(reqObj);
        posAPI.allUrl('/',AppUtil.restUrls.subscription.createSubscriptionHoldEvent)
            .post(requestObj).then(function (response) {
            if (response == true) {
                if ($scope.withImmediateEffect == true) {
                    $scope.selectedSubscription.subscriptionDetail.subscriptionStatus = $scope.actionType;
                }
                AppUtil.mySuccessAlert("Hold event added successfully to subscription with subscription id: "
                    + $scope.selectedSubscription.subscriptionDetail.subscriptionId);
            }
            $modalInstance.close();
        }, function (err) {
            AppUtil.myAlert(err.data.errorMessage);
            $modalInstance.close();
        });
    }
    $scope.executeCloseAction = function () {
        if ($scope.startDate == null) {
            alert("Start date cannot be null.");
            return false;
        }
        var reqObj = {
            subscriptionId: $scope.selectedSubscription.subscriptionDetail.subscriptionId,
            status: $scope.actionType,
            reasonText: $scope.actionReason,
            changedBy: $rootScope.globals.currentUser.userId,
            startDate: $scope.startDate + " 00:00:00",
            changeWithImmediateEffect: $scope.withImmediateEffect
        }
        var requestObj = AppUtil.GetRequest(reqObj);
        posAPI.allUrl('/',AppUtil.restUrls.subscription.cancelSubscription)
            .post(requestObj).then(function (response) {
            if (response == true) {
                if ($scope.withImmediateEffect == true) {
                    $scope.selectedSubscription.subscriptionDetail.subscriptionStatus = $scope.actionType;
                }
                AppUtil.mySuccessAlert("Close event added successfully to subscription with subscription id: "
                    + $scope.selectedSubscription.subscriptionDetail.subscriptionId);
            }
            $modalInstance.close();
        }, function (err) {
            AppUtil.myAlert(err.data.errorMessage);
            $modalInstance.close();
        });
    }
    $scope.setStartDate = function (startDate) {
        $scope.startDate = startDate;
    }

    $scope.setEndDate = function (endDate) {
        $scope.endDate = endDate;
    }
    $scope.updateStartDate = function () {
        $scope.startDate = subscriptionService.getCurrentDateFormatted("yyyy-mm-dd");
    }
});

angular.module('posApp').controller('statusEventActionsModalCtrl', function ($scope, $modalInstance, subscriptionService, AppUtil, posAPI) {
    $scope.init = function () {
        $scope.startDate = subscriptionService.getDateFromTimeStamp($scope.selectedStatusEvent.eventStartDate);
        $scope.endDate = subscriptionService.getDateFromTimeStamp($scope.selectedStatusEvent.eventEndDate);
        $scope.subscriptionMinDate = subscriptionService.subscriptionMinDate;
        $scope.withImmediateEffect = false;
        $scope.actionReason = $scope.selectedStatusEvent.reasonText;
        $scope.getCurrentDate = subscriptionService.getCurrentDate;
        $scope.getPreviousDate = subscriptionService.getPreviousDate;
    }
    $scope.ok = function () {
        if (subscriptionService.dateGTE($scope.selectedStatusEvent.eventStartDate)) {
            //console.log($scope.startDate, $scope.endDate);
            if ($scope.startDate == null || $scope.endDate == null) {
                alert("Start date and end date cannot be null.");
                return false;
            }
        } else {
            if ($scope.endDate == null) {
                alert("End date cannot be null.");
                return false;
            }
        }
        if ($scope.actionReason == null) {
            alert("Please provide reason.");
            return false;
        }
        $scope.editStatusEvent();
    };
    $scope.cancel = function () {
        $modalInstance.dismiss("cancel");
    };
    $scope.updateStartDate = function () {
        $scope.startDate = subscriptionService.getCurrentDateFormatted("yyyy-mm-dd");
    }
    $scope.setStartDate = function (startDate) {
        $scope.startDate = startDate;
    }
    $scope.setEndDate = function (endDate) {
        $scope.endDate = endDate;
    }
    $scope.editStatusEvent = function () {
        if ($scope.selectedStatusEvent.eventType == 'ON_HOLD') {
            $scope.editHoldEvent();
        } else if ($scope.selectedStatusEvent.eventType == 'CANCELLED') {
            $scope.editCancelEvent();
        }
    }

    $scope.editHoldEvent = function () {
        var reqObj;
        var isFutureStartDate = subscriptionService.dateGTE($scope.selectedStatusEvent.eventStartDate);
        reqObj = {
            subscriptionEventStatusId: $scope.selectedStatusEvent.subscriptionEventId,
            startDate: isFutureStartDate ? $scope.startDate + " 00:00:00"
                : subscriptionService.getDateFromTimeStamp($scope.selectedStatusEvent.eventStartDate) + " 00:00:00",
            endDate: $scope.endDate + " 00:00:00",
            changeWithImmediateEffect: $scope.withImmediateEffect,
            reason: $scope.actionReason
        }
        var requestObj = AppUtil.GetRequest(reqObj);
        posAPI.allUrl('/',AppUtil.restUrls.subscription.updateSubscriptionHoldEvent)
            .post(requestObj).then(function (response) {
            if (response == true) {
                AppUtil.mySuccessAlert("Hold with hold event id: " + $scope.selectedStatusEvent.subscriptionEventId + " edited successfully!");
            }
            $modalInstance.close();
        }, function (err) {
            AppUtil.myAlert(err.data.errorMessage);
            $modalInstance.close();
        });
    }

    $scope.editCancelEvent = function () {
        var reqObj;
        var isFutureStartDate = subscriptionService.dateGTE($scope.selectedStatusEvent.eventStartDate);
        reqObj = {
            subscriptionEventStatusId: $scope.selectedStatusEvent.subscriptionEventId,
            startDate: $scope.startDate + " 00:00:00",
            reason: $scope.actionReason
        }
        var requestObj = AppUtil.GetRequest(reqObj);
        posAPI.allUrl('/',AppUtil.restUrls.subscription.updateSubscriptionCancelEvent)
            .post(requestObj).then(function (response) {
            if (response == true) {
                AppUtil.mySuccessAlert("Cancel with cancel event id: " + $scope.selectedStatusEvent.subscriptionEventId + " edited successfully!");
            }
            $modalInstance.close();
        }, function (err) {
            AppUtil.myAlert(err.data.errorMessage);
            $modalInstance.close();
        });
    }
});