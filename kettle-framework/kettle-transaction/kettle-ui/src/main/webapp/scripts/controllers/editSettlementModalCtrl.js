/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

angular
	.module('posApp')
	.controller(
		'editSettlementModalCtrl',
		['$scope', '$modalInstance', '$rootScope', 'AppUtil', 'posAPI', '$location', 'orderUnitId', 'orderDataId', 'settlementData', 'orderSource',
			function($scope, $modalInstance,$rootScope, AppUtil, posAPI, $location, orderUnitId, orderDataId, settlementData,orderSource) {
		    $scope.settlement = settlementData;
		    $scope.unitId = orderUnitId;
		    $scope.orderId = orderDataId;
		    $scope.paymentModes = null;
		    $scope.selectedMode = null;
		    $scope.transactionMetadata = AppUtil.getTransactionMetadata();
		    $scope.init = function(){
			$scope.paymentModes =  getPaymentModesForEdit($scope.settlement.mode);
		    }
		    $scope.setPaymentMode = function() {
			var request = {
			    unitId : $scope.unitId,
			    orderId : $scope.orderId,
			    editedBy : $rootScope.globals.currentUser.userId,
				orderSource:orderSource,
			    details : [{
				orderSettlementId : $scope.settlement.settlementId,
				paymentModeId : $scope.selectedMode.id
			    }]
			};
			posAPI.allUrl('/',AppUtil.restUrls.order.editSettlement)
				.post(request)
				.then(
						function (response) {
							var result = response == 'true' ? false : true;
							if(result){
								bootbox.alert("Payment Mode Update Successfully");
							}else{
								bootbox.alert("Error in updating Payment Mode");
							}
							$scope.goBack();
						}, function (err) {
							console.log(err);
						}
				);
		    };
		    
		    function getPaymentModesForEdit(modeId){
		            var modes = [];
		            if($scope.transactionMetadata != undefined && $scope.transactionMetadata != null){
		        	for(var i in $scope.transactionMetadata.paymentModes){
		        	    if(!$scope.transactionMetadata.paymentModes[i].editable || $scope.transactionMetadata.paymentModes[i].id == modeId){
		        		continue;
		        	    }
		        	    modes.push($scope.transactionMetadata.paymentModes[i]);
		        	}
		            }
		            return modes;
		        };

		    $scope.goBack = function() {
			$modalInstance.close();
		    };
		}]);
