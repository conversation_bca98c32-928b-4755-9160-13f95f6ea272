/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

angular
.module('posApp')
.controller('openOrderSummaryCtrl', ['$rootScope', '$location', '$scope', 'AuthenticationService', '$modal', 'AppUtil', 'posAPI', 'AssemblyService',
	function ($rootScope, $location, $scope, AuthenticationService, $modal, AppUtil, posAPI,AssemblyService) {

	$scope.openOrderData = {};
	$scope.isOpen = true;
	$scope.isSecOpen = true;

	$scope.openOrderSearch = function (generateOrderId) {
		AppUtil.openOrderSearch(generateOrderId,"order");
	};

	$scope.backToCover = function () {
		$scope.clearSockets();
		$location.url("/CODCover");
	};

	$scope.refresh = function(){
		fetchOpenOrders();
	};


	$scope.getSettlements = function(orderObj){
		return AppUtil.getSettlements(orderObj);
	};

	//code for initializing socket
	$scope.initSocket = function () {
		$scope.socket = new SockJS("/kettle-service/rest/ws");
		$scope.stompClient = Stomp.over($scope.socket);
		$scope.stompClient.connect("", "",
				function (frame) {
			//console.log("user name of the socket :::::: " + frame);
			$scope.stompClient.subscribe('/order-queue/orders',
					$scope.updateOrders);
		}, function (err) {
			$scope.stompClient.subscribe('/order-queue/orders',
					$scope.updateOrders);
		}
		);
	};

	$scope.updateOrders = function(serverMessage){
		var orderInfo = JSON.parse(serverMessage.body);
		for(var index in $scope.openOrderData){
			var orderStatus = $scope.openOrderData[index];
			for(var orderIndex in orderStatus){
				var order = orderStatus[orderIndex];
				if(!AppUtil.isEmptyObject(order) && !AppUtil.isEmptyObject(order.order)){
					if(orderInfo.generatedOrderId == order.order.generateOrderId){
						//console.log("inside if clause of traversal");
						//console.log(order);
						$scope.$apply(function () {
							order.deliveryPartner.name = orderInfo.partnerId;
							order.deliveryDetails.deliveryStatus = orderInfo.deliveryStatus;
						});
					}
				}
			}
		}
	};


	$scope.clearSockets = function () {
		if ($scope.socket != undefined) {
			$scope.socket.close();
			if ($scope.stompClient != undefined) {
				$scope.stompClient.disconnect(function () {
					//console.log('connection closed');
				});
			}
		}
	};
	
	$scope.getPartnerClass = function(channelPartner){
		var style="text-primary";
		if(channelPartner=="ZOMATO"){
			style="text-danger";
		}else if(channelPartner=="SWIGGY"){
			style="text-warning";
		}else if(channelPartner=="FOODPANDA"){
			style="text-info";
		}else if(channelPartner=="Chaayos (Delivery)"){
			style="text-success";
		}
		return style;
	};

	$scope.assignManualTicket = function(orderId, generatedOrderId, unitId){

		var openModal = function(deliveryPartners){
			if(deliveryPartners != null){
				var modalInstance = $modal.open({
					animation: true,
					templateUrl: window.version+'scripts/modules/modals/manualDeliveryModal.html',
					controller: 'manualDeliveryModalCtrl',
					backdrop: 'static',
					scope: $scope,
					size: 'lg',
					resolve: {
						deliveryPartners: function() {
							return deliveryPartners;
						}
					}
				});
				modalInstance.result.then(function (retObj) {
					retObj['orderId'] = orderId;
					retObj['generatedOrderId'] = generatedOrderId;
					retObj['unitId'] = unitId;
					//console.log(retObj);
					var reqObj = AppUtil.GetRequest(retObj);
					posAPI.allUrl('/',AppUtil.restUrls.delivery.manualDelivery).post(reqObj)
					.then(function (response) {
						fetchOpenOrders();
					},function(err){

					});
				}, function () {
					//console.log("inside dismiss script");
				});
			}else{
				alert("Couldn't fetch delivery partners for this unit");
			}
		};

		AppUtil.getDeliveryProfileForUnit(unitId, openModal); //get delivery partners for unit and then open modal
	};

	$scope.loadSubscriptionDetails = function(order){
		AppUtil.openOrderSearch(order.generateOrderId,"subscription");
	}

	$scope.showDetails = function (order) {
		var modalInstance = $modal.open({
			animation: true,
			templateUrl: window.version+'views/orderTransitionDetail.html',
			controller: 'orderDetailCtrl',
			backdrop: 'static',
			resolve: {
				orderInfo: function () {
					return order;
				}
			}
		});
	};



	function fetchOpenOrders() {
		AppUtil.startSpin('spinner-1');
		$rootScope.showFullScreenLoader = true;
		posAPI.allUrl('/',AppUtil.restUrls.order.codOrders).post($rootScope.globals.currentUser)
		.then(function (response) {
			$scope.openOrderData = getOrders(response.plain());
			console.log("$scope.openOrderData",$scope.openOrderData);
			AppUtil.stopSpin('spinner-1');
			$rootScope.showFullScreenLoader = false;
		}, function (err) {
			AppUtil.myAlert(err.data.errorMessage);
			$rootScope.showFullScreenLoader = false;
		});
	}

	function getOrders(responseObj) {
		var returnObj = {};
		var statusArr=["CREATED","PROCESSING","READY_TO_DISPATCH","CANCELLED","SETTLED"];
		for(var i=0;i<statusArr.length;i++){
			returnObj[statusArr[i]]=[];
		}
		for (var unit in responseObj) {
			for (var orderId in responseObj[unit]) {
				var orderInfo = responseObj[unit][orderId];
				if(orderInfo.order.source == "COD"){
					var orderStatus = orderInfo.order.status;
					orderInfo.brand = AppUtil.getBrandByBrandId(orderInfo.order.brandId);
					orderInfo['unitName'] = unit;
					orderInfo['elapsedTime'] = ((Date.now() - new Date(orderInfo.order.billCreationTime))/(1000*60)).toFixed(2);
					if(orderStatus == "SETTLED"){
						orderStatus = "OUT_FOR_DELIVERY";
					}
					if (!returnObj.hasOwnProperty(orderStatus)) {
						returnObj[orderStatus] = [];
					}
					returnObj[orderStatus].push(orderInfo);
					returnObj[orderStatus].sort(function (orderInfo1, orderInfo2) {
						return new Date(orderInfo1.order.billCreationTime) - new Date(orderInfo2.order.billCreationTime);
					});
				}
			}
		}
		for(var key in returnObj){
			if(returnObj[key].length == 0){
				delete returnObj[key];
			}
		}
		return returnObj;
	}


	(function init() {
		$scope.refresh();
		$scope.initSocket();
	})();

}]).directive('changecolors', [function () {
	return function (scope, element) {
		$(element).find('.panel-heading').addClass(scope.orderStatus.toLowerCase());
	};
}]);

angular.module('posApp').controller('manualDeliveryModalCtrl', ['$scope', '$modalInstance', 'deliveryPartners', function ($scope, $modalInstance, deliveryPartners) {
	$scope.deliveryPartners = deliveryPartners;

	//check with DeliveryStatus.java for reference
	$scope.statusNames = [{id:0,name:'ACCEPTED'}, {id:1,name:'ASSIGNED'}, {id:2,name:'DEPARTED'}, {id:3,name:'ARRIVED'},
		{id:4,name:'PICKEDUP'}, {id:6,name:'DELIVERED'}, {id:9,name:'CANCELLED'}];

	//console.log($scope.deliveryPartners);
	$scope.init = function(){
		$scope.manualDelivery = {};
		$scope.manualDelivery.deliveryPartnerId = $scope.deliveryPartners[0];
		$scope.manualDelivery.deliveryStatus = $scope.statusNames[0];
	};
	$scope.ok = function () {
		$scope.manualDelivery.deliveryPartnerId = $scope.manualDelivery.deliveryPartnerId.id;
		$scope.manualDelivery.deliveryStatus = $scope.manualDelivery.deliveryStatus.id;
		//console.log($scope.manualDelivery);
		$modalInstance.close($scope.manualDelivery);
	};
	$scope.cancel = function () {
		$modalInstance.dismiss("cancel");
	};


}]);

angular.module('posApp').controller('orderDetailCtrl', ['$scope', '$modalInstance','AppUtil','$rootScope','posAPI','orderInfo',
	function ($scope, $modalInstance,AppUtil,$rootScope,posAPI,orderInfo) {
	$scope.orderInfo = orderInfo;
	console.log("orderInfo ",orderInfo);
	$scope.init = function(){
		AppUtil.startSpin('spinner-1');
		$rootScope.showFullScreenLoader = true;
		posAPI.allUrl('/',AppUtil.restUrls.order.orderTransitionDetail).post($scope.orderInfo.order.orderId)
		.then(function (response) {
			$scope.orderTransition = response.plain();
			AppUtil.stopSpin('spinner-1');
			$rootScope.showFullScreenLoader = false;
		}, function (err) {
			AppUtil.myAlert(err.data.errorMessage);
			$rootScope.showFullScreenLoader = false;
		});
	};
	$scope.closeModal=function(){
		$modalInstance.dismiss("cancel");
	};

	$scope.getAddress=function(addressObj){
		return AppUtil.arrangeAddress(addressObj,true);
	};
	
}]);