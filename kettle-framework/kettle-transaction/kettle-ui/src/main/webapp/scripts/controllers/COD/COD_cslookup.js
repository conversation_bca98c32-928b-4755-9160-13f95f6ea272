/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

angular.module('posApp').controller('COD_cslookup',
    ['$location', '$scope', 'posAPI', 'AppUtil', '$modal', 'PrintService', '$timeout', '$rootScope', 'trackingService', 'desiChaiService',
        function ($location, $scope, posAPI, AppUtil, $modal, PrintService, $timeout, $rootScope, trackingService, desiChaiService) {
        $scope.searchText = null;
        $scope.dataLoading = false;
        $scope.orders = [];
        var transactionMetadata = AppUtil.getTransactionMetadata();
        $scope.channelPartners=[];
        
        $scope.freeKettle =  AppUtil.freeKettle;

        /*$scope.newCustomer = true;*/
        $scope.isAddressNotSelected = true;
        var isNewAddress = false;
        $scope.addressSelected = false;
        var newAddressObj = null;

        var reqObj = {
            session: $rootScope.globals.currentUser,
            contactNumber: $scope.searchText,
            customer: AppUtil.CSObj,
            newAddress: null,
            newCustomer: false
        };

        $scope.isCustomerObjLoaded = false;

        var itemsPerRow = 4;

        $scope.CSObj = AppUtil.CSObj;

        $scope.addresses = [{
            locality: '',
            line1: '',
            line2: '',
            city: '',
            state: '',
            country: 'India',
            zipcode: null,
            addressType: null,
            company: null,
            preferredAddress: false,
            isSelectedAddress: false
        }];
        $scope.activePartner=null;
        
        function init(){
        	if($rootScope.isPartnerOrder){
        		selectChanneLPartner();
        	}
        	
        	if(AppUtil.defaultDeliveryDetail === undefined){
    			getDefaultDeliveryPartners();
    		}
        }
        init();
        
        function getDefaultDeliveryPartners(){
    		/***
    		 * id:  patrnerId
    		 * name :mappingType
    		 * code : mappingValue(Default delivery Partner)
    		 */
    		posAPI.allUrl('/',AppUtil.restUrls.delivery.defaultDelivery)
    		.post('Default Delivery').then(function(response){
    			AppUtil.defaultDeliveryDetail= response.plain();
    		},function(err){
    			console.log('Error in getting response',err);
    		});
    	};

        //console.log($scope.addresses);

        $scope.rows = function () {
            var rowNumber = 0;
            rowNumber = Math.ceil($scope.addresses.length / itemsPerRow);
            var rows = [];
            for (var i = 0; i < rowNumber; i++) {
                rows.push('row' + (i + 1));
            }
            return rows;

        };
        
        function selectChanneLPartner(){
        	var others = {
        		id : -1,
        		name : 'OTHERS'
        	};
        	for(var i=0;i<transactionMetadata.channelPartner.length;i++){
        		var id =transactionMetadata.channelPartner[i].id;
        		if(id == 3 || id == 5 || id == 15){
        			$scope.channelPartners.push(transactionMetadata.channelPartner[i]);
        		}
        	}
        	$scope.channelPartners.push(others);
        	 var modalInstance = $modal.open({
                 animation: true,
                 templateUrl: window.version+"scripts/modules/modals/selectChannelPartner.html",
                 controller: 'channelPartnerCtrl',
                 backdrop: 'static',
                 scope: $scope,
                 size: "lg"
             });
             modalInstance.result.then(function (result) {
                
             }, function () {
                 console.log("modal instance dismissed");
                 if(AppUtil.activeChannelPartnerId != -1){
                	for(var i=0;i<$scope.channelPartners.length;i++){
                		if($scope.channelPartners[i].id == AppUtil.activeChannelPartnerId){
                			$scope.activePartner= $scope.channelPartners[i];
                		}
                	} 
                 }
             });
        	
        }

        //rowNo starts at 1 not 0 (array starts from 0)
        function subArrayfromSubCategory(rowNo, length) {
            var itemsArray = [];
            var indexOfitemsAlreadyShown = (rowNo * itemsPerRow);
            var indexOfLastItem = indexOfitemsAlreadyShown + length;
            itemsArray = $scope.addresses.slice(indexOfitemsAlreadyShown, indexOfLastItem);
            return itemsArray;
        }

        $scope.itemsInThisRow = function (rowNo) {
            var itemsAlreadyShown = 0;
            /*onsole.log($scope.addresses);*/
            if ($scope.addresses.length <= itemsPerRow) {
                itemsAlreadyShown = $scope.addresses.length;
                return $scope.addresses;
            } else if ($scope.addresses.length > itemsPerRow) {
                if (rowNo < ($scope.rows().length - 1)) {
                    return subArrayfromSubCategory(rowNo, itemsPerRow);
                } else {
                    var modulus = $scope.addresses.length % itemsPerRow;
                    if (modulus == 0) {
                        modulus = itemsPerRow;
                    }
                    return subArrayfromSubCategory(rowNo, modulus);
                }
            }
        };

        $scope.addnewAddress = function () {
            var address = {
                locality: '',
                line1: '',
                line2: '',
                city: '',
                state: '',
                country: 'India',
                zipcode: null,
                addressType: null,
                company: null,
                preferredAddress: false,
                isSelectedAddress: false
            };
            $scope.addresses.push(address);
            // AppUtil.CSObj.addresses.push(address);
            AppUtil.CSObj.addresses = $scope.addresses;
            //console.log($scope.addresses);
            //console.log(AppUtil.CSObj);
        };

        $scope.GetCustomerObject = function () {
            //console.log($scope.searchText);
            if ($scope.searchText > 1000000000) {
                $scope.isCustomerObjLoaded = false;
                isNewAddress = false;
                AppUtil.CSObj = {};
                $scope.CSObj = AppUtil.CSObj;
                if(AppUtil.checkNumber($scope.searchText)){
                	 $rootScope.showFullScreenLoader = true;
                    setTimeout(fetchCSObj, 2000);
                } else {
                    $scope.freeKettle = false;
                    AppUtil.myAlert('Contact Number is not valid') ;
                }

            } else {
                //console.log($scope.searchText + 'second');
                AppUtil.myAlert('Contact Number can not be less than 10 digits');
            }


        };

        function fetchCSObj() {
            //console.log($scope.searchText);
            reqObj.contactNumber = $scope.searchText;
            reqObj.customer.contactNumber = $scope.searchText;
            reqObj.customer.registrationUnitId = $rootScope.globals.currentUser.unitId;
            reqObj.customer.acquisitionToken = $rootScope.globals.currentUser.userId;
            reqObj.acquisitionBrandId = AppUtil.getSelectedBrand().brandId;
            //address remove all just the first one
            var tempAddress = $scope.addresses[0];
            $scope.addresses = [];
            $scope.addresses.push(tempAddress);
            initOutletObject();
            AppUtil.CSObj = {};
            //console.log($scope.addresses);

            //console.log(reqObj);

            posAPI.allUrl('/',AppUtil.restUrls.codCustomer.lookup).post(reqObj)
                .then(function (response) {
                	if(response.customer != undefined && response.customer != null){
                    $scope.freeKettle = AppUtil.freeKettle = false; //resetting the status of first free kettle
                    $scope.isCustomerObjLoaded = true;
                    //console.log(response.plain());
                    //console.log(response);
                    AppUtil.CSObj = response.customer;
                    //not handling null condition
                    AppUtil.CSObj.newCustomer = response.newCustomer;
                    //console.log($scope.newCustomer);
                    //console.log(response.newCustomer);*/

                    $scope.CSObj = response.customer;

                    for (var i = 0; i < AppUtil.CSObj.addresses.length; i++) {
                        $scope.addresses.push(AppUtil.CSObj.addresses[i]);
                    }
                    //console.log($scope.addresses);

                    //not filling AppUtil here because of check in back function
                    //console.log(AppUtil.CSObj);
                    if(response.customer.id!=undefined && response.customer.id!=0){
                    	var reqObj = {
                             	customerId:response.customer.id,
                             	offerCode:AppUtil.freeKettleCode
                             };
                    	
                    	 posAPI.allUrl('/',AppUtil.restUrls.customerOffers.availedOffer.customer)
                    	 	.post(reqObj).then(function(response){
	                        	 //console.log("response ::: "+response);
	                             if (response == undefined) {
	                                  AppUtil.freeKettle = true;
	                             } else {
	                                  AppUtil.freeKettle = false; //double check: freeKettle to false
	                             }
	                             $scope.freeKettle = AppUtil.freeKettle;
	                             $rootScope.showFullScreenLoader = false;
	                             //console.log("free kettle check is :::: " + $scope.freeKettle);
                         },function(err){
                        	 $rootScope.showFullScreenLoader = false;
                         	//console.log('Error in getting response'+err);
                         });
                    	 
                    }else{
                    	 $rootScope.showFullScreenLoader = false;
                    }
                	}else if(response.errorMessage != undefined && response.errorMessage != null){
                        AppUtil.myAlert(response.errorMessage);
                        $rootScope.showFullScreenLoader = false;
                	}else{
                        AppUtil.myAlert('Something Went Wrong !!');
                        $rootScope.showFullScreenLoader = false;
                		
                	}
                }, function (err) {
                    AppUtil.myAlert(err.data.errorMessage);
                    $rootScope.showFullScreenLoader = false;
                });
        }

        $scope.backToCODCover = function () {
            var tempAddress = $scope.addresses[0];
            $scope.addresses = [];
            $scope.addresses.push(tempAddress);
            initOutletObject();
            //console.log($scope.addresses);
            $scope.freeKettle = AppUtil.freeKettle = false; // clearing flag for free kettle
            $location.url('/CODCover');
        };

        $scope.isEmptyObject = function (obj) {
            ////console.log(obj === null);
            return obj === null;
        };

        $scope.getStringAddressForIndex = function (parentIndex, index) {
            var arrayIndex = (parentIndex * 4) + index;
            var addressString = null;
            var address = null;
            if (arrayIndex != 0) {
                address = $scope.addresses[arrayIndex];
                removeNullUndefinedFromAddress(address);
                addressString = address.locality + ' ' + address.subLocality + "\n"
                    + address.line1 + ' ' + address.line2 + "\n"
                    + address.city + ' ' + address.state + "\n";
            } else {
                addressString = "Add New Address";
            }
            return addressString;
        };

        function removeNullUndefinedFromAddress(address) {
            for (var key in address) {
                if (address.hasOwnProperty(key)) {
                    if (address[key] == null || address[key] == undefined) {
                        address[key] = '';
                    }
                }
            }
        }
        $scope.addressButtonClicked = function (parentIndex, index) {
            var arrayIndex = (parentIndex * 4) + index;
            //console.log(arrayIndex, parentIndex, index, $scope.addresses.length);
            if (!isNewAddress) {
                //console.log(arrayIndex, isNewAddress);
                if (arrayIndex == 0) {
                    $scope.addnewAddress();
                    //console.log($scope.addresses.length);
                    arrayIndex = ($scope.addresses.length);
                    isNewAddress = true;
                }
                //console.log(arrayIndex, isNewAddress);

                initOutletObject();
                arrayIndex = arrayIndex - 1;
                //console.log(arrayIndex);
                var modalInstance = $modal.open({
                    animation: true,
                    templateUrl: window.version+'views/addEditAddressModal.html',
                    controller: 'addEditAddressModalCtrl',
                    backdrop: 'static',
                    size: 'sm',
                    resolve: {
                        index: function () {
                            return arrayIndex;
                        },
                        isNewAddress: function () {
                            return isNewAddress;
                        }
                    }
                });

                modalInstance.result.then(function (addressObj) {
                    if (isNewAddress) {
                        $scope.addresses[arrayIndex] = addressObj;
                        //console.log($scope.addresses);
                        newAddressObj = addressObj;
                        reqObj.customer = $scope.CSObj;
                        reqObj.newAddress = addressObj;
                        $scope.isAddressNotSelected = false;
                        //console.log(reqObj);
                        posAPI.allUrl('/',AppUtil.restUrls.codCustomer.addAddress).post(reqObj)
                            .then(function (response) {
                                AppUtil.customerDeliveryAddress = response;
                            }, function (err) {
                                $scope.addresses.pop();
                                AppUtil.myAlert(err.data.errorMessage);
                            });
                    } else {
                        //console.log(addressObj);
                        $scope.isAddressNotSelected = false;
                        AppUtil.customerDeliveryAddress = addressObj.id;
                        try{trackingService.setDefaultAttributes(null,addressObj.city,addressObj.locality, null);}catch(e){}
                    }
                }, function () {
                    if (isNewAddress) {
                        $scope.addresses.pop();
                    }
                    isNewAddress = false
                });
            } else {
                //to do multiple addresses
                AppUtil.myAlert("Only single new address can be added per customer session");
            }

        };

        $scope.startOrderRouting = function () {
            reqObj.customer = $scope.CSObj;
            //console.log(reqObj);
            AppUtil.startSpin('spinner-1');
            updateCustomerAndGetTaxProfile();

        };


        function updateCustomerAndGetTaxProfile() {
            //console.log(AppUtil.outlet);
            posAPI.allUrl('/',AppUtil.restUrls.codCustomer.update).post(reqObj)
                .then(function (response) {
                    ////console.log(reqObj.customer);
                    var partnerId = $scope.activePartner != null ? $scope.activePartner.id: 2; //2 is Chaayos delivery partnerId
                    if($rootScope.isPartnerOrder && AppUtil.activeChannelPartnerId == -1) {
                        partnerId = 3;
                    }
                    var brandId = AppUtil.getSelectedBrand().brandId;
                    var unitId = AppUtil.getSelectedUnitId();
                    AppUtil.setPartnerId(partnerId);
                    AppUtil.getTaxProfile();
                    AppUtil.getPackagingProfile();
                    AppUtil.getDeliveryProfile();
                    if(!AppUtil.unitPartnerBrandMappingAvailable()) {
                        bootbox.alert("No pricing unit found for brand: " + AppUtil.getSelectedBrand().brandName +
                            " and unit: " + AppUtil.getSelectedUnitIdName().name + " and partner: " + AppUtil.getPartnerNameById(partnerId));
                        return;
                    }
                    AppUtil.getBrandProductProfile(brandId, unitId, partnerId, function () {
                        desiChaiService.setRecipes();
                    });
                    /*AppUtil.getProductProfile(reqObj.customer, function () {
                        desiChaiService.setRecipes();
                    });*/
                    try{
                        var outletObj = AppUtil.getSelectedUnitIdName();
                        var addr = AppUtil.getSelectedAddress();
                        trackingService.setDefaultAttributes(null,addr.city,addr.locality,outletObj.name);
                    }catch(e){}
                    var customerObj = reqObj.customer;
                    if(customerObj.contactNumber != null){
                        try{trackingService.trackUser(customerObj.contactNumber)}catch(e){}
                        try{trackingService.trackUserData({name:customerObj.firstName,email:customerObj.emailId,contact:customerObj.contactNumber,
                            loyaltea:customerObj.loyaltyPoints, aquisitionSrc:customerObj.acquisitionSource,blacklisted:customerObj.blacklisted,
                            numberVerified:customerObj.contactNumberVerified, countryCode:customerObj.countryCode, emailVerified:customerObj.emailVerified,
                            emailSubscriber:customerObj.emailSubscriber, addTime:customerObj.addTime, numberVerificationTime:customerObj.numberVerificationTime})}catch(e){}
                    }
                }, function (err) {
                    AppUtil.myAlert(err.data.errorMessage);
                });
        }

        function initOutletObject() {
            AppUtil.outlet = {
                pri_unitId: null,
                pri_name: 'Primary Outlet',
                sec_unitId: null,
                sec_name: 'Secondary Outlet',
                ter_unitId: null,
                ter_name: 'Tertiary Outlet',
                selectedId: 1
            };
        }
        
    }]);


angular.module('posApp').controller('channelPartnerCtrl', ['$modalInstance', '$scope', 'AppUtil', '$rootScope', '$location', function ($modalInstance, $scope, AppUtil,$rootScope,$location) {
	$scope.activePartner='';
	$scope.brandList = AppUtil.transactionMetadata.brandList;
	$scope.selectedBrand = AppUtil.getSelectedBrand();
	$scope.closeSelection = function () {
		$location.url('/CODCover');
		AppUtil.activeChannelPartnerId =-1;
		$modalInstance.dismiss('dismiss');
	};

	$scope.setSelectedBrand = function(brand) {
        $scope.selectedBrand = brand;
        AppUtil.setSelectedBrand(brand);
    };

	$scope.selectedChannelPartner=function(partner){
		$scope.activePartner= partner.id;
		AppUtil.activeChannelPartnerId = $scope.activePartner; 
		$modalInstance.dismiss('dismiss');
	};
}]);
