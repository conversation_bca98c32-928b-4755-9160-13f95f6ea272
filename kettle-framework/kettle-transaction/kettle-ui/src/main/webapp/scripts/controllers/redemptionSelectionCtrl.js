/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

'use strict';

angular
    .module('posApp')
    .controller(
        'redemptionSelectionCtrl',
        ['$scope', '$modalInstance', 'AppUtil', 'redemptionCount','customerFavChaiMappings','redemptionChaiProductIds','customerObj', function ($scope, $modalInstance, AppUtil, redemptionCount,customerFavChaiMappings,redemptionChaiProductIds,customerObj) {

            $scope.init = function () {
                $scope.customerFavChaiMappings= customerFavChaiMappings;
                $scope.customerBasicInfo = customerObj;
                $scope.redemptionChaiProductIds= redemptionChaiProductIds;

                $scope.redemptionSelectionProducts = AppUtil.redemptionDisplayChaiProducts;
            	$scope.redemptionCount = redemptionCount;
            };

            
            $scope.setProduct = function(product){
                AppUtil.checkIsFavChaiRedeemed(false);
                $modalInstance.close(product.id);
            };

            $scope.setProductFromCustomerFavChaiMapping = function (customerFavChaiMapping){
                if(customerFavChaiMapping!= undefined && customerFavChaiMapping !=null &&  customerFavChaiMapping.productId!=null && customerFavChaiMapping.productId!=undefined){
                    AppUtil.checkIsFavChaiRedeemed(true);
                    AppUtil.saveClickedCustomerFavChaiMapping( customerFavChaiMapping);
                    $modalInstance.close(customerFavChaiMapping.productId);
                }
            };

            $scope.getFavChaiCustomizationShortCodes = function(customerFavChaiMapping){
                var customizationList =customerFavChaiMapping.favChaiCustomizationDetailList;
                var s =customerFavChaiMapping.productName;
                if($scope.redemptionChaiProductIds !=null && $scope.redemptionChaiProductIds!=undefined && $scope.redemptionChaiProductIds.length>0 ){
                    s=s+"( "+customerFavChaiMapping.shortCode+" )" +" - "
                }
                var detail =AppUtil.getFavChaiCustomizationShortCodes(customizationList ,customerFavChaiMapping, s);
                // console.log("Product and its Customizations shortcode details :::::::", detail);
                return detail;
            }

            $scope.isShowFavChaiInRedemption = function (favChai){
                if(favChai !== null && favChai !== undefined){
                    if($scope.redemptionChaiProductIds.indexOf(favChai.productId) < 0){
                        return false;
                    }
                }
                return true;
            }
        }]);



