<!doctype html>
<!--
  ~ SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
  ~ __________________
  ~
  ~ [2015] - [2017] Sunshine Teahouse Private Limited
  ~ All Rights Reserved.
  ~
  ~ NOTICE:  All information contained herein is, and remains
  ~ the property of Sunshine Teahouse Private Limited and its suppliers,
  ~ if any.  The intellectual and technical concepts contained
  ~ herein are proprietary to Sunshine Teahouse Private Limited
  ~ and its suppliers, and are protected by trade secret or copyright law.
  ~ Dissemination of this information or reproduction of this material
  ~ is strictly forbidden unless prior written permission is obtained
  ~ from Sunshine Teahouse Private Limited.
  -->

<html>
<head>
    <meta charset="utf-8">
    <title>Kettle - POS</title>

    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <meta name="description" content="Kettle POS">
    <meta name="author" content="@chaayos">

    <link rel="icon" type="image/png" sizes="32x32" href="favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="96x96" href="favicon-96x96.png">
    <link rel="icon" type="image/png" sizes="16x16" href="favicon-16x16.png">

            <link rel="stylesheet" href="https://d1xi2hhqgtjr19.cloudfront.net/0.0.1/css/static.c5ffb9db.css"/>
            <link rel="stylesheet" href="https://d23hbal83ofwle.cloudfront.net/0.0.1/css/content.d779d0d3.css"/>


            <script type="text/javascript" src="https://d1xi2hhqgtjr19.cloudfront.net/0.0.1/js/static.5122f1f9.js"></script>
            <script type="text/javascript" src="https://d23hbal83ofwle.cloudfront.net/0.0.1/js/content.76503635.js"></script>
            <script type="text/javascript" src="https://d23hbal83ofwle.cloudfront.net/0.0.1/js/partials.db1cf649.js"></script>

    <script data-cfasync="false" type="text/javascript">
        window.version = "0.0.1/";
        window.analyticsUrl = "https://internal.chaayos.com/";
        window.scmUrl = "https://internal.chaayos.com/";
        window.masterUrl = "https://internal.chaayos.com/";
        window.crmUrl = "https://internal.chaayos.com/";
        window.kettleUrl = "https://internal.chaayos.com/";
        window.channelPartnerUrl = "https://internal.chaayos.com/";
        window.formsUrl = "https://internal.chaayos.com/";
        window.inventoryUrl = "https://internal.chaayos.com/";
        history.pushState(null, null, '');
        window.addEventListener('popstate', function (event) {
            history.pushState(null, null, '');
        });
    </script>
</head>
<body>
<div id="handwashAlert" style="padding: 20px; background: green; color: #FFF; font-size:21px; display: none;">Please wash your hands with sanitizer.</div>
<!--[if lte IE 8]>
<p class="browsehappy">You are using an <strong>outdated</strong> browser. Please <a href="https://browsehappy.com/">upgrade
    your browser</a> to improve your experience.</p>
<![endif]-->

<noscript>
    Please enable Javascript to experience this app :)
</noscript>


<div data-ng-app="posApp" data-ng-controller="MainCtrl">
    <div data-ui-view></div>
    <div class="fullScreenLoader" data-ng-show="showFullScreenLoader">
        <img src="images/ring.gif">
    </div>
    <div class="fullScreenLoader" data-ng-show="showModalLoader">
        <img src="images/ring.gif">
    </div>
</div>


<!--  &lt;!&ndash; Google Analytics: change UA-XXXXX-X to be your site's ID &ndash;&gt;
   <script>
     !function(A,n,g,u,l,a,r){A.GoogleAnalyticsObject=l,A[l]=A[l]||function(){
     (A[l].q=A[l].q||[]).push(arguments)},A[l].l=+new Date,a=n.createElement(g),
     r=n.getElementsByTagName(g)[0],a.src=u,r.parentNode.insertBefore(a,r)
     }(window,document,'script','https://www.google-analytics.com/analytics.js','ga');

     ga('create', 'UA-XXXXX-X');
     ga('send', 'pageview');
  </script>-->

<script>
    var audio = new Audio('./audio/pop_up_alert.mp3');
    var handwashAudio = new Audio('./audio/handwashAlert.mpeg');
    audio.loop = true;
    handwashAudio.loop = true;
    var play_interval_sound = function () {
        audio.load();
        play_single_sound();
        setTimeout(stop_sound, 5000);
    };
    var play_handwash = function(){
        document.getElementById("handwashAlert").style.display = "block";
        handwashAudio.load();
        handwashAudio.play();
        setTimeout(stop_handwash,4000);
        setTimeout(function () {
            document.getElementById("handwashAlert").style.display = "none";
        },6000);
    };

    function stop_handwash() {
        handwashAudio.pause();
    }

    function stop_sound() {
        audio.pause();
    }

    function play_single_sound() {
        audio.play();
    }

    function raiseAlert(hours, minutes, text) {
        var date = new Date();
        date.setHours(hours);
        date.setMinutes(minutes);
        date.setSeconds(0);
        var ms = date.getTime() - new Date().getTime();
        if (ms > 0) {
            setTimeout(function () {
                bootbox.alert(text);
            }, ms);
        }
    }
    function playHourlyHandWash() {
        setInterval(function () {
            play_handwash();
        }, 60*60*1000)
    }
    playHourlyHandWash();
</script>
</body>
</html>
