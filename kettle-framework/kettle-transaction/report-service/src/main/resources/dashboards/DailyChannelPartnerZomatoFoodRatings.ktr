<?xml version="1.0" encoding="UTF-8"?>
<transformation>
  <info>
    <name>DailyChannelPartnerZomatoFoodRatings</name>
    <description/>
    <extended_description/>
    <trans_version/>
    <trans_type>Normal</trans_type>
    <trans_status>0</trans_status>
    <directory>/</directory>
    <parameters>
      <parameter>
        <name>file_path</name>
        <default_value/>
        <description/>
      </parameter>
    </parameters>
    <log>
      <trans-log-table>
        <connection/>
        <schema/>
        <table/>
        <size_limit_lines/>
        <interval/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STATUS</id>
          <enabled>Y</enabled>
          <name>STATUS</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
          <subject/>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
          <subject/>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
          <subject/>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
          <subject/>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
          <subject/>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
          <subject/>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>STARTDATE</id>
          <enabled>Y</enabled>
          <name>STARTDATE</name>
        </field>
        <field>
          <id>ENDDATE</id>
          <enabled>Y</enabled>
          <name>ENDDATE</name>
        </field>
        <field>
          <id>LOGDATE</id>
          <enabled>Y</enabled>
          <name>LOGDATE</name>
        </field>
        <field>
          <id>DEPDATE</id>
          <enabled>Y</enabled>
          <name>DEPDATE</name>
        </field>
        <field>
          <id>REPLAYDATE</id>
          <enabled>Y</enabled>
          <name>REPLAYDATE</name>
        </field>
        <field>
          <id>LOG_FIELD</id>
          <enabled>Y</enabled>
          <name>LOG_FIELD</name>
        </field>
        <field>
          <id>EXECUTING_SERVER</id>
          <enabled>N</enabled>
          <name>EXECUTING_SERVER</name>
        </field>
        <field>
          <id>EXECUTING_USER</id>
          <enabled>N</enabled>
          <name>EXECUTING_USER</name>
        </field>
        <field>
          <id>CLIENT</id>
          <enabled>N</enabled>
          <name>CLIENT</name>
        </field>
      </trans-log-table>
      <perf-log-table>
        <connection/>
        <schema/>
        <table/>
        <interval/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>SEQ_NR</id>
          <enabled>Y</enabled>
          <name>SEQ_NR</name>
        </field>
        <field>
          <id>LOGDATE</id>
          <enabled>Y</enabled>
          <name>LOGDATE</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STEPNAME</id>
          <enabled>Y</enabled>
          <name>STEPNAME</name>
        </field>
        <field>
          <id>STEP_COPY</id>
          <enabled>Y</enabled>
          <name>STEP_COPY</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>INPUT_BUFFER_ROWS</id>
          <enabled>Y</enabled>
          <name>INPUT_BUFFER_ROWS</name>
        </field>
        <field>
          <id>OUTPUT_BUFFER_ROWS</id>
          <enabled>Y</enabled>
          <name>OUTPUT_BUFFER_ROWS</name>
        </field>
      </perf-log-table>
      <channel-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>LOGGING_OBJECT_TYPE</id>
          <enabled>Y</enabled>
          <name>LOGGING_OBJECT_TYPE</name>
        </field>
        <field>
          <id>OBJECT_NAME</id>
          <enabled>Y</enabled>
          <name>OBJECT_NAME</name>
        </field>
        <field>
          <id>OBJECT_COPY</id>
          <enabled>Y</enabled>
          <name>OBJECT_COPY</name>
        </field>
        <field>
          <id>REPOSITORY_DIRECTORY</id>
          <enabled>Y</enabled>
          <name>REPOSITORY_DIRECTORY</name>
        </field>
        <field>
          <id>FILENAME</id>
          <enabled>Y</enabled>
          <name>FILENAME</name>
        </field>
        <field>
          <id>OBJECT_ID</id>
          <enabled>Y</enabled>
          <name>OBJECT_ID</name>
        </field>
        <field>
          <id>OBJECT_REVISION</id>
          <enabled>Y</enabled>
          <name>OBJECT_REVISION</name>
        </field>
        <field>
          <id>PARENT_CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>PARENT_CHANNEL_ID</name>
        </field>
        <field>
          <id>ROOT_CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>ROOT_CHANNEL_ID</name>
        </field>
      </channel-log-table>
      <step-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STEPNAME</id>
          <enabled>Y</enabled>
          <name>STEPNAME</name>
        </field>
        <field>
          <id>STEP_COPY</id>
          <enabled>Y</enabled>
          <name>STEP_COPY</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>LOG_FIELD</id>
          <enabled>N</enabled>
          <name>LOG_FIELD</name>
        </field>
      </step-log-table>
      <metrics-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>METRICS_DATE</id>
          <enabled>Y</enabled>
          <name>METRICS_DATE</name>
        </field>
        <field>
          <id>METRICS_CODE</id>
          <enabled>Y</enabled>
          <name>METRICS_CODE</name>
        </field>
        <field>
          <id>METRICS_DESCRIPTION</id>
          <enabled>Y</enabled>
          <name>METRICS_DESCRIPTION</name>
        </field>
        <field>
          <id>METRICS_SUBJECT</id>
          <enabled>Y</enabled>
          <name>METRICS_SUBJECT</name>
        </field>
        <field>
          <id>METRICS_TYPE</id>
          <enabled>Y</enabled>
          <name>METRICS_TYPE</name>
        </field>
        <field>
          <id>METRICS_VALUE</id>
          <enabled>Y</enabled>
          <name>METRICS_VALUE</name>
        </field>
      </metrics-log-table>
    </log>
    <maxdate>
      <connection/>
      <table/>
      <field/>
      <offset>0.0</offset>
      <maxdiff>0.0</maxdiff>
    </maxdate>
    <size_rowset>10000</size_rowset>
    <sleep_time_empty>50</sleep_time_empty>
    <sleep_time_full>50</sleep_time_full>
    <unique_connections>N</unique_connections>
    <feedback_shown>Y</feedback_shown>
    <feedback_size>50000</feedback_size>
    <using_thread_priorities>Y</using_thread_priorities>
    <shared_objects_file/>
    <capture_step_performance>N</capture_step_performance>
    <step_performance_capturing_delay>1000</step_performance_capturing_delay>
    <step_performance_capturing_size_limit>100</step_performance_capturing_size_limit>
    <dependencies>
    </dependencies>
    <partitionschemas>
    </partitionschemas>
    <slaveservers>
    </slaveservers>
    <clusterschemas>
    </clusterschemas>
    <created_user>-</created_user>
    <created_date>2023/09/27 12:49:24.131</created_date>
    <modified_user>-</modified_user>
    <modified_date>2023/09/27 12:49:24.131</modified_date>
    <key_for_session_key>H4sIAAAAAAAAAAMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
  </notepads>
  <connection>
    <name>DUMP</name>
    <server>dump.kettle.chaayos.com</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>KETTLE_ANALYTICS</database>
    <port>3306</port>
    <username>rptusr</username>
    <password>Encrypted 2be98afc86aa7f2e4f84bff59d0d18f9b</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>500</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>3306</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <order>
    <hop>
      <from>Filter rows</from>
      <to>Set field value to a constant</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Filter rows</from>
      <to>Set field value to a constant 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Set field value to a constant</from>
      <to>Filter rows 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Set field value to a constant 2</from>
      <to>Filter rows 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Calculator</from>
      <to>Filter rows</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Microsoft Excel input</from>
      <to>Calculator</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Filter rows 2</from>
      <to>Strings cut</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Strings cut</from>
      <to>Calculator 2</to>
      <enabled>Y</enabled>
    </hop>
    <hop>
      <from>Calculator 2</from>
      <to>Table output</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <step>
    <name>Calculator</name>
    <type>Calculator</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <failIfNoFile>Y</failIfNoFile>
    <calculation>
      <field_name>BRAND_ID</field_name>
      <calc_type>CONSTANT</calc_type>
      <field_a>-1</field_a>
      <field_b/>
      <field_c/>
      <value_type>String</value_type>
      <value_length>-1</value_length>
      <value_precision>-1</value_precision>
      <remove>N</remove>
      <conversion_mask/>
      <decimal_symbol/>
      <grouping_symbol/>
      <currency_symbol/>
    </calculation>
    <calculation>
      <field_name>dateInt</field_name>
      <calc_type>COPY_FIELD</calc_type>
      <field_a>AGGREGATION</field_a>
      <field_b/>
      <field_c/>
      <value_type>Integer</value_type>
      <value_length>-1</value_length>
      <value_precision>-1</value_precision>
      <remove>N</remove>
      <conversion_mask/>
      <decimal_symbol/>
      <grouping_symbol/>
      <currency_symbol/>
    </calculation>
    <calculation>
      <field_name>dateString</field_name>
      <calc_type>COPY_FIELD</calc_type>
      <field_a>dateInt</field_a>
      <field_b/>
      <field_c/>
      <value_type>String</value_type>
      <value_length>-1</value_length>
      <value_precision>-1</value_precision>
      <remove>N</remove>
      <conversion_mask/>
      <decimal_symbol/>
      <grouping_symbol/>
      <currency_symbol/>
    </calculation>
    <calculation>
      <field_name>hyphen</field_name>
      <calc_type>CONSTANT</calc_type>
      <field_a>-</field_a>
      <field_b/>
      <field_c/>
      <value_type>None</value_type>
      <value_length>-1</value_length>
      <value_precision>-1</value_precision>
      <remove>N</remove>
      <conversion_mask/>
      <decimal_symbol/>
      <grouping_symbol/>
      <currency_symbol/>
    </calculation>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>304</xloc>
      <yloc>320</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>Calculator 2</name>
    <type>Calculator</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <failIfNoFile>Y</failIfNoFile>
    <calculation>
      <field_name>yearStr</field_name>
      <calc_type>ADD</calc_type>
      <field_a>year</field_a>
      <field_b>hyphen</field_b>
      <field_c/>
      <value_type>String</value_type>
      <value_length>-1</value_length>
      <value_precision>-1</value_precision>
      <remove>N</remove>
      <conversion_mask/>
      <decimal_symbol/>
      <grouping_symbol/>
      <currency_symbol/>
    </calculation>
    <calculation>
      <field_name>monthStr</field_name>
      <calc_type>ADD</calc_type>
      <field_a>month</field_a>
      <field_b>hyphen</field_b>
      <field_c/>
      <value_type>String</value_type>
      <value_length>-1</value_length>
      <value_precision>-1</value_precision>
      <remove>N</remove>
      <conversion_mask/>
      <decimal_symbol/>
      <grouping_symbol/>
      <currency_symbol/>
    </calculation>
    <calculation>
      <field_name>monthDay</field_name>
      <calc_type>ADD</calc_type>
      <field_a>monthStr</field_a>
      <field_b>day</field_b>
      <field_c/>
      <value_type>String</value_type>
      <value_length>-1</value_length>
      <value_precision>-1</value_precision>
      <remove>N</remove>
      <conversion_mask/>
      <decimal_symbol/>
      <grouping_symbol/>
      <currency_symbol/>
    </calculation>
    <calculation>
      <field_name>BUSINESS_DATE</field_name>
      <calc_type>ADD</calc_type>
      <field_a>yearStr</field_a>
      <field_b>monthDay</field_b>
      <field_c/>
      <value_type>Date</value_type>
      <value_length>-1</value_length>
      <value_precision>-1</value_precision>
      <remove>N</remove>
      <conversion_mask>yyyy-MM-dd</conversion_mask>
      <decimal_symbol/>
      <grouping_symbol/>
      <currency_symbol/>
    </calculation>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1104</xloc>
      <yloc>304</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>Filter rows</name>
    <type>FilterRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <send_true_to>Set field value to a constant</send_true_to>
    <send_false_to>Set field value to a constant 2</send_false_to>
    <compare>
      <condition>
        <negated>N</negated>
        <leftvalue>res_name</leftvalue>
        <function>=</function>
        <rightvalue/>
        <value>
          <name>constant</name>
          <type>String</type>
          <text>Ghee and Turmeric</text>
          <length>-1</length>
          <precision>-1</precision>
          <isnull>N</isnull>
          <mask/>
        </value>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>544</xloc>
      <yloc>320</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>Filter rows 2</name>
    <type>FilterRows</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <send_true_to>Strings cut</send_true_to>
    <send_false_to/>
    <compare>
      <condition>
        <negated>N</negated>
        <leftvalue>AGGREGATION</leftvalue>
        <function>&gt;</function>
        <rightvalue/>
        <value>
          <name>constant</name>
          <type>Number</type>
          <text>2.0220113E7</text>
          <length>-1</length>
          <precision>-1</precision>
          <isnull>N</isnull>
          <mask>####0.0#########;-####0.0#########</mask>
        </value>
      </condition>
    </compare>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>896</xloc>
      <yloc>304</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>Microsoft Excel input</name>
    <type>ExcelInput</type>
    <description/>
    <distribute>N</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <header>Y</header>
    <noempty>Y</noempty>
    <stoponempty>N</stoponempty>
    <filefield/>
    <sheetfield/>
    <sheetrownumfield/>
    <rownumfield/>
    <sheetfield/>
    <filefield/>
    <limit>0</limit>
    <encoding/>
    <add_to_result_filenames>Y</add_to_result_filenames>
    <accept_filenames>N</accept_filenames>
    <accept_field/>
    <accept_stepname/>
    <file>
      <name>${file_path}</name>
      <filemask/>
      <exclude_filemask/>
      <file_required>N</file_required>
      <include_subfolders>N</include_subfolders>
    </file>
    <fields>
      <field>
        <name>ORDER_ID</name>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
        <format/>
        <currency/>
        <decimal/>
        <group/>
      </field>
      <field>
        <name>CHANNEL_RESTAURANT_ID</name>
        <type>Number</type>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
        <format/>
        <currency/>
        <decimal/>
        <group/>
      </field>
      <field>
        <name>res_name</name>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
        <format/>
        <currency/>
        <decimal/>
        <group/>
      </field>
      <field>
        <name>city_name</name>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
        <format/>
        <currency/>
        <decimal/>
        <group/>
      </field>
      <field>
        <name>AGGREGATION</name>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
        <format/>
        <currency/>
        <decimal/>
        <group/>
      </field>
      <field>
        <name>DELIVERY_RATING</name>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
        <format/>
        <currency/>
        <decimal/>
        <group/>
      </field>
      <field>
        <name>FOOD_RATING</name>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
        <format/>
        <currency/>
        <decimal/>
        <group/>
      </field>
      <field>
        <name>ORS</name>
        <type>Number</type>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
        <format/>
        <currency/>
        <decimal/>
        <group/>
      </field>
      <field>
        <name>ORS_REASON</name>
        <type>String</type>
        <length>-1</length>
        <precision>-1</precision>
        <trim_type>none</trim_type>
        <repeat>N</repeat>
        <format/>
        <currency/>
        <decimal/>
        <group/>
      </field>
    </fields>
    <sheets>
      <sheet>
        <name>Zomato_Food_Rating</name>
        <startrow>0</startrow>
        <startcol>0</startcol>
      </sheet>
    </sheets>
    <strict_types>N</strict_types>
    <error_ignored>N</error_ignored>
    <error_line_skipped>N</error_line_skipped>
    <bad_line_files_destination_directory/>
    <bad_line_files_extension>warning</bad_line_files_extension>
    <error_line_files_destination_directory/>
    <error_line_files_extension>error</error_line_files_extension>
    <line_number_files_destination_directory/>
    <line_number_files_extension>line</line_number_files_extension>
    <shortFileFieldName/>
    <pathFieldName/>
    <hiddenFieldName/>
    <lastModificationTimeFieldName/>
    <uriNameFieldName/>
    <rootUriNameFieldName/>
    <extensionFieldName/>
    <sizeFieldName/>
    <spreadsheet_type>POI</spreadsheet_type>
    <password>Encrypted </password>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>144</xloc>
      <yloc>320</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>Set field value to a constant</name>
    <type>SetValueConstant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <usevar>N</usevar>
    <fields>
      <field>
        <name>BRAND_ID</name>
        <value>3</value>
        <mask/>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>720</xloc>
      <yloc>192</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>Set field value to a constant 2</name>
    <type>SetValueConstant</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <usevar>N</usevar>
    <fields>
      <field>
        <name>BRAND_ID</name>
        <value>1</value>
        <mask/>
        <set_empty_string>N</set_empty_string>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>720</xloc>
      <yloc>448</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>Strings cut</name>
    <type>StringCut</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <fields>
      <field>
        <in_stream_name>dateString</in_stream_name>
        <out_stream_name>year</out_stream_name>
        <cut_from>0</cut_from>
        <cut_to>4</cut_to>
      </field>
      <field>
        <in_stream_name>dateString</in_stream_name>
        <out_stream_name>month</out_stream_name>
        <cut_from>4</cut_from>
        <cut_to>6</cut_to>
      </field>
      <field>
        <in_stream_name>dateString</in_stream_name>
        <out_stream_name>day</out_stream_name>
        <cut_from>6</cut_from>
        <cut_to>8</cut_to>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>992</xloc>
      <yloc>304</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>Table output</name>
    <type>TableOutput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>DUMP</connection>
    <schema>KETTLE_ANALYTICS</schema>
    <table>ZOMD_FOOD_RATING</table>
    <commit>1000</commit>
    <truncate>N</truncate>
    <ignore_errors>N</ignore_errors>
    <use_batch>Y</use_batch>
    <specify_fields>Y</specify_fields>
    <partitioning_enabled>N</partitioning_enabled>
    <partitioning_field/>
    <partitioning_daily>N</partitioning_daily>
    <partitioning_monthly>Y</partitioning_monthly>
    <tablename_in_field>N</tablename_in_field>
    <tablename_field/>
    <tablename_in_table>Y</tablename_in_table>
    <return_keys>N</return_keys>
    <return_field/>
    <fields>
      <field>
        <column_name>ORDER_ID</column_name>
        <stream_name>ORDER_ID</stream_name>
      </field>
      <field>
        <column_name>CHANNEL_RESTAURANT_ID</column_name>
        <stream_name>CHANNEL_RESTAURANT_ID</stream_name>
      </field>
      <field>
        <column_name>AGGREGATION</column_name>
        <stream_name>AGGREGATION</stream_name>
      </field>
      <field>
        <column_name>DELIVERY_RATING</column_name>
        <stream_name>DELIVERY_RATING</stream_name>
      </field>
      <field>
        <column_name>FOOD_RATING</column_name>
        <stream_name>FOOD_RATING</stream_name>
      </field>
      <field>
        <column_name>ORS</column_name>
        <stream_name>ORS</stream_name>
      </field>
      <field>
        <column_name>ORS_REASON</column_name>
        <stream_name>ORS_REASON</stream_name>
      </field>
      <field>
        <column_name>BRAND_ID</column_name>
        <stream_name>BRAND_ID</stream_name>
      </field>
      <field>
        <column_name>BUSINESS_DATE</column_name>
        <stream_name>BUSINESS_DATE</stream_name>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>1264</xloc>
      <yloc>304</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step_error_handling>
  </step_error_handling>
  <slave-step-copy-partition-distribution>
  </slave-step-copy-partition-distribution>
  <slave_transformation>N</slave_transformation>
  <attributes/>
</transformation>
