<?xml version="1.0" encoding="UTF-8"?>
<transformation>
  <info>
    <name>service_request_v2_transformation</name>
    <description/>
    <extended_description/>
    <trans_version/>
    <trans_type>Normal</trans_type>
    <directory>/</directory>
    <parameters>
    </parameters>
    <log>
      <trans-log-table>
        <connection/>
        <schema/>
        <table/>
        <size_limit_lines/>
        <interval/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STATUS</id>
          <enabled>Y</enabled>
          <name>STATUS</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
          <subject/>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
          <subject/>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
          <subject/>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
          <subject/>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
          <subject/>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
          <subject/>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>STARTDATE</id>
          <enabled>Y</enabled>
          <name>STARTDATE</name>
        </field>
        <field>
          <id>ENDDATE</id>
          <enabled>Y</enabled>
          <name>ENDDATE</name>
        </field>
        <field>
          <id>LOGDATE</id>
          <enabled>Y</enabled>
          <name>LOGDATE</name>
        </field>
        <field>
          <id>DEPDATE</id>
          <enabled>Y</enabled>
          <name>DEPDATE</name>
        </field>
        <field>
          <id>REPLAYDATE</id>
          <enabled>Y</enabled>
          <name>REPLAYDATE</name>
        </field>
        <field>
          <id>LOG_FIELD</id>
          <enabled>Y</enabled>
          <name>LOG_FIELD</name>
        </field>
        <field>
          <id>EXECUTING_SERVER</id>
          <enabled>N</enabled>
          <name>EXECUTING_SERVER</name>
        </field>
        <field>
          <id>EXECUTING_USER</id>
          <enabled>N</enabled>
          <name>EXECUTING_USER</name>
        </field>
        <field>
          <id>CLIENT</id>
          <enabled>N</enabled>
          <name>CLIENT</name>
        </field>
      </trans-log-table>
      <perf-log-table>
        <connection/>
        <schema/>
        <table/>
        <interval/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>SEQ_NR</id>
          <enabled>Y</enabled>
          <name>SEQ_NR</name>
        </field>
        <field>
          <id>LOGDATE</id>
          <enabled>Y</enabled>
          <name>LOGDATE</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STEPNAME</id>
          <enabled>Y</enabled>
          <name>STEPNAME</name>
        </field>
        <field>
          <id>STEP_COPY</id>
          <enabled>Y</enabled>
          <name>STEP_COPY</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>INPUT_BUFFER_ROWS</id>
          <enabled>Y</enabled>
          <name>INPUT_BUFFER_ROWS</name>
        </field>
        <field>
          <id>OUTPUT_BUFFER_ROWS</id>
          <enabled>Y</enabled>
          <name>OUTPUT_BUFFER_ROWS</name>
        </field>
      </perf-log-table>
      <channel-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>LOGGING_OBJECT_TYPE</id>
          <enabled>Y</enabled>
          <name>LOGGING_OBJECT_TYPE</name>
        </field>
        <field>
          <id>OBJECT_NAME</id>
          <enabled>Y</enabled>
          <name>OBJECT_NAME</name>
        </field>
        <field>
          <id>OBJECT_COPY</id>
          <enabled>Y</enabled>
          <name>OBJECT_COPY</name>
        </field>
        <field>
          <id>REPOSITORY_DIRECTORY</id>
          <enabled>Y</enabled>
          <name>REPOSITORY_DIRECTORY</name>
        </field>
        <field>
          <id>FILENAME</id>
          <enabled>Y</enabled>
          <name>FILENAME</name>
        </field>
        <field>
          <id>OBJECT_ID</id>
          <enabled>Y</enabled>
          <name>OBJECT_ID</name>
        </field>
        <field>
          <id>OBJECT_REVISION</id>
          <enabled>Y</enabled>
          <name>OBJECT_REVISION</name>
        </field>
        <field>
          <id>PARENT_CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>PARENT_CHANNEL_ID</name>
        </field>
        <field>
          <id>ROOT_CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>ROOT_CHANNEL_ID</name>
        </field>
      </channel-log-table>
      <step-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STEPNAME</id>
          <enabled>Y</enabled>
          <name>STEPNAME</name>
        </field>
        <field>
          <id>STEP_COPY</id>
          <enabled>Y</enabled>
          <name>STEP_COPY</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>LOG_FIELD</id>
          <enabled>N</enabled>
          <name>LOG_FIELD</name>
        </field>
      </step-log-table>
      <metrics-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>METRICS_DATE</id>
          <enabled>Y</enabled>
          <name>METRICS_DATE</name>
        </field>
        <field>
          <id>METRICS_CODE</id>
          <enabled>Y</enabled>
          <name>METRICS_CODE</name>
        </field>
        <field>
          <id>METRICS_DESCRIPTION</id>
          <enabled>Y</enabled>
          <name>METRICS_DESCRIPTION</name>
        </field>
        <field>
          <id>METRICS_SUBJECT</id>
          <enabled>Y</enabled>
          <name>METRICS_SUBJECT</name>
        </field>
        <field>
          <id>METRICS_TYPE</id>
          <enabled>Y</enabled>
          <name>METRICS_TYPE</name>
        </field>
        <field>
          <id>METRICS_VALUE</id>
          <enabled>Y</enabled>
          <name>METRICS_VALUE</name>
        </field>
      </metrics-log-table>
    </log>
    <maxdate>
      <connection/>
      <table/>
      <field/>
      <offset>0.0</offset>
      <maxdiff>0.0</maxdiff>
    </maxdate>
    <size_rowset>10000</size_rowset>
    <sleep_time_empty>50</sleep_time_empty>
    <sleep_time_full>50</sleep_time_full>
    <unique_connections>N</unique_connections>
    <feedback_shown>Y</feedback_shown>
    <feedback_size>50000</feedback_size>
    <using_thread_priorities>Y</using_thread_priorities>
    <shared_objects_file/>
    <capture_step_performance>N</capture_step_performance>
    <step_performance_capturing_delay>1000</step_performance_capturing_delay>
    <step_performance_capturing_size_limit>100</step_performance_capturing_size_limit>
    <dependencies>
    </dependencies>
    <partitionschemas>
    </partitionschemas>
    <slaveservers>
    </slaveservers>
    <clusterschemas>
    </clusterschemas>
    <created_user>-</created_user>
    <created_date>2023/04/22 11:19:06.715</created_date>
    <modified_user>-</modified_user>
    <modified_date>2023/04/22 11:19:06.715</modified_date>
    <key_for_session_key>H4sIAAAAAAAA/wMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
  </notepads>
  <connection>
    <name>prod_read</name>
    <server>${host_name}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${db_name}</database>
    <port>${port_number}</port>
    <username>${user_name}</username>
    <password>${password}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>2000</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>${port_number}</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <order>
    <hop>
      <from>service_request_v2</from>
      <to>service_request_v2 output</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <step>
    <name>service_request_v2</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>prod_read</connection>
    <sql>SELECT
    sr.SERVICE_RECEIVED_ID,
    sr.VENDOR_ID,
    vd.ENTITY_NAME as VENDOR_NAME,
    ld.CITY as DELIVERY_LOCATION,
    sr.SERVICE_RECEIVE_STATUS as SR_STATUS,
    sr.CREATED_AT,
    sri.SERVICE_RECEIVED_ITEM_ID as SR_ITEM_ID,
    sri.COST_ELEMENT_ID,
    sri.COST_ELEMENT_NAME,
    sri.UNIT_OF_MEASURE as UOM,
    sri.UNIT_PRICE,
    sri.ASC_CODE AS SAC_CODE,
    sri.RECEIVED_QUANTITY,
    sri.TOTAL_PRICE,
    sri.TOTAL_TAX,
    sri.TOTAL_AMOUNT,
    sri.TAX_RATE,
    sri.SERVICE_DESCRIPTION,
    sri.BUSINESS_COST_CENTER_ID as BCC_ID,
    sri.BUSINESS_COST_CENTER_NAME as BCC_NAME,
	ud.COST_CENTER as TALLY_COST_CENTER,
    ccd.COST_CENTER_NAME as COST_CENTER,
    ud.UNIT_STATUS,
    so.SERVICE_ORDER_ID,
    so.GENERATION_TIME AS SO_GENERATION_TIME,
    pr.INVOICE_NUMBER,
    sr.PAYMENT_REQUEST_ID,
    pr.PAID_AMOUNT,
    pr.CREATION_TIME as PR_DATE,
    pr.PAYMENT_DATE,
    pr.CURRENT_STATUS as PR_STATUS,
    so.GENERATED_BY,
    emp1.EMP_NAME AS GENERATED_BY,
    so.APPROVED_BY,
    emp2.EMP_NAME AS EMP_APPROVED_NAME,
    so.TYPE AS BUDGET_TYPE,
    soi.COST_ELEMENT_DATE as SERVICE_START,
    soi.COST_ELEMENT_TO_DATE as SERVICE_END,
    lt.BUDGET_CATEGORY as BUDGET_HEAD,
    lt2.BUDGET_CATEGORY  as CAPEX_BUDGET_HEAD
FROM
	KETTLE_SCM.SERVICE_RECEIVED_ITEM sri
		INNER JOIN
	KETTLE_SCM.SERVICE_RECEIVED_DATA sr ON sri.SERVICE_RECEIVED_ID = sr.SERVICE_RECEIVED_ID
		INNER JOIN
	KETTLE_SCM.SERVICE_ORDER so ON sri.SERVICE_ORDER_ID = so.SERVICE_ORDER_ID
		LEFT JOIN
	KETTLE_SCM.PAYMENT_REQUEST pr ON sr.PAYMENT_REQUEST_ID = pr.PAYMENT_REQUEST_ID
		INNER JOIN
	KETTLE_SCM.SERVICE_ORDER_ITEM soi ON sri.SERVICE_ORDER_ITEM_ID = soi.SERVICE_ORDER_ITEM_ID
		INNER JOIN
	KETTLE_SCM.VENDOR_DETAIL_DATA vd ON sr.VENDOR_ID = vd.VENDOR_ID
		INNER JOIN
	KETTLE_SCM.COST_CENTER_DATA ccd ON so.COST_CENTER_ID = ccd.COST_CENTER_ID
		INNER JOIN 
    KETTLE_MASTER.EMPLOYEE_DETAIL emp1 ON so.GENERATED_BY = emp1.EMP_ID
		INNER JOIN
    KETTLE_MASTER.EMPLOYEE_DETAIL emp2 ON so.APPROVED_BY = emp2.EMP_ID
		INNER JOIN
	KETTLE_MASTER.LOCATION_DETAIL ld ON sr.DELIVERY_LOCATION_ID = ld.LOCATION_ID
		INNER JOIN
	KETTLE_SCM.COST_ELEMENT_DATA ced ON sri.COST_ELEMENT_ID = ced.COST_ELEMENT_ID
		LEFT JOIN
	KETTLE_SCM.LIST_TYPE lt ON ced.SUB_CATEGORY = lt.LIST_TYPE_ID
     LEFT JOIN 
     KETTLE_SCM.LIST_TYPE lt2 ON ced.CAPEX_SUB_CATEGORY = lt2.LIST_TYPE_ID
		INNER JOIN
	KETTLE_SCM.BUSINESS_COST_CENTER_DATA bcc ON sri.BUSINESS_COST_CENTER_ID = bcc.BCC_ID
		LEFT JOIN
 	KETTLE_MASTER.UNIT_DETAIL ud ON bcc.BCC_CODE = ud.UNIT_ID
WHERE
	DATE(sr.CREATED_AT) BETWEEN '2019-01-31' AND ${end_date}
        AND sr.SERVICE_RECEIVE_STATUS &lt;&gt; 'CANCELLED';
</sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>Y</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta/>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>416</xloc>
      <yloc>256</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>service_request_v2 output</name>
    <type>TextFileOutput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <separator>,</separator>
    <enclosure>"</enclosure>
    <enclosure_forced>Y</enclosure_forced>
    <enclosure_fix_disabled>N</enclosure_fix_disabled>
    <header>Y</header>
    <footer>N</footer>
    <format>DOS</format>
    <compression>None</compression>
    <encoding/>
    <endedLine/>
    <fileNameInField>N</fileNameInField>
    <fileNameField/>
    <create_parent_folder>Y</create_parent_folder>
    <file>
      <name>${base_path}\service_request_v2</name>
      <servlet_output>N</servlet_output>
      <do_not_open_new_file_init>N</do_not_open_new_file_init>
      <extention>csv</extention>
      <append>N</append>
      <split>N</split>
      <haspartno>N</haspartno>
      <add_date>Y</add_date>
      <add_time>Y</add_time>
      <SpecifyFormat>N</SpecifyFormat>
      <date_time_format/>
      <add_to_result_filenames>Y</add_to_result_filenames>
      <pad>N</pad>
      <fast_dump>Y</fast_dump>
      <splitevery/>
    </file>
    <fields>
      <field>
        <name>SERVICE_RECEIVED_ID</name>
        <type>Integer</type>
        <format>#</format>
        <currency/>
        <decimal>.</decimal>
        <group>,</group>
        <nullif/>
        <trim_type>none</trim_type>
        <length>9</length>
        <precision>0</precision>
      </field>
      <field>
        <name>VENDOR_ID</name>
        <type>Integer</type>
        <format>#</format>
        <currency/>
        <decimal>.</decimal>
        <group>,</group>
        <nullif/>
        <trim_type>none</trim_type>
        <length>9</length>
        <precision>0</precision>
      </field>
      <field>
        <name>VENDOR_NAME</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>none</trim_type>
        <length>150</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>DELIVERY_LOCATION</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>none</trim_type>
        <length>100</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>SR_STATUS</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>none</trim_type>
        <length>20</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>CREATED_AT</name>
        <type>Timestamp</type>
        <format>yyyy/MM/dd HH:mm:ss XXX</format>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>none</trim_type>
        <length>0</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>SR_ITEM_ID</name>
        <type>Integer</type>
        <format>#</format>
        <currency/>
        <decimal>.</decimal>
        <group>,</group>
        <nullif/>
        <trim_type>none</trim_type>
        <length>9</length>
        <precision>0</precision>
      </field>
      <field>
        <name>COST_ELEMENT_ID</name>
        <type>Integer</type>
        <format>#</format>
        <currency/>
        <decimal>.</decimal>
        <group>,</group>
        <nullif/>
        <trim_type>none</trim_type>
        <length>9</length>
        <precision>0</precision>
      </field>
      <field>
        <name>COST_ELEMENT_NAME</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>none</trim_type>
        <length>100</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>UOM</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>none</trim_type>
        <length>20</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>UNIT_PRICE</name>
        <type>BigNumber</type>
        <format>######0.0###################;-######0.0###################</format>
        <currency/>
        <decimal>.</decimal>
        <group/>
        <nullif/>
        <trim_type>none</trim_type>
        <length>20</length>
        <precision>6</precision>
      </field>
      <field>
        <name>SAC_CODE</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>none</trim_type>
        <length>20</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>RECEIVED_QUANTITY</name>
        <type>BigNumber</type>
        <format>######0.0###################;-######0.0###################</format>
        <currency/>
        <decimal>.</decimal>
        <group/>
        <nullif/>
        <trim_type>none</trim_type>
        <length>20</length>
        <precision>6</precision>
      </field>
      <field>
        <name>TOTAL_PRICE</name>
        <type>BigNumber</type>
        <format>######0.0###################;-######0.0###################</format>
        <currency/>
        <decimal>.</decimal>
        <group/>
        <nullif/>
        <trim_type>none</trim_type>
        <length>20</length>
        <precision>6</precision>
      </field>
      <field>
        <name>TOTAL_TAX</name>
        <type>BigNumber</type>
        <format>######0.0###################;-######0.0###################</format>
        <currency/>
        <decimal>.</decimal>
        <group/>
        <nullif/>
        <trim_type>none</trim_type>
        <length>20</length>
        <precision>6</precision>
      </field>
      <field>
        <name>TOTAL_AMOUNT</name>
        <type>BigNumber</type>
        <format>######0.0###################;-######0.0###################</format>
        <currency/>
        <decimal>.</decimal>
        <group/>
        <nullif/>
        <trim_type>none</trim_type>
        <length>20</length>
        <precision>6</precision>
      </field>
      <field>
        <name>TAX_RATE</name>
        <type>BigNumber</type>
        <format>######0.0###################;-######0.0###################</format>
        <currency/>
        <decimal>.</decimal>
        <group/>
        <nullif/>
        <trim_type>none</trim_type>
        <length>20</length>
        <precision>6</precision>
      </field>
      <field>
        <name>SERVICE_DESCRIPTION</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>none</trim_type>
        <length>1000</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>BCC_ID</name>
        <type>Integer</type>
        <format>#</format>
        <currency/>
        <decimal>.</decimal>
        <group>,</group>
        <nullif/>
        <trim_type>none</trim_type>
        <length>9</length>
        <precision>0</precision>
      </field>
      <field>
        <name>BCC_NAME</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>none</trim_type>
        <length>100</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>TALLY_COST_CENTER</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>none</trim_type>
        <length>255</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>COST_CENTER</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>none</trim_type>
        <length>20</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>UNIT_STATUS</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>none</trim_type>
        <length>15</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>SERVICE_ORDER_ID</name>
        <type>Integer</type>
        <format>#</format>
        <currency/>
        <decimal>.</decimal>
        <group>,</group>
        <nullif/>
        <trim_type>none</trim_type>
        <length>9</length>
        <precision>0</precision>
      </field>
      <field>
        <name>SO_GENERATION_TIME</name>
        <type>Timestamp</type>
        <format>yyyy/MM/dd HH:mm:ss XXX</format>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>none</trim_type>
        <length>0</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>INVOICE_NUMBER</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>none</trim_type>
        <length>255</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>PAYMENT_REQUEST_ID</name>
        <type>Integer</type>
        <format>#</format>
        <currency/>
        <decimal>.</decimal>
        <group>,</group>
        <nullif/>
        <trim_type>none</trim_type>
        <length>9</length>
        <precision>0</precision>
      </field>
      <field>
        <name>PAID_AMOUNT</name>
        <type>BigNumber</type>
        <format>######0.0###################;-######0.0###################</format>
        <currency/>
        <decimal>.</decimal>
        <group/>
        <nullif/>
        <trim_type>none</trim_type>
        <length>16</length>
        <precision>6</precision>
      </field>
      <field>
        <name>PR_DATE</name>
        <type>Timestamp</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>none</trim_type>
        <length>0</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>PAYMENT_DATE</name>
        <type>Timestamp</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>none</trim_type>
        <length>0</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>PR_STATUS</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>none</trim_type>
        <length>50</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>GENERATED_BY</name>
        <type>Integer</type>
        <format>#</format>
        <currency/>
        <decimal>.</decimal>
        <group>,</group>
        <nullif/>
        <trim_type>none</trim_type>
        <length>9</length>
        <precision>0</precision>
      </field>
      <field>
        <name>GENERATED_BY_1</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>none</trim_type>
        <length>255</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>APPROVED_BY</name>
        <type>Integer</type>
        <format>#</format>
        <currency/>
        <decimal>.</decimal>
        <group>,</group>
        <nullif/>
        <trim_type>none</trim_type>
        <length>9</length>
        <precision>0</precision>
      </field>
      <field>
        <name>EMP_APPROVED_NAME</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>none</trim_type>
        <length>255</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>BUDGET_TYPE</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>none</trim_type>
        <length>45</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>SERVICE_START</name>
        <type>Date</type>
        <format>yyyy/MM/dd HH:mm:ss XXX</format>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>none</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>SERVICE_END</name>
        <type>Date</type>
        <format>yyyy/MM/dd HH:mm:ss XXX</format>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>none</trim_type>
        <length>-1</length>
        <precision>-1</precision>
      </field>
      <field>
        <name>BUDGET_HEAD</name>
        <type>String</type>
        <format/>
        <currency/>
        <decimal/>
        <group/>
        <nullif/>
        <trim_type>none</trim_type>
        <length>50</length>
        <precision>-1</precision>
      </field>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>656</xloc>
      <yloc>256</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step_error_handling>
  </step_error_handling>
  <slave-step-copy-partition-distribution>
  </slave-step-copy-partition-distribution>
  <slave_transformation>N</slave_transformation>
  <attributes/>
</transformation>
