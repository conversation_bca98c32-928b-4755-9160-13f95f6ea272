<?xml version="1.0" encoding="UTF-8"?>
<transformation>
  <info>
    <name>Settlement_report_Transformation</name>
    <description/>
    <extended_description/>
    <trans_version/>
    <trans_type>Normal</trans_type>
    <directory>/</directory>
    <parameters>
    </parameters>
    <log>
      <trans-log-table>
        <connection/>
        <schema/>
        <table/>
        <size_limit_lines/>
        <interval/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STATUS</id>
          <enabled>Y</enabled>
          <name>STATUS</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
          <subject/>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
          <subject/>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
          <subject/>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
          <subject/>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
          <subject/>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
          <subject/>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>STARTDATE</id>
          <enabled>Y</enabled>
          <name>STARTDATE</name>
        </field>
        <field>
          <id>ENDDATE</id>
          <enabled>Y</enabled>
          <name>ENDDATE</name>
        </field>
        <field>
          <id>LOGDATE</id>
          <enabled>Y</enabled>
          <name>LOGDATE</name>
        </field>
        <field>
          <id>DEPDATE</id>
          <enabled>Y</enabled>
          <name>DEPDATE</name>
        </field>
        <field>
          <id>REPLAYDATE</id>
          <enabled>Y</enabled>
          <name>REPLAYDATE</name>
        </field>
        <field>
          <id>LOG_FIELD</id>
          <enabled>Y</enabled>
          <name>LOG_FIELD</name>
        </field>
        <field>
          <id>EXECUTING_SERVER</id>
          <enabled>N</enabled>
          <name>EXECUTING_SERVER</name>
        </field>
        <field>
          <id>EXECUTING_USER</id>
          <enabled>N</enabled>
          <name>EXECUTING_USER</name>
        </field>
        <field>
          <id>CLIENT</id>
          <enabled>N</enabled>
          <name>CLIENT</name>
        </field>
      </trans-log-table>
      <perf-log-table>
        <connection/>
        <schema/>
        <table/>
        <interval/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>SEQ_NR</id>
          <enabled>Y</enabled>
          <name>SEQ_NR</name>
        </field>
        <field>
          <id>LOGDATE</id>
          <enabled>Y</enabled>
          <name>LOGDATE</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STEPNAME</id>
          <enabled>Y</enabled>
          <name>STEPNAME</name>
        </field>
        <field>
          <id>STEP_COPY</id>
          <enabled>Y</enabled>
          <name>STEP_COPY</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>INPUT_BUFFER_ROWS</id>
          <enabled>Y</enabled>
          <name>INPUT_BUFFER_ROWS</name>
        </field>
        <field>
          <id>OUTPUT_BUFFER_ROWS</id>
          <enabled>Y</enabled>
          <name>OUTPUT_BUFFER_ROWS</name>
        </field>
      </perf-log-table>
      <channel-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>LOGGING_OBJECT_TYPE</id>
          <enabled>Y</enabled>
          <name>LOGGING_OBJECT_TYPE</name>
        </field>
        <field>
          <id>OBJECT_NAME</id>
          <enabled>Y</enabled>
          <name>OBJECT_NAME</name>
        </field>
        <field>
          <id>OBJECT_COPY</id>
          <enabled>Y</enabled>
          <name>OBJECT_COPY</name>
        </field>
        <field>
          <id>REPOSITORY_DIRECTORY</id>
          <enabled>Y</enabled>
          <name>REPOSITORY_DIRECTORY</name>
        </field>
        <field>
          <id>FILENAME</id>
          <enabled>Y</enabled>
          <name>FILENAME</name>
        </field>
        <field>
          <id>OBJECT_ID</id>
          <enabled>Y</enabled>
          <name>OBJECT_ID</name>
        </field>
        <field>
          <id>OBJECT_REVISION</id>
          <enabled>Y</enabled>
          <name>OBJECT_REVISION</name>
        </field>
        <field>
          <id>PARENT_CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>PARENT_CHANNEL_ID</name>
        </field>
        <field>
          <id>ROOT_CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>ROOT_CHANNEL_ID</name>
        </field>
      </channel-log-table>
      <step-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>TRANSNAME</id>
          <enabled>Y</enabled>
          <name>TRANSNAME</name>
        </field>
        <field>
          <id>STEPNAME</id>
          <enabled>Y</enabled>
          <name>STEPNAME</name>
        </field>
        <field>
          <id>STEP_COPY</id>
          <enabled>Y</enabled>
          <name>STEP_COPY</name>
        </field>
        <field>
          <id>LINES_READ</id>
          <enabled>Y</enabled>
          <name>LINES_READ</name>
        </field>
        <field>
          <id>LINES_WRITTEN</id>
          <enabled>Y</enabled>
          <name>LINES_WRITTEN</name>
        </field>
        <field>
          <id>LINES_UPDATED</id>
          <enabled>Y</enabled>
          <name>LINES_UPDATED</name>
        </field>
        <field>
          <id>LINES_INPUT</id>
          <enabled>Y</enabled>
          <name>LINES_INPUT</name>
        </field>
        <field>
          <id>LINES_OUTPUT</id>
          <enabled>Y</enabled>
          <name>LINES_OUTPUT</name>
        </field>
        <field>
          <id>LINES_REJECTED</id>
          <enabled>Y</enabled>
          <name>LINES_REJECTED</name>
        </field>
        <field>
          <id>ERRORS</id>
          <enabled>Y</enabled>
          <name>ERRORS</name>
        </field>
        <field>
          <id>LOG_FIELD</id>
          <enabled>N</enabled>
          <name>LOG_FIELD</name>
        </field>
      </step-log-table>
      <metrics-log-table>
        <connection/>
        <schema/>
        <table/>
        <timeout_days/>
        <field>
          <id>ID_BATCH</id>
          <enabled>Y</enabled>
          <name>ID_BATCH</name>
        </field>
        <field>
          <id>CHANNEL_ID</id>
          <enabled>Y</enabled>
          <name>CHANNEL_ID</name>
        </field>
        <field>
          <id>LOG_DATE</id>
          <enabled>Y</enabled>
          <name>LOG_DATE</name>
        </field>
        <field>
          <id>METRICS_DATE</id>
          <enabled>Y</enabled>
          <name>METRICS_DATE</name>
        </field>
        <field>
          <id>METRICS_CODE</id>
          <enabled>Y</enabled>
          <name>METRICS_CODE</name>
        </field>
        <field>
          <id>METRICS_DESCRIPTION</id>
          <enabled>Y</enabled>
          <name>METRICS_DESCRIPTION</name>
        </field>
        <field>
          <id>METRICS_SUBJECT</id>
          <enabled>Y</enabled>
          <name>METRICS_SUBJECT</name>
        </field>
        <field>
          <id>METRICS_TYPE</id>
          <enabled>Y</enabled>
          <name>METRICS_TYPE</name>
        </field>
        <field>
          <id>METRICS_VALUE</id>
          <enabled>Y</enabled>
          <name>METRICS_VALUE</name>
        </field>
      </metrics-log-table>
    </log>
    <maxdate>
      <connection/>
      <table/>
      <field/>
      <offset>0.0</offset>
      <maxdiff>0.0</maxdiff>
    </maxdate>
    <size_rowset>10000</size_rowset>
    <sleep_time_empty>50</sleep_time_empty>
    <sleep_time_full>50</sleep_time_full>
    <unique_connections>N</unique_connections>
    <feedback_shown>Y</feedback_shown>
    <feedback_size>50000</feedback_size>
    <using_thread_priorities>Y</using_thread_priorities>
    <shared_objects_file/>
    <capture_step_performance>N</capture_step_performance>
    <step_performance_capturing_delay>1000</step_performance_capturing_delay>
    <step_performance_capturing_size_limit>100</step_performance_capturing_size_limit>
    <dependencies>
    </dependencies>
    <partitionschemas>
    </partitionschemas>
    <slaveservers>
    </slaveservers>
    <clusterschemas>
    </clusterschemas>
    <created_user>-</created_user>
    <created_date>2023/04/26 12:04:14.640</created_date>
    <modified_user>-</modified_user>
    <modified_date>2023/04/26 12:04:14.640</modified_date>
    <key_for_session_key>H4sIAAAAAAAA/wMAAAAAAAAAAAA=</key_for_session_key>
    <is_key_private>N</is_key_private>
  </info>
  <notepads>
  </notepads>
  <connection>
    <name>prod_read</name>
    <server>${host_name}</server>
    <type>MYSQL</type>
    <access>Native</access>
    <database>${db_name}</database>
    <port>${port_number}</port>
    <username>${user_name}</username>
    <password>${password}</password>
    <servername/>
    <data_tablespace/>
    <index_tablespace/>
    <attributes>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.defaultFetchSize</code>
        <attribute>2000</attribute>
      </attribute>
      <attribute>
        <code>EXTRA_OPTION_MYSQL.useCursorFetch</code>
        <attribute>true</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_LOWERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>FORCE_IDENTIFIERS_TO_UPPERCASE</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>IS_CLUSTERED</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>PORT_NUMBER</code>
        <attribute>${port_number}</attribute>
      </attribute>
      <attribute>
        <code>PRESERVE_RESERVED_WORD_CASE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>QUOTE_ALL_FIELDS</code>
        <attribute>N</attribute>
      </attribute>
      <attribute>
        <code>STREAM_RESULTS</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_BOOLEAN_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>SUPPORTS_TIMESTAMP_DATA_TYPE</code>
        <attribute>Y</attribute>
      </attribute>
      <attribute>
        <code>USE_POOLING</code>
        <attribute>N</attribute>
      </attribute>
    </attributes>
  </connection>
  <order>
    <hop>
      <from>settlement_report</from>
      <to>Text file output</to>
      <enabled>Y</enabled>
    </hop>
  </order>
  <step>
    <name>Text file output</name>
    <type>TextFileOutput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <separator>,</separator>
    <enclosure>"</enclosure>
    <enclosure_forced>Y</enclosure_forced>
    <enclosure_fix_disabled>N</enclosure_fix_disabled>
    <header>Y</header>
    <footer>N</footer>
    <format>DOS</format>
    <compression>None</compression>
    <encoding/>
    <endedLine/>
    <fileNameInField>N</fileNameInField>
    <fileNameField/>
    <create_parent_folder>Y</create_parent_folder>
    <file>
      <name>${base_path}/settlement_report</name>
      <servlet_output>N</servlet_output>
      <do_not_open_new_file_init>N</do_not_open_new_file_init>
      <extention>csv</extention>
      <append>N</append>
      <split>N</split>
      <haspartno>N</haspartno>
      <add_date>Y</add_date>
      <add_time>Y</add_time>
      <SpecifyFormat>N</SpecifyFormat>
      <date_time_format/>
      <add_to_result_filenames>Y</add_to_result_filenames>
      <pad>N</pad>
      <fast_dump>Y</fast_dump>
      <splitevery/>
    </file>
    <fields>
    </fields>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>528</xloc>
      <yloc>320</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step>
    <name>settlement_report</name>
    <type>TableInput</type>
    <description/>
    <distribute>Y</distribute>
    <custom_distribution/>
    <copies>1</copies>
    <partitioning>
      <method>none</method>
      <schema_name/>
    </partitioning>
    <connection>prod_read</connection>
    <sql>SELECT 
    t2.*,
    t1.TAXABLE_AMOUNT,
    t1.TOTAL_TAX,
    t1.CGST,
    t1.SGST_UTGST,
    t1.OTHERS,
    t1.TWO_POINT_FIVE_PERCENT,
    t1.SIX_PERCENT,
    t1.NINE_PERCENT,
    t1.SUM_OF_TAX_BY_SPLIT,
    t1.TAX_DIFF,
    t1.TAXABLE_FOR_TWO_POINT_FIVE_PERCENT,
    t1.TAXABLE_FOR_SIX_PERCENT,
    t1.TAXABLE_FOR_NINE_PERCENT,
    t1.SUM_OF_TAXABLE_BY_SPLIT,
    t1.ZERO_TAX_AMOUNT,
    COALESCE(t3.WALLET_AMOUNT, 0) WALLET_AMOUNT,
    r1.ROUND_OFF_AMOUNT,
    t4.TAX_PAID_BY_PARTNER,
    t4.TAX_PAID_BY_INERNAL
FROM
    (SELECT 
			a.BIZ_DATE BIZ_DATE,
            a.UNIT_ID,
            SUM(a.TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(a.TOTAL_TAX) TOTAL_TAX,
            SUM(a.CGST) CGST,
            SUM(a.SGST_UTGST) SGST_UTGST,
            SUM(a.OTHERS) OTHERS,
            SUM(a.TWO_POINT_FIVE) 'TWO_POINT_FIVE_PERCENT',
            SUM(a.SIX) 'SIX_PERCENT',
            SUM(a.NINE) 'NINE_PERCENT',
            SUM(a.TWO_POINT_FIVE) + SUM(a.SIX) + SUM(a.NINE) SUM_OF_TAX_BY_SPLIT,
            SUM(a.TOTAL_TAX) - SUM(a.TWO_POINT_FIVE) - SUM(a.SIX) - SUM(a.NINE) TAX_DIFF,
            SUM(a.TWO_POINT_FIVE_TAXABLE_AMOUNT) 'TAXABLE_FOR_TWO_POINT_FIVE_PERCENT',
            SUM(a.SIX_TAXABLE_AMOUNT) 'TAXABLE_FOR_SIX_PERCENT',
            SUM(a.NINE_TAXABLE_AMOUNT) 'TAXABLE_FOR_NINE_PERCENT',
            SUM(a.TWO_POINT_FIVE_TAXABLE_AMOUNT) + SUM(a.SIX_TAXABLE_AMOUNT) + SUM(a.NINE_TAXABLE_AMOUNT) SUM_OF_TAXABLE_BY_SPLIT,
            SUM(a.TAXABLE_AMOUNT) - SUM(a.TWO_POINT_FIVE_TAXABLE_AMOUNT) - SUM(a.SIX_TAXABLE_AMOUNT) - SUM(a.NINE_TAXABLE_AMOUNT) ZERO_TAX_AMOUNT
    FROM
        (SELECT 
        od.ORDER_ID,
			case when od.BUSINESS_DATE IS NULL THEN DATE(od.BILLING_SERVER_TIME) ELSE od.BUSINESS_DATE END BIZ_DATE,
			od.UNIT_ID,
            MAX(od.TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            MAX(od.TOTAL_TAX) TOTAL_TAX,
            SUM(CASE
                WHEN otd.TAX_CODE = 'CGST' THEN otd.TOTAL_TAX
                ELSE 0
            END) 'CGST',
            SUM(CASE
                WHEN otd.TAX_CODE = 'SGST/UTGST' THEN otd.TOTAL_TAX
                ELSE 0
            END) 'SGST_UTGST',
            SUM(CASE
                WHEN
                    otd.TAX_CODE != 'SGST/UTGST'
                        &amp;&amp; otd.TAX_CODE != 'CGST'
                THEN
                    otd.TOTAL_TAX
                ELSE 0
            END) 'OTHERS',
            SUM(CASE
                WHEN otd.TAX_PERCENTAGE = '2.5' THEN otd.TOTAL_TAX
                ELSE 0
            END) 'TWO_POINT_FIVE',
            SUM(CASE
                WHEN otd.TAX_PERCENTAGE = '6.0' THEN otd.TOTAL_TAX
                ELSE 0
            END) 'SIX',
            SUM(CASE
                WHEN otd.TAX_PERCENTAGE = '9.0' THEN otd.TOTAL_TAX
                ELSE 0
            END) 'NINE',
            SUM(CASE
                WHEN otd.TAX_PERCENTAGE = '2.5' THEN otd.TAXABLE_AMOUNT / 2
                ELSE 0
            END) 'TWO_POINT_FIVE_TAXABLE_AMOUNT',
            SUM(CASE
                WHEN otd.TAX_PERCENTAGE = '6.0' THEN otd.TAXABLE_AMOUNT / 2
                ELSE 0
            END) 'SIX_TAXABLE_AMOUNT',
            SUM(CASE
                WHEN otd.TAX_PERCENTAGE = '9.0' THEN otd.TAXABLE_AMOUNT / 2
                ELSE 0
            END) 'NINE_TAXABLE_AMOUNT'
    FROM
        KETTLE.ORDER_DETAIL od
    LEFT JOIN KETTLE.ORDER_TAX_DETAIL otd ON otd.ORDER_ID = od.ORDER_ID
    WHERE
        (( od.BUSINESS_DATE IS NOT NULL AND od.BUSINESS_DATE BETWEEN  ${start_date}  AND   ${end_date} ) OR (od.BUSINESS_DATE IS NULL AND od.UNIT_ID = 26254 AND BILLING_SERVER_TIME BETWEEN  ${start_date}  AND DATE_ADD(  ${end_date} , INTERVAL 1 DAY)))
            AND od.ORDER_STATUS &lt;&gt; 'CANCELLED'
            AND ORDER_TYPE IN ('order' , 'paid-employee-meal')
    GROUP BY od.ORDER_ID, BIZ_DATE ) a
    GROUP BY a.UNIT_ID , a.BIZ_DATE) t1
        LEFT JOIN
        (SELECT 
			case when od.BUSINESS_DATE IS NULL THEN DATE(od.BILLING_SERVER_TIME) ELSE od.BUSINESS_DATE END BIZ_DATE,
            od.UNIT_ID,
            SUM(od.ROUND_OFF_AMOUNT) ROUND_OFF_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
        WHERE
        (( od.BUSINESS_DATE IS NOT NULL AND od.BUSINESS_DATE BETWEEN  ${start_date}  AND   ${end_date} ) OR (od.BUSINESS_DATE IS NULL AND od.UNIT_ID = 26254 AND BILLING_SERVER_TIME BETWEEN  ${start_date}  AND DATE_ADD(  ${end_date} , INTERVAL 1 DAY)))
            AND od.ORDER_STATUS &lt;&gt; 'CANCELLED'
            AND ORDER_TYPE IN ('order' , 'paid-employee-meal')
    GROUP BY od.UNIT_ID , BIZ_DATE) r1
         on t1.UNIT_ID = r1.UNIT_ID
        AND t1.BIZ_DATE = r1.BIZ_DATE

        LEFT JOIN
    (SELECT 
			case when od.BUSINESS_DATE IS NULL THEN DATE(od.BILLING_SERVER_TIME) ELSE od.BUSINESS_DATE END BIZ_DATE,
            ud.UNIT_ID,
            ud.UNIT_NAME,
            sd.STATE,
            COALESCE(SUM(os.AMOUNT_PAID + os.EXTRA_VOUCHERS), 0) 'Total Sales',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Cash' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Cash',
            SUM(CASE
                WHEN pm.MODE_NAME = 'CreditDebitCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Visa/Master Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'AMEX' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'AMEX Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Sodexo' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Sodexo Coupon',
            SUM(CASE
                WHEN pm.MODE_NAME = 'TicketRestaurant' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Ticket Restaurant Coupon',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Credit' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Credit',
            SUM(CASE
                WHEN pm.MODE_NAME = 'SodexoCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Sodexo Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'TicketRestaurantCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Ticket Restaurant Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'GiftCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Wallet',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Paytm' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Paytm',
            SUM(CASE
                WHEN pm.MODE_NAME = 'RazorPay' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'RazorPay',
            SUM(CASE
                WHEN pm.MODE_NAME = 'PayTmOnline' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'PayTmOnline',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Mobikwik' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Mobikwik',
            SUM(CASE
                WHEN pm.MODE_NAME = 'FreeCharge' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'FreeCharge',
            SUM(CASE
                WHEN pm.MODE_NAME = 'DineOut' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'DineOut',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Prepaid' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Prepaid',
            SUM(CASE
                WHEN pm.MODE_NAME = 'PhonePe' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'PhonePe',
            SUM(CASE
                WHEN pm.MODE_NAME = 'GYFTR' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'GYFTR',
            SUM(CASE
                WHEN pm.MODE_NAME = 'DineInCredit' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'DineInCredit',
            SUM(CASE
                WHEN pm.MODE_NAME = 'HopQ' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'HopQ',
            SUM(CASE
                WHEN pm.MODE_NAME = 'PayTmKioskOnline' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'PayTmKioskOnline',
            SUM(CASE
                WHEN pm.MODE_NAME = 'EzetapKioskOnline' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'EzetapKioskOnline',
            SUM(CASE
                WHEN pm.MODE_NAME = 'IngenicoOnline' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'IngenicoOnline',
            SUM(CASE
                WHEN pm.MODE_NAME = 'GPay' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'GPay',
            SUM(CASE
                WHEN pm.MODE_NAME = 'OlaMoneyQR' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'OlaMoneyQR',
            
            SUM(CASE
                WHEN pm.MODE_NAME = 'EasyDiner' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'EasyDiner',
           SUM(CASE
                WHEN pm.MODE_NAME = 'Maginpin' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Maginpin',
           SUM(CASE
                WHEN pm.MODE_NAME = 'Employee Meal' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Employee Meal',
           SUM(CASE
                WHEN pm.MODE_NAME = 'BookMyShow' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Book My Show',
           SUM(CASE
                WHEN pm.MODE_NAME = 'PAYTM DEALS' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'PAYTM DEALS',
           SUM(CASE
                WHEN pm.MODE_NAME = 'PAYTM EDC DEBIT/CREDIT' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'PAYTM EDC DEBIT/CREDIT',
           SUM(CASE
                WHEN pm.MODE_NAME = 'PAYTM EDC AMEX' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'PAYTM EDC AMEX',
           SUM(CASE
                WHEN pm.MODE_NAME = 'PAYTM EDC UPI' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'PAYTM EDC UPI',
            SUM(CASE
                WHEN pm.MODE_NAME = 'PAYPHI EDC' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'PAYPHI EDC',
            SUM(CASE
                WHEN pm.MODE_NAME = 'PAYTM DQR UPI' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'PAYTM DQR UPI',
            SUM(CASE
                WHEN 
                    pm.MODE_NAME = 'Credit'
                        AND od.CHANNEL_PARTNER_ID = 1
                THEN
                    os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'CHAAYOS',
            SUM(CASE
                WHEN
                    pm.MODE_NAME = 'Credit'
                        AND od.CHANNEL_PARTNER_ID = 2
                THEN
                    os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'CHAAYOS_DELIVERY',
            SUM(CASE
                WHEN
                    pm.MODE_NAME = 'Credit'
                        AND od.CHANNEL_PARTNER_ID = 3
                THEN
                    os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'ZOMATO',
            SUM(CASE
                WHEN
                    pm.MODE_NAME = 'Credit'
                        AND od.CHANNEL_PARTNER_ID = 4
                THEN
                    os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'TINYOWL',
            SUM(CASE
                WHEN
                    pm.MODE_NAME = 'Credit'
                        AND od.CHANNEL_PARTNER_ID = 5
                THEN
                    os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'FOODPANDA',
            SUM(CASE
                WHEN
                    pm.MODE_NAME = 'Credit'
                        AND od.CHANNEL_PARTNER_ID = 6
                THEN
                    os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'SWIGGY',
            SUM(CASE
                WHEN
                    pm.MODE_NAME = 'Credit'
                        AND od.CHANNEL_PARTNER_ID = 7
                THEN
                    os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'OLACAFE',
            SUM(CASE
                WHEN
                    pm.MODE_NAME = 'Credit'
                        AND od.CHANNEL_PARTNER_ID = 8
                THEN
                    os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'GROUPON',
            SUM(CASE
                WHEN
                    pm.MODE_NAME = 'Credit'
                        AND od.CHANNEL_PARTNER_ID = 9
                THEN
                    os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'CHAAYOS_TAKE_AWAY',
            SUM(CASE
                WHEN
                    pm.MODE_NAME = 'Credit'
                        AND od.CHANNEL_PARTNER_ID = 11
                THEN
                    os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'GROFERS',
            SUM(CASE
                WHEN
                    pm.MODE_NAME = 'Credit'
                        AND od.CHANNEL_PARTNER_ID = 12
                THEN
                    os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'RUNNR',
            SUM(CASE
                WHEN
                    pm.MODE_NAME = 'Credit'
                        AND od.CHANNEL_PARTNER_ID = 13
                THEN
                    os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'SCOOTSY',
            SUM(CASE
                WHEN
                    pm.MODE_NAME = 'Credit'
                        AND od.CHANNEL_PARTNER_ID = 14
                THEN
                    os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'CHAAYOS_WEB',
            SUM(CASE
                WHEN
                    pm.MODE_NAME = 'Credit'
                        AND od.CHANNEL_PARTNER_ID = 15
                THEN
                    os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'UBER_EATS',
            SUM(CASE
                WHEN
                    pm.MODE_NAME = 'Credit'
                        AND od.CHANNEL_PARTNER_ID = 16
                THEN
                    os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'BIGBASKET',
            SUM(CASE
                WHEN
                    pm.MODE_NAME = 'Credit'
                        AND od.CHANNEL_PARTNER_ID = 17
                THEN
                    os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'DUNZO',
            SUM(CASE
                WHEN
                    pm.MODE_NAME = 'Credit'
                        AND od.CHANNEL_PARTNER_ID = 18
                THEN
                    os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'AMAZON_PRIME_FOOD',
            SUM(CASE
                WHEN
                    pm.MODE_NAME = 'Credit'
                        AND od.CHANNEL_PARTNER_ID = 19
                THEN
                    os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'SWIGGY_STORE',
            SUM(CASE
                WHEN
                    pm.MODE_NAME = 'Credit'
                        AND od.CHANNEL_PARTNER_ID = 20
                THEN
                    os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'ZOMATO MARKET',
            SUM(CASE
                WHEN
                    pm.MODE_NAME = 'Credit'
                        AND od.CHANNEL_PARTNER_ID = 21
                THEN
                    os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'DINE_IN_APP',SUM(CASE
                WHEN
                    pm.MODE_NAME = 'Credit'
                        AND od.CHANNEL_PARTNER_ID = 22
                THEN
                    os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'EAZYDINER',
			SUM(CASE
                WHEN
                    pm.MODE_NAME = 'Credit'
                        AND od.CHANNEL_PARTNER_ID = 23
                THEN
                    os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'BAZAAR',
			SUM(CASE
                WHEN
                    pm.MODE_NAME = 'Credit'
                        AND od.CHANNEL_PARTNER_ID = 24
                THEN
                    os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'MAGICPIN',
            SUM(CASE
                WHEN
                    pm.MODE_NAME = 'Credit'
                        AND od.CHANNEL_PARTNER_ID BETWEEN 1 AND 21
                THEN
                    os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'CREDIT_SUM'
    FROM
        KETTLE.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    LEFT JOIN KETTLE_MASTER.LOCATION_DETAIL ld ON ld.LOCATION_ID = ud.LOCATION_DETAIL_ID
    LEFT JOIN KETTLE_MASTER.STATE_DETAIL sd ON ld.STATE_DETAIL_ID = sd.STATE_DETAIL_ID
    LEFT JOIN KETTLE.ORDER_SETTLEMENT os ON od.ORDER_ID = os.ORDER_ID
    LEFT JOIN KETTLE_MASTER.PAYMENT_MODE pm ON os.PAYMENT_MODE_ID = pm.PAYMENT_MODE_ID
    WHERE
        (( od.BUSINESS_DATE IS NOT NULL AND od.BUSINESS_DATE BETWEEN  ${start_date}  AND   ${end_date} ) OR (od.BUSINESS_DATE IS NULL AND od.UNIT_ID = 26254 AND BILLING_SERVER_TIME BETWEEN  ${start_date}  AND DATE_ADD(  ${end_date} , INTERVAL 1 DAY)))
            AND od.ORDER_STATUS &lt;&gt; 'CANCELLED'
            AND ORDER_TYPE IN ('order' , 'paid-employee-meal')
    GROUP BY ud.UNIT_ID , BIZ_DATE) t2 ON t1.UNIT_ID = t2.UNIT_ID
        AND t1.BIZ_DATE = t2.BIZ_DATE
        LEFT JOIN
    (SELECT 
			case when od.BUSINESS_DATE IS NULL THEN DATE(od.BILLING_SERVER_TIME) ELSE od.BUSINESS_DATE END BIZ_DATE,
            od.UNIT_ID,
            SUM(oi.TOTAL_AMOUNT) WALLET_AMOUNT
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        (( od.BUSINESS_DATE IS NOT NULL AND od.BUSINESS_DATE BETWEEN  ${start_date}  AND   ${end_date} ) OR (od.BUSINESS_DATE IS NULL AND od.UNIT_ID = 26254 AND BILLING_SERVER_TIME BETWEEN  ${start_date}  AND DATE_ADD(  ${end_date} , INTERVAL 1 DAY)))
            AND od.ORDER_STATUS &lt;&gt; 'CANCELLED'
            AND od.ORDER_TYPE IN ('order' , 'paid-employee-meal')
            AND oi.TAX_CODE IN ('GIFT_CARD')
    GROUP BY od.UNIT_ID , BIZ_DATE) t3 ON t1.UNIT_ID = t3.UNIT_ID
        AND t1.BIZ_DATE = t3.BIZ_DATE
             LEFT JOIN
    (SELECT 
			case when od.BUSINESS_DATE IS NULL THEN DATE(od.BILLING_SERVER_TIME) ELSE od.BUSINESS_DATE END BIZ_DATE,
            od.UNIT_ID,
            SUM(CASE WHEN TAX_DEDUCTED_BY_PARTNER = 'Y' THEN oi.TOTAL_TAX ELSE 0 END) TAX_PAID_BY_PARTNER, 
            SUM(CASE WHEN TAX_DEDUCTED_BY_PARTNER &lt;&gt; 'Y' THEN oi.TOTAL_TAX  END) TAX_PAID_BY_INERNAL
    FROM
        KETTLE.ORDER_DETAIL od
    INNER JOIN KETTLE.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        (( od.BUSINESS_DATE IS NOT NULL AND od.BUSINESS_DATE BETWEEN  ${start_date}  AND   ${end_date} ) OR (od.BUSINESS_DATE IS NULL AND od.UNIT_ID = 26254 AND BILLING_SERVER_TIME BETWEEN  ${start_date}  AND DATE_ADD(  ${end_date} , INTERVAL 1 DAY)))
            AND od.ORDER_STATUS &lt;&gt; 'CANCELLED'
            AND od.ORDER_TYPE IN ('order' , 'paid-employee-meal')
    GROUP BY od.UNIT_ID , BIZ_DATE) t4 ON t1.UNIT_ID = t4.UNIT_ID
        AND t1.BIZ_DATE = t4.BIZ_DATE
        ; </sql>
    <limit>0</limit>
    <lookup/>
    <execute_each_row>N</execute_each_row>
    <variables_active>Y</variables_active>
    <lazy_conversion_active>N</lazy_conversion_active>
    <cached_row_meta_active>N</cached_row_meta_active>
    <row-meta>
      <value-meta>
        <type>Date</type>
        <storagetype>normal</storagetype>
        <name>BIZ_DATE</name>
        <length>-1</length>
        <precision>-1</precision>
        <origin>settlement_report</origin>
        <comments>BIZ_DATE</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>Integer</type>
        <storagetype>normal</storagetype>
        <name>UNIT_ID</name>
        <length>9</length>
        <precision>0</precision>
        <origin>settlement_report</origin>
        <comments>UNIT_ID</comments>
        <conversion_Mask>####0;-####0</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>UNIT_NAME</name>
        <length>255</length>
        <precision>-1</precision>
        <origin>settlement_report</origin>
        <comments>UNIT_NAME</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>String</type>
        <storagetype>normal</storagetype>
        <name>STATE</name>
        <length>100</length>
        <precision>-1</precision>
        <origin>settlement_report</origin>
        <comments>STATE</comments>
        <conversion_Mask/>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol>,</grouping_symbol>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>Total Sales</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>Total Sales</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>Cash</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>Cash</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>Visa/Master Card</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>Visa/Master Card</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>AMEX Card</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>AMEX Card</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>Sodexo Coupon</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>Sodexo Coupon</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>Ticket Restaurant Coupon</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>Ticket Restaurant Coupon</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>Credit</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>Credit</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>Sodexo Card</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>Sodexo Card</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>Ticket Restaurant Card</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>Ticket Restaurant Card</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>Wallet</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>Wallet</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>Paytm</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>Paytm</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>RazorPay</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>RazorPay</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>PayTmOnline</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>PayTmOnline</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>Mobikwik</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>Mobikwik</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>FreeCharge</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>FreeCharge</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>DineOut</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>DineOut</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>Prepaid</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>Prepaid</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>PhonePe</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>PhonePe</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>GYFTR</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>GYFTR</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>DineInCredit</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>DineInCredit</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>HopQ</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>HopQ</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>PayTmKioskOnline</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>PayTmKioskOnline</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>EzetapKioskOnline</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>EzetapKioskOnline</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>IngenicoOnline</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>IngenicoOnline</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>GPay</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>GPay</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>OlaMoneyQR</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>OlaMoneyQR</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>EasyDiner</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>EasyDiner</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>Maginpin</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>Maginpin</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>Employee Meal</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>Employee Meal</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>CHAAYOS</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>CHAAYOS</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>CHAAYOS_DELIVERY</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>CHAAYOS_DELIVERY</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>ZOMATO</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>ZOMATO</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>TINYOWL</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>TINYOWL</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>FOODPANDA</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>FOODPANDA</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>SWIGGY</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>SWIGGY</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>OLACAFE</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>OLACAFE</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>GROUPON</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>GROUPON</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>CHAAYOS_TAKE_AWAY</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>CHAAYOS_TAKE_AWAY</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>GROFERS</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>GROFERS</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>RUNNR</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>RUNNR</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>SCOOTSY</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>SCOOTSY</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>CHAAYOS_WEB</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>CHAAYOS_WEB</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>UBER_EATS</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>UBER_EATS</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>BIGBASKET</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>BIGBASKET</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>DUNZO</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>DUNZO</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>AMAZON_PRIME_FOOD</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>AMAZON_PRIME_FOOD</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>SWIGGY_STORE</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>SWIGGY_STORE</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>ZOMATO MARKET</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>ZOMATO MARKET</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>DINE_IN_APP</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>DINE_IN_APP</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>EAZYDINER</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>EAZYDINER</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>BAZAAR</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>BAZAAR</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>MAGICPIN</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>MAGICPIN</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>CREDIT_SUM</name>
        <length>33</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>CREDIT_SUM</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>TAXABLE_AMOUNT</name>
        <length>32</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>TAXABLE_AMOUNT</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>TOTAL_TAX</name>
        <length>32</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>TOTAL_TAX</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>CGST</name>
        <length>54</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>CGST</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>SGST_UTGST</name>
        <length>54</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>SGST_UTGST</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>OTHERS</name>
        <length>54</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>OTHERS</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>TWO_POINT_FIVE_PERCENT</name>
        <length>54</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>TWO_POINT_FIVE_PERCENT</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>SIX_PERCENT</name>
        <length>54</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>SIX_PERCENT</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>NINE_PERCENT</name>
        <length>54</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>NINE_PERCENT</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>SUM_OF_TAX_BY_SPLIT</name>
        <length>56</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>SUM_OF_TAX_BY_SPLIT</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>TAX_DIFF</name>
        <length>57</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>TAX_DIFF</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>TAXABLE_FOR_TWO_POINT_FIVE_PERCENT</name>
        <length>58</length>
        <precision>6</precision>
        <origin>settlement_report</origin>
        <comments>TAXABLE_FOR_TWO_POINT_FIVE_PERCENT</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>TAXABLE_FOR_SIX_PERCENT</name>
        <length>58</length>
        <precision>6</precision>
        <origin>settlement_report</origin>
        <comments>TAXABLE_FOR_SIX_PERCENT</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>TAXABLE_FOR_NINE_PERCENT</name>
        <length>58</length>
        <precision>6</precision>
        <origin>settlement_report</origin>
        <comments>TAXABLE_FOR_NINE_PERCENT</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>SUM_OF_TAXABLE_BY_SPLIT</name>
        <length>60</length>
        <precision>6</precision>
        <origin>settlement_report</origin>
        <comments>SUM_OF_TAXABLE_BY_SPLIT</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>ZERO_TAX_AMOUNT</name>
        <length>61</length>
        <precision>6</precision>
        <origin>settlement_report</origin>
        <comments>ZERO_TAX_AMOUNT</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>WALLET_AMOUNT</name>
        <length>32</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>WALLET_AMOUNT</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
      <value-meta>
        <type>BigNumber</type>
        <storagetype>normal</storagetype>
        <name>ROUND_OFF_AMOUNT</name>
        <length>32</length>
        <precision>2</precision>
        <origin>settlement_report</origin>
        <comments>ROUND_OFF_AMOUNT</comments>
        <conversion_Mask>######0.0###################;-######0.0###################</conversion_Mask>
        <decimal_symbol>.</decimal_symbol>
        <grouping_symbol/>
        <currency_symbol/>
        <trim_type>none</trim_type>
        <case_insensitive>N</case_insensitive>
        <collator_disabled>Y</collator_disabled>
        <collator_strength>0</collator_strength>
        <sort_descending>N</sort_descending>
        <output_padding>N</output_padding>
        <date_format_lenient>N</date_format_lenient>
        <date_format_locale>en_US</date_format_locale>
        <date_format_timezone>Asia/Calcutta</date_format_timezone>
        <lenient_string_to_number>N</lenient_string_to_number>
      </value-meta>
    </row-meta>
    <attributes/>
    <cluster_schema/>
    <remotesteps>
      <input>
      </input>
      <output>
      </output>
    </remotesteps>
    <GUI>
      <xloc>288</xloc>
      <yloc>320</yloc>
      <draw>Y</draw>
    </GUI>
  </step>
  <step_error_handling>
  </step_error_handling>
  <slave-step-copy-partition-distribution>
  </slave-step-copy-partition-distribution>
  <slave_transformation>N</slave_transformation>
  <attributes/>
</transformation>
