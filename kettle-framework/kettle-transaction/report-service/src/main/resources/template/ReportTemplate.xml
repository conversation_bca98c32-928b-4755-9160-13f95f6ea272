<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<ReportCategories xmlns="http://www.w3schools.com">
	<categories>
		<category name="Report Category 1" type="Automated"
			accessCode="Automated" id="1" fromEmail="<EMAIL>"
			toEmails="<EMAIL>" schedule="">
			<reports>
				<report id="1" name="Report Category 1 - Report 1"
					executionType="SQL" returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[

SELECT 
    *
FROM
    (SELECT 
	emp.EMP_NAME,
        emp.EMPLOYEE_CODE,
        emp.EMP_ID,
        emp.EMP_CONTACT_NUM_1 CONTACT,
        od.BILLING_SERVER_TIME BILLING_TIME,
        od.EMP_ID ORDER_PUNCHED_BY,
        oi.PRODUCT_NAME,
        oi.AMOUNT_PAID + oi.TOTAL_TAX AMOUNT
    FROM
        KETTLE_DUMP.EMPLOYEE_MEAL_ALLOWANCE_DATA a
    LEFT JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = a.ORDER_ID
    LEFT JOIN KETTLE_DUMP.ORDER_ITEM oi on od.ORDER_ID = oi.ORDER_ID
    LEFT JOIN KETTLE_MASTER_DUMP.EMPLOYEE_DETAIL emp ON emp.EMP_ID = a.EMPLOYEE_ID
    LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.BUSINESS_DATE IS NOT NULL AND od.BUSINESS_DATE BETWEEN :startDate AND :endDate) a
				     ]]>
					</content>
					<params>
						<param name="startDate" displayName="Start Date"
							dataType="DATE" format="yyyy-MM-dd" />
						<param name="endDate" displayName="End Date" dataType="DATE"
							format="yyyy-MM-dd" />
					</params>

				</report>
				<report id="2" name="Report Category 1 - Report 2"
					executionType="SQL" returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[

SELECT 
    *
FROM
    (SELECT 
	emp.EMP_NAME,
        emp.EMPLOYEE_CODE,
        emp.EMP_ID,
        emp.EMP_CONTACT_NUM_1 CONTACT,
        od.BILLING_SERVER_TIME BILLING_TIME,
        od.EMP_ID ORDER_PUNCHED_BY,
        oi.PRODUCT_NAME,
        oi.AMOUNT_PAID + oi.TOTAL_TAX AMOUNT
    FROM
        KETTLE_DUMP.EMPLOYEE_MEAL_ALLOWANCE_DATA a
    LEFT JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = a.ORDER_ID
    LEFT JOIN KETTLE_DUMP.ORDER_ITEM oi on od.ORDER_ID = oi.ORDER_ID
    LEFT JOIN KETTLE_MASTER_DUMP.EMPLOYEE_DETAIL emp ON emp.EMP_ID = a.EMPLOYEE_ID
    LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.BUSINESS_DATE IS NOT NULL AND od.BUSINESS_DATE BETWEEN :startDate AND :endDate) a
				     ]]>
					</content>
					<params>
						<param name="startDate" displayName="Start Date"
							dataType="DATE" format="yyyy-MM-dd" />
						<param name="endDate" displayName="End Date" dataType="DATE"
							format="yyyy-MM-dd" />
					</params>

				</report>
			</reports>
		</category>
		<category name="Report Category 2" type="Automated"
			accessCode="Automated" id="1" fromEmail="<EMAIL>"
			toEmails="<EMAIL>" schedule="">
			<reports>
				<report id="1" name="Report Category 2 - Report 1"
					executionType="SQL" returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[

SELECT 
    *
FROM
    (SELECT 
	emp.EMP_NAME,
        emp.EMPLOYEE_CODE,
        emp.EMP_ID,
        emp.EMP_CONTACT_NUM_1 CONTACT,
        od.BILLING_SERVER_TIME BILLING_TIME,
        od.EMP_ID ORDER_PUNCHED_BY,
        oi.PRODUCT_NAME,
        oi.AMOUNT_PAID + oi.TOTAL_TAX AMOUNT
    FROM
        KETTLE_DUMP.EMPLOYEE_MEAL_ALLOWANCE_DATA a
    LEFT JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = a.ORDER_ID
    LEFT JOIN KETTLE_DUMP.ORDER_ITEM oi on od.ORDER_ID = oi.ORDER_ID
    LEFT JOIN KETTLE_MASTER_DUMP.EMPLOYEE_DETAIL emp ON emp.EMP_ID = a.EMPLOYEE_ID
    LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.BUSINESS_DATE IS NOT NULL AND od.BUSINESS_DATE BETWEEN :startDate AND :endDate) a
				     ]]>
					</content>
					<params>
						<param name="startDate" displayName="Start Date"
							dataType="DATE" format="yyyy-MM-dd" />
						<param name="endDate" displayName="End Date" dataType="DATE"
							format="yyyy-MM-dd" />
					</params>

				</report>
				<report id="2" name="Report Category 2 - Report 2"
					executionType="SQL" returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[

SELECT 
    *
FROM
    (SELECT 
	emp.EMP_NAME,
        emp.EMPLOYEE_CODE,
        emp.EMP_ID,
        emp.EMP_CONTACT_NUM_1 CONTACT,
        od.BILLING_SERVER_TIME BILLING_TIME,
        od.EMP_ID ORDER_PUNCHED_BY,
        oi.PRODUCT_NAME,
        oi.AMOUNT_PAID + oi.TOTAL_TAX AMOUNT
    FROM
        KETTLE_DUMP.EMPLOYEE_MEAL_ALLOWANCE_DATA a
    LEFT JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = a.ORDER_ID
    LEFT JOIN KETTLE_DUMP.ORDER_ITEM oi on od.ORDER_ID = oi.ORDER_ID
    LEFT JOIN KETTLE_MASTER_DUMP.EMPLOYEE_DETAIL emp ON emp.EMP_ID = a.EMPLOYEE_ID
    LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.BUSINESS_DATE IS NOT NULL AND od.BUSINESS_DATE BETWEEN :startDate AND :endDate) a
				     ]]>
					</content>
					<params>
						<param name="startDate" displayName="Start Date"
							dataType="DATE" format="yyyy-MM-dd" />
						<param name="endDate" displayName="End Date" dataType="DATE"
							format="yyyy-MM-dd" />
					</params>

				</report>
			</reports>
		</category>
	</categories>
</ReportCategories>
