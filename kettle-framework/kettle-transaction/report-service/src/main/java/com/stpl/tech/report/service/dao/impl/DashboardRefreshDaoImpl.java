package com.stpl.tech.report.service.dao.impl;

import com.stpl.tech.kettle.clm.model.DashboardLastUpdateData;
import com.stpl.tech.report.service.controller.DashboardRefreshResources;
import com.stpl.tech.report.service.dao.DashboardRefreshDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;

@Repository
public class DashboardRefreshDaoImpl extends CLMAbstractDaoImpl implements DashboardRefreshDao {


    @PersistenceContext(unitName = "CLMDataSourcePUName")
    @Qualifier(value = "CLMDataSourceEMFactory")
    private EntityManager transactionEntityManager;

    private static final Logger LOG = LoggerFactory.getLogger(DashboardRefreshResources.class);

    @Override
    public DashboardLastUpdateData getLastRecordDate() {
        try{
            StringBuilder queryString = new StringBuilder("FROM DashboardLastUpdateData");
            Query query = transactionEntityManager.createQuery(queryString.toString());
            return (DashboardLastUpdateData) query.getSingleResult();
        }catch(Exception e){
            LOG.error("Error in getting Dashboard Last Update Date ::::::",e);
        }
        return null;
    }

}
