package com.stpl.tech.report.service.service;

import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URISyntaxException;
import java.text.ParseException;

public interface WhatsAppDataService {


    boolean storeWhatsAppTemplateData(MultipartFile file,Boolean isTempalte) throws IOException;

    boolean updateCustomerWhatsAppCommunicationData(String startDate, String endDate,Boolean isPastDaySkipped) throws ParseException, IOException;

    Boolean pushCustomerWhatsAppStatsToCLM();

    Boolean loadWhatsAppData() throws IOException, URISyntaxException, ParseException;

    void generateWhatsAppRetentionData();

    void insertRawData();
}
