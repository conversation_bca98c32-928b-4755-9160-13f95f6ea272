package com.stpl.tech.report.service.model;

import com.stpl.tech.kettle.data.model.SuperUOrderRatingData;
import lombok.Data;

import javax.persistence.CascadeType;
import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.OneToMany;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class SuperUOrderTrim {
    Integer orderLevelId;
    Integer orderId;
    Integer unitId;
    Date billingServerTime;
    String feedback;
    BigDecimal orderRating;
    String customerName;
    Boolean isFeedbackRead = false;
    String orderDetails;
    List<SuperUCategoryRating> orderCatRating;

}
