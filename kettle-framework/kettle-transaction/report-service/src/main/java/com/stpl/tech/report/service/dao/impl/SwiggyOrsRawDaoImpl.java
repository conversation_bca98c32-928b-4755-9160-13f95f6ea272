package com.stpl.tech.report.service.dao.impl;

import com.stpl.tech.report.service.Entites.KettleAnalytics.SwiggyOrsRaw;
import com.stpl.tech.report.service.Entites.KettleAnalytics.SwiggyRejectionRaw;
import com.stpl.tech.report.service.dao.SwiggyOrsRawDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Repository;

import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Repository
@Slf4j
public class SwiggyOrsRawDaoImpl implements SwiggyOrsRawDao {

    @PersistenceContext(unitName = "DumpDataSourcePUName")
    @Qualifier(value = "DumpDataSourceEMFactory")
    protected EntityManager manager;
    @Override
    public List<SwiggyOrsRaw> getSwiggyOrsOrderForBusinessDate(Date businessDate) {
        try {
            Query query = manager.createQuery("from SwiggyOrsRaw WHERE date >= :date");
            query.setParameter("date", businessDate);
            List<SwiggyOrsRaw> res = query.getResultList();
            return res;
        }catch (Exception e){
            log.info("Error in  fetching Swiggy ors data for business date : {}",businessDate);
            return new ArrayList<>();
        }
    }

    @Override
    public List<SwiggyRejectionRaw> getSwiggyRejectionOrderForBusinessDate(Date businessDate) {
        try {
            Query query = manager.createQuery("from SwiggyRejectionRaw WHERE businessDate >= :date");
            query.setParameter("date", businessDate);
            List<SwiggyRejectionRaw> res = query.getResultList();
            return res;
        }catch (Exception e){
            log.info("Error in  fetching Swiggy ors data for business date : {}",businessDate);
            return new ArrayList<>();
        }
    }



}
