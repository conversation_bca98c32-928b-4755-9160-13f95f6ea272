package com.stpl.tech.report.service.service.impl;

import com.stpl.tech.kettle.data.model.CustomerCampaignJourney;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.report.service.dao.CampaignJourneyDao;
import com.stpl.tech.report.service.service.CampaignJourneyService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

@Service
public class CampaignJourneyServiceImpl implements CampaignJourneyService {

    @Autowired
    CampaignJourneyDao dao;

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public CustomerCampaignJourney setCustomerJourneyState(CustomerCampaignJourney journeyDetail) {
        if(Objects.nonNull(journeyDetail.getContactNumber()) && Objects.isNull(journeyDetail.getCustomerId())){
            CustomerInfo info = dao.getCustomerDetail(journeyDetail.getContactNumber());
            Integer id = Objects.nonNull(info) ? info.getCustomerId() : null;
            journeyDetail.setCustomerId(id);
        }
        if(Objects.isNull(journeyDetail.getCampaignJourneyId())){
            journeyDetail = dao.add(journeyDetail);
        }else{
            journeyDetail = dao.update(journeyDetail);
        }
        return journeyDetail;
    }
}
