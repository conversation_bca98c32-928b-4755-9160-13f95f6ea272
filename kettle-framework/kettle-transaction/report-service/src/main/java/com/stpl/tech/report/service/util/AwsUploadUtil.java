package com.stpl.tech.report.service.util;

import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.GeneratePresignedUrlRequest;
import com.amazonaws.services.s3.transfer.TransferManager;
import com.amazonaws.services.s3.transfer.Upload;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.io.File;
import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

@Component
public class AwsUploadUtil {

    private static final Logger LOG = LoggerFactory.getLogger(AwsUploadUtil.class);
    @Autowired
    TransferManager transferManager;
    @Autowired
    AmazonS3 s3Client;

    public void uploadFileToS3(String bucket, String key, File file){
        Upload upload = transferManager.upload(bucket,key,file);
        try{
            upload.waitForCompletion();
            Boolean res = file.delete();
            LOG.info("##### Final Zip file Deleted : {} #####",res);
        } catch(Exception e){
            LOG.info("!!!! Error while uploading to bucket : {} , {} ",e.getMessage(),e.getStackTrace());
        }

    }

    public String getAwsSignedUrl(String bucket,String key,int expires){
        GeneratePresignedUrlRequest generatePresignedUrlRequest = new GeneratePresignedUrlRequest(bucket,key);
        generatePresignedUrlRequest.setExpiration(getExpirationDate(expires));
        return s3Client.generatePresignedUrl(generatePresignedUrlRequest).toString();
    }
    private Date getExpirationDate(int daysToExpire) {
        Calendar calendar = Calendar.getInstance(TimeZone.getTimeZone("Asia/Kolkata"));
        calendar.add(Calendar.DATE, daysToExpire);
        return calendar.getTime();
    }

}
