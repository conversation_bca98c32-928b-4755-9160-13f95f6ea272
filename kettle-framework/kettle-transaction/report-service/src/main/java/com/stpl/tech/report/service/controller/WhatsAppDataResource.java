package com.stpl.tech.report.service.controller;

import com.stpl.tech.report.service.service.WhatsAppDataService;
import com.stpl.tech.util.AppUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URISyntaxException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

import static com.stpl.tech.report.service.core.ReportServiceConstants.API_VERSION;
import static com.stpl.tech.report.service.core.ReportServiceConstants.SEPARATOR;
import static com.stpl.tech.report.service.core.ReportServiceConstants.WHATSAPP_DATA_ROOT_CONTEXT;

@RestController
@RequestMapping(value = API_VERSION+SEPARATOR+WHATSAPP_DATA_ROOT_CONTEXT)
public class WhatsAppDataResource {

      WhatsAppDataService whatsAppDataService;

    @Autowired
    public WhatsAppDataResource(WhatsAppDataService whatsAppDataService) {
        this.whatsAppDataService = whatsAppDataService;
    }

    @PostMapping("/upload-whatsapp-data")
    void uploadWhatsAppDataLoad(@RequestParam("file")MultipartFile file,@RequestParam Boolean isTemplate) throws IOException {
            whatsAppDataService.storeWhatsAppTemplateData(file,isTemplate);
    }
    @Scheduled(cron = "0 0 16 * * *", zone = "GMT+05:30")
    @PostMapping("/update-customer-stats")
    Boolean updateCustomerStats() throws IOException, ParseException {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        return whatsAppDataService.updateCustomerWhatsAppCommunicationData(dateFormat.format(AppUtils.getPreviousDate()),dateFormat.format(AppUtils.getPreviousDate()),false);
    }

    @Scheduled(cron = "0 0 11 * * 1", zone = "GMT+05:30")
    @GetMapping("/push-data-to-clm")
    Boolean pushDataToClm() throws IOException, ParseException {
        return whatsAppDataService.pushCustomerWhatsAppStatsToCLM();
    }

    @Scheduled(cron = "0 40 14 * * *", zone = "GMT+05:30")
    @PostMapping("/load-whatsapp-data-from-email")
    Boolean loadWhatsAppDataFromEmail() throws IOException, ParseException, URISyntaxException {
        return whatsAppDataService.loadWhatsAppData();
    }

    @Scheduled(cron = "0 20 15 * * *", zone = "GMT+05:30")
    @GetMapping("/execute-generate-whatsapp-retention-data")
    void executeGenerateWhatsAppRetentionData(){
        whatsAppDataService.generateWhatsAppRetentionData();
    }

    @Scheduled(cron = "0 0 15 * * *", zone = "GMT+05:30")
    @GetMapping("/execute-insert-raw-data")
    void executeInsertRawData(){
        whatsAppDataService.insertRawData();
    }

    @PostMapping("/update-customer-stats-new")
    Boolean updateCustomerStatsNew(@RequestParam  @DateTimeFormat(pattern = "dd-MM-yyyy HH:mm:ss")  Date startDate,@RequestParam @DateTimeFormat(pattern = "dd-MM-yyyy HH:mm:ss") Date endDate) throws IOException, ParseException {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        return whatsAppDataService.updateCustomerWhatsAppCommunicationData(dateFormat.format(startDate),dateFormat.format(endDate),false);
    }

}
