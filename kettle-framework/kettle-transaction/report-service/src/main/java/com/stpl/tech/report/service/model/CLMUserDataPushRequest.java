package com.stpl.tech.report.service.model;

import java.util.Date;

public class CLMUserDataPushRequest {
    private Integer startId;
    private Integer endId;
    private Integer dataSize;
    private Integer brandId;
    private Integer eventId;
    private String type;
    private boolean isTrimmed;

    public CLMUserDataPushRequest() {
    }

    public CLMUserDataPushRequest(Integer startId, Integer endId, Integer dataSize, Integer brandId, Integer eventId, String type) {
        this.startId = startId;
        this.endId = endId;
        this.dataSize = dataSize;
        this.brandId = brandId;
        this.eventId = eventId;
        this.type = type;
    }

    public Integer getStartId() {
        return startId;
    }

    public void setStartId(Integer startId) {
        this.startId = startId;
    }

    public Integer getDataSize() {
        return dataSize;
    }

    public void setDataSize(Integer dataSize) {
        this.dataSize = dataSize;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public Integer getEventId() {
        return eventId;
    }

    public void setEventId(Integer eventId) {
        this.eventId = eventId;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getEndId() {
        return endId;
    }

    public void setEndId(Integer endId) {
        this.endId = endId;
    }

    public boolean getIsTrimmed() {
        return isTrimmed;
    }

    public void setTrimmed(boolean trimmed) {
        isTrimmed = trimmed;
    }
}
