package com.stpl.tech.report.service.dao;

import com.stpl.tech.kettle.data.model.GamifiedOfferDetail;
import com.stpl.tech.master.data.dao.AbstractDao;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface OrderSearchDao extends AbstractDao {
    public Map<String, Object> getPartnerOrderDetail(String partnerSourceId, String issueType);
    public Map<String, Object> getCustomerDetail(String partnerSourceId);
    public Map<String, Object> getFallbackOrderCustomerDetail(String partnerCustomerId);
    public List<GamifiedOfferDetail> getGamifiedOfferDetailDataList(String offerSource, String utmSource, Date recordTime);
}
