package com.stpl.tech.report.service.controller;

import static com.stpl.tech.report.service.core.ReportServiceConstants.API_VERSION;
import static com.stpl.tech.report.service.core.ReportServiceConstants.DATA_ENCRYPTION;
import static com.stpl.tech.report.service.core.ReportServiceConstants.SEPARATOR;

import java.security.GeneralSecurityException;

import org.codehaus.jettison.json.JSONException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.stpl.tech.kettle.domain.model.DataEncryptionRequest;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.report.service.service.DataEncryptionService;

import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + DATA_ENCRYPTION)
@Slf4j
public class DataEncryptionResource {

	@Autowired
	private DataEncryptionService dataEncryptionService;

	@PostMapping(value = "/encrypt", produces = "text/plain")
	public String encryptData(@RequestBody String plainText) throws GeneralSecurityException, JSONException {
		return dataEncryptionService.encryptData(plainText);
	}

	@PostMapping(value = "/decrypt", produces = "text/plain")
	public String decryptData(@RequestBody String plainText) throws GeneralSecurityException {
		return dataEncryptionService.decryptData(plainText);
	}

	@PostMapping(value = "/bulk-encrypt")
	public boolean bulkEncryptData(@RequestBody DataEncryptionRequest request,
			@RequestParam(required = true) String schema) throws GeneralSecurityException {
		try {
			if (schema.equalsIgnoreCase("KETTLE")) {
				return dataEncryptionService.bulkEncryptDataKettle(request);
			} else if (schema.equalsIgnoreCase("KETTLE_MASTER")) {
				return dataEncryptionService.bulkEncryptDataMaster(request);
			} else if (schema.equalsIgnoreCase("KETTLE_REKOGNITION")) {
				return dataEncryptionService.bulkEncryptDataRekognition(request);
			} else {
				throw new DataNotFoundException("Invalid schema");
			}
		} catch (Exception e) {
			log.error("unable to bulk encrypt data");
			return false;
		}
	}
}
