package com.stpl.tech.report.service.dao.impl;

import com.stpl.tech.kettle.data.dao.impl.AbstractDaoImpl;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.report.service.dao.CampaignJourneyDao;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;

@Repository
public class CampaignJourneyDaoImpl extends AbstractDaoImpl implements CampaignJourneyDao {
    private static final Logger LOG = LoggerFactory.getLogger(CampaignJourneyDaoImpl.class);
    @Override
    public CustomerInfo getCustomerDetail(String contactNumber) {
        try {
            Query query = manager.createQuery("FROM CustomerInfo c where c.contactNumber = :contact");
            query.setParameter("contact",contactNumber);
            CustomerInfo info = (CustomerInfo) query.getSingleResult();
            return info;
        }catch (Exception e){
            LOG.error("Error while getting customer id to save campaign journey");
        }
        return null;
    }
}
