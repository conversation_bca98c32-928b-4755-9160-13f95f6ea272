package com.stpl.tech.report.service.controller;

import com.stpl.tech.kettle.domain.model.OrderDetailEventRequest;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.report.service.service.ReportExternalService;
import com.stpl.tech.util.AppUtils;
import lombok.Synchronized;
import org.drools.core.beliefsystem.abductive.Abducible;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.util.Date;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static com.stpl.tech.report.service.core.ReportServiceConstants.API_VERSION;
import static com.stpl.tech.report.service.core.ReportServiceConstants.EXTERNAL_API_ROOT_CONTEXT;
import static com.stpl.tech.report.service.core.ReportServiceConstants.SEPARATOR;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR + EXTERNAL_API_ROOT_CONTEXT)
public class ReportExternalResource extends AbstractResources {
    private static final Logger LOG = LoggerFactory.getLogger(ReportExternalResource.class);

    @Autowired
    private ReportExternalService reportExternalService;

    private final ConcurrentHashMap<String, Object> orderLocks = new ConcurrentHashMap<>();


    @PostMapping(value = "/publish-order-detail",consumes = MediaType.APPLICATION_JSON,produces = MediaType.APPLICATION_JSON)
    public boolean publishPartnerOrderDetail(@RequestBody OrderDetailEventRequest req){
        Object lock = orderLocks.computeIfAbsent(req.getPartnerOrderId(), k -> new Object());
        synchronized(lock) {
            try {
                return reportExternalService.publishPartnerOrderDetail(req);
            }finally {
                orderLocks.remove(req.getPartnerOrderId());
            }
        }
    }

    @GetMapping(value = "/get-partner-order-detail",produces = MediaType.TEXT_PLAIN)
    public String getPartnerOrderDetail(@RequestParam String partnerSourceId, @RequestParam String issueType){
        return reportExternalService.getPartnerOrderDetail(partnerSourceId,issueType);
    }

    @GetMapping(value = "/fetch-partner-order-detail",produces = MediaType.APPLICATION_JSON)
    public Map<String,Object> fetchPartnerOrderDetail(@RequestParam String partnerSourceId, @RequestParam String issueType){
        return reportExternalService.fetchPartnerOrderDetail(partnerSourceId,issueType);
    }

    @GetMapping(value = "/get-customer-detail",produces = MediaType.TEXT_PLAIN)
    public String getCustomerDetail(@RequestParam String partnerSourceId){
        return reportExternalService.getCustomerDetail(partnerSourceId);
    }

    @GetMapping(value = "/get-fallback-order-customer-detail",produces = MediaType.TEXT_PLAIN)
    public String getFallbackOrderCustomerDetail(@RequestParam String partnerCustomerId){
        return reportExternalService.getFallbackOrderCustomerDetail(partnerCustomerId);
    }

    @RequestMapping(method = RequestMethod.GET,value = "publish-unlock-event",produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    public boolean publishUnlockEvent(@RequestParam String offerSource,@RequestParam String utmSource,@RequestParam String recordTime){
        try {
            Date temp = AppUtils.parseDate(recordTime);
            return reportExternalService.publishUnlockEvent(offerSource,utmSource,AppUtils.parseDate(recordTime));
        }catch (Exception e){
            LOG.info("Error in pushing Event to cleverTap for unlock deal",e.getMessage());
        }
        return false;
    }

    @RequestMapping(method = RequestMethod.POST,value = "publish-cleverTap-event",produces=MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    public void pushCleverTapEvent(@RequestParam String startDate,@RequestParam String endDate){
        LOG.info("API TO PUSH CLEVERTAP EVENT");
        reportExternalService.pushChargedEventForDate(startDate,endDate);
    }

    @PostMapping(value = "publish-swiggy-ors")
    public void publishSwiggyOrs(){
        try {
            reportExternalService.pushSwiggyOrsData();
        }catch (Exception e){
            LOG.info("Error in publishing Swiggy Ors data : {}",e.getMessage());
        }
    }

    @Scheduled(cron = "0 0 19 * * *", zone = "GMT+05:30")
    public void publishSwiggyOrsCron(){
        try {
            reportExternalService.pushSwiggyOrsData();
        }catch (Exception e){
            LOG.info("Error in publishing Swiggy Ors data : {}",e.getMessage());
        }
    }

    @PostMapping(value = "publish-swiggy-rejection")
    public void publishSwiggyRejection(){
        try {
            reportExternalService.pushSwiggyRejectionData();
        }catch (Exception e){
            LOG.info("Error in publishing Swiggy Rejection data : {}",e.getMessage());
        }
    }

    @Scheduled(cron = "0 0 20 * * *", zone = "GMT+05:30")
    public void publishSwiggyRejectionCron(){
        try {
            reportExternalService.pushSwiggyRejectionData();
        }catch (Exception e){
            LOG.info("Error in publishing Swiggy Rejection data : {}",e.getMessage());
        }
    }

}
