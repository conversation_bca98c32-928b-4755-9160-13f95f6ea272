package com.stpl.tech.report.service.service.impl;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

import com.stpl.tech.util.EnvType;
import org.json.JSONArray;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.google.common.base.Stopwatch;
import com.stpl.tech.kettle.customer.service.CustomerService;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.report.service.core.ReportProperties;
import com.stpl.tech.report.service.dao.CleverTapPushV1Dao;
import com.stpl.tech.report.service.model.CLMDataPushStatus;
import com.stpl.tech.report.service.model.CLMDataPushType;
import com.stpl.tech.report.service.model.CLMOrderDataPushRequest;
import com.stpl.tech.report.service.model.CLMUserDataPushRequest;
import com.stpl.tech.report.service.model.CleverTapEndpoints;
import com.stpl.tech.report.service.model.CleverTapPushResponse;
import com.stpl.tech.report.service.service.CleverTapDataPushServiceV1;
import com.stpl.tech.report.service.util.AbstractRestTemplateCLM;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;

/**
 * Created by Chaayos on 02-05-2017.
 */
@Service
public class CleverTapDataPushServiceV1Impl implements CleverTapDataPushServiceV1 {

    private static final Logger LOG = LoggerFactory.getLogger(CleverTapDataPushServiceV1Impl.class);

    @Autowired
    CustomerService customerService;

    @Autowired
    private CleverTapPushV1Dao cleverTapPushV1Dao;

    @Autowired
    private ReportProperties env;

    @Transactional(rollbackFor = Exception.class, value = "CLMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void pushClmData(CLMUserDataPushRequest request) {
        LOG.info("CLEVER_TAP-USER-PROFILE-PUSH :: performing data push for start id : {}, data size : {}, " +
                "brand id : {},type : {}, event id : {}", request.getStartId(), request.getDataSize(), request.getBrandId(), request.getType(), request.getEventId());
        CLMDataPushStatus clmDataPushStatus = CLMDataPushStatus.FAILED;
        Date dataPushStartTime = AppUtils.getCurrentTimestamp();
        Integer startId = request.getStartId();
        while (true) {
            if(Objects.nonNull(request.getEndId()) && startId >= request.getEndId()){
                break;
            }
            List<Object[]> bulkData = null;
            if (CLMDataPushType.PROFILE.getTypeValue().equals(request.getType())) {
                bulkData = cleverTapPushV1Dao.getCLMProfileBulkData(startId, request.getDataSize(), request.getBrandId(), request.getIsTrimmed());
            } else if (CLMDataPushType.EVENT.getTypeValue().equals(request.getType())) {
                bulkData = cleverTapPushV1Dao.getCLMEventBulkData(startId, request.getDataSize(), request.getEventId(), request.getIsTrimmed());
            }
            Integer startIndex = 0;
            Integer pageSize = Math.min(bulkData.size(), 1000);
            while (true) {
                JSONArray dataToPush = getPagedData(startIndex, pageSize, bulkData);
                if (dataToPush.length() <= 0) {
                    break;
                }
                pageSize = Math.min(dataToPush.length(),1000);
                Integer startCustomerId = (Integer) bulkData.get(startIndex)[0];
                Integer endCustomerId = (Integer) bulkData.get(startIndex + pageSize - 1)[0];
                JSONObject obj = new JSONObject();
                obj.put("d",dataToPush);
                CleverTapPushResponse response = pushBatchWiseData(request.getBrandId(), obj.toString(), startCustomerId,
                        endCustomerId, request.getType(), request.getEventId(), request.getDataSize(), pageSize);
                if(pageSize.equals(response.getProcessed()) && clmDataPushStatus.equals(CLMDataPushStatus.FAILED)){
                    clmDataPushStatus = CLMDataPushStatus.SUCCESS;
                }
                else if(!pageSize.equals(response.getProcessed()) && response.getProcessed() > 0){
                    clmDataPushStatus = CLMDataPushStatus.PUSHED_WITH_FAILURE;
                }
                startIndex = startIndex + pageSize;
            }
            if(bulkData.size() > 0){
                startId = (Integer) bulkData.get(bulkData.size() - 1)[0] + 1;
            }else{
                clmDataPushStatus = CLMDataPushStatus.SUCCESS;
            }
            if (bulkData.size() < request.getDataSize()) {
                break;
            }
        }
        if(CLMDataPushType.EVENT.getTypeValue().equals(request.getType())){
            cleverTapPushV1Dao.updateClmEventCalculation(dataPushStartTime,clmDataPushStatus, request.getEventId(), request.getBrandId());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CLMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void pushClmChargedEventData(CLMOrderDataPushRequest request, CLMDataPushType dataPushType) {
        if (request.isDataPushInRange() && Objects.isNull(request.getEndBusinessDate())) {
            return;
        }
        Date startDate = AppUtils.getDayBeforeOrAfterDay(request.getStartBusinessDate(), 0);
        Date endDate = Objects.isNull(request.getEndBusinessDate()) ?
                AppUtils.getDayBeforeOrAfterDay(request.getStartBusinessDate(), 1) :
                AppUtils.getDayBeforeOrAfterDay(request.getEndBusinessDate(), 0);

        while (startDate.before(endDate)) {
            Integer startIndex = 0;
            Integer pageSize = 1000;
            List<Object[]> bulkData = null;
            if (CLMDataPushType.CHARGED_ORDER.equals(dataPushType)) {
                bulkData = cleverTapPushV1Dao.getCLMChargedOrderData(startDate, request.getBrandId());
            } else if (CLMDataPushType.MEMBERSHIP_EVENT.equals(dataPushType)) {
                bulkData = cleverTapPushV1Dao.getCLMMembershipEventData(startDate, request.getBrandId());
            } else if (CLMDataPushType.WALLET_EVENT.equals(dataPushType)) {
                bulkData = cleverTapPushV1Dao.getCLMWalletEventData(startDate, request.getBrandId());
            }
            if (Objects.isNull(bulkData) || bulkData.isEmpty()) {
                startDate = AppUtils.addDays(startDate, 1);
                continue;
            }
            while (true) {
                JSONArray dataToPush = getPagedData(startIndex, pageSize, bulkData);
                if (dataToPush.length() <= 0) {
                    break;
                }
                pageSize = Math.min(dataToPush.length(), 1000);
                Integer startOrderId = (Integer) bulkData.get(startIndex)[0];
                Integer endOrderId = (Integer) bulkData.get(startIndex + pageSize - 1)[0];
                JSONObject obj = new JSONObject();
                obj.put("d", dataToPush);
                pushChargedEventBatchWiseData(request.getBrandId(), obj.toString(), startOrderId,
                        endOrderId, startDate, bulkData.size(), pageSize, dataPushType);
                startIndex = startIndex + pageSize;
            }
            startDate = AppUtils.addDays(startDate, 1);
        }

    }

    @Override
    public void pushClmOrderDataRange(CLMOrderDataPushRequest request) {
        List<Object[]> bulkData = cleverTapPushV1Dao.getCLMChargedOrderDataRange(request.getStartCustomerId(), request.getEndCustomerId(),request.getBrandId());
        Integer startIndex=0;
        Integer pageSize=1000;
        while(true){
            JSONArray dataToPush = getPagedData(startIndex, pageSize, bulkData);
            if (dataToPush.length() <= 0) {
                break;
            }
            pageSize = Math.min(dataToPush.length(),1000);
            Integer startOrderId = (Integer) bulkData.get(startIndex)[0];
            Integer endOrderId = (Integer) bulkData.get(startIndex + pageSize - 1)[0];
            JSONObject obj = new JSONObject();
            obj.put("d",dataToPush);
            CleverTapPushResponse response = pushBatchWiseData(request.getBrandId(),obj.toString(),startOrderId,endOrderId,"",null, bulkData.size(), pageSize);
            startIndex = startIndex + pageSize;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CLMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void pushProfileEventForADate(Date date, Integer startId, Integer dataSize, Integer brandId) {
        Integer eventId = cleverTapPushV1Dao.getEventIdForDate(date,brandId);
        if(Objects.nonNull(eventId)){
            CLMUserDataPushRequest request = new CLMUserDataPushRequest(startId,null,dataSize,1,eventId,"event");
            pushClmData(request);
        }else{
            LOG.info("Skipping process as no eventId fount to push for date : {}",JSONSerializer.toJSON(date));
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class, value = "CLMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void pushProfileEventForAEvent(Integer eventId, Integer startId, Integer dataSize, Integer brandId) {
        if(Objects.nonNull(eventId)){
            CLMUserDataPushRequest request = new CLMUserDataPushRequest(startId,null,dataSize,1,eventId,"event");
            pushClmData(request);
        }else{
            LOG.info("Skipping process as no eventId fount to push for event : {}",eventId);
        }
    }

    @Override
    public CleverTapPushResponse pushBatchWiseData(Integer brandId, Object pagedDataToPush, Integer startId, Integer endId,
                                           String type, Integer eventId, Integer batchSize, Integer pageSize) {
        Stopwatch watch = Stopwatch.createUnstarted();
        Date startTime = AppUtils.getCurrentTimestamp();
        watch.start();
        CleverTapPushResponse response = null;
        Pair<String, String> cleverTapAcc = getCleverTapAccByBrand(brandId);
        try {
            if (Objects.nonNull(pagedDataToPush) && Objects.nonNull(cleverTapAcc)) {
                LOG.info("CLEVER_TAP-USER-PROFILE-PUSH :: Pushing data for brand id : {}", brandId);
                 response = AbstractRestTemplateCLM.postWithHeaders(CleverTapEndpoints.UPLOAD_PROFILE_OR_EVENT,
                        pagedDataToPush, CleverTapPushResponse.class, cleverTapAcc.getKey(), cleverTapAcc.getValue(), env.getEnvironmentType());
                LOG.info("CLEVER_TAP-USER-PROFILE-PUSH :::: start id : {}, end id : {}", startId, endId);
                LOG.info("CLEVER_TAP-USER-PROFILE-PUSH :: Status : {}, processed items : {}, unprocessed items : {}", response.getStatus(), response.getProcessed(), response.getUnprocessed());
            } else {
                LOG.info("CLEVER_TAP-USER-PROFILE-PUSH :: skipping data push as no data found for brand id : {}", brandId);
                LOG.info("CLEVER_TAP-USER-PROFILE-PUSH :: Clevertap info = {}", JSONSerializer.toJSON(cleverTapAcc));
            }
            Date endTime = AppUtils.getCurrentTimestamp();
            String pushStatus = pageSize.equals(response.getProcessed()) ? CLMDataPushStatus.SUCCESS.name()
                    : CLMDataPushStatus.PUSHED_WITH_FAILURE.name();
            cleverTapPushV1Dao.addDataPushStatus(eventId, batchSize, startId, endId, pageSize, startTime, endTime, watch.stop().elapsed(TimeUnit.MILLISECONDS), brandId, pushStatus);
            return response;
        } catch (Exception e) {
            LOG.error("Error while pushing data for brand id : {}, start id : {}, end id:{}", brandId, startId, endId, e);
        }
        return new CleverTapPushResponse("fail",0);
    }

    private CleverTapPushResponse pushChargedEventBatchWiseData(Integer brandId, Object pagedDataToPush, Integer startId, Integer endId,
                                                     Date businessDate, Integer batchSize, Integer pageSize, CLMDataPushType dataPushType) {
        Stopwatch watch = Stopwatch.createUnstarted();
        Date startTime = AppUtils.getCurrentTimestamp();
        watch.start();
        CleverTapPushResponse response = null;
        Pair<String, String> cleverTapAcc = getCleverTapAccByBrand(brandId);
        try {
            if (Objects.nonNull(pagedDataToPush) && Objects.nonNull(cleverTapAcc)) {
                LOG.info("CLEVER_TAP-USER-PROFILE-PUSH :: Pushing data for brand id : {}", brandId);
                response = AbstractRestTemplateCLM.postWithHeaders(CleverTapEndpoints.UPLOAD_PROFILE_OR_EVENT,
                        pagedDataToPush, CleverTapPushResponse.class, cleverTapAcc.getKey(), cleverTapAcc.getValue(), env.getEnvironmentType());
                LOG.info("CLEVER_TAP-USER-PROFILE-PUSH :::: start  order id : {}, end order id : {}", startId, endId);
                LOG.info("CLEVER_TAP-USER-PROFILE-PUSH :: Status : {}, processed items : {}, unprocessed items : {}", response.getStatus(), response.getProcessed(), response.getUnprocessed());
            } else {
                LOG.info("CLEVER_TAP-USER-PROFILE-PUSH :: skipping data push as no data found for brand id : {}", brandId);
                LOG.info("CLEVER_TAP-USER-PROFILE-PUSH :: Clevertap info = {}", JSONSerializer.toJSON(cleverTapAcc));
            }
            Date endTime = AppUtils.getCurrentTimestamp();
            String pushStatus = pageSize.equals(response.getProcessed()) ? CLMDataPushStatus.SUCCESS.name()
                    : CLMDataPushStatus.PUSHED_WITH_FAILURE.name();
            recordPushStatus(businessDate, batchSize, startId, endId, pageSize, startTime, endTime, watch.stop().elapsed(TimeUnit.MILLISECONDS), brandId, pushStatus, dataPushType);
            return response;
        } catch (Exception e) {
            LOG.error("Error while pushing data for brand id : {}, start order id : {}, end order id:{}", brandId, startId, endId, e);
            recordPushStatus(businessDate, batchSize, startId, endId, pageSize, startTime, null, watch.stop().elapsed(TimeUnit.MILLISECONDS), brandId, CLMDataPushStatus.FAILED.name(), dataPushType);
        }
        return new CleverTapPushResponse("fail", 0);
    }

    private void recordPushStatus(Date businessDate, Integer batchSize, Integer startId, Integer endId, Integer pageSize,
                                  Date startTime, Date endTime, long elapsedTime, Integer brandId, String status, CLMDataPushType dataPushType) {
        if (CLMDataPushType.CHARGED_ORDER.equals(dataPushType)) {
            cleverTapPushV1Dao.addOrderDataPushStatus(businessDate, batchSize, startId, endId, pageSize, startTime, endTime, elapsedTime, brandId, status);
        } else if (CLMDataPushType.MEMBERSHIP_EVENT.equals(dataPushType)) {
            cleverTapPushV1Dao.addMembershipDataPushStatus(businessDate, batchSize, startId, endId, pageSize, startTime, endTime, elapsedTime, brandId, status);
        } else if (CLMDataPushType.WALLET_EVENT.equals(dataPushType)) {
            cleverTapPushV1Dao.addWalletDataPushStatus(businessDate, batchSize, startId, endId, pageSize, startTime, endTime, elapsedTime, brandId, status);
        }
    }

    @Override
     public JSONArray getPagedData(Integer startIndex, Integer pageSize, List<Object[]> fullData) {
       JSONArray pagedData = new JSONArray();
        for (int i = startIndex; i < pageSize + startIndex && i < fullData.size(); i++) {
            pagedData.put( new JSONObject((String)fullData.get(i)[1]));

        }
        return pagedData;
    }

    private Pair<String, String> getCleverTapAccByBrand(Integer brandId) {
        if (brandId.equals(AppConstants.CHAAYOS_BRAND_ID)) {
            return new Pair<>(env.getChaayosClevertapAccount(), env.getChaayosClevertapPasscode());
        } else if (brandId.equals(AppConstants.GNT_BRAND_ID)) {
            return new Pair<>(env.getGntClevertapAccount(), env.getGntClevertapPasscode());
        } else {
            LOG.info("CLEVER_TAP-USER-PROFILE-PUSH :: invalid brand id found Brand id : {}", brandId);
            return null;
        }
    }
}
