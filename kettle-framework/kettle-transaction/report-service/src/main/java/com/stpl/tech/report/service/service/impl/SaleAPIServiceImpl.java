package com.stpl.tech.report.service.service.impl;

import com.stpl.tech.kettle.core.data.vo.OrderFetchStrategy;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.domain.model.TransactionDetail;
import com.stpl.tech.kettle.xml.model.PartnerSaleRecord;
import com.stpl.tech.kettle.xml.model.PartnerSaleRecords;
import com.stpl.tech.kettle.xml.model.SaleRecord;
import com.stpl.tech.kettle.xml.model.SaleRecords;
import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DuplicateRequestException;
import com.stpl.tech.master.core.external.acl.service.TokenService;
import com.stpl.tech.master.core.external.acl.service.impl.JWTToken;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.report.service.dao.SaleAPIDao;
import com.stpl.tech.report.service.service.SaleAPIService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.xml.bind.JAXBException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class SaleAPIServiceImpl implements SaleAPIService {

    private static final Logger LOG = LoggerFactory.getLogger(SaleAPIServiceImpl.class);

    private static final Map<String, Long> apiTraffic = new HashMap<>();
    @Autowired
    MasterDataCache masterCache;
    @Autowired
    private SaleAPIDao dao;

    @Autowired(required = false)
    private TokenService<JWTToken> jwtService;

    @Override
    public List<Order> getOrderDetails(int unitId, Date startTime, Date endTime, OrderFetchStrategy strategy) {
        return dao.getOrderDetails(unitId, startTime, endTime, strategy);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public PartnerSaleRecords generatePartnerReportForDay(String api,HttpServletRequest request, String businessDate,String byPassValidation) throws DataNotFoundException, JAXBException, AuthenticationFailureException, DuplicateRequestException {
        Date date = AppUtils.parseDate(businessDate);
        String authHeader = request.getHeader("auth") != null ? request.getHeader("auth").trim() : null;
        PartnerSaleRecords list = null;
        Date startTime = AppUtils.getStartOfDay(date);
        Date endTime = AppUtils.getEndOfDay(date);
        if (authHeader != null && !authHeader.equals("null") && !authHeader.equals("")) {
            JWTToken jwtToken = new JWTToken();
            jwtService.parseToken(jwtToken, authHeader);
            int unitId = Integer.valueOf(jwtToken.getUnitId());
            int userId = Integer.valueOf(jwtToken.getUserId());
			LOG.info("Generating sales report for unit {} and StartTime {} and EndTime{} and api ", unitId, startTime,
					endTime, api);
            if(!StringUtils.isEmpty(byPassValidation) && AppConstants.YES.equals(byPassValidation)){
                list = generatePartnerSaleReport(unitId, startTime, endTime);
            }else {
                validateTraffic(api, unitId, userId);
                list = generatePartnerSaleReport(unitId, startTime, endTime);
            }
        } else {
            throw new AuthenticationFailureException("Unauthorised Access");
        }
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public SaleRecords generateHourlyReports(String api,HttpServletRequest request) throws DataNotFoundException, JAXBException, AuthenticationFailureException, DuplicateRequestException {
        String authHeader = request.getHeader("auth") != null ? request.getHeader("auth").trim() : null;
        SaleRecords list = null;
        Date currentTime = AppUtils.getCurrentTimestamp();
        Date startTime = AppUtils.getStartOfPreviousHour(currentTime);
        Date endTime = AppUtils.getEndOfPreviousHour(currentTime);
        if (authHeader != null && !authHeader.equals("null") && !authHeader.equals("")) {
            JWTToken jwtToken = new JWTToken();
            jwtService.parseToken(jwtToken, authHeader);
            String sessionKey = jwtToken.getSessionKey();
            int terminalId = Integer.valueOf(jwtToken.getTerminalId());
            int userId = Integer.valueOf(jwtToken.getUserId());
            int unitId = Integer.valueOf(jwtToken.getUnitId());
			LOG.info("Generating sales report for unit {} and StartTime {} and EndTime{} and api ", unitId, startTime,
					endTime, api);
            validateTraffic(api, unitId, userId);
            list = generatePathfinderReport(unitId, startTime, endTime);
        } else {
            throw new AuthenticationFailureException("Unauthorised Access");
        }
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public PartnerSaleRecords generatePartnerHourlyReports(String api,HttpServletRequest request) throws AuthenticationFailureException, DuplicateRequestException, DataNotFoundException, JAXBException {
        String authHeader = request.getHeader("auth") != null ? request.getHeader("auth").trim() : null;
        PartnerSaleRecords list = null;
        Date currentTime = AppUtils.getCurrentTimestamp();
        Date startTime = AppUtils.getStartOfPreviousHour(currentTime);
        Date endTime = AppUtils.getEndOfPreviousHour(currentTime);
        if (authHeader != null && !authHeader.equals("null") && !authHeader.equals("")) {
            JWTToken jwtToken = new JWTToken();
            jwtService.parseToken(jwtToken, authHeader);
            String sessionKey = jwtToken.getSessionKey();
            int terminalId = Integer.valueOf(jwtToken.getTerminalId());
            int userId = Integer.valueOf(jwtToken.getUserId());
            int unitId = Integer.valueOf(jwtToken.getUnitId());
			LOG.info("Generating sales report for unit {} and StartTime {} and EndTime{} and api ", unitId, startTime,
					endTime, api);
            validateTraffic(api, unitId, userId);
            list = generatePartnerSaleReport(unitId, startTime, endTime);
        } else {
            throw new AuthenticationFailureException("Unauthorised Access");
        }
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public SaleRecords generatePathfinderReportForDay(String api,HttpServletRequest request, String businessDate) throws DataNotFoundException, JAXBException, AuthenticationFailureException, DuplicateRequestException {
        Date date = AppUtils.parseDate(businessDate);
        String authHeader = request.getHeader("auth") != null ? request.getHeader("auth").trim() : null;
        SaleRecords list = null;
        Date startTime = AppUtils.getStartOfDay(date);
        Date endTime = AppUtils.getEndOfDay(date);
        if (authHeader != null && !authHeader.equals("null") && !authHeader.equals("")) {
            JWTToken jwtToken = new JWTToken();
            jwtService.parseToken(jwtToken, authHeader);
            int unitId = Integer.valueOf(jwtToken.getUnitId());
            int userId = Integer.valueOf(jwtToken.getUserId());
			LOG.info("Generating sales report for unit {} and StartTime {} and EndTime{} and api ", unitId, startTime,
					endTime, api);
            validateTraffic(api, unitId, userId);
            list = generatePathfinderReport(unitId, startTime, endTime);
        } else {
            throw new AuthenticationFailureException("Unauthorised Access");
        }
        return list;
    }

    private SaleRecords generatePathfinderReport(int unitId, Date startTime, Date endTime)
            throws JAXBException {
		OrderFetchStrategy strategy = new OrderFetchStrategy(false, true, false, false, false, null, -1, true);
        List<Order> orders = getOrderDetails(unitId, startTime, endTime, strategy);
        SaleRecords sr = new SaleRecords();
        sr.setRecords(new ArrayList<SaleRecord>());
        for (Order o : orders) {
            if (OrderStatus.CANCELLED.equals(o.getStatus())) {
                continue;
            }
            int zeroTaxItemCount = 0;
            for (OrderItem i : o.getOrders()) {
                if (AppUtils.isGiftCard(i.getCode())) {
                    zeroTaxItemCount = zeroTaxItemCount + 1;
                }
            }
            if (zeroTaxItemCount >= o.getOrders().size()) {
                // skip for only zero tax bill
                continue;
            }
            SaleRecord r = createSaleRecord(o);
            sr.getRecords().add(r);
        }
        return sr;
    }

    private SaleRecord createSaleRecord(Order o) {
        SaleRecord r = new SaleRecord();
        TransactionDetail td = o.getTransactionDetail();
        BigDecimal netSales = BigDecimal.ZERO;
        BigDecimal discount = BigDecimal.ZERO;
        BigDecimal paidAmount = BigDecimal.ZERO;
        BigDecimal zeroTaxAmount = BigDecimal.ZERO;
        for (OrderItem i : o.getOrders()) {
            if (AppUtils.isGiftCard(i.getCode())) {
                zeroTaxAmount = zeroTaxAmount.add(i.getPrice().multiply(new BigDecimal(i.getQuantity())));
            }
        }
        discount = td.getDiscountDetail().getPromotionalOffer().add(td.getDiscountDetail().getDiscount().getValue());
        netSales = td.getTaxableAmount().subtract(zeroTaxAmount);
        paidAmount = netSales.add(o.getTransactionDetail().getTax());
        r.setReceiptNumber(String.valueOf(o.getOrderId()));
        r.setReceiptDate(AppUtils.generateSimpleDateFormat(o.getBillingServerTime()));
        r.setTransactionTime(AppUtils.generateSimpleTimeFormat(o.getBillingServerTime()));
        r.setInvoiceAmount(getValue(netSales));
        r.setDiscountAmount(getValue(discount));
        r.setVATAmount(getValue(o.getTransactionDetail().getTax()));
        r.setServiceTaxAmount(getValue(null));
        r.setServiceChargeAmount(getValue(null));
        r.setNetSale(getValue(paidAmount));
        r.setPaymentMode("CASH");
        r.setTransactionStatus("SALE");
        return r;
    }

	private void validateTraffic(String api, int unitId, int userId)
            throws DuplicateRequestException, AuthenticationFailureException {
        if (unitId == 0 || userId == 0) {
            throw new AuthenticationFailureException("Unauthorised Access");
        }
        String key = getKey(unitId, userId);
        if (apiTraffic.containsKey(key)) {
            Long l = apiTraffic.get(key);
            if (l < System.currentTimeMillis()) {
                // 45 Minutes delay
                apiTraffic.put(key, System.currentTimeMillis() + 2700000);
            } else {
            	LOG.info("Blocked call for unit {} because of duplicate request with api{}", unitId, api);
               throw new DuplicateRequestException("Duplicate Request for Sales Data");
            }
        } else {
            // 45 Minutes Delay
            apiTraffic.put(key, System.currentTimeMillis() + 2700000);
        }
    }

    private PartnerSaleRecords generatePartnerSaleReport(int unitId, Date startTime, Date endTime)
            throws DataNotFoundException, JAXBException {
       OrderFetchStrategy strategy = new OrderFetchStrategy(false, true, false, false, false, null, -1, true);
       List<Order> orders = getOrderDetails(unitId, startTime, endTime, strategy);
        PartnerSaleRecords sr = new PartnerSaleRecords();
        sr.setPartnerRecords(new ArrayList<PartnerSaleRecord>());
        for (Order o : orders) {
            if (OrderStatus.CANCELLED.equals(o.getStatus())) {
                continue;
            }
            int zeroTaxItemCount = 0;
            for (OrderItem i : o.getOrders()) {
                if (AppUtils.isGiftCard(i.getCode())) {
                    zeroTaxItemCount = zeroTaxItemCount + 1;
                }
            }
            if (zeroTaxItemCount >= o.getOrders().size()) {
                // skip for only zero tax bill
                continue;
            }
            PartnerSaleRecord r = createPartnerSaleRecord(o);
            sr.getPartnerRecords().add(r);
        }
        return sr;
    }

    private PartnerSaleRecord createPartnerSaleRecord(Order o) {
        PartnerSaleRecord r = new PartnerSaleRecord();
        TransactionDetail td = o.getTransactionDetail();

        BigDecimal netSales = BigDecimal.ZERO;
        BigDecimal discount = BigDecimal.ZERO;
        BigDecimal paidAmount = BigDecimal.ZERO;
        BigDecimal zeroTaxAmount = BigDecimal.ZERO;

        for (OrderItem i : o.getOrders()) {
            if (AppUtils.isGiftCard(i.getCode())) {
                zeroTaxAmount = zeroTaxAmount.add(i.getPrice().multiply(new BigDecimal(i.getQuantity())));
            }
        }
        netSales = td.getTaxableAmount().subtract(zeroTaxAmount);
        discount = AppUtils.subtract(td.getTotalAmount().subtract(zeroTaxAmount), netSales);
        paidAmount = netSales.add(o.getTransactionDetail().getTax());
        r.setOrderId(String.valueOf(o.getOrderId()));
        r.setBillDate(AppUtils.generateSimpleDateFormat(o.getBillingServerTime()));
        r.setNetSales(getValue(netSales));
        r.setDiscount(getValue(discount));
        r.setTotalTax(getValue(o.getTransactionDetail().getTax()));
        r.setGrossAmount(getValue(paidAmount));
        r.setAggregatorOrderId(o.getSourceId());
        r.setArea(masterCache.getChannelPartner(o.getChannelPartner()).getCode());
        r.setBrandId(masterCache.getBrandMetaData().get(o.getBrandId()).getBrandCode());
        r.setContainerCharges("0");
        r.setInvoiceNumber(o.getGenerateOrderId());
        r.setOrderId(String.valueOf(o.getOrderId()));
        r.setMenuPrice(getValue(o.getTransactionDetail().getTotalAmount()));
        r.setOrderType(o.getSource());
        r.setRestaurant(masterCache.getUnit(o.getUnitId()).getName());
        return r;
    }

    private String getValue(BigDecimal value) {
        return String.valueOf(value != null ? value.setScale(2, BigDecimal.ROUND_HALF_UP) : BigDecimal.ZERO);
    }

    private String getKey(int unitId, int userId) {
        return unitId + "@@" + userId;
    }
}
