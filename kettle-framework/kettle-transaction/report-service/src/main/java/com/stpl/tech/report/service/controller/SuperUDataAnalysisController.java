package com.stpl.tech.report.service.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.stpl.tech.kettle.data.model.SuperUAlertsData;
import com.stpl.tech.kettle.data.model.SuperUOrderAnalysisData;
import com.stpl.tech.kettle.data.model.SuperUOrderLevelData;
import com.stpl.tech.master.core.external.notification.service.FireStoreService;
import com.stpl.tech.report.service.model.SuperUAlert;
import com.stpl.tech.report.service.model.SuperUDataResponse;
import com.stpl.tech.report.service.service.SuperUDataAnalysisService;
import com.stpl.tech.report.service.service.SuperUDataCache;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.text.ParseException;
import java.util.List;

import static com.stpl.tech.report.service.core.ReportServiceConstants.API_VERSION;
import static com.stpl.tech.report.service.core.ReportServiceConstants.SEPARATOR;
import static com.stpl.tech.report.service.core.ReportServiceConstants.SUPERU;

@RestController
@RequestMapping(value = API_VERSION+SEPARATOR+SUPERU)
@Log4j2
public class SuperUDataAnalysisController {


    @Autowired
    SuperUDataAnalysisService superUDataAnalysis;

    @Autowired
    SuperUDataCache superUDataCache;

    @Autowired
    private FireStoreService fireStoreService;

    @PostMapping("/generate-data")
    public List<SuperUOrderAnalysisData> generateData(@RequestParam("unitId") Integer unitId) throws JsonProcessingException, ParseException {
      try{
          List<SuperUOrderAnalysisData> superUOrderAnalysisData =  superUDataAnalysis.generateRatingData(unitId);
          fireStoreService.sendSuperUNotificationThroughFireStore(unitId,false);
          return null;
      }catch (Exception e){
          log.info("Error while generating data for unit id : {}, message : {}",unitId,e.getMessage());
          throw  e;
      }
    }


    @GetMapping("/get-superu-data")
    public SuperUDataResponse getSuperUData(@RequestParam("unitId") Integer unitId,@RequestParam("pageNumber") Integer pageNumber, @RequestParam("pageSize") Integer pageSize,@RequestParam(value = "cacheRefresh",required = false) boolean cacheRefresh ) throws JsonProcessingException{
        return superUDataCache.getSuperUData(unitId,pageNumber,pageSize,cacheRefresh);
    }

    @PostMapping("/mark-read")
    public boolean markRead(@RequestParam("orderLevelId") Integer orderLevelId){
        return superUDataAnalysis.markFeedbackRead(orderLevelId);
    }

    @PostMapping("/alert-notify")
    public void sendAlertNotification(@RequestParam("unitId") Integer unitId){
        log.info("### Sending alert notification on firebase for unitId : "+unitId);
        fireStoreService.sendSuperUNotificationThroughFireStore(unitId,true);
    }

    @GetMapping("/get-alert")
    public SuperUAlert sendAlertData(@RequestParam("unitId") Integer unitId){
          return superUDataAnalysis.getSuperAlertData(unitId);
    }

}
