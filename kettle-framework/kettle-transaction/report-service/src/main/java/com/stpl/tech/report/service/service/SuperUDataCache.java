package com.stpl.tech.report.service.service;

import com.stpl.tech.kettle.data.model.SuperUOrderLevelData;
import com.stpl.tech.report.service.model.SuperUDataResponse;

public interface SuperUDataCache {
    void addDataToCache(Integer unitId, SuperUOrderLevelData superUOrderLevelData, boolean updateAggregation);

    void updateOrderLevelData(Integer unitId, SuperUOrderLevelData orderLevelData);

    SuperUDataResponse getSuperUData(Integer unitId, Integer page, Integer pageSize,boolean cacheRefresh);
}
