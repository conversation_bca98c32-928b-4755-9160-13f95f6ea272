package com.stpl.tech.report.service.dao.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import javax.persistence.Query;

import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import com.stpl.tech.kettle.core.data.vo.OrderFetchStrategy;
import com.stpl.tech.kettle.data.converter.DataConverter;
import com.stpl.tech.kettle.data.dao.impl.AbstractDaoImpl;
import com.stpl.tech.kettle.data.model.CustomerAddressInfo;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.data.model.OrderInvoiceDetail;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.RecipeCache;
import com.stpl.tech.report.service.dao.SaleAPIDao;

@Repository
public class SaleAPIDaoImpl extends AbstractDaoImpl implements SaleAPIDao {

    private static final Logger LOG = LoggerFactory.getLogger(SaleAPIDaoImpl.class);

    @Autowired
    private MasterDataCache masterCache;
    @Autowired
    private RecipeCache recipeCache;

    @Autowired
    private EnvironmentProperties environmentProperties;

    @Override
    public List<Order> getOrderDetails(int unitId, Date startTime, Date endTime, OrderFetchStrategy strategy) {
        List<Order> orders = new ArrayList<Order>();
        Query query = manager.createQuery(
                "FROM OrderDetail E where E.unitId = :unitId and E.billingServerTime >= :startTime and E.billingServerTime <= :endTime");
        query.setParameter("unitId", unitId);
        query.setParameter("startTime", startTime);
        query.setParameter("endTime", endTime);
        List<OrderDetail> list = query.getResultList();
        if (list == null) {
            LOG.info(String.format("Did not find any order for unit %d between %s and %s", unitId, startTime, endTime));
            return orders;
        }
        for (OrderDetail order : list) {
            orders.add(convertOrder(order, strategy));
        }
        return orders;
    }

    private Order convertOrder(OrderDetail order, OrderFetchStrategy strategy) {
        if ((strategy == null || (strategy != null && !strategy.isDonotFetchCustomerAddress()))
                && order.getDeliveryAddress() != null && !order.getDeliveryAddress().equals(0)) {
            CustomerAddressInfo info = getOrderAddressInfo(order.getDeliveryAddress());
            if (info != null && info.getName() != null) {
                order.setCustomerName(
                        info.getName().substring(0, info.getName().length() > 45 ? 45 : info.getName().length()));
            }
        }
        if (Objects.nonNull(order.getInvoiceId())) {
            try {
                OrderInvoiceDetail orderInvoiceDetail = getInvoiceDetail(order.getGeneratedOrderId());
                order.setInvoiceDetail(orderInvoiceDetail);
            } catch (Exception e) {
                LOG.info("Unable to fetch invoice Id for this order");
            }
        }
        return DataConverter.convert(masterCache, order, strategy,recipeCache,environmentProperties);
    }

    private CustomerAddressInfo getOrderAddressInfo(Integer addressId) {
        LOG.info(String.format("Finding order address for addressId %s ", addressId));
        CustomerAddressInfo info = find(CustomerAddressInfo.class, addressId);
        return info;
    }

    private OrderInvoiceDetail getInvoiceDetail(String generatedOrderId) {
        try {
            Query query = manager.createQuery("FROM OrderInvoiceDetail E where E.orderId = :orderId ");
            query.setParameter("orderId", generatedOrderId);
            OrderInvoiceDetail orderInvoiceDetail = (OrderInvoiceDetail) query.getSingleResult();
            return orderInvoiceDetail;
        } catch (Exception e) {
            LOG.info("Error while fetching order invoice detail for order id");
        }
        return null;
    }
}
