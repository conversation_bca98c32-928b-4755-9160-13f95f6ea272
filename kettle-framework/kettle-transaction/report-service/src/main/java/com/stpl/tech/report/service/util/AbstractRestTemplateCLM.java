package com.stpl.tech.report.service.util;

import com.stpl.tech.report.service.model.CleverTapEndpoints;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.web.client.RestTemplate;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.Arrays;
import java.util.Map;

public class AbstractRestTemplateCLM {
    private static final Logger LOG = LoggerFactory.getLogger(AbstractRestTemplateCLM.class);


    public static <T> T postWithHeaders(CleverTapEndpoints endPoint, Object body, Class<T> clazz, String accountId, String passcode, EnvType props) {
        try {
            RestTemplate restTemplate = new RestTemplate();
            HttpHeaders requestHeaders = new HttpHeaders();
            requestHeaders.set("X-CleverTap-Account-Id", accountId);
            requestHeaders.set("X-CleverTap-Passcode", passcode);
            requestHeaders.set("content-type", MediaType.APPLICATION_JSON_UTF8_VALUE);
            HttpEntity<?> requestEntity;
            if (body != null) {
                requestEntity = new HttpEntity(body, requestHeaders);
            } else {
                requestEntity = new HttpEntity(requestHeaders);
            }
            MappingJackson2HttpMessageConverter jackson = new MappingJackson2HttpMessageConverter();
            jackson.setSupportedMediaTypes(Arrays.asList(MediaType.APPLICATION_JSON_UTF8));
            restTemplate.getMessageConverters().add(jackson);
            return restTemplate.postForObject(endPoint.getUrl(AppUtils.isDev(props)), requestEntity, clazz);
        } catch (Exception e) {
            LOG.error("ERROR", e);
            throw e;
        }
    }



    public static <T> T postWithoutHeaders(String endPoint, Object body, Class<T> clazz) {
        try {
            RestTemplate restTemplate = new RestTemplate();
            HttpHeaders requestHeaders = new HttpHeaders();
            requestHeaders.set("content-type", MediaType.APPLICATION_JSON_UTF8_VALUE);
            HttpEntity<?> requestEntity;
            requestEntity = new HttpEntity(body, requestHeaders);
            MappingJackson2HttpMessageConverter jackson = new MappingJackson2HttpMessageConverter();
            jackson.setSupportedMediaTypes(Arrays.asList(MediaType.APPLICATION_JSON_UTF8));
            restTemplate.getMessageConverters().add(jackson);
            LOG.info("Sending Request to {}, payload {} ", endPoint, requestEntity);
            return restTemplate.postForObject(endPoint, requestEntity, clazz);
        } catch (Exception e) {
            LOG.error("ERROR", e);
            throw e;
        }
    }

    public static String setUriVariables(String endpoint, Map<String, ?> uriVariables) {
        if (uriVariables != null) {
            for (Map.Entry<String, ?> entry : uriVariables.entrySet()) {
                LOG.info("Parameter : " + entry.getKey() + ", Value : " + entry.getValue());
                String key = "{" + entry.getKey() + "}";
                if (endpoint.toLowerCase().contains(key.toLowerCase())) {
                    endpoint = endpoint.replace(key, (CharSequence) entry.getValue());
                }
            }
        }

        return endpoint;
    }

    public static String appendQueryParams(String endpoint, Map<String, ?> paramMap) {
        if (paramMap != null) {
            endpoint += "?";
            StringBuilder endpointBuilder = new StringBuilder(endpoint);
            for (String key : paramMap.keySet()) {
                endpointBuilder.append(key).append("=").append(paramMap.get(key).toString()).append("&");
            }
            endpoint = endpointBuilder.toString();
            endpoint = endpoint.substring(0, endpoint.length() - 1);
        }
        return endpoint;
    }

    public static <T> T getWithHeaders(CleverTapEndpoints endPoint, Map<String, ?> uriVariables, Class<T> clazz, String accountId, String passcode, EnvType envType) {
        try {
            RestTemplate restTemplate = new RestTemplate();
            HttpHeaders requestHeaders = new HttpHeaders();
            requestHeaders.set("X-CleverTap-Account-Id", accountId);
            requestHeaders.set("X-CleverTap-Passcode", passcode);
            requestHeaders.set("content-type", MediaType.APPLICATION_JSON_UTF8_VALUE);
            HttpEntity<?> requestEntity;

            requestEntity = new HttpEntity(requestHeaders);

            MappingJackson2HttpMessageConverter jackson = new MappingJackson2HttpMessageConverter();
            jackson.setSupportedMediaTypes(Arrays.asList(MediaType.APPLICATION_JSON_UTF8));
            restTemplate.getMessageConverters().add(jackson);
            String url = endPoint.getUrl(AppUtils.isDev(envType));
            url = setUriVariables(url, uriVariables);
            URI uri = null;
            try {
                uri = new URI(url);
            } catch (URISyntaxException e) {
                LOG.error("Error in URL::::");
                e.printStackTrace();
                return null;
            }
            return (T) restTemplate.exchange(uri, HttpMethod.GET, requestEntity, clazz).getBody();
        } catch (Exception e) {
            LOG.error("ERROR  while getting user profile {}", e.getMessage());
            throw e;
        }
    }
}
