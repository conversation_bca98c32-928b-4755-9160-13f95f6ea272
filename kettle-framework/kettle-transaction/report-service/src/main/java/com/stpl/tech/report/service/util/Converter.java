package com.stpl.tech.report.service.util;

import com.stpl.tech.kettle.data.model.SuperUAlertsData;
import com.stpl.tech.report.service.model.SuperUAlert;

public class Converter {

    static public SuperUAlert convert(SuperUAlertsData superUAlertsData){
        SuperUAlert superUAlert = new SuperUAlert();
        superUAlert.setAlertId(superUAlertsData.getAlertId());
        superUAlert.setStoreName(superUAlertsData.getStoreName());
        superUAlert.setErrorType(superUAlertsData.getErrorType());
        superUAlert.setDescription(superUAlertsData.getDescription());
        superUAlert.setTimestamp(superUAlertsData.getTimestamp());
        superUAlert.setUnitId(superUAlertsData.getUnitId());
        return  superUAlert;
    }

}
