package com.stpl.tech.report.service.service;

import java.lang.reflect.InvocationTargetException;
import java.security.GeneralSecurityException;

import org.codehaus.jettison.json.JSONException;

import com.stpl.tech.kettle.domain.model.DataEncryptionRequest;

public interface DataEncryptionService {

	String encryptData(String plainText) throws GeneralSecurityException, JSONException;

	String decryptData(String plainText) throws GeneralSecurityException;

	boolean bulkEncryptDataKettle(DataEncryptionRequest request)
			throws GeneralSecurityException, IllegalAccessException, IllegalArgumentException,
			InvocationTargetException, NoSuchMethodException, SecurityException;

	boolean bulkEncryptDataMaster(DataEncryptionRequest request)
			throws GeneralSecurityException, IllegalAccessException, IllegalArgumentException,
			InvocationTargetException, NoSuchMethodException, SecurityException;

	boolean bulkEncryptDataRekognition(DataEncryptionRequest request)
			throws GeneralSecurityException, IllegalAccessException, IllegalArgumentException,
			InvocationTargetException, NoSuchMethodException, SecurityException;
}
