package com.stpl.tech.report.service.model;

import com.stpl.tech.util.excelparser.annotations.ExcelField;
import com.stpl.tech.util.excelparser.annotations.ExcelObject;
import com.stpl.tech.util.excelparser.annotations.ExcelSheet;
import com.stpl.tech.util.excelparser.annotations.ParseType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;

@ExcelSheet(value = "Food Rating Zomato")
@ExcelObject(parseType = ParseType.ROW, start = 1, end = 0)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ZomatoFoodRating {
    @ExcelField
    private String orderId;
    @ExcelField
    private String resId;
    @ExcelField
    private String resName;
    @ExcelField
    private String cityName;
    @ExcelField
    private String dt;
    @ExcelField
    private String deliveryRating;
    @ExcelField
    private String foodRating;
    @ExcelField
    private String ors;
    @ExcelField
    private String orsReason;
}
