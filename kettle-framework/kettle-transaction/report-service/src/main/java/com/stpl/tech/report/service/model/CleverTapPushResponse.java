package com.stpl.tech.report.service.model;



import java.util.List;

public class CleverTapPushResponse {
    private String status;
    private int processed;
    private List<Object> unprocessed;
    private List<CleverTapProfilePushTrack> profiles;
    private List<EventPushTrack> events;

    public CleverTapPushResponse() {
    }

    public CleverTapPushResponse(String status, int processed) {
        this.status = status;
        this.processed = processed;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public int getProcessed() {
        return processed;
    }

    public void setProcessed(int processed) {
        this.processed = processed;
    }

    public List<Object> getUnprocessed() {
        return unprocessed;
    }

    public void setUnprocessed(List<Object> unprocessed) {
        this.unprocessed = unprocessed;
    }

    public List<CleverTapProfilePushTrack> getProfiles() {
        return profiles;
    }

    public void setProfiles(List<CleverTapProfilePushTrack> profiles) {
        this.profiles = profiles;
    }

    public List<EventPushTrack> getEvents() {
        return events;
    }

    public void setEvents(List<EventPushTrack> events) {
        this.events = events;
    }
}
