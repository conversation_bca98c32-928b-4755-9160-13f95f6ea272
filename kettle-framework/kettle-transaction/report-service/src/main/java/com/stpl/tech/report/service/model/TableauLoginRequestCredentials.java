package com.stpl.tech.report.service.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class TableauLoginRequestCredentials implements Serializable {

    private String personalAccessTokenName;
    private String personalAccessTokenSecret;
    private Object site;

}
