package com.stpl.tech.report.service.controller;

import com.stpl.tech.kettle.xml.model.PartnerSaleRecords;
import com.stpl.tech.kettle.xml.model.SaleRecords;
import com.stpl.tech.master.core.exception.AuthenticationFailureException;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DuplicateRequestException;
import com.stpl.tech.report.service.service.SaleAPIService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.annotation.Nullable;
import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.MediaType;
import javax.xml.bind.JAXBException;

import static com.stpl.tech.report.service.core.ReportServiceConstants.API_VERSION;
import static com.stpl.tech.report.service.core.ReportServiceConstants.SALES_API_ROOT_CONTEXT;
import static com.stpl.tech.report.service.core.ReportServiceConstants.SEPARATOR;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + SALES_API_ROOT_CONTEXT)
public class SalesAPIResource {

    @Autowired
    private SaleAPIService saleAPIService;

    @RequestMapping(method = RequestMethod.GET, value = "json/sales/partner/date", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public PartnerSaleRecords generatePartnerSalesReportForDayJSON(HttpServletRequest request, @RequestParam String businessDate, @Nullable @RequestParam String byPassValidation)
            throws DataNotFoundException, JAXBException, AuthenticationFailureException, DuplicateRequestException {
        return saleAPIService.generatePartnerReportForDay("json/sales/partner/date",request, businessDate,byPassValidation);
    }

    @RequestMapping(method = RequestMethod.GET, value = "json/sales/partner/hourly", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public PartnerSaleRecords generatePartnerHourlyReportsXML(HttpServletRequest request)
            throws DataNotFoundException, JAXBException, AuthenticationFailureException, DuplicateRequestException {
        return saleAPIService.generatePartnerHourlyReports("json/sales/partner/hourly",request);
    }

    @RequestMapping(method = RequestMethod.GET, value = "sales/hourly", produces = MediaType.APPLICATION_XML)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public SaleRecords generateHourlyReportsXML(HttpServletRequest request)
            throws DataNotFoundException, JAXBException, AuthenticationFailureException, DuplicateRequestException {
        return saleAPIService.generateHourlyReports("sales/hourly", request);
    }

    @RequestMapping(method = RequestMethod.GET, value = "json/sales/hourly", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public SaleRecords generateHourlyReportsJSON(HttpServletRequest request)
            throws DataNotFoundException, JAXBException, AuthenticationFailureException, DuplicateRequestException {
        return saleAPIService.generateHourlyReports("json/sales/hourly",request);
    }

    @RequestMapping(method = RequestMethod.GET, value = "sales/date", produces = MediaType.APPLICATION_XML)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public SaleRecords generatePathfinderReportForDayXML(HttpServletRequest request, @RequestParam String businessDate)
            throws DataNotFoundException, JAXBException, AuthenticationFailureException, DuplicateRequestException {
        return saleAPIService.generatePathfinderReportForDay("sales/date",request, businessDate);
    }

    @RequestMapping(method = RequestMethod.GET, value = "json/sales/date", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public SaleRecords generatePathfinderReportForDayJSON(HttpServletRequest request, @RequestParam String businessDate)
            throws DataNotFoundException, JAXBException, AuthenticationFailureException, DuplicateRequestException {
        return saleAPIService.generatePathfinderReportForDay("json/sales/date",request, businessDate);
    }
}
