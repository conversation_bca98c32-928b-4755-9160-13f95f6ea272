package com.stpl.tech.report.service.service.impl;

import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.report.service.model.MaintenanceTicket;
import com.stpl.tech.report.service.service.MaintenanceService;
import com.stpl.tech.util.AppConstants;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.text.SimpleDateFormat;
import java.util.Iterator;
import java.util.Objects;
import java.nio.file.Path;

@Service
@Configuration
public class MaintenanceServiceImpl implements MaintenanceService {

    private static final Logger LOG = LoggerFactory.getLogger(MaintenanceServiceImpl.class);

    @Value("${maintenance.temp.folder:null}")
    String basePath;

    @Value("${maintenance.api.url}")
    String maintenanceApiEndpoint;

    @Override
    public Resource createMaintenanceTicket(MultipartFile inputFile, String apiKey){
        try {
            if(basePath == null){
                LOG.error("################# ERROR : basePath value is null #####################");
                return null;
            }
            Path tempPath = Paths.get(basePath).normalize();
            if(!Files.isDirectory(tempPath)){
                Files.createDirectories(tempPath);
            }
            Files.copy(inputFile.getInputStream(), Paths.get(basePath+"/"+inputFile.getOriginalFilename()), StandardCopyOption.REPLACE_EXISTING);
            Workbook workbook = new XSSFWorkbook(inputFile.getInputStream());
            Sheet sheet = workbook.getSheetAt(0);
            Iterator<Row> rowIterator = sheet.iterator();
            rowIterator.next();
            while (rowIterator.hasNext()) {
                Row row = rowIterator.next();
                Integer phone  = Integer.valueOf(Objects.equals(row.getCell(5).getStringCellValue(), "null") ? "0" : row.getCell(5).getStringCellValue());
                SimpleDateFormat formatter = new SimpleDateFormat("MM/dd/yyyy");

                MaintenanceTicket mt = MaintenanceTicket.builder().
                        alert(row.getCell(0).getBooleanCellValue())
                        .autorespond(row.getCell(1).getBooleanCellValue())
                        .source(row.getCell(2).getStringCellValue())
                        .name(row.getCell(3).getStringCellValue())
                        .email(row.getCell(4).getStringCellValue())
                        .phone(phone == 0 ? null : phone)
                        .subject(row.getCell(6).getStringCellValue())
                        .checkList(row.getCell(7).getStringCellValue())
                        .ip(row.getCell(8).getStringCellValue())
                        .topicId((int) row.getCell(9).getNumericCellValue())
                        .message(row.getCell(10).getStringCellValue())
                        .date(formatter.format(row.getCell(11).getDateCellValue()))
                        .dateTime(formatter.format(row.getCell(12).getDateCellValue()))
                        .eqpName(row.getCell(13).getStringCellValue())
                        .mntcZone(row.getCell(14).getStringCellValue())
                        .build();
                        String response = generateTicket(mt,apiKey);
                        row.createCell(15).setCellValue(response);
            }
            FileOutputStream outFile = new FileOutputStream(basePath+"/"+inputFile.getOriginalFilename());
            workbook.write(outFile);
            outFile.close();

           return  new UrlResource(new File(basePath+"/"+inputFile.getOriginalFilename()).toURI());

        } catch (IOException e) {
            LOG.error("################# ERROR :Exception while creating maintenance tickets  #####################");
            e.printStackTrace();
            return null;
        }


    }

    private String generateTicket(MaintenanceTicket mt, String apiKey){
     try {
         HttpPost requestObject = new HttpPost(maintenanceApiEndpoint);
         String consumptionDataJson = WebServiceHelper.convertToString(mt);
         HttpEntity httpEntity = new StringEntity(consumptionDataJson, AppConstants.CHARSET);
         requestObject.setHeader("X-API-Key",apiKey);
         requestObject.setEntity(httpEntity);
         HttpResponse res =  WebServiceHelper.postRequestWithNoTimeout(requestObject);
         String resText =  IOUtils.toString(res.getEntity().getContent(), StandardCharsets.UTF_8.name());
         if(!resText.isEmpty()){
           return resText;
         }
         return res.getStatusLine().getReasonPhrase()+" "+res.getStatusLine().getStatusCode();
     }catch (Exception ex){
         LOG.error("################# ERROR : Exception in ticket server api call #####################");
         ex.printStackTrace();
          return "Exception in api call";
     }
    }

    @Override
    public void deleteTempFiles() {
        try {
            FileUtils.cleanDirectory(new File(basePath));
        } catch (IOException e) {
            LOG.error("######## Error while cleaning maintenance temp files #########");
        }
    }
}
