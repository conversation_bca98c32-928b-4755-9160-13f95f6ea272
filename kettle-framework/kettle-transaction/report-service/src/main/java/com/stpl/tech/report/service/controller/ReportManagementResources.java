/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.report.service.controller;

import com.stpl.tech.kettle.customer.dao.LoyaltyDao;
import com.stpl.tech.kettle.customer.service.CustomerEventService;
import com.stpl.tech.kettle.customer.service.CustomerService;
import com.stpl.tech.kettle.data.model.LoyaltyScore;
import com.stpl.tech.kettle.report.metadata.model.ReportSummary;
import com.stpl.tech.kettle.report.metadata.model.ReportVersionDetail;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.exception.FileDownloadException;
import com.stpl.tech.master.core.external.report.service.ReportManagementService;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.master.util.CustomerEventType;
import com.stpl.tech.master.data.model.XMLReportDefinitionData;
import com.stpl.tech.master.domain.model.CouponCloneResponse;
import com.stpl.tech.report.service.core.ReportProperties;
import com.stpl.tech.report.service.model.CouponMappingRequest;
import com.stpl.tech.report.service.model.DeliveryCouponResponse;
import com.stpl.tech.report.service.service.CouponService;
import com.stpl.tech.report.service.service.UnitReportService;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;

import javax.annotation.Nullable;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.stpl.tech.report.service.core.ReportServiceConstants.API_VERSION;
import static com.stpl.tech.report.service.core.ReportServiceConstants.REPORT_METADATA_ROOT_CONTEXT;
import static com.stpl.tech.report.service.core.ReportServiceConstants.SEPARATOR;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + REPORT_METADATA_ROOT_CONTEXT)
public class ReportManagementResources extends AbstractResources {

    private static final Logger LOG = LoggerFactory.getLogger(ReportManagementResources.class);

    @Autowired
    private ReportManagementService reportManagementService;


    @Autowired
    private ReportProperties props;

    @Autowired
    private UnitReportService unitReportService;

    @Autowired
    private CouponService couponService;

    @Autowired
    private LoyaltyDao loyaltyDao;

    @Autowired
    private CustomerService customerService;
    @Autowired
    private CustomerEventService customerEventService;

    @RequestMapping(value = "report/report-version-details", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<ReportSummary> getReportVersionListById(@RequestBody Integer reportId) {
        return reportManagementService.getReportVersionHistoryById(reportId);
    }

    @RequestMapping(value = "report/report-categories", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public List<XMLReportDefinitionData> getAllReportCategory(@RequestParam final String executionEnvironment) {
        return reportManagementService.getAllReportCategories(executionEnvironment);
    }

    @RequestMapping(value = "report/add-new", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public int addNewReport(@RequestBody ReportSummary report) throws DataUpdationException {
        return reportManagementService.addNewReport(report);
    }

    @RequestMapping(value = "report/change-status", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public int updateReportStatus(@RequestParam final int reportId, @RequestParam final String status) {
        return reportManagementService.updateStatus(reportId, status);
    }

    @RequestMapping(value = "report/mark-default-version", method = RequestMethod.GET, produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public int markReportVersion(@RequestParam final int reportId, @RequestParam final int versionId) {
        return reportManagementService.markDefaultReportVersion(reportId, versionId);
    }

    @RequestMapping(value = "report/add-new-version", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public int addNewReportVersion(@RequestBody ReportVersionDetail report) {
        return reportManagementService.addNewReportVersion(report);
    }

    @RequestMapping(method = RequestMethod.POST, value = "report/upload-new-report-version", consumes = MediaType.MULTIPART_FORM_DATA)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void uploadDocument(HttpServletRequest request,
                               @RequestParam(value = "file") final MultipartFile file,
                               @RequestParam(value = "comments") final String comments,
                               @RequestParam(value = "reportId") final int reportId)
            throws IOException {
        LOG.info("Request to upload New Report Version - " + reportId);
        reportManagementService.uploadNewReportVersion(props.getS3ReportBucket(), file, comments, reportId);
    }

	@RequestMapping(method = RequestMethod.POST, value = "report/report-download")
	public void downloadReport(HttpServletResponse response, @RequestBody Integer versionId)
			throws DataNotFoundException, FileDownloadException, IOException {

		if (versionId == 0) {
			reportManagementService.downloadManagementReport(response, "template/ReportTemplate.xml");
			return;
		} else {
			reportManagementService.downloadManagementReport(response, versionId);
			return;
		}

	}

    @RequestMapping(method = RequestMethod.GET,value = "report/unit-closure",produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public View getReportForUnitClosure(@RequestParam(required = false) Integer unitId, @RequestParam Integer noOfDays){
        LOG.info("Requesting to issue report");
        return unitReportService.getReportForUnitClosure(unitId,noOfDays);
    }

	@RequestMapping(method = RequestMethod.POST, value = "report/coupon/burn-loyalty-points", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public CouponCloneResponse burnLoayaltyPoints(@RequestParam Integer loyalityPoints,
			@RequestParam Integer validityDays, @RequestParam String contactNumber, @RequestParam String cloneCode,
			@RequestParam String prefix) {
		CouponCloneResponse response = new CouponCloneResponse();
		contactNumber = AppUtils.getValidContactNUmber(contactNumber);
        try {
            Integer customerId = customerService.getCustomerId(contactNumber);
            LoyaltyScore loyaltyScore = loyaltyDao.getScore(customerId);
            boolean isEligible = couponService.validateLoyaltyPoint(loyalityPoints, loyaltyScore);
            if (isEligible) {
                response = couponService.generateCoupon(validityDays, contactNumber, cloneCode, prefix);
                try {
                    boolean update = couponService.updateLoyalty(loyalityPoints, customerId, contactNumber, response,
                            loyaltyScore);
                    if (update) {
                        return response;
                    }
                } catch (Exception e) {
                    LOG.info("Unable to update Loyalty for customer with contact Number  ; {}", contactNumber);
                }
            } else {
                LOG.info("Coupon is not Applicable for offer-purchase : {}", contactNumber);
            }
        }
        catch (Exception e){
            LOG.info("Customer is not a Chaayos Customer");
        }
        List<String> error = new ArrayList<>();
        error.add("Either you Don't have Enough Loyalty Point or this is valid for limited customers only");
        response.setErrors(error);
		return response;
	}


    @RequestMapping(method = RequestMethod.POST,value = "loyalty-burn-delivery-coupon",produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public DeliveryCouponResponse getCouponForDelivery(String contactNumber, Integer channelPartnerId, String code, Integer loyalityPoints, Integer getValidityInDays){
        return unitReportService.createDeliveryCoupon(contactNumber,channelPartnerId,code,loyalityPoints,getValidityInDays);

    }

    @RequestMapping(method = RequestMethod.GET,value = "get-delivery-coupon-code",produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public String getDeliveryCouponCode(String contactNumber){
        return unitReportService.getDeliveryCouponCode(contactNumber);
    }

    @RequestMapping(method = RequestMethod.GET,value = "get-redeemed-coupon-sheet")
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public View getRedeemedCouponSheet(){
        LOG.info("Request to get Redeemed Coupon sheet");
        return unitReportService.getRedeemedCouponSheet();
    }

    @RequestMapping(method = RequestMethod.POST,value = "upload-redeemed-coupon-sheet",
            consumes = MediaType.MULTIPART_FORM_DATA,produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void uploadRedeemedCouponSheet(HttpServletResponse response,@RequestParam(value = "file") final MultipartFile file,@RequestParam(value = "updateBy") Integer updateBy) throws Exception {
        unitReportService.uploadRedeemedCouponSheet(response,file,updateBy);

    }

    @RequestMapping(method = RequestMethod.POST, value = "add-event-month", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean addCustomerEventMonth(@RequestParam String contactNumber, @RequestParam Integer eventMonth, @RequestParam @Nullable Integer eventDate,
                                    @RequestParam String source, @RequestParam CustomerEventType eventType,
                                    @RequestParam @Nullable String forceUpdate) {
        LOG.info("Request to Add {} Month for contactNumber : {} by {}",eventType, contactNumber,source);
//        return unitReportService.addBirthdayMonth(contactNumber, eventMonth, eventDate,source, eventType, forceUpdate);
        return customerEventService.addCustomerEventMonth(contactNumber, eventMonth, eventDate,source, eventType, forceUpdate);

    }

    @RequestMapping(method = RequestMethod.POST, value = "generate-coupons-for-event-month", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean generateCouponForCustomerEventMonth() {
        LOG.info("Request to generate coupons for all customer Events");
         return unitReportService.generateCouponForCustomerEventMonth();

    }


    @RequestMapping(method = RequestMethod.GET,value = "get-customer-event-month",produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public Integer getCustomerEventMonth(@RequestParam @Nullable String contactNumber,@RequestParam @Nullable Integer customerId,@RequestParam CustomerEventType customerEventType){
        LOG.info("Request to get Customer {} Month",customerEventType);
        return unitReportService.getEventMonth(contactNumber,customerId,customerEventType);
    }

    @RequestMapping(method = RequestMethod.GET,value = "get-customer-event-detail",produces = MediaType.TEXT_PLAIN)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public String getCustomerEventDetails(@RequestParam @Nullable String contactNumber,@RequestParam @Nullable Integer customerId,@RequestParam CustomerEventType customerEventType){
        LOG.info("Request to get Customer {} Details",customerEventType);
        return unitReportService.getCustomerEventDetails(contactNumber,customerId,customerEventType);
    }

    @RequestMapping(method = RequestMethod.GET,value = "get-customer-month-offer",produces = MediaType.TEXT_PLAIN)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public String getCustomerEventOffer(@RequestParam String contactNumber,@RequestParam Integer offerNumber,@RequestParam CustomerEventType customerEventType){
        LOG.info("Request to get {} Offer For Customer",customerEventType);
//        return couponService.getBirthdayOfferCouponCode(contactNumber,offerNumber);
        return couponService.getCustomerEventOfferCouponCode(contactNumber,offerNumber,customerEventType);
    }

    @RequestMapping(method = RequestMethod.GET,value = "publish-customer-month-event",produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean publishCustomerDayEvent(@RequestParam CustomerEventType customerEventType){
//        if(Objects.isNull(birthdayMonth) || birthdayMonth.equals(0)){
            return couponService.publishCustomerEventForEventMonth(customerEventType);
//        }else{
//            return couponService.publishEventForBirthdayDataForCurrentMonth(birthdayMonth);
//        }
    }

    @RequestMapping(method = RequestMethod.GET,value = "acknowledge-loyalty-event",produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public void acknowledgeLoyaltyEvent(@RequestParam String contactNumber,@RequestParam Integer loyaltyPoints){
        try {
            unitReportService.acknowledgeLoyaltyEvent(contactNumber,loyaltyPoints);
        }catch (Exception e){
            LOG.info("Error in pushing Acknowledge Events : {}",e.getMessage());
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = "sync-customer-events-data", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean syncCustomerEventDetailData() {
        LOG.info("Request to generate coupons for all customer Events");
        return unitReportService.syncCustomerEventDetailData();

    }

    @RequestMapping(method = RequestMethod.POST,value = "dynamic-coupon-mapping",produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CouponCloneResponse dynamicCouponMapping(@RequestBody CouponMappingRequest request){
        return couponService.dynamicCouponMapping(request);
    }

    @RequestMapping(method = RequestMethod.POST, value = "report/coupon/campaign", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CouponCloneResponse generateCouponForCampaign(@RequestParam String contactNumber,@RequestParam Integer validityDays,
                                                  @RequestParam @Nullable String acqToken, @RequestParam @Nullable String acSrc, @RequestParam String cloneCode,
                                                  @RequestParam String prefix) {
        LOG.info("Request To generate coupon through campaign for contact number : {}",contactNumber);
        return unitReportService.generateCouponForCustomerThroughCampaign(contactNumber,cloneCode,acSrc,acqToken,validityDays,prefix);
    }

    @RequestMapping(method = RequestMethod.POST, value = "report/cashback/campaign", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CouponCloneResponse generateCashbackForCampaign(@RequestParam String contactNumber,@RequestParam Integer validityDays,
                                                         @RequestParam @Nullable String acqToken, @RequestParam @Nullable String acSrc, @RequestParam String code) {
        LOG.info("Request To map cashback coupon through campaign for contact number : {}",contactNumber);
        return unitReportService.generateCouponForCashbackCampaign(contactNumber,code,acSrc,acqToken,validityDays);
    }






}
