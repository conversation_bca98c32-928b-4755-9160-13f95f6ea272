package com.stpl.tech.report.service.Entites.KettleAnalytics;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.math.BigInteger;
import java.util.Date;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "SWIGGY_ORS_RAW")
public class SwiggyOrsRaw {

    @Column(name = "BUSINESS_DATE")
    private Date date;
    @Column(name = "SWIGGY_RID")
    private Long swiggyRid;
    @Column(name = "BRAND_ID")
    private Long brandId;
    @Column(name = "ORDER_SOURCE_ID")
    @Id
    private String orderSourceId;
    @Column(name = "ORS" , columnDefinition = "text")
    private String ors;

}
