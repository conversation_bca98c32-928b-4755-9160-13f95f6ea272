package com.stpl.tech.report.service.service.impl;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.report.service.Entites.KettleAnalytics.CustomerWhatsAppCommunicationData;
import com.stpl.tech.report.service.dao.BulkDataProcessingDao;
import com.stpl.tech.report.service.dao.CustomerWhatsAppCommunicationDataDao;
import com.stpl.tech.report.service.model.CleverTapPushResponse;
import com.stpl.tech.report.service.model.CustomerWhatsAppCommunicationDomain;
import com.stpl.tech.report.service.model.WhatsAppMessageStats;
import com.stpl.tech.report.service.service.CleverTapDataPushServiceV1;
import com.stpl.tech.report.service.service.DashboardRefreshService;
import com.stpl.tech.report.service.service.WhatsAppDataService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import lombok.extern.log4j.Log4j2;
import org.apache.commons.io.FileUtils;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.net.URISyntaxException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipInputStream;

@Service
@Log4j2
@RefreshScope
public class WhatsAppDataServiceImpl implements WhatsAppDataService {

   private final EnvironmentProperties environmentProperties;
   private final CustomerWhatsAppCommunicationDataDao customerWhatsAppCommunicationDataDao;
   private final BulkDataProcessingDao bulkDataProcessingDao;

   private final DashboardRefreshService dashboardRefreshService;

   private CleverTapDataPushServiceV1 cleverTapDataPushServiceV1;

    private String dumpHostName;
    private String username;
    private String password;

    final static String DATE_FORMAT = "yyyy-MM-dd";
    private final String pluginPath = getClass().getClassLoader().getResource("plugins").getPath();
    private final String jobs_path = getClass().getClassLoader().getResource("whatsappDataPenthao").getPath();

    @Autowired
    public WhatsAppDataServiceImpl(EnvironmentProperties environmentProperties, CustomerWhatsAppCommunicationDataDao customerWhatsAppCommunicationDataDao,
                                   @Value("${dump.hostname}") String dumpHostName,@Value("${dump.username}") String username,@Value("${dump.password}") String password,
                                   BulkDataProcessingDao bulkDataProcessingDao, CleverTapDataPushServiceV1 cleverTapDataPushServiceV1, DashboardRefreshService dashboardRefreshService) {
        this.environmentProperties = environmentProperties;
        this.customerWhatsAppCommunicationDataDao = customerWhatsAppCommunicationDataDao;
        this.dumpHostName = dumpHostName;
        this.username = username;
        this.password = password;
        this.bulkDataProcessingDao = bulkDataProcessingDao;
        this.cleverTapDataPushServiceV1 = cleverTapDataPushServiceV1;
        this.dashboardRefreshService  = dashboardRefreshService;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "DumpDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean storeWhatsAppTemplateData(MultipartFile file,Boolean isTemplate) throws IOException {
        String filename = null;
        try{

            Path fileStorageLocation = Paths.get(environmentProperties.getBasePath()).normalize();
             filename = StringUtils.cleanPath(Objects.requireNonNull(file.getOriginalFilename()));
            log.info("########## File type : "+file.getContentType());
            if(!Objects.requireNonNull(file.getContentType()).equalsIgnoreCase("text/csv")){
                log.info("Please upload csv file");
                return false;
            }
            Path storeLocation = fileStorageLocation.resolve(filename);
            Files.copy(file.getInputStream(), storeLocation, StandardCopyOption.REPLACE_EXISTING);

            log.info("####################### Starting WhatsApp Data Transformation Process ######################");
            if(isTemplate){
                bulkDataProcessingDao.insertWhatsAppTemplateData(environmentProperties.getBasePath()+"/"+filename);
                log.info("####################### Ending WhatsApp Data Transformation Process ######################");
                return true;
            }

            bulkDataProcessingDao.insertAndPreProcessWhatsAppData(environmentProperties.getBasePath()+"/"+filename);
            customerWhatsAppCommunicationDataDao.syncWhatsappMessageData2024();
            log.info("####################### Ending WhatsApp Data Transformation Process ######################");
            return true;
        }catch (Exception e){
            log.error("Error in WhatsApp Data  Process  {}", e);
        }finally {
            if(filename != null){deleteFile(filename);}
            
        }
        return false;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "DumpDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateCustomerWhatsAppCommunicationData(String startDate, String endDate,Boolean isPastDaySkipped) throws ParseException, IOException {
       String fileName = null;
        try{
           Date endDateFormatted = new SimpleDateFormat(DATE_FORMAT).parse(endDate);
           Date startDateFormatted = new SimpleDateFormat(DATE_FORMAT).parse(startDate);

           List<WhatsAppMessageStats> whatsAppMessageStats = customerWhatsAppCommunicationDataDao.getWhatsAppMessagesStats(startDate,endDate);
           List<CustomerWhatsAppCommunicationData> allCustomerData = customerWhatsAppCommunicationDataDao.findAllByCustomerIdIn(whatsAppMessageStats.stream().map(WhatsAppMessageStats::getCustomerId).collect(Collectors.toList()));
           Map<Integer,CustomerWhatsAppCommunicationDomain> customerDataMap = allCustomerData.stream().map(e->new CustomerWhatsAppCommunicationDomain(e)).collect(Collectors.toMap(CustomerWhatsAppCommunicationDomain::getCustomerId,Function.identity()));

           List<CustomerWhatsAppCommunicationDomain> customerWhatsAppCommunicationDataList = new ArrayList<>();

           for(WhatsAppMessageStats whAppMessageStats : whatsAppMessageStats){
               CustomerWhatsAppCommunicationDomain customerData = customerDataMap.get(whAppMessageStats.getCustomerId());
               if(customerData !=null){
                   if(isPastDaySkipped ||  (endDateFormatted.after(customerData.getLastIncrementalDateKey()) && startDateFormatted.after(customerData.getLastIncrementalDateKey())) ){
                       updateCustomerStats(customerData,whAppMessageStats,endDateFormatted,isPastDaySkipped);
                   }else{
                       throw  new RuntimeException("Invalid date range, date range should be greater than last data updation date");
                   }
               }else{
                   customerData = new CustomerWhatsAppCommunicationDomain();
                   createCustomerStats(customerData,whAppMessageStats,endDateFormatted);
               }
               customerWhatsAppCommunicationDataList.add(customerData);
           }
            log.info("Total data size  : "+ customerWhatsAppCommunicationDataList.size());
            fileName = convertObjectListToCSV(customerWhatsAppCommunicationDataList,"customer_whatsapp_comm_Data");
           bulkDataProcessingDao.bulkInsertOrUpdateCustomerWhatsAppStats(fileName);
           log.info("file name : "+ fileName);
           return true;
       }catch (Exception e){
           log.error("######## Error while updating customer stats data  : {}",e.getMessage());
           e.printStackTrace();
           throw  e;
       }finally {
            if(fileName!=null){
                deleteFile(fileName);
            }
       }
    }


    private void updateCustomerStats(CustomerWhatsAppCommunicationDomain customerStats,WhatsAppMessageStats whAppMessageStats,Date endDate,Boolean isPastDaySkipped){
        customerStats.setTotalMessages(customerStats.getTotalMessages() + whAppMessageStats.getTotalMessages());
        customerStats.setReadMessages(customerStats.getReadMessages() + whAppMessageStats.getReadMessages());
        customerStats.setDeliveredMessages(customerStats.getDeliveredMessages() + whAppMessageStats.getDeliveredMessages());
        customerStats.setFailedMessages(customerStats.getFailedMessages() + whAppMessageStats.getFailedMessages());

        customerStats.setReadPercentage(((double)customerStats.getReadMessages()/(double)customerStats.getTotalMessages() * 100));
        customerStats.setDeliveredPercentage(((double)customerStats.getDeliveredMessages()/(double)customerStats.getTotalMessages() * 100));
        customerStats.setFailedPercentage(((double)customerStats.getFailedMessages()/(double)customerStats.getTotalMessages() * 100));

        customerStats.setLastMessageTime(whAppMessageStats.getLastMessageTime());
        customerStats.setLastMessageStatus(whAppMessageStats.getLastMessageStatus());

        customerStats.setLastDataUpdateTime(AppUtils.getCurrentTimestamp());
        if(!isPastDaySkipped) customerStats.setLastIncrementalDateKey(endDate);
    }

    private void createCustomerStats(CustomerWhatsAppCommunicationDomain customerStats,WhatsAppMessageStats whAppMessageStats,Date endDate){
        customerStats.setCustomerId(whAppMessageStats.getCustomerId());
        customerStats.setPhoneNumber(whAppMessageStats.getPhoneNumber());
        customerStats.setTotalMessages(whAppMessageStats.getTotalMessages());
        customerStats.setReadMessages(whAppMessageStats.getReadMessages());
        customerStats.setDeliveredMessages(whAppMessageStats.getDeliveredMessages());
        customerStats.setFailedMessages(whAppMessageStats.getFailedMessages());

        customerStats.setReadPercentage( ((double)customerStats.getReadMessages()/(double)customerStats.getTotalMessages() * 100));
        customerStats.setDeliveredPercentage(((double)customerStats.getDeliveredMessages()/(double)customerStats.getTotalMessages() * 100));
        customerStats.setFailedPercentage(((double)customerStats.getFailedMessages()/(double)customerStats.getTotalMessages() * 100));

        customerStats.setLastMessageTime(whAppMessageStats.getLastMessageTime());
        customerStats.setLastMessageStatus(whAppMessageStats.getLastMessageStatus());
        customerStats.setLastDataUpdateTime(AppUtils.getCurrentTimestamp());
        customerStats.setLastIncrementalDateKey(endDate);
    }

    private <T> String convertObjectListToCSV(List<T> object,String fileName) throws IOException {
         String csvFileName  = environmentProperties.getBasePath()+"/"+fileName+"_"+AppUtils.getCurrentTimestamp().toInstant().getEpochSecond()+".csv";
        FileWriter fileWriter = new FileWriter(csvFileName);
        for(T obj : object) {
            fileWriter.write(obj.toString());
            fileWriter.write("\n");
        }
        fileWriter.close();
        return csvFileName;
    }

    @Override
    public Boolean pushCustomerWhatsAppStatsToCLM(){
       if(AppUtils.isProd(environmentProperties.getEnvironmentType())){
           try {
               log.info("######### ---- Starting publishing customer whatsapp stats  ---- ############");
               List<Object[]> customerStats = customerWhatsAppCommunicationDataDao.findAllProfileAttributes();
               Integer pageSize = Math.min(1000,customerStats.size());
               Integer startIndex = 0;
               while (true) {
                   JSONArray dataToPush = cleverTapDataPushServiceV1.getPagedData(startIndex, pageSize, customerStats);
                   log.info("#### Push Customer WhatsApp Stats to CLM data size : {}  ", dataToPush.length());
                   if (dataToPush.length() <= 0) {
                       break;
                   }
                   pageSize = Math.min(dataToPush.length(), 1000);
                   Integer startCustomerId = (Integer) customerStats.get(startIndex)[0];
                   Integer endCustomerId = (Integer) customerStats.get(startIndex + pageSize - 1)[0];
                   JSONObject obj = new JSONObject();
                   obj.put("d", dataToPush);
                   CleverTapPushResponse response = cleverTapDataPushServiceV1.pushBatchWiseData(1, obj.toString(), startCustomerId,
                           endCustomerId, "profile", -1, pageSize, pageSize);

                   startIndex = startIndex + pageSize;
               }
               log.info("######### ---- Ending  publishing customer whatsapp stats  ---- ############");
           }catch (Exception e){
               log.error("Exception while pushing whatsapp data to clm : {}",e.getMessage());
               e.printStackTrace();
               throw e;
           }
       }

        return null;
    }
    private void deleteFile(String fileLocation){
        FileUtils.deleteQuietly(new File(fileLocation));
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "DumpDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean loadWhatsAppData() throws IOException, URISyntaxException, ParseException {
        if(AppUtils.isProd(environmentProperties.getEnvironmentType())){
            try{
                log.info("################ Starting load whatsApp data from email ###############################");
                String accessToken =  dashboardRefreshService.getGmailAccessToken();

                List<String> whatsAppLabels = new ArrayList<String>();

                whatsAppLabels.add(AppConstants.LABEL_WHATSAPP_DATA);
                whatsAppLabels.add(AppConstants.LABEL_WHATSAPP_DATA_183);
                for(String label : whatsAppLabels){
                    String messageId =   dashboardRefreshService.fetchUnreadMail(accessToken, label);
                    if(messageId!=null && !messageId.isEmpty()){
                        String attachmentId =  dashboardRefreshService.fetchAttachment(accessToken,messageId);
                        if(attachmentId!=null && !attachmentId.isEmpty()) {
                            String fileName = "WhatsAppData_" + AppUtils.getCurrentTimestamp().getTime();
                            String filePath = downloadWhatsAppFile(accessToken, messageId, attachmentId, fileName);
                            dashboardRefreshService.markAsRead(accessToken, messageId);
                            log.info("File Path : " + filePath);
                            String csvFile = unzipFile(filePath);
                            log.info("Csv File Path : " + csvFile);
                            bulkDataProcessingDao.insertAndPreProcessWhatsAppDataV2(csvFile);
                            deleteFile(filePath);
                            deleteFile(csvFile);
                            log.info("################ Ending load whatsApp data from email ###############################");
                        }
                    }
                }
                customerWhatsAppCommunicationDataDao.syncWhatsappMessageData2024();
                return true;
            }catch (Exception e){
                log.error("Exception while loading load whatsapp data : {}",e.getMessage());
                throw  e;
            }
        }
      return null;
    }

    private String downloadWhatsAppFile(String accessToken, String messageId, String attachmentId, String identifier) throws IOException, URISyntaxException {
        StringBuilder requestUrl = new StringBuilder("https://gmail.googleapis.com/gmail/v1/users/me/messages/");
        requestUrl.append(messageId);
        requestUrl.append("/attachments/");
        requestUrl.append(attachmentId);
        Map<String, String> uriVariables = new HashMap<>();
        uriVariables.put("access_token", accessToken);
        try{
            JsonNode response = WebServiceHelper.getRequestWithParamAndHeader(requestUrl.toString(), null, uriVariables, JsonNode.class, false);
            ObjectMapper obj = new ObjectMapper();
            JsonNode node = obj.readTree(response.toString());
            JsonNode dataStr = node.get("data");

            String regex = "cid:[^'\"> ]+";
            Pattern pattern = Pattern.compile(regex);
            Matcher matcher = pattern.matcher(dataStr.textValue());
            String sanitizedContent = matcher.replaceAll("");

            byte[] decodedBytes = Base64.getUrlDecoder().decode(sanitizedContent);
            Path path = Paths.get(dashboardRefreshService.getAttachmentPath() + "Attachment" + identifier + ".zip");
            Files.write(path, decodedBytes);
            return path.toString();
        } catch (Exception e){
            log.error("Unable to Fetch mail ::::",e);
            throw e;
        }

    }

    private String  unzipFile(String zipFilePath) throws IOException {
        ZipInputStream zis = null;
        FileOutputStream fos = null;
        try {
            zis = new ZipInputStream(Files.newInputStream(Paths.get(zipFilePath)));
            ZipEntry zipEntry = zis.getNextEntry();
            String outputPath = null;
            if (zipEntry != null && !zipEntry.isDirectory()) {
                outputPath = dashboardRefreshService.getAttachmentPath() + "WhatsAppData_" + AppUtils.getCurrentTimestamp().getTime()+".csv";
                File newFile = new File(outputPath);
                fos = new FileOutputStream(newFile);
                int len;
                byte[] bytes = new byte[1024];
                while ((len = zis.read(bytes)) > 0) {
                    fos.write(bytes, 0, len);
                }
            }else {
                throw new RuntimeException("Not able to find content  inside zip ");
            }
            return outputPath;
        }catch (Exception e){
            log.error("Error while unzipping file : "+e.getMessage());
            throw e;
        }finally {
            if(zis != null) zis.close();
            if(fos != null) fos.close();
        }
    }

    @Override
    public void generateWhatsAppRetentionData(){
        if(AppUtils.isProd(environmentProperties.getEnvironmentType())) {
            log.info("################ Starting generateWhatsAppRetentionData  ###############################");
            DateFormat dateFormat = new SimpleDateFormat(DATE_FORMAT);
            String prevDate = dateFormat.format(AppUtils.getPreviousNthDate(1));
            customerWhatsAppCommunicationDataDao.generateWhatsAppRetentionData(prevDate);
            customerWhatsAppCommunicationDataDao.generateWhatsAppRetentionData14Days(prevDate);
            customerWhatsAppCommunicationDataDao.generateCustomerSegment(prevDate);
            log.info("################ Ending generateWhatsAppRetentionData  ###############################");
        }
        }

    @Override
    public void insertRawData(){
        if(AppUtils.isProd(environmentProperties.getEnvironmentType())) {
            log.info("################ Starting insertRawDataIntoWhatsAppReactivationTable  ###############################");
            customerWhatsAppCommunicationDataDao.insertRawDataIntoWhatsAppReactivationTable();
            log.info("################ Ending insertRawDataIntoWhatsAppReactivationTable  ###############################");
       }
    }


}
