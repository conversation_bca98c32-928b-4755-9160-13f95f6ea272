package com.stpl.tech.report.service.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.api.services.drive.model.File;
import com.stpl.tech.kettle.clm.model.DashboardLastUpdateData;
import com.stpl.tech.kettle.core.TransactionConstants;
import com.stpl.tech.kettle.core.file.management.GoogleSheetLoader;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.report.service.controller.DashboardRefreshResources;
import com.stpl.tech.report.service.core.ReportProperties;
import com.stpl.tech.report.service.dao.DashboardRefreshDao;
import com.stpl.tech.report.service.model.DashboardRefreshFlowType;
import com.stpl.tech.report.service.model.SwiggyCouponRedemption;
import com.stpl.tech.report.service.model.TableauLoginRequest;
import com.stpl.tech.report.service.model.TableauLoginRequestCredentials;
import com.stpl.tech.report.service.model.ZomatoCouponRedemption;
import com.stpl.tech.report.service.model.ZomatoFoodRating;
import com.stpl.tech.report.service.model.ZomatoGridVisibility;
import com.stpl.tech.report.service.model.ZomatoItemLevelRating;
import com.stpl.tech.report.service.model.ZomatoPromoRedemption;
import com.stpl.tech.report.service.service.DashboardRefreshService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.excelparser.SheetParser;
import com.stpl.tech.util.excelparser.exception.ExcelParsingException;
import org.apache.http.HttpHeaders;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.util.EntityUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.json.simple.JSONObject;
import org.json.simple.parser.JSONParser;
import org.pentaho.di.core.KettleEnvironment;
import org.pentaho.di.core.exception.KettleException;
import org.pentaho.di.job.Job;
import org.pentaho.di.job.JobMeta;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.subtlelib.poi.api.sheet.SheetContext;
import org.subtlelib.poi.api.workbook.WorkbookContext;
import org.subtlelib.poi.impl.workbook.WorkbookContextFactory;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URISyntaxException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.stpl.tech.master.core.WebServiceHelper.CONTENT_TYPE;

@Service
public class DashboardRefreshServiceImpl implements DashboardRefreshService {

    private static final Logger LOG = LoggerFactory.getLogger(DashboardRefreshResources.class);

    private final String jobs_path = getClass().getClassLoader().getResource("dashboards").getPath();

    private final String pluginPath = getClass().getClassLoader().getResource("plugins").getPath();

    private final WorkbookContextFactory ctxFactory = WorkbookContextFactory.useXlsx();

    private static final String DATE_FORMAT = "yyyy-MM-dd";

    @Autowired
    private EnvironmentProperties props;

    @Autowired
    private ReportProperties reportProperties;

//    @Value("${file.plugin-path}")
//    private String pluginPath;

    @Autowired
    private DashboardRefreshDao dashboardRefreshDao;


    @Override
    public String getAttachmentPath(){
        return props.getBasePath() + "/Mail-Attachments/";
    }

    @Override
    public Boolean fetchDataToAdjunctSheet(String sheetId) throws IOException, URISyntaxException {
        String accessToken = getGmailAccessToken();
        Boolean sheetEmpty = emptyAdjunctSheet(accessToken);
        if(Boolean.TRUE.equals(sheetEmpty)){
            StringBuilder requestUrl = new StringBuilder("https://sheets.googleapis.com/v4/spreadsheets/");
            requestUrl.append(AppConstants.DATA_SPREADSHEET);
            requestUrl.append("/sheets/");
            requestUrl.append(sheetId);
            requestUrl.append(":copyTo");
            Map<String, String> uriVariables = new HashMap<>();
            uriVariables.put("access_token", accessToken);
            JSONParser parser = new JSONParser();
            JSONObject jsonBody = null;
            try{
                String body = "{\"destinationSpreadsheetId\": \""+AppConstants.ADJUNCT_SHEET+"\"}";
                jsonBody = (JSONObject) parser.parse(body);
                HttpResponse httpResponse = WebServiceHelper.postWithBearer(requestUrl.toString(), null, jsonBody, uriVariables);
                JsonNode response = WebServiceHelper.convertResponse(httpResponse, JsonNode.class);
                LOG.info(":::::Copy Sheet Created::::::"+String.valueOf(response));
                return true;
            } catch (Exception e){
                LOG.error("Unable to Fetch data to Adjunct Sheet ::::",e);
            }
        }
        return false;
    }

    public Boolean emptyAdjunctSheet(String accessToken){
        StringBuilder requestUrl = new StringBuilder("https://sheets.googleapis.com/v4/spreadsheets/");
        requestUrl.append(AppConstants.ADJUNCT_SHEET);
        requestUrl.append(":batchUpdate");
        Map<String, String> uriVariables = new HashMap<>();
        uriVariables.put("access_token", accessToken);
        List<Integer> sheets = fetchSheets(accessToken);
        JSONParser parser = new JSONParser();
        JSONObject jsonBody = null;
        try{
            for(Integer sheet : sheets) {
                String body = "{\"requests\": [{\"deleteSheet\": {\"sheetId\":" + sheet.toString() + "}}]}";
                jsonBody = (JSONObject) parser.parse(body);
                HttpResponse httpResponse = WebServiceHelper.postWithBearer(requestUrl.toString(), null, jsonBody, uriVariables);
                JsonNode response = WebServiceHelper.convertResponse(httpResponse, JsonNode.class);
                LOG.info(String.valueOf(response));
            }
            return true;
        } catch (Exception e){
            LOG.error("Unable to empty SpreadSheet ::::",e);
        }

        return false;
    }

    public List<Integer> fetchSheets(String accessToken){
        List<Integer> sheetList = new ArrayList<Integer>();
        StringBuilder requestUrl = new StringBuilder("https://sheets.googleapis.com/v4/spreadsheets/");
        requestUrl.append(AppConstants.ADJUNCT_SHEET);
        Map<String, String> uriVariables = new HashMap<>();
        uriVariables.put("access_token", accessToken);
        try{
            JsonNode response = WebServiceHelper.getRequestWithParamAndHeader(requestUrl.toString(), null, uriVariables, JsonNode.class, false);
            ObjectMapper obj = new ObjectMapper();
            JsonNode node = obj.readTree(response.toString());
            JsonNode sheets = node.get("sheets");
            for(JsonNode sheet : sheets){
                if(!Objects.equals(sheet.get("properties").get("title").textValue(),"ADJUNCT_SHEET")){
                    sheetList.add(sheet.get("properties").get("sheetId").intValue());
                }
            }
            LOG.info("Deleting Sheets :::: " + String.valueOf(sheetList));
            return sheetList;
        } catch (Exception e){
            LOG.error("Unable to Fetch Sheets ::::",e);
        }
        return sheetList;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CLMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Date updateZomatoRatingsData(String fileName) {
        String outputFolder = props.getBasePath() + "/dashboardExcel";
        String outputFile = outputFolder + "/" + fileName + ".xlsx";
        GoogleSheetLoader loader = new GoogleSheetLoader();
        try {
            LOG.info("Downloading file {} ", fileName);
            File file = loader.getFileForDashboardRefresh(TransactionConstants.SERVICE_ACCOUNT_ACCOUNT_EMAIL, fileName,
                    TransactionConstants.MIMETYPE_GOOGLE_SHEETS, "");
            if (file == null) {
                throw new RuntimeException("File Not Found " + fileName);
            }
            loader.downloadFile(TransactionConstants.SERVICE_ACCOUNT_ACCOUNT_EMAIL, file.getId(),
                    AppConstants.EXCEL_MIME_TYPE, outputFile);
            String filePath = checkForUpdatedValuesZFR(outputFolder , fileName);
            if(Objects.isNull(filePath)){
                return null;
            }
            else if(!Objects.equals(filePath,"")){
                filePath = filePath +"/IncrementalValuesExcel/" + "Daily_Channel_Partner_Data_Loads.xlsx";
                Pair cornerDates = getExcelStartDate(filePath, ZomatoFoodRating.class);
                Date startDate = (Date) cornerDates.getKey();
                Date endDate = (Date) cornerDates.getValue();
                if(Objects.nonNull(startDate)){
                    Boolean complete = runPentahoTransformation(filePath, "dailyJob.kjb", startDate);
                    if(Boolean.TRUE.equals(complete)){
                        LOG.info("::::::::::::::: Pentaho Transformation Completed ::::::");
                        return endDate;
                    }
                }
            }
            return null;
        } catch (IOException | KettleException e) {
            LOG.error("Failed to download the file", e);
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public String checkForUpdatedValuesZFR(String filePath, String fileName) throws IOException, KettleException {
        DashboardLastUpdateData data = dashboardRefreshDao.getLastRecordDate();
        if(Objects.nonNull(data)){
            String lastRecordDate = data.getZfr().toString();
            lastRecordDate = lastRecordDate.substring(0,4) + lastRecordDate.substring(5,7) + lastRecordDate.substring(8,10);
            FileInputStream inputStream = new FileInputStream(new java.io.File(filePath + "/" + fileName + ".xlsx"));
            Workbook workbook = new XSSFWorkbook(inputStream);
            WorkbookContext workbookContext= ctxFactory.createWorkbook();
            List<ExcelParsingException> errors = new ArrayList<>();
            SheetParser parser = new SheetParser();
            List<ZomatoFoodRating> entityList = parser.createEntity(workbook.getSheetAt(1), ZomatoFoodRating.class,
                    errors::add);
            if (errors.isEmpty() && !entityList.isEmpty()) {
                entityList = entityList.stream().filter(x -> !x.getDt().equals("")).collect(Collectors.toList());
                String finalLastRecordDate = lastRecordDate;
                entityList = entityList.stream().filter(x -> Integer.parseInt(x.getDt()) > Integer.parseInt(finalLastRecordDate)).collect(Collectors.toList());
                if(!entityList.isEmpty()){
                    SheetContext sheetctx = workbookContext.createSheet("Zomato_Food_Rating");
                    sheetctx.nextRow()
                            .text("Order_Id")
                            .text("res_id")
                            .text("res_name")
                            .text("city_name")
                            .text("dt")
                            .text("Delivery_Rating")
                            .text("Food_Rating")
                            .text("ORS")
                            .text("ORS_Reason");

                    for (ZomatoFoodRating obj : entityList) {
                        sheetctx.nextRow()
                                .text(obj.getOrderId())
                                .text(obj.getResId())
                                .text(obj.getResName())
                                .text(obj.getCityName())
                                .text(obj.getDt())
                                .text(obj.getDeliveryRating())
                                .text(obj.getFoodRating())
                                .text(obj.getOrs())
                                .text(obj.getOrsReason());
                    }
                    AppUtils.write(workbookContext.toNativeBytes(), filePath, "IncrementalValuesExcel",
                            "Daily_Channel_Partner_Data_Loads.xlsx", LOG);
                    return filePath;
                }
                else{
                    LOG.info("::::::::ZFR Dashboard up-to-date::::::");
                }
                return null;
            } else {
                LOG.info("Error Parsing Workbook, total errors :{}", errors.size());
                StringBuilder sb = new StringBuilder();
                errors.forEach(e -> sb.append(e.getMessage() + '\n'));
                LOG.info("{}", sb.toString());
            }
        }
        return "";
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CLMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Date updateZomatoGridVisibility(String fileName) {
        String outputFolder = props.getBasePath() + "/dashboardExcel";
        String outputFile = outputFolder + "/" + fileName + ".xlsx";
        GoogleSheetLoader loader = new GoogleSheetLoader();
        try {
            LOG.info("Downloading file {} ", fileName);
            File file = loader.getFileForDashboardRefresh(TransactionConstants.SERVICE_ACCOUNT_ACCOUNT_EMAIL, fileName,
                    TransactionConstants.MIMETYPE_GOOGLE_SHEETS, "");
            if (file == null) {
                throw new RuntimeException("File Not Found " + fileName);
            }
            loader.downloadFile(TransactionConstants.SERVICE_ACCOUNT_ACCOUNT_EMAIL, file.getId(),
                    AppConstants.EXCEL_MIME_TYPE, outputFile);
            String filePath = checkForUpdatedValuesZGV(outputFolder , fileName);
            if(Objects.isNull(filePath)){
                return null;
            }
            else if(!Objects.equals(filePath,"")){
                filePath = filePath +"/IncrementalValuesExcel/" + "Daily_Channel_Partner_Data_Loads.xlsx";
                Pair cornerDates = getExcelStartDate(filePath, ZomatoGridVisibility.class);
                Date startDate = (Date) cornerDates.getKey();
                Date endDate = (Date) cornerDates.getValue();
                if(Objects.nonNull(startDate)){
                    Boolean complete = runPentahoTransformation(filePath, "dailyJobGV.kjb", null);
                    if(Boolean.TRUE.equals(complete)){
                        LOG.info("::::::::::::::: Pentaho Transformation Completed ::::::");
                        return endDate;
                    }
                }
            }
            return null;
        } catch (IOException | KettleException e) {
            LOG.error("Failed to download the file", e);
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public String checkForUpdatedValuesZGV(String filePath, String fileName) throws IOException, KettleException {
        DashboardLastUpdateData data = dashboardRefreshDao.getLastRecordDate();
        if(Objects.nonNull(data)){
            String lastRecordDate = data.getZgv().toString();
            lastRecordDate = lastRecordDate.substring(0,4) + lastRecordDate.substring(5,7) + lastRecordDate.substring(8,10);
            FileInputStream inputStream = new FileInputStream(new java.io.File(filePath + "/" + fileName + ".xlsx"));
            Workbook workbook = new XSSFWorkbook(inputStream);
            WorkbookContext workbookContext= ctxFactory.createWorkbook();
            List<ExcelParsingException> errors = new ArrayList<>();
            SheetParser parser = new SheetParser();
            List<ZomatoGridVisibility> entityList = parser.createEntity(workbook.getSheetAt(1), ZomatoGridVisibility.class,
                    errors::add);
            if (errors.isEmpty() && !entityList.isEmpty()) {
                entityList = entityList.stream().filter(x -> !x.getAggregation().equals("")).collect(Collectors.toList());
                String finalLastRecordDate = lastRecordDate;
                entityList = entityList.stream().filter(x -> Integer.parseInt(x.getAggregation()) > Integer.parseInt(finalLastRecordDate)).collect(Collectors.toList());
                if(!entityList.isEmpty()){
                    SheetContext sheetctx = workbookContext.createSheet("Grid_Visibility");
                    sheetctx.nextRow()
                            .text("Aggregation")
                            .text("res_id")
                            .text("TC_PC_Flag")
                            .text("res_name")
                            .text("city_name")
                            .text("Meal/Day")
                            .text("Actuals")
                            .text("Expected")
                            .text("Grid Visibility");

                    for (ZomatoGridVisibility obj : entityList) {
                        sheetctx.nextRow()
                                .text(obj.getAggregation())
                                .text(obj.getResId())
                                .text(obj.getTcpcFlag())
                                .text(obj.getResName())
                                .text(obj.getCityName())
                                .text(obj.getMealDay())
                                .text(obj.getActual())
                                .text(obj.getExpected())
                                .text(obj.getGridVisibility());
                    }
                    AppUtils.write(workbookContext.toNativeBytes(), filePath, "IncrementalValuesExcel",
                            "Daily_Channel_Partner_Data_Loads.xlsx", LOG);
                    return filePath;
                }
                else{
                    LOG.info("::::::::ZGV Dashboard up-to-date::::::");
                }
                return null;
            } else {
                LOG.info("Error Parsing Workbook, total errors :{}", errors.size());
                StringBuilder sb = new StringBuilder();
                errors.forEach(e -> sb.append(e.getMessage() + '\n'));
                LOG.info("{}", sb.toString());
            }
        }
        return "";
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CLMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Date updateZomatoItemLevelRating(String fileName) {
        String outputFolder = props.getBasePath() + "/dashboardExcel";
        String outputFile = outputFolder + "/" + fileName + ".xlsx";
        GoogleSheetLoader loader = new GoogleSheetLoader();
        try {
            LOG.info("Downloading file {} ", fileName);
            File file = loader.getFileForDashboardRefresh(TransactionConstants.SERVICE_ACCOUNT_ACCOUNT_EMAIL, fileName,
                    TransactionConstants.MIMETYPE_GOOGLE_SHEETS, "");
            if (file == null) {
                throw new RuntimeException("File Not Found " + fileName);
            }
            loader.downloadFile(TransactionConstants.SERVICE_ACCOUNT_ACCOUNT_EMAIL, file.getId(),
                    AppConstants.EXCEL_MIME_TYPE, outputFile);
            String filePath = checkForUpdatedValuesZILR(outputFolder , fileName);
            if(Objects.isNull(filePath)){
                return null;
            }
            else if(!Objects.equals(filePath,"")){
                filePath = filePath +"/IncrementalValuesExcel/" + "Daily_Channel_Partner_Data_Loads.xlsx";
                Pair cornerDates = getExcelStartDate(filePath, ZomatoItemLevelRating.class);
                Date startDate = (Date) cornerDates.getKey();
                Date endDate = (Date) cornerDates.getValue();
                if(Objects.nonNull(startDate)){
                    Boolean complete = runPentahoTransformation(filePath, "dailyJobILR.kjb", null);
                    if(Boolean.TRUE.equals(complete)){
                        LOG.info("::::::::::::::: Pentaho Transformation Completed ::::::");
                        return endDate;
                    }
                }
            }
            return null;
        } catch (IOException | KettleException e) {
            LOG.error("Failed to download the file", e);
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public String checkForUpdatedValuesZILR(String filePath, String fileName) throws IOException, KettleException {
        DashboardLastUpdateData data = dashboardRefreshDao.getLastRecordDate();
        if(Objects.nonNull(data)){
            String lastRecordDate = data.getZilr().toString();
            lastRecordDate = lastRecordDate.substring(0,4) + lastRecordDate.substring(5,7) + lastRecordDate.substring(8,10);
            FileInputStream inputStream = new FileInputStream(new java.io.File(filePath + "/" + fileName + ".xlsx"));
            Workbook workbook = new XSSFWorkbook(inputStream);
            WorkbookContext workbookContext= ctxFactory.createWorkbook();
            List<ExcelParsingException> errors = new ArrayList<>();
            SheetParser parser = new SheetParser();
            List<ZomatoItemLevelRating> entityList = parser.createEntity(workbook.getSheetAt(1), ZomatoItemLevelRating.class,
                    errors::add);
            if (errors.isEmpty() && !entityList.isEmpty()) {
                entityList = entityList.stream().filter(x -> !x.getDt().equals("")).collect(Collectors.toList());
                String finalLastRecordDate = lastRecordDate;
                entityList = entityList.stream().filter(x -> Integer.parseInt(x.getDt()) > Integer.parseInt(finalLastRecordDate)).collect(Collectors.toList());
                if(!entityList.isEmpty()){
                    SheetContext sheetctx = workbookContext.createSheet("Item_Level_Rating");
                    sheetctx.nextRow()
                            .text("dt")
                            .text("brand_name")
                            .text("res_id")
                            .text("order_id")
                            .text("review_text")
                            .text("item_id")
                            .text("item_name")
                            .text("item_rating");

                    for (ZomatoItemLevelRating obj : entityList) {
                        sheetctx.nextRow()
                                .text(obj.getDt())
                                .text(obj.getBrand_name())
                                .text(obj.getRes_id())
                                .text(obj.getOrder_id())
                                .text(obj.getReview_text())
                                .text(obj.getItem_id())
                                .text(obj.getItem_name())
                                .text(obj.getItem_rating());
                    }
                    AppUtils.write(workbookContext.toNativeBytes(), filePath, "IncrementalValuesExcel",
                            "Daily_Channel_Partner_Data_Loads.xlsx", LOG);
                    return filePath;
                }
                else{
                    LOG.info("::::::::ZILR Dashboard up-to-date::::::");
                }
                return null;
            } else {
                LOG.info("Error Parsing Workbook, total errors :{}", errors.size());
                StringBuilder sb = new StringBuilder();
                errors.forEach(e -> sb.append(e.getMessage() + '\n'));
                LOG.info("{}", sb.toString());
            }
        }
        return "";
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CLMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Date updateZomatoPromoRedemption(String fileName) {
        String outputFolder = props.getBasePath() + "/dashboardExcel";
        String outputFile = outputFolder + "/" + fileName + ".xlsx";
        GoogleSheetLoader loader = new GoogleSheetLoader();
        try {
            LOG.info("Downloading file {} ", fileName);
            File file = loader.getFileForDashboardRefresh(TransactionConstants.SERVICE_ACCOUNT_ACCOUNT_EMAIL, fileName,
                    TransactionConstants.MIMETYPE_GOOGLE_SHEETS, "");
            if (file == null) {
                throw new RuntimeException("File Not Found " + fileName);
            }
            loader.downloadFile(TransactionConstants.SERVICE_ACCOUNT_ACCOUNT_EMAIL, file.getId(),
                    AppConstants.EXCEL_MIME_TYPE, outputFile);
            String filePath = checkForUpdatedValuesZPR(outputFolder , fileName);
            if(Objects.nonNull(filePath) && !Objects.equals(filePath,"")){
                filePath = filePath +"/IncrementalValuesExcel/" + "Daily_Channel_Partner_Data_Loads.xlsx";
                Pair<Date, Date> cornerDates = getExcelStartDate(filePath, ZomatoPromoRedemption.class);
                Date startDate = cornerDates.getKey();
                Date endDate = cornerDates.getValue();
                if(Objects.nonNull(startDate)){
                    Boolean complete = runPentahoTransformation(filePath, "dailyJobZPR.kjb", startDate);
                    if(Boolean.TRUE.equals(complete)){
                        LOG.info("::::::::::::::: Pentaho Transformation Completed ::::::");
                        return endDate;
                    }
                }
            }
        } catch (IOException | KettleException e) {
            LOG.error("Failed to download the file", e);
        }
        return null;
    }

    private String checkForUpdatedValuesZPR(String filePath, String fileName) throws IOException, KettleException {
        DashboardLastUpdateData data = dashboardRefreshDao.getLastRecordDate();
        if (Objects.nonNull(data)) {
            String lastRecordDate = data.getZomatoPromoRedemption().toString();
            // convert data to zomato format
            lastRecordDate = lastRecordDate.substring(0, 4) + lastRecordDate.substring(5, 7) + lastRecordDate.substring(8, 10);

            FileInputStream inputStream = new FileInputStream(filePath + "/" + fileName + ".xlsx");
            Workbook workbook = new XSSFWorkbook(inputStream);
            //TODO close this stream
            WorkbookContext workbookContext = ctxFactory.createWorkbook();
            List<ExcelParsingException> errors = new ArrayList<>();
            SheetParser parser = new SheetParser();
            List<ZomatoPromoRedemption> entityList = parser.createEntity(workbook.getSheetAt(1), ZomatoPromoRedemption.class,
                    errors::add);
            if (errors.isEmpty() && !entityList.isEmpty()) {
                // filters
                String finalLastRecordDate = lastRecordDate;
                entityList = entityList.stream().filter(
                        x ->
                                !x.getAggregation().isEmpty() &&
                                        Integer.parseInt(x.getAggregation()) > Integer.parseInt(finalLastRecordDate)
                ).collect(Collectors.toList());

                if (!entityList.isEmpty()) {
                    SheetContext sheetctx = workbookContext.createSheet("Zomato_Promo_Redemption");
                    sheetctx.nextRow()
                            .text("Aggregation")
                            .text("res_id")
                            .text("res_name")
                            .text("city_name")
                            .text("res_promo_code")
                            .text("Promo_orders")
                            .text("Promo_orders_subtotal")
                            .text("ZVD")
                            .text("MVD");

                    for (ZomatoPromoRedemption obj : entityList) {
                        sheetctx.nextRow()
                                .text(obj.getAggregation())
                                .text(obj.getResId())
                                .text(obj.getResName())
                                .text(obj.getCityName())
                                .text(Objects.nonNull(obj.getResPromoCode()) ? obj.getResPromoCode() : "")
                                .text(obj.getPromoOrders())
                                .text(obj.getPromoOrdersSubtotal())
                                .text(obj.getZvd())
                                .text(obj.getMvd());
                    }
                    AppUtils.write(workbookContext.toNativeBytes(), filePath, "IncrementalValuesExcel",
                            "Daily_Channel_Partner_Data_Loads.xlsx", LOG);
                    return filePath;
                } else {
                    LOG.info("::::::::ZPR Dashboard up-to-date::::::");
                }
                return null;
            } else {
                LOG.info("Error Parsing Workbook, total errors :{}", errors.size());
                StringBuilder sb = new StringBuilder();
                errors.forEach(e -> sb.append(e.getMessage()).append('\n'));
                LOG.info("{}", sb);
            }
        }
        return "";
    }

    @Override
    public Boolean runPentahoTransformation(String outputFile, String jobPath, Date startDate) throws KettleException {
        try{
            LOG.info("####################### Starting Dashboard Refresh Process ######################");
            System.setProperty("KETTLE_PLUGIN_BASE_FOLDERS", pluginPath);
            KettleEnvironment.init();
            JobMeta jobMeta = new JobMeta(jobs_path+"/"+ jobPath,null);
            jobMeta.setParameterValue("file_path", outputFile);
            if(Objects.nonNull(startDate)){
                jobMeta.setParameterValue("start_date", AppUtils.getDateString(startDate,"yyyy-MM-dd"));
            }
            Job job = new Job(null, jobMeta);
            job.start();
            job.waitUntilFinished();
            if (job.getErrors() > 0) {
                LOG.info("Error in Dashboard Refresh job");
                return null;
            }
            LOG.info("####################### Dashboard Refresh Process Completed ######################");
            return true;
        }catch (Exception e){
            LOG.error("Error in Dashboard Refresh Process  {}", e);
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CLMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean postLastUpdate(Date endDate, Date endDateGV, Date endDateILR, String flowType, Date endDateZPR) {
        try {
            DashboardLastUpdateData data = dashboardRefreshDao.getLastRecordDate();
            if(Objects.equals(flowType,DashboardRefreshFlowType.ZOMATO_FOOD_RATING_DATA.value())){
                if(Objects.nonNull(endDate)){
                    data.setZfr(AppUtils.getDate(AppUtils.getDateString(endDate),"yyyy-MM-dd"));
                }
                if(Objects.nonNull(endDateGV)){
                    data.setZgv(AppUtils.getDate(AppUtils.getDateString(endDateGV),"yyyy-MM-dd"));
                }
                if(Objects.nonNull(endDateILR)){
                    data.setZilr(AppUtils.getDate(AppUtils.getDateString(endDateILR),"yyyy-MM-dd"));
                }
                if(Objects.nonNull(endDateZPR)){
                    data.setZomatoPromoRedemption(AppUtils.getDate(AppUtils.getDateString(endDateZPR),"yyyy-MM-dd"));
                }
            }
            else if(Objects.equals(flowType,DashboardRefreshFlowType.SWIGGY_COUPON_REDEMPTION.value())){
                if(Objects.nonNull(endDate)){
                    data.setScr(AppUtils.getDate(AppUtils.getDateString(endDate),"yyyy-MM-dd"));
                }

            }else if(Objects.equals(flowType,DashboardRefreshFlowType.ZOMATO_COUPON_REDEMPTION.value())) {
                if(Objects.nonNull(endDate)){
                    data.setZcr(AppUtils.getDate(AppUtils.getDateString(endDate),"yyyy-MM-dd"));
                }
            }
            dashboardRefreshDao.update(data);
            return true;
        }catch (Exception e) {
            LOG.error("Failed to post last update date :::::::",e);
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "CLMDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Boolean runJob(String siteId, String workbookId) throws JsonProcessingException {
        try{
            String token = loginTableau();
            LOG.info("::::::::::Tableau Logged In:::::::::::");
            refreshExtract(siteId,workbookId,token);
            LOG.info(":::::::::::Extract Refresh started:::::::::::");
            logoutTableau(token);
            LOG.info(":::::::::::Tableau Logged Out:::::::::::");
            return true;
        }catch (Exception e) {
            LOG.error("Error while running Tableau Extract Refresh Job:::",e);
        }
        return false;
    }

    private Pair<Date,Date> getExcelStartDate(String excelPath, Class clas) throws IOException {
        String minDate = null;
        String maxDate = null;
        Date startDate = null;
        Date endDate = null;
        FileInputStream inputStream = new FileInputStream(new java.io.File(excelPath));
        Workbook workbook = new XSSFWorkbook(inputStream);
        List<ExcelParsingException> errors = new ArrayList<>();
        SheetParser parser = new SheetParser();

        if(Objects.equals(clas,ZomatoFoodRating.class)){
            List<ZomatoFoodRating> entityList = parser.createEntity(workbook.getSheetAt(0), ZomatoFoodRating.class, errors::add);
            if (errors.isEmpty() && !entityList.isEmpty()) {
                minDate = entityList.get(0).getDt();
                maxDate = entityList.get(0).getDt();
                for(ZomatoFoodRating rating : entityList) {
                    if(Long.parseLong(rating.getDt()) < Long.parseLong(minDate)) {
                        minDate = rating.getDt();
                    }
                    if(Long.parseLong(rating.getDt()) > Long.parseLong(maxDate)) {
                        maxDate = rating.getDt();
                    }
                }
            }
        }else if(Objects.equals(clas,ZomatoGridVisibility.class)){
            List<ZomatoGridVisibility> entityList = parser.createEntity(workbook.getSheetAt(0), ZomatoGridVisibility.class, errors::add);
            if (errors.isEmpty() && !entityList.isEmpty()) {
                minDate = entityList.get(0).getAggregation();
                maxDate = entityList.get(0).getAggregation();
                for(ZomatoGridVisibility rating : entityList) {
                    if(Long.parseLong(rating.getAggregation()) < Long.parseLong(minDate)) {
                        minDate = rating.getAggregation();
                    }
                    if(Long.parseLong(rating.getAggregation()) > Long.parseLong(maxDate)) {
                        maxDate = rating.getAggregation();
                    }
                }
            }
        }else if(Objects.equals(clas,ZomatoItemLevelRating.class)){
            List<ZomatoItemLevelRating> entityList = parser.createEntity(workbook.getSheetAt(0), ZomatoItemLevelRating.class, errors::add);
            if (errors.isEmpty() && !entityList.isEmpty()) {
                minDate = entityList.get(0).getDt();
                maxDate = entityList.get(0).getDt();
                for(ZomatoItemLevelRating rating : entityList) {
                    if(Long.parseLong(rating.getDt()) < Long.parseLong(minDate)) {
                        minDate = rating.getDt();
                    }
                    if(Long.parseLong(rating.getDt()) > Long.parseLong(maxDate)) {
                        maxDate = rating.getDt();
                    }
                }
            }
        }else if(Objects.equals(clas,ZomatoCouponRedemption.class)){
            List<ZomatoCouponRedemption> entityList = parser.createEntity(workbook.getSheetAt(0), ZomatoCouponRedemption.class, errors::add);
            if (errors.isEmpty() && !entityList.isEmpty()) {
                minDate = entityList.get(0).getDt();
                maxDate = entityList.get(0).getDt();
                for(ZomatoCouponRedemption record : entityList) {
                    if(Long.parseLong(record.getDt()) < Long.parseLong(minDate)) {
                        minDate = record.getDt();
                    }
                    if(Long.parseLong(record.getDt()) > Long.parseLong(maxDate)) {
                        maxDate = record.getDt();
                    }
                }
            }
        }else if(Objects.equals(clas,SwiggyCouponRedemption.class)){
            List<SwiggyCouponRedemption> entityList = parser.createEntity(workbook.getSheetAt(0), SwiggyCouponRedemption.class, errors::add);
            if (errors.isEmpty() && !entityList.isEmpty()) {
                minDate = entityList.get(0).getDate();
                maxDate = entityList.get(0).getDate();
                for(SwiggyCouponRedemption record : entityList) {
                    if(AppUtils.getDate(record.getDate(),"yyyy-MM-dd").compareTo(AppUtils.getDate(minDate,"yyyy-MM-dd")) < 0) {
                        minDate = record.getDate();
                    }
                    if(AppUtils.getDate(record.getDate(),"yyyy-MM-dd").compareTo(AppUtils.getDate(maxDate,"yyyy-MM-dd")) > 0) {
                        maxDate = record.getDate();
                    }
                }
            }
            startDate = AppUtils.getDate(minDate, AppUtils.DATE_FORMAT_STRING);
            endDate = AppUtils.getDate(maxDate, AppUtils.DATE_FORMAT_STRING);
            return new Pair<>(startDate,endDate);
        }else if(Objects.equals(clas,ZomatoPromoRedemption.class)){
            List<ZomatoPromoRedemption> entityList = parser.createEntity(workbook.getSheetAt(0), ZomatoPromoRedemption.class, errors::add);
            if (errors.isEmpty() && !entityList.isEmpty()) {
                minDate = entityList.get(0).getAggregation();
                maxDate = entityList.get(0).getAggregation();
                for(ZomatoPromoRedemption recordRow : entityList) {
                    if(Long.parseLong(recordRow.getAggregation()) < Long.parseLong(minDate)) {
                        minDate = recordRow.getAggregation();
                    }
                    if(Long.parseLong(recordRow.getAggregation()) > Long.parseLong(maxDate)) {
                        maxDate = recordRow.getAggregation();
                    }
                }
            }
        }

        if(Objects.nonNull(minDate) && Objects.nonNull(maxDate)) {
            minDate = minDate.substring(0,4) + "-" + minDate.substring(4,6) + "-" + minDate.substring(6,8);
            maxDate = maxDate.substring(0,4) + "-" + maxDate.substring(4,6) + "-" + maxDate.substring(6,8);
            startDate = AppUtils.getDate(minDate, AppUtils.DATE_FORMAT_STRING);
            endDate = AppUtils.getDate(maxDate, AppUtils.DATE_FORMAT_STRING);
        }

        return new Pair<>(startDate,endDate);
    }

    @Override
    public String loginTableau() throws JsonProcessingException {
        String requestUrl = "https://eu-west-1a.online.tableau.com/api/3.21/auth/signin";
        HttpPost reqObj = new HttpPost(requestUrl);
        TableauLoginRequest request = new TableauLoginRequest();
        TableauLoginRequestCredentials credentials = new TableauLoginRequestCredentials();
        credentials.setPersonalAccessTokenName("DashboardRefreshToken");
        credentials.setPersonalAccessTokenSecret(reportProperties.getTableauAuthToken());
        credentials.setSite(new HashMap<>());
        request.setCredentials(credentials);
        String body = WebServiceHelper.convertToString(request);
        org.apache.http.HttpEntity httpEntity = new StringEntity(body, AppConstants.CHARSET);
        reqObj.setHeader(CONTENT_TYPE, MediaType.APPLICATION_JSON.toString());
        reqObj.setEntity(httpEntity);
        try {
            org.apache.http.HttpResponse response = WebServiceHelper.postRequestWithNoTimeout(reqObj);
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                BufferedReader reader = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));
                StringBuffer result = new StringBuffer();
                String line = "";
                while ((line = reader.readLine()) != null) {
                    result.append(line);
                }
                EntityUtils.consume(response.getEntity());
                String authToken = result.substring(result.indexOf("token=\"") + 7, result.indexOf("\" estimatedTimeToExpiration"));
                return authToken;
            }
        } catch (Exception e) {
            LOG.error("Error while signing-in to tableau", e);
        } finally {
            reqObj.releaseConnection();
            reqObj.abort();
        }
        return "";
    }

    @Override
    public Boolean logoutTableau(String token){
        String requestUrl = "https://eu-west-1a.online.tableau.com/api/3.21/auth/signout";
        HttpPost reqObj = new HttpPost(requestUrl);
        reqObj.setHeader("X-Tableau-Auth", token);
        try {
            org.apache.http.HttpResponse response = WebServiceHelper.postRequestWithNoTimeout(reqObj);
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                EntityUtils.consume(response.getEntity());
                return true;
            }
        } catch (Exception e) {
            LOG.error("Error while signing-out of tableau", e);
        } finally {
            reqObj.releaseConnection();
            reqObj.abort();
        }
        return false;
    }

    @Override
    public Boolean refreshExtract(String siteId, String workbookId, String token) {
        StringBuilder requestUrl = new StringBuilder("https://eu-west-1a.online.tableau.com/api/3.21/sites/");
        requestUrl.append(siteId);
        requestUrl.append("/workbooks/");
        requestUrl.append(workbookId);
        requestUrl.append("/refresh");
        HttpPost reqObj = new HttpPost(requestUrl.toString());
        reqObj.setHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON.toString());
        reqObj.setHeader("X-Tableau-Auth", token);
        org.apache.http.HttpEntity httpEntity = new StringEntity("{}", AppConstants.CHARSET);
        reqObj.setEntity(httpEntity);
        try {
            org.apache.http.HttpResponse response = WebServiceHelper.postRequestWithNoTimeout(reqObj);
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                EntityUtils.consume(response.getEntity());
                return true;
            }
        } catch (Exception e) {
            LOG.error("Error while refreshing extract for workbook ::::: {}, site :::::: {}", workbookId, siteId, e);
        } finally {
            reqObj.releaseConnection();
            reqObj.abort();
        }
        return false;
    }

    @Override
    public String getGmailAccessToken() throws IOException, URISyntaxException {
        // TODO Ids and Keys to be added to properties file
        StringBuilder requestUrl = new StringBuilder("https://www.googleapis.com/oauth2/v4/token");
        Map<String, String> uriVariables = new HashMap<>();
        uriVariables.put("client_id", reportProperties.getGoogleClientId());
        uriVariables.put("client_secret", reportProperties.getGoogleClientSecret());
        uriVariables.put("grant_type", "refresh_token");
        uriVariables.put("refresh_token", reportProperties.getGoogleRefreshToken());
        HttpResponse response = WebServiceHelper.postWithBearer(requestUrl.toString(), null, new Object(), uriVariables);
        BufferedReader reader = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));
        StringBuffer result = new StringBuffer();
        String line = "";
        while ((line = reader.readLine()) != null) {
            result.append(line);
        }
        EntityUtils.consume(response.getEntity());
        ObjectMapper obj = new ObjectMapper();
        if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
            JsonNode node = obj.readTree(result.toString());
            JsonNode tokenNode = node.get("access_token");
            return tokenNode.textValue();
        }else{
            LOG.error(result.toString());
        }
        return "";
    }

    @Override
    public String getGmailRefreshToken() throws IOException {
        StringBuilder requestUrl = new StringBuilder("https://www.googleapis.com/oauth2/v4/token");
        Map<String, String> uriVariables = new HashMap<>();
//        uriVariables.put("code", AppConstants.GMAIL_LOGIN_CODE);
//        uriVariables.put("client_id", AppConstants.GMAIL_CLIENT_ID);
//        uriVariables.put("client_secret", AppConstants.GMAIL_CLIENT_SECRET);
        uriVariables.put("grant_type", "authorization_code");
        uriVariables.put("redirect-uri", "https://localhost");
        HttpResponse response = WebServiceHelper.postWithBearer(requestUrl.toString(), null, new Object(), uriVariables);
        BufferedReader reader = new BufferedReader(new InputStreamReader(response.getEntity().getContent()));
        StringBuffer result = new StringBuffer();
        String line = "";
        while ((line = reader.readLine()) != null) {
            result.append(line);
        }
        EntityUtils.consume(response.getEntity());
        ObjectMapper obj = new ObjectMapper();
        if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
            JsonNode node = obj.readTree(result.toString());
            JsonNode tokenNode = node.get("refresh_token");
            return tokenNode.textValue();
        }else{
            LOG.error(result.toString());
        }
        return "";
    }

    @Override
    public Date updateCouponRedemptionSwiggy() throws KettleException, IOException {
        String filePath = checkForUpdatedValuesSCR(getAttachmentPath(),"Attachment-Swiggy-Ads");
        if(Objects.isNull(filePath)){
            return null;
        }
        else if(!Objects.equals(filePath,"")){
            filePath = filePath +"/IncrementalValuesExcel/" + "Daily_Swiggy_Coupon_Redemption_Data_Loads.xlsx";
            Pair cornerDates = getExcelStartDate(filePath, SwiggyCouponRedemption.class);
            Date startDate = (Date) cornerDates.getKey();
            Date endDate = (Date) cornerDates.getValue();
            if(Objects.nonNull(startDate)){
                Boolean complete = runPentahoTransformation(filePath, "dailyJobSCR.kjb", startDate);
                if(Boolean.TRUE.equals(complete)){
                    LOG.info("::::::::::::::: Pentaho Transformation Completed ::::::");
                    return endDate;
                }
            }
        }
        return null;
    }

    public String checkForUpdatedValuesSCR(String filePath, String fileName) throws IOException, KettleException {
        DashboardLastUpdateData data = dashboardRefreshDao.getLastRecordDate();
        if(Objects.nonNull(data)){
            Date lastRecordDate = data.getScr();
            FileInputStream inputStream = new FileInputStream(new java.io.File(filePath + "/" + fileName + ".xlsx"));
            Workbook workbook = new XSSFWorkbook(inputStream);
            WorkbookContext workbookContext= ctxFactory.createWorkbook();
            List<ExcelParsingException> errors = new ArrayList<>();
            SheetParser parser = new SheetParser();
            List<SwiggyCouponRedemption> entityList = parser.createEntity(workbook.getSheetAt(4), SwiggyCouponRedemption.class,
                    errors::add);
            if (errors.isEmpty() && !entityList.isEmpty()) {
                entityList = entityList.stream().filter(x -> !x.getDate().equals("")).collect(Collectors.toList());
                entityList = entityList.stream().filter(x -> AppUtils.getDate(x.getDate(),"yyyy-MM-dd").compareTo(lastRecordDate) > 0).collect(Collectors.toList());
                if(!entityList.isEmpty()){
                    SheetContext sheetctx = workbookContext.createSheet("Swiggy_Coupon_Redemption");
                    sheetctx.nextRow()
                            .text("date")
                            .text("order_id")
                            .text("swiggy_burn")
                            .text("restro_burn")
                            .text("restaurant_id")
                            .text("brand_name")
                            .text("business_entity")
                            .text("city")
                            .text("zone")
                            .text("coupon_code")
                            .text("coupon_discount")
                            .text("gmv_total")
                            .text("swiggyit_orders");

                    for (SwiggyCouponRedemption obj : entityList) {
                        sheetctx.nextRow()
                                .text(Objects.nonNull(obj.getDate())?obj.getDate().toString():"")
                                .text(Objects.nonNull(obj.getOrder_id())?obj.getOrder_id().toString():"")
                                .text(Objects.nonNull(obj.getSwiggy_burn())?obj.getSwiggy_burn().toString():"")
                                .text(Objects.nonNull(obj.getRestro_burn())?obj.getRestro_burn().toString():"")
                                .text(Objects.nonNull(obj.getRestaurant_id())?obj.getRestaurant_id().toString():"")
                                .text(Objects.nonNull(obj.getBrand_name())?obj.getBrand_name().toString():"")
                                .text(Objects.nonNull(obj.getBusiness_entity())?obj.getBusiness_entity().toString():"")
                                .text(Objects.nonNull(obj.getCity())?obj.getCity().toString():"")
                                .text(Objects.nonNull(obj.getZone())?obj.getZone().toString():"")
                                .text(Objects.nonNull(obj.getCoupon_code())?obj.getCoupon_code().toString():"")
                                .text(Objects.nonNull(obj.getCoupon_discount())?obj.getCoupon_discount().toString():"")
                                .text(Objects.nonNull(obj.getGmv_total())?obj.getGmv_total().toString():"")
                                .text(Objects.nonNull(obj.getSwiggyit_orders())?obj.getSwiggyit_orders().toString():"");
                    }
                    AppUtils.write(workbookContext.toNativeBytes(), filePath, "IncrementalValuesExcel",
                            "Daily_Swiggy_Coupon_Redemption_Data_Loads.xlsx", LOG);
                    return filePath;
                }
                else{
                    LOG.info("::::::::SCR Dashboard up-to-date::::::");
                }
                return null;
            } else {
                LOG.info("Error Parsing Workbook, total errors :{}", errors.size());
                StringBuilder sb = new StringBuilder();
                errors.forEach(e -> sb.append(e.getMessage() + '\n'));
                LOG.info("{}", sb.toString());
            }
        }
        return "";
    }

    @Override
    public Date updateCouponRedemptionZomato() throws KettleException, IOException {
        String filePath = checkForUpdatedValuesZCR(getAttachmentPath(),"Attachment-Zomato-Redemptions");
        if(Objects.isNull(filePath)){
            return null;
        }
        else if(!Objects.equals(filePath,"")){
            filePath = filePath +"/IncrementalValuesExcel/" + "Daily_Zomato_Coupon_Redemption_Data_Loads.xlsx";
            Pair cornerDates = getExcelStartDate(filePath, ZomatoCouponRedemption.class);
            Date startDate = (Date) cornerDates.getKey();
            Date endDate = (Date) cornerDates.getValue();
            if(Objects.nonNull(startDate)){
                Boolean complete = runPentahoTransformation(filePath, "dailyJobZCR.kjb", null);
                if(Boolean.TRUE.equals(complete)){
                    LOG.info("::::::::::::::: Pentaho Transformation Completed ::::::");
                    return endDate;
                }
            }
        }
        return null;
    }

    public String checkForUpdatedValuesZCR(String filePath, String fileName) throws IOException, KettleException {
        DashboardLastUpdateData data = dashboardRefreshDao.getLastRecordDate();
        if(Objects.nonNull(data)){
            String lastRecordDate = data.getZcr().toString();
            lastRecordDate = lastRecordDate.substring(0,4) + lastRecordDate.substring(5,7) + lastRecordDate.substring(8,10);
            FileInputStream inputStream = new FileInputStream(new java.io.File(filePath + "/" + fileName + ".xlsx"));
            Workbook workbook = new XSSFWorkbook(inputStream);
            WorkbookContext workbookContext= ctxFactory.createWorkbook();
            List<ExcelParsingException> errors = new ArrayList<>();
            SheetParser parser = new SheetParser();
            List<ZomatoCouponRedemption> entityList = parser.createEntity(workbook.getSheetAt(0), ZomatoCouponRedemption.class,
                    errors::add);
            if (errors.isEmpty() && !entityList.isEmpty()) {
                entityList = entityList.stream().filter(x -> !x.getDt().equals("")).collect(Collectors.toList());
                String finalLastRecordDate = lastRecordDate;
                entityList = entityList.stream().filter(x -> Integer.parseInt(x.getDt()) > Integer.parseInt(finalLastRecordDate)).collect(Collectors.toList());
                if(!entityList.isEmpty()){
                    SheetContext sheetctx = workbookContext.createSheet("Zomato_Coupon_Redemption");
                    sheetctx.nextRow()
                            .text("dt")
                            .text("created_at")
                            .text("tab_id")
                            .text("user_id")
                            .text("res_id")
                            .text("res_name")
                            .text("city_name")
                            .text("promo_code")
                            .text("bill_subtotal")
                            .text("zomato_voucher_discount")
                            .text("merchant_voucher_discount")
                            .text("tab_id1");

                    for (ZomatoCouponRedemption obj : entityList) {
                        sheetctx.nextRow()
                                .text(Objects.nonNull(obj.getDt())?obj.getDt().toString():"")
                                .text(Objects.nonNull(obj.getCreated_at())?obj.getCreated_at().toString():"")
                                .text(Objects.nonNull(obj.getTab_id())?obj.getTab_id().toString():"")
                                .text(Objects.nonNull(obj.getUser_id())?obj.getUser_id().toString():"")
                                .text(Objects.nonNull(obj.getRes_id())?obj.getRes_id().toString():"")
                                .text(Objects.nonNull(obj.getRes_name())?obj.getRes_name().toString():"")
                                .text(Objects.nonNull(obj.getCity_name())?obj.getCity_name().toString():"")
                                .text(Objects.nonNull(obj.getPromo_code())?obj.getPromo_code().toString():"")
                                .text(Objects.nonNull(obj.getBill_subtotal())?obj.getBill_subtotal().toString():"")
                                .text(Objects.nonNull(obj.getZomato_voucher_discount())?obj.getZomato_voucher_discount().toString():"")
                                .text(Objects.nonNull(obj.getMerchant_voucher_discount())?obj.getMerchant_voucher_discount().toString():"")
                                .text(Objects.nonNull(obj.getTab_id1())?obj.getTab_id1().toString():"");
                    }
                    AppUtils.write(workbookContext.toNativeBytes(), filePath, "IncrementalValuesExcel",
                            "Daily_Zomato_Coupon_Redemption_Data_Loads.xlsx", LOG);
                    return filePath;
                }
                else{
                    LOG.info("::::::::ZCR Dashboard up-to-date::::::");
                }
                return null;
            } else {
                LOG.info("Error Parsing Workbook, total errors :{}", errors.size());
                StringBuilder sb = new StringBuilder();
                errors.forEach(e -> sb.append(e.getMessage() + '\n'));
                LOG.info("{}", sb.toString());
            }
        }
        return "";
    }

//    @Override
    public String fetchUnreadMail(String accessToken, String label) throws IOException, URISyntaxException {
        StringBuilder requestUrl = new StringBuilder("https://gmail.googleapis.com/gmail/v1/users/me/messages");
        Map<String, String> uriVariables = new HashMap<>();
        uriVariables.put("access_token", accessToken);
        uriVariables.put("labelIds", label);
        uriVariables.put("q", "is:unread");
        try{
            JsonNode response = WebServiceHelper.getRequestWithParamAndHeader(requestUrl.toString(), null, uriVariables, JsonNode.class, false);
            ObjectMapper obj = new ObjectMapper();
            JsonNode node = obj.readTree(response.toString());
            JsonNode messagesNode = node.get("messages").get(0);
            JsonNode message = messagesNode.get("id");
            return message.textValue();
        } catch (Exception e){
            LOG.error("Unable to Fetch mail ::::",e);
        }
        return "";
    }

    @Override
    public String fetchAttachment(String accessToken, String messageId) throws IOException, URISyntaxException {
        StringBuilder requestUrl = new StringBuilder("https://gmail.googleapis.com/gmail/v1/users/me/messages/");
        requestUrl.append(messageId);
        Map<String, String> uriVariables = new HashMap<>();
        uriVariables.put("access_token", accessToken);
        try{
            JsonNode response = WebServiceHelper.getRequestWithParamAndHeader(requestUrl.toString(), null, uriVariables, JsonNode.class, false);
            ObjectMapper obj = new ObjectMapper();
            JsonNode node = obj.readTree(response.toString());
            JsonNode payload = node.get("payload");
            JsonNode parts = payload.get("parts").get(1);
            JsonNode body = parts.get("body");
            JsonNode attachmentId = body.get("attachmentId");
            return attachmentId.textValue();
        } catch (Exception e){
            LOG.error("Unable to Fetch mail ::::",e);
        }
        return "";
    }


    public Boolean downloadAttachment(String accessToken, String messageId, String attachmentId, String identifier) throws IOException, URISyntaxException {
        if(!Objects.equals(identifier, "")){
            StringBuilder requestUrl = new StringBuilder("https://gmail.googleapis.com/gmail/v1/users/me/messages/");
            requestUrl.append(messageId);
            requestUrl.append("/attachments/");
            requestUrl.append(attachmentId);
            Map<String, String> uriVariables = new HashMap<>();
            uriVariables.put("access_token", accessToken);
            try{
                JsonNode response = WebServiceHelper.getRequestWithParamAndHeader(requestUrl.toString(), null, uriVariables, JsonNode.class, false);
                ObjectMapper obj = new ObjectMapper();
                JsonNode node = obj.readTree(response.toString());
                JsonNode dataStr = node.get("data");

                String regex = "cid:[^'\"> ]+";
                Pattern pattern = Pattern.compile(regex);
                Matcher matcher = pattern.matcher(dataStr.textValue());
                String sanitizedContent = matcher.replaceAll("");

                byte[] decodedBytes = Base64.getUrlDecoder().decode(sanitizedContent);
                Files.write(Paths.get(getAttachmentPath() + "Attachment" + identifier + ".xlsx"), decodedBytes);

                return true;
            } catch (Exception e){
                LOG.error("Unable to Fetch mail ::::",e);
            }
            return false;
        }
        return false;
    }

    @Override
    public Boolean markAsRead(String accessToken, String messageId) throws IOException, URISyntaxException {
        StringBuilder requestUrl = new StringBuilder("https://gmail.googleapis.com/gmail/v1/users/me/messages/");
        requestUrl.append(messageId);
        requestUrl.append("/modify");
        Map<String, String> uriVariables = new HashMap<>();
        uriVariables.put("access_token", accessToken);
        JSONParser parser = new JSONParser();
        JSONObject jsonBody = null;
        try{
            String body = "{\"removeLabelIds\": [\"UNREAD\"]}";
            jsonBody = (JSONObject) parser.parse(body);
            HttpResponse httpResponse = WebServiceHelper.postWithBearer(requestUrl.toString(), null, jsonBody, uriVariables);
            JsonNode response = WebServiceHelper.convertResponse(httpResponse, JsonNode.class);
            ObjectMapper obj = new ObjectMapper();
            JsonNode node = obj.readTree(response.toString());
            JsonNode msgId = node.get("id");
            LOG.info(":::::Mail Marked As Read::::: "+ msgId.textValue());
            return true;
        } catch (Exception e){
            LOG.error("Unable to Mark Mail as READ ::::",e);
        }

        return false;
    }

    @Override
    public List<String> fetchAllUnreadMail() throws IOException, URISyntaxException {
        LOG.info("::::::::::::::::::::::::Fetching Mails::::::::::::::::::::::::");
        List<String> downloadedLabels = new ArrayList<>();
        String accessToken = getGmailAccessToken();
        if(!Objects.equals(accessToken, "")){
            LOG.info("::::::::::::::::::::::::Access Token Valid::::::::::::::::::::::::");
            List<String> labels = new ArrayList<String>();
            labels.add(AppConstants.LABEL_SWIGGY_ADS_FUNNEL);
            labels.add(AppConstants.LABEL_ZOMATO_CR);
            labels.add(AppConstants.LABEL_SWIGGY_MTD);
//            labels.add(AppConstants.LABEL_TEST_LABEL);
            for(String label : labels){
                String messageId = fetchUnreadMail(accessToken, label);
                if(!Objects.equals(messageId, "")){
                    LOG.info("::::::::::::::::::::::::Message found for Label {}::::::::::::::::::::::::", label);
                    String attachmentId = fetchAttachment(accessToken, messageId);
                    if(!Objects.equals(attachmentId, "")){
                        LOG.info("::::::::::::::::::::::::Attachment found for Label {}::::::::::::::::::::::::", label);
                        String identifier = "";
                        switch (label){
                            case AppConstants.LABEL_SWIGGY_ADS_FUNNEL: identifier="-Swiggy-Ads"; break;
                            case AppConstants.LABEL_ZOMATO_CR: identifier="-Zomato-Redemptions"; break;
                            case AppConstants.LABEL_SWIGGY_MTD: identifier="-Swiggy-Mtd"; break;
//                            case AppConstants.LABEL_TEST_LABEL: identifier="-Swiggy-Ads"; break;
                            default: break;
                        }
                        if(Boolean.TRUE.equals(downloadAttachment(accessToken, messageId, attachmentId, identifier))){
                            LOG.info("::::::::::::::::::::::::Attachment downloaded for Label {}::::::::::::::::::::::::", label);
                            markAsRead(accessToken, messageId);
                            LOG.info("::::::::::::::::::::::::Message marked as read for Label {}::::::::::::::::::::::::", label);
                            downloadedLabels.add(label);
                        }
                    }
                }
            }

        }
        return downloadedLabels;
    }

    @Override
    public DashboardLastUpdateData getLastRecordDate() {
        return dashboardRefreshDao.getLastRecordDate();
    }

}
