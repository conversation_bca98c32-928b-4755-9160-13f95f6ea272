package com.stpl.tech.report.service.dao;

import com.stpl.tech.report.service.model.CLMDataPushStatus;

import java.util.Date;
import java.util.List;

public interface CleverTapPushV1Dao extends CLMAbstractDao {

    List<Object[]> getCLMProfileBulkData(Integer startId, Integer batchSize, Integer brandId, boolean isTrimmed);

    List<Object[]> getCLMEventBulkData(Integer startId, Integer dataSize, Integer eventId , boolean isTrimmed);

    void updateClmEventCalculation(Date dataPushStartTime, CLMDataPushStatus clmDataPushStatus, Integer eventId, Integer brandId);

    void addDataPushStatus(Integer eventId, Integer batchSize, Integer startId, Integer endId, Integer pageSize,
                           Date startTime, Date endTime, long elapsed, Integer brandId, String status);

    List<Object[]> getCLMChargedOrderData(Date businessDate, Integer brandId);

    void addOrderDataPushStatus(Date businessDate, Integer batchSize, Integer startId, Integer endId, Integer pageSize, Date startTime, Date endTime, long elapsed, Integer brandId, String pushStatus);

    List<Object[]> getCLMChargedOrderDataRange(Integer startCustomerId, Integer endCustomerId, Integer brandId);

    Integer getEventIdForDate(Date date, Integer brandId);

    List<Object[]> getCLMMembershipEventData(Date startDate, Integer brandId);

    List<Object[]> getCLMWalletEventData(Date startDate, Integer brandId);

    void addMembershipDataPushStatus(Date businessDate, Integer batchSize, Integer startId, Integer endId, Integer pageSize, Date startTime, Date endTime, long elapsedTime, Integer brandId, String status);

    void addWalletDataPushStatus(Date businessDate, Integer batchSize, Integer startId, Integer endId, Integer pageSize, Date startTime, Date endTime, long elapsedTime, Integer brandId, String status);
}
