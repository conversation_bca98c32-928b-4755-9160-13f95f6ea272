package com.stpl.tech.report.service.service;

import com.stpl.tech.kettle.data.model.LoyaltyScore;
import com.stpl.tech.master.domain.model.CouponCloneResponse;
import com.stpl.tech.master.util.CustomerEventType;
import com.stpl.tech.report.service.model.CouponMappingRequest;
import com.stpl.tech.report.service.model.DeliveryCouponResponse;

import java.util.List;

public interface CouponService {

    public CouponCloneResponse generateCoupon(int validityDays, String contactNumber, String cloneCode, String prefix);

    public boolean validateLoyaltyPoint(Integer loyaltyPoint, LoyaltyScore loyaltyScore);

    public boolean validateLoyaltyPointForDelivery(Integer loyaltyPoint, LoyaltyScore loyaltyScore);

	boolean updateLoyalty(Integer loyaltyPoints, Integer customerId, String contactNumber,
			CouponCloneResponse couponDetail, LoyaltyScore loyaltyScore);


    public DeliveryCouponResponse createDeliveryCouponLoyaltyBurn(String code, Integer brandId, Integer channelPartnerId, Boolean getClonedCoupon,
                                                                  Integer customerId, String contactNumber, Integer getValidityInDays);
    public boolean updateLoyaltyForDelivery(Integer loyaltyPoints, Integer customerId, String contactNumber,
                                            DeliveryCouponResponse couponDetail, LoyaltyScore loyaltyScore);
    String getDeliveryCouponCode(String contactNumber);

//    String getBirthdayOfferCouponCode(String contactNumber,Integer offerNumber);
    String getCustomerEventOfferCouponCode(String contactNumber,Integer offerNumber,CustomerEventType customerEventType);

    public boolean  publishCustomerEventForEventMonth(CustomerEventType customerEventType);
    public boolean publishEventForBirthdayDataForCurrentMonth(Integer birthdayMonth);

    public boolean generateCouponsForCustomerEventMonth();

    public CouponCloneResponse dynamicCouponMapping(CouponMappingRequest request);

    public CouponCloneResponse generateCouponForCampaign(int validityDays, String contactNumber, String cloneCode, String prefix,
                                                         String acqSrc);

    public void pushEventToCleverTap(CouponCloneResponse response,String eventName,String contactNumber,Integer customerId);

    public CouponCloneResponse mapCouponToCustomer(String contactNumber, List<String> contactNumbersList, String code);


}
