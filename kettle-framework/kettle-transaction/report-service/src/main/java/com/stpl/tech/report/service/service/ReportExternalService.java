package com.stpl.tech.report.service.service;

import com.stpl.tech.kettle.domain.model.OrderDetailEventRequest;

import java.util.Date;
import java.util.Map;

public interface ReportExternalService {
    public boolean publishPartnerOrderDetail(OrderDetailEventRequest req);
    public String getPartnerOrderDetail(String partnerSourceId, String issueType);
    public Map<String,Object> fetchPartnerOrderDetail(String partnerSourceId, String issueType);
    public String getCustomerDetail(String partnerSourceId);
    public String getFallbackOrderCustomerDetail(String partnerCustomerId);
    boolean publishUnlockEvent(String offerSource, String utmSource, Date recordTime);
    public void pushChargedEventForDate(String startDate,String endDate);
    public void pushSwiggyOrsData();
    public void pushSwiggyRejectionData();
}
