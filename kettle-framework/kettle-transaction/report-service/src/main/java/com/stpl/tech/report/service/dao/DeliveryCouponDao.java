package com.stpl.tech.report.service.dao;

import com.stpl.tech.master.data.dao.AbstractMasterDao;
import com.stpl.tech.master.data.model.DeliveryCouponDetailData;

import java.util.List;
import java.util.Set;

public interface DeliveryCouponDao extends AbstractMasterDao {

    List<DeliveryCouponDetailData> getDeilveryCouponByCampaignStrategyAndIsExhustedAndIsRedeemed(String strategy,String isExhusted,
                                                                                                 String isRedeemed);
    List<Integer> getCustomerIdsFromDeliveryAllocation(List<Integer> couponIds);
}
