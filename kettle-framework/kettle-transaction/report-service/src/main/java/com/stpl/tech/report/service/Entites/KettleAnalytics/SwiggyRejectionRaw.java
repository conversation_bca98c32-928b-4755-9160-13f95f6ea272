package com.stpl.tech.report.service.Entites.KettleAnalytics;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Table(name = "SWIGGY_REJECTION_RAW")
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class SwiggyRejectionRaw {

    @Column(name = "BUSINESS_DATE")
    private Date businessDate;

    @Column(name = "BRAND_ID")
    private Long brandId;

    @Column(name = "SWIGGY_RID")
    private Long swiggyRid;

    @Column(name = "ORDER_SOURCE_ID")
    @Id
    private String orderSourceId;

    @Column(name = "CANCELLATION_REASON", columnDefinition = "text")
    private String cancellationReason;

    @Column(name = "CANCELLATION_COMMENT", columnDefinition = "text")
    private String cancellationComment;
}

