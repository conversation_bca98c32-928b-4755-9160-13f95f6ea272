package com.stpl.tech.report.service.controller;


import com.stpl.tech.kettle.data.model.CustomerCampaignJourney;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.report.service.service.CampaignJourneyService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.ws.rs.core.MediaType;

import static com.stpl.tech.report.service.core.ReportServiceConstants.API_VERSION;
import static com.stpl.tech.report.service.core.ReportServiceConstants.CAMPAIGN_ROOT_CONTEXT;
import static com.stpl.tech.report.service.core.ReportServiceConstants.SEPARATOR;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + CAMPAIGN_ROOT_CONTEXT) // v1/customer-campaign
public class CampaignJourneyResource  {
    private static final Logger LOG = LoggerFactory.getLogger(ReportManagementResources.class);

    @Autowired
    private CampaignJourneyService campaignJourneyService;

    @RequestMapping(method = RequestMethod.POST, value = "campaign-journey", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public CustomerCampaignJourney setCustomerJourneyState(@RequestBody CustomerCampaignJourney journeyDetail){
        LOG.info("Request to add a journey state {} for customerId : {} and contactNumber : {}",
                journeyDetail.getFinalState(), journeyDetail.getCustomerId(),
                journeyDetail.getContactNumber());
        return campaignJourneyService.setCustomerJourneyState(journeyDetail);
    }
}
