package com.stpl.tech.report.service.service.impl;

import com.stpl.tech.kettle.customer.service.KeyMutexFactory;
import com.stpl.tech.kettle.data.dao.SuperUCategoryDao;
import com.stpl.tech.kettle.data.dao.SuperUOrderLevelDataDao;
import com.stpl.tech.kettle.data.model.BcxCategorySuperU;
import com.stpl.tech.kettle.data.model.BcxSubCategorySuperU;
import com.stpl.tech.kettle.data.model.SuperUOrderLevelData;
import com.stpl.tech.kettle.data.model.SuperUOrderRatingData;
import com.stpl.tech.kettle.data.model.SuperUOrderRatingType;
import com.stpl.tech.report.service.model.SuperUAggregatedData;
import com.stpl.tech.report.service.model.SuperUCategoryRating;
import com.stpl.tech.report.service.model.SuperUDataResponse;
import com.stpl.tech.report.service.model.SuperUOrderMetaData;
import com.stpl.tech.report.service.model.SuperUOrderTrim;
import com.stpl.tech.report.service.service.SuperUDataCache;
import com.stpl.tech.util.AppUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;


import javax.annotation.PostConstruct;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;


@Service
@Log4j2
public class SuperUDataCacheImpl implements SuperUDataCache {

    Map<Integer, Map<Integer, SuperUOrderLevelData>> unitWiseOrderData;
    Map<Integer, SuperUAggregatedData> unitWiseCurrentDayAggData;
    Map<Integer, SuperUAggregatedData> unitWiseSevenDayAggData;
    @Autowired
    private SuperUCategoryDao superUCategoryDao;

    @Autowired
    private SuperUOrderLevelDataDao superUOrderLevelDataDao;

    @Autowired
    private KeyMutexFactory mutexFactory;
;

    SuperUDataCacheImpl(){
        this.unitWiseCurrentDayAggData = new ConcurrentHashMap<>();
        this.unitWiseOrderData = new ConcurrentHashMap<>();
        this.unitWiseSevenDayAggData = new ConcurrentHashMap<>();
    }

    void initCache(){
       log.info("##### Initializing SuperUDataCache ########");
        this.unitWiseCurrentDayAggData = new ConcurrentHashMap<>();
        this.unitWiseOrderData = new ConcurrentHashMap<>();
        this.unitWiseSevenDayAggData = new ConcurrentHashMap<>();

       Date currentDate =  AppUtils.getCurrentDate();
       Date nextDate = AppUtils.getNextDate(currentDate);
       List<SuperUOrderLevelData> currentData = superUOrderLevelDataDao.findByBillingServerTimeGreaterThanEqualAndBillingServerTimeLessThanAndIsApplicable(currentDate,nextDate,true);
       for(SuperUOrderLevelData data : currentData){
           addDataToCache(data.getUnitId(),data,true);
       }

       Date prevDate = AppUtils.getPreviousDate();
       Date seventhDate =  AppUtils.getPreviousNthDate(7);
       List<SuperUOrderLevelData> sevenDayData = superUOrderLevelDataDao.findByBillingServerTimeGreaterThanEqualAndBillingServerTimeLessThanAndIsApplicable(seventhDate,prevDate,true);
       updateLastSevenDayAggData(sevenDayData);
        log.info("##### Initialization Finished SuperUDataCache ########");
    }

    void initCacheForUnit(Integer unitId){
        log.info("##### Initializing SuperUDataCache for unit id : {} ########",unitId);
         this.unitWiseOrderData.remove(unitId);
         this.unitWiseCurrentDayAggData.remove(unitId);
         this.unitWiseSevenDayAggData.remove(unitId);

        Date currentDate =  AppUtils.getCurrentDate();
        Date nextDate = AppUtils.getNextDate(currentDate);
        List<SuperUOrderLevelData> currentData = superUOrderLevelDataDao.findByBillingServerTimeGreaterThanEqualAndBillingServerTimeLessThanAndUnitIdAndIsApplicable(currentDate,nextDate,unitId,true);
        for(SuperUOrderLevelData data : currentData){
            addDataToCache(data.getUnitId(),data,true);
        }

        Date prevDate = AppUtils.getPreviousDate();
        Date seventhDate =  AppUtils.getPreviousNthDate(7);
        List<SuperUOrderLevelData> sevenDayData = superUOrderLevelDataDao.findByBillingServerTimeGreaterThanEqualAndBillingServerTimeLessThanAndUnitIdAndIsApplicable(seventhDate,prevDate,unitId,true);
        updateLastSevenDayAggData(sevenDayData);
        log.info("##### Initialization Finished SuperUDataCache for unit id : {} ########",unitId);
    }




    @Override
    public void addDataToCache(Integer unitId, SuperUOrderLevelData superUOrderLevelData,boolean updateAggregation){

       Map<Integer,SuperUOrderLevelData> orderLevelMap =   unitWiseOrderData.getOrDefault(unitId,new ConcurrentHashMap<>());
       orderLevelMap.put(superUOrderLevelData.getOrderLevelId(),superUOrderLevelData);
       unitWiseOrderData.put(unitId,orderLevelMap);
       if(updateAggregation){
           updateAggrgatedData(unitWiseCurrentDayAggData,unitId,superUOrderLevelData.getOrderRatingData());
       }
    }


    public void updateLastSevenDayAggData(List<SuperUOrderLevelData> orderLevelSevenDayData){

        for(SuperUOrderLevelData superUOrderLevelData : orderLevelSevenDayData){
            updateAggrgatedData(unitWiseSevenDayAggData,superUOrderLevelData.getUnitId(),superUOrderLevelData.getOrderRatingData());
        }

    }

    private void updateAggrgatedData(Map<Integer,SuperUAggregatedData> aggregatedDataMap, Integer unitId, List<SuperUOrderRatingData> orderRatingDataList){
        synchronized (mutexFactory.getMutex("SUPERU_U_"+unitId)){
                SuperUAggregatedData aggregatedData = aggregatedDataMap.getOrDefault(unitId, new SuperUAggregatedData());

                for (SuperUOrderRatingData ratingData : orderRatingDataList) {
                    if (ratingData.getType() == SuperUOrderRatingType.SUB_CATEGORY
                            && ratingData.getKey().equalsIgnoreCase("appropriateUpsell") && ratingData.getIsApplicable()) {
                        aggregatedData.setTotalOrderForUpsell(aggregatedData.getTotalOrderForUpsell() + 1);
                        if (ratingData.getValue().compareTo(BigDecimal.valueOf(1)) == 0) {
                            aggregatedData.setUpsellOrderCount(aggregatedData.getUpsellOrderCount() + 1);
                        }
                    }

                    if (ratingData.getType() == SuperUOrderRatingType.CATEGORY && ratingData.getIsApplicable()) {
                        if (ratingData.getKey().equalsIgnoreCase("New Customer")) {

                            BigDecimal total = aggregatedData.getDelightTotal().add(ratingData.getValue());
                            aggregatedData.setDelightTotal(total);

                            int count = aggregatedData.getDelightCount() + 1;
                            aggregatedData.setDelightCount(count);

                            aggregatedData.setDelight(total.divide(BigDecimal.valueOf(count), 2, RoundingMode.HALF_UP));

                        } else if (ratingData.getKey().equalsIgnoreCase("Upsell")) {
                            BigDecimal total = aggregatedData.getUpsellTotal().add(ratingData.getValue());
                            aggregatedData.setUpsellTotal(total);

                            int count = aggregatedData.getUpsellCount() + 1;
                            aggregatedData.setUpsellCount(count);

                            aggregatedData.setUpsell(total.divide(BigDecimal.valueOf(count), 2, RoundingMode.HALF_UP));
                        } else if (ratingData.getKey().equalsIgnoreCase("Experience")) {
                            BigDecimal total = aggregatedData.getCommunicationTotal().add(ratingData.getValue());
                            aggregatedData.setCommunicationTotal(total);

                            int count = aggregatedData.getCommunicationCount() + 1;
                            aggregatedData.setCommunicationCount(count);

                            aggregatedData.setCommunication(total.divide(BigDecimal.valueOf(count), 2, RoundingMode.HALF_UP));
                        }
                    }
                }
                aggregatedDataMap.put(unitId, aggregatedData);
        }
    }

    @Override
    public void updateOrderLevelData(Integer unitId, SuperUOrderLevelData orderLevelData){
        Map<Integer,SuperUOrderLevelData> orderLevelMap =   unitWiseOrderData.getOrDefault(unitId,new ConcurrentHashMap<>());
        orderLevelMap.put(orderLevelData.getOrderLevelId(), orderLevelData);
        unitWiseOrderData.put(unitId,orderLevelMap);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public SuperUDataResponse getSuperUData(Integer unitId, Integer page, Integer pageSize, boolean cacheRefresh){
        SuperUDataResponse superUDataResponse = new SuperUDataResponse();

        SuperUOrderMetaData currentDayData = new SuperUOrderMetaData();

        Map<Integer,SuperUOrderLevelData> orderLevelMap =   this.unitWiseOrderData.getOrDefault(unitId,new ConcurrentHashMap<>());
        SuperUAggregatedData aggregatedData = this.unitWiseCurrentDayAggData.getOrDefault(unitId, new SuperUAggregatedData());
        SuperUAggregatedData aggregatedSevenDayData = this.unitWiseSevenDayAggData.getOrDefault(unitId,null);

        if(orderLevelMap.isEmpty() || aggregatedSevenDayData==null || cacheRefresh){
             initCacheForUnit(unitId);
        }

         orderLevelMap =   this.unitWiseOrderData.getOrDefault(unitId,new ConcurrentHashMap<>());
         aggregatedData = this.unitWiseCurrentDayAggData.getOrDefault(unitId, new SuperUAggregatedData());
         aggregatedSevenDayData = this.unitWiseSevenDayAggData.getOrDefault(unitId,new SuperUAggregatedData());

        List<SuperUOrderTrim> orderTrimList = new ArrayList<>();
        List<BcxCategorySuperU> categories = superUCategoryDao.findAll();
        List<BcxSubCategorySuperU> subCats = new ArrayList<>();
        categories.forEach(e->{
            subCats.addAll(e.getSubCategorySuperu());
        });

        Map<Integer, BcxSubCategorySuperU> subCatMap = subCats.stream().collect(Collectors.toMap((e)->e.getBcxSubCategoryId(),(e)->e));

        Date currentDate =  AppUtils.getCurrentDate();
        Date nextDate = AppUtils.getNextDate(currentDate);
        int readCount = 0;
        for(SuperUOrderLevelData superUOrderLevelData : orderLevelMap.values()){
            if(superUOrderLevelData.getBillingServerTime().compareTo(currentDate) >= 0
                   && superUOrderLevelData.getBillingServerTime().compareTo(nextDate) < 0 ){
              if(superUOrderLevelData.getIsFeedbackRead()){
                  readCount++;
              }
               SuperUOrderTrim orderTrim = new SuperUOrderTrim();
               orderTrim.setOrderId(superUOrderLevelData.getOrderId());
               orderTrim.setUnitId(superUOrderLevelData.getUnitId());
               orderTrim.setOrderDetails(superUOrderLevelData.getOrderDetails());
               orderTrim.setFeedback(superUOrderLevelData.getFeedback());
               orderTrim.setOrderLevelId(superUOrderLevelData.getOrderLevelId());
               orderTrim.setBillingServerTime(superUOrderLevelData.getBillingServerTime());
               orderTrim.setOrderRating(superUOrderLevelData.getOrderRating());
               orderTrim.setIsFeedbackRead(superUOrderLevelData.getIsFeedbackRead());
               orderTrim.setCustomerName(superUOrderLevelData.getCustomerName());
                List<SuperUCategoryRating> categoryRatings = new ArrayList<>();

               for(SuperUOrderRatingData ratingData : superUOrderLevelData.getOrderRatingData()){
                   SuperUCategoryRating superUCategoryRating = new SuperUCategoryRating();
                   if(ratingData.getType() == SuperUOrderRatingType.SUB_CATEGORY && ratingData.getIsApplicable()){
                       BcxSubCategorySuperU subCategorySuperU = subCatMap.get(ratingData.getKeyId());
                           if(subCategorySuperU.getFeedBack()!=null && subCategorySuperU.getSubCatAlias()!=null){
                               superUCategoryRating.setName(subCategorySuperU.getSubCatAlias());
                               superUCategoryRating.setRating(ratingData.getValue());
                               superUCategoryRating.setCategoryId(ratingData.getKeyId());
                               categoryRatings.add(superUCategoryRating);
                           }
                   }
               }
               orderTrim.setOrderCatRating(categoryRatings);
               orderTrimList.add(orderTrim);
           }
        }

        currentDayData.setOrders(paginateOrders(orderTrimList,page,pageSize));


//        List<String> categories = Arrays.asList("New Customer", "Upsell", "Experience");

         List<SuperUCategoryRating> categoryRatingList = new ArrayList<>();
        for(BcxCategorySuperU cat : categories){
            SuperUCategoryRating categoryRating = new SuperUCategoryRating();
            if(cat.getCategoryAlias().equalsIgnoreCase("New Customer") && aggregatedData.getDelightCount() !=0){
                categoryRating.setName(cat.getCategoryAlias());
                categoryRating.setRating(aggregatedData.getDelight().multiply(BigDecimal.valueOf(5)));
                categoryRating.setCategoryId(cat.getBcxCategorySuperuId());
            }else if(cat.getCategoryAlias().equalsIgnoreCase("Experience") && aggregatedData.getCommunicationCount()!=0){
                categoryRating.setName(cat.getCategoryAlias());
                categoryRating.setRating(aggregatedData.getCommunication().multiply(BigDecimal.valueOf(5)));
                categoryRating.setCategoryId(cat.getBcxCategorySuperuId());
            } else if (cat.getCategoryAlias().equalsIgnoreCase("Upsell") && aggregatedData.getUpsellCount()!=0) {
                categoryRating.setName(cat.getCategoryAlias());
                categoryRating.setRating(aggregatedData.getUpsell().multiply(BigDecimal.valueOf(5)));
                categoryRating.setCategoryId(cat.getBcxCategorySuperuId());
            }
            categoryRatingList.add(categoryRating);
        }

        currentDayData.setUpsellOrderCount(aggregatedData.getUpsellOrderCount());
        currentDayData.setTotalOrderForUpsell(aggregatedData.getTotalOrderForUpsell());
        currentDayData.setFeedBack(getOverallFeedback(categoryRatingList,categories));
        currentDayData.setCategoryRating(categoryRatingList);
        currentDayData.setTotalOrders(orderTrimList.size());
        currentDayData.setReadCount(readCount);
        superUDataResponse.setCurrentDayData(currentDayData);



        List<SuperUCategoryRating> categoryRatingSevenDayList = new ArrayList<>();
        for(BcxCategorySuperU cat : categories){
            SuperUCategoryRating categoryRating = new SuperUCategoryRating();
            if(cat.getCategoryAlias().equalsIgnoreCase("New Customer") && aggregatedSevenDayData.getDelightCount() !=0){
                categoryRating.setName(cat.getCategoryAlias());
                categoryRating.setRating(aggregatedSevenDayData.getDelight().multiply(BigDecimal.valueOf(5)));
                categoryRating.setCategoryId(cat.getBcxCategorySuperuId());
            }else if(cat.getCategoryAlias().equalsIgnoreCase("Experience") && aggregatedSevenDayData.getCommunicationCount()!=0){
                categoryRating.setName(cat.getCategoryAlias());
                categoryRating.setRating(aggregatedSevenDayData.getCommunication().multiply(BigDecimal.valueOf(5)));
                categoryRating.setCategoryId(cat.getBcxCategorySuperuId());
            } else if (cat.getCategoryAlias().equalsIgnoreCase("Upsell") && aggregatedSevenDayData.getUpsellCount()!=0) {
                categoryRating.setName(cat.getCategoryAlias());
                categoryRating.setRating(aggregatedSevenDayData.getUpsell().multiply(BigDecimal.valueOf(5)));
                categoryRating.setCategoryId(cat.getBcxCategorySuperuId());
            }
            categoryRatingSevenDayList.add(categoryRating);
        }

        SuperUOrderMetaData sevenDayData = new SuperUOrderMetaData();
        sevenDayData.setFeedBack(getOverallFeedback(categoryRatingSevenDayList,categories));
        sevenDayData.setCategoryRating(categoryRatingSevenDayList);
        sevenDayData.setTotalOrderForUpsell(aggregatedSevenDayData.getTotalOrderForUpsell());
        sevenDayData.setUpsellOrderCount(aggregatedSevenDayData.getUpsellOrderCount());

        superUDataResponse.setSevenDayData(sevenDayData);
        return superUDataResponse;
    }


    private String getOverallFeedback(List<SuperUCategoryRating> categoryRatingList,List<BcxCategorySuperU> categories){
        BigDecimal min = BigDecimal.valueOf(Double.MAX_VALUE);
        BcxCategorySuperU minCategory = null;
        Map<Integer,BcxCategorySuperU> categorySuperUMap = categories.stream().collect(Collectors.toMap((e)->e.getBcxCategorySuperuId(),(e)->e));
        for(SuperUCategoryRating categoryRating : categoryRatingList){
            if(categoryRating.getCategoryId()!=null){
                if(categoryRating.getRating().compareTo(min)<0){
                    min = categoryRating.getRating();
                    minCategory = categorySuperUMap.get(categoryRating.getCategoryId());
                }

                if(categoryRating.getRating().compareTo(min)==0){
                    if(categorySuperUMap.get(categoryRating.getCategoryId()).getPriorityOrder() < minCategory.getPriorityOrder()){
                        min = categoryRating.getRating();
                        minCategory = categorySuperUMap.get(categoryRating.getCategoryId());
                    }
                }
            }
        }
        String defaultFeedback = categories.get(0).getDefaultFeedBack();

        if(minCategory==null || min.compareTo(BigDecimal.valueOf(5))==0){
            return defaultFeedback;
        }
        return minCategory.getFeedback();
    }


    private static  List<SuperUOrderTrim> paginateOrders(List<SuperUOrderTrim> list, int page, int pageSize) {
        if (list == null || pageSize <= 0 || page <= 0) {
            throw new IllegalArgumentException("Invalid page or page size");
        }
        list.sort((a, b) -> b.getBillingServerTime().compareTo(a.getBillingServerTime()));
        int fromIndex = (page - 1) * pageSize;
        int toIndex = Math.min(fromIndex + pageSize, list.size());
        if (fromIndex >= list.size()) {
            return new ArrayList<>();
        }
        return list.subList(fromIndex, toIndex);
    }
    @Scheduled(cron = "0 10 0 * * *", zone = "GMT+05:30")
    public void resetCache(){
        this.initCache();
    }

}
