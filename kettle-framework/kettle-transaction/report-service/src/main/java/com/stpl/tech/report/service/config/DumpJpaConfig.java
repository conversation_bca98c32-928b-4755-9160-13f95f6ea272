package com.stpl.tech.report.service.config;

import com.stpl.tech.report.service.core.ReportProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;
import java.util.Properties;


@Configuration
@EnableJpaRepositories(basePackages = {
        "com.stpl.tech.report.service.dao"}, entityManagerFactoryRef = "DumpDataSourceEMFactory", transactionManagerRef = "DumpDataSourceTM")
public class DumpJpaConfig {
    @Autowired
    private ReportProperties env;


    final Properties masterAdditionalProperties() {
        final Properties hibernateProperties = new Properties();
        hibernateProperties.setProperty("hibernate.hbm2ddl.auto", env.getHibernateAuto());
        hibernateProperties.setProperty("hibernate.dialect", env.getHibernateDialect());
        return hibernateProperties;
    }


    @Bean(name = "DumpDataSourceEMFactory")
    public LocalContainerEntityManagerFactoryBean dumpDataSourceEMFactory() {
        final LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
        em.setDataSource(dumpDataSource());
        em.setPackagesToScan("com.stpl.tech.report.service.Entites.KettleAnalytics");
        final HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
        em.setJpaVendorAdapter(vendorAdapter);
        em.setJpaProperties(masterAdditionalProperties());
        em.setPersistenceUnitName("DumpDataSourcePUName");
        return em;
    }

    @Bean(name = "DumpDataSource")
    public DataSource dumpDataSource() {
        final DriverManagerDataSource dataSource = new DriverManagerDataSource();
        dataSource.setDriverClassName(env.getDumpJdbcDriverClassName());
        dataSource.setUrl(env.getDumpJdbcUrl());
        dataSource.setUsername(env.getDumpJdbcUserName());
        dataSource.setPassword(env.getDumpPass());
        return dataSource;
    }
    @Bean(name = "DumpDataSourceTM")
    public PlatformTransactionManager dumpTransactionManager() {
        final JpaTransactionManager transactionManager = new JpaTransactionManager();
        transactionManager.setEntityManagerFactory(dumpDataSourceEMFactory().getObject());
        return transactionManager;
    }

}
