/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.report.service.core;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import com.stpl.tech.util.EnvType;


@Service
public class ReportProperties {

    @Autowired
    private Environment env;

    public EnvType getEnvironmentType() {
        return EnvType.valueOf(env.getProperty("environment.type"));
    }

    public String getTableauAuthToken() {
        return env.getProperty("tableau.auth.token");
    }

    public String getGoogleClientId() {
        return env.getProperty("google.client.id");
    }

    public String getGoogleClientSecret() {
        return env.getProperty("google.client.secret");
    }
    public String getGoogleRefreshToken() {
        return env.getProperty("google.refresh.token");
    }

    public String getBasePath() {
        return env.getProperty("server.base.dir");
    }

    public String getS3Bucket() {
        return env.getProperty("amazon.s3.bucket", "chaayosdevtest");
    }

    public String getS3ReportBucket() {
        return env.getProperty("amazon.s3.report.bucket", "chaayosdevtest");
    }

    public String getJdbcPassword(){
        return env.getProperty("clm.jdbc.pass");
    }

    public String getJdbcUserName(){
        return env.getProperty("clm.jdbc.user");
    }

    public String getJdbcDriverClassName(){
        return env.getProperty("clm.jdbc.driverClassName");
    }

    public String getUrl(){
        return env.getProperty("clm.jdbc.url");
    }

    public String getHibernateAuto(){
        return env.getProperty("hibernate.hbm2ddl.auto");
    }

    public String getHibernateDialect(){
        return    env.getProperty("hibernate.dialect");
    }

    public String getHibernateSql(){
        return env.getProperty("hibernate.show_sql");
    }

    public String getChaayosClevertapAccount() {return  env.getProperty("clevertap.accountId.chaayos");}

    public String getChaayosClevertapPasscode() {return  env.getProperty("clevertap.passcode.chaayos");}

    public String getGntClevertapAccount() {return  env.getProperty("clevertap.accountId.gnt");}

    public String getGntClevertapPasscode() {return  env.getProperty("clevertap.passcode.gnt");}
    
    public String getRekognitionJdbcPassword(){
        return env.getProperty("rkg.jdbc.pass");
    }

    public String getRekognitionJdbcUserName(){
        return env.getProperty("rkg.jdbc.user");
    }

    public String getRekognitionJdbcDriverClassName(){
        return env.getProperty("rkg.jdbc.driverClassName");
    }
    
	public String getRekognitionJdbcUrl() {
		return env.getProperty("rkg.jdbc.url");
	}
    public String getDumpPass(){
        return env.getProperty("dump.jdbc.pass");
    }

    public String getDumpJdbcUserName(){
        return env.getProperty("dump.jdbc.user");
    }

    public String getDumpJdbcDriverClassName(){
        return env.getProperty("dump.jdbc.driverClassName");
    }

    public String getDumpJdbcUrl() {
        return env.getProperty("dump.jdbc.url");
    }

    public String getScmJdbcDriverClassName() { return env.getProperty("dump.scmArchive.jdbc.driverClassName"); }

    public String getScmJdbcUrl() { return env.getProperty("dump.scmArchive.jdbc.url"); }

    public String getScmJdbcUserName() { return env.getProperty("dump.scmArchive.jdbc.user"); }

    public String getScmPass() { return env.getProperty("dump.scmArchive.jdbc.pass"); }

    public String getReportAuthInternal(){ return env.getProperty("report.auth.internal"); }
}
