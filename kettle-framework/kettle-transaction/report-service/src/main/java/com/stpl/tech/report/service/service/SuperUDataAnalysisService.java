package com.stpl.tech.report.service.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.stpl.tech.kettle.data.model.SuperUOrderAnalysisData;
import com.stpl.tech.report.service.model.SuperUAlert;

import java.text.ParseException;
import java.util.List;

public interface SuperUDataAnalysisService {

    List<SuperUOrderAnalysisData> generateRatingData(Integer unitId) throws JsonProcessingException, ParseException;

    boolean markFeedbackRead(Integer orderLevelId);

    SuperUAlert getSuperAlertData(Integer unitId);
}