/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.report.service.model;

import com.stpl.tech.master.data.model.XMLReportDefinitionData;
import com.stpl.tech.master.domain.model.Adapter1;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.util.Date;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "fileName", "lastModified" })
@XmlRootElement(name = "FileOutputDetails")
public class FileOutputDetails {

	@XmlElement(required = true, nillable = false)
	protected String fileName;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "date")
	protected Date lastModified;

	protected XMLReportDefinitionData reportDetail;

	public String getFileName() {
		return fileName;
	}

	public FileOutputDetails() {

	}

	public FileOutputDetails(String fileName, Date lastModified) {
		super();
		this.fileName = fileName;
		this.lastModified = lastModified;
	}

	public FileOutputDetails(String fileName, Date lastModified, XMLReportDefinitionData reportDef) {
		super();
		this.fileName = fileName;
		this.lastModified = lastModified;
		this.reportDetail =  reportDef;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public Date getLastModified() {
		return lastModified;
	}

	public void setLastModified(Date lastModified) {
		this.lastModified = lastModified;
	}

	public XMLReportDefinitionData getReportDetail() {
		return reportDetail;
	}

	public void setReportDetail(XMLReportDefinitionData reportDetail) {
		this.reportDetail = reportDetail;
	}
}
