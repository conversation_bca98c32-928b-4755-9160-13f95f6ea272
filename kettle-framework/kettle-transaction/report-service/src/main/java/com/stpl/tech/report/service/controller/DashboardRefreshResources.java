package com.stpl.tech.report.service.controller;

import com.stpl.tech.kettle.clm.model.DashboardLastUpdateData;
import com.stpl.tech.report.service.model.DashboardRefreshFlowType;
import com.stpl.tech.report.service.service.DashboardRefreshService;
import com.stpl.tech.report.service.service.ChannelPartnerOrsRejectionDataService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.pentaho.di.core.exception.KettleException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.MediaType;
import java.io.IOException;
import java.net.URISyntaxException;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import static com.stpl.tech.report.service.core.ReportServiceConstants.*;

@RestController
@RequestMapping( value = API_VERSION + SEPARATOR + DASHBOARD_REFRESH_ROOT_CONTEXT)
public class DashboardRefreshResources{

    @Autowired
    private DashboardRefreshService dashboardRefreshService;

    private static final Logger LOG = LoggerFactory.getLogger(DashboardRefreshResources.class);

    @Autowired
    private ChannelPartnerOrsRejectionDataService channelPartnerOrsRejectionDataService;
    @Scheduled(cron="0 0 12-18 * * MON-SUN", zone = "GMT+05:30")
    @RequestMapping(method = RequestMethod.GET, value = "update/ratings-zomato", produces = MediaType.APPLICATION_JSON)
    public Boolean updateZomatoRatingsData() throws KettleException, IOException, URISyntaxException {
        try{
            Date endDate = null;
            Date endDateGV = null;
            Date endDateILR = null;
            DashboardLastUpdateData data = dashboardRefreshService.getLastRecordDate();
            if(Objects.nonNull(data)) {
                if(Objects.nonNull(data.getZfr())){
                    if(AppUtils.getDayDifference(AppUtils.getDate(data.getZfr()), AppUtils.getBusinessDate()) > 1){
                        Boolean sheetPopulatedZFR = dashboardRefreshService.fetchDataToAdjunctSheet(AppConstants.ZFR_SHEET);
                        if (Boolean.TRUE.equals(sheetPopulatedZFR)) {
                            endDate = dashboardRefreshService.updateZomatoRatingsData("Adjunct_Sheet");
                        }
                    }
                }
                if(Objects.nonNull(data.getZilr())){
                    if(AppUtils.getDayDifference(AppUtils.getDate(data.getZilr()), AppUtils.getBusinessDate()) > 1){
                        Boolean sheetPopulatedILR = dashboardRefreshService.fetchDataToAdjunctSheet(AppConstants.ILR_SHEET);
                        if (Boolean.TRUE.equals(sheetPopulatedILR)) {
                            endDateILR = dashboardRefreshService.updateZomatoItemLevelRating("Adjunct_Sheet");
                        }
                    }
                }
                if (Objects.nonNull(endDate) || Objects.nonNull(endDateILR)) {
                    dashboardRefreshService.runJob(AppConstants.TABLEAU_SITE_ID_ZFR, AppConstants.TABLEAU_WORKBOOK_ID_ZFR);
                }
                if(Objects.nonNull(data.getZgv())) {
                    if (AppUtils.getDayDifference(AppUtils.getDate(data.getZgv()), AppUtils.getBusinessDate()) > 1) {
                        Boolean sheetPopulatedGV = dashboardRefreshService.fetchDataToAdjunctSheet(AppConstants.GV_SHEET);
                        if (Boolean.TRUE.equals(sheetPopulatedGV)) {
                            endDateGV = dashboardRefreshService.updateZomatoGridVisibility("Adjunct_Sheet");
                        }
                    }
                }

                // update zomato promo redemption
                Date endDateZPR = null;
                if(Objects.nonNull(data.getZomatoPromoRedemption())){
                    if(AppUtils.getDayDifference(AppUtils.getDate(data.getZomatoPromoRedemption()), AppUtils.getBusinessDate()) > 1){
                        Boolean sheetPopulatedPRS = dashboardRefreshService.fetchDataToAdjunctSheet(AppConstants.PROMO_REDEMPTION_SHEET);
                        if (Boolean.TRUE.equals(sheetPopulatedPRS)) {
                            endDateZPR = dashboardRefreshService.updateZomatoPromoRedemption("Adjunct_Sheet");
                        }
                    }
                }

                if (Objects.nonNull(endDate) || Objects.nonNull(endDateGV) || Objects.nonNull(endDateILR)
                        || Objects.nonNull(endDateZPR)) {
                    return dashboardRefreshService.postLastUpdate(endDate, endDateGV, endDateILR, DashboardRefreshFlowType.ZOMATO_FOOD_RATING_DATA.value(), endDateZPR);
                }
                return false;
            }
            return false;
        } catch (Exception e){
            LOG.error("::::::::::::::::: Error while updating Zomato Ratings :::::::::::::: {}",e);
        }
        return false;
    }

    @Scheduled(cron="0 45 11-15 * * MON-SUN", zone = "GMT+05:30")
    @RequestMapping(method = RequestMethod.POST, value = "process/mails", produces = MediaType.APPLICATION_JSON)
    public Boolean fetchAllUnreadMail() throws KettleException, IOException, URISyntaxException {
        try{
            List<String> downloadedLabels = dashboardRefreshService.fetchAllUnreadMail();
            if(Objects.nonNull(downloadedLabels) && !downloadedLabels.isEmpty()){
                for(String label : downloadedLabels) {
                    if (Objects.equals(label, AppConstants.LABEL_SWIGGY_ADS_FUNNEL)) {
                        updateCouponRedemption("SWIGGY");
                        channelPartnerOrsRejectionDataService.updateSwiggyRatingData();

                    }
                    else if (Objects.equals(label, AppConstants.LABEL_ZOMATO_CR)){
                        updateCouponRedemption("ZOMATO");
                    }
                    else if (Objects.equals(label, AppConstants.LABEL_SWIGGY_MTD)){
                        channelPartnerOrsRejectionDataService.updateSwiggyOrsRejection();
                    }
                }
                return true;
            }
        } catch (Exception e){
            LOG.error("::::::::::::::::: Error while processing mails :::::::::::::: {}",e);
        }
        return false;
    }

    @RequestMapping(method = RequestMethod.POST, value = "update/coupon-redemption", produces = MediaType.APPLICATION_JSON)
    public Boolean updateCouponRedemption(String channelPartner) throws KettleException, IOException, URISyntaxException {
        try{
            if(Objects.nonNull(channelPartner)) {
                if(Objects.equals(channelPartner, "SWIGGY")){
                    Date endDate_S = dashboardRefreshService.updateCouponRedemptionSwiggy();
                    if(Objects.nonNull(endDate_S)){
                        dashboardRefreshService.runJob(AppConstants.TABLEAU_SITE_ID_CR, AppConstants.TABLEAU_WORKBOOK_ID_CR);
                        return dashboardRefreshService.postLastUpdate(endDate_S, null, null, DashboardRefreshFlowType.SWIGGY_COUPON_REDEMPTION.value(), null);
                    }
                }
                else if(Objects.equals(channelPartner, "ZOMATO")){
                    Date endDate_Z = dashboardRefreshService.updateCouponRedemptionZomato();
                    if(Objects.nonNull(endDate_Z)){
                        dashboardRefreshService.runJob(AppConstants.TABLEAU_SITE_ID_CR,AppConstants.TABLEAU_WORKBOOK_ID_CR);
                        return dashboardRefreshService.postLastUpdate(endDate_Z, null, null, DashboardRefreshFlowType.ZOMATO_COUPON_REDEMPTION.value(), null);
                        }
                    }
                }
            } catch (Exception e){
            LOG.error("::::::::::::::::: Error while updating Coupon Redemption :::::::::::::: {}",e);
        }
        return false;
    }

//    @RequestMapping(method = RequestMethod.POST, value = "update/ratings-swiggy", produces = MediaType.APPLICATION_JSON)
//    public Boolean updateSwiggyRatingData(String fileType) throws KettleException, IOException, URISyntaxException {
////        Date endDateSFR = null;
////        endDateSFR = dashboardRefreshService.update("Adjunct_Sheet");
//
//        return false;
//    }
    
//    @RequestMapping(method = RequestMethod.POST, value = "tableau/extract/refresh", produces = MediaType.APPLICATION_JSON)
//    public Boolean refreshExtract(@RequestBody String token,
//                                            @RequestParam(value = "siteId") String siteId,
//                                            @RequestParam(value = "workbookId") String workbookId) {
//        return dashboardRefreshService.refreshExtract(siteId, workbookId, token);
//    }

//    @RequestMapping(method = RequestMethod.GET, value = "gmail/refresh-token", produces = MediaType.APPLICATION_JSON)
//    public String getGmailRefreshToken() throws IOException {
//        return dashboardRefreshService.getGmailRefreshToken();
//    }

//    @RequestMapping(method = RequestMethod.GET, value = "gmail/access-token", produces = MediaType.APPLICATION_JSON)
//    public String getGmailAccessToken() throws IOException, URISyntaxException {
//        return dashboardRefreshService.getGmailAccessToken();
//    }

//    @RequestMapping(method = RequestMethod.GET, value = "gmail/fetch", produces = MediaType.APPLICATION_JSON)
//    public String fetchUnreadMails() throws IOException {
//        return dashboardRefreshService.fetchUnreadMail();
//    }

//    @RequestMapping(method = RequestMethod.POST, value = "data/fetch", produces = MediaType.APPLICATION_JSON)
//    public Boolean fetchDataToAdjunctSheet() throws IOException, URISyntaxException {
//        return dashboardRefreshService.fetchDataToAdjunctSheet(AppConstants.ZFR_SHEET);
//    }

//    @RequestMapping(method = RequestMethod.GET, value = "tableau/login", produces = MediaType.APPLICATION_JSON)
//    public String loginTableau() throws JsonProcessingException {
//        return dashboardRefreshService.loginTableau();
//    }

//    @RequestMapping(method = RequestMethod.GET, value = "tableau/logout", produces = MediaType.APPLICATION_JSON)
//    public Boolean logoutTableau(@RequestBody String token) {
//        return dashboardRefreshService.logoutTableau(token);
//    }

//    @RequestMapping(method = RequestMethod.GET, value = "run-pentaho-transformation", produces = MediaType.APPLICATION_JSON)
//    public Boolean runPentahoTransformation() throws KettleException {
//        return dashboardRefreshService.runPentahoTransformation("abc", "dailyJob.kjb");
//    }

    @Scheduled(cron = "0 0 13 * * *", zone = "GMT+05:30")
    @GetMapping("update-zomato-ors-data")
    public void updateZomatoOrsData() throws KettleException, IOException, URISyntaxException {
        channelPartnerOrsRejectionDataService.updateZomatoOrsData();
    }

    @Scheduled(cron = "0 0 13 * * *", zone = "GMT+05:30")
    @GetMapping("update-zomato-rejection-data")
    public void updateZomatoRejectionData() throws KettleException, IOException, URISyntaxException {
        channelPartnerOrsRejectionDataService.updateZomatoRejection();
    }


    @GetMapping("update-swiggy-rating-data")
    public void updateSwiggyRatingData() throws KettleException, IOException, URISyntaxException {
        channelPartnerOrsRejectionDataService.updateSwiggyRatingData();
    }

    @GetMapping("update-swiggy-ors-rejection-data")
    public void updateSwiggyOrsRejectionData() throws KettleException, IOException, URISyntaxException {
        channelPartnerOrsRejectionDataService.updateSwiggyOrsRejection();
    }
}
