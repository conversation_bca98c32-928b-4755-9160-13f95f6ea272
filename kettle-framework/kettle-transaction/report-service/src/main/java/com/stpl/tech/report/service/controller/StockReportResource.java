package com.stpl.tech.report.service.controller;

import com.paytm.pg.App;
import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.core.data.vo.UnitTimeStockData;
import com.stpl.tech.kettle.stock.service.AutomatedStockEventReport;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.JSONSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.stpl.tech.report.service.core.ReportServiceConstants.API_VERSION;
import static com.stpl.tech.report.service.core.ReportServiceConstants.SEPARATOR;
import static com.stpl.tech.report.service.core.ReportServiceConstants.STOCK_OUT_REPORT_ROOT_CONTEXT;

@RestController
@RequestMapping(value = API_VERSION+SEPARATOR+STOCK_OUT_REPORT_ROOT_CONTEXT)
public class StockReportResource {

    @Autowired
    private AutomatedStockEventReport reportService;

    @Autowired
    private MasterDataCache masterCache;

    private static final Logger LOG = LoggerFactory.getLogger(StockReportResource.class);
    @RequestMapping(value = "get/stock-out-data/date-range")
    public void runDailyStockOutReportForDateRange(@RequestParam(name = "startDate") String startDate, @RequestParam(name = "endDate") String endDate){
        for(Date date : AppUtils.getDaysWithoutTimeBetweenDates(AppUtils.getDate(startDate + " 00:00:00.0", "yyyy-MM-dd HH:mm:ss.SSS"),
                AppUtils.getDate(endDate + " 23:59:59.0", "yyyy-MM-dd HH:mm:ss.SSS"), false)){
            LOG.info("Running daily stock out report for Date {}", JSONSerializer.toJSON(date));
            dailyStockOutReport(date);
            LOG.info("Done - Running daily stock out report for Date {}", JSONSerializer.toJSON(date));
        }
    }



    @RequestMapping(value = "save-scm-stock-out-data")
    public void runDailyStockOutReportForDateRange(@RequestParam(name = "startDate") String startDate, @RequestParam(name = "endDate") String endDate,
                                                   @RequestParam(name="brandId") Integer brandId ,
                                                   @RequestParam(name = "unitId",required = false) Integer unitId){
        for(Date date : AppUtils.getDaysWithoutTimeBetweenDates(AppUtils.getDate(startDate + " 00:00:00.0", "yyyy-MM-dd HH:mm:ss.SSS"),
                AppUtils.getDate(endDate + " 23:59:59.0", "yyyy-MM-dd HH:mm:ss.SSS"), false)){
            LOG.info("Running save scm stock out  for Date {}", JSONSerializer.toJSON(date));
            if(Objects.nonNull(unitId)){
                reportService.saveScmStockOutData(unitId,date,brandId,AppUtils.getGntOpeningTime(date), AppUtils.getGntClosingTime(date));
            }else{
                for (UnitBasicDetail unit : masterCache.getAllUnits()) {
                    if (TransactionUtils.isActiveUnit(unit.getStatus()) && unit.isLive()
                            && UnitCategory.CAFE.equals(unit.getCategory())){
                        if(brandId ==3){
                            reportService.saveScmStockOutData(unit.getId(),date,brandId,AppUtils.getGntOpeningTime(date), AppUtils.getGntClosingTime(date));
                        }else if(brandId ==1){
                            reportService.saveScmStockOutData(unit.getId(),date,brandId,AppUtils.getChaayosOpeningTime(date), AppUtils.getChaayosClosingTime(date));
                        }
                    }
                }
            }

            LOG.info("Done - save scm stock out for Date {}", JSONSerializer.toJSON(date));
        }
    }


    @Scheduled(cron = "0 10 8 * * *", zone = "GMT+05:30")
    public void dailyStockOutReportScheduled(){
        dailyStockOutReport(AppUtils.getPreviousDate());
    }

    public void dailyStockOutReport(Date date) {
        Date runningForDate = Objects.nonNull(date) ? date : AppUtils.getPreviousDate();
        Map<Integer, UnitTimeStockData> unitTimeMap= reportService.getUnitClosingTimeMap(runningForDate);
        Map<Integer, Map<Integer, Map<String, String>>> lpse = reportService.getLatestPreviousStockEvent(runningForDate);
        if(unitTimeMap==null || unitTimeMap.isEmpty()){
            return;
        }
        List<Date> partnerHardCodedTimeChaayos = new ArrayList<>();
        if(Objects.isNull(date)){
            partnerHardCodedTimeChaayos.add(AppUtils.addMinutesToDate(AppUtils.getPreviousBusinessDate(), 480));
            partnerHardCodedTimeChaayos.add(AppUtils.addMinutesToDate(AppUtils.getPreviousBusinessDate(), 1320));
        }else{
            partnerHardCodedTimeChaayos.add(AppUtils.addMinutesToDate(AppUtils.getDate(date), 480));
            partnerHardCodedTimeChaayos.add(AppUtils.addMinutesToDate(AppUtils.getDate(date), 1320));
        }

        List<Date> partnerHardCodedTimeGnT = new ArrayList<>();
        if(Objects.isNull(date)){
            partnerHardCodedTimeGnT.add(AppUtils.addMinutesToDate(AppUtils.getPreviousBusinessDate(), 660));
            partnerHardCodedTimeGnT.add(AppUtils.addMinutesToDate(AppUtils.getPreviousBusinessDate(), 1439));
        }else{
            partnerHardCodedTimeGnT.add(AppUtils.addMinutesToDate(AppUtils.getDate(date), 660));
            partnerHardCodedTimeGnT.add(AppUtils.addMinutesToDate(AppUtils.getDate(date), 1439));
        }
        for (UnitBasicDetail unit : masterCache.getAllUnits()) {
            try {
                if (TransactionUtils.isActiveUnit(unit.getStatus()) && unit.isLive()
                        && UnitCategory.CAFE.equals(unit.getCategory())
                        && unitTimeMap.get(unit.getId()) != null) {
                    LOG.info("Creating Report For Unit {}",unit.getId());
                    Map<Integer, Map<String, String>> pds= new HashMap<>();
                    if(lpse.containsKey(unit.getId())){
                        pds = lpse.get(unit.getId());
                    }
                    reportService.execute(runningForDate, unit.getId(), true, unitTimeMap.get(unit.getId()).getDineInTime(), false, AppConstants.CHAAYOS_BRAND_ID, unitTimeMap.get(unit.getId()), pds);
                    reportService.execute(runningForDate, unit.getId(), true, unitTimeMap.get(unit.getId()).getDeliveryTime(), false, AppConstants.GNT_BRAND_ID, unitTimeMap.get(unit.getId()), pds);
                    reportService.execute(runningForDate, unit.getId(), true, unitTimeMap.get(unit.getId()).getDeliveryTime(), false, AppConstants.DOHFUL_BRAND_ID, unitTimeMap.get(unit.getId()), pds);

                    reportService.execute(runningForDate, unit.getId(), true, unitTimeMap.get(unit.getId()).getDeliveryTime(), true, AppConstants.CHAAYOS_BRAND_ID, unitTimeMap.get(unit.getId()), pds);
                    reportService.execute(runningForDate, unit.getId(), true, unitTimeMap.get(unit.getId()).getDeliveryTime(), true, AppConstants.GNT_BRAND_ID, unitTimeMap.get(unit.getId()), pds);
                    reportService.execute(runningForDate, unit.getId(), true, unitTimeMap.get(unit.getId()).getDeliveryTime(), true, AppConstants.DOHFUL_BRAND_ID, unitTimeMap.get(unit.getId()), pds);


                }
            }catch (Exception e){
                LOG.error("Exception Caught While Generating the Report for Unit {}",unit.getId(),e);
            }
        }
    }
}
