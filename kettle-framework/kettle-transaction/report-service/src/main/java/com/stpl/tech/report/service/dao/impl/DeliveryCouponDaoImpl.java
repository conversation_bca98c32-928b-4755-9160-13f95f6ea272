package com.stpl.tech.report.service.dao.impl;

import com.stpl.tech.master.data.dao.impl.AbstractMasterDaoImpl;
import com.stpl.tech.master.data.model.DeliveryCouponDetailData;
import com.stpl.tech.report.service.dao.DeliveryCouponDao;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.List;

@Slf4j
@Repository
public class DeliveryCouponDaoImpl extends AbstractMasterDaoImpl implements DeliveryCouponDao {


    @Override
    public List<DeliveryCouponDetailData> getDeilveryCouponByCampaignStrategyAndIsExhustedAndIsRedeemed(String strategy, String isExhusted, String isRedeemed) {
        try {
            Long startTime = System.currentTimeMillis();
            Query query = manager.createQuery("from DeliveryCouponDetailData data where data.couponStrategy = :strategy AND data.isExhausted = :isExhausted " +
                    "AND data.isRedeemed is null");
            query.setParameter("strategy", strategy);
            query.setParameter("isExhausted", isExhusted);
//            query.setParameter("isRedeemed", isRedeemed);
            List<DeliveryCouponDetailData> list = query.getResultList();
            log.info("Fetching Delivery Coupon Details took : {}", System.currentTimeMillis() - startTime);
            return list;
        }
        catch (Exception e){
            log.info("Error in fetching Delivery Coupon : {}",e);
            return null;
        }
    }

    @Override
    public List<Integer> getCustomerIdsFromDeliveryAllocation(List<Integer> couponIds){
        try {
            Long startTime = System.currentTimeMillis();
            Query query = manager.createQuery("select data.customerId from DeliveryCouponAllocationDetailData data where data.deliveryCouponId in (:couponIds)");
            query.setParameter("couponIds",couponIds);
            List<Integer> customerIds = query.getResultList();
            log.info("Fetching customerId took : {}", System.currentTimeMillis() - startTime);
            return customerIds;
        }catch (Exception e){
            log.info("Error in fetching CustomerIds from deliveryAllocationDetailData : {}",e);
            return null;
        }
    }
}
