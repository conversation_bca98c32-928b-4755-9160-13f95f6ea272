package com.stpl.tech.report.service.service.impl;

import com.stpl.tech.kettle.clevertap.service.CleverTapDataPushService;
import com.stpl.tech.kettle.clevertap.util.CleverTapConstants;
import com.stpl.tech.kettle.clevertap.util.CleverTapEvents;
import com.stpl.tech.kettle.customer.dao.CustomerInfoDao;
import com.stpl.tech.kettle.customer.dao.LoyaltyDao;
import com.stpl.tech.kettle.customer.service.CustomerService;
import com.stpl.tech.kettle.data.model.CafeRaiseRequestApproval;
import com.stpl.tech.kettle.data.model.CafeRaiseRequestApprovalVO;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.LoyaltyScore;
import com.stpl.tech.kettle.data.mongo.dao.CustomerDataDao;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.offer.dao.OfferManagementDao;
import com.stpl.tech.master.data.model.CustomerEventDetailData;
import com.stpl.tech.master.domain.model.CouponCloneResponse;
import com.stpl.tech.master.util.CustomerEventType;
import com.stpl.tech.master.data.model.DeliveryCouponDetailData;
import com.stpl.tech.master.data.model.RedeemedCouponDetailData;
import com.stpl.tech.kettle.customer.dao.CustomerBirthdayDetailDao;
import com.stpl.tech.report.service.dao.DeliveryCouponDao;
import com.stpl.tech.report.service.dao.RedeemedCouponDetailDataDao;
import com.stpl.tech.report.service.dao.UnitReportDao;
import com.stpl.tech.report.service.model.DeliveryCouponResponse;
import com.stpl.tech.report.service.service.CouponService;
import com.stpl.tech.report.service.service.UnitReportService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.excelparser.ExcelWriter;
import com.stpl.tech.util.excelparser.SheetParser;
import com.stpl.tech.util.excelparser.exception.ExcelParsingException;
import lombok.extern.slf4j.Slf4j;
import net.bull.javamelody.internal.common.LOG;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;
import org.springframework.web.servlet.view.document.AbstractXlsxView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

@Service
@Slf4j
public class UnitReportServiceImpl implements UnitReportService {
    @Autowired
    private UnitReportDao unitReportServiceDao;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private LoyaltyDao loyaltyDao;

    @Autowired
    private CustomerService customerService;

    @Autowired
    private CouponService couponService;

    @Autowired
    private CleverTapDataPushService cleverTapDataPushService;

    @Autowired
    private ApplicationContext applicationContext;

    @Autowired
    private RedeemedCouponDetailDataDao redeemedCouponDetailDataDao;

    @Autowired
    private DeliveryCouponDao deliveryCouponDao;
    @Autowired
    private CustomerBirthdayDetailDao customerBirthdayDetailDao;

    private static final String COUPON_STRATEGY = "WHATSAPP_BOT";

    private static final String BIRTHDAY_FREE_DESI_CHAI = "Free Desi Chai";

    private static final String BIRTHDAY_DISCOUNT = "20% off on MoV 999";

    private static final Long BUFFER_TIME = 10L;


    @Override
    @Transactional(rollbackFor = Exception.class,value = "MasterDataSourceTM", readOnly = true,propagation = Propagation.REQUIRED)
    public View getReportForUnitClosure(Integer unitId, Integer noOfDays) {

        return new AbstractXlsxView() {
            @Override
            protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest request,
                                              HttpServletResponse response) throws Exception {
                String fileName = "UnitClosureReport.xlsx";
                response.addHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                ExcelWriter writer = new ExcelWriter(workbook);
                List<CafeRaiseRequestApproval> unitClosure = unitReportServiceDao.getReportForUnitClosure(unitId, noOfDays);

                List<CafeRaiseRequestApprovalVO> unitClosureList =
                        unitClosure.stream().map(val -> CafeRaiseRequestApprovalVO.builder().unitId(val.getUnitId())
                                .cafeName(val.getCafeName()).
                                partnerName(val.getPartnerName()).reason(val.getReason())
                                .businessDate(val.getBusinessDate())
                                .actionTimestamp(val.getActionTimestamp()).actionTaken(val.getActionTaken())
                                .brandId(val.getBrandId())
                                .brandName(masterDataCache.getBrandMetaData().get(Objects.nonNull(val.getBrandId()) ? val.getBrandId() : "").getBrandName())
                                .commentReason(val.getCommentReason())
                                .autoSwitch(val.getAutoSwitch()).requestedByEmpId(val.getRequestedByEmpId()).
                                requestedByEmpName(
                                        Objects.nonNull(val.getRequestedByEmpId()) ?
                                        masterDataCache.getEmployeeBasicDetail(val.getRequestedByEmpId()).getName()
                                                : null)
                                .acceptedByEmpId(val.getAcceptedByEmpId())
                                .acceptedByEmpName(Objects.nonNull(val.getAcceptedByEmpId()) ?
                                        masterDataCache.getEmployeeBasicDetail(val.getAcceptedByEmpId()).getName()
                                        : null).build()).collect(Collectors.toList());
                writer.writeSheet(unitClosureList, CafeRaiseRequestApprovalVO.class);
            }
        };
    }

    public DeliveryCouponResponse createDeliveryCoupon(String contactNumber, Integer channelPartnerId, String code, Integer loyalityPoints, Integer getValidityInDays){
        DeliveryCouponResponse result = new DeliveryCouponResponse();
        contactNumber = AppUtils.getValidContactNUmber(contactNumber);
        try {
            Integer customerId = customerService.getCustomerId(contactNumber);
            LoyaltyScore loyaltyScore = loyaltyDao.getScore(customerId);
            Integer score = loyaltyScore.getAcquiredPoints();
            boolean isEligible = couponService.validateLoyaltyPointForDelivery(loyalityPoints, loyaltyScore);
            if (isEligible) {
                result = couponService.createDeliveryCouponLoyaltyBurn(code,1,channelPartnerId,true,customerId,contactNumber,getValidityInDays);
                if(Objects.nonNull(result.getCouponCode())) {
                    try {
                        boolean update = couponService.updateLoyaltyForDelivery(loyalityPoints, customerId, contactNumber, result,
                                loyaltyScore);

                        if (update) {
                            Map<String, Object> data = new HashMap<>();
                            Map<String,Object> userProfiles = new HashMap<>();
                            data.put("CustomerId", customerId);
                            data.put("RedeemedPoints", loyalityPoints);
                            data.put("CouponCode", result.getCouponCode());
                            data.put("ValidTill", result.getEndDate());
                            data.put("LoyaltyPointInitial", score);
                            data.put("LoyaltyPointFinal", loyaltyScore.getAcquiredPoints());
                            data.put("ContactNumber", "+91" + contactNumber);
                            if (loyalityPoints == 40) {
                                userProfiles.put("LoyaltyBurnOfferText","FLAT Rs.90 OFF");
                                data.put("OfferText", "FLAT Rs.90 OFF");
                            } else if (loyalityPoints == 60) {
                                userProfiles.put("LoyaltyBurnOfferText","FLAT Rs.130 OFF");
                                data.put("OfferText", "FLAT Rs.130 OFF");
                            }
                            if(channelPartnerId==3){
                                userProfiles.put("LoyaltyBurnOfferForPartner",AppConstants.ZOMATO);
                                data.put("OfferForPartner", AppConstants.ZOMATO);
                                if (loyalityPoints == 40) {
                                    userProfiles.put("LoyaltyBurnMov", "180");
                                    data.put("MOV", "180");
                                } else if (loyalityPoints == 60) {
                                    userProfiles.put("LoyaltyBurnMov","200");
                                    data.put("MOV", "200");
                                }
                            } else if (channelPartnerId==6) {
                                userProfiles.put("LoyaltyBurnOfferForPartner",AppConstants.SWIGGY);
                                data.put("OfferForPartner",AppConstants.SWIGGY);
                                userProfiles.put("LoyaltyBurnMov", "200");
                                data.put("MOV", "200");
                            }
                            userProfiles.put("LoyaltyPointsBalance",loyaltyScore.getAcquiredPoints());
                            Integer teaAvailable = loyaltyScore.getAcquiredPoints()/60;
                            userProfiles.put("LoyalteaAvailable",teaAvailable);
                            userProfiles.put("LastUpdatedTime","$D_"+AppUtils.getCurrentTimestamp().getTime());
                            userProfiles.put("LoyaltyBurnCouponCode",result.getCouponCode());
                            userProfiles.put("LoyaltyBurnCouponValidity",result.getEndDate());
                            userProfiles.put("LoyaltyBurnRedeemedPoints",loyalityPoints);
                            userProfiles.put("IsLoyaltyCouponRedeemed",AppConstants.NO);
                            ThreadPoolTaskExecutor executor = (ThreadPoolTaskExecutor) applicationContext.getBean("taskExecutor");
                            executor.execute(() -> {
                                try {
                                    cleverTapDataPushService.uploadProfileAttributes(customerId,AppUtils.getCurrentTimestamp().toInstant().getEpochSecond()
                                            ,CleverTapConstants.REGULAR,userProfiles);
                                    cleverTapDataPushService.publishCustomEvent(customerId, CleverTapEvents.LOYALTY_BURN,
                                            AppUtils.getCurrentTimestamp().toInstant().getEpochSecond(), CleverTapConstants.REGULAR, data);
                                } catch (Exception e) {
                                    log.info("Error in pushing data to cleverTap with exception : {}", e);
                                }
                            });
                            return result;
                        }
                    } catch (Exception e) {
                        log.info("Unable to update Loyalty for customer with contact Number  ; {}", contactNumber);
                    }
                }else{
                    result.setCouponCode(null);
                    return result;
                }
            } else {
                log.info("Coupon is not Applicable for Delivery Coupon Loyalty Burn : {}", contactNumber);
            }
        }
        catch (Exception e){
            log.info("Customer is not a Chaayos Customer");
        }
        result.setCouponCode(null);
        return result;
    }
    @Override
    public String getDeliveryCouponCode(String contactNumber){
        contactNumber = AppUtils.getValidContactNUmber(contactNumber);
        return couponService.getDeliveryCouponCode(contactNumber);
    }



    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public View getRedeemedCouponSheet(){
        return new AbstractXlsxView(){
            @Override
            protected void buildExcelDocument(Map<String, Object> model, Workbook workbook, HttpServletRequest request,
                                              HttpServletResponse response) throws Exception {
                String fileName = "RedeemedCouponSheet.xlsx";
                response.addHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                ExcelWriter writer = new ExcelWriter(workbook);
                List<RedeemedCouponDetailData> all = new ArrayList<RedeemedCouponDetailData>();
                writer.writeSheet(all, RedeemedCouponDetailData.class);
            }
        };
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void uploadRedeemedCouponSheet(HttpServletResponse response, MultipartFile file,Integer updateBy) throws Exception {
        log.info("Request to upload Redeemed Coupon Sheet");
        List<String> couponList = new ArrayList<>();
        List<RedeemedCouponDetailData> redeemedCouponDetailDataList = new ArrayList<>();
        List<DeliveryCouponDetailData> deliveryCouponlist = getDeliveryCouponList();
        Set<Integer> redeemedCouponIdSet = new HashSet<>();
        Map<String,Integer> deliveryCouponMap = new HashMap<>();
        if(Objects.nonNull(deliveryCouponlist)) {
            for (DeliveryCouponDetailData data : deliveryCouponlist) {
                deliveryCouponMap.put(data.getCouponCode(), data.getDeliveryCouponId());
            }
        }
        try {
            Workbook workbook;
            if (file.getName().endsWith("xls")) {
                workbook = new HSSFWorkbook(file.getInputStream());
            } else {
                workbook = new XSSFWorkbook(file.getInputStream());
            }
            List<ExcelParsingException> errors = new ArrayList<>();
            SheetParser parser = new SheetParser();
            List<RedeemedCouponDetailData> entityList = parser.createEntity(workbook.getSheetAt(0), RedeemedCouponDetailData.class, errors::add);
            if (errors.isEmpty()) {
                for(RedeemedCouponDetailData data : entityList){
                    try {
                        if (Objects.nonNull(data) && Objects.nonNull(data.getCouponCode()) && !data.getCouponCode().isEmpty() &&
                                                                   !data.getCouponCode().trim().isEmpty()) {
                            data.setUpdatedBy(updateBy);
                            data.setUpdatedAt(AppUtils.getCurrentTimestamp());
                            redeemedCouponDetailDataList.add(data);
                            if(deliveryCouponMap.containsKey(data.getCouponCode())){
                                redeemedCouponIdSet.add(deliveryCouponMap.get(data.getCouponCode()));
                            }
                        }
                    }catch (Exception e){
                        log.info("Error in this coupon");
                    }
                }
                ThreadPoolTaskExecutor executor = (ThreadPoolTaskExecutor) applicationContext.getBean("taskExecutor");
                executor.execute(() -> {
                    try {
                        processAndPushDataToCleverTap(redeemedCouponIdSet);
                    } catch (Exception e) {
                        log.info("Error in pushing data to cleverTap with exception : {}", e);
                    }
                });
                saveAllData(redeemedCouponDetailDataList,deliveryCouponlist,redeemedCouponIdSet);
            } else {
                log.info("Error parsing Workbook for Redeemed Coupon Sheet, total errors: {}", errors.size());
                StringBuilder sb = new StringBuilder();
                errors.forEach(e -> sb.append(e.getMessage() + '\n'));
                log.info("{}", sb.toString());
                throw new ExcelParsingException(sb.toString());
            }
            workbook.close();
        } catch (ExcelParsingException e1) {
            throw e1;
        } catch (Exception e) {
            log.error("Error while uploading Delivery coupon sheet", e);
            throw new Exception("Error in uploading Delivery coupons");
        }
    }

    private  List<DeliveryCouponDetailData> getDeliveryCouponList(){
        return  deliveryCouponDao.getDeilveryCouponByCampaignStrategyAndIsExhustedAndIsRedeemed(COUPON_STRATEGY, AppConstants.IS_EXHAUSTED, AppConstants.IS_REDEEMED);
    }

    private void processAndPushDataToCleverTap(Set<Integer> couponIds){
        List<Integer> couponIdsList = new ArrayList<>();
        for(Integer id : couponIds){
            couponIdsList.add(id);
        }
        List<Integer> customerIds = deliveryCouponDao.getCustomerIdsFromDeliveryAllocation(couponIdsList);
        if(Objects.nonNull(customerIds) && !customerIds.isEmpty()){
            for(Integer customerId : customerIds) {
                Map<String, Object> userProfiles = new HashMap<>();
                userProfiles.put("IsLoyaltyCouponRedeemed", AppConstants.YES);
                try{
                    cleverTapDataPushService.uploadProfileAttributes(customerId,AppUtils.getCurrentTimestamp().toInstant().getEpochSecond()
                            ,CleverTapConstants.REGULAR,userProfiles);
                }catch (Exception e){
                    log.info("Error in Pushing Event to clever tap");
                }
            }
        }
        else{
            log.info("No Customer Mapped with given Coupons");
        }

    }
    public void saveAllData(List<RedeemedCouponDetailData> redeemedCouponDetailDataList,List<DeliveryCouponDetailData> deliveryCouponDetailDataList,
                             Set<Integer> redeemdedCouponIds){
        try{
            int batchSize = 10000;
            List<RedeemedCouponDetailData> batchList = new ArrayList<>();
            for(int i=0;i<redeemedCouponDetailDataList.size();i++){
                if(i>0 && i%batchSize==0){
                    redeemedCouponDetailDataDao.addAll(batchList);
                    batchList.clear();
                }
                batchList.add(redeemedCouponDetailDataList.get(i));
            }
            if(!batchList.isEmpty()) {
                redeemedCouponDetailDataDao.addAll(batchList);
            }
        }catch (Exception e){
            log.info("Error in Adding reedemed Coupons");
        }
        try {
            List<DeliveryCouponDetailData> dataToSave = new ArrayList<>();
            for(DeliveryCouponDetailData data : deliveryCouponDetailDataList){
                if(redeemdedCouponIds.contains(data.getDeliveryCouponId())) {
                    data.setIsRedeemed(AppConstants.IS_REDEEMED);
                    dataToSave.add(data);
                }
            }
            deliveryCouponDao.addAll(dataToSave);
        }catch (Exception e){
            log.info("Error in adding Delivery Detail Coupons");
        }
    }

//    @Override
//    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
//    public boolean addBirthdayMonth(String contactNumber, Integer eventMonth, Integer eventDate, String acquisitionSource, CustomerEventType customerEventType, String forceUpdate){
//        contactNumber = AppUtils.getValidContactNUmber(contactNumber);
//        try{
//            Integer customerId = customerService.getCustomerId(contactNumber);
//            if(Objects.nonNull(customerId)){
//                Map<String, Object> userProfiles = new HashMap<>();
//                Map<String, Object> eventData = new HashMap<>();
//                CustomerEventDetailData customerBirthdayDetailData = customerBirthdayDetailDao.findByCustomerIdAndEventType(customerId,customerEventType.toString());
//                if(Objects.isNull(customerBirthdayDetailData) || AppConstants.YES.equals(forceUpdate)) {
//                    if(Objects.isNull(customerBirthdayDetailData)){
//                        CustomerEventDetailData data = CustomerEventDetailData.builder()
//                                .customerId(customerId)
//                                .contactNumber(contactNumber)
//                                .acquisitionSource(acquisitionSource)
//                                .customerEventType(customerEventType)
//                                .eventMonth(eventMonth)
//                                .eventDate(eventDate)
//                                .createdAt(AppUtils.getCurrentTimestamp())
//                                .build();
//                        customerBirthdayDetailData = customerBirthdayDetailDao.add(data);
//                    }else if(customerBirthdayDetailData.getAcquisitionSource().equals(acquisitionSource) || acquisitionSource.equals(AppConstants.WHATSAPP)){
//                        customerBirthdayDetailData.setEventMonth(eventMonth);
//                        customerBirthdayDetailData.setCustomerEventType(customerEventType);
//                        customerBirthdayDetailData.setEventDate(eventDate);
//                        customerBirthdayDetailData.setUpdatedAt(AppUtils.getCurrentTimestamp());
//                        customerBirthdayDetailData = customerBirthdayDetailDao.update(customerBirthdayDetailData);
//                    }
//
//                    userProfiles.put("Customer"+customerEventType.toString()+"Month", eventMonth);
//                    userProfiles.put("Customer"+customerEventType.toString()+"Date",eventDate);
//
//                    if(eventMonth == AppUtils.getCurrentMonth()){
//                        // todo generate coupon code according to event
//                        ArrayList<Integer> offerIds = customerEventType.getOfferIds();
//                        String cloneCoupon1 = offerDao.getCouponByOfferId(offerIds.get(0));
//                        String cloneCoupon2 = offerDao.getCouponByOfferId(offerIds.get(1));
//
//                        Integer validityInDays = AppUtils.getDaysInMonth(eventMonth);
//                        validityInDays = validityInDays - AppUtils.getCurrentDayofMonth() + 1;
//
//                        // todo generate coupon code according to event
//                        String coupon1 = couponService.generateCoupon(validityInDays,contactNumber,cloneCoupon1,"BTH").getMappings().get(contactNumber).getCoupon();
//                        String coupon2 = couponService.generateCoupon(validityInDays,contactNumber,cloneCoupon2,"BTH").getMappings().get(contactNumber).getCoupon();
//
//
//                        userProfiles.put("Customer"+customerEventType.toString()+"Offer1", coupon1);
//                        userProfiles.put("Customer"+customerEventType.toString()+"Offer1text",BIRTHDAY_FREE_DESI_CHAI);
//                        userProfiles.put("Customer"+customerEventType.toString()+"Offer2",coupon2);
//                        userProfiles.put("Customer"+customerEventType.toString()+"Offer2text",BIRTHDAY_DISCOUNT);
//
//                        eventData.put("Customer"+customerEventType.toString()+"Month", eventMonth);
//                        eventData.put("Customer"+customerEventType.toString()+"Date",eventDate);
//                        eventData.put("Customer"+customerEventType.toString()+"Offer1", coupon1);
//                        eventData.put("Customer"+customerEventType.toString()+"Offer1text",BIRTHDAY_FREE_DESI_CHAI);
//                        eventData.put("Customer"+customerEventType.toString()+"Offer2",coupon2);
//                        eventData.put("Customer"+customerEventType.toString()+"Offer2text",BIRTHDAY_DISCOUNT);
//
//
//                        customerBirthdayDetailData.setCouponGenerationAt(AppUtils.getCurrentTimestamp());
//                        customerBirthdayDetailData.setEventOffer1(coupon1);
//                        customerBirthdayDetailData.setEventOffer2(coupon2);
//                        customerBirthdayDetailDao.update(customerBirthdayDetailData);
//                    }
//                    try{
//                        cleverTapDataPushService.uploadProfileAttributes(customerId,AppUtils.getCurrentTimestamp().toInstant().getEpochSecond()
//                            ,CleverTapConstants.REGULAR,userProfiles);
//                    }catch (Exception e){
//                        log.info("Error in Pushing UserProfile Attribute to clever tap");
//                    }
//                    try{
//                        if(eventMonth == AppUtils.getCurrentMonth()) {
//                            if(customerEventType.equals(CustomerEventType.Birthday)){
//                                cleverTapDataPushService.publishCustomEvent(customerId, CleverTapEvents.BIRTHDAY_MONTH,
//                                        AppUtils.getCurrentTimestamp().toInstant().getEpochSecond()+BUFFER_TIME, CleverTapConstants.REGULAR, eventData);
//
//                            }else if(customerEventType.equals(CustomerEventType.Anniversary)){
//                                cleverTapDataPushService.publishCustomEvent(customerId, CleverTapEvents.ANNIVERSARY_MONTH,
//                                        AppUtils.getCurrentTimestamp().toInstant().getEpochSecond()+BUFFER_TIME, CleverTapConstants.REGULAR, eventData);
//                            }
//                            else{
//                                cleverTapDataPushService.publishCustomEvent(customerId, customerEventType.toString().concat("_Month"),
//                                        AppUtils.getCurrentTimestamp().toInstant().getEpochSecond()+BUFFER_TIME, CleverTapConstants.REGULAR, eventData);
//
//                            }
//                       }
//                    }catch (Exception e){
//                        log.info("Error in Pushing Event to clever tap");
//                    }
//                    return true;
//                }
//                else{
//                    log.info("Customer {} month Already Exist for customerId : {}",customerEventType, customerId);
//                }
//            }
//            else{
//                log.info("Not a Chaayos Customer");
//            }
//        }catch (Exception e){
//            log.info("Error in add Birthady Month : {}",e);
//        }
//        return false;
//    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public  boolean generateCouponForCustomerEventMonth(){
        return couponService.generateCouponsForCustomerEventMonth();
    }
    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public Integer getEventMonth(String contactNumber,Integer customerId,CustomerEventType customerEventType){
        Integer result = null;
        if(Objects.isNull(customerId) && Objects.nonNull(contactNumber)){
            customerId = customerService.getCustomerId(contactNumber);
        }
        if(Objects.nonNull(customerId)) {
            CustomerEventDetailData data = customerBirthdayDetailDao.findByCustomerIdAndEventType(customerId,customerEventType);
            if(Objects.nonNull(data)) {
                result = data.getEventMonth();
            }
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public String getCustomerEventDetails(String contactNumber,Integer customerId,CustomerEventType customerEventType){
        String result = "";
        if(Objects.isNull(customerId) && Objects.nonNull(contactNumber)){
            customerId = customerService.getCustomerId(contactNumber);
        }
        if(Objects.nonNull(customerId)) {
            CustomerEventDetailData data = customerBirthdayDetailDao.findByCustomerIdAndEventType(customerId,customerEventType);
            if(Objects.nonNull(data)) {
                if(Objects.nonNull(data.getEventDate())){
                    result = data.getEventDate().toString() + "/";
                }
                result = result + data.getEventMonth();
            }
        }
        return result;
    }

    @Override
    public void acknowledgeLoyaltyEvent(String contactNumber,Integer loyaltyPoints){
        contactNumber = AppUtils.getValidContactNUmber(contactNumber);
        Integer customerId = customerService.getCustomerId(contactNumber);
        Map<String, Object> data = new HashMap<>();
        data.put("IsAcknowleded","Y");
        ThreadPoolTaskExecutor executor = (ThreadPoolTaskExecutor) applicationContext.getBean("taskExecutor");
        executor.execute(() -> {
            try {
                if(loyaltyPoints == 30){
                    cleverTapDataPushService.publishCustomEvent(customerId, CleverTapEvents.COLD_30,
                            AppUtils.getCurrentTimestamp().toInstant().getEpochSecond(), CleverTapConstants.REGULAR, data);
                } else if (loyaltyPoints == 60) {
                    cleverTapDataPushService.publishCustomEvent(customerId,  CleverTapEvents.COLD_60,
                            AppUtils.getCurrentTimestamp().toInstant().getEpochSecond(), CleverTapConstants.REGULAR, data);
                }
            } catch (Exception e) {
                log.info("Error in pushing data to cleverTap with exception : {}", e);
            }
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean syncCustomerEventDetailData() {
        List<CustomerInfo> customerInfos = customerService.getCustomersWithDOBorAnniversary();
        try {
            List<CustomerEventDetailData> customerEventDetailDataList = new ArrayList<>();
            for (CustomerInfo customerInfo : customerInfos) {
                if (Objects.nonNull(customerInfo.getDateOfBirth())) {
                    CustomerEventDetailData customerBirthdayDetailData = customerBirthdayDetailDao.findByCustomerIdAndEventType(customerInfo.getCustomerId(), CustomerEventType.Birthday);
                    if (Objects.isNull(customerBirthdayDetailData)) {
                        CustomerEventDetailData data = CustomerEventDetailData.builder()
                                .customerId(customerInfo.getCustomerId())
                                .contactNumber(customerInfo.getContactNumber())
                                .acquisitionSource(AppConstants.APP)
                                .customerEventType(CustomerEventType.Birthday)
                                .eventMonth(AppUtils.getMonth(customerInfo.getDateOfBirth()))
                                .eventDate(AppUtils.getDay(customerInfo.getDateOfBirth()))
                                .createdAt(AppUtils.getCurrentTimestamp())
                                .build();
                        customerEventDetailDataList.add(data);
                    }
                }
                if (Objects.nonNull(customerInfo.getAnniversary())) {
                    CustomerEventDetailData customerBirthdayDetailData = customerBirthdayDetailDao.findByCustomerIdAndEventType(customerInfo.getCustomerId(), CustomerEventType.Anniversary);
                    if (Objects.isNull(customerBirthdayDetailData)) {
                        CustomerEventDetailData data = CustomerEventDetailData.builder()
                                .customerId(customerInfo.getCustomerId())
                                .contactNumber(customerInfo.getContactNumber())
                                .acquisitionSource(AppConstants.APP)
                                .customerEventType(CustomerEventType.Anniversary)
                                .eventMonth(AppUtils.getMonth(customerInfo.getAnniversary()))
                                .eventDate(AppUtils.getDay(customerInfo.getAnniversary()))
                                .createdAt(AppUtils.getCurrentTimestamp())
                                .build();
                        customerEventDetailDataList.add(data);
                    }
                }
            }
            customerBirthdayDetailDao.addAll(customerEventDetailDataList);
        }
        catch (Exception e){
            LOG.info("Error in Syncing Customer Event Data",e);
            return false;
        }
        return true;
    }

    @Override
    public CouponCloneResponse generateCouponForCustomerThroughCampaign(String contactNumber, String masterCode, String acqSrc, String acqToken,
                                                                        Integer validityInDays,String prefix){
        CouponCloneResponse response = new CouponCloneResponse();
        contactNumber = AppUtils.getValidContactNUmber(contactNumber);
        try {
            Integer customerId = customerService.getCustomerId(contactNumber);
            if(Objects.isNull(customerId)) {
                customerId = createNewCustomer(contactNumber,acqSrc,acqToken,masterCode);
            }
            if (Objects.nonNull(customerId)) {
                Integer finalCustomerId = customerId;
                response = couponService.generateCouponForCampaign(validityInDays, contactNumber, masterCode, prefix,acqSrc);
                if(Objects.nonNull(response) && Objects.nonNull(response.getMappings()) && !response.getMappings().isEmpty()){
                    couponService.pushEventToCleverTap(response,CleverTapEvents.CAMPAIGN_LEAD,contactNumber,finalCustomerId);
                    return response;
                }
            } else {
                log.info("unable to create customer with contact Number : {}",contactNumber);
            }
        }
        catch (Exception e){
            log.info("Error iin generating coupon for customer with contact number : {} and error is : {}",contactNumber,e);
        }
        List<String> error = new ArrayList<>();
        error.add("Either unable to create customer or error in coupon already exist with contact number");
        response.setErrors(error);
        return response;
    }

    public Integer createNewCustomer(String contactNumber,String acqSrc,String acqToken,String masterCode){
        try {
            if(StringUtils.isEmpty(acqSrc)){
                acqSrc = AppConstants.CAMPAIGN;
            }
            if(StringUtils.isEmpty(acqToken)){
                acqToken = masterCode;
            }
            Customer customer = new Customer();
            customer.setContactNumber(contactNumber);
            customer.setAcquisitionSource(acqSrc);
            customer.setAcquisitionToken(acqToken);
            CustomerInfo info = customerService.addCustomerFromCampaign(customer);
            Integer customerId = info.getCustomerId();
            if(Objects.nonNull(customerId)) {
                try {
                    cleverTapDataPushService.pushUserToCleverTap(customerId, true);
                } catch (Exception e) {
                    log.info("Error in pushing data to clevertap for new customer : {}", e);
                }
                return customerId;
            }
        }catch (Exception e){
            log.info("Error in adding new Customer for contact number : {}",contactNumber);
        }
        return null;
    }

    @Override
    public CouponCloneResponse generateCouponForCashbackCampaign(String contactNumber, String masterCode, String acqSrc, String acqToken,
                                                                 Integer validityInDays){
        CouponCloneResponse response = new CouponCloneResponse();
        contactNumber = AppUtils.getValidContactNUmber(contactNumber);
        try {
            Integer customerId = customerService.getCustomerId(contactNumber);
            if(Objects.isNull(customerId)) {
                customerId = createNewCustomer(contactNumber,acqSrc,acqToken,masterCode);
            }
            if (Objects.nonNull(customerId)) {
                response = couponService.mapCouponToCustomer(contactNumber,null,masterCode);
                if(Objects.nonNull(response) && Objects.nonNull(response.getMappings()) && !response.getMappings().isEmpty()){
                    couponService.pushEventToCleverTap(response,CleverTapEvents.CAMPAIGN_LEAD,contactNumber,customerId);
                    return response;
                }else{
                    response = new CouponCloneResponse();
                }
            }else {
                log.info("unable to create customer with contact number : {}",contactNumber);
            }
        }catch (Exception e){
            log.info("Error in generating coupon for cashback campaign for contact number : {} and error is : {}",contactNumber,e);
        }
        List<String> error = new ArrayList<>();
        error.add("Either unable to create customer or error in coupon already exist with contact number");
        response.setErrors(error);
        return response;
    }


}
