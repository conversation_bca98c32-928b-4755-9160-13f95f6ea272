package com.stpl.tech.report.service.service;

import com.stpl.tech.master.domain.model.CouponCloneResponse;
import com.stpl.tech.report.service.model.DeliveryCouponResponse;
import com.stpl.tech.master.util.CustomerEventType;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.View;

import javax.servlet.http.HttpServletResponse;

public interface UnitReportService {

    View getReportForUnitClosure(Integer unitId, Integer noOfDays);

    public DeliveryCouponResponse createDeliveryCoupon(String contactNumber, Integer channelPartnerId, String code, Integer loyalityPoints, Integer getValidityInDays);
    public String getDeliveryCouponCode(String contactNumber);

    public View getRedeemedCouponSheet();

    public void uploadRedeemedCouponSheet(HttpServletResponse response, MultipartFile file,Integer updateBy) throws Exception;
//    public boolean addBirthdayMonth(String contactNumber, Integer eventMonth, Integer eventDate, String acquisitionSource, CustomerEventType customerEventType, String forceUpdate);
    public Integer getEventMonth(String contactNumber,Integer customerId,CustomerEventType customerEventType);
    public String getCustomerEventDetails(String contactNumber,Integer customerId,CustomerEventType customerEventType);
    public void acknowledgeLoyaltyEvent(String contactNumber,Integer loyaltyPoints);
    public  boolean generateCouponForCustomerEventMonth();
    public  boolean syncCustomerEventDetailData();
    public CouponCloneResponse generateCouponForCustomerThroughCampaign(String contactNumber,String masterCode,String acqSrc,String acqToken,
                                                                        Integer validityInDays,String prefix);

    public CouponCloneResponse generateCouponForCashbackCampaign(String contactNumber, String masterCode, String acqSrc, String acqToken,
                                                                 Integer validityInDays);


}
