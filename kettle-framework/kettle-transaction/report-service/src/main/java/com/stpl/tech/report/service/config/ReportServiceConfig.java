/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.report.service.config;

import java.io.File;
import java.io.FileInputStream;
import java.net.URL;
import java.util.TimeZone;

import com.google.auth.oauth2.GoogleCredentials;
import com.google.firebase.FirebaseApp;
import com.google.firebase.FirebaseOptions;
import com.stpl.tech.kettle.core.config.CLMConfig;
import com.stpl.tech.spring.config.MasterSecurityConfiguration;
import java.util.Properties;
import java.util.TimeZone;

import javax.annotation.PostConstruct;
import javax.sql.DataSource;

import org.hibernate.cfg.AvailableSettings;
import com.stpl.tech.master.core.config.ServiceConfig;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.builder.SpringApplicationBuilder;
import org.springframework.boot.web.servlet.support.SpringBootServletInitializer;
import com.stpl.tech.kettle.core.config.TransactionExternalConfig;
import com.stpl.tech.master.core.config.MasterExternalConfig;
import com.stpl.tech.report.service.core.ReportProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.event.ContextStartedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import org.springframework.orm.hibernate5.SpringBeanContainer;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;



@SpringBootApplication
@EnableWebMvc
@Configuration
@EnableScheduling
@ComponentScan({ "com.stpl.tech.report","com.stpl.tech.kettle.clm","com.stpl.tech.spring.crypto","com.stpl.tech.kettle.data.converter","com.stpl.tech.kettle.data.crypto","com.stpl.tech.kettle.stock.*" })
@EnableTransactionManagement
@EnableJpaRepositories(basePackages = {
		"com.stpl.tech.report.service.dao.impl" }, entityManagerFactoryRef = "CLMDataSourceEMFactory", transactionManagerRef = "CLMDataSourceTM")
//@EnableJpaRepositories(basePackages = {"com.stpl.tech.master.core.external.offer.dao.impl"}, entityManagerFactoryRef = "MasterDataSourceEMFactory", transactionManagerRef = "MasterDataSourceTM")
@Import(value = {MasterExternalConfig.class,TransactionExternalConfig.class, MasterSecurityConfiguration.class, CLMConfig.class, ServiceConfig.class})
@EnableMongoRepositories(basePackages = {"com.stpl.tech.kettle.data.mongo.stock.dao"})
@Log4j2
public class ReportServiceConfig extends SpringBootServletInitializer{

	static {
		TimeZone.setDefault(TimeZone.getTimeZone("Asia/Kolkata"));
	}

	public ReportServiceConfig() {
		super();
	}

	public static void main(String[] args) {
		SpringApplication.run(ReportServiceConfig.class, args);
	}

	@Override
	protected SpringApplicationBuilder configure(SpringApplicationBuilder application) {
		return application.sources(ReportServiceConfig.class);
	}

	@Autowired
	private ReportProperties env;

	@PostConstruct
	public void initializeFirebaseApp() {
		log.info("POST CONSTRUCT Report Service FIRESTORE - STARTED");
		FileInputStream serviceAccount = null;
		try {
			try {
				URL resource = getClass().getClassLoader().getResource("firebase-" +
						env.getEnvironmentType().name().toLowerCase()+".json");
				if (resource != null) {
					File file = new File(resource.getFile());
					serviceAccount = new FileInputStream(file);
				} else {
					log.info("No File Found ..!");
					return;
				}
			} catch (Exception e) {
				log.info("Error Occurred while loading resources ..!" , e);
				return;
			}
			FirebaseOptions options = FirebaseOptions.builder()
					.setCredentials(GoogleCredentials.fromStream(serviceAccount))
					.build();
			FirebaseApp.initializeApp(options);
		} catch (Exception e) {
			log.error("File not found exception :",e);
		}
	}

	@Bean(name = "CLMDataSourceEMFactory")
	public LocalContainerEntityManagerFactoryBean clmDataSourceEMFactory() {
		final LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
		em.setDataSource(clmDataSource());
		em.setPackagesToScan("com.stpl.tech.kettle.clm.model");
		final HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
		em.setJpaVendorAdapter(vendorAdapter);
		em.setJpaProperties(masterAdditionalProperties());
		em.setPersistenceUnitName("CLMDataSourcePUName");
		return em;
	}

	@Bean(name = "CLMDataSource")
	public DataSource clmDataSource() {
		final DriverManagerDataSource dataSource = new DriverManagerDataSource();
		dataSource.setDriverClassName(env.getJdbcDriverClassName());
		dataSource.setUrl(env.getUrl());
		dataSource.setUsername(env.getJdbcUserName());
		dataSource.setPassword(env.getJdbcPassword());
		return dataSource;
	}

	@Bean(name = "CLMDataSourceTM")
	public PlatformTransactionManager clmTransactionManager() {
		final JpaTransactionManager transactionManager = new JpaTransactionManager();
		transactionManager.setEntityManagerFactory(clmDataSourceEMFactory().getObject());
		return transactionManager;
	}

	@Bean(name = "RekognitionDataSourceEMFactory")
	public LocalContainerEntityManagerFactoryBean rekognitionDataSourceEMFactory() {
		final LocalContainerEntityManagerFactoryBean em = new LocalContainerEntityManagerFactoryBean();
		em.setDataSource(rekognitionDataSource());
		em.setPackagesToScan("com.stpl.tech.report.service.core.entities.rekognition");
		final HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
		em.setJpaVendorAdapter(vendorAdapter);
		em.setJpaProperties(masterAdditionalProperties());
		em.setPersistenceUnitName("RekognitionDataSourcePUName");
		return em;
	}

	@Bean(name = "RekognitionDataSource")
	public DataSource rekognitionDataSource() {
		final DriverManagerDataSource dataSource = new DriverManagerDataSource();
		dataSource.setDriverClassName(env.getRekognitionJdbcDriverClassName());
		dataSource.setUrl(env.getRekognitionJdbcUrl());
		dataSource.setUsername(env.getRekognitionJdbcUserName());
		dataSource.setPassword(env.getRekognitionJdbcPassword());
		return dataSource;
	}

	@Bean(name = "RekognitionDataSourceTM")
	public PlatformTransactionManager rekognitionTransactionManager() {
		final JpaTransactionManager transactionManager = new JpaTransactionManager();
		transactionManager.setEntityManagerFactory(rekognitionDataSourceEMFactory().getObject());
		return transactionManager;
	}

	final Properties masterAdditionalProperties() {
		final Properties hibernateProperties = new Properties();
		hibernateProperties.setProperty("hibernate.hbm2ddl.auto", env.getHibernateAuto());
		hibernateProperties.setProperty("hibernate.dialect", env.getHibernateDialect());
		hibernateProperties.setProperty("hibernate.show_sql", env.getHibernateSql());
		return hibernateProperties;
	}

	@Bean(name = "CLMDataSourceET")
	public PersistenceExceptionTranslationPostProcessor clmExceptionTranslation() {
		return new PersistenceExceptionTranslationPostProcessor();
	}

	@Bean(name = "taskExecutor")
	public ThreadPoolTaskExecutor getAsyncExecutor() {
		ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
		executor.setCorePoolSize(5);
		executor.setMaxPoolSize(10);
		executor.setThreadNamePrefix("taskExecutor-");
		executor.setWaitForTasksToCompleteOnShutdown(true);
		executor.initialize();
		return executor;
	}

}
