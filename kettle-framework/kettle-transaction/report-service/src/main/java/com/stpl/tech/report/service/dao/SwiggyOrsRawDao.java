package com.stpl.tech.report.service.dao;

import com.stpl.tech.report.service.Entites.KettleAnalytics.SwiggyOrsRaw;
import com.stpl.tech.report.service.Entites.KettleAnalytics.SwiggyRejectionRaw;
import org.springframework.data.jpa.repository.JpaRepository;

import java.util.Date;
import java.util.List;

public interface SwiggyOrsRawDao {
    public List<SwiggyOrsRaw> getSwiggyOrsOrderForBusinessDate(Date businessDate);

    public List<SwiggyRejectionRaw> getSwiggyRejectionOrderForBusinessDate(Date businessDate);

}
