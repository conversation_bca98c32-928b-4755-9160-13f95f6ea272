package com.stpl.tech.report.service.controller;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import javax.jms.JMSException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.stpl.tech.kettle.clevertap.util.CleverTapConstants;
import com.stpl.tech.kettle.customer.service.CustomerService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.model.GamifiedOfferResponse;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.master.core.data.vo.NotificationPayload;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.notification.publisher.CustomerCommunicationEventPublisher;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;

import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping(value = CleverTapConstants.API_VERSION + CleverTapConstants.SEPARATOR + "gmb")
@Slf4j
public class GoogleMyBusinessResource {

    private static final Logger LOG = LoggerFactory.getLogger(GoogleMyBusinessResource.class);

    @Autowired
    private CustomerService customerService;

    @Autowired
    private CustomerCommunicationEventPublisher publisher;

    @Autowired
    private EnvironmentProperties environment;

    @Value("${gamified.offer.generation.url}")
    private String url;

    @Value("${gamified.offer.generation.token}")
    private String auth;

    @Value("${gamified.offer.generation.campaignToken}")
    private String campaignToken;

	@PostMapping("/create")
	public Map<String, Object> createCustomer(@RequestBody Customer customer) {
		customer.setOptWhatsapp(AppConstants.YES);
		customer.setSmsSubscriber(true);
		customer.setAcquisitionSource(AppConstants.GOOGLE_MY_BUSINESS);
		customer.setAcquisitionToken(AppConstants.GOOGLE_MY_BUSINESS);
		customer.setAcquisitionBrandId(1);
		try {
			Customer customerData = customerService.getCustomer(customer.getContactNumber());
			if (Objects.isNull(customerData)) {
				customerData = customerService.addCustomerUnchecked(customer);
				publisher.publishCustomerCommunicationEvent(environment.getEnvironmentType().name(),
						getNotificationPayload(customer));
			}
			try {
				String gamifiedURL = String.format(url, customer.getContactNumber(), campaignToken, AppConstants.GOOGLE_MY_BUSINESS,
						AppConstants.GOOGLE_MY_BUSINESS);
				GamifiedOfferResponse response = WebServiceHelper.postRequestWithAuthInternalWithTimeout(gamifiedURL, null,
						GamifiedOfferResponse.class, auth);
				Map<String, Object> map = new HashMap<>();
				map.put("offerCode", response.getOfferCode());
				map.put("validityFrom", response.getValidityFrom());
				map.put("validityTill", response.getValidityTill());
				map.put("text", response.getText());
				map.put("offerTnCString", response.getOfferTnCString());
				map.put("chaayosCash", response.getChaayosCash());
				map.put("maxUsage", response.getMaxUsage());
				return map;
			} catch (Exception e) {
				LOG.info("Unable to get coupopn for customer id {} ", customer.getId(), e);
			}
		} catch (DataUpdationException e) {
			log.info("Unable to create customer for Acquisation source {}", AppConstants.GOOGLE_MY_BUSINESS);
		} catch (JMSException e) {
			log.info("Unable to subscribe customer on gupshup for Acquisation source {}",
					AppConstants.GOOGLE_MY_BUSINESS);
		}
		return Collections.emptyMap();
	}

    private NotificationPayload getNotificationPayload(Customer customerResponse) {
        NotificationPayload payload = new NotificationPayload();
        payload.setCustomerId(customerResponse.getId());
        payload.setOrderId(0);
        payload.setMessageType(AppConstants.WA_OPT_IN);
        payload.setContactNumber(customerResponse.getContactNumber());
        payload.setRequestTime(AppUtils.getCurrentTimestamp());
        payload.setSendWhatsapp(true);
        payload.setWhatsappOptIn(true);
        Map<String, String> map = new HashMap<>();
        map.put("firstName", customerResponse.getFirstName());
        payload.setPayload(map);
        return payload;
    }

}
