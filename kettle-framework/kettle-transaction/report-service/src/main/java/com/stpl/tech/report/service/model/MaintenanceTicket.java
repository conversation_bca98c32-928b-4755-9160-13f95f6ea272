package com.stpl.tech.report.service.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import lombok.ToString;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ToString
@Builder
public class MaintenanceTicket {

       boolean  alert;
         boolean autorespond;
        String  source;
        String name;
        String email;
         Integer phone;
        String subject;
        String ip;
         Integer topicId;
         String message;
         String checkList;
         String date;
         String dateTime;
         String eqpName;
         String mntcZone;

}
