package com.stpl.tech.report.service.service.impl;

import java.lang.reflect.InvocationTargetException;
import java.security.GeneralSecurityException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.SecretKey;
import javax.persistence.EntityManager;
import javax.persistence.PersistenceContext;
import javax.persistence.Query;
import javax.persistence.Tuple;

import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang3.StringUtils;
import org.codehaus.jettison.json.JSONException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.stpl.tech.kettle.customer.dao.CustomerDataLookupDao;
import com.stpl.tech.kettle.data.converter.CustomerInfoDataConverter;
import com.stpl.tech.kettle.data.model.CustomerDataLookup;
import com.stpl.tech.kettle.domain.model.DataEncryptionRequest;
import com.stpl.tech.report.service.service.DataEncryptionService;
import com.stpl.tech.spring.crypto.CryptoService;

import lombok.extern.slf4j.Slf4j;

@Service
@Slf4j
public class DataEncryptionServiceImpl implements DataEncryptionService {

	@PersistenceContext(unitName = "TransactionDataSourcePUName")
	@Qualifier(value = "TransactionDataSourceEMFactory")
	private EntityManager tarnsactionEntityManager;

	@PersistenceContext(unitName = "MasterDataSourcePUName")
	@Qualifier(value = "MasterDataSourceEMFactory")
	protected EntityManager masterEntityManager;

	@PersistenceContext(unitName = "RekognitionDataSourcePUName")
	@Qualifier(value = "RekognitionDataSourceEMFactory")
	protected EntityManager rekognitionEntityManager;

	@Autowired
	private CryptoService cryptoService;

	@Autowired
	@Qualifier("encryptionCipher")
	private Cipher encryptionCipher;

	@Autowired
	@Qualifier("decryptionCipher")
	private Cipher decryptionCipher;

	@Autowired
	private CustomerInfoDataConverter customerInfoDataConverter;

	@Autowired
	private CustomerDataLookupDao customerDataLookupDao;

	private static final String QUERY_STRING = "FROM %s WHERE %s >= :id ORDER BY %s DESC ";

	@Override
	public String encryptData(String plainText) throws GeneralSecurityException, JSONException {
		StringBuilder sb = new StringBuilder();
		for (String s : StringEscapeUtils.unescapeJava(plainText).split(",")) {
			try {
				sb.append(cryptoService.encrypt(s, encryptionCipher));
				sb.append(System.lineSeparator());
			} catch (IllegalBlockSizeException | BadPaddingException e) {
				log.info("unable to encrypt text ");
				throw e;
			}
		}
		return sb.toString();
	}

	@Override
	public String decryptData(String plainText) throws GeneralSecurityException {
		StringBuilder sb = new StringBuilder();
		for (String s : StringEscapeUtils.unescapeJava(plainText).split(",")) {
			try {
				sb.append(cryptoService.decrypt(s, decryptionCipher));
				sb.append(System.lineSeparator());
			} catch (IllegalBlockSizeException | BadPaddingException e) {
				log.info("unable to dcrypt text ");
				throw e;
			}
		}
		return sb.toString();
	}

	private boolean bulkEncryptData(DataEncryptionRequest request, EntityManager entityManager)
			throws GeneralSecurityException, IllegalAccessException, IllegalArgumentException,
			InvocationTargetException, NoSuchMethodException, SecurityException {
		int startCustomerId = request.getStartCustomerId();
		int endCustomerId = request.getEndCustomerId();
		int batchSize = request.getBatchSize();
		String src = request.getSource();
		String idAttr = request.getIdAttr();
		String emailAttr = request.getEmailAttr();
		String contactAttr = request.getContactAttr();
		Cipher cipher = null;
		if (request.getRefresh()) {
			SecretKey key = cryptoService.generateAESSecretKey(
					cryptoService.generatePasswordBasedKeySpec(request.getPassCode(), request.getSalt(), 65536, 256));
			cryptoService.getAESECBCipher("ENCRYPT", key, true, cipher);
		}
		while (startCustomerId < endCustomerId) {
			Query query = entityManager.createQuery(String.format(QUERY_STRING, src, idAttr, idAttr), Tuple.class);
			query.setMaxResults(batchSize);
			query.setParameter("id", startCustomerId);
			List<Map<String, Object>> customers = new ArrayList<>();
			List<Tuple> result = query.getResultList();
			if (!CollectionUtils.isEmpty(result)) {
				for (Tuple t : result) {
					Map<String, Object> map = new HashMap<>();
					Object obj = t.get(0);
					Class clz = obj.getClass();
					if (StringUtils.isNotBlank(idAttr)) {
						map.put(idAttr, clz.getMethod("get" + StringUtils.capitalize(idAttr), null).invoke(obj, null));
					}
					if (StringUtils.isNotBlank(emailAttr)) {
						map.put(emailAttr,
								clz.getMethod("get" + StringUtils.capitalize(emailAttr), null).invoke(obj, null));
					}
					if (StringUtils.isNotBlank(contactAttr)) {
						map.put(contactAttr,
								clz.getMethod("get" + StringUtils.capitalize(contactAttr), null).invoke(obj, null));
					}
					customers.add(map);
				}
			}
			List<CustomerDataLookup> customerData = new ArrayList<>();
			if (!CollectionUtils.isEmpty(customers)) {
				for (Map<String, Object> customer : customers) {
					customerData.add(customerInfoDataConverter.convert(null, customer, request, cipher));
				}
			}
			customerDataLookupDao.updateAll(customerData);
			startCustomerId += batchSize;
		}
		return true;
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean bulkEncryptDataKettle(DataEncryptionRequest request)
			throws GeneralSecurityException, IllegalAccessException, IllegalArgumentException,
			InvocationTargetException, NoSuchMethodException, SecurityException {
		return bulkEncryptData(request, tarnsactionEntityManager);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean bulkEncryptDataMaster(DataEncryptionRequest request)
			throws GeneralSecurityException, IllegalAccessException, IllegalArgumentException,
			InvocationTargetException, NoSuchMethodException, SecurityException {
		return bulkEncryptData(request, masterEntityManager);
	}

	@Override
	@Transactional(rollbackFor = Exception.class, value = "RekognitionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	public boolean bulkEncryptDataRekognition(DataEncryptionRequest request)
			throws GeneralSecurityException, IllegalAccessException, IllegalArgumentException,
			InvocationTargetException, NoSuchMethodException, SecurityException {
		return bulkEncryptData(request, rekognitionEntityManager);
	}

}
