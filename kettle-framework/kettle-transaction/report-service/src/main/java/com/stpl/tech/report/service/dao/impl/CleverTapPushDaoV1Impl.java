package com.stpl.tech.report.service.dao.impl;

import com.stpl.tech.report.service.dao.CleverTapPushV1Dao;
import com.stpl.tech.report.service.model.CLMDataPushStatus;
import com.stpl.tech.util.JSONSerializer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Repository
public class CleverTapPushDaoV1Impl extends CLMAbstractDaoImpl implements CleverTapPushV1Dao {

    private static final Logger LOG = LoggerFactory.getLogger(CleverTapPushDaoV1Impl.class);

    @Override
    public List<Object[]> getCLMProfileBulkData(Integer startId, Integer batchSize, Integer brandId, boolean isTrimmed) {
        LOG.info("CLEVER_TAP-USER-PROFILE-PUSH :: Getting data for start id : {}, batchSize : {}, and brand id : {}",startId,batchSize,brandId);
        try{
            String queryString =  "CALL CLM_PROFILE_DATA_FOR_A_RANGE(:startCustomerId, :brandId, :batchSize)";
            if(isTrimmed){
                queryString = "CALL CLM_PROFILE_DATA_FOR_A_RANGE_TRIMMED(:startCustomerId, :brandId, :batchSize)";
            }
            Query query = manager.createNativeQuery(queryString);
            query.setParameter("brandId",brandId);
            query.setParameter("startCustomerId",startId);
            query.setParameter("batchSize",batchSize);
            List<Object[]> obj = query.getResultList();
            LOG.info("CLEVER_TAP-USER-PROFILE-PUSH :: Got data of size : {}",obj.size());
            return obj;
        }catch (Exception e){
            LOG.error("CLEVER_TAP-USER-PROFILE-PUSH ::  Error while fetching data for startId : {}," +
                    " brand id : {}, batch size : {}",startId,brandId,batchSize);
        }
        return null;
    }

    @Override
    public List<Object[]> getCLMEventBulkData(Integer startId, Integer dataSize, Integer eventId, boolean isTrimmed) {
        LOG.info("CLEVER_TAP-USER-EVENT-PUSH :: Getting data for event id : {}, start id : {}", eventId, startId);
        try {
            Query query = manager.createNativeQuery("CALL CLM_PROFILE_DATA_FOR_AN_EVENT(:eventId, :startId, :dataSize)");
            query.setParameter("eventId", eventId);
            query.setParameter("startId", startId);
            query.setParameter("dataSize", dataSize);
            List<Object[]> obj = query.getResultList();
            return obj;
        } catch (Exception e) {
            LOG.error("CLEVER_TAP-USER-EVENT-PUSH ::  Error while fetching data for eventId : {}", eventId);
        }
        return null;
    }

    @Override
    public void updateClmEventCalculation(Date dataPushStartTime, CLMDataPushStatus clmDataPushStatus, Integer eventId, Integer brandId) {
        LOG.info("Updating push time and push status for Clm event calculation for event id : {}, brand id :{}", eventId, brandId);
        try {
            Query query = manager.createNativeQuery("CALL UPDATE_CLM_EVENT_CALCULATION(:dataPushTime, :dataPushStatus, :eventId, :brandId)");
            query.setParameter("dataPushTime", dataPushStartTime);
            query.setParameter("dataPushStatus", clmDataPushStatus.name());
            query.setParameter("eventId", eventId);
            query.setParameter("brandId", brandId);
            query.executeUpdate();
        } catch (Exception e) {
            LOG.error("Error while updating push time and push status for Clm event calculation for event id : {}", eventId);
        }
    }

    @Override
    public void addDataPushStatus(Integer eventId, Integer batchSize, Integer startId, Integer endId, Integer pageSize,
                                  Date startTime, Date endTime, long elapsed, Integer brandId, String status) {
        LOG.info("Adding push status for event id : {}, brand id : {}", eventId, brandId);
        try {
            Query query = manager.createNativeQuery("CALL CLM_DATA_PUSH_TRACK(:brandId, :eventId, :batchSize, :startId, :endId, :pageSize, " +
                    ":startTime, :endTime, :timeElapsed, :pushStatus)");
            query.setParameter("brandId", brandId);
            query.setParameter("eventId", Objects.isNull(eventId) ? -1 : eventId);
            query.setParameter("batchSize", batchSize);
            query.setParameter("startId", startId);
            query.setParameter("endId", endId);
            query.setParameter("pageSize", pageSize);
            query.setParameter("startTime", startTime);
            query.setParameter("endTime", endTime);
            query.setParameter("timeElapsed", elapsed + " ms");
            query.setParameter("pushStatus", status);
            query.executeUpdate();
        } catch (Exception e) {
            LOG.info("Error while adding push status for event id : {}, brand id : {}", eventId, brandId);
        }
    }

    @Override
    public List<Object[]> getCLMChargedOrderData(Date businessDate, Integer brandId) {
        LOG.info("CLEVER_TAP-USER-PROFILE-PUSH :: Getting data for businessDate : {},  and brand id : {}", JSONSerializer.toJSON(businessDate), brandId);
        try {
            Query query = manager.createNativeQuery("CALL CLM_CHARGED_EVENT_DATA_FOR_A_DATE(:businessDate, :brandId)");
            query.setParameter("businessDate", businessDate);
            query.setParameter("brandId", brandId);
            List<Object[]> obj = query.getResultList();
            LOG.info("CLEVER_TAP-USER-PROFILE-PUSH :: Got data of size : {}", obj.size());
            return obj;
        } catch (Exception e) {
            LOG.error("CLEVER_TAP-USER-PROFILE-PUSH ::  Error while fetching data for businessDate : {}," +
                    " brand id : {}", JSONSerializer.toJSON(businessDate), brandId);
        }
        return null;
    }

    @Override
    public void addOrderDataPushStatus(Date businessDate, Integer batchSize, Integer startId, Integer endId, Integer pageSize, Date startTime, Date endTime, long elapsed, Integer brandId, String pushStatus) {
        LOG.info("Adding push status for businessDate : {}, brand id : {}", JSONSerializer.toJSON(businessDate), brandId);
        try {
            Query query = manager.createNativeQuery("CALL CLM_ORDER_DATA_PUSH_TRACK(:brandId, :businessDate, :batchSize, :startId, :endId, :pageSize, " +
                    ":startTime, :endTime, :timeElapsed, :pushStatus)");
            query.setParameter("brandId", brandId);
            query.setParameter("businessDate", businessDate);
            query.setParameter("batchSize", batchSize);
            query.setParameter("startId", startId);
            query.setParameter("endId", endId);
            query.setParameter("pageSize", pageSize);
            query.setParameter("startTime", startTime);
            query.setParameter("endTime", endTime);
            query.setParameter("timeElapsed", elapsed + " ms");
            query.setParameter("pushStatus", pushStatus);
            query.executeUpdate();
        } catch (Exception e) {
            LOG.info("Error while adding push status for businessDate : {}, brand id : {}", JSONSerializer.toJSON(businessDate), brandId, e);
        }
    }

    @Override
    public List<Object[]> getCLMChargedOrderDataRange(Integer startCustomerId, Integer endCustomerId, Integer brandId) {
        LOG.info("CLEVER_TAP-USER-PROFILE-PUSH :: Getting data for  and brand id : {}", brandId);
        try {
            Query query = manager.createNativeQuery("CALL CLM_CHARGED_EVENT_DATA_FOR_CUSTOMERS(:startCustomerId, :endCustomerId, :brandId)");
            query.setParameter("startCustomerId", startCustomerId);
            query.setParameter("endCustomerId", endCustomerId);
            query.setParameter("brandId", brandId);
            List<Object[]> obj = query.getResultList();
            LOG.info("CLEVER_TAP-USER-PROFILE-PUSH :: Got data of size : {}", obj.size());
            return obj;
        } catch (Exception e) {
            LOG.error("CLEVER_TAP-USER-PROFILE-PUSH ::  Error while fetching data for start customer id : {}, end customer id : {}," +
                    " brand id : {}", startCustomerId, endCustomerId, brandId);
        }
        return null;
    }

    @Override
    public Integer getEventIdForDate(Date date, Integer brandId) {
        try {
            Query query = manager.createNativeQuery("SELECT CLM_EVENT_ID FROM CLM_CALCULATION_EVENT WHERE BUSINESS_DATE = :date AND BRAND_ID = :brandId ORDER BY 1 DESC LIMIT 1");
            query.setParameter("date", date);
            query.setParameter("brandId", brandId);
            return (Integer) query.getSingleResult();
        } catch (Exception e) {
            LOG.error("Error while fetching event id for date : {}", JSONSerializer.toJSON(date), e);
        }
        return null;
    }

    @Override
    public List<Object[]> getCLMMembershipEventData(Date startDate, Integer brandId) {
        LOG.info("CLEVER_TAP-MEMBERSHIP-EVENT -PUSH :: Getting data for businessDate : {},  and brand id : {}", JSONSerializer.toJSON(startDate), brandId);
        try {
            Query query = manager.createNativeQuery("CALL CLM_CHAAYOS_MEMBERSHIP_EVENT_DATA_FOR_A_DATE(:businessDate, :brandId)");
            query.setParameter("businessDate", startDate);
            query.setParameter("brandId", brandId);
            List<Object[]> obj = query.getResultList();
            LOG.info("CLEVER_TAP-MEMBERSHIP-EVENT-PUSH :: Got data of size : {}", obj.size());
            return obj;
        } catch (Exception e) {
            LOG.error("CLEVER_TAP-MEMBERSHIP-EVENT-PUSH ::  Error while fetching data for businessDate : {}," +
                    " brand id : {}", JSONSerializer.toJSON(startDate), brandId);
        }
        return null;
    }

    @Override
    public List<Object[]> getCLMWalletEventData(Date startDate, Integer brandId) {
        LOG.info("CLEVER_TAP-WALLET-EVENT-PUSH :: Getting data for businessDate : {},  and brand id : {}", JSONSerializer.toJSON(startDate), brandId);
        try {
            Query query = manager.createNativeQuery("CALL CLM_CHAAYOS_WALLET_EVENT_DATA_FOR_A_DATE(:businessDate, :brandId)");
            query.setParameter("businessDate", startDate);
            query.setParameter("brandId", brandId);
            List<Object[]> obj = query.getResultList();
            LOG.info("CLEVER_TAP-WALLET-EVENT-PUSH :: Got data of size : {}", obj.size());
            return obj;
        } catch (Exception e) {
            LOG.error("CLEVER_TAP-WALLET-EVENT-PUSH ::  Error while fetching data for businessDate : {}," +
                    " brand id : {}", JSONSerializer.toJSON(startDate), brandId);
        }
        return null;
    }

    @Override
    public void addMembershipDataPushStatus(Date businessDate, Integer batchSize, Integer startId, Integer endId, Integer pageSize, Date startTime, Date endTime, long elapsedTime, Integer brandId, String status) {
        LOG.info("Adding push status for businessDate : {}, brand id : {}", JSONSerializer.toJSON(businessDate), brandId);
        try {
            Query query = manager.createNativeQuery("CALL CLM_MEMBERSHIP_DATA_PUSH_TRACK(:brandId, :businessDate, :batchSize, :startId, :endId, :pageSize, " +
                    ":startTime, :endTime, :timeElapsed, :pushStatus)");
            query.setParameter("brandId", brandId);
            query.setParameter("businessDate", businessDate);
            query.setParameter("batchSize", batchSize);
            query.setParameter("startId", startId);
            query.setParameter("endId", endId);
            query.setParameter("pageSize", pageSize);
            query.setParameter("startTime", startTime);
            query.setParameter("endTime", endTime);
            query.setParameter("timeElapsed", elapsedTime + " ms");
            query.setParameter("pushStatus", status);
            query.executeUpdate();
        } catch (Exception e) {
            LOG.info("Error while adding push status for businessDate : {}, brand id : {}", JSONSerializer.toJSON(businessDate), brandId, e);
        }
    }

    @Override
    public void addWalletDataPushStatus(Date businessDate, Integer batchSize, Integer startId, Integer endId, Integer pageSize, Date startTime, Date endTime, long elapsedTime, Integer brandId, String status) {
        LOG.info("Adding push status for businessDate : {}, brand id : {}", JSONSerializer.toJSON(businessDate), brandId);
        try {
            Query query = manager.createNativeQuery("CALL CLM_WALLET_DATA_PUSH_TRACK(:brandId, :businessDate, :batchSize, :startId, :endId, :pageSize, " +
                    ":startTime, :endTime, :timeElapsed, :pushStatus)");
            query.setParameter("brandId", brandId);
            query.setParameter("businessDate", businessDate);
            query.setParameter("batchSize", batchSize);
            query.setParameter("startId", startId);
            query.setParameter("endId", endId);
            query.setParameter("pageSize", pageSize);
            query.setParameter("startTime", startTime);
            query.setParameter("endTime", endTime);
            query.setParameter("timeElapsed", elapsedTime + " ms");
            query.setParameter("pushStatus", status);
            query.executeUpdate();
        } catch (Exception e) {
            LOG.info("Error while adding push status for businessDate : {}, brand id : {}", JSONSerializer.toJSON(businessDate), brandId, e);
        }
    }
}
