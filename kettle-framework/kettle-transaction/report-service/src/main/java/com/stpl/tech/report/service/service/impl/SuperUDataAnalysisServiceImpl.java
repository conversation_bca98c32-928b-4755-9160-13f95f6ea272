package com.stpl.tech.report.service.service.impl;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.stpl.tech.kettle.customer.dao.CustomerDao;
import com.stpl.tech.kettle.data.dao.SuperUAlertsDataDao;
import com.stpl.tech.kettle.data.dao.SuperUOrderLevelDataDao;
import com.stpl.tech.kettle.data.model.BcxCategorySuperU;
import com.stpl.tech.kettle.data.model.BcxSubCategorySuperU;
import com.stpl.tech.kettle.data.model.SuperUAlertsData;
import com.stpl.tech.kettle.data.model.SuperUDomain;
import com.stpl.tech.kettle.data.model.SuperUOrderAnalysisData;
import com.stpl.tech.kettle.data.dao.SuperUCategoryDao;
import com.stpl.tech.kettle.data.dao.SuperUOrderAnalysisDataDao;
import com.stpl.tech.kettle.data.model.SuperUOrderLevelData;
import com.stpl.tech.kettle.data.model.SuperUOrderRatingData;
import com.stpl.tech.kettle.data.model.SuperUOrderRatingType;
import com.stpl.tech.kettle.domain.model.Customer;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.report.service.core.ReportProperties;
import com.stpl.tech.report.service.model.ProductQuantity;
import com.stpl.tech.report.service.model.SuperUAlert;
import com.stpl.tech.report.service.service.SuperUDataAnalysisService;
import com.stpl.tech.report.service.service.SuperUDataCache;
import com.stpl.tech.report.service.util.Converter;
import com.stpl.tech.report.service.util.Endpoints;
import com.stpl.tech.report.service.util.ReportUtill;
import com.stpl.tech.util.AppUtils;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@Log4j2
public class SuperUDataAnalysisServiceImpl implements SuperUDataAnalysisService {

    @Autowired
    SuperUCategoryDao superUCategoryDao;

    @Autowired
    SuperUOrderAnalysisDataDao superUOrderAnalysisDataDao;

    @Autowired
    private ReportProperties properties;

    @Autowired
    private CustomerDao customerDao;
    @Autowired
    private SuperUOrderLevelDataDao superUOrderLevelDataDao;

    @Autowired
    private SuperUAlertsDataDao superUAlertsDataDao;

    @Autowired
    private SuperUDataCache superUDataCache;
    private  Map<Integer,Map<String,BigDecimal>>  weightsCache = new ConcurrentHashMap<>();

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<SuperUOrderAnalysisData> generateRatingData(Integer unitId) throws JsonProcessingException, ParseException {
       List<BcxCategorySuperU> category =   superUCategoryDao.findAll();
       List<SuperUOrderAnalysisData> orders = superUOrderAnalysisDataDao.findByUnitIdAndIsProcessed(unitId,"N");
        log.info("Generating rating data for unit id : {} , data size : {}",unitId,orders.size());
       if(weightsCache.get(unitId)==null || weightsCache.get(unitId).isEmpty()){
           getWeightsForParameters(unitId);
       }
       Map<String,BigDecimal> weights = weightsCache.get(unitId);
       for(SuperUOrderAnalysisData order : orders){
           String dataJson = getCategoryParamsJson(order);
           List<BcxCategorySuperU> categoryOrder = ReportUtill.getDeepClone(category);

           boolean isNewCustomer = false;
           // set weights and other meta data for cat and sub cat
           for(BcxCategorySuperU cat : categoryOrder){
              if(order.getCustomerId() !=null){
                  if(order.getCustomerId() <= 5 || order.getCustomerType().equalsIgnoreCase("NEW")) isNewCustomer = true;
              }

               setSubCategoryValues(cat.getSubCategorySuperu(),dataJson,weights,isNewCustomer);
               cat.setWeight(weights.get(cat.getCategoryAlias()));
           }



           // calculating category rating, from sub category ratings.
           for(BcxCategorySuperU cat : categoryOrder){
               BigDecimal subCatRatingSum = getApplicableSubCatRatingSum(cat.getSubCategorySuperu());
               BigDecimal subCatWeightSum = getApplicableSubCatWeightSum(cat.getSubCategorySuperu());

               // cat not applicable
               if(subCatWeightSum.compareTo(BigDecimal.ZERO) == 0){
                   cat.setApplicable(false);
                   cat.setValue(BigDecimal.valueOf(-1));
                   cat.setCategoryRating(BigDecimal.valueOf(-1));
                  continue;
               }
               // cat is applicable
               BigDecimal catRating = subCatRatingSum.divide(subCatWeightSum,2,RoundingMode.HALF_UP);
               cat.setValue(catRating);
               cat.setCategoryRating(catRating.multiply(cat.getWeight()));
           }

           // calcualte order rating
           BigDecimal categoryRatingSum =  getApplicableCatRatingSum(categoryOrder);
           BigDecimal categoryWeightSum = getApplicableCatWeightSum(categoryOrder);

           // order is not applicable
           if(categoryWeightSum.compareTo(BigDecimal.valueOf(0))==0){
               order.setApplicable(false);
               order.setOrderRating(BigDecimal.valueOf(-1));
               order.setCategorySuperU(ReportUtill.getDeepClone(categoryOrder));
              continue;
           }

           // order is applicable
           BigDecimal orderRating = categoryRatingSum.divide(categoryWeightSum,2,RoundingMode.HALF_UP);
           int multiplier = categoryOrder.get(0).getRatingFactor();
          orderRating = orderRating.multiply(BigDecimal.valueOf(multiplier));
           order.setOrderRating(orderRating);

           order.setCategorySuperU(categoryOrder);
       }
       List<SuperUOrderLevelData> orderLevelData = saveOrderData(orders);
       orderLevelData.forEach(e->{
           log.info("Adding  rating data to cahce for unit id : {} , data size : {}",unitId,orders.size());
           superUDataCache.addDataToCache(unitId,e,true);
       });
        log.info("Rating data generation finished for unit id : {} , data size : {}",unitId,orders.size());
       return orders;
   }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
   public boolean markFeedbackRead(Integer orderLevelId){
       Optional<SuperUOrderLevelData> data = superUOrderLevelDataDao.findById(orderLevelId);
        if(data.isPresent()){
            SuperUOrderLevelData orderLevelData = data.get();
            orderLevelData.setIsFeedbackRead(true);
            superUOrderLevelDataDao.save(orderLevelData);
            superUDataCache.updateOrderLevelData(orderLevelData.getUnitId(),orderLevelData);
            return true;
        }
        return false;
   }
   private List<SuperUOrderLevelData> saveOrderData(List<SuperUOrderAnalysisData> orders) throws ParseException, JsonProcessingException {
       SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
       List<SuperUOrderLevelData> orderLevelData = new ArrayList<>();
       for(SuperUOrderAnalysisData order : orders){
           SuperUOrderLevelData superUOrderLevelData = new SuperUOrderLevelData();
           superUOrderLevelData.setOrderId(order.getOrderId());
           Customer customer = null;
           try{
                customer = customerDao.getCustomer(order.getCustomerId());
           }catch (Exception e){
               log.info("Error while getting customer : {}",e.getMessage());
           }
           superUOrderLevelData.setCustomerName(customer != null ? customer.getFirstName() : "Default_Customer_Name");
           superUOrderLevelData.setOrderRating(order.getOrderRating());
           superUOrderLevelData.setIsApplicable(order.isApplicable());
           superUOrderLevelData.setUnitId(order.getUnitId());
           superUOrderLevelData.setBillingServerTime(formatter.parse(order.getBillingServerTime()));
           superUOrderLevelData.setOrderDetails(getOrderDetailString(order));
           superUOrderLevelData.setFeedback(getOrderFeedback(order.getCategorySuperU()));
           List<SuperUOrderRatingData> orderRatingData = new ArrayList<>();
           for(BcxCategorySuperU categorySuperU : order.getCategorySuperU()){
               SuperUOrderRatingData categoryRating = new SuperUOrderRatingData();
               categoryRating.setRating(categorySuperU.getCategoryRating());
               categoryRating.setWeight(categorySuperU.getWeight());
               categoryRating.setIsApplicable(categorySuperU.isApplicable());
               categoryRating.setValue(categorySuperU.getValue());
               categoryRating.setKey(categorySuperU.getCategoryAlias());
               categoryRating.setKeyId(categorySuperU.getBcxCategorySuperuId());
               categoryRating.setType(SuperUOrderRatingType.CATEGORY);
               categoryRating.setSuperUOrderLevelData(superUOrderLevelData);
               orderRatingData.add(categoryRating);
               for(BcxSubCategorySuperU subCategorySuperU : categorySuperU.getSubCategorySuperu()){
                   SuperUOrderRatingData subCategoryRating = new SuperUOrderRatingData();
                   subCategoryRating.setRating(subCategorySuperU.getSubCategoryRating());
                   subCategoryRating.setWeight(subCategorySuperU.getWeight());
                   subCategoryRating.setIsApplicable(subCategorySuperU.isApplicable());
                   subCategoryRating.setValue(BigDecimal.valueOf(subCategorySuperU.getValue()));
                   subCategoryRating.setKey(subCategorySuperU.getSubCategoryName());
                   subCategoryRating.setKeyId(subCategorySuperU.getBcxSubCategoryId());
                   subCategoryRating.setType(SuperUOrderRatingType.SUB_CATEGORY);
                   subCategoryRating.setSuperUOrderLevelData(superUOrderLevelData);
                   orderRatingData.add(subCategoryRating);
               }
           }
           superUOrderLevelData.setOrderRatingData(orderRatingData);
           orderLevelData.add(superUOrderLevelData);
           order.setIsProcessed("Y");

       }
       superUOrderAnalysisDataDao.saveAll(orders);
      return superUOrderLevelDataDao.saveAll(orderLevelData);

   }

   private String getOrderFeedback(List<BcxCategorySuperU> orderCatData){
       BcxSubCategorySuperU minRatingSubCat = null;
       int  min = Integer.MAX_VALUE;
       String defaultFeedback = "";
       for(BcxCategorySuperU cat : orderCatData){
           for(BcxSubCategorySuperU subCat : cat.getSubCategorySuperu()){
               if(subCat.getFeedBack()!=null){
                   defaultFeedback  = subCat.getDefaultFeedback();
                   if(subCat.isApplicable()){
                       if(subCat.getValue() < min){
                           minRatingSubCat = subCat;
                           min = subCat.getValue();
                       }
                       if(subCat.getValue() == minRatingSubCat.getValue()){
                           if(subCat.getPriorityOrder() < minRatingSubCat.getPriorityOrder()){
                               minRatingSubCat = subCat;
                               min = subCat.getValue();
                           }
                       }
                   }
               }
           }
       }
        if(min==1 || minRatingSubCat==null ) return defaultFeedback;
       return minRatingSubCat.getFeedBack();
   }

   private String getOrderDetailString(SuperUOrderAnalysisData order) throws JsonProcessingException {

       String productQuantityJson = order.getProductsAndQuantity();
       Pattern pattern = Pattern.compile("([a-zA-Z0-9\\s\\-]+):\\s*([^,{}]+)");
       Matcher matcher = pattern.matcher(productQuantityJson);

       StringBuffer result = new StringBuffer();
       while (matcher.find()) {
           String key = matcher.group(1).trim();
           String value = matcher.group(2).trim();
           // Wrap keys and values in double quotes
           matcher.appendReplacement(result, "\"" + key + "\":\"" + value + "\"");
       }
       matcher.appendTail(result);

       productQuantityJson = result.toString()
               .replaceAll("\"\"\\s*\\{", "{")
               .replaceAll("\\}\"\"\\s*", "}");

       ObjectMapper objectMapper = new ObjectMapper();
       List<ProductQuantity> productQuantities =   objectMapper.readValue(productQuantityJson, new TypeReference<List<ProductQuantity>>() {});
       StringBuilder orderDetails  = new StringBuilder();
       for(ProductQuantity pq : productQuantities){
           orderDetails.append(pq.getProduct()).append(",");
       }
       orderDetails.append("(Total Value : ").append(order.getRevenue()).append(")");
       return orderDetails.toString();
    }

   private BigDecimal getApplicableSubCatRatingSum(List<BcxSubCategorySuperU> subCatList){
       BigDecimal sum = BigDecimal.ZERO;
        for(BcxSubCategorySuperU subCat : subCatList){
           if(subCat.isApplicable()){
               sum = sum.add(subCat.getSubCategoryRating());
           }
       }
        return sum;
   }

    private BigDecimal getApplicableSubCatWeightSum(List<BcxSubCategorySuperU> subCatList){
        BigDecimal sum = BigDecimal.ZERO;
        for(BcxSubCategorySuperU subCat : subCatList){
            if(subCat.isApplicable()){
                sum = sum.add(subCat.getWeight());
            }
        }
        return sum;
    }

    private BigDecimal getApplicableCatWeightSum(List<BcxCategorySuperU> catList){
        BigDecimal sum = BigDecimal.ZERO;
        for(BcxCategorySuperU subCat : catList){
            if(subCat.isApplicable()){
                sum = sum.add(subCat.getWeight());
            }
        }
        return sum;
    }

    private BigDecimal getApplicableCatRatingSum(List<BcxCategorySuperU> catList){
        BigDecimal sum = BigDecimal.ZERO;
        for(BcxCategorySuperU subCat : catList){
            if(subCat.isApplicable()){
                sum = sum.add(subCat.getCategoryRating());
            }
        }
        return sum;
    }
   private void setSubCategoryValues(List<BcxSubCategorySuperU> list,String valueObject, Map<String,BigDecimal> weights,boolean isNewCustomer) throws JsonProcessingException {
       try{
           ObjectMapper objectMapper = new ObjectMapper();
           JsonNode jsonNode =  objectMapper.readTree(valueObject);
           for(BcxSubCategorySuperU category : list){
              JsonNode jsNode =  jsonNode.get(category.getSubCategoryName());
              category.setValue(getBitValueForSubCategory(jsNode));
              if(!isNewCustomer && (category.getSubCategoryName().equalsIgnoreCase("mentionLoyaltyProgramIfDeniedContact") || category.getSubCategoryName().equalsIgnoreCase("explainLoyaltyProgramIfNewCustomer"))){
                  category.setApplicable(false);
              }else{
                  category.setApplicable(getApplicablityForSubCategory(jsNode));
              }
              category.setWeight(weights.get(category.getSubCategoryName()));
              BigDecimal subCategoryRating =category.getWeight().multiply(BigDecimal.valueOf(category.getValue()));
              subCategoryRating = subCategoryRating.setScale(2,RoundingMode.HALF_UP);
              category.setSubCategoryRating(subCategoryRating);
           }
       }catch(Exception e){
               log.error("Exception while setting subCategory values : "+e.getMessage());
               throw  e;
       }
   }

    private int getBitValueForSubCategory(JsonNode jsonNode){
      if(jsonNode.isNull()){
          return 0;
      }
       if(jsonNode.isTextual()){
             return jsonNode.asText().equalsIgnoreCase("true") ? 1 : 0;
       }else if (jsonNode.isBoolean()){
           return jsonNode.asBoolean() ? 1 : 0;
       }
       log.error("Sub Category value is neither boolean nor String");
       return 0;
    }

    private boolean getApplicablityForSubCategory(JsonNode jsonNode){

        if(jsonNode.isNull()){
            return false;
        }

        if(jsonNode.isTextual()){
            String value = jsonNode.asText();
            return  value.equalsIgnoreCase("true") || value.equalsIgnoreCase("false");
        }

        return true;
    }

  public String  getCategoryParamsJson(SuperUOrderAnalysisData order){

       StringBuffer sb = new StringBuffer();
       sb.append("{");
       sb.append(" \"suggestDesiChaiAndPopularCombosIfNewCustomer\" : \"").append(order.getSuggestDesiChaiAndPopularCombosIfNewCustomer()).append("\",");
      sb.append(" \"explainLoyaltyProgramIfNewCustomer\" : \"").append(order.getExplainLoyaltyProgramIfNewCustomer()).append("\",");
      sb.append("\"useCustomerNameThrice\" : \"").append(order.getUseCustomerNameThrice()).append("\",");
      sb.append("\"providePreciseProductDescriptionsForQueries\" : \"").append(order.getProvidePreciseProductDescriptionsForQueries()).append("\",");
      sb.append("\"greeting\" : ").append(order.getGreeting()).append(",");
      sb.append("\"askForContactNumber\" : \"").append(order.getAskForContactNumber()).append("\",");
      sb.append("\"mentionLoyaltyProgramIfDeniedContact\" : \"").append(order.getMentionLoyaltyProgramIfDeniedContact()).append("\",");
      sb.append("\"thanking\" : ").append(order.getThanking()).append(",");
      sb.append("\"appropriateUpsell\" : \"").append(order.getAppropriateUpsell()).append("\",");
      sb.append("\"suggestMerchandise\" : \"").append(order.getSuggestMerchandise()).append("\",");
      sb.append("\"suggestTopUpWalletIfAvailable\" : \"").append(order.getSuggestTopUpWalletIfAvailable()).append("\" }");

      return sb.toString();
   }

   private void getWeightsForParameters(int unitId){
       try {
           SuperUDomain superUDomain = new SuperUDomain();
           superUDomain.setUnitId(unitId);
           SuperUDomain response = WebServiceHelper.postRequestWithAuthInternalNoTimeout(AppUtils.isProd(properties.getEnvironmentType()) ? Endpoints.SUPERU_PARAMS_URL_PROD : Endpoints.SUPERU_PARAMS_URL_DEV
                   ,superUDomain, SuperUDomain.class,properties.getReportAuthInternal());

           Map<String, BigDecimal> cache = weightsCache.getOrDefault(unitId,new ConcurrentHashMap<>());
           for(String key : response.getReturnMap().keySet()){
              BigDecimal value = new BigDecimal(response.getReturnMap().get(key));
              value = value.setScale(2, RoundingMode.HALF_UP);
              cache.put(key, value);
           }
           weightsCache.put(unitId,cache);
       } catch (IOException e) {
           log.error("Exception while getting the weights from parameters : "+e.getMessage());
           throw new RuntimeException(e);
       }
   }

    @Scheduled(cron = "0 10 0 * * *", zone = "GMT+05:30")
    public void clearWeightCache(){
        log.info("### Clearing weights cache ####");
        this.weightsCache = new ConcurrentHashMap<>();
   }

   @Override
   public SuperUAlert getSuperAlertData(Integer unitId){
       Pageable pageable = PageRequest.of(0,1);
       Page<SuperUAlertsData> res = superUAlertsDataDao.findRecentAlertForUnitId(unitId,pageable);
       if(res.hasContent() && res.getContent().size() >0){
          return Converter.convert(res.getContent().get(0));
       }
      return new SuperUAlert();
    }

}
