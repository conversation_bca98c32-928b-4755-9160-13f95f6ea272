package com.stpl.tech.report.service.service.impl;


import com.stpl.tech.report.service.core.ReportProperties;
import com.stpl.tech.report.service.service.ScoreCardReportService;
import com.stpl.tech.report.service.util.AwsUploadUtil;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.notification.EmailNotification;
import org.apache.commons.io.FileUtils;
import org.pentaho.di.core.KettleEnvironment;
import org.pentaho.di.job.Job;
import org.pentaho.di.job.JobMeta;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.Path;
import java.text.SimpleDateFormat;
import java.util.Date;

@Service
public class ScoreCardReportServiceImpl implements ScoreCardReportService {
    private static final Logger LOG = LoggerFactory.getLogger(ScoreCardReportServiceImpl.class);

    @Autowired
    private AwsUploadUtil awsUploadUtil;
    @Value("${server.base.dir}")
    private String basePath;

    private final String trans_path = getClass().getClassLoader().getResource("pentaho/transformations").getPath();

    private final String jobs_path = getClass().getClassLoader().getResource("pentaho/jobs").getPath();

    @Value("${aws.recon.bucket}")
    private String bucket;

    @Value("${PROD_READ.hostname}")
    private String hostname;

    @Value("${PROD_READ.port}")
    private String port;

    @Value("${PROD_READ.dbname}")
    private String dbName;

    @Value("${PROD.jdbc.user}")
    private String username;

    @Value("${PROD.jdbc.pass}")
    private String password;



    @Autowired
    private ReportProperties reportProperties;

    private static final String DATE_FORMAT = "yyyy-MM-dd";
    @Override
    public void startScoreCardReportsProcess(String startDate,String endDate,String startDateFive, String endDateFive){
     LOG.info("####################### Starting Score Card Reports Scheduled Process ######################");

    try{
        if(startDate==null || endDate==null || startDateFive==null || endDateFive==null) {
         Date endDatePrev = AppUtils.getPreviousDate(AppUtils.getCurrentTimestamp());
         int month = AppUtils.getMonth(endDatePrev);
         int year = AppUtils.getYear(endDatePrev);
         Date startDatePrev = AppUtils.getStartDateOfMonth(year, month);
         startDate = new SimpleDateFormat(DATE_FORMAT).format(startDatePrev);
         endDate = new SimpleDateFormat(DATE_FORMAT).format(endDatePrev);
         startDateFive = "'"+startDate+" 05:00:01"+"'";
         endDateFive ="'"+ new SimpleDateFormat(DATE_FORMAT).format(AppUtils.getCurrentTimestamp()) + " 05:00:00"+"'";
         startDate = "'"+startDate+" 00:00:00"+"'";
         endDate = "'"+endDate+" 23:59:59"+"'";
     }else{
            startDate = "'"+startDate+" 00:00:00"+"'";
            endDate="'"+endDate+" 23:59:59"+"'";
            startDateFive="'"+startDateFive+"'";
            endDateFive="'"+endDateFive+"'";
        }

           KettleEnvironment.init();
           JobMeta jobMeta = new JobMeta(jobs_path+"/"+"score-card-reports.kjb",null);
           Job job = new Job(null, jobMeta);
           String dateTime = new SimpleDateFormat("yyyy-MM-dd-HH-mm-ss").format(new Date());
           String zipFileName="score-card-report-zip-"+dateTime+".zip";
           job.setVariable("start_date", startDate);
           job.setVariable("end_date", endDate);
           job.setVariable("start_date_five",startDateFive);
           job.setVariable("end_date_five",endDateFive);
           job.setVariable("base_path", basePath+"/score-card/output");
           job.setVariable("zip_file_name",zipFileName);
           job.setVariable("trans_path",trans_path);
           job.setVariable("zip_path",basePath+"/score-card");

           job.setVariable("host_name",hostname);
           job.setVariable("user_name", username);
           job.setVariable("port_number",port);
           job.setVariable("db_name",dbName);
           job.setVariable("password",password);
           job.start();
           job.waitUntilFinished();
           if (job.getErrors() > 0) {
               LOG.info("Error in ScoreCardReport job");
               return;
           }
           String downloadLink = getScoreCardDataDownloadLink(zipFileName);
           sendEmail(downloadLink,false);
           LOG.info("####################### Score Card Reports Scheduled Process Completed ######################");
       }catch (Exception e){
           LOG.info("Error in ScoreCardReportsProcess : {}",e.getMessage());
           LOG.info("Exception : {}",e.getStackTrace());
           File file  = new File(basePath+"/score-card/output");
           if(file.listFiles()!=null) {
               try {
                   FileUtils.deleteDirectory(file);
               } catch (IOException ex) {
                   LOG.error("Error while deleting directory : {}",ex.getMessage());
               }
           }
       }
    }

    private String getScoreCardDataDownloadLink(String fileName){
        String key="score-card-data/"+fileName;
        File fileToUpload = new File(basePath+"/score-card/"+fileName);
        awsUploadUtil.uploadFileToS3(bucket,key,fileToUpload);
        return awsUploadUtil.getAwsSignedUrl(bucket,key,7);
    }


    private void sendEmail(String msg,boolean isWeekly) throws EmailGenerationException {
        new EmailNotification() {
            @Override
            public String subject() {
                if(isWeekly){
                    return "Weekly Score Card Reports" + AppUtils.getCurrentDateISTFormatted();
                }
                return "Score Card Reports" + AppUtils.getCurrentDateISTFormatted();
            }
            @Override
            public String[] getToEmails() {
                if(reportProperties.getEnvironmentType()!=EnvType.PROD && reportProperties.getEnvironmentType() != EnvType.SPROD){
                   String[] emails = new String[1];
                   emails[0] = "<EMAIL>";
                   return  emails;
                }
                String[] emails = new String[3];
                emails[0]="<EMAIL>";
                emails[1] ="<EMAIL>";
                emails[2]="<EMAIL>";
                return emails;
            }

            @Override
            public String getFromEmail() {
                return "<EMAIL>";
            }

            @Override
            public EnvType getEnvironmentType() {
                return EnvType.LOCAL;
            }

            @Override
            public String body() throws EmailGenerationException {
                return "Score card reports link : "+ msg;
            }
        }.sendEmail();
    }

    @Override
    public void startScoreCardReportsWeeklyProcess(String startDate,String endDate,String startDateFive, String endDateFive){
        LOG.info("####################### Starting Score Card Reports Scheduled Process ######################");

        try{
            if(startDate==null || endDate==null || startDateFive==null || endDateFive==null) {
                Date endDatePrev = AppUtils.getPreviousSundayDate(AppUtils.getCurrentTimestamp());
                Date startDatePrev = AppUtils.getPreviousMondayDate(AppUtils.getCurrentTimestamp());
                startDate = new SimpleDateFormat(DATE_FORMAT).format(startDatePrev);
                endDate = new SimpleDateFormat(DATE_FORMAT).format(endDatePrev);
                startDateFive = "'"+startDate+" 05:00:01"+"'";
                endDateFive ="'"+ new SimpleDateFormat(DATE_FORMAT).format(AppUtils.getCurrentTimestamp()) + " 05:00:00"+"'";
                startDate = "'"+startDate+" 00:00:00"+"'";
                endDate = "'"+endDate+" 23:59:59"+"'";
            }else{
                startDate = "'"+startDate+" 00:00:00"+"'";
                endDate="'"+endDate+" 23:59:59"+"'";
                startDateFive="'"+startDateFive+"'";
                endDateFive="'"+endDateFive+"'";
            }

            KettleEnvironment.init();
            JobMeta jobMeta = new JobMeta(jobs_path+"/"+"score-card-reports-weekly.kjb",null);
            Job job = new Job(null, jobMeta);
            String dateTime = new SimpleDateFormat("yyyy-MM-dd-HH-mm-ss").format(new Date());
            String zipFileName="score-card-report-weekly-zip-"+dateTime+".zip";
            job.setVariable("start_date", startDate);
            job.setVariable("end_date", endDate);
            job.setVariable("start_date_five",startDateFive);
            job.setVariable("end_date_five",endDateFive);
            job.setVariable("base_path", basePath+"/score-card/output");
            job.setVariable("zip_file_name",zipFileName);
            job.setVariable("trans_path",trans_path);
            job.setVariable("zip_path",basePath+"/score-card");

            job.setVariable("host_name",hostname);
            job.setVariable("user_name", username);
            job.setVariable("port_number",port);
            job.setVariable("db_name",dbName);
            job.setVariable("password",password);
            job.start();
            job.waitUntilFinished();
            if (job.getErrors() > 0) {
                LOG.info("Error in ScoreCardReport job");
                return;
            }
            String downloadLink = getScoreCardDataDownloadLink(zipFileName);
            sendEmail(downloadLink,true);
            LOG.info("####################### Score Card Reports Scheduled Process Completed ######################");
        }catch (Exception e){
            LOG.info("Error in ScoreCardReportsProcess : {}",e.getMessage());
            LOG.info("Exception : {}",e.getStackTrace());
            File file  = new File(basePath+"/score-card/output");
            if(file.listFiles()!=null) {
                try {
                    FileUtils.deleteDirectory(file);
                } catch (IOException ex) {
                    LOG.error("Error while deleting directory : {}",ex.getMessage());
                }
            }
        }
    }
}
