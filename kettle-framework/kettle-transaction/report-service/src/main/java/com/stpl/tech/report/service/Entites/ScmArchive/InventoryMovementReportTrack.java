package com.stpl.tech.report.service.Entites.ScmArchive;

import com.stpl.tech.report.service.model.InventoryMovTrackEventName;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Entity
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "INV_MOV_REPORT_TRACK")
public class InventoryMovementReportTrack {

    @Column(name = "TRACK_ID")
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer trackId;

    @Column(name = "START_TIME")
    private Date startTime;

    @Column(name = "END_TIME")
    private Date endTime;

    @Column(name = "STATUS")
    private String status;

    @Column(name = "MESSAGE")
    private String message;

    @Column(name = "INPUT_START_TIME")
    private Date inputStartTime;

    @Column(name = "INPUT_END_TIME")
    private Date inputEndTime;

    @Column(name = "INPUT_EVENT_TRACK_ID")
    private Integer inputEventTrackId;
    @Column(name = "IS_NOTIFIED")
    private String isNotified = "N";
    @Column(name = "EVENT_NAME")
    @Enumerated(EnumType.STRING)
    private InventoryMovTrackEventName eventName;

}
