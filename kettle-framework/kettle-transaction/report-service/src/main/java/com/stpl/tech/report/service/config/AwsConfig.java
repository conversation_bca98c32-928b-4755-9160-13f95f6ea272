package com.stpl.tech.report.service.config;

import com.amazonaws.auth.AWSStaticCredentialsProvider;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.regions.Regions;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3ClientBuilder;
import com.amazonaws.services.s3.transfer.TransferManager;
import com.amazonaws.services.s3.transfer.TransferManagerBuilder;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.Executors;

import static com.amazonaws.services.s3.internal.Constants.MB;

@Configuration
public class AwsConfig {

    @Value("${environment.type}")
    String envType;
    @Bean
    public AmazonS3 s3Client(){
        BasicAWSCredentials cred = new BasicAWSCredentials("********************", "waqQ/XK541NHcKM7xIWi5WflDunCSqfmTAA9XoSY");
        return AmazonS3ClientBuilder.standard().withCredentials(new AWSStaticCredentialsProvider(cred)).withRegion(AppUtils.getRegion(EnvType.valueOf(envType))).build();
    }

    @Bean
    public TransferManager transferManager(){
          return  TransferManagerBuilder.standard().withS3Client(s3Client()).withMultipartUploadThreshold(Long.valueOf(100*MB)).withExecutorFactory(()-> Executors.newFixedThreadPool(3)).build();
    }

}
