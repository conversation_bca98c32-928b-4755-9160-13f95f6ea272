package com.stpl.tech.report.service.controller;

import com.stpl.tech.kettle.clevertap.util.FavChaiConstants;
import com.stpl.tech.kettle.customer.service.CustomerService;
import com.stpl.tech.master.domain.model.Pair;
import lombok.extern.log4j.Log4j2;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static com.stpl.tech.report.service.core.ReportServiceConstants.API_VERSION;
import static com.stpl.tech.report.service.core.ReportServiceConstants.SEPARATOR;

@RestController
@RequestMapping(value = API_VERSION + SEPARATOR
        + FavChaiConstants.INCREMENTAL_FAV_CHAI_PUSH_ROOT_CONTEXT)
@Log4j2
public class SaveChaiPushResource {
    @Autowired
    private CustomerService customerService;

    @PostMapping(value = "save-incremental-customer-save-chai-data")
    public Pair<Integer, Integer> saveIncrementalCustomerSaveChaiData(@RequestParam Integer batchSize , @RequestParam Integer page ){
        log.info("Got request to delete duplicate save chai");
        return customerService.saveIncrementalCustomerSaveChaiData(batchSize, page);
    }

}
