/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.report.service.controller;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.stpl.tech.kettle.core.service.util.FileNameFilter;
import com.stpl.tech.kettle.core.service.util.QueryExecutorForJsonData;
import com.stpl.tech.kettle.report.metadata.model.ReportCategories;
import com.stpl.tech.kettle.report.metadata.model.ReportData;
import com.stpl.tech.kettle.report.metadata.model.ReportType;
import com.stpl.tech.master.core.config.ServiceConfig;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.report.service.ReportManagementService;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.master.data.model.XMLReportDefinitionData;
import com.stpl.tech.master.util.ServiceUtil;
import com.stpl.tech.report.service.core.ReportProperties;
import com.stpl.tech.report.service.model.FileOutputDetails;
import com.stpl.tech.report.service.model.ReportResponse;
import com.stpl.tech.util.ExecutionEnvironment;
import com.stpl.tech.util.JaxbUtil;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import javax.sql.DataSource;
import javax.ws.rs.core.MediaType;
import java.io.File;
import java.io.IOException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.stpl.tech.report.service.core.ReportServiceConstants.API_VERSION;
import static com.stpl.tech.report.service.core.ReportServiceConstants.REPORT_METADATA_ROOT_CONTEXT;
import static com.stpl.tech.report.service.core.ReportServiceConstants.SEPARATOR;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + REPORT_METADATA_ROOT_CONTEXT)
public class ReportExecutionResources extends AbstractResources {

	private static final Logger LOG = LoggerFactory.getLogger(ReportExecutionResources.class);

	@Autowired
    private ReportProperties props;

	@Autowired
	private ApplicationContext context;

	@Autowired
	private ReportManagementService reportManagementService;

	@RequestMapping(method = RequestMethod.GET, value = "report/environments", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public ExecutionEnvironment[] allEnvironments() {
		return ExecutionEnvironment.values();
	}

	@RequestMapping(method = RequestMethod.GET, value = "report/report-type", produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public ReportType[] allReportType() {
		return ReportType.values();
	}

	@RequestMapping(method = RequestMethod.POST, value = "report/adhoc-reports-list", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<FileOutputDetails> allReports(@RequestBody String file1) {
		// validate(request);
		LOG.info("Getting All Adhoc Report List ");
		String dirName = props.getBasePath() + "/reports";
		File[] files = FileNameFilter.listFile(dirName, "xml");
		List<FileOutputDetails> output = new ArrayList<FileOutputDetails>();
		for (File file : files) {
			output.add(new FileOutputDetails(file.getName(), new Date(file.lastModified())));
		}
		return output;
	}

	@RequestMapping(method = RequestMethod.POST, value = "report/adhoc-reports-list/new", consumes = MediaType.APPLICATION_JSON, produces = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public List<FileOutputDetails> allReportsNew(@RequestBody String executionEnvironment) {
		LOG.info("Getting All Adhoc Report List ");
		List<FileOutputDetails> output = new ArrayList<>();
		for (XMLReportDefinitionData xmlReport : reportManagementService.getAllReportList(executionEnvironment)) {
			output.add(new FileOutputDetails(xmlReport.getReportName(), xmlReport.getLastUpdated(), xmlReport));
		}
		return output;
	}

	@RequestMapping(value = "report/categories", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public ReportCategories getReportCategories(@RequestBody String file) throws IOException {
		return JaxbUtil.jaxbXMLToObject(ReportCategories.class, props.getBasePath() + "/reports/" + file);

	}

	@RequestMapping(value = "report/categories/new", method = RequestMethod.POST, produces = MediaType.APPLICATION_JSON, consumes = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public ReportCategories getReportCategoriesNew(@RequestBody XMLReportDefinitionData reportDetail) throws IOException, DataNotFoundException {

		//getFromS3 on basis of report id
		File file = reportManagementService.getS3FileFromActiveReportVersion(reportDetail.getReportId(), reportDetail.getVersion());

		return JaxbUtil.jaxbXMLToObject(ReportCategories.class, file);

	}

	@RequestMapping(value = "report/execute", method = RequestMethod.POST, consumes = MediaType.APPLICATION_JSON)
	@ResponseStatus(HttpStatus.OK)
	@ResponseBody
	public String executeReport(@RequestBody ReportData report) throws IOException, ParseException {

//		DataSource dataSource = ServiceUtil.getDataSourceBean(ExecutionEnvironment.valueOf(report.getEnvironment()));
		ServiceConfig config = context.getBean(ServiceConfig.class);
		DataSource dataSource = config.getCreateDataSourceBean(ExecutionEnvironment.valueOf(report.getEnvironment()),false);
		QueryExecutorForJsonData executor = new QueryExecutorForJsonData();
		executor.execute(report, dataSource);
		Gson gson = new Gson();
		JsonArray data = executor.getData();
		LOG.info("returned size: " + data.size());
		return gson.toJson(new ReportResponse(executor.getHeader(), executor.getData()));
	}

}
