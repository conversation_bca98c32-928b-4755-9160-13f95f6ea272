package com.stpl.tech.report.service.controller;


import com.stpl.tech.kettle.clevertap.domain.model.CleverTapPushResponse;
import com.stpl.tech.kettle.clevertap.domain.model.GetProfileResponse;
import com.stpl.tech.kettle.clevertap.service.CleverTapDataPushService;
import com.stpl.tech.kettle.clevertap.util.CleverTapConstants;
import com.stpl.tech.report.service.model.CLMDataPushType;
import com.stpl.tech.report.service.model.CLMOrderDataPushRequest;
import com.stpl.tech.report.service.model.CLMUserDataPushRequest;
import com.stpl.tech.report.service.service.CleverTapDataPushServiceV1;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

import javax.ws.rs.core.MediaType;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;


@RestController
@RequestMapping(value = CleverTapConstants.API_VERSION + CleverTapConstants.SEPARATOR
    + CleverTapConstants.ROOT_CONTEXT + CleverTapConstants.SEPARATOR + CleverTapConstants.CLEVERTAP_PUSH_ROOT_CONTEXT)
public class CleverTapPushResource {

    private static final Logger LOG = LoggerFactory.getLogger(CleverTapPushResource.class);


    @Autowired
    private ApplicationContext applicationContext;


    @Autowired
    private CleverTapDataPushService cleverTapDataPushService;

    @Autowired
    private CleverTapDataPushServiceV1 pushServiceV1;

    @RequestMapping(method = RequestMethod.POST, value = "push-users", produces = MediaType.APPLICATION_JSON,
        consumes = MediaType.APPLICATION_JSON)
    public void pushUserProfile(@RequestParam Integer batchSize, @RequestParam(required = false) Integer customerId) {
        ThreadPoolTaskExecutor executor = (ThreadPoolTaskExecutor) applicationContext.getBean("taskExecutor");
        if (customerId == null) {
            customerId = 0;
        }
        List<Integer> customerIds = cleverTapDataPushService.getCustomerIdsBatch(batchSize, customerId);
        while (customerIds != null && !customerIds.isEmpty()) {
            List<Integer> ids = customerIds;
            executor.execute(() -> {
                try {
                    CleverTapPushResponse cleverTapPushResponse = cleverTapDataPushService.pushUsersToCleverTap(ids, CleverTapConstants.BULK);
                    cleverTapDataPushService.persistProfileTrack(cleverTapPushResponse.getProfiles());
                } catch (Exception e) {
                    LOG.error("Error while pushing customer to clevertap: {} ", e);
                }
            });
            LOG.info("size of customerIds List {}", customerIds.size());
            customerIds = cleverTapDataPushService.getCustomerIdsBatch(batchSize, customerIds.get(customerIds.size() - 1));
        }
    }

    @RequestMapping(method = RequestMethod.GET, value = "get-user-profile", produces = MediaType.APPLICATION_JSON,
        consumes = MediaType.APPLICATION_JSON)
    public GetProfileResponse getUserProfile(@RequestParam Integer userId) {
        GetProfileResponse response = new GetProfileResponse();
        try {
            response = cleverTapDataPushService.getUserProfile(userId);
        } catch (Exception e) {
            LOG.info("error while finding user profile for customer: {} and {}", userId, e.getMessage());
        }
        return response;

    }


    @RequestMapping(method = RequestMethod.POST, value = "push-one-event", produces = MediaType.APPLICATION_JSON,
        consumes = MediaType.APPLICATION_JSON)
    public void pushSingleEvent(@RequestParam Integer orderId, @RequestParam String evtName) {
        if (orderId != null) {
            List<Integer> orderList = new ArrayList<>();
            orderList.add(orderId);
            cleverTapDataPushService.uploadEvent(orderList, evtName, CleverTapConstants.REGULAR, null);
        } else {
            LOG.info("No order Id to start with!");
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = "push-events", produces = MediaType.APPLICATION_JSON,
        consumes = MediaType.APPLICATION_JSON)
    public void pushEvents(@RequestParam String evtName, @RequestParam Integer batchSize, @RequestParam(required = false) Integer orderId) {
        ThreadPoolTaskExecutor executor = (ThreadPoolTaskExecutor) applicationContext.getBean("taskExecutor");
        if (orderId == null) {
            orderId = 1;
        }
        List<Integer> orderList = cleverTapDataPushService.getOrdersBatch(orderId, batchSize);
        while (orderList != null && !orderList.isEmpty()) {
            List<Integer> ids = orderList;
            executor.execute(() -> {
                try {
                    CleverTapPushResponse cleverTapPushResponse = cleverTapDataPushService.uploadEvent(ids, evtName, CleverTapConstants.BULK, null);
                    cleverTapDataPushService.persistEventTracks(cleverTapPushResponse.getEvents());
                } catch (Exception e) {
                    LOG.info("Error while pushing orders to clevertap {}", e);
                }
            });
            LOG.info("size of order List {}", orderList.size());
            orderList = cleverTapDataPushService.getOrdersBatch(orderList.get(orderList.size() - 1), batchSize);
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = "update-user", produces = MediaType.APPLICATION_JSON,
        consumes = MediaType.APPLICATION_JSON)
    public void updateUser(@RequestParam Integer customerId) {
        if (customerId != null) {
            cleverTapDataPushService.pushUserToCleverTap(customerId);
        } else {
            LOG.info("No customer Id to start with!");
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = "push-profile-in-batch")
    public void pushUserProfile(@RequestBody CLMUserDataPushRequest request) {
        try {
            LOG.info(" brand id : {}, startId : {}, eventId : {}, dataSize : {}, type : {}",
                    request.getBrandId(), request.getStartId(), request.getEventId(), request.getDataSize(), request.getType());
            if (Objects.isNull(request.getType())) {
                LOG.info("INVALID TYPE REQUEST :::::::::");
            } else if ((CLMDataPushType.PROFILE.getTypeValue().equals(request.getType()))) {
                if (Objects.nonNull(request.getStartId()) && Objects.nonNull(request.getDataSize()) && Objects.nonNull(request.getBrandId())) {
                    pushServiceV1.pushClmData(request);
                } else {
                    LOG.info("INVALID DATA IN REQUEST :::::::::");
                }
            } else if (CLMDataPushType.EVENT.getTypeValue().equals(request.getType())) {
                if (Objects.nonNull(request.getEventId()) && Objects.nonNull(request.getDataSize()) && Objects.nonNull(request.getStartId())) {
                    pushServiceV1.pushClmData(request);
                } else {
                    LOG.info("INVALID DATA IN REQUEST :::::::::");
                }
            } else {
                LOG.info("INVALID TYPE IN API PARAM :::::::::");
            }
        } catch (Exception e) {
            LOG.error("Error while pushing data", e);
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = "push-order-data", consumes = MediaType.APPLICATION_JSON)
    public void pushOrderData(@RequestBody CLMOrderDataPushRequest request) {
        try {
            if (Objects.nonNull(request.getStartBusinessDate()) && Objects.nonNull(request.getBrandId())) {
                pushServiceV1.pushClmChargedEventData(request, CLMDataPushType.CHARGED_ORDER);
            } else {
                LOG.info("INVALID DATA IN REQUEST BODY :::::::::");
            }
        } catch (Exception e) {
            LOG.error("Error while pushing order data ", e);
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = "push-order-data-range", consumes = MediaType.APPLICATION_JSON)
    public void pushOrderDataRange(@RequestBody CLMOrderDataPushRequest request) {
        try {
            if (Objects.nonNull(request.getBrandId())) {
                pushServiceV1.pushClmOrderDataRange(request);
            } else {
                LOG.info("INVALID DATA IN REQUEST PARAM :::::::::");
            }
        } catch (Exception e) {
            LOG.error("Error while pushing order data ", e);
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = "push-profile-event-for-date")
    public void pushProfileDataForDate(@RequestParam String date, @RequestParam Integer brandId,
                                       @RequestParam(required = false) Integer startCustomerId, @RequestParam(required = false) Integer batchSize) {
        try {
            if (Objects.nonNull(date) || Objects.nonNull(brandId)) {
                Date businessDate = AppUtils.addDays(AppUtils.getDate(date, AppUtils.DATE_FORMAT_STRING), -1);
                Integer startId = Objects.nonNull(startCustomerId) ? startCustomerId : 1;
                Integer dataSize = Objects.nonNull(batchSize) ? batchSize : 20000;
                pushServiceV1.pushProfileEventForADate(businessDate, startId, dataSize, brandId);
            } else {
                LOG.info("INVALID DATA IN REQUEST PARAM :::::::::");
            }
        } catch (Exception e) {
            LOG.error("Error while pushing order data ", e);
        }
    }
    
    
    @RequestMapping(method = RequestMethod.POST, value = "push-profile-event-for-event")
    public void pushProfileDataForAnEvent(@RequestParam Integer eventId, @RequestParam Integer brandId,
            @RequestParam(required = false) Integer startCustomerId, @RequestParam(required = false) Integer batchSize) {
        try {
            if (Objects.nonNull(brandId)) {
                Integer startId = Objects.nonNull(startCustomerId) ? startCustomerId : 1;
                Integer dataSize = Objects.nonNull(batchSize) ? batchSize : 20000;
                pushServiceV1.pushProfileEventForAEvent(eventId, startId, dataSize, brandId);
            } else {
                LOG.info("INVALID DATA IN REQUEST PARAM :::::::::");
            }
        } catch (Exception e) {
            LOG.error("Error while pushing order data ", e);
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = "push-membership-data", consumes = MediaType.APPLICATION_JSON)
    public void pushMembershipData(@RequestBody CLMOrderDataPushRequest request) {
        try {
            if (Objects.nonNull(request.getStartBusinessDate()) && Objects.nonNull(request.getBrandId())) {
                pushServiceV1.pushClmChargedEventData(request, CLMDataPushType.MEMBERSHIP_EVENT);
            } else {
                LOG.info("INVALID DATA IN REQUEST BODY :::::::::");
            }
        } catch (Exception e) {
            LOG.error("Error while pushing membership data ", e);
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = "push-wallet-data", consumes = MediaType.APPLICATION_JSON)
    public void pushWalletData(@RequestBody CLMOrderDataPushRequest request) {
        try {
            if (Objects.nonNull(request.getStartBusinessDate()) && Objects.nonNull(request.getBrandId())) {
                pushServiceV1.pushClmChargedEventData(request, CLMDataPushType.WALLET_EVENT);
            } else {
                LOG.info("INVALID DATA IN REQUEST BODY :::::::::");
            }
        } catch (Exception e) {
            LOG.error("Error while pushing wallet data ", e);
        }
    }
    
    @RequestMapping(method = RequestMethod.POST, value = "push-customer-profile", consumes = MediaType.APPLICATION_JSON)
    public String pushUserProfile(@RequestParam(required = true) Integer customerId,@RequestParam(required = false) Boolean lead) {
        try {
            if (Objects.nonNull(customerId)) {
            	CleverTapPushResponse response = cleverTapDataPushService.pushUserToCleverTap(customerId,lead);
            	if(response.getStatus().equals(CleverTapConstants.SUCCESS)) {
                	LOG.info("clevertap profile data push successfull for customer id {}",customerId);
            	}else {
                	LOG.info("clevertap profile data push unsuccessfull for customer id {}",customerId);
            	}
            	return response.getStatus();
            	} else {
                LOG.info("INVALID customer Id  :::::::::");
                return "Invalid Id";
            }
        } catch (Exception e) {
            LOG.error("Error while pushing user profile data ", e);
            return "Error while pushing user profile data ";
        }
    }

    @RequestMapping(method = RequestMethod.POST, value = "app-action", consumes = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    public void updateCustomerAppAction(@RequestBody(required = true) String payLoad) {
        try {
            JSONObject obj = new JSONObject(payLoad);
            int customerId = obj.getInt("id");
            String appAction = obj.getString("type");
            if(customerId>0) {
                LOG.info("Saving app action {} for customer {} ", appAction, customerId);
                if (appAction.equalsIgnoreCase(AppConstants.APP_INSTALLED)) {
                    cleverTapDataPushService.updateCustomerAppAction(customerId, AppConstants.APP_INSTALLED);
                } else if (appAction.equalsIgnoreCase(AppConstants.APP_UNINSTALLED)) {
                    cleverTapDataPushService.updateCustomerAppAction(customerId, AppConstants.APP_UNINSTALLED);
                } else {
                    LOG.info("Unknown app action {} for customer {} ", appAction, customerId);
                }
            }else{
                LOG.info("Invalid customerid {} ", customerId);
            }
        } catch (Exception e) {
            LOG.error("Error while updating customer data according to app event ", e);
        }
    }

}
