package com.stpl.tech.report.service.service.impl;

import com.stpl.tech.kettle.clevertap.service.CleverTapDataPushService;
import com.stpl.tech.kettle.clevertap.util.CleverTapConstants;
import com.stpl.tech.kettle.clevertap.util.CleverTapEvents;
import com.stpl.tech.kettle.core.LoyaltyEventType;
import com.stpl.tech.kettle.customer.dao.LoyaltyDao;
import com.stpl.tech.kettle.customer.service.CustomerEventService;
import com.stpl.tech.kettle.customer.service.CustomerService;
import com.stpl.tech.kettle.data.model.LoyaltyScore;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.offer.dao.OfferManagementDao;
import com.stpl.tech.master.core.external.offer.service.OfferManagementService;
import com.stpl.tech.master.data.model.CouponDetailData;
import com.stpl.tech.master.data.model.CouponDetailMappingData;
import com.stpl.tech.master.data.model.CustomerEventDetailData;
import com.stpl.tech.master.data.model.CustomerOfferMappingData;
import com.stpl.tech.master.domain.model.CouponCloneRequest;
import com.stpl.tech.master.domain.model.CouponData;
import com.stpl.tech.master.domain.model.IdentifierType;
import com.stpl.tech.master.util.CustomerEventType;
import com.stpl.tech.master.data.model.DeliveryCouponAllocationDetailData;
import com.stpl.tech.master.data.model.DeliveryCouponDetailData;
import com.stpl.tech.master.domain.model.CloneCouponData;
import com.stpl.tech.master.domain.model.CouponCloneResponse;
import com.stpl.tech.master.domain.model.DeliveryCouponStatus;
import com.stpl.tech.kettle.customer.dao.CustomerBirthdayDetailDao;
import com.stpl.tech.report.service.model.CouponMappingRequest;
import com.stpl.tech.report.service.model.DeliveryCouponResponse;
import com.stpl.tech.report.service.service.CouponService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;


@Service
public class CouponServiceImpl implements CouponService {

    private static final Logger LOG = LoggerFactory.getLogger(CouponServiceImpl.class);

    @Autowired
    private OfferManagementDao offerDao;

    @Autowired
    private OfferManagementService offerService;

    @Autowired
    private LoyaltyDao loyaltyDao;

    @Autowired
    private CustomerBirthdayDetailDao customerBirthdayDetailDao;

    @Autowired
    private CleverTapDataPushService cleverTapDataPushService;

    @Autowired
    private CustomerEventService customerEventService;

    @Autowired
    private CustomerService customerService;

    @Autowired
    private ApplicationContext applicationContext;

    private static final String BIRTHDAY_FREE_DESI_CHAI = "Free Desi Chai";

    private static final String BIRTHDAY_DISCOUNT = "20% off on MoV 999";

    private static final Long BUFFER_TIME = 10L;


    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public CouponCloneResponse generateCoupon(int validityDays, String contactNumber, String cloneCode, String prefix) {
        CouponCloneResponse couponCloneResponse = null;
        CouponDetailData clone = offerDao.getCoupon(cloneCode);
        CloneCouponData cloneCoupon = getCloneCouponData(validityDays, prefix, clone);
        List<String> contactNumbers = new ArrayList<>();
        contactNumbers.add(contactNumber);
        String startDay = AppUtils.getDateString(AppUtils.getCurrentDate(), AppConstants.DATE_FORMAT);
        try {
            couponCloneResponse = offerService.createCoupon(cloneCoupon, contactNumbers, startDay, null, null, clone);
            return couponCloneResponse;
        } catch (DataUpdationException e) {
            LOG.info("Coupon can not be Created for Customer with contact number : {}",contactNumber);
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    @Override
	public boolean validateLoyaltyPoint(Integer loyaltyPoint, LoyaltyScore loyaltyScore) {
		// LoyaltyScore loyaltyScore = loyaltyDao.getScore(customer.getId());
		if (Objects.nonNull(loyaltyScore)) {
			Integer currentLoyaltyScore = loyaltyScore.getAcquiredPoints();
			Date lastOrderdate = loyaltyScore.getLastOrderTime();
			Date currentDate = AppUtils.getCurrentDate();
            int daysDiff = 0;
            if(Objects.nonNull(lastOrderdate) && Objects.nonNull(currentDate)){
                daysDiff = AppUtils.getAbsDaysDiff(lastOrderdate, currentDate);
            }
			return currentLoyaltyScore >= loyaltyPoint && daysDiff > 180;
		}
		return false;
	}

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean validateLoyaltyPointForDelivery(Integer loyaltyPoint, LoyaltyScore loyaltyScore) {
        if (Objects.nonNull(loyaltyScore)) {
            Integer currentLoyaltyScore = loyaltyScore.getAcquiredPoints();
            return currentLoyaltyScore >= loyaltyPoint;
        }
        return false;
    }

    private CloneCouponData getCloneCouponData(int validityInDays, String prefix,
                                               CouponDetailData clone) {
        CloneCouponData data = new CloneCouponData();
        data.setCloneCouponCode(clone.getCouponCode());
        data.setOfferDetailId(clone.getOfferDetail().getOfferDetailId());
        data.setPrefix(prefix);
        data.setUsageCount(1);
        data.setMaxCustomerUsage(clone.getMaxCustomerUsage());
        data.setValidityInDays(validityInDays);
        data.setApplicableRegion(null);
        data.setApplicableUnitId(null);
        return data;
    }

	@Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
	@Override
	public boolean updateLoyalty(Integer loyaltyPoints, Integer customerId, String contactNumber,
			CouponCloneResponse couponDetail, LoyaltyScore loyaltyScore) {
		if (Objects.nonNull(loyaltyScore)) {
			loyaltyScore.setAcquiredPoints(loyaltyScore.getAcquiredPoints() - loyaltyPoints);
            loyaltyScore.setTotalRedeemedPoints(loyaltyScore.getTotalRedeemedPoints()!=null?
                    loyaltyScore.getTotalRedeemedPoints() +loyaltyPoints: 0+loyaltyPoints);
			loyaltyDao.createLoyaltyEvent(customerId, LoyaltyEventType.OFFER_PURCHASE, loyaltyPoints * (-1),
					couponDetail.getMappings().get(contactNumber).getCouponDetailId());
			loyaltyDao.update(loyaltyScore);
			return true;
		}
		return false;
	}

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean updateLoyaltyForDelivery(Integer loyaltyPoints, Integer customerId, String contactNumber,
                                 DeliveryCouponResponse couponDetail, LoyaltyScore loyaltyScore) {
        if (Objects.nonNull(loyaltyScore)) {
            loyaltyScore.setAcquiredPoints(loyaltyScore.getAcquiredPoints() - loyaltyPoints);
            loyaltyScore.setTotalRedeemedPoints(loyaltyScore.getTotalRedeemedPoints()!=null?
                    loyaltyScore.getTotalRedeemedPoints() +loyaltyPoints: 0+loyaltyPoints);
            loyaltyDao.createLoyaltyEvent(customerId, LoyaltyEventType.OFFER_PURCHASE, loyaltyPoints * (-1),-1);
            loyaltyDao.update(loyaltyScore);
            return true;
        }
        return false;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public DeliveryCouponResponse createDeliveryCouponLoyaltyBurn(String code, Integer brandId,Integer channelPartnerId, Boolean getClonedCoupon,
                                                                    Integer customerId,String contactNumber,Integer getValidityInDays){
        DeliveryCouponResponse result = new DeliveryCouponResponse();
        DeliveryCouponDetailData deliveryCoupon = getDeliveryCloneCode(code, brandId, channelPartnerId,false);
        if(Objects.nonNull(deliveryCoupon)) {
            Integer couponDelay = 0;
            Date startDate = AppUtils.getNextDate(AppUtils.addDays(AppUtils.getCurrentTimestamp(), couponDelay - 1));
            if (AppUtils.isBefore(startDate, deliveryCoupon.getStartDate())) {
                startDate = deliveryCoupon.getStartDate();
            }
            Date endDate = AppUtils.getDayBeforeOrAfterDay(startDate, getValidityInDays- 1);
            if (AppUtils.isBefore(deliveryCoupon.getEndDate(), endDate)) {
                endDate = deliveryCoupon.getEndDate();
            }
            createDeiveryLoyaltyBurnOffer(deliveryCoupon,customerId,contactNumber,-1,-1,startDate,endDate);
            result.setCouponCode(deliveryCoupon.getCouponCode());
            result.setMasterCoupon(deliveryCoupon.getMasterCoupon());
            result.setValidityInDays(deliveryCoupon.getValidityInDays());
            result.setStartDate(AppUtils.getDateString(startDate));
            result.setEndDate(AppUtils.getDateString(endDate));
        }
        return result;
    }


    public DeliveryCouponDetailData getDeliveryCloneCode(String code, Integer brandId,Integer channelPartnerId ,Boolean getClonedCoupon){
        DeliveryCouponDetailData data;
        if(getClonedCoupon){
            data = offerDao.getDeliveryCoupon(code, brandId,channelPartnerId);
            if(Objects.nonNull(data)){
                data.setDeliveryCouponStatus(DeliveryCouponStatus.PROCESSING.name());
                offerDao.update(data);
            }
        }else{
            data = offerDao.getDeliveryMasterCoupon(code, brandId,channelPartnerId);
        }
        return data;
    }

    public 	void createDeiveryLoyaltyBurnOffer(DeliveryCouponDetailData deliveryCoupon, Integer customerId, String contactNumber,
                                                 Integer campaignId, int orderId,Date startDate,Date endDate){
        updateAllocationOfLoyaltyBurnCoupon(deliveryCoupon, customerId, contactNumber, -1, -1,startDate,endDate);
    }

    public void updateAllocationOfLoyaltyBurnCoupon(DeliveryCouponDetailData deliveryCoupon, Integer customerId, String contactNumber, Integer campaignId, int orderId,Date startDate,Date endDate) {
        LOG.info("Update coupon detail after allocation for Coupon id : {}",deliveryCoupon.getDeliveryCouponId());
        DeliveryCouponDetailData couponDetailData = offerDao.find(DeliveryCouponDetailData.class, deliveryCoupon.getDeliveryCouponId());
        Date allotmentTime = AppUtils.getCurrentTimestamp();
        couponDetailData.setNoOfAllocations(couponDetailData.getNoOfAllocations()+1);
        couponDetailData.setLastAllocationTime(allotmentTime);
        couponDetailData.setDeliveryCouponStatus(DeliveryCouponStatus.AVAILABLE.name());
        if(couponDetailData.getNoOfAllocations().equals(couponDetailData.getMaxNoOfDistributions())){
            couponDetailData.setIsExhausted(AppConstants.getValue(true));
            couponDetailData.setDeliveryCouponStatus(DeliveryCouponStatus.ALLOTTED.name());
        }
        DeliveryCouponAllocationDetailData allocationData = new DeliveryCouponAllocationDetailData();
        allocationData.setDeliveryCouponId(couponDetailData.getDeliveryCouponId());
        allocationData.setCustomerId(customerId);
        allocationData.setAllotmentTime(allotmentTime);
        allocationData.setCampaignId(campaignId);
        allocationData.setContactNumber(contactNumber);
        allocationData.setAllotmentOrderId(orderId);
        allocationData.setStartDate(startDate);
        allocationData.setEndDate(endDate);
        offerDao.add(allocationData);
        offerDao.update(couponDetailData);
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public String getDeliveryCouponCode(String contactNumber){
        String result = null;
        DeliveryCouponAllocationDetailData data = offerDao.getDeliveryCouponId(contactNumber);
        if(Objects.nonNull(data)){
            DeliveryCouponDetailData detailData = offerDao.getDeliveryCouponCode(data.getDeliveryCouponId());
            if(Objects.nonNull(detailData)){
                result = detailData.getCouponCode();
                return result;
            }
            else{
                LOG.info("Error in Getting Delivery Coupon Detail data :{}",data);
                return result;
            }
        }
        else{
            LOG.info("Error in Getting Delivery Coupon Allocation Detail data :{}",data);
            return result;
        }

    }
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Scheduled(cron = "0 0 07 1 * *", zone = "GMT+05:30")
    public boolean generateCouponsForCustomerEventMonth() {
        Integer currentMonth = AppUtils.getCurrentMonth();
        Integer validityInDays = AppUtils.getDaysInMonth(currentMonth);
        try {
            List<CustomerEventType> customerEventTypes = Arrays.asList(CustomerEventType.values());
            for (CustomerEventType customerEventType : customerEventTypes) {
                List<Integer> offerIds = customerEventService.getCustomerEventOfferIds(customerEventType);
                String cloneCoupon1 = offerDao.getCouponByOfferId(offerIds.get(0));
                String cloneCoupon2 = offerDao.getCouponByOfferId(offerIds.get(1));

                List<CustomerEventDetailData> customerEventDetailDataList = customerBirthdayDetailDao.findByEventMonthAndEventType(currentMonth, customerEventType);
                if ((Objects.nonNull(customerEventDetailDataList) || !customerEventDetailDataList.isEmpty())) {
                    for (CustomerEventDetailData data : customerEventDetailDataList) {
                        if(Objects.nonNull(data.getCouponGenerationAt()) && AppUtils.getCurrentYear().equals(AppUtils.getYear(data.getCouponGenerationAt()))){
                            continue;
                        }
                        Map<String, Object> userProfiles = new HashMap<>();
                        Map<String, Object> eventData = new HashMap<>();
                        String coupon1 = generateCoupon(validityInDays, data.getContactNumber(), cloneCoupon1, customerEventType.getCouponPrefix()).getMappings().get(data.getContactNumber()).getCoupon();
                        String coupon2 = generateCoupon(validityInDays, data.getContactNumber(), cloneCoupon2, customerEventType.getCouponPrefix()).getMappings().get(data.getContactNumber()).getCoupon();

                        eventData.put("Customer" + customerEventType.toString() + "Month", data.getEventMonth());
                        eventData.put("Customer" + customerEventType.toString() + "Date", data.getEventDate());
                        eventData.put("Customer" + customerEventType.toString() + "Offer1", coupon1);
                        eventData.put("Customer" + customerEventType.toString() + "Offer2", coupon2);
                        eventData.put("Customer"+customerEventType.toString()+"Offer1text",BIRTHDAY_FREE_DESI_CHAI);
                        eventData.put("Customer"+customerEventType.toString()+"Offer2text",BIRTHDAY_DISCOUNT);

                        userProfiles.put("Customer" + customerEventType.toString() + "Offer1", coupon1);
                        userProfiles.put("Customer" + customerEventType.toString() + "Offer2", coupon2);
                        userProfiles.put("Customer" + customerEventType.toString() + "Offer1text", BIRTHDAY_FREE_DESI_CHAI);
                        userProfiles.put("Customer" + customerEventType.toString() + "Offer2text", BIRTHDAY_DISCOUNT);


                        data.setCouponGenerationAt(AppUtils.getCurrentTimestamp());
                        data.setEventOffer1(coupon1);
                        data.setEventOffer2(coupon2);
                        customerBirthdayDetailDao.update(data);
                        try {
                            cleverTapDataPushService.uploadProfileAttributes(data.getCustomerId(), AppUtils.getCurrentTimestamp().toInstant().getEpochSecond(), CleverTapConstants.REGULAR, userProfiles);
                            if (customerEventType.equals(CustomerEventType.Birthday)) {
                                cleverTapDataPushService.publishCustomEvent(data.getCustomerId(), CleverTapEvents.BIRTHDAY_MONTH, AppUtils.getCurrentTimestamp().toInstant().getEpochSecond() + BUFFER_TIME, CleverTapConstants.REGULAR, eventData);

                            } else if (customerEventType.equals(CustomerEventType.Anniversary)) {
                                cleverTapDataPushService.publishCustomEvent(data.getCustomerId(), CleverTapEvents.ANNIVERSARY_MONTH, AppUtils.getCurrentTimestamp().toInstant().getEpochSecond() + BUFFER_TIME, CleverTapConstants.REGULAR, eventData);
                            } else {
                                cleverTapDataPushService.publishCustomEvent(data.getCustomerId(), customerEventType.toString().concat("_Month"), AppUtils.getCurrentTimestamp().toInstant().getEpochSecond() + BUFFER_TIME, CleverTapConstants.REGULAR, eventData);
                            }
                        } catch (Exception e) {
                            LOG.info("Error in Pushing Event to clever tap");
                            return false;
                        }
                    }
                }
            }
        } catch (Exception e) {
            LOG.info("Error in generating Coupons ",e);
            return false;

        }
        return true;
    }
//    @Override
//    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
//    public String getBirthdayOfferCouponCode(String contactNumber,Integer offerNumber){
//        contactNumber = AppUtils.getValidContactNUmber(contactNumber);
//        List<CouponDetailMappingData> mappingDataList = offerDao.getCouponMappingsForContactNumber(contactNumber);
//        List<Integer> couponIds = new ArrayList<>();
//        CouponDetailData result = null;
//        if(Objects.nonNull(mappingDataList) && !mappingDataList.isEmpty()){
//            for(CouponDetailMappingData data : mappingDataList){
//                couponIds.add(data.getCouponDetail());
//            }
//        }
//        if(!couponIds.isEmpty()){
//            if(offerNumber==1){
//                result = offerDao.getCouponByCouponIdAndOfferId(couponIds,3909);
//            } else if (offerNumber == 2) {
//                result = offerDao.getCouponByCouponIdAndOfferId(couponIds,3908);
//            }
//            if(Objects.nonNull(result)){
//                return result.getCouponCode();
//            }
//        }
//        return null;
//    }
    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public String getCustomerEventOfferCouponCode(String contactNumber,Integer offerNumber,CustomerEventType customerEventType){
        CustomerEventDetailData customerEventDetailData = customerBirthdayDetailDao.findByContactNumberAndEventType(contactNumber,customerEventType);
        if(offerNumber == 1){
            return customerEventDetailData.getEventOffer1();
        }
        else if(offerNumber == 2){
            return customerEventDetailData.getEventOffer2();
        }
        return null;
    }



    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean publishCustomerEventForEventMonth(CustomerEventType customerEventType){
        try {
            Integer currentMonth = AppUtils.getCurrentMonth();
            List<CustomerEventDetailData> customerBirthdayDetailDataList = customerBirthdayDetailDao.findByEventMonthAndEventType(currentMonth,customerEventType);
            for(CustomerEventDetailData data : customerBirthdayDetailDataList){
                Map<String, Object> userProfiles = new HashMap<>();
                Map<String, Object> eventData = new HashMap<>();

                eventData.put("Customer"+customerEventType.toString()+"Month", data.getEventMonth());
                eventData.put("Customer"+customerEventType.toString()+"Date",data.getEventDate());
                eventData.put("Customer"+customerEventType.toString()+"Offer1", data.getEventOffer2());
                eventData.put("Customer"+customerEventType.toString()+"Offer1text",BIRTHDAY_FREE_DESI_CHAI);
                eventData.put("Customer"+customerEventType.toString()+"Offer2",data.getEventOffer2());
                eventData.put("Customer"+customerEventType.toString()+"Offer2text",BIRTHDAY_DISCOUNT);

                userProfiles.put("Customer"+customerEventType.toString()+"Offer1", data.getEventOffer1());
                userProfiles.put("Customer"+customerEventType.toString()+"Offer1text",BIRTHDAY_FREE_DESI_CHAI);
                userProfiles.put("Customer"+customerEventType.toString()+"Offer2",data.getEventOffer2());
                userProfiles.put("Customer"+customerEventType.toString()+"Offer2text",BIRTHDAY_DISCOUNT);

                try{
                    cleverTapDataPushService.uploadProfileAttributes(data.getCustomerId(),AppUtils.getCurrentTimestamp().toInstant().getEpochSecond()
                            , CleverTapConstants.REGULAR,userProfiles);
                    if(customerEventType.equals(CustomerEventType.Birthday)) {
                        cleverTapDataPushService.publishCustomEvent(data.getCustomerId(), CleverTapEvents.BIRTHDAY_MONTH,
                                AppUtils.getCurrentTimestamp().toInstant().getEpochSecond() + BUFFER_TIME, CleverTapConstants.REGULAR, eventData);
                    }
                    else if(customerEventType.equals(CustomerEventType.Anniversary)){
                        cleverTapDataPushService.publishCustomEvent(data.getCustomerId(), CleverTapEvents.ANNIVERSARY_MONTH,
                                AppUtils.getCurrentTimestamp().toInstant().getEpochSecond() + BUFFER_TIME, CleverTapConstants.REGULAR, eventData);

                    }
                    else{
                        cleverTapDataPushService.publishCustomEvent(data.getCustomerId(), customerEventType.toString().concat("_Month"),
                                AppUtils.getCurrentTimestamp().toInstant().getEpochSecond()+BUFFER_TIME, CleverTapConstants.REGULAR, eventData);

                    }
                }catch (Exception e){
                    LOG.info("Error in Pushing Event to clever tap");
                }
            }
            return true;
        }catch (Exception e){
            LOG.info("Error in Publishing Birthday month data and error is : {}",e.getMessage());
        }
        return false;
    }

    // This function create only to publish March Month data
    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean publishEventForBirthdayDataForCurrentMonth(Integer birthdayMonth){
        try {
            List<CustomerEventDetailData> customerBirthdayDetailDataList = customerBirthdayDetailDao.findByEventMonthAndEventType(birthdayMonth,CustomerEventType.Birthday);
            for(CustomerEventDetailData data : customerBirthdayDetailDataList){
                String offer1 = getCustomerEventOfferCouponCode(data.getContactNumber(),1,CustomerEventType.Birthday);
                String offer2 = getCustomerEventOfferCouponCode(data.getContactNumber(),2, CustomerEventType.Birthday);
                Map<String, Object> userProfiles = new HashMap<>();
                Map<String, Object> eventData = new HashMap<>();
                eventData.put("CustomerBirthdayMonth", data.getEventMonth());
                eventData.put("CustomerBirthdayDate",data.getEventDate());
                if(!StringUtils.isEmpty(offer1)){
                    eventData.put("CustomerBirthdayOffer1", offer1);
                    eventData.put("CustomerBirthdayOffer1text",BIRTHDAY_FREE_DESI_CHAI);
                    userProfiles.put("CustomerBirthdayOffer1", offer1);
                    userProfiles.put("CustomerBirthdayOffer1text",BIRTHDAY_FREE_DESI_CHAI);
                }
                if(!StringUtils.isEmpty(offer2)){
                    eventData.put("CustomerBirthdayOffer2",offer2);
                    eventData.put("CustomerBirthdayOffer2text",BIRTHDAY_DISCOUNT);
                    userProfiles.put("CustomerBirthdayOffer2",offer2);
                    userProfiles.put("CustomerBirthdayOffer2text",BIRTHDAY_DISCOUNT);
                }
                try{
                    cleverTapDataPushService.uploadProfileAttributes(data.getCustomerId(),AppUtils.getCurrentTimestamp().toInstant().getEpochSecond()
                            , CleverTapConstants.REGULAR,userProfiles);
                    cleverTapDataPushService.publishCustomEvent(data.getCustomerId(), CleverTapEvents.BIRTHDAY_MONTH,
                            AppUtils.getCurrentTimestamp().toInstant().getEpochSecond()+BUFFER_TIME, CleverTapConstants.REGULAR, eventData);
                }catch (Exception e){
                    LOG.info("Error in Pushing Event to clever tap");
                }
            }
        }catch (Exception e){
            LOG.info("Error in publishing Birthday Data and Error is : {}",e.getMessage());
        }
        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public CouponCloneResponse dynamicCouponMapping(CouponMappingRequest request){
        CouponCloneResponse couponCloneResponse = null;
        if(Objects.nonNull(request)){
            if(AppConstants.YES.equals(request.getIsClone())){
                couponCloneResponse = cloneAndMapCouponToCustomer(request.getContactNumber(),request.getContactNumbers(),request.getValidityInDays(),request.getMasterCode(),request.getPrefix());
            }else{
                couponCloneResponse = mapCouponToCustomer(request.getContactNumber(),request.getContactNumbers(),request.getMasterCode());
            }
            if(Objects.nonNull(couponCloneResponse)){
                for( Map.Entry<String, CouponData> entry : couponCloneResponse.getMappings().entrySet()) {
                    Integer customerId = customerService.getCustomerId(entry.getKey());
                    Map<String,Object> data = new HashMap<>();
                    data.put("couponCode",entry.getValue().getCoupon());
                    data.put("couponEndDate",entry.getValue().getEndDate());
                    data.put("couponStartDate",entry.getValue().getStartDate());
                    data.put("couponValidityInDays",request.getValidityInDays());
                    data.put("couponDescription",couponCloneResponse.getDescription());
                    try {
                        cleverTapDataPushService.publishCustomEvent(customerId, CleverTapEvents.DYNAMIC_COUPON_GENERATION,
                                AppUtils.getCurrentTimestamp().toInstant().getEpochSecond(), CleverTapConstants.REGULAR, data);
                    } catch (Exception e) {
                        LOG.info("Error in pushing data to cleverTap with exception : {}", e);
                    }
                }
                return couponCloneResponse;
            }
        }
        return couponCloneResponse;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "MasterDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public CouponCloneResponse mapCouponToCustomer(String contactNumber,List<String> contactNumbersList,String code){
        List<String> contactNumbers = new ArrayList<>();
        if(Objects.isNull(contactNumbersList)){
            contactNumbers = new ArrayList<>();
            contactNumbers.add(AppUtils.getValidContactNUmber(contactNumber));
        }else{
            for(String num : contactNumbersList){
                contactNumbers.add(AppUtils.getValidContactNUmber(num));
            }
        }
        CouponCloneResponse cloneResponse = null;
        CouponDetailData clone = offerDao.getCoupon(code);
        if(clone == null){
            return null;
        }
        cloneResponse = new CouponCloneResponse();
        CouponCloneRequest request = new CouponCloneRequest();
        request.setIdentifierType(IdentifierType.CONTACT_NUMBER);
        request.setCode(code);
        request.getIdentifier().addAll(contactNumbers);
        cloneResponse.setCode(code);
        cloneResponse.setIdentifier(IdentifierType.CONTACT_NUMBER);
        try {
            offerService.mapCoupon(request, cloneResponse);
            return cloneResponse;
        }catch (Exception e){
            LOG.info("Coupon : {} can not be Mapped for Customer with contact number : {}",code,contactNumber);
        }
        return null;
    }

    private CouponCloneResponse cloneAndMapCouponToCustomer(String contactNumber,List<String> contactNumbersList,int validityDays,String masterCode,String prefix){
        List<String> contactNumbers = new ArrayList<>();
        if(Objects.isNull(contactNumbersList)){
            contactNumbers = new ArrayList<>();
            contactNumbers.add(AppUtils.getValidContactNUmber(contactNumber));
        }else{
            for(String num : contactNumbersList){
                contactNumbers.add(AppUtils.getValidContactNUmber(num));
            }
        }
        CouponCloneResponse couponCloneResponse = null;
        CouponDetailData clone = offerDao.getCoupon(masterCode);
        CloneCouponData cloneCoupon = getCloneCouponData(validityDays, prefix, clone);
        String startDay = AppUtils.getDateString(AppUtils.getCurrentDate(), AppConstants.DATE_FORMAT);
        try {
            couponCloneResponse = offerService.createCoupon(cloneCoupon, contactNumbers, startDay, null, null, clone);
            return couponCloneResponse;
        } catch (DataUpdationException e) {
            LOG.info("Coupon can not be Created for Customer with contact number : {}",contactNumber);
        }
        return null;
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    @Override
    public CouponCloneResponse generateCouponForCampaign(int validityDays, String contactNumber, String cloneCode, String prefix,
                                                         String acqSrc) {
        CouponCloneResponse couponCloneResponse = null;
        CouponDetailData clone = offerDao.getCoupon(cloneCode);
        List<CustomerOfferMappingData> list = offerDao.existCustomerOfferMappingDataList(clone.getOfferDetail().getOfferDetailId(),contactNumber);
        if(Objects.nonNull(list)){
            List<Integer> couponIds = new ArrayList<>();
            for(CustomerOfferMappingData l : list){
                couponIds.add(l.getCouponDetailId());
            }
            CouponDetailData couponDetailData = offerDao.getCouponByCouponDetailId(couponIds);
            if(Objects.nonNull(couponDetailData)) {
                LOG.info("Customer already have coupon");
                return new CouponCloneResponse();
            }
        }
        CloneCouponData cloneCoupon = getCloneCouponData(validityDays, prefix, clone);
        List<String> contactNumbers = new ArrayList<>();
        contactNumbers.add(contactNumber);
        String startDay = AppUtils.getDateString(AppUtils.getCurrentDate(), AppConstants.DATE_FORMAT);
        try {
            couponCloneResponse = offerService.createCoupon(cloneCoupon, contactNumbers, startDay, null, null, clone);
            offerService.addCustomerOfferMappingData(couponCloneResponse,contactNumber,acqSrc);
            return couponCloneResponse;
        } catch (DataUpdationException e) {
            LOG.info("Coupon can not be Created for Customer with contact number : {}",contactNumber);
        }
        return null;
    }

    @Override
    public void pushEventToCleverTap(CouponCloneResponse response,String eventName,String contactNumber,Integer customerId){
        try {
            Map<String, Object> data = new HashMap<>();
            data.put("couponCode", response.getMappings().get(contactNumber).getCoupon());
            data.put("couponDescription", response.getDescription());
            data.put("couponEndDate", response.getMappings().get(contactNumber).getEndDate());
            ThreadPoolTaskExecutor executor = (ThreadPoolTaskExecutor) applicationContext.getBean("taskExecutor");
            executor.execute(() -> {
                try {
                    cleverTapDataPushService.publishCustomEvent(customerId, eventName,
                            AppUtils.getCurrentTimestamp().toInstant().getEpochSecond(), CleverTapConstants.REGULAR, data);
                } catch (Exception e) {
                    LOG.info("Error in pushing data to cleverTap with exception : {}", e);
                }
            });
        }catch (Exception e){
            LOG.info("Error in creating data for cleverTap event for contact number : {} and error is : {}",contactNumber,e);
        }
    }

}
