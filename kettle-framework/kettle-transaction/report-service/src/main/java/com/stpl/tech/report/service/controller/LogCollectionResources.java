/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.report.service.controller;

import static com.stpl.tech.report.service.core.ReportServiceConstants.API_VERSION;
import static com.stpl.tech.report.service.core.ReportServiceConstants.LOG_COLLECTION_ROOT_CONTEXT;
import static com.stpl.tech.report.service.core.ReportServiceConstants.SEPARATOR;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;
import javax.ws.rs.core.MediaType;

import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.ResponseStatus;

import com.google.common.collect.HashMultimap;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.service.TemperatureLoggerService;
import com.stpl.tech.master.core.service.AbstractResources;
import com.stpl.tech.master.data.model.TemperatureLogDetail;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.report.service.core.ReportProperties;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.notification.slack.Slack;

@Controller
@RequestMapping(value = API_VERSION + SEPARATOR + LOG_COLLECTION_ROOT_CONTEXT)
public class LogCollectionResources extends AbstractResources {

    private static final Logger LOG = LoggerFactory.getLogger(LogCollectionResources.class);
    @Autowired
    private TemperatureLoggerService tempLoggerService;

    @Autowired
    private MasterDataCache masterCache;

    @Autowired
    private ReportProperties props;


    private static final BigDecimal TEMPERATURE_THRESHOLD_VALUE = new BigDecimal(10.0d);
    private static final BigDecimal KITCHEN_TEMPERATURE_THRESHOLD_VALUE = new BigDecimal(6.0d);
    private static final BigDecimal KITCHEN_CR_TEMPERATURE_THRESHOLD_VALUE = new BigDecimal(0.0d);
    private static final BigDecimal TEMPERATURE_VALID_MAX_VALUE = new BigDecimal(200.0d);
    private static final BigDecimal AC_TEMPERATURE_THRESHOLD_VALUE = new BigDecimal(24.0d);
    private static final long LAST_FIVE_MINUTES_BREACH_NOTIFICATION = 10l; //10 min
    private static final Integer LAST_TEN_MINUTES_RESEND_NOTIFICATION = 15 * 60; // 15 min to sec
    private static final Integer LAST_TEN_MINUTES_LOG_TIME = LAST_TEN_MINUTES_RESEND_NOTIFICATION + 60; // 16 min to sec

    @RequestMapping(method = RequestMethod.GET, value = "temperature", produces = MediaType.APPLICATION_JSON)
    @ResponseStatus(HttpStatus.OK)
    @ResponseBody
    public boolean logTemperature(HttpServletRequest request, @RequestParam final Integer unitId,
                                  @RequestParam final String macAddress, @RequestParam final String locationType,
                                  @RequestParam final String locationId, @RequestParam final String deviceName,
                                  @RequestParam final String deviceLocation, @RequestParam final BigDecimal temperature) throws IOException {
        TemperatureLogDetail log = new TemperatureLogDetail();
		UnitBasicDetail ubd = masterCache.getUnitBasicDetail(unitId);
		BigDecimal thresholdValue = getThresholdValue(ubd, deviceName);
        log.setUnitId(unitId);
        log.setDeviceLocation(deviceLocation);
        log.setDeviceName(deviceName);
        log.setIpAddress(request.getRemoteAddr());
        log.setIsNotified(AppConstants.NO);
        log.setLocationId(locationId);
        log.setLocationType(locationType);
        log.setMacAddress(macAddress);
        log.setLogTime(AppUtils.getCurrentTimestamp());
        log.setLogDate(AppUtils.getBusinessDate());
        log.setTemperatureThresholdValue(thresholdValue);
        log.setTemperatureValue(temperature);
        if (temperature.compareTo(TEMPERATURE_VALID_MAX_VALUE) >= 0) {
            log.setIsValid(AppConstants.NO);
            log.setBreachDetected(AppConstants.NO);
        } else {
            log.setIsValid(AppConstants.YES);
            if (temperature.compareTo(thresholdValue) >= 0) {
                log.setBreachDetected(AppConstants.YES);
            } else {
                log.setBreachDetected(AppConstants.NO);
            }
        }
        TemperatureLogDetail finalLog = tempLoggerService.addLog(log);
        boolean logsSuccess = finalLog != null;
          /* boolean breachDetected = AppConstants.getValue(log.getBreachDetected());
            if (logsSuccess && breachDetected) {
                boolean notified = sendNotification(finalLog);
                tempLoggerService.markAsNotified(finalLog.getId(), notified);
            }
           */
        return logsSuccess;
    }


	private BigDecimal getThresholdValue(UnitBasicDetail ubd, String deviceName) {
        if(Objects.nonNull(deviceName) && deviceName.toLowerCase().equals("ac")){
            return AC_TEMPERATURE_THRESHOLD_VALUE;
        }
		return ubd.getCategory().equals(UnitCategory.CAFE) ? TEMPERATURE_THRESHOLD_VALUE
				: (deviceName.toLowerCase().startsWith("cr")) ? KITCHEN_CR_TEMPERATURE_THRESHOLD_VALUE
						: KITCHEN_TEMPERATURE_THRESHOLD_VALUE;
	}


	@Scheduled(cron = "0 0/5 * * * ?", zone = "GMT+05:30")
    public void sendNotificationsSchedule() {
        //last minute - 14 minutes minutes start time
        //last minute end time
        Date endTime = AppUtils.getCurrentTimestamp();
        Date startTime = AppUtils.getDateBeforeOrAfterInSeconds(endTime, LAST_TEN_MINUTES_LOG_TIME * -1);

        HashMultimap<String, TemperatureLogDetail> notifications =  HashMultimap.create();
        List<TemperatureLogDetail> logs = tempLoggerService.findAll(startTime, endTime);
        LOG.info(" list is {}", logs);
        Map<TemperatureLoggerKey, List<TemperatureLogDetail>> logMap = new HashMap<>();
        for (TemperatureLogDetail loger : logs) {
        	TemperatureLoggerKey key = getKey(loger);
            if (!logMap.containsKey(key)) {
                logMap.put(key, new ArrayList<>());
            }
            logMap.get(key).add(loger);
        }
        Iterator<Map.Entry<TemperatureLoggerKey, List<TemperatureLogDetail>>> itr = logMap.entrySet().iterator();
        while (itr.hasNext()) {
            Map.Entry<TemperatureLoggerKey, List<TemperatureLogDetail>> entry = itr.next();
            TemperatureLogDetail detail = getNotification(entry.getKey(), entry.getValue());
			if (detail != null) {
				notifications.put(detail.getLocationId(), detail);
			}
        }
        if(notifications != null && notifications.size() > 0) {
        	for(String locationId : notifications.keySet()) {
        		sendNotification(locationId, notifications.get(locationId));
        	}
        }

    }

    private TemperatureLoggerKey getKey(TemperatureLogDetail loger) {
		return new TemperatureLoggerKey(loger.getLocationId(),loger.getDeviceName(), loger.getDeviceLocation());
	}


	private TemperatureLogDetail getNotification(TemperatureLoggerKey deviceLocation, List<TemperatureLogDetail> logs) {
		Date currentTime = AppUtils.getCurrentTimestamp();
		TemperatureLogDetail breachInDevice = checkIfBreached(logs, deviceLocation);
		if (breachInDevice != null) {
			Date lastNotificationTime = getLastNotificationTime(logs);
			if (lastNotificationTime == null || AppUtils.getSecondsDiff(currentTime,
					lastNotificationTime) >= LAST_TEN_MINUTES_RESEND_NOTIFICATION) {
				LOG.info("found breach to be notified {} ",  deviceLocation);
				return breachInDevice;
			}
			return null;
		}
		return null;
	}

	private boolean sendNotification(String locationId, Set<TemperatureLogDetail> logs) {
		boolean notified = sendSlackNotification(locationId, logs);
		LOG.info("publish slack is   " + notified);
		for(TemperatureLogDetail log : logs) {
			tempLoggerService.markAsNotified(log.getId(), notified);
		}
		return notified;
	}


   /* private boolean sendNotification(TemperatureLogDetail finalLog) {
        Date currentTime = AppUtils.getCurrentTimestamp();
        List<TemperatureLogDetail> logs = tempLoggerService.getTemperatureDetail(finalLog.getLocationId(),
            finalLog.getDeviceName(),
            AppUtils.getDateBeforeOrAfterInSeconds(currentTime, LAST_TEN_MINUTES_LOG_TIME * -1));
        if (logs == null || logs.size() <= 1) {
            return false;
        }
        boolean breachInDevice = checkIfBreached(logs, finalLog.getDeviceLocation());
        if (breachInDevice) {
            Date lastNotificationTime =  getLastNotificationTime(logs);
            if (lastNotificationTime == null || AppUtils.getSecondsDiff(currentTime,
                lastNotificationTime) >= LAST_TEN_MINUTES_RESEND_NOTIFICATION) {
                return sendSlackNotification(finalLog);
            }
            return false;
        }
        return false;
    }
    */

	private boolean sendSlackNotification(String locationId, Set<TemperatureLogDetail> logs) {
		if (logs == null || logs.size() == 0) {
			LOG.info("NO Slack Notification for location id {} ", locationId);
			return false;
		}
		LOG.info("publishing Slack Notification for location id {} and breach count {}", locationId, logs.size());
		try {
			StringBuffer buffer = new StringBuffer();
			int count = 0;
			TemperatureLogDetail finalLog = null;
			for (TemperatureLogDetail log : logs) {
				if (count == 0) {
					finalLog = log;
					buffer.append(String.format(" LocationType : *%s*\n LocationId : *%s*\n", log.getLocationType(),
							log.getLocationId()));
				}
				buffer.append(
						String.format("DeviceName : *%s*\n DeviceLocation : *%s*\n  TemperatureValue : *%s*\n\n\n  ",
								log.getDeviceName(), log.getDeviceLocation(), log.getTemperatureValue()));
				count++;
			}
			String message = buffer.toString();
			UnitBasicDetail ubd = masterCache.getUnitBasicDetail(finalLog.getUnitId());
			EmployeeBasicDetail e1 = masterCache.getEmployeeBasicDetail(ubd.getCafeManagerId());
			EmployeeBasicDetail e2 = masterCache.getEmployeeBasicDetail(ubd.getUnitManagerId());
			// cafeManager
			if (e1 != null && e1.getSlackChannel() != null && e1.getSlackChannel().trim().length() > 0) {
				SlackNotificationService.getInstance().sendNotification(props.getEnvironmentType(), "Temperature Notification",
						e1.getSlackChannel(), null, message);
			}
			// unitManager
			if (e2 != null && e2.getSlackChannel() != null && e2.getSlackChannel().trim().length() > 0) {
				SlackNotificationService.getInstance().sendNotification(props.getEnvironmentType(), "Temperature Notification",
						e2.getSlackChannel(), null, message);
			}
			// CAFE
			if (ubd.getCategory().equals(UnitCategory.CAFE)) {
				SlackNotificationService.getInstance().sendNotification(props.getEnvironmentType(), "Temperature Notification", null,
						SlackNotification.CAFE_TEMPERATURE_BREACH.getChannel(props.getEnvironmentType()), message);
			} else {
				SlackNotificationService.getInstance().sendNotification(props.getEnvironmentType(), "Temperature Notification", null,
						SlackNotification.OTHER_TEMPERATURE_BREACH.getChannel(props.getEnvironmentType()), message);
			}
			return true;
		} catch (Exception e) {
			LOG.error("Error while publishing slack", e);
		}
		return false;
	}

    private Date getLastNotificationTime(List<TemperatureLogDetail> logs) {
        LOG.info("check if notification sent in last " + LAST_TEN_MINUTES_RESEND_NOTIFICATION);
        Date minimumNotifyTime = null;
        for (TemperatureLogDetail log : logs) {
            if (log.getIsNotified().equals(AppConstants.YES)) {
                if (minimumNotifyTime != null) {
                    minimumNotifyTime = minDate(minimumNotifyTime, log.getNotificationTime());
                } else {
                    minimumNotifyTime = log.getNotificationTime();
                }
            }
        }
        return minimumNotifyTime;
    }

    public static Date minDate(Date date1, Date date2) {
        return date1.before(date2) ? date1 : date2;
    }

    private TemperatureLogDetail checkIfBreached(List<TemperatureLogDetail> logs, TemperatureLoggerKey deviceLocation) {
        LOG.info("Check if continuousBreach or Not");
        boolean continuousBreach = false;
        TemperatureLogDetail temp = null;
        boolean firstBreachTime = true;
        Date firstTime = null;
        for (TemperatureLogDetail log : logs) {
            if (log.getBreachDetected().equals(AppConstants.YES)) {
                continuousBreach = true;
                if (firstBreachTime) {
                    temp = log;
                    firstTime = log.getLogTime();
                    firstBreachTime = false;
                }
            } else {
                continuousBreach = false;
                firstBreachTime = true;
            }
            if(firstTime!=null) {
                long isDifference = firstTime.getTime() - log.getLogTime().getTime();
                isDifference = isDifference / (60 * 1000) % 60;
                LOG.info("difference in time is  " + isDifference);
                if (isDifference >= LAST_FIVE_MINUTES_BREACH_NOTIFICATION && continuousBreach) {
                    return temp;
                }
            }
        }
        return null;
    }

}