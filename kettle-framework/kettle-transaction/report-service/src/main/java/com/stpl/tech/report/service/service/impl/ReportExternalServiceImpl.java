package com.stpl.tech.report.service.service.impl;

import com.google.gson.Gson;
import com.stpl.tech.kettle.clevertap.converter.CleverTapConverter;
import com.stpl.tech.kettle.clevertap.data.model.EventPushTrack;
import com.stpl.tech.kettle.clevertap.domain.model.CleverTapPushResponse;
import com.stpl.tech.kettle.clevertap.domain.model.ClevertapChargedEventData;
import com.stpl.tech.kettle.clevertap.domain.model.EventUploadRequest;
import com.stpl.tech.kettle.clevertap.service.CleverTapDataPushService;
import com.stpl.tech.kettle.clevertap.util.CleverTapConstants;
import com.stpl.tech.kettle.clevertap.util.CleverTapEvents;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.dao.CustomerSearchDao;
import com.stpl.tech.kettle.data.dao.EventPushTrackDao;
import com.stpl.tech.kettle.data.dao.OrderDetailDao;
import com.stpl.tech.kettle.data.dao.OrderNotificationDao;
import com.stpl.tech.kettle.data.dao.impl.OrderComplaintDetailDao;
import com.stpl.tech.kettle.data.model.CustomerInfo;
import com.stpl.tech.kettle.data.model.GamifiedOfferDetail;
import com.stpl.tech.kettle.data.model.OrderComplaintDetailData;
import com.stpl.tech.kettle.data.model.OrderDetail;
import com.stpl.tech.kettle.data.model.OrderItem;
import com.stpl.tech.kettle.data.model.OrderNotification;
import com.stpl.tech.kettle.domain.model.OrderDetailEventRequest;
import com.stpl.tech.kettle.domain.model.OrderItemReviewModal;
import com.stpl.tech.master.core.WebServiceHelper;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.notification.BCXOrderDetailTemplate;
import com.stpl.tech.master.notification.OrderDetailEmailNotification;
import com.stpl.tech.report.service.Entites.KettleAnalytics.SwiggyOrsRaw;
import com.stpl.tech.report.service.Entites.KettleAnalytics.SwiggyRejectionRaw;
import com.stpl.tech.report.service.core.ReportProperties;
import com.stpl.tech.report.service.dao.OrderSearchDao;
import com.stpl.tech.report.service.dao.SwiggyOrsRawDao;
import com.stpl.tech.report.service.model.CleverTapEndpoints;
import com.stpl.tech.report.service.service.ReportExternalService;
import com.stpl.tech.report.service.util.AbstractRestTemplateCLM;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

@Service
public class ReportExternalServiceImpl implements ReportExternalService {

    private static final Logger LOG = LoggerFactory.getLogger(ReportExternalServiceImpl.class);
    @Autowired
    private OrderSearchDao orderSearchDao;
    @Autowired
    private EnvironmentProperties props;
    @Autowired
    private OrderComplaintDetailDao orderComplaintDetailDao;
    @Autowired
    private CleverTapDataPushService cleverTapDataPushService;

    @Autowired
    private OrderNotificationDao orderNotificationDao;
    @Autowired
    private CustomerSearchDao customerSearchDao;

    @Autowired
    private MasterDataCache masterDataCache;

    @Autowired
    private OrderDetailDao orderDetailDao;

    @Autowired
    private CleverTapConverter cleverTapConverter;

    @Autowired
    private SwiggyOrsRawDao swiggyOrsRawDao;

    private static String NEW_LINE = " \\n ";

    private static String GET_ORDER_DETAIL = "get-edited-order-original-request";

    @Autowired
    private ReportProperties env;

    @Autowired
    private EventPushTrackDao eventPushTrackDao;



    public Map<String,String> getOrderOriginalDetailFromPartner(String partnerOrderId){
        try {
            Map<String,String> params = new HashMap<>();
            params.put("partnerOrderId",partnerOrderId);
            Map<String,String> res = WebServiceHelper.postRequestWithParamAndAuthInternal(props.getChannelPartnerBaseUrl()+ AppConstants.PARTNER_MANAGEMENT_ENDPOINT+GET_ORDER_DETAIL,props.getPartnerToken(),
                    params, Map.class,false);
            if(Objects.isNull(res) || res.isEmpty()){
                return null;
            }
            return res;
        }catch (Exception e){
            LOG.info("Exception in fetching fallback detail for order with id : {} and exception is : {}",partnerOrderId,e.getMessage());
            return null;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public boolean publishPartnerOrderDetail(OrderDetailEventRequest req) {
            try {
                OrderComplaintDetailData orderComplaintDetailData = orderComplaintDetailDao.findByOrderSourceId(req.getPartnerOrderId().toString());
                if (Objects.nonNull(orderComplaintDetailData)) {
                    return false;
                }
                Map<String, Object> res = orderSearchDao.getPartnerOrderDetail(req.getPartnerOrderId(), req.getIssueType());
                Map<String, String> originalOrderDetail = getOrderOriginalDetailFromPartner(req.getPartnerOrderId());
                if (Objects.nonNull(res)) {
                    res.put("COMPLAINT_TIME", req.getComplaintTime());
                    res.put("COMPLAINT_ITEMS", req.getComplaintItem());
                    res.put("COMPLAINT_TYPE", req.getType());
                    res.put("PREVIOUS_ITEMS", req.getPreviousItems());
                    res.put("MODIFIED_ITEMS", req.getModifiedItems());
                    res.put("FALLBACK_ERRORS", null);
                    if (Objects.nonNull(originalOrderDetail)) {
                        String previousItems = "";
                        if (originalOrderDetail.containsKey("items") && !StringUtils.isEmpty(originalOrderDetail.get("items"))) {
                            previousItems = previousItems + originalOrderDetail.get("items");
                            if (originalOrderDetail.containsKey("modifiers") && !StringUtils.isEmpty(originalOrderDetail.get("modifiers"))) {
                                previousItems = previousItems + " , " + originalOrderDetail.get("modifiers");
                            }
                            res.put("PREVIOUS_ITEMS", previousItems);
                            res.put("MODIFIED_ITEMS", res.get("ITEMS"));
                        }
                        if (originalOrderDetail.containsKey("errors") && !StringUtils.isEmpty(originalOrderDetail.get("errors"))) {
                            res.put("FALLBACK_ERRORS", originalOrderDetail.get("errors"));
                        }
                    }
                    List<String> toEmails = new ArrayList<>(Arrays.asList("<EMAIL>", "<EMAIL>"));
                    try {
                        BCXOrderDetailTemplate bcxOrderDetailTemplate = new BCXOrderDetailTemplate(res, req.getPartnerOrderId(), req.getType(), props.getEnvironmentType(), props.getBasePath());
                        OrderDetailEmailNotification orderDetailEmailNotification = new OrderDetailEmailNotification(res, req.getPartnerOrderId(), req.getType(), toEmails, props.getEnvironmentType(), bcxOrderDetailTemplate);
                        orderDetailEmailNotification.sendEmail();
                    } catch (Exception e) {
                        LOG.error("Error in sending Email : {}", e);
                    }
                    try {
                        if (AppUtils.isProd(props.getEnvironmentType()) && req.isPublishToFamePilot()) {
                            LOG.info("Calling FamePilot Api");
                            publishToFamePilot(res, req.getPartnerOrderId());
                        }
                    } catch (Exception e) {
                        LOG.error("Error in publishing Data to FamePilot : {}", e);
                    }
                    try {
                        saveOrderComplaintData(res);
                    } catch (Exception e) {
                        LOG.error("Error in saving complaint Data : {}", e);
                    }
                    return true;
                }
            } catch (Exception e) {
                LOG.error("Fail To publish Partner Order Detail ", e);
                return false;
            }
        return false;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public String getPartnerOrderDetail(String partnerSourceId, String issueType){
        try {
            Map<String, Object> res = orderSearchDao.getPartnerOrderDetail(partnerSourceId, issueType);
            Map<String,String> originalOrderDetail = getOrderOriginalDetailFromPartner(partnerSourceId);
            OrderComplaintDetailData orderComplaintDetailData = orderComplaintDetailDao.findByOrderSourceId(partnerSourceId);
            if(Objects.nonNull(orderComplaintDetailData) && !StringUtils.isEmpty(orderComplaintDetailData.getIssueType())){
                issueType = orderComplaintDetailData.getIssueType();
            }else{
                issueType = null;
            }
            if (Objects.nonNull(res)) {
                res.put("PREVIOUS_ITEMS",null);
                res.put("MODIFIED_ITEMS",null);
                res.put("FALLBACK_ERRORS",null);
                if(Objects.nonNull(originalOrderDetail)){
                    String previousItems = "";
                    if(originalOrderDetail.containsKey("items") && !StringUtils.isEmpty(originalOrderDetail.get("items"))){
                        previousItems = previousItems + originalOrderDetail.get("items");
                        if(originalOrderDetail.containsKey("modifiers") && !StringUtils.isEmpty(originalOrderDetail.get("modifiers"))){
                            previousItems = previousItems + " , " + originalOrderDetail.get("modifiers");
                        }
                        res.put("PREVIOUS_ITEMS",previousItems);
                        res.put("MODIFIED_ITEMS",res.get("ITEMS"));
                    }
                    if(originalOrderDetail.containsKey("errors") && !StringUtils.isEmpty(originalOrderDetail.get("errors"))){
                        res.put("FALLBACK_ERRORS",originalOrderDetail.get("errors"));
                    }
                }
                return getOrderDetailText(res, partnerSourceId, issueType);
            }
        }catch (Exception e){
            LOG.error("Error in fetching OrderDetail : {}",e);
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public Map<String,Object> fetchPartnerOrderDetail(String partnerSourceId,String issueType){
        try {
            Map<String, Object> res = orderSearchDao.getPartnerOrderDetail(partnerSourceId, issueType);
            return res;
        }catch (Exception e){
            LOG.error("Error in fetching OrderDetail Object : {}",e);
        }
        return null;
    }

    private boolean publishToFamePilot(Map<String,Object> map,String partnerOrderId) throws IOException {
        String url = props.getFamePilotUrlForFeedbackEvent();
        List<NameValuePair> famePilotObject = getObjectForFamePilot(map,partnerOrderId);
        try{
            WebServiceHelper.postRequestForFamePilot(url,famePilotObject);
            return true;
        }catch (Exception e){
            LOG.error("Error in calling FamePilot API : {}",e);
        }
        return false;
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public List<NameValuePair> getObjectForFamePilot(Map<String,Object> map,String partnerSourceId) throws IOException {
        List<NameValuePair> formData = new ArrayList<>();
        try {
            formData.add(new BasicNameValuePair("business_uuid", map.get("BUSINESS_UID").toString()));
            formData.add(new BasicNameValuePair("branch", map.get("BRANCH_ID").toString()));
            formData.add(new BasicNameValuePair("reviewer_name", map.get("CUSTOMER_NAME").toString()));
            formData.add(new BasicNameValuePair("contact_number", "+************"));
            formData.add(new BasicNameValuePair("email", "NA"));
            formData.add(new BasicNameValuePair("review_time", AppUtils.getCurrentDateTime()));
            formData.add(new BasicNameValuePair("rating", "1"));
            formData.add(new BasicNameValuePair("review_text", getOrderDetailText(map,partnerSourceId,"NA")));
            formData.add(new BasicNameValuePair("review_tags", getItemsTags(map.get("ITEMS").toString())));
            formData.add(new BasicNameValuePair("order_id", partnerSourceId));
            formData.add(new BasicNameValuePair("service_type","delivery"));
            formData.add(new BasicNameValuePair("order_items",getOrderItemsText(map)));
            return formData;
        }catch (Exception e){
            LOG.info("Error in creating Object For FamePilot: {}",e);
            return null;
        }
    }

    public String getItemsTags(String allItemsString){
        String resultString = "";
        String[] allItems = allItemsString.split(",");
        for(String s : allItems){
            if(StringUtils.isEmpty(resultString)){
                resultString = resultString + s.split("X")[0].trim();
            }else{
                resultString = resultString + "," + s.split("X")[0].trim();
            }
        }
        return resultString;
    }

    private String getOrderItemsText(Map<String,Object> res){
        List<OrderItemReviewModal> list = new ArrayList<>();
        try {
            String allItemsString = String.valueOf(res.get("ITEMS"));
            String[] allItems = allItemsString.split(",");
            String complaintItemsString = "";
            if (res.containsKey("COMPLAINT_ITEMS") && Objects.nonNull(res.get("COMPLAINT_ITEMS"))) {
                complaintItemsString = String.valueOf(res.get("COMPLAINT_ITEMS"));
            }
            if (StringUtils.isEmpty(complaintItemsString)) {
                for (String s : allItems) {
                    OrderItemReviewModal o = new OrderItemReviewModal();
                    o.setItemName(s.split("X")[0].trim());
                    o.setQuantity(Integer.valueOf(s.split("X")[1].trim()));
                    list.add(o);
                }
            } else {
                for (String s : allItems) {
                    OrderItemReviewModal o = new OrderItemReviewModal();
                    if (complaintItemsString.contains(s.split("X")[0].trim())) {
                        o.setItemName(s.split("X")[0].trim());
                        o.setQuantity(Integer.valueOf(s.split("X")[1].trim()));
                        list.add(o);
                    }else{
                        o.setItemName(s.split("X")[0].trim());
                        o.setQuantity(Integer.valueOf(s.split("X")[1].trim()));
                        list.add(o);
                    }
                }
            }
            if (!CollectionUtils.isEmpty(list)) {
                String resultString = "[";
                for (OrderItemReviewModal o : list) {
                    if (resultString.equals("[")) {
                        resultString = resultString + o.toString();
                    } else {
                        resultString = resultString + "," + o.toString();
                    }
                }
                resultString = resultString + "]";
                return resultString;
            }
        }catch (Exception e){
            LOG.error("Error in sending orderItems to famePilot : {}",e);
        }
        return "[]";

    }

    private String getOrderDetailText(Map<String,Object> res,String partnerSourceId, String issueType){
        StringBuilder sb = new StringBuilder();
        sb.append("Hi "+res.get("UNIT_NAME")+","+NEW_LINE);
        if(res.containsKey("COMPLAINT_TYPE")) {
            sb.append("We have received a " + res.get("COMPLAINT_TYPE") + " from " + res.get("PARTNER_CODE") + " for " + res.get("BRAND_NAME") + NEW_LINE);
        }else {
            sb.append("We have received a "+ "ORS from " + res.get("PARTNER_CODE") + " for " + res.get("BRAND_NAME") + NEW_LINE);
        }
        sb.append("Below are the details"+NEW_LINE);
        if(!StringUtils.isEmpty(issueType)) {
            sb.append("Issue Type : " + issueType + NEW_LINE);
        }
        sb.append("Order Id : "+res.get("GENERATED_ORDER_ID")+NEW_LINE);
        if(res.containsKey("ORDER_SOURCE_ID") && Objects.nonNull(res.get("ORDER_SOURCE_ID"))) {
            sb.append("Partner Order Id : " + res.get("ORDER_SOURCE_ID") + NEW_LINE);
        }
        sb.append("Customer Name : "+res.get("CUSTOMER_NAME")+NEW_LINE);
        sb.append("Order Time : "+res.get("BILLING_SERVER_TIME")+NEW_LINE);
        if(Objects.nonNull(res.get("READY_TO_DISPATCH")) && Objects.nonNull(res.get("PREPERATION_TIME"))) {
            sb.append("Processing Time : " + res.get("READY_TO_DISPATCH") + " (" + res.get("PREPERATION_TIME") + " mins )" + NEW_LINE);
        } else if (Objects.nonNull(res.get("READY_TO_DISPATCH"))) {
            sb.append("Processing Time : " + res.get("READY_TO_DISPATCH")+NEW_LINE);
        }
        if(Objects.nonNull(res.get("RIDER_ASSIGNED_AT")) && Objects.nonNull(res.get("RIDER_ASSIGNED_TIME"))) {
            sb.append("Rider Assigned At : "+ res.get("RIDER_ASSIGNED_AT")+ " ("+res.get("RIDER_ASSIGNED_TIME")+" mins )"+NEW_LINE);
        } else if (Objects.nonNull(res.get("READY_TO_DISPATCH"))) {
            sb.append("Rider Assigned At : "+ res.get("RIDER_ASSIGNED_AT")+NEW_LINE);
        }
        if(Objects.nonNull(res.get("RIDER_ARRIVED_AT")) && Objects.nonNull(res.get("RIDER_ARRIVED_TIME"))) {
            sb.append("Rider Arrived At : "+ res.get("RIDER_ARRIVED_AT")+ " ("+res.get("RIDER_ARRIVED_TIME")+" mins)"+NEW_LINE);
        } else if (Objects.nonNull(res.get("RIDER_ARRIVED_AT"))) {
            sb.append("Rider Arrived At : "+ res.get("RIDER_ARRIVED_AT")+NEW_LINE);
        }
        if(Objects.nonNull(res.get("ORDER_PICKUP_TIME")) && Objects.nonNull(res.get("RIDER_PICKED_TIME"))) {
            sb.append("Pickup Time : "+res.get("ORDER_PICKUP_TIME")+" ("+res.get("RIDER_PICKED_TIME")+" mins )"+NEW_LINE);
        } else if (Objects.nonNull(res.get("ORDER_PICKUP_TIME"))) {
            sb.append("Pickup Time : "+res.get("ORDER_PICKUP_TIME")+NEW_LINE);
        }
        if(Objects.nonNull(res.get("ORDER_DELIVERY_TIME")) && Objects.nonNull(res.get("RIDER_DELIVERY_TIME"))) {
            sb.append("Delivery Time : "+res.get("ORDER_DELIVERY_TIME")+" ("+res.get("RIDER_DELIVERY_TIME")+" mins )"+NEW_LINE);
        } else if (Objects.nonNull(res.get("ORDER_DELIVERY_TIME"))) {
            sb.append("Delivery Time : "+res.get("ORDER_DELIVERY_TIME")+NEW_LINE);
        }
        if(Objects.nonNull(res.get("RIDER_NAME")) && Objects.nonNull(res.get("RIDER_CONTACT"))) {
            sb.append("Rider Details : "+res.get("RIDER_NAME")+" ("+res.get("RIDER_CONTACT")+")"+NEW_LINE);
        } else if (Objects.nonNull(res.get("RIDER_NAME"))) {
            sb.append("Rider Details : "+res.get("RIDER_NAME")+NEW_LINE);
        }
        if(res.containsKey("COMPLAINT_TIME") && Objects.nonNull(res.get("COMPLAINT_TIME"))) {
            sb.append("Customer Complaint Time : " + res.get("COMPLAINT_TIME") + NEW_LINE);
        }
        if(res.containsKey("COMPLAINT_ITEMS") && Objects.nonNull(res.get("COMPLAINT_ITEMS"))) {
            sb.append("Complaint Items : " + res.get("COMPLAINT_ITEMS") + NEW_LINE);
        }
        if(res.containsKey("PREVIOUS_ITEMS") && Objects.nonNull(res.get("PREVIOUS_ITEMS"))) {
            sb.append("Previous Items : " + res.get("PREVIOUS_ITEMS") + NEW_LINE);
        }
        if(res.containsKey("MODIFIED_ITEMS") && Objects.nonNull(res.get("MODIFIED_ITEMS"))) {
            sb.append("Modified Items : " + res.get("MODIFIED_ITEMS") + NEW_LINE);
        }
        if(res.containsKey("FALLBACK_ERRORS") && !StringUtils.isEmpty(Objects.toString(res.get("FALLBACK_ERRORS"),""))){
            sb.append("Fallback Errors : " + res.get("FALLBACK_ERRORS") + NEW_LINE);
        }
        sb.append("All Items : "+ res.get("ITEMS")+NEW_LINE);
        sb.append("Order Amount : "+res.get("TOTAL_AMOUNT")+NEW_LINE);
        sb.append("DAM : "+res.get("CAFE_MANAGER")+ " (" + res.get("CAFE_MANAGER_CONTACT")+ ")" +NEW_LINE);
        sb.append("AM : "+res.get("UNIT_MANAGER")+ " (" + res.get("UNIT_MANAGER_CONTACT")+ ")"+ NEW_LINE);
        sb.append("Cafe Manager : "+res.get("UNIT_CAFE_MANAGER")+NEW_LINE);
        sb.append("Please Look Into This asap" + NEW_LINE +
                "Thanks" + NEW_LINE +
                "BCX Management Team"+NEW_LINE);
        return sb.toString();
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    private void saveOrderComplaintData(Map<String,Object> map){
        try {
            OrderComplaintDetailData orderComplaintDetailData = orderComplaintDetailDao.findByOrderSourceId(map.get("ORDER_SOURCE_ID").toString());
            if(Objects.nonNull(orderComplaintDetailData)){
                return;
            }
            orderComplaintDetailData = new OrderComplaintDetailData();
            orderComplaintDetailData.setUnitName(Objects.toString(map.get("UNIT_NAME"),""));
            Objects.toString(map.get("UNIT_ID"),"");
            orderComplaintDetailData.setUnitId(Integer.parseInt(Objects.toString(map.get("UNIT_ID"),"")));
            orderComplaintDetailData.setUnitRegion(Objects.toString(map.get("UNIT_REGION"),""));
            orderComplaintDetailData.setUnitEmail(Objects.toString(map.get("UNIT_EMAIL"),""));
            orderComplaintDetailData.setUnitManager(Objects.toString(map.get("UNIT_MANAGER"),""));
            orderComplaintDetailData.setUnitManagerId(Objects.toString(map.get("UNIT_MANAGER_ID"),""));
            orderComplaintDetailData.setUnitManagerContact(Objects.toString(map.get("UNIT_MANAGER_CONTACT"),""));
            orderComplaintDetailData.setUnitCafeManager(Objects.toString(map.get("UNIT_CAFE_MANAGER"),""));
            orderComplaintDetailData.setIssueTime((Date) map.get("ISSUE_TIME"));
            orderComplaintDetailData.setBillingServerTime((Date) map.get("BILLING_SERVER_TIME"));
            orderComplaintDetailData.setReadyToDispatch((Date) map.get("READY_TO_DISPATCH"));
            orderComplaintDetailData.setRiderAssignedAt((Date) map.get("RIDER_ASSIGNED_AT"));
            orderComplaintDetailData.setRiderArrivedAt((Date) map.get("RIDER_ARRIVED_AT"));
            orderComplaintDetailData.setOrderPickupTime((Date) map.get("ORDER_PICKUP_TIME"));
            orderComplaintDetailData.setOrderDeliveryTime((Date) map.get("ORDER_DELIVERY_TIME"));
            if(Objects.nonNull(map.get("PREPERATION_TIME")) && StringUtils.isBlank(map.get("PREPERATION_TIME").toString())) {
                orderComplaintDetailData.setPreperationTime(Long.parseLong(map.get("PREPERATION_TIME").toString()));
            }
            if(Objects.nonNull(map.get("RIDER_ARRIVED_TIME")) && StringUtils.isBlank(map.get("RIDER_ARRIVED_TIME").toString())) {
                orderComplaintDetailData.setRiderArrivedTime(Long.parseLong(map.get("RIDER_ARRIVED_TIME").toString()));
            }
            if(Objects.nonNull(map.get("RIDER_ASSIGNED_TIME")) && StringUtils.isBlank(map.get("RIDER_ASSIGNED_TIME").toString())) {
                orderComplaintDetailData.setRiderAssignedTime(Long.parseLong(map.get("RIDER_ASSIGNED_TIME").toString()));
            }
            if(Objects.nonNull(map.get("RIDER_PICKED_TIME")) && StringUtils.isBlank(map.get("RIDER_PICKED_TIME").toString())) {
                orderComplaintDetailData.setRiderPickedTime(Long.parseLong(Objects.toString(map.get("RIDER_PICKED_TIME"), "")));
            }
            if(Objects.nonNull(map.get("RIDER_DELIVERY_TIME")) && StringUtils.isBlank(map.get("RIDER_DELIVERY_TIME").toString())) {
                orderComplaintDetailData.setRiderDeliveryTime(Long.parseLong(map.get("RIDER_DELIVERY_TIME").toString()));
            }
            if(Objects.nonNull(map.get("TOTAL_DELIVERY_TIME")) && StringUtils.isBlank(map.get("TOTAL_DELIVERY_TIME").toString())) {
                orderComplaintDetailData.setTotalDeliveryTime(Long.parseLong(map.get("TOTAL_DELIVERY_TIME").toString()));
            }
            orderComplaintDetailData.setRiderLateArrived(Objects.toString(map.get("RIDER_LATE_ARRIVED"),""));
            orderComplaintDetailData.setOrderLateDelivered(Objects.toString(map.get("ORDER_LATE_DELIVERED"),""));
            orderComplaintDetailData.setFoodPreparedLate(Objects.toString(map.get("FOOD_PREPARED_LATE"),""));
            orderComplaintDetailData.setBrandName(Objects.toString(map.get("BRAND_NAME"),""));
            orderComplaintDetailData.setBranchId(Integer.parseInt(Objects.toString(map.get("BRANCH_ID"),"")));
            orderComplaintDetailData.setBusinessUid(Objects.toString(map.get("BUSINESS_UID"),""));
            orderComplaintDetailData.setCafeManager(Objects.toString(map.get("CAFE_MANAGER"),""));
            orderComplaintDetailData.setCafeManagerId(Objects.toString(map.get("CAFE_MANAGER_ID"),""));
            orderComplaintDetailData.setCafeManagerContact(Objects.toString(map.get("CAFE_MANAGER_CONTACT"), ""));
            orderComplaintDetailData.setOrderSourceId(Objects.toString(map.get("ORDER_SOURCE_ID"), ""));
            orderComplaintDetailData.setOrderId(Integer.parseInt(Objects.toString(map.get("ORDER_ID"), "")));
            orderComplaintDetailData.setGeneratedOrderId(Objects.toString(map.get("GENERATED_ORDER_ID"), ""));
            orderComplaintDetailData.setCustomerName(Objects.toString(map.get("CUSTOMER_NAME"), ""));
            orderComplaintDetailData.setPartnerCode(Objects.toString(map.get("PARTNER_CODE"), ""));
            orderComplaintDetailData.setItems(Objects.toString(map.get("ITEMS"), ""));
            orderComplaintDetailData.setItemNames(Objects.toString(map.get("ITEM_NAMES"), ""));
            orderComplaintDetailData.setRevenue((BigDecimal) map.get("REVENUE"));
            orderComplaintDetailData.setTotalAmount((BigDecimal) map.get("TOTAL_AMOUNT"));
            orderComplaintDetailData.setDiscount((BigDecimal) map.get("DISCOUNT"));
            orderComplaintDetailData.setRiderName(Objects.toString(map.get("RIDER_NAME"), ""));
            orderComplaintDetailData.setRiderContact(Objects.toString(map.get("RIDER_CONTACT"), ""));
            orderComplaintDetailData.setCustomerComplaint(Objects.toString(map.get("CUTSOMER_COMPLAINT"), ""));
            orderComplaintDetailData.setIssueType(Objects.toString(map.get("ISSUE_TYPE"), ""));
            orderComplaintDetailData.setComplaintItems(Objects.toString(map.get("COMPLAINT_ITEMS"), ""));
            orderComplaintDetailData.setComplaintTime(Objects.toString(map.get("COMPLAINT_TIME"), ""));
            orderComplaintDetailData.setPreviousItems(Objects.toString(map.get("PREVIOUS_ITEMS"), ""));
            orderComplaintDetailData.setModifiedItems(Objects.toString(map.get("MODIFIED_ITEMS"), ""));
            orderComplaintDetailData.setComplaintType(Objects.toString(map.get("COMPLAINT_TYPE"), ""));
            orderComplaintDetailData.setFallbackErrors(Objects.toString(map.get("FALLBACK_ERRORS"),""));
            orderComplaintDetailDao.save(orderComplaintDetailData);
        }catch (Exception e){
            LOG.info("Error in save Customer complaint Data for order source Id : {} and Error is e : {}",
                    map.get("ORDER_SOURCE_ID").toString(),e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public String getCustomerDetail(String partnerSourceId){
        try {
            Map<String, Object> res = orderSearchDao.getCustomerDetail(partnerSourceId);
            if (Objects.nonNull(res)) {
                return getCustomerDetailText(res);
            }
        }catch (Exception e){
            LOG.error("Error in fetching Customer detail : {}",e);
        }
        return null;
    }

    private String getCustomerDetailText(Map<String,Object> res){
        StringBuilder sb = new StringBuilder();

        sb.append("Order Id : "+ res.get("ORDER_ID")+NEW_LINE);
        if(Objects.nonNull(res.get("ORDER_SOURCE"))) {
            sb.append("Order Source : " + res.get("ORDER_SOURCE") + NEW_LINE);
        }
        sb.append("Customer Id : "+ res.get("CUSTOMER_ID")+NEW_LINE);
        sb.append("Contact Number : "+ res.get("CONTACT_NUMBER")+NEW_LINE);
        sb.append("First Name : "+ res.get("FIRST_NAME")+NEW_LINE);
        sb.append("Is Number Verified : "+ res.get("IS_NUMBER_VERIFIED")+NEW_LINE);

        return sb.toString();
    }

    public String getFallbackOrderCustomerDetail(String partnerCustomerId){
        try {
            Map<String, Object> res = orderSearchDao.getFallbackOrderCustomerDetail(partnerCustomerId);
            if (Objects.nonNull(res)) {
                return getCustomerDetailText(res);
            }
        }catch (Exception e){
            LOG.error("Error in fetching Customer detail : {}",e);
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = true, propagation = Propagation.REQUIRED)
    public boolean publishUnlockEvent(String offerSource,String utmSource,Date recordTime){
        try {
            List<GamifiedOfferDetail> offerList = orderSearchDao.getGamifiedOfferDetailDataList(offerSource, utmSource, recordTime);
            if (!CollectionUtils.isEmpty(offerList)) {
                for(GamifiedOfferDetail detail : offerList){
                    try{
                        Map<String,Object> eventData = new HashMap<>();
                        cleverTapDataPushService.publishCustomEvent(detail.getCustomerId(), CleverTapEvents.UNLOCK_DEAL,
                                detail.getRecordTime().toInstant().getEpochSecond(), CleverTapConstants.REGULAR, eventData);
                    }catch (Exception e){
                        LOG.info("Error in Pushing Event to clever tap for CustomerId : {}",detail.getCustomerId(),e.getMessage());
                    }
                }
                return true;
            }
        }catch (Exception e){
            LOG.info("Error during pushing event to cleverTap",e.getMessage());
        }
        return false;
    }


    @Override
    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void pushChargedEventForDate(String startDate,String endDate){
        try {
            LOG.info("######### ---- Starting publishing charged event data to Clevertap  ---- ############");
            LOG.info("##### startDate : {} and enDate : {}",AppUtils.parseDate(startDate),AppUtils.parseDate(endDate));
            List<EventPushTrack> eventPushTrackList = eventPushTrackDao.findByStatusInAndEventNameAndU(AppUtils.parseDate(startDate),AppUtils.parseDate(endDate),
                    CleverTapConstants.PUSHED_TO_QUEUE, CleverTapEvents.CHARGED);
            LOG.info("######## ---- Size of Event Track ---- {} #######",eventPushTrackList.size());
            List<Integer> orderIdsToPush = new ArrayList<>();
            for(EventPushTrack et : eventPushTrackList){
                orderIdsToPush.add(et.getOrderId());
            }
            LOG.info("##### size Order Ids to push : {} ####",orderIdsToPush.size());
            List<OrderDetail> orderDetailList = orderDetailDao.
                    findByBusinessDateAndCustomerIdNotInAndOrderSourceAndBrandIdAndOrderStatusNotIn(AppUtils.parseDate(startDate),AppUtils.parseDate(endDate),
                            AppConstants.EXCLUDE_CUSTOMER_IDS,AppConstants.CAFE,AppConstants.CHAAYOS_BRAND_ID,Arrays.asList("CANCELLED","CANCELLED_REQUESTED"),orderIdsToPush);
            LOG.info("######## ---- Size of Order List ---- {} #######",orderDetailList.size());
            if(!CollectionUtils.isEmpty(orderDetailList)) {
                Integer pageSize = Math.min(1000, orderDetailList.size());
                Integer startIndex = 0;
                while (true) {
                    List<OrderDetail> pageData = new ArrayList<>();
                    for (int i = startIndex; i < pageSize + startIndex && i < orderDetailList.size(); i++) {
                        pageData.add(orderDetailList.get(i));
                    }
                    LOG.info("StartIndex is : {} and pageData size is : {}",startIndex,pageData.size());
                    if (pageData.size() <= 0) {
                        break;
                    }
                    pageSize = Math.min(pageData.size(), 1000);
                    List<Integer> orderIdsList = new ArrayList<>();
                    List<OrderNotification> orderNotificationsList = new ArrayList<>();
                    Map<Integer,OrderNotification> orderNotificationMap = new HashMap<>();
                    Map<Integer,Integer> orderToCustomerIdMap = new HashMap<>();
                    List<Integer> customerIdsList = new ArrayList<>();
                    List<CustomerInfo> customerInfoList = new ArrayList<>();
                    Map<Integer,CustomerInfo> customerInfoMap = new HashMap<>();
                    for (OrderDetail o : pageData) {
                        orderIdsList.add(o.getOrderId());
                        orderToCustomerIdMap.put(o.getOrderId(),o.getCustomerId());
                        customerIdsList.add(o.getCustomerId());
                    }
                    try {
                        orderNotificationsList = orderNotificationDao.findByOrderIdIn(orderIdsList);
                        if(!CollectionUtils.isEmpty(orderNotificationsList)){
                            for(OrderNotification o : orderNotificationsList){
                                orderNotificationMap.put(o.getOrderId(),o);
                            }
                        }
                    }catch (Exception e){
                        LOG.info("Error in fetching OrderNotification List :{}",e.getMessage());
                    }
                    try {
                        customerInfoList = customerSearchDao.findByCustomerIdIn(customerIdsList);
                        for(CustomerInfo c : customerInfoList){
                            customerInfoMap.put(c.getCustomerId(),c);
                        }
                    }catch (Exception e){
                        LOG.info("Error in fetching Customer Data ",e.getMessage());
                    }
                    pushEventDataToCleverTap(pageData,orderIdsList,orderNotificationMap,orderToCustomerIdMap,customerInfoMap);
                    startIndex = startIndex + pageSize;
                }
            }else{
                LOG.info("Order detail list is empty");
            }
        }catch (Exception e){
            LOG.error("Error in pushing charged event data to cleverTap for a date range between : {} and : {} and {}",startDate,endDate,e.getMessage());
        }
    }

    @Transactional(rollbackFor = Exception.class, value = "TransactionDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    private void pushEventDataToCleverTap(List<OrderDetail> orderDetailList,List<Integer> orderIds,
                                          Map<Integer,OrderNotification> orderNotificationMap,
                                          Map<Integer,Integer> orderToCustomerIdMap,Map<Integer,CustomerInfo> customerInfoMap){
        List<EventUploadRequest> requestList = new ArrayList<>();
        for(OrderDetail order : orderDetailList){
            String evtName = getOrderType(order);
            ClevertapChargedEventData evt = new ClevertapChargedEventData();
            CustomerInfo customer = customerInfoMap.get(orderToCustomerIdMap.get(order.getOrderId()));
            try {
                if (evtName.equals(CleverTapEvents.CHARGED)) {
                    evt = cleverTapConverter.convertChargedEventOrderData(order, customer,
                            createOrderNotificationDOmain(orderNotificationMap.get(order.getOrderId())));
                } else if (evtName.equalsIgnoreCase(CleverTapEvents.SUBSCRIPTION_PURCHASED_EVENT)) {
                    evt = cleverTapConverter.convertSubscriptionEventOrderData(order, customer,
                            createOrderNotificationDOmain(orderNotificationMap.get(order.getOrderId())));
                } else if (evtName.equalsIgnoreCase(CleverTapEvents.WALLET_PURCHASED_EVENT)) {
                    evt = cleverTapConverter.convertWalletEventOrderData(order, customer,
                            createOrderNotificationDOmain(orderNotificationMap.get(order.getOrderId())));
                }
                if(AppConstants.YES.equals(evt.getIsNewCustomer())) {
                    EventUploadRequest request = cleverTapConverter.convert(order, evtName, evt);
                    requestList.add(request);
                }
            }catch (Exception e){
                LOG.info("Exception occured while converting clevertap event request ", e);
            }
        }
        if (!CollectionUtils.isEmpty(requestList)){
            pushToCleverTap(requestList);
        }

    }

    private void pushToCleverTap(Object payload){
        CleverTapPushResponse cleverTapPushResponse = new CleverTapPushResponse();
        if (Objects.nonNull(payload)){
            Map<String, Object> uploadRequest = new HashMap<>();
            if (payload instanceof List) {
                uploadRequest.put("d", payload);
            } else {
                uploadRequest.put("d", Arrays.asList(payload));
            }
            Pair<String, String> cleverTapAcc = getCleverTapAccByBrand(1);
            try {
                cleverTapPushResponse = AbstractRestTemplateCLM.postWithHeaders(CleverTapEndpoints.UPLOAD_PROFILE_OR_EVENT,
                        uploadRequest, CleverTapPushResponse.class, cleverTapAcc.getKey(), cleverTapAcc.getValue(), env.getEnvironmentType());
                LOG.info("Response Status from cleverTap is : {}",cleverTapPushResponse.getStatus());
                LOG.info("Response from cleverTap is : {}",new Gson().toJson(cleverTapPushResponse).toString());
            }catch (Exception e){
                LOG.info("Error in pushing data to cleverTap using API :{}",e);
            }
        }

    }

    private Pair<String, String> getCleverTapAccByBrand(Integer brandId) {
        if (brandId.equals(AppConstants.CHAAYOS_BRAND_ID)) {
            return new Pair<>(env.getChaayosClevertapAccount(), env.getChaayosClevertapPasscode());
        } else if (brandId.equals(AppConstants.GNT_BRAND_ID)) {
            return new Pair<>(env.getGntClevertapAccount(), env.getGntClevertapPasscode());
        } else {
            LOG.info("CLEVER_TAP-USER-PROFILE-PUSH :: invalid brand id found Brand id : {}", brandId);
            return null;
        }
    }



    private String getOrderType(OrderDetail order) {
        for (OrderItem item : order.getOrderItems()) {
            if (Objects.nonNull(masterDataCache.getSubscriptionProductDetail(item.getProductId()))) {
                return CleverTapEvents.SUBSCRIPTION_PURCHASED_EVENT;
            } else if (AppUtils.getStatus(order.getGiftCardOrder())) {
                return CleverTapEvents.WALLET_PURCHASED_EVENT;
            }
        }
        return CleverTapEvents.CHARGED;
    }

    private com.stpl.tech.kettle.domain.model.OrderNotification createOrderNotificationDOmain(com.stpl.tech.kettle.data.model.OrderNotification orderNotification) {
        //com.stpl.tech.kettle.data.model.OrderNotification orderNotificationData = new com.stpl.tech.kettle.data.model.OrderNotification();
        com.stpl.tech.kettle.domain.model.OrderNotification orderNotificationData = new com.stpl.tech.kettle.domain.model.OrderNotification();
        if(Objects.nonNull(orderNotification)){
            try {
                orderNotificationData.setSubscriptionName(orderNotification.getSubscriptionName());
                orderNotificationData.setOfferDescription(orderNotification.getOfferDescription());
                orderNotificationData.setValidDays(orderNotification.getValidDays());
                orderNotificationData.setPlanEndDate(orderNotification.getPlanEndDate());
                orderNotificationData.setCustomerName(orderNotification.getCustomerName());
                orderNotificationData.setSelectOverallSaving(orderNotification.getSelectOverallSaving());
                orderNotificationData.setSelectSavingAmount(orderNotification.getSelectSavingAmt());
                orderNotificationData.setNextOfferText(orderNotification.getNextOfferText());
                orderNotificationData.setValidityTill(orderNotification.getValidityTill());
                orderNotificationData.setChannelPartner(orderNotification.getChannelPartner());
                orderNotificationData.setLoyalTeaTotalCount(orderNotification.getLoyalTeaTotalCount());
                orderNotificationData.setTotalLoyalTeaPoint(orderNotification.getTotalLoyalTeaPoint());
                orderNotificationData.setLoyalTeaPoints(orderNotification.getLoyalTeaPoints());
                orderNotificationData.setOrderAmt(orderNotification.getOrderAmt());
                orderNotificationData.setSavingAmt(orderNotification.getSavingAmt());
                orderNotificationData.setSavingText(orderNotification.getSavingText());
                orderNotificationData.setOrderFeedBackUrl(orderNotification.getOrderFeedbackUrl());
                orderNotificationData.setOrderRecieptUrl(orderNotification.getOrderRecieptUrl());
                orderNotificationData.setIsSubscriptionUsed(AppUtils.getStatus(orderNotification.getIsSubscriptionUsed()));
                orderNotificationData.setSubscriptionValidityInDays(orderNotification.getSubscriptionValidityInDays());
                orderNotificationData.setWalletPurchaseAmt(orderNotification.getWalletPurchaseAmt());
                orderNotificationData.setWalletPendingAmount(orderNotification.getWalletPendingAmt());
//			orderNotificationData.setIs(orderNotification.getIsWalletPurchased());
                orderNotificationData.setWalletSavingAmount(orderNotification.getWalletSavingAmt());
                orderNotificationData.setWalletExtraAmount(orderNotification.getWalletExtraAmt());
//			orderNotificationData.setWalletUsed(orderNotification.getWalletUsed());
                orderNotificationData.setGenerateOrderId(orderNotification.getGeneratedOrderId());
                orderNotificationData.setItemCode(orderNotification.getItemCode());
                orderNotificationData.setCashPendingAmount(orderNotification.getCashPendingAmt());
                orderNotificationData.setVoucherCode(orderNotification.getVoucherCode());
//			orderNotificationData.setSmsTemplateDate(orderNotification.getSmsTemplateDate());
                orderNotificationData.setUsedAmount(orderNotification.getUsedAmt());
                orderNotificationData.setCashBackAmount(orderNotification.getCashBackAmt());
                orderNotificationData.setCashBackAllotmentStartDate(orderNotification.getCashBackStartDate());
//			orderNotificationData.setCashBackAllotmentEndDate(orderNotification.getCashBackAllotmentEndDate());
                orderNotificationData.setRefundAmount(orderNotification.getRefundAmt());
                orderNotificationData.setSmsSubscriber(AppUtils.getStatus(orderNotification.getIsSmsSubscriber()));
                orderNotificationData.setWhatsAppOptIn(AppUtils.getStatus(orderNotification.getIsWhatsappOptIn()));
                orderNotificationData.setCustomerContactNumber(orderNotification.getCustomerContact());
                orderNotificationData.setIsLoyaltyUnlocked(AppUtils.getStatus(orderNotification.getIsLoyaltyUnlocked()));
                orderNotificationData.setSubscriptionValidityInDays(orderNotification.getDaysLeft());
                orderNotificationData.setIsSubscriptionPurched(AppUtils.getStatus(orderNotification.getIsSubscriptionPurched()));
            }catch (Exception e) {
                LOG.error("Error while setting orderNotification data  ::{}", e);
            }
        }
        return orderNotificationData;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "DumpDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void pushSwiggyOrsData(){
        List<SwiggyOrsRaw> swiggyOrsRawList =
                swiggyOrsRawDao.getSwiggyOrsOrderForBusinessDate(AppUtils.parseDate(AppUtils.getPreviousDateFormatted()));
        OrderDetailEventRequest request = new OrderDetailEventRequest();
        request.setPublishToFamePilot(true);
        request.setType("ORS");
        for(SwiggyOrsRaw swiggyOrsRaw : swiggyOrsRawList){
            request.setPartnerOrderId(swiggyOrsRaw.getOrderSourceId());
            request.setIssueType(swiggyOrsRaw.getOrs());
            publishPartnerOrderDetail(request);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class, value = "DumpDataSourceTM", readOnly = false, propagation = Propagation.REQUIRED)
    public void pushSwiggyRejectionData(){
        List<SwiggyRejectionRaw> swiggyRejectionRawList = swiggyOrsRawDao.
                getSwiggyRejectionOrderForBusinessDate(AppUtils.parseDate(AppUtils.getPreviousDateFormatted()));
        OrderDetailEventRequest request = new OrderDetailEventRequest();
        request.setPublishToFamePilot(true);
        request.setType("CANCELLED ORDER");
        for(SwiggyRejectionRaw swiggyRejectionRaw : swiggyRejectionRawList){
            request.setPartnerOrderId(swiggyRejectionRaw.getOrderSourceId());
            request.setIssueType(swiggyRejectionRaw.getCancellationComment());
            publishPartnerOrderDetail(request);
        }
    }


}
