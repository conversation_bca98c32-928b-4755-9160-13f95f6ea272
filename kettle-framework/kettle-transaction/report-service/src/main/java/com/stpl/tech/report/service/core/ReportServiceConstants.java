/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.report.service.core;

public class ReportServiceConstants {

    public static final String API_VERSION = "v1";

    public static final String SEPARATOR = "/";

    public static final String REPORT_METADATA_ROOT_CONTEXT = "report-metadata";

    public static final String LOG_COLLECTION_ROOT_CONTEXT = "log-data";

    public static final String CAMPAIGN_ROOT_CONTEXT = "customer-campaign";

    public static final String SALES_API_ROOT_CONTEXT = "sales-resource";
    
    public static final String DATA_ENCRYPTION = "crypto";
    public static final String SCORE_CARD_ROOT_CONTEXT = "score-card-resource";
    public static final String INVENTORY_MOVEMENT_ROOT_CONTEXT = "inventory-movement-resource";
    public static final String STOCK_OUT_REPORT_ROOT_CONTEXT = "stock-report";
    public static final String MAINTENANCE_CONTEXT = "maintenance-ticket";

    public static final String INCREMENTAL_FAV_CHAI_PUSH = "incremental-fav-chai-push";
    public static final String DASHBOARD_REFRESH_ROOT_CONTEXT = "dashboard-refresh";
    public static final String EXTERNAL_API_ROOT_CONTEXT = "external";

    public static final String WHATSAPP_DATA_ROOT_CONTEXT = "whatsapp-data";
    public static final String FAME_PILOT_ROOT_CONTEXT = "fame-pilot";

    public static final String SUPERU = "super-u";

}
