package com.stpl.tech.report.service.dao.impl;

import com.stpl.tech.kettle.data.dao.impl.AbstractDaoImpl;
import com.stpl.tech.kettle.data.model.GamifiedOfferDetail;
import com.stpl.tech.report.service.dao.OrderSearchDao;
import org.hibernate.query.internal.NativeQueryImpl;
import org.hibernate.transform.AliasToEntityMapResultTransformer;
import org.springframework.stereotype.Repository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.persistence.Query;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;


@Repository
public class OrderSearchDaoImpl extends AbstractDaoImpl implements OrderSearchDao {

    private static final Logger LOG = LoggerFactory.getLogger(OrderSearchDaoImpl.class);
    @Override
    public Map<String, Object> getPartnerOrderDetail(String partnerSourceId, String issueType) {
        Query query = manager.createNativeQuery("CALL GET_PARTNER_ORDER_DETAILS(:partnerSourceId,:customerComplaint,:issueType)");
        NativeQueryImpl nativeQuery = (NativeQueryImpl) query;
        nativeQuery.setParameter("partnerSourceId", partnerSourceId);
        nativeQuery.setParameter("customerComplaint","NA");
        nativeQuery.setParameter("issueType",issueType);
        nativeQuery.setResultTransformer(AliasToEntityMapResultTransformer.INSTANCE);
        List<Map<String,Object>> result = nativeQuery.getResultList();
        if(Objects.nonNull(result.get(0))) {
            return result.get(0);
        }
        return null;
    }

    @Override
    public Map<String, Object> getCustomerDetail(String partnerSourceId) {
        Query query = manager.createNativeQuery("SELECT od.ORDER_ID,od.ORDER_SOURCE_ID,ci.CUSTOMER_ID,ci.CONTACT_NUMBER,ci.FIRST_NAME,ci.IS_NUMBER_VERIFIED\n" +
                "FROM ORDER_DETAIL od LEFT OUTER JOIN PARTNER_ORDER_DETAIL pod ON od.ORDER_ID = pod.KETTLE_ORDER_ID\n" +
                "        LEFT OUTER JOIN PARTNER_ORDER_CUSTOMER_MAPPING pocm ON pod.CUSTOMER_ID = pocm.PARTNER_CUSTOMER_ID\n" +
                "        AND od.BRAND_ID = pocm.BRAND_ID AND od.CHANNEL_PARTNER_ID = pocm.CHANNEL_PARTNER_ID AND pocm.VALID = 'Y'\n" +
                "        LEFT OUTER JOIN CUSTOMER_INFO ci ON pocm.CUSTOMER_ID = ci.CUSTOMER_ID\n" +
                "WHERE od.ORDER_SOURCE_ID = :partnerSourceId OR od.ORDER_ID = :partnerSourceId OR od.GENERATED_ORDER_ID = :partnerSourceId");
        NativeQueryImpl nativeQuery = (NativeQueryImpl) query;
        nativeQuery.setParameter("partnerSourceId", partnerSourceId);
        nativeQuery.setResultTransformer(AliasToEntityMapResultTransformer.INSTANCE);
        List<Map<String,Object>> result = nativeQuery.getResultList();
        if(Objects.nonNull(result.get(0))) {
            return result.get(0);
        }
        return null;
    }

    @Override
    public Map<String, Object> getFallbackOrderCustomerDetail(String partnerCustomerId){
        Query query = manager.createNativeQuery("SELECT pocm.PARTNER_CUSTOMER_ID,ci.CUSTOMER_ID,ci.CONTACT_NUMBER,ci.FIRST_NAME,ci.IS_NUMBER_VERIFIED\n" +
                "FROM PARTNER_ORDER_CUSTOMER_MAPPING pocm LEFT OUTER JOIN CUSTOMER_INFO ci ON pocm.CUSTOMER_ID = ci.CUSTOMER_ID\n" +
                "WHERE pocm.PARTNER_CUSTOMER_ID = :partnerCustomerId ");
        query.setParameter("partnerCustomerId", partnerCustomerId);
        org.hibernate.query.NativeQuery nativeQuery = query.unwrap(org.hibernate.query.NativeQuery.class);
        nativeQuery.setResultTransformer(AliasToEntityMapResultTransformer.INSTANCE);
        List<Map<String,Object>> result = nativeQuery.getResultList();
        if(Objects.nonNull(result.get(0))) {
            return result.get(0);
        }
        return null;
    }

    @Override
    public List<GamifiedOfferDetail> getGamifiedOfferDetailDataList(String offerSource, String utmSource, Date recordTime){
        try {
            Query query = manager.createQuery("SELECT data From GamifiedOfferDetail data where data.offerSource = :offerSource and data.utmSource = :utmSource "
                    + "and data.recordTime>= :recordTime");
            query.setParameter("offerSource",offerSource);
            query.setParameter("utmSource",utmSource);
            query.setParameter("recordTime",recordTime);
            List<GamifiedOfferDetail> resultList = query.getResultList();
            return resultList;
        }catch (Exception e){
            LOG.info("Error in fetching gamified offer detail data list for offerSource: {}",offerSource);
        }
        return null;
    }
}
