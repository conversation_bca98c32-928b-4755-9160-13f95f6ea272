package com.stpl.tech.report.service.util;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.ObjectInputStream;
import java.io.ObjectOutputStream;

public class ReportUtill {

    public static <T> T getDeepClone (T object){
    try{
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        ObjectOutputStream objOutput = new ObjectOutputStream(output);
        objOutput.writeObject(object);
        ByteArrayInputStream input  = new ByteArrayInputStream(output.toByteArray());
        ObjectInputStream objInput   = new ObjectInputStream(input);
        return (T) objInput.readObject();
    }catch (Exception e){
            throw new RuntimeException(e.getMessage());
    }
    }


}
