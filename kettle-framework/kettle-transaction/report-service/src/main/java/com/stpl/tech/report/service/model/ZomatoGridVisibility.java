package com.stpl.tech.report.service.model;

import com.stpl.tech.util.excelparser.annotations.ExcelField;
import com.stpl.tech.util.excelparser.annotations.ExcelObject;
import com.stpl.tech.util.excelparser.annotations.ExcelSheet;
import com.stpl.tech.util.excelparser.annotations.ParseType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@ExcelSheet(value = "Grid_Visibility")
@ExcelObject(parseType = ParseType.ROW, start = 1, end = 0)
@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class ZomatoGridVisibility {
    @ExcelField
    private String aggregation;
    @ExcelField
    private String resId;
    @ExcelField
    private String tcpcFlag;
    @ExcelField
    private String resName;
    @ExcelField
    private String cityName;
    @ExcelField
    private String mealDay;
    @ExcelField
    private String actual;
    @ExcelField
    private String expected;
    @ExcelField
    private String gridVisibility;
}
