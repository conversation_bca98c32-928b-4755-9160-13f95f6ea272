package com.stpl.tech.report.service.service;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.stpl.tech.kettle.clm.model.DashboardLastUpdateData;
import org.pentaho.di.core.exception.KettleException;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.IOException;
import java.net.URISyntaxException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

public interface DashboardRefreshService {

    String getAttachmentPath();

    Boolean fetchDataToAdjunctSheet(String sheetID) throws IOException, URISyntaxException;

    Date updateZomatoRatingsData(String fileName);

    Boolean runJob(String siteId, String workbookId) throws KettleException, JsonProcessingException;

    Date updateZomatoGridVisibility(String fileName);

    String checkForUpdatedValuesZFR(String filePath, String fileName) throws IOException, KettleException;

    Date updateZomatoItemLevelRating(String fileName);

    Date updateZomatoPromoRedemption(String fileName);

    Boolean runPentahoTransformation(String outputFile, String jobPath, Date startDate) throws KettleException;

    String loginTableau() throws JsonProcessingException;

    Boolean logoutTableau(String token);

    Boolean refreshExtract(String siteId, String workbookId, String token);

    String getGmailAccessToken() throws IOException, URISyntaxException;

    String getGmailRefreshToken() throws IOException;

    Date updateCouponRedemptionSwiggy() throws KettleException, IOException;

    Date updateCouponRedemptionZomato() throws KettleException, IOException;

    String fetchUnreadMail(String accessToken, String label) throws IOException, URISyntaxException;

    String fetchAttachment(String accessToken, String messageId) throws IOException, URISyntaxException;

    Boolean markAsRead(String accessToken, String messageId) throws IOException, URISyntaxException;

    Boolean postLastUpdate(Date endDate, Date endDateGV, Date endDateILR, String flowType, Date endDateZPR);

    List<String> fetchAllUnreadMail() throws IOException, URISyntaxException;

    DashboardLastUpdateData getLastRecordDate();
}
