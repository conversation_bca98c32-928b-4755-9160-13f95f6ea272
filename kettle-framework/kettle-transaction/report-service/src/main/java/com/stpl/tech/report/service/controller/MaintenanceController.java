package com.stpl.tech.report.service.controller;

import com.stpl.tech.report.service.service.MaintenanceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import static com.stpl.tech.report.service.core.ReportServiceConstants.*;

@RestController
@RequestMapping(value = API_VERSION+SEPARATOR+MAINTENANCE_CONTEXT)
public class MaintenanceController {

    @Autowired
    MaintenanceService maintenanceService;
    @PostMapping("/create-tickets")
    ResponseEntity<Resource> startTicketGeneration(@RequestParam("file") MultipartFile file,@RequestParam("apiKey") String apiKey){
        Resource output =  maintenanceService.createMaintenanceTicket(file,apiKey);
        String contentType = "application/xlsx";
        String headerValue = "attachment; filename=\"" + output.getFilename() + "\"";
        return ResponseEntity.ok()
                .header(HttpHeaders.CONTENT_DISPOSITION,headerValue)
                .contentType(MediaType.parseMediaType(contentType))
                .body(output);
    }

    @Scheduled(cron = "0 0 0 * * *", zone = "GMT+05:30")
    @GetMapping("/clean-temp-files")
    void cleanTempFiles(){
        maintenanceService.deleteTempFiles();
    }
}
