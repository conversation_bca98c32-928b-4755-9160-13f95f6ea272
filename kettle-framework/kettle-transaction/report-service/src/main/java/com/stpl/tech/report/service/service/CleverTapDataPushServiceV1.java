package com.stpl.tech.report.service.service;

import com.stpl.tech.report.service.model.CLMDataPushType;
import com.stpl.tech.report.service.model.CLMOrderDataPushRequest;
import com.stpl.tech.report.service.model.CLMUserDataPushRequest;
import com.stpl.tech.report.service.model.CleverTapPushResponse;
import org.json.JSONArray;

import java.util.Date;
import java.util.List;

/**
 * Created by Chaayos on 02-05-2017.
 */
public interface CleverTapDataPushServiceV1 {

    void pushClmData(CLMUserDataPushRequest request);

    void pushClmChargedEventData(CLMOrderDataPushRequest request, CLMDataPushType dataPushType);

    void pushClmOrderDataRange(CLMOrderDataPushRequest request);

    void pushProfileEventForADate(Date date, Integer startId, Integer dataSize, Integer brandId);

    void pushProfileEventForAEvent(Integer eventId, Integer startId, Integer dataSize, Integer brandId);

    CleverTapPushResponse pushBatchWiseData(Integer brandId, Object pagedDataToPush, Integer startId, Integer endId,
                                            String type, Integer eventId, Integer batchSize, Integer pageSize);

    JSONArray getPagedData(Integer startIndex, Integer pageSize, List<Object[]> fullData);
}
