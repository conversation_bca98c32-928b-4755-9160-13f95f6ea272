package com.stpl.tech.report.service.controller;

import com.stpl.tech.report.service.model.ScoreCardBody;
import com.stpl.tech.report.service.service.ScoreCardReportService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static com.stpl.tech.report.service.core.ReportServiceConstants.*;

@RestController
@RequestMapping(value = API_VERSION+SEPARATOR+SCORE_CARD_ROOT_CONTEXT)
public class ScoreCardReportResource {
    @Autowired
    private ScoreCardReportService scoreCardReportService;

    @Scheduled(cron = "0 0 12 1 * *", zone = "GMT+05:30")
    @PostMapping("/start/monthly/score-card-process")
    void startJob(){
        scoreCardReportService.startScoreCardReportsProcess(null,null,null,null);
    }

    @PostMapping("/start/score-card-process")
    void startJob(@RequestBody ScoreCardBody scoreCardBody){
        scoreCardReportService.startScoreCardReportsProcess(scoreCardBody.getStartDate(),scoreCardBody.getEndDate(),scoreCardBody.getStartDateFive(),scoreCardBody.getEndDateFive());
    }

    @PostMapping("start/weekly/score-card-process")
    @Scheduled(cron = "0 0 2 * * MON", zone = "GMT+05:30")
    void startJobWeekly(){
        scoreCardReportService.startScoreCardReportsWeeklyProcess(null,null,null,null);
    }
}
