package com.stpl.tech.report.service.dao.impl;

import com.stpl.tech.kettle.data.dao.impl.AbstractDaoImpl;
import com.stpl.tech.kettle.data.model.CafeRaiseRequestApproval;
import com.stpl.tech.report.service.dao.UnitReportDao;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Repository;

import javax.persistence.Query;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Repository
public class UnitReportDaoImpl extends AbstractDaoImpl implements UnitReportDao {
    private static final Logger LOG = LoggerFactory.getLogger(UnitReportDaoImpl.class);

    @Override
    public List<CafeRaiseRequestApproval> getReportForUnitClosure(Integer unitId, Integer noOfDays) {
        try {
            String queryString="FROM CafeRaiseRequestApproval C WHERE C.businessDate BETWEEN :startDate AND :endDate ";
            if(Objects.nonNull(unitId)){
                queryString=queryString+" and C.unitId=:unitId";
            }
            Query query = manager.createQuery(queryString);
            if(Objects.nonNull(unitId)){
                query.setParameter("unitId",unitId);
            }
            query.setParameter("startDate",AppUtils.getOldDate(AppUtils.getCurrentDate(),noOfDays));
            query.setParameter("endDate",AppUtils.getCurrentDate());
            return query.getResultList();
           
        }
        catch (Exception e){
            LOG.error("Error is creating report");
        }

        return new ArrayList<>();
    }
}
