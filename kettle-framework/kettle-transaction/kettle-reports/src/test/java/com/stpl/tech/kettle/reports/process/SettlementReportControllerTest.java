/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.process;

import com.stpl.tech.kettle.core.TestUtil;
import com.stpl.tech.kettle.core.cache.MetadataCache;
import com.stpl.tech.kettle.core.config.TransactionConfig;
import com.stpl.tech.kettle.core.service.OrderManagementService;
import com.stpl.tech.kettle.customer.service.CardService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.TaxDataCache;
import com.stpl.tech.master.core.external.offer.service.OfferManagementExternalService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.support.AnnotationConfigContextLoader;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = { TransactionConfig.class }, loader = AnnotationConfigContextLoader.class)
public class SettlementReportControllerTest {
	@Autowired
	private OrderManagementService metadataDao;
	@Autowired
	private MetadataCache cache;

	@Autowired
	private MasterDataCache masterCache;

	@Autowired
	private TaxDataCache taxCache;

	@Autowired
	private EnvironmentProperties props;
	@Autowired
	private OfferManagementExternalService offerManagementExternalService;
	@Autowired
	private CardService cardService;

	@Test
	public void testGetMultipleSettlements() throws DataUpdationException, DataNotFoundException {
		SettlementReportController controller = new SettlementReportController(masterCache, cache, taxCache, props,
				offerManagementExternalService, cardService);
		for (int i = 1; i <= 100; i++) {
			int orderId = metadataDao.createOrder(TestUtil.getOrder(1, i)).getOrderId();
			Assert.assertEquals(true, orderId >= 1);
		}
		controller.execute(null);
	}
}
