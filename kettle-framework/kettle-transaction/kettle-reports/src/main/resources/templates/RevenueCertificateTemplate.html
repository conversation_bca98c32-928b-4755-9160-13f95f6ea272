<html lang="en">
<head>
</head>
<body style="font-family: <PERSON>ibri; font-size: 10.0pt;height: 100%; width:100%;">

    <div style=" height: 90%;">
        <div style="text-align: center; font-family: Calibri; font-size: 10.0pt; ">
            <b>To whom so ever it may concern</b>
        </div>
        <div style="font-family: Calibri; font-size: 10.0pt;">This is to confirm and certify that revenue detail of
            Sunshine Teahouse Private Limited for its cafe at $data.unitName for the month of
            $data.month $data.year are as under:
        </div>
        <div style="text-align: center; font-family: Calibri; font-size: 10.0pt;">$data.unitName</div>
        <table border="1"
               style="text-align: right; width: 100%; border-spacing: 0; border-color: #ccc; font-family: Calibri; font-size: 10.0pt;">
            <tr>
                <th style="text-align: center;">Date</th>
                #if( $data.isBreakdown == false )
                <th style="text-align: center;">Gross Sales</th>
                #end
                #if( $number.format("##,##0.00",$data.totalChaayosDineInSales) != '0' && $data.isBreakdown == true )
                <th style="text-align: center;">Chaayos Dine-in Net Sales</th>
                #end
                #if( $number.format("##,##0.00",$data.totalChaayosDeliverySales) != '0' && $data.isBreakdown == true )
                <th style="text-align: center;">Chaayos Delivery Net Sales</th>
                #end
                #if( $number.format("##,##0.00",$data.totalGntDineInSales) != '0' && $data.isBreakdown == true )
                <th style="text-align: center;">GnT Dine-in Net Sales</th>
                #end
                #if( $number.format("##,##0.00",$data.totalGntDeliverySales) != '0' && $data.isBreakdown == true )
                <th style="text-align: center;">GnT Delivery Net Sales</th>
                #end
                #if( $number.format("##,##0.00",$data.totalDesiCanteenDineInSales) != '0' && $data.isBreakdown == true )
                <th style="text-align: center;">Desi Canteen Dine-in Net Sales</th>
                #end
                #if( $number.format("##,##0.00",$data.totalDesiCanteenDeliverySales) != '0' && $data.isBreakdown == true
                )
                <th style="text-align: center;">Desi Canteen Delivery Net Sales</th>
                #end
                #if( $number.format("##,##0.00",$data.totalServiceCharge) != '0' && $data.isBreakdown == false )
                <th style="text-align: center;">Service Charges</th>
                #end
                #if( $number.format("##,##0.00",$data.totalServiceCharge) != '0' && $data.isBreakdown == false )
                <th style="text-align: center; width: 14%;">Sales After Service Charges</th>
                #end
                #if( $data.isBreakdown == false )
                <th style="text-align: center;">Discount</th>
                #end
                #if( $data.isBreakdown == false )
                <th style="text-align: center;">Taxes</th>
                #end
                <th style="text-align: center;">Total Net Sale</th>
            </tr>
            #foreach( $item in $data.certificates )
            <tr>
                <td style="text-align: center; padding-right: 5pt;">$date.format('dd-MM-yy',$item.date)</td>
                #if( $data.isBreakdown == false )
                <td style="text-align: right; padding-right: 5pt;">$number.format("##,##0.00",$item.grossSales)</td>
                #end
                #if( $number.format("##,##0.00",$data.totalChaayosDineInSales) != '0' && $data.isBreakdown == true )
                <td style="text-align: right; padding-right: 5pt;">
                    $number.format("##,##0.00",$item.chaayosDineInSales)
                </td>
                #end
                #if( $number.format("##,##0.00",$data.totalChaayosDeliverySales) != '0' && $data.isBreakdown == true )
                <td style="text-align: right; padding-right: 5pt;">
                    $number.format("##,##0.00",$item.chaayosDeliverySales)
                </td>
                #end
                #if( $number.format("##,##0.00",$data.totalGntDineInSales) != '0' && $data.isBreakdown == true )
                <td style="text-align: right; padding-right: 5pt;">$number.format("##,##0.00",$item.gntDineInSales)</td>
                #end
                #if( $number.format("##,##0.00",$data.totalGntDeliverySales) != '0' && $data.isBreakdown == true )
                <td style="text-align: right; padding-right: 5pt;">$number.format("##,##0.00",$item.gntDeliverySales)
                </td>
                #end
                #if( $number.format("##,##0.00",$data.totalDesiCanteenDineInSales) != '0' && $data.isBreakdown == true )
                <td style="text-align: right; padding-right: 5pt;">
                    $number.format("##,##0.00",$item.desiCanteenDineInSales)
                </td>
                #end
                #if( $number.format("##,##0.00",$data.totalDesiCanteenDeliverySales) != '0' && $data.isBreakdown == true
                )
                <td style="text-align: right; padding-right: 5pt;">
                    $number.format("##,##0.00",$item.desiCanteenDeliverySales)
                </td>
                #end
                #if( $number.format("##,##0.00",$data.totalServiceCharge) != '0' && $data.isBreakdown == false )
                <td style="text-align: right; padding-right: 5pt;">$number.format("##,##0.00",$item.serviceCharge)</td>
                #end
                #if( $number.format("##,##0.00",$data.totalServiceCharge) != '0' && $data.isBreakdown == false )
                <td style="text-align: right; padding-right: 5pt;">
                    $number.format("##,##0.00",$item.salesAfterServiceCharge)
                </td>
                #end
                #if( $data.isBreakdown == false )
                <td style="text-align: right; padding-right: 5pt;">$number.format("##,##0.00",$item.discount)</td>
                #end
                #if( $data.isBreakdown == false )
                <td style="text-align: right; padding-right: 5pt;">$number.format("##,##0.00",$item.taxes)</td>
                #end
                <td style="text-align: right; padding-right: 5pt;">$number.format("##,##0.00",$item.netSale)</td>
            </tr>
            #end
            <tr>
                <td style="text-align: center; padding-right: 5pt;">Grand Total</td>
                #if( $data.isBreakdown == false )
                <td style="text-align: right; padding-right: 5pt;">$number.format("##,##0.00",$data.totalGrossSales)
                </td>
                #end
                #if( $number.format("##,##0.00",$data.totalChaayosDineInSales) != '0' && $data.isBreakdown == true )
                <td style="text-align: right; padding-right: 5pt;">
                    $number.format("##,##0.00",$data.totalChaayosDineInSales)
                </td>
                #end
                #if( $number.format("##,##0.00",$data.totalChaayosDeliverySales) != '0' && $data.isBreakdown == true )
                <td style="text-align: right; padding-right: 5pt;">
                    $number.format("##,##0.00",$data.totalChaayosDeliverySales)
                </td>
                #end
                #if( $number.format("##,##0.00",$data.totalGntDineInSales) != '0' && $data.isBreakdown == true )
                <td style="text-align: right; padding-right: 5pt;">
                    $number.format("##,##0.00",$data.totalGntDineInSales)
                </td>
                #end
                #if( $number.format("##,##0.00",$data.totalGntDeliverySales) != '0' && $data.isBreakdown == true )
                <td style="text-align: right; padding-right: 5pt;">
                    $number.format("##,##0.00",$data.totalGntDeliverySales)
                </td>
                #end
                #if( $number.format("##,##0.00",$data.totalDesiCanteenDineInSales) != '0' && $data.isBreakdown == true )
                <td style="text-align: right; padding-right: 5pt;">
                    $number.format("##,##0.00",$data.totalDesiCanteenDineInSales)
                </td>
                #end
                #if( $number.format("##,##0.00",$data.totalDesiCanteenDeliverySales) != '0' && $data.isBreakdown == true
                )
                <td style="text-align: right; padding-right: 5pt;">
                    $number.format("##,##0.00",$data.totalDesiCanteenDeliverySales)
                </td>
                #end
                #if( $number.format("##,##0.00",$data.totalServiceCharge) != '0' && $data.isBreakdown == false )
                <td style="text-align: right; padding-right: 5pt;">-</td>
                #end
                #if( $number.format("##,##0.00",$data.totalServiceCharge) != '0' && $data.isBreakdown == false )
                <td style="text-align: right; padding-right: 5pt;">
                    $number.format("##,##0.00",$data.totalSalesAfterServiceCharge)
                </td>
                #end
                #if( $data.isBreakdown == false )
                <td style="text-align: right; padding-right: 5pt;">$number.format("##,##0.00",$data.totalDiscount)</td>
                #end
                #if( $data.isBreakdown == false )
                <td style="text-align: right; padding-right: 5pt;">$number.format("##,##0.00",$data.totalTaxes)</td>
                #end
                <td style="text-align: right; padding-right: 5pt;">$number.format("##,##0.00",$data.totalNetSale)</td>
            </tr>
        </table>
        <div style="font-family: Calibri; font-size: 10.0pt;padding-top: 10pt">Dated: $date.format('dd MMMM yyyy',$data.dateToday)</div>
    </div>
</body>
</html>
