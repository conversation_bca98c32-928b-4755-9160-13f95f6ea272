<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<ReportCategories xmlns="http://www.w3schools.com">
	<categories>
		<category name="Metadata Reports" type="kettle"
			accessCode="kettle">
			<reports>
				<report name="Customer Email with Chaayos Domain"
					executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
    FIRST_NAME,
    CONTACT_NUMBER,
    EMAIL_ID,
    IS_NUMBER_VERIFIED,
    IS_EMAIL_VERIFIED
FROM
    KETTLE_DUMP.CUSTOMER_INFO
WHERE
    EMAIL_ID LIKE '%chaayos.com'
]]>
					</content>
				</report>
				<report name="Unit Data"
					executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
    UNIT_ID,
    UNIT_NAME,
    UNIT_REGION,
    UNIT_CATEGORY,
    UNIT_EMAIL,
    UNIT_STATUS,
    UNIT_SUB_CATEGORY,
    GSTIN,
    ai.*,
    ed1.EMP_ID AREA_MANAGER_ID,
    ed1.EMP_NAME AREA_MANAGER_NAME,
    ed2.EMP_ID DEPUTY_AREA_MANAGER_ID,
    ed2.EMP_NAME DEPUTY_MANAGER_NAME
FROM
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud
    INNER JOIN KETTLE_MASTER_DUMP.ADDRESS_INFO ai ON ud.UNIT_ADDR_ID = ai.ADDRESS_ID
    INNER JOIN KETTLE_MASTER_DUMP.EMPLOYEE_DETAIL ed1 ON ud.UNIT_MANAGER = ed1.EMP_ID
    INNER JOIN KETTLE_MASTER_DUMP.EMPLOYEE_DETAIL ed2 ON ud.CAFE_MANAGER = ed2.EMP_ID;


]]>
					</content>
					<params>
					</params>
				</report>
			</reports>
		</category>		
	</categories>
</ReportCategories>

