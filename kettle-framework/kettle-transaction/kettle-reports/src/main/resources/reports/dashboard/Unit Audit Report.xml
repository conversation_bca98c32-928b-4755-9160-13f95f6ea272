<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<ReportCategories xmlns="http://www.w3schools.com">
	<categories>
		<category name="Audit Report" type="SuMo" accessCode="SuMo">
			<reports>
				<report name="Goods Received For a Unit For Date Range"
                        executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
    SELECT 
    ux.UNIT_NAME,
    p.PRODUCT_ID,
    p.PRODUCT_NAME,
    g.GOODS_RECEIVED_ID,
    g.LAST_UPDATE_TIME,
    CASE
        WHEN g.GENERATION_UNIT_ID = g.GENERATED_FOR_UNIT_ID THEN vd.VENDOR_NAME
        ELSE u.UNIT_NAME
    END TRANSFER_LOCATION_CODE,
    'TransferIn' TRANSFER_TYPE,
    gi.UNIT_OF_MEASURE,
	TRUNCATE(ri.REQUESTED_QUANTITY, 3) REQUESTED_QUANTITY,
	TRUNCATE(gi.TRANSFERRED_QUANTITY, 3) TRANSFERRED_QUANTITY,
    TRUNCATE(gi.RECEIVED_QUANTITY, 3) RECEIVED_QUANTITY
	
FROM
    KETTLE_SCM_DUMP.GOODS_RECEIVED_ITEM gi
        INNER JOIN
    KETTLE_SCM_DUMP.GOODS_RECEIVED g ON gi.GOODS_RECEIVED_ID = g.GOODS_RECEIVED_ID
        AND g.GOODS_RECEIVED_STATUS = 'SETTLED'
        AND DATE(g.LAST_UPDATE_TIME) >= :startDate
        AND DATE(g.LAST_UPDATE_TIME) <= :endDate
        INNER JOIN
    KETTLE_SCM_DUMP.SKU_DEFINITION s ON s.SKU_ID = gi.SKU_ID
        INNER JOIN
    KETTLE_SCM_DUMP.PRODUCT_DEFINITION p ON p.PRODUCT_ID = s.LINKED_PRODUCT_ID
        INNER JOIN
    KETTLE_SCM_DUMP.REQUEST_ORDER_ITEM ri ON ri.REQUEST_ORDER_ITEM_ID = gi.REQUEST_ORDER_ITEM_ID
        INNER JOIN
    KETTLE_SCM_DUMP.UNIT_DETAIL u ON u.UNIT_ID = g.GENERATION_UNIT_ID
        INNER JOIN
    KETTLE_SCM_DUMP.UNIT_DETAIL ux ON ux.UNIT_ID = g.GENERATED_FOR_UNIT_ID
        LEFT JOIN
    KETTLE_SCM_DUMP.VENDOR_DETAIL vd ON vd.VENDOR_ID = ri.VENDOR_ID
WHERE
    g.GENERATED_FOR_UNIT_ID = :unitId
    order by g.GOODS_RECEIVED_ID
                        ]]>
					</content>
					<params>
						<param name="unitId" displayName="Unit Id"
                               dataType="INTEGER"/>
						<param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>

					</params>
				</report>
				<report name="Wastage Report For A Unit For Date Range"
                        executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
						SELECT
						wid.PRODUCT_ID,
						pd.PRODUCT_NAME,
						cd.CATEGORY_NAME,
						scd.SUB_CATEGORY_NAME,
						ud.UNIT_NAME,
						pd.NEGOTIATED_UNIT_PRICE,
						wid.QUANTITY,
						(pd.NEGOTIATED_UNIT_PRICE*wid.QUANTITY) AS COST,
						pd.UNIT_OF_MEASURE,
						we.BUSINESS_DATE,
						we.GENERATION_TIME,
						we.GENERATED_BY,
						wid.COMMENT
						FROM
						KETTLE_SCM_DUMP.WASTAGE_EVENT we
							INNER JOIN
						KETTLE_SCM_DUMP.WASTAGE_ITEM_DATA wid on wid.WASTAGE_ID=we.WASTAGE_ID
                            				INNER JOIN 
						KETTLE_SCM_DUMP.PRODUCT_DEFINITION pd ON wid.PRODUCT_ID = pd.PRODUCT_ID
							INNER JOIN
						KETTLE_SCM_DUMP.CATEGORY_DEFINITION cd ON pd.CATEGORY_ID = cd.CATEGORY_ID     
							INNER JOIN
						KETTLE_SCM_DUMP.SUB_CATEGORY_DEFINITION scd ON pd.SUB_CATEGORY_ID = scd.SUB_CATEGORY_ID     
        						INNER JOIN
						KETTLE_SCM_DUMP.UNIT_DETAIL ud ON we.UNIT_ID = ud.UNIT_ID
						AND we.STATUS = 'SETTLED' AND we.BUSINESS_DATE >= :startDate AND we.BUSINESS_DATE <= :endDate
						AND we.UNIT_ID = :unitId;
                        ]]>
					</content>
					<params>
						<param name="unitId" displayName="Unit Id"
                               dataType="INTEGER"/>
						<param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
					</params>

				</report>
				<report name="Calculated Variance for a Unit for Date Range"
                        executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
			SELECT 
    x.UNIT_ID,
    x.UNIT_NAME,
    x.PRODUCT_ID,
    x.PRODUCT_NAME,
    x.UNIT_OF_MEASURE,
    x.ITEM_PRICE,
    x.CATEGORY_NAME,
    x.SUB_CATEGORY_NAME,
    x.OPENING_BUSINESS_DATE,
    x.CLOSING_BUSINESS_DATE,
    x.OPENING,
    x.RECEIVED,
    x.TRANSFERRED,
    x.CONSUMPTION,
    x.WASTAGE,
    x.CLOSING,
    CASE
        WHEN x.VARIANCE < 0 THEN 0.000000
        ELSE x.VARIANCE
    END AS VARIANCE,
    CASE
        WHEN x.VARIANCE < 0 THEN 0.000000
        ELSE COALESCE(x.VARIANCE * x.ITEM_PRICE, 0)
    END AS COST_PRICE
FROM
    (SELECT 
        up.UNIT_ID,
            up.UNIT_NAME,
            up.PRODUCT_ID,
            up.UNIT_OF_MEASURE,
            up.PRODUCT_NAME,
            up.CATEGORY_NAME,
            up.SUB_CATEGORY_NAME,
            up.ITEM_PRICE,
            opening.BUSINESS_DATE OPENING_BUSINESS_DATE,
            closing.BUSINESS_DATE CLOSING_BUSINESS_DATE,
            COALESCE(opening.OPENING_STOCK, 0) OPENING,
            COALESCE(gr.RECEIVED_QUANTITY, 0) RECEIVED,
            COALESCE(tr.TRANSFERRED_QUANTITY, 0) TRANSFERRED,
            COALESCE(consumption.CONSUMPTION, 0) CONSUMPTION,
            COALESCE(wastage.WASTAGE, 0) WASTAGE,
            COALESCE(closing.CLOSING, 0) CLOSING,
            (COALESCE(opening.OPENING_STOCK, 0) + COALESCE(gr.RECEIVED_QUANTITY, 0) - COALESCE(tr.TRANSFERRED_QUANTITY, 0) - COALESCE(consumption.CONSUMPTION, 0) - COALESCE(wastage.WASTAGE, 0) - COALESCE(closing.CLOSING, 0)) VARIANCE
    FROM
        (SELECT 
        ud.UNIT_ID,
            ud.UNIT_NAME,
            pd.PRODUCT_ID,
            pd.PRODUCT_NAME,
            pd.UNIT_OF_MEASURE,
            cd.CATEGORY_NAME,
            scd.SUB_CATEGORY_NAME,
            pd.NEGOTIATED_UNIT_PRICE ITEM_PRICE
    FROM
        KETTLE_SCM_DUMP.PRODUCT_DEFINITION pd, KETTLE_SCM_DUMP.UNIT_DETAIL ud, KETTLE_SCM_DUMP.CATEGORY_DEFINITION cd, KETTLE_SCM_DUMP.SUB_CATEGORY_DEFINITION scd
    WHERE
        cd.CATEGORY_ID = pd.CATEGORY_ID
            AND scd.SUB_CATEGORY_ID = pd.SUB_CATEGORY_ID) up
    LEFT OUTER JOIN (SELECT 
        si.UNIT_ID,
            si.PRODUCT_ID,
            si.BUSINESS_DATE,
            si.OPENING_STOCK OPENING_STOCK
    FROM
        KETTLE_SCM_DUMP.STOCK_INVENTORY si
    INNER JOIN KETTLE_SCM_DUMP.DAY_CLOSE_EVENT dce ON si.CURRENT_EVENT_ID = dce.EVENT_ID
    WHERE
        dce.BUSINESS_DATE = DATE(:startDate)
            AND dce.CLOSURE_EVENT_TYPE = 'STOCK_TAKE'
            AND dce.STATUS <> 'CANCELLED'
            AND dce.UNIT_ID = :unitId
    GROUP BY si.UNIT_ID , si.PRODUCT_ID , si.BUSINESS_DATE) opening ON opening.UNIT_ID = up.UNIT_ID
        AND opening.PRODUCT_ID = up.PRODUCT_ID
    LEFT OUTER JOIN (SELECT 
        gr.GENERATED_FOR_UNIT_ID UNIT_ID,
            pd.PRODUCT_ID,
            SUM(gri.RECEIVED_QUANTITY) RECEIVED_QUANTITY
    FROM
        KETTLE_SCM_DUMP.GOODS_RECEIVED gr
    INNER JOIN KETTLE_SCM_DUMP.GOODS_RECEIVED_ITEM gri ON gr.GOODS_RECEIVED_ID = gri.GOODS_RECEIVED_ID
    INNER JOIN KETTLE_SCM_DUMP.SKU_DEFINITION sd ON sd.SKU_ID = gri.SKU_ID
    INNER JOIN KETTLE_SCM_DUMP.PRODUCT_DEFINITION pd ON sd.LINKED_PRODUCT_ID = pd.PRODUCT_ID
    WHERE
        gr.GOODS_RECEIVED_STATUS = 'SETTLED'
            AND gr.LAST_UPDATE_TIME >= DATE_ADD(DATE(:startDate), INTERVAL 7 HOUR)
            AND gr.LAST_UPDATE_TIME <= DATE_ADD(DATE_ADD(DATE(:endDate), INTERVAL 1 DAY), INTERVAL 7 HOUR)
            AND gr.GENERATED_FOR_UNIT_ID = :unitId
    GROUP BY gr.GENERATED_FOR_UNIT_ID , pd.PRODUCT_ID , pd.PRODUCT_NAME) gr ON gr.UNIT_ID = up.UNIT_ID
        AND gr.PRODUCT_ID = up.PRODUCT_ID
    LEFT OUTER JOIN (SELECT 
        tr.GENERATION_UNIT_ID UNIT_ID,
            pd.PRODUCT_ID,
            pd.PRODUCT_NAME,
            SUM(tri.TRANSFERRED_QUANTITY) TRANSFERRED_QUANTITY
    FROM
        KETTLE_SCM_DUMP.TRANSFER_ORDER tr
    INNER JOIN KETTLE_SCM_DUMP.TRANSFER_ORDER_ITEM tri ON tr.TRANSFER_ORDER_ID = tri.TRANSFER_ORDER_ID
    INNER JOIN KETTLE_SCM_DUMP.SKU_DEFINITION sd ON sd.SKU_ID = tri.SKU_ID
    INNER JOIN KETTLE_SCM_DUMP.PRODUCT_DEFINITION pd ON sd.LINKED_PRODUCT_ID = pd.PRODUCT_ID
    WHERE
        tr.TRANSFER_ORDER_STATUS IN ('CREATED' , 'SETTLED')
            AND tr.LAST_UPDATE_TIME >= DATE_ADD(DATE(:startDate), INTERVAL 7 HOUR)
            AND tr.LAST_UPDATE_TIME <= DATE_ADD(DATE_ADD(DATE(:endDate), INTERVAL 1 DAY), INTERVAL 7 HOUR)
            AND tr.GENERATION_UNIT_ID <> tr.GENERATED_FOR_UNIT_ID
            AND tr.GENERATION_UNIT_ID = :unitId
    GROUP BY tr.GENERATION_UNIT_ID , pd.PRODUCT_ID , pd.PRODUCT_NAME) tr ON tr.UNIT_ID = up.UNIT_ID
        AND tr.PRODUCT_ID = up.PRODUCT_ID
    LEFT OUTER JOIN (SELECT 
        dce.UNIT_ID,
            dcp.PRODUCT_ID,
            SUM(COALESCE(dcp.CONSUMPTION, 0)) CONSUMPTION
    FROM
        KETTLE_SCM_DUMP.DAY_CLOSE_EVENT dce, KETTLE_SCM_DUMP.DAY_CLOSE_PRODUCT_VALUES dcp
    WHERE
        dce.EVENT_ID = dcp.EVENT_ID
            AND dce.BUSINESS_DATE >= DATE(:startDate)
            AND dce.BUSINESS_DATE < DATE_ADD(DATE(:endDate), INTERVAL 1 DAY)
            AND dce.CLOSURE_EVENT_TYPE = 'CLOSING'
            AND dce.STATUS IN ('INITIATED' , 'CLOSED')
            AND dce.UNIT_ID = :unitId
    GROUP BY dce.UNIT_ID , dcp.PRODUCT_ID) consumption ON consumption.UNIT_ID = up.UNIT_ID
        AND consumption.PRODUCT_ID = up.PRODUCT_ID
    LEFT OUTER JOIN (SELECT 
        we.UNIT_ID UNIT_ID, wid.PRODUCT_ID PRODUCT_ID, SUM(wid.QUANTITY) WASTAGE
    FROM
        KETTLE_SCM_DUMP.WASTAGE_EVENT we
        INNER JOIN KETTLE_SCM_DUMP.WASTAGE_ITEM_DATA wid on wid.WASTAGE_ID = we.WASTAGE_ID
    WHERE
        we.BUSINESS_DATE >= DATE(:startDate)
            AND we.GENERATION_TIME < DATE_ADD(DATE(:endDate), INTERVAL 1 DAY)
            AND we.UNIT_ID = :unitId
    GROUP BY UNIT_ID , PRODUCT_ID) wastage ON wastage.UNIT_ID = up.UNIT_ID
        AND wastage.PRODUCT_ID = up.PRODUCT_ID
    LEFT OUTER JOIN (SELECT 
        si.UNIT_ID,
            si.PRODUCT_ID,
            si.BUSINESS_DATE,
            CLOSING_STOCK CLOSING
    FROM
        KETTLE_SCM_DUMP.STOCK_INVENTORY si
    INNER JOIN KETTLE_SCM_DUMP.DAY_CLOSE_EVENT dce ON si.CURRENT_EVENT_ID = dce.EVENT_ID
    WHERE
        dce.BUSINESS_DATE = DATE(:endDate)
            AND dce.CLOSURE_EVENT_TYPE = 'STOCK_TAKE'
            AND dce.STATUS <> 'CANCELLED'
            AND dce.UNIT_ID = :unitId
    GROUP BY si.UNIT_ID , si.PRODUCT_ID , si.BUSINESS_DATE) closing ON closing.UNIT_ID = up.UNIT_ID
        AND closing.PRODUCT_ID = up.PRODUCT_ID
    WHERE
        opening.UNIT_ID IS NOT NULL
    ORDER BY up.UNIT_ID , up.PRODUCT_NAME) x                        
                        ]]>
					</content>
					<params>
						<param name="unitId" displayName="Unit Id"
                               dataType="INTEGER"/>
						<param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
					</params>


				</report>

				<report name="Transfer Items Report For Specific Unit For Date Range"
                        executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
                        
SELECT 
    ud1.UNIT_NAME,
    pd.PRODUCT_ID,
    pd.PRODUCT_NAME,
    t.TRANSFER_ORDER_ID,
    t.GENERATION_TIME,
    CASE
        WHEN ud.UNIT_NAME IS NULL THEN 'NA'
        ELSE ud.UNIT_NAME
    END TRANSFER_LOCATION_CODE,
    t.GENERATION_UNIT_ID,
    'TransferOut' TRANSFER_TYPE,
    t.TRANSFER_ORDER_STATUS,
    ti.UNIT_OF_MEASURE,
    ti.REQUESTED_QUANTITY,
    ti.TRANSFERRED_QUANTITY,
    ti.RECEIVED_QUANTITY
FROM
    KETTLE_SCM_DUMP.TRANSFER_ORDER t
        LEFT JOIN
    KETTLE_SCM_DUMP.TRANSFER_ORDER_ITEM ti ON t.TRANSFER_ORDER_ID = ti.TRANSFER_ORDER_ID
        AND t.GENERATION_UNIT_ID = :unitId
        INNER JOIN
    KETTLE_SCM_DUMP.SKU_DEFINITION sd ON sd.SKU_ID = ti.SKU_ID
        INNER JOIN
    KETTLE_SCM_DUMP.PRODUCT_DEFINITION pd ON sd.LINKED_PRODUCT_ID = pd.PRODUCT_ID
        INNER JOIN
    KETTLE_SCM_DUMP.UNIT_DETAIL ud ON t.GENERATED_FOR_UNIT_ID = ud.UNIT_ID
        INNER JOIN
    KETTLE_SCM_DUMP.UNIT_DETAIL ud1 ON t.GENERATION_UNIT_ID = ud1.UNIT_ID
        INNER JOIN
    KETTLE_SCM_DUMP.SKU_DEFINITION s ON ti.SKU_ID = s.SKU_ID
        AND t.GENERATION_TIME >= :startDate
        AND t.GENERATION_TIME <= :endDate
        AND t.TRANSFER_ORDER_STATUS <> 'CANCELLED'
        AND t.GENERATED_FOR_UNIT_ID <> t.GENERATION_UNIT_ID
ORDER BY t.TRANSFER_ORDER_ID
			]]>
					</content>
					<params>
						<param name="unitId" displayName="Unit Id"
                               dataType="INTEGER" />
						<param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>


					</params>
				</report>

				<report name="Item Consumption Report For Specific Unit For Date Range"
                        executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
                        
SELECT 
    ud.UNIT_NAME,
    pd.PRODUCT_NAME,
    rl.RTL_CODE,
    oi.DIMENSION,
    oi.PRICE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) IN (2101 , 2103, 2104)
        THEN
            oi.QUANTITY
        ELSE 0
    END) ACCOUNTABLE_COMPLIMENTARY_QUANTITY,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) IN (2100 , 2102, 2105, 2106)
        THEN
            oi.QUANTITY
        ELSE 0
    END) NON_ACCOUNTABLE_COMPLIMENTARY_QUANTITY,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'N'
                OR IS_COMPLIMENTARY IS NULL
        THEN
            oi.QUANTITY
        ELSE 0
    END) SOLD_QUANTITY,
    SUM(oi.QUANTITY) TOTAL_QUANTITY,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) IN (2101 , 2103, 2104)
        THEN
            oi.TOTAL_AMOUNT
        ELSE 0
    END) ACCOUNTABLE_COMPLIMENTARY_GMV,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) IN (2100 , 2102, 2105, 2106)
        THEN
            oi.PRICE * oi.QUANTITY
        ELSE 0
    END) NON_ACCOUNTABLE_COMPLIMENTARY_GMV,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'N'
                OR IS_COMPLIMENTARY IS NULL
        THEN
            oi.PRICE * oi.QUANTITY
        ELSE 0
    END) SOLD_QUANTITY_GMV,
    SUM(oi.PRICE * oi.QUANTITY) TOTAL_AMOUNT_GMV
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        LEFT JOIN
    KETTLE_DUMP.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd ON pd.PRODUCT_ID = oi.PRODUCT_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.REF_LOOKUP_TYPE rl ON pd.PRODUCT_TYPE = rl.RTL_ID
WHERE
    od.ORDER_STATUS <> 'CANCELLED'
        AND od.ORDER_ID > (SELECT 
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:startDate, 1))
        AND od.ORDER_ID <= (SELECT 
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :endDate)
        AND od.UNIT_ID = :unitId
GROUP BY ud.UNIT_NAME , pd.PRODUCT_NAME , rl.RTL_CODE , oi.DIMENSION , oi.PRICE
ORDER BY ud.UNIT_NAME , pd.PRODUCT_NAME , oi.DIMENSION

			]]>
					</content>
					<params>
						<param name="unitId" displayName="Unit Id"
                               dataType="INTEGER" />
						<param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
					</params>
				</report>
				<report name="Cancelled Orders Report For Specific For Date Range"
                        executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
    a.*
FROM
    (SELECT 
        ud.UNIT_NAME,
            ud.UNIT_REGION,
            od.GENERATED_ORDER_ID AS ORDER_NO,
            od.BILLING_SERVER_TIME AS BILL_TIME,
            od.BILL_CANCELLATION_TIME,
            nt.QUANTITY,
            od.ORDER_SOURCE,
            od.TAXABLE_AMOUNT,
            od.SURCHARGE_TAX_AMOUNT,
            od.SERVICE_TAX_AMOUNT,
            od.NET_PRICE_VAT_AMOUNT,
            od.MRP_VAT_AMOUNT,
            od.SETTLED_AMOUNT,
            od.EMP_ID,
            COALESCE(ed.EMP_NAME, 'NA') AS EMP_NAME,
            od.GENERATED_ORDER_ID,
            COALESCE(od.CANCELATION_REASON, 'NA') AS CANCELATION_REASON,
            COALESCE(od.CANCELLED_BY, 'NA') AS CANCELLED_BY_ID,
            COALESCE(ed2.EMP_NAME, 'NA') AS CANCELLED_BY,
            COALESCE(od.CANCEL_APPROVED_BY, 'NA') AS CANCEL_APPROVED_BY_ID,
            COALESCE(ed2.EMP_NAME, 'NA') AS CANCELLED_APPROVED_BY
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    LEFT JOIN KETTLE_MASTER_DUMP.EMPLOYEE_DETAIL ed ON od.EMP_ID = ed.EMP_ID
    LEFT JOIN KETTLE_MASTER_DUMP.EMPLOYEE_DETAIL ed2 ON od.CANCEL_APPROVED_BY = ed2.EMP_ID
    LEFT JOIN KETTLE_MASTER_DUMP.EMPLOYEE_DETAIL ed3 ON od.CANCELLED_BY = ed3.EMP_ID
    LEFT JOIN (SELECT 
        od.ORDER_ID, SUM(oi.QUANTITY) AS QUANTITY
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    LEFT JOIN KETTLE_DUMP.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS = 'CANCELLED'
            AND od.ORDER_ID > (SELECT 
                MAX(LAST_ORDER_ID) START_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(:startDate, 1))
            AND od.ORDER_ID <= (SELECT 
                MAX(LAST_ORDER_ID) END_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = :endDate)
    GROUP BY od.ORDER_ID) nt ON od.ORDER_ID = nt.ORDER_ID
    WHERE
        od.ORDER_STATUS = 'CANCELLED'
            AND od.ORDER_ID > (SELECT 
                MAX(LAST_ORDER_ID) START_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(:startDate, 1))
            AND od.ORDER_ID <= (SELECT 
                MAX(LAST_ORDER_ID) END_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = :endDate)
			AND od.UNIT_ID = :unitId) a
					]]>
					</content>
					<params>
						<param name="unitId" displayName="Unit Id"
                               dataType="INTEGER" />
						<param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
					</params>
				</report>
				<report name="Discount Report For Specific Unit For Date Range"
                        executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
    a.*
FROM
    (SELECT 
        od.UNIT_ID,
            ud.UNIT_NAME,
            od.ORDER_SOURCE,
            ud.UNIT_REGION,
            od.GENERATED_ORDER_ID AS ORDER_NO,
            od.BILLING_SERVER_TIME BILL_TIME,
            oi.PRODUCT_ID,
            pd.PRODUCT_NAME,
            oi.PRICE,
            oi.QUANTITY,
            oi.IS_COMPLIMENTARY,
            od.EMP_ID,
            COALESCE(ed.EMP_NAME, 'NA') AS EMP_NAME,
            (CASE
                WHEN oi.IS_COMPLIMENTARY = 'Y' THEN 100
                ELSE 0
            END) AS DISCOUNT_PERCENT,
            COALESCE(oi.COMPLIMENTARY_TYPE_ID, 'NA') AS COMPLIMENTARY_ID,
            COALESCE(cc.COMP_CODE, 'NA') AS REASON_FOR_COMPLIMENTARY,
            ci.CUSTOMER_ID,
            ci.CONTACT_NUMBER,
            ci.FIRST_NAME,
            od.TOTAL_AMOUNT,
            od.TAXABLE_AMOUNT,
            od.DISCOUNT_AMOUNT TOTAL_DISCOUNT_AMOUNT,
            od.PROMOTIONAL_DISCOUNT TOTAL_PROMOTIONAL_DISCOUNT,
            oi.PROMOTIONAL_DISCOUNT ITEM_PROMOTIONAL_DISCOUNT,
            od.SETTLED_AMOUNT,
            od.OFFER_CODE
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    LEFT JOIN KETTLE_DUMP.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    LEFT JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    LEFT JOIN KETTLE_DUMP.COMPLIMENTARY_CODE cc ON cc.COMP_ID = oi.COMPLIMENTARY_TYPE_ID
    LEFT JOIN KETTLE_DUMP.CUSTOMER_INFO ci ON od.CUSTOMER_ID = ci.CUSTOMER_ID
    LEFT JOIN KETTLE_MASTER_DUMP.EMPLOYEE_DETAIL ed ON od.EMP_ID = ed.EMP_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_ID > (SELECT 
                MAX(LAST_ORDER_ID) START_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(:startDate, 1))
            AND od.ORDER_ID <= (SELECT 
                MAX(LAST_ORDER_ID) END_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = :endDate)
			AND od.UNIT_ID = :unitId) a
WHERE
    a.IS_COMPLIMENTARY = 'Y'
        OR a.TOTAL_DISCOUNT_AMOUNT > 0
        OR a.TOTAL_PROMOTIONAL_DISCOUNT > 0
					]]>
					</content>
					<params>
						<param name="unitId" displayName="Unit Id"
                               dataType="INTEGER" />
						<param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
					</params>
				</report>
				<report name="Bill Reprint Report For Specific Unit For Date Range"
                        executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
    a.*
FROM
    (SELECT 
        od.UNIT_ID,
            ud.UNIT_NAME,
            od.ORDER_SOURCE,
            ud.UNIT_REGION,
            od.GENERATED_ORDER_ID AS ORDER_NO,
            od.BILLING_SERVER_TIME AS BILL_TIME,
            od.EMP_ID,
            COALESCE(ed.EMP_NAME, 'NA') AS EMP_NAME,
            ci.CUSTOMER_ID,
            ci.CONTACT_NUMBER,
            ci.FIRST_NAME,
            od.TOTAL_AMOUNT,
            od.TAXABLE_AMOUNT,
            od.SETTLED_AMOUNT,
            od.OFFER_CODE,
            od.PRINT_COUNT
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    LEFT JOIN KETTLE_DUMP.CUSTOMER_INFO ci ON od.CUSTOMER_ID = ci.CUSTOMER_ID
    LEFT JOIN KETTLE_MASTER_DUMP.EMPLOYEE_DETAIL ed ON od.EMP_ID = ed.EMP_ID
    WHERE
		od.PRINT_COUNT > 1
            AND od.ORDER_ID > (SELECT 
                MAX(LAST_ORDER_ID) START_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(:startDate, 1))
            AND od.ORDER_ID <= (SELECT 
                MAX(LAST_ORDER_ID) END_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = :endDate)
			AND od.UNIT_ID = :unitId) a;

]]>
					</content>
					<params>
						<param name="unitId" displayName="Unit Id"
                               dataType="INTEGER" />
						<param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
					</params>
				</report>
				<report name="Raw Material Consumption Report for Unit"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
    dce.BUSINESS_DATE,
    ud.UNIT_ID,
    ud.UNIT_NAME,
    ai.STATE,
    pd.PRODUCT_ID,
    pd.PRODUCT_NAME,
    dcpv.UOM,
    dcpv.CONSUMPTION
FROM
    KETTLE_SCM_DUMP.DAY_CLOSE_EVENT dce
        LEFT JOIN
    KETTLE_SCM_DUMP.DAY_CLOSE_PRODUCT_VALUES dcpv ON dce.EVENT_ID = dcpv.EVENT_ID
        LEFT JOIN
    KETTLE_SCM_DUMP.PRODUCT_DEFINITION pd ON pd.PRODUCT_ID = dcpv.PRODUCT_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = dce.UNIT_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.ADDRESS_INFO ai ON ud.UNIT_ADDR_ID = ai.ADDRESS_ID
WHERE
    dce.CLOSURE_EVENT_TYPE = 'CLOSING'
        AND dce.STATUS = 'CLOSED'
        AND dce.BUSINESS_DATE >= :fromBusinessDate
        AND dce.BUSINESS_DATE <= :toBusinessDate
		AND ud.UNIT_ID = :unitId
				]]>
					</content>
					<params>
						<param name="unitId" displayName="Unit Id" dataType="INTEGER" />
						<param name="fromBusinessDate" displayName="From Business Date" dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="To Business Date" dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
				<report name="Consolidated Settlement Report for Unit"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
	ucd.BUSINESS_DATE,
    COALESCE(COUNT(od.ORDER_ID), 0) TOTAL_TICKETS,
    ud.UNIT_NAME,
    ud.UNIT_ID,
    COALESCE(SUM(os.AMOUNT_PAID + os.EXTRA_VOUCHERS),
            0) 'Total Sales',
    SUM(CASE
        WHEN pm.MODE_NAME = 'Cash' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'Cash',
    SUM(CASE
        WHEN pm.MODE_NAME = 'CreditDebitCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'Visa/Master Card',
    SUM(CASE
        WHEN pm.MODE_NAME = 'AMEX' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'AMEX Card',
    SUM(CASE
        WHEN pm.MODE_NAME = 'Sodexo' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'Sodexo Coupon',
    SUM(CASE
        WHEN pm.MODE_NAME = 'TicketRestaurant' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'Ticket Restaurant Coupon',
    SUM(CASE
        WHEN pm.MODE_NAME = 'Credit' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'Credit',
    SUM(CASE
        WHEN pm.MODE_NAME = 'SodexoCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'Sodexo Card',
    SUM(CASE
        WHEN pm.MODE_NAME = 'TicketRestaurantCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'Ticket Restaurant Card',
    SUM(CASE
        WHEN pm.MODE_NAME = 'GiftCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'Gift Card',
    SUM(CASE
        WHEN pm.MODE_NAME = 'Paytm' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'Paytm',
SUM(CASE
        WHEN pm.MODE_NAME = 'RazorPay' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'RazorPay',
SUM(CASE
        WHEN pm.MODE_NAME = 'PayTmOnline' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'PayTmOnline',
SUM(CASE
        WHEN pm.MODE_NAME = 'Mobikwik' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'Mobikwik',
SUM(CASE
        WHEN pm.MODE_NAME = 'FreeCharge' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'FreeCharge'
FROM
    (SELECT 
        *
    FROM
        KETTLE_DUMP.UNIT_CLOSURE_DETAILS
    WHERE
        BUSINESS_DATE >=  :fromBusinessDate
        AND BUSINESS_DATE <=  :toBusinessDate
            AND CURRENT_STATUS <> 'CANCELLED') ucd
        INNER JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ucd.UNIT_ID = ud.UNIT_ID
        LEFT JOIN
    KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID > ucd.START_ORDER_ID
        AND od.ORDER_ID <= ucd.LAST_ORDER_ID
        AND od.UNIT_ID = ucd.UNIT_ID
        AND od.ORDER_STATUS <> 'CANCELLED'
        LEFT JOIN
    KETTLE_DUMP.ORDER_SETTLEMENT os ON od.ORDER_ID = os.ORDER_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.PAYMENT_MODE pm ON os.PAYMENT_MODE_ID = pm.PAYMENT_MODE_ID
    WHERE ud.UNIT_ID = :unitId
GROUP BY ud.UNIT_NAME , ud.UNIT_ID ,ucd.BUSINESS_DATE;
						]]>
					</content>
					<params>
						<param name="unitId" displayName="Unit Id" dataType="INTEGER" />
						<param name="fromBusinessDate" displayName="From Business Date" dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="To Business Date" dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
				<report name="Bill level Discount Report for Unit"
                              		executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
    A.ORDER_ID,
    A.UNIT_ID,
    UNIT_NAME,
    ORDER_SOURCE,
    BILLING_SERVER_TIME,
    OFFER_CODE,
    TOTAL_AMOUNT,
    DISCOUNT_AMOUNT,
    TAXABLE_AMOUNT,
    PROMOTIONAL_DISCOUNT,
    SETTLED_AMOUNT,
    SAVING_AMOUNT
FROM
    KETTLE_DUMP.ORDER_DETAIL A
        INNER JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL B ON A.UNIT_ID = B.UNIT_ID
WHERE
    ORDER_STATUS <> 'CANCELLED'
        AND TOTAL_AMOUNT <> 0.00
        AND A.UNIT_ID = :unitId
        AND A.ORDER_ID > (SELECT 
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:fromBusinessDate, 1))
        AND A.ORDER_ID <= (SELECT 
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)
						]]>
					</content>
					<params>
						<param name="unitId" displayName="Unit Id" dataType="INTEGER" />
						<param name="fromBusinessDate" displayName="From Business Date" dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="To Business Date" dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
			<report name="Consolidated Discount Report for Units"
                              		executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[

SELECT A.UNIT_ID,
B.UNIT_NAME,
A.ORDER_ID,
A.GENERATED_ORDER_ID,
A.ORDER_SOURCE,
DATE(A.BILLING_SERVER_TIME) ORDER_DATE,
TIME(A.BILLING_SERVER_TIME) ORDER_TIME,
A.TAXABLE_AMOUNT,
A.OFFER_CODE,
A.DISCOUNT_REASON,
A.EMP_ID,
D.EMP_NAME,
C.CONTACT_NUMBER,
C.FIRST_NAME,
COALESCE(A.TOTAL_AMOUNT,0) SALES_AMOUNT,
(coalesce(A.PROMOTIONAL_DISCOUNT,0)+COALESCE(A.DISCOUNT_AMOUNT,0))DISCOUNT,
(coalesce(A.SERVICE_TAX_AMOUNT,0)+coalesce(A.MRP_VAT_AMOUNT,0)+coalesce(A.NET_PRICE_VAT_AMOUNT,0)+coalesce(A.SURCHARGE_TAX_AMOUNT,0)+
coalesce(A.SERVICE_CHARGE_AMOUNT,0)+coalesce(A.SB_CESS_AMOUNT,0)+ coalesce(A.KK_CESS_AMOUNT,0))TAX_AMOUNT,
coalesce(A.SETTLED_AMOUNT,0)TOTAL_AMOUNT
FROM KETTLE_DUMP.ORDER_DETAIL A
INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL B
INNER JOIN KETTLE_DUMP.CUSTOMER_INFO C ON A.CUSTOMER_ID=C.CUSTOMER_ID
INNER JOIN KETTLE_MASTER_DUMP.EMPLOYEE_DETAIL D ON A.EMP_ID = D.EMP_ID
 WHERE
 A.ORDER_STATUS <> 'CANCELLED'
 AND A.TOTAL_AMOUNT<>0.00
 AND A.UNIT_ID = :unitId
 AND A.ORDER_ID > (SELECT
 MAX(LAST_ORDER_ID) END_ORDER_ID
 FROM
 KETTLE_DUMP.UNIT_CLOSURE_DETAILS
 WHERE
 BUSINESS_DATE = :fromBusinessDate)
 AND A.ORDER_ID <= (SELECT
 MAX(LAST_ORDER_ID) END_ORDER_ID
 FROM
 KETTLE_DUMP.UNIT_CLOSURE_DETAILS
 WHERE
 BUSINESS_DATE = :toBusinessDate)
 AND OFFER_CODE IS NOT NULL
 GROUP BY ORDER_ID;



					]]></content>				
					<params>
						<param name="unitId" displayName="Unit Id" dataType="INTEGER" />
						<param name="fromBusinessDate" displayName="From Business Date" dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="To Business Date" dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
				<report name="Order Details for Bill Number"
                              		executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
*
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        LEFT JOIN
    KETTLE_DUMP.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
WHERE
    GENERATED_ORDER_ID = :orderId
;
					]]></content>				
					<params>
						<param name="orderId" displayName="Bill Number" dataType="STRING" />
					</params>
				</report>

			</reports>
		</category>
	</categories>
</ReportCategories>
