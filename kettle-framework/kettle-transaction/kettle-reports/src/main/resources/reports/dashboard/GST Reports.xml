<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<ReportCategories xmlns="http://www.w3schools.com">
	<categories>
		<category name="Transfer Order Reports" type="SuMo" accessCode="SuMo">
			<reports>
				<report name="Transfer Order Summary"
                        executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
    SELECT
    sd.STATE_CODE,
    CONCAT(sd.STATE_CODE,
            '/OST/',
            MIN(CAST(SUBSTRING(too.GENERATED_INVOICE_ID, 8) AS UNSIGNED))) START_INVOICE_ID,
    CONCAT(sd.STATE_CODE,
            '/OST/',
            MAX(CAST(SUBSTRING(too.GENERATED_INVOICE_ID, 8) AS UNSIGNED))) END_INVOICE_ID,
    COUNT(*) TOTAL_ORDERS,
    SUM(CASE
        WHEN too.TRANSFER_ORDER_STATUS = 'CANCELLED' THEN 1
        ELSE 0
    END) CANCELLED_ORDERS
FROM
    KETTLE_SCM_DUMP.TRANSFER_ORDER too,
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud,
    KETTLE_MASTER_DUMP.STATE_DETAIL sd,
    KETTLE_MASTER_DUMP.LOCATION_DETAIL ld
WHERE
    too.GENERATION_UNIT_ID = ud.UNIT_ID
        AND ud.LOCATION_DETAIL_ID = ld.LOCATION_ID
        AND ld.STATE_DETAIL_ID = sd.STATE_DETAIL_ID
        AND too.GENERATION_UNIT_ID <> too.GENERATED_FOR_UNIT_ID
        AND MONTH(too.GENERATION_TIME) = :month
        AND YEAR(too.GENERATION_TIME) = :year
        AND too.TRANSFER_TYPE = 'INVOICE'
GROUP BY sd.STATE_CODE
                        ]]>
					</content>
					<params>
						<param name="month" displayName="Month"
                               dataType="INTEGER"/>
						<param name="year" displayName="Year"
                               dataType="INTEGER"/>
					</params>
				</report>
				<report name="Transfer Order HSN Wise Data"
                        executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
 SELECT
    a.STATE_CODE,
    a.HSN,
    a.DESCRIPTION,
    CASE
        WHEN a.UQC = 'PC' OR a.UQC = 'SACHET' THEN 'PCS-PIECES'
        WHEN a.UQC = 'L' THEN 'KLO-KILOLITRE'
        WHEN a.UQC = 'KG' THEN 'KGS-KILOGRAMS'
    END 'UQC',
    a.TOTAL_QUANTITY,
    a.TOTAL_VALUE,
    a.TAXABLE_VALUE,
    a.INTEGRATED_TAX_AMOUNT,
    a.CENTRAL_TAX_AMOUNT,
    a.STATE_UT_TAX_AMOUNT,
    a.TOTAL_CESS
FROM
    (SELECT
        sd.STATE_CODE 'STATE_CODE',
            toi.TAX_CODE 'HSN',
            toi.SKU_NAME 'DESCRIPTION',
            toi.UNIT_OF_MEASURE 'UQC',
            SUM(toi.TRANSFERRED_QUANTITY) 'TOTAL_QUANTITY',
            SUM(toi.CALCULATED_AMOUNT + COALESCE(toi.TOTAL_TAX, 0)) 'TOTAL_VALUE',
            SUM(toi.CALCULATED_AMOUNT) 'TAXABLE_VALUE',
            SUM(COALESCE(toi.TOTAL_TAX, 0)) 'INTEGRATED_TAX_AMOUNT',
            0.00 'CENTRAL_TAX_AMOUNT',
            0.00 'STATE_UT_TAX_AMOUNT',
            0.00 'TOTAL_CESS'
    FROM
        KETTLE_SCM_DUMP.TRANSFER_ORDER too, KETTLE_SCM_DUMP.TRANSFER_ORDER_ITEM toi, KETTLE_MASTER_DUMP.UNIT_DETAIL ud, KETTLE_MASTER_DUMP.STATE_DETAIL sd, KETTLE_MASTER_DUMP.LOCATION_DETAIL ld
    WHERE
        too.TRANSFER_ORDER_ID = toi.TRANSFER_ORDER_ID
            AND too.GENERATION_UNIT_ID = ud.UNIT_ID
            AND ud.LOCATION_DETAIL_ID = ld.LOCATION_ID
            AND ld.STATE_DETAIL_ID = sd.STATE_DETAIL_ID
            AND too.TRANSFER_ORDER_STATUS <> 'CANCELLED'
            AND too.GENERATION_UNIT_ID <> too.GENERATED_FOR_UNIT_ID
            AND too.GENERATION_TIME > :startDate
            AND too.GENERATION_TIME < DATE_ADD(:endDate, INTERVAL 1 DAY)
            AND too.TRANSFER_TYPE = 'INVOICE'
    GROUP BY sd.STATE_CODE , toi.TAX_CODE , toi.SKU_NAME , toi.UNIT_OF_MEASURE
    ORDER BY sd.STATE_CODE , toi.TAX_CODE , toi.SKU_NAME) a
                        
                        ]]>
					</content>
					<params>
                        <param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
                        <param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
                    </params>
				</report>

	<report name="Transfer Order HSN Wise Company wise Data"
                        executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT
	a.COMPANY_NAME,
    a.STATE_CODE,
    a.HSN,
    a.DESCRIPTION,
    CASE
        WHEN a.UQC = 'PC' OR a.UQC = 'SACHET' THEN 'PCS-PIECES'
        WHEN a.UQC = 'L' THEN 'KLO-KILOLITRE'
        WHEN a.UQC = 'KG' THEN 'KGS-KILOGRAMS'
    END 'UQC',
    a.TOTAL_QUANTITY,
    a.TOTAL_VALUE,
    a.TAXABLE_VALUE,
    a.INTEGRATED_TAX_AMOUNT,
    a.CENTRAL_TAX_AMOUNT,
    a.STATE_UT_TAX_AMOUNT,
    a.TOTAL_CESS
FROM
    (SELECT
		cd.COMPANY_NAME,
        sd.STATE_CODE 'STATE_CODE',
            toi.TAX_CODE 'HSN',
            toi.SKU_NAME 'DESCRIPTION',
            toi.UNIT_OF_MEASURE 'UQC',
            SUM(toi.TRANSFERRED_QUANTITY) 'TOTAL_QUANTITY',
            SUM(toi.CALCULATED_AMOUNT + COALESCE(toi.TOTAL_TAX, 0)) 'TOTAL_VALUE',
            SUM(toi.CALCULATED_AMOUNT) 'TAXABLE_VALUE',
            SUM(COALESCE(toi.TOTAL_TAX, 0)) 'INTEGRATED_TAX_AMOUNT',
            0.00 'CENTRAL_TAX_AMOUNT',
            0.00 'STATE_UT_TAX_AMOUNT',
            0.00 'TOTAL_CESS'
    FROM
        KETTLE_SCM_DUMP.TRANSFER_ORDER too, KETTLE_SCM_DUMP.TRANSFER_ORDER_ITEM toi, KETTLE_MASTER_DUMP.UNIT_DETAIL ud, KETTLE_MASTER_DUMP.STATE_DETAIL sd, KETTLE_MASTER_DUMP.LOCATION_DETAIL ld, KETTLE_MASTER_DUMP.COMPANY_DETAIL cd
    WHERE
        too.TRANSFER_ORDER_ID = toi.TRANSFER_ORDER_ID
            AND too.GENERATION_UNIT_ID = ud.UNIT_ID
            AND ud.LOCATION_DETAIL_ID = ld.LOCATION_ID
            AND ld.STATE_DETAIL_ID = sd.STATE_DETAIL_ID
            AND too.TRANSFER_ORDER_STATUS <> 'CANCELLED'
            AND too.GENERATION_UNIT_ID <> too.GENERATED_FOR_UNIT_ID
            AND too.GENERATION_TIME > :startDate
            AND too.GENERATION_TIME < DATE_ADD(:endDate, INTERVAL 1 DAY)
            AND too.TRANSFER_TYPE = 'INVOICE'
            AND cd.COMPANY_ID = too.SOURCE_COMPANY_ID
    GROUP BY sd.STATE_CODE , toi.TAX_CODE , toi.SKU_NAME , toi.UNIT_OF_MEASURE, cd.COMPANY_ID
    ORDER BY sd.STATE_CODE , toi.TAX_CODE , toi.SKU_NAME, cd.COMPANY_ID) a
                         


                        
                        ]]>
					</content>
					<params>
                        <param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
                        <param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
                    </params>
				</report>

<report name="TO Taxation For Date Range"
                        executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[
SELECT * FROM (
SELECT
   ud.UNIT_ID SENDING_UNIT_ID,
   ud.UNIT_NAME SENDING_UNIT,
   sd.STATE SENDING_UNIT_STATE,
   sd.STATE_CODE SENDING_UNIT_STATE_CODE,
   ud1.UNIT_ID RECEIVING_UNIT_ID,
   ud1.UNIT_NAME RECEIVING_UNIT,
   sd1.STATE RECEIVING_UNIT_STATE,
   sd1.STATE_CODE RECEIVING_UNIT_STATE_CODE,
   a.GENERATED_INVOICE_ID,
   a.TRANSFER_ORDER_DATE,
   a.TAX_CATEGORY,
   a.TAXABLE_AMOUNT,
   ROUND(a.TOTAL_TAX / a.TAXABLE_AMOUNT * 100.00,
           2) TAX_PERCENTAGE,
   a.TOTAL_TAX
FROM
   (SELECT
       t.GENERATION_UNIT_ID SENDING_UNIT_ID,
           t.GENERATED_FOR_UNIT_ID RECEIVING_UNIT_ID,
           t.GENERATED_INVOICE_ID GENERATED_INVOICE_ID,
           toi.TAX_CATEGORY,
           DATE(t.INITIATION_TIME) TRANSFER_ORDER_DATE,
           SUM(toi.TAXABLE_AMOUNT) TAXABLE_AMOUNT,
           SUM(toi.TOTAL_TAX) TOTAL_TAX
   FROM
       KETTLE_SCM_DUMP.TRANSFER_ORDER t, KETTLE_SCM_DUMP.TRANSFER_ORDER_ITEM_INVOICE toi
   WHERE
       t.TRANSFER_ORDER_ID = toi.ORDER_ID
           AND t.TRANSFER_ORDER_STATUS <> 'CANCELLED'
           AND t.INITIATION_TIME > :startDate
           AND t.INITIATION_TIME < :endDate
   GROUP BY t.GENERATION_UNIT_ID , t.GENERATED_FOR_UNIT_ID , t.GENERATED_INVOICE_ID , toi.TAX_CATEGORY, TRANSFER_ORDER_DATE) a,
   KETTLE_MASTER_DUMP.UNIT_DETAIL ud,
   KETTLE_MASTER_DUMP.LOCATION_DETAIL ld,
   KETTLE_MASTER_DUMP.STATE_DETAIL sd,
   KETTLE_MASTER_DUMP.UNIT_DETAIL ud1,
   KETTLE_MASTER_DUMP.LOCATION_DETAIL ld1,
   KETTLE_MASTER_DUMP.STATE_DETAIL sd1
WHERE
   ud.UNIT_ID = a.SENDING_UNIT_ID
       AND ud.LOCATION_DETAIL_ID = ld.LOCATION_ID
       AND ld.STATE_DETAIL_ID = sd.STATE_DETAIL_ID
       AND ud1.UNIT_ID = a.RECEIVING_UNIT_ID
       AND ud1.LOCATION_DETAIL_ID = ld1.LOCATION_ID
       AND ld1.STATE_DETAIL_ID = sd1.STATE_DETAIL_ID) a
			]]>
                    </content>
                    <params>
                        <param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
                        <param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
                    </params>
                </report>


      <report name="TO Taxation Category wise For Date Range"
                        executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[

SELECT * from (SELECT
   t.TRANSFER_ORDER_ID,
   t.GENERATION_UNIT_ID SENDING_UNIT_ID,
   ud.UNIT_NAME SENDING_UNIT_NAME,
   t.GENERATED_FOR_UNIT_ID RECEIVING_UNIT_ID,
   ud1.UNIT_NAME RECEIVING_UNIT_NAME,
   t.GENERATED_INVOICE_ID GENERATED_INVOICE_ID,
   DATE(t.INITIATION_TIME) TRANSFER_ORDER_DATE,
   toi.SKU_ID,
   sku.SKU_NAME,
   toi.TRANSFERRED_QUANTITY,
   cd.CATEGORY_NAME,
   scd.SUB_CATEGORY_NAME,
   tdi.TAXABLE_AMOUNT,
   tdi.TOTAL_TAX
FROM
   KETTLE_SCM_DUMP.TRANSFER_ORDER t
        INNER JOIN
   KETTLE_SCM_DUMP.TRANSFER_ORDER_ITEM toi ON toi.TRANSFER_ORDER_ID = t.TRANSFER_ORDER_ID
       INNER JOIN
    KETTLE_SCM_DUMP.TRANSFER_ORDER_ITEM_TAX_DETAIL tdi ON tdi.ORDER_ITEM_ID = toi.TRANSFER_ORDER_ITEM_ID
       INNER JOIN
   KETTLE_SCM_DUMP.SKU_DEFINITION sku ON sku.SKU_ID = toi.SKU_ID
        INNER JOIN
   KETTLE_SCM_DUMP.PRODUCT_DEFINITION pd ON pd.PRODUCT_ID = sku.LINKED_PRODUCT_ID
       INNER JOIN
   KETTLE_SCM_DUMP.CATEGORY_DEFINITION cd ON pd.CATEGORY_ID = cd.CATEGORY_ID
       INNER JOIN
   KETTLE_SCM_DUMP.SUB_CATEGORY_DEFINITION scd ON pd.SUB_CATEGORY_ID = scd.SUB_CATEGORY_ID
        INNER JOIN
    KETTLE_SCM_DUMP.UNIT_DETAIL ud on ud.UNIT_ID=t.GENERATION_UNIT_ID
        INNER JOIN
    KETTLE_SCM_DUMP.UNIT_DETAIL ud1 on ud1.UNIT_ID=t.GENERATED_FOR_UNIT_ID
WHERE t.TRANSFER_ORDER_STATUS <> 'CANCELLED'
       AND t.INITIATION_TIME > :startDate
       AND t.INITIATION_TIME < :endDate) s]]>
                    </content>
                    <params>
                        <param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
                        <param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
                    </params>
                </report>




	<report name="TO Taxation For Date Range - Grouped on Tax%"
                        executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[
SELECT 
    SENDING_UNIT_ID,
    SENDING_UNIT,
    SENDING_UNIT_STATE,
    SENDING_UNIT_STATE_CODE,
    RECEIVING_UNIT_ID,
    RECEIVING_UNIT,
    RECEIVING_UNIT_STATE,
    RECEIVING_UNIT_STATE_CODE,
    RECEIVING_UNIT_GSTIN,
    CONCAT(RECEIVING_UNIT_STATE_CODE,
            '-',
            RECEIVING_UNIT_STATE) PLACE_OF_SUPPLY,
    GENERATED_INVOICE_ID,
    DATE_FORMAT(TRANSFER_ORDER_DATE, '%d-%b-%Y') TRANSFER_ORDER_DATE,
    SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
    TAX_PERCENTAGE,
    SUM(TOTAL_TAX) TOTAL_TAX,
    INVOICE_AMOUNT
FROM
    (SELECT 
        ud.UNIT_ID SENDING_UNIT_ID,
            ud.UNIT_NAME SENDING_UNIT,
            sd.STATE SENDING_UNIT_STATE,
            sd.STATE_CODE SENDING_UNIT_STATE_CODE,
            ud1.UNIT_ID RECEIVING_UNIT_ID,
            ud1.UNIT_NAME RECEIVING_UNIT,
            sd1.STATE RECEIVING_UNIT_STATE,
            sd1.STATE_CODE RECEIVING_UNIT_STATE_CODE,
            ud1.GSTIN RECEIVING_UNIT_GSTIN,
            a.GENERATED_INVOICE_ID,
            a.TRANSFER_ORDER_DATE,
            a.TAX_CATEGORY,
            ROUND(a.TAXABLE_AMOUNT, 2) TAXABLE_AMOUNT,
            ROUND(a.TOTAL_TAX / a.TAXABLE_AMOUNT * 100.00) TAX_PERCENTAGE,
            ROUND(a.TOTAL_TAX, 2) TOTAL_TAX,
            ROUND(a.INVOICE_AMOUNT, 2) INVOICE_AMOUNT
    FROM
        (SELECT 
        t.GENERATION_UNIT_ID SENDING_UNIT_ID,
            t.GENERATED_FOR_UNIT_ID RECEIVING_UNIT_ID,
            t.GENERATED_INVOICE_ID GENERATED_INVOICE_ID,
            toi.TAX_CATEGORY,
            DATE(t.INITIATION_TIME) TRANSFER_ORDER_DATE,
            SUM(toi.TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(toi.TOTAL_TAX) TOTAL_TAX,
            toi2.INVOICE_AMOUNT
    FROM
        KETTLE_SCM_DUMP.TRANSFER_ORDER t, KETTLE_SCM_DUMP.TRANSFER_ORDER_ITEM_INVOICE toi, (SELECT 
        a.ORDER_ID,
            SUM(a.TAXABLE_AMOUNT + a.TOTAL_TAX) INVOICE_AMOUNT
    FROM
        KETTLE_SCM_DUMP.TRANSFER_ORDER_ITEM_INVOICE a
    INNER JOIN KETTLE_SCM_DUMP.TRANSFER_ORDER b ON a.ORDER_ID = b.TRANSFER_ORDER_ID
    WHERE
        b.TRANSFER_ORDER_STATUS <> 'CANCELLED'
            AND b.INITIATION_TIME > :startDate
            AND b.INITIATION_TIME < :endDate
    GROUP BY a.ORDER_ID) toi2
    WHERE
        t.TRANSFER_ORDER_ID = toi.ORDER_ID
            AND t.TRANSFER_ORDER_ID = toi2.ORDER_ID
            AND t.TRANSFER_ORDER_STATUS <> 'CANCELLED'
            AND t.INITIATION_TIME > :startDate
            AND t.INITIATION_TIME < :endDate
    GROUP BY t.GENERATION_UNIT_ID , t.GENERATED_FOR_UNIT_ID , t.GENERATED_INVOICE_ID , toi.TAX_CATEGORY , TRANSFER_ORDER_DATE) a, KETTLE_MASTER_DUMP.UNIT_DETAIL ud, KETTLE_MASTER_DUMP.LOCATION_DETAIL ld, KETTLE_MASTER_DUMP.STATE_DETAIL sd, KETTLE_MASTER_DUMP.UNIT_DETAIL ud1, KETTLE_MASTER_DUMP.LOCATION_DETAIL ld1, KETTLE_MASTER_DUMP.STATE_DETAIL sd1
    WHERE
        ud.UNIT_ID = a.SENDING_UNIT_ID
            AND ud.LOCATION_DETAIL_ID = ld.LOCATION_ID
            AND ld.STATE_DETAIL_ID = sd.STATE_DETAIL_ID
            AND ud1.UNIT_ID = a.RECEIVING_UNIT_ID
            AND ud1.LOCATION_DETAIL_ID = ld1.LOCATION_ID
            AND ld1.STATE_DETAIL_ID = sd1.STATE_DETAIL_ID) b
GROUP BY GENERATED_INVOICE_ID , TAX_PERCENTAGE
ORDER BY GENERATED_INVOICE_ID
			]]>
                    </content>
                    <params>
                        <param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
                        <param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
                    </params>
                </report>
				<report name="ZERO tax products TO Taxation For Date Range"
                        executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[

SELECT 
    *
FROM
    (SELECT 
        cd1.COMPANY_NAME SENDING_COMPANY,
            sd.STATE SENDING_UNIT_STATE,
            sd.STATE_CODE SENDING_UNIT_STATE_CODE,
            cd2.COMPANY_NAME RECEIVING_COMPANY,
            sd1.STATE RECEIVING_STATE,
            sd1.STATE_CODE RECEIVING_STATE_CODE,
            MONTHNAME(t.INITIATION_TIME) MONTH_NAME,
            ROUND(SUM(toi.CALCULATED_AMOUNT), 2) TOTAL_AMOUNT
    FROM
        KETTLE_SCM_DUMP.TRANSFER_ORDER t, KETTLE_SCM_DUMP.TRANSFER_ORDER_ITEM toi, KETTLE_MASTER_DUMP.UNIT_DETAIL ud, KETTLE_MASTER_DUMP.LOCATION_DETAIL ld, KETTLE_MASTER_DUMP.STATE_DETAIL sd, KETTLE_MASTER_DUMP.UNIT_DETAIL ud1, KETTLE_MASTER_DUMP.LOCATION_DETAIL ld1, KETTLE_MASTER_DUMP.STATE_DETAIL sd1, KETTLE_MASTER_DUMP.COMPANY_DETAIL cd1, KETTLE_MASTER_DUMP.COMPANY_DETAIL cd2
    WHERE
        t.TRANSFER_ORDER_ID = toi.TRANSFER_ORDER_ID
            AND ud.UNIT_ID = t.GENERATION_UNIT_ID
            AND ud.LOCATION_DETAIL_ID = ld.LOCATION_ID
            AND ld.STATE_DETAIL_ID = sd.STATE_DETAIL_ID
            AND ud1.UNIT_ID = t.GENERATED_FOR_UNIT_ID
            AND ud1.LOCATION_DETAIL_ID = ld1.LOCATION_ID
            AND ld1.STATE_DETAIL_ID = sd1.STATE_DETAIL_ID
            AND cd1.COMPANY_ID = t.SOURCE_COMPANY_ID
            AND cd2.COMPANY_ID = t.RECEIVING_COMPANY_ID
            AND t.TRANSFER_ORDER_STATUS <> 'CANCELLED'
            AND t.INITIATION_TIME > :startDate
            AND t.INITIATION_TIME < :endDate
            AND toi.TOTAL_TAX IS NULL
    GROUP BY sd.STATE_CODE , sd1.STATE_CODE , cd1.COMPANY_ID , cd2.COMPANY_ID , MONTH(INITIATION_TIME)) a
						]]>
                    </content>
                    <params>
                        <param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
                        <param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
                    </params>
                </report>
			</reports>
		</category>
<category name="Kettle Invoice Reports" type="Kettle" accessCode="SuMo">
			<reports>
				<report name="Kettle Invoice Summary"
                        executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
    SELECT
    sd.STATE_CODE,
    MIN(toi.STATE_INVOICE_ID) START_INVOICE_ID,
    MAX(toi.STATE_INVOICE_ID) END_INVOICE_ID,
    COUNT(*) TOTAL_ORDERS,
    SUM(CASE
        WHEN too.ORDER_STATUS = 'CANCELLED' THEN 1
        ELSE 0
    END) CANCELLED_ORDERS
FROM
    KETTLE_DUMP.ORDER_DETAIL too,
    KETTLE_DUMP.ORDER_ITEM_INVOICE toi, 
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud,
    KETTLE_MASTER_DUMP.STATE_DETAIL sd,
    KETTLE_MASTER_DUMP.LOCATION_DETAIL ld
WHERE
    too.UNIT_ID = ud.UNIT_ID
    AND too.ORDER_ID = toi.ORDER_ID
        AND ud.LOCATION_DETAIL_ID = ld.LOCATION_ID
        AND ld.STATE_DETAIL_ID = sd.STATE_DETAIL_ID
        AND MONTH(too.BILLING_SERVER_TIME) = :month
        AND YEAR(too.BILLING_SERVER_TIME) = :year
GROUP BY sd.STATE_CODE;
                        ]]>
					</content>
					<params>
						<param name="month" displayName="Month"
                               dataType="INTEGER"/>
						<param name="year" displayName="Year"
                               dataType="INTEGER"/>
					</params>
				</report>
				<report name="Kettle HSN Summary"
                        executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
    *
FROM
    (SELECT 
        sd.STATE_CODE,
            too.TAX_CODE 'HSN',
            too.PRODUCT_NAME 'DESCRIPTION',
            'PCS-PIECES' AS 'UQC',
            SUM(too.TOTAL_QUANTITY) TOTAL_QUANTITY,
            SUM(too.TAXABLE_VALUE + too.INTEGRATED_TAX_AMOUNT + too.CENTRAL_TAX_AMOUNT + COALESCE(too.STATE_UT_TAX_AMOUNT,0) + COALESCE(too.TOTAL_CESS,0)) TOTAL_VALUE,
            SUM(too.TAXABLE_VALUE) TAXABLE_VALUE,
            SUM(too.INTEGRATED_TAX_AMOUNT) INTEGRATED_TAX_AMOUNT,
            SUM(too.CENTRAL_TAX_AMOUNT) CENTRAL_TAX_AMOUNT,
            SUM(too.STATE_UT_TAX_AMOUNT) STATE_UT_TAX_AMOUNT,
            SUM(too.TOTAL_CESS) TOTAL_CESS
    FROM
        (SELECT 
        od.UNIT_ID,
            oi.ORDER_ITEM_ID,
            oi.TAX_CODE,
            oi.PRODUCT_NAME,
            SUM(oi.QUANTITY) 'TOTAL_QUANTITY',
            MAX(oit.TOTAL_AMOUNT) 'TOTAL_VALUE',
            MAX(oit.TAXABLE_AMOUNT) 'TAXABLE_VALUE',
            SUM(CASE
                WHEN oit.TAX_CODE = 'IGST' THEN oit.TOTAL_TAX
                ELSE 0
            END) 'INTEGRATED_TAX_AMOUNT',
            SUM(CASE
                WHEN oit.TAX_CODE = 'CGST' THEN oit.TOTAL_TAX
                ELSE 0
            END) 'CENTRAL_TAX_AMOUNT',
            SUM(CASE
                WHEN oit.TAX_CODE = 'SGST/UTGST' THEN oit.TOTAL_TAX
                ELSE 0
            END) 'STATE_UT_TAX_AMOUNT',
            SUM(CASE
                WHEN oit.TAX_CODE NOT IN ('IGST' , 'CGST', 'SGST/UTGST') THEN oit.TOTAL_TAX
                ELSE 0
            END) 'TOTAL_CESS'
    FROM
        KETTLE_DUMP.ORDER_DETAIL od, KETTLE_DUMP.ORDER_ITEM oi, KETTLE_DUMP.ORDER_ITEM_TAX_DETAIL oit
    WHERE
        od.ORDER_ID = oi.ORDER_ID
            AND oi.ORDER_ITEM_ID = oit.ORDER_ITEM_ID
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate
            AND od.ORDER_TYPE IN ('order','paid-employee-meal')
            GROUP BY od.UNIT_ID , oi.ORDER_ITEM_ID , oi.TAX_CODE , oi.PRODUCT_ID) too, KETTLE_MASTER_DUMP.UNIT_DETAIL ud, KETTLE_MASTER_DUMP.STATE_DETAIL sd, KETTLE_MASTER_DUMP.LOCATION_DETAIL ld
    WHERE
        too.UNIT_ID = ud.UNIT_ID
            AND ud.LOCATION_DETAIL_ID = ld.LOCATION_ID
            AND ld.STATE_DETAIL_ID = sd.STATE_DETAIL_ID
    GROUP BY sd.STATE_CODE , too.TAX_CODE , too.PRODUCT_NAME) a
                        ]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
			</reports>
		</category>
		<category name="Kettle Accounts Reports" type="Kettle" accessCode="Kettle">
			<reports>
				<report name="Kettle State Wise Tax Summary"
                        executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT * FROM (
    SELECT 
	sd.STATE,
    oitd.TAX_CODE,
    oitd.TAX_PERCENTAGE,
    SUM(oitd.TAXABLE_AMOUNT) TAXABLE_AMOUNT,
	SUM(oitd.TOTAL_TAX) TOTAL_TAX
 FROM
        KETTLE_DUMP.ORDER_DETAIL od
    INNER JOIN KETTLE_DUMP.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_DUMP.ORDER_ITEM_TAX_DETAIL oitd ON oi.ORDER_ITEM_ID  = oitd.ORDER_ITEM_ID
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE_MASTER_DUMP.LOCATION_DETAIL ld ON ud.LOCATION_DETAIL_ID = ld.LOCATION_ID
    INNER JOIN KETTLE_MASTER_DUMP.STATE_DETAIL sd ON ld.STATE_DETAIL_ID = sd.STATE_DETAIL_ID    
    WHERE
            od.ORDER_TYPE IN ('order','paid-employee-meal')
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate
    GROUP BY sd.STATE_DETAIL_ID, oitd.TAX_CODE,oitd.TAX_PERCENTAGE ) a
                                    ]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
				<report name="Kettle State Wise Tax Summary - with month"
                        executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT * FROM (
    SELECT 
	sd.STATE,
    oitd.TAX_CODE,
    oitd.TAX_PERCENTAGE,
    SUM(oitd.TAXABLE_AMOUNT) TAXABLE_AMOUNT,
	SUM(oitd.TOTAL_TAX) TOTAL_TAX,
    MONTHNAME(od.BUSINESS_DATE) MONTH_NAME
 FROM
        KETTLE_DUMP.ORDER_DETAIL od
    INNER JOIN KETTLE_DUMP.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_DUMP.ORDER_ITEM_TAX_DETAIL oitd ON oi.ORDER_ITEM_ID  = oitd.ORDER_ITEM_ID
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE_MASTER_DUMP.LOCATION_DETAIL ld ON ud.LOCATION_DETAIL_ID = ld.LOCATION_ID
    INNER JOIN KETTLE_MASTER_DUMP.STATE_DETAIL sd ON ld.STATE_DETAIL_ID = sd.STATE_DETAIL_ID    
    WHERE
            od.ORDER_TYPE IN ('order','paid-employee-meal')
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate
	GROUP BY sd.STATE_DETAIL_ID, oitd.TAX_CODE,oitd.TAX_PERCENTAGE,MONTH(od.BUSINESS_DATE)) a
                                    ]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
				<report name="Kettle State Wise Tax Summary for a Unit" executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[

SELECT * FROM (
    SELECT 
	sd.STATE,
    oitd.TAX_CODE,
    oitd.TAX_PERCENTAGE,
    SUM(oitd.TAXABLE_AMOUNT) TAXABLE_AMOUNT,
	SUM(oitd.TOTAL_TAX) TOTAL_TAX,
    MONTHNAME(od.BUSINESS_DATE) MONTH_NAME
 FROM
        KETTLE_DUMP.ORDER_DETAIL od
    INNER JOIN KETTLE_DUMP.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_DUMP.ORDER_ITEM_TAX_DETAIL oitd ON oi.ORDER_ITEM_ID  = oitd.ORDER_ITEM_ID
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE_MASTER_DUMP.LOCATION_DETAIL ld ON ud.LOCATION_DETAIL_ID = ld.LOCATION_ID
    INNER JOIN KETTLE_MASTER_DUMP.STATE_DETAIL sd ON ld.STATE_DETAIL_ID = sd.STATE_DETAIL_ID    
    WHERE
            od.ORDER_TYPE IN ('order','paid-employee-meal')
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate AND od.UNIT_ID = :unitId
	GROUP BY sd.STATE_DETAIL_ID, oitd.TAX_CODE,oitd.TAX_PERCENTAGE,MONTH(od.BUSINESS_DATE)) a
						
						]]>
					</content>


					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="unitId" displayName="Unit ID"
                                          dataType="INTEGER" />
					</params>

				</report>
				<report name="Kettle Unit Wise State Wise Tax Summary (GST) for Date Range" executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
						
SELECT * FROM (
    SELECT 
    ud.UNIT_NAME,
	sd.STATE,
    oitd.TAX_CODE,
    oitd.TAX_PERCENTAGE,
    SUM(oitd.TAXABLE_AMOUNT) TAXABLE_AMOUNT,
	SUM(oitd.TOTAL_TAX) TOTAL_TAX,
    MONTHNAME(od.BUSINESS_DATE) MONTH_NAME
 FROM
        KETTLE_DUMP.ORDER_DETAIL od
    INNER JOIN KETTLE_DUMP.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_DUMP.ORDER_ITEM_TAX_DETAIL oitd ON oi.ORDER_ITEM_ID  = oitd.ORDER_ITEM_ID
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE_MASTER_DUMP.LOCATION_DETAIL ld ON ud.LOCATION_DETAIL_ID = ld.LOCATION_ID
    INNER JOIN KETTLE_MASTER_DUMP.STATE_DETAIL sd ON ld.STATE_DETAIL_ID = sd.STATE_DETAIL_ID    
    WHERE
            od.ORDER_TYPE IN ('order','paid-employee-meal')
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate
	GROUP BY ud.UNIT_ID, oitd.TAX_CODE,oitd.TAX_PERCENTAGE,MONTH(od.BUSINESS_DATE)) a
						]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
				<report name="Kettle Unit Wise State Wise Tax Summary (VAT) for Date Range" executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[

SELECT 
		od.ORDER_ID,
        ud.UNIT_NAME,
        sd.STATE,
            od.TAXABLE_AMOUNT,
            (CASE WHEN oi.PRODUCT_ID IN (1026, 1027, 1048,1056) THEN oi.QUANTITY * oi.PRICE ELSE 0 END ) GIFT_CARD_AMOUNT,
            od.NET_PRICE_VAT_PERCENT,
            od.NET_PRICE_VAT_AMOUNT,
            od.MRP_VAT_PERCENT,
            od.MRP_VAT_AMOUNT,
            od.SERVICE_TAX_PERCENT,
            od.SERVICE_TAX_AMOUNT,
            od.SURCHARGE_TAX_PERCENT,
            od.SURCHARGE_TAX_AMOUNT,
            od.SB_CESS_PERCENT,
            od.SB_CESS_AMOUNT,
            od.KK_CESS_PERCENT,
            od.KK_CESS_AMOUNT,
            od.SETTLED_AMOUNT,
			od.SALE_AMOUNT,
			od.ROUND_OFF_AMOUNT,
            od.SAVING_AMOUNT,
            od.BUSINESS_DATE
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    INNER JOIN KETTLE_DUMP.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    INNER JOIN KETTLE_MASTER_DUMP.LOCATION_DETAIL ld ON ud.LOCATION_DETAIL_ID = ld.LOCATION_ID
    INNER JOIN KETTLE_MASTER_DUMP.STATE_DETAIL sd ON ld.STATE_DETAIL_ID = sd.STATE_DETAIL_ID    
    WHERE
        od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate
            AND (od.ORDER_TYPE IS NULL
            OR od.ORDER_TYPE IN ('order' , 'paid-employee-meal'))
            AND od.ORDER_STATUS <> 'CANCELLED'
    GROUP BY od.ORDER_ID						]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
			</reports>
		</category>
		<category name="SUMO_HSN_DUMP" type="SuMo" accessCode="SuMo">
			<reports>
				<report name="SUMO_PRODUCT_TO_HSN_DUMP"
                        		executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[

SELECT 
    pd.PRODUCT_ID,
    pd.PRODUCT_NAME,
    cd.CATEGORY_NAME,
    scd.SUB_CATEGORY_NAME,
    tcd.CATEGORY_CODE,
    tcd.CATEGORY_DESCRIPTION,
    sd.STATE,
    sd.STATE_CODE,
    ctd.IGST_TAX_RATE,
    ctd.CGST_TAX_RATE,
    ctd.SGST_TAX_RATE
FROM
    KETTLE_SCM_DUMP.PRODUCT_DEFINITION pd
        INNER JOIN
    KETTLE_SCM_DUMP.CATEGORY_DEFINITION cd ON pd.CATEGORY_ID = cd.CATEGORY_ID
        INNER JOIN
    KETTLE_SCM_DUMP.SUB_CATEGORY_DEFINITION scd ON scd.SUB_CATEGORY_ID = pd.SUB_CATEGORY_ID
        INNER JOIN
    KETTLE_MASTER_DUMP.TAX_CATEGORY_DATA tcd ON tcd.CATEGORY_CODE = pd.TAX_CATEGORY_CODE
        INNER JOIN
    KETTLE_MASTER_DUMP.CATEGORY_TAX_DATA ctd ON tcd.TAX_CATEGORY_DATA_ID = ctd.CATEGORY_TAX_DATA_ID
        INNER JOIN
    KETTLE_MASTER_DUMP.STATE_DETAIL sd ON sd.STATE_DETAIL_ID = ctd.STATE_DETAIL_ID
WHERE
    ctd.STATE_DETAIL_ID IN (32 , 31, 8, 15, 28)
						]]>
					</content>
				</report>
			</reports>
		</category>
	</categories>
</ReportCategories>
