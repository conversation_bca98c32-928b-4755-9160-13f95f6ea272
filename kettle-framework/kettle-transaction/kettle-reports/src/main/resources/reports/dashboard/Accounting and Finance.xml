<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<ReportCategories xmlns="http://www.w3schools.com">
	<categories>
		<category name="Accounting and Investor Data" type="Cafe Operations"
                  accessCode="Marketing">
			<reports>
				<report name="Tkts, Sales, Gmv, Discount and Taxes on Day and Cafe wise"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
							  
SELECT A.UNIT_NAME,A.UNIT_ID,A.BUSINESS_DATE,A.TKT, A.SALES,A.DISCOUNT,C.SGST, C.CGST, C.CESS,B.GMV FROM (SELECT
    x.UNIT_ID UNIT_ID,
    x.UNIT_NAME,
    CASE
        WHEN p.GIFT_CARD_TICKETS IS NULL THEN x.TOTAL_TICKET
        ELSE x.TOTAL_TICKET - p.GIFT_CARD_TICKETS
    END TOTAL_TICKET,
     CASE
        WHEN p.GIFT_CARD_TICKETS IS NULL THEN x.TKT
        ELSE x.TKT - p.GIFT_CARD_TICKETS
    END TKT,
    CASE
        WHEN p.GIFT_CARD_AMOUNT IS NULL THEN x.SALES
        ELSE x.SALES - p.GIFT_CARD_AMOUNT
    END SALES,
    x.DISCOUNT + x.PROMOTIONAL AS DISCOUNT,
    x.BUSINESS_DATE
FROM
    (SELECT
        od.UNIT_ID UNIT_ID,
        ud.UNIT_NAME,
            COUNT(*) AS TOTAL_TICKET,
            SUM(CASE
                WHEN od.ORDER_TYPE = 'order' THEN 1
                ELSE 0
            END) AS TKT,
            SUM(COALESCE(od.TAXABLE_AMOUNT, 0)) AS SALES,
            SUM(COALESCE(od.DISCOUNT_AMOUNT, 0)) AS DISCOUNT,
            SUM(COALESCE(od.PROMOTIONAL_DISCOUNT, 0)) AS PROMOTIONAL,
            od.BUSINESS_DATE
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
		 AND od.ORDER_TYPE IN ('order', 'paid-employee-meal')
		AND od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate
   GROUP BY od.UNIT_ID, od.BUSINESS_DATE) x
        LEFT OUTER JOIN
    (SELECT
        a.UNIT_ID,
        a.BUSINESS_DATE,
            SUM(CASE
                WHEN a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT THEN 1
                ELSE 0
            END) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT
    FROM
        (SELECT
        oi.ORDER_ID,
            od.UNIT_ID,
            od.BUSINESS_DATE,
            SUM(oi.QUANTITY * oi.PRICE) GIFT_CARD_AMOUNT,
            od.SETTLED_AMOUNT
    FROM
        KETTLE_DUMP.ORDER_ITEM oi
    INNER JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
        AND od.ORDER_TYPE IN ('order', 'paid-employee-meal')
            AND oi.TAX_CODE = 'GIFT_CARD'
            AND od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate
    GROUP BY oi.ORDER_ID , od.UNIT_ID) a
    GROUP BY a.UNIT_ID, a.BUSINESS_DATE) p ON p.UNIT_ID = x.UNIT_ID AND p.BUSINESS_DATE = x.BUSINESS_DATE) A
    LEFT JOIN 
    (SELECT 
			od.UNIT_ID,
            od.BUSINESS_DATE,
			SUM(CASE
                WHEN
				    oi.TAX_CODE NOT IN ('GIFT_CARD', 'COMBO')
					AND ( IS_COMPLIMENTARY = 'N' 
							OR IS_COMPLIMENTARY IS NULL
							OR (IS_COMPLIMENTARY = 'Y' AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) NOT IN (2100 , 2102, 2105, 2106, 2107)))
                THEN
                    oi.QUANTITY * oi.PRICE
                ELSE 0
            END) GMV
 FROM
        KETTLE_DUMP.ORDER_DETAIL od
    LEFT JOIN KETTLE_DUMP.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
        AND od.ORDER_TYPE = 'order'
        AND od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate
	GROUP BY od.UNIT_ID , od.BUSINESS_DATE) B ON A.UNIT_ID = B.UNIT_ID AND A.BUSINESS_DATE = B.BUSINESS_DATE
LEFT JOIN 

(SELECT 
        od.UNIT_ID,
            od.BUSINESS_DATE,
            SUM(CASE
                WHEN TAX_CODE = 'SGST/UTGST' THEN otd.TOTAL_TAX
                ELSE 0
            END) 'SGST',
            SUM(CASE
                WHEN TAX_CODE = 'CGST' THEN otd.TOTAL_TAX
                ELSE 0
            END) 'CGST',
            SUM(CASE
                WHEN TAX_CODE = 'CESS1' THEN otd.TOTAL_TAX
                ELSE 0
            END) 'CESS'
    FROM
        KETTLE_DUMP.ORDER_TAX_DETAIL otd
    LEFT JOIN KETTLE_DUMP.ORDER_DETAIL od ON otd.ORDER_ID = od.ORDER_ID
    WHERE
        od.ORDER_TYPE IN ('order','paid-employee-meal')
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate
    GROUP BY od.UNIT_ID , od.BUSINESS_DATE) C
    ON A.UNIT_ID = C.UNIT_ID AND A.BUSINESS_DATE = C.BUSINESS_DATE           
			
			]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
				<report name="Delivery Tkts, Sales, Gmv, Discount and Taxes on Day and Cafe wise"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT ud.UNIT_NAME, od.UNIT_ID,
(CASE
            WHEN
                HOUR(od.BILLING_SERVER_TIME) <= 5
            THEN
                DATE(DATE_ADD(od.BILLING_SERVER_TIME,
                        INTERVAL - 6 HOUR))
            ELSE DATE(od.BILLING_SERVER_TIME)
        END) AS BUSINESS_DATE,
        SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) NET_TICKETS,
        SUM(od.TAXABLE_AMOUNT) AS SALES,
        SUM(od.DISCOUNT_AMOUNT) AS DISCOUNT,
        SUM(od.NET_PRICE_VAT_AMOUNT) AS VAT,
        SUM(od.MRP_VAT_AMOUNT) AS MRP_VAT,
        SUM(od.SERVICE_TAX_AMOUNT) AS SERVICE_TAX,
        SUM(od.SURCHARGE_TAX_AMOUNT) AS SURCHARGE,
        SUM(od.SB_CESS_AMOUNT) AS SB_CESS,
        d.GMV AS GMV
        FROM KETTLE_DUMP.ORDER_DETAIL od 
        LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud
        ON od.UNIT_ID=ud.UNIT_ID
        LEFT JOIN 
        (
        SELECT od.UNIT_ID,  (CASE
            WHEN
                HOUR(od.BILLING_SERVER_TIME) <= 5
            THEN
                DATE(DATE_ADD(od.BILLING_SERVER_TIME,
                        INTERVAL - 6 HOUR))
            ELSE DATE(od.BILLING_SERVER_TIME)
        END) AS BUSINESS_DATE,
SUM(oi.PRICE*oi.QUANTITY) AS GMV
FROM KETTLE_DUMP.ORDER_DETAIL od
LEFT JOIN KETTLE_DUMP.ORDER_ITEM oi
ON od.ORDER_ID=oi.ORDER_ID
WHERE COALESCE(oi.COMPLIMENTARY_TYPE_ID,0) NOT IN ('2100','2102','2105','2106')
AND od.ORDER_SOURCE='COD'
AND od.ORDER_STATUS <> 'CANCELLED'
       
	  AND od.ORDER_ID > (SELECT 
                MAX(LAST_ORDER_ID) START_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(:fromBusinessDate, 1))
            AND od.ORDER_ID <= (SELECT 
                MAX(LAST_ORDER_ID) END_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = :toBusinessDate)
			
            GROUP BY od.UNIT_ID,BUSINESS_DATE) AS d
ON od.UNIT_ID =d.UNIT_ID
 AND     (CASE
            WHEN
                HOUR(od.BILLING_SERVER_TIME) <= 5
            THEN
                DATE(DATE_ADD(od.BILLING_SERVER_TIME,
                        INTERVAL - 6 HOUR))
            ELSE DATE(od.BILLING_SERVER_TIME)
        END)  
 = d.BUSINESS_DATE
        
        WHERE od.ORDER_STATUS <> 'CANCELLED'
        AND od.ORDER_SOURCE='COD'
        
       AND od.ORDER_ID > (SELECT 
                MAX(LAST_ORDER_ID) START_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(:fromBusinessDate, 1))
            AND od.ORDER_ID <= (SELECT 
                MAX(LAST_ORDER_ID) END_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = :toBusinessDate)
        
        GROUP BY ud.UNIT_NAME,od.UNIT_ID,BUSINESS_DATE;
			]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>		   

				<report name="Unit wise total Item consumption"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[

SELECT 
    ud.UNIT_NAME,
    (CASE
        WHEN
            HOUR(od.BILLING_SERVER_TIME) <= 5
        THEN
            DATE(DATE_ADD(BILLING_SERVER_TIME,
                    INTERVAL - 6 HOUR))
        ELSE DATE(od.BILLING_SERVER_TIME)
    END) AS BUSINESS_DATE,
    pd.PRODUCT_NAME,
    rl.RTL_CODE,
    oi.DIMENSION,
	SUM(oi.QUANTITY) TOTAL_QUANTITY
FROM
    KETTLE_DUMP.ORDER_DETAIL od
   LEFT JOIN KETTLE_DUMP.ORDER_ITEM oi
    on od.ORDER_ID = oi.ORDER_ID
   LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud
    ON ud.UNIT_ID = od.UNIT_ID
   LEFT JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd
    ON pd.PRODUCT_ID=oi.PRODUCT_ID
   LEFT JOIN KETTLE_MASTER_DUMP.REF_LOOKUP_TYPE rl
    ON pd.PRODUCT_TYPE=rl.RTL_ID
WHERE
        od.ORDER_STATUS <> 'CANCELLED'
		
      AND od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:fromBusinessDate,1))
        AND od.ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)
			
GROUP BY ud.UNIT_NAME ,BUSINESS_DATE, pd.PRODUCT_NAME , rl.RTL_CODE, oi.DIMENSION
ORDER BY ud.UNIT_NAME ,BUSINESS_DATE, pd.PRODUCT_NAME , oi.DIMENSION

 ]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>

				<report name="New report for total Item consumption"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[		
SELECT 
    ud.UNIT_NAME,
    (CASE
        WHEN
            HOUR(od.BILLING_SERVER_TIME) <= 5
        THEN
            DATE(DATE_ADD(BILLING_SERVER_TIME,
                    INTERVAL - 6 HOUR))
        ELSE DATE(od.BILLING_SERVER_TIME)
    END) AS BUSINESS_DATE,
    pd.PRODUCT_NAME,
    rl.RTL_CODE,
    oi.DIMENSION,
    oi.PRICE,
    
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID,0) =2101
        THEN
            oi.QUANTITY
        ELSE 0
    END) LOYALTY_QUANTITY_ACCOUNTABLE,
    
      SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID,0) =2104
        THEN
            oi.QUANTITY
        ELSE 0
    END) COD_FIRST_ORDER_QUANTITY_ACCOUNTABLE,
    
       SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID,0) =2103
        THEN
            oi.QUANTITY
        ELSE 0
    END) OTHER_QUANTITY_ACCOUNTABLE,    
    
    SUM(CASE
        WHEN IS_COMPLIMENTARY = 'N' OR IS_COMPLIMENTARY IS NULL
		THEN oi.QUANTITY
        ELSE 0
    END) SOLD_QUANTITY,
    
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID,0) =2100
        THEN
            oi.QUANTITY
        ELSE 0
    END) EMPLOYEE_MEAL_QUANTITY_NON_ACCOUNTABLE,
    
     SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID,0) =2102
        THEN
            oi.QUANTITY
        ELSE 0
    END) UNSATISFIED_QUANTITY__NON_ACCOUNTABLE,
    
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID,0) =2105
        THEN
            oi.QUANTITY
        ELSE 0
    END) TRAINING_QUANTITY_NON_ACCOUNTABLE,  
    
        SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID,0) =2106
        THEN
            oi.QUANTITY
        ELSE 0
    END) SAMPLING_MARKETING_QUANTITY_NON_ACCOUNTABLE,
    
    
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID,0) =2101
        THEN
            oi.QUANTITY*oi.PRICE
        ELSE 0
    END) LOYALTY_GMV_ACCOUNTABLE,
    
 SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID,0) =2104
        THEN
            oi.QUANTITY*oi.PRICE
        ELSE 0
    END) COD_FIRST_ORDER_GMV_ACCOUNTABLE,


    
       SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID,0) =2103
        THEN
            oi.QUANTITY*oi.PRICE
        ELSE 0
    END) OTHER_GMV_ACCOUNTABLE,
    
 
    SUM(CASE
        WHEN IS_COMPLIMENTARY = 'N' OR IS_COMPLIMENTARY IS NULL
		THEN oi.PRICE * oi.QUANTITY
        ELSE 0
    END) SOLD_QUANTITY_GMV,   
    
   
        SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID,0) =2100
        THEN
            oi.QUANTITY*oi.PRICE
        ELSE 0
    END) EMPLOYEE_GMV_NON_ACCOUNTABLE,
       
        SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID,0) =2102
        THEN
            oi.QUANTITY*oi.PRICE
        ELSE 0
    END) UNSATISFIED_GMV__NON_ACCOUNTABLE,
    
    
  
        SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID,0) =2105
        THEN
            oi.QUANTITY*oi.PRICE
        ELSE 0
    END) TRAINING_GMV_NON_ACCOUNTABLE,
    



        SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID,0) =2106
        THEN
            oi.QUANTITY*oi.PRICE
        ELSE 0
    END) SAMPLING_MARKETING_GMV_NON_ACCOUNTABLE,
        
    SUM(oi.QUANTITY) TOTAL_QUANTITY,
 SUM(oi.PRICE * oi.QUANTITY) TOTAL_AMOUNT_GMV
    

FROM
    KETTLE_DUMP.ORDER_DETAIL od
   LEFT JOIN KETTLE_DUMP.ORDER_ITEM oi
    on od.ORDER_ID = oi.ORDER_ID
   LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud
    ON ud.UNIT_ID = od.UNIT_ID
   LEFT JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd
    ON pd.PRODUCT_ID=oi.PRODUCT_ID
   LEFT JOIN KETTLE_MASTER_DUMP.REF_LOOKUP_TYPE rl
    ON pd.PRODUCT_TYPE=rl.RTL_ID
WHERE
        od.ORDER_STATUS <> 'CANCELLED'
		
      AND od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:fromBusinessDate,1))
        AND od.ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)
			
	GROUP BY ud.UNIT_NAME ,BUSINESS_DATE, pd.PRODUCT_NAME , rl.RTL_CODE,oi.DIMENSION , oi.PRICE
ORDER BY ud.UNIT_NAME ,BUSINESS_DATE, pd.PRODUCT_NAME , oi.DIMENSION

	]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>	
				<report name="Total Item Consumption on the Date, Cafe and Dimension Wise"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[					   
					   
SELECT 
    ud.UNIT_NAME,
    (CASE
        WHEN
            HOUR(od.BILLING_SERVER_TIME) <= 5
        THEN
            DATE(DATE_ADD(BILLING_SERVER_TIME,
                    INTERVAL - 6 HOUR))
        ELSE DATE(od.BILLING_SERVER_TIME)
    END) AS BUSINESS_DATE,
    pd.PRODUCT_NAME,
    rl.RTL_CODE,
    oi.DIMENSION,
    oi.PRICE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID,0) IN (2101,2103,2104)
        THEN
            oi.QUANTITY
        ELSE 0
    END) ACCOUNTABLE_COMPLIMENTARY_QUANTITY,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND   COALESCE(oi.COMPLIMENTARY_TYPE_ID,0)  IN(2100,2102,2105,2106)
        THEN
            oi.QUANTITY
        ELSE 0
    END) NON_ACCOUNTABLE_COMPLIMENTARY_QUANTITY,
    SUM(CASE
        WHEN IS_COMPLIMENTARY = 'N' OR IS_COMPLIMENTARY IS NULL
		THEN oi.QUANTITY
        ELSE 0
    END) SOLD_QUANTITY,
    SUM(oi.QUANTITY) TOTAL_QUANTITY,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND  COALESCE(oi.COMPLIMENTARY_TYPE_ID,0) IN (2101,2103,2104)
        THEN
            oi.TOTAL_AMOUNT
        ELSE 0
    END) ACCOUNTABLE_COMPLIMENTARY_GMV,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
               AND  COALESCE(oi.COMPLIMENTARY_TYPE_ID,0)   IN (2100,2102,2105,2106)
        THEN
            oi.PRICE * oi.QUANTITY
        ELSE 0
    END) NON_ACCOUNTABLE_COMPLIMENTARY_GMV,
    SUM(CASE
        WHEN IS_COMPLIMENTARY = 'N' OR IS_COMPLIMENTARY IS NULL
		THEN oi.PRICE * oi.QUANTITY
        ELSE 0
    END) SOLD_QUANTITY_GMV,
    SUM(oi.PRICE * oi.QUANTITY) TOTAL_AMOUNT_GMV
FROM
    KETTLE_DUMP.ORDER_DETAIL od
   LEFT JOIN KETTLE_DUMP.ORDER_ITEM oi
    on od.ORDER_ID = oi.ORDER_ID
   LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud
    ON ud.UNIT_ID = od.UNIT_ID
   LEFT JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd
    ON pd.PRODUCT_ID=oi.PRODUCT_ID
   LEFT JOIN KETTLE_MASTER_DUMP.REF_LOOKUP_TYPE rl
    ON pd.PRODUCT_TYPE=rl.RTL_ID
WHERE
        od.ORDER_STATUS <> 'CANCELLED'
		
        AND od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:fromBusinessDate,1))
        AND od.ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)
			
GROUP BY ud.UNIT_NAME ,BUSINESS_DATE, pd.PRODUCT_NAME , rl.RTL_CODE,oi.DIMENSION , oi.PRICE
ORDER BY ud.UNIT_NAME ,BUSINESS_DATE, pd.PRODUCT_NAME , oi.DIMENSION

			]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>		   
				<report name="Cafe Item Consumption Only (Exclude Delivery Item Consuption) "
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[					   
SELECT 
    ud.UNIT_NAME,
    (CASE
        WHEN
            HOUR(od.BILLING_SERVER_TIME) <= 5
        THEN
            DATE(DATE_ADD(BILLING_SERVER_TIME,
                    INTERVAL - 6 HOUR))
        ELSE DATE(od.BILLING_SERVER_TIME)
    END) AS BUSINESS_DATE,
    pd.PRODUCT_NAME,
    rl.RTL_CODE,
    oi.DIMENSION,
    oi.PRICE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID,0) IN (2101,2103,2104)
        THEN
            oi.QUANTITY
        ELSE 0
    END) ACCOUNTABLE_COMPLIMENTARY_QUANTITY,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND   COALESCE(oi.COMPLIMENTARY_TYPE_ID,0)  IN(2100,2102,2105,2106)
        THEN
            oi.QUANTITY
        ELSE 0
    END) NON_ACCOUNTABLE_COMPLIMENTARY_QUANTITY,
    SUM(CASE
        WHEN IS_COMPLIMENTARY = 'N' OR IS_COMPLIMENTARY IS NULL
		THEN oi.QUANTITY
        ELSE 0
    END) SOLD_QUANTITY,
    SUM(oi.QUANTITY) TOTAL_QUANTITY,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND  COALESCE(oi.COMPLIMENTARY_TYPE_ID,0) IN (2101,2103,2104)
        THEN
            oi.TOTAL_AMOUNT
        ELSE 0
    END) ACCOUNTABLE_COMPLIMENTARY_GMV,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
               AND  COALESCE(oi.COMPLIMENTARY_TYPE_ID,0)   IN (2100,2102,2105,2106)
        THEN
            oi.PRICE * oi.QUANTITY
        ELSE 0
    END) NON_ACCOUNTABLE_COMPLIMENTARY_GMV,
    SUM(CASE
        WHEN IS_COMPLIMENTARY = 'N' OR IS_COMPLIMENTARY IS NULL
		THEN oi.PRICE * oi.QUANTITY
        ELSE 0
    END) SOLD_QUANTITY_GMV,
    SUM(oi.PRICE * oi.QUANTITY) TOTAL_AMOUNT_GMV
FROM
    KETTLE_DUMP.ORDER_DETAIL od
   LEFT JOIN KETTLE_DUMP.ORDER_ITEM oi
    on od.ORDER_ID = oi.ORDER_ID
   LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud
    ON ud.UNIT_ID = od.UNIT_ID
   LEFT JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd
    ON pd.PRODUCT_ID=oi.PRODUCT_ID
   LEFT JOIN KETTLE_MASTER_DUMP.REF_LOOKUP_TYPE rl
    ON pd.PRODUCT_TYPE=rl.RTL_ID
WHERE
        od.ORDER_STATUS <> 'CANCELLED'
		and od.ORDER_SOURCE='CAFE'
		AND ud.UNIT_CATEGORY='CAFE'
		
      AND od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:fromBusinessDate,1))
        AND od.ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)
			
GROUP BY ud.UNIT_NAME ,BUSINESS_DATE, pd.PRODUCT_NAME , rl.RTL_CODE,oi.DIMENSION , oi.PRICE
ORDER BY ud.UNIT_NAME ,BUSINESS_DATE, pd.PRODUCT_NAME , oi.DIMENSION
			]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
				<report name="New Delivery Item Consumption report"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
    ud.UNIT_NAME,
    (CASE
        WHEN
            HOUR(od.BILLING_SERVER_TIME) <= 5
        THEN
            DATE(DATE_ADD(BILLING_SERVER_TIME,
                    INTERVAL - 6 HOUR))
        ELSE DATE(od.BILLING_SERVER_TIME)
    END) AS BUSINESS_DATE,
    pd.PRODUCT_NAME,
    rl.RTL_CODE,
    oi.DIMENSION,
    oi.PRICE,
    
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID,0) =2101
        THEN
            oi.QUANTITY
        ELSE 0
    END) LOYALTY_QUANTITY_ACCOUNTABLE,
    
      SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID,0) =2104
        THEN
            oi.QUANTITY
        ELSE 0
    END) COD_FIRST_ORDER_QUANTITY_ACCOUNTABLE,
    
       SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID,0) =2103
        THEN
            oi.QUANTITY
        ELSE 0
    END) OTHER_QUANTITY_ACCOUNTABLE,    
    
    SUM(CASE
        WHEN IS_COMPLIMENTARY = 'N' OR IS_COMPLIMENTARY IS NULL
		THEN oi.QUANTITY
        ELSE 0
    END) SOLD_QUANTITY,
    
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID,0) =2100
        THEN
            oi.QUANTITY
        ELSE 0
    END) EMPLOYEE_MEAL_QUANTITY_NON_ACCOUNTABLE,
    
     SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID,0) =2102
        THEN
            oi.QUANTITY
        ELSE 0
    END) UNSATISFIED_QUANTITY__NON_ACCOUNTABLE,
    
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID,0) =2105
        THEN
            oi.QUANTITY
        ELSE 0
    END) TRAINING_QUANTITY_NON_ACCOUNTABLE,  
    
        SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID,0) =2106
        THEN
            oi.QUANTITY
        ELSE 0
    END) SAMPLING_MARKETING_QUANTITY_NON_ACCOUNTABLE,
    
    
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID,0) =2101
        THEN
            oi.QUANTITY*oi.PRICE
        ELSE 0
    END) LOYALTY_GMV_ACCOUNTABLE,
    
 SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID,0) =2104
        THEN
            oi.QUANTITY*oi.PRICE
        ELSE 0
    END) COD_FIRST_ORDER_GMV_ACCOUNTABLE,


    
       SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID,0) =2103
        THEN
            oi.QUANTITY*oi.PRICE
        ELSE 0
    END) OTHER_GMV_ACCOUNTABLE,
    
 
    SUM(CASE
        WHEN IS_COMPLIMENTARY = 'N' OR IS_COMPLIMENTARY IS NULL
		THEN oi.PRICE * oi.QUANTITY
        ELSE 0
    END) SOLD_QUANTITY_GMV,   
    
   
        SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID,0) =2100
        THEN
            oi.QUANTITY*oi.PRICE
        ELSE 0
    END) EMPLOYEE_GMV_NON_ACCOUNTABLE,
       
        SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID,0) =2102
        THEN
            oi.QUANTITY*oi.PRICE
        ELSE 0
    END) UNSATISFIED_GMV__NON_ACCOUNTABLE,
    
    
  
        SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID,0) =2105
        THEN
            oi.QUANTITY*oi.PRICE
        ELSE 0
    END) TRAINING_GMV_NON_ACCOUNTABLE,
    



        SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID,0) =2106
        THEN
            oi.QUANTITY*oi.PRICE
        ELSE 0
    END) SAMPLING_MARKETING_GMV_NON_ACCOUNTABLE,
        
    SUM(oi.QUANTITY) TOTAL_QUANTITY,
 
 SUM(oi.PRICE * oi.QUANTITY) TOTAL_AMOUNT_GMV
    

FROM
    KETTLE_DUMP.ORDER_DETAIL od
   LEFT JOIN KETTLE_DUMP.ORDER_ITEM oi
    on od.ORDER_ID = oi.ORDER_ID
   LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud
    ON ud.UNIT_ID = od.UNIT_ID
   LEFT JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd
    ON pd.PRODUCT_ID=oi.PRODUCT_ID
   LEFT JOIN KETTLE_MASTER_DUMP.REF_LOOKUP_TYPE rl
    ON pd.PRODUCT_TYPE=rl.RTL_ID
WHERE
        od.ORDER_STATUS <> 'CANCELLED'
		and od.ORDER_SOURCE='COD'
		
      AND od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:fromBusinessDate,1))
        AND od.ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)
			
GROUP BY ud.UNIT_NAME ,BUSINESS_DATE, pd.PRODUCT_NAME , rl.RTL_CODE,oi.DIMENSION , oi.PRICE
ORDER BY ud.UNIT_NAME ,BUSINESS_DATE, pd.PRODUCT_NAME , oi.DIMENSION
]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
				<report name="Delivery Item Consumption on the Date, Cafe and Dimension Wise"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[					   
SELECT 
    ud.UNIT_NAME,
    (CASE
        WHEN
            HOUR(od.BILLING_SERVER_TIME) <= 5
        THEN
            DATE(DATE_ADD(BILLING_SERVER_TIME,
                    INTERVAL - 6 HOUR))
        ELSE DATE(od.BILLING_SERVER_TIME)
    END) AS BUSINESS_DATE,
    pd.PRODUCT_NAME,
    rl.RTL_CODE,
    oi.DIMENSION,
    oi.PRICE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID,0) IN (2101,2103,2104)
        THEN
            oi.QUANTITY
        ELSE 0
    END) ACCOUNTABLE_COMPLIMENTARY_QUANTITY,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND   COALESCE(oi.COMPLIMENTARY_TYPE_ID,0)  IN(2100,2102,2105,2106)
        THEN
            oi.QUANTITY
        ELSE 0
    END) NON_ACCOUNTABLE_COMPLIMENTARY_QUANTITY,
    SUM(CASE
        WHEN IS_COMPLIMENTARY = 'N' OR IS_COMPLIMENTARY IS NULL
		THEN oi.QUANTITY
        ELSE 0
    END) SOLD_QUANTITY,
    SUM(oi.QUANTITY) TOTAL_QUANTITY,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND  COALESCE(oi.COMPLIMENTARY_TYPE_ID,0) IN (2101,2103,2104)
        THEN
            oi.TOTAL_AMOUNT
        ELSE 0
    END) ACCOUNTABLE_COMPLIMENTARY_GMV,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
               AND  COALESCE(oi.COMPLIMENTARY_TYPE_ID,0)   IN (2100,2102,2105,2106)
        THEN
            oi.PRICE * oi.QUANTITY
        ELSE 0
    END) NON_ACCOUNTABLE_COMPLIMENTARY_GMV,
    SUM(CASE
        WHEN IS_COMPLIMENTARY = 'N' OR IS_COMPLIMENTARY IS NULL
		THEN oi.PRICE * oi.QUANTITY
        ELSE 0
    END) SOLD_QUANTITY_GMV,
    SUM(oi.PRICE * oi.QUANTITY) TOTAL_AMOUNT_GMV

FROM
    KETTLE_DUMP.ORDER_DETAIL od
   LEFT JOIN KETTLE_DUMP.ORDER_ITEM oi
    on od.ORDER_ID = oi.ORDER_ID
   LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud
    ON ud.UNIT_ID = od.UNIT_ID
   LEFT JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd
    ON pd.PRODUCT_ID=oi.PRODUCT_ID
   LEFT JOIN KETTLE_MASTER_DUMP.REF_LOOKUP_TYPE rl
    ON pd.PRODUCT_TYPE=rl.RTL_ID
WHERE
        od.ORDER_STATUS <> 'CANCELLED'	
		
        AND od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:fromBusinessDate,1))
        AND od.ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)
			
            AND od.ORDER_SOURCE='COD'
GROUP BY ud.UNIT_NAME ,BUSINESS_DATE, pd.PRODUCT_NAME , rl.RTL_CODE,oi.DIMENSION , oi.PRICE
ORDER BY ud.UNIT_NAME ,BUSINESS_DATE, pd.PRODUCT_NAME , oi.DIMENSION
			]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>

				</report>		   
				<report name="Discount Data for Investor"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[					   
SELECT 
    ud.UNIT_NAME AS UNIT_NAME,
    COALESCE(od.DISCOUNT_REASON_ID, 0) AS DISCOUNT_REASON_CODE,
    rl.RL_NAME AS DISCOUNT_REASON,
    COUNT(DISTINCT od.ORDER_ID) AS TICKETS,
    SUM(od.DISCOUNT_AMOUNT) AS AMOUNT
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        LEFT JOIN
    KETTLE_MASTER_DUMP.REF_LOOKUP rl ON od.DISCOUNT_REASON_ID = rl.RL_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
WHERE
    od.ORDER_STATUS <> 'CANCELLED'
        AND COALESCE(od.DISCOUNT_REASON_ID, 0) <> 0
		
 AND od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:fromBusinessDate,1))
        AND od.ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)
			
GROUP BY ud.UNIT_NAME , od.DISCOUNT_REASON_ID , rl.RL_NAME 
UNION ALL SELECT 
    ud.UNIT_NAME,
    COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0),
    cc.NAME,
    COUNT(DISTINCT oi.ORDER_ID),
    SUM(oi.PRICE * oi.QUANTITY)
FROM
    KETTLE_DUMP.ORDER_ITEM oi
        LEFT JOIN
    KETTLE_DUMP.COMPLIMENTARY_CODE cc ON oi.COMPLIMENTARY_TYPE_ID = cc.COMP_ID
        LEFT JOIN
    KETTLE_DUMP.ORDER_DETAIL od ON oi.ORDER_ID = od.ORDER_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
WHERE
    od.ORDER_STATUS <> 'CANCELLED'
        AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) <> 0
		
       AND od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:fromBusinessDate,1))
        AND od.ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)
			
GROUP BY ud.UNIT_NAME , oi.COMPLIMENTARY_TYPE_ID , cc.NAME
ORDER BY UNIT_NAME , DISCOUNT_REASON_CODE
			]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>		   					   
				<report name="Employee Meal Consumption"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[				   
SELECT 
    ud.UNIT_NAME,
    (CASE
        WHEN
            HOUR(od.BILLING_SERVER_TIME) <= 5
        THEN
            DATE(DATE_ADD(BILLING_SERVER_TIME,
                    INTERVAL - 6 HOUR))
        ELSE DATE(od.BILLING_SERVER_TIME)
    END) AS BUSINESS_DATE,
    pd.PRODUCT_NAME,
    oi.DIMENSION,
    SUM(oi.QUANTITY) AS QUANTITY,
    SUM(oi.QUANTITY * oi.PRICE) AS GMV
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        LEFT JOIN
    KETTLE_DUMP.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd ON pd.PRODUCT_ID = oi.PRODUCT_ID
WHERE
    od.ORDER_STATUS <> 'CANCELLED'
        AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2100
		
      AND od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:fromBusinessDate,1))
        AND od.ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)
			
		GROUP BY ud.UNIT_NAME , BUSINESS_DATE , pd.PRODUCT_NAME , oi.DIMENSION
ORDER BY ud.UNIT_NAME , BUSINESS_DATE , pd.PRODUCT_NAME , oi.DIMENSION
			]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>		   					   
				<report name="Employee Meal per Order Consumption"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[	
SELECT ud.UNIT_NAME, od.BILLING_SERVER_TIME, od.ORDER_ID, pd.PRODUCT_NAME, oi.QUANTITY,oi.PRICE, cc.COMP_CODE
FROM KETTLE_DUMP.ORDER_DETAIL od
LEFT JOIN KETTLE_DUMP.ORDER_ITEM oi
ON od.ORDER_ID=oi.ORDER_ID
LEFT JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd
ON oi.PRODUCT_ID=pd.PRODUCT_ID
LEFT JOIN KETTLE_DUMP.COMPLIMENTARY_CODE cc
ON oi.COMPLIMENTARY_TYPE_ID=cc.COMP_ID
            LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud
            ON od.UNIT_ID=ud.UNIT_ID
WHERE od.ORDER_STATUS <> 'CANCELLED'
AND COALESCE(oi.COMPLIMENTARY_TYPE_ID,0)=2100 

 AND od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:fromBusinessDate,1))
        AND od.ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)
]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>	
				<report name="Combos Item Consumption older then 7th July 2016"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[					
SELECT ud.UNIT_NAME,a.BUSINESS_DATE,a.PRODUCT_ID,a.PRODUCT_NAME,rl.RL_CODE,rlt.RTL_CODE,a.QUANTITY
FROM
(SELECT 
        od.UNIT_ID,(CASE
        WHEN
            HOUR(od.BILLING_SERVER_TIME) <= 5
        THEN
            DATE(DATE_ADD(od.BILLING_SERVER_TIME,
                    INTERVAL - 6 HOUR))
        ELSE DATE(od.BILLING_SERVER_TIME)
    END) AS BUSINESS_DATE,
         
            pd1.PRODUCT_ID,pd1.PRODUCT_NAME,
            (CASE
                WHEN pd1.DIMENSION_CODE = 1 THEN 1
                WHEN
                    pd1.DIMENSION_CODE = 2
                        AND od.ORDER_SOURCE = 'CAFE'
                THEN
                    21
                WHEN
                    pd1.DIMENSION_CODE = 2
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    24
                WHEN pd1.DIMENSION_CODE = 3 THEN 30
                WHEN
                    pd1.DIMENSION_CODE = 11
                        AND od.ORDER_SOURCE = 'CAFE'
                THEN
                    1101
                WHEN
                    pd1.DIMENSION_CODE = 11
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1102
                WHEN
                    pd1.DIMENSION_CODE = 34
                        AND od.ORDER_SOURCE = 'CAFE'
                THEN
                    3401
                WHEN
                    pd1.DIMENSION_CODE = 34
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    3402
                ELSE 1
            END) AS DIMENSIONS,
            SUM(oi.QUANTITY) AS QUANTITY
    FROM
        ORDER_DETAIL od
    INNER JOIN ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN ORDER_ITEM_ADDON oia ON oi.ORDER_ITEM_ID = oia.ORDER_ITEM_ID
    INNER JOIN ADDON_PRODUCT_DATA apd ON oia.ADDON_ID = apd.ADDON_ID
    INNER JOIN PRODUCT_DETAIL pd1 ON pd1.PRODUCT_ID = apd.PRODUCT_ID
   
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
 AND od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:fromBusinessDate,1))
        AND od.ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)
            AND pd.PRODUCT_TYPE = 8
            AND apd.ADDON_ID IS NOT NULL
    GROUP BY od.UNIT_ID , BUSINESS_DATE, pd1.PRODUCT_ID,pd1.PRODUCT_NAME , DIMENSIONS) AS a

LEFT JOIN UNIT_DETAIL ud
ON a.UNIT_ID=ud.UNIT_ID
LEFT JOIN REF_LOOKUP rl
ON a.DIMENSIONS=rl.RL_ID
LEFT JOIN PRODUCT_DETAIL pd
ON a.PRODUCT_ID=pd.PRODUCT_ID
LEFT JOIN REF_LOOKUP_TYPE rlt
ON rlt.RTL_ID=pd.PRODUCT_TYPE;			
		]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
				<report name="Combos Item Consumption"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[					
SELECT ud.UNIT_NAME,a.BUSINESS_DATE,a.PRODUCT_ID,a.PRODUCT_NAME,rl.RL_CODE,rlt.RTL_CODE,a.QUANTITY
FROM
(SELECT 
			od.UNIT_ID,(CASE
			WHEN
				HOUR(od.BILLING_SERVER_TIME) <= 5
			THEN
				DATE(DATE_ADD(od.BILLING_SERVER_TIME,
						INTERVAL - 6 HOUR))
			ELSE DATE(od.BILLING_SERVER_TIME)
			END) AS BUSINESS_DATE,
			 
			pd1.PRODUCT_ID,
			pd1.PRODUCT_NAME,
        
            (CASE
                WHEN pd1.DIMENSION_CODE = 1 THEN 1
                WHEN
                    pd1.DIMENSION_CODE = 2
                        AND od.ORDER_SOURCE = 'CAFE'
                THEN
                    21
                WHEN
                    pd1.DIMENSION_CODE = 2
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    24
                WHEN pd1.DIMENSION_CODE = 3 THEN 30
                WHEN
                    pd1.DIMENSION_CODE = 11
                        AND od.ORDER_SOURCE = 'CAFE'
                THEN
                    1101
                WHEN
                    pd1.DIMENSION_CODE = 11
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1102
                WHEN
                    pd1.DIMENSION_CODE = 34
                        AND od.ORDER_SOURCE = 'CAFE'
                THEN
                    3401
                WHEN
                    pd1.DIMENSION_CODE = 34
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    3402
                ELSE 1
            END) AS DIMENSIONS,
            SUM(oi.QUANTITY) AS QUANTITY
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    INNER JOIN KETTLE_DUMP.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd1 ON oi.PRODUCT_ID = pd1.PRODUCT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
 AND od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:fromBusinessDate,1))
        AND od.ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)
            AND pd1.PRODUCT_TYPE = 8
    GROUP BY od.UNIT_ID , BUSINESS_DATE, pd1.PRODUCT_ID,pd1.PRODUCT_NAME , DIMENSIONS) AS a
LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud
ON a.UNIT_ID=ud.UNIT_ID
LEFT JOIN KETTLE_MASTER_DUMP.REF_LOOKUP rl
ON a.DIMENSIONS=rl.RL_ID
LEFT JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd
ON a.PRODUCT_ID=pd.PRODUCT_ID
LEFT JOIN KETTLE_MASTER_DUMP.REF_LOOKUP_TYPE rlt
ON rlt.RTL_ID=pd.PRODUCT_TYPE;		
		]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>	
				<report name="Combos Item Consumption - Products Data"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[					
											
SELECT ud.UNIT_NAME,a.BUSINESS_DATE,a.PRODUCT_ID,a.PRODUCT_NAME,rl.RL_CODE,rlt.RTL_CODE,a.QUANTITY
FROM
(SELECT 
			od.UNIT_ID,(CASE
			WHEN
				HOUR(od.BILLING_SERVER_TIME) <= 5
			THEN
				DATE(DATE_ADD(od.BILLING_SERVER_TIME,
						INTERVAL - 6 HOUR))
			ELSE DATE(od.BILLING_SERVER_TIME)
			END) AS BUSINESS_DATE,
			 
			pd1.PRODUCT_ID,
			pd1.PRODUCT_NAME,
        
            (CASE
                WHEN pd1.DIMENSION_CODE = 1 THEN 1
                WHEN
                    pd1.DIMENSION_CODE = 2
                        AND od.ORDER_SOURCE = 'CAFE'
                THEN
                    21
                WHEN
                    pd1.DIMENSION_CODE = 2
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    24
                WHEN pd1.DIMENSION_CODE = 3 THEN 30
                WHEN
                    pd1.DIMENSION_CODE = 11
                        AND od.ORDER_SOURCE = 'CAFE'
                THEN
                    1101
                WHEN
                    pd1.DIMENSION_CODE = 11
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1102
                WHEN
                    pd1.DIMENSION_CODE = 34
                        AND od.ORDER_SOURCE = 'CAFE'
                THEN
                    3401
                WHEN
                    pd1.DIMENSION_CODE = 34
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    3402
                ELSE 1
            END) AS DIMENSIONS,
            SUM(oi.QUANTITY) AS QUANTITY
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    INNER JOIN KETTLE_DUMP.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd1 ON oi.PRODUCT_ID = pd1.PRODUCT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
 AND od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:fromBusinessDate,1))
        AND od.ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)            
            AND oi.PARENT_ITEM_ID IS NOT NULL
            AND oi.COMBO_CONSTITUENT = 'Y'
    GROUP BY od.UNIT_ID , BUSINESS_DATE, pd1.PRODUCT_ID,pd1.PRODUCT_NAME , DIMENSIONS) AS a

LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud
ON a.UNIT_ID=ud.UNIT_ID
LEFT JOIN KETTLE_MASTER_DUMP.REF_LOOKUP rl
ON a.DIMENSIONS=rl.RL_ID
LEFT JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd
ON a.PRODUCT_ID=pd.PRODUCT_ID
LEFT JOIN KETTLE_MASTER_DUMP.REF_LOOKUP_TYPE rlt
ON rlt.RTL_ID=pd.PRODUCT_TYPE;		

		]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>	
				<report name="Last Monday till date Tkts, Sales, and Delivery Data"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
	ud.UNIT_ID,
    ud.UNIT_NAME,
    COALESCE(SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END),
            0) AS TKT,
    COALESCE(SUM(od.TAXABLE_AMOUNT), 0) AS SALES,
    COALESCE(TRUNCATE(SUM(od.TAXABLE_AMOUNT) / SUM(CASE
                    WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                    ELSE 0
                END),
                0),
            0) AS APC,
    COALESCE(SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END),
            0) AS DELIVERY_TKT,
    COALESCE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END),
            0) AS DELIVERY_SALES,
    COALESCE(TRUNCATE(SUM(CASE
                    WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                    ELSE 0
                END) / SUM(CASE
                    WHEN
                        od.TOTAL_AMOUNT <> '0.00'
                            AND od.ORDER_SOURCE = 'COD'
                    THEN
                        1
                    ELSE 0
                END),
                0),
            0) AS DELIVERY_APC
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        LEFT JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
WHERE
    od.ORDER_STATUS <> 'CANCELLED'
        AND od.BILLING_SERVER_TIME BETWEEN 
        DATE_ADD((
CASE 
WHEN 
DAYOFWEEK(CASE WHEN HOUR(NOW())<=5 THEN SUBDATE(CURRENT_DATE(),1) ELSE CURRENT_DATE() END) =2 THEN 
SUBDATE((CASE WHEN HOUR(NOW())<=5 THEN SUBDATE(CURRENT_DATE(),1) ELSE CURRENT_DATE() END),7) 
WHEN
DAYOFWEEK(CASE WHEN HOUR(NOW())<=5 THEN SUBDATE(CURRENT_DATE(),1) ELSE CURRENT_DATE() END) =3 THEN 
SUBDATE((CASE WHEN HOUR(NOW())<=5 THEN SUBDATE(CURRENT_DATE(),1) ELSE CURRENT_DATE() END),1) 

WHEN
DAYOFWEEK(CASE WHEN HOUR(NOW())<=5 THEN SUBDATE(CURRENT_DATE(),1) ELSE CURRENT_DATE() END) =4 THEN 
SUBDATE((CASE WHEN HOUR(NOW())<=5 THEN SUBDATE(CURRENT_DATE(),1) ELSE CURRENT_DATE() END),2) 
WHEN
DAYOFWEEK(CASE WHEN HOUR(NOW())<=5 THEN SUBDATE(CURRENT_DATE(),1) ELSE CURRENT_DATE() END) =5 THEN 
SUBDATE((CASE WHEN HOUR(NOW())<=5 THEN SUBDATE(CURRENT_DATE(),1) ELSE CURRENT_DATE() END),3) 
WHEN
DAYOFWEEK(CASE WHEN HOUR(NOW())<=5 THEN SUBDATE(CURRENT_DATE(),1) ELSE CURRENT_DATE() END) =6 THEN 
SUBDATE((CASE WHEN HOUR(NOW())<=5 THEN SUBDATE(CURRENT_DATE(),1) ELSE CURRENT_DATE() END),4) 
WHEN
DAYOFWEEK(CASE WHEN HOUR(NOW())<=5 THEN SUBDATE(CURRENT_DATE(),1) ELSE CURRENT_DATE() END) =7 THEN 
SUBDATE((CASE WHEN HOUR(NOW())<=5 THEN SUBDATE(CURRENT_DATE(),1) ELSE CURRENT_DATE() END),5) 
ELSE SUBDATE((CASE WHEN HOUR(NOW())<=5 THEN SUBDATE(CURRENT_DATE(),1) ELSE CURRENT_DATE() END),6) END), INTERVAL 5 HOUR)
        
        AND NOW()
GROUP BY ud.UNIT_NAME
]]>
					</content>
				</report>
				<report name="Orders with Settlement Type Paytm"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
    *
FROM
    (SELECT 
        od.UNIT_ID,
            ud.UNIT_NAME,
            ci.CONTACT_NUMBER,
            ci.FIRST_NAME,
            od.ORDER_ID,
            od.GENERATED_ORDER_ID,
            od.ORDER_STATUS,
            od.CANCELATION_REASON,
            od.BILL_CANCELLATION_TIME,
            od.BILLING_SERVER_TIME,
            od.ORDER_SOURCE,
            od.SETTLED_AMOUNT AS ORDER_AMOUNT,
            os.SETTLEMENT_ID,
            os.PAYMENT_MODE_ID,
            pm.MODE_NAME,
            os.AMOUNT_PAID AS PAID_BY_PAYTM
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    LEFT JOIN KETTLE_DUMP.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
    LEFT JOIN KETTLE_DUMP.ORDER_SETTLEMENT os ON os.ORDER_ID = od.ORDER_ID
    LEFT JOIN KETTLE_MASTER_DUMP.PAYMENT_MODE pm ON os.PAYMENT_MODE_ID = pm.PAYMENT_MODE_ID
    WHERE
        od.ORDER_ID > (SELECT 
                MAX(LAST_ORDER_ID) START_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(:fromBusinessDate, 1))
            AND od.ORDER_ID <= (SELECT 
                MAX(LAST_ORDER_ID) END_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = :toBusinessDate)
            AND os.PAYMENT_MODE_ID = 11) a
						]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
<report name="Chai Chakhna Sales across units" executionType="SQL" returnType="java.lang.String">
<content>
<![CDATA[
SELECT 
    od.UNIT_ID,
    ud.UNIT_NAME,
    pd.PRODUCT_NAME,
    SUM(oi.QUANTITY) AS CCB_QUANTITY,
    SUM(oi.TOTAL_AMOUNT) AS CCB_AMOUNT
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE_DUMP.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd ON pd.PRODUCT_ID = oi.PRODUCT_ID
    WHERE
      od.BUSINESS_DATE BETWEEN :startDate and :endDate AND od.ORDER_STATUS <> 'CANCELLED' AND IS_COMPLIMENTARY<>'Y'
            AND ORDER_TYPE = 'ORDER' AND ud.UNIT_CATEGORY="CAFE" and ud.UNIT_STATUS="ACTIVE"
            AND pd.PRODUCT_ID IN (1164,1165,1166,1167,1168,1169,1170,1171)
    GROUP BY ud.UNIT_ID,pd.PRODUCT_ID order by ud.UNIT_ID;
]]>
</content>
<params>
<param name="startDate" displayName="Start Date" dataType="DATE" format="yyyy-MM-dd" />
<param name="endDate" displayName="End Date" dataType="DATE" format="yyyy-MM-dd" />
</params>
</report>					
			</reports>
		</category>
		<category name="Receivables Bill Report" type="Cafe Operations"
                  accessCode="Marketing">
			<reports>
				<report name="Consolidate Settlement Report"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
	od.BUSINESS_DATE,
    COALESCE(COUNT(od.ORDER_ID), 0) TOTAL_TICKETS,
    ud.UNIT_NAME,
    ud.UNIT_ID,
    COALESCE(SUM(os.AMOUNT_PAID + os.EXTRA_VOUCHERS),
            0) 'Total Sales',
    SUM(CASE
        WHEN pm.MODE_NAME = 'Cash' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'Cash',
    SUM(CASE
        WHEN pm.MODE_NAME = 'CreditDebitCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'Visa/Master Card',
    SUM(CASE
        WHEN pm.MODE_NAME = 'AMEX' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'AMEX Card',
    SUM(CASE
        WHEN pm.MODE_NAME = 'Sodexo' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'Sodexo Coupon',
    SUM(CASE
        WHEN pm.MODE_NAME = 'TicketRestaurant' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'Ticket Restaurant Coupon',
    SUM(CASE
        WHEN pm.MODE_NAME = 'Credit' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'Credit',
    SUM(CASE
        WHEN pm.MODE_NAME = 'SodexoCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'Sodexo Card',
    SUM(CASE
        WHEN pm.MODE_NAME = 'TicketRestaurantCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'Ticket Restaurant Card',
    SUM(CASE
        WHEN pm.MODE_NAME = 'GiftCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'Gift Card',
    SUM(CASE
        WHEN pm.MODE_NAME = 'Paytm' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'Paytm',
    SUM(CASE
        WHEN pm.MODE_NAME = 'RazorPay' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'RazorPay',
    SUM(CASE
        WHEN pm.MODE_NAME = 'PayTmOnline' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'PayTmOnline',
    SUM(CASE
        WHEN pm.MODE_NAME = 'Mobikwik' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'Mobikwik',
    SUM(CASE
        WHEN pm.MODE_NAME = 'FreeCharge' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'FreeCharge',
    SUM(CASE
        WHEN pm.MODE_NAME = 'DineOut' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'DineOut'
    SUM(CASE
        WHEN pm.MODE_NAME = 'DineOut' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'DineOut',
	SUM(CASE
	        WHEN pm.MODE_NAME = 'Prepaid' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
	        ELSE 0
	    END) 'Prepaid',
	SUM(CASE
	        WHEN pm.MODE_NAME = 'PhonePe' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
	        ELSE 0
	    END) 'PhonePe',
	SUM(CASE
	        WHEN pm.MODE_NAME = 'GYFTR' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
	        ELSE 0
	    END) 'GYFTR'
FROM
    KETTLE_DUMP.ORDER_DETAIL od 
    LEFT JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
        LEFT JOIN
    KETTLE_DUMP.ORDER_SETTLEMENT os ON od.ORDER_ID = os.ORDER_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.PAYMENT_MODE pm ON os.PAYMENT_MODE_ID = pm.PAYMENT_MODE_ID
    WHERE od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate
AND od.ORDER_STATUS <> 'CANCELLED' AND ORDER_TYPE IN ('order', 'paid-employee-meal')
GROUP BY od.BUSINESS_DATE, ud.UNIT_NAME , ud.UNIT_ID;
 					]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
				<report name="Google Item Consumption"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
    od.BILLING_SERVER_TIME,
    od.GENERATED_ORDER_ID,
    od.ORDER_ID,
    pd.PRODUCT_NAME,
    oi.DIMENSION,
    oi.PRICE,
    oi.QUANTITY,
    oi.PRICE*oi.QUANTITY AS GMV,
    oi.IS_COMPLIMENTARY,
    COALESCE(cc.COMP_CODE, 'NA') AS REASON_FOR_COMPLIMENTARY
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        LEFT JOIN
    KETTLE_DUMP.ORDER_ITEM oi ON od.ORDER_id = oi.ORDER_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
        LEFT JOIN
    KETTLE_DUMP.COMPLIMENTARY_CODE cc ON oi.COMPLIMENTARY_TYPE_ID = cc.COMP_ID
WHERE
    od.ORDER_STATUS <> 'CANCELLED'
 AND od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:fromBusinessDate,1))
        AND od.ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)
AND od.UNIT_ID=10014
			]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>

				<report name="Cafe Item Consumption"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT
    od.BILLING_SERVER_TIME,
    od.GENERATED_ORDER_ID,
    od.ORDER_ID,
    pd.PRODUCT_NAME,
    oi.DIMENSION,
    oi.PRICE,
    oi.QUANTITY,
    oi.PRICE*oi.QUANTITY AS GMV,
    oi.IS_COMPLIMENTARY,
    COALESCE(cc.COMP_CODE, 'NA') AS REASON_FOR_COMPLIMENTARY
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        LEFT JOIN
    KETTLE_DUMP.ORDER_ITEM oi ON od.ORDER_id = oi.ORDER_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
        LEFT JOIN
    KETTLE_DUMP.COMPLIMENTARY_CODE cc ON oi.COMPLIMENTARY_TYPE_ID = cc.COMP_ID
WHERE
    od.ORDER_STATUS <> 'CANCELLED'
 AND od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:fromBusinessDate,1))
        AND od.ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)
AND od.UNIT_ID=:unitId
                        ]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="unitId" displayName="Unit Id"
                                          dataType="STRING" />
					</params>
				</report>

				<report name="Credit Bill Report"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[	
							
SELECT * FROM (
SELECT 
    ud.UNIT_NAME,
    cp.PARTNER_CODE CHANNEL_PARTNER,
    COALESCE(dp.PARTNER_DISPLAY_NAME, 'NA') AS DELIVERY_PARTNER,
    od.ORDER_ID,
    od.GENERATED_ORDER_ID,	
    od.ORDER_STATUS	,
    od.CANCELATION_REASON	,
    od.UNIT_ID	,
    od.CHANNEL_PARTNER_ID,	
    od.TOTAL_AMOUNT	,
    od.TAXABLE_AMOUNT	,
    od.DISCOUNT_PERCENT	,
    od.DISCOUNT_AMOUNT	,
    od.DISCOUNT_REASON_ID,	
    od.DISCOUNT_REASON	,
    od.NET_PRICE_VAT_PERCENT,	
    od.NET_PRICE_VAT_AMOUNT,	
    od.MRP_VAT_PERCENT	,
    od.MRP_VAT_AMOUNT	,
    od.SERVICE_TAX_PERCENT	,
    od.SERVICE_TAX_AMOUNT	,
    od.SURCHARGE_TAX_PERCENT	,
    od.SURCHARGE_TAX_AMOUNT,	
    od.GST_PERCENT	,
    od.GST_AMOUNT	,
    od.ROUND_OFF_AMOUNT ROUND_OFF_AMOUNT_2	,
    od.SETTLED_AMOUNT	,
    od.BILL_CANCELLATION_TIME,	
    od.DELIVERY_PARTNER_ID,	
    od.ORDER_REMARK	,
    od.SB_CESS_PERCENT	,
    od.SB_CESS_AMOUNT	,
    od.ORDER_SOURCE_ID,
    od.SALE_AMOUNT	,
    od.PROMOTIONAL_DISCOUNT,	
    od.TOTAL_DISCOUNT	,
    od.BILLING_SERVER_TIME	,
    od.OFFER_CODE	,
    od.SAVING_AMOUNT	,
    od.SUBSCRIPTION_ID	,
    od.KK_CESS_PERCENT,	
    od.KK_CESS_AMOUNT,	
    od.TOTAL_TAX	,
    od.CANCELATION_REASON_ID	,
    od.BUSINESS_DATE	,
    os.PAYMENT_MODE_ID	,
    os.AMOUNT_PAID	,
    os.ROUND_OFF_AMOUNT	,
    pm.COMMISSION_RATE	,
    B.PACKAGING_CHARGES	,
    B.DELIVERY_CHARGES
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        LEFT JOIN
    KETTLE_DUMP.ORDER_SETTLEMENT os ON od.ORDER_ID = os.ORDER_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.PAYMENT_MODE pm ON os.PAYMENT_MODE_ID = pm.PAYMENT_MODE_ID
        LEFT JOIN
    KETTLE_DUMP.CHANNEL_PARTNER cp ON od.CHANNEL_PARTNER_ID = cp.PARTNER_ID
        LEFT JOIN
    KETTLE_DUMP.DELIVERY_PARTNER dp ON od.DELIVERY_PARTNER_ID = dp.PARTNER_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
     LEFT JOIN
    (SELECT 
        od.ORDER_ID,
            SUM(CASE
                WHEN oi.PRODUCT_ID = 1043 THEN oi.QUANTITY * oi.PRICE
                ELSE 0
            END) AS PACKAGING_CHARGES,
            SUM(CASE
                WHEN oi.PRODUCT_ID = 1044 THEN oi.QUANTITY * oi.PRICE
                ELSE 0
            END) AS DELIVERY_CHARGES
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    LEFT JOIN KETTLE_DUMP.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate
        AND od.ORDER_STATUS <> 'CANCELLED'
    GROUP BY od.ORDER_ID) B ON od.ORDER_ID = B.ORDER_ID
WHERE
  od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate
  AND od.ORDER_STATUS <> 'CANCELLED'
  AND pm.PAYMENT_MODE_ID = 6 ) x
				
					
				]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
				<report name="Unit Wise Pull Settlement Report"
                              		executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[

SELECT 
    u.BUSINESS_DATE,
    ud.UNIT_NAME,
    p.MODE_NAME,
    c.TOTAL_AMOUNT,
    pd.PULL_AMOUNT,
    pd.STATUS,
    s.TOTAL_AMOUNT,
    s.SETTLEMENT_AMOUNT,
    s.UNSETTLED_AMOUNT,
    s.ORIGINAL_AMOUNT,
    s.EXTRA_AMOUNT
FROM
    KETTLE_DUMP.UNIT_CLOSURE_DETAILS u
        INNER JOIN
    KETTLE_DUMP.CLOSURE_PAYMENT_DETAILS c ON u.CLOSURE_ID = c.CLOSURE_ID
        LEFT JOIN
    KETTLE_DUMP.PULL_DETAIL pd ON pd.CLOSURE_PAYMENT_DETAIL_ID = c.PAYMENT_CLOSURE_ID
        LEFT JOIN
    KETTLE_DUMP.SETTLEMENT_DETAIL s ON s.SETTLEMENT_DETAIL_ID = pd.SETTLEMENT_DETAIL
        INNER JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = u.UNIT_ID
        INNER JOIN
    KETTLE_MASTER_DUMP.PAYMENT_MODE p ON p.PAYMENT_MODE_ID = c.PAYMENT_MODE_ID
WHERE
    u.BUSINESS_DATE >= :fromBusinessDate
        AND u.BUSINESS_DATE <= :toBusinessDate
       AND s.TOTAL_AMOUNT > 0

						]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
<report name="Consolidate TO EP Report"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
			SELECT 
    etr.TRANSFER_ORDER_ID,
    etr.VENDOR_NAME,
    etr.LOCATION_NAME,
    etr.UPDATED_BY,
    etr.UPDATED_AT,
    tri.SKU_ID,
    tri.SKU_NAME,
    cd.CATEGORY_NAME,
    scd.SUB_CATEGORY_NAME,
    tri.TRANSFERRED_QUANTITY,
    tri.UNIT_OF_MEASURE,
    tri.UNIT_PRICE,
    tri.CALCULATED_AMOUNT,
    ti.COMMENT
FROM
    KETTLE_SCM_DUMP.TRANSFER_ORDER ti
        INNER JOIN
    KETTLE_SCM_DUMP.TRANSFER_ORDER_ITEM tri ON tri.TRANSFER_ORDER_ID = ti.TRANSFER_ORDER_ID
        INNER JOIN
    KETTLE_SCM_DUMP.EXTERNAL_TRANSFER_DETAILS etr ON etr.TRANSFER_ORDER_ID = ti.TRANSFER_ORDER_ID
        INNER JOIN
    KETTLE_SCM_DUMP.SKU_DEFINITION sku ON sku.SKU_ID = tri.SKU_ID
        INNER JOIN
    KETTLE_SCM_DUMP.PRODUCT_DEFINITION pd ON pd.PRODUCT_ID = sku.LINKED_PRODUCT_ID
        INNER JOIN
    KETTLE_SCM_DUMP.CATEGORY_DEFINITION cd ON cd.CATEGORY_ID = pd.CATEGORY_ID
        INNER JOIN
    KETTLE_SCM_DUMP.SUB_CATEGORY_DEFINITION scd ON scd.LINKED_CATEGORY_ID = cd.CATEGORY_ID
WHERE
    EXTERNAL_TRANSFER = 'Y'
        AND ti.TRANSFER_ORDER_STATUS <> 'CANCELLED';			
						]]>
					</content>
			</report>	
			
			<report name="ODC Settlement Report unit wise"
                              		executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
    *
FROM
    (SELECT 
        ud.UNIT_NAME,
            od.ORDER_ID,
            od.GENERATED_ORDER_ID,
            od.BILLING_SERVER_TIME,
            od.OFFER_CODE,
            od.CUSTOMER_NAME,
            ci.FIRST_NAME,
            od.BUSINESS_DATE,
            ed.EMP_ID,
            ed.EMP_NAME
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    INNER JOIN KETTLE_DUMP.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    INNER JOIN KETTLE_MASTER_DUMP.EMPLOYEE_DETAIL ed ON ed.EMP_ID = od.EMP_ID
    INNER JOIN KETTLE_DUMP.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
    WHERE
        od.BUSINESS_DATE >= :fromDate
            AND od.BUSINESS_DATE <= :toDate
            AND (od.ORDER_TYPE IS NULL
            OR od.ORDER_TYPE IN ('order' , 'paid-employee-meal'))
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID = :unitId
    GROUP BY od.ORDER_ID) A
        INNER JOIN
    (SELECT 
        od.ORDER_ID,
            ud.UNIT_ID,
            COALESCE(SUM(os.AMOUNT_PAID + os.EXTRA_VOUCHERS), 0) 'Total Sales',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Cash' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Cash',
            SUM(CASE
                WHEN pm.MODE_NAME = 'CreditDebitCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Visa/Master Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'AMEX' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'AMEX Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Sodexo' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Sodexo Coupon',
            SUM(CASE
                WHEN pm.MODE_NAME = 'TicketRestaurant' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Ticket Restaurant Coupon',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Credit' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Credit',
            SUM(CASE
                WHEN pm.MODE_NAME = 'SodexoCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Sodexo Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'TicketRestaurantCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Ticket Restaurant Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'GiftCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Gift Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Paytm' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Paytm',
            SUM(CASE
                WHEN pm.MODE_NAME = 'RazorPay' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'RazorPay',
            SUM(CASE
                WHEN pm.MODE_NAME = 'PayTmOnline' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'PayTmOnline',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Mobikwik' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Mobikwik',
            SUM(CASE
                WHEN pm.MODE_NAME = 'FreeCharge' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'FreeCharge',
            SUM(CASE
                WHEN pm.MODE_NAME = 'DineOut' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'DineOut'
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    LEFT JOIN KETTLE_DUMP.ORDER_SETTLEMENT os ON od.ORDER_ID = os.ORDER_ID
    LEFT JOIN KETTLE_MASTER_DUMP.PAYMENT_MODE pm ON os.PAYMENT_MODE_ID = pm.PAYMENT_MODE_ID
    WHERE
        od.BUSINESS_DATE >= :fromDate
            AND od.BUSINESS_DATE <= :toDate
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID = :unitId
    GROUP BY od.ORDER_ID) B ON A.ORDER_ID = B.ORDER_ID;
						]]>
					</content>
									<params>
					<param name="fromDate" displayName="From Business Date"
                                         dataType="DATE" format="yyyy-MM-dd" />
					<param name="toDate" displayName="To Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					<param name="unitId" displayName="Unit Id"
                                          dataType="INTEGER"/>
				</params>

			</report>	
<report name="ODC Settlement Reports for all units"
                              		executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
    *
FROM
    (SELECT 
        ud.UNIT_NAME,
            od.ORDER_ID,
            od.GENERATED_ORDER_ID,
            od.BILLING_SERVER_TIME,
            od.OFFER_CODE,
            od.CUSTOMER_NAME,
            ci.FIRST_NAME,
            od.BUSINESS_DATE,
            ed.EMP_ID,
            ed.EMP_NAME
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    INNER JOIN KETTLE_DUMP.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    INNER JOIN KETTLE_MASTER_DUMP.EMPLOYEE_DETAIL ed ON ed.EMP_ID = od.EMP_ID
    INNER JOIN KETTLE_DUMP.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
    WHERE
        od.BUSINESS_DATE >= :fromDate
            AND od.BUSINESS_DATE <= :toDate
            AND (od.ORDER_TYPE IS NULL
            OR od.ORDER_TYPE IN ('order' , 'paid-employee-meal'))
            AND od.ORDER_STATUS <> 'CANCELLED'
	    AND od.UNIT_ID IN (26008, 26009, 26010, 26011)
    GROUP BY od.ORDER_ID) A
        INNER JOIN
    (SELECT 
        od.ORDER_ID,
            ud.UNIT_ID,
            COALESCE(SUM(os.AMOUNT_PAID + os.EXTRA_VOUCHERS), 0) 'Total Sales',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Cash' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Cash',
            SUM(CASE
                WHEN pm.MODE_NAME = 'CreditDebitCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Visa/Master Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'AMEX' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'AMEX Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Sodexo' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Sodexo Coupon',
            SUM(CASE
                WHEN pm.MODE_NAME = 'TicketRestaurant' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Ticket Restaurant Coupon',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Credit' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Credit',
            SUM(CASE
                WHEN pm.MODE_NAME = 'SodexoCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Sodexo Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'TicketRestaurantCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Ticket Restaurant Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'GiftCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Gift Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Paytm' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Paytm',
            SUM(CASE
                WHEN pm.MODE_NAME = 'RazorPay' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'RazorPay',
            SUM(CASE
                WHEN pm.MODE_NAME = 'PayTmOnline' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'PayTmOnline',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Mobikwik' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Mobikwik',
            SUM(CASE
                WHEN pm.MODE_NAME = 'FreeCharge' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'FreeCharge',
            SUM(CASE
                WHEN pm.MODE_NAME = 'DineOut' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'DineOut'
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    LEFT JOIN KETTLE_DUMP.ORDER_SETTLEMENT os ON od.ORDER_ID = os.ORDER_ID
    LEFT JOIN KETTLE_MASTER_DUMP.PAYMENT_MODE pm ON os.PAYMENT_MODE_ID = pm.PAYMENT_MODE_ID
    WHERE
        od.BUSINESS_DATE >= :fromDate
            AND od.BUSINESS_DATE <= :toDate
            AND od.ORDER_STATUS <> 'CANCELLED'
	    AND od.UNIT_ID IN (26008, 26009, 26010, 26011)

    GROUP BY od.ORDER_ID) B ON A.ORDER_ID = B.ORDER_ID;
						]]>
					</content>
									<params>
					<param name="fromDate" displayName="From Business Date"
                                         dataType="DATE" format="yyyy-MM-dd" />
					<param name="toDate" displayName="To Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
				</params>

			</report>	
			</reports>
		</category>
		<category name="Delivery Partner Report" type="Cafe Operations"
                  accessCode="Marketing">
			<reports>
				<report name="All Delivery Partner Wise Orders"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
    ud.UNIT_NAME,
    COALESCE(dp.PARTNER_DISPLAY_NAME,'None') as KETTLE_DUMP.DELIVERY_PARTNER,
    SUM(CASE
        WHEN od.ORDER_STATUS = 'SETTLED' THEN 1
        ELSE 0
    END) AS SETTLED_ORDERS,
    SUM(CASE
        WHEN od.ORDER_STATUS = 'CANCELLED' THEN 1
        ELSE 0
    END) AS CANCELLED_ORDERS
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        LEFT JOIN
    KETTLE_DUMP.DELIVERY_PARTNER dp ON od.DELIVERY_PARTNER_ID = dp.PARTNER_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
WHERE
 od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:fromBusinessDate,1))
        AND od.ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)
            and od.ORDER_SOURCE='COD'
GROUP BY ud.UNIT_NAME , dp.PARTNER_DISPLAY_NAME
ORDER BY ud.UNIT_NAME , dp.PARTNER_DISPLAY_NAME
			]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>	
				<report name="Delivery Partner Wise Orders"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
			SELECT 
    ud.UNIT_NAME,
    COALESCE(dp.PARTNER_DISPLAY_NAME,'None') as KETTLE_DUMP.DELIVERY_PARTNER,
    SUM(CASE
        WHEN od.ORDER_STATUS = 'SETTLED' THEN 1
        ELSE 0
    END) AS SETTLED_ORDERS,
    SUM(CASE
        WHEN od.ORDER_STATUS = 'CANCELLED' THEN 1
        ELSE 0
    END) AS CANCELLED_ORDERS
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        LEFT JOIN
    KETTLE_DUMP.DELIVERY_PARTNER dp ON od.DELIVERY_PARTNER_ID = dp.PARTNER_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
WHERE
  od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:fromBusinessDate,1))
        AND od.ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)
            and od.ORDER_SOURCE='COD'
			and dp.PARTNER_CODE LIKE :deliveryPartner
GROUP BY ud.UNIT_NAME , dp.PARTNER_DISPLAY_NAME
ORDER BY ud.UNIT_NAME , dp.PARTNER_DISPLAY_NAME
			]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="deliveryPartner" displayName="KETTLE_DUMP.DELIVERY_PARTNER" dataType="STRING"/>
					</params>
				</report>	
				<report name="Shadowfax Orders -Through API "
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[						
SELECT od.ORDER_ID, od.GENERATED_ORDER_ID,od.ORDER_STATUS,ud.UNIT_NAME,ci.CONTACT_NUMBER,od.SETTLED_AMOUNT,od.BILLING_SERVER_TIME
FROM KETTLE_DUMP.ORDER_DETAIL od
LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud
ON od.UNIT_ID=ud.UNIT_ID
LEFT JOIN KETTLE_DUMP.CUSTOMER_INFO ci
ON od.CUSTOMER_ID=ci.CUSTOMER_ID
WHERE od.DELIVERY_PARTNER_ID=3
 AND od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:fromBusinessDate,1))
        AND od.ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)

]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
				<report name="Opinio Orders -Through API "
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[						
SELECT od.ORDER_ID, od.GENERATED_ORDER_ID,od.ORDER_STATUS,ud.UNIT_NAME,ci.CONTACT_NUMBER,od.SETTLED_AMOUNT,od.BILLING_SERVER_TIME
FROM KETTLE_DUMP.ORDER_DETAIL od
LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud
ON od.UNIT_ID=ud.UNIT_ID
LEFT JOIN KETTLE_DUMP.CUSTOMER_INFO ci
ON od.CUSTOMER_ID=ci.CUSTOMER_ID
WHERE od.DELIVERY_PARTNER_ID=4
 AND od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:fromBusinessDate,1))
        AND od.ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)

]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>				
				<report name="Shadowfax Orders - Assigned Manually"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[	
	
SELECT dd.ORDER_ID,dd.GENERATED_ORDER_ID,ci.CONTACT_NUMBER,od.SETTLED_AMOUNT,od.BILLING_SERVER_TIME,dd.STATUS_UPDATE_TMSTMP
FROM KETTLE_DUMP.DELIVERY_DETAIL dd
LEFT JOIN KETTLE_DUMP.ORDER_DETAIL od
ON dd.ORDER_ID=od.ORDER_ID
LEFT JOIN KETTLE_DUMP.CUSTOMER_INFO ci
ON od.CUSTOMER_ID=ci.CUSTOMER_ID
WHERE DATE(dd.STATUS_UPDATE_TMSTMP) BETWEEN :fromBusinessDate AND :toBusinessDate
AND dd.DELIVERY_TASK_ID LIKE 'SFX%'
AND dd.DELIVERY_STATUS='ACCEPTED'
]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>

				</report>
				<report name="Shadowfax Orders - Dispatched from Cafe and Cancelled"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[						
		SELECT ose.ORDER_ID,od.GENERATED_ORDER_ID,od.SETTLED_AMOUNT,od.ORDER_STATUS,ud.UNIT_NAME,ci.CONTACT_NUMBER,od.BILLING_SERVER_TIME
FROM KETTLE_DUMP.ORDER_STATUS_EVENT ose
LEFT JOIN KETTLE_DUMP.DELIVERY_DETAIL dd
ON ose.ORDER_ID=dd.ORDER_ID
LEFT JOIN KETTLE_DUMP.ORDER_DETAIL od
ON od.ORDER_ID=ose.ORDER_ID
LEFT JOIN KETTLE_DUMP.CUSTOMER_INFO ci
ON od.CUSTOMER_ID=ci.CUSTOMER_ID
LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud
ON od.UNIT_ID=ud.UNIT_ID
WHERE ose.TO_STATUS IN ('CANCELLED_REQUESTED' , 'CANCELLED') 
AND ose.FROM_STATUS ='SETTLED'
AND od.DELIVERY_PARTNER_ID=3
AND od.ORDER_SOURCE='COD'				
and  DATE(od.BILLING_SERVER_TIME) BETWEEN :fromBusinessDate AND :toBusinessDate						
]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>						
			</reports>
		</category>	
		<category name="Cash Management Report" type="Cafe Operations"
                  accessCode="Marketing">
			<reports>
				<report name="Pull and Settlement Reports for all cafe and all type"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
			SELECT 
    ud.UNIT_NAME,
    ud.UNIT_REGION,
    ld.CITY,
    DATE(pd.PULL_DATE) as PULL_DATE,
    pd.PULL_ID,
    pd.PULL_AMOUNT,
    pm.MODE_NAME,
    pd.CREATION_TIME ,
    pd.STATUS,
	sd.SETTLEMENT_SERVICE_PROVIDER,
	sd.SETTLEMENT_DETAIL_ID,
    COALESCE(DATE(sd.SETTLEMENT_TIME), 'NA') AS  SETTLEMENT_DATE ,
    COALESCE(sd.SETTLEMENT_STATUS, 'NA') AS SETTLEMENT_STATUS,
    COALESCE(sd.SETTLEMENT_AMOUNT, 'NA') AS SETTLEMENT_AMOUNT,
    COALESCE(sd.TOTAL_AMOUNT, 'NA') AS TOTAL_AMOUNT,
    COALESCE(pm2.MODE_NAME, 'NA')  AS PAYMENT_MODE
FROM
    KETTLE_DUMP.PULL_DETAIL pd
        LEFT JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON pd.PULL_UNIT = ud.UNIT_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.PAYMENT_MODE pm ON pd.PAYMENT_MODE = pm.PAYMENT_MODE_ID
        LEFT JOIN
    KETTLE_DUMP.SETTLEMENT_DETAIL sd ON pd.SETTLEMENT_DETAIL = sd.SETTLEMENT_DETAIL_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.PAYMENT_MODE pm2 ON sd.PAYMENT_MODE = pm2.PAYMENT_MODE_ID
		LEFT JOIN
    KETTLE_MASTER_DUMP.LOCATION_DETAIL ld on ld.LOCATION_ID = ud.LOCATION_DETAIL_ID
WHERE
    DATE(sd.SETTLEMENT_TIME) BETWEEN :fromBusinessDate AND :toBusinessDate
			]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Settlement Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Settlement Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>	
				<report name="Pull and Settlement Reports for all type and particular cafe"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
			SELECT 
    ud.UNIT_NAME,
    DATE(pd.PULL_DATE) as PULL_DATE,
    pd.PULL_ID,
    pd.PULL_AMOUNT,
    pm.MODE_NAME,
    pd.CREATION_TIME ,
    pd.STATUS,
	sd.SETTLEMENT_SERVICE_PROVIDER,
	sd.SETTLEMENT_DETAIL_ID,
    COALESCE(DATE(sd.SETTLEMENT_TIME), 'NA')  AS  SETTLEMENT_DATE ,
    COALESCE(sd.SETTLEMENT_STATUS, 'NA') AS SETTLEMENT_STATUS,
    COALESCE(sd.SETTLEMENT_AMOUNT, 'NA') AS SETTLEMENT_AMOUNT,
    COALESCE(sd.TOTAL_AMOUNT, 'NA') AS TOTAL_AMOUNT,
    COALESCE(pm2.MODE_NAME, 'NA')  AS MODE_NAME
FROM
    KETTLE_DUMP.PULL_DETAIL pd
        LEFT JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON pd.PULL_UNIT = ud.UNIT_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.PAYMENT_MODE pm ON pd.PAYMENT_MODE = pm.PAYMENT_MODE_ID
        LEFT JOIN
    KETTLE_DUMP.SETTLEMENT_DETAIL sd ON pd.SETTLEMENT_DETAIL = sd.SETTLEMENT_DETAIL_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.PAYMENT_MODE pm2 ON sd.PAYMENT_MODE = pm2.PAYMENT_MODE_ID
WHERE
    DATE(sd.SETTLEMENT_TIME) BETWEEN :fromBusinessDate AND :toBusinessDate
	and ud.UNIT_NAME LIKE concat('%',:unitName, '%')
			]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Settlement Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Settlement Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="unitName" displayName="UNIT NAME" dataType="STRING"/>				  
					</params>
				</report>	
				<report name="Pull and Settlement Reports for all cafes and particular type"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
			SELECT
    ud.UNIT_ID, 
    ud.UNIT_NAME,
    DATE(pd.PULL_DATE) as PULL_DATE,
    pd.PULL_ID,
    pd.PULL_AMOUNT,
    pm.MODE_NAME,
    pd.CREATION_TIME ,
    pd.STATUS,
	sd.SETTLEMENT_SERVICE_PROVIDER,
	sd.SETTLEMENT_DETAIL_ID,
    CONCAT(sd.SETTLEMENT_DETAIL_ID,".",ud.UNIT_ID) as SETTLEMENT_PULL_ID,
    CONCAT(DATE_FORMAT(pd.PULL_DATE, "%d%m%Y"),".",ud.UNIT_ID) as UNIT_PULL_ID,
    COALESCE(DATE(sd.SETTLEMENT_TIME), 'NA') AS  SETTLEMENT_DATE,
    COALESCE(sd.SETTLEMENT_STATUS, 'NA') AS SETTLEMENT_STATUS,
    COALESCE(sd.SETTLEMENT_AMOUNT, 'NA') AS SETTLEMENT_AMOUNT,
    COALESCE(sd.TOTAL_AMOUNT, 'NA') AS TOTAL_AMOUNT,
    COALESCE(pm2.MODE_NAME, 'NA') AS MODE_NAME
FROM
    KETTLE_DUMP.PULL_DETAIL pd
        LEFT JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON pd.PULL_UNIT = ud.UNIT_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.PAYMENT_MODE pm ON pd.PAYMENT_MODE = pm.PAYMENT_MODE_ID
        LEFT JOIN
    KETTLE_DUMP.SETTLEMENT_DETAIL sd ON pd.SETTLEMENT_DETAIL = sd.SETTLEMENT_DETAIL_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.PAYMENT_MODE pm2 ON sd.PAYMENT_MODE = pm2.PAYMENT_MODE_ID
WHERE
    DATE(sd.SETTLEMENT_TIME) BETWEEN DATE(:fromBusinessDate) AND DATE(:toBusinessDate)
	and pm.MODE_NAME LIKE concat('%',:paymentMode, '%')
			]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Settlement Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Settlement Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="paymentMode" displayName="PAYMENT MODE" dataType="STRING"/>				  
					</params>
				</report>
			</reports>
		</category>
		<category name="EBITDA and COGS" type="Cafe Operations"
                  accessCode="Marketing">
			<reports>
				<report name="Delivery COGS and other variables"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[			
SELECT 
    ud.UNIT_ID,
    ud.UNIT_NAME,
    COALESCE(r.TOTAL_TICKET, 0) TOTAL_TICKET,
    COALESCE(r.TKT, 0) TKT,
    COALESCE(r.SALES, 0) SALES,
	aq.GMV AS GMV,
    COALESCE(w.CHANNEL_PARTNER_COST, 0) CHANNEL_PARTNER_COST,
    COALESCE(e.DELIVERY_PARTNER_COST, 0) DELIVERY_PARTNER_COST,
    COALESCE(t.UNSATISFIED_QUANTITY, 0) UNSATISFIED_QUANTITY,
    COALESCE(t.SAMPLING_MARKETING, 0) SAMPLING_MARKETING,
    COALESCE(t.COGS_1, 0) + COALESCE(y.COGS_2, 0) AS TOTAL_COGS
FROM
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud
        LEFT JOIN
    (SELECT 
        a.UNIT_ID,
            SUM(a.CHANNEL_PARTNER_COST) AS CHANNEL_PARTNER_COST
    FROM
        (SELECT 
        od.UNIT_ID,
            cp.PARTNER_DISPLAY_NAME,
            TRUNCATE(SUM(COALESCE(od.TAXABLE_AMOUNT, 0)) * cp.COMMISSION_RATE / 100, 0) AS CHANNEL_PARTNER_COST
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    LEFT JOIN KETTLE_DUMP.CHANNEL_PARTNER cp ON od.CHANNEL_PARTNER_ID = cp.PARTNER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_SOURCE = 'COD'
			
            AND od.ORDER_ID > (SELECT 
                MAX(LAST_ORDER_ID) START_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(:fromBusinessDate, 1))
            AND od.ORDER_ID <= (SELECT 
                MAX(LAST_ORDER_ID) END_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = :toBusinessDate)
    GROUP BY od.UNIT_ID , cp.PARTNER_DISPLAY_NAME) AS a
    GROUP BY a.UNIT_ID) AS w ON ud.UNIT_ID = w.UNIT_ID
        LEFT JOIN
    (SELECT 
        a.UNIT_ID,
            SUM(a.DELIVERY_PARTNER_COST) AS DELIVERY_PARTNER_COST
    FROM
        (SELECT 
        od.UNIT_ID,
            cp.PARTNER_DISPLAY_NAME,
            COALESCE(SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) * cp.PER_DELIVERY_COST, 0) AS DELIVERY_PARTNER_COST
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    LEFT JOIN KETTLE_DUMP.DELIVERY_PARTNER cp ON od.DELIVERY_PARTNER_ID = cp.PARTNER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_SOURCE = 'COD'
            AND od.ORDER_ID > (SELECT 
                MAX(LAST_ORDER_ID) START_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(:fromBusinessDate, 1))
            AND od.ORDER_ID <= (SELECT 
                MAX(LAST_ORDER_ID) END_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = :toBusinessDate)
    GROUP BY od.UNIT_ID , cp.PARTNER_DISPLAY_NAME) AS a
    GROUP BY a.UNIT_ID) AS e ON ud.UNIT_ID = e.UNIT_ID
        LEFT JOIN
    (SELECT 
        od.UNIT_ID,
            COUNT(*) AS TOTAL_TICKET,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS TKT,
            SUM(COALESCE(od.TAXABLE_AMOUNT, 0)) AS SALES,
            SUM(COALESCE(od.DISCOUNT_AMOUNT, 0)) AS DISCOUNT,
            SUM(COALESCE(od.PROMOTIONAL_DISCOUNT, 0)) AS PROMOTIONAL
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_SOURCE = 'COD'
            AND od.ORDER_ID > (SELECT 
                MAX(LAST_ORDER_ID) START_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(:fromBusinessDate, 1))
            AND od.ORDER_ID <= (SELECT 
                MAX(LAST_ORDER_ID) END_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = :toBusinessDate)
    GROUP BY od.UNIT_ID) AS r ON ud.UNIT_ID = r.UNIT_ID
        LEFT JOIN
    (SELECT 
        c.UNIT_ID,
            SUM(c.COGS) AS COGS_1,
            SUM(c.EMPLOYEE_MEAL) AS EMPLOYEE_MEAL,
            SUM(c.UNSATISFIED_QUANTITY) AS UNSATISFIED_QUANTITY,
            SUM(c.SAMPLING_MARKETING) AS SAMPLING_MARKETING,
            SUM(c.GMV) AS GMV,
            SUM(c.NEWGMV) AS NEWGMV
    FROM
        (SELECT 
        a.UNIT_ID,
            a.PRODUCT_ID,
            a.DIMENSION,
            COALESCE(a.SOLD_QUANTITY, 0) * COALESCE(b.COST, 0) AS COGS,
            COALESCE(a.EMPLOYEE_MEAL, 0) * COALESCE(b.COST, 0) AS EMPLOYEE_MEAL,
            COALESCE(a.UNSATISFIED_QUANTITY, 0) * COALESCE(b.COST, 0) AS UNSATISFIED_QUANTITY,
            COALESCE(a.SAMPLING_MARKETING, 0) * COALESCE(b.COST, 0) AS SAMPLING_MARKETING,
            COALESCE(a.GMV, 0) GMV,
            COALESCE(a.SOLD_QUANTITY, 0) * b.PRICE AS NEWGMV
    FROM
        (SELECT 
        od.UNIT_ID,
            pd.PRODUCT_ID,
            rl2.RL_ID AS DIMENSION,
            SUM(CASE
                WHEN
                    oi.IS_COMPLIMENTARY = 'N'
                        OR oi.IS_COMPLIMENTARY IS NULL
                        OR (oi.IS_COMPLIMENTARY = 'Y'
                        AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) NOT IN (2100 , 2102, 2105, 2106))
                THEN
                    oi.QUANTITY
                ELSE 0
            END) SOLD_QUANTITY,
            SUM(CASE
                WHEN
                    oi.IS_COMPLIMENTARY = 'Y'
                        AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2100
                THEN
                    oi.QUANTITY
                ELSE 0
            END) EMPLOYEE_MEAL,
            SUM(CASE
                WHEN
                    oi.IS_COMPLIMENTARY = 'Y'
                        AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) = 2102
                THEN
                    oi.QUANTITY
                ELSE 0
            END) UNSATISFIED_QUANTITY,
            SUM(CASE
                WHEN
                    oi.IS_COMPLIMENTARY = 'Y'
                        AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) IN (2105 , 2106)
                THEN
                    oi.QUANTITY
                ELSE 0
            END) SAMPLING_MARKETING,
            SUM(CASE
                WHEN
                    COALESCE(oi.COMPLIMENTARY_TYPE_ID,0) NOT IN ('2100','2102','2105','2106')
                THEN
                    oi.QUANTITY * oi.PRICE
                ELSE 0
            END) GMV
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    LEFT JOIN KETTLE_DUMP.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
  LEFT JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd ON pd.PRODUCT_ID = oi.PRODUCT_ID

    LEFT JOIN KETTLE_MASTER_DUMP.REF_LOOKUP rl2 ON oi.DIMENSION = rl2.RL_CODE
        AND rl2.RTL_ID = pd.DIMENSION_CODE
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_SOURCE = 'COD'
            AND od.ORDER_ID > (SELECT 
                MAX(LAST_ORDER_ID) START_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(:fromBusinessDate, 1))
            AND od.ORDER_ID <= (SELECT 
                MAX(LAST_ORDER_ID) END_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = :toBusinessDate)
    GROUP BY od.UNIT_ID , pd.PRODUCT_ID , rl2.RL_ID ) AS a
    INNER JOIN (SELECT 
        upm.UNIT_ID,
            upm.PRODUCT_ID,
            upp.DIMENSION_CODE,
            upp.PRICE,
            upp.COST
    FROM
        KETTLE_MASTER_DUMP.UNIT_PRODUCT_MAPPING upm
    LEFT JOIN KETTLE_MASTER_DUMP.UNIT_PRODUCT_PRICING upp ON upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID) AS b ON a.UNIT_ID = b.UNIT_ID
        AND a.PRODUCT_ID = b.PRODUCT_ID
        AND a.DIMENSION = b.DIMENSION_CODE) AS c
    GROUP BY c.UNIT_ID) AS t ON ud.UNIT_ID = t.UNIT_ID
        LEFT JOIN
    (SELECT 
        p.UNIT_ID, SUM(p.QUANTITY * b.COST) AS COGS_2
    FROM
        (SELECT 
        od.UNIT_ID,
            pd1.PRODUCT_ID,
            (CASE
                WHEN pd1.DIMENSION_CODE = 1 THEN 1
                WHEN
                    pd1.DIMENSION_CODE = 2
                        AND od.ORDER_SOURCE = 'CAFE'
                THEN
                    21
                WHEN
                    pd1.DIMENSION_CODE = 2
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    24
                WHEN pd1.DIMENSION_CODE = 3 THEN 30
                WHEN
                    pd1.DIMENSION_CODE = 11
                        AND od.ORDER_SOURCE = 'CAFE'
                THEN
                    1101
                WHEN
                    pd1.DIMENSION_CODE = 11
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1102
                WHEN
                    pd1.DIMENSION_CODE = 34
                        AND od.ORDER_SOURCE = 'CAFE'
                THEN
                    3401
                WHEN
                    pd1.DIMENSION_CODE = 34
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    3402
                ELSE 1
            END) AS DIMENSIONS,
            SUM(oi.QUANTITY) AS QUANTITY
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    INNER JOIN KETTLE_DUMP.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE_DUMP.ORDER_ITEM_ADDON oia ON oi.ORDER_ITEM_ID = oia.ORDER_ITEM_ID
    INNER JOIN KETTLE_MASTER_DUMP.ADDON_PRODUCT_DATA apd ON oia.ADDON_ID = apd.ADDON_ID
    INNER JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd1 ON pd1.PRODUCT_ID = apd.PRODUCT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_SOURCE = 'COD'
            AND od.ORDER_ID > (SELECT 
                MAX(LAST_ORDER_ID) START_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(:fromBusinessDate, 1))
            AND od.ORDER_ID <= (SELECT 
                MAX(LAST_ORDER_ID) END_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = :toBusinessDate)
            AND pd.PRODUCT_TYPE = 8
            AND apd.ADDON_ID IS NOT NULL
    GROUP BY od.UNIT_ID , pd1.PRODUCT_ID , DIMENSIONS) AS p
    INNER JOIN (SELECT 
        upm.UNIT_ID,
            upm.PRODUCT_ID,
            upp.DIMENSION_CODE,
            upp.PRICE,
            upp.COST
    FROM
        KETTLE_MASTER_DUMP.UNIT_PRODUCT_MAPPING upm
    LEFT JOIN KETTLE_MASTER_DUMP.UNIT_PRODUCT_PRICING upp ON upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID) AS b ON p.UNIT_ID = b.UNIT_ID
        AND p.PRODUCT_ID = b.PRODUCT_ID
        AND p.DIMENSIONS = b.DIMENSION_CODE
    GROUP BY p.UNIT_ID) AS y ON ud.UNIT_ID = y.UNIT_ID
   
   
   
LEFT JOIN 

(SELECT a.UNIT_ID,SUM(a.GMV) AS GMV
FROM

(        SELECT od.UNIT_ID,  (CASE
            WHEN
                HOUR(od.BILLING_SERVER_TIME) <= 5
            THEN
                DATE(DATE_ADD(od.BILLING_SERVER_TIME,
                        INTERVAL - 6 HOUR))
            ELSE DATE(od.BILLING_SERVER_TIME)
        END) AS BUSINESS_DATE,
SUM(oi.PRICE*oi.QUANTITY) AS GMV
FROM KETTLE_DUMP.ORDER_DETAIL od
LEFT JOIN KETTLE_DUMP.ORDER_ITEM oi
ON od.ORDER_ID=oi.ORDER_ID
WHERE COALESCE(oi.COMPLIMENTARY_TYPE_ID,0) NOT IN ('2100','2102','2105','2106')
AND od.ORDER_SOURCE='COD'
AND od.ORDER_STATUS <> 'CANCELLED'
       AND od.ORDER_ID > (SELECT 
            CASE
                    WHEN MAX(LAST_ORDER_ID) IS NULL THEN 0
                    ELSE MAX(LAST_ORDER_ID)
                END
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:fromBusinessDate, 1))
        AND od.ORDER_ID <= (SELECT 
            CASE
                    WHEN MAX(LAST_ORDER_ID) IS NULL THEN 0
                    ELSE MAX(LAST_ORDER_ID)
                END
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)
            GROUP BY od.UNIT_ID,BUSINESS_DATE) AS a
            
GROUP BY a.UNIT_ID) AS aq
ON ud.UNIT_ID=aq.UNIT_ID

			]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>	
			
			<report name="Unit Details"
                              executionType="SQL" returnType="java.lang.String">
				<content>
					<![CDATA[
SELECT 
    UNIT_ID, UNIT_NAME, UNIT_REGION, START_DATE, UNIT_STATUS
FROM
    KETTLE_MASTER_DUMP.UNIT_DETAIL
]]>
				</content>
			</report>
			<report name="Active Vendor Details"
                              executionType="SQL" returnType="java.lang.String">
				<content>
					<![CDATA[

							SELECT 
							vd.VENDOR_ID,
							upper(vd.ENTITY_NAME) as ENTITTY_NAME,
    							upper(vd.VENDOR_FIRST_NAME) as FIRST_NAME,
    							upper(vd.VENDOR_LAST_NAME)as LAST_NAME,
								vcd.BUSINESS_TYPE as BUSINESS_TYPE,
    							vd.VENDOR_PRIMARY_EMAIL,
    							vd.VENDOR_PRIMARY_CONTACT,
    							vd.VENDOR_STATUS,
    							upper(vad.ACCOUNT_NUMBER) as ACCOUNT_NUMBER,
    							upper(vad.IFSC_CODE) as IFSC_CODE,
							vcd.CREDIT_CYCLE,
							vd.VENDOR_UPDATED_AT,
							vcd.PAN,
    							addr.*,
                                vrrd.REQUESTED_BY_ID,
                                vrrd.REQUESTED_BY_NAME,
                                vrrd.REQUESTED_FOR_ID,
                                vrrd.REQUESTED_FOR_NAME,
                                vrrd.COPY_EMAILS
						from KETTLE_SCM_DUMP.VENDOR_DETAIL_DATA vd
						inner join KETTLE_SCM_DUMP.VENDOR_COMPANY_DETAIL vcd on vcd.VENDOR_ID=vd.VENDOR_ID
						inner join KETTLE_SCM_DUMP.VENDOR_ACCOUNT_DETAILS vad on vad.VENDOR_ID=vd.VENDOR_ID
						inner join KETTLE_SCM_DUMP.ADDRESS_DETAIL_DATA addr on addr.ADDRESS_ID = vd.VENDOR_ADDRESS_ID
                        inner join KETTLE_SCM_DUMP.VENDOR_REGISTRATION_REQUEST_DETAIL vrrd on vd.VENDOR_ID = vrrd.VENDOR_LINK
						where vd.VENDOR_STATUS = "ACTIVE";
                        
					]]>
				</content>
			</report>
			<report name="Sumo Product to HSN Mapping"
                              executionType="SQL" returnType="java.lang.String">
				<content>
					<![CDATA[
SELECT 
    PRODUCT_ID,
    PRODUCT_NAME,
    TAX_CATEGORY_CODE,
    CATEGORY_CODE,
    CATEGORY_DESCRIPTION
FROM
    KETTLE_SCM_DUMP.PRODUCT_DEFINITION pd
        LEFT JOIN
    KETTLE_MASTER_DUMP.TAX_CATEGORY_DATA tcd ON tcd.CATEGORY_CODE = pd.TAX_CATEGORY_CODE;
					]]>
				</content>
			</report>
<report name="KETTLE Product to HSN Mapping"
                              executionType="SQL" returnType="java.lang.String">
				<content>
					<![CDATA[
SELECT 
    PRODUCT_ID, PRODUCT_NAME, TAX_CODE
FROM
    KETTLE_MASTER_DUMP.PRODUCT_DETAIL
					]]>
				</content>
			</report>

			</reports>
		</category>	
		<category name="Channel Partner Reports" type="Cafe Operations" accessCode="Marketing">
			<reports>
				<report name="Channel Partner Commission for Date Range and Cafe wise"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT * FROM (
SELECT 
	ud.UNIT_ID,
    ud.UNIT_NAME,
    cp.PARTNER_CODE,
    COUNT(DISTINCT ORDER_ID) ORDER_COUNT,
    SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
    cp.COMMISSION_RATE,
    (cp.COMMISSION_RATE / 100) * SUM(TAXABLE_AMOUNT) COMMISSION
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        LEFT JOIN
    KETTLE_DUMP.CHANNEL_PARTNER cp ON cp.PARTNER_ID = od.CHANNEL_PARTNER_ID
    LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud
    ON ud.UNIT_ID = od.UNIT_ID
WHERE
	od.ORDER_STATUS <> 'CANCELLED'
    AND
    od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate
GROUP BY od.UNIT_ID, od.CHANNEL_PARTNER_ID) a
					]]>
				</content>
				<params>
					<param name="fromBusinessDate" displayName="Business Date"
                                         dataType="DATE" format="yyyy-MM-dd" />
					<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
				</params>
			</report>
			</reports>
		</category>		

	<category name="Settlement Reports" type="Cafe Operations"
                  accessCode="Marketing">
			<reports>
				<report name="Settlement Reports for time duration Payment mode wise Cafe wise"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
    ud.UNIT_NAME,
    ud.UNIT_ID,
    COALESCE(COUNT(od.ORDER_ID), 0) TOTAL_TICKETS,
    COALESCE(SUM(os.AMOUNT_PAID + os.EXTRA_VOUCHERS),
            0) 'Total Sales',
    SUM(CASE
        WHEN pm.MODE_NAME = 'Cash' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'Cash',
    SUM(CASE
        WHEN pm.MODE_NAME = 'CreditDebitCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'Visa/Master Card',
    SUM(CASE
        WHEN pm.MODE_NAME = 'AMEX' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'AMEX Card',
    SUM(CASE
        WHEN pm.MODE_NAME = 'Sodexo' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'Sodexo Coupon',
    SUM(CASE
        WHEN pm.MODE_NAME = 'TicketRestaurant' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'Ticket Restaurant Coupon',
    SUM(CASE
        WHEN pm.MODE_NAME = 'Credit' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'Credit',
    SUM(CASE
        WHEN pm.MODE_NAME = 'SodexoCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'Sodexo Card',
    SUM(CASE
        WHEN pm.MODE_NAME = 'TicketRestaurantCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'Ticket Restaurant Card',
    SUM(CASE
        WHEN pm.MODE_NAME = 'GiftCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'Gift Card',
    SUM(CASE
        WHEN pm.MODE_NAME = 'Paytm' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'Paytm',
    SUM(CASE
        WHEN pm.MODE_NAME = 'RazorPay' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'RazorPay',
    SUM(CASE
        WHEN pm.MODE_NAME = 'PayTmOnline' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'PayTmOnline',
    SUM(CASE
        WHEN pm.MODE_NAME = 'Mobikwik' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'Mobikwik',
    SUM(CASE
        WHEN pm.MODE_NAME = 'FreeCharge' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
        ELSE 0
    END) 'FreeCharge'
FROM
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud
        LEFT JOIN
    KETTLE_DUMP.ORDER_DETAIL od ON od.UNIT_ID = ud.UNIT_ID
        AND od.ORDER_STATUS <> 'CANCELLED'
        LEFT JOIN
    KETTLE_DUMP.ORDER_SETTLEMENT os ON od.ORDER_ID = os.ORDER_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.PAYMENT_MODE pm ON os.PAYMENT_MODE_ID = pm.PAYMENT_MODE_ID
WHERE
    od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate
GROUP BY ud.UNIT_ID;
						]]>
				</content>
				<params>
					<param name="fromBusinessDate" displayName="Business Date"
                                         dataType="DATE" format="yyyy-MM-dd" />
					<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
				</params>
			</report>

<report name="Bill level Data for DLF Mall Recon Reports(VAT)"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
   *
FROM
    (SELECT 
        ud.UNIT_NAME,
            od.ORDER_ID,
            od.GENERATED_ORDER_ID,
            od.TOTAL_AMOUNT,
            od.TAXABLE_AMOUNT,
            od.DISCOUNT_PERCENT,
            od.DISCOUNT_AMOUNT,
            od.DISCOUNT_REASON,
            od.NET_PRICE_VAT_PERCENT,
            od.NET_PRICE_VAT_AMOUNT,
            od.MRP_VAT_PERCENT,
            od.MRP_VAT_AMOUNT,
            od.SERVICE_TAX_PERCENT,
            od.SERVICE_TAX_AMOUNT,
            od.SURCHARGE_TAX_PERCENT,
            od.SURCHARGE_TAX_AMOUNT,
            od.SERVICE_CHARGE_PERCENT,
            od.SERVICE_CHARGE_AMOUNT,
            od.ROUND_OFF_AMOUNT,
            od.SETTLED_AMOUNT,
            od.ORDER_SOURCE,
            od.POINTS_REDEEMED,
            od.SB_CESS_PERCENT,
            od.SB_CESS_AMOUNT,
            od.SALE_AMOUNT,
            od.PROMOTIONAL_DISCOUNT,
            od.TOTAL_DISCOUNT,
            od.BILLING_SERVER_TIME,
            od.OFFER_CODE,
            od.SAVING_AMOUNT,
            od.KK_CESS_PERCENT,
            od.KK_CESS_AMOUNT,
            od.CUSTOMER_NAME,
            od.ORDER_TYPE,
            od.BUSINESS_DATE,
            od.MANUAL_BILL_BOOK_NO
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    INNER JOIN KETTLE_DUMP.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.BILLING_SERVER_TIME BETWEEN :fromBusinessDate AND :toBusinessDate
            AND (od.ORDER_TYPE IS NULL
            OR od.ORDER_TYPE IN ('order' , 'paid-employee-meal'))
            AND od.ORDER_STATUS <> 'CANCELLED'
    GROUP BY od.ORDER_ID) A
        INNER JOIN
    (SELECT 
        od.ORDER_ID,
            ud.UNIT_ID,
            COALESCE(SUM(os.AMOUNT_PAID + os.EXTRA_VOUCHERS), 0) 'Total Sales',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Cash' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Cash',
            SUM(CASE
                WHEN pm.MODE_NAME = 'CreditDebitCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Visa/Master Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'AMEX' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'AMEX Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Sodexo' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Sodexo Coupon',
            SUM(CASE
                WHEN pm.MODE_NAME = 'TicketRestaurant' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Ticket Restaurant Coupon',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Credit' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Credit',
            SUM(CASE
                WHEN pm.MODE_NAME = 'SodexoCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Sodexo Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'TicketRestaurantCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Ticket Restaurant Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'GiftCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Gift Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Paytm' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Paytm',
            SUM(CASE
                WHEN pm.MODE_NAME = 'RazorPay' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'RazorPay',
            SUM(CASE
                WHEN pm.MODE_NAME = 'PayTmOnline' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'PayTmOnline',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Mobikwik' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Mobikwik',
            SUM(CASE
                WHEN pm.MODE_NAME = 'FreeCharge' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'FreeCharge',
            SUM(CASE
                WHEN pm.MODE_NAME = 'DineOut' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'DineOut'
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    LEFT JOIN KETTLE_DUMP.ORDER_SETTLEMENT os ON od.ORDER_ID = os.ORDER_ID
    LEFT JOIN KETTLE_MASTER_DUMP.PAYMENT_MODE pm ON os.PAYMENT_MODE_ID = pm.PAYMENT_MODE_ID
    WHERE
        od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate
            AND od.ORDER_STATUS <> 'CANCELLED'
    GROUP BY od.ORDER_ID) B ON A.ORDER_ID = B.ORDER_ID;
]]>
				</content>
				<params>
					<param name="fromBusinessDate" displayName="Business Date"
                                         dataType="DATE" format="yyyy-MM-dd" />
					<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
				</params>
			</report>

<report name="Bill level Data for DLF Mall Recon Reports(VAT) Unit Wise"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
   *
FROM
    (SELECT 
        ud.UNIT_NAME,
            od.ORDER_ID,
            od.GENERATED_ORDER_ID,
            od.TOTAL_AMOUNT,
            od.TAXABLE_AMOUNT,
            od.DISCOUNT_PERCENT,
            od.DISCOUNT_AMOUNT,
            od.DISCOUNT_REASON,
            od.NET_PRICE_VAT_PERCENT,
            od.NET_PRICE_VAT_AMOUNT,
            od.MRP_VAT_PERCENT,
            od.MRP_VAT_AMOUNT,
            od.SERVICE_TAX_PERCENT,
            od.SERVICE_TAX_AMOUNT,
            od.SURCHARGE_TAX_PERCENT,
            od.SURCHARGE_TAX_AMOUNT,
            od.SERVICE_CHARGE_PERCENT,
            od.SERVICE_CHARGE_AMOUNT,
            od.ROUND_OFF_AMOUNT,
            od.SETTLED_AMOUNT,
            od.ORDER_SOURCE,
            od.POINTS_REDEEMED,
            od.SB_CESS_PERCENT,
            od.SB_CESS_AMOUNT,
            od.SALE_AMOUNT,
            od.PROMOTIONAL_DISCOUNT,
            od.TOTAL_DISCOUNT,
            od.BILLING_SERVER_TIME,
            od.OFFER_CODE,
            od.SAVING_AMOUNT,
            od.KK_CESS_PERCENT,
            od.KK_CESS_AMOUNT,
            od.CUSTOMER_NAME,
            od.ORDER_TYPE,
            od.BUSINESS_DATE,
            od.MANUAL_BILL_BOOK_NO
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    INNER JOIN KETTLE_DUMP.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.BILLING_SERVER_TIME BETWEEN :fromBusinessDate AND :toBusinessDate
            AND (od.ORDER_TYPE IS NULL
            OR od.ORDER_TYPE IN ('order' , 'paid-employee-meal'))
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID = :unitId
    GROUP BY od.ORDER_ID) A
        INNER JOIN
    (SELECT 
        od.ORDER_ID,
            ud.UNIT_ID,
            COALESCE(SUM(os.AMOUNT_PAID + os.EXTRA_VOUCHERS), 0) 'Total Sales',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Cash' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Cash',
            SUM(CASE
                WHEN pm.MODE_NAME = 'CreditDebitCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Visa/Master Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'AMEX' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'AMEX Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Sodexo' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Sodexo Coupon',
            SUM(CASE
                WHEN pm.MODE_NAME = 'TicketRestaurant' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Ticket Restaurant Coupon',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Credit' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Credit',
            SUM(CASE
                WHEN pm.MODE_NAME = 'SodexoCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Sodexo Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'TicketRestaurantCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Ticket Restaurant Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'GiftCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Gift Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Paytm' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Paytm',
            SUM(CASE
                WHEN pm.MODE_NAME = 'RazorPay' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'RazorPay',
            SUM(CASE
                WHEN pm.MODE_NAME = 'PayTmOnline' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'PayTmOnline',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Mobikwik' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Mobikwik',
            SUM(CASE
                WHEN pm.MODE_NAME = 'FreeCharge' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'FreeCharge',
            SUM(CASE
                WHEN pm.MODE_NAME = 'DineOut' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'DineOut'
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    LEFT JOIN KETTLE_DUMP.ORDER_SETTLEMENT os ON od.ORDER_ID = os.ORDER_ID
    LEFT JOIN KETTLE_MASTER_DUMP.PAYMENT_MODE pm ON os.PAYMENT_MODE_ID = pm.PAYMENT_MODE_ID
    WHERE
        od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND od.UNIT_ID = :unitId
    GROUP BY od.ORDER_ID) B ON A.ORDER_ID = B.ORDER_ID;
]]>
				</content>
				<params>
					<param name="fromBusinessDate" displayName="Business Date"
                                         dataType="DATE" format="yyyy-MM-dd" />
					<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					<param name="unitId" displayName="Unit Id"
                                          dataType="INTEGER"/>
				</params>
			</report>

			<report name="Bill level data for Recon Report(GST)"
                        	executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
		
SELECT 
    *
FROM
    (SELECT 
		od.ORDER_ID,
		od.GENERATED_ORDER_ID,
            od.UNIT_ID,
            ud.UNIT_NAME,
            sd.STATE,
            od.TOTAL_AMOUNT,
            od.DISCOUNT_PERCENT,
            od.DISCOUNT_AMOUNT,
            od.DISCOUNT_REASON,
            MAX(od.TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            MAX(od.TOTAL_TAX) TOTAL_TAX,
            SUM(CASE
                WHEN otd.TAX_CODE = 'CGST' THEN otd.TOTAL_TAX
                ELSE 0
            END) 'CGST',
            SUM(CASE
                WHEN otd.TAX_CODE = 'SGST/UTGST' THEN otd.TOTAL_TAX
                ELSE 0
            END) 'SGST_UTGST',
            SUM(CASE
                WHEN
                    otd.TAX_CODE != 'SGST/UTGST'
                        && otd.TAX_CODE != 'CGST'
                THEN
                    otd.TOTAL_TAX
                ELSE 0
            END) 'OTHERS',
            od.ROUND_OFF_AMOUNT,
            od.SETTLED_AMOUNT,
            od.ORDER_SOURCE,
            od.POINTS_REDEEMED,
            od.SALE_AMOUNT,
            od.PROMOTIONAL_DISCOUNT,
            od.TOTAL_DISCOUNT,
            od.BILLING_SERVER_TIME,
            od.OFFER_CODE,
            od.SAVING_AMOUNT,
            od.CUSTOMER_NAME,
            od.ORDER_TYPE,
            od.BUSINESS_DATE,
            od.MANUAL_BILL_BOOK_NO
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
        LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    LEFT JOIN KETTLE_MASTER_DUMP.LOCATION_DETAIL ld ON ld.LOCATION_ID = ud.LOCATION_DETAIL_ID
    LEFT JOIN KETTLE_MASTER_DUMP.STATE_DETAIL sd ON ld.STATE_DETAIL_ID = sd.STATE_DETAIL_ID
    LEFT JOIN KETTLE_DUMP.ORDER_TAX_DETAIL otd ON otd.ORDER_ID = od.ORDER_ID
    WHERE
        od.BUSINESS_DATE BETWEEN :startDate AND :endDate
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE IN ('order' , 'paid-employee-meal')
    GROUP BY od.ORDER_ID) t1
        LEFT JOIN
    (SELECT 
		od.BUSINESS_DATE,
        od.ORDER_ID,
            COALESCE(SUM(os.AMOUNT_PAID + os.EXTRA_VOUCHERS), 0) 'Total Sales',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Cash' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Cash',
            SUM(CASE
                WHEN pm.MODE_NAME = 'CreditDebitCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Visa/Master Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'AMEX' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'AMEX Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Sodexo' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Sodexo Coupon',
            SUM(CASE
                WHEN pm.MODE_NAME = 'TicketRestaurant' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Ticket Restaurant Coupon',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Credit' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Credit',
            SUM(CASE
                WHEN pm.MODE_NAME = 'SodexoCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Sodexo Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'TicketRestaurantCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Ticket Restaurant Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'GiftCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Gift Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Paytm' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Paytm',
            SUM(CASE
                WHEN pm.MODE_NAME = 'RazorPay' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'RazorPay',
            SUM(CASE
                WHEN pm.MODE_NAME = 'PayTmOnline' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'PayTmOnline',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Mobikwik' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Mobikwik',
            SUM(CASE
                WHEN pm.MODE_NAME = 'FreeCharge' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'FreeCharge',
            SUM(CASE
                WHEN pm.MODE_NAME = 'DineOut' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'DineOut',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Prepaid' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Prepaid'
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    LEFT JOIN KETTLE_DUMP.ORDER_SETTLEMENT os ON od.ORDER_ID = os.ORDER_ID
    LEFT JOIN KETTLE_MASTER_DUMP.PAYMENT_MODE pm ON os.PAYMENT_MODE_ID = pm.PAYMENT_MODE_ID
    WHERE
        od.BUSINESS_DATE BETWEEN :startDate AND :endDate
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE IN ('order' , 'paid-employee-meal')
    GROUP BY od.ORDER_ID) t2 ON t1.ORDER_ID = t2.ORDER_ID
    LEFT JOIN 
    ( SELECT 
    od.ORDER_ID, SUM(oi.TOTAL_AMOUNT) GIFT_CARD_AMOUNT
FROM
    ORDER_DETAIL od
        INNER JOIN
    ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
WHERE
    od.BUSINESS_DATE BETWEEN :startDate AND :endDate
        AND od.ORDER_STATUS <> 'CANCELLED'
        AND od.ORDER_TYPE IN ('order' , 'paid-employee-meal')
        AND oi.TAX_CODE IN ('GIFT_CARD')
GROUP BY od.ORDER_ID) t3 ON t1.ORDER_ID = t3.ORDER_ID;
    
 						]]>
					</content>
					<params>
						<param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
					</params>
			</report>


			<report name="Bill level data for Recon Report Unit Wise(GST)"
                        	executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
		
SELECT 
    *
FROM
    (SELECT 
		od.ORDER_ID,
		od.GENERATED_ORDER_ID,
            od.UNIT_ID,
            ud.UNIT_NAME,
            sd.STATE,
            od.TOTAL_AMOUNT,
            od.DISCOUNT_PERCENT,
            od.DISCOUNT_AMOUNT,
            od.DISCOUNT_REASON,
            MAX(od.TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            MAX(od.TOTAL_TAX) TOTAL_TAX,
            SUM(CASE
                WHEN otd.TAX_CODE = 'CGST' THEN otd.TOTAL_TAX
                ELSE 0
            END) 'CGST',
            SUM(CASE
                WHEN otd.TAX_CODE = 'SGST/UTGST' THEN otd.TOTAL_TAX
                ELSE 0
            END) 'SGST_UTGST',
            SUM(CASE
                WHEN
                    otd.TAX_CODE != 'SGST/UTGST'
                        && otd.TAX_CODE != 'CGST'
                THEN
                    otd.TOTAL_TAX
                ELSE 0
            END) 'OTHERS',
            od.ROUND_OFF_AMOUNT,
            od.SETTLED_AMOUNT,
            od.ORDER_SOURCE,
            od.POINTS_REDEEMED,
            od.SALE_AMOUNT,
            od.PROMOTIONAL_DISCOUNT,
            od.TOTAL_DISCOUNT,
            od.BILLING_SERVER_TIME,
            od.OFFER_CODE,
            od.SAVING_AMOUNT,
            od.CUSTOMER_NAME,
            od.ORDER_TYPE,
            od.BUSINESS_DATE,
            od.MANUAL_BILL_BOOK_NO
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
        LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    LEFT JOIN KETTLE_MASTER_DUMP.LOCATION_DETAIL ld ON ld.LOCATION_ID = ud.LOCATION_DETAIL_ID
    LEFT JOIN KETTLE_MASTER_DUMP.STATE_DETAIL sd ON ld.STATE_DETAIL_ID = sd.STATE_DETAIL_ID
    LEFT JOIN KETTLE_DUMP.ORDER_TAX_DETAIL otd ON otd.ORDER_ID = od.ORDER_ID
    WHERE
        od.BUSINESS_DATE BETWEEN :startDate AND :endDate
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE IN ('order' , 'paid-employee-meal')
	    AND od.UNIT_ID = :unitId
    GROUP BY od.ORDER_ID) t1
        LEFT JOIN
    (SELECT 
		od.BUSINESS_DATE,
        od.ORDER_ID,
            COALESCE(SUM(os.AMOUNT_PAID + os.EXTRA_VOUCHERS), 0) 'Total Sales',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Cash' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Cash',
            SUM(CASE
                WHEN pm.MODE_NAME = 'CreditDebitCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Visa/Master Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'AMEX' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'AMEX Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Sodexo' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Sodexo Coupon',
            SUM(CASE
                WHEN pm.MODE_NAME = 'TicketRestaurant' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Ticket Restaurant Coupon',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Credit' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Credit',
            SUM(CASE
                WHEN pm.MODE_NAME = 'SodexoCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Sodexo Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'TicketRestaurantCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Ticket Restaurant Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'GiftCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Gift Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Paytm' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Paytm',
            SUM(CASE
                WHEN pm.MODE_NAME = 'RazorPay' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'RazorPay',
            SUM(CASE
                WHEN pm.MODE_NAME = 'PayTmOnline' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'PayTmOnline',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Mobikwik' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Mobikwik',
            SUM(CASE
                WHEN pm.MODE_NAME = 'FreeCharge' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'FreeCharge',
            SUM(CASE
                WHEN pm.MODE_NAME = 'DineOut' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'DineOut',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Prepaid' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Prepaid'
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    LEFT JOIN KETTLE_DUMP.ORDER_SETTLEMENT os ON od.ORDER_ID = os.ORDER_ID
    LEFT JOIN KETTLE_MASTER_DUMP.PAYMENT_MODE pm ON os.PAYMENT_MODE_ID = pm.PAYMENT_MODE_ID
    WHERE
        od.BUSINESS_DATE BETWEEN :startDate AND :endDate
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE IN ('order' , 'paid-employee-meal')
	    AND od.UNIT_ID = :unitId
    GROUP BY od.ORDER_ID) t2 ON t1.ORDER_ID = t2.ORDER_ID
    LEFT JOIN 
    ( SELECT 
    od.ORDER_ID, SUM(oi.TOTAL_AMOUNT) GIFT_CARD_AMOUNT
FROM
    ORDER_DETAIL od
        INNER JOIN
    ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
WHERE
    od.BUSINESS_DATE BETWEEN :startDate AND :endDate
        AND od.ORDER_STATUS <> 'CANCELLED'
        AND od.ORDER_TYPE IN ('order' , 'paid-employee-meal')
        AND oi.TAX_CODE IN ('GIFT_CARD')
	    AND od.UNIT_ID = :unitId
GROUP BY od.ORDER_ID) t3 ON t1.ORDER_ID = t3.ORDER_ID;
    
 						]]>
					</content>
					<params>
						<param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="unitId" displayName="Unit Id"
                               dataType="INTEGER"/>
					</params>
			</report>
			<report name="Order Details by Bill Number"
                        	executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT od.GENERATED_ORDER_ID, od.UNIT_ID, ud.UNIT_NAME, od.BILLING_SERVER_TIME,od.BUSINESS_DATE,od.SETTLED_AMOUNT
FROM KETTLE_DUMP.ORDER_DETAIL od 
INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID 
WHERE od.GENERATED_ORDER_ID LIKE CONCAT(:orderId,'%') LIMIT 10; 
 						]]>
					</content>
					<params>
						<param name="orderId" displayName="BILL Number"
                               dataType="STRING"/>
					</params>
			</report>

			</reports>
		</category>		
	</categories>
</ReportCategories>
