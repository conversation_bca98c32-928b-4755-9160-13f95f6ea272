<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<ReportCategories xmlns="http://www.w3schools.com">
	<categories>
		<category name="Call Center Reports" type="Cafe Operations"
                  accessCode="Marketing">
			<reports>
				<report name="Chaayos SDP Report"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
    *
FROM
    (SELECT 
        z.ORDER_ID,
            z.GENERATED_ORDER_ID,
            z.PARTNER_DISPLAY_NAME,
            z.SETTLED_AMOUNT,
			z.TAXABLE_AMOUNT,
            p.PAYMENT_MODE PAYMENT_BY,
            z.UNIT_NAME,
            z.CITY,
            z.FIRST_NAME,
            z.CONTACT_NUMBER,
            c.INITIATED,
            c.CREATED,
            c.PROCESSING,
            c.READY_TO_DISPATCH,
            c.SETTLED,
            c.CANCELLED_REQUESTED,
            c.CANCELLED,
            c.DELIVERED
    FROM
        (SELECT 
        od.ORDER_ID,
            od.GENERATED_ORDER_ID,
	    cp.PARTNER_DISPLAY_NAME,
            od.SETTLED_AMOUNT,
			od.TAXABLE_AMOUNT,
            ud.UNIT_NAME,
            ai.CITY,
            ci.FIRST_NAME,
            ci.CONTACT_NUMBER
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    LEFT JOIN KETTLE_MASTER_DUMP.ADDRESS_INFO ai ON ud.UNIT_ADDR_ID = ai.ADDRESS_ID
    LEFT JOIN KETTLE_DUMP.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
    LEFT JOIN KETTLE_DUMP.ORDER_SETTLEMENT os ON os.ORDER_ID = od.ORDER_ID
    LEFT JOIN KETTLE_MASTER_DUMP.PAYMENT_MODE pm ON os.PAYMENT_MODE_ID = pm.PAYMENT_MODE_ID
    LEFT JOIN KETTLE_DUMP.CHANNEL_PARTNER cp ON cp.PARTNER_ID = od.CHANNEL_PARTNER_ID
    WHERE
        od.ORDER_SOURCE = 'COD'
            AND od.ORDER_ID > (SELECT 
                MAX(LAST_ORDER_ID) START_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(:fromBusinessDate, 1))
            AND od.ORDER_ID <= (SELECT 
                MAX(LAST_ORDER_ID) END_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = :toBusinessDate)) z
    LEFT JOIN (SELECT 
        pm.MODE_DESCRIPTION PAYMENT_MODE, os.ORDER_ID
    FROM
        KETTLE_DUMP.ORDER_SETTLEMENT os
    LEFT JOIN KETTLE_MASTER_DUMP.PAYMENT_MODE pm ON os.PAYMENT_MODE_ID = pm.PAYMENT_MODE_ID
    WHERE
        os.ORDER_ID > (SELECT 
                MAX(LAST_ORDER_ID) START_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(:fromBusinessDate, 1))
            AND os.ORDER_ID <= (SELECT 
                MAX(LAST_ORDER_ID) END_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = :toBusinessDate)
    GROUP BY os.ORDER_ID) p ON p.ORDER_ID = z.ORDER_ID
    LEFT JOIN (SELECT 
        *
    FROM
        (SELECT 
        a.ORDER_ID,
            MAX(a.INITIATED) INITIATED,
            MAX(a.CREATED) CREATED,
            MAX(a.PROCESSING) PROCESSING,
            MAX(a.READY_TO_DISPATCH) READY_TO_DISPATCH,
            MAX(a.SETTLED) SETTLED,
            MAX(a.CANCELLED_REQUESTED) CANCELLED_REQUESTED,
            MAX(a.CANCELLED) CANCELLED,
            MAX(a.DELIVERED) DELIVERED
    FROM
        (SELECT 
        ose.ORDER_ID,
            (CASE
                WHEN ose.FROM_STATUS = 'INITIATED' THEN ose.START_TIME
                ELSE NULL
            END) AS INITIATED,
            (CASE
                WHEN ose.TO_STATUS = 'CREATED' THEN ose.UPDATE_TIME
                ELSE NULL
            END) AS CREATED,
            (CASE
                WHEN ose.TO_STATUS = 'PROCESSING' THEN ose.UPDATE_TIME
                ELSE NULL
            END) AS PROCESSING,
            (CASE
                WHEN ose.TO_STATUS = 'READY_TO_DISPATCH' THEN ose.UPDATE_TIME
                ELSE NULL
            END) AS READY_TO_DISPATCH,
            (CASE
                WHEN ose.TO_STATUS = 'SETTLED' THEN ose.UPDATE_TIME
                ELSE NULL
            END) AS SETTLED,
            (CASE
                WHEN ose.TO_STATUS = 'CANCELLED_REQUESTED' THEN ose.UPDATE_TIME
                ELSE NULL
            END) AS CANCELLED_REQUESTED,
            (CASE
                WHEN ose.TO_STATUS = 'CANCELLED' THEN ose.UPDATE_TIME
                ELSE NULL
            END) AS CANCELLED,
            (CASE
                WHEN ose.TO_STATUS = 'DELIVERED' THEN ose.UPDATE_TIME
                ELSE NULL
            END) AS DELIVERED
    FROM
        KETTLE_DUMP.ORDER_STATUS_EVENT ose
    WHERE
        ose.ORDER_ID > (SELECT 
                MAX(LAST_ORDER_ID) START_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(:fromBusinessDate, 1))
            AND ose.ORDER_ID <= (SELECT 
                MAX(LAST_ORDER_ID) END_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = :toBusinessDate)) a
    GROUP BY a.ORDER_ID) b
    WHERE
        b.CREATED IS NOT NULL) c ON c.ORDER_ID = z.ORDER_ID) part1
        LEFT JOIN
    (SELECT 
        dd.ORDER_ID,
            dd.DELIVERY_BOY_ID,
            dd.DELIVERY_BOY_NAME,
            dd.DELIVERY_BOY_PHONE_NUM,
            dp.PARTNER_DISPLAY_NAME,
            b.DELIVERY_TASK_ID,
            b.ASSIGNED
    FROM
        KETTLE_DUMP.DELIVERY_DETAIL dd
    LEFT JOIN KETTLE_DUMP.DELIVERY_STATUS_EVENT dse ON dd.DELIVERY_TASK_ID = dse.DELIVERY_TASK_ID
    LEFT JOIN KETTLE_DUMP.DELIVERY_PARTNER dp ON dd.DELIVERY_PARTNER_ID = dp.PARTNER_ID
    LEFT JOIN (SELECT 
        a.ORDER_ID, a.DELIVERY_TASK_ID, MAX(a.ASSIGNED) ASSIGNED
    FROM
        (SELECT 
        dse.DELIVERY_TASK_ID,
            dse.ORDER_ID,
            (CASE
                WHEN dse.DELIVERY_TO_STATUS = 'ASSIGNED' THEN dse.STATUS_START_TMSTMP
                ELSE NULL
            END) AS ASSIGNED
    FROM
        KETTLE_DUMP.DELIVERY_STATUS_EVENT dse
    WHERE
        dse.ORDER_ID > (SELECT 
                MAX(LAST_ORDER_ID) START_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(:fromBusinessDate, 1))
            AND dse.ORDER_ID <= (SELECT 
                MAX(LAST_ORDER_ID) END_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = :toBusinessDate)) a
    GROUP BY a.ORDER_ID) b ON b.ORDER_ID = dd.ORDER_ID
    WHERE
        dd.ORDER_ID > (SELECT 
                MAX(LAST_ORDER_ID) START_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(:fromBusinessDate, 1))
            AND dd.ORDER_ID <= (SELECT 
                MAX(LAST_ORDER_ID) END_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = :toBusinessDate)) part2 ON part1.ORDER_ID = part2.ORDER_ID
                        ]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>

				</report>
				<report name="Delivery Delay Report"
                              		executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
							CALL SP_DELIVERY_DELAY_REPORT_DATA();
						]]>
					</content>
				</report>
				<report name="Agent Performance Report"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
    od.EMP_ID,
    ed.EMP_NAME,
    SUM(CASE
        WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
        ELSE 0
    END) AS TKT,
    SUM(od.SETTLED_AMOUNT) AS BILL_AMOUNT,
    SUM(od.TAXABLE_AMOUNT) AS SALES,
    TRUNCATE(SUM(od.SETTLED_AMOUNT) / SUM(CASE
            WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
            ELSE 0
        END),
        2) AS APC,
    TRUNCATE(AVG(od.BILL_CREATION_SECONDS),
        2) AS Avg_Processing_Time,
    SUM(CASE
        WHEN
            CHANNEL_PARTNER_ID = 2
                AND od.TOTAL_AMOUNT <> '0.00'
        THEN
            1
        ELSE 0
    END) AS VOICE_CALL,
    SUM(CASE
        WHEN
            CHANNEL_PARTNER_ID <> 2
                AND od.TOTAL_AMOUNT <> '0.00'
        THEN
            1
        ELSE 0
    END) AS NON_VOICE_CALL
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        LEFT JOIN
    KETTLE_MASTER_DUMP.EMPLOYEE_DETAIL ed ON od.EMP_ID = ed.EMP_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
WHERE
    od.ORDER_SOURCE = 'COD'
       AND od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate 
       AND od.ORDER_STATUS <>'CANCELLED'
GROUP BY od.EMP_ID , ed.EMP_NAME

	   			]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
				<report name="Hourly Tickets and Sales"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[						
						
SELECT 
    ud.UNIT_NAME,
    DATE(od.BILLING_SERVER_TIME) AS 'DATE',
    HOUR(od.BILLING_SERVER_TIME) AS 'HOUR',
    COUNT(od.ORDER_ID) AS TKT,
    SUM(od.TAXABLE_AMOUNT) AS SALES
FROM
    KETTLE_DUMP.ORDER_DETAIL od,
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud
WHERE
    od.UNIT_ID = ud.UNIT_ID
        AND od.ORDER_STATUS <> 'CANCELLED'
        AND od.ORDER_SOURCE = 'COD'
        AND od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate
GROUP BY ud.UNIT_NAME , DATE(od.BILLING_SERVER_TIME) , HOUR(od.BILLING_SERVER_TIME)

]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>

				</report>
				<report name="Cancelled Report"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[												

SELECT 
    ud.UNIT_NAME AS UNIT_NAME,
    ci.CONTACT_NUMBER AS CONTACT_NUMBER,
    od.ORDER_ID AS ORDER_ID,
    od.BILLING_SERVER_TIME AS BILLING_SERVER_TIME,
    od.TAXABLE_AMOUNT AS 'TAXABLE_AMOUNT',
    COALESCE(od.BILL_CANCELLATION_TIME,'NA') AS BILL_CANCELLATION_TIME,
    COALESCE(ed1.EMP_NAME, 'NA') AS CANCELLED_BY,
    COALESCE(ed2.EMP_NAME, 'NA') AS CANCELLED_APPROVED_BY,
    COALESCE(od.CANCELATION_REASON,'NA') AS CANCELATION_REASON
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        LEFT JOIN
    KETTLE_DUMP.CUSTOMER_INFO ci ON od.CUSTOMER_ID = ci.CUSTOMER_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.EMPLOYEE_DETAIL ed1 ON ed1.EMP_ID = od.CANCELLED_BY
        LEFT JOIN
    KETTLE_MASTER_DUMP.EMPLOYEE_DETAIL ed2 ON ed2.EMP_ID = od.CANCEL_APPROVED_BY
WHERE
    od.ORDER_STATUS = 'CANCELLED'
        AND od.ORDER_SOURCE = 'COD'
      AND od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate

						]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>

				</report>

				<report name="Channel Partner Report Day wise"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[						
SELECT 
    cp.PARTNER_DISPLAY_NAME,
    (CASE
        WHEN
            HOUR(od.BILLING_SERVER_TIME) <= 5
        THEN
            DATE(DATE_ADD(od.BILLING_SERVER_TIME,
                    INTERVAL - 6 HOUR))
        ELSE DATE(od.BILLING_SERVER_TIME)
    END) AS BUSINESS_DATE,
    SUM(CASE
        WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
        ELSE 0
    END) AS TICKETS,
    SUM(od.TAXABLE_AMOUNT) AS SALES,
    TRUNCATE(SUM(od.TAXABLE_AMOUNT) / SUM(CASE
            WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
            ELSE 0
        END),
        0) AS APC
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        LEFT JOIN
    KETTLE_DUMP.CHANNEL_PARTNER cp ON od.CHANNEL_PARTNER_ID = cp.PARTNER_ID
WHERE
    od.ORDER_STATUS <> 'CANCELLED'
 	AND od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate
	AND od.CHANNEL_PARTNER_ID IS NOT NULL		
 GROUP BY cp.PARTNER_DISPLAY_NAME, BUSINESS_DATE
			]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
				<report name="COD Bill Level Complete Data"
					executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[

SELECT * FROM (SELECT
    ud.UNIT_NAME,
    od.BUSINESS_DATE,
    od.ORDER_ID,
    od.ORDER_SOURCE,
    od.BILLING_SERVER_TIME,
    dr.RL_NAME,
    cp.PARTNER_DISPLAY_NAME AS CHANNEL_PARTNER,
    dp.PARTNER_DISPLAY_NAME AS DELIVERY_PARTNER,
    ci.CONTACT_NUMBER,
    ci.FIRST_NAME,
    ai.*
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        INNER JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
        INNER JOIN
    KETTLE_DUMP.CHANNEL_PARTNER cp ON od.CHANNEL_PARTNER_ID = cp.PARTNER_ID
        INNER JOIN
    KETTLE_DUMP.DELIVERY_PARTNER dp ON od.DELIVERY_PARTNER_ID = dp.PARTNER_ID
        LEFT OUTER JOIN
    KETTLE_DUMP.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
        LEFT OUTER JOIN
    KETTLE_DUMP.CUSTOMER_ADDRESS_INFO ai ON od.DELIVERY_ADDRESS = ai.ADDRESS_ID
        LEFT OUTER JOIN
    KETTLE_MASTER_DUMP.REF_LOOKUP dr ON dr.RL_ID = od.DISCOUNT_REASON_ID
WHERE
    od.ORDER_SOURCE = 'COD'
 AND od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate
ORDER BY ud.UNIT_NAME, BUSINESS_DATE) x

]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>

				</report>
				<report name="COD Bill Level Complete Data For Unit"
					executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT * FROM (
SELECT
    ud.UNIT_NAME,
    od.*,
    dr.RL_NAME,
    cp.PARTNER_DISPLAY_NAME AS CHANNEL_PARTNER,
    dp.PARTNER_DISPLAY_NAME AS DELIVERY_PARTNER,
    ci.CONTACT_NUMBER,
    ci.FIRST_NAME,
    ai.ADDRESS_ID,
    ai.ADDRESS_LINE_1,
    ai.ADDRESS_LINE_2,
    ai.ADDRESS_LINE_3,
    ai.CITY,
    ai.STATE,
    ai.COUNTRY,
    ai.ZIPCODE,
    ai.ADDRESS_TYPE,
    ai.PREFERRED_ADDRESS,
    ai.COMPANY,
    ai.LOCALITY,
    ai.LANDMARK,
    ai.SUB_LOCALITY
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        INNER JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
        INNER JOIN
    KETTLE_DUMP.CHANNEL_PARTNER cp ON od.CHANNEL_PARTNER_ID = cp.PARTNER_ID
        INNER JOIN
    KETTLE_DUMP.DELIVERY_PARTNER dp ON od.DELIVERY_PARTNER_ID = dp.PARTNER_ID
        LEFT OUTER JOIN
    KETTLE_DUMP.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
        LEFT OUTER JOIN
    KETTLE_DUMP.CUSTOMER_ADDRESS_INFO ai ON od.DELIVERY_ADDRESS = ai.ADDRESS_ID
        LEFT OUTER JOIN
    KETTLE_MASTER_DUMP.REF_LOOKUP dr ON dr.RL_ID = od.DISCOUNT_REASON_ID
WHERE
    od.ORDER_SOURCE = 'COD'
 AND od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate
  AND ud.UNIT_NAME LIKE CONCAT('%', :unitName , '%')
ORDER BY ud.UNIT_NAME, BUSINESS_DATE) x
]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="unitName" displayName="Unit Name"
                                          dataType="STRING" />
					</params>
				</report>
				<report name="COD Item Level Complete Data"
					executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT * FROM (
SELECT 
    ud.UNIT_NAME,
    od.BUSINESS_DATE,
	od.ORDER_ID,
    od.GENERATED_ORDER_ID,
    od.ORDER_STATUS,
    od.CANCELATION_REASON,
    od.BILL_START_TIME,
    od.BILL_GENERATION_TIME,
    od.TOTAL_AMOUNT,
    od.TAXABLE_AMOUNT,
    od.DISCOUNT_PERCENT,
    od.DISCOUNT_AMOUNT,
    od.DISCOUNT_REASON_ID,
    od.DISCOUNT_REASON,
    od.ROUND_OFF_AMOUNT,
    od.SETTLED_AMOUNT,
    od.ORDER_SOURCE,
    od.BILL_CANCELLATION_TIME,
    od.CANCELLED_BY,
    od.CANCEL_APPROVED_BY,
    od.POINTS_REDEEMED,
    od.DELIVERY_PARTNER_ID,
    od.ORDER_REMARK,
    od.SALE_AMOUNT,
    od.PROMOTIONAL_DISCOUNT,
    od.TOTAL_DISCOUNT,
    od.BILL_CREATION_SECONDS,
    od.BILLING_SERVER_TIME,
    od.OFFER_CODE,
    od.SAVING_AMOUNT,
    od.CUSTOMER_NAME,
    od.TOTAL_TAX,
    od.WASTAGE_TYPE,
    od.ORDER_TYPE,
    od.MANUAL_BILL_BOOK_NO,
    od.OUT_OF_DELIVERY,
    oi.PRODUCT_ID,
    oi.PRODUCT_NAME,
    oi.QUANTITY,
    oi.PRICE,
    oi.TOTAL_AMOUNT AS ITEM_AMOUNT,
    oi.DISCOUNT_PERCENT AS ITEM_DISCOUNT_PERCENT,
    oi.DISCOUNT_AMOUNT AS ITEM_DISCOUNT_AMOUNT,
    oi.DISCOUNT_REASON_ID AS ITEM_DISCOUNT_REASON_ID,
    oi.DISCOUNT_REASON AS ITEM_DISCOUNT_REASON,
    oi.IS_COMPLIMENTARY,
    oi.COMPLIMENTARY_TYPE_ID,
    oi.COMPLIMENTARY_REASON,
    oi.DIMENSION,
    oi.AMOUNT_PAID,
    oi.PROMOTIONAL_DISCOUNT AS ITEM_PROMOTIONAL_DISCOUNT,
    oi.COMBO_CONSTITUENT,
    oi.PARENT_ITEM_ID,
    oi.TOTAL_TAX AS ITEM_TOTAL_TAX,
    oi.TAX_CODE,
    oi.CANCELATION_REASON_ID,
    oi.WASTAGE_BOOKED,
    cr.RL_NAME,
    dimension.RL_NAME AS DIMENTION
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        INNER JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
        INNER JOIN
    KETTLE_DUMP.ORDER_ITEM oi ON oi.ORDER_ID = od.ORDER_ID
        LEFT OUTER JOIN
    KETTLE_MASTER_DUMP.REF_LOOKUP cr ON cr.RL_ID = oi.COMPLIMENTARY_TYPE_ID
        LEFT OUTER JOIN
    KETTLE_MASTER_DUMP.REF_LOOKUP dimension ON dimension.RL_ID = oi.DIMENSION
WHERE
    od.ORDER_SOURCE = 'COD'
        AND od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate
ORDER BY od.UNIT_ID , BUSINESS_DATE ) x
]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
				<report name="Offer Detail Report"
					executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
    A.OFFER_CODE,
    A.PARTNER_CODE,
    A.OFFER_ORDER_COUNT,
    B.PARTNER_ORDER_COUNT,
    ROUND((A.OFFER_ORDER_COUNT / B.PARTNER_ORDER_COUNT) * 100,
            2) OFFER_ORDER_PERCENTAGE,
    A.TOTAL_DISCOUNT,
    A.TAXABLE_AMOUNT,
    ROUND((A.TOTAL_DISCOUNT / (A.TOTAL_DISCOUNT + A.TAXABLE_AMOUNT)) * 100,
            2) SALES_DISCOUNT_PERCENTAGE
FROM
    (SELECT 
        od.OFFER_CODE,
            od.CHANNEL_PARTNER_ID,
            cp.PARTNER_CODE,
            COUNT(DISTINCT od.ORDER_ID) OFFER_ORDER_COUNT,
            SUM(od.TOTAL_DISCOUNT) TOTAL_DISCOUNT,
            SUM(od.TAXABLE_AMOUNT) TAXABLE_AMOUNT
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    INNER JOIN KETTLE_DUMP.CHANNEL_PARTNER cp ON od.CHANNEL_PARTNER_ID = cp.PARTNER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_SOURCE = 'COD'
            AND (od.ORDER_TYPE IN ('order' , 'paid-employee-meal')
            OR od.ORDER_TYPE IS NULL)
            AND od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate
            AND od.OFFER_CODE IS NOT NULL
    GROUP BY od.OFFER_CODE) A
        LEFT JOIN
    (SELECT 
        od.CHANNEL_PARTNER_ID,
            COUNT(DISTINCT od.ORDER_ID) PARTNER_ORDER_COUNT
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_SOURCE = 'COD'
            AND (od.ORDER_TYPE IN ('order' , 'paid-employee-meal')
            OR od.ORDER_TYPE IS NULL)
            AND od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate
    GROUP BY od.CHANNEL_PARTNER_ID) B ON A.CHANNEL_PARTNER_ID = B.CHANNEL_PARTNER_ID;
  
]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
			</reports>
		</category>
	</categories>
</ReportCategories>
