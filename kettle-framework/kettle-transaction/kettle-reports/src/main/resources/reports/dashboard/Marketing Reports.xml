<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<ReportCategories xmlns="http://www.w3schools.com">
	<categories>
		<category name="Marketing Sales Reports" type="Marketing"
			accessCode="Marketing">
			<reports>
				<report name="CAFE Daily Sales and Acquisition Report"
					executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
    ud.UNIT_ID,
    ud.UNIT_NAME,
    :businessDate BUSINESS_DATE,
    ud.UNIT_CATEGORY,
    TOTAL_TICKETS,
    NET_SALES_AMOUNT,
    CASE
        WHEN
            TOTAL_TICKETS IS NULL
                OR TOTAL_TICKETS = 0
        THEN
            'NA'
        ELSE CAST(COALESCE(CAFE_TICKETS_WITH_CUSTOMERS / TOTAL_TICKETS * 100)
            AS DECIMAL (10 , 2 ))
    END PERCENT_ACQUISTION,
    CASE
        WHEN
            TOTAL_TICKETS IS NULL
                OR TOTAL_TICKETS = 0
        THEN
            'NA'
        ELSE CAST(COALESCE(NET_SALES_AMOUNT / TOTAL_TICKETS)
            AS DECIMAL (10 , 2 ))
    END NET_APC,
    CAFE_NET_TICKETS,
    CAFE_NET_SALES_AMOUNT,
    COD_NET_TICKETS,
    COD_NET_SALES_AMOUNT,
    CAFE_NEW_CUSTOMERS,
    CAFE_OLD_CUSTOMERS,
    CAFE_TICKETS_WITH_CUSTOMERS,
    CAFE_TICKETS_WITH_NO_CUSTOMERS
FROM
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud
        LEFT OUTER JOIN
    KETTLE_DUMP.SALES_REPORT_DATA srd ON srd.UNIT_ID = ud.UNIT_ID
WHERE
    ud.UNIT_STATUS = 'ACTIVE'
        AND ud.UNIT_CATEGORY = 'CAFE'
        AND BUSINESS_DATE = :businessDate
ORDER BY ud.UNIT_NAME

]]>
					</content>
					<params>
						<param name="businessDate" displayName="Business Date"
							dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
				<report name="Hourly tickets and sales"
					executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[			
SELECT ud.UNIT_NAME,(CASE
        WHEN
            HOUR(od.BILLING_SERVER_TIME) <= 5
        THEN
            DATE(DATE_ADD(od.BILLING_SERVER_TIME,
                    INTERVAL - 6 HOUR))
        ELSE DATE(od.BILLING_SERVER_TIME)
    END) AS BUSINESS_DATE  ,DATE(od.BILLING_SERVER_TIME) AS 'DATE', HOUR(od.BILLING_SERVER_TIME) AS 'HOUR', SUM(CASE
        WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
        ELSE 0
    END) AS TICKETS
,SUM(od.TAXABLE_AMOUNT) AS SALES,
TRUNCATE(SUM(od.TAXABLE_AMOUNT)/SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END),0) AS APC
FROM KETTLE_DUMP.ORDER_DETAIL od
left join KETTLE_MASTER_DUMP.UNIT_DETAIL ud
ON od.UNIT_ID=ud.UNIT_ID
WHERE od.ORDER_STATUS <>'CANCELLED'
AND od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:fromBusinessDate,1))
        AND od.ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)
GROUP BY ud.UNIT_NAME,BUSINESS_DATE,DATE(od.BILLING_SERVER_TIME), HOUR(od.BILLING_SERVER_TIME)
				
]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
				<report name="Customer Count for all Cafe on a date range" executionType="SQL"
					returnType="java.lang.String">
					<content>
						<![CDATA[		

SELECT od.UNIT_ID,ud.UNIT_NAME,(CASE
            WHEN
                HOUR(od.BILLING_SERVER_TIME) <= 5
            THEN
                DATE(DATE_ADD(od.BILLING_SERVER_TIME,
                        INTERVAL - 6 HOUR))
            ELSE DATE(od.BILLING_SERVER_TIME)
        END) AS BUSINESS_DATE, 
         SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS CUSTOMER_COUNT
FROM KETTLE_DUMP.ORDER_DETAIL od
LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud
ON od.UNIT_ID=ud.UNIT_ID
WHERE od.TOTAL_AMOUNT<>0
AND od.ORDER_STATUS <>'CANCELLED'
AND od.CUSTOMER_ID>'5'
AND ud.UNIT_CATEGORY='CAFE'
   AND od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:fromBusinessDate,1))
        AND od.ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)
GROUP BY od.UNIT_ID,ud.UNIT_NAME,BUSINESS_DATE
]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />

					</params>
				</report>										
				<report name="Sales Report For A Day" executionType="SQL"
					returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
    :businessDate BUSINESS_DATE,
    ud.UNIT_ID,
    ud.UNIT_NAME,
    ud.UNIT_CATEGORY,
    COALESCE(current_data.TOTAL_TICKETS, 'NA') TOTAL_TICKETS,
    COALESCE(current_data.NET_SALES_AMOUNT, 'NA') NET_SALES_AMOUNT,
    CASE
        WHEN
            lmtd_data.LMTD_TOTAL_TICKETS IS NULL
                OR lmtd_data.LMTD_TOTAL_TICKETS = '0.00'
        THEN
            'NA'
        ELSE CAST((COALESCE(mtd_data.MTD_TOTAL_TICKETS, 0.00) - COALESCE(lmtd_data.LMTD_TOTAL_TICKETS, 0.00)) / COALESCE(lmtd_data.LMTD_TOTAL_TICKETS, 0.00) * 100
            AS DECIMAL (10 , 2 ))
    END PERCENTAGE_TICKETS_GROWTH,
    CASE
        WHEN
            lmtd_data.LMTD_NET_SALES_AMOUNT IS NULL
                OR lmtd_data.LMTD_NET_SALES_AMOUNT = '0.00'
        THEN
            'NA'
        ELSE CAST((COALESCE(mtd_data.MTD_NET_SALES_AMOUNT, 0.00) - COALESCE(lmtd_data.LMTD_NET_SALES_AMOUNT, 0.00)) / COALESCE(lmtd_data.LMTD_NET_SALES_AMOUNT, 0.00) * 100
            AS DECIMAL (10 , 2 ))
    END PERCENTAGE_SALES_GROWTH,
    CASE
        WHEN current_data.TOTAL_TICKETS IS NULL THEN 'NA'
        ELSE CAST(current_data.NET_SALES_AMOUNT / current_data.TOTAL_TICKETS
            AS DECIMAL (10 , 2 ))
    END NET_APC,
    CASE
        WHEN current_data.GMV_AMOUNT IS NULL THEN 'NA'
        ELSE CAST(COALESCE(COALESCE(current_data.FOOD_TOTAL_SALES, 0.00) + COALESCE(current_data.BAKERY_TOTAL_SALES, 0.00) + COALESCE(current_data.COMBOS_TOTAL_SALES * 0.60,
                            0.00),
                    0.00) / current_data.GMV_AMOUNT * 100
            AS DECIMAL (10 , 2 ))
    END FOOD_PERCENT,
    CASE
        WHEN current_data.GMV_AMOUNT IS NULL THEN 'NA'
        ELSE CAST(COALESCE(COALESCE(current_data.HOT_BEVERAGES_TOTAL_SALES,
                            0.00) + COALESCE(current_data.COLD_BEVERAGES_TOTAL_SALES + COALESCE(current_data.COMBOS_TOTAL_SALES * 0.40,
                                    0.00),
                            0.00),
                    0.00) / current_data.GMV_AMOUNT * 100
            AS DECIMAL (10 , 2 ))
    END BEVERAGE_PERCENT,
    COALESCE(mtd_data.MTD_TOTAL_TICKETS, 'NA') MTD_TOTAL_TICKETS,
    COALESCE(mtd_data.MTD_NET_SALES_AMOUNT, 'NA') MTD_NET_SALES_AMOUNT,
    CASE
        WHEN mtd_data.MTD_TOTAL_TICKETS IS NULL THEN 'NA'
        ELSE CAST(mtd_data.MTD_NET_SALES_AMOUNT / mtd_data.MTD_TOTAL_TICKETS
            AS DECIMAL (10 , 2 ))
    END MTD_NET_APC,
    CASE
        WHEN mtd_data.MTD_GMV_AMOUNT IS NULL THEN 'NA'
        ELSE CAST(COALESCE(COALESCE(mtd_data.MTD_FOOD_TOTAL_SALES, 0.00) + COALESCE(mtd_data.MTD_BAKERY_TOTAL_SALES, 0.00) + COALESCE(mtd_data.MTD_COMBOS_TOTAL_SALES * 0.60,
                            0.00),
                    0.00) / mtd_data.MTD_GMV_AMOUNT * 100
            AS DECIMAL (10 , 2 ))
    END MTD_FOOD_PERCENT,
    CASE
        WHEN mtd_data.MTD_GMV_AMOUNT IS NULL THEN 'NA'
        ELSE CAST(COALESCE(COALESCE(mtd_data.MTD_HOT_BEVERAGES_TOTAL_SALES,
                            0.00) + COALESCE(mtd_data.MTD_COLD_BEVERAGES_TOTAL_SALES + COALESCE(mtd_data.MTD_COMBOS_TOTAL_SALES * 0.40,
                                    0.00),
                            0.00),
                    0.00) / mtd_data.MTD_GMV_AMOUNT * 100
            AS DECIMAL (10 , 2 ))
    END MTD_BEVERAGE_PERCENT,
    COALESCE(lmtd_data.LMTD_TOTAL_TICKETS, 'NA') LMTD_TOTAL_TICKETS,
    COALESCE(lmtd_data.LMTD_NET_SALES_AMOUNT, 'NA') LMTD_NET_SALES_AMOUNT,
    CASE
        WHEN lmtd_data.LMTD_TOTAL_TICKETS IS NULL THEN 'NA'
        ELSE CAST(lmtd_data.LMTD_NET_SALES_AMOUNT / lmtd_data.LMTD_TOTAL_TICKETS
            AS DECIMAL (10 , 2 ))
    END LMTD_NET_APC,
    CASE
        WHEN lmtd_data.LMTD_GMV_AMOUNT IS NULL THEN 'NA'
        ELSE CAST(COALESCE(COALESCE(lmtd_data.LMTD_FOOD_TOTAL_SALES, 0.00) + COALESCE(lmtd_data.LMTD_BAKERY_TOTAL_SALES, 0.00) + COALESCE(lmtd_data.LMTD_COMBOS_TOTAL_SALES * 0.60,
                            0.00),
                    0.00) / lmtd_data.LMTD_GMV_AMOUNT * 100
            AS DECIMAL (10 , 2 ))
    END LMTD_FOOD_PERCENT,
    CASE
        WHEN lmtd_data.LMTD_GMV_AMOUNT IS NULL THEN 'NA'
        ELSE CAST(COALESCE(COALESCE(lmtd_data.LMTD_HOT_BEVERAGES_TOTAL_SALES,
                            0.00) + COALESCE(lmtd_data.LMTD_COLD_BEVERAGES_TOTAL_SALES,
                            0.00) + COALESCE(lmtd_data.LMTD_COMBOS_TOTAL_SALES * 0.40,
                            0.00),
                    0.00) / lmtd_data.LMTD_GMV_AMOUNT * 100
            AS DECIMAL (10 , 2 ))
    END LMTD_BEVERAGE_PERCENT
FROM
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud
        LEFT OUTER JOIN
    (SELECT 
        *
    FROM
        KETTLE_DUMP.SALES_REPORT_DATA
    WHERE
        BUSINESS_DATE = :businessDate) current_data ON ud.UNIT_ID = current_data.UNIT_ID
        LEFT OUTER JOIN
    (SELECT 
        UNIT_ID,
            SUM(TOTAL_TICKETS) MTD_TOTAL_TICKETS,
            SUM(NET_SALES_AMOUNT) MTD_NET_SALES_AMOUNT,
            SUM(GMV_AMOUNT) MTD_GMV_AMOUNT,
            SUM(FOOD_TOTAL_SALES) MTD_FOOD_TOTAL_SALES,
            SUM(HOT_BEVERAGES_TOTAL_SALES) MTD_HOT_BEVERAGES_TOTAL_SALES,
            SUM(COLD_BEVERAGES_TOTAL_SALES) MTD_COLD_BEVERAGES_TOTAL_SALES,
            SUM(COMBOS_TOTAL_SALES) MTD_COMBOS_TOTAL_SALES,
            SUM(BAKERY_TOTAL_SALES) MTD_BAKERY_TOTAL_SALES,
           SUM(MERCHANDISE_TOTAL_SALES) MTD_MERCHANDISE_TOTAL_SALES,
            SUM(OTHERS_TOTAL_SALES) MTD_OTHERS_TOTAL_SALES
    FROM
        KETTLE_DUMP.SALES_REPORT_DATA
    WHERE
        BUSINESS_DATE >= CAST(DATE_FORMAT(:businessDate, '%Y-%m-01')
            AS DATE)
            AND BUSINESS_DATE <= :businessDate
    GROUP BY UNIT_ID) mtd_data ON ud.UNIT_ID = mtd_data.UNIT_ID
        LEFT OUTER JOIN
    (SELECT 
        UNIT_ID,
            SUM(TOTAL_TICKETS) LMTD_TOTAL_TICKETS,
            SUM(NET_SALES_AMOUNT) LMTD_NET_SALES_AMOUNT,
            SUM(GMV_AMOUNT) LMTD_GMV_AMOUNT,
            SUM(FOOD_TOTAL_SALES) LMTD_FOOD_TOTAL_SALES,
            SUM(HOT_BEVERAGES_TOTAL_SALES) LMTD_HOT_BEVERAGES_TOTAL_SALES,
            SUM(COLD_BEVERAGES_TOTAL_SALES) LMTD_COLD_BEVERAGES_TOTAL_SALES,
            SUM(COMBOS_TOTAL_SALES) LMTD_COMBOS_TOTAL_SALES,
            SUM(BAKERY_TOTAL_SALES) LMTD_BAKERY_TOTAL_SALES,
            SUM(MERCHANDISE_TOTAL_SALES) LMTD_MERCHANDISE_TOTAL_SALES,
            SUM(OTHERS_TOTAL_SALES) LMTD_OTHERS_TOTAL_SALES
    FROM
        KETTLE_DUMP.SALES_REPORT_DATA
    WHERE
        BUSINESS_DATE >= CAST(DATE_FORMAT(DATE_ADD(:businessDate, INTERVAL - 1 MONTH), '%Y-%m-01')
            AS DATE)
            AND BUSINESS_DATE <= DATE_ADD(:businessDate, INTERVAL - 1 MONTH)
    GROUP BY UNIT_ID) lmtd_data ON ud.UNIT_ID = lmtd_data.UNIT_ID
WHERE
    ud.UNIT_STATUS = 'ACTIVE'
    and ud.UNIT_CATEGORY IN ('CAFE', 'DELIVERY')
order by ud.UNIT_CATEGORY, ud.UNIT_NAME
]]>
					</content>
					<params>
						<param name="businessDate" displayName="Business Date"
							dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
				<report name="Product Sales Report For A Day" executionType="SQL"
					returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
	ud.UNIT_ID UNIT_ID,
    ud.UNIT_NAME UNIT_NAME,
    ud.UNIT_CATEGORY UNIT_CATEGORY,
    :businessDate BUSINESS_DATE,
    pd.PRODUCT_NAME PRODUCT_NAME,
    oi.DIMENSION,
    oi.PRICE PRICE,
    SUM(oi.QUANTITY) QUANTITY,
    SUM(oi.PRICE * oi.QUANTITY) TOTAL_SALES
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        INNER JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
        INNER JOIN
    KETTLE_DUMP.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
        INNER JOIN
    KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd ON pd.PRODUCT_ID = oi.PRODUCT_ID
WHERE
    od.ORDER_STATUS <>'CANCELLED'
        AND od.ORDER_ID > (SELECT 
            MAX(LAST_ORDER_ID)
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:businessDate, 1))
        AND od.ORDER_ID <= (SELECT 
            MAX(LAST_ORDER_ID)
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :businessDate)
GROUP BY ud.UNIT_ID, ud.UNIT_NAME , ud.UNIT_CATEGORY , BUSINESS_DATE , pd.PRODUCT_NAME , oi.PRICE , oi.DIMENSION
ORDER BY ud.UNIT_CATEGORY , ud.UNIT_NAME , pd.PRODUCT_TYPE , pd.PRODUCT_SUB_TYPE , pd.PRODUCT_NAME
]]>
					</content>
					<params>
						<param name="businessDate" displayName="Business Date"
							dataType="DATE" format="yyyy-MM-dd" />
					</params>

				</report>
				<report name="Product Sales Report By Category For A Day"
					executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
	ud.UNIT_ID,
    ud.UNIT_NAME,
     ud.UNIT_CATEGORY UNIT_CATEGORY,
    :businessDate BUSINESS_DATE,
    rtl.RTL_NAME PRODUCT_CATEGORY,
    SUM(CASE
                WHEN oi.COMPLIMENTARY_TYPE_ID IN (2100 , 2102) THEN (oi.QUANTITY)
                ELSE 0
            END) NON_ACCOUNTABLE,
            SUM(CASE
                WHEN oi.COMPLIMENTARY_TYPE_ID NOT IN (2100 , 2102) THEN (oi.QUANTITY)
                ELSE 0
            END) ACCOUNTABLE,
            SUM(oi.QUANTITY) TOTAL_QUANTITY,
             SUM(CASE
                WHEN oi.COMPLIMENTARY_TYPE_ID IS NOT NULL THEN (oi.QUANTITY)
                ELSE 0
            END) SALE_QUANTITY,
    SUM(oi.PRICE * oi.QUANTITY) TOTAL_SALES
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        INNER JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
        INNER JOIN
    KETTLE_DUMP.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
        INNER JOIN
    KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd ON pd.PRODUCT_ID = oi.PRODUCT_ID
        INNER JOIN
    KETTLE_MASTER_DUMP.REF_LOOKUP_TYPE rtl ON rtl.RTL_ID = pd.PRODUCT_TYPE
WHERE
    od.ORDER_STATUS <>'CANCELLED'
        AND od.ORDER_ID > (SELECT 
            MAX(LAST_ORDER_ID)
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:businessDate, 1))
        AND od.ORDER_ID <= (SELECT 
            MAX(LAST_ORDER_ID)
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :businessDate)
GROUP BY ud.UNIT_ID, ud.UNIT_NAME ,ud.UNIT_CATEGORY, BUSINESS_DATE , rtl.RTL_NAME
ORDER BY ud.UNIT_NAME, rtl.RTL_ID
]]>
					</content>
					<params>
						<param name="businessDate" displayName="Business Date"
							dataType="DATE" format="yyyy-MM-dd" />
					</params>

				</report>
				<report name="Product Sales Report By Sub Category For A Day"
					executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
    ud.UNIT_ID,
    ud.UNIT_NAME,
    :businessDate BUSINESS_DATE,
    rl.RL_NAME PRODUCT_SUB_CATEGORY,
    oi.PRICE,
    SUM(oi.QUANTITY) QUANTITY,
    SUM(oi.PRICE * oi.QUANTITY) TOTAL_SALES
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        INNER JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
        INNER JOIN
    KETTLE_DUMP.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
        INNER JOIN
    KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd ON pd.PRODUCT_ID = oi.PRODUCT_ID
        INNER JOIN
    KETTLE_MASTER_DUMP.REF_LOOKUP rl ON rl.RL_ID = pd.PRODUCT_SUB_TYPE
WHERE
    od.ORDER_STATUS <>'CANCELLED'
        AND od.ORDER_ID > (SELECT 
            MAX(LAST_ORDER_ID)
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:businessDate, 1))
        AND od.ORDER_ID <= (SELECT 
            MAX(LAST_ORDER_ID)
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :businessDate)
GROUP BY ud.UNIT_ID , ud.UNIT_NAME , BUSINESS_DATE , rl.RL_NAME , oi.PRICE
ORDER BY ud.UNIT_NAME , rl.RL_ID
]]>
					</content>
					<params>
						<param name="businessDate" displayName="Business Date"
							dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
				<report name="Sales Till Date Across Cafe's" executionType="SQL"
					returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
	ud.UNIT_ID UNIT_ID,
    ud.UNIT_NAME UNIT_NAME,
    ud.UNIT_CATEGORY UNIT_CATEGORY,
    COUNT(*) TOTAL_TICKETS,
    SUM(od.TOTAL_AMOUNT) GMV_AMOUNT,
    SUM(od.TAXABLE_AMOUNT) NET_SALE_AMOUNT,
    SUM(CASE
        WHEN od.CUSTOMER_ID > 5 THEN 1
        ELSE 0
    END) TICKETS_WITH_CUSTOMERS,
    COUNT(DISTINCT CUSTOMER_ID) DISTINCT_CUSTOMERS,
    SUM(CASE
        WHEN od.CUSTOMER_ID <= 5 THEN 1
        ELSE 0
    END) TICKETS_WITH_NO_CUSTOMERS,
    MAX(MORE_THAN_ONE.CUSTOMER_COUNT) MORE_THAN_ONE_VISITS ,
    MAX(ONE_CUSTOMER.CUSTOMER_COUNT) SINGLE_VISITS
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        INNER JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
        LEFT OUTER JOIN
    (SELECT 
        cust.UNIT_ID UNIT_ID,
            COUNT(DISTINCT cust.CUSTOMER_ID) CUSTOMER_COUNT
    FROM
        (SELECT 
        od1.UNIT_ID UNIT_ID,
            od1.CUSTOMER_ID CUSTOMER_ID,
            COUNT(*) CUSTOMER_VISIT_COUNT
    FROM
        KETTLE_DUMP.ORDER_DETAIL od1
    WHERE
        od1.ORDER_STATUS <>'CANCELLED'
    GROUP BY od1.UNIT_ID , od1.CUSTOMER_ID
    HAVING COUNT(*) > 1) cust
    GROUP BY cust.UNIT_ID) MORE_THAN_ONE ON od.UNIT_ID = MORE_THAN_ONE.UNIT_ID
        LEFT OUTER JOIN
    (SELECT 
        cust.UNIT_ID UNIT_ID,
            COUNT(DISTINCT cust.CUSTOMER_ID) CUSTOMER_COUNT
    FROM
        (SELECT 
        od1.UNIT_ID UNIT_ID,
            od1.CUSTOMER_ID CUSTOMER_ID,
            COUNT(*) CUSTOMER_VISIT_COUNT
    FROM
        KETTLE_DUMP.ORDER_DETAIL od1
    WHERE
        od1.ORDER_STATUS <>'CANCELLED'
    GROUP BY od1.UNIT_ID , od1.CUSTOMER_ID
    HAVING COUNT(*) = 1) cust
    GROUP BY cust.UNIT_ID) ONE_CUSTOMER ON od.UNIT_ID = ONE_CUSTOMER.UNIT_ID
WHERE
    od.ORDER_STATUS <>'CANCELLED'
GROUP BY ud.UNIT_CATEGORY,ud.UNIT_ID,ud.UNIT_NAME
ORDER BY ud.UNIT_CATEGORY,ud.UNIT_NAME
]]>
					</content>
				</report>
				<report name="Marketing Promo Code Report" executionType="SQL"
					returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT
   UNIT_DETAIL.UNIT_NAME,
   ORDER_SOURCE,
   OFFER_CODE,
   DATE(BUSINESS_DATE) BUSINESS_DATE,
   CONTACT_NUMBER,
   DATE(CI.ADD_TIME) ADD_TIME,
   COUNT(DISTINCT ORDER_ID) ORDERS,
   ROUND(SUM(TAXABLE_AMOUNT), 0) AMOUNT
FROM
   KETTLE_DUMP.ORDER_DETAIL ORDER_DETAIL
       INNER JOIN
   CLM_ANALYTICS.UNIT_DETAIL UNIT_DETAIL ON UNIT_DETAIL.UNIT_ID = ORDER_DETAIL.UNIT_ID
       INNER JOIN
   KETTLE_DUMP.CUSTOMER_INFO CI ON CI.CUSTOMER_ID = ORDER_DETAIL.CUSTOMER_ID
WHERE
   ORDER_STATUS <> 'CANCELLED'
       AND ORDER_TYPE = 'ORDER'
       AND OFFER_CODE IS NOT NULL
       AND ORDER_DETAIL.OFFER_CODE like CONCAT('%',:offerCode,'%') AND
       ORDER_DETAIL.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate
GROUP BY 1 , 2 , 3 , 4 , 5 , 6
ORDER BY BUSINESS_DATE;
					]]>
					</content>
					<params>
						<param name="offerCode" displayName="Coupon Code"
							dataType="STRING" />
						<param name="fromBusinessDate" displayName="From Business Date"
							dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="To Business Date"
							dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
				<report name="Full to Regular Ratio"
					executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
    UNIT_NAME,
    BUSINESS_DATE,
    concat((ROUND(COALESCE(FULL,0)/COALESCE(REGULAR,1)*100,0)),'%') AS 'FULL TO REGULAR RATIO'
FROM
    (SELECT 
        UNIT_NAME,
            (CASE
                WHEN HOUR(od.BILLING_SERVER_TIME) <= 5 THEN DATE(DATE_ADD(od.BILLING_SERVER_TIME, INTERVAL - 6 HOUR))
                ELSE DATE(od.BILLING_SERVER_TIME)
            END) AS BUSINESS_DATE,
            SUM(CASE
                WHEN DIMENSION = 'FULL' THEN QUANTITY
            END) AS 'FULL',
            SUM(CASE
                WHEN DIMENSION = 'REGULAR' THEN QUANTITY
            END) AS 'REGULAR'
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE_DUMP.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN CLM_ANALYTICS.PRODUCT_DETAILS_BIME PDB ON PDB.PRODUCT_ID = oi.PRODUCT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND IS_COMPLIMENTARY <> 'Y'
            AND od.ORDER_ID > (SELECT 
                MAX(LAST_ORDER_ID) END_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(:fromBusinessDate, 1))
            AND od.ORDER_ID <= (SELECT 
                MAX(LAST_ORDER_ID) END_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = :toBusinessDate)
                    AND DIMENSION IN ('Regular' , 'Full')
    GROUP BY 1 , 2) A
]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="From Business Date"
							dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="To Business Date"
							dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
				<report name="Loyaltea Free Chai"
					executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
(CASE
 WHEN
 HOUR(od.BILLING_SERVER_TIME) <= 5
 
       THEN
            DATE(DATE_ADD(od.BILLING_SERVER_TIME,
 INTERVAL - 6 HOUR))
  
      ELSE DATE(od.BILLING_SERVER_TIME)
    END) AS BUSINESS_DATE,
    ud.UNIT_NAME,
    OFFER_CODE,
    SUM(CASE
        WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
        ELSE 0
    END) NET_TICKETS,
    SUM(od.TAXABLE_AMOUNT)AMOUNT
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        LEFT JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
WHERE
    od.OFFER_CODE = 'LOYALTEA'
        AND od.ORDER_ID > (SELECT 
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :fromBusinessDate)
        AND od.ORDER_ID <= (SELECT 
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)
        AND ORDER_STATUS <> 'CANCELLED'
        AND PROMOTIONAL_DISCOUNT = 0
GROUP BY 1 , 2,3;

]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="From Business Date"
							dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="To Business Date"
							dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
				<report name="Product Sales and Quantity"
					executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
						
SELECT * FROM  ( SELECT        
UNIT_NAME,                        
PRODUCT_DETAILS_BIME.ATTRIBUTE1 PRODUCT_SUB_CATEGORY,
PRODUCT_DETAILS_BIME.PRODUCT_NAME,
count(DISTINCT ORDER_ITEM.ORDER_ID) ORDERS,
SUM(ORDER_ITEM.QUANTITY) QUANTITY,
SUM(ORDER_ITEM.TOTAL_AMOUNT-ORDER_ITEM.PROMOTIONAL_DISCOUNT) AMOUNT
FROM                                
   KETTLE_DUMP.ORDER_DETAIL od                                
       INNER JOIN                                
   CLM_ANALYTICS.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID                                
       INNER JOIN                                
   KETTLE_DUMP.ORDER_ITEM ORDER_ITEM ON od.ORDER_ID = ORDER_ITEM.ORDER_ID
   INNER JOIN CLM_ANALYTICS.PRODUCT_DETAILS_BIME PRODUCT_DETAILS_BIME ON PRODUCT_DETAILS_BIME.PRODUCT_ID=ORDER_ITEM.PRODUCT_ID
    WHERE  od.ORDER_STATUS <>'CANCELLED' AND  ORDER_TYPE='ORDER' AND    od.TOTAL_AMOUNT<>0.00        
    AND od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate and ORDER_ITEM.PRODUCT_NAME LIKE concat('%',:productName,'%') 
GROUP BY 1,2,3) a
						]]>
					</content>
					<params>
						<param name="productName" displayName="Product Name"
							dataType="STRING" />
						<param name="fromBusinessDate" displayName="From Business Date"
							dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="To Business Date"
							dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
			</reports>
		</category>
		<category name="Accounting Reports" type="Accounts"
			accessCode="Accounts">
			<reports>
				<report name="Complimentary Bills Report" executionType="SQL"
					returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT 
	od.BILLING_SERVER_TIME,
    ud.UNIT_NAME,
    ci.FIRST_NAME,
    ci.CONTACT_NUMBER,
    oi.NON_ACCOUNTABLE,
    oi.ACCOUNTABLE,
    ed.EMP_NAME,
    od.GENERATED_ORDER_ID,
    od.ORDER_SOURCE,
    cp.PARTNER_DISPLAY_NAME,
    od.TOTAL_AMOUNT,
    od.TAXABLE_AMOUNT,
    od.SETTLED_AMOUNT,
    od.DISCOUNT_AMOUNT,
    od.PROMOTIONAL_DISCOUNT,
    od.TOTAL_DISCOUNT,
    od.ORDER_STATUS,
    od.SALE_AMOUNT
FROM
    KETTLE_DUMP.ORDER_DETAIL od,
    KETTLE_DUMP.CUSTOMER_INFO ci,
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud,
    KETTLE_MASTER_DUMP.CHANNEL_PARTNER cp,
    KETTLE_MASTER_DUMP.EMPLOYEE_DETAIL ed,
    (SELECT 
        ORDER_ID,
            SUM(CASE
                WHEN COMPLIMENTARY_TYPE_ID IN (2100 , 2102) THEN (PRICE * QUANTITY)
                ELSE 0
            END) NON_ACCOUNTABLE,
            SUM(CASE
                WHEN COMPLIMENTARY_TYPE_ID NOT IN (2100 , 2102) THEN (PRICE * QUANTITY)
                ELSE 0
            END) ACCOUNTABLE
    FROM
        KETTLE_DUMP.ORDER_ITEM
    WHERE
        IS_COMPLIMENTARY = 'Y'
    GROUP BY ORDER_ID) oi
WHERE
    od.ORDER_ID = oi.ORDER_ID
        AND od.UNIT_ID = ud.UNIT_ID
        AND od.CUSTOMER_ID = ci.CUSTOMER_ID
        AND od.CHANNEL_PARTNER_ID = cp.PARTNER_ID
        AND ed.EMP_ID = od.EMP_ID
       AND od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:fromBusinessDate,1))
        AND od.ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)
ORDER BY oi.ACCOUNTABLE DESC , NON_ACCOUNTABLE DESC
						]]>
					</content>
					<params>
						<param name="businessDate" displayName="Business Date"
							dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
			</reports>
		</category>
		<category name="Partner Integration Report" type="Marketing"
			accessCode="Marketing">
			<reports>
				<report name="Offer Code Report"
					executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[	
SELECT 
    od.OFFER_CODE,
    ud.UNIT_NAME,
    ud.UNIT_REGION,
    od.ORDER_ID,
    od.BILLING_SERVER_TIME,
    ci.CONTACT_NUMBER,
    od.SETTLED_AMOUNT
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        LEFT JOIN
    KETTLE_DUMP.CUSTOMER_INFO ci ON od.CUSTOMER_ID = ci.CUSTOMER_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
WHERE
    od.ORDER_STATUS <>'CANCELLED'
        AND od.OFFER_CODE IS NOT NULL
        AND od.OFFER_CODE <> 'COD_FIRST_KETTLE_FREE'
		and od.OFFER_CODE LIKE :offerCode
AND od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:fromBusinessDate,1))
        AND od.ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)
			   			]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="offerCode" displayName="Offer Code" dataType="STRING"/>
					</params>
				</report>
				<report name="Discount Code Wise Report" executionType="SQL"
					returnType="java.lang.String">
					<content>
						<![CDATA[		
SELECT 
    RL_CODE,
    ud.UNIT_NAME,
    ud.UNIT_REGION,
    od.ORDER_ID,
    od.BILLING_SERVER_TIME,
    ci.CONTACT_NUMBER,
    od.SETTLED_AMOUNT
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        LEFT JOIN
    KETTLE_DUMP.CUSTOMER_INFO ci ON od.CUSTOMER_ID = ci.CUSTOMER_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.REF_LOOKUP rl ON od.DISCOUNT_REASON_ID = rl.RL_ID
WHERE
    od.ORDER_STATUS <>'CANCELLED'
AND od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:fromBusinessDate,1))
        AND od.ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)
        AND rl.RL_ID > 2007
		
			]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>

				</report>
			</reports>		    
		</category>
		<category name="Sales Planning Report" type="Marketing"
			accessCode="Marketing">
			<reports>
			<report name="Cold Product Penetration Report" executionType="SQL"
                                        returnType="java.lang.String">
                                        <content>
                                                <![CDATA[
    
SELECT 
    UNIT_NAME,
    BUSINESS_DATE,
    WEEK,
    QUANTITY,
    COLD_QUANTITY,
    CONCAT((ROUND(COALESCE(COLD_QUANTITY, 0) / COALESCE(QUANTITY, 1) * 100,
                    0)),
            '%') AS COLD_CONTRIBUTION
FROM
    (SELECT 
        UNIT_NAME,
            (CASE
                WHEN HOUR(od.BILLING_SERVER_TIME) <= 5 THEN DATE(DATE_ADD(BILLING_SERVER_TIME, INTERVAL - 6 HOUR))
                ELSE DATE(od.BILLING_SERVER_TIME)
            END) AS BUSINESS_DATE,
            WEEK((CASE
                WHEN HOUR(od.BILLING_SERVER_TIME) <= 5 THEN DATE(DATE_ADD(BILLING_SERVER_TIME, INTERVAL - 6 HOUR))
                ELSE DATE(od.BILLING_SERVER_TIME)
            END), 1) AS WEEK,
            SUM(oi.QUANTITY) QUANTITY,
            COALESCE(SUM(CASE
                WHEN PRODUCT_TYPE = 6 THEN QUANTITY
            END), 0) AS COLD_QUANTITY
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE_DUMP.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN CLM_ANALYTICS.PRODUCT_DETAILS_BIME PDB ON PDB.PRODUCT_ID = oi.PRODUCT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND IS_COMPLIMENTARY <> 'Y'
            AND od.ORDER_ID > (SELECT 
                MAX(LAST_ORDER_ID) START_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(:fromBusinessDate, 1))
            AND od.ORDER_ID <= (SELECT 
                MAX(LAST_ORDER_ID) END_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = :toBusinessDate)
    GROUP BY 1 , 2 , 3) A;


                        ]]>
                                        </content>
                                        <params>
                                                <param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
                                                <param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
                                        </params>

                                </report>

				<report name="Ticket wise data"
					executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
					
SELECT ud.UNIT_NAME,     (CASE WHEN DAYOFWEEK(CASE
        WHEN
            HOUR(od.BILLING_SERVER_TIME) <= 5
        THEN
            DATE(DATE_ADD(od.BILLING_SERVER_TIME,
                    INTERVAL - 6 HOUR))
        ELSE DATE(od.BILLING_SERVER_TIME)
    END) IN (1,7) THEN 'WE' ELSE 'WD' END) AS DAY_OF_WEEK,
   nt2.NO_OF_DAYS,
 (CASE WHEN FLOOR(HOUR(od.BILLING_SERVER_TIME))+1 IN (1,2,3,4,5,6,23,24) THEN 'E_Post_Dinner' 
 ELSE (CASE WHEN FLOOR(HOUR(od.BILLING_SERVER_TIME))+1  IN (7,8,9,10,11) THEN 'A_Breakfast'
 ELSE (CASE WHEN FLOOR(HOUR(od.BILLING_SERVER_TIME))+1 IN (12,13,14,15) THEN 'B_Lunch'        
 ELSE  (CASE WHEN FLOOR(HOUR(od.BILLING_SERVER_TIME))+1 IN (16,17,18,19) THEN 'C_Evening' ELSE 'D_Dinner' END)
 END)
 END)
 END) AS CATEGORY, 
 SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS TKT,  
            
            
            SUM(od.TAXABLE_AMOUNT) AS SALES,
            
             TRUNCATE(SUM(od.TAXABLE_AMOUNT)/SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END),0) AS APC
            
        
    FROM KETTLE_DUMP.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud
    ON od.UNIT_ID=ud.UNIT_ID
    LEFT JOIN (
   SELECT nt.UNIT_NAME,nt.DAY_OF_WEEK,COUNT(DISTINCT nt.BUSINESS_DATE) AS NO_OF_DAYS
FROM (
 SELECT ud.UNIT_NAME,
    (CASE WHEN DAYOFWEEK(CASE
        WHEN
            HOUR(od.BILLING_SERVER_TIME) <= 5
        THEN
            DATE(DATE_ADD(od.BILLING_SERVER_TIME,
                    INTERVAL - 6 HOUR))
        ELSE DATE(od.BILLING_SERVER_TIME)
    END) IN (1,7) THEN 'WE' ELSE 'WD' END) AS DAY_OF_WEEK,
     (CASE
        WHEN
            HOUR(od.BILLING_SERVER_TIME) <= 5
        THEN
            DATE(DATE_ADD(od.BILLING_SERVER_TIME,
                    INTERVAL - 6 HOUR))
        ELSE DATE(od.BILLING_SERVER_TIME)
    END) AS BUSINESS_DATE
    
       
    FROM KETTLE_DUMP.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud
    ON od.UNIT_ID=ud.UNIT_ID
       WHERE od.ORDER_STATUS <>'CANCELLED'
AND od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:fromBusinessDate,1))
        AND od.ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)
 GROUP BY ud.UNIT_NAME,DAY_OF_WEEK,BUSINESS_DATE) AS nt
 GROUP BY nt.UNIT_NAME,nt.DAY_OF_WEEK) AS nt2
    
    ON ud.UNIT_NAME=nt2.UNIT_NAME AND 
    (CASE WHEN DAYOFWEEK(CASE
        WHEN
            HOUR(od.BILLING_SERVER_TIME) <= 5
        THEN
            DATE(DATE_ADD(od.BILLING_SERVER_TIME,
                    INTERVAL - 6 HOUR))
        ELSE DATE(od.BILLING_SERVER_TIME)
    END) IN (1,7) THEN 'WE' ELSE 'WD' END) =nt2.DAY_OF_WEEK
    
    
    WHERE od.ORDER_STATUS <>'CANCELLED'
AND od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:fromBusinessDate,1))
        AND od.ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)
    GROUP BY ud.UNIT_NAME,DAY_OF_WEEK,CATEGORY
]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>

				</report>

				<report name="Item category wise data"
					executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[				
SELECT 
    ud.UNIT_NAME,
    (CASE WHEN DAYOFWEEK(CASE
        WHEN
            HOUR(od.BILLING_SERVER_TIME) <= 5
        THEN
            DATE(DATE_ADD(od.BILLING_SERVER_TIME,
                    INTERVAL - 6 HOUR))
        ELSE DATE(od.BILLING_SERVER_TIME)
    END) IN (1,7) THEN 'WE' ELSE 'WD' END) AS DAY_OF_WEEK,
    
  nt2.NO_OF_DAYS,
    
    (CASE WHEN FLOOR(HOUR(od.BILLING_SERVER_TIME))+1 IN (1,2,3,4,5,6,23,24) THEN 'E_Post_Dinner' 
 ELSE (CASE WHEN FLOOR(HOUR(od.BILLING_SERVER_TIME))+1  IN (7,8,9,10,11) THEN 'A_Breakfast'
 ELSE (CASE WHEN FLOOR(HOUR(od.BILLING_SERVER_TIME))+1 IN (12,13,14,15) THEN 'B_Lunch'        
 ELSE  (CASE WHEN FLOOR(HOUR(od.BILLING_SERVER_TIME))+1 IN (16,17,18,19) THEN 'C_Evening' ELSE 'D_Dinner' END)
 END)
 END)
 END) AS CATEGORY,
  
    rl.RTL_CODE,
     SUM(oi.QUANTITY) AS QUANTITY,
     SUM(oi.QUANTITY*oi.PRICE) AS GMV
     
     FROM
    KETTLE_DUMP.ORDER_DETAIL od
   LEFT JOIN KETTLE_DUMP.ORDER_ITEM oi
    on od.ORDER_ID = oi.ORDER_ID
   LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud
    ON ud.UNIT_ID = od.UNIT_ID
   LEFT JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd
    ON pd.PRODUCT_ID=oi.PRODUCT_ID
   LEFT JOIN KETTLE_MASTER_DUMP.REF_LOOKUP_TYPE rl
    ON pd.PRODUCT_TYPE=rl.RTL_ID
    LEFT JOIN (
   SELECT nt.UNIT_NAME,nt.DAY_OF_WEEK,COUNT(DISTINCT nt.BUSINESS_DATE) AS NO_OF_DAYS
FROM (
 SELECT ud.UNIT_NAME,
    (CASE WHEN DAYOFWEEK(CASE
        WHEN
            HOUR(BILLING_SERVER_TIME) <= 5
        THEN
            DATE(DATE_ADD(BILLING_SERVER_TIME,
                    INTERVAL - 6 HOUR))
        ELSE DATE(BILLING_SERVER_TIME)
    END) IN (1,7) THEN 'WE' ELSE 'WD' END) AS DAY_OF_WEEK,
     (CASE
        WHEN
            HOUR(BILLING_SERVER_TIME) <= 5
        THEN
            DATE(DATE_ADD(BILLING_SERVER_TIME,
                    INTERVAL - 6 HOUR))
        ELSE DATE(BILLING_SERVER_TIME)
    END) AS BUSINESS_DATE
    
    FROM KETTLE_DUMP.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud
    ON od.UNIT_ID=ud.UNIT_ID
       WHERE od.ORDER_STATUS <>'CANCELLED'
AND od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:fromBusinessDate,1))
        AND od.ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)
 GROUP BY ud.UNIT_NAME,DAY_OF_WEEK,BUSINESS_DATE) AS nt
 GROUP BY nt.UNIT_NAME,nt.DAY_OF_WEEK) AS nt2
    
    ON ud.UNIT_NAME=nt2.UNIT_NAME AND 
    (CASE WHEN DAYOFWEEK(CASE
        WHEN
            HOUR(BILLING_SERVER_TIME) <= 5
        THEN
            DATE(DATE_ADD(BILLING_SERVER_TIME,
                    INTERVAL - 6 HOUR))
        ELSE DATE(BILLING_SERVER_TIME)
    END) IN (1,7) THEN 'WE' ELSE 'WD' END) =nt2.DAY_OF_WEEK
    
     
     WHERE COALESCE(oi.COMPLIMENTARY_TYPE_ID,0) NOT IN (2100,2102,2105,2106)
     
        AND   od.ORDER_STATUS <>'CANCELLED'
AND od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:fromBusinessDate,1))
        AND od.ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)
GROUP BY ud.UNIT_NAME ,DAY_OF_WEEK,NO_OF_DAYS,CATEGORY, rl.RTL_CODE 
]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>					
			</reports>					
		</category>
		<category name="Customer Acquisition Report" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>,<EMAIL>,<EMAIL>"
                        schedule="">
			<reports>
				<report id="1" name="Daily Customer Acquisition Summary Report" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT 
    *
FROM
    (SELECT
	:businessDate AS 'BUSINESS_DATE',
        A.UNIT_ID,
            Z.UNIT_NAME,
            COALESCE(A.NEW_CAFE_CUSTOMER, 0) 'NEW_CAFE_CUSTOMER',
            COALESCE(A.OLD_CAFE_CUSTOMER, 0) 'OLD_CAFE_CUSTOMER',
            COALESCE(B.NEW_COD_CUSTOMER, 0) 'NEW_COD_CUSTOMER',
            COALESCE(B.OLD_COD_CUSTOMER, 0) 'OLD_COD_CUSTOMER'
    FROM
        KETTLE_MASTER_DUMP.UNIT_DETAIL Z
    INNER JOIN (SELECT 
        UNIT_ID,
            COUNT(DISTINCT (CASE
                WHEN
                    ci.CUSTOMER_ID > (SELECT 
                            MAX(CUSTOMER_ID)
                        FROM
                            KETTLE_DUMP.ORDER_DETAIL
                        WHERE
                            ORDER_ID <= (SELECT 
                                    MAX(LAST_ORDER_ID)
                                FROM
                                    KETTLE_DUMP.UNIT_CLOSURE_DETAILS
                                WHERE
                                    BUSINESS_DATE = SUBDATE(:businessDate, 1)))
                THEN
                    ci.CUSTOMER_ID
            END)) NEW_CAFE_CUSTOMER,
            COUNT(DISTINCT (CASE
                WHEN
                    ci.CUSTOMER_ID < (SELECT 
                            MAX(CUSTOMER_ID)
                        FROM
                            KETTLE_DUMP.ORDER_DETAIL
                        WHERE
                            ORDER_ID <= (SELECT 
                                    MAX(LAST_ORDER_ID)
                                FROM
                                    KETTLE_DUMP.UNIT_CLOSURE_DETAILS
                                WHERE
                                    BUSINESS_DATE = SUBDATE(:businessDate, 1)))
                THEN
                    ci.CUSTOMER_ID
            END)) OLD_CAFE_CUSTOMER
    FROM
        KETTLE_DUMP.CUSTOMER_INFO ci, KETTLE_DUMP.ORDER_DETAIL od
    WHERE
        od.ORDER_ID > (SELECT 
                MAX(LAST_ORDER_ID)
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(:businessDate, 1))
			 AND od.ORDER_ID <= (SELECT 
                MAX(LAST_ORDER_ID) END_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = :businessDate)
            AND od.CUSTOMER_ID = ci.CUSTOMER_ID
            AND od.CUSTOMER_ID > 5
            AND ORDER_SOURCE <> 'COD'
    GROUP BY UNIT_ID) A ON A.UNIT_ID = Z.UNIT_ID
    LEFT JOIN (SELECT 
        UNIT_ID,
            COUNT(DISTINCT (CASE
                WHEN
                    ci.CUSTOMER_ID > (SELECT 
                            MAX(CUSTOMER_ID)
                        FROM
                            KETTLE_DUMP.ORDER_DETAIL
                        WHERE
                            ORDER_ID <= (SELECT 
                                    MAX(LAST_ORDER_ID)
                                FROM
                                    KETTLE_DUMP.UNIT_CLOSURE_DETAILS
                                WHERE
                                    BUSINESS_DATE = SUBDATE(:businessDate, 1)))
                THEN
                    ci.CUSTOMER_ID
            END)) NEW_COD_CUSTOMER,
            COUNT(DISTINCT (CASE
                WHEN
                    ci.CUSTOMER_ID < (SELECT 
                            MAX(CUSTOMER_ID)
                        FROM
                            KETTLE_DUMP.ORDER_DETAIL
                        WHERE
                            ORDER_ID <= (SELECT 
                                    MAX(LAST_ORDER_ID)
                                FROM
                                    KETTLE_DUMP.UNIT_CLOSURE_DETAILS
                                WHERE
                                    BUSINESS_DATE = SUBDATE(:businessDate, 1)))
                THEN
                    ci.CUSTOMER_ID
            END)) OLD_COD_CUSTOMER
    FROM
        KETTLE_DUMP.CUSTOMER_INFO ci, KETTLE_DUMP.ORDER_DETAIL od
    WHERE
        od.ORDER_ID > (SELECT 
                MAX(LAST_ORDER_ID)
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(:businessDate, 1))
            AND od.ORDER_ID <= (SELECT 
                MAX(LAST_ORDER_ID) END_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = :businessDate)
            AND od.CUSTOMER_ID = ci.CUSTOMER_ID
            AND od.CUSTOMER_ID > 5
            AND ORDER_SOURCE = 'COD'
    GROUP BY UNIT_ID) B ON B.UNIT_ID = Z.UNIT_ID) D
				    ]]>
					</content>
					<params>
						<param name="businessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
				<report id="1" name="Daily Customer Acquisition Detail Report" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT
DISTINCT
    ud.UNIT_ID,
    ud.UNIT_NAME,
    (ci.CONTACT_NUMBER) CONTACT_NUMBER,
    ci.IS_NUMBER_VERIFIED,
    case when ci.CUSTOMER_ID > (select max(CUSTOMER_ID)
from KETTLE_DUMP.ORDER_DETAIL
where ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID)
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:businessDate, 2))) then 'NEW' ELSE 'OLD' END NEW_OR_OLD,
                COUNT(DISTINCT od.ORDER_ID) ORDER_COUNT
FROM
    KETTLE_DUMP.CUSTOMER_INFO ci,
    KETTLE_DUMP.ORDER_DETAIL od,
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud
WHERE
    od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID)
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:businessDate, 2))
		AND od.ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :businessDate)
		
        AND od.CUSTOMER_ID = ci.CUSTOMER_ID
        and
        od.UNIT_ID=ud.UNIT_ID
        and od.CUSTOMER_ID > 5
        group by ud.UNIT_ID, CONTACT_NUMBER
        order by ud.UNIT_ID, CONTACT_NUMBER
				    ]]>
					</content>
					<params>
						<param name="businessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
				<report id="1" name="Customer Acquisition For a date range" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT
DISTINCT
	ud.UNIT_ID,
    ud.UNIT_NAME,
    (ci.CONTACT_NUMBER) CONTACT_NUMBER,
    ci.IS_NUMBER_VERIFIED
    
FROM
    KETTLE_DUMP.CUSTOMER_INFO ci,
    KETTLE_DUMP.ORDER_DETAIL od,
    KETTLE_DUMP.UNIT_DETAIL ud
   
WHERE
    od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID)
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:fromBusinessDate, 1))
		AND  od.ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID)
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)

        AND od.CUSTOMER_ID = ci.CUSTOMER_ID
        and
        od.UNIT_ID=ud.UNIT_ID
        and od.CUSTOMER_ID > 5
        order by ud.UNIT_ID, CONTACT_NUMBER
				    ]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
			</reports>
		</category>
		<category name="Sales Summary Report" type="MarketingReport"
                        accessCode="MarketingReport" id="1">
			<reports>
				<report id="1" name="Unit wise Item wise Dimesion wise Quantity and Amount" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT 
    ud.UNIT_NAME,
    oi.PRODUCT_NAME,
    oi.DIMENSION,
    SUM(oi.QUANTITY) QUANTITY,
    SUM(oi.AMOUNT_PAID - oi.PROMOTIONAL_DISCOUNT) AMOUNT,
    MONTHNAME(od.BILLING_SERVER_TIME) MONTH
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        LEFT JOIN
    KETTLE_DUMP.ORDER_ITEM oi ON oi.ORDER_ID = od.ORDER_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
WHERE
    od.ORDER_ID > (SELECT 
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:fromBusinessDate, 1))
        AND od.ORDER_ID <= (SELECT 
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)
        AND od.ORDER_STATUS <> 'CANCELLED'
        AND od.TOTAL_AMOUNT > 0.0
GROUP BY od.UNIT_ID , oi.PRODUCT_ID , oi.DIMENSION, MONTH(od.BILLING_SERVER_TIME);
				    ]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
				<report id="2" name="Unit wise Locality wise Count of Delivery Orders" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT
    ud.UNIT_ID,
    ud.UNIT_NAME,
    cia.LOCALITY,
    SUM(od.TAXABLE_AMOUNT) TAXABLE_AMOUNT,
    COUNT(od.ORDER_ID) ORDER_COUNT,
    SUM(od.TAXABLE_AMOUNT) / COUNT(ORDER_ID) APC
FROM
    KETTLE_DUMP.ORDER_DETAIL od,
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud,
    KETTLE_DUMP.CUSTOMER_ADDRESS_INFO cia
WHERE
        od.DELIVERY_ADDRESS = cia.ADDRESS_ID
        AND od.ORDER_STATUS <> 'CANCELLED'
        AND od.ORDER_SOURCE = 'COD'
        AND od.UNIT_ID = ud.UNIT_ID
        AND od.TOTAL_AMOUNT > 0.0
        AND od.ORDER_ID > (SELECT 
                MAX(LAST_ORDER_ID) START_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(:fromBusinessDate, 1))
		AND od.ORDER_ID <= (SELECT 
                MAX(LAST_ORDER_ID) END_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = :toBusinessDate)
		AND ORDER_SOURCE = 'COD'
GROUP BY ud.UNIT_ID , ud.UNIT_NAME , cia.LOCALITY
ORDER BY ud.UNIT_ID , ud.UNIT_NAME , cia.LOCALITY;
				    ]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
				<report id="3" name="Unit wise Day wise Hour Wise Count of Orders, Sales and Food percentage" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT
    ud.UNIT_ID,
    ud.UNIT_NAME,
    (CASE
        WHEN
            HOUR(od.BILLING_SERVER_TIME) <= 5
        THEN
            DATE(DATE_ADD(od.BILLING_SERVER_TIME,
                    INTERVAL - 6 HOUR))
        ELSE DATE(od.BILLING_SERVER_TIME)
    END) AS BUSINESS_DATE,
    HOUR(od.BILLING_SERVER_TIME) HOUR_OF_DAY,
    count(DISTINCT od.ORDER_ID) ORDERS,
    SUM(od.TAXABLE_AMOUNT) NET_SALES,
    SUM(b.FOOD_ITEMS) / SUM(b.TOTAL_ITEMS) FOOD_PERCENTAGE
FROM
    KETTLE_DUMP.ORDER_DETAIL od,
    UNIT_DETAIL ud,
    (SELECT
        oi.ORDER_ID,
            SUM(CASE
                WHEN pd.PRODUCT_TYPE = 7 THEN oi.QUANTITY
                ELSE 0
            END) FOOD_ITEMS,
            SUM(oi.QUANTITY) TOTAL_ITEMS
    FROM
        KETTLE_DUMP.ORDER_ITEM oi
    INNER JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    WHERE
        oi.ORDER_ID > (SELECT
                MAX(LAST_ORDER_ID) START_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(:fromBusinessDate, 1))
            AND oi.ORDER_ID <= (SELECT
                MAX(LAST_ORDER_ID) END_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = :toBusinessDate)
    GROUP BY oi.ORDER_ID) b
WHERE
    od.ORDER_ID = b.ORDER_ID
        AND od.ORDER_STATUS <> 'CANCELLED'
        AND od.TOTAL_AMOUNT <> '0.00'
        AND od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:fromBusinessDate, 1))
        AND od.ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)
        AND od.UNIT_ID = ud.UNIT_ID
GROUP BY ud.UNIT_ID , ud.UNIT_NAME , BUSINESS_DATE , HOUR_OF_DAY;
				    ]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
				<report id="4" name="Unit wise Month wise Discount Promotinal discount Cancelled tickets" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT 
    ud.UNIT_NAME,
    MONTHNAME(CASE
            WHEN
                HOUR(od.BILLING_SERVER_TIME) <= 5
            THEN
                DATE(DATE_ADD(od.BILLING_SERVER_TIME,
                        INTERVAL - 6 HOUR))
            ELSE DATE(od.BILLING_SERVER_TIME)
        END) MONTH_OF_ORDER,
    SUM(CASE
        WHEN od.ORDER_STATUS <> 'CANCELLED' THEN od.DISCOUNT_AMOUNT
        ELSE 0
    END) TOTAL_DISCOUNT_AMOUNT_BY_PERCENT,
	SUM(CASE
        WHEN od.ORDER_STATUS <> 'CANCELLED' THEN od.PROMOTIONAL_DISCOUNT
        ELSE 0
    END) TOTAL_PROMOTIONAL_DISCOUNT,
    SUM(CASE
        WHEN od.ORDER_STATUS <> 'CANCELLED' THEN od.TOTAL_AMOUNT
        ELSE 0
    END) TOTAL_AMOUNT,
    SUM(CASE
        WHEN od.ORDER_STATUS = 'CANCELLED' THEN 1
        ELSE 0
    END) CANCELLED_TICKETS,
        SUM(CASE
        WHEN od.ORDER_STATUS <> 'CANCELLED' THEN 1
        ELSE 0
    END) SETTLED_ORDERS
FROM
    ORDER_DETAIL od,
    UNIT_DETAIL ud
WHERE
    od.ORDER_ID > (SELECT 
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:fromBusinessDate, 1))
        AND od.ORDER_ID <= (SELECT 
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)
        AND od.UNIT_ID = ud.UNIT_ID
GROUP BY ud.UNIT_ID , ud.UNIT_NAME , MONTH_OF_ORDER;
				    ]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
			</reports>
		</category>
	</categories>
</ReportCategories>

