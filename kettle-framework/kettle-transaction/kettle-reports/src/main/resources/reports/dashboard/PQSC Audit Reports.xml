<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<ReportCategories xmlns="http://www.w3schools.com">
    <categories>
        <category name="Auditor Weekly Reports" type="Auditor Weekly Reports" accessCode="PQSC">
            <reports>
                <report id="1" name="Category wise percentage score" executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[
							  
SELECT 
    *
FROM
    (SELECT 
        T.LINKED_ENTITY_ID,
            afv.ENTITY_LABEL,
            IFNULL(SUM(T.ACQUIRED_SCORE), 0) AS ACQUIRED_SCORE,
            SUM(T.MAX_SCORE) AS MAX_SCORE,
            CONCAT(ROUND((IFNULL(SUM(T.ACQUIRED_SCORE), 0) / SUM(T.MAX_SCORE)) * 100), '%') AS PERCENTAGE,
			 COUNT(DISTINCT T.ID) as AUDIT_COUNT
    FROM
        (SELECT 
        av.ACQUIRED_SCORE, av.MAX_SCORE, afv.LINKED_ENTITY_ID, a.ID
    FROM
        KETTLE_DUMP.AUDIT_DETAIL a
    INNER JOIN KETTLE_DUMP.AUDIT_DETAIL_VALUES av ON a.ID = av.AUDIT_DETAIL_ID
        AND a.AUDIT_TIME >= DATE(:startDate)
        AND a.AUDIT_TIME <= DATE(:endDate)
		AND a.AUDIT_TYPE = 'REGISTERED'
    INNER JOIN KETTLE_DUMP.AUDIT_FORM_VALUES afv ON afv.ID = av.FORM_VALUE_ID AND afv.SCORE_COUNTED = 'Y'
    INNER JOIN KETTLE_DUMP.AUDIT_FORM af ON af.ID = afv.AUDIT_FORM_ID AND af.ID = 2) T
    INNER JOIN KETTLE_DUMP.AUDIT_FORM_VALUES afv ON afv.ID = T.LINKED_ENTITY_ID
    GROUP BY T.LINKED_ENTITY_ID UNION (SELECT 
        0,
            'Total',
            IFNULL(SUM(av.ACQUIRED_SCORE), 0) AS ACQUIRED_SCORE,
            SUM(av.MAX_SCORE) AS MAX_SCORE,
            CONCAT(ROUND((SUM(av.ACQUIRED_SCORE) / SUM(av.MAX_SCORE)) * 100), '%') AS PERCENTAGE,
			COUNT(DISTINCT a.ID) as AUDIT_COUNT
    FROM
        KETTLE_DUMP.AUDIT_DETAIL_VALUES av
    INNER JOIN KETTLE_DUMP.AUDIT_DETAIL a ON a.ID = av.AUDIT_DETAIL_ID
        AND a.AUDIT_TIME >= DATE(:startDate)
        AND a.AUDIT_TIME <= DATE(:endDate)
		AND a.AUDIT_TYPE = 'REGISTERED'
        INNER JOIN KETTLE_DUMP.AUDIT_FORM_VALUES afv ON afv.ID = av.FORM_VALUE_ID AND afv.SCORE_COUNTED = 'Y'
        INNER JOIN KETTLE_DUMP.AUDIT_FORM af ON af.ID = afv.AUDIT_FORM_ID AND af.ID = 2)) M;         
			
			]]>
                    </content>
                    <params>
                        <param name="startDate" displayName="Start Date" dataType="DATE" format="yyyy-MM-dd" />
                        <param name="endDate" displayName="End Date" dataType="DATE" format="yyyy-MM-dd" />
                    </params>
                </report>
                <report id="2" name="Cafe and Category wise percentage score" executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[
							  
        SELECT * FROM
    (SELECT * FROM
    (SELECT 
    T.UNIT_ID, T.UNIT_NAME,
        T.LINKED_ENTITY_ID,
            afv.ENTITY_LABEL,
            IFNULL(SUM(T.ACQUIRED_SCORE), 0) AS ACQUIRED_SCORE,
            SUM(T.MAX_SCORE) AS MAX_SCORE,
            CONCAT(ROUND((IFNULL(SUM(T.ACQUIRED_SCORE), 0) / SUM(T.MAX_SCORE)) * 100), '%') AS PERCENTAGE
    FROM
        (SELECT u.UNIT_ID, u.UNIT_NAME, av.ACQUIRED_SCORE, av.MAX_SCORE, afv.LINKED_ENTITY_ID FROM
        KETTLE_DUMP.AUDIT_DETAIL a
    INNER JOIN KETTLE_DUMP.AUDIT_DETAIL_VALUES av ON a.ID = av.AUDIT_DETAIL_ID
        AND a.AUDIT_TIME >= DATE(:startDate)
        AND a.AUDIT_TIME <= DATE(:endDate)
		AND a.AUDIT_TYPE = 'REGISTERED'
    INNER JOIN KETTLE_DUMP.AUDIT_FORM_VALUES afv ON afv.ID = av.FORM_VALUE_ID AND afv.SCORE_COUNTED = 'Y'
    INNER JOIN KETTLE_DUMP.AUDIT_FORM af ON af.ID = afv.AUDIT_FORM_ID AND af.ID = 2
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL u ON u.UNIT_ID=a.AUDIT_UNIT_ID
    ) T
    INNER JOIN KETTLE_DUMP.AUDIT_FORM_VALUES afv ON afv.ID = T.LINKED_ENTITY_ID
    GROUP BY T.LINKED_ENTITY_ID, T.UNIT_ID
    UNION
    (SELECT 
        u.UNIT_ID, u.UNIT_NAME, 0,
            'Total',
            IFNULL(SUM(av.ACQUIRED_SCORE), 0) AS ACQUIRED_SCORE,
            SUM(av.MAX_SCORE) AS MAX_SCORE,
            CONCAT(ROUND((SUM(av.ACQUIRED_SCORE) / SUM(av.MAX_SCORE)) * 100), '%') AS PERCENTAGE
    FROM
        KETTLE_DUMP.AUDIT_DETAIL_VALUES av
    INNER JOIN KETTLE_DUMP.AUDIT_DETAIL a ON a.ID = av.AUDIT_DETAIL_ID
        AND a.AUDIT_TIME >= DATE(:startDate)
        AND a.AUDIT_TIME <= DATE(:endDate)
		AND a.AUDIT_TYPE = 'REGISTERED'
        INNER JOIN KETTLE_DUMP.AUDIT_FORM_VALUES afv ON afv.ID = av.FORM_VALUE_ID AND afv.SCORE_COUNTED = 'Y'
        INNER JOIN KETTLE_DUMP.AUDIT_FORM af ON af.ID = afv.AUDIT_FORM_ID AND af.ID = 2
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL u ON u.UNIT_ID=a.AUDIT_UNIT_ID GROUP BY u.UNIT_ID) 
     UNION
    (SELECT 
        u.UNIT_ID, u.UNIT_NAME, 0,
            'Projected',
            IFNULL(SUM(a.PROJECTED_ACQUIRED_SCORE), 0) AS PROJECTED_ACQUIRED_SCORE,
            SUM(a.PROJECTED_TOTAL_SCORE) AS PROJECTED_TOTAL_SCORE,
            CONCAT(ROUND((SUM(a.PROJECTED_ACQUIRED_SCORE) / SUM(a.PROJECTED_TOTAL_SCORE)) * 100), '%') AS PROJECTED_PERCENTAGE
    FROM
        KETTLE_DUMP.AUDIT_DETAIL_VALUES av
    INNER JOIN KETTLE_DUMP.AUDIT_DETAIL a ON a.ID = av.AUDIT_DETAIL_ID
        AND a.AUDIT_TIME >= DATE(:startDate)
        AND a.AUDIT_TIME <= DATE(:endDate)
		AND a.AUDIT_TYPE = 'REGISTERED'
        INNER JOIN KETTLE_DUMP.AUDIT_FORM_VALUES afv ON afv.ID = av.FORM_VALUE_ID AND afv.SCORE_COUNTED = 'Y'
        INNER JOIN KETTLE_DUMP.AUDIT_FORM af ON af.ID = afv.AUDIT_FORM_ID AND af.ID = 2
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL u ON u.UNIT_ID=a.AUDIT_UNIT_ID GROUP BY u.UNIT_ID) ) M  ORDER BY M.UNIT_ID, M.LINKED_ENTITY_ID) N;
			
			]]>
                    </content>
                    <params>
                        <param name="startDate" displayName="Start Date" dataType="DATE" format="yyyy-MM-dd" />
                        <param name="endDate" displayName="End Date" dataType="DATE" format="yyyy-MM-dd" />
                    </params>
                </report>
                <report id="3" name="Category wise percentage score for single unit" executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[
							  
        SELECT * FROM
    (SELECT * FROM
    (SELECT 
    T.UNIT_ID, T.UNIT_NAME,
        T.LINKED_ENTITY_ID,
            afv.ENTITY_LABEL,
            IFNULL(SUM(T.ACQUIRED_SCORE), 0) AS ACQUIREC_SCORE,
            SUM(T.MAX_SCORE) AS MAX_CORE,
            CONCAT(ROUND((IFNULL(SUM(T.ACQUIRED_SCORE), 0) / SUM(T.MAX_SCORE)) * 100), '%') AS PERCENTAGE
    FROM
        (SELECT u.UNIT_ID, u.UNIT_NAME, av.ACQUIRED_SCORE, av.MAX_SCORE, afv.LINKED_ENTITY_ID FROM
        KETTLE_DUMP.AUDIT_DETAIL a
    INNER JOIN KETTLE_DUMP.AUDIT_DETAIL_VALUES av ON a.ID = av.AUDIT_DETAIL_ID
       AND a.AUDIT_TIME >= DATE(:startDate)
        AND a.AUDIT_TIME <= DATE(:endDate)
		AND a.AUDIT_UNIT_ID = :unitId
		AND a.AUDIT_TYPE = 'REGISTERED'
    INNER JOIN KETTLE_DUMP.AUDIT_FORM_VALUES afv ON afv.ID = av.FORM_VALUE_ID AND afv.SCORE_COUNTED = 'Y'
    INNER JOIN KETTLE_DUMP.AUDIT_FORM af ON af.ID = afv.AUDIT_FORM_ID AND af.ID = 2
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL u ON u.UNIT_ID=a.AUDIT_UNIT_ID
    ) T
    INNER JOIN KETTLE_DUMP.AUDIT_FORM_VALUES afv ON afv.ID = T.LINKED_ENTITY_ID
    GROUP BY T.LINKED_ENTITY_ID, T.UNIT_ID
    UNION
    (SELECT 
        u.UNIT_ID, u.UNIT_NAME, 0,
            'Total',
            IFNULL(SUM(av.ACQUIRED_SCORE), 0) AS ACQUIRED_SCORE,
            SUM(av.MAX_SCORE) AS MAX_SCORE,
            CONCAT(ROUND((SUM(av.ACQUIRED_SCORE) / SUM(av.MAX_SCORE)) * 100), '%') AS PERCENTAGE
    FROM
        KETTLE_DUMP.AUDIT_DETAIL_VALUES av
    INNER JOIN KETTLE_DUMP.AUDIT_DETAIL a ON a.ID = av.AUDIT_DETAIL_ID
        AND a.AUDIT_TIME >= DATE(:startDate)
        AND a.AUDIT_TIME <= DATE(:endDate)
		AND a.AUDIT_UNIT_ID = :unitId
		AND a.AUDIT_TYPE = 'REGISTERED'
        INNER JOIN KETTLE_DUMP.AUDIT_FORM_VALUES afv ON afv.ID = av.FORM_VALUE_ID AND afv.SCORE_COUNTED = 'Y'
        INNER JOIN KETTLE_DUMP.AUDIT_FORM af ON af.ID = afv.AUDIT_FORM_ID AND af.ID = 2
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL u ON u.UNIT_ID=a.AUDIT_UNIT_ID GROUP BY u.UNIT_ID) 
   UNION
    (SELECT 
        u.UNIT_ID, u.UNIT_NAME, 0,
            'Projected',
            IFNULL(SUM(a.PROJECTED_ACQUIRED_SCORE), 0) AS PROJECTED_ACQUIRED_SCORE,
            SUM(a.PROJECTED_TOTAL_SCORE) AS PROJECTED_TOTAL_SCORE,
            CONCAT(ROUND((SUM(a.PROJECTED_ACQUIRED_SCORE) / SUM(a.PROJECTED_TOTAL_SCORE)) * 100), '%') AS PROJECTED_PERCENTAGE
    FROM
        KETTLE_DUMP.AUDIT_DETAIL_VALUES av
    INNER JOIN KETTLE_DUMP.AUDIT_DETAIL a ON a.ID = av.AUDIT_DETAIL_ID
        AND a.AUDIT_TIME >= DATE(:startDate)
        AND a.AUDIT_TIME <= DATE(:endDate)
		AND a.AUDIT_UNIT_ID = :unitId
		AND a.AUDIT_TYPE = 'REGISTERED'
        INNER JOIN KETTLE_DUMP.AUDIT_FORM_VALUES afv ON afv.ID = av.FORM_VALUE_ID AND afv.SCORE_COUNTED = 'Y'
        INNER JOIN KETTLE_DUMP.AUDIT_FORM af ON af.ID = afv.AUDIT_FORM_ID AND af.ID = 2
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL u ON u.UNIT_ID=a.AUDIT_UNIT_ID GROUP BY u.UNIT_ID) ) M  ORDER BY M.UNIT_ID, M.LINKED_ENTITY_ID) N;
			
			]]>
                    </content>
                    <params>
                        <param name="startDate" displayName="Start Date" dataType="DATE" format="yyyy-MM-dd" />
                        <param name="endDate" displayName="End Date" dataType="DATE" format="yyyy-MM-dd" />
                        <param name="unitId" displayName="Unit Id" dataType="INTEGER" />
                    </params>
                </report>
                <report id="4" name="Cafe wise overall score" executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[
							  
        SELECT * FROM (SELECT 
    T.UNIT_ID,
    T.UNIT_NAME,
    T.UNIT_REGION,
    COUNT(T.ID) AS AUDITS,
    ROUND(AVG(T.PERCENTAGE)) AS SCORE
FROM
    (SELECT 
        u.UNIT_ID,
            u.UNIT_NAME,
            u.UNIT_REGION,
            a.ID,
            ROUND(IFNULL(SUM(av.ACQUIRED_SCORE), 0) / SUM(av.MAX_SCORE) * 100) AS PERCENTAGE
    FROM
        KETTLE_DUMP.AUDIT_DETAIL_VALUES av
    INNER JOIN KETTLE_DUMP.AUDIT_DETAIL a ON a.ID = av.AUDIT_DETAIL_ID
        AND a.AUDIT_TYPE = 'REGISTERED'
    INNER JOIN KETTLE_DUMP.AUDIT_FORM_VALUES afv ON afv.ID = av.FORM_VALUE_ID
        AND afv.SCORE_COUNTED = 'Y'
    INNER JOIN KETTLE_DUMP.AUDIT_FORM af ON af.ID = afv.AUDIT_FORM_ID AND af.ID = 2
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL u ON u.UNIT_ID = a.AUDIT_UNIT_ID
    WHERE
        a.AUDIT_TIME >= :startDate
            AND a.AUDIT_TIME <= :endDate
            AND a.AUDITOR_ID NOT IN (100002,100044,120085,120576,120817,120902,121078,121285,121670,100007,121149,121211,122030)
    GROUP BY u.UNIT_ID , a.ID
    ORDER BY u.UNIT_REGION , u.UNIT_NAME) AS T
GROUP BY T.UNIT_ID) as S;
			
			]]>
                    </content>
                    <params>
                        <param name="startDate" displayName="Start Date" dataType="DATE" format="yyyy-MM-dd" />
                        <param name="endDate" displayName="End Date" dataType="DATE" format="yyyy-MM-dd" />
                    </params>
                </report>
                <report id="5" name="Overall Product Excellence Score Product Wise Unit Wise" executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[
							  
        SELECT 
    *
FROM
    ((SELECT 
        av.PRODUCT_NAME,
        u.UNIT_ID,
        u.UNIT_NAME,
            ROUND(SUM(av.ACQUIRED_SCORE) / av.MAX_SCORE) AS CORRECT,
            ROUND((SUM(av.MAX_SCORE) - SUM(av.ACQUIRED_SCORE)) / av.MAX_SCORE) AS INCORRECT,
            CONCAT(ROUND(((SUM(av.MAX_SCORE) - SUM(av.ACQUIRED_SCORE)) / SUM(av.MAX_SCORE)) * 100), '%') AS INCORRECT_PERCENTAGE,
            ROUND(SUM(av.ACQUIRED_SCORE)) AS TOTAL_ACQUIRED_SCORE,
            ROUND(SUM(av.MAX_SCORE)) AS TOTAL_MAX_SCORE
    FROM
        KETTLE_DUMP.AUDIT_DETAIL ad
    INNER JOIN KETTLE_DUMP.AUDIT_DETAIL_VALUES av ON ad.AUDIT_TIME >= DATE(:startDate)  
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL u ON u.UNIT_ID=ad.AUDIT_UNIT_ID
    INNER JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd ON av.PRODUCT_ID=pd.PRODUCT_ID
        AND ad.AUDIT_TIME <= DATE(:endDate)
        AND ad.AUDIT_TYPE = 'REGISTERED'
        AND av.FORM_VALUE_ID > 63
        AND av.FORM_VALUE_ID < 69
        AND pd.PRODUCT_STATUS = 'ACTIVE'
        AND av.PRODUCT_ID IS NOT NULL
    GROUP BY av.PRODUCT_ID, u.UNIT_ID
    ORDER BY av.PRODUCT_NAME) UNION (SELECT 
        'TOTAL',
         u.UNIT_ID,
        u.UNIT_NAME,
            ROUND(SUM(av.ACQUIRED_SCORE) / av.MAX_SCORE) AS CORRECT,
            ROUND((SUM(av.MAX_SCORE) - SUM(av.ACQUIRED_SCORE)) / av.MAX_SCORE) AS INCORRECT,
            CONCAT(ROUND(((SUM(av.MAX_SCORE) - SUM(av.ACQUIRED_SCORE)) / SUM(av.MAX_SCORE)) * 100), '%') AS INCORRECT_PERCENTAGE,
            ROUND(SUM(av.ACQUIRED_SCORE)) AS TOTAL_ACQUIRED_SCORE,
            ROUND(SUM(av.MAX_SCORE)) AS TOTAL_MAX_SCORE
    FROM
        KETTLE_DUMP.AUDIT_DETAIL ad
    INNER JOIN KETTLE_DUMP.AUDIT_DETAIL_VALUES av ON ad.AUDIT_TIME >= DATE(:startDate)
	INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL u ON u.UNIT_ID=ad.AUDIT_UNIT_ID
	INNER JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd ON av.PRODUCT_ID=pd.PRODUCT_ID
        AND ad.AUDIT_TIME <= DATE(:endDate)
        AND ad.AUDIT_TYPE = 'REGISTERED'
        AND av.FORM_VALUE_ID > 63
        AND av.FORM_VALUE_ID < 69
		AND pd.PRODUCT_STATUS = 'ACTIVE'
        AND av.PRODUCT_ID IS NOT NULL)) N;
			
			]]>
                    </content>
                    <params>
                        <param name="startDate" displayName="Start Date" dataType="DATE" format="yyyy-MM-dd" />
                        <param name="endDate" displayName="End Date" dataType="DATE" format="yyyy-MM-dd" />
                    </params>
                </report>
				<report id="6" name="Overall Product Excellence Score Product Wise Audit Wise" executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[
							  
        SELECT 
    *
FROM
    ((SELECT 
        av.PRODUCT_NAME,
        ad.ID,
            ROUND(SUM(av.ACQUIRED_SCORE) / av.MAX_SCORE) AS CORRECT,
            ROUND((SUM(av.MAX_SCORE) - SUM(av.ACQUIRED_SCORE)) / av.MAX_SCORE) AS INCORRECT,
            CONCAT(ROUND(((SUM(av.MAX_SCORE) - SUM(av.ACQUIRED_SCORE)) / SUM(av.MAX_SCORE)) * 100), '%') AS INCORRECT_PERCENTAGE,
            ROUND(SUM(av.ACQUIRED_SCORE)) AS TOTAL_ACQUIRED_SCORE,
            ROUND(SUM(av.MAX_SCORE)) AS TOTAL_MAX_SCORE
    FROM
        KETTLE_DUMP.AUDIT_DETAIL ad
    INNER JOIN KETTLE_DUMP.AUDIT_DETAIL_VALUES av ON ad.ID = av.AUDIT_DETAIL_ID
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL u ON u.UNIT_ID=ad.AUDIT_UNIT_ID
    INNER JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd ON av.PRODUCT_ID=pd.PRODUCT_ID
		AND ad.AUDIT_TIME >= DATE(:startDate)   
        AND ad.AUDIT_TIME <= DATE(:endDate)
        AND ad.AUDIT_TYPE = 'REGISTERED'
        AND av.FORM_VALUE_ID > 63
        AND av.FORM_VALUE_ID < 69
        AND pd.PRODUCT_STATUS = 'ACTIVE'
        AND av.PRODUCT_ID IS NOT NULL
    GROUP BY av.PRODUCT_ID, ad.ID
    ORDER BY av.PRODUCT_NAME) UNION (SELECT 
        'TOTAL',
         ad.ID,
            ROUND(SUM(av.ACQUIRED_SCORE) / av.MAX_SCORE) AS CORRECT,
            ROUND((SUM(av.MAX_SCORE) - SUM(av.ACQUIRED_SCORE)) / av.MAX_SCORE) AS INCORRECT,
            CONCAT(ROUND(((SUM(av.MAX_SCORE) - SUM(av.ACQUIRED_SCORE)) / SUM(av.MAX_SCORE)) * 100), '%') AS INCORRECT_PERCENTAGE,
            ROUND(SUM(av.ACQUIRED_SCORE)) AS TOTAL_ACQUIRED_SCORE,
            ROUND(SUM(av.MAX_SCORE)) AS TOTAL_MAX_SCORE
    FROM
        KETTLE_DUMP.AUDIT_DETAIL ad
    INNER JOIN KETTLE_DUMP.AUDIT_DETAIL_VALUES av ON ad.AUDIT_TIME >= DATE(:startDate)
	INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL u ON u.UNIT_ID=ad.AUDIT_UNIT_ID
	INNER JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd ON av.PRODUCT_ID=pd.PRODUCT_ID
        AND ad.AUDIT_TIME <= DATE(:endDate)
        AND ad.AUDIT_TYPE = 'REGISTERED'
        AND av.FORM_VALUE_ID > 63
        AND av.FORM_VALUE_ID < 69
		AND pd.PRODUCT_STATUS = 'ACTIVE'
        AND av.PRODUCT_ID IS NOT NULL)) N;
			
			]]>
                    </content>
                    <params>
                        <param name="startDate" displayName="Start Date" dataType="DATE" format="yyyy-MM-dd" />
                        <param name="endDate" displayName="End Date" dataType="DATE" format="yyyy-MM-dd" />
                    </params>
                </report>
                <report id="7" name="Cafe Wise Critical Issues" executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[
							  
        SELECT 
    *
FROM
    (SELECT 
        ad.AUDIT_UNIT_NAME, GROUP_CONCAT(REPLACE(av.ANSWER_COMMENT, ',',' and '),'|') CRITICAL
    FROM
        KETTLE_DUMP.AUDIT_DETAIL ad
    INNER JOIN KETTLE_DUMP.AUDIT_DETAIL_VALUES av ON av.AUDIT_DETAIL_ID = ad.ID
        AND ad.AUDIT_TIME >= (:startDate)
        AND ad.AUDIT_TIME <= (:endDate)
        AND ad.AUDIT_TYPE = 'REGISTERED'
        AND av.FORM_VALUE_ID > 129
        AND av.FORM_VALUE_ID < 136
        AND av.ACQUIRED_SCORE < 0
		AND av.ANSWER_COMMENT IS NOT NULL
    GROUP BY ad.AUDIT_UNIT_ID) N;
			
			]]>
                    </content>
                    <params>
                        <param name="startDate" displayName="Start Date" dataType="DATE" format="yyyy-MM-dd" />
                        <param name="endDate" displayName="End Date" dataType="DATE" format="yyyy-MM-dd" />
                    </params>
                </report>
            </reports>
        </category>
		
		 <category name="Area Manager Weekly Reports" type="Area Manager Weekly Reports" accessCode="PQSC">
            <reports>
                <report id="1" name="Category wise percentage score" executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[
							  
SELECT 
    *
FROM
    (SELECT 
        T.LINKED_ENTITY_ID,
            afv.ENTITY_LABEL,
            IFNULL(SUM(T.ACQUIRED_SCORE), 0) AS ACQUIRED_SCORE,
            SUM(T.MAX_SCORE) AS MAX_SCORE,
            CONCAT(ROUND((IFNULL(SUM(T.ACQUIRED_SCORE), 0) / SUM(T.MAX_SCORE)) * 100), '%') AS PERCENTAGE,
			COUNT(DISTINCT T.ID) as AUDIT_COUNT
    FROM
        (SELECT 
        av.ACQUIRED_SCORE, av.MAX_SCORE, afv.LINKED_ENTITY_ID, a.ID
    FROM
        KETTLE_DUMP.AUDIT_DETAIL a
    INNER JOIN KETTLE_DUMP.AUDIT_DETAIL_VALUES av ON a.ID = av.AUDIT_DETAIL_ID
        AND a.AUDIT_TIME >= DATE(:startDate)
        AND a.AUDIT_TIME <= DATE(:endDate)
		AND a.AUDIT_TYPE = 'AREA_MANAGER'
    INNER JOIN KETTLE_DUMP.AUDIT_FORM_VALUES afv ON afv.ID = av.FORM_VALUE_ID AND afv.SCORE_COUNTED = 'Y'
    INNER JOIN KETTLE_DUMP.AUDIT_FORM af ON af.ID = afv.AUDIT_FORM_ID AND af.ID = 2) T
    INNER JOIN KETTLE_DUMP.AUDIT_FORM_VALUES afv ON afv.ID = T.LINKED_ENTITY_ID
    GROUP BY T.LINKED_ENTITY_ID UNION (SELECT 
        0,
            'Total',
            IFNULL(SUM(av.ACQUIRED_SCORE), 0) AS ACQUIRED_SCORE,
            SUM(av.MAX_SCORE) AS MAX_SCORE,
            CONCAT(ROUND((SUM(av.ACQUIRED_SCORE) / SUM(av.MAX_SCORE)) * 100), '%') AS PERCENTAGE,
			COUNT(DISTINCT a.ID) as AUDIT_COUNT
    FROM
        KETTLE_DUMP.AUDIT_DETAIL_VALUES av
    INNER JOIN KETTLE_DUMP.AUDIT_DETAIL a ON a.ID = av.AUDIT_DETAIL_ID
        AND a.AUDIT_TIME >= DATE(:startDate)
        AND a.AUDIT_TIME <= DATE(:endDate)
		AND a.AUDIT_TYPE = 'AREA_MANAGER'
        INNER JOIN KETTLE_DUMP.AUDIT_FORM_VALUES afv ON afv.ID = av.FORM_VALUE_ID AND afv.SCORE_COUNTED = 'Y'
        INNER JOIN KETTLE_DUMP.AUDIT_FORM af ON af.ID = afv.AUDIT_FORM_ID AND af.ID = 2)) M;         
			
			]]>
                    </content>
                    <params>
                        <param name="startDate" displayName="Start Date" dataType="DATE" format="yyyy-MM-dd" />
                        <param name="endDate" displayName="End Date" dataType="DATE" format="yyyy-MM-dd" />
                    </params>
                </report>
                <report id="2" name="Cafe and Category wise percentage score" executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[
							  
        SELECT * FROM
    (SELECT * FROM
    (SELECT 
    T.UNIT_ID, T.UNIT_NAME,
        T.LINKED_ENTITY_ID,
            afv.ENTITY_LABEL,
            IFNULL(SUM(T.ACQUIRED_SCORE), 0) AS ACQUIRED_SCORE,
            SUM(T.MAX_SCORE) AS MAX_SCORE,
            CONCAT(ROUND((IFNULL(SUM(T.ACQUIRED_SCORE), 0) / SUM(T.MAX_SCORE)) * 100), '%') AS PERCENTAGE
    FROM
        (SELECT u.UNIT_ID, u.UNIT_NAME, av.ACQUIRED_SCORE, av.MAX_SCORE, afv.LINKED_ENTITY_ID FROM
        KETTLE_DUMP.AUDIT_DETAIL a
    INNER JOIN KETTLE_DUMP.AUDIT_DETAIL_VALUES av ON a.ID = av.AUDIT_DETAIL_ID
        AND a.AUDIT_TIME >= DATE(:startDate)
        AND a.AUDIT_TIME <= DATE(:endDate)
		AND a.AUDIT_TYPE = 'AREA_MANAGER'
    INNER JOIN KETTLE_DUMP.AUDIT_FORM_VALUES afv ON afv.ID = av.FORM_VALUE_ID AND afv.SCORE_COUNTED = 'Y'
    INNER JOIN KETTLE_DUMP.AUDIT_FORM af ON af.ID = afv.AUDIT_FORM_ID AND af.ID = 2
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL u ON u.UNIT_ID=a.AUDIT_UNIT_ID
    ) T
    INNER JOIN KETTLE_DUMP.AUDIT_FORM_VALUES afv ON afv.ID = T.LINKED_ENTITY_ID
    GROUP BY T.LINKED_ENTITY_ID, T.UNIT_ID
    UNION
    (SELECT 
        u.UNIT_ID, u.UNIT_NAME, 0,
            'Total',
            IFNULL(SUM(av.ACQUIRED_SCORE), 0) AS ACQUIRED_SCORE,
            SUM(av.MAX_SCORE) AS MAX_SCORE,
            CONCAT(ROUND((SUM(av.ACQUIRED_SCORE) / SUM(av.MAX_SCORE)) * 100), '%') AS PERCENTAGE
    FROM
        KETTLE_DUMP.AUDIT_DETAIL_VALUES av
    INNER JOIN KETTLE_DUMP.AUDIT_DETAIL a ON a.ID = av.AUDIT_DETAIL_ID
        AND a.AUDIT_TIME >= DATE(:startDate)
        AND a.AUDIT_TIME <= DATE(:endDate)
		AND a.AUDIT_TYPE = 'AREA_MANAGER'
        INNER JOIN KETTLE_DUMP.AUDIT_FORM_VALUES afv ON afv.ID = av.FORM_VALUE_ID AND afv.SCORE_COUNTED = 'Y'
        INNER JOIN KETTLE_DUMP.AUDIT_FORM af ON af.ID = afv.AUDIT_FORM_ID AND af.ID = 2
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL u ON u.UNIT_ID=a.AUDIT_UNIT_ID GROUP BY u.UNIT_ID) 
     UNION
    (SELECT 
        u.UNIT_ID, u.UNIT_NAME, 0,
            'Projected',
            IFNULL(SUM(a.PROJECTED_ACQUIRED_SCORE), 0) AS PROJECTED_ACQUIRED_SCORE,
            SUM(a.PROJECTED_TOTAL_SCORE) AS PROJECTED_TOTAL_SCORE,
            CONCAT(ROUND((SUM(a.PROJECTED_ACQUIRED_SCORE) / SUM(a.PROJECTED_TOTAL_SCORE)) * 100), '%') AS PROJECTED_PERCENTAGE
    FROM
        KETTLE_DUMP.AUDIT_DETAIL_VALUES av
    INNER JOIN KETTLE_DUMP.AUDIT_DETAIL a ON a.ID = av.AUDIT_DETAIL_ID
        AND a.AUDIT_TIME >= DATE(:startDate)
        AND a.AUDIT_TIME <= DATE(:endDate)
		AND a.AUDIT_TYPE = 'AREA_MANAGER'
        INNER JOIN KETTLE_DUMP.AUDIT_FORM_VALUES afv ON afv.ID = av.FORM_VALUE_ID AND afv.SCORE_COUNTED = 'Y'
        INNER JOIN KETTLE_DUMP.AUDIT_FORM af ON af.ID = afv.AUDIT_FORM_ID AND af.ID = 2
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL u ON u.UNIT_ID=a.AUDIT_UNIT_ID GROUP BY u.UNIT_ID) ) M  ORDER BY M.UNIT_ID, M.LINKED_ENTITY_ID) N;
			
			]]>
                    </content>
                    <params>
                        <param name="startDate" displayName="Start Date" dataType="DATE" format="yyyy-MM-dd" />
                        <param name="endDate" displayName="End Date" dataType="DATE" format="yyyy-MM-dd" />
                    </params>
                </report>
                <report id="3" name="Category wise percentage score for single unit" executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[
							  
        SELECT * FROM
    (SELECT * FROM
    (SELECT 
    T.UNIT_ID, T.UNIT_NAME,
        T.LINKED_ENTITY_ID,
            afv.ENTITY_LABEL,
            IFNULL(SUM(T.ACQUIRED_SCORE), 0) AS ACQUIREC_SCORE,
            SUM(T.MAX_SCORE) AS MAX_CORE,
            CONCAT(ROUND((IFNULL(SUM(T.ACQUIRED_SCORE), 0) / SUM(T.MAX_SCORE)) * 100), '%') AS PERCENTAGE
    FROM
        (SELECT u.UNIT_ID, u.UNIT_NAME, av.ACQUIRED_SCORE, av.MAX_SCORE, afv.LINKED_ENTITY_ID FROM
        KETTLE_DUMP.AUDIT_DETAIL a
    INNER JOIN KETTLE_DUMP.AUDIT_DETAIL_VALUES av ON a.ID = av.AUDIT_DETAIL_ID
       AND a.AUDIT_TIME >= DATE(:startDate)
        AND a.AUDIT_TIME <= DATE(:endDate)
		AND a.AUDIT_UNIT_ID = :unitId
		AND a.AUDIT_TYPE = 'AREA_MANAGER'
    INNER JOIN KETTLE_DUMP.AUDIT_FORM_VALUES afv ON afv.ID = av.FORM_VALUE_ID AND afv.SCORE_COUNTED = 'Y'
    INNER JOIN KETTLE_DUMP.AUDIT_FORM af ON af.ID = afv.AUDIT_FORM_ID AND af.ID = 2
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL u ON u.UNIT_ID=a.AUDIT_UNIT_ID
    ) T
    INNER JOIN KETTLE_DUMP.AUDIT_FORM_VALUES afv ON afv.ID = T.LINKED_ENTITY_ID
    GROUP BY T.LINKED_ENTITY_ID, T.UNIT_ID
    UNION
    (SELECT 
        u.UNIT_ID, u.UNIT_NAME, 0,
            'Total',
            IFNULL(SUM(av.ACQUIRED_SCORE), 0) AS ACQUIRED_SCORE,
            SUM(av.MAX_SCORE) AS MAX_SCORE,
            CONCAT(ROUND((SUM(av.ACQUIRED_SCORE) / SUM(av.MAX_SCORE)) * 100), '%') AS PERCENTAGE
    FROM
        KETTLE_DUMP.AUDIT_DETAIL_VALUES av
    INNER JOIN KETTLE_DUMP.AUDIT_DETAIL a ON a.ID = av.AUDIT_DETAIL_ID
        AND a.AUDIT_TIME >= DATE(:startDate)
        AND a.AUDIT_TIME <= DATE(:endDate)
		AND a.AUDIT_UNIT_ID = :unitId
		AND a.AUDIT_TYPE = 'AREA_MANAGER'
        INNER JOIN KETTLE_DUMP.AUDIT_FORM_VALUES afv ON afv.ID = av.FORM_VALUE_ID AND afv.SCORE_COUNTED = 'Y'
        INNER JOIN KETTLE_DUMP.AUDIT_FORM af ON af.ID = afv.AUDIT_FORM_ID AND af.ID = 2
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL u ON u.UNIT_ID=a.AUDIT_UNIT_ID GROUP BY u.UNIT_ID) 
   UNION
    (SELECT 
        u.UNIT_ID, u.UNIT_NAME, 0,
            'Projected',
            IFNULL(SUM(a.PROJECTED_ACQUIRED_SCORE), 0) AS PROJECTED_ACQUIRED_SCORE,
            SUM(a.PROJECTED_TOTAL_SCORE) AS PROJECTED_TOTAL_SCORE,
            CONCAT(ROUND((SUM(a.PROJECTED_ACQUIRED_SCORE) / SUM(a.PROJECTED_TOTAL_SCORE)) * 100), '%') AS PROJECTED_PERCENTAGE
    FROM
        KETTLE_DUMP.AUDIT_DETAIL_VALUES av
    INNER JOIN KETTLE_DUMP.AUDIT_DETAIL a ON a.ID = av.AUDIT_DETAIL_ID
        AND a.AUDIT_TIME >= DATE(:startDate)
        AND a.AUDIT_TIME <= DATE(:endDate)
		AND a.AUDIT_UNIT_ID = :unitId
		AND a.AUDIT_TYPE = 'AREA_MANAGER'
        INNER JOIN KETTLE_DUMP.AUDIT_FORM_VALUES afv ON afv.ID = av.FORM_VALUE_ID AND afv.SCORE_COUNTED = 'Y'
        INNER JOIN KETTLE_DUMP.AUDIT_FORM af ON af.ID = afv.AUDIT_FORM_ID AND af.ID = 2
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL u ON u.UNIT_ID=a.AUDIT_UNIT_ID GROUP BY u.UNIT_ID) ) M  ORDER BY M.UNIT_ID, M.LINKED_ENTITY_ID) N;
			
			]]>
                    </content>
                    <params>
                        <param name="startDate" displayName="Start Date" dataType="DATE" format="yyyy-MM-dd" />
                        <param name="endDate" displayName="End Date" dataType="DATE" format="yyyy-MM-dd" />
                        <param name="unitId" displayName="Unit Id" dataType="INTEGER" />
                    </params>
                </report>
                <report id="4" name="Cafe wise overall score" executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[
							  
        SELECT * FROM (SELECT 
        u.UNIT_ID, u.UNIT_NAME, u.UNIT_REGION,
            IFNULL(SUM(av.ACQUIRED_SCORE), 0) AS ACQUIRED_SCORE,
            SUM(av.MAX_SCORE) AS MAX_SCORE,
            CONCAT(ROUND((SUM(av.ACQUIRED_SCORE) / SUM(av.MAX_SCORE)) * 100), '%') AS PERCENTAGE,
            COUNT(DISTINCT a.ID) as AUDIT_COUNT
    FROM
        KETTLE_DUMP.AUDIT_DETAIL_VALUES av
    INNER JOIN KETTLE_DUMP.AUDIT_DETAIL a ON a.ID = av.AUDIT_DETAIL_ID
        AND a.AUDIT_TIME >= DATE(:startDate)
        AND a.AUDIT_TIME <= DATE(:endDate)
		AND a.AUDIT_TYPE = 'AREA_MANAGER'
        INNER JOIN KETTLE_DUMP.AUDIT_FORM_VALUES afv ON afv.ID = av.FORM_VALUE_ID AND afv.SCORE_COUNTED = 'Y'
        INNER JOIN KETTLE_DUMP.AUDIT_FORM af ON af.ID = afv.AUDIT_FORM_ID AND af.ID = 2
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL u ON u.UNIT_ID=a.AUDIT_UNIT_ID GROUP BY u.UNIT_ID ORDER BY u.UNIT_REGION, u.UNIT_NAME) N;
			
			]]>
                    </content>
                    <params>
                        <param name="startDate" displayName="Start Date" dataType="DATE" format="yyyy-MM-dd" />
                        <param name="endDate" displayName="End Date" dataType="DATE" format="yyyy-MM-dd" />
                    </params>
                </report>
                <report id="5" name="Overall Product Excellence Score Product Wise Cafe Wise" executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[
							  
        SELECT 
    *
FROM
    ((SELECT 
        av.PRODUCT_NAME,
        u.UNIT_ID,
        u.UNIT_NAME,
            ROUND(SUM(av.ACQUIRED_SCORE) / av.MAX_SCORE) AS CORRECT,
            ROUND((SUM(av.MAX_SCORE) - SUM(av.ACQUIRED_SCORE)) / av.MAX_SCORE) AS INCORRECT,
            CONCAT(ROUND(((SUM(av.MAX_SCORE) - SUM(av.ACQUIRED_SCORE)) / SUM(av.MAX_SCORE)) * 100), '%') AS INCORRECT_PERCENTAGE,
            ROUND(SUM(av.ACQUIRED_SCORE)) AS TOTAL_ACQUIRED_SCORE,
            ROUND(SUM(av.MAX_SCORE)) AS TOTAL_MAX_SCORE
    FROM
        KETTLE_DUMP.AUDIT_DETAIL ad
    INNER JOIN KETTLE_DUMP.AUDIT_DETAIL_VALUES av ON ad.AUDIT_TIME >= DATE(:startDate)  
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL u ON u.UNIT_ID=ad.AUDIT_UNIT_ID
    INNER JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd ON av.PRODUCT_ID=pd.PRODUCT_ID
        AND ad.AUDIT_TIME <= DATE(:endDate)
        AND ad.AUDIT_TYPE = 'AREA_MANAGER'
        AND av.FORM_VALUE_ID > 63
        AND av.FORM_VALUE_ID < 69
        AND pd.PRODUCT_STATUS = 'ACTIVE'
        AND av.PRODUCT_ID IS NOT NULL
    GROUP BY av.PRODUCT_ID, u.UNIT_ID
    ORDER BY av.PRODUCT_NAME) UNION (SELECT 
        'TOTAL',
         u.UNIT_ID,
        u.UNIT_NAME,
            ROUND(SUM(av.ACQUIRED_SCORE) / av.MAX_SCORE) AS CORRECT,
            ROUND((SUM(av.MAX_SCORE) - SUM(av.ACQUIRED_SCORE)) / av.MAX_SCORE) AS INCORRECT,
            CONCAT(ROUND(((SUM(av.MAX_SCORE) - SUM(av.ACQUIRED_SCORE)) / SUM(av.MAX_SCORE)) * 100), '%') AS INCORRECT_PERCENTAGE,
            ROUND(SUM(av.ACQUIRED_SCORE)) AS TOTAL_ACQUIRED_SCORE,
            ROUND(SUM(av.MAX_SCORE)) AS TOTAL_MAX_SCORE
    FROM
        KETTLE_DUMP.AUDIT_DETAIL ad
    INNER JOIN KETTLE_DUMP.AUDIT_DETAIL_VALUES av ON ad.AUDIT_TIME >= DATE(:startDate)
	INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL u ON u.UNIT_ID=ad.AUDIT_UNIT_ID
	INNER JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd ON av.PRODUCT_ID=pd.PRODUCT_ID
        AND ad.AUDIT_TIME <= DATE(:endDate)
        AND ad.AUDIT_TYPE = 'AREA_MANAGER'
        AND av.FORM_VALUE_ID > 63
        AND av.FORM_VALUE_ID < 69
		AND pd.PRODUCT_STATUS = 'ACTIVE'
        AND av.PRODUCT_ID IS NOT NULL)) N;
			
			]]>
                    </content>
                    <params>
                        <param name="startDate" displayName="Start Date" dataType="DATE" format="yyyy-MM-dd" />
                        <param name="endDate" displayName="End Date" dataType="DATE" format="yyyy-MM-dd" />
                    </params>
                </report>
				<report id="6" name="Overall Product Excellence Score Product Wise Audit Wise" executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[
							  
        SELECT 
    *
FROM
    ((SELECT 
        av.PRODUCT_NAME,
        ad.ID,
            ROUND(SUM(av.ACQUIRED_SCORE) / av.MAX_SCORE) AS CORRECT,
            ROUND((SUM(av.MAX_SCORE) - SUM(av.ACQUIRED_SCORE)) / av.MAX_SCORE) AS INCORRECT,
            CONCAT(ROUND(((SUM(av.MAX_SCORE) - SUM(av.ACQUIRED_SCORE)) / SUM(av.MAX_SCORE)) * 100), '%') AS INCORRECT_PERCENTAGE,
            ROUND(SUM(av.ACQUIRED_SCORE)) AS TOTAL_ACQUIRED_SCORE,
            ROUND(SUM(av.MAX_SCORE)) AS TOTAL_MAX_SCORE
    FROM
        KETTLE_DUMP.AUDIT_DETAIL ad
    INNER JOIN KETTLE_DUMP.AUDIT_DETAIL_VALUES av ON ad.ID = av.AUDIT_DETAIL_ID
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL u ON u.UNIT_ID=ad.AUDIT_UNIT_ID
    INNER JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd ON av.PRODUCT_ID=pd.PRODUCT_ID
		AND ad.AUDIT_TIME >= DATE(:startDate)   
        AND ad.AUDIT_TIME <= DATE(:endDate)
        AND ad.AUDIT_TYPE = 'AREA_MANAGER'
        AND av.FORM_VALUE_ID > 63
        AND av.FORM_VALUE_ID < 69
        AND pd.PRODUCT_STATUS = 'ACTIVE'
        AND av.PRODUCT_ID IS NOT NULL
    GROUP BY av.PRODUCT_ID, ad.ID
    ORDER BY av.PRODUCT_NAME) UNION (SELECT 
        'TOTAL',
         ad.ID,
            ROUND(SUM(av.ACQUIRED_SCORE) / av.MAX_SCORE) AS CORRECT,
            ROUND((SUM(av.MAX_SCORE) - SUM(av.ACQUIRED_SCORE)) / av.MAX_SCORE) AS INCORRECT,
            CONCAT(ROUND(((SUM(av.MAX_SCORE) - SUM(av.ACQUIRED_SCORE)) / SUM(av.MAX_SCORE)) * 100), '%') AS INCORRECT_PERCENTAGE,
            ROUND(SUM(av.ACQUIRED_SCORE)) AS TOTAL_ACQUIRED_SCORE,
            ROUND(SUM(av.MAX_SCORE)) AS TOTAL_MAX_SCORE
    FROM
        KETTLE_DUMP.AUDIT_DETAIL ad
    INNER JOIN KETTLE_DUMP.AUDIT_DETAIL_VALUES av ON ad.AUDIT_TIME >= DATE(:startDate)
	INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL u ON u.UNIT_ID=ad.AUDIT_UNIT_ID
	INNER JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd ON av.PRODUCT_ID=pd.PRODUCT_ID
        AND ad.AUDIT_TIME <= DATE(:endDate)
        AND ad.AUDIT_TYPE = 'AREA_MANAGER'
        AND av.FORM_VALUE_ID > 63
        AND av.FORM_VALUE_ID < 69
		AND pd.PRODUCT_STATUS = 'ACTIVE'
        AND av.PRODUCT_ID IS NOT NULL)) N;
			
			]]>
                    </content>
                    <params>
                        <param name="startDate" displayName="Start Date" dataType="DATE" format="yyyy-MM-dd" />
                        <param name="endDate" displayName="End Date" dataType="DATE" format="yyyy-MM-dd" />
                    </params>
                </report>
                <report id="7" name="Cafe Wise Critical Issues" executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[
							  
        SELECT 
    *
FROM
    (SELECT 
        ad.AUDIT_UNIT_NAME, GROUP_CONCAT(REPLACE(av.ANSWER_COMMENT, ',',' and '),'|') CRITICAL
    FROM
        KETTLE_DUMP.AUDIT_DETAIL ad
    INNER JOIN KETTLE_DUMP.AUDIT_DETAIL_VALUES av ON av.AUDIT_DETAIL_ID = ad.ID
        AND ad.AUDIT_TIME >= (:startDate)
        AND ad.AUDIT_TIME <= (:endDate)
        AND ad.AUDIT_TYPE = 'AREA_MANAGER'
        AND av.FORM_VALUE_ID > 129
        AND av.FORM_VALUE_ID < 136
        AND av.ACQUIRED_SCORE < 0
		AND av.ANSWER_COMMENT IS NOT NULL
    GROUP BY ad.AUDIT_UNIT_ID) N;
			
			]]>
                    </content>
                    <params>
                        <param name="startDate" displayName="Start Date" dataType="DATE" format="yyyy-MM-dd" />
                        <param name="endDate" displayName="End Date" dataType="DATE" format="yyyy-MM-dd" />
                    </params>
                </report>
            </reports>
        </category>
		
        <category name="Other Reports" type="Other Reports" accessCode="PQSC">
            <reports>
                <report id="1" name="Audit done cafe wise report" executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[
							  
        SELECT 
    af.NAME,
    ad.AUDIT_UNIT_ID,
    ad.AUDIT_UNIT_NAME,
    ad.AUDIT_DATE,
    ad.AUDIT_TIME,
    ad.AUDIT_SUBMIT_DATE,
	ad.AUDIT_TYPE,
    e.EMP_ID,
    e.EMP_NAME,
    e.EMPLOYEE_CODE,
    d.DESIGNATION_NAME
FROM
    KETTLE_DUMP.AUDIT_FORM af
        INNER JOIN
    KETTLE_DUMP.AUDIT_DETAIL ad ON ad.AUDIT_FORM_ID = af.ID
        AND af.STATUS = 'ACTIVE'
        AND ad.AUDIT_TIME >= (:startDate)
        AND ad.AUDIT_TIME <= (:endDate)
        INNER JOIN
    KETTLE_MASTER_DUMP.EMPLOYEE_DETAIL e ON e.EMP_ID = ad.AUDITOR_ID
        INNER JOIN
    KETTLE_MASTER_DUMP.DESIGNATION d ON d.DESIGNATION_ID = e.DESIGNATION_ID;
			
			]]>
                    </content>
                    <params>
                        <param name="startDate" displayName="Start Date" dataType="DATE" format="yyyy-MM-dd" />
                        <param name="endDate" displayName="End Date" dataType="DATE" format="yyyy-MM-dd" />
                    </params>
                </report>
				
                <report id="2" name="Auditor wise audit count for date range" executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[
							  
        SELECT 
    COUNT(*) AS AUDIT_COUNT, AUDITOR_ID, AUDITOR_NAME
FROM
    KETTLE_DUMP.AUDIT_DETAIL
WHERE
    AUDIT_FORM_ID = 2
        AND DATE(AUDIT_DATE) >= (:startDate)
        AND DATE(AUDIT_DATE) <= (:endDate)
        AND AUDIT_TYPE = 'REGISTERED'
GROUP BY AUDITOR_ID;
			
			]]>
                    </content>
                    <params>
                        <param name="startDate" displayName="Start Date" dataType="DATE" format="yyyy-MM-dd" />
                        <param name="endDate" displayName="End Date" dataType="DATE" format="yyyy-MM-dd" />
                    </params>
                </report>
            </reports>
        </category>
        <category name="Area Manager Form Reports" type="Area Manager Form Reports" accessCode="PQSC">
            <reports>
                <report id="1" name="Area Manager Form Report across cafe for date range" executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[
							  
        SELECT * FROM (SELECT 
    ad.AUDITOR_ID,
    ad.AUDITOR_NAME,
    ad.AUDIT_UNIT_ID,
    ad.AUDIT_UNIT_NAME,
    ad.AUDIT_TIME,
    ad.CAFE_MANAGER_ID,
    ad.CAFE_MANAGER,
    ad.MANAGER_ON_DUTY_ID,
    ad.MANAGER_ON_DUTY,
    ad.AREA_MANAGER_ID,
    ad.AREA_MANAGER,
    afv.ENTITY_LABEL AS 'QUESTION',
    adv.TEXTAREA AS 'ANSWER',
	CONCAT(adv.EMPLOYEE_ID,
                '-',
                adv.EMPLOYEE_NAME,
                '-',
                adv.EMPLOYEE_DESIGNATION) AS EMPLOYEE
FROM
    KETTLE_DUMP.AUDIT_DETAIL ad
        INNER JOIN
    KETTLE_DUMP.AUDIT_DETAIL_VALUES adv ON adv.AUDIT_DETAIL_ID = ad.ID
        AND ad.AUDIT_FORM_ID = 3
        AND ad.AUDIT_TYPE = 'REGISTERED'
        AND DATE(ad.AUDIT_SUBMIT_DATE) >= :startDate
        AND DATE(ad.AUDIT_SUBMIT_DATE) <= :endDate
        INNER JOIN
    AUDIT_FORM_VALUES afv ON afv.ID = adv.FORM_VALUE_ID) T;
			
			]]>
                    </content>
                    <params>
                        <param name="startDate" displayName="Start Date" dataType="DATE" format="yyyy-MM-dd" />
                        <param name="endDate" displayName="End Date" dataType="DATE" format="yyyy-MM-dd" />
                    </params>
                </report>
            </reports>
        </category>
		
	<category name="Cafe Audit Dump" type="Cafe Audit Reports" accessCode="PQSC">
            <reports>
                <report id="1" name="Cafe Audit Reports" executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[
							  
        SELECT * FROM (SELECT 
    U.UNIT_NAME AS CAFE_NAME,
    U.UNIT_ID,
    A.ID AS AUDIT_ID,
    A.CAFE_MANAGER,
    A.MANAGER_ON_DUTY,
    E.EMP_NAME AS AREA_MANAGER,
    A.AUDIT_DATE,
    A.AUDIT_TIME,
    A.AUDITOR_NAME,
    A.AUDIT_TYPE,
    AFV.ENTITY_LABEL,
    AV.PRODUCT_NAME,
    AV.TEXTAREA ,
    AV.EMPLOYEE_NAME,
    AV.EMPLOYEE_DESIGNATION,
    AV.EMPLOYEE_ID,
    AV.ACQUIRED_SCORE
FROM
    KETTLE_DUMP.AUDIT_DETAIL A
        INNER JOIN
    KETTLE_DUMP.AUDIT_DETAIL_VALUES AV ON A.ID = AV.AUDIT_DETAIL_ID
        AND A.AUDIT_TIME >= DATE(:startDate)
        AND A.AUDIT_TIME <= DATE(:endDate)
        INNER JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL U ON U.UNIT_ID = A.AUDIT_UNIT_ID
        INNER JOIN
    KETTLE_MASTER_DUMP.EMPLOYEE_DETAIL E ON U.UNIT_MANAGER = E.EMP_ID
        INNER JOIN
    KETTLE_DUMP.AUDIT_FORM_VALUES AFV ON AV.FORM_VALUE_ID = AFV.ID ORDER BY A.ID) T;
			
			]]>
                    </content>
                    <params>
                        <param name="startDate" displayName="Start Date" dataType="DATE" format="yyyy-MM-dd" />
                        <param name="endDate" displayName="End Date" dataType="DATE" format="yyyy-MM-dd" />
                    </params>
                </report>
            </reports>
        </category>
		<category name="Cafe AOI Reports" type="Cafe AOI Reports" accessCode="PQSC">
            <reports>
                <report id="1" name="Cafe AOI Reports Audit Wise" executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[
							  
        SELECT * FROM (select X.UNIT_ID, X.UNIT_NAME, X.ENTITY_LABEL,X.ACQUIRED_SCORE, X.MAX_SCORE,X.AOI_Details, IFNULL(X.ANSWER_COMMENT,'') as Comment from (SELECT 
    T.UNIT_ID, T.UNIT_NAME,T.ID, afv.ENTITY_LABEL,T.ACQUIRED_SCORE, T.MAX_SCORE,T.ANSWER_COMMENT,
CASE 
	WHEN T.ACQUIRED_SCORE < T.MAX_SCORE THEN 'Opportunities'
    WHEN T.ACQUIRED_SCORE = T.MAX_SCORE THEN 'Strengths'
END    as AOI_Details
    FROM
        (SELECT u.UNIT_ID, u.UNIT_NAME,afv.LINKED_ENTITY_ID, av.ACQUIRED_SCORE, av.MAX_SCORE,a.ID,av.ANSWER_COMMENT FROM
        KETTLE_DUMP.AUDIT_DETAIL a
    INNER JOIN KETTLE_DUMP.AUDIT_DETAIL_VALUES av ON av.AUDIT_DETAIL_ID =a.ID
    INNER JOIN KETTLE_DUMP.AUDIT_FORM_VALUES afv ON av.FORM_VALUE_ID = afv.ID 
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL u ON u.UNIT_ID=a.AUDIT_UNIT_ID
        AND a.AUDIT_TIME >= DATE(:startDate)
        AND a.AUDIT_TIME <= DATE(:endDate)
		AND a.AUDIT_TYPE = 'REGISTERED'
		AND av.ACQUIRED_SCORE  IS NOT NULL 
		AND av.MAX_SCORE  IS NOT NULL
    ) T
    INNER JOIN KETTLE_DUMP.AUDIT_FORM_VALUES afv ON afv.ID = T.LINKED_ENTITY_ID
    GROUP BY  T.UNIT_ID, T.LINKED_ENTITY_ID) X) T;
			
			]]>
                    </content>
                    <params>
                        <param name="startDate" displayName="Start Date" dataType="DATE" format="yyyy-MM-dd" />
                        <param name="endDate" displayName="End Date" dataType="DATE" format="yyyy-MM-dd" />
                    </params>
                </report>
            </reports>
        </category>
		<category name="Auditor Weekly Reports Path Wise" type="Auditor Weekly Reports Patch Wise" accessCode="PQSC">
            <reports>
                <report id="1" name="Category wise percentage score Patch Wise" executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[
							  
SELECT 
    *
FROM
    (SELECT 
        T.LINKED_ENTITY_ID,
            afv.ENTITY_LABEL,
            IFNULL(SUM(T.ACQUIRED_SCORE), 0) AS ACQUIRED_SCORE,
            SUM(T.MAX_SCORE) AS MAX_SCORE,
            CONCAT(ROUND((IFNULL(SUM(T.ACQUIRED_SCORE), 0) / SUM(T.MAX_SCORE)) * 100), '%') AS PERCENTAGE,
			 COUNT(DISTINCT T.ID) as AUDIT_COUNT
    FROM
        (SELECT 
        av.ACQUIRED_SCORE, av.MAX_SCORE, afv.LINKED_ENTITY_ID, a.ID
    FROM
        KETTLE_DUMP.AUDIT_DETAIL a
    INNER JOIN KETTLE_DUMP.AUDIT_DETAIL_VALUES av ON a.ID = av.AUDIT_DETAIL_ID
       AND a.AUDIT_TIME >= DATE(:startDate)
        AND a.AUDIT_TIME <= DATE(:endDate)
		AND a.AUDIT_TYPE = 'REGISTERED'
    INNER JOIN KETTLE_DUMP.AUDIT_FORM_VALUES afv ON afv.ID = av.FORM_VALUE_ID AND afv.SCORE_COUNTED = 'Y'
    INNER JOIN KETTLE_DUMP.AUDIT_FORM af ON af.ID = afv.AUDIT_FORM_ID AND af.ID = 2
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = a.AUDIT_UNIT_ID AND ud.UNIT_ID in (
    SELECT UNIT_ID FROM KETTLE_MASTER_DUMP.UNIT_DETAIL WHERE UNIT_MANAGER = :amId)) T
    INNER JOIN KETTLE_DUMP.AUDIT_FORM_VALUES afv ON afv.ID = T.LINKED_ENTITY_ID
    GROUP BY T.LINKED_ENTITY_ID UNION (SELECT 
        0,
            'Total',
            IFNULL(SUM(av.ACQUIRED_SCORE), 0) AS ACQUIRED_SCORE,
            SUM(av.MAX_SCORE) AS MAX_SCORE,
            CONCAT(ROUND((SUM(av.ACQUIRED_SCORE) / SUM(av.MAX_SCORE)) * 100), '%') AS PERCENTAGE,
			COUNT(DISTINCT a.ID) as AUDIT_COUNT
    FROM
        KETTLE_DUMP.AUDIT_DETAIL_VALUES av
    INNER JOIN KETTLE_DUMP.AUDIT_DETAIL a ON a.ID = av.AUDIT_DETAIL_ID
       AND a.AUDIT_TIME >= DATE(:startDate)
        AND a.AUDIT_TIME <= DATE(:endDate)
		AND a.AUDIT_TYPE = 'REGISTERED'
        INNER JOIN KETTLE_DUMP.AUDIT_FORM_VALUES afv ON afv.ID = av.FORM_VALUE_ID AND afv.SCORE_COUNTED = 'Y'
        INNER JOIN KETTLE_DUMP.AUDIT_FORM af ON af.ID = afv.AUDIT_FORM_ID AND af.ID = 2)) M;         
			
			]]>
                    </content>
                    <params>
                        <param name="startDate" displayName="Start Date" dataType="DATE" format="yyyy-MM-dd" />
                        <param name="endDate" displayName="End Date" dataType="DATE" format="yyyy-MM-dd" />
						<param name="amId" displayName="Employee Id" dataType="INTEGER"/>
                    </params>
                </report>
                <report id="2" name="Cafe and Category wise percentage score Patch Wise" executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[
							  
        SELECT * FROM
    (SELECT * FROM
    (SELECT 
    T.UNIT_ID, T.UNIT_NAME,
        T.LINKED_ENTITY_ID,
            afv.ENTITY_LABEL,
            IFNULL(SUM(T.ACQUIRED_SCORE), 0) AS ACQUIRED_SCORE,
            SUM(T.MAX_SCORE) AS MAX_SCORE,
            CONCAT(ROUND((IFNULL(SUM(T.ACQUIRED_SCORE), 0) / SUM(T.MAX_SCORE)) * 100), '%') AS PERCENTAGE
    FROM
        (SELECT u.UNIT_ID, u.UNIT_NAME, av.ACQUIRED_SCORE, av.MAX_SCORE, afv.LINKED_ENTITY_ID FROM
        KETTLE_DUMP.AUDIT_DETAIL a
    INNER JOIN KETTLE_DUMP.AUDIT_DETAIL_VALUES av ON a.ID = av.AUDIT_DETAIL_ID
        AND a.AUDIT_TIME >= DATE(:startDate)
        AND a.AUDIT_TIME <= DATE(:endDate)
		AND a.AUDIT_TYPE = 'REGISTERED'
    INNER JOIN KETTLE_DUMP.AUDIT_FORM_VALUES afv ON afv.ID = av.FORM_VALUE_ID AND afv.SCORE_COUNTED = 'Y'
    INNER JOIN KETTLE_DUMP.AUDIT_FORM af ON af.ID = afv.AUDIT_FORM_ID AND af.ID = 2
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL u ON u.UNIT_ID=a.AUDIT_UNIT_ID
    AND u.UNIT_ID in (
    SELECT UNIT_ID FROM KETTLE_MASTER_DUMP.UNIT_DETAIL WHERE UNIT_MANAGER = :amId)) T
    INNER JOIN KETTLE_DUMP.AUDIT_FORM_VALUES afv ON afv.ID = T.LINKED_ENTITY_ID
    GROUP BY T.LINKED_ENTITY_ID, T.UNIT_ID
    UNION
    (SELECT 
        u.UNIT_ID, u.UNIT_NAME, 0,
            'Total',
            IFNULL(SUM(av.ACQUIRED_SCORE), 0) AS ACQUIRED_SCORE,
            SUM(av.MAX_SCORE) AS MAX_SCORE,
            CONCAT(ROUND((SUM(av.ACQUIRED_SCORE) / SUM(av.MAX_SCORE)) * 100), '%') AS PERCENTAGE
    FROM
        KETTLE_DUMP.AUDIT_DETAIL_VALUES av
    INNER JOIN KETTLE_DUMP.AUDIT_DETAIL a ON a.ID = av.AUDIT_DETAIL_ID
        AND a.AUDIT_TIME >= DATE(:startDate)
        AND a.AUDIT_TIME <= DATE(:endDate)
		AND a.AUDIT_TYPE = 'REGISTERED'
        INNER JOIN KETTLE_DUMP.AUDIT_FORM_VALUES afv ON afv.ID = av.FORM_VALUE_ID AND afv.SCORE_COUNTED = 'Y'
        INNER JOIN KETTLE_DUMP.AUDIT_FORM af ON af.ID = afv.AUDIT_FORM_ID AND af.ID = 2
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL u ON u.UNIT_ID=a.AUDIT_UNIT_ID GROUP BY u.UNIT_ID) 
     UNION
    (SELECT 
        u.UNIT_ID, u.UNIT_NAME, 0,
            'Projected',
            IFNULL(SUM(a.PROJECTED_ACQUIRED_SCORE), 0) AS PROJECTED_ACQUIRED_SCORE,
            SUM(a.PROJECTED_TOTAL_SCORE) AS PROJECTED_TOTAL_SCORE,
            CONCAT(ROUND((SUM(a.PROJECTED_ACQUIRED_SCORE) / SUM(a.PROJECTED_TOTAL_SCORE)) * 100), '%') AS PROJECTED_PERCENTAGE
    FROM
        KETTLE_DUMP.AUDIT_DETAIL_VALUES av
    INNER JOIN KETTLE_DUMP.AUDIT_DETAIL a ON a.ID = av.AUDIT_DETAIL_ID
        AND a.AUDIT_TIME >= DATE(:startDate)
        AND a.AUDIT_TIME <= DATE(:endDate)
		AND a.AUDIT_TYPE = 'REGISTERED'
        INNER JOIN KETTLE_DUMP.AUDIT_FORM_VALUES afv ON afv.ID = av.FORM_VALUE_ID AND afv.SCORE_COUNTED = 'Y'
        INNER JOIN KETTLE_DUMP.AUDIT_FORM af ON af.ID = afv.AUDIT_FORM_ID AND af.ID = 2
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL u ON u.UNIT_ID=a.AUDIT_UNIT_ID GROUP BY u.UNIT_ID) ) M  ORDER BY M.UNIT_ID, M.LINKED_ENTITY_ID) N;
			
			]]>
                    </content>
                    <params>
                        <param name="startDate" displayName="Start Date" dataType="DATE" format="yyyy-MM-dd" />
                        <param name="endDate" displayName="End Date" dataType="DATE" format="yyyy-MM-dd" />
						<param name="amId" displayName="Employee Id" dataType="INTEGER"/>
                    </params>
                </report>
                <report id="3" name="Cafe wise overall score Patch Wise" executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[
							  
        SELECT * FROM (SELECT 
        u.UNIT_ID, u.UNIT_NAME, u.UNIT_REGION,
            IFNULL(SUM(av.ACQUIRED_SCORE), 0) AS ACQUIRED_SCORE,
            SUM(av.MAX_SCORE) AS MAX_CORE,
            CONCAT(ROUND((SUM(av.ACQUIRED_SCORE) / SUM(av.MAX_SCORE)) * 100), '%') AS PERCENTAGE,
            COUNT(DISTINCT a.ID) as AUDIT_COUNT
    FROM
        KETTLE_DUMP.AUDIT_DETAIL_VALUES av
    INNER JOIN KETTLE_DUMP.AUDIT_DETAIL a ON a.ID = av.AUDIT_DETAIL_ID
        AND a.AUDIT_TIME >= DATE(:startDate)
        AND a.AUDIT_TIME <= DATE(:endDate)
		AND a.AUDIT_TYPE = 'REGISTERED'
        INNER JOIN KETTLE_DUMP.AUDIT_FORM_VALUES afv ON afv.ID = av.FORM_VALUE_ID AND afv.SCORE_COUNTED = 'Y'
        INNER JOIN KETTLE_DUMP.AUDIT_FORM af ON af.ID = afv.AUDIT_FORM_ID AND af.ID = 2
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL u ON u.UNIT_ID=a.AUDIT_UNIT_ID AND u.UNIT_ID in (
    SELECT UNIT_ID FROM KETTLE_MASTER_DUMP.UNIT_DETAIL WHERE UNIT_MANAGER = :amId) GROUP BY u.UNIT_ID ORDER BY u.UNIT_REGION, u.UNIT_NAME) N;
			
			]]>
                    </content>
                    <params>
                        <param name="startDate" displayName="Start Date" dataType="DATE" format="yyyy-MM-dd" />
                        <param name="endDate" displayName="End Date" dataType="DATE" format="yyyy-MM-dd" />
						<param name="amId" displayName="Employee Id" dataType="INTEGER"/>
                    </params>
                </report>
                <report id="4" name="Overall Product Excellence Score Product Wise Unit Wise Patch Wise" executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[
							  
        SELECT 
    *
FROM
    ((SELECT 
        av.PRODUCT_NAME,
        u.UNIT_ID,
        u.UNIT_NAME,
            ROUND(SUM(av.ACQUIRED_SCORE) / av.MAX_SCORE) AS CORRECT,
            ROUND((SUM(av.MAX_SCORE) - SUM(av.ACQUIRED_SCORE)) / av.MAX_SCORE) AS INCORRECT,
            CONCAT(ROUND(((SUM(av.MAX_SCORE) - SUM(av.ACQUIRED_SCORE)) / SUM(av.MAX_SCORE)) * 100), '%') AS INCORRECT_PERCENTAGE,
            ROUND(SUM(av.ACQUIRED_SCORE)) AS TOTAL_ACQUIRED_SCORE,
            ROUND(SUM(av.MAX_SCORE)) AS TOTAL_MAX_SCORE
    FROM
        KETTLE_DUMP.AUDIT_DETAIL ad
    INNER JOIN KETTLE_DUMP.AUDIT_DETAIL_VALUES av ON ad.AUDIT_TIME >= DATE(:startDate)  
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL u ON u.UNIT_ID=ad.AUDIT_UNIT_ID AND u.UNIT_ID in (
    SELECT UNIT_ID FROM KETTLE_MASTER_DUMP.UNIT_DETAIL WHERE UNIT_MANAGER = :amId)
    INNER JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd ON av.PRODUCT_ID=pd.PRODUCT_ID
        AND ad.AUDIT_TIME <= DATE(:endDate)
        AND ad.AUDIT_TYPE = 'REGISTERED'
        AND av.FORM_VALUE_ID > 63
        AND av.FORM_VALUE_ID < 69
        AND pd.PRODUCT_STATUS = 'ACTIVE'
        AND av.PRODUCT_ID IS NOT NULL
    GROUP BY av.PRODUCT_ID, u.UNIT_ID
    ORDER BY av.PRODUCT_NAME) UNION (SELECT 
        'TOTAL',
         u.UNIT_ID,
        u.UNIT_NAME,
            ROUND(SUM(av.ACQUIRED_SCORE) / av.MAX_SCORE) AS CORRECT,
            ROUND((SUM(av.MAX_SCORE) - SUM(av.ACQUIRED_SCORE)) / av.MAX_SCORE) AS INCORRECT,
            CONCAT(ROUND(((SUM(av.MAX_SCORE) - SUM(av.ACQUIRED_SCORE)) / SUM(av.MAX_SCORE)) * 100), '%') AS INCORRECT_PERCENTAGE,
            ROUND(SUM(av.ACQUIRED_SCORE)) AS TOTAL_ACQUIRED_SCORE,
            ROUND(SUM(av.MAX_SCORE)) AS TOTAL_MAX_SCORE
    FROM
        KETTLE_DUMP.AUDIT_DETAIL ad
    INNER JOIN KETTLE_DUMP.AUDIT_DETAIL_VALUES av ON ad.AUDIT_TIME >= DATE(:startDate)
	INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL u ON u.UNIT_ID=ad.AUDIT_UNIT_ID
	INNER JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd ON av.PRODUCT_ID=pd.PRODUCT_ID
        AND ad.AUDIT_TIME <= DATE(:endDate)
        AND ad.AUDIT_TYPE = 'REGISTERED'
        AND av.FORM_VALUE_ID > 63
        AND av.FORM_VALUE_ID < 69
		AND pd.PRODUCT_STATUS = 'ACTIVE'
        AND av.PRODUCT_ID IS NOT NULL)) N;
			
			]]>
                    </content>
                    <params>
                        <param name="startDate" displayName="Start Date" dataType="DATE" format="yyyy-MM-dd" />
                        <param name="endDate" displayName="End Date" dataType="DATE" format="yyyy-MM-dd" />
						<param name="amId" displayName="Employee Id" dataType="INTEGER"/>
                    </params>
                </report>
				
                <report id="5" name="Cafe Wise Critical Issues Patch Wise" executionType="SQL" returnType="java.lang.String">
                    <content>
                        <![CDATA[
							  
        SELECT 
    *
FROM
    (SELECT 
        ad.AUDIT_UNIT_NAME, GROUP_CONCAT(REPLACE(av.ANSWER_COMMENT, ',',' and '),'|') CRITICAL
    FROM
        KETTLE_DUMP.AUDIT_DETAIL ad
    INNER JOIN KETTLE_DUMP.AUDIT_DETAIL_VALUES av ON av.AUDIT_DETAIL_ID = ad.ID
	INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL u ON u.UNIT_ID=ad.AUDIT_UNIT_ID AND u.UNIT_ID in (
    SELECT UNIT_ID FROM KETTLE_MASTER_DUMP.UNIT_DETAIL WHERE UNIT_MANAGER = :amId) 
        AND ad.AUDIT_TIME >= (:startDate)
        AND ad.AUDIT_TIME <= (:endDate)
        AND ad.AUDIT_TYPE = 'REGISTERED'
        AND av.FORM_VALUE_ID > 129
        AND av.FORM_VALUE_ID < 136
        AND av.ACQUIRED_SCORE < 0
		AND av.ANSWER_COMMENT IS NOT NULL
    GROUP BY ad.AUDIT_UNIT_ID) N;
			
			]]>
                    </content>
                    <params>
                        <param name="startDate" displayName="Start Date" dataType="DATE" format="yyyy-MM-dd" />
                        <param name="endDate" displayName="End Date" dataType="DATE" format="yyyy-MM-dd" />
						<param name="amId" displayName="Employee Id" dataType="INTEGER"/>
                    </params>
                </report>
            </reports>
        </category>
    </categories>
</ReportCategories>
