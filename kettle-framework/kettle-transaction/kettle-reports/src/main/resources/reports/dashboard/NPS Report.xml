<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<ReportCategories xmlns="http://www.w3schools.com">
	<categories>
		<category name="NPS Report" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>"
                        schedule="">
			<reports>
				<report id="1" name="NPS For Date Range" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[

select :startDate START_DATE,
	:endDate END_DATE,
    UNIT_ID,
    UNIT_NAME,
    NPS_SCORE,
    COALESCE(CAFE_NPS_SCORE, 0) CAFE_NPS_SCORE,
    COALESCE(COD_NPS_SCORE, 0) COD_NPS_SCORE,
    COALESCE(POSITIVE_PERCENTAGE, 0) POSITIVE_PERCENTAGE,
    COALESCE(NEGATIVE_PERCENTAGE, 0) NEGATIVE_PERCENTAGE,
    COALESCE(TOTAL_POSITIVE, 0) TOTAL_POSITIVE,
    COALESCE(TOTAL_NEGATIVE, 0) TOTAL_NEGATIVE,
    COALESCE(TOTAL_TICKET, 0) TOTAL_TICKET,
    COALESCE(TOTAL_CAFE_POSITIVE, 0) TOTAL_CAFE_POSITIVE,
    COALESCE(TOTAL_CAFE_NEGATIVE, 0) TOTAL_CAFE_NEGATIVE,
    COALESCE(TOTAL_CAFE_NEUTRAL, 0) TOTAL_CAFE_NEUTRAL,
    COALESCE(TOTAL_CAFE_TICKETS, 0) TOTAL_CAFE_TICKETS,
    COALESCE(TOTAL_COD_POSITIVE, 0) TOTAL_COD_POSITIVE,
    COALESCE(TOTAL_COD_NEGATIVE, 0) TOTAL_COD_NEGATIVE,
    COALESCE(TOTAL_COD_NEUTRAL, 0) TOTAL_COD_NEUTRAL,
    COALESCE(TOTAL_COD_TICKETS, 0) TOTAL_COD_TICKETS from (
SELECT 
    '2018-03-17' BUSINESS_DATE,
    a.UNIT_ID,
    ud.UNIT_NAME,
    ud.UNIT_CATEGORY,
    SUM(a.TOTAL_TICKET) TOTAL_TICKET,
    SUM(a.TOTAL_NEGATIVE) TOTAL_NEGATIVE,
    SUM(a.TOTAL_NEUTRAL) TOTAL_NEUTRAL,
    SUM(a.TOTAL_POSITIVE) TOTAL_POSITIVE,
    TRUNCATE(SUM(a.TOTAL_POSITIVE) / SUM(a.TOTAL_TICKET) * 100,
        2) POSITIVE_PERCENTAGE,
    TRUNCATE(SUM(a.TOTAL_NEGATIVE) / SUM(a.TOTAL_TICKET) * 100,
        2) NEGATIVE_PERCENTAGE,
    TRUNCATE(SUM(a.TOTAL_NEUTRAL) / SUM(a.TOTAL_TICKET) * 100,
        2) NEUTRAL_PERCENTAGE,
    SUM(CASE
        WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_TICKET
    END) AS TOTAL_CAFE_TICKETS,
    SUM(CASE
        WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_NEGATIVE
    END) AS TOTAL_CAFE_NEGATIVE,
    SUM(CASE
        WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_NEUTRAL
    END) AS TOTAL_CAFE_NEUTRAL,
    SUM(CASE
        WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_POSITIVE
    END) AS TOTAL_CAFE_POSITIVE,
    TRUNCATE(SUM(CASE
            WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_POSITIVE
        END) / SUM(CASE
            WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_TICKET
        END) * 100,
        2) CAFE_POSITIVE_PERCENTAGE,
    TRUNCATE(SUM(CASE
            WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_NEGATIVE
        END) / SUM(CASE
            WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_TICKET
        END) * 100,
        2) CAFE_NEGATIVE_PERCENTAGE,
    TRUNCATE(SUM(CASE
            WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_NEUTRAL
        END) / SUM(CASE
            WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_TICKET
        END) * 100,
        2) CAFE_NEUTRAL_PERCENTAGE,
    SUM(CASE
        WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_TICKET
    END) AS TOTAL_COD_TICKETS,
    SUM(CASE
        WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_NEGATIVE
    END) AS TOTAL_COD_NEGATIVE,
    SUM(CASE
        WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_NEUTRAL
    END) AS TOTAL_COD_NEUTRAL,
    SUM(CASE
        WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_POSITIVE
    END) AS TOTAL_COD_POSITIVE,
    TRUNCATE(SUM(CASE
            WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_POSITIVE
        END) / SUM(CASE
            WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_TICKET
        END) * 100,
        2) COD_POSITIVE_PERCENTAGE,
    TRUNCATE(SUM(CASE
            WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_NEGATIVE
        END) / SUM(CASE
            WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_TICKET
        END) * 100,
        2) COD_NEGATIVE_PERCENTAGE,
    TRUNCATE(SUM(CASE
            WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_NEUTRAL
        END) / SUM(CASE
            WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_TICKET
        END) * 100,
        2) COD_NEUTRAL_PERCENTAGE,
    ROUND(TRUNCATE(SUM(a.TOTAL_POSITIVE) / SUM(a.TOTAL_TICKET) * 100,
                2)) - ROUND(TRUNCATE(SUM(a.TOTAL_NEGATIVE) / SUM(a.TOTAL_TICKET) * 100,
                2)) NPS_SCORE,
    ROUND(TRUNCATE(SUM(CASE
                    WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_POSITIVE
                END) / SUM(CASE
                    WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_TICKET
                END) * 100,
                2)) - ROUND(TRUNCATE(SUM(CASE
                    WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_NEGATIVE
                END) / SUM(CASE
                    WHEN ORDER_SOURCE <> 'COD' THEN a.TOTAL_TICKET
                END) * 100,
                2)) CAFE_NPS_SCORE,
    ROUND(TRUNCATE(SUM(CASE
                    WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_POSITIVE
                END) / SUM(CASE
                    WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_TICKET
                END) * 100,
                2)) - ROUND(TRUNCATE(SUM(CASE
                    WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_NEGATIVE
                END) / SUM(CASE
                    WHEN ORDER_SOURCE = 'COD' THEN a.TOTAL_TICKET
                END) * 100,
                2)) COD_NPS_SCORE
FROM
    (SELECT 
        nd.UNIT_ID,
            ORDER_SOURCE,
            COUNT(*) TOTAL_TICKET,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 0 AND nd.NPS_SCORE <= 6 THEN 1
                ELSE 0
            END) TOTAL_NEGATIVE,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 7 AND nd.NPS_SCORE <= 8 THEN 1
                ELSE 0
            END) TOTAL_NEUTRAL,
            SUM(CASE
                WHEN nd.NPS_SCORE >= 9 AND nd.NPS_SCORE <= 10 THEN 1
                ELSE 0
            END) TOTAL_POSITIVE
    FROM
        KETTLE_DUMP.ORDER_NPS_DETAIL nd
    INNER JOIN KETTLE_DUMP.ORDER_DETAIL ud ON ud.ORDER_ID = nd.ORDER_ID
    WHERE
        DATE(nd.SURVEY_CREATION_TIME) >= :startDate
            AND DATE(nd.SURVEY_CREATION_TIME) <= :endDate
	GROUP BY ud.ORDER_SOURCE, nd.UNIT_ID) a,
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud
WHERE
    a.UNIT_ID = ud.UNIT_ID
GROUP BY 1 , 2 , 3 , 4
) a

				     ]]>
					</content>
					<params>
                        <param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
                        <param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
                    </params>
				</report>

			</reports>
		</category>
	</categories>
</ReportCategories>

