<ReportCategories xmlns="http://www.w3schools.com">
<categories>
<category name="Intra Day Dispatch and Delivery Delay Details"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>,<EMAIL>"
                        schedule="">
                        <reports>
                                 <report id="1" name="Intra Day Dispatch and Delivery Delay Details" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
                                        <content><![CDATA[
                                    SELECT T.* FROM (SELECT
    od.GENERATED_ORDER_ID,
    cai.ADDRESS_LINE_1,
    cai.ADDRESS_LINE_2,
    cai.LOCALITY,
    od.ORDER_ID,
    ud.UNIT_NAME,
    od.SETTLED_AMOUNT ORDER_VALUE,
    ci.FIRST_NAME CUSTOMER_NAME,
    ci.CONTACT_NUMBER,
    oen.ENTRY_TYPE DELAY_TYPE, 
    date_format(od.BILLING_SERVER_TIME,'%d %b %Y %T') CREATED_AT,
    time_to_sec(timediff(ose.UPDATE_TIME,od.BILLING_SERVER_TIME))/60 DELIVERY_ELAPSED_TIME 
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        INNER JOIN
    KETTLE_DUMP.CUSTOMER_ADDRESS_INFO cai on cai.ADDRESS_ID = od.DELIVERY_ADDRESS
        INNER JOIN
    KETTLE_DUMP.ORDER_EMAIL_NOTIFICATION oen ON oen.ORDER_ID = od.ORDER_ID
        INNER JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
        INNER JOIN
    KETTLE_DUMP.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
        LEFT JOIN
    KETTLE_DUMP.ORDER_STATUS_EVENT ose ON od.ORDER_ID = ose.ORDER_ID
        AND ose.TRANSITION_STATUS = 'SUCCESS'
        AND (ose.FROM_STATUS = 'SETTLED'
        AND ose.TO_STATUS = 'DELIVERED')
WHERE
    oen.ENTRY_TYPE IN ('DISPATCH_DELAY' , 'DELIVERY_DELAY')
        AND oen.IS_EMAIL_DELIVERED = 'Y'
        AND oen.EXECUTION_TIME > :fromBusinessDate
        AND oen.EXECUTION_TIME < ADDDATE(:toBusinessDate, 1)
	AND od.DELIVERY_PARTNER_ID = 8
GROUP BY od.ORDER_ID) as T;

                                     ]]></content>
					 <params>
                                                <param name="fromBusinessDate" displayName="Business Date"
                                                        dataType="DATE" format="yyyy-MM-dd" />
                                                <param name="toBusinessDate" displayName="Business Date"
                                                        dataType="DATE" format="yyyy-MM-dd" />
                                        </params>
                                </report>

                        </reports>
</category>
</categories>
</ReportCategories>

