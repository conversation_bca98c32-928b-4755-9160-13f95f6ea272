<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<ReportCategories xmlns="http://www.w3schools.com">
	<categories>
		<category name="Customers Reports" type="kettle"
			accessCode="kettle">
			<reports>
				<report name="Customer Email with Chaayos Domain"
					executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
    *
FROM
    KETTLE_DUMP.CUSTOMER_INFO
WHERE
    EMAIL_ID LIKE '%chaayos.com'

]]>
					</content>
				</report>
			</reports>
		</category>		
		<category name="Notification Reports" type="kettle"
			accessCode="kettle">
			<reports>
				<report name="Notification Count"
					executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
    DATE(NOTIFICATION_TIME) NOTIFICATION_DATE, COUNT(*) NOTIFICATION_COUNT
FROM
    KETTLE_MASTER_DUMP.NOTIFICATION_LOG_DETAIL
WHERE
    NOTIFICATION_TIME BETWEEN DATE(:fromBusinessDate) AND DATE(:toBusinessDate)
GROUP BY NOTIFICATION_DATE;

]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date" dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date" dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
			</reports>
		</category>		
	</categories>
</ReportCategories>

