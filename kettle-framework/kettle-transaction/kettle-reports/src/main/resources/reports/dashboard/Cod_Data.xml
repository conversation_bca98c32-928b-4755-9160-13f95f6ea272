<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<ReportCategories xmlns="http://www.w3schools.com">
	<categories>
<category name="Intra Day Dispatch and Delivery Delay Details" accessCode="Automated" id="1">
                        <reports>
                                 <report id="1" name="Intra Day Dispatch and Delivery Delay Details" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
                                        <content><![CDATA[
SELECT * FROM (SELECT
    od.GENERATED_ORDER_ID GENERATED_ORDER_ID,
    cai.ADDRESS_LINE_1 ADDRESS_LINE_1,
    cai.LOCALITY LOCALITY,
    od.ORDER_ID ORDER_ID,
    od.SETTLED_AMOUNT ORDER_VALUE,
    ud.UNIT_NAME UNIT_NAME,
    ci.FIRST_NAME CUSTOMER_NAME,
    ci.CONTACT_NUMBER CONTACT_NUMBER,
    oen.ENTRY_TYPE DELAY_TYPE,
    DATE_FORMAT(od.BILLING_SERVER_TIME, '%d %b %Y %T') CREATED_AT,
    CASE
        WHEN ose.UPDATE_TIME IS NULL THEN - 1
        ELSE TIME_TO_SEC(TIMEDIFF(ose.UPDATE_TIME, od.BILLING_SERVER_TIME)) / 60
    END DELIVERY_ELAPSED_TIME
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        INNER JOIN
    KETTLE_DUMP.CUSTOMER_ADDRESS_INFO cai ON cai.ADDRESS_ID = od.DELIVERY_ADDRESS
        INNER JOIN
    KETTLE_DUMP.ORDER_EMAIL_NOTIFICATION oen ON oen.ORDER_ID = od.ORDER_ID
        INNER JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
        INNER JOIN
    KETTLE_DUMP.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
        LEFT JOIN
    KETTLE_DUMP.ORDER_STATUS_EVENT ose ON od.ORDER_ID = ose.ORDER_ID
        AND ose.TRANSITION_STATUS = 'SUCCESS'
        AND (ose.FROM_STATUS = 'SETTLED'
        AND ose.TO_STATUS = 'DELIVERED')
WHERE
    oen.ENTRY_TYPE IN ('DISPATCH_DELAY' , 'DELIVERY_DELAY')
        AND oen.IS_EMAIL_DELIVERED = 'Y'
        AND oen.EXECUTION_TIME BETWEEN DATE(:startDate) AND DATE(:endDate)
        AND od.DELIVERY_PARTNER_ID = 8
GROUP BY od.ORDER_ID ORDER BY od.ORDER_ID DESC) T

                                     ]]></content>
<params>
						<param name="startDate" displayName="Start Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="endDate" displayName="End Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
                                </report>

                        </reports>
</category>
		<category name="Cod Reports" type="Cafe Operations"
                  accessCode="Marketing">
			<reports>
				<report name="DeliveryPartner_TAT "
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
select od.ORDER_ID,
		ud.UNIT_NAME,
        ose.FROM_STATUS,
        ose.TO_STATUS,
        od.BILLING_SERVER_TIME,
        truncate((ose.UPDATE_TIME - ose.START_TIME)/60,2) AS TIME_DIFFERENCE_FROM_CREATION	
    from KETTLE_DUMP.ORDER_DETAIL od
    inner join KETTLE_MASTER_DUMP.UNIT_DETAIL ud on od.UNIT_ID = ud.UNIT_ID
	inner join KETTLE_DUMP.ORDER_STATUS_EVENT ose on od.ORDER_ID = ose.ORDER_ID and ose.FROM_STATUS in ('CREATED','READY_TO_DISPATCH') 
					and ose.TO_STATUS in ('PROCESSING','SETTLED') and od.ORDER_SOURCE='COD' and od.ORDER_STATUS = 'SETTLED'
                    WHERE      od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:fromBusinessDate,1))
        AND od.ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)
			
			]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>

				</report>
				<report name="COD Bill Level Complete Data"
					executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT
    ud.UNIT_NAME,
    od.BUSINESS_DATE,
    od.*,
    dr.RL_NAME,
    cp.PARTNER_DISPLAY_NAME,
    dp.PARTNER_DISPLAY_NAME,
    ci.*,
    ai.*
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        INNER JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
        INNER JOIN
    KETTLE_DUMP.CHANNEL_PARTNER cp ON od.CHANNEL_PARTNER_ID = cp.PARTNER_ID
        INNER JOIN
    KETTLE_DUMP.DELIVERY_PARTNER dp ON od.DELIVERY_PARTNER_ID = dp.PARTNER_ID
        LEFT OUTER JOIN
    KETTLE_DUMP.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
        LEFT OUTER JOIN
    KETTLE_DUMP.CUSTOMER_ADDRESS_INFO ai ON od.DELIVERY_ADDRESS = ai.ADDRESS_ID
        LEFT OUTER JOIN
    KETTLE_MASTER_DUMP.REF_LOOKUP dr ON dr.RL_ID = od.DISCOUNT_REASON_ID
WHERE
    od.ORDER_SOURCE = 'COD'
    and od.ORDER_STATUS <> "CANCELLED"
 AND od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate
ORDER BY ud.UNIT_NAME, BUSINESS_DATE
]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>

				</report>
<report name="COD Bill Level Night Hours Complete Data"
					executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT
    ud.UNIT_NAME,
    od.BUSINESS_DATE,
    od.*,
    dr.RL_NAME,
    cp.PARTNER_DISPLAY_NAME,
    dp.PARTNER_DISPLAY_NAME,
    ci.*,
    ai.*
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        INNER JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
        INNER JOIN
    KETTLE_DUMP.CHANNEL_PARTNER cp ON od.CHANNEL_PARTNER_ID = cp.PARTNER_ID
        INNER JOIN
    KETTLE_DUMP.DELIVERY_PARTNER dp ON od.DELIVERY_PARTNER_ID = dp.PARTNER_ID
        LEFT OUTER JOIN
    KETTLE_DUMP.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
        LEFT OUTER JOIN
    KETTLE_DUMP.CUSTOMER_ADDRESS_INFO ai ON od.DELIVERY_ADDRESS = ai.ADDRESS_ID
        LEFT OUTER JOIN
    KETTLE_MASTER_DUMP.REF_LOOKUP dr ON dr.RL_ID = od.DISCOUNT_REASON_ID
WHERE
    od.ORDER_SOURCE = 'COD' 
    AND (TIME(od.BILLING_SERVER_TIME) > '23:30:00' 
    OR TIME(od.BILLING_SERVER_TIME) < '07:30:00' )
 AND od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate
ORDER BY ud.UNIT_NAME, BUSINESS_DATE
]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>

				</report>
<report name="COD Bill Level Complete Data For Unit"
					executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT
    ud.UNIT_NAME,
    od.BUSINESS_DATE,
    od.ORDER_ID,
    od.ORDER_SOURCE,
    od.BILLING_SERVER_TIME,
    dr.RL_NAME,
    cp.PARTNER_DISPLAY_NAME,
    dp.PARTNER_DISPLAY_NAME,
    ci.CONTACT_NUMBER,
    ci.FIRST_NAME,
    ai.*
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        INNER JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
        INNER JOIN
    KETTLE_DUMP.CHANNEL_PARTNER cp ON od.CHANNEL_PARTNER_ID = cp.PARTNER_ID
        INNER JOIN
    KETTLE_DUMP.DELIVERY_PARTNER dp ON od.DELIVERY_PARTNER_ID = dp.PARTNER_ID
        LEFT OUTER JOIN
    KETTLE_DUMP.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
        LEFT OUTER JOIN
    KETTLE_DUMP.CUSTOMER_ADDRESS_INFO ai ON od.DELIVERY_ADDRESS = ai.ADDRESS_ID
        LEFT OUTER JOIN
    KETTLE_MASTER_DUMP.REF_LOOKUP dr ON dr.RL_ID = od.DISCOUNT_REASON_ID
WHERE
    od.ORDER_SOURCE = 'COD'
 AND od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate
 AND od.UNIT_ID = :unitId
ORDER BY ud.UNIT_NAME, BUSINESS_DATE
]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="unitId" displayName="Unit Id"
                                          dataType="STRING" format="yyyy-MM-dd" />
					</params>
</report>
<report name="COD Bill Level Complete Data VIA Channel Partner"
					executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT
    ud.UNIT_NAME,
    od.BUSINESS_DATE,
    od.*,
    dr.RL_NAME,
    cp.PARTNER_DISPLAY_NAME,
    dp.PARTNER_DISPLAY_NAME,
    ci.*,
    ai.*
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        INNER JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
        INNER JOIN
    KETTLE_DUMP.CHANNEL_PARTNER cp ON od.CHANNEL_PARTNER_ID = cp.PARTNER_ID
        INNER JOIN
    KETTLE_DUMP.DELIVERY_PARTNER dp ON od.DELIVERY_PARTNER_ID = dp.PARTNER_ID
        LEFT OUTER JOIN
    KETTLE_DUMP.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
        LEFT OUTER JOIN
    KETTLE_DUMP.CUSTOMER_ADDRESS_INFO ai ON od.DELIVERY_ADDRESS = ai.ADDRESS_ID
        LEFT OUTER JOIN
    KETTLE_MASTER_DUMP.REF_LOOKUP dr ON dr.RL_ID = od.DISCOUNT_REASON_ID
WHERE
    od.ORDER_SOURCE = 'COD'
    AND cp.PARTNER_CODE LIKE concat('%',:channelPartnerName,'%')
 AND od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate
ORDER BY ud.UNIT_NAME, BUSINESS_DATE
]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					<param name="channelPartnerName" displayName="Channel Partner Name"
                                          dataType="STRING" format="yyyy-MM-dd" />
					</params>

				</report>


				<report name="COD Item Level Complete Data"
					executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT
    ud.UNIT_NAME,od.BUSINESS_DATE,
    od.*, oi.*, cr.RL_NAME, dimension.RL_NAME
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        INNER JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
        INNER JOIN
    KETTLE_DUMP.ORDER_ITEM oi ON oi.ORDER_ID = od.ORDER_ID
        LEFT OUTER JOIN
    KETTLE_MASTER_DUMP.REF_LOOKUP cr ON cr.RL_ID = oi.COMPLIMENTARY_TYPE_ID
        LEFT OUTER JOIN
    KETTLE_MASTER_DUMP.REF_LOOKUP dimension ON dimension.RL_ID = oi.DIMENSION
WHERE
    od.ORDER_SOURCE = 'COD'
 AND od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate
ORDER BY od.UNIT_ID , BUSINESS_DATE
]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>

				</report>

				<report name="COD Order Status Event Report"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
    z.GENERATED_ORDER_ID,
    z.UNIT_NAME,
    z.FIRST_NAME,
    z.CONTACT_NUMBER,
    c.INITIATED,
    c.CREATED,
    c.PROCESSING,
    c.READY_TO_DISPATCH,
    c.SETTLED,
    c.CANCELLED_REQUESTED,
    c.CANCELLED
FROM
    (SELECT 
        od.ORDER_ID,
            od.GENERATED_ORDER_ID,
            ud.UNIT_NAME,
            ci.FIRST_NAME,
            ci.CONTACT_NUMBER
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    LEFT JOIN KETTLE_DUMP.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
    WHERE
        od.ORDER_SOURCE = 'COD'
            AND od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate) z
        LEFT JOIN
    (SELECT 
        *
    FROM
        (SELECT 
        a.ORDER_ID,
            MAX(a.INITIATED) INITIATED,
            MAX(a.CREATED) CREATED,
            MAX(a.PROCESSING) PROCESSING,
            MAX(a.READY_TO_DISPATCH) READY_TO_DISPATCH,
            MAX(a.SETTLED) SETTLED,
            MAX(a.CANCELLED_REQUESTED) CANCELLED_REQUESTED,
            MAX(a.CANCELLED) CANCELLED
    FROM
        (SELECT 
        ose.ORDER_ID,
            (CASE
                WHEN ose.FROM_STATUS = 'INITIATED' THEN ose.START_TIME
                ELSE NULL
            END) AS INITIATED,
            (CASE
                WHEN ose.TO_STATUS = 'CREATED' THEN ose.UPDATE_TIME
                ELSE NULL
            END) AS CREATED,
            (CASE
                WHEN ose.TO_STATUS = 'PROCESSING' THEN ose.UPDATE_TIME
                ELSE NULL
            END) AS PROCESSING,
            (CASE
                WHEN ose.TO_STATUS = 'READY_TO_DISPATCH' THEN ose.UPDATE_TIME
                ELSE NULL
            END) AS READY_TO_DISPATCH,
            (CASE
                WHEN ose.TO_STATUS = 'SETTLED' THEN ose.UPDATE_TIME
                ELSE NULL
            END) AS SETTLED,
            (CASE
                WHEN ose.TO_STATUS = 'CANCELLED_REQUESTED' THEN ose.UPDATE_TIME
                ELSE NULL
            END) AS CANCELLED_REQUESTED,
            (CASE
                WHEN ose.TO_STATUS = 'CANCELLED' THEN ose.UPDATE_TIME
                ELSE NULL
            END) AS CANCELLED
    FROM
        KETTLE_DUMP.ORDER_STATUS_EVENT ose
    WHERE
        ose.ORDER_ID > (SELECT 
                MAX(LAST_ORDER_ID) START_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(:fromBusinessDate, 1))
            AND ose.ORDER_ID <= (SELECT 
                MAX(LAST_ORDER_ID) END_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = :toBusinessDate)) a
    GROUP BY a.ORDER_ID) b
    WHERE
        b.CREATED IS NOT NULL) c ON c.ORDER_ID = z.ORDER_ID
]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
				<report name="COD Order Status Event Report via BILL Number"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
    z.GENERATED_ORDER_ID,
    z.UNIT_NAME,
    z.FIRST_NAME,
    z.CONTACT_NUMBER,
    c.INITIATED,
    c.CREATED,
    c.PROCESSING,
    c.READY_TO_DISPATCH,
    c.SETTLED,
    c.CANCELLED_REQUESTED,
    c.CANCELLED
FROM
    (SELECT 
        od.ORDER_ID,
            od.GENERATED_ORDER_ID,
            ud.UNIT_NAME,
            ci.FIRST_NAME,
            ci.CONTACT_NUMBER
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    LEFT JOIN KETTLE_DUMP.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
    WHERE
        od.ORDER_SOURCE = 'COD'
            AND od.GENERATED_ORDER_ID = :billNumber) z
        LEFT JOIN
    (SELECT 
        *
    FROM
        (SELECT 
        a.ORDER_ID,
            MAX(a.INITIATED) INITIATED,
            MAX(a.CREATED) CREATED,
            MAX(a.PROCESSING) PROCESSING,
            MAX(a.READY_TO_DISPATCH) READY_TO_DISPATCH,
            MAX(a.SETTLED) SETTLED,
            MAX(a.CANCELLED_REQUESTED) CANCELLED_REQUESTED,
            MAX(a.CANCELLED) CANCELLED
    FROM
        (SELECT 
        ose.ORDER_ID,
            (CASE
                WHEN ose.FROM_STATUS = 'INITIATED' THEN ose.START_TIME
                ELSE NULL
            END) AS INITIATED,
            (CASE
                WHEN ose.TO_STATUS = 'CREATED' THEN ose.UPDATE_TIME
                ELSE NULL
            END) AS CREATED,
            (CASE
                WHEN ose.TO_STATUS = 'PROCESSING' THEN ose.UPDATE_TIME
                ELSE NULL
            END) AS PROCESSING,
            (CASE
                WHEN ose.TO_STATUS = 'READY_TO_DISPATCH' THEN ose.UPDATE_TIME
                ELSE NULL
            END) AS READY_TO_DISPATCH,
            (CASE
                WHEN ose.TO_STATUS = 'SETTLED' THEN ose.UPDATE_TIME
                ELSE NULL
            END) AS SETTLED,
            (CASE
                WHEN ose.TO_STATUS = 'CANCELLED_REQUESTED' THEN ose.UPDATE_TIME
                ELSE NULL
            END) AS CANCELLED_REQUESTED,
            (CASE
                WHEN ose.TO_STATUS = 'CANCELLED' THEN ose.UPDATE_TIME
                ELSE NULL
            END) AS CANCELLED
    FROM
        KETTLE_DUMP.ORDER_STATUS_EVENT ose
    WHERE
        ose.ORDER_ID = (SELECT ORDER_ID FROM KETTLE_DUMP.ORDER_DETAIL WHERE GENERATED_ORDER_ID = :billNumber)) a
    GROUP BY a.ORDER_ID) b
    WHERE
        b.CREATED IS NOT NULL) c ON c.ORDER_ID = z.ORDER_ID
]]>
					</content>
					<params>
						<param name="billNumber" displayName="BILL Number"
                                          dataType="STRING" />
					</params>
				</report>	
				<report name="Order Status Event"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
    od.GENERATED_ORDER_ID,
    ose.*
FROM
    KETTLE_DUMP.ORDER_STATUS_EVENT ose
        INNER JOIN
    KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = ose.ORDER_ID
WHERE
    od.GENERATED_ORDER_ID LIKE CONCAT(:billNumber,'%')
LIMIT 100;
			
]]>
					</content>
					<params>
						<param name="billNumber" displayName="16 Digit Bill Number"
                                          dataType="STRING" format="yyyy-MM-dd" />
					</params>
				</report>
<report name="Customer Order Summary"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
		SELECT
    ud.UNIT_NAME,
    od.BUSINESS_DATE,
    od.*,
    dr.RL_NAME,
    cp.PARTNER_DISPLAY_NAME,
    dp.PARTNER_DISPLAY_NAME,
    ci.*,
    ai.*
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        INNER JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
        INNER JOIN
    KETTLE_DUMP.CHANNEL_PARTNER cp ON od.CHANNEL_PARTNER_ID = cp.PARTNER_ID
        INNER JOIN
    KETTLE_DUMP.DELIVERY_PARTNER dp ON od.DELIVERY_PARTNER_ID = dp.PARTNER_ID
        LEFT OUTER JOIN
    KETTLE_DUMP.CUSTOMER_INFO ci ON ci.CUSTOMER_ID = od.CUSTOMER_ID
        LEFT OUTER JOIN
    KETTLE_DUMP.CUSTOMER_ADDRESS_INFO ai ON od.DELIVERY_ADDRESS = ai.ADDRESS_ID
        LEFT OUTER JOIN
    KETTLE_MASTER_DUMP.REF_LOOKUP dr ON dr.RL_ID = od.DISCOUNT_REASON_ID
WHERE
    od.ORDER_STATUS <> "CANCELLED"
 AND od.CUSTOMER_ID = (SELECT CUSTOMER_ID FROM KETTLE_DUMP.CUSTOMER_INFO WHERE CONTACT_NUMBER = :contact);	
			
			
]]>
					</content>
					<params>
						<param name="contact" displayName="Contact"
                                          dataType="STRING" format="yyyy-MM-dd" />
					</params>
				</report>			
			</reports>
		</category>
		<category name="Locality" type="Cafe Operations"
                  accessCode="Marketing">
			<reports>
				<report name="LOCALITY WISE BILL LEVEL DATA "
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
			
			
			
SELECT * FROM (		
SELECT 
    od.ORDER_ID,
    od.BILLING_SERVER_TIME,
    od.CUSTOMER_ID,
    ci.CONTACT_NUMBER,
    COALESCE(ci.FIRST_NAME, 'NA') AS 'NAME',
    COALESCE(cai.LOCALITY, 'NA') AS 'LOCALITY',
    COALESCE(cai.ADDRESS_LINE_1, 'NA') AS 'ADDRESS1',
    COALESCE(cai.ADDRESS_LINE_2, 'NA') AS ADDRESS2,
    COALESCE(cai.ADDRESS_LINE_3, 'NA') AS ADDRESS3,
    cai.CITY,
    cai.STATE,
    COALESCE(cai.ADDRESS_TYPE, 'NA') AS ADDRESS_TYPE,
    od.TAXABLE_AMOUNT,
    od.DISCOUNT_AMOUNT,
    od.TOTAL_AMOUNT,
    od.SETTLED_AMOUNT,
    COALESCE(cp.PARTNER_CODE, 'NA') AS CHANNEL_PARTNER,
    COALESCE(dp.PARTNER_CODE, 'NA') AS DELIVERY_PARTNER
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        LEFT JOIN
    KETTLE_DUMP.CUSTOMER_ADDRESS_INFO cai ON od.DELIVERY_ADDRESS = cai.ADDRESS_ID
        LEFT JOIN
    KETTLE_DUMP.CUSTOMER_INFO ci ON od.CUSTOMER_ID = ci.CUSTOMER_ID
        LEFT JOIN
    KETTLE_DUMP.CHANNEL_PARTNER cp ON cp.PARTNER_ID = od.CHANNEL_PARTNER_ID
        LEFT JOIN
    KETTLE_DUMP.DELIVERY_PARTNER dp ON od.DELIVERY_PARTNER_ID = dp.PARTNER_ID
WHERE
    od.ORDER_SOURCE = 'COD'
        AND od.ORDER_STATUS <> 'CANCELLED'
 AND od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate) a

			
]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>

				</report>			



				<report name="LOCALITY WISE ITEM LEVEL DATA "
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
			
SELECT * FROM (			
SELECT 
    od.ORDER_ID,
    od.BILLING_SERVER_TIME,
    od.CUSTOMER_ID,
    ci.CONTACT_NUMBER,
    ci.FIRST_NAME,
    cai.LOCALITY,
    COALESCE(cai.ADDRESS_LINE_1, 'NA') AS ADDRESS1,
    COALESCE(cai.ADDRESS_LINE_2, 'NA') AS ADDRESS2,
    COALESCE(cai.ADDRESS_LINE_3, 'NA') AS ADDRESS3,
    cai.CITY,
    cai.STATE,
    COALESCE(cai.ADDRESS_TYPE, 'NA') AS ADDRESS_TYPE,
    pd.PRODUCT_NAME,
    oi.DIMENSION,
    oi.PRICE,
    oi.QUANTITY,
    oi.IS_COMPLIMENTARY,
    COALESCE(oi.COMPLIMENTARY_TYPE_ID, 'NA') AS COMPLIMENTARY_CODE,
    COALESCE(cc.NAME, 'NA') AS COMPLIMENTARY_REASON,
    cp.PARTNER_CODE CHANNEL_PARTNER,
    dp.PARTNER_CODE DELIVERY_PARTNER
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        LEFT JOIN
    KETTLE_DUMP.CUSTOMER_ADDRESS_INFO cai ON od.CUSTOMER_ID = cai.CUSTOMER_ID
        LEFT JOIN
    KETTLE_DUMP.CUSTOMER_INFO ci ON od.CUSTOMER_ID = ci.CUSTOMER_ID
        LEFT JOIN
    KETTLE_DUMP.CHANNEL_PARTNER cp ON cp.PARTNER_ID = od.CHANNEL_PARTNER_ID
        LEFT JOIN
    KETTLE_DUMP.DELIVERY_PARTNER dp ON od.DELIVERY_PARTNER_ID = dp.PARTNER_ID
        LEFT JOIN
    KETTLE_DUMP.ORDER_ITEM oi ON oi.ORDER_ID = od.ORDER_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd ON pd.PRODUCT_ID = oi.PRODUCT_ID
        LEFT JOIN
    KETTLE_DUMP.COMPLIMENTARY_CODE cc ON oi.COMPLIMENTARY_TYPE_ID = cc.COMP_ID
WHERE
    od.ORDER_SOURCE = 'COD'
  AND od.ORDER_STATUS <> 'CANCELLED'
  AND od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate ) a			
			
]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>

				</report>				
				<report name="LOCALITY WISE TAT DATA "
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
			
	SELECT * FROM (					
			SELECT 
    od.ORDER_ID,
    od.BILLING_SERVER_TIME,
    od.CUSTOMER_ID,
    ci.CONTACT_NUMBER,
    COALESCE(ci.FIRST_NAME, 'NA') AS 'NAME',
    COALESCE(cai.ADDRESS_TYPE, 'NA'),
    od.TAXABLE_AMOUNT AS NET_SALES,
    ose.FROM_STATUS,
    ose.TO_STATUS,
    ose.TRANSITION_STATUS,
    TIMESTAMPDIFF(SECOND,
        ose.START_TIME,
        ose.UPDATE_TIME) AS TIME_FOR_STATUS_CHANGE,
    COALESCE(cai.LOCALITY, 'NA'),
    COALESCE(cai.ADDRESS_LINE_1, 'NA') AS ADDRESS1,
    COALESCE(cai.ADDRESS_LINE_2, 'NA') AS ADDRESS2,
    COALESCE(cai.ADDRESS_LINE_3, 'NA') AS ADDRESS3,
    cai.CITY,
    cai.STATE,
    cp.PARTNER_CODE AS CHANNEL_PARTNER,
    COALESCE(dp.PARTNER_CODE, 'NA') AS DELIVERY_PARTNER
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        LEFT JOIN
    KETTLE_DUMP.CUSTOMER_ADDRESS_INFO cai ON od.CUSTOMER_ID = cai.CUSTOMER_ID
        LEFT JOIN
    KETTLE_DUMP.CUSTOMER_INFO ci ON od.CUSTOMER_ID = ci.CUSTOMER_ID
        LEFT JOIN
    KETTLE_DUMP.CHANNEL_PARTNER cp ON cp.PARTNER_ID = od.CHANNEL_PARTNER_ID
        LEFT JOIN
    KETTLE_DUMP.DELIVERY_PARTNER dp ON od.DELIVERY_PARTNER_ID = dp.PARTNER_ID
        LEFT JOIN
    KETTLE_DUMP.ORDER_STATUS_EVENT ose ON ose.ORDER_ID = od.ORDER_ID
WHERE
    od.ORDER_SOURCE = 'COD'
        AND od.ORDER_STATUS <> 'CANCELLED'
  AND od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate) a				
			
]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
				<report name="Locality Wise Delivery Order"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
			
				
SELECT 
    ud.UNIT_ID,
    ud.UNIT_NAME,
    cia.LOCALITY,
    od.BUSINESS_DATE,
    SUM(od.TAXABLE_AMOUNT) TAXABLE_AMOUNT,
    COUNT(*) ORDER_COUNT,
    ROUND(SUM(od.TAXABLE_AMOUNT) / COUNT(*),2) APC
FROM
    KETTLE_DUMP.ORDER_DETAIL od,
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud,
    KETTLE_DUMP.CUSTOMER_ADDRESS_INFO cia
WHERE
    od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate
        AND od.DELIVERY_ADDRESS = cia.ADDRESS_ID
        AND od.ORDER_STATUS <> 'CANCELLED'
        AND od.ORDER_SOURCE = 'COD'
        AND od.UNIT_ID = ud.UNIT_ID
        AND od.TOTAL_AMOUNT > 0.0
GROUP BY ud.UNIT_ID , ud.UNIT_NAME , cia.LOCALITY , od.BUSINESS_DATE
ORDER BY od.BUSINESS_DATE, ud.UNIT_ID , ud.UNIT_NAME , cia.LOCALITY 

			
			
]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>	
			</reports>
		</category>
	</categories>
</ReportCategories>
