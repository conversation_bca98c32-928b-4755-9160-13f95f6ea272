<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<ReportCategories xmlns="http://www.w3schools.com">
	<categories>
		<category name="Partner Commission Report" type="SuMo" accessCode="SuMo">
			<reports>
				<report name="Partner Commission Report"
                        executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT
        od.UNIT_ID,
        ud.UNIT_NAME,
	cp.PARTNER_DISPLAY_NAME,
        oi.PRODUCT_ID,
        oi.PRODUCT_NAME,
        SUM(oi.QUANTITY) QUANTITY,
        SUM(COALESCE(od.TAXABLE_AMOUNT,0)) TAXABLE_AMOUNT,
		cp.COMMISSION_RATE,
		TRUNCATE(SUM(COALESCE(od.TAXABLE_AMOUNT,0)) * cp.COMMISSION_RATE / 100, 0) AS CHANNEL_PARTNER_COST
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
	INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
	INNER JOIN KETTLE_DUMP.ORDER_ITEM oi ON oi.ORDER_ID = od.ORDER_ID
    LEFT JOIN KETTLE_DUMP.CHANNEL_PARTNER cp ON od.CHANNEL_PARTNER_ID = cp.PARTNER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
        AND od.ORDER_TYPE = 'order'
        AND od.BUSINESS_DATE BETWEEN :startDate AND :endDate
    GROUP BY od.UNIT_ID ,oi.PRODUCT_ID, cp.PARTNER_DISPLAY_NAME
    
                        ]]>
					</content>
					<params>
						<param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>

					</params>
				</report>

			</reports>
		</category>
		<category name="Employee Meal Report" type="SuMo" accessCode="SuMo">
			<reports>
				<report name="Employee Meal Report For a Unit For Date Range"
                        executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
    
                        ]]>
					</content>
					<params>
						<param name="unitId" displayName="Unit Id"
                               dataType="INTEGER"/>
						<param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>

					</params>
				</report>
				<report name="Employee Meal Report For Date Range"
                        executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
						
                        ]]>
					</content>
					<params>
						<param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
					</params>
				</report>
			</reports>
		</category>
		<category name="Settlement Reports" type="Kettle" accessCode="Kettle">
			<reports>
				<report name="Tax and Settlement Report For GST"
                        executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
						
						
SELECT 
    t2.*,
    t1.TAXABLE_AMOUNT,
    t1.TOTAL_TAX,
    t1.CGST,
    t1.SGST_UTGST,
    t1.OTHERS,
    t1.TWO_POINT_FIVE_PERCENT,
    t1.SIX_PERCENT,
    t1.NINE_PERCENT,
    t1.SUM_OF_TAX_BY_SPLIT,
    t1.TAX_DIFF,
    t1.TAXABLE_FOR_TWO_POINT_FIVE_PERCENT,
    t1.TAXABLE_FOR_SIX_PERCENT,
    t1.TAXABLE_FOR_NINE_PERCENT,
    t1.SUM_OF_TAXABLE_BY_SPLIT,
    t1.ZERO_TAX_AMOUNT,
    COALESCE(t3.GIFT_CARD_AMOUNT, 0) GIFT_CARD_AMOUNT
FROM
    (SELECT 
        a.BUSINESS_DATE,
            a.UNIT_ID,
            SUM(a.TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(a.TOTAL_TAX) TOTAL_TAX,
            SUM(a.CGST) CGST,
            SUM(a.SGST_UTGST) SGST_UTGST,
            SUM(a.OTHERS) OTHERS,
            SUM(a.TWO_POINT_FIVE) 'TWO_POINT_FIVE_PERCENT',
            SUM(a.SIX) 'SIX_PERCENT',
            SUM(a.NINE) 'NINE_PERCENT',
            SUM(a.TWO_POINT_FIVE) + SUM(a.SIX) + SUM(a.NINE) SUM_OF_TAX_BY_SPLIT,
            SUM(a.TOTAL_TAX) - SUM(a.TWO_POINT_FIVE) - SUM(a.SIX) - SUM(a.NINE) TAX_DIFF,
            SUM(a.TWO_POINT_FIVE_TAXABLE_AMOUNT) 'TAXABLE_FOR_TWO_POINT_FIVE_PERCENT',
            SUM(a.SIX_TAXABLE_AMOUNT) 'TAXABLE_FOR_SIX_PERCENT',
            SUM(a.NINE_TAXABLE_AMOUNT) 'TAXABLE_FOR_NINE_PERCENT',
            SUM(a.TWO_POINT_FIVE_TAXABLE_AMOUNT) + SUM(a.SIX_TAXABLE_AMOUNT) + SUM(a.NINE_TAXABLE_AMOUNT) SUM_OF_TAXABLE_BY_SPLIT,
            SUM(a.TAXABLE_AMOUNT) - SUM(a.TWO_POINT_FIVE_TAXABLE_AMOUNT) - SUM(a.SIX_TAXABLE_AMOUNT) - SUM(a.NINE_TAXABLE_AMOUNT) ZERO_TAX_AMOUNT
    FROM
        (SELECT 
        od.ORDER_ID,
            od.BUSINESS_DATE,
            od.UNIT_ID,
            MAX(od.TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            MAX(od.TOTAL_TAX) TOTAL_TAX,
            SUM(CASE
                WHEN otd.TAX_CODE = 'CGST' THEN otd.TOTAL_TAX
                ELSE 0
            END) 'CGST',
            SUM(CASE
                WHEN otd.TAX_CODE = 'SGST/UTGST' THEN otd.TOTAL_TAX
                ELSE 0
            END) 'SGST_UTGST',
            SUM(CASE
                WHEN
                    otd.TAX_CODE != 'SGST/UTGST'
                        && otd.TAX_CODE != 'CGST'
                THEN
                    otd.TOTAL_TAX
                ELSE 0
            END) 'OTHERS',
            SUM(CASE
                WHEN otd.TAX_PERCENTAGE = '2.5' THEN otd.TOTAL_TAX
                ELSE 0
            END) 'TWO_POINT_FIVE',
            SUM(CASE
                WHEN otd.TAX_PERCENTAGE = '6.0' THEN otd.TOTAL_TAX
                ELSE 0
            END) 'SIX',
            SUM(CASE
                WHEN otd.TAX_PERCENTAGE = '9.0' THEN otd.TOTAL_TAX
                ELSE 0
            END) 'NINE',
            SUM(CASE
                WHEN otd.TAX_PERCENTAGE = '2.5' THEN otd.TAXABLE_AMOUNT / 2
                ELSE 0
            END) 'TWO_POINT_FIVE_TAXABLE_AMOUNT',
            SUM(CASE
                WHEN otd.TAX_PERCENTAGE = '6.0' THEN otd.TAXABLE_AMOUNT / 2
                ELSE 0
            END) 'SIX_TAXABLE_AMOUNT',
            SUM(CASE
                WHEN otd.TAX_PERCENTAGE = '9.0' THEN otd.TAXABLE_AMOUNT / 2
                ELSE 0
            END) 'NINE_TAXABLE_AMOUNT'
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    LEFT JOIN KETTLE_DUMP.ORDER_TAX_DETAIL otd ON otd.ORDER_ID = od.ORDER_ID
    WHERE
        od.BUSINESS_DATE BETWEEN :startDate AND :endDate
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE IN ('order' , 'paid-employee-meal')
    GROUP BY od.ORDER_ID , od.BUSINESS_DATE) a
    GROUP BY a.UNIT_ID , a.BUSINESS_DATE) t1
        LEFT JOIN
    (SELECT 
        od.BUSINESS_DATE,
            ud.UNIT_ID,
            ud.UNIT_NAME,
            sd.STATE,
            COALESCE(SUM(os.AMOUNT_PAID + os.EXTRA_VOUCHERS), 0) 'Total Sales',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Cash' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Cash',
            SUM(CASE
                WHEN pm.MODE_NAME = 'CreditDebitCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Visa/Master Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'AMEX' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'AMEX Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Sodexo' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Sodexo Coupon',
            SUM(CASE
                WHEN pm.MODE_NAME = 'TicketRestaurant' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Ticket Restaurant Coupon',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Credit' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Credit',
            SUM(CASE
                WHEN pm.MODE_NAME = 'SodexoCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Sodexo Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'TicketRestaurantCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Ticket Restaurant Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'GiftCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Gift Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Paytm' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Paytm',
            SUM(CASE
                WHEN pm.MODE_NAME = 'RazorPay' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'RazorPay',
            SUM(CASE
                WHEN pm.MODE_NAME = 'PayTmOnline' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'PayTmOnline',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Mobikwik' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Mobikwik',
            SUM(CASE
                WHEN pm.MODE_NAME = 'FreeCharge' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'FreeCharge',
            SUM(CASE
                WHEN pm.MODE_NAME = 'DineOut' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'DineOut',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Prepaid' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Prepaid',	    

            SUM(CASE
                WHEN
                    pm.MODE_NAME = 'Credit'
                        AND od.CHANNEL_PARTNER_ID = 1
                THEN
                    os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'CHAAYOS',
            SUM(CASE
                WHEN
                    pm.MODE_NAME = 'Credit'
                        AND od.CHANNEL_PARTNER_ID = 2
                THEN
                    os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'CHAAYOS_DELIVERY',
            SUM(CASE
                WHEN
                    pm.MODE_NAME = 'Credit'
                        AND od.CHANNEL_PARTNER_ID = 3
                THEN
                    os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'ZOMATO',
            SUM(CASE
                WHEN
                    pm.MODE_NAME = 'Credit'
                        AND od.CHANNEL_PARTNER_ID = 4
                THEN
                    os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'TINYOWL',
            SUM(CASE
                WHEN
                    pm.MODE_NAME = 'Credit'
                        AND od.CHANNEL_PARTNER_ID = 5
                THEN
                    os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'FOODPANDA',
            SUM(CASE
                WHEN
                    pm.MODE_NAME = 'Credit'
                        AND od.CHANNEL_PARTNER_ID = 6
                THEN
                    os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'SWIGGY',
            SUM(CASE
                WHEN
                    pm.MODE_NAME = 'Credit'
                        AND od.CHANNEL_PARTNER_ID = 7
                THEN
                    os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'OLACAFE',
            SUM(CASE
                WHEN
                    pm.MODE_NAME = 'Credit'
                        AND od.CHANNEL_PARTNER_ID = 8
                THEN
                    os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'GROUPON',
            SUM(CASE
                WHEN
                    pm.MODE_NAME = 'Credit'
                        AND od.CHANNEL_PARTNER_ID = 9
                THEN
                    os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'CHAAYOS_TAKE_AWAY',
            SUM(CASE
                WHEN
                    pm.MODE_NAME = 'Credit'
                        AND od.CHANNEL_PARTNER_ID = 11
                THEN
                    os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'GROFERS',
            SUM(CASE
                WHEN
                    pm.MODE_NAME = 'Credit'
                        AND od.CHANNEL_PARTNER_ID = 12
                THEN
                    os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'RUNNR',
            SUM(CASE
                WHEN
                    pm.MODE_NAME = 'Credit'
                        AND od.CHANNEL_PARTNER_ID = 13
                THEN
                    os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'SCOOTSY',
            SUM(CASE
                WHEN
                    pm.MODE_NAME = 'Credit'
                        AND od.CHANNEL_PARTNER_ID = 14
                THEN
                    os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'CHAAYOS_WEB',
            SUM(CASE
                WHEN
                    pm.MODE_NAME = 'Credit'
                        AND od.CHANNEL_PARTNER_ID = 15
                THEN
                    os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'UBER_EATS',
            SUM(CASE
                WHEN
                    pm.MODE_NAME = 'Credit'
                        AND od.CHANNEL_PARTNER_ID = 16
                THEN
                    os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'BIGBASKET',
	    SUM(CASE
                WHEN
                    pm.MODE_NAME = 'Credit'
                        AND od.CHANNEL_PARTNER_ID BETWEEN 1 AND 16
                THEN
                    os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'CREDIT_SUM'
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    LEFT JOIN KETTLE_MASTER_DUMP.LOCATION_DETAIL ld ON ld.LOCATION_ID = ud.LOCATION_DETAIL_ID
    LEFT JOIN KETTLE_MASTER_DUMP.STATE_DETAIL sd ON ld.STATE_DETAIL_ID = sd.STATE_DETAIL_ID
    LEFT JOIN KETTLE_DUMP.ORDER_SETTLEMENT os ON od.ORDER_ID = os.ORDER_ID
    LEFT JOIN KETTLE_MASTER_DUMP.PAYMENT_MODE pm ON os.PAYMENT_MODE_ID = pm.PAYMENT_MODE_ID
    WHERE
        od.BUSINESS_DATE BETWEEN :startDate AND :endDate
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE IN ('order' , 'paid-employee-meal')
    GROUP BY ud.UNIT_ID , od.BUSINESS_DATE) t2 ON t1.UNIT_ID = t2.UNIT_ID
        AND t1.BUSINESS_DATE = t2.BUSINESS_DATE
        LEFT JOIN
    (SELECT 
        od.BUSINESS_DATE,
            od.UNIT_ID,
            SUM(oi.TOTAL_AMOUNT) GIFT_CARD_AMOUNT
    FROM
        ORDER_DETAIL od
    INNER JOIN ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.BUSINESS_DATE BETWEEN :startDate AND :endDate
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_TYPE IN ('order' , 'paid-employee-meal')
            AND oi.TAX_CODE IN ('GIFT_CARD')
    GROUP BY od.UNIT_ID , od.BUSINESS_DATE) t3 ON t1.UNIT_ID = t3.UNIT_ID
        AND t1.BUSINESS_DATE = t3.BUSINESS_DATE;
     
                        ]]>
					</content>
					<params>
						<param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>

					</params>
				</report>
				<report name="Tax and Settlement Report For GST UnitWise"
                        executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
						
SELECT 
    t2.*,
    t1.TAXABLE_AMOUNT,
    t1.TOTAL_TAX,
    t1.CGST,
    t1.SGST_UTGST,
    t1.OTHERS,
    t1.TWO_POINT_FIVE_PERCENT,
    t1.SIX_PERCENT,
    t1.NINE_PERCENT,
    t1.SUM_OF_TAX_BY_SPLIT,
    t1.TAX_DIFF,
    t1.TAXABLE_FOR_TWO_POINT_FIVE_PERCENT,
    t1.TAXABLE_FOR_SIX_PERCENT,
    t1.TAXABLE_FOR_NINE_PERCENT,
    t1.SUM_OF_TAXABLE_BY_SPLIT,
    t1.ZERO_TAX_AMOUNT,
    coalesce(t3.GIFT_CARD_AMOUNT,0) GIFT_CARD_AMOUNT
FROM
    (SELECT 
    a.BUSINESS_DATE,
    a.UNIT_ID,
    SUM(a.TAXABLE_AMOUNT) TAXABLE_AMOUNT,
    SUM(a.TOTAL_TAX) TOTAL_TAX,
    SUM(a.CGST) CGST,
    SUM(a.SGST_UTGST) SGST_UTGST,
    SUM(a.OTHERS) OTHERS,
    SUM(a.TWO_POINT_FIVE) 'TWO_POINT_FIVE_PERCENT',
    SUM(a.SIX) 'SIX_PERCENT',
    SUM(a.NINE) 'NINE_PERCENT',
    SUM(a.TWO_POINT_FIVE) + SUM(a.SIX) + SUM(a.NINE) SUM_OF_TAX_BY_SPLIT,
    SUM(a.TOTAL_TAX) - SUM(a.TWO_POINT_FIVE) - SUM(a.SIX) - SUM(a.NINE) TAX_DIFF,
    SUM(a.TWO_POINT_FIVE_TAXABLE_AMOUNT) 'TAXABLE_FOR_TWO_POINT_FIVE_PERCENT',
    SUM(a.SIX_TAXABLE_AMOUNT) 'TAXABLE_FOR_SIX_PERCENT',
    SUM(a.NINE_TAXABLE_AMOUNT) 'TAXABLE_FOR_NINE_PERCENT',
    SUM(a.TWO_POINT_FIVE_TAXABLE_AMOUNT) + SUM(a.SIX_TAXABLE_AMOUNT) + SUM(a.NINE_TAXABLE_AMOUNT) SUM_OF_TAXABLE_BY_SPLIT,
    SUM(a.TAXABLE_AMOUNT) - SUM(a.TWO_POINT_FIVE_TAXABLE_AMOUNT) - SUM(a.SIX_TAXABLE_AMOUNT) - SUM(a.NINE_TAXABLE_AMOUNT) ZERO_TAX_AMOUNT
FROM
    (SELECT 
        od.ORDER_ID,
            od.BUSINESS_DATE,
            od.UNIT_ID,
            MAX(od.TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            MAX(od.TOTAL_TAX) TOTAL_TAX,
            SUM(CASE
                WHEN otd.TAX_CODE = 'CGST' THEN otd.TOTAL_TAX
                ELSE 0
            END) 'CGST',
            SUM(CASE
                WHEN otd.TAX_CODE = 'SGST/UTGST' THEN otd.TOTAL_TAX
                ELSE 0
            END) 'SGST_UTGST',
            SUM(CASE
                WHEN
                    otd.TAX_CODE != 'SGST/UTGST'
                        && otd.TAX_CODE != 'CGST'
                THEN
                    otd.TOTAL_TAX
                ELSE 0
            END) 'OTHERS',
            SUM(CASE
                WHEN otd.TAX_PERCENTAGE = '2.5' THEN otd.TOTAL_TAX
                ELSE 0
            END) 'TWO_POINT_FIVE',
            SUM(CASE
                WHEN otd.TAX_PERCENTAGE = '6.0' THEN otd.TOTAL_TAX
                ELSE 0
            END) 'SIX',
            SUM(CASE
                WHEN otd.TAX_PERCENTAGE = '9.0' THEN otd.TOTAL_TAX
                ELSE 0
            END) 'NINE',
            SUM(CASE
                WHEN otd.TAX_PERCENTAGE = '2.5' THEN otd.TAXABLE_AMOUNT / 2
                ELSE 0
            END) 'TWO_POINT_FIVE_TAXABLE_AMOUNT',
            SUM(CASE
                WHEN otd.TAX_PERCENTAGE = '6.0' THEN otd.TAXABLE_AMOUNT / 2
                ELSE 0
            END) 'SIX_TAXABLE_AMOUNT',
            SUM(CASE
                WHEN otd.TAX_PERCENTAGE = '9.0' THEN otd.TAXABLE_AMOUNT / 2
                ELSE 0
            END) 'NINE_TAXABLE_AMOUNT'
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    LEFT JOIN KETTLE_DUMP.ORDER_TAX_DETAIL otd ON otd.ORDER_ID = od.ORDER_ID
    WHERE
        od.BUSINESS_DATE BETWEEN :startDate AND :endDate
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE IN ('order' , 'paid-employee-meal')
	    AND od.UNIT_ID = :unitId
    GROUP BY od.ORDER_ID , od.BUSINESS_DATE) a
GROUP BY a.UNIT_ID , a.BUSINESS_DATE) t1
        LEFT JOIN
    (SELECT 
		od.BUSINESS_DATE,
        ud.UNIT_ID,
            ud.UNIT_NAME,
            sd.STATE,
            COALESCE(SUM(os.AMOUNT_PAID + os.EXTRA_VOUCHERS), 0) 'Total Sales',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Cash' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Cash',
            SUM(CASE
                WHEN pm.MODE_NAME = 'CreditDebitCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Visa/Master Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'AMEX' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'AMEX Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Sodexo' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Sodexo Coupon',
            SUM(CASE
                WHEN pm.MODE_NAME = 'TicketRestaurant' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Ticket Restaurant Coupon',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Credit' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Credit',
            SUM(CASE
                WHEN pm.MODE_NAME = 'SodexoCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Sodexo Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'TicketRestaurantCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Ticket Restaurant Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'GiftCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Gift Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Paytm' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Paytm',
            SUM(CASE
                WHEN pm.MODE_NAME = 'RazorPay' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'RazorPay',
            SUM(CASE
                WHEN pm.MODE_NAME = 'PayTmOnline' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'PayTmOnline',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Mobikwik' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Mobikwik',
            SUM(CASE
                WHEN pm.MODE_NAME = 'FreeCharge' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'FreeCharge',
            SUM(CASE
                WHEN pm.MODE_NAME = 'DineOut' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'DineOut',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Prepaid' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Prepaid'
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    LEFT JOIN KETTLE_MASTER_DUMP.LOCATION_DETAIL ld ON ld.LOCATION_ID = ud.LOCATION_DETAIL_ID
    LEFT JOIN KETTLE_MASTER_DUMP.STATE_DETAIL sd ON ld.STATE_DETAIL_ID = sd.STATE_DETAIL_ID
    LEFT JOIN KETTLE_DUMP.ORDER_SETTLEMENT os ON od.ORDER_ID = os.ORDER_ID
    LEFT JOIN KETTLE_MASTER_DUMP.PAYMENT_MODE pm ON os.PAYMENT_MODE_ID = pm.PAYMENT_MODE_ID
    WHERE
        od.BUSINESS_DATE BETWEEN :startDate AND :endDate
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE IN ('order' , 'paid-employee-meal')
	    AND od.UNIT_ID = :unitId
    GROUP BY ud.UNIT_ID, od.BUSINESS_DATE) t2 ON t1.UNIT_ID = t2.UNIT_ID AND t1.BUSINESS_DATE = t2.BUSINESS_DATE
    LEFT JOIN 
    ( SELECT 
    od.BUSINESS_DATE, od.UNIT_ID, SUM(oi.TOTAL_AMOUNT) GIFT_CARD_AMOUNT
FROM
    ORDER_DETAIL od
        INNER JOIN
    ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
WHERE
    od.BUSINESS_DATE BETWEEN :startDate AND :endDate
        AND od.ORDER_STATUS <> 'CANCELLED'
        AND od.ORDER_TYPE IN ('order' , 'paid-employee-meal')
        AND oi.TAX_CODE IN ('GIFT_CARD')
	    AND od.UNIT_ID = :unitId
GROUP BY od.UNIT_ID , od.BUSINESS_DATE) t3 ON t1.UNIT_ID = t3.UNIT_ID AND t1.BUSINESS_DATE = t3.BUSINESS_DATE;
    
                        ]]>
					</content>
					<params>
						<param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>

				<param name="unitId" displayName="Unit Id"
                               dataType="INTEGER"/>
					</params>
				</report>

<report name="Tax and Settlement Report For VAT"
                        executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
    t2.*,
    t1.TAXABLE_AMOUNT,
    t1.TOTAL_TAX,
    t1.NET_PRICE_VAT,
    t1.MRP_VAT,
    t1.SERVICE_TAX,
    t1.SURCHARGE_TAX,
    t1.SB_CESS,
    t1.KK_CESS,
    t1.OTHERS,
    t1.POINT_TWO_ZERO,
    t1.POINT_THREE_ZERO,
    t1.FOUR,
    t1.FIVE,
    t1.FIVE_POINT_SIX_ZERO,
    t1.EIGHT_POINT_FOUR_ZERO,
    t1.TWELVE_POINT_FIVE_ZERO,
    t1.THIRTEEN_POINT_FIVE_ZERO,
    t1.FOURTEEN_POINT_FIVE_ZERO,
    t1.SUM_OF_TAX_BY_SPLIT,
    t1.TAX_DIFF,
    coalesce(t3.GIFT_CARD_AMOUNT,0) GIFT_CARD_AMOUNT
FROM
    (SELECT 
    a.BUSINESS_DATE,
    a.UNIT_ID,
    SUM(a.TAXABLE_AMOUNT) TAXABLE_AMOUNT,
    SUM(a.TOTAL_TAX) TOTAL_TAX,
    SUM(a.NET_PRICE_VAT) NET_PRICE_VAT,
    SUM(a.MRP_VAT) MRP_VAT,
    SUM(a.SERVICE_TAX) SERVICE_TAX,
    SUM(a.SURCHARGE_TAX) SURCHARGE_TAX,
    SUM(a.SB_CESS) SB_CESS,
    SUM(a.KK_CESS) KK_CESS,
    SUM(a.OTHERS) OTHERS,
    SUM(a.POINT_TWO_ZERO) POINT_TWO_ZERO,
    SUM(a.POINT_THREE_ZERO) POINT_THREE_ZERO,
    SUM(a.FOUR) FOUR,
    SUM(a.FIVE) FIVE,
    SUM(a.FIVE_POINT_SIX_ZERO) FIVE_POINT_SIX_ZERO,
    SUM(a.EIGHT_POINT_FOUR_ZERO) EIGHT_POINT_FOUR_ZERO,
    SUM(a.TWELVE_POINT_FIVE_ZERO) TWELVE_POINT_FIVE_ZERO,
    SUM(a.THIRTEEN_POINT_FIVE_ZERO) THIRTEEN_POINT_FIVE_ZERO,
    SUM(a.FOURTEEN_POINT_FIVE_ZERO) FOURTEEN_POINT_FIVE_ZERO,
    SUM(a.POINT_TWO_ZERO) + SUM(a.POINT_THREE_ZERO) + SUM(a.FOUR) + SUM(a.FIVE) + SUM(a.FIVE_POINT_SIX_ZERO) + SUM(a.EIGHT_POINT_FOUR_ZERO) + SUM(a.TWELVE_POINT_FIVE_ZERO) + SUM(a.THIRTEEN_POINT_FIVE_ZERO) + SUM(a.FOURTEEN_POINT_FIVE_ZERO) SUM_OF_TAX_BY_SPLIT,
    SUM(a.TOTAL_TAX) - (SUM(a.POINT_TWO_ZERO) + SUM(a.POINT_THREE_ZERO) + SUM(a.FOUR) + SUM(a.FIVE) + SUM(a.FIVE_POINT_SIX_ZERO) + SUM(a.EIGHT_POINT_FOUR_ZERO) + SUM(a.TWELVE_POINT_FIVE_ZERO) + SUM(a.THIRTEEN_POINT_FIVE_ZERO) + SUM(a.FOURTEEN_POINT_FIVE_ZERO)) TAX_DIFF
FROM
    (SELECT 
        od.ORDER_ID,
            od.BUSINESS_DATE,
            od.UNIT_ID,
            MAX(od.TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            MAX(od.TOTAL_TAX) TOTAL_TAX,
            SUM(CASE
                WHEN otd.TAX_CODE = 'NET_PRICE_VAT' THEN otd.TOTAL_TAX
                ELSE 0
            END) 'NET_PRICE_VAT',
            SUM(CASE
                WHEN otd.TAX_CODE = 'MRP_VAT' THEN otd.TOTAL_TAX
                ELSE 0
            END) 'MRP_VAT',
            SUM(CASE
                WHEN otd.TAX_CODE = 'SERVICE_TAX' THEN otd.TOTAL_TAX
                ELSE 0
            END) 'SERVICE_TAX',
            SUM(CASE
                WHEN otd.TAX_CODE = 'SURCHARGE_TAX' THEN otd.TOTAL_TAX
                ELSE 0
            END) 'SURCHARGE_TAX',
            SUM(CASE
                WHEN otd.TAX_CODE = 'SB_CESS' THEN otd.TOTAL_TAX
                ELSE 0
            END) 'SB_CESS',
            SUM(CASE
                WHEN otd.TAX_CODE = 'KK_CESS' THEN otd.TOTAL_TAX
                ELSE 0
            END) 'KK_CESS',
            SUM(CASE
                WHEN otd.TAX_CODE NOT IN ('NET_PRICE_VAT' , 'MRP_VAT', 'SERVICE_TAX', 'SURCHARGE_TAX', 'SB_CESS', 'KK_CESS') THEN otd.TOTAL_TAX
                ELSE 0
            END) 'OTHERS',
            SUM(CASE
                WHEN otd.TAX_PERCENTAGE = '0.20' THEN otd.TOTAL_TAX
                ELSE 0
            END) 'POINT_TWO_ZERO',
            SUM(CASE
                WHEN otd.TAX_PERCENTAGE = '0.30' THEN otd.TOTAL_TAX
                ELSE 0
            END) 'POINT_THREE_ZERO',
            SUM(CASE
                WHEN otd.TAX_PERCENTAGE = '4.00' THEN otd.TOTAL_TAX
                ELSE 0
            END) 'FOUR',
            SUM(CASE
                WHEN otd.TAX_PERCENTAGE = '5.00' THEN otd.TOTAL_TAX
                ELSE 0
            END) 'FIVE',
            SUM(CASE
                WHEN otd.TAX_PERCENTAGE = '5.60' THEN otd.TOTAL_TAX
                ELSE 0
            END) 'FIVE_POINT_SIX_ZERO',
            SUM(CASE
                WHEN otd.TAX_PERCENTAGE = '8.40' THEN otd.TOTAL_TAX
                ELSE 0
            END) 'EIGHT_POINT_FOUR_ZERO',
            SUM(CASE
                WHEN otd.TAX_PERCENTAGE = '12.50' THEN otd.TOTAL_TAX
                ELSE 0
            END) 'TWELVE_POINT_FIVE_ZERO',
            SUM(CASE
                WHEN otd.TAX_PERCENTAGE = '13.50' THEN otd.TOTAL_TAX
                ELSE 0
            END) 'THIRTEEN_POINT_FIVE_ZERO',
            SUM(CASE
                WHEN otd.TAX_PERCENTAGE = '14.50' THEN otd.TOTAL_TAX
                ELSE 0
            END) 'FOURTEEN_POINT_FIVE_ZERO',
            SUM(CASE
                WHEN otd.TAX_PERCENTAGE = '0.20' THEN otd.TAXABLE_AMOUNT
                ELSE 0
            END) 'POINT_TWO_ZERO_TAXABLE_AMOUNT',
            SUM(CASE
                WHEN otd.TAX_PERCENTAGE = '0.30' THEN otd.TAXABLE_AMOUNT
                ELSE 0
            END) 'POINT_THREE_ZERO_TAXABLE_AMOUNT',
            SUM(CASE
                WHEN otd.TAX_PERCENTAGE = '4.00' THEN otd.TAXABLE_AMOUNT
                ELSE 0
            END) 'FOUR_TAXABLE_AMOUNT',
            SUM(CASE
                WHEN otd.TAX_PERCENTAGE = '5.00' THEN otd.TAXABLE_AMOUNT
                ELSE 0
            END) 'FIVE_TAXABLE_AMOUNT',
            SUM(CASE
                WHEN otd.TAX_PERCENTAGE = '5.60' THEN otd.TAXABLE_AMOUNT
                ELSE 0
            END) 'FIVE_POINT_SIX_ZERO_TAXABLE_AMOUNT',
            SUM(CASE
                WHEN otd.TAX_PERCENTAGE = '8.40' THEN otd.TAXABLE_AMOUNT
                ELSE 0
            END) 'EIGHT_POINT_FOUR_ZERO_TAXABLE_AMOUNT',
            SUM(CASE
                WHEN otd.TAX_PERCENTAGE = '12.50' THEN otd.TAXABLE_AMOUNT
                ELSE 0
            END) 'TWELVE_POINT_FIVE_ZERO_TAXABLE_AMOUNT',
            SUM(CASE
                WHEN otd.TAX_PERCENTAGE = '13.50' THEN otd.TAXABLE_AMOUNT
                ELSE 0
            END) 'THIRTEEN_POINT_FIVE_ZERO_TAXABLE_AMOUNT',
            SUM(CASE
                WHEN otd.TAX_PERCENTAGE = '14.50' THEN otd.TAXABLE_AMOUNT
                ELSE 0
            END) 'FOURTEEN_POINT_FIVE_ZERO_TAXABLE_AMOUNT'
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    LEFT JOIN KETTLE_DUMP.ORDER_TAX_DETAIL otd ON otd.ORDER_ID = od.ORDER_ID
    WHERE
        od.BUSINESS_DATE BETWEEN :startDate AND :endDate
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE IN ('order' , 'paid-employee-meal')
    GROUP BY od.ORDER_ID , od.BUSINESS_DATE) a
GROUP BY a.UNIT_ID , a.BUSINESS_DATE) t1
        LEFT JOIN
    (SELECT 
		od.BUSINESS_DATE,
        ud.UNIT_ID,
            ud.UNIT_NAME,
            sd.STATE,
            COALESCE(SUM(os.AMOUNT_PAID + os.EXTRA_VOUCHERS), 0) 'Total Sales',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Cash' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Cash',
            SUM(CASE
                WHEN pm.MODE_NAME = 'CreditDebitCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Visa/Master Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'AMEX' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'AMEX Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Sodexo' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Sodexo Coupon',
            SUM(CASE
                WHEN pm.MODE_NAME = 'TicketRestaurant' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Ticket Restaurant Coupon',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Credit' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Credit',
            SUM(CASE
                WHEN pm.MODE_NAME = 'SodexoCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Sodexo Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'TicketRestaurantCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Ticket Restaurant Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'GiftCard' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Gift Card',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Paytm' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Paytm',
            SUM(CASE
                WHEN pm.MODE_NAME = 'RazorPay' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'RazorPay',
            SUM(CASE
                WHEN pm.MODE_NAME = 'PayTmOnline' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'PayTmOnline',
            SUM(CASE
                WHEN pm.MODE_NAME = 'Mobikwik' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'Mobikwik',
            SUM(CASE
                WHEN pm.MODE_NAME = 'FreeCharge' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'FreeCharge',
            SUM(CASE
                WHEN pm.MODE_NAME = 'DineOut' THEN os.AMOUNT_PAID + os.EXTRA_VOUCHERS
                ELSE 0
            END) 'DineOut'
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    LEFT JOIN KETTLE_MASTER_DUMP.LOCATION_DETAIL ld ON ld.LOCATION_ID = ud.LOCATION_DETAIL_ID
    LEFT JOIN KETTLE_MASTER_DUMP.STATE_DETAIL sd ON ld.STATE_DETAIL_ID = sd.STATE_DETAIL_ID
    LEFT JOIN KETTLE_DUMP.ORDER_SETTLEMENT os ON od.ORDER_ID = os.ORDER_ID
    LEFT JOIN KETTLE_MASTER_DUMP.PAYMENT_MODE pm ON os.PAYMENT_MODE_ID = pm.PAYMENT_MODE_ID
    WHERE
        od.BUSINESS_DATE BETWEEN :startDate AND :endDate
            AND od.ORDER_STATUS <> 'CANCELLED'
            AND ORDER_TYPE IN ('order' , 'paid-employee-meal')
    GROUP BY ud.UNIT_ID, od.BUSINESS_DATE) t2 ON t1.UNIT_ID = t2.UNIT_ID AND t1.BUSINESS_DATE = t2.BUSINESS_DATE
    LEFT JOIN 
    ( SELECT 
    od.BUSINESS_DATE, od.UNIT_ID, SUM(oi.TOTAL_AMOUNT) GIFT_CARD_AMOUNT
FROM
    ORDER_DETAIL od
        INNER JOIN
    ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
WHERE
    od.BUSINESS_DATE BETWEEN :startDate AND :endDate
        AND od.ORDER_STATUS <> 'CANCELLED'
        AND od.ORDER_TYPE IN ('order' , 'paid-employee-meal')
        AND oi.TAX_CODE IN ('GIFT_CARD')
GROUP BY od.UNIT_ID , od.BUSINESS_DATE) t3 ON t1.UNIT_ID = t3.UNIT_ID AND t1.BUSINESS_DATE = t3.BUSINESS_DATE;					
    
                        ]]>
					</content>
					<params>
						<param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>

					</params>
				</report>
			</reports>
		</category>
		<category name="Order IO Report" type="SuMo" accessCode="SuMo">
			<reports>
				<report name="Order IO Report"
                        executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT * FROM (
SELECT 
    ORDER_ID,
    GENERATED_ORDER_ID,
    UNIT_ORDER_ID,
    CUSTOMER_ID,
    EMP_ID,
    ORDER_STATUS,
    CANCELATION_REASON,
    SETTLEMENT_TYPE,
    UNIT_ID,
    HAS_PARCEL,
    BILL_START_TIME,
    BILL_GENERATION_TIME,
    CHANNEL_PARTNER_ID,
    TOTAL_AMOUNT,
    TAXABLE_AMOUNT,
    DISCOUNT_PERCENT,
    DISCOUNT_AMOUNT,
    DISCOUNT_REASON_ID,
    DISCOUNT_REASON,
    NET_PRICE_VAT_PERCENT,
    NET_PRICE_VAT_AMOUNT,
    MRP_VAT_PERCENT,
    MRP_VAT_AMOUNT,
    SERVICE_TAX_PERCENT,
    SERVICE_TAX_AMOUNT,
    SURCHARGE_TAX_PERCENT,
    SURCHARGE_TAX_AMOUNT,
    GST_PERCENT,
    GST_AMOUNT,
    SERVICE_CHARGE_PERCENT,
    SERVICE_CHARGE_AMOUNT,
    ROUND_OFF_AMOUNT,
    SETTLED_AMOUNT,
    ORDER_SOURCE,
    PRINT_COUNT,
    BILL_CANCELLATION_TIME,
    CANCELLED_BY,
    CANCEL_APPROVED_BY,
    POINTS_REDEEMED,
    TERMINAL_ID,
    DELIVERY_PARTNER_ID,
    TRIM(REPLACE(REPLACE(REPLACE(REPLACE(ORDER_REMARK, '\n', ' '), '\r', ' '), '\t', ' '),',',' ')) ORDER_REMARK,
    SB_CESS_PERCENT,
    SB_CESS_AMOUNT,
    DELIVERY_ADDRESS,
    ORDER_SOURCE_ID,
    SALE_AMOUNT,
    PROMOTIONAL_DISCOUNT,
    TOTAL_DISCOUNT,
    BILL_CREATION_SECONDS,
    BILLING_SERVER_TIME,
    OFFER_CODE,
    SAVING_AMOUNT,
    SUBSCRIPTION_ID,
    KK_CESS_PERCENT,
    KK_CESS_AMOUNT,
    TABLE_NUMBER,
    TEMP_CODE,
    CUSTOMER_NAME,
    CAMPAIGN_ID,
    TOTAL_TAX,
    CANCELATION_REASON_ID,
    WASTAGE_TYPE,
    WASTAGE_KETTLE_ID,
    ORDER_TYPE,
    LINKED_ORDER_ID,
    TOKEN_NUMBER,
    BUSINESS_DATE,
    MANUAL_BILL_BOOK_NO,
    OUT_OF_DELIVERY
FROM
    ORDER_DETAIL
WHERE ORDER_STATUS <> 'CANCELLED'
AND BUSINESS_DATE BETWEEN :startDate AND :endDate) A ;

                        ]]>
					</content>
					<params>
						<param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>

					</params>
				</report>
			</reports>
		</category>

	</categories>
</ReportCategories>
