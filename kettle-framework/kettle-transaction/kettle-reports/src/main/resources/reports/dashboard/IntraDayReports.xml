<ReportCategories xmlns="http://www.w3schools.com">
	<categories>
		<category name="Intra Day Sales Report" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>,<EMAIL>"
                        schedule="">
			<reports>
				<report id="1" name="All Regions" executionType="SQL"
                                        GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[

SELECT 
    A.BIZ_DATE,
    'All Regions' AS TOTAL,
    A.NET_TICKETS,
    B.LWSD_NET_TICKETS,
    A.NET_APC,
    B.LWSD_NET_APC,
    A.NET_SALES,
    B.LWSD_NET_SALES,
    A.NET_DELIVERY_TICKETS,
    B.LWSD_NET_DELIVERY_TICKETS LWSD_NET_DELIVERY_TICKETS,
    A.NET_DELIVERY_APC,
    B.LWSD_NET_DELIVERY_APC,
    A.NET_DELIVERY_SALES,
    B.LWSD_NET_DELIVERY_SALES LWSD_NET_DELIVERY_SALES,
    (A.NET_TICKETS - A.NET_DELIVERY_TICKETS) DINE_IN_TICKETS,
    (B.LWSD_NET_TICKETS - B.LWSD_NET_DELIVERY_TICKETS) LWSD_DINE_IN_TICKETS,
    TRUNCATE((A.NET_SALES + A.GIFT_CARD_REDEMPTION - A.NET_DELIVERY_SALES - A.GIFT_CARD_AMOUNT) / (A.NET_TICKETS - A.NET_DELIVERY_TICKETS),
        0) NET_DINE_IN_APC,
    TRUNCATE((B.LWSD_NET_SALES + B.LWSD_GIFT_CARD_REDEMPTION - B.LWSD_NET_DELIVERY_SALES - B.LWSD_GIFT_CARD_AMOUNT) / (B.LWSD_NET_TICKETS - B.LWSD_NET_DELIVERY_TICKETS),
        0) LWSD_NET_DINE_IN_APC,
    (A.NET_SALES - A.NET_DELIVERY_SALES) NET_DINE_IN_SALES,
    (B.LWSD_NET_SALES - B.LWSD_NET_DELIVERY_SALES) LWSD_NET_DINE_IN_SALES,
    A.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
    A.GIFT_CARD_AMOUNT,
    COALESCE(B.LWSD_GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
    A.MERCHANDISE_SALES,
    B.LWSD_MERCHANDISE_SALES
FROM
    (SELECT 
        m.BIZ_DATE,
            (m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) NET_TICKETS,
            (m.NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0)) NET_SALES,
            TRUNCATE((m.NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0)) / GREATEST((m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) NET_APC,
            m.NET_DELIVERY_TICKETS,
            m.NET_DELIVERY_SALES,
            COALESCE(m.NET_DELIVERY_APC, 0) NET_DELIVERY_APC,
            m.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) GIFT_CARD_AMOUNT,
            COALESCE(n.MERCHANDISE_SALES, 0) MERCHANDISE_SALES,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) GIFT_CARD_TICKETS,
            j.COLD_PENETRATION
    FROM
        (SELECT 
        od.BIZ_DATE,
            od.NET_TICKETS,
            od.NET_SALES,
            od.NET_APC,
            od.NET_DELIVERY_TICKETS,
            od.NET_DELIVERY_SALES,
            od.NET_DELIVERY_APC,
            od.CUSTOMER_CAPTURE_PERCENT_OF_TKTS
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS NET_SALES,
            TRUNCATE(SUM(od.TAXABLE_AMOUNT) / SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END), 0) AS NET_APC,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS NET_DELIVERY_APC,
            (SUM(CASE
                WHEN
                    od.CUSTOMER_ID > 5
                        AND od.TOTAL_AMOUNT <> '0.00'
                THEN
                    1
                ELSE 0
            END)) * 100 / (SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END)) AS CUSTOMER_CAPTURE_PERCENT_OF_TKTS
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) od) m
    LEFT OUTER JOIN (SELECT 
        qa.BIZ_DATE,
            TRUNCATE((SUM(COLD_QUANTITY) / SUM(TOTAL_QUANTITY)) * 100.0, 0) COLD_PENETRATION
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END COLD_QUANTITY,
            CASE
                WHEN pd.PRODUCT_TYPE IN (5 , 6, 7, 10) THEN oi.QUANTITY
                ELSE 0
            END TOTAL_QUANTITY
    FROM
        KETTLE_DUMP.ORDER_ITEM oi
    INNER JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) qa
    GROUP BY qa.BIZ_DATE) j ON j.BIZ_DATE = m.BIZ_DATE
    LEFT OUTER JOIN (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(CASE
                WHEN a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT THEN 1
                ELSE 0
            END) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT,
            SUM(a.MERCHANDISE_SALES) MERCHANDISE_SALES
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
            CASE
                WHEN oi.PRODUCT_ID IN (1026 , 1027, 1048) THEN SUM(oi.QUANTITY * oi.PRICE)
                ELSE 0
            END GIFT_CARD_AMOUNT,
            CASE
                WHEN oi.PRODUCT_ID NOT IN (1026 , 1027, 1048) THEN SUM(oi.QUANTITY * oi.PRICE)
                ELSE 0
            END MERCHANDISE_SALES,
            od.SETTLED_AMOUNT
    FROM
        KETTLE_DUMP.ORDER_ITEM oi
    INNER JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')
            AND oi.PRODUCT_ID IN (1026 , 1027, 1048, 692, 700, 710, 720, 730, 1057)
    GROUP BY oi.ORDER_ID , od.UNIT_ID) a
    GROUP BY BIZ_DATE) n ON m.BIZ_DATE = n.BIZ_DATE
    LEFT OUTER JOIN (SELECT 
        a.BIZ_DATE,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_TYPE = 'order'
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE_DUMP.ORDER_SETTLEMENT os
            INNER JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED'
                    AND od.ORDER_TYPE = 'order'
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00'), INTERVAL 5 HOUR) AND CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) a
    INNER JOIN (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE_DUMP.ORDER_SETTLEMENT os
    INNER JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY BIZ_DATE) b ON a.BIZ_DATE = b.BIZ_DATE) p ON m.BIZ_DATE = p.BIZ_DATE) A
        LEFT OUTER JOIN
    (SELECT 
        m.BIZ_DATE,
            (m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) LWSD_NET_TICKETS,
            (m.LWSD_NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0)) LWSD_NET_SALES,
            TRUNCATE((m.LWSD_NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0)) / GREATEST((m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) LWSD_NET_APC,
            m.LWSD_NET_DELIVERY_TICKETS,
            m.LWSD_NET_DELIVERY_SALES,
            COALESCE(m.LWSD_NET_DELIVERY_APC, 0) LWSD_NET_DELIVERY_APC,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
            COALESCE(n.MERCHANDISE_SALES, 0) LWSD_MERCHANDISE_SALES,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) LWSD_GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) LWSD_GIFT_CARD_TICKETS,
            COALESCE(j.COLD_PENETRATION, 0) LWSD_COLD_PENETRATION
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS LWSD_NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS LWSD_NET_SALES,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS LWSD_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS LWSD_NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS LWSD_NET_DELIVERY_APC
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00'), INTERVAL - 7 DAY)) m
    LEFT OUTER JOIN (SELECT 
        qa.BIZ_DATE,
            TRUNCATE((SUM(COLD_QUANTITY) / SUM(TOTAL_QUANTITY)) * 100.0, 0) COLD_PENETRATION
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END COLD_QUANTITY,
            CASE
                WHEN pd.PRODUCT_TYPE IN (5 , 6, 7, 10) THEN oi.QUANTITY
                ELSE 0
            END TOTAL_QUANTITY
    FROM
        KETTLE_DUMP.ORDER_ITEM oi
    INNER JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00'), INTERVAL - 7 DAY)) qa
    GROUP BY qa.BIZ_DATE) j ON j.BIZ_DATE = m.BIZ_DATE
    LEFT OUTER JOIN (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(CASE
                WHEN a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT THEN 1
                ELSE 0
            END) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT,
            SUM(a.MERCHANDISE_SALES) MERCHANDISE_SALES
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
            CASE
                WHEN oi.PRODUCT_ID IN (1026 , 1027, 1048) THEN SUM(oi.QUANTITY * oi.PRICE)
                ELSE 0
            END GIFT_CARD_AMOUNT,
            CASE
                WHEN oi.PRODUCT_ID NOT IN (1026 , 1027, 1048) THEN SUM(oi.QUANTITY * oi.PRICE)
                ELSE 0
            END MERCHANDISE_SALES,
            od.SETTLED_AMOUNT
    FROM
        KETTLE_DUMP.ORDER_ITEM oi
    INNER JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00'), INTERVAL - 7 DAY)
            AND oi.PRODUCT_ID IN (1026 , 1027, 1048, 692, 700, 710, 720, 730, 1057)
    GROUP BY oi.ORDER_ID , od.UNIT_ID) a
    GROUP BY BIZ_DATE) n ON m.BIZ_DATE = n.BIZ_DATE
    LEFT OUTER JOIN (SELECT 
        a.BIZ_DATE,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_TYPE = 'order'
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE_DUMP.ORDER_SETTLEMENT os
            INNER JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED'
                    AND od.ORDER_TYPE = 'order'
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00'), 7), INTERVAL 5 HOUR) AND DATE_ADD(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00'), INTERVAL - 7 DAY)
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00'), INTERVAL - 7 DAY)) a
    INNER JOIN (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE_DUMP.ORDER_SETTLEMENT os
    INNER JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00'), INTERVAL - 7 DAY)
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY BIZ_DATE) b ON a.BIZ_DATE = b.BIZ_DATE) p ON m.BIZ_DATE = p.BIZ_DATE) B ON A.BIZ_DATE = B.BIZ_DATE

        ]]>
					</content>
					<params>
						<param name="businessDate" displayName="Business Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
					</params>
				</report>
				<report id="1" name="Regional" executionType="SQL"
                                        GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT 
    A.BIZ_DATE,
    A.UNIT_REGION,
    A.NET_TICKETS,
    B.LWSD_NET_TICKETS,
    A.NET_APC,
    B.LWSD_NET_APC,
    A.NET_SALES,
    B.LWSD_NET_SALES,
	A.NET_DELIVERY_TICKETS,
    B.LWSD_NET_DELIVERY_TICKETS LWSD_NET_DELIVERY_TICKETS,
    A.NET_DELIVERY_APC,
    B.LWSD_NET_DELIVERY_APC,
    A.NET_DELIVERY_SALES,
    B.LWSD_NET_DELIVERY_SALES LWSD_NET_DELIVERY_SALES,
    (A.NET_TICKETS - A.NET_DELIVERY_TICKETS) DINE_IN_TICKETS,
    (B.LWSD_NET_TICKETS - B.LWSD_NET_DELIVERY_TICKETS) LWSD_DINE_IN_TICKETS,
	TRUNCATE((A.NET_SALES + A.GIFT_CARD_REDEMPTION - A.NET_DELIVERY_SALES  - A.GIFT_CARD_AMOUNT)/(A.NET_TICKETS - A.NET_DELIVERY_TICKETS),0) NET_DINE_IN_APC,
	TRUNCATE((B.LWSD_NET_SALES + B.LWSD_GIFT_CARD_REDEMPTION - B.LWSD_NET_DELIVERY_SALES - B.LWSD_GIFT_CARD_AMOUNT)/(B.LWSD_NET_TICKETS - B.LWSD_NET_DELIVERY_TICKETS),0) LWSD_NET_DINE_IN_APC,
    (A.NET_SALES - A.NET_DELIVERY_SALES) NET_DINE_IN_SALES,
    (B.LWSD_NET_SALES - B.LWSD_NET_DELIVERY_SALES) LWSD_NET_DINE_IN_SALES,
    A.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
    A.GIFT_CARD_AMOUNT,
    COALESCE(B.LWSD_GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
    A.MERCHANDISE_SALES,
    B.LWSD_MERCHANDISE_SALES
FROM
    (SELECT 
        m.BIZ_DATE,
            m.UNIT_REGION,
            (m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) NET_TICKETS,
            (m.NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0)) NET_SALES,
            TRUNCATE((m.NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0)) / GREATEST((m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) NET_APC,
            m.NET_DELIVERY_TICKETS,
            m.NET_DELIVERY_SALES,
            COALESCE(m.NET_DELIVERY_APC, 0) NET_DELIVERY_APC,
            m.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) GIFT_CARD_AMOUNT,
            COALESCE(n.MERCHANDISE_SALES, 0) MERCHANDISE_SALES,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) GIFT_CARD_TICKETS,
            j.COLD_PENETRATION
    FROM
        (SELECT 
        od.BIZ_DATE,
            od.UNIT_REGION,
            od.NET_TICKETS,
            od.NET_SALES,
            od.NET_DELIVERY_TICKETS,
            od.NET_DELIVERY_SALES,
            od.NET_DELIVERY_APC,
            od.CUSTOMER_CAPTURE_PERCENT_OF_TKTS
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            ud.UNIT_REGION,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS NET_SALES,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS NET_DELIVERY_APC,
            (SUM(CASE
                WHEN
                    od.CUSTOMER_ID > 5
                        AND od.TOTAL_AMOUNT <> '0.00'
                THEN
                    1
                ELSE 0
            END)) * 100 / (SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END)) AS CUSTOMER_CAPTURE_PERCENT_OF_TKTS
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')
    GROUP BY ud.UNIT_REGION) od) m
    LEFT OUTER JOIN (SELECT 
        qa.UNIT_REGION,
            TRUNCATE((SUM(COLD_QUANTITY) / SUM(TOTAL_QUANTITY)) * 100.0, 0) COLD_PENETRATION
    FROM
        (SELECT 
        ud.UNIT_REGION,
            CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END COLD_QUANTITY,
            CASE
                WHEN pd.PRODUCT_TYPE IN (5 , 6, 7, 10) THEN oi.QUANTITY
                ELSE 0
            END TOTAL_QUANTITY
    FROM
        KETTLE_DUMP.ORDER_ITEM oi
    INNER JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) qa
    GROUP BY qa.UNIT_REGION) j ON j.UNIT_REGION = m.UNIT_REGION
    LEFT OUTER JOIN (SELECT 
        a.UNIT_REGION,
            SUM(CASE
                WHEN a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT THEN 1
                ELSE 0
            END) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT,
            SUM(a.MERCHANDISE_SALES) MERCHANDISE_SALES
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
            ud.UNIT_REGION,
            CASE
                WHEN oi.PRODUCT_ID IN (1026 , 1027, 1048) THEN SUM(oi.QUANTITY * oi.PRICE)
                ELSE 0
            END GIFT_CARD_AMOUNT,
            CASE
                WHEN oi.PRODUCT_ID NOT IN (1026 , 1027, 1048) THEN SUM(oi.QUANTITY * oi.PRICE)
                ELSE 0
            END MERCHANDISE_SALES,
            od.SETTLED_AMOUNT
    FROM
        KETTLE_DUMP.ORDER_ITEM oi
    INNER JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')
            AND oi.PRODUCT_ID IN (1026 , 1027, 1048, 692, 700, 710, 720, 730, 1057)
    GROUP BY oi.ORDER_ID , od.UNIT_ID , ud.UNIT_REGION) a
    GROUP BY a.UNIT_REGION) n ON m.UNIT_REGION = n.UNIT_REGION
    LEFT OUTER JOIN (SELECT 
        a.UNIT_REGION,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        ud.UNIT_REGION,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE_DUMP.ORDER_SETTLEMENT os
            INNER JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                    WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
                    ELSE CURRENT_DATE
                END), INTERVAL 5 HOUR) AND CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')
    GROUP BY ud.UNIT_REGION) a
    INNER JOIN (SELECT 
        ud.UNIT_REGION, SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE_DUMP.ORDER_SETTLEMENT os
    INNER JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY ud.UNIT_REGION) b ON a.UNIT_REGION = b.UNIT_REGION) p ON m.UNIT_REGION = p.UNIT_REGION) A
        LEFT OUTER JOIN
    (SELECT 
        m.BIZ_DATE,
            m.UNIT_REGION,
            (m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) LWSD_NET_TICKETS,
            (m.LWSD_NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0)) LWSD_NET_SALES,
            TRUNCATE((m.LWSD_NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0)) / GREATEST((m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) LWSD_NET_APC,
            m.LWSD_NET_DELIVERY_TICKETS,
            m.LWSD_NET_DELIVERY_SALES,
            COALESCE(m.LWSD_NET_DELIVERY_APC, 0) LWSD_NET_DELIVERY_APC,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
            COALESCE(n.MERCHANDISE_SALES, 0) LWSD_MERCHANDISE_SALES,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) LWSD_GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) LWSD_GIFT_CARD_TICKETS,
            COALESCE(j.COLD_PENETRATION, 0) LWSD_COLD_PENETRATION
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            ud.UNIT_REGION,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS LWSD_NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS LWSD_NET_SALES,
            TRUNCATE(SUM(od.TAXABLE_AMOUNT) / SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END), 0) AS LWSD_NET_APC,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS LWSD_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS LWSD_NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS LWSD_NET_DELIVERY_APC
    FROM
        KETTLE_DUMP.ORDER_DETAIL od, KETTLE_MASTER_DUMP.UNIT_DETAIL ud
    WHERE
        od.UNIT_ID = ud.UNIT_ID
            AND od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00'), INTERVAL - 7 DAY)
    GROUP BY ud.UNIT_REGION) m
    LEFT OUTER JOIN (SELECT 
        qa.UNIT_REGION,
            TRUNCATE((SUM(COLD_QUANTITY) / SUM(TOTAL_QUANTITY)) * 100.0, 0) COLD_PENETRATION
    FROM
        (SELECT 
        ud.UNIT_REGION,
            CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END COLD_QUANTITY,
            CASE
                WHEN pd.PRODUCT_TYPE IN (5 , 6, 7, 10) THEN oi.QUANTITY
                ELSE 0
            END TOTAL_QUANTITY
    FROM
        KETTLE_DUMP.ORDER_ITEM oi
    INNER JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00'), INTERVAL - 7 DAY)) qa
    GROUP BY qa.UNIT_REGION) j ON j.UNIT_REGION = m.UNIT_REGION
    LEFT OUTER JOIN (SELECT 
        a.UNIT_REGION,
            SUM(CASE
                WHEN a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT THEN 1
                ELSE 0
            END) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT,
            SUM(a.MERCHANDISE_SALES) MERCHANDISE_SALES
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
            ud.UNIT_REGION,
            CASE
                WHEN oi.PRODUCT_ID IN (1026 , 1027, 1048) THEN SUM(oi.QUANTITY * oi.PRICE)
                ELSE 0
            END GIFT_CARD_AMOUNT,
            CASE
                WHEN oi.PRODUCT_ID NOT IN (1026 , 1027, 1048) THEN SUM(oi.QUANTITY * oi.PRICE)
                ELSE 0
            END MERCHANDISE_SALES,
            od.SETTLED_AMOUNT
    FROM
        KETTLE_DUMP.ORDER_ITEM oi
    INNER JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00'), INTERVAL - 7 DAY)
            AND oi.PRODUCT_ID IN (1026 , 1027, 1048, 692, 700, 710, 720, 730, 1057)
    GROUP BY oi.ORDER_ID , od.UNIT_ID , ud.UNIT_REGION) a
    GROUP BY a.UNIT_REGION) n ON m.UNIT_REGION = n.UNIT_REGION
    LEFT OUTER JOIN (SELECT 
        a.UNIT_REGION,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        ud.UNIT_REGION,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE_DUMP.ORDER_SETTLEMENT os
            INNER JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
                    WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
                    ELSE CURRENT_DATE
                END), 7), INTERVAL 5 HOUR) AND DATE_ADD(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00'), INTERVAL - 7 DAY)
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00'), INTERVAL - 7 DAY)
    GROUP BY ud.UNIT_REGION) a
    INNER JOIN (SELECT 
        ud.UNIT_REGION, SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE_DUMP.ORDER_SETTLEMENT os
    INNER JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00'), INTERVAL - 7 DAY)
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY ud.UNIT_REGION) b ON a.UNIT_REGION = b.UNIT_REGION) p ON m.UNIT_REGION = p.UNIT_REGION) B ON A.BIZ_DATE = B.BIZ_DATE
        AND A.UNIT_REGION = B.UNIT_REGION
GROUP BY A.UNIT_REGION

        ]]>
					</content>
					<params>
						<param name="businessDate" displayName="Business Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
					</params>
				</report>
				<report id="1" name="Area Managers" executionType="SQL"
                                        GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT 
    A.BIZ_DATE,
	UCASE(ed.EMP_NAME) AREA_MANAGER,
    A.NET_TICKETS,
    B.LWSD_NET_TICKETS,
    A.NET_APC,
    B.LWSD_NET_APC,
    A.NET_SALES,
    B.LWSD_NET_SALES,
    A.NET_DELIVERY_TICKETS,
    B.LWSD_NET_DELIVERY_TICKETS LWSD_NET_DELIVERY_TICKETS,
    A.NET_DELIVERY_APC,
    B.LWSD_NET_DELIVERY_APC,
    A.NET_DELIVERY_SALES,
    B.LWSD_NET_DELIVERY_SALES LWSD_NET_DELIVERY_SALES,
	(A.NET_TICKETS - A.NET_DELIVERY_TICKETS) DINE_IN_TICKETS,
    (B.LWSD_NET_TICKETS - B.LWSD_NET_DELIVERY_TICKETS) LWSD_DINE_IN_TICKETS,
	TRUNCATE((A.NET_SALES + A.GIFT_CARD_REDEMPTION - A.NET_DELIVERY_SALES  - A.GIFT_CARD_AMOUNT)/(A.NET_TICKETS - A.NET_DELIVERY_TICKETS),0) NET_DINE_IN_APC,
	TRUNCATE((B.LWSD_NET_SALES + B.LWSD_GIFT_CARD_REDEMPTION - B.LWSD_NET_DELIVERY_SALES - B.LWSD_GIFT_CARD_AMOUNT)/(B.LWSD_NET_TICKETS - B.LWSD_NET_DELIVERY_TICKETS),0) LWSD_NET_DINE_IN_APC,
    (A.NET_SALES - A.NET_DELIVERY_SALES) NET_DINE_IN_SALES,
    (B.LWSD_NET_SALES - B.LWSD_NET_DELIVERY_SALES) LWSD_NET_DINE_IN_SALES,
    A.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
    A.GIFT_CARD_AMOUNT,
    COALESCE(B.LWSD_GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
    A.MERCHANDISE_SALES,
    B.LWSD_MERCHANDISE_SALES
FROM
    (SELECT 
        m.BIZ_DATE,
            m.UNIT_MANAGER,
            (m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) NET_TICKETS,
            (m.NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0)) NET_SALES,
            TRUNCATE((m.NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0)) / GREATEST((m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) NET_APC,
            m.NET_DELIVERY_TICKETS,
            m.NET_DELIVERY_SALES,
            COALESCE(m.NET_DELIVERY_APC, 0) NET_DELIVERY_APC,
            m.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) GIFT_CARD_AMOUNT,
            COALESCE(n.MERCHANDISE_SALES, 0) MERCHANDISE_SALES,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) GIFT_CARD_TICKETS,
            j.COLD_PENETRATION
    FROM
        (SELECT 
        od.BIZ_DATE,
            od.UNIT_MANAGER,
            od.NET_TICKETS,
            od.NET_SALES,
            od.NET_DELIVERY_TICKETS,
            od.NET_DELIVERY_SALES,
            od.NET_DELIVERY_APC,
            od.CUSTOMER_CAPTURE_PERCENT_OF_TKTS
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            ud.UNIT_MANAGER,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS NET_SALES,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS NET_DELIVERY_APC,
            (SUM(CASE
                WHEN
                    od.CUSTOMER_ID > 5
                        AND od.TOTAL_AMOUNT <> '0.00'
                THEN
                    1
                ELSE 0
            END)) * 100 / (SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END)) AS CUSTOMER_CAPTURE_PERCENT_OF_TKTS
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')
    GROUP BY ud.UNIT_MANAGER) od) m
    LEFT OUTER JOIN (SELECT 
        qa.UNIT_MANAGER,
            TRUNCATE((SUM(COLD_QUANTITY) / SUM(TOTAL_QUANTITY)) * 100.0, 0) COLD_PENETRATION
    FROM
        (SELECT 
        ud.UNIT_MANAGER,
            CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END COLD_QUANTITY,
            CASE
                WHEN pd.PRODUCT_TYPE IN (5 , 6, 7, 10) THEN oi.QUANTITY
                ELSE 0
            END TOTAL_QUANTITY
    FROM
        KETTLE_DUMP.ORDER_ITEM oi
    INNER JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) qa
    GROUP BY qa.UNIT_MANAGER) j ON j.UNIT_MANAGER = m.UNIT_MANAGER
    LEFT OUTER JOIN (SELECT 
        a.UNIT_MANAGER,
            SUM(CASE
                WHEN a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT THEN 1
                ELSE 0
            END) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT,
            SUM(a.MERCHANDISE_SALES) MERCHANDISE_SALES
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
            ud.UNIT_MANAGER,
            CASE
                WHEN oi.PRODUCT_ID IN (1026 , 1027, 1048) THEN SUM(oi.QUANTITY * oi.PRICE)
                ELSE 0
            END GIFT_CARD_AMOUNT,
            CASE
                WHEN oi.PRODUCT_ID NOT IN (1026 , 1027, 1048) THEN SUM(oi.QUANTITY * oi.PRICE)
                ELSE 0
            END MERCHANDISE_SALES,
            od.SETTLED_AMOUNT
    FROM
        KETTLE_DUMP.ORDER_ITEM oi
    INNER JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')
            AND oi.PRODUCT_ID IN (1026 , 1027, 1048, 692, 700, 710, 720, 730, 1057)
    GROUP BY oi.ORDER_ID , od.UNIT_ID , ud.UNIT_MANAGER) a
    GROUP BY a.UNIT_MANAGER) n ON m.UNIT_MANAGER = n.UNIT_MANAGER
    LEFT OUTER JOIN (SELECT 
        a.UNIT_MANAGER,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        ud.UNIT_MANAGER,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE_DUMP.ORDER_SETTLEMENT os
            INNER JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                    WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
                    ELSE CURRENT_DATE
                END), INTERVAL 5 HOUR) AND CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')
    GROUP BY ud.UNIT_MANAGER) a
    INNER JOIN (SELECT 
        ud.UNIT_MANAGER, SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE_DUMP.ORDER_SETTLEMENT os
    INNER JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY ud.UNIT_MANAGER) b ON a.UNIT_MANAGER = b.UNIT_MANAGER) p ON m.UNIT_MANAGER = p.UNIT_MANAGER) A
        LEFT OUTER JOIN
    (SELECT 
        m.BIZ_DATE,
            m.UNIT_MANAGER,
            (m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) LWSD_NET_TICKETS,
            (m.LWSD_NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0)) LWSD_NET_SALES,
            TRUNCATE((m.LWSD_NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0)) / GREATEST((m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) LWSD_NET_APC,
            m.LWSD_NET_DELIVERY_TICKETS,
            m.LWSD_NET_DELIVERY_SALES,
            COALESCE(m.LWSD_NET_DELIVERY_APC, 0) LWSD_NET_DELIVERY_APC,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
            COALESCE(n.MERCHANDISE_SALES, 0) LWSD_MERCHANDISE_SALES,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) LWSD_GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) LWSD_GIFT_CARD_TICKETS,
            COALESCE(j.COLD_PENETRATION, 0) LWSD_COLD_PENETRATION
    FROM
        (SELECT 
        DATE_FORMAT(CURRENT_DATE, '%d-%m-%Y') BIZ_DATE,
            ud.UNIT_MANAGER,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS LWSD_NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS LWSD_NET_SALES,
            TRUNCATE(SUM(od.TAXABLE_AMOUNT) / SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END), 0) AS LWSD_NET_APC,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS LWSD_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS LWSD_NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS LWSD_NET_DELIVERY_APC
    FROM
        KETTLE_DUMP.ORDER_DETAIL od, KETTLE_MASTER_DUMP.UNIT_DETAIL ud
    WHERE
        od.UNIT_ID = ud.UNIT_ID
            AND od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00'), INTERVAL - 7 DAY)
    GROUP BY ud.UNIT_MANAGER) m
    LEFT OUTER JOIN (SELECT 
        qa.UNIT_MANAGER,
            TRUNCATE((SUM(COLD_QUANTITY) / SUM(TOTAL_QUANTITY)) * 100.0, 0) COLD_PENETRATION
    FROM
        (SELECT 
        ud.UNIT_MANAGER,
            CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END COLD_QUANTITY,
            CASE
                WHEN pd.PRODUCT_TYPE IN (5 , 6, 7, 10) THEN oi.QUANTITY
                ELSE 0
            END TOTAL_QUANTITY
    FROM
        KETTLE_DUMP.ORDER_ITEM oi
    INNER JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00'), INTERVAL - 7 DAY)) qa
    GROUP BY qa.UNIT_MANAGER) j ON j.UNIT_MANAGER = m.UNIT_MANAGER
    LEFT OUTER JOIN (SELECT 
        a.UNIT_MANAGER,
            SUM(CASE
                WHEN a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT THEN 1
                ELSE 0
            END) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT,
            SUM(a.MERCHANDISE_SALES) MERCHANDISE_SALES
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
            ud.UNIT_MANAGER,
            CASE
                WHEN oi.PRODUCT_ID IN (1026 , 1027, 1048) THEN SUM(oi.QUANTITY * oi.PRICE)
                ELSE 0
            END GIFT_CARD_AMOUNT,
            CASE
                WHEN oi.PRODUCT_ID NOT IN (1026 , 1027, 1048) THEN SUM(oi.QUANTITY * oi.PRICE)
                ELSE 0
            END MERCHANDISE_SALES,
            od.SETTLED_AMOUNT
    FROM
        KETTLE_DUMP.ORDER_ITEM oi
    INNER JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00'), INTERVAL - 7 DAY)
            AND oi.PRODUCT_ID IN (1026 , 1027, 1048, 692, 700, 710, 720, 730, 1057)
    GROUP BY oi.ORDER_ID , od.UNIT_ID , ud.UNIT_MANAGER) a
    GROUP BY a.UNIT_MANAGER) n ON m.UNIT_MANAGER = n.UNIT_MANAGER
    LEFT OUTER JOIN (SELECT 
        a.UNIT_MANAGER,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        ud.UNIT_MANAGER,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE_DUMP.ORDER_SETTLEMENT os
            INNER JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
                    WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
                    ELSE CURRENT_DATE
                END), 7), INTERVAL 5 HOUR) AND DATE_ADD(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00'), INTERVAL - 7 DAY)
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00'), INTERVAL - 7 DAY)
    GROUP BY ud.UNIT_MANAGER) a
    INNER JOIN (SELECT 
        ud.UNIT_MANAGER, SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE_DUMP.ORDER_SETTLEMENT os
    INNER JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00'), INTERVAL - 7 DAY)
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY ud.UNIT_MANAGER) b ON a.UNIT_MANAGER = b.UNIT_MANAGER) p ON m.UNIT_MANAGER = p.UNIT_MANAGER) B ON A.BIZ_DATE = B.BIZ_DATE
        AND A.UNIT_MANAGER = B.UNIT_MANAGER
        INNER JOIN
    KETTLE_MASTER_DUMP.EMPLOYEE_DETAIL ed ON A.UNIT_MANAGER = ed.EMP_ID
GROUP BY A.UNIT_MANAGER
ORDER BY AREA_MANAGER

        ]]>
					</content>
					<params>
						<param name="businessDate" displayName="Business Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
					</params>
				</report>
				<report id="2" name="Cafes" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[

SELECT 
    ud.UNIT_NAME,
    A.NET_TICKETS,
    B.LWSD_NET_TICKETS,
    A.NET_APC,
    B.LWSD_NET_APC,
    A.NET_SALES,
    B.LWSD_NET_SALES,
	A.NET_DELIVERY_TICKETS,
    COALESCE(B.LWSD_NET_DELIVERY_TICKETS, 0) LWSD_NET_DELIVERY_TICKETS,
    A.NET_DELIVERY_APC,
    COALESCE(B.LWSD_NET_DELIVERY_APC, 0) LWSD_NET_DELIVERY_APC,
    A.NET_DELIVERY_SALES,
    COALESCE(B.LWSD_NET_DELIVERY_SALES, 0) LWSD_NET_DELIVERY_SALES,
    (A.NET_TICKETS - A.NET_DELIVERY_TICKETS) DINE_IN_TICKETS,
    (B.LWSD_NET_TICKETS - COALESCE(B.LWSD_NET_DELIVERY_TICKETS, 0)) LWSD_DINE_IN_TICKETS,
	TRUNCATE((A.NET_SALES + A.GIFT_CARD_REDEMPTION - A.NET_DELIVERY_SALES  - A.GIFT_CARD_AMOUNT)/(A.NET_TICKETS - A.NET_DELIVERY_TICKETS),0) NET_DINE_IN_APC,
	COALESCE(TRUNCATE((B.LWSD_NET_SALES + B.LWSD_GIFT_CARD_REDEMPTION - COALESCE(B.LWSD_NET_DELIVERY_SALES, 0) - COALESCE(B.LWSD_GIFT_CARD_AMOUNT,0))/(B.LWSD_NET_TICKETS - COALESCE(B.LWSD_NET_DELIVERY_TICKETS, 0)),0),0) LWSD_NET_DINE_IN_APC,
    (A.NET_SALES - A.NET_DELIVERY_SALES) NET_DINE_IN_SALES,
    (B.LWSD_NET_SALES - COALESCE(B.LWSD_NET_DELIVERY_SALES, 0)) LWSD_NET_DINE_IN_SALES,
    A.COLD_PENETRATION,
    B.LWSD_COLD_PENETRATION,
    A.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
    A.GIFT_CARD_AMOUNT,
    COALESCE(B.LWSD_GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
    A.GIFT_CARD_REDEMPTION,
    COALESCE(B.LWSD_GIFT_CARD_REDEMPTION, 0) LWSD_GIFT_CARD_REDEMPTION,
    A.MERCHANDISE_SALES,
    B.LWSD_MERCHANDISE_SALES,
	UCASE(ed.EMP_NAME) AREA_MANAGER
FROM
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud
    INNER JOIN
    KETTLE_MASTER_DUMP.EMPLOYEE_DETAIL ed ON ud.UNIT_MANAGER = ed.EMP_ID
        LEFT OUTER JOIN
    (SELECT 
        m.UNIT_ID,
            (m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) NET_TICKETS,
            (m.NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0)) NET_SALES,
            TRUNCATE((m.NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0)) / GREATEST((m.NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) NET_APC,
            m.NET_DELIVERY_TICKETS,
            m.NET_DELIVERY_SALES,
            COALESCE(m.NET_DELIVERY_APC, 0) NET_DELIVERY_APC,
            m.CUSTOMER_CAPTURE_PERCENT_OF_TKTS,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) GIFT_CARD_AMOUNT,
            COALESCE(n.MERCHANDISE_SALES, 0) MERCHANDISE_SALES,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) GIFT_CARD_TICKETS,
            j.COLD_PENETRATION
    FROM
        (SELECT 
        ud.UNIT_ID,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS NET_SALES,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS NET_DELIVERY_APC,
            (SUM(CASE
                WHEN
                    od.CUSTOMER_ID > 5
                        AND od.TOTAL_AMOUNT <> '0.00'
                THEN
                    1
                ELSE 0
            END)) * 100 / (SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END)) AS CUSTOMER_CAPTURE_PERCENT_OF_TKTS
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')
    GROUP BY od.UNIT_ID) m
    LEFT OUTER JOIN (SELECT 
        qa.UNIT_ID,
            TRUNCATE((SUM(COLD_QUANTITY) / SUM(TOTAL_QUANTITY)) * 100.0, 0) COLD_PENETRATION
    FROM
        (SELECT 
        od.UNIT_ID,
            CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END COLD_QUANTITY,
            CASE
                WHEN pd.PRODUCT_TYPE IN (5 , 6, 7, 10) THEN oi.QUANTITY
                ELSE 0
            END TOTAL_QUANTITY
    FROM
        KETTLE_DUMP.ORDER_ITEM oi
    INNER JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) qa
    GROUP BY qa.UNIT_ID) j ON m.UNIT_ID = j.UNIT_ID
    LEFT OUTER JOIN (SELECT 
        a.UNIT_ID,
            SUM(CASE
                WHEN a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT THEN 1
                ELSE 0
            END) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT,
            SUM(a.MERCHANDISE_SALES) MERCHANDISE_SALES
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
            CASE
                WHEN oi.PRODUCT_ID IN (1026 , 1027, 1048) THEN SUM(oi.QUANTITY * oi.PRICE)
                ELSE 0
            END GIFT_CARD_AMOUNT,
            CASE
                WHEN oi.PRODUCT_ID NOT IN (1026 , 1027, 1048) THEN SUM(oi.QUANTITY * oi.PRICE)
                ELSE 0
            END MERCHANDISE_SALES,
            od.SETTLED_AMOUNT
    FROM
        KETTLE_DUMP.ORDER_ITEM oi
    INNER JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')
            AND oi.PRODUCT_ID IN (1026 , 1027, 1048, 692, 700, 710, 720, 730, 1057)
    GROUP BY oi.ORDER_ID , od.UNIT_ID) a
    GROUP BY a.UNIT_ID) n ON m.UNIT_ID = n.UNIT_ID
    LEFT OUTER JOIN (SELECT 
        a.UNIT_ID,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        od.UNIT_ID,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE_DUMP.ORDER_SETTLEMENT os
            INNER JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
                    WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
                    ELSE CURRENT_DATE
                END), INTERVAL 5 HOUR) AND CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')
    GROUP BY od.UNIT_ID) a
    INNER JOIN (SELECT 
        od.UNIT_ID, SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE_DUMP.ORDER_SETTLEMENT os
    INNER JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD((CASE
            WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
            ELSE CURRENT_DATE
        END), INTERVAL 5 HOUR) AND CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY od.UNIT_ID) b ON a.UNIT_ID = b.UNIT_ID) p ON m.UNIT_ID = p.UNIT_ID) A ON A.UNIT_ID = ud.UNIT_ID
        LEFT OUTER JOIN
    (SELECT 
        m.UNIT_ID,
            (m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)) LWSD_NET_TICKETS,
            (m.LWSD_NET_SALES - COALESCE(p.GIFT_CARD_REDEMPTION, 0)) LWSD_NET_SALES,
            TRUNCATE((m.LWSD_NET_SALES - COALESCE(n.GIFT_CARD_AMOUNT, 0)) / GREATEST((m.LWSD_NET_TICKETS - COALESCE(n.GIFT_CARD_TICKETS, 0)), 1), 0) LWSD_NET_APC,
            m.LWSD_NET_DELIVERY_TICKETS,
            m.LWSD_NET_DELIVERY_SALES,
            COALESCE(m.LWSD_NET_DELIVERY_APC, 0) LWSD_NET_DELIVERY_APC,
            COALESCE(n.GIFT_CARD_AMOUNT, 0) LWSD_GIFT_CARD_AMOUNT,
            COALESCE(n.MERCHANDISE_SALES, 0) LWSD_MERCHANDISE_SALES,
            COALESCE(p.GIFT_CARD_REDEMPTION, 0) LWSD_GIFT_CARD_REDEMPTION,
            COALESCE(n.GIFT_CARD_TICKETS, 0) LWSD_GIFT_CARD_TICKETS,
            COALESCE(j.COLD_PENETRATION, 0) LWSD_COLD_PENETRATION
    FROM
        (SELECT 
        ud.UNIT_ID,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS LWSD_NET_TICKETS,
            SUM(od.TAXABLE_AMOUNT) AS LWSD_NET_SALES,
            TRUNCATE(SUM(od.TAXABLE_AMOUNT) / SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END), 0) AS LWSD_NET_APC,
            SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END) AS LWSD_NET_DELIVERY_TICKETS,
            SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) AS LWSD_NET_DELIVERY_SALES,
            TRUNCATE(SUM(CASE
                WHEN od.ORDER_SOURCE = 'COD' THEN od.TAXABLE_AMOUNT
                ELSE 0
            END) / SUM(CASE
                WHEN
                    od.TOTAL_AMOUNT <> '0.00'
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1
                ELSE 0
            END), 0) AS LWSD_NET_DELIVERY_APC
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00'), INTERVAL - 7 DAY)
    GROUP BY od.UNIT_ID) m
    LEFT OUTER JOIN (SELECT 
        qa.UNIT_ID,
            TRUNCATE((SUM(COLD_QUANTITY) / SUM(TOTAL_QUANTITY)) * 100.0, 0) COLD_PENETRATION
    FROM
        (SELECT 
        od.UNIT_ID,
            CASE
                WHEN pd.PRODUCT_TYPE = 6 THEN oi.QUANTITY
                ELSE 0
            END COLD_QUANTITY,
            CASE
                WHEN pd.PRODUCT_TYPE IN (5 , 6, 7, 10) THEN oi.QUANTITY
                ELSE 0
            END TOTAL_QUANTITY
    FROM
        KETTLE_DUMP.ORDER_ITEM oi
    INNER JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00'), INTERVAL - 7 DAY)) qa
    GROUP BY qa.UNIT_ID) j ON m.UNIT_ID = j.UNIT_ID
    LEFT OUTER JOIN (SELECT 
        a.UNIT_ID,
            SUM(CASE
                WHEN a.GIFT_CARD_AMOUNT = a.SETTLED_AMOUNT THEN 1
                ELSE 0
            END) GIFT_CARD_TICKETS,
            SUM(a.GIFT_CARD_AMOUNT) GIFT_CARD_AMOUNT,
            SUM(a.MERCHANDISE_SALES) MERCHANDISE_SALES
    FROM
        (SELECT 
        oi.ORDER_ID,
            od.UNIT_ID,
            CASE
                WHEN oi.PRODUCT_ID IN (1026 , 1027, 1048) THEN SUM(oi.QUANTITY * oi.PRICE)
                ELSE 0
            END GIFT_CARD_AMOUNT,
            CASE
                WHEN oi.PRODUCT_ID NOT IN (1026 , 1027, 1048) THEN SUM(oi.QUANTITY * oi.PRICE)
                ELSE 0
            END MERCHANDISE_SALES,
            od.SETTLED_AMOUNT
    FROM
        KETTLE_DUMP.ORDER_ITEM oi
    INNER JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00'), INTERVAL - 7 DAY)
            AND oi.PRODUCT_ID IN (1026 , 1027, 1048, 692, 700, 710, 720, 730, 1057)
    GROUP BY oi.ORDER_ID , od.UNIT_ID) a
    GROUP BY a.UNIT_ID) n ON m.UNIT_ID = n.UNIT_ID
    LEFT OUTER JOIN (SELECT 
        a.UNIT_ID,
            TRUNCATE(a.TAXABLE_AMOUNT / a.SETTLED_AMOUNT * b.GIFT_CARD_REDEMPTION, 2) GIFT_CARD_REDEMPTION
    FROM
        (SELECT 
        od.UNIT_ID,
            SUM(TAXABLE_AMOUNT) TAXABLE_AMOUNT,
            SUM(od.SETTLED_AMOUNT) SETTLED_AMOUNT
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
            AND od.ORDER_ID IN (SELECT DISTINCT
                od.ORDER_ID
            FROM
                KETTLE_DUMP.ORDER_SETTLEMENT os
            INNER JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
            WHERE
                od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
                    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
                    WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
                    ELSE CURRENT_DATE
                END), 7), INTERVAL 5 HOUR) AND DATE_ADD(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00'), INTERVAL - 7 DAY)
                    AND os.PAYMENT_MODE_ID = 10)
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00'), INTERVAL - 7 DAY)
    GROUP BY od.UNIT_ID) a
    INNER JOIN (SELECT 
        od.UNIT_ID, SUM(os.AMOUNT_PAID) GIFT_CARD_REDEMPTION
    FROM
        KETTLE_DUMP.ORDER_SETTLEMENT os
    INNER JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = os.ORDER_ID
    WHERE
        od.ORDER_STATUS <> 'CANCELLED' AND od.ORDER_TYPE = 'order'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(SUBDATE((CASE
            WHEN HOUR(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')) <= 5 THEN SUBDATE(DATE(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00')), 1)
            ELSE CURRENT_DATE
        END), 7), INTERVAL 5 HOUR) AND DATE_ADD(CONCAT(DATE_ADD(:businessDate, INTERVAL 1 DAY), ' 04:55:00'), INTERVAL - 7 DAY)
            AND os.PAYMENT_MODE_ID = 10
    GROUP BY od.UNIT_ID) b ON a.UNIT_ID = b.UNIT_ID) p ON m.UNIT_ID = p.UNIT_ID) B ON B.UNIT_ID = ud.UNIT_ID
WHERE
    A.NET_TICKETS IS NOT NULL
        OR B.LWSD_NET_TICKETS IS NOT NULL
ORDER BY AREA_MANAGER , ud.UNIT_NAME

                                                ]]>
					</content>
					<params>
						<param name="businessDate" displayName="Business Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
					</params>
				</report>
			</reports>
		</category>
	</categories>
</ReportCategories>
