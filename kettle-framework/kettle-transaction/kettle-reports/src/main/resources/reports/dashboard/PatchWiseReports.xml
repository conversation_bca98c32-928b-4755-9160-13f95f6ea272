<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<ReportCategories xmlns="http://www.w3schools.com">
	<categories>
		<category name="Patch Wise Report" type="Kettle" accessCode="Kettle">
			<reports>
				<report name="Goods Received In Report Accross Units For Date Range"
					executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
                            SELECT 
    T.*
FROM
    (SELECT 
        g.GOODS_RECEIVED_ID,
            gi.GOODS_RECEIVED_ITEM_ID,
            gi.TRANSFER_ORDER_ITEM_ID,
            gi.REQUEST_ORDER_ITEM_ID,
            u.UNIT_NAME AS TRANSFERRING_UNIT,
            ux.UNIT_NAME AS REQUESTING_RECEIVING_UNIT,
            p.PRODUCT_NAME,
            gi.SKU_NAME,
            ri.REQUESTED_QUANTITY,
            gi.TRANSFERRED_QUANTITY,
            gi.RECEIVED_QUANTITY,
            (gi.RECEIVED_QUANTITY * s.NEGOTIATED_UNIT_PRICE) AS COST,
            gi.UNIT_OF_MEASURE,
            s.NEGOTIATED_UNIT_PRICE AS UNIT_PRICE,
            c.CATEGORY_NAME,
            sc.SUB_CATEGORY_NAME,
            r.GENERATION_TIME AS REQUESTING_TIME,
            t.GENERATION_TIME AS TRANSFER_TIME,
            g.LAST_UPDATE_TIME AS RECEIVING_TIME,
            vd.ENTITY_NAME,
            g.COMMENT AS RECEIVING_COMMENT,
            r.COMMENT AS REQUESTING_COMMENT,
            ku.UNIT_MANAGER
    FROM
        KETTLE_SCM_DUMP.GOODS_RECEIVED_ITEM gi
    INNER JOIN KETTLE_SCM_DUMP.GOODS_RECEIVED g ON gi.GOODS_RECEIVED_ID = g.GOODS_RECEIVED_ID
        AND g.GOODS_RECEIVED_STATUS = 'SETTLED'
        AND DATE(g.LAST_UPDATE_TIME) >= :startDate
        AND DATE(g.LAST_UPDATE_TIME) <= :endDate
    INNER JOIN KETTLE_SCM_DUMP.SKU_DEFINITION s ON s.SKU_ID = gi.SKU_ID
    INNER JOIN KETTLE_SCM_DUMP.PRODUCT_DEFINITION p ON p.PRODUCT_ID = s.LINKED_PRODUCT_ID
    INNER JOIN KETTLE_SCM_DUMP.REQUEST_ORDER_ITEM ri ON ri.REQUEST_ORDER_ITEM_ID = gi.REQUEST_ORDER_ITEM_ID
    INNER JOIN KETTLE_SCM_DUMP.UNIT_DETAIL u ON u.UNIT_ID = g.GENERATION_UNIT_ID
    INNER JOIN KETTLE_SCM_DUMP.UNIT_DETAIL ux ON ux.UNIT_ID = g.GENERATED_FOR_UNIT_ID
    INNER JOIN KETTLE_SCM_DUMP.CATEGORY_DEFINITION c ON c.CATEGORY_ID = p.CATEGORY_ID
    INNER JOIN KETTLE_SCM_DUMP.SUB_CATEGORY_DEFINITION sc ON sc.SUB_CATEGORY_ID = p.SUB_CATEGORY_ID
    INNER JOIN KETTLE_SCM_DUMP.REQUEST_ORDER r ON r.REQUEST_ORDER_ID = g.REQUEST_ORDER_ID
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ku ON g.GENERATED_FOR_UNIT_ID=ku.UNIT_ID
    INNER JOIN KETTLE_SCM_DUMP.TRANSFER_ORDER t ON t.TRANSFER_ORDER_ID = g.TRANSFER_ORDER_ID
    LEFT JOIN KETTLE_SCM_DUMP.VENDOR_DETAIL_DATA vd ON vd.VENDOR_ID = ri.VENDOR_ID) T where T.UNIT_MANAGER=:unitManagerId
                        ]]>
					</content>
					<params>
						<param name="startDate" displayName="Start Date" dataType="DATE"
							format="yyyy-MM-dd" />
						<param name="endDate" displayName="End Date" dataType="DATE"
							format="yyyy-MM-dd" />
						<param name="unitManagerId" displayName="Unit Manager Id"
							dataType="INTEGER" />
					</params>

				</report>
				<report
					name="Goods Received In Report Accross Units For Cosumables Non Marketing For Date Range"
					executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
                           SELECT T.* FROM (SELECT
                            g.GOODS_RECEIVED_ID,
                            gi.GOODS_RECEIVED_ITEM_ID,
                            gi.TRANSFER_ORDER_ITEM_ID,
                            gi.REQUEST_ORDER_ITEM_ID,
                            u.UNIT_NAME AS TRANSFERRING_UNIT,
                            ux.UNIT_NAME AS REQUESTING_RECEIVING_UNIT,
                            p.PRODUCT_NAME,
                            gi.SKU_NAME,
                            ri.REQUESTED_QUANTITY,
                            gi.TRANSFERRED_QUANTITY,
                            gi.RECEIVED_QUANTITY,
                            (gi.RECEIVED_QUANTITY * s.NEGOTIATED_UNIT_PRICE) AS COST,
                            gi.UNIT_OF_MEASURE,
                            s.NEGOTIATED_UNIT_PRICE AS UNIT_PRICE,
                            c.CATEGORY_NAME,
                            sc.SUB_CATEGORY_NAME,
                            r.GENERATION_TIME AS REQUESTING_TIME,
                            t.GENERATION_TIME AS TRANSFER_TIME,
                            g.LAST_UPDATE_TIME AS RECEIVING_TIME,
                            vd.VENDOR_NAME,
                            g.COMMENT AS RECEIVING_COMMENT,
                            r.COMMENT AS REQUESTING_COMMENT
                        FROM
                            KETTLE_SCM_DUMP.GOODS_RECEIVED_ITEM gi
                                INNER JOIN
                            KETTLE_SCM_DUMP.GOODS_RECEIVED g ON gi.GOODS_RECEIVED_ID = g.GOODS_RECEIVED_ID
                                AND g.GOODS_RECEIVED_STATUS = 'SETTLED'
                                AND DATE(g.LAST_UPDATE_TIME) >= :startDate
                                AND DATE(g.LAST_UPDATE_TIME) <= :endDate
                                INNER JOIN
                            KETTLE_SCM_DUMP.SKU_DEFINITION s ON s.SKU_ID = gi.SKU_ID
                                INNER JOIN
                            KETTLE_SCM_DUMP.PRODUCT_DEFINITION p ON p.PRODUCT_ID = s.LINKED_PRODUCT_ID
                                INNER JOIN
                            KETTLE_SCM_DUMP.REQUEST_ORDER_ITEM ri ON ri.REQUEST_ORDER_ITEM_ID = gi.REQUEST_ORDER_ITEM_ID
                                INNER JOIN
                            KETTLE_SCM_DUMP.UNIT_DETAIL u ON u.UNIT_ID = g.GENERATION_UNIT_ID
                                INNER JOIN
                            KETTLE_SCM_DUMP.UNIT_DETAIL ux ON ux.UNIT_ID = g.GENERATED_FOR_UNIT_ID
                                INNER JOIN
                            KETTLE_SCM_DUMP.CATEGORY_DEFINITION c ON c.CATEGORY_ID = p.CATEGORY_ID
                                INNER JOIN
                            KETTLE_SCM_DUMP.SUB_CATEGORY_DEFINITION sc ON sc.SUB_CATEGORY_ID = p.SUB_CATEGORY_ID
                                INNER JOIN
                            KETTLE_SCM_DUMP.REQUEST_ORDER r ON r.REQUEST_ORDER_ID = g.REQUEST_ORDER_ID
                                INNER JOIN
                            KETTLE_SCM_DUMP.TRANSFER_ORDER t ON t.TRANSFER_ORDER_ID = g.TRANSFER_ORDER_ID
                            INNER JOIN
  							KETTLE_MASTER_DUMP.UNIT_DETAIL ku ON ku.UNIT_ID=g.GENERATED_FOR_UNIT_ID and ku.UNIT_MANAGER = :unitManagerId
                            LEFT JOIN KETTLE_SCM_DUMP.VENDOR_DETAIL vd ON vd.VENDOR_ID=ri.VENDOR_ID) T
			WHERE T.CATEGORY_NAME = 'Consumables' 
			AND T.SUB_CATEGORY_NAME <> 'Marketing'
                        ]]>
					</content>
					<params>
						<param name="startDate" displayName="Start Date" dataType="DATE"
							format="yyyy-MM-dd" />
						<param name="endDate" displayName="End Date" dataType="DATE"
							format="yyyy-MM-dd" />
						<param name="unitManagerId" displayName="Unit Manager Id"
							dataType="INTEGER" />
					</params>
				</report>

				<report name="Wastage Report Accross Units For Date Range"
					executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
    wid.PRODUCT_ID,
    pd.PRODUCT_NAME,
    cd.CATEGORY_NAME,
    scd.SUB_CATEGORY_NAME,
    ud.UNIT_NAME,
    utm.UNIT_TYPE,
    ld.CITY,
    wid.PRICE,
    wid.QUANTITY,
    wid.COST AS COST,
    pd.UNIT_OF_MEASURE,
    we.BUSINESS_DATE,
    we.GENERATION_TIME,
    we.GENERATED_BY,
    wid.COMMENT,
    CASE
        WHEN ctm.COMMENT_TYPE IS NOT NULL THEN ctm.COMMENT_TYPE
        ELSE 'Cafe Wastage'
    END AS COMMENT_TYPE
FROM
    KETTLE_SCM_DUMP.WASTAGE_ITEM_DATA wid
        INNER JOIN
    KETTLE_SCM_DUMP.WASTAGE_EVENT we ON wid.WASTAGE_ID = we.WASTAGE_ID
        INNER JOIN
    KETTLE_SCM_DUMP.PRODUCT_DEFINITION pd ON wid.PRODUCT_ID = pd.PRODUCT_ID
        INNER JOIN
    KETTLE_SCM_DUMP.CATEGORY_DEFINITION cd ON pd.CATEGORY_ID = cd.CATEGORY_ID
        INNER JOIN
    KETTLE_SCM_DUMP.SUB_CATEGORY_DEFINITION scd ON pd.SUB_CATEGORY_ID = scd.SUB_CATEGORY_ID
        INNER JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON we.UNIT_ID = ud.UNIT_ID
        INNER JOIN
    KETTLE_SCM_DUMP.UNIT_TYPE_MAPPING utm ON ud.UNIT_CATEGORY = utm.UNIT_CATEGORY
        INNER JOIN
    KETTLE_MASTER_DUMP.LOCATION_DETAIL ld ON ud.LOCATION_DETAIL_ID = ld.LOCATION_ID
        INNER JOIN
    KETTLE_SCM_DUMP.COMMENT_TYPE_MAPPING ctm ON ctm.COMMENT_VALUE = wid.COMMENT
    INNER JOIN
  KETTLE_MASTER_DUMP.UNIT_DETAIL ku ON ku.UNIT_ID=we.UNIT_ID and ku.UNIT_MANAGER = :unitManagerId
        AND we.STATUS = 'SETTLED'
        AND we.BUSINESS_DATE >= :startDate
        AND we.BUSINESS_DATE <= :endDate
                        ]]>
					</content>
					<params>
						<param name="startDate" displayName="Start Date" dataType="DATE"
							format="yyyy-MM-dd" />
						<param name="endDate" displayName="End Date" dataType="DATE"
							format="yyyy-MM-dd" />
						<param name="unitManagerId" displayName="Unit Manager Id"
							dataType="INTEGER" />
					</params>

				</report>

				<report name="Variance Daily Calculated across units for Date Range"
					executionType="SQL" returnType="java.lang.String">
					<content>

<![CDATA[
SELECT 
    *
FROM
    (SELECT 
        ud.UNIT_ID,
            ud.UNIT_NAME,
            utm.UNIT_TYPE,
            ld.CITY,
            si.BUSINESS_DATE,
            si.PRODUCT_ID,
            pd.PRODUCT_NAME,
            pd.UNIT_OF_MEASURE,
            cd.CATEGORY_NAME,
            sd.SUB_CATEGORY_NAME,
            si.OPENING_STOCK,
            COALESCE(dcpv.TRANSFER_OUT, 0) TRANSFERRED,
            COALESCE(dcpv.RECEIVED, 0) RECEIVED,
            COALESCE(dcpv.WASTAGE, 0) WASTAGE,
            COALESCE(dcpv.CONSUMPTION, 0) CONSUMPTION,
            si.CLOSING_STOCK,
            si.EXPECTED_CLOSING_VALUE,
            si.VARIANCE,
            si.VARIANCE_COST,
            si.VARIANCE_PRICE,
            pd.VARIANCE_TYPE,
            pd.STOCK_KEEPING_FREQUENCY,
            CASE
                WHEN pd.SHELF_LIFE_IN_DAYS = - 1 THEN 'NO'
                ELSE 'YES'
            END AS EXPIRABLE
    FROM
        KETTLE_SCM_DUMP.STOCK_INVENTORY si
    LEFT OUTER JOIN KETTLE_SCM_DUMP.DAY_CLOSE_PRODUCT_VALUES dcpv ON dcpv.EVENT_ID = si.CURRENT_EVENT_ID
        AND si.PRODUCT_ID = dcpv.PRODUCT_ID
        AND dcpv.EVENT_ID = si.CURRENT_EVENT_ID
    INNER JOIN KETTLE_SCM_DUMP.PRODUCT_DEFINITION pd ON pd.PRODUCT_ID = si.PRODUCT_ID
    INNER JOIN KETTLE_SCM_DUMP.CATEGORY_DEFINITION cd ON cd.CATEGORY_ID = pd.CATEGORY_ID
    INNER JOIN KETTLE_SCM_DUMP.SUB_CATEGORY_DEFINITION sd ON sd.SUB_CATEGORY_ID = pd.SUB_CATEGORY_ID
    INNER JOIN KETTLE_SCM_DUMP.DAY_CLOSE_EVENT dce ON dce.EVENT_ID = si.CURRENT_EVENT_ID
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON dce.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ku ON ku.UNIT_ID=si.UNIT_ID and ku.UNIT_MANAGER = :unitManagerId
    LEFT JOIN KETTLE_SCM_DUMP.UNIT_TYPE_MAPPING utm ON ud.UNIT_CATEGORY = utm.UNIT_CATEGORY
    LEFT JOIN KETTLE_MASTER_DUMP.LOCATION_DETAIL ld ON ud.LOCATION_DETAIL_ID = ld.LOCATION_ID
    WHERE
        si.BUSINESS_DATE >= :startDate
            AND si.BUSINESS_DATE <= :endDate
            AND dce.CLOSURE_EVENT_FREQUENCY = 'DAILY'
            AND si.STATUS = 'CLOSED'
            AND dce.STATUS = 'CLOSED'	
    ORDER BY dce.UNIT_ID , si.BUSINESS_DATE) a
                        ]]>
					</content>
					<params>
						<param name="startDate" displayName="Start Date" dataType="DATE"
							format="yyyy-MM-dd" />
						<param name="endDate" displayName="End Date" dataType="DATE"
							format="yyyy-MM-dd" />
						<param name="unitManagerId" displayName="Unit Manager Id"
							dataType="INTEGER" />
					</params>
				</report>
			
				<report name="Full to Regular Ratio" executionType="SQL"
					returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
    UNIT_NAME,
    BUSINESS_DATE,
    concat((ROUND(COALESCE(FULL,0)/COALESCE(REGULAR,1)*100,0)),'%') AS 'FULL TO REGULAR RATIO'
FROM
    (SELECT 
        ud.UNIT_NAME,
            (CASE
                WHEN HOUR(od.BILLING_SERVER_TIME) <= 5 THEN DATE(DATE_ADD(od.BILLING_SERVER_TIME, INTERVAL - 6 HOUR))
                ELSE DATE(od.BILLING_SERVER_TIME)
            END) AS BUSINESS_DATE,
            SUM(CASE
                WHEN DIMENSION = 'FULL' THEN QUANTITY
            END) AS 'FULL',
            SUM(CASE
                WHEN DIMENSION = 'REGULAR' THEN QUANTITY
            END) AS 'REGULAR'
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE_DUMP.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN CLM_ANALYTICS.PRODUCT_DETAILS_BIME PDB ON PDB.PRODUCT_ID = oi.PRODUCT_ID
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ku ON ku.UNIT_ID=od.UNIT_ID and ku.UNIT_MANAGER = :unitManagerId
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
            AND IS_COMPLIMENTARY <> 'Y'
            AND od.ORDER_ID > (SELECT 
                MAX(LAST_ORDER_ID) END_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = SUBDATE(:fromBusinessDate, 1))
            AND od.ORDER_ID <= (SELECT 
                MAX(LAST_ORDER_ID) END_ORDER_ID
            FROM
                KETTLE_DUMP.UNIT_CLOSURE_DETAILS
            WHERE
                BUSINESS_DATE = :toBusinessDate)
                    AND DIMENSION IN ('Regular' , 'Full')
    GROUP BY 1 , 2) A
]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="From Business Date"
							dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="To Business Date"
							dataType="DATE" format="yyyy-MM-dd" />
						<param name="unitManagerId" displayName="Unit Manager Id"
							dataType="INTEGER" />
					</params>
				</report>

				<report name="LOCALITY WISE BILL LEVEL DATA " executionType="SQL"
					returnType="java.lang.String">
					<content>
						<![CDATA[
			
			
			
SELECT * FROM (		
SELECT 
    od.ORDER_ID,
    ku.UNIT_NAME,
    ku.UNIT_MANAGER,
    od.BILLING_SERVER_TIME,
    od.CUSTOMER_ID,
    ci.CONTACT_NUMBER,
    COALESCE(ci.FIRST_NAME, 'NA') AS 'NAME',
    COALESCE(cai.LOCALITY, 'NA') AS 'LOCALITY',
    COALESCE(cai.ADDRESS_LINE_1, 'NA') AS 'ADDRESS1',
    COALESCE(cai.ADDRESS_LINE_2, 'NA') AS ADDRESS2,
    COALESCE(cai.ADDRESS_LINE_3, 'NA') AS ADDRESS3,
    cai.CITY,
    cai.STATE,
    COALESCE(cai.ADDRESS_TYPE, 'NA') AS ADDRESS_TYPE,
    od.TAXABLE_AMOUNT,
    od.DISCOUNT_AMOUNT,
    od.TOTAL_AMOUNT,
    od.SETTLED_AMOUNT,
    COALESCE(cp.PARTNER_CODE, 'NA') AS CHANNEL_PARTNER,
    COALESCE(dp.PARTNER_CODE, 'NA') AS DELIVERY_PARTNER
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        LEFT JOIN
    KETTLE_DUMP.CUSTOMER_ADDRESS_INFO cai ON od.DELIVERY_ADDRESS = cai.ADDRESS_ID
        LEFT JOIN
    KETTLE_DUMP.CUSTOMER_INFO ci ON od.CUSTOMER_ID = ci.CUSTOMER_ID
        LEFT JOIN
    KETTLE_DUMP.CHANNEL_PARTNER cp ON cp.PARTNER_ID = od.CHANNEL_PARTNER_ID
        LEFT JOIN
    KETTLE_DUMP.DELIVERY_PARTNER dp ON od.DELIVERY_PARTNER_ID = dp.PARTNER_ID
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ku ON ku.UNIT_ID=od.UNIT_ID and ku.UNIT_MANAGER = :unitManagerId
WHERE
    od.ORDER_SOURCE = 'COD'
        AND od.ORDER_STATUS <> 'CANCELLED'
	AND cp.PARTNER_ID <> 6
 AND od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate) a

			
]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
							dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
							dataType="DATE" format="yyyy-MM-dd" />
						<param name="unitManagerId" displayName="Unit Manager Id"
							dataType="INTEGER" />
					</params>

				</report>
				<report name="Consolidated Item Consumption " executionType="SQL"
					returnType="java.lang.String">
					<content>
						<![CDATA[	

SELECT 
    ud.UNIT_NAME,

    pd.PRODUCT_NAME,
    rl.RTL_CODE,
    oi.DIMENSION,
    oi.PRICE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID,0) IN (2101,2103,2104)
        THEN
            oi.QUANTITY
        ELSE 0
    END) ACCOUNTABLE_COMPLIMENTARY_QUANTITY,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND   COALESCE(oi.COMPLIMENTARY_TYPE_ID,0)  IN(2100,2102,2105,2106)
        THEN
            oi.QUANTITY
        ELSE 0
    END) NON_ACCOUNTABLE_COMPLIMENTARY_QUANTITY,
    SUM(CASE
        WHEN IS_COMPLIMENTARY = 'N' OR IS_COMPLIMENTARY IS NULL
		THEN oi.QUANTITY
        ELSE 0
    END) SOLD_QUANTITY,
    SUM(oi.QUANTITY) TOTAL_QUANTITY,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND  COALESCE(oi.COMPLIMENTARY_TYPE_ID,0) IN (2101,2103,2104)
        THEN
            oi.TOTAL_AMOUNT
        ELSE 0
    END) ACCOUNTABLE_COMPLIMENTARY_GMV,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
               AND  COALESCE(oi.COMPLIMENTARY_TYPE_ID,0)   IN (2100,2102,2105,2106)
        THEN
            oi.PRICE * oi.QUANTITY
        ELSE 0
    END) NON_ACCOUNTABLE_COMPLIMENTARY_GMV,
    SUM(CASE
        WHEN IS_COMPLIMENTARY = 'N' OR IS_COMPLIMENTARY IS NULL
		THEN oi.PRICE * oi.QUANTITY
        ELSE 0
    END) SOLD_QUANTITY_GMV,
    SUM(oi.PRICE * oi.QUANTITY) TOTAL_AMOUNT_GMV
FROM
    KETTLE_DUMP.ORDER_DETAIL od
   LEFT JOIN KETTLE_DUMP.ORDER_ITEM oi
    on od.ORDER_ID = oi.ORDER_ID
   LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud
    ON ud.UNIT_ID = od.UNIT_ID
   LEFT JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd
    ON pd.PRODUCT_ID=oi.PRODUCT_ID
   LEFT JOIN REF_LOOKUP_TYPE rl
    ON pd.PRODUCT_TYPE=rl.RTL_ID
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ku ON ku.UNIT_ID=od.UNIT_ID and ku.UNIT_MANAGER = :unitManagerId
WHERE
        od.ORDER_STATUS <> 'CANCELLED'
		
        AND od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:fromBusinessDate,1))
        AND od.ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)
			
GROUP BY ud.UNIT_NAME , pd.PRODUCT_NAME , rl.RTL_CODE,oi.DIMENSION , oi.PRICE
ORDER BY ud.UNIT_NAME , pd.PRODUCT_NAME , oi.DIMENSION


	]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
							dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
							dataType="DATE" format="yyyy-MM-dd" />
						<param name="unitManagerId" displayName="Unit Manager Id"
							dataType="INTEGER" />
					</params>

				</report>
				<report name="Combos Item Consumption" executionType="SQL"
					returnType="java.lang.String">
					<content>
						<![CDATA[					
		


SELECT ud.UNIT_NAME,a.PRODUCT_ID,a.PRODUCT_NAME,rl.RL_CODE,rlt.RTL_CODE,a.QUANTITY
FROM
(SELECT 
        od.UNIT_ID,
         
            pd1.PRODUCT_ID,pd1.PRODUCT_NAME,
            (CASE
                WHEN pd1.DIMENSION_CODE = 1 THEN 1
                WHEN
                    pd1.DIMENSION_CODE = 2
                        AND od.ORDER_SOURCE = 'CAFE'
                THEN
                    21
                WHEN
                    pd1.DIMENSION_CODE = 2
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    24
                WHEN pd1.DIMENSION_CODE = 3 THEN 30
                WHEN
                    pd1.DIMENSION_CODE = 11
                        AND od.ORDER_SOURCE = 'CAFE'
                THEN
                    1101
                WHEN
                    pd1.DIMENSION_CODE = 11
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1102
                WHEN
                    pd1.DIMENSION_CODE = 34
                        AND od.ORDER_SOURCE = 'CAFE'
                THEN
                    3401
                WHEN
                    pd1.DIMENSION_CODE = 34
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    3402
                ELSE 1
            END) AS DIMENSIONS,
            SUM(oi.QUANTITY) AS QUANTITY
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    INNER JOIN KETTLE_DUMP.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE_DUMP.ORDER_ITEM_ADDON oia ON oi.ORDER_ITEM_ID = oia.ORDER_ITEM_ID
    INNER JOIN KETTLE_MASTER_DUMP.ADDON_PRODUCT_DATA apd ON oia.ADDON_ID = apd.ADDON_ID
    INNER JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd1 ON pd1.PRODUCT_ID = apd.PRODUCT_ID
   INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ku ON ku.UNIT_ID=od.UNIT_ID and ku.UNIT_MANAGER = :unitManagerId
   
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
 AND od.ORDER_ID > (SELECT
            MAX(LAST_ORDER_ID) START_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(:fromBusinessDate,1))
        AND od.ORDER_ID <= (SELECT
            MAX(LAST_ORDER_ID) END_ORDER_ID
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = :toBusinessDate)
            AND pd.PRODUCT_TYPE = 8
            AND apd.ADDON_ID IS NOT NULL
    GROUP BY od.UNIT_ID , pd1.PRODUCT_ID,pd1.PRODUCT_NAME , DIMENSIONS) AS a

LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud
ON a.UNIT_ID=ud.UNIT_ID
LEFT JOIN REF_LOOKUP rl
ON a.DIMENSIONS=rl.RL_ID
LEFT JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd
ON a.PRODUCT_ID=pd.PRODUCT_ID
LEFT JOIN REF_LOOKUP_TYPE rlt
ON rlt.RTL_ID=pd.PRODUCT_TYPE
 

		
		]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
							dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
							dataType="DATE" format="yyyy-MM-dd" />
						<param name="unitManagerId" displayName="Unit Manager Id"
							dataType="INTEGER" />
					</params>

				</report>
				<report name="Product Sales Quantity" executionType="SQL"
					returnType="java.lang.String">
					<content>
						<![CDATA[	

SELECT        
UNIT_NAME,    
BUSINESS_DATE,                    
PRODUCT_ID,
PRODUCT_NAME,
SUM(oi.QUANTITY)QTY
FROM                                
  KETTLE_DUMP.ORDER_DETAIL od                                
      INNER JOIN                                
  KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID                                
      INNER JOIN                                
  KETTLE_DUMP.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID                                
   WHERE  od.ORDER_STATUS <>'CANCELLED'                      
AND  BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate            
   GROUP BY 1,2,3,4;				
				
						]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
							dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
							dataType="DATE" format="yyyy-MM-dd" />
					</params>
				</report>
			</reports>
		</category>
	</categories>
</ReportCategories>
