<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<ReportCategories xmlns="http://www.w3schools.com">
	<categories>
		<category name="Cafe Reports" type="Cafe Operations"
			accessCode="Marketing">
			<reports>


				<report name="Daily Tkt, Sales and APC "
					executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[

SELECT 
    od.UNIT_ID,
    ud.UNIT_NAME,
    od.BUSINESS_DATE,
    SUM(CASE
        WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
        ELSE 0
    END) AS TKT,
    SUM(od.TAXABLE_AMOUNT) AS SALES,
    TRUNCATE(SUM(od.TAXABLE_AMOUNT) / SUM(CASE
            WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
            ELSE 0
        END),
        2) AS APC
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        LEFT JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
WHERE
    od.ORDER_STATUS <> 'CANCELLED'
        AND od.ORDER_TYPE = 'order'
        AND od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate
GROUP BY od.UNIT_ID , ud.UNIT_NAME , BUSINESS_DATE
	
]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
							dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
							dataType="DATE" format="yyyy-MM-dd" />
					</params>

				</report>

				<report name="Consolidated Tkt, Sales and APC "
					executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[			
	SELECT 
    od.UNIT_ID,
    ud.UNIT_NAME,
    SUM(CASE
        WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
        ELSE 0
    END) AS TKT,
    SUM(od.TAXABLE_AMOUNT) AS SALES,
    TRUNCATE(SUM(od.TAXABLE_AMOUNT) / SUM(CASE
            WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
            ELSE 0
        END),
        2) AS APC
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        LEFT JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
WHERE
    od.ORDER_STATUS <> 'CANCELLED'
        AND od.ORDER_TYPE = 'order'
        AND od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate
GROUP BY od.UNIT_ID , ud.UNIT_NAME					
					
	]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
							dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
							dataType="DATE" format="yyyy-MM-dd" />
					</params>

				</report>			



				<report name="Consolidated Item Consumption "
					executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[	

SELECT 
    ud.UNIT_NAME,

    pd.PRODUCT_NAME,
    rl.RTL_CODE,
    oi.DIMENSION,
    oi.PRICE,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND COALESCE(oi.COMPLIMENTARY_TYPE_ID,0) IN (2101,2103,2104)
        THEN
            oi.QUANTITY
        ELSE 0
    END) ACCOUNTABLE_COMPLIMENTARY_QUANTITY,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND   COALESCE(oi.COMPLIMENTARY_TYPE_ID,0)  IN(2100,2102,2105,2106)
        THEN
            oi.QUANTITY
        ELSE 0
    END) NON_ACCOUNTABLE_COMPLIMENTARY_QUANTITY,
    SUM(CASE
        WHEN IS_COMPLIMENTARY = 'N' OR IS_COMPLIMENTARY IS NULL
		THEN oi.QUANTITY
        ELSE 0
    END) SOLD_QUANTITY,
    SUM(oi.QUANTITY) TOTAL_QUANTITY,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
                AND  COALESCE(oi.COMPLIMENTARY_TYPE_ID,0) IN (2101,2103,2104)
        THEN
            oi.TOTAL_AMOUNT
        ELSE 0
    END) ACCOUNTABLE_COMPLIMENTARY_GMV,
    SUM(CASE
        WHEN
            IS_COMPLIMENTARY = 'Y'
               AND  COALESCE(oi.COMPLIMENTARY_TYPE_ID,0)   IN (2100,2102,2105,2106)
        THEN
            oi.PRICE * oi.QUANTITY
        ELSE 0
    END) NON_ACCOUNTABLE_COMPLIMENTARY_GMV,
    SUM(CASE
        WHEN IS_COMPLIMENTARY = 'N' OR IS_COMPLIMENTARY IS NULL
		THEN oi.PRICE * oi.QUANTITY
        ELSE 0
    END) SOLD_QUANTITY_GMV,
    SUM(oi.PRICE * oi.QUANTITY) TOTAL_AMOUNT_GMV
FROM
    KETTLE_DUMP.ORDER_DETAIL od
   LEFT JOIN KETTLE_DUMP.ORDER_ITEM oi
    on od.ORDER_ID = oi.ORDER_ID
   LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud
    ON ud.UNIT_ID = od.UNIT_ID
   LEFT JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd
    ON pd.PRODUCT_ID=oi.PRODUCT_ID
   LEFT JOIN REF_LOOKUP_TYPE rl
    ON pd.PRODUCT_TYPE=rl.RTL_ID
WHERE
        od.ORDER_STATUS <> 'CANCELLED'
        AND od.BUSINESS_DATE BETWEEN :fromBusinessDate 
		AND :toBusinessDate	
GROUP BY ud.UNIT_NAME , pd.PRODUCT_NAME , rl.RTL_CODE,oi.DIMENSION , oi.PRICE
ORDER BY ud.UNIT_NAME , pd.PRODUCT_NAME , oi.DIMENSION


	]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
							dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
							dataType="DATE" format="yyyy-MM-dd" />
					</params>

				</report>

				<report name="Hourly tickets and sales"
					executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[			
SELECT 
    ud.UNIT_NAME,
    (CASE
        WHEN HOUR(BILLING_SERVER_TIME) >= 5 THEN DATE_FORMAT(BILLING_SERVER_TIME, '%d-%m-%Y')
        WHEN
            HOUR(BILLING_SERVER_TIME) < 5
        THEN
            DATE_FORMAT(DATE_SUB(BILLING_SERVER_TIME,
                        INTERVAL 1 DAY),
                    '%d-%m-%Y')
    END) AS BUSINESS_DATE,
    DATE_FORMAT(DATE(od.BILLING_SERVER_TIME), '%d-%m-%Y') AS BILLING_DATE,
    HOUR(od.BILLING_SERVER_TIME) AS 'HOUR',
    SUM(CASE
        WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
        ELSE 0
    END) AS TICKETS,
    SUM(od.TAXABLE_AMOUNT) AS SALES,
    TRUNCATE(SUM(od.TAXABLE_AMOUNT) / SUM(CASE
            WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
            ELSE 0
        END),
        0) AS APC
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        LEFT JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
WHERE
    od.ORDER_STATUS <> 'CANCELLED'
	AND od.ORDER_TYPE = 'order'
    AND od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate
GROUP BY ud.UNIT_NAME , BUSINESS_DATE , DATE(od.BILLING_SERVER_TIME) , HOUR(od.BILLING_SERVER_TIME)

]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />

					</params>
				</report>

				<report name="Hourly Tickets and Sales for COD Data only"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[						
						
						
						
SELECT 
    ud.UNIT_NAME,
    (CASE
        WHEN HOUR(BILLING_SERVER_TIME) >= 5 THEN DATE_FORMAT(BILLING_SERVER_TIME, '%d-%m-%Y')
        WHEN
            HOUR(BILLING_SERVER_TIME) < 5
        THEN
            DATE_FORMAT(DATE_SUB(BILLING_SERVER_TIME,
                        INTERVAL 1 DAY),
                    '%d-%m-%Y')
    END) AS BUSINESS_DATE,
    DATE_FORMAT(DATE(od.BILLING_SERVER_TIME), '%d-%m-%Y') AS BILLING_DATE,
    HOUR(od.BILLING_SERVER_TIME) AS 'HOUR',
    SUM(CASE
        WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
        ELSE 0
    END) AS TICKETS,
    SUM(od.TAXABLE_AMOUNT) AS SALES,
    TRUNCATE(SUM(od.TAXABLE_AMOUNT) / SUM(CASE
            WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
            ELSE 0
        END),
        0) AS APC
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        LEFT JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
WHERE
    od.ORDER_STATUS <> 'CANCELLED'
	AND od.ORDER_SOURCE = 'COD'
	AND od.ORDER_TYPE = 'order'
        AND od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate
GROUP BY ud.UNIT_NAME , BUSINESS_DATE , DATE(od.BILLING_SERVER_TIME) , HOUR(od.BILLING_SERVER_TIME)

]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>

				</report>	

				<report name="Hourly tickets and sales for Channel Partner"
					executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[			
						SELECT 
    ud.UNIT_NAME,
    (CASE
        WHEN HOUR(BILLING_SERVER_TIME) >= 5 THEN DATE_FORMAT(BILLING_SERVER_TIME, '%d-%m-%Y')
        WHEN
            HOUR(BILLING_SERVER_TIME) < 5
        THEN
            DATE_FORMAT(DATE_SUB(BILLING_SERVER_TIME,
                        INTERVAL 1 DAY),
                    '%d-%m-%Y')
    END) AS BUSINESS_DATE,
    DATE_FORMAT(DATE(od.BILLING_SERVER_TIME), '%d-%m-%Y') AS BILLING_DATE,
    HOUR(od.BILLING_SERVER_TIME) AS 'HOUR',
    SUM(CASE
        WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
        ELSE 0
    END) AS TICKETS,
    SUM(od.TAXABLE_AMOUNT) AS SALES,
    TRUNCATE(SUM(od.TAXABLE_AMOUNT) / SUM(CASE
            WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
            ELSE 0
        END),
        0) AS APC,
	cp.PARTNER_CODE
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        INNER JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    INNER JOIN 
    KETTLE_DUMP.CHANNEL_PARTNER cp ON od.CHANNEL_PARTNER_ID = cp.PARTNER_ID
WHERE
    od.ORDER_STATUS <> 'CANCELLED'
    AND cp.PARTNER_CODE NOT LIKE '%CHAAYOS%'
	AND od.ORDER_TYPE = 'order'
    AND od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate
GROUP BY ud.UNIT_NAME , BUSINESS_DATE , DATE(od.BILLING_SERVER_TIME) , HOUR(od.BILLING_SERVER_TIME)

]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />

					</params>
				</report>
				<report name="Combos Item Consumption"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[					
		


SELECT ud.UNIT_NAME,a.PRODUCT_ID,a.PRODUCT_NAME,rl.RL_CODE,rlt.RTL_CODE,a.QUANTITY
FROM
(SELECT 
        od.UNIT_ID,
         
            pd1.PRODUCT_ID,pd1.PRODUCT_NAME,
            (CASE
                WHEN pd1.DIMENSION_CODE = 1 THEN 1
                WHEN
                    pd1.DIMENSION_CODE = 2
                        AND od.ORDER_SOURCE = 'CAFE'
                THEN
                    21
                WHEN
                    pd1.DIMENSION_CODE = 2
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    24
                WHEN pd1.DIMENSION_CODE = 3 THEN 30
                WHEN
                    pd1.DIMENSION_CODE = 11
                        AND od.ORDER_SOURCE = 'CAFE'
                THEN
                    1101
                WHEN
                    pd1.DIMENSION_CODE = 11
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    1102
                WHEN
                    pd1.DIMENSION_CODE = 34
                        AND od.ORDER_SOURCE = 'CAFE'
                THEN
                    3401
                WHEN
                    pd1.DIMENSION_CODE = 34
                        AND od.ORDER_SOURCE = 'COD'
                THEN
                    3402
                ELSE 1
            END) AS DIMENSIONS,
            SUM(oi.QUANTITY) AS QUANTITY
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    INNER JOIN KETTLE_DUMP.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    INNER JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE_DUMP.ORDER_ITEM_ADDON oia ON oi.ORDER_ITEM_ID = oia.ORDER_ITEM_ID
    INNER JOIN KETTLE_MASTER_DUMP.ADDON_PRODUCT_DATA apd ON oia.ADDON_ID = apd.ADDON_ID
    INNER JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd1 ON pd1.PRODUCT_ID = apd.PRODUCT_ID
   
    WHERE
        od.ORDER_STATUS <> 'CANCELLED'
		AND od.ORDER_TYPE = 'order'
        AND od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate
            AND pd.PRODUCT_TYPE = 8
            AND apd.ADDON_ID IS NOT NULL
    GROUP BY od.UNIT_ID , pd1.PRODUCT_ID,pd1.PRODUCT_NAME , DIMENSIONS) AS a

LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud
ON a.UNIT_ID=ud.UNIT_ID
LEFT JOIN REF_LOOKUP rl
ON a.DIMENSIONS=rl.RL_ID
LEFT JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd
ON a.PRODUCT_ID=pd.PRODUCT_ID
LEFT JOIN REF_LOOKUP_TYPE rlt
ON rlt.RTL_ID=pd.PRODUCT_TYPE
 

		
		]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>

				</report>



				<report name="Employee Meal Consumption"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[				   
					   
					   
					   
					   
SELECT 
    ud.UNIT_NAME,
    (CASE
        WHEN
            HOUR(od.BILLING_SERVER_TIME) <= 5
        THEN
            DATE(DATE_ADD(od.BILLING_SERVER_TIME,
                    INTERVAL - 6 HOUR))
        ELSE DATE(od.BILLING_SERVER_TIME)
    END) AS BUSINESS_DATE,
    pd.PRODUCT_NAME,
    oi.DIMENSION,
    SUM(oi.QUANTITY) AS QUANTITY,
    SUM(oi.QUANTITY * oi.PRICE) AS GMV
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        LEFT JOIN
    KETTLE_DUMP.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd ON pd.PRODUCT_ID = oi.PRODUCT_ID
WHERE
    od.ORDER_STATUS <> 'CANCELLED'
		AND od.ORDER_TYPE LIKE '%employee-meal'
        AND od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate
		GROUP BY ud.UNIT_NAME , BUSINESS_DATE , pd.PRODUCT_NAME , oi.DIMENSION
ORDER BY ud.UNIT_NAME , BUSINESS_DATE , pd.PRODUCT_NAME , oi.DIMENSION
					   
					   
					   
			]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>

				</report>		   

				<report name="Cancelled Report"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[						
	SELECT 
    ud.UNIT_NAME AS UNIT_NAME,
    ci.CONTACT_NUMBER AS CONTACT_NUMBER,
    od.ORDER_ID AS ORDER_ID,
    od.BILLING_SERVER_TIME AS BILLING_SERVER_TIME,
    od.TAXABLE_AMOUNT AS 'TAXABLE_AMOUNT',
    COALESCE(od.BILL_CANCELLATION_TIME,'NA') AS BILL_CANCELLATION_TIME,
    COALESCE(ed1.EMP_NAME, 'NA') AS CANCELLED_BY,
    COALESCE(ed2.EMP_NAME, 'NA') AS CANCELLED_APPROVED_BY,
    COALESCE(od.CANCELATION_REASON,'NA') AS CANCELATION_REASON
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        LEFT JOIN
    KETTLE_DUMP.CUSTOMER_INFO ci ON od.CUSTOMER_ID = ci.CUSTOMER_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.EMPLOYEE_DETAIL ed1 ON ed1.EMP_ID = od.CANCELLED_BY
        LEFT JOIN
    KETTLE_MASTER_DUMP.EMPLOYEE_DETAIL ed2 ON ed2.EMP_ID = od.CANCEL_APPROVED_BY
WHERE
    od.ORDER_STATUS = 'CANCELLED'
        AND od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate
					
						
]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>

				</report>

				<report name="Cancelled Order Sheet"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
							  
							  
							  
	SELECT 
    ud.UNIT_NAME,
    ud.UNIT_REGION,
    od.GENERATED_ORDER_ID AS ORDER_NO,
    DATE(od.BILLING_SERVER_TIME) AS 'DATE',
    TIME(od.BILLING_SERVER_TIME) AS 'TIME',
    od.BILL_CANCELLATION_TIME,
    nt.QUANTITY,
    od.ORDER_SOURCE,
    od.TAXABLE_AMOUNT,
    od.SURCHARGE_TAX_AMOUNT,
    od.SERVICE_TAX_AMOUNT,
    od.NET_PRICE_VAT_AMOUNT,
    od.MRP_VAT_AMOUNT,
    od.SETTLED_AMOUNT,
    od.EMP_ID,
    COALESCE(ed.EMP_NAME, 'NA') AS EMP_NAME,
    od.GENERATED_ORDER_ID,
    COALESCE(od.CANCELATION_REASON, 'NA') AS CANCELATION_REASON,
    COALESCE(od.CANCELLED_BY, 'NA') AS CANCELLED_BY_ID,
    COALESCE(ed2.EMP_NAME, 'NA') AS CANCELLED_BY,
    COALESCE(od.CANCEL_APPROVED_BY, 'NA') AS CANCEL_APPROVED_BY_ID,
    COALESCE(ed2.EMP_NAME, 'NA') AS CANCELLED_APPROVED_BY
FROM
    KETTLE_DUMP.ORDER_DETAIL od
        LEFT JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = od.UNIT_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.EMPLOYEE_DETAIL ed ON od.EMP_ID = ed.EMP_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.EMPLOYEE_DETAIL ed2 ON od.CANCEL_APPROVED_BY = ed2.EMP_ID
        LEFT JOIN
    KETTLE_MASTER_DUMP.EMPLOYEE_DETAIL ed3 ON od.CANCELLED_BY = ed3.EMP_ID
        LEFT JOIN
    (SELECT 
        od.ORDER_ID, SUM(oi.QUANTITY) AS QUANTITY
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    LEFT JOIN KETTLE_DUMP.ORDER_ITEM oi ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        od.ORDER_STATUS = 'CANCELLED'
        AND od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate				
    GROUP BY od.ORDER_ID) nt ON od.ORDER_ID = nt.ORDER_ID
WHERE
    od.ORDER_STATUS = 'CANCELLED'
	AND od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate				
			
			
			   ]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>

				</report>



				<report name="Customer capture rate day wise on a date range"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[						
	
SELECT od.UNIT_ID,ud.UNIT_NAME ,(CASE
        WHEN
            HOUR(od.BILLING_SERVER_TIME) <= 5
        THEN
            DATE(DATE_ADD(od.BILLING_SERVER_TIME,
                    INTERVAL - 6 HOUR))
        ELSE DATE(od.BILLING_SERVER_TIME)
    END) AS BUSINESS_DATE, 	
 
    CONCAT(TRUNCATE(SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00'  and od.CUSTOMER_ID>5
                THEN 1
                ELSE 0
            END)*100/
 SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END),2), '%') AS CUSTOMER_CAPTURE_RATE
            
            FROM KETTLE_DUMP.ORDER_DETAIL od
            LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud
            ON od.UNIT_ID=ud.UNIT_ID
            WHERE od.ORDER_STATUS<>'CANCELLED'
	AND od.ORDER_TYPE = 'order'
	AND od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate				
	        GROUP BY od.UNIT_ID,ud.UNIT_NAME, BUSINESS_DATE	
	

  ]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>

				</report>






				<report name="Consolidated Customer capture rate"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[						
	
SELECT od.UNIT_ID,ud.UNIT_NAME , 	
 
    CONCAT(TRUNCATE(SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00'  and od.CUSTOMER_ID>5
                THEN 1
                ELSE 0
            END)*100/
 SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END),2), '%') AS CUSTOMER_CAPTURE_RATE
            
            FROM KETTLE_DUMP.ORDER_DETAIL od
            LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud
            ON od.UNIT_ID=ud.UNIT_ID
            WHERE od.ORDER_STATUS<>'CANCELLED'
AND od.ORDER_TYPE = 'order'
	AND od.BUSINESS_DATE BETWEEN :fromBusinessDate AND :toBusinessDate				
            GROUP BY od.UNIT_ID,ud.UNIT_NAME	
	

  ]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>

				</report>







				<report name="BRM Report"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[						
						
	
SELECT 
    z.UNIT_NAME,
    z.UNIT_ID,
    z.TKT AS TKTS,
    z.SALES AS SALES,
    z.APC,
    z.CUSTOMER_CAPTURE_RATE,
    CONCAT(TRUNCATE(r.PERCENT_CANCELLED_TKT,2), '%') AS PERCENT_CANCELLED_TKT,
    TRUNCATE(q.ORDERS * 100 / z.TKT, 0) '%OF_TICKETS_WITH_FOOD_ITEM',
    TRUNCATE(e.CHAI_QUANTITY / z.TKT, 2) AS CHAI_PER_TKT,
    TRUNCATE(e.TOTAL_BEVERAGE_QUANTITY / z.TKT,
        2) AS TOTAL_BEVERAGE_PER_TKT,
    TRUNCATE(e.PEOPLE_PER_ORDER_COMPARED_TO_BEVERAGE / z.TKT,
        2) AS PEOPLE_PER_TKT,
    TRUNCATE(e.HOT_BEVERAGE_QUANTITY / z.TKT,
        2) AS HOT_BEVERAGE_QUANTITY_PER_TKT,
    TRUNCATE(e.COLD_BEVERAGE_QUANTITY / z.TKT,
        2) AS COLD_BEVERAGE_QUANTITY_PER_TKT,
    TRUNCATE(e.FOOD_QUANTITY / z.TKT, 2) AS FOOD_QUANTITY_PER_TKT,
    TRUNCATE(e.COMBOS_QUANTITY / z.TKT, 2) AS COMBOS_QUANTITY_PER_TKT,
    TRUNCATE(1-(z.SALES/j.GMV),4) AS DISCOUNT_PERCENT
FROM
    (SELECT 
        ud.UNIT_NAME,
            od.UNIT_ID,
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END) AS 'TKT',
            SUM(od.TAXABLE_AMOUNT) AS 'SALES',
            TRUNCATE((SUM(od.TAXABLE_AMOUNT) / SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END)), 0) AS 'APC',
            TRUNCATE((SUM(CASE
                WHEN
                    od.CUSTOMER_ID > 5
                        AND od.TOTAL_AMOUNT <> '0.00'
                THEN
                    1
                ELSE 0
            END)) * 100 / (SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' THEN 1
                ELSE 0
            END)), 0) AS CUSTOMER_CAPTURE_RATE
    FROM
        KETTLE_DUMP.ORDER_DETAIL od
    LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON od.UNIT_ID = ud.UNIT_ID
    WHERE
        od.ORDER_STATUS<>'CANCELLED'
            AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(:fromBusinessDate, INTERVAL 5 HOUR) AND DATE_ADD(:toBusinessDate, INTERVAL 29 HOUR)
    GROUP BY od.UNIT_ID) z
        LEFT JOIN
    (SELECT 
        b.UNIT_ID,
            SUM(b.CHAI_QUANTITY) AS CHAI_QUANTITY,
            SUM(b.TOTAL_BEVERAGE_QUANTITY) AS TOTAL_BEVERAGE_QUANTITY,
            SUM(b.PEOPLE_PER_ORDER_COMPARED_TO_BEVERAGE) AS PEOPLE_PER_ORDER_COMPARED_TO_BEVERAGE,
            SUM(b.HOT_BEVERAGE_QUANTITY) AS HOT_BEVERAGE_QUANTITY,
            SUM(b.COLD_BEVERAGE_QUANTITY) AS COLD_BEVERAGE_QUANTITY,
            SUM(b.FOOD_QUANTITY) AS FOOD_QUANTITY,
            SUM(b.COMBOS_QUANTITY) AS COMBOS_QUANTITY
    FROM
        (SELECT 
        a.*,
            GREATEST(a.TOTAL_BEVERAGE_QUANTITY, a.FOOD_QUANTITY) PEOPLE_PER_ORDER_COMPARED_TO_BEVERAGE
    FROM
        (SELECT 
        od.UNIT_ID,
            (CASE
                WHEN HOUR(od.BILLING_SERVER_TIME) <= 5 THEN DATE(DATE_ADD(od.BILLING_SERVER_TIME, INTERVAL - 6 HOUR))
                ELSE DATE(od.BILLING_SERVER_TIME)
            END) AS BUSINESS_DATE,
            oi.ORDER_ID,
            SUM(CASE
                WHEN
                    pd.PRODUCT_SUB_TYPE IN (501 , 502, 503)
                        AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) NOT IN (2100 , 2102, 2105, 2106)
                THEN
                    oi.QUANTITY
                ELSE 0
            END) CHAI_QUANTITY,
            SUM(CASE
                WHEN
                    pd.PRODUCT_TYPE IN (5 , 6)
                        AND pd.PRODUCT_SUB_TYPE NOT IN (501 , 502, 503)
                        AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) NOT IN (2100 , 2102, 2105, 2106)
                THEN
                    oi.QUANTITY
                ELSE 0
            END) OTHER_BEVERAGE_QUANTITY,
            SUM(CASE
                WHEN
                    pd.PRODUCT_TYPE = 5
                        AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) NOT IN (2100 , 2102, 2105, 2106)
                THEN
                    oi.QUANTITY
                ELSE 0
            END) HOT_BEVERAGE_QUANTITY,
            SUM(CASE
                WHEN
                    pd.PRODUCT_TYPE = 6
                        AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) NOT IN (2100 , 2102, 2105, 2106)
                THEN
                    oi.QUANTITY
                ELSE 0
            END) COLD_BEVERAGE_QUANTITY,
            SUM(CASE
                WHEN
                    pd.PRODUCT_TYPE = 7
                        AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) NOT IN (2100 , 2102, 2105, 2106)
                THEN
                    oi.QUANTITY
                ELSE 0
            END) FOOD_QUANTITY,
            SUM(CASE
                WHEN
                    pd.PRODUCT_TYPE = 8
                        AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) NOT IN (2100 , 2102, 2105, 2106)
                THEN
                    oi.QUANTITY
                ELSE 0
            END) COMBOS_QUANTITY,
            SUM(CASE
                WHEN
                    pd.PRODUCT_TYPE IN (5 , 6)
                        AND COALESCE(oi.COMPLIMENTARY_TYPE_ID, 0) NOT IN (2100 , 2105, 2106)
                THEN
                    oi.QUANTITY
                ELSE 0
            END) TOTAL_BEVERAGE_QUANTITY
    FROM
        KETTLE_DUMP.ORDER_ITEM oi
    LEFT JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    LEFT JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    WHERE
       od.ORDER_STATUS<>'CANCELLED'
      AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(:fromBusinessDate, INTERVAL 5 HOUR) AND DATE_ADD(:toBusinessDate, INTERVAL 29 HOUR)
    GROUP BY od.UNIT_ID , BUSINESS_DATE , oi.ORDER_ID) AS a) AS b
    GROUP BY b.UNIT_ID) e ON z.UNIT_ID = e.UNIT_ID
        LEFT JOIN
    (SELECT 
        od.UNIT_ID, COUNT(DISTINCT oi.ORDER_ID) AS ORDERS
    FROM
        KETTLE_DUMP.ORDER_ITEM oi
    LEFT JOIN KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd ON oi.PRODUCT_ID = pd.PRODUCT_ID
    LEFT JOIN KETTLE_DUMP.ORDER_DETAIL od ON od.ORDER_ID = oi.ORDER_ID
    WHERE
        pd.PRODUCT_TYPE IN (7 , 8, 10)
         AND  od.ORDER_STATUS<>'CANCELLED'
    AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(:fromBusinessDate, INTERVAL 5 HOUR) AND DATE_ADD(:toBusinessDate, INTERVAL 29 HOUR)
    GROUP BY od.UNIT_ID) AS q ON e.UNIT_ID = q.UNIT_ID

LEFT JOIN 
(
SELECT ud.UNIT_ID , 
   COALESCE(SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' and od.ORDER_STATUS='CANCELLED' THEN 1
                ELSE 0
            END),0) *100/
            SUM(CASE
                WHEN od.TOTAL_AMOUNT <> '0.00' and od.ORDER_STATUS<>'CANCELLED' THEN 1
                ELSE 0
            END)
            AS PERCENT_CANCELLED_TKT
            FROM KETTLE_DUMP.ORDER_DETAIL od
            LEFT JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud
            ON od.UNIT_ID=ud.UNIT_ID
WHERE  

od.BILLING_SERVER_TIME BETWEEN DATE_ADD(:fromBusinessDate, INTERVAL 5 HOUR) AND DATE_ADD(:toBusinessDate, INTERVAL 29 HOUR)
            GROUP BY ud.UNIT_ID) AS r
    ON z.UNIT_ID = r.UNIT_ID

    
LEFT JOIN 
(       SELECT od.UNIT_ID,  
SUM(oi.PRICE*oi.QUANTITY) AS GMV
FROM KETTLE_DUMP.ORDER_DETAIL od
LEFT JOIN KETTLE_DUMP.ORDER_ITEM oi
ON od.ORDER_ID=oi.ORDER_ID
WHERE COALESCE(oi.COMPLIMENTARY_TYPE_ID,0) NOT IN ('2100','2102','2105','2106')
AND od.ORDER_STATUS <>'CANCELLED'
AND od.BILLING_SERVER_TIME BETWEEN DATE_ADD(:fromBusinessDate, INTERVAL 5 HOUR) AND DATE_ADD(:toBusinessDate, INTERVAL 29 HOUR)
            GROUP BY od.UNIT_ID) AS j
	ON z.UNIT_ID=j.UNIT_ID	



  ]]>
					</content>
					<params>
						<param name="fromBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
						<param name="toBusinessDate" displayName="Business Date"
                                          dataType="DATE" format="yyyy-MM-dd" />
					</params>

				</report>
				<report name="Old/New Customer Acquisition"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
    ud.UNIT_ID,
    ud.UNIT_NAME,
    (CASE
        WHEN
            ci.CUSTOMER_ID > (SELECT 
                    MAX(CUSTOMER_ID)
                FROM
                    KETTLE_DUMP.ORDER_DETAIL
                WHERE
                    ORDER_ID <= (SELECT 
                            MAX(LAST_ORDER_ID)
                        FROM
                            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
                        WHERE
                            BUSINESS_DATE = SUBDATE(CURRENT_DATE, 2)))
        THEN
            "NEW"
        ELSE "OLD"
    END) AS CUSTOMER_TYPE,
    ci.FIRST_NAME,
    ci.CONTACT_NUMBER,
    ci.EMAIL_ID
FROM
    KETTLE_DUMP.CUSTOMER_INFO ci,
    KETTLE_DUMP.ORDER_DETAIL od,
    KETTLE_DUMP.UNIT_DETAIL ud
WHERE
    od.ORDER_ID > (SELECT 
            MAX(LAST_ORDER_ID)
        FROM
            KETTLE_DUMP.UNIT_CLOSURE_DETAILS
        WHERE
            BUSINESS_DATE = SUBDATE(CURRENT_DATE, 2))
        AND od.CUSTOMER_ID = ci.CUSTOMER_ID
        AND od.UNIT_ID = ud.UNIT_ID
        AND od.CUSTOMER_ID > 5
ORDER BY ud.UNIT_ID						
						]]>
					</content>
				</report>
				<report name="Unit Product Pricing"
                              executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
    upm.UNIT_PROD_REF_ID,
    ud.UNIT_ID,
    pd.PRODUCT_ID,
    ud.UNIT_NAME,
    ud.UNIT_CATEGORY,
    pd.PRODUCT_NAME,
    pd.PRODUCT_TYPE,
    rlt.RTL_NAME,
    rl.RL_CODE,
    upp.UNIT_PROD_PRICE_ID,
    upp.DIMENSION_CODE,
    upp.PRICE,
    upp.COST,
    upp.COD_COST,
    pd.PRODUCT_STATUS PRODUCT_STATUS_AT_COMPANY_LEVEL,
    upm.PRODUCT_STATUS PRODUCT_STATUS_AT_UNIT_LEVEL
    
FROM
    KETTLE_MASTER_DUMP.UNIT_PRODUCT_MAPPING upm,
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud,
    KETTLE_MASTER_DUMP.PRODUCT_DETAIL pd,
    KETTLE_MASTER_DUMP.REF_LOOKUP rl,
    KETTLE_MASTER_DUMP.UNIT_PRODUCT_PRICING upp,
    KETTLE_MASTER_DUMP.REF_LOOKUP_TYPE rlt
WHERE
    ud.UNIT_ID = upm.UNIT_ID
        AND pd.PRODUCT_ID = upm.PRODUCT_ID
        AND pd.DIMENSION_CODE = rl.RTL_ID
        AND upm.UNIT_PROD_REF_ID = upp.UNIT_PROD_REF_ID
        AND upp.DIMENSION_CODE = rl.RL_ID
        AND rlt.RTL_ID = pd.PRODUCT_TYPE;					
						]]>
					</content>
				</report>
			</reports>
		</category>
	</categories>
</ReportCategories>