<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<ReportCategories xmlns="http://www.w3schools.com">
	<categories>
		<category name="Transaction Report" type="SuMo Transacion" accessCode="SuMo Transacion">
			<reports>
				<report name="TO-EP data"
                        		executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
    et.TRANSFER_ORDER_ID,
    et.VENDOR_NAME,
    et.LOCATION_NAME,
    et.UPDATED_BY,
    too.GENERATION_TIME,
    ud.UNIT_NAME,
    toi.SKU_NAME,
    toi.TRANSFERRED_QUANTITY,
    toi.UNIT_OF_MEASURE,
    cdd.PRICE,
    (cdd.PRICE * toi.TRANSFERRED_QUANTITY) AS AMOUNT,
TRIM(REPLACE(REPLACE(REPLACE(REPLACE(too.COMMENT, '\n', ' '), '\r', ' '), '\t', ' '),',',' ')) COMMENTS
FROM
    KETTLE_SCM_DUMP.EXTERNAL_TRANSFER_DETAILS et
        INNER JOIN
    KETTLE_SCM_DUMP.TRANSFER_ORDER too ON too.TRANSFER_ORDER_ID = et.TRANSFER_ORDER_ID
        INNER JOIN
    KETTLE_SCM_DUMP.TRANSFER_ORDER_ITEM toi ON toi.TRANSFER_ORDER_ID = too.TRANSFER_ORDER_ID
    INNER JOIN KETTLE_SCM_DUMP.COST_DETAIL_DATA cdd
    ON cdd.UNIT_ID = too.GENERATION_UNIT_ID AND cdd.KEY_ID=toi.SKU_ID
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = too.GENERATION_UNIT_ID
    WHERE cdd.IS_LATEST= 'Y'
    AND too.GENERATION_TIME BETWEEN :startDate AND :endDate;
						]]>
					</content>
					<params>
						<param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
					</params>

				</report>

				<report name="Goods Received In Report Accross Units For Date Range"
                        executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
                            SELECT T.* FROM (SELECT
  g.GOODS_RECEIVED_ID,
  gi.GOODS_RECEIVED_ITEM_ID,
  gi.TRANSFER_ORDER_ITEM_ID,
  gi.REQUEST_ORDER_ITEM_ID,
  u.UNIT_NAME AS TRANSFERRING_UNIT,
  ux.UNIT_NAME AS REQUESTING_RECEIVING_UNIT,
  TRIM(REPLACE(REPLACE(REPLACE(REPLACE(p.PRODUCT_NAME, '\n', ' '), '\r', ' '), '\t', ' '),',',' ')) PRODUCT_NAME,
  gi.SKU_NAME,
  ri.REQUESTED_QUANTITY,
  gi.TRANSFERRED_QUANTITY,
  gi.RECEIVED_QUANTITY,
  (gi.RECEIVED_QUANTITY * s.NEGOTIATED_UNIT_PRICE) AS COST,
  gi.UNIT_OF_MEASURE,
  s.NEGOTIATED_UNIT_PRICE AS UNIT_PRICE,
  c.CATEGORY_NAME,
  sc.SUB_CATEGORY_NAME,
  r.GENERATION_TIME AS REQUESTING_TIME,
  t.GENERATION_TIME AS TRANSFER_TIME,
  g.LAST_UPDATE_TIME AS RECEIVING_TIME,
  vd.ENTITY_NAME,
  TRIM(REPLACE(REPLACE(REPLACE(REPLACE(g.COMMENT, '\n', ' '), '\r', ' '), '\t', ' '),',',' ')) AS RECEIVING_COMMENT,
  TRIM(REPLACE(REPLACE(REPLACE(REPLACE(r.COMMENT, '\n', ' '), '\r', ' '), '\t', ' '),',',' ')) AS REQUESTING_COMMENT
FROM
  KETTLE_SCM_DUMP.GOODS_RECEIVED_ITEM gi
	  INNER JOIN
  KETTLE_SCM_DUMP.GOODS_RECEIVED g ON gi.GOODS_RECEIVED_ID = g.GOODS_RECEIVED_ID
	  AND g.GOODS_RECEIVED_STATUS = 'SETTLED'
	  AND DATE(g.LAST_UPDATE_TIME) >= :startDate
	  AND DATE(g.LAST_UPDATE_TIME) <= :endDate
	  INNER JOIN
  KETTLE_SCM_DUMP.SKU_DEFINITION s ON s.SKU_ID = gi.SKU_ID
	  INNER JOIN
  KETTLE_SCM_DUMP.PRODUCT_DEFINITION p ON p.PRODUCT_ID = s.LINKED_PRODUCT_ID
	  LEFT JOIN
  KETTLE_SCM_DUMP.REQUEST_ORDER_ITEM ri ON ri.REQUEST_ORDER_ITEM_ID = gi.REQUEST_ORDER_ITEM_ID
	  INNER JOIN
  KETTLE_SCM_DUMP.UNIT_DETAIL u ON u.UNIT_ID = g.GENERATION_UNIT_ID
	  INNER JOIN
  KETTLE_SCM_DUMP.UNIT_DETAIL ux ON ux.UNIT_ID = g.GENERATED_FOR_UNIT_ID
	  INNER JOIN
  KETTLE_SCM_DUMP.CATEGORY_DEFINITION c ON c.CATEGORY_ID = p.CATEGORY_ID
	  INNER JOIN
  KETTLE_SCM_DUMP.SUB_CATEGORY_DEFINITION sc ON sc.SUB_CATEGORY_ID = p.SUB_CATEGORY_ID
	  LEFT JOIN
  KETTLE_SCM_DUMP.REQUEST_ORDER r ON r.REQUEST_ORDER_ID = g.REQUEST_ORDER_ID
	  INNER JOIN
  KETTLE_SCM_DUMP.TRANSFER_ORDER t ON t.TRANSFER_ORDER_ID = g.TRANSFER_ORDER_ID
  LEFT JOIN KETTLE_SCM_DUMP.VENDOR_DETAIL_DATA vd ON vd.VENDOR_ID=ri.VENDOR_ID) T
                        ]]>
					</content>
					<params>
						<param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
					</params>

				</report>
<report name="Goods Received In Report Accross Units For Date Range By Product Name"
                        executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
                            SELECT T.* FROM (SELECT
  g.GOODS_RECEIVED_ID,
  gi.GOODS_RECEIVED_ITEM_ID,
  gi.TRANSFER_ORDER_ITEM_ID,
  gi.REQUEST_ORDER_ITEM_ID,
  u.UNIT_NAME AS TRANSFERRING_UNIT,
  ux.UNIT_NAME AS REQUESTING_RECEIVING_UNIT,
  TRIM(REPLACE(REPLACE(REPLACE(REPLACE(p.PRODUCT_NAME, '\n', ' '), '\r', ' '), '\t', ' '),',',' ')) PRODUCT_NAME,
  gi.SKU_NAME,
  ri.REQUESTED_QUANTITY,
  gi.TRANSFERRED_QUANTITY,
  gi.RECEIVED_QUANTITY,
  (gi.RECEIVED_QUANTITY * s.NEGOTIATED_UNIT_PRICE) AS COST,
  gi.UNIT_OF_MEASURE,
  s.NEGOTIATED_UNIT_PRICE AS UNIT_PRICE,
  c.CATEGORY_NAME,
  sc.SUB_CATEGORY_NAME,
  r.GENERATION_TIME AS REQUESTING_TIME,
  t.GENERATION_TIME AS TRANSFER_TIME,
  g.LAST_UPDATE_TIME AS RECEIVING_TIME,
  vd.ENTITY_NAME,
  TRIM(REPLACE(REPLACE(REPLACE(REPLACE(g.COMMENT, '\n', ' '), '\r', ' '), '\t', ' '),',',' ')) AS RECEIVING_COMMENT,
  TRIM(REPLACE(REPLACE(REPLACE(REPLACE(r.COMMENT, '\n', ' '), '\r', ' '), '\t', ' '),',',' ')) AS REQUESTING_COMMENT
FROM
  KETTLE_SCM_DUMP.GOODS_RECEIVED_ITEM gi
	  INNER JOIN
  KETTLE_SCM_DUMP.GOODS_RECEIVED g ON gi.GOODS_RECEIVED_ID = g.GOODS_RECEIVED_ID
	  AND g.GOODS_RECEIVED_STATUS = 'SETTLED'
	  AND DATE(g.LAST_UPDATE_TIME) >= :startDate
	  AND DATE(g.LAST_UPDATE_TIME) <= :endDate
	  INNER JOIN
  KETTLE_SCM_DUMP.SKU_DEFINITION s ON s.SKU_ID = gi.SKU_ID
	  INNER JOIN
  KETTLE_SCM_DUMP.PRODUCT_DEFINITION p ON p.PRODUCT_ID = s.LINKED_PRODUCT_ID AND p.PRODUCT_NAME LIKE CONCAT('%',:productName,'%')  
	  LEFT JOIN
  KETTLE_SCM_DUMP.REQUEST_ORDER_ITEM ri ON ri.REQUEST_ORDER_ITEM_ID = gi.REQUEST_ORDER_ITEM_ID
	  INNER JOIN
  KETTLE_SCM_DUMP.UNIT_DETAIL u ON u.UNIT_ID = g.GENERATION_UNIT_ID
	  INNER JOIN
  KETTLE_SCM_DUMP.UNIT_DETAIL ux ON ux.UNIT_ID = g.GENERATED_FOR_UNIT_ID
	  INNER JOIN
  KETTLE_SCM_DUMP.CATEGORY_DEFINITION c ON c.CATEGORY_ID = p.CATEGORY_ID
	  INNER JOIN
  KETTLE_SCM_DUMP.SUB_CATEGORY_DEFINITION sc ON sc.SUB_CATEGORY_ID = p.SUB_CATEGORY_ID
	  LEFT JOIN
  KETTLE_SCM_DUMP.REQUEST_ORDER r ON r.REQUEST_ORDER_ID = g.REQUEST_ORDER_ID
	  INNER JOIN
  KETTLE_SCM_DUMP.TRANSFER_ORDER t ON t.TRANSFER_ORDER_ID = g.TRANSFER_ORDER_ID
  LEFT JOIN KETTLE_SCM_DUMP.VENDOR_DETAIL_DATA vd ON vd.VENDOR_ID=ri.VENDOR_ID) T
                        ]]>
					</content>
					<params>
						<param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="productName" displayName="Product Name"
                               dataType="STRING"/>
					</params>

				</report>

				<report name="Goods Received In Report For a unit For Date Range"
                        executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
                            SELECT T.* FROM (SELECT
  g.GOODS_RECEIVED_ID,
  gi.GOODS_RECEIVED_ITEM_ID,
  gi.TRANSFER_ORDER_ITEM_ID,
  gi.REQUEST_ORDER_ITEM_ID,
  u.UNIT_NAME AS TRANSFERRING_UNIT,
  ux.UNIT_NAME AS REQUESTING_RECEIVING_UNIT,
  TRIM(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(p.PRODUCT_NAME,'"',''), '\n', ' '), '\r', ' '), '\t', ' '),',',' ')) PRODUCT_NAME,
  gi.SKU_NAME,
  ri.REQUESTED_QUANTITY,
  gi.TRANSFERRED_QUANTITY,
  gi.RECEIVED_QUANTITY,
  (gi.RECEIVED_QUANTITY * s.NEGOTIATED_UNIT_PRICE) AS COST,
  gi.UNIT_OF_MEASURE,
  s.NEGOTIATED_UNIT_PRICE AS UNIT_PRICE,
  c.CATEGORY_NAME,
  sc.SUB_CATEGORY_NAME,
  r.GENERATION_TIME AS REQUESTING_TIME,
  t.GENERATION_TIME AS TRANSFER_TIME,
  g.LAST_UPDATE_TIME AS RECEIVING_TIME,
  vd.ENTITY_NAME,
  TRIM(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(g.COMMENT,'"',''),  '\n', ' '), '\r', ' '), '\t', ' '),',',' ')) AS RECEIVING_COMMENT,
  TRIM(REPLACE(REPLACE(REPLACE(REPLACE(REPLACE(r.COMMENT,'"',''),  '\n', ' '), '\r', ' '), '\t', ' '),',',' ')) AS REQUESTING_COMMENT
FROM
  KETTLE_SCM_DUMP.GOODS_RECEIVED_ITEM gi
	  INNER JOIN
  KETTLE_SCM_DUMP.GOODS_RECEIVED g ON gi.GOODS_RECEIVED_ID = g.GOODS_RECEIVED_ID
	  AND g.GOODS_RECEIVED_STATUS = 'SETTLED'
	  AND DATE(g.LAST_UPDATE_TIME) >= :startDate
	  AND DATE(g.LAST_UPDATE_TIME) <= :endDate
	  AND g.GENERATION_UNIT_ID = :receivingUnitId
	  INNER JOIN
  KETTLE_SCM_DUMP.SKU_DEFINITION s ON s.SKU_ID = gi.SKU_ID
	  INNER JOIN
  KETTLE_SCM_DUMP.PRODUCT_DEFINITION p ON p.PRODUCT_ID = s.LINKED_PRODUCT_ID
	  INNER JOIN
  KETTLE_SCM_DUMP.REQUEST_ORDER_ITEM ri ON ri.REQUEST_ORDER_ITEM_ID = gi.REQUEST_ORDER_ITEM_ID
	  INNER JOIN
  KETTLE_SCM_DUMP.UNIT_DETAIL u ON u.UNIT_ID = g.GENERATION_UNIT_ID
	  INNER JOIN
  KETTLE_SCM_DUMP.UNIT_DETAIL ux ON ux.UNIT_ID = g.GENERATED_FOR_UNIT_ID
	  INNER JOIN
  KETTLE_SCM_DUMP.CATEGORY_DEFINITION c ON c.CATEGORY_ID = p.CATEGORY_ID
	  INNER JOIN
  KETTLE_SCM_DUMP.SUB_CATEGORY_DEFINITION sc ON sc.SUB_CATEGORY_ID = p.SUB_CATEGORY_ID
	  INNER JOIN
  KETTLE_SCM_DUMP.REQUEST_ORDER r ON r.REQUEST_ORDER_ID = g.REQUEST_ORDER_ID
	  INNER JOIN
  KETTLE_SCM_DUMP.TRANSFER_ORDER t ON t.TRANSFER_ORDER_ID = g.TRANSFER_ORDER_ID
  LEFT JOIN KETTLE_SCM_DUMP.VENDOR_DETAIL_DATA vd ON vd.VENDOR_ID=ri.VENDOR_ID) T
                        ]]>
					</content>
					<params>
						<param name="receivingUnitId" displayName="Unit Id"
                               dataType="INTEGER"/>
						<param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
					</params>

				</report>
				<report name="Goods Received In Report Accross Units For Cosumables Non Marketing For Date Range"
                        executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
                            SELECT T.* FROM (SELECT
                            g.GOODS_RECEIVED_ID,
                            gi.GOODS_RECEIVED_ITEM_ID,
                            gi.TRANSFER_ORDER_ITEM_ID,
                            gi.REQUEST_ORDER_ITEM_ID,
                            u.UNIT_NAME AS TRANSFERRING_UNIT,
                            ux.UNIT_NAME AS REQUESTING_RECEIVING_UNIT,
                            p.PRODUCT_NAME,
                            gi.SKU_NAME,
                            ri.REQUESTED_QUANTITY,
                            gi.TRANSFERRED_QUANTITY,
                            gi.RECEIVED_QUANTITY,
                            (gi.RECEIVED_QUANTITY * s.NEGOTIATED_UNIT_PRICE) AS COST,
                            gi.UNIT_OF_MEASURE,
                            s.NEGOTIATED_UNIT_PRICE AS UNIT_PRICE,
                            c.CATEGORY_NAME,
                            sc.SUB_CATEGORY_NAME,
                            r.GENERATION_TIME AS REQUESTING_TIME,
                            t.GENERATION_TIME AS TRANSFER_TIME,
                            g.LAST_UPDATE_TIME AS RECEIVING_TIME,
                            vd.VENDOR_NAME,
                            g.COMMENT AS RECEIVING_COMMENT,
                            r.COMMENT AS REQUESTING_COMMENT
                        FROM
                            KETTLE_SCM_DUMP.GOODS_RECEIVED_ITEM gi
                                INNER JOIN
                            KETTLE_SCM_DUMP.GOODS_RECEIVED g ON gi.GOODS_RECEIVED_ID = g.GOODS_RECEIVED_ID
                                AND g.GOODS_RECEIVED_STATUS = 'SETTLED'
                                AND DATE(g.LAST_UPDATE_TIME) >= :startDate
                                AND DATE(g.LAST_UPDATE_TIME) <= :endDate
                                INNER JOIN
                            KETTLE_SCM_DUMP.SKU_DEFINITION s ON s.SKU_ID = gi.SKU_ID
                                INNER JOIN
                            KETTLE_SCM_DUMP.PRODUCT_DEFINITION p ON p.PRODUCT_ID = s.LINKED_PRODUCT_ID
                                INNER JOIN
                            KETTLE_SCM_DUMP.REQUEST_ORDER_ITEM ri ON ri.REQUEST_ORDER_ITEM_ID = gi.REQUEST_ORDER_ITEM_ID
                                INNER JOIN
                            KETTLE_SCM_DUMP.UNIT_DETAIL u ON u.UNIT_ID = g.GENERATION_UNIT_ID
                                INNER JOIN
                            KETTLE_SCM_DUMP.UNIT_DETAIL ux ON ux.UNIT_ID = g.GENERATED_FOR_UNIT_ID
                                INNER JOIN
                            KETTLE_SCM_DUMP.CATEGORY_DEFINITION c ON c.CATEGORY_ID = p.CATEGORY_ID
                                INNER JOIN
                            KETTLE_SCM_DUMP.SUB_CATEGORY_DEFINITION sc ON sc.SUB_CATEGORY_ID = p.SUB_CATEGORY_ID
                                INNER JOIN
                            KETTLE_SCM_DUMP.REQUEST_ORDER r ON r.REQUEST_ORDER_ID = g.REQUEST_ORDER_ID
                                INNER JOIN
                            KETTLE_SCM_DUMP.TRANSFER_ORDER t ON t.TRANSFER_ORDER_ID = g.TRANSFER_ORDER_ID
                            LEFT JOIN KETTLE_SCM_DUMP.VENDOR_DETAIL vd ON vd.VENDOR_ID=ri.VENDOR_ID) T
			WHERE T.CATEGORY_NAME = 'Consumables' 
			AND T.SUB_CATEGORY_NAME <> 'Marketing'

                        ]]>
					</content>
					<params>
						<param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
					</params>
				</report>
				<report name="Wastage Report Accross Units For Date Range for Less Quantity"
                        executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
    *
FROM
    (SELECT 
        T.LINKED_GR_ID,
            T.PRODUCT_ID,
            T.PRODUCT_NAME,
            T.CATEGORY_NAME,
            T.SUB_CATEGORY_NAME,
            uda.UNIT_NAME AS GENERATION_UNIT,
            T.UNIT_NAME AS GENERATED_FOR_UNIT,
            utm.UNIT_TYPE,
            ld.CITY,
            T.PRICE,
            T.QUANTITY,
            T.COST,
            T.UNIT_OF_MEASURE,
            T.BUSINESS_DATE,
            T.GENERATION_TIME,
            gr.RECEIVED_BY AS GR_REJECTED_BY,
            ed.EMP_NAME AS EMPLOYEE_NAME,
            T.COMMENT,
            CASE WHEN ctm.COMMENT_TYPE IS NOT NULL THEN
            ctm.COMMENT_TYPE
            ELSE
            'Cafe Wastage'
            END AS COMMENT_TYPE
    FROM
        (SELECT 
        we.LINKED_GR_ID,
            wid.PRODUCT_ID,
            pd.PRODUCT_NAME,
            cd.CATEGORY_NAME,
            scd.SUB_CATEGORY_NAME,
            ud.UNIT_NAME,
            wid.PRICE,
            wid.QUANTITY,
            wid.COST AS COST,
            pd.UNIT_OF_MEASURE,
            we.BUSINESS_DATE,
            we.GENERATION_TIME,
            we.GENERATED_BY,
            wid.COMMENT
    FROM
        KETTLE_SCM_DUMP.WASTAGE_ITEM_DATA wid
    INNER JOIN KETTLE_SCM_DUMP.WASTAGE_EVENT we ON wid.WASTAGE_ID = we.WASTAGE_ID
    INNER JOIN KETTLE_SCM_DUMP.PRODUCT_DEFINITION pd ON wid.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE_SCM_DUMP.CATEGORY_DEFINITION cd ON pd.CATEGORY_ID = cd.CATEGORY_ID
    INNER JOIN KETTLE_SCM_DUMP.SUB_CATEGORY_DEFINITION scd ON pd.SUB_CATEGORY_ID = scd.SUB_CATEGORY_ID
    INNER JOIN KETTLE_SCM_DUMP.UNIT_DETAIL ud ON we.UNIT_ID = ud.UNIT_ID
    WHERE
        we.STATUS = 'SETTLED'
            AND we.BUSINESS_DATE >= :startDate
            AND we.BUSINESS_DATE <= :endDate
            AND wid.COMMENT = 'Less Quantity') AS T
    INNER JOIN KETTLE_SCM_DUMP.GOODS_RECEIVED gr ON gr.GOODS_RECEIVED_ID = T.LINKED_GR_ID
    INNER JOIN KETTLE_MASTER_DUMP.EMPLOYEE_DETAIL ed ON ed.EMP_ID = gr.RECEIVED_BY
    INNER JOIN KETTLE_MASTER_DUMP.UNIT_DETAIL uda ON gr.GENERATED_FOR_UNIT_ID = uda.UNIT_ID
    INNER JOIN KETTLE_SCM_DUMP.UNIT_TYPE_MAPPING utm ON uda.UNIT_CATEGORY = utm.UNIT_CATEGORY
    INNER JOIN KETTLE_MASTER_DUMP.LOCATION_DETAIL ld ON uda.LOCATION_DETAIL_ID = ld.LOCATION_ID
    INNER JOIN KETTLE_SCM_DUMP.COMMENT_TYPE_MAPPING ctm ON ctm.COMMENT_VALUE = T.COMMENT
    ) AS X

                        ]]>
					</content>
					<params>
						<param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
					</params>

				</report>

				<report name="Wastage Report Accross Units For Date Range"
                        executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
    wid.PRODUCT_ID,
    pd.PRODUCT_NAME,
    cd.CATEGORY_NAME,
    scd.SUB_CATEGORY_NAME,
    ud.UNIT_NAME,
    utm.UNIT_TYPE,
    ld.CITY,
    wid.PRICE,
    wid.QUANTITY,
    wid.COST AS COST,
    pd.UNIT_OF_MEASURE,
    we.BUSINESS_DATE,
    we.GENERATION_TIME,
    we.GENERATED_BY,
    TRIM(REPLACE(REPLACE(REPLACE(REPLACE(wid.COMMENT, '\n', ' '), '\r', ' '), '\t', ' '),',',' ')) COMMENT,
    CASE
        WHEN ctm.COMMENT_TYPE IS NOT NULL THEN ctm.COMMENT_TYPE
        ELSE 'Cafe Wastage'
    END AS COMMENT_TYPE
FROM
    KETTLE_SCM_DUMP.WASTAGE_ITEM_DATA wid
        INNER JOIN
    KETTLE_SCM_DUMP.WASTAGE_EVENT we ON wid.WASTAGE_ID = we.WASTAGE_ID
        INNER JOIN
    KETTLE_SCM_DUMP.PRODUCT_DEFINITION pd ON wid.PRODUCT_ID = pd.PRODUCT_ID
        INNER JOIN
    KETTLE_SCM_DUMP.CATEGORY_DEFINITION cd ON pd.CATEGORY_ID = cd.CATEGORY_ID
        INNER JOIN
    KETTLE_SCM_DUMP.SUB_CATEGORY_DEFINITION scd ON pd.SUB_CATEGORY_ID = scd.SUB_CATEGORY_ID
        INNER JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON we.UNIT_ID = ud.UNIT_ID
        INNER JOIN
    KETTLE_SCM_DUMP.UNIT_TYPE_MAPPING utm ON ud.UNIT_CATEGORY = utm.UNIT_CATEGORY
        INNER JOIN
    KETTLE_MASTER_DUMP.LOCATION_DETAIL ld ON ud.LOCATION_DETAIL_ID = ld.LOCATION_ID
        INNER JOIN
    KETTLE_SCM_DUMP.COMMENT_TYPE_MAPPING ctm ON ctm.COMMENT_VALUE = wid.COMMENT
        AND we.STATUS = 'SETTLED'
        AND we.BUSINESS_DATE >= :startDate
        AND we.BUSINESS_DATE <= :endDate
                        ]]>
					</content>
					<params>
						<param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
					</params>

				</report>
				<report name="Transfer Data with TAX details across units for a date range"
			 executionType="SQL" returnType="java.lang.String">

					<content>
						<![CDATA[
				SELECT X.* FROM (SELECT ud.UNIT_NAME AS FROM_UNIT, udi.UNIT_NAME AS TO_UNIT, toi.TRANSFER_ORDER_ITEM_ID, toi.SKU_ID, toi.SKU_NAME, toi.REQUESTED_QUANTITY,
toi.REQUESTED_ABSOLUTE_QUANTITY, toi.TRANSFERRED_QUANTITY, toi.UNIT_OF_MEASURE, toi.NEGOTIATED_UNIT_PRICE,
toi.TRANSFER_ORDER_ID, toi.RECEIVED_QUANTITY, toi.CALCULATED_AMOUNT,
toi.TOTAL_TAX as ITEM_TAX_DETAIL, toi.TAX_CODE as HSN_CODE , toi.ORDER_ITEM_INVOICE_ID,
tr.GENERATION_TIME, tr.GENERATION_UNIT_ID, tr.GENERATED_FOR_UNIT_ID, tr.GENERATED_BY, tr.TRANSFER_ORDER_STATUS,
tr.COMMENT,tr.INITIATION_TIME, tr.LAST_UPDATE_TIME, tr.TOTAL_AMOUNT, tr.LAST_UPDATED_BY,
tr.CLOSURE_EVENT_ID, tr.TOTAL_TAX as TOTAL_TAX, tr.GENERATED_INVOICE_ID, tr.TRANSFER_TYPE,
ttd.ORDER_ITEM_TAX_DETAIL_ID, ttd.ORDER_ITEM_ID, ttd.TAX_CODE, ttd.TAX_TYPE, ttd.TOTAL_AMOUNT as ITEM_TOTAL_AMOUNT,
ttd.TAXABLE_AMOUNT as ITEM_TAXABLE_AMOUNT, ttd.TAX_PERCENTAGE, ttd.TOTAL_TAX as ITEM_TAX
FROM
    KETTLE_SCM_DUMP.TRANSFER_ORDER_ITEM toi
        INNER JOIN
    KETTLE_SCM_DUMP.TRANSFER_ORDER tr ON tr.TRANSFER_ORDER_ID = toi.TRANSFER_ORDER_ID
        INNER JOIN
    KETTLE_SCM_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = tr.GENERATION_UNIT_ID
        INNER JOIN
    KETTLE_SCM_DUMP.UNIT_DETAIL udi ON udi.UNIT_ID = tr.GENERATED_FOR_UNIT_ID
        LEFT OUTER JOIN
    KETTLE_SCM_DUMP.TRANSFER_ORDER_ITEM_TAX_DETAIL ttd ON ttd.ORDER_ITEM_ID = toi.TRANSFER_ORDER_ITEM_ID
WHERE
    tr.GENERATION_TIME >= :startDate
        AND tr.GENERATION_TIME <= :endDate) as X; 
			]]>
					</content>
					<params>
						<param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
					</params>
				</report>




				<report name="Special Order Report Accross Units For Date Range"
                        executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
						SELECT
                            u.UNIT_NAME,
                            ro.REQUEST_ORDER_ID,
                            ro.GENERATION_TIME,
                            roi.PRODUCT_NAME,
                            roi.REQUESTED_ABSOLUTE_QUANTITY,
                            roi.RECEIVED_QUANTITY,
                            roi.UNIT_OF_MEASURE
                        FROM
                            KETTLE_SCM_DUMP.REQUEST_ORDER ro
                                INNER JOIN
                            KETTLE_SCM_DUMP.REQUEST_ORDER_ITEM roi ON ro.REQUEST_ORDER_ID = roi.REQUEST_ORDER_ID
                                AND ro.IS_SPECIAL_ORDER = 'Y'
                                AND ro.REQUEST_ORDER_STATUS = 'SETTLED'
                                AND ro.GENERATION_TIME >= :startDate
                                AND ro.GENERATION_TIME <= :endDate
                                INNER JOIN
                            KETTLE_SCM_DUMP.UNIT_DETAIL u ON u.UNIT_ID = ro.REQUEST_UNIT_ID
                        ORDER BY u.UNIT_ID, ro.REQUEST_ORDER_ID

                        ]]>
					</content>
					<params>
						<param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
					</params>

				</report>
				<report name="Milk Order Report Accross Units For Date Range"
                        executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
                            SELECT
                                ro.REQUEST_ORDER_ID,
                                u.UNIT_NAME,
                                ro.GENERATION_TIME,
                                ro.FULFILLMENT_DATE,
                                ro.LAST_UPDATE_TIME,
                                roi.REQUESTED_ABSOLUTE_QUANTITY,
                                roi.TRANSFERRED_QUANTITY,
                                roi.RECEIVED_QUANTITY,
                                roi.UNIT_OF_MEASURE,
                                e.EMP_NAME,
                                v.VENDOR_NAME,
                                v.PRIMARY_CONTACT,
                                v.PRIMARY_EMAIL
                            FROM
                                KETTLE_SCM_DUMP.REQUEST_ORDER ro
                                    INNER JOIN
                                KETTLE_SCM_DUMP.REQUEST_ORDER_ITEM roi ON ro.REQUEST_ORDER_ID = roi.REQUEST_ORDER_ID
                                    AND ro.IS_SPECIAL_ORDER = 'Y'
                                    AND roi.PRODUCT_ID = 100234
                                    AND ro.REQUEST_ORDER_STATUS = 'SETTLED'
                                    AND ro.LAST_UPDATE_TIME >= :startDate
                                    AND ro.LAST_UPDATE_TIME < :endDate
                                    INNER JOIN
                                KETTLE_SCM_DUMP.UNIT_DETAIL u ON u.UNIT_ID = ro.REQUEST_UNIT_ID
                                    INNER JOIN
                                KETTLE_MASTER_DUMP.EMPLOYEE_DETAIL e ON e.EMP_ID = ro.LAST_UPDATED_BY
                                    INNER JOIN
                                KETTLE_SCM_DUMP.VENDOR_DETAIL v ON roi.VENDOR_ID = v.VENDOR_ID

                        ]]>
					</content>
					<params>
						<param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
					</params>
				</report>
				<report name="Transfer Items Report Accross Units For Date Range"
                        executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
    T.*
FROM
    (SELECT 
        DATE(t.GENERATION_TIME) AS GENERATION_DATE,
            t.GENERATED_BY,
            t.GENERATED_FOR_UNIt_ID AS UNIT_ID,
            ud.UNIT_NAME AS UNIT_NAME,
            t.GENERATION_UNIT_ID AS SUPPLIER_ID,
            ud1.UNIT_NAME AS SUPPLIER_NAME,
            ti.SKU_NAME AS PRODUCT,
			cd.CATEGORY_NAME,
            scd.SUB_CATEGORY_NAME,
            ti.UNIT_OF_MEASURE,
            ti.UNIT_PRICE,
            ti.REQUESTED_QUANTITY,
            ti.TRANSFERRED_QUANTITY,
            ti.RECEIVED_QUANTITY,
            ro.FULFILLMENT_DATE
    FROM
        KETTLE_SCM_DUMP.TRANSFER_ORDER t
    LEFT JOIN KETTLE_SCM_DUMP.TRANSFER_ORDER_ITEM ti ON t.TRANSFER_ORDER_ID = ti.TRANSFER_ORDER_ID
    AND t.TRANSFER_ORDER_STATUS <> "CANCELLED"
    INNER JOIN KETTLE_SCM_DUMP.UNIT_DETAIL ud ON t.GENERATED_FOR_UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE_SCM_DUMP.UNIT_DETAIL ud1 ON t.GENERATION_UNIT_ID = ud1.UNIT_ID
    INNER JOIN KETTLE_SCM_DUMP.SKU_DEFINITION s ON ti.SKU_ID = s.SKU_ID
	INNER JOIN KETTLE_SCM_DUMP.PRODUCT_DEFINITION pd ON s.LINKED_PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE_SCM_DUMP.CATEGORY_DEFINITION cd ON pd.CATEGORY_ID = cd.CATEGORY_ID
    INNER JOIN KETTLE_SCM_DUMP.SUB_CATEGORY_DEFINITION scd ON pd.SUB_CATEGORY_ID = scd.SUB_CATEGORY_ID
    INNER JOIN KETTLE_SCM_DUMP.REQUEST_ORDER ro ON t.REQUEST_ORDER_ID = ro.REQUEST_ORDER_ID
		AND t.GENERATION_TIME >= :startDate
		AND t.GENERATION_TIME <= :endDate) T
			]]>
					</content>
					<params>
						<param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
					</params>
				</report>
				<report name="Transfer Items Report For Specific Unit For Date Range"
                        executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
                            SELECT T.* FROM (SELECT
                                DATE(t.GENERATION_TIME) AS GENERATION_DATE,
                                t.GENERATED_BY,
                                t.GENERATED_FOR_UNIt_ID AS UNIT_ID,
                                ud.UNIT_NAME AS UNIT_NAME,
                                t.GENERATION_UNIT_ID AS SUPPLIER_ID,
                                ud1.UNIT_NAME AS SUPPLIER_NAME,
                                ti.SKU_NAME AS PRODUCT,
								cd.CATEGORY_NAME,
								scd.SUB_CATEGORY_NAME,
                                ti.UNIT_OF_MEASURE,
                                ti.UNIT_PRICE,
                                ti.REQUESTED_QUANTITY,
                                ti.TRANSFERRED_QUANTITY,
                                ti.RECEIVED_QUANTITY
                            FROM
                                KETTLE_SCM_DUMP.TRANSFER_ORDER t
                                    LEFT JOIN
                                KETTLE_SCM_DUMP.TRANSFER_ORDER_ITEM ti ON t.TRANSFER_ORDER_ID = ti.TRANSFER_ORDER_ID
                                    AND t.GENERATION_UNIT_ID = :transferringUnitId
                                    AND t.GENERATED_FOR_UNIT_ID = :receivingUnitId
                                    INNER JOIN
                                KETTLE_SCM_DUMP.UNIT_DETAIL ud ON t.GENERATED_FOR_UNIT_ID = ud.UNIT_ID
                                    INNER JOIN
                                KETTLE_SCM_DUMP.UNIT_DETAIL ud1 ON t.GENERATION_UNIT_ID = ud1.UNIT_ID
                                    INNER JOIN
                                KETTLE_SCM_DUMP.SKU_DEFINITION s ON ti.SKU_ID = s.SKU_ID
								INNER JOIN KETTLE_SCM_DUMP.PRODUCT_DEFINITION pd ON s.LINKED_PRODUCT_ID = pd.PRODUCT_ID
								INNER JOIN KETTLE_SCM_DUMP.CATEGORY_DEFINITION cd ON pd.CATEGORY_ID = cd.CATEGORY_ID
								INNER JOIN KETTLE_SCM_DUMP.SUB_CATEGORY_DEFINITION scd ON pd.SUB_CATEGORY_ID = scd.SUB_CATEGORY_ID
                                    AND t.GENERATION_TIME >= :startDate
                                    AND t.GENERATION_TIME <= :endDate) T
                        ]]>
					</content>
					<params>
						<param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="transferringUnitId" displayName="Transferring Unit Id"
                               dataType="INTEGER" />
						<param name="receivingUnitId" displayName="Receiving Unit Id"
                               dataType="INTEGER" />
					</params>
				</report>
				<report name="External GR Report Accross Units For Date Range"
                        executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT * FROM (
SELECT 
    i.GOODS_RECEIVED_ITEM_ID,
    i.VENDOR_GR_ID,
    i.SKU_ID,
    i.HSN_CODE,
    i.SKU_NAME,
    cd.CATEGORY_CODE,
    scd.SUB_CATEGORY_CODE,
    i.PACKAGING_NAME,
    i.PACKAGING_QUANTITY,
    i.CONVERSION_RATIO,
    i.RECEIVED_QUANTITY,
    i.UNIT_OF_MEASURE,
    i.UNIT_PRICE,
    i.TOTAL_PRICE,
    i.TOTAL_TAX,
    i.TOTAL_AMOUNT,
    gr.CREATED_AT,
    gr.DOCUMENT_UPLOADED,
    gr.DOCUMENT_NUMBER,
    gr.TOTAL_PRICE AS 'TOTAL_GR_PRICE',
    gr.TOTAL_TAX AS 'TOTAL_GR_TAX',
    gr.TOTAL_AMOUNT AS 'TOTAL_GR_AMOUNT',
    gr.EXTRA_CHARGES,
    gr.DOCUMENT_DATE,
    gr.AMOUNT_MATCHED,
    ud.UNIT_NAME AS 'RECEIVING_UNIT',
    v.ENTITY_NAME AS 'VENDOR_NAME',
    a.STATE AS 'DISPATH_LOCATION_STATE',
    t.CGST,
    t.CGST_VALUE,
    t.SGST,
    t.SGST_VALUE,
    t.IGST,
    t.IGST_VALUE
FROM
     KETTLE_SCM_DUMP.VENDOR_GOODS_RECEIVED_DATA gr
        LEFT JOIN
     KETTLE_SCM_DUMP.VENDOR_GR_ITEM_DETAIL i ON gr.GOODS_RECEIVED_ID = i.VENDOR_GR_ID
     LEFT JOIN KETTLE_SCM_DUMP.SKU_DEFINITION sd ON i.SKU_ID = sd.SKU_ID
     LEFT JOIN KETTLE_SCM_DUMP.PRODUCT_DEFINITION pd ON sd.LINKED_PRODUCT_ID = pd.PRODUCT_ID
     LEFT JOIN KETTLE_SCM_DUMP.CATEGORY_DEFINITION cd ON pd.CATEGORY_ID = cd.CATEGORY_ID
     LEFT JOIN KETTLE_SCM_DUMP.SUB_CATEGORY_DEFINITION scd ON pd.SUB_CATEGORY_ID = scd.SUB_CATEGORY_ID
        LEFT JOIN
     KETTLE_SCM_DUMP.VENDOR_DETAIL_DATA v ON v.VENDOR_ID = gr.VENDOR_ID
        LEFT JOIN
     KETTLE_SCM_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = gr.DELIVERY_UNIT_ID
        LEFT JOIN
     KETTLE_SCM_DUMP.VENDOR_DISPATCH_LOCATIONS l ON l.DISPATCH_LOCATION_ID = gr.DISPATCH_ID
        LEFT JOIN
     KETTLE_SCM_DUMP.ADDRESS_DETAIL_DATA a ON a.ADDRESS_ID = l.LOCATION_ADDRESS_ID
        LEFT JOIN
    (SELECT 
        GR_ITEM_ID,
            SUM(CASE
                WHEN TAX_TYPE = 'CGST' THEN TAX_PERCENTAGE
                ELSE 0
            END) AS CGST,
            SUM(CASE
                WHEN TAX_TYPE = 'CGST' THEN TAX_VALUE
                ELSE 0
            END) AS CGST_VALUE,
            SUM(CASE
                WHEN TAX_TYPE = 'SGST' THEN TAX_PERCENTAGE
                ELSE 0
            END) AS SGST,
            SUM(CASE
                WHEN TAX_TYPE = 'SGST' THEN TAX_VALUE
                ELSE 0
            END) AS SGST_VALUE,
            SUM(CASE
                WHEN TAX_TYPE = 'IGST' THEN TAX_PERCENTAGE
                ELSE 0
            END) AS IGST,
            SUM(CASE
                WHEN TAX_TYPE = 'IGST' THEN TAX_VALUE
                ELSE 0
            END) AS IGST_VALUE
    FROM
         KETTLE_SCM_DUMP.ITEM_TAX_DETAIL_DATA
    GROUP BY GR_ITEM_ID) t ON t.GR_ITEM_ID = i.GOODS_RECEIVED_ITEM_ID
    WHERE gr.CREATED_AT BETWEEN :startDate AND :endDate ) z
			 ]]>
					</content>
					<params>
						<param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
					</params>
				</report>
<report name="External GR Report Accross Units For Date Range by Vendor"
                        executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT * FROM (
SELECT 
    i.GOODS_RECEIVED_ITEM_ID,
    i.VENDOR_GR_ID,
    i.SKU_ID,
    i.HSN_CODE,
    i.SKU_NAME,
    cd.CATEGORY_CODE,
    scd.SUB_CATEGORY_CODE,
    i.PACKAGING_NAME,
    i.PACKAGING_QUANTITY,
    i.CONVERSION_RATIO,
    i.RECEIVED_QUANTITY,
    i.UNIT_OF_MEASURE,
    i.UNIT_PRICE,
    i.TOTAL_PRICE,
    i.TOTAL_TAX,
    i.TOTAL_AMOUNT,
    gr.CREATED_AT,
    gr.DOCUMENT_UPLOADED,
    gr.DOCUMENT_NUMBER,
    gr.TOTAL_PRICE AS 'TOTAL_GR_PRICE',
    gr.TOTAL_TAX AS 'TOTAL_GR_TAX',
    gr.TOTAL_AMOUNT AS 'TOTAL_GR_AMOUNT',
    gr.EXTRA_CHARGES,
    gr.DOCUMENT_DATE,
    gr.AMOUNT_MATCHED,
    ud.UNIT_NAME AS 'RECEIVING_UNIT',
    v.ENTITY_NAME AS 'VENDOR_NAME',
    a.STATE AS 'DISPATH_LOCATION_STATE',
    t.CGST,
    t.CGST_VALUE,
    t.SGST,
    t.SGST_VALUE,
    t.IGST,
    t.IGST_VALUE
FROM
     KETTLE_SCM_DUMP.VENDOR_GOODS_RECEIVED_DATA gr
        LEFT JOIN
     KETTLE_SCM_DUMP.VENDOR_GR_ITEM_DETAIL i ON gr.GOODS_RECEIVED_ID = i.VENDOR_GR_ID
     LEFT JOIN KETTLE_SCM_DUMP.SKU_DEFINITION sd ON i.SKU_ID = sd.SKU_ID
     LEFT JOIN KETTLE_SCM_DUMP.PRODUCT_DEFINITION pd ON sd.LINKED_PRODUCT_ID = pd.PRODUCT_ID
     LEFT JOIN KETTLE_SCM_DUMP.CATEGORY_DEFINITION cd ON pd.CATEGORY_ID = cd.CATEGORY_ID
     LEFT JOIN KETTLE_SCM_DUMP.SUB_CATEGORY_DEFINITION scd ON pd.SUB_CATEGORY_ID = scd.SUB_CATEGORY_ID
        LEFT JOIN
     KETTLE_SCM_DUMP.VENDOR_DETAIL_DATA v ON v.VENDOR_ID = gr.VENDOR_ID
        LEFT JOIN
     KETTLE_SCM_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = gr.DELIVERY_UNIT_ID
        LEFT JOIN
     KETTLE_SCM_DUMP.VENDOR_DISPATCH_LOCATIONS l ON l.DISPATCH_LOCATION_ID = gr.DISPATCH_ID
        LEFT JOIN
     KETTLE_SCM_DUMP.ADDRESS_DETAIL_DATA a ON a.ADDRESS_ID = l.LOCATION_ADDRESS_ID
        LEFT JOIN
    (SELECT 
        GR_ITEM_ID,
            SUM(CASE
                WHEN TAX_TYPE = 'CGST' THEN TAX_PERCENTAGE
                ELSE 0
            END) AS CGST,
            SUM(CASE
                WHEN TAX_TYPE = 'CGST' THEN TAX_VALUE
                ELSE 0
            END) AS CGST_VALUE,
            SUM(CASE
                WHEN TAX_TYPE = 'SGST' THEN TAX_PERCENTAGE
                ELSE 0
            END) AS SGST,
            SUM(CASE
                WHEN TAX_TYPE = 'SGST' THEN TAX_VALUE
                ELSE 0
            END) AS SGST_VALUE,
            SUM(CASE
                WHEN TAX_TYPE = 'IGST' THEN TAX_PERCENTAGE
                ELSE 0
            END) AS IGST,
            SUM(CASE
                WHEN TAX_TYPE = 'IGST' THEN TAX_VALUE
                ELSE 0
            END) AS IGST_VALUE
    FROM
         KETTLE_SCM_DUMP.ITEM_TAX_DETAIL_DATA
    GROUP BY GR_ITEM_ID) t ON t.GR_ITEM_ID = i.GOODS_RECEIVED_ITEM_ID
    WHERE gr.CREATED_AT BETWEEN :startDate AND :endDate AND v.ENTITY_NAME LIKE CONCAT('%',:vendorName,'%')) z
			 ]]>
					</content>
					<params>
						<param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="vendorName" displayName="vendor Name"
                               dataType="STRING"/>
					</params>
				</report>
<report name="External GR Report Accross Units For Date Range by SKU Name"
                        executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT * FROM (
SELECT 
    i.GOODS_RECEIVED_ITEM_ID,
    i.VENDOR_GR_ID,
    i.SKU_ID,
    i.HSN_CODE,
    i.SKU_NAME,
    cd.CATEGORY_CODE,
    scd.SUB_CATEGORY_CODE,
    i.PACKAGING_NAME,
    i.PACKAGING_QUANTITY,
    i.CONVERSION_RATIO,
    i.RECEIVED_QUANTITY,
    i.UNIT_OF_MEASURE,
    i.UNIT_PRICE,
    i.TOTAL_PRICE,
    i.TOTAL_TAX,
    i.TOTAL_AMOUNT,
    gr.CREATED_AT,
    gr.DOCUMENT_UPLOADED,
    gr.DOCUMENT_NUMBER,
    gr.TOTAL_PRICE AS 'TOTAL_GR_PRICE',
    gr.TOTAL_TAX AS 'TOTAL_GR_TAX',
    gr.TOTAL_AMOUNT AS 'TOTAL_GR_AMOUNT',
    gr.EXTRA_CHARGES,
    gr.DOCUMENT_DATE,
    gr.AMOUNT_MATCHED,
    ud.UNIT_NAME AS 'RECEIVING_UNIT',
    v.ENTITY_NAME AS 'VENDOR_NAME',
    a.STATE AS 'DISPATH_LOCATION_STATE',
    t.CGST,
    t.CGST_VALUE,
    t.SGST,
    t.SGST_VALUE,
    t.IGST,
    t.IGST_VALUE
FROM
     KETTLE_SCM_DUMP.VENDOR_GOODS_RECEIVED_DATA gr
        LEFT JOIN
     KETTLE_SCM_DUMP.VENDOR_GR_ITEM_DETAIL i ON gr.GOODS_RECEIVED_ID = i.VENDOR_GR_ID
     LEFT JOIN KETTLE_SCM_DUMP.SKU_DEFINITION sd ON i.SKU_ID = sd.SKU_ID
     LEFT JOIN KETTLE_SCM_DUMP.PRODUCT_DEFINITION pd ON sd.LINKED_PRODUCT_ID = pd.PRODUCT_ID
     LEFT JOIN KETTLE_SCM_DUMP.CATEGORY_DEFINITION cd ON pd.CATEGORY_ID = cd.CATEGORY_ID
     LEFT JOIN KETTLE_SCM_DUMP.SUB_CATEGORY_DEFINITION scd ON pd.SUB_CATEGORY_ID = scd.SUB_CATEGORY_ID
        LEFT JOIN
     KETTLE_SCM_DUMP.VENDOR_DETAIL_DATA v ON v.VENDOR_ID = gr.VENDOR_ID
        LEFT JOIN
     KETTLE_SCM_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = gr.DELIVERY_UNIT_ID
        LEFT JOIN
     KETTLE_SCM_DUMP.VENDOR_DISPATCH_LOCATIONS l ON l.DISPATCH_LOCATION_ID = gr.DISPATCH_ID
        LEFT JOIN
     KETTLE_SCM_DUMP.ADDRESS_DETAIL_DATA a ON a.ADDRESS_ID = l.LOCATION_ADDRESS_ID
        LEFT JOIN
    (SELECT 
        GR_ITEM_ID,
            SUM(CASE
                WHEN TAX_TYPE = 'CGST' THEN TAX_PERCENTAGE
                ELSE 0
            END) AS CGST,
            SUM(CASE
                WHEN TAX_TYPE = 'CGST' THEN TAX_VALUE
                ELSE 0
            END) AS CGST_VALUE,
            SUM(CASE
                WHEN TAX_TYPE = 'SGST' THEN TAX_PERCENTAGE
                ELSE 0
            END) AS SGST,
            SUM(CASE
                WHEN TAX_TYPE = 'SGST' THEN TAX_VALUE
                ELSE 0
            END) AS SGST_VALUE,
            SUM(CASE
                WHEN TAX_TYPE = 'IGST' THEN TAX_PERCENTAGE
                ELSE 0
            END) AS IGST,
            SUM(CASE
                WHEN TAX_TYPE = 'IGST' THEN TAX_VALUE
                ELSE 0
            END) AS IGST_VALUE
    FROM
         KETTLE_SCM_DUMP.ITEM_TAX_DETAIL_DATA
    GROUP BY GR_ITEM_ID) t ON t.GR_ITEM_ID = i.GOODS_RECEIVED_ITEM_ID
    WHERE gr.CREATED_AT BETWEEN :startDate AND :endDate AND i.SKU_NAME LIKE CONCAT('%',:skuName,'%')) z
			 ]]>
					</content>
					<params>
						<param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="skuName" displayName="SKU Name"
                               dataType="STRING"/>
					</params>
				</report>

				<report name="External GR Report Accross Units For Date Range by GRN"
                        executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT * FROM (
SELECT 
    i.GOODS_RECEIVED_ITEM_ID,
    i.VENDOR_GR_ID,
    i.SKU_ID,
    i.HSN_CODE,
    i.SKU_NAME,
    cd.CATEGORY_CODE,
    scd.SUB_CATEGORY_CODE,
    i.PACKAGING_NAME,
    i.PACKAGING_QUANTITY,
    i.CONVERSION_RATIO,
    i.RECEIVED_QUANTITY,
    i.UNIT_OF_MEASURE,
    i.UNIT_PRICE,
    i.TOTAL_PRICE,
    i.TOTAL_TAX,
    i.TOTAL_AMOUNT,
    gr.CREATED_AT,
    gr.DOCUMENT_UPLOADED,
    gr.DOCUMENT_NUMBER,
    gr.TOTAL_PRICE AS 'TOTAL_GR_PRICE',
    gr.TOTAL_TAX AS 'TOTAL_GR_TAX',
    gr.TOTAL_AMOUNT AS 'TOTAL_GR_AMOUNT',
    gr.EXTRA_CHARGES,
    gr.DOCUMENT_DATE,
    gr.AMOUNT_MATCHED,
    ud.UNIT_NAME AS 'RECEIVING_UNIT',
    v.ENTITY_NAME AS 'VENDOR_NAME',
    a.STATE AS 'DISPATH_LOCATION_STATE',
    t.CGST,
    t.CGST_VALUE,
    t.SGST,
    t.SGST_VALUE,
    t.IGST,
    t.IGST_VALUE
FROM
     KETTLE_SCM_DUMP.VENDOR_GOODS_RECEIVED_DATA gr
        LEFT JOIN
     KETTLE_SCM_DUMP.VENDOR_GR_ITEM_DETAIL i ON gr.GOODS_RECEIVED_ID = i.VENDOR_GR_ID
     LEFT JOIN KETTLE_SCM_DUMP.SKU_DEFINITION sd ON i.SKU_ID = sd.SKU_ID
     LEFT JOIN KETTLE_SCM_DUMP.PRODUCT_DEFINITION pd ON sd.LINKED_PRODUCT_ID = pd.PRODUCT_ID
     LEFT JOIN KETTLE_SCM_DUMP.CATEGORY_DEFINITION cd ON pd.CATEGORY_ID = cd.CATEGORY_ID
     LEFT JOIN KETTLE_SCM_DUMP.SUB_CATEGORY_DEFINITION scd ON pd.SUB_CATEGORY_ID = scd.SUB_CATEGORY_ID
        LEFT JOIN
     KETTLE_SCM_DUMP.VENDOR_DETAIL_DATA v ON v.VENDOR_ID = gr.VENDOR_ID
        LEFT JOIN
     KETTLE_SCM_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = gr.DELIVERY_UNIT_ID
        LEFT JOIN
     KETTLE_SCM_DUMP.VENDOR_DISPATCH_LOCATIONS l ON l.DISPATCH_LOCATION_ID = gr.DISPATCH_ID
        LEFT JOIN
     KETTLE_SCM_DUMP.ADDRESS_DETAIL_DATA a ON a.ADDRESS_ID = l.LOCATION_ADDRESS_ID
        LEFT JOIN
    (SELECT 
        GR_ITEM_ID,
            SUM(CASE
                WHEN TAX_TYPE = 'CGST' THEN TAX_PERCENTAGE
                ELSE 0
            END) AS CGST,
            SUM(CASE
                WHEN TAX_TYPE = 'CGST' THEN TAX_VALUE
                ELSE 0
            END) AS CGST_VALUE,
            SUM(CASE
                WHEN TAX_TYPE = 'SGST' THEN TAX_PERCENTAGE
                ELSE 0
            END) AS SGST,
            SUM(CASE
                WHEN TAX_TYPE = 'SGST' THEN TAX_VALUE
                ELSE 0
            END) AS SGST_VALUE,
            SUM(CASE
                WHEN TAX_TYPE = 'IGST' THEN TAX_PERCENTAGE
                ELSE 0
            END) AS IGST,
            SUM(CASE
                WHEN TAX_TYPE = 'IGST' THEN TAX_VALUE
                ELSE 0
            END) AS IGST_VALUE
    FROM
         KETTLE_SCM_DUMP.ITEM_TAX_DETAIL_DATA
    GROUP BY GR_ITEM_ID) t ON t.GR_ITEM_ID = i.GOODS_RECEIVED_ITEM_ID
    WHERE gr.CREATED_AT BETWEEN :startDate AND :endDate AND gr.GOODS_RECEIVED_ID = :grnum) z
			 ]]>
					</content>
					<params>
						<param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="grnum" displayName="GRN"
                               dataType="STRING"/>
					</params>
				</report>
				<report name="Variance Summary Report For Date Range"
                        executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
    ud.UNIT_ID,
    ud.UNIT_NAME,
    dcad.FREQUENCY,
    dcad.BUSINESS_DATE,
    dcad.AGGREGATION_TYPE,
    dcad.AGGREGATION_CODE,
    dcad.AGGREGATION_DESC,
    dcad.VALUE_TODAY
FROM
    KETTLE_SCM_DUMP.DAY_CLOSURE_AGGREGATED_DATA dcad
        LEFT JOIN
    KETTLE_SCM_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = dcad.UNIT_ID
        LEFT JOIN
    KETTLE_SCM_DUMP.DAY_CLOSE_EVENT dce ON dce.EVENT_ID = dcad.EVENT_ID
WHERE
    dcad.AGGREGATION_CODE = 'PRODUCT_CATEGORY'
        AND dcad.AGGREGATION_TYPE = 'VARIANCE'
        AND dce.STATUS <> 'CANCELLED'
        AND dce.CLOSURE_EVENT_FREQUENCY IN ('DAILY','MONTHLY','FIXED_ASSETS')
        AND DATE(dcad.BUSINESS_DATE) >= :startDate
        AND DATE(dcad.BUSINESS_DATE) <= :endDate
ORDER BY dce.EVENT_ID DESC
			]]>
					</content>
					<params>
						<param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
					</params>
				</report>

				<report name="Variance Daily Warehouse/Kitchen"
                        		executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT 
    ud.UNIT_NAME,
    dce.BUSINESS_DATE,
    dce.GENERATION_TIME,
    sd.SKU_NAME,
    pd.PRODUCT_NAME,
    cd.CATEGORY_CODE,
    scd.SUB_CATEGORY_CODE,
    sd.UNIT_OF_MEASURE,
    inv.PRICE,
    inv.OPENING,
    coalesce(inv.TRANSFERRED,0) as TRANSFERRED,
    coalesce(inv.RECEIVED,0) as RECEIVED,
    coalesce(inv.CONSUMPTION,0) as CONSUMPTION,
    coalesce(inv.BOOKED,0) as BOOKING,
    coalesce(inv.WASTED,0) as WASTED,
    coalesce(inv.EXPECTED_CLOSING,0) as SYSTEM_CLOSING,
    coalesce(inv.ACTUAL_CLOSING,0) as ACTUAL_CLOSING,
    coalesce(inv.VARIANCE,0) as VARIANCE,
    coalesce(inv.VARIANCE_COST,0) as VARIANCE_COST
FROM
    KETTLE_SCM_DUMP.DAY_CLOSE_EVENT dce
        INNER JOIN
    KETTLE_SCM_DUMP.INVENTORY_DRILLDOWN inv ON dce.EVENT_ID = inv.CLOSURE_EVENT_ID
        INNER JOIN
    KETTLE_SCM_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = dce.UNIT_ID
        INNER JOIN
    KETTLE_SCM_DUMP.SKU_DEFINITION sd ON inv.SKU_ID = sd.SKU_ID
        INNER JOIN
    KETTLE_SCM_DUMP.PRODUCT_DEFINITION pd ON sd.LINKED_PRODUCT_ID = pd.PRODUCT_ID
        INNER JOIN
    KETTLE_SCM_DUMP.CATEGORY_DEFINITION cd ON pd.CATEGORY_ID = cd.CATEGORY_ID
        INNER JOIN
    KETTLE_SCM_DUMP.SUB_CATEGORY_DEFINITION scd ON pd.SUB_CATEGORY_ID = scd.SUB_CATEGORY_ID
WHERE
    dce.BUSINESS_DATE >= :startDate and dce.BUSINESS_DATE <= :endDate and dce.STATUS <> "CANCELLED" order by dce.UNIT_ID;					]]>
					</content>
					<params>
						<param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
					</params>

				</report>
				<report name="SKU Data"
                        executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT
   *
FROM
   KETTLE_SCM_DUMP.SKU_DEFINITION sd
       INNER JOIN
   KETTLE_SCM_DUMP.PRODUCT_DEFINITION pd ON sd.LINKED_PRODUCT_ID = pd.PRODUCT_ID
       LEFT JOIN
   KETTLE_SCM_DUMP.CATEGORY_DEFINITION cd ON cd.CATEGORY_ID = pd.CATEGORY_ID
       LEFT JOIN
   KETTLE_SCM_DUMP.SUB_CATEGORY_DEFINITION scd ON scd.SUB_CATEGORY_ID = pd.SUB_CATEGORY_ID
]]>
					</content>
				</report>
				<report name="COGS Data" executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[

SELECT 
    ud.UNIT_ID,
    ud.UNIT_NAME,
    CONSUMPTION_TYPE,
    SUM(dd.CONSUMPTION * dv.CONSUMPTION_PRICE) CONSUMPTION_COST
FROM
    KETTLE_SCM_DUMP.DAY_CLOSE_EVENT d INNER JOIN 
    KETTLE_SCM_DUMP.DAY_CLOSE_PRODUCT_VALUES dv ON d.EVENT_ID = dv.EVENT_ID AND d.STATUS <> 'CANCELLED'
        INNER JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON dv.UNIT_ID = ud.UNIT_ID
        INNER JOIN
    KETTLE_SCM_DUMP.PRODUCT_CONSUMPTION_DRILLDOWN dd ON dv.PRODUCT_ID = dd.PRODUCT_ID
        AND dv.EVENT_ID = dd.EVENT_ID
        AND d.BUSINESS_DATE BETWEEN :startDate AND :endDate
GROUP BY ud.UNIT_ID , CONSUMPTION_TYPE
						]]>
					</content>
					<params>
						<param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
					</params>
				</report>
<report name="COGS Data Product Wise" executionType="SQL" returnType="java.lang.String">
					<content>
						<![CDATA[
SELECT * FROM (SELECT 
    ud.UNIT_ID,
    ud.UNIT_NAME,
    pd.PRODUCT_ID,
    pd.PRODUCT_NAME,
    pd.UNIT_OF_MEASURE,
    CONSUMPTION_TYPE,
    dd.CONSUMPTION CONSUMPTION_QUANTITY,
    SUM(dd.CONSUMPTION * dv.CONSUMPTION_PRICE) CONSUMPTION_COST
FROM
    KETTLE_SCM_DUMP.DAY_CLOSE_EVENT d
        INNER JOIN
    KETTLE_SCM_DUMP.DAY_CLOSE_PRODUCT_VALUES dv ON d.EVENT_ID = dv.EVENT_ID
        AND d.STATUS <> 'CANCELLED'
        INNER JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON dv.UNIT_ID = ud.UNIT_ID
        INNER JOIN
    KETTLE_SCM_DUMP.PRODUCT_CONSUMPTION_DRILLDOWN dd ON dv.PRODUCT_ID = dd.PRODUCT_ID
        INNER JOIN
    KETTLE_SCM_DUMP.PRODUCT_DEFINITION pd ON dd.PRODUCT_ID = pd.PRODUCT_ID
        AND dv.EVENT_ID = dd.EVENT_ID
        AND dv.BUSINESS_DATE BETWEEN :startDate AND :endDate
	AND ud.UNIT_ID = :unitId
GROUP BY ud.UNIT_ID , CONSUMPTION_TYPE , pd.PRODUCT_ID) T;
    
	]]>
					</content>
					<params>
						<param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="unitId" displayName="Unit Id"
                               dataType="INTEGER"/>
					</params>
				</report>
				<report name="Payment Request Data" returnType="java.lang.String">
					<content>
						<![CDATA[

SELECT T.* FROM (SELECT 
    pr.PAYMENT_REQUEST_ID AS PR_ID,
    pi.INVOICE_NUMBER AS INVOICE_NUMBER,
    pr.CREATION_TIME AS CREATION_TIME,
    pi.INVOICE_DATE AS INVOICE_DATE,
    v.ENTITY_NAME AS VENDOR_NAME,
    vc.CREDIT_CYCLE AS CREDIT_CYCLE,
    u.UNIT_NAME AS UNIT_NAME,
    pr.PROPOSED_AMOUNT AS PROPOSED_AMOUNT,
    pr.PAID_AMOUNT AS PAID_AMOUNT,
    pr.CURRENT_STATUS AS CURRENT_STATUS,
    pii.SKU_ID AS SKU_ID,
    pii.SKU_NAME AS SKU_NAME,
    pii.UOM AS UOM,
    pii.QUANTITY AS QUANTITY,
    pii.PACKAGING_NAME AS PACKAGING_NAME,
    pii.TOTAL_AMOUNT AS TOTAL_AMOUNT,
    pii.TOTAL_TAX AS TOTAL_TAX,
    pii.TOTAL_PRICE AS TOTAL_PRICE,
    pii.UNIT_PRICE AS UNIT_PRICE,
    pii.PACKAGING_PRICE AS PACKAGING_PRICE,
    c.CATEGORY_NAME AS CATEGORY_NAME
FROM
    KETTLE_SCM_DUMP.PAYMENT_REQUEST pr
        INNER JOIN
    KETTLE_SCM_DUMP.PAYMENT_INVOICE pi ON pr.PAYMENT_REQUEST_ID = pi.PAYMENT_REQUEST_ID AND DATE(pr.CREATION_TIME) >= :startDate AND DATE(pr.CREATION_TIME) <= :endDate
		INNER JOIN 
    KETTLE_SCM_DUMP.PAYMENT_INVOICE_ITEM pii ON pi.PAYMENT_INVOICE_ID = pii.PAYMENT_INVOICE_ID
		INNER JOIN 
	KETTLE_SCM_DUMP.SKU_DEFINITION s ON s.SKU_ID=pii.SKU_ID
		INNER JOIN
	KETTLE_SCM_DUMP.PRODUCT_DEFINITION p ON p.PRODUCT_ID = s.LINKED_PRODUCT_ID
		INNER JOIN
	KETTLE_SCM_DUMP.CATEGORY_DEFINITION c ON c.CATEGORY_ID = p.CATEGORY_ID
        INNER JOIN
    KETTLE_SCM_DUMP.VENDOR_DETAIL_DATA v ON v.VENDOR_ID = pr.VENDOR_ID
        INNER JOIN
    KETTLE_SCM_DUMP.VENDOR_COMPANY_DETAIL vc ON vc.VENDOR_ID = v.VENDOR_ID
        INNER JOIN
    KETTLE_SCM_DUMP.UNIT_DETAIL u ON u.UNIT_ID = pr.REQUESTING_UNIT) T
						]]>
					</content>
					<params>
						<param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
					</params>
				</report>


<report name="Variance Calculated across units for Date Range - [NEW]"
                        executionType="SQL" returnType="java.lang.String">
					<content>

<![CDATA[
SELECT * FROM ( 
SELECT 
    ud.UNIT_ID,
    ud.UNIT_NAME,
    utm.UNIT_TYPE,
    ld.CITY,
    si.BUSINESS_DATE,
    si.PRODUCT_ID,
    TRIM(REPLACE(REPLACE(REPLACE(REPLACE(pd.PRODUCT_NAME, '\n', ' '), '\r', ' '), '\t', ' '),',',' ')) PRODUCT_NAME,
    pd.UNIT_OF_MEASURE,
    cd.CATEGORY_NAME,
    sd.SUB_CATEGORY_NAME,
    si.OPENING_STOCK,
    COALESCE(dcpv.TRANSFER_OUT, 0) AS TRANSFERRED,
    COALESCE(dcpv.WASTAGE, 0) AS WASTAGE,
    COALESCE(dcpv.RECEIVED, 0) AS RECEIVED,
    COALESCE(dcpv.CONSUMPTION, 0) AS CONSUMPTION,
    si.CLOSING_STOCK,
    si.EXPECTED_CLOSING_VALUE,
    si.VARIANCE,
    si.VARIANCE_COST AS COST,
    si.VARIANCE_PRICE,
    pd.VARIANCE_TYPE,
    pd.STOCK_KEEPING_FREQUENCY,
    CASE
        WHEN pd.SHELF_LIFE_IN_DAYS = - 1 THEN 'NO'
        ELSE 'YES'
    END AS EXPIRABLE
FROM
    KETTLE_SCM_DUMP.STOCK_INVENTORY si
        LEFT OUTER JOIN
    KETTLE_SCM_DUMP.DAY_CLOSE_PRODUCT_VALUES dcpv ON dcpv.EVENT_ID = si.CURRENT_EVENT_ID
        AND si.PRODUCT_ID = dcpv.PRODUCT_ID
        AND dcpv.EVENT_ID = si.CURRENT_EVENT_ID
        AND dcpv.STOCK_TYPE=si.STOCK_TYPE
        INNER JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = si.UNIT_ID
        INNER JOIN
    KETTLE_SCM_DUMP.PRODUCT_DEFINITION pd ON pd.PRODUCT_ID = si.PRODUCT_ID
        INNER JOIN
    KETTLE_SCM_DUMP.CATEGORY_DEFINITION cd ON cd.CATEGORY_ID = pd.CATEGORY_ID
        INNER JOIN
    KETTLE_SCM_DUMP.SUB_CATEGORY_DEFINITION sd ON sd.SUB_CATEGORY_ID = pd.SUB_CATEGORY_ID
        LEFT JOIN
    KETTLE_SCM_DUMP.UNIT_TYPE_MAPPING utm ON ud.UNIT_CATEGORY = utm.UNIT_CATEGORY
        LEFT JOIN
    KETTLE_MASTER_DUMP.LOCATION_DETAIL ld ON ud.LOCATION_DETAIL_ID = ld.LOCATION_ID
        INNER JOIN
    KETTLE_SCM_DUMP.DAY_CLOSE_EVENT dce ON dce.EVENT_ID = si.CURRENT_EVENT_ID
WHERE
    si.BUSINESS_DATE >= :startDate
		AND si.BUSINESS_DATE <= :endDate
        AND dce.CLOSURE_EVENT_TYPE = 'STOCK_TAKE'
        AND si.STATUS = 'CLOSED'
        AND dce.STATUS = 'CLOSED'
        AND si.STOCK_TYPE= :stockType
ORDER BY si.UNIT_ID) s;

                        ]]>
					</content>
					<params>
						<param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="stockType" displayName="Variance Type"
                               dataType="STRING" format=""/>
					</params>
				</report>
<report name="Stock Data Across Units">
<content>
<![CDATA[

select ud.UNIT_ID, ud.UNIT_NAME, pd.PRODUCT_NAME, se.UNIT_OF_MEASURE, cdd.PRICE, se.CURRENT_STOCK, (se.CURRENT_STOCK * cdd.PRICE) as COST
 from 
KETTLE_SCM_DUMP.STOCK_ENTRY se
INNER JOIN
KETTLE_SCM_DUMP.DAY_CLOSE_EVENT dce on se.UPDATE_EVENT_ID=dce.EVENT_ID
INNER JOIN  
KETTLE_SCM_DUMP.PRODUCT_DEFINITION pd on pd.PRODUCT_ID=se.PRODUCT_ID
INNER JOIN 
KETTLE_SCM_DUMP.UNIT_DETAIL ud on ud.UNIT_ID=dce.UNIT_ID
INNER JOIN 
KETTLE_SCM_DUMP.COST_DETAIL_DATA cdd on cdd.KEY_ID=pd.PRODUCT_ID and cdd.UNIT_ID=ud.UNIT_ID and cdd.KEY_TYPE="PRODUCT" and IS_LATEST="Y"
where 
dce.BUSINESS_DATE BETWEEN :startDate and :endDate and dce.STATUS<>"CANCELLED" and dce.CLOSURE_EVENT_TYPE="STOCK_TAKE" and dce.CLOSURE_EVENT_FREQUENCY= :stockType
order by ud.UNIT_ID, pd.PRODUCT_ID
]]>
</content>

<params>
						<param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="stockType" displayName="Stock Type"
                               dataType="STRING" format=""/>
</params>
</report>

<report name="Variance Calculated for a Unit for Date Range - [NEW]"
                        executionType="SQL" returnType="java.lang.String">
					<content>

<![CDATA[
SELECT * FROM ( 
SELECT 
    ud.UNIT_ID,
    ud.UNIT_NAME,
    utm.UNIT_TYPE,
    ld.CITY,
    si.BUSINESS_DATE,
    si.PRODUCT_ID,
    TRIM(REPLACE(REPLACE(REPLACE(REPLACE(pd.PRODUCT_NAME, '\n', ' '), '\r', ' '), '\t', ' '),',',' ')) PRODUCT_NAME,
    pd.UNIT_OF_MEASURE,
    cd.CATEGORY_NAME,
    sd.SUB_CATEGORY_NAME,
    si.OPENING_STOCK,
    COALESCE(dcpv.TRANSFER_OUT, 0) AS TRANSFERRED,
    COALESCE(dcpv.WASTAGE, 0) AS WASTAGE,
    COALESCE(dcpv.RECEIVED, 0) AS RECEIVED,
    COALESCE(dcpv.CONSUMPTION, 0) AS CONSUMPTION,
    si.CLOSING_STOCK,
    si.EXPECTED_CLOSING_VALUE,
    si.VARIANCE,
    si.VARIANCE_COST AS COST,
    si.VARIANCE_PRICE,
    pd.VARIANCE_TYPE,
    pd.STOCK_KEEPING_FREQUENCY,
    CASE
        WHEN pd.SHELF_LIFE_IN_DAYS = - 1 THEN 'NO'
        ELSE 'YES'
    END AS EXPIRABLE
FROM
    KETTLE_SCM_DUMP.STOCK_INVENTORY si
        LEFT OUTER JOIN
    KETTLE_SCM_DUMP.DAY_CLOSE_PRODUCT_VALUES dcpv ON dcpv.EVENT_ID = si.CURRENT_EVENT_ID
        AND si.PRODUCT_ID = dcpv.PRODUCT_ID
        AND dcpv.EVENT_ID = si.CURRENT_EVENT_ID
        AND dcpv.STOCK_TYPE=si.STOCK_TYPE
        INNER JOIN
    KETTLE_MASTER_DUMP.UNIT_DETAIL ud ON ud.UNIT_ID = si.UNIT_ID
        INNER JOIN
    KETTLE_SCM_DUMP.PRODUCT_DEFINITION pd ON pd.PRODUCT_ID = si.PRODUCT_ID
        INNER JOIN
    KETTLE_SCM_DUMP.CATEGORY_DEFINITION cd ON cd.CATEGORY_ID = pd.CATEGORY_ID
        INNER JOIN
    KETTLE_SCM_DUMP.SUB_CATEGORY_DEFINITION sd ON sd.SUB_CATEGORY_ID = pd.SUB_CATEGORY_ID
        LEFT JOIN
    KETTLE_SCM_DUMP.UNIT_TYPE_MAPPING utm ON ud.UNIT_CATEGORY = utm.UNIT_CATEGORY
        LEFT JOIN
    KETTLE_MASTER_DUMP.LOCATION_DETAIL ld ON ud.LOCATION_DETAIL_ID = ld.LOCATION_ID
        INNER JOIN
    KETTLE_SCM_DUMP.DAY_CLOSE_EVENT dce ON dce.EVENT_ID = si.CURRENT_EVENT_ID
WHERE
    si.BUSINESS_DATE >= :startDate
		AND si.BUSINESS_DATE <= :endDate
        AND dce.CLOSURE_EVENT_TYPE = 'STOCK_TAKE'
        AND si.STATUS = 'CLOSED'
        AND dce.STATUS = 'CLOSED'
        AND si.UNIT_ID=:unitId
	AND si.STOCK_TYPE= :stockType
ORDER BY si.UNIT_ID) s;

                        ]]>
					</content>
					<params>
						<param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="stockType" displayName="Variance Type"
                               dataType="STRING" format=""/>
						<param name="unitId" displayName="Unit"
                               dataType="STRING" format=""/>
					</params>
				</report>
	<report name="FIXED ASSETS Stock across units for Date Range - [NEW]"
                        executionType="SQL" returnType="java.lang.String">
			<content>
select dce.UNIT_ID,ud.UNIT_NAME,dce.BUSINESS_DATE,se.PRODUCT_ID,pd.PRODUCT_NAME,se.UNIT_OF_MEASURE,se.CURRENT_STOCK
from KETTLE_SCM_DUMP.DAY_CLOSE_EVENT dce
inner join KETTLE_SCM_DUMP.STOCK_ENTRY se on se.UPDATE_EVENT_ID=dce.EVENT_ID
inner join KETTLE_SCM_DUMP.PRODUCT_DEFINITION pd on pd.PRODUCT_ID=se.PRODUCT_ID
inner join KETTLE_SCM_DUMP.UNIT_DETAIL ud on ud.UNIT_ID=dce.UNIT_ID
where BUSINESS_DATE BETWEEN :startDate and :endDate and CLOSURE_EVENT_FREQUENCY="FIXED_ASSETS" and dce.STATUS="CLOSED" order by dce.UNIT_ID;
			</content>
			<params>
				<param name="startDate" displayName="Start Date"
                               			dataType="DATE" format="yyyy-MM-dd"/>
				<param name="endDate" displayName="End Date"
                               			dataType="DATE" format="yyyy-MM-dd"/>
			</params>
	</report>
</reports>
</category>

		<category name="Pricing Reports" type="Pricing"
			accessCode="Pricing">
			<reports>
				<report name="Price change audit report" executionType="SQL"
					returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
                            SELECT T.* FROM (SELECT 
    c.KEY_ID AS SKU_ID,
    c.KEY_TYPE,
    s.SKU_NAME,
    c.UNIT_ID,
    u.UNIT_NAME,
    c.OLD_PRICE,
    c.PRICE,
    c.TRANSACTTION_TYPE,
    c.ADD_TIME
FROM
    KETTLE_SCM_DUMP.COST_DETAIL_AUDIT_DATA c
        INNER JOIN
    KETTLE_SCM_DUMP.SKU_DEFINITION s ON s.SKU_ID = c.KEY_ID
        AND c.PRICE <> c.OLD_PRICE
        AND c.OLD_PRICE IS NOT NULL
        AND DATE(c.ADD_TIME) >= :startDate
        AND DATE(c.ADD_TIME) <= :endDate
	AND c.TRANSACTTION_TYPE <> 'CONSUMPTION'
        INNER JOIN
    KETTLE_SCM_DUMP.UNIT_DETAIL u ON u.UNIT_ID = c.UNIT_ID) T
                        ]]>
					</content>
					<params>
						<param name="startDate" displayName="Start Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
						<param name="endDate" displayName="End Date"
                               dataType="DATE" format="yyyy-MM-dd"/>
					</params>
				</report>
				<report name="Current price for Products on CAFE Units" executionType="SQL"
					returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[

SELECT 
    ud.UNIT_ID,
    ud.UNIT_NAME,
    pd.PRODUCT_ID,
    pd.PRODUCT_NAME,
    cat.CATEGORY_CODE,
    cd.PRICE
FROM
    KETTLE_SCM_DUMP.COST_DETAIL_DATA cd
        INNER JOIN
    KETTLE_SCM_DUMP.UNIT_DETAIL ud ON cd.UNIT_ID = ud.UNIT_ID
        INNER JOIN
    KETTLE_SCM_DUMP.PRODUCT_DEFINITION pd ON pd.PRODUCT_ID = cd.KEY_ID
        INNER JOIN
    KETTLE_SCM_DUMP.CATEGORY_DEFINITION cat ON pd.CATEGORY_ID = cat.CATEGORY_ID
WHERE
    IS_LATEST = 'Y' AND KEY_TYPE = 'PRODUCT'
GROUP BY cd.KEY_ID , cd.UNIT_ID;
]]>
					</content>
				</report>
<report name="Current price for SKUs on WH or KCH Units" executionType="SQL"
					returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[


SELECT 
    ud.UNIT_ID,
    ud.UNIT_NAME,
    sd.SKU_ID,
    sd.SKU_NAME,
    cat.CATEGORY_CODE,
    cd.PRICE
FROM
    KETTLE_SCM_DUMP.COST_DETAIL_DATA cd
        INNER JOIN
    KETTLE_SCM_DUMP.UNIT_DETAIL ud ON cd.UNIT_ID = ud.UNIT_ID
        INNER JOIN
    KETTLE_SCM_DUMP.SKU_DEFINITION sd ON sd.SKU_ID = cd.KEY_ID    
        INNER JOIN
    KETTLE_SCM_DUMP.PRODUCT_DEFINITION pd ON pd.PRODUCT_ID = sd.LINKED_PRODUCT_ID
        INNER JOIN
    KETTLE_SCM_DUMP.CATEGORY_DEFINITION cat ON pd.CATEGORY_ID = cat.CATEGORY_ID
WHERE
    IS_LATEST = 'Y' AND KEY_TYPE = 'SKU'
GROUP BY cd.KEY_ID , cd.UNIT_ID;
]]>
					</content>
				</report>
			</reports>
		</category>
	</categories>
</ReportCategories>