<ReportCategories xmlns="http://www.w3schools.com">
	<categories>
		<category name="SCM Report: Receiving Details - Milk" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>"
                        schedule="">
			<reports>
				<report id="1" name="Receiving Details - Milk" executionType="SQL"
                                        GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT 
    ro.REQUEST_ORDER_ID REQUEST_ORDER_ID,
    ud.UNIT_NAME UNIT_NAME,
    DATE(ro.FULFILLMENT_DATE) FULFILLMENT_DATE,
    ro.GENERATION_TIME GENERATION_TIME,
    ro.GENERATED_BY GENERATED_BY,
    roi.PRODUCT_NAME PRODUCT_NAME,
    toi.SKU_NAME SKU_NAME,
    FLOOR(SUM(roi.REQUESTED_ABSOLUTE_QUANTITY)) REQUESTED_ABSOLUTE_QUANTITY,
    FLOOR(SUM(roi.TRANSFERRED_QUANTITY)) TRANSFERRED_QUANTITY,
    FLOOR(SUM(roi.RECEIVED_QUANTITY)) RECEIVED_QUANTITY,
    vd.VENDOR_NAME VENDOR_NAME
FROM
    REQUEST_ORDER ro
        INNER JOIN
    REQUEST_ORDER_ITEM roi ON ro.REQUEST_ORDER_ID = roi.REQUEST_ORDER_ID
        AND DATE(ro.FULFILLMENT_DATE) = DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00'))
        AND ro.IS_SPECIAL_ORDER = 'Y'
        INNER JOIN
    UNIT_DETAIL ud ON ud.UNIT_ID = ro.REQUEST_UNIT_ID
        INNER JOIN
    UNIT_DETAIL ud1 ON ud1.UNIT_ID = ro.FULFILLMENT_UNIT_ID
        INNER JOIN
    VENDOR_DETAIL vd ON roi.VENDOR_ID = vd.VENDOR_ID
        INNER JOIN
    PRODUCT_DEFINITION pd ON roi.PRODUCT_ID = pd.PRODUCT_ID
        LEFT OUTER JOIN
    TRANSFER_ORDER_ITEM toi ON roi.REQUEST_ORDER_ITEM_ID = toi.REQUEST_ORDER_ITEM_ID
WHERE
    pd.CATEGORY_ID = '1'
        AND pd.SUB_CATEGORY_ID = '3'
        AND pd.PRODUCT_NAME = 'Milk'
        AND ro.REQUEST_ORDER_STATUS <> 'CANCELLED'
GROUP BY ud.UNIT_NAME
ORDER BY vd.VENDOR_NAME , roi.RECEIVED_QUANTITY;


        ]]>
					</content>
				</report>
			</reports>
		</category>
		<category name="SCM Report: External Vendor Receiving" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>"
                        schedule="">
			<reports>
				<report id="1" name="External Vendor Receiving" executionType="SQL"
                                        GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT * FROM (
SELECT 
    i.GOODS_RECEIVED_ITEM_ID,
    i.VENDOR_GR_ID,
    i.SKU_ID,
    i.HSN_CODE,
    i.SKU_NAME,
    i.PACKAGING_NAME,
    i.PACKAGING_QUANTITY,
    i.CONVERSION_RATIO,
    i.RECEIVED_QUANTITY,
    i.UNIT_OF_MEASURE,
    i.UNIT_PRICE,
    i.TOTAL_PRICE,
    i.TOTAL_TAX,
    i.TOTAL_AMOUNT,
    gr.CREATED_AT,
    gr.DOCUMENT_UPLOADED,
    gr.DOCUMENT_NUMBER,
    gr.TOTAL_PRICE AS 'TOTAL_GR_PRICE',
    gr.TOTAL_TAX AS 'TOTAL_GR_TAX',
    gr.TOTAL_AMOUNT AS 'TOTAL_GR_AMOUNT',
    gr.EXTRA_CHARGES,
    gr.DOCUMENT_DATE,
    gr.AMOUNT_MATCHED,
    ud.UNIT_NAME AS 'RECEIVING_UNIT',
    v.ENTITY_NAME AS 'VENDOR_NAME',
    a.STATE AS 'DISPATH_LOCATION_STATE',
    t.CGST,
    t.CGST_VALUE,
    t.SGST,
    t.SGST_VALUE,
    t.IGST,
    t.IGST_VALUE
FROM
     KETTLE_SCM.VENDOR_GOODS_RECEIVED_DATA gr
        LEFT JOIN
     KETTLE_SCM.VENDOR_GR_ITEM_DETAIL i ON gr.GOODS_RECEIVED_ID = i.VENDOR_GR_ID
        LEFT JOIN
     KETTLE_SCM.VENDOR_DETAIL_DATA v ON v.VENDOR_ID = gr.VENDOR_ID
        LEFT JOIN
     KETTLE_SCM.UNIT_DETAIL ud ON ud.UNIT_ID = gr.DELIVERY_UNIT_ID
        LEFT JOIN
     KETTLE_SCM.VENDOR_DISPATCH_LOCATIONS l ON l.DISPATCH_LOCATION_ID = gr.DISPATCH_ID
        LEFT JOIN
     KETTLE_SCM.ADDRESS_DETAIL_DATA a ON a.ADDRESS_ID = l.LOCATION_ADDRESS_ID
        LEFT JOIN
    (SELECT 
        GR_ITEM_ID,
            SUM(CASE
                WHEN TAX_TYPE = 'CGST' THEN TAX_PERCENTAGE
                ELSE 0
            END) AS CGST,
            SUM(CASE
                WHEN TAX_TYPE = 'CGST' THEN TAX_VALUE
                ELSE 0
            END) AS CGST_VALUE,
            SUM(CASE
                WHEN TAX_TYPE = 'SGST' THEN TAX_PERCENTAGE
                ELSE 0
            END) AS SGST,
            SUM(CASE
                WHEN TAX_TYPE = 'SGST' THEN TAX_VALUE
                ELSE 0
            END) AS SGST_VALUE,
            SUM(CASE
                WHEN TAX_TYPE = 'IGST' THEN TAX_PERCENTAGE
                ELSE 0
            END) AS IGST,
            SUM(CASE
                WHEN TAX_TYPE = 'IGST' THEN TAX_VALUE
                ELSE 0
            END) AS IGST_VALUE
    FROM
         KETTLE_SCM.ITEM_TAX_DETAIL_DATA
    GROUP BY GR_ITEM_ID) t ON t.GR_ITEM_ID = i.GOODS_RECEIVED_ITEM_ID
    WHERE gr.CREATED_AT >  DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00'))) z
        ]]>
					</content>
				</report>
			</reports>
		</category>

<category name="SCM Report: Milk Details" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>, <EMAIL>, <EMAIL>"
                        schedule="">
			<reports>
				<report id="1" name="Milk Received Quantity" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT
   ud.UNIT_NAME,
   DATE(ro.FULFILLMENT_DATE) AS FULFILLMENT_DATE,
  pd.PRODUCT_NAME,
   FLOOR(SUM(roi.REQUESTED_ABSOLUTE_QUANTITY)) AS REQUESTED_QUANTITY,
   FLOOR(SUM(roi.RECEIVED_QUANTITY)) AS RECEIVED_QUANTITY,
   vd.ENTITY_NAME
FROM
   KETTLE_SCM.REQUEST_ORDER ro
       INNER JOIN
   KETTLE_SCM.REQUEST_ORDER_ITEM roi ON ro.REQUEST_ORDER_ID = roi.REQUEST_ORDER_ID
       AND DATE(ro.FULFILLMENT_DATE) = DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00'))
       AND ro.IS_SPECIAL_ORDER = 'Y'
       INNER JOIN
   KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = ro.REQUEST_UNIT_ID
       INNER JOIN
   KETTLE_SCM.VENDOR_DETAIL_DATA vd ON roi.VENDOR_ID = vd.VENDOR_ID
       INNER JOIN
   KETTLE_SCM.PRODUCT_DEFINITION pd ON roi.Product_ID = pd.Product_ID
       INNER JOIN
   KETTLE_SCM.SUB_CATEGORY_DEFINITION cd ON cd.SUB_CATEGORY_ID = pd.SUB_CATEGORY_ID
WHERE
   pd.Category_ID = '1'
       AND pd.SUB_CATEGORY_ID = '3'
    AND ro.REQUEST_ORDER_STATUS <> "CANCELLED"
GROUP BY ud.UNIT_NAME
ORDER BY RECEIVED_QUANTITY
						]]>
					</content>
				</report>
				<report id="2" name="Milk Request Quantity" executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[

SELECT
   ud.UNIT_NAME,
   DATE(ro.FULFILLMENT_DATE) AS FULFILLMENT_DATE,
   pd.PRODUCT_NAME,
   FLOOR(SUM(roi.REQUESTED_ABSOLUTE_QUANTITY)) AS REQUESTED_QUANTITY,
   FLOOR(SUM(roi.RECEIVED_QUANTITY)) AS RECEIVED_QUANTITY,
   vd.ENTITY_NAME
FROM
   KETTLE_SCM.REQUEST_ORDER ro
       INNER JOIN
   KETTLE_SCM.REQUEST_ORDER_ITEM roi ON ro.REQUEST_ORDER_ID = roi.REQUEST_ORDER_ID
       AND DATE(ro.FULFILLMENT_DATE) = SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')),-1)
       AND ro.IS_SPECIAL_ORDER = 'Y'
       INNER JOIN
   KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = ro.REQUEST_UNIT_ID
       INNER JOIN
   KETTLE_SCM.VENDOR_DETAIL_DATA vd ON roi.VENDOR_ID = vd.VENDOR_ID
       INNER JOIN
   KETTLE_SCM.PRODUCT_DEFINITION pd ON roi.Product_ID = pd.Product_ID
       INNER JOIN
   KETTLE_SCM.SUB_CATEGORY_DEFINITION cd ON cd.SUB_CATEGORY_ID = pd.SUB_CATEGORY_ID
WHERE
   pd.Category_ID = '1'
       AND pd.SUB_CATEGORY_ID = '3'
    AND ro.REQUEST_ORDER_STATUS <> "CANCELLED"
GROUP BY ud.UNIT_NAME
ORDER BY RECEIVED_QUANTITY
						]]>
					</content>
				</report>
			</reports>
		</category>
	</categories>
</ReportCategories>


