<ReportCategories xmlns="http://www.w3schools.com">
    <categories>
        <category name="SCM Report: Variance Summary - Kitchen and Warehouse" type="Automated"
                  accessCode="Automated" id="1" fromEmail="<EMAIL>"
                  attachmentType="EXCEL"
                  toEmails="<EMAIL>"
                  schedule="">
            <reports>
                <report id="1" name="Variance Summary - Warehouse" executionType="SQL" attachmentType="EXCEL">
                    <content>
                        <![CDATA[
SELECT * FROM ( SELECT
    B.UNIT_NAME,
    B.GENERATION_DATE,
    B.DAILY_VARIANCE_COST_MOD,
    A.VARIANCE_COST_MOD_MTD,
    A.VARIANCE_COST_REAL_MTD,
    A.TARGET
FROM
    (SELECT
        ud.UNIT_ID,
            ud.UNIT_NAME,
            ROUND(SUM(ABS(inv.VARIANCE_COST)), 2) VARIANCE_COST_MOD_MTD,
            ROUND(SUM(inv.VARIANCE_COST), 2) VARIANCE_COST_REAL_MTD,
            CASE
                WHEN ud.UNIT_ID = 22001 THEN '45000-55000'
                WHEN ud.UNIT_ID = 22002 THEN '17500-22500'
            END AS TARGET
    FROM
        KETTLE_SCM_DEV.DAY_CLOSE_EVENT dce
    INNER JOIN KETTLE_SCM_DEV.INVENTORY_DRILLDOWN inv ON dce.EVENT_ID = inv.CLOSURE_EVENT_ID
    INNER JOIN KETTLE_SCM_DEV.UNIT_DETAIL ud ON ud.UNIT_ID = dce.UNIT_ID
    INNER JOIN KETTLE_SCM_DEV.SKU_DEFINITION sd ON inv.SKU_ID = sd.SKU_ID
    WHERE
        DATE(dce.BUSINESS_DATE) BETWEEN DATE_ADD(DATE(DATE_SUB(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL DAYOFMONTH(ADDTIME(UTC_TIMESTAMP, '05:30:00')) - 1 DAY)), INTERVAL 5 HOUR) AND SUBDATE(CURRENT_DATE, 2)
            AND dce.STATUS <> 'CANCELLED'
            AND ud.UNIT_ID IN (22001 , 22002)
            AND sd.SKU_STATUS = 'ACTIVE'
            AND sd.INVENTORY_LIST_ID <> '1'
    GROUP BY ud.UNIT_ID) A
        LEFT JOIN
    (SELECT
        ud.UNIT_ID,
            ud.UNIT_NAME,
            DATE(dce.GENERATION_TIME) AS GENERATION_DATE,
            ROUND(SUM(ABS(inv.VARIANCE_COST)), 2) DAILY_VARIANCE_COST_MOD
    FROM
        KETTLE_SCM_DEV.DAY_CLOSE_EVENT dce
    INNER JOIN KETTLE_SCM_DEV.INVENTORY_DRILLDOWN inv ON dce.EVENT_ID = inv.CLOSURE_EVENT_ID
    INNER JOIN KETTLE_SCM_DEV.UNIT_DETAIL ud ON ud.UNIT_ID = dce.UNIT_ID
    INNER JOIN KETTLE_SCM_DEV.SKU_DEFINITION sd ON inv.SKU_ID = sd.SKU_ID
    WHERE
        DATE(dce.BUSINESS_DATE) = SUBDATE(CURRENT_DATE, 2)
            AND dce.STATUS <> 'CANCELLED'
            AND ud.UNIT_ID IN (22001 , 22002)
            AND sd.SKU_STATUS = 'ACTIVE'
            AND sd.INVENTORY_LIST_ID <> '1'
    GROUP BY ud.UNIT_ID) B ON A.UNIT_ID = B.UNIT_ID) X
 ]]>
                    </content>
                </report>
                <report id="1" name="Variance Summary - Kitchen" executionType="SQL" attachmentType="EXCEL">
                    <content>
                        <![CDATA[
SELECT * FROM (
SELECT
    B.UNIT_NAME,
    B.GENERATION_DATE,
    B.DAILY_VARIANCE_COST_MOD,
    A.VARIANCE_COST_MOD_MTD,
    A.VARIANCE_COST_REAL_MTD,
    A.TARGET
FROM
    (SELECT
        ud.UNIT_ID,
            ud.UNIT_NAME,
            ROUND(SUM(ABS(inv.VARIANCE_COST)), 2) VARIANCE_COST_MOD_MTD,
            ROUND(SUM(inv.VARIANCE_COST), 2) VARIANCE_COST_REAL_MTD,
            CASE
                WHEN ud.UNIT_ID = 24001 THEN '15000-20000'
                WHEN ud.UNIT_ID = 24002 THEN '7500-12500'
            END AS TARGET
    FROM
        KETTLE_SCM_DEV.DAY_CLOSE_EVENT dce
    INNER JOIN KETTLE_SCM_DEV.INVENTORY_DRILLDOWN inv ON dce.EVENT_ID = inv.CLOSURE_EVENT_ID
    INNER JOIN KETTLE_SCM_DEV.UNIT_DETAIL ud ON ud.UNIT_ID = dce.UNIT_ID
    INNER JOIN KETTLE_SCM_DEV.SKU_DEFINITION sd ON inv.SKU_ID = sd.SKU_ID
    WHERE
        DATE(dce.BUSINESS_DATE) BETWEEN DATE_ADD(DATE(DATE_SUB(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL DAYOFMONTH(ADDTIME(UTC_TIMESTAMP, '05:30:00')) - 1 DAY)), INTERVAL 5 HOUR) AND SUBDATE(CURRENT_DATE, 2)
            AND dce.STATUS <> 'CANCELLED'
            AND ud.UNIT_ID IN (24001 , 24002)
            AND sd.SKU_STATUS = 'ACTIVE'
            AND sd.INVENTORY_LIST_ID <> '1'
            AND sd.SKU_ID NOT IN (1142 , 1323, 1220, 1016, 1010, 1011, 30, 7, 1066, 1134, 130, 91, 1171, 48, 178, 174, 1674, 264, 269, 123, 320, 1012, 361, 139, 1009, 1048, 1076, 150, 151, 302, 1109, 1335, 386, 23, 363, 1073, 1334, 136, 1014, 389, 548, 13, 1074, 147, 1333, 392, 265, 47, 252, 29, 1356)
    GROUP BY ud.UNIT_ID) A
        LEFT JOIN
    (SELECT
        ud.UNIT_ID,
            ud.UNIT_NAME,
            DATE(dce.GENERATION_TIME) AS GENERATION_DATE,
            ROUND(SUM(ABS(inv.VARIANCE_COST)), 2) DAILY_VARIANCE_COST_MOD
    FROM
        KETTLE_SCM_DEV.DAY_CLOSE_EVENT dce
    INNER JOIN KETTLE_SCM_DEV.INVENTORY_DRILLDOWN inv ON dce.EVENT_ID = inv.CLOSURE_EVENT_ID
    INNER JOIN KETTLE_SCM_DEV.UNIT_DETAIL ud ON ud.UNIT_ID = dce.UNIT_ID
    INNER JOIN KETTLE_SCM_DEV.SKU_DEFINITION sd ON inv.SKU_ID = sd.SKU_ID
    WHERE
        DATE(dce.BUSINESS_DATE) = SUBDATE(CURRENT_DATE, 2)
            AND dce.STATUS <> 'CANCELLED'
            AND ud.UNIT_ID IN (24001 , 24002)
            AND sd.SKU_STATUS = 'ACTIVE'
            AND sd.INVENTORY_LIST_ID <> '1'
            AND sd.SKU_ID NOT IN (1142 , 1323, 1220, 1016, 1010, 1011, 30, 7, 1066, 1134, 130, 91, 1171, 48, 178, 174, 1674, 264, 269, 123, 320, 1012, 361, 139, 1009, 1048, 1076, 150, 151, 302, 1109, 1335, 386, 23, 363, 1073, 1334, 136, 1014, 389, 548, 13, 1074, 147, 1333, 392, 265, 47, 252, 29, 1356)
    GROUP BY ud.UNIT_ID) B ON A.UNIT_ID = B.UNIT_ID) B;

 ]]>
                    </content>
                </report>
                <report id="2" name="ZERO Variance for Kitchen and Warehouse" executionType="SQL">
                    <content>
                        <![CDATA[

SELECT A.*,B.ZERO_VARIANCE_COST_MTD_MOD FROM
( SELECT
  ud.UNIT_ID,
  ud.UNIT_NAME,
  ROUND(SUM(ABS(inv.VARIANCE_COST)),2) ZERO_VARIANCE_COST_MOD
FROM
  KETTLE_SCM_DEV.DAY_CLOSE_EVENT dce
      INNER JOIN
  KETTLE_SCM_DEV.INVENTORY_DRILLDOWN inv ON dce.EVENT_ID = inv.CLOSURE_EVENT_ID
      INNER JOIN
  KETTLE_SCM_DEV.UNIT_DETAIL ud ON ud.UNIT_ID = dce.UNIT_ID
      INNER JOIN
  KETTLE_SCM_DEV.SKU_DEFINITION sd ON inv.SKU_ID = sd.SKU_ID
WHERE
  DATE(dce.BUSINESS_DATE) = SUBDATE(CURRENT_DATE, 2)
        AND dce.STATUS <> 'CANCELLED'
        AND ud.UNIT_ID IN (22001 , 22002, 24001, 24002)
        AND sd.INVENTORY_LIST_ID=16
GROUP BY ud.UNIT_ID ) A

LEFT JOIN
(
SELECT
  ud.UNIT_ID,
  ud.UNIT_NAME,
  ROUND(SUM(ABS(inv.VARIANCE_COST)),2) ZERO_VARIANCE_COST_MTD_MOD
FROM
  KETTLE_SCM_DEV.DAY_CLOSE_EVENT dce
      INNER JOIN
  KETTLE_SCM_DEV.INVENTORY_DRILLDOWN inv ON dce.EVENT_ID = inv.CLOSURE_EVENT_ID
      INNER JOIN
  KETTLE_SCM_DEV.UNIT_DETAIL ud ON ud.UNIT_ID = dce.UNIT_ID
      INNER JOIN
  KETTLE_SCM_DEV.SKU_DEFINITION sd ON inv.SKU_ID = sd.SKU_ID
WHERE
  DATE(dce.BUSINESS_DATE) BETWEEN DATE_ADD(DATE(DATE_SUB(ADDTIME(UTC_TIMESTAMP, '05:30:00'),
                INTERVAL DAYOFMONTH(ADDTIME(UTC_TIMESTAMP, '05:30:00')) - 1 DAY)),
        INTERVAL 5 HOUR) AND SUBDATE(CURRENT_DATE, 2)
        AND dce.STATUS <> 'CANCELLED'
        AND ud.UNIT_ID IN (22001 , 22002, 24001, 24002)
        AND sd.INVENTORY_LIST_ID=16
GROUP BY ud.UNIT_ID ) B
ON A.UNIT_ID = B.UNIT_ID

 ]]>
                    </content>
                </report>
                <report id="3" name="Raw Variance Data for Warehouse" executionType="SQL">
                    <content>
                        <![CDATA[
SELECT * FROM (SELECT
   ud.UNIT_NAME,
   DATE(dce.GENERATION_TIME) as GENERATION_DATE,
   dce.UPDATED_BY,
   inv.SKU_ID,
   sd.SKU_NAME,
   inv.UOM,
   inv.PRICE,
   inv.OPENING,
   inv.TRANSFERRED,
   inv.RECEIVED,
   inv.CONSUMPTION,
   inv.BOOKED,
   inv.WASTED,
   inv.EXPECTED_CLOSING,
   inv.ACTUAL_CLOSING,
   inv.VARIANCE,
   ABS(inv.VARIANCE_COST) VARIANCE_COST
FROM
   KETTLE_SCM_DEV.DAY_CLOSE_EVENT dce
       INNER JOIN
   KETTLE_SCM_DEV.INVENTORY_DRILLDOWN inv ON dce.EVENT_ID = inv.CLOSURE_EVENT_ID
       INNER JOIN
   KETTLE_SCM_DEV.UNIT_DETAIL ud ON ud.UNIT_ID = dce.UNIT_ID
       INNER JOIN
   KETTLE_SCM_DEV.SKU_DEFINITION sd ON inv.SKU_ID = sd.SKU_ID
WHERE
        dce.BUSINESS_DATE = SUBDATE(CURRENT_DATE,2) AND
         dce.UNIT_ID IN (22001,22002)
	AND dce.STATUS <> 'CANCELLED'
	AND inv.VARIANCE_COST <> '0') A

 ]]>
                    </content>
                </report>
                <report id="3" name="Raw Variance Data for Kitchen" executionType="SQL">
                    <content>
                        <![CDATA[
SELECT * FROM (

 SELECT
   ud.UNIT_NAME,   DATE(dce.GENERATION_TIME) as GENERATION_DATE,   dce.UPDATED_BY,   inv.SKU_ID,   sd.SKU_NAME,   inv.UOM,   inv.PRICE,   inv.OPENING,   inv.TRANSFERRED,   inv.RECEIVED,   inv.CONSUMPTION,   inv.BOOKED,   inv.WASTED,   inv.EXPECTED_CLOSING,   inv.ACTUAL_CLOSING,   inv.VARIANCE,   ABS(inv.VARIANCE_COST) VARIANCE_COST
FROM
   KETTLE_SCM_DEV.DAY_CLOSE_EVENT dce
       INNER JOIN
   KETTLE_SCM_DEV.INVENTORY_DRILLDOWN inv ON dce.EVENT_ID = inv.CLOSURE_EVENT_ID
       INNER JOIN
   KETTLE_SCM_DEV.UNIT_DETAIL ud ON ud.UNIT_ID = dce.UNIT_ID
       INNER JOIN
   KETTLE_SCM_DEV.SKU_DEFINITION sd ON inv.SKU_ID = sd.SKU_ID
WHERE
        dce.BUSINESS_DATE = SUBDATE(CURRENT_DATE, 2) AND
         dce.UNIT_ID IN (24001, 24002)
AND inv.SKU_ID NOT IN (1142, 1323, 1220, 1016, 1010, 1011, 30, 7, 1066, 1134, 130, 91, 1171, 48, 178, 174, 1674, 264, 269, 123, 320, 1012, 361, 139, 1009, 1048, 1076, 150, 151, 302, 1109, 1335, 386, 23, 363, 1073, 1334, 136, 1014, 389, 548, 13, 1074, 147, 1333, 392, 265, 47, 252, 29, 1356)
	AND dce.STATUS <> 'CANCELLED'
	AND inv.VARIANCE_COST <> '0'
) A

 ]]>
                    </content>
                </report>
            </reports>
        </category>
    </categories>
</ReportCategories>