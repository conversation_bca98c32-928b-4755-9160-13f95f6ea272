<ReportCategories xmlns="http://www.w3schools.com">
	<categories>
		<category name="SCM Report: Gift Card Request Orders" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>,<EMAIL>"
                        schedule="12">
			<reports>
				<report id="1" name="SCM Report: Gift Card Request Orders" executionType="SQL"
                                        GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT
    ro.REQUEST_UNIT_ID,
    ud.UNIT_NAME,
    ro.FULFILLMENT_UNIT_ID,
    ud.UNIT_NAME,
    ro.FULFILLMENT_DATE,
    roi.PRODUCT_NAME,
    ro.GENERATION_TIME,
    ro.GENERATED_BY,
    roi.REQUESTED_ABSOLUTE_QUANTITY as REQUESTED_QUANTITY,
    roi.TRANSFERRED_QUANTITY as TRANSFERRED_QUANTITY,
    roi.RECEIVED_QUANTITY as RECEIVED_QUANTITY
FROM
    KETTLE_SCM.REQUEST_ORDER ro
        INNER JOIN
    KETTLE_SCM.REQUEST_ORDER_ITEM roi ON ro.REQUEST_ORDER_ID = roi.REQUEST_ORDER_ID
        AND ro.FULFILLMENT_DATE BETWEEN DATE_SUB(CURDATE(), INTERVAL 365 day) AND DATE_ADD(CURDATE(), INTERVAL 1 day)
        INNER JOIN
    KETTLE_SCM.UNIT_DETAIL ud ON ud.UNIT_ID = ro.REQUEST_UNIT_ID
        INNER JOIN
    KETTLE_SCM.UNIT_DETAIL ud1 ON ud1.UNIT_ID = ro.FULFILLMENT_UNIT_ID
        INNER JOIN
    KETTLE_SCM.PRODUCT_DEFINITION pd ON roi.Product_ID = pd.Product_ID
WHERE
    roi.PRODUCT_ID IN ('100731','100732','100778')
    AND roi.TRANSFERRED_QUANTITY = 0;        

]]>
					</content>
				</report>
			</reports>
		</category>
	</categories>
</ReportCategories>
