<ReportCategories xmlns="http://www.w3schools.com">
	<categories>
		<category name="SCM Report: Receiving Details - Milk" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>"
                        schedule="">
			<reports>
				<report id="1" name="Receiving Details - Milk" executionType="SQL"
                                        GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT 
    ro.REQUEST_ORDER_ID REQUEST_ORDER_ID,
    ud.UNIT_NAME UNIT_NAME,
    DATE(ro.FULFILLMENT_DATE) FULFILLMENT_DATE,
    ro.GENERATION_TIME GENERATION_TIME,
    ro.GENERATED_BY GENERATED_BY,
    roi.PRODUCT_NAME PRODUCT_NAME,
    toi.SKU_NAME SKU_NAME,
    FLOOR(SUM(roi.REQUESTED_ABSOLUTE_QUANTITY)) REQUESTED_ABSOLUTE_QUANTITY,
    FLOOR(SUM(roi.TRANSFERRED_QUANTITY)) TRANSFERRED_QUANTITY,
    FLOOR(SUM(roi.RECEIVED_QUANTITY)) RECEIVED_QUANTITY,
    vd.VENDOR_NAME VENDOR_NAME
FROM
    REQUEST_ORDER ro
        INNER JOIN
    REQUEST_ORDER_ITEM roi ON ro.REQUEST_ORDER_ID = roi.REQUEST_ORDER_ID
        AND DATE(ro.FULFILLMENT_DATE) = DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00'))
        AND ro.IS_SPECIAL_ORDER = 'Y'
        INNER JOIN
    UNIT_DETAIL ud ON ud.UNIT_ID = ro.REQUEST_UNIT_ID
        INNER JOIN
    UNIT_DETAIL ud1 ON ud1.UNIT_ID = ro.FULFILLMENT_UNIT_ID
        INNER JOIN
    VENDOR_DETAIL vd ON roi.VENDOR_ID = vd.VENDOR_ID
        INNER JOIN
    PRODUCT_DEFINITION pd ON roi.PRODUCT_ID = pd.PRODUCT_ID
        LEFT OUTER JOIN
    TRANSFER_ORDER_ITEM toi ON roi.REQUEST_ORDER_ITEM_ID = toi.REQUEST_ORDER_ITEM_ID
WHERE
    pd.CATEGORY_ID = '1'
        AND pd.SUB_CATEGORY_ID = '3'
        AND pd.PRODUCT_NAME = 'Milk'
        AND ro.REQUEST_ORDER_STATUS <> 'CANCELLED'
GROUP BY ud.UNIT_NAME
ORDER BY vd.VENDOR_NAME , roi.RECEIVED_QUANTITY;


        ]]>
					</content>
				</report>
			</reports>
		</category>


		<category name="SCM Report: Inventory Summary for Kitchen and Warehouse" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>"
                        schedule="" attachmentType="EXCEL">
			<reports>
				<report id="1" name="Value Summary for Kitchen and Warehouse" executionType="SQL" attachmentType="EXCEL">
					<content>
						<![CDATA[
SELECT 
    C.*, B.AVERAGE_INVENTORY_VALUE
FROM
    (SELECT 
        ud.UNIT_NAME,
            FORMAT(ROUND(SUM(se.CURRENT_STOCK * cdd.PRICE), 0), 0, 'en_IN') AS TOTAL_VALUE,
            CASE
                WHEN ud.UNIT_ID = 24001 THEN '12,00,000'
                WHEN ud.UNIT_ID = 24002 THEN '7,50,000'
                WHEN ud.UNIT_ID = 22001 THEN '1,20,00,000'
                WHEN ud.UNIT_ID = 22002 THEN '40,00,000'
            END AS TARGET
    FROM
        KETTLE_SCM.DAY_CLOSE_EVENT dce
    INNER JOIN KETTLE_SCM.STOCK_ENTRY se ON dce.EVENT_ID = se.UPDATE_EVENT_ID
    INNER JOIN KETTLE_SCM.UNIT_DETAIL ud ON ud.UNIT_ID = dce.UNIT_ID
    INNER JOIN KETTLE_SCM.SKU_DEFINITION sd ON se.SKU_ID = sd.SKU_ID
    INNER JOIN KETTLE_SCM.COST_DETAIL_DATA cdd ON cdd.KEY_ID = se.SKU_ID
        AND cdd.IS_LATEST = 'Y'
        AND dce.UNIT_ID = cdd.UNIT_ID
    INNER JOIN KETTLE_SCM.PRODUCT_DEFINITION pd ON sd.LINKED_PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE_SCM.CATEGORY_DEFINITION cd ON pd.CATEGORY_ID = cd.CATEGORY_ID
    INNER JOIN KETTLE_SCM.SUB_CATEGORY_DEFINITION sc ON pd.SUB_CATEGORY_ID = sc.SUB_CATEGORY_ID
    INNER JOIN KETTLE_SCM.UNIT_SKU_MAPPING us ON dce.UNIT_ID = us.UNIT_ID
        AND sd.SKU_ID = us.SKU_ID
        AND us.MAPPING_STATUS = 'ACTIVE'
    WHERE
        sd.SKU_STATUS = 'ACTIVE'
            AND dce.CLOSURE_EVENT_TYPE = 'WH_OPENING'
            AND ud.UNIT_ID IN (22001 , 22002, 24001, 24002)
    GROUP BY ud.UNIT_NAME
    ORDER BY ud.UNIT_NAME) C
        LEFT JOIN
    (SELECT 
        A.UNIT_NAME,
            FORMAT(ROUND(AVG(A.INVENTORY_VALUE), 0), 0, 'en_IN') AVERAGE_INVENTORY_VALUE
    FROM
        (SELECT 
        dce.BUSINESS_DATE,
            ud.UNIT_NAME,
            ROUND(SUM(inv.OPENING * cdd.PRICE), 0) INVENTORY_VALUE
    FROM
        KETTLE_SCM.DAY_CLOSE_EVENT dce
    INNER JOIN KETTLE_SCM.INVENTORY_DRILLDOWN inv ON dce.EVENT_ID = inv.CLOSURE_EVENT_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = dce.UNIT_ID
    INNER JOIN KETTLE_SCM.SKU_DEFINITION sd ON sd.SKU_ID=inv.SKU_ID
    INNER JOIN (SELECT 
        *
    FROM
        KETTLE_SCM.COST_DETAIL_DATA
    WHERE
        IS_LATEST = 'Y'
            AND UNIT_ID IN (24001 , 24002, 22001, 22002)) cdd ON cdd.UNIT_ID = dce.UNIT_ID
        AND cdd.KEY_ID = inv.SKU_ID
        AND dce.BUSINESS_DATE BETWEEN DATE(DATE_SUB(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL 31 DAY))
        AND DATE(DATE_SUB(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL    1 DAY))
        AND dce.UNIT_ID IN (24001 , 24002, 22001, 22002)
	AND sd.SKU_STATUS = 'ACTIVE'
    GROUP BY ud.UNIT_NAME , dce.BUSINESS_DATE
    ORDER BY ud.UNIT_NAME) A
    GROUP BY A.UNIT_NAME) B ON C.UNIT_NAME = B.UNIT_NAME;
						]]>
					</content>
				</report>

				<report id="1" name="Category Wise Value Summary for Kitchen and Warehouse" executionType="SQL" attachmentType="EXCEL">
					<content>
						<![CDATA[
	       SELECT * FROM 
		(
SELECT 
    ud.UNIT_NAME,
    cd.CATEGORY_NAME,
    FORMAT(ROUND(SUM(se.CURRENT_STOCK * cdd.PRICE), 2),
        2,
        'en_IN') AS TOTAL_VALUE
FROM
    KETTLE_SCM.DAY_CLOSE_EVENT dce
        INNER JOIN
    KETTLE_SCM.STOCK_ENTRY se ON dce.EVENT_ID = se.UPDATE_EVENT_ID
        INNER JOIN
    KETTLE_SCM.UNIT_DETAIL ud ON ud.UNIT_ID = dce.UNIT_ID
        INNER JOIN
    KETTLE_SCM.SKU_DEFINITION sd ON se.SKU_ID = sd.SKU_ID
        INNER JOIN
    KETTLE_SCM.COST_DETAIL_DATA cdd ON cdd.KEY_ID = se.SKU_ID
        AND cdd.IS_LATEST = 'Y'
        AND dce.UNIT_ID = cdd.UNIT_ID
        INNER JOIN
    KETTLE_SCM.PRODUCT_DEFINITION pd ON sd.LINKED_PRODUCT_ID = pd.PRODUCT_ID
        INNER JOIN
    KETTLE_SCM.CATEGORY_DEFINITION cd ON pd.CATEGORY_ID = cd.CATEGORY_ID
        INNER JOIN
    KETTLE_SCM.SUB_CATEGORY_DEFINITION sc ON pd.SUB_CATEGORY_ID = sc.SUB_CATEGORY_ID
        INNER JOIN
    KETTLE_SCM.UNIT_SKU_MAPPING us ON dce.UNIT_ID = us.UNIT_ID
        AND sd.SKU_ID = us.SKU_ID
        AND us.MAPPING_STATUS = 'ACTIVE'
WHERE
    sd.SKU_STATUS = 'ACTIVE'
        AND dce.CLOSURE_EVENT_TYPE = 'WH_OPENING'
        AND ud.UNIT_ID IN (22001 , 22002, 24001, 24002)
GROUP BY cd.CATEGORY_NAME , ud.UNIT_NAME
ORDER BY ud.UNIT_NAME ) a
						]]>
					</content>
				</report>

				<report id="2" name="Inventory for Kitchen and Warehouse" executionType="SQL" attachmentType="EXCEL">
					<content>
						<![CDATA[
SELECT
*
FROM
(
SELECT 
    ud.UNIT_NAME,
    se.SKU_ID,
    sd.SKU_NAME,
    sd.UNIT_OF_MEASURE,
    cd.CATEGORY_NAME,
    sc.SUB_CATEGORY_NAME,
    se.CURRENT_STOCK,
    cdd.PRICE AS CURRENT_PRICE,
    il.LIST_NAME
FROM
    KETTLE_SCM.DAY_CLOSE_EVENT dce
        INNER JOIN
    KETTLE_SCM.STOCK_ENTRY se ON dce.EVENT_ID = se.UPDATE_EVENT_ID
        INNER JOIN
    KETTLE_SCM.UNIT_DETAIL ud ON ud.UNIT_ID = dce.UNIT_ID
        INNER JOIN
    KETTLE_SCM.SKU_DEFINITION sd ON se.SKU_ID = sd.SKU_ID
        INNER JOIN
    KETTLE_SCM.COST_DETAIL_DATA cdd ON cdd.KEY_ID = se.SKU_ID
        AND cdd.IS_LATEST = 'Y'
        AND dce.UNIT_ID = cdd.UNIT_ID
        INNER JOIN
    KETTLE_SCM.PRODUCT_DEFINITION pd ON sd.LINKED_PRODUCT_ID = pd.PRODUCT_ID
        INNER JOIN
    KETTLE_SCM.CATEGORY_DEFINITION cd ON pd.CATEGORY_ID = cd.CATEGORY_ID
        INNER JOIN
    KETTLE_SCM.SUB_CATEGORY_DEFINITION sc ON pd.SUB_CATEGORY_ID = sc.SUB_CATEGORY_ID
        INNER JOIN
    KETTLE_SCM.UNIT_SKU_MAPPING us ON dce.UNIT_ID = us.UNIT_ID
        AND sd.SKU_ID = us.SKU_ID
        AND us.MAPPING_STATUS = 'ACTIVE'
        INNER JOIN
    KETTLE_SCM.INVENTORY_LISTS il ON il.LIST_ID = sd.INVENTORY_LIST_ID
WHERE
    sd.SKU_STATUS = 'ACTIVE'
        AND dce.CLOSURE_EVENT_TYPE = 'WH_OPENING'
        AND ud.UNIT_ID IN (22001 , 22002, 24001, 24002)
ORDER BY ud.UNIT_NAME) a 
			]]>
					</content>
				</report>


				<report id="2" name="Region Wise Inventory for Kitchen and Warehouse" executionType="SQL" attachmentType="EXCEL">
					<content>
						<![CDATA[
SELECT
*
FROM
(
SELECT 
    ud.UNIT_REGION,
    se.SKU_ID,
    sd.SKU_NAME,
    sd.UNIT_OF_MEASURE,
    cd.CATEGORY_NAME,
    sc.SUB_CATEGORY_NAME,
    SUM(se.CURRENT_STOCK) AS CURRENT_STOCK,
    cdd.PRICE AS CURRENT_PRICE,
    il.LIST_NAME
FROM
    KETTLE_SCM.DAY_CLOSE_EVENT dce
        INNER JOIN
    KETTLE_SCM.STOCK_ENTRY se ON dce.EVENT_ID = se.UPDATE_EVENT_ID
        INNER JOIN
    KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = dce.UNIT_ID
        INNER JOIN
    KETTLE_SCM.SKU_DEFINITION sd ON se.SKU_ID = sd.SKU_ID
        INNER JOIN
    KETTLE_SCM.COST_DETAIL_DATA cdd ON cdd.KEY_ID = se.SKU_ID
        AND cdd.IS_LATEST = 'Y'
        AND dce.UNIT_ID = cdd.UNIT_ID
        INNER JOIN
    KETTLE_SCM.PRODUCT_DEFINITION pd ON sd.LINKED_PRODUCT_ID = pd.PRODUCT_ID
        INNER JOIN
    KETTLE_SCM.CATEGORY_DEFINITION cd ON pd.CATEGORY_ID = cd.CATEGORY_ID
        INNER JOIN
    KETTLE_SCM.SUB_CATEGORY_DEFINITION sc ON pd.SUB_CATEGORY_ID = sc.SUB_CATEGORY_ID
        INNER JOIN
    KETTLE_SCM.UNIT_SKU_MAPPING us ON dce.UNIT_ID = us.UNIT_ID
        AND sd.SKU_ID = us.SKU_ID
        AND us.MAPPING_STATUS = 'ACTIVE'
        INNER JOIN
    KETTLE_SCM.INVENTORY_LISTS il ON il.LIST_ID = sd.INVENTORY_LIST_ID
WHERE
    sd.SKU_STATUS = 'ACTIVE'
        AND dce.CLOSURE_EVENT_TYPE = 'WH_OPENING'
        AND ud.UNIT_ID IN (22001 , 22002, 24001, 24002)
GROUP BY se.SKU_ID,ud.UNIT_REGION
ORDER BY ud.UNIT_REGION) a 
						]]>
					</content>
				</report>

			</reports>
		</category>

		<category name="SCM Report: Variance Summary - Kitchen and Warehouse" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>"
                        schedule="">
			<reports>
				<report id="1" name="Variance Summary - Kitchen/Warehouse" executionType="SQL" attachmentType="EXCEL">
					<content>
						<![CDATA[
SELECT
   B.UNIT_NAME,
   B.GENERATION_DATE,
   B.DAILY_VARIANCE_COST_MOD,
   A.VARIANCE_COST_MOD_MTD,
   A.VARIANCE_COST_REAL_MTD,
   A.MTD_PERCENTAGE,
   A.TARGET
FROM
   (SELECT
       ud.UNIT_ID,
           ud.UNIT_NAME,
           ROUND(SUM(ABS(inv.VARIANCE_COST)), 2) VARIANCE_COST_MOD_MTD,
           ROUND(SUM(inv.VARIANCE_COST), 2) VARIANCE_COST_REAL_MTD,
           CASE
               WHEN ud.UNIT_ID = 24001 THEN CONCAT(ROUND(SUM(inv.VARIANCE_COST) / 15000, 2), '%')
               WHEN ud.UNIT_ID = 24002 THEN CONCAT(ROUND(SUM(inv.VARIANCE_COST) / 10000, 2), '%')
               WHEN ud.UNIT_ID = 22001 THEN CONCAT(ROUND(SUM(inv.VARIANCE_COST) / 150000, 2), '%')
               WHEN ud.UNIT_ID = 22002 THEN CONCAT(ROUND(SUM(inv.VARIANCE_COST) / 50000, 2), '%')
           END AS MTD_PERCENTAGE,
           '1%' AS TARGET
   FROM
       KETTLE_SCM.DAY_CLOSE_EVENT dce
   INNER JOIN KETTLE_SCM.INVENTORY_DRILLDOWN inv ON dce.EVENT_ID = inv.CLOSURE_EVENT_ID
   INNER JOIN KETTLE_SCM.UNIT_DETAIL ud ON ud.UNIT_ID = dce.UNIT_ID
   INNER JOIN KETTLE_SCM.SKU_DEFINITION sd ON inv.SKU_ID = sd.SKU_ID
   WHERE
       DATE(dce.BUSINESS_DATE) BETWEEN DATE_ADD(DATE(DATE_SUB(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL DAYOFMONTH(ADDTIME(UTC_TIMESTAMP, '05:30:00')) - 1 DAY)), INTERVAL 5 HOUR) AND SUBDATE(CURRENT_DATE, 2)
           AND dce.STATUS <> 'CANCELLED'
           AND ud.UNIT_ID IN (22001 , 22002, 24001, 24002)
           AND sd.SKU_STATUS = 'ACTIVE'
           AND sd.INVENTORY_LIST_ID <> '1'
   GROUP BY ud.UNIT_ID) A
       LEFT JOIN
   (SELECT
       ud.UNIT_ID,
       ud.UNIT_NAME,
           DATE(dce.GENERATION_TIME) AS GENERATION_DATE,
           ROUND(SUM(ABS(inv.VARIANCE_COST)),2) DAILY_VARIANCE_COST_MOD
   FROM
       KETTLE_SCM.DAY_CLOSE_EVENT dce
   INNER JOIN KETTLE_SCM.INVENTORY_DRILLDOWN inv ON dce.EVENT_ID = inv.CLOSURE_EVENT_ID
   INNER JOIN KETTLE_SCM.UNIT_DETAIL ud ON ud.UNIT_ID = dce.UNIT_ID
   INNER JOIN KETTLE_SCM.SKU_DEFINITION sd ON inv.SKU_ID = sd.SKU_ID
   WHERE
       DATE(dce.BUSINESS_DATE) = SUBDATE(CURRENT_DATE, 2)
           AND dce.STATUS <> 'CANCELLED'
           AND sd.SKU_STATUS = 'ACTIVE'
           AND sd.INVENTORY_LIST_ID <> '1'
   GROUP BY ud.UNIT_ID) B ON A.UNIT_ID = B.UNIT_ID
 ]]>
					</content>
				</report>
				<report id="2" name="ZERO Variance for Kitchen and Warehouse" executionType="SQL">
					<content>
						<![CDATA[
SELECT A.*,B.ZERO_VARIANCE_COST_MTD_MOD FROM 
( SELECT
  ud.UNIT_ID,
  ud.UNIT_NAME,
  ROUND(SUM(ABS(inv.VARIANCE_COST)),2) ZERO_VARIANCE_COST_MOD
FROM
  KETTLE_SCM.DAY_CLOSE_EVENT dce
      INNER JOIN
  KETTLE_SCM.INVENTORY_DRILLDOWN inv ON dce.EVENT_ID = inv.CLOSURE_EVENT_ID
      INNER JOIN
  KETTLE_SCM.UNIT_DETAIL ud ON ud.UNIT_ID = dce.UNIT_ID
      INNER JOIN
  KETTLE_SCM.SKU_DEFINITION sd ON inv.SKU_ID = sd.SKU_ID
WHERE
  DATE(dce.BUSINESS_DATE) = SUBDATE(CURRENT_DATE, 2)
        AND dce.STATUS <> 'CANCELLED'
        AND ud.UNIT_ID IN (22001 , 22002, 24001, 24002)
        AND sd.INVENTORY_LIST_ID=16
GROUP BY ud.UNIT_ID ) A
  
LEFT JOIN 
(
SELECT
  ud.UNIT_ID,
  ud.UNIT_NAME,
  ROUND(SUM(ABS(inv.VARIANCE_COST)),2) ZERO_VARIANCE_COST_MTD_MOD
FROM
  KETTLE_SCM.DAY_CLOSE_EVENT dce
      INNER JOIN
  KETTLE_SCM.INVENTORY_DRILLDOWN inv ON dce.EVENT_ID = inv.CLOSURE_EVENT_ID
      INNER JOIN
  KETTLE_SCM.UNIT_DETAIL ud ON ud.UNIT_ID = dce.UNIT_ID
      INNER JOIN
  KETTLE_SCM.SKU_DEFINITION sd ON inv.SKU_ID = sd.SKU_ID
WHERE
  DATE(dce.BUSINESS_DATE) BETWEEN DATE_ADD(DATE(DATE_SUB(ADDTIME(UTC_TIMESTAMP, '05:30:00'),
                INTERVAL DAYOFMONTH(ADDTIME(UTC_TIMESTAMP, '05:30:00')) - 1 DAY)),
        INTERVAL 5 HOUR) AND SUBDATE(CURRENT_DATE, 2)
        AND dce.STATUS <> 'CANCELLED'
        AND ud.UNIT_ID IN (22001 , 22002, 24001, 24002)
        AND sd.INVENTORY_LIST_ID=16
GROUP BY ud.UNIT_ID ) B
ON A.UNIT_ID = B.UNIT_ID

 ]]>
					</content>
				</report>
				<report id="3" name=" Raw Variance Data for Kitchen and Warehouse" executionType="SQL">
					<content>
						<![CDATA[
SELECT * FROM  (
 SELECT
   ud.UNIT_NAME,
   DATE(dce.GENERATION_TIME) as GENERATION_DATE,
   dce.UPDATED_BY,
   inv.SKU_ID,
   sd.SKU_NAME,
   inv.UOM,
   inv.PRICE,
   inv.OPENING,
   inv.TRANSFERRED,
   inv.RECEIVED,
   inv.CONSUMPTION,
   inv.BOOKED,
   inv.WASTED,
   inv.EXPECTED_CLOSING,
   inv.ACTUAL_CLOSING,
   inv.VARIANCE,
   ABS(inv.VARIANCE_COST) VARIANCE_COST
FROM
   KETTLE_SCM.DAY_CLOSE_EVENT dce
       INNER JOIN
   KETTLE_SCM.INVENTORY_DRILLDOWN inv ON dce.EVENT_ID = inv.CLOSURE_EVENT_ID
       INNER JOIN
   KETTLE_SCM.UNIT_DETAIL ud ON ud.UNIT_ID = dce.UNIT_ID
       INNER JOIN
   KETTLE_SCM.SKU_DEFINITION sd ON inv.SKU_ID = sd.SKU_ID
WHERE
        dce.BUSINESS_DATE = SUBDATE(CURRENT_DATE,2)
	AND dce.STATUS <> 'CANCELLED'
	AND inv.VARIANCE_COST <> '0') a

 ]]>
					</content>
				</report>
			</reports>
		</category>
		<category name="SCM Report: Fullfilment Percentage - Kitchen and Warehouse" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>"
                        schedule="">
			<reports>
				<report id="4" name="Fullfilment Percentage - Kitchen and Warehouse" executionType="SQL" attachmentType="EXCEL">
					<content>
						<![CDATA[
SELECT
   T.*
FROM
   (SELECT
       u.UNIT_NAME,
           CONCAT(ROUND(AVG(CASE
               WHEN (gi.RECEIVED_QUANTITY / ri.REQUESTED_QUANTITY) > 1 THEN 100
               ELSE (gi.RECEIVED_QUANTITY / ri.REQUESTED_QUANTITY) * 100
           END),2),'%') AS Percentage
   FROM
       KETTLE_SCM.GOODS_RECEIVED_ITEM gi
   INNER JOIN KETTLE_SCM.GOODS_RECEIVED g ON gi.GOODS_RECEIVED_ID = g.GOODS_RECEIVED_ID
   INNER JOIN KETTLE_SCM.REQUEST_ORDER_ITEM ri ON ri.REQUEST_ORDER_ITEM_ID = gi.REQUEST_ORDER_ITEM_ID
   INNER JOIN KETTLE_MASTER.UNIT_DETAIL u ON u.UNIT_ID = g.GENERATION_UNIT_ID
   INNER JOIN KETTLE_SCM.REQUEST_ORDER r ON r.REQUEST_ORDER_ID = g.REQUEST_ORDER_ID
   INNER JOIN KETTLE_SCM.TRANSFER_ORDER t ON t.TRANSFER_ORDER_ID = g.TRANSFER_ORDER_ID
       AND u.UNIT_CATEGORY IN ('KITCHEN' , 'WAREHOUSE')
       AND g.GOODS_RECEIVED_STATUS = 'SETTLED'
       AND DATE(g.LAST_UPDATE_TIME) = SUBDATE(CURRENT_DATE,1)
   GROUP BY u.UNIT_NAME) T
						 ]]>
					</content>
				</report>
				<report id="5" name="Fullfilment Percentage Last 30 Days - Kitchen/Warehouse" executionType="SQL" attachmentType="EXCEL">
					<content>
						<![CDATA[
SELECT * FROM  (SELECT
      u.UNIT_NAME,
          CONCAT(ROUND(AVG(CASE
              WHEN (gi.RECEIVED_QUANTITY / ri.REQUESTED_QUANTITY) > 1 THEN 100
              ELSE (gi.RECEIVED_QUANTITY / ri.REQUESTED_QUANTITY) * 100
          END),2),'%') AS Percentage
  FROM
      KETTLE_SCM.GOODS_RECEIVED_ITEM gi
  INNER JOIN KETTLE_SCM.GOODS_RECEIVED g ON gi.GOODS_RECEIVED_ID = g.GOODS_RECEIVED_ID
  INNER JOIN KETTLE_SCM.REQUEST_ORDER_ITEM ri ON ri.REQUEST_ORDER_ITEM_ID = gi.REQUEST_ORDER_ITEM_ID
  INNER JOIN KETTLE_MASTER.UNIT_DETAIL u ON u.UNIT_ID = g.GENERATION_UNIT_ID
  INNER JOIN KETTLE_SCM.REQUEST_ORDER r ON r.REQUEST_ORDER_ID = g.REQUEST_ORDER_ID
  INNER JOIN KETTLE_SCM.TRANSFER_ORDER t ON t.TRANSFER_ORDER_ID = g.TRANSFER_ORDER_ID
      AND u.UNIT_CATEGORY IN ('KITCHEN' , 'WAREHOUSE')
      AND g.GOODS_RECEIVED_STATUS = 'SETTLED'
      AND DATE(g.LAST_UPDATE_TIME) <= SUBDATE(CURRENT_DATE,1)
      AND DATE(g.LAST_UPDATE_TIME) >= SUBDATE(CURRENT_DATE,30)
  GROUP BY u.UNIT_NAME ) a
						]]>
					</content>
				</report>
				<report id="6" name="Fullfilment Details - Kitchen/Warehouse" executionType="SQL" attachmentType="EXCEL">
					<content>
						<![CDATA[
SELECT
   T.*
FROM
   (SELECT
       g.GOODS_RECEIVED_ID,
           u.UNIT_NAME AS TRANSFERRING_UNIT,
           ux.UNIT_NAME AS REQUESTING_RECEIVING_UNIT,
           p.PRODUCT_NAME,
           gi.SKU_NAME,
           ri.REQUESTED_QUANTITY,
           gi.TRANSFERRED_QUANTITY,
           gi.RECEIVED_QUANTITY,
           (gi.RECEIVED_QUANTITY * s.NEGOTIATED_UNIT_PRICE) AS COST,
           gi.UNIT_OF_MEASURE,
           s.NEGOTIATED_UNIT_PRICE AS UNIT_PRICE,
           c.CATEGORY_NAME,
           sc.SUB_CATEGORY_NAME,
           r.GENERATION_TIME AS REQUESTING_TIME,
           t.GENERATION_TIME AS TRANSFER_TIME,
           g.LAST_UPDATE_TIME AS RECEIVING_TIME
   FROM
       KETTLE_SCM.GOODS_RECEIVED_ITEM gi
   INNER JOIN KETTLE_SCM.GOODS_RECEIVED g ON gi.GOODS_RECEIVED_ID = g.GOODS_RECEIVED_ID
   INNER JOIN KETTLE_SCM.SKU_DEFINITION s ON s.SKU_ID = gi.SKU_ID
   INNER JOIN KETTLE_SCM.PRODUCT_DEFINITION p ON p.PRODUCT_ID = s.LINKED_PRODUCT_ID
   INNER JOIN KETTLE_SCM.REQUEST_ORDER_ITEM ri ON ri.REQUEST_ORDER_ITEM_ID = gi.REQUEST_ORDER_ITEM_ID
   INNER JOIN KETTLE_MASTER.UNIT_DETAIL u ON u.UNIT_ID = g.GENERATION_UNIT_ID
   INNER JOIN KETTLE_MASTER.UNIT_DETAIL ux ON ux.UNIT_ID = g.GENERATED_FOR_UNIT_ID
   INNER JOIN KETTLE_SCM.CATEGORY_DEFINITION c ON c.CATEGORY_ID = p.CATEGORY_ID
   INNER JOIN KETTLE_SCM.SUB_CATEGORY_DEFINITION sc ON sc.SUB_CATEGORY_ID = p.SUB_CATEGORY_ID
   INNER JOIN KETTLE_SCM.REQUEST_ORDER r ON r.REQUEST_ORDER_ID = g.REQUEST_ORDER_ID
   INNER JOIN KETTLE_SCM.TRANSFER_ORDER t ON t.TRANSFER_ORDER_ID = g.TRANSFER_ORDER_ID
AND u.UNIT_CATEGORY IN ('KITCHEN' , 'WAREHOUSE')
      AND g.GOODS_RECEIVED_STATUS = 'SETTLED'
      AND DATE(g.LAST_UPDATE_TIME) = SUBDATE(CURRENT_DATE,1)
      AND gi.RECEIVED_QUANTITY < ri.REQUESTED_QUANTITY) T
				 ]]>
					</content>
				</report>
			</reports>
		</category>
		<category name="SCM Report: Wastage report - Kitchen/Warehouse" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>"
                        schedule="">
			<reports>
				<report id="7" name="Internal Consumption Wastage" executionType="SQL" attachmentType="EXCEL">
					<content>
						<![CDATA[
SELECT 
    C.UNIT_NAME,
    COALESCE(A.TODAY_COST, 0) TODAY_COST,
    COALESCE(B.MTD_COST, 0) MTD_COST,
    C.TARGET
FROM
    (SELECT 
        UNIT_NAME,
            CASE
                WHEN UNIT_ID = 24001 THEN '90000'
                WHEN UNIT_ID = 24002 THEN '55000'
                WHEN UNIT_ID = 22001 THEN '15000'
                WHEN UNIT_ID = 22002 THEN '10000'
            END AS TARGET
    FROM
        KETTLE_MASTER.UNIT_DETAIL
    WHERE
        UNIT_ID IN (24001 , 24002, 22001, 22002)) C
        LEFT JOIN
    (SELECT 
        ud.UNIT_NAME, ROUND(SUM(wid.COST)) AS TODAY_COST
    FROM
        KETTLE_SCM.WASTAGE_ITEM_DATA wid
    INNER JOIN KETTLE_SCM.WASTAGE_EVENT we ON wid.WASTAGE_ID = we.WASTAGE_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON we.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE_SCM.COMMENT_TYPE_MAPPING ctm ON ctm.COMMENT_VALUE = wid.COMMENT
        AND we.STATUS = 'SETTLED'
        AND we.BUSINESS_DATE = SUBDATE(CURRENT_DATE, 1)
        AND ud.UNIT_CATEGORY IN ('KITCHEN' , 'WAREHOUSE')
        AND wid.COMMENT = 'INTERNAL CONSUMPTION'
    GROUP BY ud.UNIT_NAME) A ON A.UNIT_NAME = C.UNIT_NAME
        LEFT JOIN
    (SELECT 
        ud.UNIT_NAME, ROUND(SUM(wid.COST)) AS MTD_COST
    FROM
        KETTLE_SCM.WASTAGE_ITEM_DATA wid
    INNER JOIN KETTLE_SCM.WASTAGE_EVENT we ON wid.WASTAGE_ID = we.WASTAGE_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON we.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE_SCM.COMMENT_TYPE_MAPPING ctm ON ctm.COMMENT_VALUE = wid.COMMENT
        AND we.STATUS = 'SETTLED'
        AND we.BUSINESS_DATE BETWEEN DATE_ADD(DATE(DATE_SUB(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL DAYOFMONTH(ADDTIME(UTC_TIMESTAMP, '05:30:00')) - 1 DAY)), INTERVAL 5 HOUR) AND SUBDATE(CURRENT_DATE, 1)
        AND ud.UNIT_CATEGORY IN ('KITCHEN' , 'WAREHOUSE')
        AND wid.COMMENT = 'INTERNAL CONSUMPTION'
    GROUP BY ud.UNIT_NAME) B ON B.UNIT_NAME = C.UNIT_NAME;

						 ]]>
					</content>
				</report>

				<report id="8" name="Other Wastage Cost" executionType="SQL" attachmentType="EXCEL">
					<content>
						<![CDATA[

SELECT 
    C.UNIT_NAME,
    COALESCE(A.TODAY_COST, 0) TODAY_COST,
    COALESCE(B.MTD_COST, 0) MTD_COST,
    C.TARGET
FROM
    (SELECT 
        UNIT_NAME,
            CASE
                WHEN UNIT_ID = 24001 THEN '25000'
                WHEN UNIT_ID = 24002 THEN '20000'
                WHEN UNIT_ID = 22001 THEN '15000'
                WHEN UNIT_ID = 22002 THEN '15000'
            END AS TARGET
    FROM
        KETTLE_MASTER.UNIT_DETAIL
    WHERE
        UNIT_ID IN (24001 , 24002, 22001, 22002)) C
        LEFT JOIN
    (SELECT 
        ud.UNIT_NAME, ROUND(SUM(wid.COST)) AS TODAY_COST
    FROM
        KETTLE_SCM.WASTAGE_ITEM_DATA wid
    INNER JOIN KETTLE_SCM.WASTAGE_EVENT we ON wid.WASTAGE_ID = we.WASTAGE_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON we.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE_SCM.COMMENT_TYPE_MAPPING ctm ON ctm.COMMENT_VALUE = wid.COMMENT
        AND we.STATUS = 'SETTLED'
        AND we.BUSINESS_DATE = SUBDATE(CURRENT_DATE, 1)
        AND ud.UNIT_CATEGORY IN ('KITCHEN' , 'WAREHOUSE')
        AND wid.COMMENT <> 'INTERNAL CONSUMPTION'
    GROUP BY ud.UNIT_NAME) A ON A.UNIT_NAME = C.UNIT_NAME
        LEFT JOIN
    (SELECT 
        ud.UNIT_NAME, ROUND(SUM(wid.COST)) AS MTD_COST
    FROM
        KETTLE_SCM.WASTAGE_ITEM_DATA wid
    INNER JOIN KETTLE_SCM.WASTAGE_EVENT we ON wid.WASTAGE_ID = we.WASTAGE_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON we.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE_SCM.COMMENT_TYPE_MAPPING ctm ON ctm.COMMENT_VALUE = wid.COMMENT
        AND we.STATUS = 'SETTLED'
        AND we.BUSINESS_DATE BETWEEN DATE_ADD(DATE(DATE_SUB(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL DAYOFMONTH(ADDTIME(UTC_TIMESTAMP, '05:30:00')) - 1 DAY)), INTERVAL 5 HOUR) AND SUBDATE(CURRENT_DATE, 1)
        AND ud.UNIT_CATEGORY IN ('KITCHEN' , 'WAREHOUSE')
        AND wid.COMMENT <>'INTERNAL CONSUMPTION'
    GROUP BY ud.UNIT_NAME) B ON B.UNIT_NAME = C.UNIT_NAME;
    
    

						 ]]>
					</content>
				</report>

				<report id="8" name="Wastage Details" executionType="SQL" attachmentType="EXCEL">
					<content>
						<![CDATA[
SELECT 
    *
FROM
    (SELECT 
        wid.PRODUCT_ID,
            pd.PRODUCT_NAME,
            cd.CATEGORY_NAME,
            scd.SUB_CATEGORY_NAME,
            ud.UNIT_NAME,
            utm.UNIT_TYPE,
            ld.CITY,
            wid.PRICE,
            wid.QUANTITY,
            wid.COST AS COST,
            pd.UNIT_OF_MEASURE,
            we.BUSINESS_DATE,
            we.GENERATION_TIME,
            we.GENERATED_BY,
            wid.COMMENT,
            CASE
        	WHEN ctm.COMMENT_TYPE IS NOT NULL THEN ctm.COMMENT_TYPE
        	ELSE (CASE
			WHEN we.LINKED_GR_ID IS NOT NULL THEN 'Lost Inventory'
			ELSE 'Cafe Wastage' 
		     END)
	    END AS COMMENT_TYPE
    FROM
        KETTLE_SCM.WASTAGE_ITEM_DATA wid
    INNER JOIN KETTLE_SCM.WASTAGE_EVENT we ON wid.WASTAGE_ID = we.WASTAGE_ID
    INNER JOIN KETTLE_SCM.PRODUCT_DEFINITION pd ON wid.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE_SCM.CATEGORY_DEFINITION cd ON pd.CATEGORY_ID = cd.CATEGORY_ID
    INNER JOIN KETTLE_SCM.SUB_CATEGORY_DEFINITION scd ON pd.SUB_CATEGORY_ID = scd.SUB_CATEGORY_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON we.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE_SCM.UNIT_TYPE_MAPPING utm ON ud.UNIT_CATEGORY = utm.UNIT_CATEGORY
    INNER JOIN KETTLE_MASTER.LOCATION_DETAIL ld ON ud.LOCATION_DETAIL_ID = ld.LOCATION_ID
    INNER JOIN KETTLE_SCM.COMMENT_TYPE_MAPPING ctm ON ctm.COMMENT_VALUE = wid.COMMENT
        AND we.STATUS = 'SETTLED'
        AND we.BUSINESS_DATE = SUBDATE(CURRENT_DATE, 1)
        AND ud.UNIT_CATEGORY IN ('KITCHEN' , 'WAREHOUSE')) a
						 ]]>
					</content>
				</report>
			</reports>
		</category>

		<category name="Payment Request Details for SCM" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>"
                        schedule="" attachmentType="EXCEL" >
			<reports>
				<report id="9" name="Payment Request Data" executionType="SQL">
					<content>
						<![CDATA[
SELECT 
    u.UNIT_NAME,
    ROUND(AVG(DATEDIFF(pr.CREATION_TIME, pi.INVOICE_DATE)),
            2) AS PR_REQUEST_TAT_IN_DAYS,
    COUNT(DISTINCT (pr.PAYMENT_REQUEST_ID)) AS NUMBER_OF_PR_CREATED
FROM
    KETTLE_SCM.PAYMENT_REQUEST pr
        INNER JOIN
    KETTLE_MASTER.UNIT_DETAIL u ON u.UNIT_ID = pr.REQUESTING_UNIT
        INNER JOIN
    KETTLE_SCM.PAYMENT_INVOICE pi ON pr.PAYMENT_REQUEST_ID = pi.PAYMENT_REQUEST_ID
        GROUP BY u.UNIT_NAME
						 ]]>
					</content>
				</report>
				<report id="10" name="Payment Request Pending" executionType="SQL">
					<content>
						<![CDATA[
SELECT 
    u.UNIT_NAME,
    SUM(CASE
        WHEN vg.PAYMENT_REQUEST_ID IS NULL 
        AND vg.DOCUMENT_UPLOADED ='INVOICE' 
        THEN 1
        ELSE 0
    END) PR_NOT_CREATED_INVOICE,
    SUM(CASE
        WHEN vg.PAYMENT_REQUEST_ID IS NULL 
        AND vg.DOCUMENT_UPLOADED ='DELIVERY_CHALLAN' 
        THEN 1
        ELSE 0
    END) PR_NOT_CREATED_DELIVERY_CHALLAN
     
FROM
    KETTLE_SCM.VENDOR_GOODS_RECEIVED_DATA vg
        LEFT JOIN
    KETTLE_SCM.PAYMENT_REQUEST pr ON pr.PAYMENT_REQUEST_ID = vg.PAYMENT_REQUEST_ID
        LEFT JOIN
    KETTLE_MASTER.UNIT_DETAIL u ON u.UNIT_ID = vg.DELIVERY_UNIT_ID
        LEFT JOIN
    KETTLE_SCM.PAYMENT_INVOICE pi ON pr.PAYMENT_REQUEST_ID = pi.PAYMENT_REQUEST_ID
WHERE
        vg.CREATED_AT > '2017-11-01' 
        AND vg.TO_BE_PAID = 'Y'
        GROUP BY u.UNIT_NAME
						 ]]>
					</content>
				</report>

				<report id="11" name="Payment Status TAT" executionType="SQL">
					<content>
						<![CDATA[
SELECT 
    round(avg(datediff( pr1.UPDATE_TIME,pr.CREATION_TIME)),2) AS TAT_ACKNOWLEDGE_IN_DAYS,
    round(avg(datediff( pr2.UPDATE_TIME,pr1.UPDATE_TIME)),2) AS TAT_APPROVED_IN_DAYS,
    round(avg(datediff( pr3.UPDATE_TIME,pr2.UPDATE_TIME)),2) AS TAT_SENT_FOR_PAYMENT_IN_DAYS,
    round(avg(datediff(pr4.UPDATE_TIME,pr3.UPDATE_TIME)),2) AS TAT_PAID_IN_DAYS
    FROM
    KETTLE_SCM.PAYMENT_REQUEST_STATUS_LOG pr1
    LEFT JOIN 
    KETTLE_SCM.PAYMENT_REQUEST_STATUS_LOG pr2 ON pr1.PAYMENT_REQUEST_ID = pr2.PAYMENT_REQUEST_ID AND pr1.TO_STATUS = pr2.FROM_STATUS
    LEFT JOIN 
    KETTLE_SCM.PAYMENT_REQUEST_STATUS_LOG pr3 ON pr2.PAYMENT_REQUEST_ID = pr3.PAYMENT_REQUEST_ID AND pr2.TO_STATUS = pr3.FROM_STATUS
    LEFT JOIN
    KETTLE_SCM.PAYMENT_REQUEST_STATUS_LOG pr4 ON pr3.PAYMENT_REQUEST_ID = pr4.PAYMENT_REQUEST_ID AND pr3.TO_STATUS = pr4.FROM_STATUS
    LEFT JOIN
    KETTLE_SCM.PAYMENT_REQUEST_STATUS_LOG pr5 ON pr4.PAYMENT_REQUEST_ID = pr5.PAYMENT_REQUEST_ID AND pr4.TO_STATUS = pr5.FROM_STATUS
    LEFT JOIN
    KETTLE_SCM.PAYMENT_REQUEST_STATUS_LOG pr6 ON pr5.PAYMENT_REQUEST_ID = pr6.PAYMENT_REQUEST_ID AND pr5.TO_STATUS = pr6.FROM_STATUS
    LEFT JOIN
    KETTLE_SCM.PAYMENT_REQUEST pr ON pr.PAYMENT_REQUEST_ID = pr1.PAYMENT_REQUEST_ID
    WHERE pr1.FROM_STATUS = 'CREATED'
    AND pr2.TO_STATUS <> 'CANCELLED'
ORDER BY pr1.PAYMENT_REQUEST_ID
						 ]]>
					</content>
				</report>

				<report id="12" name="Payment Request Status Wise Count" executionType="SQL" skipInline="true">
					<content>
						<![CDATA[
Select 
CURRENT_STATUS AS STATUS,
COUNT(DISTINCT PAYMENT_REQUEST_ID) AS COUNT_OF_PR
FROM KETTLE_SCM.PAYMENT_REQUEST 
GROUP BY CURRENT_STATUS;
						 ]]>
					</content>
				</report>

				<report id="13" name="Pending Payment Request Details" executionType="SQL" skipInline="true"
>
					<content>
						<![CDATA[

Select vg.GOODS_RECEIVED_ID,
vg.CREATED_AT,
ud.UNIT_NAME,
vd.ENTITY_NAME,
vg.DOCUMENT_UPLOADED,
vg.TOTAL_PRICE,
vg.TOTAL_TAX,
vg.TOTAL_AMOUNT,
vg.DOCUMENT_DATE
from KETTLE_SCM.VENDOR_GOODS_RECEIVED_DATA vg INNER JOIN KETTLE_SCM.VENDOR_DETAIL_DATA vd ON vg.VENDOR_ID = vd.VENDOR_ID
INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID=vg.DELIVERY_UNIT_ID
WHERE PAYMENT_REQUEST_ID IS NULL
AND CREATED_AT > '2017-11-01'
AND vg.TO_BE_PAID = 'Y'
ORDER BY ud.UNIT_NAME 						 ]]>
					</content>
				</report>

				<report id="14" name="Payment Request Details" executionType="SQL" skipInline="true">
					<content>
						<![CDATA[
SELECT pr.PAYMENT_REQUEST_ID,
pr.VENDOR_ID,
vd.ENTITY_NAME,
pr.CREATION_TIME,
pr.CURRENT_STATUS,
pr.PROPOSED_AMOUNT,
pr.IS_BLOCKED,
pr.REQUESTING_UNIT,
ud.UNIT_NAME,
pr.GR_DOC_TYPE,
pr.INVOICE_NUMBER,
pr.PAYMENT_CYCLE
from KETTLE_SCM.PAYMENT_REQUEST pr INNER JOIN KETTLE_SCM.VENDOR_DETAIL_DATA vd ON pr.VENDOR_ID = vd.VENDOR_ID
INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID=pr.REQUESTING_UNIT
WHERE pr.CURRENT_STATUS IN ('INITIATED','REJECTED','ACKNOWLEDGED')
ORDER BY pr.CURRENT_STATUS
						 ]]>
					</content>
				</report>

			</reports>
		</category>


		<category name="Payment Request Details For Finance" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>"
                        schedule="" attachmentType="EXCEL" >
			<reports>
				<report id="15" name="Payment Request Status Wise Count" executionType="SQL">
					<content>
						<![CDATA[
Select 
CURRENT_STATUS AS STATUS,
COUNT(DISTINCT PAYMENT_REQUEST_ID) AS COUNT_OF_PR
FROM KETTLE_SCM.PAYMENT_REQUEST 
GROUP BY CURRENT_STATUS
						 ]]>
					</content>
				</report>
				<report id="15" name="Payment Status TAT" executionType="SQL">
					<content>
						<![CDATA[
SELECT 
    round(avg(datediff( pr1.UPDATE_TIME,pr.CREATION_TIME)),2) AS TAT_ACKNOWLEDGE_IN_DAYS,
    round(avg(datediff( pr2.UPDATE_TIME,pr1.UPDATE_TIME)),2) AS TAT_APPROVED_IN_DAYS,
    round(avg(datediff( pr3.UPDATE_TIME,pr2.UPDATE_TIME)),2) AS TAT_SENT_FOR_PAYMENT_IN_DAYS,
    round(avg(datediff(pr4.UPDATE_TIME,pr3.UPDATE_TIME)),2) AS TAT_PAID_IN_DAYS
    FROM
    KETTLE_SCM.PAYMENT_REQUEST_STATUS_LOG pr1
    LEFT JOIN 
    KETTLE_SCM.PAYMENT_REQUEST_STATUS_LOG pr2 ON pr1.PAYMENT_REQUEST_ID = pr2.PAYMENT_REQUEST_ID AND pr1.TO_STATUS = pr2.FROM_STATUS
    LEFT JOIN 
    KETTLE_SCM.PAYMENT_REQUEST_STATUS_LOG pr3 ON pr2.PAYMENT_REQUEST_ID = pr3.PAYMENT_REQUEST_ID AND pr2.TO_STATUS = pr3.FROM_STATUS
    LEFT JOIN
    KETTLE_SCM.PAYMENT_REQUEST_STATUS_LOG pr4 ON pr3.PAYMENT_REQUEST_ID = pr4.PAYMENT_REQUEST_ID AND pr3.TO_STATUS = pr4.FROM_STATUS
    LEFT JOIN
    KETTLE_SCM.PAYMENT_REQUEST_STATUS_LOG pr5 ON pr4.PAYMENT_REQUEST_ID = pr5.PAYMENT_REQUEST_ID AND pr4.TO_STATUS = pr5.FROM_STATUS
    LEFT JOIN
    KETTLE_SCM.PAYMENT_REQUEST_STATUS_LOG pr6 ON pr5.PAYMENT_REQUEST_ID = pr6.PAYMENT_REQUEST_ID AND pr5.TO_STATUS = pr6.FROM_STATUS
    LEFT JOIN
    KETTLE_SCM.PAYMENT_REQUEST pr ON pr.PAYMENT_REQUEST_ID = pr1.PAYMENT_REQUEST_ID
    WHERE pr1.FROM_STATUS = 'CREATED'
    AND pr2.TO_STATUS <> 'CANCELLED'
ORDER BY pr1.PAYMENT_REQUEST_ID
						 ]]>
					</content>
				</report>



				<report id="16" name="Payment Request Details" executionType="SQL" skipInline="true">
					<content>
						<![CDATA[

SELECT pr.PAYMENT_REQUEST_ID,
pr.VENDOR_ID,
vd.ENTITY_NAME,
pr.CREATION_TIME,
pr.CURRENT_STATUS,
pr.PROPOSED_AMOUNT,
pr.IS_BLOCKED,
pr.REQUESTING_UNIT,
ud.UNIT_NAME,
pr.GR_DOC_TYPE,
pr.INVOICE_NUMBER,
pr.PAYMENT_CYCLE
from KETTLE_SCM.PAYMENT_REQUEST pr INNER JOIN KETTLE_SCM.VENDOR_DETAIL_DATA vd ON pr.VENDOR_ID = vd.VENDOR_ID
INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID=pr.REQUESTING_UNIT
WHERE pr.CURRENT_STATUS IN ('CREATED','ACKNOWLEDGED','APPROVED','SENT_FOR_PAYMENT')
ORDER BY pr.CURRENT_STATUS
						 ]]>
					</content>
				</report>

			</reports>
		</category>
		<category name="SCM Report Booking Details" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>,<EMAIL>"
                        schedule="" attachmentType="EXCEL">
			<reports>
				<report id="17" name="Bookings Done at Kitchen and Warehouse" executionType="SQL">
					<content>
						<![CDATA[
SELECT * FROM(
			SELECT 
    ud.UNIT_NAME AS UNIT,
    DATE(p.GENERATION_TIME) AS GENERATED_DATE,
    COUNT(p.BOOKING_ID) AS NUMBER_OF_BOOKINGS
FROM
    KETTLE_SCM.PRODUCTION_BOOKING p
        INNER JOIN
    KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = p.UNIT_ID
WHERE
    DATE(p.GENERATION_TIME) = SUBDATE(CURRENT_DATE, 1)
    GROUP BY ud.UNIT_NAME
    ORDER BY ud.UNIT_NAME
   ) AS s;


		]]>
					</content>
				</report>



				<report id="17" name="Details of Bookings Done at Kitchen and Warehouse" executionType="SQL" skipInline="true">
					<content>
						<![CDATA[
    SELECT * FROM (SELECT 
    ud.UNIT_NAME AS UNIT,
    DATE(p.GENERATION_TIME) AS GENERATED_DATE,
   p.PRODUCT_NAME,
   p.UOM,
   ROUND((p.QUANTITY),0) AS QUANTITY,
   ROUND((p.UNIT_PRICE),2) AS UNIT_PRICE,
   ROUND((p.TOTAL_COST),2) AS TOTAL_COST
FROM
    KETTLE_SCM.PRODUCTION_BOOKING p
        INNER JOIN
    KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = p.UNIT_ID
WHERE
    DATE(p.GENERATION_TIME) = SUBDATE(CURRENT_DATE, 1)
    ORDER BY ud.UNIT_NAME) as S;



		]]>
					</content>
				</report>
			</reports>
		</category>
		
		<category name="Price Change Audit Data Report" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>,<EMAIL>"
                        schedule="" attachmentType="EXCEL">
			<reports>
				<report id="18" name="Price Change above 5 percent" executionType="SQL" attachmentType="EXCEL">
					<content>
						<![CDATA[
    SELECT
   T.*
FROM
   (SELECT
       c.KEY_ID AS SKU_ID,
           c.KEY_TYPE,
           s.SKU_NAME,
           c.UNIT_ID,
           u.UNIT_NAME,
           c.OLD_PRICE,
           c.PRICE,
           c.TRANSACTTION_TYPE,
           c.ADD_TIME,
           ROUND(((c.PRICE - c.OLD_PRICE) / c.PRICE) * 100, 2) AS PERCENTAGE_VALUE
   FROM
       KETTLE_SCM.COST_DETAIL_AUDIT_DATA c
   INNER JOIN KETTLE_SCM.SKU_DEFINITION s ON s.SKU_ID = c.KEY_ID
   INNER JOIN KETTLE_SCM.UNIT_DETAIL u ON u.UNIT_ID =c.UNIT_ID
       AND c.PRICE <> c.OLD_PRICE
       AND c.OLD_PRICE IS NOT NULL
       AND DATE(c.ADD_TIME) >= DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00'))
       AND DATE(c.ADD_TIME) <= DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00'))
       AND c.TRANSACTTION_TYPE <> 'CONSUMPTION'
        ) T WHERE PERCENTAGE_VALUE >= 5 OR PERCENTAGE_VALUE <-5
       ORDER BY UNIT_ID;
		]]>
					</content>
				</report>
				<report id="19" name="Price Change All" executionType="SQL" attachmentType="EXCEL">
					<content>
						<![CDATA[
    SELECT 
    T.*
FROM
    (SELECT 
        c.KEY_ID AS SKU_ID,
            c.KEY_TYPE,
            s.SKU_NAME,
            c.UNIT_ID,
            u.UNIT_NAME,
            c.OLD_PRICE,
            c.PRICE,
            c.TRANSACTTION_TYPE,
            c.ADD_TIME,
            ROUND(((c.PRICE - c.OLD_PRICE) / c.PRICE) * 100, 2) AS PERCENTAGE_VALUE
    FROM
        KETTLE_SCM.COST_DETAIL_AUDIT_DATA c
    INNER JOIN KETTLE_SCM.SKU_DEFINITION s ON s.SKU_ID = c.KEY_ID
    INNER JOIN KETTLE_SCM.UNIT_DETAIL u ON u.UNIT_ID = c.UNIT_ID
        AND c.PRICE <> c.OLD_PRICE
        AND c.OLD_PRICE IS NOT NULL
        AND DATE(c.ADD_TIME) >= DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00'))
        AND DATE(c.ADD_TIME) <= DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00'))
        AND c.TRANSACTTION_TYPE <> 'CONSUMPTION') T
ORDER BY UNIT_ID;
		]]>
					</content>
				</report>

			</reports>
		</category>


<category name="Pending PO Report" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>,<EMAIL>"
                        schedule="" attachmentType="EXCEL">
			<reports>
				<report id="18" name="Count of pending PO" executionType="SQL" attachmentType="EXCEL">
					<content>
						<![CDATA[

SELECT 
    A.DELIVERY_LOCATION_ID,
    A.UNIT_NAME,
    A.PENDING_ORDER_COUNT,
    A.PENDING_AMOUNT,
    A.AGEING_IN_DAYS,
    B.PENDING_ORDER_COUNT_45_DAYS,
    B.PENDING_AMOUNT_45_DAYS
FROM
    (SELECT 
        S.DELIVERY_LOCATION_ID,
            S.UNIT_NAME,
            COUNT(DISTINCT (S.PURCHASE_ORDER_ID)) AS PENDING_ORDER_COUNT,
            FORMAT(TRUNCATE(ROUND(SUM(S.PO_AMOUNT - COALESCE(RCVD_VALUE, 0)), 10), 2), 'en_IN') AS PENDING_AMOUNT,
            ROUND(AVG(S.AGE_IN_DAYS), 2) AS AGEING_IN_DAYS
    FROM
        (SELECT 
        po.PURCHASE_ORDER_ID,
            po.GENERATION_TIME,
            po.LAST_UPDATE_TIME,
            po.PURCHASE_ORDER_STATUS,
            po.GENERATED_FOR_VENDOR_ID,
            vd.ENTITY_NAME,
            po.DELIVERY_LOCATION_ID,
            ud.UNIT_NAME,
            po.FORCE_CLOSED,
            poi.REQUESTED_ABSOLUTE_QUANTITY,
            poi.TOTAL_COST + poi.TOTAL_TAX_VALUE AS PO_AMOUNT,
            poi.RECEIVED_QUANTITY,
            poi.CGST,
            poi.SGST,
            poi.UNIT_PRICE,
            poi.PACKAGING_CONVERSION_RATIO,
            poi.NEGOTIATED_UNIT_PRICE,
            (poi.RECEIVED_QUANTITY * (poi.NEGOTIATED_UNIT_PRICE / poi.PACKAGING_CONVERSION_RATIO)) * (1 + ((CASE
                WHEN IGST IS NULL THEN (CGST + SGST)
                ELSE IGST
            END) / 100)) AS RCVD_VALUE,
            DATEDIFF(CURRENT_DATE(), DATE(po.GENERATION_TIME)) AS AGE_IN_DAYS
    FROM
        KETTLE_SCM.PURCHASE_ORDER_ITEM_DETAIL poi
    INNER JOIN KETTLE_SCM.PURCHASE_ORDER po ON po.PURCHASE_ORDER_ID = poi.PURCHASE_ORDER_ID
    INNER JOIN KETTLE_SCM.VENDOR_DETAIL_DATA vd ON vd.VENDOR_ID = po.GENERATED_FOR_VENDOR_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = po.DELIVERY_LOCATION_ID
    WHERE
        po.PURCHASE_ORDER_STATUS IN ('APPROVED' , 'IN_PROGRESS')
            AND po.DELIVERY_LOCATION_ID IN (22001 , 24001, 22002, 24002)
    ORDER BY po.DELIVERY_LOCATION_ID) AS S
    GROUP BY S.DELIVERY_LOCATION_ID) A
        LEFT JOIN
    (SELECT 
        S.DELIVERY_LOCATION_ID,
            S.UNIT_NAME,
            COUNT(DISTINCT (S.PURCHASE_ORDER_ID)) AS PENDING_ORDER_COUNT_45_DAYS,
            FORMAT(TRUNCATE(ROUND(SUM(S.PO_AMOUNT - COALESCE(RCVD_VALUE, 0)), 10), 2), 'en_IN') AS PENDING_AMOUNT_45_DAYS
    FROM
        (SELECT 
        po.PURCHASE_ORDER_ID,
            po.GENERATION_TIME,
            po.LAST_UPDATE_TIME,
            po.PURCHASE_ORDER_STATUS,
            po.GENERATED_FOR_VENDOR_ID,
            vd.ENTITY_NAME,
            po.DELIVERY_LOCATION_ID,
            ud.UNIT_NAME,
            po.FORCE_CLOSED,
            poi.REQUESTED_ABSOLUTE_QUANTITY,
            poi.TOTAL_COST + poi.TOTAL_TAX_VALUE AS PO_AMOUNT,
            poi.RECEIVED_QUANTITY,
            poi.CGST,
            poi.SGST,
            poi.UNIT_PRICE,
            poi.PACKAGING_CONVERSION_RATIO,
            poi.NEGOTIATED_UNIT_PRICE,
            (poi.RECEIVED_QUANTITY * (poi.NEGOTIATED_UNIT_PRICE / poi.PACKAGING_CONVERSION_RATIO)) * (1 + ((CASE
                WHEN IGST IS NULL THEN (CGST + SGST)
                ELSE IGST
            END) / 100)) AS RCVD_VALUE,
            DATEDIFF(CURRENT_DATE(), DATE(po.GENERATION_TIME)) AS AGE_IN_DAYS
    FROM
        KETTLE_SCM.PURCHASE_ORDER_ITEM_DETAIL poi
    INNER JOIN KETTLE_SCM.PURCHASE_ORDER po ON po.PURCHASE_ORDER_ID = poi.PURCHASE_ORDER_ID
    INNER JOIN KETTLE_SCM.VENDOR_DETAIL_DATA vd ON vd.VENDOR_ID = po.GENERATED_FOR_VENDOR_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = po.DELIVERY_LOCATION_ID
    WHERE
        po.PURCHASE_ORDER_STATUS IN ('APPROVED' , 'IN_PROGRESS')
            AND po.DELIVERY_LOCATION_ID IN (22001 , 24001, 22002, 24002)
            AND DATEDIFF(CURRENT_DATE(), DATE(po.GENERATION_TIME)) >= 45
    ORDER BY po.DELIVERY_LOCATION_ID) AS S
    GROUP BY S.DELIVERY_LOCATION_ID) B ON A.DELIVERY_LOCATION_ID = B.DELIVERY_LOCATION_ID
        AND A.UNIT_NAME = B.UNIT_NAME;
     
		]]>
					</content>
				</report>
				<report id="19" name="Details of Pending PO" executionType="SQL" attachmentType="EXCEL">
					<content>
						<![CDATA[
 SELECT * FROM (
SELECT 
    *,
    FORMAT(TRUNCATE(ROUND((S.PO_AMOUNT - COALESCE(S.RCVD_VALUE, 0)),
                    10),
            2),
        'en_IN') AS PENDING_AMOUNT
FROM
    (SELECT 
        po.PURCHASE_ORDER_ID,
            po.GENERATION_TIME,
            po.LAST_UPDATE_TIME,
            po.PURCHASE_ORDER_STATUS,
            po.GENERATED_FOR_VENDOR_ID,
            vd.ENTITY_NAME,
            po.DELIVERY_LOCATION_ID,
            ud.UNIT_NAME,
            po.FORCE_CLOSED,
            poi.REQUESTED_ABSOLUTE_QUANTITY,
            poi.TOTAL_COST + poi.TOTAL_TAX_VALUE AS PO_AMOUNT,
            poi.RECEIVED_QUANTITY,
            poi.CGST,
            poi.SGST,
            poi.UNIT_PRICE,
            poi.PACKAGING_CONVERSION_RATIO,
            poi.NEGOTIATED_UNIT_PRICE,
            (poi.RECEIVED_QUANTITY * (poi.NEGOTIATED_UNIT_PRICE / poi.PACKAGING_CONVERSION_RATIO)) * (1 + ((CASE
                WHEN IGST IS NULL THEN (CGST + SGST)
                ELSE IGST
            END) / 100)) AS RCVD_VALUE,
            DATEDIFF(CURRENT_DATE(), DATE(po.GENERATION_TIME)) AS AGE_IN_DAYS
    FROM
        KETTLE_SCM.PURCHASE_ORDER po
    INNER JOIN KETTLE_SCM.PURCHASE_ORDER_ITEM_DETAIL poi ON po.PURCHASE_ORDER_ID = poi.PURCHASE_ORDER_ID
    INNER JOIN KETTLE_SCM.VENDOR_DETAIL_DATA vd ON vd.VENDOR_ID = po.GENERATED_FOR_VENDOR_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = po.DELIVERY_LOCATION_ID
    WHERE
        po.PURCHASE_ORDER_STATUS IN ('APPROVED' , 'IN_PROGRESS')
            AND po.DELIVERY_LOCATION_ID IN (22001 , 24001, 22002, 24002)
    ORDER BY po.DELIVERY_LOCATION_ID) AS S) A
        
            		]]>
					</content>
				</report>
			</reports>
		</category>

		<category name="Mumbai Fullfilment Percentage - Kitchen and Warehouse" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>,<EMAIL>,<EMAIL>"
                        schedule="" attachmentType="EXCEL">
			<reports>
				<report id="18" name="Fullfilment Percentage - Kitchen and Warehouse" executionType="SQL" attachmentType="EXCEL">
					<content>
						<![CDATA[
SELECT
   T.*
FROM
   (SELECT
       u.UNIT_NAME,
           CONCAT(ROUND(AVG(CASE
               WHEN (gi.RECEIVED_QUANTITY / ri.REQUESTED_QUANTITY) > 1 THEN 100
               ELSE (gi.RECEIVED_QUANTITY / ri.REQUESTED_QUANTITY) * 100
           END),2),'%') AS Percentage
   FROM
       KETTLE_SCM.GOODS_RECEIVED_ITEM gi
   INNER JOIN KETTLE_SCM.GOODS_RECEIVED g ON gi.GOODS_RECEIVED_ID = g.GOODS_RECEIVED_ID
   INNER JOIN KETTLE_SCM.REQUEST_ORDER_ITEM ri ON ri.REQUEST_ORDER_ITEM_ID = gi.REQUEST_ORDER_ITEM_ID
   INNER JOIN KETTLE_MASTER.UNIT_DETAIL u ON u.UNIT_ID = g.GENERATION_UNIT_ID
   INNER JOIN KETTLE_SCM.REQUEST_ORDER r ON r.REQUEST_ORDER_ID = g.REQUEST_ORDER_ID
   INNER JOIN KETTLE_SCM.TRANSFER_ORDER t ON t.TRANSFER_ORDER_ID = g.TRANSFER_ORDER_ID
       AND u.UNIT_ID IN (22002 , 24002)
       AND g.GOODS_RECEIVED_STATUS = 'SETTLED'
       AND DATE(g.LAST_UPDATE_TIME) = SUBDATE(CURRENT_DATE,1)
   GROUP BY u.UNIT_NAME) T
]]>
					</content>
				</report>
				<report id="19" name="Fullfilment Percentage Last 30 Days - Kitchen/Warehouse" executionType="SQL" attachmentType="EXCEL">
					<content>
						<![CDATA[
 SELECT * FROM  (SELECT
      u.UNIT_NAME,
          CONCAT(ROUND(AVG(CASE
              WHEN (gi.RECEIVED_QUANTITY / ri.REQUESTED_QUANTITY) > 1 THEN 100
              ELSE (gi.RECEIVED_QUANTITY / ri.REQUESTED_QUANTITY) * 100
          END),2),'%') AS Percentage
  FROM
      KETTLE_SCM.GOODS_RECEIVED_ITEM gi
  INNER JOIN KETTLE_SCM.GOODS_RECEIVED g ON gi.GOODS_RECEIVED_ID = g.GOODS_RECEIVED_ID
  INNER JOIN KETTLE_SCM.REQUEST_ORDER_ITEM ri ON ri.REQUEST_ORDER_ITEM_ID = gi.REQUEST_ORDER_ITEM_ID
  INNER JOIN KETTLE_MASTER.UNIT_DETAIL u ON u.UNIT_ID = g.GENERATION_UNIT_ID
  INNER JOIN KETTLE_SCM.REQUEST_ORDER r ON r.REQUEST_ORDER_ID = g.REQUEST_ORDER_ID
  INNER JOIN KETTLE_SCM.TRANSFER_ORDER t ON t.TRANSFER_ORDER_ID = g.TRANSFER_ORDER_ID
      AND u.UNIT_ID IN (22002 , 24002)
      AND g.GOODS_RECEIVED_STATUS = 'SETTLED'
      AND DATE(g.LAST_UPDATE_TIME) <= SUBDATE(CURRENT_DATE,1)
      AND DATE(g.LAST_UPDATE_TIME) >= SUBDATE(CURRENT_DATE,30)
  GROUP BY u.UNIT_NAME ) a
]]>
					</content>
				</report>
<report id="19" name="Fullfilment Details - Kitchen/Warehouse" executionType="SQL" attachmentType="EXCEL">
					<content>
						<![CDATA[

SELECT
  T.*
FROM
  (SELECT
      g.GOODS_RECEIVED_ID,
          u.UNIT_NAME AS TRANSFERRING_UNIT,
          ux.UNIT_NAME AS REQUESTING_RECEIVING_UNIT,
          p.PRODUCT_NAME,
          gi.SKU_NAME,
          ri.REQUESTED_QUANTITY,
          gi.TRANSFERRED_QUANTITY,
          gi.RECEIVED_QUANTITY,
          (gi.RECEIVED_QUANTITY * s.NEGOTIATED_UNIT_PRICE) AS COST,
          gi.UNIT_OF_MEASURE,
          s.NEGOTIATED_UNIT_PRICE AS UNIT_PRICE,
          c.CATEGORY_NAME,
          sc.SUB_CATEGORY_NAME,
          r.GENERATION_TIME AS REQUESTING_TIME,
          t.GENERATION_TIME AS TRANSFER_TIME,
          g.LAST_UPDATE_TIME AS RECEIVING_TIME
  FROM
      KETTLE_SCM.GOODS_RECEIVED_ITEM gi
  INNER JOIN KETTLE_SCM.GOODS_RECEIVED g ON gi.GOODS_RECEIVED_ID = g.GOODS_RECEIVED_ID
  INNER JOIN KETTLE_SCM.SKU_DEFINITION s ON s.SKU_ID = gi.SKU_ID
  INNER JOIN KETTLE_SCM.PRODUCT_DEFINITION p ON p.PRODUCT_ID = s.LINKED_PRODUCT_ID
  INNER JOIN KETTLE_SCM.REQUEST_ORDER_ITEM ri ON ri.REQUEST_ORDER_ITEM_ID = gi.REQUEST_ORDER_ITEM_ID
  INNER JOIN KETTLE_MASTER.UNIT_DETAIL u ON u.UNIT_ID = g.GENERATION_UNIT_ID
  INNER JOIN KETTLE_MASTER.UNIT_DETAIL ux ON ux.UNIT_ID = g.GENERATED_FOR_UNIT_ID
  INNER JOIN KETTLE_SCM.CATEGORY_DEFINITION c ON c.CATEGORY_ID = p.CATEGORY_ID
  INNER JOIN KETTLE_SCM.SUB_CATEGORY_DEFINITION sc ON sc.SUB_CATEGORY_ID = p.SUB_CATEGORY_ID
  INNER JOIN KETTLE_SCM.REQUEST_ORDER r ON r.REQUEST_ORDER_ID = g.REQUEST_ORDER_ID
  INNER JOIN KETTLE_SCM.TRANSFER_ORDER t ON t.TRANSFER_ORDER_ID = g.TRANSFER_ORDER_ID
AND u.UNIT_ID IN (22002,24002)
     AND g.GOODS_RECEIVED_STATUS = 'SETTLED'
     AND DATE(g.LAST_UPDATE_TIME) = SUBDATE(CURRENT_DATE,1)
     AND gi.RECEIVED_QUANTITY < ri.REQUESTED_QUANTITY) T
]]>
					</content>
				</report>

			</reports>
		</category>
		<category name="SCM Report : Standalone TO Details - Kitchen and Warehouse" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>"
                        schedule="" attachmentType="EXCEL">
			<reports>
				<report id="1" name="Standalone TO Count" executionType="SQL" attachmentType="EXCEL">
					<content>
						<![CDATA[
SELECT * FROM (
SELECT 
    u.UNIT_NAME,
    COUNT(distinct(t.TRANSFER_ORDER_ID)) AS NUMBER_OF_STANDALONE_TO
FROM
    KETTLE_MASTER.UNIT_DETAIL u
        LEFT JOIN
    KETTLE_SCM.TRANSFER_ORDER t ON u.UNIT_ID = t.GENERATION_UNIT_ID
WHERE
    t.REQUEST_ORDER_ID IS NULL
        AND date(t.GENERATION_TIME) = SUBDATE(CURRENT_DATE,1)
        AND u.UNIT_ID IN (22001 , 22002, 24001, 24002)
        AND t.TRANSFER_ORDER_STATUS <> 'CANCELLED'
GROUP BY u.UNIT_NAME
ORDER BY u.UNIT_NAME) a

						]]>
					</content>
				</report>
				<report id="1" name="Standalone TO Detail" executionType="SQL" attachmentType="EXCEL">
					<content>
						<![CDATA[
SELECT * FROM (
SELECT 
    t.TRANSFER_ORDER_ID,
    u.UNIT_NAME AS FULLFILMENT_UNIT,
    ux.UNIT_NAME AS RECEIVING_UNIT,
    t.TRANSFER_ORDER_STATUS,
    toi.SKU_ID,
    toi.SKU_NAME,
    toi.TRANSFERRED_QUANTITY,
    toi.UNIT_OF_MEASURE,
    toi.UNIT_PRICE,
    toi.CALCULATED_AMOUNT,
    toi.TOTAL_TAX
FROM
    KETTLE_MASTER.UNIT_DETAIL u
        LEFT JOIN
    KETTLE_SCM.TRANSFER_ORDER t ON u.UNIT_ID = t.GENERATION_UNIT_ID
    LEFT JOIN
    KETTLE_SCM.TRANSFER_ORDER_ITEM toi ON toi.TRANSFER_ORDER_ID = t.TRANSFER_ORDER_ID
    INNER JOIN KETTLE_MASTER.UNIT_DETAIL ux ON ux.UNIT_ID = t.GENERATED_FOR_UNIT_ID
WHERE
    t.REQUEST_ORDER_ID IS NULL
        AND date(t.GENERATION_TIME) = SUBDATE(CURRENT_DATE, 1)
        AND u.UNIT_ID IN (22001 , 22002, 24001, 24002)
        AND t.TRANSFER_ORDER_STATUS <> 'CANCELLED'
ORDER BY u.UNIT_NAME) a
						]]>
					</content>
				</report>
			</reports>
		</category>
	</categories>
</ReportCategories>
