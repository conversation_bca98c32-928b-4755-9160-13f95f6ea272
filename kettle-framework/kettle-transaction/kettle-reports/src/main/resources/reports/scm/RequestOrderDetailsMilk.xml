<ReportCategories xmlns="http://www.w3schools.com">
	<categories>
		<category name="SCM Report: Request Order Details - Milk" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>"
                        schedule="">
			<reports>
				<report id="1" name="Request Order Details - Milk" executionType="SQL"
                                        GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT * FROM (
SELECT 
    ud.UNIT_NAME,
    DATE(ro.FULFILLMENT_DATE) FULFILLMENT_DATE,
    ro.GENERATION_TIME,
    ro.GENERATED_BY,
    FLOOR(SUM(roi.REQUESTED_ABSOLUTE_QUANTITY)) REQUESTED_QUANTITY,
    vd.VENDOR_NAME
FROM
    REQUEST_ORDER ro
        INNER JOIN
    REQUEST_ORDER_ITEM roi ON ro.REQUEST_ORDER_ID = roi.REQUEST_ORDER_ID
        AND ro.FULFILLMENT_DATE = DATE_ADD(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')),
        INTERVAL 1 DAY)
        AND ro.IS_SPECIAL_ORDER = 'Y'
        INNER JOIN
    UNIT_DETAIL ud ON ud.UNIT_ID = ro.REQUEST_UNIT_ID
        INNER JOIN
    UNIT_DETAIL ud1 ON ud1.UNIT_ID = ro.FULFILLMENT_UNIT_ID
        INNER JOIN
    VENDOR_DETAIL vd ON roi.VENDOR_ID = vd.VENDOR_ID
        INNER JOIN
    PRODUCT_DEFINITION pd ON roi.Product_ID = pd.Product_ID
WHERE
    pd.Category_ID = '1'
        AND pd.SUB_CATEGORY_ID = '3'
	AND ro.REQUEST_ORDER_STATUS <> 'CANCELLED'
GROUP BY ud.UNIT_NAME
ORDER BY REQUESTED_QUANTITY) a        
]]>
					</content>
				</report>
			</reports>
		</category>
		<category name="SCM Report: Less Quantity Received" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>"
                        schedule="">
			<reports>
				<report id="1" name="SCM Report: Less Quantity Received" executionType="SQL"
                                        GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT * FROM 
(SELECT 
    g.GOODS_RECEIVED_ID,
    gi.REQUEST_ORDER_ITEM_ID,
    u.UNIT_NAME AS TRANSFERRING_UNIT,
    ux.UNIT_NAME AS REQUESTING_RECEIVING_UNIT,
    p.PRODUCT_NAME,
    gi.SKU_NAME,
    ri.REQUESTED_QUANTITY,
    gi.TRANSFERRED_QUANTITY,
    gi.RECEIVED_QUANTITY,
    (gi.TRANSFERRED_QUANTITY - gi.RECEIVED_QUANTITY) AS Less_quantity,
    ((gi.TRANSFERRED_QUANTITY - gi.RECEIVED_QUANTITY) * s.NEGOTIATED_UNIT_PRICE) AS COST,
    gi.UNIT_OF_MEASURE
FROM
    KETTLE_SCM.GOODS_RECEIVED_ITEM gi
        INNER JOIN
    KETTLE_SCM.GOODS_RECEIVED g ON gi.GOODS_RECEIVED_ID = g.GOODS_RECEIVED_ID
        INNER JOIN
    KETTLE_SCM.SKU_DEFINITION s ON s.SKU_ID = gi.SKU_ID
        INNER JOIN
    KETTLE_SCM.PRODUCT_DEFINITION p ON p.PRODUCT_ID = s.LINKED_PRODUCT_ID
        INNER JOIN
    KETTLE_SCM.REQUEST_ORDER_ITEM ri ON ri.REQUEST_ORDER_ITEM_ID = gi.REQUEST_ORDER_ITEM_ID
        INNER JOIN
    KETTLE_SCM.UNIT_DETAIL u ON u.UNIT_ID = g.GENERATION_UNIT_ID
        INNER JOIN
    KETTLE_SCM.UNIT_DETAIL ux ON ux.UNIT_ID = g.GENERATED_FOR_UNIT_ID
        INNER JOIN
    KETTLE_SCM.CATEGORY_DEFINITION c ON c.CATEGORY_ID = p.CATEGORY_ID
        INNER JOIN
    KETTLE_SCM.SUB_CATEGORY_DEFINITION sc ON sc.SUB_CATEGORY_ID = p.SUB_CATEGORY_ID
        INNER JOIN
    KETTLE_SCM.REQUEST_ORDER r ON r.REQUEST_ORDER_ID = g.REQUEST_ORDER_ID
        INNER JOIN
    KETTLE_SCM.TRANSFER_ORDER t ON t.TRANSFER_ORDER_ID = g.TRANSFER_ORDER_ID
        LEFT JOIN
    KETTLE_SCM.VENDOR_DETAIL vd ON vd.VENDOR_ID = ri.VENDOR_ID
WHERE
    g.GOODS_RECEIVED_STATUS = 'SETTLED'
        AND gi.TRANSFERRED_QUANTITY <> gi.RECEIVED_QUANTITY
        AND g.LAST_UPDATE_TIME > SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')), 1) ) T;
        ]]>
					</content>
				</report>
			</reports>
		</category>
	</categories>
</ReportCategories>


