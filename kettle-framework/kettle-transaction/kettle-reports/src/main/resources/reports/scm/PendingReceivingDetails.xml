<ReportCategories xmlns="http://www.w3schools.com">
	<categories>
<category name="Payment Request Details For Finance" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>"
                        schedule="" attachmentType="EXCEL" >
			<reports>
				<report id="15" name="Payment Request Status Wise Count" executionType="SQL">
					<content>
						<![CDATA[
Select 
CURRENT_STATUS AS STATUS,
c.COMPANY_NAME,
COUNT(DISTINCT PAYMENT_REQUEST_ID) AS COUNT_OF_PR
FROM KETTLE_SCM.PAYMENT_REQUEST p 
INNER JOIN KETTLE_MASTER.COMPANY_DETAIL c ON p.COMPANY_ID = c.COMPANY_ID
GROUP BY CURRENT_STATUS
						 ]]>
					</content>
				</report>
				<report id="15" name="Payment Status TAT" executionType="SQL">
					<content>
						<![CDATA[
SELECT 
    round(avg(datediff( pr1.UPDATE_TIME,pr.CREATION_TIME)),2) AS TAT_ACKNOWLEDGE_IN_DAYS,
    round(avg(datediff( pr2.UPDATE_TIME,pr1.UPDATE_TIME)),2) AS TAT_APPROVED_IN_DAYS,
    round(avg(datediff( pr3.UPDATE_TIME,pr2.UPDATE_TIME)),2) AS TAT_SENT_FOR_PAYMENT_IN_DAYS,
    round(avg(datediff(pr4.UPDATE_TIME,pr3.UPDATE_TIME)),2) AS TAT_PAID_IN_DAYS
    FROM
    KETTLE_SCM.PAYMENT_REQUEST_STATUS_LOG pr1
    LEFT JOIN 
    KETTLE_SCM.PAYMENT_REQUEST_STATUS_LOG pr2 ON pr1.PAYMENT_REQUEST_ID = pr2.PAYMENT_REQUEST_ID AND pr1.TO_STATUS = pr2.FROM_STATUS
    LEFT JOIN 
    KETTLE_SCM.PAYMENT_REQUEST_STATUS_LOG pr3 ON pr2.PAYMENT_REQUEST_ID = pr3.PAYMENT_REQUEST_ID AND pr2.TO_STATUS = pr3.FROM_STATUS
    LEFT JOIN
    KETTLE_SCM.PAYMENT_REQUEST_STATUS_LOG pr4 ON pr3.PAYMENT_REQUEST_ID = pr4.PAYMENT_REQUEST_ID AND pr3.TO_STATUS = pr4.FROM_STATUS
    LEFT JOIN
    KETTLE_SCM.PAYMENT_REQUEST_STATUS_LOG pr5 ON pr4.PAYMENT_REQUEST_ID = pr5.PAYMENT_REQUEST_ID AND pr4.TO_STATUS = pr5.FROM_STATUS
    LEFT JOIN
    KETTLE_SCM.PAYMENT_REQUEST_STATUS_LOG pr6 ON pr5.PAYMENT_REQUEST_ID = pr6.PAYMENT_REQUEST_ID AND pr5.TO_STATUS = pr6.FROM_STATUS
    LEFT JOIN
    KETTLE_SCM.PAYMENT_REQUEST pr ON pr.PAYMENT_REQUEST_ID = pr1.PAYMENT_REQUEST_ID
    WHERE pr1.FROM_STATUS = 'CREATED'
    AND pr2.TO_STATUS <> 'CANCELLED'
ORDER BY pr1.PAYMENT_REQUEST_ID
						 ]]>
					</content>
				</report>

				

<report id="16" name="Payment Request Details" executionType="SQL" skipInline="true">
					<content>
						<![CDATA[

SELECT pr.PAYMENT_REQUEST_ID,
pr.VENDOR_ID,
vd.ENTITY_NAME,
pr.CREATION_TIME,
pr.CURRENT_STATUS,
pr.PROPOSED_AMOUNT,
pr.IS_BLOCKED,
pr.REQUESTING_UNIT,
ud.UNIT_NAME,
pr.GR_DOC_TYPE,
pr.INVOICE_NUMBER,
pr.PAYMENT_CYCLE
from KETTLE_SCM.PAYMENT_REQUEST pr INNER JOIN KETTLE_SCM.VENDOR_DETAIL_DATA vd ON pr.VENDOR_ID = vd.VENDOR_ID
INNER JOIN KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID=pr.REQUESTING_UNIT
WHERE pr.CURRENT_STATUS IN ('CREATED','ACKNOWLEDGED','APPROVED','SENT_FOR_PAYMENT')
ORDER BY pr.CURRENT_STATUS
						 ]]>
					</content>
				</report>



			</reports>
		</category>

		<category name="SCM Report: Pending Receiving Details (Diary/bakery)" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>, <EMAIL>"
                        schedule="">
			<reports>
				<report id="1" name="Pending Receiving Details (Diary/bakery)" executionType="SQL"
                                        GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT 
    ud.UNIT_NAME,
    DATE(ro.FULFILLMENT_DATE) AS FULFILLMENT_DATE,
    ro.GENERATION_TIME,
    ro.GENERATED_BY,
    FLOOR(SUM(roi.REQUESTED_ABSOLUTE_QUANTITY)) AS REQUESTED_QUANTITY,
    FLOOR(SUM(roi.TRANSFERRED_QUANTITY)) AS TRANSFERRED_QUANTITY,
    FLOOR(SUM(roi.RECEIVED_QUANTITY)) AS RECEIVED_QUANTITY,
    vd.ENTITY_NAME,
    cd.SUB_CATEGORY_NAME
FROM
    REQUEST_ORDER ro
        INNER JOIN
    REQUEST_ORDER_ITEM roi ON ro.REQUEST_ORDER_ID = roi.REQUEST_ORDER_ID
        AND DATE(ro.FULFILLMENT_DATE) = DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00'))
        AND ro.IS_SPECIAL_ORDER = 'Y'
        INNER JOIN
    UNIT_DETAIL ud ON ud.UNIT_ID = ro.REQUEST_UNIT_ID
        INNER JOIN
    UNIT_DETAIL ud1 ON ud1.UNIT_ID = ro.FULFILLMENT_UNIT_ID
        INNER JOIN
    VENDOR_DETAIL_DATA vd ON roi.VENDOR_ID = vd.VENDOR_ID
        INNER JOIN
    PRODUCT_DEFINITION pd ON roi.Product_ID = pd.Product_ID
        INNER JOIN
    SUB_CATEGORY_DEFINITION cd ON cd.SUB_CATEGORY_ID = pd.SUB_CATEGORY_ID
WHERE
    pd.Category_ID = '1'
        AND (pd.SUB_CATEGORY_ID = '1' OR pd.SUB_CATEGORY_ID = '3')
        AND RECEIVED_QUANTITY = '0'
	AND ro.REQUEST_ORDER_STATUS NOT IN ( 'CANCELLED', 'SETTLED' )
GROUP BY ud.UNIT_NAME
ORDER BY RECEIVED_QUANTITY;
]]>
					</content>
				</report>
			</reports>
		</category>
		<category name="SCM Report: Milk Details" type="Automated"
                        accessCode="Automated" id="1" fromEmail="<EMAIL>"
                        toEmails="<EMAIL>, <EMAIL>, <EMAIL>"
                        schedule="">
			<reports>
				<report id="1" name="Milk Received Quantity" executionType="SQL"
                                        GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[
SELECT
   ud.UNIT_NAME,
   DATE(ro.FULFILLMENT_DATE) AS FULFILLMENT_DATE,
  pd.PRODUCT_NAME,
   FLOOR(SUM(roi.REQUESTED_ABSOLUTE_QUANTITY)) AS REQUESTED_QUANTITY,
   FLOOR(SUM(roi.RECEIVED_QUANTITY)) AS RECEIVED_QUANTITY,
   vd.ENTITY_NAME
FROM
   KETTLE_SCM.REQUEST_ORDER ro
       INNER JOIN
   KETTLE_SCM.REQUEST_ORDER_ITEM roi ON ro.REQUEST_ORDER_ID = roi.REQUEST_ORDER_ID
       AND DATE(ro.FULFILLMENT_DATE) = DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00'))
       AND ro.IS_SPECIAL_ORDER = 'Y'
       INNER JOIN
   KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = ro.REQUEST_UNIT_ID
       INNER JOIN
   KETTLE_SCM.VENDOR_DETAIL_DATA vd ON roi.VENDOR_ID = vd.VENDOR_ID
       INNER JOIN
   KETTLE_SCM.PRODUCT_DEFINITION pd ON roi.Product_ID = pd.Product_ID
       INNER JOIN
   KETTLE_SCM.SUB_CATEGORY_DEFINITION cd ON cd.SUB_CATEGORY_ID = pd.SUB_CATEGORY_ID
WHERE
   pd.Category_ID = '1'
       AND pd.SUB_CATEGORY_ID = '3'
    AND ro.REQUEST_ORDER_STATUS <> "CANCELLED"
GROUP BY ud.UNIT_NAME
ORDER BY RECEIVED_QUANTITY
						]]>
					</content>
				</report>
				<report id="1" name="Milk Request Quantity" executionType="SQL"
                                        GreturnType="com.amazonaws.util.json.JSONArray">
					<content>
						<![CDATA[

SELECT
   ud.UNIT_NAME,
   DATE(ro.FULFILLMENT_DATE) AS FULFILLMENT_DATE,
   pd.PRODUCT_NAME,
   FLOOR(SUM(roi.REQUESTED_ABSOLUTE_QUANTITY)) AS REQUESTED_QUANTITY,
   FLOOR(SUM(roi.RECEIVED_QUANTITY)) AS RECEIVED_QUANTITY,
   vd.ENTITY_NAME
FROM
   KETTLE_SCM.REQUEST_ORDER ro
       INNER JOIN
   KETTLE_SCM.REQUEST_ORDER_ITEM roi ON ro.REQUEST_ORDER_ID = roi.REQUEST_ORDER_ID
       AND DATE(ro.FULFILLMENT_DATE) = SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')),-1)
       AND ro.IS_SPECIAL_ORDER = 'Y'
       INNER JOIN
   KETTLE_MASTER.UNIT_DETAIL ud ON ud.UNIT_ID = ro.REQUEST_UNIT_ID
       INNER JOIN
   KETTLE_SCM.VENDOR_DETAIL_DATA vd ON roi.VENDOR_ID = vd.VENDOR_ID
       INNER JOIN
   KETTLE_SCM.PRODUCT_DEFINITION pd ON roi.Product_ID = pd.Product_ID
       INNER JOIN
   KETTLE_SCM.SUB_CATEGORY_DEFINITION cd ON cd.SUB_CATEGORY_ID = pd.SUB_CATEGORY_ID
WHERE
   pd.Category_ID = '1'
       AND pd.SUB_CATEGORY_ID = '3'
    AND ro.REQUEST_ORDER_STATUS <> "CANCELLED"
GROUP BY ud.UNIT_NAME
ORDER BY RECEIVED_QUANTITY
						]]>
					</content>
				</report>
			</reports>
		</category>
	</categories>
</ReportCategories>


