<ReportCategories xmlns="http://www.w3schools.com">
    <categories>
        <category name="SCM Report: Wastage Details - Kitchen and Warehouse" type="Automated"
                  accessCode="Automated" id="1" fromEmail="<EMAIL>"
                  attachmentType="EXCEL"
                  toEmails="<EMAIL>"
                  schedule="">
            <reports>
                <report id="7" name="Internal Consumption Wastage - Warehouse" executionType="SQL" attachmentType="EXCEL">
                    <content>
                        <![CDATA[
SELECT * FROM ( SELECT
    C.UNIT_NAME,
    COALESCE(A.TODAY_COST, 0) TODAY_COST,
    COALESCE(B.MTD_COST, 0) MTD_COST,
    C.TARGET
FROM
    (SELECT
        UNIT_NAME,
            CASE
                WHEN UNIT_ID = 22001 THEN '18000-24000'
                WHEN UNIT_ID = 22002 THEN '10000-12000'
            END AS TARGET
    FROM
        KETTLE_MASTER_DEV.UNIT_DETAIL
    WHERE
        UNIT_ID IN (22001 , 22002)) C
        LEFT JOIN
    (SELECT
        ud.UNIT_NAME, ROUND(SUM(wid.COST)) AS TODAY_COST
    FROM
        KETTLE_SCM_DEV.WASTAGE_ITEM_DATA wid
    INNER JOIN KETTLE_SCM_DEV.WASTAGE_EVENT we ON wid.WASTAGE_ID = we.WASTAGE_ID
    INNER JOIN KETTLE_MASTER_DEV.UNIT_DETAIL ud ON we.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE_SCM_DEV.COMMENT_TYPE_MAPPING ctm ON ctm.COMMENT_VALUE = wid.COMMENT
        AND we.STATUS = 'SETTLED'
        AND we.BUSINESS_DATE = SUBDATE(CURRENT_DATE, 1)
        AND ud.UNIT_CATEGORY IN ('WAREHOUSE')
        AND wid.COMMENT = 'INTERNAL CONSUMPTION'
    GROUP BY ud.UNIT_NAME) A ON A.UNIT_NAME = C.UNIT_NAME
        LEFT JOIN
    (SELECT
        ud.UNIT_NAME, ROUND(SUM(wid.COST)) AS MTD_COST
    FROM
        KETTLE_SCM_DEV.WASTAGE_ITEM_DATA wid
    INNER JOIN KETTLE_SCM_DEV.WASTAGE_EVENT we ON wid.WASTAGE_ID = we.WASTAGE_ID
    INNER JOIN KETTLE_MASTER_DEV.UNIT_DETAIL ud ON we.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE_SCM_DEV.COMMENT_TYPE_MAPPING ctm ON ctm.COMMENT_VALUE = wid.COMMENT
        AND we.STATUS = 'SETTLED'
        AND we.BUSINESS_DATE BETWEEN DATE_ADD(DATE(DATE_SUB(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL DAYOFMONTH(ADDTIME(UTC_TIMESTAMP, '05:30:00')) - 1 DAY)), INTERVAL 5 HOUR) AND SUBDATE(CURRENT_DATE, 1)
        AND ud.UNIT_CATEGORY IN ('WAREHOUSE')
        AND wid.COMMENT = 'INTERNAL CONSUMPTION'
    GROUP BY ud.UNIT_NAME) B ON B.UNIT_NAME = C.UNIT_NAME) A



						 ]]>
                    </content>
                </report>
                <report id="8" name="Internal Consumption and Variance Kitchen" executionType="SQL" attachmentType="EXCEL">
                    <content>
                        <![CDATA[
						SELECT * FROM (
SELECT
    C.UNIT_NAME,
    COALESCE(D.TODAY_COST, 0) TODAY_COST,
    COALESCE(E.MTD_COST, 0) MTD_COST,
    COALESCE(B.DAILY_VARIANCE_COST, 0) TODAY_VARIANCE,
    COALESCE(A.VARIANCE_COST_MTD, 0) MTD_VARIANCE,
    COALESCE((D.TODAY_COST + B.DAILY_VARIANCE_COST),
            0) AS 'VAR+IC_DAILY',
    COALESCE((E.MTD_COST + A.VARIANCE_COST_MTD), 0) AS 'VAR+IC_MTD',
    C.TARGET
FROM
    (SELECT
        UNIT_NAME,
            CASE
                WHEN UNIT_ID = 24001 THEN '100000-1250000'
                WHEN UNIT_ID = 24002 THEN '50000-60000'
            END AS 'TARGET'
    FROM
        KETTLE_MASTER_DEV.UNIT_DETAIL
    WHERE
        UNIT_ID IN (24001 , 24002)) C
        LEFT JOIN
    (SELECT
        ud.UNIT_NAME, ROUND(SUM(wid.COST)) AS TODAY_COST
    FROM
        KETTLE_SCM_DEV.WASTAGE_ITEM_DATA wid
    INNER JOIN KETTLE_SCM_DEV.WASTAGE_EVENT we ON wid.WASTAGE_ID = we.WASTAGE_ID
    INNER JOIN KETTLE_MASTER_DEV.UNIT_DETAIL ud ON we.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE_SCM_DEV.COMMENT_TYPE_MAPPING ctm ON ctm.COMMENT_VALUE = wid.COMMENT
        AND we.STATUS = 'SETTLED'
        AND we.BUSINESS_DATE = SUBDATE(CURRENT_DATE, 1)
        AND ud.UNIT_CATEGORY IN ('KITCHEN')
        AND wid.COMMENT = 'INTERNAL CONSUMPTION'
    GROUP BY ud.UNIT_NAME) D ON D.UNIT_NAME = C.UNIT_NAME
        LEFT JOIN
    (SELECT
        ud.UNIT_NAME, ROUND(SUM(wid.COST)) AS MTD_COST
    FROM
        KETTLE_SCM_DEV.WASTAGE_ITEM_DATA wid
    INNER JOIN KETTLE_SCM_DEV.WASTAGE_EVENT we ON wid.WASTAGE_ID = we.WASTAGE_ID
    INNER JOIN KETTLE_MASTER_DEV.UNIT_DETAIL ud ON we.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE_SCM_DEV.COMMENT_TYPE_MAPPING ctm ON ctm.COMMENT_VALUE = wid.COMMENT
        AND we.STATUS = 'SETTLED'
        AND we.BUSINESS_DATE BETWEEN DATE_ADD(DATE(DATE_SUB(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL DAYOFMONTH(ADDTIME(UTC_TIMESTAMP, '05:30:00')) - 1 DAY)), INTERVAL 5 HOUR) AND SUBDATE(CURRENT_DATE, 1)
        AND ud.UNIT_CATEGORY IN ('KITCHEN')
        AND wid.COMMENT = 'INTERNAL CONSUMPTION'
    GROUP BY ud.UNIT_NAME) E ON E.UNIT_NAME = C.UNIT_NAME
        LEFT JOIN
    (SELECT
        ud.UNIT_ID,
            ud.UNIT_NAME,
            ROUND(ABS(SUM(inv.VARIANCE_COST)), 2) VARIANCE_COST_MTD,
            CASE
                WHEN ud.UNIT_ID = 24001 THEN '15000-20000'
                WHEN ud.UNIT_ID = 24002 THEN '7500-12500'
            END AS TARGET
    FROM
        KETTLE_SCM_DEV.DAY_CLOSE_EVENT dce
    INNER JOIN KETTLE_SCM_DEV.INVENTORY_DRILLDOWN inv ON dce.EVENT_ID = inv.CLOSURE_EVENT_ID
    INNER JOIN KETTLE_SCM_DEV.UNIT_DETAIL ud ON ud.UNIT_ID = dce.UNIT_ID
    INNER JOIN KETTLE_SCM_DEV.SKU_DEFINITION sd ON inv.SKU_ID = sd.SKU_ID
    WHERE
        DATE(dce.BUSINESS_DATE) BETWEEN DATE_ADD(DATE(DATE_SUB(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL DAYOFMONTH(ADDTIME(UTC_TIMESTAMP, '05:30:00')) - 1 DAY)), INTERVAL 5 HOUR) AND SUBDATE(CURRENT_DATE, 2)
            AND dce.STATUS <> 'CANCELLED'
            AND ud.UNIT_ID IN (24001 , 24002)
            AND sd.SKU_STATUS = 'ACTIVE'
            AND sd.INVENTORY_LIST_ID <> '1'
            AND sd.SKU_ID IN (1142 , 1323, 1220, 1016, 1010, 1011, 30, 7, 1066, 1134, 130, 91, 1171, 48, 178, 174, 1674, 264, 269, 123, 320, 1012, 361, 139, 1009, 1048, 1076, 150, 151, 302, 1109, 1335, 386, 23, 363, 1073, 1334, 136, 1014, 389, 548, 13, 1074, 147, 1333, 392, 265, 47, 252, 29, 1356)
    GROUP BY ud.UNIT_ID) A ON A.UNIT_NAME = C.UNIT_NAME
        LEFT JOIN
    (SELECT
        ud.UNIT_ID,
            ud.UNIT_NAME,
            DATE(dce.GENERATION_TIME) AS GENERATION_DATE,
            ROUND(ABS(SUM((inv.VARIANCE_COST))), 2) DAILY_VARIANCE_COST
    FROM
        KETTLE_SCM_DEV.DAY_CLOSE_EVENT dce
    INNER JOIN KETTLE_SCM_DEV.INVENTORY_DRILLDOWN inv ON dce.EVENT_ID = inv.CLOSURE_EVENT_ID
    INNER JOIN KETTLE_SCM_DEV.UNIT_DETAIL ud ON ud.UNIT_ID = dce.UNIT_ID
    INNER JOIN KETTLE_SCM_DEV.SKU_DEFINITION sd ON inv.SKU_ID = sd.SKU_ID
    WHERE
        DATE(dce.BUSINESS_DATE) = SUBDATE(CURRENT_DATE, 2)
            AND dce.STATUS <> 'CANCELLED'
            AND ud.UNIT_ID IN (24001 , 24002)
            AND sd.SKU_STATUS = 'ACTIVE'
            AND sd.INVENTORY_LIST_ID <> '1'
            AND sd.SKU_ID IN (1142 , 1323, 1220, 1016, 1010, 1011, 30, 7, 1066, 1134, 130, 91, 1171, 48, 178, 174, 1674, 264, 269, 123, 320, 1012, 361, 139, 1009, 1048, 1076, 150, 151, 302, 1109, 1335, 386, 23, 363, 1073, 1334, 136, 1014, 389, 548, 13, 1074, 147, 1333, 392, 265, 47, 252, 29, 1356)
    GROUP BY ud.UNIT_ID) B ON A.UNIT_ID = B.UNIT_ID ) A
						 ]]>
                    </content>
                </report>

                <report id="8" name="Other Wastage Cost" executionType="SQL" attachmentType="EXCEL">
                    <content>
                        <![CDATA[

SELECT
    C.UNIT_NAME,
    COALESCE(A.TODAY_COST, 0) TODAY_COST,
    COALESCE(B.MTD_COST, 0) MTD_COST,
    C.TARGET
FROM
    (SELECT
        UNIT_NAME,
            CASE
                WHEN UNIT_ID = 24001 THEN '12000-15000'
                WHEN UNIT_ID = 24002 THEN '22000-25000'
                WHEN UNIT_ID = 22001 THEN '12000-15000'
                WHEN UNIT_ID = 22002 THEN '22000-25000'
            END AS TARGET
    FROM
        KETTLE_MASTER_DEV.UNIT_DETAIL
    WHERE
        UNIT_ID IN (24001 , 24002, 22001, 22002)) C
        LEFT JOIN
    (SELECT
        ud.UNIT_NAME, ROUND(SUM(wid.COST)) AS TODAY_COST
    FROM
        KETTLE_SCM_DEV.WASTAGE_ITEM_DATA wid
    INNER JOIN KETTLE_SCM_DEV.WASTAGE_EVENT we ON wid.WASTAGE_ID = we.WASTAGE_ID
    INNER JOIN KETTLE_MASTER_DEV.UNIT_DETAIL ud ON we.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE_SCM_DEV.COMMENT_TYPE_MAPPING ctm ON ctm.COMMENT_VALUE = wid.COMMENT
        AND we.STATUS = 'SETTLED'
        AND we.BUSINESS_DATE = SUBDATE(CURRENT_DATE, 1)
        AND ud.UNIT_CATEGORY IN ('KITCHEN' , 'WAREHOUSE')
        AND wid.COMMENT <> 'INTERNAL CONSUMPTION'
    GROUP BY ud.UNIT_NAME) A ON A.UNIT_NAME = C.UNIT_NAME
        LEFT JOIN
    (SELECT
        ud.UNIT_NAME, ROUND(SUM(wid.COST)) AS MTD_COST
    FROM
        KETTLE_SCM_DEV.WASTAGE_ITEM_DATA wid
    INNER JOIN KETTLE_SCM_DEV.WASTAGE_EVENT we ON wid.WASTAGE_ID = we.WASTAGE_ID
    INNER JOIN KETTLE_MASTER_DEV.UNIT_DETAIL ud ON we.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE_SCM_DEV.COMMENT_TYPE_MAPPING ctm ON ctm.COMMENT_VALUE = wid.COMMENT
        AND we.STATUS = 'SETTLED'
        AND we.BUSINESS_DATE BETWEEN DATE_ADD(DATE(DATE_SUB(ADDTIME(UTC_TIMESTAMP, '05:30:00'), INTERVAL DAYOFMONTH(ADDTIME(UTC_TIMESTAMP, '05:30:00')) - 1 DAY)), INTERVAL 5 HOUR) AND SUBDATE(CURRENT_DATE, 1)
        AND ud.UNIT_CATEGORY IN ('KITCHEN' , 'WAREHOUSE')
        AND wid.COMMENT <>'INTERNAL CONSUMPTION'
    GROUP BY ud.UNIT_NAME) B ON B.UNIT_NAME = C.UNIT_NAME;



						 ]]>
                    </content>
                </report>

                <report id="8" name="Wastage Details" executionType="SQL" attachmentType="EXCEL">
                    <content>
                        <![CDATA[
SELECT
    *
FROM
    (SELECT
        wid.PRODUCT_ID,
            pd.PRODUCT_NAME,
            cd.CATEGORY_NAME,
            scd.SUB_CATEGORY_NAME,
            ud.UNIT_NAME,
            utm.UNIT_TYPE,
            ld.CITY,
            wid.PRICE,
            wid.QUANTITY,
            wid.COST AS COST,
            pd.UNIT_OF_MEASURE,
            we.BUSINESS_DATE,
            we.GENERATION_TIME,
            we.GENERATED_BY,
            wid.COMMENT,
            CASE
        	WHEN ctm.COMMENT_TYPE IS NOT NULL THEN ctm.COMMENT_TYPE
        	ELSE (CASE
			WHEN we.LINKED_GR_ID IS NOT NULL THEN 'Lost Inventory'
			ELSE 'Cafe Wastage'
		     END)
	    END AS COMMENT_TYPE
    FROM
        KETTLE_SCM_DEV.WASTAGE_ITEM_DATA wid
    INNER JOIN KETTLE_SCM_DEV.WASTAGE_EVENT we ON wid.WASTAGE_ID = we.WASTAGE_ID
    INNER JOIN KETTLE_SCM_DEV.PRODUCT_DEFINITION pd ON wid.PRODUCT_ID = pd.PRODUCT_ID
    INNER JOIN KETTLE_SCM_DEV.CATEGORY_DEFINITION cd ON pd.CATEGORY_ID = cd.CATEGORY_ID
    INNER JOIN KETTLE_SCM_DEV.SUB_CATEGORY_DEFINITION scd ON pd.SUB_CATEGORY_ID = scd.SUB_CATEGORY_ID
    INNER JOIN KETTLE_MASTER_DEV.UNIT_DETAIL ud ON we.UNIT_ID = ud.UNIT_ID
    INNER JOIN KETTLE_SCM_DEV.UNIT_TYPE_MAPPING utm ON ud.UNIT_CATEGORY = utm.UNIT_CATEGORY
    INNER JOIN KETTLE_MASTER_DEV.LOCATION_DETAIL ld ON ud.LOCATION_DETAIL_ID = ld.LOCATION_ID
    INNER JOIN KETTLE_SCM_DEV.COMMENT_TYPE_MAPPING ctm ON ctm.COMMENT_VALUE = wid.COMMENT
        AND we.STATUS = 'SETTLED'
        AND we.BUSINESS_DATE = SUBDATE(CURRENT_DATE, 1)
        AND ud.UNIT_CATEGORY IN ('KITCHEN' , 'WAREHOUSE')) a
						 ]]>
                    </content>
                </report>
            </reports>
        </category>
    </categories>
</ReportCategories>