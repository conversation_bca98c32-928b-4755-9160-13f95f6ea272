<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<ReportCategories xmlns="http://www.w3schools.com">
	<categories>
		<category name="Daily SCM Report" type="Automated" accessCode="Automated" id="1" fromEmail="<EMAIL>" toEmails="<EMAIL>,<EMAIL>" schedule="">
			<reports>
				<report id="1" name="Milk GR Report " executionType="SQL"
                                        returnType="com.amazonaws.util.json.JSONArray" >
					<content>
						<![CDATA[
SELECT 
    gri.SKU_ID,
    gri.SKU_NAME,
    SUM(COALESCE(gri.TRANSFERRED_QUANTITY, 0)) TOTAL_TRANSFERRED_QUANTITY,
    SUM(COALESCE(gri.RECEIVED_QUANTITY, 0)) TOTAL_RECEIVED_QUANTITY,
    gri.UNIT_OF_MEASURE,
    ud.UNIT_NAME
FROM
    KETTLE_SCM.GOODS_RECEIVED gr
        LEFT JOIN
    KETTLE_SCM.GOODS_RECEIVED_ITEM gri ON gri.GOODS_RECEIVED_ID = gr.GOODS_RECEIVED_ID
        LEFT JOIN
    KETTLE_SCM.UNIT_DETAIL ud ON gr.GENERATED_FOR_UNIT_ID = ud.UNIT_ID
WHERE
    gr.LAST_UPDATE_TIME BETWEEN DATE_ADD(SUBDATE(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')),
            1),
        INTERVAL 5 HOUR) AND DATE_ADD(DATE(ADDTIME(UTC_TIMESTAMP, '05:30:00')),
        INTERVAL 5 HOUR)
        AND gr.GOODS_RECEIVED_STATUS = 'SETTLED'
        AND gri.SKU_ID IN (SELECT 
            sd.SKU_ID
        FROM
            KETTLE_SCM.SKU_DEFINITION sd
        WHERE
            sd.LINKED_PRODUCT_ID = 100234)
GROUP BY gr.GENERATED_FOR_UNIT_ID , gri.SKU_ID
]]>
					</content>
				</report>
			</reports>
		</category>
	</categories>
</ReportCategories>

