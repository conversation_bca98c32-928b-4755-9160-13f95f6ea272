<ReportCategories xmlns="http://www.w3schools.com">
    <categories>
        <category name="SCM Report: Fullfilment Percentage - Kitchen and Warehouse" type="Automated"
                  accessCode="Automated" id="1" fromEmail="<EMAIL>"
                  attachmentType="EXCEL"
                  toEmails="<EMAIL>"
                  schedule="">
            <reports>
                <report id="4" name="Fullfilment Percentage - Kitchen and Warehouse" executionType="SQL" attachmentType="EXCEL">
                    <content>
                        <![CDATA[


SELECT
	   T.* FROM
	(SELECT
	   X.TRANSFERRING_UNIT,
	   CONCAT(ROUND(AVG(CASE
						   WHEN (COALESCE(X.RECEIVED_QUANTITY, 0) / X.REQUESTED_ABSOLUTE_QUANTITY) >= 1 THEN 100
						   ELSE (COALESCE(X.RECEIVED_QUANTITY, 0) / X.REQUESTED_ABSOLUTE_QUANTITY) * 100
					   END),
					   2),
			   '%') AS Percentage,
 '99.75%' AS TARGET
	FROM
	   (SELECT
		   u.UNIT_NAME AS TRANSFERRING_UNIT,
			   ux.UNIT_NAME AS REQUESTING_UNIT,
			   ro.REQUEST_ORDER_ID,
			   ro.LAST_UPDATE_TIME,
			   roi.REQUEST_ORDER_ITEM_ID,
			   roi.PRODUCT_NAME,
			   roi.UNIT_OF_MEASURE,
			   roi.REQUESTED_ABSOLUTE_QUANTITY,
			   SUM(COALESCE(t.TRANSFERRED_QUANTITY, 0)) AS TRANSFERRED_QUANTITY,
			   SUM(COALESCE(t.RECEIVED_QUANTITY, 0)) AS RECEIVED_QUANTITY
	   FROM
		   KETTLE_SCM_DEV.REQUEST_ORDER_ITEM roi
	   INNER JOIN KETTLE_SCM_DEV.REQUEST_ORDER ro ON ro.REQUEST_ORDER_ID = roi.REQUEST_ORDER_ID
	   INNER JOIN KETTLE_MASTER_DEV.UNIT_DETAIL u ON u.UNIT_ID = ro.FULFILLMENT_UNIT_ID
	   INNER JOIN KETTLE_MASTER_DEV.UNIT_DETAIL ux ON ux.UNIT_ID = ro.REQUEST_UNIT_ID
	   LEFT JOIN (SELECT
		   t.REQUEST_ORDER_ID,
			   sku.LINKED_PRODUCT_ID,
			   SUM(toi.TRANSFERRED_QUANTITY) AS TRANSFERRED_QUANTITY,
			   SUM(toi.RECEIVED_QUANTITY + COALESCE(d.TRANSFERRED, 0)) AS RECEIVED_QUANTITY
	   FROM
		   KETTLE_SCM_DEV.GOODS_RECEIVED_ITEM toi
	   INNER JOIN KETTLE_SCM_DEV.GOODS_RECEIVED t ON t.GOODS_RECEIVED_ID = toi.GOODS_RECEIVED_ID
	   LEFT JOIN (SELECT
		   gr2.PARENT_GR GOODS_RECEIVED_ID,
			   gri.SKU_ID,
			   SUM(gri.TRANSFERRED_QUANTITY) TRANSFERRED
	   FROM
		   KETTLE_SCM_DEV.GOODS_RECEIVED_ITEM gri
	   INNER JOIN KETTLE_SCM_DEV.GOODS_RECEIVED gr ON gr.GOODS_RECEIVED_ID = gri.GOODS_RECEIVED_ID
	   INNER JOIN KETTLE_SCM_DEV.GOODS_RECEIVED gr2 ON gr2.GOODS_RECEIVED_ID = gr.PARENT_GR
	   WHERE
		   gr.PARENT_GR IS NOT NULL
			   AND gr.GOODS_RECEIVED_STATUS = 'SETTLED'
			   AND gr.GENERATION_UNIT_ID IN (22001 , 22002, 24001, 24002)
	   GROUP BY gr.PARENT_GR , gri.SKU_ID) AS d ON d.GOODS_RECEIVED_ID = t.GOODS_RECEIVED_ID
		   AND toi.SKU_ID = d.SKU_ID
	   INNER JOIN KETTLE_SCM_DEV.SKU_DEFINITION sku ON sku.SKU_ID = toi.SKU_ID
	   WHERE
		   DATE(t.LAST_UPDATE_TIME) = SUBDATE(CURRENT_DATE,1)
			   AND t.REQUEST_ORDER_ID IS NOT NULL
			   AND t.GENERATION_UNIT_ID <> t.GENERATED_FOR_UNIT_ID
			   AND t.GOODS_RECEIVED_STATUS = 'SETTLED'
	   GROUP BY t.REQUEST_ORDER_ID , sku.LINKED_PRODUCT_ID) AS t ON t.REQUEST_ORDER_ID = roi.REQUEST_ORDER_ID
		   AND t.LINKED_PRODUCT_ID = roi.PRODUCT_ID
	   WHERE
		   u.UNIT_CATEGORY IN ('KITCHEN' , 'WAREHOUSE')
			   AND DATE(ro.LAST_UPDATE_TIME) = SUBDATE(CURRENT_DATE,1)
			   AND ro.REQUEST_ORDER_STATUS = 'SETTLED'
	   GROUP BY ro.REQUEST_ORDER_ID , roi.REQUEST_ORDER_ITEM_ID) X
	GROUP BY X.TRANSFERRING_UNIT) as T


						 ]]>
                    </content>
                </report>
                <report id="5" name="Fullfilment Percentage Last 30 Days - Kitchen or Warehouse" executionType="SQL" attachmentType="EXCEL">
                    <content>
                        <![CDATA[



SELECT
	   T.* FROM
	(SELECT
	   X.TRANSFERRING_UNIT,
	   CONCAT(ROUND(AVG(CASE
						   WHEN (COALESCE(X.RECEIVED_QUANTITY, 0) / X.REQUESTED_ABSOLUTE_QUANTITY) >= 1 THEN 100
						   ELSE (COALESCE(X.RECEIVED_QUANTITY, 0) / X.REQUESTED_ABSOLUTE_QUANTITY) * 100
					   END),
					   2),
			   '%') AS Percentage,
'99.75%' AS TARGET
	FROM
	   (SELECT
		   u.UNIT_NAME AS TRANSFERRING_UNIT,
			   ux.UNIT_NAME AS REQUESTING_UNIT,
			   ro.REQUEST_ORDER_ID,
			   ro.LAST_UPDATE_TIME,
			   roi.REQUEST_ORDER_ITEM_ID,
			   roi.PRODUCT_NAME,
			   roi.UNIT_OF_MEASURE,
			   roi.REQUESTED_ABSOLUTE_QUANTITY,
			   SUM(COALESCE(t.TRANSFERRED_QUANTITY, 0)) AS TRANSFERRED_QUANTITY,
			   SUM(COALESCE(t.RECEIVED_QUANTITY, 0)) AS RECEIVED_QUANTITY
	   FROM
		   KETTLE_SCM_DEV.REQUEST_ORDER_ITEM roi
	   INNER JOIN KETTLE_SCM_DEV.REQUEST_ORDER ro ON ro.REQUEST_ORDER_ID = roi.REQUEST_ORDER_ID
	   INNER JOIN KETTLE_MASTER_DEV.UNIT_DETAIL u ON u.UNIT_ID = ro.FULFILLMENT_UNIT_ID
	   INNER JOIN KETTLE_MASTER_DEV.UNIT_DETAIL ux ON ux.UNIT_ID = ro.REQUEST_UNIT_ID
	   LEFT JOIN (SELECT
		   t.REQUEST_ORDER_ID,
			   sku.LINKED_PRODUCT_ID,
			   SUM(toi.TRANSFERRED_QUANTITY) AS TRANSFERRED_QUANTITY,
			   SUM(toi.RECEIVED_QUANTITY + COALESCE(d.TRANSFERRED, 0)) AS RECEIVED_QUANTITY
	   FROM
		   KETTLE_SCM_DEV.GOODS_RECEIVED_ITEM toi
	   INNER JOIN KETTLE_SCM_DEV.GOODS_RECEIVED t ON t.GOODS_RECEIVED_ID = toi.GOODS_RECEIVED_ID
	   LEFT JOIN (SELECT
		   gr2.PARENT_GR GOODS_RECEIVED_ID,
			   gri.SKU_ID,
			   SUM(gri.TRANSFERRED_QUANTITY) TRANSFERRED
	   FROM
		   KETTLE_SCM_DEV.GOODS_RECEIVED_ITEM gri
	   INNER JOIN KETTLE_SCM_DEV.GOODS_RECEIVED gr ON gr.GOODS_RECEIVED_ID = gri.GOODS_RECEIVED_ID
	   INNER JOIN KETTLE_SCM_DEV.GOODS_RECEIVED gr2 ON gr2.GOODS_RECEIVED_ID = gr.PARENT_GR
	   WHERE
		   gr.PARENT_GR IS NOT NULL
			   AND gr.GOODS_RECEIVED_STATUS = 'SETTLED'
			   AND gr.GENERATION_UNIT_ID IN (22001 , 22002, 24001, 24002)
	   GROUP BY gr.PARENT_GR , gri.SKU_ID) AS d ON d.GOODS_RECEIVED_ID = t.GOODS_RECEIVED_ID
		   AND toi.SKU_ID = d.SKU_ID
	   INNER JOIN KETTLE_SCM_DEV.SKU_DEFINITION sku ON sku.SKU_ID = toi.SKU_ID
	   WHERE
		   DATE(t.LAST_UPDATE_TIME) between SUBDATE(CURRENT_DATE,30) and SUBDATE(CURRENT_DATE,1)
			   AND t.REQUEST_ORDER_ID IS NOT NULL
			   AND t.GENERATION_UNIT_ID <> t.GENERATED_FOR_UNIT_ID
			   AND t.GOODS_RECEIVED_STATUS = 'SETTLED'
	   GROUP BY t.REQUEST_ORDER_ID , sku.LINKED_PRODUCT_ID) AS t ON t.REQUEST_ORDER_ID = roi.REQUEST_ORDER_ID
		   AND t.LINKED_PRODUCT_ID = roi.PRODUCT_ID
	   WHERE
		   u.UNIT_CATEGORY IN ('KITCHEN' , 'WAREHOUSE')
			   AND DATE(ro.LAST_UPDATE_TIME) between SUBDATE(CURRENT_DATE,30) and SUBDATE(CURRENT_DATE,1)
			   AND ro.REQUEST_ORDER_STATUS = 'SETTLED'
	   GROUP BY ro.REQUEST_ORDER_ID , roi.REQUEST_ORDER_ITEM_ID) X
	GROUP BY X.TRANSFERRING_UNIT) as T

						]]>
                    </content>
                </report>
                <report id="6" name="Fullfilment Details - Kitchen and Warehouse" executionType="SQL" attachmentType="EXCEL">
                    <content>
                        <![CDATA[

SELECT
	   T.*
	FROM
	   (SELECT
		   u.UNIT_NAME AS TRANSFERRING_UNIT,
			   ux.UNIT_NAME AS REQUESTING_UNIT,
			   ro.REQUEST_ORDER_ID,
			   ro.LAST_UPDATE_TIME,
			   roi.REQUEST_ORDER_ITEM_ID,
			   roi.PRODUCT_NAME,
			   roi.UNIT_OF_MEASURE,
			   roi.REQUESTED_ABSOLUTE_QUANTITY,
			   SUM(COALESCE(t.TRANSFERRED_QUANTITY, 0)) AS TRANSFERRED_QUANTITY,
			   SUM(COALESCE(t.RECEIVED_QUANTITY, 0)) AS RECEIVED_QUANTITY
	   FROM
		   KETTLE_SCM_DEV.REQUEST_ORDER_ITEM roi
	   INNER JOIN KETTLE_SCM_DEV.REQUEST_ORDER ro ON ro.REQUEST_ORDER_ID = roi.REQUEST_ORDER_ID
	   INNER JOIN KETTLE_MASTER_DEV.UNIT_DETAIL u ON u.UNIT_ID = ro.FULFILLMENT_UNIT_ID
	   INNER JOIN KETTLE_MASTER_DEV.UNIT_DETAIL ux ON ux.UNIT_ID = ro.REQUEST_UNIT_ID
	   LEFT JOIN (SELECT
		   t.REQUEST_ORDER_ID,
			   sku.LINKED_PRODUCT_ID,
			   SUM(toi.TRANSFERRED_QUANTITY) AS TRANSFERRED_QUANTITY,
			   SUM(toi.RECEIVED_QUANTITY + COALESCE(d.TRANSFERRED, 0)) AS RECEIVED_QUANTITY
	   FROM
		   KETTLE_SCM_DEV.GOODS_RECEIVED_ITEM toi
	   INNER JOIN KETTLE_SCM_DEV.GOODS_RECEIVED t ON t.GOODS_RECEIVED_ID = toi.GOODS_RECEIVED_ID
	   LEFT JOIN (SELECT
		   gr2.PARENT_GR GOODS_RECEIVED_ID,
			   gri.SKU_ID,
			   SUM(gri.TRANSFERRED_QUANTITY) TRANSFERRED
	   FROM
		   KETTLE_SCM_DEV.GOODS_RECEIVED_ITEM gri
	   INNER JOIN KETTLE_SCM_DEV.GOODS_RECEIVED gr ON gr.GOODS_RECEIVED_ID = gri.GOODS_RECEIVED_ID
	   INNER JOIN KETTLE_SCM_DEV.GOODS_RECEIVED gr2 ON gr2.GOODS_RECEIVED_ID = gr.PARENT_GR
	   WHERE
		   gr.PARENT_GR IS NOT NULL
			   AND gr.GOODS_RECEIVED_STATUS = 'SETTLED'
			   AND gr.GENERATION_UNIT_ID IN (22001 , 22002, 24001, 24002)
	   GROUP BY gr.PARENT_GR , gri.SKU_ID) AS d ON d.GOODS_RECEIVED_ID = t.GOODS_RECEIVED_ID
		   AND toi.SKU_ID = d.SKU_ID
	   INNER JOIN KETTLE_SCM_DEV.SKU_DEFINITION sku ON sku.SKU_ID = toi.SKU_ID
	   WHERE
		   DATE(t.LAST_UPDATE_TIME) = SUBDATE(CURRENT_DATE,1)
			   AND t.REQUEST_ORDER_ID IS NOT NULL
			   AND t.GENERATION_UNIT_ID <> t.GENERATED_FOR_UNIT_ID
			   AND t.GOODS_RECEIVED_STATUS = 'SETTLED'
	   GROUP BY t.REQUEST_ORDER_ID , sku.LINKED_PRODUCT_ID) AS t ON t.REQUEST_ORDER_ID = roi.REQUEST_ORDER_ID
		   AND t.LINKED_PRODUCT_ID = roi.PRODUCT_ID
	   WHERE
		   u.UNIT_CATEGORY IN ('KITCHEN' , 'WAREHOUSE')
			   AND DATE(ro.LAST_UPDATE_TIME) = SUBDATE(CURRENT_DATE,1)
			   AND ro.REQUEST_ORDER_STATUS = 'SETTLED'
			  AND COALESCE(t.RECEIVED_QUANTITY, 0) < COALESCE(roi.REQUESTED_ABSOLUTE_QUANTITY, 0)
	   GROUP BY ro.REQUEST_ORDER_ID , roi.REQUEST_ORDER_ITEM_ID) T


				 ]]>
                    </content>
                </report>
            </reports>
        </category>
    </categories>
</ReportCategories>