/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.external.impl;

import java.util.List;

import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.reports.external.DataProvider;

public class SalesDataProvider<Unit> implements DataProvider<Unit, OrderInfo> {

	List<OrderInfo> orderInfos;

	public SalesDataProvider(ExternalReportInputData input) {
		orderInfos = input.getOrderInfos();
	}

	@Override
	public List<OrderInfo> fetchContent(Unit unitContext) {
		return orderInfos;
	}

}
