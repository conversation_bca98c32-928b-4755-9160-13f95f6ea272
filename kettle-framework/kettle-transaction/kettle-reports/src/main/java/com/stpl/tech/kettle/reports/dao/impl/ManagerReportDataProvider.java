/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.dao.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeMap;

import com.google.gson.Gson;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.stpl.tech.kettle.core.ServiceType;
import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.core.cache.MetadataCache;
import com.stpl.tech.kettle.customer.service.CardService;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.Settlement;
import com.stpl.tech.kettle.reports.model.ComplimentaryBillData;
import com.stpl.tech.kettle.reports.model.CreditBillData;
import com.stpl.tech.kettle.reports.model.DiscountedBillData;
import com.stpl.tech.kettle.reports.model.SettlementReport;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.offer.service.OfferManagementExternalService;
import com.stpl.tech.master.tax.model.Taxation;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;

public class ManagerReportDataProvider {

	private static final Logger LOG = LoggerFactory.getLogger(ManagerReportDataProvider.class);
	private final MasterDataCache masterCache;
	private final MetadataCache metadataCache;

	private final UnitConsumptionReportProvider unitReportProvider;
	private final SettlementReportProvider settlementReportProvider;
	private final RevenueDataProvider revenueDataProvider;
	private Map<ServiceType, SettlementReport> serviceTypes = new TreeMap<ServiceType, SettlementReport>();
	private List<DiscountedBillData> discountedOrders = new ArrayList<DiscountedBillData>();
	private List<ComplimentaryBillData> complimentaryOrders = new ArrayList<ComplimentaryBillData>();
	private List<CreditBillData> creditOrders = new ArrayList<CreditBillData>();
	private List<Order> cancelledOrders = new ArrayList<Order>();
	private List<Order> rePrintedBills = new ArrayList<Order>();

	public ManagerReportDataProvider(MasterDataCache masterCache, MetadataCache metadataCache, OfferManagementExternalService offerService,CardService cardService) {
		this.masterCache = masterCache;
		this.metadataCache = metadataCache;
		this.unitReportProvider = new UnitConsumptionReportProvider(masterCache, metadataCache);
		this.settlementReportProvider = new SettlementReportProvider(masterCache, metadataCache);
		this.revenueDataProvider = new RevenueDataProvider(masterCache, offerService, cardService);
	}

	public void process(SettlementReportInputData reportData, Set<Taxation> stateTaxes) {
		preProcess(reportData);
		for (Order order : reportData.orders) {
			settlementReportProvider.addToPaymentType(order, stateTaxes);
			addToDeliveryType(order, stateTaxes);
			addToDiscountedOrder(order);
			addToComplimentaryOrder(order);
			addToCancelledOrder(order);
			addToReprintedOrder(order);
			addToCreditOrder(order);
			unitReportProvider.addToConsumptionDetail(order);
			if (reportData.isGeneratePnL()) {
				revenueDataProvider.process(order, reportData.getCreditCardPercentage(), reportData.getPartners());
			}
		}
	}

	public Collection<SettlementReport> getByServiceType() {
		return serviceTypes.values();
	}

	public List<DiscountedBillData> getDiscountedOrders() {
		return discountedOrders;
	}

	public List<ComplimentaryBillData> getComplimentaryOrders() {
		return complimentaryOrders;
	}

	public List<Order> getCancelledOrders() {
		return cancelledOrders;
	}

	public List<CreditBillData> getCreditOrders() {
		return creditOrders;
	}

	protected void preProcess(SettlementReportInputData reportData) {
		unitReportProvider.preProcess(reportData);
		settlementReportProvider.preProcess(reportData);
		for (ServiceType type : ServiceType.values()) {
			serviceTypes.put(type, new SettlementReport(type));
		}

	}

	private void addToCancelledOrder(Order order) {
		if (ReportUtil.isCancelled(order.getStatus())) {
			cancelledOrders.add(order);
		}

	}

	private void addToReprintedOrder(Order order) {
		if (order.getPrintCount() > 1) {
			rePrintedBills.add(order);
		}

	}

	private void addToDeliveryType(Order order, Set<Taxation> stateTaxes) {
		if (TransactionUtils.isSkipOrder(order)) {
			return;
		}
		SettlementReport data = serviceTypes
				.get(TransactionUtils.getServiceType(masterCache, order.getChannelPartner()));
		// sending zero as last parameter(BigDecimal.ZERO) because we don't show
		// extra vouchers
		// data in service type report
		ReportUtil.addSettlementsData(data, order, order.getTransactionDetail().getPaidAmount(),
				order.getTransactionDetail(), false, BigDecimal.ZERO, stateTaxes);
	}

	private void addToDiscountedOrder(Order order) {
		if (TransactionUtils.isSkipOrder(order)) {
			return;
		}
		if (order.getTransactionDetail().getDiscountDetail() != null
				&& order.getTransactionDetail().getDiscountDetail().getDiscount() != null
				&& order.getTransactionDetail().getDiscountDetail().getDiscount().getPercentage() != null
				&& order.getTransactionDetail().getDiscountDetail().getDiscount().getPercentage()
						.compareTo(new BigDecimal(0.00d)) > 0) {

			discountedOrders.add(getDiscountedData(order));

		} else if (order.getTransactionDetail().getDiscountDetail() != null
				&& order.getTransactionDetail().getDiscountDetail().getPromotionalOffer() != null && BigDecimal.ZERO
						.compareTo(order.getTransactionDetail().getDiscountDetail().getPromotionalOffer()) != 0) {
			for (OrderItem item : order.getOrders()) {
				if (!item.getComplimentaryDetail().isIsComplimentary()
						&& BigDecimal.ZERO.compareTo(item.getDiscountDetail().getPromotionalOffer()) != 0) {
					discountedOrders.add(getDiscountedData(order));
					break;
				}
			}
		}
	}

	private DiscountedBillData getDiscountedData(Order o) {
		DiscountedBillData data = new DiscountedBillData();
		data.setOrderId(o.getOrderId());
		data.setGeneratedOrderId(o.getGenerateOrderId());
		data.setEmployeeId(o.getEmployeeId());
		if (o.getTransactionDetail() == null) {
			LOG.info("Order with productId " + o.getGenerateOrderId() + " has null transaction detail");
		}
		if (o.getTransactionDetail().getDiscountDetail() != null) {
			if (o.getTransactionDetail().getDiscountDetail().getDiscountCode() != null) {
				data.setCode(masterCache.getDiscountCode(o.getTransactionDetail().getDiscountDetail().getDiscountCode())
						.getName());
			} else {
				data.setCode(AppConstants.RTL_CODE_DEFAULT_DISCOUNT_CODE);
			}
			data.setReason(o.getTransactionDetail().getDiscountDetail().getDiscountReason());
			data.setDiscountValue(o.getTransactionDetail().getDiscountDetail().getDiscount().getValue());
			data.setDiscountPercentage(o.getTransactionDetail().getDiscountDetail().getDiscount().getPercentage());
		}
		data.setPromotionalDiscount(o.getTransactionDetail().getDiscountDetail().getPromotionalOffer());
		data.setTotalAmount(o.getTransactionDetail().getTotalAmount());
		for (OrderItem item : o.getOrders()) {
			addToCost(item, data);
		}
		data.setTotalTax(o.getTransactionDetail().getTax());
		return data;
	}

	private void addToCost(OrderItem item, DiscountedBillData data) {
		if (!item.getComplimentaryDetail().isIsComplimentary()) {
			data.setTaxableAmount(data.getTaxableAmount().add(item.getAmount()));
		}
	}

	private CreditBillData getCreditData(Order o) {
		CreditBillData data = new CreditBillData();
		data.setOrderId(o.getOrderId());
		data.setGeneratedOrderId(o.getGenerateOrderId());
		data.setEmployeeId(o.getEmployeeId());
		data.setTotalAmount(o.getTransactionDetail().getTotalAmount());
		data.setChannelPartner(masterCache.getChannelPartner(o.getChannelPartner()));
		data.setOrderRemark(o.getOrderRemark());
		for (Settlement settlement : o.getSettlements()) {
			if (ReportUtil.isCredit(settlement)) {
				data.setCreditAmount(settlement.getAmount());
			}
		}
		return data;
	}

	private void addToComplimentaryOrder(Order order) {
		if (ReportUtil.isCancelled(order.getStatus())) {
			return;
		}
		// TODO This will change when we will introduce full bill complimentary
		for (OrderItem item : order.getOrders()) {
			if (ReportUtil.isComplimentary(item.getComplimentaryDetail())) {
				complimentaryOrders.add(getComplimentaryData(order));
				return;
			}
		}
	}

	private void addToCreditOrder(Order order) {
		if (ReportUtil.isCancelled(order.getStatus())) {
			return;
		}
		for (Settlement settlement : order.getSettlements()) {
			if(Objects.isNull(settlement.getModeDetail())){
				LOG.info("Settlement data for order id and Unit id and settlement data is : {} : {} : {}",order.getOrderId(),
						order.getUnitId(),new Gson().toJson(settlement));
			}
			if (ReportUtil.isCredit(settlement)) {
				creditOrders.add(getCreditData(order));
				return;
			}
		}
	}

	private ComplimentaryBillData getComplimentaryData(Order o) {
		ComplimentaryBillData data = new ComplimentaryBillData();
		data.setOrderId(o.getOrderId());
		data.setGeneratedOrderId(o.getGenerateOrderId());
		data.setTotalAmount(o.getTransactionDetail().getTotalAmount());
		data.setEmployeeId(o.getEmployeeId());
		data.setReason(o.getOrderRemark() == null ? "" : o.getOrderRemark());
		for (OrderItem item : o.getOrders()) {
			if (ReportUtil.isComplimentary(item.getComplimentaryDetail())) {
				if (item.getComplimentaryDetail().getReasonCode() != null) {
					data.setCode(metadataCache.getComplimentaryCode(item.getComplimentaryDetail().getReasonCode())
							.getName());
				} else {
					data.setCode(AppConstants.RTL_CODE_DEFAULT_COMPLIMENTARY_CODE);
				}
				data.setReason(((data.getReason() != null && !data.getReason().trim().equals(""))
						? data.getReason() + ", " : "") + item.getComplimentaryDetail().getReason());
				data.setComplimentaryValue(
						data.getComplimentaryValue().add(item.getPrice().multiply(new BigDecimal(item.getQuantity()))));
				if (AppUtils.isAccountable(
						metadataCache.getComplimentaryCode(item.getComplimentaryDetail().getReasonCode()).getType())) {
					data.setAccountableComp(data.getAccountableComp()
							.add(item.getPrice().multiply(new BigDecimal(item.getQuantity()))));
				}
			} else {
				data.setTaxableAmount(data.getTaxableAmount().add(item.getAmount()));
			}
		}
		data.setTotalTax(o.getTransactionDetail().getTax());
		return data;
	}

	public UnitConsumptionReportProvider getUnitReportProvider() {
		return unitReportProvider;
	}

	public SettlementReportProvider getSettlementReportProvider() {
		return settlementReportProvider;
	}

	public List<Order> getRePrintedBills() {
		return rePrintedBills;
	}

	public void setRePrintedBills(List<Order> rePrintedBills) {
		this.rePrintedBills = rePrintedBills;
	}

	public RevenueDataProvider getRevenueDataProvider() {
		return revenueDataProvider;
	}

}