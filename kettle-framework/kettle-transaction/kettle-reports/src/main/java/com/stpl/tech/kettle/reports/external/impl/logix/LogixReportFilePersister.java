/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.external.impl.logix;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.stpl.tech.kettle.core.notification.ErrorNotification;
import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.reports.core.ReportType;
import com.stpl.tech.kettle.reports.dao.impl.ReportFileData;
import com.stpl.tech.kettle.reports.external.DataMapper;
import com.stpl.tech.kettle.reports.external.DataPersister;
import com.stpl.tech.kettle.reports.external.impl.ExternalReportInputData;
import com.stpl.tech.kettle.reports.external.impl.ExternalReportOutputData;
import com.stpl.tech.kettle.reports.external.impl.logix.LogixOrderDataMapper.LogixFileInfoHeader;
import com.stpl.tech.kettle.reports.external.impl.logix.LogixOrderDataMapper.LogixOrderInfo;
import com.stpl.tech.util.AppUtils;

public class LogixReportFilePersister implements DataPersister<OrderInfo> {

	private Map<Class<?>, DataMapper<OrderInfo, String>> dataMappers;
	private ExternalReportOutputData output;
	private final ExternalReportInputData input;
	private static final Logger LOG = LoggerFactory.getLogger(LogixReportFilePersister.class);

	public LogixReportFilePersister(ExternalReportInputData input) {
		this.input = input;
		this.output = createOutputData(input);
		dataMappers = new HashMap<Class<?>, DataMapper<OrderInfo, String>>();
		registerDataMapper(LogixFileInfoHeader.class, new LogixFileInfoHeader());
		registerDataMapper(LogixOrderInfo.class, new LogixOrderInfo());
	}

	private ExternalReportOutputData createOutputData(ExternalReportInputData input) {
		ExternalReportOutputData out = new ExternalReportOutputData();
		out.setBusinessDate(input.getBusinessDate());
		out.setEmailId(input.getUnit().getUnitEmail());
		out.setEndOrderId(input.getEvent().getEndOrderId());
		out.setStartOrderId(input.getEvent().getStartOrderId());
		out.setEnv(input.getProps().getEnvironmentType());
		out.setUnit(input.getUnit());
		out.setConfigMap(input.getConfigMap());
		return out;
	}

	@Override
	public void persist(List<OrderInfo> data) {
		try {
			StringBuffer fileContent = new StringBuffer();
			if (data != null && !data.isEmpty()) {
				fileContent = generateFileContent(data);
			} else {
				fileContent = generateEmptyFile();
			}
			persistFile(fileContent, getFilePath());
		} catch (Exception e) {
			LOG.error("ERROR IN PROCESSING LOGIX DATA", e);
		}
	}

	private StringBuffer generateFileContent(List<OrderInfo> data) throws Exception {
		StringBuffer sb = new StringBuffer();
		List<String> errorList = new ArrayList<>();
		sb.append(getDataMappers().get(LogixFileInfoHeader.class).toRow(null));
		for (OrderInfo info : data) {
			try {
				if (OrderStatus.CANCELLED.equals(info.getOrder().getStatus())) {
					continue;
				}
				int zeroTaxItemCount = 0;
				for (OrderItem i : info.getOrder().getOrders()) {
					if (AppUtils.isGiftCard(i.getCode())) {
						zeroTaxItemCount = zeroTaxItemCount + 1;
					}
				}
				if (zeroTaxItemCount >= info.getOrder().getOrders().size()) {
					// skip for only zero tax bill
					continue;
				}
				sb.append(getDataMappers().get(LogixOrderInfo.class).toRow(info));
			} catch (Exception e) {
				LOG.error("Error while genrating details for order #{}", info.getOrder().getGenerateOrderId(), e);
				errorList.add(e.getStackTrace().toString());
			}
		}
		if (!errorList.isEmpty()) {
			new ErrorNotification("External Sales Report ERROR for Unit " + input.getUnit().getId(),
					"Error while parsing orders", errorList, input.getProps().getEnvironmentType()).sendEmail();
			// do not generate incorrect report.
			throw new Exception("unable to create External Sales Report for unit " + input.getUnit().getId());
		}
		return sb;
	}

	private StringBuffer generateEmptyFile() throws Exception {
		StringBuffer sb = new StringBuffer();
		sb.append(getDataMappers().get(LogixFileInfoHeader.class).toRow(null));
		return sb;
	}

	private String getFilePath() {
		return input.getProps().getBasePath() + "/" + input.getUnit().getId() + "/reports/external/" + getFileName();
	}

	private boolean persistFile(StringBuffer fileContent, String path) {
		try {
			Path filePath = Paths.get(path);
			if (filePath != null && filePath.getParent() != null && !Files.exists(filePath.getParent())) {
				Files.createDirectories(filePath.getParent());
			}
			if (filePath != null) {
				Files.write(filePath, String.valueOf(fileContent).getBytes());
				output.getReportFiles()
						.add(new ReportFileData(ReportType.EXTERNAL, "text/plain", getFileName(), path, true));
				output.setReportGenerated(true);
				output.setReportGenerationTime(AppUtils.getCurrentTimestamp());
				return true;
			}
		} catch (IOException e) {
			LOG.error("Error in writing LOGIX report", e);
		}
		return false;
	}

	@Override
	public Map<Class<?>, DataMapper<OrderInfo, String>> getDataMappers() {
		return dataMappers;
	}

	@Override
	public void setDataMappers(Map<Class<?>, DataMapper<OrderInfo, String>> dataMappers) {
		this.dataMappers = dataMappers;
	}

	@Override
	public void registerDataMapper(Class<?> clazz, DataMapper<OrderInfo, String> mapper) {
		dataMappers.put(clazz, mapper);
	}

	public ExternalReportOutputData getOutput() {
		return output;
	}

	public String getFileName() {
		StringBuilder fileName = new StringBuilder();
		fileName.append(new SimpleDateFormat("yyyyMMdd").format(input.getBusinessDate()));
		fileName.append(".csv");
		return fileName.toString();
	}

}
