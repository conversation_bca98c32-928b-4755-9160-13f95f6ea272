package com.stpl.tech.kettle.reports.process;

import java.io.File;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.stpl.tech.kettle.core.data.vo.RevenueCertificateData;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.notification.AbstractVelocityTemplate;

public class RevenueCertificateTemplate extends AbstractVelocityTemplate {

	private List<RevenueCertificateData> certificates;
	private int month;
	private int year;
	private String basePath;
	private String unitName;
	private String filePath;
	private Boolean isBreakdown;
//	private String urlBasePath;

	public RevenueCertificateTemplate(String basePath, List<RevenueCertificateData> certificates, int month, int year,
			String unitName, Boolean isBreakdown) {
//		this.urlBasePath=urlBasePath;
		this.certificates = certificates;
		this.month = month;
		this.year = year;
		this.basePath = basePath;
		this.unitName = unitName;
		this.isBreakdown = isBreakdown;
	}

	@Override
	public String getTemplatePath() {
		return "templates/RevenueCertificateTemplate.html";
	}

	@Override
	public String getFilepath() {
		if (filePath == null) {
			filePath = basePath + File.separator + "revenueCertificate" + File.separator + month + year + ".html";
		}
		return filePath;
	}

	@Override
	public Map<String, Object> getData() {
		Map<String, Object> stringObjectMap = new HashMap<>();
		stringObjectMap.put("certificates", certificates);
		stringObjectMap.put("month", AppUtils.getMonthName(month));
		stringObjectMap.put("year", year);
		stringObjectMap.put("unitName", unitName);
		stringObjectMap.put("dateToday", AppUtils.getCurrentTimestamp());
		BigDecimal totalGrossSales = BigDecimal.ZERO;
		BigDecimal totalChaayosDineInSales = BigDecimal.ZERO;
		BigDecimal totalChaayosDeliverySales = BigDecimal.ZERO;
		BigDecimal totalGntDineInSales = BigDecimal.ZERO;
		BigDecimal totalGntDeliverySales = BigDecimal.ZERO;
		BigDecimal totalDesiCanteenDineInSales = BigDecimal.ZERO;
		BigDecimal totalDesiCanteenDeliverySales = BigDecimal.ZERO;
		BigDecimal totalServiceCharge = BigDecimal.ZERO;
		BigDecimal totalSalesAfterServiceCharge = BigDecimal.ZERO;
		BigDecimal totalDiscount = BigDecimal.ZERO;
		BigDecimal totalTaxes = BigDecimal.ZERO;
		BigDecimal totalNetSale = BigDecimal.ZERO;
		for (RevenueCertificateData d : certificates) {
			totalDiscount = AppUtils.add(d.getDiscount(), totalDiscount);
			totalServiceCharge = AppUtils.add(d.getServiceCharge(), totalServiceCharge);
			totalSalesAfterServiceCharge = AppUtils.add(d.getSalesAfterServiceCharge(), totalSalesAfterServiceCharge);
			totalTaxes = AppUtils.add(d.getTaxes(), totalTaxes);
			totalNetSale = AppUtils.add(d.getNetSale(), totalNetSale);
			totalGrossSales = AppUtils.add(d.getGrossSales(), totalGrossSales);
			totalChaayosDineInSales = AppUtils.add(d.getChaayosDineInSales(), totalChaayosDineInSales);
			totalChaayosDeliverySales = AppUtils.add(d.getChaayosDeliverySales(), totalChaayosDeliverySales);
			totalDesiCanteenDineInSales = AppUtils.add(d.getDesiCanteenDineInSales(), totalDesiCanteenDineInSales);
			totalDesiCanteenDeliverySales = AppUtils.add(d.getDesiCanteenDeliverySales(), totalDesiCanteenDeliverySales);
			totalGntDineInSales = AppUtils.add(d.getGntDineInSales(), totalGntDineInSales);
			totalGntDeliverySales = AppUtils.add(d.getGntDeliverySales(), totalGntDeliverySales);
		}
		stringObjectMap.put("totalGrossSales", totalGrossSales);
		stringObjectMap.put("totalServiceCharge", totalServiceCharge);
		stringObjectMap.put("totalSalesAfterServiceCharge", totalSalesAfterServiceCharge);
		stringObjectMap.put("totalDiscount", totalDiscount);
		stringObjectMap.put("totalTaxes", totalTaxes);
		stringObjectMap.put("totalNetSale", totalNetSale);
		stringObjectMap.put("totalChaayosDineInSales", totalChaayosDineInSales);
		stringObjectMap.put("totalChaayosDeliverySales", totalChaayosDeliverySales);
		stringObjectMap.put("totalGntDineInSales", totalGntDineInSales);
		stringObjectMap.put("totalGntDeliverySales", totalGntDeliverySales);
		stringObjectMap.put("totalDesiCanteenDineInSales", totalDesiCanteenDineInSales);
		stringObjectMap.put("totalDesiCanteenDeliverySales", totalDesiCanteenDeliverySales);
		stringObjectMap.put("isBreakdown", isBreakdown);
//		stringObjectMap.put("urlBasePath", urlBasePath);

		return stringObjectMap;
	}

}
