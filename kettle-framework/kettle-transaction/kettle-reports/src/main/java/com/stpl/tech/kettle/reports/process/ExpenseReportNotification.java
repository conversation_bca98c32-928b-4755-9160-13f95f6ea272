/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.process;

import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.reports.dao.impl.ExpenseReportData;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.notification.EmailNotification;
import com.stpl.tech.util.EnvType;

import java.text.SimpleDateFormat;

public class ExpenseReportNotification extends EmailNotification {

	private final ExpenseReportData output;

	public ExpenseReportNotification() {
		this.output = null;
	}

	public ExpenseReportNotification(ExpenseReportData output) {
		this.output = output;
	}

	public String subject() {
		return (TransactionUtils.isDev(getEnvironmentType()) ? "DEV : " : "") + "Expense Report for : " + output.getEvent().getType().name() + "-"
				+ output.getEvent().getIterationNumber() + "-"
				+ new SimpleDateFormat("yyyy-MM-dd").format(output.getBusinessDate());
	}

	public String body() throws EmailGenerationException {

		return "<html><p>Expense Report for : " + new SimpleDateFormat("yyyy-MM-dd").format(output.getBusinessDate())
				+ "</p></html>";
	}

	public String getFromEmail() {
		return "<EMAIL>";
	}

	public String[] getToEmails() {
		return TransactionUtils.isDev(getEnvironmentType()) ? new String[] { "<EMAIL>" } : new String[] { "<EMAIL>" };

	}

	@Override
	public EnvType getEnvironmentType() {
		return output.getEnv();
	}
}
