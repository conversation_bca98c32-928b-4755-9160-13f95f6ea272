/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.external.impl.logix;

import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.reports.external.ReportTransmission;
import com.stpl.tech.kettle.reports.external.impl.AbstractReportExecutor;
import com.stpl.tech.kettle.reports.external.impl.ExternalReportInputData;
import com.stpl.tech.kettle.reports.external.impl.ExternalReportOutputData;
import com.stpl.tech.kettle.reports.external.impl.FTPTransmission;
import com.stpl.tech.kettle.reports.external.impl.SFTPTransmission;
import com.stpl.tech.kettle.reports.external.impl.SalesDataProcessor;
import com.stpl.tech.kettle.reports.external.impl.SalesDataProvider;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.util.AppConstants;

public class LogixReportExecutor
		extends AbstractReportExecutor<Unit, OrderInfo, OrderInfo, ExternalReportInputData, ExternalReportOutputData> {

	private final ExternalReportInputData input;
	private LogixReportFilePersister persister;

	public LogixReportExecutor(ExternalReportInputData input) {
		this.input = input;
	}

	@Override
	public ExternalReportOutputData getReportOutput() {
		return persister.getOutput();
	}

	@Override
	public void preProcess() {
		setProvider(new SalesDataProvider<Unit>(input));
		setProcessor(new SalesDataProcessor());
		persister = new LogixReportFilePersister(input);
		registerPersister(persister);
		ReportTransmission transmission = null;
		if (AppConstants.SFTP.equals(input.getConfigMap().get("PROTOCOL"))) {
			transmission = new SFTPTransmission(input);
		} else {
			transmission = new FTPTransmission(input);
		}
		registerTransmitter(transmission);
	}

	@Override
	public void postProcess() {
		// do Nothing
	}

}
