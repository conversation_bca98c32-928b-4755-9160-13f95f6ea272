/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.view;

import com.stpl.tech.master.util.MasterUtil;
import org.subtlelib.poi.api.sheet.SheetContext;
import org.subtlelib.poi.api.style.Style;
import org.subtlelib.poi.api.workbook.WorkbookContext;

import com.stpl.tech.master.domain.model.Consumable;
import com.stpl.tech.master.domain.model.ConsumptionData;

public class ScmReportView extends ExcelReportView<ConsumptionData> {

	public ScmReportView(String header, String type) {
		super(header, type);
	}

	public void render(WorkbookContext workbookCtx, ConsumptionData data) {
		createSummaryView(workbookCtx, data);
		createItemView(workbookCtx, data);
	}

	private void createSummaryView(WorkbookContext workbookCtx, ConsumptionData data) {
		SheetContext sheetCtx = workbookCtx.createSheet("Summary");
		Style headerStyle = MasterUtil.getHeaderStyle(workbookCtx);
		sheetCtx.nextRow()
				.setTextStyle(headerStyle)
				.text("UnitClosureId").setColumnWidth(15)
				.text("UnitId").setColumnWidth(15)
				.text("businessDate").setColumnWidth(18)
				.text("Start Order Id").setColumnWidth(15)
				.text("End Order Id").setColumnWidth(15);
		
		sheetCtx.nextRow()
				.number(data.getClosureId())
				.number(data.getUnitId())
				.date(data.getBusinessDate())
				.number(data.getStartOrderId())
				.number(data.getEndOrderId());

	}
	
	private void createItemView(WorkbookContext workbookCtx, ConsumptionData data) {
		SheetContext sheetCtx = workbookCtx.createSheet("Item Consumption");
		Style headerStyle = MasterUtil.getHeaderStyle(workbookCtx);
		sheetCtx.nextRow()
				.setTextStyle(headerStyle)
				.text("Product Id").setColumnWidth(25)
				.text("Product Name").setColumnWidth(25)
				.text("Quantity").setColumnWidth(15)
				.text("UOM").setColumnWidth(15);

		for (Consumable c : data.getConsumables()) {
			sheetCtx.nextRow()
					.number(c.getProductId())
					.text(c.getName())
					.number(c.getQuantity())
					.text(c.getUom() == null ? "N/A" : c.getUom());
		}

	}
}
