/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.process;

import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.reports.dao.impl.SettlementReportOutputData;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.notification.EmailNotification;
import com.stpl.tech.util.EnvType;

import java.text.SimpleDateFormat;

public class AuditReportNotification extends EmailNotification {

	private final SettlementReportOutputData output;

	public AuditReportNotification() {
		this.output = null;
	}

	public AuditReportNotification(SettlementReportOutputData output) {
		this.output = output;
	}

	public String subject() {
		return (TransactionUtils.isDev(getEnvironmentType()) ? "DEV : " : "") + "Audit Report for : " + output.getUnit().getName() + " for "
				+ new SimpleDateFormat("yyyy-MM-dd").format(output.getBusinessDate());
	}

	public String body() throws EmailGenerationException {

		return "<html><p> Audit Report for : " + output.getUnit().getName() + " for "
				+ new SimpleDateFormat("yyyy-MM-dd").format(output.getBusinessDate()) + "</p></html>";
	}

	public String getFromEmail() {
		return TransactionUtils.isDev(getEnvironmentType()) ? "<EMAIL>" : output.getEmailId();
	}

	public String[] getToEmails() {
		return TransactionUtils.isDev(getEnvironmentType()) ? new String[] { "<EMAIL>" } : new String[] { "<EMAIL>" };

	}

	@Override
	public EnvType getEnvironmentType() {
		return output.getEnv();
	}
}
