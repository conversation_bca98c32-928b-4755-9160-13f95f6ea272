/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

/**
 * 
 */
package com.stpl.tech.kettle.reports.view;

import java.util.HashMap;
import java.util.Map;

import org.subtlelib.poi.api.workbook.WorkbookContext;

import com.stpl.tech.kettle.core.cache.MetadataCache;
import com.stpl.tech.master.core.external.cache.MasterDataCache;

/**
 * <AUTHOR>
 *
 */
public abstract class ExcelReportView<T> implements ReportView<WorkbookContext, T> {

	protected String header;
	protected String type;
	protected MetadataCache cache;
	protected MasterDataCache masterCache;
	private Map<Integer, String> employeeMap = new HashMap<Integer, String>();

	public ExcelReportView(String header, String type) {
		super();
		this.header = header;
		this.type = type;
	}

	public ExcelReportView(String header, String type, MetadataCache cache, MasterDataCache userService) {
		super();
		this.header = header;
		this.type = type;
		this.cache = cache;
		this.masterCache = userService;
	}

	protected String getEmployeeName(Integer id) {
		if (id == null) {
			return null;
		}
		String e = getEmployee(id);
		return e == null ? "Undefined for Id :" + id : e;
	}

	public String getEmployee(Integer id) {
		if (id == null) {
			return null;
		}
		String e = employeeMap.get(id);
		if (e == null) {
			e = masterCache.getEmployee(id);
			employeeMap.put(id, e);
		}
		return e;
	}
}
