/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.external.impl.logix;

import java.math.BigDecimal;
import java.util.Date;

import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.TransactionDetail;
import com.stpl.tech.kettle.reports.external.DataMapper;
import com.stpl.tech.kettle.reports.external.ExternalReportUtil;
import com.stpl.tech.kettle.reports.external.impl.moin.IFieldMapper;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.util.AppUtils;

public class LogixOrderDataMapper {

	private static final char DELIMETER = ',';

	public static class LogixFileInfoHeader implements DataMapper<OrderInfo, String> {

		public LogixFileInfoHeader() {
		}

		private enum FieldMapper implements IFieldMapper {
			COLUMN1(String.class, true, 20, 1, "billno"),
			COLUMN2(String.class, true, 20, 2, "billdate"),
			COLUMN3(String.class, true, 20, 3, "billamount"),
			COLUMN4(String.class, true, 20, 4, "totaltax"),
			COLUMN5(Integer.class, true, 20, 5, "discount"),
			COLUMN6(Integer.class, true, 20, 6, "return"),
			COLUMN7(Integer.class, true, 20, 7, "transactionType");

			private FieldMapper(Class<?> type, boolean required, int length, int position, String value) {
				this.type = type;
				this.required = required;
				this.length = length;
				this.position = position;
				this.format = value;
			}

			private final Class<?> type;
			private boolean required;
			private final int length;
			private final int position;
			private final String format;

			@Override
			public boolean isRequired() {
				return required;
			}

			@Override
			public void setRequired(boolean required) {
				this.required = required;
			}

			@Override
			public Class<?> getType() {
				return type;
			}

			@Override
			public int getLength() {
				return length;
			}

			@Override
			public int getPosition() {
				return position;
			}

			@Override
			public String getFormat() {
				return format;
			}
		}

		@Override
		public String toRow(OrderInfo o) throws Exception {
			StringBuilder sb = new StringBuilder();
			sb.append(FieldMapper.COLUMN1.getFormat() + DELIMETER);
			sb.append(FieldMapper.COLUMN2.getFormat() + DELIMETER);
			sb.append(FieldMapper.COLUMN3.getFormat() + DELIMETER);
			sb.append(FieldMapper.COLUMN4.getFormat() + DELIMETER);
			sb.append(FieldMapper.COLUMN5.getFormat() + DELIMETER);
			sb.append(FieldMapper.COLUMN6.getFormat() + DELIMETER);
			sb.append(FieldMapper.COLUMN7.getFormat());
			ExternalReportUtil.validateTotalColumns(sb, FieldMapper.values().length - 1, DELIMETER);
			return sb.toString() + ExternalReportUtil.lineEnd();
		}
	}

	public static class LogixOrderInfo implements DataMapper<OrderInfo, String> {

		private enum FieldMapper implements IFieldMapper {

			BILL_NO(String.class, true, 20, 1, null),
			BILL_DATE(Date.class, true, 10, 2, "yyyy-MM-dd"),
			BILL_AMOUNT(BigDecimal.class, true, 10, 3, null),
			TOTAL_TAX(BigDecimal.class, true, 10, 4, null),
			DISCOUNT(BigDecimal.class, true, 10, 5, null),
			RETURN(BigDecimal.class, true, 10, 6, null),
			TRANSACTION_TYPE(String.class, true, 10, 7, "SALE|RETURN|VOID");

			private FieldMapper(Class<?> type, boolean required, int length, int position, String format) {
				this.type = type;
				this.required = required;
				this.length = length;
				this.position = position;
				this.format = format;
			}

			private final Class<?> type;
			private boolean required;
			private final int length;
			private final int position;
			private final String format;

			@Override
			public boolean isRequired() {
				return required;
			}

			@Override
			public void setRequired(boolean required) {
				this.required = required;

			}

			@Override
			public Class<?> getType() {
				return type;
			}

			@Override
			public int getLength() {
				return length;
			}

			@Override
			public int getPosition() {
				return position;
			}

			@Override
			public String getFormat() {
				return format;
			}
		}

		@Override
		public String toRow(OrderInfo o) throws Exception {
			StringBuilder sb = new StringBuilder();

			TransactionDetail transDetail = o.getOrder().getTransactionDetail();
			BigDecimal zeroTaxAmount = BigDecimal.ZERO;
			for (OrderItem item : o.getOrder().getOrders()) {
				if (AppUtils.isGiftCard(item.getCode())) {
					zeroTaxAmount.add(ExternalReportUtil.getSellingAmount(item));
				}
			}
			// Sales = Amount Paid + Total Discount applied - gift card amount
			BigDecimal totalDiscount = transDetail.getDiscountDetail().getDiscount().getValue()
					.add(transDetail.getDiscountDetail().getPromotionalOffer());
			BigDecimal sales = transDetail.getPaidAmount().add(totalDiscount).subtract(zeroTaxAmount);
			String billNumber = getBillNumber(o);

			sb.append(ExternalReportUtil.getValue(billNumber, FieldMapper.BILL_NO) + DELIMETER);
			sb.append(
					ExternalReportUtil.getValue(o.getOrder().getBillCreationTime(), FieldMapper.BILL_DATE) + DELIMETER);
			sb.append(ExternalReportUtil.getValue(sales, FieldMapper.BILL_AMOUNT) + DELIMETER);
			sb.append(ExternalReportUtil.getValue(transDetail.getTax(), FieldMapper.TOTAL_TAX) + DELIMETER);
			sb.append(ExternalReportUtil.getValue(totalDiscount, FieldMapper.DISCOUNT) + DELIMETER);
			sb.append(ExternalReportUtil.getValue(0, FieldMapper.RETURN) + DELIMETER);
			sb.append(ExternalReportUtil.getValue("SALE", FieldMapper.TRANSACTION_TYPE));

			ExternalReportUtil.validateTotalColumns(sb, FieldMapper.values().length - 1, DELIMETER);
			return sb.toString() + ExternalReportUtil.lineEnd();

		}

		private String getBillNumber(OrderInfo o) {
			String billNum = null;
			if (UnitCategory.COD.name().equals(o.getOrder().getSource())) {
				billNum = "D";
			} else if (UnitCategory.TAKE_AWAY.name().equals(o.getOrder().getSource())) {
				billNum = "T";
			} else {
				billNum = "C";
			}
			billNum = billNum + ExternalReportUtil.padLeft(o.getOrder().getUnitOrderId(), 9);
			return billNum;
		}
	}
}
