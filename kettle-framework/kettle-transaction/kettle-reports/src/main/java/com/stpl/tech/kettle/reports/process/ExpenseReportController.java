/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.process;

import com.google.common.io.Files;
import com.stpl.tech.kettle.core.TransactionConstants;
import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.core.cache.MetadataCache;
import com.stpl.tech.kettle.core.service.ReportingService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.domain.model.ExpenseUpdateEvent;
import com.stpl.tech.kettle.reports.core.ReportType;
import com.stpl.tech.kettle.reports.dao.impl.ExpenseReportData;
import com.stpl.tech.kettle.reports.dao.impl.ExpenseReportDataProvider;
import com.stpl.tech.kettle.reports.dao.impl.ReportFileData;
import com.stpl.tech.kettle.reports.view.ExpenseReportSummaryView;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.subtlelib.poi.api.workbook.WorkbookContext;
import org.subtlelib.poi.impl.workbook.WorkbookContextFactory;

import java.io.File;
import java.io.IOException;

public class ExpenseReportController {

	private static final Logger LOG = LoggerFactory.getLogger(ExpenseReportController.class);

	private final EnvironmentProperties props;
	private final ExpenseReportDataProvider provider;
	private final WorkbookContextFactory ctxFactory = WorkbookContextFactory.useXlsx();

	public ExpenseReportController(MetadataCache cache, EnvironmentProperties props,
			ReportingService reportingService) {
		super();
		this.props = props;
		this.provider = new ExpenseReportDataProvider(cache, reportingService);
	}

	public ExpenseReportData execute(ExpenseUpdateEvent event) {
		provider.process(event);

		WorkbookContext workbookCtx = ctxFactory.createWorkbook();
		expenseSummaryView(workbookCtx, provider, event);
		ExpenseReportData output = new ExpenseReportData(props.getEnvironmentType(), AppUtils.getCurrentDate(), event);
		output.getReportFiles().add(generateExpenseReport(event, workbookCtx));
		// channelPartnerExpenseDrillDownView();
		// PaymentModeDrillDownView();
		// DeliveryPartnerDrillDownView();
		return output;
	}

	private void expenseSummaryView(WorkbookContext workbookCtx, ExpenseReportDataProvider provider,
			ExpenseUpdateEvent event) {
		ExpenseReportSummaryView view = new ExpenseReportSummaryView("Expense Report", "Expense", event.getType());
		view.render(workbookCtx, provider.getExpenseList());
	}

	private ReportFileData generateExpenseReport(ExpenseUpdateEvent event, WorkbookContext workbookCtx) {
		ReportFileData expenseReport = getExpenseReportFileData(event);
		try {
			Files.write(workbookCtx.toNativeBytes(), new File(expenseReport.getFilePath()));
		} catch (IOException e) {
			LOG.error("Error in writing Expense report for event Id {}", event.getEventId(), e);
		}
		return expenseReport;
	}

	private ReportFileData getExpenseReportFileData(ExpenseUpdateEvent event) {

		String fileName = String.format("%s_%s.xlsx", TransactionUtils.getFileName(TransactionConstants.PREFIX_EXPENSE_REPORT_OUTPUT,
				event.getType(), AppUtils.getCurrentDate()), AppUtils.getCurrentTimeISTStringWithNoColons());

		if (TransactionUtils.isDev(props.getEnvironmentType())) {
			fileName = "DEV_" + fileName;
		}
		String fileDir = String.format("%s/%s/output/%s/%s/", props.getBasePath(), TransactionConstants.APP_NAME_EXPENSE_REPORT,
				TransactionConstants.PREFIX_EXPENSE_REPORT, event.getType().name());

		File file = new File(fileDir);
		if (file != null && !file.exists()) {
			boolean madeDirectories = file.mkdirs();
			LOG.error("made directories for the expense report  {} ::: {}", fileDir, madeDirectories);
		}
		String filePath = fileDir + fileName;
		ReportFileData fileData = new ReportFileData(ReportType.EXPENSE, AppConstants.EXCEL_MIME_TYPE, fileName,
				filePath, true);
		return fileData;

	}
}
