/**
 * 
 */
package com.stpl.tech.kettle.reports.view;

import java.math.BigDecimal;
import java.util.Collection;

import org.subtlelib.poi.api.row.RowContext;

import com.stpl.tech.kettle.reports.model.SettlementReport;
import com.stpl.tech.master.tax.model.Taxation;

/**
 * <AUTHOR>
 *
 */
public abstract class AbstractSettlementReportView<T> extends ExcelReportView<T> {

	/**
	 * @param header
	 * @param type
	 */
	public AbstractSettlementReportView(String header, String type) {
		super(header, type);
	}

	protected void createTaxationHeader(RowContext context, Collection<Taxation> tds) {
		for (Taxation td : tds) {
			context.text(td.toHeaderString()).setColumnWidth(15);
		}
	}

	protected void createTaxationData(RowContext context, Collection<Taxation> tds, SettlementReport settlement) {
		for (Taxation td : tds) {
			BigDecimal tax = settlement.getTaxes().get(td);
			context.number(tax == null ? BigDecimal.ZERO : tax);
		}
	}

}
