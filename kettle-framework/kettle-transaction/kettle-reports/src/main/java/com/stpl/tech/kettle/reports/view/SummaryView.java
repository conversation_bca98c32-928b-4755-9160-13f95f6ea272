/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.view;

import java.math.BigDecimal;
import java.math.RoundingMode;

import com.stpl.tech.master.util.MasterUtil;
import org.subtlelib.poi.api.sheet.SheetContext;
import org.subtlelib.poi.api.style.Style;
import org.subtlelib.poi.api.workbook.WorkbookContext;

import com.stpl.tech.kettle.core.data.budget.vo.RevenueData;
import com.stpl.tech.kettle.reports.model.UnitConsumption;

public class SummaryView extends ExcelReportView<UnitConsumption> {

	private RevenueData revenue;

	public SummaryView(String header, String type, RevenueData revenue) {
		super(header, type);
		this.revenue = revenue;
	}

	/**
	 * Bill Nos | Bill Amount| Disc. %| Disc. Amt|Generated By |Reason For
	 * Discount
	 * ------------------------------------------------------------------
	 * ------------------------------------------------------
	 */
	public void render(WorkbookContext workbookCtx, UnitConsumption unit) {
		boolean billCheck = unit.getBillCount() > 0 || unit.getComplimentaryBillCount() > 0;

		SheetContext sheetCtx = workbookCtx.createSheet(header);
		Style headerStyle = MasterUtil.getHeaderStyle(workbookCtx);
		
		sheetCtx.nextRow().text("Unit Name").setColumnWidth(40).text(unit.getName());
		sheetCtx.nextRow().setTextStyle(headerStyle).text("A. Total No. of Bills (A=B+C)").setColumnWidth(20)
				.number(revenue.getTicket() + unit.getComplimentaryBillCount());
		sheetCtx.nextRow().text("B. Complimentary Bills").setColumnWidth(20).number(unit.getComplimentaryBillCount());
		sheetCtx.nextRow().text("C. Net Bills").setColumnWidth(20).number(revenue.getTicket());
		sheetCtx.nextRow().text("D. Gross Sales Amount").setColumnWidth(20)
				.number(getValue(billCheck, revenue.getRevenue()));
		sheetCtx.nextRow().text("E. Discounts").setColumnWidth(20).number(getValue(billCheck, revenue.getDiscount()));
        sheetCtx.nextRow().text("F. Net Revenue (F=D-E)").setColumnWidth(20)
                .number(getValue(billCheck, revenue.getRevenue()));
		sheetCtx.nextRow().text("G. Net GC Balance").setColumnWidth(20)
				.number(getValue(billCheck, revenue.getGiftCardNetSale()));
		sheetCtx.nextRow().text("H. Net Sales (H=D+G)").setColumnWidth(20)
				.number(getValue(billCheck, revenue.getNetSales()));
		sheetCtx.nextRow().text("I. Net Sales - APC (H=D/C)").setColumnWidth(20)
				.number(getValue(billCheck, revenue.getApc()));
		sheetCtx.nextRow().text("J. Dine In Net Sales").setColumnWidth(20)
				.number(getValue(billCheck, revenue.getDineInNetSales()));
		sheetCtx.nextRow().text("K. Delivery Net Sales").setColumnWidth(20)
				.number(getValue(billCheck, revenue.getDeliveryNetSales()));
		sheetCtx.nextRow().text("L. Number of Employee Meals").setColumnWidth(25)
				.number(revenue.getEmployeeMealTicket());
		sheetCtx.nextRow().text("M. Net Employee Meal Sales").setColumnWidth(25)
				.number(revenue.getEmployeeMealSales());
	}

	private BigDecimal getValue(boolean check, BigDecimal value) {
		return check ? value : BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
	}

}
