/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.external.impl;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.Validate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.stpl.tech.kettle.reports.core.ReportExecutor;
import com.stpl.tech.kettle.reports.core.ReportInput;
import com.stpl.tech.kettle.reports.core.ReportOutput;
import com.stpl.tech.kettle.reports.external.DataFilter;
import com.stpl.tech.kettle.reports.external.DataPersister;
import com.stpl.tech.kettle.reports.external.DataProcessor;
import com.stpl.tech.kettle.reports.external.DataProvider;
import com.stpl.tech.kettle.reports.external.ReportTransmission;

public abstract class AbstractReportExecutor<C, T, R, I extends ReportInput<C>, O extends ReportOutput>
		implements ReportExecutor<C, I, O> {

	private static final Logger LOG = LoggerFactory.getLogger(AbstractReportExecutor.class);

	private DataProvider<C, T> provider;

	private DataProcessor<T, R> processor;

	private List<DataFilter<T>> filters = new ArrayList<>();

	private List<DataPersister<R>> persisters = new ArrayList<>();

	private List<ReportTransmission> transmitters = new ArrayList<>();

	@Override
	public O execute(I data) {
		preProcess();
		Validate.notNull(provider, "Data Provider Cannot be null");
		List<T> inputData = provider.fetchContent(data.getContext());
		if (!filters.isEmpty()) {
			for (DataFilter<T> filter : filters) {
				LOG.info(String.format("Applying filter %s on data of size %d", filter.getClass().getName(),
						inputData.size()));
				inputData = filter.filter(inputData);
				LOG.info(String.format("Applied filter %s on data of reduced the size  to %d",
						filter.getClass().getName(), inputData.size()));
			}
		}
		List<R> processedData = processor.process(inputData);
		for (DataPersister<R> persister : persisters) {
			LOG.info(String.format("Peristing data using persister %s on data of size %d",
					persister.getClass().getName(), processedData.size()));
			persister.persist(processedData);
		}
		O output = getReportOutput();
		for (ReportTransmission transmitter : transmitters) {
			LOG.info(String.format("Transmitting data using transmitter %s on data of size %d",
					transmitter.getClass().getName(), processedData.size()));
			transmitter.transfer(output);
		}
		postProcess();
		return output;
	}

	public abstract O getReportOutput();

	public abstract void preProcess();

	public abstract void postProcess();

	public DataProvider<C, T> getProvider() {
		return provider;
	}

	public void setProvider(DataProvider<C, T> provider) {
		this.provider = provider;
	}

	public List<DataFilter<T>> getFilters() {
		return filters;
	}

	public void setFilters(List<DataFilter<T>> filters) {
		this.filters = filters;
	}

	public void registerFilter(DataFilter<T> filter) {
		this.filters.add(filter);
	}

	public List<DataPersister<R>> getPersisters() {
		return persisters;
	}

	public void setPersisters(List<DataPersister<R>> persisters) {
		this.persisters = persisters;
	}

	public void registerPersister(DataPersister<R> persister) {
		this.persisters.add(persister);
	}

	public List<ReportTransmission> getTransmitters() {
		return transmitters;
	}

	public void setTransmitters(List<ReportTransmission> transmitters) {
		this.transmitters = transmitters;
	}

	public void registerTransmitter(ReportTransmission transmitter) {
		this.transmitters.add(transmitter);
	}

	public DataProcessor<T, R> getProcessor() {
		return processor;
	}

	public void setProcessor(DataProcessor<T, R> processor) {
		this.processor = processor;
	}
}
