/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.view;

import com.stpl.tech.kettle.reports.model.CategoryConsumption;
import com.stpl.tech.kettle.reports.model.ProductConsumption;
import com.stpl.tech.kettle.reports.model.ProductDimensionConsumption;
import com.stpl.tech.kettle.reports.model.ProductPriceConsumption;
import com.stpl.tech.kettle.reports.model.ProductSourceConsumption;
import com.stpl.tech.kettle.reports.model.SubCategoryConsumption;
import com.stpl.tech.master.util.MasterUtil;
import com.stpl.tech.util.AppConstants;
import org.subtlelib.poi.api.sheet.SheetContext;
import org.subtlelib.poi.api.style.Style;
import org.subtlelib.poi.api.workbook.WorkbookContext;

import java.util.Collection;

public class ItemConsumptionView extends
		ExcelReportView<Collection<CategoryConsumption<ProductConsumption<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>>>>> {

	public ItemConsumptionView(String header, String type) {
		super(header, type);
	}

	/**
	 * Bill Nos | Bill Amount| Disc. %| Disc. Amt|Generated By |Reason For
	 * Discount
	 * ------------------------------------------------------------------
	 * ------------------------------------------------------
	 */
	public void render(WorkbookContext workbookCtx,
			Collection<CategoryConsumption<ProductConsumption<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>>>> orders) {
		SheetContext sheetCtx = workbookCtx.createSheet(header);
		Style headerStyle = MasterUtil.getHeaderStyle(workbookCtx);

		sheetCtx.nextRow().skipCell().setTextStyle(headerStyle).text("Categories").setColumnWidth(20).text("Sub Categories")
				.setColumnWidth(20).text("Products").setColumnWidth(20).text("Dimension").setColumnWidth(15)
				.text("Order Source").setColumnWidth(15).text("Price").setColumnWidth(15).text("Quantity")
				.setColumnWidth(15).text("Combo Quantity")
				.setColumnWidth(15).text("Compl. Quantity").setColumnWidth(15).text("Amount").setColumnWidth(15);

		for (CategoryConsumption<ProductConsumption<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>>> order : orders) {
			if (order.isHasItems()) {
				sheetCtx.nextRow().skipCell().setTextStyle(headerStyle).text(order.getName()).skipCell().skipCell().skipCell().skipCell()
						.skipCell().skipCell().skipCell().skipCell().number(order.getAmount());
				for (SubCategoryConsumption<ProductConsumption<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>>> sub : order
						.getAllItems()) {
					if (sub.isHasItems()) {
						sheetCtx.nextRow().skipCell().skipCell().setTextStyle(headerStyle).text(sub.getName()).skipCell()
								.skipCell().skipCell().skipCell().skipCell().skipCell().skipCell().number(sub.getAmount());
						for (ProductConsumption<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>> prod : sub
								.getAllItems()) {
							if (prod.isHasItems()) {
								for (ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>> source : prod
										.getAllItems()) {
									for (ProductDimensionConsumption<ProductPriceConsumption> dim : source
											.getAllItems()) {
										for (ProductPriceConsumption price : dim.getAllItems()) {
											if (price.getQuantity() > 0 || price.getComplimentaryQuantity() > 0 || price.getCompositeQuantity() > 0) {
												sheetCtx.nextRow().skipCell().skipCell().skipCell().text(prod.getName())
														.text((AppConstants.NO_DIMENSION_STRING.equals(
																dim.getDimension()) ? "" : " " + dim.getDimension()))
														.text(source.getName()).number(price.getPrice())
														.number(price.getQuantity())
														.number(price.getCompositeQuantity())
														.number(price.getComplimentaryQuantity())
														.number(price.getAmount());
											}
										}
									}
								}
							}
						}
					}
				}

			}
		}

	}

}
