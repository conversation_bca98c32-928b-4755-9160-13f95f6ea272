/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.view;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;

import com.stpl.tech.master.util.MasterUtil;
import org.apache.commons.lang.WordUtils;
import org.subtlelib.poi.api.row.RowContext;
import org.subtlelib.poi.api.sheet.SheetContext;
import org.subtlelib.poi.api.style.Style;
import org.subtlelib.poi.api.workbook.WorkbookContext;

import com.stpl.tech.kettle.core.TransactionConstants;
import com.stpl.tech.kettle.reports.dao.impl.ReportUtil;
import com.stpl.tech.kettle.reports.model.CategoryConsumption;
import com.stpl.tech.kettle.reports.model.ProductConsumption;
import com.stpl.tech.kettle.reports.model.ProductDimensionConsumption;
import com.stpl.tech.kettle.reports.model.ProductPriceConsumption;
import com.stpl.tech.kettle.reports.model.ProductSourceConsumption;
import com.stpl.tech.kettle.reports.model.SubCategoryConsumption;
import com.stpl.tech.kettle.reports.model.UnitConsumption;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.PaymentMode;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.tax.model.Taxation;
import com.stpl.tech.util.AppConstants;

public class ConsumptionReportView {

	private String header;
	private Set<PaymentMode> paymentModes;
	private Set<String> taxSet;
	private String voucherRefNo = "";

	public ConsumptionReportView(String header, Set<PaymentMode> paymentModes, Set<Taxation> taxationData) {
		this.header = header;
		this.paymentModes = paymentModes;
		this.taxSet = new TreeSet<String>();
		// taxes required in case missing
		this.taxSet.add("CESS");
		this.taxSet.add("IGST");
		for (Taxation t : taxationData) {
			this.taxSet.add(t.getCode());
		}
	}

	public void render(WorkbookContext workbookCtx, Map<Unit, UnitConsumption> consumptionMap, Date businessDate) {
		SheetContext sheetCtx = workbookCtx.createSheet(header);
		Style headerStyle = MasterUtil.getHeaderStyle(workbookCtx);
		addHeader(sheetCtx, headerStyle);
		voucherRefNo = new SimpleDateFormat("ddMMyyyy").format(businessDate) + "_SALE";
		for (Unit unit : consumptionMap.keySet()) {
			addRows(sheetCtx, unit, consumptionMap.get(unit), businessDate);
		}
	}

	public void renderComplimentaryView(WorkbookContext workbookCtx, Map<Unit, UnitConsumption> consumptionMap,
			Date businessDate) {
		SheetContext sheetCtx = workbookCtx.createSheet(header);
		Style headerStyle = MasterUtil.getHeaderStyle(workbookCtx);
		addComplimentaryHeader(sheetCtx, headerStyle);
		voucherRefNo = new SimpleDateFormat("ddMMyyyy").format(businessDate) + "_SALE";
		for (Unit unit : consumptionMap.keySet()) {
			// createTaxProfileMap(unit);
			addComplimentaryRows(sheetCtx, unit, consumptionMap.get(unit), businessDate);
		}
	}

	private void addHeader(SheetContext sheetCtx, Style headerStyle) {

		RowContext rowContext = sheetCtx.nextRow().skipCell().setTextStyle(headerStyle).text("Date").setColumnWidth(20).text("UnitId")
				.setColumnWidth(20).text("Unit Name").setColumnWidth(20).text("State").setColumnWidth(20).text("Products").setColumnWidth(20)
				.text("Dimension").setColumnWidth(15).text("Order Source").setColumnWidth(15)
				.text("Busy Product Name").setColumnWidth(15).text("Price").setColumnWidth(15)
				.text("Sold Quantity").setColumnWidth(15).text("Combo Quantity").setColumnWidth(15)
				.text("Total Quantity").setColumnWidth(15).text("Gross value").setColumnWidth(15)
				.text("Standard Discount Amount").setColumnWidth(15).text("Combo Discount Amount")
				.setColumnWidth(15).text("Total Discount Amount").setColumnWidth(15).text("Taxable Amount")
				.setColumnWidth(15);

		for (String t : taxSet) {
			rowContext.text(t + " Rate").setColumnWidth(15).text(t).setColumnWidth(15);
		}

		rowContext.setColumnWidth(15).text("Round Off").setColumnWidth(15);

		for (PaymentMode mode : paymentModes) {
			rowContext.text(mode.getDescription()).setColumnWidth(15);
			rowContext.text("Voucher Ref No").setColumnWidth(15);
		}

		rowContext.text("Total Collection").setColumnWidth(15).text("Total Ticket Collection").setColumnWidth(15)
				.text("Diff").setColumnWidth(15).text("Extra Vouchers").setColumnWidth(15);

	}

	private void addComplimentaryHeader(SheetContext sheetCtx, Style headerStyle) {
		sheetCtx.nextRow().skipCell().setTextStyle(headerStyle).text("Date").setColumnWidth(20).text("UnitId").setColumnWidth(20)
				.text("Unit Name").setColumnWidth(20).text("Products").setColumnWidth(20).text("Dimension")
				.setColumnWidth(15).text("Order Source").setColumnWidth(15).text("Busy Product Name")
				.setColumnWidth(15).text("Price").setColumnWidth(15).text("Compl. Quantity").setColumnWidth(15);
	}

	private void addRows(SheetContext sheetCtx, Unit unit, UnitConsumption consumption, Date businessDate) {
		String businessDateString = new SimpleDateFormat("MM-dd-yyyy").format(businessDate);
		BigDecimal taxableAmount = BigDecimal.ZERO;
		for (CategoryConsumption<ProductConsumption<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>>> category : consumption
				.getAllItems()) {
			for (SubCategoryConsumption<ProductConsumption<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>>> sub : category
					.getAllItems()) {
				for (ProductConsumption<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>> prod : sub
						.getAllItems()) {
					for (ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>> source : prod
							.getAllItems()) {
						for (ProductDimensionConsumption<ProductPriceConsumption> dim : source.getAllItems()) {
							for (ProductPriceConsumption price : dim.getAllItems()) {
								if (isNotCombo(category.getName())
										&& (price.getQuantity() > 0 || price.getCompositeQuantity() > 0)) {
									taxableAmount = ReportUtil.getTaxableAmount(price);

									RowContext rowContext = sheetCtx.nextRow().skipCell().text(businessDateString)
											// UnitId
											.number(unit.getId())
											// Unit Name
											.text(unit.getName())
											// State
											.text(unit.getLocation().getState().getName())
											// Products
											.text(prod.getName())
											// Dimension
											.text(getDimesionName(dim.getDimension()))
											// Order Source
											.text(source.getName())
											// Busy Product Name
											.text(getBusyProductName(source.getName(), prod.getName(),
													dim.getDimension()))
											// Price
											.number(setScale(price.getPrice()))
											// Sold Quantity
											.number(price.getQuantity())
											// Combo Quantity
											.number(price.getCompositeQuantity())
											// Total Quantity
											.number(ReportUtil.getTotalQuantity(price))
											// Gross Amount
											.number(setScale(ReportUtil.getGrossAmount(price)))
											// Standard Discount Amount
											.number(setScale(price.getStandardDiscount()))
											// Composite Discount
											.number(setScale(price.getCompositeDiscount()))
											// Total Discount
											.number(setScale(ReportUtil.getTotalDiscount(price)))
											// Taxable Amount
											.number(setScale(taxableAmount));

									BigDecimal taxes = BigDecimal.ZERO;

									for (String t : taxSet) {
										Pair<BigDecimal, BigDecimal> pair = price.getTaxes().get(t);
										if (pair != null) {
											rowContext.number(pair.getKey()).setColumnWidth(15).number(setScale(pair.getValue()))
													.setColumnWidth(15);

											taxes = taxes.add(pair.getValue());
										} else {
											rowContext.number(BigDecimal.ZERO).setColumnWidth(15)
													.number(BigDecimal.ZERO).setColumnWidth(15);
										}
									}

									// Round Off
									rowContext.number(setScale(
											price.getSettlementsByModes().get(TransactionConstants.ROUND_OFF) != null
													? price.getSettlementsByModes().get(TransactionConstants.ROUND_OFF)
													: BigDecimal.ZERO));

									/*taxes = taxes.add(
											price.getSettlementsByModes().get(TransactionConstants.ROUND_OFF) != null
													? price.getSettlementsByModes().get(TransactionConstants.ROUND_OFF)
													: BigDecimal.ZERO);
									*/

									BigDecimal totalCollection = BigDecimal.ZERO;

									for (PaymentMode mode : paymentModes) {
										BigDecimal value = price.getSettlementsByModes().get(mode.getName()) != null
												? price.getSettlementsByModes().get(mode.getName())
												: BigDecimal.ZERO;
										totalCollection = totalCollection.add(value);
										rowContext.number(setScale(value)).setColumnWidth(15);
										rowContext.text(voucherRefNo).setColumnWidth(15);
									}

									// total settlement collection
									rowContext.number(setScale(totalCollection));
									BigDecimal ticketCollection = BigDecimal.ZERO.add(taxableAmount).add(taxes);
									// total ticket collection
									rowContext.number(setScale(ticketCollection))
											// difference
											.number(setScale(totalCollection).subtract(setScale(ticketCollection)));
									// extra vouchers
									rowContext.number(setScale(price.getSettlementsByModes()
											.get(TransactionConstants.EXTRA_VOUCHERS) != null
													? price.getSettlementsByModes()
															.get(TransactionConstants.EXTRA_VOUCHERS)
													: BigDecimal.ZERO));
								}
							}
						}
					}
				}
			}
		}
	}

	private BigDecimal setScale(BigDecimal value) {
		return value.setScale(2, BigDecimal.ROUND_HALF_UP);
	}

	private boolean isNotCombo(String categoryName) {
		return !AppConstants.RTL_GROUP_CATEGORY_COMBO_NAME.equals(categoryName);
	}

	private String getBusyProductName(String source, String product, String dimension) {
		String str = product + " " + getShortCode(getDimesionName(dimension));
		if (UnitCategory.CAFE.name().equals(source)) {
			return str.trim();
		} else {
			return str.trim() + " " + getShortCode(source.toLowerCase());
		}
	}

	private String getShortCode(String str) {
		return WordUtils.capitalizeFully(str.replace("_", " ").replaceAll("([A-Z])", " $1")).replaceAll("[^A-Z]", "");
	}

	private String getDimesionName(String dimension) {
		return (AppConstants.NO_DIMENSION_STRING.equals(dimension) ? " " : " " + dimension).trim();
	}

	private void addComplimentaryRows(SheetContext sheetCtx, Unit unit, UnitConsumption consumption,
			Date businessDate) {
		for (CategoryConsumption<ProductConsumption<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>>> category : consumption
				.getAllItems()) {
			for (SubCategoryConsumption<ProductConsumption<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>>> sub : category
					.getAllItems()) {
				for (ProductConsumption<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>> prod : sub
						.getAllItems()) {
					for (ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>> source : prod
							.getAllItems()) {
						for (ProductDimensionConsumption<ProductPriceConsumption> dim : source.getAllItems()) {
							for (ProductPriceConsumption price : dim.getAllItems()) {
								if (isNotCombo(category.getName()) && price.getComplimentaryQuantity() > 0) {
									sheetCtx.nextRow().skipCell().date(businessDate)
											// UnitId
											.number(unit.getId())
											// Unit Name
											.text(unit.getName())
											// Products
											.text(prod.getName())
											// Dimension
											.text((AppConstants.NO_DIMENSION_STRING.equals(dim.getDimension()) ? ""
													: " " + dim.getDimension()))
											// Order Source
											.text(source.getName())
											// Busy Product Name
											.text(getBusyProductName(source.getName(), prod.getName(),
													dim.getDimension()))
											// Price
											.number(setScale(price.getPrice()))
											// Complimentary Quantity
											.number(price.getComplimentaryQuantity());
								}
							}
						}
					}
				}
			}
		}
	}

}
