/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.view;

import java.util.Collection;

import com.stpl.tech.master.util.MasterUtil;
import org.subtlelib.poi.api.sheet.SheetContext;
import org.subtlelib.poi.api.style.Style;
import org.subtlelib.poi.api.workbook.WorkbookContext;

import com.stpl.tech.kettle.domain.model.IterationType;
import com.stpl.tech.kettle.domain.model.UnitExpense;
import com.stpl.tech.util.AppUtils;

public class ExpenseReportSummaryView extends ExcelReportView<Collection<UnitExpense>> {

	private IterationType iterationType;
	
	public ExpenseReportSummaryView(String header, String type, IterationType iterationType) {
		super(header, type);
		this.iterationType = iterationType;
	}

	@Override
	public void render(WorkbookContext workbookCtx, Collection<UnitExpense> unitExpenseList) {
		if(IterationType.MOM.equals(iterationType)){
			renderMOM(workbookCtx, unitExpenseList);
		}else{
			renderWOW(workbookCtx, unitExpenseList);
		}
	}
	
	private void renderMOM(WorkbookContext workbookCtx, Collection<UnitExpense> unitExpenseList) {

		SheetContext sheetCtx = workbookCtx.createSheet(header);
		Style headerStyle = MasterUtil.getHeaderStyle(workbookCtx);
		sheetCtx.nextRow()
				.skipCell()
				.setTextStyle(headerStyle)
				.text("Comments").setColumnWidth(20)
				.text("Unit Id").setColumnWidth(20)
				.text("Unit Name").setColumnWidth(20)
				.text("Total Tickets").setColumnWidth(20)
				.text("Total Delivery Tickets").setColumnWidth(20)
				.text("Net Tickets").setColumnWidth(20)
				.text("Net Delivery Tickets").setColumnWidth(20)
				.text("GMV").setColumnWidth(20)
				.text("Delivery GMV").setColumnWidth(20)
				.text("Sales").setColumnWidth(20)
				.text("Delivery Sales").setColumnWidth(20)
				.text("Total Cost").setColumnWidth(20)
				.text("EBITDA Percentage").setColumnWidth(20)
				.text("COGS").setColumnWidth(20)
				.text("Delivery COGS").setColumnWidth(20)
				.text("Employee Meal Cost").setColumnWidth(20)
				.text("Unsatified Customer Cost").setColumnWidth(20)
				.text("PPE Cost").setColumnWidth(20)
				.text("Unsatified Customer Cost for Delivery").setColumnWidth(20)
				.text("PPE Cost for Delivery").setColumnWidth(20)
				.text("Sampling and Marketing Cost").setColumnWidth(20)
				.text("Sampling and Marketing Cost for Delivery").setColumnWidth(20)
				.text("Credit Card Charges").setColumnWidth(20)
				.text("Amex Charges").setColumnWidth(20)
				.text("Sodexo Charges").setColumnWidth(20)
				.text("Tkt Rest Charges").setColumnWidth(20)
				.text("Channel Partner Cost").setColumnWidth(20)
				.text("Delivery Partner Cost").setColumnWidth(20)
				.text("Manpower").setColumnWidth(20)
				.text("Consumables - Utilities").setColumnWidth(20)
				.text("Consumables - Stationary").setColumnWidth(20)
				.text("Consumables - Uniform").setColumnWidth(20)
				.text("Consumables - Equipment").setColumnWidth(20)
				.text("Consumables - Cutlery").setColumnWidth(20)
				.text("Wastage & Expired").setColumnWidth(20)
				.text("Electricity").setColumnWidth(20)
				.text("Water").setColumnWidth(20)
				.text("DG - Rent").setColumnWidth(20)
				.text("DG - Charges").setColumnWidth(20)
				.text("SCM Rental").setColumnWidth(20)
				.text("EDC Machine").setColumnWidth(20)
				.text("Freight Outward").setColumnWidth(20)
				.text("Convenyance").setColumnWidth(20)
				.text("Staff Welfare").setColumnWidth(20)
				.text("Repair & Maintenance - Minor").setColumnWidth(20)
				.text("Change Commission").setColumnWidth(20)
				.text("Courier").setColumnWidth(20)
				.text("Printing & Stationery").setColumnWidth(20)
				.text("Misc Exp").setColumnWidth(20)
				.text("Parking charges").setColumnWidth(20)
				.text("Cleaning Charges").setColumnWidth(20)
				.text("Newspaper").setColumnWidth(20)
				.text("CAM").setColumnWidth(20)
				.text("Internet").setColumnWidth(20)
				.text("Telephone").setColumnWidth(20)
				.text("Ops Cost Percentage").setColumnWidth(20)
				.text("Ops Cost Total").setColumnWidth(20)
				.text("Kitchen Cost Percentage").setColumnWidth(20)
				.text("Kitchen Cost Total").setColumnWidth(20)
				.text("Repair & Maintenance - Major").setColumnWidth(20)
				.text("Fixed Rent").setColumnWidth(20)
				.text("Rent Percentage").setColumnWidth(20)
				.text("Rent").setColumnWidth(20)
				.text("Customer Care Cost").setColumnWidth(20)
				.text("Maintenance Team Cost").setColumnWidth(20)
				.text("Training Team Cost").setColumnWidth(20)
				.text("IT Team Cost").setColumnWidth(20)
				.text("MSP").setColumnWidth(20)
				.text("System Rent").setColumnWidth(20)
				.text("Insurance").setColumnWidth(20)
				.text("Paytm Charges").setColumnWidth(20)
				.text("Paytm Charges for Delivery").setColumnWidth(20)
				.text("Mobikwik Carges").setColumnWidth(20)
				.text("FreeCharge Charges").setColumnWidth(20);

		for (UnitExpense expense : unitExpenseList) {
			sheetCtx.nextRow()
					.skipCell()
					.text(expense.getComments()!= null ? expense.getComments() : "")
					.number(expense.getUnitId())
					.text(expense.getUnitName())
					.number(expense.getTotalTickets())
					.number(expense.getDeliveryTotalTickets())
					.number(expense.getNetTickets())
					.number(expense.getDeliveryNetTickets())
					.number(expense.getGmvAmount())
					.number(expense.getDeliveryGMV())
					.number(expense.getNetSalesAmount())
					.number(expense.getDeliverySales())
					.number(expense.getTotalCost())
					.number(expense.getEbitdaPercentage())
					.number(expense.getCogs())
					.number(expense.getDeliveryCOGS())
					.number(expense.getEmployeeMeal())
					.number(expense.getUnsatifiedCustomerCost())
					.number(expense.getPPECost())
					.number(expense.getDeliveryUnsatisfiedCustomerCost())
					.number(expense.getDeliveryPPECost())
					.number(expense.getMarketingAndSampling())
					.number(expense.getDeliverySampleingAndMarketingCost())
					.number(expense.getCreditCardCharges())
					.number(expense.getAmexCardCharges())
					.number(expense.getSodexoCharges())
					.number(expense.getTktRestaurantCharges())
					.number(expense.getChannelPartnerCharges())
					.number(expense.getDeliveryCost())
					.number(expense.getManpower())
					.number(expense.getConsumablesAndUtilities())
					.number(expense.getConsumablesStationary())
					.number(expense.getConsumablesUniform())
					.number(expense.getConsumablesEquipment())
					.number(expense.getConsumablesCutlery())
					.number(expense.getWastageAndExpired())
					.number(expense.getElectricity())
					.number(expense.getWater())
					.number(expense.getRentDG())
					.number(expense.getChargesDG())
					.number(expense.getScmRental())
					.number(expense.getEdcMachine())
					.number(expense.getFreightOutward())
					.number(expense.getConvenyance())
					.number(expense.getStaffWelfare())
					.number(expense.getRepairAndMaintenanceMinor())
					.number(expense.getChangeCommission())
					.number(expense.getCourier())
					.number(expense.getPrintingAndStationery())
					.number(expense.getMiscExp())
					.number(expense.getParkingCharges())
					.number(expense.getCleaningCharges())
					.number(expense.getNewspaper())
					.number(expense.getCamCharges())
					.number(expense.getInternet())
					.number(expense.getTelephone())
					.number(expense.getOpsCostPercentage())
					.number(expense.getOpsCostTotal())
					.number(expense.getKitchenCostPercentage())
					.number(expense.getKitchenCostTotal())
					.number(expense.getRepairAndMaintenanceMajor())
					.number(expense.getFixedRent())
					.number(expense.getRentPercentage())
					.number(expense.getRent())
					.number(expense.getCustomerCareCost())
					.number(expense.getMaintenanceTeamCost())
					.number(expense.getTrainingTeamCost())
					.number(expense.getItTeamCost())
					.number(expense.getMsp())
					.number(expense.getSystemRent())
					.number(expense.getInsurance())
					.number(expense.getPaytmCharges())
					.number(expense.getDeliveryPaytmCharges())
					.number(expense.getMobikwikCharges())
					.number(expense.getFreeChargeCharges());
		}
	}

	private void renderWOW(WorkbookContext workbookCtx, Collection<UnitExpense> unitExpenseList){

		SheetContext sheetCtx = workbookCtx.createSheet(header);
		Style headerStyle = MasterUtil.getHeaderStyle(workbookCtx);
		sheetCtx.nextRow()
				.skipCell()
				.setTextStyle(headerStyle)
				.text("Comments").setColumnWidth(20)
				.text("Unit Id").setColumnWidth(20)
				.text("Unit Name").setColumnWidth(20)
				.text("Total Tickets").setColumnWidth(20)
				.text("Total Delivery Tickets").setColumnWidth(20)	
				.text("Net Tickets").setColumnWidth(20)
				.text("Net Delivery Tickets").setColumnWidth(20)
				.text("GMV").setColumnWidth(20)
				.text("Delivery GMV").setColumnWidth(20)
				.text("Discount").setColumnWidth(20)
				.text("Delivery Discount").setColumnWidth(20)
				.text("Sales").setColumnWidth(20)
				.text("Delivery Sales").setColumnWidth(20)
				.text("Total Cost").setColumnWidth(20)
				.text("EBITDA Percentage").setColumnWidth(20)
				.text("COGS").setColumnWidth(20)
				.text("Delivery COGS").setColumnWidth(20)
				.text("Wastage & Expired").setColumnWidth(20)
				.text("Employee Meal Cost").setColumnWidth(20)
				.text("Consumables - Stationary").setColumnWidth(20)
				.text("Consumables - Utility").setColumnWidth(20)
				.text("Consumables - Uniform").setColumnWidth(20)
				.text("Consumables - Equipment").setColumnWidth(20)
				.text("Consumables - Cutlery").setColumnWidth(20)
				.text("Freight Outward").setColumnWidth(20)
				.text("Electricity").setColumnWidth(20)
				.text("DG - Charges").setColumnWidth(20)
				.text("Water").setColumnWidth(20)
				.text("Telephone").setColumnWidth(20)
				.text("Internet").setColumnWidth(20)
				.text("Commissions - Credit Card").setColumnWidth(20)
				.text("Commissions - Vouchers").setColumnWidth(20)
				.text("Channel Partner Cost").setColumnWidth(20)
				.text("Change Commission").setColumnWidth(20)
				.text("Newspaper").setColumnWidth(20)
				.text("Convenyance").setColumnWidth(20)
				.text("Staff Welfare").setColumnWidth(20)
				.text("Courier").setColumnWidth(20)
				.text("Printing & Stationery").setColumnWidth(20)
				.text("MSP").setColumnWidth(20)
				.text("Delivery Partner Cost").setColumnWidth(20)
				.text("DG - Rent").setColumnWidth(20)
				.text("SCM Rental").setColumnWidth(20)
				.text("EDC Machine").setColumnWidth(20)
				.text("CAM").setColumnWidth(20)
				.text("Rent").setColumnWidth(20)
				.text("System Rent").setColumnWidth(20)
				.text("Insurance").setColumnWidth(20)
				.text("Repair & Maintenance").setColumnWidth(20)
				.text("Manpower").setColumnWidth(20)
				.text("Support-Ops").setColumnWidth(20)
				.text("Support-Hq").setColumnWidth(20)
				.text("COGS%").setColumnWidth(20)
				.text("Delivery COGS%").setColumnWidth(20)
				.text("Consumable%").setColumnWidth(20)
				.text("Facility Variable%").setColumnWidth(20)
				.text("Ops Cost Percentage").setColumnWidth(20)
				.text("Kitchen Cost Percentage").setColumnWidth(20)
				.text("Fixed Rent").setColumnWidth(20)
				.text("Rent Percentage").setColumnWidth(20)
				.text("Misc Exp").setColumnWidth(20)
				.text("Parking charges").setColumnWidth(20)
				.text("Paytm charges").setColumnWidth(20)
				.text("Paytm charges for Delivery").setColumnWidth(20)
				.text("Mobikwik charges").setColumnWidth(20)
				.text("Freecharge charges").setColumnWidth(20);
		

		for (UnitExpense expense : unitExpenseList) {
			sheetCtx.nextRow()
					.skipCell()
					.text(expense.getComments()!= null ? expense.getComments() : "")
					.number(expense.getUnitId())
					.text(expense.getUnitName())
					.number(expense.getTotalTickets())
					.number(expense.getDeliveryTotalTickets())
					.number(expense.getNetTickets())
					.number(expense.getDeliveryNetTickets())
					.number(expense.getGmvAmount())
					.number(expense.getDeliveryGMV())
					.number(expense.getGmvAmount().subtract(expense.getNetSalesAmount()))
					.number(expense.getDeliveryGMV().subtract(expense.getDeliverySales()))
					.number(expense.getNetSalesAmount())
					.number(expense.getDeliverySales())
					.number(expense.getTotalCost())
					.number(expense.getEbitdaPercentage())
					
					// COGS
					.number(expense.getCogs()
							.add(expense.getUnsatifiedCustomerCost())
							.add(expense.getPPECost())
							.add(expense.getMarketingAndSampling()))
					
					// DELIVERY COGS
					.number(expense.getDeliveryCOGS()
							.add(expense.getDeliveryUnsatisfiedCustomerCost())
							.add(expense.getDeliveryPPECost())
							.add(expense.getDeliverySampleingAndMarketingCost()))
					
					.number(expense.getWastageAndExpired())
					.number(expense.getEmployeeMeal())
					
					.number(expense.getConsumablesStationary())
					.number(expense.getConsumablesAndUtilities())
					.number(expense.getConsumablesUniform())
					.number(expense.getConsumablesEquipment())
					.number(expense.getConsumablesCutlery())
					
					.number(expense.getFreightOutward())
					.number(expense.getElectricity())
					.number(expense.getChargesDG())
					.number(expense.getWater())
					.number(expense.getTelephone())
					.number(expense.getInternet())
					
					// Commissions - Credit Card
					.number(expense.getCreditCardCharges()
							.add(expense.getAmexCardCharges()))
					
					// Commissions - Vouchers
					.number(expense.getSodexoCharges()
							.add(expense.getTktRestaurantCharges()))
					
					.number(expense.getChannelPartnerCharges())
					.number(expense.getChangeCommission())
					.number(expense.getNewspaper())
					.number(expense.getConvenyance())
					.number(expense.getStaffWelfare())
					.number(expense.getCourier())
					.number(expense.getPrintingAndStationery())
					.number(expense.getMsp())
					.number(expense.getDeliveryCost())
					.number(expense.getRentDG())
					.number(expense.getScmRental())
					.number(expense.getEdcMachine())
					.number(expense.getCamCharges())
					.number(expense.getRent())
					
					.number(expense.getSystemRent())
					.number(expense.getInsurance())
					
					// Repair & Maintenance
					.number(expense.getRepairAndMaintenanceMinor()
							.add(expense.getCleaningCharges())
							.add(expense.getRepairAndMaintenanceMajor()))
					
					.number(expense.getManpower())
					
					// Support-OPS
					.number(expense.getOpsCostTotal()
							.add(expense.getKitchenCostTotal()))
					
					// Support-HQ
					.number(expense.getCustomerCareCost()
							.add(expense.getMaintenanceTeamCost())
							.add(expense.getTrainingTeamCost())
							.add(expense.getItTeamCost()))
					
					// COGS %
					// ( COGS + Unsatisfied Customer Cost + PPE + Sampling and Marketing Cost ) / Sales
					.number(AppUtils.percentage(
							expense.getCogs()
							.add(expense.getUnsatifiedCustomerCost())
									.add(expense.getPPECost())
							.add(expense.getMarketingAndSampling())
							,expense.getNetSalesAmount()))
					
					// Delivery COGS  %
					.number(AppUtils.percentage(
							expense.getDeliveryCOGS()
							.add(expense.getDeliveryUnsatisfiedCustomerCost())
									.add(expense.getDeliveryPPECost())
							.add(expense.getDeliverySampleingAndMarketingCost())
							,expense.getDeliverySales()))
					
					// Consumable %
					// (ConsumablesStationery + ConsumablesUtility + ConsumablesUniform + ConsumablesEquipment + ConsumablesCutlery) / SALES

					.number(AppUtils.percentage(
							expense.getConsumablesStationary()
							.add(expense.getConsumablesAndUtilities()
							.add(expense.getConsumablesUniform())
							.add(expense.getConsumablesEquipment())
							.add(expense.getConsumablesCutlery())),
							expense.getNetSalesAmount()))
					
					//Facility Variable %
					// (Electricity	+ DGCharges	+ Water + Telephone + Internet ) / Sales
					.number(AppUtils.percentage(
							expense.getElectricity()
							.add(expense.getChargesDG()
							.add(expense.getWater())
							.add(expense.getTelephone())
							.add(expense.getInternet())),
							expense.getNetSalesAmount()))
					
					.number(expense.getOpsCostPercentage())
					.number(expense.getKitchenCostPercentage())
					.number(expense.getFixedRent())
					.number(expense.getRentPercentage())
					.number(expense.getMiscExp())
					.number(expense.getParkingCharges())
					.number(expense.getPaytmCharges())
					.number(expense.getDeliveryPaytmCharges())
					.number(expense.getMobikwikCharges())
					.number(expense.getFreeChargeCharges());
					
		}
	}
}
