/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.external;

import java.util.List;
import java.util.Map;

public interface DataPersister<T> {

	public void persist(List<T> data);

	public Map<Class<?>, DataMapper<T, String>> getDataMappers();

	public void setDataMappers(Map<Class<?>, DataMapper<T, String>> dataMappers);

	public void registerDataMapper(Class<?> clazz, DataMapper<T, String> mapper);

}
