/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.external.impl.pathfinder;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.ReportStatusEvent;
import com.stpl.tech.kettle.domain.model.Settlement;
import com.stpl.tech.kettle.domain.model.TransactionDetail;
import com.stpl.tech.kettle.reports.core.ReportFrequency;
import com.stpl.tech.kettle.reports.external.DataMapper;
import com.stpl.tech.kettle.reports.external.ExternalReportUtil;
import com.stpl.tech.kettle.reports.external.impl.moin.IFieldMapper;
import com.stpl.tech.util.AppUtils;

public class PathFinderDataMapper {

	private static final String BLANK = "";

	public static class PathFinderSegmentHeaderRecord implements DataMapper<OrderInfo, String> {

		private static final String CMD_CODE = "G010";
		private ReportStatusEvent event;
		private ReportFrequency frequency;

		public PathFinderSegmentHeaderRecord(ReportStatusEvent event, ReportFrequency frequency) {
			this.event = event;
			this.frequency = frequency;
		}

		private enum FieldMapper implements IFieldMapper {

			RECORD_TYPE(String.class, true, 4, 1, "G010"),
			FILE_TYPE(String.class, true, 1, 2, "H|I"),
			TENENT_ID(String.class, true, 20, 3, null),
			SITE_ID(String.class, true, 10, 4, null),
			FILE_SEQ_NO(Integer.class, true, 4, 5, null),
			CREATION_DATE(Date.class, true, 8, 6, "yyyyMMdd"),
			CREATION_TIME(Date.class, true, 6, 7, "HHmmss"),
			BUSINESS_DATE(Date.class, true, 8, 8, "yyyyMMdd");

			private FieldMapper(Class<?> type, boolean required, int length, int position, String value) {
				this.type = type;
				this.required = required;
				this.length = length;
				this.position = position;
				this.format = value;
			}

			private final Class<?> type;
			private boolean required;
			private final int length;
			private final int position;
			private final String format;

			@Override
			public boolean isRequired() {
				return required;
			}

			@Override
			public void setRequired(boolean required) {
				this.required = required;
			}

			@Override
			public Class<?> getType() {
				return type;
			}

			@Override
			public int getLength() {
				return length;
			}

			@Override
			public int getPosition() {
				return position;
			}

			@Override
			public String getFormat() {
				return format;
			}
		}

		@Override
		public String toRow(OrderInfo o) throws Exception {
			StringBuilder sb = new StringBuilder(CMD_CODE);
			String s = ReportFrequency.HOURLY.equals(frequency) ? "I" : "H";
			sb.append(ExternalReportUtil.getPaddedValue(s, FieldMapper.FILE_TYPE));
			sb.append(ExternalReportUtil.getPaddedValue(event.getTenantId().trim(), FieldMapper.TENENT_ID));
			sb.append(ExternalReportUtil.getPaddedValue("T1", FieldMapper.SITE_ID));
			sb.append(ExternalReportUtil.getPaddedValue(event.getFileNum(), FieldMapper.FILE_SEQ_NO));
			sb.append(ExternalReportUtil.getValue(event.getStartTime(), FieldMapper.CREATION_DATE));
			sb.append(ExternalReportUtil.getValue(event.getStartTime(), FieldMapper.CREATION_TIME));
			sb.append(ExternalReportUtil.getValue(event.getBusinessDate(), FieldMapper.BUSINESS_DATE));
			ExternalReportUtil.validateTotalLength(sb, FieldMapper.values());
			return sb.toString() + ExternalReportUtil.lineEnd();
		}
	}

	public static class PathFinderTransactionDetailRecord implements DataMapper<OrderInfo, String> {

		private static final String CMD_CODE = "G100";
		private boolean extended = false ;

		public PathFinderTransactionDetailRecord(boolean extended){
			this.extended = extended;
		}
		
		private enum FieldMapper implements IFieldMapper {

			RECORD_TYPE(String.class, true, 4, 1, "G100"),
			POS_TILL(String.class, false, 5, 2, null),
			SHIFT_NO(Integer.class, false, 2, 3, null),
			RECEIPT_NO(String.class, true, 10, 4, null),
			TIMESTAMP(Date.class, true, 12, 5, "yyyyMMddHHmm"),
			INV_AMT(BigDecimal.class, true, 15, 6, null),
			TAX_AMT(BigDecimal.class, false, 15, 7, null),
			DIS_AMT(BigDecimal.class, false, 15, 8, null),
			NET_AMT(BigDecimal.class, true, 15, 9, null),
			RET_AMT(BigDecimal.class, false, 15, 10, null),
			CUST_NAME(String.class, false, 50, 11, null),
			CUST_GENDER(BigDecimal.class, false, 1, 12, "M|F"),
			PASSPORT_NO(String.class, false, 12, 13, null),
			NATION_CODE(String.class, false, 2, 14, "IN"),
			CONNECT_FLIGHT(String.class, false, 1, 15, null),
			FLIGHT_NO(String.class, false, 10, 16, null),
			TICKET_NO(String.class, false, 10, 17, null),
			PAX_TYPE(String.class, false, 1, 18, null),
			TRANSACTION_STATUS(String.class, true, 10, 19, "SALES");
			/* can't extend these as they are not available for other pathfinder cafes
			 * OP_CR(String.class, true, 3, 20, "INR"),
			BC_EXCH(Integer.class, true, 8, 21, null);*/

			private FieldMapper(Class<?> type, boolean required, int length, int position, String format) {
				this.type = type;
				this.required = required;
				this.length = length;
				this.position = position;
				this.format = format;
			}

			private final Class<?> type;
			private boolean required;
			private final int length;
			private final int position;
			private final String format;

			@Override
			public boolean isRequired() {
				return required;
			}

			@Override
			public void setRequired(boolean required) {
				this.required = required;
			}

			@Override
			public Class<?> getType() {
				return type;
			}

			@Override
			public int getLength() {
				return length;
			}

			@Override
			public int getPosition() {
				return position;
			}

			@Override
			public String getFormat() {
				return format;
			}
		}

		@Override
		public String toRow(OrderInfo o) throws Exception {
			StringBuilder sb = new StringBuilder(CMD_CODE);
			TransactionDetail td = o.getOrder().getTransactionDetail();

			BigDecimal netSales = BigDecimal.ZERO;
			BigDecimal discount = BigDecimal.ZERO;
			BigDecimal paidAmount = BigDecimal.ZERO;
			BigDecimal zeroTaxAmount = BigDecimal.ZERO;

			for (OrderItem i : o.getOrder().getOrders()) {
				if (AppUtils.isGiftCard(i.getCode())) {
					zeroTaxAmount = zeroTaxAmount.add(i.getPrice().multiply(new BigDecimal(i.getQuantity())));
				}
			}
			discount = Objects.nonNull(td.getDiscountDetail().getPromotionalOffer())
					? (Objects.nonNull(td.getDiscountDetail().getDiscount())
					? td.getDiscountDetail().getPromotionalOffer().add(td.getDiscountDetail().getDiscount().getValue())
					: td.getDiscountDetail().getPromotionalOffer().add(BigDecimal.ZERO))
					: (Objects.nonNull(td.getDiscountDetail().getDiscount())
					? td.getDiscountDetail().getDiscount().getValue() : discount);
			netSales = Objects.nonNull(td.getTaxableAmount()) ? td.getTaxableAmount().subtract(zeroTaxAmount)
					: netSales;
			paidAmount = Objects.nonNull(o.getOrder().getTransactionDetail().getTax())
					?  netSales.add(o.getOrder().getTransactionDetail().getTax())
					: paidAmount;

			sb.append(ExternalReportUtil.getPaddedValue(BLANK, FieldMapper.POS_TILL));
			sb.append(ExternalReportUtil.getPaddedValue(BLANK, FieldMapper.SHIFT_NO));
			sb.append(ExternalReportUtil.getPaddedValue(o.getOrder().getOrderId(), FieldMapper.RECEIPT_NO));
			sb.append(ExternalReportUtil.getValue(o.getOrder().getBillingServerTime(), FieldMapper.TIMESTAMP));
			sb.append(ExternalReportUtil.getPaddedValue(netSales, FieldMapper.INV_AMT));
			sb.append(ExternalReportUtil.getPaddedValue(o.getOrder().getTransactionDetail().getTax(), FieldMapper.TAX_AMT));
			sb.append(ExternalReportUtil.getPaddedValue(discount, FieldMapper.DIS_AMT));
			sb.append(ExternalReportUtil.getPaddedValue(paidAmount, FieldMapper.NET_AMT));
			sb.append(ExternalReportUtil.getPaddedValue(BigDecimal.ZERO, FieldMapper.RET_AMT));
			sb.append(ExternalReportUtil.getPaddedValue(BLANK, FieldMapper.CUST_NAME));
			sb.append(ExternalReportUtil.getPaddedValue(BLANK, FieldMapper.CUST_GENDER));
			sb.append(ExternalReportUtil.getPaddedValue(BLANK, FieldMapper.PASSPORT_NO));
			sb.append(ExternalReportUtil.getPaddedValue(BLANK, FieldMapper.NATION_CODE));
			sb.append(ExternalReportUtil.getPaddedValue(BLANK, FieldMapper.CONNECT_FLIGHT));
			sb.append(ExternalReportUtil.getPaddedValue(BLANK, FieldMapper.FLIGHT_NO));
			sb.append(ExternalReportUtil.getPaddedValue(BLANK, FieldMapper.TICKET_NO));
			sb.append(ExternalReportUtil.getPaddedValue(BLANK, FieldMapper.PAX_TYPE));
			sb.append(ExternalReportUtil.getPaddedValue("SALES", FieldMapper.TRANSACTION_STATUS));
			ExternalReportUtil.validateTotalLength(sb, FieldMapper.values());
			if(extended) {
				// this is for airport currency and exchange rate
				sb.append("INR00001000");
			}
			return sb.toString() + ExternalReportUtil.lineEnd();
		}
	}

	public static class PathFinderItemDetailRecord implements DataMapper<OrderInfo, String> {

		private static final String CMD_CODE = "G111";
		private boolean extended = false ;
		
		public PathFinderItemDetailRecord(boolean extended){
			this.extended = extended;
		}
		
		private enum FieldMapper implements IFieldMapper {

			RECORD_TYPE(String.class, true, 4, 1, "G111"),
			ITEM_CODE(String.class, true, 16, 2, null),
			ITEM_NAME(String.class, true, 70, 3, null),
			ITEM_QTY(Integer.class, true, 8, 4, null),
			ITEM_PRICE(BigDecimal.class, true, 15, 5, null),
			ITEM_CATG(String.class, true, 40, 6, null),
			TAX_NAME(String.class, false, 40, 7, null), // NUMERIC
			TAX_AMT(BigDecimal.class, true, 15, 8, null),
			TAX_TYPE(BigDecimal.class, true, 1, 9, null),
			DISC_AMT(BigDecimal.class, false, 15, 10, null),
			NET_AMT(String.class, false, 15, 11, null);

			private FieldMapper(Class<?> type, boolean required, int length, int position, String format) {
				this.type = type;
				this.required = required;
				this.length = length;
				this.position = position;
				this.format = format;
			}

			private final Class<?> type;
			private boolean required;
			private final int length;
			private final int position;
			private final String format;

			@Override
			public boolean isRequired() {
				return required;
			}

			@Override
			public void setRequired(boolean required) {
				this.required = required;
			}

			@Override
			public Class<?> getType() {
				return type;
			}

			@Override
			public int getLength() {
				return length;
			}

			@Override
			public int getPosition() {
				return position;
			}

			@Override
			public String getFormat() {
				return format;
			}
		}

		@Override
		public String toRow(OrderInfo o) throws Exception {

			StringBuilder outer = new StringBuilder();
			StringBuilder inner = null;
			for (OrderItem item : o.getOrder().getOrders()) {
				if (AppUtils.isGiftCard(item.getCode())) {
					continue;
				}
				BigDecimal discount = BigDecimal.ZERO;
				if(Objects.nonNull(item.getDiscountDetail())){
					discount = Objects.isNull(item.getDiscountDetail().getDiscount()) ? BigDecimal.ZERO
							: item.getDiscountDetail().getDiscount().getValue();
					discount = Objects.isNull(item.getDiscountDetail().getPromotionalOffer()) ? discount
							: discount.add(item.getDiscountDetail().getPromotionalOffer());
				}
				inner = new StringBuilder(CMD_CODE);
				inner.append(ExternalReportUtil.getPaddedValue(item.getProductId(), FieldMapper.ITEM_CODE));
				inner.append(ExternalReportUtil.getPaddedValue(item.getProductName(), FieldMapper.ITEM_NAME));
				inner.append(ExternalReportUtil.getPaddedValue(item.getQuantity(), FieldMapper.ITEM_QTY));
				inner.append(ExternalReportUtil.getPaddedValue(item.getPrice(), FieldMapper.ITEM_PRICE));
				inner.append(
						ExternalReportUtil.getPaddedValue(item.getProductCategory().getName(), FieldMapper.ITEM_CATG));
				inner.append(ExternalReportUtil.getPaddedValue(0, FieldMapper.TAX_NAME));// NUMERIC
				inner.append(ExternalReportUtil.getPaddedValue(item.getTax(), FieldMapper.TAX_AMT));
				inner.append(ExternalReportUtil.getPaddedValue(BLANK, FieldMapper.TAX_TYPE));
				inner.append(ExternalReportUtil.getPaddedValue(discount, FieldMapper.DISC_AMT));// NUMERIC
				inner.append(ExternalReportUtil.getPaddedValue(
						item.getPrice().multiply(new BigDecimal(item.getQuantity())), FieldMapper.NET_AMT));
				ExternalReportUtil.validateTotalLength(inner, FieldMapper.values());
				if(extended) {
					// this is for airport currency and exchange rate
					inner.append("INR00001000");
				}
				outer.append(inner.toString() + ExternalReportUtil.lineEnd());
				
			}
			return outer.toString();

		}
	}

	public static class PathFinderPaymentDetailRecord implements DataMapper<OrderInfo, String> {

		private static final String CMD_CODE = "G115";
		private boolean extended = false ;
		
		public PathFinderPaymentDetailRecord(boolean extended){
			this.extended = extended;
		}
		
		private enum FieldMapper implements IFieldMapper {

			RECORD_TYPE(String.class, true, 4, 1, "G115"),
			PAYMENT_NAME(String.class, true, 40, 2, "Cash|Other"),
			CURR_CODE(String.class, false, 3, 3, "INR"),
			EXCHANGE_RATE(Integer.class, true, 8, 4, null),
			AMOUNT(BigDecimal.class, true, 15, 5, null);

			private FieldMapper(Class<?> type, boolean required, int length, int position, String format) {
				this.type = type;
				this.required = required;
				this.length = length;
				this.position = position;
				this.format = format;
			}

			private final Class<?> type;
			private boolean required;
			private final int length;
			private final int position;
			private final String format;

			@Override
			public boolean isRequired() {
				return required;
			}

			@Override
			public void setRequired(boolean required) {
				this.required = required;
			}

			@Override
			public Class<?> getType() {
				return type;
			}

			@Override
			public int getLength() {
				return length;
			}

			@Override
			public int getPosition() {
				return position;
			}

			@Override
			public String getFormat() {
				return format;
			}
		}

		@Override
		public String toRow(OrderInfo o) throws Exception {
			StringBuilder outer = new StringBuilder();
			StringBuilder inner = null;
			BigDecimal zeroTaxAmount = BigDecimal.ZERO;
			for (OrderItem i : o.getOrder().getOrders()) {
				if (AppUtils.isGiftCard(i.getCode())) {
					zeroTaxAmount = zeroTaxAmount.add(i.getPrice().multiply(new BigDecimal(i.getQuantity())));
				}
			}

			BigDecimal ratio = BigDecimal.ONE;
			if (BigDecimal.ZERO.compareTo(o.getOrder().getTransactionDetail().getPaidAmount()) != 0) {
				ratio = AppUtils.divideWithScale10(
						o.getOrder().getTransactionDetail().getPaidAmount().subtract(zeroTaxAmount),
						o.getOrder().getTransactionDetail().getPaidAmount());
			}

			if (o.getOrder().getSettlements().isEmpty()) {
				inner = new StringBuilder(CMD_CODE);
				inner.append(ExternalReportUtil.getPaddedValue("CASH", FieldMapper.PAYMENT_NAME));
				inner.append(ExternalReportUtil.getPaddedValue("INR", FieldMapper.CURR_CODE));
				inner.append(ExternalReportUtil.getPaddedValue(1, FieldMapper.EXCHANGE_RATE));// NUMERIC
				inner.append(ExternalReportUtil.getPaddedValue(0, FieldMapper.AMOUNT)); //numeric
				ExternalReportUtil.validateTotalLength(inner, FieldMapper.values());
				if(extended) {
					// this is for airport currency and exchange rate
					inner.append("INR00001000");
				}
				inner.append(ExternalReportUtil.lineEnd());
				outer.append(inner);
			} else {
				for (Settlement settlement : o.getOrder().getSettlements()) {
					inner = new StringBuilder(CMD_CODE);
					inner.append(ExternalReportUtil.getPaddedValue(settlement.getModeDetail().getName(),
							FieldMapper.PAYMENT_NAME));
					inner.append(ExternalReportUtil.getPaddedValue("INR", FieldMapper.CURR_CODE));
					inner.append(ExternalReportUtil.getPaddedValue(1, FieldMapper.EXCHANGE_RATE)); //NUMERIC
					inner.append(ExternalReportUtil.getPaddedValue(
							ratio.multiply(settlement.getAmount()).setScale(0, RoundingMode.HALF_UP),
							FieldMapper.AMOUNT));
					ExternalReportUtil.validateTotalLength(inner, FieldMapper.values());
					if(extended) {
						// this is for airport currency and exchange rate
						inner.append("INR00001000");
					}
					inner.append(ExternalReportUtil.lineEnd());
					outer.append(inner);
				}
			}
			return outer.toString();
		}
	}

	public static class PathFinderSegmentTrailRecord implements DataMapper<OrderInfo, String> {

		private static final String CMD_CODE = "G020";
		List<OrderInfo> orderInfos;

		public PathFinderSegmentTrailRecord(List<OrderInfo> orderInfos) {
			this.orderInfos = orderInfos;
		}

		private enum FieldMapper implements IFieldMapper {

			RECORD_TYPE(String.class, true, 4, 1, "G115"),
			NO_OF_RECORDS(String.class, true, 4, 2, null),
			HASH_TOTAL(String.class, true, 15, 3, null);

			private FieldMapper(Class<?> type, boolean required, int length, int position, String format) {
				this.type = type;
				this.required = required;
				this.length = length;
				this.position = position;
				this.format = format;
			}

			private final Class<?> type;
			private boolean required;
			private final int length;
			private final int position;
			private final String format;

			@Override
			public boolean isRequired() {
				return required;
			}

			@Override
			public void setRequired(boolean required) {
				this.required = required;
			}

			@Override
			public Class<?> getType() {
				return type;
			}

			@Override
			public int getLength() {
				return length;
			}

			@Override
			public int getPosition() {
				return position;
			}

			@Override
			public String getFormat() {
				return format;
			}
		}

		@Override
		public String toRow(OrderInfo o) throws Exception {
			StringBuilder sb = new StringBuilder(CMD_CODE);
			BigDecimal amount = BigDecimal.ZERO;
			Integer totalOrders = 0;
			for (OrderInfo orderInfo : orderInfos) {

				TransactionDetail td = orderInfo.getOrder().getTransactionDetail();
				BigDecimal netSales = BigDecimal.ZERO;
				BigDecimal discount = BigDecimal.ZERO;
				BigDecimal paidAmount = BigDecimal.ZERO;
				BigDecimal zeroTaxAmount = BigDecimal.ZERO;

				int zeroTaxItemCount = 0;
				for (OrderItem i : orderInfo.getOrder().getOrders()) {
					if (AppUtils.isGiftCard(i.getCode())) {
						zeroTaxAmount = zeroTaxAmount.add(i.getPrice().multiply(new BigDecimal(i.getQuantity())));
						zeroTaxItemCount = zeroTaxItemCount + 1;
					}
				}

				if (zeroTaxItemCount >= orderInfo.getOrder().getOrders().size()) {
					continue;
				}

				discount = td.getDiscountDetail().getPromotionalOffer()
						.add(td.getDiscountDetail().getDiscount().getValue());

				netSales = td.getTaxableAmount().subtract(zeroTaxAmount).add(discount);

				paidAmount = netSales.add(orderInfo.getOrder().getTransactionDetail().getTax());
				amount = amount.add(paidAmount);
				totalOrders = totalOrders + 1;
			}
			sb.append(ExternalReportUtil.getPaddedValue(totalOrders, FieldMapper.NO_OF_RECORDS));
			sb.append(ExternalReportUtil.getPaddedValue(amount, FieldMapper.HASH_TOTAL));

			ExternalReportUtil.validateTotalLength(sb, FieldMapper.values());
			return sb.toString() + ExternalReportUtil.lineEnd();

		}
	}
}
