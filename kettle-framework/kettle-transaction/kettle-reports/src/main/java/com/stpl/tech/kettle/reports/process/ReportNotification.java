/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.process;

import java.text.SimpleDateFormat;
import java.util.HashSet;
import java.util.Set;

import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.reports.dao.impl.SettlementReportOutputData;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.notification.EmailNotification;

public class ReportNotification extends EmailNotification {

	private final SettlementReportOutputData output;

	public ReportNotification() {
		this.output = null;
	}

	public ReportNotification(SettlementReportOutputData output) {
		this.output = output;
	}

	public String subject() {
		return (TransactionUtils.isDev(getEnvironmentType()) ? "DEV : " : "") + output.getReportName() + " for : " + output.getUnit().getName() + " for "
				+ new SimpleDateFormat("yyyy-MM-dd").format(output.getBusinessDate());
	}

	public String body() throws EmailGenerationException {

		return "<html><p> " + output.getReportName() + " for : " + output.getUnit().getName() + " for "
				+ new SimpleDateFormat("yyyy-MM-dd").format(output.getBusinessDate()) + "</p></html>";
	}

	public String getFromEmail() {
		return TransactionUtils.isDev(getEnvironmentType()) ? "<EMAIL>" : output.getEmailId();
	}

	public String[] getToEmails() {
		Set<String> s = new HashSet<>();
		if (TransactionUtils.isDev(getEnvironmentType())) {
			return new String[] { "<EMAIL>" };
		}
		s.add(output.getEmailId());
		s.add("<EMAIL>");
		s.add("<EMAIL>");
		if (output.getManagerEmailId() != null) {
			s.add(output.getManagerEmailId());
		}
		s.addAll(output.getToEmails());
		return s.toArray(new String[s.size()]);
	}

	@Override
	public EnvType getEnvironmentType() {
		return output.getEnv();
	}
}
