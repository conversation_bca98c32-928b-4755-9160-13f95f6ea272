/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.external.impl;

import java.util.List;

import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.reports.external.DataProcessor;

public class SalesDataProcessor implements DataProcessor<OrderInfo, OrderInfo> {

	@Override
	public List<OrderInfo> process(List<OrderInfo> context) {
		return context;
	}


}
