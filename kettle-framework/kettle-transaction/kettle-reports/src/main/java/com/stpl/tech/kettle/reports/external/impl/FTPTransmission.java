/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.external.impl;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.List;

import org.apache.commons.io.FileUtils;
import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPClientConfig;
import org.apache.commons.net.ftp.FTPReply;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.stpl.tech.kettle.reports.core.ReportOutput;
import com.stpl.tech.kettle.reports.dao.impl.ReportFileData;
import com.stpl.tech.kettle.reports.external.ReportTransmission;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.notification.AttachmentData;

public class FTPTransmission implements ReportTransmission {

	protected String server = "";
	protected int port = 21;
	protected String username = "";
	protected String password = "";
	protected String directory = "";

	protected ExternalReportOutputData output;

	public FTPTransmission(String server, int port, String username, String password, String directory) {
		super();
		this.server = server;
		this.port = port;
		this.username = username;
		this.password = password;
		this.directory = directory;
	}

	public FTPTransmission(ExternalReportInputData input) {
		this.server = input.getConfigMap().get("FTP_SERVER");
		this.username = input.getConfigMap().get("USERNAME");
		this.password = input.getConfigMap().get("PASSWORD");
		this.port = Integer.parseInt(input.getConfigMap().get("FTP_PORT"));
		this.directory = input.getConfigMap().get("DIRECTORY");
	}

	private static final Logger LOG = LoggerFactory.getLogger(FTPTransmission.class);

	@Override
	public boolean transfer(ReportOutput output) {

		setReportOutput(output);

		if (AppUtils.isDev(getEnvironmentType())) {
			LOG.info("File Transfer cancelled for DEV Enviournment", getServer());
			return false;
		}

		LOG.info("Request for File Transfer to {}", getServer());

		FTPClient ftp = null;
		try {
			ftp = getClient();
			LOG.info("Transmitting {} files", getReportFiles().size());
			for (ReportFileData fileData : getReportFiles()) {
				File file = new File(fileData.getFilePath());
				if (file.exists()) {
					LOG.info("Transmitting {} ", fileData.getFileName());
					InputStream inputStream = new FileInputStream(file);
					ftp.setFileType(FTP.ASCII_FILE_TYPE);
					ftp.enterLocalPassiveMode();
					boolean done = ftp.storeFile(fileData.getFileName(), inputStream);
					inputStream.close();
					if (done) {
						LOG.info("File uploaded successfully.");
					} else {
						SlackNotificationService.getInstance().sendNotification(getEnvironmentType(),
								AppConstants.KETTLE, SlackNotification.SYSTEM_ERRORS,
								"FTP File Upload failed on server " + getServer());
					}
				} else {
					LOG.error("File Not Found: {}", fileData.getFilePath());
				}
			}
			ftp.logout();
		} catch (IOException e) {
			LOG.error("error while transferring file using FTP", e);
			SlackNotificationService.getInstance().sendNotification(getEnvironmentType(), AppConstants.KETTLE,
					SlackNotification.SYSTEM_ERRORS,
					"FTP File Upload failed on server " + getServer() + "\nError: " + e.getMessage());
		} finally {
			if (ftp != null && ftp.isConnected()) {
				try {
					ftp.disconnect();
				} catch (IOException ioe) {
					LOG.error("error while disconnecting FTP", ioe);
				}
			}
		}
		return false;
	}

	@Override
	public boolean transfer(EnvType env, String tempDir, AttachmentData data) {

		if (AppUtils.isDev(env)) {
			LOG.info("File Transfer cancelled for DEV Enviournment", getServer());
			return false;
		}

		LOG.info("Request for File Transfer to {}", getServer());
		String fileName = data.getFileName()
				+ (AppConstants.EXCEL_MIME_TYPE.equals(data.getContentType()) ? ".xlsx" : ".csv");
		FTPClient ftp = null;
		try {
			ftp = getClient();
			LOG.info("Transmitting {} ", data.getFileName());
			File file = new File(tempDir + "/" + fileName);
			if (!file.getParentFile().exists()) {
				file.getParentFile().mkdirs();
			}
			FileUtils.writeByteArrayToFile(file, data.getAttachment());
			ftp.setFileType(AppConstants.EXCEL_MIME_TYPE.equals(data.getContentType()) ? FTP.BINARY_FILE_TYPE
					: FTP.ASCII_FILE_TYPE);
			ftp.enterLocalPassiveMode();
			InputStream inputStream = new FileInputStream(file);
			boolean done = ftp.storeFile(fileName, inputStream);
			inputStream.close();
			if (done) {
				LOG.info("File uploaded successfully.");
			} else {
				SlackNotificationService.getInstance().sendNotification(env, AppConstants.KETTLE,
						SlackNotification.SYSTEM_ERRORS, "FTP File Upload failed on server " + getServer());
			}
			ftp.logout();
			try {
				if (file != null) {
					file.delete();
				}
			} catch (Exception e) {

			}

		} catch (IOException e) {
			LOG.error("error while transferring file using FTP", e);
			SlackNotificationService.getInstance().sendNotification(env, AppConstants.KETTLE,
					SlackNotification.SYSTEM_ERRORS,
					"FTP File Upload failed on server " + getServer() + "\nError: " + e.getMessage());
		} finally {
			if (ftp != null && ftp.isConnected()) {
				try {
					ftp.disconnect();
				} catch (IOException ioe) {
					LOG.error("error while disconnecting FTP", ioe);
				}
			}
		}
		return false;
	}
	
	 public boolean transferJSONFile(EnvType env, File file) {

	        if (AppUtils.isDev(env)) {
	            LOG.info("File Transfer cancelled for DEV Enviournment", getServer());
	            return false;
	        }

	        LOG.info("Request for File Transfer to {}", getServer());
	        String fileName = file.getName();
	        FTPClient ftp = null;
	        try {
	            ftp = getClient();
	            LOG.info("Transmitting {} ", file.getName());
	            if (!file.getParentFile().exists()) {
	                file.getParentFile().mkdirs();
	            }
	            ftp.setFileType(FTP.ASCII_FILE_TYPE);
	            ftp.enterLocalPassiveMode();
	            InputStream inputStream = new FileInputStream(file);
	            boolean done = ftp.storeFile(fileName, inputStream);
	            inputStream.close();
	            if (done) {
	                LOG.info(" JSON File uploaded successfully.");
	            } else {
	            	  LOG.info(" Error Whie JSON File uploading.");
	                SlackNotificationService.getInstance().sendNotification(env, AppConstants.KETTLE,
	                        SlackNotification.SYSTEM_ERRORS, "FTP JSON File Upload failed on server " + getServer());
	            }
	            ftp.logout();
	            try {
	                if (file != null) {
	                    file.delete();
	                }
	            } catch (Exception e) {

	            }

	        } catch (IOException e) {
	            LOG.error("error while transferring JSON file using FTP", e);
	            SlackNotificationService.getInstance().sendNotification(env, AppConstants.KETTLE,
	                    SlackNotification.SYSTEM_ERRORS,
	                    "FTP JSON File Upload failed on server " + getServer() + "\nError: " + e.getMessage());
	        } finally {
	            if (ftp != null && ftp.isConnected()) {
	                try {
	                    ftp.disconnect();
	                } catch (IOException ioe) {
	                    LOG.error("error while disconnecting FTP", ioe);
	                }
	            }
	        }
	        return false;
	    }

	private FTPClient getClient() throws IOException {
		LOG.info("Request for File Transfer to {}", getServer());
		FTPClient ftp = new FTPClient();
		FTPClientConfig config = new FTPClientConfig();
		ftp.configure(config);
		try {
			ftp.connect(getServer(), getPort());
			LOG.info("Connected to " + getServer() + ".");
			LOG.info(ftp.getReplyString());
			int reply = ftp.getReplyCode();

			if (!FTPReply.isPositiveCompletion(reply)) {
				ftp.disconnect();
				LOG.error("FTP server refused connection.");
			}

			ftp.login(getUsername(), getPassword());
			LOG.info("Login Success.");

			if (getDirectory() != null && !"".equals(getDirectory())) {
				ftp.changeWorkingDirectory(getDirectory());
				LOG.info("Directory Changed to : {} ", getDirectory());
			}
		} catch (IOException e) {
			LOG.error("error while getting server connetion", e);
			throw e;
		}
		return ftp;
	}

	public String getDirectory() {
		return directory;
	}

	public List<ReportFileData> getReportFiles() {
		return output.getReportFiles();
	}

	public void setReportOutput(ReportOutput reportOutput) {
		this.output = (ExternalReportOutputData) reportOutput;
	}

	public EnvType getEnvironmentType() {
		return output.getEnv();
	}

	public String getPassword() {
		return password;
	}

	public String getUsername() {
		return username;
	}

	public int getPort() {
		return port;
	}

	public String getServer() {
		return server.trim();
	}

}
