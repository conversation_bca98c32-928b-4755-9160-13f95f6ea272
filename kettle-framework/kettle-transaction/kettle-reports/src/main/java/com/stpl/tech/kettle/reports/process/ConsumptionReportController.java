package com.stpl.tech.kettle.reports.process;

import com.google.common.io.Files;
import com.stpl.tech.kettle.core.cache.MetadataCache;
import com.stpl.tech.kettle.core.service.OrderSearchService;
import com.stpl.tech.kettle.core.service.PosMetadataService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.data.model.UnitClosureDetails;
import com.stpl.tech.kettle.reports.core.ReportType;
import com.stpl.tech.kettle.reports.dao.impl.ReportFileData;
import com.stpl.tech.kettle.reports.dao.impl.SettlementReportInputData;
import com.stpl.tech.kettle.reports.dao.impl.SettlementReportOutputData;
import com.stpl.tech.kettle.reports.dao.impl.UnitConsumptionReportProvider;
import com.stpl.tech.kettle.reports.model.UnitConsumption;
import com.stpl.tech.kettle.reports.view.ConsumptionReportView;
import com.stpl.tech.master.core.exception.DataNotFoundException;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.TaxDataCache;
import com.stpl.tech.master.domain.model.PaymentMode;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.tax.model.Taxation;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.subtlelib.poi.api.workbook.WorkbookContext;
import org.subtlelib.poi.impl.workbook.WorkbookContextFactory;

import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;

public class ConsumptionReportController {

	private static final Logger LOG = LoggerFactory.getLogger(ConsumptionReportController.class);

	private final MasterDataCache masterCache;
	private final TaxDataCache taxDataCache;
	private final EnvironmentProperties props;
	private final UnitConsumptionReportProvider provider;
	private final WorkbookContextFactory ctxFactory = WorkbookContextFactory.useXlsx();
	private WorkbookContext workbookCtx;
	private Map<Unit, UnitConsumption> consumptionMap;

	public ConsumptionReportController(MasterDataCache masterCache, MetadataCache metadataCache,
			TaxDataCache taxDataCache, EnvironmentProperties props) {
		this.masterCache = masterCache;
		this.taxDataCache = taxDataCache;
		this.provider = new UnitConsumptionReportProvider(masterCache, metadataCache);
		this.props = props;
		this.workbookCtx = ctxFactory.createWorkbook();
		this.consumptionMap = new HashMap<>();
	}

	public SettlementReportOutputData processData(PosMetadataService posMetadataService,
			OrderSearchService orderManagementService, Date businessDate, Set<Integer> units)
			throws DataNotFoundException {

		for (UnitClosureDetails detail : posMetadataService.getClosureForBusinessDate(businessDate, units)) {
			processUnitData(new SettlementReportInputData(businessDate,
					masterCache.getUnit(detail.getUnitId()), orderManagementService.getOrderDetailsForDay(
							detail.getUnitId(), detail.getStartOrderId(), detail.getLastOrderId()),
					false, null, null,null), businessDate);
		}
		processAllUnitConsumption(businessDate);
		SettlementReportOutputData output = getOutput(businessDate);
		output.getReportFiles().add(generateConsumptionReport(workbookCtx, businessDate));
		return output;
	}

	private void processAllUnitConsumption(Date businessDate) {
		Set<PaymentMode> paymentModes = new TreeSet<>();
		for (PaymentMode mode : masterCache.getPaymentModes().values()) {
			paymentModes.add(mode);
		}
		Set<Taxation> stateTaxes = new TreeSet<>(
				taxDataCache.getSaleTaxations().get(taxDataCache.getSaleTaxations().keySet().iterator().next()));
		ConsumptionReportView view = new ConsumptionReportView("All Unit Consumption", paymentModes, stateTaxes);
		view.render(workbookCtx, consumptionMap, businessDate);
		ConsumptionReportView complimentaryView = new ConsumptionReportView("Complimentary Consumption", paymentModes,
				stateTaxes);
		complimentaryView.renderComplimentaryView(workbookCtx, consumptionMap, businessDate);
	}

	public void processUnitData(SettlementReportInputData data, Date businessDate) {
		provider.process(data);
		Unit unit = getUnitWithTaxProfileOnly(data.getUnit());
		consumptionMap.put(unit, provider.getConsumptionData());
	}

	private Unit getUnitWithTaxProfileOnly(Unit unit) {
		Unit u = new Unit();
		u.setId(unit.getId());
		u.setName(unit.getName());
		u.getTaxProfiles().addAll(unit.getTaxProfiles());
		u.setLocation(unit.getLocation());
		return u;
	}

	private SettlementReportOutputData getOutput(Date businessDate) {
		SettlementReportOutputData output = new SettlementReportOutputData();
		output.setReportGenerated(true);
		output.setReportGenerationTime(AppUtils.getCurrentTimestamp());
		output.setEnv(props.getEnvironmentType());
		output.setBusinessDate(businessDate);
		return output;
	}

	private ReportFileData generateConsumptionReport(WorkbookContext workbookCtx, Date businessDate) {

		ReportFileData consumptionReport = getConsumptionReportFileData(businessDate);
		try {
			Files.write(workbookCtx.toNativeBytes(), new File(consumptionReport.getFilePath()));
		} catch (IOException e) {
			LOG.error("Error in writing consumption report", e);
			consumptionReport.setGenerated(false);
		}
		return consumptionReport;
	}

	private ReportFileData getConsumptionReportFileData(Date businessDate) {
		String fileName = "ConsumptionReport-" + AppUtils.getDateString(businessDate) + "-"
				+ AppUtils.getCurrentTimeISTStringWithNoColons() + ".xlsx";
		String fileDir = props.getBasePath() + "/consumption/";
		File file = new File(fileDir);
		if (file != null && !file.exists()) {
			boolean madeDirectories = file.mkdirs();
			LOG.error("made directories for the consumption report  {} ::: {}", fileDir, madeDirectories);
		}
		String filePath = fileDir + fileName;
		ReportFileData fileData = new ReportFileData(ReportType.MANAGER, AppConstants.EXCEL_MIME_TYPE, fileName,
				filePath, true);
		return fileData;
	}

}
