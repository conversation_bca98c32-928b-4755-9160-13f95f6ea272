/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.external.impl.moin;

import java.math.BigDecimal;
import java.util.Date;

import com.stpl.tech.kettle.core.notification.OrderInfo;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.ReportStatusEvent;
import com.stpl.tech.kettle.domain.model.Settlement;
import com.stpl.tech.kettle.domain.model.TransactionDetail;
import com.stpl.tech.kettle.reports.external.DataMapper;
import com.stpl.tech.kettle.reports.external.ExternalReportUtil;
import com.stpl.tech.util.AppUtils;

public class MoINOrderDataMapper {

	private static final char DELIMETER = '|';
	private static final String BLANK = "";

	/**
	 * This record contains the information of the shop, POS terminal number and
	 * time when this file was opened / closed. One record with 'OPENED' status
	 * appears at the beginning of the file to indicate the beginning of the
	 * transaction file. One record with 'CLOSED' status appears at the end of
	 * the transaction file to indicate the ending of the transaction file.
	 */
	public static class MoINShopInfoHeader implements DataMapper<OrderInfo, String> {

		private static final int CMD_CODE = 1;
		private ReportStatusEvent event;

		public MoINShopInfoHeader(ReportStatusEvent event) {
			this.event = event;
		}

		private enum FieldMapper implements IFieldMapper {
			FILE_STAT(String.class, true, 6, 1, "OPENED"),
			TENANT_NO(String.class, true, 18, 2, null),
			POS_NO(String.class, true, 6, 3, null),
			RECEIPT_NO(String.class, true, 10, 4, null),
			TRAN_FILE_NO(Integer.class, true, 4, 5, null),
			DATE(Date.class, true, 8, 6, "yyyyMMdd"),
			TIME(String.class, true, 8, 7, "hh:mm:ss"),
			USER_ID(String.class, true, 8, 8, null),
			SALE_DATE(Date.class, true, 8, 9, "yyyyMMdd");

			private FieldMapper(Class<?> type, boolean required, int length, int position, String format) {
				this.type = type;
				this.required = required;
				this.length = length;
				this.position = position;
				this.format = format;
			}

			private final Class<?> type;
			private boolean required;
			private final int length;
			private final int position;
			private final String format;

			@Override
			public boolean isRequired() {
				return required;
			}

			@Override
			public void setRequired(boolean required) {
				this.required = required;

			}

			@Override
			public Class<?> getType() {
				return type;
			}

			@Override
			public int getLength() {
				return length;
			}

			@Override
			public int getPosition() {
				return position;
			}

			@Override
			public String getFormat() {
				return format;
			}
		}

		@Override
		public String toRow(OrderInfo o) throws Exception {
			StringBuilder sb = new StringBuilder("" + CMD_CODE + DELIMETER);
			sb.append("OPENED" + DELIMETER);
			sb.append(ExternalReportUtil.getValue(event.getTenantId(), FieldMapper.TENANT_NO) + DELIMETER);
			sb.append(ExternalReportUtil.getValue(event.getTerminalId(), FieldMapper.POS_NO) + DELIMETER);
			sb.append(ExternalReportUtil.getValue(event.getStartOrderId(), FieldMapper.RECEIPT_NO) + DELIMETER);
			sb.append(ExternalReportUtil.getValue(event.getFileNum(), FieldMapper.TRAN_FILE_NO) + DELIMETER);
			sb.append(ExternalReportUtil.getValue(AppUtils.getCurrentDate(), FieldMapper.DATE) + DELIMETER);
			sb.append(ExternalReportUtil.getValue(AppUtils.getCurrentDate(), FieldMapper.TIME) + DELIMETER);
			sb.append(ExternalReportUtil.getValue(event.getUserId(), FieldMapper.USER_ID) + DELIMETER);
			sb.append(ExternalReportUtil.getValue(event.getBusinessDate(), FieldMapper.SALE_DATE));
			ExternalReportUtil.validateTotalColumns(sb, FieldMapper.values().length, DELIMETER);
			return sb.toString() + ExternalReportUtil.lineEnd();
		}
	}

	/**
	 * This record contains the information of the shop, POS terminal number and
	 * time when this file was opened / closed. One record with 'OPENED' status
	 * appears at the beginning of the file to indicate the beginning of the
	 * transaction file. One record with 'CLOSED' status appears at the end of
	 * the transaction file to indicate the ending of the transaction file.
	 */
	public static class MoINShopInfoFooter implements DataMapper<OrderInfo, String> {

		private static final int CMD_CODE = 1;
		private ReportStatusEvent event;

		public MoINShopInfoFooter(ReportStatusEvent event) {
			this.event = event;
		}

		private enum FieldMapper implements IFieldMapper {
			FILE_STAT(String.class, true, 6, 1, "OPENED"),
			TENANT_NO(String.class, true, 18, 2, null),
			POS_NO(String.class, true, 6, 3, null),
			RECEIPT_NO(String.class, true, 10, 4, null),
			TRAN_FILE_NO(Integer.class, true, 4, 5, null),
			DATE(Date.class, true, 8, 6, "yyyyMMdd"),
			TIME(String.class, true, 8, 7, "hh:mm:ss"),
			USER_ID(String.class, true, 8, 8, null),
			SALE_DATE(Date.class, true, 8, 9, "yyyyMMdd");

			private FieldMapper(Class<?> type, boolean required, int length, int position, String format) {
				this.type = type;
				this.required = required;
				this.length = length;
				this.position = position;
				this.format = format;
			}

			private final Class<?> type;
			private boolean required;
			private final int length;
			private final int position;
			private final String format;

			@Override
			public boolean isRequired() {
				return required;
			}

			@Override
			public void setRequired(boolean required) {
				this.required = required;

			}

			@Override
			public Class<?> getType() {
				return type;
			}

			@Override
			public int getLength() {
				return length;
			}

			@Override
			public int getPosition() {
				return position;
			}

			@Override
			public String getFormat() {
				return format;
			}
		}

		@Override
		public String toRow(OrderInfo o) throws Exception {
			StringBuilder sb = new StringBuilder("" + CMD_CODE + DELIMETER);
			sb.append("CLOSED" + DELIMETER);
			sb.append(ExternalReportUtil.getValue(event.getTenantId(), FieldMapper.TENANT_NO) + DELIMETER);
			sb.append(ExternalReportUtil.getValue(event.getTerminalId(), FieldMapper.POS_NO) + DELIMETER);
			sb.append(ExternalReportUtil.getValue(event.getEndOrderId(), FieldMapper.RECEIPT_NO) + DELIMETER);
			sb.append(ExternalReportUtil.getValue(event.getFileNum(), FieldMapper.TRAN_FILE_NO) + DELIMETER);
			sb.append(ExternalReportUtil.getValue(AppUtils.getCurrentDate(), FieldMapper.DATE) + DELIMETER);
			sb.append(ExternalReportUtil.getValue(AppUtils.getCurrentDate(), FieldMapper.TIME) + DELIMETER);
			sb.append(ExternalReportUtil.getValue(event.getUserId(), FieldMapper.USER_ID) + DELIMETER);
			sb.append(ExternalReportUtil.getValue(event.getBusinessDate(), FieldMapper.SALE_DATE));
			ExternalReportUtil.validateTotalColumns(sb, FieldMapper.values().length, DELIMETER);
			return sb.toString() + ExternalReportUtil.lineEnd();
		}
	}

	/**
	 * This is the header record of each transaction.
	 *
	 */
	public static class MoINHeaderInfo implements DataMapper<OrderInfo, String> {

		private static final int CMD_CODE = 101;

		private enum FieldMapper implements IFieldMapper {

			RECEIPT_NO(String.class, true, 10, 1, null),
			SHIFT_NO(Integer.class, true, 2, 2, null),
			DATE(Date.class, true, 8, 3, "yyyyMMdd"),
			TIME(String.class, true, 8, 4, "hh:mm:ss"),
			USER_ID(String.class, true, 8, 5, null),
			MANUAL_RECEIPT(String.class, false, 10, 6, null),
			REFUND_RECEIPT(String.class, false, 10, 7, null),
			REASON_CODE(String.class, false, 2, 8, null),
			SALESMAN_CODE(String.class, false, 8, 9, null),
			TABLE_NO(Integer.class, false, 2, 10, null),
			CUST_COUNT(String.class, false, 2, 11, null),
			TRAINING(String.class, true, 1, 12, null),
			TRAN_STATUS(String.class, true, 8, 13, null);

			private FieldMapper(Class<?> type, boolean required, int length, int position, String format) {
				this.type = type;
				this.required = required;
				this.length = length;
				this.position = position;
				this.format = format;
			}

			private final Class<?> type;
			private boolean required;
			private final int length;
			private final int position;
			private final String format;

			@Override
			public boolean isRequired() {
				return required;
			}

			@Override
			public void setRequired(boolean required) {
				this.required = required;

			}

			@Override
			public Class<?> getType() {
				return type;
			}

			@Override
			public int getLength() {
				return length;
			}

			@Override
			public int getPosition() {
				return position;
			}

			@Override
			public String getFormat() {
				return format;
			}
		}

		@Override
		public String toRow(OrderInfo o) throws Exception {
			StringBuilder sb = new StringBuilder("" + CMD_CODE + DELIMETER);
			sb.append(ExternalReportUtil.getValue(o.getOrder().getUnitOrderId(), FieldMapper.RECEIPT_NO) + DELIMETER);
			sb.append(ExternalReportUtil.getValue(1, FieldMapper.SHIFT_NO) + DELIMETER);
			sb.append(ExternalReportUtil.getValue(o.getOrder().getBillCreationTime(), FieldMapper.DATE) + DELIMETER);
			sb.append(ExternalReportUtil.getValue(o.getOrder().getBillCreationTime(), FieldMapper.TIME) + DELIMETER);
			sb.append(ExternalReportUtil.getValue(o.getOrder().getEmployeeId(), FieldMapper.USER_ID) + DELIMETER);
			sb.append(ExternalReportUtil.getValue(BLANK, FieldMapper.MANUAL_RECEIPT) + DELIMETER);
			sb.append(ExternalReportUtil.getValue(BLANK, FieldMapper.REFUND_RECEIPT) + DELIMETER);
			sb.append(ExternalReportUtil.getValue(BLANK, FieldMapper.REASON_CODE) + DELIMETER);
			sb.append(ExternalReportUtil.getValue(BLANK, FieldMapper.SALESMAN_CODE) + DELIMETER);
			sb.append(ExternalReportUtil.getValue(BLANK, FieldMapper.TABLE_NO) + DELIMETER);
			sb.append(ExternalReportUtil.getValue(BLANK, FieldMapper.CUST_COUNT) + DELIMETER);
			sb.append(ExternalReportUtil.getValue("N", FieldMapper.TRAINING) + DELIMETER);
			sb.append(ExternalReportUtil.getValue("SALE", FieldMapper.TRAN_STATUS));
			ExternalReportUtil.validateTotalColumns(sb, FieldMapper.values().length, DELIMETER);
			return sb.toString() + ExternalReportUtil.lineEnd();
		}
	}

	/**
	 * This record stores the deposit information of the customer. If customer
	 * information is not entered, this record is not created.
	 *
	 */
	public static class MoINCustomerInfo implements DataMapper<OrderInfo, String> {

		private static final int CMD_CODE = 103;

		private enum FieldMapper implements IFieldMapper {

			CUST_NAME(String.class, true, 30, 1, null),
			PHONE_NO(String.class, false, 20, 2, null),
			ADDRESS_1(String.class, false, 30, 3, null),
			ADDRESS_2(String.class, false, 30, 4, null),
			ADDRESS_3(String.class, false, 30, 5, null);

			private FieldMapper(Class<?> type, boolean required, int length, int position, String format) {
				this.type = type;
				this.required = required;
				this.length = length;
				this.position = position;
				this.format = format;
			}

			private final Class<?> type;
			private boolean required;
			private final int length;
			private final int position;
			private final String format;

			@Override
			public boolean isRequired() {
				return required;
			}

			@Override
			public void setRequired(boolean required) {
				this.required = required;

			}

			@Override
			public Class<?> getType() {
				return type;
			}

			@Override
			public int getLength() {
				return length;
			}

			@Override
			public int getPosition() {
				return position;
			}

			@Override
			public String getFormat() {
				return format;
			}
		}

		@Override
		public String toRow(OrderInfo o) throws Exception {
			StringBuilder sb = new StringBuilder("" + CMD_CODE + DELIMETER);
			if (o.getCustomer().getId() > 5) {
				sb.append(
						ExternalReportUtil.getValue(o.getCustomer().getFirstName(), FieldMapper.CUST_NAME) + DELIMETER);
				sb.append(ExternalReportUtil.getValue(o.getCustomer().getContactNumber(), FieldMapper.PHONE_NO)
						+ DELIMETER);
				sb.append(ExternalReportUtil.getValue("", FieldMapper.ADDRESS_1) + DELIMETER);
				sb.append(ExternalReportUtil.getValue("", FieldMapper.ADDRESS_2) + DELIMETER);
				sb.append(ExternalReportUtil.getValue("", FieldMapper.ADDRESS_3));
				ExternalReportUtil.validateTotalColumns(sb, FieldMapper.values().length, DELIMETER);
				sb.append(ExternalReportUtil.lineEnd());
			}
			return sb.toString();
		}
	}

	/**
	 * This record stores information of the item sold including void items. If
	 * N different items are sold in a transaction, N records are created.
	 *
	 */
	public static class MoINItemInfo implements DataMapper<OrderInfo, String> {

		private static final int CMD_CODE = 111;

		private enum FieldMapper implements IFieldMapper {

			ITEM_CODE(String.class, true, 16, 1, null),
			ITEM_QTY(Integer.class, true, 8, 2, null),
			ORG_PRICE(Integer.class, true, 8, 3, null),
			NEW_PRICE(Integer.class, true, 8, 4, null),
			ITEM_FLAG(String.class, false, 1, 5, null),
			TAX_CODE(String.class, true, 4, 6, null),
			DISCOUNT_CODE(String.class, false, 2, 7, null),
			DISCOUNT_AMT(BigDecimal.class, false, 8, 8, null),
			ITEM_DEPT(String.class, true, 8, 9, null),
			ITEM_CATG(String.class, false, 8, 10, null),
			LABEL_KEYS(String.class, false, 3, 11, null),
			ITEM_COMM(String.class, true, 1, 12, null),
			ITEM_NSALES(BigDecimal.class, true, 10, 13, null),
			DISCOUNT_BY(BigDecimal.class, false, 6, 14, null),
			DISCOUNT_SIGN(String.class, true, 1, 15, null),
			ITEM_STAX(Integer.class, true, 10, 16, null),
			PLU_CODE(String.class, false, 16, 17, null);

			private FieldMapper(Class<?> type, boolean required, int length, int position, String format) {
				this.type = type;
				this.required = required;
				this.length = length;
				this.position = position;
				this.format = format;
			}

			private final Class<?> type;
			private boolean required;
			private final int length;
			private final int position;
			private final String format;

			@Override
			public boolean isRequired() {
				return required;
			}

			@Override
			public void setRequired(boolean required) {
				this.required = required;

			}

			@Override
			public Class<?> getType() {
				return type;
			}

			@Override
			public int getLength() {
				return length;
			}

			@Override
			public int getPosition() {
				return position;
			}

			@Override
			public String getFormat() {
				return format;
			}
		}

		@Override
		public String toRow(OrderInfo o) throws Exception {

			StringBuilder outer = new StringBuilder();
			StringBuilder inner = null;
			for (OrderItem item : o.getOrder().getOrders()) {
				// skipped zero tax items from bills
				if (!AppUtils.isGiftCard(item.getCode())) {
					inner = new StringBuilder("" + CMD_CODE + DELIMETER);
					inner.append(ExternalReportUtil.getValue(item.getItemId(), FieldMapper.ITEM_CODE) + DELIMETER);
					inner.append(ExternalReportUtil.getValue(item.getQuantity(), FieldMapper.ITEM_QTY) + DELIMETER);
					inner.append(ExternalReportUtil.getValue(item.getPrice(), FieldMapper.ORG_PRICE) + DELIMETER);
					inner.append(ExternalReportUtil.getValue(item.getPrice(), FieldMapper.NEW_PRICE) + DELIMETER);
					// ITEM_FLAG -> V = Complimentary Item
					// P = discounted Item, "" = No changes
					String itemFlag = "";
					if (item.getComplimentaryDetail().isIsComplimentary()) {
						itemFlag = "V";
					}
					/*
					 * else if (item.getDiscountDetail().getTotalDiscount() !=
					 * null &&
					 * item.getDiscountDetail().getTotalDiscount().compareTo(
					 * BigDecimal.ZERO) > 0) { itemFlag = "P"; }
					 */
					inner.append(ExternalReportUtil.getValue(itemFlag, FieldMapper.ITEM_FLAG) + DELIMETER);
					inner.append(ExternalReportUtil.getValue(item.getCode(), FieldMapper.TAX_CODE) + DELIMETER);
					// TODO Tax Code ?
					inner.append(ExternalReportUtil.getValue(BLANK, FieldMapper.DISCOUNT_CODE) + DELIMETER);
					inner.append(ExternalReportUtil.getValue(item.getDiscountDetail().getTotalDiscount(),
							FieldMapper.DISCOUNT_AMT) + DELIMETER);
					inner.append(ExternalReportUtil.getValue("F&B", FieldMapper.ITEM_DEPT) + DELIMETER);
					inner.append(ExternalReportUtil.getValue(BLANK, FieldMapper.ITEM_CATG) + DELIMETER);
					inner.append(ExternalReportUtil.getValue(BLANK, FieldMapper.LABEL_KEYS) + DELIMETER);
					inner.append(ExternalReportUtil.getValue("", FieldMapper.ITEM_COMM) + DELIMETER);
					// TODO FLAG VALUE?
					inner.append(ExternalReportUtil.getValue(ExternalReportUtil.getSellingAmount(item),
							FieldMapper.ITEM_NSALES) + DELIMETER);
					inner.append(ExternalReportUtil.getValue(BLANK, FieldMapper.DISCOUNT_BY) + DELIMETER);
					inner.append(ExternalReportUtil.getValue("$", FieldMapper.DISCOUNT_SIGN) + DELIMETER);
					inner.append(ExternalReportUtil.getValue(0, FieldMapper.ITEM_STAX) + DELIMETER);
					// TODO Calculation
					inner.append(ExternalReportUtil.getValue(BLANK, FieldMapper.PLU_CODE));
					ExternalReportUtil.validateTotalColumns(inner, FieldMapper.values().length, DELIMETER);
					outer.append(inner.toString() + ExternalReportUtil.lineEnd());
				}
			}
			return outer.toString();
		}
	}

	/**
	 * This is the footer record of a deposit transaction.
	 *
	 */
	public static class MoINTransactionInfo implements DataMapper<OrderInfo, String> {

		private static final int CMD_CODE = 115;

		private enum FieldMapper implements IFieldMapper {

			FLAG(String.class, true, 1, 1, null),
			TAX_CODE(String.class, true, 4, 2, null),
			TAX_TYPE(String.class, true, 1, 3, "I|E"),
			TAX_EXEMPT(String.class, true, 1, 4, "Y|N"),
			DEP_AMOUNT(BigDecimal.class, false, 10, 5, null),
			TAX_AMT(BigDecimal.class, true, 10, 6, null);

			private FieldMapper(Class<?> type, boolean required, int length, int position, String format) {
				this.type = type;
				this.required = required;
				this.length = length;
				this.position = position;
				this.format = format;
			}

			private final Class<?> type;
			private boolean required;
			private final int length;
			private final int position;
			private final String format;

			@Override
			public boolean isRequired() {
				return required;
			}

			@Override
			public void setRequired(boolean required) {
				this.required = required;

			}

			@Override
			public Class<?> getType() {
				return type;
			}

			@Override
			public int getLength() {
				return length;
			}

			@Override
			public int getPosition() {
				return position;
			}

			@Override
			public String getFormat() {
				return format;
			}
		}

		@Override
		public String toRow(OrderInfo o) throws Exception {
			StringBuilder sb = new StringBuilder("" + CMD_CODE + DELIMETER);
			sb.append(ExternalReportUtil.getValue("C", FieldMapper.FLAG) + DELIMETER);
			sb.append(ExternalReportUtil.getValue("TAX", FieldMapper.TAX_CODE) + DELIMETER);
			sb.append(ExternalReportUtil.getValue("I", FieldMapper.TAX_TYPE) + DELIMETER);
			sb.append(ExternalReportUtil.getValue("N", FieldMapper.TAX_EXEMPT) + DELIMETER);
			sb.append(ExternalReportUtil.getValue(o.getOrder().getTransactionDetail().getPaidAmount(),
					FieldMapper.DEP_AMOUNT) + DELIMETER);// TODO What is deposit
															// Entry
			sb.append(ExternalReportUtil.getValue("", FieldMapper.TAX_AMT));
			ExternalReportUtil.validateTotalColumns(sb, FieldMapper.values().length, DELIMETER);
			return sb.toString() + ExternalReportUtil.lineEnd();
		}
	}

	/**
	 * This is the footer record of a transaction that stores all Tax, Cess,
	 * Service Charge and Discount details. Cess and Service Charge are used for
	 * F&B only.
	 *
	 */
	public static class MoINTransactionFooter implements DataMapper<OrderInfo, String> {

		private static final int CMD_CODE = 121;

		private enum FieldMapper implements IFieldMapper {

			SALES(BigDecimal.class, true, 11, 1, null),
			DISCOUNT(BigDecimal.class, false, 11, 2, null),
			CESS(BigDecimal.class, false, 11, 3, null),
			CHARGES(BigDecimal.class, false, 11, 4, null),
			TAX(BigDecimal.class, false, 11, 5, null),
			TAX_TYPE(String.class, true, 1, 6, "I|E"),
			EXEMPT_GST(String.class, true, 1, 7, "Y|N"),
			DISCOUNT_CODE(String.class, false, 2, 8, null),
			OTHER_CHG(BigDecimal.class, false, 7, 9, null),
			DISCOUNT_PER(BigDecimal.class, false, 6, 10, null),
			ROUNDING_AMT(BigDecimal.class, false, 7, 11, null);

			private FieldMapper(Class<?> type, boolean required, int length, int position, String format) {
				this.type = type;
				this.required = required;
				this.length = length;
				this.position = position;
				this.format = format;
			}

			private final Class<?> type;
			private boolean required;
			private final int length;
			private final int position;
			private final String format;

			@Override
			public boolean isRequired() {
				return required;
			}

			@Override
			public void setRequired(boolean required) {
				this.required = required;

			}

			@Override
			public Class<?> getType() {
				return type;
			}

			@Override
			public int getLength() {
				return length;
			}

			@Override
			public int getPosition() {
				return position;
			}

			@Override
			public String getFormat() {
				return format;
			}

		}

		@Override
		public String toRow(OrderInfo o) throws Exception {
			// Changes as suggested by Mr. Lalit Kumar
			// + Changes after gift cards
			TransactionDetail transDetail = o.getOrder().getTransactionDetail();
			StringBuilder sb = new StringBuilder("" + CMD_CODE + DELIMETER);
			// Total discount applied on order
			// BigDecimal totalDiscount =
			// transDetail.getDiscountDetail().getTotalDiscount()
			// .add(transDetail.getDiscountDetail().getPromotionalOffer() !=
			// null ? transDetail.getDiscountDetail().getPromotionalOffer() :
			// BigDecimal.ZERO);

			// Sales = Amount Paid + Total Discount applied - gift card amount
			BigDecimal zeroTaxAmount = BigDecimal.ZERO;
			for (OrderItem item : o.getOrder().getOrders()) {
				if (AppUtils.isGiftCard(item.getCode())) {
					zeroTaxAmount = zeroTaxAmount.add(ExternalReportUtil.getSellingAmount(item));
				}
			}
			
			BigDecimal sales = transDetail.getPaidAmount().add(transDetail.getDiscountDetail().getDiscount().getValue())
					.subtract(zeroTaxAmount);
			sb.append(ExternalReportUtil.getValue(sales, FieldMapper.SALES) + DELIMETER);
			sb.append(ExternalReportUtil.getValue(transDetail.getDiscountDetail().getDiscount().getValue(),
					FieldMapper.DISCOUNT) + DELIMETER);
			sb.append(ExternalReportUtil.getValue(
					BigDecimal.ZERO, FieldMapper.CESS)
					+ DELIMETER);
			sb.append(ExternalReportUtil.getValue(BigDecimal.ZERO, FieldMapper.CHARGES) + DELIMETER);
			sb.append(ExternalReportUtil.getValue(transDetail.getTax(), FieldMapper.TAX) + DELIMETER);
			sb.append(ExternalReportUtil.getValue("I", FieldMapper.TAX_TYPE) + DELIMETER);
			sb.append(ExternalReportUtil.getValue("Y", FieldMapper.EXEMPT_GST) + DELIMETER);
			sb.append(ExternalReportUtil.getValue(BLANK, FieldMapper.DISCOUNT_CODE) + DELIMETER);
			sb.append(ExternalReportUtil.getValue(BLANK, FieldMapper.OTHER_CHG) + DELIMETER);
			sb.append(ExternalReportUtil.getValue(transDetail.getDiscountDetail().getDiscount().getPercentage(),
					FieldMapper.DISCOUNT_PER) + DELIMETER);
			sb.append(ExternalReportUtil.getValue(transDetail.getRoundOffValue(), FieldMapper.ROUNDING_AMT));
			ExternalReportUtil.validateTotalColumns(sb, FieldMapper.values().length, DELIMETER);
			return sb.toString() + ExternalReportUtil.lineEnd();
		}
	}

	/**
	 * This is the footer record of a transaction that stores the payment
	 * medium, currency code and amount tendered or changed. If payment is made
	 * in N currencies, N records are created.
	 *
	 */
	public static class MoINPaymentInfo implements DataMapper<OrderInfo, String> {

		private static final int CMD_CODE = 131;

		private enum FieldMapper implements IFieldMapper {

			TYPE(String.class, true, 1, 1, null),
			PAYMENT_NAME(String.class, true, 8, 2, null),
			CURR_CODE(String.class, true, 4, 3, null),
			BUY_RATE(BigDecimal.class, true, 12, 4, "1.0"),
			AMOUNT(BigDecimal.class, true, 10, 5, null),
			REMAKRS_1(String.class, false, 24, 6, null),
			REMARKS_2(String.class, false, 24, 7, null),
			BASE_AMT(BigDecimal.class, false, 10, 8, null);

			private FieldMapper(Class<?> type, boolean required, int length, int position, String format) {
				this.type = type;
				this.required = required;
				this.length = length;
				this.position = position;
				this.format = format;
			}

			private final Class<?> type;
			private boolean required;
			private final int length;
			private final int position;
			private final String format;

			@Override
			public boolean isRequired() {
				return required;
			}

			@Override
			public void setRequired(boolean required) {
				this.required = required;

			}

			@Override
			public Class<?> getType() {
				return type;
			}

			@Override
			public int getLength() {
				return length;
			}

			@Override
			public int getPosition() {
				return position;
			}

			@Override
			public String getFormat() {
				return format;
			}

		}

		@Override
		public String toRow(OrderInfo o) throws Exception {
			StringBuilder outer = new StringBuilder();
			StringBuilder inner = null;
			if (o.getOrder().getSettlements().isEmpty()) {
				inner = new StringBuilder("" + CMD_CODE + DELIMETER);
				inner.append(ExternalReportUtil.getValue("T", FieldMapper.TYPE) + DELIMETER);
				inner.append(ExternalReportUtil.getValue("CASH", FieldMapper.PAYMENT_NAME) + DELIMETER);
				inner.append(ExternalReportUtil.getValue("INR", FieldMapper.CURR_CODE) + DELIMETER);
				inner.append(ExternalReportUtil.getValue(BigDecimal.ONE, FieldMapper.BUY_RATE) + DELIMETER);
				inner.append(ExternalReportUtil.getValue(0, FieldMapper.AMOUNT) + DELIMETER);
				inner.append(ExternalReportUtil.getValue(BLANK, FieldMapper.REMAKRS_1) + DELIMETER);
				inner.append(ExternalReportUtil.getValue(BLANK, FieldMapper.REMARKS_2) + DELIMETER);
				inner.append(ExternalReportUtil.getValue(0, FieldMapper.BASE_AMT));
				inner.append(ExternalReportUtil.lineEnd());
				ExternalReportUtil.validateTotalColumns(inner, FieldMapper.values().length, DELIMETER);
				outer.append(inner);
			} else {
				for (Settlement settlement : o.getOrder().getSettlements()) {
					inner = new StringBuilder("" + CMD_CODE + DELIMETER);
					inner.append(ExternalReportUtil.getValue("T", FieldMapper.TYPE) + DELIMETER);
					inner.append(
							ExternalReportUtil.getValue(settlement.getModeDetail().getName(), FieldMapper.PAYMENT_NAME)
									+ DELIMETER);
					inner.append(ExternalReportUtil.getValue("INR", FieldMapper.CURR_CODE) + DELIMETER);
					inner.append(ExternalReportUtil.getValue(BigDecimal.ONE, FieldMapper.BUY_RATE) + DELIMETER);
					inner.append(ExternalReportUtil.getValue(settlement.getAmount(), FieldMapper.AMOUNT) + DELIMETER);
					inner.append(ExternalReportUtil.getValue(BLANK, FieldMapper.REMAKRS_1) + DELIMETER);
					inner.append(ExternalReportUtil.getValue(BLANK, FieldMapper.REMARKS_2) + DELIMETER);
					inner.append(ExternalReportUtil.getValue(settlement.getAmount(), FieldMapper.BASE_AMT));
					inner.append(ExternalReportUtil.lineEnd());
					ExternalReportUtil.validateTotalColumns(inner, FieldMapper.values().length, DELIMETER);
					outer.append(inner);
				}
			}
			return outer.toString();
		}
	}

	/**
	 * This is the footer record of a transaction that stores the payment
	 * medium, currency code and amount tendered or changed. If payment is made
	 * in N currencies, N records are created.
	 *
	 */
	public static class MoINCustomerLoyalityInfo implements DataMapper<OrderInfo, String> {

		private static final int CMD_CODE = 161;

		private enum FieldMapper implements IFieldMapper {

			CARD_NO(String.class, true, 16, 1, null), BONUS_POINT(BigDecimal.class, false, 12, 2, null);

			private FieldMapper(Class<?> type, boolean required, int length, int position, String format) {
				this.type = type;
				this.required = required;
				this.length = length;
				this.position = position;
				this.format = format;
			}

			private final Class<?> type;
			private boolean required;
			private final int length;
			private final int position;
			private final String format;

			@Override
			public boolean isRequired() {
				return required;
			}

			@Override
			public void setRequired(boolean required) {
				this.required = required;

			}

			@Override
			public Class<?> getType() {
				return type;
			}

			@Override
			public int getLength() {
				return length;
			}

			@Override
			public int getPosition() {
				return position;
			}

			@Override
			public String getFormat() {
				return format;
			}
		}

		@Override
		public String toRow(OrderInfo o) throws Exception {
			StringBuilder sb = new StringBuilder("" + CMD_CODE + DELIMETER);
			sb.append(ExternalReportUtil.getValue(o.getCustomer().getId(), FieldMapper.CARD_NO) + DELIMETER);
			sb.append(ExternalReportUtil.getValue(BLANK, FieldMapper.BONUS_POINT));
			// TODO Loyalty not provided
			ExternalReportUtil.validateTotalColumns(sb, FieldMapper.values().length, DELIMETER);
			return sb.toString() + ExternalReportUtil.lineEnd();
		}
	}
}
