/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.dao.impl;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.stpl.tech.kettle.core.data.budget.vo.BudgetDetail;
import com.stpl.tech.kettle.reports.core.ReportOutput;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.util.EnvType;

public class SettlementReportOutputData implements ReportOutput {

	private List<ReportFileData> reportFiles = new ArrayList<ReportFileData>();

	private boolean reportGenerated;

	private boolean reportEmailed;

	private String emailId;

	private String managerEmailId;

	private Set<String> toEmails;

	private int startOrderId;

	private String endOrderId;

	private String reportName;

	private Date reportGenerationTime;

	private Unit unit;

	private EnvType env;

	private Date businessDate;

	private BudgetDetail budgetDetail;

	public boolean isReportGenerated() {
		return reportGenerated;
	}

	public void setReportGenerated(boolean reportGenerated) {
		this.reportGenerated = reportGenerated;
	}

	public boolean isReportEmailed() {
		return reportEmailed;
	}

	public void setReportEmailed(boolean reportEmailed) {
		this.reportEmailed = reportEmailed;
	}

	public String getEmailId() {
		return emailId;
	}

	public void setEmailId(String emailId) {
		this.emailId = emailId;
	}

	public int getStartOrderId() {
		return startOrderId;
	}

	public void setStartOrderId(int startOrderId) {
		this.startOrderId = startOrderId;
	}

	public String getEndOrderId() {
		return endOrderId;
	}

	public void setEndOrderId(String endOrderId) {
		this.endOrderId = endOrderId;
	}

	public Date getReportGenerationTime() {
		return reportGenerationTime;
	}

	public void setReportGenerationTime(Date reportGenerationTime) {
		this.reportGenerationTime = reportGenerationTime;
	}

	public Unit getUnit() {
		return unit;
	}

	public void setUnit(Unit unit) {
		this.unit = unit;
	}

	public EnvType getEnv() {
		return env;
	}

	public void setEnv(EnvType env) {
		this.env = env;
	}

	public Date getBusinessDate() {
		return businessDate;
	}

	public void setBusinessDate(Date businessDate) {
		this.businessDate = businessDate;
	}

	public List<ReportFileData> getReportFiles() {
		return reportFiles;
	}

	public String getManagerEmailId() {
		return managerEmailId;
	}

	public void setManagerEmailId(String managerEmailId) {
		this.managerEmailId = managerEmailId;
	}

	public String getReportName() {
		return reportName;
	}

	public void setReportName(String reportName) {
		this.reportName = reportName;
	}

	public BudgetDetail getBudgetDetail() {
		return budgetDetail;
	}

	public void setBudgetDetail(BudgetDetail budgetDetail) {
		this.budgetDetail = budgetDetail;
	}

	public Set<String> getToEmails() {
		if (toEmails == null) {
			toEmails = new HashSet<>();
		}
		return toEmails;
	}

	public void setToEmails(Set<String> toEmails) {
		this.toEmails = toEmails;
	}

}
