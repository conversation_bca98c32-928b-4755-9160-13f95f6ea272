/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.view;

import com.google.common.base.Optional;
import com.stpl.tech.kettle.core.cache.MetadataCache;
import com.stpl.tech.kettle.domain.model.ActionDetail;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.util.MasterUtil;
import com.stpl.tech.util.AppUtils;
import org.subtlelib.poi.api.sheet.SheetContext;
import org.subtlelib.poi.api.style.Style;
import org.subtlelib.poi.api.workbook.WorkbookContext;

import java.util.Collection;

public class ReprintedBillsView extends ExcelReportView<Collection<Order>> {

	public ReprintedBillsView(MetadataCache cache, MasterDataCache masterCache, String header, String type) {
		super(header, type, cache, masterCache);
	}

	/**
	 * Bill Nos | Bill Amount| Disc. %| Disc. Amt|Generated By |Reason For
	 * Discount
	 * ------------------------------------------------------------------
	 * ------------------------------------------------------
	 */
	public void render(WorkbookContext workbookCtx, Collection<Order> orders) {
		SheetContext sheetCtx = workbookCtx.createSheet(header);
		Style headerStyle = MasterUtil.getHeaderStyle(workbookCtx);
		
		sheetCtx.nextRow().skipCell().setTextStyle(headerStyle).text("Order No.").setColumnWidth(20).text("Bill No.").setColumnWidth(20)
				.text("Bill Amount").setColumnWidth(15).text("Bill Status").setColumnWidth(15)
				.text("Bill Creation Time").setColumnWidth(15).text("Bill Cancellation Time").setColumnWidth(15)
				.text("Reprint Time").setColumnWidth(15).text("Generated By").setColumnWidth(15)
				.text("Reprinted By").setColumnWidth(15).text("Approved By").setColumnWidth(15)
				.text("Reason For " + type).setColumnWidth(50);
		for (Order order : orders) {
			for (ActionDetail reprint : order.getReprints()) {
				sheetCtx.nextRow().skipCell().text(order.getGenerateOrderId()).number(order.getOrderId())
						.number(order.getTransactionDetail().getPaidAmount()).text(order.getStatus().name())
						.text(AppUtils.getTimeWithoutMillisISTString(order.getBillCreationTime()))
						.text(AppUtils.getTimeWithoutMillisISTString(order.getCancellationDetails().getActionTime()))
						.text(AppUtils.getTimeWithoutMillisISTString(reprint.getActionTime()))
						.text(getEmployeeName(order.getEmployeeId()))
						.text(Optional.<String> fromNullable(getEmployeeName(reprint.getGeneratedBy())))
						.text(Optional.<String> fromNullable(getEmployeeName(reprint.getApprovedBy())))
						.text(reprint.getReason());
			}
		}

	}
}
