/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.view;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

import com.stpl.tech.kettle.reports.model.CategoryConsumption;
import com.stpl.tech.kettle.reports.model.ProductConsumption;
import com.stpl.tech.kettle.reports.model.ProductDimensionConsumption;
import com.stpl.tech.kettle.reports.model.ProductPriceConsumption;
import com.stpl.tech.kettle.reports.model.ProductSourceConsumption;
import com.stpl.tech.kettle.reports.model.SubCategoryConsumption;
import com.stpl.tech.kettle.reports.model.UnitConsumption;
import com.stpl.tech.master.util.MasterUtil;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.util.Pair;
import org.subtlelib.poi.api.sheet.SheetContext;
import org.subtlelib.poi.api.style.Style;
import org.subtlelib.poi.api.workbook.WorkbookContext;

public class ConsumptionView
        extends ExcelReportView<Collection<CategoryConsumption<ProductConsumption<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>>>>> {

    private static final Logger LOG = LoggerFactory.getLogger(ConsumptionView.class);

    private BigDecimal totalAmount;
    private boolean isSubCategory;
    private UnitConsumption unitConsumption;

    public ConsumptionView(String header, String type, UnitConsumption consumption, boolean isSubCategory) {
        super(header, type);
        this.unitConsumption = consumption;
        this.totalAmount = consumption.getAmount();
        this.isSubCategory = isSubCategory;
    }

    /**
     * ------------------------------------------------------------------
     * Product <TYPE> | Quantity | Amount | % Share
     * ------------------------------------------------------------------
     */
    /*public void render(WorkbookContext workbookCtx, Collection<T> orders) {
        SheetContext sheetCtx = workbookCtx.createSheet(header);

		sheetCtx.nextRow().skipCell().header("Product " + type).setColumnWidth(20).header("Quantity").setColumnWidth(20)
				.header("Amount").setColumnWidth(20).header("% Share").setColumnWidth(15);

		ColumnTotalsDataRange totalsData = sheetCtx.startColumnTotalsDataRangeFromNextRow();
		if (totalAmount.compareTo(BigDecimal.ZERO) == 0) {
			return;
		}
		for (ConsumptionData order : orders) {
			sheetCtx.nextRow().skipCell().text(order.getName()).number(order.getQuantity()).number(order.getAmount())
					.number(AppUtils.percentage(order.getAmount(), totalAmount));
		}
		if (orders.size() > 0) {
			sheetCtx.nextRow().nextRow().setTotalsDataRange(totalsData).header("Total:").skipCell().total(Formula.SUM).total(Formula.SUM)
					.total(Formula.SUM);
		}

	}*/
    public void render(WorkbookContext workbookCtx,
                       Collection<CategoryConsumption<ProductConsumption<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>>>> orders) {
        SheetContext sheetCtx = workbookCtx.createSheet(header);
        Style headerStyle = MasterUtil.getHeaderStyle(workbookCtx);

        sheetCtx.nextRow().skipCell()
                .setTextStyle(headerStyle)
                .text("Product " + type).setColumnWidth(20)
                .text("Dimension").setColumnWidth(20)
                .text("Quantity").setColumnWidth(20)
                .text("Amount").setColumnWidth(20)
                .text("% Share").setColumnWidth(15)
                .text("% Penetration").setColumnWidth(20);

        Map<String, Map<String, Pair<Integer, BigDecimal>>> categoryMap = new HashMap<>();

        for (CategoryConsumption<ProductConsumption<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>>> order : orders) {
            //String dimension = AppConstants.NO_DIMENSION_STRING;
            if (order.isHasItems() && !order.getName().toLowerCase().contains(AppConstants.OTHERS)) {
                for (SubCategoryConsumption<ProductConsumption<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>>> sub : order.getAllItems()) {
                    // check to not report free addons in manager's report
                    if (sub.isHasItems()) {
                        for (ProductConsumption<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>> prod : sub.getAllItems()) {
                            if (prod.isHasItems()) {
                                for (ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>> source : prod.getAllItems()) {
                                    for (ProductDimensionConsumption<ProductPriceConsumption> dim : source.getAllItems()) {
                                        String name = isSubCategory ? sub.getName() : order.getName();
                                        LOG.info(":::::  Inside add to Map function call for category :: {}", name);
                                        categoryMap = addToMap(categoryMap, name, dim);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        LOG.info(":::::  Size of category Map   ::::: {}", categoryMap.size());
        Map<String, Integer> billCountMap = isSubCategory ? unitConsumption.getBillCountBySubCategory()
                : unitConsumption.getBillCountByCategory();
        for (String category : categoryMap.keySet()) {
            Map<String, Pair<Integer, BigDecimal>> dimensionMap = categoryMap.get(category);
            if (dimensionMap != null) {
                for (String dimension : dimensionMap.keySet()) {
                    Pair<Integer, BigDecimal> values = dimensionMap.get(dimension);
                    if (values != null) {
                        sheetCtx.nextRow().skipCell()
                                .text(category)
                                .text(dimension)
                                .number(values.getFirst())
                                .number(values.getSecond())
                                .number(AppUtils.percentage(values.getSecond(), totalAmount))
                                .number(getCategoryPenetration(category, billCountMap));
                    }
                }
            }
        }
    }

    private Map<String, Map<String, Pair<Integer, BigDecimal>>> addToMap(Map<String, Map<String, Pair<Integer, BigDecimal>>> categoryMap,
                                                                         String name, ProductDimensionConsumption consumption) {

        Map<String, Pair<Integer, BigDecimal>> dimensionMap = categoryMap.get(name);
        if (dimensionMap == null) {
            dimensionMap = new HashMap<>();
        }
        String dimension = consumption.getDimension();
        Pair<Integer, BigDecimal> quantityPair = dimensionMap.get(dimension);
        if (quantityPair == null) {
            quantityPair = Pair.of(consumption.getQuantity(), consumption.getAmount());
        } else {
            Integer totalQuantity = quantityPair.getFirst() + consumption.getQuantity();
            BigDecimal totalAmount = quantityPair.getSecond().add(consumption.getAmount());
            quantityPair = Pair.of(totalQuantity, totalAmount);
        }

        dimensionMap.put(dimension, quantityPair);
        categoryMap.put(name, dimensionMap);

        LOG.info(":::::  Size of category Map inside addToMap method ::::: {}", categoryMap.size());

        return categoryMap;
    }

    private BigDecimal getCategoryPenetration(String category, Map<String, Integer> billCountMap) {
        BigDecimal penetration = BigDecimal.ZERO.setScale(2, RoundingMode.HALF_UP);
        Integer categoryWiseCount = billCountMap.get(category);
        Integer billCount = unitConsumption.getBillCount();
        LOG.info("CategoryWiseCount :::: {} :::::: TotalBillCount :::: {}", categoryWiseCount, billCount);
        if (categoryWiseCount != null) {
            penetration = AppUtils.percentage(categoryWiseCount, billCount);
        }
        LOG.info("Penetration % :::: {}", penetration);
        return penetration;
    }
}
