/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.dao.impl;

import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.core.data.budget.vo.ExpenseField;
import com.stpl.tech.kettle.core.data.budget.vo.RevenueData;
import com.stpl.tech.kettle.core.data.budget.vo.SalesCommissionData;
import com.stpl.tech.kettle.customer.service.CardService;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.domain.model.Settlement;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.offer.service.OfferManagementExternalService;
import com.stpl.tech.master.core.external.offer.service.OfferManagementService;
import com.stpl.tech.master.domain.model.ChannelPartnerDetail;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;

import java.math.BigDecimal;
import java.util.Map;

public class RevenueDataProvider {

    private MasterDataCache masterCache;

    private OfferManagementExternalService offerService;

    private CardService cardService;

    private OfferManagementService offerManagementService;

    private final RevenueData revenue = new RevenueData();

    private final SalesCommissionData salesCommission = new SalesCommissionData();

    public RevenueDataProvider(MasterDataCache cache, OfferManagementExternalService offerService, CardService cardService) {
        this.masterCache = cache;
        this.offerService = offerService;
        this.cardService = cardService;
    }

    public void process(Order order, BigDecimal creditCardPercentage,
                        Map<Integer, ChannelPartnerDetail> channelPartnerDetails) {
        updateRevenueData(order);
        updateSalesCommissionData(order, channelPartnerDetails);
        updateTransactionCommissionData(order, creditCardPercentage);
    }


    private void updateTransactionCommissionData(Order order, BigDecimal creditCardPercentage) {
        if (OrderStatus.CANCELLED.equals(order.getStatus())) {
            return;
        }
        for (Settlement settlement : order.getSettlements()) {
            if (settlement.getMode() == 2) {
                BigDecimal paymentModeComission = AppUtils.multiplyWithScale10(settlement.getAmount(),
                        AppUtils.divideWithScale10(creditCardPercentage == null || creditCardPercentage.intValue() == 0
                                ? masterCache.getAllPaymentModeComission().get(settlement.getMode())
                                : creditCardPercentage, new BigDecimal("100.00")));
                salesCommission.setCreditCardTransactionCharges(
                        salesCommission.getCreditCardTransactionCharges().add(paymentModeComission));
            } else {
                BigDecimal paymentModeComissionPercentage = masterCache.getAllPaymentModeComission()
                        .get(settlement.getMode());
                BigDecimal paymentModeComission = AppUtils.multiplyWithScale10(settlement.getAmount(), AppUtils.divideWithScale10(
                        paymentModeComissionPercentage == null ? BigDecimal.ZERO : paymentModeComissionPercentage,
                        new BigDecimal("100.00")));
                if (settlement.getMode() == 3) {
                    salesCommission.setCreditCardTransactionCharges(
                            salesCommission.getCreditCardTransactionCharges().add(paymentModeComission));
                } else if (settlement.getMode() == 4 || settlement.getMode() == 5 || settlement.getMode() == 7
                        || settlement.getMode() == 8) {
                    salesCommission.setVoucherTransactionCharges(
                            salesCommission.getVoucherTransactionCharges().add(paymentModeComission));
                } else {
                    salesCommission.setWalletsTransactionCharges(
                            salesCommission.getWalletsTransactionCharges().add(paymentModeComission));
                }
            }
        }

    }

    private void updateSalesCommissionData(Order order, Map<Integer, ChannelPartnerDetail> channelPartnerDetails) {
        if (OrderStatus.CANCELLED.equals(order.getStatus())) {
            return;
        }
        if (TransactionUtils.isCODOrder(order.getSource())) {
            ChannelPartnerDetail partner = channelPartnerDetails.get(order.getChannelPartner());
            BigDecimal channelPartnerCommission = AppUtils.multiplyWithScale10(order.getTransactionDetail().getTaxableAmount(),
                    AppUtils.divideWithScale10(partner.getCommission() == null ? BigDecimal.ZERO : partner.getCommission(),
                            new BigDecimal("100.00")));
            BigDecimal channelPartnerCommissionTax = AppUtils.multiplyWithScale10(channelPartnerCommission,
                    AppUtils.divideWithScale10(partner.getTaxRate() == null ? BigDecimal.ZERO : partner.getTaxRate(),
                            new BigDecimal("100.00")));
            channelPartnerCommission = AppUtils.add(channelPartnerCommission, channelPartnerCommissionTax);
            salesCommission.setCommissionChannelPartners(
                    salesCommission.getCommissionChannelPartners().add(channelPartnerCommission));
        }

    }

    private void updateRevenueData(Order order) {
        if (OrderStatus.CANCELLED.equals(order.getStatus())) {
            return;
        }
        if (TransactionUtils.isPaidEmployeeMeal(order)) {
            revenue.setEmployeeMealSales(
                    revenue.getEmployeeMealSales().add(order.getTransactionDetail().getTaxableAmount()));
            revenue.setEmployeeMealTicket(revenue.getEmployeeMealTicket() + 1);
            revenue.setEmployeeMealGmv(AppUtils.add(revenue.getEmployeeMealGmv(), order.getTransactionDetail().getTaxableAmount()));
        } else if (TransactionUtils.isRegularOrder(order)) {
            BigDecimal giftCardRedemption = BigDecimal.ZERO;
            for (Settlement settlement : order.getSettlements()) {
                if (settlement.getMode() == AppConstants.PAYMENT_MODE_GIFT_CARD) {
                    giftCardRedemption = giftCardRedemption.add(settlement.getAmount());
                }
            }
            BigDecimal giftCardRedemptionSale = AppUtils.multiply(giftCardRedemption,
                    order.getTransactionDetail().getTaxableAmount());
            giftCardRedemptionSale = AppUtils.divide(giftCardRedemptionSale,
                    order.getTransactionDetail().getPaidAmount());
            GiftCardDetail gcDetail = getGiftCardDetail(order);
            calculateGiftCardDetailAmount(order.getOrderId(), gcDetail);
            BigDecimal actualSales = AppUtils.subtract(order.getTransactionDetail().getTaxableAmount(),
                    gcDetail.gcAmount);
            BigDecimal actualGMV = AppUtils.subtract(order.getTransactionDetail().getTotalAmount(), gcDetail.gcAmount);
            revenue.setRevenue(AppUtils.add(revenue.getRevenue(), actualSales));
            revenue.setGmv(AppUtils.add(revenue.getGmv(), actualGMV));
            revenue.setDiscount(AppUtils.subtract(revenue.getGmv(), revenue.getRevenue()));
            if (!gcDetail.onlyGiftCard) {
                revenue.setTicket(revenue.getTicket() + 1);
            }
            if (revenue.getTicket() > 0) {
                revenue.setApc(AppUtils.divide(revenue.getRevenue(), new BigDecimal(revenue.getTicket())));
            }
            if (TransactionUtils.isCODOrder(order.getSource())) {
                revenue.setDeliverySales(AppUtils.add(revenue.getDeliverySales(), actualSales));
                revenue.setDeliveryGmv(AppUtils.add(revenue.getDeliveryGmv(), actualGMV));
                if (!gcDetail.onlyGiftCard) {
                    revenue.setDeliveryTicket(revenue.getDeliveryTicket() + 1);
                }
                if (revenue.getDeliveryTicket() > 0) {
                    revenue.setDeliveryApc(
                            AppUtils.divide(revenue.getDeliverySales(), new BigDecimal(revenue.getDeliveryTicket())));
                }
                revenue.setDeliveryDiscount(AppUtils.subtract(revenue.getDeliveryGmv(), revenue.getDeliverySales()));
            } else {
                revenue.setDineInSales(AppUtils.add(revenue.getDineInSales(), actualSales));
                revenue.setDineInGmv(AppUtils.add(revenue.getDineInGmv(), actualGMV));
                if (!gcDetail.onlyGiftCard) {
                    revenue.setDineInTicket(revenue.getDineInTicket() + 1);
                }
                if (revenue.getDineInTicket() > 0) {
                    revenue.setDineInApc(
                            AppUtils.divide(revenue.getDineInSales(), new BigDecimal(revenue.getDineInTicket())));
                }
                revenue.setDineInDiscount(AppUtils.subtract(revenue.getDineInGmv(), revenue.getDineInSales()));
            }

            setTotalDiscount(revenue, AppUtils.subtract(actualGMV, actualSales), order.getOfferCode(), TransactionUtils.isCODOrder(order.getSource()),
                    masterCache.getChannelPartner(order.getChannelPartner()).getCode());
            if (TransactionUtils.isCODOrder(order.getSource())) {
                revenue.setDeliveryGiftCardSale(revenue.getDeliveryGiftCardSale().add(gcDetail.gcAmount));
                revenue.setDeliveryGiftCardRedemption(
                        revenue.getDeliveryGiftCardRedemption().add(giftCardRedemptionSale));
                revenue.setDeliveryGiftCardNetSale(
                        revenue.getDeliveryGiftCardSale().subtract(revenue.getDeliveryGiftCardRedemption()));
                revenue.setDeliveryNetSales(
                        AppUtils.add(revenue.getDeliverySales(), revenue.getDeliveryGiftCardNetSale()));

            } else {
                revenue.setDineInGiftCardSale(revenue.getDineInGiftCardSale().add(gcDetail.gcAmount));
                revenue.setDineInGiftCardRedemption(revenue.getDineInGiftCardRedemption().add(giftCardRedemptionSale));
                revenue.setDineInGiftCardNetSale(
                        revenue.getDineInGiftCardSale().subtract(revenue.getDineInGiftCardRedemption()));
                revenue.setDineInNetSales(AppUtils.add(revenue.getDineInSales(), revenue.getDineInGiftCardNetSale()));

            }
            revenue.setGiftCardSale(revenue.getGiftCardSale().add(gcDetail.gcAmount));
            revenue.setGiftCardDiscount(revenue.getGiftCardDiscount().add(gcDetail.gcDiscount));
            revenue.setGiftCardRedemption(revenue.getGiftCardRedemption().add(giftCardRedemptionSale));
            revenue.setGiftCardNetSale(revenue.getGiftCardSale().subtract(revenue.getGiftCardRedemption()));
            revenue.setNetSales(AppUtils.add(revenue.getRevenue(), revenue.getGiftCardNetSale()));
            revenue.setNetRevenue(AppUtils.add(revenue.getRevenue(), revenue.getEmployeeMealSales()));

        }

    }


    private void calculateGiftCardDetailAmount(Integer orderId, GiftCardDetail gcDetail) {
        if (gcDetail.gcAmount.compareTo(new BigDecimal("0.00")) > 0) {
            gcDetail.gcDiscount = cardService.getGiftCardOffer(orderId);
        }
    }

    private ExpenseField.DiscounRecordCategory getCategory(BigDecimal discount, String offerCode, boolean isDelivery, String partnerCode) {
        ExpenseField.DiscounRecordCategory cat = ExpenseField.DiscounRecordCategory.NONE;
        if (discount != null && discount.compareTo(BigDecimal.ZERO) > 0) {
            cat = ExpenseField.DiscounRecordCategory.MARKETING;
            if (offerCode != null) {
                String category = offerService.getOfferBudgerCategory(offerCode);
                if (category != null) {
                    try {
                        cat = ExpenseField.DiscounRecordCategory.valueOf(category);
                    } catch (Exception e) {
                        return cat;
                    }
                } else {
                    if (isDelivery) {
                        try {
                            offerService.createPartnerDiscountCoupon(AppConstants.CHANNEL_PARTNER_DEFAULT_COUPON, partnerCode);
                        } catch (Exception e) {
                            return cat;
                        }
                        cat = getCategory(discount, offerCode, false, partnerCode);
                    }

                }
            }
        }
        return cat;
    }

    private void setTotalDiscount(RevenueData revenue2, BigDecimal totalDiscount, String offerCode,
                                  boolean isDelivery, String partnerCode) {
        ExpenseField.DiscounRecordCategory cat = getCategory(totalDiscount, offerCode, isDelivery, partnerCode);
        switch (cat) {
            case NONE:
                break;
            case MARKETING:
                revenue.setDiscountMarketing(AppUtils.add(revenue.getDiscountMarketing(), totalDiscount));
                if (isDelivery) {
                    revenue.setDeliveryDiscountMarketing(
                            AppUtils.add(revenue.getDeliveryDiscountMarketing(), totalDiscount));
                } else {
                    revenue.setDineInDiscountMarketing(AppUtils.add(revenue.getDineInDiscountMarketing(), totalDiscount));
                }
                break;
            case BUSINESS_DEVELOPMENT:
                revenue.setDiscountBd(AppUtils.add(revenue.getDiscountBd(), totalDiscount));
                if (isDelivery) {
                    revenue.setDeliveryDiscountBd(AppUtils.add(revenue.getDeliveryDiscountBd(), totalDiscount));
                } else {
                    revenue.setDineInDiscountBd(AppUtils.add(revenue.getDineInDiscountBd(), totalDiscount));
                }
                break;
            case OPERATIONS:
                revenue.setDiscountOps(AppUtils.add(revenue.getDiscountOps(), totalDiscount));
                if (isDelivery) {
                    revenue.setDeliveryDiscountOps(AppUtils.add(revenue.getDeliveryDiscountOps(), totalDiscount));
                } else {
                    revenue.setDineInDiscountOps(AppUtils.add(revenue.getDineInDiscountOps(), totalDiscount));
                }
                break;
            case EMPLOYEE_FICO:
                revenue.setDiscountEmployeeFico(AppUtils.add(revenue.getDiscountEmployeeFico(), totalDiscount));
                if (isDelivery) {
                    revenue.setDeliveryDiscountEmployeeFico(
                            AppUtils.add(revenue.getDeliveryDiscountEmployeeFico(), totalDiscount));
                } else {
                    revenue.setDineInDiscountEmployeeFico(
                            AppUtils.add(revenue.getDineInDiscountEmployeeFico(), totalDiscount));
                }
                break;
            case LOYAL_TEA:
                revenue.setDiscountLoyalty(AppUtils.add(revenue.getDiscountLoyalty(), totalDiscount));
                if (isDelivery) {
                    revenue.setDeliveryDiscountLoyalty(AppUtils.add(revenue.getDeliveryDiscountLoyalty(), totalDiscount));
                } else {
                    revenue.setDineInDiscountLoyalty(AppUtils.add(revenue.getDineInDiscountLoyalty(), totalDiscount));
                }
                break;
            case EMP_DISCOUNT_LOYAL_TEA:
                revenue.setEmpDiscountLoyalty(AppUtils.add(revenue.getEmpDiscountLoyalty(), totalDiscount));
                break;
            case EMP_DISCOUNT_MARKETING:
                revenue.setEmpDiscountMarketing(AppUtils.add(revenue.getEmpDiscountMarketing(), totalDiscount));
                break;
            case EMP_DISCOUNT_OPS:
                revenue.setEmpDiscountOps(AppUtils.add(revenue.getEmpDiscountOps(), totalDiscount));
                break;
            case EMP_DISCOUNT_BD:
                revenue.setEmpDiscountBd(AppUtils.add(revenue.getEmpDiscountBd(), totalDiscount));
                break;
            case EMP_DISCOUNT_EMPLOYEE_FICO:
                revenue.setEmpDiscountEmployeeFico(AppUtils.add(revenue.getEmpDiscountEmployeeFico(), totalDiscount));
                break;
            default:
                revenue.setDiscountMarketing(AppUtils.add(revenue.getDiscountMarketing(), totalDiscount));
                if (isDelivery) {
                    revenue.setDeliveryDiscountMarketing(
                            AppUtils.add(revenue.getDeliveryDiscountMarketing(), totalDiscount));
                } else {
                    revenue.setDineInDiscountMarketing(AppUtils.add(revenue.getDineInDiscountMarketing(), totalDiscount));
                }
                break;
        }
    }

    private class GiftCardDetail {
        boolean onlyGiftCard = true;
        BigDecimal gcAmount = new BigDecimal(0d);
        BigDecimal gcDiscount = new BigDecimal(0d);
    }

    /**
     * @param order
     * @return
     */
    private GiftCardDetail getGiftCardDetail(Order order) {
        GiftCardDetail detail = new GiftCardDetail();
        for (OrderItem item : order.getOrders()) {
            if (!AppUtils.isGiftCard(item.getCode())) {
                detail.onlyGiftCard = false;
            } else {
                detail.gcAmount = AppUtils.add(detail.gcAmount, item.getAmount());
            }
        }
        return detail;
    }

    public RevenueData getRevenue() {
        return revenue;
    }

    public SalesCommissionData getSalesCommission() {
        return salesCommission;
    }

}
