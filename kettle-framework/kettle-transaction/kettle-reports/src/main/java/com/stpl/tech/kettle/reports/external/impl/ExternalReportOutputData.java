/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.external.impl;

import com.stpl.tech.kettle.reports.core.ReportOutput;
import com.stpl.tech.kettle.reports.dao.impl.ReportFileData;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.util.EnvType;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

public class ExternalReportOutputData implements ReportOutput {

	private List<ReportFileData> reportFiles = new ArrayList<ReportFileData>();
	
	private Map<String,String> configMap;

	private boolean reportGenerated;

	private boolean reportEmailed;

	private String emailId;

	private int startOrderId;

	private int endOrderId;

	private Date reportGenerationTime;

	private Unit unit;

	private EnvType env;

	private Date businessDate;

	public boolean isReportGenerated() {
		return reportGenerated;
	}

	public void setReportGenerated(boolean reportGenerated) {
		this.reportGenerated = reportGenerated;
	}

	public boolean isReportEmailed() {
		return reportEmailed;
	}

	public void setReportEmailed(boolean reportEmailed) {
		this.reportEmailed = reportEmailed;
	}

	public String getEmailId() {
		return emailId;
	}

	public void setEmailId(String emailId) {
		this.emailId = emailId;
	}

	public int getStartOrderId() {
		return startOrderId;
	}

	public void setStartOrderId(int startOrderId) {
		this.startOrderId = startOrderId;
	}

	public int getEndOrderId() {
		return endOrderId;
	}

	public void setEndOrderId(int endOrderId) {
		this.endOrderId = endOrderId;
	}

	public Date getReportGenerationTime() {
		return reportGenerationTime;
	}

	public void setReportGenerationTime(Date reportGenerationTime) {
		this.reportGenerationTime = reportGenerationTime;
	}

	public Unit getUnit() {
		return unit;
	}

	public void setUnit(Unit unit) {
		this.unit = unit;
	}

	public EnvType getEnv() {
		return env;
	}

	public void setEnv(EnvType env) {
		this.env = env;
	}

	public Date getBusinessDate() {
		return businessDate;
	}

	public void setBusinessDate(Date businessDate) {
		this.businessDate = businessDate;
	}

	public List<ReportFileData> getReportFiles() {
		return reportFiles;
	}

	public Map<String,String> getConfigMap() {
		return configMap;
	}

	public void setConfigMap(Map<String,String> configMap) {
		this.configMap = configMap;
	}

}
