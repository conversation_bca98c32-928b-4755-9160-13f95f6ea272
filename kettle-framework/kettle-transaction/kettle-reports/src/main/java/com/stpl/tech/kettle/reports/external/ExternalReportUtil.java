/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.external;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;

import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.reports.external.impl.moin.IFieldMapper;
import com.stpl.tech.util.AppUtils;

public class ExternalReportUtil {

	public static String getValue(BigDecimal obj, IFieldMapper mapper) throws ExternalReportDataFormatException {

		if (obj != null && obj.toString().length() > mapper.getLength()) {
			throw new ExternalReportDataFormatException(
					String.format("Length is greater then Max length %d", mapper.getLength()));
		}
		if (obj == null && mapper.isRequired()) {
			throw new ExternalReportDataFormatException(String.format("Required Field is Missing: %s", mapper.name()));
		}
		return obj != null ? obj.setScale(2, BigDecimal.ROUND_HALF_UP).toString() : "";
	}

	public static String getValue(Integer obj, IFieldMapper mapper) throws ExternalReportDataFormatException {

		if (obj != null && obj.toString().length() > mapper.getLength()) {
			throw new ExternalReportDataFormatException(
					String.format("Length is greater then Max length %d", mapper.getLength()));
		}
		if (obj == null && mapper.isRequired()) {
			throw new ExternalReportDataFormatException(String.format("Required Field is Missing: %s", mapper.name()));
		}
		return obj != null ? obj.toString() : "";
	}

	public static String getValue(String obj, IFieldMapper mapper) throws ExternalReportDataFormatException {

		if (obj == null && mapper.isRequired()) {
			throw new ExternalReportDataFormatException(String.format("Required Field is Missing: %s", mapper.name()));
		}
		if (obj != null && obj.toString().length() > mapper.getLength()) {
			obj = obj.substring(0, mapper.getLength() - 1);
		}
		return obj != null ? obj : "";
	}

	public static String getValue(Date date, IFieldMapper mapper) {
		return date != null ? new SimpleDateFormat(mapper.getFormat()).format(date) : "";
	}

	public static boolean validateTotalColumns(StringBuilder sb, int noOfColumns, char delimeter)
			throws ExternalReportDataFormatException {
		boolean result = false;
		String s = sb.toString();
		int counter = 0;
		for (int i = 0; i < s.length(); i++) {
			if (s.charAt(i) == delimeter) {
				counter++;
			}
		}
		if (counter == noOfColumns) {
			result = true;
		} else {
			throw new ExternalReportDataFormatException("Columns Missing from External Sales Report");
		}
		return result;
	}

	public static String lineEnd() {
		return "\r\n";
	}

	public static int calculateMoINFileNumber(int oldfileNum) {
		if (oldfileNum == 9999) {
			return 1;
		}
		return oldfileNum + 1;
	}


	public static BigDecimal getSellingAmount(OrderItem item) {

		BigDecimal amount = item.getPrice().multiply(new BigDecimal(item.getQuantity()));
		amount = AppUtils.subtract(amount, item.getDiscountDetail().getDiscount().getValue());
		amount = AppUtils.subtract(amount, item.getDiscountDetail().getPromotionalOffer());

		return amount;
	}

	public static void validateTotalLength(StringBuilder sb, IFieldMapper[] values)
			throws ExternalReportDataFormatException {
		int length = 0;
		for (IFieldMapper f : values) {
			length = length + f.getLength();
		}
		if (length != sb.length()) {
			throw new ExternalReportDataFormatException("Columns Missing from External Sales Report");
		}
	}

	public static String getPaddedValue(String string, IFieldMapper mapper) throws ExternalReportDataFormatException {
		String value = getValue(string, mapper);
		return padLeft(value, mapper.getLength());
	}

	public static String getPaddedValue(Integer integer, IFieldMapper mapper) throws ExternalReportDataFormatException {
		String value = getValue(integer, mapper);
		return padLeft(value, mapper.getLength()).replace(" ", "0");
	}

	public static String getPaddedValue(BigDecimal decimal, IFieldMapper mapper)
			throws ExternalReportDataFormatException {
		String value = getValue(decimal, mapper).replace(".", "");
		return padLeft(value, mapper.getLength()).replace(" ", "0");
	}

	private static String padRight(String s, int n) {
		return n > 0 ? String.format("%1$-" + n + "s", s) : s;
	}

	private static String padLeft(String s, int n) {
		return n > 0 ? String.format("%1$" + n + "s", s) : s;
	}

	public static String padLeft(Integer s, int n) {
		return n > 0 ? String.format("%1$" + n + "s", s).replace(" ", "0") : String.valueOf(s);
	}

	public static void main(String args[]) throws Exception {
		System.out.println(padRight("Howto", 20) + "*");
		System.out.println(padLeft("Howto", 20) + "*");
	}
}
