/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.view;

import com.stpl.tech.kettle.reports.model.CategoryConsumption;
import com.stpl.tech.kettle.reports.model.ProductConsumption;
import com.stpl.tech.kettle.reports.model.ProductDimensionConsumption;
import com.stpl.tech.kettle.reports.model.ProductPriceConsumption;
import com.stpl.tech.kettle.reports.model.ProductSourceConsumption;
import com.stpl.tech.kettle.reports.model.SubCategoryConsumption;
import com.stpl.tech.master.util.MasterUtil;
import com.stpl.tech.util.AppConstants;
import org.subtlelib.poi.api.sheet.SheetContext;
import org.subtlelib.poi.api.style.Style;
import org.subtlelib.poi.api.totals.ColumnTotalsDataRange;
import org.subtlelib.poi.api.totals.Formula;
import org.subtlelib.poi.api.workbook.WorkbookContext;

import java.util.Collection;

public class ProductConsumptionView extends
		ExcelReportView<Collection<CategoryConsumption<ProductConsumption<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>>>>> {


	public ProductConsumptionView(String header, String type) {
		super(header, type);
	}

	/**
	 * Bill Nos | Bill Amount| Disc. %| Disc. Amt|Generated By |Reason For
	 * Discount
	 * ------------------------------------------------------------------
	 * ------------------------------------------------------
	 */

//	public CellStyle getStyle(Workbook workbook) {
//		Style headerStyle = workboo
//		Font font = workbook.createFont();
//		font.setFontHeightInPoints((short) 12);
//		font.setBold(true);
//		font.setColor(IndexedColors.WHITE.getIndex());
//
//		headerStyle.setFont(font);
//		headerStyle.setFillForegroundColor(IndexedColors.SEA_GREEN.getIndex());
//		headerStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
//		headerStyle.setBorderBottom(BorderStyle.THIN);
//		headerStyle.setBorderLeft(BorderStyle.THIN);
//		headerStyle.setBorderRight(BorderStyle.THIN);
//		headerStyle.setBorderTop(BorderStyle.THIN);
//
//		return headerStyle;
//	}
//
//	public RowContext customHeader(String text, RowContext rowContext, int i, WorkbookContext workbookContext) {
//		Row row = rowContext.getNativeRow();
//		Cell cell = row.createCell(i);
//		CellStyle cellStyle = cell.getCellStyle();
//		cell.setCellStyle(getHeaderStyle(workbookContext.toNativeWorkbook()));
//		return rowContext;
//	}

	public void render(WorkbookContext workbookCtx,
			Collection<CategoryConsumption<ProductConsumption<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>>>> orders) {
		SheetContext sheetCtx = workbookCtx.createSheet(header);

		Style headerStyle = MasterUtil.getHeaderStyle(workbookCtx);

		sheetCtx.nextRow().skipCell().setTextStyle(headerStyle).text("Categories").setColumnWidth(20).text("Sub Categories")
				.setColumnWidth(20).text("Products").setColumnWidth(20).text("Dimension").setColumnWidth(15)
				.text("Order Source").setColumnWidth(15).text("Price").setColumnWidth(15).text("Quantity")
				.setColumnWidth(15).text("Combo Quantity")
				.setColumnWidth(15).text("Compl. Quantity").setColumnWidth(15).text("Amount").setColumnWidth(15);

		ColumnTotalsDataRange totalsData = sheetCtx.startColumnTotalsDataRangeFromNextRow();
		for (CategoryConsumption<ProductConsumption<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>>> order : orders) {
			for (SubCategoryConsumption<ProductConsumption<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>>> sub : order
					.getAllItems()) {
				for (ProductConsumption<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>> prod : sub
						.getAllItems()) {
					for (ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>> source : prod
							.getAllItems()) {
						for (ProductDimensionConsumption<ProductPriceConsumption> dim : source.getAllItems()) {
							for (ProductPriceConsumption price : dim.getAllItems()) {
								sheetCtx.nextRow().skipCell().text(order.getName()).text(sub.getName())
										.text(prod.getName())
										.text((AppConstants.NO_DIMENSION_STRING.equals(dim.getDimension()) ? ""
												: " " + dim.getDimension()))
										.text(source.getName()).number(price.getPrice()).number(price.getQuantity())
										.number(price.getCompositeQuantity()).number(price.getComplimentaryQuantity()).number(price.getAmount());
							}
						}
					}
				}

			}
		}

		if (orders.size() > 0) {
			sheetCtx.nextRow().nextRow().setTotalsDataRange(totalsData).text("Total:").skipCell().skipCell()
					.skipCell().skipCell().skipCell().skipCell().total(Formula.SUM).total(Formula.SUM).total(Formula.SUM)
					.total(Formula.SUM);
		}

	}
}
