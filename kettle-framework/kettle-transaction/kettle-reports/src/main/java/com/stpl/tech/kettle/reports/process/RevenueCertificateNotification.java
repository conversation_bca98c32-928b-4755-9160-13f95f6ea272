package com.stpl.tech.kettle.reports.process;

import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EmailGenerationException;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.notification.EmailNotification;

public class RevenueCertificateNotification extends EmailNotification {

	private EnvType envType;
	private int month;
	private int year;
	private String[] revenueCertificateEmail;
	private String unitName;
	private String type;

	public String getRevenueCertificateUrl() {
		return revenueCertificateUrl;
	}

	public void setRevenueCertificateUrl(String revenueCertificateUrl) {
		this.revenueCertificateUrl = revenueCertificateUrl;
	}

	private String revenueCertificateUrl ="";

	public RevenueCertificateNotification(EnvType envType, int month, int year,String[] revenueCertificateEmail,String unitName,String type) {
		super();
		this.envType = envType;
		this.month = month;
		this.year = year;
		this.revenueCertificateEmail = revenueCertificateEmail;
		this.unitName = unitName;
		this.type = type;
	}

	@Override
	public String[] getToEmails() {
		if (TransactionUtils.isProd(envType)) {
			if (revenueCertificateEmail.length != 0) {
				return revenueCertificateEmail;
			} else {
				return new String[]{"<EMAIL>"};
			}
		} else {
			if (revenueCertificateEmail.length != 0) {
				return revenueCertificateEmail;
			} else {
				return new String[]{"<EMAIL>"};
			}
		}
	}

	@Override
	public String getFromEmail() {
		return "<EMAIL>";
	}

	@Override
	public String subject() {
		String subjectOfEmail = "Revenue Certificate for " + AppUtils.getMonthName(month) + "-" + year;
		if (TransactionUtils.isDev(envType)) {
			subjectOfEmail = " [DEV] : " + subjectOfEmail;
		}
		return subjectOfEmail;
	}

	@Override
	public String body() throws EmailGenerationException {
		switch (type) {
			case "cron":
				return revenueCertificateUrl;
			case "uploading":
				return "<html><p> " + " Revenue Certificate of " + unitName + " for " + AppUtils.getMonthName(month) + " " + year
						+ "</p></html>";
			default:
				return null;
		}
	}

	@Override
	public EnvType getEnvironmentType() {
		return envType;
	}

}
