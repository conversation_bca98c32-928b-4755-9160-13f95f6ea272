/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.dao.impl;

import com.stpl.tech.kettle.reports.core.ReportType;

/**
 * <AUTHOR>
 *
 */
public class ReportFileData {

	private ReportType type;
	private String fileName;
	private String mimeType;
	private String filePath;
	private boolean generated;

	public ReportFileData(ReportType type, String mimeType, String fileName, String filePath, boolean generated) {
		super();
		this.type = type;
		this.fileName = fileName;
		this.mimeType = mimeType;
		this.filePath = filePath;
		this.generated = generated;
	}

	public ReportType getType() {
		return type;
	}

	public String getFileName() {
		return fileName;
	}

	public String getFilePath() {
		return filePath;
	}

	public boolean isGenerated() {
		return generated;
	}

	public void setGenerated(boolean generated) {
		this.generated = generated;
	}

	public String getMimeType() {
		return mimeType;
	}

}
