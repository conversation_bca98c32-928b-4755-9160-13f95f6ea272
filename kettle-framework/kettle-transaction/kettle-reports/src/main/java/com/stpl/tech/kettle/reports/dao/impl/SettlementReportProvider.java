/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.dao.impl;

import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.core.cache.MetadataCache;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.Settlement;
import com.stpl.tech.kettle.reports.model.SettlementReport;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.PaymentMode;
import com.stpl.tech.master.tax.model.Taxation;
import com.stpl.tech.util.AppConstants;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeMap;

public class SettlementReportProvider {

	private MasterDataCache masterCache;
	private MetadataCache metadataCache;

	private Map<Integer, SettlementReport> paymentType = new TreeMap<Integer, SettlementReport>();
	private Map<Integer, Map<Integer, SettlementReport>> channelPartners = new TreeMap<Integer, Map<Integer, SettlementReport>>();
	private Map<Integer, Map<Integer, SettlementReport>> terminals = new TreeMap<Integer, Map<Integer, SettlementReport>>();

	public SettlementReportProvider(MasterDataCache cache, MetadataCache metadataCache) {
		this.masterCache = cache;
		this.metadataCache = metadataCache;
	}

	public void process(SettlementReportInputData reportData, Set<Taxation> stateTaxes) {
		preProcess(reportData);
		for (Order order : reportData.orders) {
			addToPaymentType(order, stateTaxes);
		}
	}

	public Collection<SettlementReport> getByPaymentType() {
		return paymentType.values();
	}

	protected void preProcess(SettlementReportInputData reportData) {
		List<PaymentMode> allPaymentMode = masterCache.getAllPaymentMode();
		for (PaymentMode mode : allPaymentMode) {
			paymentType.put(mode.getId(), new SettlementReport(mode));
		}

		for (IdCodeName channelPartner : masterCache.getAllChannelPartners()) {
			channelPartners.put(channelPartner.getId(), new TreeMap<>());
			for (PaymentMode mode : allPaymentMode) {
				channelPartners.get(channelPartner.getId()).put(mode.getId(), new SettlementReport(mode));
			}
		}

		if (reportData.getUnit().getNoOfTerminals() > 0) {
			for (int i = 0; i <= reportData.getUnit().getNoOfTerminals(); i++) {
				terminals.put(i, new TreeMap<>());
				for (PaymentMode mode : allPaymentMode) {
					terminals.get(i).put(mode.getId(), new SettlementReport(mode));
				}
			}
		}

		if (reportData.getUnit().getNoOfTakeawayTerminals() > 0) {
			for (int i = 0; i <= reportData.getUnit().getNoOfTakeawayTerminals(); i++) {
				int terminalId = 0;
				if (i == 0) {
					terminalId = i; // for COD terminal
				} else {
					terminalId = AppConstants.TAKEAWAY_SEED + i; // take away
																	// terminals
																	// are
																	// indexed
																	// with
																	// 100+index
				}

				terminals.put(terminalId, new TreeMap<Integer, SettlementReport>());
				for (PaymentMode mode : allPaymentMode) {
					terminals.get(terminalId).put(mode.getId(), new SettlementReport(mode));
				}
			}
		}

	}

	protected void addToPaymentType(Order order, Set<Taxation> stateTaxes) {
		if (TransactionUtils.isSkipOrder(order)) {
			return;
		}
		for (Settlement settlement : order.getSettlements()) {
			SettlementReport data = paymentType.get(settlement.getMode());
			ReportUtil.addSettlementsData(data, order, settlement.getAmount(), order.getTransactionDetail(),
					order.getSettlements().size() > 1, settlement.getExtraVouchers(), stateTaxes);
			SettlementReport data1 = channelPartners.get(order.getChannelPartner()).get(settlement.getMode());
			ReportUtil.addSettlementsData(data1, order, settlement.getAmount(), order.getTransactionDetail(),
					order.getSettlements().size() > 1, settlement.getExtraVouchers(), stateTaxes);
			channelPartners.get(order.getChannelPartner()).put(settlement.getMode(), data1);
			SettlementReport terminalData = null;
			if (TransactionUtils.isCODOrder(order.getSource())) {
				terminalData = terminals.get(0).get(settlement.getMode());
			} else {
				terminalData = terminals.get(order.getTerminalId()).get(settlement.getMode());
			}
			ReportUtil.addSettlementsData(terminalData, order, settlement.getAmount(), order.getTransactionDetail(),
					order.getSettlements().size() > 1, settlement.getExtraVouchers(), stateTaxes);
		}
	}

	public Map<Integer, Map<Integer, SettlementReport>> getChannelPartners() {
		return channelPartners;
	}

	public Collection<SettlementReport> getByTerminal(int terminalId) {
		return terminals.get(terminalId).values();
	}
}
