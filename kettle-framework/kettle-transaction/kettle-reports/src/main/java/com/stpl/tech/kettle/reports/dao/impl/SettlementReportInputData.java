/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.dao.impl;

import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderDetailTrim;
import com.stpl.tech.kettle.reports.core.ReportInput;
import com.stpl.tech.master.domain.model.ChannelPartnerDetail;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.util.notification.ReportDetailData;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

public class SettlementReportInputData implements ReportInput<Unit> {

	protected final List<Order> orders;
	protected final List<OrderDetailTrim> orderDetailTrims;
	protected final Date businessDate;
	protected final boolean generatePnL;
	protected final BigDecimal creditCardPercentage;
	protected final Map<Integer, ChannelPartnerDetail> partners;
	protected final Unit unit;
	protected Map<String, List<ReportDetailData>> queryReports;

	public SettlementReportInputData(Date businessDate, Unit unit, List<Order> orders, boolean generatePnL,
			BigDecimal creditCardPercentage, Map<Integer, ChannelPartnerDetail> partners ,
									 List<OrderDetailTrim> orderDetailTrims) {
		this.businessDate = businessDate;
		this.unit = unit;
		this.orders = orders;
		this.generatePnL = generatePnL;
		this.creditCardPercentage = creditCardPercentage;
		this.partners = partners;
		this.orderDetailTrims=orderDetailTrims;
	}

	public SettlementReportInputData(Date businessDate, Unit unit, List<Order> orders,
									 Map<String, List<ReportDetailData>> queryReports, boolean generatePnL, BigDecimal creditCardPercentage,
									 Map<Integer, ChannelPartnerDetail> partners, List<OrderDetailTrim> orderDetailTrims) {
		super();
		this.orders = orders;
		this.businessDate = businessDate;
		this.unit = unit;
		this.queryReports = queryReports;
		this.generatePnL = generatePnL;
		this.creditCardPercentage = creditCardPercentage;
		this.partners = partners;
		this.orderDetailTrims = orderDetailTrims;
	}

	public Map<String, List<ReportDetailData>> getQueryReports() {
		return queryReports;
	}

	public void setQueryReports(Map<String, List<ReportDetailData>> queryReports) {
		this.queryReports = queryReports;
	}

	public List<Order> getOrders() {
		return orders;
	}

	public List<OrderDetailTrim> getOrderDetailTrims(){ return orderDetailTrims;}

	public Unit getUnit() {
		return unit;
	}

	public Date getBusinessDate() {
		return businessDate;
	}

	@Override
	public Unit getContext() {
		return unit;
	}

	public boolean isGeneratePnL() {
		return generatePnL;
	}

	public BigDecimal getCreditCardPercentage() {
		return creditCardPercentage;
	}

	public Map<Integer, ChannelPartnerDetail> getPartners() {
		return partners;
	}

	
}
