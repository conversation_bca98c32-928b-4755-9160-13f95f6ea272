/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.view;

import java.util.Collection;

import com.stpl.tech.master.util.MasterUtil;
import org.subtlelib.poi.api.sheet.SheetContext;
import org.subtlelib.poi.api.style.Style;
import org.subtlelib.poi.api.totals.ColumnTotalsDataRange;
import org.subtlelib.poi.api.totals.Formula;
import org.subtlelib.poi.api.workbook.WorkbookContext;

import com.google.common.base.Optional;
import com.stpl.tech.kettle.core.cache.MetadataCache;
import com.stpl.tech.kettle.reports.model.ComplimentaryBillData;
import com.stpl.tech.master.core.external.cache.MasterDataCache;

public class ComplimentaryReportView extends ExcelReportView<Collection<ComplimentaryBillData>> {

	public ComplimentaryReportView(MetadataCache cache, MasterDataCache userService, String header, String type) {
		super(header, type, cache, userService);
	}

	/**
	 * TABLE HEADERS
	 * ------------------------------------------------------------------------
	 * Order No. | Bill No. | Bill Amount | Net Price Amount | MRP Amount |
	 * Total Complimentary Amt | Accountable Complimentary | Non Accountable
	 * Complimentary | Total VAT | Net Price VAT | MRP VAT | Generated By |
	 * Reason Code | Reason For Discount
	 * ------------------------------------------------------------------------
	 */
	public void render(WorkbookContext workbookCtx, Collection<ComplimentaryBillData> orders) {
		SheetContext sheetCtx = workbookCtx.createSheet(header);
		Style headerStyle = MasterUtil.getHeaderStyle(workbookCtx);


		sheetCtx.nextRow().skipCell().setTextStyle(headerStyle).text("Order No.").setColumnWidth(20).text("Bill No.").setColumnWidth(20)
				.text("Bill Amount").setColumnWidth(15).text("Taxable Amount").setColumnWidth(15)
				.text("Total Complimentary Amt").setColumnWidth(20).text("Accountable Complimentary")
				.setColumnWidth(20).text("Non Accountable Complimentary").setColumnWidth(20).text("Total Tax")
				.setColumnWidth(15).text("Generated By").setColumnWidth(15).text("Reason Code").setColumnWidth(15)
				.text("Reason For Complimentary").setColumnWidth(50);

		ColumnTotalsDataRange totalsData = sheetCtx.startColumnTotalsDataRangeFromNextRow();

		for (ComplimentaryBillData order : orders) {
			sheetCtx.nextRow().skipCell().text(order.getGeneratedOrderId()).number(order.getOrderId())
					.number(order.getTotalAmount()).number(order.getTaxableAmount())
					.number(order.getComplimentaryValue()).number(order.getAccountableComp())
					.number(order.getComplimentaryValue().subtract(order.getAccountableComp()))
					.number(order.getTotalTax())
					.text(Optional.<String> fromNullable(getEmployeeName(order.getEmployeeId()))).text(order.getCode())
					.text(Optional.<String> fromNullable(order.getReason()));
		}
		if (orders.size() > 0) {
			sheetCtx.nextRow().nextRow().setTextStyle(headerStyle).setTotalsDataRange(totalsData).text("Total:").skipCell().skipCell()
					.total(Formula.SUM).total(Formula.SUM).total(Formula.SUM).total(Formula.SUM).total(Formula.SUM)
					.total(Formula.SUM);
		}
	}
}
