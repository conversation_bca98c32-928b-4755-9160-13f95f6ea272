/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.dao.impl;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.stream.Collectors;

import com.stpl.tech.kettle.core.TransactionConstants;
import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.core.cache.MetadataCache;
import com.stpl.tech.kettle.domain.model.ComplimentaryDetail;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.Settlement;
import com.stpl.tech.kettle.domain.model.TaxDetail;
import com.stpl.tech.kettle.reports.model.CategoryConsumption;
import com.stpl.tech.kettle.reports.model.ProductConsumption;
import com.stpl.tech.kettle.reports.model.ProductDimensionConsumption;
import com.stpl.tech.kettle.reports.model.ProductPriceConsumption;
import com.stpl.tech.kettle.reports.model.ProductSourceConsumption;
import com.stpl.tech.kettle.reports.model.SubCategoryConsumption;
import com.stpl.tech.kettle.reports.model.UnitConsumption;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.ListData;
import com.stpl.tech.master.domain.model.Pair;
import com.stpl.tech.master.domain.model.Product;
import com.stpl.tech.master.domain.model.ProductPrice;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.recipe.model.IngredientProductDetail;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class UnitConsumptionReportProvider {
    private static final Logger LOG = LoggerFactory.getLogger(UnitConsumptionReportProvider.class);

    private final MasterDataCache masterCache;
    private final MetadataCache metadataCache;

    protected UnitConsumption unitConsumption;

    public UnitConsumptionReportProvider(MasterDataCache cache, MetadataCache metadataCache) {
        this.masterCache = cache;
        this.metadataCache = metadataCache;
    }

    public void process(SettlementReportInputData reportData) {
        preProcess(reportData);
        for (Order order : reportData.orders) {
            try{
                addToConsumptionDetail(order);
            }catch (Exception e){
                LOG.error("Error While Adding Consumption For Order Id : {} ::::: {} ", e, order.getOrderId());
            }
        }
    }

    public UnitConsumption getConsumptionData() {
        return unitConsumption;
    }

    protected void preProcess(SettlementReportInputData reportData) {
        unitConsumption = new UnitConsumption(reportData.unit.getName());
        Map<Integer, CategoryConsumption<ProductConsumption<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>>>> consumptionData = new TreeMap<Integer, CategoryConsumption<ProductConsumption<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>>>>();
        for (ListData code : masterCache.getAllProductCategories()) {
            LOG.info("Day Close {} ### PRODUCT CATEGORY {}",reportData.getUnit().getId(),code.getDetail().getId());
            consumptionData.put(code.getDetail().getId(),
                    new CategoryConsumption<ProductConsumption<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>>>(
                            code.getDetail().getName()));
            for (IdCodeName sub : code.getContent()) {
                LOG.info("Day Close ### SUB PRODUCT CATEGORY {}",sub.getCode());
                consumptionData.get(code.getDetail().getId()).getItems().put(sub.getId(),
                        new SubCategoryConsumption<ProductConsumption<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>>>(
                                sub.getName()));
            }
        }
        unitConsumption.getItems().putAll(consumptionData);
        for (Product product : masterCache.getUnitProductDetails(reportData.getUnit().getId())) {
            try {
                Map<Integer, ProductConsumption<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>>> productsMap = unitConsumption
                        .getItems().get(product.getType()).getItems().get(product.getSubType()).getItems();
                ProductConsumption<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>> consumption = new ProductConsumption<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>>(
                        product.getName());
                consumption.setBillType(product.getBillType());
                productsMap.put(product.getId(), consumption);
                ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>> source = new ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>(
                        UnitCategory.CAFE.name());
                consumption.getItems().put(UnitCategory.CAFE.name(), source);
                for (ProductPrice price : product.getPrices()) {
                    ProductPriceConsumption priceConsumption = new ProductPriceConsumption(product.getName(),
                            price.getPrice());
                    if (!source.getItems().containsKey(price.getDimension())) {
                        ProductDimensionConsumption<ProductPriceConsumption> dim = new ProductDimensionConsumption<ProductPriceConsumption>(
                                product.getName(), price.getDimension());
                        source.getItems().put(price.getDimension(), dim);
                        dim.setCategory(product.getType());
                    }
                    if (price.getPrice() == null) {
                        LOG.error("day close price missing::: {}", product.getName());
                    }
                    source.getItems().get(price.getDimension()).getItems().put(price.getPrice(), priceConsumption);
                }
            } catch (Exception e) {
                LOG.error("Day Close {} ### PRODUCT ID {} ::::::::::",reportData.getUnit().getId(),product.getId(),e);
            }
        }

    }

    protected void addToConsumptionDetail(Order order) {
        if (TransactionUtils.isSkipOrder(order)) {
            return;
        }
        unitConsumption
                .setGrossAmount(unitConsumption.getGrossAmount().add(order.getTransactionDetail().getTotalAmount()));
        ReportUtil.addAmount(unitConsumption, order.getTransactionDetail().getTaxableAmount());
        for (OrderItem item : order.getOrders()) {
            addProductConsumption(order.getSource(), item.getProductId(), item.getProductName(), item.getDimension(),
                    item.getPrice(), item.getAmount(), item.getQuantity(), item.getComplimentaryDetail(),
                    getDiscountAmount(order, item, null), getPaymentDistribution(order, item, null), item.getTaxes());
            if (item.getComposition().getMenuProducts() != null && item.getComposition().getMenuProducts().size() > 0) {
                for (OrderItem menuProduct : item.getComposition().getMenuProducts()) {
                    addProductConsumption(order.getSource(), menuProduct.getProductId(), menuProduct.getProductName(),
                            menuProduct.getDimension(), menuProduct.getPrice(), menuProduct.getAmount(),
                            menuProduct.getQuantity(), menuProduct.getComplimentaryDetail(),
                            getDiscountAmount(order, menuProduct, item),
                            getPaymentDistribution(order, menuProduct, item), menuProduct.getTaxes());
                    if (menuProduct.getComposition().getAddons() != null
                            && menuProduct.getComposition().getAddons().size() > 0) {
                        for (IngredientProductDetail addon : menuProduct.getComposition().getAddons()) {
                            addProductConsumption(order.getSource(), addon.getProduct().getProductId(),
                                    addon.getProduct().getName(), addon.getDimension().getName(), BigDecimal.ZERO,
                                    BigDecimal.ZERO, menuProduct.getQuantity(), menuProduct.getComplimentaryDetail(),
                                    BigDecimal.ZERO, null, item.getTaxes());
                        }
                    }
                }
            }
            if (item.getComposition().getAddons() != null && item.getComposition().getAddons().size() > 0) {
                for (IngredientProductDetail addon : item.getComposition().getAddons()) {
                    addProductConsumption(order.getSource(), addon.getProduct().getProductId(),
                            addon.getProduct().getName(), addon.getDimension().getName(), BigDecimal.ZERO,
                            BigDecimal.ZERO, item.getQuantity(), item.getComplimentaryDetail(), BigDecimal.ZERO, null,
                            null);
                }
            }

        }
        if (isComplimentary(order.getTransactionDetail().getTotalAmount(), order.getOrders())) {
            unitConsumption.setComplimentaryBillCount(unitConsumption.getComplimentaryBillCount() + 1);
        } else {
            unitConsumption.setBillCount(unitConsumption.getBillCount() + 1);
            addToCategoryAndSubCategoryBillCount(order, unitConsumption);
        }
    }

    private void addToCategoryAndSubCategoryBillCount(Order order, UnitConsumption unitConsumption) {
        Map<String, Integer> categoryWiseBillCount = unitConsumption.getBillCountByCategory();
        Map<String, Integer> subCategoryWiseBillCount = unitConsumption.getBillCountBySubCategory();

        List<String> categories = order.getOrders().stream()
                .map(orderItem -> orderItem.getProductCategory().getName())
                .distinct().collect(Collectors.toList());

        List<String> subCategories = order.getOrders().stream()
                .map(orderItem -> orderItem.getProductSubCategory().getName())
                .distinct().collect(Collectors.toList());

        categoryWiseBillCount = addToMap(categories, categoryWiseBillCount);
        subCategoryWiseBillCount = addToMap(subCategories, subCategoryWiseBillCount);
        unitConsumption.setBillCountByCategory(categoryWiseBillCount);
        unitConsumption.setBillCountBySubCategory(subCategoryWiseBillCount);
    }

    private Map<String, Integer> addToMap(List<String> keys, Map<String, Integer> map) {
        if(map==null){
            map = new HashMap<>();
        }
        for (String key : keys) {
            Integer count = map.get(key);
            if (count == null) {
                map.put(key, 1);
            } else {
                map.put(key, count + 1);
            }
        }
        return map;
    }


    // Method to distribute payment
    private Map<String, BigDecimal> getPaymentDistribution(Order order, OrderItem item, OrderItem parentItem) {
        BigDecimal discount = getDiscountAmount(order, item, parentItem);
        BigDecimal sellingPrice = AppUtils.multiplyWithScale10(item.getPrice(), new BigDecimal(item.getQuantity()))
                .subtract(discount).add(item.getTax());

        BigDecimal percentOfTotal = AppUtils.percentageWithScale10(sellingPrice,
                order.getTransactionDetail().getPaidAmount());

        Map<String, BigDecimal> payments = new HashMap<>();
        String key = null;
        BigDecimal value = null;

        for (Settlement settlement : order.getSettlements()) {
            key = settlement.getModeDetail().getName();
            value = AppUtils.percentOfWithScale10(settlement.getAmount(), percentOfTotal);
            if (settlement.getExtraVouchers() != null
                    && !AppUtils.isEqual(settlement.getExtraVouchers(), BigDecimal.ZERO)) {
                // extra vouchers distributed here
                BigDecimal extraVoucherShare = AppUtils.percentOfWithScale10(settlement.getExtraVouchers(),
                        percentOfTotal);
                value = value.add(extraVoucherShare);
                addToMap(TransactionConstants.EXTRA_VOUCHERS, extraVoucherShare, payments);
            }
            addToMap(key, value, payments);
        }
        // round off payment
        // percentage of total amount is also percentage of round off
        value = AppUtils.percentOfWithScale10(order.getTransactionDetail().getRoundOffValue(), percentOfTotal);
        addToMap(TransactionConstants.ROUND_OFF, value, payments);
        return payments;
    }

    private void addToMap(String key, BigDecimal value, Map<String, BigDecimal> payments) {
        if (payments.containsKey(key)) {
            payments.put(key, payments.get(key).add(value));
        } else {
            payments.put(key, value);
        }
    }

    private BigDecimal getDiscountAmount(Order order, OrderItem item, OrderItem parentItem) {

        // discount amount for a particular order item
        BigDecimal discount = BigDecimal.ZERO;
        // CASE 1 Complimentary order
        if (ReportUtil.isComplimentary(item.getComplimentaryDetail())) {
            // CASE 2 Combo Item
            if (ReportUtil.isPartOfCompositeProduct(item.getComplimentaryDetail().getReasonCode())) {
                // CASE 3 Discount on combo
                BigDecimal discountOnParentItem = getStandardDiscount(order, parentItem);
                // discount as per CASE 2
                discount = ReportUtil.calculateCompositeDiscountAmount(item, parentItem, discountOnParentItem);
            } else {
                // discount as per CASE 1
                discount = item.getPrice().multiply(new BigDecimal(item.getQuantity()));
            }
        } else {
            // CASE 4 Non Complimentary
            discount = getStandardDiscount(order, item);
        }
        return discount;
    }

    private BigDecimal getStandardDiscount(Order order, OrderItem item) {
        // Case 4 Non Complimentary Normal Discount

        // Case 4.1 Order Level
        BigDecimal discount = ReportUtil.calculateOrderLevelDiscountOnItem(item, order);

        // Case 4.2 Item level
        if (item.getDiscountDetail().getDiscount().getValue() != null
                && BigDecimal.ZERO.compareTo(item.getDiscountDetail().getDiscount().getValue()) != 0) {
            discount = discount.add(item.getDiscountDetail().getDiscount().getValue());
        }

        // Case 4.3 Item level Promotional discount
        if (item.getDiscountDetail().getPromotionalOffer() != null
                && BigDecimal.ZERO.compareTo(item.getDiscountDetail().getPromotionalOffer()) != 0) {
            discount = discount.add(item.getDiscountDetail().getPromotionalOffer());
        }

        // sum of all of the above
        return discount;
    }

    private boolean isComplimentary(BigDecimal totalAmount, List<OrderItem> items) {

        if (totalAmount.compareTo(BigDecimal.ZERO) != 0) {
            return false;
        }
        for (OrderItem item : items) {
            if (!item.getComplimentaryDetail().isIsComplimentary()) {
                return false;
            }
        }
        return true;
    }

    private void addProductConsumption(String src, int productId, String productName, String dimensionString,
                                       BigDecimal productPrice, BigDecimal amount, int quantity, ComplimentaryDetail complimentaryDetail,
                                       BigDecimal discount, Map<String, BigDecimal> payments, List<TaxDetail> taxes) {

        Product product = masterCache.getProduct(productId);
        CategoryConsumption<ProductConsumption<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>>> category = unitConsumption
                .getItems().get(product.getType());
        SubCategoryConsumption<ProductConsumption<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>>> subCategory = category
                .getItems().get(product.getSubType());
        ProductConsumption<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>> productData = subCategory
                .getItems().get(productId);
        if (productData == null) {
            productData = new ProductConsumption<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>>(
                    productName);
            productData.setBillType(product.getBillType());
            subCategory.getItems().put(productId, productData);
        }
        ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>> source = productData.getItems()
                .get(src);
        if (source == null) {
            source = new ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>(src);
            productData.getItems().put(src, source);
        }
        ProductDimensionConsumption<ProductPriceConsumption> dimension = source.getItems().get(dimensionString);
        if (dimension == null) {
            dimension = new ProductDimensionConsumption<ProductPriceConsumption>(productName, dimensionString);
            source.getItems().put(dimensionString, dimension);
        }
        ProductPriceConsumption price = dimension.getItems().get(productPrice);
        if (price == null) {
            price = new ProductPriceConsumption(productName, productPrice);
            dimension.getItems().put(productPrice, price);
        }
        boolean isComplimentary = ReportUtil.isComplimentary(complimentaryDetail);
        if (!isComplimentary) {
            price.setAmount(price.getAmount().add(amount));
            dimension.setAmount(dimension.getAmount().add(amount));
            price.setQuantity(price.getQuantity() + quantity);
            price.setStandardDiscount(price.getStandardDiscount().add(discount));
            dimension.setQuantity(dimension.getQuantity() + quantity);
            ReportUtil.addAmountAndQuantity(productData, amount, quantity);
            ReportUtil.addAmountAndQuantity(subCategory, amount, quantity);
            ReportUtil.addAmountAndQuantity(category, amount, quantity);
        } else {
            ReportUtil.addQuantity(productData, quantity);
            ReportUtil.addQuantity(subCategory, quantity);
            ReportUtil.addQuantity(category, quantity);
            if (ReportUtil.isPartOfCompositeProduct(complimentaryDetail.getReasonCode())) {
                price.setCompositeQuantity(price.getCompositeQuantity() + quantity);
                price.setCompositeDiscount(price.getCompositeDiscount().add(discount));
                dimension.setCompositeQuantity(dimension.getCompositeQuantity() + quantity);
            } else {
                price.setComplimentaryQuantity(price.getComplimentaryQuantity() + quantity);
                price.setComplimentaryDiscount(price.getComplimentaryDiscount().add(discount));
                dimension.setComplimentaryQuantity(dimension.getComplimentaryQuantity() + quantity);
            }
            unitConsumption.setHasItems(true);
        }
        if (payments != null) {
            for (String mode : payments.keySet()) {
                BigDecimal payment = price.getSettlementsByModes().get(mode);
                if (payment != null) {
                    payment = payment.add(payments.get(mode));
                    price.getSettlementsByModes().put(mode, payment);
                } else {
                    price.getSettlementsByModes().put(mode, BigDecimal.ZERO.add(payments.get(mode)));
                    // masterCache.getPaymentModes()
                }
            }
        }

        if (taxes != null && !taxes.isEmpty()) {
            for (TaxDetail tax : taxes) {
                Pair<BigDecimal, BigDecimal> b = price.getTaxes().get(getTaxCode(tax));
                if (b != null) {
                    b.setValue(b.getValue().add(getTax(tax)));
                } else {
                    b = new Pair<BigDecimal, BigDecimal>(tax.getPercentage(), getTax(tax));
                }
                price.getTaxes().put(getTaxCode(tax), b);
            }
        }
    }

    private BigDecimal getTax(TaxDetail tax) {
        return AppUtils.percentOfWithScale10(tax.getPercentage(), tax.getTaxable());
    }

    private String getTaxCode(TaxDetail tax) {
        if (tax.getType().equals("GST")) {
            return tax.getCode();
        }
        return "CESS";
    }
}
