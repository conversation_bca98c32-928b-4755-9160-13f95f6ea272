/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.dao.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import com.stpl.tech.kettle.core.TransactionConstants;
import com.stpl.tech.kettle.domain.model.ComplimentaryDetail;
import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.domain.model.Settlement;
import com.stpl.tech.kettle.domain.model.TaxDetail;
import com.stpl.tech.kettle.domain.model.TransactionDetail;
import com.stpl.tech.kettle.reports.model.AbstractConsumption;
import com.stpl.tech.kettle.reports.model.CategoryConsumption;
import com.stpl.tech.kettle.reports.model.ProductConsumption;
import com.stpl.tech.kettle.reports.model.ProductDimensionConsumption;
import com.stpl.tech.kettle.reports.model.ProductPriceConsumption;
import com.stpl.tech.kettle.reports.model.ProductSourceConsumption;
import com.stpl.tech.kettle.reports.model.SettlementReport;
import com.stpl.tech.kettle.reports.model.SubCategoryConsumption;
import com.stpl.tech.master.domain.model.BillType;
import com.stpl.tech.master.domain.model.TaxType;
import com.stpl.tech.master.tax.model.Taxation;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.apache.commons.lang3.StringUtils;

public class ReportUtil {

	public static void addAmount(AbstractConsumption consumption, BigDecimal amount) {
		consumption.setAmount(consumption.getAmount().add(amount));
		consumption.setHasItems(true);
	}

	/*
	 * public static void addAmountAndQuantity(AbstractConsumption consumption,
	 * OrderItem item) {
	 * consumption.setAmount(consumption.getAmount().add(item.getAmount()));
	 * consumption.setQuantity(consumption.getQuantity() + item.getQuantity());
	 * consumption.setHasItems(true); }
	 */

	public static void addAmountAndQuantity(AbstractConsumption consumption, BigDecimal amount, int quantity) {
		consumption.setAmount(consumption.getAmount().add(amount));
		consumption.setQuantity(consumption.getQuantity() + quantity);
		consumption.setHasItems(true);
	}

	public static void addQuantity(AbstractConsumption consumption, int quantity) {
		consumption.setQuantity(consumption.getQuantity() + quantity);
		consumption.setHasItems(true);
	}

	public static boolean isComplimentary(ComplimentaryDetail detail) {
		return detail != null && detail.isIsComplimentary();
	}

	public static boolean isCredit(Settlement settlement) {
		return AppConstants.PAYMENT_MODE_CREDIT.equals(settlement.getModeDetail().getName());
		//return settlement.getModeDetail().getName().equals(AppConstants.PAYMENT_MODE_CREDIT);
	}

	public static boolean isCancelled(OrderStatus status) {
		return OrderStatus.CANCELLED.equals(status);
	}

	public static void addSettlementsData(SettlementReport data, Order order, BigDecimal settlementAmount,
			TransactionDetail settlement, boolean isSplit, BigDecimal extraVouchers, Set<Taxation> stateTaxes) {
		BigDecimal value = new BigDecimal(0.0d);
		if (settlement != null && settlement.getPaidAmount() != null) {
			value = settlement.getPaidAmount();
			if (settlement.getRoundOffValue() != null) {
				value = value.add(settlement.getRoundOffValue());
			}
		}
		BigDecimal percentage = isSplit ? AppUtils.divide(settlementAmount, value) : new BigDecimal(1);
		addSettlementsData(data, settlement, percentage, settlementAmount, extraVouchers, stateTaxes);

	}

	public static void addSettlementsData(SettlementReport data, TransactionDetail settlement, BigDecimal percentage,
			BigDecimal settlementAmount, BigDecimal extraVouchers, Set<Taxation> stateTaxes) {
		data.setTotal(data.getTotal().add(extraVouchers).add(settlementAmount).setScale(2, BigDecimal.ROUND_HALF_UP));
		data.setGrossAmount(data.getGrossAmount().add(AppUtils.multiply(settlement.getTotalAmount(), percentage)));
		if (settlement.getDiscountDetail() != null && settlement.getDiscountDetail().getDiscount() != null) {
			data.setDiscountAmount(data.getDiscountAmount()
					.add(AppUtils.multiply(settlement.getDiscountDetail().getDiscount().getValue(), percentage)));
		}
		data.setAmount(data.getAmount().add(AppUtils.multiply(settlement.getTaxableAmount(), percentage)));
		data.setRoundOff(data.getRoundOff().add(AppUtils.multiply(settlement.getRoundOffValue(), percentage)));
		for (TaxDetail detail : settlement.getTaxes()) {
			Taxation td = getTaxation(detail, stateTaxes);
			data.addTax(td, AppUtils.multiply(detail.getValue(), percentage));
		}
		data.setNoOfBills(data.getNoOfBills() + 1);
		data.setExtraVouchers(data.getExtraVouchers().add(extraVouchers));
	}

	private static Taxation getTaxation(TaxDetail detail, Set<Taxation> stateTaxes) {
		Taxation td = null;
		if (detail.getType().equals("GST")) {
			td = new Taxation("A", detail.getCode(), detail.getCode(), detail.getPercentage(), detail.getType());
		} else {
			td = new Taxation("CESS", "CESS", "CESS", detail.getPercentage(), detail.getType());
		}
		if (!stateTaxes.contains(td)) {
			stateTaxes.add(td);
		}
		return td;
	}

	public static List<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>> getConsumptions(
			UnitConsumptionReportProvider provider) {
		List<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>> products = new ArrayList<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>>();
		for (CategoryConsumption<ProductConsumption<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>>> order : provider
				.getConsumptionData().getAllItems()) {
			for (SubCategoryConsumption<ProductConsumption<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>>> sub : order
					.getAllItems()) {
				for (ProductConsumption<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>> prod : sub
						.getAllItems()) {
					for (ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>> source : prod
							.getAllItems()) {
						for (ProductDimensionConsumption<ProductPriceConsumption> dim : source.getAllItems()) {
							if (dim.getQuantity() > 0 || dim.getComplimentaryQuantity() > 0
									|| dim.getCompositeQuantity() > 0) {
								products.add(source);
								break;
							}
						}
					}
				}

			}
		}
		return products;
	}

	public static boolean isPartOfCompositeProduct(Integer reasonCode) {
		return reasonCode != null && reasonCode.equals(AppConstants.COMPLEMENTARY_CODE_COMBO);
	}

	public static BigDecimal calculateCompositeDiscountAmount(OrderItem item, OrderItem parentItem,
			BigDecimal discountOnParentItem) {

		BigDecimal discountOnItem = BigDecimal.ZERO;

		BigDecimal totalAmount = BigDecimal.ZERO;

		// calculate total amount i.e. total sum if products are sold separately
		// total amount will always be greater than parent item price
		for (OrderItem i : parentItem.getComposition().getMenuProducts()) {
			totalAmount = totalAmount.add(AppUtils.multiplyWithScale10(i.getPrice(), new BigDecimal(i.getQuantity())));
		} // 353

		BigDecimal comboAmount = AppUtils.multiplyWithScale10(parentItem.getPrice(),
				new BigDecimal(parentItem.getQuantity()));
		// 289

		BigDecimal discountOnCombo = totalAmount.subtract(comboAmount);
		// 64

		BigDecimal discountPercentage = AppUtils.divideWithScale10(discountOnCombo, totalAmount);

		// item's selling price i.e price * quantity
		BigDecimal itemSP = AppUtils.multiplyWithScale10(item.getPrice(), new BigDecimal(item.getQuantity()));

		discountOnItem = AppUtils.multiplyWithScale10(itemSP, discountPercentage);

		// ratio as a part of combo
		BigDecimal ratioOfItemToTotalAmount = AppUtils.divideWithScale10(itemSP, totalAmount);

		BigDecimal comboItemPrice = ratioOfItemToTotalAmount.multiply(
				AppUtils.multiplyWithScale10(parentItem.getPrice(), new BigDecimal(parentItem.getQuantity())));

		// discount in composite product = actual product price - price as part
		// of composite product
		discountOnItem = itemSP.subtract(comboItemPrice);

		// add discount due to parent(Combo)
		// this too will be shared among all combo constituents
		if (discountOnParentItem != null && BigDecimal.ZERO.compareTo(discountOnParentItem) != 0) {
		    discountOnItem = discountOnItem.add(ratioOfItemToTotalAmount.multiply(discountOnParentItem));
		}

		return discountOnItem;
	}

	public static BigDecimal calculateOrderLevelDiscountOnItem(OrderItem item, Order order) {
		BigDecimal discount = BigDecimal.ZERO;
		BigDecimal totalOrderAmount = BigDecimal.ZERO;
		BigDecimal totalOfpromotionlAmountByItems = BigDecimal.ZERO;
		BigDecimal totalOfdiscountByItems = BigDecimal.ZERO;

		for (OrderItem i : order.getOrders()) {
			// calculating total order amount
			totalOrderAmount = totalOrderAmount.add(getTaxableAmount(i));
			// promotional amount at item level
			if (i.getDiscountDetail().getPromotionalOffer() != null) {
				totalOfpromotionlAmountByItems = totalOfpromotionlAmountByItems
						.add(i.getDiscountDetail().getPromotionalOffer());
			}
			// standard discount at item level
			if (i.getDiscountDetail().getDiscount().getValue() != null) {
				totalOfdiscountByItems = totalOfdiscountByItems.add(i.getDiscountDetail().getDiscount().getValue());
			}
		}

		BigDecimal itemShareRatio = AppUtils.percentageWithScale10(getTaxableAmount(item), totalOrderAmount);

		// CASE 01 - Promotional discount
		// promotional discount at order level
		BigDecimal promotionalOffer = order.getTransactionDetail().getDiscountDetail().getPromotionalOffer();
		if (promotionalOffer == null) {
			promotionalOffer = BigDecimal.ZERO;
		}
		// distribute excess promotional offer
		if (promotionalOffer != null && !AppUtils.isEqual(promotionalOffer, BigDecimal.ZERO)) {
			// promotional offer at order level = order level - item level
			promotionalOffer = promotionalOffer.subtract(totalOfpromotionlAmountByItems);
			// promotional offer on item = (item amount / actual order amount )
			discount = discount.add(AppUtils.percentOfWithScale10(promotionalOffer, itemShareRatio));
		}

		// CASE 02 - Standard Discount
		// discount at order level
		BigDecimal orderLevelDiscount = order.getTransactionDetail().getDiscountDetail().getDiscount().getValue();
		if (orderLevelDiscount == null) {
			orderLevelDiscount = BigDecimal.ZERO;
		}

		// distribute order level discount
		if (orderLevelDiscount != null && !AppUtils.isEqual(orderLevelDiscount, BigDecimal.ZERO)) {
			orderLevelDiscount = orderLevelDiscount.subtract(totalOfdiscountByItems);
			discount = discount.add(AppUtils.percentOfWithScale10(orderLevelDiscount, itemShareRatio));
		}

		return discount;
	}

	public static BigDecimal getTaxableAmount(OrderItem item) {
		if (item.getComplimentaryDetail().isIsComplimentary()) {
			return BigDecimal.ZERO;
		}
		BigDecimal amount = item.getPrice().multiply(new BigDecimal(item.getQuantity()));

		amount = AppUtils.subtract(amount, item.getDiscountDetail().getDiscount().getValue());
		amount = AppUtils.subtract(amount, item.getDiscountDetail().getPromotionalOffer());
		return amount;
	}

	public static BigDecimal getTaxableAmount(ProductPriceConsumption price) {
		BigDecimal taxableAmount = AppUtils.multiply(price.getPrice(), getTotalQuantity(price));
		return taxableAmount.subtract(getTotalDiscount(price));
	}

	public static BigDecimal getTotalDiscount(ProductPriceConsumption price) {
		return price.getStandardDiscount().add(price.getCompositeDiscount());
	}

	// removed complimentary quantity
	public static BigDecimal getTotalQuantity(ProductPriceConsumption price) {
		return new BigDecimal(price.getQuantity() + price.getCompositeQuantity());
	}

	public static BigDecimal getGrossAmount(ProductPriceConsumption price) {
		return AppUtils.multiply(price.getPrice(), getTotalQuantity(price));
	}

	public static TaxType getBillType(BillType billType) {
		TaxType taxType = null;
		switch (billType) {
		case MRP:
			taxType = TaxType.MRP_VAT;
			break;
		case NET_PRICE:
			taxType = TaxType.NET_PRICE_VAT;
			break;
		default:
			break;
		}
		return taxType;
	}

	public static boolean isMarketingVoucherAppliedOnItem(OrderItem item) {
		if (item != null && item.getDiscountDetail() != null && item.getDiscountDetail().getDiscountCode() != null) {
			return TransactionConstants.MARKETING_VOUCHER_ID == item.getDiscountDetail().getDiscountCode();
		}
		return false;
	}

	/*
	 * public static List<ProductSourceConsumption<AddonConsumption>>
	 * getAddonsConsumptions( UnitConsumptionReportProvider provider) {
	 * List<ProductSourceConsumption<AddonConsumption>> products = new
	 * ArrayList<ProductSourceConsumption<AddonConsumption>>(); for
	 * (SubCategoryConsumption<ProductConsumption<ProductSourceConsumption<
	 * AddonConsumption>>> order : provider
	 * .getConsumptionData().getAllAddonItems()) { for
	 * (ProductConsumption<ProductSourceConsumption<AddonConsumption>> prod :
	 * order.getAllItems()) { for (ProductSourceConsumption<AddonConsumption> source
	 * : prod.getAllItems()) { for (AddonConsumption dim : source.getAllItems()) {
	 * if (dim.getQuantity() > 0 || dim.getComplimentaryQuantity() > 0) {
	 * products.add(source); break; }
	 * 
	 * } } } } return products; }
	 */
}
