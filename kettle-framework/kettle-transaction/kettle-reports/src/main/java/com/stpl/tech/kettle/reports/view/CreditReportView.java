/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.view;

import java.util.Collection;

import com.stpl.tech.master.util.MasterUtil;
import org.subtlelib.poi.api.sheet.SheetContext;
import org.subtlelib.poi.api.style.Style;
import org.subtlelib.poi.api.totals.ColumnTotalsDataRange;
import org.subtlelib.poi.api.totals.Formula;
import org.subtlelib.poi.api.workbook.WorkbookContext;

import com.google.common.base.Optional;
import com.stpl.tech.kettle.core.cache.MetadataCache;
import com.stpl.tech.kettle.reports.model.CreditBillData;
import com.stpl.tech.master.core.external.cache.MasterDataCache;

public class CreditReportView extends ExcelReportView<Collection<CreditBillData>> {

	public CreditReportView(MetadataCache cache, MasterDataCache userService, String header, String type) {
		super(header, type, cache, userService);
	}

	/**
	 * Bill Nos | Bill Amount| Disc. %| Disc. Amt|Generated By |Reason For
	 * Discount
	 * ------------------------------------------------------------------
	 * ------------------------------------------------------
	 */
	public void render(WorkbookContext workbookCtx, Collection<CreditBillData> orders) {
		SheetContext sheetCtx = workbookCtx.createSheet(header);
		Style headerStyle = MasterUtil.getHeaderStyle(workbookCtx);

		sheetCtx.nextRow().skipCell().setTextStyle(headerStyle).text("Order No.").setColumnWidth(20).text("Bill No.")
				.setColumnWidth(20).text("Bill Amount").setColumnWidth(15)
				.text(type + " Amt").setColumnWidth(15).text("Generated By").setColumnWidth(15)
				.text("Channel Partner").setColumnWidth(20).text("Order Remark").setColumnWidth(50);

		ColumnTotalsDataRange totalsData = sheetCtx.startColumnTotalsDataRangeFromNextRow();

		for (CreditBillData order : orders) {
			sheetCtx.nextRow().skipCell().text(order.getGeneratedOrderId()).number(order.getOrderId())
					.number(order.getTotalAmount()).number(order.getCreditAmount())
					.text(Optional.<String> fromNullable(getEmployeeName(order.getEmployeeId())))
					.text(order.getChannelPartner().getName()).text(Optional.<String> fromNullable(order.getOrderRemark()));
		}
		if (orders.size() > 0) {
			sheetCtx.nextRow().nextRow().setTextStyle(headerStyle).setTotalsDataRange(totalsData).text("Total:").skipCell().skipCell()
					.total(Formula.SUM).total(Formula.SUM);
		}

	}
}
