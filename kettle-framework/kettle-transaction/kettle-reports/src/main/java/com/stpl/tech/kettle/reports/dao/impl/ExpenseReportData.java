/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.dao.impl;

import com.stpl.tech.kettle.domain.model.ExpenseUpdateEvent;
import com.stpl.tech.kettle.reports.core.ReportOutput;
import com.stpl.tech.util.EnvType;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ExpenseReportData implements ReportOutput {

	private List<ReportFileData> reportFiles = new ArrayList<ReportFileData>();

	private EnvType env;

	private Date businessDate;

	private ExpenseUpdateEvent event;

	public ExpenseReportData(EnvType env, Date businessDate, ExpenseUpdateEvent event) {
		this.setEnv(env);
		this.businessDate = businessDate;
		this.event = event;
	}

	@Override
	public List<ReportFileData> getReportFiles() {

		return reportFiles;
	}

	public EnvType getEnv() {
		return env;
	}

	public void setEnv(EnvType env) {
		this.env = env;
	}

	public Date getBusinessDate() {
		return businessDate;
	}

	public void setBusinessDate(Date businessDate) {
		this.businessDate = businessDate;
	}

	public ExpenseUpdateEvent getEvent() {
		return event;
	}

	public void setEvent(ExpenseUpdateEvent event) {
		this.event = event;
	}

}
