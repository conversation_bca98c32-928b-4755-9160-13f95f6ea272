/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.view;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Map;

import com.stpl.tech.kettle.core.ServiceType;
import com.stpl.tech.kettle.core.TransactionUtils;
import com.stpl.tech.kettle.reports.model.SettlementReport;
import com.stpl.tech.kettle.reports.model.SettlementTypeReportData;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.util.MasterUtil;
import com.stpl.tech.util.AppUtils;
import org.subtlelib.poi.api.row.RowContext;
import org.subtlelib.poi.api.sheet.SheetContext;
import org.subtlelib.poi.api.style.Style;
import org.subtlelib.poi.api.totals.ColumnTotalsDataRange;
import org.subtlelib.poi.api.totals.Formula;
import org.subtlelib.poi.api.workbook.WorkbookContext;

public class PartnerReportView extends AbstractSettlementReportView<SettlementTypeReportData> {

	private final MasterDataCache masterDataCache;

	public PartnerReportView(MasterDataCache cache, String header, String type) {
		super(header, type);
		this.masterDataCache = cache;
	}

	public void render(WorkbookContext workbookCtx, SettlementTypeReportData data) {
		SheetContext sheetCtx = workbookCtx.createSheet(header);
		Style headerStyle = MasterUtil.getHeaderStyle(workbookCtx);
		renderChannelWiseApc(sheetCtx,data, headerStyle);
        RowContext context = sheetCtx.nextRow();
		context.skipCell().setTextStyle(headerStyle).text("Service Type").setColumnWidth(25).text("Channel Partner").setColumnWidth(25)
				.text("Payment Mode").setColumnWidth(25).text("GMV").setColumnWidth(15).text("Discount")
				.setColumnWidth(15).text("Net Sales Amount").setColumnWidth(18);
		createTaxationHeader(context, data.getTaxations());
		context.text("Round Off").setColumnWidth(15).text("Total").setColumnWidth(15).text("No.Of Bills");
		for (ServiceType type : ServiceType.values()) {

			for (IdCodeName channelPartner : masterDataCache.getAllChannelPartners()) {
				if (TransactionUtils.getServiceType(masterDataCache, channelPartner.getId()).equals(type)) {
					Map<Integer, SettlementReport> datas = data.getMap().get(channelPartner.getId());
					boolean hasValues = hasValues(datas);
					ColumnTotalsDataRange totalsData = null;
					if (hasValues) {
						totalsData = sheetCtx.startColumnTotalsDataRangeFromNextRow();
					}
					for (SettlementReport settlement : datas.values()) {
						if (settlement.getGrossAmount().equals(new BigDecimal(0.0D))) {
							continue;
						}
						RowContext c = sheetCtx.nextRow();
						c.skipCell().text(type.getDesc()).text(channelPartner.getName()).text(settlement.getName())
								.number(settlement.getGrossAmount()).number(settlement.getDiscountAmount())
								.number(settlement.getAmount());
						createTaxationData(c, data.getTaxations(), settlement);
						c.number(settlement.getRoundOff()).number(settlement.getTotal())
								.number(settlement.getNoOfBills());
					}
					if (hasValues) {
						sheetCtx.nextRow().nextRow().setTotalsDataRange(totalsData).setTextStyle(headerStyle).text("Total:").skipCell()
								.skipCell().skipCell().total(Formula.SUM).total(Formula.SUM).total(Formula.SUM)
								.total(Formula.SUM).total(Formula.SUM).total(Formula.SUM).total(Formula.SUM)
								.total(Formula.SUM).total(Formula.SUM).total(Formula.SUM).total(Formula.SUM);
						sheetCtx.nextRow();
					}
				}
			}
		}
	}

	//for rendering channel wise APC
    private void renderChannelWiseApc(SheetContext sheetCtx, SettlementTypeReportData data, Style headerStyle) {
        RowContext context = sheetCtx.nextRow();
        context.skipCell().setTextStyle(headerStyle).text("Channel Partner").setColumnWidth(25)
                .text("Net Sales").setColumnWidth(25)
                .text("Total Tickets").setColumnWidth(25)
                .text("APC").setColumnWidth(15);
        for (IdCodeName channelPartner : masterDataCache.getAllChannelPartners()) {
            Collection<SettlementReport> channelWiseReport = data.getMap().get(channelPartner.getId()).values();
            Integer totalTickets = 0;
            BigDecimal grossAmount = BigDecimal.ZERO;
            BigDecimal netSales = BigDecimal.ZERO;
            for(SettlementReport report : channelWiseReport){
                totalTickets += report.getNoOfBills();
                grossAmount = grossAmount.add(report.getGrossAmount());
                netSales = netSales.add(report.getTotal());
            }
            if (totalTickets > 0){
                BigDecimal apc = AppUtils.divide(grossAmount,BigDecimal.valueOf(totalTickets));
                RowContext c = sheetCtx.nextRow();
                c.skipCell().text(channelPartner.getName())
                        .number(netSales.setScale(2, BigDecimal.ROUND_HALF_UP))
                        .number(totalTickets)
                        .number(apc.setScale(2, BigDecimal.ROUND_HALF_UP));
            }
        }
        sheetCtx.nextRow();//add another row
    }

    private boolean hasValues(Map<Integer, SettlementReport> datas) {
		for (SettlementReport data : datas.values()) {
			if (data.getGrossAmount().equals(new BigDecimal(0.0D))) {
				continue;
			} else {
				return true;
			}
		}
		return false;
	}
}
