/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.view;

import com.stpl.tech.kettle.reports.dao.impl.ReportUtil;
import com.stpl.tech.kettle.reports.dao.impl.UnitConsumptionReportProvider;
import com.stpl.tech.kettle.reports.model.ProductDimensionConsumption;
import com.stpl.tech.kettle.reports.model.ProductPriceConsumption;
import com.stpl.tech.kettle.reports.model.ProductSourceConsumption;
import com.stpl.tech.util.TemplateRenderingException;
import com.stpl.tech.util.notification.AbstractTemplate;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;

public class ItemConsumptionSCMReport extends AbstractTemplate {
	private static final String DELIMITER = "|";
	private Unit unit;
	private List<ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>>> productConsumptions;
	private String basePath;
	private String generationTime = AppUtils.getCurrentTimeISTString();
	private UnitCategory category;
	private boolean isCOD;

	public ItemConsumptionSCMReport(String basePath, Unit unit, UnitConsumptionReportProvider consumptionProvider,
			UnitCategory category) {
		this.unit = unit;
		this.productConsumptions = ReportUtil.getConsumptions(consumptionProvider);
		this.basePath = basePath;
		this.category = category;
		this.isCOD = UnitCategory.COD.equals(category);
	}

	@Override
	public String getTemplatePath() {
		return "template/ItemConsumptionSCMReport.html";
	}

	public String getFileName() {
		return "ItemConsumptionSCMReport-" + unit.getName() + "-" + category.name() + "-"
				+ AppUtils.getCurrentTimeISTStringWithNoColons() + ".txt";
	}

	public String getFilepath() {
		return basePath + "/" + unit.getId() + "/reports/";
	}

	// TODO
	public String getContent() throws TemplateRenderingException {
		StringBuffer buffer = getHeader(true);
		StringBuffer comboBuffer = new StringBuffer();
		for (ProductSourceConsumption<ProductDimensionConsumption<ProductPriceConsumption>> product : productConsumptions) {
			if (product.getName().equals(category.name())) {
				for (ProductDimensionConsumption<ProductPriceConsumption> dimension : product.getAllItems()) {
					if(AppConstants.CHAAYOS_COMBO_PRODUCT_TYPE == dimension.getCategory()){
						appendToBuffer(comboBuffer, dimension);
					}else{
						appendToBuffer(buffer, dimension);
					}
				}
			}
		}
		buffer.append(getFooter(true));
		if(comboBuffer.length()>0){
			buffer.append("Combo Item Details\n");
			buffer.append(getHeader(false));
			buffer.append(comboBuffer);
			buffer.append(getFooter(false));
		}
		return buffer.toString();
	}

	private StringBuffer getHeader(boolean getReportHeader) {
		StringBuffer buffer = new StringBuffer();
		if(getReportHeader){
			buffer.append("Chaayos\n");
			buffer.append("Report : Item Consumption Report for category\n");
			buffer.append("Reporting On : " + generationTime + "\n");
		}
		buffer.append(
				"--------------------------------------------------------------------------------------------------------------\n");
		buffer.append(StringUtils.rightPad("Particulars", 31));
		buffer.append(DELIMITER);
		buffer.append(StringUtils.leftPad("Sale Qty", 15));
		buffer.append(DELIMITER);
		buffer.append(StringUtils.leftPad("Comp. Qty", 15));
		buffer.append(DELIMITER);
		buffer.append(StringUtils.leftPad("Amount", 15));
		buffer.append(DELIMITER);
		buffer.append(StringUtils.leftPad("% Of Section", 15));
		buffer.append(DELIMITER);
		buffer.append(StringUtils.leftPad("% Of Total", 14));
		buffer.append("\n");
		buffer.append(
				"--------------------------------------------------------------------------------------------------------------\n\n");
		buffer.append(StringUtils.rightPad("Category", 31));
		buffer.append(DELIMITER);
		buffer.append(StringUtils.leftPad("", 15));
		buffer.append(DELIMITER);
		buffer.append(StringUtils.leftPad("", 15));
		buffer.append(DELIMITER);
		buffer.append(StringUtils.leftPad("Amount", 15));
		buffer.append(DELIMITER);
		buffer.append(StringUtils.leftPad("", 15));
		buffer.append(DELIMITER);
		buffer.append(StringUtils.leftPad("", 14));
		buffer.append("\n");
		buffer.append(
				"--------------------------------------------------------------------------------------------------------------\n\n");

		return buffer;
	}

	private StringBuffer getFooter(boolean getReportFooter) {
		StringBuffer buffer = new StringBuffer();
		buffer.append("\n");
		buffer.append(
				"--------------------------------------------------------------------------------------------------------------\n");
		buffer.append(StringUtils.rightPad("TOTAL", 31));
		buffer.append(DELIMITER);
		buffer.append(StringUtils.leftPad("", 15));
		buffer.append(DELIMITER);
		buffer.append(StringUtils.leftPad("", 15));
		buffer.append(DELIMITER);
		buffer.append(StringUtils.leftPad("NA", 15));
		buffer.append(DELIMITER);
		buffer.append(StringUtils.leftPad("", 15));
		buffer.append(DELIMITER);
		buffer.append(StringUtils.leftPad("", 14));
		buffer.append("\n\n");
		if(getReportFooter){
			buffer.append("END OF Item Consumption Report for category\n\n\n");
			buffer.append("NOTE : Amounts indicated are exclusive of taxes, discounts and complimentary sale.\n");
		}
		buffer.append("\n\n");
		return buffer;
	}

	@Override
	public Map<String, Object> getData() {
		return null;
	}
	
	public void appendToBuffer(StringBuffer buffer, ProductDimensionConsumption<ProductPriceConsumption> dimension){
		int quantity = dimension.getQuantity();
		int complimentaryQuantity = dimension.getComplimentaryQuantity() + dimension.getCompositeQuantity();
		if (quantity == 0 && complimentaryQuantity == 0) {
			return;
		}
		String productDisplayName = dimension.getName().toUpperCase()
				+ (AppConstants.NO_DIMENSION_STRING.equals(dimension.getDimension()) ? ""
						: " " + dimension.getDimension().toUpperCase())
				+ (isCOD ? " " + UnitCategory.COD.name() : "");
		buffer.append(StringUtils.rightPad(productDisplayName, 31));
		buffer.append(DELIMITER);
		buffer.append(StringUtils.leftPad(String.valueOf(quantity), 15));
		buffer.append(DELIMITER);
		buffer.append(StringUtils.leftPad(String.valueOf(complimentaryQuantity), 15));
		buffer.append(DELIMITER);
		buffer.append(StringUtils.leftPad(String.valueOf(dimension.getAmount()), 15));
		buffer.append(DELIMITER);
		buffer.append(StringUtils.leftPad("NA", 15));
		buffer.append(DELIMITER);
		buffer.append(StringUtils.leftPad("NA", 14));
		buffer.append("\n");
	}
}
