/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.process;

import com.google.common.io.Files;
import com.stpl.tech.kettle.core.cache.MetadataCache;
import com.stpl.tech.kettle.core.data.budget.vo.BudgetDetail;
import com.stpl.tech.kettle.core.data.budget.vo.RevenueData;
import com.stpl.tech.kettle.customer.service.CardService;
import com.stpl.tech.kettle.customer.service.impl.EnvironmentProperties;
import com.stpl.tech.kettle.reports.core.ReportExecutor;
import com.stpl.tech.kettle.reports.core.ReportType;
import com.stpl.tech.kettle.reports.dao.impl.ManagerReportDataProvider;
import com.stpl.tech.kettle.reports.dao.impl.ReportFileData;
import com.stpl.tech.kettle.reports.dao.impl.SettlementReportInputData;
import com.stpl.tech.kettle.reports.dao.impl.SettlementReportOutputData;
import com.stpl.tech.kettle.reports.model.SettlementReportData;
import com.stpl.tech.kettle.reports.model.SettlementTypeReportData;
import com.stpl.tech.kettle.reports.model.UnitConsumption;
import com.stpl.tech.kettle.reports.view.CancelledReportView;
import com.stpl.tech.kettle.reports.view.ComplimentaryReportView;
import com.stpl.tech.kettle.reports.view.ConsumptionView;
import com.stpl.tech.kettle.reports.view.CreditReportView;
import com.stpl.tech.kettle.reports.view.DiscountReportView;
import com.stpl.tech.kettle.reports.view.ItemConsumptionSCMReport;
import com.stpl.tech.kettle.reports.view.ItemConsumptionView;
import com.stpl.tech.kettle.reports.view.PartnerReportView;
import com.stpl.tech.kettle.reports.view.ProductConsumptionView;
import com.stpl.tech.kettle.reports.view.ReprintedBillsView;
import com.stpl.tech.kettle.reports.view.SettlementReportView;
import com.stpl.tech.kettle.reports.view.SummaryView;
import com.stpl.tech.master.core.exception.DataUpdationException;
import com.stpl.tech.master.core.external.cache.EmployeeBasicDetail;
import com.stpl.tech.master.core.external.cache.MasterDataCache;
import com.stpl.tech.master.core.external.cache.TaxDataCache;
import com.stpl.tech.master.core.external.offer.service.OfferManagementExternalService;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.master.notification.QueryToExcelWriter;
import com.stpl.tech.master.tax.model.Taxation;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.subtlelib.poi.api.workbook.WorkbookContext;
import org.subtlelib.poi.impl.workbook.WorkbookContextFactory;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.Writer;
import java.util.Collection;
import java.util.Date;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;

public class SettlementReportController
		implements ReportExecutor<Unit, SettlementReportInputData, SettlementReportOutputData> {

	private final ManagerReportDataProvider provider;
	private final MetadataCache metadataCache;
	private final MasterDataCache masterCache;
	private final TaxDataCache taxCache;
	private final EnvironmentProperties props;
	private static final Logger LOG = LoggerFactory.getLogger(SettlementReportController.class);
	private final WorkbookContextFactory ctxFactory = WorkbookContextFactory.useXlsx();

	public SettlementReportController(MasterDataCache masterCache, MetadataCache metadataCache, TaxDataCache taxCache,
			EnvironmentProperties props,
			OfferManagementExternalService offerService, CardService cardService) {
		super();
		this.metadataCache = metadataCache;
		this.masterCache = masterCache;
		this.provider = new ManagerReportDataProvider(masterCache, metadataCache, offerService, cardService);

		this.taxCache = taxCache;
		this.props = props;
	}

	public SettlementReportOutputData execute(SettlementReportInputData data) throws DataUpdationException {
		try {
			if (data == null) {
				return null;
			}
			int stateId = data.getUnit().getLocation().getState().getId();
			Set<Taxation> stateTaxes = new TreeSet<>(taxCache.getSaleTaxations().get(stateId));
			if (data.isGeneratePnL()) {

			}
			provider.process(data, stateTaxes);
			WorkbookContext workbookCtx = ctxFactory.createWorkbook();
			summaryView(workbookCtx, provider);
			bySettlementType(workbookCtx, provider, stateTaxes);
			byTerminals(workbookCtx, provider, 0, stateTaxes);
			if (data.getUnit().getNoOfTerminals() > 1) {
				for (int i = 1; i <= data.getUnit().getNoOfTerminals(); i++) {
					byTerminals(workbookCtx, provider, i, stateTaxes);
				}
			}
			if (data.getUnit().getNoOfTakeawayTerminals() > 0) {
				for (int i = 1; i <= data.getUnit().getNoOfTakeawayTerminals(); i++) {
					byTerminals(workbookCtx, provider, AppConstants.TAKEAWAY_SEED + i, stateTaxes);
				}
			}
			byServiceType(workbookCtx, provider, stateTaxes);
			LOG.info("Report Generation For Service Type Done {}", data.getUnit().getId());
			byChannelPartner(workbookCtx, provider, stateTaxes);
			LOG.info("Report Generation For Channel Partner Done {}", data.getUnit().getId());
			creditBills(workbookCtx, provider);
			LOG.info("Report Generation For Credit Bills Done {}", data.getUnit().getId());
			discountedBills(workbookCtx, provider);
			LOG.info("Report Generation For Discounted Bills Done {}", data.getUnit().getId());
			complimentaryBills(workbookCtx, provider);
			LOG.info("Report Generation For Complimentary Bills Done {}", data.getUnit().getId());
			cancelledBills(workbookCtx, provider);
			LOG.info("Report Generation For Cancelled Bills Done {}", data.getUnit().getId());
			reprintedBills(workbookCtx, provider);
			LOG.info("Report Generation For Printed Bills Done {}", data.getUnit().getId());
			byCategoryConsumption(workbookCtx, provider);
			LOG.info("Report Generation For Category Consumption Done {}", data.getUnit().getId());
			bySubCategoryConsumption(workbookCtx, provider);
			LOG.info("Report Generation For Sub Category Consumption Done {}", data.getUnit().getId());
			byItemConsumption(workbookCtx, provider);
			LOG.info("Report Generation For Item Consumption Done {}", data.getUnit().getId());
			byProductConsumption(workbookCtx, provider);
			LOG.info("Report Generation For Product Consumption Done {}", data.getUnit().getId());
			if (data.getQueryReports() != null && data.getQueryReports().size() > 0) {
				QueryToExcelWriter writer = new QueryToExcelWriter(workbookCtx);
				for (String category : data.getQueryReports().keySet()) {
					writer.writeToSheet(category, data.getQueryReports().get(category), false);
				}
			}
			LOG.info("Report Generation For QueryToExcelWriter Done {}", data.getUnit().getId());
			SettlementReportOutputData output = getOutput(data.getBusinessDate(), data.getUnit());
			if (Objects.nonNull(output)){
				output.getReportFiles().add(generateManagersReport(data.getUnit(), workbookCtx));
				if (data.getUnit().getFamily().equals(UnitCategory.CAFE)) {
					output.getReportFiles().add(generateSCMReport(data, output, UnitCategory.CAFE));
					output.getReportFiles().add(generateSCMReport(data, output, UnitCategory.COD));
				} else if (data.getUnit().getFamily().equals(UnitCategory.DELIVERY)) {
					output.getReportFiles().add(generateSCMReport(data, output, UnitCategory.COD));
				} else if (data.getUnit().getFamily().equals(UnitCategory.TAKE_AWAY)) {
					output.getReportFiles().add(generateSCMReport(data, output, UnitCategory.TAKE_AWAY));
				}
				LOG.info("Report Generation For SettlementReportOutputData Done {}", data.getUnit().getId());
				addBudgetDetail(output);
			}
			return output;
		}catch (Exception e){
			LOG.error("Exception Caught while executing the report for unit {}",data.getUnit().getId(), e);
			return null;
		}
	}

	/**
	 * @param output
	 */
	private void addBudgetDetail(SettlementReportOutputData output) {
		BudgetDetail detail = new BudgetDetail();
		detail.setCommissions(provider.getRevenueDataProvider().getSalesCommission());
		detail.setRevenue(provider.getRevenueDataProvider().getRevenue());
		output.setBudgetDetail(detail);
	}

	private ReportFileData generateManagersReport(Unit unit, WorkbookContext workbookCtx) {
		ReportFileData managersReport = getManagersReportFileData(unit);
		try {
			Files.write(workbookCtx.toNativeBytes(), new File(managersReport.getFilePath()));
		} catch (IOException e) {
			LOG.error("Error in writing managers report for unit " + unit.getName(), e);
			managersReport.setGenerated(false);
		}
		return managersReport;
	}

	private ReportFileData generateSCMReport(SettlementReportInputData data, SettlementReportOutputData output,
			UnitCategory category) {
		ItemConsumptionSCMReport report = new ItemConsumptionSCMReport(props.getBasePath(), data.getUnit(),
				provider.getUnitReportProvider(), category);
		String fileDir = report.getFilepath();
		String fileName = report.getFileName();
		String filePath = fileDir + fileName;
		ReportFileData fileData = new ReportFileData(ReportType.SUPPLY_CHAIN, AppConstants.TEXT_MIME_TYPE, fileName,
				filePath, true);
		try {
			generateSCMReport(filePath, report.getContent());
		} catch (Exception e) {
			LOG.error("Error in writing SCM report for unit " + output.getUnit().getName(), e);
			fileData.setGenerated(false);
		}

		return fileData;
	}

	private void generateSCMReport(String filePath, String content) throws IOException {
		File f = new File(filePath);
		if (!f.exists()) {
			f.getParentFile().mkdirs();
		}
		Writer file = new FileWriter(f);
		file.write(content);
		file.flush();
		file.close();
	}

	private SettlementReportOutputData getOutput(Date businessDate, Unit unit) throws DataUpdationException {
		try {
			SettlementReportOutputData output = new SettlementReportOutputData();
			output.setEmailId(unit.getUnitEmail());
			output.setReportGenerated(true);
			output.setReportGenerationTime(AppUtils.getCurrentTimestamp());
			output.setUnit(unit);
			if (unit.getManagerId() != null && unit.getManagerEmail() != null && !unit.getManagerEmail().trim().isEmpty()) {
				output.setManagerEmailId(unit.getManagerEmail());
			}
			if (unit.getCafeManager() != null) {
				EmployeeBasicDetail e = masterCache.getEmployeeBasicDetail(unit.getCafeManager().getId());
				if (e != null && e.getEmailId() != null && !e.getEmailId().trim().isEmpty()) {
					output.getToEmails().add(e.getEmailId());
				}
			}
			output.setEnv(props.getEnvironmentType());
			output.setBusinessDate(businessDate);
			output.setReportName("Managers Report");
			return output;
		} catch (Exception e) {
			LOG.error("Error Faced While Generating Manager Report for UNit ::: {}",unit.getId(),e);
			return null;
		}
	}

	private ReportFileData getManagersReportFileData(Unit unit) {
		String fileName = "ManagersReport-" + unit.getName() + "-" + AppUtils.getCurrentTimeISTStringWithNoColons()
				+ ".xlsx";
		String fileDir = props.getBasePath() + "/" + unit.getId() + "/reports/";
		File file = new File(fileDir);
		if (file != null && !file.exists()) {
			boolean madeDirectories = file.mkdirs();
			LOG.info("made directories for the managers report  {} ::: {}", fileDir, madeDirectories);
		}
		String filePath = fileDir + fileName;
		ReportFileData fileData = new ReportFileData(ReportType.MANAGER, AppConstants.EXCEL_MIME_TYPE, fileName,
				filePath, true);
		return fileData;

	}

	private void summaryView(WorkbookContext workbookCtx, ManagerReportDataProvider provider) {
		UnitConsumption consumption = provider.getUnitReportProvider().getConsumptionData();
		RevenueData revenue = provider.getRevenueDataProvider().getRevenue();
		SummaryView view = new SummaryView("Unit Summary", "Item", revenue);
		view.render(workbookCtx, consumption);
	}

	private void bySettlementType(WorkbookContext workbookCtx, ManagerReportDataProvider provider,
			Collection<Taxation> stateTaxes) {
		SettlementReportView view = new SettlementReportView("Settlement Type", "Settlement");
		view.render(workbookCtx,
				new SettlementReportData(stateTaxes, provider.getSettlementReportProvider().getByPaymentType()));
	}

	private void byTerminals(WorkbookContext workbookCtx, ManagerReportDataProvider provider, int terminalId,
			Collection<Taxation> stateTaxes) {
		String sheetName = "Settlement Type" + (terminalId > 0 ? " T" + terminalId : " COD");
		SettlementReportView view = new SettlementReportView(sheetName, "Settlement");
		view.render(workbookCtx,
				new SettlementReportData(stateTaxes, provider.getSettlementReportProvider().getByTerminal(terminalId)));
	}

	private void byServiceType(WorkbookContext workbookCtx, ManagerReportDataProvider provider,
			Collection<Taxation> stateTaxes) {
		SettlementReportView view = new SettlementReportView("Service Type", "Service Type");
		view.render(workbookCtx, new SettlementReportData(stateTaxes, provider.getByServiceType()));
	}

	private void byChannelPartner(WorkbookContext workbookCtx, ManagerReportDataProvider provider,
			Collection<Taxation> stateTaxes) {
		PartnerReportView view = new PartnerReportView(masterCache, "Channel Partner", "Channel Partner");
		view.render(workbookCtx,
				new SettlementTypeReportData(stateTaxes, provider.getSettlementReportProvider().getChannelPartners()));
	}

	private void creditBills(WorkbookContext workbookCtx, ManagerReportDataProvider provider) {
		CreditReportView view = new CreditReportView(metadataCache, masterCache, "Credit Bills", "Credit");
		view.render(workbookCtx, provider.getCreditOrders());
	}

	private void discountedBills(WorkbookContext workbookCtx, ManagerReportDataProvider provider) {
		DiscountReportView view = new DiscountReportView(metadataCache, masterCache, "Discounted Bills", "Discount");
		view.render(workbookCtx, provider.getDiscountedOrders());
	}

	private void complimentaryBills(WorkbookContext workbookCtx, ManagerReportDataProvider provider) {
		ComplimentaryReportView view = new ComplimentaryReportView(metadataCache, masterCache, "Complimentary Bills",
				"Complimentary");
		view.render(workbookCtx, provider.getComplimentaryOrders());
	}

	private void reprintedBills(WorkbookContext workbookCtx, ManagerReportDataProvider provider2) {
		ReprintedBillsView view = new ReprintedBillsView(metadataCache, masterCache, "Reprinted Bills", "Reprint");
		view.render(workbookCtx, provider.getRePrintedBills());
	}

	private void cancelledBills(WorkbookContext workbookCtx, ManagerReportDataProvider provider) {
		CancelledReportView view = new CancelledReportView(metadataCache, masterCache, "Cancelled Bills", "Cancelled");
		view.render(workbookCtx, provider.getCancelledOrders());
	}

	private void byCategoryConsumption(WorkbookContext workbookCtx, ManagerReportDataProvider provider) {
		UnitConsumption consumption = provider.getUnitReportProvider().getConsumptionData();
		ConsumptionView view = new ConsumptionView("By Category", "Category", consumption, false);
		view.render(workbookCtx, consumption.getAllItems());
	}

	private void byItemConsumption(WorkbookContext workbookCtx, ManagerReportDataProvider provider) {
		UnitConsumption consumption = provider.getUnitReportProvider().getConsumptionData();
		ItemConsumptionView view = new ItemConsumptionView("Item Consumption", "Item");
		view.render(workbookCtx, consumption.getAllItems());
	}

	private void byProductConsumption(WorkbookContext workbookCtx, ManagerReportDataProvider provider) {
		UnitConsumption consumption = provider.getUnitReportProvider().getConsumptionData();
		ProductConsumptionView view = new ProductConsumptionView("Product Consumption", "Item");
		view.render(workbookCtx, consumption.getAllItems());
	}

	private void bySubCategoryConsumption(WorkbookContext workbookCtx, ManagerReportDataProvider provider) {
		UnitConsumption consumption = provider.getUnitReportProvider().getConsumptionData();
		ConsumptionView view = new ConsumptionView("By Sub Category", "Sub Category", consumption, true);
		view.render(workbookCtx, consumption.getAllItems());
	}
}
