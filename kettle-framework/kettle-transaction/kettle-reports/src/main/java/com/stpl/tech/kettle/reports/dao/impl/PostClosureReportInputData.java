/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.dao.impl;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.stpl.tech.kettle.reports.core.ReportInput;
import com.stpl.tech.master.domain.model.Unit;
import com.stpl.tech.util.notification.ReportDetailData;

public class PostClosureReportInputData implements ReportInput<Unit> {

	protected final Date businessDate;
	protected final Unit unit;
	protected Map<String, List<ReportDetailData>> queryReports;

	public PostClosureReportInputData(Date businessDate, Unit unit, Map<String, List<ReportDetailData>> queryReports) {
		super();
		this.businessDate = businessDate;
		this.unit = unit;
		this.queryReports = queryReports;
	}

	public Map<String, List<ReportDetailData>> getQueryReports() {
		return queryReports;
	}

	public void setQueryReports(Map<String, List<ReportDetailData>> queryReports) {
		this.queryReports = queryReports;
	}

	public Unit getUnit() {
		return unit;
	}

	public Date getBusinessDate() {
		return businessDate;
	}

	@Override
	public Unit getContext() {
		return unit;
	}

}
