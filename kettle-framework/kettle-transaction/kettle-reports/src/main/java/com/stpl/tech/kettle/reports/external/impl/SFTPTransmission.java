package com.stpl.tech.kettle.reports.external.impl;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.util.List;

import org.apache.commons.io.FileUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.jcraft.jsch.Channel;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import com.jcraft.jsch.SftpException;
import com.stpl.tech.kettle.reports.core.ReportOutput;
import com.stpl.tech.kettle.reports.dao.impl.ReportFileData;
import com.stpl.tech.kettle.reports.external.ReportTransmission;
import com.stpl.tech.master.core.external.notification.SlackNotification;
import com.stpl.tech.master.core.external.notification.SlackNotificationService;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import com.stpl.tech.util.EnvType;
import com.stpl.tech.util.notification.AttachmentData;

public class SFTPTransmission implements ReportTransmission {

	private static final Logger LOG = LoggerFactory.getLogger(SFTPTransmission.class);

	protected String server = "";
	protected int port = 22;
	protected String username = "";
	protected String password = "";
	protected String directory = "";

	protected ExternalReportOutputData output;

	public SFTPTransmission(String server, int port, String username, String password, String directory) {
		super();
		this.server = server;
		this.port = port;
		this.username = username;
		this.password = password;
		this.directory = directory;
	}

	public SFTPTransmission(ExternalReportInputData input) {
		this.server = input.getConfigMap().get("FTP_SERVER");
		this.username = input.getConfigMap().get("USERNAME");
		this.password = input.getConfigMap().get("PASSWORD");
		this.port = Integer.parseInt(input.getConfigMap().get("FTP_PORT"));
		this.directory = input.getConfigMap().get("DIRECTORY");
	}

	@Override
	public boolean transfer(ReportOutput output) {

		setReportOutput(output);

		if (AppUtils.isDev(getEnvironmentType())) {
			LOG.info("File Transfer cancelled for DEV Enviournment", getServer());
			return false;
		}

		LOG.info("Request for File Transfer to {}", getServer());
		Session session = null;
		Channel channel = null;
		ChannelSftp channelSftp = null;

		try {

			JSch jsch = new JSch();
			session = jsch.getSession(getUsername(), getServer(), getPort());
			session.setPassword(getPassword());
			java.util.Properties config = new java.util.Properties();
			config.put("StrictHostKeyChecking", "no");
			session.setConfig(config);
			session.connect();

			LOG.info("Connected to " + getServer() + ".");

			channel = session.openChannel("sftp");
			channel.connect();
			channelSftp = (ChannelSftp) channel;
			if (getDirectory() != null && !"".equals(getDirectory())) {
				channelSftp.cd(getDirectory());
				LOG.info("Directory Changed to : {} ", getDirectory());
			}
			LOG.info("Login Success.");

			LOG.info("Transmitting {} files", getReportFiles().size());

			for (ReportFileData fileData : getReportFiles()) {
				File file = new File(fileData.getFilePath());
				if (file.exists()) {
					LOG.info("Transmitting {} ", fileData.getFileName());
					channelSftp.put(new FileInputStream(file), file.getName());
					LOG.info("File uploaded successfully.");
				} else {
					LOG.error("File Not Found: {}", fileData.getFilePath());
				}
			}
		} catch (SftpException | FileNotFoundException | JSchException e) {
			LOG.error("error while transferring file using FTP", e);
			SlackNotificationService.getInstance().sendNotification(getEnvironmentType(), AppConstants.KETTLE,
					SlackNotification.SYSTEM_ERRORS,
					"FTP File Upload failed on server " + getServer() + "\nError: " + e.getMessage());

		} finally {
			if (channel != null && channel.isConnected()) {
				channel.disconnect();
			}
			if (session != null && session.isConnected()) {
				session.disconnect();
			}
		}
		return true;
	}

	@Override
	public boolean transfer(EnvType env, String tempDir, AttachmentData data) {

		if (AppUtils.isDev(getEnvironmentType())) {
			LOG.info("File Transfer cancelled for DEV Enviournment", getServer());
			return false;
		}

		LOG.info("Request for File Transfer to {}", getServer());
		String fileName = data.getFileName()
				+ (AppConstants.EXCEL_MIME_TYPE.equals(data.getContentType()) ? ".xlsx" : ".csv");
		Session session = null;
		Channel channel = null;
		ChannelSftp channelSftp = null;

		try {

			JSch jsch = new JSch();
			session = jsch.getSession(getUsername(), getServer(), getPort());
			session.setPassword(getPassword());
			java.util.Properties config = new java.util.Properties();
			config.put("StrictHostKeyChecking", "no");
			session.setConfig(config);
			session.connect();

			LOG.info("Connected to " + getServer() + ".");

			channel = session.openChannel("sftp");
			channel.connect();
			channelSftp = (ChannelSftp) channel;

			LOG.info("Login Success.");

			LOG.info("Transmitting {} files", getReportFiles().size());

			File file = new File(tempDir + "/" + fileName);
			if (!file.getParentFile().exists()) {
				file.getParentFile().mkdirs();
			}
			FileUtils.writeByteArrayToFile(file, data.getAttachment());
			if (file.exists()) {
				LOG.info("Transmitting {} ", file.getName());
				channelSftp.put(new FileInputStream(file), file.getName());
				LOG.info("File uploaded successfully.");
			} else {
				LOG.error("File Not Found: {}", file.getAbsolutePath());
			}
			try {
				if (file != null) {
					file.delete();
				}
			} catch (Exception e) {

			}

		} catch (SftpException | JSchException | IOException e) {
			LOG.error("error while transferring file using FTP", e);
			SlackNotificationService.getInstance().sendNotification(getEnvironmentType(), AppConstants.KETTLE,
					SlackNotification.SYSTEM_ERRORS,
					"FTP File Upload failed on server " + getServer() + "\nError: " + e.getMessage());

		} finally {
			if (channel != null && channel.isConnected()) {
				channel.disconnect();
			}
			if (session != null && session.isConnected()) {
				session.disconnect();
			}
		}
		return true;
	}

	public String getDirectory() {
		return directory;
	}

	public List<ReportFileData> getReportFiles() {
		return output.getReportFiles();
	}

	public void setReportOutput(ReportOutput reportOutput) {
		this.output = (ExternalReportOutputData) reportOutput;
	}

	public EnvType getEnvironmentType() {
		return output.getEnv();
	}

	public String getPassword() {
		return password;
	}

	public String getUsername() {
		return username;
	}

	public int getPort() {
		return port;
	}

	public String getServer() {
		return server;
	}
}
