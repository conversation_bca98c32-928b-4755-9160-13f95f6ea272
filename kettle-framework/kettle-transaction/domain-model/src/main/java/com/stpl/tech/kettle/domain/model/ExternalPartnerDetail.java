package com.stpl.tech.kettle.domain.model;

import com.stpl.tech.master.domain.model.PaymentMode;
import com.stpl.tech.master.domain.model.Product;

public class ExternalPartnerDetail {
	private int id;

	private String partnerName;

	private String partnerCode;

	private Product linkedProduct;

	private PaymentMode linkedPaymentMode;

	private Integer recipeId;

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getPartnerName() {
		return partnerName;
	}

	public void setPartnerName(String partnerName) {
		this.partnerName = partnerName;
	}

	public String getPartnerCode() {
		return partnerCode;
	}

	public void setPartnerCode(String partnerCode) {
		this.partnerCode = partnerCode;
	}

	public Product getLinkedProduct() {
		return linkedProduct;
	}

	public void setLinkedProduct(Product linkedProduct) {
		this.linkedProduct = linkedProduct;
	}

	public PaymentMode getLinkedPaymentMode() {
		return linkedPaymentMode;
	}

	public void setLinkedPaymentMode(PaymentMode linkedPaymentMode) {
		this.linkedPaymentMode = linkedPaymentMode;
	}

	public Integer getRecipeId() {
		return recipeId;
	}

	public void setRecipeId(Integer recipeId) {
		this.recipeId = recipeId;
	}

}
