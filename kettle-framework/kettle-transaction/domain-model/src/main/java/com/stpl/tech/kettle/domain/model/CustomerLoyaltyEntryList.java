package com.stpl.tech.kettle.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;
import java.util.List;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {

})
public class CustomerLoyaltyEntryList {

    List<CustomerLoyaltyEntry> list;

    Integer totalGiftedPoints;

    public Integer getTotalGiftedPoints() {
        return totalGiftedPoints;
    }

    public void setTotalGiftedPoints(Integer totalGiftedPoints) {
        this.totalGiftedPoints = totalGiftedPoints;
    }


    public CustomerLoyaltyEntryList(List<CustomerLoyaltyEntry> list) {
        this.list = list;
    }

    public CustomerLoyaltyEntryList() {
    }

    public List<CustomerLoyaltyEntry> getList() {
        return list;
    }

    public void setList(List<CustomerLoyaltyEntry> list) {
        this.list = list;
    }
}
