package com.stpl.tech.kettle.domain.model;

import java.util.Date;

public class SaveCustomerFavChaiResponse {

    private Integer customizationId; //id across which the customer's fav chai customisation is stored
    private SaveCustomerFavChaiResponseStatus saveCustomerFavChaiResponseStatus;
    private Integer customerId ;
    private Integer productId ;
    private Date createdAt;
    private boolean isSaved ;

    public SaveCustomerFavChaiResponse(Integer customizationId, SaveCustomerFavChaiResponseStatus saveCustomerFavChaiResponseStatus, Integer customerId, Integer productId, Date createdAt) {
        this.customizationId = customizationId;
        this.saveCustomerFavChaiResponseStatus = saveCustomerFavChaiResponseStatus;
        this.customerId = customerId;
        this.productId = productId;
        this.createdAt = createdAt;
    }

    public SaveCustomerFavChaiResponse() {

    }

    public Integer getCustomizationId() {
        return customizationId;
    }

    public void setCustomizationId(Integer customizationId) {
        this.customizationId = customizationId;
    }

    public SaveCustomerFavChaiResponseStatus getSaveCustomerFavChaiResponseStatus() {
        return saveCustomerFavChaiResponseStatus;
    }

    public void setSaveCustomerFavChaiResponseStatus(SaveCustomerFavChaiResponseStatus saveCustomerFavChaiResponseStatus) {
        this.saveCustomerFavChaiResponseStatus = saveCustomerFavChaiResponseStatus;
    }

    public Integer getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    public Integer getProductId() {
        return productId;
    }

    public void setProductId(Integer productId) {
        this.productId = productId;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public boolean getIsSaved() {
        return isSaved;
    }

    public void setIsSaved(boolean isSaved) {
        this.isSaved = isSaved;
    }
}
