package com.stpl.tech.kettle.xml.model;

import javax.xml.bind.annotation.XmlElement;

public class SaleRecord {

	private String ReceiptNumber = "";
	private String ReceiptDate = "";
	private String TransactionTime = "";
	private String InvoiceAmount = "";
	private String DiscountAmount = "";
	private String VATAmount = "";
	private String ServiceTaxAmount = "";
	private String ServiceChargeAmount = "";
	private String NetSale = "";
	private String PaymentMode = "";
	private String TransactionStatus = "";

	@XmlElement
	public String getReceiptNumber() {
		return ReceiptNumber;
	}

	public void setReceiptNumber(String receiptNumber) {
		ReceiptNumber = receiptNumber;
	}

	@XmlElement
	public String getReceiptDate() {
		return ReceiptDate;
	}

	public void setReceiptDate(String receiptDate) {
		ReceiptDate = receiptDate;
	}

	@XmlElement
	public String getTransactionTime() {
		return TransactionTime;
	}

	public void setTransactionTime(String transactionTime) {
		TransactionTime = transactionTime;
	}

	@XmlElement
	public String getInvoiceAmount() {
		return InvoiceAmount;
	}

	public void setInvoiceAmount(String invoiceAmount) {
		InvoiceAmount = invoiceAmount;
	}

	@XmlElement
	public String getDiscountAmount() {
		return DiscountAmount;
	}

	public void setDiscountAmount(String discountAmount) {
		DiscountAmount = discountAmount;
	}

	@XmlElement
	public String getVATAmount() {
		return VATAmount;
	}

	public void setVATAmount(String vATAmount) {
		VATAmount = vATAmount;
	}

	@XmlElement
	public String getServiceTaxAmount() {
		return ServiceTaxAmount;
	}

	public void setServiceTaxAmount(String serviceTaxAmount) {
		ServiceTaxAmount = serviceTaxAmount;
	}

	@XmlElement
	public String getServiceChargeAmount() {
		return ServiceChargeAmount;
	}

	public void setServiceChargeAmount(String serviceChargeAmount) {
		ServiceChargeAmount = serviceChargeAmount;
	}

	@XmlElement
	public String getNetSale() {
		return NetSale;
	}

	public void setNetSale(String netSale) {
		NetSale = netSale;
	}

	@XmlElement
	public String getPaymentMode() {
		return PaymentMode;
	}

	public void setPaymentMode(String paymentMode) {
		PaymentMode = paymentMode;
	}

	@XmlElement
	public String getTransactionStatus() {
		return TransactionStatus;
	}

	public void setTransactionStatus(String transactionStatus) {
		TransactionStatus = transactionStatus;
	}

}
