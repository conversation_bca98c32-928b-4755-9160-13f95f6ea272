/**
 * 
 */
package com.stpl.tech.kettle.reports.model;

import java.util.Collection;

import com.stpl.tech.master.tax.model.Taxation;

/**
 * <AUTHOR>
 *
 */
public class SettlementReportData {

	private Collection<Taxation> taxations;
	private Collection<SettlementReport> settlements;

	public SettlementReportData() {

	}

	/**
	 * @param taxations
	 * @param settlements
	 */
	public SettlementReportData(Collection<Taxation> taxations, Collection<SettlementReport> settlements) {
		super();
		this.taxations = taxations;
		this.settlements = settlements;
	}

	public Collection<Taxation> getTaxations() {
		return taxations;
	}

	public void setTaxations(Collection<Taxation> taxations) {
		this.taxations = taxations;
	}

	public Collection<SettlementReport> getSettlements() {
		return settlements;
	}

	public void setSettlements(Collection<SettlementReport> settlements) {
		this.settlements = settlements;
	}

}
