package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;
import java.util.Date;

public class WorkstationManualTask implements Serializable {

    private static final long serialVersionUID = -8090954369334980365L;
    private Integer manualTaskId;
    private String taskType;
    private Date orderTime;
    private Integer unitId;
    private Integer employeeId;
    private String  generatedOrderId;;
    private Integer productItemId;
    private String dimension;
    private Integer quantity;
    private Integer monkNumber;
    private String errorType;

    /*
        check for field specifying if order tat was low, medium, high (will be useful when checking correct order)
     */

    public Integer getManualTaskId() {
        return manualTaskId;
    }

    public void setManualTaskId(Integer manualTaskId) {
        this.manualTaskId = manualTaskId;
    }

    public String getTaskType() {
        return taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType;
    }

    public Date getOrderTime() {
        return orderTime;
    }

    public void setOrderTime(Date orderTime) {
        this.orderTime = orderTime;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Integer getEmployeeId() {
        return employeeId;
    }

    public void setEmployeeId(Integer employeeId) {
        this.employeeId = employeeId;
    }

    public String getGeneratedOrderId() {
        return generatedOrderId;
    }

    public void setGeneratedOrderId(String generatedOrderId) {
        this.generatedOrderId = generatedOrderId;
    }

    public Integer getProductItemId() {
        return productItemId;
    }

    public void setProductItemId(Integer productItemId) {
        this.productItemId = productItemId;
    }

    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    public Integer getQuantity() {
        return quantity;
    }

    public void setQuantity(Integer quantity) {
        this.quantity = quantity;
    }

    public Integer getMonkNumber() {
        return monkNumber;
    }

    public void setMonkNumber(Integer monkNumber) {
        this.monkNumber = monkNumber;
    }

    public String getErrorType() {
        return errorType;
    }

    public void setErrorType(String errorType) {
        this.errorType = errorType;
    }

    @Override
    public String toString() {
        return "WorkstationManualTask{" +
                "manualTaskId=" + manualTaskId +
                ", taskType='" + taskType + '\'' +
                ", orderTime=" + orderTime +
                ", unitId=" + unitId +
                ", employeeId=" + employeeId +
                ", generatedOrderId='" + generatedOrderId + '\'' +
                ", productItemId=" + productItemId +
                ", dimension='" + dimension + '\'' +
                ", quantity=" + quantity +
                ", monkNumber=" + monkNumber +
                ", errorType='" + errorType + '\'' +
                '}';
    }
}
