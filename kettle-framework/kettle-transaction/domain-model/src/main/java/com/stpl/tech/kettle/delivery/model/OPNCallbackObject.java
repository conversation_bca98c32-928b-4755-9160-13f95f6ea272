/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.01.11 at 02:51:51 PM IST 
//

package com.stpl.tech.kettle.delivery.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <p>
 * Java class for anonymous complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="order_code" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="state" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="pilot_name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="pilot_phone" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="pilot_latitude" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="pilot_longitude" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="time" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "orderCode", "state", "pilotName", "pilotPhone", "pilotLatitude", "pilotLongitude",
		"time" })
@XmlRootElement(name = "OPNCallbackObject")
@JsonIgnoreProperties(ignoreUnknown = true)
public class OPNCallbackObject implements CallbackObject {

	@XmlElement(name = "order_code", required = true)
	@JsonProperty("order_code")
	protected String orderCode;
	@Override
	public String toString() {
		return "OPNCallbackObject [orderCode=" + orderCode + ", state=" + state + ", pilotName=" + pilotName
				+ ", pilotPhone=" + pilotPhone + ", pilotLatitude=" + pilotLatitude + ", pilotLongitude="
				+ pilotLongitude + ", time=" + time + "]";
	}

	@XmlElement(required = true)
	@JsonProperty("state")
	protected String state;
	@XmlElement(name = "pilot_name", required = true)
	@JsonProperty("pilot_name")
	protected String pilotName;
	@XmlElement(name = "pilot_phone", required = true)
	@JsonProperty("pilot_phone")
	protected String pilotPhone;
	@XmlElement(name = "pilot_latitude", required = true)
	@JsonProperty("pilot_latitude")
	protected String pilotLatitude;
	@XmlElement(name = "pilot_longitude", required = true)
	@JsonProperty("pilot_longitude")
	protected String pilotLongitude;
	@XmlElement(required = true)
	@JsonProperty("time")
	protected String time;

	/**
	 * Gets the value of the orderCode property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getOrderCode() {
		return orderCode;
	}

	/**
	 * Sets the value of the orderCode property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setOrderCode(String value) {
		this.orderCode = value;
	}

	/**
	 * Gets the value of the state property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getState() {
		return state;
	}

	/**
	 * Sets the value of the state property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setState(String value) {
		this.state = value;
	}

	/**
	 * Gets the value of the pilotName property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getPilotName() {
		return pilotName;
	}

	/**
	 * Sets the value of the pilotName property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setPilotName(String value) {
		this.pilotName = value;
	}

	/**
	 * Gets the value of the pilotPhone property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getPilotPhone() {
		return pilotPhone;
	}

	/**
	 * Sets the value of the pilotPhone property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setPilotPhone(String value) {
		this.pilotPhone = value;
	}

	/**
	 * Gets the value of the pilotLatitude property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getPilotLatitude() {
		return pilotLatitude;
	}

	/**
	 * Sets the value of the pilotLatitude property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setPilotLatitude(String value) {
		this.pilotLatitude = value;
	}

	/**
	 * Gets the value of the pilotLongitude property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getPilotLongitude() {
		return pilotLongitude;
	}

	/**
	 * Sets the value of the pilotLongitude property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setPilotLongitude(String value) {
		this.pilotLongitude = value;
	}

	/**
	 * Gets the value of the time property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getTime() {
		return time;
	}

	/**
	 * Sets the value of the time property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setTime(String value) {
		this.time = value;
	}

}
