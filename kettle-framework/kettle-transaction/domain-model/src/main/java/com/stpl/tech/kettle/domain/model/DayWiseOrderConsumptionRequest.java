/*
 * Created By Shanmukh
 */

package com.stpl.tech.kettle.domain.model;

import com.stpl.tech.master.domain.model.Consumable;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Data
public class DayWiseOrderConsumptionRequest {
    private Integer unitId;
    private List<DayWiseOrderConsumptionItem> dayWiseOrderConsumptionItems;
    private Map<Integer, List<Consumable>> dayCloseEventWiseConsumption;
    private BigDecimal bulkOrderMinimLimit;
    private boolean excludeOnlyBulkOrders;
}
