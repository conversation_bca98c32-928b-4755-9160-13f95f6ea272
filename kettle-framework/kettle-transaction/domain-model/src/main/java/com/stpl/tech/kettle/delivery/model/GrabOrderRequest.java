/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.09.05 at 12:05:55 PM IST 
//


package com.stpl.tech.kettle.delivery.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="merchantId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="prepTime" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="customerPhone" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="customerName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="customerAddressLine1" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="customerAddressLine2" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="customerAddressLandMark" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="billAmount" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="billNo" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="orderType" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="amountPaidByClient" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="amountCollectedInCash" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "merchantId",
    "prepTime",
    "customerPhone",
    "customerName",
    "customerAddressLine1",
    "customerAddressLine2",
    "customerAddressLandMark",
    "billAmount",
    "billNo",
    "orderType",
    "amountPaidByClient",
    "amountCollectedInCash"
})
@XmlRootElement(name = "GrabOrderRequest")
public class GrabOrderRequest extends GrabGenericRequest implements GrabRequest{

    @XmlElement(required = true)
    protected String merchantId;
    @XmlElement(required = true)
    protected String prepTime;
    @XmlElement(required = true)
    protected String customerPhone;
    @XmlElement(required = true)
    protected String customerName;
    @XmlElement(required = true)
    protected String customerAddressLine1;
    @XmlElement(required = true)
    protected String customerAddressLine2;
    @XmlElement(required = true)
    protected String customerAddressLandMark;
    @XmlElement(required = true)
    protected String billAmount;
    @XmlElement(required = true)
    protected String billNo;
    @XmlElement(required = true)
    protected String orderType;
    protected float amountPaidByClient;
    protected float amountCollectedInCash;
    @XmlElement(required = true)
    protected String comments;

    /**
     * Gets the value of the merchantId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMerchantId() {
        return merchantId;
    }

    /**
     * Sets the value of the merchantId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMerchantId(String value) {
        this.merchantId = value;
    }

    /**
     * Gets the value of the prepTime property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getPrepTime() {
        return prepTime;
    }

    /**
     * Sets the value of the prepTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setPrepTime(String value) {
        this.prepTime = value;
    }

    /**
     * Gets the value of the customerPhone property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCustomerPhone() {
        return customerPhone;
    }

    /**
     * Sets the value of the customerPhone property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCustomerPhone(String value) {
        this.customerPhone = value;
    }

    /**
     * Gets the value of the customerName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCustomerName() {
        return customerName;
    }

    /**
     * Sets the value of the customerName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCustomerName(String value) {
        this.customerName = value;
    }

    /**
     * Gets the value of the customerAddressLine1 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCustomerAddressLine1() {
        return customerAddressLine1;
    }

    /**
     * Sets the value of the customerAddressLine1 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCustomerAddressLine1(String value) {
        this.customerAddressLine1 = value;
    }

    /**
     * Gets the value of the customerAddressLine2 property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCustomerAddressLine2() {
        return customerAddressLine2;
    }

    /**
     * Sets the value of the customerAddressLine2 property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCustomerAddressLine2(String value) {
        this.customerAddressLine2 = value;
    }

    /**
     * Gets the value of the customerAddressLandMark property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getCustomerAddressLandMark() {
        return customerAddressLandMark;
    }

    /**
     * Sets the value of the customerAddressLandMark property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCustomerAddressLandMark(String value) {
        this.customerAddressLandMark = value;
    }

    /**
     * Gets the value of the billAmount property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBillAmount() {
        return billAmount;
    }

    /**
     * Sets the value of the billAmount property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBillAmount(String value) {
        this.billAmount = value;
    }

    /**
     * Gets the value of the billNo property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBillNo() {
        return billNo;
    }

    /**
     * Sets the value of the billNo property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBillNo(String value) {
        this.billNo = value;
    }

    /**
     * Gets the value of the orderType property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOrderType() {
        return orderType;
    }

    /**
     * Sets the value of the orderType property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOrderType(String value) {
        this.orderType = value;
    }

    /**
     * Gets the value of the amountPaidByClient property.
     * 
     * @return
     *     possible object is
     *     {@link Float }
     *     
     */
    public float getAmountPaidByClient() {
        return amountPaidByClient;
    }

    /**
     * Sets the value of the amountPaidByClient property.
     * 
     * @param value
     *     allowed object is
     *     {@link Float }
     *     
     */
    public void setAmountPaidByClient(float value) {
        this.amountPaidByClient = value;
    }

    /**
     * Gets the value of the amountCollectedInCash property.
     * 
     * @return
     *     possible object is
     *     {@link Float }
     *     
     */
    public float getAmountCollectedInCash() {
        return amountCollectedInCash;
    }

    /**
     * Sets the value of the amountCollectedInCash property.
     * 
     * @param value
     *     allowed object is
     *     {@link Float }
     *     
     */
    public void setAmountCollectedInCash(float value) {
        this.amountCollectedInCash = value;
    }


    /**
     * Gets the value of the comments property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getComments() {
        return comments;
    }

    /**
     * Sets the value of the comments property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setComments(String value) {
        this.comments = value;
    }

}
