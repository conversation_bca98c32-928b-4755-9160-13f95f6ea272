package com.stpl.tech.kettle.domain.model;

import java.util.Date;

public class CustomerDineInView {

	private Integer customerId;
	private Integer activeDineInOrders;
	private Integer dineInOrders;
	private String availedSignupOffer;
	private Date signupOfferExpiryTime;
	private Date lastOrderTime;


	public CustomerDineInView() {

	}


	public CustomerDineInView(Integer customerId, Integer activeDineInOrders, Integer dineInOrders,
			String availedSignupOffer, Date signupOfferExpiryTime, Date lastOrderTime) {
		super();
		this.customerId = customerId;
		this.activeDineInOrders = activeDineInOrders;
		this.dineInOrders = dineInOrders;
		this.availedSignupOffer = availedSignupOffer;
		this.signupOfferExpiryTime = signupOfferExpiryTime;
		this.lastOrderTime = lastOrderTime;
	}


	public Integer getCustomerId() {
		return customerId;
	}


	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}


	public Integer getActiveDineInOrders() {
		return activeDineInOrders;
	}


	public void setActiveDineInOrders(Integer activeDineInOrders) {
		this.activeDineInOrders = activeDineInOrders;
	}


	public Integer getDineInOrders() {
		return dineInOrders;
	}


	public void setDineInOrders(Integer dineInOrders) {
		this.dineInOrders = dineInOrders;
	}


	public String getAvailedSignupOffer() {
		return availedSignupOffer;
	}


	public void setAvailedSignupOffer(String availedSignupOffer) {
		this.availedSignupOffer = availedSignupOffer;
	}


	public Date getSignupOfferExpiryTime() {
		return signupOfferExpiryTime;
	}


	public void setSignupOfferExpiryTime(Date signupOfferExpiryTime) {
		this.signupOfferExpiryTime = signupOfferExpiryTime;
	}

	public Date getLastOrderTime() {
		return lastOrderTime;
	}

	public void setLastOrderTime(Date lastOrderTime) {
		this.lastOrderTime = lastOrderTime;
	}
}
