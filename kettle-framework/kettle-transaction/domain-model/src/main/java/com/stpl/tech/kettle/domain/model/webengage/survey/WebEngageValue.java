/**
 * 
 */
package com.stpl.tech.kettle.domain.model.webengage.survey;

import java.io.Serializable;

/**
 * <AUTHOR>
 *
 */
public class WebEngageValue implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 4394913468247202661L;
	private Object value;
	private Object text;

	public Object getValue() {
		return value!=null ? value : getText();
	}

	public void setValue(Object value) {
		this.value = value;
	}

    public Object getText() {
        return text;
    }

    public void setText(Object text) {
        this.text = text;
    }

    @Override
	public String toString() {
		return "ClassPojo [value = " + getValue() + "]";
	}
}
