/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.model;

import java.util.Collection;
import java.util.Map;
import java.util.TreeMap;

public class ProductSourceConsumption<T> extends AbstractConsumption implements ConsumptionData {

	private Map<String, T> items = new TreeMap<String, T>();

	/**
	 * @param name
	 */
	public ProductSourceConsumption(String name) {
		super(name);
	}

	/**
	 * @return the items
	 */
	public Map<String, T> getItems() {
		return items;
	}

	/**
	 * @return the items
	 */
	public Collection<T> getAllItems() {
		return items.values();
	}

	/**
	 * @param items
	 *            the items to set
	 */
	public void setItems(Map<String, T> items) {
		this.items = items;
	}

}
