package com.stpl.tech.kettle.referral.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.math.BigDecimal;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "customerIds",
        "amount",
        "validityInDays",
        "sendNotification",
        "notificationTemplate",
        "publishEvent",
        "cashMetadataType",
        "cashTransactionCode",
        "lagInDays",
        "comment"
})
public class AllotChaayosCashRequestBody {

    @JsonProperty("customerIds")
    private List<Integer> customerIds;
    @JsonProperty("amount")
    private BigDecimal amount;
    @JsonProperty("validityInDays")
    private Integer validityInDays;
    @JsonProperty("sendNotification")
    private Boolean sendNotification;
    @JsonProperty("notificationTemplate")
    private String notificationTemplate;
    @JsonProperty("publishEvent")
    private Boolean publishEvent;
    @JsonProperty("cashMetadataType")
    private String cashMetadataType;
    @JsonProperty("cashTransactionCode")
    private String cashTransactionCode;
    @JsonProperty("lagInDays")
    private Integer lagInDays;
    @JsonProperty("comment")
    private String comment;

    public List<Integer> getCustomerIds() {
        return customerIds;
    }

    public void setCustomerIds(List<Integer> customerIds) {
        this.customerIds = customerIds;
    }

    public BigDecimal getAmount() {
        return amount;
    }

    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    public Integer getValidityInDays() {
        return validityInDays;
    }

    public void setValidityInDays(Integer validityInDays) {
        this.validityInDays = validityInDays;
    }

    public Boolean getSendNotification() {
        return sendNotification;
    }

    public void setSendNotification(Boolean sendNotification) {
        this.sendNotification = sendNotification;
    }

    public String getNotificationTemplate() {
        return notificationTemplate;
    }

    public void setNotificationTemplate(String notificationTemplate) {
        this.notificationTemplate = notificationTemplate;
    }

    public Boolean getPublishEvent() {
        return publishEvent;
    }

    public void setPublishEvent(Boolean publishEvent) {
        this.publishEvent = publishEvent;
    }

    public String getCashMetadataType() {
        return cashMetadataType;
    }

    public void setCashMetadataType(String cashMetadataType) {
        this.cashMetadataType = cashMetadataType;
    }

    public String getCashTransactionCode() {
        return cashTransactionCode;
    }

    public void setCashTransactionCode(String cashTransactionCode) {
        this.cashTransactionCode = cashTransactionCode;
    }

    public Integer getLagInDays() {
        return lagInDays;
    }

    public void setLagInDays(Integer lagInDays) {
        this.lagInDays = lagInDays;
    }

    public String getComment() {
        return comment;
    }

    public void setComment(String comment) {
        this.comment = comment;
    }

    @Override
    public String toString() {
        return "AllotChaayosCashRequestBody{" +
                "customerIds=" + customerIds +
                ", amount=" + amount +
                ", validityInDays=" + validityInDays +
                ", sendNotification=" + sendNotification +
                ", notificationTemplate='" + notificationTemplate + '\'' +
                ", publishEvent=" + publishEvent +
                ", cashMetadaType='" + cashMetadataType + '\'' +
                ", cashTransactionCode='" + cashTransactionCode + '\'' +
                ", lagInDays=" + lagInDays +
                ", comment='" + comment + '\'' +
                '}';
    }
}
