/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.02.16 at 04:20:25 PM IST
//

package com.stpl.tech.kettle.domain.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;

/**
 * <p>
 * Java class for EnquiryItem complex type.
 *
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 *
 * <pre>
 * &lt;complexType name="EnquiryItem"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="orderedQuantity" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="availableQuantity" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="replacementServed" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="linkedOrderId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="linkedCustomerId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="linkedUnitId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "EnquiryItem", propOrder = { "id", "name", "orderedQuantity", "availableQuantity", "replacementServed",
		"linkedOrderId", "linkedCustomerId", "linkedUnitId" })
@Document
public class EnquiryItem implements Serializable{
	/**
	 *
	 */
	private static final long serialVersionUID = 4039167939158169047L;

	@Id
	private String _id;

	/*@Version
	@JsonIgnore
	private Long version;

	*//**
	 * Added to avoid a runtime error whereby the detachAll property is checked
	 * for existence but not actually used.
	 *//*
	private String detachAll;*/

	@Field
	protected int id;
	@XmlElement(required = true)
	@Field
	protected String name;
	@Field
	protected int orderedQuantity;
	@Field
	protected int availableQuantity;
	@Field
	protected boolean replacementServed;
	@Field
	protected int linkedOrderId;
	@Field
	protected int linkedCustomerId;
	@Field
	protected int linkedUnitId;

	/*public String get_id() {
		return _id;
	}

	public void set_id(String _id) {
		this._id = _id;
	}

	public Long getVersion() {
		return version;
	}

	public void setVersion(Long version) {
		this.version = version;
	}

	public String getDetachAll() {
		return detachAll;
	}

	public void setDetachAll(String detachAll) {
		this.detachAll = detachAll;
	}*/

	/**
	 * Gets the value of the id property.
	 *
	 */
	public int getId() {
		return id;
	}

	/**
	 * Sets the value of the id property.
	 *
	 */
	public void setId(int value) {
		this.id = value;
	}

	/**
	 * Gets the value of the name property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getName() {
		return name;
	}

	/**
	 * Sets the value of the name property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setName(String value) {
		this.name = value;
	}

	/**
	 * Gets the value of the orderedQuantity property.
	 *
	 */
	public int getOrderedQuantity() {
		return orderedQuantity;
	}

	/**
	 * Sets the value of the orderedQuantity property.
	 *
	 */
	public void setOrderedQuantity(int value) {
		this.orderedQuantity = value;
	}

	/**
	 * Gets the value of the availableQuantity property.
	 *
	 */
	public int getAvailableQuantity() {
		return availableQuantity;
	}

	/**
	 * Sets the value of the availableQuantity property.
	 *
	 */
	public void setAvailableQuantity(int value) {
		this.availableQuantity = value;
	}

	/**
	 * Gets the value of the replacementServed property.
	 *
	 */
	public boolean isReplacementServed() {
		return replacementServed;
	}

	/**
	 * Sets the value of the replacementServed property.
	 *
	 */
	public void setReplacementServed(boolean value) {
		this.replacementServed = value;
	}

	/**
	 * Gets the value of the linkedOrderId property.
	 *
	 */
	public int getLinkedOrderId() {
		return linkedOrderId;
	}

	/**
	 * Sets the value of the linkedOrderId property.
	 *
	 */
	public void setLinkedOrderId(int value) {
		this.linkedOrderId = value;
	}

	/**
	 * Gets the value of the linkedCustomerId property.
	 *
	 */
	public int getLinkedCustomerId() {
		return linkedCustomerId;
	}

	/**
	 * Sets the value of the linkedCustomerId property.
	 *
	 */
	public void setLinkedCustomerId(int value) {
		this.linkedCustomerId = value;
	}

	/**
	 * Gets the value of the linkedUnitId property.
	 *
	 */
	public int getLinkedUnitId() {
		return linkedUnitId;
	}

	/**
	 * Sets the value of the linkedUnitId property.
	 *
	 */
	public void setLinkedUnitId(int value) {
		this.linkedUnitId = value;
	}

}
