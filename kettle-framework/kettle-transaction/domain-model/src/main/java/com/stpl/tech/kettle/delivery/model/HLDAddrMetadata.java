/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2015.12.23 at 12:37:04 PM IST 
//

package com.stpl.tech.kettle.delivery.model;


import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <p>
 * Java class for HLDAddrMetadata complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="HLDAddrMetadata"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="phone" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="lat" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="lng" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="delivery_subzone" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="address" type="{http://www.w3schools.com}HLDAddress"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "HLDAddrMetadata", propOrder = { "name", "phone", "lat", "lng", "delivery_Subzone", "address" })
public class HLDAddrMetadata {

	@XmlElement(required = true)
	@JsonProperty("name")
	protected String name;
	@XmlElement(required = true)
	@JsonProperty("phone")
	protected String phone;
	@XmlElement(required = true)
	@JsonProperty("lat")
	protected String lat;
	@XmlElement(required = true)
	@JsonProperty("lng")
	protected String lng;
	@XmlElement(name = "delivery_subzone", required = true)
	@JsonProperty("delivery_subzone")
	protected String delivery_Subzone;
	@XmlElement(required = true)
	@JsonProperty("address")
	protected HLDAddress address;

	/**
	 * Gets the value of the name property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getName() {
		return name;
	}

	/**
	 * Sets the value of the name property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setName(String value) {
		this.name = value;
	}

	/**
	 * Gets the value of the phone property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getPhone() {
		return phone;
	}

	/**
	 * Sets the value of the phone property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setPhone(String value) {
		this.phone = value;
	}

	/**
	 * Gets the value of the lat property.
	 * 
	 * 
	 */
	public String getLat() {
		return lat;
	}

	/**
	 * Sets the value of the lat property.
	 * 
	 * @param value
	 * 
	 */
	public void setLat(String value) {
		this.lat = value;
	}

	/**
	 * Gets the value of the lng property.
	 * 
	 * 
	 */
	public String getLng() {
		return lng;
	}

	/**
	 * Sets the value of the lng property.
	 * 
	 * @param value
	 * 
	 */
	public void setLng(String value) {
		this.lng = value;
	}

	/**
	 * Gets the value of the delivery_Subzone property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getDelivery_Subzone() {
		return delivery_Subzone;
	}

	/**
	 * Sets the value of the delivery_Subzone property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setDelivery_Subzone(String value) {
		this.delivery_Subzone = value;
	}

	/**
	 * Gets the value of the address property.
	 * 
	 * @return possible object is {@link HLDAddress }
	 * 
	 */
	public HLDAddress getAddress() {
		return address;
	}

	/**
	 * Sets the value of the address property.
	 * 
	 * @param value
	 *            allowed object is {@link HLDAddress }
	 * 
	 */
	public void setAddress(HLDAddress value) {
		this.address = value;
	}

}
