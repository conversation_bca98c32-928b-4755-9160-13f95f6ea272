package com.stpl.tech.kettle.domain.model;

import java.math.BigDecimal;
import java.util.List;

public class CartOrderItem {
    private Integer id;

    private Integer pid;

    private Integer qty;

    private String name;//productName

    private List<Variant> variant;

    private List<Addon> addon;

    private List<CartOrderItem> paidAddons; // list of paid addons

    private Integer subType;
    private BigDecimal price;
    private Dimension dim ;

    public Object clone() throws CloneNotSupportedException
    {
        return super.clone();
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public void setPid(Integer pid) {
        this.pid = pid;
    }

    public void setQty(Integer qty) {
        this.qty = qty;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setVariant(List<Variant> variant) {
        this.variant = variant;
    }

    public void setAddon(List<Addon> addon) {
        this.addon = addon;
    }

    public void setPaidAddons(List<CartOrderItem> paidAddons) {
        this.paidAddons = paidAddons;
    }

    public void setSubType(Integer subType) {
        this.subType = subType;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public void setDim(Dimension dim) {
        this.dim = dim;
    }

    public Dimension getDim() {
        return dim;
    }

    public Integer getId() {
        return id;
    }

    public Integer getPid() {
        return pid;
    }

    public Integer getQty() {
        return qty;
    }

    public String getName() {
        return name;
    }

    public List<Variant> getVariant() {
        return variant;
    }

    public List<Addon> getAddon() {
        return addon;
    }

    public List<CartOrderItem> getPaidAddons() {
        return paidAddons;
    }

    public Integer getSubType() {
        return subType;
    }

    public BigDecimal getPrice() {
        return price;
    }
}
