/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.02.11 at 01:20:58 PM IST 
//


package com.stpl.tech.kettle.domain.model;

import java.math.BigDecimal;
import java.util.Date;


public class PnlAdjustment {


    private int adjustmentId;
    private String pnlHeaderName;
    private String pnlHeaderDetail;
    private String pnlHeaderColumnName;
    private String pnlHeaderType;
    private String adjustmentType;
    private BigDecimal adjustmentValue;
    private int unitId;
    private String status;
    private int month;
    private int year;
    private int createdBy;
    private Date creationTime;
    private String createComment;
    private String createCommentText;
    private int rejectedBy;
    private Date rejectionTime;
    private String rejectComment;
    private String rejectCommentText;
    private int approvedBy;
    private Date approvalTime;
    private String approvedComment;
    private String approvedCommentText;
    private int cancelledBy;
    private Date cancellationTime;
    private String cancellationComment;
    private String cancellationCommentText;
    private Boolean isApplied;

    public int getAdjustmentId() {
        return adjustmentId;
    }

    public void setAdjustmentId(int adjustmentId) {
        this.adjustmentId = adjustmentId;
    }

    public String getPnlHeaderName() {
        return pnlHeaderName;
    }

    public void setPnlHeaderName(String pnlHeaderName) {
        this.pnlHeaderName = pnlHeaderName;
    }

    public String getPnlHeaderDetail() {
        return pnlHeaderDetail;
    }

    public void setPnlHeaderDetail(String pnlHeaderDetail) {
        this.pnlHeaderDetail = pnlHeaderDetail;
    }

    public String getPnlHeaderColumnName() {
        return pnlHeaderColumnName;
    }

    public void setPnlHeaderColumnName(String pnlHeaderColumnName) {
        this.pnlHeaderColumnName = pnlHeaderColumnName;
    }

    public String getPnlHeaderType() {
        return pnlHeaderType;
    }

    public void setPnlHeaderType(String pnlHeaderType) {
        this.pnlHeaderType = pnlHeaderType;
    }

    public String getAdjustmentType() {
        return adjustmentType;
    }

    public void setAdjustmentType(String adjustmentType) {
        this.adjustmentType = adjustmentType;
    }

    public BigDecimal getAdjustmentValue() {
        return adjustmentValue;
    }

    public void setAdjustmentValue(BigDecimal adjustmentValue) {
        this.adjustmentValue = adjustmentValue;
    }

    public int getUnitId() {
        return unitId;
    }

    public void setUnitId(int unitId) {
        this.unitId = unitId;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public int getMonth() {
        return month;
    }

    public void setMonth(int month) {
        this.month = month;
    }

    public int getYear() {
        return year;
    }

    public void setYear(int year) {
        this.year = year;
    }

    public int getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(int createdBy) {
        this.createdBy = createdBy;
    }

    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    public String getCreateComment() {
        return createComment;
    }

    public void setCreateComment(String createComment) {
        this.createComment = createComment;
    }

    public String getCreateCommentText() {
        return createCommentText;
    }

    public void setCreateCommentText(String createCommentText) {
        this.createCommentText = createCommentText;
    }

    public int getRejectedBy() {
        return rejectedBy;
    }

    public void setRejectedBy(int rejectedBy) {
        this.rejectedBy = rejectedBy;
    }

    public Date getRejectionTime() {
        return rejectionTime;
    }

    public void setRejectionTime(Date rejectionTime) {
        this.rejectionTime = rejectionTime;
    }

    public String getRejectComment() {
        return rejectComment;
    }

    public void setRejectComment(String rejectComment) {
        this.rejectComment = rejectComment;
    }

    public String getRejectCommentText() {
        return rejectCommentText;
    }

    public void setRejectCommentText(String rejectCommentText) {
        this.rejectCommentText = rejectCommentText;
    }

    public int getApprovedBy() {
        return approvedBy;
    }

    public void setApprovedBy(int approvedBy) {
        this.approvedBy = approvedBy;
    }

    public Date getApprovalTime() {
        return approvalTime;
    }

    public void setApprovalTime(Date approvalTime) {
        this.approvalTime = approvalTime;
    }

    public String getApprovedComment() {
        return approvedComment;
    }

    public void setApprovedComment(String approvedComment) {
        this.approvedComment = approvedComment;
    }

    public String getApprovedCommentText() {
        return approvedCommentText;
    }

    public void setApprovedCommentText(String approvedCommentText) {
        this.approvedCommentText = approvedCommentText;
    }

    public int getCancelledBy() {
        return cancelledBy;
    }

    public void setCancelledBy(int cancelledBy) {
        this.cancelledBy = cancelledBy;
    }

    public Date getCancellationTime() {
        return cancellationTime;
    }

    public void setCancellationTime(Date cancellationTime) {
        this.cancellationTime = cancellationTime;
    }

    public String getCancellationComment() {
        return cancellationComment;
    }

    public void setCancellationComment(String cancellationComment) {
        this.cancellationComment = cancellationComment;
    }

    public String getCancellationCommentText() {
        return cancellationCommentText;
    }

    public void setCancellationCommentText(String cancellationCommentText) {
        this.cancellationCommentText = cancellationCommentText;
    }

    public Boolean getApplied() {
        return isApplied;
    }

    public void setApplied(Boolean applied) {
        isApplied = applied;
    }
}
