/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2015.12.23 at 12:37:04 PM IST 
//

package com.stpl.tech.kettle.delivery.model;

import java.math.BigDecimal;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <p>
 * Java class for anonymous complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="order_id" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="task_id" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="merchant_id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="restaurant" type="{http://www.w3schools.com}HLDAddrMetadata"/&gt;
 *         &lt;element name="customer" type="{http://www.w3schools.com}HLDAddrMetadata"/&gt;
 *         &lt;element name="totalAmount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="collectable_amount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="delivery_type" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="pickup_time" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="slot_order" type="{http://www.w3schools.com}HLDSlotOrder"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "order_Id", "task_Id", "merchant_Id", "restaurant", "customer", "totalAmount",
		"collectable_Amount", "delivery_Type", "pickup_Time", "slot_Order" })
@XmlRootElement(name = "HLDRequest")
public class HLDRequest implements HLDGenericRequest {

	@XmlElement(name = "order_id", required = true)
	@JsonProperty("order_id")
	protected String order_Id;
	@XmlElement(name = "task_id", required = true)
	@JsonProperty("task_id")
	protected String task_Id;
	@XmlElement(name = "merchant_id")
	@JsonProperty("merchant_id")
	protected int merchant_Id;
	@XmlElement(required = true)
	@JsonProperty("restaurant")
	protected HLDAddrMetadata restaurant;
	@XmlElement(required = true)
	@JsonProperty("customer")
	protected HLDAddrMetadata customer;
	@XmlElement(name = "totalAmount", required = true)
	@JsonProperty("totalAmount")
	protected BigDecimal totalAmount;
	@XmlElement(name = "collectable_amount", required = true)
	@JsonProperty("collectable_amount")
	protected BigDecimal collectable_Amount;
	@XmlElement(name = "delivery_type", required = true)
	@JsonProperty("delivery_type")
	protected String delivery_Type;
	@XmlElement(name = "pickup_time", required = true)
	@JsonProperty("pickup_time")
	protected String pickup_Time;

	@XmlElement(name = "slot_order", required = true)
	@JsonProperty("slot_order")
	protected HLDSlotOrder slot_Order;

	/**
	 * Gets the value of the order_Id property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getOrder_Id() {
		return order_Id;
	}

	/**
	 * Sets the value of the order_Id property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setOrder_Id(String value) {
		this.order_Id = value;
	}

	/**
	 * Gets the value of the task_Id property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getTask_Id() {
		return task_Id;
	}

	/**
	 * Sets the value of the task_Id property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setTask_Id(String value) {
		this.task_Id = value;
	}

	/**
	 * Gets the value of the merchant_Id property.
	 * 
	 */
	public int getMerchant_Id() {
		return merchant_Id;
	}

	/**
	 * Sets the value of the merchant_Id property.
	 * 
	 */
	public void setMerchant_Id(int value) {
		this.merchant_Id = value;
	}

	/**
	 * Gets the value of the restaurant property.
	 * 
	 * @return possible object is {@link HLDAddrMetadata }
	 * 
	 */
	public HLDAddrMetadata getRestaurant() {
		return restaurant;
	}

	/**
	 * Sets the value of the restaurant property.
	 * 
	 * @param value
	 *            allowed object is {@link HLDAddrMetadata }
	 * 
	 */
	public void setRestaurant(HLDAddrMetadata value) {
		this.restaurant = value;
	}

	/**
	 * Gets the value of the customer property.
	 * 
	 * @return possible object is {@link HLDAddrMetadata }
	 * 
	 */
	public HLDAddrMetadata getCustomer() {
		return customer;
	}

	/**
	 * Sets the value of the customer property.
	 * 
	 * @param value
	 *            allowed object is {@link HLDAddrMetadata }
	 * 
	 */
	public void setCustomer(HLDAddrMetadata value) {
		this.customer = value;
	}

	/**
	 * Gets the value of the totalAmount property.
	 * 
	 * @return possible object is {@link BigDecimal }
	 * 
	 */
	public BigDecimal getTotalAmount() {
		return totalAmount;
	}

	/**
	 * Sets the value of the totalAmount property.
	 * 
	 * @param value
	 *            allowed object is {@link BigDecimal }
	 * 
	 */
	public void setTotalAmount(BigDecimal value) {
		this.totalAmount = value;
	}

	/**
	 * Gets the value of the collectable_Amount property.
	 * 
	 * @return possible object is {@link BigDecimal }
	 * 
	 */
	public BigDecimal getCollectable_Amount() {
		return collectable_Amount;
	}

	/**
	 * Sets the value of the collectable_Amount property.
	 * 
	 * @param value
	 *            allowed object is {@link BigDecimal }
	 * 
	 */
	public void setCollectable_Amount(BigDecimal value) {
		this.collectable_Amount = value;
	}

	/**
	 * Gets the value of the delivery_Type property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getDelivery_Type() {
		return delivery_Type;
	}

	/**
	 * Sets the value of the delivery_Type property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setDelivery_Type(String value) {
		this.delivery_Type = value;
	}

	/**
	 * Gets the value of the pickup_Time property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getPickup_Time() {
		return pickup_Time;
	}

	/**
	 * Sets the value of the pickup_Time property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setPickup_Time(String value) {
		this.pickup_Time = value;
	}

	/**
	 * Gets the value of the slot_Order property.
	 * 
	 * @return possible object is {@link HLDSlotOrder }
	 * 
	 */
	public HLDSlotOrder getSlot_Order() {
		return slot_Order;
	}

	/**
	 * Sets the value of the slot_Order property.
	 * 
	 * @param value
	 *            allowed object is {@link HLDSlotOrder }
	 * 
	 */
	public void setSlot_Order(HLDSlotOrder value) {
		this.slot_Order = value;
	}

}
