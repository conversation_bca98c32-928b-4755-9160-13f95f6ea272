package com.stpl.tech.kettle.domain.model;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.master.CustomJsonDateDeserializer;
import com.stpl.tech.master.domain.model.Adapter5;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "TableOrder", propOrder = { "orderId", "generateOrderId", "source", "status", "transactionDetail",
		"billingServerTime", "offerCode", "customerName" })
public class TableOrder {

	@XmlElement(required = true, type = Integer.class, nillable = true)
	protected Integer orderId;
	@XmlElement(required = true, nillable = true)
	protected String generateOrderId;
	@XmlElement(required = true, nillable = true)
	protected String source;
	@XmlElement(required = true, defaultValue = "INITIATED")
	@XmlSchemaType(name = "string")
	protected OrderStatus status;
	protected TransactionDetail transactionDetail;
	@XmlJavaTypeAdapter(Adapter5.class)
	@XmlSchemaType(name = "date")
	@JsonDeserialize(using = CustomJsonDateDeserializer.class)
	protected Date billingServerTime;
	@XmlElement(required = true, nillable = true)
	protected String offerCode;
	protected String customerName;

	protected List<TableOrderItem> items;

	public Integer getOrderId() {
		return orderId;
	}

	public void setOrderId(Integer orderId) {
		this.orderId = orderId;
	}

	public String getGenerateOrderId() {
		return generateOrderId;
	}

	public void setGenerateOrderId(String generateOrderId) {
		this.generateOrderId = generateOrderId;
	}

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public OrderStatus getStatus() {
		return status;
	}

	public void setStatus(OrderStatus status) {
		this.status = status;
	}

	public TransactionDetail getTransactionDetail() {
		return transactionDetail;
	}

	public void setTransactionDetail(TransactionDetail transactionDetail) {
		this.transactionDetail = transactionDetail;
	}

	public Date getBillingServerTime() {
		return billingServerTime;
	}

	public void setBillingServerTime(Date billingServerTime) {
		this.billingServerTime = billingServerTime;
	}

	public String getOfferCode() {
		return offerCode;
	}

	public void setOfferCode(String offerCode) {
		this.offerCode = offerCode;
	}

	public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	public List<TableOrderItem> getItems() {
		if (items == null) {
			items = new ArrayList<>();
		}
		return items;
	}

	public void setItems(List<TableOrderItem> items) {
		this.items = items;
	}

}
