package com.stpl.tech.kettle.domain.model;

import com.stpl.tech.master.domain.model.IdCodeName;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class OrderFeedbackMetadata implements Serializable {

    private static final long serialVersionUID = 8625865275466245363L;
    private Integer feedbackId;
    private Integer feedbackEventId;
    private int customerId;
    private IdCodeName feedbackAndReceiptDetails;
    private String feedbackUrl;

}
