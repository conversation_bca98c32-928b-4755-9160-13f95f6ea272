/**
 * 
 */
package com.stpl.tech.kettle.domain.model.webengage.survey;

import java.io.Serializable;

/**
 * <AUTHOR>
 *
 */
public class WebEngageQuestionResponse implements Serializable {

	/**
	* 
	*/
	private static final long serialVersionUID = -6365038134875037333L;

	private String order;

	private String questionId;

	private String questionText;

	private WebEngageValue value;

	public String getOrder() {
		return order;
	}

	public void setOrder(String order) {
		this.order = order;
	}

	public String getQuestionId() {
		return questionId;
	}

	public void setQuestionId(String questionId) {
		this.questionId = questionId;
	}

	public String getQuestionText() {
		return questionText;
	}

	public void setQuestionText(String questionText) {
		this.questionText = questionText;
	}

	public WebEngageValue getValue() {
		return value;
	}

	public void setValue(WebEngageValue value) {
		this.value = value;
	}

	@Override
	public String toString() {
		return "ClassPojo [order = " + order + ", questionId = " + questionId + ", questionText = " + questionText
				+ ", value = " + value + "]";
	}
}
