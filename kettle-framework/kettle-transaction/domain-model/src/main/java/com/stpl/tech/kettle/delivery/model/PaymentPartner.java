package com.stpl.tech.kettle.delivery.model;

import com.stpl.tech.util.EnvType;

public enum PaymentPartner {

	PAYTM(13,13), RAZORPAY(12,12), INGENICO(25,25), NONE(-1,-1), GPAY(27,27);

	private final int systemId;
	private final int devSystemID;

	private PaymentPartner(int devSystemId, int systemId) {
		this.devSystemID = devSystemId;
		this.systemId = systemId;
	}

	public int getSystemId(EnvType envType) {
		return (envType==null || envType.equals(EnvType.DEV)) ? devSystemID : systemId;
	}

	public static PaymentPartner getPartner(int id, EnvType env) {
		for (PaymentPartner partner : PaymentPartner.values()) {
			if (id == partner.getSystemId(env)) {
				return partner;
			}
		}
		return NONE;
	}

}
