package com.stpl.tech.kettle.domain.model;

import java.util.Date;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.stpl.tech.master.domain.model.Adapter2;

public class UnitMetadataEvent {

	protected int eventId;
	@XmlElement(required = true)
	protected String eventType;
	protected int month;
	protected int year;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter2.class)
	@XmlSchemaType(name = "date")
	protected Date createdAt;
	protected int createdBy;
	@XmlElement(required = true)
	protected String filePath;

	public int getEventId() {
		return eventId;
	}

	public void setEventId(int eventId) {
		this.eventId = eventId;
	}

	public String getEventType() {
		return eventType;
	}

	public void setEventType(String eventType) {
		this.eventType = eventType;
	}

	public int getMonth() {
		return month;
	}

	public void setMonth(int month) {
		this.month = month;
	}

	public int getYear() {
		return year;
	}

	public void setYear(int year) {
		this.year = year;
	}

	public Date getCreatedAt() {
		return createdAt;
	}

	public void setCreatedAt(Date createdAt) {
		this.createdAt = createdAt;
	}

	public int getCreatedBy() {
		return createdBy;
	}

	public void setCreatedBy(int createdBy) {
		this.createdBy = createdBy;
	}

	public String getFilePath() {
		return filePath;
	}

	public void setFilePath(String filePath) {
		this.filePath = filePath;
	}

}
