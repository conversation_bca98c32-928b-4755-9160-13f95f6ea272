/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2015.12.23 at 12:37:04 PM IST 
//

package com.stpl.tech.kettle.delivery.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <p>
 * Java class for SFXCustomer complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="SFXCustomer"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="contact_number" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="address_line_1" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="address_line_2" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="city" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="latitude" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *         &lt;element name="longitude" type="{http://www.w3.org/2001/XMLSchema}float"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SFXCustomer", propOrder = { "name", "contact_Number", "address_Line_1", "address_Line_2", "city",
		"latitude", "longitude" })
@JsonIgnoreProperties(ignoreUnknown=true)
public class SFXCustomer {

	@XmlElement(required = true)
	@JsonProperty("name")
	protected String name;
	@XmlElement(name = "contact_number", required = true)
	@JsonProperty("contact_number")
	protected String contact_Number;
	@XmlElement(name = "address_line_1", required = true)
	@JsonProperty("address_line_1")
	protected String address_Line_1;
	@XmlElement(name = "address_line_2", required = true)
	@JsonProperty("address_line_2")
	protected String address_Line_2;
	@XmlElement(required = true)
	@JsonProperty("city")
	protected String city;
	@JsonIgnore
	protected String latitude;
	@JsonIgnore
	protected String longitude;

	/**
	 * Gets the value of the name property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getName() {
		return name;
	}

	public SFXCustomer(String name, String contact_Number, String address_Line_1, String address_Line_2, String city) {
		super();
		this.name = name;
		this.contact_Number = contact_Number;
		this.address_Line_1 = address_Line_1;
		this.address_Line_2 = address_Line_2;
		this.city = city;
	}

	public SFXCustomer() {
		// TODO Auto-generated constructor stub
	}

	/**
	 * Sets the value of the name property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setName(String value) {
		this.name = value;
	}

	/**
	 * Gets the value of the contact_Number property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getContact_Number() {
		return contact_Number;
	}

	/**
	 * Sets the value of the contact_Number property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setContact_Number(String value) {
		this.contact_Number = value;
	}

	/**
	 * Gets the value of the address_Line_1 property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getAddress_Line_1() {
		return address_Line_1;
	}

	/**
	 * Sets the value of the address_Line_1 property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setAddress_Line_1(String value) {
		this.address_Line_1 = value;
	}

	/**
	 * Gets the value of the address_Line_2 property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getAddress_Line_2() {
		return address_Line_2;
	}

	/**
	 * Sets the value of the address_Line_2 property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setAddress_Line_2(String value) {
		this.address_Line_2 = value;
	}

	/**
	 * Gets the value of the city property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getCity() {
		return city;
	}

	/**
	 * Sets the value of the city property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setCity(String value) {
		this.city = value;
	}

	/**
	 * Gets the value of the latitude property.
	 * 
	 */
	public String getLatitude() {
		return latitude;
	}

	/**
	 * Sets the value of the latitude property.
	 * 
	 */
	public void setLatitude(String value) {
		this.latitude = value;
	}

	/**
	 * Gets the value of the longitude property.
	 * 
	 */
	public String getLongitude() {
		return longitude;
	}

	/**
	 * Sets the value of the longitude property.
	 * 
	 */
	public void setLongitude(String value) {
		this.longitude = value;
	}

}
