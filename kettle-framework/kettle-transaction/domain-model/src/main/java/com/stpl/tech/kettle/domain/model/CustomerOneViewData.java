package com.stpl.tech.kettle.domain.model;

import java.math.BigDecimal;
import java.util.Date;

public class CustomerOneViewData {

	private Integer customerId;
	private Integer brandId;
	private String firstName;
	private String contactNumber;
	private String countryCode;
	private String emailId;
	private String refCode;
	private String isNumberVerified;
	private String isEmailVerified;
	private String isDnd;
	private String isNew;
	private String isBlacklisted;
	private Integer activeDeliveryOrders;
	private Integer activeDineInOrders;
	private Integer activeWalletOrders;
	private Integer activeOverallOrders;
	private BigDecimal activeDeliverySales;
	private BigDecimal activeDineInSales;
	private BigDecimal activeOverallSales;
	private BigDecimal activeWalletSales;
	private BigDecimal activeDeliveryApc;
	private BigDecimal activeDineInApc;
	private BigDecimal activeOverallApc;
	private BigDecimal activeWalletApc;
	private BigDecimal activeDeliveryGmv;
	private BigDecimal activeDineInGmv;
	private BigDecimal activeOverallGmv;
	private BigDecimal activeDeliveryDiscount;
	private BigDecimal activeDineInDiscount;
	private BigDecimal activeOverallDiscount;
	private Integer deliveryOrders;
	private Integer dineInOrders;
	private Integer walletOrders;
	private Integer overallOrders;
	private BigDecimal deliverySales;
	private BigDecimal dineInSales;
	private BigDecimal overallSales;
	private BigDecimal walletSales;
	private BigDecimal deliveryApc;
	private BigDecimal dineInApc;
	private BigDecimal overallApc;
	private BigDecimal walletApc;
	private BigDecimal deliveryGmv;
	private BigDecimal dineInGmv;
	private BigDecimal overallGmv;
	private BigDecimal deliveryDiscount;
	private BigDecimal dineInDiscount;
	private BigDecimal overallDiscount;
	private Integer lastOrderId;
	private Integer lastDineOrderId;
	private Integer lastDeliveryOrderId;
	private Integer lastWalletOrderId;
	private Integer lastOrderTime;
	private Date lastDineInOrderTime;
	private Date lastDeliveryOrderTime;
	private Date lastWalletOrderTime;
	private String availedSignupOffer;
	private Date signupOfferExpiryTime;
	private Integer acquiredLoyaltyPoints;
	private Integer cumulativeLoyaltyPoints;

	
	public CustomerOneViewData() {
		
	}
	public CustomerOneViewData(Integer customerId, String firstName, String contactNumber,
			String countryCode, String emailId, String refCode, String isNumberVerified, String isEmailVerified,
			String isDnd, String isBlacklisted, Integer activeDeliveryOrders, Integer activeDineInOrders,
			Integer activeWalletOrders, Integer activeOverallOrders, BigDecimal activeDeliverySales,
			BigDecimal activeDineInSales, BigDecimal activeOverallSales, BigDecimal activeWalletSales,
			BigDecimal activeDeliveryGmv, BigDecimal activeDineInGmv, BigDecimal activeOverallGmv,
			Integer deliveryOrders, Integer dineInOrders, Integer walletOrders, Integer overallOrders,
			BigDecimal deliverySales, BigDecimal dineInSales, BigDecimal overallSales, BigDecimal walletSales,
			BigDecimal deliveryGmv, BigDecimal dineInGmv, BigDecimal overallGmv, Integer lastOrderId,
			Integer lastDineOrderId, Integer lastDeliveryOrderId, Integer lastWalletOrderId, Integer lastOrderTime,
			Date lastDineInOrderTime, Date lastDeliveryOrderTime, Date lastWalletOrderTime, String availedSignupOffer,
			Date signupOfferExpiryTime, Integer acquiredLoyaltyPoints, Integer cumulativeLoyaltyPoints) {
		super();
		this.customerId = customerId;
		this.firstName = firstName;
		this.contactNumber = contactNumber;
		this.countryCode = countryCode;
		this.emailId = emailId;
		this.refCode = refCode;
		this.isNumberVerified = isNumberVerified;
		this.isEmailVerified = isEmailVerified;
		this.isDnd = isDnd;
		this.isBlacklisted = isBlacklisted;
		this.activeDeliveryOrders = activeDeliveryOrders;
		this.activeDineInOrders = activeDineInOrders;
		this.activeWalletOrders = activeWalletOrders;
		this.activeOverallOrders = activeOverallOrders;
		this.activeDeliverySales = activeDeliverySales;
		this.activeDineInSales = activeDineInSales;
		this.activeOverallSales = activeOverallSales;
		this.activeWalletSales = activeWalletSales;
		this.activeDeliveryGmv = activeDeliveryGmv;
		this.activeDineInGmv = activeDineInGmv;
		this.activeOverallGmv = activeOverallGmv;
		this.deliveryOrders = deliveryOrders;
		this.dineInOrders = dineInOrders;
		this.walletOrders = walletOrders;
		this.overallOrders = overallOrders;
		this.deliverySales = deliverySales;
		this.dineInSales = dineInSales;
		this.overallSales = overallSales;
		this.walletSales = walletSales;
		this.deliveryGmv = deliveryGmv;
		this.dineInGmv = dineInGmv;
		this.overallGmv = overallGmv;
		this.lastOrderId = lastOrderId;
		this.lastDineOrderId = lastDineOrderId;
		this.lastDeliveryOrderId = lastDeliveryOrderId;
		this.lastWalletOrderId = lastWalletOrderId;
		this.lastOrderTime = lastOrderTime;
		this.lastDineInOrderTime = lastDineInOrderTime;
		this.lastDeliveryOrderTime = lastDeliveryOrderTime;
		this.lastWalletOrderTime = lastWalletOrderTime;
		this.availedSignupOffer = availedSignupOffer;
		this.signupOfferExpiryTime = signupOfferExpiryTime;
		this.acquiredLoyaltyPoints = acquiredLoyaltyPoints;
		this.cumulativeLoyaltyPoints = cumulativeLoyaltyPoints;
	}

	public Integer getCustomerId() {
		return customerId;
	}

	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}

	public Integer getBrandId() {
		return brandId;
	}

	public void setBrandId(Integer brandId) {
		this.brandId = brandId;
	}

	public String getFirstName() {
		return firstName;
	}

	public void setFirstName(String firstName) {
		this.firstName = firstName;
	}

	public String getContactNumber() {
		return contactNumber;
	}

	public void setContactNumber(String contactNumber) {
		this.contactNumber = contactNumber;
	}

	public String getCountryCode() {
		return countryCode;
	}

	public void setCountryCode(String countryCode) {
		this.countryCode = countryCode;
	}

	public String getEmailId() {
		return emailId;
	}

	public void setEmailId(String emailId) {
		this.emailId = emailId;
	}

	public String getRefCode() {
		return refCode;
	}

	public void setRefCode(String refCode) {
		this.refCode = refCode;
	}

	public String getIsNumberVerified() {
		return isNumberVerified;
	}

	public void setIsNumberVerified(String isNumberVerified) {
		this.isNumberVerified = isNumberVerified;
	}

	public String getIsEmailVerified() {
		return isEmailVerified;
	}

	public void setIsEmailVerified(String isEmailVerified) {
		this.isEmailVerified = isEmailVerified;
	}

	public String getIsDnd() {
		return isDnd;
	}

	public void setIsDnd(String isDnd) {
		this.isDnd = isDnd;
	}

	public String getIsNew() {
		return isNew;
	}

	public void setIsNew(String isNew) {
		this.isNew = isNew;
	}

	public String getIsBlacklisted() {
		return isBlacklisted;
	}

	public void setIsBlacklisted(String isBlacklisted) {
		this.isBlacklisted = isBlacklisted;
	}

	public Integer getActiveDeliveryOrders() {
		return activeDeliveryOrders;
	}

	public void setActiveDeliveryOrders(Integer activeDeliveryOrders) {
		this.activeDeliveryOrders = activeDeliveryOrders;
	}

	public Integer getActiveDineInOrders() {
		return activeDineInOrders;
	}

	public void setActiveDineInOrders(Integer activeDineInOrders) {
		this.activeDineInOrders = activeDineInOrders;
	}

	public Integer getActiveWalletOrders() {
		return activeWalletOrders;
	}

	public void setActiveWalletOrders(Integer activeWalletOrders) {
		this.activeWalletOrders = activeWalletOrders;
	}

	public Integer getActiveOverallOrders() {
		return activeOverallOrders;
	}

	public void setActiveOverallOrders(Integer activeOverallOrders) {
		this.activeOverallOrders = activeOverallOrders;
	}

	public BigDecimal getActiveDeliverySales() {
		return activeDeliverySales;
	}

	public void setActiveDeliverySales(BigDecimal activeDeliverySales) {
		this.activeDeliverySales = activeDeliverySales;
	}

	public BigDecimal getActiveDineInSales() {
		return activeDineInSales;
	}

	public void setActiveDineInSales(BigDecimal activeDineInSales) {
		this.activeDineInSales = activeDineInSales;
	}

	public BigDecimal getActiveOverallSales() {
		return activeOverallSales;
	}

	public void setActiveOverallSales(BigDecimal activeOverallSales) {
		this.activeOverallSales = activeOverallSales;
	}

	public BigDecimal getActiveWalletSales() {
		return activeWalletSales;
	}

	public void setActiveWalletSales(BigDecimal activeWalletSales) {
		this.activeWalletSales = activeWalletSales;
	}

	public BigDecimal getActiveDeliveryApc() {
		return activeDeliveryApc;
	}

	public void setActiveDeliveryApc(BigDecimal activeDeliveryApc) {
		this.activeDeliveryApc = activeDeliveryApc;
	}

	public BigDecimal getActiveDineInApc() {
		return activeDineInApc;
	}

	public void setActiveDineInApc(BigDecimal activeDineInApc) {
		this.activeDineInApc = activeDineInApc;
	}

	public BigDecimal getActiveOverallApc() {
		return activeOverallApc;
	}

	public void setActiveOverallApc(BigDecimal activeOverallApc) {
		this.activeOverallApc = activeOverallApc;
	}

	public BigDecimal getActiveWalletApc() {
		return activeWalletApc;
	}

	public void setActiveWalletApc(BigDecimal activeWalletApc) {
		this.activeWalletApc = activeWalletApc;
	}

	public BigDecimal getActiveDeliveryGmv() {
		return activeDeliveryGmv;
	}

	public void setActiveDeliveryGmv(BigDecimal activeDeliveryGmv) {
		this.activeDeliveryGmv = activeDeliveryGmv;
	}

	public BigDecimal getActiveDineInGmv() {
		return activeDineInGmv;
	}

	public void setActiveDineInGmv(BigDecimal activeDineInGmv) {
		this.activeDineInGmv = activeDineInGmv;
	}

	public BigDecimal getActiveOverallGmv() {
		return activeOverallGmv;
	}

	public void setActiveOverallGmv(BigDecimal activeOverallGmv) {
		this.activeOverallGmv = activeOverallGmv;
	}

	public BigDecimal getActiveDeliveryDiscount() {
		return activeDeliveryDiscount;
	}

	public void setActiveDeliveryDiscount(BigDecimal activeDeliveryDiscount) {
		this.activeDeliveryDiscount = activeDeliveryDiscount;
	}

	public BigDecimal getActiveDineInDiscount() {
		return activeDineInDiscount;
	}

	public void setActiveDineInDiscount(BigDecimal activeDineInDiscount) {
		this.activeDineInDiscount = activeDineInDiscount;
	}

	public BigDecimal getActiveOverallDiscount() {
		return activeOverallDiscount;
	}

	public void setActiveOverallDiscount(BigDecimal activeOverallDiscount) {
		this.activeOverallDiscount = activeOverallDiscount;
	}

	public Integer getDeliveryOrders() {
		return deliveryOrders;
	}

	public void setDeliveryOrders(Integer deliveryOrders) {
		this.deliveryOrders = deliveryOrders;
	}

	public Integer getDineInOrders() {
		return dineInOrders;
	}

	public void setDineInOrders(Integer dineInOrders) {
		this.dineInOrders = dineInOrders;
	}

	public Integer getWalletOrders() {
		return walletOrders;
	}

	public void setWalletOrders(Integer walletOrders) {
		this.walletOrders = walletOrders;
	}

	public Integer getOverallOrders() {
		return overallOrders;
	}

	public void setOverallOrders(Integer overallOrders) {
		this.overallOrders = overallOrders;
	}

	public BigDecimal getDeliverySales() {
		return deliverySales;
	}

	public void setDeliverySales(BigDecimal deliverySales) {
		this.deliverySales = deliverySales;
	}

	public BigDecimal getDineInSales() {
		return dineInSales;
	}

	public void setDineInSales(BigDecimal dineInSales) {
		this.dineInSales = dineInSales;
	}

	public BigDecimal getOverallSales() {
		return overallSales;
	}

	public void setOverallSales(BigDecimal overallSales) {
		this.overallSales = overallSales;
	}

	public BigDecimal getWalletSales() {
		return walletSales;
	}

	public void setWalletSales(BigDecimal walletSales) {
		this.walletSales = walletSales;
	}

	public BigDecimal getDeliveryApc() {
		return deliveryApc;
	}

	public void setDeliveryApc(BigDecimal deliveryApc) {
		this.deliveryApc = deliveryApc;
	}

	public BigDecimal getDineInApc() {
		return dineInApc;
	}

	public void setDineInApc(BigDecimal dineInApc) {
		this.dineInApc = dineInApc;
	}

	public BigDecimal getOverallApc() {
		return overallApc;
	}

	public void setOverallApc(BigDecimal overallApc) {
		this.overallApc = overallApc;
	}

	public BigDecimal getWalletApc() {
		return walletApc;
	}

	public void setWalletApc(BigDecimal walletApc) {
		this.walletApc = walletApc;
	}

	public BigDecimal getDeliveryGmv() {
		return deliveryGmv;
	}

	public void setDeliveryGmv(BigDecimal deliveryGmv) {
		this.deliveryGmv = deliveryGmv;
	}

	public BigDecimal getDineInGmv() {
		return dineInGmv;
	}

	public void setDineInGmv(BigDecimal dineInGmv) {
		this.dineInGmv = dineInGmv;
	}

	public BigDecimal getOverallGmv() {
		return overallGmv;
	}

	public void setOverallGmv(BigDecimal overallGmv) {
		this.overallGmv = overallGmv;
	}

	public BigDecimal getDeliveryDiscount() {
		return deliveryDiscount;
	}

	public void setDeliveryDiscount(BigDecimal deliveryDiscount) {
		this.deliveryDiscount = deliveryDiscount;
	}

	public BigDecimal getDineInDiscount() {
		return dineInDiscount;
	}

	public void setDineInDiscount(BigDecimal dineInDiscount) {
		this.dineInDiscount = dineInDiscount;
	}

	public BigDecimal getOverallDiscount() {
		return overallDiscount;
	}

	public void setOverallDiscount(BigDecimal overallDiscount) {
		this.overallDiscount = overallDiscount;
	}

	public Integer getLastOrderId() {
		return lastOrderId;
	}

	public void setLastOrderId(Integer lastOrderId) {
		this.lastOrderId = lastOrderId;
	}

	public Integer getLastDineOrderId() {
		return lastDineOrderId;
	}

	public void setLastDineOrderId(Integer lastDineOrderId) {
		this.lastDineOrderId = lastDineOrderId;
	}

	public Integer getLastDeliveryOrderId() {
		return lastDeliveryOrderId;
	}

	public void setLastDeliveryOrderId(Integer lastDeliveryOrderId) {
		this.lastDeliveryOrderId = lastDeliveryOrderId;
	}

	public Integer getLastWalletOrderId() {
		return lastWalletOrderId;
	}

	public void setLastWalletOrderId(Integer lastWalletOrderId) {
		this.lastWalletOrderId = lastWalletOrderId;
	}

	public Integer getLastOrderTime() {
		return lastOrderTime;
	}

	public void setLastOrderTime(Integer lastOrderTime) {
		this.lastOrderTime = lastOrderTime;
	}

	public Date getLastDineInOrderTime() {
		return lastDineInOrderTime;
	}

	public void setLastDineInOrderTime(Date lastDineInOrderTime) {
		this.lastDineInOrderTime = lastDineInOrderTime;
	}

	public Date getLastDeliveryOrderTime() {
		return lastDeliveryOrderTime;
	}

	public void setLastDeliveryOrderTime(Date lastDeliveryOrderTime) {
		this.lastDeliveryOrderTime = lastDeliveryOrderTime;
	}

	public Date getLastWalletOrderTime() {
		return lastWalletOrderTime;
	}

	public void setLastWalletOrderTime(Date lastWalletOrderTime) {
		this.lastWalletOrderTime = lastWalletOrderTime;
	}

	public String getAvailedSignupOffer() {
		return availedSignupOffer;
	}

	public void setAvailedSignupOffer(String availedSignupOffer) {
		this.availedSignupOffer = availedSignupOffer;
	}

	public Date getSignupOfferExpiryTime() {
		return signupOfferExpiryTime;
	}

	public void setSignupOfferExpiryTime(Date signupOfferExpiryTime) {
		this.signupOfferExpiryTime = signupOfferExpiryTime;
	}

	public Integer getAcquiredLoyaltyPoints() {
		return acquiredLoyaltyPoints;
	}

	public void setAcquiredLoyaltyPoints(Integer acquiredLoyaltyPoints) {
		this.acquiredLoyaltyPoints = acquiredLoyaltyPoints;
	}

	public Integer getCumulativeLoyaltyPoints() {
		return cumulativeLoyaltyPoints;
	}

	public void setCumulativeLoyaltyPoints(Integer cumulativeLoyaltyPoints) {
		this.cumulativeLoyaltyPoints = cumulativeLoyaltyPoints;
	}

	@Override
	public String toString() {
		return "CustomerOneViewData [customerId=" + customerId + ", isNew=" + isNew + ", brandId=" + brandId
				+ ", isNumberVerified=" + isNumberVerified + ", isEmailVerified=" + isEmailVerified + ", isDnd=" + isDnd
				+ ", isBlacklisted=" + isBlacklisted + ", activeDeliveryOrders=" + activeDeliveryOrders
				+ ", activeDineInOrders=" + activeDineInOrders + ", activeWalletOrders=" + activeWalletOrders
				+ ", activeOverallOrders=" + activeOverallOrders + ", activeDeliverySales=" + activeDeliverySales
				+ ", activeDineInSales=" + activeDineInSales + ", activeOverallSales=" + activeOverallSales
				+ ", activeWalletSales=" + activeWalletSales + ", activeDeliveryApc=" + activeDeliveryApc
				+ ", activeDineInApc=" + activeDineInApc + ", activeOverallApc=" + activeOverallApc
				+ ", activeWalletApc=" + activeWalletApc + ", activeDeliveryGmv=" + activeDeliveryGmv
				+ ", activeDineInGmv=" + activeDineInGmv + ", activeOverallGmv=" + activeOverallGmv
				+ ", activeDeliveryDiscount=" + activeDeliveryDiscount + ", activeDineInDiscount="
				+ activeDineInDiscount + ", activeOverallDiscount=" + activeOverallDiscount + ", deliveryOrders="
				+ deliveryOrders + ", dineInOrders=" + dineInOrders + ", walletOrders=" + walletOrders
				+ ", overallOrders=" + overallOrders + ", deliverySales=" + deliverySales + ", dineInSales="
				+ dineInSales + ", overallSales=" + overallSales + ", walletSales=" + walletSales + ", deliveryApc="
				+ deliveryApc + ", dineInApc=" + dineInApc + ", overallApc=" + overallApc + ", walletApc=" + walletApc
				+ ", deliveryGmv=" + deliveryGmv + ", dineInGmv=" + dineInGmv + ", overallGmv=" + overallGmv
				+ ", deliveryDiscount=" + deliveryDiscount + ", dineInDiscount=" + dineInDiscount + ", overallDiscount="
				+ overallDiscount + ", lastOrderId=" + lastOrderId + ", lastDineOrderId=" + lastDineOrderId
				+ ", lastDeliveryOrderId=" + lastDeliveryOrderId + ", lastWalletOrderId=" + lastWalletOrderId
				+ ", lastOrderTime=" + lastOrderTime + ", lastDineInOrderTime=" + lastDineInOrderTime
				+ ", lastDeliveryOrderTime=" + lastDeliveryOrderTime + ", lastWalletOrderTime=" + lastWalletOrderTime
				+ ", availedSignupOffer=" + availedSignupOffer + ", signupOfferExpiryTime=" + signupOfferExpiryTime
				+ ", acquiredLoyaltyPoints=" + acquiredLoyaltyPoints + ", cumulativeLoyaltyPoints="
				+ cumulativeLoyaltyPoints + "]";
	}

}
