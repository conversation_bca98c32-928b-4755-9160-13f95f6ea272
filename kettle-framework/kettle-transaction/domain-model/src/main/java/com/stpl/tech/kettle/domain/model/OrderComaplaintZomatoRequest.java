package com.stpl.tech.kettle.domain.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "external_order_id",
        "order_id",
        "reference_id",
        "image_urls",
        "complaint_message",
        "complaint_reason",
        "created_at",
        "expired_at",
        "ordered_items",
        "customer_complaints_count",
        "repeat_customer_count",
        "refund_options",
        "min_custom_refund"
})
public class OrderComaplaintZomatoRequest implements Serializable {

    @JsonProperty("external_order_id")
    private String externalOrderId;

    @JsonProperty("order_id")
    private Integer orderId;

    @JsonProperty("reference_id")
    private String referenceId;

    @JsonProperty("image_urls")
    private List<String> imageUrlsList = new ArrayList<>();

    @JsonProperty("complaint_message")
    private String complaintMessage;

    @JsonProperty("complaint_reason")
    private String complaintReason;

    @JsonProperty("created_at")
    private Date createdAt;

    @JsonProperty("expired_at")
    private Date expiredAt;

    @JsonProperty("ordered_items")
    private List<OrderItemRequest> orderedItemsList=new ArrayList<>();

    @JsonProperty("customer_complaints_count")
    private Integer customerComplaintsCount;

    @JsonProperty("repeat_customer_count")
    private Integer repeatCustomerCount;

    @JsonProperty("refund_options")
    private List<RefundOptionsRequest> refundOptionsList= new ArrayList<>();

    @JsonProperty("min_custom_refund")
    private Float minCustomRefund;

    public String getExternalOrderId() {
        return externalOrderId;
    }

    public void setExternalOrderId(String externalOrderId) {
        this.externalOrderId = externalOrderId;
    }

    public Integer getOrderId() {
        return orderId;
    }

    public void setOrderId(Integer orderId) {
        this.orderId = orderId;
    }

    public String getReferenceId() {
        return referenceId;
    }

    public void setReferenceId(String referenceId) {
        this.referenceId = referenceId;
    }

    public List<String> getImageUrlsList() {
        return imageUrlsList;
    }

    public void setImageUrlsList(List<String> imageUrlsList) {
        this.imageUrlsList = imageUrlsList;
    }

    public String getComplaintMessage() {
        return complaintMessage;
    }

    public void setComplaintMessage(String complaintMessage) {
        this.complaintMessage = complaintMessage;
    }

    public String getComplaintReason() {
        return complaintReason;
    }

    public void setComplaintReason(String complaintReason) {
        this.complaintReason = complaintReason;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getExpiredAt() {
        return expiredAt;
    }

    public void setExpiredAt(Date expiredAt) {
        this.expiredAt = expiredAt;
    }

    public List<OrderItemRequest> getOrderedItemsList() {
        return orderedItemsList;
    }

    public void setOrderedItemsList(List<OrderItemRequest> orderedItemsList) {
        this.orderedItemsList = orderedItemsList;
    }

    public Integer getCustomerComplaintsCount() {
        return customerComplaintsCount;
    }

    public void setCustomerComplaintsCount(Integer customerComplaintsCount) {
        this.customerComplaintsCount = customerComplaintsCount;
    }

    public Integer getRepeatCustomerCount() {
        return repeatCustomerCount;
    }

    public void setRepeatCustomerCount(Integer repeatCustomerCount) {
        this.repeatCustomerCount = repeatCustomerCount;
    }

    public List<RefundOptionsRequest> getRefundOptionsList() {
        return refundOptionsList;
    }

    public void setRefundOptionsList(List<RefundOptionsRequest> refundOptionsList) {
        this.refundOptionsList = refundOptionsList;
    }

    public Float getMinCustomRefund() {
        return minCustomRefund;
    }

    public void setMinCustomRefund(Float minCustomRefund) {
        this.minCustomRefund = minCustomRefund;
    }

    public OrderComaplaintZomatoRequest() {
    }

    public OrderComaplaintZomatoRequest(String externalOrderId, Integer orderId, String referenceId, List<String> imageUrlsList, String complaintMessage, String complaintReason, Date createdAt, Date expiredAt, List<OrderItemRequest> orderedItemsList, Integer customerComplaintsCount, Integer repeatCustomerCount, List<RefundOptionsRequest> refundOptionsList, Float minCustomRefund) {
        this.externalOrderId = externalOrderId;
        this.orderId = orderId;
        this.referenceId = referenceId;
        this.imageUrlsList = imageUrlsList;
        this.complaintMessage = complaintMessage;
        this.complaintReason = complaintReason;
        this.createdAt = createdAt;
        this.expiredAt = expiredAt;
        this.orderedItemsList = orderedItemsList;
        this.customerComplaintsCount = customerComplaintsCount;
        this.repeatCustomerCount = repeatCustomerCount;
        this.refundOptionsList = refundOptionsList;
        this.minCustomRefund = minCustomRefund;
    }

    @Override
    public String toString() {
        return "OrderComaplaintZomatoRequest{" +
                "imageUrlsList=" + imageUrlsList +
                '}';
    }
}
