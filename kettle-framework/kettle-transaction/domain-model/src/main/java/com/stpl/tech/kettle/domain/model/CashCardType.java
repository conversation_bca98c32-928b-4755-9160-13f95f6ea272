//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2017.10.21 at 06:01:16 PM IST
//


package com.stpl.tech.kettle.domain.model;

public enum CashCardType {

    PHYSICAL,
    GIFT,
    ECARD,
    AP01,
    GYFTR,
    MICRO;

    public String value() {
        return name();
    }

    public static CashCardType fromValue(String v) {
        return valueOf(v);
    }

}
