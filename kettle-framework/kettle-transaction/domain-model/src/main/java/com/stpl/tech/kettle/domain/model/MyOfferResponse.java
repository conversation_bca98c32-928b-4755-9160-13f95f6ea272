package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;
import java.util.Date;

public class MyOfferResponse implements Serializable {
    private static final long serialVersionUID = 2278827058948266998L;
    private String customerName;
    private String offerDesc;
    private String offerCouponCode;
    private String redirectionUrl;
    private Integer campaignId;
    private String contentUrl;
    private String validityFrom;
    private String validityTill;
    private Boolean isExistingOffer;
    private String offerStatus;
    private Date offerStartDate;
    private Date offerEndDate;
    private Integer channelPartnerId;

    public MyOfferResponse() {
    }

    public MyOfferResponse(String customerName, String offerDesc, String offerCouponCode, String redirectionUrl,
                           Integer campaignId, String contentUrl, String validityFrom, String validityTill,Integer channelPartnerId) {
        this.customerName = customerName;
        this.offerDesc = offerDesc;
        this.offerCouponCode = offerCouponCode;
        this.redirectionUrl = redirectionUrl;
        this.campaignId = campaignId;
        this.contentUrl = contentUrl;
        this.validityFrom = validityFrom;
        this.validityTill = validityTill;
        this.channelPartnerId = channelPartnerId;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getOfferDesc() {
        return offerDesc;
    }

    public void setOfferDesc(String offerDesc) {
        this.offerDesc = offerDesc;
    }

    public String getOfferCouponCode() {
        return offerCouponCode;
    }

    public void setOfferCouponCode(String offerCouponCode) {
        this.offerCouponCode = offerCouponCode;
    }

    public String getRedirectionUrl() {
        return redirectionUrl;
    }

    public void setRedirectionUrl(String redirectionUrl) {
        this.redirectionUrl = redirectionUrl;
    }

    public Integer getCampaignId() {
        return campaignId;
    }

    public void setCampaignId(Integer campaignId) {
        this.campaignId = campaignId;
    }

    public String getContentUrl() {
        return contentUrl;
    }

    public void setContentUrl(String contentUrl) {
        this.contentUrl = contentUrl;
    }

    public String getValidityFrom() {
        return validityFrom;
    }

    public void setValidityFrom(String validityFrom) {
        this.validityFrom = validityFrom;
    }

    public String getValidityTill() {
        return validityTill;
    }

    public void setValidityTill(String validityTill) {
        this.validityTill = validityTill;
    }

    public Boolean getExistingOffer() {
        return isExistingOffer;
    }

    public void setExistingOffer(Boolean existingOffer) {
        isExistingOffer = existingOffer;
    }

    public String getOfferStatus() {
        return offerStatus;
    }

    public void setOfferStatus(String offerStatus) {
        this.offerStatus = offerStatus;
    }

    public Date getOfferStartDate() {
        return offerStartDate;
    }

    public void setOfferStartDate(Date offerStartDate) {
        this.offerStartDate = offerStartDate;
    }

    public Date getOfferEndDate() {
        return offerEndDate;
    }

    public void setOfferEndDate(Date offerEndDate) {
        this.offerEndDate = offerEndDate;
    }

    public Integer getChannelPartnerId() {
        return channelPartnerId;
    }

    public void setChannelPartnerId(Integer channelPartnerId) {
        this.channelPartnerId = channelPartnerId;
    }
}

