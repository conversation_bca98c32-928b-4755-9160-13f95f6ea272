/**
 * 
 */
package com.stpl.tech.kettle.core.data.budget.vo;

/**
 * <AUTHOR>
 *
 */
public class BudgetDetail {

	private BudgetKey key;
	private RevenueData revenue;
	private SalesCommissionData commissions;
	private RentAggregate rentData;
	private WastageAggregate wastage;
	private InventoryAggregate inventory;
	private ConsumablesAggregate consumables;
	private ExpenseAggregate expenses;

	//private ElectricityAggregate electricity;
	private ServiceAggregate services;
	//private ElectricityAggregate electricity;
	private DirectCost uploadCost;
	
	private AdjustmentAggregate adjustment;

	public SalesCommissionData getCommissions() {
		return commissions;
	}

	public void setCommissions(SalesCommissionData commissions) {
		this.commissions = commissions;
	}

	public BudgetKey getKey() {
		return key;
	}

	public void setKey(BudgetKey key) {
		this.key = key;
	}

	public RevenueData getRevenue() {
		return revenue;
	}

	public void setRevenue(RevenueData revenue) {
		this.revenue = revenue;
	}

	public WastageAggregate getWastage() {
		return wastage;
	}

	public void setWastage(WastageAggregate wastage) {
		this.wastage = wastage;
	}

	public InventoryAggregate getInventory() {
		return inventory;
	}

	public void setInventory(InventoryAggregate inventory) {
		this.inventory = inventory;
	}

	public ConsumablesAggregate getConsumables() {
		return consumables;
	}

	public void setConsumables(ConsumablesAggregate consumables) {
		this.consumables = consumables;
	}

	public ExpenseAggregate getExpenses() {
		return expenses;
	}

	public void setExpenses(ExpenseAggregate expenses) {
		this.expenses = expenses;
	}
/*
	public ElectricityAggregate getElectricity() {
		return electricity;
	}

	public void setElectricity(ElectricityAggregate electricity) {
		this.electricity = electricity;
	}
*/
	public DirectCost getUploadCost() {
		return uploadCost;
	}

	public void setUploadCost(DirectCost uploadCost) {
		this.uploadCost = uploadCost;
	}

	public RentAggregate getRentData() {
		return rentData;
	}

	public void setRentData(RentAggregate rentData) {
		this.rentData = rentData;
	}



	public ServiceAggregate getServices() {
		return services;
	}

	public void setServices(ServiceAggregate services) {
		this.services = services;
	}

	public AdjustmentAggregate getAdjustment() {
		return adjustment;
	}

	public void setAdjustment(AdjustmentAggregate adjustment) {
		this.adjustment = adjustment;
	}

	
	
}
