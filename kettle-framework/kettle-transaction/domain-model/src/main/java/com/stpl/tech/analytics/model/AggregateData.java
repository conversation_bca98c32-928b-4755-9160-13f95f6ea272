//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.10 at 06:51:04 PM IST 
//

package com.stpl.tech.analytics.model;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

/**
 * <p>
 * Java class for AggregateData complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="AggregateData"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="netSale" type="{http://www.w3schools.com}DecimalData"/&gt;
 *         &lt;element name="deliveryNetSale" type="{http://www.w3schools.com}DecimalData"/&gt;
 *         &lt;element name="gmv" type="{http://www.w3schools.com}DecimalData"/&gt;
 *         &lt;element name="deliveryGmv" type="{http://www.w3schools.com}DecimalData"/&gt;
 *         &lt;element name="apc" type="{http://www.w3schools.com}DecimalData"/&gt;
 *         &lt;element name="foodCapture" type="{http://www.w3schools.com}DecimalData"/&gt;
 *         &lt;element name="customerCapture" type="{http://www.w3schools.com}DecimalData"/&gt;
 *         &lt;element name="deliveryApc" type="{http://www.w3schools.com}DecimalData"/&gt;
 *         &lt;element name="tickets" type="{http://www.w3schools.com}NumericData"/&gt;
 *         &lt;element name="deliveryTickets" type="{http://www.w3schools.com}NumericData"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "AggregateData", propOrder = { "netSale", "deliveryNetSale", "gmv", "deliveryGmv", "apc", "foodCapture",
		"customerCapture", "deliveryApc", "tickets", "deliveryTickets" })
public class AggregateData  implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -903949359391651699L;
	@XmlElement(required = true)
	protected DecimalData netSale;
	@XmlElement(required = true)
	protected DecimalData deliveryNetSale;
	@XmlElement(required = true)
	protected DecimalData gmv;
	@XmlElement(required = true)
	protected DecimalData deliveryGmv;
	@XmlElement(required = true)
	protected DecimalData apc;
	@XmlElement(required = true)
	protected DecimalData foodCapture;
	@XmlElement(required = true)
	protected DecimalData customerCapture;
	@XmlElement(required = true)
	protected DecimalData deliveryApc;
	@XmlElement(required = true)
	protected NumericData tickets;
	@XmlElement(required = true)
	protected NumericData deliveryTickets;

	/**
	 * Gets the value of the netSale property.
	 * 
	 * @return possible object is {@link DecimalData }
	 * 
	 */
	public DecimalData getNetSale() {
		return netSale;
	}

	/**
	 * Sets the value of the netSale property.
	 * 
	 * @param value
	 *            allowed object is {@link DecimalData }
	 * 
	 */
	public void setNetSale(DecimalData value) {
		this.netSale = value;
	}

	/**
	 * Gets the value of the deliveryNetSale property.
	 * 
	 * @return possible object is {@link DecimalData }
	 * 
	 */
	public DecimalData getDeliveryNetSale() {
		return deliveryNetSale;
	}

	/**
	 * Sets the value of the deliveryNetSale property.
	 * 
	 * @param value
	 *            allowed object is {@link DecimalData }
	 * 
	 */
	public void setDeliveryNetSale(DecimalData value) {
		this.deliveryNetSale = value;
	}

	/**
	 * Gets the value of the gmv property.
	 * 
	 * @return possible object is {@link DecimalData }
	 * 
	 */
	public DecimalData getGmv() {
		return gmv;
	}

	/**
	 * Sets the value of the gmv property.
	 * 
	 * @param value
	 *            allowed object is {@link DecimalData }
	 * 
	 */
	public void setGmv(DecimalData value) {
		this.gmv = value;
	}

	/**
	 * Gets the value of the deliveryGmv property.
	 * 
	 * @return possible object is {@link DecimalData }
	 * 
	 */
	public DecimalData getDeliveryGmv() {
		return deliveryGmv;
	}

	/**
	 * Sets the value of the deliveryGmv property.
	 * 
	 * @param value
	 *            allowed object is {@link DecimalData }
	 * 
	 */
	public void setDeliveryGmv(DecimalData value) {
		this.deliveryGmv = value;
	}

	/**
	 * Gets the value of the apc property.
	 * 
	 * @return possible object is {@link DecimalData }
	 * 
	 */
	public DecimalData getApc() {
		return apc;
	}

	/**
	 * Sets the value of the apc property.
	 * 
	 * @param value
	 *            allowed object is {@link DecimalData }
	 * 
	 */
	public void setApc(DecimalData value) {
		this.apc = value;
	}

	/**
	 * Gets the value of the foodCapture property.
	 * 
	 * @return possible object is {@link DecimalData }
	 * 
	 */
	public DecimalData getFoodCapture() {
		return foodCapture;
	}

	/**
	 * Sets the value of the foodCapture property.
	 * 
	 * @param value
	 *            allowed object is {@link DecimalData }
	 * 
	 */
	public void setFoodCapture(DecimalData value) {
		this.foodCapture = value;
	}

	/**
	 * Gets the value of the customerCapture property.
	 * 
	 * @return possible object is {@link DecimalData }
	 * 
	 */
	public DecimalData getCustomerCapture() {
		return customerCapture;
	}

	/**
	 * Sets the value of the customerCapture property.
	 * 
	 * @param value
	 *            allowed object is {@link DecimalData }
	 * 
	 */
	public void setCustomerCapture(DecimalData value) {
		this.customerCapture = value;
	}

	/**
	 * Gets the value of the deliveryApc property.
	 * 
	 * @return possible object is {@link DecimalData }
	 * 
	 */
	public DecimalData getDeliveryApc() {
		return deliveryApc;
	}

	/**
	 * Sets the value of the deliveryApc property.
	 * 
	 * @param value
	 *            allowed object is {@link DecimalData }
	 * 
	 */
	public void setDeliveryApc(DecimalData value) {
		this.deliveryApc = value;
	}

	/**
	 * Gets the value of the tickets property.
	 * 
	 * @return possible object is {@link NumericData }
	 * 
	 */
	public NumericData getTickets() {
		return tickets;
	}

	/**
	 * Sets the value of the tickets property.
	 * 
	 * @param value
	 *            allowed object is {@link NumericData }
	 * 
	 */
	public void setTickets(NumericData value) {
		this.tickets = value;
	}

	/**
	 * Gets the value of the deliveryTickets property.
	 * 
	 * @return possible object is {@link NumericData }
	 * 
	 */
	public NumericData getDeliveryTickets() {
		return deliveryTickets;
	}

	/**
	 * Sets the value of the deliveryTickets property.
	 * 
	 * @param value
	 *            allowed object is {@link NumericData }
	 * 
	 */
	public void setDeliveryTickets(NumericData value) {
		this.deliveryTickets = value;
	}

}
