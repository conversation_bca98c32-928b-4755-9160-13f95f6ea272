package com.stpl.tech.kettle.domain.model;

public class FavouriteChai {
    private Integer customerId;

    private String customerName;
    private String name; // tagType

    private CartOrderItem cartOrderItem;

    private FavChaiProductDetails productDetailsForFavChai;

    private String status; // ACTIVE , IN_ACTIVE

    private String isUpdated;

    private Integer unitId ;
    private Integer sourceId ;
    private String sourceName ;

    public FavouriteChai(Integer customerId, String name, CartOrderItem cartOrderItem, FavChaiProductDetails productDetailsForFavChai, String status, String isUpdated) {
        this.customerId = customerId;
        this.name = name;
        this.cartOrderItem = cartOrderItem;
        this.productDetailsForFavChai = productDetailsForFavChai;
        this.status = status;
        this.isUpdated = isUpdated;
    }
    public FavouriteChai(){

    }

    public Integer getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Integer customerId) {
        this.customerId = customerId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public CartOrderItem getCartOrderItem() {
        return cartOrderItem;
    }

    public void setCartOrderItem(CartOrderItem cartOrderItem) {
        this.cartOrderItem = cartOrderItem;
    }

    public FavChaiProductDetails getProductDetailsForFavChai() {
        return productDetailsForFavChai;
    }

    public void setProductDetailsForFavChai(FavChaiProductDetails productDetailsForFavChai) {
        this.productDetailsForFavChai = productDetailsForFavChai;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getIsUpdated() {
        return isUpdated;
    }

    public void setIsUpdated(String isUpdated) {
        this.isUpdated = isUpdated;
    }

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Integer getSourceId() {
        return sourceId;
    }

    public void setSourceId(Integer sourceId) {
        this.sourceId = sourceId;
    }

    public String getSourceName() {
        return sourceName;
    }

    public void setSourceName(String sourceName) {
        this.sourceName = sourceName;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }
}
