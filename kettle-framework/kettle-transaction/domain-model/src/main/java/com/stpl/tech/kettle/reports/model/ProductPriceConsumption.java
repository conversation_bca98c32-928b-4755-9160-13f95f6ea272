/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.model;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import com.stpl.tech.master.domain.model.Pair;

public class ProductPriceConsumption extends AbstractConsumption {

	public ProductPriceConsumption(String name, BigDecimal price) {
		super(name);
		this.price = price;
	}

	private int quantity;
	private int compositeQuantity;
	private int complimentaryQuantity;
	private BigDecimal price = new BigDecimal(0);
	private BigDecimal amount = new BigDecimal(0);
	private BigDecimal standardDiscount = new BigDecimal(0);
	private BigDecimal compositeDiscount = new BigDecimal(0);
	private BigDecimal complimentaryDiscount = new BigDecimal(0);
	private Map<String,BigDecimal> settlementsByMode = new HashMap<String, BigDecimal>();
	private Map<String,Pair<BigDecimal, BigDecimal>> taxes = new HashMap<>();

	/**
	 * @return the price
	 */
	public BigDecimal getPrice() {
		return price;
	}

	/**
	 * @param price
	 *            the price to set
	 */
	public void setPrice(BigDecimal price) {
		this.price = price;
	}

	/**
	 * @return the amount
	 */
	public BigDecimal getAmount() {
		return amount;
	}

	/**
	 * @param amount
	 *            the amount to set
	 */
	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}

	public int getQuantity() {
		return quantity;
	}

	public void setQuantity(int quantity) {
		this.quantity = quantity;
	}

	public int getComplimentaryQuantity() {
		return complimentaryQuantity;
	}

	public void setComplimentaryQuantity(int complimentaryQuantity) {
		this.complimentaryQuantity = complimentaryQuantity;
	}

	public int getCompositeQuantity() {
		return compositeQuantity;
	}

	public void setCompositeQuantity(int compositeQuantity) {
		this.compositeQuantity = compositeQuantity;
	}

	public BigDecimal getStandardDiscount() {
		return standardDiscount;
	}

	public void setStandardDiscount(BigDecimal standardDiscount) {
		this.standardDiscount = standardDiscount;
	}

	public BigDecimal getComplimentaryDiscount() {
		return complimentaryDiscount;
	}

	public void setComplimentaryDiscount(BigDecimal compDiscount) {
		complimentaryDiscount = compDiscount;
	}

	public BigDecimal getCompositeDiscount() {
		return compositeDiscount;
	}

	public void setCompositeDiscount(BigDecimal comboDiscount) {
		this.compositeDiscount = comboDiscount;
	}

	public Map<String, BigDecimal> getSettlementsByModes() {
		return settlementsByMode;
	}

	public Map<String, Pair<BigDecimal, BigDecimal>> getTaxes() {
		return taxes;
	}

}
