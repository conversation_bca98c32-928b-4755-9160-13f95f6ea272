/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2015.12.23 at 12:37:04 PM IST 
//

package com.stpl.tech.kettle.delivery.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <p>
 * Java class for DUNAddress complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="DUNAddress"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="apartment_address" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="street_address_1" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="street_address_2" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="landmark" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="city" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="state" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="pincode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="country" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "DUNAddress", 
	propOrder = 
			{ 
				"apartment_address",
				"street_address_1",
				"street_address_2",
				"landmark",
				"city",
				"state",
				"pincode",
				"country"
			}
)
public class DUNAddress {

	@XmlElement(name = "apartment_address")
	@JsonProperty("apartment_address")
	protected String apartment_address;
	
	@XmlElement(name = "street_address_1",required = true)
	@JsonProperty("street_address_1")
	protected String street_address_1;
	
	@XmlElement(name = "street_address_2")
	@JsonProperty("street_address_2")
	protected String street_address_2;
	
	@XmlElement(name = "landmark")
	@JsonProperty("landmark")
	protected String landmark;
	
	@XmlElement(name = "city")
	@JsonProperty("city")
	protected String city;
	
	@XmlElement(name = "state")
	@JsonProperty("state")
	protected String state;
	
	@XmlElement(name = "pincode")
	@JsonProperty("pincode")
	protected String pincode;
	
	@XmlElement(name = "country")
	@JsonProperty("country")
	protected String country;
	
	

	public DUNAddress() {
		super();
	}

	public DUNAddress(String apartment_address, String street_address_1, String street_address_2, String landmark,
			String city, String state, String pincode, String country) {
		super();
		this.apartment_address = apartment_address;
		this.street_address_1 = street_address_1;
		this.street_address_2 = street_address_2;
		this.landmark = landmark;
		this.city = city;
		this.state = state;
		this.pincode = pincode;
		this.country = country;
	}

	public String getApartment_address() {
		return apartment_address;
	}

	public void setApartment_address(String apartment_address) {
		this.apartment_address = apartment_address;
	}

	public String getStreet_address_1() {
		return street_address_1;
	}

	public void setStreet_address_1(String street_address_1) {
		this.street_address_1 = street_address_1;
	}

	public String getStreet_address_2() {
		return street_address_2;
	}

	public void setStreet_address_2(String street_address_2) {
		this.street_address_2 = street_address_2;
	}

	public String getLandmark() {
		return landmark;
	}

	public void setLandmark(String landmark) {
		this.landmark = landmark;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getState() {
		return state;
	}

	public void setState(String state) {
		this.state = state;
	}

	public String getPincode() {
		return pincode;
	}

	public void setPincode(String pincode) {
		this.pincode = pincode;
	}

	public String getCountry() {
		return country;
	}

	public void setCountry(String country) {
		this.country = country;
	}

	
}
