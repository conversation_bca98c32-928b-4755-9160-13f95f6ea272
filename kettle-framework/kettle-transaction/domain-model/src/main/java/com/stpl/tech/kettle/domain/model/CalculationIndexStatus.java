//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2018.01.25 at 01:07:15 PM IST 
//


package com.stpl.tech.kettle.domain.model;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for CalculationIndexStatus.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="CalculationIndexStatus"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="FIRST"/&gt;
 *     &lt;enumeration value="MID"/&gt;
 *     &lt;enumeration value="LAST"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 * 
 */
@XmlType(name = "CalculationIndexStatus")
@XmlEnum
public enum CalculationIndexStatus {

    FIRST,
    MID,
    LAST;

    public String value() {
        return name();
    }

    public static CalculationIndexStatus fromValue(String v) {
        return valueOf(v);
    }

}
