//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.10 at 07:11:28 PM IST 
//

package com.stpl.tech.analytics.model;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "CumulativeData", propOrder = { "mtd", "mtdTarget", "monthlyTarget", "monthlyProjected", "cafeCountMTD",
		"cafeCountProjected" })
public class CumulativeData implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = -2947571913854492970L;

	private SalesSplitData mtd;

	private SalesSplitData todayTarget;

	private SalesSplitData mtdTarget;

	private SalesSplitData monthlyTarget;

	private SalesSplitData monthlyProjected;

	private PercentageIntegerData cafeCountMTD;
	private PercentageIntegerData cafeCountProjected;

	public SalesSplitData getMtd() {
		return mtd;
	}

	public void setMtd(SalesSplitData mtd) {
		this.mtd = mtd;
	}

	public SalesSplitData getMtdTarget() {
		return mtdTarget;
	}

	public void setMtdTarget(SalesSplitData mtdTarget) {
		this.mtdTarget = mtdTarget;
	}

	public SalesSplitData getMonthlyTarget() {
		return monthlyTarget;
	}

	public void setMonthlyTarget(SalesSplitData monthlyTarget) {
		this.monthlyTarget = monthlyTarget;
	}

	public SalesSplitData getMonthlyProjected() {
		return monthlyProjected;
	}

	public void setMonthlyProjected(SalesSplitData monthlyProjected) {
		this.monthlyProjected = monthlyProjected;
	}

	public PercentageIntegerData getCafeCountMTD() {
		return cafeCountMTD;
	}

	public void setCafeCountMTD(PercentageIntegerData cafeCountMTD) {
		this.cafeCountMTD = cafeCountMTD;
	}

	public PercentageIntegerData getCafeCountProjected() {
		return cafeCountProjected;
	}

	public void setCafeCountProjected(PercentageIntegerData cafeCountProjected) {
		this.cafeCountProjected = cafeCountProjected;
	}

	public SalesSplitData getTodayTarget() {
		return todayTarget;
	}

	public void setTodayTarget(SalesSplitData todayTarget) {
		this.todayTarget = todayTarget;
	}

}
