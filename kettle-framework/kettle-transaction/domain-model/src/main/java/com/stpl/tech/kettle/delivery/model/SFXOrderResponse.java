/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2015.12.23 at 12:37:04 PM IST 
//

package com.stpl.tech.kettle.delivery.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <p>
 * Java class for SFXOrderResponse complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="SFXOrderResponse"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="sfx_order_id" type="{http://www.w3.org/2001/XMLSchema}long"/&gt;
 *         &lt;element name="store_code" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="pickup_contact_number" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="order_details" type="{http://www.w3schools.com}SFXOrder"/&gt;
 *         &lt;element name="status" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="customer_details" type="{http://www.w3schools.com}SFXCustomer"/&gt;
 *         &lt;element name="rider_details" type="{http://www.w3schools.com}SFXRider"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@JsonIgnoreProperties(ignoreUnknown=true)
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SFXOrderResponse", propOrder = { "sfx_Order_Id", "store_Code", "pickup_Contact_Number",
		"order_Details", "status", "customer_Details", "rider_Details" })
public class SFXOrderResponse {

	@XmlElement(name = "sfx_order_id")
	@JsonProperty("sfx_order_id")
	protected long sfx_Order_Id;
	@XmlElement(name = "store_code", required = true)
	@JsonProperty("store_code")
	protected String store_Code;
	@XmlElement(name = "pickup_contact_number", required = true)
	@JsonProperty("pickup_contact_number")
	protected String pickup_Contact_Number;
	@XmlElement(name = "order_details", required = true)
	@JsonProperty("order_details")
	protected SFXOrder order_Details;
	@XmlElement(required = true)
	@JsonProperty
	protected String status;
	@XmlElement(name = "customer_details", required = true)
	@JsonProperty("customer_details")
	protected SFXCustomer customer_Details;
	@XmlElement(name = "rider_details", required = true)
	@JsonProperty("rider_details")
	protected SFXRider rider_Details;

	/**
	 * Gets the value of the sfx_Order_Id property.
	 * 
	 */
	public long getSfx_Order_Id() {
		return sfx_Order_Id;
	}

	/**
	 * Sets the value of the sfx_Order_Id property.
	 * 
	 */
	public void setSfx_Order_Id(long value) {
		this.sfx_Order_Id = value;
	}

	/**
	 * Gets the value of the store_Code property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getStore_Code() {
		return store_Code;
	}

	/**
	 * Sets the value of the store_Code property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setStore_Code(String value) {
		this.store_Code = value;
	}

	/**
	 * Gets the value of the pickup_Contact_Number property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getPickup_Contact_Number() {
		return pickup_Contact_Number;
	}

	/**
	 * Sets the value of the pickup_Contact_Number property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setPickup_Contact_Number(String value) {
		this.pickup_Contact_Number = value;
	}

	/**
	 * Gets the value of the order_Details property.
	 * 
	 * @return possible object is {@link SFXOrder }
	 * 
	 */
	public SFXOrder getOrder_Details() {
		return order_Details;
	}

	/**
	 * Sets the value of the order_Details property.
	 * 
	 * @param value
	 *            allowed object is {@link SFXOrder }
	 * 
	 */
	public void setOrder_Details(SFXOrder value) {
		this.order_Details = value;
	}

	/**
	 * Gets the value of the status property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getStatus() {
		return status;
	}

	/**
	 * Sets the value of the status property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setStatus(String value) {
		this.status = value;
	}

	/**
	 * Gets the value of the customer_Details property.
	 * 
	 * @return possible object is {@link SFXCustomer }
	 * 
	 */
	public SFXCustomer getCustomer_Details() {
		return customer_Details;
	}

	/**
	 * Sets the value of the customer_Details property.
	 * 
	 * @param value
	 *            allowed object is {@link SFXCustomer }
	 * 
	 */
	public void setCustomer_Details(SFXCustomer value) {
		this.customer_Details = value;
	}

	/**
	 * Gets the value of the rider_Details property.
	 * 
	 * @return possible object is {@link SFXRider }
	 * 
	 */
	public SFXRider getRider_Details() {
		return rider_Details;
	}

	/**
	 * Sets the value of the rider_Details property.
	 * 
	 * @param value
	 *            allowed object is {@link SFXRider }
	 * 
	 */
	public void setRider_Details(SFXRider value) {
		this.rider_Details = value;
	}

}
