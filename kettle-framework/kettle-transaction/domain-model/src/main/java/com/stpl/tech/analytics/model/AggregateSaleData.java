//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.05.10 at 07:11:28 PM IST
//

package com.stpl.tech.analytics.model;

import com.stpl.tech.kettle.domain.model.Order;
import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.kettle.domain.model.Settlement;
import com.stpl.tech.master.domain.model.UnitCategory;
import com.stpl.tech.util.AppConstants;
import com.stpl.tech.util.AppUtils;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * Java class for AggregateSaleData complex type.
 *
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 *
 * <pre>
 * &lt;complexType name="AggregateSaleData"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="netSale" type="{http://www.w3schools.com}DecimalData"/&gt;
 *         &lt;element name="deliveryNetSale" type="{http://www.w3schools.com}DecimalData"/&gt;
 *         &lt;element name="gmv" type="{http://www.w3schools.com}DecimalData"/&gt;
 *         &lt;element name="deliveryGmv" type="{http://www.w3schools.com}DecimalData"/&gt;
 *         &lt;element name="apc" type="{http://www.w3schools.com}DecimalData"/&gt;
 *         &lt;element name="foodCapture" type="{http://www.w3schools.com}DecimalData"/&gt;
 *         &lt;element name="customerCapture" type="{http://www.w3schools.com}DecimalData"/&gt;
 *         &lt;element name="deliveryApc" type="{http://www.w3schools.com}DecimalData"/&gt;
 *         &lt;element name="tickets" type="{http://www.w3schools.com}NumericData"/&gt;
 *         &lt;element name="deliveryTickets" type="{http://www.w3schools.com}NumericData"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "AggregateSaleData", propOrder = { "netSale", "deliveryNetSale","deliveryNetSaleChaayos","deliveryNetSaleGnT", "gmv", "deliveryGmv", "apc",
	"deliveryApc","deliveryApcChaayos","deliveryApcGnT" ,"tickets", "deliveryTickets","deliveryTicketsChaayos","deliveryTicketsGnT"})
@Document
public class AggregateSaleData implements Serializable {

	/**
	 *
	 */
	private static final long serialVersionUID = 5433850792096079447L;
	@Id
	private String _id;

	/*
	 * @Version
	 *
	 * @JsonIgnore private Long version;
	 *
	 *//**
		 * Added to avoid a runtime error whereby the detachAll property is
		 * checked for existence but not actually used.
		 *//*
		 * private String detachAll;
		 */

	@XmlElement(required = true)
	@Field
	protected DecimalData netSale = new DecimalData();
	@XmlElement(required = true)
	@Field
	protected DecimalData deliveryNetSale = new DecimalData();
	@XmlElement(required = true)
	@Field
	protected DecimalData deliveryNetSaleChaayos = new DecimalData();
	@XmlElement(required = true)
	@Field
	protected DecimalData deliveryNetSaleGnT = new DecimalData();
	@XmlElement(required = true)
	@Field
	protected DecimalData dineInNetSale = new DecimalData();
	@XmlElement(required = true)
	@Field
	protected NumericData tickets = new NumericData();
	@XmlElement(required = true)
	@Field
	protected NumericData deliveryTickets = new NumericData();
	@XmlElement(required = true)
	@Field
	protected NumericData deliveryTicketsChaayos = new NumericData();
	@XmlElement(required = true)
	@Field
	protected NumericData deliveryTicketsGnT = new NumericData();
	@XmlElement(required = true)
	@Field
	protected NumericData dineInTickets = new NumericData();
	@XmlElement(required = true)
	@Field
	protected DecimalData gmv = new DecimalData();
	@XmlElement(required = true)
	@Field
	protected DecimalData deliveryGmv = new DecimalData();
	@XmlElement(required = true)
	@Field
	protected DecimalData dineInGmv = new DecimalData();
	@XmlElement(required = true)
	@Field
	protected DecimalData apc = new DecimalData();
	@XmlElement(required = true)
	@Field
	protected DecimalData deliveryApc = new DecimalData();
	@XmlElement(required = true)
	@Field
	protected DecimalData deliveryApcChaayos = new DecimalData();
	@Field
	protected DecimalData deliveryApcGnT = new DecimalData();
	@XmlElement(required = true)
	@Field
	protected DecimalData dineInApc = new DecimalData();

	/**
	 * @return the _id
	 */
	public String get_id() {
		return _id;
	}

	/**
	 * @param _id
	 *            the _id to set
	 */
	public void set_id(String _id) {
		this._id = _id;
	}

	/*
		*/
	/**
	 * @return the version
	 *//*
		 *
		 * public Long getVersion() { return version; }
		 *
		 */
	/**
	 * @param version
	 *            the version to set
	 *//*
		 *
		 * public void setVersion(Long version) { this.version = version; }
		 *
		 */
	/**
	 * @return the detachAll
	 *//*
		 *
		 * public String getDetachAll() { return detachAll; }
		 *
		 */
	/**
	 * @param detachAll
	 *            the detachAll to set
	 *//*
		 *
		 * public void setDetachAll(String detachAll) { this.detachAll =
		 * detachAll; }
		 */

	/**
	 * Gets the value of the netSale property.
	 *
	 * @return possible object is {@link DecimalData }
	 *
	 */
	public DecimalData getNetSale() {
		return netSale;
	}

	/**
	 * Sets the value of the netSale property.
	 *
	 * @param value
	 *            allowed object is {@link DecimalData }
	 *
	 */
	public void setNetSale(DecimalData value) {
		this.netSale = value;
	}

	/**
	 * Gets the value of the deliveryNetSale property.
	 *
	 * @return possible object is {@link DecimalData }
	 *
	 */
	public DecimalData getDeliveryNetSale() {
		return deliveryNetSale;
	}

	/**
	 * Sets the value of the deliveryNetSale property.
	 *
	 * @param value
	 *            allowed object is {@link DecimalData }
	 *
	 */
	public void setDeliveryNetSale(DecimalData value) {
		this.deliveryNetSale = value;
	}

	/**
	 * Gets the value of the gmv property.
	 *
	 * @return possible object is {@link DecimalData }
	 *
	 */
	public DecimalData getGmv() {
		return gmv;
	}

	/**
	 * Sets the value of the gmv property.
	 *
	 * @param value
	 *            allowed object is {@link DecimalData }
	 *
	 */
	public void setGmv(DecimalData value) {
		this.gmv = value;
	}

	/**
	 * Gets the value of the deliveryGmv property.
	 *
	 * @return possible object is {@link DecimalData }
	 *
	 */
	public DecimalData getDeliveryGmv() {
		return deliveryGmv;
	}

	/**
	 * Sets the value of the deliveryGmv property.
	 *
	 * @param value
	 *            allowed object is {@link DecimalData }
	 *
	 */
	public void setDeliveryGmv(DecimalData value) {
		this.deliveryGmv = value;
	}

	/**
	 * Gets the value of the apc property.
	 *
	 * @return possible object is {@link DecimalData }
	 *
	 */
	public DecimalData getApc() {
		return apc;
	}

	/**
	 * Sets the value of the apc property.
	 *
	 * @param value
	 *            allowed object is {@link DecimalData }
	 *
	 */
	public void setApc(DecimalData value) {
		this.apc = value;
	}

	/**
	 * Gets the value of the deliveryApc property.
	 *
	 * @return possible object is {@link DecimalData }
	 *
	 */
	public DecimalData getDeliveryApc() {
		return deliveryApc;
	}

	/**
	 * Sets the value of the deliveryApc property.
	 *
	 * @param value
	 *            allowed object is {@link DecimalData }
	 *
	 */
	public void setDeliveryApc(DecimalData value) {
		this.deliveryApc = value;
	}

	/**
	 * Gets the value of the tickets property.
	 *
	 * @return possible object is {@link NumericData }
	 *
	 */
	public NumericData getTickets() {
		return tickets;
	}

	/**
	 * Sets the value of the tickets property.
	 *
	 * @param value
	 *            allowed object is {@link NumericData }
	 *
	 */
	public void setTickets(NumericData value) {
		this.tickets = value;
	}

	/**
	 * Gets the value of the deliveryTickets property.
	 *
	 * @return possible object is {@link NumericData }
	 *
	 */
	public NumericData getDeliveryTickets() {
		return deliveryTickets;
	}

	/**
	 * Sets the value of the deliveryTickets property.
	 *
	 * @param value
	 *            allowed object is {@link NumericData }
	 *
	 */
	public void setDeliveryTickets(NumericData value) {
		this.deliveryTickets = value;
	}

	public void addOrder(Order order) {
		if (order.getTransactionDetail().getTotalAmount().compareTo(BigDecimal.ZERO) <= 0) {
			return;
		}
		BigDecimal multiplier = new BigDecimal(1.0d);
		int ticketMultipler = 1;
		int ticketCount = 1;
		BigDecimal giftCardAmount = BigDecimal.ZERO;
		if (OrderStatus.CANCELLED.equals(order.getStatus())
				|| OrderStatus.CANCELLED_REQUESTED.equals(order.getStatus())) {
			multiplier = new BigDecimal(-1.0d);
			ticketMultipler = -1;
		}
		BigDecimal giftCardRedemption = BigDecimal.ZERO;
		for (Settlement settlement : order.getSettlements()) {
			if (settlement.getMode() == 10) {
				giftCardRedemption = giftCardRedemption.add(settlement.getAmount());
			}
		}
		BigDecimal giftCardRedemptionSale = AppUtils.multiply(giftCardRedemption,
				order.getTransactionDetail().getTaxableAmount());
		giftCardRedemptionSale = AppUtils.divide(giftCardRedemptionSale, order.getTransactionDetail().getPaidAmount());
		BigDecimal orderValue = AppUtils.multiply(multiplier, order.getTransactionDetail().getTaxableAmount())
				.subtract(AppUtils.multiply(multiplier, giftCardRedemptionSale));
		boolean onlyGiftCard = true;
		boolean onlySubscriptionProduct = true;
		int subscriptionProductSubCategoryId = AppConstants.SUBSCRIPTION_PRODUCT_SUB_CATEGORY;
		if (order.getTransactionDetail().getTotalAmount().compareTo(BigDecimal.ZERO) != 0) {
			for (OrderItem item : order.getOrders()) {
				if (!AppUtils.isGiftCard(item.getCode())) {
					onlyGiftCard = false;
				} else {
					giftCardAmount = AppUtils.add(giftCardAmount, item.getAmount());
				}

				if (subscriptionProductSubCategoryId != item.getProductSubCategory().getId()) {
					onlySubscriptionProduct = false;
				}
			}
		}

		refreshAPC(order, order.getTransactionDetail().getTaxableAmount(), ticketCount, ticketMultipler,
				onlyGiftCard, giftCardAmount, multiplier, onlySubscriptionProduct);
		this.netSale.current = this.netSale.current.add(orderValue);
		this.gmv.current = this.gmv.current
				.add(AppUtils.multiply(multiplier, order.getTransactionDetail().getTotalAmount()));
		if (order.getTransactionDetail().getTotalAmount().compareTo(BigDecimal.ZERO) != 0 && !onlyGiftCard && !onlySubscriptionProduct) {
			this.tickets.current = this.tickets.current + ticketCount * ticketMultipler;
		}

		if (!UnitCategory.COD.name().equals(order.getSource())) {
			this.dineInNetSale.current = this.dineInNetSale.current.add(orderValue);
			this.dineInGmv.current = this.dineInGmv.current
					.add(AppUtils.multiply(multiplier, order.getTransactionDetail().getTotalAmount()));
			if (order.getTransactionDetail().getTotalAmount().compareTo(BigDecimal.ZERO) != 0 && !onlyGiftCard
					&& !AppConstants.ORDER_TYPE_PAID_EMPLOYEE_MEAL.equals(order.getOrderType())
					&& !onlySubscriptionProduct) {
				this.dineInTickets.current = this.dineInTickets.current + ticketCount * ticketMultipler;
			}
		}

		if (UnitCategory.COD.name().equals(order.getSource())) {
			this.deliveryNetSale.current = this.deliveryNetSale.current.add(orderValue);
			this.deliveryGmv.current = this.deliveryGmv.current
					.add(AppUtils.multiply(multiplier, order.getTransactionDetail().getTotalAmount()));
			if (order.getTransactionDetail().getTotalAmount().compareTo(BigDecimal.ZERO) != 0 && !onlyGiftCard
					&& !onlySubscriptionProduct) {
				this.deliveryTickets.current = this.deliveryTickets.current + ticketCount * ticketMultipler;
			}
			if(order.getBrandId()==AppConstants.CHAAYOS_BRAND_ID){
				this.deliveryNetSaleChaayos.current = this.deliveryNetSaleChaayos.current.add(orderValue);
				if (order.getTransactionDetail().getTotalAmount().compareTo(BigDecimal.ZERO) != 0 && !onlyGiftCard
						&& !onlySubscriptionProduct) {
					this.deliveryTicketsChaayos.current = this.deliveryTicketsChaayos.current + ticketCount * ticketMultipler;
				}
			}
			if(order.getBrandId()==AppConstants.GNT_BRAND_ID){
				this.deliveryNetSaleGnT.current = this.deliveryNetSaleGnT.current.add(orderValue);
				if (order.getTransactionDetail().getTotalAmount().compareTo(BigDecimal.ZERO) != 0 && !onlyGiftCard) {
					this.deliveryTicketsGnT.current = this.deliveryTicketsGnT.current + ticketCount * ticketMultipler;
				}
			}
		}
	}

	public void updateTargets(AggregateSaleData target) {

		this.netSale.target = target.getNetSale().getTarget();
		this.tickets.target = target.getTickets().getTarget();
		this.dineInNetSale.target = target.getDineInNetSale().getTarget();
		this.dineInTickets.target = target.getDineInTickets().getTarget();
		this.dineInApc.target = target.getDineInApc().getTarget();
		this.deliveryNetSale.target = target.getDeliveryNetSale().getTarget();
		this.deliveryTickets.target = target.getDeliveryTickets().getTarget();
		this.deliveryApc.target = target.getDeliveryApc().getTarget();
		this.deliveryNetSaleChaayos.target = target.getDeliveryNetSaleChaayos().getTarget();
		this.deliveryTicketsChaayos.target = target.getDeliveryTicketsChaayos().getTarget();
		this.deliveryApcChaayos.target = target.getDeliveryApcChaayos().getTarget();
		this.deliveryNetSaleGnT.target = target.getDeliveryNetSaleGnT().getTarget();
		this.deliveryTicketsGnT.target = target.getDeliveryTicketsGnT().getTarget();
		this.deliveryApcGnT.target = target.getDeliveryApcGnT().getTarget();
		this.apc.target = target.getApc().getTarget();
	}

	private void refreshAPC(Order order, BigDecimal taxableAmount, int ticketCount, int ticketMultipler,
			boolean onlyGiftCard, BigDecimal giftCardAmount, BigDecimal multiplier, boolean onlySubscriptionProduct) {
		// skip bulk order
		if (UnitCategory.COD.name().equals(order.getSource()) && taxableAmount.compareTo(new BigDecimal(3000d)) > 0) {
			return;
		}
		BigDecimal updatedTaxableAmount = AppUtils.multiply(multiplier, taxableAmount)
				.subtract(AppUtils.multiply(multiplier, giftCardAmount));
		calculateAPC(order.getSource(), updatedTaxableAmount, ticketCount, ticketMultipler, onlyGiftCard, onlySubscriptionProduct);
		if (UnitCategory.COD.name().equals(order.getSource())) {
			calculateDeliveryAPC(order, updatedTaxableAmount, ticketCount, ticketMultipler, onlyGiftCard, onlySubscriptionProduct);
		} else {
			calculateDineInAPC(order.getSource(), updatedTaxableAmount, ticketCount, ticketMultipler, onlyGiftCard, onlySubscriptionProduct);
		}
	}


	private void calculateAPC(String source, BigDecimal taxableAmount, int ticketCount, int ticketMultipler,
			boolean onlyGiftCard, boolean onlySubscriptionProduct) {
		BigDecimal currentSale = AppUtils.multiply(this.getApc().getCurrent(),
				new BigDecimal(this.getTickets().getCurrent()));
		currentSale = currentSale.add(taxableAmount);
		int tickets = !onlyGiftCard && !onlySubscriptionProduct ? this.getTickets().getCurrent() + (ticketCount * ticketMultipler)
				: this.getTickets().getCurrent();
		this.getApc().setCurrent(AppUtils.divide(currentSale, new BigDecimal(tickets)));
		this.getApc().setTrendUpward(this.getApc().getCurrent().compareTo(this.getApc().getTarget()) >= 0);
	}

	private void calculateDineInAPC(String source, BigDecimal taxableAmount, int ticketCount, int ticketMultipler,
			boolean onlyGiftCard, boolean onlySubscriptionProduct) {
		BigDecimal currentSale = AppUtils.multiply(this.getDineInApc().getCurrent(),
				new BigDecimal(this.getDineInTickets().getCurrent()));
		currentSale = currentSale.add(taxableAmount);
		int tickets = !onlyGiftCard && !onlySubscriptionProduct ? this.getDineInTickets().getCurrent() + (ticketCount * ticketMultipler)
				: this.getDineInTickets().getCurrent();
		this.getDineInApc().setCurrent(AppUtils.divide(currentSale, new BigDecimal(tickets)));
		this.getDineInApc()
				.setTrendUpward(this.getDineInApc().getCurrent().compareTo(this.getDineInApc().getTarget()) >= 0);
	}

	private void calculateDeliveryAPC(Order order, BigDecimal taxableAmount, int ticketCount, int ticketMultipler,
			boolean onlyGiftCard, boolean onlySubscriptionProduct) {
		BigDecimal currentSale = AppUtils.multiply(this.getDeliveryApc().getCurrent(),
				new BigDecimal(this.getDeliveryTickets().getCurrent()));
		currentSale = currentSale.add(taxableAmount);
		int tickets = !onlyGiftCard && !onlySubscriptionProduct ? this.getDeliveryTickets().getCurrent() + (ticketCount * ticketMultipler)
				: this.getDeliveryTickets().getCurrent();
		this.getDeliveryApc().setCurrent(AppUtils.divide(currentSale, new BigDecimal(tickets)));
		this.getDeliveryApc()
				.setTrendUpward(this.getDeliveryApc().getCurrent().compareTo(this.getDeliveryApc().getTarget()) >= 0);
		if(order.getBrandId()==AppConstants.CHAAYOS_BRAND_ID){
			BigDecimal currentSaleChaayos = AppUtils.multiply(this.getDeliveryApcChaayos().getCurrent(),
				new BigDecimal(this.getDeliveryTicketsChaayos().getCurrent()));
			currentSaleChaayos = currentSaleChaayos.add(taxableAmount);
			int ticketsChaayos = !onlyGiftCard && !onlySubscriptionProduct ? this.getDeliveryTicketsChaayos().getCurrent() + (ticketCount * ticketMultipler)
				: this.getDeliveryTicketsChaayos().getCurrent();
			this.getDeliveryApcChaayos().setCurrent(AppUtils.divide(currentSaleChaayos, new BigDecimal(ticketsChaayos)));
			this.getDeliveryApcChaayos()
				.setTrendUpward(this.getDeliveryApcChaayos().getCurrent().compareTo(this.getDeliveryApcChaayos().getTarget()) >= 0);
		}
		if (order.getBrandId()==AppConstants.GNT_BRAND_ID){
			BigDecimal currentSaleGnT = AppUtils.multiply(this.getDeliveryApcGnT().getCurrent(),
				new BigDecimal(this.getDeliveryTicketsGnT().getCurrent()));
			currentSaleGnT = currentSaleGnT.add(taxableAmount);
			int ticketsGnT = !onlyGiftCard && !onlySubscriptionProduct ? this.getDeliveryTicketsGnT().getCurrent() + (ticketCount * ticketMultipler)
				: this.getDeliveryTicketsGnT().getCurrent();
			this.getDeliveryApcGnT().setCurrent(AppUtils.divide(currentSaleGnT, new BigDecimal(ticketsGnT)));
			this.getDeliveryApcGnT()
				.setTrendUpward(this.getDeliveryApcGnT().getCurrent().compareTo(this.getDeliveryApcGnT().getTarget()) >= 0);
		}
	}

	public DecimalData getDineInNetSale() {
		return dineInNetSale;
	}

	public void setDineInNetSale(DecimalData dineInNetSale) {
		this.dineInNetSale = dineInNetSale;
	}

	public NumericData getDineInTickets() {
		return dineInTickets;
	}

	public void setDineInTickets(NumericData dineInTickets) {
		this.dineInTickets = dineInTickets;
	}

	public DecimalData getDineInGmv() {
		return dineInGmv;
	}

	public void setDineInGmv(DecimalData dineInGmv) {
		this.dineInGmv = dineInGmv;
	}

	public DecimalData getDineInApc() {
		return dineInApc;
	}

	public void setDineInApc(DecimalData dineInApc) {
		this.dineInApc = dineInApc;
	}

	public DecimalData getDeliveryNetSaleGnT() {
		return deliveryNetSaleGnT;
	}

	public void setDeliveryNetSaleGnT(DecimalData deliveryNetSaleGnT) {
		this.deliveryNetSaleGnT = deliveryNetSaleGnT;
	}

	public NumericData getDeliveryTicketsGnT() {
		return deliveryTicketsGnT;
	}

	public void setDeliveryTicketsGnT(NumericData deliveryTicketsGnT) {
		this.deliveryTicketsGnT = deliveryTicketsGnT;
	}

	public DecimalData getDeliveryApcGnT() {
		return deliveryApcGnT;
	}

	public void setDeliveryApcGnT(DecimalData deliveryApcGnT) {
		this.deliveryApcGnT = deliveryApcGnT;
	}

	public DecimalData getDeliveryNetSaleChaayos() {
		return deliveryNetSaleChaayos;
	}

	public void setDeliveryNetSaleChaayos(DecimalData deliveryNetSaleChaayos) {
		this.deliveryNetSaleChaayos = deliveryNetSaleChaayos;
	}

	public NumericData getDeliveryTicketsChaayos() {
		return deliveryTicketsChaayos;
	}

	public void setDeliveryTicketsChaayos(NumericData deliveryTicketsChaayos) {
		this.deliveryTicketsChaayos = deliveryTicketsChaayos;
	}

	public DecimalData getDeliveryApcChaayos() {
		return deliveryApcChaayos;
	}

	public void setDeliveryApcChaayos(DecimalData deliveryApcChaayos) {
		this.deliveryApcChaayos = deliveryApcChaayos;
	}
}
