/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.02.02 at 11:32:30 AM IST 
//


package com.stpl.tech.kettle.domain.model;

import java.util.Date;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.stpl.tech.master.domain.model.Adapter2;


/**
 * <p>Java class for SubscriptionEvent complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="SubscriptionEvent"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="subscriptionEventId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="subscriptionDetail" type="{http://www.w3schools.com}Order"/&gt;
 *         &lt;element name="orderDetail" type="{http://www.w3schools.com}Order"/&gt;
 *         &lt;element name="status" type="{http://www.w3schools.com}SubscriptionEventStatus"/&gt;
 *         &lt;element name="retryCount" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="eventId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="originalDeliveryTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="actualDeliveryTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="eventSource" type="{http://www.w3schools.com}SubscriptionEventSource"/&gt;
 *         &lt;element name="reason" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="addTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="regularTimeChanged" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="eventDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="eventTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="lastUpdateTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="timeUpdated" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="remark" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SubscriptionEvent", propOrder = {
    "subscriptionEventId",
    "subscriptionDetail",
    "orderDetail",
    "status",
    "retryCount",
    "eventId",
    "originalDeliveryTime",
    "actualDeliveryTime",
    "eventSource",
    "reason",
    "addTime",
    "regularTimeChanged",
    "eventDate",
    "eventTime",
    "lastUpdateTime",
    "timeUpdated",
    "remark"
})
public class SubscriptionEvent {

    protected int subscriptionEventId;
    @XmlElement(required = true)
    protected Order subscriptionDetail;
    @XmlElement(required = true)
    protected Order orderDetail;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected SubscriptionEventStatus status;
    protected int retryCount;
    protected int eventId;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter2 .class)
    @XmlSchemaType(name = "date")
    protected Date originalDeliveryTime;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter2 .class)
    @XmlSchemaType(name = "date")
    protected Date actualDeliveryTime;
    @XmlElement(required = true, defaultValue = "AUTOMATED")
    @XmlSchemaType(name = "string")
    protected SubscriptionEventSource eventSource;
    @XmlElement(required = true)
    protected String reason;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter2 .class)
    @XmlSchemaType(name = "date")
    protected Date addTime;
    protected boolean regularTimeChanged;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter2 .class)
    @XmlSchemaType(name = "date")
    protected Date eventDate;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter2 .class)
    @XmlSchemaType(name = "date")
    protected Date eventTime;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter2 .class)
    @XmlSchemaType(name = "date")
    protected Date lastUpdateTime;
    @XmlElement(defaultValue = "false")
    protected boolean timeUpdated;
    @XmlElement(required = true, nillable = true)
    protected String remark;

    /**
     * Gets the value of the subscriptionEventId property.
     * 
     */
    public int getSubscriptionEventId() {
        return subscriptionEventId;
    }

    /**
     * Sets the value of the subscriptionEventId property.
     * 
     */
    public void setSubscriptionEventId(int value) {
        this.subscriptionEventId = value;
    }

    /**
     * Gets the value of the subscriptionDetail property.
     * 
     * @return
     *     possible object is
     *     {@link Order }
     *     
     */
    public Order getSubscriptionDetail() {
        return subscriptionDetail;
    }

    /**
     * Sets the value of the subscriptionDetail property.
     * 
     * @param value
     *     allowed object is
     *     {@link Order }
     *     
     */
    public void setSubscriptionDetail(Order value) {
        this.subscriptionDetail = value;
    }

    /**
     * Gets the value of the orderDetail property.
     * 
     * @return
     *     possible object is
     *     {@link Order }
     *     
     */
    public Order getOrderDetail() {
        return orderDetail;
    }

    /**
     * Sets the value of the orderDetail property.
     * 
     * @param value
     *     allowed object is
     *     {@link Order }
     *     
     */
    public void setOrderDetail(Order value) {
        this.orderDetail = value;
    }

    /**
     * Gets the value of the status property.
     * 
     * @return
     *     possible object is
     *     {@link SubscriptionEventStatus }
     *     
     */
    public SubscriptionEventStatus getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     * 
     * @param value
     *     allowed object is
     *     {@link SubscriptionEventStatus }
     *     
     */
    public void setStatus(SubscriptionEventStatus value) {
        this.status = value;
    }

    /**
     * Gets the value of the retryCount property.
     * 
     */
    public int getRetryCount() {
        return retryCount;
    }

    /**
     * Sets the value of the retryCount property.
     * 
     */
    public void setRetryCount(int value) {
        this.retryCount = value;
    }

    /**
     * Gets the value of the eventId property.
     * 
     */
    public int getEventId() {
        return eventId;
    }

    /**
     * Sets the value of the eventId property.
     * 
     */
    public void setEventId(int value) {
        this.eventId = value;
    }

    /**
     * Gets the value of the originalDeliveryTime property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getOriginalDeliveryTime() {
        return originalDeliveryTime;
    }

    /**
     * Sets the value of the originalDeliveryTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOriginalDeliveryTime(Date value) {
        this.originalDeliveryTime = value;
    }

    /**
     * Gets the value of the actualDeliveryTime property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getActualDeliveryTime() {
        return actualDeliveryTime;
    }

    /**
     * Sets the value of the actualDeliveryTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setActualDeliveryTime(Date value) {
        this.actualDeliveryTime = value;
    }

    /**
     * Gets the value of the eventSource property.
     * 
     * @return
     *     possible object is
     *     {@link SubscriptionEventSource }
     *     
     */
    public SubscriptionEventSource getEventSource() {
        return eventSource;
    }

    /**
     * Sets the value of the eventSource property.
     * 
     * @param value
     *     allowed object is
     *     {@link SubscriptionEventSource }
     *     
     */
    public void setEventSource(SubscriptionEventSource value) {
        this.eventSource = value;
    }

    /**
     * Gets the value of the reason property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getReason() {
        return reason;
    }

    /**
     * Sets the value of the reason property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setReason(String value) {
        this.reason = value;
    }

    /**
     * Gets the value of the addTime property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getAddTime() {
        return addTime;
    }

    /**
     * Sets the value of the addTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAddTime(Date value) {
        this.addTime = value;
    }

    /**
     * Gets the value of the regularTimeChanged property.
     * 
     */
    public boolean isRegularTimeChanged() {
        return regularTimeChanged;
    }

    /**
     * Sets the value of the regularTimeChanged property.
     * 
     */
    public void setRegularTimeChanged(boolean value) {
        this.regularTimeChanged = value;
    }

    /**
     * Gets the value of the eventDate property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getEventDate() {
        return eventDate;
    }

    /**
     * Sets the value of the eventDate property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEventDate(Date value) {
        this.eventDate = value;
    }

    /**
     * Gets the value of the eventTime property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getEventTime() {
        return eventTime;
    }

    /**
     * Sets the value of the eventTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setEventTime(Date value) {
        this.eventTime = value;
    }

    /**
     * Gets the value of the lastUpdateTime property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getLastUpdateTime() {
        return lastUpdateTime;
    }

    /**
     * Sets the value of the lastUpdateTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setLastUpdateTime(Date value) {
        this.lastUpdateTime = value;
    }

    /**
     * Gets the value of the timeUpdated property.
     * 
     */
    public boolean isTimeUpdated() {
        return timeUpdated;
    }

    /**
     * Sets the value of the timeUpdated property.
     * 
     */
    public void setTimeUpdated(boolean value) {
        this.timeUpdated = value;
    }

    /**
     * Gets the value of the remark property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getRemark() {
        return remark;
    }

    /**
     * Sets the value of the remark property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setRemark(String value) {
        this.remark = value;
    }

}
