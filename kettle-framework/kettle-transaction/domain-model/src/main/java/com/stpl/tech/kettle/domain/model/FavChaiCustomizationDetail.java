package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;
import java.math.BigDecimal;

public class FavChaiCustomizationDetail implements Serializable {

    private static final long serialVersionUID = 615497187905463948L;

    private Integer favChaiCustomizationDetailId;
    private Integer customizationId;
    private int productId;
    private String dimension;
    private String name;
    private String type;
    private String defaultSetting;
    private BigDecimal quantity;
    private String source;
    private String shortCode;
    private String uom;

    public Integer getFavChaiCustomizationDetailId() {
        return favChaiCustomizationDetailId;
    }

    public void setFavChaiCustomizationDetailId(Integer favChaiCustomizationDetailId) {
        this.favChaiCustomizationDetailId = favChaiCustomizationDetailId;
    }

    public int getProductId() {
        return productId;
    }

    public void setProductId(int productId) {
        this.productId = productId;
    }

    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getDefaultSetting() {
        return defaultSetting;
    }

    public void setDefaultSetting(String defaultSetting) {
        this.defaultSetting = defaultSetting;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public Integer getCustomizationId() {
        return customizationId;
    }

    public void setCustomizationId(Integer customizationId) {
        this.customizationId = customizationId;
    }

    public String getShortCode() {return shortCode;}

    public void setShortCode(String shortCode) {this.shortCode = shortCode;}

    public String getUom() {
        return uom;
    }

    public void setUom(String uom) {
        this.uom = uom;
    }
}
