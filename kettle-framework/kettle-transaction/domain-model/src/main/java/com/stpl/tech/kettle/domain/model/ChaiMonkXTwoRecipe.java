/*
 * Created By Shanmukh
 */

package com.stpl.tech.kettle.domain.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonIgnoreProperties(ignoreUnknown=true)
public class ChaiMonkXTwoRecipe {

    @JsonProperty("CMD")
    private XTwoCommandType CMD;

    @JsonProperty("ATTR")
    private ChaiMonkXTwoAttribute ATTR;

    @JsonProperty("taskSentToMonkTime")
    private Date taskSentToMonkTime;

    @JsonProperty("taskAcceptedTime")
    private Date taskAcceptedTime;

    @JsonProperty("taskCompletedTime")
    private Date taskCompletedTime;

    @JsonProperty("taskStoppedTime")
    private Date taskStoppedTime;

    @JsonProperty("taskStopAcceptedTime")
    private Date taskStopAcceptedTime;

    @JsonProperty("taskStopRejectedTime")
    private Date taskStopRejectedTime;

    @JsonProperty("taskCookingCompletedTime")
    private Date taskCookingCompletedTime;

    @JsonProperty("ERROR")
    private String ERROR;
}
