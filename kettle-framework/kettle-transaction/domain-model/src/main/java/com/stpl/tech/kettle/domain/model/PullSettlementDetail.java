/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.02.21 at 02:28:22 PM IST 
//


package com.stpl.tech.kettle.domain.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import javax.xml.datatype.XMLGregorianCalendar;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.master.domain.model.Adapter1;
import com.stpl.tech.master.domain.model.PaymentMode;
import com.stpl.tech.master.domain.model.UnitBasicDetail;
import com.stpl.tech.util.domain.adapter.BigDecimalDeserializer;


/**
 * <p>Java class for PullSettlementDetail complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="PullSettlementDetail"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="settlementUnit" type="{http://www.w3schools.com}UnitBasicDetail"/&gt;
 *         &lt;element name="settlementTime" type="{http://www.w3.org/2001/XMLSchema}dateTime"/&gt;
 *         &lt;element name="settlementServiceProvider" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="settlementAmount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="settlementType" type="{http://www.w3schools.com}PaymentMode"/&gt;
 *         &lt;element name="settlementServiceProviderReceipt" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="unsettledAmount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="totalAmount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="closingAmount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="settlementStatus" type="{http://www.w3schools.com}SettlementStatus"/&gt;
 *         &lt;element name="settlementClosingReceipt" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="settlementReceiptPath" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="pullDetails" type="{http://www.w3schools.com}PullPacket" maxOccurs="unbounded"/&gt;
 *         &lt;element name="settlementDenominations" type="{http://www.w3schools.com}PullSettlementDenomination" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "PullSettlementDetail", propOrder = {
    "id",
    "settlementUnit",
    "settlementTime",
    "settlementServiceProvider",
    "settlementAmount",
    "originalAmount",
    "extraAmount",
    "settlementType",
    "settlementServiceProviderReceipt",
    "unsettledAmount",
    "totalAmount",
    "closingAmount",
    "settlementStatus",
    "settlementClosingReceipt",
    "settlementReceiptPath",
    "pullDetails",
    "settlementDenominations"
})
public class PullSettlementDetail {

    protected int id;
    @XmlElement(required = true)
    protected UnitBasicDetail settlementUnit;
    @XmlElement(required = true, nillable = true)
    @XmlSchemaType(name = "dateTime")
    protected XMLGregorianCalendar settlementTime;
    @XmlElement(required = true, nillable = true)
    protected String settlementServiceProvider;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal settlementAmount;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal originalAmount;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal extraAmount;
    @XmlElement(required = true)
    protected PaymentMode settlementType;
    @XmlElement(required = true, nillable = true)
    protected String settlementServiceProviderReceipt;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal unsettledAmount;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal totalAmount;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal closingAmount;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected SettlementStatus settlementStatus;
    @XmlElement(required = true, nillable = true)
    protected String settlementClosingReceipt;
    @XmlElement(required = true, nillable = true)
    protected String settlementReceiptPath;
    @XmlElement(required = true)
    protected PaymentMode paymentMode;
    @XmlElement(required = true)
    protected List<PullPacket> pullDetails;
    protected List<PullSettlementDenomination> settlementDenominations;
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "MM/dd/yyyy")
    protected Date settlementDate;
    protected String serialNumber;
    protected String ticketNumber;
    protected String slipNumber;

    /**
     * Gets the value of the id property.
     * 
     */
    public int getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     * 
     */
    public void setId(int value) {
        this.id = value;
    }

    /**
     * Gets the value of the settlementUnit property.
     * 
     * @return
     *     possible object is
     *     {@link UnitBasicDetail }
     *     
     */
    public UnitBasicDetail getSettlementUnit() {
        return settlementUnit;
    }

    /**
     * Sets the value of the settlementUnit property.
     * 
     * @param value
     *     allowed object is
     *     {@link UnitBasicDetail }
     *     
     */
    public void setSettlementUnit(UnitBasicDetail value) {
        this.settlementUnit = value;
    }

    /**
     * Gets the value of the settlementTime property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getSettlementTime() {
        return settlementTime;
    }

    /**
     * Sets the value of the settlementTime property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setSettlementTime(XMLGregorianCalendar value) {
        this.settlementTime = value;
    }

    /**
     * Gets the value of the settlementServiceProvider property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSettlementServiceProvider() {
        return settlementServiceProvider;
    }

    /**
     * Sets the value of the settlementServiceProvider property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSettlementServiceProvider(String value) {
        this.settlementServiceProvider = value;
    }

    /**
     * Gets the value of the settlementAmount property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getSettlementAmount() {
        return settlementAmount;
    }

    /**
     * Sets the value of the settlementAmount property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSettlementAmount(BigDecimal value) {
        this.settlementAmount = value;
    }

    
    public BigDecimal getOriginalAmount() {
		return originalAmount;
	}

	public void setOriginalAmount(BigDecimal originalAmount) {
		this.originalAmount = originalAmount;
	}

	public BigDecimal getExtraAmount() {
		return extraAmount;
	}

	public void setExtraAmount(BigDecimal extraAmount) {
		this.extraAmount = extraAmount;
	}

	/**
     * Gets the value of the settlementType property.
     * 
     * @return
     *     possible object is
     *     {@link PaymentMode }
     *     
     */
    public PaymentMode getSettlementType() {
        return settlementType;
    }

    /**
     * Sets the value of the settlementType property.
     * 
     * @param value
     *     allowed object is
     *     {@link PaymentMode }
     *     
     */
    public void setSettlementType(PaymentMode value) {
        this.settlementType = value;
    }

    /**
     * Gets the value of the settlementServiceProviderReceipt property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSettlementServiceProviderReceipt() {
        return settlementServiceProviderReceipt;
    }

    /**
     * Sets the value of the settlementServiceProviderReceipt property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSettlementServiceProviderReceipt(String value) {
        this.settlementServiceProviderReceipt = value;
    }

    /**
     * Gets the value of the unsettledAmount property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getUnsettledAmount() {
        return unsettledAmount;
    }

    /**
     * Sets the value of the unsettledAmount property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setUnsettledAmount(BigDecimal value) {
        this.unsettledAmount = value;
    }

    /**
     * Gets the value of the totalAmount property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    /**
     * Sets the value of the totalAmount property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setTotalAmount(BigDecimal value) {
        this.totalAmount = value;
    }

    /**
     * Gets the value of the closingAmount property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getClosingAmount() {
        return closingAmount;
    }

    /**
     * Sets the value of the closingAmount property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setClosingAmount(BigDecimal value) {
        this.closingAmount = value;
    }

    /**
     * Gets the value of the settlementStatus property.
     * 
     * @return
     *     possible object is
     *     {@link SettlementStatus }
     *     
     */
    public SettlementStatus getSettlementStatus() {
        return settlementStatus;
    }

    /**
     * Sets the value of the settlementStatus property.
     * 
     * @param value
     *     allowed object is
     *     {@link SettlementStatus }
     *     
     */
    public void setSettlementStatus(SettlementStatus value) {
        this.settlementStatus = value;
    }

    /**
     * Gets the value of the settlementClosingReceipt property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSettlementClosingReceipt() {
        return settlementClosingReceipt;
    }

    /**
     * Sets the value of the settlementClosingReceipt property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSettlementClosingReceipt(String value) {
        this.settlementClosingReceipt = value;
    }

    /**
     * Gets the value of the settlementReceiptPath property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getSettlementReceiptPath() {
        return settlementReceiptPath;
    }

    /**
     * Sets the value of the settlementReceiptPath property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setSettlementReceiptPath(String value) {
        this.settlementReceiptPath = value;
    }

    /**
     * Gets the value of the pullDetails property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the pullDetails property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getPullDetails().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link PullPacket }
     * 
     * 
     */
    public List<PullPacket> getPullDetails() {
        if (pullDetails == null) {
            pullDetails = new ArrayList<PullPacket>();
        }
        return this.pullDetails;
    }

    /**
     * Gets the value of the settlementDenominations property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the settlementDenominations property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getSettlementDenominations().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link PullSettlementDenomination }
     * 
     * 
     */
    public List<PullSettlementDenomination> getSettlementDenominations() {
        if (settlementDenominations == null) {
            settlementDenominations = new ArrayList<PullSettlementDenomination>();
        }
        return this.settlementDenominations;
    }

	public PaymentMode getPaymentMode() {
		return paymentMode;
	}

	public void setPaymentMode(PaymentMode paymentMode) {
		this.paymentMode = paymentMode;
	}

    public Date getSettlementDate() {
        return settlementDate;
    }

    public void setSettlementDate(Date settlementDate) {
        this.settlementDate = settlementDate;
    }

    public String getSerialNumber() {
        return serialNumber;
    }

    public void setSerialNumber(String serialNumber) {
        this.serialNumber = serialNumber;
    }

    public String getTicketNumber() {
        return ticketNumber;
    }

    public void setTicketNumber(String ticketNumber) {
        this.ticketNumber = ticketNumber;
    }

    public String getSlipNumber() {
        return slipNumber;
    }

    public void setSlipNumber(String slipNumber) {
        this.slipNumber = slipNumber;
    }
}
