package com.stpl.tech.kettle.referral.model;

import java.util.Date;

public class ReferentInfo {

    private String name;

    private String status;

    private String contact;

    private Date creationTime;

    private Date lastOrderTime;

    private Boolean firstOrderPlaced;

    private Integer defaultBonus;

    private Integer acquiredPoints;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public Date getCreationTime() {
        return creationTime;
    }

    public void setCreationTime(Date creationTime) {
        this.creationTime = creationTime;
    }

    public Date getLastOrderTime() {
        return lastOrderTime;
    }

    public void setLastOrderTime(Date lastOrderTime) {
        this.lastOrderTime = lastOrderTime;
    }

    public Boolean getFirstOrderPlaced() {
        return firstOrderPlaced;
    }

    public void setFirstOrderPlaced(Boolean firstOrderPlaced) {
        this.firstOrderPlaced = firstOrderPlaced;
    }

    public Integer getDefaultBonus() {
        return defaultBonus;
    }

    public void setDefaultBonus(Integer defaultBonus) {
        this.defaultBonus = defaultBonus;
    }

    public Integer getAcquiredPoints() {
        return acquiredPoints;
    }

    public void setAcquiredPoints(Integer acquiredPoints) {
        this.acquiredPoints = acquiredPoints;
    }
}
