/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.model;

import java.math.BigDecimal;
import java.util.Collection;
import java.util.Map;
import java.util.TreeMap;

public class ProductDimensionConsumption<T> extends AbstractConsumption {

	private String dimension;
	private int category;
	private int compositeQuantity;
	private int complimentaryQuantity;
	private Map<BigDecimal, T> items = new TreeMap<>();

	/**
	 * @param name
	 * @param dimension
	 * @param price
	 */
	public ProductDimensionConsumption(String name, String dimension) {
		super(name);
		this.dimension = dimension;
	}

	/**
	 * @return the dimension
	 */
	public String getDimension() {
		return dimension;
	}

	/**
	 * @param dimension
	 *            the dimension to set
	 */
	public void setDimension(String dimension) {
		this.dimension = dimension;
	}

	public int getCategory() {
		return category;
	}

	public void setCategory(int category) {
		this.category = category;
	}

	public Map<BigDecimal, T> getItems() {
		return items;
	}

	public void setItems(Map<BigDecimal, T> items) {
		this.items = items;
	}
	
	/**
	 * @return the items
	 */
	public Collection<T> getAllItems() {
		return items.values();
	}

	public int getComplimentaryQuantity() {
		return complimentaryQuantity;
	}

	public void setComplimentaryQuantity(int complimentaryQuantity) {
		this.complimentaryQuantity = complimentaryQuantity;
	}

	public int getCompositeQuantity() {
		return compositeQuantity;
	}

	public void setCompositeQuantity(int compositeQuantity) {
		this.compositeQuantity = compositeQuantity;
	}

}
