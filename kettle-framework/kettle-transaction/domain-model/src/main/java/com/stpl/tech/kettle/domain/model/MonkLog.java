package com.stpl.tech.kettle.domain.model;

import java.util.Date;
import java.util.Map;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 11-09-2017.
 */
public class MonkLog {

    protected int orderId;
    protected int taskId;
    protected String monkName;
    protected Date taskCreationTime;
    protected boolean isReassigned;
    protected Map<Integer,Long> eventMap;

    public int getTaskId() {
        return taskId;
    }

    public void setTaskId(int taskId) {
        this.taskId = taskId;
    }

    public String getMonkName() {
        return monkName;
    }

    public void setMonkName(String monkName) {
        this.monkName = monkName;
    }

    public Date getTaskCreationTime() {
        return taskCreationTime;
    }

    public void setTaskCreationTime(Date taskCreationTime) {
        this.taskCreationTime = taskCreationTime;
    }

    public Map<Integer, Long> getEventMap() {
        return eventMap;
    }

    public void setEventMap(Map<Integer, Long> eventMap) {
        this.eventMap = eventMap;
    }

    public int getOrderId() {

        return orderId;
    }

    public void setOrderId(int orderId) {
        this.orderId = orderId;
    }

    public boolean isReassigned() {
        return isReassigned;
    }

    public void setReassigned(boolean reassigned) {
        isReassigned = reassigned;
    }
}
