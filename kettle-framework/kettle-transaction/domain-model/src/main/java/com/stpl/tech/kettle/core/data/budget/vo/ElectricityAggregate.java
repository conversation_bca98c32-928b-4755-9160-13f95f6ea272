/**
 * 
 */
package com.stpl.tech.kettle.core.data.budget.vo;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 *
 */
public class ElectricityAggregate {

	private BigDecimal energyElectricity = new BigDecimal(0d);
	private BigDecimal energyDG = new BigDecimal(0d);;

	public BigDecimal getEnergyElectricity() {
		return energyElectricity;
	}

	public void setEnergyElectricity(BigDecimal energyElectricity) {
		this.energyElectricity = energyElectricity;
	}

	public BigDecimal getEnergyDG() {
		return energyDG;
	}

	public void setEnergyDG(BigDecimal energyDG) {
		this.energyDG = energyDG;
	}

}
