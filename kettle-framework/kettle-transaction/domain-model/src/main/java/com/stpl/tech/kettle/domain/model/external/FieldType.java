//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.08.22 at 05:50:13 PM IST 
//


package com.stpl.tech.kettle.domain.model.external;

import javax.xml.bind.annotation.XmlEnum;
import javax.xml.bind.annotation.XmlEnumValue;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for FieldType.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * <p>
 * <pre>
 * &lt;simpleType name="FieldType"&gt;
 *   &lt;restriction base="{http://www.w3.org/2001/XMLSchema}string"&gt;
 *     &lt;enumeration value="short_text"/&gt;
 *     &lt;enumeration value="multiple_choice"/&gt;
 *     &lt;enumeration value="rating"/&gt;
 *     &lt;enumeration value="long_text"/&gt;
 *     &lt;enumeration value="picture_choice"/&gt;
 *   &lt;/restriction&gt;
 * &lt;/simpleType&gt;
 * </pre>
 * 
 */
@XmlType(name = "FieldType")
@XmlEnum
public enum FieldType {
	

    @XmlEnumValue("date")
    date("date"),
    @XmlEnumValue("dropdown")
    dropdown("dropdown"),
    @XmlEnumValue("email")
    email("email"),
    @XmlEnumValue("file_upload")
    file_upload("file_upload"),
    @XmlEnumValue("goup")
    group("group"),
    @XmlEnumValue("legal")
    legal("legal"),
    @XmlEnumValue("long_text")
    long_text("long_text"),
    @XmlEnumValue("multiple_choice")
    multiple_choice("multiple_choice"),
    @XmlEnumValue("number")
    number("number"),
    @XmlEnumValue("opinion_scale")
    opinion_scale("opinion_scale"),
    @XmlEnumValue("payment")
    payment("payment"),
    @XmlEnumValue("picture_choice")
    picture_choice("picture_choice"),
    @XmlEnumValue("rating")
    rating("rating"),
    @XmlEnumValue("short_text")
    short_text("short_text"),
    @XmlEnumValue("statement")
    statement("statement"),
    @XmlEnumValue("list")
    list("list"),
    @XmlEnumValue("website")
    website("website"),
    @XmlEnumValue("phone_number")
    phone_number("phone_number"),
    @XmlEnumValue("textarea")
    textarea("textarea"),
    @XmlEnumValue("yes-no")
    yes_no("yes-no");
    private final String value;
    FieldType(String v) {
        value = v;
    }

    public String value() {
        return value;
    }

    public static FieldType fromValue(String v) {
        for (FieldType c: FieldType.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }

}
