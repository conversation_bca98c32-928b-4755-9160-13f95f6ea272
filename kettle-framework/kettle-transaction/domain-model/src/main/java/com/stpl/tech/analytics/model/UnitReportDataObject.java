//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.05.10 at 07:11:28 PM IST
//

package com.stpl.tech.analytics.model;

import com.stpl.tech.master.domain.model.Adapter2;
import com.stpl.tech.util.AppUtils;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;
import java.util.Date;

/**
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "UnitReportDataObject", propOrder = { "instance" })
@Document(collection = "UnitReportDataObject")
public class UnitReportDataObject implements Serializable {

	/**
	 *
	 */
	private static final long serialVersionUID = 7711079636428999286L;

	/**
	 *
	 */
	@Id
	private String _id;

	@Field
	private int id;


	@Field
	protected UnitReportData instance;

	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter2.class)
	@XmlSchemaType(name = "date")
	@Field
	protected Date time;

	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter2.class)
	@XmlSchemaType(name = "date")
	@Field
	protected Date date;

	public UnitReportDataObject() {

	}

	public UnitReportDataObject(UnitReportData instance) {
		super();
		this.instance = instance;
		this.time = AppUtils.getCurrentTimestamp();
		this.date = AppUtils.getCurrentBusinessDate();
	}

	public String get_id() {
		return _id;
	}

	public void set_id(String _id) {
		this._id = _id;
	}


	/**
	 * @return the id
	 */
	public int getId() {
		return id;
	}

	/**
	 * @param id the id to set
	 */
	public void setId(int id) {
		this.id = id;
	}

	/**
	 * @return the time
	 */
	public Date getTime() {
		return time;
	}

	/**
	 * @param time the time to set
	 */
	public void setTime(Date time) {
		this.time = time;
	}

	/**
	 * Gets the value of the businessDate property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public Date getDate() {
		return date;
	}

	/**
	 * Sets the value of the businessDate property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setDate(Date value) {
		this.date = value;
	}

	/**
	 * @return the instance
	 */
	public UnitReportData getInstance() {
		return instance;
	}

	/**
	 * @param instance
	 *            the instance to set
	 */
	public void setInstance(UnitReportData instance) {
		this.instance = instance;
	}

}
