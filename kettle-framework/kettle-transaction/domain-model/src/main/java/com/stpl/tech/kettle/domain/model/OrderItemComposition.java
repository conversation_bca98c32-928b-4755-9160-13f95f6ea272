package com.stpl.tech.kettle.domain.model;

import com.stpl.tech.master.recipe.model.IngredientProductDetail;
import com.stpl.tech.master.recipe.model.IngredientVariantDetail;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "OrderItemComposition", propOrder = { "variants", "products", "addons", "menuProducts" })
@Document
public class OrderItemComposition implements Serializable {

	/**
	 *
	 */
	private static final long serialVersionUID = -5083045930894371724L;
	@Id
	private String _id;

	/*@Version
	@JsonIgnore
	private Long version;

	*//**
	 *
	 * Added to avoid a runtime error whereby the detachAll property is checked
	 * for existence but not actually used.
	 *//*
	private String detachAll;*/

	@XmlElement(nillable = true)
	@Field
	protected List<IngredientVariantDetail> variants;
	@XmlElement(nillable = true)
	@Field
	protected List<IngredientProductDetail> products;
	@XmlElement(nillable = true)
	@Field
	protected List<IngredientProductDetail> addons;
	@XmlElement(nillable = true)
	@Field
	protected List<OrderItem> menuProducts;

	@XmlElement(nillable = true)
	@Field
	protected List<String> options;

	@XmlElement(nillable = true)
	@Field
	protected List<IngredientProductDetail> others;

	/*public String get_id() {
		return _id;
	}

	public void set_id(String _id) {
		this._id = _id;
	}

	public Long getVersion() {
		return version;
	}

	public void setVersion(Long version) {
		this.version = version;
	}

	public String getDetachAll() {
		return detachAll;
	}

	public void setDetachAll(String detachAll) {
		this.detachAll = detachAll;
	}*/

	public List<IngredientVariantDetail> getVariants() {
		if (variants == null) {
			variants = new ArrayList<>();
		}
		return variants;
	}

	public void setVariants(List<IngredientVariantDetail> variants) {
		this.variants = variants;
	}

	public List<IngredientProductDetail> getProducts() {
		if (products == null) {
			products = new ArrayList<>();
		}
		return products;
	}

	public void setProducts(List<IngredientProductDetail> products) {
		this.products = products;
	}

	public List<IngredientProductDetail> getAddons() {
		if (addons == null) {
			addons = new ArrayList<>();
		}
		return addons;
	}

	public void setAddons(List<IngredientProductDetail> addons) {
		this.addons = addons;
	}

	public List<OrderItem> getMenuProducts() {
		if (menuProducts == null) {
			menuProducts = new ArrayList<>();
		}
		return menuProducts;
	}

	public void setMenuProducts(List<OrderItem> menuProducts) {
		this.menuProducts = menuProducts;
	}

	public List<String> getOptions() {
		if (options == null) {
			options = new ArrayList<>();
		}
		return options;
	}

	public void setOptions(List<String> options) {
		this.options = options;
	}

	public boolean hasDefaultVariant() {
		for (IngredientVariantDetail variant : getVariants()) {
			if (!variant.isDefaultSetting()) {
				return true;
			}
		}
		return false;
	}

	public List<IngredientProductDetail> getOthers() {
		if(others == null){
			others= new ArrayList<>();
		}
		return others;
	}

	public void setOthers(List<IngredientProductDetail> others) {
		this.others = others;
	}
}
