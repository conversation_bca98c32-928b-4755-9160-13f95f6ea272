/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2017.05.20 at 01:27:06 PM IST 
//


package com.stpl.tech.kettle.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for WorkstationLog complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="WorkstationLog"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="workstationLogId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="orderId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="itemId" type="{http://www.w3.org/2001/XMLSchema}long"/&gt;
 *         &lt;element name="billCreationTime" type="{http://www.w3.org/2001/XMLSchema}long"/&gt;
 *         &lt;element name="unitId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="type" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="employeeId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="productId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="itemQuantity" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="dimension" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="orderSource" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="timeToAcknowledge" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="timeToStart" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="timeToProcess" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="timeToCancel" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="timeToProcessByMachine" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="cooktopStation" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="stationTasksForOrder" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="dispatched" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="cancelled" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="monkName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "WorkstationLog", propOrder = {
    "workstationLogId",
    "orderId",
    "itemId",
    "billCreationTime",
    "unitId",
    "type",
    "employeeId",
    "productId",
    "itemQuantity",
    "dimension",
    "orderSource",
    "timeToAcknowledge",
    "timeToStart",
    "timeToProcess",
    "timeToCancel",
    "timeToProcessByMachine",
    "cooktopStation",
    "stationTasksForOrder",
    "dispatched",
    "cancelled",
    "monkName"
})
public class WorkstationLog {

    protected int workstationLogId;
    protected int orderId;
    protected long itemId;
    protected long billCreationTime;
    protected int unitId;
    @XmlElement(required = true)
    protected String type;
    protected int employeeId;
    protected int productId;
    protected int itemQuantity;
    @XmlElement(required = true)
    protected String dimension;
    @XmlElement(required = true)
    protected String orderSource;
    protected int timeToAcknowledge;
    protected int timeToStart;
    protected int timeToProcess;
    protected int timeToCancel;
    protected int timeToProcessByMachine;
    protected int cooktopStation;
    protected int stationTasksForOrder;
    protected boolean dispatched;
    protected boolean cancelled;
    @XmlElement(required = true)
    protected String monkName;
    protected MonkLog monkLog;

    public MonkLog getMonkLog() {
        return monkLog;
    }

    public void setMonkLog(MonkLog monkLog) {
        this.monkLog = monkLog;
    }

    /**
     * Gets the value of the workstationLogId property.
     * 
     */
    public int getWorkstationLogId() {
        return workstationLogId;
    }

    /**
     * Sets the value of the workstationLogId property.
     * 
     */
    public void setWorkstationLogId(int value) {
        this.workstationLogId = value;
    }

    /**
     * Gets the value of the orderId property.
     * 
     */
    public int getOrderId() {
        return orderId;
    }

    /**
     * Sets the value of the orderId property.
     * 
     */
    public void setOrderId(int value) {
        this.orderId = value;
    }

    /**
     * Gets the value of the itemId property.
     * 
     */
    public long getItemId() {
        return itemId;
    }

    /**
     * Sets the value of the itemId property.
     * 
     */
    public void setItemId(long value) {
        this.itemId = value;
    }

    /**
     * Gets the value of the billCreationTime property.
     * 
     */
    public long getBillCreationTime() {
        return billCreationTime;
    }

    /**
     * Sets the value of the billCreationTime property.
     * 
     */
    public void setBillCreationTime(long value) {
        this.billCreationTime = value;
    }

    /**
     * Gets the value of the unitId property.
     * 
     */
    public int getUnitId() {
        return unitId;
    }

    /**
     * Sets the value of the unitId property.
     * 
     */
    public void setUnitId(int value) {
        this.unitId = value;
    }

    /**
     * Gets the value of the type property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getType() {
        return type;
    }

    /**
     * Sets the value of the type property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setType(String value) {
        this.type = value;
    }

    /**
     * Gets the value of the employeeId property.
     * 
     */
    public int getEmployeeId() {
        return employeeId;
    }

    /**
     * Sets the value of the employeeId property.
     * 
     */
    public void setEmployeeId(int value) {
        this.employeeId = value;
    }

    /**
     * Gets the value of the productId property.
     * 
     */
    public int getProductId() {
        return productId;
    }

    /**
     * Sets the value of the productId property.
     * 
     */
    public void setProductId(int value) {
        this.productId = value;
    }

    /**
     * Gets the value of the itemQuantity property.
     * 
     */
    public int getItemQuantity() {
        return itemQuantity;
    }

    /**
     * Sets the value of the itemQuantity property.
     * 
     */
    public void setItemQuantity(int value) {
        this.itemQuantity = value;
    }

    /**
     * Gets the value of the dimension property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDimension() {
        return dimension;
    }

    /**
     * Sets the value of the dimension property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDimension(String value) {
        this.dimension = value;
    }

    /**
     * Gets the value of the orderSource property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOrderSource() {
        return orderSource;
    }

    /**
     * Sets the value of the orderSource property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOrderSource(String value) {
        this.orderSource = value;
    }

    /**
     * Gets the value of the timeToAcknowledge property.
     * 
     */
    public int getTimeToAcknowledge() {
        return timeToAcknowledge;
    }

    /**
     * Sets the value of the timeToAcknowledge property.
     * 
     */
    public void setTimeToAcknowledge(int value) {
        this.timeToAcknowledge = value;
    }

    /**
     * Gets the value of the timeToStart property.
     * 
     */
    public int getTimeToStart() {
        return timeToStart;
    }

    /**
     * Sets the value of the timeToStart property.
     * 
     */
    public void setTimeToStart(int value) {
        this.timeToStart = value;
    }

    /**
     * Gets the value of the timeToProcess property.
     * 
     */
    public int getTimeToProcess() {
        return timeToProcess;
    }

    /**
     * Sets the value of the timeToProcess property.
     * 
     */
    public void setTimeToProcess(int value) {
        this.timeToProcess = value;
    }

    /**
     * Gets the value of the timeToCancel property.
     * 
     */
    public int getTimeToCancel() {
        return timeToCancel;
    }

    /**
     * Sets the value of the timeToCancel property.
     * 
     */
    public void setTimeToCancel(int value) {
        this.timeToCancel = value;
    }

    /**
     * Gets the value of the timeToProcessByMachine property.
     * 
     */
    public int getTimeToProcessByMachine() {
        return timeToProcessByMachine;
    }

    /**
     * Sets the value of the timeToProcessByMachine property.
     * 
     */
    public void setTimeToProcessByMachine(int value) {
        this.timeToProcessByMachine = value;
    }

    /**
     * Gets the value of the cooktopStation property.
     * 
     */
    public int getCooktopStation() {
        return cooktopStation;
    }

    /**
     * Sets the value of the cooktopStation property.
     * 
     */
    public void setCooktopStation(int value) {
        this.cooktopStation = value;
    }

    /**
     * Gets the value of the stationTasksForOrder property.
     * 
     */
    public int getStationTasksForOrder() {
        return stationTasksForOrder;
    }

    /**
     * Sets the value of the stationTasksForOrder property.
     * 
     */
    public void setStationTasksForOrder(int value) {
        this.stationTasksForOrder = value;
    }

    /**
     * Gets the value of the dispatched property.
     * 
     */
    public boolean isDispatched() {
        return dispatched;
    }

    /**
     * Sets the value of the dispatched property.
     * 
     */
    public void setDispatched(boolean value) {
        this.dispatched = value;
    }

    /**
     * Gets the value of the cancelled property.
     * 
     */
    public boolean isCancelled() {
        return cancelled;
    }

    /**
     * Sets the value of the cancelled property.
     * 
     */
    public void setCancelled(boolean value) {
        this.cancelled = value;
    }

    /**
     * Gets the value of the monkName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getMonkName() {
        return monkName;
    }

    /**
     * Sets the value of the monkName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setMonkName(String value) {
        this.monkName = value;
    }

}
