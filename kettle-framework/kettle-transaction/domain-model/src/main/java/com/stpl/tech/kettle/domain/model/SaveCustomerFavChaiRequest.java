package com.stpl.tech.kettle.domain.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.stpl.tech.master.recipe.read.model.RecipeDetailVO;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;
import java.io.Serializable;
import java.util.Objects;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name="SaveCustomerFavChaiRequest",propOrder = {"customerBasicInfo","orderItemDetails","recipeDetails"})
@JsonIgnoreProperties(ignoreUnknown=true)
public class SaveCustomerFavChaiRequest implements Serializable {

    private static final long serialVersionUID = 1828179878232227350L;

    @XmlElement(required = true)
    @Field
    @JsonProperty(value = "customerBasicInfo",required = true)

    protected CustomerBasicInfo customerBasicInfo;

    @Field
    @JsonProperty(value ="orderDetails",required = true)
    protected SelectedOrderItem orderItemDetails;

    @Field
    @JsonProperty(value ="productDetails")
    protected SelectedProductDetails productDetails;


    @Field
    @JsonProperty("recipeDetails")
    protected RecipeDetailVO recipeDetails;

    @Field
    @JsonProperty(value = "isFavChaiMarked",required = true)
    protected boolean isFavChaiMarked;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SavedChai savedChai = (SavedChai) o;
        return savedChai.getProductId().equals(getOrderItemDetails().getProductId());
//                Objects.equals(getDimension(), savedChai.getDimension()) &&
//                Objects.equals(getRecipeId(), savedChai.getRecipeId()) &&
//                Objects.equals(getRecipeProfile(), savedChai.getRecipeProfile()) ;
//                Objects.equals(getProductShortCode(), savedChai.getProductShortCode());
    }
    public CustomerBasicInfo getCustomerBasicInfo() {
        if(customerBasicInfo==null){
            return new CustomerBasicInfo();
        }
        return customerBasicInfo;
    }

    public void setCustomerBasicInfo(CustomerBasicInfo customerBasicInfo) {
        this.customerBasicInfo = customerBasicInfo;
    }

    public SelectedOrderItem getOrderItemDetails() {
        if(orderItemDetails==null){
            return new SelectedOrderItem();
        }
        return orderItemDetails;
    }

    public void setOrderItemDetails(SelectedOrderItem orderItemDetails) {
        this.orderItemDetails = orderItemDetails;
    }

    public RecipeDetailVO getRecipeDetails() {
        return recipeDetails;
    }

    public void setRecipeDetails(RecipeDetailVO recipeDetails) {
        this.recipeDetails = recipeDetails;
    }

    public boolean isFavChaiMarked() {
        return isFavChaiMarked;
    }

    public void setFavChaiMarked(boolean favChaiMarked) {
        isFavChaiMarked = favChaiMarked;
    }
}
