package com.stpl.tech.kettle.offer.model;

import java.util.List;

public class OrderData {

	private boolean newCustomer;
	private boolean offer;
	private int hotBeverageCount;
	private int coldBeverageCount;
	private int foodCount;
	private int othersCount;
	private int merchandiseCount;
	private DaySlot slot;
	private int unitId;
	private String rule;
	private List<Integer> productIds;

	public OrderData() {
		slot = DaySlot.getCurrentDaySlot(); // 0 to 23
	}

	public boolean isNewCustomer() {
		return newCustomer;
	}

	public void setNewCustomer(boolean newCustomer) {
		this.newCustomer = newCustomer;
	}

	public int getHotBeverageCount() {
		return hotBeverageCount;
	}

	public void setHotBeverageCount(int hotBeverageCount) {
		this.hotBeverageCount = hotBeverageCount;
	}

	public int getColdBeverageCount() {
		return coldBeverageCount;
	}

	public void setColdBeverageCount(int coldBeverageCount) {
		this.coldBeverageCount = coldBeverageCount;
	}

	public int getFoodCount() {
		return foodCount;
	}

	public void setFoodCount(int foodCount) {
		this.foodCount = foodCount;
	}

	public int getOthersCount() {
		return othersCount;
	}

	public void setOthersCount(int othersCount) {
		this.othersCount = othersCount;
	}

	public int getMerchandiseCount() {
		System.out.println("merchandiseCount " + merchandiseCount);
		return merchandiseCount;
	}

	public void setMerchandiseCount(int merchandiseCount) {
		this.merchandiseCount = merchandiseCount;
	}

	public DaySlot getSlot() {
		return slot;
	}

	public void setSlot(DaySlot slot) {
		this.slot = slot;
	}


	public List<Integer> getProductIds() {
		return productIds;
	}

	public void setProductIds(List<Integer> productIds) {
		this.productIds = productIds;
	}

	public String getRule() {
		return rule;
	}

	public void setRule(String rule) {
		this.rule = rule;
	}

	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	public boolean isOffer() {
		return offer;
	}

	public void setOffer(boolean offer) {
		this.offer = offer;
	}

	@Override
	public String toString() {
		return "OrderData [newCustomer=" + newCustomer + ", offer=" + offer + ", hotBeverageCount=" + hotBeverageCount
				+ ", coldBeverageCount=" + coldBeverageCount + ", foodCount=" + foodCount + ", othersCount="
				+ othersCount + ", merchandiseCount=" + merchandiseCount + ", slot=" + slot + ", unitId=" + unitId + ", rule=" + rule + ", productIds=" + productIds + "]";
	}

}
