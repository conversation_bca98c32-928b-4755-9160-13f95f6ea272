package com.stpl.tech.kettle.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SavedChai implements Serializable {
    private static final long serialVersionUID = 4887057544137305488L;
    private Integer customizationId;

    private Integer customerId;

    private Integer productId;

    private String productName;

    private String tagType;

    private String status;

    private Date creationTime;

    private Date createdAt;

    private Integer quantity;

    private String dimension;

    private String consumeType;

    private Integer sourceId;

    private String sourceName;

    private Integer recipeId;

    private String recipeProfile;

    private String productShortCode;

    private String previousTagType;

    @Builder.Default
    private List<ChaiCustomization> chaiCustomizationList= new ArrayList<>();

    @Override
    public String toString() {
        return "SavedChai{" +
                "customizationId=" + customizationId +
                ", customerId=" + customerId +
                ", productId=" + productId +
                ", productName='" + productName + '\'' +
                ", tagType='" + tagType + '\'' +
                ", status='" + status + '\'' +
                ", creationTime=" + creationTime +
                ", createdAt=" + createdAt +
                ", quantity=" + quantity +
                ", dimension=" + dimension +
                ", consumeType='" + consumeType + '\'' +
                ", sourceId='" + sourceId + '\'' +
                ", sourceName='" + sourceName + '\'' +
                ", recipeId='" + recipeId + '\'' +
                ", recipeProfile='" + recipeProfile + '\'' +
                ", productShortCode='" + productShortCode + '\'' +
                ", chaiCustomizationList=" + chaiCustomizationList +
                '}';
    }
    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        SavedChai savedChai = (SavedChai) o;
        return getProductId().equals(savedChai.getProductId()) &&
                getProductName().equals(savedChai.getProductName()) &&
                getTagType().equals(savedChai.getTagType()) &&
                Objects.equals(getDimension(), savedChai.getDimension()) &&
                Objects.equals(getRecipeId(), savedChai.getRecipeId()) &&
                Objects.equals(getRecipeProfile(), savedChai.getRecipeProfile()) &&
                Objects.equals(getProductShortCode(), savedChai.getProductShortCode());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getProductId(), getProductName(), getTagType(), getDimension(), getRecipeId(), getRecipeProfile(), getProductShortCode(), getChaiCustomizationList());
    }
}
