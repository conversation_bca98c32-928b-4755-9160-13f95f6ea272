 
package com.stpl.tech.kettle.core.data.budget.vo;


public class ConsumableBudgetRequest {
	private ConsumablesAggregate requestedAggregate;
	private ConsumablesAggregate currentAggregate;
	private int unitId;
	private int createdBy;
	private boolean fixedAsset;
	
	public ConsumablesAggregate getRequestedAggregate() {
		return requestedAggregate;
	}
	public void setRequestedAggregate(ConsumablesAggregate requestedAggregate) {
		this.requestedAggregate = requestedAggregate;
	}
	public ConsumablesAggregate getCurrentAggregate() {
		return currentAggregate;
	}
	public void setCurrentAggregate(ConsumablesAggregate currentAggregate) {
		this.currentAggregate = currentAggregate;
	}
	public int getUnitId() {
		return unitId;
	}
	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}
	public int getCreatedBy() {
		return createdBy;
	}
	public void setCreatedBy(int createdBy) {
		this.createdBy = createdBy;
	}
	public boolean isFixedAsset() {
		return fixedAsset;
	}
	public void setFixedAsset(boolean fixedAsset) {
		this.fixedAsset = fixedAsset;
	}
	
}
