//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.05.10 at 07:11:28 PM IST
//

package com.stpl.tech.analytics.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.stpl.tech.master.domain.model.Adapter2;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Version;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * Java class for anonymous complex type.
 *
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 *
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="businessDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="data" type="{http://www.w3schools.com}AggregateSaleData"/&gt;
 *         &lt;element name="drillDown" type="{http://www.w3schools.com}UnitReportData" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = { "name", "businessDate", "data", "drillDown" })
@XmlRootElement(name = "SubCategoryReportData")
@Document
public class SubCategoryReportData  implements Serializable {

	/**
	 *
	 */
	private static final long serialVersionUID = 1212668684910994289L;
	@Id
	private String _id;

	@JsonIgnore
	private Long version;

	/**
	 * Added to avoid a runtime error whereby the detachAll property is checked
	 * for existence but not actually used.
	 */
	private String detachAll;
	@XmlElement(required = true)
	protected String name;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter2.class)
	@XmlSchemaType(name = "date")
	protected Date businessDate;
	@XmlElement(required = true)
	protected AggregateSaleData data;
	protected List<UnitReportData> drillDown;


	/**
	 * @return the _id
	 */
	public String get_id() {
		return _id;
	}

	/**
	 * @param _id the _id to set
	 */
	public void set_id(String _id) {
		this._id = _id;
	}

	/**
	 * @return the version
	 */
	public Long getVersion() {
		return version;
	}

	/**
	 * @param version the version to set
	 */
	public void setVersion(Long version) {
		this.version = version;
	}

	/**
	 * @return the detachAll
	 */
	public String getDetachAll() {
		return detachAll;
	}

	/**
	 * @param detachAll the detachAll to set
	 */
	public void setDetachAll(String detachAll) {
		this.detachAll = detachAll;
	}

	/**
	 * Gets the value of the name property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getName() {
		return name;
	}

	/**
	 * Sets the value of the name property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setName(String value) {
		this.name = value;
	}

	/**
	 * Gets the value of the businessDate property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public Date getBusinessDate() {
		return businessDate;
	}

	/**
	 * Sets the value of the businessDate property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setBusinessDate(Date value) {
		this.businessDate = value;
	}

	/**
	 * Gets the value of the data property.
	 *
	 * @return possible object is {@link AggregateSaleData }
	 *
	 */
	public AggregateSaleData getData() {
		return data;
	}

	/**
	 * Sets the value of the data property.
	 *
	 * @param value
	 *            allowed object is {@link AggregateSaleData }
	 *
	 */
	public void setData(AggregateSaleData value) {
		this.data = value;
	}

	/**
	 * Gets the value of the drillDown property.
	 *
	 * <p>
	 * This accessor method returns a reference to the live list, not a
	 * snapshot. Therefore any modification you make to the returned list will
	 * be present inside the JAXB object. This is why there is not a
	 * <CODE>set</CODE> method for the drillDown property.
	 *
	 * <p>
	 * For example, to add a new item, do as follows:
	 *
	 * <pre>
	 * getDrillDown().add(newItem);
	 * </pre>
	 *
	 *
	 * <p>
	 * Objects of the following type(s) are allowed in the list
	 * {@link UnitReportData }
	 *
	 *
	 */
	public List<UnitReportData> getDrillDown() {
		if (drillDown == null) {
			drillDown = new ArrayList<UnitReportData>();
		}
		return this.drillDown;
	}

}
