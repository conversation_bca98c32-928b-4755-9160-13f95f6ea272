package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;
import java.util.Date;

public class SubscriptionOfferInfoDetail implements Serializable {
    private static final long serialVersionUID = -6314976048575109390L;
    private String subscriptionCode;
    private String customerName;
    private String offerText;
    private Integer offerValue;
    private Integer validDays;
    private Date expiryDate;
    private String subscriptionUrl;

    public SubscriptionOfferInfoDetail() {
    }

    public SubscriptionOfferInfoDetail(String subscriptionCode, Integer offerValue) {
        this.subscriptionCode = subscriptionCode;
        this.offerValue = offerValue;
    }

    public String getSubscriptionCode() {
        return subscriptionCode;
    }

    public void setSubscriptionCode(String subscriptionCode) {
        this.subscriptionCode = subscriptionCode;
    }

    public Integer getOfferValue() {
        return offerValue;
    }

    public void setOfferValue(Integer offerValue) {
        this.offerValue = offerValue;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public Integer getValidDays() {
        return validDays;
    }

    public void setValidDays(Integer validDays) {
        this.validDays = validDays;
    }

    public Date getExpiryDate() {
        return expiryDate;
    }

    public void setExpiryDate(Date expiryDate) {
        this.expiryDate = expiryDate;
    }

    public String getSubscriptionUrl() {
        return subscriptionUrl;
    }

    public void setSubscriptionUrl(String subscriptionUrl) {
        this.subscriptionUrl = subscriptionUrl;
    }

    public String getOfferText() {
        return offerText;
    }

    public void setOfferText(String offerText) {
        this.offerText = offerText;
    }
}
