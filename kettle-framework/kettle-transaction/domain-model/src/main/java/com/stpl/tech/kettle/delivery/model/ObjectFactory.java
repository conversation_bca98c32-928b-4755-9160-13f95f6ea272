/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2015.12.23 at 12:37:04 PM IST 
//


package com.stpl.tech.kettle.delivery.model;

import javax.xml.bind.annotation.XmlRegistry;


/**
 * This object contains factory methods for each 
 * Java content interface and Java element interface 
 * generated in the com.stpl.tech.kettle.delivery.model package. 
 * <p>An ObjectFactory allows you to programatically 
 * construct new instances of the Java representation 
 * for XML content. The Java representation of XML 
 * content can consist of schema derived interfaces 
 * and classes representing the binding of schema 
 * type definitions, element declarations and model 
 * groups.  Factory methods for each of these are 
 * provided in this class.
 * 
 */
@XmlRegistry
public class ObjectFactory {


    /**
     * Create a new ObjectFactory that can be used to create new instances of schema derived classes for package: com.stpl.tech.kettle.delivery.model
     * 
     */
    public ObjectFactory() {
    }

    /**
     * Create an instance of {@link DeliveryResponse }
     * 
     */
    public DeliveryResponse createDeliveryResponse() {
        return new DeliveryResponse();
    }

    /**
     * Create an instance of {@link SFXRequest }
     * 
     */
    public SFXRequest createSFXRequest() {
        return new SFXRequest();
    }

    /**
     * Create an instance of {@link SFXOrder }
     * 
     */
    public SFXOrder createSFXOrder() {
        return new SFXOrder();
    }

    /**
     * Create an instance of {@link SFXCustomer }
     * 
     */
    public SFXCustomer createSFXCustomer() {
        return new SFXCustomer();
    }

    /**
     * Create an instance of {@link HLDRequest }
     * 
     */
    public HLDRequest createHLDRequest() {
        return new HLDRequest();
    }

    /**
     * Create an instance of {@link HLDAddrMetadata }
     * 
     */
    public HLDAddrMetadata createHLDAddrMetadata() {
        return new HLDAddrMetadata();
    }

    /**
     * Create an instance of {@link HLDSlotOrder }
     * 
     */
    public HLDSlotOrder createHLDSlotOrder() {
        return new HLDSlotOrder();
    }

    /**
     * Create an instance of {@link OPNRequest }
     * 
     */
    public OPNRequest createOPNRequest() {
        return new OPNRequest();
    }

    /**
     * Create an instance of {@link HLDResponse }
     * 
     */
    public HLDResponse createHLDResponse() {
        return new HLDResponse();
    }

    /**
     * Create an instance of {@link OPNResponse }
     * 
     */
    public OPNResponse createOPNResponse() {
        return new OPNResponse();
    }

    /**
     * Create an instance of {@link OPNErrorResponse }
     * 
     */
    public OPNErrorResponse createOPNErrorResponse() {
        return new OPNErrorResponse();
    }

    /**
     * Create an instance of {@link SFXResponse }
     * 
     */
    public SFXResponse createSFXResponse() {
        return new SFXResponse();
    }

    /**
     * Create an instance of {@link SFXOrderResponse }
     * 
     */
    public SFXOrderResponse createSFXOrderResponse() {
        return new SFXOrderResponse();
    }

    /**
     * Create an instance of {@link SFXErrorResponse }
     * 
     */
    public SFXErrorResponse createSFXErrorResponse() {
        return new SFXErrorResponse();
    }

    /**
     * Create an instance of {@link AuthorizationObject }
     * 
     */
    public AuthorizationObject createAuthorizationObject() {
        return new AuthorizationObject();
    }

    /**
     * Create an instance of {@link HLDErrorResponse }
     * 
     */
    public HLDErrorResponse createHLDErrorResponse() {
        return new HLDErrorResponse();
    }

    /**
     * Create an instance of {@link SFXRider }
     * 
     */
    public SFXRider createSFXRider() {
        return new SFXRider();
    }

    /**
     * Create an instance of {@link SFXRiderLocation }
     * 
     */
    public SFXRiderLocation createSFXRiderLocation() {
        return new SFXRiderLocation();
    }

    /**
     * Create an instance of {@link HLDAddress }
     * 
     */
    public HLDAddress createHLDAddress() {
        return new HLDAddress();
    }

}
