package com.stpl.tech.kettle.domain.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.master.domain.model.Adapter1;
import com.stpl.tech.master.domain.model.BillType;
import com.stpl.tech.util.domain.adapter.BigDecimalDeserializer;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SelectedOrderItem",propOrder = {"itemId","productId","productName","quantity","price","originalPrice","totalAmount"})
@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class SelectedOrderItem extends OrderItem implements Serializable {

    private static final long serialVersionUID = -6399778306438878937L;

    @Field
    @JsonProperty("itemId")
    protected int itemId;
    @Field
    @JsonProperty(value = "productId",required = true)
    protected int productId;
    @XmlElement(required = true)
    @Field
    @JsonProperty(value = "productName",required = true)
    protected String productName;
    @Field
    @JsonProperty(value = "quantity",required = true)
    protected int quantity;
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "decimal")
    @Field
    @JsonProperty(value = "price",required = true)
    protected BigDecimal price;
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "decimal")
    @Field
    @JsonProperty(value = "originalPrice",required = true)
    protected BigDecimal originalPrice;
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "decimal")
    @Field
    @JsonProperty(value = "totalAmount",required = true)
    protected BigDecimal totalAmount;
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "decimal")
    @Field
    @JsonProperty(value = "amount",required = true)
    protected BigDecimal amount;
    @XmlElement(required = true, nillable = true)
    @JsonProperty(value = "discountDetail",required = true)
    @Field
    protected DiscountDetail discountDetail;
    @XmlElement(required = true)
    @Field
    @JsonProperty("complimentaryDetail")
    protected ComplimentaryDetail complimentaryDetail;
    @XmlElement(required = true, nillable = true)
    @Field
    @JsonProperty("dimension")
    protected String dimension;
    @XmlElement(required = true, defaultValue = "NET_PRICE")
    @XmlSchemaType(name = "string")
    @Field
    @JsonProperty("billType")
    protected BillType billType;

    @XmlElement(nillable = true)
    @Field
    @JsonProperty(value = "composition",required = true)
    protected OrderItemComposition composition;

    @XmlElement(nillable = true)
    @Field
    @JsonProperty("recipeId")
    protected int recipeId;
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "decimal")
    @Field
    @JsonProperty("tax")
    protected BigDecimal tax;

    @Field
    @JsonProperty("taxes")
    protected List<TaxDetail> taxes;

    @Field
    @JsonProperty(value="consumeType",required = true)
    protected String consumeType ;

    @Field
    @JsonProperty(value="tagType",required = true)
    protected String tagType;

    @Field
    @JsonProperty(value="unitId",required = true)
    protected int unitId ;

    @Field
    @JsonProperty(value="brandId",required = true)
    protected Integer brandId ;

    @Field
    @JsonProperty(value="sourceId")
    protected Integer sourceId;

    @Field
    @JsonProperty(value="sourceName")
    protected String sourceName;

    @Override
    public int getItemId() {
        return itemId;
    }

    @Override
    public void setItemId(int itemId) {
        this.itemId = itemId;
    }

    @Override
    public int getProductId() {
        return productId;
    }

    @Override
    public void setProductId(int productId) {
        this.productId = productId;
    }

    @Override
    public String getProductName() {
        return productName;
    }

    @Override
    public void setProductName(String productName) {
        this.productName = productName;
    }

    @Override
    public int getQuantity() {
        return quantity;
    }

    @Override
    public void setQuantity(int quantity) {
        this.quantity = quantity;
    }

    @Override
    public BigDecimal getPrice() {
        return price;
    }

    @Override
    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    @Override
    public BigDecimal getOriginalPrice() {
        return originalPrice;
    }

    @Override
    public void setOriginalPrice(BigDecimal originalPrice) {
        this.originalPrice = originalPrice;
    }

    @Override
    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    @Override
    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    @Override
    public BigDecimal getAmount() {
        return amount;
    }

    @Override
    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    @Override
    public DiscountDetail getDiscountDetail() {
        return discountDetail;
    }

    @Override
    public void setDiscountDetail(DiscountDetail discountDetail) {
        this.discountDetail = discountDetail;
    }

    @Override
    public ComplimentaryDetail getComplimentaryDetail() {
        return complimentaryDetail;
    }

    @Override
    public void setComplimentaryDetail(ComplimentaryDetail complimentaryDetail) {
        this.complimentaryDetail = complimentaryDetail;
    }

    @Override
    public String getDimension() {
        return dimension;
    }

    @Override
    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    @Override
    public BillType getBillType() {
        return billType;
    }

    @Override
    public void setBillType(BillType billType) {
        this.billType = billType;
    }

    @Override
    public OrderItemComposition getComposition() {
        return composition;
    }

    @Override
    public void setComposition(OrderItemComposition composition) {
        this.composition = composition;
    }

    @Override
    public int getRecipeId() {
        return recipeId;
    }

    @Override
    public void setRecipeId(int recipeId) {
        this.recipeId = recipeId;
    }

    @Override
    public BigDecimal getTax() {
        return tax;
    }

    @Override
    public void setTax(BigDecimal tax) {
        this.tax = tax;
    }

    @Override
    public List<TaxDetail> getTaxes() {
        return taxes;
    }

    public void setTaxes(List<TaxDetail> taxes) {
        this.taxes = taxes;
    }

    public String getConsumeType() {
        return consumeType;
    }

    public void setConsumeType(String consumeType) {
        this.consumeType = consumeType;
    }

    public String getTagType() {
        return tagType;
    }

    public void setTagType(String tagType) {
        this.tagType = tagType;
    }

    public int getUnitId() {
        return unitId;
    }

    public Integer getBrandId() {
        return brandId;
    }

    public void setUnitId(int unitId) {
        this.unitId = unitId;
    }

    public void setBrandId(Integer brandId) {
        this.brandId = brandId;
    }

    public void setSourceId(Integer sourceId) {
        this.sourceId = sourceId;
    }

    public Integer getSourceId() {
        return sourceId;
    }
    public String getSourceName() {
        return sourceName;
    }

    public void setSourceName(String sourceName) {
        this.sourceName = sourceName;
    }
}

