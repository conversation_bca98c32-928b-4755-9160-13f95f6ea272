package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "OrderItem", propOrder = { "brandId", "partnerName", "discountName", "discountType",
		"discountCategory", "discountValue", "discountAmount", "discountIsTaxed", "discountAppliedOn", "voucherCode"})

@Document
public class OrderDiscountData implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 4778973176476607094L;

	@Id
	private String _id;

	/*
	 * @Version
	 * 
	 * @JsonIgnore private Long version;
	 * 
	 *//**
		 * Added to avoid a runtime error whereby the detachAll property is
		 * checked for existence but not actually used.
		 *//*
		 * private String detachAll;
		 */
	@XmlElement(required = true)
	protected String partnerName;
	@Field
	protected String discountName;
	@Field
	protected String discountType;
	@Field
	protected String discountCategory;
	@Field
	protected Float discountValue;
	@Field
	protected Float discountAmount;
	@Field
	protected Boolean discountIsTaxed;
	@Field
	protected Float discountAppliedOn;
	@Field
	protected String voucherCode;
	@Field
	protected String isPartnerDiscount;
	
	public String get_id() {
		return _id;
	}
	public void set_id(String _id) {
		this._id = _id;
	}
	public String getPartnerName() {
		return partnerName;
	}
	public void setPartnerName(String partnerName) {
		this.partnerName = partnerName;
	}
	public String getDiscountName() {
		return discountName;
	}
	public void setDiscountName(String discountName) {
		this.discountName = discountName;
	}
	public String getDiscountType() {
		return discountType;
	}
	public void setDiscountType(String discountType) {
		this.discountType = discountType;
	}
	public String getDiscountCategory() {
		return discountCategory;
	}
	public void setDiscountCategory(String discountCategory) {
		this.discountCategory = discountCategory;
	}
	public Float getDiscountValue() {
		return discountValue;
	}
	public void setDiscountValue(Float discountValue) {
		this.discountValue = discountValue;
	}
	public Float getDiscountAmount() {
		return discountAmount;
	}
	public void setDiscountAmount(Float discountAmount) {
		this.discountAmount = discountAmount;
	}
	public Boolean getDiscountIsTaxed() {
		return discountIsTaxed;
	}
	public void setDiscountIsTaxed(Boolean discountIsTaxed) {
		this.discountIsTaxed = discountIsTaxed;
	}
	public Float getDiscountAppliedOn() {
		return discountAppliedOn;
	}
	public void setDiscountAppliedOn(Float discountAppliedOn) {
		this.discountAppliedOn = discountAppliedOn;
	}
	public String getVoucherCode() {
		return voucherCode;
	}
	public void setVoucherCode(String voucherCode) {
		this.voucherCode = voucherCode;
	}
	public String getIsPartnerDiscount() {
		return isPartnerDiscount;
	}
	public void setIsPartnerDiscount(String isPartnerDiscount) {
		this.isPartnerDiscount = isPartnerDiscount;
	}
	
}
