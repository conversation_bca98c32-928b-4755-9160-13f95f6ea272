package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;


public class GyftrVoucher implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = -4840201197841957453L;
	
	List<GyftrVBatchConsumeResult> vBatchConsumeResult = new ArrayList<>();

	public List<GyftrVBatchConsumeResult> getvBatchConsumeResult() {
		return vBatchConsumeResult;
	}

	public void setvBatchConsumeResult(List<GyftrVBatchConsumeResult> vBatchConsumeResult) {
		this.vBatchConsumeResult = vBatchConsumeResult;
	}

	@Override
	public String toString() {
		return "GytrVoucher [vBatchConsumeResult=" + vBatchConsumeResult + "]";
	}

}
