/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.model;

import java.math.BigDecimal;
import java.util.Map;
import java.util.TreeMap;

import com.stpl.tech.kettle.core.ServiceType;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.master.domain.model.PaymentMode;
import com.stpl.tech.master.tax.model.Taxation;

public class SettlementReport {

	private int id;
	private String name;
	private String code;
	private BigDecimal grossAmount = new BigDecimal(0);
	private BigDecimal discountAmount = new BigDecimal(0);
	private BigDecimal amount = new BigDecimal(0);
	private Map<Taxation, BigDecimal> taxes = new TreeMap<>();
	private BigDecimal roundOff = new BigDecimal(0);
	private BigDecimal total = new BigDecimal(0);
	private BigDecimal tip = new BigDecimal(0);
	private BigDecimal extraVouchers = new BigDecimal(0);
	private int noOfBills;

	public SettlementReport() {

	}

	public SettlementReport(PaymentMode mode) {
		this.id = mode.getId();
		this.name = mode.getDescription();
		this.code = mode.getName();
	}

	public SettlementReport(IdCodeName mode) {
		this.id = mode.getId();
		this.name = mode.getName();
		this.code = mode.getCode();
	}

	public SettlementReport(ServiceType type) {
		this.id = type.ordinal();
		this.code = type.name();
		this.name = type.getDesc();
	}

	public int getId() {
		return id;
	}

	public void setId(int id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public BigDecimal getAmount() {
		return amount;
	}

	public void setAmount(BigDecimal amount) {
		this.amount = amount;
	}

	public Map<Taxation, BigDecimal> getTaxes() {
		return taxes;
	}

	public void setTaxes(Map<Taxation, BigDecimal> taxes) {
		this.taxes = taxes;
	}

	public BigDecimal getRoundOff() {
		return roundOff;
	}

	public void setRoundOff(BigDecimal roundOff) {
		this.roundOff = roundOff;
	}

	public BigDecimal getTotal() {
		return total;
	}

	public void setTotal(BigDecimal total) {
		this.total = total;
	}

	public BigDecimal getTip() {
		return tip;
	}

	public void setTip(BigDecimal tip) {
		this.tip = tip;
	}

	public int getNoOfBills() {
		return noOfBills;
	}

	public void setNoOfBills(int noOfBills) {
		this.noOfBills = noOfBills;
	}

	public BigDecimal getGrossAmount() {
		return grossAmount;
	}

	public void setGrossAmount(BigDecimal grossAmount) {
		this.grossAmount = grossAmount;
	}

	public BigDecimal getDiscountAmount() {
		return discountAmount;
	}

	public void setDiscountAmount(BigDecimal discountAmount) {
		this.discountAmount = discountAmount;
	}

	public BigDecimal getExtraVouchers() {
		return extraVouchers;
	}

	public void setExtraVouchers(BigDecimal extraVouchers) {
		this.extraVouchers = extraVouchers;
	}

	public void addTax(Taxation td, BigDecimal tax) {
		if (!taxes.containsKey(td)) {
			taxes.put(td, BigDecimal.ZERO);
		}
		taxes.put(td, taxes.get(td).add(tax));
	}

}
