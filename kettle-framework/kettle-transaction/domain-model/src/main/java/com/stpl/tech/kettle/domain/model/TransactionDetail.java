/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.02.01 at 12:19:28 PM IST
//

package com.stpl.tech.kettle.domain.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.master.domain.model.Adapter1;
import com.stpl.tech.util.domain.adapter.BigDecimalDeserializer;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * Java class for TransactionDetail complex type.
 *
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 *
 * <pre>
 * &lt;complexType name="TransactionDetail"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="saleAmount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="totalAmount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="taxableAmount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="discountDetail" type="{http://www.w3schools.com}DiscountDetail"/&gt;
 *         &lt;element name="paidAmount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="roundOffValue" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="savings" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "TransactionDetail", propOrder = { "saleAmount", "totalAmount", "taxableAmount", "discountDetail", "paidAmount", "roundOffValue", "savings" })
@Document
public class TransactionDetail implements Serializable{

	/**
	 *
	 */
	private static final long serialVersionUID = -3256468249758673855L;

	@Id
	private String _id;

	/*@Version
	@JsonIgnore
	private Long version;

	*//**
	 * Added to avoid a runtime error whereby the detachAll property is checked
	 * for existence but not actually used.
	 *//*
	private String detachAll;*/
	@JsonDeserialize(using=BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal saleAmount;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal totalAmount;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal taxableAmount;
	@XmlElement(required = true, nillable = true)
	@Field
	protected DiscountDetail discountDetail;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal paidAmount;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal roundOffValue;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal collectionAmount;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal savings;

	@JsonDeserialize(using=BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal tax;
	protected BigDecimal collectionTax;
	protected List<TaxDetail> taxes;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	protected BigDecimal serviceCharge;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	protected BigDecimal serviceChargePercent;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	protected BigDecimal serviceTaxAmount;
	protected String serviceChargeDescription;

	public String get_id() {
		return _id;
	}

	public void set_id(String _id) {
		this._id = _id;
	}

	/*public Long getVersion() {
		return version;
	}

	public void setVersion(Long version) {
		this.version = version;
	}

	public String getDetachAll() {
		return detachAll;
	}

	public void setDetachAll(String detachAll) {
		this.detachAll = detachAll;
	}*/

	/**
	 * Gets the value of the saleAmount property.
	 *
	 * @return possible object is {@link BigDecimal }
	 *
	 */
	public BigDecimal getSaleAmount() {
		return saleAmount;
	}

	/**
	 * Sets the value of the saleAmount property.
	 *
	 * @param value
	 *            allowed object is {@link BigDecimal }
	 *
	 */
	public void setSaleAmount(BigDecimal value) {
		this.saleAmount = value;
	}

	/**
	 * Gets the value of the totalAmount property.
	 *
	 * @return possible object is {@link BigDecimal }
	 *
	 */
	public BigDecimal getTotalAmount() {
		return totalAmount;
	}

	/**
	 * Sets the value of the totalAmount property.
	 *
	 * @param value
	 *            allowed object is {@link BigDecimal }
	 *
	 */
	public void setTotalAmount(BigDecimal value) {
		this.totalAmount = value;
	}

	/**
	 * Gets the value of the taxableAmount property.
	 *
	 * @return possible object is {@link BigDecimal }
	 *
	 */
	public BigDecimal getTaxableAmount() {
		return taxableAmount;
	}

	/**
	 * Sets the value of the taxableAmount property.
	 *
	 * @param value
	 *            allowed object is {@link BigDecimal }
	 *
	 */
	public void setTaxableAmount(BigDecimal value) {
		this.taxableAmount = value;
	}

	/**
	 * Gets the value of the discountDetail property.
	 *
	 * @return possible object is {@link DiscountDetail }
	 *
	 */
	public DiscountDetail getDiscountDetail() {
		return discountDetail;
	}

	/**
	 * Sets the value of the discountDetail property.
	 *
	 * @param value
	 *            allowed object is {@link DiscountDetail }
	 *
	 */
	public void setDiscountDetail(DiscountDetail value) {
		this.discountDetail = value;
	}

	/**
	 * Gets the value of the paidAmount property.
	 *
	 * @return possible object is {@link BigDecimal }
	 *
	 */
	public BigDecimal getPaidAmount() {
		return paidAmount;
	}

	/**
	 * Sets the value of the paidAmount property.
	 *
	 * @param value
	 *            allowed object is {@link BigDecimal }
	 *
	 */
	public void setPaidAmount(BigDecimal value) {
		this.paidAmount = value;
	}

	/**
	 * Gets the value of the roundOffValue property.
	 *
	 * @return possible object is {@link BigDecimal }
	 *
	 */
	public BigDecimal getRoundOffValue() {
		return roundOffValue;
	}

	/**
	 * Sets the value of the roundOffValue property.
	 *
	 * @param value
	 *            allowed object is {@link BigDecimal }
	 *
	 */
	public void setRoundOffValue(BigDecimal value) {
		this.roundOffValue = value;
	}

	/**
	 * Gets the value of the savings property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public BigDecimal getSavings() {
		return savings;
	}

	/**
	 * Sets the value of the savings property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setSavings(BigDecimal value) {
		this.savings = value;
	}


	public BigDecimal getTax() {
		return tax;
	}

	public void setTax(BigDecimal tax) {
		this.tax = tax;
	}

	public BigDecimal getCollectionTax() {
		return collectionTax;
	}

	public void setCollectionTax(BigDecimal collectionTax) {
		this.collectionTax = collectionTax;
	}

	public List<TaxDetail> getTaxes() {
		if(taxes == null){
			taxes =  new ArrayList<>();
		}
		return taxes;
	}

	public BigDecimal getCollectionAmount() {
		return collectionAmount;
	}

	public void setCollectionAmount(BigDecimal collectionAmount) {
		this.collectionAmount = collectionAmount;
	}

	public BigDecimal getServiceCharge() {
		return serviceCharge;
	}

	public void setServiceCharge(BigDecimal serviceCharge) {
		this.serviceCharge = serviceCharge;
	}

	public BigDecimal getServiceChargePercent() {
		return serviceChargePercent;
	}

	public void setServiceChargePercent(BigDecimal serviceChargePercent) {
		this.serviceChargePercent = serviceChargePercent;
	}

	public BigDecimal getServiceTaxAmount() {
		return serviceTaxAmount;
	}

	public void setServiceTaxAmount(BigDecimal serviceTaxAmount) {
		this.serviceTaxAmount = serviceTaxAmount;
	}

	public String getServiceChargeDescription() {
		return serviceChargeDescription;
	}

	public void setServiceChargeDescription(String serviceChargeDescription) {
		this.serviceChargeDescription = serviceChargeDescription;
	}
}
