package com.stpl.tech.kettle.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MilkVariant implements Serializable {
    Integer productId;
    String productName;

    String profile;

    Integer scmProductId;

    String scmProductName;

}

