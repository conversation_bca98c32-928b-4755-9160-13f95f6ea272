package com.stpl.tech.kettle.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class OrderItemReviewModal {

    private String itemName;
    private BigDecimal price;
    private Integer quantity;
    private Integer rating;
    private String itemReview;

    @Override
    public String toString(){
        return "{\"name\":\"" + itemName + "\", \"price\":" + price + ", \"quantity\":" + quantity + ", \"item_review\":\""+ "" + "\", \"rating\":\"\"}";
    }

}
