/*
 * Created By Shanmukh
 */

package com.stpl.tech.kettle.domain.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@JsonIgnoreProperties(ignoreUnknown=true)
public class ChaiMonkXTwo {

    @JsonProperty("chaiMonkId")
    private String chaiMonkId;

    @JsonProperty("taskAssignedId")
    private Integer taskAssignedId;

    @JsonProperty("isConnected")
    private boolean isConnected;

    @JsonProperty("isActive")
    private boolean isActive;

    @JsonProperty("isMonkProcessing")
    private boolean isMonkProcessing;

    @JsonProperty("isCleaning")
    private boolean isCleaning;

    @JsonProperty("isPouring")
    private boolean isPouring;

    @JsonProperty("currentTaskRecipe")
    private ChaiMonkXTwoRecipe currentTaskRecipe;

    @JsonProperty("lastSentRecipe")
    private ChaiMonkXTwoRecipe lastSentRecipe;

    @JsonProperty("monkStatus")
    private String monkStatus;

    @JsonProperty("resetReason")
    private String resetReason;
}
