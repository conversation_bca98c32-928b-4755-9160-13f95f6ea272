//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.05.10 at 07:11:28 PM IST 
//

package com.stpl.tech.analytics.model;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;

@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "CurrentData", propOrder = { "today", "target" })
public class CurrentData implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 2060853546855702828L;
	private UnitSaleData today;
	private UnitSaleData target;

	public UnitSaleData getToday() {
		return today;
	}

	public void setToday(UnitSaleData today) {
		this.today = today;
	}

	public UnitSaleData getTarget() {
		return target;
	}

	public void setTarget(UnitSaleData target) {
		this.target = target;
	}

}
