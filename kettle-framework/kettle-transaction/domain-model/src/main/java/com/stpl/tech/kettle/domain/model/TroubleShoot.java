package com.stpl.tech.kettle.domain.model;

import java.util.Date;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 02-02-2017.
 */

public class TroubleShoot {

    private int code;
    private String codeMeaning;

    private List<String> troubleShootSteps;

    private boolean acknowleged = false;

    private boolean loading = true;

    private final Date createdAt = new Date();

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getCodeMeaning() {
        return codeMeaning;
    }

    public void setCodeMeaning(String codeMeaning) {
        this.codeMeaning = codeMeaning;
    }

    public List<String> getTroubleShootSteps() {
        return troubleShootSteps;
    }

    public void setTroubleShootSteps(List<String> troubleShootSteps) {
        this.troubleShootSteps = troubleShootSteps;
    }

    public boolean isAcknowleged() {
        return acknowleged;
    }

    public void setAcknowleged(boolean acknowleged) {
        this.acknowleged = acknowleged;
    }

    @Override
    public boolean equals(Object obj) {
        return this.hashCode() == obj.hashCode();
    }

    @Override
    public int hashCode() {
        return this.code > 100 ? this.code - 95 : this.code; // TO handle re publish of trouble shoot data with changed status.
    }

    public boolean isLoading() {
        return loading;
    }

    public void setLoading(boolean loading) {
        this.loading = loading;
    }

    public TroubleShoot(int code, String codeMeaning, List<String> troubleShootSteps, boolean acknowleged, boolean loading) {
        this.code = code;
        this.codeMeaning = codeMeaning;
        this.troubleShootSteps = troubleShootSteps;
        this.acknowleged = acknowleged;
        this.loading = loading;
    }

    public TroubleShoot() {
    }
}
