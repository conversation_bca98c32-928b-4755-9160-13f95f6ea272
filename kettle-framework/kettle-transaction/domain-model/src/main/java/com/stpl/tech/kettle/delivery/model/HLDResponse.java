/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2015.12.23 at 07:13:57 PM IST 
//

package com.stpl.tech.kettle.delivery.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlType;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <p>
 * Java class for anonymous complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="task_id" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="status" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="delivery_boy_name" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="delivery_boy_phone" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="delivery_boy_latitude" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="delivery_boy_longitude" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="estimated_pickup_time" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="estimated_delivery_time" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="error" type="{http://www.w3schools.com}HLDErrorResponse"/&gt;
 *         &lt;element name="errorMessage" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "task_Id",
    "status",
    "delivery_Boy_Name",
    "delivery_Boy_Phone",
    "delivery_Boy_Latitude",
    "delivery_Boy_Longitude",
    "estimated_Pickup_Time",
    "estimated_Delivery_Time",
    "error",
    "errorMessage"
})
@XmlRootElement(name = "HLDResponse")
@JsonIgnoreProperties(ignoreUnknown=true)
public class HLDResponse implements DeliveryPartnerResponse {

	@XmlElement(name = "task_id", required = true)
	@JsonProperty("task_id")
	protected String task_Id;
	@XmlElement(required = true)
	@JsonProperty("status")
	protected String status;
	@XmlElement(name = "delivery_boy_name", required = true)
	@JsonProperty("delivery_boy_name")
	protected String delivery_Boy_Name;
	@XmlElement(name = "delivery_boy_phone", required = true)
	@JsonProperty("delivery_boy_phone")
	protected String delivery_Boy_Phone;
	@XmlElement(name = "delivery_boy_latitude", required = true)
	@JsonProperty("delivery_boy_latitude")
	protected String delivery_Boy_Latitude;
	@XmlElement(name = "delivery_boy_longitude", required = true)
	@JsonProperty("delivery_boy_longitude")
	protected String delivery_Boy_Longitude;
	@XmlElement(name = "estimated_pickup_time", required = true)
	@JsonProperty("estimated_pickup_time")
	protected String estimated_Pickup_Time;
	@XmlElement(name = "estimated_delivery_time", required = true)
	@JsonProperty("estimated_delivery_time")
	protected String estimated_Delivery_Time;
	@XmlElement(required = true)
	protected HLDErrorResponse error;
	@XmlElement(required = true)
    protected String errorMessage;

	/**
	 * Gets the value of the task_Id property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getTask_Id() {
		return task_Id;
	}

	/**
	 * Sets the value of the task_Id property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setTask_Id(String value) {
		this.task_Id = value;
	}

	/**
	 * Gets the value of the status property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getStatus() {
		return status;
	}

	/**
	 * Sets the value of the status property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setStatus(String value) {
		this.status = value;
	}

	/**
	 * Gets the value of the delivery_Boy_Name property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getDelivery_Boy_Name() {
		return delivery_Boy_Name;
	}

	/**
	 * Sets the value of the delivery_Boy_Name property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setDelivery_Boy_Name(String value) {
		this.delivery_Boy_Name = value;
	}

	/**
	 * Gets the value of the delivery_Boy_Phone property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getDelivery_Boy_Phone() {
		return delivery_Boy_Phone;
	}

	/**
	 * Sets the value of the delivery_Boy_Phone property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setDelivery_Boy_Phone(String value) {
		this.delivery_Boy_Phone = value;
	}

	/**
	 * Gets the value of the delivery_Boy_Latitude property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getDelivery_Boy_Latitude() {
		return delivery_Boy_Latitude;
	}

	/**
	 * Sets the value of the delivery_Boy_Latitude property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setDelivery_Boy_Latitude(String value) {
		this.delivery_Boy_Latitude = value;
	}

	/**
	 * Gets the value of the delivery_Boy_Longitude property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getDelivery_Boy_Longitude() {
		return delivery_Boy_Longitude;
	}

	/**
	 * Sets the value of the delivery_Boy_Longitude property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setDelivery_Boy_Longitude(String value) {
		this.delivery_Boy_Longitude = value;
	}

	/**
	 * Gets the value of the estimated_Pickup_Time property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getEstimated_Pickup_Time() {
		return estimated_Pickup_Time;
	}

	/**
	 * Sets the value of the estimated_Pickup_Time property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setEstimated_Pickup_Time(String value) {
		this.estimated_Pickup_Time = value;
	}

	/**
	 * Gets the value of the estimated_Delivery_Time property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getEstimated_Delivery_Time() {
		return estimated_Delivery_Time;
	}

	/**
	 * Sets the value of the estimated_Delivery_Time property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setEstimated_Delivery_Time(String value) {
		this.estimated_Delivery_Time = value;
	}

	/**
	 * Gets the value of the error property.
	 * 
	 * @return possible object is {@link HLDErrorResponse }
	 * 
	 */
	public HLDErrorResponse getError() {
		return error;
	}

	/**
	 * Sets the value of the error property.
	 * 
	 * @param value
	 *            allowed object is {@link HLDErrorResponse }
	 * 
	 */
	public void setError(HLDErrorResponse value) {
		this.error = value;
	}
	
    /**
     * Gets the value of the errorMessage property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getErrorMessage() {
        return errorMessage;
    }

    /**
     * Sets the value of the errorMessage property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setErrorMessage(String value) {
        this.errorMessage = value;
    }
}
