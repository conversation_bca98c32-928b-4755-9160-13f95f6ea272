/**
 * 
 */
package com.stpl.tech.kettle.domain.model.webengage.survey;

import java.io.Serializable;

/**
 * <AUTHOR>
 *
 */
public class WebEngageData implements Serializable {
	/**
	 * 
	 */
	private static final long serialVersionUID = -5310390744840581974L;

	private String id;

	private WebEngageQuestionResponse[] questionResponses;

	private String title;

	private String licenseCode;

	private WebEngageCustomerData customData;

	private String surveyId;

	private String totalQuestions;

	private WebEngageActivity activity;

	public String getId() {
		return id;
	}

	public void setId(String id) {
		this.id = id;
	}

	public WebEngageQuestionResponse[] getQuestionResponses() {
		return questionResponses;
	}

	public void setQuestionResponses(WebEngageQuestionResponse[] questionResponses) {
		this.questionResponses = questionResponses;
	}

	public String getTitle() {
		return title;
	}

	public void setTitle(String title) {
		this.title = title;
	}

	public String getLicenseCode() {
		return licenseCode;
	}

	public void setLicenseCode(String licenseCode) {
		this.licenseCode = licenseCode;
	}

	public WebEngageCustomerData getCustomData() {
		return customData;
	}

	public void setCustomData(WebEngageCustomerData customData) {
		this.customData = customData;
	}

	public String getSurveyId() {
		return surveyId;
	}

	public void setSurveyId(String surveyId) {
		this.surveyId = surveyId;
	}

	public String getTotalQuestions() {
		return totalQuestions;
	}

	public void setTotalQuestions(String totalQuestions) {
		this.totalQuestions = totalQuestions;
	}

	public WebEngageActivity getActivity() {
		return activity;
	}

	public void setActivity(WebEngageActivity activity) {
		this.activity = activity;
	}

	@Override
	public String toString() {
		return "ClassPojo [id = " + id + ", questionResponses = " + questionResponses + ", title = " + title
				+ ", licenseCode = " + licenseCode + ", customData = " + customData + ", surveyId = " + surveyId
				+ ", totalQuestions = " + totalQuestions + ", activity = " + activity + "]";
	}
}
