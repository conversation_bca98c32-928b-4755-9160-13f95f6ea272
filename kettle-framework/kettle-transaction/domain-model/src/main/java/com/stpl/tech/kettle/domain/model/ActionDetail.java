/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.01.09 at 05:35:16 PM IST
//

package com.stpl.tech.kettle.domain.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.master.CustomJsonDateDeserializer;
import com.stpl.tech.master.domain.model.Adapter5;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * Java class for ActionDetail complex type.
 *
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 *
 * <pre>
 * &lt;complexType name="ActionDetail"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="reason" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="approvedBy" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="generatedBy" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="actionTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ActionDetail", propOrder = { "reason", "approvedBy", "generatedBy", "actionTime" })
@Document
public class ActionDetail implements Serializable {
	/**
	 *
	 */
	private static final long serialVersionUID = -7782659644886973589L;

	@Id
	private String _id;

	/*
	 * @Version
	 *
	 * @JsonIgnore private Long version;
	 *
	 *//**
		 * Added to avoid a runtime error whereby the detachAll property is
		 * checked for existence but not actually used.
		 *//*
		 * private String detachAll;
		 */
	@XmlElement(required = true, nillable = true)
	@Field
	protected String reason;
	@XmlElement(required = true, nillable = true)
	@Field
	protected Integer reasonId;
	@XmlElement(required = true, nillable = true)
	@Field
	protected String bookedWastage;
	@XmlElement(required = true, type = Integer.class, nillable = true)
	@Field
	protected Integer approvedBy;
	@XmlElement(required = true, type = Integer.class, nillable = true)
	@Field
	protected Integer generatedBy;
	@XmlElement(required = true, type = String.class, nillable = true)
	@XmlJavaTypeAdapter(Adapter5.class)
	@XmlSchemaType(name = "date")
	@JsonDeserialize(using = CustomJsonDateDeserializer.class)
	@Field
	protected Date actionTime;

	public String get_id() {
		return _id;
	}

	public void set_id(String _id) {
		this._id = _id;
	}

	/*
	 * public Long getVersion() { return version; }
	 *
	 * public void setVersion(Long version) { this.version = version; }
	 *
	 * public String getDetachAll() { return detachAll; }
	 *
	 * public void setDetachAll(String detachAll) { this.detachAll = detachAll;
	 * }
	 */

	/**
	 * Gets the value of the reason property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getReason() {
		return reason;
	}

	/**
	 * Sets the value of the reason property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setReason(String value) {
		this.reason = value;
	}

	/**
	 * Gets the value of the approvedBy property.
	 *
	 * @return possible object is {@link Integer }
	 *
	 */
	public Integer getApprovedBy() {
		return approvedBy;
	}

	/**
	 * Sets the value of the approvedBy property.
	 *
	 * @param value
	 *            allowed object is {@link Integer }
	 *
	 */
	public void setApprovedBy(Integer value) {
		this.approvedBy = value;
	}

	/**
	 * Gets the value of the generatedBy property.
	 *
	 * @return possible object is {@link Integer }
	 *
	 */
	public Integer getGeneratedBy() {
		return generatedBy;
	}

	/**
	 * Sets the value of the generatedBy property.
	 *
	 * @param value
	 *            allowed object is {@link Integer }
	 *
	 */
	public void setGeneratedBy(Integer value) {
		this.generatedBy = value;
	}

	/**
	 * Gets the value of the actionTime property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public Date getActionTime() {
		return actionTime;
	}

	/**
	 * Sets the value of the actionTime property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setActionTime(Date value) {
		this.actionTime = value;
	}

	public Integer getReasonId() {
		return reasonId;
	}

	public void setReasonId(Integer reasonId) {
		this.reasonId = reasonId;
	}

	public String getBookedWastage() {
		return bookedWastage;
	}

	public void setBookedWastage(String bookedWastage) {
		this.bookedWastage = bookedWastage;
	}

}
