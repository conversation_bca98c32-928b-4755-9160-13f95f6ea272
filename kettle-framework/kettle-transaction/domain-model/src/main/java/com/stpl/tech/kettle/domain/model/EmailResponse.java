package com.stpl.tech.kettle.domain.model;

public enum EmailResponse {

    InvalidEmail("Invalid Email"),
    EmailVerified("Email Already Verified"),
    VerificationSent("Email Verification Sent"),
    EmailUpdated("Email Updated"),
    EmailNotUpdated("Email Not Updated"),
    EmailNotVerified("Email Not Verified"),
    EmailExists("Email Already Set"),
    DataUpdated("Data Updated");

    private final String value;

    EmailResponse(String str) {
        value = str;
    }

    public String value() {
        return value;
    }

    public static EmailResponse fromValue(String v) {
        for (EmailResponse c: EmailResponse.values()) {
            if (c.value.equals(v)) {
                return c;
            }
        }
        throw new IllegalArgumentException(v);
    }
}
