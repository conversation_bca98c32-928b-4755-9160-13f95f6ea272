/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.delivery.model;

import java.util.HashMap;
import java.util.Map;

public enum DeliveryStatus {

	REQUEST_DECLINED(-2), CREATE_DECLINED(-1), ACCEPTED(0), ASSIGNED(1), DEPARTED(2), ARRIVED(3),
	PICKEDUP(4), OUT_FOR_DELIVERY(20), DELIVERED(6), CANCELLED(9);

	private final int deliveryStatus;
	
	// Reverse-lookup map for getting a day from an abbreviation
    private static final Map<Integer, DeliveryStatus> lookup = new HashMap<Integer, DeliveryStatus>();

    static {
        for (DeliveryStatus status : DeliveryStatus.values()) {
            lookup.put(status.getDeliveryStatus(), status);
        }
    }

	private DeliveryStatus(int deliveryStatus) {
		this.deliveryStatus = deliveryStatus;
	}

	public int getDeliveryStatus() {
		return this.deliveryStatus;
	}

    public static DeliveryStatus get(Integer enumValue) {
        return lookup.get(enumValue);
    }
}
