package com.stpl.tech.kettle.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;
import java.util.Date;
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class OrderRefund {
    private Integer orderRefundDetailId;
    private Integer orderId;
    private Integer referenceRefundId;
    private BigDecimal refundAmount;
    private String refundStatus;
    private String refundType;
    private String refundReason;
    private Integer createdBy;
    private Date creationTime;
    private Integer customerId;
    private String otpVerifiedBy;
    private String otpVerifiedContact;
    private Integer unitId;
    private Date updationTime;
}
