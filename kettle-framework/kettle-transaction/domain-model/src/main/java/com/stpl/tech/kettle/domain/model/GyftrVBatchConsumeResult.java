package com.stpl.tech.kettle.domain.model;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class GyftrVBatchConsumeResult  implements Serializable{
	/**
	 * 
	 */
	private static final long serialVersionUID = -4771280432716030255L;
	String AuthorizationCode;
	String ErrorCode;
	String ErrorMsg;
	String FailedVoucherNumber;
	String Message;
	String ResultType;
	String LastConsumedShopCode;
	String LastConsumedDate;
	List<GyftrVoucherAction> VOUCHERACTION = new ArrayList<>();

	@Override
	public String toString() {
		return "GyftrVBatchConsumeResult [AuthorizationCode=" + AuthorizationCode + ", ErrorCode=" + ErrorCode
				+ ", ErrorMsg=" + ErrorMsg + ", FailedVoucherNumber=" + FailedVoucherNumber + ", Message=" + Message
				+ ", ResultType=" + ResultType + ", LastConsumedShopCode=" + LastConsumedShopCode
				+ ", LastConsumedDate=" + LastConsumedDate + ", VOUCHERACTION=" + VOUCHERACTION + "]";
	}

	public String getAuthorizationCode() {
		return AuthorizationCode;
	}

	public void setAuthorizationCode(String authorizationCode) {
		AuthorizationCode = authorizationCode;
	}

	public String getErrorCode() {
		return ErrorCode;
	}

	public void setErrorCode(String errorCode) {
		ErrorCode = errorCode;
	}

	public String getErrorMsg() {
		return ErrorMsg;
	}

	public void setErrorMsg(String errorMsg) {
		ErrorMsg = errorMsg;
	}

	public String getFailedVoucherNumber() {
		return FailedVoucherNumber;
	}

	public void setFailedVoucherNumber(String failedVoucherNumber) {
		FailedVoucherNumber = failedVoucherNumber;
	}

	public String getMessage() {
		return Message;
	}

	public void setMessage(String message) {
		Message = message;
	}

	public String getResultType() {
		return ResultType;
	}

	public void setResultType(String resultType) {
		ResultType = resultType;
	}

	public String getLastConsumedShopCode() {
		return LastConsumedShopCode;
	}

	public void setLastConsumedShopCode(String lastConsumedShopCode) {
		LastConsumedShopCode = lastConsumedShopCode;
	}

	public String getLastConsumedDate() {
		return LastConsumedDate;
	}

	public void setLastConsumedDate(String lastConsumedDate) {
		LastConsumedDate = lastConsumedDate;
	}

	public List<GyftrVoucherAction> getVOUCHERACTION() {
		return VOUCHERACTION;
	}

	public void setVOUCHERACTION(List<GyftrVoucherAction> vOUCHERACTION) {
		VOUCHERACTION = vOUCHERACTION;
	}

}
