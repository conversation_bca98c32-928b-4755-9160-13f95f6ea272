/**
 * 
 */
package com.stpl.tech.analytics.model;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlType;

/**
 * <AUTHOR>
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "SalesSplitData", propOrder = { "total", "dineIn", "delivery", "count" })
public class SalesSplitData implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 3861679151190090619L;
	private UnitSaleData total = new UnitSaleData();
	private UnitSaleData dineIn = new UnitSaleData();
	private UnitSaleData delivery = new UnitSaleData();
	private int count;

	public UnitSaleData getTotal() {
		return total;
	}

	public void setTotal(UnitSaleData total) {
		this.total = total;
	}

	public UnitSaleData getDineIn() {
		return dineIn;
	}

	public void setDineIn(UnitSaleData dineIn) {
		this.dineIn = dineIn;
	}

	public UnitSaleData getDelivery() {
		return delivery;
	}

	public void setDelivery(UnitSaleData delivery) {
		this.delivery = delivery;
	}

	public int getCount() {
		return count;
	}

	public void setCount(int count) {
		this.count = count;
	}

}
