package com.stpl.tech.kettle.domain.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class OrderPaymentDetailData implements Serializable {

    private static final long serialVersionUID = 8664642689328666331L;
    private String generatedOrderId;
    private Integer orderId;
    private String paymentSource;
    private String paymentModeName;
    private String requestStatus;
    private String paymentStatus;
    private Date requestTime;
    private String partnerTransactionId;
    private String contactNumber;
    private String customerName;
    private Integer customerId;
    private BigDecimal transactionAmount;
}
