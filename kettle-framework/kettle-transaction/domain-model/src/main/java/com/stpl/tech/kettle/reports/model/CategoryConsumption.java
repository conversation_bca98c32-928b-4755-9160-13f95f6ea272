/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.reports.model;

import java.util.Collection;
import java.util.Map;
import java.util.TreeMap;

public class CategoryConsumption<T> extends AbstractConsumption implements ConsumptionData {

	private Map<Integer, SubCategoryConsumption<T>> items = new TreeMap<Integer, SubCategoryConsumption<T>>();

	/**
	 * @param name
	 */
	public CategoryConsumption(String name) {
		super(name);
	}

	/**
	 * @return the items
	 */
	public Map<Integer, SubCategoryConsumption<T>> getItems() {
		return items;
	}

	/**
	 * @param items
	 *            the items to set
	 */
	public void setItems(Map<Integer, SubCategoryConsumption<T>> items) {
		this.items = items;
	}

	/**
	 * @return the items
	 */
	public Collection<SubCategoryConsumption<T>> getAllItems() {
		return items.values();
	}

}
