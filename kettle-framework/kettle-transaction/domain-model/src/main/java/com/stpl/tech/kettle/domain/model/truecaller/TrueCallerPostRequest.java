package com.stpl.tech.kettle.domain.model.truecaller;

/**
 * Copyright (C) $today.year,
 * Sunshine Teahouse Private Limited - All Rights Reserved
 * Unauthorized copying of this file, via any medium is strictly prohibited
 * Proprietary and confidential
 * Created by shikhar on 10-03-2018.
 */
public class TrueCallerPostRequest {

    private Integer unitId;
    private Integer terminal;
    private String contact;
    private String requestId;
    private TrueCallerRequestType type;

    public Integer getUnitId() {
        return unitId;
    }

    public void setUnitId(Integer unitId) {
        this.unitId = unitId;
    }

    public Integer getTerminal() {
        return terminal;
    }

    public void setTerminal(Integer terminal) {
        this.terminal = terminal;
    }

    public String getContact() {
        return contact;
    }

    public void setContact(String contact) {
        this.contact = contact;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public TrueCallerRequestType getType() {
        return type;
    }

    public void setType(TrueCallerRequestType type) {
        this.type = type;
    }
}
