/**
 *
 */
package com.stpl.tech.kettle.sales.domain.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.master.CustomJsonDateDeserializer;
import com.stpl.tech.master.domain.model.Adapter1;
import com.stpl.tech.master.domain.model.Adapter5;
import com.stpl.tech.util.domain.adapter.BigDecimalDeserializer;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 *
 */
@Document(collection = "CustomerData")
public class CustomerData {

	@Id
	private String _id;

	@Field
	// Load
	protected Integer customerId;
	@Field
	// Load
	protected String contactNumber;
	@Field
	// Load
	protected String customerName;
	@Field
	// Load
	protected String aquisitionSource;
	@Field
	// Load
	protected String aquisitionToken;
	@Field
	// Load
	protected String emailId;
	@Field
	// Load
	protected Boolean numberVerified;
	@Field
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter5.class)
	@XmlSchemaType(name = "date")
	@JsonDeserialize(using = CustomJsonDateDeserializer.class)
	// Load
	protected Date numberVerificationTime;
	@Field
	// Load
	protected Boolean emailVerified;
	@Field
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter5.class)
	@XmlSchemaType(name = "date")
	@JsonDeserialize(using = CustomJsonDateDeserializer.class)
	// Load
	protected Date emailVerificationTime;
	@Field
	// Load
	protected Integer totalPoints;
	@Field
	// Load
	protected Integer currentPoints;
	@Field
	// Load
	protected Integer redeemedPoints;
	@Field
	// Load
	protected Boolean availedSignupOffer;

	@Field
	// Load
	protected Integer registrationUnitId;
	@Field
	protected Integer lastUnitId;
	@Field
	protected Integer firstUnitId;
	@Field
	protected String lastUnitName;
	@Field
	protected String firstUnitName;
	@Field
	protected Integer lastOrderId;
	@Field
	protected Integer firstOrderId;
	@Field
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter5.class)
	@XmlSchemaType(name = "date")
	@JsonDeserialize(using = CustomJsonDateDeserializer.class)
	protected Date firstOrderDate;
	@Field
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter5.class)
	@XmlSchemaType(name = "date")
	@JsonDeserialize(using = CustomJsonDateDeserializer.class)
	protected Date lastOrderDate;
	@Field
	protected Integer daysGapSinceLastOrder;

	@Field
	protected Integer ticketCount;
	@Field
	protected Integer zeroAmountDineInTicketCount;
	@Field
	protected Integer zeroAmountDeliveryTicketCount;
	@Field
	protected Integer zeroAmountTicketCount;
	@Field

	protected Integer cancelledOrderCount;
	@Field
	protected Integer ticketWithOffer;
	@Field
	protected Integer ticketWithRedemption;
	@Field
	protected Integer dineInTicket;
	@Field
	protected Integer deliveryTicket;
	@Field
	protected Integer takeawayTicket;
	@Field
	protected Integer zomatoTicket;
	@Field
	protected Integer swiggyTicket;
	@Field
	protected Integer foodPandaTicket;
	@Field
	protected Integer uberEatsTicket;
	@Field
	protected Integer otherParnerTicket;
	@Field
	protected Integer webAppTicket;
	@Field
	protected Integer oldAppTicket;
	@Field
	protected Integer callCenterTicket;
	@Field
	protected Integer otherPartnerTicket;

	@Field
	protected Integer ticketWithFood;
	@Field
	protected Integer ticketWithVeg;
	@Field
	protected Integer ticketWithNonVeg;
	@Field
	protected Integer ticketWithHot;
	@Field
	protected Integer ticketWithCold;
	@Field
	protected Integer ticketWithBakery;
	@Field
	protected Integer ticketWithCombo;
	@Field
	protected Integer ticketWithMerchandise;
	@Field
	protected Integer ticketWithOthers;
	@Field
	protected Integer ticketWithGiftCard;
	@Field
	protected Integer ticketOnMonday;
	@Field
	protected Integer ticketOnTuesday;
	@Field
	protected Integer ticketOnWednesday;
	@Field
	protected Integer ticketOnThursday;
	@Field
	protected Integer ticketOnFriday;
	@Field
	protected Integer ticketOnSaturday;
	@Field
	protected Integer ticketOnSunday;
	@Field
	protected Integer ticketOnWeekday;
	@Field
	protected Integer ticketOnWeekend;

	@Field
	protected Integer ticketInBreakfast;
	@Field
	protected Integer ticketInLunch;
	@Field
	protected Integer ticketInEvening;
	@Field
	protected Integer ticketInDinner;
	@Field
	protected Integer ticketInPostDinner;
	@Field
	protected Integer ticketInNight;

	@Field
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal spend;
	@Field
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal discount;
	@Field
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal apc;
	@Field
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal minApc;
	@Field
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal maxApc;

	@Field
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal deliverySpend;
	@Field
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal deliveryDiscount;
	@Field
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal deliveryApc;
	@Field
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal minDeliveryApc;
	@Field
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal maxDeliveryApc;

	@Field
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal dineInSpend;
	@Field
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal dineInDiscount;
	@Field
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal dineInApc;
	@Field
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal minDineInApc;
	@Field
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal maxDineInApc;

	@Field
	protected Integer unitsVisited;
	@Field
	protected Integer peoplePerTicket;
	@Field
	protected Integer minPeoplePerOrder;
	@Field
	protected Integer maxPeoplePerOrder;

	@Field
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal cashSpend;
	@Field
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal cardSpend;
	@Field
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal amexSpend;
	@Field
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal paytmSpend;
	@Field
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal giftCardSpend;
	@Field
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal mobikwikSpend;
	@Field
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal onlineSpend;
	@Field
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal otherSpend;

	@Field
	protected Integer cashTicket;
	@Field
	protected Integer cardTicket;
	@Field
	protected Integer amexTicket;
	@Field
	protected Integer paytmTicket;
	@Field
	protected Integer giftCardTicket;
	@Field
	protected Integer mobikwikTicket;
	@Field
	protected Integer onlineTicket;
	@Field
	protected Integer otherPaymentTicket;
	@Field
	protected Integer splitPaymentTicket;

	@Field
	protected Integer oneNPSTicket;
	@Field
	protected Integer twoNPSTicket;
	@Field
	protected Integer threeNPSTicket;
	@Field
	protected Integer fourNPSTicket;
	@Field
	protected Integer fiveNPSTicket;
	@Field
	protected Integer sixNPSTicket;
	@Field
	protected Integer sevenNPSTicket;
	@Field
	protected Integer eightNPSTicket;
	@Field
	protected Integer nineNPSTicket;
	@Field
	protected Integer tenNPSTicket;
	@Field
	protected Integer lastNPSScore;
	@Field
	protected Integer negativeNPSTicket;
	@Field
	protected Integer positiveNPSTicket;
	@Field
	protected Integer neutralNPSTicket;

	@Field
	protected Integer oneFeedbackTicket;
	@Field
	protected Integer twoFeedbackTicket;
	@Field
	protected Integer threeFeedbackTicket;
	@Field
	protected Integer fourFeedbackTicket;
	@Field
	protected Integer fiveFeedbackTicket;
	@Field
	protected Integer lastFeedbackScore;


	@Field
	protected Integer ticketInNinetyDays;
	@Field
	protected Integer deliveryTicketInNinetyDays;
	@Field
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal spendInNinetyDays;
	@Field
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal deliverySpendInNinetyDays;

	@Field
	protected Integer lastPaymentMode;

	@Field
	protected Integer onlyGiftCardTicket;


	public String get_id() {
		return _id;
	}

	public void set_id(String _id) {
		this._id = _id;
	}

	public Date getNumberVerificationTime() {
		return numberVerificationTime;
	}

	public void setNumberVerificationTime(Date numberVerificationTime) {
		this.numberVerificationTime = numberVerificationTime;
	}

	public Date getEmailVerificationTime() {
		return emailVerificationTime;
	}

	public void setEmailVerificationTime(Date emailVerificationTime) {
		this.emailVerificationTime = emailVerificationTime;
	}

	public Integer getFirstUnitId() {
		return firstUnitId;
	}

	public void setFirstUnitId(Integer firstUnitId) {
		this.firstUnitId = firstUnitId;
	}

	public Integer getCustomerId() {
		return customerId;
	}

	public void setCustomerId(Integer customerId) {
		this.customerId = customerId;
	}

	public String getCustomerName() {
		return customerName;
	}

	public void setCustomerName(String customerName) {
		this.customerName = customerName;
	}

	public String getEmailId() {
		return emailId;
	}

	public void setEmailId(String emailId) {
		this.emailId = emailId;
	}

	public Boolean getNumberVerified() {
		return numberVerified;
	}

	public void setNumberVerified(Boolean numberVerified) {
		this.numberVerified = numberVerified;
	}

	public Boolean getEmailVerified() {
		return emailVerified;
	}

	public void setEmailVerified(Boolean emailVerified) {
		this.emailVerified = emailVerified;
	}

	public Integer getTotalPoints() {
		return totalPoints;
	}

	public void setTotalPoints(Integer totalPoints) {
		this.totalPoints = totalPoints;
	}

	public Integer getCurrentPoints() {
		return currentPoints;
	}

	public void setCurrentPoints(Integer currentPoints) {
		this.currentPoints = currentPoints;
	}

	public Integer getRedeemedPoints() {
		return redeemedPoints;
	}

	public void setRedeemedPoints(Integer redeemedPoints) {
		this.redeemedPoints = redeemedPoints;
	}

	public Boolean getAvailedSignupOffer() {
		return availedSignupOffer;
	}

	public void setAvailedSignupOffer(Boolean availedSignupOffer) {
		this.availedSignupOffer = availedSignupOffer;
	}

	public Integer getLastUnitId() {
		return lastUnitId;
	}

	public void setLastUnitId(Integer lastUnitId) {
		this.lastUnitId = lastUnitId;
	}

	public Integer getLastOrderId() {
		return lastOrderId;
	}

	public void setLastOrderId(Integer lastOrderId) {
		this.lastOrderId = lastOrderId;
	}

	public Integer getFirstOrderId() {
		return firstOrderId;
	}

	public void setFirstOrderId(Integer firstOrderId) {
		this.firstOrderId = firstOrderId;
	}

	public Date getFirstOrderDate() {
		return firstOrderDate;
	}

	public void setFirstOrderDate(Date firstOrderDate) {
		this.firstOrderDate = firstOrderDate;
	}

	public Date getLastOrderDate() {
		return lastOrderDate;
	}

	public void setLastOrderDate(Date lastOrderDate) {
		this.lastOrderDate = lastOrderDate;
	}

	public Integer getDaysGapSinceLastOrder() {
		return daysGapSinceLastOrder;
	}

	public void setDaysGapSinceLastOrder(Integer daysGapSinceLastOrder) {
		this.daysGapSinceLastOrder = daysGapSinceLastOrder;
	}

	public Integer getTicketCount() {
		return ticketCount;
	}

	public void setTicketCount(Integer ticketCount) {
		this.ticketCount = ticketCount;
	}

	public Integer getTicketWithOffer() {
		return ticketWithOffer;
	}

	public void setTicketWithOffer(Integer ticketWithOffer) {
		this.ticketWithOffer = ticketWithOffer;
	}

	public Integer getTicketWithRedemption() {
		return ticketWithRedemption;
	}

	public void setTicketWithRedemption(Integer ticketWithRedemption) {
		this.ticketWithRedemption = ticketWithRedemption;
	}

	public Integer getDineInTicket() {
		return dineInTicket;
	}

	public void setDineInTicket(Integer dineInTicket) {
		this.dineInTicket = dineInTicket;
	}

	public Integer getDeliveryTicket() {
		return deliveryTicket;
	}

	public void setDeliveryTicket(Integer deliveryTicket) {
		this.deliveryTicket = deliveryTicket;
	}

	public Integer getTakeawayTicket() {
		return takeawayTicket;
	}

	public void setTakeawayTicket(Integer takeawayTicket) {
		this.takeawayTicket = takeawayTicket;
	}

	public Integer getZomatoTicket() {
		return zomatoTicket;
	}

	public void setZomatoTicket(Integer zomatoTicket) {
		this.zomatoTicket = zomatoTicket;
	}

	public Integer getSwiggyTicket() {
		return swiggyTicket;
	}

	public void setSwiggyTicket(Integer swiggyTicket) {
		this.swiggyTicket = swiggyTicket;
	}

	public Integer getFoodPandaTicket() {
		return foodPandaTicket;
	}

	public void setFoodPandaTicket(Integer foodPandaTicket) {
		this.foodPandaTicket = foodPandaTicket;
	}

	public Integer getUberEatsTicket() {
		return uberEatsTicket;
	}

	public void setUberEatsTicket(Integer uberEatsTicket) {
		this.uberEatsTicket = uberEatsTicket;
	}

	public Integer getOtherParnerTicket() {
		return otherParnerTicket;
	}

	public void setOtherParnerTicket(Integer otherParnerTicket) {
		this.otherParnerTicket = otherParnerTicket;
	}

	public Integer getCallCenterTicket() {
		return callCenterTicket;
	}

	public void setCallCenterTicket(Integer callCenterTicket) {
		this.callCenterTicket = callCenterTicket;
	}

	public Integer getOtherPartnerTicket() {
		return otherPartnerTicket;
	}

	public void setOtherPartnerTicket(Integer otherPartnerTicket) {
		this.otherPartnerTicket = otherPartnerTicket;
	}

	public Integer getOnlyGiftCardTicket() {
		return onlyGiftCardTicket;
	}

	public void setOnlyGiftCardTicket(Integer onlyGiftCardTicket) {
		this.onlyGiftCardTicket = onlyGiftCardTicket;
	}

	public Integer getTicketWithFood() {
		return ticketWithFood;
	}

	public void setTicketWithFood(Integer ticketWithFood) {
		this.ticketWithFood = ticketWithFood;
	}

	public Integer getTicketWithVeg() {
		return ticketWithVeg;
	}

	public void setTicketWithVeg(Integer ticketWithVeg) {
		this.ticketWithVeg = ticketWithVeg;
	}

	public Integer getTicketWithNonVeg() {
		return ticketWithNonVeg;
	}

	public void setTicketWithNonVeg(Integer ticketWithNonVeg) {
		this.ticketWithNonVeg = ticketWithNonVeg;
	}

	public Integer getTicketWithHot() {
		return ticketWithHot;
	}

	public void setTicketWithHot(Integer ticketWithHot) {
		this.ticketWithHot = ticketWithHot;
	}

	public Integer getTicketWithCold() {
		return ticketWithCold;
	}

	public void setTicketWithCold(Integer ticketWithCold) {
		this.ticketWithCold = ticketWithCold;
	}

	public Integer getTicketWithBakery() {
		return ticketWithBakery;
	}

	public void setTicketWithBakery(Integer ticketWithBakery) {
		this.ticketWithBakery = ticketWithBakery;
	}

	public Integer getTicketWithCombo() {
		return ticketWithCombo;
	}

	public void setTicketWithCombo(Integer ticketWithCombo) {
		this.ticketWithCombo = ticketWithCombo;
	}

	public Integer getTicketWithMerchandise() {
		return ticketWithMerchandise;
	}

	public void setTicketWithMerchandise(Integer ticketWithMerchandise) {
		this.ticketWithMerchandise = ticketWithMerchandise;
	}

	public Integer getTicketWithOthers() {
		return ticketWithOthers;
	}

	public void setTicketWithOthers(Integer ticketWithOthers) {
		this.ticketWithOthers = ticketWithOthers;
	}

	public Integer getTicketWithGiftCard() {
		return ticketWithGiftCard;
	}

	public void setTicketWithGiftCard(Integer ticketWithGiftCard) {
		this.ticketWithGiftCard = ticketWithGiftCard;
	}

	public Integer getTicketOnMonday() {
		return ticketOnMonday;
	}

	public void setTicketOnMonday(Integer ticketOnMonday) {
		this.ticketOnMonday = ticketOnMonday;
	}

	public Integer getTicketOnTuesday() {
		return ticketOnTuesday;
	}

	public void setTicketOnTuesday(Integer ticketOnTuesday) {
		this.ticketOnTuesday = ticketOnTuesday;
	}

	public Integer getTicketOnWednesday() {
		return ticketOnWednesday;
	}

	public void setTicketOnWednesday(Integer ticketOnWednesday) {
		this.ticketOnWednesday = ticketOnWednesday;
	}

	public Integer getTicketOnThursday() {
		return ticketOnThursday;
	}

	public void setTicketOnThursday(Integer ticketOnThursday) {
		this.ticketOnThursday = ticketOnThursday;
	}

	public Integer getTicketOnFriday() {
		return ticketOnFriday;
	}

	public void setTicketOnFriday(Integer ticketOnFriday) {
		this.ticketOnFriday = ticketOnFriday;
	}

	public Integer getTicketOnSaturday() {
		return ticketOnSaturday;
	}

	public void setTicketOnSaturday(Integer ticketOnSaturday) {
		this.ticketOnSaturday = ticketOnSaturday;
	}

	public Integer getTicketOnSunday() {
		return ticketOnSunday;
	}

	public void setTicketOnSunday(Integer ticketOnSunday) {
		this.ticketOnSunday = ticketOnSunday;
	}

	public Integer getTicketOnWeekday() {
		return ticketOnWeekday;
	}

	public void setTicketOnWeekday(Integer ticketOnWeekday) {
		this.ticketOnWeekday = ticketOnWeekday;
	}

	public Integer getTicketOnWeekend() {
		return ticketOnWeekend;
	}

	public void setTicketOnWeekend(Integer ticketOnWeekend) {
		this.ticketOnWeekend = ticketOnWeekend;
	}

	public Integer getTicketInBreakfast() {
		return ticketInBreakfast;
	}

	public void setTicketInBreakfast(Integer ticketInBreakfast) {
		this.ticketInBreakfast = ticketInBreakfast;
	}

	public Integer getTicketInLunch() {
		return ticketInLunch;
	}

	public void setTicketInLunch(Integer ticketInLunch) {
		this.ticketInLunch = ticketInLunch;
	}

	public Integer getTicketInEvening() {
		return ticketInEvening;
	}

	public void setTicketInEvening(Integer ticketInEvening) {
		this.ticketInEvening = ticketInEvening;
	}

	public Integer getTicketInDinner() {
		return ticketInDinner;
	}

	public void setTicketInDinner(Integer ticketInDinner) {
		this.ticketInDinner = ticketInDinner;
	}

	public Integer getTicketInPostDinner() {
		return ticketInPostDinner;
	}

	public void setTicketInPostDinner(Integer ticketInPostDinner) {
		this.ticketInPostDinner = ticketInPostDinner;
	}

	public Integer getTicketInNight() {
		return ticketInNight;
	}

	public void setTicketInNight(Integer ticketInNight) {
		this.ticketInNight = ticketInNight;
	}

	public BigDecimal getSpend() {
		return spend;
	}

	public void setSpend(BigDecimal spend) {
		this.spend = spend;
	}

	public BigDecimal getDiscount() {
		return discount;
	}

	public void setDiscount(BigDecimal discount) {
		this.discount = discount;
	}

	public BigDecimal getApc() {
		return apc;
	}

	public void setApc(BigDecimal apc) {
		this.apc = apc;
	}

	public BigDecimal getMinApc() {
		return minApc;
	}

	public void setMinApc(BigDecimal minApc) {
		this.minApc = minApc;
	}

	public BigDecimal getMaxApc() {
		return maxApc;
	}

	public void setMaxApc(BigDecimal maxApc) {
		this.maxApc = maxApc;
	}

	public BigDecimal getDeliverySpend() {
		return deliverySpend;
	}

	public void setDeliverySpend(BigDecimal deliverySpend) {
		this.deliverySpend = deliverySpend;
	}

	public BigDecimal getDeliveryDiscount() {
		return deliveryDiscount;
	}

	public void setDeliveryDiscount(BigDecimal deliveryDiscount) {
		this.deliveryDiscount = deliveryDiscount;
	}

	public BigDecimal getDeliveryApc() {
		return deliveryApc;
	}

	public void setDeliveryApc(BigDecimal deliveryApc) {
		this.deliveryApc = deliveryApc;
	}

	public BigDecimal getMinDeliveryApc() {
		return minDeliveryApc;
	}

	public void setMinDeliveryApc(BigDecimal minDeliveryApc) {
		this.minDeliveryApc = minDeliveryApc;
	}

	public BigDecimal getMaxDeliveryApc() {
		return maxDeliveryApc;
	}

	public void setMaxDeliveryApc(BigDecimal maxDeliveryApc) {
		this.maxDeliveryApc = maxDeliveryApc;
	}

	public BigDecimal getDineInSpend() {
		return dineInSpend;
	}

	public void setDineInSpend(BigDecimal dineInSpend) {
		this.dineInSpend = dineInSpend;
	}

	public BigDecimal getDineInDiscount() {
		return dineInDiscount;
	}

	public void setDineInDiscount(BigDecimal dineInDiscount) {
		this.dineInDiscount = dineInDiscount;
	}

	public BigDecimal getDineInApc() {
		return dineInApc;
	}

	public void setDineInApc(BigDecimal dineInApc) {
		this.dineInApc = dineInApc;
	}

	public BigDecimal getMinDineInApc() {
		return minDineInApc;
	}

	public void setMinDineInApc(BigDecimal minDineInApc) {
		this.minDineInApc = minDineInApc;
	}

	public BigDecimal getMaxDineInApc() {
		return maxDineInApc;
	}

	public void setMaxDineInApc(BigDecimal maxDineInApc) {
		this.maxDineInApc = maxDineInApc;
	}

	public Integer getTicketInNinetyDays() {
		return ticketInNinetyDays;
	}

	public void setTicketInNinetyDays(Integer ticketInNinetyDays) {
		this.ticketInNinetyDays = ticketInNinetyDays;
	}

	public Integer getDeliveryTicketInNinetyDays() {
		return deliveryTicketInNinetyDays;
	}

	public void setDeliveryTicketInNinetyDays(Integer deliveryTicketInNinetyDays) {
		this.deliveryTicketInNinetyDays = deliveryTicketInNinetyDays;
	}

	public BigDecimal getSpendInNinetyDays() {
		return spendInNinetyDays;
	}

	public void setSpendInNinetyDays(BigDecimal spendInNinetyDays) {
		this.spendInNinetyDays = spendInNinetyDays;
	}

	public BigDecimal getDeliverySpendInNinetyDays() {
		return deliverySpendInNinetyDays;
	}

	public void setDeliverySpendInNinetyDays(BigDecimal deliverySpendInNinetyDays) {
		this.deliverySpendInNinetyDays = deliverySpendInNinetyDays;
	}

	public Integer getUnitsVisited() {
		return unitsVisited;
	}

	public void setUnitsVisited(Integer unitsVisited) {
		this.unitsVisited = unitsVisited;
	}

	public Integer getPeoplePerTicket() {
		return peoplePerTicket;
	}

	public void setPeoplePerTicket(Integer peoplePerTicket) {
		this.peoplePerTicket = peoplePerTicket;
	}

	public Integer getMinPeoplePerOrder() {
		return minPeoplePerOrder;
	}

	public void setMinPeoplePerOrder(Integer minPeoplePerOrder) {
		this.minPeoplePerOrder = minPeoplePerOrder;
	}

	public Integer getMaxPeoplePerOrder() {
		return maxPeoplePerOrder;
	}

	public void setMaxPeoplePerOrder(Integer maxPeoplePerOrder) {
		this.maxPeoplePerOrder = maxPeoplePerOrder;
	}

	public BigDecimal getCashSpend() {
		return cashSpend;
	}

	public void setCashSpend(BigDecimal cashSpend) {
		this.cashSpend = cashSpend;
	}

	public BigDecimal getCardSpend() {
		return cardSpend;
	}

	public void setCardSpend(BigDecimal cardSpend) {
		this.cardSpend = cardSpend;
	}

	public BigDecimal getAmexSpend() {
		return amexSpend;
	}

	public void setAmexSpend(BigDecimal amexSpend) {
		this.amexSpend = amexSpend;
	}

	public BigDecimal getPaytmSpend() {
		return paytmSpend;
	}

	public void setPaytmSpend(BigDecimal paytmSpend) {
		this.paytmSpend = paytmSpend;
	}

	public BigDecimal getGiftCardSpend() {
		return giftCardSpend;
	}

	public void setGiftCardSpend(BigDecimal giftCardSpend) {
		this.giftCardSpend = giftCardSpend;
	}

	public BigDecimal getMobikwikSpend() {
		return mobikwikSpend;
	}

	public void setMobikwikSpend(BigDecimal mobikwikSpend) {
		this.mobikwikSpend = mobikwikSpend;
	}

	public BigDecimal getOnlineSpend() {
		return onlineSpend;
	}

	public void setOnlineSpend(BigDecimal onlineSpend) {
		this.onlineSpend = onlineSpend;
	}

	public BigDecimal getOtherSpend() {
		return otherSpend;
	}

	public void setOtherSpend(BigDecimal otherSpend) {
		this.otherSpend = otherSpend;
	}

	public Integer getCashTicket() {
		return cashTicket;
	}

	public void setCashTicket(Integer cashTicket) {
		this.cashTicket = cashTicket;
	}

	public Integer getCardTicket() {
		return cardTicket;
	}

	public void setCardTicket(Integer cardTicket) {
		this.cardTicket = cardTicket;
	}

	public Integer getAmexTicket() {
		return amexTicket;
	}

	public void setAmexTicket(Integer amexTicket) {
		this.amexTicket = amexTicket;
	}

	public Integer getPaytmTicket() {
		return paytmTicket;
	}

	public void setPaytmTicket(Integer paytmTicket) {
		this.paytmTicket = paytmTicket;
	}

	public Integer getGiftCardTicket() {
		return giftCardTicket;
	}

	public void setGiftCardTicket(Integer giftCardTicket) {
		this.giftCardTicket = giftCardTicket;
	}

	public Integer getMobikwikTicket() {
		return mobikwikTicket;
	}

	public void setMobikwikTicket(Integer mobikwikTicket) {
		this.mobikwikTicket = mobikwikTicket;
	}

	public Integer getOnlineTicket() {
		return onlineTicket;
	}

	public void setOnlineTicket(Integer onlineTicket) {
		this.onlineTicket = onlineTicket;
	}

	public Integer getOtherPaymentTicket() {
		return otherPaymentTicket;
	}

	public void setOtherPaymentTicket(Integer otherPaymentTicket) {
		this.otherPaymentTicket = otherPaymentTicket;
	}

	public Integer getSplitPaymentTicket() {
		return splitPaymentTicket;
	}

	public void setSplitPaymentTicket(Integer splitPaymentTicket) {
		this.splitPaymentTicket = splitPaymentTicket;
	}

	public Integer getLastPaymentMode() {
		return lastPaymentMode;
	}

	public void setLastPaymentMode(Integer lastPaymentMode) {
		this.lastPaymentMode = lastPaymentMode;
	}

	public Integer getOneNPSTicket() {
		return oneNPSTicket;
	}

	public void setOneNPSTicket(Integer oneNPSTicket) {
		this.oneNPSTicket = oneNPSTicket;
	}

	public Integer getTwoNPSTicket() {
		return twoNPSTicket;
	}

	public void setTwoNPSTicket(Integer twoNPSTicket) {
		this.twoNPSTicket = twoNPSTicket;
	}

	public Integer getThreeNPSTicket() {
		return threeNPSTicket;
	}

	public void setThreeNPSTicket(Integer threeNPSTicket) {
		this.threeNPSTicket = threeNPSTicket;
	}

	public Integer getFourNPSTicket() {
		return fourNPSTicket;
	}

	public void setFourNPSTicket(Integer fourNPSTicket) {
		this.fourNPSTicket = fourNPSTicket;
	}

	public Integer getFiveNPSTicket() {
		return fiveNPSTicket;
	}

	public void setFiveNPSTicket(Integer fiveNPSTicket) {
		this.fiveNPSTicket = fiveNPSTicket;
	}

	public Integer getSixNPSTicket() {
		return sixNPSTicket;
	}

	public void setSixNPSTicket(Integer sixNPSTicket) {
		this.sixNPSTicket = sixNPSTicket;
	}

	public Integer getSevenNPSTicket() {
		return sevenNPSTicket;
	}

	public void setSevenNPSTicket(Integer sevenNPSTicket) {
		this.sevenNPSTicket = sevenNPSTicket;
	}

	public Integer getEightNPSTicket() {
		return eightNPSTicket;
	}

	public void setEightNPSTicket(Integer eightNPSTicket) {
		this.eightNPSTicket = eightNPSTicket;
	}

	public Integer getNineNPSTicket() {
		return nineNPSTicket;
	}

	public void setNineNPSTicket(Integer nineNPSTicket) {
		this.nineNPSTicket = nineNPSTicket;
	}

	public Integer getTenNPSTicket() {
		return tenNPSTicket;
	}

	public void setTenNPSTicket(Integer tenNPSTicket) {
		this.tenNPSTicket = tenNPSTicket;
	}

	public Integer getLastNPSScore() {
		return lastNPSScore;
	}

	public void setLastNPSScore(Integer lastNPSScore) {
		this.lastNPSScore = lastNPSScore;
	}

	public Integer getNegativeNPSTicket() {
		return negativeNPSTicket;
	}

	public void setNegativeNPSTicket(Integer negativeNPSTicket) {
		this.negativeNPSTicket = negativeNPSTicket;
	}

	public Integer getPositiveNPSTicket() {
		return positiveNPSTicket;
	}

	public void setPositiveNPSTicket(Integer positiveNPSTicket) {
		this.positiveNPSTicket = positiveNPSTicket;
	}

	public Integer getNeutralNPSTicket() {
		return neutralNPSTicket;
	}

	public void setNeutralNPSTicket(Integer neutralNPSTicket) {
		this.neutralNPSTicket = neutralNPSTicket;
	}

	public Integer getOneFeedbackTicket() {
		return oneFeedbackTicket;
	}

	public void setOneFeedbackTicket(Integer oneFeedbackTicket) {
		this.oneFeedbackTicket = oneFeedbackTicket;
	}

	public Integer getTwoFeedbackTicket() {
		return twoFeedbackTicket;
	}

	public void setTwoFeedbackTicket(Integer twoFeedbackTicket) {
		this.twoFeedbackTicket = twoFeedbackTicket;
	}

	public Integer getThreeFeedbackTicket() {
		return threeFeedbackTicket;
	}

	public void setThreeFeedbackTicket(Integer threeFeedbackTicket) {
		this.threeFeedbackTicket = threeFeedbackTicket;
	}

	public Integer getFourFeedbackTicket() {
		return fourFeedbackTicket;
	}

	public void setFourFeedbackTicket(Integer fourFeedbackTicket) {
		this.fourFeedbackTicket = fourFeedbackTicket;
	}

	public Integer getFiveFeedbackTicket() {
		return fiveFeedbackTicket;
	}

	public void setFiveFeedbackTicket(Integer fiveFeedbackTicket) {
		this.fiveFeedbackTicket = fiveFeedbackTicket;
	}

	public Integer getLastFeedbackScore() {
		return lastFeedbackScore;
	}

	public void setLastFeedbackScore(Integer lastFeedbackScore) {
		this.lastFeedbackScore = lastFeedbackScore;
	}

	public String getContactNumber() {
		return contactNumber;
	}

	public void setContactNumber(String contactNumber) {
		this.contactNumber = contactNumber;
	}

	public String getAquisitionSource() {
		return aquisitionSource;
	}

	public void setAquisitionSource(String aquisitionSource) {
		this.aquisitionSource = aquisitionSource;
	}

	public String getAquisitionToken() {
		return aquisitionToken;
	}

	public void setAquisitionToken(String aquisitionTime) {
		this.aquisitionToken = aquisitionTime;
	}

	public Integer getRegistrationUnitId() {
		return registrationUnitId;
	}

	public void setRegistrationUnitId(Integer registrationUnitId) {
		this.registrationUnitId = registrationUnitId;
	}

	public Integer getZeroAmountDineInTicketCount() {
		return zeroAmountDineInTicketCount;
	}

	public void setZeroAmountDineInTicketCount(Integer zeroAmountDineInTicketCount) {
		this.zeroAmountDineInTicketCount = zeroAmountDineInTicketCount;
	}

	public Integer getZeroAmountDeliveryTicketCount() {
		return zeroAmountDeliveryTicketCount;
	}

	public void setZeroAmountDeliveryTicketCount(Integer zeroAmountDeliveryTicketCount) {
		this.zeroAmountDeliveryTicketCount = zeroAmountDeliveryTicketCount;
	}

	public Integer getZeroAmountTicketCount() {
		return zeroAmountTicketCount;
	}

	public void setZeroAmountTicketCount(Integer zeroAmountTicketCount) {
		this.zeroAmountTicketCount = zeroAmountTicketCount;
	}

	public Integer getCancelledOrderCount() {
		return cancelledOrderCount;
	}

	public void setCancelledOrderCount(Integer cancelledOrderCount) {
		this.cancelledOrderCount = cancelledOrderCount;
	}

	public Integer getWebAppTicket() {
		return webAppTicket;
	}

	public void setWebAppTicket(Integer webAppTicket) {
		this.webAppTicket = webAppTicket;
	}

	public Integer getOldAppTicket() {
		return oldAppTicket;
	}

	public void setOldAppTicket(Integer oldAppTicket) {
		this.oldAppTicket = oldAppTicket;
	}

	public String getLastUnitName() {
		return lastUnitName;
	}

	public void setLastUnitName(String lastUnitName) {
		this.lastUnitName = lastUnitName;
	}

	public String getFirstUnitName() {
		return firstUnitName;
	}

	public void setFirstUnitName(String firstUnitName) {
		this.firstUnitName = firstUnitName;
	}

}
