/**
 * 
 */
package com.stpl.tech.kettle.core.data.budget.vo;

import java.util.Date;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.master.CustomJsonDateDeserializer;
import com.stpl.tech.master.domain.model.Adapter2;
import com.stpl.tech.master.domain.model.Adapter5;

/**
 * <AUTHOR>
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
public class BudgetKey {

	private Integer dayClosureId;
	private Integer sumoClosureId;
	private String calculation;
	private int unitId;
	private String unitName;
	private int year;
	private int month;
	private int day;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter5.class)
	@XmlSchemaType(name = "date")
	@JsonDeserialize(using = CustomJsonDateDeserializer.class)
	private Date businessDate;

	public Integer getDayClosureId() {
		return dayClosureId;
	}

	public void setDayClosureId(Integer dayClosureId) {
		this.dayClosureId = dayClosureId;
	}

	public Integer getSumoClosureId() {
		return sumoClosureId;
	}

	public void setSumoClosureId(Integer sumoClosureId) {
		this.sumoClosureId = sumoClosureId;
	}

	public String getCalculation() {
		return calculation;
	}

	public void setCalculation(String calculation) {
		this.calculation = calculation;
	}

	public int getUnitId() {
		return unitId;
	}

	public void setUnitId(int unitId) {
		this.unitId = unitId;
	}

	public String getUnitName() {
		return unitName;
	}

	public void setUnitName(String unitName) {
		this.unitName = unitName;
	}

	public int getYear() {
		return year;
	}

	public void setYear(int year) {
		this.year = year;
	}

	public int getMonth() {
		return month;
	}

	public void setMonth(int month) {
		this.month = month;
	}

	public int getDay() {
		return day;
	}

	public void setDay(int day) {
		this.day = day;
	}

	public Date getBusinessDate() {
		return businessDate;
	}

	public void setBusinessDate(Date businessDate) {
		this.businessDate = businessDate;
	}

}
