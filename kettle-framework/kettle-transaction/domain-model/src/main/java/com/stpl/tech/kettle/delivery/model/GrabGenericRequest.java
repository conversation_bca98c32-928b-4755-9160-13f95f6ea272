/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

package com.stpl.tech.kettle.delivery.model;

import com.stpl.tech.kettle.delivery.model.GrabRequest;

import javax.xml.bind.annotation.XmlElement;

public class GrabGenericRequest implements GrabRequest{

    @XmlElement(required = true)
    protected String clientId;
    @XmlElement(required = false)
    protected String orderId;
    @XmlElement(required = true)
    protected String clientOrderId;
    @XmlElement(required = true)
    protected String dttm;


    /**
     * Gets the value of the clientId property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getClientId() {
        return clientId;
    }

    /**
     * Sets the value of the clientId property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setClientId(String value) {
        this.clientId = value;
    }

    @Override
    public void setMerchantId(String merchantId) {}

    /**
     * Gets the value of the orderId property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getOrderId() {
        return orderId;
    }

    /**
     * Sets the value of the orderId property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setOrderId(String value) {
        this.orderId = value;
    }

    /**
     * Gets the value of the clientOrderId property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getClientOrderId() {
        return clientOrderId;
    }

    /**
     * Sets the value of the clientOrderId property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setClientOrderId(String value) {
        this.clientOrderId = value;
    }

    /**
     * Gets the value of the dttm property.
     *
     * @return
     *     possible object is
     *     {@link String }
     *
     */
    public String getDttm() {
        return dttm;
    }

    /**
     * Sets the value of the dttm property.
     *
     * @param value
     *     allowed object is
     *     {@link String }
     *
     */
    public void setDttm(String value) {
        this.dttm = value;
    }



}
