package com.stpl.tech.kettle.referral.model;

public class ReferralRequest {

	private String name;
	private String contact;
	private Integer referrerId;
	private String signUpRefCode;
	private String otp;
	private String campaign;
	private String source;
	private String token;

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getContact() {
		return contact;
	}

	public void setContact(String contact) {
		this.contact = contact;
	}

	public Integer getReferrerId() {
		return referrerId;
	}

	public void setReferrerId(Integer referrerId) {
		this.referrerId = referrerId;
	}

	public String getSignUpRefCode() {
		return signUpRefCode;
	}

	public String getOtp() {
		return otp;
	}

	public void setOtp(String otp) {
		this.otp = otp;
	}

	public String getCampaign() {
		return campaign;
	}

	public void setCampaign(String campaign) {
		this.campaign = campaign;
	}

	public String getSource() {
		return source;
	}

	public void setSource(String source) {
		this.source = source;
	}

	public void setSignUpRefCode(String signUpRefCode) {
		this.signUpRefCode = signUpRefCode;
	}

	public String getToken() {
		return token;
	}

	public void setToken(String token) {
		this.token = token;
	}

	@Override
	public String toString() {
		return "name: " + name + " contact : " + contact + " referrerId: " + referrerId + " code:" + signUpRefCode;
	}
}
