package com.stpl.tech.kettle.domain.model;

import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@Setter
@Builder
public class PotentialSavingData implements Serializable {
    private Integer totalOrderCount;
    private Integer currentOrderCount;
    private BigDecimal totalNetSales;
    private BigDecimal currentNetSales;
    private BigDecimal totalGmvSales;
    private BigDecimal currentGmvSale;
}
