/**
 * 
 */
package com.stpl.tech.kettle.domain.model.webengage.survey;

import java.io.Serializable;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * <AUTHOR>
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "WebEngageCustomerData", propOrder = { "orderId", "unitName", "scope", "mobileNumber" })
public class WebEngageCustomerData implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 4045331286821271493L;

	@XmlElement(name = "Order_id", required = true)
	@JsonProperty("Order_id")
	private String[] orderId;
	@XmlElement(name = "Unit_name", required = true)
	@JsonProperty("Unit_name")
	private String[] unitName;

	private String[] scope;
	@XmlElement(name = "MobileNumber", required = true)
	@JsonProperty("MobileNumber")
	private String[] mobileNumber;

	public String[] getOrderId() {
		return orderId;
	}

	public void setOrderId(String[] Order_id) {
		this.orderId = Order_id;
	}

	public String[] getUnitName() {
		return unitName;
	}

	public void setUnitName(String[] Unit_name) {
		this.unitName = Unit_name;
	}

	public String[] getScope() {
		return scope;
	}

	public void setScope(String[] scope) {
		this.scope = scope;
	}

	public String[] getMobileNumber() {
		return mobileNumber;
	}

	public void setMobileNumber(String[] MobileNumber) {
		this.mobileNumber = MobileNumber;
	}

	@Override
	public String toString() {
		return "ClassPojo [Order_id = " + orderId + ", Unit_name = " + unitName + ", scope = " + scope
				+ ", MobileNumber = " + mobileNumber + "]";
	}
}
