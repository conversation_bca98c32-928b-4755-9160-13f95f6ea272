package com.stpl.tech.kettle.domain.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class OrderSummaryOHC {

	private String POSDate;
	private String fileExportDateTime;
	private Integer fileId;
	private String storeId;
	private Integer transCount;
	private BigDecimal grossSaleAmt;
	private BigDecimal totalNetTaxAmt;
	private BigDecimal totalNetSaleAmt;
	private BigDecimal edc1Ammt = BigDecimal.ZERO;
	private BigDecimal edc2Ammt = BigDecimal.ZERO;
	private BigDecimal edc3Ammt = BigDecimal.ZERO;
	private BigDecimal cashAmt;
	private BigDecimal miscPayModes;
	private BigDecimal membershipRedeemed = BigDecimal.ZERO;
	private List<OrderOHC> bills = new ArrayList<>();
	private Memberships memberships;

	class Memberships {

	}

	public String getPOSDate() {
		return POSDate;
	}

	public void setPOSDate(String pOSDate) {
		POSDate = pOSDate;
	}

	public String getFileExportDateTime() {
		return fileExportDateTime;
	}

	public void setFileExportDateTime(String fileExportDateTime) {
		this.fileExportDateTime = fileExportDateTime;
	}

	public Integer getFileId() {
		return fileId;
	}

	public void setFileId(Integer fileId) {
		this.fileId = fileId;
	}

	public String getStoreId() {
		return storeId;
	}

	public void setStoreId(String storeId) {
		this.storeId = storeId;
	}

	public Integer getTransCount() {
		return transCount;
	}

	public void setTransCount(Integer transCount) {
		this.transCount = transCount;
	}

	public BigDecimal getGrossSaleAmt() {
		return grossSaleAmt;
	}

	public void setGrossSaleAmt(BigDecimal grossSaleAmt) {
		this.grossSaleAmt = grossSaleAmt;
	}

	public BigDecimal getTotalNetTaxAmt() {
		return totalNetTaxAmt;
	}

	public void setTotalNetTaxAmt(BigDecimal totalNetTaxAmt) {
		this.totalNetTaxAmt = totalNetTaxAmt;
	}

	public BigDecimal getTotalNetSaleAmt() {
		return totalNetSaleAmt;
	}

	public void setTotalNetSaleAmt(BigDecimal totalNetSaleAmt) {
		this.totalNetSaleAmt = totalNetSaleAmt;
	}

	public BigDecimal getEdc1Ammt() {
		return edc1Ammt;
	}

	public void setEdc1Ammt(BigDecimal edc1Ammt) {
		this.edc1Ammt = edc1Ammt;
	}

	public BigDecimal getEdc2Ammt() {
		return edc2Ammt;
	}

	public void setEdc2Ammt(BigDecimal edc2Ammt) {
		this.edc2Ammt = edc2Ammt;
	}

	public BigDecimal getEdc3Ammt() {
		return edc3Ammt;
	}

	public void setEdc3Ammt(BigDecimal edc3Ammt) {
		this.edc3Ammt = edc3Ammt;
	}

	public BigDecimal getCashAmt() {
		return cashAmt;
	}

	public void setCashAmt(BigDecimal cashAmt) {
		this.cashAmt = cashAmt;
	}

	public BigDecimal getMiscPayModes() {
		return miscPayModes;
	}

	public void setMiscPayModes(BigDecimal miscPayModes) {
		this.miscPayModes = miscPayModes;
	}

	public BigDecimal getMembershipRedeemed() {
		return membershipRedeemed;
	}

	public void setMembershipRedeemed(BigDecimal membershipRedeemed) {
		this.membershipRedeemed = membershipRedeemed;
	}

	public List<OrderOHC> getBills() {
		return bills;
	}

	public void setBills(List<OrderOHC> bills) {
		this.bills = bills;
	}

	public Memberships getMemberships() {
		return memberships;
	}

	public void setMemberships(Memberships memberships) {
		this.memberships = memberships;
	}

}
