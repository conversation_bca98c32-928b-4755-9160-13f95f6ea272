//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2018.01.29 at 12:29:36 PM IST 
//


package com.stpl.tech.kettle.domain.model;

import java.util.Date;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.stpl.tech.master.domain.model.Adapter2;
import com.stpl.tech.master.domain.model.IdCodeName;


/**
 * <p>Java class for anonymous complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="unitId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="billType" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="meterNo" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="lastReading" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="currentReading" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="calculationIndex" type="{http://www.w3schools.com}CalculationIndexStatus"/&gt;
 *         &lt;element name="createdBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="status" type="{http://www.w3schools.com}ExpenseStatus"/&gt;
 *         &lt;element name="createdOn" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="cancelledBy" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="cancelledOn" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="cancel" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="entryType" type="{http://www.w3schools.com}MeterDetailEntryType"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "id",
    "unitId",
    "billType",
    "meterNo",
    "lastReading",
    "currentReading",
    "calculationIndex",
    "createdBy",
    "status",
    "createdOn",
    "cancelledBy",
    "cancelledOn",
    "cancel",
    "entryType"
})
@XmlRootElement(name = "MeterDetail")
public class MeterDetail {

    protected int id;
    protected int unitId;
    @XmlElement(required = true)
    protected String billType;
    protected int meterNo;
    protected int lastReading;
    protected int currentReading;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected CalculationIndexStatus calculationIndex;
    @XmlElement(required = true)
    protected IdCodeName createdBy;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected ExpenseStatus status;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter2 .class)
    @XmlSchemaType(name = "date")
    protected Date createdOn;
    @XmlElement(required = true)
    protected IdCodeName cancelledBy;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter2 .class)
    @XmlSchemaType(name = "date")
    protected Date cancelledOn;
    protected boolean cancel;
    @XmlElement(required = true)
    @XmlSchemaType(name = "string")
    protected MeterDetailEntryType entryType;
    @XmlElement(required = true)
    protected IdCodeName updatedBy;
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter2 .class)
    @XmlSchemaType(name = "date")
    protected Date updatedOn;

    /**
     * Gets the value of the id property.
     * 
     */
    public int getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     * 
     */
    public void setId(int value) {
        this.id = value;
    }

    /**
     * Gets the value of the unitId property.
     * 
     */
    public int getUnitId() {
        return unitId;
    }

    /**
     * Sets the value of the unitId property.
     * 
     */
    public void setUnitId(int value) {
        this.unitId = value;
    }

    /**
     * Gets the value of the billType property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getBillType() {
        return billType;
    }

    /**
     * Sets the value of the billType property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setBillType(String value) {
        this.billType = value;
    }

    /**
     * Gets the value of the meterNo property.
     * 
     */
    public int getMeterNo() {
        return meterNo;
    }

    /**
     * Sets the value of the meterNo property.
     * 
     */
    public void setMeterNo(int value) {
        this.meterNo = value;
    }

    /**
     * Gets the value of the lastReading property.
     * 
     */
    public int getLastReading() {
        return lastReading;
    }

    /**
     * Sets the value of the lastReading property.
     * 
     */
    public void setLastReading(int value) {
        this.lastReading = value;
    }

    /**
     * Gets the value of the currentReading property.
     * 
     */
    public int getCurrentReading() {
        return currentReading;
    }

    /**
     * Sets the value of the currentReading property.
     * 
     */
    public void setCurrentReading(int value) {
        this.currentReading = value;
    }

    /**
     * Gets the value of the calculationIndex property.
     * 
     * @return
     *     possible object is
     *     {@link CalculationIndexStatus }
     *     
     */
    public CalculationIndexStatus getCalculationIndex() {
        return calculationIndex;
    }

    /**
     * Sets the value of the calculationIndex property.
     * 
     * @param value
     *     allowed object is
     *     {@link CalculationIndexStatus }
     *     
     */
    public void setCalculationIndex(CalculationIndexStatus value) {
        this.calculationIndex = value;
    }

    /**
     * Gets the value of the createdBy property.
     * 
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *     
     */
    public IdCodeName getCreatedBy() {
        return createdBy;
    }

    /**
     * Sets the value of the createdBy property.
     * 
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *     
     */
    public void setCreatedBy(IdCodeName value) {
        this.createdBy = value;
    }

    /**
     * Gets the value of the status property.
     * 
     * @return
     *     possible object is
     *     {@link ExpenseStatus }
     *     
     */
    public ExpenseStatus getStatus() {
        return status;
    }

    /**
     * Sets the value of the status property.
     * 
     * @param value
     *     allowed object is
     *     {@link ExpenseStatus }
     *     
     */
    public void setStatus(ExpenseStatus value) {
        this.status = value;
    }

    /**
     * Gets the value of the createdOn property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getCreatedOn() {
        return createdOn;
    }

    /**
     * Sets the value of the createdOn property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCreatedOn(Date value) {
        this.createdOn = value;
    }

    /**
     * Gets the value of the cancelledBy property.
     * 
     * @return
     *     possible object is
     *     {@link IdCodeName }
     *     
     */
    public IdCodeName getCancelledBy() {
        return cancelledBy;
    }

    /**
     * Sets the value of the cancelledBy property.
     * 
     * @param value
     *     allowed object is
     *     {@link IdCodeName }
     *     
     */
    public void setCancelledBy(IdCodeName value) {
        this.cancelledBy = value;
    }

    /**
     * Gets the value of the cancelledOn property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public Date getCancelledOn() {
        return cancelledOn;
    }

    /**
     * Sets the value of the cancelledOn property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setCancelledOn(Date value) {
        this.cancelledOn = value;
    }

    /**
     * Gets the value of the cancel property.
     * 
     */
    public boolean isCancel() {
        return cancel;
    }

    /**
     * Sets the value of the cancel property.
     * 
     */
    public void setCancel(boolean value) {
        this.cancel = value;
    }

    /**
     * Gets the value of the entryType property.
     * 
     * @return
     *     possible object is
     *     {@link MeterDetailEntryType }
     *     
     */
    public MeterDetailEntryType getEntryType() {
        return entryType;
    }

    /**
     * Sets the value of the entryType property.
     * 
     * @param value
     *     allowed object is
     *     {@link MeterDetailEntryType }
     *     
     */
    public void setEntryType(MeterDetailEntryType value) {
        this.entryType = value;
    }

	public IdCodeName getUpdatedBy() {
		return updatedBy;
	}

	public void setUpdatedBy(IdCodeName updatedBy) {
		this.updatedBy = updatedBy;
	}

	public Date getUpdatedOn() {
		return updatedOn;
	}

	public void setUpdatedOn(Date updatedOn) {
		this.updatedOn = updatedOn;
	}

}
