//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.05.10 at 07:11:28 PM IST
//

package com.stpl.tech.analytics.model;

import com.stpl.tech.kettle.domain.model.OrderItem;
import com.stpl.tech.kettle.domain.model.OrderStatus;
import com.stpl.tech.master.domain.model.Adapter2;
import com.stpl.tech.master.domain.model.ProductBasicDetail;
import com.stpl.tech.master.domain.model.UnitCategory;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * Java class for ProductReportData complex type.
 *
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 *
 * <pre>
 * &lt;complexType name="ProductReportData"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="productId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="dimension" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="businessDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="data" type="{http://www.w3schools.com}AggregateQuantityData"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ProductReportData", propOrder = { "productId", "dimension", "businessDate", "data" })
@Document
public class ProductReportData implements Serializable, Comparable<ProductReportData> {

	/**
	 *
	 */
	private static final long serialVersionUID = 122168373134417234L;
	@Id
	private String _id;
	/*
	 * @Version
	 *
	 * @JsonIgnore private Long version;
	 *
	 *//**
		 * Added to avoid a runtime error whereby the detachAll property is
		 * checked for existence but not actually used.
		 *//*
		 * private String detachAll;
		 */

	@Field
	protected int id;
	@XmlElement(required = true)
	@Field
	protected String dimension;
	@XmlElement(required = true)
	@Field
	protected ProductBasicDetail detail;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter2.class)
	@XmlSchemaType(name = "date")
	@Field
	protected Date businessDate;
	@XmlElement(required = true)
	@Field
	protected AggregateQuantityData data = new AggregateQuantityData();

	/**
	 * @return the _id
	 */
	public String get_id() {
		return _id;
	}

	/**
	 * @param _id
	 *            the _id to set
	 */
	public void set_id(String _id) {
		this._id = _id;
	}

	/**
	 * @return the version
	 */
	/*
	 * public Long getVersion() { return version; }
	 *
	 *//**
		 * @param version
		 *            the version to set
		 */
	/*
	 * public void setVersion(Long version) { this.version = version; }
	 *
	 *//**
		 * @return the detachAll
		 */
	/*
	 * public String getDetachAll() { return detachAll; }
	 *
	 *//**
		 * @param detachAll
		 *            the detachAll to set
		 *//*
		 * public void setDetachAll(String detachAll) { this.detachAll =
		 * detachAll; }
		 */

	/**
	 * Gets the value of the productId property.
	 *
	 */
	public int getId() {
		return id;
	}

	/**
	 * Sets the value of the productId property.
	 *
	 */
	public void setId(int value) {
		this.id = value;
	}

	/**
	 * Gets the value of the dimension property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getDimension() {
		return dimension;
	}

	/**
	 * Sets the value of the dimension property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setDimension(String value) {
		this.dimension = value;
	}

	/**
	 * Gets the value of the businessDate property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public Date getBusinessDate() {
		return businessDate;
	}

	/**
	 * Sets the value of the businessDate property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setBusinessDate(Date value) {
		this.businessDate = value;
	}

	/**
	 * Gets the value of the data property.
	 *
	 * @return possible object is {@link AggregateQuantityData }
	 *
	 */
	public AggregateQuantityData getData() {
		return data;
	}

	public ProductBasicDetail getDetail() {
		return detail;
	}

	public void setDetail(ProductBasicDetail detail) {
		this.detail = detail;
	}

	/**
	 * Sets the value of the data property.
	 *
	 * @param value
	 *            allowed object is {@link AggregateQuantityData }
	 *
	 */
	public void setData(AggregateQuantityData value) {
		this.data = value;
	}

	public ProductReportData addOrderItem(UnitCategory category, OrderStatus status, OrderItem order) {
		this.data.addOrderItem(category, status, order);
		return this;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + id;
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		UnitReportData other = (UnitReportData) obj;
		if (id != other.id)
			return false;
		return true;
	}

	@Override
	public int compareTo(ProductReportData o) {
		return Integer.compare(this.id, o.id);
	}

}
