package com.stpl.tech.kettle.domain.model;

import java.math.BigDecimal;

import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.stpl.tech.master.domain.model.Adapter1;
import com.stpl.tech.master.domain.model.Consumable;

public class OrderItemConsumable {

	protected int menuProductId;
	@XmlElement(required = true)
	protected String menuProductName;
	protected int menuProductQuanity;
	@XmlElement(required = true, nillable = true)
	protected String menuProductDimension;
	protected int scmProductId;
	@XmlElement(required = true)
	protected String scmProductName;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal scmProductQuantity;
	@XmlElement(required = true)
	protected String scmProductUom;

	public OrderItemConsumable() {
		super();
	}

	public OrderItemConsumable(OrderItem item, Consumable consumables) {
		super();

		this.menuProductId = item.getProductId();
		this.menuProductName = item.getProductName();
		this.menuProductDimension = item.getDimension();
		this.menuProductQuanity = item.getQuantity();

		this.scmProductId = consumables.getProductId();
		this.scmProductName = consumables.getName();
		this.scmProductQuantity = consumables.getQuantity();
		this.scmProductUom = consumables.getUom();
	}

	public int getMenuProductId() {
		return menuProductId;
	}

	public void setMenuProductId(int menuProductId) {
		this.menuProductId = menuProductId;
	}

	public String getMenuProductName() {
		return menuProductName;
	}

	public void setMenuProductName(String menuProductName) {
		this.menuProductName = menuProductName;
	}

	public int getMenuProductQuanity() {
		return menuProductQuanity;
	}

	public void setMenuProductQuanity(int menuProductQuanity) {
		this.menuProductQuanity = menuProductQuanity;
	}

	public String getMenuProductDimension() {
		return menuProductDimension;
	}

	public void setMenuProductDimension(String menuProductDimension) {
		this.menuProductDimension = menuProductDimension;
	}

	public int getScmProductId() {
		return scmProductId;
	}

	public void setScmProductId(int scmProductId) {
		this.scmProductId = scmProductId;
	}

	public String getScmProductName() {
		return scmProductName;
	}

	public void setScmProductName(String scmProductName) {
		this.scmProductName = scmProductName;
	}

	public BigDecimal getScmProductQuantity() {
		return scmProductQuantity;
	}

	public void setScmProductQuantity(BigDecimal scmProductQuantity) {
		this.scmProductQuantity = scmProductQuantity;
	}

	public String getScmProductUom() {
		return scmProductUom;
	}

	public void setScmProductUom(String scmProductUom) {
		this.scmProductUom = scmProductUom;
	}

}
