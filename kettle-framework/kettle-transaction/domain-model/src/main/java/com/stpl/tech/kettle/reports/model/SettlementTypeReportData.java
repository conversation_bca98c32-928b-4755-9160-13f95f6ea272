/**
 * 
 */
package com.stpl.tech.kettle.reports.model;

import java.util.Collection;
import java.util.Map;

import com.stpl.tech.master.tax.model.Taxation;

/**
 * <AUTHOR>
 *
 */
public class SettlementTypeReportData {

	private Collection<Taxation> taxations;
	private Map<Integer, Map<Integer, SettlementReport>> map;

	public SettlementTypeReportData() {

	}

	/**
	 * @param taxations
	 * @param map
	 */
	public SettlementTypeReportData(Collection<Taxation> taxations, Map<Integer, Map<Integer, SettlementReport>> map) {
		super();
		this.taxations = taxations;
		this.map = map;
	}

	public Map<Integer, Map<Integer, SettlementReport>> getMap() {
		return map;
	}

	public void setMap(Map<Integer, Map<Integer, SettlementReport>> map) {
		this.map = map;
	}

	public Collection<Taxation> getTaxations() {
		return taxations;
	}

	public void setTaxations(Collection<Taxation> taxations) {
		this.taxations = taxations;
	}

}
