/**
 * 
 */
package com.stpl.tech.kettle.core.data.budget.vo;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 *
 */
@Retention(value = RetentionPolicy.RUNTIME)
@Target({ ElementType.FIELD })
public @interface ExpenseField {

	enum ExpenseFieldSource {
		KEY_SOURCE, KETTLE, SUMO, FILE_UPLOAD;
	}

	enum ExpenseFieldEntity {
		KEY_ENTITY, PROFIT_DATA, SALES_DATA, INVENTORY, WASTAGE, GOODS_RECEIVED, ELECTRICITY_MODULE, EXPENSE_MODULE,
		UPLOAD, VARIANCE;
	}

	enum ExpenseFieldType {
		DECIMAL, NUMBERIC, PERCENTAGE, YES_NO, STRING;
	}

	enum ExpenseFieldGroup {
		<PERSON><PERSON><PERSON>_GRO<PERSON>, REVENU<PERSON>, TICKET, DIRECT_COST, MARKETING, OTHER_VARIABLE, PRE_OPENING, FIXED_COST, CALCULATION,
		ALLOCATION, VARIANCE, VARIABLE
	}

	enum ScoreCardCategory {
		KEY_FIELDS, PROFIT, SALES, EMPLOYEE_MEAL_COGS, COGS, COGS_TAX, EMPLOYEE_MEAL, EMPLOYEE_MEAL_TAX, TRAINING_COGS,
		EXPIRY_WASTAGE, EXPIRY_WASTAGE_TAX, CONSUMABLE, CONSUMABLE_TAX, FIXED_ASSETS, MARKETING_LOCAL_STORE, LOGISTICS,
		FACILITIES_VARIABLE, FACILITIES_FIXED, ANY_OTHER_VARIABLE, ANY_OTHER_VARIABLE_NON_COTROLABLES,
		ANY_OTHER_VARIABLE_COTROLABLES, DELIVERY_CHARGES, MAINTENANCE, MANPOWER, MANPOWER_INCENTIVE, MANPOWER_FIXED,
		FIXED_EXPENSES, MARKETING_CORPORATE, STOCK_VARIANCE, STOCK_VARIANCE_TAX, ELECTRICITY,
		COMMISSION_CARD_OR_WALLETS, CANCELLATION_CHARGES_CHANNEL_PARTNERS, COMMISSION_CHANNEL_PARTNERS, FACILITIES_PROPERTY, SUPPORT_OPS_MANAGEMENT,
		SUPPORT_HQ, SUPPORT_COMM_WH, TECHNOLOGY, PRE_OPENING, FINANCIAL_EXPENSES, FICO_PAYOUTS, CAPEX_AND_FIXED_ASSETS,
		INTEREST_INCOME;
	}

	enum CalculationType {
		EARNING, COST, NO_TYPE
	}

	enum CalculationMode {
		DIRECT, CALCULATED, NOT_CALCULATED
	}

	enum ExpenseRecordCategory {
		NONE, VEHICLE_REGULAR_MAINTENANCE_CAFE, COMMISSION_CHANGE_CAFE,
		CLEANING_CHARGES, COGS_OTHERS, CONVEYANCE_MARKETING,
		CONVEYANCE_OPERATIONS,CONVEYANCE_OTHERS,
		COURIER_CHARGES, ENERGY_DG_RUNNING_CAFE,
		MARKETING_DATA_ANALYSIS, MARKETING_NPI_CAFE, NEWSPAPER_CHARGES_CAFE,
		PARKING_CHARGES_CAFE, PHOTO_COPY_EXPENSES, RND_ENGINEERING_EXPENSE,
		BUILDING_MAINTENANCE_CAFE, COMPUTER_IT_MAINTENANCE_CAFE, EQUIPMENT_MAINTENANCE_CAFE,
		FUEL_CHARGES_CAFE, STAFF_WELFARE_EXPENSES,
        PRINTING_AND_STATIONARY, TRAVELLING_EXPENSE_ODC, TRAVELLING_EXPENSES,
		BUSINESS_PROMOTION, MAINTENANCE_PEST_CONTROL,
		WATER_CHARGES_CAFE,  
		DIGITAL_MARKETING_EXPENSE,
		ADVERTISEMENT_ONLINE, ADVERTISEMENT_OFFLINE, MARKETING_OUTDOOR, 
		ADVERTISEMENT_PNS, ADVERTISEMENT_AGENCY_FEES,MAINTENANCE_PEST_CONTROL_CAFE,CONVEYANCE_ODC;
	}

	enum DiscounRecordCategory {
		LOYAL_TEA, MARKETING, BUSINESS_DEVELOPMENT, OPERATIONS, EMPLOYEE_FICO, NONE, EMP_DISCOUNT_LOYAL_TEA,EMP_DISCOUNT_MARKETING, EMP_DISCOUNT_OPS,
		EMP_DISCOUNT_BD,EMP_DISCOUNT_EMPLOYEE_FICO

		}
	
	enum ServiceRecordCategory{
		SECURITY_GUARD_CHARGES, FUEL_CHARGES, LOGISTIC_CHARGES, COMMUNICATION_INTERNET, COMMUNICATION_TELEPHONE, COMMUNICATION_ILL,
		PAYROLL_PROCESSING_FEES, NEWSPAPER_CHARGES,
		STAFF_WELFARE_EXPENSES, COURIER_CHARGES, PRINTING_AND_STATIONARY, BUSINESS_PROMOTION,
		LEGAL_CHARGES, PROFESSIONAL_CHARGES, CLEANING_CHARGES, PEST_CONTROL_CHARGES,
		TECHNOLOGY_TRAINING, CORPORATE_MARKETING_DIGITAL, CORPORATE_MARKETING_AD_OFFLINE, CORPORATE_MARKETING_AD_ONLINE,CORPORATE_MARKETING_CHANNEL_PARTNER,
		CORPORATE_MARKETING_OUTDOOR, CORPORATE_MARKETING_AGENCY_FEES,
		DELIVERY_CHARGES_VARIABLE, CONVEYANCE_MARKETING, CONVEYANCE_OPERATION, CONVEYANCE_OTHERS,
		AUDIT_FEE, AUDIT_FEE_OUT_OF_POCKET, BROKERAGE, CHARITY_AND_DONATIONS, DOMESTIC_TICKETS_AND_HOTELS,
		INTERNATIONAL_TICKETS_AND_HOTELS, HOUSEKEEPING_CHARGES, LATE_FEE_CHARGES, MARKETING_DATA_ANALYSIS,
		MISCELLANEOUS_EXPENSES, PENALTY, PHOTO_COPY_EXPENSES, QCR_EXPENSE, RECRUITMENT_CONSULTANTS, ROC_FEES,
		DEBIT_CREDIT_WRITTEN_OFF, DIFFENECE_IN_EXCHANGE, RND_ENGINEERING_EXPENSE, CAPITAL_IMPROVEMENT_EXPENSES,
		LEASE_HOLD_IMPROVEMENTS, MARKETING_LAUNCH, CORPORATE_MARKETING_PHOTO, ODC_RENTAL, COGS_OTHERS, COMMISSION_CHANGE,

		VEHICLE_REGULAR_MAINTENANCE_HQ, BUILDING_MAINTENANCE_HQ, COMPUTER_IT_MAINTENANCE_HQ,
		EQUIPMENT_MAINTENANCE_HQ, MARKETING_NPI_HQ, LICENSE_EXPENSES, CORPORATE_MARKETING_ATL_RADIO,
		CORPORATE_MARKETING_ATL_TV, CORPORATE_MARKETING_ATL_PRINT_AD, CORPORATE_MARKETING_ATL_CINEMA,
		CORPORATE_MARKETING_ATL_DIGITAL, LOGISTIC_INTRASTATE_COLD_VEHICLE, LOGISTIC_INTRASTATE_NON_COLD_VEHICLE,
		LOGISTIC_INTERSTATE_AIR, LOGISTIC_INTERSTATE_ROAD, LOGISTIC_INTERSTATE_TRAIN, AIR_CONDITIONER_AMC, NONE,

		SECURITY_DEPOSIT_PROPERTY,SECURITY_DEPOSIT_MVAT,SECURITY_DEPOSIT_ELECTRICITY,MARKETING_DISCOUNT_ECOM,SHIPPING_CHARGES,
		OTHER_TRANSACTION_CHARGES,DISCOUNT_DEALER_MARGIN,PERFORMANCE_MARKETING_SERVICE,INSURANCE_MARINE,SHARE_STAMPING_CHARGES,
		OTHER_CHARGES_ECOM, COMISSION_CHANNEL_PARTNER_FIXED,COGS_TRADING_GOODS,ROYALTY_FEES,FREIGHT_CHARGES,CORPORATE_MARKETING_SMS_EMAIL,
		PRONTO_AMC ,OTHERS_MAINTENANCE,TECHNOLOGY_PLATFORM_CHARGES,
		INTEREST_ON_TERM_LOAN,TECHNOLOGY_OTHERS,SYSTEM_RENTAL,RO_RENTAL,INSURANCE_ACCIDENTAL,DG_RENTAL,
		OTHERS_AMC ,MUSIC_RENTALS,VOUCHER_TRANSACTION_CHARGES,INSURANCE_ASSETS,INSURANCE_CGL,INSURANCE_MEDICAL,PROPERTY_FIX_RENT,
		PETTY_CASH_RENTALS,ENERGY_DG_RUNNING_CAFE,EDC_RENTAL,EMPLOYEE_FACILITATION_CHARGES;

		}


	enum RestrictionType {
		WARNING, ERROR, NOTHING;
	}

	enum ExpenseLabel {
		EMPTY(""), 

		UNIT_ID("Unit Id"),
		UNIT_NAME("Unit Name"), 
		BUSINESS_DATE("Business Date"), 

		GROSS_SALE("A. Gross Sales Value"), 
		DISCOUNT("B. Discounts"), 
		NET_REVENUE("C. Net Revenue"),
		NET_GC_BALANCE("D. Net GC balance"), 
		NET_SALES("E. Net Sales"),
		DINE_IN_NET_SALE("F. Dine In Net Sales"),
		DELIVERY_NET_SALE("G. Delivery Net Sales"),
		TOTAL_TRANSACTIONS("H. Total transactions"),

		APC("I. Avg Transaction Value (APC)"),
		
		COGS("J1.1 COGS"), 
		COGS_TAX("J1.2 COGS TAX"),
		
		EMPLOYEE_MEAL("J2.1 Employee Meal"), 
		EMPLOYEE_MEAL_TAX("J2.2 Employee Meal Tax"),
	
		EXPIRY_WASTAGE("J3.1 Expiry_Wastage"),
		EXPIRY_WASTAGE_TAX("J3.2. Wastage Tax"), 
	
		CONSUMABLE("J4.1. Consumable"),
		CONSUMABLE_TAX("J4.2. Consumable Tax"), 
		
		STOCK_VARIANCE("J5.1. Stock Variance"),
		STOCK_VARIANCE_TAX("J5.2. Stock Variance Tax"),
	
		LOGISTICS("J6. Logistics"),
		GROSS_PROFIT("K. Gross Profit (K=C-J)"),
		GROSS_PROFIT_PERCENT("L. Gross Profit Percentage (L=K/C*100)"),
		
		//ELECTRICITY("M1. Electricity"),
		FACILITIES_VARIABLE("M1. Facilities Variable"),
		//AOV_NON_CONTROLLABLE("M3. Any Other Variable Non Controllables"),
		ANY_OTHER_VARIABLE("M2. Any Other Variable"), 
		COMMISSION_CARD_WALLET("M3. Commission Card/Wallet"),
		COMMISSION_CHANNEL_PARTNER("M4. Commission Channel Partner"),
		//AOV_CONTROLLABLE("M4. Any Other Variable Controllables"),
		DELIVERY_CHARGES("M5. Delivery Charges"),
		MAINTENANCE("M6. Maintenance"),
		MANPOWER_INCENTIVE("M7. Manpower Incentive"),
		MANPOWER_FIXED("M8. Manpower Fixed"),
		//FIXED_EXPENSE("M8. Fixed Expenses"), 
		MARKETING_LS("M9. Marketing-LS"), 
		MARKETING_CORP("M10. Marketing-Corp"),	
		FIXED_ASSETS("M11. Fixed Assets"),
		FACILITIES_PROPERTY("M12. Facilities Property"),
		SUPPORT_OPS_MANAGEMENT("M13. Support OPS Management"), 
		SUPPORT_HQ("M14. Support HQ"), 
		SUPPORT_COMM_WH("M15. Support Comm/WH"),
		//MISCELLANEOUS("M16. Miscellaneous"),
		CAPEX_AND_FIXED_ASSETS("M16. Capex And Fixed Assets"),
		PRE_OPENING("M17. Pre Opening"),
		FINANCIAL_EXPENSES("M18. Financial Expenses"),
		INTEREST_INCOME("M19. Interest Income"),
		TECHNOLOGY("M20. Technology"), 
		FACILITIES_FIXED("M21. Facilities Fixed"),
		
		TOTAL_COST("N. Total Cost (N=J+M)"),
		EBITDA("O. EBIDTA = (C-N)"),
		EBITDA_PERCENT("P. EBIDTA % = (EBITDA/NET REVENUE)*100");


		private final String label;
		private final boolean split;

		private ExpenseLabel(String label, boolean split) {
			this.label = label;
			this.split = split;
		}

		private ExpenseLabel(String label) {
			this.label = label;
			this.split = false;
		}

		public String getLabel() {
			return label;
		}

		public boolean isSplit() {
			return split;
		}

	}

	ExpenseFieldGroup group() default ExpenseFieldGroup.KEY_GROUP;

	ScoreCardCategory category();

	String detail();

	int order() default 0;

	RestrictionType budgetType() default RestrictionType.NOTHING;

	ExpenseLabel orderLabel() default ExpenseLabel.EMPTY;

	ExpenseFieldType type() default ExpenseFieldType.STRING;

	ExpenseRecordCategory expense() default ExpenseRecordCategory.NONE;

	ExpenseFieldSource source() default ExpenseFieldSource.FILE_UPLOAD;

	ExpenseFieldEntity entity() default ExpenseFieldEntity.KEY_ENTITY;

	CalculationType calculationType();

	CalculationMode calculationMode() default CalculationMode.NOT_CALCULATED;

	ServiceRecordCategory service() default ServiceRecordCategory.NONE;

}