package com.stpl.tech.kettle.domain.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.master.CustomJsonDateDeserializer;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class WalletEventData {
    private Integer customerId;

    private Integer orderId;

    @JsonDeserialize(using = CustomJsonDateDeserializer.class)
    private Date billingServerTime;

    private Integer partnerId;

    private Integer brandId;

    private SuggestedWallet suggestedWallet;




}
