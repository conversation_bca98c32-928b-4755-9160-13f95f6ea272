/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.04.06 at 01:49:42 PM IST 
//


package com.stpl.tech.kettle.domain.model;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlType;


/**
 * <p>Java class for AssemblyLog complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="AssemblyLog"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="assemblyLogId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="billingServerTime" type="{http://www.w3.org/2001/XMLSchema}long"/&gt;
 *         &lt;element name="unitId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="orderSource" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="channelPartner" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="deliveryPartner" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="timeToAcknowledge" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="timeToProcessHot" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="timeToProcessCold" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="timeToProcessFood" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="timeToProcessByWorkstations" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="timeToReadyForDispatch" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="timeToCancel" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="cooktopStation" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="stationEventsForOrder" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "AssemblyLog", propOrder = {
    "assemblyLogId",
    "billingServerTime",
    "unitId",
    "orderSource",
    "channelPartner",
    "deliveryPartner",
    "timeToAcknowledge",
    "timeToProcessHot",
    "timeToProcessCold",
    "timeToProcessFood",
    "timeToProcessByWorkstations",
    "timeToReadyForDispatch",
    "timeToCancel",
    "cooktopStation",
    "stationEventsForOrder"
})
public class AssemblyLog {

    protected int assemblyLogId;
    protected int orderId;
    protected long billingServerTime;
    protected int unitId;
    @XmlElement(required = true)
    protected String orderSource;
    @XmlElement(required = true)
    protected String channelPartner;
    @XmlElement(required = true)
    protected String deliveryPartner;
    protected int timeToAcknowledge;
    protected int timeToProcessHot;
    protected int timeToProcessCold;
    protected int timeToProcessFood;
    protected int timeToProcessByWorkstations;
    protected int timeToReadyForDispatch;
    protected int timeToDispatch;
    protected int timeToCancel;
    protected int cooktopStation;
    protected int stationEventsForOrder;
    protected int timeToDeliver;

    /**
     * Gets the value of the assemblyLogId property.
     * 
     */
    public int getAssemblyLogId() {
        return assemblyLogId;
    }

    /**
     * Sets the value of the assemblyLogId property.
     * 
     */
    public void setAssemblyLogId(int value) {
        this.assemblyLogId = value;
    }

    
    public int getOrderId() {
		return orderId;
	}

	public void setOrderId(int orderId) {
		this.orderId = orderId;
	}

	/**
     * Gets the value of the billingServerTime property.
     * 
     */
    public long getBillingServerTime() {
        return billingServerTime;
    }

    /**
     * Sets the value of the billingServerTime property.
     * 
     */
    public void setBillingServerTime(long value) {
        this.billingServerTime = value;
    }

    /**
     * Gets the value of the unitId property.
     * 
     */
    public int getUnitId() {
        return unitId;
    }

    /**
     * Sets the value of the unitId property.
     * 
     */
    public void setUnitId(int value) {
        this.unitId = value;
    }

    /**
     * Gets the value of the orderSource property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getOrderSource() {
        return orderSource;
    }

    /**
     * Sets the value of the orderSource property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setOrderSource(String value) {
        this.orderSource = value;
    }

    /**
     * Gets the value of the channelPartner property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getChannelPartner() {
        return channelPartner;
    }

    /**
     * Sets the value of the channelPartner property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setChannelPartner(String value) {
        this.channelPartner = value;
    }

    /**
     * Gets the value of the deliveryPartner property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getDeliveryPartner() {
        return deliveryPartner;
    }

    /**
     * Sets the value of the deliveryPartner property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setDeliveryPartner(String value) {
        this.deliveryPartner = value;
    }

    /**
     * Gets the value of the timeToAcknowledge property.
     * 
     */
    public int getTimeToAcknowledge() {
        return timeToAcknowledge;
    }

    /**
     * Sets the value of the timeToAcknowledge property.
     * 
     */
    public void setTimeToAcknowledge(int value) {
        this.timeToAcknowledge = value;
    }

    /**
     * Gets the value of the timeToProcessHot property.
     * 
     */
    public int getTimeToProcessHot() {
        return timeToProcessHot;
    }

    /**
     * Sets the value of the timeToProcessHot property.
     * 
     */
    public void setTimeToProcessHot(int value) {
        this.timeToProcessHot = value;
    }

    /**
     * Gets the value of the timeToProcessCold property.
     * 
     */
    public int getTimeToProcessCold() {
        return timeToProcessCold;
    }

    /**
     * Sets the value of the timeToProcessCold property.
     * 
     */
    public void setTimeToProcessCold(int value) {
        this.timeToProcessCold = value;
    }

    /**
     * Gets the value of the timeToProcessFood property.
     * 
     */
    public int getTimeToProcessFood() {
        return timeToProcessFood;
    }

    /**
     * Sets the value of the timeToProcessFood property.
     * 
     */
    public void setTimeToProcessFood(int value) {
        this.timeToProcessFood = value;
    }

    /**
     * Gets the value of the timeToProcessByWorkstations property.
     * 
     */
    public int getTimeToProcessByWorkstations() {
        return timeToProcessByWorkstations;
    }

    /**
     * Sets the value of the timeToProcessByWorkstations property.
     * 
     */
    public void setTimeToProcessByWorkstations(int value) {
        this.timeToProcessByWorkstations = value;
    }

    /**
     * Gets the value of the timeToReadyForDispatch property.
     * 
     */
    public int getTimeToReadyForDispatch() {
        return timeToReadyForDispatch;
    }

    /**
     * Sets the value of the timeToReadyForDispatch property.
     * 
     */
    public void setTimeToReadyForDispatch(int value) {
        this.timeToReadyForDispatch = value;
    }

    /**
     * Gets the value of the timeToCancel property.
     * 
     */
    public int getTimeToDispatch() {
        return timeToDispatch;
    }

    /**
     * Sets the value of the timeToCancel property.
     * 
     */
    public void setTimeToDispatch(int value) {
        this.timeToDispatch = value;
    }

    /**
     * Gets the value of the cooktopStation property.
     * 
     */
    public int getCooktopStation() {
        return cooktopStation;
    }

    /**
     * Sets the value of the cooktopStation property.
     * 
     */
    public void setCooktopStation(int value) {
        this.cooktopStation = value;
    }

    /**
     * Gets the value of the stationEventsForOrder property.
     * 
     */
    public int getStationEventsForOrder() {
        return stationEventsForOrder;
    }

    /**
     * Sets the value of the stationEventsForOrder property.
     * 
     */
    public void setStationEventsForOrder(int value) {
        this.stationEventsForOrder = value;
    }

	public int getTimeToCancel() {
		return timeToCancel;
	}

	public void setTimeToCancel(int timeToCancel) {
		this.timeToCancel = timeToCancel;
	}

	public int getTimeToDeliver() {
		return timeToDeliver;
	}

	public void setTimeToDeliver(int timeToDeliver) {
		this.timeToDeliver = timeToDeliver;
	}

}
