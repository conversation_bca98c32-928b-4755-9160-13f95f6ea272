//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.08.22 at 07:47:15 PM IST 
//


package com.stpl.tech.kettle.domain.model.external;
	
import java.util.ArrayList;
import java.util.List;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;

import com.fasterxml.jackson.annotation.JsonProperty;


/**
 * <p>Java class for FormResponse complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="FormResponse"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="form_id" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="token" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="submitted_at" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="hidden" type="{http://www.w3schools.com}HiddenAttributes"/&gt;
 *         &lt;element name="definition" type="{http://www.w3schools.com}FormDefinition"/&gt;
 *         &lt;element name="answers" type="{http://www.w3schools.com}FormResponseData" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "FormResponse", propOrder = {
    "formId",
    "token",
    "submittedAt",
    "hidden",
    "definition",
    "answers"
})
public class FormResponse {
	
	@JsonProperty("form_id")
    @XmlElement(name = "form_id", required = true)
    protected String formId;
    @XmlElement(required = true)
    protected String token;
    @XmlElement(name = "submitted_at", required = true)
    @XmlSchemaType(name = "date")
    @JsonProperty("submitted_at")
    protected XMLGregorianCalendar submittedAt;
    @XmlElement(name = "submitted_at", required = true)
    @XmlSchemaType(name = "date")
    @JsonProperty("landed_at")
    protected XMLGregorianCalendar landedAt;
    @XmlElement(required = true)
    protected HiddenAttributes hidden;
    @XmlElement(required = true)
    protected FormDefinition definition;
    protected List<FormResponseData> answers;

    /**
     * Gets the value of the formId property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getFormId() {
        return formId;
    }

    /**
     * Sets the value of the formId property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setFormId(String value) {
        this.formId = value;
    }

    /**
     * Gets the value of the token property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getToken() {
        return token;
    }

    /**
     * Sets the value of the token property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setToken(String value) {
        this.token = value;
    }

    /**
     * Gets the value of the submittedAt property.
     * 
     * @return
     *     possible object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public XMLGregorianCalendar getSubmittedAt() {
        return submittedAt;
    }

    /**
     * Sets the value of the submittedAt property.
     * 
     * @param value
     *     allowed object is
     *     {@link XMLGregorianCalendar }
     *     
     */
    public void setSubmittedAt(XMLGregorianCalendar value) {
        this.submittedAt = value;
    }

    /**
     * Gets the value of the hidden property.
     * 
     * @return
     *     possible object is
     *     {@link HiddenAttributes }
     *     
     */
    public HiddenAttributes getHidden() {
        return hidden;
    }

    /**
     * Sets the value of the hidden property.
     * 
     * @param value
     *     allowed object is
     *     {@link HiddenAttributes }
     *     
     */
    public void setHidden(HiddenAttributes value) {
        this.hidden = value;
    }

    /**
     * Gets the value of the definition property.
     * 
     * @return
     *     possible object is
     *     {@link FormDefinition }
     *     
     */
    public FormDefinition getDefinition() {
        return definition;
    }

    /**
     * Sets the value of the definition property.
     * 
     * @param value
     *     allowed object is
     *     {@link FormDefinition }
     *     
     */
    public void setDefinition(FormDefinition value) {
        this.definition = value;
    }

    /**
     * Gets the value of the answers property.
     * 
     * <p>
     * This accessor method returns a reference to the live list,
     * not a snapshot. Therefore any modification you make to the
     * returned list will be present inside the JAXB object.
     * This is why there is not a <CODE>set</CODE> method for the answers property.
     * 
     * <p>
     * For example, to add a new item, do as follows:
     * <pre>
     *    getAnswers().add(newItem);
     * </pre>
     * 
     * 
     * <p>
     * Objects of the following type(s) are allowed in the list
     * {@link FormResponseData }
     * 
     * 
     */
    public List<FormResponseData> getAnswers() {
        if (answers == null) {
            answers = new ArrayList<FormResponseData>();
        }
        return this.answers;
    }

	public XMLGregorianCalendar getLandedAt() {
		return landedAt;
	}

	public void setLandedAt(XMLGregorianCalendar landedAt) {
		this.landedAt = landedAt;
	}
    
    

}
