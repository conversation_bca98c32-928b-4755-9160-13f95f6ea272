/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2015.12.19 at 12:10:26 PM IST
//

package com.stpl.tech.kettle.domain.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.kettle.domain.model.truecaller.TrueCallerVerifiedProfile;
import com.stpl.tech.master.domain.model.Adapter1;
import com.stpl.tech.master.domain.model.Adapter2;
import com.stpl.tech.master.domain.model.Address;
import com.stpl.tech.util.domain.adapter.BigDecimalDeserializer;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * Java class for anonymous complex type.
 * <p>
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 *
 * <pre>
 * &lt;complexType&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="firstName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="middleName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="lastName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="countryCode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="contactNumber" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="emailId" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="emailVerified" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="loyaltyPoints" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="registrationUnitId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="acquisitionSource" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="acquisitionToken" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="contactNumberVerified" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="availedSignupOffer" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="lastOrderId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="orderCount" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="lastOrderTime" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="smsSubscriber" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="emailSubscriber" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="loyaltySubscriber" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="addresses" type="{http://www.w3schools.com}Address" maxOccurs="unbounded" minOccurs="0"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "", propOrder = {
    "id",
    "firstName",
    "middleName",
    "lastName",
    "countryCode",
    "contactNumber",
    "emailId",
    "emailVerified",
    "loyaltyPoints",
    "registrationUnitId",
    "acquisitionSource",
    "acquisitionToken",
    "contactNumberVerified",
    "availedSignupOffer",
    "lastOrderId",
    "orderCount",
    "lastOrderTime",
    "smsSubscriber",
    "emailSubscriber",
    "loyaltySubscriber",
    "addresses"
})
@XmlRootElement(name = "Customer")
@Document
public class Customer implements Serializable {

    /**
     *
     */
    private static final long serialVersionUID = -5975124835916759966L;
    @Id
    private String _id;

	/*@Version
	@JsonIgnore
	private Long version;

	*/
    /**
     * Added to avoid a runtime error whereby the detachAll property is checked
     * for existence but not actually used.
     *//*
	private String detachAll;
	*/
    @Field
    protected int id;
    @XmlElement(required = true)
    @Field
    protected String firstName;
    @XmlElement(required = true)
    @Field
    protected String middleName;
    @XmlElement(required = true)
    @Field
    protected String lastName;
    @XmlElement(required = true)
    @Field
    protected String countryCode;
    @XmlElement(required = true)
    @Field
    protected String contactNumber;
    @XmlElement(required = true)
    @Field
    protected String emailId;
    @Field
    protected boolean emailVerified;
    @Field
    protected int loyaltyPoints;
    @JsonDeserialize(using = BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1.class)
    @XmlSchemaType(name = "decimal")
    @Field
    protected BigDecimal chaayosCash;
    @XmlElement(required = true, type = Integer.class, nillable = true)
    @Field
    protected Integer registrationUnitId;
    @XmlElement(required = true)
    @Field
    protected String acquisitionSource;
    @XmlElement(required = true)
    @Field
    protected String acquisitionToken;
    @Field
    protected boolean contactNumberVerified;
    @Field
    protected boolean availedSignupOffer;
    @Field
    protected Integer lastOrderId;
    @Field
    protected Integer orderCount;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter2.class)
    @XmlSchemaType(name = "date")
    @Field
    protected Date lastOrderTime;
    @Field
    protected boolean smsSubscriber;
    @Field
    protected boolean emailSubscriber;
    @Field
    protected boolean loyaltySubscriber;
    @Field
    protected boolean isBlacklisted;
    @Field
    protected boolean internal;
    @Field
    protected List<Address> addresses;
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter2.class)
    @XmlSchemaType(name = "date")
    protected Date addTime;
    @Field
    protected Date numberVerificationTime;
    @Field
    protected TrueCallerVerifiedProfile trueCallerProfile;
    @Field
    protected boolean isDND;
    @Field
    protected boolean optOutOfFaceIt = false;
    @Field
    @XmlElement(required = true, type = String.class, nillable = true)
    @XmlJavaTypeAdapter(Adapter2.class)
    @XmlSchemaType(name = "date")
    protected Date optOutTime;
    @Field
    protected Date lastNPSTime;
    @Field
    private String refCode;
    @Field
    private String isRefSubscriber;
    @Field
    private String refAcquisitionSource;
    @Field
    private Date referredOn;
    @Field
    private Integer referralDataId;
    @Field
    private Integer referrerId;
    @Field
    private String signUpRefCode; // ref code of referrer used on signup
    @Field
    private boolean isReferrerAwarded;
    @Field
    private String faceId;
    @XmlElement(required = true)
    @Field
    protected Integer acquisitionBrandId;
    @XmlElement(required = true)
    @Field
    protected boolean chaayosCustomer;
    @Field
    private String gender;
    @Field
    private Date dateOfBirth;
    @Field
    private Date anniversary;

    private boolean isNewCustomerForBrand;

    private boolean signUpOfferExpired;
    private Date signupOfferExpiryTime;

    private String customerAppId;
    private String optWhatsapp;
    private SubscriptionInfoDetail subscriptionInfoDetail;
    @Field
    private CustomerTransaction customerTransaction;

    private BigDecimal walletBalance;

    private String appAction;

    private Date appActionTime;


    public String get_id() {
        return _id;
    }

    public void set_id(String _id) {
        this._id = _id;
    }

/*	public Long getVersion() {
		return version;
	}

	public void setVersion(Long version) {
		this.version = version;
	}

	public String getDetachAll() {
		return detachAll;
	}

	public void setDetachAll(String detachAll) {
		this.detachAll = detachAll;
	}*/

    /**
     * Gets the value of the id property.
     */
    public int getId() {
        return id;
    }

    /**
     * Sets the value of the id property.
     */
    public void setId(int value) {
        this.id = value;
    }

    /**
     * Gets the value of the firstName property.
     *
     * @return possible object is {@link String }
     */
    public String getFirstName() {
        return firstName;
    }

    /**
     * Sets the value of the firstName property.
     *
     * @param value allowed object is {@link String }
     */
    public void setFirstName(String value) {
        this.firstName = value;
    }

    /**
     * Gets the value of the middleName property.
     *
     * @return possible object is {@link String }
     */
    public String getMiddleName() {
        return middleName;
    }

    /**
     * Sets the value of the middleName property.
     *
     * @param value allowed object is {@link String }
     */
    public void setMiddleName(String value) {
        this.middleName = value;
    }

    /**
     * Gets the value of the lastName property.
     *
     * @return possible object is {@link String }
     */
    public String getLastName() {
        return lastName;
    }

    /**
     * Sets the value of the lastName property.
     *
     * @param value allowed object is {@link String }
     */
    public void setLastName(String value) {
        this.lastName = value;
    }

    /**
     * Gets the value of the countryCode property.
     *
     * @return possible object is {@link String }
     */
    public String getCountryCode() {
        return countryCode;
    }

    /**
     * Sets the value of the countryCode property.
     *
     * @param value allowed object is {@link String }
     */
    public void setCountryCode(String value) {
        this.countryCode = value;
    }

    /**
     * Gets the value of the contactNumber property.
     *
     * @return possible object is {@link String }
     */
    public String getContactNumber() {
        return contactNumber;
    }

    /**
     * Sets the value of the contactNumber property.
     *
     * @param value allowed object is {@link String }
     */
    public void setContactNumber(String value) {
        this.contactNumber = value;
    }

    /**
     * Gets the value of the emailId property.
     *
     * @return possible object is {@link String }
     */
    public String getEmailId() {
        return emailId;
    }

    /**
     * Sets the value of the emailId property.
     *
     * @param value allowed object is {@link String }
     */
    public void setEmailId(String value) {
        this.emailId = value;
    }

    /**
     * Gets the value of the emailVerified property.
     */
    public boolean isEmailVerified() {
        return emailVerified;
    }

    /**
     * Sets the value of the emailVerified property.
     */
    public void setEmailVerified(boolean value) {
        this.emailVerified = value;
    }

    /**
     * Gets the value of the loyaltyPoints property.
     */
    public int getLoyaltyPoints() {
        return loyaltyPoints;
    }

    /**
     * Sets the value of the loyaltyPoints property.
     */
    public void setLoyaltyPoints(int value) {
        this.loyaltyPoints = value;
    }

    /**
     * Gets the value of the registrationUnitId property.
     *
     * @return possible object is {@link Integer }
     */
    public Integer getRegistrationUnitId() {
        return registrationUnitId;
    }

    /**
     * Sets the value of the registrationUnitId property.
     *
     * @param value allowed object is {@link Integer }
     */
    public void setRegistrationUnitId(Integer value) {
        this.registrationUnitId = value;
    }

    /**
     * Gets the value of the acquisitionSource property.
     *
     * @return possible object is {@link String }
     */
    public String getAcquisitionSource() {
        return acquisitionSource;
    }

    /**
     * Sets the value of the acquisitionSource property.
     *
     * @param value allowed object is {@link String }
     */
    public void setAcquisitionSource(String value) {
        this.acquisitionSource = value;
    }

    /**
     * Gets the value of the acquisitionToken property.
     *
     * @return possible object is {@link String }
     */
    public String getAcquisitionToken() {
        return acquisitionToken;
    }

    /**
     * Sets the value of the acquisitionToken property.
     *
     * @param value allowed object is {@link String }
     */
    public void setAcquisitionToken(String value) {
        this.acquisitionToken = value;
    }

    /**
     * Gets the value of the contactNumberVerified property.
     */
    public boolean isContactNumberVerified() {
        return contactNumberVerified;
    }

    /**
     * Sets the value of the contactNumberVerified property.
     */
    public void setContactNumberVerified(boolean value) {
        this.contactNumberVerified = value;
    }

    /**
     * Gets the value of the availedSignupOffer property.
     */
    public boolean isAvailedSignupOffer() {
        return availedSignupOffer;
    }

    /**
     * Sets the value of the availedSignupOffer property.
     */
    public void setAvailedSignupOffer(boolean value) {
        this.availedSignupOffer = value;
    }

    /**
     * Gets the value of the lastOrderId property.
     */
    public Integer getLastOrderId() {
        return lastOrderId;
    }

    /**
     * Sets the value of the lastOrderId property.
     */
    public void setLastOrderId(Integer value) {
        this.lastOrderId = value;
    }

    /**
     * Gets the value of the orderCount property.
     */
    public Integer getOrderCount() {
        return orderCount;
    }

    /**
     * Sets the value of the orderCount property.
     */
    public void setOrderCount(Integer value) {
        this.orderCount = value;
    }

    /**
     * Gets the value of the lastOrderTime property.
     *
     * @return possible object is {@link String }
     */
    public Date getLastOrderTime() {
        return lastOrderTime;
    }

    /**
     * Sets the value of the lastOrderTime property.
     *
     * @param value allowed object is {@link String }
     */
    public void setLastOrderTime(Date value) {
        this.lastOrderTime = value;
    }

    /**
     * Gets the value of the smsSubscriber property.
     */
    public boolean isSmsSubscriber() {
        return smsSubscriber;
    }

    /**
     * Sets the value of the smsSubscriber property.
     */
    public void setSmsSubscriber(boolean value) {
        this.smsSubscriber = value;
    }

    /**
     * Gets the value of the emailSubscriber property.
     */
    public boolean isEmailSubscriber() {
        return emailSubscriber;
    }

    /**
     * Sets the value of the emailSubscriber property.
     */
    public void setEmailSubscriber(boolean value) {
        this.emailSubscriber = value;
    }

    /**
     * Gets the value of the loyaltySubscriber property.
     */
    public boolean isLoyaltySubscriber() {
        return loyaltySubscriber;
    }

    /**
     * Sets the value of the loyaltySubscriber property.
     */
    public void setLoyaltySubscriber(boolean value) {
        this.loyaltySubscriber = value;
    }

    /**
     * Gets the value of the addresses property.
     * <p>
     * <p>
     * This accessor method returns a reference to the live list, not a
     * snapshot. Therefore any modification you make to the returned list will
     * be present inside the JAXB object. This is why there is not a
     * <CODE>set</CODE> method for the addresses property.
     * <p>
     * <p>
     * For example, to add a new item, do as follows:
     *
     * <pre>
     * getAddresses().add(newItem);
     * </pre>
     * <p>
     * <p>
     * <p>
     * Objects of the following type(s) are allowed in the list {@link Address }
     */
    public List<Address> getAddresses() {
        if (addresses == null) {
            addresses = new ArrayList<Address>();
        }
        return this.addresses;
    }

    public void setAddresses(List<Address> addresses) {
        this.addresses = addresses;
    }

    public boolean isBlacklisted() {
        return isBlacklisted;
    }

    public void setBlacklisted(boolean blacklisted) {
        isBlacklisted = blacklisted;
    }

    public Date getAddTime() {
        return addTime;
    }

    public void setAddTime(Date addTime) {
        this.addTime = addTime;
    }

    public Date getNumberVerificationTime() {
        return numberVerificationTime;
    }

    public void setNumberVerificationTime(Date numberVerificationTime) {
        this.numberVerificationTime = numberVerificationTime;
    }

    public TrueCallerVerifiedProfile getTrueCallerProfile() {
        return trueCallerProfile;
    }

    public void setTrueCallerProfile(TrueCallerVerifiedProfile trueCallerProfile) {
        this.trueCallerProfile = trueCallerProfile;
    }

    public boolean isDND() {
        return isDND;
    }

    public void setDND(boolean isDND) {
        this.isDND = isDND;
    }

    public Date getLastNPSTime() {
        return lastNPSTime;
    }

    public void setLastNPSTime(Date lastNPSTime) {
        this.lastNPSTime = lastNPSTime;
    }

    public boolean isInternal() {
        return internal;
    }

    public void setInternal(boolean internal) {
        this.internal = internal;
    }

    public String getRefCode() {
        return refCode;
    }

    public void setRefCode(String refCode) {
        this.refCode = refCode;
    }

    public String getIsRefSubscriber() {
        return isRefSubscriber;
    }

    public void setIsRefSubscriber(String isRefSubscriber) {
        this.isRefSubscriber = isRefSubscriber;
    }

    public String getRefAcquisitionSource() {
        return refAcquisitionSource;
    }

    public void setRefAcquisitionSource(String refAcquisitionSource) {
        this.refAcquisitionSource = refAcquisitionSource;
    }

    public Date getReferredOn() {
        return referredOn;
    }

    public void setReferredOn(Date referredOn) {
        this.referredOn = referredOn;
    }

    public Integer getReferralDataId() {
        return referralDataId;
    }

    public void setReferralDataId(Integer referralDataId) {
        this.referralDataId = referralDataId;
    }

    public Integer getReferrerId() {
        return referrerId;
    }

    public void setReferrerId(Integer referralId) {
        this.referrerId = referralId;
    }

    public String getSignUpRefCode() {
        return signUpRefCode;
    }

    public void setSignUpRefCode(String signUpRefCode) {
        this.signUpRefCode = signUpRefCode;
    }

    public boolean isReferrerAwarded() {
        return isReferrerAwarded;
    }

    public void setReferrerAwarded(boolean isReferrerAwarded) {
        this.isReferrerAwarded = isReferrerAwarded;
    }

    public BigDecimal getChaayosCash() {
        return chaayosCash;
    }

    public void setChaayosCash(BigDecimal chaayosCash) {
        this.chaayosCash = chaayosCash;
    }

    public boolean isOptOutOfFaceIt() {
        return optOutOfFaceIt;
    }

    public void setOptOutOfFaceIt(boolean optOutOfFaceIt) {
        this.optOutOfFaceIt = optOutOfFaceIt;
    }

    public Date getOptOutTime() {
        return optOutTime;
    }

    public void setOptOutTime(Date optOutTime) {
        this.optOutTime = optOutTime;
    }

    public String getFaceId() {
        return faceId;
    }

    public void setFaceId(String faceId) {
        this.faceId = faceId;
    }

    public Integer getAcquisitionBrandId() {
        return acquisitionBrandId;
    }

    public void setAcquisitionBrandId(Integer acquisitionBrandId) {
        this.acquisitionBrandId = acquisitionBrandId;
    }

    public boolean isChaayosCustomer() {
        return chaayosCustomer;
    }

    public void setChaayosCustomer(boolean chaayosCustomer) {
        this.chaayosCustomer = chaayosCustomer;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public Date getDateOfBirth() {
        return dateOfBirth;
    }

    public void setDateOfBirth(Date dateOfBirth) {
        this.dateOfBirth = dateOfBirth;
    }

    public Date getAnniversary() {
        return anniversary;
    }

    public void setAnniversary(Date anniversary) {
        this.anniversary = anniversary;
    }

    public boolean isNewCustomerForBrand() {
        return isNewCustomerForBrand;
    }

    public void setNewCustomerForBrand(boolean newCustomerForBrand) {
        isNewCustomerForBrand = newCustomerForBrand;
    }

    public boolean isSignUpOfferExpired() {
        return signUpOfferExpired;
    }

    public void setSignUpOfferExpired(boolean signUpOfferExpired) {
        this.signUpOfferExpired = signUpOfferExpired;
    }

    public Date getSignupOfferExpiryTime() {
        return signupOfferExpiryTime;
    }

    public void setSignupOfferExpiryTime(Date signupOfferExpiryTime) {
        this.signupOfferExpiryTime = signupOfferExpiryTime;
    }

    public String getCustomerAppId() {
        return customerAppId;
    }

    public void setCustomerAppId(String customerAppId) {
        this.customerAppId = customerAppId;
    }

    public SubscriptionInfoDetail getSubscriptionInfoDetail() {
        return subscriptionInfoDetail;
    }

    public void setSubscriptionInfoDetail(SubscriptionInfoDetail subscriptionInfoDetail) {
        this.subscriptionInfoDetail = subscriptionInfoDetail;
    }

    public String getOptWhatsapp() {
        return optWhatsapp;
    }

    public void setOptWhatsapp(String optWhatsapp) {
        this.optWhatsapp = optWhatsapp;
    }

    public CustomerTransaction getCustomerTransaction() {
        return customerTransaction;
    }

    public void setCustomerTransaction(CustomerTransaction customerTransaction) {
        this.customerTransaction = customerTransaction;
    }

    public BigDecimal getWalletBalance() {
        return walletBalance;
    }

    public void setWalletBalance(BigDecimal walletBalance) {
        this.walletBalance = walletBalance;
    }

    public String getAppAction() {
        return appAction;
    }

    public void setAppAction(String appAction) {
        this.appAction = appAction;
    }

    public Date getAppActionTime() {
        return appActionTime;
    }

    public void setAppActionTime(Date appActionTime) {
        this.appActionTime = appActionTime;
    }
}
