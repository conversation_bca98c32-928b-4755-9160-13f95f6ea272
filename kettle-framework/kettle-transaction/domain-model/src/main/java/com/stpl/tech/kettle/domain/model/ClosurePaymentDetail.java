/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.01.09 at 05:35:16 PM IST 
//

package com.stpl.tech.kettle.domain.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.master.domain.model.Adapter1;
import com.stpl.tech.util.domain.adapter.BigDecimalDeserializer;

/**
 * <p>
 * Java class for ClosurePaymentDetail complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="ClosurePaymentDetail"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="id" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="closureId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="reconState" type="{http://www.w3schools.com}ReconState"/&gt;
 *         &lt;element name="paymentModeId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="expectedAmount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="actualAmount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="comment" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="gmv" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="discount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="netSalesAmount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="roundOff" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="totalAmount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="billCount" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "ClosurePaymentDetail", propOrder = { "id", "closureId", "reconState", "paymentModeId",
		"expectedAmount", "actualAmount", "comment", "gmv", "discount", "netSalesAmount", "roundOff", "totalAmount", "billCount" })
public class ClosurePaymentDetail {

	protected int id;
	protected int closureId;
	@XmlElement(required = true, nillable = true)
	@XmlSchemaType(name = "string")
	protected ReconState reconState;
	protected int paymentModeId;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal expectedAmount;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal actualAmount;
	@XmlElement(required = true)
	protected String comment;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal gmv;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal discount;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal netSalesAmount;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal roundOff;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal totalAmount;
	protected int billCount;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	protected BigDecimal tax;
	protected List<TaxDetail> taxes;

	/**
	 * Gets the value of the id property.
	 * 
	 */
	public int getId() {
		return id;
	}

	/**
	 * Sets the value of the id property.
	 * 
	 */
	public void setId(int value) {
		this.id = value;
	}

	/**
	 * Gets the value of the closureId property.
	 * 
	 */
	public int getClosureId() {
		return closureId;
	}

	/**
	 * Sets the value of the closureId property.
	 * 
	 */
	public void setClosureId(int value) {
		this.closureId = value;
	}

	/**
	 * Gets the value of the reconState property.
	 * 
	 * @return possible object is {@link ReconState }
	 * 
	 */
	public ReconState getReconState() {
		return reconState;
	}

	/**
	 * Sets the value of the reconState property.
	 * 
	 * @param value
	 *            allowed object is {@link ReconState }
	 * 
	 */
	public void setReconState(ReconState value) {
		this.reconState = value;
	}

	/**
	 * Gets the value of the paymentModeId property.
	 * 
	 */
	public int getPaymentModeId() {
		return paymentModeId;
	}

	/**
	 * Sets the value of the paymentModeId property.
	 * 
	 */
	public void setPaymentModeId(int value) {
		this.paymentModeId = value;
	}

	/**
	 * Gets the value of the expectedAmount property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public BigDecimal getExpectedAmount() {
		return expectedAmount;
	}

	/**
	 * Sets the value of the expectedAmount property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setExpectedAmount(BigDecimal value) {
		this.expectedAmount = value;
	}

	/**
	 * Gets the value of the actualAmount property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public BigDecimal getActualAmount() {
		return actualAmount;
	}

	/**
	 * Sets the value of the actualAmount property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setActualAmount(BigDecimal value) {
		this.actualAmount = value;
	}

	/**
	 * Gets the value of the comment property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getComment() {
		return comment;
	}

	/**
	 * Sets the value of the comment property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setComment(String value) {
		this.comment = value;
	}

	/**
	 * Gets the value of the gmv property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public BigDecimal getGmv() {
		return gmv;
	}

	/**
	 * Sets the value of the gmv property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setGmv(BigDecimal value) {
		this.gmv = value;
	}

	/**
	 * Gets the value of the discount property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public BigDecimal getDiscount() {
		return discount;
	}

	/**
	 * Sets the value of the discount property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setDiscount(BigDecimal value) {
		this.discount = value;
	}

	/**
	 * Gets the value of the netSalesAmount property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public BigDecimal getNetSalesAmount() {
		return netSalesAmount;
	}

	/**
	 * Sets the value of the netSalesAmount property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setNetSalesAmount(BigDecimal value) {
		this.netSalesAmount = value;
	}

	/**
	 * Gets the value of the roundOff property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public BigDecimal getRoundOff() {
		return roundOff;
	}

	/**
	 * Sets the value of the roundOff property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setRoundOff(BigDecimal value) {
		this.roundOff = value;
	}

	/**
	 * Gets the value of the totalAmount property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public BigDecimal getTotalAmount() {
		return totalAmount;
	}

	/**
	 * Sets the value of the totalAmount property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setTotalAmount(BigDecimal value) {
		this.totalAmount = value;
	}

	/**
	 * Gets the value of the billCount property.
	 * 
	 */
	public int getBillCount() {
		return billCount;
	}

	/**
	 * Sets the value of the billCount property.
	 * 
	 */
	public void setBillCount(int value) {
		this.billCount = value;
	}

	public List<TaxDetail> getTaxes() {
		if (taxes == null) {
			taxes = new ArrayList<>();
		}
		return taxes;
	}

	public BigDecimal getTax() {
		return tax;
	}

	public void setTax(BigDecimal tax) {
		this.tax = tax;
	}

	
}
