/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.04.09 at 06:52:19 PM IST 
//


package com.stpl.tech.kettle.domain.model;

import java.math.BigDecimal;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.master.domain.model.Adapter1;
import com.stpl.tech.util.domain.adapter.BigDecimalDeserializer;


/**
 * <p>Java class for UnitExpenseDrilldown complex type.
 * 
 * <p>The following schema fragment specifies the expected content contained within this class.
 * 
 * <pre>
 * &lt;complexType name="UnitExpenseDrilldown"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="drilldownId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="unitExpenseDetailId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="expenseUpdateEventId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="referenceId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="referenceName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="expenseType" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="expenseValue" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="noOfTickets" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="expenseRate" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="adjustments" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "UnitExpenseDrilldown", propOrder = {
    "drilldownId",
    "unitExpenseDetailId",
    "expenseUpdateEventId",
    "referenceId",
    "referenceName",
    "expenseType",
    "expenseValue",
    "noOfTickets",
    "expenseRate",
    "adjustments"
})
public class UnitExpenseDrilldown {

    protected int drilldownId;
    protected int unitExpenseDetailId;
    protected int expenseUpdateEventId;
    protected int referenceId;
    @XmlElement(required = true)
    protected String referenceName;
    @XmlElement(required = true)
    protected String expenseType;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal expenseValue;
    protected int noOfTickets;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal expenseRate;
	@JsonDeserialize(using=BigDecimalDeserializer.class)
    @XmlElement(required = true, type = String.class)
    @XmlJavaTypeAdapter(Adapter1 .class)
    @XmlSchemaType(name = "decimal")
    protected BigDecimal adjustments;

    /**
     * Gets the value of the drilldownId property.
     * 
     */
    public int getDrilldownId() {
        return drilldownId;
    }

    /**
     * Sets the value of the drilldownId property.
     * 
     */
    public void setDrilldownId(int value) {
        this.drilldownId = value;
    }

    /**
     * Gets the value of the unitExpenseDetailId property.
     * 
     */
    public int getUnitExpenseDetailId() {
        return unitExpenseDetailId;
    }

    /**
     * Sets the value of the unitExpenseDetailId property.
     * 
     */
    public void setUnitExpenseDetailId(int value) {
        this.unitExpenseDetailId = value;
    }

    /**
     * Gets the value of the expenseUpdateEventId property.
     * 
     */
    public int getExpenseUpdateEventId() {
        return expenseUpdateEventId;
    }

    /**
     * Sets the value of the expenseUpdateEventId property.
     * 
     */
    public void setExpenseUpdateEventId(int value) {
        this.expenseUpdateEventId = value;
    }

    /**
     * Gets the value of the referenceId property.
     * 
     */
    public int getReferenceId() {
        return referenceId;
    }

    /**
     * Sets the value of the referenceId property.
     * 
     */
    public void setReferenceId(int value) {
        this.referenceId = value;
    }

    /**
     * Gets the value of the referenceName property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getReferenceName() {
        return referenceName;
    }

    /**
     * Sets the value of the referenceName property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setReferenceName(String value) {
        this.referenceName = value;
    }

    /**
     * Gets the value of the expenseType property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public String getExpenseType() {
        return expenseType;
    }

    /**
     * Sets the value of the expenseType property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setExpenseType(String value) {
        this.expenseType = value;
    }

    /**
     * Gets the value of the expenseValue property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getExpenseValue() {
        return expenseValue;
    }

    /**
     * Sets the value of the expenseValue property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setExpenseValue(BigDecimal value) {
        this.expenseValue = value;
    }

    /**
     * Gets the value of the noOfTickets property.
     * 
     */
    public int getNoOfTickets() {
        return noOfTickets;
    }

    /**
     * Sets the value of the noOfTickets property.
     * 
     */
    public void setNoOfTickets(int value) {
        this.noOfTickets = value;
    }

    /**
     * Gets the value of the expenseRate property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getExpenseRate() {
        return expenseRate;
    }

    /**
     * Sets the value of the expenseRate property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setExpenseRate(BigDecimal value) {
        this.expenseRate = value;
    }

    /**
     * Gets the value of the adjustments property.
     * 
     * @return
     *     possible object is
     *     {@link String }
     *     
     */
    public BigDecimal getAdjustments() {
        return adjustments;
    }

    /**
     * Sets the value of the adjustments property.
     * 
     * @param value
     *     allowed object is
     *     {@link String }
     *     
     */
    public void setAdjustments(BigDecimal value) {
        this.adjustments = value;
    }

}
