/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11 
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a> 
// Any modifications to this file will be lost upon recompilation of the source schema. 
// Generated on: 2016.01.09 at 05:35:16 PM IST 
//

package com.stpl.tech.kettle.domain.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.master.domain.model.Adapter1;
import com.stpl.tech.master.domain.model.BillType;
import com.stpl.tech.master.domain.model.IdCodeName;
import com.stpl.tech.util.domain.adapter.BigDecimalDeserializer;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * Java class for OrderItem complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="OrderItem"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="itemId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="productId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="productName" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="productCategory" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="productSubCategory" type="{http://www.w3schools.com}IdCodeName"/&gt;
 *         &lt;element name="quantity" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="price" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="totalAmount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="amount" type="{http://www.w3.org/2001/XMLSchema}decimal"/&gt;
 *         &lt;element name="discountDetail" type="{http://www.w3schools.com}DiscountDetail"/&gt;
 *         &lt;element name="complimentaryDetail" type="{http://www.w3schools.com}ComplimentaryDetail"/&gt;
 *         &lt;element name="dimension" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *         &lt;element name="billType" type="{http://www.w3schools.com}BillType"/&gt;
 *         &lt;element name="recipeId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="itemCode" type="{http://www.w3.org/2001/XMLSchema}string"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "OrderItem", propOrder = { "itemId", "productId", "productName", "productCategory",
		"productSubCategory", "quantity", "price", "totalAmount", "amount", "discountDetail", "complimentaryDetail",
		"dimension", "billType", "composition", "recipeId", "itemCode", "taxes","recomCategory", "partnerTaxType" })

@Document
public class OrderItem implements Serializable {

	/**
	 * 
	 */
	private static final long serialVersionUID = 4778973176476607094L;

	@Id
	private String _id;

	/*
	 * @Version
	 * 
	 * @JsonIgnore private Long version;
	 * 
	 *//**
		 * Added to avoid a runtime error whereby the detachAll property is
		 * checked for existence but not actually used.
		 *//*
		 * private String detachAll;
		 */
	@Field
	protected int itemId;
	@Field
	protected int productId;
	@XmlElement(required = true)
	@Field
	protected String productName;

	@Field
	protected String itemName;

	@XmlElement(required = true)
	@Field
	protected String productAliasName;
	@XmlElement(required = true)
	@Field
	protected IdCodeName productCategory;
	@Field
	protected IdCodeName productSubCategory;
	@Field
	protected int quantity;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal price;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal originalPrice;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal totalAmount;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal amount;
	@XmlElement(required = true, nillable = true)
	@Field
	protected DiscountDetail discountDetail;
	@XmlElement(required = true)
	@Field
	protected ComplimentaryDetail complimentaryDetail;
	@XmlElement(required = true, nillable = true)
	@Field
	protected String dimension;
	@XmlElement(required = true, defaultValue = "NET_PRICE")
	@XmlSchemaType(name = "string")
	@Field
	protected BillType billType;
	@XmlElement(nillable = true)
	@Field
	protected OrderItemComposition composition;
	@XmlElement(nillable = true)
	@Field
	protected int recipeId;
	@XmlElement(required = true)
	protected String itemCode;
	@JsonDeserialize(using = BigDecimalDeserializer.class)
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter1.class)
	@XmlSchemaType(name = "decimal")
	@Field
	protected BigDecimal tax;
	@Field
	protected String code;
	@Field
	protected List<TaxDetail> taxes;
	@XmlElement(required = true, nillable = true)
	@Field
	protected Integer reasonId;
	@XmlElement(required = true, nillable = true)
	@Field
	protected Boolean bookedWastage;
	@XmlElement(required = true, nillable = true)
	@Field
	protected String cardType;
	@XmlElement(required = true, nillable = true)
	@Field
	protected Boolean takeAway;
	@Field
	protected String recipeProfile;
	protected Date validUpto;
	protected String voucherCode;
	protected BigDecimal offerAmount;
	@Field
	protected String recomCategory;
	protected Boolean taxDeductedByPartner;
	protected Boolean hasPreference;
	protected PreferenceDetail preferenceDetail;
	protected String partnerTaxType;

	protected String sourceCategory;
	protected String sourceSubCategory;

	protected int productType;
	protected int productSubType;

	protected String orderItemRemark;
	protected BigDecimal prepTime;

	protected String productAttr;

	protected boolean loyaltyBurned;
	protected Integer loyaltyBurnPoints;

	protected String itemKey;

	protected String isBestPairedItem;

	protected String stationCategory;

	protected String productDescription;

	protected MilkVariant milkVariant;

	protected String isHoldOn = "N";

	public String getProductDescription() {
		return productDescription;
	}

	public void setProductDescription(String productDescription) {
		this.productDescription = productDescription;
	}



	public String getRecomCategory() {
		return recomCategory;
	}

	public void setRecomCategory(String recomCategory) {
		this.recomCategory = recomCategory;
	}




	public String get_id() {
		return _id;
	}

	public void set_id(String _id) {
		this._id = _id;
	}

	/*
	 * public Long getVersion() { return version; }
	 * 
	 * public void setVersion(Long version) { this.version = version; }
	 * 
	 * public String getDetachAll() { return detachAll; }
	 * 
	 * public void setDetachAll(String detachAll) { this.detachAll = detachAll;
	 * }
	 */
	/**
	 * Gets the value of the itemId property.
	 * 
	 */
	public int getItemId() {
		return itemId;
	}

	/**
	 * Sets the value of the itemId property.
	 * 
	 */
	public void setItemId(int value) {
		this.itemId = value;
	}

	/**
	 * Gets the value of the productId property.
	 * 
	 */
	public int getProductId() {
		return productId;
	}

	/**
	 * Sets the value of the productId property.
	 * 
	 */
	public void setProductId(int value) {
		this.productId = value;
	}

	/**
	 * Gets the value of the productName property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getProductName() {
		return productName;
	}

	/**
	 * Sets the value of the productName property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setProductName(String value) {
		this.productName = value;
	}

	/**
	 * Gets the value of the productCategory property.
	 * 
	 * @return possible object is {@link IdCodeName }
	 * 
	 */
	public IdCodeName getProductCategory() {
		return productCategory;
	}

	/**
	 * Sets the value of the productCategory property.
	 * 
	 * @param value
	 *            allowed object is {@link IdCodeName }
	 * 
	 */
	public void setProductCategory(IdCodeName value) {
		this.productCategory = value;
	}

	/**
	 * Gets the value of the productCategory property.
	 * 
	 * @return possible object is {@link IdCodeName }
	 * 
	 */
	public IdCodeName getProductSubCategory() {
		return productSubCategory;
	}

	/**
	 * Sets the value of the productCategory property.
	 * 
	 * @param value
	 *            allowed object is {@link IdCodeName }
	 * 
	 */
	public void setProductSubCategory(IdCodeName value) {
		this.productSubCategory = value;
	}

	/**
	 * Gets the value of the quantity property.
	 * 
	 */
	public int getQuantity() {
		return quantity;
	}

	/**
	 * Sets the value of the quantity property.
	 * 
	 */
	public void setQuantity(int value) {
		this.quantity = value;
	}

	/**
	 * Gets the value of the price property.
	 * 
	 * @return possible object is {@link BigDecimal }
	 * 
	 */
	public BigDecimal getPrice() {
		return price;
	}

	/**
	 * Sets the value of the price property.
	 * 
	 * @param value
	 *            allowed object is {@link BigDecimal }
	 * 
	 */
	public void setPrice(BigDecimal value) {
		this.price = value;
	}

	/**
	 * Gets the value of the totalAmount property.
	 * 
	 * @return possible object is {@link BigDecimal }
	 * 
	 */
	public BigDecimal getTotalAmount() {
		return totalAmount;
	}

	/**
	 * Sets the value of the totalAmount property.
	 * 
	 * @param value
	 *            allowed object is {@link BigDecimal }
	 * 
	 */
	public void setTotalAmount(BigDecimal value) {
		this.totalAmount = value;
	}

	/**
	 * Gets the value of the amount property.
	 * 
	 * @return possible object is {@link BigDecimal }
	 * 
	 */
	public BigDecimal getAmount() {
		return amount;
	}

	/**
	 * Sets the value of the amount property.
	 * 
	 * @param value
	 *            allowed object is {@link BigDecimal }
	 * 
	 */
	public void setAmount(BigDecimal value) {
		this.amount = value;
	}

	/**
	 * Gets the value of the discountDetail property.
	 * 
	 * @return possible object is {@link DiscountDetail }
	 * 
	 */
	public DiscountDetail getDiscountDetail() {
		return discountDetail;
	}

	/**
	 * Sets the value of the discountDetail property.
	 * 
	 * @param value
	 *            allowed object is {@link DiscountDetail }
	 * 
	 */
	public void setDiscountDetail(DiscountDetail value) {
		this.discountDetail = value;
	}

	/**
	 * Gets the value of the complimentaryDetail property.
	 * 
	 * @return possible object is {@link ComplimentaryDetail }
	 * 
	 */
	public ComplimentaryDetail getComplimentaryDetail() {
		return complimentaryDetail;
	}

	/**
	 * Sets the value of the complimentaryDetail property.
	 * 
	 * @param value
	 *            allowed object is {@link ComplimentaryDetail }
	 * 
	 */
	public void setComplimentaryDetail(ComplimentaryDetail value) {
		this.complimentaryDetail = value;
	}

	/**
	 * Gets the value of the dimension property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getDimension() {
		return dimension;
	}

	/**
	 * Sets the value of the dimension property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setDimension(String value) {
		this.dimension = value;
	}

	/**
	 * Gets the value of the billType property.
	 * 
	 * @return possible object is {@link BillType }
	 * 
	 */
	public BillType getBillType() {
		return billType;
	}

	/**
	 * Sets the value of the billType property.
	 * 
	 * @param value
	 *            allowed object is {@link BillType }
	 * 
	 */
	public void setBillType(BillType value) {
		this.billType = value;
	}

	public OrderItemComposition getComposition() {
		return composition;
	}

	public void setComposition(OrderItemComposition composition) {
		this.composition = composition;
	}

	/**
	 * Gets the value of the recipeId property.
	 * 
	 */
	public int getRecipeId() {
		return recipeId;
	}

	/**
	 * Sets the value of the recipeId property.
	 * 
	 */
	public void setRecipeId(int value) {
		this.recipeId = value;
	}

	/**
	 * Gets the value of the itemCode property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public String getItemCode() {
		return itemCode;
	}

	/**
	 * Sets the value of the itemCode property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setItemCode(String value) {
		this.itemCode = value;
	}

	public List<TaxDetail> getTaxes() {
		if (taxes == null) {
			taxes = new ArrayList<>();
		}
		return taxes;
	}

	public BigDecimal getTax() {
		return tax;
	}

	public void setTax(BigDecimal tax) {
		this.tax = tax;
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public Integer getReasonId() {
		return reasonId;
	}

	public void setReasonId(Integer reasonId) {
		this.reasonId = reasonId;
	}

	public Boolean getBookedWastage() {
		return bookedWastage;
	}

	public void setBookedWastage(Boolean bookedWastage) {
		this.bookedWastage = bookedWastage;
	}

	public String getCardType() {
		return cardType;
	}

	public void setCardType(String cardType) {
		this.cardType = cardType;
	}

	public Boolean getTakeAway() {
		return takeAway;
	}

	public void setTakeAway(Boolean takeAway) {
		this.takeAway = takeAway;
	}

	public String getSourceCategory() {
		return sourceCategory;
	}

	public void setSourceCategory(String sourceCategory) {
		this.sourceCategory = sourceCategory;
	}

	public String getSourceSubCategory() {
		return sourceSubCategory;
	}

	public void setSourceSubCategory(String sourceSubCategory) {
		this.sourceSubCategory = sourceSubCategory;
	}

	@JsonIgnore
	public Date getValidUpto() {
		return validUpto;
	}

	public void setValidUpto(Date validUpto) {
		this.validUpto = validUpto;
	}

	@JsonIgnore
	public String getVoucherCode() {
		return voucherCode;
	}
	
	public void setVoucherCode(String voucherCode) {
		this.voucherCode = voucherCode;
	}

	public String getRecipeProfile() {
		return recipeProfile;
	}

	public void setRecipeProfile(String recipeProfile) {
		this.recipeProfile = recipeProfile;
	}

	public BigDecimal getOfferAmount() {
		return offerAmount;
	}

	public void setOfferAmount(BigDecimal offerAmount) {
		this.offerAmount = offerAmount;
	}

	public BigDecimal getOriginalPrice() {
		return originalPrice;
	}

	public void setOriginalPrice(BigDecimal originalPrice) {
		this.originalPrice = originalPrice;
	}

	public Boolean getTaxDeductedByPartner() {
		return taxDeductedByPartner;
	}

	public void setTaxDeductedByPartner(Boolean taxDeductedByPartner) {
		this.taxDeductedByPartner = taxDeductedByPartner;
	}

	public Boolean getHasPreference() {
		return hasPreference;
	}

	public void setHasPreference(Boolean hasPreference) {
		this.hasPreference = hasPreference;
	}

	public PreferenceDetail getPreferenceDetail() {
		return preferenceDetail;
	}

	public void setPreferenceDetail(PreferenceDetail preferenceDetail) {
		this.preferenceDetail = preferenceDetail;
	}

	public String getPartnerTaxType() {
		return partnerTaxType;
	}

	public void setPartnerTaxType(String partnerTaxType) {
		this.partnerTaxType = partnerTaxType;
	}

	public int getProductType() {
		return productType;
	}

	public void setProductType(int productType) {
		this.productType = productType;
	}

	public String getOrderItemRemark() {
		return orderItemRemark;
	}

	public void setOrderItemRemark(String orderItemRemark) {
		this.orderItemRemark = orderItemRemark;
	}

	public BigDecimal getPrepTime() {
		return prepTime;
	}

	public void setPrepTime(BigDecimal prepTime) {
		this.prepTime = prepTime;
	}

	public String getProductAliasName() {
		return productAliasName;
	}

	public void setProductAliasName(String productAliasName) {
		this.productAliasName = productAliasName;
	}

	public String getItemName() {
		return itemName;
	}

	public void setItemName(String itemName) {
		this.itemName = itemName;
	}

	public String getProductAttr() {
		return productAttr;
	}

	public void setProductAttr(String productAttr) {
		this.productAttr = productAttr;
	}

	public boolean isLoyaltyBurned() {
		return loyaltyBurned;
	}

	public void setLoyaltyBurned(boolean loyaltyBurned) {
		this.loyaltyBurned = loyaltyBurned;
	}

	public Integer getLoyaltyBurnPoints() {
		return loyaltyBurnPoints;
	}

	public void setLoyaltyBurnPoints(Integer loyaltyBurnPoints) {
		this.loyaltyBurnPoints = loyaltyBurnPoints;
	}

	public String getItemKey() {
		return itemKey;
	}

	public void setItemKey(String itemKey) {
		this.itemKey = itemKey;
	}

	public String getIsBestPairedItem() {
		return isBestPairedItem;
	}

	public void setIsBestPairedItem(String isBestPairedItem) {
		this.isBestPairedItem = isBestPairedItem;
	}

	public String getStationCategory() {
		return stationCategory;
	}

	public void setStationCategory(String stationCategory) {
		this.stationCategory = stationCategory;
	}

	public MilkVariant getMilkVariant() {
		return milkVariant;
	}

	public void setMilkVariant(MilkVariant milkVariant) {
		this.milkVariant = milkVariant;
	}
	public String getIsHoldOn() {
		return isHoldOn;
	}

	public void setIsHoldOn(String isHoldOn) {
		this.isHoldOn = isHoldOn;
	}

	public int getProductSubType() {
		return productSubType;
	}

	public void setProductSubType(int productSubType) {
		this.productSubType = productSubType;
	}
}
