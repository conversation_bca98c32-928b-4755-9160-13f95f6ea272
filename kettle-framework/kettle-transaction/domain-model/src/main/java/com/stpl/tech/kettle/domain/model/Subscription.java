/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

//
// This file was generated by the JavaTM Architecture for XML Binding(JAXB) Reference Implementation, v2.2.11
// See <a href="http://java.sun.com/xml/jaxb">http://java.sun.com/xml/jaxb</a>
// Any modifications to this file will be lost upon recompilation of the source schema.
// Generated on: 2016.02.24 at 05:36:55 PM IST
//

package com.stpl.tech.kettle.domain.model;

import com.fasterxml.jackson.databind.annotation.JsonDeserialize;
import com.stpl.tech.master.CustomJsonDateDeserializer;
import com.stpl.tech.master.domain.model.Adapter5;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.bind.annotation.adapters.XmlJavaTypeAdapter;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <p>
 * Java class for Subscription complex type.
 *
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 *
 * <pre>
 * &lt;complexType name="Subscription"&gt;
 *   &lt;complexContent&gt;
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType"&gt;
 *       &lt;sequence&gt;
 *         &lt;element name="subscriptionId" type="{http://www.w3.org/2001/XMLSchema}int"/&gt;
 *         &lt;element name="subscriptionStatus" type="{http://www.w3schools.com}SubscriptionStatus"/&gt;
 *         &lt;element name="startDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="endDate" type="{http://www.w3.org/2001/XMLSchema}date"/&gt;
 *         &lt;element name="type" type="{http://www.w3schools.com}SubscriptionType"/&gt;
 *         &lt;element name="emailNotification" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="smsNotification" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="automatedDelivery" type="{http://www.w3.org/2001/XMLSchema}boolean"/&gt;
 *         &lt;element name="daysOfTheMonth" type="{http://www.w3.org/2001/XMLSchema}int" maxOccurs="7" minOccurs="0"/&gt;
 *         &lt;element name="daysOfTheWeek" type="{http://www.w3.org/2001/XMLSchema}int" maxOccurs="7" minOccurs="0"/&gt;
 *         &lt;element name="timeOfTheDay" type="{http://www.w3.org/2001/XMLSchema}int" maxOccurs="96"/&gt;
 *       &lt;/sequence&gt;
 *     &lt;/restriction&gt;
 *   &lt;/complexContent&gt;
 * &lt;/complexType&gt;
 * </pre>
 *
 *
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "Subscription", propOrder = { "subscriptionId", "subscriptionStatus", "startDate", "endDate", "type",
		"emailNotification", "smsNotification", "automatedDelivery", "daysOfTheMonth", "daysOfTheWeek",
		"timeOfTheDay" })
@Document
public class Subscription implements Serializable{
	/**
	 *
	 */
	private static final long serialVersionUID = 7695229577630112318L;

	@Id
	private String _id;
/*
	@Version
	@JsonIgnore
	private Long version;

	*//**
	 * Added to avoid a runtime error whereby the detachAll property is checked
	 * for existence but not actually used.
	 *//*
	private String detachAll;*/
	@Field
	protected int subscriptionId;
	@XmlElement(required = true)
	@XmlSchemaType(name = "string")
	@Field
	protected SubscriptionStatus subscriptionStatus;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter5.class)
	@XmlSchemaType(name = "date")
	@JsonDeserialize(using = CustomJsonDateDeserializer.class)
	@Field
	protected Date startDate;
	@XmlElement(required = true, type = String.class)
	@XmlJavaTypeAdapter(Adapter5.class)
	@XmlSchemaType(name = "date")
	@JsonDeserialize(using = CustomJsonDateDeserializer.class)
	@Field
	protected Date endDate;
	@XmlElement(required = true)
	@XmlSchemaType(name = "string")
	@Field
	protected SubscriptionType type;
	@Field
	protected boolean emailNotification;
	@Field
	protected boolean smsNotification;
	@XmlElement(defaultValue = "true")
	@Field
	protected boolean automatedDelivery;
	@XmlElement(type = Integer.class)
	@Field
	protected List<Integer> daysOfTheMonth;
	@XmlElement(type = Integer.class)
	@Field
	protected List<Integer> daysOfTheWeek;
	@XmlElement(type = Integer.class)
	@Field
	protected List<Integer> timeOfTheDay;
	protected BigDecimal overAllSavings;

	public String get_id() {
		return _id;
	}

	public void set_id(String _id) {
		this._id = _id;
	}

/*
	public Long getVersion() {
		return version;
	}

	public void setVersion(Long version) {
		this.version = version;
	}

	public String getDetachAll() {
		return detachAll;
	}

	public void setDetachAll(String detachAll) {
		this.detachAll = detachAll;
	}
*/

	/**
	 * Gets the value of the subscriptionId property.
	 *
	 */
	public int getSubscriptionId() {
		return subscriptionId;
	}

	/**
	 * Sets the value of the subscriptionId property.
	 *
	 */
	public void setSubscriptionId(int value) {
		this.subscriptionId = value;
	}

	/**
	 * Gets the value of the subscriptionStatus property.
	 *
	 * @return possible object is {@link SubscriptionStatus }
	 *
	 */
	public SubscriptionStatus getSubscriptionStatus() {
		return subscriptionStatus;
	}

	/**
	 * Sets the value of the subscriptionStatus property.
	 *
	 * @param value
	 *            allowed object is {@link SubscriptionStatus }
	 *
	 */
	public void setSubscriptionStatus(SubscriptionStatus value) {
		this.subscriptionStatus = value;
	}

	/**
	 * Gets the value of the startDate property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public Date getStartDate() {
		return startDate;
	}

	/**
	 * Sets the value of the startDate property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setStartDate(Date value) {
		this.startDate = value;
	}

	/**
	 * Gets the value of the endDate property.
	 *
	 * @return possible object is {@link String }
	 *
	 */
	public Date getEndDate() {
		return endDate;
	}

	/**
	 * Sets the value of the endDate property.
	 *
	 * @param value
	 *            allowed object is {@link String }
	 *
	 */
	public void setEndDate(Date value) {
		this.endDate = value;
	}

	/**
	 * Gets the value of the type property.
	 *
	 * @return possible object is {@link SubscriptionType }
	 *
	 */
	public SubscriptionType getType() {
		return type;
	}

	/**
	 * Sets the value of the type property.
	 *
	 * @param value
	 *            allowed object is {@link SubscriptionType }
	 *
	 */
	public void setType(SubscriptionType value) {
		this.type = value;
	}

	/**
	 * Gets the value of the emailNotification property.
	 *
	 */
	public boolean isEmailNotification() {
		return emailNotification;
	}

	/**
	 * Sets the value of the emailNotification property.
	 *
	 */
	public void setEmailNotification(boolean value) {
		this.emailNotification = value;
	}

	/**
	 * Gets the value of the smsNotification property.
	 *
	 */
	public boolean isSmsNotification() {
		return smsNotification;
	}

	/**
	 * Sets the value of the smsNotification property.
	 *
	 */
	public void setSmsNotification(boolean value) {
		this.smsNotification = value;
	}

	/**
	 * Gets the value of the automatedDelivery property.
	 *
	 */
	public boolean isAutomatedDelivery() {
		return automatedDelivery;
	}

	/**
	 * Sets the value of the automatedDelivery property.
	 *
	 */
	public void setAutomatedDelivery(boolean value) {
		this.automatedDelivery = value;
	}

	/**
	 * Gets the value of the daysOfTheMonth property.
	 *
	 * <p>
	 * This accessor method returns a reference to the live list, not a
	 * snapshot. Therefore any modification you make to the returned list will
	 * be present inside the JAXB object. This is why there is not a
	 * <CODE>set</CODE> method for the daysOfTheMonth property.
	 *
	 * <p>
	 * For example, to add a new item, do as follows:
	 *
	 * <pre>
	 * getDaysOfTheMonth().add(newItem);
	 * </pre>
	 *
	 *
	 * <p>
	 * Objects of the following type(s) are allowed in the list {@link Integer }
	 *
	 *
	 */
	public List<Integer> getDaysOfTheMonth() {
		if (daysOfTheMonth == null) {
			daysOfTheMonth = new ArrayList<Integer>();
		}
		return this.daysOfTheMonth;
	}

	/**
	 * Gets the value of the daysOfTheWeek property.
	 *
	 * <p>
	 * This accessor method returns a reference to the live list, not a
	 * snapshot. Therefore any modification you make to the returned list will
	 * be present inside the JAXB object. This is why there is not a
	 * <CODE>set</CODE> method for the daysOfTheWeek property.
	 *
	 * <p>
	 * For example, to add a new item, do as follows:
	 *
	 * <pre>
	 * getDaysOfTheWeek().add(newItem);
	 * </pre>
	 *
	 *
	 * <p>
	 * Objects of the following type(s) are allowed in the list {@link Integer }
	 *
	 *
	 */
	public List<Integer> getDaysOfTheWeek() {
		if (daysOfTheWeek == null) {
			daysOfTheWeek = new ArrayList<Integer>();
		}
		return this.daysOfTheWeek;
	}

	/**
	 * Gets the value of the timeOfTheDay property.
	 *
	 * <p>
	 * This accessor method returns a reference to the live list, not a
	 * snapshot. Therefore any modification you make to the returned list will
	 * be present inside the JAXB object. This is why there is not a
	 * <CODE>set</CODE> method for the timeOfTheDay property.
	 *
	 * <p>
	 * For example, to add a new item, do as follows:
	 *
	 * <pre>
	 * getTimeOfTheDay().add(newItem);
	 * </pre>
	 *
	 *
	 * <p>
	 * Objects of the following type(s) are allowed in the list {@link Integer }
	 *
	 *
	 */
	public List<Integer> getTimeOfTheDay() {
		if (timeOfTheDay == null) {
			timeOfTheDay = new ArrayList<Integer>();
		}
		return this.timeOfTheDay;
	}

	public void setDaysOfTheMonth(List<Integer> daysOfTheMonth) {
		this.daysOfTheMonth = daysOfTheMonth;
	}

	public void setDaysOfTheWeek(List<Integer> daysOfTheWeek) {
		this.daysOfTheWeek = daysOfTheWeek;
	}

	public void setTimeOfTheDay(List<Integer> timeOfTheDay) {
		this.timeOfTheDay = timeOfTheDay;
	}

public BigDecimal getOverAllSavings() {
		return overAllSavings;
	}

	public void setOverAllSavings(BigDecimal overAllSavings) {
		this.overAllSavings = overAllSavings;
	}
}
